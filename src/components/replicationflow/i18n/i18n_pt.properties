#~~~ WorkBench ~~~~~~~~~~~~~~~~~~

#XTIT: Save Dialog param
@modelNameRF=Fluxo de replicação

#XFLD: Edit Schema button text
editSchema=Editar esquema

#XTIT : Properties heading
configSchema=Configurar esquema

#XFLD: save changed button text
applyChanges=Aplicar alterações


#~~~ RFBuilder Main Table ~~~~~~~~~~~~~~~~~~

#XFLD
sourceConnectionEmptyText=Selecionar conexão de origem
#XFLD
sourceContainernEmptyText=Selecionar container
#XFLD
targetConnectionEmptyText=Selecionar conexão de destino
#XFLD
targetContainernEmptyText=Selecionar container
#XFLD
sourceSelectObjectText=Selecionar objeto de origem
#XFLD
sourceObjectCount=Objetos de origem ({0})
#XFLD
targetObjectText=Objetos de destino
#XFLD
confluentBrowseContext=Selecionar contexto
#XBUT
@retry=Tentar novamente
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=Upgrade do locatário em andamento.

#XTOL
browseSourceConnection=Procurar conexão de origem
#XTOL
browseTargetConnection=Procurar conexão de destino
#XTOL
browseSourceContainer=Procurar container de origem
#XTOL
browseAndAddSourceDataset=Adicionar objetos de origem
#XTOL
browseTargetContainer=Procurar container de destino
#XTOL
browseTargetSetting=Procurar configurações de destino
#XTOL
browseSourceSetting=Procurar configurações de origem
#XTOL
sourceDatasetInfo=Informações
#XTOL
sourceDatasetRemove=Remover
#XTOL
mappingCount=Representa o número total de mapeamentos/expressões não baseados em nome.
#XTOL
filterCount=Representa o número total de condições de filtro.
#XTOL
loading=Carregando...
#XCOL
deltaCapture=Captura de delta
#XCOL
deltaCaptureTableName=Tabela de captura de delta
#XCOL
loadType=Tipo de carregamento
#XCOL
deleteAllBeforeLoading=Excluir tudo antes do carregamento
#XCOL
transformationsTab=Projeções
#XCOL
settingsTab=Configurações

#XBUT
renameTargetObjectBtn=Renomear objeto de destino
#XBUT
mapToExistingTargetObjectBtn=Mapear para objeto de destino existente
#XBUT
changeContainerPathBtn=Alterar caminho do container
#XBUT
viewSQLDDLUpdated=Exibir instrução SQL de criação de tabela
#XMSG
sourceObjectNonDeltaSupportErrorMainEditorUpdated=O objeto de origem não oferece suporte à captura delta, mas o objeto de destino selecionado tem a opção de captura delta ativada.
#XMSG
sourceObjectNonDeltaSupportErrorMsg=Não é possível usar o objeto de destino, a captura de delta está ativada{0}e o objeto de origem não suporta captura de delta.{1}Você pode selecionar outro objeto de destino que não suporte captura de delta.
#XMSG
sourceObjectNonDeltaSupportErrorMsgForRenamedTarget=Já existe um objeto de destino com este nome. No entanto, ele não pode ser usado{0}porque a captura de delta está ativada, apesar do objeto de origem não{0}suportar captura de delta.{1}Você pode inserir o nome de um objeto de destino existente que não{0}suporte captura de delta ou inserir um nome que não exista ainda.
#XBUT
copySQLDDLUpdated=Copiar instrução SQL de criação de tabela
#XMSG
targetObjExistingNoCDCColumnUpdated=As tabelas existentes no Google BigQuery devem incluir as seguintes colunas para captura de alteração de dados (CDC):{0}{0}{1} operation_flag{0}{1} is_deleted{0}{1} recordstamp
#XMSG
new_infoForUnsupportedDatasetNoKeys=Os seguintes objetos de origem não são suportados porque não têm uma chave primária ou estão usando uma conexão que não atende às condições para recuperar a chave primária:
#XMSG
new_infoForUnsupportedDatasetNoKeys2=Consulte o SAP KBA 3531135 para uma possível solução.
#XLST: load type list values
initial=Só inicial
@emailUpdateError=Erro ao atualizar a lista de notificações por e-mail

#XLST
initialDelta=Inicial e delta

#XLST
deltaOnly=Só delta
#XMSG
confluentDeltaLoadTypeInfo=Para a fonte Confluent Kafka, apenas o tipo de carregamento Inicial e delta é suportado.
#XMSG
confirmRemoveReplicationObject=Tem certeza de que deseja excluir a replicação?
#XMSG
confirmRemoveReplicationTaskPrompt=Esta ação excluirá as replicações existentes. Deseja continuar?
#XMSG
confirmTargetConnectionChangePrompt=Esta ação redefinirá a conexão de destino, o container de destino, e excluirá todos os objetos de destino. Deseja continuar?
#XMSG
confirmTargetContainerChangePrompt=Esta ação redefinirá o container de destino e excluirá todos os objetos de destino existentes. Deseja continuar?
#XMSG
confirmRemoveTransformObject=Tem certeza de que deseja excluir a projeção {0}?
#XMSG
ErrorMsgContainerChange=Ocorreu um erro ao alterar o caminho do container.
#XMSG
infoForUnsupportedDatasetNoKeys=Os seguintes objetos de origem não são suportados porque eles não têm uma chave primária:
#XMSG
infoForUnsupportedDatasetView=Os seguintes objetos de origem que tipo Visões não são suportados:
#XMSG
infoForUnsupportedSourceAsSQLViewWithParams=O seguinte objeto de origem não é suportado, ele é uma visão SQL que contém parâmetros de entrada:
#XMSG
infoForUnsupportedDatasetExtractionDisabled=Os seguintes objetos de origem não são suportados porque a extração está desativada para eles:
#XMSG
infoForUnsupportedDatasetSchemaTypeUpdated=Para conexões Confluent, os únicos formatos de serialização permitidos são AVRO e JSON. Os seguintes objetos não são suportados porque eles usam um formato de serialização diferente:
#XMSG
infoForUnsupportedDatasetSchemaNotFound=Não é possível buscar o esquema para os objetos a seguir. Selecione o contexto apropriado ou verifique a configuração de registro de esquema
#XTOL: warning dialog header on deleting replication task
deleteHeader=Excluir
#XBUT
disabledDeleteAllBeforeLoadingForGBQInfo=A configuração "Excluir tudo antes do carregamento" não é suportada para o Google BigQuery.
#XBUT
DeleteAllBeforeLoadingConfluentInfo=A configuração "Excluir tudo antes" exclui e recria o objeto (tópico) antes de cada replicação. Ela também exclui todas as mensagens atribuídas.
#XTOL
DeleteAllBeforeLoadingLTFInfo=A configuração "Excluir tudo antes" não é suportada para esse tipo de destino.
#~~~ Connection Browser ~~~~~~~~~~~~~~~~~~

#XCOL
connTechnicalName=Nome técnico
#XCOL
connBusinessName=Nome comercial
#XCOL
connDescriptionName=Descrição
#XCOL
connType=Tipo
#XMSG
connTblNoDataFoundtxt=Nenhuma conexão encontrada
#XMSG
connectionError=Ocorreu um erro ao buscar conexões.
#XMSG
connectionCombinationUnsupportedErrorTitle=A combinação de conexões não é suportada
#XMSG
connectionCombinationUnsupportedErrorMsgTxt=A replicação do {0} para o {1} atualmente não é suportada.
#XMSG
invalidTargetforSourceHDLFErrorTitle=A combinação de tipo de conexão não é suportada
#XMSG Error message when invalid target is chosen after selecting HDLF as source
invalidTargetforSourceHDLFError=A replicação de uma conexão do tipo Arquivos do SAP HANA Cloud, data lake com {0} não é suportada. Você só pode replicar para o SAP Datasphere.

#~~~ Container Browser ~~~~~~~~~~~~~~~~~~

#XBUT
containerSelectBtn=Selecionar
#XBUT
containerCancelBtn=Cancelar
#XTOL
containerSelectTooltip=Selecionar
#XTOL
containerCancelTooltip=Cancelar
#XMSG
containerContainerPathPlcHold=Caminho do container
#XFLD
containerContainertxt=Container
#XFLD
confluentContainerContainertxt=Contexto
#XMSG
infoMessageForSLTSelection=Só /SLT/ID de transferência em massa é permitido como container. Selecione um ID de transferência em massa sob SLT (se disponível) e clique em Enviar.
#XMSG
msgFetchContainerFail=Ocorreu um erro ao buscar os dados do container.
#XMSG
infoMessageForSLTHidden=Esta conexão não suporta pastas SLT, elas não serão exibidas na lista abaixo.

#XMSG
infoForHavingSubFoldersForObjectstoreContainer=Selecione um container que contenha subpastas.
#XMSG
sftpIncludeSubFolderText=Falso
#XMSG
sftpIncludeSubFolderTextNew=Não

#~~~ Start of Dataset Browser ~~~~~~~~~~~~~~~~~~

#XMSG
@noFilterMappingYet=(Nenhum mapeamento de filtro ainda)
#XMSG
failToFetchRemoteMetadata=Ocorreu um erro ao buscar metadados.
#XMSG
failToFetchData=Ocorreu um erro ao buscar o destino existente.
#XCOL
@loadType=Carregar tipo
#XCOL
@deleteAllBeforeLoading=Excluir tudo antes do carregamento

#XMSG
@loading=Carregando...
#XFLD
@selectSourceObjects=Selecionar objetos de origem
#XMSG
@exceedLimit=Você não pode importar mais do que {0} objetos por vez. Desmarque pelo menos {1} objetos.
#XFLD
@objects=Objetos
#XBUT
@ok=OK
#XBUT
@cancel=Cancelar
#XBUT: remote object browser has 2 steps, search and select, press next and go to selected screen
@next=Avançar
#XBUT
btnAddSelection=Adicionar seleção
#XTOL
@remoteFromSelection=Remover da seleção
#XMSG
@searchInForSearchField=Procurar em {0}

#XCOL
@name=Nome técnico
#XCOL
@type=Tipo
#XCOL
@location=Local
#XCOL
@label=Nome comercial
#XCOL
@status=Status

#XFLD
@searchIn=Procurar em:
#XBUT
@available=Disponível
#XBUT
@selection=Seleção

#XFLD
@noSourceSubFolder=Tabelas e visualizações
#XMSG
@alreadyAdded=Já presente no diagrama
#XMSG
@askForFilter=Há mais de {0} itens. Insira uma string de filtro para restringir o número de itens.
#XFLD: success label
lblSuccess=Êxito
#XFLD: ready label
lblReady=Pronto
#XFLD: failure label
lblFailed=Com falha
#XFLD: fetching status label
lblFetchingDetail=Buscando detalhes

#XMSG Place holder text for tree filter control
filterPlaceHolder=Digite o texto para filtrar os objetos de nível superior
#XMSG Place holder text for server search control
serverSearchPlaceholder=Digite e pressione Enter para procurar
#XMSG
@deployObjects=Importando {0} objetos...
#XMSG
@deployObjectsStatus=Número de objetos que foram importados: {0}. Número de objetos que não puderam ser importados: {1}.

# @TODO : Verify texts with UA
#XMSG
@openRepositoryBrowserError=Falha ao abrir Repository Browser local.
#XMSG
@openRemoteSourceBrowserError=Falha ao buscar objetos de origem.
#XMSG
@openRemoteTargetBrowserError=Falha ao buscar objetos de destino.
#XMSG
@validatingTargetsError=Ocorreu um erro ao validar destinos.
#XMSG
@waitingToImport=Pronto para importação

#~~~ End of Datset ~~~~~~~~~~~~~~~~~~

#XMSG: Information message telling user that he can’t add more replications
datasetLimitCrossed=O número máximo de objetos foi excedido. Selecione, no máximo, 500 objetos para um fluxo de replicação.

#~~~ Dataset Info Popover ~~~~~~~~~~~~~~~~~~

#XFLD
sourceObjectName=Nome técnico
#XFLD
sourceObjectBusinessName=Nome comercial
#XFLD
sourceNoColumns=Número de colunas
#XFLD
containerLbl=Container

#~~~ RFBuilder Main Table validation~~~~~~~~~~~~~~~~~~

#XMSG
validationSourceNonExist=Você precisa selecionar uma conexão de origem para o fluxo de replicação.
#XMSG
validationSourceContainerNonExist=Você precisa selecionar um container para a conexão de origem.
#XMSG
validationTargetNonExist=Você precisa selecionar uma conexão de destino para o fluxo de replicação.
#XMSG
validationTargetContainerNonExist=Você precisa selecionar um container para a conexão de destino.
#XMSG
validationTruncateDisabledForObjectTitle=Replicação para armazenamentos de objetos.
#XMSG
validationDeleteAllBeforeLoadingDisabledForObjectStoreTarget=A replicação em um armazenamento em nuvem só é possível quando a opção Excluir tudo antes do carregamento está definida ou o objeto de destino não existe no destino.{0}{0} Caso queira ativar a replicação de objetos sem a opção Excluir tudo antes do carregamento definida, verifique se o objeto de destino não existe no sistema antes de executar o fluxo de replicação.
#XMSG
validationTaskNonExist=Você precisa ter pelo menos uma replicação no fluxo de replicação.
#XMSG
validationTaskTargetMissing=Você precisa ter um destino para a replicação com a origem: {0}
#XMSG
validationTaskTargetIsSAC=Destino selecionado é um artefato SAC: {0}
#XMSG
validationTaskTargetIsNonDwcTableKey=O destino selecionado não é uma tabela local suportada: {0}
#XMSG
validationTaskTargetIsNonDwcTableDescription=Já existe um objeto com este nome no destino. No entanto, esse objeto não pode ser usado como objeto de destino de um fluxo de replicação para o repositório local porque ele não é uma tabela local.
#XMSG
validateSourceTargetSystemDifference=Você precisa selecionar combinações diferentes de container e conexão de origem e de destino para o fluxo de replicação.
#XMSG
validateDuplicateSources=uma ou mais replicações têm nomes duplicados de objetos de origem: {0}.
#XMSG
validateDuplicateTargets=uma ou mais replicações têm nomes duplicados de objetos de destino: {0}.
#XMSG
validationTaskSourceObjectTargetObjectDeltaMismatchUpdated=O objeto de origem {0} não suporta captura delta, enquanto o objeto de destino {1} suporta. Você deve remover a replicação.
#XMSG
validationTaskTargetObjectLoadTypeMismatch=Você deve selecionar o tipo de carregamento "Inicial e delta" para a replicação com nome do objeto de destino {0}.
#XMSG
validationAutoRenameTarget=As colunas de destino foram renomeadas.
#XMSG: 0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionDWC2=Uma projeção automática foi adicionada e as seguintes colunas de destino foram renomeadas para permitir replicação no destino:{1}{1} {0} {1}{1}Isso se deve a um dos seguintes motivos:{1}{1}{2} Caracteres não suportados{1}{2} Prefixo reservado
#XMSG
validationAutoRenameTargetDescriptionUpdated=Uma projeção automática foi adicionada e as seguintes colunas de destino foram renomeadas para permitir a replicação no Google BigQuery:{1}{1} {0} {1}{1}Isso se deve a um dos seguintes motivos:{1}{1}{2} Nome de coluna reservado{1}{2} Caracteres não suportados{1}{2} Prefixo reservado
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionConfluent=Uma projeção automática foi adicionada e as seguintes colunas de destino foram renomeadas para permitir replicações no Confluent:{1}{1} {0} {1}{1}Isso se deve a um dos seguintes motivos:{1}{1}{2} Nome de coluna reservado{1}{2} Caracteres não suportados{1}{2} Prefixo reservado
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionGeneric=Uma projeção automática foi adicionada e as seguintes colunas de destino foram renomeadas para permitir replicações no destino:{1}{1} {0} {1}{1}Isso se deve a um dos seguintes motivos:{1}{1}{2} Nome de coluna reservado{1}{2} Caracteres não suportados{1}{2} Prefixo reservado
#XMSG
validationAutoRenameTargetSubTitle={0}
#XMSG
autoRenameInfo=O objeto de destino foi renomeado.
#XMSG
autoRenameInfoDesc=O objeto de destino foi renomeado porque ele continha caracteres não suportados. Somente os seguintes caracteres são suportados:{0}{0}{1}A–Z{0}{1}a–z{0}{1}0–9{0}{1}.(ponto){0}{1}_(sublinhado){0}{1}-(traço)
#XMSG
validationAutoTargetTypeConversion=Os tipos de dados de destino foram alterados.
#XMSG
validationAutoTargetTypeConversionDescriptionUpdated=Para as colunas de destino a seguir, os tipos de dados de destino foram alterados porque, no Google BigQuery, os tipos de dados de origem não são suportados:{1}{1}{0}
#XMSG: 0 - Converted datatype col names , 1 - new line , 2 - Connection Name ( Can be SAP Datasphere or SAP Datasphere (HDL_FILES))
validationAutoTargetTypeConversionDescriptionDWC3=Para as colunas de destino a seguir, os tipos de dados de destino foram alterados porque os tipos de dados de origem não são suportados na conexão de destino: {1}{1}{0}
#XMSG
validationAutoTargetTypeConversionSubTitle={0}
#XMSG
validationMaxCharLengthGBQTarget=Reduza os nomes de coluna de destino.
#XMSG
validationMaxCharLengthGBQTargetDescription=No Google BigQuery, os nomes de coluna podem ter, no máximo, 300 caracteres. Use uma projeção para reduzir os seguintes nomes de coluna de destino:{1}{0}
#XMSG
validationMaxCharLengthGBQTargetSubTitle={0}
#XMSG
validationGBQUnableToCreateKey=As chaves primárias não serão criadas.
#XMSG
validationGBQUnableToCreateKeyMaxKeyCombinationDescription=No Google BigQuery, são suportadas, no máximo, 16 chaves primárias, mas o objeto de origem tem um número maior de chaves primárias. Nenhuma das chaves primárias será criada no objeto de destino.
#XMSG
validationGBQUnableToCreateKeySubTitle={0}
#XMSG
validationIncompatiblePKTypeDescUpdated4=Uma ou mais colunas de origem têm tipos de dados que não podem ser definidos como chaves primárias no Google BigQuery. Nenhuma chave primária será criada no objeto de destino.{0}{0}Os seguintes tipos de dados de destino são compatíveis com os tipos de dados do Google BigQuery, para os quais uma chave primária pode ser definida:{0}{0}{1} BOOLEAN{0}{1} DATE{0}{1} DECIMAL{0}{1} INT32{0}{1} INT64{0}{1} NUMERIC{0}{1} STRING{0}{1} TIMESTAMP
#XMSG
validateObjectStoreNoPKDatasetError=Defina uma ou mais colunas como chave primária.
#XMSG
validateObjectStoreNoPKDatasetErrorDescription1=Você deve definir uma ou mais colunas como chave primária; para isso, use a caixa de diálogo de esquema de origem.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetError=Defina uma ou mais colunas como chave primária.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetErrorDescription=Você deve definir uma ou mais colunas como chave primária que correspondem às restrições de chave primária para seu objeto de origem. Vá para Configurar esquema nas propriedades do seu objeto de origem para fazer isso.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetSubtitle={0}
#XMSG
validateMaxPartitionValueError=Insira um valor máximo de partições válido.
#XMSG
validateMaxPartitionValueErrorDescriptionUpdated=O valor máximo de partição deve ser ≥ 1 e ≤ 2147483647
#XMSG
validateHDLFNoPKDatasetError=Defina uma ou mais colunas como chave primária.
#XMSG
validateHDLFNoPKDatasetSubtitle={0}
#XMSG
validateHDLFNoPKDatasetErrorDescription1=Para replicar um objeto, você precisa definir uma ou mais colunas de destino como chave primária. Use uma projeção para fazer isso.
#XMSG
validateHDLFNoPKExistingDatasetError=Defina uma ou mais colunas como chave primária.
#XMSG
validateHDLFNoPKExistingDatasetSubtitle={0}
#XMSG {0} {1} being used for formatting
validateHDLFNoPKExistingDatasetErrorDescription1=Para replicar dados em um objeto de destino existente, ele deve ter uma ou mais colunas definidas como chave primária. {0} Você tem as seguintes opções para definir uma ou mais colunas como chave primária: {0} {1} Use o editor de tabelas local para alterar o objeto de destino existente. Em seguida, recarregue o fluxo de replicação. {0}{1} Renomeie o objeto de destino no fluxo de replicação. Com isso, outro objeto será criado assim que a execução for iniciada. Depois de renomeá-lo, você pode definir uma ou mais colunas como chave primária em uma projeção.{0}{1} Mapeie o objeto para outro objeto de destino existente em que uma ou mais colunas já estejam definidas como chave primária.
#XMSG: Error duplicate target object technical name validation message : {0} technical name of the object
validationDuplicateTechnicalNameKey=O destino selecionado já existe no repositório: {0}
#XMSG: {0} delta capture name i.e. MyTable_Delta
validationDuplicateTechnicalNameDescription=Os nomes das tabelas de captura de delta já foram usados por outras tabelas no repositório: {0} Você deve renomear esses objetos de destino para garantir que os nomes das tabelas de captura de delta associadas sejam exclusivos antes de salvar o fluxo de replicação.
#XMSG
validateConfluentEmptySchema=Definir esquema
#XMSG
validateConfluentEmptySchemaDescUpdated=A tabela de origem não tem um esquema. Clique em Configurar esquema para definir um
#XMSG
validationCSVEncoding=Codificação CSV inválida
#XMSG
validationCSVEncodingDescription=A codificação CSV da tarefa não é válida.
#XMSG
validateConfluentEmptySchema=Selecione um tipo de dados de destino compatível
#XMSG
validateTargetDataTypeSubtitle={0}
#XMSG:
globalValidateTargetDataType=Selecione um tipo de dados de destino compatível
#XMSG
globalValidateTargetDataTypeDesc=Ocorreu um erro nos mapeamentos de coluna. Vá para Projeção e verifique se todas as colunas de origem estão mapeadas com uma coluna única, com uma coluna que tem um tipo de dados compatível e na qual todas as expressões definidas são válidas.
#XMSG: Error for new datasets with duplicate columns
validateDuplicateColumnsForNewTargets=Nomes de coluna duplicados.
#XMSG: Error for existing datasets with duplicate columns
validateDuplicateColumnsForNewTargetsDescription=Nomes de coluna duplicados não são suportados. Use a caixa de diálogo Projeção para corrigir. Os objetos de destino a seguir têm nomes de coluna duplicados: {0}
#XMS: Warning for new datasets with duplicate columns
validateDuplicateColumnsExistingTargets=Nomes de coluna duplicados.
#XMSG: Warn description for existing datasets with duplicate columns
validateDuplicateColumnsExistingTargetsDescription=Nomes de coluna duplicados não são suportados. Os objetos de destino a seguir têm nomes de coluna duplicados: {0}
#XMSG
deltaOnlyLoadTypeTittle=Podem existir inconsistências nos dados.
#XMSG
deltaOnlyLoadTypeDescriptionUpdated=O tipo de carregamento Só delta não considerará as alterações efetuadas na fonte entre a última gravação e a próxima execução.
#XMSG
deltaOnlyLoadTypeSubtitle={0}
#~~~ ABAP Source without key Main Table validations~~~~~~~~~~~~~~~~~~
#XMSG
sourceABAPWithoutKeyLoadTypeError=Altere o tipo de carregamento para "Inicial".
#XMSG
sourceABAPWithoutKeyLoadTypeErrorDescription1=A replicação de objetos baseados em ABAP sem uma chave primária só é possível para o tipo de carregamento "Só inicial".
#XMSG
sourceABAPWithoutKeyLoadTypeSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTarget=Desative a captura de delta.
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTargetDescription1=Para replicar um objeto sem chave primária usando o tipo de conexão de origem ABAP, você deve primeiro desativar a captura de delta dessa tabela.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTarget1=Altere o objeto de destino.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTargetDescription1=O objeto de destino não pode ser usado porque a captura de delta está ativada. Você pode renomear o objeto de destino e, em seguida, desativar a captura de delta para o objeto novo (renomeado) ou mapear o objeto de origem para um objeto de destino no qual a captura de delta está desativada.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATarget=Altere o objeto de destino.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetDescription=O objeto de destino não pode ser usado porque ele não tem a coluna técnica necessária __load_package_id. Você pode renomear o objeto de destino usando um nome que ainda não existe. O sistema, em seguida, cria um objeto com a mesma definição que o objeto de origem e que contém a coluna técnica. Como alternativa, você pode mapear o objeto de destino para um objeto existente com a coluna técnica necessária (__load_package_id).
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTarget1=Altere o objeto de destino.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetDescription1=O objeto de destino não pode ser usado porque ele não tem a coluna técnica necessária __load_record_id. Você pode renomear o objeto de destino usando um nome que ainda não existe. O sistema, em seguida, cria um objeto com a mesma definição que o objeto de origem e que contém a coluna técnica. Como alternativa, você pode mapear o objeto de destino para um objeto existente com a coluna técnica necessária (__load_record_id).
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTarget1=Altere o objeto de destino.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTargetDescription1=O objeto de destino não pode ser usado porque o tipo de dados da respectiva coluna técnica __load_record_id não é "string(44)". Você pode renomear o objeto de destino usando um nome que ainda não existe. O sistema, em seguida, cria um objeto com a mesma definição que o objeto de origem e, consequentemente, com o tipo de dados correto. Como alternativa, você pode mapear o objeto de destino para um objeto existente com a coluna técnica necessária (__load_record_id) e tipo de dados correto.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATarget1=Altere o objeto de destino.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingHANATarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATargetDescription2=O objeto de destino não pode ser usado porque ele tem uma chave primária enquanto o objeto de origem não tem nenhuma. Você pode renomear o objeto de destino usando um nome que ainda não existe. O sistema, em seguida, cria um objeto com a mesma definição que o objeto de origem e, consequentemente, nenhuma chave primária. Como alternativa, você pode mapear o objeto de destino para um objeto existente com a coluna técnica necessária (__load_package_id) e sem uma chance primária.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTarget=Altere o objeto de destino.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingNonABAPTarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTargetDescription=O objeto de destino não pode ser usado porque ele tem uma chave primária enquanto o objeto de origem não tem nenhuma. Você pode renomear o objeto de destino usando um nome que ainda não existe. O sistema, em seguida, cria um objeto com a mesma definição que o objeto de origem e, consequentemente, nenhuma chave primária. Como alternativa, você pode mapear o objeto de destino para um objeto existente com a coluna técnica necessária (__load_record_id) e sem uma chance primária.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATarget1=Altere o objeto de destino.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATargetDescription2=O objeto de destino não pode ser usado porque o tipo de dados da respectiva coluna técnica __load_package_id não é "binary(>=256)". Você pode renomear o objeto de destino usando um nome que ainda não existe. O sistema, em seguida, cria um objeto com a mesma definição que o objeto de origem e, consequentemente, com o tipo de dados correto. Como alternativa, você pode mapear o objeto de destino para um objeto existente com a coluna técnica necessária (__load_package_id) e tipo de dados correto.
#XMSG
validationAutoRenameTargetDPID=As colunas de destino foram renomeadas.
#XMSG
validationAutoRenameTargetDPIDSubTitle={0}
#XMSG
sourceABAPWithoutKeyWithLTF=Remover objeto de origem.
#XMSG
sourceABAPWithoutKeyWithLTFSubtitle={0}
#XMSG
sourceABAPWithoutKeyWithLTFDescription=O objeto de origem não tem uma coluna de chave como uma chave primária, e isso não é suportado nesse contexto.
#XMSG
validationAutoRenameTargetDPIDDescription=Uma projeção automática foi adicionada e as seguintes colunas de destino foram renomeadas para permitir a replicação a partir da fonte ABAP sem chaves:{1}{1} {0} {1}{1}Isso se deve a um dos seguintes motivos:{1}{1}{2} Nome de coluna reservado{1}{2} Caracteres não suportados{1}{2} Prefixo reservado
#XMSG generic title for any target system not supported when FF off
targetXSystemFFDisabledTitle=Replicação para {0}.
#XMSG generic descripition for any target system not supported when FF off
targetXSystemFFDisabledDesciption=Atualmente, não é possível salvar e implementar fluxos de replicação com o {0} como destino porque estamos realizando manutenção nessa função.
#XMSG
TargetColumnSkippedLTF=A coluna de destino foi ignorada.
#XMSG
TargetColumnSkippedLTFSubtitle={0}
#XMSG
TargetColumnSkippedLTFDescription=A coluna de destino foi ignorada devido a um tipo de dados não suportado. {0}{1}
#XMSG
validatePKTimeColumnLTF1=Coluna de tempo como chave primária.
#XMSG
validatePKTimeColumnLTFSubtitle={0}
#XMSG
validatePKTimeColumnLTFDescription=O objeto de origem tem uma coluna de tempo como uma chave primária, e isso não é suportado nesse contexto.
#XMSG
validateNoPKInLTFTarget=Chave primária ausente.
#XMSG
validateNoPKInLTFTargetDescription=A chave primária não está definida no destino, e isso não é suportado neste contexto.
#XMSG
validateABAPClusterTableLTF=Tabela de cluster ABAP.
#XMSG
validateABAPClusterTableLTFSubtitle={0}
#XMSG
validateABAPClusterTableLTFDescription=O objeto de origem está em uma tabela de cluster ABAP que não é suportada neste contexto.
#~~~ RFBuilder Welcome Panel ~~~~~~~~~~~~~~~~~~

#YINS
welcomeText1=Parece que você ainda não adicionou dados.
#YINS
welcomeText2=Para iniciar seu fluxo de replicação, selecione uma conexão e um objeto de origem no lado esquerdo.

#XBUT
wizStep1=Selecionar conexão de origem
#XBUT
wizStep2=Selecionar container de origem
#XBUT
wizStep3=Adicionar objetos de origem

#XMSG
limitDataset=O número máximo de objetos foi alcançado. Remova os objetos existentes para adicionar novos ou crie um outro fluxo de replicação.
#XMSG
premiumOutBoundRFCannotStartWarningMsg=O fluxo de replicação para essa conexão de destino não SAP não pode ser iniciado, não há volume de saída disponível para esse mês.
#XMSG
premiumOutBoundRFAdminWarningMsg=Um administrador pode aumentar os blocos Saída premium para esse locatário, disponibilizando volume de saída para esse mês.
#XMSG
messageForToastForDPIDColumn2=Nova coluna adicionada a destino para {0} objetos – necessário para tratar registros duplicados na conexão com os objetos de origem baseados em ABAP que não tem uma chave primária.
#XMSG
PremiumInboundWarningMessage=Dependendo do número de fluxos de replicação e do volume de dados a ser replicado, os recursos do SAP HANA {0}necessários para a replicação de dados usando {1} podem exceder a capacidade disponível para seu locatário.
#XMSG
PremiumInboundWarningMsg=Dependendo do número de fluxos de replicação e do volume de dados a ser replicado,{0}os recursos do SAP HANA necessários para a replicação de dados usando "{1}" podem exceder a capacidade disponível para seu locatário.
#~~~ Transformation Dialog ~~~~~~~~~~~~~~~~~~


#XMSG: validation messages
emptyTransformationName=Insira um nome de projeção.
#XMSG
emptyTargetColumn=Insira um nome de coluna de destino.
#XMSG
emptyTargetColumnBusinessName=Insira uma coluna de destino Nome comercial.
#XMSG
invalidTransformName=Insira um nome de projeção.
#XMSG
uniqueColumnName=Renomeie a coluna de destino.
#XMSG
copySourceColumnLbl=Copiar colunas do objeto de origem
#XMSG
renameWarning=Lembre-se de escolher um nome único ao renomear a tabela de destino. Se uma tabela com mesmo novo nome já existir na área, ele usará a definição dessa tabela.

#XMSG
uniqueColumnBusinessName=Renomeie o nome comercial da coluna de destino.
#XMSG
uniqueSourceMapping=Selecione uma coluna de origem diferente.
#XMSG: 0=Source column name, 1=new line character, 2=Target column name
uniqueSourceMappingDesc=A coluna de origem {0} já é usada pelas seguintes colunas de destino:{1}{1}{2}{1}{1} Para essa coluna de destino ou para as outras colunas de destino, selecione uma coluna de origem que não esteja em uso para salvar a projeção.
#XMSG
uniqueColumnNameDescription=O nome da coluna de destino inserido já existe. Para salvar a projeção, você precisa inserir um nome de coluna exclusivo.
#XMSG
uniqueColumnBusinessNameDesc=O nome comercial da coluna de destino já existe. Para salvar a projeção, você precisa inserir um nome comercial de coluna exclusivo.
#XMSG
emptySource=Selecione uma coluna de origem ou insira uma constante.
#XMSG
emptySourceDescription=Para criar uma entrada de mapeamento válida, você precisa selecionar uma coluna de origem ou inserir um valor constante.
#XMSG
emptyExpression=Defina o mapeamento.
#XMSG
emptyExpressionDescription1=Selecione a coluna de origem para a qual você deseja mapear a coluna de destino ou marque a caixa de seleção na coluna {0} Funções/Constantes{1}. {2} {2}As funções são inseridas automaticamente, de acordo com o tipo de dados de destino. Os valores de constante podem ser inseridos manualmente.
#XMSG
numberExpressionErr=Insira um número.
#XMSG
numberExpressionErrDescription=Você selecionou um tipo de dados numérico. Isso significa que você só pode inserir números, além do ponto decimal, se aplicável. Não use aspas simples.
#XMSG
invalidLength=Insira um valor de comprimento válido.
#XMSG
invalidLengthDescription=O comprimento do tipo de dados deve ser igual ou maior do que o comprimento da coluna de origem e pode ser entre 1 e 5000.
#XMSG
invalidMappedLength=Insira um valor de comprimento válido.
#XMSG
invalidMappedLengthDescription=O comprimento do tipo de dados deve ser igual ou maior do que o comprimento da coluna de origem {0} e pode ser entre 1 e 5000.
#XMSG
invalidPrecision=Insira um valor de precisão válido.
#XMSG
invalidPrecisionDescription=Precisão define o número total de dígitos. Escala define o número de dígitos depois do ponto decimal e pode ser entre 0 e a precisão.{0}{0} Exemplos: {0}{1} Precisão 6, escala 2 corresponde a números como 1234.56.{0}{1} Precisão 6, escala 6 corresponde a números como 0.123546.{0} {0} A precisão e a escala do destino devem ser compatíveis com a precisão e a escala da origem, de modo que todos os dígitos da origem se encaixem no campo de destino. Por exemplo, se você tiver precisão 6 e escala 2 na origem (e, consequentemente dígitos diferentes de 0 antes do ponto decimal), não pode ter precisão 6 e escala 6 no destino.
#XMSG
invalidPrimaryKey=Insira pelo menos uma chave primária.
#XMSG
invalidPrimaryKeyDescription=Chave primária não definida para este esquema.
#XMSG
invalidMappedPrecision=Insira um valor de precisão válido.
#XMSG
invalidMappedPrecisionDescription1=Precisão define o número total de dígitos. Escala define o número de dígitos depois do ponto decimal e pode ser entre 0 e a precisão.{0}{0} Exemplos:{0}{1} Precisão 6, escala 2 corresponde a números como 1234.56.{0}{1} Precisão 6, escala 6 corresponde a números como 0.123546.{0}{0}A precisão do tipo de dados deve ser igual ou maior do que a precisão da origem ({2}).
#XMSG
invalidScale=Insira um valor de escala válido.
#XMSG
invalidScaleDescription=Precisão define o número total de dígitos. Escala define o número de dígitos depois do ponto decimal e pode ser entre 0 e a precisão.{0}{0} Exemplos: {0}{1} Precisão 6, escala 2 corresponde a números como 1234.56.{0}{1} Precisão 6, escala 6 corresponde a números como 0.123546.{0} {0} A precisão e a escala do destino devem ser compatíveis com a precisão e a escala da origem, de modo que todos os dígitos da origem se encaixem no campo de destino. Por exemplo, se você tiver precisão 6 e escala 2 na origem (e, consequentemente dígitos diferentes de 0 antes do ponto decimal), não pode ter precisão 6 e escala 6 no destino.
#XMSG
invalidMappedScale=Insira um valor de escala válido.
#XMSG
invalidMappedScaleDescription1=Precisão define o número total de dígitos. Escala define o número de dígitos depois do ponto decimal e pode ser entre 0 e a precisão.{0}{0} Exemplos:{0}{1} Precisão 6, escala 2 corresponde a números como 1234.56.{0}{1} Precisão 6, escala 6 corresponde a números como 0.123546.{0}{0}A escala do tipo de dados deve ser igual ou maior do que a escala da origem ({2}).
#XMSG
nonCompatibleDataType=Selecione um tipo de dados de destino compatível.
#XMSG
nonCompatibleDataTypeDescription1=O tipo de dados especificado aqui deve ser compatível com o tipo de dados de origem ({0}). {1}{1} Exemplo: se sua coluna de origem tem o tipo de dados string e contém letras, você não pode usar um tipo de dados decimal no destino.
#XMSG
invalidColumnCount=Selecione uma coluna de origem.
#XMSG
ObjectStoreInvalidScaleORPrecision=Insira um valor válido para precisão e escala.
#XMSG
ObjectStoreInvalidScaleORPrecisionUpdated2=O primeiro valor é a precisão, que define o número total de dígitos. O segundo valor é a escala, que define os dígitos depois do ponto decimal. Insira um valor de escala de destino que seja maior do que o valor de escala de origem e confira se a diferença entre a escala de destino inserida e o valor de precisão é maior do que a diferença entre a escala de origem e o valor de precisão.
#XMSG
InvalidPrecisionORScale=Insira um valor válido para precisão e escala.
#XMSG
GBQNonCompatibleDataTypeDecfloatUpdated2=O primeiro valor é a precisão, que define o número total de dígitos. O segundo valor é a escala, que define o número de dígitos após o ponto decimal.{0}{0}Como o tipo de dados de origem não é suportado no Google BigQuery, ele é convertido no tipo de dados de destino DECIMAL. Nesse caso, a precisão só pode ser definida entre 38 e 76, e a escala entre 9 e 38. Além disso, o resultado da precisão menos a escala, que representa o número de dígitos antes do ponto decimal, deve ser entre 29 e 38.
#XMSG
GBQNonCompatibleDataTypeUnit64Updated1=O primeiro valor é a precisão, que define o número total de dígitos. O segundo valor é a escala, que define o número de dígitos após o ponto decimal.{0}{0}Como o tipo de dados de origem não é suportado no Google BigQuery, ele é convertido no tipo de dados de destino DECIMAL. Nesse caso, a precisão deve ser definida como 20 ou mais. Além disso, o resultado da precisão menos a escala, que representa o número de dígitos antes do ponto decimal, deve ser 20 ou mais.
#XMSG
DWCLTFInvalidPrecisionOrScaleDescription3=O primeiro valor é a precisão, que define o número total de dígitos. O segundo valor é a escala, que define o número de dígitos após o ponto decimal.{0}{0}Como o tipo de dados de origem não é suportado no destino, ele é convertido no tipo de dados de destino DECIMAL. Nesse caso, a precisão deve ser definida por qualquer número maior ou igual a 1 e menor ou igual a 38 e a escala deve ser menor ou igual à precisão.
#XMSG
DWCLTFNonCompatibleDataTypeUnit64Description1=O primeiro valor é a precisão, que define o número total de dígitos. O segundo valor é a escala, que define o número de dígitos após o ponto decimal.{0}{0}Como o tipo de dados de origem não é suportado no destino, ele é convertido no tipo de dados de destino DECIMAL. Nesse caso, a precisão deve ser definida como 20 ou mais. Além disso, o resultado da precisão menos a escala, que representa o número de dígitos antes do ponto decimal, deve ser 20 ou mais.
#XMSG
invalidColumnCountDescription=Para criar uma entrada de mapeamento válida, você precisa selecionar uma coluna de origem ou inserir um valor constante.
#XMSG
duplicateColumns=Renomeie a coluna de destino.
#XMSG
duplicateGBQCDCColumnsDesc=O nome da coluna de destino é reservado no Google BigQuery. Você precisa renomeá-la para salvar a projeção.
#XMSG
duplicateConfluentCDCColumnsDesc=O nome da coluna de destino é reservado no Confluent. Você precisa renomeá-la para salvar a projeção.
#XMSG
duplicateSignavioCDCColumnsDesc=O nome da coluna de destino é reservado no SAP Signavio. Você precisa renomeá-la para salvar a projeção.
#XMSG
duplicateMsOneLakeCDCColumnsDesc=O nome da coluna de destino é reservado no MS OneLake. Você precisa renomeá-la para salvar a projeção.
#XMSG
duplicateSFTPCDCColumnsDesc=O nome da coluna de destino é reservado no SFTP. Você precisa renomeá-la para salvar a projeção.
#XMSG
GBQTargetNameWithPrefixUpdated1=O nome da coluna de destino contém um prefixo que é reservado no Google BigQuery. Você precisa renomeá-la para salvar a projeção. {0}{0}O nome da coluna de destino não pode começar com nenhuma das seguintes strings:{0}{0}{1}{2} _TABLE_{3}{2}_FILE_{3}{2}_PARTITION{3}{2} _ROW_TIMESTAMP{3}{2} __ROOT__{3}{2} _COLIDENTIFIER{3}{4}
#XMSG
GBQtargetMaxLength=Reduza o nome da coluna de destino.
#XMSG
GBQtargetMaxLengthDesc=No Google BigQuery, um nome de coluna pode usar, no máximo, 300 caracteres. Reduza o nome da coluna de destino para salvar a projeção.
#XMSG
invalidMappedScalePrecision=A precisão e a escala do destino devem ser compatíveis com a precisão e a escala da origem para que todos os dígitos da origem se acomodem no campo de destino.
#XMSG
invalidMappedScalePrecisionShortText=Informe um valor de precisão e de escala válido.
#XMSG
validationIncompatiblePKTypeDescProjection3=Uma ou mais colunas de origem têm tipos de dados que não podem ser definidos como chaves primárias no Google BigQuery. Nenhuma chave primária será criada no objeto de destino.{0}{0}Os seguintes tipos de dados de destino são compatíveis com os tipos de dados do Google BigQuery, para os quais uma chave primária pode ser definida:{1}{2} BOOLEAN{3}{2} DATE{3}{2} DECIMAL{3}{2} INT32{3}{2} INT64{3}{2} NUMERIC{3}{2} STRING{3}{2} TIMESTAMP
#XMSG
uncheckColumnMessageId=Desmarque a coluna __message_id.
#XMSG
uncheckColumnMessageIdDesc=Coluna: chave primária
#XMSG
validationOpCodeInsert=Você deve digitar o valor para Inserção.
#XMSG
recommendDifferentPrimaryKey=Recomendamos que você selecione uma chave primária diferente no nível do item.
#XMSG
recommendDifferentPrimaryKeyDesc=Quando o código de operação já está definido, é recomendável selecionar chaves primárias diferentes para o índice de matriz e os itens a fim de evitar problemas como, por exemplo, duplicação de coluna.
#XMSG
selectPrimaryKeyItemLevel=Você deve selecionar pelo menos uma chave primária para os níveis de cabeçalho e de item.
#XMSG
selectPrimaryKeyItemLevelDesc=Quando uma matriz ou um mapa é expandido, você deve selecionar duas chaves primárias, uma no nível de cabeçalho e uma no nível de item.
#XMSG
invalidMapKey=Você deve selecionar pelo menos uma chave primária no nível de cabeçalho.
#XMSG
invalidMapKeyDesc=Quando uma matriz ou um mapa é expandido, você deve selecionar uma chave primária no nível de cabeçalho.
#XFLD
txtSearchFields=Procurar colunas de destino
#XFLD
txtName=Nome
#XMSG
txtSourceColValidation=Uma ou mais colunas de origem não são suportadas:
#XMSG
txtMappingCount=Mapeamentos ({0})
#XMSG
schema=Esquema
#XMSG
sourceColumn=Colunas de origem
#XMSG
warningSourceSchema=As alterações feitas no esquema afetarão os mapeamentos na caixa de diálogo de projeção.
#XCOL
txtTargetColName=Coluna de destino (nome técnico)
#XCOL
txtDataType=Tipo de dados de destino
#XCOL
txtSourceDataType=Tipo de dados de origem
#XCOL
srcColName=Coluna de origem (nome técnico)
#XCOL
precision=Precisão
#XCOL
scale=Escala
#XCOL
functionsOrConstants=Funções/Constantes
#XCOL
txtTargetColBusinessName=Coluna de destino (Nome comercial)
#XCOL
prKey=Chave primária
#XCOL
txtProperties=Propriedades
#XBUT
txtOK=Salvar
#XBUT
txtCancel=Cancelar
#XBUT
txtRemove=Remover
#XFLD
txtDesc=Descrição
#XMSG
rftdMapping=Mapeamento
#XFLD
@lblColumnDataType=Tipo de dados
#XFLD
@lblColumnTechnicalName=Nome técnico
#XBUT
txtAutomap=Mapear automaticamente
#XBUT
txtUp=Para cima
#XBUT
txtDown=Para baixo

#XTOL
txtTransformationHeader=Projeção
#XTOL
editTransformation=Editar
#XTOL
primaryKeyToolip=Chave


#XMSG
rftdFilter=Filtrar
#XMSG
rftdFilterColumnCount=Fonte: {0}({1})
#XTOL
rftdFilterColSearch=Procurar
#XMSG
rftdFilterColNoData=Nenhuma coluna para exibir
#XMSG
rftdFilteredColNoExps=Sem expressões de filtro
#XMSG
rftdFilterSelectedColTxt=Adicionar filtro para
#XMSG
rftdFilterTxt=Filtro disponível para
#XBUT
rftdFilterSelectedAddColExp=Adicionar expressão
#YINS
rftdFilterNoSelectedCol=Selecione uma coluna para adicionar um filtro.
#XMSG
rftdFilterExp=Expressão de filtro
#XMSG
rftdFilterNotAllowedColumn=A adição de filtros não é suportada para esta coluna.
#XMSG
rftdFilterNotAllowedHead=Coluna não suportada
#XMSG
rftdFilterNoExp=Nenhum filtro foi definido
#XTOL
rftdfilteredTt=Filtrado
#XTOL
rftdremoveexpTt=Remover expressão de filtro
#XTOL
validationMessageTt=Mensagens de validação
#XTOL
rftdFilterDateInp=Selecionar uma data
#XTOL
rftdFilterDateTimeInp=Selecionar uma data/hora
#XTOL
rftdFilterTimeInp=Selecionar um horário
#XTOL
rftdFilterInp=Inserir valor
#XMSG
rftdFilterValidateEmptyMsg=As expressões de filtro {0} na coluna {1} estão vazias
#XMSG
rftdFilterValidateInvalidNumericMsg=As expressões de filtro {0} na coluna {1} contêm valores numéricos inválidos
#XMSG
rftdFilterValidateAddExpInvalidNumericMsg=A expressão de filtro deve conter valores numéricos válidos
#XMSG: Information Message telling user how to adapt changes done to dwc target tables (using table editor) which were prev used in RF builder
messageForExistingDWCTargetModification=Se o esquema do objeto de destino for alterado, use a função "Mapear para objeto de destino existente" na página principal para adaptar as alterações e remapear o objeto de destino com a respectiva origem novamente.
#XMSG: Information Message telling user to change target table before deploying replication flow
messageForNewColumnBeforeDeployment=Quando a tabela de destino já existe e o mapeamento inclui uma alteração de esquema, você deve alterar a tabela de destino de forma adequada antes de implementar o fluxo de replicação.
#XMSG: Information message telling user that user needs to manually change target table for any schema change before deploying replication flow if it has already been deployed.
messageForNewColumnAfterDeployment=Quando o mapeamento envolve uma alteração de esquema, você deve alterar a tabela de destino de forma adequada antes de implementar o fluxo de replicação.
#XMSG: Information message telling user that unsupported cdc & Lob  columns are skipped in source
messageForSourceInvalidColumns=As seguintes colunas não suportadas foram ignoradas na definição de origem: {0}
#XMSG: Information message telling user that unsupported cdc & Lob columns are skipped in target
messageForTargetInvalidColumns=As seguintes colunas não suportadas foram ignoradas na definição de destino: {0}
#XMSG : Information message for Unsupported SAC Datasets. {0} {2} has been used for formatting purpose, and should not be altered. {1} contains the array of unsupported datasets along with the newlines and •
errorForDatasetFromSAC1=Os seguintes objetos não são suportados, eles estão expostos para consumo: {0} {1} {0} {0} Para usar as tabelas em um fluxo de replicação, o uso semântico (nas configurações da tabela) não pode estar definido como {2}Conjunto de dados analíticos{2}.
#XMSG : Information message for Unsupported SAC Dataset while map to exist. {0} is used for new lines , {1} is used for ""
errorForMapToExistingOfSACDataset1=Não é possível usar o objeto de destino, ele está exposto para consumo: {0} {0} Para usar a tabela em um fluxo de replicação, o uso semântico (nas configurações da tabela) não pode estar definido como {1}Conjunto de dados analíticos{1}.
#XMSG : Information message for Unsupported SAC Dataset while rename. {0} is used for new lines , {1} is used for ""
errorForRenameToSACDataset1=Já existe um objeto de destino com este nome. No entanto, ele não pode ser usado porque está exposto para consumo: {0} {0} Para usar a tabela em um fluxo de replicação, o uso semântico (nas configurações da tabela) não pode estar definido como {1}Conjunto de dados analíticos{1}.
#XMSG: Information message for Unsupported non DWC_LOCAL_TABLE Dataset while rename. {0} is used for new lines
errorForRenameToUnsupportedDWCDataset=Já existe um objeto com este nome no destino.{0}No entanto, esse objeto não pode ser usado como objeto de destino de um fluxo de replicação para o repositório local porque ele não é uma tabela local.
#XMSG:
targetAutoRenameUpdated=A coluna de destino foi renomeada.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDescUpdated1=A coluna de destino foi renomeada para permitir replicações no Google BigQuery. Isso se deve a um dos seguintes motivos:{0} {1}{2}nome de coluna reservado{3}{2}caracteres não suportados{3}{2}prefixo reservado{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetConfluentAutoRenameDesc=A coluna de destino foi renomeada para permitir replicações no Confluent. Isso se deve a um dos seguintes motivos:{0} {1}{2}nome de coluna reservado{3}{2}caracteres não suportados{3}{2}prefixo reservado{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetDatasphereAutoRenameDesc1=A coluna de destino foi renomeada para permitir replicações no destino. Isso se deve a um dos seguintes motivos:{0} {1}{2}caracteres não suportados{3}{2}prefixo reservado{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameGenericDesc=A coluna de destino foi renomeada para permitir replicações no destino. Isso se deve a um dos seguintes motivos:{0} {1}{2}nome de coluna reservado{3}{2}caracteres não suportados{3}{2}prefixo reservado{3}{4}
#XMSG:
targetAutoDataType=Tipo de dados de destino alterado.
#XMSG:
targetAutoDataTypeDesc=Tipo de dados de destino alterado para {0}, o tipo de dados de origem não é suportado no Google BigQuery.
#XMSG: 0 - Datatype , 1 - Connection Name, can be SAP Datasphere or SAP Datasphere ( HDL_FILES)
targetAutoDataTypeDWC2=Tipo de dados de destino alterado para {0}, o tipo de dados de origem não é suportado na conexão de destino.
#XMSG
projectionGBQUnableToCreateKey=As chaves primárias não serão criadas.
#XMSG
projectionGBQUnableToCreateKeyMaxKeyCombinationDescription=No Google BigQuery, são suportadas, no máximo, 16 chaves primárias, mas o objeto de origem tem um número maior de chaves primárias. Nenhuma das chaves primárias será criada no objeto de destino.
#XMSG
HDLFNoKeyError=Defina uma ou mais colunas como chave primária.
#XMSG
HDLFNoKeyErrorDescription=Para replicar um objeto, você precisa definir uma ou mais colunas como chave primária.
#XMSG
HDLFNoKeyErrorExistingTarget=Defina uma ou mais colunas como chave primária.
#XMSG Arguments {0} and {1} are being used for formatting
HDLFNoKeyErrorExistingTargetDescription3=Para replicar dados em um objeto de destino existente, ele deve ter uma ou mais colunas definidas como chave primária. {0} {0} Você tem as seguintes opções para definir uma ou mais colunas como chave primária: {0}{0}{1} Use o editor de tabelas local para alterar o objeto de destino existente. Em seguida, recarregue o fluxo de replicação.{0}{0}{1} Renomeie o objeto de destino no fluxo de replicação. Com isso, outro objeto será criado assim que a execução for iniciada. Depois de renomeá-lo, você pode definir uma ou mais colunas como chave primária em uma projeção.{0}{0}{1} Mapeie o objeto para outro objeto de destino existente em que uma ou mais colunas já estejam definidas como chave primária.
#XMSG
HDLFSourceTargetDifferentKeysWarning=Chave primária alterada.
#XMSG
HDLFSourceTargetDifferentKeysWarningDescription1=Em comparação com o objeto de origem, você definiu colunas diferentes como chave primária para o objeto de destino. Verifique se essas colunas identificam de forma exclusiva todas as linhas para evitar que os dados sejam corrompidos quando forem replicados posteriormente. {0} {0} No objeto de origem, as seguintes colunas estão definidas como chave primária: {0} {1}
#XMSG
duplicateDPIDColumns=Renomeie a coluna de destino.
#XMSG
duplicateDPIDDColumnsDesc1=Este nome de coluna de destino é reservado para uma coluna técnica. Insira um nome diferente para salvar a projeção.
#XMSG:
targetAutoRenameDPID=A coluna de destino foi renomeada.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDPIDDesc=A coluna de destino foi renomeada para permitir replicações a partir da fonte ABAP sem chaves. Isso se deve a um dos seguintes motivos:{0} {1}{2}Nome de coluna reservado{3}{2}Caracteres não suportados{3}{2}Prefixo reservado{3}{4}
#~~~ Connection Setting Dialog ~~~~~~~~~~~~~~~~~~

#XTIT: Title text for Target Settings of selected container or object
targetSettingTitle=Configurações de destino de {0}
#XTIT: Title text for Source Settings of selected container or object
sourceSettingTitle=Configurações de origem de {0}
#XBUT
connectionSettingSave=Salvar
#XBUT
connectionSettingCancel=Cancelar
#XBUT: Button to keep the object level settings
txtKeep=Manter
#XBUT: Button to overwrite the Object level settings
txtOverwrite=Sobregravar
#XFLD
targetConnectionThreadlimit=Limite de threads de destino para carregamento inicial (1-100)
#XFLD
connectionThreadLimit=Limite de threads de origem para carregamento inicial (1-100)
#XFLD
maxConnection=Limite de threads de replicação (1–100)
#XFLD
kafkaNumberOfPartitions=Número de partições
#XFLD
kafkaReplicationFactor=Fator de replicação
#XFLD
kafkaMessageEncoder=Codificador de mensagens
#XFLD
kafkaMessageCompression=Compressão de mensagens
#XFLD
fileGroupDeltaFilesBy=Delta de grupo por
#XFLD
fileFormat=Tipo de arquivo
#XFLD
csvEncoding=Codificação CSV
#XFLD
abapExitLbl=Saída ABAP
#XFLD
deltaPartition=Contagem de threads de objeto para carregamentos delta (1-10)
#XFLD
clamping_Data=Falha em caso de truncamento de dados
#XFLD
fail_On_Incompatible=Falha em caso de incompatibilidade de dados
#XFLD
maxPartitionInput=Número máximo de partições
#XFLD
max_Partition=Definir número máximo de partições
#XFLD
include_SubFolder=Incluir subpastas
#XFLD
fileGlobalPattern=Padrão global para nome de arquivo
#XFLD
fileCompression=Compressão de arquivo
#XFLD: csv files have deliminiters like comma, colon, semicolon
fileColumnDelimiter=Delimitador de arquivo
#XFLD
fileIsHeaderIncluded=Cabeçalho do arquivo
#XFLD
fileOrient=Orient
#XFLD
gbqWriteMode=Modo de gravação
#XFLD
suppressDuplicate=Suprimir duplicados
#XFLD
apacheSpark=Ativar compatibilidade com Apache Spark
#XFLD
clampingDatatypeCb=Limitar n  de casas decimais em tipos de dados de ponto flutuante
#XFLD
overwriteDatasetSetting=Sobregravar configurações de destino no nível do objeto
#XFLD
overwriteSourceDatasetSetting=Sobregravar configurações de origem no nível do objeto
#XMSG
kafkaInvalidConnectionSetting=Informe o número entre {0} e {1}.
#XMSG
MinReplicationThreadErrorMsg=Insira um número maior que {0}.
#XMSG
MaxReplicationThreadErrorMsg=Insira um número menor que {0}.
#XMSG
DeltaThreadErrorMsg=Insira um valor entre 1 e 10.
#XMSG
MaxPartitionErrorMsg=Insira um valor entre 1 <= x <= 2147483647. O valor padrão é 10.
#XMSG
MaxReplicationThreadErrorMsgForNonDecimal=Insira um inteiro entre {0} e {1}.
#XFLD: Inherit value from the Confluent host broker
useDefaultConfluentFactor=Usar fator de replicação do broker
#XFLD
serializationFormat=Formato de serialização
#XFLD
compressionType=Tipo de compressão
#XFLD
schemaRegistry=Usar Schema Registry
#XFLD
subjectNameStrat=Estratégia de nome do subject
#XFLD
compatibilityType=Tipo de compatibilidade
#XFLD
confluentTopicName=Nome do tópico
#XFLD
confluentRecordName=Nome do registro
#XFLD
confluentSubjectNamePreview=Visualização do nome do subject
#XMSG
serializationChangeToastMsgUpdated2=O formato de serialização alterado para JSON como registro do esquema não está ativado. Para alterar o formato de serialização de volta para AVRO, você deve ativar primeiro o registro do esquema.
#XBUT
confluentTopicNameInfo=O nome do tópico sempre é baseado no nome do objeto de destino. Para alterá-lo, renomeie o objeto de destino.
#XMSG
emptyRecordNameValidationHeaderMsg=Insira um nome de registro.
#XMSG
emptyPartionHeader=Insira o número de partições.
#XMSG
invalidPartitionsHeader=Insira um número válido de partições.
#XMSG
invalidpartitionsDesc=Inserir um número entre 1 e 200.000.
#XMSG
emptyrFactorHeader=Insira um fator de replicação.
#XMSG
invalidrFactorHeader=Insira um fator de replicação válido.
#XMSG
invalidrFactorDesc=Inserir um número entre 1 e 32.767.
#XMSG
emptyRecordNameValidationDescMsg=Quando o formato de serialização "AVRO" é usado, apenas os seguintes caracteres são suportados:{0}{1} A–Z{0}{1} a–z{0}{1} 0–9{0}{1} _(sublinhado)
#XMSG
validRecordNameValidationHeaderMsg=Insira um nome de registro válido.
#XMSG
validRecordNameValidationDescMsgUpdated=Como o formato de serialização "AVRO" foi usado, o nome do registro deve consistir apenas de caracteres alfanuméricos (A–Z, a–z, 0–9) e sublinhados (_). Ele deve começar com uma letra ou um sublinhado.
#XMSG
targetObjectNameSubTitle={0}
#XMSG
deltaPartitionnotEnabled=A "Contagem de threads de objeto para carregamentos delta" pode ser definida assim que um ou mais objetos tiverem o tipo de carregamento "Inicial e delta".
#XMSG
invalidTargetName=Nome de coluna inválido
#XMSG
invalidTargetNameDesc=O nome da coluna de destino deve consistir apenas de caracteres alfanuméricos (A–Z, a–z, 0–9) e sublinhados (_).
#XFLD
consumeOtherSchema=Consumir outras versões do esquema
#XFLD
ignoreSchemamissmatch=Ignorar incompatibilidade do esquema
#XFLD
confleuntDatatruncation=Falha em caso de truncamento de dados
#XFLD
isolationLevel=Nível de isolamento
#XFLD
confluentOffset=Ponto de partida
#XFLD
signavioGroupDeltaFilesByText=Nenhum
#XFLD
signavioFileFormatText=Parquet
#XFLD
signavioSparkCompatibilityParquetText=Não
#XFLD
siganvioFileCompressionText=Snappy
#XFLD
siganvioSuppressDuplicatesText=Não

#~~~~ Replication Task properties panel ~~~~~~~~~~~

#XFLD
lblTransformationProperties=Projeções
#XBUT
txtAdd=Adicionar
#XBUT
txtEdit=Editar
#XMSG
transformationText=Adicione uma projeção para configurar o filtro ou o mapeamento.
#XMSG
primaryKeyRequiredText=Selecione uma chave primária com Configurar esquema.
#XFLD
lblSettings=Configurações
#XFLD
lblTargetSetting={0}: Configurações de destino
#XMSG
@csvRF=Selecione o arquivo que contém a definição de esquema que você deseja aplicar em todos os arquivos da pasta.
#XFLD
lblSourceColumns=Colunas de origem
#XFLD
lblJsonStructure=Estrutura de JSON
#XFLD
lblSourceSetting={0}: Configurações de origem
#XFLD
lblSourceSchemaSetting={0}: Configurações do esquema de origem
#XBUT
messageSettings=Configurações de mensagem
#XFLD
lblPropertyTitle1=Propriedades do objeto
#XFLD
lblRFPropertyTitle=Propriedades do fluxo de replicação
#XMSG
noDataTxt=Não há colunas para serem exibidas.
#XMSG
noTargetObjectText=Não há objeto de destino selecionado.
#XLNK Ignore for translation
txtLessThen=<
#XFLD
targetColumns=Colunas de destino
#XMSG
searchColumns=Procurar colunas
#XTOL
cdcColumnTooltip=Coluna para captura de delta
#XMSG
sourceNonDeltaSupportErrorUpdated=O objeto de origem não suporta a captura delta.
#XMSG
targetCDCColumnAdded=2 colunas de destino foram adicionadas para captura de delta.
#XMSG
deltaPartitionEnable=Limite de threads de objeto para carregamentos delta adicionado às configurações de origem.
#XMSG
attributeMappingRemovalTxt=Removendo mapeamentos inválidos que não são suportados para o novo objeto de destino.
#XMSG
targetCDCColumnRemoved=2 colunas de destino usadas para captura de delta foram removidas.
#XMSG
replicationLoadTypeChanged=Tipo de carregamento alterado para "Inicial e delta".
#XMSG
sourceHDLFLoadTypeError=Altere o tipo de carregamento para "Inicial e delta".
#XMSG
sourceHDLFLoadTypeSubtitle={0}
#XMSG
sourceHDLFLoadTypeErrorDescription1=Para replicar um objeto de uma conexão de origem do tipo Arquivos do SAP HANA Cloud, data lake com o SAP Datasphere, você precisa usar o tipo de carregamento "Inicial e delta".
#XMSG
sourceHDLFDeltaCaptureErrorForNewTarget=Ative a captura de delta.
#XMSG
sourceHDLFDeltaCaptureSubtitleForNewTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForNewTargetDescription1=Para replicar um objeto de uma conexão de origem do tipo Arquivos do SAP HANA Cloud, data lake com o SAP Datasphere, você precisa ativar a captura de delta.
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTarget=Altere o objeto de destino.
#XMSG
sourceHDLFDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTargetDescription1=O objeto de destino não pode ser usado porque a captura de delta está desativada. Você pode renomear o objeto de destino (que permite a criação de um outro objeto com captura de delta) ou mapeá-lo para um objeto existente com captura de delta ativada.
#XMSG
deltaPartitionError=Insira uma contagem de threads de objeto válida para carregamentos delta.
#XMSG
deltaPartitionErrorDescription=Insira um valor entre 1 e 10.
#XMSG
deltaPartitionEmptyError=Insira uma contagem de threads de objeto para carregamentos delta.
#XFLD
@lblColumnDescription=Descrição
#XMSG
@lblColumnDescriptionText1=Para fins técnicos – tratamento de registros duplicados causados por problemas durante a replicação de objetos de origem baseados em ABAP que não tem uma chave primária.
#XFLD
storageType=Armazenamento
#XFLD
skipUnmappedColLbl=Ignorar colunas não mapeadas
#XFLD
abapContentTypeLbl=Tipo de conteúdo
#XFLD
autoMergeForTargetLbl=Mesclar dados automaticamente
#~~~~~~ Replication flow properties panel ~~~~~~~~~

#XFLD
lblGeneral=Geral
#XFLD
lblBusinessName=Nome comercial
#XFLD
lblTechnicalName=Nome técnico
#XFLD
lblPackage=Pacote
#XFLD
statusPanel=Status da execução
#XBTN: Schedule dropdown menu
SCHEDULE=Programar
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Editar programação
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Excluir programação
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Criar programação
#XFLD: empty cell
EMPTY_CELL=---
#XMSG:
SCHEDULE_CHECK_FAILED=Falha ao verificar validação de programação
#XMSG: error when creating schedule when in deploying state
msgReplicationFlowDeployScheduleError=Não é possível criar uma programação, o fluxo de replicação está em implementação.{0}Aguarde a implementação do fluxo de replicação.
#XMSG: replication flow create schedule Error
msgReplicationFlowScheduleErrorText=Para fluxos de replicação que contêm objetos com tipo de carregamento "Inicial e delta", não é possível criar programação.
#XMSG
msgReplicationFlowScheduleErrorTextUpdated=Para fluxos de replicação que contêm objetos com tipo de carregamento "Inicial e delta/Só delta", não é possível criar programação.
#XFLD : Scheduled popover
SCHEDULED=Programado
#XFLD
CREATE_REPLICATION_TEXT=Criar um fluxo de replicação
#XFLD
EDIT_REPLICATION_TEXT=Editar um fluxo de replicação
#XFLD
DELETE_REPLICATION_TEXT=Excluir um fluxo de replicação
#XFLD
REFRESH_FREQUENCY=Frequência
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT=O fluxo de replicação não pode ser implementado porque a programação existente{0} não suporta o tipo de carregamento "Inicial e delta".{0}{0}Para implementar o fluxo de replicação, você deve definir os tipos de carregamento de todos os objetos{0} como "Só inicial". Como alternativa, você pode excluir a programação, implementar o {0}fluxo de replicação e iniciar uma nova execução. Isso resultará em uma execução sem {0}término, que também suportará objetos com o tipo de carregamento "Inicial e delta".
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT_UPDATED=O fluxo de replicação não pode ser implementado porque a programação existente{0} não suporta o tipo de carregamento "Inicial e delta/Só delta".{0}{0}Para implementar o fluxo de replicação, você deve definir os tipos de carregamento de todos os objetos{0} como "Só inicial". Como alternativa, você pode excluir a programação, implementar o {0}fluxo de replicação e iniciar uma nova execução. Isso resultará em uma execução sem {0}término, que também suportará objetos com o tipo de carregamento "Inicial e delta/Só delta".
#XMSG
SCHEDULE_EXCEPTION=Falha ao obter os detalhes da programação
#XFLD: Label for frequency column
everyLabel=A cada
#XFLD: Plural Recurrence text for Hour
hoursLabel=Horas
#XFLD: Plural Recurrence text for Day
daysLabel=Dias
#XFLD: Plural Recurrence text for Month
monthsLabel=Meses
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minutos
#XMSG
VALIDATE_EXCEPTION_SCHEDULE_POSSIBLE=Falha ao obter informações sobre possibilidade de programação.
#XFLD :Paused field
PAUSED=Pausado
#XMSG
navToMonitoring=Abrir no Monitor de fluxos
#XFLD
statusLbl=Status
#XFLD
lblLastRunExecuted=Início da última execução
#XFLD
lblLastExecuted=Última execução
#XFLD: Status text for Completed
statusCompleted=Concluído
#XFLD: Status text for Running
statusRunning=Em execução
#XFLD: Status text for Failed
statusFailed=Com falha
#XFLD: Status text for Stopped
statusStopped=Interrompido
#XFLD: Status text for Stopping
statusStopping=Interrompendo
#XFLD: Status text for Active
statusActive=Ativo
#XFLD: Status text for Paused
statusPaused=Pausado
#XFLD: Status text for not executed
lblNotExecuted=Não executado
#XFLD
messagesSettings=Configurações de mensagens
#XTOL
@validateModel=Mensagens de validação
#XTOL
@hierarchy=Hierarquia
#XTOL
@columnCount=Número de colunas
#XMSG
VAL_PACKAGE_CHANGED=Você atribuiu este objeto ao pacote "{1}". Clique em “Salvar” para confirmar e validar essa alteração. Observe que a atribuição a um pacote não pode ser desfeita neste editor depois que ele é salvo.
#XMSG
MISSING_DEPENDENCY=Não é possível resolver as dependências do objeto "{0}" no pacote "{1}".
#XFLD
deltaLoadInterval=Intervalo de carregamento de delta
#XFLD
lblHour=Horas (0-24)
#XFLD
lblMinutes=Minutos (0-59)
#XMSG
maxHourOrMinErr=Insira um valor entre 0 e {0}.
#XMSG
maxDeltaInterval=O valor máximo de intervalo de carregamento de delta é 24 horas.{0}Altere o valor de minutos ou de horas de forma adequada.
#~~~ Container Path ~~~~~~~~~~~~~~~~~~
#XFLD
targetContainernPathText=Caminho do container de destino
#XFLD
confluentSubjectName=Nome do sujeito
#XFLD
confluentSchemaVersion=Versão do esquema
#XFLD
confluentIncludeTechKeyUpdated=Incluir chave técnica
#XFLD
confluentOmitNonExpandedArrays=Omitir matrizes não expandidas
#XFLD
confluentExpandArrayOrMap=Expandir matriz ou mapa
#XCOL
confluentOperationMapping=Mapeamento de operação
#XCOL
confluentOpCode=Opcode
#XFLD
confluentInsertOpCode=Inserir
#XFLD
confluentUpdateOpCode=Atualizar
#XFLD
confluentDeleteOpCode=Excluir
#XFLD
expandArrayOrMapNotSelectedTxt=Não selecionado
#XFLD
confluentSwitchTxtYes=Sim
#XFLD
confluentSwitchTxtNo=Não
#~~~ RF Execute  ~~~~~~~~~~~~~~~~~~

#XTIT
executeError=Erro
#XTIT
executeWarning=Aviso
#XMSG
executeunsavederror=Salve seu fluxo de replicação antes de executá-lo.
#XMSG
executemodifiederror=Há alterações não salvas no fluxo de replicação. Salve o fluxo de replicação.
#XMSG
executeundeployederror=Você deve implementar o fluxo de replicação antes de executá-lo.
#XMSG
executedeployingerror=Aguarde a conclusão da implementação.
#XMSG
msgRunStarted=Execução iniciada
#XMSG
msgExecuteFail=Falha ao executar o fluxo de replicação.
#XMSG
titleExecuteBusy=Aguarde.
#XMSG
msgExecuteBusy=Estamos preparando seus dados para executar o fluxo de replicação.
#XTIT
executeConfirmDialog=Aviso
#XMSG
msgExecuteWithValidations=O fluxo de replicação tem erros de validação. A execução do fluxo de replicação pode resultar em falhas.
#XMSG
msgRunDeployedVersion=Há alterações a serem implementadas. A última versão implementada do fluxo de replicação será executada. Deseja continuar?
#XBUT
btnExecuteAnyway=Executar mesmo assim
#XBUT
btnExecuteClose=Fechar
#XBUT
loaderClose=Fechar
#XTIT
loaderTitle=Carregando
#XMSG
loaderText=Buscando detalhes do servidor
#XMSG
premiumOutBoundRFCannotStartErrMsgPart1=O fluxo de replicação para essa conexão de destino não SAP não pode ser iniciado,
#XMSG
premiumOutBoundRFCannotStartErrMsgPart2=não há volume de saída disponível para esse mês.
#XMSG
premiumOutBoundRFAdminErrMsgPart1=Um administrador pode aumentar os blocos Saída premium para esse
#XMSG
premiumOutBoundRFAdminErrMsgPart2=locatário, disponibilizando volume de saída para esse mês.


#~~~ RF Deploy ~~~~~~~~~~~~~~
#XTIT
deployError=Erro
#XTIT
deployInfo=Informações
#XMSG
deployCheckFailException=Exceção durante implementação
#XMSG
deployGBQFFDisabled=Atualmente não é possível implementar fluxos de replicação com uma conexão de destino para o Google BigQuery, estamos realizando manutenções nessa função.
#XMSG
deployKAFKAFFDisabled=Atualmente não é possível implementar fluxos de replicação com uma conexão de destino para o Apache Kafka, estamos realizando manutenções nessa função.
#XMSG
deployConfluentDisabled=Atualmente não é possível implementar fluxos de replicação com uma conexão de destino para o Confluent Kafka, estamos realizando manutenções nessa função.
#XMSG
deployInValidDWCTargetDeltaCaptureObject=Para os objetos de destino a seguir, os nomes de tabela de captura de delta já foram usados por outras tabelas no repositório: {0} Você deve renomear esses objetos de destino para garantir que os nomes das tabelas de captura de delta associadas sejam exclusivos antes de implementar o fluxo de replicação.
#XMSG
deployDWCSourceFFDisabled=Atualmente, não é possível implementar fluxos de replicação que têm o SAP Datasphere como fonte porque estamos realizando manutenção nessa função.
#XMSG
deployDWCSourceWithDeltaEnabledInitialFFDisabled=Atualmente, não é possível implementar fluxos de replicação que contenham tabelas locais com delta ativado como objeto de origem porque estamos realizando manutenção nessa função.
#XMSG
deployHDLFSourceFFDisabled=Atualmente, não é possível implementar fluxos de replicação que têm conexões de fonte do tipo Arquivos do SAP HANA Cloud, data lake porque estamos realizando manutenção.
#XMSG
deployObjectStoreAsSourceFFDisabled=Atualmente, não é possível implementar fluxos de replicação que tenham provedores de armazenamento em nuvem como fonte.
#XMSG
deployConfluentSourceFFDisabled=Atualmente, não é possível implementar fluxos de replicação que têm o Confluent Kafka como fonte porque estamos realizando manutenção nessa função.
#XMSG
deployMaxDWCNewTableCrossed=Para fluxos de replicação grandes, não é possível "salvar e implementar" de uma só vez. Salve primeiro seu fluxo de replicação e, em seguida, implemente-o.
#XMSG
deployInProgressInfo=A implementação já está em andamento.
#XMSG
deploySourceObjectInUse=Os objetos de origem {0} já estão sendo usados nos fluxos de replicação {1}.
#XMSG
deployTargetSourceObjectInUse=Os objetos de origem {0} já estão sendo usados nos fluxos de replicação {1}. Os objetos de destino {2} já estão sendo usados nos fluxos de replicação {3}.
#XMSG
deployReplicationFlowCheckError=Erro ao verificar o fluxo de replicação: {0}
#XMSG
preDeployTargetObjectInUse=Os objetos de destino {0} já estão sendo usados em fluxos de replicação {1}, e você não pode ter o mesmo objeto de destino em dois fluxos de replicação diferentes. Selecione outro objeto de destino e tente novamente.
#XMSG
runInProgressInfo=O fluxo de replicação já está em execução.
#XMSG
deploySignavioTargetFFDisabled=Atualmente não é possível implementar fluxos de replicação usando o SAP Signavio como destino porque estamos realizando manutenção nessa função.
#XMSG
deployHanaViewAsSourceFFDisabled=Atualmente, não é possível implementar fluxos de replicação que têm visões como objetos de origem para a conexão de fonte selecionada. Tente novamente mais tarde.
#XMSG
deployMsOneLakeTargetFFDisabled=Atualmente não é possível implementar fluxos de replicação que usam o MS OneLake como destino porque estamos realizando manutenção nessa função.
#XMSG
deploySFTPTargetFFDisabled=Atualmente não é possível implementar fluxos de replicação que usam o SFTP como destino porque estamos realizando manutenção nessa função.
#XMSG
deploySFTPSourceFFDisabled=Atualmente não é possível implementar fluxos de replicação que usam o SFTP como origem porque estamos realizando manutenção nessa função.
#~~~ Rename Target ~~~~~~~~~~~~~~~~~~~
#XFLD
renameTargetFieldName=Nome técnico
#XFLD
businessNameInRenameTarget=Nome comercial
#XTOL
renametargetDialogTitle=Renomear objeto de destino
#XBUT
targetRenameButton=Renomear
#XBUT
targetRenameCancel=Cancelar
#XMSG
mandatoryTargetName=Você deve inserir um nome.
#XMSG
dwcSpecialChar=_(sublinhado) é o único caractere especial permitido.
#XMSG
dwcWithDot=O nome da tabela de destino pode conter letras do alfabeto latino, números, sublinhados (_) e pontos finais (.). O primeiro caractere deve ser uma letra, um número ou um sublinhado (não um ponto final).
#XMSG
nonDwcSpecialChar=Os caracteres especiais permitidos são _(sublinhado) -(hífen) .(ponto)
#XMSG
firstUnderscorePattern=O nome não pode começar com um _ (sublinhado).

#~~~~ DDL dialog ~~~~~~~~~~~~~~~~~#
#XTIT
sqlTargetDDL={0}: Exibir instrução SQL de criação de tabela
#XMSG
sqlDialogMaxPKWarning=No Google BigQuery, são suportadas, no máximo, 16 chaves primárias, e o objeto de origem tem um número maior. Nenhuma chave primária foi definida para essa instrução.
#XMSG
sqlDialogIncomptiblePKTypeWarning=Uma ou mais colunas de origem têm tipos de dados que não podem ser definidos como chaves primárias no Google BigQuery. Nesse caso, nenhuma chave primária foi definida. No Google BigQuery, apenas os seguintes tipos de dados podem ter uma chave primária: BIGNUMERIC, BOOLEAN, DATE, DATETIME, INT64, NUMERIC, STRING, TIMESTAMP
#XBUT
copyAndCloseDDL=Copiar e fechar
#XBUT
closeDDL=Fechar
#XMSG
copiedToClipboard=Copiado para clipboard


#~~~~ RF Service backend translated text ~~~~~~~~~~~~~~~~~#
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY=O objeto ''{0}'' não pode fazer parte de uma cadeia de tarefas porque não tem um fim (ele inclui objetos com tipo de carregamento Inicial e delta/Só delta).
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY_DELTA_OFF=O objeto ''{0}'' não pode fazer parte de uma cadeia de tarefas porque não tem um fim (ele inclui objetos com tipo de carregamento Inicial e delta).
#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=O objeto "{0}" não pode ser adicionado à cadeia de tarefas.


#~~~ RF Open ~~~~~~~~~~~~~~
#XMSG: Information prompt to user when he opens an older replication flow with dsp target objects that are not saved yet. {0} is used for new lines
openReplicationFlowUnsavedTargetObject=Existem objetos de destino não salvos. Salve-os novamente .{0}{0} O comportamento desse recurso foi alterado: no passado, objetos de destino só eram criados no ambiente de destino quando o fluxo de replicação era implementado.{0} Agora, os objetos já são criados quando o fluxo de replicação é salvo. Seu fluxo de replicação foi criado antes dessa alteração e contém objetos novos.{0} Você precisa salvar o fluxo de replicação mais uma vez novamente antes de implementá-lo para que os novos objetos sejam incluídos corretamente.
#XMSG
confirmChangeContentTypeMessage=Você está prestes a alterar o tipo de conteúdo. Se fizer isso, todas as projeções existentes serão excluídas.

#~~~ Schema change Dialog ~~~~~~~~~~~~~~
#XFLD
schemaDialogSubjectName=Nome do sujeito
#XFLD
schemaDialogVersionName=Versão do esquema
#XFLD
includeTechKey=Incluir chave técnica
#XFLD
segementButtonFlat=Plano
#XFLD
segementButtonNested=Aninhado
#XMSG
subjectNamePlaceholder=Pesquisar nome do sujeito

#XMSG
@EmailNotificationSuccess=A configuração de notificações por e-mail em tempo de execução é salva.

#XFLD
@RuntimeEmailNotification=Notificação por e-mail em tempo de execução

#XBTN
@TXT_SAVE=Salvar


