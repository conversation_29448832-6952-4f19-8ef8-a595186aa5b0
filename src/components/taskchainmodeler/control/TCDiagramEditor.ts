/**
 * Copyright 2021 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

/* eslint-disable no-underscore-dangle */
/* eslint-disable spaced-comment */
import {
  BaseDiagramEditorControl,
  PrivateBaseDiagramEditorControl,
} from "../../commonui/control/diagrameditor/BaseDiagramEditor";
import { ShellContainer } from "../../shell/utility/Container";
import { DWCFeature, EventType } from "../../shell/utility/ShellUsageCollectionService";
import Utils from "../js/utils";
import "./TCDiagramEditor.control.xml";
import { JSONTCAdapter } from "./extension/JSONTCAdapter";
require("../../commonui/control/diagrameditor/BaseDiagramEditor");

jQuery.sap.declare("sap.cdw.components.taskchainmodeler.control.TCDiagramEditor");
sap.ui.define(
  "sap/cdw/components/taskchainmodeler/control/TCDiagramEditor",
  ["sap/cdw/components/commonui/control/diagrameditor/BaseDiagramEditor"],
  function (BaseDiagramEditor) {
    "use strict";
    return BaseDiagramEditor.extend("sap.cdw.components.taskchainmodeler.control.TCDiagramEditor", {
      resourceModel: undefined,
      metadata: {
        properties: {},
        aggregations: {},
        associations: {},
        events: {
          onCreateEntity: {
            parameters: {},
          },
        },
      },
      fragment: require("./TCDiagramEditor.control.xml"),
      init: function () {
        // eslint-disable-next-line prefer-rest-params
        const control = this as PrivateTCDiagramEditorControl;
        // eslint-disable-next-line prefer-rest-params
        BaseDiagramEditor.prototype.init?.apply(this, arguments);
        const csnAdapter = new JSONTCAdapter(control);
        control.setModelHandler(csnAdapter.getModelHandler());
        control.setDocumentAdapter(csnAdapter);
        control.setEditorExtensionClassName("sap.cdw.taskchainmodeler.ui.DiagramEditorExtension");
      },

      getResourceBundleExtension(): sap.base.i18n.ResourceBundle[] {
        const bundleName = require("../i18n/i18n.properties");
        const resourceModel = new sap.ui.model.resource.ResourceModel({
          bundleName: bundleName,
        });
        return [resourceModel.getResourceBundle()];
      },

      onAfterRendering: function () {
        // eslint-disable-next-line prefer-rest-params
        BaseDiagramEditor.prototype.onAfterRendering?.apply(this, arguments);
      },

      onSourceDragEnter(data: any): void {
        const control = this as PrivateTCDiagramEditorControl;
        if (control.getVisible()) {
          const model = this.getModelHandler().getModel();
          control.onCreateDataSource(data);
        }
      },

      onOperatorClick(oEvent): void {
        const oBindingContext = oEvent.oSource.getBindingContext("uiModel");
        if (oBindingContext) {
          const oObject = oBindingContext.getObject();
          const model = this.getModelHandler().getModel();
          const aOpers = model?.nodes?.filter((obj) => obj.isOperator) || [];
          const operatorName = sap.cdw.taskchainmodeler.ModelImpl.getUniqueAlias(aOpers, oObject.component);
          if (oObject && oObject.component) {
            const diagramEditor = this.getDiagramEditor();
            const oCreateParams = {
              objectParam: {
                operationType: oObject.component,
                name: operatorName,
              },
            };
            diagramEditor.selectTool(diagramEditor.extension.OPERATION_SYMBOL, true, oCreateParams);
            diagramEditor._tool.objectParam.operationType = oObject.component;
            diagramEditor._tool.objectParam.name = oCreateParams.objectParam.name;
            this.getParentController().onDefaultSourceDragEnter("", "", oCreateParams.objectParam);
          }
        }
      },

      onCreateDataSource(oData: any): void {
        const diagramEditor: sap.galilei.ui.editor.DiagramEditor = this.getDiagramEditor();
        let symbolTool;
        if (oData.operationType) {
          const model = this.getModelHandler().getModel();
          const aOpers = model?.nodes?.filter((obj) => obj.isOperator) || [];
          const operatorName = sap.cdw.taskchainmodeler.ModelImpl.getUniqueAlias(aOpers, oData.operationType);
          oData.name = operatorName;
          symbolTool = "sap.cdw.taskchainmodeler.ui.OperationSymbol";
        } else {
          symbolTool = "sap.cdw.taskchainmodeler.ui.TaskSymbol";
          Utils.assignTaskType(oData);
          const tool = sap.galilei.ui.editor.tool.getTool(symbolTool);
          if (tool) {
            tool.objectParam = undefined;
          }
        }
        const oCreateParams = oData && {
          objectParam: oData,
        };
        diagramEditor.selectTool(symbolTool, true, oCreateParams);
        (this.getView().getModel("workbenchEnv") as sap.ui.model.json.JSONModel).setProperty("/isDragging", true);
      },
      orientationsBtnTapped(): void {
        const model: sap.galilei.ui5.GalileiModel = this.getView().getModel(
            "galileiModel"
          ) as sap.galilei.ui5.GalileiModel,
          isVerticalView = model.getProperty("/isVerticalView"),
          newStatus = isVerticalView === "undefined" ? false : !isVerticalView;

        setTimeout(() => {
          model.getProperty("/").resource.applyUndoableAction(
            function () {
              window.sessionStorage.setItem("isVerticalView", JSON.stringify(newStatus));
              model.setProperty("/isVerticalView", newStatus);
              model.setProperty("/bRedrawDiagram", true);
              sap.cdw.taskchainmodeler.ModelImpl.requestAdjustDiagramsContent({
                autoLayout: true,
                model: model.getProperty("/"),
                protectFromUndoRedo: true,
              });
            },
            "change orientation protected from undo",
            true
          );
        }, 300);
      },
      orientationsBtnHandler(objects: any[]) {
        let isEnabled = false;
        isEnabled = !(objects?.length <= 12);
        return isEnabled;
      },
      onDelete(): void {
        this.getDiagramEditor().deleteSelectedSymbols();
        const model = this.getModelHandler().getModel();
        this.getDiagramEditor()?.controller?.getParentController?.()._onSymbolSelectionChange();
      },

      onAutoLayout(): void {
        this.getDiagramBuilder().autoLayout(this.getDiagramEditor(), true);
        if (sap.cdw.taskchainmodeler.Validation) {
          setTimeout(() => {
            sap.cdw.taskchainmodeler.Validation.requestRefreshDecorators(this.getModelHandler().getModel());
          }, 400);
        }
        // record action for usage tracking
        ShellContainer.get()
          .getUsageCollectionService()
          .recordAction({
            action: Utils.UsageActions.AUTO_LAYOUT,
            feature: DWCFeature.DATA_BUILDER,
            eventtype: EventType.CLICK,
            options: [
              {
                param: "target",
                value: Utils.USAGE_TASKCHAIN_MODELER,
              },
            ],
          });
      },

      reloadProperty(node) {
        sap.ui.getCore().getEventBus().publish("TASKCHAIN_MODELER", "PROPERTY_PANEL_RELOAD", { oNode: node });
      },

      getDiagramBuilder() {
        return this.getModelHandler().getDiagramBuilder();
      },

      onDragStart(event) {
        // eslint-disable-next-line no-underscore-dangle
        if (event.oSource.getParent()._inactiveButton) {
          // eslint-disable-next-line no-underscore-dangle
          event.oSource.getParent()._inactiveButton();
        }
        const target = event.getParameter("target");
        const operatorModel = target.getBindingContext("uiModel").getObject();
        const browserEvent = event.getParameter("browserEvent");
        const eventDataTransfer =
          browserEvent.dataTransfer || (browserEvent.originalEvent && browserEvent.originalEvent.dataTransfer);
        eventDataTransfer && eventDataTransfer.setDragImage(document.createElement("img"), 25, 25); // remove ghost image
        this.getParentController().onDefaultSourceDragEnter("", "", { operationType: operatorModel.component });
      },

      onAddPlaceholderOnSelected(oEvent) {
        this._addPlaceHolders(false);
      },

      onAddParallelBranchOnSelected(oEvent) {
        this._addPlaceHolders(true);
      },

      _addPlaceHolders(isParallel) {
        const diagramEditor = this.getDiagramEditor();
        const self = this;
        if (diagramEditor.selectedSymbols.length > 0) {
          const oSymbol = diagramEditor.selectedSymbols.get(0);
          if (["TaskSymbol", "OperationSymbol"].includes(oSymbol?.classDefinition?.name) && oSymbol.object) {
            const oObject = oSymbol.object;
            let oPredecessorSymbol = oSymbol;
            if (isParallel) {
              const oPredecessorNode = oObject?.predecessorNodes.length && oObject.predecessorNodes[0];
              oPredecessorSymbol =
                oPredecessorNode && this.getDiagramBuilder().relatedSymbols(oPredecessorNode, diagramEditor.diagram);
              if (oPredecessorSymbol.length > 0) {
                oPredecessorSymbol = oPredecessorSymbol[0];
              } else {
                oPredecessorSymbol = undefined;
              }
            }
            if (oPredecessorSymbol) {
              oSymbol.resource.applyUndoableAction(function () {
                oPredecessorSymbol.object.placeHolderNodes = oPredecessorSymbol.object.placeHolderNodes + 1;
                self
                  .getDiagramBuilder()
                  .addPlaceHolderSymbols(oPredecessorSymbol, diagramEditor.diagram, diagramEditor);
                sap.cdw.taskchainmodeler.ModelImpl.requestAdjustDiagramsContent({
                  autoLayout: true,
                  selection: oSymbol.object,
                });
              });
            }
          }
        }
      },

      getParentController() {
        const control = this as PrivateTCDiagramEditorControl;
        const controller = (control.getParent() as any).getController();
        return controller;
      },

      onApiClick(oEvent) {
        const model = this.getModelHandler().getModel();
        const oApiTasks = model?.nodes?.filter((obj) => obj.isRestApi) || [];
        let apiName = this.getUniqueAlias(oApiTasks, "APITask");
        // apiName = apiName.replace(/ /g, "_");
        this.getParentController().onDefaultSourceDragEnter("", "", {
          isRestApi: true,
          name: apiName,
          id: apiName,
          applicationId: "API",
          icon: "sap-icon://sac/open-connectors",
        });
      },

      onApiDragStart(event) {
        // eslint-disable-next-line no-underscore-dangle
        if (event.oSource.getParent()._inactiveButton) {
          // eslint-disable-next-line no-underscore-dangle
          event.oSource.getParent()._inactiveButton();
        }
        const model = this.getModelHandler().getModel();
        const oApiTasks = model?.nodes?.filter((obj) => obj.isRestApi) || [];
        let apiName = this.getUniqueAlias(oApiTasks, "APITask");
        // apiName = apiName.replace(/ /g, "_");
        const browserEvent = event.getParameter("browserEvent");
        const eventDataTransfer =
          browserEvent.dataTransfer || (browserEvent.originalEvent && browserEvent.originalEvent.dataTransfer);
        eventDataTransfer && eventDataTransfer.setDragImage(document.createElement("img"), 25, 25); // remove ghost image
        this.getParentController().onDefaultSourceDragEnter("", "", {
          isRestApi: true,
          name: apiName,
          id: apiName,
          applicationId: "API",
          icon: "sap-icon://sac/open-connectors",
        });
      },

      onNotificationTaskClick(oEvent) {
        const model = this.getModelHandler().getModel();
        const oNotificationTasks = model?.nodes?.filter((obj) => obj.isNotificationTask) || [];
        const notificationName = this.getUniqueAlias(oNotificationTasks, "Notification");
        this.getParentController().onDefaultSourceDragEnter("", "", {
          isNotificationTask: true,
          name: notificationName,
          id: notificationName,
          applicationId: "NOTIFICATION",
          icon: "sap-icon://ui-notifications",
        });
      },

      onNotificationTaskDragStart(event) {
        // eslint-disable-next-line no-underscore-dangle
        if (event.oSource.getParent()._inactiveButton) {
          // eslint-disable-next-line no-underscore-dangle
          event.oSource.getParent()._inactiveButton();
        }
        const model = this.getModelHandler().getModel();
        const oNotificationTasks = model?.nodes?.filter((obj) => obj.isNotificationTask) || [];
        const notificationName = this.getUniqueAlias(oNotificationTasks, "Notification");
        const browserEvent = event.getParameter("browserEvent");
        const eventDataTransfer =
          browserEvent.dataTransfer || (browserEvent.originalEvent && browserEvent.originalEvent.dataTransfer);
        eventDataTransfer && eventDataTransfer.setDragImage(document.createElement("img"), 25, 25); // remove ghost image
        this.getParentController().onDefaultSourceDragEnter("", "", {
          isNotificationTask: true,
          name: notificationName,
          id: notificationName,
          applicationId: "NOTIFICATION",
          icon: "sap-icon://ui-notifications",
        });
      },

      getUniqueAlias(oObjects, name) {
        const aNames = {};
        let sAlias;
        const suffix = Math.floor(Math.random() * 10000);
        const sName = `${name}_${suffix}`;
        sAlias = sName;
        const setUniqueAlias = function () {
          let n = 0;
          sAlias = "";
          do {
            sAlias = `${name}_${n++}`;
          } while (aNames[sAlias]);
          aNames[sAlias] = {
            actualName: sAlias,
          };
        };

        // Visit all objects names/aliases
        if (oObjects) {
          oObjects.forEach((entity) => {
            const sActualName = entity && entity.name;
            aNames[sActualName] = {
              actualName: sActualName,
              entity: entity,
            };
          });
        }
        // should we need suggest an alias?
        const duplicate = aNames[sName];
        if (duplicate) {
          setUniqueAlias();
        }
        return sAlias;
      },

      onTaskDragEnd(event) {
        this.getParentController().onSourceDragEnd();
      },
    });
  }
);

export const TCDiagramEditor = (sap.cdw.components as any).taskchainmodeler.control.TCDiagramEditor;
// Typescript API
export interface TCDiagramEditorControl extends BaseDiagramEditorControl {
  getDiagramBuilder(): sap.cdw.taskchainmodeler.DiagramImpl;
  attachOnCreateEntity(): void;
}
interface PrivateTCDiagramEditorControl extends TCDiagramEditorControl, PrivateBaseDiagramEditorControl {
  onCreateDataSource(data: any): void;
}
