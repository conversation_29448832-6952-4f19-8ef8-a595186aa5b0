<core:FragmentDefinition
  xmlns:core="sap.ui.core"
  xmlns="sap.m"
  xmlns:g="sap.galilei.ui5"
  xmlns:dnd="sap.ui.core.dnd"
  xmlns:f="sap.ui.layout.form"
  xmlns:l="sap.ui.layout"
  xmlns:html="http://www.w3.org/1999/xhtml"
>

  <Page
    id="diagram"
    enableScrolling="false"
    showHeader="true"
    class="GalileiPage tc-galile-page"
  >
    <headerContent class="tc-header-content">
      <OverflowToolbar width="100%">
        <!-- Layout drop down menu -->
        <Label
          text="{i18n>@lblLayout}: "
          visible="{= ${featureflags>/DWCO_TASKCHAIN_DIGRAM_ORIENTATION_MODE} === true}"
          class="tc-palette-lbl"
          labelFor="taskchain_orientation"
        >
          <layoutData>
            <OverflowToolbarLayoutData group="1" />
          </layoutData>
        </Label>
        <ComboBox
          id="taskchain_orientation"
          class="sapUiTinyMarginBegin"
          width="150px"
          selectedKey="{= ${galileiModel>/isVerticalView} }"
          enabled="{parts:[{path:'galileiModel>/resource/objects'}], formatter:'.orientationsBtnHandler'}"
          selectionChange="orientationsBtnTapped"
          visible="{= ${featureflags>/DWCO_TASKCHAIN_DIGRAM_ORIENTATION_MODE} === true}"
        >
          <core:ListItem
            key="true"
            text="{i18n>topToBtm}"
          />
          <core:ListItem
            key="false"
            text="{i18n>leftToRight}"
          />
          <layoutData>
            <OverflowToolbarLayoutData
              group="1"
              shrinkable="true"
            />
          </layoutData>
        </ComboBox>
        <ToolbarSeparator visible="{= ${featureflags>/DWCO_TASKCHAIN_DIGRAM_ORIENTATION_MODE} === true}" />
        <!-- Add placeholder dropdown  -->
        <MenuButton
          id="menuAddActions"
          visible="{workbenchEnv>/canUpdateModel}"
          icon="sap-icon://add"
          tooltip="{i18n>txtAdd}"
        >
          <menu>
            <Menu>
              <MenuItem
                text="{i18n>addplaceholderonselected}"
                icon="sap-icon://add"
                press="onAddPlaceholderOnSelected"
                enabled="{uiModel>/isTaskOperatorSelected}"
              />
              <MenuItem
                text="{i18n>addparallelbranchonselected}"
                icon="sap-icon://sac/network"
                press="onAddParallelBranchOnSelected"
                enabled="{uiModel>/isTaskOperatorSelected}"
              />
            </Menu>
          </menu>
        </MenuButton>
        <ToolbarSeparator />

        <Label
          text="{i18n>@lblOperators}:"
          class="tc-palette-lbl"
          visible="{= ${featureflags>/DWCO_TASK_FRAMEWORK_NOTIFICATION_TASK} === false}"
        >
        </Label>
        <!-- ALL and ANY button -->
        <!-- HBox used to have iteration for operator and have that many buttons -->
        <HBox
          visible="{workbenchEnv>/canUpdateModel}"
          id="tc-palette-container"
          class="tc-palette-container sapUiTinyMarginTop"
          height="2.5rem"
          design="Transparent"
          items="{uiModel>/operators}"
        >
          <Button
            id="action"
            type="Transparent"
            icon="{path:'uiModel>icon', formatter:'.paletteIconFormatter' }"
            tooltip="{uiModel>description}"
            class="operator"
            text="{uiModel>component}"
            press="onOperatorClick"
            enabled="true"
          >
            <customData>
              <core:CustomData
                xmlns:core="sap.ui.core"
                key="operator-type"
                value="create-table"
                text="{name}"
              />
            </customData>
            <dragDropConfig>
              <dnd:DragDropInfo
                dragStart="onDragStart"
                dragEnd="onDragEnd"
              />
            </dragDropConfig>
          </Button>
          <layoutData>
            <OverflowToolbarLayoutData group="2" />
          </layoutData>
        </HBox>

        <!-- RESTful API Task & Notification Task Button -->
        <ToolbarSeparator
          visible="{= ${featureflags>/DWCO_INFRA_TASKS_API_TASK} === true || ${featureflags>/DWCO_TASK_FRAMEWORK_NOTIFICATION_TASK} === true}"
        >
          <layoutData>
            <OverflowToolbarLayoutData
              priority="NeverOverflow"
              group="2"
            />
          </layoutData>
        </ToolbarSeparator>
        <Label
          text="{i18n>@lblTasks}:"
          class="tc-palette-lbl"
          labelFor="RESTfulTask"
          visible="{= ${featureflags>/DWCO_TASK_FRAMEWORK_NOTIFICATION_TASK} === true ? false : ${featureflags>/DWCO_INFRA_TASKS_API_TASK} === true ? true : false}"
        />
        <!-- API Task button -->
        <Button
          id="RESTfulTask"
          type="Transparent"
          icon="sap-icon://sac/open-connectors"
          tooltip="{i18n>@lblRESTfullTask}"
          class="operator"
          press="onApiClick"
          enabled="true"
          content="{galileiModel}"
          visible="{= ${featureflags>/DWCO_INFRA_TASKS_API_TASK} === true}"
        >
          <customData>
            <core:CustomData
              xmlns:core="sap.ui.core"
              key="apiTask"
              value="create-table"
            />
          </customData>
          <dragDropConfig>
            <dnd:DragDropInfo
              dragStart="onApiDragStart"
              dragEnd="onTaskDragEnd"
            />
          </dragDropConfig>
        </Button>
        <!-- Notification Task-->
        <Button
          id="notificationTask"
          type="Transparent"
          icon="sap-icon://ui-notifications"
          tooltip="{i18n>@lblNotificationTask}"
          class="operator"
          press="onNotificationTaskClick"
          enabled="true"
          content="{galileiModel}"
          visible="{= ${featureflags>/DWCO_TASK_FRAMEWORK_NOTIFICATION_TASK} === true}"
        >
          <customData>
            <core:CustomData
              xmlns:core="sap.ui.core"
              key="notificationTask"
              value="create-table"
            />
          </customData>
          <dragDropConfig>
            <dnd:DragDropInfo
              dragStart="onNotificationTaskDragStart"
              dragEnd="onTaskDragEnd"
            />
          </dragDropConfig>
        </Button>
        <ToolbarSeparator />
        <ToolbarSpacer />
        <!-- Delete, auto-layout and zoom button -->
        <ToolbarSpacer />
        <Button
          id="tcDeleteNodeButton"
          icon="sap-icon://delete"
          press="onDelete"
          visible="{workbenchEnv>/canUpdateModel}"
          tooltip="{i18n>@deleteNode}"
        >
        </Button>
        <ToolbarSeparator visible="{workbenchEnv>/canUpdateModel}" />
        <Button
          id="tcAutoLayout"
          icon="sap-icon://overview-chart"
          press="onAutoLayout"
          visible="{workbenchEnv>/canUpdateModel}"
          tooltip="{i18n>@autoLayout}"
        >
        </Button>
        <Button
          id="tcZoomToFitButton"
          icon="sap-icon://sys-monitor"
          press="onZoomToFit"
          visible="true"
          tooltip="{i18n>@zoomToFit}"
        >
        </Button>

      </OverflowToolbar>
    </headerContent>
    <!-- Editor section -->
    <content>
      <g:Editor
        id="editorControl"
        viewBorderWidth="0"
        class="ERModeler"
        width="100%"
        height="100%"
        enableInPlaceEditing="false"
        enableInPlaceEditingAfterCreate="false"
      />
      <!-- <Panel id="tcWelcomeTextPanel" class="WelcomePanel" visible="{=${$this>welcomeVisible} === true}">
      <FlexBox
        width="100%"
        height="100%"
        direction="Column"
        alignItems="Center"
        justifyContent="Center">
        <items>
          <Image src="./images/TC_Modeler_EmptyState.svg" width="90%"></Image>
          <Title class="sapUiSmallMarginTopBottom" text="{i18n>@txtNoData}" wrapping="true" titleStyle="H2"></Title>
          <Label text="{i18n>@welcomeText}" wrapping="true"></Label>
        </items>
      </FlexBox>
    </Panel> -->
    </content>
  </Page>

</core:FragmentDefinition>
