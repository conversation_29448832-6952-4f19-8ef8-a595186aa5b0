#XTOL Undo
@undo=Ongedaan maken
#XTOL Redo
@redo=Herhalen
#XTOL Delete Selected Symbol
@deleteNode=Geselecteerd symbool verwijderen
#XTOL Zoom to Fit
@zoomToFit=Passend maken
#XTOL Auto Layout
@autoLayout=Automatische lay-out
#XMSG
@welcomeText=Sleep objecten uit het linkervenster en zet ze neer op dit canvas.
#XMSG
@txtNoData=U lijkt nog geen object te hebben toegevoegd.
#XMSG
VAL_ENTER_VALID_STRING_GEN=Voer een geldige tekenreeks in met een lengte korter dan of gelijk aan {0}.
#XMSG
@noParametersMsg=Deze procedure heeft geen invoerparameters.
#XMSG
@ip_enterValueMsg="{0}" (SQL-uitvoeringsscriptprocedure) heeft invoerparameters. U kunt een waarde instellen voor elke parameter.
#XTOL
@validateModel=Validatiemeldingen
#XTOL
@hierarchy=Hiërarchie
#XTOL
@columnCount=Aantal kolommen
#XFLD
@yes=Ja
#XFLD
@no=Nee
#XTIT Save Dialog param
@modelNameTaskChain=Taakketen
#properties panel
@lblPropertyTitle=Eigenschappen
#XFLD
@lblGeneral=Algemeen
#XFLD : Setting
@lblSetting=Instellingen
#XTXT: Delta Enabled Local Table Deletion
txtDeltaTableDeletion=Alle volledig verwerkte records verwijderen met wijzigingstype 'Verwijderd' die ouder zijn dan
#XTXT: Delta Enabled Local Table Deletion Input field description
desDeltaTableDeletion=Dagen
#XFLD: Data Activation label
@lblDataActivation=Gegevensactivering
#XFLD: Latency label
@latency=Wachttijd
#XTEXT: Text for Latency dropdown
txtLatencyDefault=Standaard
#XTEXT: Text 1 hour
txtOneHour=1 uur
#XTEXT: Text 2 hours
txtTwoHours=2 uur
#XTEXT: Text 3 hours
txtThreeHours=3 uur
#XTEXT: Text 4 hours
txtFourHours=4 uur
#XTEXT: Text 6 hours
txtSixHours=6 uur
#XTEXT: Text 12 hours
txtTwelveHours=12 uur
#XTEXT: Text 1 day
txtOneDay=1 dag
#XFLD: Latency label
@autoRestartHead=Automatische herstart
#XFLD
@lblConnectionName=Verbinding
#XFLD
@lblQualifiedName=Gekwalificeerde naam
#XFLD
@lblSpaceName=Naam ruimte
#XFLD
@lblLocalSchemaName=Lokaal schema
#XFLD
@lblType=Objecttype
#XFLD
@lblActivity=Activiteit
#XFLD
@lblTableName=Tabelnaam
#XFLD
@lblBusinessName=Objectnaam
#XFLD
@lblTechnicalName=Technische naam
#XFLD
@lblSpace=Ruimte
#XFLD
@lblLabel=Label
#XFLD
@lblDataType=Gegevenstype
#XFLD
@lblDescription=Omschrijving
#XFLD
@lblStorageType=Opslag
#XFLD
@lblHTTPConnection=Generieke HTTP-verbinding
#XFLD
@lblAPISettings=Generieke API-instellingen
#XFLD
@header=Koppen
#XFLD
@lblInvoke=API-aanroep
#XFLD
@lblMethod=Methode
#XFLD
@lblUrl=Basis-URL
#XFLD
@lblAPIPath=API-pad
#XFLD
@lblMode=Modus
#XFLD
@lblCSRFToken=CSRF-token vereisen
#XFLD
@lblTokenURL=CSRF-token-URL
#XFLD
@csrfTokenInfoText=Indien leeg, zullen basis-URL en API-pad worden gebruikt
#XFLD
@lblCSRF=CSRF-token
#XFLD
@lblRequestBody=Aanvraagtekst
#XFLD
@lblFormat=Formatteren
#XFLD
@lblResponse=Respons
#XFLD
@lblId=ID om status op te halen
#XFLD
@Id=ID
#XFLD
@lblSuccessIndicator=Succesteken
#XFLD
@lblErrorIndicator=Foutteken
#XFLD
@lblErrorReason=Reden van fout
#XFLD
@lblStatus=Status
#XFLD
@lblApiTestRun=API-testrun
#XFLD
@lblRunStatus=Runstatus
#XFLD
@lblLastRan=Laatste uitgevoerd op
#XFLD
@lblTestRun=Testrun
#XFLD
@lblDefaultHeader=Standaardkopvelden (sleutel-waardeparen)
#XFLD
@lblAdditionalHeader=Aanvullend kopveld
#XFLD
@lblKey=Sleutel
#XFLD
@lblEditJSON=JSON bewerken
#XFLD
@lblTasks=Taken
#XFLD
@lblRESTfullTask=API
#XFLD: add field button text
btnAddField=Kopveld toevoegen
#XFLD: view Details link
@viewDetails=Details weergeven
#XTOL
tooltipTxt=Meer
#XTOL
delete=Verwijderen
#XBTN: ok button text
btnOk=OK
#XBTN: cancel button text
btnCancel=Annuleren
#XBTN: save button text
btnSave=Opslaan
#XMSG Error: Object has duplicated technical name.
VAL_OBJECT_DUPLICATE_NAME=Object "{0}" bestaat al in de repository. Voer een andere naam in.
#XMSG: loading message while opening task chain
loadTaskChain=Bezig met laden van taakketen...
#model properties
#XFLD
@status_panel=Uitvoeringsstatus
#XFLD
@deploy_status_panel=Status implementeren
#XFLD
@status_lbl=Status
#XFLD
@lblLastExecuted=Laatste uitvoering
#XFLD
@lblNotExecuted=Nog niet uitgevoerd
#XFLD
@lblNotDeployed=Niet geïmplementeerd
#XFLD
errorDetailsTxt=Runstatus is niet opgehaald
#XBTN: Schedule dropdown menu
SCHEDULE=Planning
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Planning bewerken
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Planning verwijderen
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Planning maken
#XLNK
viewDetails=Details weergeven
#XMSG: error message for reading execution status from backend
backendErrorMsg=De gegevens worden op dit moment niet geladen van de server. Probeer de gegevens opnieuw op te halen.
#XFLD: Status text for Completed
@statusCompleted=Voltooid
#XFLD: Status text for Running
@statusRunning=Wordt uitgevoerd
#XFLD: Status text for Failed
@statusFailed=Mislukt
#XFLD: Status text for Stopped
@statusStopped=Gestopt
#XFLD: Status text for Stopping
@statusStopping=Wordt gestopt
#XFLD
@LoaderTitle=Bezig met laden
#XFLD
#XFLD: Deploy status, dataflow has  been deployed / deployment date exists
@deployStatusActive=Geïmplementeerd
@deployStatusRevised=Lokale updates
@deployStatusFailed=Mislukt
@deployStatusPending=Bezig met implementeren...
@LoaderText=De details worden opgehaald van de server.
#XMSG
@msgDetailFetchError=Fout tijdens het ophalen van de details van de server.
#XFLD
@executeError=Fout
#XFLD
@executeWarning=Waarschuwing
#XMSG
@executeConfirmDialog=Info
#XMSG
@executeunsavederror=Sla uw taakketen op voordat u deze uitvoert.
#XMSG
@executemodifiederror=Er zijn niet-opgeslagen wijzigingen in de taakketen. Sla deze op.
#XMSG
@executerunningerror=De taakketen wordt momenteel uitgevoerd. Wacht totdat de huidige uitvoering is voltooid voordat u begint met een nieuwe.
#XMSG
@btnExecuteAnyway=Toch uitvoeren
#XMSG
@msgExecuteWithValidations=De taakketen heeft validatiefouten. Het uitvoeren van de taakketen kan tot fouten leiden.
#XMSG
@msgRunDeployedVersion=Er zijn wijzigingen die moeten worden geïmplementeerd. De laatst geïmplementeerde versie van de taakketen wordt uitgevoerd. Wilt u doorgaan?
#XMSG
#XMSG
@navToMonitoring=Openen in Monitor taakketens
#XMSG
txtOR=OF
#XFLD
@preview=Preview
#XMSG
txtand=EN
#NOTR
@dateFormat=yyyy-MM-dd
#NOTR
@dateTimeFormatFilter=yyyy-MM-dd HH:mm:ss.
#NOTR
@timeFormat=HH:mm:ss
@lblColum=Kolom
#XFLD
@lblCondition=Conditie
#XFLD
@lblValue=Waarde
#XMSG
@msgJsonInvalid=De taakketen is niet opgeslagen omdat er fouten in de JSON zijn. Bekijk de fouten en los ze op.
#XTIT
@msgSaveFailTitle=Ongeldige JSON.
#XMSG
NOT_CHAINABLE=Object "{0}" kan niet worden toegevoegd aan taakketen.
#XMSG
NOT_CHAINABLE_REMOTETABLE=Object ''{0}'': replicatietype wordt gewijzigd.
#XMSG
searchTaskChain=Objecten zoeken

#XFLD
@txtTaskChain=Taakketen
#XFLD
@txtRemoteTable=Remote tabel
#XFLD
@txtRemoveData=Gerepliceerde gegevens verwijderen
#XFLD
@txtRemovePersist=Persistente gegevens verwijderen
#XFLD
@txtView=Weergave
#XFLD
@txtDataFlow=Gegevensstroom
#XFLD
@txtIL=Intelligente zoekactie
#XFLD
@txtTransformationFlow=Transformatiestroom
#XFLD
@txtReplicationFlow=Replicatiestroom
#XFLD
@txtDeltaLocalTable=Lokale tabel
#XFLD
@txtBWProcessChain=BW-procesketen
#XFLD
@txtSQLScriptProcedure=SQL-scriptprocedure
#XFLD
@txtAPI=API
#XFLD
@txtMerge=Samenvoegen
#XFLD
@txtOptimize=Optimaliseren
#XFLD
@txtVacuum=Records verwijderen

#XFLD
@txtRun=Uitvoeren
#XFLD
@txtPersist=Persistent maken
#XFLD
@txtReplicate=Repliceren
#XFLD
@txtDelete=Records met wijzigingstype 'Verwijderd' verwijderen
#XFLD
@txtRunTC=Voer taakketen uit
#XFLD
@txtRunBW=BW-procesketen uitvoeren
#XFLD
@txtRunSQLScriptProcedure=SQL-scriptprocedure uitvoeren

#XFLD
@txtRunDataFlow=Gegevensstroom uitvoeren
#XFLD
@txtPersistView=View persistent maken
#XFLD
@txtReplicateTable=Tabel repliceren
#XFLD
@txtRunIL=Intelligente zoekactie uitvoeren
#XFLD
@txtRunTF=Transformatiestroom uitvoeren
#XFLD
@txtRunRF=Replicatiestroom uitvoeren
#XFLD
@txtRemoveReplicatedData=Gerepliceerde gegevens verwijderen
#XFLD
@txtRemovePersistedData=Persistente gegevens verwijderen
#XFLD
@txtMergeData=Samenvoegen
#XFLD
@txtOptimizeData=Optimaliseren
#XFLD
@txtVacuumData=Records verwijderen
#XFLD
@txtRunAPI=API uitvoeren

#XFLD storage type text
hdlfStorage=Bestand

@statusNew=Niet geïmplementeerd
@statusActive=Geïmplementeerd
@statusRevised=Lokale updates
@statusPending=Bezig met implementeren...
@statusChangesToDeploy=Te implementeren wijzigingen
@statusDesignTimeError=Ontwerptijdfout
@statusRunTimeError=Runtimefout

#XTIT
txtNodes=Objecten in taakketen ({0})
#XBTN
@deleteNodes=Verwijderen

#XMSG
@txtDropDataToDiagram=Sleep objecten en zet ze neer in diagram.
#XMSG
@noData=Geen objecten

#XFLD
@txtTaskPosition=Taakpositie

#input parameters and variables
#XFLD
lblInputParameters=Invoerparameters
#XFLD
ip_name=Naam
#XFLD
ip_value=Waarde
#XTEXT
@noObjectsFound=Geen objecten gevonden

#XMSG
@msgExecuteSuccess=Uitvoering van taakketen is gestart.
#XMSG
@msgExecuteFail=Uitvoering van taakketen is mislukt.
#XMSG
@msgDeployAndRunSuccess=Implementatie en uitvoering van taakketen zijn gestart.
#XMSG
@msgDeployAndRunFail=Implementatie en uitvoering van taakketen zijn mislukt.
#XMSG
@titleExecuteBusy=Even geduld a.u.b.
#XMSG
@msgExecuteBusy=We zijn bezig met voorbereiden van uw gegevens om de taakketen uit te voeren.
#XMSG
@msgAPITestRunSuccess=API-testrun is gestart
#XMSG
@msgAPIExecuteBusy=We zijn bezig met voorbereiden van uw gegevens om de API-testrun uit te voeren.

#XTOL
txtOpenInEditor=Openen in editor
#XTOL
txtPreviewData=Preview van gegevens weergeven

#datapreview
#XMSG
@msgDataPreviewNotSupp=Gegevenspreview is niet beschikbaar voor dit object.

#XMSG Error: empty model
VAL_MODEL_EMPTY=Uw model lijkt leeg. Voeg enkele objecten toe.
#XMSG Error: deploy model
@msgDeployBeforeRun=U moet de taakketen implementeren voordat u deze kunt uitvoeren.
#BTN: close dialog
btnClose=Sluiten

#XMSG Object is not Deployed
VAL_NOT_DEPLOYED=Object "{0}" moet worden geïmplementeerd om door te gaan met de taakketen.
#XMSG Object is having runtime error
VAL_RUNTIME_ERROR=Object ''{0}'' heeft runtimefout. Controleer object.
#XMSG Object is having design time error
VAL_DESIGNTIME_ERROR=Object ''{0}'' heeft ontwerptijdfout. Controleer object.
#XMSG Procedure Object has been deleted
VAL_DELETED_PROCEDURE=De volgende procedures zijn verwijderd: {0}
#XMSG New Parameter added to Procedure object
VAL_NEW_PARAMETER_ADDED=Nieuwe parameters toegevoegd aan procedure "{1}": "{0}". Implementeer taakketen opnieuw.
#XMSG Parameter removed from Procedure object
VAL_PARAMETER_DELETED=Parameters verwijderd uit procedure "{1}": "{0}". Implementeer taakketen opnieuw.
#XMSG Parameter data type changed in Procedure object
VAL_PARAMETER_DATATYPE_MODIFIED=Parameter gegevenstype "{0}" gewijzigd van "{1}" in "{2}" in procedure "{3}".
#XMSG Parameter length changed of specific datatype in Procedure object
VAL_PARAMETER_LENGTH_MODIFIED=Parameter lengte "{0}" gewijzigd van "{1}" in "{2}" in procedure "{3}".
#XMSG Parameter precision changed of Decimal datatype in Procedure object
VAL_PARAMETER_PRECISION_MODIFIED=Parameter precisie "{0}" gewijzigd van "{1}" in "{2}" in procedure "{3}".
#XMSG Parameter scale changed of Decimal datatype in Procedure object
VAL_PARAMETER_SCALE_MODIFIED=Parameter staffel "{0}" gewijzigd van "{1}" in "{2}" in procedure "{3}".
#XMSG Parameter value not provided in Procedure object
VAL_SQL_SCRIPT_PROCEDURE_PARAMETER_VALUE_MISSING=U hebt geen waarde ingevoerd voor parameters die vereist zijn voor procedure "{0}": "{1}"
#XMSG Remote Table activity changes to Remove Replicated Data
VAL_REMOVE_REPLICATED_DATA=De volgende run van een taakketen wijzigt het gegevenstoegangstype van de remote tabel en gegevens zullen niet meer in realtime worden geüpload.
#XMSG Remote Table activity changes
REPLICATION_TYPE_CHANGED=Object ''{0}'': replicatietype wordt gewijzigd.

#XFLD
@lblStartNode=Startknooppunt
#XFLD
@lblEndNode=Eindknooppunt
#XFLD
@linkTo={0} naar {1}
#XTOL
@txtViewDetails=Details weergeven

#XTOL
txtOpenImpactLineage=Impact- en herkomstanalyse
#XFLD
@emailNotifications=E-mailmeldingen
#XFLD
@txtReset=Resetten
#XFLD
@emailMsgWarning=Standaard sjabloon-e-mail wordt verzonden als e-mailbericht leeg is
#XFLD
@notificationSettings=Meldingsinstellingen
#XFLD
@recipientEmailAddr=E-mailadres ontvanger
#XFLD
@emailSubject=E-mailonderwerp
@emailSubjectText=Taakketen <TASKCHAIN_NAME> voltooid met status <STATUS>
#XFLD
@emailMessage=E-mailbericht
@emailMessageText=Geachte gebruiker,\n\n Bij dezen laten we u weten dat taakketen:<TASKCHAIN_NAME> uitgevoerd om <START_TIME> is voltooid met status <STATUS>. De uitvoering is beëindigd om <END_TIME>.\n\nDetails:\nSpace:<SPACE_NAME>\nError:<ERROR_MESSAGE>
@selectEmailRecipientDialogTitle=E-mailadres ontvanger selecteren
@tenantMembers=Tenantleden
@others=Overige ({0})
@selectedEmailAddress=Geselecteerde ontvangers
@add=Toevoegen
@placeholder=Plaatsaanduiding
@description=Omschrijving
@copyText=Tekst kopiëren
@taskchainDetailsPlaceholder=Plaatsaanduidingen voor taakketendetails
@placeholderCopied=Plaatsaanduiding is gekopieerd
@invalidEmailInfo=Voer correct e-mailadres in
@maxMembersAlreadyAdded=U hebt al het maximum van 20 leden ingevoerd
@enterEmailAddress=Voer e-mailadres gebruiker in
@inCorrectPlaceHolder={0} is geen verwachte plaatsaanduiding in e-mailtekst.
@nsOFF=Geen meldingen verzenden
@nsFAILED=Verstuur e-mailmelding alleen als run is voltooid met fout
@nsCOMPLETED=Verstuur e-mailmelding alleen als run met succes is voltooid
@nsANY=Verstuur e-mail als run is voltooid
@phStatus=Status van taakketen - Geslaagd|Mislukt
@phTaskChainTName=Technische naam van taakketen
@phTaskChainBName=Bedrijfsnaam van taakketen
@phLogId=Logische-ID van run
@phUser=Gebruiker die taakketen uitvoert
@phLogUILink=Link naar verslagweergave van taakketen
@phStartTime=Starttijd van run
@phEndTime=Eindtijd van run
@phErrMsg=Eerste foutmelding in taaklog. Log is leeg in geval van SUCCESS
@phSpaceName=Technische naam van ruimte
@emailFormatError=Ongeldige e-mailindeling
@emailFormatErrorInListText=Ongeldige e-mailindeling ingevoerd in niet-tenantledenlijst
@emailSubjectTemplateText=Melding voor taakketen:$$taskChainName$$ - Ruimte: $$spaceId$$ - Status: $$status$$
@emailMessageTemplateText=Hallo,\n\n Uw taakketen met label $$taskChainName$$ is gereed met status $$status$$. \n Hier zijn enkele andere details over de taakketen:\n - Technische naam taakketen: $$taskChainName$$ \n - Log-ID van taakketenrun: $$logId$$ \n - Gebruiker die taakketen heeft uitgevoerd: $$user$$ \n - Link naar verslagweergave van taakketen: $$uiLink$$ \n - Starttijd van taakketenrun: $$startTime$$ \n - Eindtijd van taakketenrun: $$endTime$$ \n - Naam van ruimte: $$spaceId$$ \n
@deleteEmailRecepient=Ontvanger verwijderen
@emailInputDisabledText=Implementeer de taakketen om e-mailontvangers toe te voegen.
@tenantOwnerDomainMatchErrorText=Het e-mailadresdomein komt niet overeen met het tenanteigenaardomein: {0}
@totalEmailIdLimitInfoText=U kunt maximaal 20 e-mailontvangers selecteren inclusief tenantlidgebruikers en andere ontvangers.
@emailDomainInfoText=Alleen e-mailadressen met domein: {0} worden geaccepteerd.
@duplicateEmailErrorText=Er staan dubbele e-mailontvangers in de lijst.

#XFLD Zorder Title
@txtZorderTitle=Apache Spark Z-orderkolommen

#XFLD Zorder NoColumn
@txtZorderNoColumn=Geen Z-orderkolommen gevonden

#XFLD Zorder Column PrimaryKey
@txtZorderColumnPrimaryKey=Primaire sleutel

#XFLD
@lblOperators=Operators
addNewSelector=Toevoegen als nieuwe taak
parallelSelector=Toevoegen als parallelle taak
replaceSelector=Bestaande taak vervangen
addparallelbranch=Toevoegen als parallelle tak
addplaceholder=Plaatsaanduiding toevoegen
addALLOperation=ALL-operator
addOROperation=ANY-operator
addplaceholdertocanvas=Plaatsaanduiding toevoegen aan canvas
addplaceholderonselected=Plaatsaanduiding toevoegen na geselecteerde taak
#XFLD: Add Parallel Branch to Selected Task
addparallelbranchonselected=Parallelle tak toevoegen na geselecteerde taak
addOperator=Operator toevoegen
txtAdd=Toevoegen
txtPlaceHolderText=Sleep hier een taak naartoe
@lblLayout=Lay-out

#XMSG
VAL_UNCONNECTED_TASK=Taak ''{0}'' is niet verbonden met taakketen.
#XMSG
VAL_TASK_SHOULD_HAVE_ONE_INCOMING_LINK=Taak ''{0}'' mag maar één inkomende verbinding hebben.
#XMSG
VAL_TASK_SHOULD_HAVE_INCOMING_LINK=Taak ''{0}'' moet één inkomende verbinding hebben.
#XMSG
VAL_UNCONNECTED_OPERATOR=Operator ''{0}'' is niet verbonden met taakketen.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ATLEAST_TWO_INCOMING_LINK=Operator ''{0}'' moet ten minste twee inkomende verbindingen hebben.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ONE_OUT_LINK=Operator ''{0}'' moet ten minste één uitgaande verbinding hebben.
#XMSG
VAL_CIRCULAR_REFERENCE_EXITS=Er bestaat een cirkelvormige lus in taakketen ''{0}''.
#XMSG
VAL_UNCONNECTED_BRANCH=Object/tak ''{0}'' is niet verbonden met taakketen.
#XMSG
VAL_TASK_TRIGGERED_PARALLEL_MORE_THAN_ONE_TIME=Taak ''{0}'' is meer dan één keer parallel verbonden. Verwijder duplicaten om door te gaan.


txtBegin=Start
txtNodesInLink=Betrokken objecten
#XTOL Tooltip for a context button on diagram
openInNewTab=Openen op nieuw tabblad
#XTOL Tooltip for a context button on diagram
txtConnectToTaskOperator=Slepen om verbinding te maken
@emailUpdateError=Fout bij bijwerken e-mailmeldingenlijst

#XMSG
noTeamPrivilegeTxt=U hebt geen toestemming om een lijst met tenantleden te bekijken. Gebruik het tabblad Overige om e-mailontvangers handmatig toe te voegen.

#XFLD Package
@txtPackage=Pakket

#XMSG warning for repository package assignment changed. {1} - package name
VAL_PACKAGE_CHANGED=U hebt dit object aan pakket "{1}" toegewezen. Klik op "Opslaan" om deze wijziging te bevestigen en te valideren. Let op: een toewijzing aan een pakket kan in deze editor niet meer ongedaan worden gemaakt nadat u hebt opgeslagen.
#XMSG warning for repository package dependency issue.
MISSING_DEPENDENCY=Afhankelijkheden van object ''{0}" kunnen niet worden opgelost in de context van pakket ''{1}''.

#Warning messages for the folders in the "Others" tab of task chain editor
#XMSG generic warning if getting the objects in a particular folder fails with unhandled errors
getNonRepoArtefactTypeDescendantsError=Er was een probleem bij het ophalen van objecten in de map die u hebt geselecteerd.
#BW Process Chains folder related warning messages
#XMSG warning if user does not have the necessary permission related to the SAP BW Bridge space
missingPrivilegeForBridgeSpaceError=U hebt niet de vereiste bevoegdheid om BW-procesketenen weer te geven of op te nemen in een taakketen.
#XMSG warning if there was a problem retrieving the BW process chains due to bridge space membership, bridge connection issues or other errors
generalBridgeSpaceError=Er was een probleem bij het ophalen BW-procesketenen.
#XMSG warning if the BW API for retrieving the BW process chains from the SAP BW Bridge tenant fails
bwApiClientError=Er was een probleem bij het ophalen van de BW-procesketenen uit de SAP BW Bridge-tenant.
#XMSG warning if BW process chains list is empty
bwApiEmptyListError=Geen BW-procesketenen gevonden in de SAP BW Bridge-tenant.
#XMSG: Task log message for invalid OIDC configuration in the SAP BW Bridge tenant
bwBridgeOIDCConfigStatusInvalid=OpenID-vertificatie is niet geconfigureerd voor deze tenant. Gebruik de clientID "{0}" en de tokenURL "{1}" om OpenID-verificatie in de SAP BW Bridge-tenant te configureren zoals omschreven in SAP Note 3536298.
#XMSG BW Process Chain Object has been deleted
VAL_DELETED_BW=De volgende BW-procesketenen zijn mogelijk verwijderd uit de SAP Bridge-tenant: {0}
#BW SQL Script Procedures folder related warning messages
#XFLD waring if the Procedure list is empty
procedureAPIEmptyList=Er zijn geen procedures gecreëerd of de machtiging EXECUTE is niet verleend aan Open SQL-schema
#Change digram orientations
changeOrientations=Oriëntaties wijzigen


# placeholder for the API Path
apiPath=Voorbeeld: /job/v1
# placeholder for the status API Path
statusAPIPath=Voorbeeld: /job/v1/{id}/status
# valueStateText for the API Path
apiPathRequired=API-pad is vereist
#placeholder for the CSRF Token URL
csrfTokenURL=Alleen HTTPS wordt ondersteund
# Response Type 1
statusCode=Resultaat van HTTP-statuscode ophalen
# Response Type 2
locationHeader=Resultaat van HTTP-statuscode en locatiekop ophalen
# Response Type 3
responseBody=Resultaat van HTTP-statuscode en responstekst ophalen
# placeholder for ID
idPlaceholder=JSON-pad invoeren
# placeholder for indicator value
indicatorValue=Waarde invoeren
# Placeholder for key
keyPlaceholder=Code invoeren
# Error message for missing key
KeyRequired=Code is verplicht
# Error message for invalid key format
invalidKeyFormat=De kopcode die u hebt ingevoerd, is niet toegestaan. Geldige koppen zijn:<ul><li>"voorkeur geven aan"</li><li>koppen die beginnen met "x-", behalve "x-forwarded-host"</li><li>koppen die alfanumerieke tekens, "-", of "_" bevatten</li></ul>
# Error message for missing value
valueRequired=Waarde is vereist
# Error message for invalid characters in value
invalidValueCharacters=De kop bevat ongeldige tekens. Speciale tekens die toegestaan zijn, zijn:\t ";", ":", "-", "_", ",", "?", "/" en "*"
# Validation message for invoke api path
apiPathValidation=Voer een geldig API-pad in, bijv.: /job/v1
# Validation message for JSON path
jsonPathValidation=Voer een geldig JSON-pad in
# Validation message for success/error indicator
indicatorValueValidation=Tekenwaarde moet met een alfanumeriek teken starten en kan de volgende speciale tekens bevatten:\t "-" en "_" 
# Error message for JSON path
jsonPathRequired=JSON-pad is vereist
# Error message for invalid API Technical Name
invalidTechnicalName=De technische naam bevat ongeldige tekens
# Error message for empty Technical Name
emptyTechnicalName=Technische naam is vereist
# Tooltip for codeEditor dialog
codeEditorTooltip=JSON-bewerkingsvenster openen
# Status Api path validation message
validationStatusAPIPath=Voer een geldig API-pad in, bijv.: /job/v1
# CSRF token URL validation message
validationCSRFTokenURL=De CSRF-token-URL moet beginnen met 'https://' en een geldige URL zijn
# Select a connection
selectConnection=Selecteer een verbinding
# Validation message for connection item error
connectionNotReplicated=Verbinding momenteel ongeldig voor uitvoer van API-taken. Open de app "Verbindingen" en voer uw aanmeldgegevens opnieuw in om de HTTP-verbinding te repareren.
# Validation message for API task properties wrong value or missing
VAL_REST_API_PROPERTIES_MISSING=De API-taak "{0}" heeft onjuiste of ontbrekenmde waarden voor de volgende eigenschappen: {1}
# Validation message for API connection api is throwing some error or selected connection has some warning
VAL_REST_API_CONNECTION=De API-taak "{0}" heeft een probleem met de HTTP-verbinding. Open de app "Verbindingen" en controleer de verbinding
# Validation message for API task properties when delected connection
VAL_REST_API_CONNECTION_DELETED=De API-taak "{0}" heeft een verwijderden verbinding "{1}"
# Validation message for API task properties when connection cannot be consumed in the API task
VAL_REST_API_CONNECTION_CONSUMPTION_WARNING=API-taak "{0}" heeft een verbinding "{1}" die niet kan worden gebruikt om API-taken uit te voeren. Open de app "Verbindingen" en voer uw gegevens opnieuw in om verbinding voor API-taken tot stand te brengen.
# Information message if synchrnous mode is selected in the API Invocation then we don't need status information
modeInfoText=In de synchrone modus wordt het statuspaneel niet weergegeven voor API-aanroepen
# validation dialog button for Run API Test
saveAnyway=Toch opslaan
# validation message for technical name
technicalNameValidation=De technische naam moet uniek zijn binnen de taakketen. Kies een andere technische naam
# Connection error message
getHttpConnectionsError=Kon HTTP-verbindingen niet ophalen
# Msg for the API test run when the Task chain is not saved
taskChainNotSaved=De taakketen moet zijn opgeslagen voordat de API-testrun kan worden uitgevoerd.
# Msg failed to run API test run
@failedToRunAPI=Kon API-testrun niet uitvoeren
# Msg for the API test run when its already in running state
apiTaskRunning=Er wordt al een API-testrun uitgevoerd. Nieuwe testrun starten?

topToBtm=Van boven naar beneden
leftToRight=Van links naar rechts

#XTIT Apache Spark Application Settings
txtHeaderApacheSettings=Instellingen Apache Spark-applicatie
#XFLD Use Default
txtUseSpaceDefault=Standaard gebruiken
#XFLD Application
txtApplication=Applicatie
#XFLD Define new settings for this Task
txtNewSettings=Nieuwe instellingen voor deze taak definiëren

#XFLD Use Default
txtUseDefault=Standaard gebruiken




