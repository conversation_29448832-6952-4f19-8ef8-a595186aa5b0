#XTOL Undo
@undo=Deshacer
#XTOL Redo
@redo=Rehacer
#XTOL Delete Selected Symbol
@deleteNode=Eliminar símbolo seleccionado
#XTOL Zoom to Fit
@zoomToFit=Ampliar para ajustar
#XTOL Auto Layout
@autoLayout=Diseño automático
#XMSG
@welcomeText=Arrastre y suelte objetos desde el panel izquierdo hasta este lienzo.
#XMSG
@txtNoData=Parece que aún no agregó objetos.
#XMSG
VAL_ENTER_VALID_STRING_GEN=Ingrese una string válida de una longitud inferior o igual a {0}.
#XMSG
@noParametersMsg=Este procedimiento no tiene parámetros de entrada.
#XMSG
@ip_enterValueMsg=El "{0}" (ejecutar procedimiento de script SQL) tiene parámetros de entrada. Puede configurar un valor para cada uno de ellos.
#XTOL
@validateModel=Mensajes de validación
#XTOL
@hierarchy=Jerarquía
#XTOL
@columnCount=Número de columnas
#XFLD
@yes=Sí
#XFLD
@no=No
#XTIT Save Dialog param
@modelNameTaskChain=Cadena de tareas
#properties panel
@lblPropertyTitle=Propiedades
#XFLD
@lblGeneral=General
#XFLD : Setting
@lblSetting=Opciones
#XTXT: Delta Enabled Local Table Deletion
txtDeltaTableDeletion=Eliminar todos los registros completamente procesados con el tipo de cambio "Eliminado" que tengan más de
#XTXT: Delta Enabled Local Table Deletion Input field description
desDeltaTableDeletion=Días
#XFLD: Data Activation label
@lblDataActivation=Activación de datos
#XFLD: Latency label
@latency=Latencia
#XTEXT: Text for Latency dropdown
txtLatencyDefault=Predeterminada
#XTEXT: Text 1 hour
txtOneHour=1 hora
#XTEXT: Text 2 hours
txtTwoHours=2 horas
#XTEXT: Text 3 hours
txtThreeHours=3 horas
#XTEXT: Text 4 hours
txtFourHours=4 horas
#XTEXT: Text 6 hours
txtSixHours=6 horas
#XTEXT: Text 12 hours
txtTwelveHours=12 horas
#XTEXT: Text 1 day
txtOneDay=1 día
#XFLD: Latency label
@autoRestartHead=Reinicio automático
#XFLD
@lblConnectionName=Conexión
#XFLD
@lblQualifiedName=Nombre calificado
#XFLD
@lblSpaceName=Nombre de espacio
#XFLD
@lblLocalSchemaName=Esquema local
#XFLD
@lblType=Tipo de objeto
#XFLD
@lblActivity=Actividad
#XFLD
@lblTableName=Nombre de tabla
#XFLD
@lblBusinessName=Nombre empresarial
#XFLD
@lblTechnicalName=Nombre técnico
#XFLD
@lblSpace=Espacio
#XFLD
@lblLabel=Etiqueta
#XFLD
@lblDataType=Tipo de datos
#XFLD
@lblDescription=Descripción
#XFLD
@lblStorageType=Almacenamiento
#XFLD
@lblHTTPConnection=Conexión HTTP genérica
#XFLD
@lblAPISettings=Opciones de API genéricas
#XFLD
@header=Encabezados
#XFLD
@lblInvoke=Invocación de API
#XFLD
@lblMethod=Método
#XFLD
@lblUrl=URL de base
#XFLD
@lblAPIPath=Ruta de API
#XFLD
@lblMode=Modo
#XFLD
@lblCSRFToken=Requerir token CSRF
#XFLD
@lblTokenURL=URL de token de CSRF
#XFLD
@csrfTokenInfoText=Si no se ingresa, se usarán la URL de base y la ruta de API
#XFLD
@lblCSRF=Token CSRF
#XFLD
@lblRequestBody=Cuerpo de solicitud
#XFLD
@lblFormat=Formato
#XFLD
@lblResponse=Respuesta
#XFLD
@lblId=ID para recuperar estado
#XFLD
@Id=ID
#XFLD
@lblSuccessIndicator=Indicador de estado correcto
#XFLD
@lblErrorIndicator=Indicador de error
#XFLD
@lblErrorReason=Motivo de error
#XFLD
@lblStatus=Estado
#XFLD
@lblApiTestRun=Ejecución de prueba de API
#XFLD
@lblRunStatus=Estado de ejecución
#XFLD
@lblLastRan=Fecha de última ejecución
#XFLD
@lblTestRun=Ejecución de prueba
#XFLD
@lblDefaultHeader=Campos de encabezado predeterminados (pares de clave-valor)
#XFLD
@lblAdditionalHeader=Campo de encabezado adicional
#XFLD
@lblKey=Clave
#XFLD
@lblEditJSON=Editar JSON
#XFLD
@lblTasks=Tareas
#XFLD
@lblRESTfullTask=API
#XFLD: add field button text
btnAddField=Agregar campo de encabezado
#XFLD: view Details link
@viewDetails=Ver detalles
#XTOL
tooltipTxt=Más
#XTOL
delete=Eliminar
#XBTN: ok button text
btnOk=Ok
#XBTN: cancel button text
btnCancel=Cancelar
#XBTN: save button text
btnSave=Guardar
#XMSG Error: Object has duplicated technical name.
VAL_OBJECT_DUPLICATE_NAME=El objeto ''{0}'' ya existe en el repositorio. Ingrese otro nombre.
#XMSG: loading message while opening task chain
loadTaskChain=Cargado la cadena de tareas…
#model properties
#XFLD
@status_panel=Estado de ejecución
#XFLD
@deploy_status_panel=Estado de implementación
#XFLD
@status_lbl=Estado
#XFLD
@lblLastExecuted=Última ejecución
#XFLD
@lblNotExecuted=Aún sin ejecutar
#XFLD
@lblNotDeployed=No implementado
#XFLD
errorDetailsTxt=No se pudo recuperar el estado de ejecución
#XBTN: Schedule dropdown menu
SCHEDULE=Programar
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Editar programa
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Eliminar programa
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Crear programa
#XLNK
viewDetails=Ver detalles
#XMSG: error message for reading execution status from backend
backendErrorMsg=Parece que no se están cargando los datos del servidor en este momento. Intente recuperar los datos nuevamente.
#XFLD: Status text for Completed
@statusCompleted=Finalizado
#XFLD: Status text for Running
@statusRunning=En ejecución
#XFLD: Status text for Failed
@statusFailed=Error
#XFLD: Status text for Stopped
@statusStopped=Detenido
#XFLD: Status text for Stopping
@statusStopping=Detención en curso
#XFLD
@LoaderTitle=Cargando
#XFLD
#XFLD: Deploy status, dataflow has  been deployed / deployment date exists
@deployStatusActive=Implementado
@deployStatusRevised=Actualizaciones locales
@deployStatusFailed=Error
@deployStatusPending=Implementando…
@LoaderText=Recuperando detalles del servidor
#XMSG
@msgDetailFetchError=Se produjo un error durante la recuperación de detalles del servidor
#XFLD
@executeError=Error
#XFLD
@executeWarning=Advertencia
#XMSG
@executeConfirmDialog=Información
#XMSG
@executeunsavederror=Guarde su cadena de tareas antes de ejecutarla.
#XMSG
@executemodifiederror=Hay cambios sin guardar en la cadena de tareas. Guárdela.
#XMSG
@executerunningerror=La cadena de tareas está actualmente en ejecución. Espere a que se complete la ejecución actual antes de iniciar una nueva.
#XMSG
@btnExecuteAnyway=Ejecutar de todas maneras
#XMSG
@msgExecuteWithValidations=La cadena de tareas tiene errores de validación. La ejecución de la cadena de tareas puede terminar en error.
#XMSG
@msgRunDeployedVersion=Hay cambios para implementar. Se ejecutará la última versión implementada de la cadena de tareas. ¿Desea continuar?
#XMSG
#XMSG
@navToMonitoring=Abrir en el supervisor de la cadena de tareas
#XMSG
txtOR=O
#XFLD
@preview=Vista previa
#XMSG
txtand=y
#NOTR
@dateFormat=yyyy-MM-dd
#NOTR
@dateTimeFormatFilter=yyyy-MM-dd HH:mm:ss.
#NOTR
@timeFormat=HH:mm:ss
@lblColum=Columna
#XFLD
@lblCondition=Condición
#XFLD
@lblValue=Valor
#XMSG
@msgJsonInvalid=La cadena de tareas no se pudo guardar porque hay errores en el archivo JSON. Verifique y resuelva los errores.
#XTIT
@msgSaveFailTitle=JSON no válido.
#XMSG
NOT_CHAINABLE=El objeto "{0}" no se puede agregar a la cadena de tareas.
#XMSG
NOT_CHAINABLE_REMOTETABLE=Objeto ''{0}'': se cambiará el tipo de replicación.
#XMSG
searchTaskChain=Buscar objetos

#XFLD
@txtTaskChain=Cadena de tareas
#XFLD
@txtRemoteTable=Tabla remota
#XFLD
@txtRemoveData=Quitar datos replicados
#XFLD
@txtRemovePersist=Quitar datos persistentes
#XFLD
@txtView=Vista
#XFLD
@txtDataFlow=Flujo de datos
#XFLD
@txtIL=Búsqueda inteligente
#XFLD
@txtTransformationFlow=Flujo de transformación
#XFLD
@txtReplicationFlow=Flujo de replicación
#XFLD
@txtDeltaLocalTable=Tabla local
#XFLD
@txtBWProcessChain=Cadena de procesos de BW
#XFLD
@txtSQLScriptProcedure=Procedimiento de script SQL
#XFLD
@txtAPI=API
#XFLD
@txtMerge=Combinar
#XFLD
@txtOptimize=Optimizar
#XFLD
@txtVacuum=Eliminar registros

#XFLD
@txtRun=Ejecutar
#XFLD
@txtPersist=Persistencia
#XFLD
@txtReplicate=Replicar
#XFLD
@txtDelete=Eliminar registros con el tipo de cambio "Eliminado"
#XFLD
@txtRunTC=Ejecutar cadena de tareas
#XFLD
@txtRunBW=Ejecutar cadena de procesos de BW
#XFLD
@txtRunSQLScriptProcedure=Ejecutar procedimiento de script SQL

#XFLD
@txtRunDataFlow=Ejecutar flujo de datos
#XFLD
@txtPersistView=Vista persistente
#XFLD
@txtReplicateTable=Replicar tabla
#XFLD
@txtRunIL=Ejecutar búsqueda inteligente
#XFLD
@txtRunTF=Ejecutar flujo de transformación
#XFLD
@txtRunRF=Ejecutar flujo de replicación
#XFLD
@txtRemoveReplicatedData=Quitar datos replicados
#XFLD
@txtRemovePersistedData=Quitar datos persistentes
#XFLD
@txtMergeData=Combinar
#XFLD
@txtOptimizeData=Optimizar
#XFLD
@txtVacuumData=Eliminar registros
#XFLD
@txtRunAPI=Ejecutar API

#XFLD storage type text
hdlfStorage=Archivo

@statusNew=No implementado
@statusActive=Implementado
@statusRevised=Actualizaciones locales
@statusPending=Implementando…
@statusChangesToDeploy=Cambios para implementar
@statusDesignTimeError=Error de tiempo de diseño
@statusRunTimeError=Error de tiempo de ejecución

#XTIT
txtNodes=Objetos en la cadena de tareas ({0})
#XBTN
@deleteNodes=Eliminar

#XMSG
@txtDropDataToDiagram=Arrastre y suelte objetos en el diagrama.
#XMSG
@noData=Sin objetos

#XFLD
@txtTaskPosition=Posición de tarea

#input parameters and variables
#XFLD
lblInputParameters=Parámetros de entrada
#XFLD
ip_name=Nombre
#XFLD
ip_value=Valor
#XTEXT
@noObjectsFound=No se encontraron objetos

#XMSG
@msgExecuteSuccess=Se inició la cadena de tareas.
#XMSG
@msgExecuteFail=Se produjo un error en la ejecución de la cadena de tareas.
#XMSG
@msgDeployAndRunSuccess=Se inició la implementación y ejecución de la cadena de tareas.
#XMSG
@msgDeployAndRunFail=Se produjo un error al implementar y ejecutar la cadena de tareas.
#XMSG
@titleExecuteBusy=Espere.
#XMSG
@msgExecuteBusy=Estamos preparando sus datos para iniciar la ejecución de la cadena de tareas.
#XMSG
@msgAPITestRunSuccess=Se inició la ejecución de prueba de API.
#XMSG
@msgAPIExecuteBusy=Estamos preparando sus datos para iniciar la ejecución de prueba de API.

#XTOL
txtOpenInEditor=Abrir en el editor
#XTOL
txtPreviewData=Vista previa de datos

#datapreview
#XMSG
@msgDataPreviewNotSupp=No está disponible la vista previa de datos para este objeto.

#XMSG Error: empty model
VAL_MODEL_EMPTY=Parece que su modelo está vacío. Agregue algunos objetos.
#XMSG Error: deploy model
@msgDeployBeforeRun=Necesita implementar la cadena de tareas antes de ejecutarla.
#BTN: close dialog
btnClose=Cerrar

#XMSG Object is not Deployed
VAL_NOT_DEPLOYED=Se debe implementar el objeto "{0}" para continuar con la cadena de tareas.
#XMSG Object is having runtime error
VAL_RUNTIME_ERROR=El objeto "{0}" muestra un error de tiempo de ejecución. Verifique el objeto.
#XMSG Object is having design time error
VAL_DESIGNTIME_ERROR=El objeto "{0}" muestra un error de tiempo de diseño. Verifique el objeto.
#XMSG Procedure Object has been deleted
VAL_DELETED_PROCEDURE=Se eliminaron los siguientes procedimientos: {0}
#XMSG New Parameter added to Procedure object
VAL_NEW_PARAMETER_ADDED=Nuevos parámetros agregados al procedimiento "{1}": "{0}". Vuelva a implementar la cadena de tareas.
#XMSG Parameter removed from Procedure object
VAL_PARAMETER_DELETED=Nuevos parámetros quitados del procedimiento "{1}": "{0}". Vuelva a implementar la cadena de tareas.
#XMSG Parameter data type changed in Procedure object
VAL_PARAMETER_DATATYPE_MODIFIED=El tipo de datos del parámetro "{0}" cambió de "{1}" a "{2}" en el procedimiento "{3}".
#XMSG Parameter length changed of specific datatype in Procedure object
VAL_PARAMETER_LENGTH_MODIFIED=La longitud del parámetro "{0}" cambió de "{1}" a "{2}" en el procedimiento "{3}".
#XMSG Parameter precision changed of Decimal datatype in Procedure object
VAL_PARAMETER_PRECISION_MODIFIED=La precisión del parámetro "{0}" cambió de "{1}" a "{2}" en el procedimiento "{3}".
#XMSG Parameter scale changed of Decimal datatype in Procedure object
VAL_PARAMETER_SCALE_MODIFIED=La escala del parámetro "{0}" cambió de "{1}" a "{2}" en el procedimiento "{3}".
#XMSG Parameter value not provided in Procedure object
VAL_SQL_SCRIPT_PROCEDURE_PARAMETER_VALUE_MISSING=No introdujo un valor para los parámetros de entrada "{0}" que se requieren para el procedimiento "{1}".
#XMSG Remote Table activity changes to Remove Replicated Data
VAL_REMOVE_REPLICATED_DATA=La siguiente ejecución de una cadena de tareas convertirá el tipo de acceso de datos y los datos ya no se cargarán en tiempo real.
#XMSG Remote Table activity changes
REPLICATION_TYPE_CHANGED=Objeto "{0}": se cambiará el tipo de replicación.

#XFLD
@lblStartNode=Nodo de inicio
#XFLD
@lblEndNode=Nodo de fin
#XFLD
@linkTo={0} a {1}
#XTOL
@txtViewDetails=Ver detalles

#XTOL
txtOpenImpactLineage=Análisis de impacto y linaje
#XFLD
@emailNotifications=Notificaciones de correo electrónico
#XFLD
@txtReset=Restablecer
#XFLD
@emailMsgWarning=Se enviará un correo electrónico de plantilla predeterminado cuando el mensaje de correo esté vacío
#XFLD
@notificationSettings=Opciones de notificación
#XFLD
@recipientEmailAddr=Dirección de correo electrónico del destinatario
#XFLD
@emailSubject=Asunto de correo electrónico
@emailSubjectText=La cadena de tareas <TASKCHAIN_NAME> se completó con el estado <STATUS>
#XFLD
@emailMessage=Mensaje de correo electrónico
@emailMessageText=Estimado usuario:\n\n Este mensaje es para notificarle que la cadena de tareas:<TASKCHAIN_NAME> que se inició a las <START_TIME> finalizó con el estado <STATUS>. La ejecución se completó a las <END_TIME>.\n\nDetalles:\nEspacio:<SPACE_NAME>\nError:<ERROR_MESSAGE>
@selectEmailRecipientDialogTitle=Seleccionar dirección de correo electrónico del destinatario
@tenantMembers=Miembros del inquilino
@others=Otros({0})
@selectedEmailAddress=Destinatarios seleccionados
@add=Agregar
@placeholder=Marcador
@description=Descripción
@copyText=Copiar texto
@taskchainDetailsPlaceholder=Marcadores para los detalles de la cadena de tareas
@placeholderCopied=Se copió el marcador
@invalidEmailInfo=Ingrese la dirección de correo electrónico correcta
@maxMembersAlreadyAdded=Ya agregó el máximo de 20 miembros
@enterEmailAddress=Ingrese la dirección de correo electrónico del usuario
@inCorrectPlaceHolder={0} no es un marcador esperado en el cuerpo del correo electrónico.
@nsOFF=No enviar notificaciones
@nsFAILED=Enviar notificaciones de correo electrónico solo cuando la ejecución finalizó con errores
@nsCOMPLETED=Enviar notificaciones de correo electrónico solo cuando la ejecución finalizó correctamente
@nsANY=Enviar notificaciones de correo electrónico solo cuando la ejecución finalizó
@phStatus=Estado de la cadena de tareas - CORRECTO|ERROR
@phTaskChainTName=Nombre técnico de la cadena de tareas
@phTaskChainBName=Nombre empresarial de la cadena de tareas
@phLogId=ID de registro de la ejecución
@phUser=Usuario que ejecuta la cadena de tareas
@phLogUILink=Vínculo a la pantalla de registro de la cadena de tareas
@phStartTime=Hora de inicio de la ejecución
@phEndTime=Hora de fin de la ejecución
@phErrMsg=Primer mensaje de error en el registro de tareas. El registro está vacío si el estado es CORRECTO
@phSpaceName=Nombre técnico del espacio
@emailFormatError=Formato de correo electrónico no válido
@emailFormatErrorInListText=El formato de correo electrónico no es válido y se ingresó en la lista de miembros que no son inquilinos.
@emailSubjectTemplateText=Notificación para la cadena de tareas: $$taskChainName$$ - Espacio: $$spaceId$$ - Estado: $$status$$
@emailMessageTemplateText=Hola:\n\n Su cadena de tareas con la etiqueta $$taskChainName$$ finalizó con el estado $$status$$. \n Aquí hay otros detalles de la cadena de tareas:\n - Nombre técnico de la cadena de tareas: $$taskChainName$$ \n - ID de registro de la ejecución de la cadena de tareas: $$logId$$ \n - Usuario que ejecutó la cadena de tareas: $$user$$ \n - Vínculo a la pantalla de registro de la cadena de tareas: $$uiLink$$ \n - Hora de inicio de la ejecución de la cadena de tareas: $$startTime$$ \n - Hora de fin de la ejecución de la cadena de tareas: $$endTime$$ \n - Nombre del espacio: $$spaceId$$ \n
@deleteEmailRecepient=Eliminar destinatario
@emailInputDisabledText=Implemente la cadena de tareas para agregar destinatarios de correo electrónico.
@tenantOwnerDomainMatchErrorText=El dominio de la dirección de correo electrónico no coincide con el dominio del propietario del inquilino: {0}
@totalEmailIdLimitInfoText=Puede seleccionar hasta 20 destinatarios de correo electrónico, incluidos los usuarios miembros del inquilino y otros destinatarios.
@emailDomainInfoText=Solo se aceptan las direcciones de correo electrónico con el dominio: {0}.
@duplicateEmailErrorText=Hay destinatarios de correo electrónico duplicados en la lista.

#XFLD Zorder Title
@txtZorderTitle=Columnas de orden Z de Apache Spark 

#XFLD Zorder NoColumn
@txtZorderNoColumn=No se encontraron columnas de orden Z

#XFLD Zorder Column PrimaryKey
@txtZorderColumnPrimaryKey=Clave principal

#XFLD
@lblOperators=Operadores
addNewSelector=Agregar una nueva tarea
parallelSelector=Agregar una tarea en paralelo
replaceSelector=Reemplazar tarea existente
addparallelbranch=Agregar como rama en paralelo
addplaceholder=Agregar marcador
addALLOperation=Operador ALL
addOROperation=Operador ANY
addplaceholdertocanvas=Agregar marcador a lienzo
addplaceholderonselected=Agregar marcador tras la selección de tarea
#XFLD: Add Parallel Branch to Selected Task
addparallelbranchonselected=Agregar rama en paralelo tras la selección de tarea
addOperator=Agregar operador
txtAdd=Agregar
txtPlaceHolderText=Arrastrar y soltar una tarea aquí
@lblLayout=Diseño

#XMSG
VAL_UNCONNECTED_TASK=La tarea ''{0}'' no está conectada a la cadena de tareas.
#XMSG
VAL_TASK_SHOULD_HAVE_ONE_INCOMING_LINK=La tarea ''{0}'' debe tener solo un vínculo de entrada.
#XMSG
VAL_TASK_SHOULD_HAVE_INCOMING_LINK=La tarea ''{0}'' debe tener un vínculo de entrada.
#XMSG
VAL_UNCONNECTED_OPERATOR=El operador ''{0}'' no está conectado a la cadena de tareas.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ATLEAST_TWO_INCOMING_LINK=El operador ''{0}'' debe tener al menos dos vínculos de entrada.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ONE_OUT_LINK=El operador ''{0}'' debe tener al menos un vínculo de salida.
#XMSG
VAL_CIRCULAR_REFERENCE_EXITS=El bucle circular existe en la cadena de tareas ''{0}''.
#XMSG
VAL_UNCONNECTED_BRANCH=El operador o la rama ''{0}'' no están conectados a la cadena de tareas.
#XMSG
VAL_TASK_TRIGGERED_PARALLEL_MORE_THAN_ONE_TIME=La cadena ''{0}'' está conectada en paralelo más de una vez. Quite los duplicados para continuar.


txtBegin=Comenzar
txtNodesInLink=Objetos involucrados
#XTOL Tooltip for a context button on diagram
openInNewTab=Abrir en una pestaña nueva
#XTOL Tooltip for a context button on diagram
txtConnectToTaskOperator=Arrastrar para conectar
@emailUpdateError=Error al actualizar la lista de notificaciones de correo electrónico

#XMSG
noTeamPrivilegeTxt=No tiene permiso para ver una lista de miembros de inquilino. Use la pestaña Otros para agregar destinatarios de correo electrónico manualmente.

#XFLD Package
@txtPackage=Paquete

#XMSG warning for repository package assignment changed. {1} - package name
VAL_PACKAGE_CHANGED=Asignó este objeto al paquete ''{1}''. Haga clic en Guardar para confirmar y validar este cambio. Tenga presente que, luego de guardar este cambio, la asignación a un paquete no se puede deshacer en este editor.
#XMSG warning for repository package dependency issue.
MISSING_DEPENDENCY=Las dependencias del objeto ''{0}'' no se pueden resolver en el contexto del paquete ''{1}''.

#Warning messages for the folders in the "Others" tab of task chain editor
#XMSG generic warning if getting the objects in a particular folder fails with unhandled errors
getNonRepoArtefactTypeDescendantsError=Ocurrió un problema al recuperar los objetos en la carpeta que seleccionó.
#BW Process Chains folder related warning messages
#XMSG warning if user does not have the necessary permission related to the SAP BW Bridge space
missingPrivilegeForBridgeSpaceError=No tiene la autorización necesaria para ver o incluir cadenas de procesos de BW en una cadena de tareas.
#XMSG warning if there was a problem retrieving the BW process chains due to bridge space membership, bridge connection issues or other errors
generalBridgeSpaceError=Ocurrió un problema al recuperar las cadenas de procesos de BW.
#XMSG warning if the BW API for retrieving the BW process chains from the SAP BW Bridge tenant fails
bwApiClientError=Ocurrió un problema al recuperar las cadenas de procesos de BW del inquilino de puente de SAP BW.
#XMSG warning if BW process chains list is empty
bwApiEmptyListError=No se encontraron cadenas de procesos de BW en el inquilino de puente de SAP BW.
#XMSG: Task log message for invalid OIDC configuration in the SAP BW Bridge tenant
bwBridgeOIDCConfigStatusInvalid=La autenticación de OpenID no se configuró para este inquilino. Use el clientID "{0}" y la tokenURL "{1}" para configurar la autenticación de OpenID en el inquilino del puente de SAP BW como se describe en la nota de SAP 3536298.
#XMSG BW Process Chain Object has been deleted
VAL_DELETED_BW=Es posible que se hayan eliminado las siguientes cadenas de procesos de BW del inquilino de puente de SAP: {0}
#BW SQL Script Procedures folder related warning messages
#XFLD waring if the Procedure list is empty
procedureAPIEmptyList=No se creó ningún procedimiento o no se otorgó el privilegio de ejecución para el esquema de SQL abierto.
#Change digram orientations
changeOrientations=Cambiar orientaciones


# placeholder for the API Path
apiPath=Por ejemplo: /job/v1
# placeholder for the status API Path
statusAPIPath=Por ejemplo: /job/v1/{id}/status
# valueStateText for the API Path
apiPathRequired=Se requiere la ruta de API
#placeholder for the CSRF Token URL
csrfTokenURL=Solo se admite HTTPS
# Response Type 1
statusCode=Obtener resultado desde código de estado HTTP
# Response Type 2
locationHeader=Obtener resultado desde código de estado HTTP y encabezado de ubicación
# Response Type 3
responseBody=Obtener resultado desde código de estado HTTP y cuerpo de respuesta
# placeholder for ID
idPlaceholder=Ingresar ruta JSON
# placeholder for indicator value
indicatorValue=Ingresar valor
# Placeholder for key
keyPlaceholder=Ingresar clave
# Error message for missing key
KeyRequired=Se requiere la clave
# Error message for invalid key format
invalidKeyFormat=La clave de encabezado que ingresó no está admitida. Los encabezados válidos son:<ul><li>"prefer"</li><li>Encabezados que empiezan con "x-", excepto "x-forwarded-host"</li><li>Encabezados que contiene caracteres alfanuméricos, "-" o "_"</li></ul>
# Error message for missing value
valueRequired=Se requiere un valor
# Error message for invalid characters in value
invalidValueCharacters=El encabezado contiene caracteres no válidos. Los caracteres especiales permitidos son:\t ";", ":", "-", "_", ",", "?", "/" y "*"
# Validation message for invoke api path
apiPathValidation=Ingrese una ruta de API válida, por ejemplo: /job/v1
# Validation message for JSON path
jsonPathValidation=Ingrese una ruta JSON válida
# Validation message for success/error indicator
indicatorValueValidation=El valor de indicador debe empezar con un carácter alfanumérico y puede incluir los siguientes caracteres especiales:\t "-" y "_"
# Error message for JSON path
jsonPathRequired=Se requiere la ruta JSON
# Error message for invalid API Technical Name
invalidTechnicalName=El nombre técnico contiene caracteres no válidos
# Error message for empty Technical Name
emptyTechnicalName=El nombre técnico es obligatorio
# Tooltip for codeEditor dialog
codeEditorTooltip=Abrir la ventana de edición de JSON
# Status Api path validation message
validationStatusAPIPath=Ingrese una ruta de API válida, por ejemplo:  /job/v1/{id}/status
# CSRF token URL validation message
validationCSRFTokenURL=La URL de token CSRF debe empezar con "https://" y ser una URL válida
# Select a connection
selectConnection=Seleccione una conexión
# Validation message for connection item error
connectionNotReplicated=La conexión actualmente no es válida para ejecutar tareas de API. Abra la aplicación "Conexiones" y vuelva a ingresar sus credenciales para reparar la conexión HTTP
# Validation message for API task properties wrong value or missing
VAL_REST_API_PROPERTIES_MISSING=La tarea de API "{0}" tiene valores incorrectos o faltantes para las siguientes propiedades: {1}
# Validation message for API connection api is throwing some error or selected connection has some warning
VAL_REST_API_CONNECTION=La tarea de API "{0}" tiene un problema con la conexión HTTP. Abra la aplicación "Conexiones" y verifique la conexión
# Validation message for API task properties when delected connection
VAL_REST_API_CONNECTION_DELETED=La tarea de API "{0}" tiene una conexión "{1}" eliminada
# Validation message for API task properties when connection cannot be consumed in the API task
VAL_REST_API_CONNECTION_CONSUMPTION_WARNING=La tarea de API "{0}" tiene una conexión "{1}" que no se puede usar para ejecutar tareas de API. Abra la aplicación "Connections" y vuelva a ingresar sus credenciales para establecer conectividad para tareas de API
# Information message if synchrnous mode is selected in the API Invocation then we don't need status information
modeInfoText=En el modo de sincronización, el panel de estado no se muestra para invocaciones de API
# validation dialog button for Run API Test
saveAnyway=Guardar de todas maneras
# validation message for technical name
technicalNameValidation=El nombre técnico debe ser único dentro de la cadena de tareas. Elija otro nombre técnico.
# Connection error message
getHttpConnectionsError=Error al obtener las conexiones HTTP
# Msg for the API test run when the Task chain is not saved
taskChainNotSaved=Se debe guardar la cadena de tareas antes de ejecutar la prueba de API
# Msg failed to run API test run
@failedToRunAPI=Falló la ejecución de la prueba de API
# Msg for the API test run when its already in running state
apiTaskRunning=Ya hay una ejecución de prueba de API en curso. ¿Quiere iniciar una nueva ejecución de prueba?

topToBtm=Superior-Inferior
leftToRight=Izquierda-Derecha

#XTIT Apache Spark Application Settings
txtHeaderApacheSettings=Opciones de aplicación de Apache Spark
#XFLD Use Default
txtUseSpaceDefault=Usar valor predeterminado
#XFLD Application
txtApplication=Aplicación
#XFLD Define new settings for this Task
txtNewSettings=Definir nuevas opciones para esta tarea

#XFLD Use Default
txtUseDefault=Usar valor predeterminado




