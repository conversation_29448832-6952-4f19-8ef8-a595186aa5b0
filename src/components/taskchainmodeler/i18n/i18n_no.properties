#XTOL Undo
@undo=Angre
#XTOL Redo
@redo=Gjenta
#XTOL Delete Selected Symbol
@deleteNode=Slett valgt symbol
#XTOL Zoom to Fit
@zoomToFit=Zoom for å tilpasse visningen etter skjermen
#XTOL Auto Layout
@autoLayout=Automatisk oppsett
#XMSG
@welcomeText=Dra og slipp objekter fra panelet til venstre til dette lerretet.
#XMSG
@txtNoData=Det ser ut som du ikke har lagt til noe objekt ennå.
#XMSG
VAL_ENTER_VALID_STRING_GEN=Oppgi en gyldig streng med en lengde som er mindre eller lik {0}.
#XMSG
@noParametersMsg=Denne prosedyren har ingen inndataparametere.
#XMSG
@ip_enterValueMsg="{0}" (Kjør SQL Script-prosedyre) har inndataparametere. Du kan sette en verdi for hver av dem.
#XTOL
@validateModel=Valideringsmeldinger
#XTOL
@hierarchy=Hierarki
#XTOL
@columnCount=Antall kolonner
#XFLD
@yes=Ja
#XFLD
@no=Nei
#XTIT Save Dialog param
@modelNameTaskChain=Oppgavekjede
#properties panel
@lblPropertyTitle=Egenskaper
#XFLD
@lblGeneral=Generelt
#XFLD : Setting
@lblSetting=Innstillinger
#XTXT: Delta Enabled Local Table Deletion
txtDeltaTableDeletion=Slett alle alle ferdigbehandlede poster med endringstypen "Slettet", som er eldre enn
#XTXT: Delta Enabled Local Table Deletion Input field description
desDeltaTableDeletion=Dager
#XFLD: Data Activation label
@lblDataActivation=Dataaktivering
#XFLD: Latency label
@latency=Latens
#XTEXT: Text for Latency dropdown
txtLatencyDefault=Standard
#XTEXT: Text 1 hour
txtOneHour=1 time
#XTEXT: Text 2 hours
txtTwoHours=2 timer
#XTEXT: Text 3 hours
txtThreeHours=3 timer
#XTEXT: Text 4 hours
txtFourHours=4 timer
#XTEXT: Text 6 hours
txtSixHours=6 timer
#XTEXT: Text 12 hours
txtTwelveHours=12 timer
#XTEXT: Text 1 day
txtOneDay=1 dag
#XFLD: Latency label
@autoRestartHead=Automatisk omstart
#XFLD
@lblConnectionName=Forbindelse
#XFLD
@lblQualifiedName=Kvalifisert navn
#XFLD
@lblSpaceName=Romnavn
#XFLD
@lblLocalSchemaName=Lokalt skjema
#XFLD
@lblType=Objekttype
#XFLD
@lblActivity=Aktivitet
#XFLD
@lblTableName=Tabellnavn
#XFLD
@lblBusinessName=Forretningsnavn
#XFLD
@lblTechnicalName=Teknisk navn
#XFLD
@lblSpace=Rom
#XFLD
@lblLabel=Etikett
#XFLD
@lblDataType=Datatype
#XFLD
@lblDescription=Beskrivelse
#XFLD
@lblStorageType=Lagring
#XFLD
@lblHTTPConnection=Generisk HTTP-forbindelse
#XFLD
@lblAPISettings=Generiske API-forbindelser
#XFLD
@header=Topptekster
#XFLD
@lblInvoke=API-anrop
#XFLD
@lblMethod=Metode
#XFLD
@lblUrl=Basis-URL
#XFLD
@lblAPIPath=API-bane
#XFLD
@lblMode=Modus
#XFLD
@lblCSRFToken=Krev CSRF-token
#XFLD
@lblTokenURL=URL-adresse til CSRF-token
#XFLD
@csrfTokenInfoText=Hvis ikke oppgitt, brukes basis-URL-en og API-banen
#XFLD
@lblCSRF=CSRF-token
#XFLD
@lblRequestBody=Forespørselshoveddel
#XFLD
@lblFormat=Format
#XFLD
@lblResponse=Svar
#XFLD
@lblId=ID for å hente status
#XFLD
@Id=ID
#XFLD
@lblSuccessIndicator=Resultatindikator
#XFLD
@lblErrorIndicator=Feilindikator
#XFLD
@lblErrorReason=Årsak til feil
#XFLD
@lblStatus=Status
#XFLD
@lblApiTestRun=API-testkjøring
#XFLD
@lblRunStatus=Kjøringsstatus
#XFLD
@lblLastRan=Sist kjørt den
#XFLD
@lblTestRun=Testkjøring
#XFLD
@lblDefaultHeader=Standard topptekstfelt (nøkkelverdipar)
#XFLD
@lblAdditionalHeader=Ytterligere topptekstfelt
#XFLD
@lblKey=Nøkkel
#XFLD
@lblEditJSON=Rediger JSON
#XFLD
@lblTasks=Oppgaver
#XFLD
@lblRESTfullTask=API
#XFLD: add field button text
btnAddField=Legg til topptekstfelt
#XFLD: view Details link
@viewDetails=Vis detaljer
#XTOL
tooltipTxt=Mer
#XTOL
delete=Slett
#XBTN: ok button text
btnOk=OK
#XBTN: cancel button text
btnCancel=Avbryt
#XBTN: save button text
btnSave=Lagre
#XMSG Error: Object has duplicated technical name.
VAL_OBJECT_DUPLICATE_NAME=Objektet ''{0}'' finnes allerede i repository. Oppgi et annet navn.
#XMSG: loading message while opening task chain
loadTaskChain=Laster oppgavekjeden ...
#model properties
#XFLD
@status_panel=Kjøringsstatus
#XFLD
@deploy_status_panel=Implementeringsstatus
#XFLD
@status_lbl=Status
#XFLD
@lblLastExecuted=Siste kjøring
#XFLD
@lblNotExecuted=Ikke kjørt ennå
#XFLD
@lblNotDeployed=Ikke implementert
#XFLD
errorDetailsTxt=Kan ikke hente kjøringsstatus
#XBTN: Schedule dropdown menu
SCHEDULE=Tidsplan
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Rediger tidsplan
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Slett tidsplan
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Opprett tidsplan
#XLNK
viewDetails=Vis detaljer
#XMSG: error message for reading execution status from backend
backendErrorMsg=Det ser ut til at dataene ikke lastes fra serveren for øyeblikket. Prøv å hente dataene på nytt..
#XFLD: Status text for Completed
@statusCompleted=Fullført
#XFLD: Status text for Running
@statusRunning=Kjører
#XFLD: Status text for Failed
@statusFailed=Mislykket
#XFLD: Status text for Stopped
@statusStopped=Stoppet
#XFLD: Status text for Stopping
@statusStopping=Stopper
#XFLD
@LoaderTitle=Laster
#XFLD
#XFLD: Deploy status, dataflow has  been deployed / deployment date exists
@deployStatusActive=Implementert
@deployStatusRevised=Lokale oppdateringer
@deployStatusFailed=Mislykket
@deployStatusPending=Distribuerer ...
@LoaderText=Henter detaljer fra serveren
#XMSG
@msgDetailFetchError=Feil under henting av detaljer fra serveren
#XFLD
@executeError=Feil
#XFLD
@executeWarning=Advarsel
#XMSG
@executeConfirmDialog=Info
#XMSG
@executeunsavederror=Lagre oppgavekjeden før du kjører den.
#XMSG
@executemodifiederror=Det er ulagrede endringer i oppgavekjeden. Lagre den.
#XMSG
@executerunningerror=Oppgavekjeden kjøres akkurat nå. Vent til denne kjøringen er ferdig før du starter en ny.
#XMSG
@btnExecuteAnyway=Kjør likevel
#XMSG
@msgExecuteWithValidations=Oppgavekjeden har valideringsfeil. Hvis du kjører oppgavekjeden, kan det føre til feil.
#XMSG
@msgRunDeployedVersion=Det finnes endringer som må distribueres. Den sist distribuerte versjonen av oppgavekjeden vil bli kjørt. Vil du fortsette?
#XMSG
#XMSG
@navToMonitoring=Åpne i Oppgavekjedemonitor
#XMSG
txtOR=ELLER
#XFLD
@preview=Forhåndsvisning
#XMSG
txtand=OG
#NOTR
@dateFormat=yyyy-MM-dd
#NOTR
@dateTimeFormatFilter=yyyy-MM-dd HH:mm:ss.
#NOTR
@timeFormat=HH:mm:ss
@lblColum=Kolonne
#XFLD
@lblCondition=Betingelse
#XFLD
@lblValue=Verdi
#XMSG
@msgJsonInvalid=Oppgavekjeden kan ikke lagres fordi det er feil i JSON-filen. Kontroller og korriger feilen.
#XTIT
@msgSaveFailTitle=Ugyldig JSON.
#XMSG
NOT_CHAINABLE=Objektet "{0}" kan ikke legges til i oppgavekjeden.
#XMSG
NOT_CHAINABLE_REMOTETABLE=Objekt ''{0}'': Replikeringstype blir endret.
#XMSG
searchTaskChain=Søk etter objekter

#XFLD
@txtTaskChain=Oppgavekjede
#XFLD
@txtRemoteTable=Fjerntabell
#XFLD
@txtRemoveData=Fjern replikerte data
#XFLD
@txtRemovePersist=Fjern persisterte data
#XFLD
@txtView=Vis
#XFLD
@txtDataFlow=Dataflyt
#XFLD
@txtIL=Intelligent søk
#XFLD
@txtTransformationFlow=Transformasjonsflyt
#XFLD
@txtReplicationFlow=Replikeringsflyt
#XFLD
@txtDeltaLocalTable=Lokal tabell
#XFLD
@txtBWProcessChain=BW-prosesskjede
#XFLD
@txtSQLScriptProcedure=SQL Script-prosedyre
#XFLD
@txtAPI=API
#XFLD
@txtMerge=Slå sammen
#XFLD
@txtOptimize=Optimer
#XFLD
@txtVacuum=Slett poster

#XFLD
@txtRun=Kjør
#XFLD
@txtPersist=Persister
#XFLD
@txtReplicate=Repliker
#XFLD
@txtDelete=Slett poster med endringstype 'Slettet'
#XFLD
@txtRunTC=Kjør oppgavekjede
#XFLD
@txtRunBW=Kjør BW-prosesskjede
#XFLD
@txtRunSQLScriptProcedure=Kjør SQL Script-prosedyre

#XFLD
@txtRunDataFlow=Kjør dataflyt
#XFLD
@txtPersistView=Persister visning
#XFLD
@txtReplicateTable=Repliker tabell
#XFLD
@txtRunIL=Kjør intelligent søk
#XFLD
@txtRunTF=Kjør transformasjonsflyt
#XFLD
@txtRunRF=Kjør replikeringsflyt
#XFLD
@txtRemoveReplicatedData=Fjern replikerte data
#XFLD
@txtRemovePersistedData=Fjern persisterte data
#XFLD
@txtMergeData=Slå sammen
#XFLD
@txtOptimizeData=Optimer
#XFLD
@txtVacuumData=Slett poster
#XFLD
@txtRunAPI=Kjør API

#XFLD storage type text
hdlfStorage=Fil

@statusNew=Ikke distribuert
@statusActive=Distribuert
@statusRevised=Lokale oppdateringer
@statusPending=Distribuerer ...
@statusChangesToDeploy=Endringer som skal distribueres
@statusDesignTimeError=Designtidsavbrudd
@statusRunTimeError=Feil under kjøring

#XTIT
txtNodes=Objekter i oppgavekjeden ({0})
#XBTN
@deleteNodes=Slett

#XMSG
@txtDropDataToDiagram=Dra og slipp objekter til diagrammet.
#XMSG
@noData=Ingen objekter

#XFLD
@txtTaskPosition=Oppgaveposisjon

#input parameters and variables
#XFLD
lblInputParameters=Inndataparametere
#XFLD
ip_name=Navn
#XFLD
ip_value=Verdi
#XTEXT
@noObjectsFound=Finner ingen objekter

#XMSG
@msgExecuteSuccess=Kjøringen av oppgavekjeden er startet.
#XMSG
@msgExecuteFail=Kan ikke kjøre oppgavekjeden.
#XMSG
@msgDeployAndRunSuccess=Distribusjon og kjøring av oppgavekjeden er startet.
#XMSG
@msgDeployAndRunFail=Kan ikke distribuere og kjøre oppgavekjeden.
#XMSG
@titleExecuteBusy=Vent litt.
#XMSG
@msgExecuteBusy=Vi forbereder dataene dine til kjøring av oppgavekjeden.
#XMSG
@msgAPITestRunSuccess=API-testkjøring har startet.
#XMSG
@msgAPIExecuteBusy=Vi forbereder dataene dine for å starte API-testkjøringen.

#XTOL
txtOpenInEditor=Åpne i redigeringsprogram
#XTOL
txtPreviewData=Forhåndsvis data

#datapreview
#XMSG
@msgDataPreviewNotSupp=Forhåndsvisning av data er ikke tilgjengelig for dette objektet.

#XMSG Error: empty model
VAL_MODEL_EMPTY=Det virker som modellen din er tom. Legg til noen objekter.
#XMSG Error: deploy model
@msgDeployBeforeRun=Du må distribuere oppgavekjeden før du kjører den.
#BTN: close dialog
btnClose=Lukk

#XMSG Object is not Deployed
VAL_NOT_DEPLOYED=Objektet "{0}" må distribueres før du kan fortsette med oppgavekjeden.
#XMSG Object is having runtime error
VAL_RUNTIME_ERROR=Objektet ''{0}'' returnerer en kjøringstidsfeil. Kontroller objektet.
#XMSG Object is having design time error
VAL_DESIGNTIME_ERROR=Objektet ''{0}'' returnerer en designtidsfeil. Kontroller objektet.
#XMSG Procedure Object has been deleted
VAL_DELETED_PROCEDURE=Følgende prosedyrer er slettet: {0}
#XMSG New Parameter added to Procedure object
VAL_NEW_PARAMETER_ADDED=Nye parametere er lagt til i prosedyren "{1}": "{0}". Distribuer oppgavekjeden på nytt.
#XMSG Parameter removed from Procedure object
VAL_PARAMETER_DELETED=Nye parametere er fjernet fra prosedyren "{1}": "{0}". Distribuer oppgavekjeden på nytt.
#XMSG Parameter data type changed in Procedure object
VAL_PARAMETER_DATATYPE_MODIFIED=Datatype for parameter "{0}" er endret fra "{1}" til "{2}" i prosedyre "{3}".
#XMSG Parameter length changed of specific datatype in Procedure object
VAL_PARAMETER_LENGTH_MODIFIED=Lengde på parameter "{0}" er endret fra "{1}" til "{2}" i prosedyre "{3}".
#XMSG Parameter precision changed of Decimal datatype in Procedure object
VAL_PARAMETER_PRECISION_MODIFIED=Presisjon for parameter "{0}" er endret fra "{1}" til "{2}" i prosedyre "{3}".
#XMSG Parameter scale changed of Decimal datatype in Procedure object
VAL_PARAMETER_SCALE_MODIFIED=Skala for parameter "{0}" er endret fra "{1}" til "{2}" i prosedyre "{3}".
#XMSG Parameter value not provided in Procedure object
VAL_SQL_SCRIPT_PROCEDURE_PARAMETER_VALUE_MISSING=Du har ikke oppgitt verdier for inndataparameterne, som er nødvendig for prosedyre "{0}": "{1}"
#XMSG Remote Table activity changes to Remove Replicated Data
VAL_REMOVE_REPLICATED_DATA=Den neste kjøringen av en oppgavekjede vil endre datatilgangstypen, og data blir ikke lenger oppdatert i sanntid.
#XMSG Remote Table activity changes
REPLICATION_TYPE_CHANGED=Objekt ''{0}'': Replikeringstype blir endret.

#XFLD
@lblStartNode=Startnode
#XFLD
@lblEndNode=Sluttnode
#XFLD
@linkTo={0} til {1}
#XTOL
@txtViewDetails=Vis detaljer

#XTOL
txtOpenImpactLineage=Analyse av påvirkning og avstamning
#XFLD
@emailNotifications=E-postvarslinger
#XFLD
@txtReset=Tilbakestill
#XFLD
@emailMsgWarning=Standardmal-e-post vil bli sendt når e-postemeldingen er tom
#XFLD
@notificationSettings=Varslingsinnstillinger
#XFLD
@recipientEmailAddr=E-postadresse for mottaker
#XFLD
@emailSubject=E-postemne
@emailSubjectText=Oppgavekjede <TASKCHAIN_NAME> er fullført med status <STATUS>
#XFLD
@emailMessage=E-postmelding
@emailMessageText=Kjøre bruker,\n\n Dette er en melding for å varsle deg om at oppgavekjeden <TASKCHAIN_NAME> som ble kjørt kl. <START_TIME>, er fullført med status <STATUS>. Utføringen ble avsluttet kl. <END_TIME>.\n\Detaljer:\nRom:<SPACE_NAME>\nFeil:<ERROR_MESSAGE>
@selectEmailRecipientDialogTitle=Velg mottakers e-postadresse
@tenantMembers=Tenantmedlemmer
@others=Andre ({0})
@selectedEmailAddress=Valgte mottakere
@add=Legg til
@placeholder=Plassholder
@description=Beskrivelse
@copyText=Kopier tekst
@taskchainDetailsPlaceholder=Plassholder for detaljer i oppgavekjede
@placeholderCopied=Plassholder er kopiert
@invalidEmailInfo=Oppgi riktig e-postadresse
@maxMembersAlreadyAdded=Du har allerede lagt til maksimalt antall medlemmer som er 20
@enterEmailAddress=Oppgi e-postadresse for bruker
@inCorrectPlaceHolder={0} er ikke en forventet plassholder i brødteksten i e-postmeldingen.
@nsOFF=Ikke send varslinger
@nsFAILED=Send e-postvarsling bare når kjøringen er fullført med feil
@nsCOMPLETED=Send e-postvarsling bare når kjøringen er fullført uten feil
@nsANY=Send e-postvarsling bare når kjøringen er fullført
@phStatus=Status for oppgavekjeden - Vellykket|Mislykket
@phTaskChainTName=Teknisk navn på oppgavekjeden
@phTaskChainBName=Forretningsnavn på oppgavekjeden
@phLogId=Protokoll-ID for kjøringen
@phUser=Bruker som kjører oppgavekjeden
@phLogUILink=Kobling til protokollvisningen for oppgavekjeden
@phStartTime=Starttidspunkt for kjøringen
@phEndTime=Sluttidspunkt for kjøringen
@phErrMsg=Første feilmelding i oppgaveprotokollen. Protokollen er tom hvis statusen er Vellykket.
@phSpaceName=Teknisk navn på rommet
@emailFormatError=Ugyldig e-postformat
@emailFormatErrorInListText=Ugyldig e-postformat oppgitt i listen over ikke-tenantmedlemmer.
@emailSubjectTemplateText=Varsling for oppgavekjede: $$taskChainName$$ - rom: $$spaceId$$ - status: $$status$$
@emailMessageTemplateText=Hei!\n\n Oppgavekjeden med navnet $$taskChainName$$ er utført med status $$status$$. \n Her er noen andre detaljer om oppgavekjeden:\n - Teknisk navn på oppgavekjeden: $$taskChainName$$ \n - Protokoll-ID for oppgavekjedekjøringen: $$logId$$ \n - Brukeren som kjørte oppgavekjeden: $$user$$ \n - Kobling til protokollvisningen for oppgavekjeden: $$uiLink$$ \n - Starttidspunkt for oppgavekjedekjøringen: $$startTime$$ \n - Sluttidspunkt for oppgavekjedekjøringen: $$endTime$$ \n - Navn på rommet: $$spaceId$$ \n
@deleteEmailRecepient=Slett mottaker
@emailInputDisabledText=Distribuer oppgavekjeden for å legge til e-postmottakere.
@tenantOwnerDomainMatchErrorText=Domenet for e-postadressen samsvarer ikke med domenet for tenant-eieren: {0}
@totalEmailIdLimitInfoText=Du kan velge opptil 20 e-postmottakere inkludert tenantmedlemsbrukere og andre mottakere.
@emailDomainInfoText=Bare e-postadresser med domenet {0} blir akseptert.
@duplicateEmailErrorText=Det finnes dupliserte e-postmottakere på listen.

#XFLD Zorder Title
@txtZorderTitle=Apache Spark Z-Order-kolonner

#XFLD Zorder NoColumn
@txtZorderNoColumn=Finner ingen Z-Order-kolonner

#XFLD Zorder Column PrimaryKey
@txtZorderColumnPrimaryKey=Primærnøkkel

#XFLD
@lblOperators=Operatorer
addNewSelector=Legg til som ny oppgave
parallelSelector=Legg til som parallell oppgave
replaceSelector=Erstatt eksisterende oppgave
addparallelbranch=Legg til som parallell gren
addplaceholder=Legg til plassholder
addALLOperation=ALL-operator
addOROperation=ANY-operator
addplaceholdertocanvas=Legg til plassholder på lerret
addplaceholderonselected=Legg til plassholder etter valgt oppgave
#XFLD: Add Parallel Branch to Selected Task
addparallelbranchonselected=Legg til parallell gren etter valgt oppgave
addOperator=Legg til operator
txtAdd=Legg til
txtPlaceHolderText=Dra og slipp en oppgave her
@lblLayout=Oppsett

#XMSG
VAL_UNCONNECTED_TASK=Oppgave "{0}" er ikke tilknyttet oppgavekjeden.
#XMSG
VAL_TASK_SHOULD_HAVE_ONE_INCOMING_LINK=Oppgave "{0}" kan ha bare én inngående kobling.
#XMSG
VAL_TASK_SHOULD_HAVE_INCOMING_LINK=Oppgave "{0}" kan ha én inngående kobling.
#XMSG
VAL_UNCONNECTED_OPERATOR=Operator "{0}" er ikke tilknyttet oppgavekjeden.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ATLEAST_TWO_INCOMING_LINK=Operator "{0}" må ha minst to inngående koblinger.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ONE_OUT_LINK=Operator "{0}" må ha minst én utgående kobling.
#XMSG
VAL_CIRCULAR_REFERENCE_EXITS=Sirkulær sløyfe finnes i oppgavekjeden "{0}".
#XMSG
VAL_UNCONNECTED_BRANCH=Objekt/gren "{0}" er ikke tilknyttet oppgavekjeden.
#XMSG
VAL_TASK_TRIGGERED_PARALLEL_MORE_THAN_ONE_TIME=Oppgave "{0}" er tilknyttet parallelt mer enn en gang. Fjern duplikater for å fortsette.


txtBegin=Start
txtNodesInLink=Objekter involvert
#XTOL Tooltip for a context button on diagram
openInNewTab=Åpne i nytt faneark
#XTOL Tooltip for a context button on diagram
txtConnectToTaskOperator=Dra for å koble til
@emailUpdateError=Feil ved oppdatering av e-postvarselliste

#XMSG
noTeamPrivilegeTxt=Du har ikke autorisasjon til å vise en liste over tenantmedlemmer. Bruk fanearket Andre for å legge til e-postmottakere manuelt.

#XFLD Package
@txtPackage=Pakke

#XMSG warning for repository package assignment changed. {1} - package name
VAL_PACKAGE_CHANGED=Du har tilordnet dette objektet til pakken ''{1}''. Klikk på Lagre for å bekrefte og validere denne endringen. Vær oppmerksom på at du ikke kan angre tilordningen til en pakke i dette redigeringsprogrammet etter at du har lagret.
#XMSG warning for repository package dependency issue.
MISSING_DEPENDENCY=Avhengigheter for objektet ''{0}'' kan ikke løses i konteksten til pakken ''{1}''.

#Warning messages for the folders in the "Others" tab of task chain editor
#XMSG generic warning if getting the objects in a particular folder fails with unhandled errors
getNonRepoArtefactTypeDescendantsError=Det oppsto et problem ved henting av objektene i mappen du valgte.
#BW Process Chains folder related warning messages
#XMSG warning if user does not have the necessary permission related to the SAP BW Bridge space
missingPrivilegeForBridgeSpaceError=Du har ikke autorisasjonen som kreves for å vise eller inkludere BW-prosesskjeder i en oppgavekjede.
#XMSG warning if there was a problem retrieving the BW process chains due to bridge space membership, bridge connection issues or other errors
generalBridgeSpaceError=Det oppsto et problem ved henting av BW-prosesskjedene.
#XMSG warning if the BW API for retrieving the BW process chains from the SAP BW Bridge tenant fails
bwApiClientError=Det oppsto et problem ved henting av BW-prosesskjedene fra SAP BW-brotenanten.
#XMSG warning if BW process chains list is empty
bwApiEmptyListError=Finner ingen BW-prosesskjeder i SAP BW-brotenanten.
#XMSG: Task log message for invalid OIDC configuration in the SAP BW Bridge tenant
bwBridgeOIDCConfigStatusInvalid=OpenID-autentisering er ikke konfigurert for denne tenanten. Bruk ClientID "{0}" og tokenURL "{1}" til å konfigurere OpenID-autentisering i SAP BW-brotenanten slik det står beskrevet i SAP-merknad 3536298.
#XMSG BW Process Chain Object has been deleted
VAL_DELETED_BW=Det er mulig at de følgende BW-prosesskjedene er slettet fra SAP BW-brotenanten: {0}
#BW SQL Script Procedures folder related warning messages
#XFLD waring if the Procedure list is empty
procedureAPIEmptyList=Ingen prosedyrer er opprettet eller autorisasjonen EXECUTE er ikke tildelt Open SQL-skjema.
#Change digram orientations
changeOrientations=Endre retninger


# placeholder for the API Path
apiPath=For eksempel: /job/v1
# placeholder for the status API Path
statusAPIPath=For eksempel: /job/v1/{id}/status
# valueStateText for the API Path
apiPathRequired=API-bane er obligatorisk
#placeholder for the CSRF Token URL
csrfTokenURL=Bare HTTPS støttes
# Response Type 1
statusCode=Hent resultat fra HTTP-statuskode
# Response Type 2
locationHeader=Hent resultat fra HTTP-statuskode og lokaliseringstopptekst
# Response Type 3
responseBody=Hent resultat fra HTTP-statuskode for svartekst
# placeholder for ID
idPlaceholder=Oppgi JSON-bane
# placeholder for indicator value
indicatorValue=Oppgi verdi
# Placeholder for key
keyPlaceholder=Oppgi nøkkel
# Error message for missing key
KeyRequired=Nøkkel er obligatorisk
# Error message for invalid key format
invalidKeyFormat=Topptekstnøkkelen som du oppgav er ikke tillatt. Gyldige topptekster er:<ul><li>"foretrekk"</li><li>topptekster som starter med "x-", unntatt "x-forwarded-host"</li><li>topptekster som inneholder alfanumeriske tegn , "-", eller "_"</li></ul>
# Error message for missing value
valueRequired=Verdi er obligatorisk
# Error message for invalid characters in value
invalidValueCharacters=Toppteksten inneholder ugyldige tegn. Spesialtegn som er tillatt er:\t ";", ":", "-", "_", ",", "?", "/", og "*"
# Validation message for invoke api path
apiPathValidation=Oppgi en gyldig API-bane, for eksempel: /job/v1
# Validation message for JSON path
jsonPathValidation=Oppgi en gyldig JSON-bane
# Validation message for success/error indicator
indicatorValueValidation=Indikatorverdi må starte med et alfanumerisk tegn og kan inkludere følgende spesialtegn:\t "-", og "_"
# Error message for JSON path
jsonPathRequired=JSON-bane er obligatorisk
# Error message for invalid API Technical Name
invalidTechnicalName=Teknisk navn inneholder ugyldige tegn
# Error message for empty Technical Name
emptyTechnicalName=Teknisk navn er obligatorisk
# Tooltip for codeEditor dialog
codeEditorTooltip=Åpne JSON redigeringsvindu
# Status Api path validation message
validationStatusAPIPath=Oppgi en gyldig API-bane, for eksempel: /job/v1/{id}/status
# CSRF token URL validation message
validationCSRFTokenURL=CSRF-token-URL må starte med 'https://' og være en gyldig URL
# Select a connection
selectConnection=Velg en forbindelse
# Validation message for connection item error
connectionNotReplicated=Forbindelse er for øyeblikket ugyldig for å kjøre API-oppgaver. Åpne appen "Forbindelser" og oppgi påloggingsinformasjonen din på nytt for å fikse HTTP-forbindelsen.
# Validation message for API task properties wrong value or missing
VAL_REST_API_PROPERTIES_MISSING=API-oppgaven "{0}" har feil eller mangler verdier for følgende egenskaper: {1}
# Validation message for API connection api is throwing some error or selected connection has some warning
VAL_REST_API_CONNECTION=API-oppgaven "{0}" har et problem med HTTP-forbindelsen. Åpne appen "Forbindelser" og kontroller forbindelsen.
# Validation message for API task properties when delected connection
VAL_REST_API_CONNECTION_DELETED=API-oppgaven "{0}" har en slettet "{1}"-forbindelse
# Validation message for API task properties when connection cannot be consumed in the API task
VAL_REST_API_CONNECTION_CONSUMPTION_WARNING=API-oppgaven "{0}" har en "{1}"-forbindelse som ikke kan brukes til å utføre API-oppgaver. Åpne appen "Forbindelser" og skriv inn påloggingsinformasjonen din på nytt, for å opprette forbindelse for API-oppgaver.
# Information message if synchrnous mode is selected in the API Invocation then we don't need status information
modeInfoText=Statuspanelet vises ikke for API-anrop i synkron modus
# validation dialog button for Run API Test
saveAnyway=Lagre likevel
# validation message for technical name
technicalNameValidation=Det tekniske navnet må være entydig i oppgavekjeden. Velg et annet teknisk navn.
# Connection error message
getHttpConnectionsError=Henting av HTTP-forbindelser mislyktes
# Msg for the API test run when the Task chain is not saved
taskChainNotSaved=Oppgavekjede må lagres før utføring av API-testkjøring
# Msg failed to run API test run
@failedToRunAPI=Kan ikke utføre API-testkjøring
# Msg for the API test run when its already in running state
apiTaskRunning=En API-testkjøring pågår allerede. Vil du starte en ny testkjøring?

topToBtm=Topp-bunn
leftToRight=Venstre-høyre

#XTIT Apache Spark Application Settings
txtHeaderApacheSettings=Applikasjonsinnstillinger for Apache Spark
#XFLD Use Default
txtUseSpaceDefault=Bruk standard
#XFLD Application
txtApplication=Applikasjon
#XFLD Define new settings for this Task
txtNewSettings=Definer nye innstillinger for denne oppgaven

#XFLD Use Default
txtUseDefault=Bruk standard




