#XTOL Undo
@undo=Zpět
#XTOL Redo
@redo=Znovu
#XTOL Delete Selected Symbol
@deleteNode=Odstranit vybraný symbol
#XTOL Zoom to Fit
@zoomToFit=Přizpůsobit zobrazení
#XTOL Auto Layout
@autoLayout=Automatický layout
#XMSG
@welcomeText=Umístěte objekty přetažením z levého panelu na toto plátno.
#XMSG
@txtNoData=Vypadá to, že jste ještě nepřidali žádný objekt.
#XMSG
VAL_ENTER_VALID_STRING_GEN=Zadejte platný řetězec o délce menší nebo rovné {0}.
#XMSG
@noParametersMsg=Tento postup nemá žádné vstupní parametry.
#XMSG
@ip_enterValueMsg="{0}" (běh postupu skriptu SQL) má vstupní parametry. Můžete nastavit hodnotu každ<PERSON><PERSON> z nich.
#XTOL
@validateModel=Ov<PERSON>řovací zprávy
#XTOL
@hierarchy=Hierarchie
#XTOL
@columnCount=Počet sloupců
#XFLD
@yes=Ano
#XFLD
@no=Ne
#XTIT Save Dialog param
@modelNameTaskChain=Řetězce úloh
#properties panel
@lblPropertyTitle=Vlastnosti
#XFLD
@lblGeneral=Všeobecně
#XFLD : Setting
@lblSetting=Nastavení
#XTXT: Delta Enabled Local Table Deletion
txtDeltaTableDeletion=Odstranit všechny kompletně zpracované záznamy s typem změny ‚Odstraněno‘ které jsou starší než
#XTXT: Delta Enabled Local Table Deletion Input field description
desDeltaTableDeletion=Dny
#XFLD: Data Activation label
@lblDataActivation=Aktivace dat
#XFLD: Latency label
@latency=Latence
#XTEXT: Text for Latency dropdown
txtLatencyDefault=Standardní
#XTEXT: Text 1 hour
txtOneHour=1 hodina
#XTEXT: Text 2 hours
txtTwoHours=2 hodiny
#XTEXT: Text 3 hours
txtThreeHours=3 hodiny
#XTEXT: Text 4 hours
txtFourHours=4 hodiny
#XTEXT: Text 6 hours
txtSixHours=6 hodiny
#XTEXT: Text 12 hours
txtTwelveHours=12 hodiny
#XTEXT: Text 1 day
txtOneDay=1 den
#XFLD: Latency label
@autoRestartHead=Automaticky restartovat
#XFLD
@lblConnectionName=Připojení
#XFLD
@lblQualifiedName=Kvalifikovaný název
#XFLD
@lblSpaceName=Název prostoru
#XFLD
@lblLocalSchemaName=Lokální schéma
#XFLD
@lblType=Typ objektu
#XFLD
@lblActivity=Činnost
#XFLD
@lblTableName=Název tabulky
#XFLD
@lblBusinessName=Business název
#XFLD
@lblTechnicalName=Technický název
#XFLD
@lblSpace=Prostor
#XFLD
@lblLabel=Popisek
#XFLD
@lblDataType=Datový typ
#XFLD
@lblDescription=Popis
#XFLD
@lblStorageType=Úložiště
#XFLD
@lblHTTPConnection=Generické připojení HTTP
#XFLD
@lblAPISettings=Generická nastavení HTTP
#XFLD
@header=Hlavičky
#XFLD
@lblInvoke=Vyvolání API
#XFLD
@lblMethod=Metoda
#XFLD
@lblUrl=Základní URL
#XFLD
@lblAPIPath=Cesta k API
#XFLD
@lblMode=Režim
#XFLD
@lblCSRFToken=Vyžádat token CSRF
#XFLD
@lblTokenURL=URL tokenu CSRF
#XFLD
@csrfTokenInfoText=Není-li zadáno, použije se základní URL a cesta API
#XFLD
@lblCSRF=Token CSRF
#XFLD
@lblRequestBody=Text požadavku
#XFLD
@lblFormat=Formát
#XFLD
@lblResponse=Odpověď
#XFLD
@lblId=ID pro načtení stavu
#XFLD
@Id=ID
#XFLD
@lblSuccessIndicator=Znak úspěchu
#XFLD
@lblErrorIndicator=Znak chyby
#XFLD
@lblErrorReason=Důvod chyby
#XFLD
@lblStatus=Status
#XFLD
@lblApiTestRun=Testovací běh API
#XFLD
@lblRunStatus=Status běhu
#XFLD
@lblLastRan=Naposledy spuštěno
#XFLD
@lblTestRun=Testovací běh
#XFLD
@lblDefaultHeader=Standardní pole hlavičky (páry klíčových hodnot)
#XFLD
@lblAdditionalHeader=Další pole hlavičky
#XFLD
@lblKey=Klíč
#XFLD
@lblEditJSON=Upravit JSON
#XFLD
@lblTasks=Úlohy
#XFLD
@lblRESTfullTask=API
#XFLD: add field button text
btnAddField=Přidat pole hlavičky
#XFLD: view Details link
@viewDetails=Zobrazit detaily
#XTOL
tooltipTxt=Více
#XTOL
delete=Odstranit
#XBTN: ok button text
btnOk=OK
#XBTN: cancel button text
btnCancel=Zrušit
#XBTN: save button text
btnSave=Uložit
#XMSG Error: Object has duplicated technical name.
VAL_OBJECT_DUPLICATE_NAME=Objekt ''{0}'' již v úložišti existuje. Zadejte jiný název.
#XMSG: loading message while opening task chain
loadTaskChain=Načítání řetězce úloh...
#model properties
#XFLD
@status_panel=Status běhu
#XFLD
@deploy_status_panel=Status nasazení
#XFLD
@status_lbl=Status
#XFLD
@lblLastExecuted=Poslední běh
#XFLD
@lblNotExecuted=Dosud nespuštěno
#XFLD
@lblNotDeployed=Nenasazeno
#XFLD
errorDetailsTxt=Nebylo možné vyvolat status běhu
#XBTN: Schedule dropdown menu
SCHEDULE=Naplánovat
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Upravit časový plán
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Odstranit časový plán
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Vytvořit časový plán
#XLNK
viewDetails=Zobrazit detaily
#XMSG: error message for reading execution status from backend
backendErrorMsg=Zdá se, že momentálně nejsou zavedena data ze serveru. Pokuste se data znovu vyvolat.
#XFLD: Status text for Completed
@statusCompleted=Dokončeno
#XFLD: Status text for Running
@statusRunning=Probíhá
#XFLD: Status text for Failed
@statusFailed=Neúspěšné
#XFLD: Status text for Stopped
@statusStopped=Zastaveno
#XFLD: Status text for Stopping
@statusStopping=Zastavuje se
#XFLD
@LoaderTitle=Načítání
#XFLD
#XFLD: Deploy status, dataflow has  been deployed / deployment date exists
@deployStatusActive=Nasazeno
@deployStatusRevised=Lokální aktualizace
@deployStatusFailed=Neúspěšné
@deployStatusPending=Nasazování...
@LoaderText=Získávání detailů ze serveru
#XMSG
@msgDetailFetchError=Chyba během získávání detailů ze serveru
#XFLD
@executeError=Chyba
#XFLD
@executeWarning=Upozornění
#XMSG
@executeConfirmDialog=Informace
#XMSG
@executeunsavederror=Uložte váš řetězec úloh před jeho spuštěním.
#XMSG
@executemodifiederror=V řetězci úloh jsou neuložené změny. Uložte je.
#XMSG
@executerunningerror=Řetězec úloh momentálně probíhá. Před spuštěním nového řetězce úloh počkejte, než bude dokončen aktuální běh.
#XMSG
@btnExecuteAnyway=Přesto spustit
#XMSG
@msgExecuteWithValidations=Řetězec úloh má chyby ověření. Spuštění řetězce úloh může vést k chybě.
#XMSG
@msgRunDeployedVersion=Existují změny k nasazení. Bude spuštěna poslední nasazená verze řetězce úloh. Chcete pokračovat?
#XMSG
#XMSG
@navToMonitoring=Otevřít v monitoru řetězce úloh
#XMSG
txtOR=NEBO
#XFLD
@preview=Náhled
#XMSG
txtand=a
#NOTR
@dateFormat=yyyy-MM-dd
#NOTR
@dateTimeFormatFilter=yyyy-MM-dd HH:mm:ss.
#NOTR
@timeFormat=HH:mm:ss
@lblColum=Sloupec
#XFLD
@lblCondition=Podmínka
#XFLD
@lblValue=Hodnota
#XMSG
@msgJsonInvalid=Řetězec úloh nebylo možné uložit, protože jsou chyby v JSON. Zkontrolujte to a vyřešte.
#XTIT
@msgSaveFailTitle=Neplatný JSON.
#XMSG
NOT_CHAINABLE=Objekt ''{0}'' nelze přidat k řetězci úloh.
#XMSG
NOT_CHAINABLE_REMOTETABLE=Objekt ''{0}'': typ replikace se změní.
#XMSG
searchTaskChain=Hledat objekty

#XFLD
@txtTaskChain=Řetězec úloh
#XFLD
@txtRemoteTable=Vzdálená tabulka
#XFLD
@txtRemoveData=Odebrat replikovaná data
#XFLD
@txtRemovePersist=Odebrat perzistentní data
#XFLD
@txtView=Pohled
#XFLD
@txtDataFlow=Datový tok
#XFLD
@txtIL=Inteligentní vyhledání
#XFLD
@txtTransformationFlow=Tok transformace
#XFLD
@txtReplicationFlow=Tok replikace
#XFLD
@txtDeltaLocalTable=Lokální tabulka
#XFLD
@txtBWProcessChain=Řetězec procesů BW
#XFLD
@txtSQLScriptProcedure=Postup skriptu SQL
#XFLD
@txtAPI=API
#XFLD
@txtMerge=Sloučit
#XFLD
@txtOptimize=Optimalizovat
#XFLD
@txtVacuum=Odstranit záznamy

#XFLD
@txtRun=Spustit
#XFLD
@txtPersist=Trvale uložit
#XFLD
@txtReplicate=Replikovat
#XFLD
@txtDelete=Odstranit záznamy s typem změny ‚Odstraněno‘
#XFLD
@txtRunTC=Spustit řetězec úloh
#XFLD
@txtRunBW=Běh řetězce procesů BW
#XFLD
@txtRunSQLScriptProcedure=Běh postupu skriptu SQL

#XFLD
@txtRunDataFlow=Spustit datový tok
#XFLD
@txtPersistView=Trvale uložit pohled
#XFLD
@txtReplicateTable=Replikovat tabulku
#XFLD
@txtRunIL=Spustit inteligentní vyhledávání
#XFLD
@txtRunTF=Spustit tok transformace
#XFLD
@txtRunRF=Spustit tok replikace
#XFLD
@txtRemoveReplicatedData=Odebrat replikovaná data
#XFLD
@txtRemovePersistedData=Odebrat perzistentní data
#XFLD
@txtMergeData=Sloučit
#XFLD
@txtOptimizeData=Optimalizovat
#XFLD
@txtVacuumData=Odstranit záznamy
#XFLD
@txtRunAPI=Spustit API

#XFLD storage type text
hdlfStorage=Soubor

@statusNew=Nenasazeno
@statusActive=Nasazeno
@statusRevised=Lokální aktualizace
@statusPending=Nasazování...
@statusChangesToDeploy=Změny k nasazení
@statusDesignTimeError=Chyba doby návrhu
@statusRunTimeError=Chyba doby běhu

#XTIT
txtNodes=Objekty v řetězci úloh ({0})
#XBTN
@deleteNodes=Odstranit

#XMSG
@txtDropDataToDiagram=Přetáhnout objekty do diagramu.
#XMSG
@noData=Žádné objekty

#XFLD
@txtTaskPosition=Pozice úlohy

#input parameters and variables
#XFLD
lblInputParameters=Vstupní parametry
#XFLD
ip_name=Název
#XFLD
ip_value=Hodnota
#XTEXT
@noObjectsFound=Žádné objekty nenalezeny

#XMSG
@msgExecuteSuccess=Běh řetězce úloh spuštěn.
#XMSG
@msgExecuteFail=Nezdařilo se spustit řetězec úloh.
#XMSG
@msgDeployAndRunSuccess=Nasazení a běh řetězce úloh spuštěn.
#XMSG
@msgDeployAndRunFail=Nezdařilo se nasadit a spustit řetězec úloh.
#XMSG
@titleExecuteBusy=Vyčkejte.
#XMSG
@msgExecuteBusy=Připravujeme vaše data na spuštění běhu řetězce úloh.
#XMSG
@msgAPITestRunSuccess=Test API byl spuštěn.
#XMSG
@msgAPIExecuteBusy=Připravujeme vaše data na spuštění testu API.

#XTOL
txtOpenInEditor=Otevřít v editoru
#XTOL
txtPreviewData=Náhled dat

#datapreview
#XMSG
@msgDataPreviewNotSupp=Pro tento objekt není dostupný náhled dat.

#XMSG Error: empty model
VAL_MODEL_EMPTY=Zdá se, že je váš model prázdný. Přidejte nějaké objekty.
#XMSG Error: deploy model
@msgDeployBeforeRun=Před spuštěním řetězce úloh ho musíte nasadit.
#BTN: close dialog
btnClose=Zavřít

#XMSG Object is not Deployed
VAL_NOT_DEPLOYED=Pro pokračování s řetězcem úloh musí být nasazen objekt ''{0}''.
#XMSG Object is having runtime error
VAL_RUNTIME_ERROR=Objekt ''{0}'' vrací chybu doby běhu. Zkontrolujte objekt.
#XMSG Object is having design time error
VAL_DESIGNTIME_ERROR=Objekt ''{0}'' vrací chybu doby návrhu. Zkontrolujte objekt.
#XMSG Procedure Object has been deleted
VAL_DELETED_PROCEDURE=Následující postupy byly odstraněny: {0}
#XMSG New Parameter added to Procedure object
VAL_NEW_PARAMETER_ADDED=Byly přidány nové parametry k postupu "{1}": "{0}". Nasaďte znovu řetězec úloh.
#XMSG Parameter removed from Procedure object
VAL_PARAMETER_DELETED=Byly odebrány parametry z postupu "{1}": "{0}". Nasaďte znovu řetězec úloh.
#XMSG Parameter data type changed in Procedure object
VAL_PARAMETER_DATATYPE_MODIFIED=Datový typ parametru "{0}" změněn z "{1}" na "{2}" v postupu "{3}".
#XMSG Parameter length changed of specific datatype in Procedure object
VAL_PARAMETER_LENGTH_MODIFIED=Délka parametru "{0}" změněna z "{1}" na "{2}" v postupu "{3}".
#XMSG Parameter precision changed of Decimal datatype in Procedure object
VAL_PARAMETER_PRECISION_MODIFIED=Přesnost parametru "{0}" změněna z "{1}" na "{2}" v postupu "{3}".
#XMSG Parameter scale changed of Decimal datatype in Procedure object
VAL_PARAMETER_SCALE_MODIFIED=Stupnice parametru "{0}" změněna z "{1}" na "{2}" v postupu "{3}".
#XMSG Parameter value not provided in Procedure object
VAL_SQL_SCRIPT_PROCEDURE_PARAMETER_VALUE_MISSING=Nezadali jste hodnoty pro vstupní parametry požadované pro postup "{0}":  "{1}".
#XMSG Remote Table activity changes to Remove Replicated Data
VAL_REMOVE_REPLICATED_DATA=Příští běh řetězce úloh změní typ přístupu k datům vzdálené tabulky a data již nebudou aktualizována v reálném čase.
#XMSG Remote Table activity changes
REPLICATION_TYPE_CHANGED=Objekt "{0}": Typ replikace se změní.

#XFLD
@lblStartNode=Počáteční uzel
#XFLD
@lblEndNode=Koncový uzel
#XFLD
@linkTo={0} až {1}
#XTOL
@txtViewDetails=Zobrazit detaily

#XTOL
txtOpenImpactLineage=Analýza účinku a rodokmenu dat
#XFLD
@emailNotifications=E-mailové notifikace
#XFLD
@txtReset=Resetovat
#XFLD
@emailMsgWarning=Bude odeslána standardní šablona e-mailu, když je e-mailová zpráva prázdná
#XFLD
@notificationSettings=Nastavení oznamování
#XFLD
@recipientEmailAddr=E-mailová adresa příjemce
#XFLD
@emailSubject=Předmět e-mailu
@emailSubjectText=Řetězec úloh <TASKCHAIN_NAME> dokončen se statusem <STATUS>
#XFLD
@emailMessage=E-mailová zpráva
@emailMessageText=Vážený uživateli,\n\n Tímto vás informuji, že běh řetězce úloh:<TASKCHAIN_NAME> spuštěný v <START_TIME> byl dokončen se statusem <STATUS>. Provedení skončilo v <END_TIME>.\n\nDetaily:\nProstor:<SPACE_NAME>\nChyba:<ERROR_MESSAGE>
@selectEmailRecipientDialogTitle=Vyberte e-mailovou adresu příjemce
@tenantMembers=Členové tenanta
@others=Ostatní({0})
@selectedEmailAddress=Vybraní příjemci
@add=Přidat
@placeholder=Zástupce
@description=Popis
@copyText=Zkopírovat text
@taskchainDetailsPlaceholder=Zástupci pro detaily řetězce úloh
@placeholderCopied=Zástupce je zkopírován
@invalidEmailInfo=Zadejte správnou e-mailovou adresu
@maxMembersAlreadyAdded=Již jste přidali maximální počet 20 členů
@enterEmailAddress=Zadejte e-mailovou adresu uživatele
@inCorrectPlaceHolder={0} není očekávaný zástupce v textu e-mailu.
@nsOFF=Neodesílejte žádná oznámení
@nsFAILED=E-mailovou notifikaci odešlete, jen když byl běh dokončen s chybou
@nsCOMPLETED=E-mailovou notifikaci odešlete, jen když byl běh úspěšně dokončen
@nsANY=Odešlete e-mail, když byl běh dokončen
@phStatus=Status řetězce úloh - ÚSPĚŠNÝ/NEÚSPĚŠNÝ
@phTaskChainTName=Technický název řetězce úloh
@phTaskChainBName=Business název řetězce úloh
@phLogId=ID protokolu běhu
@phUser=Uživatel, který spustil řetězec úloh
@phLogUILink=Odkaz na zobrazení protokolu řetězce úloh
@phStartTime=Čas zahájení běhu
@phEndTime=Čas ukončení běhu
@phErrMsg=První chybová zpráva v protokolu úlohy. Protokol je prázdný v případě ÚSPĚCHU
@phSpaceName=Technický název prostoru
@emailFormatError=Neplatný formát e-mailu
@emailFormatErrorInListText=Neplatný formát e-mailu zadaný do seznamu členů nepatřících k tenantovi
@emailSubjectTemplateText=Oznámení pro řetězec úloh: $$taskChainName$$ - Prostor: $$spaceId$$ - Status: $$status$$
@emailMessageTemplateText=Dobrý den,\n\n Váš řetězec úloh označený $$taskChainName$$ skončil se statusem $$status$$. \n Zde je několik dalších detailů o řetězci úloh:\n - Technický název řetězce úloh: $$taskChainName$$ \n ID protokolu běhu řetězce úloh: $$logId$$ \n - Uživatel, který spustil řetězec úloh: $$user$$ \n - Odkaz na zobrazení protokolu řetězce úloh: $$uiLink$$ \n -  Čas zahájení běhu řetězce úloh: $$startTime$$ \n - Čas ukončení běhu řetězce úloh: $$endTime$$ \n - Název prostoru: $$spaceId$$ \n
@deleteEmailRecepient=Odstranit příjemce
@emailInputDisabledText=Nasaďte řetězec úloh pro přidání příjemců e-mailu.
@tenantOwnerDomainMatchErrorText=E-mailová adresa domény neodpovídá doméně vlastníka tenanta: {0}
@totalEmailIdLimitInfoText=Můžete vybrat až 20 příjemců e-mailu včetně uživatelů členů tenanta a jiných příjemců.
@emailDomainInfoText=Akceptovány jsou pouze e-mailové adresy s doménou: {0}.
@duplicateEmailErrorText=V seznamu jsou duplicitní příjemci e-mailů.

#XFLD Zorder Title
@txtZorderTitle=Sloupce Z-objednávky Apache Spark

#XFLD Zorder NoColumn
@txtZorderNoColumn=Žádné sloupce Z-objednávky Apache Spark nenalezeny

#XFLD Zorder Column PrimaryKey
@txtZorderColumnPrimaryKey=Primární klíč

#XFLD
@lblOperators=Operátory
addNewSelector=Přidat jako novou úlohu
parallelSelector=Přidat jako paralelní úlohu
replaceSelector=Nahradit existující úlohu
addparallelbranch=Přidat jako paralelní větev
addplaceholder=Přidat zástupce
addALLOperation=Operátor ALL
addOROperation=Operátor ANY
addplaceholdertocanvas=Přidat zástupce na plátno
addplaceholderonselected=Přidat zástupce za vybranou úlohu
#XFLD: Add Parallel Branch to Selected Task
addparallelbranchonselected=Přidat paralelní větev za vybranou úlohu
addOperator=Přidat operátor
txtAdd=Přidat
txtPlaceHolderText=Přetáhnout úlohu sem
@lblLayout=Rozložení

#XMSG
VAL_UNCONNECTED_TASK=Úloha ''{0}'' není připojena k řetězci úloh.
#XMSG
VAL_TASK_SHOULD_HAVE_ONE_INCOMING_LINK=Úloha ''{0}'' by měla mít jen jeden vstupní odkaz.
#XMSG
VAL_TASK_SHOULD_HAVE_INCOMING_LINK=Úloha ''{0}'' by měla mít jeden vstupní odkaz.
#XMSG
VAL_UNCONNECTED_OPERATOR=Operátor ''{0}'' není připojen k řetězci úloh.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ATLEAST_TWO_INCOMING_LINK=Operátor ''{0}'' by měl mít alespoň dva vstupní odkazy.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ONE_OUT_LINK=Operátor ''{0}'' by měl mít alespoň jeden výstupní odkaz.
#XMSG
VAL_CIRCULAR_REFERENCE_EXITS=V řetězci úloha ''{0}''‘ existuje kruhová smyčka.
#XMSG
VAL_UNCONNECTED_BRANCH=Objekt/větev ''{0}'' není připojen(a) k řetězci úloh.
#XMSG
VAL_TASK_TRIGGERED_PARALLEL_MORE_THAN_ONE_TIME=Úloha ''{0}'' je paralelně připojena více než jednou. Odeberte duplikáty, abyste mohli pokračovat.


txtBegin=Začít
txtNodesInLink=Zahrnuté objekty
#XTOL Tooltip for a context button on diagram
openInNewTab=Otevřít jako novou záložku
#XTOL Tooltip for a context button on diagram
txtConnectToTaskOperator=Přetáhnout k připojení
@emailUpdateError=Chyba aktualizace seznamu e-mailových notifikací

#XMSG
noTeamPrivilegeTxt=Nemáte oprávnění k zobrazení seznamu členů tenanta. Chcete-li přidat příjemce e-mailu manuálně, použijte záložku Jiné.

#XFLD Package
@txtPackage=Paket

#XMSG warning for repository package assignment changed. {1} - package name
VAL_PACKAGE_CHANGED=Přiřadili jste tento objekt k paketu ''{1}''. Kliknutím na Uložit potvrdíte a ověříte tuto změnu. Uvědomte si, že toto přiřazení k paketu již nelze po uložení v tomto editoru vrátit.
#XMSG warning for repository package dependency issue.
MISSING_DEPENDENCY=Závislosti objektu ''{0}'' nelze vyřešit v kontextu paketu ''{1}''.

#Warning messages for the folders in the "Others" tab of task chain editor
#XMSG generic warning if getting the objects in a particular folder fails with unhandled errors
getNonRepoArtefactTypeDescendantsError=Byl problém s načítáním objektů do vámi vybrané složky.
#BW Process Chains folder related warning messages
#XMSG warning if user does not have the necessary permission related to the SAP BW Bridge space
missingPrivilegeForBridgeSpaceError=Nemáte potřebná k oprávnění k zobrazení nebo zahrnutí řetězce procesů BW do řetězce úloh.
#XMSG warning if there was a problem retrieving the BW process chains due to bridge space membership, bridge connection issues or other errors
generalBridgeSpaceError=Byl problém s načítáním řetězců procesů BW.
#XMSG warning if the BW API for retrieving the BW process chains from the SAP BW Bridge tenant fails
bwApiClientError=Byl problém s načítáním řetězců procesů BW z tenanta SAP BW Bridge.
#XMSG warning if BW process chains list is empty
bwApiEmptyListError=V tenantu SAP BW Bridge nebyly nalezeny žádné řetězce procesů BW.
#XMSG: Task log message for invalid OIDC configuration in the SAP BW Bridge tenant
bwBridgeOIDCConfigStatusInvalid=Autetizace OpenID není konfigurována pro tohoto tenanta. Pro konfiguraci autentizace OpenID v tenantu SAP BW Bridge použijte clientID "{0}" a tokenURL "{1}", jak je popsáno v pokynu SAP 3536298.
#XMSG BW Process Chain Object has been deleted
VAL_DELETED_BW=Následující řetězce procesů byly možná odstraněny z tenanta SAP Bridge: {0}
#BW SQL Script Procedures folder related warning messages
#XFLD waring if the Procedure list is empty
procedureAPIEmptyList=Žádné postupy nebyly vytvořeny nebo schématu Open SQL nebylo uděleno oprávnění EXECUTE.
#Change digram orientations
changeOrientations=Změnit orientace


# placeholder for the API Path
apiPath=Například: /job/v1
# placeholder for the status API Path
statusAPIPath=Například: /job/v1/{id}/status
# valueStateText for the API Path
apiPathRequired=Cesta API je povinná
#placeholder for the CSRF Token URL
csrfTokenURL=Podporováno je pouze HTTPS
# Response Type 1
statusCode=Načíst výsledek ze stavového kódu HTTP
# Response Type 2
locationHeader=Načíst výsledek ze stavového kódu HTTP umístění hlavičky
# Response Type 3
responseBody=Načíst výsledek ze stavového kódu HTTP a textu odpovědi
# placeholder for ID
idPlaceholder=Zadejte cestu JSON
# placeholder for indicator value
indicatorValue=Zadejte hodnotu
# Placeholder for key
keyPlaceholder=Zadejte klíč
# Error message for missing key
KeyRequired=Klíč je povinný
# Error message for invalid key format
invalidKeyFormat=Vámi zadaná hlavička je nepřípustná. Platné hlavičky jsou:<ul><li>"prefer"</li><li>Hlavičky začínající na "x-", kromě "x-forwarded-host"</li><li>Hlavičky, které obsahují alfanumerické znaky, "-"nebo "_"</li></ul>
# Error message for missing value
valueRequired=Hodnota je povinná
# Error message for invalid characters in value
invalidValueCharacters=Hlavička obsahuje neplatné znaky. Přípustné zvláštní znaky jsou:\t ";", ":", "-", "_", ",", "?", "/" a "*"
# Validation message for invoke api path
apiPathValidation=Zadejte platnou cestu API, například: /job/v1
# Validation message for JSON path
jsonPathValidation=Zadejte platnou cestu JSON
# Validation message for success/error indicator
indicatorValueValidation=Hodnota indikátoru musí začínat alfanumerickým znakem a může obsahovat následující zvláštní zanaky:\t "-" a "_"
# Error message for JSON path
jsonPathRequired=Cesta JSON je povinná
# Error message for invalid API Technical Name
invalidTechnicalName=Technický název obsahuje neplatné znaky
# Error message for empty Technical Name
emptyTechnicalName=Technický název je povinný
# Tooltip for codeEditor dialog
codeEditorTooltip=Otevřít okno editace JSON
# Status Api path validation message
validationStatusAPIPath=Zadejte platnou cestu API, například: /job/v1/{id}/status
# CSRF token URL validation message
validationCSRFTokenURL=URL tokenu CSRF musí začínat na 'https://' a musí být platným URL
# Select a connection
selectConnection=Vybrat připojení
# Validation message for connection item error
connectionNotReplicated=Připojení je aktuálně neplatné pro spouštění úloh API. Otevřete aplikaci "Připojení" a znovu zadejte své přihlašovací údaje, abyste opravili připojení HTTP
# Validation message for API task properties wrong value or missing
VAL_REST_API_PROPERTIES_MISSING=Úloha API "{0}" má nesprávné nebo chybějící hodnoty pro následující vlastnosti: {1}
# Validation message for API connection api is throwing some error or selected connection has some warning
VAL_REST_API_CONNECTION=Úloha API "{0}" má problém s připojením HTTP. Otevřete aplikaci "Připojení" a zkontrolujte připojení
# Validation message for API task properties when delected connection
VAL_REST_API_CONNECTION_DELETED=Úloha API "{0}" má odstraněnou podmínku "{1}" 
# Validation message for API task properties when connection cannot be consumed in the API task
VAL_REST_API_CONNECTION_CONSUMPTION_WARNING=Úloha API "{0}" má připojení "{1}", které nelze používat pro spuštění úloh API. Otevřete aplikaci "Připojení" a znovu zadejte své přihlašovací údaje, abyste zřídili připojení pro úlohy API
# Information message if synchrnous mode is selected in the API Invocation then we don't need status information
modeInfoText=V synchronním režimu není stavový panel zobrazen pro vyvolání API
# validation dialog button for Run API Test
saveAnyway=Přesto uložit
# validation message for technical name
technicalNameValidation=Technický název musí být v rámci řetězce úloh jedinečný. Zvolte jiný technický název
# Connection error message
getHttpConnectionsError=Nezdařilo se načíst připojení HTTP
# Msg for the API test run when the Task chain is not saved
taskChainNotSaved=Před spuštěním testu API musí být uložen řetězec úloh
# Msg failed to run API test run
@failedToRunAPI=Nezdařilo se spustit test API
# Msg for the API test run when its already in running state
apiTaskRunning=Testovací běh API již probíhá. Chcete spustit nový testovací běh?

topToBtm=Shora dolů
leftToRight=Zleva doprava

#XTIT Apache Spark Application Settings
txtHeaderApacheSettings=Nastavení aplikace Apache Spark
#XFLD Use Default
txtUseSpaceDefault=Použít standardní
#XFLD Application
txtApplication=Aplikace
#XFLD Define new settings for this Task
txtNewSettings=Definovat nová nastavení pro tuto úlohu

#XFLD Use Default
txtUseDefault=Použít standardní




