#XTOL Undo
@undo=Fortryd
#XTOL Redo
@redo=<PERSON><PERSON><PERSON> fortryd
#XTOL Delete Selected Symbol
@deleteNode=Slet valgt symbol
#XTOL Zoom to Fit
@zoomToFit=Tilpas til indhold
#XTOL Auto Layout
@autoLayout=Automatisk layout
#XMSG
@welcomeText=Træk og slip objekter fra venstre panel til dette lærred.
#XMSG
@txtNoData=Det ser ud til, at du endnu ikke har tilføjet noget objekt.
#XMSG
VAL_ENTER_VALID_STRING_GEN=Indtast en gyldig streng, der er kortere end eller lig med {0}.
#XMSG
@noParametersMsg=Denne procedure har ingen inputparametre.
#XMSG
@ip_enterValueMsg="{0}" (Kør SQL Script-procedure) har inputparametre. Du kan indstille en værdi for hver af dem.
#XTOL
@validateModel=Valideringsmeddelelser
#XTOL
@hierarchy=Hierarki
#XTOL
@columnCount=Antal kolonner
#XFLD
@yes=Ja
#XFLD
@no=Nej
#XTIT Save Dialog param
@modelNameTaskChain=Opgavekæde
#properties panel
@lblPropertyTitle=Egenskaber
#XFLD
@lblGeneral=Generelt
#XFLD : Setting
@lblSetting=Indstillinger
#XTXT: Delta Enabled Local Table Deletion
txtDeltaTableDeletion=Slet alle fuldt behandlede poster med ændringstype 'Slettet', der er ældre end
#XTXT: Delta Enabled Local Table Deletion Input field description
desDeltaTableDeletion=Dage
#XFLD: Data Activation label
@lblDataActivation=Dataaktivering
#XFLD: Latency label
@latency=Ventetid
#XTEXT: Text for Latency dropdown
txtLatencyDefault=Standard
#XTEXT: Text 1 hour
txtOneHour=1 time
#XTEXT: Text 2 hours
txtTwoHours=2 timer
#XTEXT: Text 3 hours
txtThreeHours=3 timer
#XTEXT: Text 4 hours
txtFourHours=4 timer
#XTEXT: Text 6 hours
txtSixHours=6 timer
#XTEXT: Text 12 hours
txtTwelveHours=12 timer
#XTEXT: Text 1 day
txtOneDay=1 dag
#XFLD: Latency label
@autoRestartHead=Automatisk genstart
#XFLD
@lblConnectionName=Forbindelse
#XFLD
@lblQualifiedName=Kvalificeret navn
#XFLD
@lblSpaceName=Navn på space
#XFLD
@lblLocalSchemaName=Lokalt skema
#XFLD
@lblType=Objekttype
#XFLD
@lblActivity=Aktivitet
#XFLD
@lblTableName=Tabelnavn
#XFLD
@lblBusinessName=Forretningsnavn
#XFLD
@lblTechnicalName=Teknisk navn
#XFLD
@lblSpace=Space
#XFLD
@lblLabel=Etiket
#XFLD
@lblDataType=Datatype
#XFLD
@lblDescription=Beskrivelse
#XFLD
@lblStorageType=Lager
#XFLD
@lblHTTPConnection=Generisk HTTP-forbindelse
#XFLD
@lblAPISettings=Generiske API-indstillinger
#XFLD
@header=Sidehoveder
#XFLD
@lblInvoke=Kald af API
#XFLD
@lblMethod=Metode
#XFLD
@lblUrl=Basis-URL
#XFLD
@lblAPIPath=API-sti
#XFLD
@lblMode=Tilstand
#XFLD
@lblCSRFToken=Kræv CSRF-token
#XFLD
@lblTokenURL=CSRF-token-URL
#XFLD
@csrfTokenInfoText=Hvis det ikke er indtastet, bruges basis-URL'en og API-stien
#XFLD
@lblCSRF=CSRF-token
#XFLD
@lblRequestBody=Anmodningstekst
#XFLD
@lblFormat=Format
#XFLD
@lblResponse=Svar
#XFLD
@lblId=Id til at hente status
#XFLD
@Id=Id
#XFLD
@lblSuccessIndicator=Succesindikator
#XFLD
@lblErrorIndicator=Fejlindikator
#XFLD
@lblErrorReason=Årsag til fejl
#XFLD
@lblStatus=Status
#XFLD
@lblApiTestRun=API-testkørsel
#XFLD
@lblRunStatus=Kørselsstatus
#XFLD
@lblLastRan=Senest kørt den
#XFLD
@lblTestRun=Testkørsel
#XFLD
@lblDefaultHeader=Standardfelter på sidehoved (nøgleværdipar)
#XFLD
@lblAdditionalHeader=Ekstra sidehovedfelt
#XFLD
@lblKey=Nøgle
#XFLD
@lblEditJSON=Rediger JSON
#XFLD
@lblTasks=Opgaver
#XFLD
@lblRESTfullTask=API
#XFLD: add field button text
btnAddField=Tilføj sidehovedfelt
#XFLD: view Details link
@viewDetails=Vis detaljer
#XTOL
tooltipTxt=Mere
#XTOL
delete=Slet
#XBTN: ok button text
btnOk=OK
#XBTN: cancel button text
btnCancel=Annuller
#XBTN: save button text
btnSave=Gem
#XMSG Error: Object has duplicated technical name.
VAL_OBJECT_DUPLICATE_NAME=Objekt ''{0}'' findes allerede i lageret. Indtast venligst et andet navn.
#XMSG: loading message while opening task chain
loadTaskChain=Indlæser opgavekæden...
#model properties
#XFLD
@status_panel=Kørselsstatus
#XFLD
@deploy_status_panel=Implementeringsstatus
#XFLD
@status_lbl=Status
#XFLD
@lblLastExecuted=Sidste kørsel
#XFLD
@lblNotExecuted=Ikke kørt endnu
#XFLD
@lblNotDeployed=Ikke implementeret
#XFLD
errorDetailsTxt=Kunne ikke hente kørselsstatus
#XBTN: Schedule dropdown menu
SCHEDULE=Planlæg
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Rediger tidsplan
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Slet tidsplan
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Opret tidsplan
#XLNK
viewDetails=Vis detaljer
#XMSG: error message for reading execution status from backend
backendErrorMsg=Det ser ud til, at data ikke indlæses fra serveren i øjeblikket. Forsøg at hente data igen.
#XFLD: Status text for Completed
@statusCompleted=Fuldført
#XFLD: Status text for Running
@statusRunning=Kører
#XFLD: Status text for Failed
@statusFailed=Mislykkedes
#XFLD: Status text for Stopped
@statusStopped=Stoppet
#XFLD: Status text for Stopping
@statusStopping=Stopper
#XFLD
@LoaderTitle=Indlæser
#XFLD
#XFLD: Deploy status, dataflow has  been deployed / deployment date exists
@deployStatusActive=Implementeret
@deployStatusRevised=Lokale opdateringer
@deployStatusFailed=Mislykkedes
@deployStatusPending=Implementerer...
@LoaderText=Henter detaljer fra serveren
#XMSG
@msgDetailFetchError=Fejl ved hentning af detaljer fra serveren
#XFLD
@executeError=Fejl
#XFLD
@executeWarning=Advarsel
#XMSG
@executeConfirmDialog=Info
#XMSG
@executeunsavederror=Gem din opgavekæde, før du kører den.
#XMSG
@executemodifiederror=Der er ændringer i opgavekæden, der ikke er gemt. Gem dem venligst.
#XMSG
@executerunningerror=Opgavekæden kører i øjeblikket. Vent, indtil den aktuelle kørsel er fuldført, før du starter en ny.
#XMSG
@btnExecuteAnyway=Kør alligevel
#XMSG
@msgExecuteWithValidations=Der er valideringsfejl i opgavekæden. Hvis opgavekæden køres, kan det medføre fejl.
#XMSG
@msgRunDeployedVersion=Der er ændringer, der skal implementeres. Den senest implementerede version af opgavekæden køres. Vil du fortsætte?
#XMSG
#XMSG
@navToMonitoring=Åbn i monitor for opgavekæde
#XMSG
txtOR=OR
#XFLD
@preview=Forhåndsvisning
#XMSG
txtand=AND
#NOTR
@dateFormat=yyyy-MM-dd
#NOTR
@dateTimeFormatFilter=yyyy-MM-dd HH:mm:ss.
#NOTR
@timeFormat=HH:mm:ss
@lblColum=Kolonne
#XFLD
@lblCondition=Betingelse
#XFLD
@lblValue=Værdi
#XMSG
@msgJsonInvalid=Opgavekæden kunne ikke gemmes, da der er fejl i JSON-filen. Kontroller venligst, og løs problemet.
#XTIT
@msgSaveFailTitle=Ugyldig JSON.
#XMSG
NOT_CHAINABLE=Objektet ''{0}'' kan ikke føjes til opgavekæden.
#XMSG
NOT_CHAINABLE_REMOTETABLE=Objektet ''{0}'': Replikeringstype ændres.
#XMSG
searchTaskChain=Søg objekter

#XFLD
@txtTaskChain=Opgavekæde
#XFLD
@txtRemoteTable=Ekstern tabel
#XFLD
@txtRemoveData=Fjern replikerede data
#XFLD
@txtRemovePersist=Fjern persisterede data
#XFLD
@txtView=Vis
#XFLD
@txtDataFlow=Dataflow
#XFLD
@txtIL=Intelligent opslag
#XFLD
@txtTransformationFlow=Transformationsflow
#XFLD
@txtReplicationFlow=Replikeringsflow
#XFLD
@txtDeltaLocalTable=Lokal tabel
#XFLD
@txtBWProcessChain=BW-proceskæde
#XFLD
@txtSQLScriptProcedure=SQL Script-procedure
#XFLD
@txtAPI=API
#XFLD
@txtMerge=Flet
#XFLD
@txtOptimize=Optimer
#XFLD
@txtVacuum=Slet dataposter

#XFLD
@txtRun=Kør
#XFLD
@txtPersist=Persister
#XFLD
@txtReplicate=Repliker
#XFLD
@txtDelete=Slet poster med ændringstypen "Slettet"
#XFLD
@txtRunTC=Kør opgavekæde
#XFLD
@txtRunBW=Kør BW-proceskæde
#XFLD
@txtRunSQLScriptProcedure=Kør SQL Script-procedure

#XFLD
@txtRunDataFlow=Udfør dataflow
#XFLD
@txtPersistView=Persister visning
#XFLD
@txtReplicateTable=Repliker tabel
#XFLD
@txtRunIL=Kør intelligent opslag
#XFLD
@txtRunTF=Kør transformationsflow
#XFLD
@txtRunRF=Kør replikeringsflow
#XFLD
@txtRemoveReplicatedData=Fjern replikerede data
#XFLD
@txtRemovePersistedData=Fjern persisterede data
#XFLD
@txtMergeData=Flet
#XFLD
@txtOptimizeData=Optimer
#XFLD
@txtVacuumData=Slet dataposter
#XFLD
@txtRunAPI=Kør API

#XFLD storage type text
hdlfStorage=Fil

@statusNew=Ikke implementeret
@statusActive=Implementeret
@statusRevised=Lokale opdateringer
@statusPending=Implementerer...
@statusChangesToDeploy=Ændringer, der skal implementeres
@statusDesignTimeError=Designtidsfejl
@statusRunTimeError=Kørselstidsfejl

#XTIT
txtNodes=Objekter i opgavekæde ({0})
#XBTN
@deleteNodes=Slet

#XMSG
@txtDropDataToDiagram=Træk og slip objekter til diagrammet.
#XMSG
@noData=Ingen objekter

#XFLD
@txtTaskPosition=Opgaveposition

#input parameters and variables
#XFLD
lblInputParameters=Inputparametre
#XFLD
ip_name=Navn
#XFLD
ip_value=Værdi
#XTEXT
@noObjectsFound=Fandt ingen objekter

#XMSG
@msgExecuteSuccess=Kørsel af opgavekæden er startet.
#XMSG
@msgExecuteFail=Kørsel af opgavekæden mislykkedes.
#XMSG
@msgDeployAndRunSuccess=Opgavekædens implementering og kørsel er startet.
#XMSG
@msgDeployAndRunFail=Kunne ikke implementere og køre opgavekæden.
#XMSG
@titleExecuteBusy=Vent
#XMSG
@msgExecuteBusy=Vi forbereder dine data for at starte kørsel af opgavekæden.
#XMSG
@msgAPITestRunSuccess=API-testkørslen er startet.
#XMSG
@msgAPIExecuteBusy=Vi forbereder dine data for at starte API-testkørslen.

#XTOL
txtOpenInEditor=Åbn i editor
#XTOL
txtPreviewData=Forhåndsvis data

#datapreview
#XMSG
@msgDataPreviewNotSupp=Forhåndsvisning af data er ikke tilgængelig for dette objekt.

#XMSG Error: empty model
VAL_MODEL_EMPTY=Din model ser ud til at være tom. Tilføj venligst nogle objekter.
#XMSG Error: deploy model
@msgDeployBeforeRun=Du skal implementere opgavekæden, før du kører den.
#BTN: close dialog
btnClose=Luk

#XMSG Object is not Deployed
VAL_NOT_DEPLOYED=Objektet ''{0}'' skal være implementeret, før du kan fortsætte med opgavekæden.
#XMSG Object is having runtime error
VAL_RUNTIME_ERROR=Objekt ''{0}'' returnerer en kørselstidsfejl. Kontroller objektet.
#XMSG Object is having design time error
VAL_DESIGNTIME_ERROR=Objekt ''{0}'' returnerer en designtidsfejl. Kontroller objektet.
#XMSG Procedure Object has been deleted
VAL_DELETED_PROCEDURE=Følgende procedurer blev slettet: {0}
#XMSG New Parameter added to Procedure object
VAL_NEW_PARAMETER_ADDED=Nye parametre tilføjet til proceduren "{1}": "{0}". Implementer opgavekæden igen.
#XMSG Parameter removed from Procedure object
VAL_PARAMETER_DELETED=Parametre fjernet fra proceduren "{1}": "{0}". Implementer opgavekæden igen.
#XMSG Parameter data type changed in Procedure object
VAL_PARAMETER_DATATYPE_MODIFIED=Datatype for parameter "{0}" ændret fra "{1}" til "{2}" i procedure "{3}".
#XMSG Parameter length changed of specific datatype in Procedure object
VAL_PARAMETER_LENGTH_MODIFIED=Længde for parameter "{0}" ændret fra "{1}" til "{2}" i procedure "{3}".
#XMSG Parameter precision changed of Decimal datatype in Procedure object
VAL_PARAMETER_PRECISION_MODIFIED=Præcision for parameter "{0}" ændret fra "{1}" til "{2}" i procedure "{3}".
#XMSG Parameter scale changed of Decimal datatype in Procedure object
VAL_PARAMETER_SCALE_MODIFIED=Skala for parameter "{0}" ændret fra "{1}" til "{2}" i procedure "{3}".
#XMSG Parameter value not provided in Procedure object
VAL_SQL_SCRIPT_PROCEDURE_PARAMETER_VALUE_MISSING=Du har ikke indtastet værdier for inputparametre, der kræves til proceduren "{0}": "{1}"
#XMSG Remote Table activity changes to Remove Replicated Data
VAL_REMOVE_REPLICATED_DATA=Den næste kørsel af en opgavekæde vil ændre dataadgangstypen, og data uploades ikke længere i realtid.
#XMSG Remote Table activity changes
REPLICATION_TYPE_CHANGED=Objekt "{0}": Replikeringstype ændres.

#XFLD
@lblStartNode=Startknude
#XFLD
@lblEndNode=Slutknude
#XFLD
@linkTo={0} til {1}
#XTOL
@txtViewDetails=Vis detaljer

#XTOL
txtOpenImpactLineage=Konsekvens- og oprindelsesanalyse
#XFLD
@emailNotifications=E-mailnotifikationer
#XFLD
@txtReset=Nulstil
#XFLD
@emailMsgWarning=Standardskabelonen for e-mail sendes, når e-mailmeddelelsen er tom.
#XFLD
@notificationSettings=Notifikationsindstillinger
#XFLD
@recipientEmailAddr=Modtagers e-mailadresse
#XFLD
@emailSubject=Emne i e-mail
@emailSubjectText=Opgavekæden <TASKCHAIN_NAME> afsluttet med status <STATUS>
#XFLD
@emailMessage=E-mailbesked
@emailMessageText=Kære bruger\n\n Dette er for at underrette dig om, at opgavekæden:<TASKCHAIN_NAME> med start <START_TIME> er afsluttet med status <STATUS>. Udførelsen sluttede <END_TIME>.\n\nDetails:\nSpace:<SPACE_NAME>\nError:<ERROR_MESSAGE>
@selectEmailRecipientDialogTitle=Vælg modtagers e-mailadresse
@tenantMembers=Lejermedlemmer
@others=Andre ({0})
@selectedEmailAddress=Valgte modtagere
@add=Tilføj
@placeholder=Pladsholder
@description=Beskrivelse
@copyText=Kopiér tekst
@taskchainDetailsPlaceholder=Pladsholdere til opgavekædedetaljer
@placeholderCopied=Pladsholder er kopieret
@invalidEmailInfo=Indtast korrekt e-mailadresse
@maxMembersAlreadyAdded=Du har allerede indtastet de maksimale 20 medlemmer.
@enterEmailAddress=Indtast brugerens e-mailadresse
@inCorrectPlaceHolder={0} er ikke en forventet pladsholder i e-mailteksten.
@nsOFF=Send ikke notifikationer
@nsFAILED=Send kun e-mailnotifikation, når kørslen er udført med fejl
@nsCOMPLETED=Send kun e-mailnotifikation, når kørslen er udført uden fejl
@nsANY=Send e-mail, når kørslen er udført
@phStatus=Status for opgavekæden - UDFØRT/MISLYKKET
@phTaskChainTName=Teknisk navn på opgavekæden
@phTaskChainBName=Forretningsnavn på opgavekæden
@phLogId=Log-id for kørslen
@phUser=Bruger, som kører opgavekæden
@phLogUILink=Link til logvisning for opgavekæden
@phStartTime=Starttid for kørslen
@phEndTime=Sluttid for kørslen
@phErrMsg=Første fejlmeddelelse i opgaveloggen. Loggen er tom i tilfælde af status som UDFØRT
@phSpaceName=Teknisk navn på spacet
@emailFormatError=Ugyldigt e-mailformat
@emailFormatErrorInListText=Der er indtastet et ugyldigt e-mailformat i listen over ikke-tenant-elementer.
@emailSubjectTemplateText=Meddelelse for opgavekæde: $$taskChainName$$ - space: $$spaceId$$ - status: $$status$$
@emailMessageTemplateText=Hej.\n\n Din opgavekæde med betegnelsen $$taskChainName$$ er afsluttet med status $$status$$. \n Her er nogle andre detaljer om opgavekæden:\n - Teknisk navn på opgavekæde: $$taskChainName$$ \n - Log-id for kørslen af opgavekæden: $$logId$$ \n - Bruger, der kørte opgavekæden: $$user$$ \n - Link til logvisningen af opgavekæden: $$uiLink$$ \n - Starttidspunkt for kørslen af opgavekæden: $$startTime$$ \n - Sluttidspunkt for kørslen af opgavekæden: $$endTime$$ \n - Navn på spacet: $$spaceId$$ \n
@deleteEmailRecepient=Slet modtager
@emailInputDisabledText=Implementer opgavekæden for at tilføje e-mailmodtagere.
@tenantOwnerDomainMatchErrorText=Domænet for e-mailadressen stemmer ikke overens med tenantejerens domæne: {0}
@totalEmailIdLimitInfoText=Du kan vælge op til 20 e-mailmodtagere, herunder tenantmedlemsbrugere og andre modtagere.
@emailDomainInfoText=Kun e-mailadresser med domænet: {0} accepteres.
@duplicateEmailErrorText=Der er dobbelte e-mailmodtagere på listen.

#XFLD Zorder Title
@txtZorderTitle=Kolonner med z-rækkefølge i Apache Spark

#XFLD Zorder NoColumn
@txtZorderNoColumn=Der blev ikke fundet kolonner med z-rækkefølge

#XFLD Zorder Column PrimaryKey
@txtZorderColumnPrimaryKey=Primær nøgle

#XFLD
@lblOperators=Operatorer
addNewSelector=Tilføj som ny opgave
parallelSelector=Tilføj som parallel opgave
replaceSelector=Erstat eksisterende opgave
addparallelbranch=Tilføj som parallel gren
addplaceholder=Tilføj pladsholder
addALLOperation=Operatoren ALL
addOROperation=Operatoren ANY
addplaceholdertocanvas=Tilføj pladsholder til lærred
addplaceholderonselected=Tilføj pladsholder efter valgt opgave
#XFLD: Add Parallel Branch to Selected Task
addparallelbranchonselected=Tilføj parallel gren efter valgt opgave
addOperator=Tilføj operator
txtAdd=Tilføj
txtPlaceHolderText=Træk og slip en opgave her
@lblLayout=Layout

#XMSG
VAL_UNCONNECTED_TASK=Opgave ''{0}'' er ikke forbundet med opgavekæden.
#XMSG
VAL_TASK_SHOULD_HAVE_ONE_INCOMING_LINK=Opgave ''{0}'' bør kun have et indgående link.
#XMSG
VAL_TASK_SHOULD_HAVE_INCOMING_LINK=Opgave ''{0}'' bør have et indgående link.
#XMSG
VAL_UNCONNECTED_OPERATOR=Operator ''{0}'' er ikke forbundet med opgavekæden.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ATLEAST_TWO_INCOMING_LINK=Operator ''{0}'' bør have mindst to indgående links.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ONE_OUT_LINK=Operator ''{0}'' bør have mindst et udgående link.
#XMSG
VAL_CIRCULAR_REFERENCE_EXITS=Der findes en cirkulær løkke i opgavekæden ''{0}''.
#XMSG
VAL_UNCONNECTED_BRANCH=Objekt/kæde ''{0}'' er ikke forbundet med opgavekæden.
#XMSG
VAL_TASK_TRIGGERED_PARALLEL_MORE_THAN_ONE_TIME=Opgave ''{0}'' er forbundet parallelt mere end en gang. Fjern dubletter for at fortsætte.


txtBegin=Start
txtNodesInLink=Involverede objekter
#XTOL Tooltip for a context button on diagram
openInNewTab=Åbn i ny fane
#XTOL Tooltip for a context button on diagram
txtConnectToTaskOperator=Træk for at oprette forbindelse
@emailUpdateError=Fejl ved opdatering af liste med e-mailbeskeder

#XMSG
noTeamPrivilegeTxt=Du har ikke tilladelse til at se en liste over tenant-medlemmer. Brug fanebladet Andre til at tilføje e-mail-modtagere manuelt.

#XFLD Package
@txtPackage=Pakke

#XMSG warning for repository package assignment changed. {1} - package name
VAL_PACKAGE_CHANGED=Du har tildelt dette objekt til pakken ''{1}''. Klik på "Gem" for at bekræfte og validere denne ændring. Bemærk, at tildelingen til en pakke ikke kan fortrydes i denne editor, efter at du gemmer.
#XMSG warning for repository package dependency issue.
MISSING_DEPENDENCY=Afhængigheder for objektet ''{0}'' kan ikke løses i kontekst af pakken ''{1}''.

#Warning messages for the folders in the "Others" tab of task chain editor
#XMSG generic warning if getting the objects in a particular folder fails with unhandled errors
getNonRepoArtefactTypeDescendantsError=Der opstod et problem med at hente objekterne i den mappe, du valgte.
#BW Process Chains folder related warning messages
#XMSG warning if user does not have the necessary permission related to the SAP BW Bridge space
missingPrivilegeForBridgeSpaceError=Du har ikke de nødvendige rettigheder til at se eller inkludere BW-proceskæder i en opgavekæde.
#XMSG warning if there was a problem retrieving the BW process chains due to bridge space membership, bridge connection issues or other errors
generalBridgeSpaceError=Der opstod et problem med at hente BW-proceskæderne.
#XMSG warning if the BW API for retrieving the BW process chains from the SAP BW Bridge tenant fails
bwApiClientError=Der opstod et problem med at hente BW-proceskæderne fra SAP BW Bridge-tenanten.
#XMSG warning if BW process chains list is empty
bwApiEmptyListError=Der blev ikke fundet BW-proceskæder i SAP BW Bridge-tenanten.
#XMSG: Task log message for invalid OIDC configuration in the SAP BW Bridge tenant
bwBridgeOIDCConfigStatusInvalid=OpenID-godkendelse er ikke konfigureret for denne tenant. Brug clientID "{0}" og tokenURL "{1}" til at konfigurere OpenID-godkendelse i SAP BW bridge-tenant som beskrevet i SAP-note 3536298.
#XMSG BW Process Chain Object has been deleted
VAL_DELETED_BW=Følgende BW-proceskæder blev muligvis slettet fra SAP Bridge-tenanten: {0}
#BW SQL Script Procedures folder related warning messages
#XFLD waring if the Procedure list is empty
procedureAPIEmptyList=Ingen procedurer blev oprettet, eller rettigheden UDFØR er ikke givet til Open SQL-skemaet.
#Change digram orientations
changeOrientations=Ændr retninger


# placeholder for the API Path
apiPath=Eksempel: /job/v1
# placeholder for the status API Path
statusAPIPath=Eksempel: /job/v1/{id}/status
# valueStateText for the API Path
apiPathRequired=API-sti kræves
#placeholder for the CSRF Token URL
csrfTokenURL=Kun HTTPS understøttes
# Response Type 1
statusCode=Få resultat fra HTTP-statuskode
# Response Type 2
locationHeader=Få resultat fra HTTP-statuskode og placeringssidehoved
# Response Type 3
responseBody=Få resultat fra HTTP-statuskode og svartekst
# placeholder for ID
idPlaceholder=Indtast JSON-sti
# placeholder for indicator value
indicatorValue=Indtast værdi
# Placeholder for key
keyPlaceholder=Indtast nøgle
# Error message for missing key
KeyRequired=Nøgle kræves
# Error message for invalid key format
invalidKeyFormat=Den sidehovednøgle, du har indtastet, er ikke tilladt. Gyldige sidehoveder er:<ul><li>"prefer"</li><li>, der starter med "x-", undtagen "x-forwarded-host"</li><li>Sidehoveder, der indeholder alfanumeriske tegn, "-" eller "_"</li></ul>
# Error message for missing value
valueRequired=Værdi kræves
# Error message for invalid characters in value
invalidValueCharacters=Sidehovedet indeholder ugyldige tegn. Tilladte specialtegn er::\t ";", ":", "-", "_", ",", "?", "/", and "*"
# Validation message for invoke api path
apiPathValidation=Indtast en gyldig API-sti, for eksempel: /job/v1
# Validation message for JSON path
jsonPathValidation=Indtast en gyldig JSON-sti
# Validation message for success/error indicator
indicatorValueValidation=Indikatorværdien skal starte med et alfanumerisk tegn og kan indeholde følgende specialtegn:\t "-" og "_"
# Error message for JSON path
jsonPathRequired=JSON-sti kræves
# Error message for invalid API Technical Name
invalidTechnicalName=Det tekniske navn indeholder ugyldige tegn
# Error message for empty Technical Name
emptyTechnicalName=Teknisk navn kræves
# Tooltip for codeEditor dialog
codeEditorTooltip=Åbn vinduet JSON-redigering
# Status Api path validation message
validationStatusAPIPath=Indtast en gyldig API-sti, for eksempel: /job/v1/{id}/status
# CSRF token URL validation message
validationCSRFTokenURL=CSRF-token-URL'en skal starte med 'https://' og være en gyldig URL
# Select a connection
selectConnection=Vælg en forbindelse
# Validation message for connection item error
connectionNotReplicated=Forbindelsen er i øjeblikket ugyldig til at køre API-opgaver. Åbn appen "Forbindelser", og indtast dine legitimationsoplysninger igen for at korrigere HTTP-forbindelsen
# Validation message for API task properties wrong value or missing
VAL_REST_API_PROPERTIES_MISSING=API-opgaven "{0}" har forkerte eller manglende værdier for følgende egenskaber: {1}
# Validation message for API connection api is throwing some error or selected connection has some warning
VAL_REST_API_CONNECTION=API-opgaven "{0}" har et problem med HTTP-forbindelsen. Åbn appen "Forbindelser", og kontroller forbindelsen
# Validation message for API task properties when delected connection
VAL_REST_API_CONNECTION_DELETED=API-opgaven "{0}" har en slettet "{1}"-forbindelse
# Validation message for API task properties when connection cannot be consumed in the API task
VAL_REST_API_CONNECTION_CONSUMPTION_WARNING=API-opgaven "{0}" har en "{1}"-forbindelse, der ikke kan bruges til at køre API-opgaver. Åbn appen "Forbindelser", og genindtast dine legitimationsoplysninger for at etablere forbindelse for API-opgaver
# Information message if synchrnous mode is selected in the API Invocation then we don't need status information
modeInfoText=I synkron tilstand vises statuspanelet ikke for kald af API.
# validation dialog button for Run API Test
saveAnyway=Gem alligevel
# validation message for technical name
technicalNameValidation=Det tekniske navn skal være unikt inden for opgavekæden. Vælg venligst et andet teknisk navn
# Connection error message
getHttpConnectionsError=Kunne ikke hente HTTP-forbindelserne
# Msg for the API test run when the Task chain is not saved
taskChainNotSaved=Opgavekæden skal gemmes, før API-testkørslen udføres
# Msg failed to run API test run
@failedToRunAPI=Kunne ikke udføre API-testkørslen
# Msg for the API test run when its already in running state
apiTaskRunning=En API-testkørsel er allerede i gang. Vil du starte en ny testkørsel?

topToBtm=Øverst-nederst
leftToRight=Venstre-højre

#XTIT Apache Spark Application Settings
txtHeaderApacheSettings=Indstillinger for Apache Spark-applikation
#XFLD Use Default
txtUseSpaceDefault=Brug standard
#XFLD Application
txtApplication=Applikation
#XFLD Define new settings for this Task
txtNewSettings=Definer nye indstilinger for denne opgave

#XFLD Use Default
txtUseDefault=Brug standard




