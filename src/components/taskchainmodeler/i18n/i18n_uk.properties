#XTOL Undo
@undo=Відмінити
#XTOL Redo
@redo=Повторити
#XTOL Delete Selected Symbol
@deleteNode=Видалити вибраний символ
#XTOL Zoom to Fit
@zoomToFit=Збільшити розмір
#XTOL Auto Layout
@autoLayout=Автоматичний формат
#XMSG
@welcomeText=Перетягніть свої об’єкти з панелі ліворуч до цієї робочої області.
#XMSG
@txtNoData=Схоже, ви ще не додали жодних об’єктів.
#XMSG
VAL_ENTER_VALID_STRING_GEN=Введіть дійсний рядок довжиною не більше {0}.
#XMSG
@noParametersMsg=Ця процедура не має параметрів введення.
#XMSG
@ip_enterValueMsg="{0}" (прогін процедури скрипта SQL) має параметри введення. Для кожного з них можна задати значення.
#XTOL
@validateModel=Повідомлення про перевірку
#XTOL
@hierarchy=Ієрархія
#XTOL
@columnCount=Кількість стовпчиків
#XFLD
@yes=Так
#XFLD
@no=Ні
#XTIT Save Dialog param
@modelNameTaskChain=Ланцюжок завдань
#properties panel
@lblPropertyTitle=Властивості
#XFLD
@lblGeneral=Загальне
#XFLD : Setting
@lblSetting=Настройки
#XTXT: Delta Enabled Local Table Deletion
txtDeltaTableDeletion=Видаляти всі повністю оброблені записи з типом зміни ''Видалено'', що існують довше ніж
#XTXT: Delta Enabled Local Table Deletion Input field description
desDeltaTableDeletion=Дні
#XFLD: Data Activation label
@lblDataActivation=Активація даних
#XFLD: Latency label
@latency=Затримка
#XTEXT: Text for Latency dropdown
txtLatencyDefault=Усталено
#XTEXT: Text 1 hour
txtOneHour=1 год
#XTEXT: Text 2 hours
txtTwoHours=2 год
#XTEXT: Text 3 hours
txtThreeHours=3 год
#XTEXT: Text 4 hours
txtFourHours=4 год
#XTEXT: Text 6 hours
txtSixHours=6 год
#XTEXT: Text 12 hours
txtTwelveHours=12 год
#XTEXT: Text 1 day
txtOneDay=1 день
#XFLD: Latency label
@autoRestartHead=Автоматичний перезапуск
#XFLD
@lblConnectionName=З’єднання
#XFLD
@lblQualifiedName=Кваліфіковане ім’я
#XFLD
@lblSpaceName=Ім’я простору
#XFLD
@lblLocalSchemaName=Локальна схема
#XFLD
@lblType=Тип об’єкта
#XFLD
@lblActivity=Операція
#XFLD
@lblTableName=Ім’я таблиці
#XFLD
@lblBusinessName=Бізнес-ім’я
#XFLD
@lblTechnicalName=Технічне ім’я
#XFLD
@lblSpace=Простір
#XFLD
@lblLabel=Надпис
#XFLD
@lblDataType=Тип даних
#XFLD
@lblDescription=Опис
#XFLD
@lblStorageType=Сховище
#XFLD
@lblHTTPConnection=Універсальне з'єднання HTTP
#XFLD
@lblAPISettings=Універсальні настройки API
#XFLD
@header=Заголовки
#XFLD
@lblInvoke=Виклик API
#XFLD
@lblMethod=Метод
#XFLD
@lblUrl=Базова URL-адреса
#XFLD
@lblAPIPath=Шлях API
#XFLD
@lblMode=Режим
#XFLD
@lblCSRFToken=Обов'язковий маркер CSRF
#XFLD
@lblTokenURL=URL-адреса маркера CSRF
#XFLD
@csrfTokenInfoText=Якщо не введено, використовуватимуться базова URL-адреса та шлях API
#XFLD
@lblCSRF=Маркер CSRF
#XFLD
@lblRequestBody=Тіло запиту
#XFLD
@lblFormat=Формат
#XFLD
@lblResponse=Відповідь
#XFLD
@lblId=ІД для отримання статусу
#XFLD
@Id=ІД
#XFLD
@lblSuccessIndicator=Індикатор успіху
#XFLD
@lblErrorIndicator=Індикатор помилки
#XFLD
@lblErrorReason=Причина помилки
#XFLD
@lblStatus=Статус
#XFLD
@lblApiTestRun=Тестовий прогін API
#XFLD
@lblRunStatus=Статус прогону
#XFLD
@lblLastRan=Дата й час останнього прогону
#XFLD
@lblTestRun=Тестовий прогін
#XFLD
@lblDefaultHeader=Усталені поля заголовка (пари "ключ – значення")
#XFLD
@lblAdditionalHeader=Додаткове поле заголовка
#XFLD
@lblKey=Ключ
#XFLD
@lblEditJSON=Редагувати JSON
#XFLD
@lblTasks=Завдання
#XFLD
@lblRESTfullTask=API
#XFLD: add field button text
btnAddField=Додати поле заголовка
#XFLD: view Details link
@viewDetails=Переглянути подробиці
#XTOL
tooltipTxt=Більше
#XTOL
delete=Видалити
#XBTN: ok button text
btnOk=Ok
#XBTN: cancel button text
btnCancel=Скасувати
#XBTN: save button text
btnSave=Зберегти
#XMSG Error: Object has duplicated technical name.
VAL_OBJECT_DUPLICATE_NAME=Об’єкт "{0}" в репозиторії вже існує. Введіть інше ім’я.
#XMSG: loading message while opening task chain
loadTaskChain=Завантаження ланцюжка завдань...
#model properties
#XFLD
@status_panel=Статус прогону
#XFLD
@deploy_status_panel=Стан розгортання
#XFLD
@status_lbl=Статус
#XFLD
@lblLastExecuted=Останній прогін
#XFLD
@lblNotExecuted=Прогін ще не відбувся
#XFLD
@lblNotDeployed=Не розгорнуто
#XFLD
errorDetailsTxt=Не вдалось отримати стан прогону
#XBTN: Schedule dropdown menu
SCHEDULE=Розклад
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Змінити розклад
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Видалити розклад
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Створити розклад
#XLNK
viewDetails=Переглянути подробиці
#XMSG: error message for reading execution status from backend
backendErrorMsg=Схоже, на цей момент дані не завантажуються із сервера. Спробуйте отримати дані ще раз.
#XFLD: Status text for Completed
@statusCompleted=Завершено
#XFLD: Status text for Running
@statusRunning=Виконується
#XFLD: Status text for Failed
@statusFailed=Помилка
#XFLD: Status text for Stopped
@statusStopped=Зупинено
#XFLD: Status text for Stopping
@statusStopping=Зупиняється
#XFLD
@LoaderTitle=Завантаження
#XFLD
#XFLD: Deploy status, dataflow has  been deployed / deployment date exists
@deployStatusActive=Розгорнуто
@deployStatusRevised=Локальні оновлення
@deployStatusFailed=Помилка
@deployStatusPending=Розгортання...
@LoaderText=Отримання докладної інформації із сервера
#XMSG
@msgDetailFetchError=Помилка під час отримання докладної інформації із сервера
#XFLD
@executeError=Помилка
#XFLD
@executeWarning=Застереження
#XMSG
@executeConfirmDialog=Інформація
#XMSG
@executeunsavederror=Збережіть ланцюжок завдань перед його запуском.
#XMSG
@executemodifiederror=У ланцюжку завдань є незбережені зміни. Збережіть його.
#XMSG
@executerunningerror=Наразі ланцюжок завдань запущено. Зачекайте, доки поточний прогін не буде завершено, перш ніж почати новий.
#XMSG
@btnExecuteAnyway=Однак виконати
#XMSG
@msgExecuteWithValidations=У ланцюжку завдань є помилки перевірки. Прогін ланцюжка завдань може призвести до збою.
#XMSG
@msgRunDeployedVersion=Існують зміни для розгортання. Буде запущено останню розгорнуту версію ланцюжка завдань. Продовжити?
#XMSG
#XMSG
@navToMonitoring=Відкрити в моніторі ланцюжка завдань
#XMSG
txtOR=OR
#XFLD
@preview=Попередній перегляд
#XMSG
txtand=AND
#NOTR
@dateFormat=yyyy-MM-dd
#NOTR
@dateTimeFormatFilter=yyyy-MM-dd HH:mm:ss.
#NOTR
@timeFormat=HH:mm:ss
@lblColum=Стовпчик
#XFLD
@lblCondition=Умова
#XFLD
@lblValue=Значення
#XMSG
@msgJsonInvalid=Не вдалося зберегти ланцюжок завдань, оскільки у файлі JSON є помилки. Знайдіть їх та усуньте.
#XTIT
@msgSaveFailTitle=Неприпустимий JSON.
#XMSG
NOT_CHAINABLE=Об’єкт "{0}" не можна додати до ланцюжка завдань.
#XMSG
NOT_CHAINABLE_REMOTETABLE=Об''єкт ''{0}'': тип реплікації буде змінено.
#XMSG
searchTaskChain=Об'єкти пошуку

#XFLD
@txtTaskChain=Ланцюжок завдань
#XFLD
@txtRemoteTable=Віддалена таблиця
#XFLD
@txtRemoveData=Вилучити репліковані дані
#XFLD
@txtRemovePersist=Вилучити постійно відтворювані дані
#XFLD
@txtView=Подання
#XFLD
@txtDataFlow=Потік даних
#XFLD
@txtIL=Інтелектуальний пошук
#XFLD
@txtTransformationFlow=Потік трансформації
#XFLD
@txtReplicationFlow=Потік реплікації
#XFLD
@txtDeltaLocalTable=Локальна таблиця
#XFLD
@txtBWProcessChain=Ланцюг процесів BW
#XFLD
@txtSQLScriptProcedure=Процедура скрипта SQL
#XFLD
@txtAPI=API
#XFLD
@txtMerge=Об'єднати
#XFLD
@txtOptimize=Оптимізувати
#XFLD
@txtVacuum=Видалити записи

#XFLD
@txtRun=Прогін
#XFLD
@txtPersist=Постійно
#XFLD
@txtReplicate=Реплікувати
#XFLD
@txtDelete=Видалити записи з типом зміни ''Видалено''
#XFLD
@txtRunTC=Прогін ланцюжка завдань
#XFLD
@txtRunBW=Прогін ланцюга процесів BW
#XFLD
@txtRunSQLScriptProcedure=Прогін процедури скрипта SQL

#XFLD
@txtRunDataFlow=Прогін потоку даних
#XFLD
@txtPersistView=Постійне подання
#XFLD
@txtReplicateTable=Реплікувати таблицю
#XFLD
@txtRunIL=Прогнати інтелектуальний пошук
#XFLD
@txtRunTF=Прогнати потік трансформації
#XFLD
@txtRunRF=Прогнати потік реплікації
#XFLD
@txtRemoveReplicatedData=Вилучити репліковані дані
#XFLD
@txtRemovePersistedData=Вилучити постійно відтворювані дані
#XFLD
@txtMergeData=Об'єднати
#XFLD
@txtOptimizeData=Оптимізувати
#XFLD
@txtVacuumData=Видалити записи
#XFLD
@txtRunAPI=Запустити API

#XFLD storage type text
hdlfStorage=Файл

@statusNew=Не розгорнуто
@statusActive=Розгорнуто
@statusRevised=Локальні оновлення
@statusPending=Розгортання...
@statusChangesToDeploy=Зміни до розгортання
@statusDesignTimeError=Помилка часу проектування
@statusRunTimeError=Помилка часу виконання

#XTIT
txtNodes=Об’єкти в ланцюжку завдань ({0})
#XBTN
@deleteNodes=Видалити

#XMSG
@txtDropDataToDiagram=Перетягуйте об’єкти на схему.
#XMSG
@noData=Немає об’єктів

#XFLD
@txtTaskPosition=Позиція завдання

#input parameters and variables
#XFLD
lblInputParameters=Параметри введення
#XFLD
ip_name=Ім'я
#XFLD
ip_value=Значення
#XTEXT
@noObjectsFound=Об'єкти не знайдено

#XMSG
@msgExecuteSuccess=Прогін ланцюжка завдань розпочато.
#XMSG
@msgExecuteFail=Не вдалося прогнати ланцюжок завдань.
#XMSG
@msgDeployAndRunSuccess=Розгортання та запуск ланцюжка завдань розпочато.
#XMSG
@msgDeployAndRunFail=Не вдалося розгорнути й прогнати ланцюжок завдань.
#XMSG
@titleExecuteBusy=Зачекайте.
#XMSG
@msgExecuteBusy=Ми готуємо ваші дані для запуску ланцюжка завдань.
#XMSG
@msgAPITestRunSuccess=Розпочато тестовий прогін API.
#XMSG
@msgAPIExecuteBusy=Ми готуємо ваші дані для запуску тестового прогону API.

#XTOL
txtOpenInEditor=Відкрити в редакторі
#XTOL
txtPreviewData=Попередній перегляд даних

#datapreview
#XMSG
@msgDataPreviewNotSupp=Попередній перегляд даних для цього об’єкта недоступний.

#XMSG Error: empty model
VAL_MODEL_EMPTY=Схоже, що ваша модель порожня. Додайте кілька об’єктів.
#XMSG Error: deploy model
@msgDeployBeforeRun=Потрібно розгорнути ланцюжок завдань перед його запуском.
#BTN: close dialog
btnClose=Закрити

#XMSG Object is not Deployed
VAL_NOT_DEPLOYED=Щоб продовжити ланцюжок завдань, необхідно розгорнути об’єкт "{0}".
#XMSG Object is having runtime error
VAL_RUNTIME_ERROR=Об''єкт "{0}" повертає помилку часу виконання. Перевірте об''єкт.
#XMSG Object is having design time error
VAL_DESIGNTIME_ERROR=Об''єкт "{0}" повертає помилку часу розробки. Перевірте об''єкт.
#XMSG Procedure Object has been deleted
VAL_DELETED_PROCEDURE=Ці процедури було видалено: {0}
#XMSG New Parameter added to Procedure object
VAL_NEW_PARAMETER_ADDED=До процедури "{1}" додано нові параметри: "{0}". Розгорніть повторно ланцюжок завдань.
#XMSG Parameter removed from Procedure object
VAL_PARAMETER_DELETED=З процедури "{1}" вилучено параметри: "{0}". Розгорніть повторно ланцюжок завдань.
#XMSG Parameter data type changed in Procedure object
VAL_PARAMETER_DATATYPE_MODIFIED=Тип даних параметра "{0}" змінено з "{1}" на "{2}" в процедурі "{3}".
#XMSG Parameter length changed of specific datatype in Procedure object
VAL_PARAMETER_LENGTH_MODIFIED=Довжину параметра "{0}" змінено з "{1}" на "{2}" в процедурі "{3}".
#XMSG Parameter precision changed of Decimal datatype in Procedure object
VAL_PARAMETER_PRECISION_MODIFIED=Точність параметра "{0}" змінено з "{1}" на "{2}" в процедурі "{3}".
#XMSG Parameter scale changed of Decimal datatype in Procedure object
VAL_PARAMETER_SCALE_MODIFIED=Масштаб параметра "{0}" змінено з "{1}" на "{2}" в процедурі "{3}".
#XMSG Parameter value not provided in Procedure object
VAL_SQL_SCRIPT_PROCEDURE_PARAMETER_VALUE_MISSING=Не введено значення для параметрів введення, необхідних для процедури "{0}": "{1}".
#XMSG Remote Table activity changes to Remove Replicated Data
VAL_REMOVE_REPLICATED_DATA=Під час наступного прогону ланцюжка завдань тип доступу до даних буде змінено, і дані більше не вивантажуватимуться в реальному часі.
#XMSG Remote Table activity changes
REPLICATION_TYPE_CHANGED=Об''єкт "{0}": тип реплікації буде змінено.

#XFLD
@lblStartNode=Початковий вузол
#XFLD
@lblEndNode=Кінцевий вузол
#XFLD
@linkTo={0} до {1}
#XTOL
@txtViewDetails=Переглянути подробиці

#XTOL
txtOpenImpactLineage=Аналіз впливу та походження даних
#XFLD
@emailNotifications=Сповіщення електронною поштою
#XFLD
@txtReset=Скинути
#XFLD
@emailMsgWarning=Якщо повідомлення електронної пошти порожнє, буде надіслано усталений шаблон електронної пошти
#XFLD
@notificationSettings=Настройки сповіщень
#XFLD
@recipientEmailAddr=Адреса електронної пошти одержувача
#XFLD
@emailSubject=Тема електронного листа
@emailSubjectText=Ланцюжок завдань <TASKCHAIN_NAME> завершено зі статусом <STATUS>
#XFLD
@emailMessage=Повідомлення електронної пошти
@emailMessageText=Шановний користувач!\n\n Повідомляємо, що ланцюжок завдань <TASKCHAIN_NAME>, запущений о <START_TIME>, завершено зі статусом <STATUS>. Виконання завершено о <END_TIME>.\n\nПодробиці:\nПростір:<SPACE_NAME>\nПомилка:<ERROR_MESSAGE>
@selectEmailRecipientDialogTitle=Виберіть адресу електронної пошти одержувача
@tenantMembers=Учасники орендаря
@others=Інші ({0})
@selectedEmailAddress=Вибрані одержувачі
@add=Додати
@placeholder=Заповнювач
@description=Опис
@copyText=Копіювати текст
@taskchainDetailsPlaceholder=Заповнювачі для подробиць ланцюжка завдань
@placeholderCopied=Заповнювач скопійовано
@invalidEmailInfo=Введіть правильну електронну адресу
@maxMembersAlreadyAdded=Ви вже додали максимальну кількість: 20 учасників
@enterEmailAddress=Введіть електронну адресу користувача
@inCorrectPlaceHolder={0} не є очікуваним заповнювачем у тексті електронного листа.
@nsOFF=Не надсилати сповіщення
@nsFAILED=Надсилати сповіщення електронною поштою лише тоді, коли запуск завершено з помилкою
@nsCOMPLETED=Надсилати сповіщення електронною поштою лише після успішного завершення запуску
@nsANY=Надсилати електронний лист після завершення запуску
@phStatus=Статус ланцюжка завдань: ВИКОНАНО|НЕ ВИКОНАНО
@phTaskChainTName=Технічне ім’я ланцюжка завдань
@phTaskChainBName=Бізнес-ім’я ланцюжка завдань
@phLogId=Ідентифікатор журналу прогону
@phUser=Користувач, який запускає ланцюжок завдань
@phLogUILink=Посилання на відображення журналу ланцюжка завдань
@phStartTime=Час початку прогону
@phEndTime=Час закінчення прогону
@phErrMsg=Перше повідомлення про помилку в журналі завдань. Журнал порожній, якщо статус ВИКОНАНО
@phSpaceName=Технічне ім’я простору
@emailFormatError=Недійсний формат адреси електронної пошти
@emailFormatErrorInListText=У списку учасників, які не є клієнтами, введено недійсний формат адреси електронної пошти.
@emailSubjectTemplateText=Сповіщення для ланцюжка завдань: $$taskChainName$$ - Простір: $$spaceId$$ - Статус: $$status$$
@emailMessageTemplateText=Вітаємо!\n\n Ваш ланцюжок завдань $$taskChainName$$ виконано зі статусом $$status$$. \n Ось деякі інші подробиці про ланцюжок завдань:\n -Технічне ім’я ланцюжка завдань: $$taskChainName$$ \n - Ідентифікатор журналу прогону ланцюжка завдань: $$logId$$ \n - Користувач, який запускав ланцюжок завдань: $$user$$ \n - Посилання для перегляду журналу ланцюжка завдань: $$uiLink$$ \n - Час початку виконання ланцюжка завдань: $$startTime$$ \n - Час закінчення виконання ланцюжка завдань: $$endTime$$ \n - Ім’я простору: $$spaceId$$ \n
@deleteEmailRecepient=Видалити одержувача
@emailInputDisabledText=Розгорніть ланцюжок завдань, щоб додати одержувачів електронної пошти.
@tenantOwnerDomainMatchErrorText=Домен адреси електронної пошти не збігається з доменом власника орендаря: {0}
@totalEmailIdLimitInfoText=Ви можете вибрати до 20 одержувачів повідомлень електронної пошти, зокрема користувачів-учасників та інших одержувачів.
@emailDomainInfoText=Приймаються лише електронні адреси з такого домену: {0}.
@duplicateEmailErrorText=У списку є повторювані одержувачі повідомлень електронної пошти.

#XFLD Zorder Title
@txtZorderTitle=Стовпчики Z-упорядкування Apache Spark

#XFLD Zorder NoColumn
@txtZorderNoColumn=Стовпчики Z-упорядкування не знайдено

#XFLD Zorder Column PrimaryKey
@txtZorderColumnPrimaryKey=Первинний ключ

#XFLD
@lblOperators=Оператори
addNewSelector=Додати як нове завдання
parallelSelector=Додати як паралельне завдання
replaceSelector=Замінити наявне завдання
addparallelbranch=Додати як паралельне відгалуження
addplaceholder=Додати мітку-заповнювач
addALLOperation=Оператор ALL
addOROperation=Оператор ANY
addplaceholdertocanvas=Додати мітку-заповнювач до робочої області
addplaceholderonselected=Додати мітку-заповнювач після вибраного завдання
#XFLD: Add Parallel Branch to Selected Task
addparallelbranchonselected=Додати паралельне відгалуження після вибраного завдання
addOperator=Додати оператор
txtAdd=Додати
txtPlaceHolderText=Перетягніть сюди завдання
@lblLayout=Формат

#XMSG
VAL_UNCONNECTED_TASK=Завдання ''{0}'' не з’єднано з ланцюгом завдань.
#XMSG
VAL_TASK_SHOULD_HAVE_ONE_INCOMING_LINK=У завдання ''{0}'' має бути тільки один вхідний зв’язок.
#XMSG
VAL_TASK_SHOULD_HAVE_INCOMING_LINK=У завдання ''{0}'' має бути один вхідний зв’язок.
#XMSG
VAL_UNCONNECTED_OPERATOR=Оператор ''{0}'' не з’єднано з ланцюгом завдань.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ATLEAST_TWO_INCOMING_LINK=В оператора ''{0}'' має бути принаймні два вхідних зв’язки.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ONE_OUT_LINK=В оператора ''{0}'' має бути принаймні один вихідний зв’язок.
#XMSG
VAL_CIRCULAR_REFERENCE_EXITS=У ланцюгу завдань ''{0}'' існує замкнений цикл.
#XMSG
VAL_UNCONNECTED_BRANCH=Об’єкт чи відгалуження ''{0}'' не з’єднано з ланцюгом завдань.
#XMSG
VAL_TASK_TRIGGERED_PARALLEL_MORE_THAN_ONE_TIME=Завдання ''{0}'' з’єднано паралельно кілька разів. Вилучіть дублікати, щоб продовжити.


txtBegin=Почати
txtNodesInLink=Задіяні об’єкти
#XTOL Tooltip for a context button on diagram
openInNewTab=Відкрити в новій вкладці
#XTOL Tooltip for a context button on diagram
txtConnectToTaskOperator=Перетягніть для з'єднання
@emailUpdateError=Помилка в оновленні списку сповіщень електронною поштою

#XMSG
noTeamPrivilegeTxt=У вас немає дозволу на перегляд списку членів орендатора. Скористайтеся вкладкою "Інші", щоб додати отримувачів пошти вручну.

#XFLD Package
@txtPackage=Пакет

#XMSG warning for repository package assignment changed. {1} - package name
VAL_PACKAGE_CHANGED=Ви присвоїли цей об''єкт пакету ''{1}''. Натисніть кнопку ''Зберегти'', щоб підтвердити та схвалити цю зміну. Зверніть увагу, що після збереження в цьому редакторі не можна скасувати присвоєння пакету.
#XMSG warning for repository package dependency issue.
MISSING_DEPENDENCY=Залежності об''єкта ''{0}'' не можна розв''язати в контексті пакета ''{1}''.

#Warning messages for the folders in the "Others" tab of task chain editor
#XMSG generic warning if getting the objects in a particular folder fails with unhandled errors
getNonRepoArtefactTypeDescendantsError=Виникла проблема з отриманням об'єктів у вибраній папці.
#BW Process Chains folder related warning messages
#XMSG warning if user does not have the necessary permission related to the SAP BW Bridge space
missingPrivilegeForBridgeSpaceError=У вас немає необхідного дозволу для перегляду ланцюгів процесів або їх включення до ланцюжка завдань.
#XMSG warning if there was a problem retrieving the BW process chains due to bridge space membership, bridge connection issues or other errors
generalBridgeSpaceError=Виникла проблема з отриманням ланцюгів процесів BW.
#XMSG warning if the BW API for retrieving the BW process chains from the SAP BW Bridge tenant fails
bwApiClientError=Виникла проблема з отриманням ланцюгів процесів BW з орендатора моста SAP BW.
#XMSG warning if BW process chains list is empty
bwApiEmptyListError=В орендаторі моста SAP BW не знайдено ланцюги процесів BW.
#XMSG: Task log message for invalid OIDC configuration in the SAP BW Bridge tenant
bwBridgeOIDCConfigStatusInvalid=Для цього орендатора не сконфігуровано аутентифікацію OpenID. Використайте clientID "{0}" і tokenURL "{1}", щоб сконфігурувати автентифікацію OpenID в орендаторі моста SAP BW, як описано в нотатці SAP 3536298.
#XMSG BW Process Chain Object has been deleted
VAL_DELETED_BW=Можливо, ці ланцюги процесів BW було видалено з орендатора моста SAP: {0}
#BW SQL Script Procedures folder related warning messages
#XFLD waring if the Procedure list is empty
procedureAPIEmptyList=Не створено жодної процедури або схемі Open SQL не надано привілей EXECUTE.
#Change digram orientations
changeOrientations=Змінити орієнтації


# placeholder for the API Path
apiPath=Наприклад: /job/v1
# placeholder for the status API Path
statusAPIPath=Наприклад: /job/v1/{id}/status
# valueStateText for the API Path
apiPathRequired=Потрібен шлях API
#placeholder for the CSRF Token URL
csrfTokenURL=Підтримується тільки HTTPS
# Response Type 1
statusCode=Отримати результат з коду статусу HTTP
# Response Type 2
locationHeader=Отримати результат із заголовка розташування та коду статусу HTTP
# Response Type 3
responseBody=Отримати результат із тіла відповіді та коду статусу HTTP
# placeholder for ID
idPlaceholder=Введіть шлях JSON
# placeholder for indicator value
indicatorValue=Введіть значення
# Placeholder for key
keyPlaceholder=Введіть ключ
# Error message for missing key
KeyRequired=Потрібен ключ
# Error message for invalid key format
invalidKeyFormat=Введений ключ заголовка недозволений. Дійсні заголовки:<ul><li>"prefer";</li><li>заголовки, що починаються з "x-", за винятком "x-forwarded-host";</li><li>заголовки, що містять алфавітно-цифрові символи, "-" або "_".</li></ul>
# Error message for missing value
valueRequired=Значення є обов'язковим
# Error message for invalid characters in value
invalidValueCharacters=Заголовок містить недійсні символи. Дозволені спеціальні символи :\t ";", ":", "-", "_", ",", "?", "/" і "*"
# Validation message for invoke api path
apiPathValidation=Введіть дійсний шлях API, наприклад: /job/v1
# Validation message for JSON path
jsonPathValidation=Введіть дійсний шлях JSON
# Validation message for success/error indicator
indicatorValueValidation=Значення індикатора має починатися з алфавітно-цифрового символу й може включати в себе такі спеціальні символи:\t "-" і "_"
# Error message for JSON path
jsonPathRequired=Потрібен шлях JSON
# Error message for invalid API Technical Name
invalidTechnicalName=Технічне ім'я містить дійсні символи
# Error message for empty Technical Name
emptyTechnicalName=Технічне ім'я є обов'язковим
# Tooltip for codeEditor dialog
codeEditorTooltip=Відкрити вікно редагування JSON
# Status Api path validation message
validationStatusAPIPath=Введіть дійсний шлях API, наприклад: /job/v1/{id}/status
# CSRF token URL validation message
validationCSRFTokenURL=URL-адреса маркера CSRF має починатися з "https://" і бути дійсною URL-адресою
# Select a connection
selectConnection=Вибрати з'єднання
# Validation message for connection item error
connectionNotReplicated=З'єднання наразі недійсне для прогону завдань API. Відкрийте застосунок "З'єднання" і ще раз введіть реєстраційні дані, щоб виправити з'єднання HTTP.
# Validation message for API task properties wrong value or missing
VAL_REST_API_PROPERTIES_MISSING=Завдання API "{0}" має неправильні або відсутні значення для таких властивостей: {1}
# Validation message for API connection api is throwing some error or selected connection has some warning
VAL_REST_API_CONNECTION=У завдання API "{0}" виникла проблема зі з''єднанням HTTP. Відкрийте застосунок "З''єднання" і перевірте з''єднання.
# Validation message for API task properties when delected connection
VAL_REST_API_CONNECTION_DELETED=Завдання API "{0}" має видалене з''єднання "{1}"
# Validation message for API task properties when connection cannot be consumed in the API task
VAL_REST_API_CONNECTION_CONSUMPTION_WARNING=Завдання API "{0}" має з''єднання "{1}", яке не можна використовувати для запуску завдань API. Відкрийте застосунок "З''єднання" і введіть знову свої реєстраційні дані, щоб установити з''єднання для завдань API.
# Information message if synchrnous mode is selected in the API Invocation then we don't need status information
modeInfoText=У синхронному режимі для викликів API панель статусу не відображається
# validation dialog button for Run API Test
saveAnyway=Усе одно зберегти
# validation message for technical name
technicalNameValidation=Технічне ім'я має бути унікальним у межах ланцюжка завдань. Виберіть інше технічне ім'я.
# Connection error message
getHttpConnectionsError=Не вдалося отримати з'єднання HTTP
# Msg for the API test run when the Task chain is not saved
taskChainNotSaved=Перед запуском тестового прогону API слід зберегти ланцюжок завдань
# Msg failed to run API test run
@failedToRunAPI=Не вдалося запустити тестовий прогін API
# Msg for the API test run when its already in running state
apiTaskRunning=Тестовий прогін API вже виконується. Запустити новий тестовий прогін?

topToBtm=Згори вниз
leftToRight=Зліва направо

#XTIT Apache Spark Application Settings
txtHeaderApacheSettings=Настройки застосунку Apache Spark
#XFLD Use Default
txtUseSpaceDefault=Використовувати усталене
#XFLD Application
txtApplication=Застосунок
#XFLD Define new settings for this Task
txtNewSettings=Визначити нові настройки для цього завдання

#XFLD Use Default
txtUseDefault=Використовувати усталене




