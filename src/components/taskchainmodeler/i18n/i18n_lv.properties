#XTOL Undo
@undo=Atsaukt
#XTOL Redo
@redo=Atcelt atsaukšanu
#XTOL Delete Selected Symbol
@deleteNode=Dzēst atlasīto simbolu
#XTOL Zoom to Fit
@zoomToFit=T<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, lai ietilpinātu
#XTOL Auto Layout
@autoLayout=Automātiskais izkārtojums
#XMSG
@welcomeText=Velciet un nometiet objektus no kreisā paneļa uz šo kanvu.
#XMSG
@txtNoData=Šķiet, jūs neesat vēl pievienojis nevienu objektu.
#XMSG
VAL_ENTER_VALID_STRING_GEN=Lūdzu, ievadiet derīgu virkni, kuras garums ir mazāks par vai vienāds ar {0}.
#XMSG
@noParametersMsg=Šai procedūrai nav ievades parametru.
#XMSG
@ip_enterValueMsg=Elementam "{0}" (Izpildīt SQL skripta procedūru) ir ievades parametri. Katram no tiem varat iestatīt vērtību.
#XTOL
@validateModel=Pārbaudes ziņojumi
#XTOL
@hierarchy=Hierarhija
#XTOL
@columnCount=Kolonnu skaits
#XFLD
@yes=Jā
#XFLD
@no=Nē
#XTIT Save Dialog param
@modelNameTaskChain=Uzdevumu ķēde
#properties panel
@lblPropertyTitle=Rekvizīti
#XFLD
@lblGeneral=Vispārīgi
#XFLD : Setting
@lblSetting=Iestatījumi
#XTXT: Delta Enabled Local Table Deletion
txtDeltaTableDeletion=Dzēst visus pilnībā apstrādātos ierakstus ar izmaiņu tipu “Dzēsts”, kas ir vecāki par
#XTXT: Delta Enabled Local Table Deletion Input field description
desDeltaTableDeletion=Dienas
#XFLD: Data Activation label
@lblDataActivation=Datu aktivizācija
#XFLD: Latency label
@latency=Latentums
#XTEXT: Text for Latency dropdown
txtLatencyDefault=Noklusējums
#XTEXT: Text 1 hour
txtOneHour=1 stunda
#XTEXT: Text 2 hours
txtTwoHours=2 stundas
#XTEXT: Text 3 hours
txtThreeHours=3 stundas
#XTEXT: Text 4 hours
txtFourHours=4 stundas
#XTEXT: Text 6 hours
txtSixHours=6 stundas
#XTEXT: Text 12 hours
txtTwelveHours=12 stundas
#XTEXT: Text 1 day
txtOneDay=1 diena
#XFLD: Latency label
@autoRestartHead=Automātiskā restartēšana
#XFLD
@lblConnectionName=Savienojums
#XFLD
@lblQualifiedName=Kvalificētais nosaukums
#XFLD
@lblSpaceName=Vietas nosaukums
#XFLD
@lblLocalSchemaName=Lokālā shēma
#XFLD
@lblType=Objekta tips
#XFLD
@lblActivity=Darbība
#XFLD
@lblTableName=Tabulas nosaukums
#XFLD
@lblBusinessName=Biznesa nosaukums
#XFLD
@lblTechnicalName=Tehniskais nosaukums
#XFLD
@lblSpace=Vieta
#XFLD
@lblLabel=Etiķete
#XFLD
@lblDataType=Datu tips
#XFLD
@lblDescription=Apraksts
#XFLD
@lblStorageType=Krātuve
#XFLD
@lblHTTPConnection=Ģenērisks HTTP savienojums
#XFLD
@lblAPISettings=Ģenēriski API iestatījumi
#XFLD
@header=Galvenes
#XFLD
@lblInvoke=API izsaukšana
#XFLD
@lblMethod=Metode
#XFLD
@lblUrl=Bāzes URL
#XFLD
@lblAPIPath=API ceļš
#XFLD
@lblMode=Režīms
#XFLD
@lblCSRFToken=Pieprasīt CSRF marķieri
#XFLD
@lblTokenURL=CSRF marķiera URL
#XFLD
@csrfTokenInfoText=Ja nav ievadīts, tiek izmantots bāzes URL un API ceļš
#XFLD
@lblCSRF=CSRF marķieris
#XFLD
@lblRequestBody=Pieprasīt pamattekstu
#XFLD
@lblFormat=Formāts
#XFLD
@lblResponse=Atbilde
#XFLD
@lblId=ID statusa izgūšanai
#XFLD
@Id=ID
#XFLD
@lblSuccessIndicator=Sekmīguma indikators
#XFLD
@lblErrorIndicator=Kļūdas indikators
#XFLD
@lblErrorReason=Kļūdas iemesls
#XFLD
@lblStatus=Statuss
#XFLD
@lblApiTestRun=API testa izpilde
#XFLD
@lblRunStatus=Izpildes statuss
#XFLD
@lblLastRan=Pēdējoreiz darbināts
#XFLD
@lblTestRun=Testa izpilde
#XFLD
@lblDefaultHeader=Noklusējuma galvenes lauki (atslēgas-vērtības pāri)
#XFLD
@lblAdditionalHeader=Papildu galvenes lauks
#XFLD
@lblKey=Atslēga
#XFLD
@lblEditJSON=Rediģēt JSON
#XFLD
@lblTasks=Uzdevumi
#XFLD
@lblRESTfullTask=API
#XFLD: add field button text
btnAddField=Pievienot galvenes lauku
#XFLD: view Details link
@viewDetails=Skatīt detalizētu informāciju
#XTOL
tooltipTxt=Vairāk
#XTOL
delete=Dzēst
#XBTN: ok button text
btnOk=Labi
#XBTN: cancel button text
btnCancel=Atcelt
#XBTN: save button text
btnSave=Saglabāt
#XMSG Error: Object has duplicated technical name.
VAL_OBJECT_DUPLICATE_NAME=Objekts “{0}” jau pastāv repozitorijā. Lūdzu, ievadiet citu nosaukumu.
#XMSG: loading message while opening task chain
loadTaskChain=Notiek uzdevumu ķēdes ielāde...
#model properties
#XFLD
@status_panel=Izpildes statuss
#XFLD
@deploy_status_panel=Izvietošanas statuss
#XFLD
@status_lbl=Statuss
#XFLD
@lblLastExecuted=Pēdējā izpilde
#XFLD
@lblNotExecuted=Vēl nav izpildīts
#XFLD
@lblNotDeployed=Nav izvietots
#XFLD
errorDetailsTxt=Nevarēja ienest izpildes statusu
#XBTN: Schedule dropdown menu
SCHEDULE=Grafiks
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Rediģēt grafiku
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Dzēst grafiku
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Izveidot grafiku
#XLNK
viewDetails=Skatīt detalizētu informāciju
#XMSG: error message for reading execution status from backend
backendErrorMsg=Šķiet, pašlaik no servera dati netiek ielādēti. Mēģiniet ienest datus vēlreiz.
#XFLD: Status text for Completed
@statusCompleted=Pabeigts
#XFLD: Status text for Running
@statusRunning=Tiek izpildīts
#XFLD: Status text for Failed
@statusFailed=Neizdevās
#XFLD: Status text for Stopped
@statusStopped=Apturēts
#XFLD: Status text for Stopping
@statusStopping=Tiek apturēts
#XFLD
@LoaderTitle=Ielāde
#XFLD
#XFLD: Deploy status, dataflow has  been deployed / deployment date exists
@deployStatusActive=Izvietots
@deployStatusRevised=Lokālie atjauninājumi
@deployStatusFailed=Neizdevās
@deployStatusPending=Notiek izvietošana...
@LoaderText=Detalizētās informācijas ienese no servera
#XMSG
@msgDetailFetchError=Kļūda, ienesot detalizēto informāciju no servera
#XFLD
@executeError=Kļūda
#XFLD
@executeWarning=Brīdinājums
#XMSG
@executeConfirmDialog=Informācija
#XMSG
@executeunsavederror=Pirms uzdevumu ķēdes izpildes saglabājiet to.
#XMSG
@executemodifiederror=Uzdevumu ķēdē ir nesaglabātas izmaiņas. Lūdzu, saglabājiet tās.
#XMSG
@executerunningerror=Uzdevumu ķēde pašlaik tiek izpildīta. Pirms sākat jaunu izpildi, uzgaidiet, līdz pašreizējā tiek pabeigta.
#XMSG
@btnExecuteAnyway=Vienalga izpildīt
#XMSG
@msgExecuteWithValidations=Uzdevumu ķēdei ir pārbaudes kļūdas. Uzdevumu ķēdes izpilde var beigties ar kļūmi.
#XMSG
@msgRunDeployedVersion=Ir izvietojamas izmaiņas. Tiks izpildīta uzdevumu ķēdes pēdējā izvietotā versija. Vai vēlaties turpināt?
#XMSG
#XMSG
@navToMonitoring=Atvērt uzdevumu ķēdes pārraugā
#XMSG
txtOR=VAI
#XFLD
@preview=Priekšskatījums
#XMSG
txtand=un
#NOTR
@dateFormat=yyyy-MM-dd
#NOTR
@dateTimeFormatFilter=yyyy-MM-dd HH:mm:ss.
#NOTR
@timeFormat=HH:mm:ss
@lblColum=Kolonna
#XFLD
@lblCondition=Nosacījums
#XFLD
@lblValue=Vērtība
#XMSG
@msgJsonInvalid=Uzdevumu ķēdi nevarēja saglabāt, jo JSON ir kļūdas. Lūdzu, pārbaudiet un atrisiniet.
#XTIT
@msgSaveFailTitle=Nederīgs JSON.
#XMSG
NOT_CHAINABLE=Objektu “{0}” nevar pievienot uzdevumu ķēdei.
#XMSG
NOT_CHAINABLE_REMOTETABLE=Objekts ''{0}'': replicēšanas tips tiks mainīts.
#XMSG
searchTaskChain=Meklēt objektus

#XFLD
@txtTaskChain=Uzdevumu ķēde
#XFLD
@txtRemoteTable=Attālā tabula
#XFLD
@txtRemoveData=Noņemt replicētos datus
#XFLD
@txtRemovePersist=Noņemt pastāvīgos datus
#XFLD
@txtView=Skats
#XFLD
@txtDataFlow=Datu plūsma
#XFLD
@txtIL=Intelektiskā uzmeklēšana
#XFLD
@txtTransformationFlow=Transformācijas plūsma
#XFLD
@txtReplicationFlow=Replicēšanas plūsma
#XFLD
@txtDeltaLocalTable=Lokālā tabula
#XFLD
@txtBWProcessChain=BW procesu ķēde
#XFLD
@txtSQLScriptProcedure=SQL skripta procedūra
#XFLD
@txtAPI=API
#XFLD
@txtMerge=Sapludināt
#XFLD
@txtOptimize=Optimizēt
#XFLD
@txtVacuum=Dzēst ierakstus

#XFLD
@txtRun=Izpildīt
#XFLD
@txtPersist=Pastāvēt
#XFLD
@txtReplicate=Replicēt
#XFLD
@txtDelete=Dzēst ierakstus ar izmaiņu tipu "Dzēsts"
#XFLD
@txtRunTC=Izpildīt uzdevumu ķēdi
#XFLD
@txtRunBW=Izpildīt BW procesu ķēdi
#XFLD
@txtRunSQLScriptProcedure=Izpildīt SQL skripta procedūru

#XFLD
@txtRunDataFlow=Izpildīt datu plūsmu
#XFLD
@txtPersistView=Pastāvošais skats
#XFLD
@txtReplicateTable=Replicēt tabulu
#XFLD
@txtRunIL=Izpildīt intelektisko uzmeklēšanu
#XFLD
@txtRunTF=Izpildīt transformācijas plūsmu
#XFLD
@txtRunRF=Izpildīt replicēšanas plūsmu
#XFLD
@txtRemoveReplicatedData=Noņemt replicētos datus
#XFLD
@txtRemovePersistedData=Noņemt pastāvīgos datus
#XFLD
@txtMergeData=Sapludināt
#XFLD
@txtOptimizeData=Optimizēt
#XFLD
@txtVacuumData=Dzēst ierakstus
#XFLD
@txtRunAPI=Palaist API

#XFLD storage type text
hdlfStorage=Fails

@statusNew=Nav izvietots
@statusActive=Izvietots
@statusRevised=Lokālie atjauninājumi
@statusPending=Notiek izvietošana...
@statusChangesToDeploy=Izvietojamās izmaiņas
@statusDesignTimeError=Dizaina laika kļūda
@statusRunTimeError=Izpildlaika kļūda

#XTIT
txtNodes=Objekti uzdevumu ķēdē ({0})
#XBTN
@deleteNodes=Dzēst

#XMSG
@txtDropDataToDiagram=Velciet un nometiet objektus diagrammā.
#XMSG
@noData=Nav objektu

#XFLD
@txtTaskPosition=Uzdevuma pozīcija

#input parameters and variables
#XFLD
lblInputParameters=Ievades parametri
#XFLD
ip_name=Nosaukums
#XFLD
ip_value=Vērtība
#XTEXT
@noObjectsFound=Nav atrasts neviens objekts

#XMSG
@msgExecuteSuccess=Uzdevumu ķēdes izpilde ir sākta.
#XMSG
@msgExecuteFail=Neizdevās izpildīt uzdevumu ķēdi.
#XMSG
@msgDeployAndRunSuccess=Uzdevumu ķēdes izvietošana un izpilde ir sākta.
#XMSG
@msgDeployAndRunFail=Neizdevās izvietot un izpildīt uzdevumu ķēdi.
#XMSG
@titleExecuteBusy=Lūdzu, uzgaidiet.
#XMSG
@msgExecuteBusy=Jūsu dati tiek sagatavoti, lai sāktu izpildīt uzdevumu ķēdi.
#XMSG
@msgAPITestRunSuccess=API testa izpilde ir sākusies.
#XMSG
@msgAPIExecuteBusy=Jūsu dati tiek sagatavoti, lai sāktu API testa izpildi.

#XTOL
txtOpenInEditor=Atvērt redaktorā
#XTOL
txtPreviewData=Priekšskatīt datus

#datapreview
#XMSG
@msgDataPreviewNotSupp=Datu priekšskatījums šim objektam nav pieejams.

#XMSG Error: empty model
VAL_MODEL_EMPTY=Jūsu modelis, šķiet, ir tukšs. Lūdzu, pievienojiet kādus objektus.
#XMSG Error: deploy model
@msgDeployBeforeRun=Pirms uzdevumu ķēdes izpildes jums tā ir jāizvieto.
#BTN: close dialog
btnClose=Aizvērt

#XMSG Object is not Deployed
VAL_NOT_DEPLOYED=Lai turpinātu ar uzdevumu ķēdi, jums ir jāizvieto objekts “{0}”.
#XMSG Object is having runtime error
VAL_RUNTIME_ERROR=Objekts “{0}” atgriež izpildlaika kļūdu. Lūdzu, pārbaudiet objektu.
#XMSG Object is having design time error
VAL_DESIGNTIME_ERROR=Objekts “{0}” atgriež izstrādes laika kļūdu. Lūdzu, pārbaudiet objektu.
#XMSG Procedure Object has been deleted
VAL_DELETED_PROCEDURE=Ir izdzēstas šādas procedūras: {0}
#XMSG New Parameter added to Procedure object
VAL_NEW_PARAMETER_ADDED=Jauni parametri pievienoti procedūrai "{1}": "{0}". Lūdzu, izvietojiet uzdevumu ķēdi vēlreiz.
#XMSG Parameter removed from Procedure object
VAL_PARAMETER_DELETED=Parametri noņemti no procedūras "{1}": "{0}". Lūdzu, izvietojiet uzdevumu ķēdi vēlreiz.
#XMSG Parameter data type changed in Procedure object
VAL_PARAMETER_DATATYPE_MODIFIED=Parametra "{0}" datu tips mainīts no "{1}" uz "{2}" procedūrā "{3}".
#XMSG Parameter length changed of specific datatype in Procedure object
VAL_PARAMETER_LENGTH_MODIFIED=Parametra "{0}" garums mainīts no "{1}" uz "{2}" procedūrā "{3}".
#XMSG Parameter precision changed of Decimal datatype in Procedure object
VAL_PARAMETER_PRECISION_MODIFIED=Parametra "{0}" precizitāte mainīta no "{1}" uz "{2}" procedūrā "{3}".
#XMSG Parameter scale changed of Decimal datatype in Procedure object
VAL_PARAMETER_SCALE_MODIFIED=Parametra "{0}" mērogs mainīts no "{1}" uz "{2}" procedūrā "{3}".
#XMSG Parameter value not provided in Procedure object
VAL_SQL_SCRIPT_PROCEDURE_PARAMETER_VALUE_MISSING=Jūs neievadījāt vērtības ievades parametriem, kuri ir nepieciešami procedūrai "{0}": "{1}"
#XMSG Remote Table activity changes to Remove Replicated Data
VAL_REMOVE_REPLICATED_DATA=Uzdevumu ķēdes nākamā izpilde mainīs datu piekļuves tipu, un dati vairs netiks augšupielādēti reāllaikā.
#XMSG Remote Table activity changes
REPLICATION_TYPE_CHANGED=Objekts "{0}": replicēšanas tips tiks mainīts.

#XFLD
@lblStartNode=Sākuma mezgls
#XFLD
@lblEndNode=Beigu mezgls
#XFLD
@linkTo=No {0} līdz {1}
#XTOL
@txtViewDetails=Skatīt detalizētu informāciju

#XTOL
txtOpenImpactLineage=Ietekmes un izcelsmes analīze
#XFLD
@emailNotifications=E-pasta paziņojumi
#XFLD
@txtReset=Atiestatīt
#XFLD
@emailMsgWarning=Ja e-pasta ziņojums ir tukšs, tiek nosūtīts noklusējuma veidnes e-pasta ziņojums
#XFLD
@notificationSettings=Paziņojumu iestatījumi
#XFLD
@recipientEmailAddr=Saņēmēja e-pasta adrese
#XFLD
@emailSubject=E-pasta tēma
@emailSubjectText=Uzdevumu ķēde <TASKCHAIN_NAME> pabeigta ar statusu <STATUS>
#XFLD
@emailMessage=E-pasta ziņojums
@emailMessageText=Cien. lietotāj!\n\n Ar šo paziņojam, ka uzdevumu ķēde:<TASKCHAIN_NAME> ar izpildi plkst. <START_TIME> tika pabeigta ar statusu <STATUS>. Izpilde beidzās plkst. <END_TIME>.\n\nDetalizēta informācija:\nVieta:<SPACE_NAME>\nKļūda:<ERROR_MESSAGE>
@selectEmailRecipientDialogTitle=Atlasīt saņēmēja e-pasta adresi
@tenantMembers=Nomnieka dalībnieki
@others=Citi ({0})
@selectedEmailAddress=Atlasītie saņēmēji
@add=Pievienot
@placeholder=Vietturis
@description=Apraksts
@copyText=Kopēt tekstu
@taskchainDetailsPlaceholder=Vietturi detalizētai informācijai par uzdevumu ķēdi
@placeholderCopied=Vietturis ir kopēts
@invalidEmailInfo=Ievadīt pareizu e-pasta adresi
@maxMembersAlreadyAdded=Jūs jau pievienojāt maksimumu 20 dalībnieki
@enterEmailAddress=Ievadīt lietotāja e-pasta adresi
@inCorrectPlaceHolder={0} nav paredzētais vietturis e-pasta pamattekstā.
@nsOFF=Nesūtīt nevienu paziņojumu
@nsFAILED=Sūtīt e-pasta paziņojumu tikai tad, ja izpilde tiek pabeigta ar kļūdu
@nsCOMPLETED=Sūtīt e-pasta paziņojumu tikai tad, ja izpilde tiek sekmīgi pabeigta
@nsANY=Sūtīt e-pastu tikai tad, ja izpilde tiek pabeigta
@phStatus=Uzdevumu ķēdes statuss — IZDEVĀS|NEIZDEVĀS
@phTaskChainTName=Uzdevumu ķēdes tehniskais nosaukums
@phTaskChainBName=Uzdevumu ķēdes biznesa nosaukums
@phLogId=Izpildes žurnāla ID
@phUser=Lietotājs, kas izpilda uzdevumu ķēdi
@phLogUILink=Saite uz uzdevumu ķēdes žurnāla attēlojumu
@phStartTime=Izpildes sākuma laiks
@phEndTime=Izpildes beigu laiks
@phErrMsg=Pirmais kļūdas ziņojums uzdevumu žurnālā. SEKMĪGAS IZPILDES gadījumā žurnāls ir tukšs
@phSpaceName=Vietas tehniskais nosaukums
@emailFormatError=Nederīgs e-pasta formāts
@emailFormatErrorInListText=Tādā dalībnieku sarakstā, kas nav nomnieki, ir ievadīts nederīgs e-pasta formāts.
@emailSubjectTemplateText=Uzdevumu ķēdes paziņojums: $$taskChainName$$ - vieta: $$spaceId$$ - statuss: $$status$$
@emailMessageTemplateText=Labdien!\n\n Jūsu uzdevumu ķēde ar apzīmējumu $$taskChainName$$ ir pabeigta ar statusu $$status$$. \N Šeit būs papildu detalizēta informācija par uzdevumu ķēdi:\n - Uzdevumu ķēdes tehniskais nosaukums: $$taskChainName$$ \n - Uzdevumu ķēdes izpildes žurnāla ID: $$logId$$ \n - Lietotājs, kas izpildīja uzdevumu ķēdi: $$user$$ \n - Saite uz uzdevumu ķēdes žurnāla attēlojumu: $$uiLink$$ \n - Uzdevumu ķēdes izpildes sākuma laiks: $$startTime$$ \n - Uzdevumu ķēdes izpildes beigu laiks: $$endTime$$ \n - Vietas nosaukums: $$spaceId$$ \n
@deleteEmailRecepient=Dzēst saņēmēju
@emailInputDisabledText=Lūdzu, izvietojiet uzdevumu ķēdi, lai pievienotu e-pasta saņēmējus.
@tenantOwnerDomainMatchErrorText=E-pasta adreses domēns neatbilst nomnieka īpašnieka domēnam: {0}
@totalEmailIdLimitInfoText=Varat atlasīt līdz pat 20 e-pasta saņēmējus, tostarp nomnieka dalībnieka lietotājus un citus saņēmējus.
@emailDomainInfoText=Tiek pieņemtas tikai e-pasta adreses ar domēnu: {0}.
@duplicateEmailErrorText=Sarakstā ir dublēti e-pasta saņēmēji

#XFLD Zorder Title
@txtZorderTitle=Apache Spark Z secības kolonnas

#XFLD Zorder NoColumn
@txtZorderNoColumn=Neviena Z secības kolonna nav atrasta

#XFLD Zorder Column PrimaryKey
@txtZorderColumnPrimaryKey=Primārā atslēga

#XFLD
@lblOperators=Operatori
addNewSelector=Pievienot jaunu uzdevumu
parallelSelector=Pievienot kā paralēlo uzdevumu
replaceSelector=Aizstāt esošo uzdevumu
addparallelbranch=Pievienot kā paralēlo zaru
addplaceholder=Pievienot vietturi
addALLOperation=Operators ALL
addOROperation=Operators ANY
addplaceholdertocanvas=Pievienot audeklam vietturi
addplaceholderonselected=Pievienot vietturi pēc atlasītā uzdevuma
#XFLD: Add Parallel Branch to Selected Task
addparallelbranchonselected=Pievienot paralēlo zarojumu pēc atlasītā uzdevuma
addOperator=Pievienot operatoru
txtAdd=Pievienot
txtPlaceHolderText=Šeit vilkt un nomest uzdevumu
@lblLayout=Izkārtojums

#XMSG
VAL_UNCONNECTED_TASK=Uzdevums “{0}” nav savienots ar uzdevumu ķēdi.
#XMSG
VAL_TASK_SHOULD_HAVE_ONE_INCOMING_LINK=Uzdevumam “{0}” vajadzētu būt tikai vienai ienākošajai saitei.
#XMSG
VAL_TASK_SHOULD_HAVE_INCOMING_LINK=Uzdevumam “{0}” vajadzētu būt ienākošajai saitei.
#XMSG
VAL_UNCONNECTED_OPERATOR=Operators “{0}” nav savienots ar uzdevumu ķēdi.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ATLEAST_TWO_INCOMING_LINK=Operatoram “{0}” vajadzētu būt vismaz divām ienākošajām saitēm.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ONE_OUT_LINK=Operatoram “{0}” vajadzētu būt vismaz vienai izejošajai saitei.
#XMSG
VAL_CIRCULAR_REFERENCE_EXITS=Uzdevumu ķēdē “{0}” pastāv cirkulārā cilpa.
#XMSG
VAL_UNCONNECTED_BRANCH=Objekts/zarojums “{0}” nav savienots ar uzdevumu ķēdi.
#XMSG
VAL_TASK_TRIGGERED_PARALLEL_MORE_THAN_ONE_TIME=Uzdevums “{0}” ir savienots paralēli vairāk nekā vienu reizi. Lai turpinātu, lūdzu, noņemiet dublikātus.


txtBegin=Sākt
txtNodesInLink=Iesaistītie objekti
#XTOL Tooltip for a context button on diagram
openInNewTab=Atvērt jaunā cilnē
#XTOL Tooltip for a context button on diagram
txtConnectToTaskOperator=Vilkt, lai savienotu
@emailUpdateError=Kļūda, atjauninot e-pasta paziņojumu sarakstu

#XMSG
noTeamPrivilegeTxt=Jums nav atļaujas skatīt nomnieku dalībnieku sarakstu. Izmantojiet cilni Citi, lai e-pasta adresātus pievienotu manuāli.

#XFLD Package
@txtPackage=Pakotne

#XMSG warning for repository package assignment changed. {1} - package name
VAL_PACKAGE_CHANGED=Jūs esat piešķīris šo objektu pakotnei “{1}”. Noklikšķiniet uz “Saglabāt”, lai apstiprinātu un pārbaudītu šīs izmaiņas. Ņemiet vērā, ka šajā redaktorā pēc saglabāšanas nevarēsit atsaukt objektu piešķiri pakotnei.
#XMSG warning for repository package dependency issue.
MISSING_DEPENDENCY=Objekta “{0}” atkarības pakotnes “{1}” kontekstā nevar atrisināt.

#Warning messages for the folders in the "Others" tab of task chain editor
#XMSG generic warning if getting the objects in a particular folder fails with unhandled errors
getNonRepoArtefactTypeDescendantsError=Bija problēma izgūt objektus jūsu atlasītajā mapē.
#BW Process Chains folder related warning messages
#XMSG warning if user does not have the necessary permission related to the SAP BW Bridge space
missingPrivilegeForBridgeSpaceError=Jums nav nepieciešamās atļaujas skatīt vai iekļaut BW procesu ķēdes uzdevumu ķēdē.
#XMSG warning if there was a problem retrieving the BW process chains due to bridge space membership, bridge connection issues or other errors
generalBridgeSpaceError=Bija problēma izgūt BW procesu ķēdes.
#XMSG warning if the BW API for retrieving the BW process chains from the SAP BW Bridge tenant fails
bwApiClientError=Bija problēma izgūt BW procesu ķēdes no SAP BW tilta nomnieka.
#XMSG warning if BW process chains list is empty
bwApiEmptyListError=SAP BW tilta nomniekā netika atrasta neviena BW procesu ķēde.
#XMSG: Task log message for invalid OIDC configuration in the SAP BW Bridge tenant
bwBridgeOIDCConfigStatusInvalid=OpenID autentifikācija šim nomniekam nav konfigurēta. Lūdzu, izmantojiet clientID "{0}" un tokenURL "{1}", lai konfigurētu OpenID autentifikāciju SAP BW tilta nomniekā, kā aprakstīts SAP piezīmē 3536298.
#XMSG BW Process Chain Object has been deleted
VAL_DELETED_BW=No SAP tilta nomnieka varbūt tika izdzēstas šādas BW procesu ķēdes: {0}
#BW SQL Script Procedures folder related warning messages
#XFLD waring if the Procedure list is empty
procedureAPIEmptyList=Nav izveidota neviena procedūra, vai Open SQL shēmai nav piešķirta privilēģija EXECUTE.
#Change digram orientations
changeOrientations=Mainīt orientācijas


# placeholder for the API Path
apiPath=Piemēram: /job/v1
# placeholder for the status API Path
statusAPIPath=Piemēram: /job/v1/{id}/status
# valueStateText for the API Path
apiPathRequired=API ceļš ir obligāts
#placeholder for the CSRF Token URL
csrfTokenURL=Tiek atbalstīts tikai HTTPS
# Response Type 1
statusCode=Iegūt rezultātu no HTTP statusa koda
# Response Type 2
locationHeader=Iegūt rezultātu no HTTP statusa koda un atrašanās vietas galvenes
# Response Type 3
responseBody=Iegūt rezultātu no HTTP statusa koda un atbildes pamatteksta
# placeholder for ID
idPlaceholder=Ievadīt JSON ceļu
# placeholder for indicator value
indicatorValue=Ievadīt vērtību
# Placeholder for key
keyPlaceholder=Ievadīt atslēgu
# Error message for missing key
KeyRequired=Atslēga ir obligāta
# Error message for invalid key format
invalidKeyFormat=Jūsu ievadītā galvenes atslēga nav atļauta. Derīgas galvenes ir:<ul><li>"prefer"</li><li>Galvenes, kas sākas ar "x-", izņemot "x-forwarded-host"</li><li>Galvenes, kurās ir burti un cipari, "-" vai "_"</li></ul>
# Error message for missing value
valueRequired=Vērtība ir obligāta
# Error message for invalid characters in value
invalidValueCharacters=Galvenē ir nederīgas rakstzīmes. Ir atļautas šādas speciālās rakstzīmes:\t ";", ":", "-", "_", ",", "?", "/" un "*"
# Validation message for invoke api path
apiPathValidation=Lūdzu, ievadiet derīgu API ceļu, piemēram: /job/v1
# Validation message for JSON path
jsonPathValidation=Lūdzu, ievadiet derīgu JSON ceļu
# Validation message for success/error indicator
indicatorValueValidation=Indikatora vērtībai ir jāsākas ar burtu vai ciparu, un tajā var būt šādas speciālās rakstzīmes:\t "-" un "_"
# Error message for JSON path
jsonPathRequired=JSON ceļš ir obligāts
# Error message for invalid API Technical Name
invalidTechnicalName=Tehniskajā nosaukumā ir nederīga rakstzīmes
# Error message for empty Technical Name
emptyTechnicalName=Tehniskais nosaukums ir obligāts
# Tooltip for codeEditor dialog
codeEditorTooltip=Atvērt JSON rediģēšanas logu
# Status Api path validation message
validationStatusAPIPath=Lūdzu, ievadiet derīgu API ceļu, piemēram: /job/v1/{id}/status
# CSRF token URL validation message
validationCSRFTokenURL=CSRF marķiera URL ir jāsākas ar “https://”, un tam ir jābūt derīgam URL
# Select a connection
selectConnection=Atlasīt savienojumu
# Validation message for connection item error
connectionNotReplicated=Savienojums pašlaik ir nederīgs API uzdevumu veikšanai. Atveriet lietojumprogrammu "Savienojumi" un vēlreiz ievadiet akreditācijas datus, lai izlabotu HTTP savienojumu
# Validation message for API task properties wrong value or missing
VAL_REST_API_PROPERTIES_MISSING=API uzdevumam "{0}" ir nepareizas vērtības vai trūkst vērtību šādiem rekvizītiem: {1}
# Validation message for API connection api is throwing some error or selected connection has some warning
VAL_REST_API_CONNECTION=API uzdevumam "{0}" ir problēma ar HTTP savienojumu. Atveriet lietojumprogrammu "Savienojumi" un pārbaudiet savienojumu
# Validation message for API task properties when delected connection
VAL_REST_API_CONNECTION_DELETED=API uzdevumam "{0}" ir dzēsts "{1}" savienojums
# Validation message for API task properties when connection cannot be consumed in the API task
VAL_REST_API_CONNECTION_CONSUMPTION_WARNING=API uzdevumam "{0}" ir savienojums "{1}", kuru nevar izmantot API uzdevumu darbināšanai. Atveriet lietojumprogrammu "Savienojumi" un vēlreiz ievadiet savus akreditācijas datus, lai izveidotu savienojamību API uzdevumiem
# Information message if synchrnous mode is selected in the API Invocation then we don't need status information
modeInfoText=Režīmā “Sinhrons” statusa panelis netiek attēlots API izsaukumiem
# validation dialog button for Run API Test
saveAnyway=Saglabāt tik un tā
# validation message for technical name
technicalNameValidation=Tehniskajam nosaukumam uzdevumu ķēdē ir jābūt unikālam. Lūdzu, izvēlieties citu tehnisko nosaukumu
# Connection error message
getHttpConnectionsError=Neizdevās iegūt HTTP savienojumus
# Msg for the API test run when the Task chain is not saved
taskChainNotSaved=Uzdevumu ķēde ir jāsaglabā pirms API testa izpildes palaišanas
# Msg failed to run API test run
@failedToRunAPI=Neizdevās palaist API testa izpildi
# Msg for the API test run when its already in running state
apiTaskRunning=API testa izpilde jau notiek. Vai vēlaties sākt jaunu testa izpildi?

topToBtm=No augšas uz apakšu
leftToRight=No kreisās puses uz labo

#XTIT Apache Spark Application Settings
txtHeaderApacheSettings=Apache Spark lietojumprogrammas iestatījumi
#XFLD Use Default
txtUseSpaceDefault=Izmantot noklusējumu
#XFLD Application
txtApplication=Lietojumprogramma
#XFLD Define new settings for this Task
txtNewSettings=Definēt jaunus iestatījumus šim uzdevumam

#XFLD Use Default
txtUseDefault=Izmantot noklusējumu




