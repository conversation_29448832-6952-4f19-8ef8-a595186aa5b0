#XTOL Undo
@undo=撤销
#XTOL Redo
@redo=重做
#XTOL Delete Selected Symbol
@deleteNode=删除选定符号
#XTOL Zoom to Fit
@zoomToFit=缩放到合适大小
#XTOL Auto Layout
@autoLayout=自动布局
#XMSG
@welcomeText=将对象从左面板拖放到此画布。
#XMSG
@txtNoData=你似乎尚未添加任何对象。
#XMSG
VAL_ENTER_VALID_STRING_GEN=请输入长度小于等于 {0} 的有效字符串。
#XMSG
@noParametersMsg=这个程序没有输入参数。
#XMSG
@ip_enterValueMsg="{0}"（运行 SQL 脚本程序）存在输入参数。可以为每个参数设置一个值。
#XTOL
@validateModel=验证消息
#XTOL
@hierarchy=层次结构
#XTOL
@columnCount=列数
#XFLD
@yes=是
#XFLD
@no=否
#XTIT Save Dialog param
@modelNameTaskChain=任务链
#properties panel
@lblPropertyTitle=属性
#XFLD
@lblGeneral=常规
#XFLD : Setting
@lblSetting=设置
#XTXT: Delta Enabled Local Table Deletion
txtDeltaTableDeletion=删除所有已完全处理并符合以下条件的记录：更改类型为 "已删除" 且时间早于
#XTXT: Delta Enabled Local Table Deletion Input field description
desDeltaTableDeletion=天
#XFLD: Data Activation label
@lblDataActivation=数据激活
#XFLD: Latency label
@latency=延迟
#XTEXT: Text for Latency dropdown
txtLatencyDefault=默认
#XTEXT: Text 1 hour
txtOneHour=1 小时
#XTEXT: Text 2 hours
txtTwoHours=2 小时
#XTEXT: Text 3 hours
txtThreeHours=3 小时
#XTEXT: Text 4 hours
txtFourHours=4 小时
#XTEXT: Text 6 hours
txtSixHours=6 小时
#XTEXT: Text 12 hours
txtTwelveHours=12 小时
#XTEXT: Text 1 day
txtOneDay=1 天
#XFLD: Latency label
@autoRestartHead=自动重启
#XFLD
@lblConnectionName=连接
#XFLD
@lblQualifiedName=限定名称
#XFLD
@lblSpaceName=空间名称
#XFLD
@lblLocalSchemaName=本地模式
#XFLD
@lblType=对象类型
#XFLD
@lblActivity=活动
#XFLD
@lblTableName=表名称
#XFLD
@lblBusinessName=业务名称
#XFLD
@lblTechnicalName=技术名称
#XFLD
@lblSpace=空间
#XFLD
@lblLabel=标签
#XFLD
@lblDataType=数据类型
#XFLD
@lblDescription=说明
#XFLD
@lblStorageType=存储
#XFLD
@lblHTTPConnection=通用 HTTP 连接
#XFLD
@lblAPISettings=通用 API 设置
#XFLD
@header=标头
#XFLD
@lblInvoke=API 调用
#XFLD
@lblMethod=方法
#XFLD
@lblUrl=基本 URL
#XFLD
@lblAPIPath=API 路径
#XFLD
@lblMode=模式
#XFLD
@lblCSRFToken=需要输入 CSRF 令牌
#XFLD
@lblTokenURL=CSRF 令牌 URL
#XFLD
@csrfTokenInfoText=如果没有输入，将使用基本 URL 和 API 路径
#XFLD
@lblCSRF=CSRF 令牌
#XFLD
@lblRequestBody=请求正文
#XFLD
@lblFormat=格式
#XFLD
@lblResponse=响应
#XFLD
@lblId=用于检索状态的 ID
#XFLD
@Id=ID
#XFLD
@lblSuccessIndicator=成功指示器
#XFLD
@lblErrorIndicator=错误指示器
#XFLD
@lblErrorReason=错误原因
#XFLD
@lblStatus=状态
#XFLD
@lblApiTestRun=API 测试运行
#XFLD
@lblRunStatus=运行状态
#XFLD
@lblLastRan=上次运行日期
#XFLD
@lblTestRun=测试运行
#XFLD
@lblDefaultHeader=默认标头字段（键值对）
#XFLD
@lblAdditionalHeader=额外的标头字段
#XFLD
@lblKey=键
#XFLD
@lblEditJSON=编辑 JSON
#XFLD
@lblTasks=任务
#XFLD
@lblRESTfullTask=API
#XFLD: add field button text
btnAddField=添加标头字段
#XFLD: view Details link
@viewDetails=查看详细信息
#XTOL
tooltipTxt=更多
#XTOL
delete=删除
#XBTN: ok button text
btnOk=确定
#XBTN: cancel button text
btnCancel=取消
#XBTN: save button text
btnSave=保存
#XMSG Error: Object has duplicated technical name.
VAL_OBJECT_DUPLICATE_NAME=资源库中已存在对象 “{0}”。请输入其他名称。 
#XMSG: loading message while opening task chain
loadTaskChain=正在加载任务链...
#model properties
#XFLD
@status_panel=运行状态
#XFLD
@deploy_status_panel=部署状态
#XFLD
@status_lbl=状态
#XFLD
@lblLastExecuted=上次运行
#XFLD
@lblNotExecuted=尚未运行
#XFLD
@lblNotDeployed=未部署
#XFLD
errorDetailsTxt=无法获取运行状态
#XBTN: Schedule dropdown menu
SCHEDULE=计划
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=编辑计划
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=删除计划
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=创建计划
#XLNK
viewDetails=查看详细信息
#XMSG: error message for reading execution status from backend
backendErrorMsg=似乎当前未在从服务器加载数据。请重新尝试获取数据。
#XFLD: Status text for Completed
@statusCompleted=已完成
#XFLD: Status text for Running
@statusRunning=运行中
#XFLD: Status text for Failed
@statusFailed=失败
#XFLD: Status text for Stopped
@statusStopped=已停止
#XFLD: Status text for Stopping
@statusStopping=正在停止
#XFLD
@LoaderTitle=正在加载
#XFLD
#XFLD: Deploy status, dataflow has  been deployed / deployment date exists
@deployStatusActive=已部署
@deployStatusRevised=本地更新
@deployStatusFailed=失败
@deployStatusPending=正在部署...
@LoaderText=正在从服务器获取详细信息
#XMSG
@msgDetailFetchError=从服务器获取详细信息时出错
#XFLD
@executeError=错误
#XFLD
@executeWarning=警告
#XMSG
@executeConfirmDialog=信息
#XMSG
@executeunsavederror=运行前先保存任务链。
#XMSG
@executemodifiederror=任务链中存在未保存的更改。请保存。
#XMSG
@executerunningerror=任务链当前正在运行。请等待当前运行完成后，再开始新的运行。
#XMSG
@btnExecuteAnyway=仍要运行
#XMSG
@msgExecuteWithValidations=任务链存在验证错误。运行任务链可能失败。
#XMSG
@msgRunDeployedVersion=存在要部署的更改。将运行任务链的上次部署版本。是否要继续？
#XMSG
#XMSG
@navToMonitoring=在任务链监控器中打开
#XMSG
txtOR=或
#XFLD
@preview=预览
#XMSG
txtand=与
#NOTR
@dateFormat=yyyy-MM-dd
#NOTR
@dateTimeFormatFilter=yyyy-MM-dd HH:mm:ss.
#NOTR
@timeFormat=HH:mm:ss
@lblColum=列
#XFLD
@lblCondition=条件
#XFLD
@lblValue=值
#XMSG
@msgJsonInvalid=没能保存任务链，JSON 中有错误。请检查并解决错误。
#XTIT
@msgSaveFailTitle=JSON 无效。
#XMSG
NOT_CHAINABLE=无法将对象 “{0}” 添加到任务链。
#XMSG
NOT_CHAINABLE_REMOTETABLE=对象 “{0}”：复制类型将更改。
#XMSG
searchTaskChain=搜索对象

#XFLD
@txtTaskChain=任务链
#XFLD
@txtRemoteTable=远程表
#XFLD
@txtRemoveData=移除复制的数据
#XFLD
@txtRemovePersist=移除持久数据
#XFLD
@txtView=视图
#XFLD
@txtDataFlow=数据流
#XFLD
@txtIL=智能查找
#XFLD
@txtTransformationFlow=转换流
#XFLD
@txtReplicationFlow=复制流
#XFLD
@txtDeltaLocalTable=本地表
#XFLD
@txtBWProcessChain=BW 流程链
#XFLD
@txtSQLScriptProcedure=SQL 脚本程序
#XFLD
@txtAPI=API
#XFLD
@txtMerge=合并
#XFLD
@txtOptimize=优化
#XFLD
@txtVacuum=删除记录

#XFLD
@txtRun=运行
#XFLD
@txtPersist=保持
#XFLD
@txtReplicate=复制
#XFLD
@txtDelete=删除更改类型为 "已删除" 的记录
#XFLD
@txtRunTC=运行任务链
#XFLD
@txtRunBW=运行 BW 流程链
#XFLD
@txtRunSQLScriptProcedure=运行 SQL 脚本程序

#XFLD
@txtRunDataFlow=运行数据流
#XFLD
@txtPersistView=持久化视图
#XFLD
@txtReplicateTable=复制表
#XFLD
@txtRunIL=运行智能查找
#XFLD
@txtRunTF=运行转换流
#XFLD
@txtRunRF=运行复制流
#XFLD
@txtRemoveReplicatedData=移除复制的数据
#XFLD
@txtRemovePersistedData=移除持久数据
#XFLD
@txtMergeData=合并
#XFLD
@txtOptimizeData=优化
#XFLD
@txtVacuumData=删除记录
#XFLD
@txtRunAPI=运行 API

#XFLD storage type text
hdlfStorage=文件

@statusNew=未部署
@statusActive=已部署
@statusRevised=本地更新
@statusPending=正在部署...
@statusChangesToDeploy=待部署更改
@statusDesignTimeError=设计时错误
@statusRunTimeError=运行时错误

#XTIT
txtNodes=任务链中的对象（{0}）
#XBTN
@deleteNodes=删除

#XMSG
@txtDropDataToDiagram=将对象拖放到图表。
#XMSG
@noData=无对象

#XFLD
@txtTaskPosition=任务位置

#input parameters and variables
#XFLD
lblInputParameters=输入参数
#XFLD
ip_name=名称
#XFLD
ip_value=值
#XTEXT
@noObjectsFound=找不到对象

#XMSG
@msgExecuteSuccess=任务链运行已开始。
#XMSG
@msgExecuteFail=无法运行任务链。
#XMSG
@msgDeployAndRunSuccess=任务链部署和运行已开始。
#XMSG
@msgDeployAndRunFail=无法部署并运行任务链。
#XMSG
@titleExecuteBusy=请稍候。
#XMSG
@msgExecuteBusy=我们正在准备你的数据，以开始运行任务链。
#XMSG
@msgAPITestRunSuccess=API 测试运行已开始。
#XMSG
@msgAPIExecuteBusy=我们正在准备数据，以便开始运行 API 测试运行。

#XTOL
txtOpenInEditor=在编辑器中打开
#XTOL
txtPreviewData=预览数据

#datapreview
#XMSG
@msgDataPreviewNotSupp=对此对象不提供数据预览。

#XMSG Error: empty model
VAL_MODEL_EMPTY=你的模型似乎为空。请添加一些对象。
#XMSG Error: deploy model
@msgDeployBeforeRun=需要先部署任务链，然后才能运行。
#BTN: close dialog
btnClose=关闭

#XMSG Object is not Deployed
VAL_NOT_DEPLOYED=要继续运行任务链，必须先部署对象“{0}”。
#XMSG Object is having runtime error
VAL_RUNTIME_ERROR=对象 "{0}" 返回运行时错误。请检查对象。
#XMSG Object is having design time error
VAL_DESIGNTIME_ERROR=对象 "{0}" 返回设计时错误。请检查对象。
#XMSG Procedure Object has been deleted
VAL_DELETED_PROCEDURE=以下程序已删除：{0}
#XMSG New Parameter added to Procedure object
VAL_NEW_PARAMETER_ADDED=新参数已添加到程序 "{1}"："{0}"。请重新部署任务链。
#XMSG Parameter removed from Procedure object
VAL_PARAMETER_DELETED=参数已从程序 "{1}"："{0}" 中移除。请重新部署任务链。
#XMSG Parameter data type changed in Procedure object
VAL_PARAMETER_DATATYPE_MODIFIED=在程序 "{3}" 中，参数 "{0}" 数据类型已从 "{1}" 更改为 "{2}"。
#XMSG Parameter length changed of specific datatype in Procedure object
VAL_PARAMETER_LENGTH_MODIFIED=在程序 "{3}" 中，参数 "{0}" 长度已从 "{1}" 更改为 "{2}"。
#XMSG Parameter precision changed of Decimal datatype in Procedure object
VAL_PARAMETER_PRECISION_MODIFIED=在程序 "{3}" 中，参数 "{0}" 精度已从 "{1}" 更改为 "{2}"。
#XMSG Parameter scale changed of Decimal datatype in Procedure object
VAL_PARAMETER_SCALE_MODIFIED=在程序 "{3}" 中，参数 "{0}" 小数位数已从 "{1}" 更改为 "{2}"。
#XMSG Parameter value not provided in Procedure object
VAL_SQL_SCRIPT_PROCEDURE_PARAMETER_VALUE_MISSING=还没有输入程序 "{0}" 所需的输入参数 的值："{1}"
#XMSG Remote Table activity changes to Remove Replicated Data
VAL_REMOVE_REPLICATED_DATA=下次运行任务链时会更改数据访问类型，且数据将不再实时上载。
#XMSG Remote Table activity changes
REPLICATION_TYPE_CHANGED=对象 "{0}"：复制类型将更改。

#XFLD
@lblStartNode=开始节点
#XFLD
@lblEndNode=结束节点
#XFLD
@linkTo={0} 至 {1}
#XTOL
@txtViewDetails=查看详细信息

#XTOL
txtOpenImpactLineage=影响和沿袭分析
#XFLD
@emailNotifications=电子邮件通知
#XFLD
@txtReset=重置
#XFLD
@emailMsgWarning=电子邮件消息为空时，发送默认模板电子邮件
#XFLD
@notificationSettings=通知设置
#XFLD
@recipientEmailAddr=收件人电子邮件地址
#XFLD
@emailSubject=电子邮件主题
@emailSubjectText=任务链 <TASKCHAIN_NAME> 已完成，状态为：<STATUS>
#XFLD
@emailMessage=电子邮件消息
@emailMessageText=尊敬的用户：\n\n<START_TIME> 开始运行的任务链 <TASKCHAIN_NAME> 已完成，状态为：<STATUS>，请知晓。执行在 <END_TIME> 结束。\n\n详细信息：\n空间：<SPACE_NAME>\n错误：<ERROR_MESSAGE>
@selectEmailRecipientDialogTitle=选择收件人电子邮件地址
@tenantMembers=租户成员
@others=其他（{0}）
@selectedEmailAddress=选择的收件人
@add=添加
@placeholder=占位符
@description=说明
@copyText=复制文本
@taskchainDetailsPlaceholder=任务链占位符详细信息
@placeholderCopied=占位符已复制
@invalidEmailInfo=输入正确的电子邮件地址
@maxMembersAlreadyAdded=已添加 20 个（上限）成员
@enterEmailAddress=输入用户电子邮件地址
@inCorrectPlaceHolder={0} 不是电子邮件正文中应有的占位符。
@nsOFF=不发送任何通知
@nsFAILED=仅在运行完成出错时发送电子邮件通知
@nsCOMPLETED=仅在运行成功完成时发送电子邮件通知
@nsANY=运行完成时发送电子邮件
@phStatus=任务链的状态 - 成功|失败
@phTaskChainTName=任务链的技术名称
@phTaskChainBName=任务链的业务名称
@phLogId=运行的日志 ID
@phUser=运行任务链的用户
@phLogUILink=任务链日志显示的链接
@phStartTime=运行的开始时间
@phEndTime=运行的结束时间
@phErrMsg=任务日志中的第一条错误消息。如果状态为成功，则日志为空
@phSpaceName=空间的技术名称
@emailFormatError=电子邮件格式无效
@emailFormatErrorInListText=非租户成员列表中输入的电子邮件格式无效。
@emailSubjectTemplateText=任务链的通知：$$taskChainName$$ - 空间：$$spaceId$$ - 状态：$$status$$
@emailMessageTemplateText=你好，\n\n标有 $$taskChainName$$ 的任务链已完成，状态为：$$status$$。\n以下是有关此任务链的其他一些详细信息：\n - 任务链技术名称：$$taskChainName$$ \n - 任务链运行的日志 ID：$$logId$$ \n - 运行任务链的用户：$$user$$ \n - 任务链日志显示的链接：$$uiLink$$ \n - 任务链运行的开始时间：$$startTime$$ \n - 任务链运行的结束时间：$$endTime$$ \n - 空间名称：$$spaceId$$ \n
@deleteEmailRecepient=删除收件人
@emailInputDisabledText=请部署任务链，以添加电子邮件收件人。
@tenantOwnerDomainMatchErrorText=电子邮件地址域与租户所有者域不匹配：{0}
@totalEmailIdLimitInfoText=可选择最多 20 个电子邮件收件人，包括租户成员用户和其他收件人。
@emailDomainInfoText=仅接受域为 {0} 的电子邮件地址。
@duplicateEmailErrorText=列表中存在重复的电子邮件收件人。

#XFLD Zorder Title
@txtZorderTitle=Apache Spark Z-Order 列

#XFLD Zorder NoColumn
@txtZorderNoColumn=找不到 Z-Order 列

#XFLD Zorder Column PrimaryKey
@txtZorderColumnPrimaryKey=主键

#XFLD
@lblOperators=运算符
addNewSelector=添加为新任务
parallelSelector=添加为并行任务
replaceSelector=替换现有任务
addparallelbranch=添加为并行分支
addplaceholder=添加占位符
addALLOperation=ALL 运算符
addOROperation=ANY 运算符
addplaceholdertocanvas=添加占位符至画布
addplaceholderonselected=在选定任务后添加占位符
#XFLD: Add Parallel Branch to Selected Task
addparallelbranchonselected=在选定任务后添加并行分支
addOperator=添加运算符
txtAdd=添加
txtPlaceHolderText=拖放任务到此处
@lblLayout=布局

#XMSG
VAL_UNCONNECTED_TASK=任务 “{0}” 未连接到任务链。
#XMSG
VAL_TASK_SHOULD_HAVE_ONE_INCOMING_LINK=任务 “{0}” 应仅有一个传入链接。
#XMSG
VAL_TASK_SHOULD_HAVE_INCOMING_LINK=任务 “{0}” 应有一个传入链接。
#XMSG
VAL_UNCONNECTED_OPERATOR=运算符 "{0}" 未连接到任务链。
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ATLEAST_TWO_INCOMING_LINK=运算符 “{0}” 应至少有两个传入链接。
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ONE_OUT_LINK=运算符 “{0}” 应至少有一个传出链接。
#XMSG
VAL_CIRCULAR_REFERENCE_EXITS=任务链 “{0}” 中存在循环。
#XMSG
VAL_UNCONNECTED_BRANCH=对象/分支 “{0}” 未连接到任务链。
#XMSG
VAL_TASK_TRIGGERED_PARALLEL_MORE_THAN_ONE_TIME=任务 “{0}” 已多次并行连接。请移除重复项以继续。


txtBegin=开始
txtNodesInLink=涉及的对象
#XTOL Tooltip for a context button on diagram
openInNewTab=在新选项卡中打开
#XTOL Tooltip for a context button on diagram
txtConnectToTaskOperator=拖动以连接
@emailUpdateError=更新电子邮件通知列表时出错。

#XMSG
noTeamPrivilegeTxt=你没有查看租户成员列表的权限。请使用 “其他” 选项卡，手动添加电子邮件收件人。

#XFLD Package
@txtPackage=包

#XMSG warning for repository package assignment changed. {1} - package name
VAL_PACKAGE_CHANGED=已将这个对象已经分配给包 "{1}"。点击 "保存"，确认并验证这项更改。请注意，保存后将不能在这个编辑器中撤销对包的分配。
#XMSG warning for repository package dependency issue.
MISSING_DEPENDENCY=对象 "{0}" 的依赖项不能在包 "{1}" 的产品上下文中解析。

#Warning messages for the folders in the "Others" tab of task chain editor
#XMSG generic warning if getting the objects in a particular folder fails with unhandled errors
getNonRepoArtefactTypeDescendantsError=在选择的文件夹中检索对象时出现问题。
#BW Process Chains folder related warning messages
#XMSG warning if user does not have the necessary permission related to the SAP BW Bridge space
missingPrivilegeForBridgeSpaceError=你没有查看 BW 流程链或将其纳入任务链所需的权限。
#XMSG warning if there was a problem retrieving the BW process chains due to bridge space membership, bridge connection issues or other errors
generalBridgeSpaceError=检索 BW 流程链时出现问题。
#XMSG warning if the BW API for retrieving the BW process chains from the SAP BW Bridge tenant fails
bwApiClientError=从 SAP BW 网桥租户中检索 BW 流程链时出现问题。
#XMSG warning if BW process chains list is empty
bwApiEmptyListError=在 SAP BW 网桥租户中找不到 BW 流程链。
#XMSG: Task log message for invalid OIDC configuration in the SAP BW Bridge tenant
bwBridgeOIDCConfigStatusInvalid=这个租户没有配置 OpenID 身份验证。请按照 SAP Note 3536298 中的说明，使用客户端 ID "{0}" 和令牌 URL "{1}" 在 SAP BW 网桥租户中配置 OpenID 身份验证。
#XMSG BW Process Chain Object has been deleted
VAL_DELETED_BW=以下 BW 流程链可能已从 SAP BW 网桥租户中删除：{0}
#BW SQL Script Procedures folder related warning messages
#XFLD waring if the Procedure list is empty
procedureAPIEmptyList=还没有创建程序或者还没有为 Open SQL 模式授予 "执行" 权限。
#Change digram orientations
changeOrientations=更改方向


# placeholder for the API Path
apiPath=示例：/job/v1
# placeholder for the status API Path
statusAPIPath=示例：/job/v1/{id}/状态
# valueStateText for the API Path
apiPathRequired=API 路径为必填
#placeholder for the CSRF Token URL
csrfTokenURL=只支持 HTTPS
# Response Type 1
statusCode=从 HTTP 状态码获取结果
# Response Type 2
locationHeader=从 HTTP 状态码和位置标头获取结果
# Response Type 3
responseBody=从 HTTP 状态码和响应正文获取结果
# placeholder for ID
idPlaceholder=输入 JSON 路径
# placeholder for indicator value
indicatorValue=输入值
# Placeholder for key
keyPlaceholder=输入键值
# Error message for missing key
KeyRequired=键为必填。
# Error message for invalid key format
invalidKeyFormat=输入的标头键不受支持。有效标头有：<ul><li>"prefer"</li><li>，以 "x-" 开头的标头（不包括 "x-forwarded-host" </li><li>标头），包含字母数字字符、 "-" 或 "_" 的标头</li></ul>
# Error message for missing value
valueRequired=值为必填
# Error message for invalid characters in value
invalidValueCharacters=标头包含无效字符。可以使用的特殊字符为：\t ";"、":"、"-"、"_"、","、"?"、"/" 和 "*"
# Validation message for invoke api path
apiPathValidation=请输入有效的 API 路径，例如：/job/v1
# Validation message for JSON path
jsonPathValidation=请输入有效的 JSON 路径
# Validation message for success/error indicator
indicatorValueValidation=指示符值必须以字母数字字符开头，并且可以包含以下特殊字符：\t "-" 和 "_"
# Error message for JSON path
jsonPathRequired=JSON 路径为必填
# Error message for invalid API Technical Name
invalidTechnicalName=技术名称包含无效字符
# Error message for empty Technical Name
emptyTechnicalName=技术名称为必填
# Tooltip for codeEditor dialog
codeEditorTooltip=打开 JSON 编辑窗口
# Status Api path validation message
validationStatusAPIPath=请输入有效的 API 路径，例如：/job/v1/{id}/状态
# CSRF token URL validation message
validationCSRFTokenURL=CSRF 令牌 URL 必须以 'https://' 开头，并且必须为有效 URL
# Select a connection
selectConnection=选择连接
# Validation message for connection item error
connectionNotReplicated=连接当前无效，不能运行 API 任务。请打开 "连接" 应用并重新输入凭据，以修复 HTTP 连接
# Validation message for API task properties wrong value or missing
VAL_REST_API_PROPERTIES_MISSING=API 任务 "{0}" 中，以下属性的值不正确或缺失：{1}
# Validation message for API connection api is throwing some error or selected connection has some warning
VAL_REST_API_CONNECTION=API 任务 "{0}"  存在 HTTP 连接问题。请打开 "连接" 应用并检查连接
# Validation message for API task properties when delected connection
VAL_REST_API_CONNECTION_DELETED=API 任务 "{0}" 的连接 "{1}" 已删除。
# Validation message for API task properties when connection cannot be consumed in the API task
VAL_REST_API_CONNECTION_CONSUMPTION_WARNING=API 任务 "{0}" 的连接 "{1}" 无法用于运行 API 任务。请打开 "连接" 应用并重新输入凭据，建立用于运行 API 任务的连接。
# Information message if synchrnous mode is selected in the API Invocation then we don't need status information
modeInfoText=在同步模式下，API 调用不显示状态面板
# validation dialog button for Run API Test
saveAnyway=仍然保存
# validation message for technical name
technicalNameValidation=技术名称在任务链中必须是唯一的。请选择另一个技术名称
# Connection error message
getHttpConnectionsError=没能获取 HTTP 连接
# Msg for the API test run when the Task chain is not saved
taskChainNotSaved=运行 API 测试运行之前需要保存任务链
# Msg failed to run API test run
@failedToRunAPI=运行 API 测试运行失败
# Msg for the API test run when its already in running state
apiTaskRunning=API 测试运行已在进行中。是否要启动新的测试运行？

topToBtm=从上向下
leftToRight=从左向右

#XTIT Apache Spark Application Settings
txtHeaderApacheSettings=Apache Spark 应用设置
#XFLD Use Default
txtUseSpaceDefault=使用默认值
#XFLD Application
txtApplication=应用
#XFLD Define new settings for this Task
txtNewSettings=为这个任务定义新设置

#XFLD Use Default
txtUseDefault=使用默认值




