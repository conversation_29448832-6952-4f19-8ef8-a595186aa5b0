#XTOL Undo
@undo=Undo
#XTOL Redo
@redo=Redo
#XTOL Delete Selected Symbol
@deleteNode=Delete Selected Symbol
#XTOL Zoom to Fit
@zoomToFit=Zoom to Fit
#XTOL Auto Layout
@autoLayout=Auto Layout
#XMSG
@welcomeText=Drag and drop objects from the left panel to this canvas.
#XMSG
@txtNoData=It looks like you haven’t added any object yet.
#XMSG
VAL_ENTER_VALID_STRING_GEN=Please enter a valid string of length less than equal to {0}.
#XMSG
@noParametersMsg=This procedure has no input parameters.
#XMSG
@ip_enterValueMsg=The "{0}" (Run SQL Script Procedure) has input parameters. You can set a value of each of them.
#XTOL
@validateModel=Validation Messages
#XTOL
@hierarchy=Hierarchy
#XTOL
@columnCount=Number of Columns
#XFLD
@yes=Yes
#XFLD
@no=No
#XTIT Save Dialog param
@modelNameTaskChain=Task chain
#properties panel
@lblPropertyTitle=Properties
#XFLD
@lblGeneral=General
#XFLD : Setting
@lblSetting=Settings
#XTXT: Delta Enabled Local Table Deletion
txtDeltaTableDeletion=Delete all fully-processed records with Change Type 'Deleted' that are older than
#XTXT: Delta Enabled Local Table Deletion Input field description
desDeltaTableDeletion=Days
#XFLD: Data Activation label
@lblDataActivation=Data Activation
#XFLD: Latency label
@latency=Latency
#XTEXT: Text for Latency dropdown
txtLatencyDefault=Default
#XTEXT: Text 1 hour
txtOneHour=1 Hour
#XTEXT: Text 2 hours
txtTwoHours=2 Hours
#XTEXT: Text 3 hours
txtThreeHours=3 Hours
#XTEXT: Text 4 hours
txtFourHours=4 Hours
#XTEXT: Text 6 hours
txtSixHours=6 Hours
#XTEXT: Text 12 hours
txtTwelveHours=12 Hours
#XTEXT: Text 1 day
txtOneDay=1 Day
#XFLD: Latency label
@autoRestartHead=Automatic Restart
#XFLD
@lblConnectionName=Connection
#XFLD
@lblQualifiedName=Qualified Name
#XFLD
@lblSpaceName=Space Name
#XFLD
@lblLocalSchemaName=Local Schema
#XFLD
@lblType=Object Type
#XFLD
@lblActivity=Activity
#XFLD
@lblTableName=Table Name
#XFLD
@lblBusinessName=Business Name
#XFLD
@lblTechnicalName=Technical Name
#XFLD
@lblSpace=Space
#XFLD
@lblLabel=Label
#XFLD
@lblDataType=Data Type
#XFLD
@lblDescription=Description
#XFLD
@lblStorageType=Storage
#XFLD
@lblHTTPConnection=Generic HTTP Connection
#XFLD
@lblAPISettings=Generic API Settings
#XFLD
@header=Headers
#XFLD
@lblInvoke=API Invocation
#XFLD
@lblMethod=Method
#XFLD
@lblUrl=Base URL
#XFLD
@lblAPIPath=API Path
#XFLD
@lblMode=Mode
#XFLD
@lblCSRFToken=Require CSRF Token
#XFLD
@lblTokenURL=CSRF Token URL
#XFLD
@csrfTokenInfoText=If not entered, the Base URL and the API path will be used
#XFLD
@lblCSRF=CSRF Token
#XFLD
@lblRequestBody=Request Body
#XFLD
@lblFormat=Format
#XFLD
@lblResponse=Response
#XFLD
@lblId=ID to retrieve status
#XFLD
@Id=ID
#XFLD
@lblSuccessIndicator=Success Indicator
#XFLD
@lblErrorIndicator=Error Indicator
#XFLD
@lblErrorReason=Reason for Error
#XFLD
@lblStatus=Status
#XFLD
@lblApiTestRun=API Test Run
#XFLD
@lblRunStatus=Run Status
#XFLD
@lblLastRan=Last Ran On
#XFLD
@lblTestRun=Test Run
#XFLD
@lblDefaultHeader=Default Header Fields (Key-Value pairs)
#XFLD
@lblAdditionalHeader=Additional Header Field
#XFLD
@lblKey=Key
#XFLD
@lblEditJSON=Edit JSON
#XFLD
@lblTasks=Tasks
#XFLD
@lblRESTfullTask=API
#XFLD: add field button text
btnAddField=Add Header Field
#XFLD: view Details link
@viewDetails=View Details
#XTOL
tooltipTxt=More
#XTOL
delete=Delete
#XBTN: ok button text
btnOk=Ok
#XBTN: cancel button text
btnCancel=Cancel
#XBTN: save button text
btnSave=Save
#XMSG Error: Object has duplicated technical name.
VAL_OBJECT_DUPLICATE_NAME=Object "{0}" already exists in the repository. Please enter another name.
#XMSG: loading message while opening task chain
loadTaskChain=Loading the task chain...
#model properties
#XFLD
@status_panel=Run Status
#XFLD
@deploy_status_panel=Deploy Status
#XFLD
@status_lbl=Status
#XFLD
@lblLastExecuted=Last Run
#XFLD
@lblNotExecuted=Not Run Yet
#XFLD
@lblNotDeployed=Not Deployed
#XFLD
errorDetailsTxt=Couldn’t fetch run status
#XBTN: Schedule dropdown menu
SCHEDULE=Schedule
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Edit Schedule
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Delete Schedule
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Create Schedule
#XLNK
viewDetails=View Details
#XMSG: error message for reading execution status from backend
backendErrorMsg=It looks like the data isn’t loading from the server at the moment. Try fetching the data again.
#XFLD: Status text for Completed
@statusCompleted=Completed
#XFLD: Status text for Running
@statusRunning=Running
#XFLD: Status text for Failed
@statusFailed=Failed
#XFLD: Status text for Stopped
@statusStopped=Stopped
#XFLD: Status text for Stopping
@statusStopping=Stopping
#XFLD
@LoaderTitle=Loading
#XFLD
#XFLD: Deploy status, dataflow has  been deployed / deployment date exists
@deployStatusActive=Deployed
@deployStatusRevised=Local Updates
@deployStatusFailed=Failed
@deployStatusPending=Deploying...
@LoaderText=Fetching details from the server
#XMSG
@msgDetailFetchError=Error while fetching details from the server
#XFLD
@executeError=Error
#XFLD
@executeWarning=Warning
#XMSG
@executeConfirmDialog=Info
#XMSG
@executeunsavederror=Save your task chain before running it.
#XMSG
@executemodifiederror=There are unsaved changes in the task chain. Please save it.
#XMSG
@executerunningerror=The task chain is currently running. Wait until the current run is completed before starting with a new one.
#XMSG
@btnExecuteAnyway=Run Anyway
#XMSG
@msgExecuteWithValidations=The task chain has validation errors. Running the task chain may result in failure.
#XMSG
@msgRunDeployedVersion=There are changes to deploy. The last deployed version of the task chain will be run. Do you want to continue?
#XMSG
#XMSG
@navToMonitoring=Open in Task Chain Monitor
#XMSG
txtOR=OR
#XFLD
@preview=Preview
#XMSG
txtand=and
#NOTR
@dateFormat=yyyy-MM-dd
#NOTR
@dateTimeFormatFilter=yyyy-MM-dd HH:mm:ss.
#NOTR
@timeFormat=HH:mm:ss
@lblColum=Column
#XFLD
@lblCondition=Condition
#XFLD
@lblValue=Value
#XMSG
@msgJsonInvalid=The task chain couldn’t be saved as there are errors in the JSON. Please check and resolve.
#XTIT
@msgSaveFailTitle=Invalid JSON.
#XMSG
NOT_CHAINABLE=Object "{0}" can’t be added to the task chain.
#XMSG
NOT_CHAINABLE_REMOTETABLE=Object "{0}": replication type will be changed.
#XMSG
searchTaskChain=Search Objects

#XFLD
@txtTaskChain=Task Chain
#XFLD
@txtRemoteTable=Remote Table
#XFLD
@txtRemoveData=Remove Replicated data
#XFLD
@txtRemovePersist=Remove Persisted Data
#XFLD
@txtView=View
#XFLD
@txtDataFlow=Data Flow
#XFLD
@txtIL=Intelligent Lookup
#XFLD
@txtTransformationFlow=Transformation Flow
#XFLD
@txtReplicationFlow=Replication Flow
#XFLD
@txtDeltaLocalTable=Local Table
#XFLD
@txtBWProcessChain=BW Process Chain
#XFLD
@txtSQLScriptProcedure=SQL Script Procedure
#XFLD
@txtAPI=API
#XFLD
@txtMerge=Merge
#XFLD
@txtOptimize=Optimize
#XFLD
@txtVacuum=Delete Records

#XFLD
@txtRun=Run
#XFLD
@txtPersist=Persist
#XFLD
@txtReplicate=Replicate
#XFLD
@txtDelete=Delete Records with Change Type 'Deleted'
#XFLD
@txtRunTC=Run Task Chain
#XFLD
@txtRunBW=Run BW Process Chain
#XFLD
@txtRunSQLScriptProcedure=Run SQL Script Procedure

#XFLD
@txtRunDataFlow=Run Data Flow
#XFLD
@txtPersistView=Persist View
#XFLD
@txtReplicateTable=Replicate Table
#XFLD
@txtRunIL=Run Intelligent Lookup
#XFLD
@txtRunTF=Run Transformation Flow
#XFLD
@txtRunRF=Run Replication Flow
#XFLD
@txtRemoveReplicatedData=Remove Replicated data
#XFLD
@txtRemovePersistedData=Remove Persisted Data
#XFLD
@txtMergeData=Merge
#XFLD
@txtOptimizeData=Optimize
#XFLD
@txtVacuumData=Delete Records
#XFLD
@txtRunAPI=Run API

#XFLD storage type text
hdlfStorage=File

@statusNew=Not Deployed
@statusActive=Deployed
@statusRevised=Local Updates
@statusPending=Deploying...
@statusChangesToDeploy=Changes To Deploy
@statusDesignTimeError=Design Time Error
@statusRunTimeError=Run Time Error

#XTIT
txtNodes=Objects in Task Chain ({0})
#XBTN
@deleteNodes=Delete

#XMSG
@txtDropDataToDiagram=Drag and drop objects to the diagram.
#XMSG
@noData=No objects

#XFLD
@txtTaskPosition=Task Position

#input parameters and variables
#XFLD
lblInputParameters=Input Parameters
#XFLD
ip_name=Name
#XFLD
ip_value=Value
#XTEXT
@noObjectsFound=No Objects Found

#XMSG
@msgExecuteSuccess=Task chain run has started.
#XMSG
@msgExecuteFail=Failed to run the task chain.
#XMSG
@msgDeployAndRunSuccess=Task chain deployment and run has started.
#XMSG
@msgDeployAndRunFail=Failed to deploy and run the task chain.
#XMSG
@titleExecuteBusy=Please wait.
#XMSG
@msgExecuteBusy=We are preparing your data to start running the task chain.
#XMSG
@msgAPITestRunSuccess=API test run has started.
#XMSG
@msgAPIExecuteBusy=We are preparing your data to start running the API test run.

#XTOL
txtOpenInEditor=Open in Editor
#XTOL
txtPreviewData=Preview Data

#datapreview
#XMSG
@msgDataPreviewNotSupp=Data preview is not available for this object.

#XMSG Error: empty model
VAL_MODEL_EMPTY=Your model seems to be empty. Please add some objects.
#XMSG Error: deploy model
@msgDeployBeforeRun=You need to deploy the task chain before running it.
#BTN: close dialog
btnClose=Close

#XMSG Object is not Deployed
VAL_NOT_DEPLOYED=Object "{0}" must be deployed to continue with the task chain.
#XMSG Object is having runtime error
VAL_RUNTIME_ERROR=Object "{0}" returns a run time error. Please check the object.
#XMSG Object is having design time error
VAL_DESIGNTIME_ERROR=Object "{0}" returns a design time error. Please check the object.
#XMSG Procedure Object has been deleted
VAL_DELETED_PROCEDURE=The following procedures  have been deleted: {0}
#XMSG New Parameter added to Procedure object
VAL_NEW_PARAMETER_ADDED=New parameters added to procedure "{1}": "{0}". Please redeploy the task chain.
#XMSG Parameter removed from Procedure object
VAL_PARAMETER_DELETED=Parameters removed from procedure "{1}": "{0}". Please redeploy the task chain.
#XMSG Parameter data type changed in Procedure object
VAL_PARAMETER_DATATYPE_MODIFIED=Parameter "{0}" data type changed from "{1}" to "{2}" in procedure "{3}".
#XMSG Parameter length changed of specific datatype in Procedure object
VAL_PARAMETER_LENGTH_MODIFIED=Parameter "{0}" length changed from "{1}" to "{2}" in procedure "{3}".
#XMSG Parameter precision changed of Decimal datatype in Procedure object
VAL_PARAMETER_PRECISION_MODIFIED=Parameter "{0}" precision changed from "{1}" to "{2}" in procedure "{3}".
#XMSG Parameter scale changed of Decimal datatype in Procedure object
VAL_PARAMETER_SCALE_MODIFIED=Parameter "{0}" scale changed from "{1}" to "{2}" in procedure "{3}".
#XMSG Parameter value not provided in Procedure object
VAL_SQL_SCRIPT_PROCEDURE_PARAMETER_VALUE_MISSING=You have not entered values for input parameters that are required for procedure "{0}": "{1}"
#XMSG Remote Table activity changes to Remove Replicated Data
VAL_REMOVE_REPLICATED_DATA=The next run of a task chain will change the data access type, and data will no longer be uploaded in real-time.
#XMSG Remote Table activity changes
REPLICATION_TYPE_CHANGED=Object "{0}": Replication type will be changed.

#XFLD
@lblStartNode=Start Node
#XFLD
@lblEndNode=End Node
#XFLD
@linkTo={0} to {1}
#XTOL
@txtViewDetails=View Details

#XTOL
txtOpenImpactLineage=Impact and Lineage Analysis
#XFLD
@emailNotifications=Email Notifications
#XFLD
@txtReset=Reset
#XFLD
@emailMsgWarning=Default template email will be sent when the email message is empty
#XFLD
@notificationSettings=Notification Settings
#XFLD
@recipientEmailAddr=Recipient Email Address
#XFLD
@emailSubject=Email Subject
@emailSubjectText=Task chain <TASKCHAIN_NAME> completed with status <STATUS>
#XFLD
@emailMessage=Email Message
@emailMessageText=Dear User,\n\n This is to notify you that task chain:<TASKCHAIN_NAME> run at <START_TIME> has finished with status <STATUS>. The execution ended at <END_TIME>.\n\nDetails:\nSpace:<SPACE_NAME>\nError:<ERROR_MESSAGE>
@selectEmailRecipientDialogTitle=Select Recipient Email Address
@tenantMembers=Tenant members
@others=Others({0})
@selectedEmailAddress=Selected Recipients
@add=Add
@placeholder=Placeholder
@description=Description
@copyText=Copy text
@taskchainDetailsPlaceholder=Placeholders for task chain details
@placeholderCopied=Placeholder is copied
@invalidEmailInfo=Enter correct email address
@maxMembersAlreadyAdded=You have already added the maximum of 20 members
@enterEmailAddress=Enter user email address
@inCorrectPlaceHolder={0} is not an expected placeholder in the email body.
@nsOFF=Do not send any notifications
@nsFAILED=Send email notification only when the run has completed with an error
@nsCOMPLETED=Send email notification only when the run has completed successfully
@nsANY=Send email when the run has completed
@phStatus=Status of the task chain - SUCCESS|FAILED
@phTaskChainTName=Technical name of the task chain
@phTaskChainBName=Business name of the task chain
@phLogId=Log ID of the run
@phUser=User that is running the task chain
@phLogUILink=Link to the log display of the task chain
@phStartTime=Start time of the run
@phEndTime=End time of the run
@phErrMsg=First error message in the task log. The log is empty in case of SUCCESS
@phSpaceName=Technical name of the space
@emailFormatError=Invalid email format
@emailFormatErrorInListText=Invalid email format entered in non-tenant members list.
@emailSubjectTemplateText=Notification for Task Chain: $$taskChainName$$ - Space: $$spaceId$$ - Status: $$status$$
@emailMessageTemplateText=Hello,\n\n Your task chain labeled $$taskChainName$$ has finished with status $$status$$. \n Here are some other details about the task chain:\n - Task chain technical name: $$taskChainName$$ \n - Log ID of the task chain run: $$logId$$ \n - User that ran the task chain: $$user$$ \n - Link to the log display of the task chain: $$uiLink$$ \n - Start time of the task chain run: $$startTime$$ \n - End time of the task chain run: $$endTime$$ \n - Name of the space: $$spaceId$$ \n
@deleteEmailRecepient=Delete Recipient
@emailInputDisabledText=Please deploy the task chain to add email recipients.
@tenantOwnerDomainMatchErrorText=The email address domain does not match the tenant owner domain: {0}
@totalEmailIdLimitInfoText=You can select up to 20 email recipients including tenant member users and other recipients.
@emailDomainInfoText=Only email addresses with domain: {0} are accepted.
@duplicateEmailErrorText=There are duplicate email recipients in the list.

#XFLD Zorder Title
@txtZorderTitle=Apache Spark Z-Order Columns

#XFLD Zorder NoColumn
@txtZorderNoColumn=No Z-Order Columns Found

#XFLD Zorder Column PrimaryKey
@txtZorderColumnPrimaryKey=Primary Key

#XFLD
@lblOperators=Operators
addNewSelector=Add as New Task
parallelSelector=Add as Parallel Task
replaceSelector=Replace Existing Task
addparallelbranch=Add as Parallel Branch
addplaceholder=Add Placeholder
addALLOperation=ALL Operator
addOROperation=ANY Operator
addplaceholdertocanvas=Add Placeholder to Canvas
addplaceholderonselected=Add Placeholder after Selected Task
#XFLD: Add Parallel Branch to Selected Task
addparallelbranchonselected=Add Parallel Branch after Selected Task
addOperator=Add Operator
txtAdd=Add
txtPlaceHolderText=Drag & Drop a Task here
@lblLayout=Layout

#XMSG
VAL_UNCONNECTED_TASK=Task "{0}" is not connected to the task chain.
#XMSG
VAL_TASK_SHOULD_HAVE_ONE_INCOMING_LINK=Task "{0}" should have only one incoming link.
#XMSG
VAL_TASK_SHOULD_HAVE_INCOMING_LINK=Task "{0}" should have one incoming link.
#XMSG
VAL_UNCONNECTED_OPERATOR=Operator "{0}" is not connected to the task chain.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ATLEAST_TWO_INCOMING_LINK=Operator "{0}" should have at least two incoming links.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ONE_OUT_LINK=Operator "{0}" should have at least one outgoing link.
#XMSG
VAL_CIRCULAR_REFERENCE_EXITS=Circular loop exists in the task chain "{0}".
#XMSG
VAL_UNCONNECTED_BRANCH=Object/branch "{0}" is not connected to the task chain.
#XMSG
VAL_TASK_TRIGGERED_PARALLEL_MORE_THAN_ONE_TIME=Task "{0}" connected in parallel more than once. Please remove duplicates to continue.


txtBegin=Begin
txtNodesInLink=Objects Involved
#XTOL Tooltip for a context button on diagram
openInNewTab=Open in New Tab
#XTOL Tooltip for a context button on diagram
txtConnectToTaskOperator=Drag to Connect
@emailUpdateError=Error in updating Email Notification list

#XMSG
noTeamPrivilegeTxt=You do not have permission to see a list of tenant members. Use the Others tab to add email recipients manually.

#XFLD Package
@txtPackage=Package

#XMSG warning for repository package assignment changed. {1} - package name
VAL_PACKAGE_CHANGED=You have assigned this object to package "{1}". Click Save to confirm and validate this change. Note that assignment to a package cannot be undone in this editor after you save.
#XMSG warning for repository package dependency issue.
MISSING_DEPENDENCY=Dependencies of object "{0}" cannot be resolved in the context of package "{1}".

#Warning messages for the folders in the "Others" tab of task chain editor
#XMSG generic warning if getting the objects in a particular folder fails with unhandled errors
getNonRepoArtefactTypeDescendantsError=There was a problem retrieving the objects in the folder you selected.
#BW Process Chains folder related warning messages
#XMSG warning if user does not have the necessary permission related to the SAP BW Bridge space
missingPrivilegeForBridgeSpaceError=You do not have the necessary permission to view or include BW process chains in a task chain.
#XMSG warning if there was a problem retrieving the BW process chains due to bridge space membership, bridge connection issues or other errors
generalBridgeSpaceError=There was a problem retrieving the BW process chains.
#XMSG warning if the BW API for retrieving the BW process chains from the SAP BW Bridge tenant fails
bwApiClientError=There was a problem retrieving the BW process chains from the SAP BW Bridge tenant.
#XMSG warning if BW process chains list is empty
bwApiEmptyListError=No BW process chains were found in the SAP BW Bridge tenant.
#XMSG: Task log message for invalid OIDC configuration in the SAP BW Bridge tenant
bwBridgeOIDCConfigStatusInvalid=OpenID authentication is not configured for this tenant. Please use the clientID "{0}" and the tokenURL "{1}" to configure the OpenID authentication in the SAP BW bridge tenant as described in SAP note 3536298.
#XMSG BW Process Chain Object has been deleted
VAL_DELETED_BW=The following BW process chains were possibly deleted from the SAP Bridge tenant: {0}
#BW SQL Script Procedures folder related warning messages
#XFLD waring if the Procedure list is empty
procedureAPIEmptyList=No procedures have been created or the EXECUTE privilege has not been granted to the Open SQL schema.
#Change digram orientations
changeOrientations=Change Orientations


# placeholder for the API Path
apiPath=For example: /job/v1
# placeholder for the status API Path
statusAPIPath=For example: /job/v1/{id}/status
# valueStateText for the API Path
apiPathRequired=API Path is required
#placeholder for the CSRF Token URL
csrfTokenURL=Only HTTPS is supported
# Response Type 1
statusCode=Get result from HTTP status code
# Response Type 2
locationHeader=Get result from HTTP status code and location header
# Response Type 3
responseBody=Get result from HTTP status code and response body
# placeholder for ID
idPlaceholder=Enter JSON Path
# placeholder for indicator value
indicatorValue=Enter value
# Placeholder for key
keyPlaceholder=Enter key
# Error message for missing key
KeyRequired=Key is required
# Error message for invalid key format
invalidKeyFormat=The header key you entered is not allowed. Valid headers are:<ul><li>"prefer"</li><li>Headers starting with "x-", except "x-forwarded-host"</li><li>Headers that contain alphanumeric characters, "-", or "_"</li></ul>
# Error message for missing value
valueRequired=Value is required
# Error message for invalid characters in value
invalidValueCharacters=The header contains invalid characters. Special characters that are allowed are:\t ";", ":", "-", "_", ",", "?", "/", and "*"
# Validation message for invoke api path
apiPathValidation=Please enter a valid API path, for example: /job/v1
# Validation message for JSON path
jsonPathValidation=Please enter a valid JSON path
# Validation message for success/error indicator
indicatorValueValidation=Indicator value must start with an alphanumeric character and can include the following special characters:\t "-", and "_"
# Error message for JSON path
jsonPathRequired=JSON Path is required
# Error message for invalid API Technical Name
invalidTechnicalName=The technical name contains invalid characters
# Error message for empty Technical Name
emptyTechnicalName=Technical Name is required
# Tooltip for codeEditor dialog
codeEditorTooltip=Open JSON Edit window
# Status Api path validation message
validationStatusAPIPath=Please enter a valid API path, for example: /job/v1/{id}/status
# CSRF token URL validation message
validationCSRFTokenURL=The CSRF Token URL must start with 'https://' and be a valid URL
# Select a connection
selectConnection=Select a connection
# Validation message for connection item error
connectionNotReplicated=Connection currently invalid to run API tasks. Open the "Connections" app and re-enter your credentials to fix the HTTP connection
# Validation message for API task properties wrong value or missing
VAL_REST_API_PROPERTIES_MISSING=The API task "{0}" has incorrect or missing values for the following properties: {1}
# Validation message for API connection api is throwing some error or selected connection has some warning
VAL_REST_API_CONNECTION=The API task "{0}" has an issue with the HTTP connection. Open the "Connections" app and check the connection
# Validation message for API task properties when delected connection
VAL_REST_API_CONNECTION_DELETED=The API task "{0}" has a deleted "{1}" connection
# Validation message for API task properties when connection cannot be consumed in the API task
VAL_REST_API_CONNECTION_CONSUMPTION_WARNING=The API task "{0}" has a connection "{1}" that cannot be used to run API tasks. Open the "Connections" app and re-enter your credentials to establish connectivity for API tasks
# Information message if synchrnous mode is selected in the API Invocation then we don't need status information
modeInfoText=In Synchronous mode, the status panel is not displayed for API invocations
# validation dialog button for Run API Test
saveAnyway=Save Anyway
# validation message for technical name
technicalNameValidation=The technical name must be unique within the task chain. Please choose another technical name
# Connection error message
getHttpConnectionsError=Failed to get the HTTP connections
# Msg for the API test run when the Task chain is not saved
taskChainNotSaved=The task chain must be saved before running the API test run
# Msg failed to run API test run
@failedToRunAPI=Failed to run the API test run
# Msg for the API test run when its already in running state
apiTaskRunning=An API test run is already in progress. Do you want to start a new test run?

topToBtm=Top-Bottom
leftToRight=Left-Right

#XTIT Apache Spark Application Settings
txtHeaderApacheSettings=Apache Spark Application Settings
#XFLD Use Default
txtUseSpaceDefault=Use Default
#XFLD Application
txtApplication=Application
#XFLD Define new settings for this Task
txtNewSettings=Define new settings for this Task

#XFLD Use Default
txtUseDefault=Use Default




