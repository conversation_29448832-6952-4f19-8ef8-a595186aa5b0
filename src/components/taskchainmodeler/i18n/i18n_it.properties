#XTOL Undo
@undo=Annulla
#XTOL Redo
@redo=Ripristina
#XTOL Delete Selected Symbol
@deleteNode=Elimina simbolo selezionato
#XTOL Zoom to Fit
@zoomToFit=Adatta con zoom
#XTOL Auto Layout
@autoLayout=Layout automatico
#XMSG
@welcomeText=Trascina e rilascia gli oggetti dal pannello sinistro a questa area di disegno.
#XMSG
@txtNoData=Sembra che non siano stati ancora aggiunti oggetti.
#XMSG
VAL_ENTER_VALID_STRING_GEN=Immettere una stringa valida di lunghezza inferiore o uguale a {0}.
#XMSG
@noParametersMsg=Questa procedura non presenta parametri di input.
#XMSG
@ip_enterValueMsg="{0}" (Esegui procedura script SQL) ha parametri di input. È possibile impostare un valore per ognuno di essi.
#XTOL
@validateModel=Messaggi di convalida
#XTOL
@hierarchy=Gerarchia
#XTOL
@columnCount=Numero di colonne
#XFLD
@yes=Sì
#XFLD
@no=No
#XTIT Save Dialog param
@modelNameTaskChain=Catena di task
#properties panel
@lblPropertyTitle=Proprietà
#XFLD
@lblGeneral=Generale
#XFLD : Setting
@lblSetting=Impostazioni
#XTXT: Delta Enabled Local Table Deletion
txtDeltaTableDeletion=Elimina tutti i record completamente elaborati con tipo di modifica "Eliminato" precedenti a
#XTXT: Delta Enabled Local Table Deletion Input field description
desDeltaTableDeletion=Giorni
#XFLD: Data Activation label
@lblDataActivation=Attivazione dati
#XFLD: Latency label
@latency=Latenza
#XTEXT: Text for Latency dropdown
txtLatencyDefault=Predefinito
#XTEXT: Text 1 hour
txtOneHour=1 ora
#XTEXT: Text 2 hours
txtTwoHours=2 ore
#XTEXT: Text 3 hours
txtThreeHours=3 ore
#XTEXT: Text 4 hours
txtFourHours=4 ore
#XTEXT: Text 6 hours
txtSixHours=6 ore
#XTEXT: Text 12 hours
txtTwelveHours=12 ore
#XTEXT: Text 1 day
txtOneDay=1 giorno
#XFLD: Latency label
@autoRestartHead=Riavvio automatico
#XFLD
@lblConnectionName=Connessione
#XFLD
@lblQualifiedName=Nome qualificato
#XFLD
@lblSpaceName=Nome spazio
#XFLD
@lblLocalSchemaName=Schema locale
#XFLD
@lblType=Tipo di oggetto
#XFLD
@lblActivity=Attività
#XFLD
@lblTableName=Nome tabella
#XFLD
@lblBusinessName=Nome aziendale
#XFLD
@lblTechnicalName=Nome tecnico
#XFLD
@lblSpace=Spazio
#XFLD
@lblLabel=Etichetta
#XFLD
@lblDataType=Tipo di dati
#XFLD
@lblDescription=Descrizione
#XFLD
@lblStorageType=Spazio di archiviazione
#XFLD
@lblHTTPConnection=Connessione HTTP generica
#XFLD
@lblAPISettings=Impostazioni API generiche
#XFLD
@header=Intestazioni
#XFLD
@lblInvoke=Chiamata API
#XFLD
@lblMethod=Metodo
#XFLD
@lblUrl=URL di base
#XFLD
@lblAPIPath=Percorso API
#XFLD
@lblMode=Modalità
#XFLD
@lblCSRFToken=Richiedi token CSRF
#XFLD
@lblTokenURL=URL token CSRF
#XFLD
@csrfTokenInfoText=Se non immesso, verranno utilizzati l'URL di base e il Percorso API
#XFLD
@lblCSRF=Token CSRF
#XFLD
@lblRequestBody=Corpo della richiesta
#XFLD
@lblFormat=Formato
#XFLD
@lblResponse=Risposta
#XFLD
@lblId=ID per recupero stato
#XFLD
@Id=ID
#XFLD
@lblSuccessIndicator=Indicatore di operazione riuscita
#XFLD
@lblErrorIndicator=Indicatore di errore
#XFLD
@lblErrorReason=Motivo dell'errore
#XFLD
@lblStatus=Stato
#XFLD
@lblApiTestRun=Esecuzione test API
#XFLD
@lblRunStatus=Stato esecuzione
#XFLD
@lblLastRan=Data ultima esecuzione
#XFLD
@lblTestRun=Esecuzione test
#XFLD
@lblDefaultHeader=Campi intestazione predefiniti (coppie chiave-valore)
#XFLD
@lblAdditionalHeader=Campo intestazione supplementare
#XFLD
@lblKey=Chiave
#XFLD
@lblEditJSON=Modifica JSON
#XFLD
@lblTasks=Task
#XFLD
@lblRESTfullTask=API
#XFLD: add field button text
btnAddField=Aggiungi campo intestazione
#XFLD: view Details link
@viewDetails=Visualizza dettagli
#XTOL
tooltipTxt=Altro
#XTOL
delete=Elimina
#XBTN: ok button text
btnOk=OK
#XBTN: cancel button text
btnCancel=Annulla
#XBTN: save button text
btnSave=Salva
#XMSG Error: Object has duplicated technical name.
VAL_OBJECT_DUPLICATE_NAME=Oggetto ''{0}'' già esistente nel repository. Immettere un altro nome.
#XMSG: loading message while opening task chain
loadTaskChain=Caricamento della catena di task...
#model properties
#XFLD
@status_panel=Stato esecuzione
#XFLD
@deploy_status_panel=Stato distribuzione
#XFLD
@status_lbl=Stato
#XFLD
@lblLastExecuted=Ultima esecuzione
#XFLD
@lblNotExecuted=Non ancora eseguito
#XFLD
@lblNotDeployed=Non distribuito
#XFLD
errorDetailsTxt=Impossibile richiamare stato esecuzione
#XBTN: Schedule dropdown menu
SCHEDULE=Pianifica
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Modifica pianificazione
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Elimina pianificazione
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Crea pianificazione
#XLNK
viewDetails=Visualizza dettagli
#XMSG: error message for reading execution status from backend
backendErrorMsg=Sembra che il caricamento dei dati dal server non stia avvenendo al momento. Tentare di nuovo il recupero dei dati.
#XFLD: Status text for Completed
@statusCompleted=Completato
#XFLD: Status text for Running
@statusRunning=In esecuzione
#XFLD: Status text for Failed
@statusFailed=Non riuscito
#XFLD: Status text for Stopped
@statusStopped=Interrotto
#XFLD: Status text for Stopping
@statusStopping=Interruzione in corso
#XFLD
@LoaderTitle=Caricamento in corso
#XFLD
#XFLD: Deploy status, dataflow has  been deployed / deployment date exists
@deployStatusActive=Distribuito
@deployStatusRevised=Aggiornamenti locali
@deployStatusFailed=Non riuscito
@deployStatusPending=Distribuzione...
@LoaderText=Chiamata dettagli dal server
#XMSG
@msgDetailFetchError=Errore durante la chiamata dei dettagli dal server
#XFLD
@executeError=Errore
#XFLD
@executeWarning=Avviso
#XMSG
@executeConfirmDialog=Informazioni
#XMSG
@executeunsavederror=Salvare la catena di task prima di eseguirla.
#XMSG
@executemodifiederror=Sono presenti modifiche non salvate nella catena di task. Salvarla.
#XMSG
@executerunningerror=La catena di task è attualmente in esecuzione. Attendere il completamento dell'esecuzione attuale prima di avviare una nuova.
#XMSG
@btnExecuteAnyway=Esegui comunque
#XMSG
@msgExecuteWithValidations=La catena di task presenta errori di convalida; la sua esecuzione potrebbe determinare un errore.
#XMSG
@msgRunDeployedVersion=Sono presenti modifiche da distribuire. Verrà eseguita l'ultima versione distribuita della catena di task. Continuare?
#XMSG
#XMSG
@navToMonitoring=Apri in monitor della catena di task
#XMSG
txtOR=O
#XFLD
@preview=Anteprima
#XMSG
txtand=E
#NOTR
@dateFormat=yyyy-MM-dd
#NOTR
@dateTimeFormatFilter=yyyy-MM-dd HH:mm:ss.
#NOTR
@timeFormat=HH:mm:ss
@lblColum=Colonna
#XFLD
@lblCondition=Condizione
#XFLD
@lblValue=Valore
#XMSG
@msgJsonInvalid=Impossibile salvare la catena di task perché sono presenti errori nel JSON. Verificare e risolverli.
#XTIT
@msgSaveFailTitle=JSON non valido.
#XMSG
NOT_CHAINABLE=Impossibile aggiungere l''oggetto ''{0}'' alla catena di task.
#XMSG
NOT_CHAINABLE_REMOTETABLE=Oggetto ''{0}'': il tipo di replicazione verrà modificato.
#XMSG
searchTaskChain=Cerca oggetti

#XFLD
@txtTaskChain=Catena di task
#XFLD
@txtRemoteTable=Tabella remota
#XFLD
@txtRemoveData=Rimuovi dati replicati
#XFLD
@txtRemovePersist=Rimuovi dati persistenza
#XFLD
@txtView=Vista
#XFLD
@txtDataFlow=Flusso di dati
#XFLD
@txtIL=Ricerca intelligente
#XFLD
@txtTransformationFlow=Flusso di trasformazione
#XFLD
@txtReplicationFlow=Flusso di replicazione
#XFLD
@txtDeltaLocalTable=Tabella locale
#XFLD
@txtBWProcessChain=Catena di processi BW
#XFLD
@txtSQLScriptProcedure=Procedura script SQL
#XFLD
@txtAPI=API
#XFLD
@txtMerge=Unisci
#XFLD
@txtOptimize=Ottimizza
#XFLD
@txtVacuum=Elimina record

#XFLD
@txtRun=Esegui
#XFLD
@txtPersist=Applica persistenza
#XFLD
@txtReplicate=Replica
#XFLD
@txtDelete=Elimina record con tipo di modifica "Eliminato"
#XFLD
@txtRunTC=Esegui catena di task
#XFLD
@txtRunBW=Esegui catena di processi BW
#XFLD
@txtRunSQLScriptProcedure=Esegui procedura script SQL

#XFLD
@txtRunDataFlow=Esegui flusso di dati
#XFLD
@txtPersistView=Applica persistenza alla vista
#XFLD
@txtReplicateTable=Replica tabella
#XFLD
@txtRunIL=Esegui ricerca intelligente
#XFLD
@txtRunTF=Esegui flusso di trasformazione
#XFLD
@txtRunRF=Esegui flusso di replicazione
#XFLD
@txtRemoveReplicatedData=Rimuovi dati replicati
#XFLD
@txtRemovePersistedData=Rimuovi dati persistenza
#XFLD
@txtMergeData=Unisci
#XFLD
@txtOptimizeData=Ottimizza
#XFLD
@txtVacuumData=Elimina record
#XFLD
@txtRunAPI=Esegui API

#XFLD storage type text
hdlfStorage=File

@statusNew=Non distribuito
@statusActive=Distribuito
@statusRevised=Aggiornamenti locali
@statusPending=Distribuzione...
@statusChangesToDeploy=Modifiche a distribuzione
@statusDesignTimeError=Errore fase di progettazione
@statusRunTimeError=Errore di run-time

#XTIT
txtNodes=Oggetti nella catena di task ({0})
#XBTN
@deleteNodes=Elimina

#XMSG
@txtDropDataToDiagram=Trascina e rilascia gli oggetti nel diagramma.
#XMSG
@noData=Nessun oggetto

#XFLD
@txtTaskPosition=Posizione task

#input parameters and variables
#XFLD
lblInputParameters=Parametri di input
#XFLD
ip_name=Nome
#XFLD
ip_value=Valore
#XTEXT
@noObjectsFound=Nessun oggetto trovato

#XMSG
@msgExecuteSuccess=Esecuzione catena di task avviata.
#XMSG
@msgExecuteFail=Esecuzione catena di task non riuscita.
#XMSG
@msgDeployAndRunSuccess=Distribuzione ed esecuzione catena di task avviate.
#XMSG
@msgDeployAndRunFail=Distribuzione ed esecuzione catena di task non riuscite.
#XMSG
@titleExecuteBusy=Attendi.
#XMSG
@msgExecuteBusy=Stiamo preparando i dati per avviare l'esecuzione della catena di task.
#XMSG
@msgAPITestRunSuccess=Esecuzione test API avviata.
#XMSG
@msgAPIExecuteBusy=Preparazione dei dati in corso per l'avvio dell'esecuzione test API.

#XTOL
txtOpenInEditor=Apri nell'editor
#XTOL
txtPreviewData=Visualizza anteprima dati

#datapreview
#XMSG
@msgDataPreviewNotSupp=Anteprima dati non disponibile per questo oggetto.

#XMSG Error: empty model
VAL_MODEL_EMPTY=Il modello sembra essere vuoto. Aggiungere degli oggetti.
#XMSG Error: deploy model
@msgDeployBeforeRun=Prima di eseguirla, è necessario distribuire la catena di task.
#BTN: close dialog
btnClose=Chiudi

#XMSG Object is not Deployed
VAL_NOT_DEPLOYED=È necessario distribuire l''oggetto ''{0}'' prima di continuare con la catena di task.
#XMSG Object is having runtime error
VAL_RUNTIME_ERROR=L''oggetto "{0}" restituisce un errore di run-time. Verificare l''oggetto.
#XMSG Object is having design time error
VAL_DESIGNTIME_ERROR=L''oggetto "{0}" restituisce un errore della fase progettazione. Verificare l''oggetto.
#XMSG Procedure Object has been deleted
VAL_DELETED_PROCEDURE=Le seguenti procedure sono state eliminate: {0}
#XMSG New Parameter added to Procedure object
VAL_NEW_PARAMETER_ADDED=Nuovi parametri aggiunti alla procedura "{1}": "{0}". Ridistribuire la catena di task.
#XMSG Parameter removed from Procedure object
VAL_PARAMETER_DELETED=Parametri rimossi dalla procedura "{1}": "{0}". Ridistribuire la catena di task.
#XMSG Parameter data type changed in Procedure object
VAL_PARAMETER_DATATYPE_MODIFIED=Tipo di dati parametro "{0}" modificato da "{1}" a "{2}" nella procedura "{3}".
#XMSG Parameter length changed of specific datatype in Procedure object
VAL_PARAMETER_LENGTH_MODIFIED=Lunghezza parametro "{0}" modificata da "{1}" a "{2}" nella procedura "{3}".
#XMSG Parameter precision changed of Decimal datatype in Procedure object
VAL_PARAMETER_PRECISION_MODIFIED=Precisione parametro "{0}" modificata da "{1}" a "{2}" nella procedura "{3}".
#XMSG Parameter scale changed of Decimal datatype in Procedure object
VAL_PARAMETER_SCALE_MODIFIED=Scala parametro "{0}" modificata da "{1}" a "{2}" nella procedura "{3}".
#XMSG Parameter value not provided in Procedure object
VAL_SQL_SCRIPT_PROCEDURE_PARAMETER_VALUE_MISSING=Non sono stati immessi valori per i parametri di input richiesti per la procedura "{0}": "{1}"
#XMSG Remote Table activity changes to Remove Replicated Data
VAL_REMOVE_REPLICATED_DATA=La prossima esecuzione di una catena di task modificherà il tipo di accesso ai dati e l'upload dei dati non avverrà più in tempo reale.
#XMSG Remote Table activity changes
REPLICATION_TYPE_CHANGED=Oggetto "{0}": il tipo di replicazione verrà modificato.

#XFLD
@lblStartNode=Nodo iniziale
#XFLD
@lblEndNode=Nodo finale
#XFLD
@linkTo=Da {0} a {1}
#XTOL
@txtViewDetails=Visualizza dettagli

#XTOL
txtOpenImpactLineage=Analisi impatto e derivazione
#XFLD
@emailNotifications=Notifiche di posta elettronica
#XFLD
@txtReset=Reimposta
#XFLD
@emailMsgWarning=Se il messaggio di posta elettronica è vuoto, verrà inviato il modello di messaggio predefinito
#XFLD
@notificationSettings=Impostazioni notifica
#XFLD
@recipientEmailAddr=Indirizzo di posta elettronica destinatario
#XFLD
@emailSubject=Oggetto messaggio
@emailSubjectText=Catena di task <TASKCHAIN_NAME> completata con stato <STATUS>
#XFLD
@emailMessage=Messaggio di posta elettronica
@emailMessageText=Gentile utente,\n\n Questa notifica informa che l'esecuzione della catena di task <TASKCHAIN_NAME>, effettuata alle ore <START_TIME>, si è conclusa con stato <STATUS>. L'esecuzione è terminata alle ore <END_TIME>.\n\nDettagli:\nSpazio:<SPACE_NAME>\nErrore:<ERROR_MESSAGE>
@selectEmailRecipientDialogTitle=Seleziona indirizzo di posta elettronica destinatario
@tenantMembers=Membri del tenant
@others=Altri ({0})
@selectedEmailAddress=Destinatari selezionati
@add=Aggiungi
@placeholder=Riempimento
@description=Descrizione
@copyText=Copia testo
@taskchainDetailsPlaceholder=Riempimenti per dettagli catena di task
@placeholderCopied=Riempimento copiato
@invalidEmailInfo=Immetti indirizzo di posta elettronica corretto
@maxMembersAlreadyAdded=È già stato aggiunto il numero massimo di 20 membri
@enterEmailAddress=Immetti indirizzo di posta elettronica utente
@inCorrectPlaceHolder={0} non è un carattere di riempimento previsto per il corpo del messaggio di posta elettronica.
@nsOFF=Non inviare alcuna notifica
@nsFAILED=Invia notifica di posta elettronica solo al completamento dell'esecuzione con un errore
@nsCOMPLETED=Invia notifica di posta elettronica solo al completamento corretto dell'esecuzione
@nsANY=Invia messaggio di posta elettronica al completamento dell'esecuzione
@phStatus=Stato della catena di task - RIUSCITO|NON RIUSCITO
@phTaskChainTName=Nome tecnico della catena di task
@phTaskChainBName=Nome aziendale della catena di task
@phLogId=ID registro dell'esecuzione
@phUser=Utente che esegue la catena di task
@phLogUILink=Collegamento alla visualizzazione del registro della catena di task
@phStartTime=Ora di inizio dell'esecuzione
@phEndTime=Ora di fine dell'esecuzione
@phErrMsg=Primo messaggio di errore nel registro del task; il registro è vuoto in caso di stato RIUSCITO
@phSpaceName=Nome tecnico dello spazio
@emailFormatError=Formato posta elettronica non valido
@emailFormatErrorInListText=Formato posta elettronica non valido immesso nell'elenco di membri non tenant.
@emailSubjectTemplateText=Notifica per catena di task: $$taskChainName$$ - Spazio: $$spaceId$$ - Stato: $$status$$
@emailMessageTemplateText=Salve,\n\n la tua catena di task con etichetta $$taskChainName$$ è conclusa con stato $$status$$.\nQui riportiamo altri dettagli sulla catena di task:\n- Nome tecnico della catena di task: $$taskChainName$$\n- ID registro dell'esecuzione della catena di task: $$logId$$\n- Utente che ha eseguito la catena di task: $$user$$\n- Collegamento alla visualizzazione del registro della catena di task: $$uiLink$$\n- Ora di inizio dell'esecuzione della catena di task: $$startTime$$\n- Ora di fine dell'esecuzione della catena di task: $$endTime$$\n- Nome dello spazio: $$spaceId$$\n
@deleteEmailRecepient=Elimina destinatario
@emailInputDisabledText=Distribuire la catena di task per aggiungere i destinatari di posta elettronica.
@tenantOwnerDomainMatchErrorText=Il dominio dell''indirizzo di posta elettronica non corrisponde il dominio del proprietario tenant: {0}
@totalEmailIdLimitInfoText=È possibile selezionare fino a 20 destinatari di posta elettronica, compresi gli utenti membro del tenant e altri destinatari.
@emailDomainInfoText=Sono accettati solo indirizzi di posta elettronica il cui dominio è {0}.
@duplicateEmailErrorText=Nell'elenco sono presenti destinatari di posta elettronica duplicati.

#XFLD Zorder Title
@txtZorderTitle=Colonne Z-Order Apache Spark

#XFLD Zorder NoColumn
@txtZorderNoColumn=Nessuna colonna Z-Order trovata

#XFLD Zorder Column PrimaryKey
@txtZorderColumnPrimaryKey=Chiave primaria

#XFLD
@lblOperators=Operatori
addNewSelector=Aggiungi come nuovo task
parallelSelector=Aggiungi come task parallelo
replaceSelector=Sostituisci task esistente
addparallelbranch=Aggiungi come diramazione parallela
addplaceholder=Aggiungi riempimento
addALLOperation=Operatore ALL
addOROperation=Operatore ANY
addplaceholdertocanvas=Aggiungi riempimento ad area di disegno
addplaceholderonselected=Aggiungi riempimento dopo task selezionato
#XFLD: Add Parallel Branch to Selected Task
addparallelbranchonselected=Aggiungi diramazione parallela dopo task selezionato
addOperator=Aggiungi operatore
txtAdd=Aggiungi
txtPlaceHolderText=Trascina e rilascia qui un task
@lblLayout=Layout

#XMSG
VAL_UNCONNECTED_TASK=Task ''{0}'' non connesso a catena di task.
#XMSG
VAL_TASK_SHOULD_HAVE_ONE_INCOMING_LINK=Il task ''{0}'' deve avere un solo collegamento in entrata.
#XMSG
VAL_TASK_SHOULD_HAVE_INCOMING_LINK=Il task ''{0}'' deve avere un collegamento in entrata.
#XMSG
VAL_UNCONNECTED_OPERATOR=Operatore ''{0}'' non connesso a catena di task.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ATLEAST_TWO_INCOMING_LINK=L''operatore ''{0}'' deve avere almeno due collegamenti in entrata.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ONE_OUT_LINK=L''operatore ''{0}'' deve avere almeno un collegamento in uscita.
#XMSG
VAL_CIRCULAR_REFERENCE_EXITS=Loop circolare esistente nella catena di task ''{0}''.
#XMSG
VAL_UNCONNECTED_BRANCH=Operatore/Diramazione ''{0}'' non connessi a catena di task.
#XMSG
VAL_TASK_TRIGGERED_PARALLEL_MORE_THAN_ONE_TIME=Task ''{0}'' connesso in parallelo più di una volta. Rimuovere i duplicati per continuare.


txtBegin=Inizio
txtNodesInLink=Oggetti coinvolti
#XTOL Tooltip for a context button on diagram
openInNewTab=Apri in nuova scheda
#XTOL Tooltip for a context button on diagram
txtConnectToTaskOperator=Trascina per connettere
@emailUpdateError=Errore di aggiornamento dell'elenco di notifiche di posta elettronica

#XMSG
noTeamPrivilegeTxt=Non si dispone dell'autorizzazione per visualizzare un elenco di membri del tenant. Utilizzare la scheda Altri per aggiungere manualmente i destinatari di posta elettronica.

#XFLD Package
@txtPackage=Pacchetto

#XMSG warning for repository package assignment changed. {1} - package name
VAL_PACKAGE_CHANGED=Questo oggetto è stato assegnato al pacchetto ''{1}''. Fare clic su Salva per confermare e convalidare questa modifica. Notare che l''assegnazione a un pacchetto non può essere annullata in questo editor dopo il salvataggio.
#XMSG warning for repository package dependency issue.
MISSING_DEPENDENCY=Le dipendenze dell''oggetto ''{0}'' non possono essere risolte nel contesto del pacchetto ''{1}''.

#Warning messages for the folders in the "Others" tab of task chain editor
#XMSG generic warning if getting the objects in a particular folder fails with unhandled errors
getNonRepoArtefactTypeDescendantsError=Si è verificato un problema nel recupero degli oggetti nella cartella selezionata.
#BW Process Chains folder related warning messages
#XMSG warning if user does not have the necessary permission related to the SAP BW Bridge space
missingPrivilegeForBridgeSpaceError=Non si dispone dell'autorizzazione necessaria per visualizzare o includere le catene di processi BW in una catena di task.
#XMSG warning if there was a problem retrieving the BW process chains due to bridge space membership, bridge connection issues or other errors
generalBridgeSpaceError=Si è verificato un problema nel recupero delle catene di processi BW.
#XMSG warning if the BW API for retrieving the BW process chains from the SAP BW Bridge tenant fails
bwApiClientError=Si è verificato un problema nel recupero delle catene di processi BW dal tenant del ponte SAP BW.
#XMSG warning if BW process chains list is empty
bwApiEmptyListError=Nessuna catena di processi BW trovata nel tenant del ponte SAP BW.
#XMSG: Task log message for invalid OIDC configuration in the SAP BW Bridge tenant
bwBridgeOIDCConfigStatusInvalid=Autenticazione OpenID non configurata per questo tenant. Utilizzare l''ID client "{0}" e l''URL token "{1}" per configurare l''autenticazione OpenID nel tenant del ponte SAP BW come illustrato nella nota SAP 3536298.
#XMSG BW Process Chain Object has been deleted
VAL_DELETED_BW=Le seguenti catene di processi BW sono state probabilmente eliminate dal tenant del ponte SAP BW: {0}
#BW SQL Script Procedures folder related warning messages
#XFLD waring if the Procedure list is empty
procedureAPIEmptyList=Nessuna procedura creata o il privilegio ESECUZIONE non è stato concesso nello schema Open SQL.
#Change digram orientations
changeOrientations=Modifica orientamenti


# placeholder for the API Path
apiPath=Ad esempio: /job/v1
# placeholder for the status API Path
statusAPIPath=Ad esempio: /job/v1/{id}/status
# valueStateText for the API Path
apiPathRequired=Percorso API obbligatorio
#placeholder for the CSRF Token URL
csrfTokenURL=Solo HTTPS è supportato
# Response Type 1
statusCode=Recupera risultato da codice stato HTTP
# Response Type 2
locationHeader=Recupera risultato da codice stato HTTP e intestazione di ubicazione
# Response Type 3
responseBody=Recupera risultato da codice stato HTTP e corpo della risposta
# placeholder for ID
idPlaceholder=Immettere percorso JSON
# placeholder for indicator value
indicatorValue=Immettere valore
# Placeholder for key
keyPlaceholder=Immettere chiave
# Error message for missing key
KeyRequired=La chiave è obbligatoria
# Error message for invalid key format
invalidKeyFormat=La chiave intestazione immessa non è consentita. Le intestazioni valide sono: <ul><li>"prefer"</li><li>Intestazioni che iniziano con "x-", ad eccezione di "x-forwarded-host"</li><li>Intestazioni che contengono caratteri alfanumerici, "-" o "_"</li></ul>
# Error message for missing value
valueRequired=Il valore è obbligatorio
# Error message for invalid characters in value
invalidValueCharacters=L'intestazione contiene caratteri non validi. I caratteri speciali consentiti sono:\t ";", ":", "-", "_", ",", "?", "/" e "*"
# Validation message for invoke api path
apiPathValidation=Immettere un percorso API valido, ad esempio: /job/v1
# Validation message for JSON path
jsonPathValidation=Immettere un percorso JSON valido
# Validation message for success/error indicator
indicatorValueValidation=Il valore indicatore deve iniziare con un carattere alfanumerico e può includere i seguenti caratteri speciali:\t "-" e "_"
# Error message for JSON path
jsonPathRequired=Percorso JSON obbligatorio
# Error message for invalid API Technical Name
invalidTechnicalName=Il nome tecnico contiene caratteri non validi
# Error message for empty Technical Name
emptyTechnicalName=Nome tecnico obbligatorio
# Tooltip for codeEditor dialog
codeEditorTooltip=Apri finestra di modifica JSON
# Status Api path validation message
validationStatusAPIPath=Immettere un percorso API valido, ad esempio: /job/v1/{id}/status
# CSRF token URL validation message
validationCSRFTokenURL=L'URL token CSRF deve iniziare con "https://" ed essere un URL valido
# Select a connection
selectConnection=Selezionare una connessione
# Validation message for connection item error
connectionNotReplicated=Connessione attualmente non valida per eseguire task API. Aprire l'app "Connessioni" e immettere nuovamente le credenziali per correggere la connessione HTTP
# Validation message for API task properties wrong value or missing
VAL_REST_API_PROPERTIES_MISSING=Il task API "{0}" presenta valori non corretti o mancanti per le seguenti proprietà: {1}
# Validation message for API connection api is throwing some error or selected connection has some warning
VAL_REST_API_CONNECTION=Il task API "{0}" presenta un problema con la connessione HTTP. Aprire l''app "Connessioni" e verificare la connessione
# Validation message for API task properties when delected connection
VAL_REST_API_CONNECTION_DELETED=Il task API "{0}" ha una connessione "{1}" eliminata
# Validation message for API task properties when connection cannot be consumed in the API task
VAL_REST_API_CONNECTION_CONSUMPTION_WARNING=Il task API "{0}" ha una connessione "{1}" che non può essere utilizzata per eseguire task API. Aprire l''app "Connessioni" e immettere di nuovo le credenziali per stabilire la connettività per i task API
# Information message if synchrnous mode is selected in the API Invocation then we don't need status information
modeInfoText=In modalità Sincrono, il pannello dello stato non è visualizzato per le chiamate API
# validation dialog button for Run API Test
saveAnyway=Salva comunque
# validation message for technical name
technicalNameValidation=Il nome tecnico deve essere univoco all'interno della catena di task. Scegliere un altro nome tecnico
# Connection error message
getHttpConnectionsError=Recupero connessioni HTTP non riuscito
# Msg for the API test run when the Task chain is not saved
taskChainNotSaved=È necessario salvare la catena di task prima di avviare l'esecuzione test API
# Msg failed to run API test run
@failedToRunAPI=Esecuzione test API non riuscita
# Msg for the API test run when its already in running state
apiTaskRunning=Un'esecuzione test API è già in corso. Avviare una nuova esecuzione test?

topToBtm=Dall'alto in basso
leftToRight=Da sinistra a destra

#XTIT Apache Spark Application Settings
txtHeaderApacheSettings=Impostazioni applicazione Apache Spark
#XFLD Use Default
txtUseSpaceDefault=Usa valore predefinito
#XFLD Application
txtApplication=Applicazione
#XFLD Define new settings for this Task
txtNewSettings=Definisci nuove impostazioni per questo task

#XFLD Use Default
txtUseDefault=Usa valore predefinito




