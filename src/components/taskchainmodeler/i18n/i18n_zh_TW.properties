#XTOL Undo
@undo=復原
#XTOL Redo
@redo=取消復原
#XTOL Delete Selected Symbol
@deleteNode=刪除所選符號
#XTOL Zoom to Fit
@zoomToFit=縮放至適當比例
#XTOL Auto Layout
@autoLayout=自動配置
#XMSG
@welcomeText=請將您的物件從左側面板拖放至此畫布。
#XMSG
@txtNoData=您尚未新增物件。
#XMSG
VAL_ENTER_VALID_STRING_GEN=請輸入長度小於等於 {0} 的有效字串。
#XMSG
@noParametersMsg=此程序沒有輸入參數。
#XMSG
@ip_enterValueMsg="{0}" (執行 SQL 指令碼程序) 有輸入參數。您可設定各項的值。
#XTOL
@validateModel=驗證訊息
#XTOL
@hierarchy=階層
#XTOL
@columnCount=欄數量
#XFLD
@yes=是
#XFLD
@no=否
#XTIT Save Dialog param
@modelNameTaskChain=工作細項鏈
#properties panel
@lblPropertyTitle=屬性
#XFLD
@lblGeneral=一般
#XFLD : Setting
@lblSetting=設定
#XTXT: Delta Enabled Local Table Deletion
txtDeltaTableDeletion=刪除所有晚於下列日期更改類型為「已刪除」的完全處理記錄：
#XTXT: Delta Enabled Local Table Deletion Input field description
desDeltaTableDeletion=天
#XFLD: Data Activation label
@lblDataActivation=資料啟用
#XFLD: Latency label
@latency=延遲
#XTEXT: Text for Latency dropdown
txtLatencyDefault=預設
#XTEXT: Text 1 hour
txtOneHour=1 小時
#XTEXT: Text 2 hours
txtTwoHours=2 小時
#XTEXT: Text 3 hours
txtThreeHours=3 小時
#XTEXT: Text 4 hours
txtFourHours=4 小時
#XTEXT: Text 6 hours
txtSixHours=6 小時
#XTEXT: Text 12 hours
txtTwelveHours=12 小時
#XTEXT: Text 1 day
txtOneDay=1 天
#XFLD: Latency label
@autoRestartHead=自動重新開始
#XFLD
@lblConnectionName=連線
#XFLD
@lblQualifiedName=合格的名稱
#XFLD
@lblSpaceName=空間名稱
#XFLD
@lblLocalSchemaName=本端綱要
#XFLD
@lblType=物件類型
#XFLD
@lblActivity=活動
#XFLD
@lblTableName=表格名稱
#XFLD
@lblBusinessName=業務名稱
#XFLD
@lblTechnicalName=技術名稱
#XFLD
@lblSpace=空間
#XFLD
@lblLabel=標籤
#XFLD
@lblDataType=資料類型
#XFLD
@lblDescription=說明
#XFLD
@lblStorageType=儲存
#XFLD
@lblHTTPConnection=通用 HTTP 連線
#XFLD
@lblAPISettings=通用 API 設定
#XFLD
@header=表頭
#XFLD
@lblInvoke=API 呼叫
#XFLD
@lblMethod=方法
#XFLD
@lblUrl=基礎 URL
#XFLD
@lblAPIPath=API 路徑
#XFLD
@lblMode=模式
#XFLD
@lblCSRFToken=需要 CSRF 權杖
#XFLD
@lblTokenURL=CSRF 權杖 URL
#XFLD
@csrfTokenInfoText=若未輸入，則將使用基礎 URL 和 API 路徑
#XFLD
@lblCSRF=CSRF 權杖
#XFLD
@lblRequestBody=請求本文
#XFLD
@lblFormat=格式
#XFLD
@lblResponse=回應
#XFLD
@lblId=檢索狀態的 ID
#XFLD
@Id=ID
#XFLD
@lblSuccessIndicator=成功指示碼
#XFLD
@lblErrorIndicator=錯誤指示碼
#XFLD
@lblErrorReason=錯誤的回應
#XFLD
@lblStatus=狀態
#XFLD
@lblApiTestRun=API 測試執行
#XFLD
@lblRunStatus=執行狀態
#XFLD
@lblLastRan=最後執行日期
#XFLD
@lblTestRun=測試執行
#XFLD
@lblDefaultHeader=預設表頭欄位 (鍵值與值配對)
#XFLD
@lblAdditionalHeader=附加表頭欄位
#XFLD
@lblKey=鍵值
#XFLD
@lblEditJSON=編輯 JSON
#XFLD
@lblTasks=工作細項
#XFLD
@lblRESTfullTask=API
#XFLD: add field button text
btnAddField=新增表頭欄位
#XFLD: view Details link
@viewDetails=檢視明細
#XTOL
tooltipTxt=更多
#XTOL
delete=刪除
#XBTN: ok button text
btnOk=確定
#XBTN: cancel button text
btnCancel=取消
#XBTN: save button text
btnSave=儲存
#XMSG Error: Object has duplicated technical name.
VAL_OBJECT_DUPLICATE_NAME=儲藏庫中已有物件「{0}」。請輸入其他名稱。
#XMSG: loading message while opening task chain
loadTaskChain=正在載入工作細項鏈...
#model properties
#XFLD
@status_panel=執行狀態
#XFLD
@deploy_status_panel=部署狀態
#XFLD
@status_lbl=狀態
#XFLD
@lblLastExecuted=最後執行
#XFLD
@lblNotExecuted=尚未執行
#XFLD
@lblNotDeployed=未部署
#XFLD
errorDetailsTxt=無法取得執行狀態
#XBTN: Schedule dropdown menu
SCHEDULE=排程
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=編輯排程
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=刪除排程
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=建立排程
#XLNK
viewDetails=檢視明細
#XMSG: error message for reading execution status from backend
backendErrorMsg=目前無法從伺服器載入資料。請再次嘗試取得資料。
#XFLD: Status text for Completed
@statusCompleted=已完成
#XFLD: Status text for Running
@statusRunning=執行中
#XFLD: Status text for Failed
@statusFailed=失敗
#XFLD: Status text for Stopped
@statusStopped=已停止
#XFLD: Status text for Stopping
@statusStopping=正在停止
#XFLD
@LoaderTitle=正在載入
#XFLD
#XFLD: Deploy status, dataflow has  been deployed / deployment date exists
@deployStatusActive=已部署
@deployStatusRevised=本端更新
@deployStatusFailed=失敗
@deployStatusPending=正在部署...
@LoaderText=正在從伺服器取得明細
#XMSG
@msgDetailFetchError=從伺服器取得明細時發生錯誤
#XFLD
@executeError=錯誤
#XFLD
@executeWarning=警告
#XMSG
@executeConfirmDialog=資訊
#XMSG
@executeunsavederror=請儲存工作細項鏈再執行。
#XMSG
@executemodifiederror=工作細項鏈中有未儲存的更改內容。請儲存。
#XMSG
@executerunningerror=工作細項鏈目前執行中。請等待目前執行完成再開始新執行。
#XMSG
@btnExecuteAnyway=一律執行
#XMSG
@msgExecuteWithValidations=工作細項鏈發生驗證錯誤。執行工作細項鏈可能造成失敗。
#XMSG
@msgRunDeployedVersion=有更改待部署。將執行工作細項鏈的最後部署版本。您要繼續嗎？
#XMSG
#XMSG
@navToMonitoring=在工作細項鏈監控器中開啟
#XMSG
txtOR=或
#XFLD
@preview=預覽
#XMSG
txtand=和
#NOTR
@dateFormat=yyyy-MM-dd
#NOTR
@dateTimeFormatFilter=yyyy-MM-dd HH:mm:ss.
#NOTR
@timeFormat=HH:mm:ss
@lblColum=欄
#XFLD
@lblCondition=條件
#XFLD
@lblValue=值
#XMSG
@msgJsonInvalid=由於 JSON 中有錯誤，因此無法儲存工作細項鏈。請檢查並解決。
#XTIT
@msgSaveFailTitle=JSON 無效。
#XMSG
NOT_CHAINABLE=無法將物件「{0}」新增至工作細項鏈。
#XMSG
NOT_CHAINABLE_REMOTETABLE=物件「{0}」複製類型將更改。
#XMSG
searchTaskChain=搜尋物件

#XFLD
@txtTaskChain=工作細項鏈
#XFLD
@txtRemoteTable=遠端表格
#XFLD
@txtRemoveData=移除複製的資料
#XFLD
@txtRemovePersist=移除保存的資料
#XFLD
@txtView=檢視
#XFLD
@txtDataFlow=資料流程
#XFLD
@txtIL=智慧查詢
#XFLD
@txtTransformationFlow=轉換流程
#XFLD
@txtReplicationFlow=複製流程
#XFLD
@txtDeltaLocalTable=本端表格
#XFLD
@txtBWProcessChain=BW 程序鏈
#XFLD
@txtSQLScriptProcedure=SQL 指令碼程序
#XFLD
@txtAPI=API
#XFLD
@txtMerge=合併
#XFLD
@txtOptimize=最佳化
#XFLD
@txtVacuum=刪除記錄

#XFLD
@txtRun=執行
#XFLD
@txtPersist=保存
#XFLD
@txtReplicate=複製
#XFLD
@txtDelete=刪除更改類型為「已刪除」的記錄
#XFLD
@txtRunTC=執行工作細項鏈
#XFLD
@txtRunBW=執行 BW 程序鏈
#XFLD
@txtRunSQLScriptProcedure=執行 SQL 指令碼程序

#XFLD
@txtRunDataFlow=執行資料流程
#XFLD
@txtPersistView=保存檢視
#XFLD
@txtReplicateTable=複製表格
#XFLD
@txtRunIL=執行智慧查詢
#XFLD
@txtRunTF=執行轉換流程
#XFLD
@txtRunRF=執行複製流程
#XFLD
@txtRemoveReplicatedData=移除複製的資料
#XFLD
@txtRemovePersistedData=移除保存的資料
#XFLD
@txtMergeData=合併
#XFLD
@txtOptimizeData=最佳化
#XFLD
@txtVacuumData=刪除記錄
#XFLD
@txtRunAPI=執行 API

#XFLD storage type text
hdlfStorage=檔案

@statusNew=未部署
@statusActive=已部署
@statusRevised=本端更新
@statusPending=正在部署...
@statusChangesToDeploy=要部署的更改內容
@statusDesignTimeError=設計時期發生錯誤
@statusRunTimeError=執行時期發生錯誤

#XTIT
txtNodes=工作細項鏈中的物件 ({0})
#XBTN
@deleteNodes=刪除

#XMSG
@txtDropDataToDiagram=將物件拖放至圖表。
#XMSG
@noData=無物件

#XFLD
@txtTaskPosition=工作細項位置

#input parameters and variables
#XFLD
lblInputParameters=輸入參數
#XFLD
ip_name=名稱
#XFLD
ip_value=值
#XTEXT
@noObjectsFound=找不到物件

#XMSG
@msgExecuteSuccess=已開始執行工作細項鏈。
#XMSG
@msgExecuteFail=無法執行工作細項鏈。
#XMSG
@msgDeployAndRunSuccess=已開始工作細項鏈部署和執行。
#XMSG
@msgDeployAndRunFail=無法部署和執行工作細項鏈。
#XMSG
@titleExecuteBusy=請稍候。
#XMSG
@msgExecuteBusy=系統正在準備您的資料以開始執行工作細項鏈。
#XMSG
@msgAPITestRunSuccess=API 測試執行已開始。
#XMSG
@msgAPIExecuteBusy=系統正在準備您的資料以開始執行 API 測試執行。

#XTOL
txtOpenInEditor=於編輯器中開啟
#XTOL
txtPreviewData=預覽資料

#datapreview
#XMSG
@msgDataPreviewNotSupp=此物件無法預覽資料。

#XMSG Error: empty model
VAL_MODEL_EMPTY=模型似乎空白。請新增一些物件。
#XMSG Error: deploy model
@msgDeployBeforeRun=您必須部署工作細項鏈才能執行。
#BTN: close dialog
btnClose=關閉

#XMSG Object is not Deployed
VAL_NOT_DEPLOYED=必須部署物件「{0}」才能繼續工作細項鏈。
#XMSG Object is having runtime error
VAL_RUNTIME_ERROR=物件 "{0}" 傳回執行時期錯誤。請檢查物件。
#XMSG Object is having design time error
VAL_DESIGNTIME_ERROR=物件 "{0}" 傳回設計時期錯誤。請檢查物件。
#XMSG Procedure Object has been deleted
VAL_DELETED_PROCEDURE=已刪除下列程序：{0}
#XMSG New Parameter added to Procedure object
VAL_NEW_PARAMETER_ADDED=已將新參數新增至程序 "{1}"："{0}"。請重新部署工作細項鏈。
#XMSG Parameter removed from Procedure object
VAL_PARAMETER_DELETED=已從程序 "{1}" 移除參數："{0}"。請重新部署工作細項鏈。
#XMSG Parameter data type changed in Procedure object
VAL_PARAMETER_DATATYPE_MODIFIED=程序 "{3}" 中的參數 "{0}" 資料類型已從 "{1}" 更改為 "{2}"。
#XMSG Parameter length changed of specific datatype in Procedure object
VAL_PARAMETER_LENGTH_MODIFIED=程序 "{3}" 中的參數 "{0}" 長度已從 "{1}" 更改為 "{2}"。
#XMSG Parameter precision changed of Decimal datatype in Procedure object
VAL_PARAMETER_PRECISION_MODIFIED=程序 "{3}" 中的參數 "{0}" 總位數已從 "{1}" 更改為 "{2}"。
#XMSG Parameter scale changed of Decimal datatype in Procedure object
VAL_PARAMETER_SCALE_MODIFIED=程序 "{3}" 中的參數 "{0}" 小數位數已從 "{1}" 更改為 "{2}"。
#XMSG Parameter value not provided in Procedure object
VAL_SQL_SCRIPT_PROCEDURE_PARAMETER_VALUE_MISSING=您未針對程序 "{0}" 所需的輸入參數輸入值："{1}"
#XMSG Remote Table activity changes to Remove Replicated Data
VAL_REMOVE_REPLICATED_DATA=下次執行工作細項鏈將更改資料存取類型，且資料不再即時上傳。
#XMSG Remote Table activity changes
REPLICATION_TYPE_CHANGED=物件「{0}」：複製類型將更改。

#XFLD
@lblStartNode=開始節點
#XFLD
@lblEndNode=結束節點
#XFLD
@linkTo={0} 至 {1}
#XTOL
@txtViewDetails=檢視明細

#XTOL
txtOpenImpactLineage=影響和歷程分析
#XFLD
@emailNotifications=電子郵件通知
#XFLD
@txtReset=重設
#XFLD
@emailMsgWarning=電子郵件訊息為空白時，將傳送預設範本電子郵件
#XFLD
@notificationSettings=通知設定
#XFLD
@recipientEmailAddr=收件人電子郵件地址
#XFLD
@emailSubject=電子郵件主旨
@emailSubjectText=工作細項鏈 <TASKCHAIN_NAME> 已完成，狀態為 <STATUS>
#XFLD
@emailMessage=電子郵件訊息
@emailMessageText=使用者您好：\n\n 此為通知您在 <START_TIME> 執行的工作細項鏈：<TASKCHAIN_NAME> 已結束，狀態為 <STATUS>。執行結束於 <END_TIME>。\n\n明細：\n空間：<SPACE_NAME>\n錯誤：<ERROR_MESSAGE>
@selectEmailRecipientDialogTitle=選擇收件人電子郵件地址
@tenantMembers=租用戶成員
@others=其他 ({0})
@selectedEmailAddress=所選收件人
@add=新增
@placeholder=位置保留符號
@description=說明
@copyText=複製內文
@taskchainDetailsPlaceholder=工作細項鏈明細的位置保留符號
@placeholderCopied=已複製位置保留符號
@invalidEmailInfo=請輸入正確的電子郵件地址
@maxMembersAlreadyAdded=您已新增最多 20 個成員
@enterEmailAddress=輸入使用者電子郵件地址
@inCorrectPlaceHolder={0} 不是電子郵件本文中預期的位置保留符號。
@nsOFF=不要傳送通知
@nsFAILED=只在執行完成但有錯誤時，傳送電子郵件通知
@nsCOMPLETED=只在執行成功完成時，傳送電子郵件通知
@nsANY=執行完成時，傳送電子郵件
@phStatus=工作細項鏈狀態 - 成功|失敗
@phTaskChainTName=工作細項鏈技術名稱
@phTaskChainBName=工作細項鏈業務名稱
@phLogId=執行日誌 ID
@phUser=執行工作細項鏈的使用者
@phLogUILink=連結至工作細項鏈的日誌顯示
@phStartTime=執行開始時間
@phEndTime=執行結束時間
@phErrMsg=工作細項日誌中的第一個錯誤訊息。若為成功，則日誌空白
@phSpaceName=空間技術名稱
@emailFormatError=電子郵件格式無效
@emailFormatErrorInListText=非租用戶成員清單中輸入的電子郵件格式無效。
@emailSubjectTemplateText=工作細項鏈通知：$$taskChainName$$ - 空間：$$spaceId$$ - 狀態：$$status$$
@emailMessageTemplateText=您好，\n\n標記為 $$taskChainName$$ 的工作細項鏈已結束，狀態是 $$status$$。\n此為關於該工作細項鏈的明細：\n - 工作細項鏈技術名稱：$$taskChainName$$ \n - 工作細項鏈執行日誌 ID：$$logId$$ \n - 執行工作細項鏈的使用者：$$user$$ \n - 工作細項鏈日誌顯示的連結：$$uiLink$$ \n - 工作細細鏈執行開始時間：$$startTime$$ \n - 工作細項鏈結束時間：$$endTime$$ \n - 空間名稱：$$spaceId$$ \n
@deleteEmailRecepient=刪除收件人
@emailInputDisabledText=請部署工作細項鏈以新增電子郵件收件人。
@tenantOwnerDomainMatchErrorText=電子郵件網域與租用戶所有人網域不相符：{0}
@totalEmailIdLimitInfoText=您可選擇最多 20 個電子郵件收件人，包含租用戶成員使用者和其他收件人。
@emailDomainInfoText=僅接受網域為：{0} 的電子郵件地址。
@duplicateEmailErrorText=清單中有重複的電子郵件收件人。

#XFLD Zorder Title
@txtZorderTitle=Apache Spark Z 排序欄

#XFLD Zorder NoColumn
@txtZorderNoColumn=找不到 Z 排序欄

#XFLD Zorder Column PrimaryKey
@txtZorderColumnPrimaryKey=主鍵值

#XFLD
@lblOperators=運算子
addNewSelector=新增為新工作細項
parallelSelector=新增為平行工作細項
replaceSelector=取代現有工作細項
addparallelbranch=新增為平行分支
addplaceholder=新增保留位置
addALLOperation=ALL 運算子
addOROperation=ANY 運算子
addplaceholdertocanvas=將保留位置新增至畫布
addplaceholderonselected=在所選工作細項後新增保留位置
#XFLD: Add Parallel Branch to Selected Task
addparallelbranchonselected=在所選工作細項後新增平行分支
addOperator=新增運算子
txtAdd=新增
txtPlaceHolderText=在此拖放工作細項
@lblLayout=配置

#XMSG
VAL_UNCONNECTED_TASK=工作細項「{0}」未連線至工作細項鏈。
#XMSG
VAL_TASK_SHOULD_HAVE_ONE_INCOMING_LINK=工作細項「{0}」應只有一個內傳連結。
#XMSG
VAL_TASK_SHOULD_HAVE_INCOMING_LINK=工作細項「{0}」應有一個內傳連結。
#XMSG
VAL_UNCONNECTED_OPERATOR=運算子「{0}」未連線至工作細項鏈。
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ATLEAST_TWO_INCOMING_LINK=運算子「{0}」應至少有兩個內傳連結。
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ONE_OUT_LINK=運算子「{0}」應至少有一個外傳連結。
#XMSG
VAL_CIRCULAR_REFERENCE_EXITS=工作細項鏈「{0}」中有循環迴圈。
#XMSG
VAL_UNCONNECTED_BRANCH=物件/分支「{0}」未連線至工作細項鏈。
#XMSG
VAL_TASK_TRIGGERED_PARALLEL_MORE_THAN_ONE_TIME=工作細項「{0}」已平行連線超過一次。請移除重複項再繼續。


txtBegin=開始
txtNodesInLink=相關物件
#XTOL Tooltip for a context button on diagram
openInNewTab=在新索引標籤中開啟
#XTOL Tooltip for a context button on diagram
txtConnectToTaskOperator=拖曳以連線
@emailUpdateError=更新電子郵件通知清單時發生錯誤

#XMSG
noTeamPrivilegeTxt=您沒有權限查看租用戶成員清單。請使用「其他」索引標籤手動新增電子郵件收件人。

#XFLD Package
@txtPackage=套件

#XMSG warning for repository package assignment changed. {1} - package name
VAL_PACKAGE_CHANGED=您已將此物件指派給套件「{1}」。按一下儲存確認並驗證此更改。請注意，儲存後即無法復原此編輯器中的套件指派。
#XMSG warning for repository package dependency issue.
MISSING_DEPENDENCY=無法解析套件「{1}」內容中的物件「{0}」相關性。

#Warning messages for the folders in the "Others" tab of task chain editor
#XMSG generic warning if getting the objects in a particular folder fails with unhandled errors
getNonRepoArtefactTypeDescendantsError=檢索您所選資料夾中的物件時發生問題。
#BW Process Chains folder related warning messages
#XMSG warning if user does not have the necessary permission related to the SAP BW Bridge space
missingPrivilegeForBridgeSpaceError=您沒有必要權限，無法在工作細項鏈中檢視或包含 BW 程序鏈。
#XMSG warning if there was a problem retrieving the BW process chains due to bridge space membership, bridge connection issues or other errors
generalBridgeSpaceError=檢索 BW 程序鏈時發生問題。
#XMSG warning if the BW API for retrieving the BW process chains from the SAP BW Bridge tenant fails
bwApiClientError=從 SAP BW 橋接租用戶檢索 BW 程序鏈時發生問題。
#XMSG warning if BW process chains list is empty
bwApiEmptyListError=SAP BW 橋接租用戶中找不到 BW 程序鏈。
#XMSG: Task log message for invalid OIDC configuration in the SAP BW Bridge tenant
bwBridgeOIDCConfigStatusInvalid=未針對此租用戶組態 OpenID 驗證。請在 SAP BW 橋接租用戶中，如同 SAP 註記 3536298 所說明來使用 clientID "{0}" 和 tokenURL "{1}" 組態 OpenID 驗證。
#XMSG BW Process Chain Object has been deleted
VAL_DELETED_BW=下列 BW 程序鏈可能已從 SAP 橋接租用戶中刪除：{0}
#BW SQL Script Procedures folder related warning messages
#XFLD waring if the Procedure list is empty
procedureAPIEmptyList=未建立程序，或未將「執行」許可授予給開放式 SQL 綱要。
#Change digram orientations
changeOrientations=更改方向


# placeholder for the API Path
apiPath=例如：/job/v1
# placeholder for the status API Path
statusAPIPath=例如：/job/v1/{id}/status
# valueStateText for the API Path
apiPathRequired=API 路徑為必要
#placeholder for the CSRF Token URL
csrfTokenURL=僅支援 HTTPS
# Response Type 1
statusCode=自 HTTP 狀態代碼取得結果
# Response Type 2
locationHeader=自 HTTP 狀態代碼和位置表頭取得結果
# Response Type 3
responseBody=自 HTTP 狀態代碼和回應本文取得結果
# placeholder for ID
idPlaceholder=輸入 JSON 路徑
# placeholder for indicator value
indicatorValue=輸入值
# Placeholder for key
keyPlaceholder=輸入鍵值
# Error message for missing key
KeyRequired=鍵值為必要
# Error message for invalid key format
invalidKeyFormat=您輸入的表頭鍵值為不允許。有效表頭為：<ul><li>"prefer"</li><li>以 "x-" 而非 "x-forwarded-host" 開頭的表頭</li><li>包含英數字元、"-" 或 "_" 的表頭</li></ul>
# Error message for missing value
valueRequired=值為必要
# Error message for invalid characters in value
invalidValueCharacters=表頭包含無效字元。允許的特殊字元為：\t ";", ":", "-", "_", ",", "?", "/" 和 "*"
# Validation message for invoke api path
apiPathValidation=請輸入有效的 API 路徑，例如：/job/v1
# Validation message for JSON path
jsonPathValidation=請輸入有效的 JSON 路徑
# Validation message for success/error indicator
indicatorValueValidation=指示碼值必須以英數字元開頭，並可包含下列特殊字元：\t "-" 和 "_"
# Error message for JSON path
jsonPathRequired=JSON 路徑為必要
# Error message for invalid API Technical Name
invalidTechnicalName=技術名稱包含無效字元
# Error message for empty Technical Name
emptyTechnicalName=技術名稱為必要
# Tooltip for codeEditor dialog
codeEditorTooltip=開啟 JSON 編輯視窗
# Status Api path validation message
validationStatusAPIPath=請輸入有效的 API 路徑，例如：/job/v1/{id}/status
# CSRF token URL validation message
validationCSRFTokenURL=CSRF 權杖 URL必須以 'https://' 開頭且為有效 URL
# Select a connection
selectConnection=選擇連線
# Validation message for connection item error
connectionNotReplicated=連線目前無效，無法執行 API 工作細項。請開啟「連線」應用程式，重新輸入資格證明以修正 HTTP 連線
# Validation message for API task properties wrong value or missing
VAL_REST_API_PROPERTIES_MISSING=API 工作細項 "{0}" 不正確，或缺少下列屬性的值：{1}
# Validation message for API connection api is throwing some error or selected connection has some warning
VAL_REST_API_CONNECTION=API 工作細項 "{0}" 的 HTTP 連線發生問題。請開啟「連線」應用程式並檢查連線
# Validation message for API task properties when delected connection
VAL_REST_API_CONNECTION_DELETED=API 工作細項 "{0}" 具有已刪除的 "{1}" 連線
# Validation message for API task properties when connection cannot be consumed in the API task
VAL_REST_API_CONNECTION_CONSUMPTION_WARNING=API 工作細項 "{0}" 具有無法用來執行 API 工作細項的連線 "{1}"。請開啟「連線」應用程式，並重新輸入資格證明以建立 API 工作細項的連線
# Information message if synchrnous mode is selected in the API Invocation then we don't need status information
modeInfoText=「同步」模式不會顯示 API 呼叫的狀態面板
# validation dialog button for Run API Test
saveAnyway=一律儲存
# validation message for technical name
technicalNameValidation=工作細項鏈中的技術名稱必須是唯一。請選擇其他技術名稱
# Connection error message
getHttpConnectionsError=無法取得 HTTP 連線
# Msg for the API test run when the Task chain is not saved
taskChainNotSaved=必須先儲存工作細項鏈，才能執行 API 測試執行
# Msg failed to run API test run
@failedToRunAPI=無法執行 API 測試執行
# Msg for the API test run when its already in running state
apiTaskRunning=API 測試執行已在進行中。您要開始新的測試執行嗎？

topToBtm=上下
leftToRight=左右

#XTIT Apache Spark Application Settings
txtHeaderApacheSettings=Apache Spark 應用程式設定
#XFLD Use Default
txtUseSpaceDefault=使用預設
#XFLD Application
txtApplication=應用程式
#XFLD Define new settings for this Task
txtNewSettings=定義此工作細項的新設定

#XFLD Use Default
txtUseDefault=使用預設




