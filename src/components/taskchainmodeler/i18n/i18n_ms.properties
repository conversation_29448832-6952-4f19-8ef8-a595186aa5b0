#XTOL Undo
@undo=Buat Asal
#XTOL Redo
@redo=Buat Semula
#XTOL Delete Selected Symbol
@deleteNode=Padam Simbol Dipilih
#XTOL Zoom to Fit
@zoomToFit=Zum untuk Muat
#XTOL Auto Layout
@autoLayout=Tataletak Automatik
#XMSG
@welcomeText=Seret dan lepaskan objek dari panel di sebelah kiri ke kanvas ini.
#XMSG
@txtNoData=Tambahkan mana-mana objek.
#XMSG
VAL_ENTER_VALID_STRING_GEN=Sila masukkan rentetan sah yang panjangnya kurang daripada sama dengan {0}.
#XMSG
@noParametersMsg=Tatacara ini tidak mempunyai parameter input.
#XMSG
@ip_enterValueMsg="{0}" (Jalankan Tatacara Skrip SQL) mempunyai parameter input. Anda boleh menetapkan nilai bagi setiap satu.
#XTOL
@validateModel=Mesej <PERSON>
#XTOL
@hierarchy=Hierarki
#XTOL
@columnCount=Bilangan Lajur
#XFLD
@yes=Ya
#XFLD
@no=Tidak
#XTIT Save Dialog param
@modelNameTaskChain=Rantaian tugas
#properties panel
@lblPropertyTitle=Ciri
#XFLD
@lblGeneral=Umum
#XFLD : Setting
@lblSetting=Tetapan
#XTXT: Delta Enabled Local Table Deletion
txtDeltaTableDeletion=Padamkan semua rekod yang diproses sepenuhnya dengan Jenis Perubahan 'Dipadam' yang lebih lama daripada
#XTXT: Delta Enabled Local Table Deletion Input field description
desDeltaTableDeletion=Hari
#XFLD: Data Activation label
@lblDataActivation=Pengaktifan Data
#XFLD: Latency label
@latency=Pendaman
#XTEXT: Text for Latency dropdown
txtLatencyDefault=Lalai
#XTEXT: Text 1 hour
txtOneHour=1 Jam
#XTEXT: Text 2 hours
txtTwoHours=2 Jam
#XTEXT: Text 3 hours
txtThreeHours=3 Jam
#XTEXT: Text 4 hours
txtFourHours=4 Jam
#XTEXT: Text 6 hours
txtSixHours=6 Jam
#XTEXT: Text 12 hours
txtTwelveHours=12 Jam
#XTEXT: Text 1 day
txtOneDay=1 Hari
#XFLD: Latency label
@autoRestartHead=Mula Semula Automatik
#XFLD
@lblConnectionName=Sambungan
#XFLD
@lblQualifiedName=Nama Layak
#XFLD
@lblSpaceName=Nama Ruang
#XFLD
@lblLocalSchemaName=Skema Tempatan
#XFLD
@lblType=Jenis Objek
#XFLD
@lblActivity=Aktiviti
#XFLD
@lblTableName=Nama Jadual
#XFLD
@lblBusinessName=Nama Perniagaan
#XFLD
@lblTechnicalName=Nama Teknikal
#XFLD
@lblSpace=Ruang
#XFLD
@lblLabel=Label
#XFLD
@lblDataType=Jenis Data
#XFLD
@lblDescription=Perihalan
#XFLD
@lblStorageType=Storan
#XFLD
@lblHTTPConnection=Sambungan HTTP Generik
#XFLD
@lblAPISettings=Tetapan API Generik
#XFLD
@header=Pengepala
#XFLD
@lblInvoke=Panggilan API
#XFLD
@lblMethod=Kaedah
#XFLD
@lblUrl=URL Asas
#XFLD
@lblAPIPath=Laluan API
#XFLD
@lblMode=Mod
#XFLD
@lblCSRFToken=Memerlukan Token CSRF
#XFLD
@lblTokenURL=URL Token CSRF
#XFLD
@csrfTokenInfoText=Jika tidak dimasukkan, URL Asas dan laluan API akan digunakan
#XFLD
@lblCSRF=Token CSRF
#XFLD
@lblRequestBody=Kandungan Permintaan
#XFLD
@lblFormat=Format
#XFLD
@lblResponse=Maklum Balas
#XFLD
@lblId=ID untuk mendapatkan semula status
#XFLD
@Id=ID
#XFLD
@lblSuccessIndicator=Penunjuk Kejayaan
#XFLD
@lblErrorIndicator=Penunjuk Ralat
#XFLD
@lblErrorReason=Sebab Ralat
#XFLD
@lblStatus=Status
#XFLD
@lblApiTestRun=Jalanan Ujian API
#XFLD
@lblRunStatus=Status Jalanan
#XFLD
@lblLastRan=Jalanan Terakhir pada
#XFLD
@lblTestRun=Jalanan Ujian
#XFLD
@lblDefaultHeader=Medan Pengepala Lalai (pasangan Nilai Kod)
#XFLD
@lblAdditionalHeader=Medan Pengepala Tambahan
#XFLD
@lblKey=Kod
#XFLD
@lblEditJSON=Edit JSON
#XFLD
@lblTasks=Tugas
#XFLD
@lblRESTfullTask=API
#XFLD: add field button text
btnAddField=Tambah Medan Pengepala
#XFLD: view Details link
@viewDetails=Butiran Paparan
#XTOL
tooltipTxt=Selanjutnya
#XTOL
delete=Padam
#XBTN: ok button text
btnOk=Ok
#XBTN: cancel button text
btnCancel=Batalkan
#XBTN: save button text
btnSave=Simpan
#XMSG Error: Object has duplicated technical name.
VAL_OBJECT_DUPLICATE_NAME=Objek ''{0}'' telah wujud dalam repositori. Masukkan nama lain.
#XMSG: loading message while opening task chain
loadTaskChain=Memuatkan rantaian tugas...
#model properties
#XFLD
@status_panel=Status Jalanan
#XFLD
@deploy_status_panel=Status Atur Duduk
#XFLD
@status_lbl=Status
#XFLD
@lblLastExecuted=Jalanan Akhir
#XFLD
@lblNotExecuted=Belum Dilaksanakan Lagi
#XFLD
@lblNotDeployed=Tidak Diatur Duduk
#XFLD
errorDetailsTxt=Cuba dapatkan status jalanan sekali lagi
#XBTN: Schedule dropdown menu
SCHEDULE=Jadual
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Edit Jadual
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Padam Jadual
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Cipta Jadual
#XLNK
viewDetails=Butiran Paparan
#XMSG: error message for reading execution status from backend
backendErrorMsg=Data tidak dimuat naik daripada pelayan pada masa ini. Dapatkan data semula.
#XFLD: Status text for Completed
@statusCompleted=Selesai
#XFLD: Status text for Running
@statusRunning=Sedang Berjalan
#XFLD: Status text for Failed
@statusFailed=Gagal
#XFLD: Status text for Stopped
@statusStopped=Dihentikan
#XFLD: Status text for Stopping
@statusStopping=Pemberhentian
#XFLD
@LoaderTitle=Memuat
#XFLD
#XFLD: Deploy status, dataflow has  been deployed / deployment date exists
@deployStatusActive=Diatur Duduk
@deployStatusRevised=Kemas Kini Tempatan
@deployStatusFailed=Gagal
@deployStatusPending=Diatur Duduk...
@LoaderText=Mendapatkan butiran dari pelayan
#XMSG
@msgDetailFetchError=Ralat semasa mendapatkan butiran dari pelayan
#XFLD
@executeError=Ralat
#XFLD
@executeWarning=Amaran
#XMSG
@executeConfirmDialog=Maklumat
#XMSG
@executeunsavederror=Simpan rantaian tugas anda sebelum menjalankannya.
#XMSG
@executemodifiederror=Terdapat perubahan tidak disimpan dalam rantaian tugas. Sila simpankannya.
#XMSG
@executerunningerror=Rantaian tugas sedang berjalan. Tunggu sehingga jalanan semasa selesai sebelum memulakan yang baharu.
#XMSG
@btnExecuteAnyway=Jalankan Juga
#XMSG
@msgExecuteWithValidations=Rantaian tugas mempunyai ralat pengesahan. Menjalankan tugas mungkin menyebabkan kegagalan.
#XMSG
@msgRunDeployedVersion=Terdapat perubahan untuk atur duduk. Versi terakhir atur duduk bagi rantaian tugas akan dijalankan. Adakah anda ingin teruskan?
#XMSG
#XMSG
@navToMonitoring=Buka dalam Pemantau Rantaian Tugas
#XMSG
txtOR=ATAU
#XFLD
@preview=Pratonton
#XMSG
txtand=dan
#NOTR
@dateFormat=yyyy-MM-dd
#NOTR
@dateTimeFormatFilter=yyyy-MM-dd HH:mm:ss.
#NOTR
@timeFormat=HH:mm:ss
@lblColum=Lajur
#XFLD
@lblCondition=Syarat
#XFLD
@lblValue=Nilai
#XMSG
@msgJsonInvalid=Rantaian tugas tidak berjaya disimpan kerana terdapat ralat dalam JSON. Sila semak dan selesaikan.
#XTIT
@msgSaveFailTitle=JSON tidak sah.
#XMSG
NOT_CHAINABLE=Objek "{0}" tidak boleh ditambah ke rantaian tugas.
#XMSG
NOT_CHAINABLE_REMOTETABLE=Objek "{0}": jenis replikasi akan berubah.
#XMSG
searchTaskChain=Cari Objek

#XFLD
@txtTaskChain=Rantaian Tugas
#XFLD
@txtRemoteTable=Jadual Jauh
#XFLD
@txtRemoveData=Keluarkan data Direplikakan
#XFLD
@txtRemovePersist=Keluarkan Data Berterusan
#XFLD
@txtView=Papar
#XFLD
@txtDataFlow=Aliran Data
#XFLD
@txtIL=Carian Pintar
#XFLD
@txtTransformationFlow=Aliran Perubahan
#XFLD
@txtReplicationFlow=Aliran Replikasi
#XFLD
@txtDeltaLocalTable=Jadual Tempatan
#XFLD
@txtBWProcessChain=Rantaian Proses BW
#XFLD
@txtSQLScriptProcedure=Tatacara Skrip SQL
#XFLD
@txtAPI=API
#XFLD
@txtMerge=Gabungkan
#XFLD
@txtOptimize=Optimumkan
#XFLD
@txtVacuum=Padam Rekod

#XFLD
@txtRun=Jalanan
#XFLD
@txtPersist=Teruskan
#XFLD
@txtReplicate=Replikakan
#XFLD
@txtDelete=Padam Rekod dengan Jenis Perubahan 'Dipadam'
#XFLD
@txtRunTC=Jalankan Rantaian Tugas
#XFLD
@txtRunBW=Jalankan Rantaian Proses BW
#XFLD
@txtRunSQLScriptProcedure=Jalankan Tatacara Skrip SQL

#XFLD
@txtRunDataFlow=Jalankan Aliran Data
#XFLD
@txtPersistView=Teruskan Paparan
#XFLD
@txtReplicateTable=Replikakan Jadual
#XFLD
@txtRunIL=Jalankan Carian Pintar
#XFLD
@txtRunTF=Jalankan Aliran Perubahan
#XFLD
@txtRunRF=Jalankan Aliran Replikasi
#XFLD
@txtRemoveReplicatedData=Keluarkan data Direplikakan
#XFLD
@txtRemovePersistedData=Keluarkan Data Berterusan
#XFLD
@txtMergeData=Gabungkan
#XFLD
@txtOptimizeData=Optimumkan
#XFLD
@txtVacuumData=Padam Rekod
#XFLD
@txtRunAPI=Jalankan API

#XFLD storage type text
hdlfStorage=Fail

@statusNew=Tidak Diatur Duduk
@statusActive=Diatur Duduk
@statusRevised=Kemas Kini Tempatan
@statusPending=Diatur Duduk...
@statusChangesToDeploy=Perubahan untuk Atur Duduk
@statusDesignTimeError=Ralat Masa Reka Bentuk
@statusRunTimeError=Ralat Masa Jalanan

#XTIT
txtNodes=Objek dalam Rantaian Tugas ({0})
#XBTN
@deleteNodes=Padam

#XMSG
@txtDropDataToDiagram=Seret dan lepaskan objek ke gambar rajah.
#XMSG
@noData=Tiada objek

#XFLD
@txtTaskPosition=Kedudukan Tugas

#input parameters and variables
#XFLD
lblInputParameters=Parameter Input
#XFLD
ip_name=Nama
#XFLD
ip_value=Nilai
#XTEXT
@noObjectsFound=Tiada Objek Ditemui

#XMSG
@msgExecuteSuccess=Jalanan rantaian tugas telah bermula.
#XMSG
@msgExecuteFail=Cuba semula jalanan rantaian tugas kemudian.
#XMSG
@msgDeployAndRunSuccess=Pengerahan dan jalanan rantaian tugas telah bermula.
#XMSG
@msgDeployAndRunFail=Cuba semula mengatur duduk dan menjalankan rantaian tugas.
#XMSG
@titleExecuteBusy=Tunggu.
#XMSG
@msgExecuteBusy=Kami sedang menyediakan data anda untuk mula menjalankan rantaian tugas.
#XMSG
@msgAPITestRunSuccess=Jalanan ujian API telah dimulakan.
#XMSG
@msgAPIExecuteBusy=Kami sedang menyediakan data anda untuk mula menjalankan jalanan ujian API.

#XTOL
txtOpenInEditor=Buka dalam Editor
#XTOL
txtPreviewData=Data Pratonton

#datapreview
#XMSG
@msgDataPreviewNotSupp=Pratonton data tidak tersedia untuk objek ini.

#XMSG Error: empty model
VAL_MODEL_EMPTY=Model anda kelihatan kosong. Tambah beberapa objek.
#XMSG Error: deploy model
@msgDeployBeforeRun=Anda perlu atur duduk rantaian tugas sebelum menjalankannya.
#BTN: close dialog
btnClose=Tutup

#XMSG Object is not Deployed
VAL_NOT_DEPLOYED=Objek "{0}" mesti diatur duduk untuk teruskan dengan rantaian tugas.
#XMSG Object is having runtime error
VAL_RUNTIME_ERROR=Objek "{0}" mengembalikan ralat masa jalanan. Sila semak objek.
#XMSG Object is having design time error
VAL_DESIGNTIME_ERROR=Objek "{0}"  mengembalikan ralat masa rekaan. Sila semak objek.
#XMSG Procedure Object has been deleted
VAL_DELETED_PROCEDURE=Tatacara berikut telah dipadam: {0}
#XMSG New Parameter added to Procedure object
VAL_NEW_PARAMETER_ADDED=Parameter baharu ditambah pada tatacara "{1}": "{0}". Atur duduk semula rantaian tugas.
#XMSG Parameter removed from Procedure object
VAL_PARAMETER_DELETED=Parameter dikeluarkan daripada tatacara "{1}": "{0}". Atur duduk semula rantaian tugas.
#XMSG Parameter data type changed in Procedure object
VAL_PARAMETER_DATATYPE_MODIFIED=Data jenis parameter "{0}" diubah daripada "{1}" kepada "{2}" dalam tatacara "{3}".
#XMSG Parameter length changed of specific datatype in Procedure object
VAL_PARAMETER_LENGTH_MODIFIED=Panjang parameter "{0}" diubah daripada "{1}" kepada "{2}" dalam tatacara "{3}".
#XMSG Parameter precision changed of Decimal datatype in Procedure object
VAL_PARAMETER_PRECISION_MODIFIED=Ketepatan parameter "{0}" diubah daripada "{1}" kepada "{2}" dalam tatacara "{3}".
#XMSG Parameter scale changed of Decimal datatype in Procedure object
VAL_PARAMETER_SCALE_MODIFIED=Skala parameter "{0}" diubah daripada "{1}" kepada "{2}" dalam tatacara "{3}".
#XMSG Parameter value not provided in Procedure object
VAL_SQL_SCRIPT_PROCEDURE_PARAMETER_VALUE_MISSING=Anda perlu memasukkan nilai untuk parameter input yang diperlukan untuk tatacara "{0}": "{1}"
#XMSG Remote Table activity changes to Remove Replicated Data
VAL_REMOVE_REPLICATED_DATA=Rantaian tugas seterusnya akan mengubah jenis capaian data dan data tidak akan dimuat naik dalam masa nyata lagi.
#XMSG Remote Table activity changes
REPLICATION_TYPE_CHANGED=Objek "{0}": Jenis replikasi akan berubah.

#XFLD
@lblStartNode=Nod Permulaan
#XFLD
@lblEndNode=Nod Akhir
#XFLD
@linkTo={0} hingga {1}
#XTOL
@txtViewDetails=Butiran Paparan

#XTOL
txtOpenImpactLineage=Analisis Asal-usul dan Kesan
#XFLD
@emailNotifications=Pemberitahuan E-mel
#XFLD
@txtReset=Tetapkan Semula
#XFLD
@emailMsgWarning=Penghantaran e-mel templat lalai berlaku apabila mesej e-mel kosong
#XFLD
@notificationSettings=Tetapan Pemberitahuan
#XFLD
@recipientEmailAddr=Alamat E-mel Penerima
#XFLD
@emailSubject=Subjek E-mel
@emailSubjectText=Rantaian tugas <TASKCHAIN_NAME> diselesaikan dengan status <STATUS>
#XFLD
@emailMessage=Mesej E-mel
@emailMessageText=Pengguna yang Dihormati,\n\n Ini adalah untuk memberitahu anda bahawa rantaian tugasan:<TASKCHAIN_NAME> dijalankan pada <START_TIME> telah selesai dengan status <STATUS>. Pelaksanaan tamat pada <END_TIME>.\n\nButiran:\nRuang:<SPACE_NAME>\nRalat:<ERROR_MESSAGE>
@selectEmailRecipientDialogTitle=Pilih Alamat E-mel Penerima
@tenantMembers=Ahli penyewa
@others=Lain-lain({0})
@selectedEmailAddress=Penerima Terpilih
@add=Tambah
@placeholder=Ruang Letak
@description=Perihalan
@copyText=Salin teks
@taskchainDetailsPlaceholder=Ruang letak untuk butiran rantaian tugas
@placeholderCopied=Ruang letak disalin
@invalidEmailInfo=Masukkan alamat e-mel yang betul
@maxMembersAlreadyAdded=Anda telah menambah maksimum 20 ahli
@enterEmailAddress=Masukkan alamat e-mel pengguna
@inCorrectPlaceHolder={0} bukan ruang letak yang dijangka dalam badan e-mel.
@nsOFF=Mana-mana pemberitahuan tidak boleh dihantar
@nsFAILED=Hantar pemberitahuan e-mel hanya apabila jalanan telah selesai dengan ralat
@nsCOMPLETED=Hantar pemberitahuan e-mel hanya apabila jalanan telah berjaya diselesaikan
@nsANY=Hantar e-mel apabila jalanan telah diselesaikan
@phStatus=Status rantaian tugas - BERJAYA|TIDAK BERJAYA
@phTaskChainTName=Nama teknikal rantaian tugas
@phTaskChainBName=Nama perniagaan rantaian tugas
@phLogId=ID Log jalanan
@phUser=Pengguna yang menjalankan rantaian tugas
@phLogUILink=Pautan ke paparan log rantaian tugas
@phStartTime=Masa mula jalanan
@phEndTime=Masa tamat jalanan
@phErrMsg=Mesej ralat pertama dalam log tugas. Log kosong sekiranya BERJAYA
@phSpaceName=Nama teknikal ruang
@emailFormatError=Format e-mel tidak sah
@emailFormatErrorInListText=Format e-mel tidak sah dimasukkan dalam senarai ahli bukan penyewa.
@emailSubjectTemplateText=Pemberitahuan untuk Rantaian Tugas: $$taskChainName$$ - Ruang: $$spaceId$$ - Status: $$status$$
@emailMessageTemplateText=Hello,\n\n Rantaian tugas anda berlabel $$taskChainName$$ telah selesai dengan status $$status$$. \n Berikut ialah beberapa butiran lain tentang rantaian tugas:\n - Nama teknikal rantaian tugas: $$taskChainName$$ \n - Log ID bagi rantaian tugas yang dijalankan: $$logId$$ \n - Pengguna yang menjalankan rantaian tugas: $$user$$ \n - Pautan ke paparan log rantaian tugasan: $$uiLink$$ \n - Masa mula jalanan rantaian tugas: $$startTime$$ \n - Masa tamat jalanan rantaian tugas: $$endTime$$ \n - Nama ruang: $$spaceId$$ \n
@deleteEmailRecepient=Padam Penerima
@emailInputDisabledText=Sila atur duduk rantaian tugas untuk menambah penerima e-mel.
@tenantOwnerDomainMatchErrorText=Anda perlu masukkan domain alamat e-mel yang sepadan dengan domain pemilik penyewa: {0}
@totalEmailIdLimitInfoText=Anda boleh pilih sehingga 20 penerima e-mel termasuk pengguna ahli penyewa dan penerima lain.
@emailDomainInfoText=Hanya alamat e-mel dengan domain: {0} diterima.
@duplicateEmailErrorText=Terdapat penerima e-mel pendua dalam senarai.

#XFLD Zorder Title
@txtZorderTitle=Lajur Susunan Z Apache Spark

#XFLD Zorder NoColumn
@txtZorderNoColumn=Tiada Lajur Susunan Z Ditemui

#XFLD Zorder Column PrimaryKey
@txtZorderColumnPrimaryKey=Kod Utama

#XFLD
@lblOperators=Pengendali
addNewSelector=Tambah sebagai Tugas Baharu
parallelSelector=Tambah sebagai Tugas Selari
replaceSelector=Gantikan Tugas Sedia Ada
addparallelbranch=Tambah sebagai Cawangan Selari
addplaceholder=Tambah Ruang Letak
addALLOperation=SEMUA Pengendali
addOROperation=APA-APA Pengendali
addplaceholdertocanvas=Tambah Ruang Letak pada Kanvas
addplaceholderonselected=Tambah Ruang Letak selepas Tugas Terpilih
#XFLD: Add Parallel Branch to Selected Task
addparallelbranchonselected=Tambah Cawangan Selari selepas Tugas Terpilih
addOperator=Tambah Pengendali
txtAdd=Tambah
txtPlaceHolderText=Seret & Letak Tugas di sini
@lblLayout=Tataletak

#XMSG
VAL_UNCONNECTED_TASK=Tugas ''''{0}'''' tidak bersambung dengan rantaian tugas.
#XMSG
VAL_TASK_SHOULD_HAVE_ONE_INCOMING_LINK=Tugas ''''{0}'''' perlu mempunyai satu pautan masuk sahaja.
#XMSG
VAL_TASK_SHOULD_HAVE_INCOMING_LINK=Tugas ''''{0}'''' perlu mempunyai satu pautan masuk.
#XMSG
VAL_UNCONNECTED_OPERATOR=Pengendali ''''{0}'''' tidak bersambung dengan rantaian tugas.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ATLEAST_TWO_INCOMING_LINK=Pengendali ''''{0}'''' perlu mempunyai sekurang-kurang dua pautan masuk.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ONE_OUT_LINK=Pengendali ''''{0}'''' perlu mempunyai sekurang-kurang satu pautan keluar.
#XMSG
VAL_CIRCULAR_REFERENCE_EXITS=Gelung pekeliling wujud dalam rantaian tugas ''''{0}''''.
#XMSG
VAL_UNCONNECTED_BRANCH=Objek/cawangan ''''{0}'''' tidak bersambung dengan rantaian tugas.
#XMSG
VAL_TASK_TRIGGERED_PARALLEL_MORE_THAN_ONE_TIME=Tugas ''''{0}'''' bersambung secara selari lebih daripada sekali. Sila keluarkan penduaan untuk teruskan.


txtBegin=Mulakan
txtNodesInLink=Objek Terlibat
#XTOL Tooltip for a context button on diagram
openInNewTab=Buka dalam Tab Baharu
#XTOL Tooltip for a context button on diagram
txtConnectToTaskOperator=Seret untuk Sambungkan
@emailUpdateError=Ralat dalam mengemas kini senarai Pemberitahuan E-mel

#XMSG
noTeamPrivilegeTxt=Anda tidak mempunyai kebenaran untuk melihat senarai ahli penyewa. Gunakan tab Lain-Lain untuk menambah penerima e-mel secara manual.

#XFLD Package
@txtPackage=Pakej

#XMSG warning for repository package assignment changed. {1} - package name
VAL_PACKAGE_CHANGED=Anda telah umpukkan objek ini kepada pakej ''''{1}''''. Klik Simpan untuk mengesahkan perubahan ini. Ambil perhatian bahawa umpukan kepada pakej dalam editor ini tidak boleh dibatalkan selepas anda menyimpannya.
#XMSG warning for repository package dependency issue.
MISSING_DEPENDENCY=Tidak boleh selesaikan semula kebersandaran objek ''''{0}'''' dalam konteks pakej ''''{1}''''.

#Warning messages for the folders in the "Others" tab of task chain editor
#XMSG generic warning if getting the objects in a particular folder fails with unhandled errors
getNonRepoArtefactTypeDescendantsError=Terdapat masalah mendapatkan semula objek dalam folder yang anda pilih.
#BW Process Chains folder related warning messages
#XMSG warning if user does not have the necessary permission related to the SAP BW Bridge space
missingPrivilegeForBridgeSpaceError=Anda perlu mempunyai kebenaran yang diperlukan untuk memaparkan atau memasukkan rantaian proses BW dalam rantaian tugas.
#XMSG warning if there was a problem retrieving the BW process chains due to bridge space membership, bridge connection issues or other errors
generalBridgeSpaceError=Terdapat masalah mendapatkan semula rantaian proses BW.
#XMSG warning if the BW API for retrieving the BW process chains from the SAP BW Bridge tenant fails
bwApiClientError=Terdapat masalah mendapatkan semula rantaian proses BW daripada penyewa SAP BW Bridge.
#XMSG warning if BW process chains list is empty
bwApiEmptyListError=Tiada rantaian proses BW ditemui dalam penyewa SAP BW Bridge.
#XMSG: Task log message for invalid OIDC configuration in the SAP BW Bridge tenant
bwBridgeOIDCConfigStatusInvalid=Pengesahan OpenID tidak dikonfigurasikan untuk penyewa ini. Gunakan clientID "{0}" dan tokenURL "{1}" untuk konfigurasikan Pengesahan OpenID dalam penyewa penghubung SAP BW seperti yang diterangkan dalam nota SAP 3536298.
#XMSG BW Process Chain Object has been deleted
VAL_DELETED_BW=Rantaian proses BW berikut mungkin dipadamkan daripada penyewa SAP Bridge: {0}
#BW SQL Script Procedures folder related warning messages
#XFLD waring if the Procedure list is empty
procedureAPIEmptyList=Cipta tatacara atau keistimewaan LAKSANAKAN diperlukan untuk membuka skema SQL Terbuka.
#Change digram orientations
changeOrientations=Ubah Orientasi


# placeholder for the API Path
apiPath=Contohnya: /job/v1
# placeholder for the status API Path
statusAPIPath=Contohnya: /job/v1/{id}/status
# valueStateText for the API Path
apiPathRequired=Laluan API diperlukan
#placeholder for the CSRF Token URL
csrfTokenURL=Hanya HTTPS disokong
# Response Type 1
statusCode=Dapatkan hasil daripada kod status HTTP
# Response Type 2
locationHeader=Dapatkan hasil daripada kod status HTTP dan pengepala lokasi
# Response Type 3
responseBody=Dapatkan hasil daripada kod status HTTP dan kandungan maklum balas
# placeholder for ID
idPlaceholder=Masukkan Laluan JSON
# placeholder for indicator value
indicatorValue=Masukkan nilai
# Placeholder for key
keyPlaceholder=Masukkan kod
# Error message for missing key
KeyRequired=Kod diperlukan
# Error message for invalid key format
invalidKeyFormat=Kod pengepala yang anda masukkan tidak dibenarkan. Pengepala yang sah ialah:<ul><li>"pilihan"</li><li>Pengepala bermula dengan "x-", kecuali "x dimajukan hos"</li><li>Pengepala yang mengandungi aksara abjad angka, "-", atau "_"</li></ul>
# Error message for missing value
valueRequired=Nilai diperlukan
# Error message for invalid characters in value
invalidValueCharacters=Pengepala mengandungi aksara yang tidak sah. Aksara khas yang dibenarkan ialah:\t ";", ":", "-", "_", ",", "?", "/", dan "*"
# Validation message for invoke api path
apiPathValidation=Masukkan laluan API yang sah, contohnya: /job/v1
# Validation message for JSON path
jsonPathValidation=Masukkan laluan JSON yang sah
# Validation message for success/error indicator
indicatorValueValidation=Nilai penunjuk mesti bermula dengan aksara abjad angka dan boleh termasuk aksara khas berikut:\t "-", dan "_"
# Error message for JSON path
jsonPathRequired=Laluan JSON diperlukan
# Error message for invalid API Technical Name
invalidTechnicalName=Masukkan nama teknikal mengandungi aksara yang sah
# Error message for empty Technical Name
emptyTechnicalName=Masukkan Nama Teknikal
# Tooltip for codeEditor dialog
codeEditorTooltip=Buka tetingkap Edit JSON
# Status Api path validation message
validationStatusAPIPath=Masukkan laluan API yang sah, contohnya: /job/v1/{id}/status
# CSRF token URL validation message
validationCSRFTokenURL=URL Token CSRF mesti bermula dengan 'https://' dan menjadi URL yang sah
# Select a connection
selectConnection=Pilih sambungan
# Validation message for connection item error
connectionNotReplicated=Sambungan pada masa ini tidak sah untuk menjalankan tugas API. Buka aplikasi "Sambungan" dan masukkan semula kelayakan anda untuk membetulkan sambungan HTTP
# Validation message for API task properties wrong value or missing
VAL_REST_API_PROPERTIES_MISSING=Tugas API "{0}" mempunyai nilai yang salah atau tiada untuk sifat berikut: {1}
# Validation message for API connection api is throwing some error or selected connection has some warning
VAL_REST_API_CONNECTION=Tugas API "{0}" mempunyai isu dengan sambungan HTTP. Buka aplikasi "Sambungan" dan semak sambungan
# Validation message for API task properties when delected connection
VAL_REST_API_CONNECTION_DELETED=Tugas API "{0}" mempunyai sambungan "{1}" yang dipadam
# Validation message for API task properties when connection cannot be consumed in the API task
VAL_REST_API_CONNECTION_CONSUMPTION_WARNING=Tugas API "{0}" mempunyai sambungan "{1}" yang tidak boleh digunakan untuk menjalankan tugas API. Buka aplikasi "Sambungan" dan masukkan semula kelayakan anda untuk mewujudkan ketersambungan bagi tugas API
# Information message if synchrnous mode is selected in the API Invocation then we don't need status information
modeInfoText=Dalam mod Segerak, panel status tidak dipaparkan untuk panggilan API
# validation dialog button for Run API Test
saveAnyway=Simpan Juga
# validation message for technical name
technicalNameValidation=Nama teknikal mestilah unik dalam rantaian tugas. Pilih nama teknikal lain
# Connection error message
getHttpConnectionsError=Tidak berjaya mendapatkan sambungan HTTP
# Msg for the API test run when the Task chain is not saved
taskChainNotSaved=Rantaian tugas mesti disimpan sebelum menjalankan jalanan ujian API
# Msg failed to run API test run
@failedToRunAPI=Jalanan ujian API tidak berjaya dijalankan
# Msg for the API test run when its already in running state
apiTaskRunning=Jalanan ujian API sedang dijalankan. Adakah anda ingin mulakan jalanan ujian baharu?

topToBtm=Atas ke Bawah
leftToRight=Kiri ke Kanan

#XTIT Apache Spark Application Settings
txtHeaderApacheSettings=Tetapan Aplikasi Apache Spark
#XFLD Use Default
txtUseSpaceDefault=Gunakan Lalai
#XFLD Application
txtApplication=Aplikasi
#XFLD Define new settings for this Task
txtNewSettings=Takrifkan tetapan baharu untuk Tugas ini

#XFLD Use Default
txtUseDefault=Gunakan Lalai




