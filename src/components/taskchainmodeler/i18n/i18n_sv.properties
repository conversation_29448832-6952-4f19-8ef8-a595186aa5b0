#XTOL Undo
@undo=Ångra
#XTOL Redo
@redo=Gör om
#XTOL Delete Selected Symbol
@deleteNode=Radera vald symbol
#XTOL Zoom to Fit
@zoomToFit=Anpassa zoomning
#XTOL Auto Layout
@autoLayout=Automatisk layout
#XMSG
@welcomeText=Dra och släpp objekt från den vänstra panelen till denna arbetsyta.
#XMSG
@txtNoData=Du verkar inte ha lagt till något objekt än.
#XMSG
VAL_ENTER_VALID_STRING_GEN=Ange en giltig sträng som är kortare än eller lika med {0}.
#XMSG
@noParametersMsg=Proceduren har inga inparametrar.
#XMSG
@ip_enterValueMsg="{0}" (Kör SQL-skriptprocedur) har inparametrar. Du kan ange ett värde för var och en av dem.
#XTOL
@validateModel=Valideringsmeddelanden
#XTOL
@hierarchy=Hierarki
#XTOL
@columnCount=<PERSON>tal kolumner
#XFLD
@yes=Ja
#XFLD
@no=Nej
#XTIT Save Dialog param
@modelNameTaskChain=Uppgiftskedja
#properties panel
@lblPropertyTitle=Egenskaper
#XFLD
@lblGeneral=Allmänt
#XFLD : Setting
@lblSetting=Inställningar
#XTXT: Delta Enabled Local Table Deletion
txtDeltaTableDeletion=Radera alla fullständigt bearbetade poster med ändringstyp "Raderad" som är äldre än
#XTXT: Delta Enabled Local Table Deletion Input field description
desDeltaTableDeletion=Dagar
#XFLD: Data Activation label
@lblDataActivation=Dataaktivering
#XFLD: Latency label
@latency=Latens
#XTEXT: Text for Latency dropdown
txtLatencyDefault=Standard
#XTEXT: Text 1 hour
txtOneHour=1 timme
#XTEXT: Text 2 hours
txtTwoHours=2 timmar
#XTEXT: Text 3 hours
txtThreeHours=3 timmar
#XTEXT: Text 4 hours
txtFourHours=4 timmar
#XTEXT: Text 6 hours
txtSixHours=6 timmar
#XTEXT: Text 12 hours
txtTwelveHours=12 timmar
#XTEXT: Text 1 day
txtOneDay=1 dag
#XFLD: Latency label
@autoRestartHead=Automatisk omstart
#XFLD
@lblConnectionName=Anslutning
#XFLD
@lblQualifiedName=Kvalificerat namn
#XFLD
@lblSpaceName=Namn på utrymme
#XFLD
@lblLocalSchemaName=Lokalt schema
#XFLD
@lblType=Objekttyp
#XFLD
@lblActivity=Aktivitet
#XFLD
@lblTableName=Tabellnamn
#XFLD
@lblBusinessName=Affärsnamn
#XFLD
@lblTechnicalName=Tekniskt namn
#XFLD
@lblSpace=Utrymme
#XFLD
@lblLabel=Etikett
#XFLD
@lblDataType=Datatyp
#XFLD
@lblDescription=Beskrivning
#XFLD
@lblStorageType=Lagring
#XFLD
@lblHTTPConnection=Anslutning för generisk HTTP
#XFLD
@lblAPISettings=Generiska API-inställningar
#XFLD
@header=Huvuden
#XFLD
@lblInvoke=API-anrop
#XFLD
@lblMethod=Metod
#XFLD
@lblUrl=Bas-URL
#XFLD
@lblAPIPath=API-sökväg
#XFLD
@lblMode=Läge
#XFLD
@lblCSRFToken=Kräv CSRF-token
#XFLD
@lblTokenURL=URL för CSRF-token
#XFLD
@csrfTokenInfoText=Om detta inte anges kommer bas-URL och API-sökväg att användas
#XFLD
@lblCSRF=CSRF-token
#XFLD
@lblRequestBody=Text i begäran
#XFLD
@lblFormat=Format
#XFLD
@lblResponse=Svar
#XFLD
@lblId=ID för att hämta status
#XFLD
@Id=ID
#XFLD
@lblSuccessIndicator=Resultatindikator
#XFLD
@lblErrorIndicator=Felindikator
#XFLD
@lblErrorReason=Orsak till fel
#XFLD
@lblStatus=Status
#XFLD
@lblApiTestRun=API-testkörning
#XFLD
@lblRunStatus=Körningsstatus
#XFLD
@lblLastRan=Senaste körning den
#XFLD
@lblTestRun=Testkörning
#XFLD
@lblDefaultHeader=Standardhuvudfält (nyckelvärdespar)
#XFLD
@lblAdditionalHeader=Ytterligare huvudfält
#XFLD
@lblKey=Nyckel
#XFLD
@lblEditJSON=Redigera JSON
#XFLD
@lblTasks=Uppgifter
#XFLD
@lblRESTfullTask=API
#XFLD: add field button text
btnAddField=Lägg till huvudfält
#XFLD: view Details link
@viewDetails=Visa detaljer
#XTOL
tooltipTxt=Mer
#XTOL
delete=Radera
#XBTN: ok button text
btnOk=OK
#XBTN: cancel button text
btnCancel=Avbryt
#XBTN: save button text
btnSave=Spara
#XMSG Error: Object has duplicated technical name.
VAL_OBJECT_DUPLICATE_NAME=Objekt ''{0}'' finns redan i repository. Ange ett annat namn.
#XMSG: loading message while opening task chain
loadTaskChain=Läser in uppgiftskedja...
#model properties
#XFLD
@status_panel=Körningsstatus
#XFLD
@deploy_status_panel=Distribueringsstatus
#XFLD
@status_lbl=Status
#XFLD
@lblLastExecuted=Senaste körning
#XFLD
@lblNotExecuted=Ej utförd än
#XFLD
@lblNotDeployed=Ej distribuerad
#XFLD
errorDetailsTxt=Körningsstatus kunde inte hämtas
#XBTN: Schedule dropdown menu
SCHEDULE=Schema
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Redigera schema
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Radera schema
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Skapa schema
#XLNK
viewDetails=Visa detaljer
#XMSG: error message for reading execution status from backend
backendErrorMsg=Det ser inte ut som att data läses in från servern just nu. Pröva att hämta data igen.
#XFLD: Status text for Completed
@statusCompleted=Slutförd
#XFLD: Status text for Running
@statusRunning=Körs
#XFLD: Status text for Failed
@statusFailed=Misslyckades
#XFLD: Status text for Stopped
@statusStopped=Stoppad
#XFLD: Status text for Stopping
@statusStopping=Stoppas
#XFLD
@LoaderTitle=Läser in
#XFLD
#XFLD: Deploy status, dataflow has  been deployed / deployment date exists
@deployStatusActive=Distribuerad
@deployStatusRevised=Lokala uppdateringar
@deployStatusFailed=Misslyckades
@deployStatusPending=Distribuerar...
@LoaderText=Hämtar detaljer från servern
#XMSG
@msgDetailFetchError=Fel vid hämtning av detaljer från servern
#XFLD
@executeError=Fel
#XFLD
@executeWarning=Varning
#XMSG
@executeConfirmDialog=Info
#XMSG
@executeunsavederror=Spara uppgiftskedja innan den körs.
#XMSG
@executemodifiederror=Det finns ändringar som inte sparats i uppgiftskedjan. Spara den.
#XMSG
@executerunningerror=Uppgiftskedja körs. Vänta tills aktuell körning har slutförts innan en ny startas.
#XMSG
@btnExecuteAnyway=Kör ändå
#XMSG
@msgExecuteWithValidations=Uppgiftskedjan har valideringsfel. Utförande av uppgiftskedjan kan leda till fel.
#XMSG
@msgRunDeployedVersion=Det finns ändringar att distribuera. Den senast distribuerade versionen av uppgiftskedjan utförs. Vill du fortsätta?
#XMSG
#XMSG
@navToMonitoring=Öppna i uppgiftskedjemonitor
#XMSG
txtOR=ELLER
#XFLD
@preview=Förhandsgranskning
#XMSG
txtand=och
#NOTR
@dateFormat=yyyy-MM-dd
#NOTR
@dateTimeFormatFilter=yyyy-MM-dd HH:mm:ss.
#NOTR
@timeFormat=HH:mm:ss
@lblColum=Kolumn
#XFLD
@lblCondition=Villkor
#XFLD
@lblValue=Värde
#XMSG
@msgJsonInvalid=Uppgiftskedja kunde inte sparas eftersom JSON innehåller fel. Kontrollera och åtgärda.
#XTIT
@msgSaveFailTitle=Ogiltig JSON.
#XMSG
NOT_CHAINABLE=Objekt ''{0}'' kan inte läggas till i uppgiftskedja.
#XMSG
NOT_CHAINABLE_REMOTETABLE=Objekt "{0}": Replikeringstyp ändras.
#XMSG
searchTaskChain=Sök objekt

#XFLD
@txtTaskChain=Uppgiftskedja
#XFLD
@txtRemoteTable=Fjärrtabell
#XFLD
@txtRemoveData=Ta bort replikerade data
#XFLD
@txtRemovePersist=Ta bort persisterade data
#XFLD
@txtView=Vy
#XFLD
@txtDataFlow=Dataflöde
#XFLD
@txtIL=Intelligent sökning
#XFLD
@txtTransformationFlow=Transformationsflöde
#XFLD
@txtReplicationFlow=Replikeringsflöde
#XFLD
@txtDeltaLocalTable=Lokal tabell
#XFLD
@txtBWProcessChain=BW-processkedja
#XFLD
@txtSQLScriptProcedure=SQL-skriptprocedur
#XFLD
@txtAPI=API
#XFLD
@txtMerge=Slå samman
#XFLD
@txtOptimize=Optimera
#XFLD
@txtVacuum=Radera poster

#XFLD
@txtRun=Kör
#XFLD
@txtPersist=Persistera
#XFLD
@txtReplicate=Replikera
#XFLD
@txtDelete=Radera poster med ändringstyp "Raderad"
#XFLD
@txtRunTC=Kör uppgiftskedja
#XFLD
@txtRunBW=Kör BW-processkedja
#XFLD
@txtRunSQLScriptProcedure=Kör SQL-skriptprocedur

#XFLD
@txtRunDataFlow=Kör dataflöde
#XFLD
@txtPersistView=Persistera vy
#XFLD
@txtReplicateTable=Replikera tabell
#XFLD
@txtRunIL=Kör intelligent sökning
#XFLD
@txtRunTF=Kör transformationsflöde
#XFLD
@txtRunRF=Kör replikeringsflöde
#XFLD
@txtRemoveReplicatedData=Ta bort replikerade data
#XFLD
@txtRemovePersistedData=Ta bort persisterade data
#XFLD
@txtMergeData=Slå samman
#XFLD
@txtOptimizeData=Optimera
#XFLD
@txtVacuumData=Radera poster
#XFLD
@txtRunAPI=Kör API

#XFLD storage type text
hdlfStorage=Fil

@statusNew=Ej distribuerad
@statusActive=Distribuerad
@statusRevised=Lokala uppdateringar
@statusPending=Distribuerar...
@statusChangesToDeploy=Ändringar att distribuera
@statusDesignTimeError=Designtidsfel
@statusRunTimeError=Körtidsfel

#XTIT
txtNodes=Objekt i uppgiftskedja ({0})
#XBTN
@deleteNodes=Radera

#XMSG
@txtDropDataToDiagram=Dra och släpp objekt till diagram.
#XMSG
@noData=Inga objekt

#XFLD
@txtTaskPosition=Uppgiftsposition

#input parameters and variables
#XFLD
lblInputParameters=Inparametrar
#XFLD
ip_name=Namn
#XFLD
ip_value=Värde
#XTEXT
@noObjectsFound=Inga objekt hittades

#XMSG
@msgExecuteSuccess=Körning av uppgiftskedja har startat.
#XMSG
@msgExecuteFail=Uppgiftskedja kunde inte köras.
#XMSG
@msgDeployAndRunSuccess=Distribution och körning av uppgiftskedja har startat.
#XMSG
@msgDeployAndRunFail=Uppgiftskedja kunde inte distribueras och utföras.
#XMSG
@titleExecuteBusy=Vänta.
#XMSG
@msgExecuteBusy=Data förbereds för körning av uppgiftskedja.
#XMSG
@msgAPITestRunSuccess=API-testkörning har startat.
#XMSG
@msgAPIExecuteBusy=Data förbereds för körning av API-testkörning.

#XTOL
txtOpenInEditor=Öppna i editor
#XTOL
txtPreviewData=Förhandsgranska data

#datapreview
#XMSG
@msgDataPreviewNotSupp=Förhandsgranskning av data är inte tillgänglig för detta objekt.

#XMSG Error: empty model
VAL_MODEL_EMPTY=Din modell verkar vara tom. Lägg till objekt.
#XMSG Error: deploy model
@msgDeployBeforeRun=Uppgiftskedja måste distribueras innan körning.
#BTN: close dialog
btnClose=Stäng

#XMSG Object is not Deployed
VAL_NOT_DEPLOYED=Objekt "{0}" måste distribueras för att uppgiftskedjan ska kunna fortsätta.
#XMSG Object is having runtime error
VAL_RUNTIME_ERROR=Objekt ''{0}'' returnerar ett körtidsfel. Kontrollera objektet.
#XMSG Object is having design time error
VAL_DESIGNTIME_ERROR=Objekt ''{0}'' returnerar ett designtidsfel. Kontrollera objektet.
#XMSG Procedure Object has been deleted
VAL_DELETED_PROCEDURE=Följande procedurer har raderats: {0}
#XMSG New Parameter added to Procedure object
VAL_NEW_PARAMETER_ADDED=Nya parametrar har lagts till i procedur "{1}": "{0}". Distribuera uppgiftskedjan på nytt.
#XMSG Parameter removed from Procedure object
VAL_PARAMETER_DELETED=Parametrar har tagits bort från procedur "{1}": "{0}". Distribuera uppgiftskedjan på nytt.
#XMSG Parameter data type changed in Procedure object
VAL_PARAMETER_DATATYPE_MODIFIED=Datatyp för parameter "{0}" har ändrats från "{1}" till "{2}" i procedur "{3}".
#XMSG Parameter length changed of specific datatype in Procedure object
VAL_PARAMETER_LENGTH_MODIFIED=Längd för parameter "{0}" har ändrats från "{1}" till "{2}" i procedur "{3}".
#XMSG Parameter precision changed of Decimal datatype in Procedure object
VAL_PARAMETER_PRECISION_MODIFIED=Precision för parameter "{0}" har ändrats från "{1}" till "{2}" i procedur "{3}".
#XMSG Parameter scale changed of Decimal datatype in Procedure object
VAL_PARAMETER_SCALE_MODIFIED=Skala för parameter "{0}" har ändrats från "{1}" till "{2}" i procedur "{3}".
#XMSG Parameter value not provided in Procedure object
VAL_SQL_SCRIPT_PROCEDURE_PARAMETER_VALUE_MISSING=Du har inte angett värden för indataparametrar som krävs för procedur "{0}": "{1}"
#XMSG Remote Table activity changes to Remove Replicated Data
VAL_REMOVE_REPLICATED_DATA=Nästa körning av en uppgiftskedja ändrar dataåtkomsttypen och data uppdateras inte längre i realtid.
#XMSG Remote Table activity changes
REPLICATION_TYPE_CHANGED=Objekt "{0}": Replikeringstyp ändras.

#XFLD
@lblStartNode=Startnod
#XFLD
@lblEndNode=Slutnod
#XFLD
@linkTo={0} till {1}
#XTOL
@txtViewDetails=Visa detaljer

#XTOL
txtOpenImpactLineage=Effekt- och ursprungsanalys
#XFLD
@emailNotifications=E-postaviseringar
#XFLD
@txtReset=Återställ
#XFLD
@emailMsgWarning=E-postmeddelande baserat på standardmall skickas om e-postmeddelandet är tomt
#XFLD
@notificationSettings=Aviseringsinställningar
#XFLD
@recipientEmailAddr=E-postadress för mottagare
#XFLD
@emailSubject=E-postämne
@emailSubjectText=Uppgiftskedja <TASKCHAIN_NAME> slutförd med status <STATUS>
#XFLD
@emailMessage=E-postmeddelande
@emailMessageText=Hej,\n\n Vi vill meddela att uppgiftskedja: <TASKCHAIN_NAME> som kördes kl. <START_TIME> har avslutats med status <STATUS>. Utförandet slutade kl. <END_TIME>.\n\nDetaljer:\nUtrymme:<SPACE_NAME>\nFel:<ERROR_MESSAGE>
@selectEmailRecipientDialogTitle=Välj e-postadress för mottagare
@tenantMembers=Tenantelement
@others=Övriga ({0})
@selectedEmailAddress=Valda mottagare
@add=Lägg till
@placeholder=Platshållare
@description=Beskrivning
@copyText=Kopiera text
@taskchainDetailsPlaceholder=Platshållare för uppgiftskedjedetaljer
@placeholderCopied=Platshållare har kopierats
@invalidEmailInfo=Ange korrekt e-postadress
@maxMembersAlreadyAdded=Du har redan lagt till maximala 20 element
@enterEmailAddress=Ange e-postadress för användare
@inCorrectPlaceHolder={0} är inte en förväntad platshållare i brödtext i e-post.
@nsOFF=Skicka inga aviseringar
@nsFAILED=Skicka endast e-postaviseringar när körningen har avslutats med fel
@nsCOMPLETED=Skicka endast e-postaviseringar när körningen har avslutats utan fel
@nsANY=Skicka e-post när körningen har slutförts
@phStatus=Status för uppgiftskedjan - OK|MISSLYCKADES
@phTaskChainTName=Tekniskt namn för uppgiftskedjan
@phTaskChainBName=Affärsnamn för uppgiftskedjan
@phLogId=Protokoll-ID för körning
@phUser=Användare som kör uppgiftskedjan
@phLogUILink=Länk till protokollvisning av uppgiftskedjan
@phStartTime=Starttid för körningen
@phEndTime=Sluttid för körningen
@phErrMsg=Första felmeddelandet i uppgiftsprotokollet. Protokollet är tomt om resultatet var OK.
@phSpaceName=Tekniskt namn för utrymmet
@emailFormatError=Ogiltigt e-postformat
@emailFormatErrorInListText=Ogiltigt e-postformat har angetts i listan över icke-tenantelement.
@emailSubjectTemplateText=Avisering för uppgiftskedja: $$taskChainName$$ - Utrymme: $$spaceId$$ - Status: $$status$$
@emailMessageTemplateText=Hej!\n\n Din uppgiftskedja märkt $$taskChainName$$ har slutförts med status $$status$$. \n Här är några fler detaljer om uppgiftskedjan:\n - Uppgiftskedjans tekniska namn: $$taskChainName$$ \n - Protokoll-ID för körningen av uppgiftskedjan: $$logId$$ \n - Användare som körde uppgiftskedjan: $$user$$ \n - Länk till protokollvisningen av uppgiftskedjan: $$uiLink$$ \n - Starttid för körningen av uppgiftskedjan: $$startTime$$ \n - Sluttid för körningen av uppgiftskedjan: $$endTime$$ \n - Namn på utrymmet: $$spaceId$$ \n
@deleteEmailRecepient=Radera mottagare
@emailInputDisabledText=Distribuera uppgiftskedja för att lägga till e-postmottagare.
@tenantOwnerDomainMatchErrorText=E-postdomän matchar inte tenantägardomän: {0}
@totalEmailIdLimitInfoText=Du kan välja upp till 20 e-postmottagare inklusive tenantelementanvändare och andra mottagare.
@emailDomainInfoText=Endast e-postadresser med domän: {0} accepteras.
@duplicateEmailErrorText=Duplicerade e-postmottagare finns i lista.

#XFLD Zorder Title
@txtZorderTitle=Apache Spark Z-ordningskolumner

#XFLD Zorder NoColumn
@txtZorderNoColumn=Inga Z-ordningskolumner hittades

#XFLD Zorder Column PrimaryKey
@txtZorderColumnPrimaryKey=Primärnyckel

#XFLD
@lblOperators=Operatorer
addNewSelector=Lägg till som ny uppgift
parallelSelector=Lägg till som parallell uppgift
replaceSelector=Ersätt befintlig uppgift
addparallelbranch=Lägg till som parallell gren
addplaceholder=Lägg till platshållare
addALLOperation=Operator ALLA
addOROperation=Operator VALFRI
addplaceholdertocanvas=Lägg till platshållare till arbetsyta
addplaceholderonselected=Lägg till platshållare efter vald uppgift
#XFLD: Add Parallel Branch to Selected Task
addparallelbranchonselected=Lägg till parallell gren efter vald uppgift
addOperator=Lägg till operator
txtAdd=Lägg till
txtPlaceHolderText=Dra och släpp en uppgift här
@lblLayout=Layout

#XMSG
VAL_UNCONNECTED_TASK=Uppgift "{0}" är inte ansluten till uppgiftskedja.
#XMSG
VAL_TASK_SHOULD_HAVE_ONE_INCOMING_LINK=Uppgift "{0}" ska endast ha en inkommande länk.
#XMSG
VAL_TASK_SHOULD_HAVE_INCOMING_LINK=Uppgift "{0}" ska ha en inkommande länk.
#XMSG
VAL_UNCONNECTED_OPERATOR=Operator "{0}" är inte ansluten till uppgiftskedja.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ATLEAST_TWO_INCOMING_LINK=Operator "{0}" ska ha minst två inkommande länkar.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ONE_OUT_LINK=Operator "{0}" ska ha minst en utgående länk.
#XMSG
VAL_CIRCULAR_REFERENCE_EXITS=Det finns en cirkulär loop för uppgiftskedja "{0}".
#XMSG
VAL_UNCONNECTED_BRANCH=Objekt/gren "{0}" är inte ansluten till uppgiftskedja.
#XMSG
VAL_TASK_TRIGGERED_PARALLEL_MORE_THAN_ONE_TIME=Uppgift "{0}" har mer än en parallell anslutning. Ta bort dubbletter för att fortsätta.


txtBegin=Starta
txtNodesInLink=Berörda objekt
#XTOL Tooltip for a context button on diagram
openInNewTab=Öppna i ny flik
#XTOL Tooltip for a context button on diagram
txtConnectToTaskOperator=Dra för att ansluta
@emailUpdateError=Fel vid uppdatering av e-postaviseringslistan

#XMSG
noTeamPrivilegeTxt=Behörighet saknas för att se en lista över tenantelement. Använd fliken Övriga för att lägga till e-postmottagare manuellt.

#XFLD Package
@txtPackage=Paket

#XMSG warning for repository package assignment changed. {1} - package name
VAL_PACKAGE_CHANGED=Du har allokerat objektet till paket "{1}". Klicka på Spara för att bekräfta och validera ändringen. Observera att allokering till ett paket inte kan ångras i den här editorn efter lagring.
#XMSG warning for repository package dependency issue.
MISSING_DEPENDENCY=Beroenden för objekt "{0}" kan inte åtgärdas i kontexten för paket "{1}".

#Warning messages for the folders in the "Others" tab of task chain editor
#XMSG generic warning if getting the objects in a particular folder fails with unhandled errors
getNonRepoArtefactTypeDescendantsError=Ett problem inträffade vid hämtning av objekten från mappen du valde.
#BW Process Chains folder related warning messages
#XMSG warning if user does not have the necessary permission related to the SAP BW Bridge space
missingPrivilegeForBridgeSpaceError=Du saknar behörigheten som krävs för att visa eller inkludera BW-processkedjor i en uppgiftskedja.
#XMSG warning if there was a problem retrieving the BW process chains due to bridge space membership, bridge connection issues or other errors
generalBridgeSpaceError=Ett problem inträffade vid hämtning av BW-processkedjor.
#XMSG warning if the BW API for retrieving the BW process chains from the SAP BW Bridge tenant fails
bwApiClientError=Ett problem inträffade vid hämtning av BW-processkedjor från SAP BW Bridge-tenanten.
#XMSG warning if BW process chains list is empty
bwApiEmptyListError=Inga BW-processkedjor hittades i SAP BW Bridge-tenanten.
#XMSG: Task log message for invalid OIDC configuration in the SAP BW Bridge tenant
bwBridgeOIDCConfigStatusInvalid=OpenID-autentisering har inte konfigurerats för den här tenanten. Använd klient-ID "{0}" och token-URL "{1}" för att konfigurera OpenID-autentisering i SAP BW Bridge-tenanten enligt beskrivningen i SAP-not 3536298.
#XMSG BW Process Chain Object has been deleted
VAL_DELETED_BW=Följande BW-processkedjor kan ha raderats från SAP Bridge-tenanten: {0}
#BW SQL Script Procedures folder related warning messages
#XFLD waring if the Procedure list is empty
procedureAPIEmptyList=Inga procedurer har skapats eller så har behörigheten EXECUTE inte beviljats till Open SQL-schemat.
#Change digram orientations
changeOrientations=Ändra orientering


# placeholder for the API Path
apiPath=Till exempel: /job/v1
# placeholder for the status API Path
statusAPIPath=Till exempel: /job/v1/{id}/status
# valueStateText for the API Path
apiPathRequired=API-sökväg krävs
#placeholder for the CSRF Token URL
csrfTokenURL=Endast HTTPS medges
# Response Type 1
statusCode=Hämta resultat från HTTP-statuskod
# Response Type 2
locationHeader=Hämta resultat från HTTP-statuskod och platshuvud
# Response Type 3
responseBody=Hämta resultat från HTTP-statuskod och text i svar
# placeholder for ID
idPlaceholder=Ange JSON-sökväg
# placeholder for indicator value
indicatorValue=Ange värde
# Placeholder for key
keyPlaceholder=Ange nyckel
# Error message for missing key
KeyRequired=Nyckel krävs
# Error message for invalid key format
invalidKeyFormat=Huvudnyckeln du har angett tillåts inte. Giltiga huvuden är:<ul><li>"prefer"</li><li>Huvuden som börjar på "x-", förutom "x-forwarded-host"</li><li>Huvuden som innehåller alfanumeriska tecken, "-", eller "_"</li></ul>
# Error message for missing value
valueRequired=Värde krävs
# Error message for invalid characters in value
invalidValueCharacters=Huvudet innehåller ogiltiga tecken. Specialtecken som tillåts är:\t ";", ":", "-", "_", ",", "?", "/" och "*"
# Validation message for invoke api path
apiPathValidation=Ange en giltig API-sökväg, till exempel: /job/v1
# Validation message for JSON path
jsonPathValidation=Ange en giltig JSON-sökväg
# Validation message for success/error indicator
indicatorValueValidation=Indikatorvärde måste börja med ett alfanumeriskt tecken och kan innehålla följande specialtecken:\t "-" och "_"
# Error message for JSON path
jsonPathRequired=JSON-sökväg krävs
# Error message for invalid API Technical Name
invalidTechnicalName=Tekniskt namn innehåller ogiltiga tecken
# Error message for empty Technical Name
emptyTechnicalName=Tekniskt namn krävs
# Tooltip for codeEditor dialog
codeEditorTooltip=Öppna JSON-redigeringsfönster
# Status Api path validation message
validationStatusAPIPath=Ange en giltig API-sökväg, till exempel: /job/v1/{id}/status
# CSRF token URL validation message
validationCSRFTokenURL=URL för CSRF-token måste börja på 'https://' och vara en giltig URL
# Select a connection
selectConnection=Välj en anslutning
# Validation message for connection item error
connectionNotReplicated=Anslutningen kan inte köra API-uppgifter just nu. Öppna appen "Anslutningar" och ange inloggningsuppgifterna på nytt för att upprätta HTTP-anslutningen
# Validation message for API task properties wrong value or missing
VAL_REST_API_PROPERTIES_MISSING=API-uppgift "{0}" har felaktiga värden eller saknar värden för följande egenskaper: {1}
# Validation message for API connection api is throwing some error or selected connection has some warning
VAL_REST_API_CONNECTION=API-uppgift "{0}" har ett problem med HTTP-anslutningen. Öppna appen "Anslutningar" och kontrollera anslutningen
# Validation message for API task properties when delected connection
VAL_REST_API_CONNECTION_DELETED=API-uppgift "{0}" har en raderad "{1}"-anslutning
# Validation message for API task properties when connection cannot be consumed in the API task
VAL_REST_API_CONNECTION_CONSUMPTION_WARNING=API-uppgift "{0}" har en anslutning "{1}" som inte kan användas för att köra API-uppgifter. Öppna appen "Anslutningar" och ange dina inloggningsuppgifter på nytt för att upprätta konnektivitet för API-uppgifter.
# Information message if synchrnous mode is selected in the API Invocation then we don't need status information
modeInfoText=I synkront läge visas inte statuspanelen för API-anrop
# validation dialog button for Run API Test
saveAnyway=Spara ändå
# validation message for technical name
technicalNameValidation=Tekniskt namn måste vara unikt inom uppgiftskedjan. Välj ett annat tekniskt namn
# Connection error message
getHttpConnectionsError=HTTP-anslutningar kunde inte hämtas
# Msg for the API test run when the Task chain is not saved
taskChainNotSaved=Uppgiftskedjan måste sparas innan du kör API-testkörningen
# Msg failed to run API test run
@failedToRunAPI=API-testkörning kunde inte köras
# Msg for the API test run when its already in running state
apiTaskRunning=En API-testkörning pågår redan. Vill du starta en ny testkörning?

topToBtm=Uppifrån och ner
leftToRight=Vänster till höger

#XTIT Apache Spark Application Settings
txtHeaderApacheSettings=Applikationsinställningar för Apache Spark
#XFLD Use Default
txtUseSpaceDefault=Använd standard
#XFLD Application
txtApplication=Applikation
#XFLD Define new settings for this Task
txtNewSettings=Definiera nya inställningar för uppgiften

#XFLD Use Default
txtUseDefault=Använd standard




