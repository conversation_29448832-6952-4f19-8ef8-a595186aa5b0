#XTOL Undo
@undo=Geri al
#XTOL Redo
@redo=Yinele
#XTOL Delete Selected Symbol
@deleteNode=Seçili sembolü sil
#XTOL Zoom to Fit
@zoomToFit=Sığacak kadar yakınlaştır
#XTOL Auto Layout
@autoLayout=Otomatik düzen
#XMSG
@welcomeText=Soldaki panelde yer alan nesneleri sürükleyip bu grafik alanına bırakın.
#XMSG
@txtNoData=Görünüşe göre henüz hiç nesne eklemediniz.
#XMSG
VAL_ENTER_VALID_STRING_GEN=Uzunluğu {0} veya daha az olan geçerli bir dize girin.
#XMSG
@noParametersMsg=Bu prosedür, girdi parametresi içermiyor.
#XMSG
@ip_enterValueMsg="{0}" (SQL komut dosyası prosedürünü çalıştır) girdi parametreleri içeriyor. <PERSON><PERSON><PERSON><PERSON>n her birinin değerini belirleyebilirsiniz.
#XTOL
@validateModel=Doğrulama iletileri
#XTOL
@hierarchy=Hiyerarşi
#XTOL
@columnCount=Sütun sayısı
#XFLD
@yes=Evet
#XFLD
@no=Hayır
#XTIT Save Dialog param
@modelNameTaskChain=Görev zinciri
#properties panel
@lblPropertyTitle=Özellikler
#XFLD
@lblGeneral=Genel
#XFLD : Setting
@lblSetting=Ayarlar
#XTXT: Delta Enabled Local Table Deletion
txtDeltaTableDeletion=Değişiklik türü 'Silindi' olan, tamamen işlenen, şu süreden daha eski tüm kayıtları silin:
#XTXT: Delta Enabled Local Table Deletion Input field description
desDeltaTableDeletion=Gün
#XFLD: Data Activation label
@lblDataActivation=Veri etkinleştirme
#XFLD: Latency label
@latency=Gecikme süresi
#XTEXT: Text for Latency dropdown
txtLatencyDefault=Varsayılan
#XTEXT: Text 1 hour
txtOneHour=1 saat
#XTEXT: Text 2 hours
txtTwoHours=2 saat
#XTEXT: Text 3 hours
txtThreeHours=3 saat
#XTEXT: Text 4 hours
txtFourHours=4 saat
#XTEXT: Text 6 hours
txtSixHours=6 saat
#XTEXT: Text 12 hours
txtTwelveHours=12 saat
#XTEXT: Text 1 day
txtOneDay=1 gün
#XFLD: Latency label
@autoRestartHead=Otomatik yeniden başlatma
#XFLD
@lblConnectionName=Bağlantı
#XFLD
@lblQualifiedName=Nitelikli ad
#XFLD
@lblSpaceName=Alan adı
#XFLD
@lblLocalSchemaName=Yerel şema
#XFLD
@lblType=Nesne türü
#XFLD
@lblActivity=Aktivite
#XFLD
@lblTableName=Tablo adı
#XFLD
@lblBusinessName=İş adı
#XFLD
@lblTechnicalName=Teknik ad
#XFLD
@lblSpace=Alan
#XFLD
@lblLabel=Etiket
#XFLD
@lblDataType=Veri türü
#XFLD
@lblDescription=Tanım
#XFLD
@lblStorageType=Depolama
#XFLD
@lblHTTPConnection=Genel HTTP bağlantısı
#XFLD
@lblAPISettings=Genel API ayarları
#XFLD
@header=Başlıklar
#XFLD
@lblInvoke=API çağrısı
#XFLD
@lblMethod=Yöntem
#XFLD
@lblUrl=Temel URL
#XFLD
@lblAPIPath=API yolu
#XFLD
@lblMode=Mod
#XFLD
@lblCSRFToken=CSRF belirteci gerektir
#XFLD
@lblTokenURL=CSRF belirteç URL'si
#XFLD
@csrfTokenInfoText=Girilmezse Temel URL ve API yolu kullanılır
#XFLD
@lblCSRF=CSRF belirteci
#XFLD
@lblRequestBody=Talep gövdesi
#XFLD
@lblFormat=Biçim
#XFLD
@lblResponse=Yanıt
#XFLD
@lblId=Durumu almak için tanıtıcı
#XFLD
@Id=Tanıtıcı
#XFLD
@lblSuccessIndicator=Başarı göstergesi
#XFLD
@lblErrorIndicator=Hata göstergesi
#XFLD
@lblErrorReason=Hata nedeni
#XFLD
@lblStatus=Durum
#XFLD
@lblApiTestRun=API test çalıştırması
#XFLD
@lblRunStatus=Çalıştırma durumu
#XFLD
@lblLastRan=Son çalıştırma zamanı
#XFLD
@lblTestRun=Test çalıştırması
#XFLD
@lblDefaultHeader=Varsayılan başlık alanları (anahtar-değer çiftleri)
#XFLD
@lblAdditionalHeader=Ek başlık alanı
#XFLD
@lblKey=Anahtar
#XFLD
@lblEditJSON=JSON'yi düzenle
#XFLD
@lblTasks=Görevler
#XFLD
@lblRESTfullTask=API
#XFLD: add field button text
btnAddField=API başlık alanı
#XFLD: view Details link
@viewDetails=Ayrıntıları görüntüle
#XTOL
tooltipTxt=Daha fazla
#XTOL
delete=Sil
#XBTN: ok button text
btnOk=Tamam
#XBTN: cancel button text
btnCancel=İptal
#XBTN: save button text
btnSave=Kaydet
#XMSG Error: Object has duplicated technical name.
VAL_OBJECT_DUPLICATE_NAME=Nesne "{0}" havuzda zaten var. Başka ad girin.
#XMSG: loading message while opening task chain
loadTaskChain=Görev zinciri yükleniyor...
#model properties
#XFLD
@status_panel=Çalıştırma durumu
#XFLD
@deploy_status_panel=Dağıtım durumu
#XFLD
@status_lbl=Durum
#XFLD
@lblLastExecuted=Son çalıştırma
#XFLD
@lblNotExecuted=Henüz yürütülmedi
#XFLD
@lblNotDeployed=Dağıtılmadı
#XFLD
errorDetailsTxt=Çalıştırma durumu getirilemedi
#XBTN: Schedule dropdown menu
SCHEDULE=Planla
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Planlamayı düzenle
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Planlamayı sil
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Planlama oluştur
#XLNK
viewDetails=Ayrıntıları görüntüle
#XMSG: error message for reading execution status from backend
backendErrorMsg=Veriler şu anda sunucudan yüklenmiyor gibi görünüyor. Verileri tekrar getirmeyi deneyin.
#XFLD: Status text for Completed
@statusCompleted=Tamamlandı
#XFLD: Status text for Running
@statusRunning=Çalışıyor
#XFLD: Status text for Failed
@statusFailed=Başarısız oldu
#XFLD: Status text for Stopped
@statusStopped=Durduruldu
#XFLD: Status text for Stopping
@statusStopping=Durduruluyor
#XFLD
@LoaderTitle=Yükleniyor
#XFLD
#XFLD: Deploy status, dataflow has  been deployed / deployment date exists
@deployStatusActive=Dağıtıldı
@deployStatusRevised=Yerel güncellemeler
@deployStatusFailed=Başarısız oldu
@deployStatusPending=Dağıtılıyor...
@LoaderText=Sunucudan ayrıntılar alınıyor
#XMSG
@msgDetailFetchError=Sunucudan ayrıntılar alınırken hata oluştu
#XFLD
@executeError=Hata
#XFLD
@executeWarning=Uyarı
#XMSG
@executeConfirmDialog=Bilgi
#XMSG
@executeunsavederror=Görev zincirinizi çalıştırmadan önce kaydedin.
#XMSG
@executemodifiederror=Görev zincirinde kaydedilmemiş değişiklikler var. Görev zincirini kaydedin.
#XMSG
@executerunningerror=Görev zinciri şu anda çalıştırılıyor. Yeni bir görev zinciri başlatabilmek için mevcut çalıştırmanın tamamlanmasını bekleyin.
#XMSG
@btnExecuteAnyway=Yine de çalıştır
#XMSG
@msgExecuteWithValidations=Görev zincirinde doğrulama hataları var. Görev zincirinin çalıştırılması başarısızlıkla sonuçlanabilir.
#XMSG
@msgRunDeployedVersion=Dağıtılacak değişiklikler mevcut. Görev zincirinin son dağıtılan versiyonu çalıştırılacak. Devam etmek istiyor musunuz?
#XMSG
#XMSG
@navToMonitoring=Görev zinciri izleme'de aç
#XMSG
txtOR=OR
#XFLD
@preview=Önizle
#XMSG
txtand=AND
#NOTR
@dateFormat=yyyy-MM-dd
#NOTR
@dateTimeFormatFilter=yyyy-MM-dd HH:mm:ss.
#NOTR
@timeFormat=HH:mm:ss
@lblColum=Sütun
#XFLD
@lblCondition=Koşul
#XFLD
@lblValue=Değer
#XMSG
@msgJsonInvalid=JSON dosyasında hatalar bulunduğundan görev zinciri kaydedilemedi. Kontrol edip düzeltin.
#XTIT
@msgSaveFailTitle=Geçersiz JSON.
#XMSG
NOT_CHAINABLE=''{0}'' nesnesi, görev zincirine eklenemiyor.
#XMSG
NOT_CHAINABLE_REMOTETABLE=Nesne "{0}": Çoğaltma türü değiştirilecek.
#XMSG
searchTaskChain=Nesne ara

#XFLD
@txtTaskChain=Görev zinciri
#XFLD
@txtRemoteTable=Uzak tablo
#XFLD
@txtRemoveData=Çoğaltılan verileri kaldır
#XFLD
@txtRemovePersist=Kalıcı verileri kaldır
#XFLD
@txtView=Görüntüle
#XFLD
@txtDataFlow=Veri akışı
#XFLD
@txtIL=Akıllı arama
#XFLD
@txtTransformationFlow=Dönüştürme akışı
#XFLD
@txtReplicationFlow=Çoğaltma akışı
#XFLD
@txtDeltaLocalTable=Yerel tablo
#XFLD
@txtBWProcessChain=BW süreç zinciri
#XFLD
@txtSQLScriptProcedure=SQL komut dosyası prosedürü
#XFLD
@txtAPI=API
#XFLD
@txtMerge=Birleştir
#XFLD
@txtOptimize=Optimize et
#XFLD
@txtVacuum=Kayıtları sil

#XFLD
@txtRun=Çalıştır
#XFLD
@txtPersist=Kalıcı hale getir
#XFLD
@txtReplicate=Çoğalt
#XFLD
@txtDelete=Değişiklik türü 'Silindi' olan kayıtları sil
#XFLD
@txtRunTC=Görev zincirini çalıştır
#XFLD
@txtRunBW=BW süreç zincirini çalıştır
#XFLD
@txtRunSQLScriptProcedure=SQL komut dosyası prosedürünü çalıştır

#XFLD
@txtRunDataFlow=Veri akışını çalıştır
#XFLD
@txtPersistView=Görünümü kalıcı hale getir
#XFLD
@txtReplicateTable=Tabloyu kalıcı hale getir
#XFLD
@txtRunIL=Akıllı aramayı çalıştır
#XFLD
@txtRunTF=Dönüştürme akışını çalıştır
#XFLD
@txtRunRF=Çoğaltma akışını çalıştır
#XFLD
@txtRemoveReplicatedData=Çoğaltılan verileri kaldır
#XFLD
@txtRemovePersistedData=Kalıcı verileri kaldır
#XFLD
@txtMergeData=Birleştir
#XFLD
@txtOptimizeData=Optimize et
#XFLD
@txtVacuumData=Kayıtları sil
#XFLD
@txtRunAPI=API'yi çalıştır

#XFLD storage type text
hdlfStorage=Dosya

@statusNew=Dağıtılmadı
@statusActive=Dağıtıldı
@statusRevised=Yerel güncellemeler
@statusPending=Dağıtılıyor...
@statusChangesToDeploy=Dağıtılacak değişiklikler
@statusDesignTimeError=Tasarım zamanı hatası
@statusRunTimeError=Çalıştırma saati hatası

#XTIT
txtNodes=Görev zincirindeki nesneler ({0})
#XBTN
@deleteNodes=Sil

#XMSG
@txtDropDataToDiagram=Nesneleri sürükleyip diyagrama bırakın
#XMSG
@noData=Nesne yok

#XFLD
@txtTaskPosition=Görev konumu

#input parameters and variables
#XFLD
lblInputParameters=Girdi parametreleri
#XFLD
ip_name=Ad
#XFLD
ip_value=Değer
#XTEXT
@noObjectsFound=Nesne bulunamadı

#XMSG
@msgExecuteSuccess=Görev zinciri çalıştırması başlatıldı.
#XMSG
@msgExecuteFail=Görev zinciri çalıştırılamadı.
#XMSG
@msgDeployAndRunSuccess=Görev zinciri dağıtımı ve çalıştırması başlatıldı.
#XMSG
@msgDeployAndRunFail=Görev zinciri dağıtılamadı ve çalıştırılamadı.
#XMSG
@titleExecuteBusy=Bekleyin.
#XMSG
@msgExecuteBusy=Görev zincirini çalıştırmaya başlamak için verilerinizi hazırlıyoruz.
#XMSG
@msgAPITestRunSuccess=API test çalıştırması başlatıldı.
#XMSG
@msgAPIExecuteBusy=API test çalıştırmasını çalıştırmaya başlamak için verilerinizi hazırlıyoruz.

#XTOL
txtOpenInEditor=Düzenleyicide aç
#XTOL
txtPreviewData=Verileri önizle

#datapreview
#XMSG
@msgDataPreviewNotSupp=Veri önizleme bu nesne için kullanılamıyor.

#XMSG Error: empty model
VAL_MODEL_EMPTY=Modeliniz boş görünüyor. Birkaç nesne ekleyin.
#XMSG Error: deploy model
@msgDeployBeforeRun=Görev zincirini çalıştırabilmek için dağıtmanız gerekir.
#BTN: close dialog
btnClose=Kapat

#XMSG Object is not Deployed
VAL_NOT_DEPLOYED=Görev zincirinin sürdürülebilmesi için ''{0}'' nesnesi dağıtılmalıdır.
#XMSG Object is having runtime error
VAL_RUNTIME_ERROR=''{0}'' nesnesi, çalıştırma süresi hatası döndürüyor. Nesneyi kontrol edin.
#XMSG Object is having design time error
VAL_DESIGNTIME_ERROR=''{0}'' nesnesi, tasarım süresi hatası döndürüyor. Nesneyi kontrol edin.
#XMSG Procedure Object has been deleted
VAL_DELETED_PROCEDURE=Şu prosedürler silindi: {0}
#XMSG New Parameter added to Procedure object
VAL_NEW_PARAMETER_ADDED="{1}" prosedürüne yeni parametreler eklendi: "{0}". Görev zincirini yeniden dağıtın.
#XMSG Parameter removed from Procedure object
VAL_PARAMETER_DELETED="{1}" prosedüründen parametreler kaldırıldı: "{0}". Görev zincirini yeniden dağıtın.
#XMSG Parameter data type changed in Procedure object
VAL_PARAMETER_DATATYPE_MODIFIED="{0}" parametresi veri türü, "{3}" prosedüründe "{1}" iken "{2}" olarak değiştirildi.
#XMSG Parameter length changed of specific datatype in Procedure object
VAL_PARAMETER_LENGTH_MODIFIED="{0}" parametresi uzunluğu, "{3}" prosedüründe "{1}" iken "{2}" olarak değiştirildi.
#XMSG Parameter precision changed of Decimal datatype in Procedure object
VAL_PARAMETER_PRECISION_MODIFIED="{0}" parametresi kesinliği, "{3}" prosedüründe "{1}" iken "{2}" olarak değiştirildi.
#XMSG Parameter scale changed of Decimal datatype in Procedure object
VAL_PARAMETER_SCALE_MODIFIED="{0}" parametresi ölçeği, "{3}" prosedüründe "{1}" iken "{2}" olarak değiştirildi.
#XMSG Parameter value not provided in Procedure object
VAL_SQL_SCRIPT_PROCEDURE_PARAMETER_VALUE_MISSING="{0}" prosedürü için gerekli giriş parametrelerine ilişkin değer girmediniz: "{1}"
#XMSG Remote Table activity changes to Remove Replicated Data
VAL_REMOVE_REPLICATED_DATA=Bir görev zincirinin bir sonraki çalıştırması veri erişimi türünü değiştirir ve veriler artık gerçek zamanlı olarak yüklenmez.
#XMSG Remote Table activity changes
REPLICATION_TYPE_CHANGED=Nesne "{0}": Çoğaltma türü değiştirilecek.

#XFLD
@lblStartNode=Düğümü başlat
#XFLD
@lblEndNode=Düğümü bitir
#XFLD
@linkTo={0} - {1}
#XTOL
@txtViewDetails=Ayrıntıları görüntüle

#XTOL
txtOpenImpactLineage=Etki ve kaynak analizi
#XFLD
@emailNotifications=E-posta bildirimleri
#XFLD
@txtReset=Sıfırla
#XFLD
@emailMsgWarning=E-posta iletisi boş olduğunda varsayılan şablon e-postası gönderilir
#XFLD
@notificationSettings=Bildirim ayarları
#XFLD
@recipientEmailAddr=Alıcı e-posta adresi
#XFLD
@emailSubject=E-posta konusu
@emailSubjectText=<TASKCHAIN_NAME> adlı görev zinciri <STATUS> durumuyla tamamlandı
#XFLD
@emailMessage=E-posta iletisi
@emailMessageText=Sayın kullanıcı,\n\n <START_TIME> itibarıyla çalıştırılan <TASKCHAIN_NAME> adlı görev zincirinin <STATUS> durumuyla tamamlandığını bilginize sunarız. Yürütme <END_TIME> saatinde sona erdi.\n\nAyrıntılar:\nAlan:<SPACE_NAME>\nHata:<ERROR_MESSAGE>
@selectEmailRecipientDialogTitle=Alıcı e-posta adresi seç
@tenantMembers=Kiracı üyeleri
@others=Diğer ({0})
@selectedEmailAddress=Seçilen alıcılar
@add=Ekle
@placeholder=Yer tutucu
@description=Tanım
@copyText=Metni kopyala
@taskchainDetailsPlaceholder=Görev zinciri ayrıntıları için yer tutucular
@placeholderCopied=Yer tutucu kopyalandı
@invalidEmailInfo=Doğru e-posta adresini girin
@maxMembersAlreadyAdded=Ekleyebileceğiniz 20 üyelik üst sınıra zaten ulaştınız
@enterEmailAddress=Kullanıcı e-posta adresini girin
@inCorrectPlaceHolder={0}, e-posta gövdesinde beklenen bir yer tutucu değil.
@nsOFF=Bildirim gönderme
@nsFAILED=Yalnızca çalıştırma bir hatayla tamamlandığında e-posta bildirimi gönder
@nsCOMPLETED=Yalnızca çalıştırma başarıyla tamamlandığında e-posta bildirimi gönder
@nsANY=Çalıştırma tamamlandığında e-posta gönder
@phStatus=Görev zincirinin durumu - BAŞARILI|BAŞARISIZ OLDU
@phTaskChainTName=Görev zincirinin teknik adı
@phTaskChainBName=Görev zincirinin iş adı
@phLogId=Çalıştırmanın günlük tanıtıcısı
@phUser=Görev zincirini çalıştıran kullanıcı
@phLogUILink=Görev zincirine ilişkin günlük görüntüsünün bağlantısı
@phStartTime=Çalıştırmanın başlangıç saati
@phEndTime=Çalıştırmanın bitiş saati
@phErrMsg=Görev günlüğündeki ilk hata iletisi. BAŞARI durumunda günlük boş olur
@phSpaceName=Alanın teknik adı
@emailFormatError=Geçersiz e-posa biçimi
@emailFormatErrorInListText=Kiracı üyesi olmayan üyeler listesine geçersiz e-posta biçimi girildi.
@emailSubjectTemplateText=Görev zinciri bildirimi: $$taskChainName$$ - alan: $$spaceId$$ - durum: $$status$$
@emailMessageTemplateText=Merhaba,\n\n $$taskChainName$$ etiketli görev zinciriniz $$status$$ durumuyla tamamlandı. \n Görev zinciri hakkındaki bazı diğer bilgiler:\n - Görev zinciri teknik adı: $$taskChainName$$ \n - Görev zinciri çalıştırmasının günlük tanıtıcısı: $$logId$$ \n - Görev zincirini çalıştıran kullanıcı: $$user$$ \n - Görev zinciri günlük görüntüsünün bağlantısı: $$uiLink$$ \n - Görev zinciri çalıştırmasının başlangıç zamanı: $$startTime$$ \n - Görev zinciri çalıştırmasının bitiş zamanı: $$endTime$$ \n - Alanın adı: $$spaceId$$ \n
@deleteEmailRecepient=Alıcıyı sil
@emailInputDisabledText=E-posta alıcıları eklemek için görev zincirini dağıtın.
@tenantOwnerDomainMatchErrorText=E-posta adresi etki alanı, kiracı sorumlusunun etki alanıyla eşleşmiyor: {0}
@totalEmailIdLimitInfoText=Kiracı üyesi olan kullanıcılar ve diğer alıcılar dahil olmak üzere en fazla 20 e-posta alıcısı seçebilirsiniz.
@emailDomainInfoText=Yalnızca {0} etki alanına sahip olan e-posta adresleri kabul edilir.
@duplicateEmailErrorText=Listede çift e-posta alıcıları var.

#XFLD Zorder Title
@txtZorderTitle=Apache Spark Z-sırası sütunları

#XFLD Zorder NoColumn
@txtZorderNoColumn=Z-sırası sütunu bulunamadı

#XFLD Zorder Column PrimaryKey
@txtZorderColumnPrimaryKey=Birincil anahtar

#XFLD
@lblOperators=İşleçler
addNewSelector=Yeni görev olarak ekle
parallelSelector=Paralel görev olarak ekle
replaceSelector=Mevcut görevi değiştir
addparallelbranch=Paralel dal olarak ekle
addplaceholder=Yer tutucu ekle
addALLOperation=ALL işleci
addOROperation=ANY işleci
addplaceholdertocanvas=Grafik alanına yer tutucu ekle
addplaceholderonselected=Seçilen görevin sonrasına yer tutucu ekle
#XFLD: Add Parallel Branch to Selected Task
addparallelbranchonselected=Seçilen görevin sonrasına paralel dal ekle
addOperator=İşleç ekle
txtAdd=Ekle
txtPlaceHolderText=Buraya görev sürükleyip bırakın
@lblLayout=Düzen

#XMSG
VAL_UNCONNECTED_TASK=Görev ''{0}'', görev zincirine bağlı değil.
#XMSG
VAL_TASK_SHOULD_HAVE_ONE_INCOMING_LINK=Görev ''{0}'' yalnızca tek bir gelen bağlantı içermelidir.
#XMSG
VAL_TASK_SHOULD_HAVE_INCOMING_LINK=Görev ''{0}'' tek bir gelen bağlantı içermelidir.
#XMSG
VAL_UNCONNECTED_OPERATOR=İşleç ''{0}'', görev zincirine bağlı değil.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ATLEAST_TWO_INCOMING_LINK=İşleç ''{0}'' en az iki gelen bağlantı içermelidir.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ONE_OUT_LINK=İşleç ''{0}'' en az bir giden bağlantı içermelidir.
#XMSG
VAL_CIRCULAR_REFERENCE_EXITS=Görev zinciri ''{0}'' dairesel döngü içeriyor.
#XMSG
VAL_UNCONNECTED_BRANCH=Nesne/dal ''{0}'', görev zincirine bağlı değil.
#XMSG
VAL_TASK_TRIGGERED_PARALLEL_MORE_THAN_ONE_TIME=Görev ''{0}'' paralel olarak birden fazla kez bağlı. Devam etmek için yinelemeleri kaldırın..


txtBegin=Başlat
txtNodesInLink=Dahil edilen nesneler
#XTOL Tooltip for a context button on diagram
openInNewTab=Yeni sekmede aç
#XTOL Tooltip for a context button on diagram
txtConnectToTaskOperator=Bağlamak için sürükleyin
@emailUpdateError=E-posta bildirimi listesi güncellenirken hata oluştu

#XMSG
noTeamPrivilegeTxt=Kiracı üyelerinin listesini görme izniniz yok. E-posta alıcılarını manüel olarak eklemek için Diğer sekmesini kullanın.

#XFLD Package
@txtPackage=Paket

#XMSG warning for repository package assignment changed. {1} - package name
VAL_PACKAGE_CHANGED=Bu nesneyi ''{1}'' paketine tayin ettiniz. Bu değişikliği teyit edip doğrulamak için Kaydet''e tıklayın. Paket tayininin, kaydetmeniz sonrasında bu düzenleyicide geri alınamayacağını unutmayın.
#XMSG warning for repository package dependency issue.
MISSING_DEPENDENCY=''{0}'' nesnesinin bağlılıkları ''{1}'' paketi bağlamında çözümlenemiyor.

#Warning messages for the folders in the "Others" tab of task chain editor
#XMSG generic warning if getting the objects in a particular folder fails with unhandled errors
getNonRepoArtefactTypeDescendantsError=Seçtiğiniz klasördeki nesneler getirilirken sorun oluştu.
#BW Process Chains folder related warning messages
#XMSG warning if user does not have the necessary permission related to the SAP BW Bridge space
missingPrivilegeForBridgeSpaceError=Bir görev zincirindeki BW süreç zincirlerini görüntülemek veya dahil etmek için gerekli izne sahip değilsiniz.
#XMSG warning if there was a problem retrieving the BW process chains due to bridge space membership, bridge connection issues or other errors
generalBridgeSpaceError=BW süreç zincirleri getirilirken sorun oluştu.
#XMSG warning if the BW API for retrieving the BW process chains from the SAP BW Bridge tenant fails
bwApiClientError=SAP BW köprüsü kiracısından BW süreç zincirleri getirilirken sorun oluştu.
#XMSG warning if BW process chains list is empty
bwApiEmptyListError=SAP BW köprüsü kiracısında hiç BW süreç zinciri bulunamadı.
#XMSG: Task log message for invalid OIDC configuration in the SAP BW Bridge tenant
bwBridgeOIDCConfigStatusInvalid=OpenID kimlik doğrulaması, bu kiracı için konfigüre edilmemiş. 3536298 numaralı SAP notunda açıklandığı şekilde SAP BW köprüsü kiracısında OpenID kimlik doğrulamasını konfigüre etmek için clientID "{0}" ve tokenURL "{1}" kullanın.
#XMSG BW Process Chain Object has been deleted
VAL_DELETED_BW=Şu BW süreç zincirleri muhtemelen SAP köprüsü kiracısından silindi: {0}
#BW SQL Script Procedures folder related warning messages
#XFLD waring if the Procedure list is empty
procedureAPIEmptyList=Hiçbir prosedür oluşturulmadı veya Open SQL şemasına YÜRÜT ayrıcalığı verilmedi.
#Change digram orientations
changeOrientations=Yönü değiştir


# placeholder for the API Path
apiPath=Örnek: /job/v1
# placeholder for the status API Path
statusAPIPath=Örnek: /job/v1/{id}/status
# valueStateText for the API Path
apiPathRequired=API yolu gerekli
#placeholder for the CSRF Token URL
csrfTokenURL=Yalnızca HTTPS desteklenir
# Response Type 1
statusCode=HTTP durum kodundan sonucu al
# Response Type 2
locationHeader=HTTP durum kodundan ve konum başlığından sonucu al
# Response Type 3
responseBody=HTTP durum kodundan ve yanıt gövdesinden sonucu al
# placeholder for ID
idPlaceholder=JSON yolu girin
# placeholder for indicator value
indicatorValue=Değer girin
# Placeholder for key
keyPlaceholder=Anahtar girin
# Error message for missing key
KeyRequired=Anahtar gerekli
# Error message for invalid key format
invalidKeyFormat=Girdiğiniz başlık anahtarına izin verilmiyor. Geçerli başlıklar:<ul><li>"prefer"</li><li>"x-forwarded-host" hariç olmak üzere "x-" ile başlayan başlıklar </li><li>Alfasayısal karakterler, "-" veya "_" içeren başlıklar</li></ul>
# Error message for missing value
valueRequired=Değer gerekli
# Error message for invalid characters in value
invalidValueCharacters=Başlık geçersiz karakterler içeriyor. İzin verilen özel karakterler:\t ";", ":", "-", "_", ",", "?", "/" ve "*"
# Validation message for invoke api path
apiPathValidation=Geçerli bir API yolu girin. Örnek: /job/v1
# Validation message for JSON path
jsonPathValidation=Geçerli bir JSON yolu girin
# Validation message for success/error indicator
indicatorValueValidation=Gösterge değeri alfasayısal bir karakterle başlamalıdır ve şu özel karakterleri içerebilir:\t "-" ve "_"
# Error message for JSON path
jsonPathRequired=JSON yolu gerekli
# Error message for invalid API Technical Name
invalidTechnicalName=Teknik ad geçersiz karakterler içeriyor
# Error message for empty Technical Name
emptyTechnicalName=Teknik ad gerekli
# Tooltip for codeEditor dialog
codeEditorTooltip=JSON düzenleme penceresini açın
# Status Api path validation message
validationStatusAPIPath=Geçerli bir API yolu girin. Örnek: /job/v1/{id}/status
# CSRF token URL validation message
validationCSRFTokenURL=CSRF belirteci URL'si 'https://' ile başlamalı ve geçerli bir URL olmalı
# Select a connection
selectConnection=Bağlantı seç
# Validation message for connection item error
connectionNotReplicated=Bağlantı şu anda API görevleri çalıştırmak için geçersiz. HTTP bağlantısını düzeltmek için "Bağlantılar" uygulamasını açın ve kimlik bilgilerinizi yeniden girin
# Validation message for API task properties wrong value or missing
VAL_REST_API_PROPERTIES_MISSING="{0}" API görevinde şu özellikler için yanlış veya eksik değerler bulunuyor: {1}
# Validation message for API connection api is throwing some error or selected connection has some warning
VAL_REST_API_CONNECTION="{0}" API görevinde HTTP bağlantısı sorunu var. "Bağlantılar" uygulamasını açın ve bağlantıyı kontrol edin
# Validation message for API task properties when delected connection
VAL_REST_API_CONNECTION_DELETED="{0}" API göreviyle bir "{1}" bağlantısı silindi
# Validation message for API task properties when connection cannot be consumed in the API task
VAL_REST_API_CONNECTION_CONSUMPTION_WARNING="{0}" API görevi, API görevlerinin çalıştırılması için kullanılamayan bir "{1}" bağlantısı içeriyor. API görevleri için bağlantı kurmak amacıyla "Bağlantılar" uygulamasını açın ve kimlik bilgilerinizi yeniden girin 
# Information message if synchrnous mode is selected in the API Invocation then we don't need status information
modeInfoText=Senkron modda API çağrıları için durum paneli gösterilmez
# validation dialog button for Run API Test
saveAnyway=Yine de kaydet
# validation message for technical name
technicalNameValidation=Teknik ad, görev zincirinde benzersiz olmalıdır. Başka bir teknik ad seçin 
# Connection error message
getHttpConnectionsError=HTTP bağlantıları alınamadı
# Msg for the API test run when the Task chain is not saved
taskChainNotSaved=API test çalıştırması çalıştırılmadan önce görev zinciri kaydedilmelidir
# Msg failed to run API test run
@failedToRunAPI=API test çalıştırması çalıştırılamadı
# Msg for the API test run when its already in running state
apiTaskRunning=API test çalıştırması zaten devam ediyor. Yeni bir test çalıştırması başlatmak istiyor musunuz?

topToBtm=Yukarıdan aşağıya
leftToRight=Soldan sağa

#XTIT Apache Spark Application Settings
txtHeaderApacheSettings=Apache Spark uygulama ayarları
#XFLD Use Default
txtUseSpaceDefault=Varsayılanı kullan
#XFLD Application
txtApplication=Uygulama
#XFLD Define new settings for this Task
txtNewSettings=Bu görev için yeni ayarlar tanımla

#XFLD Use Default
txtUseDefault=Varsayılanı kullan




