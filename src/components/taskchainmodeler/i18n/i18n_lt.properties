#XTOL Undo
@undo=Anuliuoti
#XTOL Redo
@redo=Perdaryti
#XTOL Delete Selected Symbol
@deleteNode=Naikinti pasirinktą simbolį
#XTOL Zoom to Fit
@zoomToFit=Keisti mastelį, kad tiktų
#XTOL Auto Layout
@autoLayout=Automatinis išdėstymas
#XMSG
@welcomeText=Nutempkite objektus ant šios drobės iš kairiojo skydo.
#XMSG
@txtNoData=Regis, dar nepridėjote jokių objektų.
#XMSG
VAL_ENTER_VALID_STRING_GEN=Įveskite leistiną eilutę, trumpesnę arba lygią {0}.
#XMSG
@noParametersMsg=Ši procedūra neturi įvesties parametrų.
#XMSG
@ip_enterValueMsg=„{0}“ (SQL scenarijaus procedūros vykdymas) turi įvesties parametrų. Galite nustatyti kiekvieno iš jų vertę.
#XTOL
@validateModel=Tikrinimo pranešimai
#XTOL
@hierarchy=Hierarchija
#XTOL
@columnCount=Stulpelių skaičius
#XFLD
@yes=Taip
#XFLD
@no=Ne
#XTIT Save Dialog param
@modelNameTaskChain=Užduočių grandinė
#properties panel
@lblPropertyTitle=Ypatybės
#XFLD
@lblGeneral=Bendra
#XFLD : Setting
@lblSetting=Parametrai
#XTXT: Delta Enabled Local Table Deletion
txtDeltaTableDeletion=Naikinti visus visiškai apdorotus įrašus su pakeitimo tipu „Panaikinta“, kurie yra senesni nei
#XTXT: Delta Enabled Local Table Deletion Input field description
desDeltaTableDeletion=Dienos
#XFLD: Data Activation label
@lblDataActivation=Duomenų aktyvinimas
#XFLD: Latency label
@latency=Laukimo laikas
#XTEXT: Text for Latency dropdown
txtLatencyDefault=Numatytasis
#XTEXT: Text 1 hour
txtOneHour=1 valanda
#XTEXT: Text 2 hours
txtTwoHours=2 valandos
#XTEXT: Text 3 hours
txtThreeHours=3 valandų
#XTEXT: Text 4 hours
txtFourHours=4 valandos
#XTEXT: Text 6 hours
txtSixHours=6 valandų
#XTEXT: Text 12 hours
txtTwelveHours=12 valandos
#XTEXT: Text 1 day
txtOneDay=1 diena
#XFLD: Latency label
@autoRestartHead=Automatinis paleidimas iš naujo
#XFLD
@lblConnectionName=Ryšys
#XFLD
@lblQualifiedName=Patvirtintas pavadinimas
#XFLD
@lblSpaceName=Vietos pavadinimas
#XFLD
@lblLocalSchemaName=Vietinė schema
#XFLD
@lblType=Objekto tipas
#XFLD
@lblActivity=Veikla
#XFLD
@lblTableName=Lentelės pavadinimas
#XFLD
@lblBusinessName=Verslo pavadinimas
#XFLD
@lblTechnicalName=Techninis pavadinimas
#XFLD
@lblSpace=Sritis
#XFLD
@lblLabel=Etiketė
#XFLD
@lblDataType=Duomenų tipas
#XFLD
@lblDescription=Aprašas
#XFLD
@lblStorageType=Saugykla
#XFLD
@lblHTTPConnection=Bendrasis HTTP ryšys
#XFLD
@lblAPISettings=Bendrieji API parametrai
#XFLD
@header=Antraštės
#XFLD
@lblInvoke=API iškvietimas
#XFLD
@lblMethod=Metodas
#XFLD
@lblUrl=Pagrindinis URL
#XFLD
@lblAPIPath=API kelias
#XFLD
@lblMode=Režimas
#XFLD
@lblCSRFToken=Reikalauti CSRF atpažinimo ženklo
#XFLD
@lblTokenURL=CSRF atpažinimo ženklo URL
#XFLD
@csrfTokenInfoText=Neįvedus, bus naudojamas pagrindinis URL ir API kelias
#XFLD
@lblCSRF=CSRF atpažinimo ženklas
#XFLD
@lblRequestBody=Užklausos tekstas
#XFLD
@lblFormat=Formatas
#XFLD
@lblResponse=Atsakymas
#XFLD
@lblId=ID, skirtas būsenai gauti
#XFLD
@Id=ID
#XFLD
@lblSuccessIndicator=Sėkmės indikatorius
#XFLD
@lblErrorIndicator=Klaidos indikatorius
#XFLD
@lblErrorReason=Klaidos priežastis
#XFLD
@lblStatus=Būsena
#XFLD
@lblApiTestRun=API bandomasis vykdymas
#XFLD
@lblRunStatus=Vykdymo būsena
#XFLD
@lblLastRan=Paskutinio vykdymo data
#XFLD
@lblTestRun=Bandomasis vykdymas
#XFLD
@lblDefaultHeader=Numatytieji antraštės laukai (raktų ir reikšmių poros)
#XFLD
@lblAdditionalHeader=Papildomas antraštės laukas
#XFLD
@lblKey=Raktas
#XFLD
@lblEditJSON=Redaguoti JSON
#XFLD
@lblTasks=Užduotys
#XFLD
@lblRESTfullTask=API
#XFLD: add field button text
btnAddField=Pridėti antraštės lauką
#XFLD: view Details link
@viewDetails=Peržiūrėti išsamią informaciją
#XTOL
tooltipTxt=Daugiau
#XTOL
delete=Naikinti
#XBTN: ok button text
btnOk=Gerai
#XBTN: cancel button text
btnCancel=Atšaukti
#XBTN: save button text
btnSave=Įrašyti
#XMSG Error: Object has duplicated technical name.
VAL_OBJECT_DUPLICATE_NAME=Objektas „{0}“ jau yra saugykloje. Įveskite kitą pavadinimą.
#XMSG: loading message while opening task chain
loadTaskChain=Įkeliama užduočių grandinė...
#model properties
#XFLD
@status_panel=Vykdymo būsena
#XFLD
@deploy_status_panel=Diegimo būsena
#XFLD
@status_lbl=Būsena
#XFLD
@lblLastExecuted=Paskutinis vykdymas
#XFLD
@lblNotExecuted=Neįvykdyta
#XFLD
@lblNotDeployed=Neįdiegta
#XFLD
errorDetailsTxt=Nepavyko gauti vykdymo būsenos
#XBTN: Schedule dropdown menu
SCHEDULE=Tvarkaraštis
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Redaguoti tvarkaraštį
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Naikinti tvarkaraštį
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Kurti tvarkaraštį
#XLNK
viewDetails=Peržiūrėti išsamią informaciją
#XMSG: error message for reading execution status from backend
backendErrorMsg=Regis, šiuo metu duomenys nėra įkeliami iš serverio. Pabandykite dar kartą iškviesti duomenis.
#XFLD: Status text for Completed
@statusCompleted=Užbaigta
#XFLD: Status text for Running
@statusRunning=Vykdoma
#XFLD: Status text for Failed
@statusFailed=Nepavyko
#XFLD: Status text for Stopped
@statusStopped=Sustabdyta
#XFLD: Status text for Stopping
@statusStopping=Stabdoma
#XFLD
@LoaderTitle=Įkeliama
#XFLD
#XFLD: Deploy status, dataflow has  been deployed / deployment date exists
@deployStatusActive=Įdiegta
@deployStatusRevised=Vietiniai naujinimai
@deployStatusFailed=Nepavyko
@deployStatusPending=Diegiama...
@LoaderText=Iškviečiama informacija iš serverio
#XMSG
@msgDetailFetchError=Klaida iškviečiant informaciją iš serverio
#XFLD
@executeError=Klaida
#XFLD
@executeWarning=Įspėjimas
#XMSG
@executeConfirmDialog=Informacija
#XMSG
@executeunsavederror=Prieš paleisdami savo užduočių grandinę, ją įrašykite.
#XMSG
@executemodifiederror=Užduočių grandinėje yra neįrašytų keitimų. Įrašykite.
#XMSG
@executerunningerror=Užduočių grandinė šiuo metu vykdoma. Prieš pradėdami naują vykdymą, palaukite, kol bus užbaigtas dabartinis.
#XMSG
@btnExecuteAnyway=Vis tiek vykdyti
#XMSG
@msgExecuteWithValidations=Užduočių grandinėje yra tikrinimo klaidų. Jos vykdymas gali sukelti triktį.
#XMSG
@msgRunDeployedVersion=Yra diegiamų keitimų. Bus vykdoma paskutinė įdiegta užduočių grandinės versija. Ar norite tęsti?
#XMSG
#XMSG
@navToMonitoring=Atidaryti užduočių grandinės stebėjimo priemonėje
#XMSG
txtOR=ARBA
#XFLD
@preview=Peržiūra
#XMSG
txtand=ir
#NOTR
@dateFormat=yyyy-MM-dd
#NOTR
@dateTimeFormatFilter=yyyy-MM-dd HH:mm:ss.
#NOTR
@timeFormat=HH:mm:ss
@lblColum=Stulpelis
#XFLD
@lblCondition=Sąlyga
#XFLD
@lblValue=Reikšmė
#XMSG
@msgJsonInvalid=Užduočių grandinės įrašyti nepavyko, nes yra JSON klaidų. Patikrinkite ir išspręskite.
#XTIT
@msgSaveFailTitle=Negaliojantis JSON.
#XMSG
NOT_CHAINABLE=Objekto „{0}“ negalima pridėti prie užduočių grandinės.
#XMSG
NOT_CHAINABLE_REMOTETABLE=Objektas „{0}“: replikavimo tipas bus pakeistas.
#XMSG
searchTaskChain=Ieškoti objektų

#XFLD
@txtTaskChain=Užduočių grandinė
#XFLD
@txtRemoteTable=Nuotolinė lentelė
#XFLD
@txtRemoveData=Pašalinti replikuotus duomenis
#XFLD
@txtRemovePersist=Pašalinti pastovius duomenis
#XFLD
@txtView=Rakursas
#XFLD
@txtDataFlow=Duomenų srautas
#XFLD
@txtIL=Išmanioji peržvalga
#XFLD
@txtTransformationFlow=Transformavimo srautas
#XFLD
@txtReplicationFlow=Replikavimo srautas
#XFLD
@txtDeltaLocalTable=Vietinė lentelė
#XFLD
@txtBWProcessChain=BW proceso grandinė
#XFLD
@txtSQLScriptProcedure=SQL scenarijaus procedūra
#XFLD
@txtAPI=API
#XFLD
@txtMerge=Sulieti
#XFLD
@txtOptimize=Optimizuoti
#XFLD
@txtVacuum=Naikinti įrašus

#XFLD
@txtRun=Vykdyti
#XFLD
@txtPersist=Išlaikyti
#XFLD
@txtReplicate=Replikuoti
#XFLD
@txtDelete=Naikinti įrašus su pakeitimo tipu „Panaikinta“
#XFLD
@txtRunTC=Vykdyti užduočių grandinę
#XFLD
@txtRunBW=Vykdyti BW proceso grandinę
#XFLD
@txtRunSQLScriptProcedure=Vykdyti SQL scenarijaus procedūrą

#XFLD
@txtRunDataFlow=Vykdyti duomenų srautą
#XFLD
@txtPersistView=Išlaikyti rakursą
#XFLD
@txtReplicateTable=Replikuoti lentelę
#XFLD
@txtRunIL=Vykdyti išmaniąją peržvalgą
#XFLD
@txtRunTF=Vykdyti transformavimo srautą
#XFLD
@txtRunRF=Vykdyti replikavimo srautą
#XFLD
@txtRemoveReplicatedData=Pašalinti replikuotus duomenis
#XFLD
@txtRemovePersistedData=Pašalinti pastovius duomenis
#XFLD
@txtMergeData=Sulieti
#XFLD
@txtOptimizeData=Optimizuoti
#XFLD
@txtVacuumData=Naikinti įrašus
#XFLD
@txtRunAPI=Vykdyti API

#XFLD storage type text
hdlfStorage=Failas

@statusNew=Neįdiegta
@statusActive=Įdiegta
@statusRevised=Vietiniai naujinimai
@statusPending=Diegiama...
@statusChangesToDeploy=Pasikeičia į „Diegimas“
@statusDesignTimeError=Kūrimo laiko klaida
@statusRunTimeError=Vykdymo laiko klaida

#XTIT
txtNodes=Objektai užduočių grandinėje ({0})
#XBTN
@deleteNodes=Naikinti

#XMSG
@txtDropDataToDiagram=Nutempkite objektus į diagramą.
#XMSG
@noData=Nėra objektų

#XFLD
@txtTaskPosition=Užduoties padėtis

#input parameters and variables
#XFLD
lblInputParameters=Įvesties parametrai
#XFLD
ip_name=Pavadinimas
#XFLD
ip_value=Reikšmė
#XTEXT
@noObjectsFound=Objektų nerasta

#XMSG
@msgExecuteSuccess=Užduočių grandinės vykdymas pradėtas.
#XMSG
@msgExecuteFail=Nepavyko vykdyti užduočių grandinės.
#XMSG
@msgDeployAndRunSuccess=Užduočių grandinės diegimas ir vykdymas pradėtas.
#XMSG
@msgDeployAndRunFail=Nepavyko diegti ir vykdyti užduočių grandinės.
#XMSG
@titleExecuteBusy=Palaukite.
#XMSG
@msgExecuteBusy=Ruošiame jūsų duomenis, kad būtų galima pradėti užduočių grandinės vykdymą.
#XMSG
@msgAPITestRunSuccess=API bandomasis vykdymas pradėtas.
#XMSG
@msgAPIExecuteBusy=Ruošiame jūsų duomenis, kad būtų galima pradėti API bandomąjį vykdymą.

#XTOL
txtOpenInEditor=Atidaryti rengyklėje
#XTOL
txtPreviewData=Peržiūros duomenys

#datapreview
#XMSG
@msgDataPreviewNotSupp=Šio objekto duomenų peržiūra negalima.

#XMSG Error: empty model
VAL_MODEL_EMPTY=Regis, jūsų modelis yra tuščias. Pridėkite objektų.
#XMSG Error: deploy model
@msgDeployBeforeRun=Prieš vykdydami užduočių grandinę, turite ją įdiegti.
#BTN: close dialog
btnClose=Uždaryti

#XMSG Object is not Deployed
VAL_NOT_DEPLOYED=Norint tęsti darbą su užduočių grandine, reikia įdiegti objektą „{0}“.
#XMSG Object is having runtime error
VAL_RUNTIME_ERROR=Objektas „{0}“ grąžino vykdymo laiko klaidą. Patikrinkite objektą.
#XMSG Object is having design time error
VAL_DESIGNTIME_ERROR=Objektas „{0}“ grąžino dizaino laiko klaidą. Patikrinkite objektą.
#XMSG Procedure Object has been deleted
VAL_DELETED_PROCEDURE=Šios procedūros buvo panaikintos: {0}
#XMSG New Parameter added to Procedure object
VAL_NEW_PARAMETER_ADDED=Prie procedūros „{1}“ pridėti nauji parametrai: „{0}“. Prašome iš naujo įdiegti užduoties grandinę.
#XMSG Parameter removed from Procedure object
VAL_PARAMETER_DELETED=Iš procedūros „{1}“ pašalinti nauji parametrai: „{0}“. Prašome iš naujo įdiegti užduoties grandinę.
#XMSG Parameter data type changed in Procedure object
VAL_PARAMETER_DATATYPE_MODIFIED=Parametro „{0}“ duomenų tipai pakeisti iš „{1}“ į „{2}“ procedūroje „{3}“.
#XMSG Parameter length changed of specific datatype in Procedure object
VAL_PARAMETER_LENGTH_MODIFIED=Parametro „{0}“ ilgis pakeistas iš „{1}“ į „{2}“ procedūroje „{3}“.
#XMSG Parameter precision changed of Decimal datatype in Procedure object
VAL_PARAMETER_PRECISION_MODIFIED=Parametro „{0}“ tikslumas pakeistas iš „{1}“ į „{2}“ procedūroje „{3}“.
#XMSG Parameter scale changed of Decimal datatype in Procedure object
VAL_PARAMETER_SCALE_MODIFIED=Parametro „{0}“ skalė pakeista iš „{1}“ į „{2}“ procedūroje „{3}“.
#XMSG Parameter value not provided in Procedure object
VAL_SQL_SCRIPT_PROCEDURE_PARAMETER_VALUE_MISSING=Neįvedėte įvesties parametrų, kurių reikia procedūrai „{0}“, reikšmių: „{1}“.
#XMSG Remote Table activity changes to Remove Replicated Data
VAL_REMOVE_REPLICATED_DATA=Kitas užduočių grandinės vykdymas pakeis duomenų prieigos tipą ir duomenys nebebus įkeliami realiuoju laiku.
#XMSG Remote Table activity changes
REPLICATION_TYPE_CHANGED=Objektas „{0}“: replikavimo tipas bus pakeistas.

#XFLD
@lblStartNode=Pradžios mazgas
#XFLD
@lblEndNode=Pabaigos mazgas
#XFLD
@linkTo={0} iki {1}
#XTOL
@txtViewDetails=Peržiūrėti išsamią informaciją

#XTOL
txtOpenImpactLineage=Poveikis ir kilmės analizė
#XFLD
@emailNotifications=Pranešimai el. paštu
#XFLD
@txtReset=Nustatyti iš naujo
#XFLD
@emailMsgWarning=Numatytasis šablono el. laiškas bus išsiųstas, kai el. laiško pranešimas bus tuščias
#XFLD
@notificationSettings=Pranešimų parametrai
#XFLD
@recipientEmailAddr=Gavėjo el. pašto adresas
#XFLD
@emailSubject=El. laiško tema
@emailSubjectText=Užduočių grandinė <TASKCHAIN_NAME> užbaigta ir jos būsena yra <STATUS>
#XFLD
@emailMessage=El. laiško pranešimas
@emailMessageText=Gerb. vartotojau,\n\n šiuo laišku informuojame, kad užduočių grandinė <TASKCHAIN_NAME>, vykdyta <START_TIME>, užbaigta, ir jos būsena yra <STATUS>. Vykdymas baigėsi <END_TIME>.\n\nInformacija:\nSritis:<SPACE_NAME>\nKlaida:<ERROR_MESSAGE>
@selectEmailRecipientDialogTitle=Pasirinkti gavėjo el. pašto adresą
@tenantMembers=Kliento nariai
@others=Kiti ({0})
@selectedEmailAddress=Pasirinkti gavėjai
@add=Pridėti
@placeholder=Vietos rezervavimo ženklas
@description=Aprašas
@copyText=Kopijuoti tekstą
@taskchainDetailsPlaceholder=Užduočių grandinės informacijos vietos rezervavimo ženklas
@placeholderCopied=Vietos rezervavimo ženklas nukopijuotas
@invalidEmailInfo=Įveskite teisingą el. pašto adresą
@maxMembersAlreadyAdded=Jau pridėjote maksimalų narių skaičių (20)
@enterEmailAddress=Įveskite vartotojo el. pašto adresą
@inCorrectPlaceHolder={0} nėra numatytas vietos rezervavimo ženklas el. laiško tekste.
@nsOFF=Daugiau nesiųsti pranešimų
@nsFAILED=Siųsti pranešimus el. paštu tik kai vykdymas užbaigtas su klaida
@nsCOMPLETED=Siųsti pranešimus el. paštu tik kai vykdymas sėkmingai užbaigtas
@nsANY=Siųsti pranešimus el. paštu kai vykdymas užbaigtas
@phStatus=Užduočių grandinės būsena – PAVYKO|NEPAVYKO
@phTaskChainTName=Užduočių grandinės techninis pavadinimas
@phTaskChainBName=Užduočių grandinės verslo pavadinimas
@phLogId=Vykdymo žurnalo ID
@phUser=Užduočių grandinę vykdantis vartotojas
@phLogUILink=Nuoroda į užduočių grandinės žurnalo ekraną
@phStartTime=Vykdymo pradžios laikas
@phEndTime=Vykdymo pabaigos laikas
@phErrMsg=Pirmasis klaidos pranešimas užduočių žurnale. Jei PAVYKO, žurnalas yra tuščias.
@phSpaceName=Srities techninis pavadinimas
@emailFormatError=Netinkamas el. pašto formatas
@emailFormatErrorInListText=Ne klientų narių sąraše įvestas netinkamas el. pašto formatas.
@emailSubjectTemplateText=Užduočių grandinės pranešimas: $$taskChainName$$ - Sritis: $$spaceId$$ - Būsena: $$status$$
@emailMessageTemplateText=Sveiki,\n\n Jūsų užduočių grandinė pavadinimu $$taskChainName$$ užbaigta su būsena $$status$$. \n Toliau pateikiama kita informacija apie užduočių grandinę:\n - Užduočių grandinės techninis pavadinimas: $$taskChainName$$ \n - Užduočių grandinės vykdymo žurnalo ID: $$logId$$ \n - Užduočių grandinę vykdęs vartotojas: $$user$$ \n - Nuoroda į užduočių grandinės žurnalo ekraną: $$uiLink$$ \n - Užduočių grandinės vykdymo pradžios laikas: $$startTime$$ \n - Užduočių grandinės vykdymo pabaigos laikas: $$endTime$$ \n - Srities pavadinimas: $$spaceId$$ \n
@deleteEmailRecepient=Naikinti gavėją
@emailInputDisabledText=Norėdami pridėti el. laiško gavėjų, įdiekite užduočių grandinę.
@tenantOwnerDomainMatchErrorText=El. pašto adreso domenas neatitinka kliento savininko domeno: {0}
@totalEmailIdLimitInfoText=Galite pasirinkti iki 20 el. laiško gavėjų, įskaitant kliento narių vartotojus ir kitus gavėjus.
@emailDomainInfoText=Priimami tik el. pašto adresai su domenu: {0}.
@duplicateEmailErrorText=Sąraše yra el. laiško gavėjų dublikatų.

#XFLD Zorder Title
@txtZorderTitle=„Apache Spark Z-Order“ stulpeliai

#XFLD Zorder NoColumn
@txtZorderNoColumn=Nerasta „Z-Order“ stulpelių

#XFLD Zorder Column PrimaryKey
@txtZorderColumnPrimaryKey=Pirminis raktas

#XFLD
@lblOperators=Operatoriai
addNewSelector=Pridėti kaip naują užduotį
parallelSelector=Pridėti kaip lygiagrečią užduotį
replaceSelector=Pakeisti esamą užduotį
addparallelbranch=Pridėti kaip lygiagrečią šaką
addplaceholder=Pridėti vietos rezervavimo ženklą
addALLOperation=ALL operatorius
addOROperation=ANY operatorius
addplaceholdertocanvas=Pridėti vietos rezervavimo ženklą į drobę
addplaceholderonselected=Pridėti vietos rezervavimo ženklą po pasirinktos užduoties
#XFLD: Add Parallel Branch to Selected Task
addparallelbranchonselected=Pridėti lygiagrečią šaką po pasirinktos užduoties
addOperator=Pridėti operatorių
txtAdd=Pridėti
txtPlaceHolderText=Čia nuvilkite užduotį
@lblLayout=Išdėstymas

#XMSG
VAL_UNCONNECTED_TASK=Užduotis „{0}“ neprijungta prie užduočių grandinės.
#XMSG
VAL_TASK_SHOULD_HAVE_ONE_INCOMING_LINK=Užduotyje „{0}“ turi būti tik viena įeinanti nuoroda.
#XMSG
VAL_TASK_SHOULD_HAVE_INCOMING_LINK=Užduotyje „{0}“ turi būti viena įeinanti nuoroda.
#XMSG
VAL_UNCONNECTED_OPERATOR=Operatorius „{0}“ neprijungtas prie užduočių grandinės.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ATLEAST_TWO_INCOMING_LINK=Operatoriuje „{0}“ turi būti bent dvi įeinančios nuorodos.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ONE_OUT_LINK=Operatoriuje „{0}“ turi būti bent viena išeinanti nuoroda.
#XMSG
VAL_CIRCULAR_REFERENCE_EXITS=Užduočių grandinėje „{0}“ yra žiedinis ciklas.
#XMSG
VAL_UNCONNECTED_BRANCH=Objektas / šaka „{0}“ neprijungta prie užduočių grandinės.
#XMSG
VAL_TASK_TRIGGERED_PARALLEL_MORE_THAN_ONE_TIME=Užduotis „{0}“ lygiagrečiai prijungta kelis kartus. Norėdami tęsti pašalinkite dublikatus.


txtBegin=Pradėti
txtNodesInLink=Įtraukti objektai
#XTOL Tooltip for a context button on diagram
openInNewTab=Atidaryti naujame skirtuke
#XTOL Tooltip for a context button on diagram
txtConnectToTaskOperator=Vilkite, jei norite susieti
@emailUpdateError=Klaida naujinant pranešimų el. paštu sąrašą

#XMSG
noTeamPrivilegeTxt=Neturite leidimo matyti klientų narių sąrašą. Naudokite skirtuką „Kiti“, kad pridėtumėte el. laiško gavėjus rankiniu būdu.

#XFLD Package
@txtPackage=Paketas

#XMSG warning for repository package assignment changed. {1} - package name
VAL_PACKAGE_CHANGED=Šį objektą priskyrėte pakuotei „{1}“. Spustelėkite „Įrašyti“, norėdami patvirtinti ir tikrinti šį pakeitimą. Atkreipkite dėmesį, kad įrašius šioje rengyklėje priskyrimo pakuotei negalima anuliuoti.
#XMSG warning for repository package dependency issue.
MISSING_DEPENDENCY=Objekto „{0}“ priklausomybių negalima išspręsti paketo „{1}“ kontekste.

#Warning messages for the folders in the "Others" tab of task chain editor
#XMSG generic warning if getting the objects in a particular folder fails with unhandled errors
getNonRepoArtefactTypeDescendantsError=Nuskaitant objektus pasirinktame aplanke, iškilo problema.
#BW Process Chains folder related warning messages
#XMSG warning if user does not have the necessary permission related to the SAP BW Bridge space
missingPrivilegeForBridgeSpaceError=Neturite reikiamo leidimo peržiūrėti ar įtraukti BW procesų grandines užduoties grandinėje.
#XMSG warning if there was a problem retrieving the BW process chains due to bridge space membership, bridge connection issues or other errors
generalBridgeSpaceError=Nuskaitant BW proceso grandines iškilo problema.
#XMSG warning if the BW API for retrieving the BW process chains from the SAP BW Bridge tenant fails
bwApiClientError=Nuskaitant BW proceso grandines iš SAP BW tilto kliento iškilo problema.
#XMSG warning if BW process chains list is empty
bwApiEmptyListError=BW proceso grandinių SAP BW tilto kliente nerasta.
#XMSG: Task log message for invalid OIDC configuration in the SAP BW Bridge tenant
bwBridgeOIDCConfigStatusInvalid=Šiam klientui nesukonfigūruota „OpenID“ autentifikacija. Naudokite kliento ID „{0}“ ir atpažinimo ženklo URL „{1}“, kad SAP BW tilto kliente sukonfigūruotumėte „OpenID“ autentifikacija pagal SAP pastabą 3536298.
#XMSG BW Process Chain Object has been deleted
VAL_DELETED_BW=Toliau nurodytos BW proceso grandinės galimai buvo panaikintos iš SAP tilto kliento: {0}
#BW SQL Script Procedures folder related warning messages
#XFLD waring if the Procedure list is empty
procedureAPIEmptyList=Jokių procedūrų nebuvo sukurta arba teisė VYKDYTI nebuvo suteikta „Open SQL“ schemai.
#Change digram orientations
changeOrientations=Keisti padėtį


# placeholder for the API Path
apiPath=Pavyzdžiui: /job/v1
# placeholder for the status API Path
statusAPIPath=Pavyzdžiui: /job/v1/{id}/status
# valueStateText for the API Path
apiPathRequired=Būtinas API kelias
#placeholder for the CSRF Token URL
csrfTokenURL=Palaikomas tik HTTPS
# Response Type 1
statusCode=Gauti rezultatą iš HTTP būsenos kodo
# Response Type 2
locationHeader=Gauti rezultatą iš HTTP būsenos kodo ir vietos antraštės
# Response Type 3
responseBody=Gauti rezultatą iš HTTP būsenos kodo ir atsakymo teksto
# placeholder for ID
idPlaceholder=Įveskite JSON kelią
# placeholder for indicator value
indicatorValue=Įveskite vertę
# Placeholder for key
keyPlaceholder=Įveskite raktą
# Error message for missing key
KeyRequired=Būtinas raktas
# Error message for invalid key format
invalidKeyFormat=Jūsų įvestas antraštės raktas neleidžiamas. Tinkamos antraštės yra:<ul><li>„prefer“</li><li>Antraštės, prasidedančios „x-“, išskyrus „x-forwarded-host“</li><li>Antraštės, kuriose yra raidiniai ir skaitiniai simboliai, „-“ arba „_“</li></ul>
# Error message for missing value
valueRequired=Reikšmė būtina
# Error message for invalid characters in value
invalidValueCharacters=Antraštėje yra netinkamų simbolių. Leidžiami specialieji simboliai yra:\t „;“, „:“, „-“, „_“, „,“, „?“, „/“ ir „*“
# Validation message for invoke api path
apiPathValidation=Įveskite tinkamą API kelią, pvz., /job/v1
# Validation message for JSON path
jsonPathValidation=Įveskite tinkamą JSON kelią
# Validation message for success/error indicator
indicatorValueValidation=Indikatoriaus reikšmė turi prasidėti raidiniu arba skaitiniu simboliu ir joje gali būti šie specialieji simboliai:\t „-“ ir „_“
# Error message for JSON path
jsonPathRequired=Būtinas JSON kelias
# Error message for invalid API Technical Name
invalidTechnicalName=Techniniame pavadinime yra netinkamų simbolių
# Error message for empty Technical Name
emptyTechnicalName=Reikalingas techninis pavadinimas
# Tooltip for codeEditor dialog
codeEditorTooltip=Atidaryti JSON redagavimo langą
# Status Api path validation message
validationStatusAPIPath=Įveskite tinkamą API kelią, pvz., /job/v1/{id}/status
# CSRF token URL validation message
validationCSRFTokenURL=CSRF atpažinimo ženklo URL turi prasidėti „https://“ ir būti galiojantis URL
# Select a connection
selectConnection=Pasirinkite ryšį
# Validation message for connection item error
connectionNotReplicated=Ryšys šiuo metu netinkamas API užduotims vykdyti. Atidarykite taikomąją programą „Ryšiai“ ir iš naujo įveskite savo kredencialus, kad pataisytumėte HTTP ryšį
# Validation message for API task properties wrong value or missing
VAL_REST_API_PROPERTIES_MISSING=API užduotyje „{0}“ yra netinkamų reikšmių arba jų trūksta šioms ypatybėms: {1}
# Validation message for API connection api is throwing some error or selected connection has some warning
VAL_REST_API_CONNECTION=Įvyko API užduoties „{0}“ HTTP ryšio klaida. Atidarykite taikomąją programą „Ryšiai“ ir patikrinkite ryšį
# Validation message for API task properties when delected connection
VAL_REST_API_CONNECTION_DELETED=API užduotis „{0}“ panaikino „{1}“ ryšį
# Validation message for API task properties when connection cannot be consumed in the API task
VAL_REST_API_CONNECTION_CONSUMPTION_WARNING=API užduoties „{0}“ ryšio „{1}“ negalima naudoti API užduotims vykdyti. Atidarykite taikomąją programą „Ryšiai“ ir iš naujo įveskite savo kredencialus, kad užmegztumėte ryšį API užduotims
# Information message if synchrnous mode is selected in the API Invocation then we don't need status information
modeInfoText=Veikiant sinchroniniu režimu, API iškvietimams nerodomas būsenos skydelis
# validation dialog button for Run API Test
saveAnyway=Vis tiek įrašyti
# validation message for technical name
technicalNameValidation=Techninis pavadinimas turi būti unikalus užduočių grandinėje. Pasirinkite kitą techninį pavadinimą
# Connection error message
getHttpConnectionsError=Nepavyko gauti HTTP ryšių
# Msg for the API test run when the Task chain is not saved
taskChainNotSaved=Prieš vykdant API bandomąjį vykdymą reikia įrašyti užduočių grandinę
# Msg failed to run API test run
@failedToRunAPI=API bandomasis vykdymas nepavyko
# Msg for the API test run when its already in running state
apiTaskRunning=API bandomasis vykdymas jau vykdomas. Ar norite pradėti naują bandomąjį vykdymą?

topToBtm=Iš viršaus į apačią
leftToRight=Iš kairės į dešinę

#XTIT Apache Spark Application Settings
txtHeaderApacheSettings=„Apache Spark“ taikomosios programos parametrai
#XFLD Use Default
txtUseSpaceDefault=Naudoti numatytuosius parametrus
#XFLD Application
txtApplication=Taikomoji programa
#XFLD Define new settings for this Task
txtNewSettings=Apibrėžti naujus parametrus šiai užduočiai

#XFLD Use Default
txtUseDefault=Naudoti numatytuosius parametrus




