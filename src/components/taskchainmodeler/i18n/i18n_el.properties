#XTOL Undo
@undo=Ανάκληση
#XTOL Redo
@redo=Επαναφορά
#XTOL Delete Selected Symbol
@deleteNode=Διαγραφή Επιλεγμένου Συμβόλου
#XTOL Zoom to Fit
@zoomToFit=Εστίαση για Προσαρμογή
#XTOL Auto Layout
@autoLayout=Αυτόματη Διάταξη
#XMSG
@welcomeText=Επιλέξτε και σύρετε τα αντικείμενα από τον αριστερό πίνακα σε αυτόν τον καμβά.
#XMSG
@txtNoData=Μάλλον δεν έχετε προσθέσει ακόμα αντικείμενα.
#XMSG
VAL_ENTER_VALID_STRING_GEN=Εισάγετε έγκυρο αλφαριθμητικό μικρότερο ή ίσο με {0}
#XMSG
@noParametersMsg=Αυτή η διαδικασία δεν έχει παραμέτρους εισόδου.
#XMSG
@ip_enterValueMsg=Το "{0}" (Εκτέλεση Διαδικασίας Σεναρίου SQL) έχει παραμέτρους εισόδου. Μπορείτε να καθορίσετε μία τιμή για καθεμία.
#XTOL
@validateModel=Μηνύματα Επικύρωσης
#XTOL
@hierarchy=Ιεραρχία
#XTOL
@columnCount=Αριθμός Στηλών
#XFLD
@yes=Ναι
#XFLD
@no=Οχι
#XTIT Save Dialog param
@modelNameTaskChain=Αλυσίδα εργασιών
#properties panel
@lblPropertyTitle=Ιδιότητες
#XFLD
@lblGeneral=Γενικό
#XFLD : Setting
@lblSetting=Ρυθμίσεις
#XTXT: Delta Enabled Local Table Deletion
txtDeltaTableDeletion=Διαγραφή όλων των πλήρως επεξεργασμένων εγγραφών με Τύπο Αλλαγής «Διαγραμμένο» που είναι παλαιότερες από
#XTXT: Delta Enabled Local Table Deletion Input field description
desDeltaTableDeletion=Ημέρες
#XFLD: Data Activation label
@lblDataActivation=Ενεργοποίηση Δεδομένων
#XFLD: Latency label
@latency=Λανθάνων χρόνος
#XTEXT: Text for Latency dropdown
txtLatencyDefault=Προεπιλογή
#XTEXT: Text 1 hour
txtOneHour=1 Ωρα
#XTEXT: Text 2 hours
txtTwoHours=2 Ωρες
#XTEXT: Text 3 hours
txtThreeHours=3 Ωρες
#XTEXT: Text 4 hours
txtFourHours=4 Ωρες
#XTEXT: Text 6 hours
txtSixHours=6 Ωρες
#XTEXT: Text 12 hours
txtTwelveHours=12 Ωρες
#XTEXT: Text 1 day
txtOneDay=1 Ημέρα
#XFLD: Latency label
@autoRestartHead=Αυτόματη Επανεκκίνηση
#XFLD
@lblConnectionName=Σύνδεση
#XFLD
@lblQualifiedName=Προσδιορισμένο Όνομα
#XFLD
@lblSpaceName=Όνομα Χώρου
#XFLD
@lblLocalSchemaName=Τοπικό Σχήμα
#XFLD
@lblType=Τύπ.Αντικ.
#XFLD
@lblActivity=Δραστηριότητα
#XFLD
@lblTableName=Ονομα Πίνακα
#XFLD
@lblBusinessName=Επωνυμία Επιχείρησης
#XFLD
@lblTechnicalName=Τεχνικό Ονομα
#XFLD
@lblSpace=Χώρος
#XFLD
@lblLabel=Ετικέτα
#XFLD
@lblDataType=Τύπος Δεδομένων
#XFLD
@lblDescription=Περιγραφή
#XFLD
@lblStorageType=Αποθήκευση
#XFLD
@lblHTTPConnection=Γενική Σύνδεση ΗΤΤΡ
#XFLD
@lblAPISettings=Γενικές Ρυθμίσεις ΑΡΙ
#XFLD
@header=Κεφαλίδες
#XFLD
@lblInvoke=Κλήση ΑΡΙ
#XFLD
@lblMethod=Μέθοδος
#XFLD
@lblUrl=URL Βάσης
#XFLD
@lblAPIPath=Διαδρομή API
#XFLD
@lblMode=Λειτουργία
#XFLD
@lblCSRFToken=Απαιτούμενο Διακριτικό CSRF 
#XFLD
@lblTokenURL=Διεύθυνση URL διακριτικού CSRF
#XFLD
@csrfTokenInfoText=Αν δεν καταχωρίστηκε, θα χρρησιμοποιηθούν το βασικό URL και η διαδρομή API
#XFLD
@lblCSRF=Διακριτικό CSRF
#XFLD
@lblRequestBody=Κείμενο Αίτησης
#XFLD
@lblFormat=Μορφοπoίηση
#XFLD
@lblResponse=Απόκριση
#XFLD
@lblId=ID για ανάκτηση κατάστασης
#XFLD
@Id=ID
#XFLD
@lblSuccessIndicator=Δείκτης Επιτυχίας
#XFLD
@lblErrorIndicator=Δείκτης Σφάλματος
#XFLD
@lblErrorReason=Αιτία σφάλματος
#XFLD
@lblStatus=Κατάσταση
#XFLD
@lblApiTestRun=Δοκιμαστική Εκτέλεση ΑΡΙ
#XFLD
@lblRunStatus=Κατάσταση Εκτέλεσης
#XFLD
@lblLastRan=Τελευταία Εκτέλεση Στις
#XFLD
@lblTestRun=Δοκιμαστική Εκτέλεση
#XFLD
@lblDefaultHeader=Προεπιλεγμένα Πεδία Κεφαλίδας (Ζεύγη Τιμών-Κωδικών)
#XFLD
@lblAdditionalHeader=Πρόσθετο Πεδίο Κεφαλίδας
#XFLD
@lblKey=Κλειδί
#XFLD
@lblEditJSON=Επεξεργασία JSON
#XFLD
@lblTasks=Εργασίες
#XFLD
@lblRESTfullTask=ΑΡΙ
#XFLD: add field button text
btnAddField=Προσθήκη Πεδίου Κεφαλίδας
#XFLD: view Details link
@viewDetails=Προβολή Λεπτομερειών
#XTOL
tooltipTxt=Περισσότερα
#XTOL
delete=Διαγραφή
#XBTN: ok button text
btnOk=Ok
#XBTN: cancel button text
btnCancel=Ακύρωση
#XBTN: save button text
btnSave=Αποθήκευση
#XMSG Error: Object has duplicated technical name.
VAL_OBJECT_DUPLICATE_NAME=Το αντικείμενο ''{0}'' υπάρχει ήδη στην αποθήκη. Εισάγετε άλλο όνομα.
#XMSG: loading message while opening task chain
loadTaskChain=Φόρτωση αλυσίδας εργασιών...
#model properties
#XFLD
@status_panel=Κατάσταση Εκτέλεσης
#XFLD
@deploy_status_panel=Κατάσταση Ανάπτυξης
#XFLD
@status_lbl=Κατάσταση
#XFLD
@lblLastExecuted=Τελευταία Εκτέλεση
#XFLD
@lblNotExecuted=Δεν Εκτελέστηκε Ακόμα
#XFLD
@lblNotDeployed=Μη Αναπτυγμένο
#XFLD
errorDetailsTxt=Αδύνατη εμφάνιση κατάστασης εκτέλεσης
#XBTN: Schedule dropdown menu
SCHEDULE=Χρονοδ/μα
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Επεξεργασία Προγράμματος
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Διαγραφή Προγράμματος
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Δημιουργία Προγράμματος
#XLNK
viewDetails=Προβολή Λεπτομερειών
#XMSG: error message for reading execution status from backend
backendErrorMsg=Τα δεδομένα ενδεχομένως δεν φορτώνονται από τον διακομιστή προσωρινά. Δοκιμάστε να προσκομίσετε ξανά τα δεδομένα.
#XFLD: Status text for Completed
@statusCompleted=Ολοκληρωμένο
#XFLD: Status text for Running
@statusRunning=Εκτελείται
#XFLD: Status text for Failed
@statusFailed=Απέτυχε
#XFLD: Status text for Stopped
@statusStopped=Διακόπηκε
#XFLD: Status text for Stopping
@statusStopping=Διακόπηκε
#XFLD
@LoaderTitle=Φόρτωση
#XFLD
#XFLD: Deploy status, dataflow has  been deployed / deployment date exists
@deployStatusActive=Αναπτύχθηκε
@deployStatusRevised=Τοπικές Ενημερώσεις
@deployStatusFailed=Απέτυχε
@deployStatusPending=Ανάπτυξη...
@LoaderText=Προσκόμιση λεπτομερειών από τον διακομιστή
#XMSG
@msgDetailFetchError=Σφάλμα κατά την προσκόμιση λεπτομερειών από τον διακομιστή
#XFLD
@executeError=Σφάλμα
#XFLD
@executeWarning=Προειδοποίηση
#XMSG
@executeConfirmDialog=Πληροφορίες
#XMSG
@executeunsavederror=Αποθηκεύστε την αλυσίδα εργασιών πριν την εκτελέσετε.
#XMSG
@executemodifiederror=Υπάρχουν μη αποθηκευμένες αλλαγές στην αλυσίδα εργασιών. Αποθηκεύστε τις.
#XMSG
@executerunningerror=Η αλυσίδα εργασιών εκτελείται ήδη. Περιμένετε μέχρι να ολοκληρωθεί η τρέχουσα εκτέλεση πριν ξεκινήσετε μία νέα.
#XMSG
@btnExecuteAnyway=Εκτέλεση
#XMSG
@msgExecuteWithValidations=Η αλυσίδα εργασιών έχει λάθη επαλήθευσης. Η εκτέλεση της αλυσίδας εργασιών ενδέχεται να οδηγήσει σε αποτυχία.
#XMSG
@msgRunDeployedVersion=Υπάρχουν αλλαγές για ανάπτυξη. Η τελευταία αναπτυγμένη έκδοση της αλυσίδας εργασιών θα εκτελεστεί. Θέλετε να συνεχίσετε;
#XMSG
#XMSG
@navToMonitoring=Ανοικτό σε Παρακολούθηση Αλυσίδας Εργασιών
#XMSG
txtOR=OR
#XFLD
@preview=Προεπισκόπηση
#XMSG
txtand=και
#NOTR
@dateFormat=yyyy-MM-dd
#NOTR
@dateTimeFormatFilter=yyyy-MM-dd HH:mm:ss.
#NOTR
@timeFormat=HH:mm:ss
@lblColum=Στήλη
#XFLD
@lblCondition=Συνθήκη
#XFLD
@lblValue=Αξία
#XMSG
@msgJsonInvalid=Η αλυσίδα εργασιών δεν αποθηκεύτηκε γιατί υπάρχουν σφάλματα στο JSON. Ελέγξτε και επιλύστε το πρόβλημα.
#XTIT
@msgSaveFailTitle=Ακυρο JSON.
#XMSG
NOT_CHAINABLE=Το αντικείμενο ''{0}''’ δεν μπορεί να προστεθεί στην αλυσίδα εργασιών.
#XMSG
NOT_CHAINABLE_REMOTETABLE=Αντικείμενο ''{0}'': τύπος αντιγραφής θα αλλάξει.
#XMSG
searchTaskChain=Αναζήτηση Αντικειμένων

#XFLD
@txtTaskChain=Αλυσίδα Εργασιών
#XFLD
@txtRemoteTable=Απομακρυσμένος Πίνακας
#XFLD
@txtRemoveData=Κατάργηση Αντιγραμμένων δεδομένων
#XFLD
@txtRemovePersist=Αφαίρεση Διατηρημένων Δεδομένων
#XFLD
@txtView=Προβολή
#XFLD
@txtDataFlow=Ροή Δεδομένων
#XFLD
@txtIL=Έξυπνη Αναζήτηση
#XFLD
@txtTransformationFlow=Ροή Μετασχηματισμού
#XFLD
@txtReplicationFlow=Ροή Αντιγραφής
#XFLD
@txtDeltaLocalTable=Τοπικός Πίνακας
#XFLD
@txtBWProcessChain=Αλυσίδα Διαδικασιών BW
#XFLD
@txtSQLScriptProcedure=Διαδικασία Σεναρίου SQL
#XFLD
@txtAPI=ΑΡΙ
#XFLD
@txtMerge=Συγχώνευση
#XFLD
@txtOptimize=Βελτιστοποίηση
#XFLD
@txtVacuum=Διαγραφή Εγγραφών

#XFLD
@txtRun=Εκτέλεση
#XFLD
@txtPersist=Διατήρηση
#XFLD
@txtReplicate=Αντιγραφή
#XFLD
@txtDelete=Διαγραφή Δεδομένων με Τύπο Αλλαγής ‘Διαγραμμένο’
#XFLD
@txtRunTC=Εκτέλεση Αλυσίδας Εργασιών
#XFLD
@txtRunBW=Εκτέλεση Αλυσίδας Διαδικασιών ΒW
#XFLD
@txtRunSQLScriptProcedure=Εκτέλεση Διαδικασίας Σεναρίου SQL

#XFLD
@txtRunDataFlow=Εκτέλεση Ροής Δεδομένων
#XFLD
@txtPersistView=Διατήρηση Προβολής
#XFLD
@txtReplicateTable=Αντιγραφή Πίνακα
#XFLD
@txtRunIL=Εκτέλεση Εξυπνης Αναζήτησης
#XFLD
@txtRunTF=Εκτέλεση Ροής Μετασχηματισμού
#XFLD
@txtRunRF=Εκτέλεση Ροής Αντιγραφής
#XFLD
@txtRemoveReplicatedData=Κατάργηση Αντιγραμμένων δεδομένων
#XFLD
@txtRemovePersistedData=Αφαίρεση Διατηρημένων Δεδομένων
#XFLD
@txtMergeData=Συγχώνευση
#XFLD
@txtOptimizeData=Βελτιστοποίηση
#XFLD
@txtVacuumData=Διαγραφή Εγγραφών
#XFLD
@txtRunAPI=Εκτέλεση ΑΡΙ

#XFLD storage type text
hdlfStorage=Αρχείο

@statusNew=Μη Αναπτυγμένο
@statusActive=Αναπτύχθηκε
@statusRevised=Τοπικές Ενημερώσεις
@statusPending=Ανάπτυξη...
@statusChangesToDeploy=Αλλαγές για Ανάπτυξη
@statusDesignTimeError=Λάθος Χρόνου Σχεδίασης
@statusRunTimeError=Λάθος Χρόνου Εκτέλεσης

#XTIT
txtNodes=Αντικείμενα σε Αλυσίδα Εργασιών ({0})
#XBTN
@deleteNodes=Διαγραφή

#XMSG
@txtDropDataToDiagram=Επλέξτε και σύρετε αντικείμενα στο διάγραμμα.
#XMSG
@noData=Κανένα αντικείμενο

#XFLD
@txtTaskPosition=Θέση Εργασίας

#input parameters and variables
#XFLD
lblInputParameters=Παράμετροι Εισόδου
#XFLD
ip_name=Ονομα
#XFLD
ip_value=Αξία
#XTEXT
@noObjectsFound=Δεν βρέθηκαν Αντικείμενα

#XMSG
@msgExecuteSuccess=Η εκτέλεση της αλυσίδας εργασιών άρχισε.
#XMSG
@msgExecuteFail=Αδύνατη εκτέλεση της αλυσίδας εργασιών.
#XMSG
@msgDeployAndRunSuccess=Η ανάπτυξη αλυσίδας εργασιών και η εκτέλεση άρχισαν.
#XMSG
@msgDeployAndRunFail=Αδύνατη ανάπτυξη και εκτέλεση αλυσίδας εργασιών.
#XMSG
@titleExecuteBusy=Περιμένετε.
#XMSG
@msgExecuteBusy=Προετοιμάζονται τα δεδομένα για έναρξη εκτέλεσης της αλυσίδας εργασιών.
#XMSG
@msgAPITestRunSuccess=Δοκιμαστική εκτέλεση ΑΡΙ άρχισε.
#XMSG
@msgAPIExecuteBusy=Προετοιμάζονται τα δεδομένα για έναρξη εκτέλεσης της δοκιμαστικής εκτέλεσης ΑΡΙ.

#XTOL
txtOpenInEditor=Ανοιγμα σε Επεξεργαστή
#XTOL
txtPreviewData=Δεδομένα Προεπισκόπησης

#datapreview
#XMSG
@msgDataPreviewNotSupp=Προεπισκόπηση δεδομένων μη διαθέσιμη για αυτό το αντικείμενο.

#XMSG Error: empty model
VAL_MODEL_EMPTY=Το μοντέλο σας φαίνεται να είναι κενό. Προσθέστε ορισμένα αντικείμενα.
#XMSG Error: deploy model
@msgDeployBeforeRun=Πρέπει να αναπτύξετε την αλυσίδα εργασιών πριν την εκτελέσετε.
#BTN: close dialog
btnClose=Κλείσιμο

#XMSG Object is not Deployed
VAL_NOT_DEPLOYED=Το αντικείμενο ''{0}'' πρέπει να αναπτυχθεί για να συνεχίσει με την αλυσίδα εργασιών.
#XMSG Object is having runtime error
VAL_RUNTIME_ERROR=Το αντικείμενο ''{0}'' έχει σφάλμα χρόνου εκτέλεσης. Ελέγξτε το.
#XMSG Object is having design time error
VAL_DESIGNTIME_ERROR=Το αντικείμενο ''{0}'' έχει σφάλμα χρόνου σχεδίασης. Ελέγξτε το.
#XMSG Procedure Object has been deleted
VAL_DELETED_PROCEDURE=Οι παρακάτω διαδικασίες διαγράφηκαν: {0}
#XMSG New Parameter added to Procedure object
VAL_NEW_PARAMETER_ADDED=Νέες παράμετροι προστέθηκαν σε διαδικασία "{1}": "{0}". Επαναναπτύξτε την αλυσίδα εργασιών
#XMSG Parameter removed from Procedure object
VAL_PARAMETER_DELETED=Παράμετροι διαγράφηκαν από διαδικασία "{1}": "{0}".Επαναναπτύξτε την αλυσίδα εργασιών
#XMSG Parameter data type changed in Procedure object
VAL_PARAMETER_DATATYPE_MODIFIED=Τύπος δεδομένων παραμέτρου  "{0}" άλλαξε από "{1}"σε "{2}" στη διαδικασία "{3}"
#XMSG Parameter length changed of specific datatype in Procedure object
VAL_PARAMETER_LENGTH_MODIFIED=Μήκος παραμέτρου  "{0}" άλλαξε από "{1}" σε "{2}" στη διαδικασία "{3}".
#XMSG Parameter precision changed of Decimal datatype in Procedure object
VAL_PARAMETER_PRECISION_MODIFIED=Ακρίβεια παραμέτρου"{0}" άλλαξε από "{1}" σε  "{2}" στη διαδικασία "{3}".
#XMSG Parameter scale changed of Decimal datatype in Procedure object
VAL_PARAMETER_SCALE_MODIFIED=Κλίμακα παραμέτρου "{0}" άλλαξε από "{1}" σε "{2}" στη διαδικασία "{3}".
#XMSG Parameter value not provided in Procedure object
VAL_SQL_SCRIPT_PROCEDURE_PARAMETER_VALUE_MISSING=Δεν καταχωρίσατε τιμές για παραμέτρους εισόδου που απαιτούνται για διαδικασία "{0}": "{1}"
#XMSG Remote Table activity changes to Remove Replicated Data
VAL_REMOVE_REPLICATED_DATA=Η επόμενη εκτέλεση μίας αλυσίδας εργασιών θα αλλάξει τον τύπο πρόσβασης δεδομένων και δεν θα φορτωθούν δεδομένα σε πραγματικό χρόνο.
#XMSG Remote Table activity changes
REPLICATION_TYPE_CHANGED=Αντικείμενο ''{0}'': τύπος αντιγραφής θα αλλάξει.

#XFLD
@lblStartNode=Κόμβος Εναρξης
#XFLD
@lblEndNode=Κόμβος Λήξης
#XFLD
@linkTo={0} έως {1}
#XTOL
@txtViewDetails=Προβολή Λεπτομερειών

#XTOL
txtOpenImpactLineage=Ανάλυση Επιπτώσεων και Προέλευσης
#XFLD
@emailNotifications=Ηλεκτρονικές Ειδοποιήσεις
#XFLD
@txtReset=Επανακαθορισμός
#XFLD
@emailMsgWarning=Το προεπιλεγμένο email προτύπου θα σταλεί όταν το μήνυμα είναι κενό
#XFLD
@notificationSettings=Ρυθμίσεις Ειδοποίησης
#XFLD
@recipientEmailAddr=Ηλεκτρονική Διεύθυνση Παραλήπτη
#XFLD
@emailSubject=Θέμα του Email
@emailSubjectText=Αλυσίδα εργασιών <TASKCHAIN_NAME> ολοκληρώθηκε με κατάσταση <STATUS>
#XFLD
@emailMessage=Μήνυμα Email
@emailMessageText=Αγαπητέ Χρήστη,\n\n Με το παρόν σας ενημερώνουμε ότι η αλυσίδα εργασιών:<TASKCHAIN_NAME> που εκτελέστηκε στις <START_TIME> ολοκληρώθηκε με κατάσταση <STATUS>. Η εκτέλεση έληξε στις <END_TIME>.\n\nΛεπτομέρειες:\nΧώρος:<SPACE_NAME>\nΣφάλμα:<ERROR_MESSAGE>
@selectEmailRecipientDialogTitle=Επιλέξτε Ηλεκτρονική Διεύθυνση Παραλήπτη
@tenantMembers=Μέλη μισθωτή
@others=Λοιπά({0})
@selectedEmailAddress=Επιλεγμένοι Παραλήπτες
@add=Προσθήκη
@placeholder=Σύμβολο
@description=Περιγραφή
@copyText=Αντιγραφή κειμένου
@taskchainDetailsPlaceholder=Σύμβολα για λεπτομέρειες αλυσίδας εργασιών
@placeholderCopied=Το σύμβολο αντιγράφηκε
@invalidEmailInfo=Εισάγετε την σωστή ηλεκτρονική διεύθυνση
@maxMembersAlreadyAdded=Προσθέσατε ήδη τον μέγιστο αριθμό 20 μελών
@enterEmailAddress=Εισάγετε ηλεκτρονική διεύθυνση χρήστη
@inCorrectPlaceHolder={0} δεν είναι το αναμενόμενο σύμβολο στο κείμενο του email.
@nsOFF=Μην στείλετε ειδοποιήσεις
@nsFAILED=Αποστολή ηλεκτρονικής ειδοποίησης μόνο όταν η εκτέλεση ολοκληρωθεί με σφάλμα
@nsCOMPLETED=Αποστολή ηλεκτρονικής ειδοποίησης μόνο όταν η εκτέλεση ολοκληρωθεί επιτυχώς
@nsANY=Αποστολή email όταν η εκτέλεση ολοκληρωθεί
@phStatus=Κατάσταση αλυσίδας εργασιών - ΕΠΙΤΥΧΙΑ|ΑΠΟΤΥΧΙΑ
@phTaskChainTName=Τεχνικό όνομα αλυσίδας εργασιών
@phTaskChainBName=Επιχειρηματικό όνομα αλυσίδας εργασιών
@phLogId=ΙD ημερολογίου της εκτέλεσης
@phUser=Ο χρήστης που εκτελεί την αλυσίδα εργασιών
@phLogUILink=Συνδεση με την εμφάνιση ημερολογίου της αλυσίδας εργασιών
@phStartTime=Χρόνος έναρξης εκτέλεσης
@phEndTime=Χρόνος λήξης εκτέλεσης
@phErrMsg=Πρώτο μήνυμα σφάλματος στο ημερολόγιο εργασιών. Το ημερολόγιο είναι κενό σε περίπτωση ΕΠΙΤΥΧΙΑΣ
@phSpaceName=Τεχνικό όνομα χώρου
@emailFormatError=Ακυρη μορφή email
@emailFormatErrorInListText=Ακυρη μορφή email καταχωρίστηκε σε λίστα μελών μη μισθωτών.
@emailSubjectTemplateText=Ειδοποίηση για Αλυσίδα Εργασιών: $$taskChainName$$ - Χώρος: $$spaceId$$ - Κατάσταση: $$status$$
@emailMessageTemplateText=Γεια σας,\n\n Η αλυσίδα εργασιών σας με όνομα $$taskChainName$$ ολοκληρώθηκε με κατάσταση $$status$$. \n Εδώ θα βρείτε λεπτομέρειες σχετικά με την αλυσίδα εργασιών:\n - Τεχνικό όνομα αλυσίδας εργασιών: $$taskChainName$$ \n - ID ημερολογίου της εκτέλεσης αλυσίδας εργασιών: $$logId$$ \n - Ο χρήστης που εκτέλεσε την αλυσίδα εργασιών: $$user$$ \n - Σύνδεση με την εμφάνιση ημερολογίου της αλυσίδας εργασιών: $$uiLink$$ \n - Χρόνος έναρξης της εκτέλεσης αλυσίδας εργασιών: $$startTime$$ \n - Χρόνος λήξης της εκτέλεσης αλυσίδας εργασιών: $$endTime$$ \n - Ονομα του χώρου: $$spaceId$$ \n
@deleteEmailRecepient=Διαγραφή Παραλήπτη
@emailInputDisabledText=Αναπτύξτε την αλυσίδα εργασιών για να προσθέσετε παραλήπτες email.
@tenantOwnerDomainMatchErrorText=Ο τομέας διεύθυνσης email δεν συμφωνεί με τον τομέα ιδιοκτήτη μισθωτή: {0}
@totalEmailIdLimitInfoText=Μπορείτε να επιιλέξετε μέχρι 20 παραλήπτες email συμπεριλαμβανομένων των χρηστών μέλους μισθωτή και άλλων παραληπτών.
@emailDomainInfoText=Μόνο οι διευθύνσεις email με τομέα: {0} είναι αποδεκτές.
@duplicateEmailErrorText=Υπάρχουν παραλήπτες ίδιων email στη λίστα.

#XFLD Zorder Title
@txtZorderTitle=Apache Spark Z-Order Στήλες

#XFLD Zorder NoColumn
@txtZorderNoColumn=Δεν Βρέθηκαν Στήλες Εντολής Ζ

#XFLD Zorder Column PrimaryKey
@txtZorderColumnPrimaryKey=Αρχικός Κωδικός

#XFLD
@lblOperators=Τελεστές
addNewSelector=Προσθήκη ως Νέα Εργασία
parallelSelector=Προσθήκη ως Παράλληλη Εργασία
replaceSelector=Αντικατάσταση Υπάρχουσας Εργασίας
addparallelbranch=Προσθήκη ως Παράλληλος Κλάδος
addplaceholder=Προσθήκη Συμβόλου
addALLOperation=Τελεστές ALL
addOROperation=Τελεστές ANY
addplaceholdertocanvas=Προσθήκη Συμβόλου σε Καμβά
addplaceholderonselected=Προσθήκη Συμβόλου μετά την Επιλεγμένη Εργασία
#XFLD: Add Parallel Branch to Selected Task
addparallelbranchonselected=Προσθήκη Παράλληλου Κλάδου μετά την Επιλεγμένη Εργασία
addOperator=Προσθήκη Χειριστή
txtAdd=Προσθήκη
txtPlaceHolderText=Επιλογή και Μεταφορά Εργασίας εδώ
@lblLayout=Διάταξη

#XMSG
VAL_UNCONNECTED_TASK=Η εργασία ''{0}'' δεν είναι συνδεδεμένη με την αλυσίδα εργασιών.
#XMSG
VAL_TASK_SHOULD_HAVE_ONE_INCOMING_LINK=Η εργασία ''{0}'' πρέπει να έχει μόνο έναν εισερχόμενο σύνδεσμο.
#XMSG
VAL_TASK_SHOULD_HAVE_INCOMING_LINK=Η εργασία ''{0}'' πρέπει να έχει έναν εισερχόμενο σύνδεσμο.
#XMSG
VAL_UNCONNECTED_OPERATOR=Τελεστής ''{0}'' δεν είναι συνδεδεμένος με την αλυσίδα εργασιών.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ATLEAST_TWO_INCOMING_LINK=Χειριστής ''{0}'' πρέπει να έχει τουλάχιστον δύο εισερχόμενους συνδέσμους.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ONE_OUT_LINK=Χειριστής ''{0}'' πρέπει να έχει τουλάχιστον έναν εξερχόμενο σύνδεσμο.
#XMSG
VAL_CIRCULAR_REFERENCE_EXITS=Κυκλικός βρόχος υπάρχει στην αλυσίδα εργασιών ''{0}''.
#XMSG
VAL_UNCONNECTED_BRANCH=Αντικείμενο/ κλάδος ''{0}'' δεν είναι συνδεδεμένο με την αλυσίδα εργασιών.
#XMSG
VAL_TASK_TRIGGERED_PARALLEL_MORE_THAN_ONE_TIME=Η εργασία ''{0}'' συνδέεται παράλληλα περισσότερες από μία φορές. Διαγράψτε διπλοεγγραφές για να συνεχίσετε.


txtBegin=Εναρξη
txtNodesInLink=Σχετικά Αντικείμενα
#XTOL Tooltip for a context button on diagram
openInNewTab=Ανοιγμα σε Νέα Καρτέλα
#XTOL Tooltip for a context button on diagram
txtConnectToTaskOperator=Σύρετε για Σύνδεση
@emailUpdateError=Σφάλμα στην ενημέρωση λίστας Ηλεκτρονικής Ειδοποίησης

#XMSG
noTeamPrivilegeTxt=Δεν έχετε άδεια για εμφάνιση λίστας μελών μισθωτή. Χρησιμοποιήστε την καρτέλα Διάφορα για να προσθέσετε παραλήπτες email μη αυτόματα.

#XFLD Package
@txtPackage=Πακέτο

#XMSG warning for repository package assignment changed. {1} - package name
VAL_PACKAGE_CHANGED=Αντιστοιχίσατε αυτό το αντικείμενο στο πακέτο ''{1}''. Πατήστε Αποθήκευση για να επιβεβαιώσετε και να επαληθεύσετε αυτή την αλλαγή. Η αντιστοίχιση σε ένα πακέτο δεν ανακαλείται σε αυτό τον επεξεργαστή μετά την αποθήκευση.
#XMSG warning for repository package dependency issue.
MISSING_DEPENDENCY=Οι εξαρτήσεις του αντικειμένου ''{0}'' δεν μπορούν να επιλυθούν στο πλαίσιο του πακέτου ''{1}''.

#Warning messages for the folders in the "Others" tab of task chain editor
#XMSG generic warning if getting the objects in a particular folder fails with unhandled errors
getNonRepoArtefactTypeDescendantsError=Υπήρχε πρόβλημα στην ανάκτηση αντικειμένων στον φάκελο που επιλέξατε.
#BW Process Chains folder related warning messages
#XMSG warning if user does not have the necessary permission related to the SAP BW Bridge space
missingPrivilegeForBridgeSpaceError=Δεν έχετε την απαραίτητη άδεια για προβολή ή συμπερίληψη των αλυσίδων διαδικασιών BW σε μία αλυσίδα εργασιών.
#XMSG warning if there was a problem retrieving the BW process chains due to bridge space membership, bridge connection issues or other errors
generalBridgeSpaceError=Υπηρχε σφάλμα στην ανάκτηση των αλυσίδων διαδικασιών BW.
#XMSG warning if the BW API for retrieving the BW process chains from the SAP BW Bridge tenant fails
bwApiClientError=Υπήρχε σφάλμα στην ανάκτηση των αλυσίδων διαδικασιών BW από τον μισθωτή SAP BW Bridge.
#XMSG warning if BW process chains list is empty
bwApiEmptyListError=Δεν βρέθηκαν αλυσίδες διαδικασιών BW στον μισθωτή SAP BW Bridge.
#XMSG: Task log message for invalid OIDC configuration in the SAP BW Bridge tenant
bwBridgeOIDCConfigStatusInvalid=OIDC δεν διαμορφώθηκε για αυτόν τον μισθωτή. Χρησιμοποιήστε το clientID "{0}" και το tokenURL "{1}" για να διαμορφώσετε το OIDC στον μισθωτή SAP BW bridge όπως περιγράφεται στο SAP note 3536298.
#XMSG BW Process Chain Object has been deleted
VAL_DELETED_BW=Οι παρακάτω αλυσίδες διαδικασιών BW διαγράφηκαν από τον μισθωτή SAP Bridge: {0}
#BW SQL Script Procedures folder related warning messages
#XFLD waring if the Procedure list is empty
procedureAPIEmptyList=Δεν δημιουργήθηκαν διαδικασίες ή το δικαίωμα ΕΚΤΕΛΕΣΗ δεν παραχωρήθηκε στο σχήμα Ανοικτού SQL.
#Change digram orientations
changeOrientations=Αλλαγή Προσανατολισμών


# placeholder for the API Path
apiPath=Για παράδειγμα: /job/v1
# placeholder for the status API Path
statusAPIPath=Για παράδειγμα: /job/v1/{id}/κατάσταση
# valueStateText for the API Path
apiPathRequired=Διαδρομή ΑΡΙ υποχρεωτική
#placeholder for the CSRF Token URL
csrfTokenURL=Μόνο HTTPS υποστηρίζεται
# Response Type 1
statusCode=Εμφάνιση αποτελέσματος από κωδικό κατάστασης HTTP 
# Response Type 2
locationHeader=Εμφάνιση αποτελέσματος από κωδικό κατάστασης HTTP και κεφαλίδα τοποθεσίας
# Response Type 3
responseBody=Εμφάνιση αποτελέσματος από κωδικό κατάστασης HTTP και κείμενο απάντησης
# placeholder for ID
idPlaceholder=Εισαγωγή Διαδρομής JSON
# placeholder for indicator value
indicatorValue=Εισάγετε τιμή
# Placeholder for key
keyPlaceholder=Εισαγωγή κωδικού
# Error message for missing key
KeyRequired=Κωδικός υποχρεωτικός
# Error message for invalid key format
invalidKeyFormat=Ο κωδικός κεφαλίδας που καταχωρίσατε δεν επιτρέπεται. Έγκυρες κεφαλίδες είναι:<ul><li>"προτιμάει"</li><li>Κεφαλίδες που αρχίζουν με "x-", εκτός από "x-forwarded-host"</li><li>Οι κεφαλίδες που περιέχουν αλφαριθμητικούς χαρακτήρες, "-", ή "_"</li></ul>
# Error message for missing value
valueRequired=Τιμή απαιτείται
# Error message for invalid characters in value
invalidValueCharacters=Η κεφαλίδα περιέχει άκυρους χαρακτήρες. Υποστηρίζονται οι εξής χαρακτήρες:\t ";", ":", "-", "_", ",", "?", "/", και "*"
# Validation message for invoke api path
apiPathValidation=Εισάγετε έγκυρη διαδρομή ΑΡΙ, για παράδειγμα: /job/v1
# Validation message for JSON path
jsonPathValidation=Εισάγετε έγκυρη διαδρομή JSON
# Validation message for success/error indicator
indicatorValueValidation=Ένδειξη ότι η τιμή πρέπει να αρχίζει με έναν αλφαριθμητικό χαρακτήρα και να περιλαμβάνει τους εξής ειδικούς χαρακτήρες: \t "-", και "_"
# Error message for JSON path
jsonPathRequired=Διαδρομή JSON υποχρεωτική
# Error message for invalid API Technical Name
invalidTechnicalName=Το τεχνικό όνομα περιέχει άκυρους χαρακτήρες
# Error message for empty Technical Name
emptyTechnicalName=Τεχνικό όνομα απαιτείται
# Tooltip for codeEditor dialog
codeEditorTooltip=Άνοιγμα παραθύρου Επεξεργασίας JSON
# Status Api path validation message
validationStatusAPIPath=Εισάγετε έγκυρη διαδρομή ΑΡΙ, για παράδειγμα: /job/v1/{id}/κατάσταση
# CSRF token URL validation message
validationCSRFTokenURL=Το URL Διακριτικού CSRF πρέπει να αρχίζει με 'https://' και να είναι έγκυρο URL
# Select a connection
selectConnection=Επιλογή Σύνδεσης
# Validation message for connection item error
connectionNotReplicated=Η σύνδεση δεν είναι έγκυρη ώστε να εκτελούνται εργασίες ΑΡΙ. Ανοίξτε την εφαρμογή "Συνδέσεις" και επαναεισάγετε τα διαπιστευτήριά σας για να αποκαταστήσετε την σύνδεση ΗΤΤΡ
# Validation message for API task properties wrong value or missing
VAL_REST_API_PROPERTIES_MISSING=Η εργασία API "{0}" έχει λανθασμένες τιμές ή λείπουν τιμές για τις παρακάτω ιδιότητες: {1}
# Validation message for API connection api is throwing some error or selected connection has some warning
VAL_REST_API_CONNECTION=Η εργασία API "{0}" έχει πρόβλημα με την σύνδεση ΗΤΤΡ. Ανοίξτε την εφαρμογή "Συνδέσεις" και ελέγξτε την σύνδεση
# Validation message for API task properties when delected connection
VAL_REST_API_CONNECTION_DELETED=Η εργασία ΑΡΙ "{0}" έχει μία διαγραμμένη "{1}" σύνδεση
# Validation message for API task properties when connection cannot be consumed in the API task
VAL_REST_API_CONNECTION_CONSUMPTION_WARNING=Η εργασία ΑΡΙ "{0}" έχει μία σύνδεση "{1}" που δεν μπορεί να χρησιμοποιηθεί για εκτέλεση εργασιών ΑΡΙ. Ανοίξτε την εφαρμογή "Συνδέσεις" και εισάγετε ξανά τα διαπιστευτήρια για να δημιουργήσετε συνδεσιμότητα με εργασίες ΑΡΙ
# Information message if synchrnous mode is selected in the API Invocation then we don't need status information
modeInfoText=Στην Σύγχρονη λειτουργία, ο πίνακας κατάστασης δεν εμφανίζεται για κλήσεις ΑΡΙ
# validation dialog button for Run API Test
saveAnyway=Αποθήκευση
# validation message for technical name
technicalNameValidation=Το τεχνικό όνομα πρέπει να είναι μοναδικό στην αλυσίδα εργασιών. Επιλέξτε άλλο τεχνικό όνομα
# Connection error message
getHttpConnectionsError=Αδύνατη εμφάνιση συνδέσεων ΗΤΤΡ
# Msg for the API test run when the Task chain is not saved
taskChainNotSaved=Η αλυσίδα εργασιών πρέπει να αποθηκευτεί πριν την εκτέλεση της δοκιμαστικής εκτέλεσης ΑΡΙ
# Msg failed to run API test run
@failedToRunAPI=Αδύνατη εκτέλεση της δοκιμαστικής εκτέλεσης ΑΡΙ
# Msg for the API test run when its already in running state
apiTaskRunning=Δοκιμαστική εκτέλεση ΑΡΙ είναι ήδη σε εξέλιξη. Επιθυμείτε έναρξη νέας δοκιμαστικής εκτέλεσης;

topToBtm=Πάνω-Κάτω
leftToRight=Αριστερά-Δεξιά

#XTIT Apache Spark Application Settings
txtHeaderApacheSettings=Ρυθμίσεις για Εφαρμογή Apache Spark 
#XFLD Use Default
txtUseSpaceDefault=Χρήση Προεπιλογής
#XFLD Application
txtApplication=Εφαρμογή
#XFLD Define new settings for this Task
txtNewSettings=Καθορισμός νέας ρύθμισης για αυτή την εργασία

#XFLD Use Default
txtUseDefault=Χρήση Προεπιλογής




