#XTOL Undo
@undo=Поништи
#XTOL Redo
@redo=Понови
#XTOL Delete Selected Symbol
@deleteNode=Избриши одабрани симбол
#XTOL Zoom to Fit
@zoomToFit=Увећај до уклапања
#XTOL Auto Layout
@autoLayout=Аутоматски изглед
#XMSG
@welcomeText=Превуците и испустите објекте с левог панела на ово платно.
#XMSG
@txtNoData=Изгледа да још нисте додали објекат.
#XMSG
VAL_ENTER_VALID_STRING_GEN=Унесите важећи низ дужине {0} или краћи.
#XMSG
@noParametersMsg=Поступак нема параметре уноса.
#XMSG
@ip_enterValueMsg="{0}" (Покрени поступак SQL Script-а) има параметре уноса. Можете поставити вредност за сваки од њих.
#XTOL
@validateModel=Поруке валидације
#XTOL
@hierarchy=Хијерархија
#XTOL
@columnCount=Број колона
#XFLD
@yes=Да
#XFLD
@no=Не
#XTIT Save Dialog param
@modelNameTaskChain=Ланац задатака
#properties panel
@lblPropertyTitle=Својства
#XFLD
@lblGeneral=Опште
#XFLD : Setting
@lblSetting=Подешавања
#XTXT: Delta Enabled Local Table Deletion
txtDeltaTableDeletion=Избришите све потпуно обрађене записе с типом промене "Избрисано" старије од
#XTXT: Delta Enabled Local Table Deletion Input field description
desDeltaTableDeletion=Дани
#XFLD: Data Activation label
@lblDataActivation=Активирање података
#XFLD: Latency label
@latency=Латентност
#XTEXT: Text for Latency dropdown
txtLatencyDefault=Стандард
#XTEXT: Text 1 hour
txtOneHour=1 сат
#XTEXT: Text 2 hours
txtTwoHours=2 сата
#XTEXT: Text 3 hours
txtThreeHours=3 сата
#XTEXT: Text 4 hours
txtFourHours=4 сата
#XTEXT: Text 6 hours
txtSixHours=6 сати
#XTEXT: Text 12 hours
txtTwelveHours=12 сати
#XTEXT: Text 1 day
txtOneDay=1 дан
#XFLD: Latency label
@autoRestartHead=Аутоматско поновно покретање
#XFLD
@lblConnectionName=Веза
#XFLD
@lblQualifiedName=Квалификовани назив
#XFLD
@lblSpaceName=Назив простора
#XFLD
@lblLocalSchemaName=Локална шема
#XFLD
@lblType=Тип објекта
#XFLD
@lblActivity=Активност
#XFLD
@lblTableName=Назив табеле
#XFLD
@lblBusinessName=Пословни назив
#XFLD
@lblTechnicalName=Технички назив
#XFLD
@lblSpace=Простор
#XFLD
@lblLabel=Ознака
#XFLD
@lblDataType=Тип података
#XFLD
@lblDescription=Опис
#XFLD
@lblStorageType=Складиште
#XFLD
@lblHTTPConnection=Генеричка HTTP веза
#XFLD
@lblAPISettings=Генеричка подешавања API-ја
#XFLD
@header=Заглавља
#XFLD
@lblInvoke=Позивање API-ја
#XFLD
@lblMethod=Метод
#XFLD
@lblUrl=Основни URL
#XFLD
@lblAPIPath=Пут API-ја
#XFLD
@lblMode=Начин
#XFLD
@lblCSRFToken=Захтевај токен CSRF 
#XFLD
@lblTokenURL=URL CSRF токена
#XFLD
@csrfTokenInfoText=Ако није унет, користиће се основни URL и пут API-ја
#XFLD
@lblCSRF=Токен CSRF 
#XFLD
@lblRequestBody=Захтевај главни део
#XFLD
@lblFormat=Форматирај
#XFLD
@lblResponse=Одговор
#XFLD
@lblId=ID ѕа позивање статуса
#XFLD
@Id=ID
#XFLD
@lblSuccessIndicator=Показатељ успеха
#XFLD
@lblErrorIndicator=Показатељ грешке
#XFLD
@lblErrorReason=Разлог за грешку
#XFLD
@lblStatus=Статус
#XFLD
@lblApiTestRun=Пробно извођење API-ја
#XFLD
@lblRunStatus=Статус извођења
#XFLD
@lblLastRan=Последњи пут изведено
#XFLD
@lblTestRun=Пробно извођење
#XFLD
@lblDefaultHeader=Стандардна поља заглавља (парови кључ-вредност)
#XFLD
@lblAdditionalHeader=Додатно поље заглавља
#XFLD
@lblKey=Кључ
#XFLD
@lblEditJSON=Уреди JSON
#XFLD
@lblTasks=Задаци
#XFLD
@lblRESTfullTask=API
#XFLD: add field button text
btnAddField=Додај поље заглавља
#XFLD: view Details link
@viewDetails=Прикажи детаље
#XTOL
tooltipTxt=Више
#XTOL
delete=Избриши
#XBTN: ok button text
btnOk=ОК
#XBTN: cancel button text
btnCancel=Одустани
#XBTN: save button text
btnSave=Сачувај
#XMSG Error: Object has duplicated technical name.
VAL_OBJECT_DUPLICATE_NAME=Објекат "{0}" већ постоји у репозиторијуму. Унесите други назив.
#XMSG: loading message while opening task chain
loadTaskChain=Учитавање ланца задатака...
#model properties
#XFLD
@status_panel=Статус извођења
#XFLD
@deploy_status_panel=Статус имплементације
#XFLD
@status_lbl=Статус
#XFLD
@lblLastExecuted=Последње извођење
#XFLD
@lblNotExecuted=Још није изведено
#XFLD
@lblNotDeployed=Није имплементирано
#XFLD
errorDetailsTxt=Није могуће позвати статус извођења
#XBTN: Schedule dropdown menu
SCHEDULE=Планирај
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Уреди план
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Избриши план
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Креирај план
#XLNK
viewDetails=Прикажи детаље
#XMSG: error message for reading execution status from backend
backendErrorMsg=Изгледа да се подаци тренутно не учитавају са сервера. Покушајте поново да позовете податке.
#XFLD: Status text for Completed
@statusCompleted=Завршено
#XFLD: Status text for Running
@statusRunning=Изводи се
#XFLD: Status text for Failed
@statusFailed=Није успело
#XFLD: Status text for Stopped
@statusStopped=Заустављено
#XFLD: Status text for Stopping
@statusStopping=Зауставља се
#XFLD
@LoaderTitle=Учитава се
#XFLD
#XFLD: Deploy status, dataflow has  been deployed / deployment date exists
@deployStatusActive=Имплементирано
@deployStatusRevised=Локална ажурирања
@deployStatusFailed=Није успело
@deployStatusPending=Имплементација...
@LoaderText=Позивање детаља са сервера
#XMSG
@msgDetailFetchError=Грешка при позивању детаља са сервера
#XFLD
@executeError=Грешка
#XFLD
@executeWarning=Упозорење
#XMSG
@executeConfirmDialog=Информације
#XMSG
@executeunsavederror=Сачувајте ланац задатака пре извођења.
#XMSG
@executemodifiederror=Постоје несачуване промене у ланцу задатака. Сачувајте их.
#XMSG
@executerunningerror=Ланац задатака се тренутно изводи. Сачекајте да се тренутно извођење заврши пре него што покренете ново.
#XMSG
@btnExecuteAnyway=Ипак изведи
#XMSG
@msgExecuteWithValidations=Ланац задатака има грешке валидације. Извођење ланца задатака може довести до грешке.
#XMSG
@msgRunDeployedVersion=Постоје промене за имплементацију. Биће покренута последња имплементирана верзија ланца задатака. Да ли желите да наставите?
#XMSG
#XMSG
@navToMonitoring=Отвори у монитору ланца задатака
#XMSG
txtOR=ИЛИ
#XFLD
@preview=Претходно прикажи
#XMSG
txtand=и
#NOTR
@dateFormat=yyyy-MM-dd
#NOTR
@dateTimeFormatFilter=yyyy-MM-dd HH:mm:ss.
#NOTR
@timeFormat=HH:mm:ss
@lblColum=Колона
#XFLD
@lblCondition=Услов
#XFLD
@lblValue=Вредност
#XMSG
@msgJsonInvalid=Ланац задатака се не може сачувати јер постоје грешке у JSON-у. Проверите и разрешите.
#XTIT
@msgSaveFailTitle=Неважећи JSON.
#XMSG
NOT_CHAINABLE=Објекат "{0}" се не може додати у ланац задатака.
#XMSG
NOT_CHAINABLE_REMOTETABLE=Објекат "{0}": тип репликације биће промењен.
#XMSG
searchTaskChain=Тражи објекте

#XFLD
@txtTaskChain=Ланац задатака
#XFLD
@txtRemoteTable=Удаљена табела
#XFLD
@txtRemoveData=Уклони реплициране податке
#XFLD
@txtRemovePersist=Уклони трајно сачуване податке
#XFLD
@txtView=Прикажи
#XFLD
@txtDataFlow=Ток података
#XFLD
@txtIL=Паметно тражење
#XFLD
@txtTransformationFlow=Ток трансформације
#XFLD
@txtReplicationFlow=Ток репликације
#XFLD
@txtDeltaLocalTable=Локална табела
#XFLD
@txtBWProcessChain=Ланац процеса BW
#XFLD
@txtSQLScriptProcedure=Поступак SQL Script-а
#XFLD
@txtAPI=API
#XFLD
@txtMerge=Спој
#XFLD
@txtOptimize=Оптимизирај
#XFLD
@txtVacuum=Избриши записе

#XFLD
@txtRun=Изведи
#XFLD
@txtPersist=Трајно сачувај
#XFLD
@txtReplicate=Реплицирај
#XFLD
@txtDelete=Избриши записе с типом промене "Избрисано"
#XFLD
@txtRunTC=Изведите ланац задатака
#XFLD
@txtRunBW=Покрени ланац процеса BW
#XFLD
@txtRunSQLScriptProcedure=Покрени поступак SQL Script-а

#XFLD
@txtRunDataFlow=Изведи ток података
#XFLD
@txtPersistView=Трајно сачувај поглед
#XFLD
@txtReplicateTable=Реплицирај табелу
#XFLD
@txtRunIL=Покрени паметно тражење
#XFLD
@txtRunTF=Покрени ток трансформације
#XFLD
@txtRunRF=Покрени ток репликације
#XFLD
@txtRemoveReplicatedData=Уклони реплициране податке
#XFLD
@txtRemovePersistedData=Уклони трајно сачуване податке
#XFLD
@txtMergeData=Спој
#XFLD
@txtOptimizeData=Оптимизирај
#XFLD
@txtVacuumData=Избриши записе
#XFLD
@txtRunAPI=Изведи API

#XFLD storage type text
hdlfStorage=Фајл

@statusNew=Није имплементирано
@statusActive=Имплементирано
@statusRevised=Локална ажурирања
@statusPending=Имплементација...
@statusChangesToDeploy=Промене за имплементацију
@statusDesignTimeError=Грешка времена дизајна
@statusRunTimeError=Грешка времена извођења

#XTIT
txtNodes=Објекти у ланцу задатака ({0})
#XBTN
@deleteNodes=Избриши

#XMSG
@txtDropDataToDiagram=Превуците и испустите објекте у дијаграм.
#XMSG
@noData=Нема објеката

#XFLD
@txtTaskPosition=Позиција задатка

#input parameters and variables
#XFLD
lblInputParameters=Параметри уноса
#XFLD
ip_name=Назив
#XFLD
ip_value=Вредност
#XTEXT
@noObjectsFound=Објекти нису нађени

#XMSG
@msgExecuteSuccess=Извођење ланца задатака је покренуто.
#XMSG
@msgExecuteFail=Није успело извођење ланца задатака.
#XMSG
@msgDeployAndRunSuccess=Имплементација и извођење ланца задатака су покренути.
#XMSG
@msgDeployAndRunFail=Није успело имплементирање и извођење ланца задатака.
#XMSG
@titleExecuteBusy=Сачекајте.
#XMSG
@msgExecuteBusy=Припремамо ваше податке за покретање извођења ланца задатака.
#XMSG
@msgAPITestRunSuccess=Пробно извођење API-ја је покренуто.
#XMSG
@msgAPIExecuteBusy=Припремамо ваше податке за покретање пробног извођења API-ја.

#XTOL
txtOpenInEditor=Отвори у уређивачу
#XTOL
txtPreviewData=Претходно прикажи податке

#datapreview
#XMSG
@msgDataPreviewNotSupp=Претходни приказ података није доступан за овај објекат.

#XMSG Error: empty model
VAL_MODEL_EMPTY=Изгледа да је ваш модел празан. Додајте објекте.
#XMSG Error: deploy model
@msgDeployBeforeRun=Морате имплементирати ланац задатака пре него што га изведете.
#BTN: close dialog
btnClose=Затвори

#XMSG Object is not Deployed
VAL_NOT_DEPLOYED=Објекат "{0}" се мора имплементирати да би се наставио ланац задатака.
#XMSG Object is having runtime error
VAL_RUNTIME_ERROR=Објекат "{0}" враћа грешку времена извођења. Проверите објекат.
#XMSG Object is having design time error
VAL_DESIGNTIME_ERROR=Објекат "{0}" враћа грешку времена дизајна. Проверите објекат.
#XMSG Procedure Object has been deleted
VAL_DELETED_PROCEDURE=Следећи поступци су избрисани: {0}
#XMSG New Parameter added to Procedure object
VAL_NEW_PARAMETER_ADDED=Нови параметри додати поступку "{1}": "{0}". Поново имплементирајте ланац задатака.
#XMSG Parameter removed from Procedure object
VAL_PARAMETER_DELETED=Параметри уклоњени из поступка "{1}": "{0}". Поново имплементирајте ланац задатака.
#XMSG Parameter data type changed in Procedure object
VAL_PARAMETER_DATATYPE_MODIFIED=Тип података параметра "{0}" промењен из "{1}" у "{2}" у поступку "{3}".
#XMSG Parameter length changed of specific datatype in Procedure object
VAL_PARAMETER_LENGTH_MODIFIED=Дужина параметра "{0}" промењена из "{1}" у "{2}" у поступку "{3}".
#XMSG Parameter precision changed of Decimal datatype in Procedure object
VAL_PARAMETER_PRECISION_MODIFIED=Прецизност параметра "{0}" промењена из "{1}" у "{2}" у поступку "{3}".
#XMSG Parameter scale changed of Decimal datatype in Procedure object
VAL_PARAMETER_SCALE_MODIFIED=Скала параметра "{0}" промењена из "{1}" у "{2}" у поступку "{3}".
#XMSG Parameter value not provided in Procedure object
VAL_SQL_SCRIPT_PROCEDURE_PARAMETER_VALUE_MISSING=Нисте унели вредности за параметре уноса који су обавезни за поступак "{0}": "{1}"
#XMSG Remote Table activity changes to Remove Replicated Data
VAL_REMOVE_REPLICATED_DATA=Следеће извођење ланца задатака ће променити тип приступа подацима и подаци се више неће преносити на сервер у реалном времену.
#XMSG Remote Table activity changes
REPLICATION_TYPE_CHANGED=Објекат "{0}": Тип репликације биће промењен.

#XFLD
@lblStartNode=Почетни чвор
#XFLD
@lblEndNode=Завршни чвор
#XFLD
@linkTo={0} до {1}
#XTOL
@txtViewDetails=Прикажи детаље

#XTOL
txtOpenImpactLineage=Анализа учинка и порекла
#XFLD
@emailNotifications=Обавештења е-поштом
#XFLD
@txtReset=Поново постави
#XFLD
@emailMsgWarning=Стандардни шаблон е-поште биће послат када порука е-поште буде празна
#XFLD
@notificationSettings=Подешавања обавештења
#XFLD
@recipientEmailAddr=Адреса примаоца е-поште
#XFLD
@emailSubject=Предмет е-поште
@emailSubjectText=Ланац задатака <TASKCHAIN_NAME> завршен са статусом <STATUS>
#XFLD
@emailMessage=Порука е-поште
@emailMessageText=Поштовани корисниче,\n\n Овим вас обавештавамо да је ланац задатака:<TASKCHAIN_NAME> изведен у <START_TIME> завршен са статусом <STATUS>. Извршење је завршено у <END_TIME>.\n\nДетаљи:\nПростор:<SPACE_NAME>\nГрешка:<ERROR_MESSAGE>
@selectEmailRecipientDialogTitle=Одабери адресу примаоца е-поште
@tenantMembers=Елементи клијента
@others=Други ({0})
@selectedEmailAddress=Одабрани примаоци
@add=Додај
@placeholder=Резервисано место
@description=Опис
@copyText=Копирај текст
@taskchainDetailsPlaceholder=Резервисана места за детаље ланца задатака
@placeholderCopied=Резервисано место је копирано
@invalidEmailInfo=Унесите тачну адресу е-поште
@maxMembersAlreadyAdded=Већ сте додали максималних 20 елемената
@enterEmailAddress=Унесите адресу е-поште
@inCorrectPlaceHolder={0} није очекивано резервисано место у главном делу е-поште.
@nsOFF=Немој слати обавештења
@nsFAILED=Пошаљи обавештење е-поштом само када се извођење заврши с грешком
@nsCOMPLETED=Пошаљи обавештење е-поштом само када се извођење успешно заврши
@nsANY=Пошаљи е-пошту када се извођење заврши
@phStatus=Статус ланца задатака - УСПЕШНО|НЕУСПЕШНО
@phTaskChainTName=Технички назив ланца задатака
@phTaskChainBName=Пословни назив ланца задатака
@phLogId=ID протокола извођења
@phUser=Корисник који изводи ланац задатака
@phLogUILink=Веза с приказом протокола ланца задатака
@phStartTime=Време почетка извођења
@phEndTime=Време завршетка извођења
@phErrMsg=Прва порука о грешци у протоколу задатка. Протокол је празан у случају УСПЕХА
@phSpaceName=Технички назив простора
@emailFormatError=Неважећи формат е-поште
@emailFormatErrorInListText=Неважећи формат е-поште унет у листу елемената који нису клијенти.
@emailSubjectTemplateText=Обавештење за ланац задатака: $$taskChainName$$ - простор: $$spaceId$$ - статус: $$статус$$
@emailMessageTemplateText=Поштовани,\n\n Ваш ланац задатака означен $$taskChainName$$ завршен је са статусом $$status$$. \n Ово су још неки детаљи о ланцу задатака:\n - технички назив ланца задатака: $$taskChainName$$ \n - ID протокола извођења ланца задатака: $$logId$$ \n - корисник који је извео ланац задатака: $$user$$ \n - веза за приказ протокола ланца задатака: $$uiLink$$ \n - време почетка извођења ланца задатака: $$startTime$$ \n - време завршетка извођења ланца задатака: $$endTime$$ \n - назив простора: $$spaceId$$ \n
@deleteEmailRecepient=Избриши примаоца
@emailInputDisabledText=Имплементирајте ланац задатака за додавање прималаца е-поште.
@tenantOwnerDomainMatchErrorText=Домен адресе е-поште не одговара домену одговорног лица клијента: {0}
@totalEmailIdLimitInfoText=Можете одабрати до 20 прималаца е-поште укључујући кориснике елемента клијента и друге примаоце.
@emailDomainInfoText=Прихваћене су само адресе е-поште с доменом: {0}
@duplicateEmailErrorText=На листи постоје дупли примаоци е-поште.

#XFLD Zorder Title
@txtZorderTitle=Apache Spark - колоне Z-редоследа

#XFLD Zorder NoColumn
@txtZorderNoColumn=Нису нађене колоне Z-редоследа

#XFLD Zorder Column PrimaryKey
@txtZorderColumnPrimaryKey=Примарни кључ

#XFLD
@lblOperators=Оператори
addNewSelector=Додај као нови задатак
parallelSelector=Додај као паралелни задатак
replaceSelector=Замени постојећи задатак
addparallelbranch=Додај као паралелну грану
addplaceholder=Додај резервисано место
addALLOperation=Оператор ALL
addOROperation=Оператор ANY
addplaceholdertocanvas=Додај резервисано место на платно
addplaceholderonselected=Додај резервисано место након одабраног задатка
#XFLD: Add Parallel Branch to Selected Task
addparallelbranchonselected=Додај паралелну грану након одабраног задатка
addOperator=Додај оператор
txtAdd=Додај
txtPlaceHolderText=Овде превуците и испустите задатак
@lblLayout=Изглед

#XMSG
VAL_UNCONNECTED_TASK=Задатак "{0}" није повезан с ланцем задатака.
#XMSG
VAL_TASK_SHOULD_HAVE_ONE_INCOMING_LINK=Задатак "{0}" треба да има само једну улазну везу.
#XMSG
VAL_TASK_SHOULD_HAVE_INCOMING_LINK=Задатак "{0}" треба да има једну улазну везу.
#XMSG
VAL_UNCONNECTED_OPERATOR=Оператор "{0}" није повезан с ланцем задатака.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ATLEAST_TWO_INCOMING_LINK=Оператор ''{0}'' треба да има најмање две улазне везе.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ONE_OUT_LINK=Оператор "{0}" треба да има најмање једну излазну везу.
#XMSG
VAL_CIRCULAR_REFERENCE_EXITS=Кружна петља постоји у ланцу задатака "{0}".
#XMSG
VAL_UNCONNECTED_BRANCH=Објекат/грана "{0}" нису повезани с ланцем задатака.
#XMSG
VAL_TASK_TRIGGERED_PARALLEL_MORE_THAN_ONE_TIME=Задатак "{0}" паралелно повезан више пута. Уклоните дупликате да бисте наставили.


txtBegin=Почни
txtNodesInLink=Укључени објекти
#XTOL Tooltip for a context button on diagram
openInNewTab=Отвори у новој картици
#XTOL Tooltip for a context button on diagram
txtConnectToTaskOperator=Превуци за повезивање
@emailUpdateError=Грешка у ажурирању листе обавештења е-поштом

#XMSG
noTeamPrivilegeTxt=Немате дозволу да прикажете листу елемента клијента. Користите картицу Друго да ручно додате примаоце е-поште.

#XFLD Package
@txtPackage=Пакет

#XMSG warning for repository package assignment changed. {1} - package name
VAL_PACKAGE_CHANGED=Доделили сте овај објекат пакету "{1}". Кликните на Сачувај да бисте потврдили и вредновали ову промену. Узмите у обзир да доделу пакету није могуће поништити у овом уређивачу након снимања.
#XMSG warning for repository package dependency issue.
MISSING_DEPENDENCY=Зависности објекта "{0}" не могу се решити у контексту пакета "{1}".

#Warning messages for the folders in the "Others" tab of task chain editor
#XMSG generic warning if getting the objects in a particular folder fails with unhandled errors
getNonRepoArtefactTypeDescendantsError=Проблем при позивању објеката у фолдеру који сте одабрали.
#BW Process Chains folder related warning messages
#XMSG warning if user does not have the necessary permission related to the SAP BW Bridge space
missingPrivilegeForBridgeSpaceError=Немате неопходну дозволу за приказ или укључивање процеса BW у ланац задатака.
#XMSG warning if there was a problem retrieving the BW process chains due to bridge space membership, bridge connection issues or other errors
generalBridgeSpaceError=Проблем при позивању ланаца процеса BW.
#XMSG warning if the BW API for retrieving the BW process chains from the SAP BW Bridge tenant fails
bwApiClientError=Проблем при позивању ланаца процеса из клијента премошћавања апликације SAP BW.
#XMSG warning if BW process chains list is empty
bwApiEmptyListError=Ланци процеса BW нису нађени у клијенту премошћавања апликације SAP BW.
#XMSG: Task log message for invalid OIDC configuration in the SAP BW Bridge tenant
bwBridgeOIDCConfigStatusInvalid=Потврда идентитета OpenID није конфигурисана за овај клијент. Користите clientID "{0}" и tokenURL "{1}" за конфигурацију потврде идентитета OpenID у клијенту Премошћавања апликације SAP BW као што је описано у SAP белешци 3536298.
#XMSG BW Process Chain Object has been deleted
VAL_DELETED_BW=Следећи ланци процеса BW су можда избрисани из клијента SAP Bridge: {0}
#BW SQL Script Procedures folder related warning messages
#XFLD waring if the Procedure list is empty
procedureAPIEmptyList=Поступци нису креирани или привилегија ИЗВРШИ није додељена Open SQL шеми.
#Change digram orientations
changeOrientations=Промени оријентације


# placeholder for the API Path
apiPath=На пример: /job/v1
# placeholder for the status API Path
statusAPIPath=На пример: /job/v1/{id}/status
# valueStateText for the API Path
apiPathRequired=Пут API-ја је обавезан
#placeholder for the CSRF Token URL
csrfTokenURL=Подржани су само HTTPS
# Response Type 1
statusCode=Позови резултат из шифре статуса HTTP 
# Response Type 2
locationHeader=Позови резултат из шифре статуса HTTP и заглавља локације 
# Response Type 3
responseBody=Позови резултат из шифре статуса HTTP и главног дела одговора
# placeholder for ID
idPlaceholder=Унеси пут JSON 
# placeholder for indicator value
indicatorValue=Унеси вредност
# Placeholder for key
keyPlaceholder=Унеси кључ
# Error message for missing key
KeyRequired=Кључ је обавезан
# Error message for invalid key format
invalidKeyFormat=Кључ заглавља који сте унели није дозвољен. Важећа заглавља су:<ul><li>"prefer"</li><li>Заглавља која почињу са "x-", осим "x-forwarded-host"</li><li>Заглавља која садрже алфанумеричке знакове, "-", или "_"</li></ul>
# Error message for missing value
valueRequired=Вредност је обавезна
# Error message for invalid characters in value
invalidValueCharacters=Заглавље садржи неважеће знакове. Посебни знакови који су дозвољени су:\t ";", ":", "-", "_", ",", "?", "/", и "*"
# Validation message for invoke api path
apiPathValidation=Унесите важећи пут API-ја, на пример: /job/v1
# Validation message for JSON path
jsonPathValidation=Унесите важећи пут JSON 
# Validation message for success/error indicator
indicatorValueValidation=Вредност показатеља мора почети са алфанумеричким знаком и може укључивати следеће посебне знакове:\t "-", и "_"
# Error message for JSON path
jsonPathRequired=Пут JSON је обавезан
# Error message for invalid API Technical Name
invalidTechnicalName=Технички назив садржи неважеће знакове
# Error message for empty Technical Name
emptyTechnicalName=Технички назив је обавезан
# Tooltip for codeEditor dialog
codeEditorTooltip=Отвори прозор за уређивање JSON 
# Status Api path validation message
validationStatusAPIPath=Унесите важећи пут API-ја, на пример: /job/v1/{id}/status
# CSRF token URL validation message
validationCSRFTokenURL=URL токена CSRF мора почети са 'https://' и бити важећи URL
# Select a connection
selectConnection=Одабери везу
# Validation message for connection item error
connectionNotReplicated=Веза је тренутно неважећа за извођење задатака API-ја. Отворите апликацију "Везе" и поново унесите своје акредитиве за поправљање HTTP везе
# Validation message for API task properties wrong value or missing
VAL_REST_API_PROPERTIES_MISSING=Задатак API-ја "{0}" има нетачне вредности или вредности које недостају за следећа својства: {1}
# Validation message for API connection api is throwing some error or selected connection has some warning
VAL_REST_API_CONNECTION=Задатак API-ја "{0}" има проблем HTTP везе. Отоврите апликацију "Везе" и проверите везу
# Validation message for API task properties when delected connection
VAL_REST_API_CONNECTION_DELETED=Задатак API-ја "{0}" има избрисану везу "{1}"
# Validation message for API task properties when connection cannot be consumed in the API task
VAL_REST_API_CONNECTION_CONSUMPTION_WARNING=Задатак API-ја "{0}" има везу "{1}" која се не може користити за извођење задатка API-ја. Отворите апликацију „Везе” и поново унесите податке приступа да бисте успоставили везу са задацима API-ја
# Information message if synchrnous mode is selected in the API Invocation then we don't need status information
modeInfoText=У синхроном начину, панел статуса није приказан за позивања API-ја
# validation dialog button for Run API Test
saveAnyway=Ипак сачувај
# validation message for technical name
technicalNameValidation=Технички назив мора бити јединствен у оквиру ланца задатака. Изаберите други технички назив
# Connection error message
getHttpConnectionsError=Није успело позивање HTTP везе
# Msg for the API test run when the Task chain is not saved
taskChainNotSaved=Ланац задатака се мора сачувати пре покретања пробног извођења API-ја
# Msg failed to run API test run
@failedToRunAPI=Извођење пробног извођења API-ја није успело
# Msg for the API test run when its already in running state
apiTaskRunning=Пробно извођење API-ја је већ у току. Да ли желите да покренете ново пробно извођење?

topToBtm=Од горе надоле
leftToRight=Слева надесно

#XTIT Apache Spark Application Settings
txtHeaderApacheSettings=Подешавања апликације Apache Spark
#XFLD Use Default
txtUseSpaceDefault=Користи стандардну вредност
#XFLD Application
txtApplication=Апликација
#XFLD Define new settings for this Task
txtNewSettings=Дефиниши нова подешавања за овај задатак

#XFLD Use Default
txtUseDefault=Користи стандардну вредност




