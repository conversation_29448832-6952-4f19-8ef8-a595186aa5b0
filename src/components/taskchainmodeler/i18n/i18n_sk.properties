#XTOL Undo
@undo=Späť
#XTOL Redo
@redo=Znovu
#XTOL Delete Selected Symbol
@deleteNode=Odstrániť vybraný symbol
#XTOL Zoom to Fit
@zoomToFit=Prispôsobiť zobrazenie
#XTOL Auto Layout
@autoLayout=Automatické rozloženie
#XMSG
@welcomeText=Myšou presuňte svoje objekty z ľavého panela do tejto pracovnej oblasti.
#XMSG
@txtNoData=Zdá sa, že ste ešte nepridali žiadne objekty.
#XMSG
VAL_ENTER_VALID_STRING_GEN=Zadajte platný reťazec s dĺžkou menšou alebo rovnou {0}.
#XMSG
@noParametersMsg=Tento postup nemá žiadne vstupné parametre.
#XMSG
@ip_enterValueMsg=The "{0}" (Run SQL Script Procedure) má vstupné parametre. Môžete nastaviť hodnotu každ<PERSON>ho z nich.
#XTOL
@validateModel=Hlásenia overenia
#XTOL
@hierarchy=Hierarchia
#XTOL
@columnCount=Počet stĺpcov
#XFLD
@yes=Áno
#XFLD
@no=Nie
#XTIT Save Dialog param
@modelNameTaskChain=Reťazec úloh
#properties panel
@lblPropertyTitle=Vlastnosti
#XFLD
@lblGeneral=Všeobecne
#XFLD : Setting
@lblSetting=Nastavenia
#XTXT: Delta Enabled Local Table Deletion
txtDeltaTableDeletion=Odstráňte všetky úplne spracované záznamy s typom zmeny „Odstránené“, ktoré sú staršie ako
#XTXT: Delta Enabled Local Table Deletion Input field description
desDeltaTableDeletion=Dni
#XFLD: Data Activation label
@lblDataActivation=Aktivácia údajov
#XFLD: Latency label
@latency=Latencia
#XTEXT: Text for Latency dropdown
txtLatencyDefault=Predvolené
#XTEXT: Text 1 hour
txtOneHour=1 hodina
#XTEXT: Text 2 hours
txtTwoHours=2 hodiny
#XTEXT: Text 3 hours
txtThreeHours=3 hodiny
#XTEXT: Text 4 hours
txtFourHours=4 hodiny
#XTEXT: Text 6 hours
txtSixHours=6 hodín
#XTEXT: Text 12 hours
txtTwelveHours=12 hodín
#XTEXT: Text 1 day
txtOneDay=1 deň
#XFLD: Latency label
@autoRestartHead=Automatický reštart
#XFLD
@lblConnectionName=Pripojenie
#XFLD
@lblQualifiedName=Kvalifikovaný názov
#XFLD
@lblSpaceName=Názov priestoru
#XFLD
@lblLocalSchemaName=Lokálna schéma
#XFLD
@lblType=Typ objektu
#XFLD
@lblActivity=Aktivita
#XFLD
@lblTableName=Názov tabuľky
#XFLD
@lblBusinessName=Podnikový názov
#XFLD
@lblTechnicalName=Technický názov
#XFLD
@lblSpace=Priestor
#XFLD
@lblLabel=Označenie
#XFLD
@lblDataType=Typ údajov
#XFLD
@lblDescription=Popis
#XFLD
@lblStorageType=Ukladací priestor
#XFLD
@lblHTTPConnection=Generické pripojenie HTTP
#XFLD
@lblAPISettings=Generické nastavenia API
#XFLD
@header=Hlavičky
#XFLD
@lblInvoke=Vyvolanie API
#XFLD
@lblMethod=Metóda
#XFLD
@lblUrl=Základná adresa URL
#XFLD
@lblAPIPath=Cesta API
#XFLD
@lblMode=Režim
#XFLD
@lblCSRFToken=Vyžiadať token CSRF
#XFLD
@lblTokenURL=URL tokenu CSRF
#XFLD
@csrfTokenInfoText=Ak nie je zadaná, použije sa základná adresa URL a cesta rozhrania API
#XFLD
@lblCSRF=Token CSRF
#XFLD
@lblRequestBody=Hlavná časť požiadavky
#XFLD
@lblFormat=Formátovať
#XFLD
@lblResponse=Reakcia
#XFLD
@lblId=ID na načítanie statusu
#XFLD
@Id=ID
#XFLD
@lblSuccessIndicator=Znak úspešného vykonania
#XFLD
@lblErrorIndicator=Znak chyby
#XFLD
@lblErrorReason=Dôvod chyby
#XFLD
@lblStatus=Status
#XFLD
@lblApiTestRun=Testovací chod API
#XFLD
@lblRunStatus=Status chodu
#XFLD
@lblLastRan=Naposledy spustené dňa
#XFLD
@lblTestRun=Testovací chod
#XFLD
@lblDefaultHeader=Predvolené polia hlavičky (páry kľúč – hodnota)
#XFLD
@lblAdditionalHeader=Doplnkové pole hlavičky
#XFLD
@lblKey=Kľúč
#XFLD
@lblEditJSON=Upraviť JSON
#XFLD
@lblTasks=Úlohy
#XFLD
@lblRESTfullTask=API
#XFLD: add field button text
btnAddField=Pridať pole hlavičky
#XFLD: view Details link
@viewDetails=Zobraziť detaily
#XTOL
tooltipTxt=Viac
#XTOL
delete=Odstrániť
#XBTN: ok button text
btnOk=OK
#XBTN: cancel button text
btnCancel=Zrušiť
#XBTN: save button text
btnSave=Uložiť
#XMSG Error: Object has duplicated technical name.
VAL_OBJECT_DUPLICATE_NAME=Objekt „{0}“ už existuje v repository. Zadajte iný názov.
#XMSG: loading message while opening task chain
loadTaskChain=Načítava sa reťazec úloh...
#model properties
#XFLD
@status_panel=Status chodu
#XFLD
@deploy_status_panel=Status nasadenia
#XFLD
@status_lbl=Status
#XFLD
@lblLastExecuted=Posledný chod
#XFLD
@lblNotExecuted=Ešte nevykonané
#XFLD
@lblNotDeployed=Nenasadené
#XFLD
errorDetailsTxt=Status chodu nebolo možné načítať
#XBTN: Schedule dropdown menu
SCHEDULE=Plán
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Upraviť plán
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Odstrániť plán
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Vytvoriť plán
#XLNK
viewDetails=Zobraziť detaily
#XMSG: error message for reading execution status from backend
backendErrorMsg=Zdá sa, že údaje sa momentálne nenačítavajú zo servera. Skúste údaje znovu načítať.
#XFLD: Status text for Completed
@statusCompleted=Dokončené
#XFLD: Status text for Running
@statusRunning=Spustené
#XFLD: Status text for Failed
@statusFailed=Neúspešné
#XFLD: Status text for Stopped
@statusStopped=Zastavené
#XFLD: Status text for Stopping
@statusStopping=Zastavuje sa
#XFLD
@LoaderTitle=Načítava sa
#XFLD
#XFLD: Deploy status, dataflow has  been deployed / deployment date exists
@deployStatusActive=Nasadené
@deployStatusRevised=Lokálne aktualizácie
@deployStatusFailed=Neúspešné
@deployStatusPending=Nasadzuje sa...
@LoaderText=Načítavajú sa detaily zo servera
#XMSG
@msgDetailFetchError=Chyba počas načítania detailov zo servera
#XFLD
@executeError=Chyba
#XFLD
@executeWarning=Upozornenie
#XMSG
@executeConfirmDialog=Informácie
#XMSG
@executeunsavederror=Pred spustením uložte reťazec úloh.
#XMSG
@executemodifiederror=V reťazci úloh sú neuložené zmeny. Uložte.
#XMSG
@executerunningerror=Reťazec úloh je momentálne spustený. Pred spustením nového počkajte, kým sa nedokončí aktuálny chod.
#XMSG
@btnExecuteAnyway=Napriek tomu vykonať
#XMSG
@msgExecuteWithValidations=Reťazec úloh má chyby overenia. Spustenie reťazca úloh môže viesť k zlyhaniu.
#XMSG
@msgRunDeployedVersion=Neexistujú žiadne zmeny na nasadenie. Spustí sa posledná nasadená verzia reťazca úloh. Chcete pokračovať?
#XMSG
#XMSG
@navToMonitoring=Otvoriť v Monitore reťazca úloh
#XMSG
txtOR=ALEBO
#XFLD
@preview=Ukážka
#XMSG
txtand=a
#NOTR
@dateFormat=yyyy-MM-dd
#NOTR
@dateTimeFormatFilter=yyyy-MM-dd HH:mm:ss.
#NOTR
@timeFormat=HH:mm:ss
@lblColum=Stĺpec
#XFLD
@lblCondition=Podmienka
#XFLD
@lblValue=Hodnota
#XMSG
@msgJsonInvalid=Reťazec úloh sa nepodarilo uložiť, pretože v súbore JSON sú chyby. Skontrolujte a vyriešte.
#XTIT
@msgSaveFailTitle=Neplatné JSON.
#XMSG
NOT_CHAINABLE=Objekt „{0}“ nie je možné pridať do reťazca úloh.
#XMSG
NOT_CHAINABLE_REMOTETABLE=Objekt ''{0}'': Typ replikácie bude zmenený.
#XMSG
searchTaskChain=Hľadať objekty

#XFLD
@txtTaskChain=Reťazec úloh
#XFLD
@txtRemoteTable=Vzdialená tabuľka
#XFLD
@txtRemoveData=Odstrániť replikované údaje
#XFLD
@txtRemovePersist=Odstrániť perzistentné údaje
#XFLD
@txtView=Zobraziť
#XFLD
@txtDataFlow=Tok údajov
#XFLD
@txtIL=Inteligentné vyhľadávanie
#XFLD
@txtTransformationFlow=Transformačný tok
#XFLD
@txtReplicationFlow=Replikačný tok
#XFLD
@txtDeltaLocalTable=Lokálna tabuľka
#XFLD
@txtBWProcessChain=Procesný reťazec BW
#XFLD
@txtSQLScriptProcedure=Postup skriptu SQL
#XFLD
@txtAPI=API
#XFLD
@txtMerge=Zlúčiť
#XFLD
@txtOptimize=Optimalizovať
#XFLD
@txtVacuum=Odstrániť záznamy

#XFLD
@txtRun=Spustiť
#XFLD
@txtPersist=Trvalo uložiť
#XFLD
@txtReplicate=Replikovať
#XFLD
@txtDelete=Odstrániť záznamy s typom zmeny „Odstránené“
#XFLD
@txtRunTC=Spustiť reťazec úloh
#XFLD
@txtRunBW=Spustiť procesný reťazec BW
#XFLD
@txtRunSQLScriptProcedure=Spustiť postup skriptu SQL

#XFLD
@txtRunDataFlow=Spustiť tok údajov
#XFLD
@txtPersistView=Trvalo uložiť zobrazenie
#XFLD
@txtReplicateTable=Replikovať tabuľku
#XFLD
@txtRunIL=Spustiť inteligentné vyhľadávanie
#XFLD
@txtRunTF=Spustiť transformačný tok
#XFLD
@txtRunRF=Spustiť replikačný tok
#XFLD
@txtRemoveReplicatedData=Odstrániť replikované údaje
#XFLD
@txtRemovePersistedData=Odstrániť perzistentné údaje
#XFLD
@txtMergeData=Zlúčiť
#XFLD
@txtOptimizeData=Optimalizovať
#XFLD
@txtVacuumData=Odstrániť záznamy
#XFLD
@txtRunAPI=API chodu

#XFLD storage type text
hdlfStorage=Súbor

@statusNew=Nenasadené
@statusActive=Nasadené
@statusRevised=Lokálne aktualizácie
@statusPending=Nasadzuje sa...
@statusChangesToDeploy=Zmeny na nasadenie
@statusDesignTimeError=Chyba času návrhu
@statusRunTimeError=Chyba času chodu

#XTIT
txtNodes=Objekty v reťazci úloh({0})
#XBTN
@deleteNodes=Odstrániť

#XMSG
@txtDropDataToDiagram=Presuňte objekty do diagramu myšou.
#XMSG
@noData=Žiadne objekty

#XFLD
@txtTaskPosition=Pozícia úlohy

#input parameters and variables
#XFLD
lblInputParameters=Vstupné parametre
#XFLD
ip_name=Názov
#XFLD
ip_value=Hodnota
#XTEXT
@noObjectsFound=Nenájdené žiadne objekty

#XMSG
@msgExecuteSuccess=Chod reťazca úloh sa spustil.
#XMSG
@msgExecuteFail=Nepodarilo sa spustiť reťazec úloh.
#XMSG
@msgDeployAndRunSuccess=Nasadenie reťazca úloh a chod sa spustili.
#XMSG
@msgDeployAndRunFail=Nepodarilo sa nasadiť a vykonať reťazec úloh.
#XMSG
@titleExecuteBusy=Čakajte.
#XMSG
@msgExecuteBusy=Pripravujeme vaše údaje na spustenie reťazca úloh.
#XMSG
@msgAPITestRunSuccess=Testovací chod API bol spustený.
#XMSG
@msgAPIExecuteBusy=Pripravujeme vaše údaje na spustenie testovacieho chodu API.

#XTOL
txtOpenInEditor=Otvoriť v editore
#XTOL
txtPreviewData=Zobraziť ukážku údajov

#datapreview
#XMSG
@msgDataPreviewNotSupp=Pre tento objekt nie je k dispozícii ukážka údajov.

#XMSG Error: empty model
VAL_MODEL_EMPTY=Zdá sa, že váš model je prázdny. Pridajte niekoľko objektov.
#XMSG Error: deploy model
@msgDeployBeforeRun=Pred spustením musíte nasadiť reťazec úloh.
#BTN: close dialog
btnClose=Zavrieť

#XMSG Object is not Deployed
VAL_NOT_DEPLOYED=Objekt ''{0}''‘ musí byť nasadený, aby mohol pokračovať v reťazci úloh.
#XMSG Object is having runtime error
VAL_RUNTIME_ERROR=Objekt ''{0}'' vrátil chybu počas spustenia. Skontrolujte objekt.
#XMSG Object is having design time error
VAL_DESIGNTIME_ERROR=Objekt ''{0}'' vrátil chybu času návrhu. Skontrolujte objekt.
#XMSG Procedure Object has been deleted
VAL_DELETED_PROCEDURE=Nasledujúce postupy boli odstránené: {0}
#XMSG New Parameter added to Procedure object
VAL_NEW_PARAMETER_ADDED=Pridané nové parametre do postupu "{1}": "{0}". Znova nasaďte reťazec úloh.
#XMSG Parameter removed from Procedure object
VAL_PARAMETER_DELETED=Parametre odstránené z postupu "{1}": "{0}". Znova nasaďte reťazec úloh.
#XMSG Parameter data type changed in Procedure object
VAL_PARAMETER_DATATYPE_MODIFIED=Dátový typ parametra "{0}" bol zmenený z "{1}" na "{2}" v postupe "{3}".
#XMSG Parameter length changed of specific datatype in Procedure object
VAL_PARAMETER_LENGTH_MODIFIED=Dĺžka parametra "{0}" bola zmenená z "{1}" na "{2}" v postupe "{3}".
#XMSG Parameter precision changed of Decimal datatype in Procedure object
VAL_PARAMETER_PRECISION_MODIFIED=Presnosť parametra "{0}" bola zmenená z "{1}" na "{2}" v postupe "{3}".
#XMSG Parameter scale changed of Decimal datatype in Procedure object
VAL_PARAMETER_SCALE_MODIFIED=Stupnica parametra "{0}" bola zmenená z "{1}" na "{2}" v postupe "{3}".
#XMSG Parameter value not provided in Procedure object
VAL_SQL_SCRIPT_PROCEDURE_PARAMETER_VALUE_MISSING=Nezadali ste hodnoty pre vstupné parametre, ktoré sú potrebné pre postup "{0}": "{1}"
#XMSG Remote Table activity changes to Remove Replicated Data
VAL_REMOVE_REPLICATED_DATA=Ďalšie spustenie reťazca úloh zmení typ prístupu k údajom a údaje sa už nebudú odovzdávať v reálnom čase.
#XMSG Remote Table activity changes
REPLICATION_TYPE_CHANGED=Objekt ''{0}'': Typ replikácie bude zmenený.

#XFLD
@lblStartNode=Počiatočný uzol
#XFLD
@lblEndNode=Koncový uzol
#XFLD
@linkTo={0} do {1}
#XTOL
@txtViewDetails=Zobraziť detaily

#XTOL
txtOpenImpactLineage=Analýza účinku a pôvodu
#XFLD
@emailNotifications=E-mailové oznámenia
#XFLD
@txtReset=Resetovať
#XFLD
@emailMsgWarning=Štandardná šablóna e-mailu sa odošle, keď je e-mailová správa prázdna
#XFLD
@notificationSettings=Nastavenia oznámenia
#XFLD
@recipientEmailAddr=E-mailová adresa príjemcu
#XFLD
@emailSubject=Predmet e-mailu
@emailSubjectText=Reťazec úloh <TASKCHAIN_NAME> dokončený so statusom <STATUS>
#XFLD
@emailMessage=E-mailová správa
@emailMessageText=Vážený používateľ,\n\n týmto vás informujeme, že reťazec úloh:<TASKCHAIN_NAME> spustený o <START_TIME> bol dokončený so statusom <STATUS>. Vykonanie bolo ukončené o <END_TIME>.\n\nDetaily:\nPriestor:<SPACE_NAME>\nChyba:<ERROR_MESSAGE>
@selectEmailRecipientDialogTitle=Vybrať e-mailovú adresu príjemcu
@tenantMembers=Členovia nájomcu
@others=Ostatné({0})
@selectedEmailAddress=Vybraní príjemcovia
@add=Pridať
@placeholder=Zástupný znak
@description=Popis
@copyText=Kopírovať text
@taskchainDetailsPlaceholder=Zástupné znaky pre detaily reťazca úloh
@placeholderCopied=Zástupný znak je skopírovaný
@invalidEmailInfo=Zadajte správnu e-mailovú adresu
@maxMembersAlreadyAdded=Už ste pridali maximálne 20 členov
@enterEmailAddress=Zadajte e-mailovú adresu používateľa
@inCorrectPlaceHolder={0} nie je očakávaný zástupný znak v texte e-mailu.
@nsOFF=Neodosielať žiadne oznámenia
@nsFAILED=Odoslať e-mailové oznámenie, len keď sa chod dokončí s chybou
@nsCOMPLETED=Odoslať e-mailové oznámenie, len keď sa chod úspešne dokončí
@nsANY=Odoslať e-mail, keď sa chod dokončí
@phStatus=Status reťazca úloh - ÚSPEŠNÉ|NEÚSPEŠNÉ
@phTaskChainTName=Technický názov reťazca úloh
@phTaskChainBName=Obchodný názov reťazca úloh
@phLogId=ID protokolu chodu
@phUser=Používateľ, ktorý spúšťa reťazec úloh
@phLogUILink=Prepojenie na zobrazenie protokolu reťazca úloh
@phStartTime=Počiatočný čas chodu
@phEndTime=Koncový čas chodu
@phErrMsg=Prvé chybové hlásenie v protokole úloh. Protokol je prázdny v prípade statusu ÚSPEŠNÉ
@phSpaceName=Technický názov priestoru
@emailFormatError=Neplatný formát e-mailu
@emailFormatErrorInListText=V zozname členov, ktorí nie sú klientmi, bol zadaný neplatný formát e-mailu.
@emailSubjectTemplateText=Oznámenie pre reťazec úloh: $$taskChainName$$ - Priestor: $$spaceId$$ - Status: $$status$$
@emailMessageTemplateText=Dobrý deň,\n\n Váš reťazec úloh označený ako $$taskChainName$$ sa dokončil so statusom $$status$$. \n Tu sú niektoré ďalšie podrobnosti o reťazci úloh:\n - Technický názov reťazca úloh: $$taskChainName$$ \n - ID protokolu chodu reťazca úloh: $$logId$$ \n - Používateľ, ktorý spustil reťazec úloh: $$user$$ \n - Odkaz na zobrazenie protokolu reťazca úloh: $$uiLink$$ \n - Čas začiatku chodu reťazca úloh: $$startTime$$ \n - Čas ukončenia chodu reťazca úloh: $$endTime$$ \n - Názov priestoru: $$spaceId$$ \n
@deleteEmailRecepient=Odstrániť príjemcu
@emailInputDisabledText=Ak chcete pridať príjemcov e-mailu, nasaďte reťazec úloh.
@tenantOwnerDomainMatchErrorText=Doména e-mailovej adresy sa nezhoduje s doménou vlastníka klienta: {0}
@totalEmailIdLimitInfoText=Môžete vybrať až 20 príjemcov e-mailu vrátane používateľov členov klienta a iných príjemcov.
@emailDomainInfoText=Akceptované sú iba e-mailové adresy s doménou: {0}.
@duplicateEmailErrorText=V zozname sú duplicitní príjemcovia e-mailu.

#XFLD Zorder Title
@txtZorderTitle=Apache Spar: Stĺpce v poradí Z

#XFLD Zorder NoColumn
@txtZorderNoColumn=Nenájdené žiadne stĺpce v poradí Z

#XFLD Zorder Column PrimaryKey
@txtZorderColumnPrimaryKey=Primárny kľúč

#XFLD
@lblOperators=Operátory
addNewSelector=Pridať ako Nová úloha
parallelSelector=Pridať ako Paralelná úloha
replaceSelector=Nahradiť existujúcu úlohu
addparallelbranch=Pridať ako Paralelné odvetvie
addplaceholder=Pridať Zástupný znak
addALLOperation=VŠETKY operátory
addOROperation=AKÝKOĽVEK operátor
addplaceholdertocanvas=Pridať zástupný znak do plátna
addplaceholderonselected=Pridať zástupný znak po vybranej úlohe
#XFLD: Add Parallel Branch to Selected Task
addparallelbranchonselected=Pridať paralelné odvetvie po vybranej úlohe
addOperator=Pridať operátor
txtAdd=Pridať
txtPlaceHolderText=Presunúť myšou úlohu sem
@lblLayout=Rozloženie

#XMSG
VAL_UNCONNECTED_TASK=Úloha ''{0}'' nie je pripojená k reťazcu úloh.
#XMSG
VAL_TASK_SHOULD_HAVE_ONE_INCOMING_LINK=Úloha ''{0}'' by mala mať len jeden prichádzajúci odkaz.
#XMSG
VAL_TASK_SHOULD_HAVE_INCOMING_LINK=Úloha ''{0}'' by mala mať jeden prichádzajúci odkaz.
#XMSG
VAL_UNCONNECTED_OPERATOR=Operátor ''{0}'' nie je pripojený k reťazcu úloh.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ATLEAST_TWO_INCOMING_LINK=Operátor ''{0}'' by mal mať aspoň dva prichádzajúce odkazy.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ONE_OUT_LINK=Operátor ''{0}'' by mal mať aspoň jeden odchádzajúci odkaz.
#XMSG
VAL_CIRCULAR_REFERENCE_EXITS=Kruhová slučka existuje v reťazci úloh ''{0}''.
#XMSG
VAL_UNCONNECTED_BRANCH=Objekt/odvetvie ''{0}'' nie je pripojené k reťazcu úloh.
#XMSG
VAL_TASK_TRIGGERED_PARALLEL_MORE_THAN_ONE_TIME=Úloha ''{0}''‘ pripojená paralelne viac ako raz. Pre pokračovanie odstráňte duplicity.


txtBegin=Začať
txtNodesInLink=Objekty zúčastnené
#XTOL Tooltip for a context button on diagram
openInNewTab=Otvoriť na novej karte
#XTOL Tooltip for a context button on diagram
txtConnectToTaskOperator=Potiahnutím pripojte
@emailUpdateError=Chyba pri aktualizácii zoznamu oznámení e-mailom

#XMSG
noTeamPrivilegeTxt=Nemáte povolenie na zobrazenie zoznamu členov klienta. Na manuálne pridanie príjemcov e-mailu použite kartu Iné.

#XFLD Package
@txtPackage=Balík

#XMSG warning for repository package assignment changed. {1} - package name
VAL_PACKAGE_CHANGED=Tento objekt ste priradili k balíku „{1}“. Kliknutím na možnosť Uložiť túto zmenu potvrdíte a overíte. Upozorňujeme, že priradenie k balíku nemožno v tomto editore po uložení vrátiť späť.
#XMSG warning for repository package dependency issue.
MISSING_DEPENDENCY=Závislosti objektu „{0}“ nie je možné vyriešiť v kontexte balíka „{1}“.

#Warning messages for the folders in the "Others" tab of task chain editor
#XMSG generic warning if getting the objects in a particular folder fails with unhandled errors
getNonRepoArtefactTypeDescendantsError=Pri načítavaní objektov vo vybranom priečinku sa vyskytol problém.
#BW Process Chains folder related warning messages
#XMSG warning if user does not have the necessary permission related to the SAP BW Bridge space
missingPrivilegeForBridgeSpaceError=Nemáte potrebné povolenie na zobrazenie alebo zahrnutie reťazcov procesov BW do reťazca úloh.
#XMSG warning if there was a problem retrieving the BW process chains due to bridge space membership, bridge connection issues or other errors
generalBridgeSpaceError=Pri načítavaní reťazcov procesov BW sa vyskytol problém.
#XMSG warning if the BW API for retrieving the BW process chains from the SAP BW Bridge tenant fails
bwApiClientError=Vyskytol sa problém pri získavaní procesných reťazcov BW z klienta SAP BW Bridge.
#XMSG warning if BW process chains list is empty
bwApiEmptyListError=V kliente SAP BW Bridge neboli nájdené žiadne procesné reťazce BW.
#XMSG: Task log message for invalid OIDC configuration in the SAP BW Bridge tenant
bwBridgeOIDCConfigStatusInvalid=Overenie OpenID nie je nakonfigurované pre tento klient. Použite clientID "{0}" a tokenURL "{1}" na konfiguráciu overenia OpenID v kliente SAP BW bridge, ako je popísané v pokyne SAP 3536298.
#XMSG BW Process Chain Object has been deleted
VAL_DELETED_BW=Nasledujúce reťazce procesov BW boli pravdepodobne odstránené z klienta SAP Bridge: {0}
#BW SQL Script Procedures folder related warning messages
#XFLD waring if the Procedure list is empty
procedureAPIEmptyList=Neboli vytvorené žiadne postupy alebo privilégium EXECUTE nebolo udelené schéme Open SQL.
#Change digram orientations
changeOrientations=Zmeniť orientácie


# placeholder for the API Path
apiPath=Napríklad: /job/v1
# placeholder for the status API Path
statusAPIPath=Napríklad: /job/v1/{id}/status
# valueStateText for the API Path
apiPathRequired=Cesta API je povinná
#placeholder for the CSRF Token URL
csrfTokenURL=Podporované je iba HTTPS
# Response Type 1
statusCode=Načítať výsledok zo stavového kódu HTTP
# Response Type 2
locationHeader=Načítať výsledok zo stavového kódu HTTP a hlavičky lokality
# Response Type 3
responseBody=Načítať výsledok zo stavového kódu HTTP a textu odpovede
# placeholder for ID
idPlaceholder=Zadajte cestu JSON
# placeholder for indicator value
indicatorValue=Zadajte hodnotu
# Placeholder for key
keyPlaceholder=Zadajte kľúč
# Error message for missing key
KeyRequired=Kľúč je povinný
# Error message for invalid key format
invalidKeyFormat=Zadaný kľúč hlavičky nie je povolený. Platné hlavičky sú:<ul><li>"prefer"</li><li>Hlavičky začínajúce na "x-", okrem "x-forwarded-host"</li><li>Hlavičky, ktoré obsahujú alfanumerické znaky, "-", alebo "_"</li></ul>
# Error message for missing value
valueRequired=Vyžaduje sa hodnota
# Error message for invalid characters in value
invalidValueCharacters=Hlavička obsahuje neplatné znaky. Špeciálne znaky, ktoré sú povolené, sú:\t ";", ":", "-", "_", ",", "?", "/", a "*"
# Validation message for invoke api path
apiPathValidation=Zadajte platnú cestu API, napríklad: /job/v1
# Validation message for JSON path
jsonPathValidation=Zadajte platnú cestu JSON
# Validation message for success/error indicator
indicatorValueValidation=Hodnota indikátora musí začínať alfanumerickým znakom a môže obsahovať nasledujúce špeciálne znaky:\t "-", a "_"
# Error message for JSON path
jsonPathRequired=Cesta JSON je povinná
# Error message for invalid API Technical Name
invalidTechnicalName=Technický názov obsahuje neplatné znaky
# Error message for empty Technical Name
emptyTechnicalName=Technický názov je povinný
# Tooltip for codeEditor dialog
codeEditorTooltip=Otvoriť okno úpravy JSON
# Status Api path validation message
validationStatusAPIPath=Zadajte platnú cestu API, napríklad: /job/v1/{id}/status
# CSRF token URL validation message
validationCSRFTokenURL=Adresa URL tokenu CSRF musí začínať reťazcom „https://“ a musí ísť o platnú adresu URL
# Select a connection
selectConnection=Vybrať pripojenie
# Validation message for connection item error
connectionNotReplicated=Pripojenie je momentálne neplatné na spustenie úloh rozhrania API. Otvorte aplikáciu „Pripojenia“ a znova zadajte svoje poverenia, aby ste opravili pripojenie HTTP
# Validation message for API task properties wrong value or missing
VAL_REST_API_PROPERTIES_MISSING=Úloha API "{0}" obsahuje nesprávne alebo chýbajúce hodnoty pre nasledujúce vlastnosti: {1}
# Validation message for API connection api is throwing some error or selected connection has some warning
VAL_REST_API_CONNECTION=Úloha API "{0}" má problém s pripojením HTTP. Otvorte aplikáciu „Pripojenia“ a skontrolujte pripojenie
# Validation message for API task properties when delected connection
VAL_REST_API_CONNECTION_DELETED=Úloha API "{0}" odstránila pripojenie "{1}"
# Validation message for API task properties when connection cannot be consumed in the API task
VAL_REST_API_CONNECTION_CONSUMPTION_WARNING=Úloha API "{0}"má pripojenie "{1}" , ktoré nemožno použiť na spustenie úloh API. Otvorte aplikáciu „Pripojenia“ a znova zadajte svoje poverenia, aby ste vytvorili pripojenie pre úlohy API
# Information message if synchrnous mode is selected in the API Invocation then we don't need status information
modeInfoText=V synchrónnom režime sa stavový panel pri vyvolaní API nezobrazuje
# validation dialog button for Run API Test
saveAnyway=Napriek tomu uložiť
# validation message for technical name
technicalNameValidation=Technický názov musí byť jedinečný v rámci reťazca úloh. Vyberte iný technický názov
# Connection error message
getHttpConnectionsError=Nepodarilo sa vyvolať pripojenia HTTP
# Msg for the API test run when the Task chain is not saved
taskChainNotSaved=Pred spustením testovacieho chodu API je potrebné uložiť reťazec úloh
# Msg failed to run API test run
@failedToRunAPI=Spustenie testovacieho chodu API zlyhalo
# Msg for the API test run when its already in running state
apiTaskRunning=Testovací chod API už prebieha. Chcete spustiť nový testovací chod?

topToBtm=Zhora nadol
leftToRight=Zľava doprava

#XTIT Apache Spark Application Settings
txtHeaderApacheSettings=Nastavenia aplikácie Apache Spark
#XFLD Use Default
txtUseSpaceDefault=Použiť predvolené
#XFLD Application
txtApplication=Aplikácia
#XFLD Define new settings for this Task
txtNewSettings=Definujte nové nastavenia pre túto úlohu

#XFLD Use Default
txtUseDefault=Použiť predvolené




