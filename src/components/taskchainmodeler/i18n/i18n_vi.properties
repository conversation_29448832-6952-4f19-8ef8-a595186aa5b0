#XTOL Undo
@undo=Hoàn tác
#XTOL Redo
@redo=Làm lại
#XTOL Delete Selected Symbol
@deleteNode=Xóa biểu tượng đã chọn
#XTOL Zoom to Fit
@zoomToFit=Thu/phóng đến kích cỡ phù hợp
#XTOL Auto Layout
@autoLayout=Bố cục tự động
#XMSG
@welcomeText=Kéo và thả đối tượng của bạn từ panen trái đến vùng hiển thị dữ liệu này.
#XMSG
@txtNoData=Có vẻ như bạn chưa thêm bất kỳ đối tượng nào.
#XMSG
VAL_ENTER_VALID_STRING_GEN=Vui lòng nhập chuỗi hợp lệ có độ dài nhỏ hơn hoặc bằng {0}.
#XMSG
@noParametersMsg=Thủ tục này không có tham số đầu vào.
#XMSG
@ip_enterValueMsg="{0}" (Thực hiện thủ tục SQL Script) có các tham số đầu vào. Bạn có thể đặt giá trị của mỗi tham số.
#XTOL
@validateModel=Thông báo xác thực
#XTOL
@hierarchy=Phân cấp
#XTOL
@columnCount=Số lượng cột
#XFLD
@yes=Có
#XFLD
@no=Không
#XTIT Save Dialog param
@modelNameTaskChain=Chuỗi tác vụ
#properties panel
@lblPropertyTitle=Đặc tính
#XFLD
@lblGeneral=Chung
#XFLD : Setting
@lblSetting=Thiết lập
#XTXT: Delta Enabled Local Table Deletion
txtDeltaTableDeletion=Xóa tất cả bản ghi đã được xử lý đầy đủ với Kiểu thay đổi 'Đã xóa' cũ hơn
#XTXT: Delta Enabled Local Table Deletion Input field description
desDeltaTableDeletion=Ngày
#XFLD: Data Activation label
@lblDataActivation=Kích hoạt dữ liệu
#XFLD: Latency label
@latency=̣Độ trễ
#XTEXT: Text for Latency dropdown
txtLatencyDefault=Mặc định
#XTEXT: Text 1 hour
txtOneHour=1 giờ
#XTEXT: Text 2 hours
txtTwoHours=2 giờ
#XTEXT: Text 3 hours
txtThreeHours=3 giờ
#XTEXT: Text 4 hours
txtFourHours=4 giờ
#XTEXT: Text 6 hours
txtSixHours=6 giờ
#XTEXT: Text 12 hours
txtTwelveHours=12 giờ
#XTEXT: Text 1 day
txtOneDay=1 ngày
#XFLD: Latency label
@autoRestartHead=Tự động khởi động lại
#XFLD
@lblConnectionName=Kết nối
#XFLD
@lblQualifiedName=Tên đủ tiêu chuẩn
#XFLD
@lblSpaceName=Tên vùng dữ liệu
#XFLD
@lblLocalSchemaName=Biểu đồ cục bộ
#XFLD
@lblType=Kiểu đối tượng
#XFLD
@lblActivity=Hoạt động
#XFLD
@lblTableName=Tên bảng
#XFLD
@lblBusinessName=Tên doanh nghiệp
#XFLD
@lblTechnicalName=Tên kỹ thuật
#XFLD
@lblSpace=Vùng dữ liệu
#XFLD
@lblLabel=Nhãn
#XFLD
@lblDataType=Kiểu dữ liệu
#XFLD
@lblDescription=Mô tả
#XFLD
@lblStorageType=Lưu trữ
#XFLD
@lblHTTPConnection=Kết nối HTTP cùng loại
#XFLD
@lblAPISettings=Cài đặt API cùng loại
#XFLD
@header=Tiêu đề
#XFLD
@lblInvoke=Gọi API
#XFLD
@lblMethod=Phương thức
#XFLD
@lblUrl=URL cơ sở
#XFLD
@lblAPIPath=Đường dẫn API
#XFLD
@lblMode=Chế độ
#XFLD
@lblCSRFToken=Yêu cầu mã thông báo CSRF
#XFLD
@lblTokenURL=URL mã thông báo CSRF
#XFLD
@csrfTokenInfoText=Nếu không được nhập, URL cơ sở và đường dẫn API sẽ được sử dụng
#XFLD
@lblCSRF=Mã thông báo CSRF
#XFLD
@lblRequestBody=Phần thân yêu cầu
#XFLD
@lblFormat=Định dạng
#XFLD
@lblResponse=Phản hồi
#XFLD
@lblId=ID để truy xuất trạng thái
#XFLD
@Id=ID
#XFLD
@lblSuccessIndicator=Chỉ báo thành công
#XFLD
@lblErrorIndicator=Chỉ báo lỗi
#XFLD
@lblErrorReason=Lý do cho lỗi
#XFLD
@lblStatus=Trạng thái
#XFLD
@lblApiTestRun=Chạy thử API
#XFLD
@lblRunStatus=Trạng thái chạy
#XFLD
@lblLastRan=Lần chạy cuối vào ngày
#XFLD
@lblTestRun=Chạy thử
#XFLD
@lblDefaultHeader=Trường tiêu đề mặc định (cặp Khóa-Giá trị)
#XFLD
@lblAdditionalHeader=Trường tiêu đề bổ sung
#XFLD
@lblKey=Khóa
#XFLD
@lblEditJSON=Hiệu chỉnh JSON
#XFLD
@lblTasks=Tác vụ
#XFLD
@lblRESTfullTask=API
#XFLD: add field button text
btnAddField=Thêm trường tiêu đề
#XFLD: view Details link
@viewDetails=Xem chi tiết
#XTOL
tooltipTxt=Thêm
#XTOL
delete=Xóa
#XBTN: ok button text
btnOk=Ok
#XBTN: cancel button text
btnCancel=Hủy
#XBTN: save button text
btnSave=Lưu
#XMSG Error: Object has duplicated technical name.
VAL_OBJECT_DUPLICATE_NAME=Đối tượng ''{0}''’ đã tồn tại trong kho lưu trữ. Vui lòng nhập tên khác.
#XMSG: loading message while opening task chain
loadTaskChain=Đang tải chuỗi tác vụ...
#model properties
#XFLD
@status_panel=Trạng thái thực hiện
#XFLD
@deploy_status_panel=Trạng thái triển khai
#XFLD
@status_lbl=Trạng thái
#XFLD
@lblLastExecuted=Thực hiện lần cuối
#XFLD
@lblNotExecuted=Chưa thực hiện
#XFLD
@lblNotDeployed=Chưa triển khai
#XFLD
errorDetailsTxt=Không thể tìm nạp trạng thái thực hiện
#XBTN: Schedule dropdown menu
SCHEDULE=Lập lịch
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Hiệu chỉnh lịch
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Xóa lịch
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Tạo lịch
#XLNK
viewDetails=Xem chi tiết
#XMSG: error message for reading execution status from backend
backendErrorMsg=Có vẻ như dữ liệu hiện tại không được tải từ máy chủ. Hãy thử tìm nạp lại dữ liệu.
#XFLD: Status text for Completed
@statusCompleted=Đã hoàn tất
#XFLD: Status text for Running
@statusRunning=Chạy
#XFLD: Status text for Failed
@statusFailed=Bị lỗi
#XFLD: Status text for Stopped
@statusStopped=Đã dừng
#XFLD: Status text for Stopping
@statusStopping=Đang dừng
#XFLD
@LoaderTitle=Đang tải
#XFLD
#XFLD: Deploy status, dataflow has  been deployed / deployment date exists
@deployStatusActive=Đã triển khai
@deployStatusRevised=Cập nhật cục bộ
@deployStatusFailed=Bị lỗi
@deployStatusPending=Đang triển khai...
@LoaderText=Tìm nhận chi tiết từ máy chủ
#XMSG
@msgDetailFetchError=Lỗi khi tìm nhận chi tiết từ máy chủ
#XFLD
@executeError=Lỗi
#XFLD
@executeWarning=Cảnh báo
#XMSG
@executeConfirmDialog=Thông tin
#XMSG
@executeunsavederror=Lưu trước khi chạy chuỗi tác vụ.
#XMSG
@executemodifiederror=Chưa lưu thay đổi trong chuỗi tác vụ. Hãy lưu sự thay đổi.
#XMSG
@executerunningerror=Chuỗi tác vụ hiện đang chạy. Chờ cho đến khi quá trình chạy hiện tại hoàn tất trước khi bắt đầu với lần chạy mới.
#XMSG
@btnExecuteAnyway=Vẫn tiếp tục thực hiện
#XMSG
@msgExecuteWithValidations=Chuỗi tác vụ có lỗi xác thực. Chạy chuỗi tác vụ sẽ không thành công.
#XMSG
@msgRunDeployedVersion=Có những thay đổi để triển khai. Phiên bản được triển khai cuối cùng của chuỗi tác vụ sẽ được thực hiện. Bạn có muốn tiếp tục không?
#XMSG
#XMSG
@navToMonitoring=Mở trong Giám sát chuỗi tác vụ
#XMSG
txtOR=OR
#XFLD
@preview=Xem trước
#XMSG
txtand=và
#NOTR
@dateFormat=yyyy-MM-dd
#NOTR
@dateTimeFormatFilter=yyyy-MM-dd HH:mm:ss.
#NOTR
@timeFormat=HH:mm:ss
@lblColum=Cột
#XFLD
@lblCondition=Điều kiện
#XFLD
@lblValue=Giá trị
#XMSG
@msgJsonInvalid=Chuỗi tác vụ không thể lưu được vì bị lỗi trong JSON. Vui lòng kiểm tra và giải quyết lỗi.
#XTIT
@msgSaveFailTitle=JSON không hợp lệ.
#XMSG
NOT_CHAINABLE=Không thể thêm đối tượng ''{0}''’ vào chuỗi tác vụ.
#XMSG
NOT_CHAINABLE_REMOTETABLE=Đối tượng ''{0}'': kiểu sao chép sẽ được thay đổi.
#XMSG
searchTaskChain=Đối tượng tìm kiếm

#XFLD
@txtTaskChain=Chuỗi tác vụ
#XFLD
@txtRemoteTable=Bảng từ xa
#XFLD
@txtRemoveData=Loại bỏ dữ liệu đã sao chép
#XFLD
@txtRemovePersist=Loại bỏ dữ liệu liên tục
#XFLD
@txtView=Màn hình
#XFLD
@txtDataFlow=Luồng dữ liệu
#XFLD
@txtIL=Tra cứu thông minh
#XFLD
@txtTransformationFlow=Luồng chuyển đổi
#XFLD
@txtReplicationFlow=Luồng sao chép
#XFLD
@txtDeltaLocalTable=Bảng cục bộ
#XFLD
@txtBWProcessChain=Chuỗi quy trình BW
#XFLD
@txtSQLScriptProcedure=Thủ tục SQL Script
#XFLD
@txtAPI=API
#XFLD
@txtMerge=Sáp nhập
#XFLD
@txtOptimize=Tối ưu hóa
#XFLD
@txtVacuum=Xóa bản ghi

#XFLD
@txtRun=Chạy
#XFLD
@txtPersist=Vẫn còn
#XFLD
@txtReplicate=Sao chép
#XFLD
@txtDelete=Xóa bản ghi có kiểu thay đổi ‘Đã xóa’
#XFLD
@txtRunTC=Chạy chuỗi tác vụ
#XFLD
@txtRunBW=Thực hiện chuỗi quy trình BW
#XFLD
@txtRunSQLScriptProcedure=Thực hiện thủ tục SQL Script

#XFLD
@txtRunDataFlow=Thực hiện luồng dữ liệu
#XFLD
@txtPersistView=Duy trì màn hình
#XFLD
@txtReplicateTable=Sao chép bảng
#XFLD
@txtRunIL=Thực hiện tra cứu thông minh
#XFLD
@txtRunTF=Thực hiện luồng chuyển đổi
#XFLD
@txtRunRF=Chạy luồng sao chép
#XFLD
@txtRemoveReplicatedData=Loại bỏ dữ liệu đã sao chép
#XFLD
@txtRemovePersistedData=Loại bỏ dữ liệu liên tục
#XFLD
@txtMergeData=Sáp nhập
#XFLD
@txtOptimizeData=Tối ưu hóa
#XFLD
@txtVacuumData=Xóa bản ghi
#XFLD
@txtRunAPI=Chạy API

#XFLD storage type text
hdlfStorage=Tập tin

@statusNew=Chưa triển khai
@statusActive=Đã triển khai
@statusRevised=Cập nhật cục bộ
@statusPending=Đang triển khai...
@statusChangesToDeploy=Các thay đổi để triển khai
@statusDesignTimeError=Lỗi thời gian thiết kế
@statusRunTimeError=Lỗi thời gian thực hiện

#XTIT
txtNodes=Đối tượng trong chuỗi tác vụ ({0})
#XBTN
@deleteNodes=Xóa

#XMSG
@txtDropDataToDiagram=Kéo và thả đối tượng vào sơ đồ.
#XMSG
@noData=Không có đối tượng

#XFLD
@txtTaskPosition=Vị trí tác vụ

#input parameters and variables
#XFLD
lblInputParameters=Tham số nhập
#XFLD
ip_name=Tên
#XFLD
ip_value=Giá trị
#XTEXT
@noObjectsFound=Không tìm thấy đối tượng

#XMSG
@msgExecuteSuccess=Đã bắt đầu quá trình chạy chuỗi tác vụ.
#XMSG
@msgExecuteFail=Quá trình chạy chuỗi tác vụ không thành công.
#XMSG
@msgDeployAndRunSuccess=Đã bắt đầu quá trình triển khai và chạy chuỗi tác vụ.
#XMSG
@msgDeployAndRunFail=Không thể triển khai và chạy chuỗi tác vụ.
#XMSG
@titleExecuteBusy=Vui lòng chờ.
#XMSG
@msgExecuteBusy=Chúng tôi đang chuẩn bị dữ liệu của bạn để bắt đầu chạy chuỗi tác vụ.
#XMSG
@msgAPITestRunSuccess=Quá trình chạy thử API đã bắt đầu.
#XMSG
@msgAPIExecuteBusy=Chúng tôi đang chuẩn bị dữ liệu của bạn để bắt đầu chạy thử API.

#XTOL
txtOpenInEditor=Mở trong trình soạn thảo
#XTOL
txtPreviewData=Xem trước dữ liệu

#datapreview
#XMSG
@msgDataPreviewNotSupp=Xem trước dữ liệu không khả dụng cho đối tượng này.

#XMSG Error: empty model
VAL_MODEL_EMPTY=Mô hình của bạn dường như trống. Vui lòng thêm một số đối tượng.
#XMSG Error: deploy model
@msgDeployBeforeRun=Bạn cần triển khai trước khi chạy chuỗi tác vụ.
#BTN: close dialog
btnClose=Đóng

#XMSG Object is not Deployed
VAL_NOT_DEPLOYED=Phải triển khai đối tượng ''{0}'' để tiếp tục với chuỗi tác vụ.
#XMSG Object is having runtime error
VAL_RUNTIME_ERROR=Đối tượng ''{0}'' trả về lỗi thời gian chạy. Vui lòng kiểm tra đối tượng.
#XMSG Object is having design time error
VAL_DESIGNTIME_ERROR=Đối tượng ''{0}'' trả về lỗi thời gian thiết kế. Vui lòng kiểm tra đối tượng.
#XMSG Procedure Object has been deleted
VAL_DELETED_PROCEDURE=Thủ tục sau đây đã bị xóa: {0}
#XMSG New Parameter added to Procedure object
VAL_NEW_PARAMETER_ADDED=Tham số mới được thêm vào thủ tục "{1}": "{0}". Vui lòng triển khai lại chuỗi tác vụ.
#XMSG Parameter removed from Procedure object
VAL_PARAMETER_DELETED=Tham số được loại bỏ khỏi thủ tục "{1}": "{0}". Vui lòng triển khai lại chuỗi tác vụ.
#XMSG Parameter data type changed in Procedure object
VAL_PARAMETER_DATATYPE_MODIFIED=Loại dữ liệu tham số "{0}" được thay đổi từ "{1}" thành "{2}" trong thủ tục "{3}".
#XMSG Parameter length changed of specific datatype in Procedure object
VAL_PARAMETER_LENGTH_MODIFIED=Độ dài tham số "{0}" được thay đổi từ "{1}" thành "{2}" trong thủ tục "{3}".
#XMSG Parameter precision changed of Decimal datatype in Procedure object
VAL_PARAMETER_PRECISION_MODIFIED=Độ chính xác của tham số "{0}" được thay đổi từ "{1}" thành "{2}" trong thủ tục "{3}".
#XMSG Parameter scale changed of Decimal datatype in Procedure object
VAL_PARAMETER_SCALE_MODIFIED=Tỷ lệ tham số "{0}" được thay đổi từ "{1}" thành "{2}" trong thủ tục "{3}".
#XMSG Parameter value not provided in Procedure object
VAL_SQL_SCRIPT_PROCEDURE_PARAMETER_VALUE_MISSING=Bạn đã không nhập giá trị cho tham số đầu vào được yêu cầu cho thủ tục "{0}": "{1}"
#XMSG Remote Table activity changes to Remove Replicated Data
VAL_REMOVE_REPLICATED_DATA=Lần chạy tiếp theo của chuỗi tác vụ sẽ thay đổi kiểu truy cập dữ liệu và dữ liệu sẽ không còn được tải lên theo thời gian thực nữa.
#XMSG Remote Table activity changes
REPLICATION_TYPE_CHANGED=Đối tượng "{0}": Kiểu sao chép sẽ được thay đổi.

#XFLD
@lblStartNode=Nút bắt đầu
#XFLD
@lblEndNode=Nút kết thúc
#XFLD
@linkTo={0} đến {1}
#XTOL
@txtViewDetails=Xem chi tiết

#XTOL
txtOpenImpactLineage=Phân tích dòng và tác động
#XFLD
@emailNotifications=Thông báo email
#XFLD
@txtReset=Thiết lập lại
#XFLD
@emailMsgWarning=Email mẫu mặc định sẽ được gửi khi thông báo email trống
#XFLD
@notificationSettings=Thiết lập thông báo
#XFLD
@recipientEmailAddr=̣Địa chỉ emai người nhận
#XFLD
@emailSubject=Tiêu đề email
@emailSubjectText=Chuỗi tác vụ <TASKCHAIN_NAME> đã hoàn tất với trạng thái <STATUS>
#XFLD
@emailMessage=Thông báo email
@emailMessageText=Người dùng thân mến,\n\n Email này là để thông báo cho bạn rằng chuỗi tác vụ:<TASKCHAIN_NAME> chạy vào <START_TIME> đã hoàn tất với trạng thái <STATUS>. Việc thực hiện kết thúc lúc <END_TIME>.\n\nChi tiết:\nVùng dữ liệu:<SPACE_NAME>\nLỗi:<ERROR_MESSAGE>
@selectEmailRecipientDialogTitle=Chọn địa chỉ emai người nhận
@tenantMembers=Thành viên đối tượng thuê
@others=Khác({0})
@selectedEmailAddress=Người nhận được chọn
@add=Thêm vào
@placeholder=Trình giữ chỗ
@description=Mô tả
@copyText=Sao chép văn bản
@taskchainDetailsPlaceholder=Trình giữ chỗ cho chi tiết chuỗi tác vụ
@placeholderCopied=Trình giữ chỗ được sao chép
@invalidEmailInfo=Nhập địa chỉ email đúng
@maxMembersAlreadyAdded=Bạn đã thêm vào tối đa 20 thành viên
@enterEmailAddress=Nhập địa chỉ email người dùng
@inCorrectPlaceHolder={0} không phải là trình giữ chỗ được mong đợi trong nội dung email.
@nsOFF=Không gửi bất kỳ thông báo nào
@nsFAILED=Chỉ gửi thông báo qua email khi quá trình chạy hoàn tất bị lỗi
@nsCOMPLETED=Chỉ gửi thông báo qua email khi quá trình chạy hoàn tất thành công
@nsANY=Gửi email khi quá trình chạy hoàn tất
@phStatus=Trạng thái của chuỗi tác vụ - THÀNH CÔNG|KHÔNG THÀNH CÔNG
@phTaskChainTName=Tên kỹ thuật của chuỗi tác vụ
@phTaskChainBName=Tên kinh doanh của chuỗi tác vụ
@phLogId=ID nhật ký của quá trình chạy
@phUser=Người dùng đang chạy chuỗi tác vụ
@phLogUILink=Liên kết đến màn hình hiển thị nhật ký của chuỗi tác vụ
@phStartTime=Thời gian bắt đầu của quá trình chạy
@phEndTime=Thời gian kết thúc của quá trình chạy
@phErrMsg=Thông báo lỗi đầu tiên trong nhật ký tác vụ. Nhật ký trống trong trường hợp THÀNH CÔNG
@phSpaceName=Tên kỹ thuật của vùng dữ liệu
@emailFormatError=Định dạng email không hợp lệ
@emailFormatErrorInListText=Định dạng email không hợp lệ được nhập vào danh sách thành viên không phải là đối tượng thuê.
@emailSubjectTemplateText=Thông báo cho chuỗi tác vụ: $$taskChainName$$ - Vùng dữ liệu: $$spaceId$$ - Status: $$status$$
@emailMessageTemplateText=Xin chào,\n\n Chuỗi tác vụ được gắn nhãn $$taskChainName$$ của bạn đã kết thúc với trạng thái $$status$$. \n Ở đây là một số chi tiết khác về chuỗi tác vụ:\n - Tên kỹ thuật của chuỗi tác vụ: $$taskChainName$$ \n - ID nhật ký của chuỗi tác vụ chạy: $$logId$$ \n - Người dùng đã chạy chuỗi tác vụ : $$user$$ \n - Liên kết đến màn hình nhật ký của chuỗi tác vụ: $$uiLink$$ \n - Thời gian bắt đầu chạy chuỗi tác vụ: $$startTime$$ \n - Thời gian kết thúc chuỗi tác vụ : $$endTime$$ \n - Tên vùng dữ liệu: $$spaceId$$ \n
@deleteEmailRecepient=Xóa người nhận
@emailInputDisabledText=Vui lòng triển khai chuỗi tác vụ để thêm người nhận email.
@tenantOwnerDomainMatchErrorText=Tên miền địa chỉ email không khớp với tên miền chủ sở hữu đối tượng thuê: {0}
@totalEmailIdLimitInfoText=Bạn có thể chọn tối đa 20 người nhận email bao gồm người dùng thành viên đối tượng thuê và người nhận khác.
@emailDomainInfoText=Chỉ các địa chỉ email có tên miền: {0} được chấp nhận.
@duplicateEmailErrorText=Có những người nhận email trùng lặp trong danh sách.

#XFLD Zorder Title
@txtZorderTitle=Cột thứ tự Z Apache Spark

#XFLD Zorder NoColumn
@txtZorderNoColumn=Không tìm thấy cột thứ tự Z

#XFLD Zorder Column PrimaryKey
@txtZorderColumnPrimaryKey=Khóa chính

#XFLD
@lblOperators=Toán tử
addNewSelector=Thêm tác vụ mới
parallelSelector=Thêm tác vụ song song
replaceSelector=Thay thế tác vụ hiện có
addparallelbranch=Thêm nhánh song song
addplaceholder=Thêm trình giữ chỗ
addALLOperation=TẤT CẢ toán tử
addOROperation=BẤT KỲ toán tử
addplaceholdertocanvas=Thêm trình giữ chỗ vào vùng hiển thị dữ liệu
addplaceholderonselected=Thêm trình giữ chỗ vào sau Tác vụ đã chọn
#XFLD: Add Parallel Branch to Selected Task
addparallelbranchonselected=Thêm nhánh song song vào sau Tác vụ đã chọn
addOperator=Thêm toán tử
txtAdd=Thêm
txtPlaceHolderText=Kéo và thả Tác vụ vào đây
@lblLayout=Bố cục

#XMSG
VAL_UNCONNECTED_TASK=Tác vụ ''{0}'' không được kết nối với chuỗi tác vụ.
#XMSG
VAL_TASK_SHOULD_HAVE_ONE_INCOMING_LINK=Tác vụ ''{0}'' chỉ nên có một liên kết đến.
#XMSG
VAL_TASK_SHOULD_HAVE_INCOMING_LINK=Tác vụ ''{0}'' nên có một liên kết đến.
#XMSG
VAL_UNCONNECTED_OPERATOR=Toán tử ''{0}'' không được kết nối với chuỗi tác vụ.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ATLEAST_TWO_INCOMING_LINK=Toán tử ''{0}'' nên có tối thiểu hai liên kết đến.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ONE_OUT_LINK=Toán tử ''{0}'' nên có tối thiểu một liên kết đi.
#XMSG
VAL_CIRCULAR_REFERENCE_EXITS=Vòng lặp tuần hoàn tồn tại trong chuỗi tác vụ ''{0}''.
#XMSG
VAL_UNCONNECTED_BRANCH=Đối tượng/nhánh ''{0}'' không được kết nối với chuỗi tác vụ.
#XMSG
VAL_TASK_TRIGGERED_PARALLEL_MORE_THAN_ONE_TIME=Tác vụ ''{0}'' được kết nối song song nhiều hơn một lần. Vui lòng loại bỏ trùng lặp để tiếp tục.


txtBegin=Bắt đầu
txtNodesInLink=̣Đối tượng liên quan
#XTOL Tooltip for a context button on diagram
openInNewTab=Mở trong thẻ mới
#XTOL Tooltip for a context button on diagram
txtConnectToTaskOperator=Kéo để kết nối
@emailUpdateError=Lỗi khi cập nhật danh sách thông báo qua email

#XMSG
noTeamPrivilegeTxt=Bạn không có quyền xem danh sách thành viên đối tượng thuê. Sử dụng thẻ Khác để thêm người nhận email theo cách thủ công.

#XFLD Package
@txtPackage=Gói

#XMSG warning for repository package assignment changed. {1} - package name
VAL_PACKAGE_CHANGED=Bạn đã gán đối tượng này cho gói ''{1}''. Nhấp vào Lưu để xác nhận và xác thực thay đổi này. Lưu ý rằng không thể hoàn tác gán cho gói trong trình soạn thảo này sau khi lưu.
#XMSG warning for repository package dependency issue.
MISSING_DEPENDENCY=Phần phụ thuộc của đối tượng ''{0}'' không thể được giải quyết trong ngữ cảnh của gói ''{1}''.

#Warning messages for the folders in the "Others" tab of task chain editor
#XMSG generic warning if getting the objects in a particular folder fails with unhandled errors
getNonRepoArtefactTypeDescendantsError=Đã xảy ra sự cố khi truy xuất đối tượng trong thư mục mà bạn đã chọn.
#BW Process Chains folder related warning messages
#XMSG warning if user does not have the necessary permission related to the SAP BW Bridge space
missingPrivilegeForBridgeSpaceError=Bạn không có quyền cần có để xem hoặc đưa chuỗi quy trình BW vào chuỗi tác vụ.
#XMSG warning if there was a problem retrieving the BW process chains due to bridge space membership, bridge connection issues or other errors
generalBridgeSpaceError=Đã xảy ra sự cố khi truy xuất chuỗi quy trình BW.
#XMSG warning if the BW API for retrieving the BW process chains from the SAP BW Bridge tenant fails
bwApiClientError=Đã xảy ra sự cố khi truy xuất chuỗi quy trình BW từ đối tượng thuê SAP BW Bridge.
#XMSG warning if BW process chains list is empty
bwApiEmptyListError=Không tìm thấy chuỗi quy trình BW nào trong đối tượng thuê SAP BW Bridge.
#XMSG: Task log message for invalid OIDC configuration in the SAP BW Bridge tenant
bwBridgeOIDCConfigStatusInvalid=Phần xác thực OpenID không được định cấu hình cho đối tượng thuê này. Vui lòng sử dụng clientID "{0}" và tokenURL "{1}" để định cấu hình phần xác thực OpenID trong đối tượng thuê cầu nối SAP BW như mô tả trong ghi chú SAP 3536298.
#XMSG BW Process Chain Object has been deleted
VAL_DELETED_BW=Các chuỗi quy trình BW sau có thể đã bị xóa khỏi đối tượng thuê SAP Bridge: {0}
#BW SQL Script Procedures folder related warning messages
#XFLD waring if the Procedure list is empty
procedureAPIEmptyList=Không có thủ tục nào được tạo hoặc đặc quyền EXECUTE chưa được cấp cho lược đồ Open SQL.
#Change digram orientations
changeOrientations=Thay đổi định hướng


# placeholder for the API Path
apiPath=Ví dụ: /job/v1
# placeholder for the status API Path
statusAPIPath=Ví dụ: /job/v1/{id}/status
# valueStateText for the API Path
apiPathRequired=Đường dẫn API được yêu cầu
#placeholder for the CSRF Token URL
csrfTokenURL=Chỉ hỗ trợ HTTPS
# Response Type 1
statusCode=Nhận kết quả từ mã trạng thái HTTP
# Response Type 2
locationHeader=Nhận kết quả từ mã trạng thái HTTP và tiêu đề vị trí
# Response Type 3
responseBody=Nhận kết quả từ mã trạng thái HTTP và phần thân của phản hồi
# placeholder for ID
idPlaceholder=Nhập đường dẫn JSON
# placeholder for indicator value
indicatorValue=Nhập giá trị
# Placeholder for key
keyPlaceholder=Nhập khóa
# Error message for missing key
KeyRequired=Khóa được yêu cầu
# Error message for invalid key format
invalidKeyFormat=Khóa tiêu đề mà bạn đã nhập không được phép. Các tiêu đề hợp lệ là:<ul><li>"prefer"</li><li>Tiêu đề bắt đầu bằng "x-", ngoại trừ "x-forwarded-host"</li><li>Tiêu đề chứa ký tự chữ và số, "-" hoặc "_"</li></ul>
# Error message for missing value
valueRequired=Giá trị được yêu cầu
# Error message for invalid characters in value
invalidValueCharacters=Tiêu đề chứa ký tự không hợp lệ. Các ký tự đặc biệt được phép là:\t ";", ":", "-", "_", ",", "?", "/" và "*"
# Validation message for invoke api path
apiPathValidation=Vui lòng nhập đường dẫn API hợp lệ, ví dụ: /job/v1
# Validation message for JSON path
jsonPathValidation=Vui lòng nhập đường dẫn JSON hợp lệ
# Validation message for success/error indicator
indicatorValueValidation=Giá trị chỉ báo phải bắt đầu bằng ký tự chữ và số và có thể bao gồm các ký tự đặc biệt sau:\t "-" và "_"
# Error message for JSON path
jsonPathRequired=Đường dẫn JSON được yêu cầu
# Error message for invalid API Technical Name
invalidTechnicalName=Tên kỹ thuật chứa ký tự không hợp lệ
# Error message for empty Technical Name
emptyTechnicalName=Tên kỹ thuật được yêu cầu
# Tooltip for codeEditor dialog
codeEditorTooltip=Mở cửa sổ Hiệu chỉnh JSON
# Status Api path validation message
validationStatusAPIPath=Vui lòng nhập đường dẫn API hợp lệ, ví dụ: /job/v1/{id}/status
# CSRF token URL validation message
validationCSRFTokenURL=URL mã thông báo CSRF phải bắt đầu bằng 'https://' và phải là URL hợp lệ
# Select a connection
selectConnection=Chọn kết nối
# Validation message for connection item error
connectionNotReplicated=Kết nối hiện không hợp lệ để chạy tác vụ API. Hãy mở ứng dụng "Kết nối" và nhập lại dữ liệu chứng thực của bạn để sửa kết nối HTTP
# Validation message for API task properties wrong value or missing
VAL_REST_API_PROPERTIES_MISSING=Tác vụ API "{0}" có các giá trị không chính xác hoặc bị thiếu đối với các đặc tính sau: {1}
# Validation message for API connection api is throwing some error or selected connection has some warning
VAL_REST_API_CONNECTION=Tác vụ API "{0}" gặp sự cố với kết nối HTTP. Hãy mở ứng dụng "Kết nối" và kiểm tra kết nối
# Validation message for API task properties when delected connection
VAL_REST_API_CONNECTION_DELETED=Tác vụ API "{0}" có kết nối "{1}" đã được xóa
# Validation message for API task properties when connection cannot be consumed in the API task
VAL_REST_API_CONNECTION_CONSUMPTION_WARNING=Tác vụ API "{0}" có kết nối "{1}" không thể sử dụng để chạy các tác vụ API. Mở ứng dụng "Kết nối" và nhập lại thông tin chứng thực của bạn để thiết lập kết nối cho các tác vụ API
# Information message if synchrnous mode is selected in the API Invocation then we don't need status information
modeInfoText=Ở chế độ Đồng bộ, bảng điều khiển trạng thái không được hiển thị cho các lệnh gọi API
# validation dialog button for Run API Test
saveAnyway=Vẫn lưu
# validation message for technical name
technicalNameValidation=Tên kỹ thuật phải là duy nhất bên trong chuỗi tác vụ. Vui lòng chọn tên kỹ thuật khác
# Connection error message
getHttpConnectionsError=Không nhận được kết nối HTTP
# Msg for the API test run when the Task chain is not saved
taskChainNotSaved=Chuỗi tác vụ phải được lưu trước khi thực hiện chạy thử API
# Msg failed to run API test run
@failedToRunAPI=Không thực hiện được quá trình chạy thử API
# Msg for the API test run when its already in running state
apiTaskRunning=Chạy thử API đang được tiến hành. Bạn có muốn bắt đầu lượt chạy thử mới không?

topToBtm=Trên cùng-Dưới cùng
leftToRight=Bên trái-Bên phải

#XTIT Apache Spark Application Settings
txtHeaderApacheSettings=Cài đặt ứng dụng Apache Spark
#XFLD Use Default
txtUseSpaceDefault=Sử dụng mặc định
#XFLD Application
txtApplication=Ứng dụng
#XFLD Define new settings for this Task
txtNewSettings=Xác định thiết lập mới cho tác vụ này

#XFLD Use Default
txtUseDefault=Sử dụng mặc định




