#XTOL Undo
@undo=Razveljavi
#XTOL Redo
@redo=Obnovi
#XTOL Delete Selected Symbol
@deleteNode=Izbriši izbrani simbol
#XTOL Zoom to Fit
@zoomToFit=Povečaj za prileganje
#XTOL Auto Layout
@autoLayout=Samodejna postavitev
#XMSG
@welcomeText=Povlecite in spustite objekte s plošče na levi na to platno.
#XMSG
@txtNoData=Videti je, da še niste dodali objektov.
#XMSG
VAL_ENTER_VALID_STRING_GEN=Vnesite veljaven niz z dolžino, manjšo od {0}.
#XMSG
@noParametersMsg=Ta postopek nima vhodnih parametrov.
#XMSG
@ip_enterValueMsg="{0}" (Izvajanje postopka skripta SQL) ima vhodne parametre. Nastavite lahko vrednost vsakega izmed njih.
#XTOL
@validateModel=Sporočila preverjanja veljavnosti
#XTOL
@hierarchy=Hierarhija
#XTOL
@columnCount=Število stolpcev
#XFLD
@yes=Da
#XFLD
@no=Ne
#XTIT Save Dialog param
@modelNameTaskChain=Veriga nalog
#properties panel
@lblPropertyTitle=Lastnosti
#XFLD
@lblGeneral=Splošno
#XFLD : Setting
@lblSetting=Nastavitve
#XTXT: Delta Enabled Local Table Deletion
txtDeltaTableDeletion=Izbriši vse v celoti obdelane zapise z vrsto spremembe 'Izbrisano', ki so starejši od
#XTXT: Delta Enabled Local Table Deletion Input field description
desDeltaTableDeletion=Dnevi
#XFLD: Data Activation label
@lblDataActivation=Aktiviranje podatkov
#XFLD: Latency label
@latency=Zakasnitev
#XTEXT: Text for Latency dropdown
txtLatencyDefault=Privzeto
#XTEXT: Text 1 hour
txtOneHour=1 ura
#XTEXT: Text 2 hours
txtTwoHours=2 uri
#XTEXT: Text 3 hours
txtThreeHours=3 ure
#XTEXT: Text 4 hours
txtFourHours=4 ure
#XTEXT: Text 6 hours
txtSixHours=6 ure
#XTEXT: Text 12 hours
txtTwelveHours=12 ur
#XTEXT: Text 1 day
txtOneDay=1 dan
#XFLD: Latency label
@autoRestartHead=Samodejni vnovični zagon
#XFLD
@lblConnectionName=Povezava
#XFLD
@lblQualifiedName=Kvalificirano ime
#XFLD
@lblSpaceName=Ime prostora
#XFLD
@lblLocalSchemaName=Lokalna shema
#XFLD
@lblType=Vrsta objekta
#XFLD
@lblActivity=Dejavnost
#XFLD
@lblTableName=Ime tabele
#XFLD
@lblBusinessName=Poslovno ime
#XFLD
@lblTechnicalName=Tehnično ime
#XFLD
@lblSpace=Prostor
#XFLD
@lblLabel=Oznaka
#XFLD
@lblDataType=Podatkovni tip
#XFLD
@lblDescription=Opis
#XFLD
@lblStorageType=Shramba
#XFLD
@lblHTTPConnection=Generična povezava HTTP
#XFLD
@lblAPISettings=Generične nastavitve API
#XFLD
@header=Glave
#XFLD
@lblInvoke=Poziv za API
#XFLD
@lblMethod=Način
#XFLD
@lblUrl=Osnovni URL
#XFLD
@lblAPIPath=Pot API
#XFLD
@lblMode=Način
#XFLD
@lblCSRFToken=Zahteva za žeton CSRF
#XFLD
@lblTokenURL=URL žetona CSRF
#XFLD
@csrfTokenInfoText=Če ga ne vnesete, bosta uporabljena osnovni URL in pot API.
#XFLD
@lblCSRF=Žeton CSRF
#XFLD
@lblRequestBody=Telo zahteve
#XFLD
@lblFormat=Oblika
#XFLD
@lblResponse=Odziv
#XFLD
@lblId=ID za priklic statusa
#XFLD
@Id=ID
#XFLD
@lblSuccessIndicator=Indikator uspeha
#XFLD
@lblErrorIndicator=Indikator napake
#XFLD
@lblErrorReason=Vzrok za napako
#XFLD
@lblStatus=Status
#XFLD
@lblApiTestRun=Testni zagon za API
#XFLD
@lblRunStatus=Status izvajanja
#XFLD
@lblLastRan=Zadnje izvajanje v
#XFLD
@lblTestRun=Testno izvajanje
#XFLD
@lblDefaultHeader=Privzeta polja glave (pari vrednost–ključ)
#XFLD
@lblAdditionalHeader=Dodatno polje glave
#XFLD
@lblKey=Ključ
#XFLD
@lblEditJSON=Urejanje JSON
#XFLD
@lblTasks=Naloge
#XFLD
@lblRESTfullTask=API
#XFLD: add field button text
btnAddField=Dodaj polje glave
#XFLD: view Details link
@viewDetails=Prikaz podrobnosti
#XTOL
tooltipTxt=Več
#XTOL
delete=Izbriši
#XBTN: ok button text
btnOk=V redu
#XBTN: cancel button text
btnCancel=Prekliči
#XBTN: save button text
btnSave=Shrani
#XMSG Error: Object has duplicated technical name.
VAL_OBJECT_DUPLICATE_NAME=Objekt ''{0}'' že obstaja v odložišču. Vnesite drugo ime.
#XMSG: loading message while opening task chain
loadTaskChain=Prenos verige nalog poteka ...
#model properties
#XFLD
@status_panel=Status izvajanja
#XFLD
@deploy_status_panel=Status postavitve
#XFLD
@status_lbl=Status
#XFLD
@lblLastExecuted=Najnovejše izvajanje
#XFLD
@lblNotExecuted=Še ni izvedeno
#XFLD
@lblNotDeployed=Ni postavljeno
#XFLD
errorDetailsTxt=Statusa izvajanja ni bilo mogoče priklicati
#XBTN: Schedule dropdown menu
SCHEDULE=Časovni načrt
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Uredi časovni načrt
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Izbriši časovni načrt
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Ustvari časovni načrt
#XLNK
viewDetails=Prikaz podrobnosti
#XMSG: error message for reading execution status from backend
backendErrorMsg=Očitno se podatki trenutno ne prenašajo iz strežnika. Poskusite znova priklicati podatke.
#XFLD: Status text for Completed
@statusCompleted=Dokončano
#XFLD: Status text for Running
@statusRunning=Se izvaja
#XFLD: Status text for Failed
@statusFailed=Ni uspelo
#XFLD: Status text for Stopped
@statusStopped=Zaustavljeno
#XFLD: Status text for Stopping
@statusStopping=Se zaustavlja
#XFLD
@LoaderTitle=Prenos poteka
#XFLD
#XFLD: Deploy status, dataflow has  been deployed / deployment date exists
@deployStatusActive=Postavljeno
@deployStatusRevised=Lokalne posodobitve
@deployStatusFailed=Ni uspelo
@deployStatusPending=Postavitev poteka ...
@LoaderText=Priklic podrobnosti iz strežnika
#XMSG
@msgDetailFetchError=Napaka pri priklicu podrobnosti iz strežnika
#XFLD
@executeError=Napaka
#XFLD
@executeWarning=Opozorilo
#XMSG
@executeConfirmDialog=Podatki
#XMSG
@executeunsavederror=Preden verigo nalog zaženete, jo shranite.
#XMSG
@executemodifiederror=V verigi nalog so neshranjene spremembe. Shranite jih.
#XMSG
@executerunningerror=Veriga nalog se trenutno izvaja. Preden zaženete novo izvajanje, počakajte, da se trenutno dokonča.
#XMSG
@btnExecuteAnyway=Vseeno zaženi
#XMSG
@msgExecuteWithValidations=Veriga nalog ima napake preverjanja veljavnosti. Izvajanje verige nalog lahko privede do napake.
#XMSG
@msgRunDeployedVersion=Obstajajo spremembe za postavitev. Izvedena bo zadnja postavljena različica verige nalog. Želite nadaljevati?
#XMSG
#XMSG
@navToMonitoring=Odpri v nadzoru verige nalog
#XMSG
txtOR=ALI
#XFLD
@preview=Predogled
#XMSG
txtand=in
#NOTR
@dateFormat=yyyy-MM-dd
#NOTR
@dateTimeFormatFilter=yyyy-MM-dd HH:mm:ss.
#NOTR
@timeFormat=HH:mm:ss
@lblColum=Stolpec
#XFLD
@lblCondition=Pogoj
#XFLD
@lblValue=Vrednost
#XMSG
@msgJsonInvalid=Verige nalog ni bilo mogoče shraniti zaradi napak v JSON-u. Preverite napake in jih odpravite.
#XTIT
@msgSaveFailTitle=Neveljaven JSON.
#XMSG
NOT_CHAINABLE=Objekta ''{0}'' ni mogoče dodati verigi nalog.
#XMSG
NOT_CHAINABLE_REMOTETABLE=Objekt ''{0}'': Vrsta podvajanja bo spremenjena.
#XMSG
searchTaskChain=Poiščite objekte

#XFLD
@txtTaskChain=Veriga nalog
#XFLD
@txtRemoteTable=Oddaljena tabela
#XFLD
@txtRemoveData=Odstranitev podvojenih podatkov
#XFLD
@txtRemovePersist=Odstranitev trajnih podatkov
#XFLD
@txtView=Pogled
#XFLD
@txtDataFlow=Tok podatkov
#XFLD
@txtIL=Pametno iskanje
#XFLD
@txtTransformationFlow=Tok preoblikovanja
#XFLD
@txtReplicationFlow=Tok replikacije
#XFLD
@txtDeltaLocalTable=Lokalna tabela
#XFLD
@txtBWProcessChain=Procesna veriga BW
#XFLD
@txtSQLScriptProcedure=Postopek skripta SQL
#XFLD
@txtAPI=API
#XFLD
@txtMerge=Združi
#XFLD
@txtOptimize=Optimiziraj
#XFLD
@txtVacuum=Izbris zapisov

#XFLD
@txtRun=Izvedi
#XFLD
@txtPersist=Trajno shrani
#XFLD
@txtReplicate=Podvoji
#XFLD
@txtDelete=Izbriši zapise z vrsto spremembe "Izbrisano"
#XFLD
@txtRunTC=Izvedi verigo nalog
#XFLD
@txtRunBW=Izvajanje procesne verige BW
#XFLD
@txtRunSQLScriptProcedure=Izvajanje postopka skripta SQL

#XFLD
@txtRunDataFlow=Izvedi tok podatkov
#XFLD
@txtPersistView=Trajno shrani pogled
#XFLD
@txtReplicateTable=Podvoji tabelo
#XFLD
@txtRunIL=Zaženi pametno iskanje
#XFLD
@txtRunTF=Zaženi tok preoblikovanja
#XFLD
@txtRunRF=Izvajanje toka podvajanja
#XFLD
@txtRemoveReplicatedData=Odstranitev podvojenih podatkov
#XFLD
@txtRemovePersistedData=Odstranitev trajnih podatkov
#XFLD
@txtMergeData=Združi
#XFLD
@txtOptimizeData=Optimiziraj
#XFLD
@txtVacuumData=Izbris zapisov
#XFLD
@txtRunAPI=Zagon API

#XFLD storage type text
hdlfStorage=Datoteka

@statusNew=Ni postavljeno
@statusActive=Postavljeno
@statusRevised=Lokalne posodobitve
@statusPending=Postavitev poteka ...
@statusChangesToDeploy=Spremembe za postavitev
@statusDesignTimeError=Napaka časa oblikovanja
@statusRunTimeError=Napaka časa izvajanja

#XTIT
txtNodes=Objekti v verigi nalog ({0})
#XBTN
@deleteNodes=Izbriši

#XMSG
@txtDropDataToDiagram=Povlecite in spustite objekte v diagram.
#XMSG
@noData=Ni objektov

#XFLD
@txtTaskPosition=Položaj naloge

#input parameters and variables
#XFLD
lblInputParameters=Vhodni parametri
#XFLD
ip_name=Ime
#XFLD
ip_value=Vrednost
#XTEXT
@noObjectsFound=Objekti niso bili najdeni

#XMSG
@msgExecuteSuccess=Izvajanje verige nalog je začeto.
#XMSG
@msgExecuteFail=Izvajanje verige nalog ni uspelo.
#XMSG
@msgDeployAndRunSuccess=Postavitev in izvajanje verige nalog je začeto.
#XMSG
@msgDeployAndRunFail=Postavitev in izvajanje verige nalog ni uspelo.
#XMSG
@titleExecuteBusy=Počakajte.
#XMSG
@msgExecuteBusy=Poteka priprava vaših podatkov za začetek izvajanja verige nalog.
#XMSG
@msgAPITestRunSuccess=Testni zagon za API se je začel.
#XMSG
@msgAPIExecuteBusy=Poteka priprava vaših podatkov za začetek testnega zagona za API.

#XTOL
txtOpenInEditor=Odpri v urejevalniku
#XTOL
txtPreviewData=Prikaži predogled podatkov

#datapreview
#XMSG
@msgDataPreviewNotSupp=Predogled podatkov ni na voljo za ta objekt.

#XMSG Error: empty model
VAL_MODEL_EMPTY=Videti je, da je vaš model prazen. Dodajte objekte.
#XMSG Error: deploy model
@msgDeployBeforeRun=Preden verigo nalog zaženete, jo morate postaviti.
#BTN: close dialog
btnClose=Zapri

#XMSG Object is not Deployed
VAL_NOT_DEPLOYED=Objekt ''{0}'' mora biti postavljen, če želite nadaljevati verigo nalog.
#XMSG Object is having runtime error
VAL_RUNTIME_ERROR=Objekt ''{0}'' vrne napako časa izvajanja. Preverite objekt.
#XMSG Object is having design time error
VAL_DESIGNTIME_ERROR=Objekt ''{0}'' vrne napako časa oblikovanja. Preverite objekt.
#XMSG Procedure Object has been deleted
VAL_DELETED_PROCEDURE=Naslednji postopki so bili izbrisani: {0}
#XMSG New Parameter added to Procedure object
VAL_NEW_PARAMETER_ADDED=Novi parametri, dodani postopku "{1}": "{0}". Ponovno uvedite verigo nalog.
#XMSG Parameter removed from Procedure object
VAL_PARAMETER_DELETED=Parametri odstranejni iz postopka "{1}": "{0}". Ponovno uvedite verigo nalog.
#XMSG Parameter data type changed in Procedure object
VAL_PARAMETER_DATATYPE_MODIFIED=Vrsta podatkov za parameter  "{0}" spremenjena iz "{1}" v "{2}" v postopku "{3}".
#XMSG Parameter length changed of specific datatype in Procedure object
VAL_PARAMETER_LENGTH_MODIFIED=Dolžina parametra "{0}" spremenjena iz "{1}" v "{2}" v postopku "{3}".
#XMSG Parameter precision changed of Decimal datatype in Procedure object
VAL_PARAMETER_PRECISION_MODIFIED=Natančnost parametra "{0}" spremenjena iz "{1}" v "{2}" v postopku "{3}".
#XMSG Parameter scale changed of Decimal datatype in Procedure object
VAL_PARAMETER_SCALE_MODIFIED=Velikost parametra "{0}" spremenjena iz "{1}" v "{2}" v postopku "{3}".
#XMSG Parameter value not provided in Procedure object
VAL_SQL_SCRIPT_PROCEDURE_PARAMETER_VALUE_MISSING=Niste vnesli vrednosti za vhodne parametre, zahtevane za postopek "{0}": ""{1}.
#XMSG Remote Table activity changes to Remove Replicated Data
VAL_REMOVE_REPLICATED_DATA=Pri naslednjem zagonu verige nalog se bo spremenila vrsta dostopa do podatkov in podatki se ne bodo več prenašali v strežnik v realnem času.
#XMSG Remote Table activity changes
REPLICATION_TYPE_CHANGED=Objekt ''{0}'': Vrsta podvajanja bo spremenjena.

#XFLD
@lblStartNode=Začetno vozlišče
#XFLD
@lblEndNode=Končno vozlišče
#XFLD
@linkTo=Od {0} do {1}
#XTOL
@txtViewDetails=Prikaz podrobnosti

#XTOL
txtOpenImpactLineage=Analiza vpliva in porekla
#XFLD
@emailNotifications=Elektronska obvestila
#XFLD
@txtReset=Ponastavi
#XFLD
@emailMsgWarning=Privzeta e-poštna predloga bo poslana, ko bo e-poštno sporočilo prazno
#XFLD
@notificationSettings=Nastavitve obvestila
#XFLD
@recipientEmailAddr=Naslov prejemnika e-pošte
#XFLD
@emailSubject=Zadeva e-pošte
@emailSubjectText=Veriga naloge <TASKCHAIN_NAME> je zaključena s statusom <STATUS>
#XFLD
@emailMessage=E-poštno sporočilo
@emailMessageText=Spoštovani uporabnik,\n\n Obveščamo vas, da se je veriga naloge:<TASKCHAIN_NAME> run at <START_TIME> zaključila s statusom <STATUS>. Izvedba se je končala <END_TIME>.\n\nPodrobnosti:\nProstor:<SPACE_NAME>\nNapaka:<ERROR_MESSAGE>
@selectEmailRecipientDialogTitle=Izberite naslov prejemnika e-pošte
@tenantMembers=Elementi najemnika
@others=Drugi ({0})
@selectedEmailAddress=Izbrani prejemniki
@add=Dodaj
@placeholder=Rezervirano mesto
@description=Opis
@copyText=Kopiraj besedilo
@taskchainDetailsPlaceholder=Rezervirana mesta za podrobnosti verige naloge
@placeholderCopied=Rezervirano mesto je kopirano
@invalidEmailInfo=Vnesite pravilni e-poštni naslov
@maxMembersAlreadyAdded=Dodali ste že največ 20 elementov
@enterEmailAddress=Vnesite e-poštni naslov uporabnika
@inCorrectPlaceHolder={0} ni pričakovano rezervirano mesto v jedru e-sporočila.
@nsOFF=Ne pošiljajte obvestil
@nsFAILED=Pošljite e-poštno obvestilo, le če se je izvajanje zaključilo z napako
@nsCOMPLETED=Pošljite e-poštno obvestilo, le če se je izvajanje uspešno zaključilo
@nsANY=Pošljite e-poštno obvestilo, ko je izvajanje zaključeno
@phStatus=Status verige nalog – USPELA/NI USPELA
@phTaskChainTName=Tehnično ime verige nalog
@phTaskChainBName=Poslovno ime verige nalog
@phLogId=ID dnevnika izvajanja
@phUser=Uporabnik, ki izvaja verigo nalog
@phLogUILink=Povezava do zaslona dnevnika verige nalog
@phStartTime=Čas začetka izvajanja
@phEndTime=Čas zaključka izvajanja
@phErrMsg=Prvo sporočilo o napaki v dnevniku nalog. Dnevnik je prazen, če je status USPEŠEN
@phSpaceName=Tehnično ime prostora
@emailFormatError=Neveljana oblika elektronskega naslova
@emailFormatErrorInListText=Na seznamu članov, ki niso najemniki, je vnesena neveljavna oblika elektronskega naslova.
@emailSubjectTemplateText=Obvestilo za verigo naloge: $$taskChainName$$ - Space: $$spaceId$$ - Status: $$status$$
@emailMessageTemplateText=Pozdravljeni,\n\n vaša veriga naloge, označena kot $$taskChainName$$, je končala v statusu $$status$$. \n Tukaj je nekaj drugih detajlov o verigi naloge:\n - Tehnično ime verige naloge: $$taskChainName$$ \n - ID dnevnika izvajanja verige naloge: $$logId$$ \n - Uporabnik, ki je izvedel verigo naloge: $$user$$ \n - Povezava do prikaza dnevnika verige naloge:: $$uiLink$$ \n - Datum začetka izvajanja verige naloge: $$startTime$$ \n - Datum konca izvajanja verige naloge: $$endTime$$ \n - Ime prostora: $$spaceId$$ \n
@deleteEmailRecepient=Izbris prejemnika
@emailInputDisabledText=Za dodajanje prejemnikov uporabite verigo nalog.
@tenantOwnerDomainMatchErrorText=Domena e-poštnega naslova se ne ujema z domeno lastnika najemnika: {0}
@totalEmailIdLimitInfoText=Izberete lahko do 20 prejemnikov e-pošte, vključno z uporabniki elementa najemnika in drugimi prejemniki.
@emailDomainInfoText=Dovoljeni so samo e-poštni naslovi z domeno: {0}.
@duplicateEmailErrorText=Na seznamu so podvojeni prejemniki e-pošte.

#XFLD Zorder Title
@txtZorderTitle=Stolpci z razvrščanjem po osi Z v Apache Spark

#XFLD Zorder NoColumn
@txtZorderNoColumn=Stolpci z razvrščanjem po osi Z niso najdeni

#XFLD Zorder Column PrimaryKey
@txtZorderColumnPrimaryKey=Primarni ključ

#XFLD
@lblOperators=Operatorji
addNewSelector=Dodaj novo nalogo
parallelSelector=Dodaj vzporedno nalogo
replaceSelector=Zamenjaj obstoječo nalogo
addparallelbranch=Dodaj kot vzporedno vejo
addplaceholder=Dodaj rezervirano mesto
addALLOperation=Operator ALL
addOROperation=Operator ANY
addplaceholdertocanvas=Dodaj rezervirano mesto na platno
addplaceholderonselected=Dodaj rezervirano mesto po izbrani nalogi
#XFLD: Add Parallel Branch to Selected Task
addparallelbranchonselected=Dodaj vzporedno vejo po izbrani nalogi
addOperator=Dodaj operator
txtAdd=Dodaj
txtPlaceHolderText=Povleci in spusti nalogo tu
@lblLayout=Postavitev

#XMSG
VAL_UNCONNECTED_TASK=Naloga ''{0}'' ni povezana z verigo nalog.
#XMSG
VAL_TASK_SHOULD_HAVE_ONE_INCOMING_LINK=Naloga ''{0}'' mora imeti le eno vhodno povezavo.
#XMSG
VAL_TASK_SHOULD_HAVE_INCOMING_LINK=Naloga ''{0}'' mora imeti eno vhodno povezavo.
#XMSG
VAL_UNCONNECTED_OPERATOR=Operator ''{0}'' ni povezan z verigo nalog.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ATLEAST_TWO_INCOMING_LINK=Operator ''{0}'' mora imeti vsaj dve vhodni povezavi.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ONE_OUT_LINK=Operator ''{0}'' mora imeti vsaj eno vhodno povezavo.
#XMSG
VAL_CIRCULAR_REFERENCE_EXITS=V verigi nalog ''{0}'' obstaja krožna zanka.
#XMSG
VAL_UNCONNECTED_BRANCH=Objekt/veja ''{0}'' ni povezana z verigo nalog.
#XMSG
VAL_TASK_TRIGGERED_PARALLEL_MORE_THAN_ONE_TIME=Naloga ''{0}'' je vzporedno povezana več kot enkrat. Če želite nadaljevati, odstranite dvojnike.


txtBegin=Začni
txtNodesInLink=Vključeni objekti
#XTOL Tooltip for a context button on diagram
openInNewTab=Odpri v novem zavihku
#XTOL Tooltip for a context button on diagram
txtConnectToTaskOperator=Povlecite, da vzpostavite povezavo
@emailUpdateError=Napaka pri posodobitvi seznama e-poštnih obvestil

#XMSG
noTeamPrivilegeTxt=Nimate dovoljenja za prikaz seznama elementov najemnika. Uporabite zavihek Drugo, če želite prejemnike e-pošte dodati ročno.

#XFLD Package
@txtPackage=Paket

#XMSG warning for repository package assignment changed. {1} - package name
VAL_PACKAGE_CHANGED=Ta objekt ste dodelili paketu ''{1}''. Da potrdite in preverite veljavnost te spremembe, kliknite Shrani. Upoštevajte, da v tem urejevalniku ni mogoče razveljaviti dodelitve paketu, ko shranite.
#XMSG warning for repository package dependency issue.
MISSING_DEPENDENCY=Odvisnosti objekta ''{0}'' ni mogoče razrešiti v kontekstu paketa ''{1}''.

#Warning messages for the folders in the "Others" tab of task chain editor
#XMSG generic warning if getting the objects in a particular folder fails with unhandled errors
getNonRepoArtefactTypeDescendantsError=Pri priklicu objektov v mapi, ki ste jo izbrali, je prišlo do težave.
#BW Process Chains folder related warning messages
#XMSG warning if user does not have the necessary permission related to the SAP BW Bridge space
missingPrivilegeForBridgeSpaceError=Nimate potrebnega dovoljenja za ogled ali vključitev verig postopkov BW v verigo nalog.
#XMSG warning if there was a problem retrieving the BW process chains due to bridge space membership, bridge connection issues or other errors
generalBridgeSpaceError=Pri priklicu verig postopkov BW je prišlo do težave. 
#XMSG warning if the BW API for retrieving the BW process chains from the SAP BW Bridge tenant fails
bwApiClientError=Pri priklicu verig postopkov BW iz najemnika SAP BW Bridge je prišlo do težave. 
#XMSG warning if BW process chains list is empty
bwApiEmptyListError=Verige postopkov BW niso najdene v najemniku SAP BW Bridge.
#XMSG: Task log message for invalid OIDC configuration in the SAP BW Bridge tenant
bwBridgeOIDCConfigStatusInvalid=Preverjanje pristnosti za OpenID ni konfigurirano za tega najemnika. Uporabite clientID "{0}" in tokenURL "{1}" za konfiguracijo preverjanja pristnosti za OpenID v najemniku SAP BW Bridge, kot je opisano v SAP-jevem navodilu 3536298.
#XMSG BW Process Chain Object has been deleted
VAL_DELETED_BW=Te verige postopkov BW so morda izbrisane iz najemnika SAP Bridge: {0}
#BW SQL Script Procedures folder related warning messages
#XFLD waring if the Procedure list is empty
procedureAPIEmptyList=Noben postopek ni bil ustvarjen ali pa pravica EXECUTE ni bila dodeljena shemi Open SQL.
#Change digram orientations
changeOrientations=Sprememba usmerjenosti


# placeholder for the API Path
apiPath=Na primer: /job/v1
# placeholder for the status API Path
statusAPIPath=Na primer: /job/v1/{id}/status
# valueStateText for the API Path
apiPathRequired=Pot API je zahtevana
#placeholder for the CSRF Token URL
csrfTokenURL=Podprt je samo HTTPS
# Response Type 1
statusCode=Pridobivanje rezultata iz kode statusa HTTP
# Response Type 2
locationHeader=Pridobivanje rezultata iz kode statusa HTTP in glave lokacije
# Response Type 3
responseBody=Pridobivanje rezultata iz kode statusa HTTP in telesa odziva
# placeholder for ID
idPlaceholder=Vnos poti JSON
# placeholder for indicator value
indicatorValue=Vnos vrednosti
# Placeholder for key
keyPlaceholder=Vnos ključa
# Error message for missing key
KeyRequired=Ključ je zahtevan
# Error message for invalid key format
invalidKeyFormat=Vneseni ključ glave ni dovoljen. Veljavne glave so:<ul><li>"preferirano"</li><li>Glave, ki se začnejo z "x-", razen "x-forwarded-host"</li><li>Glave, ki vsebujejo črkovnoštevilske znake, "-", ali "_"</li></ul>
# Error message for missing value
valueRequired=Vrednost je obvezna
# Error message for invalid characters in value
invalidValueCharacters=Glava vsebuje neveljavne znake. Posebni znaki, ki niso dovoljeni, so:\t ";", ":", "-", "_", ",", "?", "/" in "*"
# Validation message for invoke api path
apiPathValidation=Vnesite veljavno pot API, na primer: /job/v1
# Validation message for JSON path
jsonPathValidation=Vnesite veljavno pot JSON
# Validation message for success/error indicator
indicatorValueValidation=Vrednost indikatorja se mora začeti z znakovnoštevilskim znakom in lahko vsebuje naslednje posebne znake:\t "-" ter "_"
# Error message for JSON path
jsonPathRequired=Pot JSON je obvezna
# Error message for invalid API Technical Name
invalidTechnicalName=Tehnično ime vsebuje neveljavne znake
# Error message for empty Technical Name
emptyTechnicalName=Tehnično ime je obvezno
# Tooltip for codeEditor dialog
codeEditorTooltip=Odpiranje okna za urejanje JSON
# Status Api path validation message
validationStatusAPIPath=Vnesite veljavno pot API, na primer: /job/v1/{id}/status
# CSRF token URL validation message
validationCSRFTokenURL=URL žetona CSRF se mora začeti z 'https://' in mora biti veljaven URL
# Select a connection
selectConnection=Izbira povezave
# Validation message for connection item error
connectionNotReplicated=Povezava trenutno ni veljavna za izvajanje nalog API. Odprite aplikacijo "Povezave" in znova vnesite svoje poverilnice, da popravite povezavo HTTP.
# Validation message for API task properties wrong value or missing
VAL_REST_API_PROPERTIES_MISSING=Naloga API "{0}" ima nepravilne ali manjkajoče vrednosti za naslednje lastnosti: {1}
# Validation message for API connection api is throwing some error or selected connection has some warning
VAL_REST_API_CONNECTION=V nalogi API "{0}" je prišlo do težave s povezavo HTTP. Odprite aplikacijo "Povezave" in preverite povezavo
# Validation message for API task properties when delected connection
VAL_REST_API_CONNECTION_DELETED=Naloga API "{0}" ima izbrisano povezavo "{1}"
# Validation message for API task properties when connection cannot be consumed in the API task
VAL_REST_API_CONNECTION_CONSUMPTION_WARNING=Naloga API "{0}" ima povezavo "{1}", ki je ni mogoče uporabiti za izvajanje nalog API. Odprite aplikacijo "Povezave" in ponovno vnesite svoje poverilnice za vzpostavitev povezljivosti z nalogami API
# Information message if synchrnous mode is selected in the API Invocation then we don't need status information
modeInfoText=V sinhronem načinu plošča statusa ni prikazana za pozive API
# validation dialog button for Run API Test
saveAnyway=Vseeno shrani
# validation message for technical name
technicalNameValidation=Tehnično ime mora biti enolično z verigo nalog. Izberite drugo tehnično ime
# Connection error message
getHttpConnectionsError=Povezav HTTP ni mogoče priklicati
# Msg for the API test run when the Task chain is not saved
taskChainNotSaved=Veriga naloge mora biti shranjena pred začetkom testnega zagona za API.
# Msg failed to run API test run
@failedToRunAPI=Testnega zagona za API ni mogoče zagnati.
# Msg for the API test run when its already in running state
apiTaskRunning=Testno izvajanje API-ja je že v teku. Želite zagnati novo testno izvajanje?

topToBtm=Od zgoraj navzdol
leftToRight=Od leve proti desni

#XTIT Apache Spark Application Settings
txtHeaderApacheSettings=Nastavitve za aplikacijo Apache Spark
#XFLD Use Default
txtUseSpaceDefault=Uporabi privzeto
#XFLD Application
txtApplication=Aplikacija
#XFLD Define new settings for this Task
txtNewSettings=Določitev novih nastavitev za to nalogo

#XFLD Use Default
txtUseDefault=Uporabi privzeto




