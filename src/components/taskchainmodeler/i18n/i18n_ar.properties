#XTOL Undo
@undo=تراجع
#XTOL Redo
@redo=إعادة
#XTOL Delete Selected Symbol
@deleteNode=حذف الرمز المحدد
#XTOL Zoom to Fit
@zoomToFit=تكبير/تصغير للملاءمة
#XTOL Auto Layout
@autoLayout=المخطط التلقائي
#XMSG
@welcomeText=قم بسحب الكائنات وإسقاطها من اللوحة الموجودة على اليسار إلى منطقة العرض هذه.
#XMSG
@txtNoData=يبدو أنك لم تقم بإضافة أي كائنات حتى الآن.
#XMSG
VAL_ENTER_VALID_STRING_GEN=يُرجى إدخال سلسلة صالحة بطول أقل من يساوي {0}.
#XMSG
@noParametersMsg=لا يحتوي هذا الإجراء على معامِلات إدخال.
#XMSG
@ip_enterValueMsg=يحتوي "{0}" (تشغيل إجراء البرنامج النصي لـ SQL) على معامِلات إدخال. ويمكنك تعيين قيمة لكل منها.
#XTOL
@validateModel=رسائل التحقق من الصحة
#XTOL
@hierarchy=الهرمية
#XTOL
@columnCount=عدد الأعمدة
#XFLD
@yes=نعم
#XFLD
@no=لا
#XTIT Save Dialog param
@modelNameTaskChain=سلسلة المهام
#properties panel
@lblPropertyTitle=الخصائص
#XFLD
@lblGeneral=عام
#XFLD : Setting
@lblSetting=الإعدادات
#XTXT: Delta Enabled Local Table Deletion
txtDeltaTableDeletion=حذف جميع السجلات التي تمت معالجتها بالكامل باستخدام نوع التغيير 'محذوف' والتي تكون أقدم من
#XTXT: Delta Enabled Local Table Deletion Input field description
desDeltaTableDeletion=أيام
#XFLD: Data Activation label
@lblDataActivation=تنشيط البيانات
#XFLD: Latency label
@latency=التأخير
#XTEXT: Text for Latency dropdown
txtLatencyDefault=افتراضي
#XTEXT: Text 1 hour
txtOneHour=1 ساعة
#XTEXT: Text 2 hours
txtTwoHours=2 ساعة
#XTEXT: Text 3 hours
txtThreeHours=3 ساعة
#XTEXT: Text 4 hours
txtFourHours=4 ساعة
#XTEXT: Text 6 hours
txtSixHours=6 ساعة
#XTEXT: Text 12 hours
txtTwelveHours=12 ساعة
#XTEXT: Text 1 day
txtOneDay=يوم واحد
#XFLD: Latency label
@autoRestartHead=إعادة التشغيل تلقائيًا
#XFLD
@lblConnectionName=اتصال
#XFLD
@lblQualifiedName=الاسم المؤهَّل
#XFLD
@lblSpaceName=اسم المساحة
#XFLD
@lblLocalSchemaName=المخطط المحلي
#XFLD
@lblType=نوع الكائن
#XFLD
@lblActivity=النشاط
#XFLD
@lblTableName=اسم الجدول
#XFLD
@lblBusinessName=الاسم التجاري
#XFLD
@lblTechnicalName=الاسم التقني
#XFLD
@lblSpace=المساحة
#XFLD
@lblLabel=التسمية
#XFLD
@lblDataType=نوع البيانات
#XFLD
@lblDescription=الوصف
#XFLD
@lblStorageType=التخزين
#XFLD
@lblHTTPConnection=اتصال HTTP العام
#XFLD
@lblAPISettings=إعدادات واجهة برمجة التطبيقات العامة
#XFLD
@header=المقدمات
#XFLD
@lblInvoke=استدعاء واجهة برمجة التطبيقات
#XFLD
@lblMethod=الأسلوب
#XFLD
@lblUrl=الرابط الأساسي
#XFLD
@lblAPIPath=مسار واجهة برمجة التطبيقات
#XFLD
@lblMode=النمط
#XFLD
@lblCSRFToken=يتطلب الرمز المميز لتزييف الطلب عبر المواقع (CSRF)
#XFLD
@lblTokenURL=رابط الرمز المميز لـ CSRF
#XFLD
@csrfTokenInfoText=إذا لم يتم إدخاله، فسيتم استخدام الرابط الأساسي ومسار واجهة برمجة التطبيقات
#XFLD
@lblCSRF=الرمز المميز لتزييف الطلب عبر المواقع (CSRF)
#XFLD
@lblRequestBody=النص الأساسي للطلب
#XFLD
@lblFormat=التنسيق
#XFLD
@lblResponse=الاستجابة
#XFLD
@lblId=معرف استرجاع الحالة
#XFLD
@Id=المعرف
#XFLD
@lblSuccessIndicator=مؤشر النجاح
#XFLD
@lblErrorIndicator=مؤشر الخطأ
#XFLD
@lblErrorReason=سبب الخطأ
#XFLD
@lblStatus=الحالة
#XFLD
@lblApiTestRun=تشغيل اختباري لواجهة برمجة التطبيقات
#XFLD
@lblRunStatus=حالة التشغيل
#XFLD
@lblLastRan=تاريخ آخر تشغيل
#XFLD
@lblTestRun=تشغيل اختباري
#XFLD
@lblDefaultHeader=حقول المقدمة الافتراضية (أزواج القيم الأساسية)
#XFLD
@lblAdditionalHeader=حقل المقدمة الإضافي
#XFLD
@lblKey=المفتاح
#XFLD
@lblEditJSON=تحرير JSON
#XFLD
@lblTasks=المهام
#XFLD
@lblRESTfullTask=واجهة برمجة التطبيقات
#XFLD: add field button text
btnAddField=إضافة حقل المقدمة
#XFLD: view Details link
@viewDetails=عرض التفاصيل
#XTOL
tooltipTxt=المزيد
#XTOL
delete=حذف
#XBTN: ok button text
btnOk=موافق
#XBTN: cancel button text
btnCancel=إلغاء
#XBTN: save button text
btnSave=حفظ
#XMSG Error: Object has duplicated technical name.
VAL_OBJECT_DUPLICATE_NAME=الكائن ''{0}'' موجود بالفعل في المستودع. برجاء إدخال اسم آخر.
#XMSG: loading message while opening task chain
loadTaskChain=جارٍ تحميل سلسلة المهام...
#model properties
#XFLD
@status_panel=حالة التشغيل
#XFLD
@deploy_status_panel=حالة النشر
#XFLD
@status_lbl=الحالة
#XFLD
@lblLastExecuted=آخر تشغيل
#XFLD
@lblNotExecuted=لم يتم التشغيل حتى الآن
#XFLD
@lblNotDeployed=غير منشور
#XFLD
errorDetailsTxt=تعذر استدعاء حالة التشغيل
#XBTN: Schedule dropdown menu
SCHEDULE=الجدول الزمني
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=تحرير الجدول الزمني
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=حذف الجدول الزمني
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=إنشاء الجدول الزمني
#XLNK
viewDetails=عرض التفاصيل
#XMSG: error message for reading execution status from backend
backendErrorMsg=يبدو أنه لم يتم تحميل البيانات من الخادم في الوقت الحالي. حاول استدعاء البيانات مرة أخرى.
#XFLD: Status text for Completed
@statusCompleted=مكتمل
#XFLD: Status text for Running
@statusRunning=قيد التشغيل
#XFLD: Status text for Failed
@statusFailed=فشل
#XFLD: Status text for Stopped
@statusStopped=موقوف
#XFLD: Status text for Stopping
@statusStopping=جارٍ الإيقاف
#XFLD
@LoaderTitle=جارٍ التحميل
#XFLD
#XFLD: Deploy status, dataflow has  been deployed / deployment date exists
@deployStatusActive=منشور
@deployStatusRevised=التحديثات المحلية
@deployStatusFailed=فشل
@deployStatusPending=جارٍ النشر...
@LoaderText=استدعاء التفاصيل من الخادم
#XMSG
@msgDetailFetchError=حدث خطأ أثناء استدعاء التفاصيل من الخادم
#XFLD
@executeError=خطأ
#XFLD
@executeWarning=تحذير
#XMSG
@executeConfirmDialog=معلومات
#XMSG
@executeunsavederror=احفظ سلسلة المهام الخاصة بك قبل تشغيلها.
#XMSG
@executemodifiederror=هناك تغييرات غير محفوظة في سلسلة المهام. يُرجى حفظها.
#XMSG
@executerunningerror=سلسلة المهام قيد التشغيل حاليًا. انتظر حتى اكتمال التشغيل الحالي قبل البدء بسلسلة جديدة.
#XMSG
@btnExecuteAnyway=تشغيل على أي حال
#XMSG
@msgExecuteWithValidations=سلسلة المهام بها أخطاء في التحقق من الصحة. قد يؤدي تشغيل سلسلة المهام إلى الفشل.
#XMSG
@msgRunDeployedVersion=توجد تغييرات للنشر. سيتم تشغيل آخر إصدار منشور لسلسلة المهام. هل تريد المتابعة؟
#XMSG
#XMSG
@navToMonitoring=فتح في مراقب سلسلة المهام
#XMSG
txtOR=أو
#XFLD
@preview=معاينة
#XMSG
txtand=و
#NOTR
@dateFormat=yyyy-MM-dd
#NOTR
@dateTimeFormatFilter=yyyy-MM-dd HH:mm:ss.
#NOTR
@timeFormat=HH:mm:ss
@lblColum=العمود
#XFLD
@lblCondition=الشرط
#XFLD
@lblValue=القيمة
#XMSG
@msgJsonInvalid=تعذر حفظ سلسلة المهام نظرًا لوجود أخطاء في JSON. يُرجى التحقق والحل.
#XTIT
@msgSaveFailTitle=JSON غير صالح.
#XMSG
NOT_CHAINABLE=لا يمكن إضافة الكائن ''{0}'' إلى سلسلة المهام.
#XMSG
NOT_CHAINABLE_REMOTETABLE=الكائن ''{0}'': سيتم تغيير نوع النسخ المتماثل.
#XMSG
searchTaskChain=بحث عن كائنات

#XFLD
@txtTaskChain=سلسلة المهام
#XFLD
@txtRemoteTable=الجدول البعيد
#XFLD
@txtRemoveData=إزالة البيانات المنسوخة نسخًا متماثلًا
#XFLD
@txtRemovePersist=إزالة البيانات المخزنة بصفة دائمة
#XFLD
@txtView=عرض
#XFLD
@txtDataFlow=تدفق البيانات
#XFLD
@txtIL=البحث الذكي
#XFLD
@txtTransformationFlow=تدفق التحويل
#XFLD
@txtReplicationFlow=تدفق النسخ المتماثل
#XFLD
@txtDeltaLocalTable=الجدول المحلي
#XFLD
@txtBWProcessChain=سلسلة عمليات BW
#XFLD
@txtSQLScriptProcedure=إجراء البرنامج النصي لـ SQL
#XFLD
@txtAPI=واجهة برمجة التطبيقات
#XFLD
@txtMerge=دمج
#XFLD
@txtOptimize=تحسين
#XFLD
@txtVacuum=حذف السجلات

#XFLD
@txtRun=تشغيل
#XFLD
@txtPersist=تخزين بصفة دائمة
#XFLD
@txtReplicate=نسخ متماثل
#XFLD
@txtDelete=حذف السجلات بنوع التغيير 'محذوف'
#XFLD
@txtRunTC=تشغيل سلسلة المهام
#XFLD
@txtRunBW=تشغيل سلسلة عمليات BW
#XFLD
@txtRunSQLScriptProcedure=تشغيل إجراء البرنامج النصي لـ SQL

#XFLD
@txtRunDataFlow=تشغيل تدفق البيانات
#XFLD
@txtPersistView=تخزين العرض بصفة دائمة
#XFLD
@txtReplicateTable=تكرار الجدول
#XFLD
@txtRunIL=تشغيل البحث الذكي
#XFLD
@txtRunTF=تشغيل تدفق التحويل
#XFLD
@txtRunRF=تشغيل تدفق النسخ المتماثل
#XFLD
@txtRemoveReplicatedData=إزالة البيانات المنسوخة نسخًا متماثلًا
#XFLD
@txtRemovePersistedData=إزالة البيانات المخزنة بصفة دائمة
#XFLD
@txtMergeData=دمج
#XFLD
@txtOptimizeData=تحسين
#XFLD
@txtVacuumData=حذف السجلات
#XFLD
@txtRunAPI=تشغيل واجهة برمجة التطبيقات

#XFLD storage type text
hdlfStorage=ملف

@statusNew=غير منشور
@statusActive=منشور
@statusRevised=التحديثات المحلية
@statusPending=جارٍ النشر...
@statusChangesToDeploy=التغييرات للنشر
@statusDesignTimeError=خطأ في وقت التصميم
@statusRunTimeError=خطأ في وقت التشغيل

#XTIT
txtNodes=الكائنات في سلسلة المهام ({0})
#XBTN
@deleteNodes=حذف

#XMSG
@txtDropDataToDiagram=قم بسحب الكائنات وإسقاطها في المخطط.
#XMSG
@noData=دون كائنات

#XFLD
@txtTaskPosition=وضع المهمة

#input parameters and variables
#XFLD
lblInputParameters=معامِلات الإدخال
#XFLD
ip_name=الاسم
#XFLD
ip_value=القيمة
#XTEXT
@noObjectsFound=لم يتم العثور على أي كائنات

#XMSG
@msgExecuteSuccess=بدأ تشغيل سلسلة المهام.
#XMSG
@msgExecuteFail=فشل تشغيل سلسلة المهام.
#XMSG
@msgDeployAndRunSuccess=بدأ نشر سلسلة المهام وتشغيلها.
#XMSG
@msgDeployAndRunFail=فشل نشر سلسلة المهام وتشغيلها.
#XMSG
@titleExecuteBusy=الرجاء الانتظار.
#XMSG
@msgExecuteBusy=نحن نحضر بياناتك لبدء تشغيل سلسلة المهام.
#XMSG
@msgAPITestRunSuccess=بدأ التشغيل الاختباري لواجهة برمجة التطبيقات.
#XMSG
@msgAPIExecuteBusy=نقوم بتحضير بياناتك لبدء تشغيل التشغيل الاختباري لواجهة برمجة التطبيقات.

#XTOL
txtOpenInEditor=فتح في المحرِّر
#XTOL
txtPreviewData=معاينة البيانات

#datapreview
#XMSG
@msgDataPreviewNotSupp=معاينة البيانات غير متوفرة لهذا الكائن.

#XMSG Error: empty model
VAL_MODEL_EMPTY=يبدو أن نموذجك فارغ. يُرجى إضافة بعض الكائنات.
#XMSG Error: deploy model
@msgDeployBeforeRun=تحتاج إلى نشر سلسلة المهام قبل تشغيلها.
#BTN: close dialog
btnClose=إغلاق

#XMSG Object is not Deployed
VAL_NOT_DEPLOYED=يجب نشر الكائن ''{0}'' للمتابعة بسلسلة المهام.
#XMSG Object is having runtime error
VAL_RUNTIME_ERROR=الكائن ''{0}'' يُرجع خطأ في وقت التشغيل. يُرجى فحص الكائن.
#XMSG Object is having design time error
VAL_DESIGNTIME_ERROR=الكائن ''{0}'' يُرجع خطأ في وقت التصميم. يُرجى فحص الكائن.
#XMSG Procedure Object has been deleted
VAL_DELETED_PROCEDURE=تم حذف الإجراءات التالية: {0}
#XMSG New Parameter added to Procedure object
VAL_NEW_PARAMETER_ADDED=تمت إضافة معامِلات جديدة إلى الإجراء "{1}": "{0}". يُرجى إعادة نشر سلسلة المهام.
#XMSG Parameter removed from Procedure object
VAL_PARAMETER_DELETED=تمت إزالة المعامِلات من الإجراء {1}": "{0}". يُرجى إعادة نشر سلسلة المهام.
#XMSG Parameter data type changed in Procedure object
VAL_PARAMETER_DATATYPE_MODIFIED=تم تغيير نوع بيانات المعامل "{0}" من "{1}" إلى "{2}" في الإجراء "{3}".
#XMSG Parameter length changed of specific datatype in Procedure object
VAL_PARAMETER_LENGTH_MODIFIED=تم تغيير طول المعامل "{0}" من "{1}" إلى "{2}" في الإجراء "{3}".
#XMSG Parameter precision changed of Decimal datatype in Procedure object
VAL_PARAMETER_PRECISION_MODIFIED=تم تغيير دقة المعامل "{0}" من "{1}" إلى "{2}" في الإجراء "{3}".
#XMSG Parameter scale changed of Decimal datatype in Procedure object
VAL_PARAMETER_SCALE_MODIFIED=تم تغيير مقياس المعامل "{0}" من "{1}" إلى "{2}" في الإجراء "{3}".
#XMSG Parameter value not provided in Procedure object
VAL_SQL_SCRIPT_PROCEDURE_PARAMETER_VALUE_MISSING=لم تقم بإدخال قيم لمعامِلات الإدخال المطلوبة للإجراء "{0}": "{1}"
#XMSG Remote Table activity changes to Remove Replicated Data
VAL_REMOVE_REPLICATED_DATA=التشغيل التالي لسلسلة المهام سيؤدي إلى تغيير نوع الوصول إلى البيانات، ولن يتم تحميل البيانات فيما بعد في الوقت الفعلي.
#XMSG Remote Table activity changes
REPLICATION_TYPE_CHANGED=الكائن "{0}": سيتم تغيير نوع النسخ المتماثل.

#XFLD
@lblStartNode=بدء العقدة
#XFLD
@lblEndNode=إنهاء العقدة
#XFLD
@linkTo={0} إلى {1}
#XTOL
@txtViewDetails=عرض التفاصيل

#XTOL
txtOpenImpactLineage=تحليل التأثيرات وأصل البيانات
#XFLD
@emailNotifications=إشعارات البريد الإلكتروني
#XFLD
@txtReset=إعادة تعيين
#XFLD
@emailMsgWarning=سيتم إرسال البريد الإلكتروني للقالب الافتراضي عندما تكون رسالة البريد الإلكتروني فارغة
#XFLD
@notificationSettings=إعدادات الإشعار
#XFLD
@recipientEmailAddr=عنوان البريد الإلكتروني للمستلِم
#XFLD
@emailSubject=موضوع البريد الإلكتروني
@emailSubjectText=تم إكمال سلسلة المهام <TASKCHAIN_NAME> بالحالة <STATUS>
#XFLD
@emailMessage=رسالة البريد الإلكتروني
@emailMessageText=عزيزي المستخدم،\n\n هذا لإعلامك بأنه تم إنهاء سلسلة المهام هذه:<TASKCHAIN_NAME> التي يتم تشغيلها على <START_TIME> بالحالة <STATUS>. تم إنهاء التنفيذ في <END_TIME>.\n\nالتفاصيل:\nالمساحة:<SPACE_NAME>\nالخطأ:<ERROR_MESSAGE>
@selectEmailRecipientDialogTitle=تحديد عنوان البريد الإلكتروني للمستلِم
@tenantMembers=عناصر الوحدة المستضافة
@others=({0}) الأخرى
@selectedEmailAddress=المستلِمون
@add=إضافة
@placeholder=عنصر نائب
@description=الوصف
@copyText=نسخ النص
@taskchainDetailsPlaceholder=العناصر النائبة لتفاصيل سلسلة المهام
@placeholderCopied=تم نسخ العنصر نائب
@invalidEmailInfo=إدخال عنوان البريد الإلكتروني الصحيح
@maxMembersAlreadyAdded=أضفت بالفعل العدد الأقصى من 20 عنصرًا
@enterEmailAddress=إدخال عنوان البريد الإلكتروني للمستخدم
@inCorrectPlaceHolder={0} ليس عنصرًا نائبًا متوقعًا في نص البريد الإلكتروني.
@nsOFF=لا ترسل أي إشعارات
@nsFAILED=إرسال إشعار بالبريد الإلكتروني فقط عند اكتمال التشغيل مع وجود خطأ
@nsCOMPLETED=إرسال إشعار بالبريد الإلكتروني فقط عند اكتمال التشغيل بنجاح
@nsANY=إرسال البريد الإلكتروني فقط عند اكتمال التشغيل
@phStatus=حالة سلسلة المهام - ناجح|فاشل
@phTaskChainTName=الاسم التقني لسلسلة المهام
@phTaskChainBName=الاسم التجاري لسلسلة المهام
@phLogId=معرف السجل للتشغيل
@phUser=المستخدم الذي يقوم بتشغيل سلسلة المهام
@phLogUILink=رابط لعرض سجل سلسلة المهام
@phStartTime=وقت بدء التشغيل
@phEndTime=وقت انتهاء التشغيل
@phErrMsg=أول رسالة خطأ في سجل المهام. السجل فارغ في حالة النجاح
@phSpaceName=الاسم التقني للمساحة
@emailFormatError=تنسيق بريد إلكتروني غير صالح
@emailFormatErrorInListText=تنسيق بريد إلكتروني غير صالح مدخل في قائمة عناصر غير الوحدة المستضافة.
@emailSubjectTemplateText=إشعار سلسلة المهام: $$taskChainName$$ - المساحة: $$spaceId$$ - الحالة: $$status$$
@emailMessageTemplateText=مرحبًا\n\n تمت تسمية سلسلة المهام الخاصة بك $$taskChainName$$ انتهى بالحالة $$status$$. \n فيما يلي بعض التفاصيل الأخرى حول سلسلة المهام:\n - اسم سلسلة المهام التقني: $$taskChainName$$ \n - معرف سجل تشغيل سلسلة المهام: $$logId$$ \n - مستخدم قام بتشغيل سلسلة المهام: $$user$$ \n - ارتباط بعرض سجل سلسلة المهام: $$uiLink$$ \n - وقت بداية تشغيل سلسلة المهام: $$startTime$$ \n - وقت نهاية تشغيل سلسلة المهام: $$endTime$$ \n - اسم المساحة: $$spaceId$$ \n
@deleteEmailRecepient=حذف المستلِم
@emailInputDisabledText=يُرجى نشر سلسلة المهام لإضافة مستلمي البريد الإلكتروني.
@tenantOwnerDomainMatchErrorText=مجال عنوان البريد الإلكتروني لا يتطابق مع مجال مالك الوحدة المستضافة: {0}
@totalEmailIdLimitInfoText=يمكنك تحديد ما يصل إلى 20 مستلمًا للبريد الإلكتروني بما في ذلك المستخدمين الأعضاء في الوحدة المستضافة والمستلمين الآخرين.
@emailDomainInfoText=عناوين البريد الإلكتروني بالمجال فقط: {0} مقبولة.
@duplicateEmailErrorText=هناك مستلمون مكررون للبريد الإلكتروني في القائمة.

#XFLD Zorder Title
@txtZorderTitle=أعمدة الترتيب Z لـ Apache Spark

#XFLD Zorder NoColumn
@txtZorderNoColumn=لم يتم العثور على أعمدة الترتيب Z 

#XFLD Zorder Column PrimaryKey
@txtZorderColumnPrimaryKey=المفتاح الأساسي

#XFLD
@lblOperators=المعامِلات
addNewSelector=إضافة كمهمة جديدة
parallelSelector=إضافة كمهمة متوازية
replaceSelector=استبدال المهمة الموجودة
addparallelbranch=إضافة كفرع متوازٍ
addplaceholder=إضافة عنصر نائب
addALLOperation=كل المعاملات
addOROperation=أي معامل
addplaceholdertocanvas=إضافة عنصر نائب إلى منطقة عرض
addplaceholderonselected=إضافة عنصر نائب بعد المهمة المحددة
#XFLD: Add Parallel Branch to Selected Task
addparallelbranchonselected=إضافة فرع متوازٍ بعد المهمة المحددة
addOperator=إضافة معامل
txtAdd=إضافة
txtPlaceHolderText=سحب وإسقاط مهمة هنا
@lblLayout=المخطط

#XMSG
VAL_UNCONNECTED_TASK=المهمة ''{0}'' غير متصلة بسلسلة المهام.
#XMSG
VAL_TASK_SHOULD_HAVE_ONE_INCOMING_LINK=المهمة ''{0}'' يجب أن تحتوي على ارتباط وارد واحد فقط.
#XMSG
VAL_TASK_SHOULD_HAVE_INCOMING_LINK=المهمة ''{0}'' يجب أن تحتوي على ارتباط وارد واحد.
#XMSG
VAL_UNCONNECTED_OPERATOR=المعامل ''{0}'' غير متصل بسلسلة المهام.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ATLEAST_TWO_INCOMING_LINK=المعامل ''{0}'' يجب أن يحتوي على ارتباطين واردين على الأقل.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ONE_OUT_LINK=المعامل ''{0}'' يجب أن يحتوي على ارتباط صادر واحد على الأقل.
#XMSG
VAL_CIRCULAR_REFERENCE_EXITS=تكرار دائري موجود في سلسلة المهام ''{0}''.
#XMSG
VAL_UNCONNECTED_BRANCH=الكائن/الفرع ''{0}'' غير متصل بسلسلة المهام.
#XMSG
VAL_TASK_TRIGGERED_PARALLEL_MORE_THAN_ONE_TIME=المهمة ''{0}'' متصلة بالتوازي أكثر من مرة. يُرجى إزالة التكرارات للمتابعة.


txtBegin=بدء
txtNodesInLink=الكائنات المتضمنة
#XTOL Tooltip for a context button on diagram
openInNewTab=فتح في علامة تبويب جديدة
#XTOL Tooltip for a context button on diagram
txtConnectToTaskOperator=السحب للاتصال
@emailUpdateError=حدث خطأ في تحديث قائمة إشعارات البريد الإلكتروني

#XMSG
noTeamPrivilegeTxt=ليس لديك إذن للاطلاع على قائمة عناصر الوحدة المستضافة. استخدم علامة التبويب أخرى لإضافة مستلمي البريد الإلكتروني يدويًا.

#XFLD Package
@txtPackage=الحزمة

#XMSG warning for repository package assignment changed. {1} - package name
VAL_PACKAGE_CHANGED=لقد قمت بتعيين هذا الكائن للحزمة ''{1}''. انقر فوق حفظ لتأكيد هذا التغيير والتحقق من صحته. لاحظ أنه لا يمكن التراجع عن التعيين لحزمة في هذا المحرر بعد الحفظ.
#XMSG warning for repository package dependency issue.
MISSING_DEPENDENCY=لا يمكن حل تبعيات الكائن ''{0}'' في سياق الحزمة ''{1}''.

#Warning messages for the folders in the "Others" tab of task chain editor
#XMSG generic warning if getting the objects in a particular folder fails with unhandled errors
getNonRepoArtefactTypeDescendantsError=حدثت مشكلة أثناء استرجاع الكائنات في المجلد الذي حددته.
#BW Process Chains folder related warning messages
#XMSG warning if user does not have the necessary permission related to the SAP BW Bridge space
missingPrivilegeForBridgeSpaceError=ليس لديك الإذن اللازم لعرض سلاسل عمليات BW أو تضمينها في سلسلة مهام.
#XMSG warning if there was a problem retrieving the BW process chains due to bridge space membership, bridge connection issues or other errors
generalBridgeSpaceError=حدثت مشكلة أثناء استرجاع سلاسل عمليات BW.
#XMSG warning if the BW API for retrieving the BW process chains from the SAP BW Bridge tenant fails
bwApiClientError=حدثت مشكلة أثناء استرجاع سلاسل عمليات BW من الوحدة المستضافة لوحدة توصيل SAP BW.
#XMSG warning if BW process chains list is empty
bwApiEmptyListError=لم يتم العثور على سلاسل عمليات BW في الوحدة المستضافة لوحدة توصيل SAP BW.
#XMSG: Task log message for invalid OIDC configuration in the SAP BW Bridge tenant
bwBridgeOIDCConfigStatusInvalid=لم يتم تكوين مصادقة OpenID للوحدة المستضافة هذه. يُرجى استخدام clientID "{0}" وtokenURL "{1}" لتكوين مصادقة OpenID في الوحدة المستضافة لوحدة توصيل SAP BW كما هو موضح في ملاحظة SAP رقم 3536298.
#XMSG BW Process Chain Object has been deleted
VAL_DELETED_BW=ربما تم حذف سلاسل عمليات BW التالية من الوحدة المستضافة لوحدة توصيل SAP: {0}
#BW SQL Script Procedures folder related warning messages
#XFLD waring if the Procedure list is empty
procedureAPIEmptyList=لم يتم إنشاء أي إجراءات أو لم يتم منح امتياز EXECUTE (تنفيذ) إلى مخطط SQL المفتوح.
#Change digram orientations
changeOrientations=تغيير الاتجاهات


# placeholder for the API Path
apiPath=على سبيل المثال: /job/v1
# placeholder for the status API Path
statusAPIPath=على سبيل المثال: /job/v1/{id}/status
# valueStateText for the API Path
apiPathRequired=مسار واجهة برمجة التطبيقات مطلوب
#placeholder for the CSRF Token URL
csrfTokenURL=HTTPS مدعوم فقط
# Response Type 1
statusCode=استدعاء نتيجة من رمز حالة HTTP
# Response Type 2
locationHeader=استدعاء نتيجة من رمز حالة HTTP ومقدمة الموقع
# Response Type 3
responseBody=استدعاء نتيجة من النص الأساسي للاستجابة ورمز حالة HTTP
# placeholder for ID
idPlaceholder=إدخال مسار JSON
# placeholder for indicator value
indicatorValue=إدخال قيمة
# Placeholder for key
keyPlaceholder=إدخال مفتاح
# Error message for missing key
KeyRequired=المفتاح مطلوب
# Error message for invalid key format
invalidKeyFormat=غير مسموح بمفتاح المقدمة الذي أدخلته. المقدمات الصالحة هي: مقدمات <ul><li>"prefer"</li><li> تبدأ بـ "x-"، باستثناء مقدمات "x-forwarded-host"</li><li> التي تحتوي على أحرف أبجدية رقمية أو "-" أو "_"</li></ul>
# Error message for missing value
valueRequired=القيمة مطلوبة
# Error message for invalid characters in value
invalidValueCharacters=تحتوي المقدمة على حروف غير صالحة. الحروف الخاصة المسموح بها هي:\t ";", ":", "-", "_", ",", "?"، "/"، و"*"
# Validation message for invoke api path
apiPathValidation=يرجى إدخال مسار واجهة برمجة تطبيقات صالح، على سبيل المثال: /job/v1
# Validation message for JSON path
jsonPathValidation=يرجى إدخال مسار JSON صالح
# Validation message for success/error indicator
indicatorValueValidation=يجب أن تبدأ قيمة المؤشر بحرف أبجدي رقمي ويمكن أن تتضمن الأحرف الخاصة التالية:\t "-" و"_"
# Error message for JSON path
jsonPathRequired=مسار JSON مطلوب
# Error message for invalid API Technical Name
invalidTechnicalName=يحتوي الاسم التقني على حروف غير صالحة
# Error message for empty Technical Name
emptyTechnicalName=الاسم التقني مطلوب
# Tooltip for codeEditor dialog
codeEditorTooltip=فتح نافذة تحرير JSON
# Status Api path validation message
validationStatusAPIPath=يرجى إدخال مسار واجهة برمجة تطبيقات صالح، على سبيل المثال: /job/v1/{id}/status
# CSRF token URL validation message
validationCSRFTokenURL=يجب أن يبدأ رابط الرمز المميز لتزييف الطلب عبر المواقع (CSRF) بـ 'https://' وأن يكون رابطًا صالحًا
# Select a connection
selectConnection=تحديد الاتصال
# Validation message for connection item error
connectionNotReplicated=الاتصال غير صالح حاليًا لتشغيل مهام واجهة برمجة التطبيقات. افتح تطبيق "الاتصالات" وأعد إدخال بيانات اعتمادك لإصلاح اتصال HTTP
# Validation message for API task properties wrong value or missing
VAL_REST_API_PROPERTIES_MISSING=تحتوي مهمة واجهة برمجة التطبيقات "{0}" على قيم غير صحيحة أو مفقودة للخصائص التالية: {1}
# Validation message for API connection api is throwing some error or selected connection has some warning
VAL_REST_API_CONNECTION=تحتوي مهمة واجهة برمجة التطبيقات "{0}" على مشكلة في اتصال HTTP. افتح تطبيق "الاتصالات" وتحقق من الاتصال
# Validation message for API task properties when delected connection
VAL_REST_API_CONNECTION_DELETED=تحتوي مهمة واجهة برمجة التطبيقات "{0}" على اتصال "{1}" محذوف
# Validation message for API task properties when connection cannot be consumed in the API task
VAL_REST_API_CONNECTION_CONSUMPTION_WARNING=تحتوي مهمة واجهة برمجة التطبيقات "{0}" على اتصال "{1}" لا يمكن استخدامه لتشغيل مهام واجهة برمجة التطبيقات. افتح تطبيق "الاتصالات" وأعد إدخال بيانات اعتمادك لإنشاء اتصال لمهام واجهة برمجة التطبيقات
# Information message if synchrnous mode is selected in the API Invocation then we don't need status information
modeInfoText=في النمط المتزامن، لم يتم عرض لوحة الحالة لعمليات فوترة واجهة برمجة التطبيقات
# validation dialog button for Run API Test
saveAnyway=حفظ على أي حال
# validation message for technical name
technicalNameValidation=يجب أن يكون الاسم التقني فريدًا في سلسلة المهام. الرجاء اختيار اسم تقني آخر
# Connection error message
getHttpConnectionsError=فشل استدعاء اتصالات HTTP
# Msg for the API test run when the Task chain is not saved
taskChainNotSaved=يجب حفظ سلسلة المهام قبل تشغيل التشغيل الاختباري لواجهة برمجة التطبيقات
# Msg failed to run API test run
@failedToRunAPI=فشل تشغيل التشغيل الاختباري لواجهة برمجة التطبيقات
# Msg for the API test run when its already in running state
apiTaskRunning=التشغيل الاختباري لواجهة برمجة التطبيقات قيد التنفيذ بالفعل. هل تريد بدء تشغيل اختباري جديد؟

topToBtm=أعلى إلى أسفل
leftToRight=يمين إلى يسار

#XTIT Apache Spark Application Settings
txtHeaderApacheSettings=إعدادات تطبيق Apache Spark
#XFLD Use Default
txtUseSpaceDefault=استخدام الإعداد الافتراضي
#XFLD Application
txtApplication=التطبيق
#XFLD Define new settings for this Task
txtNewSettings=تحديد إعدادات جديدة لهذه المهمة

#XFLD Use Default
txtUseDefault=استخدام الإعداد الافتراضي




