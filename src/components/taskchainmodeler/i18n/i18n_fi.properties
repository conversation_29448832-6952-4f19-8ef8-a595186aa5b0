#XTOL Undo
@undo=Kumoa
#XTOL Redo
@redo=Tee uudelleen
#XTOL Delete Selected Symbol
@deleteNode=Poista valittu symboli
#XTOL Zoom to Fit
@zoomToFit=Zoomaa sopivaksi
#XTOL Auto Layout
@autoLayout=Automaattinen asettelu
#XMSG
@welcomeText=Vedä ja pudota objektit vasemmasta paneelista tähän pohjaan.
#XMSG
@txtNoData=Vaikuttaa siltä, ettet ole vielä lisännyt objektia.
#XMSG
VAL_ENTER_VALID_STRING_GEN=Syötä kelpaava merkkijono, jonka pituus on pienempi tai yhtä suuri kuin {0}.
#XMSG
@noParametersMsg=Tällä menettelyllä ei ole syöttöparametreja.
#XMSG
@ip_enterValueMsg=Kohteella "{0}" (suorita SQL Script -menettely) on syöttöparametreja. Voit asettaa arvon niille kaikille.
#XTOL
@validateModel=Validointi-ilmoitukset
#XTOL
@hierarchy=Hierarkia
#XTOL
@columnCount=Sarakkeiden lukumäärä
#XFLD
@yes=Kyllä
#XFLD
@no=Ei
#XTIT Save Dialog param
@modelNameTaskChain=Tehtäväketju
#properties panel
@lblPropertyTitle=Ominaisuudet
#XFLD
@lblGeneral=Yleinen
#XFLD : Setting
@lblSetting=Asetukset
#XTXT: Delta Enabled Local Table Deletion
txtDeltaTableDeletion=Poista kaikki kokonaan käsitellyt tietueet, joiden muutoslaji on "Poistettu" ja jotka ovat vanhempia kuin
#XTXT: Delta Enabled Local Table Deletion Input field description
desDeltaTableDeletion=pv
#XFLD: Data Activation label
@lblDataActivation=Tietojen aktivointi
#XFLD: Latency label
@latency=Latenssi
#XTEXT: Text for Latency dropdown
txtLatencyDefault=Oletusarvo
#XTEXT: Text 1 hour
txtOneHour=1 tunti
#XTEXT: Text 2 hours
txtTwoHours=2 tuntia
#XTEXT: Text 3 hours
txtThreeHours=3 tuntia
#XTEXT: Text 4 hours
txtFourHours=4 tuntia
#XTEXT: Text 6 hours
txtSixHours=6 tuntia
#XTEXT: Text 12 hours
txtTwelveHours=12 tuntia
#XTEXT: Text 1 day
txtOneDay=1 päivä
#XFLD: Latency label
@autoRestartHead=Automaattinen uudelleenkäynnistys
#XFLD
@lblConnectionName=Yhteys
#XFLD
@lblQualifiedName=Kvalifioitu nimi
#XFLD
@lblSpaceName=Tilan nimi
#XFLD
@lblLocalSchemaName=Paikallinen kaavio
#XFLD
@lblType=Objektityyppi
#XFLD
@lblActivity=Toiminto
#XFLD
@lblTableName=Taulun nimi
#XFLD
@lblBusinessName=Liiketoiminnallinen nimi
#XFLD
@lblTechnicalName=Tekninen nimi
#XFLD
@lblSpace=Tila
#XFLD
@lblLabel=Tunniste
#XFLD
@lblDataType=Tietotyyppi
#XFLD
@lblDescription=Kuvaus
#XFLD
@lblStorageType=Tallennustila
#XFLD
@lblHTTPConnection=Geneerinen HTTP-yhteys
#XFLD
@lblAPISettings=Geneeriset API-asetukset
#XFLD
@header=Otsikot
#XFLD
@lblInvoke=API-kutsu
#XFLD
@lblMethod=Menetelmä
#XFLD
@lblUrl=Perus-URL
#XFLD
@lblAPIPath=API-polku
#XFLD
@lblMode=Tila
#XFLD
@lblCSRFToken=Vaadi CSRF-tunniste
#XFLD
@lblTokenURL=CSRF-tunnisteen URL
#XFLD
@csrfTokenInfoText=Jos ei syötetä, käytetään perus-URL:ää ja API-polkua
#XFLD
@lblCSRF=CSRF-tunniste
#XFLD
@lblRequestBody=Pyynnön runko
#XFLD
@lblFormat=Muoto
#XFLD
@lblResponse=Vastaus
#XFLD
@lblId=Tunnus tilan hakua varten
#XFLD
@Id=Tunnus
#XFLD
@lblSuccessIndicator=Onnistumistunnus
#XFLD
@lblErrorIndicator=Virhetunnus
#XFLD
@lblErrorReason=Virheen syy
#XFLD
@lblStatus=Tila
#XFLD
@lblApiTestRun=API-testiajo
#XFLD
@lblRunStatus=Ajon tila
#XFLD
@lblLastRan=Viimeisen ajon päivämäärä
#XFLD
@lblTestRun=Testiajo
#XFLD
@lblDefaultHeader=Oletusotsikkokentät (avain-arvoparit)
#XFLD
@lblAdditionalHeader=Lisäotsikkokenttä
#XFLD
@lblKey=Avain
#XFLD
@lblEditJSON=Muokkaa JSON:ää
#XFLD
@lblTasks=Tehtävät
#XFLD
@lblRESTfullTask=API
#XFLD: add field button text
btnAddField=Lisää otsikkokenttä
#XFLD: view Details link
@viewDetails=Näytä lisätiedot
#XTOL
tooltipTxt=Lisää
#XTOL
delete=Poista
#XBTN: ok button text
btnOk=OK
#XBTN: cancel button text
btnCancel=Peruuta
#XBTN: save button text
btnSave=Tallenna
#XMSG Error: Object has duplicated technical name.
VAL_OBJECT_DUPLICATE_NAME=Objekti ''{0}'' on jo hakemistossa. Syötä jokin muu nimi.
#XMSG: loading message while opening task chain
loadTaskChain=Tehtäväketjua ladataan...
#model properties
#XFLD
@status_panel=Ajon tila
#XFLD
@deploy_status_panel=Käyttöönoton tila
#XFLD
@status_lbl=Tila
#XFLD
@lblLastExecuted=Viimeinen ajo
#XFLD
@lblNotExecuted=Ei vielä ajettu
#XFLD
@lblNotDeployed=Ei otettu käyttöön
#XFLD
errorDetailsTxt=Ajon tilaa ei voitu hakea
#XBTN: Schedule dropdown menu
SCHEDULE=Ajoita
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Muokkaa aikataulua
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Poista aikataulu
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Luo aikataulu
#XLNK
viewDetails=Näytä lisätiedot
#XMSG: error message for reading execution status from backend
backendErrorMsg=Näyttää siltä, että tiedot eivät tällä hetkellä lataudu palvelimesta. Yritä hakea tiedot uudelleen.
#XFLD: Status text for Completed
@statusCompleted=Päätetty
#XFLD: Status text for Running
@statusRunning=Käynnissä
#XFLD: Status text for Failed
@statusFailed=Epäonnistui
#XFLD: Status text for Stopped
@statusStopped=Pysäytetty
#XFLD: Status text for Stopping
@statusStopping=Pysäytetään
#XFLD
@LoaderTitle=Ladataan
#XFLD
#XFLD: Deploy status, dataflow has  been deployed / deployment date exists
@deployStatusActive=Otettu käyttöön
@deployStatusRevised=Paikalliset päivitykset
@deployStatusFailed=Epäonnistui
@deployStatusPending=Otetaan käyttöön...
@LoaderText=Haetaan lisätietoja palvelimesta
#XMSG
@msgDetailFetchError=Virhe haettaessa lisätietoja palvelimesta
#XFLD
@executeError=Virhe
#XFLD
@executeWarning=Varoitus
#XMSG
@executeConfirmDialog=Tiedot
#XMSG
@executeunsavederror=Tallenna tehtäväketjusi ennen sen ajamista.
#XMSG
@executemodifiederror=Tehtäväketjussa on tallentamattomia muutoksia. Tallenna se.
#XMSG
@executerunningerror=Tehtäväketjua ajetaan tällä hetkellä. Odota, että nykyinen ajo on suoritettu loppuun ennen kuin aloitat uutta.
#XMSG
@btnExecuteAnyway=Aja silti
#XMSG
@msgExecuteWithValidations=Tehtäväketjulla on validointivirheitä. Tehtäväketjun ajo saattaa päättyä epäonnistumiseen.
#XMSG
@msgRunDeployedVersion=Käyttöön otettavia muutoksia on olemassa. Viimeksi käyttöön otettu tehtäväketju ajetaan. Haluatko jatkaa?
#XMSG
#XMSG
@navToMonitoring=Avaa tehtävienvalvonnassa
#XMSG
txtOR=TAI
#XFLD
@preview=Esikatselu
#XMSG
txtand=ja
#NOTR
@dateFormat=yyyy-MM-dd
#NOTR
@dateTimeFormatFilter=yyyy-MM-dd HH:mm:ss.
#NOTR
@timeFormat=HH:mm:ss
@lblColum=Sarake
#XFLD
@lblCondition=Ehto
#XFLD
@lblValue=Arvo
#XMSG
@msgJsonInvalid=Tehtäväketjua ei voitu tallentaa, koska JSON sisältää virheitä. Tarkista ja ratkaise virheet.
#XTIT
@msgSaveFailTitle=Virheellinen JSON.
#XMSG
NOT_CHAINABLE=Objektia ''{0}'' ei voi lisätä tehtäväketjuun.
#XMSG
NOT_CHAINABLE_REMOTETABLE=Objekti ”{0}”: replikointityyppi muutetaan.
#XMSG
searchTaskChain=Hae objekteja

#XFLD
@txtTaskChain=Tehtäväketju
#XFLD
@txtRemoteTable=Etätaulu
#XFLD
@txtRemoveData=Poista replikoidut tiedot
#XFLD
@txtRemovePersist=Poista pysyvät tiedot
#XFLD
@txtView=Näytä
#XFLD
@txtDataFlow=Tietovirta
#XFLD
@txtIL=Älykäs haku
#XFLD
@txtTransformationFlow=Muuntovirta
#XFLD
@txtReplicationFlow=Replikointivirta
#XFLD
@txtDeltaLocalTable=Paikallinen taulu
#XFLD
@txtBWProcessChain=BW-prosessiketju
#XFLD
@txtSQLScriptProcedure=SQL Script -menettely
#XFLD
@txtAPI=API
#XFLD
@txtMerge=Yhdistä
#XFLD
@txtOptimize=Optimoi
#XFLD
@txtVacuum=Poista tietueet

#XFLD
@txtRun=Aja
#XFLD
@txtPersist=Säilytä
#XFLD
@txtReplicate=Replikoi
#XFLD
@txtDelete=Poista tietueet, joiden muutoslaji on "Poistettu"
#XFLD
@txtRunTC=Aja tehtäväketju
#XFLD
@txtRunBW=Suorita BW-prosessiketju
#XFLD
@txtRunSQLScriptProcedure=Suorita SQL Script -menettely

#XFLD
@txtRunDataFlow=Aja tietovirta
#XFLD
@txtPersistView=Säilytä näkymä
#XFLD
@txtReplicateTable=Replikoi taulu
#XFLD
@txtRunIL=Aja älykäs haku
#XFLD
@txtRunTF=Aja muuntovirta
#XFLD
@txtRunRF=Suorita replikointivirta
#XFLD
@txtRemoveReplicatedData=Poista replikoidut tiedot
#XFLD
@txtRemovePersistedData=Poista pysyvät tiedot
#XFLD
@txtMergeData=Yhdistä
#XFLD
@txtOptimizeData=Optimoi
#XFLD
@txtVacuumData=Poista tietueet
#XFLD
@txtRunAPI=Aja API

#XFLD storage type text
hdlfStorage=Tiedosto

@statusNew=Ei otettu käyttöön
@statusActive=Otettu käyttöön
@statusRevised=Paikalliset päivitykset
@statusPending=Otetaan käyttöön...
@statusChangesToDeploy=Käyttöön otettavat muutokset
@statusDesignTimeError=Suunnitteluaikavirhe
@statusRunTimeError=Ajoaikavirhe

#XTIT
txtNodes=Objektit tehtäväketjussa ({0})
#XBTN
@deleteNodes=Poista

#XMSG
@txtDropDataToDiagram=Vedä ja pudota objektit kaavioon.
#XMSG
@noData=Ei objekteja

#XFLD
@txtTaskPosition=Tehtävän sijainti

#input parameters and variables
#XFLD
lblInputParameters=Syöttöparametrit
#XFLD
ip_name=Nimi
#XFLD
ip_value=Arvo
#XTEXT
@noObjectsFound=Objekteja ei löytynyt

#XMSG
@msgExecuteSuccess=Tehtäväketjun ajo on käynnistetty.
#XMSG
@msgExecuteFail=Tehtäväketjun ajo epäonnistui.
#XMSG
@msgDeployAndRunSuccess=Tehtäväketjun käyttöönotto ja ajo käynnistetty.
#XMSG
@msgDeployAndRunFail=Tehtäväketjun käyttöönotto ja ajo epäonnistui
#XMSG
@titleExecuteBusy=Odota.
#XMSG
@msgExecuteBusy=Valmistelemme tietojasi tehtäväketjun ajoa varten.
#XMSG
@msgAPITestRunSuccess=API-testiajo on käynnistynyt.
#XMSG
@msgAPIExecuteBusy=Valmistelemme tietojasi API-testiajon käynnistystä varten.

#XTOL
txtOpenInEditor=Avaa muokkausohjelmassa
#XTOL
txtPreviewData=Esikatsele tiedot

#datapreview
#XMSG
@msgDataPreviewNotSupp=Tietojen esikatselu ei ole käytettävissä tätä objektia varten.

#XMSG Error: empty model
VAL_MODEL_EMPTY=Malli näyttää olevan tyhjä. Lisää objekteja.
#XMSG Error: deploy model
@msgDeployBeforeRun=Sinun on otettava tehtäväketju käyttöön ennen sen ajoa.
#BTN: close dialog
btnClose=Sulje

#XMSG Object is not Deployed
VAL_NOT_DEPLOYED=Objekti''{0}'' on otettava käyttöön, jotta tehtäväketjua voidaan jatkaa.
#XMSG Object is having runtime error
VAL_RUNTIME_ERROR=Objekti ''{0}'' palauttaa ajoaikavirheen. Tarkista objekti.
#XMSG Object is having design time error
VAL_DESIGNTIME_ERROR=Objekti ''{0}'' palauttaa suunnitteluaikavirheen. Tarkista objekti.
#XMSG Procedure Object has been deleted
VAL_DELETED_PROCEDURE=Seuraavat menettelyt on poistettu: {0}
#XMSG New Parameter added to Procedure object
VAL_NEW_PARAMETER_ADDED=Menettelyyn "{1}": "{0}" lisättiin uusia parametrejä. Käynnistä tehtäväketju uudelleen.
#XMSG Parameter removed from Procedure object
VAL_PARAMETER_DELETED=Menettelystä "{1}": "{0}" poistettiin parametrejä. Käynnistä tehtäväketju uudelleen.
#XMSG Parameter data type changed in Procedure object
VAL_PARAMETER_DATATYPE_MODIFIED=Parametrin "{0}" tietotyyppi muutettu tietotyypistä "{1}" tietotyypiksi "{2}" menettelyssä "{3}".
#XMSG Parameter length changed of specific datatype in Procedure object
VAL_PARAMETER_LENGTH_MODIFIED=Parametrin "{0}" pituus muutettu pituudesta "{1}" pituudeksi "{2}" menettelyssä "{3}".
#XMSG Parameter precision changed of Decimal datatype in Procedure object
VAL_PARAMETER_PRECISION_MODIFIED=Parametrin "{0}" tarkkuus muutettu tarkkuudesta "{1}" tarkkuudeksi "{2}" menettelyssä "{3}".
#XMSG Parameter scale changed of Decimal datatype in Procedure object
VAL_PARAMETER_SCALE_MODIFIED=Parametrin "{0}" asteikko muutettu asteikosta "{1}" asteikoksi "{2}" menettelyssä "{3}". 
#XMSG Parameter value not provided in Procedure object
VAL_SQL_SCRIPT_PROCEDURE_PARAMETER_VALUE_MISSING=Et ole syöttänyt menettelyn "{0}": "{1}" vaatimia syöttöparametrien arvoja
#XMSG Remote Table activity changes to Remove Replicated Data
VAL_REMOVE_REPLICATED_DATA=Tehtäväketjun seuraava ajo muuttaa tietojen käytön tyyppiä, eikä tietoja enää ladata reaaliaikaisesti.
#XMSG Remote Table activity changes
REPLICATION_TYPE_CHANGED=Objekti ”{0}”: replikointityyppi muutetaan.

#XFLD
@lblStartNode=Alkusolmu
#XFLD
@lblEndNode=Loppusolmu
#XFLD
@linkTo={0} - {1}
#XTOL
@txtViewDetails=Näytä lisätiedot

#XTOL
txtOpenImpactLineage=Vaikutus- ja alkuperäanalyysi
#XFLD
@emailNotifications=Sähköposti-ilmoitukset
#XFLD
@txtReset=Palauta
#XFLD
@emailMsgWarning=Oletusmallisähköposti lähetetään, kun sähköpostiviesti on tyhjä
#XFLD
@notificationSettings=Ilmoitusasetukset
#XFLD
@recipientEmailAddr=Vastaanottajan sähköpostiosoite
#XFLD
@emailSubject=Sähköpostin aihe
@emailSubjectText=Tehtäväketju <TASKCHAIN_NAME> päätetty tilassa <STATUS>
#XFLD
@emailMessage=Sähköpostiviesti
@emailMessageText=Hyvä käyttäjä\n\n Tämä ilmoitus koskee tehtäväketjun <TASKCHAIN_NAME> ajoa <START_TIME>, joka on päättynyt tilassa <STATUS>. Suoritus päättyi <END_TIME>.\n\nLisätiedot:\nTila:<SPACE_NAME>\nVirhe:<ERROR_MESSAGE>
@selectEmailRecipientDialogTitle=Valitse vastaanottajan sähköpostiosoite
@tenantMembers=Vuokralaisen jäsenet
@others=Muut({0})
@selectedEmailAddress=Valitut vastaanottajat
@add=Lisää
@placeholder=Paikanvaraaja
@description=Kuvaus
@copyText=Kopioi teksti
@taskchainDetailsPlaceholder=Tehtäväketjun lisätietojen paikanvaraajat
@placeholderCopied=Paikanvaraaja on kopioitu
@invalidEmailInfo=Syötä oikea sähköpostiosoite
@maxMembersAlreadyAdded=Olet jo lisännyt sallitun jäsenten enimmäismäärän, joka on 20
@enterEmailAddress=Syötä käyttäjän sähköpostiosoite
@inCorrectPlaceHolder={0} ei ole odotettu paikanvaraaja sähköpostin tekstissä.
@nsOFF=Älä lähetä ilmoituksia
@nsFAILED=Lähetä sähköposti-ilmoitus vain, kun ajo on päätetty virheen kanssa
@nsCOMPLETED=Lähetä sähköposti-ilmoitus vain, kun ajo on päätetty onnistuneesti
@nsANY=Lähetä sähköposti-ilmoitus, kun ajo on päätetty
@phStatus=Tehtäväketjun tila - ONNISTUI|EPÄONNISTUI
@phTaskChainTName=Tehtäväketjun tekninen nimi
@phTaskChainBName=Tehtäväketjun liiketoiminnallinen nimi
@phLogId=Ajon lokitunnus
@phUser=Käyttäjä, joka ajaa tehtäväketjua
@phLogUILink=Linkki tehtäväketjun lokinäyttöön
@phStartTime=Ajon alkuaika
@phEndTime=Ajon loppuaika
@phErrMsg=Tehtävälokin ensimmäinen virheilmoitus. Loki on tyhjä, jos tila on ONNISTUMINEN
@phSpaceName=Tilan tekninen nimi
@emailFormatError=Virheellinen sähköpostimuoto
@emailFormatErrorInListText=Syötetty virheellinen sähköpostimuoto ei-vuokralaisjäsenten luetteloon.
@emailSubjectTemplateText=Ilmoitus tehtäväketjusta: $$taskChainName$$ - Tila: $$spaceId$$ - Ketjun tila: $$status$$
@emailMessageTemplateText=Hei\n\n Tehtäväketjusi, jonka nimi on $$taskChainName$$, on päättynyt tilaan $$status$$. \n\ Tässä on joitain muita tietoja tehtäväketjusta:\n - Tehtäväketjun tekninen nimi: $$taskChainName$$ \n - Tehtäväketjun suorituksen lokitunnus: $$logId$$ \n - Tehtäväketjun suorittanut käyttäjä: $$user$$ \n - Linkki tehtäväketjun lokinäyttöön: $$uiLink$$ \n - Tehtäväketjun suorituksen alkamisaika: $$startTime$$ \n - Tehtäväketjun suorituksen päättymisaika: $$endTime$$ \n - Tilan nimi: $$spaceId$$ \n
@deleteEmailRecepient=Poista vastaanottaja
@emailInputDisabledText=Ota tehtäväketju käyttöön sähköpostin vastaanottajien lisäämiseksi.
@tenantOwnerDomainMatchErrorText=Sähköpostiosoitteen toimialue ei vastaa vuokralaisen omistajan toimialuetta: {0}
@totalEmailIdLimitInfoText=Voit valita enintään 20 sähköpostin vastaanottajaa, mukaan lukien vuokralaisen jäsenkäyttäjät ja muut vastaanottajat.
@emailDomainInfoText=Vain sähköpostiosoitteet, joilla on toimialue: {0}, hyväksytään.
@duplicateEmailErrorText=Luettelossa on päällekkäisiä sähköpostin vastaanottajia.

#XFLD Zorder Title
@txtZorderTitle=Apache Sparkin Z-järjestyksessä olevat sarakkeet

#XFLD Zorder NoColumn
@txtZorderNoColumn=Z-järjestyksessä olevia sarakkeita ei löytynyt

#XFLD Zorder Column PrimaryKey
@txtZorderColumnPrimaryKey=Ensisijainen avain

#XFLD
@lblOperators=Operaattorit
addNewSelector=Lisää uutena tehtävänä
parallelSelector=Lisää rinnakkaisena tehtävänä
replaceSelector=Korvaa olemassa olevia tehtävä
addparallelbranch=Lisää rinnakkaisena haarana
addplaceholder=Lisää paikanvaraaja
addALLOperation=ALL-operattori
addOROperation=ANY-operaattori
addplaceholdertocanvas=Lisää paikanvaraaja pohjaan
addplaceholderonselected=Lisää paikanvaraaja valitun tehtävän jälkeen
#XFLD: Add Parallel Branch to Selected Task
addparallelbranchonselected=Lisää rinnakkainen haara valitun tehtävän jälkeen
addOperator=Lisää operaattori
txtAdd=Lisää
txtPlaceHolderText=Vedä ja pudota tehtävä tähän
@lblLayout=Asettelu

#XMSG
VAL_UNCONNECTED_TASK=Tehtävää ''{0}'' ei ole yhdistetty tehtäväketjuun.
#XMSG
VAL_TASK_SHOULD_HAVE_ONE_INCOMING_LINK=Tehtävällä ''{0}'' pitäisi olla vain yksi saapuva linkki.
#XMSG
VAL_TASK_SHOULD_HAVE_INCOMING_LINK=Tehtävällä ''{0}'' pitäisi olla yksi saapuva linkki.
#XMSG
VAL_UNCONNECTED_OPERATOR=Operaattoria ''{0}'' ei ole yhdistetty tehtäväketjuun.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ATLEAST_TWO_INCOMING_LINK=Operaattorilla ''{0}''’ pitäisi olla ainakin kaksi saapuvaa linkkiä.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ONE_OUT_LINK=Operaattorilla ''{0}''’ pitäisi olla ainakin yksi lähtevä linkki.
#XMSG
VAL_CIRCULAR_REFERENCE_EXITS=Kehäsilmukka tehtäväketjussa ''{0}''.
#XMSG
VAL_UNCONNECTED_BRANCH=Objektia/haaraa ''{0}''’ ei ole yhdistetty tehtäväketjuun.
#XMSG
VAL_TASK_TRIGGERED_PARALLEL_MORE_THAN_ONE_TIME=Tehtävä ''{0}''’ on yhdistetty rinnakkaisesti useammin kuin kerran. Poista kaksoiskappaleet jatkaaksesi.


txtBegin=Aloita
txtNodesInLink=Liittyvät objektit
#XTOL Tooltip for a context button on diagram
openInNewTab=Avaa uudessa välilehdessä
#XTOL Tooltip for a context button on diagram
txtConnectToTaskOperator=Yhdistä vetämällä
@emailUpdateError=Virhe sähköposti-ilmoitusluettelon päivityksessä

#XMSG
noTeamPrivilegeTxt=Sinulla ei ole oikeutta tarkastella vuokralaisen jäsenten luetteloa. Käytä Muut-välilehteä lisätäksesi sähköpostin vastaanottajia manuaalisesti.

#XFLD Package
@txtPackage=Paketti

#XMSG warning for repository package assignment changed. {1} - package name
VAL_PACKAGE_CHANGED=Olet kohdistanut tämän objektin pakettiin ”{1}”. Napsauta Tallenna vahvistaaksesi ja validoidaksesi muutoksen. Huomaa, että kohdistamista pakettiin ei voi kumota tässä editorissa tallentamisen jälkeen.
#XMSG warning for repository package dependency issue.
MISSING_DEPENDENCY=Objektin {0} sidonnaisuuksia ei voi ratkaista paketin {1} kontekstissa.

#Warning messages for the folders in the "Others" tab of task chain editor
#XMSG generic warning if getting the objects in a particular folder fails with unhandled errors
getNonRepoArtefactTypeDescendantsError=Ongelma objektien noudossa valitsemastasi kansiosta.
#BW Process Chains folder related warning messages
#XMSG warning if user does not have the necessary permission related to the SAP BW Bridge space
missingPrivilegeForBridgeSpaceError=Sinulla ei ole tarvittavia oikeuksia näyttää tai sisällyttää tehtäväketjun BW-prosessiketjuja.
#XMSG warning if there was a problem retrieving the BW process chains due to bridge space membership, bridge connection issues or other errors
generalBridgeSpaceError=Ongelma BW-prosessiketjujen noudossa.
#XMSG warning if the BW API for retrieving the BW process chains from the SAP BW Bridge tenant fails
bwApiClientError=Ongelma BW-prosessiketjujen noudossa SAP BW -siltavuokralaisesta.
#XMSG warning if BW process chains list is empty
bwApiEmptyListError=BW-prosessiketjuja ei löytynyt SAP BW -siltavuokralaisesta.
#XMSG: Task log message for invalid OIDC configuration in the SAP BW Bridge tenant
bwBridgeOIDCConfigStatusInvalid=OpenID-todentamista ei ole konfiguroitu tätä vuokralaista varten. Käytä clientID:tä "{0}" ja tokenURL:ää "{1}" OpenID-todentamiseen SAP BW -sillan vuokralaisessa, kuten on kuvattu SAP-ohjeessa 3536298.
#XMSG BW Process Chain Object has been deleted
VAL_DELETED_BW=Seuraavat BW-prosessiketjut on mahdollisesti poistettu SAP-siltavuokralaisesta: {0}
#BW SQL Script Procedures folder related warning messages
#XFLD waring if the Procedure list is empty
procedureAPIEmptyList=Menettelyitä ei ole luotu tai EXECUTE-käyttöoikeutta ei ole myönnetty Open SQL -kaaviolle.
#Change digram orientations
changeOrientations=Muuta suuntia


# placeholder for the API Path
apiPath=Esimerkki: /job/v1
# placeholder for the status API Path
statusAPIPath=Esimerkki: /job/v1/{tunnus}/status
# valueStateText for the API Path
apiPathRequired=API-polku on pakollinen
#placeholder for the CSRF Token URL
csrfTokenURL=Vain HTTPS:ää tuetaan
# Response Type 1
statusCode=Hae tulos HTTP-tilakoodista
# Response Type 2
locationHeader=Hae tulos HTTP-tilakoodista ja sijainnin otsikosta
# Response Type 3
responseBody=Hae tulos HTTP-tilakoodista ja vastauksen rungosta
# placeholder for ID
idPlaceholder=Syötä JSON-polku
# placeholder for indicator value
indicatorValue=Syötä arvo
# Placeholder for key
keyPlaceholder=Syötä avain
# Error message for missing key
KeyRequired=Avain on pakollinen
# Error message for invalid key format
invalidKeyFormat=Syöttämäsi otsikkoavain ei ole sallittu. Kelpaavat otsikot:<ul><li>"suosi"</li><li>Otsikot, joiden alussa on"x-", lukuun ottamatta "x-forwarded-host"</li><li>Otsikot, joissa on aakkosnumeerisia merkkejä, "-", tai "_"</li></ul>
# Error message for missing value
valueRequired=Arvo on pakollinen
# Error message for invalid characters in value
invalidValueCharacters=Otsikko sisältää virheellisiä merkkejä. Sallitut erikoismerkit:\t ";", ":", "-", "_", ",", "?", "/" ja "*"
# Validation message for invoke api path
apiPathValidation=Syötä kelpaava API-polku, esimerkki: /job/v1
# Validation message for JSON path
jsonPathValidation=Syötä kelpaava JSON-polku
# Validation message for success/error indicator
indicatorValueValidation=Tunnuksen arvon alussa on oltava aakkosnumeerinen merkki, ja se voi sisältää seuraavia erikoismerkkejä:\t "-" ja "_"
# Error message for JSON path
jsonPathRequired=JSON-polku on pakollinen
# Error message for invalid API Technical Name
invalidTechnicalName=Tekninen nimi sisältää virheellisiä merkkejä
# Error message for empty Technical Name
emptyTechnicalName=Tekninen nimi on pakollinen
# Tooltip for codeEditor dialog
codeEditorTooltip=Avaa JSON-muokkausikkuna
# Status Api path validation message
validationStatusAPIPath=Syötä kelpaava API-polku, esimerkki: /job/v1/{tunnus}/status
# CSRF token URL validation message
validationCSRFTokenURL=CSRF-tunnuksen URL:n alussa on oltava "https://", ja sen on oltava kelpaava URL
# Select a connection
selectConnection=Valitse yhteys
# Validation message for connection item error
connectionNotReplicated=Yhteys ei tällä hetkellä kelpaa API-tehtävien ajamiseen. Avaa Yhteydet-sovellus ja korjaa HTTP-yhteys syöttämällä valtuustiedot uudelleen
# Validation message for API task properties wrong value or missing
VAL_REST_API_PROPERTIES_MISSING=API-tehtävässä "{0}" on seuraavien ominaisuuksien virheellisiä tai puutteellisia arvoja: {1}
# Validation message for API connection api is throwing some error or selected connection has some warning
VAL_REST_API_CONNECTION=API-tehtävässä "{0}" on HTTP-yhteysongelma. Avaa Yhteydet-sovellus ja tarkista yhteys
# Validation message for API task properties when delected connection
VAL_REST_API_CONNECTION_DELETED=API-tehtävä "{0}" on poistanut yhteyden "{1}"
# Validation message for API task properties when connection cannot be consumed in the API task
VAL_REST_API_CONNECTION_CONSUMPTION_WARNING=API-tehtävällä "{0}" on yhteys "{1}", jota ei voi käyttää API-tehtävien suorittamiseen. Avaa "Yhteydet"-sovellus ja muodosta yhteys API-tehtäviä varten syöttämällä käyttäjätietosi uudelleen
# Information message if synchrnous mode is selected in the API Invocation then we don't need status information
modeInfoText=Synkronisessa tilassa ei näytetä tilapaneelia API-kutsuja varten
# validation dialog button for Run API Test
saveAnyway=Tallenna silti
# validation message for technical name
technicalNameValidation=Teknisen nimen on oltava tehtäväketjun sisällä yksiselitteinen. Valitse toinen tekninen nimi
# Connection error message
getHttpConnectionsError=HTTP-yhteyksien haku epäonnistui
# Msg for the API test run when the Task chain is not saved
taskChainNotSaved=Tehtäväketju pitää tallentaa ennen API-testiajon suorittamista
# Msg failed to run API test run
@failedToRunAPI=API-testiajon suoritus epäonnistui
# Msg for the API test run when its already in running state
apiTaskRunning=API-testiajo on jo käynnissä. Haluatko käynnistää uuden testiajon?

topToBtm=Ylhäältä alas
leftToRight=Vasemmalta oikealle

#XTIT Apache Spark Application Settings
txtHeaderApacheSettings=Apache Spark -sovellusasetukset
#XFLD Use Default
txtUseSpaceDefault=Käytä oletusarvoa
#XFLD Application
txtApplication=Sovellus
#XFLD Define new settings for this Task
txtNewSettings=Määritä uudet asetukset tätä tehtävää varten

#XFLD Use Default
txtUseDefault=Käytä oletusarvoa




