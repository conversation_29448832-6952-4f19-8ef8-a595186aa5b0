#XTOL Undo
@undo=पूर्ववत करें
#XTOL Redo
@redo=फिर से करें
#XTOL Delete Selected Symbol
@deleteNode=चयनित प्रतीक हटाएं
#XTOL Zoom to Fit
@zoomToFit=फ़िट करने के लिए ज़ूम करें
#XTOL Auto Layout
@autoLayout=स्वचालित लेआउट
#XMSG
@welcomeText=इस कैनवास के लिए बाएं पैनल से ऑब्जेक्ट खींचें और छोड़ें.
#XMSG
@txtNoData=ऐसा लगता है कि आपने अभी तक कोई ऑब्जेक्ट नहीं जोड़ा है.
#XMSG
VAL_ENTER_VALID_STRING_GEN=कृपया {0} के बराबर से कम लंबाई की मान्य स्ट्रिंग दर्ज करें.
#XMSG
@noParametersMsg=इस प्रक्रिया में कोई इनपुट पैरामीटर नहीं हैं.
#XMSG
@ip_enterValueMsg="{0}" (रन SQL स्क्रिप्ट प्रोसीजर) में इनपुट पैरामीटर में हैं. आप उनमें से प्रत्येक का मान निर्धारित कर सकते हैं.
#XTOL
@validateModel=सत्यापन संदेश
#XTOL
@hierarchy=पदानुक्रम
#XTOL
@columnCount=स्तंभो की संख्या
#XFLD
@yes=हां
#XFLD
@no=नहीं
#XTIT Save Dialog param
@modelNameTaskChain=कार्य श्रृंखला
#properties panel
@lblPropertyTitle=गुण
#XFLD
@lblGeneral=सामान्य
#XFLD : Setting
@lblSetting=सेटिंग
#XTXT: Delta Enabled Local Table Deletion
txtDeltaTableDeletion='हटाए गए' प्रकार के परिवर्तन के साथ सभी पूर्णतः संसाधित रिकॉर्ड हटाएं जो इससे पुराने हैं
#XTXT: Delta Enabled Local Table Deletion Input field description
desDeltaTableDeletion=दिन
#XFLD: Data Activation label
@lblDataActivation=डेटा सक्रियण
#XFLD: Latency label
@latency=लेटेंसी
#XTEXT: Text for Latency dropdown
txtLatencyDefault=डिफ़ॉल्ट
#XTEXT: Text 1 hour
txtOneHour=1 घंटे
#XTEXT: Text 2 hours
txtTwoHours=2 घंटे
#XTEXT: Text 3 hours
txtThreeHours=3 घंटे
#XTEXT: Text 4 hours
txtFourHours=4 घंटे
#XTEXT: Text 6 hours
txtSixHours=6 घंटे
#XTEXT: Text 12 hours
txtTwelveHours=12 घंटे
#XTEXT: Text 1 day
txtOneDay=1 दिन
#XFLD: Latency label
@autoRestartHead=स्वचालित पुनःआरंभ
#XFLD
@lblConnectionName=कनेक्शन
#XFLD
@lblQualifiedName=योग्य नाम
#XFLD
@lblSpaceName=स्पेस का नाम
#XFLD
@lblLocalSchemaName=स्थानीय स्कीमा
#XFLD
@lblType=ऑब्जेक्ट प्रकार
#XFLD
@lblActivity=गतिविधि
#XFLD
@lblTableName=तालिका नाम
#XFLD
@lblBusinessName=व्यवसाय का नाम
#XFLD
@lblTechnicalName=तकनीकी नाम
#XFLD
@lblSpace=स्पेस
#XFLD
@lblLabel=लेबल
#XFLD
@lblDataType=डेटा प्रकार
#XFLD
@lblDescription=वर्णन
#XFLD
@lblStorageType=संग्रहण
#XFLD
@lblHTTPConnection=जेनेरिक HTTP कनेक्शन
#XFLD
@lblAPISettings=जेनेरिक API  सेटिंग
#XFLD
@header=शीर्ष लेख
#XFLD
@lblInvoke=API इनवोकेशन 
#XFLD
@lblMethod=विधि
#XFLD
@lblUrl=आधार URL
#XFLD
@lblAPIPath=API पथ
#XFLD
@lblMode=मोड
#XFLD
@lblCSRFToken=CSRF टोकन की आवश्यकता है
#XFLD
@lblTokenURL=CSRF टोकन URL
#XFLD
@csrfTokenInfoText=यदि दर्ज नहीं किया गया है, तो बेस URL और API पथ का उपयोग किया जाएगा
#XFLD
@lblCSRF=CSRF टोकन 
#XFLD
@lblRequestBody=मुख्य भाग से अनुरोध करें
#XFLD
@lblFormat=स्वरूप
#XFLD
@lblResponse=प्रतिसाद 
#XFLD
@lblId=स्थिति पुनः प्राप्त करने के लिए ID 
#XFLD
@Id=ID
#XFLD
@lblSuccessIndicator=सफलता संकेतक
#XFLD
@lblErrorIndicator=त्रुटी संकेतक 
#XFLD
@lblErrorReason=त्रुटि का कारण
#XFLD
@lblStatus=स्थिति
#XFLD
@lblApiTestRun=API परिक्षण रन 
#XFLD
@lblRunStatus=स्थिति रन करें
#XFLD
@lblLastRan=अंतिम रन पर 
#XFLD
@lblTestRun=परिक्षण रन 
#XFLD
@lblDefaultHeader=डिफ़ॉल्ट शीर्षलेख फ़ील्ड (कुंजी-मान जोड़े)
#XFLD
@lblAdditionalHeader=अतिरिक्त शीर्षलेख फ़ील्ड
#XFLD
@lblKey=कुंजी
#XFLD
@lblEditJSON=JSON संपादित करें 
#XFLD
@lblTasks=कार्य
#XFLD
@lblRESTfullTask=API
#XFLD: add field button text
btnAddField=शीर्षलेख फ़ील्ड जोड़ें 
#XFLD: view Details link
@viewDetails=विवरण देखें
#XTOL
tooltipTxt=अधिक
#XTOL
delete=हटाएं
#XBTN: ok button text
btnOk=ठीक है
#XBTN: cancel button text
btnCancel=रद्द करें
#XBTN: save button text
btnSave=सहेजें
#XMSG Error: Object has duplicated technical name.
VAL_OBJECT_DUPLICATE_NAME=ऑब्जेक्ट ''{0}'' पहले से ही रिपॉजिटरी में मौजूद है. कृपया दूसरा नाम दर्ज करें.
#XMSG: loading message while opening task chain
loadTaskChain=कार्य श्रृंखला लोड हो रहा है...
#model properties
#XFLD
@status_panel=स्थिति रन करें
#XFLD
@deploy_status_panel=नियोजित स्थिति
#XFLD
@status_lbl=स्थिति
#XFLD
@lblLastExecuted=अंतिम रन
#XFLD
@lblNotExecuted=अभी भी निष्पादित नहीं किया गया
#XFLD
@lblNotDeployed=परिनियोजित नहीं किया गया
#XFLD
errorDetailsTxt=रन स्थिति फ़ेच नहीं कर सकते
#XBTN: Schedule dropdown menu
SCHEDULE=शेड्यूल
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=शेड्यूल संपादित करें
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=शेड्यूल हटाएं
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=शेड्यूल बनाएं
#XLNK
viewDetails=विवरण देखें
#XMSG: error message for reading execution status from backend
backendErrorMsg=ऐसा लगता है कि फिलहाल सर्वर से डेटा लोड नहीं हो रहा है. डेटा फिर से फ़ेच करने की कोशिश करें.
#XFLD: Status text for Completed
@statusCompleted=पूर्ण किया गया
#XFLD: Status text for Running
@statusRunning=चल रहा है
#XFLD: Status text for Failed
@statusFailed=विफल
#XFLD: Status text for Stopped
@statusStopped=रोका गया
#XFLD: Status text for Stopping
@statusStopping=रोका जा रहा है
#XFLD
@LoaderTitle=लोडिंग
#XFLD
#XFLD: Deploy status, dataflow has  been deployed / deployment date exists
@deployStatusActive=परिनियोजित
@deployStatusRevised=लोकल अपडेट
@deployStatusFailed=विफल
@deployStatusPending=परिनियोजित करना...
@LoaderText=सर्वर से विवरण फ़ेच करना
#XMSG
@msgDetailFetchError=सर्वर से विवरण फ़ेच करते समय त्रुटि
#XFLD
@executeError=त्रुटि
#XFLD
@executeWarning=चेतावनी
#XMSG
@executeConfirmDialog=जानकारी
#XMSG
@executeunsavederror=इसे रन करने से पहले अपने कार्य श्रृंखला सहेजें
#XMSG
@executemodifiederror=कार्य श्रृंखला में परिवर्तनों को सहेजा नहीं गया है. कृपया इसे सहेजें.
#XMSG
@executerunningerror=कार्य श्रृंखला वर्तमान रूप से रन कर रहा है. एक नए के साथ शुरू करने से पहले वर्तमान रन पूरा होने तक प्रतीक्षा करें.
#XMSG
@btnExecuteAnyway=फिर भी रन करें
#XMSG
@msgExecuteWithValidations=कार्य श्रृंखला में सत्यापन त्रुटियां है. कार्य श्रृंखला चलाने से विफलता हो सकती है.
#XMSG
@msgRunDeployedVersion=परिनियोजन के लिए परिवर्तन हैं. कार्य श्रृंखला का अंतिम परिनियोजित संस्करण रन किया जाएगा. क्या आप जारी रखना चाहते हैं?
#XMSG
#XMSG
@navToMonitoring=कार्य श्रृंखला मॉनिटर में खोलें
#XMSG
txtOR=या
#XFLD
@preview=पूर्वावलोकन
#XMSG
txtand=और
#NOTR
@dateFormat=yyyy-MM-dd
#NOTR
@dateTimeFormatFilter=yyyy-MM-dd HH:mm:ss.
#NOTR
@timeFormat=HH:mm:ss
@lblColum=स्तंभ
#XFLD
@lblCondition=शर्तें
#XFLD
@lblValue=मान
#XMSG
@msgJsonInvalid=कार्य श्रृंखला सहेजा नहीं जा सका क्योंकि JSON में त्रुटियां हैं. कृपया जांच करके समाधान करें.
#XTIT
@msgSaveFailTitle=अमान्य JSON.
#XMSG
NOT_CHAINABLE=ऑब्जेक्ट ''{0}'' को कार्य श्रृंखला में जोड़ा नहीं जा सकता.
#XMSG
NOT_CHAINABLE_REMOTETABLE=ऑब्जेक्ट ''{0}'': प्रतिकृति प्रकार बदल जाएगा.
#XMSG
searchTaskChain=ऑब्जेक्ट खोजें

#XFLD
@txtTaskChain=कार्य श्रृंखला
#XFLD
@txtRemoteTable=दूरस्थ तालिका
#XFLD
@txtRemoveData=प्रतिकृति किए गए डेटा को निकालें
#XFLD
@txtRemovePersist=स्थायी डेटा निकालें
#XFLD
@txtView=दृश्य
#XFLD
@txtDataFlow=डेटा प्रवाह
#XFLD
@txtIL=इंटेलिजेंट लुकअप
#XFLD
@txtTransformationFlow=रूपांतरण प्रवाह
#XFLD
@txtReplicationFlow=प्रतिकृति प्रवाह
#XFLD
@txtDeltaLocalTable=लोकल तालिका
#XFLD
@txtBWProcessChain=BW प्रक्रिया श्रृंखला
#XFLD
@txtSQLScriptProcedure=SQL स्क्रिप्ट प्रक्रिया
#XFLD
@txtAPI=API
#XFLD
@txtMerge=विलय करें
#XFLD
@txtOptimize=ऑप्टिमाइज़ करें
#XFLD
@txtVacuum=रिकॉर्ड्स हटाएं

#XFLD
@txtRun=रन
#XFLD
@txtPersist=जारी
#XFLD
@txtReplicate=प्रतिकृति बनाई गई
#XFLD
@txtDelete=परिवर्तन प्रकार 'हटाया' के साथ रिकॉर्ड हटाएं
#XFLD
@txtRunTC=कार्य श्रृंखला रन करें
#XFLD
@txtRunBW=BW प्रक्रिया श्रृंखला रन करें
#XFLD
@txtRunSQLScriptProcedure=SQL स्क्रिप्ट प्रक्रिया रन करें

#XFLD
@txtRunDataFlow=डेटा फ़्लो रन करें
#XFLD
@txtPersistView=निर्बाध दृश्य
#XFLD
@txtReplicateTable=प्रतिकृति तालिका
#XFLD
@txtRunIL=इंटेलिजेंट लुकअप रन करें
#XFLD
@txtRunTF=रूपांतरण प्रवाह रन करें
#XFLD
@txtRunRF=प्रतिकृति प्रवाह रन करें
#XFLD
@txtRemoveReplicatedData=प्रतिकृति किए गए डेटा को निकालें
#XFLD
@txtRemovePersistedData=स्थायी डेटा निकालें
#XFLD
@txtMergeData=विलय करें
#XFLD
@txtOptimizeData=ऑप्टिमाइज़ करें
#XFLD
@txtVacuumData=रिकॉर्ड्स हटाएं
#XFLD
@txtRunAPI=API रन करें 

#XFLD storage type text
hdlfStorage=फ़ाइल

@statusNew=परिनियोजित नहीं किया गया
@statusActive=परिनियोजित
@statusRevised=लोकल अपडेट
@statusPending=परिनियोजित करना...
@statusChangesToDeploy=परिनियोजन में परिवर्तन
@statusDesignTimeError=समय त्रुटि डिज़ाइन करें
@statusRunTimeError=रन समय त्रुटि

#XTIT
txtNodes=कार्य श्रृंखला में ऑब्जेक्ट ({0})
#XBTN
@deleteNodes=हटाएं

#XMSG
@txtDropDataToDiagram=डाएग्राम के लिए ऑब्जेक्ट खीचें और छोड़ें.
#XMSG
@noData=कोई ऑब्जेक्ट नहीं

#XFLD
@txtTaskPosition=कार्य स्थिति

#input parameters and variables
#XFLD
lblInputParameters=इनपुट पैरामीटर
#XFLD
ip_name=नाम
#XFLD
ip_value=मान
#XTEXT
@noObjectsFound=कोई ऑब्जेक्ट नहीं मिला

#XMSG
@msgExecuteSuccess=कार्य श्रृंखला रन आरंभ किया गया.
#XMSG
@msgExecuteFail=कार्य श्रृंखला रन करने में विफल हुआ.
#XMSG
@msgDeployAndRunSuccess=कार्य श्रृंखला नियोजन और रन आरंभ किया गया है.
#XMSG
@msgDeployAndRunFail=कार्य श्रृंखला परिनियोजन और रन करना विफल रहा.
#XMSG
@titleExecuteBusy=कृपया प्रतीक्षा करें.
#XMSG
@msgExecuteBusy=हम कार्य श्रृंखला रन शुरू करने के लिए आपका डेटा तैयार कर रहे हैं.
#XMSG
@msgAPITestRunSuccess=API परिक्षण रन शुरू हो गया है.
#XMSG
@msgAPIExecuteBusy=हम API परीक्षण रन करने के लिए आपका डेटा तैयार कर रहे हैं.

#XTOL
txtOpenInEditor=संपादक में खोलें
#XTOL
txtPreviewData=डेटा का पूर्वावलोकन

#datapreview
#XMSG
@msgDataPreviewNotSupp=इस ऑब्जेक्ट के लिए डेटा पूर्वावलोकन उपलब्ध नहीं है.

#XMSG Error: empty model
VAL_MODEL_EMPTY=आपका मॉडल रिक्त लगता है. कृपया कुछ ऑब्जेक्ट जोड़ें.
#XMSG Error: deploy model
@msgDeployBeforeRun=आपको इसे रन करन से पहले कार्य श्रृंखला परिनियोजित करने की आवश्यकता है.
#BTN: close dialog
btnClose=बंद करें

#XMSG Object is not Deployed
VAL_NOT_DEPLOYED=ऑब्जेक्ट ''{0}''’ कार्य श्रृंखला के साथ जारी करने के लिए परिनियोजित किया जाना चाहिए.
#XMSG Object is having runtime error
VAL_RUNTIME_ERROR=ऑब्जेक्ट ''{0}'' में रन टाइम त्रुटि हो रही है. कृपया ऑब्जेक्ट की जांच करें.
#XMSG Object is having design time error
VAL_DESIGNTIME_ERROR=ऑब्जेक्ट ''{0}'' में डिज़ाइन समय त्रुटि हो रही है. कृपया ऑब्जेक्ट की जांच करें.
#XMSG Procedure Object has been deleted
VAL_DELETED_PROCEDURE=निम्न प्रक्रियाएं हटा दी गई हैं: {0}
#XMSG New Parameter added to Procedure object
VAL_NEW_PARAMETER_ADDED=प्रक्रिया  "{1}":"{0}" में नए पैरामीटर जोड़े गए. कृपया कार्य शृंखला को पुनः नियोजित करें.
#XMSG Parameter removed from Procedure object
VAL_PARAMETER_DELETED=प्रक्रिया  "{1}":"{0}" में नए पैरामीटर निकाले गए. कृपया कार्य शृंखला को पुनः नियोजित करें.
#XMSG Parameter data type changed in Procedure object
VAL_PARAMETER_DATATYPE_MODIFIED=पैरामीटर "{0}" डेटा प्रकार "{1}" से "{2}" में प्रक्रिया "{3}" में परिवर्तित कर दिया गया है.
#XMSG Parameter length changed of specific datatype in Procedure object
VAL_PARAMETER_LENGTH_MODIFIED=पैरामीटर "{0}" की लंबाई "{1}" से "{2}" में प्रक्रिया "{3}" में परिवर्तित कर दी गई है.
#XMSG Parameter precision changed of Decimal datatype in Procedure object
VAL_PARAMETER_PRECISION_MODIFIED=पैरामीटर "{0}" की परिशुद्धता "{1}" से "{2}" में प्रक्रिया "{3}" में परिवर्तित कर दी गई है.
#XMSG Parameter scale changed of Decimal datatype in Procedure object
VAL_PARAMETER_SCALE_MODIFIED=पैरामीटर "{0}" का पैमाना "{1}" से "{2}" में प्रक्रिया "{3}" में परिवर्तित कर दिया गया है.
#XMSG Parameter value not provided in Procedure object
VAL_SQL_SCRIPT_PROCEDURE_PARAMETER_VALUE_MISSING=आपने प्रक्रिया "{0}": "{1}" के लिए आवश्यक इनपुट पैरामीटर्स के लिए मान दर्ज नहीं किए हैं
#XMSG Remote Table activity changes to Remove Replicated Data
VAL_REMOVE_REPLICATED_DATA=किसी कार्य श्रृंखला का अगला रन डेटा पहुंच प्रकार को परिवर्तित कर देगा और डेटा अब रीयल-टाइम में अपलोड नहीं किया जाएगा.
#XMSG Remote Table activity changes
REPLICATION_TYPE_CHANGED=ऑब्जेक्ट ''{0}'': प्रतिकृति प्रकार बदल जाएगा.

#XFLD
@lblStartNode=आरंभ नोड
#XFLD
@lblEndNode=समाप्ति नोड
#XFLD
@linkTo={0} से लेकर {1} तक
#XTOL
@txtViewDetails=विवरण देखें

#XTOL
txtOpenImpactLineage=प्रभाव और वंश विश्लेषण
#XFLD
@emailNotifications=ई-मेल अधिसूचना
#XFLD
@txtReset=रीसेट करें
#XFLD
@emailMsgWarning=ईमेल संदेश खाली होने पर डिफ़ॉल्ट टेम्पलेट ईमेल भेजा जाएगा
#XFLD
@notificationSettings=अधिसूचना सेटिंग
#XFLD
@recipientEmailAddr=प्राप्तकर्ता ई-मेल पता
#XFLD
@emailSubject=ई-मेल विषय
@emailSubjectText=कार्य श्रृंखला <TASKCHAIN_NAME>
#XFLD
@emailMessage=ई-मेल संदेश
@emailMessageText=प्रिय उपयोगकर्ता,\n\n यह आपको सूचित करने के लिए है कि कार्य श्रृंखलाः<TASKCHAIN_NAME><START_TIME> पर रन करने की स्थिति <STATUS> के साथ समाप्त हो गई है. निष्पादन <END_TIME> पर समाप्त हुआ.\n\nविवरण:\nस्पेस:<SPACE_NAME>\nत्रुटि:<ERROR_MESSAGE>
@selectEmailRecipientDialogTitle=प्राप्तकर्ता ई-मेल पता का चयन करें
@tenantMembers=टेनेंट सदस्य
@others=अन्य ({0})
@selectedEmailAddress=चयनित प्राप्तकर्ता
@add=जोड़ें
@placeholder=प्लेसहोल्डर
@description=वर्णन
@copyText=कॉपी टेक्स्ट
@taskchainDetailsPlaceholder=कार्य श्रृंखला विवरणों हेतु प्लेसहोल्डर
@placeholderCopied=प्लेसहोल्डर की प्रति बनाई गई
@invalidEmailInfo=सही ई-मेल पता दर्ज करें
@maxMembersAlreadyAdded=आप 20 सदस्यों को अधिकतम को पहले से जोड़ा गया
@enterEmailAddress=उपयोगकर्ता ई-मेल पता दर्ज करें
@inCorrectPlaceHolder={0} ई-मेल बॉडी में अपेक्षित प्लेसहोल्डर नहीं है.
@nsOFF=किसी अधिसूचनाओं को नहीं भेजें
@nsFAILED=ईमेल अधिसूचना तभी भेजें जब रन त्रुटि के साथ पूरा हो गया हो
@nsCOMPLETED=ईमेल अधिसूचना तभी भेजें जब रन सफलतापूर्वक पूरा किया गया हो
@nsANY=रन पूरा होने पर ईमेल भेजें
@phStatus=कार्य श्रृंखला की स्थिति - सफल|विफल हुआ
@phTaskChainTName=कार्य श्रृंखला के तनकीनी नाम
@phTaskChainBName=कार्य श्रृंखला का व्यवसाय नाम
@phLogId=रन की लॉग ID
@phUser=उपयोगकर्ता जो कार्य श्रृंखला रन कर रहा है
@phLogUILink=कार्य श्रृंखला के लॉग प्रदर्शन को लिंक करें
@phStartTime=रन का आरंभ समय
@phEndTime=रन का समाप्ति समय
@phErrMsg=कार्य लॉग में पहला त्रुटि संदेश. लॉग सफलता के केस में खाली है
@phSpaceName=स्थान के तकनीकी नाम
@emailFormatError=अमान्य ईमेल स्वरूप
@emailFormatErrorInListText=गैर-किराएदार सदस्यों की सूची में दर्ज किया गया अमान्य ईमेल स्वरूप.
@emailSubjectTemplateText=कार्य श्रृंखला के लिए अधिसूचना: $$taskChainName$$ - स्पेस: $$spaceId$$ - स्थिति: $$status$$
@emailMessageTemplateText=हैलो,\n\n $$taskChainName$$ लेबल वाली आपकी कार्य श्रृंखला $$status$$ स्थिति के साथ समाप्त हो गई है. \n यहां कार्य श्रृंखला के बारे में कुछ अन्य विवरण दिए गए हैं:\n - कार्य श्रृंखला तकनीकी नाम: $$taskChainName$$ \n - कार्य श्रृंखला रन की लॉग ID: $$logId$$ \n - कार्य श्रृंखला रन करने वाला उपयोगकर्ता: $$user$$ \n - कार्य श्रृंखला के लॉग प्रदर्शन की लिंक: $$uiLink$$ \n - कार्य श्रृंखला रन का प्रारंभ समय: $$startTime$$ \n - कार्य श्रृंखला रन का समाप्ति समय: $$endTime$$ \n - स्पेस का नाम: $$spaceId$$ \n
@deleteEmailRecepient=प्राप्तकर्ता हटाएं
@emailInputDisabledText=कृपया ई-मेल प्राप्ति जोड़ने के लिए कार्य श्रृंखला नियोजित करें.
@tenantOwnerDomainMatchErrorText=ईमेल पता डोमेन किरायेदार मालिक डोमेन से मेल नहीं खाता हैः {0}
@totalEmailIdLimitInfoText=आप किरायेदार सदस्य उपयोगकर्ताओं और अन्य प्राप्तकर्ताओं सहित अधिकतम 20 ईमेल प्राप्तकर्ताओं का चयन कर सकते हैं.
@emailDomainInfoText=डोमेन के साथ केवल ई-मेल पताः {0} स्वीकृत किया गया.
@duplicateEmailErrorText=सूची में डुप्लिकेट ई-मेल प्राप्ति.

#XFLD Zorder Title
@txtZorderTitle=Apache Spark Z-ऑर्डर स्तंभ

#XFLD Zorder NoColumn
@txtZorderNoColumn=कोई Z-ऑर्डर स्तंभ नहीं मिला

#XFLD Zorder Column PrimaryKey
@txtZorderColumnPrimaryKey=प्राथमिक कुंजी

#XFLD
@lblOperators=ऑपरेटर
addNewSelector=नए कार्य के रूप में जोड़ें
parallelSelector=समानांतर कार्य के रूप में जोड़ें
replaceSelector=मौजूदा कार्य बदलें
addparallelbranch=समानांतर शाखा के रूप में जोड़ें
addplaceholder=प्लेसहोल्डर जोड़ें
addALLOperation=सभी ऑपरेटर
addOROperation=कोई भी ऑपरेटर
addplaceholdertocanvas=कैनवास में प्लेसहोल्डर जोड़ें
addplaceholderonselected=चयनित कार्य के बाद प्लेसहोल्डर जोड़ें
#XFLD: Add Parallel Branch to Selected Task
addparallelbranchonselected=चयनित कार्य के बाद समानांतर शाखा जोड़ें
addOperator=ऑपरेटर जोड़ें
txtAdd=जोड़ें
txtPlaceHolderText=किसी कार्य को यहां खींचें और छोड़ें
@lblLayout=लेआउट

#XMSG
VAL_UNCONNECTED_TASK=कार्य श्रृंखला से कार्य ''{0}'' कनेक्ट नहीं है.
#XMSG
VAL_TASK_SHOULD_HAVE_ONE_INCOMING_LINK=कार्य ''{0}'' में केवल एक इनकमिंग लिंक होना चाहिए.
#XMSG
VAL_TASK_SHOULD_HAVE_INCOMING_LINK=कार्य ''{0}'' में एक इनकमिंग लिंक होना चाहिए.
#XMSG
VAL_UNCONNECTED_OPERATOR=कार्य श्रृंखला से ऑपरेटर ''{0}'' कनेक्ट नहीं है.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ATLEAST_TWO_INCOMING_LINK=ऑपरेटर ''{0}'' में कम से कम दो इनकमिंग लिंक होने चाहिए.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ONE_OUT_LINK=ऑपरेटर ''{0}'' में कम से कम एक आउटगोइंग लिंक होना चाहिए.
#XMSG
VAL_CIRCULAR_REFERENCE_EXITS=कार्य श्रृंखला ''{0}'' में सर्कुलर लूप मौजूद है.
#XMSG
VAL_UNCONNECTED_BRANCH=कार्य श्रृंखला से ऑब्जेक्ट/शाखा ''{0}'' कनेक्ट नहीं है.
#XMSG
VAL_TASK_TRIGGERED_PARALLEL_MORE_THAN_ONE_TIME=कार्य ''{0}'' समानांतर में एक से अधिक बार कनेक्ट किया गया है. कृपया जारी रखने के लिए डुप्लीकेट निकाल दें.


txtBegin=आरंभ
txtNodesInLink=शामिल ऑब्जेक्ट
#XTOL Tooltip for a context button on diagram
openInNewTab=नए टैब में खोलें
#XTOL Tooltip for a context button on diagram
txtConnectToTaskOperator=कनेक्ट करने के लिए खींचें
@emailUpdateError=ईमेल अधिसूचना सूची को अपडेटिंग में त्रुटि

#XMSG
noTeamPrivilegeTxt=आपके पास किरायेदार सदस्यों की सूची देखने की अनुमति नहीं है. ईमेल प्राप्तकर्ताओं को मैन्युअल रूप से जोड़ने के लिए अन्य टैब का उपयोग करें.

#XFLD Package
@txtPackage=पैकेज

#XMSG warning for repository package assignment changed. {1} - package name
VAL_PACKAGE_CHANGED=आपने इस ऑब्जेक्ट को पैकेज ''{1}'' को असाइन किया है. इस परिवर्तन की पुष्टि और सत्यापन करने के लिए सहेजें पर क्लिक करें. ध्यान दें कि आपके द्वारा सहेजे जाने के बाद, इस संपादक में किसी पैकेज का असाइनमेंट पूर्ववत नहीं किया जा सकता.
#XMSG warning for repository package dependency issue.
MISSING_DEPENDENCY=ऑब्जेक्ट ''{0}''’ की निर्भरता को पैकेज ''{1}''’के संदर्भ में हल नहीं किया जा सकता है.

#Warning messages for the folders in the "Others" tab of task chain editor
#XMSG generic warning if getting the objects in a particular folder fails with unhandled errors
getNonRepoArtefactTypeDescendantsError=आपके द्वारा चयनित फ़ोल्डर में ऑब्जेक्ट पुनर्प्राप्त करने में समस्या हुई.
#BW Process Chains folder related warning messages
#XMSG warning if user does not have the necessary permission related to the SAP BW Bridge space
missingPrivilegeForBridgeSpaceError=आपके पास कार्य श्रृंखला में BW प्रक्रिया श्रृंखलाओं को देखने या शामिल करने के लिए आवश्यक अनुमति नहीं है.
#XMSG warning if there was a problem retrieving the BW process chains due to bridge space membership, bridge connection issues or other errors
generalBridgeSpaceError=BW प्रक्रिया श्रृंखलाओं को पुनः प्राप्त करने में समस्या थी.
#XMSG warning if the BW API for retrieving the BW process chains from the SAP BW Bridge tenant fails
bwApiClientError=SAP BW Bridge किरायेदार से BW प्रक्रिया श्रृंखलाएँ पुनर्प्राप्त करने में समस्या थी.
#XMSG warning if BW process chains list is empty
bwApiEmptyListError=SAP BW Bridge किराएदार में कोई BW प्रक्रिया श्रृंखलाएं नहीं मिली हैं.
#XMSG: Task log message for invalid OIDC configuration in the SAP BW Bridge tenant
bwBridgeOIDCConfigStatusInvalid=इस किरायेदार के लिए OpenID प्रमाणीकरण कॉन्फ़िगर नहीं किया गया है. कृपया SAP नोट 3536298 में वर्णित अनुसार SAP BW ब्रिज किरायेदार में OpenID प्रमाणीकरण को कॉन्फ़िगर करने के लिए क्लाइंटID "{0}" और टोकनURL "{1}" का उपयोग करें.
#XMSG BW Process Chain Object has been deleted
VAL_DELETED_BW=निम्नलिखित BW प्रक्रिया श्रृंखलाएँ संभवतः  SAP Bridge किरायेदार से हटा दी गई थीं: {0}
#BW SQL Script Procedures folder related warning messages
#XFLD waring if the Procedure list is empty
procedureAPIEmptyList=Open SQL स्कीमा के लिए कोई प्रक्रिया नहीं बनाई गई है या EXECUTE विशेषाधिकार प्रदान नहीं किया गया है,
#Change digram orientations
changeOrientations=दिशा परिवर्तन


# placeholder for the API Path
apiPath=उदाहरण के लिए: /job/v1
# placeholder for the status API Path
statusAPIPath=उदाहरण के लिए:  /job/v1/{id}/status
# valueStateText for the API Path
apiPathRequired=API पथ आवश्यक है
#placeholder for the CSRF Token URL
csrfTokenURL=केवल HTTPS समर्थित है
# Response Type 1
statusCode=HTTP स्थिति कोड से परिणाम प्राप्त करें
# Response Type 2
locationHeader=HTTP स्थिति कोड और स्थान शीर्षलेख से परिणाम प्राप्त करें
# Response Type 3
responseBody=HTTP स्थिति कोड और प्रतिक्रिया मुख्य भाग से परिणाम प्राप्त करें
# placeholder for ID
idPlaceholder=JSON पथ दर्ज करें
# placeholder for indicator value
indicatorValue=मान दर्ज करें
# Placeholder for key
keyPlaceholder=कुंजी दर्ज करें 
# Error message for missing key
KeyRequired=कुंजी आवश्यक है
# Error message for invalid key format
invalidKeyFormat=आपके द्वारा दर्ज की गई शीर्षलेख कुंजी की अनुमति नहीं है. मान्य शीर्षलेख हैं:<ul><li>"पसंदीदा"</li><li>"x-" से शुरू होने वाले शीर्षलेख, "x-forwarded-host" को छोड़कर</li><li>ऐसे शीर्षलेख जिनमें अल्फ़ान्यूमेरिक वर्ण, "-", या "_"</li></ul> शामिल हैं
# Error message for missing value
valueRequired=मान आवश्यक है.
# Error message for invalid characters in value
invalidValueCharacters=शीर्षलेख में अमान्य वर्ण हैं. विशेष वर्ण जिनकी अनुमति है वे हैं:\t ";", ":", "-", "_", ",", "?", "/", और "*"
# Validation message for invoke api path
apiPathValidation=कृपया एक मान्य API पथ दर्ज करें, उदाहरण के लिए: /job/v1
# Validation message for JSON path
jsonPathValidation=कृपया एक मान्य JSON पथ दर्ज करें
# Validation message for success/error indicator
indicatorValueValidation=संकेतक मान अल्फ़ान्यूमेरिक वर्ण से शुरू होना चाहिए और इसमें निम्नलिखित विशेष वर्ण शामिल हो सकते हैं:\t "-", और "_"
# Error message for JSON path
jsonPathRequired=JSON पथ आवश्यक है
# Error message for invalid API Technical Name
invalidTechnicalName=तकनीकी नाम में अमान्य वर्ण हैं
# Error message for empty Technical Name
emptyTechnicalName=तकनीकी नाम आवश्यक है
# Tooltip for codeEditor dialog
codeEditorTooltip=JSON संपादन विंडो खोलें
# Status Api path validation message
validationStatusAPIPath=कृपया एक मान्य API पथ दर्ज करें, उदाहरण के लिए: /job/v1/{id}/status
# CSRF token URL validation message
validationCSRFTokenURL=CSRF टोकन URL 'https://' से शुरू होना चाहिए और एक वैध URL होना चाहिए
# Select a connection
selectConnection=कनेक्शन चयन करें 
# Validation message for connection item error
connectionNotReplicated=API कार्यों को रन करने के लिए कनेक्शन वर्तमान में अमान्य है. HTTP कनेक्शन को ठीक करने के लिए "कनेक्शन" ऐप खोलें और अपने क्रेडेंशियल पुनः दर्ज करें
# Validation message for API task properties wrong value or missing
VAL_REST_API_PROPERTIES_MISSING=API कार्य "{0}" में निम्नलिखित गुणों के लिए गलत या अनुपलब्ध मान हैं: {1}
# Validation message for API connection api is throwing some error or selected connection has some warning
VAL_REST_API_CONNECTION=API कार्य "{0}" में HTTP कनेक्शन के साथ कोई समस्या है. "कनेक्शन" ऐप खोलें और कनेक्शन की जाँच करें
# Validation message for API task properties when delected connection
VAL_REST_API_CONNECTION_DELETED=API कार्य "{0}" में एक हटाया गया "{1}" कनेक्शन है
# Validation message for API task properties when connection cannot be consumed in the API task
VAL_REST_API_CONNECTION_CONSUMPTION_WARNING=API कार्य "{0}" में एक कनेक्शन "{1}" है जिसका उपयोग API कार्यों को रन करने के लिए नहीं किया जा सकता है. API कार्यों के लिए कनेक्टिविटी स्थापित करने के लिए "कनेक्शन" ऐप खोलें और अपने क्रेडेंशियल पुनः दर्ज करें
# Information message if synchrnous mode is selected in the API Invocation then we don't need status information
modeInfoText=सिंक्रोनस मोड में, API इनवोकेशन के लिए स्थिति पैनल प्रदर्शित नहीं होता है
# validation dialog button for Run API Test
saveAnyway=कैसे भी सहेजें
# validation message for technical name
technicalNameValidation=कार्य श्रृंखला के भीतर तकनीकी नाम अद्वितीय होना चाहिए. कृपया कोई अन्य तकनीकी नाम चुनें.
# Connection error message
getHttpConnectionsError=HTTP कनेक्शन प्राप्त करने में विफल
# Msg for the API test run when the Task chain is not saved
taskChainNotSaved=API परीक्षण रन करने से पहले कार्य श्रृंखला को सहेजा जाना चाहिए
# Msg failed to run API test run
@failedToRunAPI=API परीक्षण रन करने में विफल
# Msg for the API test run when its already in running state
apiTaskRunning=API परीक्षण पहले से ही प्रगति पर है. क्या आप एक नया परीक्षण शुरू करना चाहते हैं?

topToBtm=ऊपर से नीचे
leftToRight=बाएं से दाएं

#XTIT Apache Spark Application Settings
txtHeaderApacheSettings=Apache Spark एप्लिकेशन सेटिंग
#XFLD Use Default
txtUseSpaceDefault=डिफ़ॉल्ट का उपयोग करें
#XFLD Application
txtApplication=एप्लिकेशन
#XFLD Define new settings for this Task
txtNewSettings=इस कार्य के लिए नई सेटिंग परिभाषित करें

#XFLD Use Default
txtUseDefault=डिफ़ॉल्ट का उपयोग करें




