#XTOL Undo
@undo=בטל פעולה
#XTOL Redo
@redo=בצע מחדש
#XTOL Delete Selected Symbol
@deleteNode=מחק סמל שנבחר
#XTOL Zoom to Fit
@zoomToFit=שנה גודל תצוגה להתאמה
#XTOL Auto Layout
@autoLayout=פריסה אוטומטית
#XMSG
@welcomeText=גרור ושחרר אובייקטים מהלוח השמאלי לקנבס זה.
#XMSG
@txtNoData=נראה שעוד לא הוספת אף אובייקט.
#XMSG
VAL_ENTER_VALID_STRING_GEN=הזן מחרוזת באורך חוקי של פחות מ- או שווה ל-{0}.
#XMSG
@noParametersMsg=להליך זה אין פרמטרי קלט.
#XMSG
@ip_enterValueMsg=ל-''{0}'' (הליך SQL Script להפעלה) יש פרממטרי קלט. ניתן להגדיר ערך לכל אחד מהם.
#XTOL
@validateModel=הודעות בדיקת תקינות
#XTOL
@hierarchy=היררכיה
#XTOL
@columnCount=מספר עמודות
#XFLD
@yes=כן
#XFLD
@no=לא
#XTIT Save Dialog param
@modelNameTaskChain=שרשרת משימות
#properties panel
@lblPropertyTitle=תכונות
#XFLD
@lblGeneral=כללי
#XFLD : Setting
@lblSetting=הגדרות
#XTXT: Delta Enabled Local Table Deletion
txtDeltaTableDeletion=מחק את כל הרשומות שעובדו במלואם עם סוג שינוי 'נמחק' ישנים יותר מ-
#XTXT: Delta Enabled Local Table Deletion Input field description
desDeltaTableDeletion=ימים
#XFLD: Data Activation label
@lblDataActivation=הפעלת נתונים
#XFLD: Latency label
@latency=השהיה
#XTEXT: Text for Latency dropdown
txtLatencyDefault=ברירת מחדל
#XTEXT: Text 1 hour
txtOneHour=שעה 1
#XTEXT: Text 2 hours
txtTwoHours=שעתיים
#XTEXT: Text 3 hours
txtThreeHours=3 שעות
#XTEXT: Text 4 hours
txtFourHours=4 שעות
#XTEXT: Text 6 hours
txtSixHours=6 שעות
#XTEXT: Text 12 hours
txtTwelveHours=12 שעות
#XTEXT: Text 1 day
txtOneDay=יום 1
#XFLD: Latency label
@autoRestartHead=אתחול אוטומטי
#XFLD
@lblConnectionName=חיבור
#XFLD
@lblQualifiedName=שם מוסמך
#XFLD
@lblSpaceName=שם מרחב
#XFLD
@lblLocalSchemaName=תרשים מקומי
#XFLD
@lblType=סוג אובייקט
#XFLD
@lblActivity=פעילות
#XFLD
@lblTableName=שם טבלה
#XFLD
@lblBusinessName=שם עסקי
#XFLD
@lblTechnicalName=שם טכני
#XFLD
@lblSpace=מרחב
#XFLD
@lblLabel=תווית
#XFLD
@lblDataType=סוג נתונים
#XFLD
@lblDescription=תיאור
#XFLD
@lblStorageType=אחסון
#XFLD
@lblHTTPConnection=חיבור HTTP גנרי
#XFLD
@lblAPISettings=הגדרות HTTP גנריות
#XFLD
@header=כותרות
#XFLD
@lblInvoke=קריאת API
#XFLD
@lblMethod=שיטה
#XFLD
@lblUrl=כתובת URL בסיסית
#XFLD
@lblAPIPath=נתיב API
#XFLD
@lblMode=מצב
#XFLD
@lblCSRFToken=דרוש CSRF Token
#XFLD
@lblTokenURL=כתובת URL של אסימון CSRF
#XFLD
@csrfTokenInfoText=אם היא לא הוזנה, ייעשה שימוש בכתובת URL בסיסית ובנתיב API
#XFLD
@lblCSRF=CSRF Token
#XFLD
@lblRequestBody=גוף בקשה
#XFLD
@lblFormat=פורמט
#XFLD
@lblResponse=תגובה
#XFLD
@lblId=זיהוי לאחזור סטאטוס
#XFLD
@Id=זיהוי
#XFLD
@lblSuccessIndicator=סמן הצלחה
#XFLD
@lblErrorIndicator=סמן שגיאה
#XFLD
@lblErrorReason=סיבה עבור שגיאה
#XFLD
@lblStatus=סטאטוס
#XFLD
@lblApiTestRun=הפעלה של בדיקת API
#XFLD
@lblRunStatus=סטאטוס הפעלה
#XFLD
@lblLastRan=פעל לאחרונה בתאריך
#XFLD
@lblTestRun=הפעלת בדיקה
#XFLD
@lblDefaultHeader=שדות כותרת של ברירת מחדל (זוגות מפתח-ערך)
#XFLD
@lblAdditionalHeader=שדה כותרת נוסף
#XFLD
@lblKey=מפתח
#XFLD
@lblEditJSON= ערוך JSON
#XFLD
@lblTasks=משימות
#XFLD
@lblRESTfullTask=API
#XFLD: add field button text
btnAddField=הוסף שדה כותרת 
#XFLD: view Details link
@viewDetails=הצג פרטים
#XTOL
tooltipTxt=עוד
#XTOL
delete=מחק
#XBTN: ok button text
btnOk=Ok
#XBTN: cancel button text
btnCancel=בטל
#XBTN: save button text
btnSave=שמור
#XMSG Error: Object has duplicated technical name.
VAL_OBJECT_DUPLICATE_NAME=אובייקט ''{0}'' כבר קיים במאגר. הזן שם אחר.
#XMSG: loading message while opening task chain
loadTaskChain=טוען את שרשרת המשימות...
#model properties
#XFLD
@status_panel=סטאטוס הפעלה
#XFLD
@deploy_status_panel=סטאטוס פריסה
#XFLD
@status_lbl=סטאטוס
#XFLD
@lblLastExecuted=הפעלה אחרונה
#XFLD
@lblNotExecuted=לא הופעל עדיין
#XFLD
@lblNotDeployed=לא נפרס
#XFLD
errorDetailsTxt=לא ניתן היה להביא את סטאטוס ההפעלה
#XBTN: Schedule dropdown menu
SCHEDULE=תזמן
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=ערוך תזמון
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=מחק תזמון
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=צור תזמון
#XLNK
viewDetails=הצג פרטים
#XMSG: error message for reading execution status from backend
backendErrorMsg=נראה שהנתונים לא נטענים מהשרת כרגע. נסה להביא את הנתונים שוב.
#XFLD: Status text for Completed
@statusCompleted=הושלם
#XFLD: Status text for Running
@statusRunning=פועל
#XFLD: Status text for Failed
@statusFailed=נכשל
#XFLD: Status text for Stopped
@statusStopped=נעצר
#XFLD: Status text for Stopping
@statusStopping=מתבצעת עצירה
#XFLD
@LoaderTitle=טוען
#XFLD
#XFLD: Deploy status, dataflow has  been deployed / deployment date exists
@deployStatusActive=נפרס
@deployStatusRevised=עדכונים מקומיים
@deployStatusFailed=נכשל
@deployStatusPending=מבצע פריסה...
@LoaderText=שולף פרטים מהשרת
#XMSG
@msgDetailFetchError=שגיאה במהלך שליפת פרטים מהשרת
#XFLD
@executeError=שגיאה
#XFLD
@executeWarning=אזהרה
#XMSG
@executeConfirmDialog=מידע
#XMSG
@executeunsavederror=שמור את שרשרת המשימות לפני הפעלתה.
#XMSG
@executemodifiederror=קיימים שינויים לא שמורים בשרשרת המשימות. שמור אותה.
#XMSG
@executerunningerror=שרשרת המשימות מופעלת כעת. המתן עד להשלמת ההפעלה הנוכחית לפני התחלת אחת חדשה.
#XMSG
@btnExecuteAnyway=הפעל בכל מקרה
#XMSG
@msgExecuteWithValidations=שרשרת המשימות כוללת שגיאות בדיקות תקינות. הפעלת שרשרת המשימות עשויה להוביל לכשל.
#XMSG
@msgRunDeployedVersion=קיימים שינויים לפריסה. הגרסה האחרונה שנפרסה של שרשרת משימות תופעל. האם ברצונך להמשיך?
#XMSG
#XMSG
@navToMonitoring=פתח במעקב אחר שרשרת משימות
#XMSG
txtOR=או
#XFLD
@preview=תצוגה מקדימה
#XMSG
txtand=ו-
#NOTR
@dateFormat=yyyy-MM-dd
#NOTR
@dateTimeFormatFilter=yyyy-MM-dd HH:mm:ss.
#NOTR
@timeFormat=HH:mm:ss
@lblColum=עמודה
#XFLD
@lblCondition=תנאי
#XFLD
@lblValue=ערך
#XMSG
@msgJsonInvalid=לא ניתן היה לשמור את שרשרת המשימות כיוון שהיו שגיאות ב-JSON. בדוק ופתור זאת.
#XTIT
@msgSaveFailTitle=קובץ JSON לא חוקי.
#XMSG
NOT_CHAINABLE=אובייקט "{0}" לא ניתן להוספה לשרשרת המשימות.
#XMSG
NOT_CHAINABLE_REMOTETABLE=אובייקט ''{0}'': סוג שכפול ישתנה.
#XMSG
searchTaskChain=חפש אובייקטים

#XFLD
@txtTaskChain=שרשרת משימות
#XFLD
@txtRemoteTable=טבלה מרוחקת
#XFLD
@txtRemoveData=הסר נתונים משוכפלים
#XFLD
@txtRemovePersist=הסר נתונים עקביים
#XFLD
@txtView=תצוגה
#XFLD
@txtDataFlow=תזרים נתונים
#XFLD
@txtIL=חיפוש חכם
#XFLD
@txtTransformationFlow=תזרים טרנספורמציה
#XFLD
@txtReplicationFlow=תזרים שכפול
#XFLD
@txtDeltaLocalTable=טבלה מקומית
#XFLD
@txtBWProcessChain=שרשרת תהליכי BW
#XFLD
@txtSQLScriptProcedure=הליך של SQL Script
#XFLD
@txtAPI=API
#XFLD
@txtMerge=מזג
#XFLD
@txtOptimize=יעל
#XFLD
@txtVacuum=מחק רשומות

#XFLD
@txtRun=הפעלה
#XFLD
@txtPersist=באחסון קבוע
#XFLD
@txtReplicate=שכפל
#XFLD
@txtDelete=מחק רשומות עם סוג שינוי 'נמחק'
#XFLD
@txtRunTC=הפעל את שרשרת המשימות
#XFLD
@txtRunBW=הפעל שרשרת תהליכי BW
#XFLD
@txtRunSQLScriptProcedure=הפעל הליך של SQL Script

#XFLD
@txtRunDataFlow=הפעל תזרים נתונים
#XFLD
@txtPersistView=תצוגה באחסון קבוע
#XFLD
@txtReplicateTable=שכפל טבלה
#XFLD
@txtRunIL=הפעל חיפוש חכם
#XFLD
@txtRunTF=הפעל תזרים טרנספורמציה
#XFLD
@txtRunRF=הפעל תזרים שכפול
#XFLD
@txtRemoveReplicatedData=הסר נתונים משוכפלים
#XFLD
@txtRemovePersistedData=הסר נתונים עקביים
#XFLD
@txtMergeData=מזג
#XFLD
@txtOptimizeData=יעל
#XFLD
@txtVacuumData=מחק רשומות
#XFLD
@txtRunAPI=הפעל API

#XFLD storage type text
hdlfStorage=קובץ

@statusNew=לא נפרס
@statusActive=נפרס
@statusRevised=עדכונים מקומיים
@statusPending=מבצע פריסה...
@statusChangesToDeploy=שינויים בפריסה
@statusDesignTimeError=שגיאה של זמן עיצוב
@statusRunTimeError=שגיאה של זמן ריצה

#XTIT
txtNodes=אובייקטים בשרשרת משימות ({0})
#XBTN
@deleteNodes=מחק

#XMSG
@txtDropDataToDiagram=גרור ושחרר אובייקטים לדיאגרמה.
#XMSG
@noData=אין אובייקטים

#XFLD
@txtTaskPosition=מיקום משימה

#input parameters and variables
#XFLD
lblInputParameters=פרמטרי קלט
#XFLD
ip_name=שם
#XFLD
ip_value=ערך
#XTEXT
@noObjectsFound=לא נמצאו אובייקטים

#XMSG
@msgExecuteSuccess=הפעלת שרשרת המשימות החלה.
#XMSG
@msgExecuteFail=הפעלת שרשרת המשימות נכשלה.
#XMSG
@msgDeployAndRunSuccess=פריסה והפעלה של שרשרת משימות החלו.
#XMSG
@msgDeployAndRunFail=כשל בפריסה ובהפעלה של שרשרת משימות.
#XMSG
@titleExecuteBusy=יש להמתין.
#XMSG
@msgExecuteBusy=אנחנו מכינים את הנתונים שלך כדי להפעיל את שרשרת המשימות.
#XMSG
@msgAPITestRunSuccess=הפעלה של בדיקת API החלה.
#XMSG
@msgAPIExecuteBusy=אנחנו מכינים את הנתונים שלך כדי להתחיל להפעיל את ההפעלה של בדיקת API 

#XTOL
txtOpenInEditor=פתח בעורך
#XTOL
txtPreviewData=הצג נתונים בתצוגה מקדימה

#datapreview
#XMSG
@msgDataPreviewNotSupp=תצוגה מקדימה של נתונים לא זמינה עבור אובייקט זה.

#XMSG Error: empty model
VAL_MODEL_EMPTY=נראה שהמודל שלך ריק. הוסף מספר אובייקטים.
#XMSG Error: deploy model
@msgDeployBeforeRun=עליך לפרוס את שרשרת המשימות לפני הפעלתה.
#BTN: close dialog
btnClose=סגור

#XMSG Object is not Deployed
VAL_NOT_DEPLOYED=חובה לפרוס את אובייקט "{0}" כדי להמשיך את שרשרת המשימות.
#XMSG Object is having runtime error
VAL_RUNTIME_ERROR=אובייקט "{0}" מחזיר שגיאת זמן ריצה. בדוק את האובייקט.
#XMSG Object is having design time error
VAL_DESIGNTIME_ERROR=אובייקט "{0}" מחזיר שגיאת זמן עיצוב. בדוק את האובייקט.
#XMSG Procedure Object has been deleted
VAL_DELETED_PROCEDURE=ההליכים הבאים נמחקו: {0}
#XMSG New Parameter added to Procedure object
VAL_NEW_PARAMETER_ADDED=פרמטרים חדשים נוספו להליך ''{1}'': ''{0}''. פרוס מחדש את שרשרת המשימות.
#XMSG Parameter removed from Procedure object
VAL_PARAMETER_DELETED=פרמטרים הוסרו מהליך ''{1}'': ''{0}''. פרוס מחדש את שרשרת המשימות.
#XMSG Parameter data type changed in Procedure object
VAL_PARAMETER_DATATYPE_MODIFIED=סוג הנתונים של פרמטר ''{0}'' שונה מ''{1}'' אל ''{2}'' בהליך ''{3}''.
#XMSG Parameter length changed of specific datatype in Procedure object
VAL_PARAMETER_LENGTH_MODIFIED=האורך של פרמטר ''{0}'' שונה מ''{1}'' אל ''{2}'' בהליך ''{3}''.
#XMSG Parameter precision changed of Decimal datatype in Procedure object
VAL_PARAMETER_PRECISION_MODIFIED=הדיוק של פרמטר ''{0}'' שונה מ''{1}'' אל ''{2}'' בהליך ''{3}''.
#XMSG Parameter scale changed of Decimal datatype in Procedure object
VAL_PARAMETER_SCALE_MODIFIED=המדרג של פרמטר ''{0}'' שונה מ''{1}'' אל ''{2}'' בהליך ''{3}''.
#XMSG Parameter value not provided in Procedure object
VAL_SQL_SCRIPT_PROCEDURE_PARAMETER_VALUE_MISSING=לא הזנת ערכים עבור פרמטרי הקלט שנדרשים עבור ההליך ''{0}'':  ''{1}''
#XMSG Remote Table activity changes to Remove Replicated Data
VAL_REMOVE_REPLICATED_DATA=ההפעלה הבאה של שרשרת משימות תשנה את סוג הגישה לנתונים, והנתונים לא יועלו עוד בזמן אמת.
#XMSG Remote Table activity changes
REPLICATION_TYPE_CHANGED=אובייקט ''{0}'': סוג שכפול ישתנה.

#XFLD
@lblStartNode=צומת התחלה
#XFLD
@lblEndNode=צומת סיום
#XFLD
@linkTo={0} עד {1}
#XTOL
@txtViewDetails=הצג פרטים

#XTOL
txtOpenImpactLineage=ניתוח שושלת יוחסין והשפעה
#XFLD
@emailNotifications=הודעות דוא"ל
#XFLD
@txtReset=אפס
#XFLD
@emailMsgWarning=דוא"ל בתבנית ברירת מחדל יישלח כשהודעת הדוא"ל ריקה.
#XFLD
@notificationSettings=הגדרות הודעה
#XFLD
@recipientEmailAddr=כתובת דוא"ל של מקבל
#XFLD
@emailSubject=נושא הודעת הדוא"ל
@emailSubjectText=שרשרת משימות <TASKCHAIN_NAME> הושלמה בסטאטוס <STATUS>
#XFLD
@emailMessage=הודעת דוא"ל
@emailMessageText=משתמש יקר,\n\n זוהי הודעה על כך ששרשרת המשימות <TASKCHAIN_NAME> שהופעלה בשעה <START_TIME> הסתיימה בסטאטוס <STATUS>. הביצוע הסתיים בשעה <END_TIME>.‏\n\nפרטים:\nמרחב:<SPACE_NAME>‏\nשגיאה:<ERROR_MESSAGE>
@selectEmailRecipientDialogTitle=בחר כתובת דוא"ל של מקבל
@tenantMembers=איברי הדייר
@others=אחרים({0})
@selectedEmailAddress=מקבלים שנבחרו
@add=הוסף
@placeholder=מציין מיקום
@description=תיאור
@copyText=העתק טקסט
@taskchainDetailsPlaceholder=מצייני מיקום עבור פרטי שרשרת משימות
@placeholderCopied=מציין המיקום הועתק
@invalidEmailInfo=הזן כתובת דוא"ל נכונה
@maxMembersAlreadyAdded=כבר הוספת את מספר האיברים המקסימלי, 20
@enterEmailAddress=הזן כתובת דוא"ל של משתמש
@inCorrectPlaceHolder={0} אינו מציין מיקום צפוי בגוף הדוא"ל.
@nsOFF=אל תשלח הודעות כלל
@nsFAILED=שלח הודעת דוא"ל רק כאשר ההפעלה הושלמה עם שגיאה
@nsCOMPLETED=שלח הודעת דוא"ל רק כאשר ההפעלה הושלמה בהצלחה
@nsANY=שלח הודעת דוא"ל כאשר ההפעלה הושלמה
@phStatus=סטאטוס שרשרת המשימות - הצליחה|נכשלה
@phTaskChainTName=שם טכני של שרשרת המשימות
@phTaskChainBName=שם עסקי של שרשרת המשימות
@phLogId=זיהוי יומן של ההפעלה
@phUser=המשתמש שמפעיל את שרשרת המשימות
@phLogUILink=הקישור לתצוגת היומן של שרשרת המשימות
@phStartTime=שעת ההתחלה של ההפעלה
@phEndTime=שעת הסיום של ההפעלה
@phErrMsg=הודעת השגיאה הראשונה ביומן המשימות. היומן ריק במקרה של 'הצליחה'
@phSpaceName=שם טכני של המרחב
@emailFormatError=פורמטר דוא"ל לא חוקי
@emailFormatErrorInListText=פורמט דוא"ל לא חוקי הוזן ברשימת איברים שאינם דיירים.
@emailSubjectTemplateText=הערה עבור שרשרת משימות: $$taskChainName$$ - מרחב: $$spaceId$$ - סטאטוס: $$status$$
@emailMessageTemplateText=שלום,\n\n שרשרת המשימות עם התווית $$taskChainName$$ הסתיימה בסטאטוס $$status$$. \n להלן מספר פרטים לגבי שרשרת המשימות:\n - שם טכני של שרשרת משימות: $$taskChainName$$ \n - זיהוי יומן של הפעלת שרשרת משימות: $$logId$$ \n - משתמש שהפעיל את שרשרשת המשימות: $$user$$ \n - קישור לתצוגת היומן של שרשרת המשימות: $$uiLink$$ \n - שעת התחלה של הפעלת שרשרת המשימות: $$startTime$$ \n - שעת סיום של הפעלת שרשרת משימות: $$endTime$$ \n - שם המרחב: $$spaceId$$ \n
@deleteEmailRecepient=מחק נמען
@emailInputDisabledText=פרוס את שרשרת המשימות כדי להוסיף נמעני דוא"ל
@tenantOwnerDomainMatchErrorText=תחום כתובת הדוא"ל לא תואם לתחום הבעלים הדייר: {0}
@totalEmailIdLimitInfoText=באפשרותך לבחור עד 20 נמעני דוא"ל כולל משתמשי חבר דייר ונמענים אחרים.
@emailDomainInfoText=מתקבלות רק כתובות דוא"ל עם תחום: {0}
@duplicateEmailErrorText=יש נמעני דוא"ל כפולים ברשימה.

#XFLD Zorder Title
@txtZorderTitle=עמודות הזמנת-Z של Apache Spark

#XFLD Zorder NoColumn
@txtZorderNoColumn=לא נמצאו עמודות הזמנת-Z 

#XFLD Zorder Column PrimaryKey
@txtZorderColumnPrimaryKey=מפתח ראשי

#XFLD
@lblOperators=אופרטורים
addNewSelector=הוסף כמשימה חדשה
parallelSelector=הוסף כמשימה במקביל
replaceSelector=החלף משימה קיימת
addparallelbranch=הוסף כענף במקביל
addplaceholder=הוסף מציין מיקום
addALLOperation=ALL אופרטור
addOROperation=ANY אופרטור
addplaceholdertocanvas=הוסף מציין מיקום לקנבס
addplaceholderonselected=הוסף מציין מיקום לאחר משימה שנבחרה
#XFLD: Add Parallel Branch to Selected Task
addparallelbranchonselected=הוסף ענף במקביל לאחר משימה שנבחרה
addOperator=הוסף אופרטור
txtAdd=הוסף
txtPlaceHolderText=גרור ושחרר לכאן משימה
@lblLayout=פריסה

#XMSG
VAL_UNCONNECTED_TASK=משימה ''{0}'' לא מחוברת לשרשרת המשימות.
#XMSG
VAL_TASK_SHOULD_HAVE_ONE_INCOMING_LINK=למשימה ''{0}'' צריך להיות רק קישור נכנס אחד.
#XMSG
VAL_TASK_SHOULD_HAVE_INCOMING_LINK=למשימה ''{0}'' צריך להיות קישור נכנס אחד.
#XMSG
VAL_UNCONNECTED_OPERATOR=אופרטור ''{0}'' לא מחובר לשרשרת המשימות.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ATLEAST_TWO_INCOMING_LINK=לאופרטור ''{0}'' צריך להיות שני קישורים נכנסים לפחות.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ONE_OUT_LINK=לאופרטור ''{0}'' צריך להיות קישור יוצא אחד לפחות.
#XMSG
VAL_CIRCULAR_REFERENCE_EXITS=לולאה מעגלית קיימת בשרשרת המשימות ''{0}''.
#XMSG
VAL_UNCONNECTED_BRANCH=אובייקט/ענף ''{0}'' לא מחובר לשרשרת המשימות.
#XMSG
VAL_TASK_TRIGGERED_PARALLEL_MORE_THAN_ONE_TIME=משימה ''{0}'' מחוברת במקביל יותר מפעם אחת. הסר כפילויות כדי להמשיך.


txtBegin=התחל
txtNodesInLink=אובייקטים מעורבים
#XTOL Tooltip for a context button on diagram
openInNewTab=פתח בלשונית חדשה
#XTOL Tooltip for a context button on diagram
txtConnectToTaskOperator=גרור להתחברות
@emailUpdateError=שגיאה בעדכון רשימת הודעות דוא"ל

#XMSG
noTeamPrivilegeTxt=אין לך הרשאה לראות רשימה של איברי דייר. השתמש בלשונית 'אחרים' כדי להוסיף נמעני דוא"ל באופן ידני.

#XFLD Package
@txtPackage=חבילה

#XMSG warning for repository package assignment changed. {1} - package name
VAL_PACKAGE_CHANGED=הקצת את האובייקט הזה לחבילה "{1}". לחץ על שמור כדי לאשר ולאמת את השינוי הזה. שים לב שההקצאה לחבילה אינה ניתנת לביטול בעורך זה לאחר השמירה.
#XMSG warning for repository package dependency issue.
MISSING_DEPENDENCY=לא ניתן לפתור יחסי תלות של אובייקט "{0}"  בהקשר של חבילה "{1}".

#Warning messages for the folders in the "Others" tab of task chain editor
#XMSG generic warning if getting the objects in a particular folder fails with unhandled errors
getNonRepoArtefactTypeDescendantsError=הייתה בעיה באחזור האובייקטים בתיקייה שבחרת.
#BW Process Chains folder related warning messages
#XMSG warning if user does not have the necessary permission related to the SAP BW Bridge space
missingPrivilegeForBridgeSpaceError=אין לך ההרשאות הנדרשות להצגה או הכללה של שרשרות תהליך BW בשרשרת משימות.
#XMSG warning if there was a problem retrieving the BW process chains due to bridge space membership, bridge connection issues or other errors
generalBridgeSpaceError=הייתה בעיה באחזור שרשרות תהליכי BW.
#XMSG warning if the BW API for retrieving the BW process chains from the SAP BW Bridge tenant fails
bwApiClientError=הייתה בעיה באחזור שרשרות תהליכי BW מדייר SAP BW Bridge.
#XMSG warning if BW process chains list is empty
bwApiEmptyListError=לא נמצאו שרשרות תהליכי BW בדייר SAP BW Bridge.
#XMSG: Task log message for invalid OIDC configuration in the SAP BW Bridge tenant
bwBridgeOIDCConfigStatusInvalid=אימות OpenID לא הוגדר עבור דייר זה. השתמש בזיהוי סביבה "{0}" וכתובת URL של אסימון "{1}" כדי להגדיר אימות OpenID בדייר SAP BW Bridge כפי מתואר בהערת SAP 3536298.
#XMSG BW Process Chain Object has been deleted
VAL_DELETED_BW=ייתכן ששרשרות תהליכי BW הבאות נמחקו מדייר SAP Bridge : {0}
#BW SQL Script Procedures folder related warning messages
#XFLD waring if the Procedure list is empty
procedureAPIEmptyList=לא נוצרו הליכים או שלא ניתנה להם ההרשאה 'בצע' כדי לפתוח את התרשים SQL.
#Change digram orientations
changeOrientations=שנה כיוונים


# placeholder for the API Path
apiPath=לדוגמה: /עבודה/v1
# placeholder for the status API Path
statusAPIPath=לדוגמה: /עבודה/v1/{id}/סטאטוס
# valueStateText for the API Path
apiPathRequired=נתיב API נדרש
#placeholder for the CSRF Token URL
csrfTokenURL=רק HTTPS נתמכים
# Response Type 1
statusCode=קבל תוצאה מקוד סטאטוס של HTTP
# Response Type 2
locationHeader=קבל תוצאה מקוד סטאטוס של HTTP וכותרת מיקום
# Response Type 3
responseBody=קבל תוצאה מקוד סטאטוס של HTTP וגוף תגובה
# placeholder for ID
idPlaceholder=הזן נתיב JSON
# placeholder for indicator value
indicatorValue=הזן ערך
# Placeholder for key
keyPlaceholder=הזן מפתח
# Error message for missing key
KeyRequired=נדרש מפתח
# Error message for invalid key format
invalidKeyFormat=מפתח הכותרת שהזנת אינו מותר. כותרות חוקיות הן:<ul><li>"העדף"</li><li>כותרות שמתחילות ב-"x-", מלבד "x-forwarded-host"</li><li>כותרות שמכילות תווים אלפאנומריים, "-", או "_"</li></ul>
# Error message for missing value
valueRequired=נדרש ערך
# Error message for invalid characters in value
invalidValueCharacters=הכותרת מכילה תווים לא חוקיים. תווין מיוחדים שמותרים הם:\t ";", ":", "-", "_", ",", "?", "/", ו- "*"
# Validation message for invoke api path
apiPathValidation=הזן נתיב API חוקי, לדוגמה: /עבודה/v1
# Validation message for JSON path
jsonPathValidation=הזן נתיב JASON חוקי
# Validation message for success/error indicator
indicatorValueValidation=ערך סמן צריך להתחיל בתו אלפאנומרי ויכול לכלול את התווים המיוחדים הבאים: :\t "-", ו- "_"
# Error message for JSON path
jsonPathRequired=נדרש נתיב JASON
# Error message for invalid API Technical Name
invalidTechnicalName=השם הטכני מכיל תווים לא חוקיים
# Error message for empty Technical Name
emptyTechnicalName=נדרש שם טכני
# Tooltip for codeEditor dialog
codeEditorTooltip=פתח את חלון 'ערוך' של JASON
# Status Api path validation message
validationStatusAPIPath=הזן נתיב API חוקי, לדוגמה: /עבודה/v1/{id}/סטאטוס
# CSRF token URL validation message
validationCSRFTokenURL=כתובת ה-URL של CSRF Token חייבת להתחיל ב- 'https://' ולהיות כתובת URL חוקית
# Select a connection
selectConnection=בחר חיבור
# Validation message for connection item error
connectionNotReplicated=החיבור לא חוקי כרגע עבור הפעלת משימות API. פתח את היישום 'חיבורים' והזן מחדש את האישורים שלך כדי לתקן את חיבור ה-HTTP
# Validation message for API task properties wrong value or missing
VAL_REST_API_PROPERTIES_MISSING=למשימת ה-API "{0}" יש ערכים שגויים או חסרים עבור התכונות הבאות: {1}
# Validation message for API connection api is throwing some error or selected connection has some warning
VAL_REST_API_CONNECTION=למשימת ה-API "{0}" יש בעיה עם חיבור HTTP פתח את היישום ''חיבורים'' ובדוק את החיבור.
# Validation message for API task properties when delected connection
VAL_REST_API_CONNECTION_DELETED=למשימת ה-API "{0}" נמחק חיבור "{1}" שנמחק
# Validation message for API task properties when connection cannot be consumed in the API task
VAL_REST_API_CONNECTION_CONSUMPTION_WARNING=למשימת API "{0}" קיים חיבור "{1}" שלא ניתן להשתמש בו בהפעלת משימות API. פתח את היישום "חיבורים" והזן מחדש את אישוריך כדי ליצור קישוריות עבור משימות API
# Information message if synchrnous mode is selected in the API Invocation then we don't need status information
modeInfoText=במצב סינכרוני, סטאטוס הלוח לא מוצג עבור קריאותת API
# validation dialog button for Run API Test
saveAnyway=שמור בכל זאת
# validation message for technical name
technicalNameValidation=השם הטכני חייב להיות ייחודי בשרשרת המשימה. בחר שם טכני אחר.
# Connection error message
getHttpConnectionsError=הבאת חיבורי HTTP נכשלה
# Msg for the API test run when the Task chain is not saved
taskChainNotSaved=יש לשמור את שרשרת המשימות לפני הפעלת ההפעלה של בדיקת API
# Msg failed to run API test run
@failedToRunAPI=הפעלת ההפעלה של בדיקת API נכשלה
# Msg for the API test run when its already in running state
apiTaskRunning=הפעלת בדיקה של API כבר בתהליך. האם ברצונך להתחיל הפעלת בדיקה חדשה?

topToBtm=למעלה-למטה
leftToRight=שמאל-ימין

#XTIT Apache Spark Application Settings
txtHeaderApacheSettings=הגדרות יישום Apache Spark 
#XFLD Use Default
txtUseSpaceDefault=השתמש בברירת מחדל
#XFLD Application
txtApplication=יישום
#XFLD Define new settings for this Task
txtNewSettings=הגדר הגדרות חדשות עבור משימה זו

#XFLD Use Default
txtUseDefault=השתמש בברירת מחדל




