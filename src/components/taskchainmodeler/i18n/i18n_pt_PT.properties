#XTOL Undo
@undo=Anular
#XTOL Redo
@redo=Refazer
#XTOL Delete Selected Symbol
@deleteNode=Eliminar símbolo selecionado
#XTOL Zoom to Fit
@zoomToFit=Aplicar zoom para ajustar
#XTOL Auto Layout
@autoLayout=Esquema automático
#XMSG
@welcomeText=Arraste e largue objetos do painel esquerdo para esta tela.
#XMSG
@txtNoData=Parece que ainda não adicionou qualquer objeto.
#XMSG
VAL_ENTER_VALID_STRING_GEN=Insira uma cadeia válida de comprimento inferior ou igual a {0}.
#XMSG
@noParametersMsg=Este procedimento não tem parâmetros de entrada.
#XMSG
@ip_enterValueMsg=O "{0}" (procedimento de execução de script SQL) tem parâmetros de entrada. Pode definir um valor para cada um.
#XTOL
@validateModel=Mensagens de validação
#XTOL
@hierarchy=Hierarquia
#XTOL
@columnCount=Número de colunas
#XFLD
@yes=Sim
#XFLD
@no=Não
#XTIT Save Dialog param
@modelNameTaskChain=Cadeia de tarefas
#properties panel
@lblPropertyTitle=Propriedades
#XFLD
@lblGeneral=Geral
#XFLD : Setting
@lblSetting=Definições
#XTXT: Delta Enabled Local Table Deletion
txtDeltaTableDeletion=Eliminar todos os registos totalmente processados com tipo de alteração 'Eliminado' com mais de
#XTXT: Delta Enabled Local Table Deletion Input field description
desDeltaTableDeletion=Dias
#XFLD: Data Activation label
@lblDataActivation=Ativação de dados
#XFLD: Latency label
@latency=Latência
#XTEXT: Text for Latency dropdown
txtLatencyDefault=Padrão
#XTEXT: Text 1 hour
txtOneHour=1 hora
#XTEXT: Text 2 hours
txtTwoHours=2 horas
#XTEXT: Text 3 hours
txtThreeHours=3 horas
#XTEXT: Text 4 hours
txtFourHours=4 horas
#XTEXT: Text 6 hours
txtSixHours=6 horas
#XTEXT: Text 12 hours
txtTwelveHours=12 horas
#XTEXT: Text 1 day
txtOneDay=1 dia
#XFLD: Latency label
@autoRestartHead=Reinício automático
#XFLD
@lblConnectionName=Ligação
#XFLD
@lblQualifiedName=Nome qualificado
#XFLD
@lblSpaceName=Nome do espaço
#XFLD
@lblLocalSchemaName=Esquema local
#XFLD
@lblType=Tipo de objeto
#XFLD
@lblActivity=Atividade
#XFLD
@lblTableName=Nome da tabela
#XFLD
@lblBusinessName=Nome comercial
#XFLD
@lblTechnicalName=Nome técnico
#XFLD
@lblSpace=Espaço
#XFLD
@lblLabel=Etiqueta
#XFLD
@lblDataType=Tipo de dados
#XFLD
@lblDescription=Descrição
#XFLD
@lblStorageType=Armazenamento
#XFLD
@lblHTTPConnection=Ligação HTTP genérica
#XFLD
@lblAPISettings=Definições de API genéricas
#XFLD
@header=Cabeçalhos
#XFLD
@lblInvoke=Invocação de API
#XFLD
@lblMethod=Método
#XFLD
@lblUrl=URL de base
#XFLD
@lblAPIPath=Caminho da API
#XFLD
@lblMode=Modo
#XFLD
@lblCSRFToken=Exigir token CSRF
#XFLD
@lblTokenURL=URL de token CSRF
#XFLD
@csrfTokenInfoText=Se não introduzido, o URL de base e o caminho da API serão utilizados
#XFLD
@lblCSRF=Token CSRF
#XFLD
@lblRequestBody=Corpo do pedido
#XFLD
@lblFormat=Formato
#XFLD
@lblResponse=Resposta
#XFLD
@lblId=ID para recuperar estado
#XFLD
@Id=ID
#XFLD
@lblSuccessIndicator=Indicador de sucesso
#XFLD
@lblErrorIndicator=Indicador de erro
#XFLD
@lblErrorReason=Motivo do erro
#XFLD
@lblStatus=Estado
#XFLD
@lblApiTestRun=Execução de teste da API
#XFLD
@lblRunStatus=Estado de execução
#XFLD
@lblLastRan=Última execução a
#XFLD
@lblTestRun=Execução de teste
#XFLD
@lblDefaultHeader=Campos de cabeçalho predefinidos (pares chave-valor)
#XFLD
@lblAdditionalHeader=Campo de cabeçalho adicional
#XFLD
@lblKey=Chave
#XFLD
@lblEditJSON=Editar JSON
#XFLD
@lblTasks=Tarefas
#XFLD
@lblRESTfullTask=API
#XFLD: add field button text
btnAddField=Adicionar campo de cabeçalho
#XFLD: view Details link
@viewDetails=Ver detalhes
#XTOL
tooltipTxt=Mais
#XTOL
delete=Eliminar
#XBTN: ok button text
btnOk=Ok
#XBTN: cancel button text
btnCancel=Cancelar
#XBTN: save button text
btnSave=Guardar
#XMSG Error: Object has duplicated technical name.
VAL_OBJECT_DUPLICATE_NAME=O objeto ''{0}'' já existe no repositório. Introduza outro nome.
#XMSG: loading message while opening task chain
loadTaskChain=A carregar a cadeia de tarefas...
#model properties
#XFLD
@status_panel=Estado de execução
#XFLD
@deploy_status_panel=Estado de implementação
#XFLD
@status_lbl=Estado
#XFLD
@lblLastExecuted=Última execução
#XFLD
@lblNotExecuted=Ainda não executado
#XFLD
@lblNotDeployed=Não implementado
#XFLD
errorDetailsTxt=Não foi possível obter o estado de execução
#XBTN: Schedule dropdown menu
SCHEDULE=Agendar
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Editar agenda
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Eliminar agenda
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Criar agenda
#XLNK
viewDetails=Ver detalhes
#XMSG: error message for reading execution status from backend
backendErrorMsg=Parece que os dados não estão a ser carregados do servidor neste momento. Tente obter os dados novamente.
#XFLD: Status text for Completed
@statusCompleted=Concluído
#XFLD: Status text for Running
@statusRunning=Em execução
#XFLD: Status text for Failed
@statusFailed=Falhado
#XFLD: Status text for Stopped
@statusStopped=Parado
#XFLD: Status text for Stopping
@statusStopping=A parar
#XFLD
@LoaderTitle=A carregar
#XFLD
#XFLD: Deploy status, dataflow has  been deployed / deployment date exists
@deployStatusActive=Implementado
@deployStatusRevised=Atualizações locais
@deployStatusFailed=Falhado
@deployStatusPending=A implementar...
@LoaderText=A obter detalhes do servidor
#XMSG
@msgDetailFetchError=Erro ao obter detalhes do servidor
#XFLD
@executeError=Erro
#XFLD
@executeWarning=Aviso
#XMSG
@executeConfirmDialog=Informação
#XMSG
@executeunsavederror=Guarde a sua cadeia de tarefas antes de a executar.
#XMSG
@executemodifiederror=Existem alterações não guardadas na cadeia de tarefas. Guarde-as.
#XMSG
@executerunningerror=A cadeia de tarefas estã em execução. Aguarde até a execução atual estar concluída antes de iniciar uma nova.
#XMSG
@btnExecuteAnyway=Executar na mesma
#XMSG
@msgExecuteWithValidations=A cadeia de tarefas tem erros de validação. A execução da cadeia de tarefas poderá resultar numa falha.
#XMSG
@msgRunDeployedVersion=Não existem alterações para implementar. A última versão implementada da cadeia de tarefas será executada. Pretende continuar?
#XMSG
#XMSG
@navToMonitoring=Abrir no monitor de cadeia de tarefas
#XMSG
txtOR=OU
#XFLD
@preview=Pré-visualização
#XMSG
txtand=E
#NOTR
@dateFormat=yyyy-MM-dd
#NOTR
@dateTimeFormatFilter=yyyy-MM-dd HH:mm:ss.
#NOTR
@timeFormat=HH:mm:ss
@lblColum=Coluna
#XFLD
@lblCondition=Condição
#XFLD
@lblValue=Valor
#XMSG
@msgJsonInvalid=Não foi possível guardar a cadeia de tarefas, pois existem erros no JSON. Verifique e resolva.
#XTIT
@msgSaveFailTitle=JSON inválido.
#XMSG
NOT_CHAINABLE=Não é possível adicionar o objeto ''{0}'' à cadeia de tarefas.
#XMSG
NOT_CHAINABLE_REMOTETABLE=Objeto "{0}": o tipo de replicação será alterado.
#XMSG
searchTaskChain=Procurar objetos

#XFLD
@txtTaskChain=Cadeia de tarefas
#XFLD
@txtRemoteTable=Tabela remota
#XFLD
@txtRemoveData=Remover dados replicados
#XFLD
@txtRemovePersist=Remover dados persistentes
#XFLD
@txtView=Vista
#XFLD
@txtDataFlow=Fluxo de dados
#XFLD
@txtIL=Pesquisa inteligente
#XFLD
@txtTransformationFlow=Fluxo de transformação
#XFLD
@txtReplicationFlow=Fluxo de replicação
#XFLD
@txtDeltaLocalTable=Tabela local
#XFLD
@txtBWProcessChain=Cadeia de processos BW
#XFLD
@txtSQLScriptProcedure=Procedimento de script SQL
#XFLD
@txtAPI=API
#XFLD
@txtMerge=União
#XFLD
@txtOptimize=Otimizar
#XFLD
@txtVacuum=Eliminar registos

#XFLD
@txtRun=Executar
#XFLD
@txtPersist=Persistir
#XFLD
@txtReplicate=Replicar
#XFLD
@txtDelete=Eliminar registos com tipo de alteração ‘Eliminado’
#XFLD
@txtRunTC=Executar a cadeia de tarefas
#XFLD
@txtRunBW=Executar cadeia de processos BW
#XFLD
@txtRunSQLScriptProcedure=Executar procedimento de script SQL

#XFLD
@txtRunDataFlow=Executar fluxo de dados
#XFLD
@txtPersistView=Efetuar a persistência da vista
#XFLD
@txtReplicateTable=Replicar tabela
#XFLD
@txtRunIL=Executar pesquisa inteligente
#XFLD
@txtRunTF=Executar fluxo de transformação
#XFLD
@txtRunRF=Executar fluxo de replicação
#XFLD
@txtRemoveReplicatedData=Remover dados replicados
#XFLD
@txtRemovePersistedData=Remover dados persistentes
#XFLD
@txtMergeData=União
#XFLD
@txtOptimizeData=Otimizar
#XFLD
@txtVacuumData=Eliminar registos
#XFLD
@txtRunAPI=Executar API

#XFLD storage type text
hdlfStorage=Ficheiro

@statusNew=Não implementado
@statusActive=Implementado
@statusRevised=Atualizações locais
@statusPending=A implementar...
@statusChangesToDeploy=Alterações a implementar
@statusDesignTimeError=Erro em tempo de design
@statusRunTimeError=Erro em tempo de execução

#XTIT
txtNodes=Objetos na cadeia de tarefas ({0})
#XBTN
@deleteNodes=Eliminar

#XMSG
@txtDropDataToDiagram=Arrastar e largar objetos no diagrama.
#XMSG
@noData=Sem objetos

#XFLD
@txtTaskPosition=Posição de tarefa

#input parameters and variables
#XFLD
lblInputParameters=Parâmetros de entrada
#XFLD
ip_name=Nome
#XFLD
ip_value=Valor
#XTEXT
@noObjectsFound=Nenhum objeto encontrado

#XMSG
@msgExecuteSuccess=A execução da cadeia de tarefas foi iniciada.
#XMSG
@msgExecuteFail=Falha ao executar a cadeia de tarefas.
#XMSG
@msgDeployAndRunSuccess=A implementação e execução da cadeia de tarefas foi iniciada.
#XMSG
@msgDeployAndRunFail=Falha ao implementar e executar a cadeia de tarefas.
#XMSG
@titleExecuteBusy=Aguarde.
#XMSG
@msgExecuteBusy=Estamos a preparar os seus dados para iniciar a execução da cadeia de tarefas.
#XMSG
@msgAPITestRunSuccess=A execução de teste de API foi iniciada.
#XMSG
@msgAPIExecuteBusy=Estamos a preparar os seus dados para iniciar a execução de teste de API.

#XTOL
txtOpenInEditor=Abrir no editor
#XTOL
txtPreviewData=Pré-visualizar dados

#datapreview
#XMSG
@msgDataPreviewNotSupp=A pré-visualização de dados não está disponível para este objeto.

#XMSG Error: empty model
VAL_MODEL_EMPTY=O seu modelo parece estar em branco. Adicione alguns objetos.
#XMSG Error: deploy model
@msgDeployBeforeRun=É necessário implementar a cadeia de tarefas antes de a executar.
#BTN: close dialog
btnClose=Fechar

#XMSG Object is not Deployed
VAL_NOT_DEPLOYED=O objeto ''{0}'' deve ser implementado para continuar com a cadeia de tarefas.
#XMSG Object is having runtime error
VAL_RUNTIME_ERROR=O objeto ''{0}'' devolve um erro em tempo de execução. Verifique o objeto.
#XMSG Object is having design time error
VAL_DESIGNTIME_ERROR=O objeto ''{0}'' devolve um erro em tempo de design. Verifique o objeto.
#XMSG Procedure Object has been deleted
VAL_DELETED_PROCEDURE=Os seguintes procedimentos foram eliminados: {0}
#XMSG New Parameter added to Procedure object
VAL_NEW_PARAMETER_ADDED=Novos parâmetros adicionados ao procedimento "{1}": "{0}". Reimplemente a cadeia de tarefas.
#XMSG Parameter removed from Procedure object
VAL_PARAMETER_DELETED=Parâmetros removidos do procedimento "{1}": "{0}". Reimplemente a cadeia de tarefas.
#XMSG Parameter data type changed in Procedure object
VAL_PARAMETER_DATATYPE_MODIFIED=Tipo de dados de parâmetro "{0}" alterado de "{1}" para "{2}" no procedimento "{3}".
#XMSG Parameter length changed of specific datatype in Procedure object
VAL_PARAMETER_LENGTH_MODIFIED=Comprimento de parâmetro "{0}" alterado de "{1}" para "{2}" no procedimento "{3}".
#XMSG Parameter precision changed of Decimal datatype in Procedure object
VAL_PARAMETER_PRECISION_MODIFIED=Precisão de parâmetro "{0}" alterada de "{1}" para "{2}" no procedimento "{3}".
#XMSG Parameter scale changed of Decimal datatype in Procedure object
VAL_PARAMETER_SCALE_MODIFIED=Escala de parâmetro "{0}" alterada de "{1}" para "{2}" no procedimento "{3}".
#XMSG Parameter value not provided in Procedure object
VAL_SQL_SCRIPT_PROCEDURE_PARAMETER_VALUE_MISSING=Não introduziu valores para os parâmetros de entrada que são necessários para o procedimento "{0}": "{1}"
#XMSG Remote Table activity changes to Remove Replicated Data
VAL_REMOVE_REPLICATED_DATA=A próxima execução de uma cadeia de tarefas alterará o tipo de acesso a dados e os dados já não serão carregados em tempo real.
#XMSG Remote Table activity changes
REPLICATION_TYPE_CHANGED=Objeto "{0}": o tipo de replicação será alterado.

#XFLD
@lblStartNode=Nó de início
#XFLD
@lblEndNode=Nó de fim
#XFLD
@linkTo={0} para {1}
#XTOL
@txtViewDetails=Ver detalhes

#XTOL
txtOpenImpactLineage=Análise de impacto e linhagem
#XFLD
@emailNotifications=Notificações por e-mail
#XFLD
@txtReset=Repor
#XFLD
@emailMsgWarning=Será enviado um e-mail modelo predefinido quando a mensagem de e-mail estiver vazia
#XFLD
@notificationSettings=Definições de notificação
#XFLD
@recipientEmailAddr=Endereço de e-mail de destinatário
#XFLD
@emailSubject=Assunto do e-mail
@emailSubjectText=Cadeia de tarefas <TASKCHAIN_NAME> concluída com o estado <STATUS>
#XFLD
@emailMessage=Mensagem de e-mail
@emailMessageText=Caro utilizador,\n\n Este e-mail serve para notificá-lo de que a cadeia de tarefas:<TASKCHAIN_NAME> execução às <START_TIME> terminou com o estado <STATUS>. A execução terminou em <END_TIME>.\n\nDetalhes:\nEspaço:<SPACE_NAME>\nErro:<ERROR_MESSAGE>
@selectEmailRecipientDialogTitle=Seleciono endereço de e-mail de destinatário
@tenantMembers=Membros do inquilino
@others=Outros({0})
@selectedEmailAddress=Destinatários selecionados
@add=Adicionar
@placeholder=Marcador de posição
@description=Descrição
@copyText=Copiar texto
@taskchainDetailsPlaceholder=Marcadores de posição para detalhes de cadeia de tarefas
@placeholderCopied=O marcador de posição é copiado
@invalidEmailInfo=Introduzir o endereço de e-mail correto
@maxMembersAlreadyAdded=Já adicionou o máximo de 20 membros
@enterEmailAddress=Introduzir o endereço de e-mail do utilizador
@inCorrectPlaceHolder={0} não é um marcador de posição previsto no corpo do e-mail.
@nsOFF=Não enviar notificações
@nsFAILED=Enviar notificação por e-mail só quando a execução tiver sido concluída com um erro
@nsCOMPLETED=Enviar notificação por e-mail só quando a execução tiver sido concluída com êxito
@nsANY=Enviar e-mail só quando a execução tiver sido concluída
@phStatus=Estado da cadeia de tarefas - COM ÊXITO|FALHADO
@phTaskChainTName=Nome técnico da cadeia de tarefas
@phTaskChainBName=Nome comercial da cadeia de tarefas
@phLogId=ID do registo da execução
@phUser=Utilizador que está a executar a cadeia de tarefas
@phLogUILink=Link para a apresentação de registo da cadeia de tarefas
@phStartTime=Hora de início da execução
@phEndTime=Hora de fim da execução
@phErrMsg=Primeira mensagem de erro no registo de tarefa. O registo está vazio em caso de ÊXITO
@phSpaceName=Nome técnico do espaço
@emailFormatError=Formato de e-mail inválido
@emailFormatErrorInListText=Formato de e-mail inválido introduzido na lista de membros não inquilinos.
@emailSubjectTemplateText=Notificação para cadeia de tarefas: $$taskChainName$$ - Espaço: $$spaceId$$ - Estado: $$status$$
@emailMessageTemplateText=Olá,\n\n A sua cadeia de tarefas etiquetada $$taskChainName$$ foi concluída com o estado $$status$$. \n Aqui estão mais alguns detalhes sobre a cadeia de tarefas:\n - Nome técnico da cadeia de tarefas: $$taskChainName$$ \n - ID de registo da execução da cadeia de tarefas: $$logId$$ \n - Utilizador que executou a cadeia de tarefas: $$user$$ \n - Link para a apresentação do registo da cadeia de tarefas: $$uiLink$$ \n - Hora de início da execução da cadeia de tarefas: $$startTime$$ \n - Hora de fim da execução da cadeia de tarefas: $$endTime$$ \n - Nome do espaço: $$spaceId$$ \n
@deleteEmailRecepient=Eliminar destinatário
@emailInputDisabledText=Implemente a cadeia de tarefas para adicionar destinatários de e-mail.
@tenantOwnerDomainMatchErrorText=O domínio do endereço de e-mail não corresponde ao domínio do proprietário do inquilino: {0}
@totalEmailIdLimitInfoText=Pode selecionar até 20 destinatários de e-mail, incluindo utilizadores de membros do inquilino e outros destinatários.
@emailDomainInfoText=Só endereços de e-mail com o domínio: {0} são aceites.
@duplicateEmailErrorText=Existem destinatários de e-mail duplicados na lista.

#XFLD Zorder Title
@txtZorderTitle=Colunas de ordem Z Apache Spark

#XFLD Zorder NoColumn
@txtZorderNoColumn=Nenhuma coluna de ordem Z encontrada

#XFLD Zorder Column PrimaryKey
@txtZorderColumnPrimaryKey=Chave primária

#XFLD
@lblOperators=Operadores
addNewSelector=Adicionar como nova tarefa
parallelSelector=Adicionar como tarefa paralela
replaceSelector=Substituir tarefa existente
addparallelbranch=Adicionar como ramo paralelo
addplaceholder=Adicionar marcador de posição
addALLOperation=Operador ALL
addOROperation=Operador ANY
addplaceholdertocanvas=Adicionar marcador de posição à tela
addplaceholderonselected=Adicionar marcador de posição após tarefa selecionada
#XFLD: Add Parallel Branch to Selected Task
addparallelbranchonselected=Adicionar ramo paralelo após tarefa selecionada
addOperator=Adicionar operador
txtAdd=Adicionar
txtPlaceHolderText=Arrastar e largar uma tarefa aqui
@lblLayout=Esquema

#XMSG
VAL_UNCONNECTED_TASK=A tarefa ''{0}'' não está ligada à cadeia de tarefas.
#XMSG
VAL_TASK_SHOULD_HAVE_ONE_INCOMING_LINK=A tarefa ''{0}'' só deve ter uma ligação de entrada.
#XMSG
VAL_TASK_SHOULD_HAVE_INCOMING_LINK=A tarefa ''{0}'' deve ter uma ligação de entrada.
#XMSG
VAL_UNCONNECTED_OPERATOR=O operador ''{0}'' não está ligado à cadeia de tarefas.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ATLEAST_TWO_INCOMING_LINK=O operador ''{0}'' deve ter pelo menos duas ligações de entrada.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ONE_OUT_LINK=O operador ''{0}'' deve ter pelo menos uma ligação de saída.
#XMSG
VAL_CIRCULAR_REFERENCE_EXITS=Existe um ciclo circular na cadeia de tarefas ''{0}''.
#XMSG
VAL_UNCONNECTED_BRANCH=O objeto/ramo ''{0}'' não está ligado à cadeia de tarefas.
#XMSG
VAL_TASK_TRIGGERED_PARALLEL_MORE_THAN_ONE_TIME=Tarefa ''{0}'' ligada em paralelo mais do que uma vez. Remova os duplicados para continuar.


txtBegin=Início
txtNodesInLink=Objetos envolvidos
#XTOL Tooltip for a context button on diagram
openInNewTab=Abrir num novo separador
#XTOL Tooltip for a context button on diagram
txtConnectToTaskOperator=Arrastar para ligar
@emailUpdateError=Erro ao atualizar lista de notificação por e-mail

#XMSG
noTeamPrivilegeTxt=Não tem permissão para ver uma lista de membros de inquilinos. Utilize o separador Outros para adicionar manualmente os destinatários de e-mail.

#XFLD Package
@txtPackage=Pacote

#XMSG warning for repository package assignment changed. {1} - package name
VAL_PACKAGE_CHANGED=Atribuiu este objeto ao pacote ''{1}''. Clique em Guardar para confirmar e validar esta alteração. Considere que a atribuição a um pacote não pode ser anulada neste editor depois de guardar.
#XMSG warning for repository package dependency issue.
MISSING_DEPENDENCY=As dependências do objeto ''{0}'' não podem ser resolvidas no contexto do pacote ''{1}''.

#Warning messages for the folders in the "Others" tab of task chain editor
#XMSG generic warning if getting the objects in a particular folder fails with unhandled errors
getNonRepoArtefactTypeDescendantsError=Ocorreu um problema ao recuperar os objetos na pasta que selecionou.
#BW Process Chains folder related warning messages
#XMSG warning if user does not have the necessary permission related to the SAP BW Bridge space
missingPrivilegeForBridgeSpaceError=Não tem a permissão necessária para ver ou incluir cadeias de processos BW numa cadeia de tarefas.
#XMSG warning if there was a problem retrieving the BW process chains due to bridge space membership, bridge connection issues or other errors
generalBridgeSpaceError=Ocorreu um problema ao recuperar as cadeias de processos BW.
#XMSG warning if the BW API for retrieving the BW process chains from the SAP BW Bridge tenant fails
bwApiClientError=Ocorreu um problema ao recuperar as cadeias de processos BW do inquilino SAP BW Bridge.
#XMSG warning if BW process chains list is empty
bwApiEmptyListError=Nenhuma cadeia de processos BW encontrada no inquilino SAP BW Bridge.
#XMSG: Task log message for invalid OIDC configuration in the SAP BW Bridge tenant
bwBridgeOIDCConfigStatusInvalid=A autenticação OpenID não está configurada para este inquilino. Utilize o clientID "{0}" e o tokenURL "{1}" para configurar a autenticação OpenID no inquilino da ponte SAP BW, conforme descrito na nota SAP 3536298.
#XMSG BW Process Chain Object has been deleted
VAL_DELETED_BW=As seguintes cadeias de processos BW foram possivelmente eliminadas do inquilino SAP Bridge: {0}
#BW SQL Script Procedures folder related warning messages
#XFLD waring if the Procedure list is empty
procedureAPIEmptyList=Não foram criados procedimentos ou o privilégio EXECUTE não foi concedido ao esquema Open SQL.
#Change digram orientations
changeOrientations=Alterar orientações


# placeholder for the API Path
apiPath=Por exemplo: /job/v1
# placeholder for the status API Path
statusAPIPath=Por exemplo: /job/v1/{id}/status
# valueStateText for the API Path
apiPathRequired=O caminho da API é obrigatório
#placeholder for the CSRF Token URL
csrfTokenURL=Apenas o HTTPS é suportado
# Response Type 1
statusCode=Obter resultado do código de estado HTTP
# Response Type 2
locationHeader=Obter resultado do código de estado HTTP e cabeçalho "location"
# Response Type 3
responseBody=Obter resultado do código de estado HTTP e corpo da resposta
# placeholder for ID
idPlaceholder=Introduzir caminho JSON
# placeholder for indicator value
indicatorValue=Introduzir valor
# Placeholder for key
keyPlaceholder=Introduzir chave
# Error message for missing key
KeyRequired=A chave é obrigatória
# Error message for invalid key format
invalidKeyFormat=A chave de cabeçalho que introduziu não é permitida. Os cabeçalhos válidos são:<ul><li>"prefer"</li><li>Cabeçalhos que começam com "x-", exceto "x-forwarded-host"</li><li>Cabeçalhos que contêm carateres alfanuméricos, "-", ou "_"</li></ul>
# Error message for missing value
valueRequired=O valor é obrigatório
# Error message for invalid characters in value
invalidValueCharacters=O cabeçalho contém carateres inválidos. Os carateres especiais permitidos são:\t ";", ":", "-", "_", ",", "?", "/", e "*"
# Validation message for invoke api path
apiPathValidation=Introduza um caminho de API válido, por exemplo: /job/v1
# Validation message for JSON path
jsonPathValidation=Introduza um caminho JSON válido
# Validation message for success/error indicator
indicatorValueValidation=O valor do indicador deve começar com um caráter alfanumérico e pode incluir os seguintes carateres especiais:\t "-", e "_"
# Error message for JSON path
jsonPathRequired=O caminho JSON é obrigatório
# Error message for invalid API Technical Name
invalidTechnicalName=O nome técnico contém carateres inválidos
# Error message for empty Technical Name
emptyTechnicalName=O nome técnico é obrigatório
# Tooltip for codeEditor dialog
codeEditorTooltip=Abrir janela de edição de JSON
# Status Api path validation message
validationStatusAPIPath=Introduza um caminho de API válido, por exemplo: /job/v1/{id}/status
# CSRF token URL validation message
validationCSRFTokenURL=O URL do token CSRF deve começar com 'https://' e ser um URL válido
# Select a connection
selectConnection=Selecionar uma ligação
# Validation message for connection item error
connectionNotReplicated=Ligação atualmente inválida para executar tarefas da API. Abra a aplicação "Ligações" e reintroduza as suas credenciais para corrigir a ligação HTTP
# Validation message for API task properties wrong value or missing
VAL_REST_API_PROPERTIES_MISSING=A tarefa API  "{0}" tem valores incorretos ou em falta para as seguintes propriedades: {1}
# Validation message for API connection api is throwing some error or selected connection has some warning
VAL_REST_API_CONNECTION=A tarefa API "{0}" tem um problema com a ligação HTTP. Abra a aplicação "Ligações" e verifique a ligação
# Validation message for API task properties when delected connection
VAL_REST_API_CONNECTION_DELETED=A tarefa API "{0}" tem uma ligação "{1}" eliminada
# Validation message for API task properties when connection cannot be consumed in the API task
VAL_REST_API_CONNECTION_CONSUMPTION_WARNING=A tarefa API "{0}" tem uma ligação "{1}" que não pode ser utilizada para executar tarefas API. Abra a aplicação "Ligações" e e reintroduza as suas credenciais para estabelecer a conectividade para tarefas API.
# Information message if synchrnous mode is selected in the API Invocation then we don't need status information
modeInfoText=No modo síncrono, o painel de estado não é apresentado para inovações de API.
# validation dialog button for Run API Test
saveAnyway=Guardar mesmo assim
# validation message for technical name
technicalNameValidation=O nome técnico deve ser exclusivo na cadeia de tarefas. Escolha outro nome técnico
# Connection error message
getHttpConnectionsError=Falha ao obter as ligações HTTP
# Msg for the API test run when the Task chain is not saved
taskChainNotSaved=A cadeia de tarefas deve ser guardada antes da execução de teste de API
# Msg failed to run API test run
@failedToRunAPI=Falha na execução de teste de API
# Msg for the API test run when its already in running state
apiTaskRunning=Uma execução de teste de API está já em curso. Iniciar uma nova execução de teste?

topToBtm=Cima-baixo
leftToRight=Esquerda-direita

#XTIT Apache Spark Application Settings
txtHeaderApacheSettings=Definições da aplicação Apache Spark
#XFLD Use Default
txtUseSpaceDefault=Utilizar predefinição
#XFLD Application
txtApplication=Aplicação
#XFLD Define new settings for this Task
txtNewSettings=Configurar novas definições para esta tarefa

#XFLD Use Default
txtUseDefault=Utilizar predefinição




