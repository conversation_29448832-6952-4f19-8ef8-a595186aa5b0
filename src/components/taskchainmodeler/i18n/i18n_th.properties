#XTOL Undo
@undo=เลิกทำ
#XTOL Redo
@redo=ทำซ้ำ
#XTOL Delete Selected Symbol
@deleteNode=ลบสัญลักษณ์ที่เลือก
#XTOL Zoom to Fit
@zoomToFit=ย่อ/ขยายให้พอดี
#XTOL Auto Layout
@autoLayout=โครงร่างอัตโนมัติ
#XMSG
@welcomeText=ลากแล้วปล่อยออบเจคจากแผงด้านซ้ายลงบนผืนผ้าใบนี้
#XMSG
@txtNoData=ดูเหมือนว่าคุณยังไม่ได้เพิ่มออบเจคใดๆ
#XMSG
VAL_ENTER_VALID_STRING_GEN=กรุณาป้อนสตริงที่ถูกต้องซึ่งมีความยาวน้อยกว่าหรือเท่ากับ {0}
#XMSG
@noParametersMsg=ขั้นตอนนี้ไม่มีพารามิเตอร์ป้อนข้อมูล
#XMSG
@ip_enterValueMsg="{0}" (ดำเนินการขั้นตอน SQL Script) มีพารามิเตอร์ป้อนข้อมูล คุณสามารถกำหนดค่าของแต่ละรายการได้
#XTOL
@validateModel=ข้อความการตรวจสอบความถูกต้อง
#XTOL
@hierarchy=ลำดับชั้น
#XTOL
@columnCount=จำนวนคอลัมน์
#XFLD
@yes=ใช่
#XFLD
@no=ไม่ใช่
#XTIT Save Dialog param
@modelNameTaskChain=เชนงาน
#properties panel
@lblPropertyTitle=คุณสมบัติ
#XFLD
@lblGeneral=ทั่วไป
#XFLD : Setting
@lblSetting=การกำหนดค่า
#XTXT: Delta Enabled Local Table Deletion
txtDeltaTableDeletion=ลบเรคคอร์ดทั้งหมดที่ดำเนินการเสร็จสมบูรณ์โดยมีประเภทการเปลี่ยนแปลง 'ลบแล้ว' ที่นานกว่า
#XTXT: Delta Enabled Local Table Deletion Input field description
desDeltaTableDeletion=วัน
#XFLD: Data Activation label
@lblDataActivation=การเปิดใช้งานข้อมูล
#XFLD: Latency label
@latency=เวลาแฝง
#XTEXT: Text for Latency dropdown
txtLatencyDefault=ค่าตั้งต้น
#XTEXT: Text 1 hour
txtOneHour=1 ชั่วโมง
#XTEXT: Text 2 hours
txtTwoHours=2 ชั่วโมง
#XTEXT: Text 3 hours
txtThreeHours=3 ชั่วโมง
#XTEXT: Text 4 hours
txtFourHours=4 ชั่วโมง
#XTEXT: Text 6 hours
txtSixHours=6 ชั่วโมง
#XTEXT: Text 12 hours
txtTwelveHours=12 ชั่วโมง
#XTEXT: Text 1 day
txtOneDay=1 วัน
#XFLD: Latency label
@autoRestartHead=การเริ่มต้นใหม่โดยอัตโนมัติ
#XFLD
@lblConnectionName=การเชื่อมต่อ
#XFLD
@lblQualifiedName=ชื่อที่มีคุณสมบัติตามที่กำหนด
#XFLD
@lblSpaceName=ชื่อพื้นที่
#XFLD
@lblLocalSchemaName=Schema ภายใน
#XFLD
@lblType=ประเภทออบเจค
#XFLD
@lblActivity=กิจกรรม
#XFLD
@lblTableName=ชื่อตาราง
#XFLD
@lblBusinessName=ชื่อทางธุรกิจ
#XFLD
@lblTechnicalName=ชื่อทางเทคนิค
#XFLD
@lblSpace=พื้นที่
#XFLD
@lblLabel=ป้ายชื่อ
#XFLD
@lblDataType=ประเภทข้อมูล
#XFLD
@lblDescription=คำอธิบาย
#XFLD
@lblStorageType=พื้นที่จัดเก็บ
#XFLD
@lblHTTPConnection=การเชื่อมต่อ HTTP ทั่วไป
#XFLD
@lblAPISettings=การกำหนดค่า API ทั่วไป
#XFLD
@header=ส่วนหัว
#XFLD
@lblInvoke=การเรียกใช้ API
#XFLD
@lblMethod=วิธีการ
#XFLD
@lblUrl=URL หลัก
#XFLD
@lblAPIPath=พาธ API
#XFLD
@lblMode=โหมด
#XFLD
@lblCSRFToken=ต้องมี CSRF Token
#XFLD
@lblTokenURL=URL ของ CSRF Token
#XFLD
@csrfTokenInfoText=หากไม่ได้ป้อนไว้ URL หลักและพาธ API จะถูกใช้
#XFLD
@lblCSRF=CSRF Token
#XFLD
@lblRequestBody=เนื้อหาคําขอ
#XFLD
@lblFormat=รูปแบบ
#XFLD
@lblResponse=การตอบกลับ
#XFLD
@lblId=ID เพื่อดึงข้อมูลสถานะ
#XFLD
@Id=ID
#XFLD
@lblSuccessIndicator=ตัวบ่งชี้ความสำเร็จ
#XFLD
@lblErrorIndicator=ตัวบ่งชี้ข้อผิดพลาด
#XFLD
@lblErrorReason=เหตุผลสำหรับข้อผิดพลาด
#XFLD
@lblStatus=สถานะ
#XFLD
@lblApiTestRun=การทดสอบการทำงานของ API
#XFLD
@lblRunStatus=สถานะการดำเนินการ
#XFLD
@lblLastRan=ดำเนินการครั้งล่าสุดเมื่อ
#XFLD
@lblTestRun=การทดสอบการทำงาน
#XFLD
@lblDefaultHeader=ฟิลด์ส่วนหัวตั้งต้น (คู่คีย์-ค่า)
#XFLD
@lblAdditionalHeader=ฟิลด์ส่วนหัวเพิ่มเติม
#XFLD
@lblKey=คีย์
#XFLD
@lblEditJSON=แก้ไข JSON
#XFLD
@lblTasks=งาน
#XFLD
@lblRESTfullTask=API
#XFLD: add field button text
btnAddField=เพิ่มฟิลด์ส่วนหัว
#XFLD: view Details link
@viewDetails=ดูรายละเอียด
#XTOL
tooltipTxt=เพิ่มเติม
#XTOL
delete=ลบ
#XBTN: ok button text
btnOk=ตกลง
#XBTN: cancel button text
btnCancel=ยกเลิก
#XBTN: save button text
btnSave=เก็บบันทึก
#XMSG Error: Object has duplicated technical name.
VAL_OBJECT_DUPLICATE_NAME=ออบเจค ''{0}'' มีอยู่ในพื้นที่เก็บข้อมูลแล้ว กรุณาป้อนชื่ออื่น
#XMSG: loading message while opening task chain
loadTaskChain=กำลังโหลดเชนงาน...
#model properties
#XFLD
@status_panel=สถานะการดำเนินการ
#XFLD
@deploy_status_panel=สถานะการปรับใช้
#XFLD
@status_lbl=สถานะ
#XFLD
@lblLastExecuted=การดำเนินการครั้งล่าสุด
#XFLD
@lblNotExecuted=ยังไม่ได้ดำเนินการ
#XFLD
@lblNotDeployed=ยังไม่ได้ปรับใช้
#XFLD
errorDetailsTxt=ไม่สามารถดึงข้อมูลสถานะการดำเนินการได้
#XBTN: Schedule dropdown menu
SCHEDULE=กำหนดการ
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=แก้ไขกำหนดการ
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=ลบกำหนดการ
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=สร้างกำหนดการ
#XLNK
viewDetails=ดูรายละเอียด
#XMSG: error message for reading execution status from backend
backendErrorMsg=ดูเหมือนว่าไม่มีการโหลดข้อมูลจากเซิร์ฟเวอร์อยู่ในขณะนี้ กรุณาลองดึงข้อมูลอีกครั้ง
#XFLD: Status text for Completed
@statusCompleted=เสร็จสมบูรณ์
#XFLD: Status text for Running
@statusRunning=กำลังดำเนินการ
#XFLD: Status text for Failed
@statusFailed=ล้มเหลว
#XFLD: Status text for Stopped
@statusStopped=ถูกหยุด
#XFLD: Status text for Stopping
@statusStopping=กำลังหยุด
#XFLD
@LoaderTitle=กำลังโหลด
#XFLD
#XFLD: Deploy status, dataflow has  been deployed / deployment date exists
@deployStatusActive=ปรับใช้แล้ว
@deployStatusRevised=การอัพเดทภายใน
@deployStatusFailed=ล้มเหลว
@deployStatusPending=กำลังปรับใช้...
@LoaderText=กำลังดึงข้อมูลรายละเอียดจากเซิร์ฟเวอร์
#XMSG
@msgDetailFetchError=เกิดข้อผิดพลาดขณะดึงข้อมูลรายละเอียดจากเซิร์ฟเวอร์
#XFLD
@executeError=ข้อผิดพลาด
#XFLD
@executeWarning=คำเตือน
#XMSG
@executeConfirmDialog=ข้อมูล
#XMSG
@executeunsavederror=เก็บบันทึกเชนงานของคุณก่อนดำเนินการ
#XMSG
@executemodifiederror=มีการเปลี่ยนแปลงที่ยังไม่ได้เก็บบันทึกในเชนงาน กรุณาเก็บบันทึกก่อน
#XMSG
@executerunningerror=เชนงานกำลังดำเนินการอยู่ในขณะนี้ รอจนกว่าการดำเนินการปัจจุบันจะเสร็จสมบูรณ์ก่อนเริ่มเชนงานใหม่
#XMSG
@btnExecuteAnyway=ยืนยันการดำเนินการ
#XMSG
@msgExecuteWithValidations=เชนงานมีข้อผิดพลาดในการตรวจสอบความถูกต้อง การดำเนินการเชนงานอาจล้มเหลว
#XMSG
@msgRunDeployedVersion=มีการเปลี่ยนแปลงให้ปรับใช้ เวอร์ชันเชนงานที่ปรับใช้ล่าสุดจะถูกดำเนินการ คุณต้องการดำเนินการต่อหรือไม่?
#XMSG
#XMSG
@navToMonitoring=เปิดในตัวติดตามตรวจสอบเชนงาน
#XMSG
txtOR=หรือ
#XFLD
@preview=แสดงตัวอย่าง
#XMSG
txtand=และ
#NOTR
@dateFormat=yyyy-MM-dd
#NOTR
@dateTimeFormatFilter=yyyy-MM-dd HH:mm:ss.
#NOTR
@timeFormat=HH:mm:ss
@lblColum=คอลัมน์
#XFLD
@lblCondition=เงื่อนไข
#XFLD
@lblValue=ค่า
#XMSG
@msgJsonInvalid=ไม่สามารถเก็บบันทึกเชนงานได้เนื่องจากมีข้อผิดพลาดใน JSON กรุณาตรวจสอบและแก้ปัญหา
#XTIT
@msgSaveFailTitle=JSON ไม่ถูกต้อง
#XMSG
NOT_CHAINABLE=ไม่สามารถเพิ่มออบเจค ''{0}'' ในเชนงานได้
#XMSG
NOT_CHAINABLE_REMOTETABLE=ออบเจค ''{0}'': ประเภทการทำสำเนาจะถูกเปลี่ยนแปลง
#XMSG
searchTaskChain=ค้นหาออบเจค

#XFLD
@txtTaskChain=เชนงาน
#XFLD
@txtRemoteTable=ตารางระยะไกล
#XFLD
@txtRemoveData=ย้ายข้อมูลที่ทำสำเนาออก
#XFLD
@txtRemovePersist=ย้ายข้อมูลที่คงไว้ออก
#XFLD
@txtView=มุมมอง
#XFLD
@txtDataFlow=ผังข้อมูล
#XFLD
@txtIL=การค้นหาแบบอัจฉริยะ
#XFLD
@txtTransformationFlow=ผังการแปลงข้อมูล
#XFLD
@txtReplicationFlow=ผังการทำสำเนา
#XFLD
@txtDeltaLocalTable=ตารางภายใน
#XFLD
@txtBWProcessChain=เชนกระบวนการ BW
#XFLD
@txtSQLScriptProcedure=ขั้นตอน SQL Script
#XFLD
@txtAPI=API
#XFLD
@txtMerge=ผสาน
#XFLD
@txtOptimize=ปรับให้เหมาะสม
#XFLD
@txtVacuum=ลบเรคคอร์ด

#XFLD
@txtRun=ดำเนินการ
#XFLD
@txtPersist=คงไว้
#XFLD
@txtReplicate=ทำสำเนา
#XFLD
@txtDelete=ลบเรคคอร์ดที่มีประเภทการเปลี่ยนแปลง 'ลบแล้ว'
#XFLD
@txtRunTC=ดำเนินการเชนงาน
#XFLD
@txtRunBW=ดำเนินการเชนกระบวนการ BW
#XFLD
@txtRunSQLScriptProcedure=ดำเนินการขั้นตอน SQL Script

#XFLD
@txtRunDataFlow=ดำเนินการผังข้อมูล
#XFLD
@txtPersistView=คงมุมมองไว้
#XFLD
@txtReplicateTable=ทำสำเนาตาราง
#XFLD
@txtRunIL=ดำเนินการการค้นหาแบบอัจฉริยะ
#XFLD
@txtRunTF=ดำเนินการผังการแปลงข้อมูล
#XFLD
@txtRunRF=ดำเนินการผังการทำสำเนา
#XFLD
@txtRemoveReplicatedData=ย้ายข้อมูลที่ทำสำเนาออก
#XFLD
@txtRemovePersistedData=ย้ายข้อมูลที่คงไว้ออก
#XFLD
@txtMergeData=ผสาน
#XFLD
@txtOptimizeData=ปรับให้เหมาะสม
#XFLD
@txtVacuumData=ลบเรคคอร์ด
#XFLD
@txtRunAPI=รัน API

#XFLD storage type text
hdlfStorage=ไฟล์

@statusNew=ยังไม่ได้ปรับใช้
@statusActive=ปรับใช้แล้ว
@statusRevised=การอัพเดทภายใน
@statusPending=กำลังปรับใช้...
@statusChangesToDeploy=มีการเปลี่ยนแปลงให้ปรับใช้
@statusDesignTimeError=ข้อผิดพลาดของ Design Time
@statusRunTimeError=ข้อผิดพลาดของรันไทม์

#XTIT
txtNodes=ออบเจคในเชนงาน ({0})
#XBTN
@deleteNodes=ลบ

#XMSG
@txtDropDataToDiagram=ลากแล้วปล่อยออบเจคในไดอะแกรม
#XMSG
@noData=ไม่มีออบเจค

#XFLD
@txtTaskPosition=ตำแหน่งของงาน

#input parameters and variables
#XFLD
lblInputParameters=พารามิเตอร์ป้อนข้อมูล
#XFLD
ip_name=ชื่อ
#XFLD
ip_value=ค่า
#XTEXT
@noObjectsFound=ไม่พบออบเจค

#XMSG
@msgExecuteSuccess=การดำเนินการเชนงานเริ่มต้นแล้ว
#XMSG
@msgExecuteFail=ไม่สามารถดำเนินการเชนงานได้
#XMSG
@msgDeployAndRunSuccess=การดำเนินการและการปรับใช้เชนงานเริ่มต้นแล้ว
#XMSG
@msgDeployAndRunFail=ไม่สามารถปรับใช้และดำเนินการเชนงาน
#XMSG
@titleExecuteBusy=กรุณารอสักครู่
#XMSG
@msgExecuteBusy=เรากำลังจัดเตรียมข้อมูลของคุณเพื่อเริ่มการดำเนินการเชนงาน
#XMSG
@msgAPITestRunSuccess=การทดสอบการทำงานของ API เริ่มต้นแล้ว
#XMSG
@msgAPIExecuteBusy=เรากำลังจัดเตรียมข้อมูลของคุณเพื่อเริ่มดำเนินการทดสอบการทำงานของ API

#XTOL
txtOpenInEditor=เปิดในเอดิเตอร์
#XTOL
txtPreviewData=แสดงตัวอย่างข้อมูล

#datapreview
#XMSG
@msgDataPreviewNotSupp=การแสดงตัวอย่างข้อมูลไม่พร้อมใช้งานสำหรับออบเจคนี้

#XMSG Error: empty model
VAL_MODEL_EMPTY=ดูเหมือนว่าโมเดลของคุณจะว่างเปล่า กรุณาเพิ่มออบเจค
#XMSG Error: deploy model
@msgDeployBeforeRun=คุณต้องปรับใช้เชนงานก่อนดำเนินการ
#BTN: close dialog
btnClose=ปิด

#XMSG Object is not Deployed
VAL_NOT_DEPLOYED=ต้องปรับใช้ออบเจค ''{0}'' เพื่อดำเนินการเชนงานต่อ
#XMSG Object is having runtime error
VAL_RUNTIME_ERROR=ออบเจค ''{0}'' มีข้อผิดพลาดของรันไทม์ กรุณาตรวจสอบออบเจค
#XMSG Object is having design time error
VAL_DESIGNTIME_ERROR=ออบเจค ''{0}'' มีข้อผิดพลาดของ Design Time กรุณาตรวจสอบออบเจค
#XMSG Procedure Object has been deleted
VAL_DELETED_PROCEDURE=ขั้นตอนต่อไปนี้ถูกลบแล้ว: {0}
#XMSG New Parameter added to Procedure object
VAL_NEW_PARAMETER_ADDED=เพิ่มพารามิเตอร์ใหม่ในขั้นตอน "{1}": "{0}" แล้ว กรุณาปรับใช้เชนงานอีกครั้ง
#XMSG Parameter removed from Procedure object
VAL_PARAMETER_DELETED=พารามิเตอร์ถูกย้ายออกจากขั้นตอน "{1}": "{0}" แล้ว กรุณาปรับใช้เชนงานอีกครั้ง
#XMSG Parameter data type changed in Procedure object
VAL_PARAMETER_DATATYPE_MODIFIED=ประเภทข้อมูลของพารามิเตอร์ "{0}" ถูกเปลี่ยนจาก "{1}" เป็น "{2}" แล้วในขั้นตอน "{3}"
#XMSG Parameter length changed of specific datatype in Procedure object
VAL_PARAMETER_LENGTH_MODIFIED=ความยาวของพารามิเตอร์ "{0}" ถูกเปลี่ยนจาก "{1}" เป็น "{2}" แล้วในขั้นตอน "{3}"
#XMSG Parameter precision changed of Decimal datatype in Procedure object
VAL_PARAMETER_PRECISION_MODIFIED=ความแม่นยำของพารามิเตอร์ "{0}" ถูกเปลี่ยนจาก "{1}" เป็น "{2}" แล้วในขั้นตอน "{3}"
#XMSG Parameter scale changed of Decimal datatype in Procedure object
VAL_PARAMETER_SCALE_MODIFIED=สเกลของพารามิเตอร์ "{0}" ถูกเปลี่ยนจาก "{1}" เป็น "{2}" แล้วในขั้นตอน "{3}"
#XMSG Parameter value not provided in Procedure object
VAL_SQL_SCRIPT_PROCEDURE_PARAMETER_VALUE_MISSING=คุณไม่ได้ป้อนค่าสำหรับพารามิเตอร์ป้อนข้อมูลที่จำเป็นสำหรับขั้นตอน "{0}": "{1}"
#XMSG Remote Table activity changes to Remove Replicated Data
VAL_REMOVE_REPLICATED_DATA=การดำเนินการถัดไปของเชนงานจะเปลี่ยนแปลงประเภทการเข้าถึงข้อมูลและข้อมูลจะไม่ได้รับการอัพโหลดตามเวลาจริงอีกต่อไป
#XMSG Remote Table activity changes
REPLICATION_TYPE_CHANGED=ออบเจค "{0}": ประเภทการทำสำเนาจะถูกเปลี่ยนแปลง

#XFLD
@lblStartNode=โหนดเริ่มต้น
#XFLD
@lblEndNode=โหนดสิ้นสุด
#XFLD
@linkTo={0} ไปยัง {1}
#XTOL
@txtViewDetails=ดูรายละเอียด

#XTOL
txtOpenImpactLineage=การวิเคราะห์ผลกระทบและสายข้อมูล
#XFLD
@emailNotifications=การแจ้งทางอีเมล์
#XFLD
@txtReset=รีเซ็ต
#XFLD
@emailMsgWarning=อีเมล์แบบร่างตั้งต้นจะถูกส่งเมื่อข้อความอีเมล์ว่างเปล่า
#XFLD
@notificationSettings=การกำหนดค่าการแจ้ง
#XFLD
@recipientEmailAddr=อีเมล์แอดเดรสของผู้รับ
#XFLD
@emailSubject=ชื่อเรื่องอีเมล์
@emailSubjectText=เชนงาน <TASKCHAIN_NAME> เสร็จสมบูรณ์โดยมีสถานะ <STATUS>
#XFLD
@emailMessage=ข้อความอีเมล์
@emailMessageText=เรียนผู้ใช้\n\n ขอแจ้งให้คุณทราบว่าเชนงาน:<TASKCHAIN_NAME> ที่ดำเนินการเมื่อ <START_TIME> เสร็จสิ้นแล้วโดยมีสถานะ <STATUS> การดำเนินการสิ้นสุดเมื่อ <END_TIME>\n\nรายละเอียด:\nพื้นที่:<SPACE_NAME>\nข้อผิดพลาด:<ERROR_MESSAGE>
@selectEmailRecipientDialogTitle=เลือกอีเมล์แอดเดรสของผู้รับ
@tenantMembers=สมาชิกของ Tenant
@others=อื่นๆ ({0})
@selectedEmailAddress=ผู้รับที่เลือก
@add=เพิ่ม
@placeholder=เพลซโฮลเดอร์
@description=คำอธิบาย
@copyText=คัดลอกข้อความ
@taskchainDetailsPlaceholder=เพลซโฮลเดอร์สำหรับรายละเอียดของเชนงาน
@placeholderCopied=เพลซโฮลเดอร์ถูกคัดลอก
@invalidEmailInfo=ป้อนอีเมล์แอดเดรสที่ถูกต้อง
@maxMembersAlreadyAdded=คุณเพิ่มสมาชิกสูงสุด 20 รายแล้ว
@enterEmailAddress=ป้อนอีเมล์แอดเดรสของผู้ใช้
@inCorrectPlaceHolder={0} ไม่ใช่เพลซโฮลเดอร์ที่คาดไว้ในเนื้อหาอีเมล์
@nsOFF=ไม่ต้องส่งการแจ้งใดๆ
@nsFAILED=ส่งการแจ้งทางอีเมล์ก็ต่อเมื่อการดำเนินการเสร็จสมบูรณ์โดยมีข้อผิดพลาด
@nsCOMPLETED=ส่งการแจ้งทางอีเมล์ก็ต่อเมื่อการดำเนินการเสร็จสมบูรณ์แล้ว
@nsANY=ส่งอีเมล์เมื่อการดำเนินการเสร็จสมบูรณ์
@phStatus=สถานะของเชนงาน - สำเร็จ|ล้มเหลว
@phTaskChainTName=ชื่อทางเทคนิคของเชนงาน
@phTaskChainBName=ชื่อทางธุรกิจของเชนงาน
@phLogId=ID ล็อกของการดำเนินการ
@phUser=ผู้ใช้ที่กำลังดำเนินการเชนงาน
@phLogUILink=ลิงก์ไปยังการแสดงล็อกของเชนงาน
@phStartTime=เวลาเริ่มต้นของการดำเนินการ
@phEndTime=เวลาสิ้นสุดของการดำเนินการ
@phErrMsg=ข้อความแสดงข้อผิดพลาดแรกในล็อกของงาน ล็อกจะว่างเปล่าในกรณีที่ 'สำเร็จ'
@phSpaceName=ชื่อทางเทคนิคของพื้นที่
@emailFormatError=รูปแบบอีเมล์ไม่ถูกต้อง
@emailFormatErrorInListText=มีการป้อนรูปแบบอีเมล์ที่ไม่ถูกต้องในรายการสมาชิกที่ไม่ใช่ Tenant
@emailSubjectTemplateText=การแจ้งสำหรับเชนงาน: $$taskChainName$$ - พื้นที่: $$spaceId$$ - สถานะ: $$status$$
@emailMessageTemplateText=สวัสดี\n\n เชนงานของคุณที่มีป้ายชื่อ $$taskChainName$$ เสร็จสิ้นโดยมีสถานะ $$status$$ \n ต่อไปนี้คือรายละเอียดอื่นๆ บางอย่างเกี่ยวกับเชนงาน:\n - ชื่อทางเทคนิคของเชนงาน: $$taskChainName$$ \n - ID ล็อกของการดำเนินการเชนงาน: $$logId$$ \n - ผู้ใช้ที่ดำเนินการเชนงาน: $$user$$ \n - ลิงก์ไปยังการแสดงล็อกของเชนงาน: $$uiLink$$ \n - เวลาเริ่มต้นของการดำเนินการเชนงาน: $$startTime$$ \n - เวลาสิ้นสุดของการดำเนินการเชนงาน: $$endTime$$ \n - ชื่อพื้นที่: $$spaceId$$ \n
@deleteEmailRecepient=ลบผู้รับ
@emailInputDisabledText=กรุณาปรับใช้เชนงานเพื่อเพิ่มผู้รับอีเมล์
@tenantOwnerDomainMatchErrorText=โดเมนของอีเมล์แอดเดรสไม่ตรงกับโดเมนของเจ้าของ Tenant: {0}
@totalEmailIdLimitInfoText=คุณสามารถเลือกผู้รับอีเมล์ได้ถึง 20 รายรวมถึงผู้ใช้ที่เป็นสมาชิกของ Tenant และผู้รับอื่นๆ
@emailDomainInfoText=ยอมรับเฉพาะอีเมล์แอดเดรสที่มีโดเมน: {0} เท่านั้น
@duplicateEmailErrorText=มีผู้รับอีเมล์ที่ซ้ำกันในรายการ

#XFLD Zorder Title
@txtZorderTitle=คอลัมน์ลำดับ Z ของ Apache Spark

#XFLD Zorder NoColumn
@txtZorderNoColumn=ไม่พบคอลัมน์ลำดับ Z

#XFLD Zorder Column PrimaryKey
@txtZorderColumnPrimaryKey=คีย์หลัก

#XFLD
@lblOperators=ตัวดำเนินการ
addNewSelector=เพิ่มเป็นงานใหม่
parallelSelector=เพิ่มเป็นงานแบบขนาน
replaceSelector=แทนที่งานที่มีอยู่
addparallelbranch=เพิ่มเป็นสาขาแบบขนาน
addplaceholder=เพิ่มเพลซโฮลเดอร์
addALLOperation=ตัวดำเนินการ 'ALL'
addOROperation=ตัวดำเนินการ 'ANY'
addplaceholdertocanvas=เพิ่มเพลซโฮลเดอร์ในผืนผ้าใบ
addplaceholderonselected=เพิ่มเพลซโฮลเดอร์หลังงานที่เลือก
#XFLD: Add Parallel Branch to Selected Task
addparallelbranchonselected=เพิ่มสาขาแบบขนานหลังงานที่เลือก
addOperator=เพิ่มตัวดำเนินการ
txtAdd=เพิ่ม
txtPlaceHolderText=ลากแล้วปล่อยงานที่นี่
@lblLayout=โครงร่าง

#XMSG
VAL_UNCONNECTED_TASK=งาน ''{0}'' ไม่เชื่อมต่อกับเชนงาน
#XMSG
VAL_TASK_SHOULD_HAVE_ONE_INCOMING_LINK=งาน ''{0}'' ควรมีการเชื่อมโยงขาเข้าเพียงหนึ่งรายการ
#XMSG
VAL_TASK_SHOULD_HAVE_INCOMING_LINK=งาน ''{0}'' ควรมีการเชื่อมโยงขาเข้าหนึ่งรายการ
#XMSG
VAL_UNCONNECTED_OPERATOR=ตัวดำเนินการ ''{0}'' ไม่เชื่อมต่อกับเชนงาน
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ATLEAST_TWO_INCOMING_LINK=ตัวดำเนินการ ''{0}'' ควรมีการเชื่อมโยงขาเข้าอย่างน้อยสองรายการ
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ONE_OUT_LINK=ตัวดำเนินการ ''{0}'' ควรมีการเชื่อมโยงขาออกอย่างน้อยหนึ่งรายการ
#XMSG
VAL_CIRCULAR_REFERENCE_EXITS=มีลูปแบบวงกลมในเชนงาน ''{0}''
#XMSG
VAL_UNCONNECTED_BRANCH=ออบเจค/สาขา ''{0}'' ไม่เชื่อมต่อกับเชนงาน
#XMSG
VAL_TASK_TRIGGERED_PARALLEL_MORE_THAN_ONE_TIME=งาน ''{0}'' มีการเชื่อมต่อแบบขนานมากกว่าหนึ่งครั้ง กรุณาย้ายรายการที่ซ้ำกันออกเพื่อดำเนินการต่อ


txtBegin=เริ่มต้น
txtNodesInLink=ออบเจคที่เกี่ยวข้อง
#XTOL Tooltip for a context button on diagram
openInNewTab=เปิดในแท็บใหม่
#XTOL Tooltip for a context button on diagram
txtConnectToTaskOperator=ลากเพื่อเชื่อมต่อ
@emailUpdateError=เกิดข้อผิดพลาดในการอัพเดทรายการการแจ้งทางอีเมล์

#XMSG
noTeamPrivilegeTxt=คุณไม่มีสิทธิในการดูรายการสมาชิกของ Tenant กรุณาใช้แท็บ 'อื่นๆ' เพื่อเพิ่มผู้รับอีเมล์ด้วยตนเอง

#XFLD Package
@txtPackage=แพคเกจ

#XMSG warning for repository package assignment changed. {1} - package name
VAL_PACKAGE_CHANGED=คุณได้กำหนดออบเจคนี้ให้กับแพคเกจ ''{1}'' คลิก ''เก็บบันทึก'' เพื่อยืนยันและตรวจสอบการเปลี่ยนแปลงนี้ กรุณาจำไว้ว่าการกำหนดให้กับแพคเกจไม่สามารถเลิกทำได้ในเอดิเตอร์นี้หลังจากที่คุณเก็บบันทึก
#XMSG warning for repository package dependency issue.
MISSING_DEPENDENCY=ไม่สามารถแก้ไขความสัมพันธ์ของออบเจค ''{0}'' ในเนื้อหาของแพคเกจ ''{1}''

#Warning messages for the folders in the "Others" tab of task chain editor
#XMSG generic warning if getting the objects in a particular folder fails with unhandled errors
getNonRepoArtefactTypeDescendantsError=มีปัญหาในการดึงข้อมูลออบเจคในแฟ้มที่คุณเลือก
#BW Process Chains folder related warning messages
#XMSG warning if user does not have the necessary permission related to the SAP BW Bridge space
missingPrivilegeForBridgeSpaceError=คุณไม่มีสิทธิที่จำเป็นในการดูหรือรวมเชนกระบวนการ BW ในเชนงาน
#XMSG warning if there was a problem retrieving the BW process chains due to bridge space membership, bridge connection issues or other errors
generalBridgeSpaceError=มีปัญหาในการดึงข้อมูลเชนกระบวนการ BW
#XMSG warning if the BW API for retrieving the BW process chains from the SAP BW Bridge tenant fails
bwApiClientError=มีปัญหาในการดึงข้อมูลเชนกระบวนการ BW จาก Tenant บริดจ์ของ SAP BW
#XMSG warning if BW process chains list is empty
bwApiEmptyListError=ไม่พบเชนกระบวนการ BW ใน Tenant บริดจ์ของ SAP BW
#XMSG: Task log message for invalid OIDC configuration in the SAP BW Bridge tenant
bwBridgeOIDCConfigStatusInvalid=การรับรองความถูกต้องด้วย OpenId ไม่ได้รับการกำหนดรูปแบบสำหรับ Tenant นี้ กรุณาใช้ clientID "{0}" และ tokenURL "{1}" เพื่อกำหนดรูปแบบการรับรองความถูกต้องด้วย OpenId ใน Tenant บริดจ์ของ SAP BW ตามที่อธิบายไว้ใน SAP Note 3536298
#XMSG BW Process Chain Object has been deleted
VAL_DELETED_BW=เชนกระบวนการ BW ต่อไปนี้อาจถูกลบออกจาก Tenant บริดจ์ของ SAP: {0}
#BW SQL Script Procedures folder related warning messages
#XFLD waring if the Procedure list is empty
procedureAPIEmptyList=ไม่มีการสร้างขั้นตอนหรือไม่ได้รับสิทธิพิเศษ EXECUTE สำหรับ Open SQL Schema
#Change digram orientations
changeOrientations=เปลี่ยนแปลงทิศทาง


# placeholder for the API Path
apiPath=ตัวอย่างเช่น: /job/v1
# placeholder for the status API Path
statusAPIPath=ตัวอย่างเช่น: /job/v1/{id}/status
# valueStateText for the API Path
apiPathRequired=ต้องระบุพาธ API
#placeholder for the CSRF Token URL
csrfTokenURL=รองรับเฉพาะ HTTPS เท่านั้น
# Response Type 1
statusCode=ดึงข้อมูลผลลัพธ์จากรหัสสถานะ HTTP
# Response Type 2
locationHeader=ดึงข้อมูลผลลัพธ์จากรหัสสถานะ HTTP และส่วนหัวของที่ตั้ง
# Response Type 3
responseBody=ดึงข้อมูลผลลัพธ์จากรหัสสถานะ HTTP และเนื้อหาการตอบกลับ
# placeholder for ID
idPlaceholder=ป้อนพาธ JSON
# placeholder for indicator value
indicatorValue=ป้อนค่า
# Placeholder for key
keyPlaceholder=ป้อนคีย์
# Error message for missing key
KeyRequired=ต้องระบุคีย์
# Error message for invalid key format
invalidKeyFormat=คีย์ส่วนหัวที่คุณป้อนไม่ได้รับอนุญาต ส่วนหัวที่ถูกต้องคือ:<ul><li>"prefer"</li><li>ส่วนหัวที่ขึ้นต้นด้วย "x-" ยกเว้น "x-forwarded-host"</li><li>ส่วนหัวที่มีอักขระตัวอักษรผสมตัวเลข, "-" หรือ "_"</li></ul>
# Error message for missing value
valueRequired=ต้องระบุค่า
# Error message for invalid characters in value
invalidValueCharacters=ส่วนหัวมีอักขระที่ไม่ถูกต้อง อักขระพิเศษที่อนุญาต ได้แก่:\t ";", ":", "-", "_", ",", "?", "/" และ "*"
# Validation message for invoke api path
apiPathValidation=กรุณาป้อนพาธ API ที่ถูกต้อง ตัวอย่างเช่น: /job/v1
# Validation message for JSON path
jsonPathValidation=กรุณาป้อนพาธ JSON ที่ถูกต้อง
# Validation message for success/error indicator
indicatorValueValidation=ค่าตัวบ่งชี้ต้องขึ้นต้นด้วยอักขระตัวอักษรผสมตัวเลขและสามารถมีอักขระพิเศษต่อไปนี้:\t "-" และ "_"
# Error message for JSON path
jsonPathRequired=ต้องระบุพาธ JSON
# Error message for invalid API Technical Name
invalidTechnicalName=ชื่อทางเทคนิคมีอักขระที่ไม่ถูกต้อง
# Error message for empty Technical Name
emptyTechnicalName=ต้องระบุชื่อทางเทคนิค
# Tooltip for codeEditor dialog
codeEditorTooltip=เปิดหน้าต่างแก้ไข JSON
# Status Api path validation message
validationStatusAPIPath=กรุณาป้อนพาธ API ที่ถูกต้อง ตัวอย่างเช่น: /job/v1/{id}/status
# CSRF token URL validation message
validationCSRFTokenURL=URL ของ CSRF Token ต้องขึ้นต้นด้วย 'https://' และต้องเป็น URL ที่ถูกต้อง
# Select a connection
selectConnection=เลือกการเชื่อมต่อ
# Validation message for connection item error
connectionNotReplicated=การเชื่อมต่อในขณะนี้ไม่ถูกต้องสำหรับการดำเนินการงาน API เปิดแอพ "การเชื่อมต่อ" และป้อนข้อมูลประจำตัวของคุณอีกครั้งเพื่อแก้ไขการเชื่อมต่อ HTTP
# Validation message for API task properties wrong value or missing
VAL_REST_API_PROPERTIES_MISSING=งาน API "{0}" มีค่าที่ไม่ถูกต้องหรือขาดหายไปสำหรับคุณสมบัติต่อไปนี้: {1}
# Validation message for API connection api is throwing some error or selected connection has some warning
VAL_REST_API_CONNECTION=งาน API "{0}" มีปัญหากับการเชื่อมต่อ HTTP เปิดแอพ "การเชื่อมต่อ" และตรวจสอบการเชื่อมต่อ
# Validation message for API task properties when delected connection
VAL_REST_API_CONNECTION_DELETED=งาน API "{0}" มีการเชื่อมต่อ "{1}" ที่ถูกลบ
# Validation message for API task properties when connection cannot be consumed in the API task
VAL_REST_API_CONNECTION_CONSUMPTION_WARNING=งาน API "{0}" มีการเชื่อมต่อ "{1}" ที่ไม่สามารถใช้เพื่อดำเนินการงาน API ได้ เปิดแอพ "การเชื่อมต่อ" แล้วป้อนข้อมูลประจำตัวของคุณอีกครั้งเพื่อสร้างการเชื่อมต่อสำหรับงาน API
# Information message if synchrnous mode is selected in the API Invocation then we don't need status information
modeInfoText=ในโหมดซิงโครนัส แผงสถานะจะไม่ปรากฏสำหรับการเรียกใช้ API
# validation dialog button for Run API Test
saveAnyway=ยืนยันการเก็บบันทึก
# validation message for technical name
technicalNameValidation=ชื่อทางเทคนิคต้องไม่ซ้ำกันภายในเชนงาน กรุณาเลือกชื่อทางเทคนิคอื่น
# Connection error message
getHttpConnectionsError=ไม่สามารถดึงข้อมูลการเชื่อมต่อ HTTP
# Msg for the API test run when the Task chain is not saved
taskChainNotSaved=ต้องเก็บบันทึกเชนงานก่อนที่จะดำเนินการทดสอบการทำงานของ API
# Msg failed to run API test run
@failedToRunAPI=ไม่สามารถดำเนินการทดสอบการทำงานของ API
# Msg for the API test run when its already in running state
apiTaskRunning=การทดสอบการทำงานของ API อยู่ระหว่างดำเนินการแล้ว คุณต้องการเริ่มต้นการทดสอบการทำงานใหม่หรือไม่?

topToBtm=บน-ล่าง
leftToRight=ซ้าย-ขวา

#XTIT Apache Spark Application Settings
txtHeaderApacheSettings=การกำหนดค่าแอพพลิเคชัน Apache Spark
#XFLD Use Default
txtUseSpaceDefault=ใช้ค่าตั้งต้น
#XFLD Application
txtApplication=แอพพลิเคชัน
#XFLD Define new settings for this Task
txtNewSettings=ระบุการกำหนดค่าใหม่สำหรับงานนี้

#XFLD Use Default
txtUseDefault=ใช้ค่าตั้งต้น




