#XTOL Undo
@undo=Desfer
#XTOL Redo
@redo=Refer
#XTOL Delete Selected Symbol
@deleteNode=Suprimir el símbol seleccionat
#XTOL Zoom to Fit
@zoomToFit=Ampliar fins a ajustar
#XTOL Auto Layout
@autoLayout=Disposició automàtica
#XMSG
@welcomeText=Arrossegueu i deixeu anar objectes del panell de l'esquerra en aquest llenç.
#XMSG
@txtNoData=Sembla que encara no heu afegit cap objecte.
#XMSG
VAL_ENTER_VALID_STRING_GEN=Introduïu una cadena vàlida amb una longitud inferior o igual que {0}.
#XMSG
@noParametersMsg=Aquest procediment no té paràmetres d'entrada.
#XMSG
@ip_enterValueMsg=El "{0}" (procediment SQLScript d''execució) té paràmetres d''entrada. Podeu fixar un valor per a cadascun d''ells.
#XTOL
@validateModel=Missatges de validació
#XTOL
@hierarchy=Jerarquia
#XTOL
@columnCount=Número de columnes
#XFLD
@yes=Sí
#XFLD
@no=No
#XTIT Save Dialog param
@modelNameTaskChain=Cadenes de tasques
#properties panel
@lblPropertyTitle=Propietats
#XFLD
@lblGeneral=General
#XFLD : Setting
@lblSetting=Opcions
#XTXT: Delta Enabled Local Table Deletion
txtDeltaTableDeletion=Eliminar tots els registres registrats totalment amb el tipus de modificació "Suprimit" que tinguin més de
#XTXT: Delta Enabled Local Table Deletion Input field description
desDeltaTableDeletion=Dies
#XFLD: Data Activation label
@lblDataActivation=Activació de dades
#XFLD: Latency label
@latency=Latència
#XTEXT: Text for Latency dropdown
txtLatencyDefault=Estàndard
#XTEXT: Text 1 hour
txtOneHour=1 hora
#XTEXT: Text 2 hours
txtTwoHours=2 hores
#XTEXT: Text 3 hours
txtThreeHours=3 hores
#XTEXT: Text 4 hours
txtFourHours=4 hores
#XTEXT: Text 6 hours
txtSixHours=6 hores
#XTEXT: Text 12 hours
txtTwelveHours=12 hores
#XTEXT: Text 1 day
txtOneDay=1 dia
#XFLD: Latency label
@autoRestartHead=Reinici automàtic
#XFLD
@lblConnectionName=Connexió
#XFLD
@lblQualifiedName=Nom qualificat
#XFLD
@lblSpaceName=Nom d’espai
#XFLD
@lblLocalSchemaName=Esquema local
#XFLD
@lblType=Classe d'objecte
#XFLD
@lblActivity=Activitat
#XFLD
@lblTableName=Nom de la taula
#XFLD
@lblBusinessName=Nom empresarial
#XFLD
@lblTechnicalName=Nom tècnic
#XFLD
@lblSpace=Espai
#XFLD
@lblLabel=Etiqueta
#XFLD
@lblDataType=Tipus de dades
#XFLD
@lblDescription=Descripció
#XFLD
@lblStorageType=Emmagatzematge
#XFLD
@lblHTTPConnection=Connexió genèrica HTTP
#XFLD
@lblAPISettings=Opcions genèriques d'API
#XFLD
@header=Capçaleres
#XFLD
@lblInvoke=Crida d'API
#XFLD
@lblMethod=Mètode
#XFLD
@lblUrl=URL base
#XFLD
@lblAPIPath=Via d'accés d’API
#XFLD
@lblMode=Mode
#XFLD
@lblCSRFToken=Requerir marca CSRF
#XFLD
@lblTokenURL=URL de token CSRF
#XFLD
@csrfTokenInfoText=Si no s'indica, s'utilitzaran l'URL base i la via d'accés d'API
#XFLD
@lblCSRF=Marca CSRF
#XFLD
@lblRequestBody=Cos de sol·licitud
#XFLD
@lblFormat=Format
#XFLD
@lblResponse=Resposta
#XFLD
@lblId=ID per recuperar estat
#XFLD
@Id=ID
#XFLD
@lblSuccessIndicator=Indicador d'èxit
#XFLD
@lblErrorIndicator=Indicador d'error
#XFLD
@lblErrorReason=Motiu de l'error
#XFLD
@lblStatus=Estat
#XFLD
@lblApiTestRun=Execució de prova d'API
#XFLD
@lblRunStatus=Estat d’execució
#XFLD
@lblLastRan=Data de l'última execució
#XFLD
@lblTestRun=Execució de prova
#XFLD
@lblDefaultHeader=Camps de capçalera per defecte (parells clau-valor)
#XFLD
@lblAdditionalHeader=Camp de capçalera addicional
#XFLD
@lblKey=Clau
#XFLD
@lblEditJSON=Editar JSON
#XFLD
@lblTasks=Tasques
#XFLD
@lblRESTfullTask=API
#XFLD: add field button text
btnAddField=Afegir camp de capçalera
#XFLD: view Details link
@viewDetails=Veure detalls
#XTOL
tooltipTxt=Més
#XTOL
delete=Suprimir
#XBTN: ok button text
btnOk=D'acord
#XBTN: cancel button text
btnCancel=Cancel·lar
#XBTN: save button text
btnSave=Desar
#XMSG Error: Object has duplicated technical name.
VAL_OBJECT_DUPLICATE_NAME=L''objecte "{0}" ja existeix al dipòsit. Escriviu un altre nom.
#XMSG: loading message while opening task chain
loadTaskChain=S'està carregant la cadena de tasques...
#model properties
#XFLD
@status_panel=Estat d’execució
#XFLD
@deploy_status_panel=Estat de desplegament
#XFLD
@status_lbl=Estat
#XFLD
@lblLastExecuted=Última execució
#XFLD
@lblNotExecuted=Encara no s’ha executat
#XFLD
@lblNotDeployed=No desplegat
#XFLD
errorDetailsTxt=No s'ha pogut obtenir l'estat d'execució
#XBTN: Schedule dropdown menu
SCHEDULE=Programar
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Editar programa
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Suprimir programa
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Crear programa
#XLNK
viewDetails=Veure detalls
#XMSG: error message for reading execution status from backend
backendErrorMsg=Sembla que en aquest moment les dades no es carreguen del servidor. Proveu de tornar a obtenir les dades.
#XFLD: Status text for Completed
@statusCompleted=Conclòs
#XFLD: Status text for Running
@statusRunning=En execució
#XFLD: Status text for Failed
@statusFailed=Erroni
#XFLD: Status text for Stopped
@statusStopped=Aturat
#XFLD: Status text for Stopping
@statusStopping=Aturant
#XFLD
@LoaderTitle=Carregant
#XFLD
#XFLD: Deploy status, dataflow has  been deployed / deployment date exists
@deployStatusActive=Desplegat
@deployStatusRevised=Actualitzacions locals
@deployStatusFailed=Error
@deployStatusPending=Desplegant...
@LoaderText=S'estan obtenint els detalls del servidor
#XMSG
@msgDetailFetchError=Error en obtenir detalls del servidor
#XFLD
@executeError=Error
#XFLD
@executeWarning=Advertència
#XMSG
@executeConfirmDialog=Informació
#XMSG
@executeunsavederror=Deseu la cadena de tasques abans d'executar-la.
#XMSG
@executemodifiederror=Hi ha modificacions sense desar a la cadena de tasques. Deseu-les.
#XMSG
@executerunningerror=La cadena de tasques s'està executant. Espereu que hagi conclòs l'execució actual abans de començar-ne una de nova.
#XMSG
@btnExecuteAnyway=Executar igualment
#XMSG
@msgExecuteWithValidations=La cadena de tasques té errors de validació. Si s'executa es pot produir un error.
#XMSG
@msgRunDeployedVersion=Hi ha modificacions per desplegar. S'executarà la darrera versió desplegada de la cadena de tasques. Voleu continuar?
#XMSG
#XMSG
@navToMonitoring=Obrir al monitor de cadena de tasques
#XMSG
txtOR=O
#XFLD
@preview=Previsualització
#XMSG
txtand=i
#NOTR
@dateFormat=yyyy-MM-dd
#NOTR
@dateTimeFormatFilter=yyyy-MM-dd HH:mm:ss.
#NOTR
@timeFormat=HH:mm:ss
@lblColum=Columna
#XFLD
@lblCondition=Condició
#XFLD
@lblValue=Valor
#XMSG
@msgJsonInvalid=La cadena de tasques no s'ha pogut desar perquè hi ha errors al fitxer JSON. Verifiqueu-ho i resoleu-ho.
#XTIT
@msgSaveFailTitle=El fitxer JSON no és vàlid.
#XMSG
NOT_CHAINABLE=L''objecte "{0}" no es pot afegir a la cadena de tasques.
#XMSG
NOT_CHAINABLE_REMOTETABLE=Objecte ''{0}'': el tipus de replicació canviarà.
#XMSG
searchTaskChain=Cercar objectes

#XFLD
@txtTaskChain=Cadena de tasques
#XFLD
@txtRemoteTable=Taula remota
#XFLD
@txtRemoveData=Eliminar dades replicades
#XFLD
@txtRemovePersist=Eliminar dades persistides
#XFLD
@txtView=Vista
#XFLD
@txtDataFlow=Flux de dades
#XFLD
@txtIL=Cerca intel·ligent
#XFLD
@txtTransformationFlow=Flux de transformació
#XFLD
@txtReplicationFlow=Flux de replicació
#XFLD
@txtDeltaLocalTable=Taula local
#XFLD
@txtBWProcessChain=Cadena de processos BW
#XFLD
@txtSQLScriptProcedure=Procediment SQLScript
#XFLD
@txtAPI=API
#XFLD
@txtMerge=Fusionar
#XFLD
@txtOptimize=Optimitza
#XFLD
@txtVacuum=Suprimir registres

#XFLD
@txtRun=Executar
#XFLD
@txtPersist=Persistir
#XFLD
@txtReplicate=Replicar
#XFLD
@txtDelete=Suprimir registres amb el tipus de modificació "Suprimit"
#XFLD
@txtRunTC=Executar la cadena de tasques
#XFLD
@txtRunBW=Executar cadena de processos BW
#XFLD
@txtRunSQLScriptProcedure=Executar procediment SQLScript

#XFLD
@txtRunDataFlow=Executar flux de dades
#XFLD
@txtPersistView=Persistir vista
#XFLD
@txtReplicateTable=Replicar taula
#XFLD
@txtRunIL=Executar cerca intel·ligent
#XFLD
@txtRunTF=Executar flux de transformació
#XFLD
@txtRunRF=Executar flux de replicació
#XFLD
@txtRemoveReplicatedData=Eliminar dades replicades
#XFLD
@txtRemovePersistedData=Eliminar dades persistides
#XFLD
@txtMergeData=Fusionar
#XFLD
@txtOptimizeData=Optimitza
#XFLD
@txtVacuumData=Suprimir registres
#XFLD
@txtRunAPI=Executar API

#XFLD storage type text
hdlfStorage=Fitxer

@statusNew=No desplegat
@statusActive=Desplegat
@statusRevised=Actualitzacions locals
@statusPending=Desplegant...
@statusChangesToDeploy=Modificacions per desplegar
@statusDesignTimeError=Error de temps de disseny
@statusRunTimeError=Error de temps d'execució

#XTIT
txtNodes=Objectes a la cadena de tasques ({0})
#XBTN
@deleteNodes=Suprimir

#XMSG
@txtDropDataToDiagram=Arrossegueu i deixeu anar objectes al diagrama.
#XMSG
@noData=No hi ha cap objecte

#XFLD
@txtTaskPosition=Posició de la tasca

#input parameters and variables
#XFLD
lblInputParameters=Paràmetres d’entrada
#XFLD
ip_name=Nom
#XFLD
ip_value=Valor
#XTEXT
@noObjectsFound=No s’ha trobat cap objecte

#XMSG
@msgExecuteSuccess=S'ha iniciat l'execució de la cadena de tasques.
#XMSG
@msgExecuteFail=Error en executar la cadena de tasques.
#XMSG
@msgDeployAndRunSuccess=S'ha iniciat el desplegament i l'execució de la cadena de tasques.
#XMSG
@msgDeployAndRunFail=No s'ha pogut desplegar i executar la cadena de tasques.
#XMSG
@titleExecuteBusy=Espereu.
#XMSG
@msgExecuteBusy=Us estem preparant les dades per començar a executar la cadena de tasques.
#XMSG
@msgAPITestRunSuccess=L'execució de prova d'API s'ha iniciat. 
#XMSG
@msgAPIExecuteBusy=Us estem preparant les dades per iniciar l'execució de prova d'API.

#XTOL
txtOpenInEditor=Obrir a l'editor
#XTOL
txtPreviewData=Previsualitzar les dades

#datapreview
#XMSG
@msgDataPreviewNotSupp=La previsualització de dades no està disponible per a aquest objecte.

#XMSG Error: empty model
VAL_MODEL_EMPTY=Sembla que el vostre model és buit. Afegiu-hi alguns objectes.
#XMSG Error: deploy model
@msgDeployBeforeRun=Heu de desplegar la cadena de tasques abans d'executar-la.
#BTN: close dialog
btnClose=Tancar

#XMSG Object is not Deployed
VAL_NOT_DEPLOYED=L''objecte ''{0}'' ha d''estar desplegat per continuar amb la cadena de tasques.
#XMSG Object is having runtime error
VAL_RUNTIME_ERROR=L''objecte ''{0}'' torna un error en temps d''execució. Verifiqueu l''objecte.
#XMSG Object is having design time error
VAL_DESIGNTIME_ERROR=L''objecte ''{0}'' torna un error en temps de disseny. Verifiqueu l''objecte.
#XMSG Procedure Object has been deleted
VAL_DELETED_PROCEDURE=S''han suprimit els procediments següents: {0}
#XMSG New Parameter added to Procedure object
VAL_NEW_PARAMETER_ADDED=S''han afegit paràmetres nous al procediment "{1}": "{0}". Torneu a desplegar la cadena de tasques.
#XMSG Parameter removed from Procedure object
VAL_PARAMETER_DELETED=S''han eliminat paràmetres del procediment "{1}": "{0}". Torneu a desplegar la cadena de tasques.
#XMSG Parameter data type changed in Procedure object
VAL_PARAMETER_DATATYPE_MODIFIED=El tipus de dades del paràmetre "{0}" s''ha modificat de "{1}" a "{2}" en el procediment "{3}".
#XMSG Parameter length changed of specific datatype in Procedure object
VAL_PARAMETER_LENGTH_MODIFIED=La longitud del paràmetre "{0}" s''ha modificat de "{1}" a "{2}" en el procediment "{3}".
#XMSG Parameter precision changed of Decimal datatype in Procedure object
VAL_PARAMETER_PRECISION_MODIFIED=La precisió del paràmetre "{0}" s''ha modificat de "{1}" a "{2}" en el procediment "{3}".
#XMSG Parameter scale changed of Decimal datatype in Procedure object
VAL_PARAMETER_SCALE_MODIFIED=L''escala del paràmetre "{0}" s''ha modificat de "{1}" a "{2}" en el procediment "{3}".
#XMSG Parameter value not provided in Procedure object
VAL_SQL_SCRIPT_PROCEDURE_PARAMETER_VALUE_MISSING=No heu introduït cap valor per als paràmetres d''entrada requerits per al procediment "{0}": "{1}"
#XMSG Remote Table activity changes to Remove Replicated Data
VAL_REMOVE_REPLICATED_DATA=La propera execució d'una cadena de tasques modificarà el tipus d'accés a dades i es deixaran d'actualitzar dades en temps real.
#XMSG Remote Table activity changes
REPLICATION_TYPE_CHANGED=Objecte "{0}": El tipus de replicació canviarà.

#XFLD
@lblStartNode=Node d'inici
#XFLD
@lblEndNode=Node de fi
#XFLD
@linkTo=De {0} a {1}
#XTOL
@txtViewDetails=Veure detalls

#XTOL
txtOpenImpactLineage=Anàlisi d’impacte i llinatge
#XFLD
@emailNotifications=Notificacions per correu electrònic
#XFLD
@txtReset=Restablir
#XFLD
@emailMsgWarning=S'enviarà un model de correu electrònic predeterminat quan el missatge de correu electrònic estigui buit.
#XFLD
@notificationSettings=Opcions de notificació
#XFLD
@recipientEmailAddr=Adreça de correu electrònic del destinatari
#XFLD
@emailSubject=Assumpte del correu electrònic
@emailSubjectText=Cadena de tasques <TASKCHAIN_NAME> conclosa amb l'estat <STATUS>
#XFLD
@emailMessage=Missatge de correu electrònic
@emailMessageText=Apreciats usuaris,\n\n Us informem que l'execució de la cadena de tasques <TASKCHAIN_NAME> que ha començat a les <START_TIME> ha finalitzat amb l'estat <STATUS>. L'execució ha conclòs a les <END_TIME>.\n\nDetalls:\nEspai:<SPACE_NAME>\nError:<ERROR_MESSAGE>
@selectEmailRecipientDialogTitle=Seleccioneu l'adreça de correu electrònic del destinatari
@tenantMembers=Membres arrendataris
@others=Altres ({0})
@selectedEmailAddress=Destinataris seleccionats
@add=Afegir
@placeholder=Agafador d'espai
@description=Descripció
@copyText=Copiar text
@taskchainDetailsPlaceholder=Agafadors d'espai per a detalls de cadena de tasques
@placeholderCopied=Es copia l'agafador d'espai
@invalidEmailInfo=Introduïu una adreça de correu electrònic correcta
@maxMembersAlreadyAdded=Ja heu afegit el màxim de 20 membres
@enterEmailAddress=Introduïu una adreça de correu electrònic d'usuari
@inCorrectPlaceHolder={0} no és un agafador d''espai previst en el cos del correu.
@nsOFF=No enviar notificacions
@nsFAILED=Enviar notificació per correu electrònic només quan l'execució hagi conclòs amb un error
@nsCOMPLETED=Enviar notificació per correu electrònic quan l'execució hagi conclòs correctament
@nsANY=Enviar notificació per correu electrònic quan l'execució hagi conclòs
@phStatus=Estat de la cadena de tasques - CORRECTE|FALLIT
@phTaskChainTName=Nom tècnic de la cadena de tasques
@phTaskChainBName=Nom empresarial de la cadena de tasques
@phLogId=ID de log de l'execució
@phUser=Usuari que executa la cadena de tasques
@phLogUILink=Enllaç a la visualització de log de la cadena de tasques
@phStartTime=Hora d'inici de l'execució
@phEndTime=Hora de fi de l'execució
@phErrMsg=Primer missatge d'error del log de tasques. El log està buit si l'estat és CORRECTE.
@phSpaceName=Nom tècnic de l'espai
@emailFormatError=Format de correu electrònic no vàlid
@emailFormatErrorInListText=S'ha indicat un format de correu electrònic no vàlid en una llista de membres no arrendataris.
@emailSubjectTemplateText=Notificació per a cadena de tasques: $$taskChainName$$ - Espai: $$spaceId$$ - Estat: $$status$$
@emailMessageTemplateText=Hola,\n\n La vostra cadena de tasques amb etiqueta $$taskChainName$$ ha finalitzat amb l'estat $$status$$. \n Aquests són alguns detalls més de la cadena de tasques:\n - Nom tècnic de la cadena de tasques: $$taskChainName$$ \n - ID de log de l'execució de la cadena de tasques: $$logId$$ \n - Usuari que ha executat la cadena de tasques: $$user$$ \n - Enllaç a la visualització de log de la cadena de tasques: $$uiLink$$ \n - Hora d'inici de l'execució de la cadena de tasques: $$startTime$$ \n - Hora de fi de l'execució de la cadena de tasques: $$endTime$$ \n - Nom de l'espai: $$spaceId$$ \n
@deleteEmailRecepient=Suprimir destinatari
@emailInputDisabledText=Desplegueu la cadena de tasques per afegir destinataris de correu electrònic.
@tenantOwnerDomainMatchErrorText=El domini de l''adreça de correu electrònic no coincideix amb el domini del propietari de l''arrendatari: {0}
@totalEmailIdLimitInfoText=Podeu seleccionar fins a 20 destinataris de correu electrònic, inclosos usuaris membres de l'arrendatari i altres destinataris.
@emailDomainInfoText=Només s''admeten adreces de correu electrònic amb el domini {0}.
@duplicateEmailErrorText=Hi ha destinataris de correu electrònic duplicats a la llista.

#XFLD Zorder Title
@txtZorderTitle=Apache Spark Columnes en ordre Z

#XFLD Zorder NoColumn
@txtZorderNoColumn=No existeixen columnes en ordre Z

#XFLD Zorder Column PrimaryKey
@txtZorderColumnPrimaryKey=Clau primària

#XFLD
@lblOperators=Operadors
addNewSelector=Afegir una tasca nova
parallelSelector=Afegir una tasca paral·lela
replaceSelector=Substituir tasca existent
addparallelbranch=Afegir com a tasca paral·lela
addplaceholder=Afegir agafador d'espai
addALLOperation=Operador ALL
addOROperation=Operador ANY
addplaceholdertocanvas=Afegir agafadors d'espai al llenç
addplaceholderonselected=Afegir agafador d'espai després de la tasca seleccionada
#XFLD: Add Parallel Branch to Selected Task
addparallelbranchonselected=Afegir tasca paral·lela després de la tasca seleccionada
addOperator=Afegir operador
txtAdd=Afegir
txtPlaceHolderText=Arrossegar i deixar anar una tasca aquí
@lblLayout=Disposició

#XMSG
VAL_UNCONNECTED_TASK=La tasca ''{0}'' no està connectada a la cadena de tasques.
#XMSG
VAL_TASK_SHOULD_HAVE_ONE_INCOMING_LINK=La tasca ''{0}'' només pot tenir un enllaç d''entrada.
#XMSG
VAL_TASK_SHOULD_HAVE_INCOMING_LINK=La tasca ''{0}'' ha de tenir un enllaç d''entrada.
#XMSG
VAL_UNCONNECTED_OPERATOR=L''operador ''{0}'' no està connectat a la cadena de tasques.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ATLEAST_TWO_INCOMING_LINK=L''operador ''{0}'' ha de tenir com a mínim dos enllaços d''entrada.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ONE_OUT_LINK=L''operador ''{0}'' ha de tenir com a mínim un enllaç de sortida.
#XMSG
VAL_CIRCULAR_REFERENCE_EXITS=Existeix un loop circular a la cadena de tasques ''{0}''.
#XMSG
VAL_UNCONNECTED_BRANCH=L''objecte/La branca ''{0}'' no està connectat a la cadena de tasques.
#XMSG
VAL_TASK_TRIGGERED_PARALLEL_MORE_THAN_ONE_TIME=La tasca ''{0}'' s''ha connectat en paral·lel més d''un cop. Elimineu els duplicats per continuar.


txtBegin=Començar
txtNodesInLink=Objectes implicats
#XTOL Tooltip for a context button on diagram
openInNewTab=Obrir en una pestanya nova
#XTOL Tooltip for a context button on diagram
txtConnectToTaskOperator=Arrossegar per connectar-se
@emailUpdateError=Error en actualitzar la llista de notificacions per correu electrònic

#XMSG
noTeamPrivilegeTxt=No teniu permís per veure una llista de membres d'arrendatari. Utilitzeu la pestanya Altres per afegir destinataris de correu electrònic manualment.

#XFLD Package
@txtPackage=Paquet

#XMSG warning for repository package assignment changed. {1} - package name
VAL_PACKAGE_CHANGED=Heu assignat aquest objecte al paquet ''{1}''. Feu clic a Desar per confirmar i validar el canvi. Tingueu en compte que l''assignació a un paquet no es pot desfer en aquest editor un cop s''ha desat.
#XMSG warning for repository package dependency issue.
MISSING_DEPENDENCY=Les dependències de l''objecte ''{0}'' no es poden resoldre en el context del paquet ''{1}''.

#Warning messages for the folders in the "Others" tab of task chain editor
#XMSG generic warning if getting the objects in a particular folder fails with unhandled errors
getNonRepoArtefactTypeDescendantsError=Hi havia un problema en recuperar els objectes a la carpeta seleccionada.
#BW Process Chains folder related warning messages
#XMSG warning if user does not have the necessary permission related to the SAP BW Bridge space
missingPrivilegeForBridgeSpaceError=No teniu el permís necessari per a veure o incloure les cadenes del procés BW en una cadena de tasques.
#XMSG warning if there was a problem retrieving the BW process chains due to bridge space membership, bridge connection issues or other errors
generalBridgeSpaceError=Hi havia in problema en recuperar les cadenes del procés BW.
#XMSG warning if the BW API for retrieving the BW process chains from the SAP BW Bridge tenant fails
bwApiClientError=Hi havia un problema en recuperar les cadenes del procés BW de l'arrendatari de SAP BW Bridge.
#XMSG warning if BW process chains list is empty
bwApiEmptyListError=No s'ha trobat cap cadena del procés BW a l'arrendatari de SAP BW Bridge.
#XMSG: Task log message for invalid OIDC configuration in the SAP BW Bridge tenant
bwBridgeOIDCConfigStatusInvalid=L''autenticació OpenID no s''ha configurat per a aquest arrendatari. Utilitzeu l''ID de client "{0}" i l''URL de marca "{1}" per configurar l''autenticació OpenID a l''arrendatari del pont de SAP BW tal com es descriu a la nota SAP 3536298.
#XMSG BW Process Chain Object has been deleted
VAL_DELETED_BW=Possiblement s''han eliminat les següents cadenes del procés BW de l''arrendatari de SAP Bridge: {0}
#BW SQL Script Procedures folder related warning messages
#XFLD waring if the Procedure list is empty
procedureAPIEmptyList=No s'han creat procediments o el privilegi EXECUTAR no s'ha concedit a l'esquema Open SQL.
#Change digram orientations
changeOrientations=Modifica les orientacions


# placeholder for the API Path
apiPath=Per exemple: /job/v1
# placeholder for the status API Path
statusAPIPath=Per exemple: /job/v1/{id}/status
# valueStateText for the API Path
apiPathRequired=Via d'accés d'API requerida
#placeholder for the CSRF Token URL
csrfTokenURL=Només s'admet HTTPS
# Response Type 1
statusCode=Obtenir resultat de codi d'estat HTTP
# Response Type 2
locationHeader=Obtenir resultat de codi d'estat HTTP i capçalera d'ubicació
# Response Type 3
responseBody=Obtenir resultat de codi d'estat HTTP i cos de resposta
# placeholder for ID
idPlaceholder=Introduir via d'accés JSON
# placeholder for indicator value
indicatorValue=Introduïu valor
# Placeholder for key
keyPlaceholder=Introduïu una clau
# Error message for missing key
KeyRequired=La clau és obligatòria
# Error message for invalid key format
invalidKeyFormat=La clau de capçalera que heu introduït no està permesa. Les capçaleres vàlides són:<ul><li>"preferir"</li><li>Capçaleres que comencen amb "x-", tret de "x-forwarded-host"</li><li>Capçaleres de contenen els caràcters alfanumèrics, "-" o "_"</li></ul>
# Error message for missing value
valueRequired=El valor és obligatori
# Error message for invalid characters in value
invalidValueCharacters=La capçalera conté caràcters no vàlids. Els caràcters especials permesos són:\t ";", ":", "-", "_", ",", "?", "/" i "*"
# Validation message for invoke api path
apiPathValidation=Introduïu una via d'accés d'API vàlida, per exemple: /job/v1
# Validation message for JSON path
jsonPathValidation=Introduïu una via d'accés JSON vàlida
# Validation message for success/error indicator
indicatorValueValidation=El valor de l'indicador ha de començar amb un caràcter alfanumèric i pot incloure els següents caràcters especials:\t "-" i "_"
# Error message for JSON path
jsonPathRequired=Via d'accés JSON requerida
# Error message for invalid API Technical Name
invalidTechnicalName=El nom tècnic conté caràcters no vàlids
# Error message for empty Technical Name
emptyTechnicalName=El nom tècnic és obligatori
# Tooltip for codeEditor dialog
codeEditorTooltip=Obrir finestra d'edició JSON
# Status Api path validation message
validationStatusAPIPath=Introduïu una via d'accés d'API vàlida, per exemple: /job/v1/{id}/status
# CSRF token URL validation message
validationCSRFTokenURL=L'URL de marca CSRF ha de començar amb "https://" i ser un URL vàlid
# Select a connection
selectConnection=Seleccionar una connexió
# Validation message for connection item error
connectionNotReplicated=La connexió actualment no és vàlida per executar tasques d'API. Obriu l'aplicació "Connexions" i torneu a introduir-hi les credencials per corregir la connexió HTTP.
# Validation message for API task properties wrong value or missing
VAL_REST_API_PROPERTIES_MISSING=La tasca d''API "{0}" té valors incorrectes o en falten per a les propietats següents: {1}
# Validation message for API connection api is throwing some error or selected connection has some warning
VAL_REST_API_CONNECTION=La tasca d''API "{0}" té un problema amb la connexió HTTP. Obriu l''aplicació "Connexions" i verifiqueu la connexió.
# Validation message for API task properties when delected connection
VAL_REST_API_CONNECTION_DELETED=La tasca d''API "{0}" té una connexió "{1}" suprimida
# Validation message for API task properties when connection cannot be consumed in the API task
VAL_REST_API_CONNECTION_CONSUMPTION_WARNING=La tasca d''API "{0}" té una connexió "{1}" que no es pot utilitzar per executar tasques d''API. Obriu l''aplicació "Connexions" i torneu a introduir les credencials per establir la connectivitat per a tasques API
# Information message if synchrnous mode is selected in the API Invocation then we don't need status information
modeInfoText=En mode sincrònic, el panell d'estat no es mostra per a crides d'API.
# validation dialog button for Run API Test
saveAnyway=Desar de totes maneres
# validation message for technical name
technicalNameValidation=El nom tècnic ha de ser unívoc dins de la cadena de tasques. Seleccioneu un altre nom tècnic.
# Connection error message
getHttpConnectionsError=Error en obtenir les connexions HTTP
# Msg for the API test run when the Task chain is not saved
taskChainNotSaved=Cal desar la cadena de tasques abans d'iniciar l'execució de prova d'API.
# Msg failed to run API test run
@failedToRunAPI=Error en executar la prova d'API
# Msg for the API test run when its already in running state
apiTaskRunning=Ja hi ha en curs una execució de prova de l'API. Voleu iniciar una nova execució de prova?

topToBtm=Descendent
leftToRight=D'esquerra a dreta

#XTIT Apache Spark Application Settings
txtHeaderApacheSettings=Opcions de l'aplicació Apache Spark
#XFLD Use Default
txtUseSpaceDefault=Utilitzar valor estàndard
#XFLD Application
txtApplication=Aplicació
#XFLD Define new settings for this Task
txtNewSettings=Definir configuració nova per a aquesta tasca

#XFLD Use Default
txtUseDefault=Utilitzar valor estàndard




