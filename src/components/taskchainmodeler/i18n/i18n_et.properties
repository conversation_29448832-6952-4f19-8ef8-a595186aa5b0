#XTOL Undo
@undo=Võta tagasi
#XTOL Redo
@redo=Tee uuesti
#XTOL Delete Selected Symbol
@deleteNode=Kustuta valitud sümbol
#XTOL Zoom to Fit
@zoomToFit=<PERSON>umi sobitamiseks
#XTOL Auto Layout
@autoLayout=Automaatpaigutus
#XMSG
@welcomeText=Pukseerige objektid vasakpaanilt sellele lõuendile.
#XMSG
@txtNoData=Näib, et te pole veel ühtegi objekti lisanud.
#XMSG
VAL_ENTER_VALID_STRING_GEN=Sisestage sobiv string, mille pikkus ei ületaks {0}.
#XMSG
@noParametersMsg=Sellel protseduuril pole sisendparameetreid.
#XMSG
@ip_enterValueMsg=Päringus „{0}“ (Käita SQL-skripti protseduur) on sisendparameetreid. Saate iga sisendparameetri jaoks määrata väärtuse.
#XTOL
@validateModel=Valideerimisteated
#XTOL
@hierarchy=Hierarhia
#XTOL
@columnCount=Veergude arv
#XFLD
@yes=Jah
#XFLD
@no=Ei
#XTIT Save Dialog param
@modelNameTaskChain=Tegumiahel
#properties panel
@lblPropertyTitle=Atribuudid
#XFLD
@lblGeneral=Üldist
#XFLD : Setting
@lblSetting=Sätted
#XTXT: Delta Enabled Local Table Deletion
txtDeltaTableDeletion=Kustutage kõik täielikult töödeldud kirjed muudatuse tüübiga 'Kustutatud' mis on vanemad kui
#XTXT: Delta Enabled Local Table Deletion Input field description
desDeltaTableDeletion=Päevad
#XFLD: Data Activation label
@lblDataActivation=Andmete aktiveerimine
#XFLD: Latency label
@latency=Latentsus
#XTEXT: Text for Latency dropdown
txtLatencyDefault=Vaikeväärtus
#XTEXT: Text 1 hour
txtOneHour=1 tund
#XTEXT: Text 2 hours
txtTwoHours=2 tundi
#XTEXT: Text 3 hours
txtThreeHours=3 tundi
#XTEXT: Text 4 hours
txtFourHours=4 tundi
#XTEXT: Text 6 hours
txtSixHours=6 tundi
#XTEXT: Text 12 hours
txtTwelveHours=12 tundi
#XTEXT: Text 1 day
txtOneDay=1 päev
#XFLD: Latency label
@autoRestartHead=Automaatne taaskäivitamine
#XFLD
@lblConnectionName=Ühendus
#XFLD
@lblQualifiedName=Liitnimi
#XFLD
@lblSpaceName=Ruumi nimi
#XFLD
@lblLocalSchemaName=Kohalik skeem
#XFLD
@lblType=Objekti tüüp
#XFLD
@lblActivity=Tegevus
#XFLD
@lblTableName=Tabeli nimi
#XFLD
@lblBusinessName=Ärinimi
#XFLD
@lblTechnicalName=Tehniline nimi
#XFLD
@lblSpace=Ruum
#XFLD
@lblLabel=Silt
#XFLD
@lblDataType=Andmetüüp
#XFLD
@lblDescription=Kirjeldus
#XFLD
@lblStorageType=Salvestusruum
#XFLD
@lblHTTPConnection=Üldine HTTP-ühendus
#XFLD
@lblAPISettings=Üldised API sätted
#XFLD
@header=Päised
#XFLD
@lblInvoke=API aktiveerimine
#XFLD
@lblMethod=Meetod
#XFLD
@lblUrl=Baas-URL
#XFLD
@lblAPIPath=API tee
#XFLD
@lblMode=Režiim
#XFLD
@lblCSRFToken=Nõua CSRF-tõendit
#XFLD
@lblTokenURL=CSRF-tõendi URL
#XFLD
@csrfTokenInfoText=Kui seda pole sisestatud, kasutatakse baas-URL-i ja API teed
#XFLD
@lblCSRF=CSRF-tõend
#XFLD
@lblRequestBody=Päringu sisu
#XFLD
@lblFormat=Vorming
#XFLD
@lblResponse=Vastus
#XFLD
@lblId=ID oleku toomiseks
#XFLD
@Id=ID
#XFLD
@lblSuccessIndicator=Õnnestumise tunnus
#XFLD
@lblErrorIndicator=Tõrke tunnus
#XFLD
@lblErrorReason=Tõrke põhjus
#XFLD
@lblStatus=Olek
#XFLD
@lblApiTestRun=API testkäitus
#XFLD
@lblRunStatus=Käituse olek
#XFLD
@lblLastRan=Viimati käitatud
#XFLD
@lblTestRun=Testkäitus
#XFLD
@lblDefaultHeader=Vaikepäiseväljad (võtme ja väärtuse paarid)
#XFLD
@lblAdditionalHeader=Täiendav päiseväli
#XFLD
@lblKey=Võti
#XFLD
@lblEditJSON=Redigeeri JSON-i
#XFLD
@lblTasks=Toimingud
#XFLD
@lblRESTfullTask=API
#XFLD: add field button text
btnAddField=Lisa päiseväli
#XFLD: view Details link
@viewDetails=Kuva üksikasjad
#XTOL
tooltipTxt=Rohkem
#XTOL
delete=Kustuta
#XBTN: ok button text
btnOk=OK
#XBTN: cancel button text
btnCancel=Tühista
#XBTN: save button text
btnSave=Salvesta
#XMSG Error: Object has duplicated technical name.
VAL_OBJECT_DUPLICATE_NAME=Objekt ''{0}'' on juba hoidlas olemas. Sisestage erinev nimi.
#XMSG: loading message while opening task chain
loadTaskChain=Tegumiahela laadimine...
#model properties
#XFLD
@status_panel=Käituse olek
#XFLD
@deploy_status_panel=Juurutamisolek
#XFLD
@status_lbl=Olek
#XFLD
@lblLastExecuted=Viimati käivitatud
#XFLD
@lblNotExecuted=Pole veel käivitatud
#XFLD
@lblNotDeployed=Pole juurutatud
#XFLD
errorDetailsTxt=Käituse olekut ei saanud tuua
#XBTN: Schedule dropdown menu
SCHEDULE=Graafik
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Redigeeri graafikut
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Kustuta graafik
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Loo graafik
#XLNK
viewDetails=Kuva üksikasjad
#XMSG: error message for reading execution status from backend
backendErrorMsg=Näib, et andmeid praegu serverist ei laadita. Proovige andmed uuesti tuua.
#XFLD: Status text for Completed
@statusCompleted=Lõpetatud
#XFLD: Status text for Running
@statusRunning=Käitamine
#XFLD: Status text for Failed
@statusFailed=Nurjunud
#XFLD: Status text for Stopped
@statusStopped=Peatatud
#XFLD: Status text for Stopping
@statusStopping=Peatamine
#XFLD
@LoaderTitle=Laadimine
#XFLD
#XFLD: Deploy status, dataflow has  been deployed / deployment date exists
@deployStatusActive=Juurutatud
@deployStatusRevised=Kohalikud uuendused
@deployStatusFailed=Nurjunud
@deployStatusPending=Juurutamine...
@LoaderText=Üksikasjade toomine serverist
#XMSG
@msgDetailFetchError=Serverist üksikasjade toomisel ilmnes tõrge
#XFLD
@executeError=Tõrge
#XFLD
@executeWarning=Hoiatus
#XMSG
@executeConfirmDialog=Teave
#XMSG
@executeunsavederror=Salvestage oma tegumiahel enne selle käivitamist.
#XMSG
@executemodifiederror=Tegumiahelas on salvestamata muudatusi. Salvestage see.
#XMSG
@executerunningerror=Tegumiahel töötab praegu. Oodake enne uue käituse alustamist, kuni praegune käitus on lõpule viidud.
#XMSG
@btnExecuteAnyway=Käivita ikka
#XMSG
@msgExecuteWithValidations=Tegumiahelal on valideerimistõrkeid. Tegumiahela käivitamine võib nurjuda.
#XMSG
@msgRunDeployedVersion=Leidub juurutatavaid muudatusi. Käivitatakse tegumiahela viimane juurutatud versioon. Kas soovite jätkata?
#XMSG
#XMSG
@navToMonitoring=Ava tegumiahela seiretööriistas
#XMSG
txtOR=VÕI
#XFLD
@preview=Eelvaade
#XMSG
txtand=ja
#NOTR
@dateFormat=yyyy-MM-dd
#NOTR
@dateTimeFormatFilter=yyyy-MM-dd HH:mm:ss.
#NOTR
@timeFormat=HH:mm:ss
@lblColum=Veerg
#XFLD
@lblCondition=Tingimus
#XFLD
@lblValue=Väärtus
#XMSG
@msgJsonInvalid=Tegumiahelat ei saanud salvestada, kuna JSON-is on tõrkeid. Kontrollige ja lahendage.
#XTIT
@msgSaveFailTitle=Sobimatu JSON.
#XMSG
NOT_CHAINABLE=Objekti ''{0}'' ei saa tegumiahelasse lisada.
#XMSG
NOT_CHAINABLE_REMOTETABLE=Objekt „{0}“: alliktabelite replikeerimise tüüpi muudetakse.
#XMSG
searchTaskChain=Otsinguobjektid

#XFLD
@txtTaskChain=Tegumiahel
#XFLD
@txtRemoteTable=Kaugtabel
#XFLD
@txtRemoveData=Eemalda tiražeeritud andmed
#XFLD
@txtRemovePersist=Eemalda püsisalvestatud andmed
#XFLD
@txtView=Vaade
#XFLD
@txtDataFlow=Andmevoog
#XFLD
@txtIL=Intelligentne otsing
#XFLD
@txtTransformationFlow=Teisendusvoog
#XFLD
@txtReplicationFlow=Alliktabelite replikeerimise voog
#XFLD
@txtDeltaLocalTable=Kohalik tabel
#XFLD
@txtBWProcessChain=BW protsessiahel
#XFLD
@txtSQLScriptProcedure=SQL-skripti protseduur
#XFLD
@txtAPI=API
#XFLD
@txtMerge=Ühenda
#XFLD
@txtOptimize=Optimeeri
#XFLD
@txtVacuum=Kustuta kirjed

#XFLD
@txtRun=Käivita
#XFLD
@txtPersist=Salvesta püsivalt
#XFLD
@txtReplicate=Tiražeeri
#XFLD
@txtDelete=Kustuta andmekirjed, mille muudatuse tüüp on „Kustutatud“
#XFLD
@txtRunTC=Käivita tegumiahel
#XFLD
@txtRunBW=Käivita BW protsessiahel
#XFLD
@txtRunSQLScriptProcedure=Käivita SQL-skripti protseduur

#XFLD
@txtRunDataFlow=Käivita andmevoog
#XFLD
@txtPersistView=Püsisalvesta vaade
#XFLD
@txtReplicateTable=Tiražeeri tabel
#XFLD
@txtRunIL=Käivitage intelligentne otsing
#XFLD
@txtRunTF=Käivita teisendusvoog
#XFLD
@txtRunRF=Käivita alliktabelite replikeerimise voog
#XFLD
@txtRemoveReplicatedData=Eemalda tiražeeritud andmed
#XFLD
@txtRemovePersistedData=Eemalda püsisalvestatud andmed
#XFLD
@txtMergeData=Ühenda
#XFLD
@txtOptimizeData=Optimeeri
#XFLD
@txtVacuumData=Kustuta kirjed
#XFLD
@txtRunAPI=Käivita API

#XFLD storage type text
hdlfStorage=Fail

@statusNew=Pole juurutatud
@statusActive=Juurutatud
@statusRevised=Kohalikud uuendused
@statusPending=Juurutamine...
@statusChangesToDeploy=Juurutatavad muudatused
@statusDesignTimeError=Kujundusaja tõrge
@statusRunTimeError=Käitusaja tõrge

#XTIT
txtNodes=Objektid tegumiahelas ({0})
#XBTN
@deleteNodes=Kustuta

#XMSG
@txtDropDataToDiagram=Pukseerige objektid diagrammi.
#XMSG
@noData=Objekte pole

#XFLD
@txtTaskPosition=Tegumi positsioon

#input parameters and variables
#XFLD
lblInputParameters=Sisendparameetrid
#XFLD
ip_name=Nimi
#XFLD
ip_value=Väärtus
#XTEXT
@noObjectsFound=Objekte ei leitud

#XMSG
@msgExecuteSuccess=Tegumiahela käitus on käivitatud.
#XMSG
@msgExecuteFail=Tegumiahela käivitamine nurjus.
#XMSG
@msgDeployAndRunSuccess=Tegumiahela juurutamine ja käitus on alanud.
#XMSG
@msgDeployAndRunFail=Tegumiahela juurutamine ja käivitamine nurjus.
#XMSG
@titleExecuteBusy=Palun oodake.
#XMSG
@msgExecuteBusy=Valmistame teie andmed ette tegumiahela käivitamiseks.
#XMSG
@msgAPITestRunSuccess=API testkäitus on alanud.
#XMSG
@msgAPIExecuteBusy=Valmistame teie andmed ette API testkäituse käivitamiseks.

#XTOL
txtOpenInEditor=Ava redaktoris
#XTOL
txtPreviewData=Kuva andmete eelvaade

#datapreview
#XMSG
@msgDataPreviewNotSupp=Selle objekti puhul pole andmete eelvaade saadaval.

#XMSG Error: empty model
VAL_MODEL_EMPTY=Teie mudel näib olevat tühi. Lisage mõni objekt.
#XMSG Error: deploy model
@msgDeployBeforeRun=Peate tegumiahela enne selle käivitamist juurutama.
#BTN: close dialog
btnClose=Sule

#XMSG Object is not Deployed
VAL_NOT_DEPLOYED=Objekt ''{0}'' peab olema tegumiahelaga jätkamiseks juurutatud.
#XMSG Object is having runtime error
VAL_RUNTIME_ERROR=Objekt „{0}“ tagastab käitusaja tõrke. Kontrollige objekti.
#XMSG Object is having design time error
VAL_DESIGNTIME_ERROR=Objekt „{0}“ tagastab kujundusaja tõrke. Kontrollige objekti.
#XMSG Procedure Object has been deleted
VAL_DELETED_PROCEDURE=Järgmised protseduurid on kustutatud: {0}
#XMSG New Parameter added to Procedure object
VAL_NEW_PARAMETER_ADDED=Protseduurile „{1}“ on lisatud uued parameetrid: {0}. Juurutage tegumiahel uuesti.
#XMSG Parameter removed from Procedure object
VAL_PARAMETER_DELETED=Parameetrid on protseduurilt „{1}“ eemaldatud: {0}. Juurutage tegumiahel uuesti.
#XMSG Parameter data type changed in Procedure object
VAL_PARAMETER_DATATYPE_MODIFIED=Parameetri „{0}“ andmetüüp „{1}“ on protseduuris „{3}“ muutunud ja on nüüd „{2}“.
#XMSG Parameter length changed of specific datatype in Procedure object
VAL_PARAMETER_LENGTH_MODIFIED=Parameetri „{0}“ pikkus „{1}“ on protseduuris „{3}“ muutunud ja on nüüd „{2}“.
#XMSG Parameter precision changed of Decimal datatype in Procedure object
VAL_PARAMETER_PRECISION_MODIFIED=Parameetri „{0}“ täpsus „{1}“ on protseduuris „{3}“ muutunud ja on nüüd „{2}“.
#XMSG Parameter scale changed of Decimal datatype in Procedure object
VAL_PARAMETER_SCALE_MODIFIED=Parameetri „{0}“ mõõtkava „{1}“ on protseduuris „{3}“ muutunud ja on nüüd „{2}“.
#XMSG Parameter value not provided in Procedure object
VAL_SQL_SCRIPT_PROCEDURE_PARAMETER_VALUE_MISSING=Te pole sisendparameetrite jaoks sisestanud väärtuseid, mis on protseduuri „{0}“ jaoks nõutavad: {1}
#XMSG Remote Table activity changes to Remove Replicated Data
VAL_REMOVE_REPLICATED_DATA=Tegumiahela järgmine käitus muudab andmepöördustüüpi ja andmeid ei laadita enam üles reaalajas.
#XMSG Remote Table activity changes
REPLICATION_TYPE_CHANGED=Objekt „{0}“: tiražeerimise tüüpi muudetakse.

#XFLD
@lblStartNode=Algussõlm
#XFLD
@lblEndNode=Lõppsõlm
#XFLD
@linkTo={0} - {1}
#XTOL
@txtViewDetails=Kuva üksikasjad

#XTOL
txtOpenImpactLineage=Mõju- ja järglusliini analüüs
#XFLD
@emailNotifications=Meiliteatised
#XFLD
@txtReset=Lähtesta
#XFLD
@emailMsgWarning=Vaikemalli meilisõnum saadetakse, kui meilisõnum on tühi
#XFLD
@notificationSettings=Teavitussätted
#XFLD
@recipientEmailAddr=Adressaadi meiliaadress
#XFLD
@emailSubject=Meilisõnumi teema
@emailSubjectText=Tegumiahel <TASKCHAIN_NAME> on lõpule viidud olekuga <STATUS>
#XFLD
@emailMessage=Meilisõnum
@emailMessageText=Lugupeetud kasutaja\n\n Teavitame teid, et tegumiahel <TASKCHAIN_NAME>, mis käivitati <START_TIME>, on lõpule viidud olekuga <STATUS>. Täitmine lõppes <END_TIME>.\n\nÜksikasjad\nRuum:<SPACE_NAME>\nTõrge:<ERROR_MESSAGE>
@selectEmailRecipientDialogTitle=Adressaadi meiliaadressi valimine
@tenantMembers=Rentniku liikmed
@others=Muud({0})
@selectedEmailAddress=Valitud adressaadid
@add=Lisa
@placeholder=Kohatäide
@description=Kirjeldus
@copyText=Kopeeri tekst
@taskchainDetailsPlaceholder=Tegumiahela üksikasjade kohatäited
@placeholderCopied=Kohatäide on kopeeritud
@invalidEmailInfo=Sisestage õige meiliaadress
@maxMembersAlreadyAdded=Olete juba lisanud maksimumarvu liikmeid (20)
@enterEmailAddress=Sisestage kasutaja meiliaadress
@inCorrectPlaceHolder={0} pole meilisõnumi sisus eelduspärane kohatäide.
@nsOFF=Ära saada teateid
@nsFAILED=Saada meiliteatis ainult juhul, kui käitus viiakse lõpule tõrkega
@nsCOMPLETED=Saada meiliteatis ainult juhul, kui käitus viiakse lõpule edukalt
@nsANY=Saada meiliteatis, kui käitus viiakse lõpule
@phStatus=Tegumiahela olek – EDUKAS|NURJUNUD
@phTaskChainTName=Tegumiahela tehniline nimi
@phTaskChainBName=Tegumiahela ärinimi
@phLogId=Käituse logi ID
@phUser=Tegumiahelat käitav kasutaja
@phLogUILink=Tegumiahela logikuva link
@phStartTime=Käituse algusaeg
@phEndTime=Käituse lõppaeg
@phErrMsg=Tegumilogi esimene tõrketeade. Kui EDUKAS, siis on logi tühi
@phSpaceName=Ruumi tehniline nimi
@emailFormatError=Sobimatu meilivorming
@emailFormatErrorInListText=Mitterentnikest liikmete loendisse sisestatud sobimatu meilivorming.
@emailSubjectTemplateText=Tegumiahela märguanne: $$taskChainName$$ – tühik: $$spaceId$$ – olek: $$status$$
@emailMessageTemplateText=Tere!\n\n Teie tegumiahel sildiga $$taskChainName$$ on lõppenud olekuga $$status$$. \n Siin on mõned muud üksikasjad tegumiahela kohta:\n - Tegumiahela tehniline nimi: $$taskChainName$$ \n - Tegumiahela käitamise logi ID: $$logId$$ \n - Tegumiahelat juhtinud kasutaja : $$user$$ \n - Link tegumiahela logikuvale: $$uiLink$$ \n - Tegumiahela käitamise algusaeg: $$startTime$$ \n - Tegumiahela käitamise lõppaeg : $$endTime$$ \n - Ruumi nimi: $$spaceId$$ \n
@deleteEmailRecepient=Kustutage adressaat
@emailInputDisabledText=Juurutage meilisaajate lisamiseks tegumiahel.
@tenantOwnerDomainMatchErrorText=E-posti aadressi domeen ei ühti rentniku omaniku domeeniga: {0}
@totalEmailIdLimitInfoText=Saate valida kuni 20 meilisaajat, sealhulgas rentnikliikmed ja muud adressaadid.
@emailDomainInfoText=Aktsepteeritakse ainult e-posti aadresse domeeniga: {0}.
@duplicateEmailErrorText=Loendis on korduvaid meili saajaid.

#XFLD Zorder Title
@txtZorderTitle=Apache Sparki Z-järjestusega veerud

#XFLD Zorder NoColumn
@txtZorderNoColumn=Z-järjestusega veerge ei leitud

#XFLD Zorder Column PrimaryKey
@txtZorderColumnPrimaryKey=Primaarvõti

#XFLD
@lblOperators=Tehtemärgid
addNewSelector=Lisa uue tegumina
parallelSelector=Lisa paralleeltegumina
replaceSelector=Asenda olemasolev tegum
addparallelbranch=Lisa paralleelharuna
addplaceholder=Lisa kohatäide
addALLOperation=Tehtemärk KÕIK
addOROperation=Tehtemärk IGA
addplaceholdertocanvas=Lisa kohatäide lõuendile
addplaceholderonselected=Lisa kohatäide pärast valitud tegumit
#XFLD: Add Parallel Branch to Selected Task
addparallelbranchonselected=Lisa paralleelharu pärast valitud tegumit
addOperator=Lisa tehtemärk
txtAdd=Lisa
txtPlaceHolderText=Lohistage tegum siia
@lblLayout=Paigutus

#XMSG
VAL_UNCONNECTED_TASK=Tegum „{0}“ pole tegumiahelaga ühendatud.
#XMSG
VAL_TASK_SHOULD_HAVE_ONE_INCOMING_LINK=Tegumil „{0}“ peab olema ainult üks sissetulev link.
#XMSG
VAL_TASK_SHOULD_HAVE_INCOMING_LINK=Tegumil „{0}“ peab olema üks sissetulev link.
#XMSG
VAL_UNCONNECTED_OPERATOR=Tehtemärk „{0}“ pole tegumiahelaga ühendatud.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ATLEAST_TWO_INCOMING_LINK=Tehtemärgil „{0}“ peab olema vähemalt kaks sissetulevat linki.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ONE_OUT_LINK=Tehtemärgil „{0}“ peab olema vähemalt üks väljaminev link.
#XMSG
VAL_CIRCULAR_REFERENCE_EXITS=Tegumiahelas „{0}“ on ringtsükkel.
#XMSG
VAL_UNCONNECTED_BRANCH=Objekt/haru „{0}“ pole tegumiahelaga ühendatud.
#XMSG
VAL_TASK_TRIGGERED_PARALLEL_MORE_THAN_ONE_TIME=Tegum „{0}“ on paralleelselt ühendatud üle ühe korra. Jätkamiseks eemaldage duplikaadid.


txtBegin=Alusta
txtNodesInLink=Kaasatud objektid
#XTOL Tooltip for a context button on diagram
openInNewTab=Ava uuel vahekaardil
#XTOL Tooltip for a context button on diagram
txtConnectToTaskOperator=Ühendamiseks lohistage
@emailUpdateError=Tõrge meiliteatiste loendi uuendamisel

#XMSG
noTeamPrivilegeTxt=Teil ei ole luba näha üürnike nimekirja. Kasutage meili saajate käsitsi lisamiseks vahekaarti Muud.

#XFLD Package
@txtPackage=Pakett

#XMSG warning for repository package assignment changed. {1} - package name
VAL_PACKAGE_CHANGED=Olete selle objekti määranud paketile „{1}“. Selle muudatuse kinnitamiseks ja valideerimiseks klõpsake nuppu „Salvesta“. Võtke arvesse, et pärast salvestamist ei saa paketile määramist selles redaktoris tagasi võtta.
#XMSG warning for repository package dependency issue.
MISSING_DEPENDENCY=Objekti „{0}“ sõltuvusseoseid ei saa paketi „{1}“ kontekstis lahendada.

#Warning messages for the folders in the "Others" tab of task chain editor
#XMSG generic warning if getting the objects in a particular folder fails with unhandled errors
getNonRepoArtefactTypeDescendantsError=Teie valitud kaustas asuvate objektide toomisel ilmnes probleem.
#BW Process Chains folder related warning messages
#XMSG warning if user does not have the necessary permission related to the SAP BW Bridge space
missingPrivilegeForBridgeSpaceError=Teil pole tegumiahelas BW protsessiahelate vaatamiseks või kaasamiseks õigust.
#XMSG warning if there was a problem retrieving the BW process chains due to bridge space membership, bridge connection issues or other errors
generalBridgeSpaceError=BW protsessiahelate toomisel ilmnes probleem.
#XMSG warning if the BW API for retrieving the BW process chains from the SAP BW Bridge tenant fails
bwApiClientError=BW protsessiahelate toomisel SAP BW Bridge’i rentnikust ilmnes probleem.
#XMSG warning if BW process chains list is empty
bwApiEmptyListError=SAP BW Bridge’i rentnikust ei leitud BW protsessiahelaid.
#XMSG: Task log message for invalid OIDC configuration in the SAP BW Bridge tenant
bwBridgeOIDCConfigStatusInvalid=OpenID autentimist pole selle rentniku jaoks konfigureeritud. Kasutage clientID-d „{0}“ ja tokenURL-i „{1}“, et konfigureerida SAP BW silla rentnikus OpenID autentimine nii, nagu seda on kirjeldatud SAP-i teadaandes 3536298.
#XMSG BW Process Chain Object has been deleted
VAL_DELETED_BW=On võimalik, et järgmised BW protsessiahelad on SAP Bridge’i rentnikust kustutatud: {0}
#BW SQL Script Procedures folder related warning messages
#XFLD waring if the Procedure list is empty
procedureAPIEmptyList=Protseduure pole loodud või õigust EXECUTE pole Open SQL-i skeemile antud.
#Change digram orientations
changeOrientations=Muuda suunda


# placeholder for the API Path
apiPath=Näide: /töö/v1
# placeholder for the status API Path
statusAPIPath=Näide: /töö/v1/{id}/olek
# valueStateText for the API Path
apiPathRequired=API tee on nõutav
#placeholder for the CSRF Token URL
csrfTokenURL=Toetatud on ainult HTTPS
# Response Type 1
statusCode=Too tulemus HTTP olekukoodist
# Response Type 2
locationHeader=Too tulemus HTTP olekukoodist ja asukohapäisest
# Response Type 3
responseBody=Too tulemus HTTP olekukoodist ja vastuse sisuosast
# placeholder for ID
idPlaceholder=Sisestage JSON-i tee
# placeholder for indicator value
indicatorValue=Sisestage väärtus
# Placeholder for key
keyPlaceholder=Sisestage võti
# Error message for missing key
KeyRequired=Võti on nõutav
# Error message for invalid key format
invalidKeyFormat=Teie sisestatud päisevõti pole lubatud. Sobivad päised on:<ul><li>„prefer“</li><li>Päised, mille alguses on „x-“, v.a „x-forwarded-host“</li><li>Päised, mis sisaldavad tähe- ja numbrimärke, sidekriipsu „-“ või allkriipsu „_“</li></ul>
# Error message for missing value
valueRequired=Väärtus on nõutav
# Error message for invalid characters in value
invalidValueCharacters=Päis sisaldab sobimatuid märke. Lubatud erimärgid on:\t ; : - _ , ? / ja *
# Validation message for invoke api path
apiPathValidation=Sisestage sobiv API tee, näiteks: /töö/v1
# Validation message for JSON path
jsonPathValidation=Sisestage sobiv JSON-i tee
# Validation message for success/error indicator
indicatorValueValidation=Tunnuse väärtus peab algama tähe- või numbrimärgiga ja tohib sisaldada järgmisi erimärke:\t - ja _
# Error message for JSON path
jsonPathRequired=JSON-i tee on nõutav
# Error message for invalid API Technical Name
invalidTechnicalName=Tehniline nimi sisaldab sobimatuid märke
# Error message for empty Technical Name
emptyTechnicalName=Tehniline nimi on nõutav
# Tooltip for codeEditor dialog
codeEditorTooltip=Ava JSON-i redigeerimise aken
# Status Api path validation message
validationStatusAPIPath=Sisestage sobiv API tee, näiteks: /töö/v1/{id}/olek
# CSRF token URL validation message
validationCSRFTokenURL=CSRF-tõendi URL-i alguses peab olema „https://“ ja see peab olema kehtiv URL
# Select a connection
selectConnection=Valige ühendus
# Validation message for connection item error
connectionNotReplicated=Ühendus ei sobi praegu API toimingute käitamiseks. Avage rakendus „Ühendused“ ja sisestage oma identimisteave HTTP-ühenduse parandamiseks uuesti
# Validation message for API task properties wrong value or missing
VAL_REST_API_PROPERTIES_MISSING=API toimingu „{0}“ väärtused on järgmiste atribuutide jaoks valed või puuduvad: {1}
# Validation message for API connection api is throwing some error or selected connection has some warning
VAL_REST_API_CONNECTION=API toimingul „{0}“ on HTTP-ühendusega probleeme. Avage rakendus „Ühendused“ ja kontrollige ühendust
# Validation message for API task properties when delected connection
VAL_REST_API_CONNECTION_DELETED=API toimingul „{0}“ on kustutatud ühendus „{1}“
# Validation message for API task properties when connection cannot be consumed in the API task
VAL_REST_API_CONNECTION_CONSUMPTION_WARNING=API toimingul „{0}“ on ühendus „{1}“, mida ei saa API toimingute käitamiseks kasutada. Avage rakendus „Ühendused“ ja sisestage identimisteave API toimingute jaoks ühenduse loomiseks uuesti
# Information message if synchrnous mode is selected in the API Invocation then we don't need status information
modeInfoText=Sünkroonses režiimis ei kuvata olekupaneeli API aktiveerimiste jaoks
# validation dialog button for Run API Test
saveAnyway=Salvesta ikkagi
# validation message for technical name
technicalNameValidation=Tehniline nimi peab tegumiahela lõikes olema kordumatu. Valige mõni teine tehniline nimi
# Connection error message
getHttpConnectionsError=HTTP-ühendusi ei saanud tuua
# Msg for the API test run when the Task chain is not saved
taskChainNotSaved=Tegumiahel tuleb enne API testkäituse käivitamist salvestada
# Msg failed to run API test run
@failedToRunAPI=API testkäitust ei saanud käivitada
# Msg for the API test run when its already in running state
apiTaskRunning=API testkäitus on juba pooleli. Kas soovite käivitada uue testkäituse?

topToBtm=Ülevalt alla
leftToRight=Vasakult paremale

#XTIT Apache Spark Application Settings
txtHeaderApacheSettings=Apache Sparki rakenduse sätted
#XFLD Use Default
txtUseSpaceDefault=Kasuta vaikeväärtust
#XFLD Application
txtApplication=Rakendus
#XFLD Define new settings for this Task
txtNewSettings=Määratlege selle ülesande jaoks uued sätted

#XFLD Use Default
txtUseDefault=Kasuta vaikeväärtust




