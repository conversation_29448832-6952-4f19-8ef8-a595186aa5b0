#XTOL Undo
@undo=Врати
#XTOL Redo
@redo=Повтори
#XTOL Delete Selected Symbol
@deleteNode=Избриши го избраниот симбол
#XTOL Zoom to Fit
@zoomToFit=Зумирај за да приспособиш
#XTOL Auto Layout
@autoLayout=Автоматски изглед
#XMSG
@welcomeText=Повлечете па спуштете ги објектите од левата табела на ова платно.
#XMSG
@txtNoData=Се чини дека сè уште не сте додале објект.
#XMSG
VAL_ENTER_VALID_STRING_GEN=Внесете важечка низа со должина помала или еднаква на {0}.
#XMSG
@noParametersMsg=Оваа процедура нема влезни параметри.
#XMSG
@ip_enterValueMsg=„{0}“ (Изврши процедура за SQL-скрипта) има влезни параметри. Може да поставите вредност за секој од нив.
#XTOL
@validateModel=Пораки за потврдување
#XTOL
@hierarchy=Хиерархија
#XTOL
@columnCount=Број на колони
#XFLD
@yes=Да
#XFLD
@no=Не
#XTIT Save Dialog param
@modelNameTaskChain=Синџир од задачи
#properties panel
@lblPropertyTitle=Својства
#XFLD
@lblGeneral=Општо
#XFLD : Setting
@lblSetting=Поставки
#XTXT: Delta Enabled Local Table Deletion
txtDeltaTableDeletion=Избришете ги сите целосно обработени записи со промена на типот „Избришани“ коишто се постари од
#XTXT: Delta Enabled Local Table Deletion Input field description
desDeltaTableDeletion=Денови
#XFLD: Data Activation label
@lblDataActivation=Активирање податоци
#XFLD: Latency label
@latency=Доцнење
#XTEXT: Text for Latency dropdown
txtLatencyDefault=Стандардно
#XTEXT: Text 1 hour
txtOneHour=1 час
#XTEXT: Text 2 hours
txtTwoHours=2 часа
#XTEXT: Text 3 hours
txtThreeHours=3 часа
#XTEXT: Text 4 hours
txtFourHours=4 часа
#XTEXT: Text 6 hours
txtSixHours=6 часа
#XTEXT: Text 12 hours
txtTwelveHours=12 часа
#XTEXT: Text 1 day
txtOneDay=1 ден
#XFLD: Latency label
@autoRestartHead=Автоматско рестартирање
#XFLD
@lblConnectionName=Врска
#XFLD
@lblQualifiedName=Квалификуван назив
#XFLD
@lblSpaceName=Назив на просторот
#XFLD
@lblLocalSchemaName=Локална шема
#XFLD
@lblType=Тип објект
#XFLD
@lblActivity=Активност
#XFLD
@lblTableName=Назив на табелата
#XFLD
@lblBusinessName=Деловен назив
#XFLD
@lblTechnicalName=Технички назив
#XFLD
@lblSpace=Простор
#XFLD
@lblLabel=Етикета
#XFLD
@lblDataType=Тип податоци
#XFLD
@lblDescription=Опис
#XFLD
@lblStorageType=Склад
#XFLD
@lblHTTPConnection=Генеричка HTTP-врска
#XFLD
@lblAPISettings=Генерички поставки за API
#XFLD
@header=Заглавија
#XFLD
@lblInvoke=Повикување API
#XFLD
@lblMethod=Начин
#XFLD
@lblUrl=Основна URL-адреса
#XFLD
@lblAPIPath=API-патека
#XFLD
@lblMode=Режим
#XFLD
@lblCSRFToken=Барај токен CSRF
#XFLD
@lblTokenURL=URL-адреса на токенот CSRF
#XFLD
@csrfTokenInfoText=Aко не е внесена, ќе се употребат основната URL-адреса и API-патеката
#XFLD
@lblCSRF=Токен CSRF
#XFLD
@lblRequestBody=Содржина на барањето
#XFLD
@lblFormat=Формат
#XFLD
@lblResponse=Одговор
#XFLD
@lblId=ИД за вчитување статус
#XFLD
@Id=ИД
#XFLD
@lblSuccessIndicator=Показател на успех
#XFLD
@lblErrorIndicator=Показател на грешка
#XFLD
@lblErrorReason=Причина за грешката
#XFLD
@lblStatus=Статус
#XFLD
@lblApiTestRun=Пробно извршување на API
#XFLD
@lblRunStatus=Статус на извршувањето
#XFLD
@lblLastRan=Последно извршено на
#XFLD
@lblTestRun=Пробно извршување
#XFLD
@lblDefaultHeader=Стандардни полиња на заглавието (парови клуч-вредност)
#XFLD
@lblAdditionalHeader=Дополнително поле на заглавие
#XFLD
@lblKey=Клуч
#XFLD
@lblEditJSON=Уреди го JSON
#XFLD
@lblTasks=Задачи
#XFLD
@lblRESTfullTask=API
#XFLD: add field button text
btnAddField=Додај поле на заглавие
#XFLD: view Details link
@viewDetails=Прикажи ги деталите
#XTOL
tooltipTxt=Повеќе
#XTOL
delete=Избриши
#XBTN: ok button text
btnOk=Во ред
#XBTN: cancel button text
btnCancel=Откажи
#XBTN: save button text
btnSave=Зачувај
#XMSG Error: Object has duplicated technical name.
VAL_OBJECT_DUPLICATE_NAME=Објектот „{0}“ веќе постои во репозиториумот. Внесете друг назив.
#XMSG: loading message while opening task chain
loadTaskChain=Синџирот од задачи се вчитува...
#model properties
#XFLD
@status_panel=Статус на извршувањето
#XFLD
@deploy_status_panel=Статус на применувањето
#XFLD
@status_lbl=Статус
#XFLD
@lblLastExecuted=Последно извршување
#XFLD
@lblNotExecuted=Сè уште не е извршено
#XFLD
@lblNotDeployed=Не е применето
#XFLD
errorDetailsTxt=Статусот на извршување не можеше да се добие
#XBTN: Schedule dropdown menu
SCHEDULE=Распоред
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Уреди распоред
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Избриши распоред
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Создај распоред
#XLNK
viewDetails=Прикажи ги деталите
#XMSG: error message for reading execution status from backend
backendErrorMsg=Изгледа дека податоците не се вчитуваат од серверот во моментов. Обидете се да ги земете податоците повторно.
#XFLD: Status text for Completed
@statusCompleted=Завршено
#XFLD: Status text for Running
@statusRunning=Се извршува
#XFLD: Status text for Failed
@statusFailed=Неуспешно
#XFLD: Status text for Stopped
@statusStopped=Запрено
#XFLD: Status text for Stopping
@statusStopping=Се запира
#XFLD
@LoaderTitle=Се вчитува
#XFLD
#XFLD: Deploy status, dataflow has  been deployed / deployment date exists
@deployStatusActive=Применето
@deployStatusRevised=Локални ажурирања
@deployStatusFailed=Неуспешно
@deployStatusPending=Се применува...
@LoaderText=Се повикуваат детали од серверот
#XMSG
@msgDetailFetchError=Грешка при земањето детали од серверот
#XFLD
@executeError=Грешка
#XFLD
@executeWarning=Предупредување
#XMSG
@executeConfirmDialog=Информации
#XMSG
@executeunsavederror=Зачувајте го синџирот од задачи пред да го извршите.
#XMSG
@executemodifiederror=Има незачувани промени во синџирот од задачи. Зачувајте го.
#XMSG
@executerunningerror=Синџирот од задачи моментално се извршува. Почекајте да заврши тековното извршување пред да започнете со ново.
#XMSG
@btnExecuteAnyway=Изврши во секој случај
#XMSG
@msgExecuteWithValidations=Синџирот од задачи има грешки при проверка. Извршувањето на синџирот од задачи може да заврши со грешка.
#XMSG
@msgRunDeployedVersion=Има промени за применување. Ќе се изврши последната применета верзија на синџирот од задачи. Дали сакате да продолжите?
#XMSG
#XMSG
@navToMonitoring=Отвори во алатка за следење синџир од задачи
#XMSG
txtOR=ИЛИ
#XFLD
@preview=Преглед
#XMSG
txtand=и
#NOTR
@dateFormat=yyyy-MM-dd
#NOTR
@dateTimeFormatFilter=yyyy-MM-dd HH:mm:ss.
#NOTR
@timeFormat=HH:mm:ss
@lblColum=Колона
#XFLD
@lblCondition=Услов
#XFLD
@lblValue=Вредност
#XMSG
@msgJsonInvalid=Синџирот од задачи не може да се зачува бидејќи има грешки во JSON. Проверете и решете.
#XTIT
@msgSaveFailTitle=Неважечки JSON.
#XMSG
NOT_CHAINABLE=Објектот „{0}“ не може да се додаде во синџирот од задачи.
#XMSG
NOT_CHAINABLE_REMOTETABLE=Објект „{0}“: типот репликација ќе се промени.
#XMSG
searchTaskChain=Пребарувај објекти

#XFLD
@txtTaskChain=Синџир од задачи
#XFLD
@txtRemoteTable=Табела на далечина
#XFLD
@txtRemoveData=Отстрани ги реплицираните податоци
#XFLD
@txtRemovePersist=Отстрани ги трајно зачуваните податоци
#XFLD
@txtView=Прикажи
#XFLD
@txtDataFlow=Податочен тек
#XFLD
@txtIL=Интелигентно пребарување
#XFLD
@txtTransformationFlow=Трансформациски тек
#XFLD
@txtReplicationFlow=Репликациски тек
#XFLD
@txtDeltaLocalTable=Локална табела
#XFLD
@txtBWProcessChain=Синџир со процеси на BW
#XFLD
@txtSQLScriptProcedure=Процедура за SQL-скрипта
#XFLD
@txtAPI=API
#XFLD
@txtMerge=Спој
#XFLD
@txtOptimize=Оптимизирај
#XFLD
@txtVacuum=Избриши ги записите

#XFLD
@txtRun=Изврши
#XFLD
@txtPersist=Трајно зачувај
#XFLD
@txtReplicate=Реплицирај
#XFLD
@txtDelete=Избриши ги записите со тип на промена „Избришано“
#XFLD
@txtRunTC=Изврши синџир од задачи
#XFLD
@txtRunBW=Изврши го синџирот со процеси на BW
#XFLD
@txtRunSQLScriptProcedure=Изврши процедура за SQL-скрипта

#XFLD
@txtRunDataFlow=Изврши го податочниот тек
#XFLD
@txtPersistView=Трајно зачувај приказ
#XFLD
@txtReplicateTable=Реплицирај табела
#XFLD
@txtRunIL=Изврши интелегентно пребарување
#XFLD
@txtRunTF=Изврши трансформациски тек
#XFLD
@txtRunRF=Изврши репликациски тек
#XFLD
@txtRemoveReplicatedData=Отстрани ги реплицираните податоци
#XFLD
@txtRemovePersistedData=Отстрани ги трајно зачуваните податоци
#XFLD
@txtMergeData=Спој
#XFLD
@txtOptimizeData=Оптимизирај
#XFLD
@txtVacuumData=Избриши ги записите
#XFLD
@txtRunAPI=Изврши API

#XFLD storage type text
hdlfStorage=Датотека

@statusNew=Не е применето
@statusActive=Применето
@statusRevised=Локални ажурирања
@statusPending=Се применува...
@statusChangesToDeploy=Промени што треба да се применат
@statusDesignTimeError=Грешка во времето на дизајнирањето
@statusRunTimeError=Грешка во времето на извршувањето

#XTIT
txtNodes=Објекти во синџирот од задачи ({0})
#XBTN
@deleteNodes=Избриши

#XMSG
@txtDropDataToDiagram=Повлечете па спуштете ги објектите во дијаграмот.
#XMSG
@noData=Нема објекти

#XFLD
@txtTaskPosition=Позиција на задача

#input parameters and variables
#XFLD
lblInputParameters=Влезни параметри
#XFLD
ip_name=Назив
#XFLD
ip_value=Вредност
#XTEXT
@noObjectsFound=Не се пронајдени објекти

#XMSG
@msgExecuteSuccess=Извршувањето на синџирот од задачи започна.
#XMSG
@msgExecuteFail=Извршувањето на синџирот од задачи не е успешно.
#XMSG
@msgDeployAndRunSuccess=Извршувањето и применувањето на синџирот од задачи започна.
#XMSG
@msgDeployAndRunFail=Применувањето и извршувањето на синџирот од задачи заврши неуспешно.
#XMSG
@titleExecuteBusy=Почекајте.
#XMSG
@msgExecuteBusy=Ги подготвуваме податоците за да започнеме со извршување на синџирот од задачи.
#XMSG
@msgAPITestRunSuccess=Пробното извршување на API започна.
#XMSG
@msgAPIExecuteBusy=Ги подготвуваме податоците за да започнеме со пробно извршување на API.

#XTOL
txtOpenInEditor=Отвори во Уредникот
#XTOL
txtPreviewData=Прегледај ги податоците

#datapreview
#XMSG
@msgDataPreviewNotSupp=Прегледот на податоците не е достапен за овој објект.

#XMSG Error: empty model
VAL_MODEL_EMPTY=Се чини дека моделот е празен. Додајте неколку објекти.
#XMSG Error: deploy model
@msgDeployBeforeRun=Треба да го примените синџирот од задачи пред да го извршите.
#BTN: close dialog
btnClose=Затвори

#XMSG Object is not Deployed
VAL_NOT_DEPLOYED=Објектот „{0}“ мора да биде применет за да продолжи со синџирот од задачи.
#XMSG Object is having runtime error
VAL_RUNTIME_ERROR=Објектот „{0}“ враќа грешка во времето на извршување. Проверете го објектот.
#XMSG Object is having design time error
VAL_DESIGNTIME_ERROR=Објектот „{0}“ враќа грешка во времето на дизајнот. Проверете го објектот.
#XMSG Procedure Object has been deleted
VAL_DELETED_PROCEDURE=Следниве процедури се избришани: {0}
#XMSG New Parameter added to Procedure object
VAL_NEW_PARAMETER_ADDED=Нови параметри се додадени во постапката „{1}“: „{0}“. Повторно применете го синџирот од задачи.
#XMSG Parameter removed from Procedure object
VAL_PARAMETER_DELETED=Параметрите се отстранети од постапката „{1}“: „{0}“. Повторно применете го синџирот од задачи.
#XMSG Parameter data type changed in Procedure object
VAL_PARAMETER_DATATYPE_MODIFIED=Типот податоци на параметарот „{0}“ се промени од „{1}“ во „{2}“во процедурата „{3}“,
#XMSG Parameter length changed of specific datatype in Procedure object
VAL_PARAMETER_LENGTH_MODIFIED=Должината на параметарот „{0}“ се смени од „{1}“ во “{2}“ во постапката „{3}“.
#XMSG Parameter precision changed of Decimal datatype in Procedure object
VAL_PARAMETER_PRECISION_MODIFIED=Прецизноста на параметарот „{0}“ се смени од „{1}“ во “{2}“ во постапката „{3}“.
#XMSG Parameter scale changed of Decimal datatype in Procedure object
VAL_PARAMETER_SCALE_MODIFIED=Скалата на параметарот „{0}“ се смени од „{1}“ во “{2}“ во постапката „{3}“.
#XMSG Parameter value not provided in Procedure object
VAL_SQL_SCRIPT_PROCEDURE_PARAMETER_VALUE_MISSING=Немате внесено вредности за влезните параметри што се задолжителни за постапката „{0}“: „{1}“
#XMSG Remote Table activity changes to Remove Replicated Data
VAL_REMOVE_REPLICATED_DATA=Следното извршување на синџирот од задачи ќе го промени типот на пристапот до податоци и податоците повеќе нема да се поставуваат во реално време.
#XMSG Remote Table activity changes
REPLICATION_TYPE_CHANGED=Објект „{0}“: Типот репликација ќе се промени.

#XFLD
@lblStartNode=Почетен јазол
#XFLD
@lblEndNode=Краен јазол
#XFLD
@linkTo={0} до {1}
#XTOL
@txtViewDetails=Прикажи ги деталите

#XTOL
txtOpenImpactLineage=Анализа на потеклото и влијанието
#XFLD
@emailNotifications=Известувања по е-пошта
#XFLD
@txtReset=Ресетирај
#XFLD
@emailMsgWarning=Стандардниот шаблон за е-пошта ќе се испрати кога е-пораката е празна
#XFLD
@notificationSettings=Поставки за известувања
#XFLD
@recipientEmailAddr=Адреса на е-пошта на примателот
#XFLD
@emailSubject=Наслов на е-пораката
@emailSubjectText=Синџирот од задачи <TASKCHAIN_NAME> е завршен со статус <STATUS>
#XFLD
@emailMessage=Е-порака
@emailMessageText=Почитуван кориснику,\n\n Со ова ве известуваме дека синџирот од задачи:<TASKCHAIN_NAME> што се изврши во <START_TIME> заврши со статус <STATUS>. Извршувањето заврши во <END_TIME>.\n\nДетали:\nПростор:<SPACE_NAME>\nError:<ERROR_MESSAGE>
@selectEmailRecipientDialogTitle=Изберете адреса на е-пошта на примателот
@tenantMembers=Членови на закупец
@others=Други({0})
@selectedEmailAddress=Избрани примачи
@add=Додај
@placeholder=Резервирано место
@description=Опис
@copyText=Копирај го текстот
@taskchainDetailsPlaceholder=Резервирани места за детали за синџирот од задачи
@placeholderCopied=Резервираното место е копирано
@invalidEmailInfo=Внеси точна адреса на е-пошта
@maxMembersAlreadyAdded=Веќе додадовте максимум 20 членови
@enterEmailAddress=Внеси адреса на е-пошта од корисник
@inCorrectPlaceHolder={0} не е очекувано резервирано место во содржината на е-поштата.
@nsOFF=Не испраќај ниту едно известување
@nsFAILED=Испратете известување преку е-пошта само кога извршувањето ќе заврши со грешка
@nsCOMPLETED=Испратете известување преку е-пошта само кога извршувањето ќе заврши успешно
@nsANY=Испрати е-пошта кога ќе заврши извршувањето
@phStatus=Статус на синџирот од задачи – УСПЕШНО|НЕУСПЕШНО
@phTaskChainTName=Технички назив на синџирот од задачи
@phTaskChainBName=Деловен назив на синџирот од задачи
@phLogId=ИД на дневник на извршувањето
@phUser=Корисник што го извршува синџирот од задачи
@phLogUILink=Врска до приказот на дневникот на синџирот од задачи
@phStartTime=Време на започнување на извршувањето
@phEndTime=Време на завршување на извршувањето
@phErrMsg=Првата порака за грешка во дневникот за задачи. Дневникот е празен во случај на УСПЕХ
@phSpaceName=Техничко име на просторот
@emailFormatError=Неважечки формат на е-пошта
@emailFormatErrorInListText=Неважечки формат на е-пошта е внесен во списокот со членови што не се закупци.
@emailSubjectTemplateText=Известување за синџир од задачи: $$taskChainName$$ – Простор: $$spaceId$$ – Статус: $$status$$
@emailMessageTemplateText=Здраво,\n\n синџирот од задачи означен како $$taskChainName$$ заврши со статус $$status$$. \n Еве неколку други детали за синџирот од задачи:\n – Технички назив на синџирот од задачи: $$taskChainName$$ \n – ИД на дневникот на извршувањето на синџирот од задачи: $$logId$$ \n – Корисник што го извршил синџирот од задачи: $$user$$ \n – Врска до приказот на дневникот на синџирот од задачи: $$uiLink$$ \n – Време на започнување на извршувањето на синџирот од задачи: $$startTime$$ \n – Време на завршување на извршувањето на синџирот од задачи : $$endTime$$ \n – Назив на просторот: $$spaceId$$ \n
@deleteEmailRecepient=Избриши примач
@emailInputDisabledText=Применете го синџирот од задачи за да додадете примачи на е-пошта.
@tenantOwnerDomainMatchErrorText=Доменот на адресата на е-пошта не се совпаѓа со доменот на сопственикот на закупецот: {0}
@totalEmailIdLimitInfoText=Може да изберете до 20 примачи на е-пошта, вклучувајќи корисници членки на закупци и други примачи.
@emailDomainInfoText=Прифатени се само адреси на е-пошта со домен: {0}.
@duplicateEmailErrorText=Во списокот има дупликат примачи на е-пошта.

#XFLD Zorder Title
@txtZorderTitle=Колони во Z-редослед во Apache Spark

#XFLD Zorder NoColumn
@txtZorderNoColumn=Не се најдени колони во Z-редослед

#XFLD Zorder Column PrimaryKey
@txtZorderColumnPrimaryKey=Примарен клуч

#XFLD
@lblOperators=Оператори
addNewSelector=Додај како нова задача
parallelSelector=Додај како паралелна задача
replaceSelector=Замени постојна задача
addparallelbranch=Додај како паралелна гранка
addplaceholder=Додај резервирано место
addALLOperation=Оператор ALL
addOROperation=Оператор ANY
addplaceholdertocanvas=Додајте резервирано место на платно
addplaceholderonselected=Додајте резервирано место по избраната задача
#XFLD: Add Parallel Branch to Selected Task
addparallelbranchonselected=Додајте паралелна гранка по избраната задача
addOperator=Додај оператор
txtAdd=Додај
txtPlaceHolderText=Повлечете па спуштете задача овде
@lblLayout=Распоред

#XMSG
VAL_UNCONNECTED_TASK=Задачата „{0}“ не е поврзана со синџирот од задачи.
#XMSG
VAL_TASK_SHOULD_HAVE_ONE_INCOMING_LINK=Задачата „{0}“ треба да има само една влезна врска.
#XMSG
VAL_TASK_SHOULD_HAVE_INCOMING_LINK=Задачата „{0}“ треба да има една влезна врска.
#XMSG
VAL_UNCONNECTED_OPERATOR=Операторот „{0}“ не е поврзан со синџирот од задачи.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ATLEAST_TWO_INCOMING_LINK=Операторот „{0}“ треба да има најмалку две влезни врски.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ONE_OUT_LINK=Операторот „{0}“ треба да има најмалку една излезна врска.
#XMSG
VAL_CIRCULAR_REFERENCE_EXITS=Кружна јамка постои во синџирот од задачи „{0}“.
#XMSG
VAL_UNCONNECTED_BRANCH=Објект/гранка „{0}“ не е поврзан со синџирот од задачи.
#XMSG
VAL_TASK_TRIGGERED_PARALLEL_MORE_THAN_ONE_TIME=Задачата „{0}“ е поврзана паралелно повеќепати. Отстранете ги дупликатите за да продолжите.


txtBegin=Започнете
txtNodesInLink=Вклучени објекти
#XTOL Tooltip for a context button on diagram
openInNewTab=Отвори во нова картичка
#XTOL Tooltip for a context button on diagram
txtConnectToTaskOperator=Повлечете за поврзување
@emailUpdateError=Грешка при ажурирањето на списокот со известувања по е-пошта

#XMSG
noTeamPrivilegeTxt=Немате дозвола да видите список на членови закупци. Користете ја картичката Друго за рачно да додавате примачи на е-пошта.

#XFLD Package
@txtPackage=Пакет

#XMSG warning for repository package assignment changed. {1} - package name
VAL_PACKAGE_CHANGED=Го доделивте овој објект на пакетот „{1}“. Кликнете Зачувај за да ја потврдите променава. Имајте предвид дека доделувањето пакет не може да се поништи во овој уредувач по зачувувањето.
#XMSG warning for repository package dependency issue.
MISSING_DEPENDENCY=Зависностите на објектот „{0}“ не може да се решат во контекст на пакетот „{1}“.

#Warning messages for the folders in the "Others" tab of task chain editor
#XMSG generic warning if getting the objects in a particular folder fails with unhandled errors
getNonRepoArtefactTypeDescendantsError=Настана проблем при вчитување на објектите во папката што ја избравте.
#BW Process Chains folder related warning messages
#XMSG warning if user does not have the necessary permission related to the SAP BW Bridge space
missingPrivilegeForBridgeSpaceError=Ја немате потребната дозвола за да ги прикажете или опфатите синџирите со процеси на BW во синџирот со задачи.
#XMSG warning if there was a problem retrieving the BW process chains due to bridge space membership, bridge connection issues or other errors
generalBridgeSpaceError=Настана проблем при вчитување на синџирите со процеси на BW.
#XMSG warning if the BW API for retrieving the BW process chains from the SAP BW Bridge tenant fails
bwApiClientError=Настана проблем при вчитување на синџирите со процеси на BW од закупецот на SAP BW Bridge.
#XMSG warning if BW process chains list is empty
bwApiEmptyListError=Ниту еден синџир со процеси на BW не е пронајден во закупецот на SAP BW Bridge.
#XMSG: Task log message for invalid OIDC configuration in the SAP BW Bridge tenant
bwBridgeOIDCConfigStatusInvalid=Автентикација со OpenID не е конфигурирана за овој закупец. Користете clientID „{0}“ и tokenURL „{1}“ за да ја конфигурирате автентикацијата со OpenID во закупецот на SAP BW Bridge како што е опишано во забелешката 3536298 од SAP.
#XMSG BW Process Chain Object has been deleted
VAL_DELETED_BW=Следниве синџири со процеси на BW можеби се избришани од закупецот на SAP Bridge: {0}
#BW SQL Script Procedures folder related warning messages
#XFLD waring if the Procedure list is empty
procedureAPIEmptyList=Не се создадени процедури или привилегијата ИЗВРШИ не е доделена во шемата Open SQL.
#Change digram orientations
changeOrientations=Промени ориентации


# placeholder for the API Path
apiPath=На пример: /job/v1
# placeholder for the status API Path
statusAPIPath=На пример: /job/v1/{id}/status
# valueStateText for the API Path
apiPathRequired=API-патеката е задолжителна
#placeholder for the CSRF Token URL
csrfTokenURL=Поддржано е само HTTPS
# Response Type 1
statusCode=Добиј резултат од шифрата за статусот на HTTP
# Response Type 2
locationHeader=Добиј резултат од шифрата за статусот на HTTP и заглавието на локацијата
# Response Type 3
responseBody=Добиј резултат од шифрата за статусот на HTTP и содржината на барањето
# placeholder for ID
idPlaceholder=Внесете JSON-патека
# placeholder for indicator value
indicatorValue=Внесете вредност
# Placeholder for key
keyPlaceholder=Внесете клуч
# Error message for missing key
KeyRequired=Клучот е задолжителен
# Error message for invalid key format
invalidKeyFormat=Клучот на заглавието што го внесовте не е дозволен. Важечки заглавија се:<ul><li>„prefer“</li><li>Заглавија што започнуваат со „x-“, освен „x-forwarded-host“</li><li>Заглавија што содржат алфанумерички знаци, „-“, или „_“</li></ul>
# Error message for missing value
valueRequired=Вредноста е задолжителна
# Error message for invalid characters in value
invalidValueCharacters=Заглавието содржи неважечки знаци. Специјални знаци што се дозволени се:\t „;“, „:“, „-“, „_“, „,“, „?“, „/“ и „*“
# Validation message for invoke api path
apiPathValidation=Внесете важечка API-патека, на пример: /job/v1
# Validation message for JSON path
jsonPathValidation=Внесете важечка JSON-патека
# Validation message for success/error indicator
indicatorValueValidation=Вредноста на показателот мора да започнува со алфанумерички знак и може да ги содржи следниве специјални знаци:\t „-“ и „_“
# Error message for JSON path
jsonPathRequired=JSON-патеката е задолжителна
# Error message for invalid API Technical Name
invalidTechnicalName=Техничкиот назив содржи неважечки знаци
# Error message for empty Technical Name
emptyTechnicalName=Техничкиот назив е задолжителен
# Tooltip for codeEditor dialog
codeEditorTooltip=Отвори го прозорецот за уредување JSON
# Status Api path validation message
validationStatusAPIPath=Внесете важечка API-патека, на пример: /job/v1/{id}/status
# CSRF token URL validation message
validationCSRFTokenURL=URL-адресата на токенот CSRF мора да започнува со „https://“ и да биде важечка URL-адреса
# Select a connection
selectConnection=Изберете врска
# Validation message for connection item error
connectionNotReplicated=Во моментов, врската е неважечка за извршување задачи за API. Отворете ја апликацијата „Врски“ и повторно внесете ги акредитивите за да ја поправите HTTP-врската
# Validation message for API task properties wrong value or missing
VAL_REST_API_PROPERTIES_MISSING=Задачата за API „{0}“ има неточни вредности или ѝ недостигаат вредности за следниве својства: {1}
# Validation message for API connection api is throwing some error or selected connection has some warning
VAL_REST_API_CONNECTION=Задачата за API „{0}“ има проблем со HTTP-врската. Отворете ја апликацијата „Врски“ и проверете ја врската
# Validation message for API task properties when delected connection
VAL_REST_API_CONNECTION_DELETED=Задачата за API „{0}“ има избришана врска „{1}“.
# Validation message for API task properties when connection cannot be consumed in the API task
VAL_REST_API_CONNECTION_CONSUMPTION_WARNING=Задачата за API „{0}“ има врска „{1}“ што не може да се користи за извршување на задачите за API. Отворете ја апликацијата „Врски“ и повторно внесете ги вашите акредитиви за да воспоставите поврзување за задачите за API
# Information message if synchrnous mode is selected in the API Invocation then we don't need status information
modeInfoText=Во синхронизиран режим, панелот за статус не е прикажан за повикувања на API
# validation dialog button for Run API Test
saveAnyway=Сепак зачувај
# validation message for technical name
technicalNameValidation=Техничкиот назив мора да биде единствен во рамките на синџирот од задачи. Изберете друг технички назив
# Connection error message
getHttpConnectionsError=Земањето HTTP-врски е неуспешно
# Msg for the API test run when the Task chain is not saved
taskChainNotSaved=Синџирот од задачи мора да се зачува пред да се направи пробно извршување на API
# Msg failed to run API test run
@failedToRunAPI=Пробното извршување на API не успеа
# Msg for the API test run when its already in running state
apiTaskRunning=Веќе е во тек пробно извршување на API. Дали сакате да започнете ново пробно извршување?

topToBtm=Одгоре надолу
leftToRight=Одлево надесно

#XTIT Apache Spark Application Settings
txtHeaderApacheSettings=Поставки за апликацијата Apache Spark
#XFLD Use Default
txtUseSpaceDefault=Користи стандардни поставки
#XFLD Application
txtApplication=Апликација
#XFLD Define new settings for this Task
txtNewSettings=Дефинирај нови поставки за оваа задача

#XFLD Use Default
txtUseDefault=Користи стандардни поставки




