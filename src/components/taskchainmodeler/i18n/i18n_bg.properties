#XTOL Undo
@undo=Отмяна
#XTOL Redo
@redo=Повторение
#XTOL Delete Selected Symbol
@deleteNode=Изтриване на избрания символ
#XTOL Zoom to Fit
@zoomToFit=Побиране
#XTOL Auto Layout
@autoLayout=Автоматично форматиране
#XMSG
@welcomeText=Провлачете и пуснете обектите от левия панел в тази област.
#XMSG
@txtNoData=Изглежда още не сте добавили обекти.
#XMSG
VAL_ENTER_VALID_STRING_GEN=Моля, въведете валиден низ с дължина, която е по-малка или равна на {0}.
#XMSG
@noParametersMsg=Тази процедура няма входни параметри.
#XMSG
@ip_enterValueMsg=„{0}“ (Изпълнение на процедура на SQL скрипт) има входни параметри. Може да зададете стойност за всеки от тях.
#XTOL
@validateModel=Съобщения от проверката
#XTOL
@hierarchy=Йерархия
#XTOL
@columnCount=Брой колони
#XFLD
@yes=Да
#XFLD
@no=Не
#XTIT Save Dialog param
@modelNameTaskChain=Верига задачи
#properties panel
@lblPropertyTitle=Свойства
#XFLD
@lblGeneral=Общи
#XFLD : Setting
@lblSetting=Настройки
#XTXT: Delta Enabled Local Table Deletion
txtDeltaTableDeletion=Изтриване на всички напълно обработени записи с вид промяна „Изтрито“, които са по-стари от
#XTXT: Delta Enabled Local Table Deletion Input field description
desDeltaTableDeletion=Дни
#XFLD: Data Activation label
@lblDataActivation=Активиране на данни
#XFLD: Latency label
@latency=Латентност
#XTEXT: Text for Latency dropdown
txtLatencyDefault=По подразбиране
#XTEXT: Text 1 hour
txtOneHour=1 час
#XTEXT: Text 2 hours
txtTwoHours=2 часа
#XTEXT: Text 3 hours
txtThreeHours=3 часа
#XTEXT: Text 4 hours
txtFourHours=4 часа
#XTEXT: Text 6 hours
txtSixHours=6 часа
#XTEXT: Text 12 hours
txtTwelveHours=12 часа
#XTEXT: Text 1 day
txtOneDay=1 ден
#XFLD: Latency label
@autoRestartHead=Автоматично рестартиране
#XFLD
@lblConnectionName=Връзка
#XFLD
@lblQualifiedName=Квалифицирано име
#XFLD
@lblSpaceName=Име на пространство
#XFLD
@lblLocalSchemaName=Локална схема
#XFLD
@lblType=Вид обект
#XFLD
@lblActivity=Дейност
#XFLD
@lblTableName=Име на таблица
#XFLD
@lblBusinessName=Бизнес наименование
#XFLD
@lblTechnicalName=Техническо име
#XFLD
@lblSpace=Пространство
#XFLD
@lblLabel=Етикет
#XFLD
@lblDataType=Вид данни
#XFLD
@lblDescription=Описание
#XFLD
@lblStorageType=Съхранение
#XFLD
@lblHTTPConnection=Родова HTTP връзка
#XFLD
@lblAPISettings=Родови настройки за API
#XFLD
@header=Хедъри
#XFLD
@lblInvoke=Извикване на API
#XFLD
@lblMethod=Метод
#XFLD
@lblUrl=Базов URL
#XFLD
@lblAPIPath=Път на API
#XFLD
@lblMode=Режим
#XFLD
@lblCSRFToken=Изисква се CSRF токен
#XFLD
@lblTokenURL=URL на CSRF токен
#XFLD
@csrfTokenInfoText=Ако няма въвеждане, ще бъде използван базовият URL или пътят на API
#XFLD
@lblCSRF=CSRF токен
#XFLD
@lblRequestBody=Основна част на заявка
#XFLD
@lblFormat=Формат
#XFLD
@lblResponse=Отговор
#XFLD
@lblId=ИД за извличане на статус
#XFLD
@Id=ИД
#XFLD
@lblSuccessIndicator=Индикатор за успешно изпълнение
#XFLD
@lblErrorIndicator=Индикатор за грешка
#XFLD
@lblErrorReason=Причина за грешка
#XFLD
@lblStatus=Статус
#XFLD
@lblApiTestRun=Тестово изпълнение на API
#XFLD
@lblRunStatus=Статус на изпълнение
#XFLD
@lblLastRan=Дата на последно изпълнение
#XFLD
@lblTestRun=Тестово изпълнение
#XFLD
@lblDefaultHeader=Полета на заглавка по подразбиране (двойки Ключ-Стойност)
#XFLD
@lblAdditionalHeader=Допълнителни полета на заглавка
#XFLD
@lblKey=Ключ
#XFLD
@lblEditJSON=Редактиране на JSON
#XFLD
@lblTasks=Задачи
#XFLD
@lblRESTfullTask=API
#XFLD: add field button text
btnAddField=Добавяне на полета на заглавка
#XFLD: view Details link
@viewDetails=Преглед на подробни данни
#XTOL
tooltipTxt=Още
#XTOL
delete=Изтриване
#XBTN: ok button text
btnOk=ОК
#XBTN: cancel button text
btnCancel=Отказ
#XBTN: save button text
btnSave=Запазване
#XMSG Error: Object has duplicated technical name.
VAL_OBJECT_DUPLICATE_NAME=В хранилището вече има обект ''{0}''. Моля, въведете друго име.
#XMSG: loading message while opening task chain
loadTaskChain=Зареждане на веригата със задачи...
#model properties
#XFLD
@status_panel=Статус на изпълнение
#XFLD
@deploy_status_panel=Статус на разгръщане
#XFLD
@status_lbl=Статус
#XFLD
@lblLastExecuted=Последно изпълнение
#XFLD
@lblNotExecuted=Все още няма изпълнение
#XFLD
@lblNotDeployed=Неразгърнато
#XFLD
errorDetailsTxt=Невъзможно извличане на статус за изпълнение
#XBTN: Schedule dropdown menu
SCHEDULE=График
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Редактиране на графика
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Изтриване на графика
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Създаване на график
#XLNK
viewDetails=Преглед на подробни данни
#XMSG: error message for reading execution status from backend
backendErrorMsg=Изглежда, че в момента данните не се зареждат от сървъра. Опитайте пак да вземете данните.
#XFLD: Status text for Completed
@statusCompleted=Завършено
#XFLD: Status text for Running
@statusRunning=Изпълнява се
#XFLD: Status text for Failed
@statusFailed=Неуспешно
#XFLD: Status text for Stopped
@statusStopped=Спряно
#XFLD: Status text for Stopping
@statusStopping=Спира
#XFLD
@LoaderTitle=Зареждане
#XFLD
#XFLD: Deploy status, dataflow has  been deployed / deployment date exists
@deployStatusActive=Разгърнато
@deployStatusRevised=Локални актуализации
@deployStatusFailed=Неуспешно
@deployStatusPending=Разгръщане...
@LoaderText=Подробните данни се получават от сървъра
#XMSG
@msgDetailFetchError=Грешка при извличането на подробните данни от сървъра
#XFLD
@executeError=Грешка
#XFLD
@executeWarning=Предупреждение
#XMSG
@executeConfirmDialog=Информация
#XMSG
@executeunsavederror=Запазете веригата си със задачи преди да я изпълните.
#XMSG
@executemodifiederror=Във веригата със задачи има незапазени промени. Моля, запазете ги.
#XMSG
@executerunningerror=Веригата със задачи се изпълнява в момента. Изчакайте завършването на текущото изпълнение преди да започнете ново.
#XMSG
@btnExecuteAnyway=Да се изпълни въпреки това
#XMSG
@msgExecuteWithValidations=Веригата със задачи има грешки при проверка Изпълнението ѝ може да е неуспешно.
#XMSG
@msgRunDeployedVersion=Има промени за разгръщане. Ще бъде изпълнена последната разгърната версия на веригата със задачи. Желаете ли да продължите?
#XMSG
#XMSG
@navToMonitoring=Отваряне в монитор за верига задачи
#XMSG
txtOR=ИЛИ
#XFLD
@preview=Предварителен преглед
#XMSG
txtand=и
#NOTR
@dateFormat=yyyy-MM-dd
#NOTR
@dateTimeFormatFilter=yyyy-MM-dd HH:mm:ss.
#NOTR
@timeFormat=HH:mm:ss
@lblColum=Колона
#XFLD
@lblCondition=Условие
#XFLD
@lblValue=Стойност
#XMSG
@msgJsonInvalid=Веригата със задачи не беше запазена, тъй като в JSON кода има грешки. Моля, проверете го и ги коригирайте.
#XTIT
@msgSaveFailTitle=Невалиден JSON.
#XMSG
NOT_CHAINABLE=Обект ''{0}'' не може да бъде добавен към веригата задачи.
#XMSG
NOT_CHAINABLE_REMOTETABLE=Обект „{0}“: Видът на репликацията ще се промени.
#XMSG
searchTaskChain=Търсене на обекти

#XFLD
@txtTaskChain=Верига задачи
#XFLD
@txtRemoteTable=Отдалечена таблица
#XFLD
@txtRemoveData=Премахване на репликираните данни
#XFLD
@txtRemovePersist=Премахване на трайно съхранените данни
#XFLD
@txtView=Преглеждане
#XFLD
@txtDataFlow=Поток от данни
#XFLD
@txtIL=Интелигентно търсене
#XFLD
@txtTransformationFlow=Поток на трансформации
#XFLD
@txtReplicationFlow=Поток на репликация
#XFLD
@txtDeltaLocalTable=Локална таблица
#XFLD
@txtBWProcessChain=Верига на процес BW
#XFLD
@txtSQLScriptProcedure=Процедура на SQL скрипт
#XFLD
@txtAPI=API
#XFLD
@txtMerge=Сливане
#XFLD
@txtOptimize=Оптимизация
#XFLD
@txtVacuum=Изриване на записи

#XFLD
@txtRun=Изпълнение
#XFLD
@txtPersist=Трайно съхранение
#XFLD
@txtReplicate=Репликация
#XFLD
@txtDelete=Изтриване на записите с вид промяна „Изтрито“
#XFLD
@txtRunTC=Изпълнение на веригата от задачи
#XFLD
@txtRunBW=Изпълнение на верига на процес BW
#XFLD
@txtRunSQLScriptProcedure=Изпълнение на процедура на SQL скрипт

#XFLD
@txtRunDataFlow=Изпълнение на поток от данни
#XFLD
@txtPersistView=Трайно съхраняване на изглед
#XFLD
@txtReplicateTable=Репликиране на таблица
#XFLD
@txtRunIL=Изпълняване на интелигентно търсене
#XFLD
@txtRunTF=Изпълняване на поток на трансформации
#XFLD
@txtRunRF=Изпълнение на поток на репликация
#XFLD
@txtRemoveReplicatedData=Премахване на репликираните данни
#XFLD
@txtRemovePersistedData=Премахване на трайно съхранените данни
#XFLD
@txtMergeData=Сливане
#XFLD
@txtOptimizeData=Оптимизация
#XFLD
@txtVacuumData=Изриване на записи
#XFLD
@txtRunAPI=Изпълнение на API

#XFLD storage type text
hdlfStorage=Файл

@statusNew=Неразгърнато
@statusActive=Разгърнато
@statusRevised=Локални актуализации
@statusPending=Разгръщане...
@statusChangesToDeploy=Промени за разгръщане
@statusDesignTimeError=Грешка във време на дизайн
@statusRunTimeError=Грешка във време на изпълнение

#XTIT
txtNodes=Обекти във верига задачи ({0})
#XBTN
@deleteNodes=Изтриване

#XMSG
@txtDropDataToDiagram=Провлачете и пуснете обекти в диаграмата.
#XMSG
@noData=Няма обекти

#XFLD
@txtTaskPosition=Позиция на задача

#input parameters and variables
#XFLD
lblInputParameters=Входни параметри
#XFLD
ip_name=Име
#XFLD
ip_value=Стойност
#XTEXT
@noObjectsFound=Няма намерени обекти

#XMSG
@msgExecuteSuccess=Изпълнението на веригата от задачи стартира.
#XMSG
@msgExecuteFail=Неуспешно изпълнение на веригата от задачи.
#XMSG
@msgDeployAndRunSuccess=Разгръщането и изпълнението на веригата задачи стартираха.
#XMSG
@msgDeployAndRunFail=Изпълнението и разгръщането на веригата задачи бяха неуспешни.
#XMSG
@titleExecuteBusy=Моля, изчакайте.
#XMSG
@msgExecuteBusy=Подготвяме данните ви за стартиране изпълнението на веригата задачи.
#XMSG
@msgAPITestRunSuccess=Стартирано е тестово изпълнение на API.
#XMSG
@msgAPIExecuteBusy=Подготвяме данните ви за стартиране на тестовото изпълнение на API.

#XTOL
txtOpenInEditor=Отваряне в редактора
#XTOL
txtPreviewData=Предварителен преглед на данните

#datapreview
#XMSG
@msgDataPreviewNotSupp=Предварителният преглед на данните не е наличен за този обект.

#XMSG Error: empty model
VAL_MODEL_EMPTY=Изглежда вашият модел е празен. Моля, добавете обекти.
#XMSG Error: deploy model
@msgDeployBeforeRun=Трябва да разгърнете веригата задачи преди да я изпълните.
#BTN: close dialog
btnClose=Затваряне

#XMSG Object is not Deployed
VAL_NOT_DEPLOYED=Обект ''{0}'' трябва да бъде разгърнат, за да продължите с веригата задачи.
#XMSG Object is having runtime error
VAL_RUNTIME_ERROR=Обект „{0}“ има грешка във времето на изпълнение. Моля, проверете обекта.
#XMSG Object is having design time error
VAL_DESIGNTIME_ERROR=Обект „{0}“ има грешка във времето на дизайн. Моля, проверете обекта.
#XMSG Procedure Object has been deleted
VAL_DELETED_PROCEDURE=Следните процедури са изтрити: {0}
#XMSG New Parameter added to Procedure object
VAL_NEW_PARAMETER_ADDED=Добавени са нови параметри към процедурата "{1}": "{0}". Разгърнете повторно веригата задачи.
#XMSG Parameter removed from Procedure object
VAL_PARAMETER_DELETED=Премахнати са параметри от процедурата "{1}": "{0}". Разгърнете повторно веригата задачи.
#XMSG Parameter data type changed in Procedure object
VAL_PARAMETER_DATATYPE_MODIFIED=Видът данни за параметъра „{0}“ е променен от „{1}“ на „{2}“ в процедурата „{3}“.
#XMSG Parameter length changed of specific datatype in Procedure object
VAL_PARAMETER_LENGTH_MODIFIED=Дължината на параметъра „{0}“ е променена от „{1}“ на „{2}“ в процедурата „{3}“.
#XMSG Parameter precision changed of Decimal datatype in Procedure object
VAL_PARAMETER_PRECISION_MODIFIED=Точността на параметъра „{0}“ е променена от „{1}“ на „{2}“ в процедурата „{3}“.
#XMSG Parameter scale changed of Decimal datatype in Procedure object
VAL_PARAMETER_SCALE_MODIFIED=Мащабът на параметъра „{0}“ е променен от „{1}“ на „{2}“ в процедурата „{3}“.
#XMSG Parameter value not provided in Procedure object
VAL_SQL_SCRIPT_PROCEDURE_PARAMETER_VALUE_MISSING=Не сте въвели стойности за входните параметри, необходими за процедурата "{0}": "{1}"
#XMSG Remote Table activity changes to Remove Replicated Data
VAL_REMOVE_REPLICATED_DATA=Следващото изпълнение на верига от задачи ще промени вида на достъпа до данните и данните вече няма да се качват в реално време.
#XMSG Remote Table activity changes
REPLICATION_TYPE_CHANGED=Обект „{0}“: Видът на репликацията ще се промени.

#XFLD
@lblStartNode=Начален възел
#XFLD
@lblEndNode=Краен възел
#XFLD
@linkTo={0} към {1}
#XTOL
@txtViewDetails=Преглед на подробни данни

#XTOL
txtOpenImpactLineage=Анализ на въздействие и произход
#XFLD
@emailNotifications=Известия по имейл
#XFLD
@txtReset=Нулиране
#XFLD
@emailMsgWarning=В случаите, в които имейл съобщението е празно, се изпраща шаблонния имейл по подразбиране.
#XFLD
@notificationSettings=Настройки на известията
#XFLD
@recipientEmailAddr=Имейл адрес на получателя
#XFLD
@emailSubject=Тема на имейл
@emailSubjectText=Верига от задачи <TASKCHAIN_NAME> завършени със статус <STATUS>
#XFLD
@emailMessage=Съобщение по имейл
@emailMessageText=Здравейте!\n\n Това съобщение е, за да ви уведоми, че веригата от задачи:<TASKCHAIN_NAME> изпълнена в <START_TIME> завърши със статус <STATUS>. Изпълнението приключи в <END_TIME>.\n\nПодробности:\nПространство:<SPACE_NAME>\nГрешка:<ERROR_MESSAGE>
@selectEmailRecipientDialogTitle=Изберете имейл адрес на получателя
@tenantMembers=Членове на притежателя
@others=Други ({0})
@selectedEmailAddress=Избрани получатели
@add=Добавяне
@placeholder=Контейнер
@description=Описание
@copyText=Копиране на текст
@taskchainDetailsPlaceholder=Контейнери за подробни данни за вериги на задачи
@placeholderCopied=Контейнерът е копиран
@invalidEmailInfo=Въведете правилен имейл адрес
@maxMembersAlreadyAdded=Вече сте добавили максималният брой членове – 20
@enterEmailAddress=Въведете имейл адрес на потребителя
@inCorrectPlaceHolder={0} не е очакваният контейнер в текста на имейла.
@nsOFF=Не изпращайте известия
@nsFAILED=Изпращане на имейл известие само когато изпълнението е завършило с грешка
@nsCOMPLETED=Изпращане на имейл известие само когато изпълнението е завършило успешно
@nsANY=Изпращане на имейл, когато изпълнението е завършило
@phStatus=Статус на веригата от задачи – УСПЕШНО|НЕУСПЕШНО
@phTaskChainTName=Техническо име на веригата от задачи
@phTaskChainBName=Бизнес наименование на веригата от задачи
@phLogId=ИД на журнала на изпълнението
@phUser=Потребител, който изпълнява веригата от задачи
@phLogUILink=Линк към прегледа на журнала на веригата от задачи
@phStartTime=Начален час на изпълнението
@phEndTime=Краен час на изпълнението
@phErrMsg=Първо съобщение за грешка в журнала на задачите. При УСПЕХ журналът е празен.
@phSpaceName=Техническо име на пространството
@emailFormatError=Невалиден формат на имейл
@emailFormatErrorInListText=Въведен е невалиден формат на имейл в списък членове, които не са наематели.
@emailSubjectTemplateText=Известие за верига задачи: $$taskChainName$$ - област: $$spaceId$$ - статус: $$status$$
@emailMessageTemplateText=Здравейте,\n\n Вашата верига задачи с етикет $$taskChainName$$ завърши със статус $$status$$. \n Ето някои други подробности за веригата задачи:\n - техническо име на верига задачи: $$taskChainName$$ \n - ИД на журнал за изпълнението на веригата задачи: $$logId$$ \n - Потребител, изпълнил веригата задачи: $$user$$ \n - Връзка към показването на журнала за веригата задачи: $$uiLink$$ \n - Начален час на изпълнението на веригата задачи: $$startTime$$ \n - Краен час на изпълнението на веригата задачи: $$endTime$$ \n - Име на областта: $$spaceId$$ \n
@deleteEmailRecepient=Изтриване на получател
@emailInputDisabledText=Моля, разгърнете веригата от задачи, за да добавите получатели на имейли.
@tenantOwnerDomainMatchErrorText=Домейнът на имейл адреса не съвпада с домейна на собственика на наемателя: {0}
@totalEmailIdLimitInfoText=Може да изберете до 20 получатели на имейли, включително членовете на наемателя и други получатели.
@emailDomainInfoText=Приемат се само имейл адреси с домейн: {0}.
@duplicateEmailErrorText=В списъка има дублирани получатели на имейли.

#XFLD Zorder Title
@txtZorderTitle=Колони на поръчка Z в Apache Spark

#XFLD Zorder NoColumn
@txtZorderNoColumn=Няма намерени колони на поръчка Z

#XFLD Zorder Column PrimaryKey
@txtZorderColumnPrimaryKey=Първичен ключ

#XFLD
@lblOperators=Оператори
addNewSelector=Добавяне като нова задача
parallelSelector=Добавяне като паралелна задача
replaceSelector=Заместване на съществуваща задача
addparallelbranch=Добавяне като паралелно разклонение
addplaceholder=Добавяне на контейнер
addALLOperation=Оператор ALL
addOROperation=Оператор ANY
addplaceholdertocanvas=Добавяне на контейнер в област
addplaceholderonselected=Добавяне на контейнер след избраната задача
#XFLD: Add Parallel Branch to Selected Task
addparallelbranchonselected=Добавяне на разклонение след избраната задача
addOperator=Добавяне на оператор
txtAdd=Добавяне
txtPlaceHolderText=Провлачване и пускане на задача тук
@lblLayout=Оформление

#XMSG
VAL_UNCONNECTED_TASK=Задача „{0}“ не е свързана с веригата задачи.
#XMSG
VAL_TASK_SHOULD_HAVE_ONE_INCOMING_LINK=Задача „{0}“ трябва да има само една входяща връзка.
#XMSG
VAL_TASK_SHOULD_HAVE_INCOMING_LINK=Задача „{0}“ трябва да има една входяща връзка.
#XMSG
VAL_UNCONNECTED_OPERATOR=Оператор „{0}“ не е свързан с веригата задачи.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ATLEAST_TWO_INCOMING_LINK=Оператор „{0}“ трябва да има поне две входящи връзки.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ONE_OUT_LINK=Оператор „{0}“трябва да има поне една изходяща връзка.
#XMSG
VAL_CIRCULAR_REFERENCE_EXITS=Съществува цикъл във веригата задачи „{0}“.
#XMSG
VAL_UNCONNECTED_BRANCH=Обект/разклонение „{0}“ не е свързан с веригата задачи.
#XMSG
VAL_TASK_TRIGGERED_PARALLEL_MORE_THAN_ONE_TIME=Задача „{0}“ е свързана паралелно повече от веднъж. Моля, премахнете дублиранията, за да продължите.


txtBegin=Начало
txtNodesInLink=Свързани обекти
#XTOL Tooltip for a context button on diagram
openInNewTab=Отваряне в нов таб
#XTOL Tooltip for a context button on diagram
txtConnectToTaskOperator=Провлачете за свързване
@emailUpdateError=Грешка в актуализиране на списък с имейл известия

#XMSG
noTeamPrivilegeTxt=Нямате право да виждате списък членове на притежателя. Използвайте таб „Други“, за да добавите ръчно получатели на имейли.

#XFLD Package
@txtPackage=Пакет

#XMSG warning for repository package assignment changed. {1} - package name
VAL_PACKAGE_CHANGED=Присъединили сте този обект към пакета „{1}“. Кликнете върху „Запазване“, за да потвърдите и да валидирате тази промяна. Обърнете внимание, че присъединяването към пакет не може да бъде отменено в този редактор след като го запазите.
#XMSG warning for repository package dependency issue.
MISSING_DEPENDENCY=Зависимостите на обекта „{0}“ не може да се разрешат в контекста на пакета „{1}“.

#Warning messages for the folders in the "Others" tab of task chain editor
#XMSG generic warning if getting the objects in a particular folder fails with unhandled errors
getNonRepoArtefactTypeDescendantsError=Възникна проблем при извличането на обектите в избраната от вас папка.
#BW Process Chains folder related warning messages
#XMSG warning if user does not have the necessary permission related to the SAP BW Bridge space
missingPrivilegeForBridgeSpaceError=Нямате необходимите права за преглед или включване на вeриги от BW процеси във верига от задачи.
#XMSG warning if there was a problem retrieving the BW process chains due to bridge space membership, bridge connection issues or other errors
generalBridgeSpaceError=Възникна проблем при извличането на вериги от BW процеси.
#XMSG warning if the BW API for retrieving the BW process chains from the SAP BW Bridge tenant fails
bwApiClientError=Възникна проблем при извличането на вериги от BW процеси от наемателя на SAP BW моста.
#XMSG warning if BW process chains list is empty
bwApiEmptyListError=В наемателя на SAP BW моста не са намерени вериги от BW процеси.
#XMSG: Task log message for invalid OIDC configuration in the SAP BW Bridge tenant
bwBridgeOIDCConfigStatusInvalid=За този наемател не е конфигурирано удостоверяване с OpenID. За да конфигурирате удостоверяване с OpenID в наемателя на свързващото приложение SAP BW, използвайте clientID „{0}” и tokenURL „{1}”, както е описано в SAP блежка 3536298.
#XMSG BW Process Chain Object has been deleted
VAL_DELETED_BW=Възможно е следните вериги от BW процеси да са изтрити от наемателя на SAP моста: {0}
#BW SQL Script Procedures folder related warning messages
#XFLD waring if the Procedure list is empty
procedureAPIEmptyList=Не са създадени процедури или за схемата Open SQL не е предоставена привилегията за ИЗПЪЛНЕНИЕ.
#Change digram orientations
changeOrientations=Промяна на ориентациите


# placeholder for the API Path
apiPath=Например: /job/v1
# placeholder for the status API Path
statusAPIPath=Например: /job/v1/{id}/status
# valueStateText for the API Path
apiPathRequired=Изисква се път на API
#placeholder for the CSRF Token URL
csrfTokenURL=Поддържа се само HTTPS 
# Response Type 1
statusCode=Получаване на резултати от код на HTTP статус 
# Response Type 2
locationHeader=Получаване на резултати от код на HTTP статус и хедър местоположение
# Response Type 3
responseBody=Получаване на резултати от код на HTTP статус и основна част на отговор
# placeholder for ID
idPlaceholder=Въвеждане на път на JSON
# placeholder for indicator value
indicatorValue=Въвеждане на стойност
# Placeholder for key
keyPlaceholder=Въвеждане на ключ
# Error message for missing key
KeyRequired=Изисква се ключ
# Error message for invalid key format
invalidKeyFormat=Въведеният от вас ключ на хедър не е разрешен. Валидните хедъри са: <ul><li>„prefer“</li><li>Хедъри, започващи с „x“, с изключение на „x-forwarded-host“</li><li>Хедъри, съдържащи буквено-цифрени символи, „-“ или „_“</li></ul>
# Error message for missing value
valueRequired=Изисква се стойност
# Error message for invalid characters in value
invalidValueCharacters=Хедърът съдържа невалидни символи. Разрешените специални символи са:\t„;“, „:“, „-“, „_“, „,“, „?“, „/“ и „*“
# Validation message for invoke api path
apiPathValidation=Моля, въведете валиден път на API, например: /job/v1
# Validation message for JSON path
jsonPathValidation=Моля, въведете валиден път на JSON
# Validation message for success/error indicator
indicatorValueValidation=Стойността на индикатора трябва да започва с буквено-цифрен символ и м:оже да включва следните специални символи:\t „-“ и „_“
# Error message for JSON path
jsonPathRequired=Изисква се път на JSON 
# Error message for invalid API Technical Name
invalidTechnicalName=Техническото име съдържа невалидни символи
# Error message for empty Technical Name
emptyTechnicalName=Изисква се техническо име
# Tooltip for codeEditor dialog
codeEditorTooltip=Отваряне на прозорец за редактиране на JSON
# Status Api path validation message
validationStatusAPIPath=Моля, въведете валиден път на API, например: /job/v1/{id}/status
# CSRF token URL validation message
validationCSRFTokenURL=URL адресът на CSRF токен трябва да започва с „https://“ и да бъде валиден URL
# Select a connection
selectConnection=Избор на връзка
# Validation message for connection item error
connectionNotReplicated=В момента връзката е невалидна за изпълнение на API задачи. Отворете приложението „Връзки“ и въведете повторно идентификационните си данни, за да поправите HTTP връзката
# Validation message for API task properties wrong value or missing
VAL_REST_API_PROPERTIES_MISSING=API задачата „{0}“ има неправилни или липсващи стойности за следните свойства: {1}
# Validation message for API connection api is throwing some error or selected connection has some warning
VAL_REST_API_CONNECTION=API задачата „{0}“ има проблем с HTTP връзката. Отворете приложението „Връзки“ и я проверете
# Validation message for API task properties when delected connection
VAL_REST_API_CONNECTION_DELETED=API задачата „{0}“ има изтрита връзка „{1}“
# Validation message for API task properties when connection cannot be consumed in the API task
VAL_REST_API_CONNECTION_CONSUMPTION_WARNING=API задачата „{0}“ има връзка „{1}“, която не може да се използва за изпълнението на API задачи. Отворете приложението „Връзки“ и въведете отново идентификационните си данни, за да установите свързаност за API задачи.
# Information message if synchrnous mode is selected in the API Invocation then we don't need status information
modeInfoText=В синхронен режим панелът на статус не се показва за извиквания на API
# validation dialog button for Run API Test
saveAnyway=Запазване въпреки всичко
# validation message for technical name
technicalNameValidation=Техническото име трябва да е уникално в рамките на веригата от задачи. Моля, изберете друго техническо име
# Connection error message
getHttpConnectionsError=Неуспешно получаване на HTTP връзките
# Msg for the API test run when the Task chain is not saved
taskChainNotSaved=Преди стартиране изпълнението на теста на API, веригата от задачи трябва да се запази.
# Msg failed to run API test run
@failedToRunAPI=Неуспешно изпълнение на теста на API
# Msg for the API test run when its already in running state
apiTaskRunning=Вече се извършва тестово изпълнение на API. Желаете ли да стартирате ново тестово изпълнение?

topToBtm=От горе надолу
leftToRight=От ляво надясно

#XTIT Apache Spark Application Settings
txtHeaderApacheSettings=Настройки за приложението Apache Spark
#XFLD Use Default
txtUseSpaceDefault=Използване на стойност по подразбиране
#XFLD Application
txtApplication=Приложение
#XFLD Define new settings for this Task
txtNewSettings=Дефиниране на нови настройки за тази задача

#XFLD Use Default
txtUseDefault=Използване на стойност по подразбиране




