#XTOL Undo
@undo=Cofnij
#XTOL Redo
@redo=Ponów
#XTOL Delete Selected Symbol
@deleteNode=Usuń wybrany symbol
#XTOL Zoom to Fit
@zoomToFit=Powiększ w celu dopasowania
#XTOL Auto Layout
@autoLayout=Układ automatyczny
#XMSG
@welcomeText=Przeciągnij i upuść obiekty z panelu po lewej stronie do tego obszaru zawartości.
#XMSG
@txtNoData=Wygląda na to, że jeszcze nie dodano obiektu.
#XMSG
VAL_ENTER_VALID_STRING_GEN=Wprowadź prawidłowy ciąg znaków o długości krótszej niż {0}.
#XMSG
@noParametersMsg=Ta procedura nie ma parametrów wejściowych.
#XMSG
@ip_enterValueMsg="{0}" (Uruchom procedurę skryptu SQL) ma nieprawidłowe parametry. <PERSON><PERSON><PERSON><PERSON> usta<PERSON><PERSON> wartość każdego z nich.
#XTOL
@validateModel=Komunikaty walidacji
#XTOL
@hierarchy=Hierarchia
#XTOL
@columnCount=Liczba kolumn
#XFLD
@yes=Tak
#XFLD
@no=Nie
#XTIT Save Dialog param
@modelNameTaskChain=Łańcuchy zadań
#properties panel
@lblPropertyTitle=Właściwości
#XFLD
@lblGeneral=Ogólne
#XFLD : Setting
@lblSetting=Ustawienia
#XTXT: Delta Enabled Local Table Deletion
txtDeltaTableDeletion=Usuń wszystkie całkowicie przetworzone rekordy z typem zmiany 'Usunięte', które są starsze niż
#XTXT: Delta Enabled Local Table Deletion Input field description
desDeltaTableDeletion=Dni
#XFLD: Data Activation label
@lblDataActivation=Aktywacja danych
#XFLD: Latency label
@latency=Zwłoka
#XTEXT: Text for Latency dropdown
txtLatencyDefault=Domyślnie
#XTEXT: Text 1 hour
txtOneHour=1 godzina
#XTEXT: Text 2 hours
txtTwoHours=2 godzin
#XTEXT: Text 3 hours
txtThreeHours=3 godzin
#XTEXT: Text 4 hours
txtFourHours=4 godzin
#XTEXT: Text 6 hours
txtSixHours=6 godzin
#XTEXT: Text 12 hours
txtTwelveHours=12 godzin
#XTEXT: Text 1 day
txtOneDay=1 dzień
#XFLD: Latency label
@autoRestartHead=Automatyczne ponowne uruchomienie
#XFLD
@lblConnectionName=Połączenie
#XFLD
@lblQualifiedName=Nazwa kwalifikowana
#XFLD
@lblSpaceName=Nazwa przestrzeni
#XFLD
@lblLocalSchemaName=Schemat lokalny
#XFLD
@lblType=Typ obiektu
#XFLD
@lblActivity=Działanie
#XFLD
@lblTableName=Nazwa tabeli
#XFLD
@lblBusinessName=Nazwa biznesowa
#XFLD
@lblTechnicalName=Nazwa techniczna
#XFLD
@lblSpace=Przestrzeń
#XFLD
@lblLabel=Etykieta
#XFLD
@lblDataType=Typ danych
#XFLD
@lblDescription=Opis
#XFLD
@lblStorageType=Pamięć
#XFLD
@lblHTTPConnection=Ogólne połączenie HTTP
#XFLD
@lblAPISettings=Ustawienia ogólnego interfejsu API
#XFLD
@header=Nagłówki
#XFLD
@lblInvoke=Wywołanie interfejsu API
#XFLD
@lblMethod=Metoda
#XFLD
@lblUrl=Podstawowy adres URL
#XFLD
@lblAPIPath=Ścieżka API
#XFLD
@lblMode=Tryb
#XFLD
@lblCSRFToken=Wymagaj tokenu CSRF
#XFLD
@lblTokenURL=Adres URL tokenu CSRF
#XFLD
@csrfTokenInfoText=Jeśli nie zostanie wprowadzony, będą używane podstawowy adres URL i ścieżka API
#XFLD
@lblCSRF=Token CSRF
#XFLD
@lblRequestBody=Treść żądania
#XFLD
@lblFormat=Format
#XFLD
@lblResponse=Odpowiedź
#XFLD
@lblId=ID do uzyskania statusu
#XFLD
@Id=ID
#XFLD
@lblSuccessIndicator=Wskaźnik powodzenia
#XFLD
@lblErrorIndicator=Wskaźnik błędu
#XFLD
@lblErrorReason=Przyczyna błędu
#XFLD
@lblStatus=Status
#XFLD
@lblApiTestRun=Przebieg próbny API
#XFLD
@lblRunStatus=Status uruchomienia
#XFLD
@lblLastRan=Termin ostatniego uruchomienia
#XFLD
@lblTestRun=Przebieg próbny
#XFLD
@lblDefaultHeader=Domyślne pola nagłówka (pary klucz-wartość)
#XFLD
@lblAdditionalHeader=Dodatkowe pole nagłówka
#XFLD
@lblKey=Klucz
#XFLD
@lblEditJSON=Edytuj JSON
#XFLD
@lblTasks=Zadania
#XFLD
@lblRESTfullTask=Interfejs API
#XFLD: add field button text
btnAddField=Dodaj pole nagłówka
#XFLD: view Details link
@viewDetails=Wyświetl szczegóły
#XTOL
tooltipTxt=Więcej
#XTOL
delete=Usuń
#XBTN: ok button text
btnOk=Ok
#XBTN: cancel button text
btnCancel=Anuluj
#XBTN: save button text
btnSave=Zapisz
#XMSG Error: Object has duplicated technical name.
VAL_OBJECT_DUPLICATE_NAME=Obiekt "{0}" już istnieje w repozytorium. Wprowadź inną nazwę.
#XMSG: loading message while opening task chain
loadTaskChain=Wczytywanie łańcucha zadań...
#model properties
#XFLD
@status_panel=Status uruchomienia
#XFLD
@deploy_status_panel=Status wdrożenia
#XFLD
@status_lbl=Status
#XFLD
@lblLastExecuted=Ostatni przebieg
#XFLD
@lblNotExecuted=Jeszcze nie uruchomiono
#XFLD
@lblNotDeployed=Niewdrożone
#XFLD
errorDetailsTxt=Nie można było pobrać statusu przebiegu
#XBTN: Schedule dropdown menu
SCHEDULE=Harmonogram
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Edytuj harmonogram
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Usuń harmonogram
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Utwórz harmonogram
#XLNK
viewDetails=Wyświetl szczegóły
#XMSG: error message for reading execution status from backend
backendErrorMsg=Wygląda na to, że aktualnie dane nie wczytują się z serwera. Spróbuj ponownie pobrać dane.
#XFLD: Status text for Completed
@statusCompleted=Zakończone
#XFLD: Status text for Running
@statusRunning=Aktywne
#XFLD: Status text for Failed
@statusFailed=Niepowodzenie
#XFLD: Status text for Stopped
@statusStopped=Zatrzymane
#XFLD: Status text for Stopping
@statusStopping=Zatrzymywanie
#XFLD
@LoaderTitle=Wczytywanie
#XFLD
#XFLD: Deploy status, dataflow has  been deployed / deployment date exists
@deployStatusActive=Wdrożone
@deployStatusRevised=Lokalne aktualizacje
@deployStatusFailed=Niepowodzenie
@deployStatusPending=Wdrażanie...
@LoaderText=Pobieranie szczegółów z serwera
#XMSG
@msgDetailFetchError=Błąd podczas pobierania szczegółów z serwera
#XFLD
@executeError=Błąd
#XFLD
@executeWarning=Ostrzeżenie
#XMSG
@executeConfirmDialog=Informacje
#XMSG
@executeunsavederror=Zapisz swój łańcuch zadań przed uruchomieniem.
#XMSG
@executemodifiederror=Łańcuch zadań zawiera niezapisane zmiany.
#XMSG
@executerunningerror=Łańcuch zadań jest aktualnie wykonywany. Poczekaj na zakończenie bieżącego przebiegu przed rozpoczęciem nowego.
#XMSG
@btnExecuteAnyway=Uruchom mimo to
#XMSG
@msgExecuteWithValidations=Łańcuch zadań zawiera błędy walidacji. Uruchomienie łańcucha zadań może skutkować niepowodzeniem
#XMSG
@msgRunDeployedVersion=Istnieją zmiany wymagające wdrożenia. Uruchomiona zostanie ostatnia wdrożona wersja łańcucha zadań. Czy chcesz kontynuować?
#XMSG
#XMSG
@navToMonitoring=Otwórz w monitorze łańcucha zadań
#XMSG
txtOR=LUB
#XFLD
@preview=Podgląd
#XMSG
txtand=ORAZ
#NOTR
@dateFormat=yyyy-MM-dd
#NOTR
@dateTimeFormatFilter=yyyy-MM-dd HH:mm:ss.
#NOTR
@timeFormat=HH:mm:ss
@lblColum=Kolumna
#XFLD
@lblCondition=Warunek
#XFLD
@lblValue=Wartość
#XMSG
@msgJsonInvalid=Nie można było zapisać łańcucha zadań z powodu błędów w pliku JSON. Sprawdź i rozwiąż problem.
#XTIT
@msgSaveFailTitle=Nieprawidłowy plik JSON.
#XMSG
NOT_CHAINABLE=Nie można dodać obiektu "{0}" do łańcucha zadań.
#XMSG
NOT_CHAINABLE_REMOTETABLE=Obiekt "{0}": typ replikacji zostanie zmieniony.
#XMSG
searchTaskChain=Szukaj obiektów

#XFLD
@txtTaskChain=Łańcuch zadań
#XFLD
@txtRemoteTable=Tabela zdalna
#XFLD
@txtRemoveData=Usuń zreplikowane dane
#XFLD
@txtRemovePersist=Usuń utrwalone dane
#XFLD
@txtView=Widok
#XFLD
@txtDataFlow=Przepływ danych
#XFLD
@txtIL=Inteligentne wyszukiwanie
#XFLD
@txtTransformationFlow=Przepływ transformacji
#XFLD
@txtReplicationFlow=Przepływ replikacji
#XFLD
@txtDeltaLocalTable=Tabela lokalna
#XFLD
@txtBWProcessChain=Łańcuch procesów BW
#XFLD
@txtSQLScriptProcedure=Procedura skryptu SQL
#XFLD
@txtAPI=Interfejs API
#XFLD
@txtMerge=Scal
#XFLD
@txtOptimize=Optymalizuj
#XFLD
@txtVacuum=Usuń rekordy

#XFLD
@txtRun=Uruchom
#XFLD
@txtPersist=Utrwal
#XFLD
@txtReplicate=Replikuj
#XFLD
@txtDelete=Usuń rekordy z typem zmiany 'Usunięte'
#XFLD
@txtRunTC=Uruchom łańcuch zadań
#XFLD
@txtRunBW=Uruchom łańcuch procesów BW
#XFLD
@txtRunSQLScriptProcedure=Uruchom procedurę skryptu SQL

#XFLD
@txtRunDataFlow=Uruchom przepływ danych
#XFLD
@txtPersistView=Utrwal widok
#XFLD
@txtReplicateTable=Replikuj tabelę
#XFLD
@txtRunIL=Wykonaj inteligentne wyszukiwanie
#XFLD
@txtRunTF=Uruchom przepływ transformacji
#XFLD
@txtRunRF=Wykonaj przepływ replikacji
#XFLD
@txtRemoveReplicatedData=Usuń zreplikowane dane
#XFLD
@txtRemovePersistedData=Usuń utrwalone dane
#XFLD
@txtMergeData=Scal
#XFLD
@txtOptimizeData=Optymalizuj
#XFLD
@txtVacuumData=Usuń rekordy
#XFLD
@txtRunAPI=Uruchom API

#XFLD storage type text
hdlfStorage=Plik

@statusNew=Niewdrożone
@statusActive=Wdrożone
@statusRevised=Lokalne aktualizacje
@statusPending=Wdrażanie...
@statusChangesToDeploy=Zmiany do wdrożenia
@statusDesignTimeError=Błąd czasu projektowania
@statusRunTimeError=Czas przebiegu

#XTIT
txtNodes=Obiekty w łańcuchu zadań ({0})
#XBTN
@deleteNodes=Usuń

#XMSG
@txtDropDataToDiagram=Przeciągnij i upuść obiekty na wykresie.
#XMSG
@noData=Brak obiektów

#XFLD
@txtTaskPosition=Pozycja zadania

#input parameters and variables
#XFLD
lblInputParameters=Parametry wejściowe
#XFLD
ip_name=Nazwa
#XFLD
ip_value=Wartość
#XTEXT
@noObjectsFound=Nie znaleziono obiektów

#XMSG
@msgExecuteSuccess=Łańcuch zadań został uruchomiony.
#XMSG
@msgExecuteFail=Uruchomienie łańcucha zadań nie powiodło się.
#XMSG
@msgDeployAndRunSuccess=Rozpoczęto wdrożenie i uruchomienie łańcucha zadań.
#XMSG
@msgDeployAndRunFail=Wdrożenie i uruchomienie łańcucha zadań nie powiodło się.
#XMSG
@titleExecuteBusy=Czekaj.
#XMSG
@msgExecuteBusy=Przygotowujemy dane do rozpoczęcia przebiegu łańcucha danych.
#XMSG
@msgAPITestRunSuccess=Uruchomiono przebieg testowy interfejsu API.
#XMSG
@msgAPIExecuteBusy=Przygotowujemy dane do rozpoczęcia przebiegu testowego interfejsu API.

#XTOL
txtOpenInEditor=Otwórz w edytorze
#XTOL
txtPreviewData=Wyświetl podgląd danych

#datapreview
#XMSG
@msgDataPreviewNotSupp=Dla tego obiektu podgląd danych nie jest dostępny.

#XMSG Error: empty model
VAL_MODEL_EMPTY=Wygląda na to, że Twój model jest pusty. Dodaj kilka obiektów.
#XMSG Error: deploy model
@msgDeployBeforeRun=Musisz wdrożyć łańcuch zadań przed uruchomieniem.
#BTN: close dialog
btnClose=Zamknij

#XMSG Object is not Deployed
VAL_NOT_DEPLOYED=Aby można było kontynuować łańcuch zadań, obiekt "{0}" musi zostać wdrożony.
#XMSG Object is having runtime error
VAL_RUNTIME_ERROR=Obiekt "{0}" zwrócił błąd czasu wykonania. Sprawdź obiekt.
#XMSG Object is having design time error
VAL_DESIGNTIME_ERROR=Obiekt "{0}" zwrócił błąd czasu projektowania. Sprawdź obiekt.
#XMSG Procedure Object has been deleted
VAL_DELETED_PROCEDURE=Usunięto następującą procedurę: {0}
#XMSG New Parameter added to Procedure object
VAL_NEW_PARAMETER_ADDED=Dodano nowe parametry do procedury "{1}": "{0}". Wdróż ponownie łańcuch zadań.
#XMSG Parameter removed from Procedure object
VAL_PARAMETER_DELETED=Usunięto parametry z procedury "{1}": "{0}". Wdróż ponownie łańcuch zadań.
#XMSG Parameter data type changed in Procedure object
VAL_PARAMETER_DATATYPE_MODIFIED=Typ danych parametru "{0}" zmieniono z "{1}" na "{2}" w procedurze "{3}".
#XMSG Parameter length changed of specific datatype in Procedure object
VAL_PARAMETER_LENGTH_MODIFIED=Długość parametru "{0}" zmieniono z "{1}" na "{2}" w procedurze "{3}".
#XMSG Parameter precision changed of Decimal datatype in Procedure object
VAL_PARAMETER_PRECISION_MODIFIED=Dokładność parametru "{0}" zmieniono z "{1}" na "{2}" w procedurze "{3}".
#XMSG Parameter scale changed of Decimal datatype in Procedure object
VAL_PARAMETER_SCALE_MODIFIED=Skalę parametru "{0}" zmieniono z "{1}" na "{2}" w procedurze "{3}".
#XMSG Parameter value not provided in Procedure object
VAL_SQL_SCRIPT_PROCEDURE_PARAMETER_VALUE_MISSING=Nie wprowadzono wartości dla parametrów wejściowych wymaganych dla procedury "{0}": "{1}"
#XMSG Remote Table activity changes to Remove Replicated Data
VAL_REMOVE_REPLICATED_DATA=Kolejny przebieg łańcucha zadań spowoduje zmianę typu dostępu do danych i dane nie będą już aktualizowane w czasie rzeczywistym.
#XMSG Remote Table activity changes
REPLICATION_TYPE_CHANGED=Obiekt "{0}": typ replikacji zostanie zmieniony.

#XFLD
@lblStartNode=Węzeł początkowy
#XFLD
@lblEndNode=Węzeł końcowy
#XFLD
@linkTo={0} do {1}
#XTOL
@txtViewDetails=Wyświetl szczegóły

#XTOL
txtOpenImpactLineage=Analiza wpływu i pochodzenia
#XFLD
@emailNotifications=Powiadomienia e-mail
#XFLD
@txtReset=Resetuj
#XFLD
@emailMsgWarning=Gdy wiadomość e-mail jest pusta, wysłany zostanie e-mail oparty na domyślnym szablonie
#XFLD
@notificationSettings=Ustawienia powiadomień
#XFLD
@recipientEmailAddr=Adres e-mail odbiorcy
#XFLD
@emailSubject=Temat e-maila
@emailSubjectText=Ukończono łańcuch zadań <TASKCHAIN_NAME> ze statusem <STATUS>
#XFLD
@emailMessage=Wiadomość e-mail
@emailMessageText=Szanowny Użytkowniku,\n\n łańcuch zadań:<TASKCHAIN_NAME> rozpoczęty o godzinie <START_TIME> ukończono ze statusem <STATUS>. Wykonanie zostało zakończono o godzinie <END_TIME>.\n\nSzczegóły:\nPrzestrzeń:<SPACE_NAME>\nBłąd:<ERROR_MESSAGE>
@selectEmailRecipientDialogTitle=Wybierz adres e-mail odbiorcy
@tenantMembers=Członkowie tenanta
@others=Inni ({0})
@selectedEmailAddress=Wybrani odbiorcy
@add=Dodaj
@placeholder=Wypełniacz
@description=Opis
@copyText=Kopiuj tekst
@taskchainDetailsPlaceholder=Wypełniacze dla szczegółów łańcucha zadań
@placeholderCopied=Skopiowano wypełniacz
@invalidEmailInfo=Wprowadź prawidłowy adres e-mail
@maxMembersAlreadyAdded=Dodano już maksymalną liczbę 20 uczestników
@enterEmailAddress=Wprowadź adres e-mail użytkownika
@inCorrectPlaceHolder={0} Nie jest oczekiwanym wypełniaczem w treści wiadomości e-mail.
@nsOFF=Nie wysyłaj powiadomień
@nsFAILED=Wyślij powiadomienie e-mail tylko wtedy, gdy przebieg zakończy się błędem
@nsCOMPLETED=Wyślij powiadomienie e-mail tylko wtedy, gdy przebieg zakończy się powodzeniem
@nsANY=Wyślij wiadomość e-mail po ukończeniu przebiegu
@phStatus=Status łańcucha zadań - POWODZENIE|NIEPOWODZENIE
@phTaskChainTName=Nazwa techniczna łańcucha zadań
@phTaskChainBName=Nazwa biznesowa łańcucha zadań
@phLogId=ID logu przebiegu
@phUser=Użytkownik, który wykonuje łańcuch zadań
@phLogUILink=Odsyłacz do wyświetlania logu łańcucha zadań
@phStartTime=Czas rozpoczęcia przebiegu
@phEndTime=Czas zakończenia przebiegu
@phErrMsg=Pierwszy komunikat o błędzie w logu zadań. Log jest pusty w przypadku statusu POWODZENIE
@phSpaceName=Nazwa techniczna przestrzeni
@emailFormatError=Nieprawidłowy format adresu e-mail
@emailFormatErrorInListText=Wprowadzono nieprawidłowy format adresu e-mail na liście członków innych niż mandant.
@emailSubjectTemplateText=Powiadomienie dla łańcucha zadań: $$taskChainName$$ - Space: $$spaceId$$ - Status: $$status$$
@emailMessageTemplateText=Witaj!\n\n Twój łańcuch zadań oznaczony jako $$taskChainName$$ został zakończony ze statusem $$status$$. \n Oto kilka innych szczegółów dotyczących łańcucha zadań:\n - Nazwa techniczna łańcucha zadań: $$taskChainName$$ \n - ID logu przebiegu łańcucha zadań: $$logId$$ \n - Użytkownik, który uruchomił łańcuch zadań: $$user$$ \n - Odsyłacz do wyświetlania logi łańcucha zadań: $$uiLink$$ \n - Godzina rozpoczęcia przebiegu łańcucha zadań: $$startTime$$ \n - Godzina zakończenia przebiegu łańcucha zadań: $$endTime$$ \n - Nazwa przestrzeni: $$spaceId$$ \n
@deleteEmailRecepient=Usuń odbiorcę
@emailInputDisabledText=Wdróż łańcuch zadań, aby dodać odbiorców e-maila.
@tenantOwnerDomainMatchErrorText=Domena adresu e-mail jest niezgodna z domeną właściciela tenanta: {0}
@totalEmailIdLimitInfoText=Możesz wybrać maksymalnie 20 odbiorców e-maila, w tym użytkowników członka tenanta oraz innych odbiorców.
@emailDomainInfoText=Akceptowane są tylko adresy e-mail z domeną: {0}.
@duplicateEmailErrorText=Lista zawiera zduplikowanych odbiorców e-maila.

#XFLD Zorder Title
@txtZorderTitle=Kolumny Apache Spark Z-Order

#XFLD Zorder NoColumn
@txtZorderNoColumn=Nie znaleziono kolumn Z-Order

#XFLD Zorder Column PrimaryKey
@txtZorderColumnPrimaryKey=Klucz główny

#XFLD
@lblOperators=Operatory
addNewSelector=Dodaj jako nowe zadanie
parallelSelector=Dodaj jako zadanie równoległe
replaceSelector=Zastąp istniejące zadanie
addparallelbranch=Dodaj jako gałąź równoległą
addplaceholder=Dodaj wypełniacz
addALLOperation=Operator ALL
addOROperation=Operator ANY
addplaceholdertocanvas=Dodaj wypełniacz do obszaru zawartości
addplaceholderonselected=Dodaj wypełniacz po wybranym zadaniu
#XFLD: Add Parallel Branch to Selected Task
addparallelbranchonselected=Dodaj gałąź równoległą po wybranym zadaniu
addOperator=Dodaj operatora
txtAdd=Dodaj
txtPlaceHolderText=Przeciągnij i upuść zadanie tutaj
@lblLayout=Układ

#XMSG
VAL_UNCONNECTED_TASK=Zadanie "{0}" nie jest połączone z łańcuchem zadań.
#XMSG
VAL_TASK_SHOULD_HAVE_ONE_INCOMING_LINK=Zadanie "{0}" powinno mieć tylko jedno łącze przychodzące.
#XMSG
VAL_TASK_SHOULD_HAVE_INCOMING_LINK=Zadanie "{0}" powinno mieć jedno łącze przychodzące.
#XMSG
VAL_UNCONNECTED_OPERATOR=Operator "{0}" nie jest połączony z łańcuchem zadań.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ATLEAST_TWO_INCOMING_LINK=Operator "{0}" powinien mieć co najmniej dwa łącza przychodzące.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ONE_OUT_LINK=Operator "{0}" powinien mieć co najmniej jedno łącze wychodzące.
#XMSG
VAL_CIRCULAR_REFERENCE_EXITS=W łańcuchu zadań "{0}" istnieje pętla cykliczna.
#XMSG
VAL_UNCONNECTED_BRANCH=Obiekt/gałąź "{0}" nie ma połączenia z łańcuchem zadań.
#XMSG
VAL_TASK_TRIGGERED_PARALLEL_MORE_THAN_ONE_TIME=Zadanie "{0}" jest połączone równolegle więcej niż raz. Usuń duplikaty, aby kontynuować.


txtBegin=Rozpocznij
txtNodesInLink=Uwzględnione obiekty
#XTOL Tooltip for a context button on diagram
openInNewTab=Otwórz w nowej karcie
#XTOL Tooltip for a context button on diagram
txtConnectToTaskOperator=Przeciągnij do kontekstu
@emailUpdateError=Błąd podczas aktualizacji listy powiadomień e-mail

#XMSG
noTeamPrivilegeTxt=Nie masz uprawnień do wyświetlenia listy członków tenanta. Użyj karty Inni, aby ręcznie dodać odbiorców e-maila.

#XFLD Package
@txtPackage=Pakiet

#XMSG warning for repository package assignment changed. {1} - package name
VAL_PACKAGE_CHANGED=Ten obiekt został przypisany do pakietu "{1}". Kliknij opcję Zapisz, aby potwierdzić tę zmianę i przeprowadzić jej walidację. Zauważ, że po zapisaniu nie będzie można cofnąć przypisania do pakietu w tym edytorze.
#XMSG warning for repository package dependency issue.
MISSING_DEPENDENCY=Zależności obiektu "{0}" nie mogą zostać rozwiązane kontekście pakietu "{1}".

#Warning messages for the folders in the "Others" tab of task chain editor
#XMSG generic warning if getting the objects in a particular folder fails with unhandled errors
getNonRepoArtefactTypeDescendantsError=Wystąpił problem podczas pobierania obiektów w wybranym folderze.
#BW Process Chains folder related warning messages
#XMSG warning if user does not have the necessary permission related to the SAP BW Bridge space
missingPrivilegeForBridgeSpaceError=Nie masz uprawnień potrzebnych do wyświetlania lub uwzględniania łańcuchów procesów BW w łańcuchu zadań.
#XMSG warning if there was a problem retrieving the BW process chains due to bridge space membership, bridge connection issues or other errors
generalBridgeSpaceError=Wystąpił problem podczas pobierania łańcuchów procesów BW.
#XMSG warning if the BW API for retrieving the BW process chains from the SAP BW Bridge tenant fails
bwApiClientError=Wystąpił problem podczas pobierania łańcuchów procesów BW z tenanta SAP BW Bridge.
#XMSG warning if BW process chains list is empty
bwApiEmptyListError=Nie znaleziono łańcuchów procesów BW w tenancie SAP BW Bridge.
#XMSG: Task log message for invalid OIDC configuration in the SAP BW Bridge tenant
bwBridgeOIDCConfigStatusInvalid=Nie skonfigurowano uwierzytelniania OpenID dla tego tenanta. Użyj ID klienta "{0}" i adresu URL tokena "{1}", aby skonfigurować uwierzytelnianie OpenID w tenancie SAP BW Bridge zgodnie z opisem w nocie SAP 3536298.
#XMSG BW Process Chain Object has been deleted
VAL_DELETED_BW=Następujące łańcuchy procesów BW zostały prawdopodobnie usunięte z tenanta SAP BW Bridge: {0}
#BW SQL Script Procedures folder related warning messages
#XFLD waring if the Procedure list is empty
procedureAPIEmptyList=Nie utworzono procedur lub nie przyznano uprawnienia EXECUTE dla schematu Open SQL.
#Change digram orientations
changeOrientations=Zmień orientacje


# placeholder for the API Path
apiPath=Na przykład: /job/v1
# placeholder for the status API Path
statusAPIPath=Na przykład: /job/v1/{id}/status
# valueStateText for the API Path
apiPathRequired=Wymagana jest ścieżka API
#placeholder for the CSRF Token URL
csrfTokenURL=Obsługiwane jest tylko HTTPS
# Response Type 1
statusCode=Uzyskaj wynik z kodu statusu HTTP
# Response Type 2
locationHeader=Uzyskaj wynik z kodu statusu HTTP i nagłówka lokalizacji
# Response Type 3
responseBody=Uzyskaj wynik z kodu statusu HTTP i treści odpowiedzi
# placeholder for ID
idPlaceholder=Wprowadź ścieżkę JSON
# placeholder for indicator value
indicatorValue=Wprowadź wartość
# Placeholder for key
keyPlaceholder=Wprowadź klucz
# Error message for missing key
KeyRequired=Klucz jest wymagany
# Error message for invalid key format
invalidKeyFormat=Wprowadzony klucz nagłówka jest niedozwolony. Poprawne nagłówki to:<ul><li>"prefer";</li><li>nagłówki rozpoczynające się od "x-", oprócz "x-forwarded-host";</li><li>nagłówki, które zawierają znaki alfanumeryczne, znak "-" lub "_".</li></ul>
# Error message for missing value
valueRequired=Wartość jest wymagana
# Error message for invalid characters in value
invalidValueCharacters=Ten nagłówek zawiera niepoprawne znaki. Dozwolone znaki specjalne:\t ";", ":", "-", "_", ",", "?", "/" oraz "*"
# Validation message for invoke api path
apiPathValidation=Wprowadź poprawną ścieżkę API, na przykład: /job/v1
# Validation message for JSON path
jsonPathValidation=Wprowadź poprawną ścieżkę JSON
# Validation message for success/error indicator
indicatorValueValidation=Wartość wskaźnika musi zaczynać się od znaku alfanumerycznego i może zawierać następujące znaki specjalne:\t "-" oraz "_"
# Error message for JSON path
jsonPathRequired=Wymagana jest ścieżka JSON
# Error message for invalid API Technical Name
invalidTechnicalName=Ta nazwa techniczna zawiera niepoprawne znaki
# Error message for empty Technical Name
emptyTechnicalName=Nazwa techniczna jest wymagana
# Tooltip for codeEditor dialog
codeEditorTooltip=Otwórz okno edycji JSON
# Status Api path validation message
validationStatusAPIPath=Wprowadź poprawną ścieżkę API, na przykład: /job/v1/{id}/status
# CSRF token URL validation message
validationCSRFTokenURL=Adres URL tokenu CSRF musi zaczynać się od 'https://' i musi być poprawnym adresem URL
# Select a connection
selectConnection=Wybierz połączenie
# Validation message for connection item error
connectionNotReplicated=Połączenie jest aktualnie niepoprawne do uruchamiania zadań interfejsu API. Aby naprawić to połączenie HTTP, otwórz aplikację "Połączenia" i ponownie wprowadź swoje poświadczenia
# Validation message for API task properties wrong value or missing
VAL_REST_API_PROPERTIES_MISSING=Zadanie "{0}" interfejsu API ma niepoprawne lub brakujące wartości dla następujących właściwości: {1}
# Validation message for API connection api is throwing some error or selected connection has some warning
VAL_REST_API_CONNECTION=W zadaniu "{0}" interfejsu API występuje problem z połączeniem HTTP. Otwórz aplikację "Połączenia" i sprawdź to połączenie
# Validation message for API task properties when delected connection
VAL_REST_API_CONNECTION_DELETED=Zadanie API "{0}" ma usunięte połączenie "{1}"
# Validation message for API task properties when connection cannot be consumed in the API task
VAL_REST_API_CONNECTION_CONSUMPTION_WARNING=Zadanie API "{0}" ma połączenie "{1}", którego nie można użyć do uruchamiania zadań API. Otwórz aplikację "Połączenia" i wprowadź ponownie swoje dane uwierzytelniające, aby nawiązać łączność dla zadań API.
# Information message if synchrnous mode is selected in the API Invocation then we don't need status information
modeInfoText=W trybie synchronicznym panel statusu nie jest wyświetlany dla wywołań interfejsu API
# validation dialog button for Run API Test
saveAnyway=Zapisz mimo to
# validation message for technical name
technicalNameValidation=Nazwa techniczna musi być unikalna w tym łańcuchu zadań. Wybierz inną nazwę techniczną
# Connection error message
getHttpConnectionsError=Nie udało się uzyskać połączeń HTTP
# Msg for the API test run when the Task chain is not saved
taskChainNotSaved=Łańcuch zadań należy zapisać przed uruchomieniem przebiegu testowego interfejsu API
# Msg failed to run API test run
@failedToRunAPI=Uruchamianie przebiegu testowego interfejsu API nie powiodło się
# Msg for the API test run when its already in running state
apiTaskRunning=Przebieg testowy API jest już w toku. Czy chcesz rozpocząć nowy przebieg testowy?

topToBtm=Góra-dół
leftToRight=Lewo-prawo

#XTIT Apache Spark Application Settings
txtHeaderApacheSettings=Ustawienia aplikacji Apache Spark
#XFLD Use Default
txtUseSpaceDefault=Użyj wartości domyślnej
#XFLD Application
txtApplication=Aplikacja
#XFLD Define new settings for this Task
txtNewSettings=Zdefiniuj nowe ustawienia dla tego zadania

#XFLD Use Default
txtUseDefault=Użyj wartości domyślnej




