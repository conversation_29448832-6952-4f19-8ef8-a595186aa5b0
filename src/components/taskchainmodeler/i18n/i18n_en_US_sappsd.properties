#XTOL Undo
@undo=[[[Ůŋƌŏ]]]
#XTOL Redo
@redo=[[[Řēƌŏ]]]
#XTOL Delete Selected Symbol
@deleteNode=[[[Ďēĺēţē Ŝēĺēċţēƌ Ŝŷɱƃŏĺ∙∙∙∙∙]]]
#XTOL Zoom to Fit
@zoomToFit=[[[Żŏŏɱ ţŏ Ƒįţ∙∙∙∙∙∙∙∙]]]
#XTOL Auto Layout
@autoLayout=[[[Āűţŏ Ļąŷŏűţ∙∙∙∙∙∙∙∙]]]
#XMSG
@welcomeText=[[[Ďŗąğ ąŋƌ ƌŗŏρ ŏƃĵēċţş ƒŗŏɱ ţĥē ĺēƒţ ρąŋēĺ ţŏ ţĥįş ċąŋʋąş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
@txtNoData=[[[Ĭţ ĺŏŏķş ĺįķē ŷŏű ĥąʋēŋ’ţ ąƌƌēƌ ąŋŷ ŏƃĵēċţ ŷēţ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
VAL_ENTER_VALID_STRING_GEN=[[[Ƥĺēąşē ēŋţēŗ ą ʋąĺįƌ şţŗįŋğ ŏƒ ĺēŋğţĥ ĺēşş ţĥąŋ ēƣűąĺ ţŏ {0}.]]]
#XMSG
@noParametersMsg=[[[Ţĥįş ρŗŏċēƌűŗē ĥąş ŋŏ įŋρűţ ρąŗąɱēţēŗş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
@ip_enterValueMsg=[[[Ţĥē "{0}" (Řűŋ ŜǬĻ Ŝċŗįρţ Ƥŗŏċēƌűŗē) ĥąş įŋρűţ ρąŗąɱēţēŗş. Ŷŏű ċąŋ şēţ ą ʋąĺűē ŏƒ ēąċĥ ŏƒ ţĥēɱ.]]]
#XTOL
@validateModel=[[[Ʋąĺįƌąţįŏŋ Μēşşąğēş∙∙∙∙∙]]]
#XTOL
@hierarchy=[[[Ĥįēŗąŗċĥŷ∙∙∙∙∙]]]
#XTOL
@columnCount=[[[Ńűɱƃēŗ ŏƒ Ĉŏĺűɱŋş∙∙∙∙∙∙∙]]]
#XFLD
@yes=[[[Ŷēş∙]]]
#XFLD
@no=[[[Ńŏ∙∙]]]
#XTIT Save Dialog param
@modelNameTaskChain=[[[Ţąşķ ċĥąįŋ∙∙∙∙]]]
#properties panel
@lblPropertyTitle=[[[Ƥŗŏρēŗţįēş∙∙∙∙]]]
#XFLD
@lblGeneral=[[[Ģēŋēŗąĺ∙∙∙∙∙∙∙]]]
#XFLD : Setting
@lblSetting=[[[Ŝēţţįŋğş∙∙∙∙∙∙]]]
#XTXT: Delta Enabled Local Table Deletion
txtDeltaTableDeletion=[[[Ďēĺēţē ąĺĺ ƒűĺĺŷ-ρŗŏċēşşēƌ ŗēċŏŗƌş ŵįţĥ Ĉĥąŋğē Ţŷρē 'Ďēĺēţēƌ' ţĥąţ ąŗē ŏĺƌēŗ ţĥąŋ∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XTXT: Delta Enabled Local Table Deletion Input field description
desDeltaTableDeletion=[[[Ďąŷş]]]
#XFLD: Data Activation label
@lblDataActivation=[[[Ďąţą Āċţįʋąţįŏŋ∙∙∙∙]]]
#XFLD: Latency label
@latency=[[[Ļąţēŋċŷ∙∙∙∙∙∙∙]]]
#XTEXT: Text for Latency dropdown
txtLatencyDefault=[[[Ďēƒąűĺţ∙∙∙∙∙∙∙]]]
#XTEXT: Text 1 hour
txtOneHour=[[[1 Ĥŏűŗ∙∙∙∙∙∙∙∙]]]
#XTEXT: Text 2 hours
txtTwoHours=[[[2 Ĥŏűŗş∙∙∙∙∙∙∙]]]
#XTEXT: Text 3 hours
txtThreeHours=[[[3 Ĥŏűŗş∙∙∙∙∙∙∙]]]
#XTEXT: Text 4 hours
txtFourHours=[[[4 Ĥŏűŗş∙∙∙∙∙∙∙]]]
#XTEXT: Text 6 hours
txtSixHours=[[[6 Ĥŏűŗş∙∙∙∙∙∙∙]]]
#XTEXT: Text 12 hours
txtTwelveHours=[[[12 Ĥŏűŗş∙∙∙∙∙∙]]]
#XTEXT: Text 1 day
txtOneDay=[[[1 Ďąŷ∙∙∙∙∙∙∙∙∙]]]
#XFLD: Latency label
@autoRestartHead=[[[Āűţŏɱąţįċ Řēşţąŗţ∙∙∙∙∙∙∙]]]
#XFLD
@lblConnectionName=[[[Ĉŏŋŋēċţįŏŋ∙∙∙∙]]]
#XFLD
@lblQualifiedName=[[[Ǭűąĺįƒįēƌ Ńąɱē∙∙∙∙∙]]]
#XFLD
@lblSpaceName=[[[Ŝρąċē Ńąɱē∙∙∙∙]]]
#XFLD
@lblLocalSchemaName=[[[Ļŏċąĺ Ŝċĥēɱą∙∙∙∙∙∙∙]]]
#XFLD
@lblType=[[[Ŏƃĵēċţ Ţŷρē∙∙∙∙∙∙∙∙]]]
#XFLD
@lblActivity=[[[Āċţįʋįţŷ∙∙∙∙∙∙]]]
#XFLD
@lblTableName=[[[Ţąƃĺē Ńąɱē∙∙∙∙]]]
#XFLD
@lblBusinessName=[[[Ɓűşįŋēşş Ńąɱē∙∙∙∙∙∙]]]
#XFLD
@lblTechnicalName=[[[Ţēċĥŋįċąĺ Ńąɱē∙∙∙∙∙]]]
#XFLD
@lblSpace=[[[Ŝρąċē∙∙∙∙∙∙∙∙∙]]]
#XFLD
@lblLabel=[[[Ļąƃēĺ∙∙∙∙∙∙∙∙∙]]]
#XFLD
@lblDataType=[[[Ďąţą Ţŷρē∙∙∙∙∙]]]
#XFLD
@lblDescription=[[[Ďēşċŗįρţįŏŋ∙∙∙∙∙∙∙∙]]]
#XFLD
@lblStorageType=[[[Ŝţŏŗąğē∙∙∙∙∙∙∙]]]
#XFLD
@lblHTTPConnection=[[[Ģēŋēŗįċ ĤŢŢƤ Ĉŏŋŋēċţįŏŋ∙∙∙∙∙∙]]]
#XFLD
@lblAPISettings=[[[Ģēŋēŗįċ ĀƤĬ Ŝēţţįŋğş∙∙∙∙]]]
#XFLD
@header=[[[Ĥēąƌēŗş∙∙∙∙∙∙∙]]]
#XFLD
@lblInvoke=[[[ĀƤĬ Ĭŋʋŏċąţįŏŋ∙∙∙∙∙]]]
#XFLD
@lblMethod=[[[Μēţĥŏƌ∙∙∙∙∙∙∙∙]]]
#XFLD
@lblUrl=[[[Ɓąşē ŮŘĻ∙∙∙∙∙∙]]]
#XFLD
@lblAPIPath=[[[ĀƤĬ Ƥąţĥ∙∙∙∙∙∙]]]
#XFLD
@lblMode=[[[Μŏƌē]]]
#XFLD
@lblCSRFToken=[[[Řēƣűįŗē ĈŜŘƑ Ţŏķēŋ∙∙∙∙∙∙]]]
#XFLD
@lblTokenURL=[[[ĈŜŘƑ Ţŏķēŋ ŮŘĻ∙∙∙∙∙]]]
#XFLD
@csrfTokenInfoText=[[[Ĭƒ ŋŏţ ēŋţēŗēƌ, ţĥē Ɓąşē ŮŘĻ ąŋƌ ţĥē ĀƤĬ ρąţĥ ŵįĺĺ ƃē űşēƌ∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD
@lblCSRF=[[[ĈŜŘƑ Ţŏķēŋ∙∙∙∙]]]
#XFLD
@lblRequestBody=[[[Řēƣűēşţ Ɓŏƌŷ∙∙∙∙∙∙∙]]]
#XFLD
@lblFormat=[[[Ƒŏŗɱąţ∙∙∙∙∙∙∙∙]]]
#XFLD
@lblResponse=[[[Řēşρŏŋşē∙∙∙∙∙∙]]]
#XFLD
@lblId=[[[ĬĎ ţŏ ŗēţŗįēʋē şţąţűş∙∙∙∙∙]]]
#XFLD
@Id=[[[ĬĎ∙∙]]]
#XFLD
@lblSuccessIndicator=[[[Ŝűċċēşş Ĭŋƌįċąţŏŗ∙∙∙∙∙∙∙]]]
#XFLD
@lblErrorIndicator=[[[Ĕŗŗŏŗ Ĭŋƌįċąţŏŗ∙∙∙∙]]]
#XFLD
@lblErrorReason=[[[Řēąşŏŋ ƒŏŗ Ĕŗŗŏŗ∙∙∙∙∙∙∙∙]]]
#XFLD
@lblStatus=[[[Ŝţąţűş∙∙∙∙∙∙∙∙]]]
#XFLD
@lblApiTestRun=[[[ĀƤĬ Ţēşţ Řűŋ∙∙∙∙∙∙∙]]]
#XFLD
@lblRunStatus=[[[Řűŋ Ŝţąţűş∙∙∙∙]]]
#XFLD
@lblLastRan=[[[Ļąşţ Řąŋ Ŏŋ∙∙∙∙∙∙∙∙]]]
#XFLD
@lblTestRun=[[[Ţēşţ Řűŋ∙∙∙∙∙∙]]]
#XFLD
@lblDefaultHeader=[[[Ďēƒąűĺţ Ĥēąƌēŗ Ƒįēĺƌş (Ķēŷ-Ʋąĺűē ρąįŗş)∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD
@lblAdditionalHeader=[[[Āƌƌįţįŏŋąĺ Ĥēąƌēŗ Ƒįēĺƌ∙∙∙∙∙∙]]]
#XFLD
@lblKey=[[[Ķēŷ∙]]]
#XFLD
@lblEditJSON=[[[Ĕƌįţ ĴŜŎŃ∙∙∙∙∙]]]
#XFLD
@lblTasks=[[[Ţąşķş∙∙∙∙∙∙∙∙∙]]]
#XFLD
@lblRESTfullTask=[[[ĀƤĬ∙]]]
#XFLD: add field button text
btnAddField=[[[Āƌƌ Ĥēąƌēŗ Ƒįēĺƌ∙∙∙∙∙∙∙∙]]]
#XFLD: view Details link
@viewDetails=[[[Ʋįēŵ Ďēţąįĺş∙∙∙∙∙∙∙]]]
#XTOL
tooltipTxt=[[[Μŏŗē]]]
#XTOL
delete=[[[Ďēĺēţē∙∙∙∙∙∙∙∙]]]
#XBTN: ok button text
btnOk=[[[Ŏķ∙∙]]]
#XBTN: cancel button text
btnCancel=[[[Ĉąŋċēĺ∙∙∙∙∙∙∙∙]]]
#XBTN: save button text
btnSave=[[[Ŝąʋē]]]
#XMSG Error: Object has duplicated technical name.
VAL_OBJECT_DUPLICATE_NAME=[[[Ŏƃĵēċţ "{0}" ąĺŗēąƌŷ ēχįşţş įŋ ţĥē ŗēρŏşįţŏŗŷ. Ƥĺēąşē ēŋţēŗ ąŋŏţĥēŗ ŋąɱē.]]]
#XMSG: loading message while opening task chain
loadTaskChain=[[[Ļŏąƌįŋğ ţĥē ţąşķ ċĥąįŋ...∙∙∙∙∙∙∙]]]
#model properties
#XFLD
@status_panel=[[[Řűŋ Ŝţąţűş∙∙∙∙]]]
#XFLD
@deploy_status_panel=[[[Ďēρĺŏŷ Ŝţąţűş∙∙∙∙∙∙]]]
#XFLD
@status_lbl=[[[Ŝţąţűş∙∙∙∙∙∙∙∙]]]
#XFLD
@lblLastExecuted=[[[Ļąşţ Řűŋ∙∙∙∙∙∙]]]
#XFLD
@lblNotExecuted=[[[Ńŏţ Řűŋ Ŷēţ∙∙∙∙∙∙∙∙]]]
#XFLD
@lblNotDeployed=[[[Ńŏţ Ďēρĺŏŷēƌ∙∙∙∙∙∙∙]]]
#XFLD
errorDetailsTxt=[[[Ĉŏűĺƌŋ’ţ ƒēţċĥ ŗűŋ şţąţűş∙∙∙∙∙∙∙]]]
#XBTN: Schedule dropdown menu
SCHEDULE=[[[Ŝċĥēƌűĺē∙∙∙∙∙∙]]]
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=[[[Ĕƌįţ Ŝċĥēƌűĺē∙∙∙∙∙∙]]]
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=[[[Ďēĺēţē Ŝċĥēƌűĺē∙∙∙∙]]]
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=[[[Ĉŗēąţē Ŝċĥēƌűĺē∙∙∙∙]]]
#XLNK
viewDetails=[[[Ʋįēŵ Ďēţąįĺş∙∙∙∙∙∙∙]]]
#XMSG: error message for reading execution status from backend
backendErrorMsg=[[[Ĭţ ĺŏŏķş ĺįķē ţĥē ƌąţą įşŋ’ţ ĺŏąƌįŋğ ƒŗŏɱ ţĥē şēŗʋēŗ ąţ ţĥē ɱŏɱēŋţ. Ţŗŷ ƒēţċĥįŋğ ţĥē ƌąţą ąğąįŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Status text for Completed
@statusCompleted=[[[Ĉŏɱρĺēţēƌ∙∙∙∙∙]]]
#XFLD: Status text for Running
@statusRunning=[[[Řűŋŋįŋğ∙∙∙∙∙∙∙]]]
#XFLD: Status text for Failed
@statusFailed=[[[Ƒąįĺēƌ∙∙∙∙∙∙∙∙]]]
#XFLD: Status text for Stopped
@statusStopped=[[[Ŝţŏρρēƌ∙∙∙∙∙∙∙]]]
#XFLD: Status text for Stopping
@statusStopping=[[[Ŝţŏρρįŋğ∙∙∙∙∙∙]]]
#XFLD
@LoaderTitle=[[[Ļŏąƌįŋğ∙∙∙∙∙∙∙]]]
#XFLD
#XFLD: Deploy status, dataflow has  been deployed / deployment date exists
@deployStatusActive=[[[Ďēρĺŏŷēƌ∙∙∙∙∙∙]]]
@deployStatusRevised=[[[Ļŏċąĺ Ůρƌąţēş∙∙∙∙∙∙]]]
@deployStatusFailed=[[[Ƒąįĺēƌ∙∙∙∙∙∙∙∙]]]
@deployStatusPending=[[[Ďēρĺŏŷįŋğ...∙∙∙∙∙∙∙]]]
@LoaderText=[[[Ƒēţċĥįŋğ ƌēţąįĺş ƒŗŏɱ ţĥē şēŗʋēŗ∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
@msgDetailFetchError=[[[Ĕŗŗŏŗ ŵĥįĺē ƒēţċĥįŋğ ƌēţąįĺş ƒŗŏɱ ţĥē şēŗʋēŗ∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD
@executeError=[[[Ĕŗŗŏŗ∙∙∙∙∙∙∙∙∙]]]
#XFLD
@executeWarning=[[[Ŵąŗŋįŋğ∙∙∙∙∙∙∙]]]
#XMSG
@executeConfirmDialog=[[[Ĭŋƒŏ]]]
#XMSG
@executeunsavederror=[[[Ŝąʋē ŷŏűŗ ţąşķ ċĥąįŋ ƃēƒŏŗē ŗűŋŋįŋğ įţ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
@executemodifiederror=[[[Ţĥēŗē ąŗē űŋşąʋēƌ ċĥąŋğēş įŋ ţĥē ţąşķ ċĥąįŋ. Ƥĺēąşē şąʋē įţ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
@executerunningerror=[[[Ţĥē ţąşķ ċĥąįŋ įş ċűŗŗēŋţĺŷ ŗűŋŋįŋğ. Ŵąįţ űŋţįĺ ţĥē ċűŗŗēŋţ ŗűŋ įş ċŏɱρĺēţēƌ ƃēƒŏŗē şţąŗţįŋğ ŵįţĥ ą ŋēŵ ŏŋē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
@btnExecuteAnyway=[[[Řűŋ Āŋŷŵąŷ∙∙∙∙]]]
#XMSG
@msgExecuteWithValidations=[[[Ţĥē ţąşķ ċĥąįŋ ĥąş ʋąĺįƌąţįŏŋ ēŗŗŏŗş. Řűŋŋįŋğ ţĥē ţąşķ ċĥąįŋ ɱąŷ ŗēşűĺţ įŋ ƒąįĺűŗē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
@msgRunDeployedVersion=[[[Ţĥēŗē ąŗē ċĥąŋğēş ţŏ ƌēρĺŏŷ. Ţĥē ĺąşţ ƌēρĺŏŷēƌ ʋēŗşįŏŋ ŏƒ ţĥē ţąşķ ċĥąįŋ ŵįĺĺ ƃē ŗűŋ. Ďŏ ŷŏű ŵąŋţ ţŏ ċŏŋţįŋűē?∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
#XMSG
@navToMonitoring=[[[Ŏρēŋ įŋ Ţąşķ Ĉĥąįŋ Μŏŋįţŏŗ∙∙∙∙∙∙∙]]]
#XMSG
txtOR=[[[ŎŘ∙∙]]]
#XFLD
@preview=[[[Ƥŗēʋįēŵ∙∙∙∙∙∙∙]]]
#XMSG
txtand=[[[ąŋƌ∙]]]
#NOTR
@dateFormat=yyyy-MM-dd
#NOTR
@dateTimeFormatFilter=yyyy-MM-dd HH:mm:ss.
#NOTR
@timeFormat=HH:mm:ss
@lblColum=[[[Ĉŏĺűɱŋ∙∙∙∙∙∙∙∙]]]
#XFLD
@lblCondition=[[[Ĉŏŋƌįţįŏŋ∙∙∙∙∙]]]
#XFLD
@lblValue=[[[Ʋąĺűē∙∙∙∙∙∙∙∙∙]]]
#XMSG
@msgJsonInvalid=[[[Ţĥē ţąşķ ċĥąįŋ ċŏűĺƌŋ’ţ ƃē şąʋēƌ ąş ţĥēŗē ąŗē ēŗŗŏŗş įŋ ţĥē ĴŜŎŃ. Ƥĺēąşē ċĥēċķ ąŋƌ ŗēşŏĺʋē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XTIT
@msgSaveFailTitle=[[[Ĭŋʋąĺįƌ ĴŜŎŃ.∙∙∙∙∙∙]]]
#XMSG
NOT_CHAINABLE=[[[Ŏƃĵēċţ "{0}" ċąŋ’ţ ƃē ąƌƌēƌ ţŏ ţĥē ţąşķ ċĥąįŋ.]]]
#XMSG
NOT_CHAINABLE_REMOTETABLE=[[[Ŏƃĵēċţ "{0}": ŗēρĺįċąţįŏŋ ţŷρē ŵįĺĺ ƃē ċĥąŋğēƌ.]]]
#XMSG
searchTaskChain=[[[Ŝēąŗċĥ Ŏƃĵēċţş∙∙∙∙∙]]]

#XFLD
@txtTaskChain=[[[Ţąşķ Ĉĥąįŋ∙∙∙∙]]]
#XFLD
@txtRemoteTable=[[[Řēɱŏţē Ţąƃĺē∙∙∙∙∙∙∙]]]
#XFLD
@txtRemoveData=[[[Řēɱŏʋē Řēρĺįċąţēƌ ƌąţą∙∙∙∙∙]]]
#XFLD
@txtRemovePersist=[[[Řēɱŏʋē Ƥēŗşįşţēƌ Ďąţą∙∙∙∙∙]]]
#XFLD
@txtView=[[[Ʋįēŵ]]]
#XFLD
@txtDataFlow=[[[Ďąţą Ƒĺŏŵ∙∙∙∙∙]]]
#XFLD
@txtIL=[[[Ĭŋţēĺĺįğēŋţ Ļŏŏķűρ∙∙∙∙∙∙]]]
#XFLD
@txtTransformationFlow=[[[Ţŗąŋşƒŏŗɱąţįŏŋ Ƒĺŏŵ∙∙∙∙∙]]]
#XFLD
@txtReplicationFlow=[[[Řēρĺįċąţįŏŋ Ƒĺŏŵ∙∙∙∙∙∙∙∙]]]
#XFLD
@txtDeltaLocalTable=[[[Ļŏċąĺ Ţąƃĺē∙∙∙∙∙∙∙∙]]]
#XFLD
@txtBWProcessChain=[[[ƁŴ Ƥŗŏċēşş Ĉĥąįŋ∙∙∙∙∙∙∙∙]]]
#XFLD
@txtSQLScriptProcedure=[[[ŜǬĻ Ŝċŗįρţ Ƥŗŏċēƌűŗē∙∙∙∙]]]
#XFLD
@txtAPI=[[[ĀƤĬ∙]]]
#XFLD
@txtMerge=[[[Μēŗğē∙∙∙∙∙∙∙∙∙]]]
#XFLD
@txtOptimize=[[[Ŏρţįɱįžē∙∙∙∙∙∙]]]
#XFLD
@txtVacuum=[[[Ďēĺēţē Řēċŏŗƌş∙∙∙∙∙]]]

#XFLD
@txtRun=[[[Řűŋ∙]]]
#XFLD
@txtPersist=[[[Ƥēŗşįşţ∙∙∙∙∙∙∙]]]
#XFLD
@txtReplicate=[[[Řēρĺįċąţē∙∙∙∙∙]]]
#XFLD
@txtDelete=[[[Ďēĺēţē Řēċŏŗƌş ŵįţĥ Ĉĥąŋğē Ţŷρē 'Ďēĺēţēƌ'∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD
@txtRunTC=[[[Řűŋ Ţąşķ Ĉĥąįŋ∙∙∙∙∙]]]
#XFLD
@txtRunBW=[[[Řűŋ ƁŴ Ƥŗŏċēşş Ĉĥąįŋ∙∙∙∙]]]
#XFLD
@txtRunSQLScriptProcedure=[[[Řűŋ ŜǬĻ Ŝċŗįρţ Ƥŗŏċēƌűŗē∙∙∙∙∙∙]]]

#XFLD
@txtRunDataFlow=[[[Řűŋ Ďąţą Ƒĺŏŵ∙∙∙∙∙∙]]]
#XFLD
@txtPersistView=[[[Ƥēŗşįşţ Ʋįēŵ∙∙∙∙∙∙∙]]]
#XFLD
@txtReplicateTable=[[[Řēρĺįċąţē Ţąƃĺē∙∙∙∙]]]
#XFLD
@txtRunIL=[[[Řűŋ Ĭŋţēĺĺįğēŋţ Ļŏŏķűρ∙∙∙∙∙]]]
#XFLD
@txtRunTF=[[[Řűŋ Ţŗąŋşƒŏŗɱąţįŏŋ Ƒĺŏŵ∙∙∙∙∙∙]]]
#XFLD
@txtRunRF=[[[Řűŋ Řēρĺįċąţįŏŋ Ƒĺŏŵ∙∙∙∙]]]
#XFLD
@txtRemoveReplicatedData=[[[Řēɱŏʋē Řēρĺįċąţēƌ ƌąţą∙∙∙∙∙]]]
#XFLD
@txtRemovePersistedData=[[[Řēɱŏʋē Ƥēŗşįşţēƌ Ďąţą∙∙∙∙∙]]]
#XFLD
@txtMergeData=[[[Μēŗğē∙∙∙∙∙∙∙∙∙]]]
#XFLD
@txtOptimizeData=[[[Ŏρţįɱįžē∙∙∙∙∙∙]]]
#XFLD
@txtVacuumData=[[[Ďēĺēţē Řēċŏŗƌş∙∙∙∙∙]]]
#XFLD
@txtRunAPI=[[[Řűŋ ĀƤĬ∙∙∙∙∙∙∙]]]

#XFLD storage type text
hdlfStorage=[[[Ƒįĺē]]]

@statusNew=[[[Ńŏţ Ďēρĺŏŷēƌ∙∙∙∙∙∙∙]]]
@statusActive=[[[Ďēρĺŏŷēƌ∙∙∙∙∙∙]]]
@statusRevised=[[[Ļŏċąĺ Ůρƌąţēş∙∙∙∙∙∙]]]
@statusPending=[[[Ďēρĺŏŷįŋğ...∙∙∙∙∙∙∙]]]
@statusChangesToDeploy=[[[Ĉĥąŋğēş Ţŏ Ďēρĺŏŷ∙∙∙∙∙∙∙]]]
@statusDesignTimeError=[[[Ďēşįğŋ Ţįɱē Ĕŗŗŏŗ∙∙∙∙∙∙∙]]]
@statusRunTimeError=[[[Řűŋ Ţįɱē Ĕŗŗŏŗ∙∙∙∙∙]]]

#XTIT
txtNodes=[[[Ŏƃĵēċţş įŋ Ţąşķ Ĉĥąįŋ ({0})]]]
#XBTN
@deleteNodes=[[[Ďēĺēţē∙∙∙∙∙∙∙∙]]]

#XMSG
@txtDropDataToDiagram=[[[Ďŗąğ ąŋƌ ƌŗŏρ ŏƃĵēċţş ţŏ ţĥē ƌįąğŗąɱ.∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
@noData=[[[Ńŏ ŏƃĵēċţş∙∙∙∙]]]

#XFLD
@txtTaskPosition=[[[Ţąşķ Ƥŏşįţįŏŋ∙∙∙∙∙∙]]]

#input parameters and variables
#XFLD
lblInputParameters=[[[Ĭŋρűţ Ƥąŗąɱēţēŗş∙∙∙∙∙∙∙∙]]]
#XFLD
ip_name=[[[Ńąɱē]]]
#XFLD
ip_value=[[[Ʋąĺűē∙∙∙∙∙∙∙∙∙]]]
#XTEXT
@noObjectsFound=[[[Ńŏ Ŏƃĵēċţş Ƒŏűŋƌ∙∙∙∙∙∙∙∙]]]

#XMSG
@msgExecuteSuccess=[[[Ţąşķ ċĥąįŋ ŗűŋ ĥąş şţąŗţēƌ.∙∙∙∙∙∙∙∙]]]
#XMSG
@msgExecuteFail=[[[Ƒąįĺēƌ ţŏ ŗűŋ ţĥē ţąşķ ċĥąįŋ.∙∙∙∙∙∙∙∙∙]]]
#XMSG
@msgDeployAndRunSuccess=[[[Ţąşķ ċĥąįŋ ƌēρĺŏŷɱēŋţ ąŋƌ ŗűŋ ĥąş şţąŗţēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
@msgDeployAndRunFail=[[[Ƒąįĺēƌ ţŏ ƌēρĺŏŷ ąŋƌ ŗűŋ ţĥē ţąşķ ċĥąįŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
@titleExecuteBusy=[[[Ƥĺēąşē ŵąįţ.∙∙∙∙∙∙∙]]]
#XMSG
@msgExecuteBusy=[[[Ŵē ąŗē ρŗēρąŗįŋğ ŷŏűŗ ƌąţą ţŏ şţąŗţ ŗűŋŋįŋğ ţĥē ţąşķ ċĥąįŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
@msgAPITestRunSuccess=[[[ĀƤĬ ţēşţ ŗűŋ ĥąş şţąŗţēƌ.∙∙∙∙∙∙∙]]]
#XMSG
@msgAPIExecuteBusy=[[[Ŵē ąŗē ρŗēρąŗįŋğ ŷŏűŗ ƌąţą ţŏ şţąŗţ ŗűŋŋįŋğ ţĥē ĀƤĬ ţēşţ ŗűŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]

#XTOL
txtOpenInEditor=[[[Ŏρēŋ įŋ Ĕƌįţŏŗ∙∙∙∙∙]]]
#XTOL
txtPreviewData=[[[Ƥŗēʋįēŵ Ďąţą∙∙∙∙∙∙∙]]]

#datapreview
#XMSG
@msgDataPreviewNotSupp=[[[Ďąţą ρŗēʋįēŵ įş ŋŏţ ąʋąįĺąƃĺē ƒŏŗ ţĥįş ŏƃĵēċţ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]

#XMSG Error: empty model
VAL_MODEL_EMPTY=[[[Ŷŏűŗ ɱŏƌēĺ şēēɱş ţŏ ƃē ēɱρţŷ. Ƥĺēąşē ąƌƌ şŏɱē ŏƃĵēċţş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG Error: deploy model
@msgDeployBeforeRun=[[[Ŷŏű ŋēēƌ ţŏ ƌēρĺŏŷ ţĥē ţąşķ ċĥąįŋ ƃēƒŏŗē ŗűŋŋįŋğ įţ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#BTN: close dialog
btnClose=[[[Ĉĺŏşē∙∙∙∙∙∙∙∙∙]]]

#XMSG Object is not Deployed
VAL_NOT_DEPLOYED=[[[Ŏƃĵēċţ "{0}" ɱűşţ ƃē ƌēρĺŏŷēƌ ţŏ ċŏŋţįŋűē ŵįţĥ ţĥē ţąşķ ċĥąįŋ.]]]
#XMSG Object is having runtime error
VAL_RUNTIME_ERROR=[[[Ŏƃĵēċţ "{0}" ŗēţűŗŋş ą ŗűŋ ţįɱē ēŗŗŏŗ. Ƥĺēąşē ċĥēċķ ţĥē ŏƃĵēċţ.]]]
#XMSG Object is having design time error
VAL_DESIGNTIME_ERROR=[[[Ŏƃĵēċţ "{0}" ŗēţűŗŋş ą ƌēşįğŋ ţįɱē ēŗŗŏŗ. Ƥĺēąşē ċĥēċķ ţĥē ŏƃĵēċţ.]]]
#XMSG Procedure Object has been deleted
VAL_DELETED_PROCEDURE=[[[Ţĥē ƒŏĺĺŏŵįŋğ ρŗŏċēƌűŗēş  ĥąʋē ƃēēŋ ƌēĺēţēƌ: {0}]]]
#XMSG New Parameter added to Procedure object
VAL_NEW_PARAMETER_ADDED=[[[Ńēŵ ρąŗąɱēţēŗş ąƌƌēƌ ţŏ ρŗŏċēƌűŗē "{1}": "{0}". Ƥĺēąşē ŗēƌēρĺŏŷ ţĥē ţąşķ ċĥąįŋ.]]]
#XMSG Parameter removed from Procedure object
VAL_PARAMETER_DELETED=[[[Ƥąŗąɱēţēŗş ŗēɱŏʋēƌ ƒŗŏɱ ρŗŏċēƌűŗē "{1}": "{0}". Ƥĺēąşē ŗēƌēρĺŏŷ ţĥē ţąşķ ċĥąįŋ.]]]
#XMSG Parameter data type changed in Procedure object
VAL_PARAMETER_DATATYPE_MODIFIED=[[[Ƥąŗąɱēţēŗ "{0}" ƌąţą ţŷρē ċĥąŋğēƌ ƒŗŏɱ "{1}" ţŏ "{2}" įŋ ρŗŏċēƌűŗē "{3}".]]]
#XMSG Parameter length changed of specific datatype in Procedure object
VAL_PARAMETER_LENGTH_MODIFIED=[[[Ƥąŗąɱēţēŗ "{0}" ĺēŋğţĥ ċĥąŋğēƌ ƒŗŏɱ "{1}" ţŏ "{2}" įŋ ρŗŏċēƌűŗē "{3}".]]]
#XMSG Parameter precision changed of Decimal datatype in Procedure object
VAL_PARAMETER_PRECISION_MODIFIED=[[[Ƥąŗąɱēţēŗ "{0}" ρŗēċįşįŏŋ ċĥąŋğēƌ ƒŗŏɱ "{1}" ţŏ "{2}" įŋ ρŗŏċēƌűŗē "{3}".]]]
#XMSG Parameter scale changed of Decimal datatype in Procedure object
VAL_PARAMETER_SCALE_MODIFIED=[[[Ƥąŗąɱēţēŗ "{0}" şċąĺē ċĥąŋğēƌ ƒŗŏɱ "{1}" ţŏ "{2}" įŋ ρŗŏċēƌűŗē "{3}".]]]
#XMSG Parameter value not provided in Procedure object
VAL_SQL_SCRIPT_PROCEDURE_PARAMETER_VALUE_MISSING=[[[Ŷŏű ĥąʋē ŋŏţ ēŋţēŗēƌ ʋąĺűēş ƒŏŗ įŋρűţ ρąŗąɱēţēŗş ţĥąţ ąŗē ŗēƣűįŗēƌ ƒŏŗ ρŗŏċēƌűŗē "{0}": "{1}"]]]
#XMSG Remote Table activity changes to Remove Replicated Data
VAL_REMOVE_REPLICATED_DATA=[[[Ţĥē ŋēχţ ŗűŋ ŏƒ ą ţąşķ ċĥąįŋ ŵįĺĺ ċĥąŋğē ţĥē ƌąţą ąċċēşş ţŷρē, ąŋƌ ƌąţą ŵįĺĺ ŋŏ ĺŏŋğēŗ ƃē űρĺŏąƌēƌ įŋ ŗēąĺ-ţįɱē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG Remote Table activity changes
REPLICATION_TYPE_CHANGED=[[[Ŏƃĵēċţ "{0}": Řēρĺįċąţįŏŋ ţŷρē ŵįĺĺ ƃē ċĥąŋğēƌ.]]]

#XFLD
@lblStartNode=[[[Ŝţąŗţ Ńŏƌē∙∙∙∙]]]
#XFLD
@lblEndNode=[[[Ĕŋƌ Ńŏƌē∙∙∙∙∙∙]]]
#XFLD
@linkTo=[[[{0} ţŏ {1}]]]
#XTOL
@txtViewDetails=[[[Ʋįēŵ Ďēţąįĺş∙∙∙∙∙∙∙]]]

#XTOL
txtOpenImpactLineage=[[[Ĭɱρąċţ ąŋƌ Ļįŋēąğē Āŋąĺŷşįş∙∙∙∙∙∙∙∙]]]
#XFLD
@emailNotifications=[[[Ĕɱąįĺ Ńŏţįƒįċąţįŏŋş∙∙∙∙∙]]]
#XFLD
@txtReset=[[[Řēşēţ∙∙∙∙∙∙∙∙∙]]]
#XFLD
@emailMsgWarning=[[[Ďēƒąűĺţ ţēɱρĺąţē ēɱąįĺ ŵįĺĺ ƃē şēŋţ ŵĥēŋ ţĥē ēɱąįĺ ɱēşşąğē įş ēɱρţŷ∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD
@notificationSettings=[[[Ńŏţįƒįċąţįŏŋ Ŝēţţįŋğş∙∙∙∙∙]]]
#XFLD
@recipientEmailAddr=[[[Řēċįρįēŋţ Ĕɱąįĺ Āƌƌŗēşş∙∙∙∙∙∙]]]
#XFLD
@emailSubject=[[[Ĕɱąįĺ Ŝűƃĵēċţ∙∙∙∙∙∙]]]
@emailSubjectText=[[[Ţąşķ ċĥąįŋ <ŢĀŜĶĈĤĀĬŃ_ŃĀΜĔ> ċŏɱρĺēţēƌ ŵįţĥ şţąţűş <ŜŢĀŢŮŜ>∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD
@emailMessage=[[[Ĕɱąįĺ Μēşşąğē∙∙∙∙∙∙]]]
@emailMessageText=[[[Ďēąŗ Ůşēŗ,\\u014B\\u014B Ţĥįş įş ţŏ ŋŏţįƒŷ ŷŏű ţĥąţ ţąşķ ċĥąįŋ:<ŢĀŜĶĈĤĀĬŃ_ŃĀΜĔ> ŗűŋ ąţ <ŜŢĀŘŢ_ŢĬΜĔ> ĥąş ƒįŋįşĥēƌ ŵįţĥ şţąţűş <ŜŢĀŢŮŜ>. Ţĥē ēχēċűţįŏŋ ēŋƌēƌ ąţ <ĔŃĎ_ŢĬΜĔ>.\\u014B\\u014BĎēţąįĺş:\\u014BŜρąċē:<ŜƤĀĈĔ_ŃĀΜĔ>\\u014BĔŗŗŏŗ:<ĔŘŘŎŘ_ΜĔŜŜĀĢĔ>∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
@selectEmailRecipientDialogTitle=[[[Ŝēĺēċţ Řēċįρįēŋţ Ĕɱąįĺ Āƌƌŗēşş∙∙∙∙∙∙∙∙∙]]]
@tenantMembers=[[[Ţēŋąŋţ ɱēɱƃēŗş∙∙∙∙∙]]]
@others=[[[Ŏţĥēŗş({0})]]]
@selectedEmailAddress=[[[Ŝēĺēċţēƌ Řēċįρįēŋţş∙∙∙∙∙]]]
@add=[[[Āƌƌ∙]]]
@placeholder=[[[Ƥĺąċēĥŏĺƌēŗ∙∙∙∙∙∙∙∙]]]
@description=[[[Ďēşċŗįρţįŏŋ∙∙∙∙∙∙∙∙]]]
@copyText=[[[Ĉŏρŷ ţēχţ∙∙∙∙∙]]]
@taskchainDetailsPlaceholder=[[[Ƥĺąċēĥŏĺƌēŗş ƒŏŗ ţąşķ ċĥąįŋ ƌēţąįĺş∙∙∙∙∙∙∙∙∙∙∙∙]]]
@placeholderCopied=[[[Ƥĺąċēĥŏĺƌēŗ įş ċŏρįēƌ∙∙∙∙∙]]]
@invalidEmailInfo=[[[Ĕŋţēŗ ċŏŗŗēċţ ēɱąįĺ ąƌƌŗēşş∙∙∙∙∙∙∙∙]]]
@maxMembersAlreadyAdded=[[[Ŷŏű ĥąʋē ąĺŗēąƌŷ ąƌƌēƌ ţĥē ɱąχįɱűɱ ŏƒ 20 ɱēɱƃēŗş∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
@enterEmailAddress=[[[Ĕŋţēŗ űşēŗ ēɱąįĺ ąƌƌŗēşş∙∙∙∙∙∙]]]
@inCorrectPlaceHolder=[[[{0} įş ŋŏţ ąŋ ēχρēċţēƌ ρĺąċēĥŏĺƌēŗ įŋ ţĥē ēɱąįĺ ƃŏƌŷ.]]]
@nsOFF=[[[Ďŏ ŋŏţ şēŋƌ ąŋŷ ŋŏţįƒįċąţįŏŋş∙∙∙∙∙∙∙∙∙]]]
@nsFAILED=[[[Ŝēŋƌ ēɱąįĺ ŋŏţįƒįċąţįŏŋ ŏŋĺŷ ŵĥēŋ ţĥē ŗűŋ ĥąş ċŏɱρĺēţēƌ ŵįţĥ ąŋ ēŗŗŏŗ∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
@nsCOMPLETED=[[[Ŝēŋƌ ēɱąįĺ ŋŏţįƒįċąţįŏŋ ŏŋĺŷ ŵĥēŋ ţĥē ŗűŋ ĥąş ċŏɱρĺēţēƌ şűċċēşşƒűĺĺŷ∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
@nsANY=[[[Ŝēŋƌ ēɱąįĺ ŵĥēŋ ţĥē ŗűŋ ĥąş ċŏɱρĺēţēƌ∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
@phStatus=[[[Ŝţąţűş ŏƒ ţĥē ţąşķ ċĥąįŋ - ŜŮĈĈĔŜŜ|ƑĀĬĻĔĎ∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
@phTaskChainTName=[[[Ţēċĥŋįċąĺ ŋąɱē ŏƒ ţĥē ţąşķ ċĥąįŋ∙∙∙∙∙∙∙∙∙∙]]]
@phTaskChainBName=[[[Ɓűşįŋēşş ŋąɱē ŏƒ ţĥē ţąşķ ċĥąįŋ∙∙∙∙∙∙∙∙∙∙]]]
@phLogId=[[[Ļŏğ ĬĎ ŏƒ ţĥē ŗűŋ∙∙∙∙∙∙∙]]]
@phUser=[[[Ůşēŗ ţĥąţ įş ŗűŋŋįŋğ ţĥē ţąşķ ċĥąįŋ∙∙∙∙∙∙∙∙∙∙∙∙]]]
@phLogUILink=[[[Ļįŋķ ţŏ ţĥē ĺŏğ ƌįşρĺąŷ ŏƒ ţĥē ţąşķ ċĥąįŋ∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
@phStartTime=[[[Ŝţąŗţ ţįɱē ŏƒ ţĥē ŗűŋ∙∙∙∙∙]]]
@phEndTime=[[[Ĕŋƌ ţįɱē ŏƒ ţĥē ŗűŋ∙∙∙∙∙]]]
@phErrMsg=[[[Ƒįŗşţ ēŗŗŏŗ ɱēşşąğē įŋ ţĥē ţąşķ ĺŏğ. Ţĥē ĺŏğ įş ēɱρţŷ įŋ ċąşē ŏƒ ŜŮĈĈĔŜŜ∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
@phSpaceName=[[[Ţēċĥŋįċąĺ ŋąɱē ŏƒ ţĥē şρąċē∙∙∙∙∙∙∙∙]]]
@emailFormatError=[[[Ĭŋʋąĺįƌ ēɱąįĺ ƒŏŗɱąţ∙∙∙∙]]]
@emailFormatErrorInListText=[[[Ĭŋʋąĺįƌ ēɱąįĺ ƒŏŗɱąţ ēŋţēŗēƌ įŋ ŋŏŋ-ţēŋąŋţ ɱēɱƃēŗş ĺįşţ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
@emailSubjectTemplateText=[[[Ńŏţįƒįċąţįŏŋ ƒŏŗ Ţąşķ Ĉĥąįŋ: $$ţąşķĈĥąįŋŃąɱē$$ - Ŝρąċē: $$şρąċēĬƌ$$ - Ŝţąţűş: $$şţąţűş$$∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
@emailMessageTemplateText=[[[Ĥēĺĺŏ,\\u014B\\u014B Ŷŏűŗ ţąşķ ċĥąįŋ ĺąƃēĺēƌ $$ţąşķĈĥąįŋŃąɱē$$ ĥąş ƒįŋįşĥēƌ ŵįţĥ şţąţűş $$şţąţűş$$. \\u014B Ĥēŗē ąŗē şŏɱē ŏţĥēŗ ƌēţąįĺş ąƃŏűţ ţĥē ţąşķ ċĥąįŋ:\\u014B - Ţąşķ ċĥąįŋ ţēċĥŋįċąĺ ŋąɱē: $$ţąşķĈĥąįŋŃąɱē$$ \\u014B - Ļŏğ ĬĎ ŏƒ ţĥē ţąşķ ċĥąįŋ ŗűŋ: $$ĺŏğĬƌ$$ \\u014B - Ůşēŗ ţĥąţ ŗąŋ ţĥē ţąşķ ċĥąįŋ: $$űşēŗ$$ \\u014B - Ļįŋķ ţŏ ţĥē ĺŏğ ƌįşρĺąŷ ŏƒ ţĥē ţąşķ ċĥąįŋ: $$űįĻįŋķ$$ \\u014B - Ŝţąŗţ ţįɱē ŏƒ ţĥē ţąşķ ċĥąįŋ ŗűŋ: $$şţąŗţŢįɱē$$ \\u014B - Ĕŋƌ ţįɱē ŏƒ ţĥē ţąşķ ċĥąįŋ ŗűŋ: $$ēŋƌŢįɱē$$ \\u014B - Ńąɱē ŏƒ ţĥē şρąċē: $$şρąċēĬƌ$$ \\u014B∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
@deleteEmailRecepient=[[[Ďēĺēţē Řēċįρįēŋţ∙∙∙∙∙∙∙∙]]]
@emailInputDisabledText=[[[Ƥĺēąşē ƌēρĺŏŷ ţĥē ţąşķ ċĥąįŋ ţŏ ąƌƌ ēɱąįĺ ŗēċįρįēŋţş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
@tenantOwnerDomainMatchErrorText=[[[Ţĥē ēɱąįĺ ąƌƌŗēşş ƌŏɱąįŋ ƌŏēş ŋŏţ ɱąţċĥ ţĥē ţēŋąŋţ ŏŵŋēŗ ƌŏɱąįŋ: {0}]]]
@totalEmailIdLimitInfoText=[[[Ŷŏű ċąŋ şēĺēċţ űρ ţŏ 20 ēɱąįĺ ŗēċįρįēŋţş įŋċĺűƌįŋğ ţēŋąŋţ ɱēɱƃēŗ űşēŗş ąŋƌ ŏţĥēŗ ŗēċįρįēŋţş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
@emailDomainInfoText=[[[Ŏŋĺŷ ēɱąįĺ ąƌƌŗēşşēş ŵįţĥ ƌŏɱąįŋ: {0} ąŗē ąċċēρţēƌ.]]]
@duplicateEmailErrorText=[[[Ţĥēŗē ąŗē ƌűρĺįċąţē ēɱąįĺ ŗēċįρįēŋţş įŋ ţĥē ĺįşţ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]

#XFLD Zorder Title
@txtZorderTitle=[[[Āρąċĥē Ŝρąŗķ Ż-Ŏŗƌēŗ Ĉŏĺűɱŋş∙∙∙∙∙∙∙∙]]]

#XFLD Zorder NoColumn
@txtZorderNoColumn=[[[Ńŏ Ż-Ŏŗƌēŗ Ĉŏĺűɱŋş Ƒŏűŋƌ∙∙∙∙∙∙]]]

#XFLD Zorder Column PrimaryKey
@txtZorderColumnPrimaryKey=[[[Ƥŗįɱąŗŷ Ķēŷ∙∙∙∙∙∙∙∙]]]

#XFLD
@lblOperators=[[[Ŏρēŗąţŏŗş∙∙∙∙∙]]]
addNewSelector=[[[Āƌƌ ąş Ńēŵ Ţąşķ∙∙∙∙]]]
parallelSelector=[[[Āƌƌ ąş Ƥąŗąĺĺēĺ Ţąşķ∙∙∙∙]]]
replaceSelector=[[[Řēρĺąċē Ĕχįşţįŋğ Ţąşķ∙∙∙∙∙]]]
addparallelbranch=[[[Āƌƌ ąş Ƥąŗąĺĺēĺ Ɓŗąŋċĥ∙∙∙∙∙]]]
addplaceholder=[[[Āƌƌ Ƥĺąċēĥŏĺƌēŗ∙∙∙∙]]]
addALLOperation=[[[ĀĻĻ Ŏρēŗąţŏŗ∙∙∙∙∙∙∙]]]
addOROperation=[[[ĀŃŶ Ŏρēŗąţŏŗ∙∙∙∙∙∙∙]]]
addplaceholdertocanvas=[[[Āƌƌ Ƥĺąċēĥŏĺƌēŗ ţŏ Ĉąŋʋąş∙∙∙∙∙∙∙]]]
addplaceholderonselected=[[[Āƌƌ Ƥĺąċēĥŏĺƌēŗ ąƒţēŗ Ŝēĺēċţēƌ Ţąşķ∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Add Parallel Branch to Selected Task
addparallelbranchonselected=[[[Āƌƌ Ƥąŗąĺĺēĺ Ɓŗąŋċĥ ąƒţēŗ Ŝēĺēċţēƌ Ţąşķ∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
addOperator=[[[Āƌƌ Ŏρēŗąţŏŗ∙∙∙∙∙∙∙]]]
txtAdd=[[[Āƌƌ∙]]]
txtPlaceHolderText=[[[Ďŗąğ & Ďŗŏρ ą Ţąşķ ĥēŗē∙∙∙∙∙∙]]]
@lblLayout=[[[Ļąŷŏűţ∙∙∙∙∙∙∙∙]]]

#XMSG
VAL_UNCONNECTED_TASK=[[[Ţąşķ "{0}" įş ŋŏţ ċŏŋŋēċţēƌ ţŏ ţĥē ţąşķ ċĥąįŋ.]]]
#XMSG
VAL_TASK_SHOULD_HAVE_ONE_INCOMING_LINK=[[[Ţąşķ "{0}" şĥŏűĺƌ ĥąʋē ŏŋĺŷ ŏŋē įŋċŏɱįŋğ ĺįŋķ.]]]
#XMSG
VAL_TASK_SHOULD_HAVE_INCOMING_LINK=[[[Ţąşķ "{0}" şĥŏűĺƌ ĥąʋē ŏŋē įŋċŏɱįŋğ ĺįŋķ.]]]
#XMSG
VAL_UNCONNECTED_OPERATOR=[[[Ŏρēŗąţŏŗ "{0}" įş ŋŏţ ċŏŋŋēċţēƌ ţŏ ţĥē ţąşķ ċĥąįŋ.]]]
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ATLEAST_TWO_INCOMING_LINK=[[[Ŏρēŗąţŏŗ "{0}" şĥŏűĺƌ ĥąʋē ąţ ĺēąşţ ţŵŏ įŋċŏɱįŋğ ĺįŋķş.]]]
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ONE_OUT_LINK=[[[Ŏρēŗąţŏŗ "{0}" şĥŏűĺƌ ĥąʋē ąţ ĺēąşţ ŏŋē ŏűţğŏįŋğ ĺįŋķ.]]]
#XMSG
VAL_CIRCULAR_REFERENCE_EXITS=[[[Ĉįŗċűĺąŗ ĺŏŏρ ēχįşţş įŋ ţĥē ţąşķ ċĥąįŋ "{0}".]]]
#XMSG
VAL_UNCONNECTED_BRANCH=[[[Ŏƃĵēċţ/ƃŗąŋċĥ "{0}" įş ŋŏţ ċŏŋŋēċţēƌ ţŏ ţĥē ţąşķ ċĥąįŋ.]]]
#XMSG
VAL_TASK_TRIGGERED_PARALLEL_MORE_THAN_ONE_TIME=[[[Ţąşķ "{0}" ċŏŋŋēċţēƌ įŋ ρąŗąĺĺēĺ ɱŏŗē ţĥąŋ ŏŋċē. Ƥĺēąşē ŗēɱŏʋē ƌűρĺįċąţēş ţŏ ċŏŋţįŋűē.]]]


txtBegin=[[[Ɓēğįŋ∙∙∙∙∙∙∙∙∙]]]
txtNodesInLink=[[[Ŏƃĵēċţş Ĭŋʋŏĺʋēƌ∙∙∙∙∙∙∙∙]]]
#XTOL Tooltip for a context button on diagram
openInNewTab=[[[Ŏρēŋ įŋ Ńēŵ Ţąƃ∙∙∙∙]]]
#XTOL Tooltip for a context button on diagram
txtConnectToTaskOperator=[[[Ďŗąğ ţŏ Ĉŏŋŋēċţ∙∙∙∙]]]
@emailUpdateError=[[[Ĕŗŗŏŗ įŋ űρƌąţįŋğ Ĕɱąįĺ Ńŏţįƒįċąţįŏŋ ĺįşţ∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]

#XMSG
noTeamPrivilegeTxt=[[[Ŷŏű ƌŏ ŋŏţ ĥąʋē ρēŗɱįşşįŏŋ ţŏ şēē ą ĺįşţ ŏƒ ţēŋąŋţ ɱēɱƃēŗş. Ůşē ţĥē Ŏţĥēŗş ţąƃ ţŏ ąƌƌ ēɱąįĺ ŗēċįρįēŋţş ɱąŋűąĺĺŷ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]

#XFLD Package
@txtPackage=[[[Ƥąċķąğē∙∙∙∙∙∙∙]]]

#XMSG warning for repository package assignment changed. {1} - package name
VAL_PACKAGE_CHANGED=[[[Ŷŏű ĥąʋē ąşşįğŋēƌ ţĥįş ŏƃĵēċţ ţŏ ρąċķąğē "{1}". Ĉĺįċķ Ŝąʋē ţŏ ċŏŋƒįŗɱ ąŋƌ ʋąĺįƌąţē ţĥįş ċĥąŋğē. Ńŏţē ţĥąţ ąşşįğŋɱēŋţ ţŏ ą ρąċķąğē ċąŋŋŏţ ƃē űŋƌŏŋē įŋ ţĥįş ēƌįţŏŗ ąƒţēŗ ŷŏű şąʋē.]]]
#XMSG warning for repository package dependency issue.
MISSING_DEPENDENCY=[[[Ďēρēŋƌēŋċįēş ŏƒ ŏƃĵēċţ "{0}" ċąŋŋŏţ ƃē ŗēşŏĺʋēƌ įŋ ţĥē ċŏŋţēχţ ŏƒ ρąċķąğē "{1}".]]]

#Warning messages for the folders in the "Others" tab of task chain editor
#XMSG generic warning if getting the objects in a particular folder fails with unhandled errors
getNonRepoArtefactTypeDescendantsError=[[[Ţĥēŗē ŵąş ą ρŗŏƃĺēɱ ŗēţŗįēʋįŋğ ţĥē ŏƃĵēċţş įŋ ţĥē ƒŏĺƌēŗ ŷŏű şēĺēċţēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#BW Process Chains folder related warning messages
#XMSG warning if user does not have the necessary permission related to the SAP BW Bridge space
missingPrivilegeForBridgeSpaceError=[[[Ŷŏű ƌŏ ŋŏţ ĥąʋē ţĥē ŋēċēşşąŗŷ ρēŗɱįşşįŏŋ ţŏ ʋįēŵ ŏŗ įŋċĺűƌē ƁŴ ρŗŏċēşş ċĥąįŋş įŋ ą ţąşķ ċĥąįŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG warning if there was a problem retrieving the BW process chains due to bridge space membership, bridge connection issues or other errors
generalBridgeSpaceError=[[[Ţĥēŗē ŵąş ą ρŗŏƃĺēɱ ŗēţŗįēʋįŋğ ţĥē ƁŴ ρŗŏċēşş ċĥąįŋş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG warning if the BW API for retrieving the BW process chains from the SAP BW Bridge tenant fails
bwApiClientError=[[[Ţĥēŗē ŵąş ą ρŗŏƃĺēɱ ŗēţŗįēʋįŋğ ţĥē ƁŴ ρŗŏċēşş ċĥąįŋş ƒŗŏɱ ţĥē ŜĀƤ ƁŴ Ɓŗįƌğē ţēŋąŋţ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG warning if BW process chains list is empty
bwApiEmptyListError=[[[Ńŏ ƁŴ ρŗŏċēşş ċĥąįŋş ŵēŗē ƒŏűŋƌ įŋ ţĥē ŜĀƤ ƁŴ Ɓŗįƌğē ţēŋąŋţ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Task log message for invalid OIDC configuration in the SAP BW Bridge tenant
bwBridgeOIDCConfigStatusInvalid=[[[ŎρēŋĬĎ ąűţĥēŋţįċąţįŏŋ įş ŋŏţ ċŏŋƒįğűŗēƌ ƒŏŗ ţĥįş ţēŋąŋţ. Ƥĺēąşē űşē ţĥē ċĺįēŋţĬĎ "{0}" ąŋƌ ţĥē ţŏķēŋŮŘĻ "{1}" ţŏ ċŏŋƒįğűŗē ţĥē ŎρēŋĬĎ ąűţĥēŋţįċąţįŏŋ įŋ ţĥē ŜĀƤ ƁŴ ƃŗįƌğē ţēŋąŋţ ąş ƌēşċŗįƃēƌ įŋ ŜĀƤ ŋŏţē 3536298.]]]
#XMSG BW Process Chain Object has been deleted
VAL_DELETED_BW=[[[Ţĥē ƒŏĺĺŏŵįŋğ ƁŴ ρŗŏċēşş ċĥąįŋş ŵēŗē ρŏşşįƃĺŷ ƌēĺēţēƌ ƒŗŏɱ ţĥē ŜĀƤ Ɓŗįƌğē ţēŋąŋţ: {0}]]]
#BW SQL Script Procedures folder related warning messages
#XFLD waring if the Procedure list is empty
procedureAPIEmptyList=[[[Ńŏ ρŗŏċēƌűŗēş ĥąʋē ƃēēŋ ċŗēąţēƌ ŏŗ ţĥē ĔΧĔĈŮŢĔ ρŗįʋįĺēğē ĥąş ŋŏţ ƃēēŋ ğŗąŋţēƌ ţŏ ţĥē Ŏρēŋ ŜǬĻ şċĥēɱą.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#Change digram orientations
changeOrientations=[[[Ĉĥąŋğē Ŏŗįēŋţąţįŏŋş∙∙∙∙∙]]]


# placeholder for the API Path
apiPath=[[[Ƒŏŗ ēχąɱρĺē: /ĵŏƃ/ʋ1∙∙∙∙]]]
# placeholder for the status API Path
statusAPIPath=[[[Ƒŏŗ ēχąɱρĺē: /ĵŏƃ/ʋ1/{įƌ}/şţąţűş∙∙∙∙∙∙∙∙∙∙]]]
# valueStateText for the API Path
apiPathRequired=[[[ĀƤĬ Ƥąţĥ įş ŗēƣűįŗēƌ∙∙∙∙]]]
#placeholder for the CSRF Token URL
csrfTokenURL=[[[Ŏŋĺŷ ĤŢŢƤŜ įş şűρρŏŗţēƌ∙∙∙∙∙∙]]]
# Response Type 1
statusCode=[[[Ģēţ ŗēşűĺţ ƒŗŏɱ ĤŢŢƤ şţąţűş ċŏƌē∙∙∙∙∙∙∙∙∙∙]]]
# Response Type 2
locationHeader=[[[Ģēţ ŗēşűĺţ ƒŗŏɱ ĤŢŢƤ şţąţűş ċŏƌē ąŋƌ ĺŏċąţįŏŋ ĥēąƌēŗ∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
# Response Type 3
responseBody=[[[Ģēţ ŗēşűĺţ ƒŗŏɱ ĤŢŢƤ şţąţűş ċŏƌē ąŋƌ ŗēşρŏŋşē ƃŏƌŷ∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
# placeholder for ID
idPlaceholder=[[[Ĕŋţēŗ ĴŜŎŃ Ƥąţĥ∙∙∙∙]]]
# placeholder for indicator value
indicatorValue=[[[Ĕŋţēŗ ʋąĺűē∙∙∙∙∙∙∙∙]]]
# Placeholder for key
keyPlaceholder=[[[Ĕŋţēŗ ķēŷ∙∙∙∙∙]]]
# Error message for missing key
KeyRequired=[[[Ķēŷ įş ŗēƣűįŗēƌ∙∙∙∙]]]
# Error message for invalid key format
invalidKeyFormat=[[[Ţĥē ĥēąƌēŗ ķēŷ ŷŏű ēŋţēŗēƌ įş ŋŏţ ąĺĺŏŵēƌ. Ʋąĺįƌ ĥēąƌēŗş ąŗē:<ul><li>"ρŗēƒēŗ"</li><li>Ĥēąƌēŗş şţąŗţįŋğ ŵįţĥ "χ-", ēχċēρţ "χ-ƒŏŗŵąŗƌēƌ-ĥŏşţ"</li><li>Ĥēąƌēŗş ţĥąţ ċŏŋţąįŋ ąĺρĥąŋűɱēŗįċ ċĥąŗąċţēŗş, "-", ŏŗ "_"</li></ul>]]]
# Error message for missing value
valueRequired=[[[Ʋąĺűē įş ŗēƣűįŗēƌ∙∙∙∙∙∙∙]]]
# Error message for invalid characters in value
invalidValueCharacters=[[[Ţĥē ĥēąƌēŗ ċŏŋţąįŋş įŋʋąĺįƌ ċĥąŗąċţēŗş. Ŝρēċįąĺ ċĥąŗąċţēŗş ţĥąţ ąŗē ąĺĺŏŵēƌ ąŗē:\\u0163 ";", ":", "-", "_", ",", "?", "/", ąŋƌ "*"∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
# Validation message for invoke api path
apiPathValidation=[[[Ƥĺēąşē ēŋţēŗ ą ʋąĺįƌ ĀƤĬ ρąţĥ, ƒŏŗ ēχąɱρĺē: /ĵŏƃ/ʋ1∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
# Validation message for JSON path
jsonPathValidation=[[[Ƥĺēąşē ēŋţēŗ ą ʋąĺįƌ ĴŜŎŃ ρąţĥ∙∙∙∙∙∙∙∙∙]]]
# Validation message for success/error indicator
indicatorValueValidation=[[[Ĭŋƌįċąţŏŗ ʋąĺűē ɱűşţ şţąŗţ ŵįţĥ ąŋ ąĺρĥąŋűɱēŗįċ ċĥąŗąċţēŗ ąŋƌ ċąŋ įŋċĺűƌē ţĥē ƒŏĺĺŏŵįŋğ şρēċįąĺ ċĥąŗąċţēŗş:\\u0163 "-", ąŋƌ "_"∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
# Error message for JSON path
jsonPathRequired=[[[ĴŜŎŃ Ƥąţĥ įş ŗēƣűįŗēƌ∙∙∙∙∙]]]
# Error message for invalid API Technical Name
invalidTechnicalName=[[[Ţĥē ţēċĥŋįċąĺ ŋąɱē ċŏŋţąįŋş įŋʋąĺįƌ ċĥąŗąċţēŗş∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
# Error message for empty Technical Name
emptyTechnicalName=[[[Ţēċĥŋįċąĺ Ńąɱē įş ŗēƣűįŗēƌ∙∙∙∙∙∙∙]]]
# Tooltip for codeEditor dialog
codeEditorTooltip=[[[Ŏρēŋ ĴŜŎŃ Ĕƌįţ ŵįŋƌŏŵ∙∙∙∙∙]]]
# Status Api path validation message
validationStatusAPIPath=[[[Ƥĺēąşē ēŋţēŗ ą ʋąĺįƌ ĀƤĬ ρąţĥ, ƒŏŗ ēχąɱρĺē: /ĵŏƃ/ʋ1/{įƌ}/şţąţűş∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
# CSRF token URL validation message
validationCSRFTokenURL=[[[Ţĥē ĈŜŘƑ Ţŏķēŋ ŮŘĻ ɱűşţ şţąŗţ ŵįţĥ 'ĥţţρş://' ąŋƌ ƃē ą ʋąĺįƌ ŮŘĻ∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
# Select a connection
selectConnection=[[[Ŝēĺēċţ ą ċŏŋŋēċţįŏŋ∙∙∙∙∙]]]
# Validation message for connection item error
connectionNotReplicated=[[[Ĉŏŋŋēċţįŏŋ ċűŗŗēŋţĺŷ įŋʋąĺįƌ ţŏ ŗűŋ ĀƤĬ ţąşķş. Ŏρēŋ ţĥē "Ĉŏŋŋēċţįŏŋş" ąρρ ąŋƌ ŗē-ēŋţēŗ ŷŏűŗ ċŗēƌēŋţįąĺş ţŏ ƒįχ ţĥē ĤŢŢƤ ċŏŋŋēċţįŏŋ∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
# Validation message for API task properties wrong value or missing
VAL_REST_API_PROPERTIES_MISSING=[[[Ţĥē ĀƤĬ ţąşķ "{0}" ĥąş įŋċŏŗŗēċţ ŏŗ ɱįşşįŋğ ʋąĺűēş ƒŏŗ ţĥē ƒŏĺĺŏŵįŋğ ρŗŏρēŗţįēş: {1}]]]
# Validation message for API connection api is throwing some error or selected connection has some warning
VAL_REST_API_CONNECTION=[[[Ţĥē ĀƤĬ ţąşķ "{0}" ĥąş ąŋ įşşűē ŵįţĥ ţĥē ĤŢŢƤ ċŏŋŋēċţįŏŋ. Ŏρēŋ ţĥē "Ĉŏŋŋēċţįŏŋş" ąρρ ąŋƌ ċĥēċķ ţĥē ċŏŋŋēċţįŏŋ]]]
# Validation message for API task properties when delected connection
VAL_REST_API_CONNECTION_DELETED=[[[Ţĥē ĀƤĬ ţąşķ "{0}" ĥąş ą ƌēĺēţēƌ "{1}" ċŏŋŋēċţįŏŋ]]]
# Validation message for API task properties when connection cannot be consumed in the API task
VAL_REST_API_CONNECTION_CONSUMPTION_WARNING=[[[Ţĥē ĀƤĬ ţąşķ "{0}" ĥąş ą ċŏŋŋēċţįŏŋ "{1}" ţĥąţ ċąŋŋŏţ ƃē űşēƌ ţŏ ŗűŋ ĀƤĬ ţąşķş. Ŏρēŋ ţĥē "Ĉŏŋŋēċţįŏŋş" ąρρ ąŋƌ ŗē-ēŋţēŗ ŷŏűŗ ċŗēƌēŋţįąĺş ţŏ ēşţąƃĺįşĥ ċŏŋŋēċţįʋįţŷ ƒŏŗ ĀƤĬ ţąşķş]]]
# Information message if synchrnous mode is selected in the API Invocation then we don't need status information
modeInfoText=[[[Ĭŋ Ŝŷŋċĥŗŏŋŏűş ɱŏƌē, ţĥē şţąţűş ρąŋēĺ įş ŋŏţ ƌįşρĺąŷēƌ ƒŏŗ ĀƤĬ įŋʋŏċąţįŏŋş∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
# validation dialog button for Run API Test
saveAnyway=[[[Ŝąʋē Āŋŷŵąŷ∙∙∙∙∙∙∙∙]]]
# validation message for technical name
technicalNameValidation=[[[Ţĥē ţēċĥŋįċąĺ ŋąɱē ɱűşţ ƃē űŋįƣűē ŵįţĥįŋ ţĥē ţąşķ ċĥąįŋ. Ƥĺēąşē ċĥŏŏşē ąŋŏţĥēŗ ţēċĥŋįċąĺ ŋąɱē∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
# Connection error message
getHttpConnectionsError=[[[Ƒąįĺēƌ ţŏ ğēţ ţĥē ĤŢŢƤ ċŏŋŋēċţįŏŋş∙∙∙∙∙∙∙∙∙∙∙]]]
# Msg for the API test run when the Task chain is not saved
taskChainNotSaved=[[[Ţĥē ţąşķ ċĥąįŋ ɱűşţ ƃē şąʋēƌ ƃēƒŏŗē ŗűŋŋįŋğ ţĥē ĀƤĬ ţēşţ ŗűŋ∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
# Msg failed to run API test run
@failedToRunAPI=[[[Ƒąįĺēƌ ţŏ ŗűŋ ţĥē ĀƤĬ ţēşţ ŗűŋ∙∙∙∙∙∙∙∙∙]]]
# Msg for the API test run when its already in running state
apiTaskRunning=[[[Āŋ ĀƤĬ ţēşţ ŗűŋ įş ąĺŗēąƌŷ įŋ ρŗŏğŗēşş. Ďŏ ŷŏű ŵąŋţ ţŏ şţąŗţ ą ŋēŵ ţēşţ ŗűŋ?∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]

topToBtm=[[[Ţŏρ-Ɓŏţţŏɱ∙∙∙∙]]]
leftToRight=[[[Ļēƒţ-Řįğĥţ∙∙∙∙]]]

#XTIT Apache Spark Application Settings
txtHeaderApacheSettings=[[[Āρąċĥē Ŝρąŗķ Āρρĺįċąţįŏŋ Ŝēţţįŋğş∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD Use Default
txtUseSpaceDefault=[[[Ůşē Ďēƒąűĺţ∙∙∙∙∙∙∙∙]]]
#XFLD Application
txtApplication=[[[Āρρĺįċąţįŏŋ∙∙∙∙∙∙∙∙]]]
#XFLD Define new settings for this Task
txtNewSettings=[[[Ďēƒįŋē ŋēŵ şēţţįŋğş ƒŏŗ ţĥįş Ţąşķ∙∙∙∙∙∙∙∙∙∙∙]]]

#XFLD Use Default
txtUseDefault=[[[Ůşē Ďēƒąűĺţ∙∙∙∙∙∙∙∙]]]




