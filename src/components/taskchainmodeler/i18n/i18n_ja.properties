#XTOL Undo
@undo=元に戻す
#XTOL Redo
@redo=やり直し
#XTOL Delete Selected Symbol
@deleteNode=選択したシンボルを削除
#XTOL Zoom to Fit
@zoomToFit=ウィンドウのサイズに合わせて大きさを変更
#XTOL Auto Layout
@autoLayout=自動レイアウト
#XMSG
@welcomeText=左側のパネルからこのキャンバスに、オブジェクトをドラッグ & ドロップします。
#XMSG
@txtNoData=オブジェクトがまだ追加されていないようです。
#XMSG
VAL_ENTER_VALID_STRING_GEN={0} 以下の有効な文字列を入力してください。
#XMSG
@noParametersMsg=このプロシージャには入力パラメータがありません。
#XMSG
@ip_enterValueMsg="{0}" (SQLScript プロシージャを実行) に入力パラメータがあります。それらの各パラメータの値を設定できます。
#XTOL
@validateModel=チェックメッセージ
#XTOL
@hierarchy=階層
#XTOL
@columnCount=列の数
#XFLD
@yes=はい
#XFLD
@no=いいえ
#XTIT Save Dialog param
@modelNameTaskChain=タスクチェーン
#properties panel
@lblPropertyTitle=プロパティ
#XFLD
@lblGeneral=一般
#XFLD : Setting
@lblSetting=設定
#XTXT: Delta Enabled Local Table Deletion
txtDeltaTableDeletion=次よりも古い、変更タイプが '削除済み' の完全に処理されたレコードをすべて削除します
#XTXT: Delta Enabled Local Table Deletion Input field description
desDeltaTableDeletion=日
#XFLD: Data Activation label
@lblDataActivation=データの有効化
#XFLD: Latency label
@latency=待機時間
#XTEXT: Text for Latency dropdown
txtLatencyDefault=デフォルト
#XTEXT: Text 1 hour
txtOneHour=1 時間
#XTEXT: Text 2 hours
txtTwoHours=2 時間
#XTEXT: Text 3 hours
txtThreeHours=3 時間
#XTEXT: Text 4 hours
txtFourHours=4 時間
#XTEXT: Text 6 hours
txtSixHours=6 時間
#XTEXT: Text 12 hours
txtTwelveHours=12 時間
#XTEXT: Text 1 day
txtOneDay=1 日
#XFLD: Latency label
@autoRestartHead=自動再起動
#XFLD
@lblConnectionName=接続
#XFLD
@lblQualifiedName=修飾名
#XFLD
@lblSpaceName=スペース名
#XFLD
@lblLocalSchemaName=ローカルスキーマ
#XFLD
@lblType=オブジェクトタイプ
#XFLD
@lblActivity=アクティビティ
#XFLD
@lblTableName=テーブル名
#XFLD
@lblBusinessName=ビジネス名
#XFLD
@lblTechnicalName=技術名
#XFLD
@lblSpace=スペース
#XFLD
@lblLabel=ラベル
#XFLD
@lblDataType=データ型
#XFLD
@lblDescription=説明
#XFLD
@lblStorageType=ストレージ
#XFLD
@lblHTTPConnection=汎用 HTTP 接続
#XFLD
@lblAPISettings=汎用 API 設定
#XFLD
@header=ヘッダ
#XFLD
@lblInvoke=API 呼び出し
#XFLD
@lblMethod=メソッド
#XFLD
@lblUrl=基本 URL
#XFLD
@lblAPIPath=API パス
#XFLD
@lblMode=モード
#XFLD
@lblCSRFToken=CSRF トークンが必要
#XFLD
@lblTokenURL=CSRF トークン URL
#XFLD
@csrfTokenInfoText=入力しなかった場合は、基本 URL および API パスが使用されます
#XFLD
@lblCSRF=CSRF トークン
#XFLD
@lblRequestBody=要求本文
#XFLD
@lblFormat=フォーマット
#XFLD
@lblResponse=応答
#XFLD
@lblId=ステータスを取得する ID
#XFLD
@Id=ID
#XFLD
@lblSuccessIndicator=成功区分
#XFLD
@lblErrorIndicator=エラー区分
#XFLD
@lblErrorReason=エラー理由
#XFLD
@lblStatus=ステータス
#XFLD
@lblApiTestRun=API テスト実行
#XFLD
@lblRunStatus=実行ステータス
#XFLD
@lblLastRan=最終実行日
#XFLD
@lblTestRun=テスト実行
#XFLD
@lblDefaultHeader=デフォルトヘッダフィールド (キーと値のペア)
#XFLD
@lblAdditionalHeader=追加のヘッダフィールド
#XFLD
@lblKey=キー
#XFLD
@lblEditJSON=JSON を編集
#XFLD
@lblTasks=タスク
#XFLD
@lblRESTfullTask=API
#XFLD: add field button text
btnAddField=ヘッダフィールドを追加
#XFLD: view Details link
@viewDetails=詳細表示
#XTOL
tooltipTxt=詳細
#XTOL
delete=削除
#XBTN: ok button text
btnOk=OK
#XBTN: cancel button text
btnCancel=キャンセル
#XBTN: save button text
btnSave=保存
#XMSG Error: Object has duplicated technical name.
VAL_OBJECT_DUPLICATE_NAME=オブジェクト ''{0}'' はすでにリポジトリに存在します。別の名前を入力してください。
#XMSG: loading message while opening task chain
loadTaskChain=タスクチェーンをロードしています...
#model properties
#XFLD
@status_panel=実行ステータス
#XFLD
@deploy_status_panel=配置ステータス
#XFLD
@status_lbl=ステータス
#XFLD
@lblLastExecuted=前回実行
#XFLD
@lblNotExecuted=未実行
#XFLD
@lblNotDeployed=未デプロイ
#XFLD
errorDetailsTxt=実行ステータスをフェッチできませんでした
#XBTN: Schedule dropdown menu
SCHEDULE=スケジュール
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=スケジュールを編集
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=スケジュールを削除
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=スケジュールを作成
#XLNK
viewDetails=詳細表示
#XMSG: error message for reading execution status from backend
backendErrorMsg=現在、データがサーバからロードされていないようです。もう一度データをダウンロードしてください。
#XFLD: Status text for Completed
@statusCompleted=完了
#XFLD: Status text for Running
@statusRunning=実行中
#XFLD: Status text for Failed
@statusFailed=失敗
#XFLD: Status text for Stopped
@statusStopped=停止
#XFLD: Status text for Stopping
@statusStopping=停止中
#XFLD
@LoaderTitle=ロードしています
#XFLD
#XFLD: Deploy status, dataflow has  been deployed / deployment date exists
@deployStatusActive=デプロイ済み
@deployStatusRevised=ローカル更新
@deployStatusFailed=失敗
@deployStatusPending=デプロイしています...
@LoaderText=サーバから詳細をフェッチしています
#XMSG
@msgDetailFetchError=サーバから詳細をフェッチしているときのエラー
#XFLD
@executeError=エラー
#XFLD
@executeWarning=警告
#XMSG
@executeConfirmDialog=情報
#XMSG
@executeunsavederror=タスクチェーンを実行する前に、そのタスクチェーンを保存してください。
#XMSG
@executemodifiederror=保存されていない変更がタスクチェーン内にあります。変更を保存してください。
#XMSG
@executerunningerror=タスクチェーンは現在実行されています。現在の実行が完了するのを持ってから、新しいタスクチェーンを開始してください。
#XMSG
@btnExecuteAnyway=そのまま実行
#XMSG
@msgExecuteWithValidations=タスクチェーンにチェックエラーがあります。タスクチェーンを実行すると、エラーが発生する可能性があります。
#XMSG
@msgRunDeployedVersion=デプロイ対象の変更があります。最後にデプロイされたバージョンのタスクチェーンが実行されます。続行しますか?
#XMSG
#XMSG
@navToMonitoring=タスクチェーンモニタで開く
#XMSG
txtOR=または (OR)
#XFLD
@preview=プレビュー
#XMSG
txtand=かつ (AND)
#NOTR
@dateFormat=yyyy-MM-dd
#NOTR
@dateTimeFormatFilter=yyyy-MM-dd HH:mm:ss.
#NOTR
@timeFormat=HH:mm:ss
@lblColum=列
#XFLD
@lblCondition=条件
#XFLD
@lblValue=値
#XMSG
@msgJsonInvalid=JSON にエラーがあるため、タスクチェーンを保存できませんでした。チェックして解決してください。
#XTIT
@msgSaveFailTitle=無効な JSON。
#XMSG
NOT_CHAINABLE=オブジェクト ''{0}'' をタスクチェーンに追加できません。
#XMSG
NOT_CHAINABLE_REMOTETABLE=オブジェクト "{0}": 複製タイプが変更されます。
#XMSG
searchTaskChain=オブジェクト検索

#XFLD
@txtTaskChain=タスクチェーン
#XFLD
@txtRemoteTable=リモートテーブル
#XFLD
@txtRemoveData=複製されたデータを削除
#XFLD
@txtRemovePersist=永続化されたデータを削除
#XFLD
@txtView=表示
#XFLD
@txtDataFlow=データフロー
#XFLD
@txtIL=インテリジェントルックアップ
#XFLD
@txtTransformationFlow=変換フロー
#XFLD
@txtReplicationFlow=複製フロー
#XFLD
@txtDeltaLocalTable=ローカルテーブル
#XFLD
@txtBWProcessChain=BW プロセスチェーン
#XFLD
@txtSQLScriptProcedure=SQLScript プロシージャ
#XFLD
@txtAPI=API
#XFLD
@txtMerge=マージ
#XFLD
@txtOptimize=最適化
#XFLD
@txtVacuum=レコードを削除

#XFLD
@txtRun=実行
#XFLD
@txtPersist=永続
#XFLD
@txtReplicate=複製
#XFLD
@txtDelete=変更タイプが '削除済み' のレコードを削除
#XFLD
@txtRunTC=タスクチェーンを実行
#XFLD
@txtRunBW=BW プロセスチェーンを実行
#XFLD
@txtRunSQLScriptProcedure=SQLScript プロシージャを実行

#XFLD
@txtRunDataFlow=データフローの実行
#XFLD
@txtPersistView=永続ビュー
#XFLD
@txtReplicateTable=テーブルを複製
#XFLD
@txtRunIL=インテリジェントルックアップを実行
#XFLD
@txtRunTF=変換フローの実行
#XFLD
@txtRunRF=複製フローを実行
#XFLD
@txtRemoveReplicatedData=複製されたデータを削除
#XFLD
@txtRemovePersistedData=永続化されたデータを削除
#XFLD
@txtMergeData=マージ
#XFLD
@txtOptimizeData=最適化
#XFLD
@txtVacuumData=レコードを削除
#XFLD
@txtRunAPI=API を実行

#XFLD storage type text
hdlfStorage=ファイル

@statusNew=未デプロイ
@statusActive=デプロイ済み
@statusRevised=ローカル更新
@statusPending=デプロイしています...
@statusChangesToDeploy=配置対象の変更
@statusDesignTimeError=設計時エラー
@statusRunTimeError=実行時エラー

#XTIT
txtNodes=タスクチェーン内のオブジェクト ({0})
#XBTN
@deleteNodes=削除

#XMSG
@txtDropDataToDiagram=オブジェクトをダイアグラムにドラッグ & ドロップしてください。
#XMSG
@noData=オブジェクトなし

#XFLD
@txtTaskPosition=タスク位置

#input parameters and variables
#XFLD
lblInputParameters=入力パラメータ
#XFLD
ip_name=名前
#XFLD
ip_value=値
#XTEXT
@noObjectsFound=オブジェクトが見つかりません

#XMSG
@msgExecuteSuccess=タスクチェーンの実行が開始されました。
#XMSG
@msgExecuteFail=タスクチェーンを実行できませんでした。
#XMSG
@msgDeployAndRunSuccess=タスクチェーンの配置と実行が開始されました。
#XMSG
@msgDeployAndRunFail=データフローの配置と実行が失敗しました。
#XMSG
@titleExecuteBusy=お待ちください。
#XMSG
@msgExecuteBusy=タスクチェーンの実行を開始するために、データを準備しています。
#XMSG
@msgAPITestRunSuccess=API テスト実行が開始されました。
#XMSG
@msgAPIExecuteBusy=API テスト実行を開始するために、データを準備しています。

#XTOL
txtOpenInEditor=エディタで開く
#XTOL
txtPreviewData=データのプレビュー

#datapreview
#XMSG
@msgDataPreviewNotSupp=このオブジェクトではデータプレビューを利用できません。

#XMSG Error: empty model
VAL_MODEL_EMPTY=モデルが空のようです。いくつかのオブジェクトを追加してください。
#XMSG Error: deploy model
@msgDeployBeforeRun=タスクチェーンを実行する前に、そのタスクチェーンを配置する必要があります。
#BTN: close dialog
btnClose=閉じる

#XMSG Object is not Deployed
VAL_NOT_DEPLOYED=タスクチェーンを続行するには、オブジェクト ''{0}'' を配置する必要があります。
#XMSG Object is having runtime error
VAL_RUNTIME_ERROR=オブジェクト "{0}" が実行時エラーを返しました。オブジェクトをチェックしてください。
#XMSG Object is having design time error
VAL_DESIGNTIME_ERROR=オブジェクト "{0}" が設計時エラーを返しました。オブジェクトをチェックしてください。
#XMSG Procedure Object has been deleted
VAL_DELETED_PROCEDURE=次のプロシージャは削除されました: {0}
#XMSG New Parameter added to Procedure object
VAL_NEW_PARAMETER_ADDED=プロシージャ "{1}": "{0}" に新しいパラメータが追加されました。タスクチェーンを再デプロイしてください。
#XMSG Parameter removed from Procedure object
VAL_PARAMETER_DELETED=プロシージャ "{1}": "{0}" からパラメータが削除されました。タスクチェーンを再デプロイしてください。
#XMSG Parameter data type changed in Procedure object
VAL_PARAMETER_DATATYPE_MODIFIED=プロシージャ "{3}" でパラメータ "{0}" のデータ型が "{1}" から "{2}" に変更されました。
#XMSG Parameter length changed of specific datatype in Procedure object
VAL_PARAMETER_LENGTH_MODIFIED=プロシージャ "{3}" でパラメータ "{0}" の長さが "{1}" から "{2}" に変更されました。
#XMSG Parameter precision changed of Decimal datatype in Procedure object
VAL_PARAMETER_PRECISION_MODIFIED=プロシージャ "{3}" でパラメータ "{0}" の精度が "{1}" から "{2}" に変更されました。
#XMSG Parameter scale changed of Decimal datatype in Procedure object
VAL_PARAMETER_SCALE_MODIFIED=プロシージャ "{3}" でパラメータ "{0}" のスケールが "{1}" から "{2}" に変更されました。
#XMSG Parameter value not provided in Procedure object
VAL_SQL_SCRIPT_PROCEDURE_PARAMETER_VALUE_MISSING=プロシージャ "{0}": "{1}" に必要な入力パラメータの値が入力されていません
#XMSG Remote Table activity changes to Remove Replicated Data
VAL_REMOVE_REPLICATED_DATA=タスクチェーンの時間の実行によってデータアクセスタイプが変更され、データがリアルタイムでアップロードされなくなります。
#XMSG Remote Table activity changes
REPLICATION_TYPE_CHANGED=オブジェクト "{0}": 複製タイプが変更されます。

#XFLD
@lblStartNode=ノードを開始
#XFLD
@lblEndNode=ノードを終了
#XFLD
@linkTo={0} から {1}
#XTOL
@txtViewDetails=詳細表示

#XTOL
txtOpenImpactLineage=インパクトおよびリネージ分析
#XFLD
@emailNotifications=電子メール通知
#XFLD
@txtReset=リセット
#XFLD
@emailMsgWarning=電子メールメッセージが空の場合は、デフォルトテンプレートの電子メールが送信されます
#XFLD
@notificationSettings=通知設定
#XFLD
@recipientEmailAddr=受信者メールアドレス
#XFLD
@emailSubject=電子メール件名
@emailSubjectText=タスクチェーン <TASKCHAIN_NAME> がステータス <STATUS> で完了しました
#XFLD
@emailMessage=電子メールメッセージ
@emailMessageText=ユーザ様、\n\n これは、<START_TIME> で実行されたタスクチェーン: <TASKCHAIN_NAME> がステータス <STATUS> で終了したことを知らせるものです。実行は <END_TIME> に終了しました。\n\n詳細:\nスペース:<SPACE_NAME>\nエラー:<ERROR_MESSAGE>
@selectEmailRecipientDialogTitle=受信者メールアドレスを選択
@tenantMembers=テナントメンバー
@others=その他 ({0})
@selectedEmailAddress=選択された受信者
@add=追加
@placeholder=プレースホルダ
@description=説明
@copyText=テキストをコピー
@taskchainDetailsPlaceholder=タスクチェーン詳細用のプレースホルダ
@placeholderCopied=プレースホルダがコピーされました
@invalidEmailInfo=正しい電子メールアドレスを入力
@maxMembersAlreadyAdded=すでに最大限である 20 件のメンバーが追加されました
@enterEmailAddress=ユーザの電子メールアドレスを入力
@inCorrectPlaceHolder={0} は電子メール本文内の適切なプレースホルダではありません。
@nsOFF=通知を送信しない
@nsFAILED=実行がエラーで終了した場合のみ、電子メール通知を送信する
@nsCOMPLETED=実行が正常に完了した場合のみ、電子メール通知を送信する
@nsANY=実行が完了した場合に、電子メールを送信する
@phStatus=タスクチェーンのステータス - 正常終了|失敗
@phTaskChainTName=タスクチェーンの技術名
@phTaskChainBName=タスクチェーンのビジネス名
@phLogId=実行のログ ID
@phUser=タスクチェーンを実行しているユーザ
@phLogUILink=タスクチェーンのログ表示へのリンク
@phStartTime=実行の開始時間
@phEndTime=実行の終了時間
@phErrMsg=タスクログ内の最初のエラーメッセージ。正常終了の場合、ログは空です。
@phSpaceName=スペースの技術名
@emailFormatError=無効な電子メール書式
@emailFormatErrorInListText=無効な電子メール書式が非テナントメンバーリストに入力されました。
@emailSubjectTemplateText=タスクチェーン: $$taskChainName$$ - スペース: $$spaceId$$ - ステータス: $$status$$ に関する通知
@emailMessageTemplateText=こんにちは、\n\n$$taskChainName$$ というラベルのタスクチェーンがステータス $$status$$ で終了しました。\n タスクチェーンに関するその他の詳細を以下に示します。\n - タスクチェーン技術名: $$taskChainName$$ \n - タスクチェーン実行のログ ID: $$logId$$ \n - タスクチェーンを実行したユーザ: $$user$$ \n - タスクチェーンのログ表示へのリンク: $$uiLink$$ \n - タスクチェーン実行の開始時刻: $$startTime$$ \n - タスクチェーン実行の終了時刻: $$endTime$$ \n - スペースの名前: $$spaceId$$ \n
@deleteEmailRecepient=受信者を削除
@emailInputDisabledText=電子メール受信者を追加するには、タスクチェーンを配置してください。
@tenantOwnerDomainMatchErrorText=電子メールアドレスドメインがテナントオーナードメイン {0} と一致しません。
@totalEmailIdLimitInfoText=細大 20 人の受信者 (テナントメンバーユーザや、その他の受信者を含む) を選択できます。
@emailDomainInfoText=ドメイン: {0} の電子メールアドレスのみが受け入れられます。
@duplicateEmailErrorText=重複した電子メール受信者が一覧に記載されています。

#XFLD Zorder Title
@txtZorderTitle=Apache Spark Z オーダー列

#XFLD Zorder NoColumn
@txtZorderNoColumn=Z オーダー列が見つかりません

#XFLD Zorder Column PrimaryKey
@txtZorderColumnPrimaryKey=一次キー

#XFLD
@lblOperators=演算子
addNewSelector=新規タスクとして追加
parallelSelector=並列タスクとして追加
replaceSelector=既存タスクを置換
addparallelbranch=並列分岐として追加
addplaceholder=プレースホルダを追加
addALLOperation=ALL 演算子
addOROperation=ANY 演算子
addplaceholdertocanvas=プレースホルダをキャンバスに追加
addplaceholderonselected=選択したタスクの後にプレースホルダを追加
#XFLD: Add Parallel Branch to Selected Task
addparallelbranchonselected=選択したタスクの後に並列分岐を追加
addOperator=演算子を追加
txtAdd=追加
txtPlaceHolderText=タスクをここにドラッグ & ドロップ
@lblLayout=レイアウト

#XMSG
VAL_UNCONNECTED_TASK=タスク "{0}" はタスクチェーンに接続されていません。
#XMSG
VAL_TASK_SHOULD_HAVE_ONE_INCOMING_LINK=タスク "{0}" には受信リンクが 1 つだけ必要です。
#XMSG
VAL_TASK_SHOULD_HAVE_INCOMING_LINK=タスク "{0}" には受信リンクが 1 つ必要です。
#XMSG
VAL_UNCONNECTED_OPERATOR=演算子 "{0}" はタスクチェーンに接続されていません。
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ATLEAST_TWO_INCOMING_LINK=演算子 "{0}" には 2 つ以上の受信リンクが必要です。
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ONE_OUT_LINK=演算子 "{0}" には 1 つ以上の送信リンクが必要です。
#XMSG
VAL_CIRCULAR_REFERENCE_EXITS=タスクチェーン "{0}" に循環ループが存在します。
#XMSG
VAL_UNCONNECTED_BRANCH=オブジェクト/分岐 "{0}" はタスクチェーンに接続されていません。
#XMSG
VAL_TASK_TRIGGERED_PARALLEL_MORE_THAN_ONE_TIME=タスク "{0}" は複数回並列接続されています。続行するには重複を削除してください。


txtBegin=開始
txtNodesInLink=関連オブジェクト
#XTOL Tooltip for a context button on diagram
openInNewTab=新規タブで開く
#XTOL Tooltip for a context button on diagram
txtConnectToTaskOperator=ドラッグして接続
@emailUpdateError=電子メール通知一覧の更新中にエラーが発生しました

#XMSG
noTeamPrivilegeTxt=テナントメンバーの一覧を表示する権限がありません。その他タブを使用して電子メールの受信者をマニュアルで追加してください。

#XFLD Package
@txtPackage=パッケージ

#XMSG warning for repository package assignment changed. {1} - package name
VAL_PACKAGE_CHANGED=このオブジェクトをパッケージ "{1}" に割り当てました。保存をクリックしてこの変更を確認および有効化してください。保存後はこのエディタでパッケージへの割り当てを元に戻すことができないので注意してください。
#XMSG warning for repository package dependency issue.
MISSING_DEPENDENCY=パッケージ "{1}" のコンテキストでオブジェクト "{0}" の依存関係を解決できません。

#Warning messages for the folders in the "Others" tab of task chain editor
#XMSG generic warning if getting the objects in a particular folder fails with unhandled errors
getNonRepoArtefactTypeDescendantsError=選択したフォルダでのオブジェクトの取得中に問題が発生しました。
#BW Process Chains folder related warning messages
#XMSG warning if user does not have the necessary permission related to the SAP BW Bridge space
missingPrivilegeForBridgeSpaceError=BW プロセスチェーンを表示したりタスクチェーンに含めたりするために必要な権限がありません。
#XMSG warning if there was a problem retrieving the BW process chains due to bridge space membership, bridge connection issues or other errors
generalBridgeSpaceError=BW プロセスチェーンの取得中に問題が発生しました。
#XMSG warning if the BW API for retrieving the BW process chains from the SAP BW Bridge tenant fails
bwApiClientError=SAP BW ブリッジテナントから BW プロセスチェーンを取得する際に問題が発生しました。
#XMSG warning if BW process chains list is empty
bwApiEmptyListError=SAP BW ブリッジテナントで BW プロセスチェーンが見つかりませんでした。
#XMSG: Task log message for invalid OIDC configuration in the SAP BW Bridge tenant
bwBridgeOIDCConfigStatusInvalid=このテナントでは OpenID 認証が設定されていません。SAP ノート 3536298 に記載されているように、クライアント ID "{0}" およびトークン URL "{1}" を使用して SAP BW ブリッジテナントで OpenID 認証を設定してください。
#XMSG BW Process Chain Object has been deleted
VAL_DELETED_BW=SAP BW ブリッジテナントから次の BW プロセスチェーンが削除された可能性があります: {0}
#BW SQL Script Procedures folder related warning messages
#XFLD waring if the Procedure list is empty
procedureAPIEmptyList=プロシージャが作成されていないか、EXECUTE 権限が Open SQL スキーマに付与されていません。
#Change digram orientations
changeOrientations=方向を変更


# placeholder for the API Path
apiPath=例: /job/v1
# placeholder for the status API Path
statusAPIPath=例: /job/v1/{id}/status
# valueStateText for the API Path
apiPathRequired=API パスは必須です
#placeholder for the CSRF Token URL
csrfTokenURL=HTTPS のみがサポートされています
# Response Type 1
statusCode=HTTP ステータスコードから結果を取得
# Response Type 2
locationHeader=HTTP ステータスコードとロケーションヘッダから結果を取得
# Response Type 3
responseBody=HTTP ステータスコードと応答本文から結果を取得
# placeholder for ID
idPlaceholder=JSON パスを入力
# placeholder for indicator value
indicatorValue=値を入力
# Placeholder for key
keyPlaceholder=キーを入力
# Error message for missing key
KeyRequired=キーは必須です
# Error message for invalid key format
invalidKeyFormat=入力したヘッダキーは許可されていません。有効なヘッダは以下のとおりです。<ul><li>"prefer"</li><li>"x-" で始まるヘッダ ("x-forwarded-host" を除く)</li><li>英数字、"-"、または "_" を含むヘッダ</li></ul>
# Error message for missing value
valueRequired=値は必須です
# Error message for invalid characters in value
invalidValueCharacters=ヘッダに無効な文字が含まれています。使用可能な特殊文字は次のとおりです:\t ";"、":"、"-"、"_"、","、"?"、"/"、および "*"
# Validation message for invoke api path
apiPathValidation=有効な API パスを入力してください。例: /job/v1
# Validation message for JSON path
jsonPathValidation=有効な JSON パスを入力してください
# Validation message for success/error indicator
indicatorValueValidation=区分値は英数字で始まる必要があり、次の特殊文字を使用できます:\t "-" および "_"
# Error message for JSON path
jsonPathRequired=JSON パスは必須です
# Error message for invalid API Technical Name
invalidTechnicalName=技術名に無効な文字が含まれています
# Error message for empty Technical Name
emptyTechnicalName=技術名は必須です
# Tooltip for codeEditor dialog
codeEditorTooltip="JSON 編集" ウィンドウを開く
# Status Api path validation message
validationStatusAPIPath=有効な API パスを入力してください。例: /job/v1/{id}/status
# CSRF token URL validation message
validationCSRFTokenURL=CSRF トークン URL は 'https://' で始まる有効な URL である必要があります
# Select a connection
selectConnection=接続を選択してください
# Validation message for connection item error
connectionNotReplicated=API タスクを実行する接続が現在無効です。"接続" アプリを開き、認証情報を再入力して HTTP 接続を修正してください
# Validation message for API task properties wrong value or missing
VAL_REST_API_PROPERTIES_MISSING=API タスク "{0}" でプロパティ {1} の値がないか、正しくありません
# Validation message for API connection api is throwing some error or selected connection has some warning
VAL_REST_API_CONNECTION=API タスク "{0}" で HTTP 接続に問題があります。"接続" アプリを開き、接続をチェックしてください
# Validation message for API task properties when delected connection
VAL_REST_API_CONNECTION_DELETED=API タスク "{0}" に削除された "{1}" 接続があります
# Validation message for API task properties when connection cannot be consumed in the API task
VAL_REST_API_CONNECTION_CONSUMPTION_WARNING=API タスク "{0}" に、API タスクの実行に使用できない接続 "{1}" があります。"接続" アプリを開き、認証情報を再入力して、API タスクの接続を確立してください。
# Information message if synchrnous mode is selected in the API Invocation then we don't need status information
modeInfoText=同期モードでは、API 呼び出しのステータスパネルが表示されません
# validation dialog button for Run API Test
saveAnyway=保存
# validation message for technical name
technicalNameValidation=タスクチェーン内で技術名は一意である必要があります。別の技術名を選択してください
# Connection error message
getHttpConnectionsError=HTTP 接続を取得できませんでした
# Msg for the API test run when the Task chain is not saved
taskChainNotSaved=API テスト実行の前にタスクチェーンを保存する必要があります
# Msg failed to run API test run
@failedToRunAPI=API テスト実行を実行できませんでした
# Msg for the API test run when its already in running state
apiTaskRunning=API テスト実行がすでに進行中です。新規テスト実行を開始しますか?

topToBtm=上から下
leftToRight=左から右

#XTIT Apache Spark Application Settings
txtHeaderApacheSettings=Apache Spark のアプリケーション設定
#XFLD Use Default
txtUseSpaceDefault=デフォルトを使用
#XFLD Application
txtApplication=アプリケーション
#XFLD Define new settings for this Task
txtNewSettings=このタスクの新しい設定を定義

#XFLD Use Default
txtUseDefault=デフォルトを使用




