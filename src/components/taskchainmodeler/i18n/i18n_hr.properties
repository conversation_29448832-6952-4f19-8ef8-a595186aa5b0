#XTOL Undo
@undo=Poništi
#XTOL Redo
@redo=Ponovi
#XTOL Delete Selected Symbol
@deleteNode=Izbriši odabrani simbol
#XTOL Zoom to Fit
@zoomToFit=<PERSON><PERSON>raj da stane
#XTOL Auto Layout
@autoLayout=Automatski raspored
#XMSG
@welcomeText=Povucite i ispustite objekte iz lijevog panela ovamo na platno.
#XMSG
@txtNoData=Čini se da niste još dodali objekte.
#XMSG
VAL_ENTER_VALID_STRING_GEN=Unesite valjan niz dužine manje od ili jednake {0}.
#XMSG
@noParametersMsg=Ovaj postupak nema ulazne parametre.
#XMSG
@ip_enterValueMsg="{0}" (Izvođenje postupka SQLScript) ima ulazne parametre. Možete postaviti vrijednost svakog od njih.
#XTOL
@validateModel=Poruke validacije
#XTOL
@hierarchy=Hijerarhija
#XTOL
@columnCount=Broj stupaca
#XFLD
@yes=Da
#XFLD
@no=Ne
#XTIT Save Dialog param
@modelNameTaskChain=Lanac zadataka
#properties panel
@lblPropertyTitle=Svojstva
#XFLD
@lblGeneral=Općenito
#XFLD : Setting
@lblSetting=Postavke
#XTXT: Delta Enabled Local Table Deletion
txtDeltaTableDeletion=Izbrišite sve potpuno obrađene zapise s tipom promjene 'Izbrisano' koji su stariji od
#XTXT: Delta Enabled Local Table Deletion Input field description
desDeltaTableDeletion=Dani
#XFLD: Data Activation label
@lblDataActivation=Aktiviranje podataka
#XFLD: Latency label
@latency=Kašnjenje
#XTEXT: Text for Latency dropdown
txtLatencyDefault=Zadano
#XTEXT: Text 1 hour
txtOneHour=1 sat
#XTEXT: Text 2 hours
txtTwoHours=2 sata
#XTEXT: Text 3 hours
txtThreeHours=3 sata
#XTEXT: Text 4 hours
txtFourHours=4 sata
#XTEXT: Text 6 hours
txtSixHours=6 sati
#XTEXT: Text 12 hours
txtTwelveHours=12 sati
#XTEXT: Text 1 day
txtOneDay=1 dan
#XFLD: Latency label
@autoRestartHead=Automatsko ponovno pokretanje
#XFLD
@lblConnectionName=Veza
#XFLD
@lblQualifiedName=Kvalificirani naziv
#XFLD
@lblSpaceName=Naziv prostora
#XFLD
@lblLocalSchemaName=Lokalna shema
#XFLD
@lblType=Tip objekta
#XFLD
@lblActivity=Aktivnost
#XFLD
@lblTableName=Naziv tablice
#XFLD
@lblBusinessName=Poslovni naziv
#XFLD
@lblTechnicalName=Tehnički naziv
#XFLD
@lblSpace=Prostor
#XFLD
@lblLabel=Oznaka
#XFLD
@lblDataType=Tip podataka
#XFLD
@lblDescription=Opis
#XFLD
@lblStorageType=Pohrana
#XFLD
@lblHTTPConnection=Generička HTTP veza
#XFLD
@lblAPISettings=Generičke postavke API-ja
#XFLD
@header=Zaglavlja
#XFLD
@lblInvoke=Pozivanje API-ja
#XFLD
@lblMethod=Metoda
#XFLD
@lblUrl=Osnovni URL
#XFLD
@lblAPIPath=Staza API-ja
#XFLD
@lblMode=Način
#XFLD
@lblCSRFToken=Potreban token CSRF 
#XFLD
@lblTokenURL=URL tokena CSRF
#XFLD
@csrfTokenInfoText=Ako se ne unese, upotrijebit će se osnovni URL i staza API-ja
#XFLD
@lblCSRF=Token CSRF 
#XFLD
@lblRequestBody=Tijelo zahtjeva
#XFLD
@lblFormat=Format
#XFLD
@lblResponse=Odgovor
#XFLD
@lblId=ID za dohvaćanje statusa
#XFLD
@Id=ID
#XFLD
@lblSuccessIndicator=Pokazatelj uspjeha
#XFLD
@lblErrorIndicator=Pokazatelj pogreške
#XFLD
@lblErrorReason=Razlog za pogrešku
#XFLD
@lblStatus=Status
#XFLD
@lblApiTestRun=Testno izvođenje API-ja
#XFLD
@lblRunStatus=Status izvođenja
#XFLD
@lblLastRan=Datum zadnjeg izvođenja
#XFLD
@lblTestRun=Testno izvođenje
#XFLD
@lblDefaultHeader=Zadana polja zaglavlja (parovi ključ-vrijednost)
#XFLD
@lblAdditionalHeader=Dodatno polje zaglavlja
#XFLD
@lblKey=Ključ
#XFLD
@lblEditJSON=Uredi JSON
#XFLD
@lblTasks=Zadaci
#XFLD
@lblRESTfullTask=API
#XFLD: add field button text
btnAddField=Dodaj polje zaglavlja
#XFLD: view Details link
@viewDetails=Prikaži pojedinosti
#XTOL
tooltipTxt=Više
#XTOL
delete=Izbriši
#XBTN: ok button text
btnOk=OK
#XBTN: cancel button text
btnCancel=Odustani
#XBTN: save button text
btnSave=Spremi
#XMSG Error: Object has duplicated technical name.
VAL_OBJECT_DUPLICATE_NAME=Objekt "{0}" već postoji u spremištu. Unesite drugi naziv.
#XMSG: loading message while opening task chain
loadTaskChain=Učitavanje lanca zadataka...
#model properties
#XFLD
@status_panel=Status izvođenja
#XFLD
@deploy_status_panel=Status uvođenja
#XFLD
@status_lbl=Status
#XFLD
@lblLastExecuted=Zadnje izvođenje
#XFLD
@lblNotExecuted=Još nije izvedeno
#XFLD
@lblNotDeployed=Nije uvedeno
#XFLD
errorDetailsTxt=Nije moguće dohvatiti status izvođenja
#XBTN: Schedule dropdown menu
SCHEDULE=Raspored
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Uredi raspored
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Izbriši raspored
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Stvori raspored
#XLNK
viewDetails=Prikaži pojedinosti
#XMSG: error message for reading execution status from backend
backendErrorMsg=Čini se da se podaci trenutačno ne učitavaju s poslužitelja. Pokušajte ponovo dohvatiti podatke.
#XFLD: Status text for Completed
@statusCompleted=Dovršeno
#XFLD: Status text for Running
@statusRunning=Izvodi se
#XFLD: Status text for Failed
@statusFailed=Nije uspjelo
#XFLD: Status text for Stopped
@statusStopped=Zaustavljeno
#XFLD: Status text for Stopping
@statusStopping=Zaustavljanje
#XFLD
@LoaderTitle=Učitavanje
#XFLD
#XFLD: Deploy status, dataflow has  been deployed / deployment date exists
@deployStatusActive=Uvedeno
@deployStatusRevised=Lokalna ažuriranja
@deployStatusFailed=Nije uspjelo
@deployStatusPending=Uvođenje...
@LoaderText=Dohvaćanje pojedinosti s poslužitelja
#XMSG
@msgDetailFetchError=Pogreška tijekom dohvaćanja pojedinosti s poslužitelja
#XFLD
@executeError=Pogreška
#XFLD
@executeWarning=Upozorenje
#XMSG
@executeConfirmDialog=Informacije
#XMSG
@executeunsavederror=Prije izvođenja lanca zadataka, spremite ga.
#XMSG
@executemodifiederror=U lancu zadataka postoje nespremljene promjene. Spremite ih.
#XMSG
@executerunningerror=Lanac zadataka trenutačno se izvodi. Pričekajte dok se trenutačno izvođenje ne dovrši prije nego što počnete s novim.
#XMSG
@btnExecuteAnyway=Ipak izvedi
#XMSG
@msgExecuteWithValidations=Lanac zadataka ima pogreške validacije. Izvođenje lanca zadataka može dovesti do pogreške.
#XMSG
@msgRunDeployedVersion=Postoje promjene za uvođenje. Bit će izvedena zadnja uvedena verzija lanca zadataka. Želite li nastaviti?
#XMSG
#XMSG
@navToMonitoring=Otvori u nadzoru lanca zadataka
#XMSG
txtOR=ILI
#XFLD
@preview=Pretpregled
#XMSG
txtand=I
#NOTR
@dateFormat=yyyy-MM-dd
#NOTR
@dateTimeFormatFilter=yyyy-MM-dd HH:mm:ss.
#NOTR
@timeFormat=HH:mm:ss
@lblColum=Stupac
#XFLD
@lblCondition=Uvjet
#XFLD
@lblValue=Vrijednost
#XMSG
@msgJsonInvalid=Lanac zadataka nije moguće spremiti jer postoje pogreške u JSON-u. Provjerite i razriješite.
#XTIT
@msgSaveFailTitle=Nevaljan JSON.
#XMSG
NOT_CHAINABLE=Objekt ''{0}'' nije moguće dodati lancu zadataka.
#XMSG
NOT_CHAINABLE_REMOTETABLE=Objekt ''{0}'': tip replikacije će se promijeniti.
#XMSG
searchTaskChain=Pretraži objekte

#XFLD
@txtTaskChain=Lanac zadataka
#XFLD
@txtRemoteTable=Udaljena tablica
#XFLD
@txtRemoveData=Ukloni replicirane podatke
#XFLD
@txtRemovePersist=Ukloni trajne podatke
#XFLD
@txtView=Prikaz
#XFLD
@txtDataFlow=Tok podataka
#XFLD
@txtIL=Inteligentno pretraživanje
#XFLD
@txtTransformationFlow=Tok transformacije
#XFLD
@txtReplicationFlow=Tok replikacije
#XFLD
@txtDeltaLocalTable=Lokalna tablica
#XFLD
@txtBWProcessChain=Lanac procesa BW
#XFLD
@txtSQLScriptProcedure=Postupak SQLScript
#XFLD
@txtAPI=API
#XFLD
@txtMerge=Spoji
#XFLD
@txtOptimize=Optimiziraj
#XFLD
@txtVacuum=Izbriši zapise

#XFLD
@txtRun=Izvedi
#XFLD
@txtPersist=Učini trajnim
#XFLD
@txtReplicate=Repliciraj
#XFLD
@txtDelete=Izbriši zapise s tipom promjene 'Izbrisano'
#XFLD
@txtRunTC=Izvedi lanac zadataka
#XFLD
@txtRunBW=Izvedi lanac procesa BW
#XFLD
@txtRunSQLScriptProcedure=Izvedi postupak SQLScript

#XFLD
@txtRunDataFlow=Izvedi tok podataka
#XFLD
@txtPersistView=Trajni prikaz
#XFLD
@txtReplicateTable=Repliciraj tablicu
#XFLD
@txtRunIL=Izvedi inteligentno pretraživanje
#XFLD
@txtRunTF=Izvrši tok transformacije
#XFLD
@txtRunRF=Izvedi tok replikacija
#XFLD
@txtRemoveReplicatedData=Ukloni replicirane podatke
#XFLD
@txtRemovePersistedData=Ukloni trajne podatke
#XFLD
@txtMergeData=Spoji
#XFLD
@txtOptimizeData=Optimiziraj
#XFLD
@txtVacuumData=Izbriši zapise
#XFLD
@txtRunAPI=Izvedi API

#XFLD storage type text
hdlfStorage=Datoteka

@statusNew=Nije uvedeno
@statusActive=Uvedeno
@statusRevised=Lokalna ažuriranja
@statusPending=Uvođenje...
@statusChangesToDeploy=Promjene za uvođenje
@statusDesignTimeError=Pogreška vremena dizajna
@statusRunTimeError=Pogreška vremena izvođenja

#XTIT
txtNodes=Objekti u lancu zadataka ({0})
#XBTN
@deleteNodes=Izbriši

#XMSG
@txtDropDataToDiagram=Povucite i ispustite objekte u dijagram.
#XMSG
@noData=Nema objekata

#XFLD
@txtTaskPosition=Pozicija zadatka

#input parameters and variables
#XFLD
lblInputParameters=Ulazni parametri
#XFLD
ip_name=Naziv
#XFLD
ip_value=Vrijednost
#XTEXT
@noObjectsFound=Nisu pronađeni objekti

#XMSG
@msgExecuteSuccess=Izvođenje lanca zadataka pokrenuto.
#XMSG
@msgExecuteFail=Izvođenje lanca zadataka nije uspjelo.
#XMSG
@msgDeployAndRunSuccess=Pokrenuto je uvođenje i izvođenje lanca zadataka.
#XMSG
@msgDeployAndRunFail=Uvođenje i izvođenje lanca zadataka nije uspjelo.
#XMSG
@titleExecuteBusy=Pričekajte.
#XMSG
@msgExecuteBusy=Pripremamo vaše podatke za pokretanje izvođenja lanca zadataka.
#XMSG
@msgAPITestRunSuccess=Testno izvođenje API-ja pokrenuto je.
#XMSG
@msgAPIExecuteBusy=Pripremamo vaše podatke za pokretanje testnog izvođenja API-ja.

#XTOL
txtOpenInEditor=Otvori u uređivaču
#XTOL
txtPreviewData=Pretpregled podataka

#datapreview
#XMSG
@msgDataPreviewNotSupp=Pretpregled podataka nije dostupan za ovaj objekt.

#XMSG Error: empty model
VAL_MODEL_EMPTY=Čini se da je vaš model prazan. Dodajte nekoliko objekata.
#XMSG Error: deploy model
@msgDeployBeforeRun=Prije izvođenja lanca zadataka, morate ga uvesti.
#BTN: close dialog
btnClose=Zatvori

#XMSG Object is not Deployed
VAL_NOT_DEPLOYED=Objekt ''{0}'' mora se uvesti za nastavak s lancem zadataka.
#XMSG Object is having runtime error
VAL_RUNTIME_ERROR=Objekt ''{0}'' vraća pogrešku vremena izvođenja. Provjerite objekt.
#XMSG Object is having design time error
VAL_DESIGNTIME_ERROR=Objekt ''{0}'' vraća pogrešku vremena dizajna. Provjerite objekt.
#XMSG Procedure Object has been deleted
VAL_DELETED_PROCEDURE=Izbrisani su sljedeći postupci: {0}
#XMSG New Parameter added to Procedure object
VAL_NEW_PARAMETER_ADDED=Novi parametri dodani postupku "{1}": "{0}". Ponovo implementiraj lanac zadataka.
#XMSG Parameter removed from Procedure object
VAL_PARAMETER_DELETED=Parametri uklonjeni iz postupka "{1}": "{0}". Ponovo implementiraj lanac zadataka.
#XMSG Parameter data type changed in Procedure object
VAL_PARAMETER_DATATYPE_MODIFIED=Tip podataka parametra "{0}" promijenjen iz "{1}" u "{2}" u postupku "{3}".
#XMSG Parameter length changed of specific datatype in Procedure object
VAL_PARAMETER_LENGTH_MODIFIED=Dužina parametra "{0}" promijenjena iz "{1}" u "{2}" u postupku "{3}".
#XMSG Parameter precision changed of Decimal datatype in Procedure object
VAL_PARAMETER_PRECISION_MODIFIED=Preciznost parametra "{0}" promijenjena iz "{1}" u "{2}" u postupku "{3}".
#XMSG Parameter scale changed of Decimal datatype in Procedure object
VAL_PARAMETER_SCALE_MODIFIED=Ljestvica parametra "{0}" promijenjena iz "{1}" u "{2}" u postupku "{3}".
#XMSG Parameter value not provided in Procedure object
VAL_SQL_SCRIPT_PROCEDURE_PARAMETER_VALUE_MISSING=Niste unijeli vrijednosti za ulazne parametre koji su potrebni za postupak "{0}": {1}"
#XMSG Remote Table activity changes to Remove Replicated Data
VAL_REMOVE_REPLICATED_DATA=Sljedeće izvođenje lanca zadataka promijenit će tip pristupa podacima, a podaci se više neće prenositi u stvarnom vremenu.
#XMSG Remote Table activity changes
REPLICATION_TYPE_CHANGED=Objekt "{0}": promijenit će se tip replikacije.

#XFLD
@lblStartNode=Početni čvor
#XFLD
@lblEndNode=Krajnji čvor
#XFLD
@linkTo={0} u {1}
#XTOL
@txtViewDetails=Prikaži pojedinosti

#XTOL
txtOpenImpactLineage=Analiza utjecaja i porijekla
#XFLD
@emailNotifications=Obavijesti e-poštom
#XFLD
@txtReset=Ponovo postavi
#XFLD
@emailMsgWarning=Zadani predložak e-pošte poslat će se kad je e-poruka prazna
#XFLD
@notificationSettings=Postavke obavijesti
#XFLD
@recipientEmailAddr=E-adresa primatelja
#XFLD
@emailSubject=Predmet e-poruke
@emailSubjectText=Lanac zadataka <TASKCHAIN_NAME> dovršen je sa statusom <STATUS>
#XFLD
@emailMessage=E-poruka
@emailMessageText=Poštovani korisniče,\n\n ovime vas obavješćujemo da je lanac zadataka:<TASKCHAIN_NAME> izveden u <START_TIME> završio sa statusom <STATUS>. Izvršenje je završeno u <END_TIME>.\n\nPojedinosti:\nProstor:<SPACE_NAME>\nPogreška:<ERROR_MESSAGE>
@selectEmailRecipientDialogTitle=Odaberite e-adresu primatelja
@tenantMembers=Članovi zakupca
@others=Drugi ({0})
@selectedEmailAddress=Odabrani primatelji
@add=Dodaj
@placeholder=Rezervirano mjesto
@description=Opis
@copyText=Kopiraj tekst
@taskchainDetailsPlaceholder=Rezervirana mjesta za pojedinosti lanca zadataka
@placeholderCopied=Rezervirano je mjesto kopirano
@invalidEmailInfo=Unesite točnu e-adresu
@maxMembersAlreadyAdded=Već ste dodali maksimum od 20 članova
@enterEmailAddress=Unesite e-adresu korisnika
@inCorrectPlaceHolder={0} nije očekivano rezervirano mjesto u tijelu e-poruke.
@nsOFF=Ne šalji nikakve obavijesti
@nsFAILED=Pošalji obavijest e-poštom samo kad se izvođenje dovrši s pogreškom
@nsCOMPLETED=Pošalji obavijest e-poštom samo kad se izvođenje dovrši uspješno
@nsANY=Pošalji e-poruku kad se izvođenje dovrši
@phStatus=Status lanca zadataka - USPJEH|NIJE USPJELO
@phTaskChainTName=Tehnički naziv lanca zadataka
@phTaskChainBName=Poslovni naziv lanca zadataka
@phLogId=ID zapisnika izvođenja
@phUser=Korisnik koji izvodi lanac zadataka
@phLogUILink=Veza na prikaz zapisnika lanca zadataka
@phStartTime=Vrijeme početka izvođenja
@phEndTime=Vrijeme završetka izvođenja
@phErrMsg=Prva poruka o pogrešci u zapisniku zadatka. Zapisnik je prazan u slučaju USPJEHA
@phSpaceName=Tehnički naziv prostora
@emailFormatError=Nevaljan format e-pošte
@emailFormatErrorInListText=Nevaljan format e-adrese unesen u popis članova koji nisu zakupci.
@emailSubjectTemplateText=Obavijest za lanac zadataka: $$taskChainName$$ - Prostor: $$spaceId$$ - Status: $$status$$
@emailMessageTemplateText=Pozdrav,\n\n Vaš je lanac zadataka s oznakom $$taskChainName$$ završio sa statusom $$status$$. \n Slijedi nekoliko drugih pojedinosti o lancu zadataka:\n - Tehnički naziv lanca zadataka: $$taskChainName$$ \n - ID zapisnika izvođenja lanca zadataka: $$logId$$ \n - Korisnik koji je izveo lanac zadataka: $$user$$ \n - Veza na prikaz zapisnika lanca zadataka: $$uiLink$$ \n - Vrijeme početka izvođenja lanca zadataka: $$startTime$$ \n - Vrijeme završetka izvođenja lanca zadataka: $$endTime$$ \n - Naziv prostora: $$spaceId$$ \n
@deleteEmailRecepient=Izbriši primatelja
@emailInputDisabledText=Uvedite lanac zadataka kako biste dodali primatelje e-pošte.
@tenantOwnerDomainMatchErrorText=Domena e-adrese ne podudara se s domenom vlasnika zakupca: {0}
@totalEmailIdLimitInfoText=Možete odabrati do 20 primatelja e-pošte, uključujući korisnike članove zakupca i druge primatelje.
@emailDomainInfoText=Prihvaćene su samo e-adrese s domenom: {0}.
@duplicateEmailErrorText=Na popisu postoje dvostruki primatelji e-pošte.

#XFLD Zorder Title
@txtZorderTitle=Stupci Apache Spark Z-Order 

#XFLD Zorder NoColumn
@txtZorderNoColumn=Stupci Z-Order nisu pronađeni

#XFLD Zorder Column PrimaryKey
@txtZorderColumnPrimaryKey=Primarni ključ

#XFLD
@lblOperators=Operatori
addNewSelector=Dodaj kao novi zadatak
parallelSelector=Dodaj kao paralelni zadatak
replaceSelector=Zamijeni postojeći zadatak
addparallelbranch=Dodaj kao paralelno grananje
addplaceholder=Dodaj rezervirano mjesto
addALLOperation=Operator ALL
addOROperation=Operator ANY
addplaceholdertocanvas=Dodaj rezervirano mjesto na platno
addplaceholderonselected=Dodaj rezervirano mjesto poslije odabranog zadatka
#XFLD: Add Parallel Branch to Selected Task
addparallelbranchonselected=Dodaj paralelno grananje poslije odabranog zadatka
addOperator=Dodaj operator
txtAdd=Dodaj
txtPlaceHolderText=Ovdje povuci ispusti zadatak
@lblLayout=Raspored

#XMSG
VAL_UNCONNECTED_TASK=Zadatak ''{0}'' nije povezan s lancem zadataka.
#XMSG
VAL_TASK_SHOULD_HAVE_ONE_INCOMING_LINK=Zadatak ''{0}'' treba imati samo jednu ulaznu vezu.
#XMSG
VAL_TASK_SHOULD_HAVE_INCOMING_LINK=Zadatak ''{0}'' treba imati jednu ulaznu vezu.
#XMSG
VAL_UNCONNECTED_OPERATOR=Operator ''{0}'' nije povezan s lancem zadataka.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ATLEAST_TWO_INCOMING_LINK=Operator ''{0}'' treba imati najmanje dvije ulazne veze.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ONE_OUT_LINK=Operator ''{0}'' treba imati najmanje jednu izlaznu vezu.
#XMSG
VAL_CIRCULAR_REFERENCE_EXITS=Kružna petlja postoji u lancu zadataka ''{0}''.
#XMSG
VAL_UNCONNECTED_BRANCH=Objekt/grananje ''{0}'' nije povezan s lancem zadataka.
#XMSG
VAL_TASK_TRIGGERED_PARALLEL_MORE_THAN_ONE_TIME=Zadatak ''{0}'' povezan je paralelno više puta. Za nastavak uklonite duplikate.


txtBegin=Početak
txtNodesInLink=Uključeni objekti
#XTOL Tooltip for a context button on diagram
openInNewTab=Otvori u novoj kartici
#XTOL Tooltip for a context button on diagram
txtConnectToTaskOperator=Povuci za povezivanje
@emailUpdateError=Pogreška pri ažuriranju popisa obavijesti e-poštom.

#XMSG
noTeamPrivilegeTxt=Nemate dopuštenje za prikaz popisa članova zakupca. Upotrijebite karticu Ostali za ručno dodavanje primatelja e-pošte.

#XFLD Package
@txtPackage=Paket

#XMSG warning for repository package assignment changed. {1} - package name
VAL_PACKAGE_CHANGED=Ovaj objekt dodijelili ste paketu "{1}". Kliknite Spremi za potvrdu i validaciju ove promjene. Uzmite u obzir da dodjelu paketu nakon spremanja nije moguće poništiti u ovom uređivaču.
#XMSG warning for repository package dependency issue.
MISSING_DEPENDENCY=Zavisnosti objekta ''{0}'' nije moguće riješiti u kontekstu paketa ''{1}''.

#Warning messages for the folders in the "Others" tab of task chain editor
#XMSG generic warning if getting the objects in a particular folder fails with unhandled errors
getNonRepoArtefactTypeDescendantsError=Došlo je do problema pri dohvaćanju objekata u mapi koju ste odabrali.
#BW Process Chains folder related warning messages
#XMSG warning if user does not have the necessary permission related to the SAP BW Bridge space
missingPrivilegeForBridgeSpaceError=Nemate potrebno dopuštenje za prikaz ili uključivanje lanaca procesa BW u lanac zadataka.
#XMSG warning if there was a problem retrieving the BW process chains due to bridge space membership, bridge connection issues or other errors
generalBridgeSpaceError=Došlo je do problema pri dohvaćanju lanaca procesa BW.
#XMSG warning if the BW API for retrieving the BW process chains from the SAP BW Bridge tenant fails
bwApiClientError=Došlo je do problema pri dohvaćanju lanaca procesa BW iz zakupca mosta za SAP BW.
#XMSG warning if BW process chains list is empty
bwApiEmptyListError=U zakupcu mosta za SAP BW nisu pronađeni lanci procesa BW.
#XMSG: Task log message for invalid OIDC configuration in the SAP BW Bridge tenant
bwBridgeOIDCConfigStatusInvalid=Autentifikacija za OpenID nije konfigurirana za ovog zakupca. Upotrijebite clientID "{0}" i tokenURL "{1}" da biste konfigurirali autentifikaciju za OIDC u zakupcu mosta za SAP BW kako je opisano u SAP bilješci 3536298.
#XMSG BW Process Chain Object has been deleted
VAL_DELETED_BW=Sljedeći lanci procesa BW možda su izbrisani iz zakupca mosta za SAP: {0}
#BW SQL Script Procedures folder related warning messages
#XFLD waring if the Procedure list is empty
procedureAPIEmptyList=Nisu stvoreni postupci ili ovlast EXECUTE nije odobrena za shemu Open SQL.
#Change digram orientations
changeOrientations=Promjena usmjerenja


# placeholder for the API Path
apiPath=Na primjer: /job/v1
# placeholder for the status API Path
statusAPIPath=Na primjer: /job/v1/{id}/status
# valueStateText for the API Path
apiPathRequired=Staza API-ja obvezna
#placeholder for the CSRF Token URL
csrfTokenURL=Podržan je samo HTTPS 
# Response Type 1
statusCode=Dobijte rezultat iz šifre statusa HTTP-a
# Response Type 2
locationHeader=Dobijte rezultat iz šifre statusa HTTP-a i zaglavlja lokacije
# Response Type 3
responseBody=Dobijte rezultat iz šifre statusa HTTP-a i tijela poruke
# placeholder for ID
idPlaceholder=Unesite stazu JSON-a
# placeholder for indicator value
indicatorValue=Unesite vrijednost
# Placeholder for key
keyPlaceholder=Unesite ključ
# Error message for missing key
KeyRequired=Ključ je obvezan
# Error message for invalid key format
invalidKeyFormat=Ključ zaglavlja koji ste unijeli nije dopušten. Valjana zaglavlja su:<ul><li>"prefer"</li><li>Zaglavlja koja počinju s "x-", osim"x-forwarded-host"</li><li>Zaglavlja koja sadrže alfanumeričke znakove, "-" ili "_"</li></ul>
# Error message for missing value
valueRequired=Vrijednost obvezna
# Error message for invalid characters in value
invalidValueCharacters=Zaglavlje sadržava nevaljane znakove. Posebni znakovi koji su dopušteni su:\t ";", ":", "-", "_", ",", "?", "/" i "*"
# Validation message for invoke api path
apiPathValidation=Unesite valjanu stazu API-ja, na primjer: /job/v1
# Validation message for JSON path
jsonPathValidation=Unesite valjanu stazu JSON-a
# Validation message for success/error indicator
indicatorValueValidation=Vrijednost pokazatelja mora početi alfanumeričkim znakom i može uključivati sljedeće posebne znakove:\t "-" i "_"
# Error message for JSON path
jsonPathRequired=Staza JSON-a obvezna
# Error message for invalid API Technical Name
invalidTechnicalName=Tehnički naziv sadržava nevaljane znakove
# Error message for empty Technical Name
emptyTechnicalName=Tehnički naziv obvezan
# Tooltip for codeEditor dialog
codeEditorTooltip=Otvorite prozor za uređivanje JSON-a
# Status Api path validation message
validationStatusAPIPath=Unesite valjanu stazu API-ja, na primjer: /job/v1/{id}/status
# CSRF token URL validation message
validationCSRFTokenURL=URL tokena CSRF mora početi s 'https://' i mora biti valjan URL
# Select a connection
selectConnection=Odaberite vezu
# Validation message for connection item error
connectionNotReplicated=Veza je trenutačno nevaljana za izvođenje zadataka API-ja. Otvorite aplikaciju "Veze" i ponovo unesite svoje vjerodajnice kako biste popravili HTTP vezu
# Validation message for API task properties wrong value or missing
VAL_REST_API_PROPERTIES_MISSING=Zadatak API-ja "{0}" ima netočne vrijednosti ili vrijednosti koje nedostaju za sljedeća svojstva: {1}
# Validation message for API connection api is throwing some error or selected connection has some warning
VAL_REST_API_CONNECTION=Zadatak API-ja "{0}" ima problem s HTTP vezom. Otvorite aplikaciju "Veze" i provjerite vezu
# Validation message for API task properties when delected connection
VAL_REST_API_CONNECTION_DELETED=Zadatak API-ja "{0}" ima izbrisanu vezu "{1}"
# Validation message for API task properties when connection cannot be consumed in the API task
VAL_REST_API_CONNECTION_CONSUMPTION_WARNING=Zadatak API-ja "{0}" ima vezu "{1}" koja se ne može upotrijebiti u izvođenju zadataka API-ja. Otvorite aplikaciju "Veze" i ponovo unesite svoje vjerodajnice kako biste uspostavili vezu za zadatke API-ja
# Information message if synchrnous mode is selected in the API Invocation then we don't need status information
modeInfoText=U sinkronom načinu statusni panel ne prikazuje se za pozivanje API-ja
# validation dialog button for Run API Test
saveAnyway=Ipak spremi
# validation message for technical name
technicalNameValidation=Tehnički naziv mora biti jedinstven u okviru lanca zadataka. Odaberite drugi tehnički naziv
# Connection error message
getHttpConnectionsError=Pozivanje HTTP veza nije uspjelo
# Msg for the API test run when the Task chain is not saved
taskChainNotSaved=Lanac zadataka mora se spremiti prije testnog izvođenja API-ja
# Msg failed to run API test run
@failedToRunAPI=Testno izvođenje API-ja nije uspjelo
# Msg for the API test run when its already in running state
apiTaskRunning=Probno izvođenje API-ja već je u tijeku. Želite li pokrenuti novo probno izvođenje?

topToBtm=Odozgo prema dolje
leftToRight=Slijeva nadesno

#XTIT Apache Spark Application Settings
txtHeaderApacheSettings=Postavke aplikacije Apache Spark 
#XFLD Use Default
txtUseSpaceDefault=Upotrijebi zadano
#XFLD Application
txtApplication=Aplikacija
#XFLD Define new settings for this Task
txtNewSettings=Definiraj nove postavke za ovaj zadatak

#XFLD Use Default
txtUseDefault=Upotrijebi zadano




