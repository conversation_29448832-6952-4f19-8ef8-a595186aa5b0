#XTOL Undo
@undo=Desfazer
#XTOL Redo
@redo=Refazer
#XTOL Delete Selected Symbol
@deleteNode=Excluir símbolo selecionado
#XTOL Zoom to Fit
@zoomToFit=Ajustar zoom à tela
#XTOL Auto Layout
@autoLayout=Layout automático
#XMSG
@welcomeText=Arraste objetos do painel à esquerda e solte-os nesta tela.
#XMSG
@txtNoData=Parece que você ainda não adicionou nenhum objeto.
#XMSG
VAL_ENTER_VALID_STRING_GEN=Insira uma string válida de comprimento menor que ou igual a {0}.
#XMSG
@noParametersMsg=Este procedimento não tem parâmetros de entrada.
#XMSG
@ip_enterValueMsg="{0}" (Executar procedimento de script SQL) tem parâmetros de entrada. Você pode definir um valor para cada um deles.
#XTOL
@validateModel=Mensagens de validação
#XTOL
@hierarchy=Hierarquia
#XTOL
@columnCount=Número de colunas
#XFLD
@yes=Sim
#XFLD
@no=Não
#XTIT Save Dialog param
@modelNameTaskChain=Cadeia de tarefas
#properties panel
@lblPropertyTitle=Propriedades
#XFLD
@lblGeneral=Geral
#XFLD : Setting
@lblSetting=Configurações
#XTXT: Delta Enabled Local Table Deletion
txtDeltaTableDeletion=Excluir todos os registros totalmente processados com tipo de alteração "Excluído" mais antigos que
#XTXT: Delta Enabled Local Table Deletion Input field description
desDeltaTableDeletion=Dias
#XFLD: Data Activation label
@lblDataActivation=Ativação de dados
#XFLD: Latency label
@latency=Latência
#XTEXT: Text for Latency dropdown
txtLatencyDefault=Padrão
#XTEXT: Text 1 hour
txtOneHour=1 hora
#XTEXT: Text 2 hours
txtTwoHours=2 horas
#XTEXT: Text 3 hours
txtThreeHours=3 horas
#XTEXT: Text 4 hours
txtFourHours=4 horas
#XTEXT: Text 6 hours
txtSixHours=6 horas
#XTEXT: Text 12 hours
txtTwelveHours=12 horas
#XTEXT: Text 1 day
txtOneDay=1 dia
#XFLD: Latency label
@autoRestartHead=Reinício automático
#XFLD
@lblConnectionName=Conexão
#XFLD
@lblQualifiedName=Nome qualificado
#XFLD
@lblSpaceName=Nome da área
#XFLD
@lblLocalSchemaName=Esquema local
#XFLD
@lblType=Tipo de objeto
#XFLD
@lblActivity=Atividade
#XFLD
@lblTableName=Nome da tabela
#XFLD
@lblBusinessName=Nome comercial
#XFLD
@lblTechnicalName=Nome técnico
#XFLD
@lblSpace=Área
#XFLD
@lblLabel=Etiqueta
#XFLD
@lblDataType=Tipo de dados
#XFLD
@lblDescription=Descrição
#XFLD
@lblStorageType=Armazenamento
#XFLD
@lblHTTPConnection=Conexão HTTP genérica
#XFLD
@lblAPISettings=Configurações da API genérica
#XFLD
@header=Cabeçalhos
#XFLD
@lblInvoke=Chamada de API
#XFLD
@lblMethod=Método
#XFLD
@lblUrl=URL base
#XFLD
@lblAPIPath=Caminho da API
#XFLD
@lblMode=Modo
#XFLD
@lblCSRFToken=Requer token do CSRF
#XFLD
@lblTokenURL=URL do token do CSRF
#XFLD
@csrfTokenInfoText=Se não inserido, o URL base e o caminho da API serão usados
#XFLD
@lblCSRF=Token do CSRF
#XFLD
@lblRequestBody=Corpo da solicitação
#XFLD
@lblFormat=Formato
#XFLD
@lblResponse=Resposta
#XFLD
@lblId=ID para recuperar status
#XFLD
@Id=ID
#XFLD
@lblSuccessIndicator=Indicador de sucesso
#XFLD
@lblErrorIndicator=Indicador de erro
#XFLD
@lblErrorReason=Motivo do erro
#XFLD
@lblStatus=Status
#XFLD
@lblApiTestRun=Execução de teste de API
#XFLD
@lblRunStatus=Status da execução
#XFLD
@lblLastRan=Última execução em
#XFLD
@lblTestRun=Execução de teste
#XFLD
@lblDefaultHeader=Campos de cabeçalho padrão (pares de chave-valor)
#XFLD
@lblAdditionalHeader=Campo de cabeçalho adicional
#XFLD
@lblKey=Chave
#XFLD
@lblEditJSON=Editar JSON
#XFLD
@lblTasks=Tarefas
#XFLD
@lblRESTfullTask=API
#XFLD: add field button text
btnAddField=Adicionar campo do cabeçalho
#XFLD: view Details link
@viewDetails=Exibir detalhes
#XTOL
tooltipTxt=Mais
#XTOL
delete=Excluir
#XBTN: ok button text
btnOk=OK
#XBTN: cancel button text
btnCancel=Cancelar
#XBTN: save button text
btnSave=Salvar
#XMSG Error: Object has duplicated technical name.
VAL_OBJECT_DUPLICATE_NAME=O objeto ''{0}'' já existe no repositório. Informe outro nome.
#XMSG: loading message while opening task chain
loadTaskChain=Carregando a cadeia de tarefas...
#model properties
#XFLD
@status_panel=Status da execução
#XFLD
@deploy_status_panel=Status da implementação
#XFLD
@status_lbl=Status
#XFLD
@lblLastExecuted=Última execução
#XFLD
@lblNotExecuted=Não executado
#XFLD
@lblNotDeployed=Não implementado
#XFLD
errorDetailsTxt=Não foi possível buscar o status da execução
#XBTN: Schedule dropdown menu
SCHEDULE=Programar
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Editar programação
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Excluir programação
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Criar programação
#XLNK
viewDetails=Exibir detalhes
#XMSG: error message for reading execution status from backend
backendErrorMsg=Parece que os dados não estão sendo carregados do servidor no momento. Tente buscá-los novamente.
#XFLD: Status text for Completed
@statusCompleted=Concluído
#XFLD: Status text for Running
@statusRunning=Em execução
#XFLD: Status text for Failed
@statusFailed=Com falha
#XFLD: Status text for Stopped
@statusStopped=Interrompido
#XFLD: Status text for Stopping
@statusStopping=Interrompendo
#XFLD
@LoaderTitle=Carregando
#XFLD
#XFLD: Deploy status, dataflow has  been deployed / deployment date exists
@deployStatusActive=Implementado
@deployStatusRevised=Atualizações locais
@deployStatusFailed=Com falha
@deployStatusPending=Implementando...
@LoaderText=Buscando detalhes do servidor
#XMSG
@msgDetailFetchError=Erro ao buscar detalhes do servidor
#XFLD
@executeError=Erro
#XFLD
@executeWarning=Aviso
#XMSG
@executeConfirmDialog=Informações
#XMSG
@executeunsavederror=Salve a cadeia de tarefas antes de executá-la.
#XMSG
@executemodifiederror=Há alterações não salvas na cadeia de tarefas. Salve-a.
#XMSG
@executerunningerror=A cadeia de tarefas está em execução no momento. Aguarde até que a execução atual esteja concluída antes de começar uma nova.
#XMSG
@btnExecuteAnyway=Executar mesmo assim
#XMSG
@msgExecuteWithValidations=A cadeia de tarefas tem erros de validação. Executar a cadeia de tarefas pode resultar em falha.
#XMSG
@msgRunDeployedVersion=Existem alterações a serem implementadas. A última versão implementada da cadeia de tarefas será executada. Deseja continuar?
#XMSG
#XMSG
@navToMonitoring=Abrir no monitor da cadeia de tarefas
#XMSG
txtOR=OU
#XFLD
@preview=Visualizar
#XMSG
txtand=E
#NOTR
@dateFormat=yyyy-MM-dd
#NOTR
@dateTimeFormatFilter=yyyy-MM-dd HH:mm:ss.
#NOTR
@timeFormat=HH:mm:ss
@lblColum=Coluna
#XFLD
@lblCondition=Condição
#XFLD
@lblValue=Valor
#XMSG
@msgJsonInvalid=Não foi possível salvar a cadeia de tarefas porque há erros no JSON. Verifique-os e resolva-os.
#XTIT
@msgSaveFailTitle=JSON inválido.
#XMSG
NOT_CHAINABLE=Objeto ''{0}'' não pode ser adicionado à cadeia de tarefas.
#XMSG
NOT_CHAINABLE_REMOTETABLE=Objeto "{0}": o tipo de replicação será alterado.
#XMSG
searchTaskChain=Procurar objetos

#XFLD
@txtTaskChain=Cadeia de tarefas
#XFLD
@txtRemoteTable=Tabela remota
#XFLD
@txtRemoveData=Remover dados replicados
#XFLD
@txtRemovePersist=Remover dados persistidos
#XFLD
@txtView=Visão
#XFLD
@txtDataFlow=Fluxo de dados
#XFLD
@txtIL=Pesquisa inteligente
#XFLD
@txtTransformationFlow=Fluxo de transformação
#XFLD
@txtReplicationFlow=Fluxo de replicação
#XFLD
@txtDeltaLocalTable=Tabela local
#XFLD
@txtBWProcessChain=Cadeia de processos BW
#XFLD
@txtSQLScriptProcedure=Procedimento de script SQL
#XFLD
@txtAPI=API
#XFLD
@txtMerge=Mesclar
#XFLD
@txtOptimize=Otimizar
#XFLD
@txtVacuum=Excluir registros

#XFLD
@txtRun=Executar
#XFLD
@txtPersist=Persistir
#XFLD
@txtReplicate=Replicar
#XFLD
@txtDelete=Excluir registros com tipo de alteração 'Excluído'
#XFLD
@txtRunTC=Executar cadeia de tarefas
#XFLD
@txtRunBW=Executar cadeia de processos BW
#XFLD
@txtRunSQLScriptProcedure=Executar procedimento de script SQL

#XFLD
@txtRunDataFlow=Executar fluxo de dados
#XFLD
@txtPersistView=Persistir visão
#XFLD
@txtReplicateTable=Replicar tabela
#XFLD
@txtRunIL=Executar pesquisa inteligente
#XFLD
@txtRunTF=Executar fluxo de transformação
#XFLD
@txtRunRF=Executar fluxo de replicação
#XFLD
@txtRemoveReplicatedData=Remover dados replicados
#XFLD
@txtRemovePersistedData=Remover dados persistidos
#XFLD
@txtMergeData=Mesclar
#XFLD
@txtOptimizeData=Otimizar
#XFLD
@txtVacuumData=Excluir registros
#XFLD
@txtRunAPI=Executar API

#XFLD storage type text
hdlfStorage=Arquivo

@statusNew=Não implementado
@statusActive=Implementado
@statusRevised=Atualizações locais
@statusPending=Implementando...
@statusChangesToDeploy=Alterações a serem implementadas
@statusDesignTimeError=Erro no tempo de design
@statusRunTimeError=Erro no tempo de execução

#XTIT
txtNodes=Objetos na cadeia de tarefas ({0})
#XBTN
@deleteNodes=Excluir

#XMSG
@txtDropDataToDiagram=Arraste os objetos e solte-os no diagrama.
#XMSG
@noData=Nenhum objeto

#XFLD
@txtTaskPosition=Posição da tarefa

#input parameters and variables
#XFLD
lblInputParameters=Parâmetros de entrada
#XFLD
ip_name=Nome
#XFLD
ip_value=Valor
#XTEXT
@noObjectsFound=Nenhum objeto encontrado

#XMSG
@msgExecuteSuccess=Execução da cadeia de tarefas iniciada.
#XMSG
@msgExecuteFail=Falha ao executar a cadeia de tarefas.
#XMSG
@msgDeployAndRunSuccess=Implementação e execução da cadeia de tarefas iniciadas.
#XMSG
@msgDeployAndRunFail=Falha ao implementar e executar a cadeia de tarefas.
#XMSG
@titleExecuteBusy=Aguarde.
#XMSG
@msgExecuteBusy=Estamos preparando seus dados para iniciar a execução da cadeia de tarefas.
#XMSG
@msgAPITestRunSuccess=Execução de teste de API iniciada.
#XMSG
@msgAPIExecuteBusy=Estamos preparando seus dados para iniciar a execução do teste de API.

#XTOL
txtOpenInEditor=Abrir no editor
#XTOL
txtPreviewData=Visualizar dados

#datapreview
#XMSG
@msgDataPreviewNotSupp=A visualização de dados não está disponível para este objeto.

#XMSG Error: empty model
VAL_MODEL_EMPTY=Parece que seu modelo está vazio. Adicione alguns objetos.
#XMSG Error: deploy model
@msgDeployBeforeRun=Você precisa implementar a cadeia de tarefas antes de executá-la.
#BTN: close dialog
btnClose=Fechar

#XMSG Object is not Deployed
VAL_NOT_DEPLOYED=O objeto ''{0}'' deve ser implementado para continuar com a cadeia de tarefas.
#XMSG Object is having runtime error
VAL_RUNTIME_ERROR=O objeto "{0}" retorna um erro de tempo de execução. Verifique o objeto.
#XMSG Object is having design time error
VAL_DESIGNTIME_ERROR=O objeto "{0}" retorna um erro de tempo de design. Verifique o objeto.
#XMSG Procedure Object has been deleted
VAL_DELETED_PROCEDURE=Os seguintes procedimentos foram excluídos: {0}
#XMSG New Parameter added to Procedure object
VAL_NEW_PARAMETER_ADDED=Novos parâmetros adicionados ao procedimento "{1}": "{0}". Implemente novamente a cadeia de tarefas.
#XMSG Parameter removed from Procedure object
VAL_PARAMETER_DELETED=Parâmetros removidos do procedimento "{1}": "{0}". Implemente novamente a cadeia de tarefas.
#XMSG Parameter data type changed in Procedure object
VAL_PARAMETER_DATATYPE_MODIFIED=Tipo de dados do parâmetro "{0}" alterado de "{1}" para "{2}" no procedimento "{3}".
#XMSG Parameter length changed of specific datatype in Procedure object
VAL_PARAMETER_LENGTH_MODIFIED=Comprimento do parâmetro "{0}" alterado de "{1}" para "{2}" no procedimento "{3}".
#XMSG Parameter precision changed of Decimal datatype in Procedure object
VAL_PARAMETER_PRECISION_MODIFIED=Precisão do parâmetro "{0}" alterado de "{1}" para "{2}" no procedimento "{3}".
#XMSG Parameter scale changed of Decimal datatype in Procedure object
VAL_PARAMETER_SCALE_MODIFIED=Escala do parâmetro "{0}" alterado de "{1}" para "{2}" no procedimento "{3}".
#XMSG Parameter value not provided in Procedure object
VAL_SQL_SCRIPT_PROCEDURE_PARAMETER_VALUE_MISSING=Você não inseriu nenhum valor para os parâmetros de entrada necessários para o procedimento "{0}": "{1}"
#XMSG Remote Table activity changes to Remove Replicated Data
VAL_REMOVE_REPLICATED_DATA=A próxima execução de uma cadeia de tarefas alterará o tipo de acesso a dados, e os dados não serão mais carregados em tempo real.
#XMSG Remote Table activity changes
REPLICATION_TYPE_CHANGED=Objeto "{0}": o tipo de replicação será alterado.

#XFLD
@lblStartNode=Nó inicial
#XFLD
@lblEndNode=Nó final
#XFLD
@linkTo=De {0} para {1}
#XTOL
@txtViewDetails=Exibir detalhes

#XTOL
txtOpenImpactLineage=Análise de impacto e linhagem
#XFLD
@emailNotifications=Notificações por e-mail
#XFLD
@txtReset=Redefinir
#XFLD
@emailMsgWarning=O e-mail de template padrão será enviado quando a mensagem de e-mail estiver vazia.
#XFLD
@notificationSettings=Configurações de notificação
#XFLD
@recipientEmailAddr=Endereço de e-mail do destinatário
#XFLD
@emailSubject=Assunto do e-mail
@emailSubjectText=Cadeia de tarefas <TASKCHAIN_NAME> concluída com status <STATUS>
#XFLD
@emailMessage=Mensagem de e-mail
@emailMessageText=Prezado usuário,\n\n Esta mensagem é para notificar que a cadeia de tarefas:<TASKCHAIN_NAME> executada às <START_TIME> foi concluída com status <STATUS>. A execução terminou às <END_TIME>.\n\nDetalhes:\nÁrea:<SPACE_NAME>\nErro:<ERROR_MESSAGE>
@selectEmailRecipientDialogTitle=Selecionar endereço de e-mail do destinatário
@tenantMembers=Membros do locatário
@others=Outros ({0})
@selectedEmailAddress=Destinatários selecionados
@add=Adicionar
@placeholder=Espaço reservado
@description=Descrição
@copyText=Copiar texto
@taskchainDetailsPlaceholder=Espaços reservados para detalhes da cadeia de tarefas
@placeholderCopied=Espaço reservado copiado
@invalidEmailInfo=Inserir endereço de e-mail correto
@maxMembersAlreadyAdded=Você já adicionou o máximo de 20 membros
@enterEmailAddress=Inserir endereço de e-mail do usuário
@inCorrectPlaceHolder={0} não é um espaço reservado esperado no corpo do e-mail.
@nsOFF=Não enviar nenhuma notificação
@nsFAILED=Só enviar notificação por e-mail quando execução for concluída com erro
@nsCOMPLETED=Só enviar notificação por e-mail quando execução for concluída com sucesso
@nsANY=Enviar e-mail quando execução for concluída
@phStatus=Status da cadeia de tarefas - SUCESSO|FALHA
@phTaskChainTName=Nome técnico da cadeia de tarefas
@phTaskChainBName=Nome comercial da cadeia de tarefas
@phLogId=ID de log ID da execução
@phUser=Usuário que está executando a cadeia de tarefas
@phLogUILink=Link para exibição de log da cadeia de tarefas
@phStartTime=Hora de início da execução
@phEndTime=Hora de término da execução
@phErrMsg=Primeira mensagem de erro no log de tarefas. O log estará vazio em caso de SUCESSO.
@phSpaceName=Nome técnico da área
@emailFormatError=Formato de e-mail inválido
@emailFormatErrorInListText=Formato de e-mail inválido inserido na lista de membros não locatário.
@emailSubjectTemplateText=Notificação para cadeia de tarefas: $$taskChainName$$ – Área: $$spaceId$$ – Status: $$status$$
@emailMessageTemplateText=Olá,\n\n Sua cadeia de tarefas denominada $$taskChainName$$ foi concluída com status $$status$$. \n Aqui estão alguns outros detalhes sobre a cadeia de tarefas:\n – Nome técnico da cadeia de tarefas: $$taskChainName$$ \n – ID do log da execução da cadeia de tarefas: $$logId$$ \n – Usuário que executou a cadeia de tarefas: $$user$$ \n – Link para exibir o log da cadeia de tarefas: $$uiLink$$ \n – Hora de início da execução da cadeia de tarefas: $$startTime$$ \n – Hora de término da execução da cadeia de tarefas: $$endTime$$ \n – Nome da área: $$spaceId$$ \n
@deleteEmailRecepient=Excluir destinatário
@emailInputDisabledText=Implemente a cadeia de tarefas para adicionar destinatários de e-mail.
@tenantOwnerDomainMatchErrorText=O domínio de endereço de e-mail não corresponde ao domínio do proprietário do locatário: {0}
@totalEmailIdLimitInfoText=Você pode selecionar até 20 destinatários de e-mail, incluindo usuários membros do locatário e outros destinatários.
@emailDomainInfoText=Só são aceitos endereços de e-mail com domínio: {0}.
@duplicateEmailErrorText=Há destinatários de e-mail duplicados na lista.

#XFLD Zorder Title
@txtZorderTitle=Colunas z-order do Apache Spark

#XFLD Zorder NoColumn
@txtZorderNoColumn=Nenhuma coluna z-order encontrada

#XFLD Zorder Column PrimaryKey
@txtZorderColumnPrimaryKey=Chave primária

#XFLD
@lblOperators=Operadores
addNewSelector=Adicionar como nova tarefa
parallelSelector=Adicionar como tarefa paralela
replaceSelector=Substituir tarefa existente
addparallelbranch=Adicionar como ramificação paralela
addplaceholder=Adicionar espaço reservado
addALLOperation=Operador TODOS
addOROperation=Operador QUALQUER
addplaceholdertocanvas=Adicionar espaço reservado à tela
addplaceholderonselected=Adicionar espaço reservado depois da tarefa selecionada
#XFLD: Add Parallel Branch to Selected Task
addparallelbranchonselected=Adicionar ramificação paralela depois da tarefa selecionada
addOperator=Adicionar operador
txtAdd=Adicionar
txtPlaceHolderText=Arraste e solte a tarefa aqui
@lblLayout=Layout

#XMSG
VAL_UNCONNECTED_TASK=A tarefa "{0}" não está conectada à cadeia de tarefas.
#XMSG
VAL_TASK_SHOULD_HAVE_ONE_INCOMING_LINK=A tarefa "{0}" só pode ter um link de entrada.
#XMSG
VAL_TASK_SHOULD_HAVE_INCOMING_LINK=A tarefa "{0}" deve ter um link de entrada.
#XMSG
VAL_UNCONNECTED_OPERATOR=O operador "{0}" não está conectado à cadeia de tarefas.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ATLEAST_TWO_INCOMING_LINK=O operador "{0}" deve ter, pelo menos, dois links de entrada.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ONE_OUT_LINK=O operador "{0}" deve ter, pelo menos, um link de saída.
#XMSG
VAL_CIRCULAR_REFERENCE_EXITS=Existe um loop circular na cadeia de tarefas "{0}".
#XMSG
VAL_UNCONNECTED_BRANCH=O objeto/ramificação "{0}" não está conectado à cadeia de tarefas.
#XMSG
VAL_TASK_TRIGGERED_PARALLEL_MORE_THAN_ONE_TIME=A tarefa "{0}" está conectada mais de uma vez em paralelo. Remova as duplicações para continuar.


txtBegin=Início
txtNodesInLink=Objetos envolvidos
#XTOL Tooltip for a context button on diagram
openInNewTab=Abrir em nova guia
#XTOL Tooltip for a context button on diagram
txtConnectToTaskOperator=Arrastar para conectar
@emailUpdateError=Erro ao atualizar a lista de notificações por e-mail

#XMSG
noTeamPrivilegeTxt=Você não tem permissão para ver uma lista de membros do locatário. Use a guia Outros para adicionar destinatários de e-mail manualmente.

#XFLD Package
@txtPackage=Pacote

#XMSG warning for repository package assignment changed. {1} - package name
VAL_PACKAGE_CHANGED=Você atribuiu este objeto ao pacote ''{1}''. Clique em Salvar para confirmar e validar essa alteração. Observe que a atribuição a um pacote não pode ser desfeita neste editor depois que ela é salva.
#XMSG warning for repository package dependency issue.
MISSING_DEPENDENCY=Não é possível resolver as dependências do objeto ''{0}'' no contexto do pacote ''{1}''.

#Warning messages for the folders in the "Others" tab of task chain editor
#XMSG generic warning if getting the objects in a particular folder fails with unhandled errors
getNonRepoArtefactTypeDescendantsError=Ocorreu um problema na recuperação de objetos da pasta que você selecionou.
#BW Process Chains folder related warning messages
#XMSG warning if user does not have the necessary permission related to the SAP BW Bridge space
missingPrivilegeForBridgeSpaceError=Você não tem a permissão necessária para visualizar ou incluir cadeias de processos BW em uma cadeia de tarefas.
#XMSG warning if there was a problem retrieving the BW process chains due to bridge space membership, bridge connection issues or other errors
generalBridgeSpaceError=Ocorreu um problema ao recuperar as cadeias de processos BW.
#XMSG warning if the BW API for retrieving the BW process chains from the SAP BW Bridge tenant fails
bwApiClientError=Ocorreu um problema ao recuperar as cadeias de processos BW do locatário de ponte do SAP BW.
#XMSG warning if BW process chains list is empty
bwApiEmptyListError=Nenhuma cadeia de processos BW foi encontrada no locatário da ponte do SAP BW.
#XMSG: Task log message for invalid OIDC configuration in the SAP BW Bridge tenant
bwBridgeOIDCConfigStatusInvalid=A autenticação de OpenID não está configurada para esse locatário. Use o clientID "{0}" e o tokenURL "{1}" para configurar a autenticação de OpenID no locatário da ponte do SAP BW conforme descrito na nota SAP 3536298.
#XMSG BW Process Chain Object has been deleted
VAL_DELETED_BW=As seguintes cadeias de processo BW podem ter sido excluídas do locatário da ponte do SAP: {0}
#BW SQL Script Procedures folder related warning messages
#XFLD waring if the Procedure list is empty
procedureAPIEmptyList=Não foi criado nenhum procedimento ou o privilégio EXECUTE não foi concedido para o esquema Open SQL.
#Change digram orientations
changeOrientations=Alterar orientações


# placeholder for the API Path
apiPath=Por exemplo: /job/v1
# placeholder for the status API Path
statusAPIPath=Por exemplo: /job/v1/{id}/status
# valueStateText for the API Path
apiPathRequired=Caminho da API é obrigatório
#placeholder for the CSRF Token URL
csrfTokenURL=Somente HTTPS é suportado
# Response Type 1
statusCode=Obter resultado do código de status HTTP
# Response Type 2
locationHeader=Obter resultado do código de status HTTP e cabeçalho de local
# Response Type 3
responseBody=Obter resultado do código de status HTTP e corpo da resposta
# placeholder for ID
idPlaceholder=Inserir caminho JSON
# placeholder for indicator value
indicatorValue=Inserir valor
# Placeholder for key
keyPlaceholder=Inserir chave
# Error message for missing key
KeyRequired=A chave é obrigatória
# Error message for invalid key format
invalidKeyFormat=A chave de cabeçalho que você inseriu não é permitida. Cabeçalhos válidos são:<ul><li>"prefer"</li><li>Cabeçalhos que comecem com "x-", exceto "x-forwarded-host"</li><li>Cabeçalhos que contenham caracteres alfanuméricos , "-" ou "_"</li></ul>
# Error message for missing value
valueRequired=O valor é obrigatório
# Error message for invalid characters in value
invalidValueCharacters=O cabeçalho contém caracteres inválidos. Os caracteres especiais permitidos são:\t ";", ":", "-", "_", ",", "?", "/" e "*"
# Validation message for invoke api path
apiPathValidation=Insira um caminho de API válido, por exemplo: /job/v1
# Validation message for JSON path
jsonPathValidation=Insira um caminho JSON válido
# Validation message for success/error indicator
indicatorValueValidation=O valor de indicador deve começar com um caractere alfanumérico e pode incluir os seguintes caracteres especiais:\t "-" e "_"
# Error message for JSON path
jsonPathRequired=Caminho JSON é obrigatório
# Error message for invalid API Technical Name
invalidTechnicalName=O nome técnico contém caracteres inválidos
# Error message for empty Technical Name
emptyTechnicalName=Nome técnico é obrigatório
# Tooltip for codeEditor dialog
codeEditorTooltip=Abrir janela de edição de JSON
# Status Api path validation message
validationStatusAPIPath=Insira um caminho de API válido, por exemplo: /job/v1/{id}/status
# CSRF token URL validation message
validationCSRFTokenURL=O URL do token do CSRF deve começar com 'https://' e ser um URL válido
# Select a connection
selectConnection=Selecione uma conexão
# Validation message for connection item error
connectionNotReplicated=Conexão atualmente inválida para executar tarefas de API. Abra o app "Conexões" e reinsira suas credenciais para corrigir a conexão HTTP
# Validation message for API task properties wrong value or missing
VAL_REST_API_PROPERTIES_MISSING=A tarefa de API "{0}" tem valores incorretos ou ausentes para as seguintes propriedades: {1}
# Validation message for API connection api is throwing some error or selected connection has some warning
VAL_REST_API_CONNECTION=A tarefa de API "{0}" tem um problema na conexão HTTP. Abra o app "Conexões" e verifique a conexão
# Validation message for API task properties when delected connection
VAL_REST_API_CONNECTION_DELETED=A tarefa de API "{0}" tem uma conexão "{1}" excluída
# Validation message for API task properties when connection cannot be consumed in the API task
VAL_REST_API_CONNECTION_CONSUMPTION_WARNING=A tarefa de API "{0}" tem uma conexão "{1}" que não pode ser usada para executar tarefas de API. Abra o app "Conexões" e insira novamente suas credenciais para estabelecer a conectividade das tarefas de API.
# Information message if synchrnous mode is selected in the API Invocation then we don't need status information
modeInfoText=No modo síncrono, o painel de status não é exibido para chamadas de API
# validation dialog button for Run API Test
saveAnyway=Salvar mesmo assim
# validation message for technical name
technicalNameValidation=O nome técnico deve ser exclusivo dentro da cadeia de tarefas. Escolha outro nome técnico
# Connection error message
getHttpConnectionsError=Falha ao obter as conexões HTTP
# Msg for the API test run when the Task chain is not saved
taskChainNotSaved=A cadeia de tarefas deve ser salva antes da execução do teste de API
# Msg failed to run API test run
@failedToRunAPI=Falha ao executar o teste de API
# Msg for the API test run when its already in running state
apiTaskRunning=Uma execução de teste API já está em andamento. Iniciar uma nova execução de teste?

topToBtm=De cima para baixo
leftToRight=Da esquerda para a direita

#XTIT Apache Spark Application Settings
txtHeaderApacheSettings=Configurações do aplicativo Apache Spark
#XFLD Use Default
txtUseSpaceDefault=Usar padrão
#XFLD Application
txtApplication=Aplicativo
#XFLD Define new settings for this Task
txtNewSettings=Definir novas configurações para esta tarefa

#XFLD Use Default
txtUseDefault=Usar padrão




