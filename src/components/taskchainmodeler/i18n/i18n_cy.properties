#XTOL Undo
@undo=Dadwneud
#XTOL Redo
@redo=Ailwneud
#XTOL Delete Selected Symbol
@deleteNode=Dileu Symbol a Ddewiswyd
#XTOL Zoom to Fit
@zoomToFit=Nesáu/Pellhau i Ffitio
#XTOL Auto Layout
@autoLayout=Cynllun Awtomatig
#XMSG
@welcomeText=Gallwch lusgo a gollwng gwrthrychau o'r panel ar y chwith i'r cynfas hwn.
#XMSG
@txtNoData=Dydych chi heb ychwanegu gwrthrych ar hyn o bryd.
#XMSG
VAL_ENTER_VALID_STRING_GEN=Rhowch linyn dilys sy''n llai na {0} neu''n gyfwerth â hynny.
#XMSG
@noParametersMsg=Nid oes gan y weithdrefn hon baramedrau mewnbwn.
#XMSG
@ip_enterValueMsg=Mae gan "{0}" (Rhedeg Gweithdrefn Sgript SQL) baramedrau mewnbwn. Gallwch osod gwerth ar gyfer pob un ohonynt.
#XTOL
@validateModel=Negeseuon Dilysu
#XTOL
@hierarchy=Hierarchaeth
#XTOL
@columnCount=Nifer y Colofnau
#XFLD
@yes=Iawn
#XFLD
@no=Na
#XTIT Save Dialog param
@modelNameTaskChain=Cadwyn Dasgau
#properties panel
@lblPropertyTitle=Priodweddau
#XFLD
@lblGeneral=Cyffredinol
#XFLD : Setting
@lblSetting=Gosodiadau
#XTXT: Delta Enabled Local Table Deletion
txtDeltaTableDeletion=Dileu pob cofnod sydd wedi’i brosesu’n llawn gyda Math o Newid ‘Wedi Dileu’ sy’n hŷn na
#XTXT: Delta Enabled Local Table Deletion Input field description
desDeltaTableDeletion=Diwrnod
#XFLD: Data Activation label
@lblDataActivation=Gweithredu Data
#XFLD: Latency label
@latency=Wrthi'n prosesu
#XTEXT: Text for Latency dropdown
txtLatencyDefault=Diofyn
#XTEXT: Text 1 hour
txtOneHour=1 Awr
#XTEXT: Text 2 hours
txtTwoHours=2 Awr
#XTEXT: Text 3 hours
txtThreeHours=3 Awr
#XTEXT: Text 4 hours
txtFourHours=4 Awr
#XTEXT: Text 6 hours
txtSixHours=6 Awr
#XTEXT: Text 12 hours
txtTwelveHours=12 Awr
#XTEXT: Text 1 day
txtOneDay=1 Diwrnod
#XFLD: Latency label
@autoRestartHead=Ailgychwyn yn Awtomatig
#XFLD
@lblConnectionName=Cysylltiad
#XFLD
@lblQualifiedName=Enw Cymwysedig
#XFLD
@lblSpaceName=Enw Gofod
#XFLD
@lblLocalSchemaName=Sgema Lleol
#XFLD
@lblType=Math o Wrthrych
#XFLD
@lblActivity=Gweithgaredd
#XFLD
@lblTableName=Enw Tabl
#XFLD
@lblBusinessName=Enw Busnes
#XFLD
@lblTechnicalName=Enw Technegol
#XFLD
@lblSpace=Gofod
#XFLD
@lblLabel=Label
#XFLD
@lblDataType=Math o Ddata
#XFLD
@lblDescription=Disgrifiad
#XFLD
@lblStorageType=Lle Storio
#XFLD
@lblHTTPConnection=Cysylltiad HTTP Cyffredin
#XFLD
@lblAPISettings=Gosodiadau API Cyffredin
#XFLD
@header=Penynnau
#XFLD
@lblInvoke=Galwad API
#XFLD
@lblMethod=Dull
#XFLD
@lblUrl=URL Sylfaen
#XFLD
@lblAPIPath=Llwybr API
#XFLD
@lblMode=Modd
#XFLD
@lblCSRFToken=Mae angen Tocyn CSRF
#XFLD
@lblTokenURL=URL Tocyn CSRF
#XFLD
@csrfTokenInfoText=Os nad yw'n cael ei roi, bydd yr URL Sylfaen a'r llwybr API yn cael eu defnyddio
#XFLD
@lblCSRF=Tocyn CSRF
#XFLD
@lblRequestBody=Corff Cais
#XFLD
@lblFormat=Fformat
#XFLD
@lblResponse=Ymateb
#XFLD
@lblId=ID i adfer y statws
#XFLD
@Id=ID
#XFLD
@lblSuccessIndicator=Dangosydd Llwyddiant
#XFLD
@lblErrorIndicator=Dangosydd Gwall
#XFLD
@lblErrorReason=Rheswm dros y gwall
#XFLD
@lblStatus=Statws
#XFLD
@lblApiTestRun=Rhediad Arbrofol API
#XFLD
@lblRunStatus=Statws y Rhediad
#XFLD
@lblLastRan=Rhedwyd diwethaf ar
#XFLD
@lblTestRun=Rhediad Arbrofol
#XFLD
@lblDefaultHeader=Meysydd Pennawd Diofyn (parau Allwedd-Gwerth)
#XFLD
@lblAdditionalHeader=Maes Penyn Ychwanegol
#XFLD
@lblKey=Allwedd
#XFLD
@lblEditJSON=Golygu JSON
#XFLD
@lblTasks=Tasgau
#XFLD
@lblRESTfullTask=API
#XFLD: add field button text
btnAddField=Ychwanegu Maes Penyn
#XFLD: view Details link
@viewDetails=Gweld Manylion
#XTOL
tooltipTxt=Mwy
#XTOL
delete=Dileu
#XBTN: ok button text
btnOk=Iawn
#XBTN: cancel button text
btnCancel=Canslo
#XBTN: save button text
btnSave=Cadw
#XMSG Error: Object has duplicated technical name.
VAL_OBJECT_DUPLICATE_NAME=Mae gwrthrych "{0}" eisoes yn bodoli yn yr ystorfa. Rhowch enw arall.
#XMSG: loading message while opening task chain
loadTaskChain=Wrthi'n llwytho'r gadwyn dasgau...
#model properties
#XFLD
@status_panel=Statws Rhedeg
#XFLD
@deploy_status_panel=Statws Gosod
#XFLD
@status_lbl=Statws
#XFLD
@lblLastExecuted=Rhediad Diwethaf
#XFLD
@lblNotExecuted=Heb ei Redeg
#XFLD
@lblNotDeployed=Heb ei Osod
#XFLD
errorDetailsTxt=Methu cael y statws rhedeg
#XBTN: Schedule dropdown menu
SCHEDULE=Amserlennu
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Golygu Amserlen
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Dileu Amserlen
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Creu Amserlen
#XLNK
viewDetails=Gweld Manylion
#XMSG: error message for reading execution status from backend
backendErrorMsg=Mae'n ymddangos nad yw'r data yn llwytho o'r gweinydd ar hyn o bryd. Ceisiwch gyrchu'r data eto.
#XFLD: Status text for Completed
@statusCompleted=Wedi Cwblhau
#XFLD: Status text for Running
@statusRunning=Yn Rhedeg
#XFLD: Status text for Failed
@statusFailed=Wedi Methu
#XFLD: Status text for Stopped
@statusStopped=Wedi'i Stopio
#XFLD: Status text for Stopping
@statusStopping=Wrthi'n Stopio
#XFLD
@LoaderTitle=Wrthi'n Llwytho
#XFLD
#XFLD: Deploy status, dataflow has  been deployed / deployment date exists
@deployStatusActive=Wedi'i Osod
@deployStatusRevised=Diweddariadau Lleol
@deployStatusFailed=Wedi Methu
@deployStatusPending=Wrthi'n Gosod...
@LoaderText=Wrthi'n cyrchu manylion o'r gweinydd
#XMSG
@msgDetailFetchError=Roedd gwall wrth gyrchu manylion o'r gweinydd
#XFLD
@executeError=Gwall
#XFLD
@executeWarning=Rhybudd
#XMSG
@executeConfirmDialog=Gwybodaeth
#XMSG
@executeunsavederror=Cadwch eich cadwyn dasgau cyn ei rhedeg.
#XMSG
@executemodifiederror=Mae newidiadau heb eu cadw yn y gadwyn dasgau. Cadwch nhw.
#XMSG
@executerunningerror=Mae'r gadwyn dasgau'n rhedeg ar hyn o bryd. Arhoswch nes bod y rhediad wedi gorffen cyn cychwyn ar un newydd.
#XMSG
@btnExecuteAnyway=Rhedeg Beth Bynnag
#XMSG
@msgExecuteWithValidations=Mae gwallau dilysu yn y gadwyn dasgau. Gall rhedeg y gadwyn dasgau arwain at fethiant.
#XMSG
@msgRunDeployedVersion=Mae newidiadau i’w gosod. Bydd y fersiwn diwethaf i gael ei gosod o’r cadwyn dasgau yn cael ei rhedeg. Ydych chi am fwrw ymlaen?
#XMSG
#XMSG
@navToMonitoring=Agor yn y Monitor Cadwyni Tasgau
#XMSG
txtOR=NEU
#XFLD
@preview=Rhagweld
#XMSG
txtand=a
#NOTR
@dateFormat=yyyy-MM-dd
#NOTR
@dateTimeFormatFilter=yyyy-MM-dd HH:mm:ss.
#NOTR
@timeFormat=HH:mm:ss
@lblColum=Colofn
#XFLD
@lblCondition=Amod
#XFLD
@lblValue=Gwerth
#XMSG
@msgJsonInvalid=Doedd dim modd cadw'r gadwyn dasgau gan fod gwallau yn y JSON. Gwiriwch hyn a'i ddatrys.
#XTIT
@msgSaveFailTitle=JSON annilys.
#XMSG
NOT_CHAINABLE=Dim modd ychwanegu tasg ''{0}'' at y gadwyn tasgau.
#XMSG
NOT_CHAINABLE_REMOTETABLE=Gwrthrych ''{0}'': bydd y math dyblygu’n cael ei newid.
#XMSG
searchTaskChain=Chwilio Gwrthrychau

#XFLD
@txtTaskChain=Cadwyn Tasgau
#XFLD
@txtRemoteTable=Tabl Pell
#XFLD
@txtRemoveData=Tynnu Data Dyblyg
#XFLD
@txtRemovePersist=Tynnu Data Parhad
#XFLD
@txtView=Gwedd
#XFLD
@txtDataFlow=Llif Data
#XFLD
@txtIL=Chwilio Clyfar
#XFLD
@txtTransformationFlow=Llif Trawsnewid
#XFLD
@txtReplicationFlow=Llif Dyblygu
#XFLD
@txtDeltaLocalTable=Tabl Lleol
#XFLD
@txtBWProcessChain=Cadwyn Broses BW
#XFLD
@txtSQLScriptProcedure=Gweithdrefn Sgript SQL
#XFLD
@txtAPI=API
#XFLD
@txtMerge=Cyfuno
#XFLD
@txtOptimize=Optimeiddio
#XFLD
@txtVacuum=Dileu Cofnodion

#XFLD
@txtRun=Rhedeg
#XFLD
@txtPersist=Parhau
#XFLD
@txtReplicate=Dyblygu
#XFLD
@txtDelete=Dileu Cofnodion gyda Math o Newid 'Wedi Dileu'
#XFLD
@txtRunTC=Rhedeg Cadwyn Tasgau
#XFLD
@txtRunBW=Rhedeg Cadwyn Broses BW
#XFLD
@txtRunSQLScriptProcedure=Rhedeg Gweithdrefn Sgript SQL

#XFLD
@txtRunDataFlow=Rhedeg Llif Data
#XFLD
@txtPersistView=Parhau Gwedd
#XFLD
@txtReplicateTable=Dyblygu Tabl
#XFLD
@txtRunIL=Rhedeg Chwilio Clyfar
#XFLD
@txtRunTF=Rhedeg Llif Trawsnewid
#XFLD
@txtRunRF=Rhedeg Llif Dyblygu
#XFLD
@txtRemoveReplicatedData=Tynnu Data Dyblyg
#XFLD
@txtRemovePersistedData=Tynnu Data Parhad
#XFLD
@txtMergeData=Cyfuno
#XFLD
@txtOptimizeData=Optimeiddio
#XFLD
@txtVacuumData=Dileu Cofnodion
#XFLD
@txtRunAPI=Rhedeg API

#XFLD storage type text
hdlfStorage=Ffeil

@statusNew=Heb ei Osod
@statusActive=Wedi'i Osod
@statusRevised=Diweddariadau Lleol
@statusPending=Wrthi'n Gosod...
@statusChangesToDeploy=Newidiadau i'w Gosod
@statusDesignTimeError=Gwall Amser Dyluniad
@statusRunTimeError=Gwall Amser Rhedeg

#XTIT
txtNodes=Gwrthrychau yn y Gadwyn Dasgau ({0})
#XBTN
@deleteNodes=Dileu

#XMSG
@txtDropDataToDiagram=Llusgo a gollwng gwrthrychau yn y diagram.
#XMSG
@noData=Dim gwrthrychau

#XFLD
@txtTaskPosition=Safle'r Dasg

#input parameters and variables
#XFLD
lblInputParameters=Paramedrau Mewnbwn
#XFLD
ip_name=Enw
#XFLD
ip_value=Gwerth
#XTEXT
@noObjectsFound=Heb Ganfod Unrhyw Wrthrychau

#XMSG
@msgExecuteSuccess=Rhedeg y gadwyn tasgau wedi dechrau.
#XMSG
@msgExecuteFail=Wedi methu rhedeg y gadwyn tasgau.
#XMSG
@msgDeployAndRunSuccess=Mae gosod a rhedeg y gadwyn dasgau wedi dechrau.
#XMSG
@msgDeployAndRunFail=Wedi methu gosod a rhedeg y gadwyn dasgau.
#XMSG
@titleExecuteBusy=Arhoswch.
#XMSG
@msgExecuteBusy=Rydyn ni'n paratoi eich data ar gyfer dechrau rhedeg y gadwyn dasgau.
#XMSG
@msgAPITestRunSuccess=Mae'r prawf API wedi dechrau.
#XMSG
@msgAPIExecuteBusy=Rydyn ni'n paratoi eich data ar gyfer dechrau y rhediad API arbrofol.

#XTOL
txtOpenInEditor=Agor yn y Golygydd
#XTOL
txtPreviewData=Rhagweld Data

#datapreview
#XMSG
@msgDataPreviewNotSupp=Dydy rhagweld data ddim ar gael ar gyfer y gwrthrych hwn.

#XMSG Error: empty model
VAL_MODEL_EMPTY=Mae eich model yn wag. Ychwanegwch wrthrychau.
#XMSG Error: deploy model
@msgDeployBeforeRun=Mae angen i chi osod y gadwyn dasgau cyn ei rhedeg.
#BTN: close dialog
btnClose=Cau

#XMSG Object is not Deployed
VAL_NOT_DEPLOYED=Rhaid gosod gwrthrych ''{0}'' er mwyn parhau â''r gadwyn dasgau.
#XMSG Object is having runtime error
VAL_RUNTIME_ERROR=Mae gwall amser rhedeg yng ngwrthrych ''{0}''’. Gwiriwch y gwrthrych.
#XMSG Object is having design time error
VAL_DESIGNTIME_ERROR=Mae gwall amser dylunio yng ngwrthrych ''{0}''’. Gwiriwch y gwrthrych.
#XMSG Procedure Object has been deleted
VAL_DELETED_PROCEDURE=Mae''r gweithdrefnau canlynol wedi''u dileu: {0}
#XMSG New Parameter added to Procedure object
VAL_NEW_PARAMETER_ADDED=Paramedrau newydd wedi''u hychwanegu at weithdrefn "{1}": "{0}". Ewch ati i adleoli''r gadwyn tasgau.
#XMSG Parameter removed from Procedure object
VAL_PARAMETER_DELETED=Paramedrau wedi''u tynnu oddi ar weithdrefn "{1}": "{0}". Ewch ati i adleoli''r gadwyn tasgau.
#XMSG Parameter data type changed in Procedure object
VAL_PARAMETER_DATATYPE_MODIFIED=Math o ddata "{0}" paramedr wedi newid o "{1}" i "{2}" yn y weithdrefn "{3}".
#XMSG Parameter length changed of specific datatype in Procedure object
VAL_PARAMETER_LENGTH_MODIFIED=Hyd "{0}" paramedr wedi newid o "{1}" i "{2}" yn y weithdrefn "{3}".
#XMSG Parameter precision changed of Decimal datatype in Procedure object
VAL_PARAMETER_PRECISION_MODIFIED=Manylder "{0}" paramedr wedi newid o "{1}" i "{2}" yn y weithdrefn "{3}".
#XMSG Parameter scale changed of Decimal datatype in Procedure object
VAL_PARAMETER_SCALE_MODIFIED=Graddfa "{0}" paramedr wedi newid o "{1}" i "{2}" yn y weithdrefn "{3}".
#XMSG Parameter value not provided in Procedure object
VAL_SQL_SCRIPT_PROCEDURE_PARAMETER_VALUE_MISSING=Nid ydych chi wedi rhoi gwerthoedd ar gyfer paramedrau mewnbwn sydd eu hangen ar gyfer gweithdrefn "{0}": "{1}"
#XMSG Remote Table activity changes to Remove Replicated Data
VAL_REMOVE_REPLICATED_DATA=Bydd rhediad nesaf y gadwyn tasgau yn newid math y mynediad data, ac ni fydd data yn cael ei lwytho i fyny mewn amser real mwyach.
#XMSG Remote Table activity changes
REPLICATION_TYPE_CHANGED=Gwrthrych "{0}": Bydd y math dyblygu’n cael ei newid.

#XFLD
@lblStartNode=Dechrau Cwgn
#XFLD
@lblEndNode=Gorffen Cwgn
#XFLD
@linkTo={0} i {1}
#XTOL
@txtViewDetails=Gweld Manylion

#XTOL
txtOpenImpactLineage=Dadansoddiad Effaith a Llinach
#XFLD
@emailNotifications=Hysbysiadau E-bost
#XFLD
@txtReset=Ailosod
#XFLD
@emailMsgWarning=Bydd e-bost templed diofyn yn cael ei anfon pan fydd y neges e-bost yn wag
#XFLD
@notificationSettings=Gosodiadau Hysbysu
#XFLD
@recipientEmailAddr=Cyfeiriad E-bost y Derbynnydd
#XFLD
@emailSubject=Pwnc yr E-bost
@emailSubjectText=Cadwyn dasgau <TASKCHAIN_NAME> wedi cwblhau gyda statws <STATUS>
#XFLD
@emailMessage=Neges E-bost
@emailMessageText=Annwyl Ddefnyddiwr,\n\n Dyma eich hybsybu bod cadwyn dasgau:<TASKCHAIN_NAME> a gafodd ei redeg am <START_TIME> wedi gorffen gyda statws <STATUS>. Daeth i ben am <END_TIME>.\n\nManylion:\nGofod:<SPACE_NAME>\nGwall:<ERROR_MESSAGE>
@selectEmailRecipientDialogTitle=Dewiswch Gyfeiriad E-bost y Derbynnydd
@tenantMembers=Aelodau tenant
@others=Eraill({0})
@selectedEmailAddress=Derbynwyr Dan Sylw
@add=Ychwanegu
@placeholder=Dalfan
@description=Disgrifiad
@copyText=Copïo testun
@taskchainDetailsPlaceholder=Dalfannau ar gyfer manylion cadwyn tasgau
@placeholderCopied=Dalfan wedi'i chopio
@invalidEmailInfo=Rhowch gyfeiriad e-bost cywir
@maxMembersAlreadyAdded=Rydych wedi wedi ychwanegu uchafswm o 20 aelod yn barod
@enterEmailAddress=Rhowch gyfeiriad e-bost y defnyddiwr
@inCorrectPlaceHolder=Dydy {0} ddim yn ddalfan ddisgwyledig yng nghorff yr e-bost.
@nsOFF=Peidio ag anfon hysbysiadau
@nsFAILED=Anfon hysbysiadau e-bost pan fydd y rhediad wedi gorffen gyda gwall yn unig
@nsCOMPLETED=Anfon hysbysiadau e-bost pan fydd y rhediad wedi gorffen yn llwyddiannus yn unig
@nsANY=Anfon e-bost pan fydd y rhediad wedi gorffen yn unig
@phStatus=Statws y cadwyn tasgau - LLWYDDO|METHU
@phTaskChainTName=Enw technegol y cadwyn tasgau
@phTaskChainBName=Enw busnes y cadwyn tasgau
@phLogId=ID Log y rhediad
@phUser=Defnyddiwr sy'n rhedeg y cadwyn tasgau
@phLogUILink=Dolen i ddangosydd log y cadwyn tasgau
@phStartTime=Amser dechrau'r rhediad
@phEndTime=Amser gorffen y rhediad
@phErrMsg=Y neges wall gyntaf yn y log tasgau. Mae'r log yn wag os bydd yn LLWYDDO
@phSpaceName=Enw technegol y gofod
@emailFormatError=Fformat e-bost annilys
@emailFormatErrorInListText=Fformat e-bost annilys wedi'i roi yn y rhestr aelodau nad ydynt yn denantiaid.
@emailSubjectTemplateText=Hysbysiad ar gyfer Cadwyn Dasgau: $$taskChainName$$ - Gofod: $$spaceId$$ - Statws: $$status$$
@emailMessageTemplateText=Helo,\n\n Mae eich cadwyn dasgau â'r label $$taskChainName$$ wedi gorffen gyda statws $$status$$. \n Dyma rai manylion eraill am y gadwyn dasgau:\n - Enw technegol y gadwyn dasgau: $$taskChainName$$ \n - ID cofnod rhediad y gadwyn dasgau: $$logId$$ \n - Defnyddiwr a redodd y gadwyn dasgau : $$user$$ \n - Dolen i ddangosydd log y gadwyn dasgau: $$uiLink$$ \n - Amser cychwyn rhediad y gadwyn dasgau: $$startTime$$ \n - Amser gorffen rhediad y gadwyn dasgau : $$endTime$$ \n - Enw'r gofod: $$spaceId$$ \n
@deleteEmailRecepient=Dileu Derbynnydd
@emailInputDisabledText=Rhowch y gadwyn dasgau ar waith i ychwanegu derbynwyr e-bost.
@tenantOwnerDomainMatchErrorText=Dydy parth y cyfeiriad e-bost ddim yn cyfateb i barth perchennog y tenant: {0}
@totalEmailIdLimitInfoText=Gallwch ddewis hyd at 20 o dderbynwyr e-bost gan gynnwys defnyddwyr sy'n aelodau o denantiaid a derbynwyr eraill.
@emailDomainInfoText=Dim ond cyfeiriadau e-bost gyda pharth: {0} sy''n cael eu derbyn.
@duplicateEmailErrorText=Mae yna dderbynwyr e-bost dyblyg yn y rhestr.

#XFLD Zorder Title
@txtZorderTitle=Colofnau Apache Spark Z-Order

#XFLD Zorder NoColumn
@txtZorderNoColumn=Heb Ddod o Hyd i Golofnau Z-Order

#XFLD Zorder Column PrimaryKey
@txtZorderColumnPrimaryKey=Prif Allwedd

#XFLD
@lblOperators=Gweithredyddion
addNewSelector=Ychwanegu fel Tasg Newydd
parallelSelector=Ychwanegu fel Tasg Gyfochrog
replaceSelector=Disodli Tasg sy'n Bodoli'n Barod
addparallelbranch=Ychwanegu fel Cangen Gyfochrog
addplaceholder=Ychwanegu Dalfan
addALLOperation=POB Gweithredwr
addOROperation=UNRHYW Weithredwr
addplaceholdertocanvas=Ychwanegu Dalfan i Gynfas
addplaceholderonselected=Ychwanegu Dalfan ar ôl Tasg Dan Sylw
#XFLD: Add Parallel Branch to Selected Task
addparallelbranchonselected=Ychwanegu Cangen Gyfochrog ar ôl Tasg Dan Sylw
addOperator=Ychwanegu Gweithredwr
txtAdd=Ychwanegu
txtPlaceHolderText=Llusgo a Gollwng Tasg yma
@lblLayout=Cynllun

#XMSG
VAL_UNCONNECTED_TASK=Dydy tasg ''{0}'' ddim wedi''i chysylltu â''r gadwyn tasgau.
#XMSG
VAL_TASK_SHOULD_HAVE_ONE_INCOMING_LINK=Dim ond un ddolen i mewn ddylai tasg ''{0}'' ei chael.
#XMSG
VAL_TASK_SHOULD_HAVE_INCOMING_LINK=Dylai tasg ''{0}'' gael un ddolen i mewn.
#XMSG
VAL_UNCONNECTED_OPERATOR=Dydy gweithredwr ''{0}'' ddim wedi''i gysylltu â''r gadwyn tasgau.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ATLEAST_TWO_INCOMING_LINK=Dylai gweithredwr ''{0}'' gael o leiaf ddwy ddolen i mewn.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ONE_OUT_LINK=Dylai gweithredwr ''{0}'' gael o leiaf un ddolen allan.
#XMSG
VAL_CIRCULAR_REFERENCE_EXITS=Mae dolen gylch yn bodoli yn y gadwyn tasgau ''{0}''.
#XMSG
VAL_UNCONNECTED_BRANCH=Dydy gwrthrych/cangen ''{0}'' ddim wedi''i gysylltu â''r gadwyn tasgau.
#XMSG
VAL_TASK_TRIGGERED_PARALLEL_MORE_THAN_ONE_TIME=Mae tasg ''{0}'' wedi''i chysylltu''n gyfochrog fwy nag unwaith. Tynnwch y rhai dyblyg er mwyn bwrw ymlaen.


txtBegin=Cychwyn
txtNodesInLink=Gwrthrychau Dan Sylw
#XTOL Tooltip for a context button on diagram
openInNewTab=Agor mewn Tab Newydd
#XTOL Tooltip for a context button on diagram
txtConnectToTaskOperator=Llusgo i Gysylltu
@emailUpdateError=Gwall wrth ddiweddaru'r rhestr Hysbysiadau E-bost

#XMSG
noTeamPrivilegeTxt=Does gennych chi hawl i weld rhestr o aelodau tenant. Defnyddiwch y tab Eraill i ychwanegu derbynwyr e-bost eich hun.

#XFLD Package
@txtPackage=Pecyn

#XMSG warning for repository package assignment changed. {1} - package name
VAL_PACKAGE_CHANGED=Rydych chi wedi neilltuo’r gwrthrych hwn i’r pecyn ''{1}''. Cliciwch Cadw i gadarnhau a dilysu’r newid hwn. Sylwch nad oes modd dadwneud neilltuo i becyn yn y golygydd ar ôl i chi gadw.
#XMSG warning for repository package dependency issue.
MISSING_DEPENDENCY=Does dim modd datrys dibyniaethau gwrthrych ''{0}'' yng nghyd-destun pecyn ''{1}''.

#Warning messages for the folders in the "Others" tab of task chain editor
#XMSG generic warning if getting the objects in a particular folder fails with unhandled errors
getNonRepoArtefactTypeDescendantsError=Cafwyd problem wrth nôl y gwrthrychau o'r ffolder roeddech chi wedi'i dewis.
#BW Process Chains folder related warning messages
#XMSG warning if user does not have the necessary permission related to the SAP BW Bridge space
missingPrivilegeForBridgeSpaceError=Does gennych chi ddim yr caniatâd sydd ei angen i weld neu gynnwys cadwyni proses BW mewn cadwyn dasgau.
#XMSG warning if there was a problem retrieving the BW process chains due to bridge space membership, bridge connection issues or other errors
generalBridgeSpaceError=Cafwyd problem wrth nôl y cadwyni proses BW.
#XMSG warning if the BW API for retrieving the BW process chains from the SAP BW Bridge tenant fails
bwApiClientError=Cafwyd problem wrth nôl y cadwyni proses BW o'r tenant Pont SAP BW.
#XMSG warning if BW process chains list is empty
bwApiEmptyListError=Does dim cadwyni proses BW wedi'u canfod yn y tenant Pont SAP BW.
#XMSG: Task log message for invalid OIDC configuration in the SAP BW Bridge tenant
bwBridgeOIDCConfigStatusInvalid=Nid yw awdurdodiad OpenID wedi''i ffurfweddu ar gyfer y tenant hwn. Defnyddiwch y clientID "{0}" a''r tokenURL  "{1}" i ffurfweddu awdurdodiad OpenID yn y tenant SAP BW Bridge fel y disgrifir yn nodyn SAP 3536298.
#XMSG BW Process Chain Object has been deleted
VAL_DELETED_BW=Mae''n bosibl fod y cadwyni proses BW canlynol wedi''u dileu o''r tenant Pont SAP: {0}
#BW SQL Script Procedures folder related warning messages
#XFLD waring if the Procedure list is empty
procedureAPIEmptyList=Does dim gweithdrefnau wedi'u creu, neu does dim caniatâd CYFLAWNI wedi'i roi i'r Sgema SQL Agored
#Change digram orientations
changeOrientations=Newid Cyfeiriadau


# placeholder for the API Path
apiPath=Er enghraifft: /job/v1
# placeholder for the status API Path
statusAPIPath=Er enghraifft: /job/v1/{id}/status
# valueStateText for the API Path
apiPathRequired=Mae angen Llwybr API
#placeholder for the CSRF Token URL
csrfTokenURL=Dim ond HTTPS y mae modd delio â nhw
# Response Type 1
statusCode=Cael canlyniad o god statws HTTP
# Response Type 2
locationHeader=Cael canlyniad o god statws HTTP a pennyn lleoliad
# Response Type 3
responseBody=Cael canlyniad o god statws HTTP a chorf ymateb
# placeholder for ID
idPlaceholder=Rhowch Lwybr JSON
# placeholder for indicator value
indicatorValue=Nodwch werth
# Placeholder for key
keyPlaceholder=Rhowch allwedd
# Error message for missing key
KeyRequired=Mae angen allwedd
# Error message for invalid key format
invalidKeyFormat=Ni chaniateir yr allwedd pennyn rydych chi wedi'i rhoi. Mae'r penynnau dilys yn cynnwys:<ul><li>"prefer"</li><li>Pennynau sy'n dechrau gyda "x-", ac eithro "x-forwarded-host"</li><li>Pennynau sy'n cynnwys nodau alffaniwmerig, "-", neu "_"</li></ul>
# Error message for missing value
valueRequired=Mae angen gwerth
# Error message for invalid characters in value
invalidValueCharacters=Mae'r pennyn yn cynnwys nodau annilys. Dyma'r nodau arbennig na chaniateir :\t ";", ":", "-", "_", ",", "?", "/", a "*"
# Validation message for invoke api path
apiPathValidation=Rhowch lwybr API dilys, er enghraifft: /job/v1
# Validation message for JSON path
jsonPathValidation=Rhowch lwybr JSON dilys
# Validation message for success/error indicator
indicatorValueValidation=Rhaid i werth y dangosydd ddechrau gyda nod alffaniwmerig a gall gynnwys y nodau arbennig canlynol:\t "-", a "_"
# Error message for JSON path
jsonPathRequired=Mae angen Llwybr JSON
# Error message for invalid API Technical Name
invalidTechnicalName=Mae'r enw technegol yn cynnwys nodau annilys
# Error message for empty Technical Name
emptyTechnicalName=Mae angen Enw Technegol
# Tooltip for codeEditor dialog
codeEditorTooltip=Agor ffenestr Golygu JSON
# Status Api path validation message
validationStatusAPIPath=Rhowch lwybr API dilys, er enghraifft: /job/v1/{id}/status
# CSRF token URL validation message
validationCSRFTokenURL=Rhaid i URL y Tocyn CSRF ddechrau gyda 'https://' a bod yn URL dilys
# Select a connection
selectConnection=Dewiswch gysylltiad
# Validation message for connection item error
connectionNotReplicated=Mae'r cysylltiad yn annilys ar hyn o bryd, felly does dim modd rhedeg tasgau API. Agorwch yr ap "Cysylltiadau" ac ail roi eich manylion adnabod i drwsio'r cysylltiad HTTP
# Validation message for API task properties wrong value or missing
VAL_REST_API_PROPERTIES_MISSING=Mae gan y dasg API "{0}" werthoedd anghywir neu ar goll ar gyfer y priodweddau canlynol: {1}
# Validation message for API connection api is throwing some error or selected connection has some warning
VAL_REST_API_CONNECTION=Mae gan y dasg API "{0}" broblem gyda''r cysylltiad HTTP. Agorwch yr ap "Cysylltiadau" a gwirio''r cysylltiad.
# Validation message for API task properties when delected connection
VAL_REST_API_CONNECTION_DELETED=Mae gan y dasg API "{0}" gysylltiad "{1}" sydd wedi''i ddileu
# Validation message for API task properties when connection cannot be consumed in the API task
VAL_REST_API_CONNECTION_CONSUMPTION_WARNING=Mae gan y dasg API "{0}" gysylltiad "{1}" nad oes modd ei ddefnyddio i gynnal tasgau API. Ewch ati i agor yr ap "Cysylltiadau" ac ailgyflwyno eich manylion i sefydlu cysylltiad ar gyfer tasgau API
# Information message if synchrnous mode is selected in the API Invocation then we don't need status information
modeInfoText=Yn y modd Cysoni, nid yw'r panel statws yn cael ei ddangos ar gyfer galwadau API
# validation dialog button for Run API Test
saveAnyway=Cadw Beth Bynnag
# validation message for technical name
technicalNameValidation=Mae'n rhaid i'r enw technegol fod yn unigryw o fewn y gadwyn dasgau. Dewiswch enw technegol arall
# Connection error message
getHttpConnectionsError=Wedi methu â chael y cysylltiadau HTTP
# Msg for the API test run when the Task chain is not saved
taskChainNotSaved=Rhaid cadw'r gadwyn dasgau cyn dechrau'r rhediad API arbrofol
# Msg failed to run API test run
@failedToRunAPI=Wedi methu cynnal y rhediad API arbrofol
# Msg for the API test run when its already in running state
apiTaskRunning=Mae rhediad API arbrofol eisoes ar y gweill. Ydych chi eisiau dechrau rhediad arbrofol newydd?

topToBtm=Brig-Gwaelod
leftToRight=Chwith-Dde

#XTIT Apache Spark Application Settings
txtHeaderApacheSettings=Gosodiadau Rhaglen Apache Spark
#XFLD Use Default
txtUseSpaceDefault=Defnyddio'r Dewis Diofyn
#XFLD Application
txtApplication=Rhaglen
#XFLD Define new settings for this Task
txtNewSettings=Diffinio gosodiadau newydd ar gyfer y Dasg hon

#XFLD Use Default
txtUseDefault=Defnyddio'r Dewis Diofyn




