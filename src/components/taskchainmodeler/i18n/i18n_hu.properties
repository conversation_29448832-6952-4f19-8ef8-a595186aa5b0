#XTOL Undo
@undo=Visszavonás
#XTOL Redo
@redo=Újra
#XTOL Delete Selected Symbol
@deleteNode=Kijelölt szimbólum törlése
#XTOL Zoom to Fit
@zoomToFit=Nagyítás ablakméretre
#XTOL Auto Layout
@autoLayout=Automatikus elrendezés
#XMSG
@welcomeText=Húzzon objektumokat a bal oldali panelről erre a vászonra.
#XMSG
@txtNoData=Úgy tűnik, még nem adott hozzá objektumokat.
#XMSG
VAL_ENTER_VALID_STRING_GEN=Legfeljebb {0} karakterből álló érvényes karakterláncot adjon meg.
#XMSG
@noParametersMsg=Ennek az eljárásnak nincsenek bemeneti paraméterei.
#XMSG
@ip_enterValueMsg=A(z) {0} (SQL-szkript-elj<PERSON><PERSON><PERSON> futtatása) bemeneti paraméterekkel rendelkezik. Mindegyiknek adhat értéket.
#XTOL
@validateModel=Validálási üzenetek
#XTOL
@hierarchy=Hierarchia
#XTOL
@columnCount=Oszlopok száma
#XFLD
@yes=Igen
#XFLD
@no=Nem
#XTIT Save Dialog param
@modelNameTaskChain=Feladatlánc
#properties panel
@lblPropertyTitle=Tulajdonságok
#XFLD
@lblGeneral=Általános
#XFLD : Setting
@lblSetting=Beállítások
#XTXT: Delta Enabled Local Table Deletion
txtDeltaTableDeletion=Azon Törölve módosítástípusú teljesen feldolgozott rekordok törlése, amelyek régebbiek, mint
#XTXT: Delta Enabled Local Table Deletion Input field description
desDeltaTableDeletion=nap
#XFLD: Data Activation label
@lblDataActivation=Adataktiválás
#XFLD: Latency label
@latency=Késés
#XTEXT: Text for Latency dropdown
txtLatencyDefault=Alapértelmezett
#XTEXT: Text 1 hour
txtOneHour=1 óra
#XTEXT: Text 2 hours
txtTwoHours=2 óra
#XTEXT: Text 3 hours
txtThreeHours=3 óra
#XTEXT: Text 4 hours
txtFourHours=4 óra
#XTEXT: Text 6 hours
txtSixHours=6 óra
#XTEXT: Text 12 hours
txtTwelveHours=12 óra
#XTEXT: Text 1 day
txtOneDay=1 nap
#XFLD: Latency label
@autoRestartHead=Automatikus újraindítás
#XFLD
@lblConnectionName=Kapcsolat
#XFLD
@lblQualifiedName=Minősített név
#XFLD
@lblSpaceName=Tér neve
#XFLD
@lblLocalSchemaName=Helyi séma
#XFLD
@lblType=Objektumtípus
#XFLD
@lblActivity=Tevékenység
#XFLD
@lblTableName=Táblanév
#XFLD
@lblBusinessName=Üzleti név
#XFLD
@lblTechnicalName=Technikai név
#XFLD
@lblSpace=Tér
#XFLD
@lblLabel=Címke
#XFLD
@lblDataType=Adattípus
#XFLD
@lblDescription=Leírás
#XFLD
@lblStorageType=Tároló
#XFLD
@lblHTTPConnection=Általános HTTP-kapcsolat
#XFLD
@lblAPISettings=Általános API-beállítások
#XFLD
@header=Fejlécek
#XFLD
@lblInvoke=API-invokáció
#XFLD
@lblMethod=Metódus
#XFLD
@lblUrl=Alap-URL
#XFLD
@lblAPIPath=API-útvonal
#XFLD
@lblMode=Mód
#XFLD
@lblCSRFToken=CSRF-token kötelezővé tétele
#XFLD
@lblTokenURL=CSRF-token URL-címe
#XFLD
@csrfTokenInfoText=Ha nincs megadva, az alap-URL és az API-útvonal lesz alkalmazva
#XFLD
@lblCSRF=CSRF-token
#XFLD
@lblRequestBody=Kérelemtörzs
#XFLD
@lblFormat=Formátum
#XFLD
@lblResponse=Válasz
#XFLD
@lblId=Azonosító az állapot lehívásához
#XFLD
@Id=Azonosító
#XFLD
@lblSuccessIndicator=Sikerjelző
#XFLD
@lblErrorIndicator=Hibajelző
#XFLD
@lblErrorReason=Hiba oka
#XFLD
@lblStatus=Állapot
#XFLD
@lblApiTestRun=API-tesztfutás
#XFLD
@lblRunStatus=Futás állapota
#XFLD
@lblLastRan=Utolsó futtatás
#XFLD
@lblTestRun=Tesztfutás
#XFLD
@lblDefaultHeader=Alapértelmezett fejlécmezők (kulcs-érték párok)
#XFLD
@lblAdditionalHeader=További fejlécmező
#XFLD
@lblKey=Kulcs
#XFLD
@lblEditJSON=JSON szerkesztése
#XFLD
@lblTasks=Feladatok
#XFLD
@lblRESTfullTask=API
#XFLD: add field button text
btnAddField=Fejlécmező hozzáadása
#XFLD: view Details link
@viewDetails=Részletek megtekintése
#XTOL
tooltipTxt=Több
#XTOL
delete=Törlés
#XBTN: ok button text
btnOk=OK
#XBTN: cancel button text
btnCancel=Mégse
#XBTN: save button text
btnSave=Mentés
#XMSG Error: Object has duplicated technical name.
VAL_OBJECT_DUPLICATE_NAME=Már van {0} nevű objektum a tárházban. Adjon meg más nevet.
#XMSG: loading message while opening task chain
loadTaskChain=A feladatlánc betöltése...
#model properties
#XFLD
@status_panel=Futás állapota
#XFLD
@deploy_status_panel=Üzembehelyezési állapot
#XFLD
@status_lbl=Állapot
#XFLD
@lblLastExecuted=Utolsó futás
#XFLD
@lblNotExecuted=Még nem futott
#XFLD
@lblNotDeployed=Nincs üzembe helyezve
#XFLD
errorDetailsTxt=Nem sikerült lehívni a futás állapotát
#XBTN: Schedule dropdown menu
SCHEDULE=Ütemezés
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Ütemezés szerkesztése
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Ütemezés törlése
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Ütemezés létrehozása
#XLNK
viewDetails=Részletek megtekintése
#XMSG: error message for reading execution status from backend
backendErrorMsg=Úgy tűnik, hogy jelenleg nem töltődnek be az adatok a szerverről. Próbálja újra lehívni az adatokat.
#XFLD: Status text for Completed
@statusCompleted=Befejeződött
#XFLD: Status text for Running
@statusRunning=Fut
#XFLD: Status text for Failed
@statusFailed=Sikertelen
#XFLD: Status text for Stopped
@statusStopped=Leállítva
#XFLD: Status text for Stopping
@statusStopping=Leállítás
#XFLD
@LoaderTitle=Betöltés
#XFLD
#XFLD: Deploy status, dataflow has  been deployed / deployment date exists
@deployStatusActive=Üzembe helyezve
@deployStatusRevised=Helyi frissítések
@deployStatusFailed=Sikertelen
@deployStatusPending=Üzembe helyezés...
@LoaderText=Adatok lehívása a szerverről
#XMSG
@msgDetailFetchError=Hiba az adatok szerverről való lehívásakor
#XFLD
@executeError=Hiba
#XFLD
@executeWarning=Figyelmeztetés
#XMSG
@executeConfirmDialog=Infó
#XMSG
@executeunsavederror=Futtatás előtt mentse a feladatláncot.
#XMSG
@executemodifiederror=Nem mentett módosítások vannak a feladatláncban. Mentse őket.
#XMSG
@executerunningerror=A feladatlánc éppen fut. Új futás indítása előtt várja meg, amíg befejeződik a jelenlegi futás.
#XMSG
@btnExecuteAnyway=Futtatás mindenképp
#XMSG
@msgExecuteWithValidations=A feladatláncban validálási hibák vannak. A feladatlánc futtatása sikertelen lehet.
#XMSG
@msgRunDeployedVersion=Módosítások várnak üzembe helyezésre. A feladatlánc utoljára üzembe helyezett verziója fog futni. Folytatja?
#XMSG
#XMSG
@navToMonitoring=Megnyitás a feladatlánc-figyelőben
#XMSG
txtOR=VAGY
#XFLD
@preview=Előnézet
#XMSG
txtand=és
#NOTR
@dateFormat=yyyy-MM-dd
#NOTR
@dateTimeFormatFilter=yyyy-MM-dd HH:mm:ss.
#NOTR
@timeFormat=HH:mm:ss
@lblColum=Oszlop
#XFLD
@lblCondition=Feltétel
#XFLD
@lblValue=Érték
#XMSG
@msgJsonInvalid=Nem sikerült menteni a feladatláncot, mert hibák vannak a JSON-fájlban. Ellenőrizze és javítsa őket.
#XTIT
@msgSaveFailTitle=Érvénytelen JSON.
#XMSG
NOT_CHAINABLE=A(z) {0} objektum nem adható hozzá a feladatlánchoz.
#XMSG
NOT_CHAINABLE_REMOTETABLE=A(z) {0} objektum replikációtípusa módosulni fog.
#XMSG
searchTaskChain=Objektumok keresése

#XFLD
@txtTaskChain=Feladatlánc
#XFLD
@txtRemoteTable=Távoli tábla
#XFLD
@txtRemoveData=Replikált adatok eltávolítása
#XFLD
@txtRemovePersist=Véglegesített adatok eltávolítása
#XFLD
@txtView=Nézet
#XFLD
@txtDataFlow=Adatáramlás
#XFLD
@txtIL=Intelligens keresés
#XFLD
@txtTransformationFlow=Átalakítási folyamat
#XFLD
@txtReplicationFlow=Replikációs folyamat
#XFLD
@txtDeltaLocalTable=Helyi tábla
#XFLD
@txtBWProcessChain=BW-folyamatlánc
#XFLD
@txtSQLScriptProcedure=SQL-szkript-eljárás
#XFLD
@txtAPI=API
#XFLD
@txtMerge=Egyesítés
#XFLD
@txtOptimize=Optimalizálás
#XFLD
@txtVacuum=Rekordok törlése

#XFLD
@txtRun=Futtatás
#XFLD
@txtPersist=Véglegesítés
#XFLD
@txtReplicate=Replikálás
#XFLD
@txtDelete=A Törölve módosítástípusú rekordok törlése
#XFLD
@txtRunTC=Feladatlánc futtatása
#XFLD
@txtRunBW=BW-folyamatlánc futtatása
#XFLD
@txtRunSQLScriptProcedure=SQL-szkript-eljárás futtatása

#XFLD
@txtRunDataFlow=Adatáramlás futtatása
#XFLD
@txtPersistView=Nézet véglegesítése
#XFLD
@txtReplicateTable=Tábla replikálása
#XFLD
@txtRunIL=Intelligens keresés futtatása
#XFLD
@txtRunTF=Átalakítási folyamat futtatása
#XFLD
@txtRunRF=Replikációs folyamat futtatása
#XFLD
@txtRemoveReplicatedData=Replikált adatok eltávolítása
#XFLD
@txtRemovePersistedData=Véglegesített adatok eltávolítása
#XFLD
@txtMergeData=Egyesítés
#XFLD
@txtOptimizeData=Optimalizálás
#XFLD
@txtVacuumData=Rekordok törlése
#XFLD
@txtRunAPI=API futtatása

#XFLD storage type text
hdlfStorage=Fájl

@statusNew=Nincs üzembe helyezve
@statusActive=Üzembe helyezve
@statusRevised=Helyi frissítések
@statusPending=Üzembe helyezés...
@statusChangesToDeploy=Üzembe helyezendő módosítások
@statusDesignTimeError=Tervezési idejű hiba
@statusRunTimeError=Futásidejű hiba

#XTIT
txtNodes=Objektumok a feladatláncban ({0})
#XBTN
@deleteNodes=Törlés

#XMSG
@txtDropDataToDiagram=Húzzon objektumokat a diagramra.
#XMSG
@noData=Nincsenek objektumok

#XFLD
@txtTaskPosition=Feladat pozíciója

#input parameters and variables
#XFLD
lblInputParameters=Bemeneti paraméterek
#XFLD
ip_name=Név
#XFLD
ip_value=Érték
#XTEXT
@noObjectsFound=Nem található objektum

#XMSG
@msgExecuteSuccess=A feladatlánc futtatása megkezdődött.
#XMSG
@msgExecuteFail=Nem sikerült futtatni a feladatláncot.
#XMSG
@msgDeployAndRunSuccess=A feladatlánc üzembe helyezése és futtatása megkezdődött.
#XMSG
@msgDeployAndRunFail=Nem sikerült üzembe helyezni és futtatni a feladatláncot.
#XMSG
@titleExecuteBusy=Kis türelmet.
#XMSG
@msgExecuteBusy=Előkészítjük az adatokat a feladatlánc futtatására.
#XMSG
@msgAPITestRunSuccess=Az API-tesztfutás elindult.
#XMSG
@msgAPIExecuteBusy=Előkészítjük az adatokat az API-tesztfutásra.

#XTOL
txtOpenInEditor=Megnyitás a szerkesztőben
#XTOL
txtPreviewData=Adatok előnézete

#datapreview
#XMSG
@msgDataPreviewNotSupp=Ennél az objektumnál nem érhető el adatelőnézet.

#XMSG Error: empty model
VAL_MODEL_EMPTY=Úgy tűnik, üres a modell. Adjon hozzá objektumokat.
#XMSG Error: deploy model
@msgDeployBeforeRun=A futtatáshoz előbb üzembe kell helyeznie a feladatláncot.
#BTN: close dialog
btnClose=Bezárás

#XMSG Object is not Deployed
VAL_NOT_DEPLOYED=A feladatlánc folytatásához üzembe kell helyezni a(z) {0} objektumot.
#XMSG Object is having runtime error
VAL_RUNTIME_ERROR=A(z) {0} objektum futásidejű hibát ad eredményül. Ellenőrizze.
#XMSG Object is having design time error
VAL_DESIGNTIME_ERROR=A(z) {0} objektum tervezési idejű hibát ad eredményül. Ellenőrizze.
#XMSG Procedure Object has been deleted
VAL_DELETED_PROCEDURE=A következő eljárások törölve: {0}
#XMSG New Parameter added to Procedure object
VAL_NEW_PARAMETER_ADDED=Új paraméterek lettek hozzáadva a(z) {1}: {0} eljáráshoz. Helyezze újra üzembe a feladatláncot.
#XMSG Parameter removed from Procedure object
VAL_PARAMETER_DELETED=El lettek távolítva paraméterek a(z) {1}: {0} eljárásból. Helyezze újra üzembe a feladatláncot.
#XMSG Parameter data type changed in Procedure object
VAL_PARAMETER_DATATYPE_MODIFIED=A(z) {0} paraméter {1} adattípusúról {2} adattípusúra módosult a következő eljárásban: {3}.
#XMSG Parameter length changed of specific datatype in Procedure object
VAL_PARAMETER_LENGTH_MODIFIED=A(z) {0} paraméter hossza {1} értékről {2} értékre módosult a következő eljárásban: {3}.
#XMSG Parameter precision changed of Decimal datatype in Procedure object
VAL_PARAMETER_PRECISION_MODIFIED=A(z) {0} paraméter pontosság {1} értékről {2} értékre módosult a következő eljárásban: {3}.
#XMSG Parameter scale changed of Decimal datatype in Procedure object
VAL_PARAMETER_SCALE_MODIFIED=A(z) {0} paraméter skálája {1} értékről {2} értékre módosult a következő eljárásban: {3}.
#XMSG Parameter value not provided in Procedure object
VAL_SQL_SCRIPT_PROCEDURE_PARAMETER_VALUE_MISSING=Nem adott meg értékeket a következő eljáráshoz szükséges paraméterek számára: {0}: {1}
#XMSG Remote Table activity changes to Remove Replicated Data
VAL_REMOVE_REPLICATED_DATA=A legközelebbi feladatlánc-futtatás módosítani fogja az adathozzáférés típusát, és az adatok többé nem töltődnek fel valós időben.
#XMSG Remote Table activity changes
REPLICATION_TYPE_CHANGED={0} objektum: a replikáció típusa módosulni fog.

#XFLD
@lblStartNode=Kezdő csomópont
#XFLD
@lblEndNode=Záró csomópont
#XFLD
@linkTo={0} - {1}
#XTOL
@txtViewDetails=Részletek megtekintése

#XTOL
txtOpenImpactLineage=Hatás- és származáselemzés
#XFLD
@emailNotifications=E-mail-értesítések
#XFLD
@txtReset=Visszaállítás
#XFLD
@emailMsgWarning=Ha az e-mail üres, az alapértelmezett e-mail-sablon lesz elküldve
#XFLD
@notificationSettings=Értesítési beállítások
#XFLD
@recipientEmailAddr=Címzett e-mail-címe
#XFLD
@emailSubject=E-mail tárgya
@emailSubjectText=A(z) <TASKCHAIN_NAME> feladatlánc <STATUS> állapottal fejeződött be
#XFLD
@emailMessage=E-mail üzenet
@emailMessageText=Tisztelt felhasználó!\n\n Értesítjük, hogy a(z) <TASKCHAIN_NAME> feladatlánc <START_TIME> időpontban megkezdett futása <STATUS> állapottal fejeződött be. A végrehajtás befejezésének időpontja: <END_TIME>.\n\nRészletek:\nTér:<SPACE_NAME>\nHiba:<ERROR_MESSAGE>
@selectEmailRecipientDialogTitle=Címzett e-mail-címének kiválasztása
@tenantMembers=Bérlő tagfelhasználói
@others=Egyéb ({0})
@selectedEmailAddress=Kiválasztott címzettek
@add=Hozzáadás
@placeholder=Helyőrző
@description=Leírás
@copyText=Szöveg másolása
@taskchainDetailsPlaceholder=Helyőrzők feladatlánc-részletekhez
@placeholderCopied=A helyőrző másolva
@invalidEmailInfo=Érvényes e-mail-címet adjon meg
@maxMembersAlreadyAdded=Elérte a maximálisan hozzáadható 20 tagot
@enterEmailAddress=Adja meg a felhasználó e-mail-címét
@inCorrectPlaceHolder=A(z) {0} nem várt helyőrző az e-mail tartalmában.
@nsOFF=Ne küldjön értesítést
@nsFAILED=Csak akkor küldjön értesítést, ha a futás hibával fejeződött be
@nsCOMPLETED=Csak akkor küldjön értesítést, ha a futás sikeresen befejeződött
@nsANY=Küldjön értesítést a futás befejezésekor
@phStatus=A feladatlánc állapota - SIKERES|SIKERTELEN
@phTaskChainTName=Feladatlánc technikai neve
@phTaskChainBName=Feladatlánc üzleti neve
@phLogId=Futás naplóazonosítója
@phUser=A feladatláncot futtató felhasználó
@phLogUILink=Hivatkozás a feladatlánc naplómegjelenítéséhez
@phStartTime=Futás kezdetének időpontja
@phEndTime=Futás befejezésének időpontja
@phErrMsg=Első hibaüzenet a feladatnaplóban. A napló SIKERES esetén üres.
@phSpaceName=Tér technikai neve
@emailFormatError=Az e-mail-formátum érvénytelen
@emailFormatErrorInListText=Érvénytelen e-mail-formátum van megadva a nem bérlő tagfelhasználóinak listájában.
@emailSubjectTemplateText=Értesítés a következőhöz: feladatlánc: $$taskChainName$$ - tér : $$spaceId$$ - állapot: $$status$$
@emailMessageTemplateText=Üdvözlöm!\n\n A(z) $$taskChainName$$ nevű feladatlánca $$status$$ állapotban fejeződött be. \n További részletek a feladatlánccal kapcsolatban:\n - Feladatlánc technikai neve: $$taskChainName$$ \n - Feladatlánc-futtatás naplóazonosítója: $$logId$$ \n - A feladatláncot futtató felhasználó: $$user$$ \n - Hivatkozás a feladatlánc naplójának megjelenítéséhez: $$uiLink$$ \n - Feladatlánc futásának kezdete: $$startTime$$ \n - Feladatlánc futásának vége: $$endTime$$ \n - Tér neve: $$spaceId$$ \n
@deleteEmailRecepient=Címzett törlése
@emailInputDisabledText=E-mail-címzettek hozzáadásához helyezze üzembe a feladatláncot.
@tenantOwnerDomainMatchErrorText=Az e-mail-cím doménje nem egyezik a bérlőtulajdonos doménjével: {0}
@totalEmailIdLimitInfoText=Legfeljebb 20 e-mail-címzettet választhat ki a bérlő tagfelhasználói és más címzettek közül.
@emailDomainInfoText=Csak {0} doménű e-mail-címek engedélyezettek.
@duplicateEmailErrorText=Ismétlődő címzettek vannak a listában.

#XFLD Zorder Title
@txtZorderTitle=Apache Spark Z-rendezésű oszlopok

#XFLD Zorder NoColumn
@txtZorderNoColumn=Nem találhatók Z-rendezésű oszlopok

#XFLD Zorder Column PrimaryKey
@txtZorderColumnPrimaryKey=Elsődleges kulcs

#XFLD
@lblOperators=Operátorok
addNewSelector=Hozzáadás új feladatként
parallelSelector=Hozzáadás párhuzamos feladatként
replaceSelector=Meglévő feladat cseréje
addparallelbranch=Hozzáadás párhuzamos ágként
addplaceholder=Helyőrző hozzáadása
addALLOperation=ALL operátor
addOROperation=ANY operátor
addplaceholdertocanvas=Helyőrző hozzáadása a vászonhoz
addplaceholderonselected=Helyőrző hozzáadása a kijelölt feladat után
#XFLD: Add Parallel Branch to Selected Task
addparallelbranchonselected=Párhuzamos ág hozzáadása a kijelölt feladat után
addOperator=Operátor hozzáadása
txtAdd=Hozzáadás
txtPlaceHolderText=Húzza ide a feladatot
@lblLayout=Elrendezés

#XMSG
VAL_UNCONNECTED_TASK=A(z) {0} feladat nem kapcsolódik a feladatlánchoz.
#XMSG
VAL_TASK_SHOULD_HAVE_ONE_INCOMING_LINK=A(z) {0} feladatnak csak egy bejövő kapcsolata lehet.
#XMSG
VAL_TASK_SHOULD_HAVE_INCOMING_LINK=A(z) {0} feladatnak egy bejövő kapcsolata lehet.
#XMSG
VAL_UNCONNECTED_OPERATOR=A(z) {0} operátor nem kapcsolódik a feladatlánchoz.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ATLEAST_TWO_INCOMING_LINK=A(z) {0} operátornak legalább két bejövő kapcsolatának kell lennie.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ONE_OUT_LINK=A(z) {0} operátornak legalább egy kimenő kapcsolatának kell lennie.
#XMSG
VAL_CIRCULAR_REFERENCE_EXITS=Körkörös hivatkozás van a(z) {0} feladatláncban.
#XMSG
VAL_UNCONNECTED_BRANCH=A(z) {0} objektum/ág nem kapcsolódik a feladatlánchoz.
#XMSG
VAL_TASK_TRIGGERED_PARALLEL_MORE_THAN_ONE_TIME=A(z) {0} feladat egynél többször kapcsolódik párhuzamosan. A folytatáshoz távolítsa el a duplikátumokat.


txtBegin=Kezdés
txtNodesInLink=Érintett objektumok
#XTOL Tooltip for a context button on diagram
openInNewTab=Megnyitás új lapon
#XTOL Tooltip for a context button on diagram
txtConnectToTaskOperator=Összekapcsoláshoz húzza
@emailUpdateError=Hiba az e-mailes értesítési lista frissítésekor

#XMSG
noTeamPrivilegeTxt=Nincs jogosultsága a bérlő tagfelhasználó-listájának megtekintéséhez. Adja hozzá manuálisan az e-mail címzettjeit az Egyéb lapon.

#XFLD Package
@txtPackage=Csomag

#XMSG warning for repository package assignment changed. {1} - package name
VAL_PACKAGE_CHANGED=Hozzárendelte ezt az objektumot a(z) {1} csomaghoz. Kattintson a Mentés gombra a módosítás megerősítéséhez és validálásához. Ne feledje, hogy a csomaghoz való hozzárendelés mentés után nem vonható vissza ebben a szerkesztőben.
#XMSG warning for repository package dependency issue.
MISSING_DEPENDENCY=A(z) {0} objektum függőségei nem szüntethetők meg a(z) {1} csomag kontextusában.

#Warning messages for the folders in the "Others" tab of task chain editor
#XMSG generic warning if getting the objects in a particular folder fails with unhandled errors
getNonRepoArtefactTypeDescendantsError=Probléma merült fel a kiválasztott mappában lévő objektumok lehívásakor.
#BW Process Chains folder related warning messages
#XMSG warning if user does not have the necessary permission related to the SAP BW Bridge space
missingPrivilegeForBridgeSpaceError=Nincs jogosultsága BW-folyamatláncok megtekintéséhez vagy feladatláncba való felvételéhez.
#XMSG warning if there was a problem retrieving the BW process chains due to bridge space membership, bridge connection issues or other errors
generalBridgeSpaceError=Probléma merült fel a BW-folyamatláncok lehívásakor.
#XMSG warning if the BW API for retrieving the BW process chains from the SAP BW Bridge tenant fails
bwApiClientError=Probléma merült fel a BW-folyamatláncok lehívásakor az SAP BW-híd-bérlőből.
#XMSG warning if BW process chains list is empty
bwApiEmptyListError=Nem találhatók BW-folyamatláncok az SAP BW-híd-bérlőben.
#XMSG: Task log message for invalid OIDC configuration in the SAP BW Bridge tenant
bwBridgeOIDCConfigStatusInvalid=Nincs konfigurálva OpenID-hitelesítés ennél a bérlőnél. Használja a(z) {0} clientID- és a(z) {1} tokenURL-értéket az OpenID-hitelesítés konfigurálásához az SAP BW-híd-bérlőben a 3536298-as SAP-jegyzetben foglaltak szerint.
#XMSG BW Process Chain Object has been deleted
VAL_DELETED_BW=A következő BW-folyamatláncokat törölhették az SAP BW-híd-bérlőből: {0}
#BW SQL Script Procedures folder related warning messages
#XFLD waring if the Procedure list is empty
procedureAPIEmptyList=Nincsenek létrehozva eljárások, vagy az Open SQL-séma nem rendelkezik EXECUTE jogosultsággal.
#Change digram orientations
changeOrientations=Tájolások módosítása


# placeholder for the API Path
apiPath=Például: /job/v1
# placeholder for the status API Path
statusAPIPath=Például: /job/v1/{id}/status
# valueStateText for the API Path
apiPathRequired=Az API-útvonalat meg kell adni
#placeholder for the CSRF Token URL
csrfTokenURL=Csak a HTTPS támogatott
# Response Type 1
statusCode=Eredmény lehívása a HTTP-állapotkódból
# Response Type 2
locationHeader=Eredmény lehívása a HTTP-állapotkódból és a helyfejlécből
# Response Type 3
responseBody=Eredmény lehívása a HTTP-állapotkódból és a választörzsből
# placeholder for ID
idPlaceholder=Adja meg a JSON-útvonalat
# placeholder for indicator value
indicatorValue=Adja meg az értéket
# Placeholder for key
keyPlaceholder=Adja meg a kulcsot
# Error message for missing key
KeyRequired=A kulcsot meg kell adni
# Error message for invalid key format
invalidKeyFormat=A megadott fejléckulcs nem engedélyezett. Érvényes fejlécek:<ul><li>"prefer"</li><li>"x-", except "x-forwarded-host" kezdetű fejlécek</li><li>Alfanumerikus karaktereket, "-" vagy "_" jelet tartalmazó fejlécek</li></ul>
# Error message for missing value
valueRequired=Meg kell adni az értéket
# Error message for invalid characters in value
invalidValueCharacters=A fejléc érvénytelen karaktereket tartalmaz. Engedélyezett speciális karakterek:\t ";", ":", "-", "_", ",", "?", "/" és "*"
# Validation message for invoke api path
apiPathValidation=Érvényes API-útvonalat adjon meg, például: /job/v1
# Validation message for JSON path
jsonPathValidation=Érvényes JSON-útvonalat adjon meg
# Validation message for success/error indicator
indicatorValueValidation=A jelző értékének alfanumerikus karakterrel kell kezdődnie, és a következő speciális karaktereket tartalmazhatja:\t "-" és "_"
# Error message for JSON path
jsonPathRequired=A JSON-útvonalat meg kell adni
# Error message for invalid API Technical Name
invalidTechnicalName=A technikai név érvénytelen karaktereket tartalmaz
# Error message for empty Technical Name
emptyTechnicalName=A technikai nevet meg kell adni
# Tooltip for codeEditor dialog
codeEditorTooltip=JSON-szerkesztő ablakának megnyitása
# Status Api path validation message
validationStatusAPIPath=Érvényes API-útvonalat adjon meg, például:  /job/v1/{id}/status
# CSRF token URL validation message
validationCSRFTokenURL=A CSRF-token URL-címe csak https:// kezdetű lehet, és érvényes URL-címnek kell lennie
# Select a connection
selectConnection=Válasszon kapcsolatot
# Validation message for connection item error
connectionNotReplicated=A kapcsolat jelenleg érvénytelen API-feladatok futtatására. Nyissa meg a Kapcsolatok alkalmazást, és adja meg újra a hitelesítő adatait a HTTP-kapcsolat javításához
# Validation message for API task properties wrong value or missing
VAL_REST_API_PROPERTIES_MISSING=A(z) {0} API-feladatnak helytelen vagy hiányzó értékei vannak a következő tulajdonságoknál: {1}
# Validation message for API connection api is throwing some error or selected connection has some warning
VAL_REST_API_CONNECTION=A(z) {0} API-feladatnál probléma merült fel a HTTP-kapcsolatban. Nyissa meg a Kapcsolatok alkalmazást, és ellenőrizze a kapcsolatot
# Validation message for API task properties when delected connection
VAL_REST_API_CONNECTION_DELETED=A(z) {0} API-feladat törölte a következő kapcsolatot: {1} 
# Validation message for API task properties when connection cannot be consumed in the API task
VAL_REST_API_CONNECTION_CONSUMPTION_WARNING=A(z) {0} API-feladat kapcsolata {1}", amely nem használható API-feladatok futtatására. Nyissa meg a Kapcsolatok alkalmazást, és adja meg újra a hitelesítő adatait, hogy kapcsolatokat létesítsen az API-feladatok számára
# Information message if synchrnous mode is selected in the API Invocation then we don't need status information
modeInfoText=Szinkron módban nem jelenik meg az állapotpanel az API-invokációknál
# validation dialog button for Run API Test
saveAnyway=Mentés mégis
# validation message for technical name
technicalNameValidation=A technikai névnek egyedinek kell lennie a feladatláncon belül. Válasszon más technikai nevet
# Connection error message
getHttpConnectionsError=Nem sikerült lehívni a HTTP-kapcsolatokat
# Msg for the API test run when the Task chain is not saved
taskChainNotSaved=Az API-tesztfutás előtt menteni kell a feladatláncot
# Msg failed to run API test run
@failedToRunAPI=Az API-tesztfutás nem sikerült
# Msg for the API test run when its already in running state
apiTaskRunning=Már folyamatban van egy API-tesztfutás. Szeretne új tesztfutást indítani?

topToBtm=Felülről lefelé
leftToRight=Balról jobbra

#XTIT Apache Spark Application Settings
txtHeaderApacheSettings=Apache Spark alkalmazás beállításai
#XFLD Use Default
txtUseSpaceDefault=Alapértelmezés használata
#XFLD Application
txtApplication=Alkalmazás
#XFLD Define new settings for this Task
txtNewSettings=Új beállítások megadása a feladathoz

#XFLD Use Default
txtUseDefault=Alapértelmezés használata




