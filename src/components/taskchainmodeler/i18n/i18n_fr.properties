#XTOL Undo
@undo=Annuler
#XTOL Redo
@redo=Rétablir
#XTOL Delete Selected Symbol
@deleteNode=Supprimer le symbole sélectionné
#XTOL Zoom to Fit
@zoomToFit=Zoomer pour ajuster
#XTOL Auto Layout
@autoLayout=Mise en forme automatique
#XMSG
@welcomeText=Glissez et déposez les objets à partir du panneau de gauche vers la zone de graphiques.
#XMSG
@txtNoData=Il semblerait que vous n'ayez pas encore ajouté d'objet.
#XMSG
VAL_ENTER_VALID_STRING_GEN=Saisissez une chaîne valide dont la longueur est inférieure ou égale à {0}.
#XMSG
@noParametersMsg=Cette procédure ne comporte aucun paramètre d'entrée.
#XMSG
@ip_enterValueMsg="{0}" (Exécuter la procédure SQLScript) comporte des paramètres d''entrée. Vous pouvez définir une valeur pour chacun d''entre eux.
#XTOL
@validateModel=Messages de validation
#XTOL
@hierarchy=Hiérarchie
#XTOL
@columnCount=Nombre de colonnes
#XFLD
@yes=Oui
#XFLD
@no=Non
#XTIT Save Dialog param
@modelNameTaskChain=Chaîne de tâches
#properties panel
@lblPropertyTitle=Propriétés
#XFLD
@lblGeneral=Généralités
#XFLD : Setting
@lblSetting=Paramètres
#XTXT: Delta Enabled Local Table Deletion
txtDeltaTableDeletion=Supprimer tous les enregistrements totalement traités avec le type de modification 'Supprimé' de plus de
#XTXT: Delta Enabled Local Table Deletion Input field description
desDeltaTableDeletion=Jours
#XFLD: Data Activation label
@lblDataActivation=Activation des données
#XFLD: Latency label
@latency=Latence
#XTEXT: Text for Latency dropdown
txtLatencyDefault=Par défaut
#XTEXT: Text 1 hour
txtOneHour=1 heure
#XTEXT: Text 2 hours
txtTwoHours=2 heures
#XTEXT: Text 3 hours
txtThreeHours=3 heures
#XTEXT: Text 4 hours
txtFourHours=4 heures
#XTEXT: Text 6 hours
txtSixHours=6 heures
#XTEXT: Text 12 hours
txtTwelveHours=12 heures
#XTEXT: Text 1 day
txtOneDay=1 jour
#XFLD: Latency label
@autoRestartHead=Relance automatique
#XFLD
@lblConnectionName=Connexion
#XFLD
@lblQualifiedName=Nom qualifié
#XFLD
@lblSpaceName=Nom de l'espace
#XFLD
@lblLocalSchemaName=Schéma local
#XFLD
@lblType=Type d'objet
#XFLD
@lblActivity=Activité
#XFLD
@lblTableName=Nom de la table
#XFLD
@lblBusinessName=Appellation
#XFLD
@lblTechnicalName=Nom technique
#XFLD
@lblSpace=Espace
#XFLD
@lblLabel=Étiquette
#XFLD
@lblDataType=Type de données
#XFLD
@lblDescription=Description
#XFLD
@lblStorageType=Stockage
#XFLD
@lblHTTPConnection=Connexion HTTP générique
#XFLD
@lblAPISettings=Paramètres API génériques
#XFLD
@header=En-têtes
#XFLD
@lblInvoke=Appel d'API
#XFLD
@lblMethod=Méthode
#XFLD
@lblUrl=URL de base
#XFLD
@lblAPIPath=Chemin d'accès de l'API
#XFLD
@lblMode=Mode
#XFLD
@lblCSRFToken=Demander un jeton CSRF
#XFLD
@lblTokenURL=URL de jeton CSRF
#XFLD
@csrfTokenInfoText=Si non saisie, l'URL de base et le chemin d'accès de l'API seront utilisés.
#XFLD
@lblCSRF=Jeton CSRF
#XFLD
@lblRequestBody=Corps de la demande
#XFLD
@lblFormat=Mettre en forme
#XFLD
@lblResponse=Réponse
#XFLD
@lblId=ID pour récupérer le statut
#XFLD
@Id=ID
#XFLD
@lblSuccessIndicator=Code de réussite
#XFLD
@lblErrorIndicator=Code d'erreur
#XFLD
@lblErrorReason=Motif de l'erreur
#XFLD
@lblStatus=Statut
#XFLD
@lblApiTestRun=Exécution test de l'API
#XFLD
@lblRunStatus=Statut d'exécution
#XFLD
@lblLastRan=Date de dernière exécution
#XFLD
@lblTestRun=Exécution test
#XFLD
@lblDefaultHeader=Zones d'en-tête par défaut (paires clé-valeur)
#XFLD
@lblAdditionalHeader=Zone d'en-tête supplémentaire
#XFLD
@lblKey=Clé
#XFLD
@lblEditJSON=Modifier le fichier JSON
#XFLD
@lblTasks=Tâches
#XFLD
@lblRESTfullTask=API
#XFLD: add field button text
btnAddField=Ajouter une zone d'en-tête
#XFLD: view Details link
@viewDetails=Afficher les détails
#XTOL
tooltipTxt=Plus
#XTOL
delete=Supprimer
#XBTN: ok button text
btnOk=OK
#XBTN: cancel button text
btnCancel=Annuler
#XBTN: save button text
btnSave=Sauvegarder
#XMSG Error: Object has duplicated technical name.
VAL_OBJECT_DUPLICATE_NAME=L''objet ''{0}'' existe déjà dans le référentiel. Veuillez saisir un autre nom.
#XMSG: loading message while opening task chain
loadTaskChain=Chargement de la chaîne de tâches...
#model properties
#XFLD
@status_panel=Statut d'exécution
#XFLD
@deploy_status_panel=Statut de déploiement
#XFLD
@status_lbl=Statut
#XFLD
@lblLastExecuted=Dernière exécution
#XFLD
@lblNotExecuted=Pas encore exécuté
#XFLD
@lblNotDeployed=Non déployé
#XFLD
errorDetailsTxt=Impossible d'obtenir le statut de l'exécution
#XBTN: Schedule dropdown menu
SCHEDULE=Planifier
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Modifier la planification
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Supprimer la planification
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Créer une planification
#XLNK
viewDetails=Afficher les détails
#XMSG: error message for reading execution status from backend
backendErrorMsg=Il semble que le chargement des données à partir du serveur ne fonctionne pas pour l'instant. Essayez à nouveau d'accéder aux données.
#XFLD: Status text for Completed
@statusCompleted=Terminé
#XFLD: Status text for Running
@statusRunning=En cours d'exécution
#XFLD: Status text for Failed
@statusFailed=Échec
#XFLD: Status text for Stopped
@statusStopped=Arrêté
#XFLD: Status text for Stopping
@statusStopping=Arrêt en cours
#XFLD
@LoaderTitle=Chargement en cours
#XFLD
#XFLD: Deploy status, dataflow has  been deployed / deployment date exists
@deployStatusActive=Déployé
@deployStatusRevised=Mises à jour locales
@deployStatusFailed=Échec
@deployStatusPending=Déploiement en cours...
@LoaderText=Récupération des détails à partir du serveur
#XMSG
@msgDetailFetchError=Erreur lors de la récupération des détails à partir du serveur
#XFLD
@executeError=Erreur
#XFLD
@executeWarning=Avertissement
#XMSG
@executeConfirmDialog=Infos
#XMSG
@executeunsavederror=Sauvegardez votre chaîne de tâches avant de l'exécuter.
#XMSG
@executemodifiederror=La chaîne de tâches contient des modifications non sauvegardées. Veuillez la sauvegarder.
#XMSG
@executerunningerror=La chaîne de tâches est en cours d'exécution. Veuillez patienter jusqu'à la fin de l'exécution en cours avant de lancer une autre exécution.
#XMSG
@btnExecuteAnyway=Exécuter tout de même
#XMSG
@msgExecuteWithValidations=La chaîne de tâches comporte des erreurs de validation. Cela peut entraîner l'échec de l'exécution.
#XMSG
@msgRunDeployedVersion=Il existe des modifications à déployer. La dernière version déployée de la chaîne de tâches sera exécutée. Voulez-vous poursuivre ?
#XMSG
#XMSG
@navToMonitoring=Ouvrir dans le moniteur des chaînes de tâches
#XMSG
txtOR=OU
#XFLD
@preview=Aperçu
#XMSG
txtand=ET
#NOTR
@dateFormat=yyyy-MM-dd
#NOTR
@dateTimeFormatFilter=yyyy-MM-dd HH:mm:ss.
#NOTR
@timeFormat=HH:mm:ss
@lblColum=Colonne
#XFLD
@lblCondition=Condition
#XFLD
@lblValue=Valeur
#XMSG
@msgJsonInvalid=La chaîne de tâches n'a pas pu être sauvegardée car le fichier JSON comporte des erreurs. Contrôlez et résolvez les erreurs.
#XTIT
@msgSaveFailTitle=Fichier JSON non valide
#XMSG
NOT_CHAINABLE=Impossible d''ajouter l''objet ''{0}'' à la chaîne de tâches.
#XMSG
NOT_CHAINABLE_REMOTETABLE=Objet ''{0}'' : le type de réplication sera modifié.
#XMSG
searchTaskChain=Rechercher des objets

#XFLD
@txtTaskChain=Chaîne de tâches
#XFLD
@txtRemoteTable=Table distante
#XFLD
@txtRemoveData=Retirer les données répliquées
#XFLD
@txtRemovePersist=Retirer les données rendues persistantes
#XFLD
@txtView=Vue
#XFLD
@txtDataFlow=Flux de données
#XFLD
@txtIL=Recherche intelligente
#XFLD
@txtTransformationFlow=Flux de transformation
#XFLD
@txtReplicationFlow=Flux de réplication
#XFLD
@txtDeltaLocalTable=Table locale
#XFLD
@txtBWProcessChain=Chaîne de processus BW
#XFLD
@txtSQLScriptProcedure=Procédure SQLScript
#XFLD
@txtAPI=API
#XFLD
@txtMerge=Fusionner
#XFLD
@txtOptimize=Optimiser
#XFLD
@txtVacuum=Supprimer les enregistrements

#XFLD
@txtRun=Exécuter
#XFLD
@txtPersist=Rendre persistant
#XFLD
@txtReplicate=Répliquer
#XFLD
@txtDelete=Supprimer les enregistrements avec le type de modification 'Supprimé'
#XFLD
@txtRunTC=Exécuter la chaîne de tâches
#XFLD
@txtRunBW=Exécuter la chaîne de processus BW
#XFLD
@txtRunSQLScriptProcedure=Exécuter la procédure SQLScript

#XFLD
@txtRunDataFlow=Exécuter le flux de données
#XFLD
@txtPersistView=Rendre la vue persistante
#XFLD
@txtReplicateTable=Répliquer la table
#XFLD
@txtRunIL=Exécuter la recherche intelligente
#XFLD
@txtRunTF=Exécuter le flux de transformation
#XFLD
@txtRunRF=Exécuter le flux de réplication
#XFLD
@txtRemoveReplicatedData=Retirer les données répliquées
#XFLD
@txtRemovePersistedData=Retirer les données rendues persistantes
#XFLD
@txtMergeData=Fusionner
#XFLD
@txtOptimizeData=Optimiser
#XFLD
@txtVacuumData=Supprimer les enregistrements
#XFLD
@txtRunAPI=Exécuter l'API

#XFLD storage type text
hdlfStorage=Fichier

@statusNew=Non déployé
@statusActive=Déployé
@statusRevised=Mises à jour locales
@statusPending=Déploiement en cours...
@statusChangesToDeploy=Modifications à déployer
@statusDesignTimeError=Erreur au moment de la conception
@statusRunTimeError=Erreur au moment de l'exécution

#XTIT
txtNodes=Objets dans la chaîne de tâches ({0})
#XBTN
@deleteNodes=Supprimer

#XMSG
@txtDropDataToDiagram=Glissez et déposez les objets vers le diagramme.
#XMSG
@noData=Aucun objet

#XFLD
@txtTaskPosition=Position de la tâche

#input parameters and variables
#XFLD
lblInputParameters=Paramètres d'entrée
#XFLD
ip_name=Nom
#XFLD
ip_value=Valeur
#XTEXT
@noObjectsFound=Aucun objet trouvé

#XMSG
@msgExecuteSuccess=Lancement de l'exécution de la chaîne de tâches
#XMSG
@msgExecuteFail=Échec de l'exécution de la chaîne de tâches
#XMSG
@msgDeployAndRunSuccess=Le déploiement et l'exécution de la chaîne de tâches ont été lancés.
#XMSG
@msgDeployAndRunFail=Le déploiement et l'exécution de la chaîne de tâches ont échoué.
#XMSG
@titleExecuteBusy=Veuillez patienter.
#XMSG
@msgExecuteBusy=Nous préparons vos données pour lancer l'exécution de la chaîne de tâches.
#XMSG
@msgAPITestRunSuccess=L'exécution du test de l'API a commencé.
#XMSG
@msgAPIExecuteBusy=Nous préparons vos données pour lancer l'exécution du test de l'API.

#XTOL
txtOpenInEditor=Ouvrir dans l'éditeur
#XTOL
txtPreviewData=Aperçu des données

#datapreview
#XMSG
@msgDataPreviewNotSupp=Aperçu des données non disponible pour cet objet

#XMSG Error: empty model
VAL_MODEL_EMPTY=Votre modèle semble vide. Ajoutez quelques objets.
#XMSG Error: deploy model
@msgDeployBeforeRun=Vous devez déployer la chaîne de tâches avant de l'exécuter.
#BTN: close dialog
btnClose=Fermer

#XMSG Object is not Deployed
VAL_NOT_DEPLOYED=L''objet ''{0}'' doit être déployé pour poursuivre avec la chaîne de tâches.
#XMSG Object is having runtime error
VAL_RUNTIME_ERROR=L''objet ''{0}'' retourne une erreur d''exécution. Veuillez contrôler l''objet.
#XMSG Object is having design time error
VAL_DESIGNTIME_ERROR=L''objet ''{0}'' retourne une erreur de conception. Veuillez contrôler l''objet.
#XMSG Procedure Object has been deleted
VAL_DELETED_PROCEDURE=Les procédures suivantes ont été supprimées : {0}.
#XMSG New Parameter added to Procedure object
VAL_NEW_PARAMETER_ADDED=Nouveaux paramètres ajoutés à la procédure "{1}" : "{0}". Redéployez la chaîne de tâches.
#XMSG Parameter removed from Procedure object
VAL_PARAMETER_DELETED=Paramètres retirés de la procédure "{1}" : "{0}". Redéployez la chaîne de tâches.
#XMSG Parameter data type changed in Procedure object
VAL_PARAMETER_DATATYPE_MODIFIED=Type de données du paramètre "{0}" passé de "{1}" à "{2}" dans la procédure "{3}"
#XMSG Parameter length changed of specific datatype in Procedure object
VAL_PARAMETER_LENGTH_MODIFIED=Longueur du paramètre "{0}" passée de "{1}" à "{2}" dans la procédure "{3}"
#XMSG Parameter precision changed of Decimal datatype in Procedure object
VAL_PARAMETER_PRECISION_MODIFIED=Précision du paramètre "{0}" passée de "{1}" à "{2}" dans la procédure "{3}"
#XMSG Parameter scale changed of Decimal datatype in Procedure object
VAL_PARAMETER_SCALE_MODIFIED=Échelle du paramètre "{0}" passée de "{1}" à "{2}" dans la procédure "{3}"
#XMSG Parameter value not provided in Procedure object
VAL_SQL_SCRIPT_PROCEDURE_PARAMETER_VALUE_MISSING=Vous n''avez saisi aucune valeur pour les paramètres d''entrée requis pour la procédure "{0}" : "{1}".
#XMSG Remote Table activity changes to Remove Replicated Data
VAL_REMOVE_REPLICATED_DATA=La prochaine exécution d'une chaîne de tâches modifiera le type d'accès aux données et les données ne seront plus chargées en temps réel.
#XMSG Remote Table activity changes
REPLICATION_TYPE_CHANGED=Objet "{0}" : le type de réplication sera modifié.

#XFLD
@lblStartNode=Noeud de départ
#XFLD
@lblEndNode=Noeud final
#XFLD
@linkTo={0} vers {1}
#XTOL
@txtViewDetails=Afficher les détails

#XTOL
txtOpenImpactLineage=Analyse d'impact et de lignage
#XFLD
@emailNotifications=Notifications par e-mail
#XFLD
@txtReset=Réinitialiser
#XFLD
@emailMsgWarning=L'e-mail du modèle par défaut est envoyé lorsque le message de l'e-mail est vide.
#XFLD
@notificationSettings=Paramètres de notification
#XFLD
@recipientEmailAddr=Adresse e-mail du destinataire
#XFLD
@emailSubject=Objet de l'e-mail
@emailSubjectText=Chaîne de tâches <TASKCHAIN_NAME> terminée avec le statut <STATUS>
#XFLD
@emailMessage=Message de l'e-mail
@emailMessageText=Cher utilisateur,\n\n nous vous signalons que la chaîne de tâches <TASKCHAIN_NAME>, exécutée le <START_TIME>, s'est terminée avec le statut <STATUS>. L'exécution s'est terminée le <END_TIME>.\n\nDétails :\nEspace :<SPACE_NAME>\nErreur :<ERROR_MESSAGE>
@selectEmailRecipientDialogTitle=Sélectionner l'adresse e-mail du destinataire
@tenantMembers=Membres du locataire
@others=Autres ({0})
@selectedEmailAddress=Destinataires sélectionnés
@add=Ajouter
@placeholder=Caractère générique
@description=Description
@copyText=Copier le texte
@taskchainDetailsPlaceholder=Caractères génériques pour les détails de chaîne de tâches
@placeholderCopied=Le caractère générique est copié.
@invalidEmailInfo=Saisissez une adresse e-mail correcte.
@maxMembersAlreadyAdded=Vous avez déjà ajouté le nombre maximum de membres : 20.
@enterEmailAddress=Saisissez une adresse e-mail utilisateur.
@inCorrectPlaceHolder={0} n''est pas un caractère générique attendu dans le corps de l''e-mail.
@nsOFF=N'envoyer aucune notification
@nsFAILED=Envoyer une notification par e-mail uniquement quand l'exécution s'est terminée avec une erreur
@nsCOMPLETED=Envoyer une notification par e-mail uniquement quand l'exécution s'est correctement terminée
@nsANY=Envoyer une notification par e-mail une fois l'exécution terminée
@phStatus=Statut de la chaîne de tâches - RÉUSSITE|ÉCHEC
@phTaskChainTName=Nom technique de la chaîne de tâches
@phTaskChainBName=Appellation de la chaîne de tâches
@phLogId=ID de journal de l'exécution
@phUser=Utilisateur exécutant la chaîne de tâches
@phLogUILink=Lien vers l'affichage du journal de la chaîne de tâches
@phStartTime=Date/Heure de début de l'exécution
@phEndTime=Date/Heure de fin de l'exécution
@phErrMsg=Premier message d'erreur dans le journal des tâches. Le journal est vide en cas de RÉUSSITE.
@phSpaceName=Nom technique de l'espace
@emailFormatError=Format d'e-mail non valide
@emailFormatErrorInListText=Une adresse e-mail avec un format non valide a été saisie dans la liste des membres non-locataires.
@emailSubjectTemplateText=Notification pour la chaîne de tâches : $$taskChainName$$ - Espace : $$spaceId$$ - Statut : $$status$$
@emailMessageTemplateText=Bonjour,\n\n Votre chaîne de tâches appelée $$taskChainName$$ s'est terminée avec le statut $$status$$. \n Voici d'autres détails au sujet de la chaîne de tâches :\n - Nom technique de la chaîne de tâches : $$taskChainName$$ \n - ID de journal de l'exécution de la chaîne de tâches : $$logId$$ \n - Utilisateur ayant exécuté la chaîne de tâches : $$user$$ \n - Lien vers l'affichage du journal de la chaîne de tâches : $$uiLink$$ \n - Date/Heure de début de l'exécution de la chaîne de tâches : $$startTime$$ \n - Date/Heure de fin de l'exécution de la chaîne de tâches : $$endTime$$ \n - Nom de l'espace : $$spaceId$$ \n
@deleteEmailRecepient=Supprimer le destinataire
@emailInputDisabledText=Déployez la chaîne de tâches pour ajouter des destinataires d'e-mails.
@tenantOwnerDomainMatchErrorText=Le domaine de l''adresse e-mail ne correspond pas au domaine du responsable de locataire : {0}.
@totalEmailIdLimitInfoText=Vous pouvez sélectionner jusqu'à 20 destinataires d'e-mails, y compris des utilisateurs membres du locataire et d'autres destinataires.
@emailDomainInfoText=Seules les adresses e-mail ayant le domaine "{0}" sont admises.
@duplicateEmailErrorText=La liste comporte des destinataires d'e-mails en double.

#XFLD Zorder Title
@txtZorderTitle=Apache Spark - Ordre des colonnes en Z

#XFLD Zorder NoColumn
@txtZorderNoColumn=Aucune colonne avec ordre en Z trouvée

#XFLD Zorder Column PrimaryKey
@txtZorderColumnPrimaryKey=Clé primaire

#XFLD
@lblOperators=Opérateurs
addNewSelector=Ajouter comme nouvelle tâche
parallelSelector=Ajouter comme tâche parallèle
replaceSelector=Remplacer une tâche existante
addparallelbranch=Ajouter comme branche parallèle
addplaceholder=Ajouter un espace réservé
addALLOperation=Opérateur ALL
addOROperation=Opérateur ANY
addplaceholdertocanvas=Ajouter un espace réservé à la zone de graphiques
addplaceholderonselected=Ajouter un espace réservé après la tâche sélectionnée
#XFLD: Add Parallel Branch to Selected Task
addparallelbranchonselected=Ajouter une branche parallèle après la tâche sélectionnée
addOperator=Ajouter un opérateur
txtAdd=Ajouter
txtPlaceHolderText=Glisser et déposer une tâche ici
@lblLayout=Mise en forme

#XMSG
VAL_UNCONNECTED_TASK=La tâche ''{0}'' n''est pas connectée à la chaîne de tâches.
#XMSG
VAL_TASK_SHOULD_HAVE_ONE_INCOMING_LINK=La tâche ''{0}'' ne doit comporter qu''un seul lien entrant.
#XMSG
VAL_TASK_SHOULD_HAVE_INCOMING_LINK=La tâche ''{0}'' doit comporter un lien entrant.
#XMSG
VAL_UNCONNECTED_OPERATOR=L''opérateur ''{0}'' n''est pas connectée à la chaîne de tâches.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ATLEAST_TWO_INCOMING_LINK=L''opérateur ''{0}'' doit comporter au moins deux liens entrants.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ONE_OUT_LINK=L''opérateur ''{0}'' doit comporter au moins un lien sortant.
#XMSG
VAL_CIRCULAR_REFERENCE_EXITS=La chaîne de tâches ''{0}'' contient une boucle circulaire.
#XMSG
VAL_UNCONNECTED_BRANCH=L''objet ou la branche ''{0}'' n''est pas connecté(e) à la chaîne de tâches.
#XMSG
VAL_TASK_TRIGGERED_PARALLEL_MORE_THAN_ONE_TIME=La tâche ''{0}'' est connectée en parallèle à plusieurs reprises. Retirez les doublons pour poursuivre.


txtBegin=Début
txtNodesInLink=Objets impliqués
#XTOL Tooltip for a context button on diagram
openInNewTab=Ouvrir dans un nouvel onglet
#XTOL Tooltip for a context button on diagram
txtConnectToTaskOperator=Glisser pour connecter
@emailUpdateError=Erreur lors de la mise à jour de la liste d'envoi des notifications par e-mail

#XMSG
noTeamPrivilegeTxt=Vous n'avez pas l'autorisation permettant d'afficher la liste des membres du locataire. Utilisez l'onglet Autres pour ajouter manuellement des destinataires d'e-mails.

#XFLD Package
@txtPackage=Package

#XMSG warning for repository package assignment changed. {1} - package name
VAL_PACKAGE_CHANGED=Vous avez affecté cet objet au package ''{1}''. Cliquez sur Sauvegarder pour confirmer et valider cette modification. Notez qu''une affectation à un package est irréversible dans cet éditeur une fois la sauvegarde effectuée.
#XMSG warning for repository package dependency issue.
MISSING_DEPENDENCY=Les dépendances de l''objet ''{0}'' ne peuvent pas être résolues dans le contexte du package ''{1}''.

#Warning messages for the folders in the "Others" tab of task chain editor
#XMSG generic warning if getting the objects in a particular folder fails with unhandled errors
getNonRepoArtefactTypeDescendantsError=Un problème est survenu lors de la récupération des objets dans le dossier que vous avez sélectionné.
#BW Process Chains folder related warning messages
#XMSG warning if user does not have the necessary permission related to the SAP BW Bridge space
missingPrivilegeForBridgeSpaceError=Vous ne possédez pas l'autorisation nécessaire pour afficher ou inclure les chaînes de processus BW dans une chaîne de tâches.
#XMSG warning if there was a problem retrieving the BW process chains due to bridge space membership, bridge connection issues or other errors
generalBridgeSpaceError=Un problème est survenu lors de la récupération des chaînes de processus BW.
#XMSG warning if the BW API for retrieving the BW process chains from the SAP BW Bridge tenant fails
bwApiClientError=Un problème est survenu lors de la récupération des chaînes de processus BW depuis le locataire du pont vers SAP BW.
#XMSG warning if BW process chains list is empty
bwApiEmptyListError=Aucune chaîne de processus BW n'a été trouvée dans le locataire du pont vers SAP BW.
#XMSG: Task log message for invalid OIDC configuration in the SAP BW Bridge tenant
bwBridgeOIDCConfigStatusInvalid=L''authentification OpenID n''est pas configurée pour ce locataire. Utilisez l''ID client "{0}" et l''URL du jeton "{1}" pour configurer l''authentification OpenID dans le locataire du pont vers SAP BW, tel que décrit dans la note SAP 3536298.
#XMSG BW Process Chain Object has been deleted
VAL_DELETED_BW=Les chaînes de processus BW suivantes ont peut-être été supprimées du locataire du pont vers SAP : {0}
#BW SQL Script Procedures folder related warning messages
#XFLD waring if the Procedure list is empty
procedureAPIEmptyList=Aucune procédure n'a été créée ou le droit EXÉCUTER n'a pas été octroyé au schéma Open SQL.
#Change digram orientations
changeOrientations=Modifier les orientations


# placeholder for the API Path
apiPath=Par exemple : /job/v1
# placeholder for the status API Path
statusAPIPath=Par exemple : /job/v1/{id}/status
# valueStateText for the API Path
apiPathRequired=Le chemin d'accès de l'API est requis.
#placeholder for the CSRF Token URL
csrfTokenURL=Seul HTTPS est pris en charge.
# Response Type 1
statusCode=Obtenir un résultat du code de statut HTTP
# Response Type 2
locationHeader=Obtenir un résultat du code de statut HTTP et de l'en-tête de l'emplacement
# Response Type 3
responseBody=Obtenir un résultat du code de statut HTTP et du corps de la réponse
# placeholder for ID
idPlaceholder=Saisir le chemin d'accès JSON
# placeholder for indicator value
indicatorValue=Saisir la valeur
# Placeholder for key
keyPlaceholder=Saisir la clé
# Error message for missing key
KeyRequired=Clé requise
# Error message for invalid key format
invalidKeyFormat=La clé d'en-tête que vous avez saisie n'est pas autorisée. Les en-têtes valides sont :<ul><li>"prefer"</li><li>En-têtes commençant par "x-", à l'exception de "x-forwarded-host"</li><li>En-têtes contenant des caractères alphanumériques, "-" ou "_"</li></ul>
# Error message for missing value
valueRequired=Valeur requise
# Error message for invalid characters in value
invalidValueCharacters=L'en-tête contient des caractères non valides. Caractères spéciaux autorisés :\t ";", ":", "-", "_", ",", "?", "/" et "*"
# Validation message for invoke api path
apiPathValidation=Veuillez saisir un chemin d'accès de l'API valide, par exemple : /job/v1
# Validation message for JSON path
jsonPathValidation=Veuillez saisir un chemin d'accès JSON valide.
# Validation message for success/error indicator
indicatorValueValidation=La valeur du code doit commencer par un caractère alphanumérique et peut comprendre les caractères spéciaux suivants :\t "-" et "_"
# Error message for JSON path
jsonPathRequired=Le chemin d'accès JSON est requis.
# Error message for invalid API Technical Name
invalidTechnicalName=Le nom technique contient des caractères non valides.
# Error message for empty Technical Name
emptyTechnicalName=Le nom technique est requis.
# Tooltip for codeEditor dialog
codeEditorTooltip=Ouvrir la fenêtre Modifier le fichier JSON
# Status Api path validation message
validationStatusAPIPath=Veuillez saisir un chemin d'accès de l'API valide, par exemple : /job/v1/{id}/status
# CSRF token URL validation message
validationCSRFTokenURL=L'URL du jeton CSRF doit commencer par 'https://' et doit être une URL valide.
# Select a connection
selectConnection=Sélectionner une connexion
# Validation message for connection item error
connectionNotReplicated=Actuellement, la connexion n'est pas valide pour exécuter des tâches d'API. Ouvrez l'application "Connexions" et saisissez à nouveau vos identifiants pour corriger la connexion HTTP.
# Validation message for API task properties wrong value or missing
VAL_REST_API_PROPERTIES_MISSING=Des valeurs de la tâche d''API "{0}" sont incorrectes ou manquantes pour les propriétés suivantes : {1}
# Validation message for API connection api is throwing some error or selected connection has some warning
VAL_REST_API_CONNECTION=La tâche d''API "{0}" rencontre un problème avec la connexion HTTP. Ouvrez l''application "Connexions" et vérifiez la connexion.
# Validation message for API task properties when delected connection
VAL_REST_API_CONNECTION_DELETED=La tâche d''API "{0}" a une connexion "{1}" supprimée.
# Validation message for API task properties when connection cannot be consumed in the API task
VAL_REST_API_CONNECTION_CONSUMPTION_WARNING=La tâche d''API "{0}" a une connexion "{1}" qui ne peut pas être utilisée pour exécuter les tâches d''API. Ouvrez l''onglet "Connexions" et saisissez à nouveau vos identifiants pour établir la connexion pour les tâches d''API.
# Information message if synchrnous mode is selected in the API Invocation then we don't need status information
modeInfoText=En mode synchrone, le panneau du statut ne n'affiche pas pour les appels d'API.
# validation dialog button for Run API Test
saveAnyway=Sauvegarder quand même
# validation message for technical name
technicalNameValidation=Le nom technique doit être unique dans la chaîne de tâches. Choisissez un autre nom technique.
# Connection error message
getHttpConnectionsError=Échec d'obtention des connexions HTTP
# Msg for the API test run when the Task chain is not saved
taskChainNotSaved=La chaîne de tâches doit être sauvegardée avant d'exécuter le test de l'API.
# Msg failed to run API test run
@failedToRunAPI=Échec de l'exécution du test de l'API
# Msg for the API test run when its already in running state
apiTaskRunning=Un test d'API est déjà en cours. Voulez-vous lancer un nouveau test ?

topToBtm=De haut en bas
leftToRight=De gauche à droite

#XTIT Apache Spark Application Settings
txtHeaderApacheSettings=Paramètres de l'application Apache Spark
#XFLD Use Default
txtUseSpaceDefault=Utiliser la valeur par défaut
#XFLD Application
txtApplication=Application
#XFLD Define new settings for this Task
txtNewSettings=Définir de nouveaux paramètres pour cette tâche

#XFLD Use Default
txtUseDefault=Utiliser la valeur par défaut




