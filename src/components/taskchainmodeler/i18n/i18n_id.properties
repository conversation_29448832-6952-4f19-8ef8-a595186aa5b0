#XTOL Undo
@undo=Batalkan
#XTOL Redo
@redo=Kembalikan
#XTOL Delete Selected Symbol
@deleteNode=Hapus Permanen Simbol yang Dipilih
#XTOL Zoom to Fit
@zoomToFit=Sesuaikan dengan Ukuran Jendela
#XTOL Auto Layout
@autoLayout=Sesuaikan Tata Letak Otomatis
#XMSG
@welcomeText=Seret dan jatuhkan objek dari panel sebelah kiri ke kanvas ini.
#XMSG
@txtNoData=Tampaknya Anda belum menambahkan objek apa pun.
#XMSG
VAL_ENTER_VALID_STRING_GEN=Silakan masukkan string yang valid dengan panjang kurang dari atau sama dengan {0}.
#XMSG
@noParametersMsg=Prosedur ini tidak memiliki parameter input.
#XMSG
@ip_enterValueMsg="{0}" (Eksekusi Prosedur Skrip SQL) memiliki parameter input. Anda dapat menetapkan nilai dari setiap parameter.
#XTOL
@validateModel=Pesan Validasi
#XTOL
@hierarchy=Hierarki
#XTOL
@columnCount=Jumlah Kolom
#XFLD
@yes=Ya
#XFLD
@no=Tidak
#XTIT Save Dialog param
@modelNameTaskChain=Rantai tugas
#properties panel
@lblPropertyTitle=Properti
#XFLD
@lblGeneral=Umum
#XFLD : Setting
@lblSetting=Pengaturan
#XTXT: Delta Enabled Local Table Deletion
txtDeltaTableDeletion=Hapus permanen semua catatan yang diproses sepenuhnya dengan Tipe Perubahan 'Dihapus Permanen' yang lebih lama dari
#XTXT: Delta Enabled Local Table Deletion Input field description
desDeltaTableDeletion=Hari
#XFLD: Data Activation label
@lblDataActivation=Aktivasi Data
#XFLD: Latency label
@latency=Latensi
#XTEXT: Text for Latency dropdown
txtLatencyDefault=Default
#XTEXT: Text 1 hour
txtOneHour=1 Jam
#XTEXT: Text 2 hours
txtTwoHours=2 Jam
#XTEXT: Text 3 hours
txtThreeHours=3 Jam
#XTEXT: Text 4 hours
txtFourHours=4 Jam
#XTEXT: Text 6 hours
txtSixHours=6 Jam
#XTEXT: Text 12 hours
txtTwelveHours=12 Jam
#XTEXT: Text 1 day
txtOneDay=1 Hari
#XFLD: Latency label
@autoRestartHead=Memulai Ulang Otomatis
#XFLD
@lblConnectionName=Koneksi
#XFLD
@lblQualifiedName=Nama yang Memenuhi Syarat
#XFLD
@lblSpaceName=Nama Ruang
#XFLD
@lblLocalSchemaName=Skema Lokal
#XFLD
@lblType=Tipe Objek
#XFLD
@lblActivity=Aktivitas
#XFLD
@lblTableName=Nama Tabel
#XFLD
@lblBusinessName=Nama Bisnis
#XFLD
@lblTechnicalName=Nama Teknis
#XFLD
@lblSpace=Ruang
#XFLD
@lblLabel=Label
#XFLD
@lblDataType=Tipe Data
#XFLD
@lblDescription=Deskripsi
#XFLD
@lblStorageType=Penyimpanan
#XFLD
@lblHTTPConnection=Koneksi HTTP Generik
#XFLD
@lblAPISettings=Pengaturan API Generik
#XFLD
@header=Header
#XFLD
@lblInvoke=Pemanggilan API
#XFLD
@lblMethod=Metode
#XFLD
@lblUrl=URL Dasar
#XFLD
@lblAPIPath=Jalur API
#XFLD
@lblMode=Mode
#XFLD
@lblCSRFToken=Memerlukan Token CSRF
#XFLD
@lblTokenURL=URL Token CSRF
#XFLD
@csrfTokenInfoText=Jika tidak diisi, URL Dasar dan jalur API akan digunakan
#XFLD
@lblCSRF=Token CSRF
#XFLD
@lblRequestBody=Isi Permintaan
#XFLD
@lblFormat=Format
#XFLD
@lblResponse=Respons
#XFLD
@lblId=ID untuk mengambil status
#XFLD
@Id=ID
#XFLD
@lblSuccessIndicator=Indikator Keberhasilan
#XFLD
@lblErrorIndicator=Indikator Kesalahan
#XFLD
@lblErrorReason=Alasan Kesalahan
#XFLD
@lblStatus=Status
#XFLD
@lblApiTestRun=Eksekusi Pengujian API
#XFLD
@lblRunStatus=Status Eksekusi
#XFLD
@lblLastRan=Terakhir Dieksekusi Pada
#XFLD
@lblTestRun=Eksekusi Pengujian
#XFLD
@lblDefaultHeader=Bidang Header Default (pasangan Kunci-Nilai)
#XFLD
@lblAdditionalHeader=Bidang Header Tambahan
#XFLD
@lblKey=Kunci
#XFLD
@lblEditJSON=Edit JSON
#XFLD
@lblTasks=Tugas
#XFLD
@lblRESTfullTask=API
#XFLD: add field button text
btnAddField=Tambahkan Bidang Header
#XFLD: view Details link
@viewDetails=Lihat Rincian
#XTOL
tooltipTxt=Selengkapnya
#XTOL
delete=Hapus Permanen
#XBTN: ok button text
btnOk=Oke
#XBTN: cancel button text
btnCancel=Batalkan
#XBTN: save button text
btnSave=Simpan
#XMSG Error: Object has duplicated technical name.
VAL_OBJECT_DUPLICATE_NAME=Objek ''{0}'' sudah ada di repositori. Silakan masukkan nama yang lain.
#XMSG: loading message while opening task chain
loadTaskChain=Memuat rantai tugas...
#model properties
#XFLD
@status_panel=Status Eksekusi
#XFLD
@deploy_status_panel=Status Penyebaran
#XFLD
@status_lbl=Status
#XFLD
@lblLastExecuted=Eksekusi Terakhir
#XFLD
@lblNotExecuted=Belum Dieksekusi
#XFLD
@lblNotDeployed=Tidak Disebarkan
#XFLD
errorDetailsTxt=Tidak dapat mengambil status eksekusi
#XBTN: Schedule dropdown menu
SCHEDULE=Jadwalkan
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Edit Jadwal
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Hapus Permanen Jadwal
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Buat Jadwal
#XLNK
viewDetails=Lihat Rincian
#XMSG: error message for reading execution status from backend
backendErrorMsg=Tampaknya data tidak dimuat dari server saat ini. Coba ambil data lagi.
#XFLD: Status text for Completed
@statusCompleted=Selesai
#XFLD: Status text for Running
@statusRunning=Sedang Dieksekusi
#XFLD: Status text for Failed
@statusFailed=Gagal
#XFLD: Status text for Stopped
@statusStopped=Berhenti
#XFLD: Status text for Stopping
@statusStopping=Menghentikan
#XFLD
@LoaderTitle=Memuat
#XFLD
#XFLD: Deploy status, dataflow has  been deployed / deployment date exists
@deployStatusActive=Disebarkan
@deployStatusRevised=Pembaruan Lokal
@deployStatusFailed=Gagal
@deployStatusPending=Menyebarkan...
@LoaderText=Mengambil rincian dari server
#XMSG
@msgDetailFetchError=Kesalahan saat mengambil rincian dari server
#XFLD
@executeError=Kesalahan
#XFLD
@executeWarning=Peringatan
#XMSG
@executeConfirmDialog=Info
#XMSG
@executeunsavederror=Simpan rantai tugas Anda sebelum dieksekusi.
#XMSG
@executemodifiederror=Ada perubahan yang belum disimpan di rantai tugas. Harap simpan.
#XMSG
@executerunningerror=Rantai tugas saat ini sedang dieksekusi. Tunggu hingga eksekusi saat ini selesai sebelum menjalankan rantai tugas yang baru.
#XMSG
@btnExecuteAnyway=Tetap Eksekusi
#XMSG
@msgExecuteWithValidations=Rantai tugas memiliki kesalahan validasi. Mengeksekusi rantai tugas dapat mengakibatkan kegagalan.
#XMSG
@msgRunDeployedVersion=Terdapat perubahan untuk disebarkan. Versi rantai tugas yang disebarkan terakhir akan dieksekusi. Apakah Anda ingin melanjutkan?
#XMSG
#XMSG
@navToMonitoring=Buka di Pemantau Rantai Tugas
#XMSG
txtOR=ATAU
#XFLD
@preview=Pratinjau
#XMSG
txtand=dan
#NOTR
@dateFormat=yyyy-MM-dd
#NOTR
@dateTimeFormatFilter=yyyy-MM-dd HH:mm:ss.
#NOTR
@timeFormat=HH:mm:ss
@lblColum=Kolom
#XFLD
@lblCondition=Kondisi
#XFLD
@lblValue=Nilai
#XMSG
@msgJsonInvalid=Rantai tugas tidak dapat disimpan karena adanya kesalahan di JSON. Silakan periksa dan selesaikan.
#XTIT
@msgSaveFailTitle=JSON tidak valid.
#XMSG
NOT_CHAINABLE=Objek ''{0}'' tidak dapat ditambahkan ke rantai tugas.
#XMSG
NOT_CHAINABLE_REMOTETABLE=Objek "{0}": tipe replikasi akan diubah.
#XMSG
searchTaskChain=Cari Objek

#XFLD
@txtTaskChain=Rantai Tugas
#XFLD
@txtRemoteTable=Tabel Jarak Jauh
#XFLD
@txtRemoveData=Hapus Data yang direplikasi
#XFLD
@txtRemovePersist=Hapus Data Persisten
#XFLD
@txtView=Lihat
#XFLD
@txtDataFlow=Aliran Data
#XFLD
@txtIL=Pencarian Cerdas
#XFLD
@txtTransformationFlow=Aliran Perubahan
#XFLD
@txtReplicationFlow=Aliran Replikasi
#XFLD
@txtDeltaLocalTable=Tabel Lokal
#XFLD
@txtBWProcessChain=Rantai Proses BW
#XFLD
@txtSQLScriptProcedure=Prosedur Skrip SQL
#XFLD
@txtAPI=API
#XFLD
@txtMerge=Gabungkan
#XFLD
@txtOptimize=Optimalkan
#XFLD
@txtVacuum=Hapus Permanen Catatan

#XFLD
@txtRun=Eksekusi
#XFLD
@txtPersist=Pertahankan
#XFLD
@txtReplicate=Replikasikan
#XFLD
@txtDelete=Hapus Permanen Catatan dengan Tipe Perubahan 'Dihapus Permanen'
#XFLD
@txtRunTC=Eksekusi Rantai Tugas
#XFLD
@txtRunBW=Eksekusi Rantai Proses BW
#XFLD
@txtRunSQLScriptProcedure=Eksekusi Prosedur Skrip SQL

#XFLD
@txtRunDataFlow=Eksekusi Aliran Data
#XFLD
@txtPersistView=Pertahankan Tampilan
#XFLD
@txtReplicateTable=Replikasikan Tabel
#XFLD
@txtRunIL=Eksekusi Pencarian Cerdas
#XFLD
@txtRunTF=Eksekusi Aliran Perubahan
#XFLD
@txtRunRF=Eksekusi Aliran Replikasi
#XFLD
@txtRemoveReplicatedData=Hapus Data yang direplikasi
#XFLD
@txtRemovePersistedData=Hapus Data Persisten
#XFLD
@txtMergeData=Gabungkan
#XFLD
@txtOptimizeData=Optimalkan
#XFLD
@txtVacuumData=Hapus Permanen Catatan
#XFLD
@txtRunAPI=Eksekusi API

#XFLD storage type text
hdlfStorage=File

@statusNew=Tidak Disebarkan
@statusActive=Disebarkan
@statusRevised=Pembaruan Lokal
@statusPending=Menyebarkan...
@statusChangesToDeploy=Perubahan untuk Disebarkan
@statusDesignTimeError=Kesalahan Waktu Rancangan
@statusRunTimeError=Kesalahan Waktu Eksekusi

#XTIT
txtNodes=Objek di Rantai Tugas ({0})
#XBTN
@deleteNodes=Hapus Permanen

#XMSG
@txtDropDataToDiagram=Seret dan jatuhkan objek ke diagram.
#XMSG
@noData=Tidak ada objek

#XFLD
@txtTaskPosition=Posisi Tugas

#input parameters and variables
#XFLD
lblInputParameters=Parameter Input
#XFLD
ip_name=Nama
#XFLD
ip_value=Nilai
#XTEXT
@noObjectsFound=Objek Tidak Ditemukan

#XMSG
@msgExecuteSuccess=Eksekusi rantai tugas telah dimulai.
#XMSG
@msgExecuteFail=Gagal mengeksekusi rantai tugas.
#XMSG
@msgDeployAndRunSuccess=Penyebaran dan eksekusi rantai tugas telah dimulai.
#XMSG
@msgDeployAndRunFail=Gagal menyebarkan dan mengeksekusi rantai tugas.
#XMSG
@titleExecuteBusy=Silakan tunggu.
#XMSG
@msgExecuteBusy=Kami sedang mempersiapkan data Anda untuk mulai mengeksekusi rantai tugas.
#XMSG
@msgAPITestRunSuccess=Eksekusi pengujian API telah dimulai.
#XMSG
@msgAPIExecuteBusy=Kami sedang mempersiapkan data Anda untuk memulai eksekusi pengujian API.

#XTOL
txtOpenInEditor=Buka di Editor
#XTOL
txtPreviewData=Pratinjau Data

#datapreview
#XMSG
@msgDataPreviewNotSupp=Pratinjau data tidak tersedia untuk objek ini.

#XMSG Error: empty model
VAL_MODEL_EMPTY=Model Anda sepertinya kosong. Silakan tambahkan beberapa objek.
#XMSG Error: deploy model
@msgDeployBeforeRun=Anda perlu menyebarkan rantai tugas sebelum dieksekusi.
#BTN: close dialog
btnClose=Tutup

#XMSG Object is not Deployed
VAL_NOT_DEPLOYED=Objek ''{0}'' harus disebarkan untuk melanjutkan rantai tugas.
#XMSG Object is having runtime error
VAL_RUNTIME_ERROR=Objek ''{0}''’ mengalami kesalahan waktu eksekusi. Silakan periksa objek.
#XMSG Object is having design time error
VAL_DESIGNTIME_ERROR=Objek ''{0}''’ mengalami kesalahan waktu rancangan. Silakan periksa objek.
#XMSG Procedure Object has been deleted
VAL_DELETED_PROCEDURE=Prosedur berikut ini telah dihapus permanen: {0}
#XMSG New Parameter added to Procedure object
VAL_NEW_PARAMETER_ADDED=Parameter baru ditambahkan ke prosedur "{1}": "{0}". Silakan sebar ulang rantai tugas.
#XMSG Parameter removed from Procedure object
VAL_PARAMETER_DELETED=Parameter dihapus dari prosedur "{1}": "{0}". Silakan sebar ulang rantai tugas.
#XMSG Parameter data type changed in Procedure object
VAL_PARAMETER_DATATYPE_MODIFIED=Tipe data parameter "{0}" berubah dari "{1}" menjadi "{2}" pada prosedur "{3}".
#XMSG Parameter length changed of specific datatype in Procedure object
VAL_PARAMETER_LENGTH_MODIFIED=Panjang parameter "{0}" berubah dari "{1}" menjadi "{2}" pada prosedur "{3}".
#XMSG Parameter precision changed of Decimal datatype in Procedure object
VAL_PARAMETER_PRECISION_MODIFIED=Presisi parameter "{0}" berubah dari "{1}" menjadi "{2}" pada prosedur "{3}".
#XMSG Parameter scale changed of Decimal datatype in Procedure object
VAL_PARAMETER_SCALE_MODIFIED=Skala parameter "{0}" berubah dari "{1}" menjadi "{2}" pada prosedur "{3}".
#XMSG Parameter value not provided in Procedure object
VAL_SQL_SCRIPT_PROCEDURE_PARAMETER_VALUE_MISSING=Anda belum memasukkan nilai untuk parameter input yang diperlukan untuk prosedur "{0}": "{1}"
#XMSG Remote Table activity changes to Remove Replicated Data
VAL_REMOVE_REPLICATED_DATA=Eksekusi rantai tugas berikutnya akan mengubah tipe akses data, dan data tidak akan lagi diunggah dalam waktu nyata.
#XMSG Remote Table activity changes
REPLICATION_TYPE_CHANGED=Objek "{0}": Tipe replikasi akan diubah.

#XFLD
@lblStartNode=Mulai Node
#XFLD
@lblEndNode=Akhiri Node
#XFLD
@linkTo={0} ke {1}
#XTOL
@txtViewDetails=Lihat Rincian

#XTOL
txtOpenImpactLineage=Analisis Dampak dan Silsilah
#XFLD
@emailNotifications=Pemberitahuan Email
#XFLD
@txtReset=Atur Ulang
#XFLD
@emailMsgWarning=Email templat default akan dikirim saat pesan emailnya kosong
#XFLD
@notificationSettings=Pengaturan Pemberitahuan
#XFLD
@recipientEmailAddr=Alamat Email Penerima
#XFLD
@emailSubject=Subjek Email
@emailSubjectText=Rantai tugas <TASKCHAIN_NAME> yang diselesaikan dengan status <STATUS>
#XFLD
@emailMessage=Pesan Email
@emailMessageText=Pengguna yang terhormat,\n\n Email ini dikirim untuk memberitahukan bahwa rantai tugas:<TASKCHAIN_NAME> yang dieksekusi pada <START_TIME> telah selesai dengan status <STATUS>. Pelaksanaan berakhir pada <END_TIME>.\n\nRincian:\nRuang:<SPACE_NAME>\nKesalahan:<ERROR_MESSAGE>
@selectEmailRecipientDialogTitle=Pilih Alamat Email Penerima
@tenantMembers=Anggota penyewa
@others=Lainnya ({0})
@selectedEmailAddress=Penerima Terpilih
@add=Tambahkan
@placeholder=Placeholder
@description=Deskripsi
@copyText=Salin teks
@taskchainDetailsPlaceholder=Placeholder untuk rincian rantai tugas
@placeholderCopied=Placeholder disalin
@invalidEmailInfo=Masukkan alamat email yang benar
@maxMembersAlreadyAdded=Anda telah menambahkan maksimum 20 anggota
@enterEmailAddress=Masukkan alamat email pengguna
@inCorrectPlaceHolder={0} bukan placeholder yang diharapkan di isi email.
@nsOFF=Jangan mengirim pemberitahuan apa pun
@nsFAILED=Hanya kirim pemberitahuan email jika eksekusi telah selesai dengan kesalahan
@nsCOMPLETED=Hanya kirim pemberitahuan email jika eksekusi telah berhasil selesai
@nsANY=Kirim email jika eksekusi telah selesai
@phStatus=Status rantai tugas - BERHASIL|GAGAL
@phTaskChainTName=Nama teknis rantai tugas
@phTaskChainBName=Nama bisnis rantai tugas
@phLogId=ID Log eksekusi
@phUser=Pengguna yang mengeksekusi rantai tugas
@phLogUILink=Tautkan ke tampilan log rantai tugas
@phStartTime=Waktu mulai eksekusi
@phEndTime=Waktu berakhir eksekusi
@phErrMsg=Pesan kesalahan pertama dalam log tugas. Log akan kosong jika BERHASIL
@phSpaceName=Nama teknis ruang
@emailFormatError=Format email tidak valid
@emailFormatErrorInListText=Format email yang tidak valid dimasukkan dalam daftar anggota selain penyewa.
@emailSubjectTemplateText=Pemberitahuan untuk Rantai Tugas: $$taskChainName$$ - Ruang: $$spaceId$$ - Status: $$status$$
@emailMessageTemplateText=Halo,\n\n Rantai tugas Anda yang berlabel $$taskChainName$$ telah selesai dengan status $$status$$. \n Berikut adalah beberapa rincian lain tentang rantai tugas:\n - Nama teknis rantai tugas: $$taskChainName$$ \n - ID Log dari eksekusi rantai tugas: $$logId$$ \n - Pengguna yang mengeksekusi rantai tugas: $$user$$ \n - Tautan ke tampilan log rantai tugas: $$uiLink$$ \n - Waktu mulai eksekusi rantai tugas: $$startTime$$ \n - Waktu berakhir eksekusi rantai tugas: $$endTime$$ \n - Nama ruang: $$spaceId$$ \n
@deleteEmailRecepient=Hapus Permanen Penerima
@emailInputDisabledText=Sebarkan rantai tugas untuk menambahkan penerima email.
@tenantOwnerDomainMatchErrorText=Domain alamat email tidak cocok dengan domain pemilik penyewa: {0}
@totalEmailIdLimitInfoText=Anda dapat memilih hingga 20 penerima email termasuk pengguna anggota penyewa dan penerima lainnya.
@emailDomainInfoText=Hanya alamat email dengan domain: {0} diterima.
@duplicateEmailErrorText=Terdapat penerima email duplikat dalam daftar.

#XFLD Zorder Title
@txtZorderTitle=Kolom Z-Order Apache Spark

#XFLD Zorder NoColumn
@txtZorderNoColumn=Tidak Ada Kolom Z-Order Ditemukan

#XFLD Zorder Column PrimaryKey
@txtZorderColumnPrimaryKey=Kunci Utama

#XFLD
@lblOperators=Operator
addNewSelector=Tambahkan sebagai Tugas Baru
parallelSelector=Tambahkan sebagai Tugas Paralel
replaceSelector=Ganti Tugas yang Ada
addparallelbranch=Tambahkan sebagai Cabang Paralel
addplaceholder=Tambahkan Placeholder
addALLOperation=SEMUA Operator
addOROperation=SETIAP Operator
addplaceholdertocanvas=Tambahkan Placeholder ke Kanvas
addplaceholderonselected=Tambahkan Placeholder setelah Tugas yang Dipilih
#XFLD: Add Parallel Branch to Selected Task
addparallelbranchonselected=Tambahkan Cabang Paralel setelah Tugas yang Dipilih
addOperator=Tambahkan Operator
txtAdd=Tambahkan
txtPlaceHolderText=Seret & Jatuhkan Tugas di sini
@lblLayout=Tata Letak

#XMSG
VAL_UNCONNECTED_TASK=Tugas ''{0}'' tidak terhubung ke rantai tugas.
#XMSG
VAL_TASK_SHOULD_HAVE_ONE_INCOMING_LINK=Tugas ''{0}'' seharusnya hanya memiliki satu tautan masuk.
#XMSG
VAL_TASK_SHOULD_HAVE_INCOMING_LINK=Tugas ''{0}'' seharusnya memiliki satu tautan masuk.
#XMSG
VAL_UNCONNECTED_OPERATOR=Operator ''{0}'' tidak terhubung ke rantai tugas.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ATLEAST_TWO_INCOMING_LINK=Operator ''{0}'' setidaknya harus memiliki dua tautan masuk.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ONE_OUT_LINK=Operator ''{0}'' setidaknya harus memiliki satu tautan keluar.
#XMSG
VAL_CIRCULAR_REFERENCE_EXITS=Perulangan sirkular ada di rantai tugas ''{0}''.
#XMSG
VAL_UNCONNECTED_BRANCH=Objek/cabang ''{0}'' tidak terhubung ke rantai tugas.
#XMSG
VAL_TASK_TRIGGERED_PARALLEL_MORE_THAN_ONE_TIME=Tugas ''{0}'' terhubung secara paralel lebih dari satu kali. Silakan hapus duplikat untuk melanjutkan.


txtBegin=Mulai
txtNodesInLink=Objek Dilibatkan
#XTOL Tooltip for a context button on diagram
openInNewTab=Buka di Tab Baru
#XTOL Tooltip for a context button on diagram
txtConnectToTaskOperator=Seret untuk Menghubungkan
@emailUpdateError=Terjadi kesalahan saat memperbarui daftar Pemberitahuan Email

#XMSG
noTeamPrivilegeTxt=Anda tidak memiliki izin untuk melihat daftar anggota penyewa. Gunakan tab Lainnya untuk menambahkan penerima email secara manual.

#XFLD Package
@txtPackage=Paket

#XMSG warning for repository package assignment changed. {1} - package name
VAL_PACKAGE_CHANGED=Anda telah menetapkan objek ini ke paket ''{1}''. Klik Simpan untuk mengonfirmasi dan memvalidasi perubahan ini. Perhatikan bahwa penetapan ke paket tidak dapat dibatalkan di editor ini setelah Anda simpan.
#XMSG warning for repository package dependency issue.
MISSING_DEPENDENCY=Dependensi objek ''{0}'' tidak dapat diselesaikan dalam konteks paket ''{1}''.

#Warning messages for the folders in the "Others" tab of task chain editor
#XMSG generic warning if getting the objects in a particular folder fails with unhandled errors
getNonRepoArtefactTypeDescendantsError=Terjadi masalah saat mengambil objek di folder yang Anda pilih.
#BW Process Chains folder related warning messages
#XMSG warning if user does not have the necessary permission related to the SAP BW Bridge space
missingPrivilegeForBridgeSpaceError=Anda tidak memiliki izin yang diperlukan untuk melihat atau memasukkan rantai proses BW ke dalam rantai tugas.
#XMSG warning if there was a problem retrieving the BW process chains due to bridge space membership, bridge connection issues or other errors
generalBridgeSpaceError=Terjadi masalah saat mengambil rantai proses BW.
#XMSG warning if the BW API for retrieving the BW process chains from the SAP BW Bridge tenant fails
bwApiClientError=Terjadi masalah saat mengambil rantai proses BW dari penyewa SAP BW Bridge.
#XMSG warning if BW process chains list is empty
bwApiEmptyListError=Rantai proses BW tidak ditemukan di penyewa SAP BW Bridge.
#XMSG: Task log message for invalid OIDC configuration in the SAP BW Bridge tenant
bwBridgeOIDCConfigStatusInvalid=Autentikasi OpenID tidak dikonfigurasi untuk penyewa ini. Silakan gunakan ID klien "{0}" dan URL token "{1}" untuk melakukan konfigurasi autentikasi OpenID di penyewa SAP BW bridge sebagaimana yang dijelaskan dalam catatan SAP 3536298.
#XMSG BW Process Chain Object has been deleted
VAL_DELETED_BW=Rantai proses BW berikut ini mungkin telah dihapus permanen dari penyewa SAP Bridge: {0}
#BW SQL Script Procedures folder related warning messages
#XFLD waring if the Procedure list is empty
procedureAPIEmptyList=Tidak ada prosedur yang dibuat, atau hak istimewa EXECUTE belum diberikan untuk skema Open SQL.
#Change digram orientations
changeOrientations=Ubah Orientasi


# placeholder for the API Path
apiPath=Misalnya: /job/v1
# placeholder for the status API Path
statusAPIPath=Misalnya: /job/v1/{id}/status
# valueStateText for the API Path
apiPathRequired=Jalur API diperlukan
#placeholder for the CSRF Token URL
csrfTokenURL=Hanya HTTPS yang didukung
# Response Type 1
statusCode=Dapatkan hasil dari kode status HTTP
# Response Type 2
locationHeader=Dapatkan hasil dari kode status HTTP dan header lokasi
# Response Type 3
responseBody=Dapatkan hasil dari kode status HTTP dan isi respons
# placeholder for ID
idPlaceholder=Masukkan Jalur JSON
# placeholder for indicator value
indicatorValue=Masukkan nilai
# Placeholder for key
keyPlaceholder=Masukkan kunci
# Error message for missing key
KeyRequired=Kunci harus diisi
# Error message for invalid key format
invalidKeyFormat=Kunci header yang Anda masukkan tidak diizinkan. Header yang valid adalah:<ul><li>"prefer"</li><li>Header yang dimulai dengan "x-", kecuali "x-forwarded-host"</li><li>Header yang memuat karakter alfanumerik, "-", atau "_"</li></ul>
# Error message for missing value
valueRequired=Nilai harus diisi
# Error message for invalid characters in value
invalidValueCharacters=Header memuat karakter yang tidak valid. Karakter khusus yang diizinkan adalah:\t ";", ":", "-", "_", ",", "?", "/", dan "*"
# Validation message for invoke api path
apiPathValidation=Harap masukkan jalur API yang valid, misalnya: /job/v1
# Validation message for JSON path
jsonPathValidation=Harap masukkan jalur JSON yang valid
# Validation message for success/error indicator
indicatorValueValidation=Nilai indikator harus dimulai dengan karakter alfanumerik dan dapat memuat karakter khusus berikut:\t "-" dan "_"
# Error message for JSON path
jsonPathRequired=Jalur JSON harus diisi
# Error message for invalid API Technical Name
invalidTechnicalName=Nama teknis memuat karakter yang tidak valid
# Error message for empty Technical Name
emptyTechnicalName=Nama Teknis harus diisi
# Tooltip for codeEditor dialog
codeEditorTooltip=Buka jendela Pengeditan JSON
# Status Api path validation message
validationStatusAPIPath=Harap masukkan jalur API yang valid, misalnya: /job/v1/{id}/status
# CSRF token URL validation message
validationCSRFTokenURL=URL Token CSRF harus dimulai dengan 'https://' dan berupa URL yang valid
# Select a connection
selectConnection=Pilih koneksi
# Validation message for connection item error
connectionNotReplicated=Koneksi saat ini tidak valid untuk mengeksekusi tugas API. Buka aplikasi "Koneksi" dan masukkan ulang kredensial Anda untuk memperbaiki koneksi HTTP
# Validation message for API task properties wrong value or missing
VAL_REST_API_PROPERTIES_MISSING=Tugas API "{0}" memiliki nilai yang salah atau hilang untuk properti berikut: {1}
# Validation message for API connection api is throwing some error or selected connection has some warning
VAL_REST_API_CONNECTION=Tugas API "{0}" memiliki koneksi HTTP yang bermasalah. Buka aplikasi "Koneksi" dan periksa koneksi
# Validation message for API task properties when delected connection
VAL_REST_API_CONNECTION_DELETED=Tugas API "{0}" memiliki koneksi "{1}" yang sudah dihapus.
# Validation message for API task properties when connection cannot be consumed in the API task
VAL_REST_API_CONNECTION_CONSUMPTION_WARNING=Tugas API "{0}" memiliki koneksi "{1}" yang tidak dapat digunakan untuk mengeksekusi tugas API. Buka aplikasi "Connections" dan masukkan kembali kredensial Anda untuk mengaktifkan koneksi tugas API.
# Information message if synchrnous mode is selected in the API Invocation then we don't need status information
modeInfoText=Pada mode Sinkron, panel status tidak ditampilkan untuk pemanggilan API
# validation dialog button for Run API Test
saveAnyway=Tetap Simpan
# validation message for technical name
technicalNameValidation=Nama teknis harus unik di dalam rantai tugas. Silakan pilih nama teknis yang lain
# Connection error message
getHttpConnectionsError=Gagal mendapatkan koneksi HTTP
# Msg for the API test run when the Task chain is not saved
taskChainNotSaved=Rantai tugas harus disimpan sebelum memulai eksekusi pengujian API
# Msg failed to run API test run
@failedToRunAPI=Gagal melakukan eksekusi pengujian API
# Msg for the API test run when its already in running state
apiTaskRunning=Eksekusi pengujian API sedang berlangsung. Apakah Anda ingin memulai eksekusi pengujian baru?

topToBtm=Atas-Bawah
leftToRight=Kiri-Kanan

#XTIT Apache Spark Application Settings
txtHeaderApacheSettings=Pengaturan Aplikasi Apache Spark
#XFLD Use Default
txtUseSpaceDefault=Gunakan Default
#XFLD Application
txtApplication=Aplikasi
#XFLD Define new settings for this Task
txtNewSettings=Tentukan pengaturan baru untuk Tugas ini

#XFLD Use Default
txtUseDefault=Gunakan Default




