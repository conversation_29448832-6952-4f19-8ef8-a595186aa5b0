#XTOL Undo
@undo=Renunțare
#XTOL Redo
@redo=Restaurare
#XTOL Delete Selected Symbol
@deleteNode=Ștergere simbol selectat
#XTOL Zoom to Fit
@zoomToFit=Mărire pentru potrivire
#XTOL Auto Layout
@autoLayout=Layout automat
#XMSG
@welcomeText=Efectuați drag and drop pentru obiecte de la panoul din stânga până la această zonă grafică.
#XMSG
@txtNoData=Se pare că încă nu ați adăugat obiecte.
#XMSG
VAL_ENTER_VALID_STRING_GEN=Introduceți un șir valabil cu o lungime mai mică sau egală cu {0}.
#XMSG
@noParametersMsg=Această procedură nu are parametri de intrare.
#XMSG
@ip_enterValueMsg="{0}" (executare procedură script SQL) are parametri de intrare. Puteți seta o valoare pentru fiecare dintre aceștia.
#XTOL
@validateModel=Mesaje de validare
#XTOL
@hierarchy=Ierarhie
#XTOL
@columnCount=Număr de coloane
#XFLD
@yes=Da
#XFLD
@no=Nu
#XTIT Save Dialog param
@modelNameTaskChain=Lanț de sarcini
#properties panel
@lblPropertyTitle=Proprietăți
#XFLD
@lblGeneral=Generalități
#XFLD : Setting
@lblSetting=Setări
#XTXT: Delta Enabled Local Table Deletion
txtDeltaTableDeletion=Ștergeți toate înregistrările prelucrate complet cu tipul de modificare 'Șters' care sunt mai vechi decât
#XTXT: Delta Enabled Local Table Deletion Input field description
desDeltaTableDeletion=Zile
#XFLD: Data Activation label
@lblDataActivation=Activare de date
#XFLD: Latency label
@latency=Latență
#XTEXT: Text for Latency dropdown
txtLatencyDefault=Implicit
#XTEXT: Text 1 hour
txtOneHour=1 oră
#XTEXT: Text 2 hours
txtTwoHours=2 ore
#XTEXT: Text 3 hours
txtThreeHours=3 ore
#XTEXT: Text 4 hours
txtFourHours=4 ore
#XTEXT: Text 6 hours
txtSixHours=6 ore
#XTEXT: Text 12 hours
txtTwelveHours=12 ore
#XTEXT: Text 1 day
txtOneDay=1 zi
#XFLD: Latency label
@autoRestartHead=Relansare automată
#XFLD
@lblConnectionName=Conexiune
#XFLD
@lblQualifiedName=Nume calificat
#XFLD
@lblSpaceName=Nume spațiu
#XFLD
@lblLocalSchemaName=Schemă locală
#XFLD
@lblType=Tip obiect
#XFLD
@lblActivity=Activitate
#XFLD
@lblTableName=Nume tabel
#XFLD
@lblBusinessName=Nume comercial
#XFLD
@lblTechnicalName=Nume tehnic
#XFLD
@lblSpace=Spațiu
#XFLD
@lblLabel=Etichetă
#XFLD
@lblDataType=Tip date
#XFLD
@lblDescription=Descriere
#XFLD
@lblStorageType=Spațiu de stocare
#XFLD
@lblHTTPConnection=Conexiune HTTP generică
#XFLD
@lblAPISettings=Setări API generice
#XFLD
@header=Antete
#XFLD
@lblInvoke=Invocare API
#XFLD
@lblMethod=Metodă
#XFLD
@lblUrl=URL de bază
#XFLD
@lblAPIPath=Cale API
#XFLD
@lblMode=Mod
#XFLD
@lblCSRFToken=Solicitare token CSRF
#XFLD
@lblTokenURL=URL token CSRF
#XFLD
@csrfTokenInfoText=Dacă nu este introdus, vor fi utilizate URL-ul de bază și calea API
#XFLD
@lblCSRF=Token CSRF
#XFLD
@lblRequestBody=Corp cerere
#XFLD
@lblFormat=Format
#XFLD
@lblResponse=Răspuns
#XFLD
@lblId=ID pentru regăsire stare
#XFLD
@Id=ID
#XFLD
@lblSuccessIndicator=Indicator de succes
#XFLD
@lblErrorIndicator=Indicator de eroare
#XFLD
@lblErrorReason=Motiv pentru eroare
#XFLD
@lblStatus=Stare
#XFLD
@lblApiTestRun=Execuție test API
#XFLD
@lblRunStatus=Stare execuție
#XFLD
@lblLastRan=Dată ultima execuție
#XFLD
@lblTestRun=Execuție test
#XFLD
@lblDefaultHeader=Câmpuri de antet implicite (perechi cheie-valoare)
#XFLD
@lblAdditionalHeader=Câmp de antet suplimentar
#XFLD
@lblKey=Cheie
#XFLD
@lblEditJSON=Editare JSON
#XFLD
@lblTasks=Sarcini
#XFLD
@lblRESTfullTask=API
#XFLD: add field button text
btnAddField=Adăugare câmp antet 
#XFLD: view Details link
@viewDetails=Vizualizare detalii
#XTOL
tooltipTxt=Mai mult
#XTOL
delete=Ștergere
#XBTN: ok button text
btnOk=OK
#XBTN: cancel button text
btnCancel=Anulare
#XBTN: save button text
btnSave=Salvare
#XMSG Error: Object has duplicated technical name.
VAL_OBJECT_DUPLICATE_NAME=Obiectul ''{0}'' există deja în registru. Introduceți alt nume.
#XMSG: loading message while opening task chain
loadTaskChain=Încărcare lanț de sarcini...
#model properties
#XFLD
@status_panel=Stare execuție
#XFLD
@deploy_status_panel=Stare implementare
#XFLD
@status_lbl=Stare
#XFLD
@lblLastExecuted=Ultima execuție
#XFLD
@lblNotExecuted=Neexecutat încă
#XFLD
@lblNotDeployed=Neimplementat
#XFLD
errorDetailsTxt=Stare execuție nu a putut fi obținută
#XBTN: Schedule dropdown menu
SCHEDULE=Programare
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Editare programare
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Ștergere programare
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Creare programare
#XLNK
viewDetails=Vizualizare detalii
#XMSG: error message for reading execution status from backend
backendErrorMsg=Se pare că datele nu se încarcă de pe server, în prezent. Încercați să obțineți din nou datele.
#XFLD: Status text for Completed
@statusCompleted=Terminat
#XFLD: Status text for Running
@statusRunning=În execuție
#XFLD: Status text for Failed
@statusFailed=Nereușit
#XFLD: Status text for Stopped
@statusStopped=Oprit
#XFLD: Status text for Stopping
@statusStopping=În curs de oprire
#XFLD
@LoaderTitle=Încărcare
#XFLD
#XFLD: Deploy status, dataflow has  been deployed / deployment date exists
@deployStatusActive=Implementat
@deployStatusRevised=Actualizări locale
@deployStatusFailed=Nereușit
@deployStatusPending=Implementare...
@LoaderText=Obținere detalii de la server
#XMSG
@msgDetailFetchError=Eroare la obținere detalii de la server
#XFLD
@executeError=Eroare
#XFLD
@executeWarning=Avertizare
#XMSG
@executeConfirmDialog=Info
#XMSG
@executeunsavederror=Salvați lanțul dvs. de sarcini înainte de a-l executa.
#XMSG
@executemodifiederror=Există modificări nesalvate în lanțul de sarcini. Salvați-le.
#XMSG
@executerunningerror=Lanțul de sarcini este în prezent în execuție. Așteptați până când execuția este terminată înainte de a lansa una nouă.
#XMSG
@btnExecuteAnyway=Executare în orice caz
#XMSG
@msgExecuteWithValidations=Lanțul de sarcini are erori de validare. Executarea lanțului de sarcini poate conduce la erori.
#XMSG
@msgRunDeployedVersion=Există modificări de implementat. Va fi executată ultima versiune implementată a lanțului de sarcini. Doriți să continuați?
#XMSG
#XMSG
@navToMonitoring=Deschidere în monitor de lanț de sarcini
#XMSG
txtOR=SAU
#XFLD
@preview=Previzualizare
#XMSG
txtand=ȘI
#NOTR
@dateFormat=yyyy-MM-dd
#NOTR
@dateTimeFormatFilter=yyyy-MM-dd HH:mm:ss.
#NOTR
@timeFormat=HH:mm:ss
@lblColum=Coloană
#XFLD
@lblCondition=Condiție
#XFLD
@lblValue=Valoare
#XMSG
@msgJsonInvalid=Lanțul de sarcini nu a putut fi salvat deoarece există erori în JSON. Verificați și rezolvați.
#XTIT
@msgSaveFailTitle=JSON nevalabil.
#XMSG
NOT_CHAINABLE=Obiectul "{0}" nu poate fi adăugat la lanțul de sarcini.
#XMSG
NOT_CHAINABLE_REMOTETABLE=Obiectul ''{0}'': tipul de replicare va fi modificat.
#XMSG
searchTaskChain=Căutare obiecte

#XFLD
@txtTaskChain=Lanț de sarcini
#XFLD
@txtRemoteTable=Tabel la distanță
#XFLD
@txtRemoveData=Eliminare date replicate
#XFLD
@txtRemovePersist=Eliminare date salvate persistent
#XFLD
@txtView=Vizualizare
#XFLD
@txtDataFlow=Flux de date
#XFLD
@txtIL=Căutare inteligentă
#XFLD
@txtTransformationFlow=Flux de transformare
#XFLD
@txtReplicationFlow=Flux de replicare
#XFLD
@txtDeltaLocalTable=Tabel local
#XFLD
@txtBWProcessChain=Lanț de procese BW
#XFLD
@txtSQLScriptProcedure=Procedură script SQL
#XFLD
@txtAPI=API
#XFLD
@txtMerge=Concatenare
#XFLD
@txtOptimize=Optimizare
#XFLD
@txtVacuum=Ștergere înregistrări

#XFLD
@txtRun=Executare
#XFLD
@txtPersist=Salvare persistentă
#XFLD
@txtReplicate=Replicare
#XFLD
@txtDelete=Ștergere înregistrări cu tip de modificare 'Șters'
#XFLD
@txtRunTC=Executare lanț de sarcini
#XFLD
@txtRunBW=Executare lanț de procese BW
#XFLD
@txtRunSQLScriptProcedure=Executare procedură script SQL

#XFLD
@txtRunDataFlow=Executare flux de date
#XFLD
@txtPersistView=Salvare persistentă imagine
#XFLD
@txtReplicateTable=Replicare tabel
#XFLD
@txtRunIL=Executare căutare inteligentă
#XFLD
@txtRunTF=Executare flux de transformare
#XFLD
@txtRunRF=Executare flux de replicare
#XFLD
@txtRemoveReplicatedData=Eliminare date replicate
#XFLD
@txtRemovePersistedData=Eliminare date salvate persistent
#XFLD
@txtMergeData=Concatenare
#XFLD
@txtOptimizeData=Optimizare
#XFLD
@txtVacuumData=Ștergere înregistrări
#XFLD
@txtRunAPI=Executare API

#XFLD storage type text
hdlfStorage=Fișier

@statusNew=Neimplementat
@statusActive=Implementat
@statusRevised=Actualizări locale
@statusPending=Implementare...
@statusChangesToDeploy=Modificări de implementat
@statusDesignTimeError=Eroare de timp de design
@statusRunTimeError=Eroare de timp de execuție

#XTIT
txtNodes=Obiecte din lanț de sarcini ({0})
#XBTN
@deleteNodes=Ștergere

#XMSG
@txtDropDataToDiagram=Efectuați drag and drop pentru obiecte în diagramă.
#XMSG
@noData=Niciun obiect

#XFLD
@txtTaskPosition=Poziție sarcină

#input parameters and variables
#XFLD
lblInputParameters=Parametri de intrare
#XFLD
ip_name=Nume
#XFLD
ip_value=Valoare
#XTEXT
@noObjectsFound=Niciun obiect găsit

#XMSG
@msgExecuteSuccess=Execuție lanț de sarcini a fost lansată.
#XMSG
@msgExecuteFail=Eroare la executare lanț de sarcini.
#XMSG
@msgDeployAndRunSuccess=Implementare și executare lanț de sarcini au fost lansate.
#XMSG
@msgDeployAndRunFail=Eroare la implementare și executare lanț de sarcini.
#XMSG
@titleExecuteBusy=Așteptați.
#XMSG
@msgExecuteBusy=Pregătim datele dvs. pentru a lansa executarea lanțului de sarcini.
#XMSG
@msgAPITestRunSuccess=Execuția testului API a fost lansată.
#XMSG
@msgAPIExecuteBusy=Pregătim datele dvs. pentru a lansa executarea testului API.

#XTOL
txtOpenInEditor=Deschidere în editor
#XTOL
txtPreviewData=Previzualizare date

#datapreview
#XMSG
@msgDataPreviewNotSupp=Previzualizare date nu este disponibilă pentru acest obiect.

#XMSG Error: empty model
VAL_MODEL_EMPTY=Modelul dvs. pare să fie necompletat. Adăugați câteva obiecte.
#XMSG Error: deploy model
@msgDeployBeforeRun=Trebuie să executați lanțul de sarcini înainte de a-l executa.
#BTN: close dialog
btnClose=Închidere

#XMSG Object is not Deployed
VAL_NOT_DEPLOYED=Obiectul "{0}" trebuie să fie implementat pentru a continua lanțul de sarcini.
#XMSG Object is having runtime error
VAL_RUNTIME_ERROR=Obiectul ''{0}'' returnează o eroare de depășire de timp. Verificați obiectul.
#XMSG Object is having design time error
VAL_DESIGNTIME_ERROR=Obiectul ''{0}'' returnează o eroare de timp de design. Verificați obiectul.
#XMSG Procedure Object has been deleted
VAL_DELETED_PROCEDURE=Următoarele proceduri au fost șterse: {0}
#XMSG New Parameter added to Procedure object
VAL_NEW_PARAMETER_ADDED=Parametrii noi adăugați la procedura "{1}": "{0}". Reimplementați lanțul de sarcini.
#XMSG Parameter removed from Procedure object
VAL_PARAMETER_DELETED=Parametrii eliminați din procedura "{1}": "{0}". Reimplementați lanțul de sarcini.
#XMSG Parameter data type changed in Procedure object
VAL_PARAMETER_DATATYPE_MODIFIED=Tipul de date al parametrului "{0}" modificat din "{1}" în "{2}" în procedura "{3}".
#XMSG Parameter length changed of specific datatype in Procedure object
VAL_PARAMETER_LENGTH_MODIFIED=Lungimea parametrului "{0}" modificată din "{1}" în "{2}" în procedura "{3}".
#XMSG Parameter precision changed of Decimal datatype in Procedure object
VAL_PARAMETER_PRECISION_MODIFIED=Precizia parametrului "{0}" modificată din "{1}" în "{2}" în procedura "{3}".
#XMSG Parameter scale changed of Decimal datatype in Procedure object
VAL_PARAMETER_SCALE_MODIFIED=Scara parametrului "{0}" modificată din "{1}" în "{2}" în procedura "{3}".
#XMSG Parameter value not provided in Procedure object
VAL_SQL_SCRIPT_PROCEDURE_PARAMETER_VALUE_MISSING=Nu ați introdus valori pentru parametrii de intrare care sunt necesari pentru procedura "{0}": "{1}"
#XMSG Remote Table activity changes to Remove Replicated Data
VAL_REMOVE_REPLICATED_DATA=Următoarea execuție a unui lanț de sarcini va modifica tipul de acces la date și datele nu vor mai fi actualizate în timp real.
#XMSG Remote Table activity changes
REPLICATION_TYPE_CHANGED=Obiectul "{0}": tipul de replicare va fi modificat.

#XFLD
@lblStartNode=Nod inițial
#XFLD
@lblEndNode=Nod final
#XFLD
@linkTo={0} la {1}
#XTOL
@txtViewDetails=Vizualizare detalii

#XTOL
txtOpenImpactLineage=Analiză impact și origine
#XFLD
@emailNotifications=Notificări prin e-mail
#XFLD
@txtReset=Resetare
#XFLD
@emailMsgWarning=E-mailul de șablon implicit va fi expediat când mesajul e-mailului este gol
#XFLD
@notificationSettings=Setări de notificare
#XFLD
@recipientEmailAddr=Adresă de e-mail destinatar
#XFLD
@emailSubject=Subiect e-mail
@emailSubjectText=Lanțul de sarcini <TASKCHAIN_NAME> a fost terminat cu starea <STATUS>
#XFLD
@emailMessage=Mesaj e-mail
@emailMessageText=Stimate utilizator,\n\n Vă notificăm că lanțul de sarcini:<TASKCHAIN_NAME> executat la <START_TIME> a fost finalizat cu starea <STATUS>. Execuția s-a sfârșit la <END_TIME>.\n\nDetalii:\nSpațiu:<SPACE_NAME>\nEroare:<ERROR_MESSAGE>
@selectEmailRecipientDialogTitle=Selectați adresă de e-mail destinatar
@tenantMembers=Membri tenant
@others=Alții ({0})
@selectedEmailAddress=Destinatari selectați
@add=Adăugare
@placeholder=Caracter generic
@description=Descriere
@copyText=Copiere text
@taskchainDetailsPlaceholder=Caractere generice pentru detalii lanț de sarcini
@placeholderCopied=Caracterul generic a fost copiat
@invalidEmailInfo=Introduceți adresă de e-mail corectă
@maxMembersAlreadyAdded=Ați adăugat deja maximum 20 de membri
@enterEmailAddress=Introduceți adresă de e-mail utilizator
@inCorrectPlaceHolder={0} nu este un caracter generic prevăzut în acest corp de e-mail.
@nsOFF=Fără expediere notificări
@nsFAILED=Expediere notificare prin e-mail doar când execuția a fost terminată cu eroare
@nsCOMPLETED=Expediere notificare prin e-mail doar când execuția a fost terminată cu succes
@nsANY=Expediere e-mail când execuția a fost terminată
@phStatus=Stare lanț de sarcini - SUCCES|NEREUȘITĂ
@phTaskChainTName=Nume tehnic lanț de sarcini
@phTaskChainBName=Nume comercial lanț de sarcini
@phLogId=ID jurnal execuție
@phUser=Utilizator care execută lanț de sarcini
@phLogUILink=Legătură la afișaj jurnal lanț de sarcini
@phStartTime=Oră de început execuție
@phEndTime=Oră de sfârșit execuție
@phErrMsg=Primul mesaj de eroare din jurnalul de sarcini. Jurnalul este gol în caz de SUCCES
@phSpaceName=Numele tehnic al spațiului
@emailFormatError=Format de e-mail nevalabil
@emailFormatErrorInListText=Format de e-mail nevalabil introdus în lista de membri non-tenant.
@emailSubjectTemplateText=Notificare pentru lanț de sarcini: $$taskChainName$$ - spațiu: $$spaceId$$ - stare: $$status$$
@emailMessageTemplateText=Bună ziua,\n\n Lanțul dvs. de sarcini etichetat $$taskChainName$$ a fost finalizat cu starea $$status$$. \n Iată alte câteva detalii despre lanțul de sarcini:\n - Nume tehnic lanț de sarcini: $$taskChainName$$ \n - ID jurnal execuție lanț de sarcini: $$logId$$ \n - Utilizatorul care a executat lanțul de sarcini: $$user$$ \n - Legătură la afișaj jurnal lanț de sarcini: $$uiLink$$ \n - Oră de început execuție jurnal de sarcini: $$startTime$$ \n - Oră de sfârșit execuție lanț de sarcini: $$endTime$$ \n - Numele spațiului: $$spaceId$$ \n
@deleteEmailRecepient=Ștergere destinatar
@emailInputDisabledText=Implementați lanțul de sarcini pentru a adăuga destinatari de e-mail.
@tenantOwnerDomainMatchErrorText=Domeniul adresei de e-mail nu corespunde cu domeniul proprietarului tenantului: {0}
@totalEmailIdLimitInfoText=Puteți selecta maximum 20 de destinatari de e-mail, inclusiv utilizatorii membrului tenant și alți destinatari.
@emailDomainInfoText=Doar adresele de e-mail cu domeniul: {0} sunt acceptate.
@duplicateEmailErrorText=Există destinatari de e-mail duplicați în listă.

#XFLD Zorder Title
@txtZorderTitle=Apache Spark - sortare coloane în Z

#XFLD Zorder NoColumn
@txtZorderNoColumn=Nicio coloană sortată în Z găsită

#XFLD Zorder Column PrimaryKey
@txtZorderColumnPrimaryKey=Cheie primară

#XFLD
@lblOperators=Operatori
addNewSelector=Adăugare ca sarcină nouă
parallelSelector=Adăugare ca sarcină paralelă
replaceSelector=Înlocuire sarcină existentă
addparallelbranch=Adăugare ca ramificație paralelă
addplaceholder=Adăugare caracter generic
addALLOperation=Operator ALL
addOROperation=Operator ANY
addplaceholdertocanvas=Adăugare caracter generic la zonă grafică
addplaceholderonselected=Adăugare caracter generic după sarcină selectată
#XFLD: Add Parallel Branch to Selected Task
addparallelbranchonselected=Adăugare ramificație paralelă după sarcină selectată
addOperator=Adăugare operator
txtAdd=Adăugare
txtPlaceHolderText=Efectuați drag and drop pentru o sarcină aici
@lblLayout=Layout

#XMSG
VAL_UNCONNECTED_TASK=Sarcina ''{0}'' nu este conectată la lanțul de sarcini.
#XMSG
VAL_TASK_SHOULD_HAVE_ONE_INCOMING_LINK=Sarcina ''{0}'' trebuie să aibă o singură legătură de intrare.
#XMSG
VAL_TASK_SHOULD_HAVE_INCOMING_LINK=Sarcina ''{0}'' trebuie să aibă o legătură de intrare.
#XMSG
VAL_UNCONNECTED_OPERATOR=Operatorul ''{0}'' nu este conectat la lanțul de sarcini.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ATLEAST_TWO_INCOMING_LINK=Operatorul ''{0}'' trebuie să aibă cel puțin două legături de intrare.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ONE_OUT_LINK=Operatorul ''{0}'' trebuie să aibă cel puțin o legătură de ieșire.
#XMSG
VAL_CIRCULAR_REFERENCE_EXITS=Există ciclu circular în lanțul de sarcini ''{0}''.
#XMSG
VAL_UNCONNECTED_BRANCH=Obiectul/ramificația ''{0}'' nu este conectată la lanțul de sarcini.
#XMSG
VAL_TASK_TRIGGERED_PARALLEL_MORE_THAN_ONE_TIME=Sarcina ''{0}'' este conectată în paralel de mai multe ori. Eliminați duplicatele pentru a continua.


txtBegin=Început
txtNodesInLink=Obiecte implicate
#XTOL Tooltip for a context button on diagram
openInNewTab=Deschidere în tab nou
#XTOL Tooltip for a context button on diagram
txtConnectToTaskOperator=Trageți pentru conectare
@emailUpdateError=Eroare la actualizare listă de notificări prin e-mail

#XMSG
noTeamPrivilegeTxt=Nu aveți permisiuni pentru a vedea o listă a membrilor tenantului. Utilizați tabul Alții pentru a adăuga manual destinatari de e-mail.

#XFLD Package
@txtPackage=Pachet

#XMSG warning for repository package assignment changed. {1} - package name
VAL_PACKAGE_CHANGED=Ați alocat acest obiect la pachetul ''{1}''. Efectuați click pe Salvare pentru a confirma și a valida această modificare. Rețineți faptul că alocarea la un pachet nu poate fi anulată în acest editor după salvare.
#XMSG warning for repository package dependency issue.
MISSING_DEPENDENCY=Dependențele obiectului ''{0}'' nu pot fi rezolvate în contextul pachetului ''{1}''.

#Warning messages for the folders in the "Others" tab of task chain editor
#XMSG generic warning if getting the objects in a particular folder fails with unhandled errors
getNonRepoArtefactTypeDescendantsError=A apărut o eroare la regăsirea obiectelor în folderul pe care l-ați selectat.
#BW Process Chains folder related warning messages
#XMSG warning if user does not have the necessary permission related to the SAP BW Bridge space
missingPrivilegeForBridgeSpaceError=Nu aveți permisiunea necesară pentru a vizualiza sau include lanțuri de procese BW într-un lanț de sarcini.
#XMSG warning if there was a problem retrieving the BW process chains due to bridge space membership, bridge connection issues or other errors
generalBridgeSpaceError=A apărut o eroare la regăsirea lanțurilor de procese BW.
#XMSG warning if the BW API for retrieving the BW process chains from the SAP BW Bridge tenant fails
bwApiClientError=A apărut o eroare la regăsirea lanțurilor de procese BW din tenantul punții SAP BW.
#XMSG warning if BW process chains list is empty
bwApiEmptyListError=Nu a fost găsit niciun lanț de procese BW în tenantul punții SAP BW.
#XMSG: Task log message for invalid OIDC configuration in the SAP BW Bridge tenant
bwBridgeOIDCConfigStatusInvalid=Autentificarea OpenID nu este configurată pentru acest tenant. Utilizați ID-ul de client "{0}" și URL-ul de token "{1}" pentru a configura autentificarea OpenID în tenantul punții SAP BW, conform descrierii din nota SAP 3536298.
#XMSG BW Process Chain Object has been deleted
VAL_DELETED_BW=Este posibil ca următoarele lanțuri de procese BW să fi fost șterse din tenantul punții SAP: {0}
#BW SQL Script Procedures folder related warning messages
#XFLD waring if the Procedure list is empty
procedureAPIEmptyList=Nicio procedură nu a fost creată sau privilegiul EXECUTE nu a fost acordat schemei Open SQL.
#Change digram orientations
changeOrientations=Modificare orientări


# placeholder for the API Path
apiPath=De exemplu: /job/v1
# placeholder for the status API Path
statusAPIPath=De exemplu: /job/v1/{id}/status
# valueStateText for the API Path
apiPathRequired=Calea API este obligatorie
#placeholder for the CSRF Token URL
csrfTokenURL=Doar HTTPS este suportat
# Response Type 1
statusCode=Obținere rezultat de la cod de stare HTTP
# Response Type 2
locationHeader=Obținere rezultat de la cod de stare HTTP și antet locație
# Response Type 3
responseBody=Obținere rezultat de la cod de stare HTTP și corp răspuns
# placeholder for ID
idPlaceholder=Introduceți cale JSON
# placeholder for indicator value
indicatorValue=Introduceți valoare
# Placeholder for key
keyPlaceholder=Introduceți cheie
# Error message for missing key
KeyRequired=Cheia este obligatorie
# Error message for invalid key format
invalidKeyFormat=Cheia de antet pe care ați introdus-o nu este permisă. Antelele valabile sunt: <ul><li>"prefer"</li><li>Antete care încept cu "x-", cu excepția "x-forwarded-host"</li><li>Antete care conțin caractere alfanumerice, "-" sau "_"</li></ul>
# Error message for missing value
valueRequired=Valoarea este obligatorie
# Error message for invalid characters in value
invalidValueCharacters=Antetul conține caractere nevalabile. Caracterele speciale care sunt permise sunt:\t ";", ":", "-", "_", ",", "?", "/" și "*"
# Validation message for invoke api path
apiPathValidation=Introduceți o cale API valabilă, de exemplu: /job/v1
# Validation message for JSON path
jsonPathValidation=Introduceți o cale JSON valabilă
# Validation message for success/error indicator
indicatorValueValidation=Valoarea indicatorului trebuie să înceapă cu un caracter alfanumeric și poate include următoarele caractere speciale:\t "-" și "_"
# Error message for JSON path
jsonPathRequired=Calea JSON este obligatorie
# Error message for invalid API Technical Name
invalidTechnicalName=Numele tehnic conține caractere nevalabile
# Error message for empty Technical Name
emptyTechnicalName=Numele tehnic este obligatoriu
# Tooltip for codeEditor dialog
codeEditorTooltip=Deschidere fereastră de editare JSON
# Status Api path validation message
validationStatusAPIPath=Introduceți o cale API valabilă, de exemplu: /job/v1/{id}/status
# CSRF token URL validation message
validationCSRFTokenURL=URL token CSRF trebuie să înceapă cu 'https://' și să fie un URL valabil
# Select a connection
selectConnection=Selectați o conexiune
# Validation message for connection item error
connectionNotReplicated=Conexiune nevalabilă în prezent pentru executare sarcini API. Deschideți aplicația "Conexiuni" și reintroduceți acreditările dvs. pentru a repara conexiunea HTTP
# Validation message for API task properties wrong value or missing
VAL_REST_API_PROPERTIES_MISSING=Sarcina API "{0}" are valori incorecte sau lipsă pentru următoarele proprietăți: {1}
# Validation message for API connection api is throwing some error or selected connection has some warning
VAL_REST_API_CONNECTION=Sarcina API "{0}" are o problemă cu conexiunea HTTP. Deschideți aplicația "Conexiuni" și verificați conexiunea
# Validation message for API task properties when delected connection
VAL_REST_API_CONNECTION_DELETED=Sarcina API „{0}” are o conexiune „{1}” ștearsă
# Validation message for API task properties when connection cannot be consumed in the API task
VAL_REST_API_CONNECTION_CONSUMPTION_WARNING=Sarcina API „{0}” are o conexiune „{1}” care nu poate fi utilizată pentru a executa sarcini API. Deschideți aplicația „Conexiuni” și reintroduceți acreditările dvs. pentru a stabili conectivitatea pentru sarcinile API
# Information message if synchrnous mode is selected in the API Invocation then we don't need status information
modeInfoText=În modul Sincron, panoul de stare nu este afișat pentru invocări API
# validation dialog button for Run API Test
saveAnyway=Salvare în orice caz
# validation message for technical name
technicalNameValidation=Numele tehnic trebuie să fie univoc în lanțul de sarcini. Alegeți alt nume tehnic
# Connection error message
getHttpConnectionsError=Eroare la obținere conexiuni HTTP
# Msg for the API test run when the Task chain is not saved
taskChainNotSaved=Lanțul de sarcini trebuie salvat înainte de executarea testului API
# Msg failed to run API test run
@failedToRunAPI=Eroare la executarea testului API
# Msg for the API test run when its already in running state
apiTaskRunning=O execuție de test API este deja în desfășurare. Doriți să lansați o execuție de test nouă?

topToBtm=Sus-jos
leftToRight=Stânga-dreapta

#XTIT Apache Spark Application Settings
txtHeaderApacheSettings=Setări de aplicație Apache Spark
#XFLD Use Default
txtUseSpaceDefault=Utilizare implicită
#XFLD Application
txtApplication=Aplicație
#XFLD Define new settings for this Task
txtNewSettings=Definiți setări noi pentru această sarcină

#XFLD Use Default
txtUseDefault=Utilizare implicită




