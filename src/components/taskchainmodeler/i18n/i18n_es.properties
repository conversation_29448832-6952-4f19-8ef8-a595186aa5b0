#XTOL Undo
@undo=Deshacer
#XTOL Redo
@redo=Rehacer
#XTOL Delete Selected Symbol
@deleteNode=Eliminar símbolo seleccionado
#XTOL Zoom to Fit
@zoomToFit=Zoom hasta ajustar
#XTOL Auto Layout
@autoLayout=Disposición automática
#XMSG
@welcomeText=Arrastre y suelte los objetos del panel izquierdo este área de diseño.
#XMSG
@txtNoData=Parece que no ha añadido ningún objeto todavía.
#XMSG
VAL_ENTER_VALID_STRING_GEN=Introduzca una cadena válida de longitud igual o inferior a {0}.
#XMSG
@noParametersMsg=Este procedimiento no tiene parámetros de entrada.
#XMSG
@ip_enterValueMsg="{0}" (Ejecutar procedimiento de script SQL) tiene parámetros de entrada. Puede establecer un valor de cada uno de ellos.
#XTOL
@validateModel=Mensajes de validación
#XTOL
@hierarchy=Jerarquía
#XTOL
@columnCount=Cantidad de columnas
#XFLD
@yes=Sí
#XFLD
@no=No
#XTIT Save Dialog param
@modelNameTaskChain=Cadena de tareas
#properties panel
@lblPropertyTitle=Propiedades
#XFLD
@lblGeneral=General
#XFLD : Setting
@lblSetting=Opciones
#XTXT: Delta Enabled Local Table Deletion
txtDeltaTableDeletion=Elimine todos los registros completamente procesados con el tipo de cambio 'Eliminado' que sean anteriores a
#XTXT: Delta Enabled Local Table Deletion Input field description
desDeltaTableDeletion=días
#XFLD: Data Activation label
@lblDataActivation=Activación de datos
#XFLD: Latency label
@latency=Latencia
#XTEXT: Text for Latency dropdown
txtLatencyDefault=Predeterminada
#XTEXT: Text 1 hour
txtOneHour=1 hora
#XTEXT: Text 2 hours
txtTwoHours=2 horas
#XTEXT: Text 3 hours
txtThreeHours=3 horas
#XTEXT: Text 4 hours
txtFourHours=4 horas
#XTEXT: Text 6 hours
txtSixHours=6 horas
#XTEXT: Text 12 hours
txtTwelveHours=12 horas
#XTEXT: Text 1 day
txtOneDay=1 día
#XFLD: Latency label
@autoRestartHead=Reinicio automático
#XFLD
@lblConnectionName=Conexión
#XFLD
@lblQualifiedName=Nombre cualificado
#XFLD
@lblSpaceName=Nombre de espacio
#XFLD
@lblLocalSchemaName=Esquema local
#XFLD
@lblType=Tipo de objeto
#XFLD
@lblActivity=Actividad
#XFLD
@lblTableName=Nombre de tabla
#XFLD
@lblBusinessName=Nombre empresarial
#XFLD
@lblTechnicalName=Nombre técnico
#XFLD
@lblSpace=Espacio
#XFLD
@lblLabel=Etiqueta
#XFLD
@lblDataType=Tipo de datos
#XFLD
@lblDescription=Descripción
#XFLD
@lblStorageType=Almacenamiento
#XFLD
@lblHTTPConnection=Conexión HTTP genérica
#XFLD
@lblAPISettings=Opciones genéricas de API
#XFLD
@header=Cabeceras
#XFLD
@lblInvoke=Invocación de API
#XFLD
@lblMethod=Método
#XFLD
@lblUrl=URL base
#XFLD
@lblAPIPath=Ruta de API
#XFLD
@lblMode=Modo
#XFLD
@lblCSRFToken=Requerir token CSRF
#XFLD
@lblTokenURL=URL de token de CSRF
#XFLD
@csrfTokenInfoText=Si no se introduce, se utilizarán el URL base y la ruta de API
#XFLD
@lblCSRF=Token CSRF
#XFLD
@lblRequestBody=Cuerpo de la solicitud
#XFLD
@lblFormat=Formato
#XFLD
@lblResponse=Respuesta
#XFLD
@lblId=ID para recuperar el estado
#XFLD
@Id=ID
#XFLD
@lblSuccessIndicator=Indicador de éxito
#XFLD
@lblErrorIndicator=Indicador de error
#XFLD
@lblErrorReason=Motivo del error
#XFLD
@lblStatus=Estado
#XFLD
@lblApiTestRun=Ejecución de prueba de API
#XFLD
@lblRunStatus=Estado de ejecución
#XFLD
@lblLastRan=Fecha de la última ejecución
#XFLD
@lblTestRun=Ejecución de prueba
#XFLD
@lblDefaultHeader=Campos de cabecera predeterminados (pares clave-valor)
#XFLD
@lblAdditionalHeader=Campo de cabecera adicional
#XFLD
@lblKey=Clave
#XFLD
@lblEditJSON=Editar JSON
#XFLD
@lblTasks=Tareas
#XFLD
@lblRESTfullTask=API
#XFLD: add field button text
btnAddField=Añadir campo de cabecera
#XFLD: view Details link
@viewDetails=Ver detalles
#XTOL
tooltipTxt=Más
#XTOL
delete=Eliminar
#XBTN: ok button text
btnOk=OK
#XBTN: cancel button text
btnCancel=Cancelar
#XBTN: save button text
btnSave=Guardar
#XMSG Error: Object has duplicated technical name.
VAL_OBJECT_DUPLICATE_NAME=El objeto ''{0}'' ya existe en el repositorio. Escriba otro nombre.
#XMSG: loading message while opening task chain
loadTaskChain=Cargando la cadena de tareas...
#model properties
#XFLD
@status_panel=Estado de ejecución
#XFLD
@deploy_status_panel=Estado de despliegue
#XFLD
@status_lbl=Estado
#XFLD
@lblLastExecuted=Última ejecución
#XFLD
@lblNotExecuted=No ejecutado
#XFLD
@lblNotDeployed=No desplegado
#XFLD
errorDetailsTxt=No ha sido posible obtener el estado de ejecución
#XBTN: Schedule dropdown menu
SCHEDULE=Programación
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Editar programación
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Eliminar programación
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Crear programación
#XLNK
viewDetails=Ver detalles
#XMSG: error message for reading execution status from backend
backendErrorMsg=Parece que los datos no se están cargando desde el servidor en este momento. Intente volver a recuperar los datos.
#XFLD: Status text for Completed
@statusCompleted=Completada
#XFLD: Status text for Running
@statusRunning=En ejecución
#XFLD: Status text for Failed
@statusFailed=Error
#XFLD: Status text for Stopped
@statusStopped=Parado
#XFLD: Status text for Stopping
@statusStopping=Parando
#XFLD
@LoaderTitle=Cargando
#XFLD
#XFLD: Deploy status, dataflow has  been deployed / deployment date exists
@deployStatusActive=Desplegado
@deployStatusRevised=Actualizaciones locales
@deployStatusFailed=Error
@deployStatusPending=Desplegando...
@LoaderText=Obteniendo detalles del servidor
#XMSG
@msgDetailFetchError=Error al obtener detalles del servidor
#XFLD
@executeError=Error
#XFLD
@executeWarning=Advertencia
#XMSG
@executeConfirmDialog=Información
#XMSG
@executeunsavederror=Guarde su cadena de tareas antes de ejecutarla.
#XMSG
@executemodifiederror=Hay modificaciones sin guardar en la cadena de tareas. Guárdelas.
#XMSG
@executerunningerror=Se está ejecutando la cadena de tareas. Espere a que la ejecución actual se complete antes de iniciar una nueva.
#XMSG
@btnExecuteAnyway=Ejecutar de todas formas
#XMSG
@msgExecuteWithValidations=La cadena de tareas tiene errores de validación. Es posible que su ejecución provoque un error.
#XMSG
@msgRunDeployedVersion=Hay modificaciones para desplegar. Se ejecutará la última versión desplegada de la cadena de tareas. ¿Desea continuar?
#XMSG
#XMSG
@navToMonitoring=Abrir en el Monitor de cadenas de tareas
#XMSG
txtOR=O
#XFLD
@preview=Vista previa
#XMSG
txtand=y
#NOTR
@dateFormat=yyyy-MM-dd
#NOTR
@dateTimeFormatFilter=yyyy-MM-dd HH:mm:ss.
#NOTR
@timeFormat=HH:mm:ss
@lblColum=Columna
#XFLD
@lblCondition=Condición
#XFLD
@lblValue=Valor
#XMSG
@msgJsonInvalid=No se ha podido guardar la cadena de tareas porque hay errores en el JSON. Compruebe y solucione los errores.
#XTIT
@msgSaveFailTitle=JSON no válido.
#XMSG
NOT_CHAINABLE=El objeto ''{0}'' no puede añadirse a la cadena de tareas.
#XMSG
NOT_CHAINABLE_REMOTETABLE=Objeto ''{0}'': se cambiará el tipo de replicación.
#XMSG
searchTaskChain=Buscar objetos

#XFLD
@txtTaskChain=Cadena de tareas
#XFLD
@txtRemoteTable=Tabla remota
#XFLD
@txtRemoveData=Quitar datos replicados
#XFLD
@txtRemovePersist=Quitar datos guardados de forma persistente
#XFLD
@txtView=Ver
#XFLD
@txtDataFlow=Flujo de datos
#XFLD
@txtIL=Búsqueda inteligente
#XFLD
@txtTransformationFlow=Flujo de transformación
#XFLD
@txtReplicationFlow=Flujo de replicación
#XFLD
@txtDeltaLocalTable=Tabla local
#XFLD
@txtBWProcessChain=Cadena de procesos BW
#XFLD
@txtSQLScriptProcedure=Procedimiento de script SQL
#XFLD
@txtAPI=API
#XFLD
@txtMerge=Combinar
#XFLD
@txtOptimize=Optimizar
#XFLD
@txtVacuum=Eliminar registros

#XFLD
@txtRun=Ejecutar
#XFLD
@txtPersist=Guardar de forma persistente
#XFLD
@txtReplicate=Replicar
#XFLD
@txtDelete=Eliminar registros con tipo de cambio 'Eliminado’
#XFLD
@txtRunTC=Ejecutar la cadena de tareas
#XFLD
@txtRunBW=Ejecutar cadena de procesos BW
#XFLD
@txtRunSQLScriptProcedure=Ejecutar procedimiento de script SQL

#XFLD
@txtRunDataFlow=Ejecutar flujo de datos
#XFLD
@txtPersistView=Guardar vista de forma persistente
#XFLD
@txtReplicateTable=Replicar tabla
#XFLD
@txtRunIL=Ejecutar Búsqueda inteligente
#XFLD
@txtRunTF=Ejecutar flujo de transformación
#XFLD
@txtRunRF=Ejecutar flujo de replicación
#XFLD
@txtRemoveReplicatedData=Quitar datos replicados
#XFLD
@txtRemovePersistedData=Quitar datos guardados de forma persistente
#XFLD
@txtMergeData=Combinar
#XFLD
@txtOptimizeData=Optimizar
#XFLD
@txtVacuumData=Eliminar registros
#XFLD
@txtRunAPI=Ejecutar API

#XFLD storage type text
hdlfStorage=Archivo

@statusNew=No desplegado
@statusActive=Desplegado
@statusRevised=Actualizaciones locales
@statusPending=Desplegando...
@statusChangesToDeploy=Modificaciones para desplegar
@statusDesignTimeError=Error en tiempo de diseño
@statusRunTimeError=Error en tiempo de ejecución

#XTIT
txtNodes=Objetos en la cadena de tareas ({0})
#XBTN
@deleteNodes=Eliminar

#XMSG
@txtDropDataToDiagram=Arrastre y suelte objetos al diagrama.
#XMSG
@noData=Sin objetos

#XFLD
@txtTaskPosition=Posición de tarea

#input parameters and variables
#XFLD
lblInputParameters=Parámetros de entrada
#XFLD
ip_name=Nombre
#XFLD
ip_value=Valor
#XTEXT
@noObjectsFound=No se ha encontrado ningún objeto

#XMSG
@msgExecuteSuccess=Se ha iniciado la ejecución de la cadena de tareas.
#XMSG
@msgExecuteFail=No se ha podido ejecutar la cadena de tareas.
#XMSG
@msgDeployAndRunSuccess=Se han iniciado el despliegue y la ejecución de la cadena de tareas.
#XMSG
@msgDeployAndRunFail=Se ha producido un error en el despliegue y la ejecución de la cadena de tareas.
#XMSG
@titleExecuteBusy=Espere.
#XMSG
@msgExecuteBusy=Estamos preparando sus datos para iniciar la ejecución de la cadena de tareas.
#XMSG
@msgAPITestRunSuccess=La ejecución de pruebas de API ha comenzado.
#XMSG
@msgAPIExecuteBusy=Estamos preparando sus datos para comenzar a ejecutar la prueba de API.

#XTOL
txtOpenInEditor=Abrir en editor
#XTOL
txtPreviewData=Vista previa de datos

#datapreview
#XMSG
@msgDataPreviewNotSupp=La vista previa de datos no está disponible para este objeto.

#XMSG Error: empty model
VAL_MODEL_EMPTY=Su modelo parece estar vacío. Añada algunos objetos.
#XMSG Error: deploy model
@msgDeployBeforeRun=Debe desplegar la cadena de tareas antes de ejecutarla.
#BTN: close dialog
btnClose=Cerrar

#XMSG Object is not Deployed
VAL_NOT_DEPLOYED=Es necesario desplegar el objeto ''{0}'' para continuar con la cadena de tareas.
#XMSG Object is having runtime error
VAL_RUNTIME_ERROR=El objeto ''{0}'' devuelve un error de tiempo de ejecución. Compruebe el objeto.
#XMSG Object is having design time error
VAL_DESIGNTIME_ERROR=El objeto ''{0}'' devuelve un error de tiempo de diseño. Compruebe el objeto.
#XMSG Procedure Object has been deleted
VAL_DELETED_PROCEDURE=Se han eliminado los siguientes procedimientos: {0}
#XMSG New Parameter added to Procedure object
VAL_NEW_PARAMETER_ADDED=Se han añadido nuevos parámetros al procedimiento "{1}": "{0}". Vuelva a desplegar la cadena de tareas.
#XMSG Parameter removed from Procedure object
VAL_PARAMETER_DELETED=Se han quitado parámetros del procedimiento "{1}": "{0}". Vuelva a desplegar la cadena de tareas.
#XMSG Parameter data type changed in Procedure object
VAL_PARAMETER_DATATYPE_MODIFIED=El tipo de datos del parámetro "{0}" se ha modificado de "{1}" a "{2}" en el procedimiento "{3}".
#XMSG Parameter length changed of specific datatype in Procedure object
VAL_PARAMETER_LENGTH_MODIFIED=La longitud del parámetro "{0}" se ha modificado de "{1}" a "{2}" en el procedimiento "{3}".
#XMSG Parameter precision changed of Decimal datatype in Procedure object
VAL_PARAMETER_PRECISION_MODIFIED=La precisión del parámetro "{0}" se ha modificado de "{1}" a "{2}" en el procedimiento "{3}".
#XMSG Parameter scale changed of Decimal datatype in Procedure object
VAL_PARAMETER_SCALE_MODIFIED=La escala del parámetro "{0}" se ha modificado de "{1}" a "{2}" en el procedimiento "{3}".
#XMSG Parameter value not provided in Procedure object
VAL_SQL_SCRIPT_PROCEDURE_PARAMETER_VALUE_MISSING=No ha introducido valores para los parámetros de entrada necesarios para el procedimiento "{0}": "{1}"
#XMSG Remote Table activity changes to Remove Replicated Data
VAL_REMOVE_REPLICATED_DATA=La siguiente ejecución de una cadena de tareas modificará el tipo de acceso a los datos, que ya no se cargarán en tiempo real.
#XMSG Remote Table activity changes
REPLICATION_TYPE_CHANGED=Objeto "{0}": Se modificará el tipo de replicación.

#XFLD
@lblStartNode=Nodo de inicio
#XFLD
@lblEndNode=Nodo de fin
#XFLD
@linkTo={0} a {1}
#XTOL
@txtViewDetails=Ver detalles

#XTOL
txtOpenImpactLineage=Análisis de impacto y linaje
#XFLD
@emailNotifications=Notificaciones por correo electrónico
#XFLD
@txtReset=Restablecer
#XFLD
@emailMsgWarning=La plantilla de correo electrónico predeterminada se enviará cuando el mensaje de correo electrónico esté vacío.
#XFLD
@notificationSettings=Opciones de notificación
#XFLD
@recipientEmailAddr=Dirección de correo electrónico del destinatario
#XFLD
@emailSubject=Asunto del correo electrónico
@emailSubjectText=La cadena de tareas <TASKCHAIN_NAME> ha concluido con el estado <STATUS>
#XFLD
@emailMessage=Mensaje de correo electrónico
@emailMessageText=Estimado/a usuario/a:\n\n Le informamos de que la cadena de tareas <TASKCHAIN_NAME>, ejecutada a las <START_TIME>, ha concluido con el estado <STATUS>. La ejecución ha finalizado a las <END_TIME>.\n\nDetalles:\nEspacio:<SPACE_NAME>\nError:<ERROR_MESSAGE>
@selectEmailRecipientDialogTitle=Seleccionar dirección de correo electrónico del destinatario
@tenantMembers=Miembros del arrendatario
@others=Otros ({0})
@selectedEmailAddress=Destinatarios seleccionados
@add=Añadir
@placeholder=Marcador de posición
@description=Descripción
@copyText=Copiar texto
@taskchainDetailsPlaceholder=Marcadores de posición para los detalles de la cadena de tareas
@placeholderCopied=Se ha copiado el marcador de posición
@invalidEmailInfo=Introduzca una dirección de correo electrónico correcta.
@maxMembersAlreadyAdded=Ya ha añadido el máximo de 20 miembros.
@enterEmailAddress=Introducir dirección de correo electrónico de usuario
@inCorrectPlaceHolder={0} no es ningún marcador de posición previsto en el cuerpo del correo electrónico.
@nsOFF=No enviar notificaciones
@nsFAILED=Enviar una notificación por correo electrónico solo cuando la ejecución concluya con un error
@nsCOMPLETED=Enviar una notificación por correo electrónico solo cuando la ejecución concluya correctamente
@nsANY=Enviar un correo electrónico cuando la ejecución haya concluido
@phStatus=Estado de la cadena de tareas: CORRECTO|ERROR
@phTaskChainTName=Nombre técnico de la cadena de tareas
@phTaskChainBName=Nombre empresarial de la cadena de tareas
@phLogId=ID de log de la ejecución
@phUser=Usuario que está ejecutando la cadena de tareas
@phLogUILink=Enlace a la visualización del log de la cadena de tareas
@phStartTime=Hora de inicio de la ejecución
@phEndTime=Hora de fin de la ejecución
@phErrMsg=Primer mensaje de error del log de tareas. El log está vacío en caso de SUCCESS
@phSpaceName=Nombre técnico del espacio
@emailFormatError=Formato de correo electrónico no válido
@emailFormatErrorInListText=Se ha introducido un formato de correo electrónico no válido en la lista de miembros no arrendatarios.
@emailSubjectTemplateText=Notificación para la cadena de tareas: $$taskChainName$$ - Espacio: $$spaceId$$ - Estado: $$status$$
@emailMessageTemplateText=Hola:\n\n La cadena de tareas con el nombre $$taskChainName$$ ha finalizado con el estado $$status$$. \n Aquí tiene otros detalles al respecto:\n - Nombre técnico de la cadena de tareas: $$taskChainName$$ \n - ID de log de la ejecución de la cadena de tareas: $$logId$$ \n - Usuario que ha ejecutado la cadena de tareas: $$user$$ \n - Enlace a la visualización del log de la cadena de tareas: $$uiLink$$ \n - Hora de inicio de la ejecución de la cadena de tareas: $$startTime$$ \n - Hora de fin de la ejecución de la cadena de tareas: $$endTime$$ \n - Nombre del espacio: $$spaceId$$ \n
@deleteEmailRecepient=Eliminar destinatario
@emailInputDisabledText=Despliegue la cadena de tareas para añadir destinatarios de correo electrónico.
@tenantOwnerDomainMatchErrorText=El dominio de la dirección de correo electrónico no coincide con el del propietario del arrendatario: {0}
@totalEmailIdLimitInfoText=Puede seleccionar hasta 20 destinatarios de correo electrónico, incluidos los usuarios que son miembros del arrendatario y otros destinatarios.
@emailDomainInfoText=Solo se aceptan direcciones de correo electrónico con el dominio: {0}.
@duplicateEmailErrorText=Hay destinatarios de correo electrónico duplicados en la lista.

#XFLD Zorder Title
@txtZorderTitle=Columnas de orden Z de Apache Spark

#XFLD Zorder NoColumn
@txtZorderNoColumn=No se ha encontrado ninguna columna de orden Z

#XFLD Zorder Column PrimaryKey
@txtZorderColumnPrimaryKey=Clave primaria

#XFLD
@lblOperators=Operadores
addNewSelector=Añadir como tarea nueva
parallelSelector=Añadir como tarea paralela
replaceSelector=Sustituir la tarea existente
addparallelbranch=Añadir como ramificación paralela
addplaceholder=Añadir marcador de posición
addALLOperation=Operador ALL
addOROperation=Operador ANY
addplaceholdertocanvas=Añadir marcador de posición a área de diseño
addplaceholderonselected=Añadir marcador de posición a tarea seleccionada
#XFLD: Add Parallel Branch to Selected Task
addparallelbranchonselected=Añadir ramificación paralela tras la tarea seleccionada
addOperator=Añadir operador
txtAdd=Añadir
txtPlaceHolderText=Arrastrar y soltar una tarea aquí
@lblLayout=Disposición

#XMSG
VAL_UNCONNECTED_TASK=La tarea ''{0}'' no está conectada a la cadena de tareas.
#XMSG
VAL_TASK_SHOULD_HAVE_ONE_INCOMING_LINK=La tarea "{0}" solo puede tener un enlace de entrada.
#XMSG
VAL_TASK_SHOULD_HAVE_INCOMING_LINK=La tarea "{0}" debe tener un enlace de entrada.
#XMSG
VAL_UNCONNECTED_OPERATOR=El operador ''{0}'' no está conectado a la cadena de tareas.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ATLEAST_TWO_INCOMING_LINK=El operador ''{0}'' debe tener al menos dos enlaces de entrada.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ONE_OUT_LINK=El operador ''{0}'' debe tener al menos un enlace de salida.
#XMSG
VAL_CIRCULAR_REFERENCE_EXITS=Existe un loop circular en la cadena de tareas ''{0}''.
#XMSG
VAL_UNCONNECTED_BRANCH=El objeto/la ramificación ''{0}'' no están conectados a la cadena de tareas.
#XMSG
VAL_TASK_TRIGGERED_PARALLEL_MORE_THAN_ONE_TIME=La tarea ''{0}'' está conectada más de una vez en el modo paralelo. Quite los duplicados para continuar.


txtBegin=Comenzar
txtNodesInLink=Objetos implicados
#XTOL Tooltip for a context button on diagram
openInNewTab=Abrir en una pestaña nueva
#XTOL Tooltip for a context button on diagram
txtConnectToTaskOperator=Arrastra para conectar
@emailUpdateError=Error al actualizar la lista de notificaciones por correo electrónico

#XMSG
noTeamPrivilegeTxt=No tiene permiso para ver una lista de los miembros del arrendatario. Utilice la pestaña Otros para añadir manualmente destinatarios de correo electrónico.

#XFLD Package
@txtPackage=Paquete

#XMSG warning for repository package assignment changed. {1} - package name
VAL_PACKAGE_CHANGED=Ha asignado este objeto al paquete "{1}". Haga clic en Guardar para confirmar y validar esta modificación. Tenga en cuenta que la asignación a un paquete no se puede deshacer en este editor tras guardar.
#XMSG warning for repository package dependency issue.
MISSING_DEPENDENCY=Las dependencias del objeto "{0}" no pueden resolverse en el contexto del paquete "{1}".

#Warning messages for the folders in the "Others" tab of task chain editor
#XMSG generic warning if getting the objects in a particular folder fails with unhandled errors
getNonRepoArtefactTypeDescendantsError=Se ha producido un problema al recuperar los objetos en la carpeta que ha seleccionado.
#BW Process Chains folder related warning messages
#XMSG warning if user does not have the necessary permission related to the SAP BW Bridge space
missingPrivilegeForBridgeSpaceError=No tiene el permiso necesario para ver o incluir cadenas de procesos de BW en una cadena de tareas.
#XMSG warning if there was a problem retrieving the BW process chains due to bridge space membership, bridge connection issues or other errors
generalBridgeSpaceError=Se ha producido un problema al recuperar las cadenas de procesos de BW.
#XMSG warning if the BW API for retrieving the BW process chains from the SAP BW Bridge tenant fails
bwApiClientError=Se ha producido un problema al recuperar las cadenas de procesos de BW del arrendatario de puente de SAP BW.
#XMSG warning if BW process chains list is empty
bwApiEmptyListError=No existe ninguna cadena de procesos de BW en el arrendatario de puente de SAP BW.
#XMSG: Task log message for invalid OIDC configuration in the SAP BW Bridge tenant
bwBridgeOIDCConfigStatusInvalid=La autenticación OpenID no está configurada para este arrendatario. Utilice el clientID "{0}" y el tokenURL "{1}" para configurar la autenticación OpenID en el arrendatario del puente de SAP BW como se describe en la nota SAP 3536298.
#XMSG BW Process Chain Object has been deleted
VAL_DELETED_BW=Es posible que las siguientes cadenas de procesos de BW se hayan eliminado del arrendatario de puente de SAP: {0}
#BW SQL Script Procedures folder related warning messages
#XFLD waring if the Procedure list is empty
procedureAPIEmptyList=No se ha creado ningún procedimiento o no se ha concedido la autorización EXECUTE al esquema Open SQL.
#Change digram orientations
changeOrientations=Modificar orientaciones


# placeholder for the API Path
apiPath=Por ejemplo: /job/v1
# placeholder for the status API Path
statusAPIPath=Por ejemplo: /job/v1/{id}/status
# valueStateText for the API Path
apiPathRequired=La ruta de API es obligatoria
#placeholder for the CSRF Token URL
csrfTokenURL=Solo se admite HTTPS
# Response Type 1
statusCode=Obtener resultado del código de estado HTTP
# Response Type 2
locationHeader=Obtener resultado del código de estado HTTP y la cabecera de ubicación
# Response Type 3
responseBody=Obtener resultado del código de estado HTTP y el cuerpo de respuesta
# placeholder for ID
idPlaceholder=Introducir ruta JSON
# placeholder for indicator value
indicatorValue=Introducir valor
# Placeholder for key
keyPlaceholder=Introducir clave
# Error message for missing key
KeyRequired=La clave es obligatoria
# Error message for invalid key format
invalidKeyFormat=La clave de cabecera que ha introducido no está permitida. Las cabeceras válidas son:<ul><li>"prefer"</li><li>Cabeceras que comiencen con "x-", excepto "x-forwarded-host"</li><li>Las cabeceras que contienen caracteres alfanuméricos, "-", o "_"</li></ul>
# Error message for missing value
valueRequired=El valor es obligatorio
# Error message for invalid characters in value
invalidValueCharacters=La cabecera contiene caracteres no válidos. Los caracteres especiales permitidos son:\t ";", ":", "-", "_", ",", "?", "/" y "*"
# Validation message for invoke api path
apiPathValidation=Introduzca una ruta de API válida, por ejemplo: /job/v1
# Validation message for JSON path
jsonPathValidation=Introduzca una ruta JSON válida
# Validation message for success/error indicator
indicatorValueValidation=El valor del indicador debe comenzar con un carácter alfanumérico y puede incluir los siguientes caracteres especiales:\t "-" y "_"
# Error message for JSON path
jsonPathRequired=La ruta JSON es obligatoria
# Error message for invalid API Technical Name
invalidTechnicalName=El nombre técnico contiene caracteres no válidos
# Error message for empty Technical Name
emptyTechnicalName=El nombre técnico es obligatorio
# Tooltip for codeEditor dialog
codeEditorTooltip=Abrir la ventana de edición JSON
# Status Api path validation message
validationStatusAPIPath=Introduzca una ruta de API válida, por ejemplo: /job/v1/{id}/status
# CSRF token URL validation message
validationCSRFTokenURL=El URL de token CSRF debe comenzar con 'https://' y tener un URL válido
# Select a connection
selectConnection=Seleccionar una conexión
# Validation message for connection item error
connectionNotReplicated=La conexión no es válida actualmente para ejecutar tareas de API. Abra la aplicación "Conexiones" y vuelva a introducir sus credenciales para corregir la conexión HTTP.
# Validation message for API task properties wrong value or missing
VAL_REST_API_PROPERTIES_MISSING=La tarea de API "{0}" tiene valores incorrectos, o le faltan valores, para las siguientes propiedades: {1}
# Validation message for API connection api is throwing some error or selected connection has some warning
VAL_REST_API_CONNECTION=La tarea de API "{0}" tiene un problema con la conexión HTTP. Abra la aplicación "Conexiones" y verifique la conexión.
# Validation message for API task properties when delected connection
VAL_REST_API_CONNECTION_DELETED=La tarea de API "{0}" tiene una conexión "{1}" eliminada
# Validation message for API task properties when connection cannot be consumed in the API task
VAL_REST_API_CONNECTION_CONSUMPTION_WARNING=La tarea de API "{0}" tiene una conexión "{1}" que no se puede usar para ejecutar tareas de API. Abra la aplicación "Conexiones" y vuelva a introducir sus credenciales para establecer la conectividad para las tareas de API
# Information message if synchrnous mode is selected in the API Invocation then we don't need status information
modeInfoText=En el modo sincrónico, el panel de estado no se muestra para las invocaciones de API
# validation dialog button for Run API Test
saveAnyway=Guardar de todas formas
# validation message for technical name
technicalNameValidation=El nombre técnico debe ser unívoco dentro de la cadena de tareas. Elija otro nombre técnico
# Connection error message
getHttpConnectionsError=Error al obtener las conexiones HTTP
# Msg for the API test run when the Task chain is not saved
taskChainNotSaved=La cadena de tareas debe guardarse antes de ejecutar la prueba de API
# Msg failed to run API test run
@failedToRunAPI=Error al ejecutar la prueba de API
# Msg for the API test run when its already in running state
apiTaskRunning=Ya hay una ejecución de prueba de API en curso. ¿Desea iniciar una nueva?

topToBtm=Arriba-abajo
leftToRight=Izquierda-derecha

#XTIT Apache Spark Application Settings
txtHeaderApacheSettings=Opciones de la aplicación Apache Spark
#XFLD Use Default
txtUseSpaceDefault=Utilizar valor predeterminado
#XFLD Application
txtApplication=Aplicación
#XFLD Define new settings for this Task
txtNewSettings=Definir opciones nuevas para esta tarea

#XFLD Use Default
txtUseDefault=Utilizar valor predeterminado




