#XTOL Undo
@undo=Rückgängig
#XTOL Redo
@redo=Wiederholen
#XTOL Delete Selected Symbol
@deleteNode=Ausgewähltes Symbol löschen
#XTOL Zoom to Fit
@zoomToFit=An Fenstergröße anpassen
#XTOL Auto Layout
@autoLayout=Automatisches Layout
#XMSG
@welcomeText=Ziehen Sie Objekte aus dem Bereich links in diesen Grafikbereich.
#XMSG
@txtNoData=Sie haben offenbar noch kein Objekt hinzugefügt.
#XMSG
VAL_ENTER_VALID_STRING_GEN=Geben Sie eine gültige Zeichenfolge ein, deren Länge kleiner oder gleich {0} ist.
#XMSG
@noParametersMsg=Diese Prozedur weist keine Eingabeparameter auf.
#XMSG
@ip_enterValueMsg=Die Option "{0}" (SQL-Skript-Prozedur ausführen) enthält Eingabeparameter. Sie können jeweils einen Wert festlegen.
#XTOL
@validateModel=Validierungsmeldungen
#XTOL
@hierarchy=Hierarchie
#XTOL
@columnCount=Anzahl der Spalten
#XFLD
@yes=Ja
#XFLD
@no=Nein
#XTIT Save Dialog param
@modelNameTaskChain=Aufgabenkette
#properties panel
@lblPropertyTitle=Eigenschaften
#XFLD
@lblGeneral=Allgemein
#XFLD : Setting
@lblSetting=Einstellungen
#XTXT: Delta Enabled Local Table Deletion
txtDeltaTableDeletion=Alle vollständig verarbeiteten Datensätze mit dem Änderungstyp "Gelöscht", die älter sind als
#XTXT: Delta Enabled Local Table Deletion Input field description
desDeltaTableDeletion=Tage
#XFLD: Data Activation label
@lblDataActivation=Datenaktivierung
#XFLD: Latency label
@latency=Wartezeit
#XTEXT: Text for Latency dropdown
txtLatencyDefault=Standard
#XTEXT: Text 1 hour
txtOneHour=1 Stunde
#XTEXT: Text 2 hours
txtTwoHours=2 Stunden
#XTEXT: Text 3 hours
txtThreeHours=3 Stunden
#XTEXT: Text 4 hours
txtFourHours=4 Stunden
#XTEXT: Text 6 hours
txtSixHours=6 Stunden
#XTEXT: Text 12 hours
txtTwelveHours=12 Stunden
#XTEXT: Text 1 day
txtOneDay=1 Tag
#XFLD: Latency label
@autoRestartHead=Automatischer Neustart
#XFLD
@lblConnectionName=Verbindung
#XFLD
@lblQualifiedName=Qualifizierter Name
#XFLD
@lblSpaceName=Space-Name
#XFLD
@lblLocalSchemaName=Lokales Schema
#XFLD
@lblType=Objekttyp
#XFLD
@lblActivity=Aktivität
#XFLD
@lblTableName=Tabellenname
#XFLD
@lblBusinessName=Betriebswirtschaftlicher Name
#XFLD
@lblTechnicalName=Technischer Name
#XFLD
@lblSpace=Space
#XFLD
@lblLabel=Bezeichner
#XFLD
@lblDataType=Datentyp
#XFLD
@lblDescription=Beschreibung
#XFLD
@lblStorageType=Speicher
#XFLD
@lblHTTPConnection=Generische HTTP-Verbindung
#XFLD
@lblAPISettings=Generische API-Einstellungen
#XFLD
@header=Header
#XFLD
@lblInvoke=API-Aufruf
#XFLD
@lblMethod=Methode
#XFLD
@lblUrl=Basis-URL
#XFLD
@lblAPIPath=API-Pfad
#XFLD
@lblMode=Modus
#XFLD
@lblCSRFToken=CSRF-Token erforderlich
#XFLD
@lblTokenURL=CSFR-Token-URL
#XFLD
@csrfTokenInfoText=Ohne Angabe werden die Basis-URL und der API-Pfad verwendet
#XFLD
@lblCSRF=CSRF-Token
#XFLD
@lblRequestBody=Anforderungstext
#XFLD
@lblFormat=Format
#XFLD
@lblResponse=Antwort
#XFLD
@lblId=ID für Statusabruf
#XFLD
@Id=ID
#XFLD
@lblSuccessIndicator=Erfolgsindikator
#XFLD
@lblErrorIndicator=Fehlerindikator
#XFLD
@lblErrorReason=Fehlerursache
#XFLD
@lblStatus=Status
#XFLD
@lblApiTestRun=API-Testlauf
#XFLD
@lblRunStatus=Laufstatus
#XFLD
@lblLastRan=Zuletzt ausgeführt am
#XFLD
@lblTestRun=Testlauf
#XFLD
@lblDefaultHeader=Standard-Header-Felder (Schlüssel-Wert-Paare)
#XFLD
@lblAdditionalHeader=Zusätzliches Header-Feld
#XFLD
@lblKey=Schlüssel
#XFLD
@lblEditJSON=JSON bearbeiten
#XFLD
@lblTasks=Aufgaben
#XFLD
@lblRESTfullTask=API
#XFLD: add field button text
btnAddField=Header-Feld hinzufügen
#XFLD: view Details link
@viewDetails=Details anzeigen
#XTOL
tooltipTxt=Mehr
#XTOL
delete=Löschen
#XBTN: ok button text
btnOk=OK
#XBTN: cancel button text
btnCancel=Abbrechen
#XBTN: save button text
btnSave=Sichern
#XMSG Error: Object has duplicated technical name.
VAL_OBJECT_DUPLICATE_NAME=Objekt "{0}" ist bereits im Repository vorhanden. Geben Sie einen anderen Namen ein.
#XMSG: loading message while opening task chain
loadTaskChain=Aufgabenkette wird geladen …
#model properties
#XFLD
@status_panel=Laufstatus
#XFLD
@deploy_status_panel=Aktivierungsstatus
#XFLD
@status_lbl=Status
#XFLD
@lblLastExecuted=Letzter Lauf
#XFLD
@lblNotExecuted=Noch nicht ausgeführt
#XFLD
@lblNotDeployed=Nicht aktiviert
#XFLD
errorDetailsTxt=Status des Laufs konnte nicht abgerufen werden
#XBTN: Schedule dropdown menu
SCHEDULE=Einplanen
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Zeitplan bearbeiten
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Zeitplan löschen
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Zeitplan anlegen
#XLNK
viewDetails=Details anzeigen
#XMSG: error message for reading execution status from backend
backendErrorMsg=Anscheinend werden die Daten derzeit nicht vom Server geladen. Versuchen Sie erneut, die Daten abzurufen.
#XFLD: Status text for Completed
@statusCompleted=Abgeschlossen
#XFLD: Status text for Running
@statusRunning=Wird ausgeführt
#XFLD: Status text for Failed
@statusFailed=Fehlgeschlagen
#XFLD: Status text for Stopped
@statusStopped=Gestoppt
#XFLD: Status text for Stopping
@statusStopping=Wird gestoppt
#XFLD
@LoaderTitle=Wird geladen
#XFLD
#XFLD: Deploy status, dataflow has  been deployed / deployment date exists
@deployStatusActive=Aktiviert
@deployStatusRevised=Lokale Aktualisierungen
@deployStatusFailed=Fehlgeschlagen
@deployStatusPending=Wird aktiviert …
@LoaderText=Details werden vom Server abgerufen
#XMSG
@msgDetailFetchError=Fehler beim Abrufen von Details vom Server
#XFLD
@executeError=Fehler
#XFLD
@executeWarning=Warnung
#XMSG
@executeConfirmDialog=Info
#XMSG
@executeunsavederror=Sichern Sie die Aufgabenkette, bevor Sie sie ausführen.
#XMSG
@executemodifiederror=Es gibt nicht gesicherte Änderungen in der Aufgabenkette. Sichern Sie diese.
#XMSG
@executerunningerror=Die Aufgabenkette wird gerade ausgeführt. Warten Sie, bis der aktuelle Lauf abgeschlossen ist, bevor Sie mit einem neuen beginnen.
#XMSG
@btnExecuteAnyway=Trotzdem ausführen
#XMSG
@msgExecuteWithValidations=Die Aufgabenkette weist Validierungsfehler auf. Das Ausführen der Aufgabenkette kann fehlschlagen.
#XMSG
@msgRunDeployedVersion=Es gibt Änderungen, die aktiviert werden müssen. Die letzte aktivierte Version der Aufgabenkette wird ausgeführt. Möchten Sie fortfahren?
#XMSG
#XMSG
@navToMonitoring=Im Aufgabenketten-Monitor öffnen
#XMSG
txtOR=ODER
#XFLD
@preview=Vorschau
#XMSG
txtand=UND
#NOTR
@dateFormat=yyyy-MM-dd
#NOTR
@dateTimeFormatFilter=yyyy-MM-dd HH:mm:ss.
#NOTR
@timeFormat=HH:mm:ss
@lblColum=Spalte
#XFLD
@lblCondition=Bedingung
#XFLD
@lblValue=Wert
#XMSG
@msgJsonInvalid=Die Aufgabenkette konnte nicht gesichert werden, da die JSON Fehler enthält. Überprüfen Sie sie, und beheben Sie die Fehler.
#XTIT
@msgSaveFailTitle=Ungültige JSON-Datei.
#XMSG
NOT_CHAINABLE=Objekt ''{0}'' kann nicht zur Aufgabenkette hinzugefügt werden.
#XMSG
NOT_CHAINABLE_REMOTETABLE=Objekt "{0}": Replikationstyp wird geändert.
#XMSG
searchTaskChain=Objekte suchen

#XFLD
@txtTaskChain=Aufgabenkette
#XFLD
@txtRemoteTable=Remote-Tabelle
#XFLD
@txtRemoveData=Replizierte Daten entfernen
#XFLD
@txtRemovePersist=Persistierte Daten entfernen
#XFLD
@txtView=View
#XFLD
@txtDataFlow=Datenfluss
#XFLD
@txtIL=Intelligente Suche
#XFLD
@txtTransformationFlow=Transformationsfluss
#XFLD
@txtReplicationFlow=Replikationsfluss
#XFLD
@txtDeltaLocalTable=Lokale Tabelle
#XFLD
@txtBWProcessChain=BW-Prozesskette
#XFLD
@txtSQLScriptProcedure=SQL-Skript-Prozedur
#XFLD
@txtAPI=API
#XFLD
@txtMerge=Zusammenführen
#XFLD
@txtOptimize=Optimieren
#XFLD
@txtVacuum=Datensätze löschen

#XFLD
@txtRun=Ausführen
#XFLD
@txtPersist=Persistieren
#XFLD
@txtReplicate=Replizieren
#XFLD
@txtDelete=Datensätze mit dem Änderungstyp "Gelöscht" löschen
#XFLD
@txtRunTC=Aufgabenkette ausführen
#XFLD
@txtRunBW=BW-Prozesskette ausführen
#XFLD
@txtRunSQLScriptProcedure=SQL-Skript-Prozedur ausführen

#XFLD
@txtRunDataFlow=Datenfluss ausführen
#XFLD
@txtPersistView=View persistieren
#XFLD
@txtReplicateTable=Tabelle replizieren
#XFLD
@txtRunIL=Intelligente Suche ausführen
#XFLD
@txtRunTF=Transformationsfluss ausführen
#XFLD
@txtRunRF=Replikationsfluss ausführen
#XFLD
@txtRemoveReplicatedData=Replizierte Daten entfernen
#XFLD
@txtRemovePersistedData=Persistierte Daten entfernen
#XFLD
@txtMergeData=Zusammenführen
#XFLD
@txtOptimizeData=Optimieren
#XFLD
@txtVacuumData=Datensätze löschen
#XFLD
@txtRunAPI=API ausführen

#XFLD storage type text
hdlfStorage=Datei

@statusNew=Nicht aktivieren
@statusActive=Aktiviert
@statusRevised=Lokale Aktualisierungen
@statusPending=Wird aktiviert …
@statusChangesToDeploy=Zu aktivierende Änderungen
@statusDesignTimeError=Entwurfszeitfehler
@statusRunTimeError=Laufzeitfehler

#XTIT
txtNodes=Objekte in Aufgabenkette ({0})
#XBTN
@deleteNodes=Löschen

#XMSG
@txtDropDataToDiagram=Ziehen Sie Objekte auf das Diagramm.
#XMSG
@noData=Keine Objekte

#XFLD
@txtTaskPosition=Position der Aufgabe

#input parameters and variables
#XFLD
lblInputParameters=Eingabeparameter
#XFLD
ip_name=Name
#XFLD
ip_value=Wert
#XTEXT
@noObjectsFound=Keine Objekte gefunden

#XMSG
@msgExecuteSuccess=Ausführung der Aufgabenkette wurde gestartet.
#XMSG
@msgExecuteFail=Aufgabenkette konnte nicht ausgeführt werden.
#XMSG
@msgDeployAndRunSuccess=Aktivierung und Lauf der Aufgabenkette hat begonnen.
#XMSG
@msgDeployAndRunFail=Fehler beim Aktivieren und Ausführen der Aufgabenkette.
#XMSG
@titleExecuteBusy=Bitte warten.
#XMSG
@msgExecuteBusy=Wir bereiten Ihre Daten vor, um die Ausführung der Aufgabenkette zu starten.
#XMSG
@msgAPITestRunSuccess=API-Testlauf wurde gestartet.
#XMSG
@msgAPIExecuteBusy=Wir bereiten Ihre Daten vor, um die Ausführung des API-Testlaufs zu starten.

#XTOL
txtOpenInEditor=In Editor öffnen
#XTOL
txtPreviewData=Datenvorschau

#datapreview
#XMSG
@msgDataPreviewNotSupp=Datenvorschau ist für dieses Objekt nicht verfügbar.

#XMSG Error: empty model
VAL_MODEL_EMPTY=Ihr Modell ist offenbar leer. Fügen Sie einige Objekte hinzu.
#XMSG Error: deploy model
@msgDeployBeforeRun=Sie müssen die Aufgabenkette aktivieren, bevor Sie sie ausführen.
#BTN: close dialog
btnClose=Schließen

#XMSG Object is not Deployed
VAL_NOT_DEPLOYED=Objekt "{0}" muss aktiviert werden, um mit der Aufgabenkette fortfahren zu können.
#XMSG Object is having runtime error
VAL_RUNTIME_ERROR=Objekt "{0}" gibt einen Laufzeitfehler zurück. Prüfen Sie das Objekt.
#XMSG Object is having design time error
VAL_DESIGNTIME_ERROR=Objekt "{0}" gibt einen Entwurfszeitfehler zurück. Prüfen Sie das Objekt.
#XMSG Procedure Object has been deleted
VAL_DELETED_PROCEDURE=Die folgenden Prozeduren wurden gelöscht: {0}
#XMSG New Parameter added to Procedure object
VAL_NEW_PARAMETER_ADDED=Neue Parameter zu Prozedur "{1}" hinzugefügt: "{0}". Aktivieren Sie die Aufgabenkette erneut.
#XMSG Parameter removed from Procedure object
VAL_PARAMETER_DELETED=Parameter aus Prozedur "{1}" entfernt: "{0}". Aktivieren Sie die Aufgabenkette erneut.
#XMSG Parameter data type changed in Procedure object
VAL_PARAMETER_DATATYPE_MODIFIED=Datentyp von Parameter "{0}" in Prozedur "{3}" von "{1}" in "{2}" geändert.
#XMSG Parameter length changed of specific datatype in Procedure object
VAL_PARAMETER_LENGTH_MODIFIED=Länge von Parameter "{0}" in Prozedur "{3}" von "{1}" in "{2}" geändert.
#XMSG Parameter precision changed of Decimal datatype in Procedure object
VAL_PARAMETER_PRECISION_MODIFIED=Präzision von Parameter "{0}" in Prozedur "{3}" von "{1}" in "{2}" geändert.
#XMSG Parameter scale changed of Decimal datatype in Procedure object
VAL_PARAMETER_SCALE_MODIFIED=Skala von Parameter "{0}" in Prozedur "{3}" von "{1}" in "{2}" geändert.
#XMSG Parameter value not provided in Procedure object
VAL_SQL_SCRIPT_PROCEDURE_PARAMETER_VALUE_MISSING=Sie haben keine Werte für die für Prozedur "{0}" erforderlichen Eingabeparameter eingegeben: "{1}"
#XMSG Remote Table activity changes to Remove Replicated Data
VAL_REMOVE_REPLICATED_DATA=Durch den nächsten Lauf einer Aufgabenkette wird der Datenzugriffstyp geändert, und Daten werden nicht mehr in Echtzeit hochgeladen.
#XMSG Remote Table activity changes
REPLICATION_TYPE_CHANGED=Objekt "{0}": Replikationstyp wird geändert.

#XFLD
@lblStartNode=Startknoten
#XFLD
@lblEndNode=Endknoten
#XFLD
@linkTo={0} nach {1}
#XTOL
@txtViewDetails=Details anzeigen

#XTOL
txtOpenImpactLineage=Analyse der Auswirkungen und der Herkunft
#XFLD
@emailNotifications=E-Mail-Benachrichtigungen
#XFLD
@txtReset=Zurücksetzen
#XFLD
@emailMsgWarning=Wenn die E-Mail-Nachricht leer ist, wird die Standardvorlage für E-Mails gesendet.
#XFLD
@notificationSettings=Benachrichtigungseinstellungen
#XFLD
@recipientEmailAddr=Empfänger-E-Mail-Adresse
#XFLD
@emailSubject=E-Mail-Betreff
@emailSubjectText=Aufgabenkette <NAME_DER_AUFGABENKETTE> mit Status <STATUS> abgeschlossen
#XFLD
@emailMessage=E-Mail-Nachricht
@emailMessageText=Sehr geehrter Benutzer,\n\n die Ausführung der um <STARTZEIT> ausgeführten Aufgabenkette <NAME_DER_AUFGABENKETTE> wurde mit Status <STATUS> abgeschlossen. Die Ausführung wurde um <ENDZEIT> abgeschlossen.\n\nDetails:\nSpace:<SPACE-NAME>\nFehler:<FEHLERMELDUNG>
@selectEmailRecipientDialogTitle=Empfänger-E-Mail-Adresse auswählen
@tenantMembers=Tenant-Elemente
@others=Sonstige ({0})
@selectedEmailAddress=Ausgewählte Empfänger
@add=Hinzufügen
@placeholder=Platzhalter
@description=Beschreibung
@copyText=Text kopieren
@taskchainDetailsPlaceholder=Platzhalter für Details der Aufgabenkette
@placeholderCopied=Platzhalter wird kopiert
@invalidEmailInfo=Korrekte E-Mail-Adresse eingeben
@maxMembersAlreadyAdded=Sie haben bereits das Maximum von 20 Elementen hinzugefügt
@enterEmailAddress=E-Mail-Adresse des Benutzers eingeben
@inCorrectPlaceHolder={0} ist kein erwarteter Platzhalter im E-Mail-Text.
@nsOFF=Keine Benachrichtigungen senden
@nsFAILED=E-Mail-Benachrichtigung nur senden, wenn der Lauf mit einem Fehler abgeschlossen wurde.
@nsCOMPLETED=E-Mail-Benachrichtigung nur senden, wenn der Lauf erfolgreich abgeschlossen wurde.
@nsANY=E-Mail senden, wenn der Lauf abgeschlossen wurde.
@phStatus=Status der Aufgabenkette – ERFOLGREICH|FEHLGESCHLAGEN
@phTaskChainTName=Technischer Name der Aufgabenkette
@phTaskChainBName=Betriebswirtschaftlicher Name der Aufgabenkette
@phLogId=Protokoll-ID des Laufs
@phUser=Benutzer, der die Aufgabenkette ausführt
@phLogUILink=Verknüpfung zur Protokollanzeige der Aufgabenkette
@phStartTime=Startzeit des Laufs
@phEndTime=Endzeit des Laufs
@phErrMsg=Erste Fehlermeldung im Aufgabenprotokoll. Im Falle eines Erfolges ist das Protokoll leer.
@phSpaceName=Technischer Name des Space
@emailFormatError=Ungültiges E-Mail-Format
@emailFormatErrorInListText=Ungültiges E-Mail-Format in Nicht-Tenant-Mitgliederliste eingegeben.
@emailSubjectTemplateText=Benachrichtigung für Aufgabenkette: $$taskChainName$$ – Space: $$spaceId$$ – Status: $$status$$
@emailMessageTemplateText=Hallo!\n\n Ihre Aufgabenkette namens $$taskChainName$$ wurde mit dem Status $$status$$ abgeschlossen. \n Hier sind einige weitere Details zur Aufgabenkette:\n – Technischer Name der Aufgabenkette: $$taskChainName$$ \n – Protokoll-ID des Aufgabenkettenlaufs: $$logId$$ \n – Benutzer, der die Aufgabenkette ausgeführt hat: $$user$$ \n – Verknüpfung zur Protokollanzeige der Aufgabenkette: $$uiLink$$ \n – Startzeit des Aufgabenkettenlaufs: $$startTime$$ \n – Endzeit des Aufgabenkettenlaufs: $$endTime$$ \n – Name des Space: $$spaceId$$ \n
@deleteEmailRecepient=Empfänger löschen
@emailInputDisabledText=Aktivieren Sie die Aufgabenkette, um E-Mail-Empfänger hinzuzufügen.
@tenantOwnerDomainMatchErrorText=Die Domäne der E-Mail-Adresse stimmt nicht mit der Domäne des Tenant-Eigentümers überein: {0}
@totalEmailIdLimitInfoText=Sie können bis zu 20 E-Mail-Empfänger, einschließlich Benutzer, die Tenant-Mitglied sind und andere Empfänger auswählen.
@emailDomainInfoText=Nur E-Mail-Adressen mit der Domäne {0} werden akzeptiert.
@duplicateEmailErrorText=In der Liste gibt es doppelte E-Mail-Empfänger.

#XFLD Zorder Title
@txtZorderTitle=Apache-Spark-Z-Order-Spalten

#XFLD Zorder NoColumn
@txtZorderNoColumn=Keine Z-Order-Spalten gefunden

#XFLD Zorder Column PrimaryKey
@txtZorderColumnPrimaryKey=Primärschlüssel

#XFLD
@lblOperators=Operatoren
addNewSelector=Als neue Aufgabe hinzufügen
parallelSelector=Als parallele Aufgabe hinzufügen
replaceSelector=Vorhandene Aufgabe ersetzen
addparallelbranch=Als parallelen Zweig hinzufügen
addplaceholder=Platzhalter hinzufügen
addALLOperation=Operator "ALL"
addOROperation=Operator "ANY"
addplaceholdertocanvas=Platzhalter zu Grafikbereich hinzufügen
addplaceholderonselected=Platzhalter nach ausgewählter Aufgabe hinzufügen
#XFLD: Add Parallel Branch to Selected Task
addparallelbranchonselected=Parallelen Zweig nach ausgewählter Aufgabe hinzufügen
addOperator=Operator hinzufügen
txtAdd=Hinzufügen
txtPlaceHolderText=Aufgabe per Drag&Drop hier ablegen
@lblLayout=Layout

#XMSG
VAL_UNCONNECTED_TASK=Die Aufgabe ''{0}'' ist nicht mit der Aufgabenkette verbunden.
#XMSG
VAL_TASK_SHOULD_HAVE_ONE_INCOMING_LINK=Die Aufgabe ''{0}'' sollte nur eine eingehende Verknüpfung aufweisen.
#XMSG
VAL_TASK_SHOULD_HAVE_INCOMING_LINK=Die Aufgabe ''{0}'' sollte eine eingehende Verknüpfung aufweisen.
#XMSG
VAL_UNCONNECTED_OPERATOR=Der Operator ''{0}'' ist nicht mit der Aufgabenkette verbunden.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ATLEAST_TWO_INCOMING_LINK=Der Operator ''{0}'' sollte mindestens zwei eingehende Verknüpfungen aufweisen.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ONE_OUT_LINK=Der Operator ''{0}'' sollte mindestens eine ausgehende Verknüpfung aufweisen.
#XMSG
VAL_CIRCULAR_REFERENCE_EXITS=In der Aufgabenkette ''{0}'' ist ein Zirkelbezug vorhanden.
#XMSG
VAL_UNCONNECTED_BRANCH=Das Objekt/der Zweig ''{0}'' ist nicht mit der Aufgabenkette verbunden.
#XMSG
VAL_TASK_TRIGGERED_PARALLEL_MORE_THAN_ONE_TIME=Die Aufgabe ''{0}'' ist mehr als einmal parallel verknüpft. Entfernen Sie die Duplikate, um fortzufahren.


txtBegin=Beginnen
txtNodesInLink=Betroffene Objekte
#XTOL Tooltip for a context button on diagram
openInNewTab=In neuer Registerkarte öffnen
#XTOL Tooltip for a context button on diagram
txtConnectToTaskOperator=Zum Verbinden ziehen
@emailUpdateError=Fehler beim Aktualisieren der E-Mail-Benachrichtigungsliste

#XMSG
noTeamPrivilegeTxt=Sie haben keine Berechtigung, eine Liste der Tenant-Elemente anzuzeigen. Verwenden Sie die Registerkarte "Sonstige", um E-Mail-Empfänger manuell hinzuzufügen.

#XFLD Package
@txtPackage=Paket

#XMSG warning for repository package assignment changed. {1} - package name
VAL_PACKAGE_CHANGED=Sie haben dieses Objekt dem Paket ''{1}'' zugeordnet. Wählen Sie "Sichern", um diese Änderung zu bestätigen und zu validieren. Beachten Sie, dass die Zuordnung zu einem Paket nach dem Sichern in diesem Editor nicht mehr rückgängig gemacht werden kann.
#XMSG warning for repository package dependency issue.
MISSING_DEPENDENCY=Abhängigkeiten von Objekt ''{0}'' können im Kontext von Paket ''{1}'' nicht gelöst werden.

#Warning messages for the folders in the "Others" tab of task chain editor
#XMSG generic warning if getting the objects in a particular folder fails with unhandled errors
getNonRepoArtefactTypeDescendantsError=Beim Abrufen der Objekte im von Ihnen ausgewählten Ordner ist ein Fehler aufgetreten.
#BW Process Chains folder related warning messages
#XMSG warning if user does not have the necessary permission related to the SAP BW Bridge space
missingPrivilegeForBridgeSpaceError=Sie haben nicht die Berechtigung, BW-Prozessketten in einer Aufgabenkette anzuzeigen oder einzufügen.
#XMSG warning if there was a problem retrieving the BW process chains due to bridge space membership, bridge connection issues or other errors
generalBridgeSpaceError=Beim Abrufen der BW-Prozessketten ist ein Fehler aufgetreten.
#XMSG warning if the BW API for retrieving the BW process chains from the SAP BW Bridge tenant fails
bwApiClientError=Beim Abrufen der BW-Prozessketten aus dem SAP-BW-Bridge-Tenant ist ein Fehler aufgetreten.
#XMSG warning if BW process chains list is empty
bwApiEmptyListError=Im SAP-BW-Bridge-Tenant wurden keine BW-Prozessketten gefunden.
#XMSG: Task log message for invalid OIDC configuration in the SAP BW Bridge tenant
bwBridgeOIDCConfigStatusInvalid=OpenID-Authentifizierung ist für diesen Tenant nicht konfiguriert. Verwenden Sie clientID "{0}" und tokenURL "{1}", um die OpenID-Authentifizierung im SAP-BW-Bridge-Tenant zu konfigurieren, wie in SAP-Hinweis 3536298 beschrieben.
#XMSG BW Process Chain Object has been deleted
VAL_DELETED_BW=Die folgenden BW-Prozessketten wurden möglicherweise aus dem SAP-Bridge-Tenant gelöscht: {0}
#BW SQL Script Procedures folder related warning messages
#XFLD waring if the Procedure list is empty
procedureAPIEmptyList=Es wurden keine Prozeduren angelegt oder die Berechtigung EXECUTE (Ausführen) wurde für das Open-SQL-Schema nicht erteilt.
#Change digram orientations
changeOrientations=Ausrichtung ändern


# placeholder for the API Path
apiPath=Zum Beispiel: /job/v1
# placeholder for the status API Path
statusAPIPath=Zum Beispiel: /job/v1/{id}/status
# valueStateText for the API Path
apiPathRequired=API-Pfad ist erforderlich
#placeholder for the CSRF Token URL
csrfTokenURL=Nur HTTPS wird unterstützt
# Response Type 1
statusCode=Ergebnis aus HTTP-Statuscode abrufen
# Response Type 2
locationHeader=Ergebnis aus HTTP-Statuscode und -Standort-Header abrufen
# Response Type 3
responseBody=Ergebnis aus HTTP-Statuscode und -Antworttext abrufen
# placeholder for ID
idPlaceholder=JSON-Pfad eingeben
# placeholder for indicator value
indicatorValue=Wert eingeben
# Placeholder for key
keyPlaceholder=Schlüssel eingeben
# Error message for missing key
KeyRequired=Schlüssel ist erforderlich
# Error message for invalid key format
invalidKeyFormat=Der von Ihnen eingegebene Header-Schlüssel ist nicht zulässig. Gültige Header sind:<ul><li>"prefer"</li><li>Header, die mit "x-" beginnen, außer "x-forwarded-host"</li><li>Header, die alphanumerische Zeichen, "-" oder "_" enthalten.</li></ul>
# Error message for missing value
valueRequired=Wert ist erforderlich
# Error message for invalid characters in value
invalidValueCharacters=Der Header enthält ungültige Zeichen. Erlaubte Sonderzeichen sind:\t ";", ":", "-", "_", ",", "?", "/" und "*"
# Validation message for invoke api path
apiPathValidation=Geben Sie einen gültigen API-Pfad ein, zum Beispiel: /job/v1
# Validation message for JSON path
jsonPathValidation=Geben Sie einen gültigen JSON-Pfad ein.
# Validation message for success/error indicator
indicatorValueValidation=Der Indikatorwert muss mit einem alphanumerischen Zeichen beginnen und darf folgende Sonderzeichen enthalten:\t "-" und "_"
# Error message for JSON path
jsonPathRequired=JSON-Pfad ist erforderlich
# Error message for invalid API Technical Name
invalidTechnicalName=Der technische Name enthält ungültige Zeichen.
# Error message for empty Technical Name
emptyTechnicalName=Technischer Name ist erforderlich
# Tooltip for codeEditor dialog
codeEditorTooltip=JSON-Bearbeitungsfenster öffnen
# Status Api path validation message
validationStatusAPIPath=Geben Sie einen gültigen API-Pfad ein, zum Beispiel: /job/v1/{id}/status
# CSRF token URL validation message
validationCSRFTokenURL=Die URL des CSRF-Tokens muss mit "https://" beginnen und eine gültige URL sein.
# Select a connection
selectConnection=Verbindung auswählen
# Validation message for connection item error
connectionNotReplicated=Ungültige Verbindung zum Ausführen von API-Aufgaben. Öffnen Sie die App "Verbindungen" und geben Sie Ihre Anmeldeinformationen erneut ein, um die HTTP-Verbindung zu reparieren.
# Validation message for API task properties wrong value or missing
VAL_REST_API_PROPERTIES_MISSING=Die API-Aufgabe "{0}" hat falsche oder fehlende Werte für die folgenden Eigenschaften: {1}
# Validation message for API connection api is throwing some error or selected connection has some warning
VAL_REST_API_CONNECTION=Die API-Aufgabe "{0}" hat ein Problem mit der HTTP-Verbindung. Öffnen Sie die App "Verbindungen" und überprüfen Sie die Verbindung.
# Validation message for API task properties when delected connection
VAL_REST_API_CONNECTION_DELETED=API-Aufgabe "{0}" weist eine gelöschte Verbindung vom Typ "{1}" auf.
# Validation message for API task properties when connection cannot be consumed in the API task
VAL_REST_API_CONNECTION_CONSUMPTION_WARNING=API-Aufgabe "{0}" weist eine gelöschte Verbindung vom Typ "{1}" auf, die nicht für die Ausführung von API-Aufgaben verwendet werden kann. Öffnen Sie die App "Verbindungen", und geben Sie erneut Ihre Anmeldeinformationen ein, um eine Verbindung für API-Aufgaben herzustellen.
# Information message if synchrnous mode is selected in the API Invocation then we don't need status information
modeInfoText=Im synchronen Modus wird der Statusbereich für API-Aufrufe nicht angezeigt.
# validation dialog button for Run API Test
saveAnyway=Dennoch sichern
# validation message for technical name
technicalNameValidation=Der technische Name muss innerhalb der Aufgabenkette eindeutig sein. Wählen Sie einen anderen technischen Namen.
# Connection error message
getHttpConnectionsError=HTTP-Verbindungen konnten nicht abgerufen werden
# Msg for the API test run when the Task chain is not saved
taskChainNotSaved=Aufgabenkette muss vor dem Ausführen des API-Testlaufs gesichert werden
# Msg failed to run API test run
@failedToRunAPI=Ausführen des API-Testlaufs fehlgeschlagen
# Msg for the API test run when its already in running state
apiTaskRunning=Ein API-Testlauf wird bereits ausgeführt. Möchten Sie einen neuen Testlauf starten?

topToBtm=Von oben nach unten
leftToRight=Von links nach rechts

#XTIT Apache Spark Application Settings
txtHeaderApacheSettings=Anwendungseinstellungen von Apache Spark
#XFLD Use Default
txtUseSpaceDefault=Standard verwenden
#XFLD Application
txtApplication=Anwendung
#XFLD Define new settings for this Task
txtNewSettings=Neue Einstellungen für diese Aufgabe definieren

#XFLD Use Default
txtUseDefault=Standard verwenden




