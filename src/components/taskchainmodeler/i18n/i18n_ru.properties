#XTOL Undo
@undo=Отменить
#XTOL Redo
@redo=Повторить
#XTOL Delete Selected Symbol
@deleteNode=Удалить выбранный символ
#XTOL Zoom to Fit
@zoomToFit=Масштаб по размеру
#XTOL Auto Layout
@autoLayout=Автоматический формат
#XMSG
@welcomeText=Перетащите объекты с панели слева в эту рабочую область.
#XMSG
@txtNoData=Похоже, вы еще не добавили объекты.
#XMSG
VAL_ENTER_VALID_STRING_GEN=Введите действительную строку, длина которой меньше или равна {0}.
#XMSG
@noParametersMsg=Эта процедура не имеет параметров ввода.
#XMSG
@ip_enterValueMsg="{0}" (выполнение процедуры скриптов SQL) имеет параметры ввода. Можно установить значение для каждого из них.
#XTOL
@validateModel=Сообщения о проверке
#XTOL
@hierarchy=Иерархия
#XTOL
@columnCount=Число столбцов
#XFLD
@yes=Да
#XFLD
@no=Нет
#XTIT Save Dialog param
@modelNameTaskChain=Цепочка задач
#properties panel
@lblPropertyTitle=Свойства
#XFLD
@lblGeneral=Общее
#XFLD : Setting
@lblSetting=Настройки
#XTXT: Delta Enabled Local Table Deletion
txtDeltaTableDeletion=Удалить все полностью обработанные записи с типом изменения "Удалено" старше
#XTXT: Delta Enabled Local Table Deletion Input field description
desDeltaTableDeletion=дн.
#XFLD: Data Activation label
@lblDataActivation=Активация данных
#XFLD: Latency label
@latency=Время ожидания
#XTEXT: Text for Latency dropdown
txtLatencyDefault=По умолчанию
#XTEXT: Text 1 hour
txtOneHour=1 час
#XTEXT: Text 2 hours
txtTwoHours=2 часа
#XTEXT: Text 3 hours
txtThreeHours=3 часа
#XTEXT: Text 4 hours
txtFourHours=4 часа
#XTEXT: Text 6 hours
txtSixHours=6 часов
#XTEXT: Text 12 hours
txtTwelveHours=12 часов
#XTEXT: Text 1 day
txtOneDay=1 день
#XFLD: Latency label
@autoRestartHead=Автоматический перезапуск
#XFLD
@lblConnectionName=Соединение
#XFLD
@lblQualifiedName=Уточненное имя
#XFLD
@lblSpaceName=Имя пространства
#XFLD
@lblLocalSchemaName=Локальная схема
#XFLD
@lblType=Тип объекта
#XFLD
@lblActivity=Операция
#XFLD
@lblTableName=Имя таблицы
#XFLD
@lblBusinessName=Бизнес-имя
#XFLD
@lblTechnicalName=Техническое имя
#XFLD
@lblSpace=Пространство
#XFLD
@lblLabel=Метка
#XFLD
@lblDataType=Тип данных
#XFLD
@lblDescription=Описание
#XFLD
@lblStorageType=Память
#XFLD
@lblHTTPConnection=Общее HTTP-соединение
#XFLD
@lblAPISettings=Общие настройки API
#XFLD
@header=Заголовки
#XFLD
@lblInvoke=Вызов API
#XFLD
@lblMethod=Метод
#XFLD
@lblUrl=Базовый URL
#XFLD
@lblAPIPath=Путь API
#XFLD
@lblMode=Режим
#XFLD
@lblCSRFToken=Запросить маркер CSRF
#XFLD
@lblTokenURL=URL-адрес маркера CSRF
#XFLD
@csrfTokenInfoText=Если не введен, будет использоваться базовый URL и путь API
#XFLD
@lblCSRF=Маркер CSRF
#XFLD
@lblRequestBody=Текст запроса
#XFLD
@lblFormat=Формат
#XFLD
@lblResponse=Ответ
#XFLD
@lblId=Ид. для вызова статуса
#XFLD
@Id=Ид.
#XFLD
@lblSuccessIndicator=Индикатор успеха
#XFLD
@lblErrorIndicator=Индикатор ошибки
#XFLD
@lblErrorReason=Причина ошибки
#XFLD
@lblStatus=Статус
#XFLD
@lblApiTestRun=Тестовый прогон API
#XFLD
@lblRunStatus=Статус прогона
#XFLD
@lblLastRan=Дата последнего выполнения
#XFLD
@lblTestRun=Тестовый прогон
#XFLD
@lblDefaultHeader=Поля заголовка по умолчанию (пары ключ-значение)
#XFLD
@lblAdditionalHeader=Дополнительное поле заголовка
#XFLD
@lblKey=Ключ
#XFLD
@lblEditJSON=Редактировать JSON
#XFLD
@lblTasks=Задачи
#XFLD
@lblRESTfullTask=API
#XFLD: add field button text
btnAddField=Добавить поле заголовка
#XFLD: view Details link
@viewDetails=Просмотреть сведения
#XTOL
tooltipTxt=Больше
#XTOL
delete=Удалить
#XBTN: ok button text
btnOk=ОК
#XBTN: cancel button text
btnCancel=Отменить
#XBTN: save button text
btnSave=Сохранить
#XMSG Error: Object has duplicated technical name.
VAL_OBJECT_DUPLICATE_NAME=Объект ''{0}'' уже есть в репозитарии. Введите другое имя.
#XMSG: loading message while opening task chain
loadTaskChain=Загружаем цепочку задач...
#model properties
#XFLD
@status_panel=Статус прогона
#XFLD
@deploy_status_panel=Статус развертывания
#XFLD
@status_lbl=Статус
#XFLD
@lblLastExecuted=Последний прогон
#XFLD
@lblNotExecuted=Еще не выполнено
#XFLD
@lblNotDeployed=Не развернуто
#XFLD
errorDetailsTxt=Не удалось вызвать статус выполнения
#XBTN: Schedule dropdown menu
SCHEDULE=Планирование
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Редактировать планирование
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Удалить планирование
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Создать планирование
#XLNK
viewDetails=Просмотреть сведения
#XMSG: error message for reading execution status from backend
backendErrorMsg=Вероятно, данные не загружаются с сервера. Повторите вызов данных.
#XFLD: Status text for Completed
@statusCompleted=Выполнено
#XFLD: Status text for Running
@statusRunning=Выполняется
#XFLD: Status text for Failed
@statusFailed=Не выполнено
#XFLD: Status text for Stopped
@statusStopped=Остановлено
#XFLD: Status text for Stopping
@statusStopping=Останавливается
#XFLD
@LoaderTitle=Загружается
#XFLD
#XFLD: Deploy status, dataflow has  been deployed / deployment date exists
@deployStatusActive=Развернуто
@deployStatusRevised=Локальные обновления
@deployStatusFailed=Не выполнено
@deployStatusPending=Развертывание...
@LoaderText=Вызываем сведения с сервера
#XMSG
@msgDetailFetchError=Ошибка при вызове сведений с сервера
#XFLD
@executeError=Ошибка
#XFLD
@executeWarning=Предупреждение
#XMSG
@executeConfirmDialog=Информация
#XMSG
@executeunsavederror=Сохраните цепочку задач перед выполнением.
#XMSG
@executemodifiederror=В цепочке задач есть несохраненные изменения. Сохраните.
#XMSG
@executerunningerror=Цепочка задач сейчас выполняется. Дождитесь завершения текущего прогона перед запуском нового.
#XMSG
@btnExecuteAnyway=Все равно выполнить
#XMSG
@msgExecuteWithValidations=Цепочка задач имеет ошибки проверки. Выполнение цепочки может завершиться неудачей.
#XMSG
@msgRunDeployedVersion=Есть изменения для развертывания. Будет выполнена последняя развернутая версия цепочки задач. Продолжить?
#XMSG
#XMSG
@navToMonitoring=Открыть в мониторе цепочек задач
#XMSG
txtOR=ИЛИ
#XFLD
@preview=Предпросмотр
#XMSG
txtand=И
#NOTR
@dateFormat=yyyy-MM-dd
#NOTR
@dateTimeFormatFilter=yyyy-MM-dd HH:mm:ss.
#NOTR
@timeFormat=HH:mm:ss
@lblColum=Столбец
#XFLD
@lblCondition=Условие
#XFLD
@lblValue=Значение
#XMSG
@msgJsonInvalid=Не удалось сохранить цепочку задач из-за ошибок в JSON. Проверьте и исправьте.
#XTIT
@msgSaveFailTitle=Недействительный JSON.
#XMSG
NOT_CHAINABLE=Объект ''{0}'' невозможно добавить в цепочку задач.
#XMSG
NOT_CHAINABLE_REMOTETABLE=Объект ''{0}'': тип тиражирования будет изменен.
#XMSG
searchTaskChain=Поиск объектов

#XFLD
@txtTaskChain=Цепочка задач
#XFLD
@txtRemoteTable=Дистанционная таблица
#XFLD
@txtRemoveData=Удалить тиражированные данные
#XFLD
@txtRemovePersist=Удалить устойчиво сохраненные данные
#XFLD
@txtView=Ракурс
#XFLD
@txtDataFlow=Поток данных
#XFLD
@txtIL=Интеллектуальный поиск
#XFLD
@txtTransformationFlow=Поток преобразования
#XFLD
@txtReplicationFlow=Поток тиражирования
#XFLD
@txtDeltaLocalTable=Локальная таблица
#XFLD
@txtBWProcessChain=Цепочка процессов BW
#XFLD
@txtSQLScriptProcedure=Процедура скриптов SQL
#XFLD
@txtAPI=API
#XFLD
@txtMerge=Объединить
#XFLD
@txtOptimize=Оптимизировать
#XFLD
@txtVacuum=Удалить записи

#XFLD
@txtRun=Выполнить
#XFLD
@txtPersist=Сохранить
#XFLD
@txtReplicate=Тиражировать
#XFLD
@txtDelete=Удалить записи с типом изменения "Удалено"
#XFLD
@txtRunTC=Выполнить цепочку задач
#XFLD
@txtRunBW=Выполнить цепочку процессов BW
#XFLD
@txtRunSQLScriptProcedure=Выполнить процедуру скриптов SQL

#XFLD
@txtRunDataFlow=Выполнить поток данных
#XFLD
@txtPersistView=Устойчиво сохранить ракурс
#XFLD
@txtReplicateTable=Тиражировать таблицу
#XFLD
@txtRunIL=Выполнить интеллектуальный поиск
#XFLD
@txtRunTF=Выполнить поток преобразования
#XFLD
@txtRunRF=Выполнить поток тиражирования
#XFLD
@txtRemoveReplicatedData=Удалить тиражированные данные
#XFLD
@txtRemovePersistedData=Удалить устойчиво сохраненные данные
#XFLD
@txtMergeData=Объединить
#XFLD
@txtOptimizeData=Оптимизировать
#XFLD
@txtVacuumData=Удалить записи
#XFLD
@txtRunAPI=Запустить API

#XFLD storage type text
hdlfStorage=Файл

@statusNew=Не развернуто
@statusActive=Развернуто
@statusRevised=Локальные обновления
@statusPending=Развертывание...
@statusChangesToDeploy=Изменения для развертывания
@statusDesignTimeError=Ошибка времени дизайна
@statusRunTimeError=Ошибка времени выполнения

#XTIT
txtNodes=Объекты в цепочке задач ({0})
#XBTN
@deleteNodes=Удалить

#XMSG
@txtDropDataToDiagram=Перетащите объекты на диаграмму.
#XMSG
@noData=Нет объектов

#XFLD
@txtTaskPosition=Позиция задачи

#input parameters and variables
#XFLD
lblInputParameters=Параметры ввода
#XFLD
ip_name=Имя
#XFLD
ip_value=Значение
#XTEXT
@noObjectsFound=Объекты не найдены

#XMSG
@msgExecuteSuccess=Прогон цепочки задач запущен.
#XMSG
@msgExecuteFail=Не удалось выполнить цепочку задач.
#XMSG
@msgDeployAndRunSuccess=Развертывание и выполнение цепочки задач запущены.
#XMSG
@msgDeployAndRunFail=Не удалось развернуть и выполнить цепочку задач.
#XMSG
@titleExecuteBusy=Ожидайте.
#XMSG
@msgExecuteBusy=Ваши данные подготавливаются к выполнению цепочки задач.
#XMSG
@msgAPITestRunSuccess=Тестовый прогон API начался.
#XMSG
@msgAPIExecuteBusy=Ваши данные подготавливаются к выполнению тестового прогона API.

#XTOL
txtOpenInEditor=Открыть в редакторе
#XTOL
txtPreviewData=Предпросмотр данных

#datapreview
#XMSG
@msgDataPreviewNotSupp=Предпросмотр данных недоступен для этого объекта.

#XMSG Error: empty model
VAL_MODEL_EMPTY=Ваша модель пустая. Добавьте объекты.
#XMSG Error: deploy model
@msgDeployBeforeRun=Перед выполнением цепочку задач необходимо развернуть.
#BTN: close dialog
btnClose=Закрыть

#XMSG Object is not Deployed
VAL_NOT_DEPLOYED=Необходимо развернуть объект "{0}", чтобы продолжить цепочку задач.
#XMSG Object is having runtime error
VAL_RUNTIME_ERROR=Объект "{0}" имеет ошибку времени выполнения. Проверьте объект.
#XMSG Object is having design time error
VAL_DESIGNTIME_ERROR=Объект "{0}" имеет ошибку времени дизайна. Проверьте объект.
#XMSG Procedure Object has been deleted
VAL_DELETED_PROCEDURE=Удалены следующие процедуры: {0}
#XMSG New Parameter added to Procedure object
VAL_NEW_PARAMETER_ADDED=В процедуру "{1}" добавлены новые параметры: "{0}". Повторно разверните цепочку задач.
#XMSG Parameter removed from Procedure object
VAL_PARAMETER_DELETED=Из процедуры "{1}" удалены параметры: "{0}". Повторно разверните цепочку задач.
#XMSG Parameter data type changed in Procedure object
VAL_PARAMETER_DATATYPE_MODIFIED=Тип данных параметра "{0}" изменен с "{1}" на "{2}" в процедуре "{3}".
#XMSG Parameter length changed of specific datatype in Procedure object
VAL_PARAMETER_LENGTH_MODIFIED=Длина параметра "{0}" изменена с "{1}" на "{2}" в процедуре "{3}".
#XMSG Parameter precision changed of Decimal datatype in Procedure object
VAL_PARAMETER_PRECISION_MODIFIED=Точность параметра "{0}" изменена с "{1}" на "{2}" в процедуре "{3}".
#XMSG Parameter scale changed of Decimal datatype in Procedure object
VAL_PARAMETER_SCALE_MODIFIED=Масштаб параметра "{0}" изменен с "{1}" на "{2}" в процедуре "{3}".
#XMSG Parameter value not provided in Procedure object
VAL_SQL_SCRIPT_PROCEDURE_PARAMETER_VALUE_MISSING=Вы не ввели значений для параметров ввода, необходимых для процедуры "{0}": "{1}"
#XMSG Remote Table activity changes to Remove Replicated Data
VAL_REMOVE_REPLICATED_DATA=Следующий прогон цепочки задач изменит тип доступа к данным, и данные больше не будут загружаться в реальном времени.
#XMSG Remote Table activity changes
REPLICATION_TYPE_CHANGED=Объект "{0}": тип тиражирования будет изменен.

#XFLD
@lblStartNode=Начальный узел
#XFLD
@lblEndNode=Конечный узел
#XFLD
@linkTo=С {0} по {1}
#XTOL
@txtViewDetails=Просмотреть сведения

#XTOL
txtOpenImpactLineage=Анализ зависимостей и происхождения
#XFLD
@emailNotifications=Уведомления по электронной почте
#XFLD
@txtReset=Сбросить
#XFLD
@emailMsgWarning=Шаблон электронного сообщения по умолчанию будет отправляться, когда текст электронного сообщения пустой.
#XFLD
@notificationSettings=Настройки уведомлений
#XFLD
@recipientEmailAddr=Адрес электронной почты получателя
#XFLD
@emailSubject=Тема электронного сообщения
@emailSubjectText=Цепочка задач <TASKCHAIN_NAME> завершена со статусом <STATUS>
#XFLD
@emailMessage=Текст электронного сообщения
@emailMessageText=Уважаемый пользователь!\n\n Уведомляем Вас, что цепочка задач <TASKCHAIN_NAME>, запущенная в <START_TIME>, завершена со статусом <STATUS>. Выполнение завершилось в <END_TIME>.\n\nСведения:\nПространство:<SPACE_NAME>\nОшибка:<ERROR_MESSAGE>
@selectEmailRecipientDialogTitle=Выбрать адрес электронной почты получателя
@tenantMembers=Участники арендатора
@others=Другие ({0})
@selectedEmailAddress=Выбранные получатели
@add=Добавить
@placeholder=Метка-заполнитель
@description=Описание
@copyText=Скопировать текст
@taskchainDetailsPlaceholder=Метки-заполнители для сведений о цепочке задач
@placeholderCopied=Метка-заполнитель скопирована
@invalidEmailInfo=Введите правильный адрес электронной почты
@maxMembersAlreadyAdded=Вы уже добавили максимальное число в 20 участников
@enterEmailAddress=Введите адрес электронной почты пользователя
@inCorrectPlaceHolder={0} не ожидаемая метка-заполнитель в тексте электронного сообщения.
@nsOFF=Не отправлять уведомления
@nsFAILED=Отправить уведомление по электронной почте только в случае завершения прогона с ошибкой
@nsCOMPLETED=Отправить уведомление по электронной почте только в случае успешного завершения прогона
@nsANY=Отправить уведомление по электронной почте по завершении прогона
@phStatus=Статус цепочки задач - SUCCESS (УСПЕШНО)|FAILED (НЕ ВЫПОЛНЕНО)
@phTaskChainTName=Техническое имя цепочки задач
@phTaskChainBName=Бизнес-имя цепочки задач
@phLogId=Ид. журнала прогона
@phUser=Пользователь, выполняющий цепочку задач
@phLogUILink=Ссылка для просмотра журнала цепочки задач
@phStartTime=Время начала прогона
@phEndTime=Время окончания прогона
@phErrMsg=Первое сообщение об ошибке в журнале задач. Журнал пустой в случае статуса SUCCESS (УСПЕШНО)
@phSpaceName=Техническое имя пространства
@emailFormatError=Недействительный формат электронной почты
@emailFormatErrorInListText=Недействительный формат электронной почты введен в списке участников не из арендатора.
@emailSubjectTemplateText=Уведомление для цепочки задач: $$taskChainName$$ - пространство: $$spaceId$$ - статус: $$status$$
@emailMessageTemplateText=Здравствуйте!\n\n Ваша цепочка задач с именем $$taskChainName$$ завершена со статусом $$status$$. \n Дополнительные сведения о цепочке задач:\n - Техническое имя цепочки задач: $$taskChainName$$ \n - Ид. журнала для прогона цепочки задач: $$logId$$ \n - Пользователь, выполнивший цепочку задач: $$user$$ \n - Ссылка на просмотр журнала цепочки задач: $$uiLink$$ \n - Время начала прогона цепочки задач: $$startTime$$ \n - Время окончания прогона цепочки задач: $$endTime$$ \n - Имя пространства: $$spaceId$$ \n
@deleteEmailRecepient=Удалить получателя
@emailInputDisabledText=Разверните цепочку задач, чтобы добавить получателей электронного сообщения.
@tenantOwnerDomainMatchErrorText=Домен адреса электронной почты не совпадает с доменом владельца арендатора: {0}
@totalEmailIdLimitInfoText=Вы можете выбрать до 20 получателей электронного сообщения, включая участников арендатора и других получателей.
@emailDomainInfoText=Принимаются только адреса электронной почты в домене: {0}.
@duplicateEmailErrorText=В списке есть дубликаты получателей электронного сообщения.

#XFLD Zorder Title
@txtZorderTitle=Столбцы Z-последовательности Apache Spark

#XFLD Zorder NoColumn
@txtZorderNoColumn=Столбцы Z-последовательности не найдены

#XFLD Zorder Column PrimaryKey
@txtZorderColumnPrimaryKey=Первичный ключ

#XFLD
@lblOperators=Операторы
addNewSelector=Добавить как новую задачу
parallelSelector=Добавить как параллельную задачу
replaceSelector=Заменить существующую задачу
addparallelbranch=Добавить как параллельную ветвь
addplaceholder=Добавить метку-заполнитель
addALLOperation=Оператор ВСЕ
addOROperation=Оператор ЛЮБОЕ
addplaceholdertocanvas=Добавить метку-заполнитель в рабочую область
addplaceholderonselected=Добавить метку-заполнитель после выбранной задачи
#XFLD: Add Parallel Branch to Selected Task
addparallelbranchonselected=Добавить параллельную ветвь после выбранной задачи
addOperator=Добавить оператор
txtAdd=Добавить
txtPlaceHolderText=Перетащите задачу сюда
@lblLayout=Формат

#XMSG
VAL_UNCONNECTED_TASK=Задача "{0}" не соединена с цепочкой задач.
#XMSG
VAL_TASK_SHOULD_HAVE_ONE_INCOMING_LINK=Задача "{0}" должна иметь только одну входящую ссылку.
#XMSG
VAL_TASK_SHOULD_HAVE_INCOMING_LINK=Задача "{0}" должна иметь одну входящую ссылку.
#XMSG
VAL_UNCONNECTED_OPERATOR=Оператор "{0}" не соединен с цепочкой задач.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ATLEAST_TWO_INCOMING_LINK=Оператор "{0}" должен иметь минимум две входящие ссылки.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ONE_OUT_LINK=Оператор "{0}" должен иметь минимум одну исходящую ссылку.
#XMSG
VAL_CIRCULAR_REFERENCE_EXITS=Замкнутый цикл в цепочке задач "{0}".
#XMSG
VAL_UNCONNECTED_BRANCH=Объект/ветвь "{0}" не соединены с цепочкой задач.
#XMSG
VAL_TASK_TRIGGERED_PARALLEL_MORE_THAN_ONE_TIME=Задача "{0}" соединена параллельно несколько раз. Удалите дубликаты для продолжения.


txtBegin=Начало
txtNodesInLink=Затронутые объекты
#XTOL Tooltip for a context button on diagram
openInNewTab=Открыть на новой вкладке
#XTOL Tooltip for a context button on diagram
txtConnectToTaskOperator=Перетащите для соединения
@emailUpdateError=Ошибка при обновлении списка уведомлений по электронной почте

#XMSG
noTeamPrivilegeTxt=Нет полномочий на просмотр списка участников арендатора. Используйте вкладку "Прочее", чтобы добавить получателей электронной почты вручную.

#XFLD Package
@txtPackage=Пакет

#XMSG warning for repository package assignment changed. {1} - package name
VAL_PACKAGE_CHANGED=Вы присвоили этот объект пакету ''{1}''. Нажмите "Сохранить" для подтверждения и проверки этого изменения. Присвоение пакету невозможно отменить в этом редакторе после сохранения.
#XMSG warning for repository package dependency issue.
MISSING_DEPENDENCY=Зависимости объекта ''{0}'' невозможно развернуть в контексте пакета ''{1}''.

#Warning messages for the folders in the "Others" tab of task chain editor
#XMSG generic warning if getting the objects in a particular folder fails with unhandled errors
getNonRepoArtefactTypeDescendantsError=Возникла проблема при вызове объектов из выбранной папки.
#BW Process Chains folder related warning messages
#XMSG warning if user does not have the necessary permission related to the SAP BW Bridge space
missingPrivilegeForBridgeSpaceError=У вас нет разрешения на просмотр или включение цепочек процессов BW в цепочку задач.
#XMSG warning if there was a problem retrieving the BW process chains due to bridge space membership, bridge connection issues or other errors
generalBridgeSpaceError=Возникла проблема при вызове цепочек процессов BW.
#XMSG warning if the BW API for retrieving the BW process chains from the SAP BW Bridge tenant fails
bwApiClientError=Возникла проблема при вызове цепочек процессов BW из арендатора моста SAP BW.
#XMSG warning if BW process chains list is empty
bwApiEmptyListError=Цепочки процессов BW не найдены в арендаторе моста SAP BW.
#XMSG: Task log message for invalid OIDC configuration in the SAP BW Bridge tenant
bwBridgeOIDCConfigStatusInvalid=Аутентификация OpenID не выполнена для этого арендатора. Воспользуйтесь clientID "{0}" и tokenURL "{1}", чтобы настроить аутентификацию OpenID в арендаторе моста SAP BW в соответствии с описанием в SAP-ноте 3536298.
#XMSG BW Process Chain Object has been deleted
VAL_DELETED_BW=Следующие цепочки процессов BW, возможно, были удалены из арендатора моста SAP: {0}
#BW SQL Script Procedures folder related warning messages
#XFLD waring if the Procedure list is empty
procedureAPIEmptyList=Процедуры не созданы, или привилегия ВЫПОЛНЕНИЕ не предоставлена схеме Open SQL.
#Change digram orientations
changeOrientations=Изменить ориентации


# placeholder for the API Path
apiPath=Например: /job/v1
# placeholder for the status API Path
statusAPIPath=Например: /job/v1/{id}/status
# valueStateText for the API Path
apiPathRequired=Укажите путь API
#placeholder for the CSRF Token URL
csrfTokenURL=Поддерживается только HTTPS 
# Response Type 1
statusCode=Вызвать результат из кода статуса HTTP
# Response Type 2
locationHeader=Вызвать результат из кода статуса HTTP и заголовка местоположения
# Response Type 3
responseBody=Вызвать результат из кода статуса HTTP и текста ответа
# placeholder for ID
idPlaceholder=Введите путь JSON
# placeholder for indicator value
indicatorValue=Введите значение
# Placeholder for key
keyPlaceholder=Введите ключ
# Error message for missing key
KeyRequired=Требуется ключ
# Error message for invalid key format
invalidKeyFormat=Введенный вами ключ заголовка не разрешен. Действительные заголовки:<ul><li>"prefer"</li><li>Заголовки, начинающиеся с "x-", кроме "x-forwarded-host"</li><li>Заголовки, содержащие буквенно-цифровые символы, "-" или "_"</li></ul>
# Error message for missing value
valueRequired=Требуется значение
# Error message for invalid characters in value
invalidValueCharacters=Заголовок содержит недопустимые символы. Разрешеные специальные символы:\t ";", ":", "-", "_", ",", "?", "/" и "*"
# Validation message for invoke api path
apiPathValidation=Введите действительный путь API, например: /job/v1
# Validation message for JSON path
jsonPathValidation=Введите действительный путь JSON
# Validation message for success/error indicator
indicatorValueValidation=Значение индикатора должно начинаться с буквенно-цифрового символа и может содержать следующие специальные символы:\t "-" и "_"
# Error message for JSON path
jsonPathRequired=Требуется путь JSON
# Error message for invalid API Technical Name
invalidTechnicalName=Техническое имя содержит недопустимые символы
# Error message for empty Technical Name
emptyTechnicalName=Требуется техническое имя
# Tooltip for codeEditor dialog
codeEditorTooltip=Открыть окно редактирования JSON
# Status Api path validation message
validationStatusAPIPath=Введите действительный путь API, например: /job/v1/{id}/status
# CSRF token URL validation message
validationCSRFTokenURL=URL маркера CSRF должен начинаться с 'https://' и быть действительным URL
# Select a connection
selectConnection=Выберите соединение
# Validation message for connection item error
connectionNotReplicated=Соединение сейчас недействительно для запуска задач API. Откройте приложение "Соединения" и повторно введите ваши учетные данные, чтобы исправить HTTP-соединение
# Validation message for API task properties wrong value or missing
VAL_REST_API_PROPERTIES_MISSING=Задача API "{0}" имеет неправильные значения или не имеет значений для следующих свойств: {1}
# Validation message for API connection api is throwing some error or selected connection has some warning
VAL_REST_API_CONNECTION=Задача API "{0}" имеет проблему с HTTP-соединением. Откройте приложение "Соединения" и проверьте соединение
# Validation message for API task properties when delected connection
VAL_REST_API_CONNECTION_DELETED=Задача API "{0}" имеет удаленное соединение "{1}"
# Validation message for API task properties when connection cannot be consumed in the API task
VAL_REST_API_CONNECTION_CONSUMPTION_WARNING=Задача API "{0}" имеет соединение "{1}", которое нельзя использовать для выполнения задач API. Откройте приложение "Соединения" и повторно введите учетные данные, чтобы установить соединения для задач API.
# Information message if synchrnous mode is selected in the API Invocation then we don't need status information
modeInfoText=В синхронном режиме статус панели не отображается для вызовов API
# validation dialog button for Run API Test
saveAnyway=Сохранить в любом случае
# validation message for technical name
technicalNameValidation=Техническое имя должно быть уникальным в рамках цепочки задач. Выберите другое техническое имя
# Connection error message
getHttpConnectionsError=Не удалось вызвать HTTP-соединения
# Msg for the API test run when the Task chain is not saved
taskChainNotSaved=Цепочку задач необходимо сохранить перед выполнением тестового прогона API
# Msg failed to run API test run
@failedToRunAPI=Не удалось выполнить тестовый прогон API
# Msg for the API test run when its already in running state
apiTaskRunning=Тестовый прогон API уже выполняется. Запустить новый тестовый прогон?

topToBtm=Сверху вниз
leftToRight=Слева направо

#XTIT Apache Spark Application Settings
txtHeaderApacheSettings=Настройки приложения Apache Spark
#XFLD Use Default
txtUseSpaceDefault=Использовать значение по умолчанию
#XFLD Application
txtApplication=Приложение
#XFLD Define new settings for this Task
txtNewSettings=Определить новые настройки для этой задачи

#XFLD Use Default
txtUseDefault=Использовать значение по умолчанию




