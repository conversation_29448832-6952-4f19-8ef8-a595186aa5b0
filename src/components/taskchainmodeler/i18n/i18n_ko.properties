#XTOL Undo
@undo=실행 취소
#XTOL Redo
@redo=재실행
#XTOL Delete Selected Symbol
@deleteNode=선택된 기호 삭제
#XTOL Zoom to Fit
@zoomToFit=크기에 맞게
#XTOL Auto Layout
@autoLayout=자동 레이아웃
#XMSG
@welcomeText=왼쪽 패널에서 이 캔버스로 오브젝트를 끌어 놓으십시오.
#XMSG
@txtNoData=아직 오브젝트를 추가하지 않은 것 같습니다.
#XMSG
VAL_ENTER_VALID_STRING_GEN=길이가 {0} 이하인 유효한 문자열을 입력하십시오.
#XMSG
@noParametersMsg=이 절차에 입력 매개변수가 없습니다.
#XMSG
@ip_enterValueMsg="{0}"(SQL 스크립트 실행 절차)에 입력 매개변수가 있습니다. 각각의 값을 설정할 수 있습니다.
#XTOL
@validateModel=유효성 확인 메시지
#XTOL
@hierarchy=계층구조
#XTOL
@columnCount=열 수
#XFLD
@yes=예
#XFLD
@no=아니오
#XTIT Save Dialog param
@modelNameTaskChain=태스크 체인
#properties panel
@lblPropertyTitle=속성
#XFLD
@lblGeneral=일반
#XFLD : Setting
@lblSetting=설정
#XTXT: Delta Enabled Local Table Deletion
txtDeltaTableDeletion=변경 유형이 ‘삭제됨’이고 다음 일수보다 오래된, 완전히 처리된 레코드 모두 삭제
#XTXT: Delta Enabled Local Table Deletion Input field description
desDeltaTableDeletion=일
#XFLD: Data Activation label
@lblDataActivation=데이터 활성화
#XFLD: Latency label
@latency=대기 시간
#XTEXT: Text for Latency dropdown
txtLatencyDefault=기본값
#XTEXT: Text 1 hour
txtOneHour=1시간
#XTEXT: Text 2 hours
txtTwoHours=2시간
#XTEXT: Text 3 hours
txtThreeHours=3시간
#XTEXT: Text 4 hours
txtFourHours=4시간
#XTEXT: Text 6 hours
txtSixHours=6시간
#XTEXT: Text 12 hours
txtTwelveHours=12시간
#XTEXT: Text 1 day
txtOneDay=1일
#XFLD: Latency label
@autoRestartHead=자동 재시작
#XFLD
@lblConnectionName=연결
#XFLD
@lblQualifiedName=정규화된 이름
#XFLD
@lblSpaceName=공간 이름
#XFLD
@lblLocalSchemaName=로컬 스키마
#XFLD
@lblType=오브젝트 유형
#XFLD
@lblActivity=액티비티
#XFLD
@lblTableName=테이블 이름
#XFLD
@lblBusinessName=업무 이름
#XFLD
@lblTechnicalName=기술적 이름
#XFLD
@lblSpace=공간
#XFLD
@lblLabel=레이블
#XFLD
@lblDataType=데이터 유형
#XFLD
@lblDescription=내역
#XFLD
@lblStorageType=저장소
#XFLD
@lblHTTPConnection=일반 HTTP 연결
#XFLD
@lblAPISettings=일반 API 설정
#XFLD
@header=헤더
#XFLD
@lblInvoke=API 호출
#XFLD
@lblMethod=메소드
#XFLD
@lblUrl=기본 URL
#XFLD
@lblAPIPath=API 경로
#XFLD
@lblMode=모드
#XFLD
@lblCSRFToken=CSRF 토큰 필수
#XFLD
@lblTokenURL=CSRF 토큰 URL
#XFLD
@csrfTokenInfoText=입력하지 않으면 기본 URL과 API 경로가 사용됨
#XFLD
@lblCSRF=CSRF 토큰
#XFLD
@lblRequestBody=요청 본문
#XFLD
@lblFormat=형식
#XFLD
@lblResponse=응답
#XFLD
@lblId=상태를 가져올 ID
#XFLD
@Id=ID
#XFLD
@lblSuccessIndicator=성공 지시자
#XFLD
@lblErrorIndicator=오류 지시자
#XFLD
@lblErrorReason=오류 이유
#XFLD
@lblStatus=상태
#XFLD
@lblApiTestRun=API 테스트 실행
#XFLD
@lblRunStatus=실행 상태
#XFLD
@lblLastRan=최종 실행 시간
#XFLD
@lblTestRun=테스트 실행
#XFLD
@lblDefaultHeader=기본 헤더 필드(키-값 쌍)
#XFLD
@lblAdditionalHeader=추가 헤더 필드
#XFLD
@lblKey=키
#XFLD
@lblEditJSON=JSON 편집
#XFLD
@lblTasks=태스크
#XFLD
@lblRESTfullTask=API
#XFLD: add field button text
btnAddField=헤더 필드 추가
#XFLD: view Details link
@viewDetails=세부사항 보기
#XTOL
tooltipTxt=기타
#XTOL
delete=삭제
#XBTN: ok button text
btnOk=확인
#XBTN: cancel button text
btnCancel=취소
#XBTN: save button text
btnSave=저장
#XMSG Error: Object has duplicated technical name.
VAL_OBJECT_DUPLICATE_NAME=오브젝트 "{0}"이(가) 저장소에 이미 있습니다. 다른 이름을 입력하십시오.
#XMSG: loading message while opening task chain
loadTaskChain=태스크 체인을 로드하는 중...
#model properties
#XFLD
@status_panel=실행 상태
#XFLD
@deploy_status_panel=배포 상태
#XFLD
@status_lbl=상태
#XFLD
@lblLastExecuted=최종 실행
#XFLD
@lblNotExecuted=아직 실행 안 함
#XFLD
@lblNotDeployed=배포되지 않음
#XFLD
errorDetailsTxt=상태를 가져올 수 없습니다.
#XBTN: Schedule dropdown menu
SCHEDULE=일정
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=일정 편집
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=일정 삭제
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=일정 생성
#XLNK
viewDetails=세부사항 보기
#XMSG: error message for reading execution status from backend
backendErrorMsg=지금은 서버에서 데이터가 로드되지 않는 것 같습니다. 나중에 데이터 가져오기를 다시 시도하십시오.
#XFLD: Status text for Completed
@statusCompleted=완료됨
#XFLD: Status text for Running
@statusRunning=실행 중
#XFLD: Status text for Failed
@statusFailed=실패
#XFLD: Status text for Stopped
@statusStopped=중지됨
#XFLD: Status text for Stopping
@statusStopping=중지 중
#XFLD
@LoaderTitle=로드하는 중
#XFLD
#XFLD: Deploy status, dataflow has  been deployed / deployment date exists
@deployStatusActive=배포됨
@deployStatusRevised=로컬 업데이트
@deployStatusFailed=실패
@deployStatusPending=배포 중...
@LoaderText=서버에서 세부사항을 가져오는 중
#XMSG
@msgDetailFetchError=서버에서 세부사항을 가져오는 동안 오류가 발생했습니다.
#XFLD
@executeError=오류
#XFLD
@executeWarning=경고
#XMSG
@executeConfirmDialog=정보
#XMSG
@executeunsavederror=실행하기 전에 태스크 체인을 저장하십시오.
#XMSG
@executemodifiederror=태스크 체인에 저장되지 않은 변경사항이 있습니다. 태스크 체인을 저장하십시오.
#XMSG
@executerunningerror=태스크 체인이 현재 실행 중입니다. 새로운 실행을 시작하기 전에 현재 실행이 완료될 때까지 기다리십시오.
#XMSG
@btnExecuteAnyway=실행
#XMSG
@msgExecuteWithValidations=태스크 체인에 유효성 확인 오류가 있습니다. 태스크 체인을 실행하면 오류가 발생할 수 있습니다.
#XMSG
@msgRunDeployedVersion=배포할 변경사항이 있습니다. 마지막으로 배포된 버전의 태스크 체인이 실행됩니다. 계속하시겠습니까?
#XMSG
#XMSG
@navToMonitoring=태스크 체인 모니터에서 열기
#XMSG
txtOR=또는
#XFLD
@preview=미리보기
#XMSG
txtand=및
#NOTR
@dateFormat=yyyy-MM-dd
#NOTR
@dateTimeFormatFilter=yyyy-MM-dd HH:mm:ss.
#NOTR
@timeFormat=HH:mm:ss
@lblColum=열
#XFLD
@lblCondition=조건
#XFLD
@lblValue=값
#XMSG
@msgJsonInvalid=JSON에 오류가 있어서 태스크 체인을 저장할 수 없습니다. 확인 후 문제를 해결하십시오.
#XTIT
@msgSaveFailTitle=JSON이 잘못되었습니다.
#XMSG
NOT_CHAINABLE=오브젝트 "{0}"을(를) 태스크 체인에 추가할 수 없습니다.
#XMSG
NOT_CHAINABLE_REMOTETABLE=오브젝트 "{0}": 복제 유형이 변경됩니다.
#XMSG
searchTaskChain=오브젝트 검색

#XFLD
@txtTaskChain=태스크 체인
#XFLD
@txtRemoteTable=원격 테이블
#XFLD
@txtRemoveData=복제된 데이터 제거
#XFLD
@txtRemovePersist=지속 데이터 제거
#XFLD
@txtView=조회
#XFLD
@txtDataFlow=데이터 흐름
#XFLD
@txtIL=인텔리전트 조회
#XFLD
@txtTransformationFlow=변환 흐름
#XFLD
@txtReplicationFlow=복제 흐름
#XFLD
@txtDeltaLocalTable=로컬 테이블
#XFLD
@txtBWProcessChain=BW 프로세스 체인
#XFLD
@txtSQLScriptProcedure=SQL 스크립트 절차
#XFLD
@txtAPI=API
#XFLD
@txtMerge=병합
#XFLD
@txtOptimize=최적화
#XFLD
@txtVacuum=레코드 삭제

#XFLD
@txtRun=실행
#XFLD
@txtPersist=지속
#XFLD
@txtReplicate=복제
#XFLD
@txtDelete=‘삭제됨’ 변경 유형의 레코드 삭제
#XFLD
@txtRunTC=태스크 체인 실행
#XFLD
@txtRunBW=BW 프로세스 체인 실행
#XFLD
@txtRunSQLScriptProcedure=SQL 스크립트 절차 실행

#XFLD
@txtRunDataFlow=데이터 흐름 실행
#XFLD
@txtPersistView=뷰 지속
#XFLD
@txtReplicateTable=테이블 복제
#XFLD
@txtRunIL=인텔리전트 조회 실행
#XFLD
@txtRunTF=변환 흐름 실행
#XFLD
@txtRunRF=복제 흐름 실행
#XFLD
@txtRemoveReplicatedData=복제된 데이터 제거
#XFLD
@txtRemovePersistedData=지속 데이터 제거
#XFLD
@txtMergeData=병합
#XFLD
@txtOptimizeData=최적화
#XFLD
@txtVacuumData=레코드 삭제
#XFLD
@txtRunAPI=API 실행

#XFLD storage type text
hdlfStorage=파일

@statusNew=배포되지 않음
@statusActive=배포됨
@statusRevised=로컬 업데이트
@statusPending=배포 중...
@statusChangesToDeploy=변경사항 배포 대상
@statusDesignTimeError=디자인 타임 오류
@statusRunTimeError=런타임 오류

#XTIT
txtNodes=태스크 체인의 오브젝트({0})
#XBTN
@deleteNodes=삭제

#XMSG
@txtDropDataToDiagram=오브젝트를 다이어그램에 끌어서 놓습니다.
#XMSG
@noData=오브젝트가 없습니다.

#XFLD
@txtTaskPosition=태스크 위치

#input parameters and variables
#XFLD
lblInputParameters=입력 매개변수
#XFLD
ip_name=이름
#XFLD
ip_value=값
#XTEXT
@noObjectsFound=오브젝트 없음

#XMSG
@msgExecuteSuccess=태스크 체인 실행이 시작되었습니다.
#XMSG
@msgExecuteFail=태스크 체인을 실행하지 못했습니다.
#XMSG
@msgDeployAndRunSuccess=태스크 체인 배포 및 실행이 시작되었습니다.
#XMSG
@msgDeployAndRunFail=태스크 체인을 배포하여 실행하지 못했습니다.
#XMSG
@titleExecuteBusy=잠시 기다려 주십시오.
#XMSG
@msgExecuteBusy=태스크 체인 실행을 시작하기 위해 데이터를 준비하는 중입니다.
#XMSG
@msgAPITestRunSuccess=API 테스트 실행이 시작되었습니다.
#XMSG
@msgAPIExecuteBusy=API 테스트 실행을 시작하기 위해 데이터를 준비하는 중입니다.

#XTOL
txtOpenInEditor=편집기에서 열기
#XTOL
txtPreviewData=데이터 미리보기

#datapreview
#XMSG
@msgDataPreviewNotSupp=이 오브젝트에는 데이터 미리보기를 사용할 수 없습니다.

#XMSG Error: empty model
VAL_MODEL_EMPTY=모델이 비어 있는 것 같습니다. 오브젝트를 추가하십시오.
#XMSG Error: deploy model
@msgDeployBeforeRun=실행하기 전에 태스크 체인을 배포해야 합니다.
#BTN: close dialog
btnClose=닫기

#XMSG Object is not Deployed
VAL_NOT_DEPLOYED=태스크 체인을 계속 진행하려면 오브젝트 "{0}"이(가) 배포되어야 합니다.
#XMSG Object is having runtime error
VAL_RUNTIME_ERROR=오브젝트 "{0}"이(가) 런타임 오류를 리턴했습니다. 오브젝트를 점검하십시오.
#XMSG Object is having design time error
VAL_DESIGNTIME_ERROR=오브젝트 "{0}"이(가) 디자인 타임 오류를 리턴했습니다. 오브젝트를 점검하십시오.
#XMSG Procedure Object has been deleted
VAL_DELETED_PROCEDURE={0} 절차가 삭제되었습니다.
#XMSG New Parameter added to Procedure object
VAL_NEW_PARAMETER_ADDED=절차 "{1}"에 신규 매개변수가 추가됨: "{0}". 태스크 체인을 다시 배포하십시오.
#XMSG Parameter removed from Procedure object
VAL_PARAMETER_DELETED=절차 "{1}"에서 매개변수가 제거됨: "{0}". 태스크 체인을 다시 배포하십시오.
#XMSG Parameter data type changed in Procedure object
VAL_PARAMETER_DATATYPE_MODIFIED=절차 "{3}"에서 매개변수 "{0}" 데이터 유형이 "{1}"에서 "{2}"(으)로 변경되었습니다.
#XMSG Parameter length changed of specific datatype in Procedure object
VAL_PARAMETER_LENGTH_MODIFIED=절차 "{3}"에서 매개변수 "{0}" 길이가 "{1}"에서 "{2}"(으)로 변경되었습니다.
#XMSG Parameter precision changed of Decimal datatype in Procedure object
VAL_PARAMETER_PRECISION_MODIFIED=절차 "{3}"에서 매개변수 "{0}" 정밀도가 "{1}"에서 "{2}"(으)로 변경되었습니다.
#XMSG Parameter scale changed of Decimal datatype in Procedure object
VAL_PARAMETER_SCALE_MODIFIED=절차 "{3}"에서 매개변수 "{0}" 스케일이 "{1}"에서 "{2}"(으)로 변경되었습니다.
#XMSG Parameter value not provided in Procedure object
VAL_SQL_SCRIPT_PROCEDURE_PARAMETER_VALUE_MISSING=절차 "{0}"에 필요한 입력 매개변수 값을 입력하지 않음: "{1}"
#XMSG Remote Table activity changes to Remove Replicated Data
VAL_REMOVE_REPLICATED_DATA=태스크 체인의 다음 실행은 데이터 액세스 유형을 변경하고 데이터는 더 이상 실시간으로 업로드되지 않습니다.
#XMSG Remote Table activity changes
REPLICATION_TYPE_CHANGED=오브젝트 "{0}": 복제 유형이 변경됩니다.

#XFLD
@lblStartNode=시작 노드
#XFLD
@lblEndNode=종료 노드
#XFLD
@linkTo={0}-{1}
#XTOL
@txtViewDetails=세부사항 보기

#XTOL
txtOpenImpactLineage=영향 및 계보 분석
#XFLD
@emailNotifications=전자메일 알림
#XFLD
@txtReset=재설정
#XFLD
@emailMsgWarning=전자메일 메시지가 비어 있으면 기본 템플릿 전자메일이 발송됩니다.
#XFLD
@notificationSettings=알림 설정
#XFLD
@recipientEmailAddr=수신자 전자메일 주소
#XFLD
@emailSubject=전자메일 제목
@emailSubjectText=태스크 체인 <TASKCHAIN_NAME>이(가) <STATUS> 상태로 완료되었습니다.
#XFLD
@emailMessage=전자메일 메시지
@emailMessageText=사용자님 안녕하세요,\n\n 태스크 체인:<TASKCHAIN_NAME>이(가) <START_TIME>에 시작하여 <STATUS> 상태로 종료되었습니다. 실행이 <END_TIME>에 종료되었습니다.\n\n세부사항:\n범위:<SPACE_NAME>\n오류:<ERROR_MESSAGE>
@selectEmailRecipientDialogTitle=수신자 전자메일 주소 선택
@tenantMembers=테넌트 멤버
@others=기타({0})
@selectedEmailAddress=선택한 수신자
@add=추가
@placeholder=자리 표시자
@description=내역
@copyText=텍스트 복사
@taskchainDetailsPlaceholder=태스크 체인 세부사항의 자리 표시자
@placeholderCopied=자리 표시자가 복사되었습니다.
@invalidEmailInfo=올바른 전자메일 주소를 입력하십시오.
@maxMembersAlreadyAdded=이미 최대 20명의 멤버를 추가했습니다.
@enterEmailAddress=사용자의 전자메일 주소를 입력하십시오.
@inCorrectPlaceHolder={0}은(는) 전자메일 본문에 예상되는 자리 표시자가 아닙니다.
@nsOFF=알림을 보내지 않음
@nsFAILED=실행이 완료되었지만 오류가 발생한 경우에만 전자메일 알림 보내기
@nsCOMPLETED=실행이 성공적으로 완료된 경우에만 전자메일 알림 보내기
@nsANY=실행이 완료되면 전자메일 알림 보내기
@phStatus=태스크 체인 상태 - 성공|실패
@phTaskChainTName=태스크 체인의 기술적 이름
@phTaskChainBName=태스크 체인의 업무 이름
@phLogId=실행의 로그 ID
@phUser=태스크 체인을 실행 중인 사용자
@phLogUILink=태스크 체인의 로그 표시에 대한 링크
@phStartTime=실행 시작 시간
@phEndTime=실행 종료 시간
@phErrMsg=태스크 로그의 첫 번째 오류 메시지입니다. SUCCESS의 경우 이 로그가 비어 있습니다.
@phSpaceName=공간의 기술적 이름
@emailFormatError=잘못된 전자메일 형식
@emailFormatErrorInListText=잘못된 전자메일 형식이 비테넌트 멤버 리스트에 입력되었습니다.
@emailSubjectTemplateText=태스크 체인 통지: $$taskChainName$$ - 공간: $$spaceId$$ - 상태: $$status$$
@emailMessageTemplateText=안녕하십니까,\n\n $$taskChainName$$(으)로 레이블 지정된 태스크 체인이 상태 $$status$$(으)로 완료되었습니다. \n 태스크 체인에 대한 기타 세부사항:\n - 태스크 체인 기술적 이름: $$taskChainName$$ \n - 태스크 체인 실행의 로그 ID: $$logId$$ \n - 태스크 체인을 실행한 사용자: $$user$$ \n - 태스크 체인의 로그 조회 링크: $$uiLink$$ \n - 태스크 체인 실행 시작 시간: $$startTime$$ \n - 태스크 체인 실행 종료 시간: $$endTime$$ \n - 공간 이름: $$spaceId$$ \n
@deleteEmailRecepient=수신자 삭제
@emailInputDisabledText=전자메일 수신자를 추가하려면 태스크 체인을 배포하십시오.
@tenantOwnerDomainMatchErrorText=전자메일 주소 도메인이 테넌트 소유자 도메인과 일치하지 않습니다. {0}
@totalEmailIdLimitInfoText=테넌트 멤버 사용자와 기타 수신자를 비롯하여 최대 20개의 전자메일 수신자를 선택할 수 있습니다.
@emailDomainInfoText=도메인이 {0}인 전자메일 주소만 승인됩니다.
@duplicateEmailErrorText=리스트에 중복 전자메일 수신자가 있습니다.

#XFLD Zorder Title
@txtZorderTitle=Apache Spark Z-Order 열

#XFLD Zorder NoColumn
@txtZorderNoColumn=Z-Order 열 없음

#XFLD Zorder Column PrimaryKey
@txtZorderColumnPrimaryKey=기본 키

#XFLD
@lblOperators=연산자
addNewSelector=신규 태스크로 추가
parallelSelector=병렬 태스크로 추가
replaceSelector=기존 태스크 바꾸기
addparallelbranch=병렬 브랜치로 추가
addplaceholder=자리 표시자 추가
addALLOperation=ALL 연산자
addOROperation=ANY 연산자
addplaceholdertocanvas=캔버스에 자리 표시자 추가
addplaceholderonselected=선택한 태스크 다음에 자리 표시자 추가
#XFLD: Add Parallel Branch to Selected Task
addparallelbranchonselected=선택한 태스크 다음에 병렬 브랜치 추가
addOperator=연산자 추가
txtAdd=추가
txtPlaceHolderText=여기에 태스크 끌어서 놓기
@lblLayout=레이아웃

#XMSG
VAL_UNCONNECTED_TASK="{0}" 태스크가 태스크 체인에 연결되지 않았습니다.
#XMSG
VAL_TASK_SHOULD_HAVE_ONE_INCOMING_LINK="{0}" 태스크에는 하나의 수신 링크만 있어야 합니다.
#XMSG
VAL_TASK_SHOULD_HAVE_INCOMING_LINK="{0}" 태스크에 하나의 수신 링크가 있어야 합니다.
#XMSG
VAL_UNCONNECTED_OPERATOR="{0}" 연산자가 태스크 체인에 연결되지 않았습니다.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ATLEAST_TWO_INCOMING_LINK="{0}" 연산자에는 수신 링크가 두 개 이상 있어야 합니다.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ONE_OUT_LINK="{0}" 연산자에는 발신 링크가 한 개 이상 있어야 합니다.
#XMSG
VAL_CIRCULAR_REFERENCE_EXITS="{0}" 태스크 체인에 순환 루프가 있습니다.
#XMSG
VAL_UNCONNECTED_BRANCH="{0}" 오브젝트/브랜치가 태스크 체인에 연결되지 않았습니다.
#XMSG
VAL_TASK_TRIGGERED_PARALLEL_MORE_THAN_ONE_TIME="{0}" 태스크가 두 번 이상 병렬로 연결되었습니다. 계속하려면 중복을 제거하십시오.


txtBegin=시작
txtNodesInLink=관련 오브젝트
#XTOL Tooltip for a context button on diagram
openInNewTab=새 탭에서 열기
#XTOL Tooltip for a context button on diagram
txtConnectToTaskOperator=끌어서 연결
@emailUpdateError=전자메일 통지 리스트 업데이트 중 오류 발생

#XMSG
noTeamPrivilegeTxt=테넌트 멤버 리스트를 볼 수 있는 권한이 없습니다. 기타 탭을 사용하여 전자메일 수신자를 직접 추가하십시오.

#XFLD Package
@txtPackage=패키지

#XMSG warning for repository package assignment changed. {1} - package name
VAL_PACKAGE_CHANGED=이 오브젝트를 패키지 "{1}"에 지정했습니다. 저장을 클릭하여 이 변경사항을 확인하고 유효성을 검사합니다. 저장 후에는 이 편집기에서 패키지에 대한 지정을 취소할 수 없습니다.
#XMSG warning for repository package dependency issue.
MISSING_DEPENDENCY=오브젝트 "{0}"의 종속성을 "{1}" 패키지의 컨텍스트에서 결정할 수 없습니다.

#Warning messages for the folders in the "Others" tab of task chain editor
#XMSG generic warning if getting the objects in a particular folder fails with unhandled errors
getNonRepoArtefactTypeDescendantsError=선택한 폴더에서 오브젝트를 가져오는 동안 문제가 발생했습니다.
#BW Process Chains folder related warning messages
#XMSG warning if user does not have the necessary permission related to the SAP BW Bridge space
missingPrivilegeForBridgeSpaceError=태스크 체인에서 BW 프로세스 체인을 조회하거나 포함하는 데 필요한 권한이 없습니다.
#XMSG warning if there was a problem retrieving the BW process chains due to bridge space membership, bridge connection issues or other errors
generalBridgeSpaceError=BW 프로세스 체인을 가져오는 동안 문제가 발생했습니다.
#XMSG warning if the BW API for retrieving the BW process chains from the SAP BW Bridge tenant fails
bwApiClientError=SAP BW 브리지 테넌트에서 BW 프로세스 체인을 가져오는 동안 문제가 발생했습니다.
#XMSG warning if BW process chains list is empty
bwApiEmptyListError=SAP BW 브리지 테넌트에 BW 프로세스 체인이 없습니다.
#XMSG: Task log message for invalid OIDC configuration in the SAP BW Bridge tenant
bwBridgeOIDCConfigStatusInvalid=이 테넌트에서 OpenID 인증이 구성되지 않았습니다. SAP Note 3536298의 설명에 따라 SAP BW 브리지 테넌트에서 clientID "{0}", tokenURL "{1}"을(를) 사용하여 OpenID 인증을 구성하십시오.
#XMSG BW Process Chain Object has been deleted
VAL_DELETED_BW=SAP 브리지 테넌트에서 BW 프로세스 체인 {0}이(가) 삭제되었을 수 있습니다.
#BW SQL Script Procedures folder related warning messages
#XFLD waring if the Procedure list is empty
procedureAPIEmptyList=생성된 절차가 없거나 EXECUTE 관리 권한이 Open SQL 스키마에 부여되지 않았습니다.
#Change digram orientations
changeOrientations=방향 변경


# placeholder for the API Path
apiPath=예: /job/v1
# placeholder for the status API Path
statusAPIPath=예: /job/v1/{id}/status
# valueStateText for the API Path
apiPathRequired=API 경로는 필수입니다.
#placeholder for the CSRF Token URL
csrfTokenURL=HTTPS만 지원됩니다.
# Response Type 1
statusCode=HTTP 상태 코드에서 결과 가져오기
# Response Type 2
locationHeader=HTTP 상태 코드 및 위치 헤더에서 결과 가져오기
# Response Type 3
responseBody=HTTP 상태 코드 및 DMDEKQ 에서 결과 가져오기
# placeholder for ID
idPlaceholder=JSON 경로 편집
# placeholder for indicator value
indicatorValue=값 입력
# Placeholder for key
keyPlaceholder=키 입력
# Error message for missing key
KeyRequired=키는 필수입니다.
# Error message for invalid key format
invalidKeyFormat=입력한 헤더 키는 허용되지 않습니다. 유효한 헤더:<ul><li>"prefer"</li><li>"x-"로 시작하는 헤더("x-forwarded-host" 제외)</li><li>영숫자, "-" 또는 "_"를 포함한 헤더</li></ul>
# Error message for missing value
valueRequired=값은 필수입니다.
# Error message for invalid characters in value
invalidValueCharacters=헤더에 유효하지 않은 문자가 포함되어 있습니다. 허용되는 특수 문자는 \t ";", ":", "-", "_", ",", "?", "/", "*"입니다.
# Validation message for invoke api path
apiPathValidation=유효한 API 경로(예: /job/v1)를 입력하십시오.
# Validation message for JSON path
jsonPathValidation=유효한 JSON 경로를 입력하십시오.
# Validation message for success/error indicator
indicatorValueValidation=지시자 값은 영숫자로 시작해야 하며 특수 문자 \t "-", "_"를 포함할 수 있습니다.
# Error message for JSON path
jsonPathRequired=JSON 경로는 필수입니다.
# Error message for invalid API Technical Name
invalidTechnicalName=기술적 이름에 파일 이름에 유효하지 않은 문자가 있습니다.
# Error message for empty Technical Name
emptyTechnicalName=기술적 이름은 필수입니다.
# Tooltip for codeEditor dialog
codeEditorTooltip=JSON 편집 윈도우 열기
# Status Api path validation message
validationStatusAPIPath=유효한 API 경로(예: /job/v1/{id}/status)를 입력하십시오.
# CSRF token URL validation message
validationCSRFTokenURL=CSRF 토큰 URL은 'https://'로 시작해야 하며 유효한 URL이어야 합니다.
# Select a connection
selectConnection=연결 선택
# Validation message for connection item error
connectionNotReplicated=현재 연결을 사용하여 API 태스크를 실행할 수 없습니다. "연결" 앱을 열고 자격 증명을 다시 입력하여 HTTP 연결을 수정하십시오.
# Validation message for API task properties wrong value or missing
VAL_REST_API_PROPERTIES_MISSING=API 태스크 "{0}"의 속성 {1} 값이 잘못되거나 누락되었습니다.
# Validation message for API connection api is throwing some error or selected connection has some warning
VAL_REST_API_CONNECTION=API 태스크 "{0}"의 HTTP 연결에 문제가 있습니다. "연결" 앱을 열고 연결을 확인하십시오.
# Validation message for API task properties when delected connection
VAL_REST_API_CONNECTION_DELETED=API 태스크 "{0}"에서 "{1}" 연결을 삭제했습니다.
# Validation message for API task properties when connection cannot be consumed in the API task
VAL_REST_API_CONNECTION_CONSUMPTION_WARNING=API 태스크 "{0}"에 API 태스크 실행에 사용될 수 없는 "{1}" 연결이 있습니다. "연결" 앱을 열고 자격 증명을 다시 입력하여 API 태스크를 위한 연결을 설정하십시오.
# Information message if synchrnous mode is selected in the API Invocation then we don't need status information
modeInfoText=동기 모드에서는 API 호출에 대해 상태 패널이 표시되지 않습니다.
# validation dialog button for Run API Test
saveAnyway=무시하고 저장
# validation message for technical name
technicalNameValidation=기술적 이름은 태스크 체인 내에서 고유해야 합니다. 다른 기술적 이름을 선택하십시오.
# Connection error message
getHttpConnectionsError=HTTP 연결을 가져올 수 없음
# Msg for the API test run when the Task chain is not saved
taskChainNotSaved=API 테스트 실행을 수행하려면 태스크 체인이 저장되어야 합니다.
# Msg failed to run API test run
@failedToRunAPI=API 테스트 실행을 수행하지 못했습니다.
# Msg for the API test run when its already in running state
apiTaskRunning=API 테스트 실행이 이미 진행 중입니다. 신규 테스트 실행을 시작하시겠습니까?

topToBtm=위에서 아래로
leftToRight=왼쪽에서 오른쪽으로

#XTIT Apache Spark Application Settings
txtHeaderApacheSettings=Apache Spark 어플리케이션 설정
#XFLD Use Default
txtUseSpaceDefault=기본값 사용
#XFLD Application
txtApplication=어플리케이션
#XFLD Define new settings for this Task
txtNewSettings=이 태스크에 대한 신규 설정 정의

#XFLD Use Default
txtUseDefault=기본값 사용




