#XTOL Undo
@undo=Poništi
#XTOL Redo
@redo=Ponovi
#XTOL Delete Selected Symbol
@deleteNode=Izbriši odabrani simbol
#XTOL Zoom to Fit
@zoomToFit=Uvećaj do uklapanja
#XTOL Auto Layout
@autoLayout=Automatski izgled
#XMSG
@welcomeText=Prevucite i ispustite objekte s levog panela na ovo platno.
#XMSG
@txtNoData=Izgleda da još niste dodali objekat.
#XMSG
VAL_ENTER_VALID_STRING_GEN=Unesite važeći niz dužine {0} ili kraći.
#XMSG
@noParametersMsg=Postupak nema parametre unosa.
#XMSG
@ip_enterValueMsg="{0}" (Pokreni postupak SQL Script-a) ima parametre unosa. Možete postaviti vrednost za svaki od njih.
#XTOL
@validateModel=Poruke validacije
#XTOL
@hierarchy=Hijerarhija
#XTOL
@columnCount=<PERSON><PERSON><PERSON> kolona
#XFLD
@yes=Da
#XFLD
@no=Ne
#XTIT Save Dialog param
@modelNameTaskChain=Lanac zadataka
#properties panel
@lblPropertyTitle=Svojstva
#XFLD
@lblGeneral=Opšte
#XFLD : Setting
@lblSetting=Podešavanja
#XTXT: Delta Enabled Local Table Deletion
txtDeltaTableDeletion=Izbrišite sve potpuno obrađene zapise s tipom promene "Izbrisano" starije od
#XTXT: Delta Enabled Local Table Deletion Input field description
desDeltaTableDeletion=Dani
#XFLD: Data Activation label
@lblDataActivation=Aktiviranje podataka
#XFLD: Latency label
@latency=Latentnost
#XTEXT: Text for Latency dropdown
txtLatencyDefault=Standard
#XTEXT: Text 1 hour
txtOneHour=1 sat
#XTEXT: Text 2 hours
txtTwoHours=2 sata
#XTEXT: Text 3 hours
txtThreeHours=3 sata
#XTEXT: Text 4 hours
txtFourHours=4 sata
#XTEXT: Text 6 hours
txtSixHours=6 sati
#XTEXT: Text 12 hours
txtTwelveHours=12 sati
#XTEXT: Text 1 day
txtOneDay=1 dan
#XFLD: Latency label
@autoRestartHead=Automatsko ponovno pokretanje
#XFLD
@lblConnectionName=Veza
#XFLD
@lblQualifiedName=Kvalifikovani naziv
#XFLD
@lblSpaceName=Naziv prostora
#XFLD
@lblLocalSchemaName=Lokalna šema
#XFLD
@lblType=Tip objekta
#XFLD
@lblActivity=Aktivnost
#XFLD
@lblTableName=Naziv tabele
#XFLD
@lblBusinessName=Poslovni naziv
#XFLD
@lblTechnicalName=Tehnički naziv
#XFLD
@lblSpace=Prostor
#XFLD
@lblLabel=Oznaka
#XFLD
@lblDataType=Tip podataka
#XFLD
@lblDescription=Opis
#XFLD
@lblStorageType=Skladište
#XFLD
@lblHTTPConnection=Generička HTTP veza
#XFLD
@lblAPISettings=Generička podešavanja API-ja
#XFLD
@header=Zaglavlja
#XFLD
@lblInvoke=Pozivanje API-ja
#XFLD
@lblMethod=Metod
#XFLD
@lblUrl=Osnovni URL
#XFLD
@lblAPIPath=Put API-ja
#XFLD
@lblMode=Način
#XFLD
@lblCSRFToken=Zahtevaj token CSRF 
#XFLD
@lblTokenURL=URL CSRF tokena
#XFLD
@csrfTokenInfoText=Ako nije unet, koristiće se osnovni URL i put API-ja
#XFLD
@lblCSRF=Token CSRF 
#XFLD
@lblRequestBody=Zahtevaj glavni deo
#XFLD
@lblFormat=Formatiraj
#XFLD
@lblResponse=Odgovor
#XFLD
@lblId=ID ѕa pozivanje statusa
#XFLD
@Id=ID
#XFLD
@lblSuccessIndicator=Pokazatelj uspeha
#XFLD
@lblErrorIndicator=Pokazatelj greške
#XFLD
@lblErrorReason=Razlog za grešku
#XFLD
@lblStatus=Status
#XFLD
@lblApiTestRun=Probno izvođenje API-ja
#XFLD
@lblRunStatus=Status izvođenja
#XFLD
@lblLastRan=Poslednji put izvedeno
#XFLD
@lblTestRun=Probno izvođenje
#XFLD
@lblDefaultHeader=Standardna polja zaglavlja (parovi ključ-vrednost)
#XFLD
@lblAdditionalHeader=Dodatno polje zaglavlja
#XFLD
@lblKey=Ključ
#XFLD
@lblEditJSON=Uredi JSON
#XFLD
@lblTasks=Zadaci
#XFLD
@lblRESTfullTask=API
#XFLD: add field button text
btnAddField=Dodaj polje zaglavlja
#XFLD: view Details link
@viewDetails=Prikaži detalje
#XTOL
tooltipTxt=Više
#XTOL
delete=Izbriši
#XBTN: ok button text
btnOk=OK
#XBTN: cancel button text
btnCancel=Odustani
#XBTN: save button text
btnSave=Sačuvaj
#XMSG Error: Object has duplicated technical name.
VAL_OBJECT_DUPLICATE_NAME=Objekat "{0}" već postoji u repozitorijumu. Unesite drugi naziv.
#XMSG: loading message while opening task chain
loadTaskChain=Učitavanje lanca zadataka...
#model properties
#XFLD
@status_panel=Status izvođenja
#XFLD
@deploy_status_panel=Status implementacije
#XFLD
@status_lbl=Status
#XFLD
@lblLastExecuted=Poslednje izvođenje
#XFLD
@lblNotExecuted=Još nije izvedeno
#XFLD
@lblNotDeployed=Nije implementirano
#XFLD
errorDetailsTxt=Nije moguće pozvati status izvođenja
#XBTN: Schedule dropdown menu
SCHEDULE=Planiraj
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Uredi plan
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Izbriši plan
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Kreiraj plan
#XLNK
viewDetails=Prikaži detalje
#XMSG: error message for reading execution status from backend
backendErrorMsg=Izgleda da se podaci trenutno ne učitavaju sa servera. Pokušajte ponovo da pozovete podatke.
#XFLD: Status text for Completed
@statusCompleted=Završeno
#XFLD: Status text for Running
@statusRunning=Izvodi se
#XFLD: Status text for Failed
@statusFailed=Nije uspelo
#XFLD: Status text for Stopped
@statusStopped=Zaustavljeno
#XFLD: Status text for Stopping
@statusStopping=Zaustavlja se
#XFLD
@LoaderTitle=Učitava se
#XFLD
#XFLD: Deploy status, dataflow has  been deployed / deployment date exists
@deployStatusActive=Implementirano
@deployStatusRevised=Lokalna ažuriranja
@deployStatusFailed=Nije uspelo
@deployStatusPending=Implementacija...
@LoaderText=Pozivanje detalja sa servera
#XMSG
@msgDetailFetchError=Greška pri pozivanju detalja sa servera
#XFLD
@executeError=Greška
#XFLD
@executeWarning=Upozorenje
#XMSG
@executeConfirmDialog=Informacije
#XMSG
@executeunsavederror=Sačuvajte lanac zadataka pre izvođenja.
#XMSG
@executemodifiederror=Postoje nesačuvane promene u lancu zadataka. Sačuvajte ih.
#XMSG
@executerunningerror=Lanac zadataka se trenutno izvodi. Sačekajte da se trenutno izvođenje završi pre nego što pokrenete novo.
#XMSG
@btnExecuteAnyway=Ipak izvedi
#XMSG
@msgExecuteWithValidations=Lanac zadataka ima greške validacije. Izvođenje lanca zadataka može dovesti do greške.
#XMSG
@msgRunDeployedVersion=Postoje promene za implementaciju. Biće pokrenuta poslednja implementirana verzija lanca zadataka. Da li želite da nastavite?
#XMSG
#XMSG
@navToMonitoring=Otvori u monitoru lanca zadataka
#XMSG
txtOR=ILI
#XFLD
@preview=Prethodno prikaži
#XMSG
txtand=i
#NOTR
@dateFormat=yyyy-MM-dd
#NOTR
@dateTimeFormatFilter=yyyy-MM-dd HH:mm:ss.
#NOTR
@timeFormat=HH:mm:ss
@lblColum=Kolona
#XFLD
@lblCondition=Uslov
#XFLD
@lblValue=Vrednost
#XMSG
@msgJsonInvalid=Lanac zadataka se ne može sačuvati jer postoje greške u JSON-u. Proverite i razrešite.
#XTIT
@msgSaveFailTitle=Nevažeći JSON.
#XMSG
NOT_CHAINABLE=Objekat "{0}" se ne može dodati u lanac zadataka.
#XMSG
NOT_CHAINABLE_REMOTETABLE=Objekat "{0}": tip replikacije biće promenjen.
#XMSG
searchTaskChain=Traži objekte

#XFLD
@txtTaskChain=Lanac zadataka
#XFLD
@txtRemoteTable=Udaljena tabela
#XFLD
@txtRemoveData=Ukloni replicirane podatke
#XFLD
@txtRemovePersist=Ukloni trajno sačuvane podatke
#XFLD
@txtView=Prikaži
#XFLD
@txtDataFlow=Tok podataka
#XFLD
@txtIL=Pametno traženje
#XFLD
@txtTransformationFlow=Tok transformacije
#XFLD
@txtReplicationFlow=Tok replikacije
#XFLD
@txtDeltaLocalTable=Lokalna tabela
#XFLD
@txtBWProcessChain=Lanac procesa BW
#XFLD
@txtSQLScriptProcedure=Postupak SQL Script-a
#XFLD
@txtAPI=API
#XFLD
@txtMerge=Spoj
#XFLD
@txtOptimize=Optimiziraj
#XFLD
@txtVacuum=Izbriši zapise

#XFLD
@txtRun=Izvedi
#XFLD
@txtPersist=Trajno sačuvaj
#XFLD
@txtReplicate=Repliciraj
#XFLD
@txtDelete=Izbriši zapise s tipom promene "Izbrisano"
#XFLD
@txtRunTC=Izvedite lanac zadataka
#XFLD
@txtRunBW=Pokreni lanac procesa BW
#XFLD
@txtRunSQLScriptProcedure=Pokreni postupak SQL Script-a

#XFLD
@txtRunDataFlow=Izvedi tok podataka
#XFLD
@txtPersistView=Trajno sačuvaj pogled
#XFLD
@txtReplicateTable=Repliciraj tabelu
#XFLD
@txtRunIL=Pokreni pametno traženje
#XFLD
@txtRunTF=Pokreni tok transformacije
#XFLD
@txtRunRF=Pokreni tok replikacije
#XFLD
@txtRemoveReplicatedData=Ukloni replicirane podatke
#XFLD
@txtRemovePersistedData=Ukloni trajno sačuvane podatke
#XFLD
@txtMergeData=Spoj
#XFLD
@txtOptimizeData=Optimiziraj
#XFLD
@txtVacuumData=Izbriši zapise
#XFLD
@txtRunAPI=Izvedi API

#XFLD storage type text
hdlfStorage=Fajl

@statusNew=Nije implementirano
@statusActive=Implementirano
@statusRevised=Lokalna ažuriranja
@statusPending=Implementacija...
@statusChangesToDeploy=Promene za implementaciju
@statusDesignTimeError=Greška vremena dizajna
@statusRunTimeError=Greška vremena izvođenja

#XTIT
txtNodes=Objekti u lancu zadataka ({0})
#XBTN
@deleteNodes=Izbriši

#XMSG
@txtDropDataToDiagram=Prevucite i ispustite objekte u dijagram.
#XMSG
@noData=Nema objekata

#XFLD
@txtTaskPosition=Pozicija zadatka

#input parameters and variables
#XFLD
lblInputParameters=Parametri unosa
#XFLD
ip_name=Naziv
#XFLD
ip_value=Vrednost
#XTEXT
@noObjectsFound=Objekti nisu nađeni

#XMSG
@msgExecuteSuccess=Izvođenje lanca zadataka je pokrenuto.
#XMSG
@msgExecuteFail=Nije uspelo izvođenje lanca zadataka.
#XMSG
@msgDeployAndRunSuccess=Implementacija i izvođenje lanca zadataka su pokrenuti.
#XMSG
@msgDeployAndRunFail=Nije uspelo implementiranje i izvođenje lanca zadataka.
#XMSG
@titleExecuteBusy=Sačekajte.
#XMSG
@msgExecuteBusy=Pripremamo vaše podatke za pokretanje izvođenja lanca zadataka.
#XMSG
@msgAPITestRunSuccess=Probno izvođenje API-ja je pokrenuto.
#XMSG
@msgAPIExecuteBusy=Pripremamo vaše podatke za pokretanje probnog izvođenja API-ja.

#XTOL
txtOpenInEditor=Otvori u uređivaču
#XTOL
txtPreviewData=Prethodno prikaži podatke

#datapreview
#XMSG
@msgDataPreviewNotSupp=Prethodni prikaz podataka nije dostupan za ovaj objekat.

#XMSG Error: empty model
VAL_MODEL_EMPTY=Izgleda da je vaš model prazan. Dodajte objekte.
#XMSG Error: deploy model
@msgDeployBeforeRun=Morate implementirati lanac zadataka pre nego što ga izvedete.
#BTN: close dialog
btnClose=Zatvori

#XMSG Object is not Deployed
VAL_NOT_DEPLOYED=Objekat "{0}" se mora implementirati da bi se nastavio lanac zadataka.
#XMSG Object is having runtime error
VAL_RUNTIME_ERROR=Objekat "{0}" vraća grešku vremena izvođenja. Proverite objekat.
#XMSG Object is having design time error
VAL_DESIGNTIME_ERROR=Objekat "{0}" vraća grešku vremena dizajna. Proverite objekat.
#XMSG Procedure Object has been deleted
VAL_DELETED_PROCEDURE=Sledeći postupci su izbrisani: {0}
#XMSG New Parameter added to Procedure object
VAL_NEW_PARAMETER_ADDED=Novi parametri dodati postupku "{1}": "{0}". Ponovo implementirajte lanac zadataka.
#XMSG Parameter removed from Procedure object
VAL_PARAMETER_DELETED=Parametri uklonjeni iz postupka "{1}": "{0}". Ponovo implementirajte lanac zadataka.
#XMSG Parameter data type changed in Procedure object
VAL_PARAMETER_DATATYPE_MODIFIED=Tip podataka parametra "{0}" promenjen iz "{1}" u "{2}" u postupku "{3}".
#XMSG Parameter length changed of specific datatype in Procedure object
VAL_PARAMETER_LENGTH_MODIFIED=Dužina parametra "{0}" promenjena iz "{1}" u "{2}" u postupku "{3}".
#XMSG Parameter precision changed of Decimal datatype in Procedure object
VAL_PARAMETER_PRECISION_MODIFIED=Preciznost parametra "{0}" promenjena iz "{1}" u "{2}" u postupku "{3}".
#XMSG Parameter scale changed of Decimal datatype in Procedure object
VAL_PARAMETER_SCALE_MODIFIED=Skala parametra "{0}" promenjena iz "{1}" u "{2}" u postupku "{3}".
#XMSG Parameter value not provided in Procedure object
VAL_SQL_SCRIPT_PROCEDURE_PARAMETER_VALUE_MISSING=Niste uneli vrednosti za parametre unosa koji su obavezni za postupak "{0}": "{1}"
#XMSG Remote Table activity changes to Remove Replicated Data
VAL_REMOVE_REPLICATED_DATA=Sledeće izvođenje lanca zadataka će promeniti tip pristupa podacima i podaci se više neće prenositi na server u realnom vremenu.
#XMSG Remote Table activity changes
REPLICATION_TYPE_CHANGED=Objekat "{0}": Tip replikacije biće promenjen.

#XFLD
@lblStartNode=Početni čvor
#XFLD
@lblEndNode=Završni čvor
#XFLD
@linkTo={0} do {1}
#XTOL
@txtViewDetails=Prikaži detalje

#XTOL
txtOpenImpactLineage=Analiza učinka i porekla
#XFLD
@emailNotifications=Obaveštenja e-poštom
#XFLD
@txtReset=Ponovo postavi
#XFLD
@emailMsgWarning=Standardni šablon e-pošte biće poslat kada poruka e-pošte bude prazna
#XFLD
@notificationSettings=Podešavanja obaveštenja
#XFLD
@recipientEmailAddr=Adresa primaoca e-pošte
#XFLD
@emailSubject=Predmet e-pošte
@emailSubjectText=Lanac zadataka <TASKCHAIN_NAME> završen sa statusom <STATUS>
#XFLD
@emailMessage=Poruka e-pošte
@emailMessageText=Poštovani korisniče,\n\n Ovim vas obaveštavamo da je lanac zadataka:<TASKCHAIN_NAME> izveden u <START_TIME> završen sa statusom <STATUS>. Izvršenje je završeno u <END_TIME>.\n\nDetalji:\nProstor:<SPACE_NAME>\nGreška:<ERROR_MESSAGE>
@selectEmailRecipientDialogTitle=Odaberi adresu primaoca e-pošte
@tenantMembers=Elementi klijenta
@others=Drugi ({0})
@selectedEmailAddress=Odabrani primaoci
@add=Dodaj
@placeholder=Rezervisano mesto
@description=Opis
@copyText=Kopiraj tekst
@taskchainDetailsPlaceholder=Rezervisana mesta za detalje lanca zadataka
@placeholderCopied=Rezervisano mesto je kopirano
@invalidEmailInfo=Unesite tačnu adresu e-pošte
@maxMembersAlreadyAdded=Već ste dodali maksimalnih 20 elemenata
@enterEmailAddress=Unesite adresu e-pošte
@inCorrectPlaceHolder={0} nije očekivano rezervisano mesto u glavnom delu e-pošte.
@nsOFF=Nemoj slati obaveštenja
@nsFAILED=Pošalji obaveštenje e-poštom samo kada se izvođenje završi s greškom
@nsCOMPLETED=Pošalji obaveštenje e-poštom samo kada se izvođenje uspešno završi
@nsANY=Pošalji e-poštu kada se izvođenje završi
@phStatus=Status lanca zadataka - USPEŠNO|NEUSPEŠNO
@phTaskChainTName=Tehnički naziv lanca zadataka
@phTaskChainBName=Poslovni naziv lanca zadataka
@phLogId=ID protokola izvođenja
@phUser=Korisnik koji izvodi lanac zadataka
@phLogUILink=Veza s prikazom protokola lanca zadataka
@phStartTime=Vreme početka izvođenja
@phEndTime=Vreme završetka izvođenja
@phErrMsg=Prva poruka o grešci u protokolu zadatka. Protokol je prazan u slučaju USPEHA
@phSpaceName=Tehnički naziv prostora
@emailFormatError=Nevažeći format e-pošte
@emailFormatErrorInListText=Nevažeći format e-pošte unet u listu elemenata koji nisu klijenti.
@emailSubjectTemplateText=Obaveštenje za lanac zadataka: $$taskChainName$$ - prostor: $$spaceId$$ - status: $$status$$
@emailMessageTemplateText=Poštovani,\n\n Vaš lanac zadataka označen $$taskChainName$$ završen je sa statusom $$status$$. \n Ovo su još neki detalji o lancu zadataka:\n - tehnički naziv lanca zadataka: $$taskChainName$$ \n - ID protokola izvođenja lanca zadataka: $$logId$$ \n - korisnik koji je izveo lanac zadataka: $$user$$ \n - veza za prikaz protokola lanca zadataka: $$uiLink$$ \n - vreme početka izvođenja lanca zadataka: $$startTime$$ \n - vreme završetka izvođenja lanca zadataka: $$endTime$$ \n - naziv prostora: $$spaceId$$ \n
@deleteEmailRecepient=Izbriši primaoca
@emailInputDisabledText=Implementirajte lanac zadataka za dodavanje primalaca e-pošte.
@tenantOwnerDomainMatchErrorText=Domen adrese e-pošte ne odgovara domenu odgovornog lica klijenta: {0}
@totalEmailIdLimitInfoText=Možete odabrati do 20 primalaca e-pošte uključujući korisnike elementa klijenta i druge primaoce.
@emailDomainInfoText=Prihvaćene su samo adrese e-pošte s domenom: {0}
@duplicateEmailErrorText=Na listi postoje dupli primaoci e-pošte.

#XFLD Zorder Title
@txtZorderTitle=Apache Spark - kolone Z-redosleda

#XFLD Zorder NoColumn
@txtZorderNoColumn=Nisu nađene kolone Z-redosleda

#XFLD Zorder Column PrimaryKey
@txtZorderColumnPrimaryKey=Primarni ključ

#XFLD
@lblOperators=Operatori
addNewSelector=Dodaj kao novi zadatak
parallelSelector=Dodaj kao paralelni zadatak
replaceSelector=Zameni postojeći zadatak
addparallelbranch=Dodaj kao paralelnu granu
addplaceholder=Dodaj rezervisano mesto
addALLOperation=Operator ALL
addOROperation=Operator ANY
addplaceholdertocanvas=Dodaj rezervisano mesto na platno
addplaceholderonselected=Dodaj rezervisano mesto nakon odabranog zadatka
#XFLD: Add Parallel Branch to Selected Task
addparallelbranchonselected=Dodaj paralelnu granu nakon odabranog zadatka
addOperator=Dodaj operator
txtAdd=Dodaj
txtPlaceHolderText=Ovde prevucite i ispustite zadatak
@lblLayout=Izgled

#XMSG
VAL_UNCONNECTED_TASK=Zadatak "{0}" nije povezan s lancem zadataka.
#XMSG
VAL_TASK_SHOULD_HAVE_ONE_INCOMING_LINK=Zadatak "{0}" treba da ima samo jednu ulaznu vezu.
#XMSG
VAL_TASK_SHOULD_HAVE_INCOMING_LINK=Zadatak "{0}" treba da ima jednu ulaznu vezu.
#XMSG
VAL_UNCONNECTED_OPERATOR=Operator "{0}" nije povezan s lancem zadataka.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ATLEAST_TWO_INCOMING_LINK=Operator ''{0}'' treba da ima najmanje dve ulazne veze.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ONE_OUT_LINK=Operator "{0}" treba da ima najmanje jednu izlaznu vezu.
#XMSG
VAL_CIRCULAR_REFERENCE_EXITS=Kružna petlja postoji u lancu zadataka "{0}".
#XMSG
VAL_UNCONNECTED_BRANCH=Objekat/grana "{0}" nisu povezani s lancem zadataka.
#XMSG
VAL_TASK_TRIGGERED_PARALLEL_MORE_THAN_ONE_TIME=Zadatak "{0}" paralelno povezan više puta. Uklonite duplikate da biste nastavili.


txtBegin=Počni
txtNodesInLink=Uključeni objekti
#XTOL Tooltip for a context button on diagram
openInNewTab=Otvori u novoj kartici
#XTOL Tooltip for a context button on diagram
txtConnectToTaskOperator=Prevuci za povezivanje
@emailUpdateError=Greška u ažuriranju liste obaveštenja e-poštom

#XMSG
noTeamPrivilegeTxt=Nemate dozvolu da prikažete listu elementa klijenta. Koristite karticu Drugo da ručno dodate primaoce e-pošte.

#XFLD Package
@txtPackage=Paket

#XMSG warning for repository package assignment changed. {1} - package name
VAL_PACKAGE_CHANGED=Dodelili ste ovaj objekat paketu "{1}". Kliknite na Sačuvaj da biste potvrdili i vrednovali ovu promenu. Uzmite u obzir da dodelu paketu nije moguće poništiti u ovom uređivaču nakon snimanja.
#XMSG warning for repository package dependency issue.
MISSING_DEPENDENCY=Zavisnosti objekta "{0}" ne mogu se rešiti u kontekstu paketa "{1}".

#Warning messages for the folders in the "Others" tab of task chain editor
#XMSG generic warning if getting the objects in a particular folder fails with unhandled errors
getNonRepoArtefactTypeDescendantsError=Problem pri pozivanju objekata u folderu koji ste odabrali.
#BW Process Chains folder related warning messages
#XMSG warning if user does not have the necessary permission related to the SAP BW Bridge space
missingPrivilegeForBridgeSpaceError=Nemate neophodnu dozvolu za prikaz ili uključivanje procesa BW u lanac zadataka.
#XMSG warning if there was a problem retrieving the BW process chains due to bridge space membership, bridge connection issues or other errors
generalBridgeSpaceError=Problem pri pozivanju lanaca procesa BW.
#XMSG warning if the BW API for retrieving the BW process chains from the SAP BW Bridge tenant fails
bwApiClientError=Problem pri pozivanju lanaca procesa iz klijenta premošćavanja aplikacije SAP BW.
#XMSG warning if BW process chains list is empty
bwApiEmptyListError=Lanci procesa BW nisu nađeni u klijentu premošćavanja aplikacije SAP BW.
#XMSG: Task log message for invalid OIDC configuration in the SAP BW Bridge tenant
bwBridgeOIDCConfigStatusInvalid=Potvrda identiteta OpenID nije konfigurisana za ovaj klijent. Koristite clientID "{0}" i tokenURL "{1}" za konfiguraciju potvrde identiteta OpenID u klijentu Premošćavanja aplikacije SAP BW kao što je opisano u SAP belešci 3536298.
#XMSG BW Process Chain Object has been deleted
VAL_DELETED_BW=Sledeći lanci procesa BW su možda izbrisani iz klijenta SAP Bridge: {0}
#BW SQL Script Procedures folder related warning messages
#XFLD waring if the Procedure list is empty
procedureAPIEmptyList=Postupci nisu kreirani ili privilegija IZVRŠI nije dodeljena Open SQL šemi.
#Change digram orientations
changeOrientations=Promeni orijentacije


# placeholder for the API Path
apiPath=Na primer: /job/v1
# placeholder for the status API Path
statusAPIPath=Na primer: /job/v1/{id}/status
# valueStateText for the API Path
apiPathRequired=Put API-ja je obavezan
#placeholder for the CSRF Token URL
csrfTokenURL=Podržani su samo HTTPS
# Response Type 1
statusCode=Pozovi rezultat iz šifre statusa HTTP 
# Response Type 2
locationHeader=Pozovi rezultat iz šifre statusa HTTP i zaglavlja lokacije 
# Response Type 3
responseBody=Pozovi rezultat iz šifre statusa HTTP i glavnog dela odgovora
# placeholder for ID
idPlaceholder=Unesi put JSON 
# placeholder for indicator value
indicatorValue=Unesi vrednost
# Placeholder for key
keyPlaceholder=Unesi ključ
# Error message for missing key
KeyRequired=Ključ je obavezan
# Error message for invalid key format
invalidKeyFormat=Ključ zaglavlja koji ste uneli nije dozvoljen. Važeća zaglavlja su:<ul><li>"prefer"</li><li>Zaglavlja koja počinju sa "x-", osim "x-forwarded-host"</li><li>Zaglavlja koja sadrže alfanumeričke znakove, "-", ili "_"</li></ul>
# Error message for missing value
valueRequired=Vrednost je obavezna
# Error message for invalid characters in value
invalidValueCharacters=Zaglavlje sadrži nevažeće znakove. Posebni znakovi koji su dozvoljeni su:\t ";", ":", "-", "_", ",", "?", "/", i "*"
# Validation message for invoke api path
apiPathValidation=Unesite važeći put API-ja, na primer: /job/v1
# Validation message for JSON path
jsonPathValidation=Unesite važeći put JSON 
# Validation message for success/error indicator
indicatorValueValidation=Vrednost pokazatelja mora početi sa alfanumeričkim znakom i može uključivati sledeće posebne znakove:\t "-", i "_"
# Error message for JSON path
jsonPathRequired=Put JSON je obavezan
# Error message for invalid API Technical Name
invalidTechnicalName=Tehnički naziv sadrži nevažeće znakove
# Error message for empty Technical Name
emptyTechnicalName=Tehnički naziv je obavezan
# Tooltip for codeEditor dialog
codeEditorTooltip=Otvori prozor za uređivanje JSON 
# Status Api path validation message
validationStatusAPIPath=Unesite važeći put API-ja, na primer: /job/v1/{id}/status
# CSRF token URL validation message
validationCSRFTokenURL=URL tokena CSRF mora početi sa 'https://' i biti važeći URL
# Select a connection
selectConnection=Odaberi vezu
# Validation message for connection item error
connectionNotReplicated=Veza je trenutno nevažeća za izvođenje zadataka API-ja. Otvorite aplikaciju "Veze" i ponovo unesite svoje akreditive za popravljanje HTTP veze
# Validation message for API task properties wrong value or missing
VAL_REST_API_PROPERTIES_MISSING=Zadatak API-ja "{0}" ima netačne vrednosti ili vrednosti koje nedostaju za sledeća svojstva: {1}
# Validation message for API connection api is throwing some error or selected connection has some warning
VAL_REST_API_CONNECTION=Zadatak API-ja "{0}" ima problem HTTP veze. Otovrite aplikaciju "Veze" i proverite vezu
# Validation message for API task properties when delected connection
VAL_REST_API_CONNECTION_DELETED=Zadatak API-ja "{0}" ima izbrisanu vezu "{1}"
# Validation message for API task properties when connection cannot be consumed in the API task
VAL_REST_API_CONNECTION_CONSUMPTION_WARNING=Zadatak API-ja "{0}" ima vezu "{1}" koja se ne može koristiti za izvođenje zadatka API-ja. Otvorite aplikaciju „Veze” i ponovo unesite podatke pristupa da biste uspostavili vezu sa zadacima API-ja
# Information message if synchrnous mode is selected in the API Invocation then we don't need status information
modeInfoText=U sinhronom načinu, panel statusa nije prikazan za pozivanja API-ja
# validation dialog button for Run API Test
saveAnyway=Ipak sačuvaj
# validation message for technical name
technicalNameValidation=Tehnički naziv mora biti jedinstven u okviru lanca zadataka. Izaberite drugi tehnički naziv
# Connection error message
getHttpConnectionsError=Nije uspelo pozivanje HTTP veze
# Msg for the API test run when the Task chain is not saved
taskChainNotSaved=Lanac zadataka se mora sačuvati pre pokretanja probnog izvođenja API-ja
# Msg failed to run API test run
@failedToRunAPI=Izvođenje probnog izvođenja API-ja nije uspelo
# Msg for the API test run when its already in running state
apiTaskRunning=Probno izvođenje API-ja je već u toku. Da li želite da pokrenete novo probno izvođenje?

topToBtm=Od gore nadole
leftToRight=Sleva nadesno

#XTIT Apache Spark Application Settings
txtHeaderApacheSettings=Podešavanja aplikacije Apache Spark
#XFLD Use Default
txtUseSpaceDefault=Koristi standardnu vrednost
#XFLD Application
txtApplication=Aplikacija
#XFLD Define new settings for this Task
txtNewSettings=Definiši nova podešavanja za ovaj zadatak

#XFLD Use Default
txtUseDefault=Koristi standardnu vrednost




