#XTOL Undo
@undo=Болдырмау
#XTOL Redo
@redo=Қайталау
#XTOL Delete Selected Symbol
@deleteNode=Таңдалған таңбаны жою
#XTOL Zoom to Fit
@zoomToFit=Сыйдыру үшін үлкейту
#XTOL Auto Layout
@autoLayout=Автоматты пішім
#XMSG
@welcomeText=Нысандарды сол жақтағы панельден осы кенепке сүйреп апарыңыз.
#XMSG
@txtNoData=Әлі ешқандай нысан қоспаған сияқтысыз.
#XMSG
VAL_ENTER_VALID_STRING_GEN=Ұзындығы {0} мәніне аз жарамды жолды енгізіңіз.
#XMSG
@noParametersMsg=Бұл процедурада кіріс параметрлері жоқ.
#XMSG
@ip_enterValueMsg="{0}" (SQL сценарийін орындау процедурасы) процедурасында кіріс параметрлері бар. Олардың әрқайсысының мәнін орнатуға болады.
#XTOL
@validateModel=Тексеру туралы хабарлар
#XTOL
@hierarchy=Иерархия
#XTOL
@columnCount=Бағандар саны
#XFLD
@yes=Иә
#XFLD
@no=Жоқ
#XTIT Save Dialog param
@modelNameTaskChain=Тапсырмалар тізбегі
#properties panel
@lblPropertyTitle=Сипаттар
#XFLD
@lblGeneral=Жалпы
#XFLD : Setting
@lblSetting=Параметрлер
#XTXT: Delta Enabled Local Table Deletion
txtDeltaTableDeletion=Мынанша уақыттан ескірек "Жойылды" өзгерту түріне ие барлық толық өңделген жазбаларды жою:
#XTXT: Delta Enabled Local Table Deletion Input field description
desDeltaTableDeletion=Күн
#XFLD: Data Activation label
@lblDataActivation=Деректерді белсендіру
#XFLD: Latency label
@latency=Кідіріс
#XTEXT: Text for Latency dropdown
txtLatencyDefault=Әдепкі
#XTEXT: Text 1 hour
txtOneHour=1 сағат
#XTEXT: Text 2 hours
txtTwoHours=2 сағат
#XTEXT: Text 3 hours
txtThreeHours=3 сағат
#XTEXT: Text 4 hours
txtFourHours=4 сағат
#XTEXT: Text 6 hours
txtSixHours=6 сағат
#XTEXT: Text 12 hours
txtTwelveHours=12 сағат
#XTEXT: Text 1 day
txtOneDay=1 күн
#XFLD: Latency label
@autoRestartHead=Автоматты түрде қайта бастау
#XFLD
@lblConnectionName=Қосылым
#XFLD
@lblQualifiedName=Айқындалған ат
#XFLD
@lblSpaceName=Кеңістік аты
#XFLD
@lblLocalSchemaName=Жергілікті схема
#XFLD
@lblType=Нысан түрі
#XFLD
@lblActivity=Әрекет
#XFLD
@lblTableName=Кесте аты
#XFLD
@lblBusinessName=Бизнес атау
#XFLD
@lblTechnicalName=Техникалық атау
#XFLD
@lblSpace=Кеңістік
#XFLD
@lblLabel=Белгі
#XFLD
@lblDataType=Дерек түрі
#XFLD
@lblDescription=Сипаттама
#XFLD
@lblStorageType=Сақтау
#XFLD
@lblHTTPConnection=Тектік HTTP қосылымы
#XFLD
@lblAPISettings=Тектік API параметрлері
#XFLD
@header=Тақырыптар
#XFLD
@lblInvoke=API шақыруы
#XFLD
@lblMethod=Әдіс
#XFLD
@lblUrl=Негізгі URL
#XFLD
@lblAPIPath=API жолы
#XFLD
@lblMode=Режим
#XFLD
@lblCSRFToken=CSRF маркері талап етіледі
#XFLD
@lblTokenURL=CSRF маркерінің URL
#XFLD
@csrfTokenInfoText=Енгізілмесе, негізгі URL мекенжайы мен API жолы пайдаланылады
#XFLD
@lblCSRF=CSRF маркері
#XFLD
@lblRequestBody=Сұрау мәтіні
#XFLD
@lblFormat=Пішім
#XFLD
@lblResponse=Жауап
#XFLD
@lblId=Күйді шығарып алуға арналған ид.
#XFLD
@Id=Ид.
#XFLD
@lblSuccessIndicator=Сәттілік көрсеткіші
#XFLD
@lblErrorIndicator=Қате көрсеткіші
#XFLD
@lblErrorReason=Қате себебі
#XFLD
@lblStatus=Күйі
#XFLD
@lblApiTestRun=API сынағы сеансы
#XFLD
@lblRunStatus=Сеанс күйі
#XFLD
@lblLastRan=Соңғы сеанс
#XFLD
@lblTestRun=Сынақ сеансы
#XFLD
@lblDefaultHeader=Әдепкі тақырып өрістері (кілт-мән жұбы)
#XFLD
@lblAdditionalHeader=Қосымша тақырып өрісі
#XFLD
@lblKey=Кілт
#XFLD
@lblEditJSON=JSON өңдеу
#XFLD
@lblTasks=Тапсырмалар
#XFLD
@lblRESTfullTask=API
#XFLD: add field button text
btnAddField=Тақырып өрісін қосу
#XFLD: view Details link
@viewDetails=Мәліметтерді көру
#XTOL
tooltipTxt=Қосымша
#XTOL
delete=Жою
#XBTN: ok button text
btnOk=OK
#XBTN: cancel button text
btnCancel=Бас тарту
#XBTN: save button text
btnSave=Сақтау
#XMSG Error: Object has duplicated technical name.
VAL_OBJECT_DUPLICATE_NAME="{0}" нысаны репозитарийде бұрыннан бар. Басқа атауды енгізіңіз.
#XMSG: loading message while opening task chain
loadTaskChain=Тапсырмалар тізбегі жүктелуде...
#model properties
#XFLD
@status_panel=Сеанс күйі
#XFLD
@deploy_status_panel=Қолданысқа енгізу күйі
#XFLD
@status_lbl=Күйі
#XFLD
@lblLastExecuted=Соңғы сеанс
#XFLD
@lblNotExecuted=Әлі іске қосылған жоқ
#XFLD
@lblNotDeployed=Қолданысқа енгізілмеген
#XFLD
errorDetailsTxt=Сеанс күйін алу мүмкін болмады
#XBTN: Schedule dropdown menu
SCHEDULE=Жоспарлау
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Кестені өңдеу
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Кестені жою
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Кесте жасау
#XLNK
viewDetails=Мәліметтерді көру
#XMSG: error message for reading execution status from backend
backendErrorMsg=Деректер қазір серверден жүктеліп жатқан жоқ сияқты. Деректерді қайта алып көріңіз.
#XFLD: Status text for Completed
@statusCompleted=Орындалды
#XFLD: Status text for Running
@statusRunning=Орындалуда
#XFLD: Status text for Failed
@statusFailed=Сәтсіз аяқталды
#XFLD: Status text for Stopped
@statusStopped=Тоқтатылды
#XFLD: Status text for Stopping
@statusStopping=Тоқтатылуда
#XFLD
@LoaderTitle=Жүктелуде
#XFLD
#XFLD: Deploy status, dataflow has  been deployed / deployment date exists
@deployStatusActive=Қолданысқа енгізілген
@deployStatusRevised=Жергілікті жаңартулар
@deployStatusFailed=Сәтсіз аяқталды
@deployStatusPending=Қолданысқа енгізілуде...
@LoaderText=Серверден мәліметтер алынуда
#XMSG
@msgDetailFetchError=Серверден мәліметтерді алу кезінде қате орын алды
#XFLD
@executeError=Қате
#XFLD
@executeWarning=Ескерту
#XMSG
@executeConfirmDialog=Ақпарат
#XMSG
@executeunsavederror=Тапсырмалар тізбегін іске қоспас бұрын оны сақтаңыз.
#XMSG
@executemodifiederror=Тапсырмалар тізбегінде сақталмаған өзгерстер бар. Оны сақтаңыз.
#XMSG
@executerunningerror=Тапсырмалар тізбегі ағымдағы уақытта іске қосылуда. Жаңа сеансты бастамас бұрын, ағымдағы сеанс аяқталғанша күтіңіз.
#XMSG
@btnExecuteAnyway=Сонда да орындау
#XMSG
@msgExecuteWithValidations=Тапсырмалар тізбегінде тексеру қателері бар. Тапсырмалар тізбегін орындау ақаулыққа әкелуі мүмкін.
#XMSG
@msgRunDeployedVersion=Қолданысқа енгізілетін өзгерістер бар. Тапсырмалар тізбегінің соңғы қолданысқа енгізілген нұсқасы іске қосылады. Жалғастырғыңыз келе ме?
#XMSG
#XMSG
@navToMonitoring=Тапсырмалар тізбегі мониторында ашу
#XMSG
txtOR=НЕМЕСЕ
#XFLD
@preview=Алдын ала көру
#XMSG
txtand=және
#NOTR
@dateFormat=yyyy-MM-dd
#NOTR
@dateTimeFormatFilter=yyyy-MM-dd HH:mm:ss.
#NOTR
@timeFormat=HH:mm:ss
@lblColum=Баған
#XFLD
@lblCondition=Шарт
#XFLD
@lblValue=Мәні
#XMSG
@msgJsonInvalid=Тапсырмалар тізбегін сақтау мүмкін болмады, себебі JSON файлында қателер бар. Тексеріп, қателерді түзетіңіз.
#XTIT
@msgSaveFailTitle=JSON жарамсыз.
#XMSG
NOT_CHAINABLE="{0}" нысанын тапсырмалар тізбегіне қосу мүмкін емес.
#XMSG
NOT_CHAINABLE_REMOTETABLE="{0}" нысаны: тираждау түрі өзгертіледі.
#XMSG
searchTaskChain=Нысандарды іздеу

#XFLD
@txtTaskChain=Тапсырмалар тізбегі
#XFLD
@txtRemoteTable=Қашықтағы кесте
#XFLD
@txtRemoveData=Тираждалған деректерді жою
#XFLD
@txtRemovePersist=Сақталған деректерді жою
#XFLD
@txtView=Көру
#XFLD
@txtDataFlow=Деректер ағыны
#XFLD
@txtIL=Интеллектуалды іздеу
#XFLD
@txtTransformationFlow=Түрлендіру ағыны
#XFLD
@txtReplicationFlow=Тираждау ағыны
#XFLD
@txtDeltaLocalTable=Жергілікті кесте
#XFLD
@txtBWProcessChain=BW процестер тізбегі
#XFLD
@txtSQLScriptProcedure=SQL сценарий процедурасы
#XFLD
@txtAPI=API
#XFLD
@txtMerge=Біріктіру
#XFLD
@txtOptimize=Оңтайландыру
#XFLD
@txtVacuum=Жазбаларды жою

#XFLD
@txtRun=Орындау
#XFLD
@txtPersist=Сақтау
#XFLD
@txtReplicate=Тираждау
#XFLD
@txtDelete="Жойылды" өзгерту түріне ие жазбаларды жою
#XFLD
@txtRunTC=Тапсырмалар тізбегін орындау
#XFLD
@txtRunBW=BW процестер тізбегін орындау
#XFLD
@txtRunSQLScriptProcedure=SQL сценарий процедурасын орындау

#XFLD
@txtRunDataFlow=Дерек ағынын орындау
#XFLD
@txtPersistView=Көрінісін сақтау
#XFLD
@txtReplicateTable=Кестені тираждау
#XFLD
@txtRunIL=Интеллектуалды іздеуді орындау
#XFLD
@txtRunTF=Түрлендіру ағынын орындау
#XFLD
@txtRunRF=Тираждау ағынын орындау
#XFLD
@txtRemoveReplicatedData=Тираждалған деректерді жою
#XFLD
@txtRemovePersistedData=Сақталған деректерді жою
#XFLD
@txtMergeData=Біріктіру
#XFLD
@txtOptimizeData=Оңтайландыру
#XFLD
@txtVacuumData=Жазбаларды жою
#XFLD
@txtRunAPI=API интерфейсін іске қосу

#XFLD storage type text
hdlfStorage=Файл

@statusNew=Қолданысқа енгізілмеген
@statusActive=Қолданысқа енгізілген
@statusRevised=Жергілікті жаңартулар
@statusPending=Қолданысқа енгізілуде...
@statusChangesToDeploy=Қолданысқа енгізілетін өзгерістер
@statusDesignTimeError=Жобалау уақыты қатесі
@statusRunTimeError=Орындау уақыты қатесі

#XTIT
txtNodes=Тапсырмалар тізбегіндегі нысандар ({0})
#XBTN
@deleteNodes=Жою

#XMSG
@txtDropDataToDiagram=Нысандарды диаграммаға сүйреп апарыңыз.
#XMSG
@noData=Нысандар жоқ

#XFLD
@txtTaskPosition=Тапсырма позициясы

#input parameters and variables
#XFLD
lblInputParameters=Кіріс параметрлері
#XFLD
ip_name=Атауы
#XFLD
ip_value=Мәні
#XTEXT
@noObjectsFound=Ешқандай нысан табылмады

#XMSG
@msgExecuteSuccess=Тапсырмалар тізбегін орындау басталды.
#XMSG
@msgExecuteFail=Тапсырмалар тізбегін орындау сәтсіз аяқталды.
#XMSG
@msgDeployAndRunSuccess=Тапсырмалар тізбегін қолданысқа енгізу және орындау басталды.
#XMSG
@msgDeployAndRunFail=Тапсырмалар тізбегін қолданысқа енгізу және орындау сәтсіз аяқталды.
#XMSG
@titleExecuteBusy=Күте тұрыңыз.
#XMSG
@msgExecuteBusy=Тапсырмалар тізбегін орындауды бастау үшін деректер дайындалуда.
#XMSG
@msgAPITestRunSuccess=API сынағын орындау басталды.
#XMSG
@msgAPIExecuteBusy=API сынағын орындауды бастау үшін деректер дайындалуда.

#XTOL
txtOpenInEditor=Редакторда ашу
#XTOL
txtPreviewData=Деректі алдын ала көру

#datapreview
#XMSG
@msgDataPreviewNotSupp=Осы нысан үшін деректі алдын ала көру қолжетімсіз.

#XMSG Error: empty model
VAL_MODEL_EMPTY=Үлгіңіз бос сияқты. Бірнеше нысан қосыңыз.
#XMSG Error: deploy model
@msgDeployBeforeRun=Тапсырмалар тізбегін орындамас бұрын оны қолданысқа енгізуіңіз керек.
#BTN: close dialog
btnClose=Жабу

#XMSG Object is not Deployed
VAL_NOT_DEPLOYED=Тапсырмалар тізбегін орындауды жалғастыру үшін "{0}" нысаны қолданысқа енгізілуі керек.
#XMSG Object is having runtime error
VAL_RUNTIME_ERROR="{0}" нысаны орындау уақыты қатесін қайтарады. Нысанды тексеріңіз.
#XMSG Object is having design time error
VAL_DESIGNTIME_ERROR="{0}" нысаны құрастыру уақыты қатесін қайтарады. Нысанды тексеріңіз.
#XMSG Procedure Object has been deleted
VAL_DELETED_PROCEDURE=Келесі процедуралар жойылды: {0}
#XMSG New Parameter added to Procedure object
VAL_NEW_PARAMETER_ADDED=Жаңа параметрлер "{1}" процедурасына қосылды: "{0}". Тапсырмалар тізбегін қолданысқа енгізіңіз.
#XMSG Parameter removed from Procedure object
VAL_PARAMETER_DELETED=Параметрлер "{1}" процедурасынан жойылды: "{0}". Тапсырмалар тізбегін қолданысқа енгізіңіз.
#XMSG Parameter data type changed in Procedure object
VAL_PARAMETER_DATATYPE_MODIFIED="{0}" параметрінің дерек түрі "{3}" процедурасында "{1}" түрінен "{2}" түріне өзгертілді.
#XMSG Parameter length changed of specific datatype in Procedure object
VAL_PARAMETER_LENGTH_MODIFIED="{0}" параметрінің ұзындығы "{3}" процедурасында "{1}" мәнінен "{2}" мәніне өзгертілді.
#XMSG Parameter precision changed of Decimal datatype in Procedure object
VAL_PARAMETER_PRECISION_MODIFIED="{0}" параметрінің дәлдігі "{3}" процедурасында "{1}" мәнінен "{2}" мәніне өзгертілді.
#XMSG Parameter scale changed of Decimal datatype in Procedure object
VAL_PARAMETER_SCALE_MODIFIED="{0}" параметрінің шкаласы "{3}" процедурасында "{1}" мәнінен "{2}" мәніне өзгертілді.
#XMSG Parameter value not provided in Procedure object
VAL_SQL_SCRIPT_PROCEDURE_PARAMETER_VALUE_MISSING="{0}" процедурасы үшін талап етілетін кіріс параметрлері үшін мәндерді енгізбедіңіз: "{1}"
#XMSG Remote Table activity changes to Remove Replicated Data
VAL_REMOVE_REPLICATED_DATA=Тапсырмалар тізбегінің келесі сеансы деректерге қол жеткізу түрін өзгертеді және деректер енді нақты уақыт режимінде жүктеп салынбайды.
#XMSG Remote Table activity changes
REPLICATION_TYPE_CHANGED="{0}" нысаны: тираждау түрі өзгертіледі.

#XFLD
@lblStartNode=Бастапқы түйін
#XFLD
@lblEndNode=Соңғы түйін
#XFLD
@linkTo={0} -> {1}
#XTOL
@txtViewDetails=Мәліметтерді көру

#XTOL
txtOpenImpactLineage=Тәуелділікті және шығу тегін талдау
#XFLD
@emailNotifications=Эл. пошта хабарландырулары
#XFLD
@txtReset=Қалпына келтіру
#XFLD
@emailMsgWarning=Электрондық пошта хабары бос болған кезде, әдепкі үлгідегі электрондық хабар жіберіледі.
#XFLD
@notificationSettings=Хабарландыру параметрлері
#XFLD
@recipientEmailAddr=Алушының электрондық пошта мекенжайы
#XFLD
@emailSubject=Эл. хат тақырыбы
@emailSubjectText=<TASKCHAIN_NAME> тапсырмалар тізбегі <STATUS> күйімен орындалды
#XFLD
@emailMessage=Электрондық пошта хабары
@emailMessageText=Құрметті пайдаланушы,\n\n Бұл хатпен сізге <START_TIME> кезінде басталған <TASKCHAIN_NAME> тапсырмалар тізбегі <STATUS> күйімен аяқталғаны туралы хабарлаймыз. Орындау сеансы <END_TIME> кезінде аяқталды.\n\nМәліметтер:\nКеңістік:<SPACE_NAME>\nҚате:<ERROR_MESSAGE>
@selectEmailRecipientDialogTitle=Алушының электрондық пошта мекенжайын таңдау
@tenantMembers=Тенант қатысушылары
@others=Басқалар ({0})
@selectedEmailAddress=Таңдалған алушылар
@add=Қосу
@placeholder=Толтырғыш
@description=Сипаттама
@copyText=Мәтінді көшіру
@taskchainDetailsPlaceholder=Тапсырмалар тізбегі туралы мәліметтерге арналған толтырғыштар
@placeholderCopied=Толтырғыш көшірілді
@invalidEmailInfo=Дұрыс эл. пошта мекенжайын енгізіңіз
@maxMembersAlreadyAdded=Сіз ең көбі 20 қатысушы қосып қойдыңыз
@enterEmailAddress=Пайдаланушының эл. пошта мекенжайын енгізіңіз
@inCorrectPlaceHolder={0} электрондық пошта хатының мәтінінде күтілген толтырғыш емес.
@nsOFF=Кез келген хабарландыруларды жібермеу
@nsFAILED=Сеанс қатемен аяқталған кезде ғана электрондық пошта хабарландыруын жіберу
@nsCOMPLETED=Сеанс сәтті аяқталған кезде ғана электрондық пошта хабарландыруын жіберу
@nsANY=Сеанс орындалған кезде электрондық хат жіберу
@phStatus=Тапсырмалар тізбегінің күйі - СӘТТІ|СӘТСІЗ
@phTaskChainTName=Тапсырмалар тізбегінің техникалық атауы
@phTaskChainBName=Тапсырмалар тізбегінің бизнес атауы
@phLogId=Сеанс журналының ид.
@phUser=Тапсырмалар тізбегін орындап жатқан пайдаланушы
@phLogUILink=Тапсырмалар тізбегінің журналын көрсету сілтемесі
@phStartTime=Сеанстың басталу уақыты
@phEndTime=Сеанстың аяқталу уақыты
@phErrMsg=Тапсырмалар журналындағы алғашқы қате туралы хабар. Журнал күйі СӘТТІ болған жағдайда бос болады
@phSpaceName=Кеңістіктің техникалық атауы
@emailFormatError=Эл. пошта пішімі жарамсыз 
@emailFormatErrorInListText=Тенантқа қатысы жоқ қатысушылар тізіміне жарамсыз эл. пошта пішімі енгізілді.
@emailSubjectTemplateText=Тапсырмалар тізбегіне арналған хабарландыру: $$taskChainName$$ - Кеңістік: $$spaceId$$ - Күй: $$status$$
@emailMessageTemplateText=Сәлеметсіз бе,\n\n $$taskChainName$$ белгісімен белгіленген тапсырмалар тізбегі $$status$$ күйімен аяқталды. \n Мұнда тапсырмалар тізбегі туралы басқа мәліметтер берілген:\n - Тапсырмалар тізбегінің техникалық атауы: $$taskChainName$$ \n - Тапсырмалар тізбегі сеансының журнал ид.: $$logId$$ \n - Тапсырмалар тізбегін орындаған пайдаланушы: $$user$$ \n - Тапсырмалар тізбегінің журналын көрсету сілтемесі: $$uiLink$$ \n - Тапсырмалар тізбегі сеансының басталу уақыты: $$startTime$$ \n - Тапсырмалар тізбегі сеансының аяқталу уақыты: $$endTime$$ \n - Кеңістік атауы: $$spaceId$$ \n
@deleteEmailRecepient=Алушыны жою
@emailInputDisabledText=Электрондық пошта хатының алушыларын қосу үшін тапсырмалар тізбегін қолданысқа енгізіңіз.
@tenantOwnerDomainMatchErrorText=Электрондық пошта мекенжайының домені тенант иесінің доменіне сәйкес келмейді: {0}
@totalEmailIdLimitInfoText=Ең көбі 20 электрондық пошта алушысын, соның ішінде тенант қатысушысы пайдаланушыларын және басқа алушыларды таңдай аласыз.
@emailDomainInfoText={0} домені бар электрондық пошта мекенжайлары ғана қабылданады.
@duplicateEmailErrorText=Тізімде қайталанатын электрондық пошта алушылары бар.

#XFLD Zorder Title
@txtZorderTitle=Apache Spark Z ретті бағандары

#XFLD Zorder NoColumn
@txtZorderNoColumn=Z ретті бағандар табылмады

#XFLD Zorder Column PrimaryKey
@txtZorderColumnPrimaryKey=Бастапқы кілт

#XFLD
@lblOperators=Операторлар
addNewSelector=Жаңа тапсырма ретінде қосу
parallelSelector=Параллельді тапсырма ретінде қосу
replaceSelector=Бұрыннан бар тапсырманы ауыстыру
addparallelbranch=Параллельді тармақ ретінде қосу
addplaceholder=Толтырғыш қосу
addALLOperation=БАРЛЫҚ оператор
addOROperation=КЕЗ КЕЛГЕН оператор
addplaceholdertocanvas=Толтырғышты кенепке қосу
addplaceholderonselected=Таңдалған тапсырмадан кейін толтырғышты қосу
#XFLD: Add Parallel Branch to Selected Task
addparallelbranchonselected=Таңдалған тапсырмадан кейін параллелтді тармақты қосу
addOperator=Операторды қосу
txtAdd=Қосу
txtPlaceHolderText=Тапсырманы осы жерге сүйреп апарыңыз
@lblLayout=Пішім

#XMSG
VAL_UNCONNECTED_TASK="{0}" тапсырмасы тапсырмалар тізбегіне қосылмаған.
#XMSG
VAL_TASK_SHOULD_HAVE_ONE_INCOMING_LINK="{0}" тапсырмасында тек бір кіріс сілтеме болуы керек.
#XMSG
VAL_TASK_SHOULD_HAVE_INCOMING_LINK="{0}" тапсырмасында бір кіріс сілтеме болуы керек.
#XMSG
VAL_UNCONNECTED_OPERATOR="{0}" операторы тапсырмалар тізбегіне қосылмаған.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ATLEAST_TWO_INCOMING_LINK="{0}" операторында кем дегенде екі кіріс сілтеме болуы керек.
#XMSG
VAL_OPERATOR_SHOULD_HAVE_ONE_OUT_LINK="{0}" операторында кем дегенде бір шығыс сілтеме болуы керек.
#XMSG
VAL_CIRCULAR_REFERENCE_EXITS="{0}" тапсырмалар тізбегінде циркулярлық цикл бар.
#XMSG
VAL_UNCONNECTED_BRANCH="{0}" нысаны/тармағы тапсырмалар тізбегіне қосылмаған.
#XMSG
VAL_TASK_TRIGGERED_PARALLEL_MORE_THAN_ONE_TIME= "{0}" тапсырмасы бірнеше рет параллель қосылған. Жалғастыру үшін көшірмелерді жойыңыз.


txtBegin=Бастау
txtNodesInLink=Қатысқан нысандар
#XTOL Tooltip for a context button on diagram
openInNewTab=Жаңа қойындыда ашу
#XTOL Tooltip for a context button on diagram
txtConnectToTaskOperator=Қосу үшін сүйреңіз
@emailUpdateError=Электрондық пошта хабарландыруларының тізімін жаңарту кезінде қате орын алды

#XMSG
noTeamPrivilegeTxt=Тенант қатысушыларының тізімін көруге рұқсатыңыз жоқ. Электрондық пошта алушыларын қолмен қосу үшін "Басқалар" қойындысын пайдаланыңыз.

#XFLD Package
@txtPackage=Бума

#XMSG warning for repository package assignment changed. {1} - package name
VAL_PACKAGE_CHANGED=Осы нысанды "{1}" бумасына тағайындадыңыз. Осы өзгерісті растау және бағалау үшін "Сақтау" түймесін басыңыз. Сақтағаннан кейін редакторда бумаға тағайындау әрекетінен бас тарту мүмкін емес екенін ескеріңіз.
#XMSG warning for repository package dependency issue.
MISSING_DEPENDENCY="{0}" нысанының тәуелділіктерін "{1}" бумасы мәнмәтінінде шешу мүмкін емес.

#Warning messages for the folders in the "Others" tab of task chain editor
#XMSG generic warning if getting the objects in a particular folder fails with unhandled errors
getNonRepoArtefactTypeDescendantsError=Сіз таңдаған қалтадағы нысандарды шығарып алу кезінде ақаулық орын алды.
#BW Process Chains folder related warning messages
#XMSG warning if user does not have the necessary permission related to the SAP BW Bridge space
missingPrivilegeForBridgeSpaceError=BW процестер тізбектерін тапсырмалар тізбегінде қарауға немесе қосуға қажетті рұқсатыңыз жоқ.
#XMSG warning if there was a problem retrieving the BW process chains due to bridge space membership, bridge connection issues or other errors
generalBridgeSpaceError=BW процестер тізбектерін шығарып алу кезінде мәселе туындады.
#XMSG warning if the BW API for retrieving the BW process chains from the SAP BW Bridge tenant fails
bwApiClientError=BW процестер тізбектерін SAP BW Bridge тенантынан шығарып алу кезінде мәселе туындады.
#XMSG warning if BW process chains list is empty
bwApiEmptyListError=SAP BW Bridge тенантынан BW процестер тізбегі табылмады.
#XMSG: Task log message for invalid OIDC configuration in the SAP BW Bridge tenant
bwBridgeOIDCConfigStatusInvalid=Осы тенант үшін OpenID аутентификациясы теңшелмеген. SAP BW көпірінің тенантындағы OpenID аутентификациясын SAP 3536298 ескертпесінде сипатталғандай теңшеу үшін "{0}" клиент идентификаторын және "{1}" маркердің URL мекенжайын пайдаланыңыз.
#XMSG BW Process Chain Object has been deleted
VAL_DELETED_BW=SAP BW Bridge тенантынан келесі BW процестер тізбегі жойылды: {0}
#BW SQL Script Procedures folder related warning messages
#XFLD waring if the Procedure list is empty
procedureAPIEmptyList=Ешқандай процедура жасалмады немесе Open SQL схемасына EXECUTE артықшылығы берілмеді.
#Change digram orientations
changeOrientations=Бағдарларды өзгерту


# placeholder for the API Path
apiPath=Мысалы: /job/v1
# placeholder for the status API Path
statusAPIPath=Мысалы: /job/v1/{id}/status
# valueStateText for the API Path
apiPathRequired=API жолын енгізу талап етіледі
#placeholder for the CSRF Token URL
csrfTokenURL=Тек HTTPS қолдау көрсетіледі
# Response Type 1
statusCode=HTTP күй кодынан нәтижені алу
# Response Type 2
locationHeader=HTTP күй кодынан және орналасқан жер тақырыбынан нәтижені алу
# Response Type 3
responseBody=HTTP күй коды мен жауаптың негізгі мәтінінен нәтижені алу
# placeholder for ID
idPlaceholder=JSON жолын енгізу
# placeholder for indicator value
indicatorValue=Мән енгізу
# Placeholder for key
keyPlaceholder=КІлт енгізу
# Error message for missing key
KeyRequired=Кілтті енгізу талап етіледі
# Error message for invalid key format
invalidKeyFormat=Сіз енгізген тақырып кілтіне рұқсат етілмейді. Жарамды тақырыптар:<ul><li>"prefer"</li><li>"x-forwarded-host" қоспағанда, "x-" мәнінен басталатын тақырыптар</li><li>Әріптік-сандық таңбалардан, "-" немесе "_" таңбаларынан тұратын тақырыптар</li></ul>
# Error message for missing value
valueRequired=Мәнді енгізу міндетті
# Error message for invalid characters in value
invalidValueCharacters=Тақырыпта жарамсыз таңбалар бар. Рұқсат етілетін арнайы таңбалар:\t ";", ":", "-", "_", ",", "?", "/", және "*"
# Validation message for invoke api path
apiPathValidation=Жарамды API жолын енгізіңіз, мысалы: /job/v1
# Validation message for JSON path
jsonPathValidation=Жарамды JSON жолын енгізіңіз
# Validation message for success/error indicator
indicatorValueValidation=Көрсеткіш мәні әріптік-сандық таңбадан басталуы керек және келесі арнайы таңбаларды қамтуы мүмкін:\t "-" және d "_"
# Error message for JSON path
jsonPathRequired=JSON жолын енгізу талап етіледі
# Error message for invalid API Technical Name
invalidTechnicalName=Техникалық атауда жарамсыз таңбалар бар
# Error message for empty Technical Name
emptyTechnicalName=Техникалық атауды енгізу талап етіледі
# Tooltip for codeEditor dialog
codeEditorTooltip=JSON өңдеу терезесін ашыңыз
# Status Api path validation message
validationStatusAPIPath=Жарамды API жолын енгізіңіз, мысалы: /job/v1/{id}/status
# CSRF token URL validation message
validationCSRFTokenURL=CSRF маркерінің URL мекенжайы "https:/"' деп басталуы керек және жарамды URL болуы керек
# Select a connection
selectConnection=Қосылымды таңдаңыз
# Validation message for connection item error
connectionNotReplicated=API тапсырмаларын орындау үшін қосылым жарамсыз. "Қосылымдар" қолданбасын ашып, HTTP қосылымын реттеу үшін тіркелгі деректерін қайта енгізіңіз
# Validation message for API task properties wrong value or missing
VAL_REST_API_PROPERTIES_MISSING="{0}" API тапсырмасы дұрыс емес немесе келесі сипаттар үшін мәндер енгізілмеген: {1}
# Validation message for API connection api is throwing some error or selected connection has some warning
VAL_REST_API_CONNECTION="{0}" API тапсырмасында HTTP қосылымына қатысты мәселе бар. "Қосылымдар" қолданбасын ашып, қосылымды тексеріңіз
# Validation message for API task properties when delected connection
VAL_REST_API_CONNECTION_DELETED="{0}" API тапсырмасында жойылған "{1}" қосылымы бар
# Validation message for API task properties when connection cannot be consumed in the API task
VAL_REST_API_CONNECTION_CONSUMPTION_WARNING="{0}" API тапсырмасында API тапсырмаларын жүргізу үшін пайдалануға болмайтын "{1}" қосылымы бар. API тапсырмалары үшін қосылым орнату мақсатында "Қосылымдар" қолданбасын ашыңыз және тіркелгі дерегін қайта енгізіңіз.
# Information message if synchrnous mode is selected in the API Invocation then we don't need status information
modeInfoText=Синхронды режимде күй панелі API шақырулары үшін көрсетілмейді
# validation dialog button for Run API Test
saveAnyway=Сонда да сақтау
# validation message for technical name
technicalNameValidation=Техникалық атау тапсырмалар тізбегінде бірегей болуы керек. Басқа техникалық атауды таңдаңыз
# Connection error message
getHttpConnectionsError=HTTP қосылымдарын алу сәтсіз аяқталды
# Msg for the API test run when the Task chain is not saved
taskChainNotSaved=API сынағы сеансын іске қоспас бұрын тапсырмалар тізбегі сақталуы керек
# Msg failed to run API test run
@failedToRunAPI=API сынағы сеансын орындау сәтсіз аяқталды
# Msg for the API test run when its already in running state
apiTaskRunning=API сынақ сеансы әлдеқашан орындалуда. Жаңа сынақ сеансын бастау қажет пе?

topToBtm=Жоғарғы-төменгі
leftToRight=Сол-оң

#XTIT Apache Spark Application Settings
txtHeaderApacheSettings=Apache Spark қолданбасының параметрлері
#XFLD Use Default
txtUseSpaceDefault=Әдепкісін пайдалану
#XFLD Application
txtApplication=Қолданба
#XFLD Define new settings for this Task
txtNewSettings=Осы тапсырмаға арналған жаңа параметрлерді анықтау

#XFLD Use Default
txtUseDefault=Әдепкісін пайдалану




