/**
 * Copyright 2020 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import * as commonAvailableChecks from "../../../commonmodel/model/availableChecks/common.basic";
import * as sharedModelMethods from "../../../commonmodel/model/sharedDefinitions/Methods";
import * as sharedModelProperties from "../../../commonmodel/model/sharedDefinitions/Properties";
import {
  namedObjectProperties,
  repositoryObjectProperties,
} from "../../../commonmodel/model/sharedDefinitions/Properties";
import { validationReferences } from "../../../commonmodel/model/sharedDefinitions/References";
import { expandModelDefinition } from "../../../commonmodel/utility/GalileiUtils";
import { ObjectNameDisplay } from "../../../userSettings/utility/Constants";
import Utils, {
  OutputType,
  RESTAPI_Properties,
  REST_API_Header,
  REST_API_References,
  REST_API_ReqProperties,
  REST_API_ResProperties,
  notificationProperties,
  taskReferences,
  taskTypeProperties,
} from "../utils";

sap.galilei.namespace("sap.cdw.taskchainmodeler", function () {
  const nsLocal = sap.cdw.taskchainmodeler;
  const nsCommonValidation = sap.cdw.commonmodel.Validation;

  /**
   * Task Chain meta-model definition
   *
   */
  const oModelDef = {
    contents: {
      /**
       * sap.cdw.taskchainmodeler definition
       */
      "sap.cdw.taskchainmodeler": {
        classDefinition: "sap.galilei.model.Package",
        displayName: "Task Chain Model",
        namespaceName: "sap.cdw.taskchainmodeler",
        classifiers: {
          /**
           * @class
           * Model root container for task chain modeler objects
           */
          Model: {
            displayName: "Model",
            parent: "sap.galilei.common.Model",
            properties: {
              // Indicates that the model is for Data Layer.
              isDataLayer: {
                dataType: sap.galilei.model.dataTypes.gBool,
                get: function () {
                  return true;
                },
              },
              isNew: {
                name: "isNew",
                dataType: sap.galilei.model.dataTypes.gBool,
                defaultValue: false,
              },
              isVerticalView: {
                name: "isVerticalView",
                dataType: sap.galilei.model.dataTypes.gBool,
                defaultValue: true,
              },
              bRedrawDiagram: {
                name: "bRedrawDiagram",
                dataType: sap.galilei.model.dataTypes.gBool,
                defaultValue: false,
              },

              // Object name display mode: whether business name or technical name is shown
              // see user prefs configuration
              objectNameDisplay: {
                name: "objectNameDisplay",
                dataType: sap.galilei.model.dataTypes.gString,
                defaultValue: ObjectNameDisplay.businessName,
                isVolatile: true,
              },
              hasSuccessorNodesBeforeDelete: {
                dataType: sap.galilei.model.dataTypes.gBool,
                default: false,
              },
              packageValue: {
                dataType: sap.galilei.model.dataTypes.gString,
                defaultValue: "_NONE_KEY_PACKAGE_",
                available: [commonAvailableChecks.onlyIfPrivileges],
              },
              packageStatus: {
                dataType: sap.galilei.model.dataTypes.gString,
                defaultValue: "",
                onCascadeChange: function (oEventArgs) {
                  const model = oEventArgs.instance;
                  nsLocal.Validation.validateModel(model);
                },
              },
              isEmailRecipientEmpty: {
                dataType: sap.galilei.model.dataTypes.gBool,
                defaultValue: false,
              },
              applications: {
                dataType: sap.galilei.model.dataTypes.gBlob,
                defaultValue: [],
              },
              ...namedObjectProperties,
              ...repositoryObjectProperties,
            },
            references: {
              nodes: {
                contentType: "sap.cdw.taskchainmodeler.Node",
                isMany: true,
                isContainment: true,
              },
              links: {
                contentType: "sap.cdw.taskchainmodeler.Link",
                isMany: true,
                isContainment: true,
              },
              options: {
                contentType: "sap.cdw.taskchainmodeler.NotificationOptions",
                isMany: false,
                isContainment: false,
              },
              placeholders: {
                contentType: "sap.cdw.taskchainmodeler.PlaceHolder",
                isMany: true,
                isContainment: true,
              },
              orderedNodes: {
                contentType: "sap.cdw.taskchainmodeler.Node",
                isMany: true,
                isContainment: false,
                isVolatile: true,
                isComputed: true,
                isCached: false,
                isInternal: true,
                isSupportNotification: true,
                get: function () {
                  return (
                    (this.nodes &&
                      this.nodes.toArray().sort(function (oNode1, oNode2) {
                        return oNode1.nodeIndex - oNode2.nodeIndex;
                      })) ||
                    []
                  );
                },
              },
              validations: {
                contentType: "sap.cdw.commonmodel.ValidationStatus",
                isMany: true,
                isContainment: true,
                isVolatile: true,
                isInternal: true,
                isSupportNotification: true,
              },
              // @overrride
              aggregatedValidations: {
                contentType: "sap.cdw.commonmodel.ValidationStatus",
                isMany: true,
                isContainment: false,
                isVolatile: true,
                isComputed: true,
                isCached: false,
                isInternal: true,
                isSupportNotification: true,
                get: function () {
                  const aNodesValidations = nsCommonValidation.getAggregatedValidation(this, this.nodes);
                  const aAssocValidations = nsCommonValidation.getAggregatedValidation(
                    this,
                    this.links,
                    /* bOnlyChildren */ true
                  );
                  let aggStatus = aNodesValidations.status;
                  let aggValidations = aNodesValidations.validations;
                  aggValidations =
                    aggValidations &&
                    aAssocValidations.validations &&
                    aggValidations.concat(aAssocValidations.validations);
                  if (aggStatus === "ok") {
                    aggStatus = aAssocValidations.status;
                  }
                  return {
                    status: aggStatus,
                    validations: aggValidations || [],
                    isBlocker: false,
                  };
                },
              },
              startNode: {
                contentType: "sap.cdw.taskchainmodeler.start",
                isMany: false,
                isContainment: true,
              },
            },
            methods: {
              // Methods for processing
              ...sharedModelMethods.modelProcessingMethods,
              // Methods for respository object (objectStatus for instance)
              ...sharedModelMethods.repositoryObjectMethods,

              clearValidation: function () {
                this.resource.applyUndoableAction(
                  function () {
                    if (this.validations && typeof this.validations.clear === "function") {
                      this.validations.clear();
                    }
                  }.bind(this),
                  "Clear validations",
                  /* protectedFromUndo */ true
                );
              },
              // Gets the ModelImpl class
              getModelImpl: function () {
                return nsLocal.ModelImpl;
              },
              // @overrride
              validate: async function (): Promise<any> {
                return nsLocal.Validation.validateModel(this);
              },
            },
          },

          /**
           * @class
           * Node abstract class for all node objects
           */
          Node: {
            displayName: "Node",
            parent: "sap.cdw.commonmodel.BaseObject",
            properties: {
              // The technical name
              name: {
                dataType: sap.galilei.model.dataTypes.gString,
                available: [commonAvailableChecks.onlyIfPrivileges],
              },
              nodeType: {
                dataType: sap.galilei.model.dataTypes.gInteger,
              },
              "#technicalType": {
                dataType: sap.galilei.model.dataTypes.gString,
              },
              nodeIndex: {
                dataType: sap.galilei.model.dataTypes.gInteger,
              },
              nodeIndexForJSON: {
                dataType: sap.galilei.model.dataTypes.gInteger,
              },
              placeHolderNodes: {
                dataType: sap.galilei.model.dataTypes.gInteger,
                default: 0,
              },
              isDanglingNode: {
                dataType: sap.galilei.model.dataTypes.gBool,
                get: function () {
                  return !this.predecessorNodes?.length;
                },
              },
              isDanglingBranch: {
                dataType: sap.galilei.model.dataTypes.gBool,
                get: function () {
                  const allObjects = [];
                  let isDanglingBranch = true;
                  function getNodes(obj) {
                    let objects;
                    objects =
                      obj?.container?.links
                        ?.selectAllObjects({
                          "target.objectId": obj.objectId,
                        })
                        .toArray()
                        .map((oLink) => oLink.source) || [];
                    if (objects) {
                      for (let i = 0; i < objects.length; i++) {
                        if (objects[i].isStart) {
                          isDanglingBranch = false;
                          break;
                        }
                        if (!allObjects.includes(objects[i])) {
                          allObjects.push(objects[i]);
                          getNodes(objects[i]);
                        }
                      }
                    }
                    return allObjects;
                  }
                  if (this.isStart) {
                    isDanglingBranch = false;
                  } else {
                    getNodes(this);
                  }
                  return isDanglingBranch;
                },
              },
              isStart: {
                dataType: sap.galilei.model.dataTypes.gBool,
                get: function () {
                  return false;
                },
              },
              isCrossSpace: {
                dataType: sap.galilei.model.dataTypes.gBool,
                isCached: false,
              },
              crossSpaceName: {
                dataType: sap.galilei.model.dataTypes.gString,
              },
            },
            references: {
              ...validationReferences,
              /**
               * An abstract Node class that contains links
               */
              links: {
                contentType: "sap.cdw.taskchainmodeler.Link",
                isMany: true,
                isContainment: false,
              },

              /**
               * target links
               */
              sourceLinks: {
                contentType: "sap.cdw.taskchainmodeler.Link",
                isMany: true,
                isContainment: false,
                isComputed: true,
                isCached: false,
                isVolatile: true,
                isInternal: true,
                isSupportNotification: true,
                get: function () {
                  if (nsLocal.ModelImpl.isDeleting) {
                    // in case of deleting we use resource context
                    return (
                      (this.resource &&
                        this.resource.objects &&
                        this.resource.objects
                          .selectAllObjects({
                            "target.objectId": this.objectId,
                          })
                          .toArray()) ||
                      []
                    );
                  }
                  return (
                    (this.container &&
                      this.container.links &&
                      this.container.links
                        .selectAllObjects({
                          "target.objectId": this.objectId,
                        })
                        .toArray()) ||
                    []
                  );
                },
              },
              /**
               * A node has at least one successor (0 for the last node)
               */
              successorNodes: {
                contentType: "sap.cdw.taskchainmodeler.Node",
                isMany: true,
                isContainment: false,
                isComputed: true,
                isCached: false,
                isVolatile: true,
                isInternal: true,
                isSupportNotification: true,
                get: function () {
                  if (nsLocal.ModelImpl.isDeleting) {
                    // in case of deleting we use resource context
                    return (
                      (this.resource &&
                        this.resource.objects &&
                        this.resource.objects
                          .selectAllObjects({
                            "source.objectId": this.objectId,
                          })
                          .toArray()
                          .filter((oLink) => oLink.target)
                          .map((oLink) => oLink.target)) ||
                      []
                    );
                  }
                  return (
                    (this.resource?.model?.links &&
                      this.resource.model.links
                        .selectAllObjects({
                          "source.objectId": this.objectId,
                        })
                        .toArray()
                        .filter((oLink) => oLink.target)
                        .map((oLink) => oLink.target)) ||
                    []
                  );
                },
              },
              /**
               * We can compute the predecessors as the inverse of successor
               */
              predecessorNodes: {
                contentType: "sap.cdw.taskchainmodeler.Node",
                isMany: true,
                isContainment: false,
                isComputed: true,
                isCached: false,
                isVolatile: true,
                isInternal: true,
                isSupportNotification: true,
                get: function () {
                  if (nsLocal.ModelImpl.isDeleting) {
                    // in case of deleting we use resource context
                    return (
                      (this.resource &&
                        this.resource.objects &&
                        this.resource.objects
                          .selectAllObjects({
                            "target.objectId": this.objectId,
                          })
                          .toArray()
                          .map((oLink) => oLink.source)) ||
                      []
                    );
                  }
                  return (
                    this.resource?.model?.links
                      ?.selectAllObjects({
                        "target.objectId": this.objectId,
                      })
                      .toArray()
                      .map((oLink) => oLink.source) || []
                  );
                },
              },
              /**
               * We can compute the all predecessors as the inverse of successor
               */
              allPredecessorNodes: {
                contentType: "sap.cdw.taskchainmodeler.Node",
                isMany: true,
                isContainment: false,
                isComputed: true,
                isCached: false,
                isVolatile: true,
                isInternal: true,
                isSupportNotification: true,
                get: function () {
                  const allObjects = [];
                  function getNodes(obj) {
                    let objects;
                    if (nsLocal.ModelImpl.isDeleting) {
                      // in case of deleting we use resource context
                      objects =
                        obj?.resource?.objects
                          .selectAllObjects({
                            "target.objectId": obj.objectId,
                          })
                          .toArray()
                          .map((oLink) => oLink.source) || [];
                    }
                    objects =
                      obj?.container?.links
                        ?.selectAllObjects({
                          "target.objectId": obj.objectId,
                        })
                        .toArray()
                        .map((oLink) => oLink.source) || [];
                    if (objects) {
                      for (let i = 0; i < objects.length; i++) {
                        if (!allObjects.includes(objects[i])) {
                          allObjects.push(objects[i]);
                          getNodes(objects[i]);
                        }
                      }
                    }
                    return allObjects;
                  }
                  return getNodes(this);
                },
              },

              /**
               * A node has at least one successor (0 for the last node)
               */
              connectedNodes: {
                contentType: "sap.cdw.taskchainmodeler.Node",
                isMany: true,
                isContainment: false,
                isComputed: true,
                isCached: false,
                isVolatile: true,
                isInternal: true,
                isSupportNotification: true,
                get: function () {
                  return [...this.predecessorNodes, ...this.successorNodes];
                },
              },
              /**
               * An abstract Node class that contains links
               */
              outputs: {
                contentType: "sap.cdw.taskchainmodeler.Output",
                isMany: true,
                isContainment: true,
              },
            },
            methods: {
              onBeforeDelete: function () {
                this.resource.model.hasSuccessorNodesBeforeDelete = this.successorNodes.length !== 0;
                Utils.removeObjectFromChangeManagement(this.resource.model, this);
                nsLocal.ModelImpl.onCascadeDeleteNode(this);
              },
              clearValidation: function () {
                this.resource.applyUndoableAction(
                  function () {
                    if (this.validations && typeof this.validations.clear === "function") {
                      this.validations.clear();
                    }
                  }.bind(this),
                  "Clear validations",
                  /* protectedFromUndo */ true
                );
              },
              onAfterDelete: function () {
                this.resource.model.validate();
                if (!this.resource.model.hasSuccessorNodesBeforeDelete) {
                  nsLocal.ModelImpl.checkBeginPlaceHolder(this.resource.model);
                }
                this.resource.model.hasSuccessorNodesBeforeDelete = false;
              },
            },
          },

          /**
           * @class
           * Task class for all task objects(RemoteTable, View, DataFlow)
           */
          Task: {
            displayName: "Task",
            parent: "sap.cdw.taskchainmodeler.Node",
            properties: {
              qualifiedName: {
                dataType: sap.galilei.model.dataTypes.gString,
                get: function () {
                  return this.name;
                },
              },
              displayName: {
                dataType: sap.galilei.model.dataTypes.gString,
                get: function () {
                  switch (this.rootContainer.objectNameDisplay) {
                    case ObjectNameDisplay.businessName:
                      return this.label || this.name;
                    default:
                      return this.name;
                  }
                },
              },
              label: {
                dataType: sap.galilei.model.dataTypes.gString,
                available: [commonAvailableChecks.onlyIfPrivileges],
              },
              sourceEntityName: {
                dataType: sap.galilei.model.dataTypes.gString,
                get: function () {
                  return nsLocal.ModelImpl.computeSourceEntityName(this);
                },
              },
              icon: {
                dataType: sap.galilei.model.dataTypes.gString,
              },
              taskType: {
                dataType: sap.galilei.model.dataTypes.gInteger,
                get: function () {
                  return Utils.getTaskType(this);
                },
              },
              isTable: {
                dataType: sap.galilei.model.dataTypes.gBool,
                get: function () {
                  return this.isRemoteTable;
                },
              },
              elementCount: {
                dataType: sap.galilei.model.dataTypes.gInteger,
              },
              isOperator: {
                dataType: sap.galilei.model.dataTypes.gBool,
                get: function () {
                  return false;
                },
              },
              isTask: {
                dataType: sap.galilei.model.dataTypes.gBool,
                get: function () {
                  return true;
                },
              },
              numberOfRetentionDays: {
                dataType: sap.galilei.model.dataTypes.gInteger,
                defaultValue: 90,
              },
              retentionOptionSelected: {
                dataType: sap.galilei.model.dataTypes.gBool,
                defaultValue: true,
              },
              defaultApacheSelected: {
                dataType: sap.galilei.model.dataTypes.gBool,
                defaultValue: true,
              },
              customApacheValueSelected: {
                dataType: sap.galilei.model.dataTypes.gInteger,
                defaultValue: 0,
              },
              customApacheValueMerge: {
                dataType: sap.galilei.model.dataTypes.gString,
                defaultValue: "",
              },
              customApacheValueVacuum: {
                dataType: sap.galilei.model.dataTypes.gString,
                defaultValue: "",
              },
              customApacheValueOptimize: {
                dataType: sap.galilei.model.dataTypes.gString,
                defaultValue: "",
              },
              customApacheValueTransformation: {
                dataType: sap.galilei.model.dataTypes.gString,
                defaultValue: "",
              },
              applicationDefault: {
                dataType: sap.galilei.model.dataTypes.gString,
                defaultValue: "",
              },
              selectedActivityIndex: {
                dataType: sap.galilei.model.dataTypes.gInteger,
                defaultValue: 0,
              },
              isBWProcessChainDeleted: {
                dataType: sap.galilei.model.dataTypes.gBool,
                get: function () {
                  return false;
                },
              },
              isBWProcessChainError: {
                dataType: sap.galilei.model.dataTypes.gBool,
                get: function () {
                  return false;
                },
              },
              isConnectionListEmpty: {
                dataType: sap.galilei.model.dataTypes.gBool,
                get: function () {
                  return this.connections?.length === 0;
                },
              },
              isConnectionHasError: {
                dataType: sap.galilei.model.dataTypes.gBool,
                defaultValue: false,
              },
              ...taskTypeProperties,
              ...repositoryObjectProperties,
              ...RESTAPI_Properties,
              ...notificationProperties,
            },
            methods: {
              ...sharedModelMethods.repositoryObjectMethods,

              // @overrride
              validate: function (): any {
                return nsLocal.Validation.validateTask(this);
              },
            },

            references: {
              ...taskReferences,
              ...REST_API_References,
            },
          },

          /**
           * @class
           * Operation class for all operations(AND, OR)
           */
          Operation: {
            displayName: "Operation",
            parent: "sap.cdw.taskchainmodeler.Node",
            properties: {
              operationType: {
                dataType: sap.galilei.model.dataTypes.gString,
              },
              isOperator: {
                dataType: sap.galilei.model.dataTypes.gBool,
                get: function () {
                  return true;
                },
              },
            },
            methods: {
              // @overrride
              validate: function (): any {
                return nsLocal.Validation.validateOperator(this);
              },
            },
          },

          /**
           * @class
           * Link links two nodes
           */
          Link: {
            displayName: "Link",
            parent: "sap.cdw.commonmodel.BaseLinkObject",
            properties: {
              type: {
                dataType: sap.galilei.model.dataTypes.gString,
                defaultValue: "None",
              },
              statusRequired: {
                dataType: sap.galilei.model.dataTypes.gString,
                defaultValue: OutputType.COMPLETED,
              },
              displayName: {
                dataType: sap.galilei.model.dataTypes.gString,
              },
              sourcePosition: {
                dataType: sap.galilei.model.dataTypes.gInteger,
                defaultValue: -1,
              },
              targetPosition: {
                dataType: sap.galilei.model.dataTypes.gInteger,
                defaultValue: -1,
              },
              isPlaceHolder: {
                dataType: sap.galilei.model.dataTypes.gBool,
                get: function () {
                  return this.target ? !(this.target?.isTask || this.target?.isOperator) : true;
                },
              },
              isParameter: {
                dataType: sap.galilei.model.dataTypes.gBool,
                get: function () {
                  return this.target ? !(this.target?.isTask || this.target?.isOperator) : true;
                },
              },
            },
            references: {
              source: {
                contentType: "sap.cdw.taskchainmodeler.Node",
                isMany: false,
                isContainment: false,
              },
              target: {
                contentType: "sap.cdw.taskchainmodeler.Node",
                isMany: false,
                isContainment: false,
              },
            },
            methods: {
              onBeforeDelete: function () {
                // nsLocal.ModelImpl.onCascadeDeleteLink(this);
                // Utils.removeObjectFromChangeManagement(this.resource.model, this);
              },
              validate: function (bSkipRefreshDecorator) {
                // return nsLocal.Validation.validateLink(this, bSkipRefreshDecorator);
              },
              onAfterDelete: function () {
                this.resource.model.validate();
                if (!this.resource.model.hasSuccessorNodesBeforeDelete) {
                  nsLocal.ModelImpl.checkBeginPlaceHolder(this.resource.model);
                }
              },
            },
          },

          /**
           * @class
           * output object for task execution result type.
           */
          Output: {
            displayName: "Output",
            parent: "sap.galilei.model.Object",
            properties: {
              statusRequired: {
                dataType: sap.galilei.model.dataTypes.gString,
                default: OutputType.COMPLETED,
              },
              fill: {
                dataType: sap.galilei.model.dataTypes.gString,
              },
            },
          },

          /**
           * @class
           * start object is the start of the task chain (virtual node).
           */
          Start: {
            displayName: "Begin",
            parent: "sap.cdw.taskchainmodeler.Node",
            properties: {
              name: {
                dataType: sap.galilei.model.dataTypes.gString,
              },
              isStart: {
                dataType: sap.galilei.model.dataTypes.gBool,
                get: function () {
                  return true;
                },
              },
            },
          },

          /**
           * @class
           * PlaceHolder object for placeholder symbol.
           */
          PlaceHolder: {
            displayName: "PlaceHolder",
            parent: "sap.galilei.model.Object",
            properties: {
              name: {
                dataType: sap.galilei.model.dataTypes.gString,
              },
              isTask: {
                dataType: sap.galilei.model.dataTypes.gBool,
                get: function () {
                  return false;
                },
              },
              isOperator: {
                dataType: sap.galilei.model.dataTypes.gBool,
                get: function () {
                  return false;
                },
              },
            },
          },
          /**
           * @class
           * Parameter
           */
          Parameter: {
            displayName: "Parameter",
            parent: "sap.cdw.commonmodel.BaseElement",
            properties: {
              // Override
              default: {
                dataType: sap.galilei.model.dataTypes.gString,
              },
              // override to store constant value information during mapping
              defaultValue: {
                dataType: sap.galilei.model.dataTypes.gString,
                onCascadeChange: function (oEventArgs) {},
              },
              value: {
                dataType: sap.galilei.model.dataTypes.gString,
              },
              isParameterModified: {
                dataType: sap.galilei.model.dataTypes.gBool,
                get: function () {
                  return false;
                },
              },
              ...sharedModelProperties.dataTypeProperties,
            },
          },
          /**
           * @class
           * InvokeAPIRequest
           */
          InvokeAPIRequest: {
            displayName: "InvokeAPIRequest",
            parent: "sap.galilei.model.Object",
            references: {
              request: {
                contentType: "sap.cdw.taskchainmodeler.APIRequest",
                isMany: false,
                isContainment: true,
              },
              response: {
                contentType: "sap.cdw.taskchainmodeler.APIResponse",
                isMany: false,
                isContainment: true,
              },
            },
          },

          /**
           * @class
           * StatusAPIRequest
           */
          StatusAPIRequest: {
            displayName: "StatusAPIRequest",
            parent: "sap.galilei.model.Object",
            references: {
              request: {
                contentType: "sap.cdw.taskchainmodeler.APIRequest",
                isMany: false,
                isContainment: true,
              },
              response: {
                contentType: "sap.cdw.taskchainmodeler.APIResponse",
                isMany: false,
                isContainment: true,
              },
            },
          },

          /**
           * @class
           * LogsAPIRequest
           */
          LogsAPIRequest: {
            displayName: "LogsAPIRequest",
            parent: "sap.galilei.model.Object",
            references: {
              request: {
                contentType: "sap.cdw.taskchainmodeler.APIRequest",
                isMany: false,
                isContainment: true,
              },
              response: {
                contentType: "sap.cdw.taskchainmodeler.APIResponse",
                isMany: false,
                isContainment: true,
              },
            },
          },

          /**
           * @class
           * RequestHeader
           */
          RequestHeader: {
            displayName: "RequestHeader",
            parent: "sap.galilei.model.Object",
            properties: {
              ...REST_API_Header,
            },
          },

          /**
           * @class
           * APIRequest
           */
          APIRequest: {
            displayName: "APIRequest",
            parent: "sap.galilei.model.Object",
            properties: {
              ...REST_API_ReqProperties,
            },
          },

          /**
           * @class
           * APIResponse
           */
          APIResponse: {
            displayName: "APIResponse",
            parent: "sap.galilei.model.Object",
            properties: {
              ...REST_API_ResProperties,
            },
          },
        },
      },
    },
  };

  const oResource = new sap.galilei.model.Resource("sap.cdw.taskchainmodeler.model");
  const oReader = new sap.galilei.model.JSONReader();
  oReader.load(oResource, expandModelDefinition(oModelDef));
});
