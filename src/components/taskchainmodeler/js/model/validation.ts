/**
 * Copyright 2020 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { SupportedFeaturesService } from "../../../commonmodel/api/SupportedFeaturesService";
import Utils from "../utils";

sap.galilei.namespace("sap.cdw.taskchainmodeler", function () {
  "use strict";
  const nsLocal = sap.cdw.taskchainmodeler;
  const nsValidationStatus = sap.cdw.commonmodel.ValidationStatus;
  const nsCommonValidation = sap.cdw.commonmodel.Validation;
  const sMessageGroupId = "i18n_tc";
  const i18nModel = new sap.ui.model.resource.ResourceModel({
    bundleName: require("../../i18n/i18n.properties"),
  });
  const oBundle = i18nModel.getResourceBundle();

  /**
   * @class
   * Validation implements all methods related to the validation of model objects
   */
  sap.cdw.taskchainmodeler.Validation = sap.galilei.core.defineClass({
    fullClassName: "sap.cdw.taskchainmodeler.Validation",

    statics: {
      // Avoid infinit loop in validateModel()
      isValidatingModel: false,

      getAggregatedValidation: function (oObject, aChildren) {
        return nsCommonValidation.getAggregatedValidation(oObject, aChildren);
      },

      validateModel: async function (oModel: sap.cdw.taskchainmodeler.Model): Promise<any> {
        this.doValidateModel(oModel);
      },

      doValidateModel: async function (oModel: sap.cdw.taskchainmodeler.Model): Promise<any> {
        if (!oModel) {
          return;
        }
        // eslint-disable-next-line @typescript-eslint/no-this-alias
        const self = this;
        oModel.clearValidation();
        oModel.resource.applyUndoableAction(
          async function () {
            if ((oModel as any)?.packageStatus?.length > 0) {
              const msg: any = nsValidationStatus.createWarnInstance(
                oModel,
                sMessageGroupId,
                (oModel as any)?.packageStatus,
                [oModel.name, (oModel as any).packageValue]
              );
              msg.resource.applyUndoableAction(
                function () {
                  msg.isExternal = true;
                },
                "isExternal",
                true
              );
            }

            if (oModel?.isBWProcessChainError && oModel?.error !== undefined) {
              nsValidationStatus.createErrorInstance(
                oModel,
                sMessageGroupId,
                oModel.error[0].responseJSON.code,
                [oModel.name],
                undefined,
                undefined,
                oModel.error[0].responseJSON?.details?.message
              );
            }

            if (oModel.changeManagement && oModel.changeManagement?.modifiedObjects) {
              const deletedProcedureNodes = [];
              const deletedBWNodes = [];

              for (const modifiedObjectsItem of oModel.changeManagement.modifiedObjects) {
                for (const autoFixObjectsItem of modifiedObjectsItem.autofixedObjects) {
                  // if procedure node is deleted
                  if (autoFixObjectsItem.changeInfo.procedureDeletedNodes.length > 0) {
                    let i = 1;
                    for (const deleteNode of autoFixObjectsItem.changeInfo.procedureDeletedNodes) {
                      deletedProcedureNodes.push("\n" + "\t" + i + ". " + deleteNode.elementName);
                      i++;
                    }
                  }
                  // if BW node is deleted
                  if (autoFixObjectsItem.changeInfo.bwDeletedNodes.length > 0) {
                    let i = 1;
                    for (const deletedNode of autoFixObjectsItem.changeInfo.bwDeletedNodes) {
                      deletedBWNodes.push("\n" + "\t" + i + ". " + deletedNode.elementName);
                      i++;
                    }
                  }
                }
              }
              if (deletedProcedureNodes.length > 0) {
                nsValidationStatus.createErrorInstance(oModel, sMessageGroupId, "VAL_DELETED_PROCEDURE", [
                  deletedProcedureNodes,
                ]);
              }
              if (deletedBWNodes.length > 0) {
                nsValidationStatus.createErrorInstance(oModel, sMessageGroupId, "VAL_DELETED_BW", [deletedBWNodes]);
              }
            }

            oModel.hasUnconnectedNodes = false; // reset the value before validating the nodes
            for (let nIndex = 0; nIndex < oModel.nodes.length; nIndex++) {
              const oNode = oModel.nodes.get(nIndex);
              if (oNode.validate instanceof Function) {
                oNode.clearValidation();
              }
            }

            if (oModel.changeManagement) {
              for (const modifiedObjectsItem of oModel.changeManagement?.modifiedObjects) {
                for (const autoFixObjectsItem of modifiedObjectsItem.autofixedObjects) {
                  // new parameter added
                  if (autoFixObjectsItem.changeInfo.newColumns.length > 0) {
                    const newParam = [];
                    let node, nodeName;
                    let i = 1;
                    const len = autoFixObjectsItem.changeInfo.newColumns.length;
                    for (const newColumn of autoFixObjectsItem.changeInfo.newColumns) {
                      if (len >= 2 && i === len) {
                        newParam.push(" " + newColumn.parameterName);
                        node = newColumn.element;
                        nodeName = newColumn.elementName;
                      } else {
                        newParam.push(newColumn.parameterName);
                        i++;
                        if (len === 1) {
                          node = newColumn.element;
                          nodeName = newColumn.elementName;
                        }
                      }
                    }
                    nsValidationStatus.createWarnInstance(node, sMessageGroupId, "VAL_NEW_PARAMETER_ADDED", [
                      newParam,
                      nodeName,
                    ]);
                  }
                  // parameter deleted
                  if (autoFixObjectsItem.changeInfo.deletedColumns.length > 0) {
                    const paramDeleted = [];
                    let node, nodeName;
                    let i = 1;
                    const len = autoFixObjectsItem.changeInfo.deletedColumns.length;
                    for (const deletedColumn of autoFixObjectsItem.changeInfo.deletedColumns) {
                      if (len >= 2 && i === len) {
                        paramDeleted.push(" " + deletedColumn.parameterName);
                        node = deletedColumn.element;
                        nodeName = deletedColumn.elementName;
                      } else {
                        paramDeleted.push(deletedColumn.parameterName);
                        i++;
                        if (len === 1) {
                          node = deletedColumn.element;
                          nodeName = deletedColumn.elementName;
                        }
                      }
                    }
                    nsValidationStatus.createWarnInstance(node, sMessageGroupId, "VAL_PARAMETER_DELETED", [
                      paramDeleted,
                      nodeName,
                    ]);
                  }
                  // parameter modified
                  if (autoFixObjectsItem.changeInfo.updatedDatatypeColumns.length > 0) {
                    for (const updatedDatatypeColumn of autoFixObjectsItem.changeInfo.updatedDatatypeColumns) {
                      if (updatedDatatypeColumn.old.dataType !== updatedDatatypeColumn.new.dataType) {
                        const param = [
                          updatedDatatypeColumn.parameterName,
                          updatedDatatypeColumn.old.displayType,
                          updatedDatatypeColumn.new.displayType,
                          updatedDatatypeColumn.elementName,
                        ];
                        nsValidationStatus.createWarnInstance(
                          updatedDatatypeColumn.element,
                          sMessageGroupId,
                          "VAL_PARAMETER_DATATYPE_MODIFIED",
                          param
                        );
                      } else if (updatedDatatypeColumn.old.length !== updatedDatatypeColumn.new.length) {
                        const param = [
                          updatedDatatypeColumn.parameterName,
                          updatedDatatypeColumn.old.length,
                          updatedDatatypeColumn.new.length,
                          updatedDatatypeColumn.elementName,
                        ];
                        nsValidationStatus.createWarnInstance(
                          updatedDatatypeColumn.element,
                          sMessageGroupId,
                          "VAL_PARAMETER_LENGTH_MODIFIED",
                          param
                        );
                      } else if (updatedDatatypeColumn.old.precision !== updatedDatatypeColumn.new.precision) {
                        const param = [
                          updatedDatatypeColumn.parameterName,
                          updatedDatatypeColumn.old.precision,
                          updatedDatatypeColumn.new.precision,
                          updatedDatatypeColumn.elementName,
                        ];
                        nsValidationStatus.createWarnInstance(
                          updatedDatatypeColumn.element,
                          sMessageGroupId,
                          "VAL_PARAMETER_PRECISION_MODIFIED",
                          param
                        );
                      } else if (updatedDatatypeColumn.old.scale !== updatedDatatypeColumn.new.scale) {
                        const param = [
                          updatedDatatypeColumn.parameterName,
                          updatedDatatypeColumn.old.scale,
                          updatedDatatypeColumn.new.scale,
                          updatedDatatypeColumn.elementName,
                        ];
                        nsValidationStatus.createWarnInstance(
                          updatedDatatypeColumn.element,
                          sMessageGroupId,
                          "VAL_PARAMETER_SCALE_MODIFIED",
                          param
                        );
                      }
                    }
                  }
                }
              }
            }

            for (let nIndex = 0; nIndex < oModel.nodes.length; nIndex++) {
              const oNode = oModel.nodes.get(nIndex);
              if (oNode.validate instanceof Function) {
                if (oNode.isTask) {
                  nsLocal.Validation.validateTask(oNode, undefined, true);
                } else {
                  nsLocal.Validation.validateOperator(oNode, undefined, true);
                }
              }
            }

            if ((oModel as any).startNode) {
              nsLocal.Validation.checkParallelNodes((oModel as any).startNode);
            }

            for (let nIndex = 0; nIndex < oModel.links.length; nIndex++) {
              const oLink = oModel.links.get(nIndex);
              if (oLink.validate instanceof Function) {
                oLink.validate(/* bSkipRefreshDecorators*/ true);
              }
            }

            if (oModel.nodes.length) {
              oModel.isEmpty = false;
            } else {
              oModel.isEmpty = true;
              const sResult = "VAL_MODEL_EMPTY";
              nsValidationStatus.createErrorInstance(oModel, sMessageGroupId, sResult);
            }

            nsLocal.Validation.requestRefreshDecorators(oModel);
          },
          "New Validation",
          /* bIsprotectedFromUndo*/ true
        );
      },

      requestRefreshDecorators: function (oModel) {
        if (!oModel) {
          return;
        }
        if (typeof sap !== "undefined" && sap.ui && sap.ui.getCore instanceof Function) {
          sap.ui
            .getCore()
            .getEventBus()
            .publish(nsCommonValidation.VALIDATION_CHANNEL, nsCommonValidation.REFRESH_VALIDATION_EVENT, {
              model: oModel,
            });
        }
      },

      validateTask: function (
        oNode: sap.cdw.taskchainmodeler.Node,
        bSkipRefreshDecorators,
        bSkipClear,
        bSkipNoAttributes,
        bSkipDuplicate
      ) {
        let sResult = "ok";
        const oFeatures = sap.ui.getCore().getModel("featureflags").getData();
        if (oNode) {
          if (bSkipClear !== true) {
            oNode.clearValidation();
          }
          if (oNode.notChainableMsg?.messageBundleKey === "REALTIME_REPLICATION_ENABLED") {
            const activity = Utils.getActivity(oNode);
            if (activity === "REPLICATE") {
              nsValidationStatus.createWarnInstance(
                oNode,
                oNode.notChainableMsg.messageBundleId,
                "NOT_CHAINABLE_REMOTETABLE",
                [oNode.name],
                undefined,
                undefined,
                oNode.notChainableMsg.messageBundleKey,
                oNode.notChainableMsg.parameters || []
              );
            } else if (activity === "REMOVE_REPLICATED_DATA") {
              nsValidationStatus.createWarnInstance(
                oNode,
                sMessageGroupId,
                "REPLICATION_TYPE_CHANGED",
                [oNode.name],
                undefined,
                undefined,
                "VAL_REMOVE_REPLICATED_DATA"
              );
            }
          }
          if ((oNode.isChainable === false || oNode.isChainable === undefined) && oNode.notChainableMsg) {
            if (oNode.notChainableMsg.messageBundleKey !== "VIEW_PERSISTENCY_VIEW_NOT_EXISTING") {
              nsValidationStatus.createErrorInstance(
                oNode,
                oNode.notChainableMsg.messageBundleId,
                "NOT_CHAINABLE",
                [oNode.name],
                undefined,
                undefined,
                oNode.notChainableMsg.messageBundleKey,
                oNode.notChainableMsg.parameters || []
              );
            }
          }

          if (oNode.predecessorNodes?.length === 0 && oNode.successorNodes?.length === 0) {
            nsValidationStatus.createErrorInstance(oNode, sMessageGroupId, "VAL_UNCONNECTED_TASK", [oNode.name]);
          } else if (oNode.predecessorNodes?.length === 0 && !oNode.isStart) {
            nsValidationStatus.createErrorInstance(oNode, sMessageGroupId, "VAL_TASK_SHOULD_HAVE_INCOMING_LINK", [
              oNode.name,
            ]);
          } else if (oNode.isDanglingBranch) {
            nsValidationStatus.createErrorInstance(oNode, sMessageGroupId, "VAL_UNCONNECTED_BRANCH", [oNode.name]);
          }
          if (oNode.predecessorNodes?.length > 1) {
            nsValidationStatus.createErrorInstance(oNode, sMessageGroupId, "VAL_TASK_SHOULD_HAVE_ONE_INCOMING_LINK", [
              oNode.name,
            ]);
          }
          nsLocal.Validation.checkParallelNodes(oNode);

          if (
            oNode["#objectStatus"] !== undefined &&
            Number(oNode["#objectStatus"]) === 0 &&
            !(
              oNode.isSQLScriptProcedure === true ||
              oNode.isBWProcessChain === true ||
              oNode.isRestApi === true ||
              oNode.isNotificationTask === true
            )
          ) {
            nsValidationStatus.createErrorInstance(oNode, sMessageGroupId, "VAL_NOT_DEPLOYED", [oNode.name]);
          }
          if (oNode["#objectStatus"] !== undefined && Number(oNode["#objectStatus"]) === 3) {
            nsValidationStatus.createWarnInstance(oNode, sMessageGroupId, "VAL_DESIGNTIME_ERROR", [oNode.name]);
          }
          if (oNode["#objectStatus"] !== undefined && Number(oNode["#objectStatus"]) === 4) {
            nsValidationStatus.createWarnInstance(oNode, sMessageGroupId, "VAL_RUNTIME_ERROR", [oNode.name]);
          }
          if (oNode.isSQLScriptProcedure === true) {
            // check if the SQL Script Procedure parameters has value or not

            if (oNode.parameters?.length > 0) {
              let isParameterMissing = false;
              const parameterName = [];
              oNode.parameters.forEach((param) => {
                if (!param?.value) {
                  isParameterMissing = true;
                  parameterName.push(param.name);
                }
              });
              if (isParameterMissing) {
                nsValidationStatus.createErrorInstance(
                  oNode,
                  sMessageGroupId,
                  "VAL_SQL_SCRIPT_PROCEDURE_PARAMETER_VALUE_MISSING",
                  [oNode.name, parameterName]
                );
              }
            }
          }

          if (oNode.isRestApi === true) {
            this.validateApiTask(oNode);
          }

          if (oNode.isNotificationTask === true) {
            if (oNode.technicalNameValueState === "Error" || oNode.technicalNameValueState === "Warning") {
              nsValidationStatus.createErrorInstance(oNode, sMessageGroupId, "VAL_NOTIFICATION_TASK_TECHNICAL_NAME", [
                oNode.name,
              ]);
            }
            if (
              oNode.resource?.model?.deploymentStatus !== undefined &&
              Number(oNode.resource?.model?.deploymentStatus) !== 0 &&
              oNode.resource?.model?.isEmailRecipientEmpty === true
            ) {
              nsValidationStatus.createErrorInstance(
                oNode,
                sMessageGroupId,
                "VAL_NOTIFICATION_TASK_EMPTY_RECIPIENT_LIST",
                [oNode.name]
              );
            }
          }
          nsLocal.Validation.checkCircularValidation(oNode);

          if (!bSkipRefreshDecorators) {
            nsLocal.Validation.requestRefreshDecorators(oNode.container);
          }
        }
        return sResult;
      },

      validateOperator: function (
        oNode: sap.cdw.taskchainmodeler.Node,
        bSkipRefreshDecorators,
        bSkipClear,
        bSkipNoAttributes,
        bSkipDuplicate
      ) {
        let sResult = "ok";
        const oFeatures = sap.ui.getCore().getModel("featureflags").getData();
        if (oNode) {
          if (bSkipClear !== true) {
            oNode.clearValidation();
          }

          if (oNode.predecessorNodes?.length === 0 && oNode.successorNodes?.length === 0) {
            nsValidationStatus.createErrorInstance(oNode, sMessageGroupId, "VAL_UNCONNECTED_OPERATOR", [oNode.name]);
          } else if (oNode.isDanglingBranch) {
            nsValidationStatus.createErrorInstance(oNode, sMessageGroupId, "VAL_UNCONNECTED_BRANCH", [oNode.name]);
          }
          if (oNode.predecessorNodes?.length < 2) {
            nsValidationStatus.createErrorInstance(
              oNode,
              sMessageGroupId,
              "VAL_OPERATOR_SHOULD_HAVE_ATLEAST_TWO_INCOMING_LINK",
              [oNode.name]
            );
          }
          let bSuccessorNodes = false;
          for (let pIndex = 0; pIndex < oNode.successorNodes.length; pIndex++) {
            const oSource = oNode.successorNodes[pIndex];
            if (oSource.isTask || oSource.isOperator) {
              bSuccessorNodes = true;
              break;
            }
          }
          if (!bSuccessorNodes) {
            nsValidationStatus.createErrorInstance(oNode, sMessageGroupId, "VAL_OPERATOR_SHOULD_HAVE_ONE_OUT_LINK", [
              oNode.name,
            ]);
          }
          nsLocal.Validation.checkParallelNodes(oNode);
          nsLocal.Validation.checkCircularValidation(oNode);

          if (!bSkipRefreshDecorators) {
            nsLocal.Validation.requestRefreshDecorators(oNode.container);
          }
        }
        return sResult;
      },

      checkCircularValidation: function (oNode: sap.cdw.taskchainmodeler.Node) {
        let { bCircularExists, foundNode } = nsLocal.Validation._checkCircularValidation(oNode, [oNode]);
        if (bCircularExists) {
          nsValidationStatus.createErrorInstance(oNode, sMessageGroupId, "VAL_CIRCULAR_REFERENCE_EXITS", [
            foundNode.name,
          ]);
        }
      },

      _checkCircularValidation: function (oParentNode: sap.cdw.taskchainmodeler.Node, nodesTraversed) {
        let bCircularExists;
        let foundNode;
        if (oParentNode?.successorNodes?.length) {
          const aSuccessorNodes = oParentNode?.successorNodes;
          for (let nIndex = 0; nIndex < aSuccessorNodes.length; nIndex++) {
            const oNode = aSuccessorNodes[nIndex];
            if (nodesTraversed.includes(oNode) && !oNode.isOperator) {
              bCircularExists = true;
              foundNode = oNode;
              break;
            }
          }
          if (!bCircularExists) {
            nodesTraversed = [...nodesTraversed, ...aSuccessorNodes.map((o) => o)];
            for (let nIndex = 0; nIndex < aSuccessorNodes.length; nIndex++) {
              const oNode = aSuccessorNodes[nIndex];
              let result = nsLocal.Validation._checkCircularValidation(oNode, nodesTraversed);
              bCircularExists = result.bCircularExists;
              if (bCircularExists) {
                foundNode = result.foundNode;
                break;
              }
            }
          }
        }
        return { bCircularExists, foundNode };
      },

      checkParallelNodes: function (oNode: sap.cdw.taskchainmodeler.Node) {
        if (oNode.successorNodes.length > 1) {
          let objectNames = [];
          let aObjects = {};
          let aObjectsValidated = {};
          oNode.successorNodes.map((o) => {
            if (o.isTask) {
              const applicationID = Utils.getApplicationId(o);
              const key = `${o.name}_${applicationID}`;
              if (objectNames.includes(key)) {
                const oldNode = aObjects[key];
                if (!aObjectsValidated[key]) {
                  nsValidationStatus.createWarnInstance(
                    oldNode,
                    sMessageGroupId,
                    "VAL_TASK_TRIGGERED_PARALLEL_MORE_THAN_ONE_TIME",
                    [oldNode.name]
                  );
                  aObjectsValidated[key] = true;
                }
                nsValidationStatus.createWarnInstance(
                  o,
                  sMessageGroupId,
                  "VAL_TASK_TRIGGERED_PARALLEL_MORE_THAN_ONE_TIME",
                  [o.name]
                );
              } else {
                objectNames.push(key);
                aObjects[key] = o;
                aObjectsValidated[key] = false;
              }
            }
          });
        }
      },

      validateApiTask: function (oNode: sap.cdw.taskchainmodeler.Node) {
        if (oNode.connectionNameValueState === "Error") {
          if (
            oNode.resource?.model?.connectionList &&
            oNode.resource?.model?.connectionList.find((connection) => connection.name === oNode.connectionName) ===
              undefined
          ) {
            nsValidationStatus.createErrorInstance(oNode, sMessageGroupId, "VAL_REST_API_CONNECTION_DELETED", [
              oNode.name,
              oNode.connectionName,
            ]);
          } else {
            nsValidationStatus.createErrorInstance(oNode, sMessageGroupId, "VAL_REST_API_CONNECTION", [oNode.name]);
          }
        }

        if (oNode.connectionNameValueState === "Warning") {
          nsValidationStatus.createErrorInstance(
            oNode,
            sMessageGroupId,
            "VAL_REST_API_CONNECTION_CONSUMPTION_WARNING",
            [oNode.name, oNode.connectionName]
          );
        }

        const properties = [];
        let flag;
        this.index = 0;
        if (oNode?.technicalNameValueState === "Error") {
          this.index++;
          properties.push("\n\t" + this.index + ". " + oBundle.getText("@lblTechnicalName"));
        }
        if (oNode?.invokeAPI) {
          flag = "invokeAPI";
          this.validateRequest(oNode, properties, flag);
          if (oNode?.invokeAPI?.response.from === "BODY") {
            this.validateResponse(oNode, properties, flag);
          }
        }
        if (oNode?.statusAPI && oNode?.invokeAPI?.request.mode === "ASYNC") {
          flag = "statusAPI";
          if (oNode?.invokeAPI?.response.from === "BODY") {
            this.validateRequest(oNode, properties, flag);
          }
          this.validateResponse(oNode, properties, flag);
        }

        if (properties.length > 0) {
          nsValidationStatus.createErrorInstance(oNode, sMessageGroupId, "VAL_REST_API_PROPERTIES_MISSING", [
            oNode.name,
            properties,
          ]);
        }
      },

      validateRequest: function (oNode, properties, flag) {
        const panel = flag === "invokeAPI" ? "@lblInvoke" : "@lblStatus";
        const obj = oNode[flag];
        if (obj?.request?.apiPathValueState === "Error") {
          this.index++;
          properties.push("\n\t" + this.index + ". " + oBundle.getText(panel) + " - " + oBundle.getText("@lblAPIPath"));
        }
        if (obj?.request?.csrfToken === true && obj?.request?.csrfTokenUrlValueState === "Error") {
          this.index += 1;
          properties.push("\n\t" + this.index + ". " + oBundle.getText(panel) + " - " + oBundle.getText("@lblCSRF"));
        }
        if (obj?.request?.bodyValueState === "Error") {
          this.index += 1;
          properties.push(
            "\n\t" + this.index + ". " + oBundle.getText(panel) + " - " + oBundle.getText("@lblRequestBody")
          );
        }
      },

      validateResponse: function (oNode, properties, flag) {
        const panel = flag === "invokeAPI" ? "@lblInvoke" : "@lblStatus";
        const obj = oNode[flag];
        if (flag === "invokeAPI" && obj.response.from === "BODY" && obj.request.mode === "ASYNC") {
          if (obj?.response?.jobIDPathVS === "Error") {
            this.index += 1;
            properties.push("\n\t" + this.index + ". " + oBundle.getText(panel) + " - " + oBundle.getText("@Id"));
          }
        } else if (obj.response.from === "BODY") {
          if (obj?.response?.successIndicatorPathVS === "Error" || obj?.response?.successIndicatorValueVS === "Error") {
            this.index += 1;
            properties.push(
              "\n\t" + this.index + ". " + oBundle.getText(panel) + " - " + oBundle.getText("@lblSuccessIndicator")
            );
          }
          if (obj?.response?.errorIndicatorPathVS === "Error" || obj?.response?.errorIndicatorValueVS === "Error") {
            this.index += 1;
            properties.push(
              "\n\t" + this.index + ". " + oBundle.getText(panel) + " - " + oBundle.getText("@lblErrorIndicator")
            );
          }
          if (obj?.response?.errorReasonPathVS === "Error") {
            this.index += 1;
            properties.push(
              "\n\t" + this.index + ". " + oBundle.getText(panel) + " - " + oBundle.getText("@lblErrorReason")
            );
          }
        }
      },
    },
  });
});
