/**
 * Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

/* eslint-disable id-blacklist */

import { getObjectChainable } from "../../../../services/metadata";
import * as RepositoryUtils from "../../../abstractbuilder/utility/RepositoryUtils";
import { BusinessCatalogLineageUtility } from "../../../businesscatalogs/utility/BusinessCatalogsUtility";
import { getEntityAndContextNameFromCSN } from "../../../commonmodel/csn/csnUtils";
import { openInNewTab } from "../../../databuilder/utility/DatabuilderHelper";
import { onAnalyzeImpact } from "../../../reuse/control/impactLineage/ImpactLineageDialogEntry";
import { showDialog } from "../../../reuse/utility/UIHelper";
import Utils, { OutputType } from "../utils";

const USAGE_VIEW_BUILDER = "View Builder";

enum UsageActions {
  OPEN_IN_EDITOR = "openInEditor",
  DATA_PREVIEW = "dataPreview",
}

sap.galilei.namespace("sap.cdw.taskchainmodeler", function (nsLocal) {
  /**
   * @class
   * ModelImpl implements all methods related to Model objects
   */
  nsLocal.ModelImpl = sap.galilei.core.defineClass({
    fullClassName: "sap.cdw.taskchainmodeler.ModelImpl",

    statics: {
      UNIQUE_CSN_RESOURCE_ID: "uniqueCsnResource",
      JSONMODEL_CHANNEL: "taskChainModel",
      JSONMODEL_CHANGED_EVENT: "tcModelChanged",
      JSONMODEL_DELETE_OBJECTANDSYMBOLS: "tcDeleteObjectAndSymbols",
      JSONMODEL_LOADED: "tcModelLoaded",
      JSONMODEL_NEWMODEL_EVENT: "newTCModel",
      CREATE_PLACEHOLDER_EVENT: "TCCreatePlaceHolder",
      isDeleting: false,

      /**
       * Gets the task tooltip.
       * @param {*} oObject
       */
      getTaskTooltip: function (oObject) {
        return nsLocal.ModelImpl.getLocalizedText(Utils.getActivityFullText(oObject));
      },

      /**
       * Gets the begin text.
       * @param {*} key
       */
      getLocalizedText: function (key, params) {
        const bundleName = require("../../i18n/i18n.properties");
        const resourceModel = new sap.ui.model.resource.ResourceModel({
          bundleName: bundleName,
        });
        const resourceBundle = resourceModel.getResourceBundle();
        return resourceBundle.getText(key, params);
      },

      /**
       * Sets the default name and display name.
       * @function
       * @name setDefaultObjectName
       * @memberOf sap.galilei.ui.editor.DiagramEditorExtension#
       * @param {Object} oObject The object.
       */
      setDefaultObjectName: function (oObject) {
        if (oObject) {
          const model = oObject.resource.model;
          const oSearch: any = {
            qualifiedClassName: oObject.qualifiedClassName,
          };
          if (model) {
            oSearch.container = model;
          }
          const allClassObjectsOfContainer = oObject.resource.selectAllObjects(oSearch);

          let count = 1,
            name;
          while (oObject.name === undefined) {
            name = `${oObject.classDefinition.displayName} ${count}`;
            const isNameAssigned = allClassObjectsOfContainer.filter((object) => object.name === name).length > 0;
            if (!isNameAssigned) {
              oObject.name = name;
            } else {
              count++;
            }
          }
          if (oObject.displayName === undefined) {
            oObject.displayName = `${oObject.classDefinition.displayName} ${count}`;
          }
        }
      },

      /**
       *
       * @function Post-process the drop action from the catalog tree view
       * @name onDropDataSource
       * @param {*} oTask The task object automatically created
       * @param {*} oDropParams additional information retrieved from the drop action
       */
      onDropDataSource: function (oTask, oDropParams) {
        const oResource = oTask && oTask.resource;
        const oModel = oResource && oResource.model;
        const oDeferred = jQuery.Deferred();
        const nodesLength = oModel.nodes.length;

        oTask.nodeIndex = nodesLength;

        // Initialize entity container
        if (oTask && !oTask.container && oModel) {
          oModel.nodes.push(oTask);
        }

        // Copy file info
        if (oTask && oDropParams) {
          RepositoryUtils.updateObjectFileInfo(oTask, oDropParams);
        }
        const dropDefinitions = oDropParams.definitions || (oDropParams.csn && oDropParams.csn.definitions);
        oTask.repositoryCSN = oDropParams;

        if (oTask && (oTask.isBWProcessChain || oTask.isSQLScriptProcedure || oTask.isOthersTabEnabled)) {
          oTask.name = oDropParams.name;
          oTask.displayName = oDropParams.name;

          if (oTask.isSQLScriptProcedure) {
            nsLocal.ModelImpl.createParameters(oTask, oDropParams.metadata?.parameters);
            const params = nsLocal.ModelImpl.getListOfParametersInSQLScriptProcedure(oTask);
            if (params.length > 0) {
              nsLocal.ModelImpl.showIPPopUP(oTask, params);
            }
          } else if (oTask.isBWProcessChain) {
            oTask.spaceName = oDropParams.spaceId;
          }
        } else if (oTask && oTask.isRestApi) {
          oTask.resource.applyUndoableAction(() => {
            nsLocal.ModelImpl.createRestApi(oTask, oDropParams);
          });
        } else if (oTask && oTask.isNotificationTask) {
          oTask.name = oDropParams.name;
          oTask.displayName = oDropParams.name;
          oTask.applicationId = oDropParams.applicationId;
        } else if (oTask && dropDefinitions && typeof dropDefinitions === "object") {
          const { entityName, contextName } = getEntityAndContextNameFromCSN(dropDefinitions);

          if (entityName && dropDefinitions[entityName]) {
            oTask.name = entityName;
            oTask.displayName = entityName;
            if (dropDefinitions[entityName].then instanceof Function) {
              dropDefinitions[entityName].then((definition) => {
                oTask.resource.applyUndoableAction(
                  () => {
                    dropDefinitions[entityName] = definition;
                    extractDropData(definition);
                    // nsLocal.ModelImpl.publishasyncElementsLoadedEvent({ // needed to refresh the UI
                    //   object: oEntity,
                    // });
                  },
                  "Complete loading entity data",
                  undefined,
                  /* batchMode*/ true
                );
                // await getObjectChainable(this.getSpaceName(), oData.id);
              });
            } else {
              extractDropData(dropDefinitions[entityName], contextName);
            }
          }
        }

        function extractDropData(csnEntity, contextName?) {
          oTask.csn = csnEntity;

          if (csnEntity.elements) {
            oTask.elementCount = Object.keys(csnEntity.elements).length;
          }
        }
        return oDeferred;
      },

      checkObjectChainable: function (oNode, spaceName) {
        try {
          getObjectChainable(spaceName, oNode.name, Utils.getApplicationId(oNode), Utils.getActivity(oNode)).then(
            (result) => {
              oNode.isChainable = result && result?.objectCurrentlyChainable;
              oNode.notChainableMsg = result?.notChainableMessage;
              oNode.resource.model.validate();
            }
          );
        } catch (err) {}
      },

      onPostDropDataSource: function (oNode, oParentNode, oDropOption) {
        const sourceClassName = oNode?.classDefinition?.name;
        const targetClassName = oParentNode?.classDefinition?.name;
        const refreshDiagram = true;

        if (sourceClassName === "Task" && targetClassName === "Task") {
          if (oDropOption?.operation === "Parallel") {
            nsLocal.ModelImpl.addNodeAfter(oParentNode, oNode, oDropOption.operation);
            nsLocal.ModelImpl.createLink(oParentNode, oNode, false /* bUpdateIndexing*/);
          } else if (oDropOption?.operation === "AddNew") {
            nsLocal.ModelImpl.addNodeAfter(oParentNode, oNode, oDropOption.operation);
            nsLocal.ModelImpl.createLink(oParentNode, oNode, false /* bUpdateIndexing*/);
          } else if (oDropOption?.operation === "Replace") {
            nsLocal.ModelImpl.replaceNode(oNode, oParentNode);
          } else {
            const linksAlreadyExist = oNode.resource.model.links.selectAllObjects({
              "source.objectId": oNode.objectId,
              "target.objectId": oParentNode.objectId,
            }).length;
            if (linksAlreadyExist > 0) {
              // Just swap the nodes if already exists
              nsLocal.ModelImpl.updateLinks(oNode, oParentNode, true /* bLinkCreation*/);
              nsLocal.ModelImpl.createLink(oParentNode, oNode, true /* bUpdateIndexing*/);
            } else {
              nsLocal.ModelImpl.updateLinks(oParentNode, oNode, true /* bLinkCreation*/);
              nsLocal.ModelImpl.createLink(oNode, oParentNode, true /* bUpdateIndexing*/);
            }
          }

          // } else if (
          //   sourceClassName === "Task" &&
          //   targetClassName === "PlaceHolderSymbol" &&
          //   targetClassName === "Operation"
          // ) {
        } else if (sourceClassName === "Task" && targetClassName === "PlaceHolderSymbol") {
          // oParentNode is symbol in case of placeholder and not object.
          let oLinkSymbol = oParentNode.getAllLinkSymbolss();
          if (oLinkSymbol?.length) {
            oLinkSymbol = oLinkSymbol[0];
          }
          if (oLinkSymbol && oDropOption.droppedSymbol) {
            oLinkSymbol.targetSymbol = nsLocal.DiagramImpl.getInputSymbol(oDropOption.droppedSymbol);
            nsLocal.ModelImpl.removeIntermediateNode(oDropOption.droppedSymbol.object);
            oLinkSymbol.object.target = oDropOption.droppedSymbol.object;
            oLinkSymbol.drawSymbol();
            if (oLinkSymbol.object.source) {
              oLinkSymbol.object.source.placeHolderNodes = oLinkSymbol.object.source.placeHolderNodes - 1;
              nsLocal.ModelImpl.createOrMergeLink(oLinkSymbol.object.source, oLinkSymbol.object);
            }
            oLinkSymbol.object?.resource?.model?.validate();
            if (oDropOption.editor) {
              const oSelectedSymbol =
                oDropOption.editor.selectedSymbols?.length && oDropOption.editor.selectedSymbols[0];
              if (oSelectedSymbol === oDropOption.droppedSymbol) {
                oDropOption.editor.controller
                  ?.getParentController()
                  .updatePropertyPanel([oLinkSymbol.object.resource.model]);
                setTimeout(() => {
                  oDropOption.editor.controller?.getParentController().updatePropertyPanel([oLinkSymbol.object.target]);
                }, 200);
              }
            }
          }
          oParentNode.deleteSymbol();
        } else if (sourceClassName === "Task" && (targetClassName === "Start" || targetClassName === "Operation")) {
          // Only  dangling nodes and task from tree can be dropped in the begin symbol and operation symbol
          // Create Link
          // Start => Task or Operation => Task
          const linksAlreadyExist = oNode.resource.model.links.selectAllObjects({
            "source.objectId": oParentNode.objectId,
            "target.objectId": oNode.objectId,
          }).length;
          if (linksAlreadyExist === 0) {
            if (oDropOption?.operation) {
              nsLocal.ModelImpl.addNodeAfter(oParentNode, oNode, oDropOption.operation);
            }
            let oLink = nsLocal.ModelImpl.createLink(oParentNode, oNode, false /* bUpdateIndexing*/);
            if (targetClassName === "Start") {
              oLink.statusRequired = OutputType.NONE;
            }
          }
        } else if (sourceClassName === "Operation" && (targetClassName === "Task" || targetClassName === "Operation")) {
          // Only dangling operation from diagram can be dropped task symbol and operation symbol
          // Create Link
          // Task => Operation or Operation => Operation
          const linksAlreadyExist = oNode.resource.model.links.selectAllObjects({
            "source.objectId": oParentNode.objectId,
            "target.objectId": oNode.objectId,
          }).length;
          if (linksAlreadyExist === 0) {
            let oLink = nsLocal.ModelImpl.createLink(oParentNode, oNode, false /* bUpdateIndexing*/);
            if (targetClassName === "Task") {
              oLink.statusRequired = OutputType.COMPLETED;
            } else if (targetClassName === "Operation") {
              oLink.statusRequired = OutputType.ANY;
            }
          }
        }

        if (refreshDiagram) {
          nsLocal.ModelImpl.requestAdjustDiagramsContent({
            selection: oNode,
            autoLayout: true,
          });
        }
      },

      // Input parameter function
      showIPPopUP: function (oTask, params) {
        const minVisibleRowCount = params.length;
        const ProcedureName = oTask.name;

        return new Promise((resolve) => {
          showDialog(
            require("../../../taskchainmodeler/view/InputParameter.view.xml"),
            nsLocal.ModelImpl.getLocalizedText("lblInputParameters"),
            nsLocal.ModelImpl.getLocalizedText("btnOk"),
            nsLocal.ModelImpl.getLocalizedText("btnCancel"),
            {
              currentNode: oTask,
              parameters: params,
              minVisibleRowCount: minVisibleRowCount,
              ProcedureName: ProcedureName,
            },
            "35%",
            "auto",
            null,
            null,
            null,
            (dialog: sap.m.Dialog) => {
              const ipView = dialog.getContent()[0] as sap.ui.core.mvc.View;
              const okButton = ipView?.byId("ok") as sap.m.Button;
              let enableButton = true;
              if ((ipView?.getModel("viewData") as sap.ui.model.json.JSONModel)?.getData()?.parameters) {
                for (const param of (ipView?.getModel("viewData") as sap.ui.model.json.JSONModel)?.getData()
                  ?.parameters) {
                  enableButton &&= true;
                  okButton.setEnabled(enableButton);
                }
              } else {
                okButton.setEnabled(false);
              }
            }
          ).then(async (result: any) => {
            if (result) {
              const param = result.viewData.oData.parameters;
              for (let index = 0; index < param.length; index++) {
                oTask.parameters.get(index).value = param[index].value;
              }
              oTask.resource.model.validate();
            }
          });
        });
      },

      // oParentNode - source
      // oNode - Target
      updateLinks: function (oNode, oParentNode, bLinkCreation) {
        const nodeToUpdate = oParentNode;
        const linksAlreadyExist = oParentNode.resource.model.links.selectAllObjects({
          "source.objectId": oParentNode.objectId,
          "target.objectId": oNode.objectId,
        }).length;
        if (linksAlreadyExist > 0) {
          return;
        }
        const hasSuccessorNodesBeforeDelete = oParentNode.resource.model.hasSuccessorNodesBeforeDelete;
        oParentNode.resource.model.hasSuccessorNodesBeforeDelete = true;
        // Remove parentNode(symbol dropped) node links and get the target nodes
        let aSourceLinkObjects = oParentNode.links?.toArray?.() || [];
        const aTargetNodes = [];
        const aSourceNodes = [];
        let nLength = aSourceLinkObjects ? aSourceLinkObjects.length : 0;
        let nIndex;
        for (nIndex = 0; nIndex < nLength; nIndex++) {
          const oLink = aSourceLinkObjects[nIndex];
          oLink.target ? aTargetNodes.push(oLink.target) : undefined;
          oLink.deleteObject();
        }
        if (bLinkCreation) {
          aSourceLinkObjects = oParentNode.sourceLinks;
          nLength = aSourceLinkObjects ? aSourceLinkObjects.length : 0;
          for (nIndex = 0; nIndex < nLength; nIndex++) {
            const oLink = aSourceLinkObjects[nIndex];
            oLink.source ? aSourceNodes.push(oLink.source) : undefined;
            oLink.deleteObject();
          }
        }
        let aPredecessorNode;
        let aTargetLinkObjects;
        if (bLinkCreation) {
          aTargetLinkObjects = oNode.sourceLinks;
          nLength = aTargetLinkObjects ? aTargetLinkObjects.length : 0;
          for (nIndex = 0; nIndex < nLength; nIndex++) {
            const oLink = aTargetLinkObjects[nIndex];
            const oSourceNode = oLink.source;
            oLink.deleteObject();
            if (oSourceNode) {
              nsLocal.ModelImpl.createLink(oSourceNode, oParentNode);
            }
          }
        }
        if (bLinkCreation) {
          nLength = aTargetNodes ? aTargetNodes.length : 0;
          const sourceLength = aSourceNodes ? aSourceNodes.length : 0;
          for (nIndex = 0; nIndex < nLength; nIndex++) {
            const oTargetNode = aTargetNodes[nIndex];
            for (let sourceIndex = 0; sourceIndex < sourceLength; sourceIndex++) {
              const oSourceNode = aSourceNodes[sourceIndex];
              nsLocal.ModelImpl.createLink(oSourceNode, oTargetNode);
            }
          }
        }
        oParentNode.resource.model.hasSuccessorNodesBeforeDelete = hasSuccessorNodesBeforeDelete;
        return nodeToUpdate;
      },

      // oParentNode - source
      // oNode - Target
      addNodeAfter: function (oParentNode, oNode, operation) {
        const nodeToUpdate = oParentNode;
        const linksAlreadyExist = oParentNode.resource.model.links.selectAllObjects({
          "source.objectId": oParentNode.objectId,
          "target.objectId": oNode.objectId,
        }).length;
        if (linksAlreadyExist > 0) {
          return;
        }
        let nLength, nIndex;
        const aTargetNodes = [];
        // Remove parentNode links and get the target nodes
        // For parallel node done delete outgoing links, just add new link
        if (operation === "AddNew") {
          let aSourceLinkObjects = oParentNode.links?.toArray?.() || [];
          nLength = aSourceLinkObjects ? aSourceLinkObjects.length : 0;
          nIndex;
          for (nIndex = 0; nIndex < nLength; nIndex++) {
            const oLink = aSourceLinkObjects[nIndex];
            oLink.target ? aTargetNodes.push(oLink.target) : undefined;
            oLink.deleteObject();
          }
        }

        // A => B => C -------------- A => C
        // Start Dropped node links, connect predecessor to the successor nodes
        nsLocal.ModelImpl.removeIntermediateNode(oNode);
        // End for Dropped node connected symbol

        if (operation === "AddNew") {
          nLength = aTargetNodes ? aTargetNodes.length : 0;
          for (nIndex = 0; nIndex < nLength; nIndex++) {
            const oTargetNode = aTargetNodes[nIndex];
            nsLocal.ModelImpl.createLink(oNode, oTargetNode);
          }
        }
        return nodeToUpdate;
      },

      // A => B => C -------------- A => C
      // Start Dropped node links, connect predecessor to the successor nodes
      removeIntermediateNode: function (oNode) {
        let nLength, nIndex;
        const aPredecessorDroppedNode = [];
        const aSuccessorDroppedNode = [];
        // Remove oNode links and sourceLinks
        let aLinkObjectsOut = oNode.links?.toArray?.() || [];
        nLength = aLinkObjectsOut.length;
        for (nIndex = 0; nIndex < nLength; nIndex++) {
          const oLink = aLinkObjectsOut[nIndex];
          oLink.target ? aSuccessorDroppedNode.push(oLink.target) : undefined;
          oLink.deleteObject();
        }
        let aLinkObjectsIn = oNode.sourceLinks || [];
        nLength = aLinkObjectsIn.length;
        for (nIndex = 0; nIndex < nLength; nIndex++) {
          const oLink = aLinkObjectsIn[nIndex];
          oLink.target ? aPredecessorDroppedNode.push(oLink.source) : undefined;
          oLink.deleteObject();
        }
        nLength = aSuccessorDroppedNode ? aSuccessorDroppedNode.length : 0;
        const sourceLength = aPredecessorDroppedNode ? aPredecessorDroppedNode.length : 0;
        for (nIndex = 0; nIndex < nLength; nIndex++) {
          const oTargetNode = aSuccessorDroppedNode[nIndex];
          for (let sourceIndex = 0; sourceIndex < sourceLength; sourceIndex++) {
            const oSourceNode = aPredecessorDroppedNode[sourceIndex];
            if (!(oSourceNode.isStart && oTargetNode.isOperator)) {
              nsLocal.ModelImpl.createLink(oSourceNode, oTargetNode);
            }
          }
        }
      },

      createLinkByConnector: function (oNode, oParentNode, bLinkCreation) {
        const linksAlreadyExist = oParentNode.resource.model.links.selectAllObjects({
          "source.objectId": oParentNode.objectId,
          "target.objectId": oNode.objectId,
        }).length;
        if (linksAlreadyExist > 0) {
          return;
        } else {
          let oTargetParentLinks = oNode.links?.toArray?.();
          let oTargetParentTargetOperators;
          if (oTargetParentLinks?.length) {
            oTargetParentTargetOperators = oTargetParentLinks
              .filter((obj) => obj.target?.isOperator)
              .map((obj) => obj.target);
          }
          if (!oNode.isOperator) {
            const oSourceLinks = oNode.sourceLinks;
            if (oSourceLinks) {
              for (let index = 0; index < oSourceLinks.length; index++) {
                const oLink = oSourceLinks[index];
                oLink.deleteObject();
              }
            }
            // const oTargetLinks = oNode.links?.toArray?.() || [];
            // for (let index = 0; index < oTargetLinks.length; index++) {
            //   const oLink = oTargetLinks[index];
            //   if (oLink?.target?.isOperator && oTargetParentTargetOperators.includes(oLink.target)) {
            //     oLink.deleteObject();
            //   }
            // }
          }
          nsLocal.ModelImpl.createLink(oParentNode, oNode);
          nsLocal.ModelImpl.requestAdjustDiagramsContent({
            selection: oParentNode,
            autoLayout: true,
          });
        }
      },

      replaceNode: function (oReplaceNode, oOldNode) {
        const oldIsDeleting = nsLocal.ModelImpl.isDeleting;
        nsLocal.ModelImpl.isDeleting = true;
        const aPredecessorNodes = oOldNode.predecessorNodes;
        const aSuccessorNodes = oOldNode.successorNodes;
        const sourceLinks = oOldNode.sourceLinks || [];
        const targetLinks = oOldNode.links.toArray();
        const aPredecessorNode = oOldNode.predecessorNodes?.length ? oOldNode.predecessorNodes[0] : undefined;
        let hasSuccessorNodesBeforeDelete = oOldNode.resource.model.hasSuccessorNodesBeforeDelete;
        if (aPredecessorNode?.isStart) {
          oOldNode.resource.model.hasSuccessorNodesBeforeDelete = true;
        }
        for (let index = 0; index < sourceLinks.length; index++) {
          const oLink = sourceLinks[index];
          oLink.deleteObject();
        }
        for (let index = 0; index < targetLinks.length; index++) {
          const oLink = targetLinks[index];
          oLink.deleteObject();
        }
        // Start Conncet A => B => C to A => C for replaceNode
        nsLocal.ModelImpl.removeIntermediateNode(oReplaceNode);
        // END
        for (let pIndex = 0; pIndex < aPredecessorNodes.length; pIndex++) {
          const oSource = aPredecessorNodes[pIndex];
          if (oReplaceNode !== oSource) {
            nsLocal.ModelImpl.createLink(oSource, oReplaceNode);
          }
        }
        for (let sIndex = 0; sIndex < aSuccessorNodes.length; sIndex++) {
          const oTarget = aSuccessorNodes[sIndex];
          if (oReplaceNode !== oTarget) {
            nsLocal.ModelImpl.createLink(oReplaceNode, oTarget);
          }
        }
        oOldNode.resource.model.hasSuccessorNodesBeforeDelete = hasSuccessorNodesBeforeDelete;
        if (!oldIsDeleting) {
          nsLocal.ModelImpl.isDeleting = false;
          nsLocal.ModelImpl.requestAdjustDiagramsContent({});
        }
      },

      onCascadeDeleteNode: function (oNode) {
        const oldIsDeleting = nsLocal.ModelImpl.isDeleting;
        nsLocal.ModelImpl.isDeleting = true;
        const aPredecessorNodes = oNode.predecessorNodes;
        const aSuccessorNodes = oNode.successorNodes;
        const sourceLinks = oNode?.sourceLinks || [];
        const targetLinks = oNode?.links.toArray();
        for (let index = 0; index < sourceLinks.length; index++) {
          const oLink = sourceLinks[index];
          oLink.deleteObject();
        }
        for (let index = 0; index < targetLinks.length; index++) {
          const oLink = targetLinks[index];
          oLink.deleteObject();
        }
        if (!oNode.isOperator) {
          for (let pIndex = 0; pIndex < aPredecessorNodes.length; pIndex++) {
            const oSource = aPredecessorNodes[pIndex];
            for (let sIndex = 0; sIndex < aSuccessorNodes.length; sIndex++) {
              const oTarget = aSuccessorNodes[sIndex];
              if (!(oSource.isStart && oTarget.isOperator)) {
                nsLocal.ModelImpl.createLink(oSource, oTarget);
              }
            }
          }
        }
        if (!oldIsDeleting) {
          nsLocal.ModelImpl.isDeleting = false;
          nsLocal.ModelImpl.requestAdjustDiagramsContent({});
        }
      },

      /**
       * Helper function to create an object
       * @function
       * @name createObject
       * @param {*} sClassName
       * @param {*} oParam
       */
      createObject: function (sClassName, oParam, oContainer) {
        const oClass = sap.galilei.model.getClass(sClassName),
          oResource = oContainer && oContainer.resource;
        let oNewObject;
        if (oClass && oResource) {
          oNewObject = oClass.create(oResource, oParam);
          if (sClassName === "sap.cdw.taskchainmodeler.Link") {
            oContainer.links.push(oNewObject);
          } else if (sClassName === "sap.cdw.taskchainmodeler.Output") {
            oContainer.outputs?.push(oNewObject);
          } else if (sClassName === "sap.cdw.taskchainmodeler.PlaceHolder") {
            oContainer.placeholders?.push(oNewObject);
          } else if (sClassName === "sap.cdw.taskchainmodeler.Start") {
            oContainer.startNode = oNewObject;
          } else if (sClassName === "sap.cdw.taskchainmodeler.Parameter") {
            oContainer.parameters.push(oNewObject);
          } else if (sClassName === "sap.cdw.taskchainmodeler.RequestHeader") {
            oContainer.requestHeaders.push(oNewObject);
          } else if (oContainer.nodes) {
            oContainer.nodes.push(oNewObject);
          } else if (oContainer.symbols) {
            oContainer.symbols.push(oNewObject);
          }
        }
        if (oNewObject) {
          // eslint-disable-next-line no-underscore-dangle
          oNewObject._creationTimestamp = Date.now();
        }
        if (oResource && !oResource.isLoading && oResource.model) {
          oResource.model.validate();
        }
        return oNewObject;
      },

      createLink: function (oSource, oTarget, bUpdateIndexing) {
        const linksAlreadyExist = oSource.resource.model.links.selectAllObjects({
          "source.objectId": oSource.objectId,
          "target.objectId": oTarget.objectId,
        }).length;
        if (linksAlreadyExist > 0) {
          return;
        }
        const oLink = nsLocal.ModelImpl.createObject(
          "sap.cdw.taskchainmodeler.Link",
          { source: oSource, target: oTarget },
          oSource.resource.model
        );
        const oModel = oSource.resource.model;
        if (bUpdateIndexing) {
          let targetIndex = oModel.nodes.indexOf(oTarget);
          let sourceIndex = oModel.nodes.indexOf(oSource);
          let insertIndex;
          if (targetIndex > sourceIndex) {
            insertIndex = targetIndex - 1;
            if (insertIndex < 0) {
              insertIndex = 0;
            }
            oModel.nodes.removeAt(sourceIndex);
            oModel.nodes.insert(insertIndex, oSource);
          } else {
            insertIndex = targetIndex;
            oModel.nodes.removeAt(sourceIndex);
            oModel.nodes.insert(insertIndex, oSource);
          }
          targetIndex = oModel.nodes.indexOf(oTarget);
          sourceIndex = oModel.nodes.indexOf(oSource);
          oTarget.nodeIndex = targetIndex;
          oSource.nodeIndex = sourceIndex;
        }
        nsLocal.ModelImpl.createOrMergeLink(oSource, oLink);
        return oLink;
      },

      createOrMergeLink: function (parentNode, oLink) {
        if (parentNode?.links) {
          const linkExists = nsLocal.ModelImpl.checkLinkObjectExists(parentNode.links, oLink);
          if (!linkExists) {
            parentNode.links.push(oLink);
          }
        }
      },

      checkLinkObjectExists: function (collection, oLink) {
        const obj = collection.selectObject({
          "source.name": oLink.source.name,
          "target.name": oLink.target.name,
        });
        if (obj && obj.source === oLink.source && obj.target === oLink.target) {
          return true;
        } else {
          return false;
        }
      },

      deleteObject: function (obj, tryFromRelatedSymbols: boolean = true) {
        if (tryFromRelatedSymbols) {
          nsLocal.ModelImpl.requestDeleteObjectAndSymbols(obj);
        } else {
          obj?.deleteObject();
        }
      },

      /**
       * Clear list of actions and undo-stack
       * @function
       * @name clearUndoRedo
       * @param oModel
       */
      clearUndoRedo: function (oModel: any) {
        if (oModel) {
          oModel.resource.clearListOfActions();
          oModel.resource.clearUndoStack();
        }
      },

      /**
       * Model structure has been modified request diagrams adjustments
       * @function
       * @name requestAdjustDiagramsContent
       * @param {*} oOptions the options for instance {
       *  selection: galilei object to select after adjusting symbols
       *  model: in case of loading data
       * }
       */
      requestAdjustDiagramsContent: function (oOptions) {
        if (sap.ui && sap.ui.getCore instanceof Function) {
          sap.ui
            .getCore()
            .getEventBus()
            .publish(nsLocal.ModelImpl.JSONMODEL_CHANNEL, nsLocal.ModelImpl.JSONMODEL_CHANGED_EVENT, oOptions);
        }
      },

      requestDeleteObjectAndSymbols: function (object: any, deleteObject: boolean = true) {
        if (sap.ui && sap.ui.getCore instanceof Function) {
          sap.ui
            .getCore()
            .getEventBus()
            .publish(nsLocal.ModelImpl.JSONMODEL_CHANNEL, nsLocal.ModelImpl.JSONMODEL_DELETE_OBJECTANDSYMBOLS, {
              object: object,
              deleteObject: deleteObject,
            });
        } else {
          object?.deleteObject();
        }
      },

      getOrCreateStartObject: function (oModel) {
        if (oModel.startNode) {
          return oModel.startNode;
        } else {
          const oStartNode = nsLocal.ModelImpl.createObject(
            "sap.cdw.taskchainmodeler.Start",
            { name: nsLocal.ModelImpl.getLocalizedText("txtBegin") },
            oModel
          );
          return oStartNode;
        }
      },

      createPlaceHolderObject: function (oModel) {
        if (oModel) {
          const oPlaceHolderNode = nsLocal.ModelImpl.createObject(
            "sap.cdw.taskchainmodeler.PlaceHolder",
            { name: `PlaceHolder ${oModel.placeholders.length + 1}` },
            oModel
          );
          return oPlaceHolderNode;
        }
      },

      createParameters: function (oTask, oParams) {
        oParams &&
          Object.keys(oParams).forEach((key) => {
            const oParam = oParams[key];
            oParam.primitiveDataType = Utils.fnConvertType(oParam.type);
            nsLocal.ModelImpl.createObject(
              "sap.cdw.taskchainmodeler.Parameter",
              {
                ...oParam,
                name: key,
                defaultValue: oParam.value,
                dataType: oParam.primitiveDataType,
                nativeDataType: oParam.type,
                value: oParam.value,
                scale: oParam.type === "DECIMAL" ? oParam.scale : undefined,
                precision: oParam.type === "DECIMAL" ? oParam.precision : undefined,
              },
              oTask
            );
          });
      },

      getListOfParametersInSQLScriptProcedure: function (oTask: any): any[] {
        const paramsList = [];
        const oParam = oTask.parameters;

        oParam.forEach((paramObj) => {
          paramsList.push({
            name: paramObj.name,
            displayName: paramObj.name,
            dataType: paramObj.dataType,
            primitiveDataType: paramObj.primitiveDataType,
            length: paramObj.length,
            defaultValue: paramObj.defaultValue,
            displayType: paramObj.displayType,
            value: paramObj.value,
            scale: paramObj.dataType === "DECIMAL" ? paramObj.scale : undefined,
            precision: paramObj.dataType === "DECIMAL" ? paramObj.precision : undefined,
            paramObj: paramObj,
          });
        });
        return paramsList;
      },

      createRestApi: function (oTask, oDropParams) {
        oTask.name = oDropParams.name;
        oTask.displayName = oDropParams.name;
        oTask.applicationId = oDropParams.applicationId;
        oTask.connectionName = oDropParams?.connectionName;
        oTask.baseUrl = oDropParams?.baseUrl;
        // invokeApi
        let flag = "invokeAPI";
        const invokeApiObject = nsLocal.ModelImpl.createObject("sap.cdw.taskchainmodeler.InvokeAPIRequest", {}, oTask);
        nsLocal.ModelImpl.createRequestObject(flag, invokeApiObject);
        nsLocal.ModelImpl.createResponseObject(flag, invokeApiObject);
        oTask.invokeAPI = invokeApiObject;

        // status
        flag = "status";
        const statusApiObject = nsLocal.ModelImpl.createObject("sap.cdw.taskchainmodeler.StatusAPIRequest", {}, oTask);
        nsLocal.ModelImpl.createRequestObject(flag, statusApiObject);
        nsLocal.ModelImpl.createResponseObject(flag, statusApiObject);
        oTask.statusAPI = statusApiObject;

        // logsAPI
        flag = "logsApi";
        const logsApiObject = nsLocal.ModelImpl.createObject("sap.cdw.taskchainmodeler.LogsAPIRequest", {}, oTask);
        nsLocal.ModelImpl.createRequestObject(flag, logsApiObject);
        nsLocal.ModelImpl.createResponseObject(flag, logsApiObject);
        oTask.logsAPI = logsApiObject;
      },

      createRequestObject: function (flag, oContainer) {
        const obj = {
          method: "POST",
          apiPath: undefined,
          mode: "SYNC",
          csrfToken: false,
          csrfTokenUrl: "",
          body: "",
        };
        if (flag === "invokeAPI") {
          obj.method = "POST";
        } else if (flag === "status") {
          obj.method = "GET";
          obj.mode = undefined;
        }
        const requestObject = nsLocal.ModelImpl.createObject("sap.cdw.taskchainmodeler.APIRequest", obj, oContainer);
        oContainer.request = requestObject;
      },

      createResponseObject: function (flag, oContainer) {
        const obj = {
          from: "BODY",
          responseSelectedIndex: 0,
          jobIdJsonPath: "",
          successIndicatorPath: "",
          successIndicatorCondition: "EQUAL",
          successIndicatorValue: "",
          errorIndicatorPath: "",
          errorIndicatorCondition: "EQUAL",
          errorIndicatorValue: "",
          errorReasonPath: "",
          logsPath: undefined,
        };
        if (flag === "status") {
          obj.jobIdJsonPath = undefined;
        }

        const responseObject = nsLocal.ModelImpl.createObject("sap.cdw.taskchainmodeler.APIResponse", obj, oContainer);
        oContainer.response = responseObject;
      },

      createOperationCommand: function (oParam) {
        const oObject = oParam?.symbol?.object;
        const oSymbol = oParam?.symbol;
        const oEditor = oParam?.editor;
        oParam.symbol.resource.applyUndoableAction(function () {
          // Create symbol and draw
          sap.cdw.taskchainmodeler.DiagramImpl.addOperationSymbol(oSymbol, oEditor, oParam.source.text);
          nsLocal.ModelImpl.requestAdjustDiagramsContent({ autoLayout: true, selection: oObject });
        });
      },

      checkBeginPlaceHolder: function (oModel) {
        if (oModel?.startNode?.successorNodes?.length === 0) {
          if (sap.ui && sap.ui.getCore instanceof Function) {
            sap.ui
              .getCore()
              .getEventBus()
              .publish(nsLocal.ModelImpl.JSONMODEL_CHANNEL, nsLocal.ModelImpl.CREATE_PLACEHOLDER_EVENT, {
                model: oModel,
              });
          }
        }
      },

      getUniqueAlias: function (oObjects, name) {
        const aNames = {};
        let sAlias;
        const sName = `${name} 0`;
        sAlias = sName;
        const setUniqueAlias = function () {
          let n = 0;
          sAlias = "";
          do {
            sAlias = `${name} ${n++}`;
          } while (aNames[sAlias]);
          aNames[sAlias] = {
            actualName: sAlias,
          };
        };

        // Visit all objects names/aliases
        if (oObjects) {
          oObjects.forEach((entity) => {
            const sActualName = entity && entity.name;
            aNames[sActualName] = {
              actualName: sActualName,
              entity: entity,
            };
          });
        }
        // should we need suggest an alias?
        const duplicate = aNames[sName];
        if (duplicate) {
          setUniqueAlias();
        }
        return sAlias;
      },
    },
  });

  /**
   * Edit with Command for table or view
   */
  nsLocal.EditWithCommand = sap.galilei.core.defineClass({
    fullClassName: "sap.cdw.taskchainmodeler.EditWithCommand",
    parent: sap.galilei.ui.common.command.Command,
    methods: {
      execute: function (oParam) {
        const oObject = oParam && oParam.symbol && oParam.symbol.object;
        const oEditor = oParam && oParam.editor;
        const oWorkbenchController = oEditor && oEditor.previewService;

        if (
          oObject &&
          oObject.classDefinition.name === "Task" &&
          oWorkbenchController &&
          oWorkbenchController.spaceId
        ) {
          if (oObject.isCrossSpace === true && oObject["#technicalType"] === "DWC_TASKCHAIN") {
            oObject.crossSpaceEntityName = oObject.name;
          }
          openInNewTab(undefined, oObject, oObject?.crossSpaceName);
        }
      },
    },
  });
  sap.galilei.ui.common.command.CommandRegistry.add(
    "sap.cdw.taskchainmodeler.EditWithCommand",
    new nsLocal.EditWithCommand()
  );
  /**
   * Impact Lineage Command
   */
  nsLocal.ImpactLineageCommand = sap.galilei.core.defineClass({
    fullClassName: "sap.cdw.taskchainmodeler.ImpactLineageCommand",
    parent: sap.galilei.ui.common.command.Command,
    methods: {
      execute: function (oParam) {
        const oObject = oParam && oParam.symbol && oParam.symbol.object;
        const oEditor = oParam && oParam.editor;
        const oWorkbenchController = oEditor && oEditor.previewService;
        if (oObject && oWorkbenchController) {
          const utilityClass = new BusinessCatalogLineageUtility();
          const spaceId = oObject.isCrossSpace ? oObject.crossSpaceName : oWorkbenchController.spaceId;
          const sharedObjectTargetSpace = oObject.isCrossSpace ? oWorkbenchController.spaceId : undefined;
          const dirtyModel = oWorkbenchController.isDirty()
            ? oWorkbenchController.toolAdapter.currentBreadcrumb.text
            : "";
          onAnalyzeImpact({
            utilityClass,
            spaceId,
            objectId: oObject.name,
            businessName: oObject.label,
            sharedObjectTargetSpace,
            dirtyModel,
          });
        }
        const oContextButtonPad = oParam && oParam.source && oParam.source.contextButtonPad;
        if (oContextButtonPad) {
          setTimeout(() => {
            oContextButtonPad.showWidget();
          }, 0);
        }
      },
    },
  });
  sap.galilei.ui.common.command.CommandRegistry.add(
    "sap.cdw.taskchainmodeler.ImpactLineageCommand",
    new nsLocal.ImpactLineageCommand()
  );

  /**
   * Add PlaceHolder for the symbol
   */
  nsLocal.AddPlaceHolderCommand = sap.galilei.core.defineClass({
    fullClassName: "sap.cdw.taskchainmodeler.AddPlaceHolderCommand",
    parent: sap.galilei.ui.common.command.Command,
    methods: {
      canExecute: function (oParam) {
        return true;
      },
      execute: function (oParam) {
        const oObject = oParam?.symbol?.object;
        const oSymbol = oParam?.symbol;
        const oEditor = oParam?.editor;

        oParam.symbol.resource.applyUndoableAction(function () {
          oObject.placeHolderNodes++;
          // Create symbol and draw
          sap.cdw.taskchainmodeler.DiagramImpl.addPlaceHolderSymbols(oSymbol, oEditor.diagram, oEditor);
          nsLocal.ModelImpl.requestAdjustDiagramsContent({ autoLayout: true, selection: oObject });
        });
      },
    },
  });
  sap.galilei.ui.common.command.CommandRegistry.add(
    "sap.cdw.taskchainmodeler.AddPlaceHolderCommand",
    new nsLocal.AddPlaceHolderCommand()
  );

  /**
   * Add ParallelBranch for the symbol
   */
  nsLocal.AddParallelBranchCommand = sap.galilei.core.defineClass({
    fullClassName: "sap.cdw.taskchainmodeler.AddParallelBranchCommand",
    parent: sap.galilei.ui.common.command.Command,
    methods: {
      canExecute: function (oParam) {
        return true;
      },
      execute: function (oParam) {
        const oObject = oParam?.symbol?.object;
        const oEditor = oParam?.editor;
        const oPredecessorNode = oObject?.predecessorNodes.length && oObject.predecessorNodes[0];
        let oPredecessorSymbol =
          oPredecessorNode && nsLocal.DiagramImpl.relatedSymbols(oPredecessorNode, oEditor.diagram);
        if (oPredecessorSymbol.length > 0) {
          oPredecessorSymbol = oPredecessorSymbol[0];
        }
        if (oPredecessorSymbol) {
          oParam.symbol.resource.applyUndoableAction(function () {
            oPredecessorNode.placeHolderNodes++;
            // Create symbol and draw
            sap.cdw.taskchainmodeler.DiagramImpl.addPlaceHolderSymbols(oPredecessorSymbol, oEditor.diagram, oEditor);
            nsLocal.ModelImpl.requestAdjustDiagramsContent({ autoLayout: true, selection: oObject });
          });
        }
      },
    },
  });
  sap.galilei.ui.common.command.CommandRegistry.add(
    "sap.cdw.taskchainmodeler.AddParallelBranchCommand",
    new nsLocal.AddParallelBranchCommand()
  );

  /**
   * Add Operation for the symbol
   */
  nsLocal.AddOperationCommand = sap.galilei.core.defineClass({
    fullClassName: "sap.cdw.taskchainmodeler.AddOperationCommand",
    parent: sap.galilei.ui.common.command.Command,
    methods: {
      canExecute: function (oParam) {
        return true;
      },
      executeAll: function (oParam) {},
      execute: function (oParam) {
        nsLocal.ModelImpl.createOperationCommand(oParam);
      },
    },
  });
  sap.galilei.ui.common.command.CommandRegistry.add(
    "sap.cdw.taskchainmodeler.AddOperationCommand",
    new nsLocal.AddOperationCommand()
  );

  /**
   * Add Operation for the symbol
   */
  nsLocal.AddAnyOperationCommand = sap.galilei.core.defineClass({
    fullClassName: "sap.cdw.taskchainmodeler.AddAnyOperationCommand",
    parent: sap.galilei.ui.common.command.Command,
    methods: {
      canExecute: function (oParam) {
        return true;
      },
      executeAll: function (oParam) {
        // ToDO
      },
      execute: function (oParam) {
        nsLocal.ModelImpl.createOperationCommand(oParam);
      },
    },
  });
  sap.galilei.ui.common.command.CommandRegistry.add(
    "sap.cdw.taskchainmodeler.AddAnyOperationCommand",
    new nsLocal.AddAnyOperationCommand()
  );
});
