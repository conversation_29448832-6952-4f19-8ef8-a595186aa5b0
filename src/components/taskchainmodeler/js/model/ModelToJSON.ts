/**
 * Copyright 2020 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { isSparkSelectionVacuumEnabled } from "../../../abstractbuilder/commonservices/FeatureFlagCheck";
import { Activity } from "../../../tasklog/utility/Constants";
import Utils, { NODE_TYPE, OPERATORS, OutputType } from "../utils";

export class ModelToJSON {
  /**
   *
   *
   * @static
   * @param {*} oModel
   * @returns
   * @memberof ModelToJSON
   */
  static serializeModel(oModel) {
    // process nodes
    let orderedNodes;
    if (oModel.nodes.length === 1) {
      orderedNodes = oModel.nodes.toArray();
    } else {
      orderedNodes = oModel.nodes
        .toArray()
        .filter((oNode) => oNode?.successorNodes?.length > 0 || oNode?.predecessorNodes?.length > 0);
    }
    const startNode = oModel.startNode;
    if (startNode) {
      startNode.nodeIndexForJSON = 0;
      orderedNodes = ModelToJSON.traverseNodes([startNode], 0, [startNode]);
    }

    // cleanup unused nodes
    ModelToJSON.cleanupNodes(oModel);
    const value: any = {
      nodes: [],
      links: [],
    };
    orderedNodes.sort((b, a) => b.nodeIndexForJSON - a.nodeIndexForJSON);
    let lastNodeIndex = orderedNodes[orderedNodes.length - 1].nodeIndexForJSON;
    let aUnconnctedNodes = oModel.nodes.toArray().filter((oNode) => !orderedNodes.includes(oNode)) || [];
    aUnconnctedNodes.map((obj) => {
      lastNodeIndex++;
      obj.nodeIndexForJSON = lastNodeIndex;
    });
    orderedNodes = [...orderedNodes, ...aUnconnctedNodes];

    for (let index = 0; index < orderedNodes.length; index++) {
      let oNode = orderedNodes[index];
      ModelToJSON.processNodes(value, oModel, oNode);
    }
    for (let index = 0; index < orderedNodes.length; index++) {
      let oNode = orderedNodes[index];
      ModelToJSON.processLinks(value, oModel, oNode);
    }
    if (oModel.options) {
      value.options = oModel.options;
    }
    const oFeatures = sap.ui.getCore().getModel("featureflags").getData();
    if (oModel.isVerticalView !== "undefined" && oFeatures.DWCO_TASKCHAIN_DIGRAM_ORIENTATION_MODE) {
      if (value.options) {
        value.options["layout"] = oModel.isVerticalView ? "VERTICAL" : "HORIZONTAL";
      } else {
        value.options = { layout: oModel.isVerticalView ? "VERTICAL" : "HORIZONTAL" };
      }
    }

    return value;
  }

  /**
   *
   *
   * @static
   * @param {*} oModel
   * @memberof ModelToJSON
   */
  static cleanupNodes(oModel) {
    for (const oNode of oModel.nodes.toArray()) {
      if (oNode) {
        // if (oNode.getLinks() && oNode.getLinks().length === 0) {
        // empty Node
        // oModel.resource.applyUndoableAction(() => {
        //   oNode.deleteObject();
        // }, "remove unused Node", true);
        // }
      }
    }
  }

  /**
   *
   *
   * @static
   * @param {*} contents
   * @param {*} oModel
   * @param {sap.cdw.taskchainmodeler.Node} oNode
   * @memberof ModelToJSON
   */
  static processNodes(value: any, oModel: sap.cdw.taskchainmodeler.Model, oNode: any) {
    let oNodeContent;
    const oFeatures = sap.ui.getCore()?.getModel("featureflags")?.getData();
    if (oNode.isOperator && oNode.operationType) {
      oNodeContent = {
        id: oNode.nodeIndexForJSON,
        type: Object.keys(OPERATORS)[Object.values(OPERATORS).indexOf(oNode.operationType)],
      };
    } else if (oNode.isStart) {
      oNodeContent = {
        id: oNode.nodeIndexForJSON,
        type: NODE_TYPE.START,
      };
    } else if (oNode.isTask) {
      oNodeContent = {
        id: oNode.nodeIndexForJSON,
        type: "TASK",
        taskIdentifier: {
          applicationId: Utils.getApplicationId(oNode),
          activity: Utils.getActivity(oNode),
          objectId: oNode.name,
        },
      };
      if (oNode.isDeltaTable && oNodeContent.taskIdentifier.activity === "REMOVE_DELETED_RECORDS") {
        if (!oFeatures.DWCO_LOCAL_TABLE_TASKS_ACTIVE_RECORDS) {
          oNodeContent.taskIdentifier.objectId = oNode.name;
        }
        oNodeContent.taskIdentifier.parameters = {
          retentionTimeInDays: {
            val: oNode.numberOfRetentionDays,
          },
        };
      } else if (
        oNode.isDeltaTable &&
        oNodeContent.taskIdentifier.activity === "VACUUM_FILES" &&
        oNode.retentionOptionSelected
      ) {
        if (isSparkSelectionVacuumEnabled() && !oNode.defaultApacheSelected) {
          oNodeContent.taskIdentifier.parameters = {
            retentionTimeInDays: {
              val: oNode.numberOfRetentionDays,
            },
            SAP_sparkapplication: {
              val: oNode.customApacheValueSelected,
            },
          };
        } else {
          oNodeContent.taskIdentifier.parameters = {
            retentionTimeInDays: {
              val: oNode.numberOfRetentionDays,
            },
          };
        }
      } else if (
        (oNode.isDeltaTable || oNode.isTransformationFlow) &&
        oFeatures.DWCO_LARGE_SYSTEMS_SPARK_SELECTION &&
        (oNode.isTransformationFlow ||
          [Activity.MERGE_FILES, Activity.OPTIMIZE_FILES].includes(oNodeContent.taskIdentifier.activity)) &&
        !oNode.defaultApacheSelected
      ) {
        oNodeContent.taskIdentifier.parameters = {
          SAP_sparkapplication: {
            val: oNode.customApacheValueSelected,
          },
        };
      } else if (oNode.isSQLScriptProcedure) {
        oNodeContent.taskIdentifier.objectId = oNode.name;

        const obj = {};
        oNode.parameters.forEach((oParamGalileiObj) => {
          obj[oParamGalileiObj.name] = {
            type: oParamGalileiObj.nativeDataType,
            primitiveDataType: oParamGalileiObj.primitiveDataType,
            length: oParamGalileiObj.length,
            value: oParamGalileiObj.value,
            scale: oParamGalileiObj.nativeDataType === "Decimal" ? oParamGalileiObj.scale : undefined,
            precision: oParamGalileiObj.nativeDataType === "Decimal" ? oParamGalileiObj.precision : undefined,
          };
        });
        oNodeContent.taskIdentifier.parameters = {
          procedureParameters: obj,
        };
      } else if (oNode.isBWProcessChain) {
        oNodeContent.taskIdentifier.spaceId = oNode?.spaceName ? oNode?.spaceName : oNode?.repositoryCSN?.spaceId;
      } else if (oNode.isCrossSpace && oNode.isBWProcessChain === false && oNode.isRestApi === false) {
        oNodeContent.taskIdentifier.spaceId = oNode.repositoryCSN?.crossSpaceName
          ? oNode.repositoryCSN?.crossSpaceName
          : oNode.repositoryCSN?.spaceName;
      } else if (oNode.isRestApi === true) {
        ModelToJSON.processRestApi(oNodeContent, oNode);
      } else if (oNode.isNotificationTask === true) {
        oNodeContent.configuration = {
          body: oNode?.notificationMessage,
          subject: oNode?.notificationSubject,
          chainName: oModel?.name,
        };
      }
    }
    if (oNodeContent) {
      value.nodes.push(oNodeContent);
    }
  }

  /**
   *
   *
   * @static
   * @param {*} oNodeContent
   * @param {sap.cdw.taskchainmodeler.Node} oNode
   * @memberof ModelToJSON
   */
  static processRestApi(oNodeContent: any, oNode: any) {
    if (!oNode?.resource?.model?.connectionList) {
      oNode.resource.model.connectionList = Utils.getApiConnectionList(oNode);
    }
    const flag = true;
    const config = {
      // apiGenericSettings: {
      //   requestHeaders: {},
      // },
      invokeAPI: {
        request: ModelToJSON.getRESTAPIRequestObject(oNode.invokeAPI?.request, flag),
        expectedResponse: ModelToJSON.getRESTAPIResponseObject(oNode.invokeAPI?.response, oNode, flag),
      },
      ...(oNode.invokeAPI.request.mode === "ASYNC"
        ? oNode.invokeAPI.response.from === "BODY"
          ? {
              statusAPI: {
                request: ModelToJSON.getRESTAPIRequestObject(oNode.statusAPI?.request, !flag),
                expectedResponse: ModelToJSON.getRESTAPIResponseObject(oNode.statusAPI?.response, oNode, !flag),
              },
            }
          : {
              statusAPI: {
                expectedResponse: ModelToJSON.getRESTAPIResponseObject(oNode.statusAPI?.response, oNode, !flag),
              },
            }
        : {}),

      // if logsAPI is required uncomment below code
      // logsAPI: {
      //   request: ModelToJSON.getRESTAPIRequestObject(oNode.logsApi?.request),
      //   response: ModelToJSON.getRESTAPIResponseObject(oNode.logsApi?.response),
      // },
    } as any;
    config.connectionName = Utils.getApiSelectedConnection(oNode);
    if (oNode.requestHeaders.length > 0) {
      config.apiGenericSettings = {
        requestHeaders: {},
      };
    }
    oNode.requestHeaders.forEach((header: any) => {
      if (header.key?.trim() !== "" && header.value?.trim() !== "") {
        config.apiGenericSettings.requestHeaders[header.key] = header.value;
      }
    });
    oNodeContent.configuration = config;
  }

  /**
   *
   *
   * @static
   * @param {*} obj - request object
   * @memberof ModelToJSON
   */
  static getRESTAPIRequestObject(obj: any, flag: boolean) {
    const result = {} as any;
    if (obj?.method) {
      result.method = obj.method;
    }
    if (obj?.apiPath) {
      result.apiPath = obj.apiPath;
    }
    if (obj?.mode) {
      result.mode = obj.mode;
    }
    if (obj?.body !== null && obj?.body !== undefined) {
      try {
        result.body = JSON.parse(obj.body);
      } catch (error) {
        result.body = {};
      }
    }
    if (flag) {
      result.csrfTokenRequired = !!obj.csrfToken;
      if (result.csrfTokenRequired && obj?.csrfTokenUrl) {
        result.csrfTokenUrl = obj.csrfTokenUrl;
      }
    }
    return result;
  }

  /**
   *
   *
   * @static
   * @param {*} obj - response object
   * @memberof ModelToJSON
   */
  static getRESTAPIResponseObject(obj: any, oNode: any, flag: boolean) {
    const result = {} as any;
    if (obj?.from) {
      result.from = obj.from;
    }
    if (obj?.from === "BODY") {
      if (flag && oNode?.invokeAPI?.request?.mode === "ASYNC") {
        result.body = { jobIdJsonPath: obj.jobIdJsonPath || "" };
      }
      if ((flag && oNode?.invokeAPI?.request?.mode === "SYNC") || !flag) {
        result.body = {
          successJsonPath: obj.successIndicatorPath,
          successCondition: obj.successIndicatorCondition,
          successExpectedValue: obj.successIndicatorValue,
          errorJsonPath: obj.errorIndicatorPath,
          errorCondition: obj.errorIndicatorCondition,
          errorExpectedValue: obj.errorIndicatorValue,
          errorReasonJsonPath: obj.errorReasonPath,
        };
      }
    } else if (obj?.from === "code") {
      // TODO: add properties to handle status code
    }
    return result;
  }

  /**
   *
   *
   * @static
   * @param {*} contents
   * @param {*} oModel
   * @param {sap.cdw.taskchainmodeler.Node} oNode
   * @memberof ModelToJSON
   */
  static processLinks(value: any, oModel: sap.cdw.taskchainmodeler.Model, oNode: sap.cdw.taskchainmodeler.Node) {
    let oLinks: sap.galilei.model.BaseCollection<sap.cdw.taskchainmodeler.Link> = oNode.links;
    oLinks &&
      oLinks.forEach((oLink: any) => {
        if (
          (Object.values(OutputType).includes(oLink.statusRequired) || oNode.isStart) &&
          (oLink.target?.isTask || oLink.target?.isOperator)
        ) {
          let oLinkContent = {
            startNode: {
              nodeId: oLink.source.nodeIndexForJSON,
              statusRequired: oNode.isStart || oNode.isOperator ? OutputType.ANY : oLink.statusRequired,
            },
            endNode: {
              nodeId: oLink.target.nodeIndexForJSON,
            },
            id: value.links.length,
          };
          value.links.push(oLinkContent);
        }
      });
  }

  // Traverse nodes
  //        0-A
  //    1-B     2-C
  // 3-D  4-E       5-F
  static traverseNodes(oNodes: any, nodeIndexForJSON: any, updatedNodes) {
    let { nodeIndexForJSONUpdated, targetNodes, updatedNodes2 } = ModelToJSON._traverseNodes(
      oNodes,
      nodeIndexForJSON,
      updatedNodes
    );
    nodeIndexForJSON = nodeIndexForJSONUpdated;
    updatedNodes = updatedNodes2;
    if (targetNodes?.length) {
      updatedNodes = ModelToJSON.traverseNodes(targetNodes, nodeIndexForJSON, updatedNodes);
    }
    return updatedNodes;
  }

  static _traverseNodes(oNodes: any, nodeIndexForJSON, updatedNodes) {
    let targetNodes = [];
    for (let index = 0; index < oNodes.length; index++) {
      const oNode = oNodes[index];
      if (oNode) {
        const links = oNode?.links?.toArray?.() || [];
        for (let index = 0; index < links.length; index++) {
          const oLink = links[index];
          if (
            oLink?.target &&
            (oLink?.target.isTask || oLink?.target.isOperator) &&
            !updatedNodes.includes(oLink.target)
          ) {
            nodeIndexForJSON++;
            oLink.target.nodeIndexForJSON = nodeIndexForJSON;
            updatedNodes.push(oLink.target);
            targetNodes.push(oLink.target);
          }
        }
      }
    }
    return { nodeIndexForJSONUpdated: nodeIndexForJSON, targetNodes, updatedNodes2: updatedNodes };
  }
}
