/** @format */

import { getArtefactSharesForTarget } from "../../../services/metadata";
import { EditorCapabilities, IBrowserOptions } from "../../abstractbuilder/api";
import {
  isReplicationFlowEnabled,
  isSparkSelectionVacuumEnabled,
  isTransformationFlowEnabled,
} from "../../abstractbuilder/commonservices/FeatureFlagCheck";
import { getSpaceName } from "../../abstractbuilder/utility/BuilderUtils";
import * as RepositoryUtils from "../../abstractbuilder/utility/RepositoryUtils";
import { FileType, Type, getFileTypeIcon, getIcon } from "../../businesscatalogs/utility/BusinessCatalogsUtility";
import { CDSDataType } from "../../commonmodel/model/types/cds.types";
import { getActivityData } from "../../datasourcebrowser/utility/DatasourceUtility";
import { MessageHandler } from "../../reuse/utility/MessageHandler";
import { ContentType, DataType, HttpMethod, ServiceCall } from "../../reuse/utility/ServiceCall";
import { Repo } from "../../shell/utility/Repo";
import { UISpaceCapabilities } from "../../shell/utility/UISpaceCapabilities";
import { Activity, ApplicationId } from "../../tasklog/utility/Constants";

interface ITaskTypeProperties {
  isRemoteTable: boolean;
  isDeltaTable: boolean;
  isView: boolean;
  isDataFlow: boolean;
  isIntelligentLookup: boolean;
  isTransformationFlow: boolean;
  isReplicationFlow: boolean;
  isTaskChain: boolean;
  isBWProcessChain: boolean;
  isSQLScriptProcedure: boolean;
  isRemoveData: boolean;
}

// Properties for Task Type
export const taskTypeProperties: { [key in keyof ITaskTypeProperties]: any } = {
  isDeltaTable: {
    dataType: sap.galilei.model.dataTypes.gBool,
    defaultValue: false,
  },
  isRemoteTable: {
    dataType: sap.galilei.model.dataTypes.gBool,
    defaultValue: false,
  },
  isView: {
    dataType: sap.galilei.model.dataTypes.gBool,
    defaultValue: false,
  },
  isDataFlow: {
    dataType: sap.galilei.model.dataTypes.gBool,
    defaultValue: false,
  },
  isIntelligentLookup: {
    dataType: sap.galilei.model.dataTypes.gBool,
    defaultValue: false,
  },
  isTransformationFlow: {
    dataType: sap.galilei.model.dataTypes.gBool,
    defaultValue: false,
  },
  isReplicationFlow: {
    dataType: sap.galilei.model.dataTypes.gBool,
    defaultValue: false,
  },
  isTaskChain: {
    dataType: sap.galilei.model.dataTypes.gBool,
    defaultValue: false,
  },
  isBWProcessChain: {
    dataType: sap.galilei.model.dataTypes.gBool,
    defaultValue: false,
  },
  isSQLScriptProcedure: {
    dataType: sap.galilei.model.dataTypes.gBool,
    defaultValue: false,
  },
  isRemoveData: {
    dataType: sap.galilei.model.dataTypes.gBool,
    defaultValue: false,
  },
};

// References for Task
export const taskReferences = {
  parameters: {
    name: "parameters",
    contentType: "sap.cdw.taskchainmodeler.Parameter",
    isMany: true,
    isContainment: true,
  },
};

export enum TaskTypes {
  REMOTE_TABLE = 1,
  VIEW = 2,
  DATA_FLOW = 3,
  INTELLIGENT_LOOKUP = 4,
  TRANSFORMATION_FLOW = 5,
  REPLICATION_FLOW = 6,
  DELTA_LOCAL_TABLE = 7,
  TASK_CHAIN = 8,
  BW_PROCESS_CHAIN = 9,
  SQL_SCRIPT_PROCEDURE = 10,
  UNDEFINED = 0,
  API = 11,
  NOTIFICATION = 12,
}

export enum TechnicalTypes {
  REMOTE_TABLE = "@txtRemoteTable",
  VIEW = "@txtView",
  DATA_FLOW = "@txtDataFlow",
  INTELLIGENT_LOOKUP = "@txtIL",
  TRANSFORMATION_FLOW = "@txtTransformationFlow",
  REPLICATION_FLOW = "@txtReplicationFlow",
  DELTA_LOCAL_TABLE = "@txtDeltaLocalTable",
  TASK_CHAIN = "@txtTaskChain",
  BW_PROCESS_CHAIN = "@txtBWProcessChain",
  SQL_SCRIPT_PROCEDURE = "@txtSQLScriptProcedure",
  API = "@txtAPI",
  NOTIFICATION = "@txtNotification",
}

export enum ActivityTypes {
  REMOTE_TABLE = "@txtReplicate",
  REMOVE_REMOTE_DATA = "@txtRemoveData",
  VIEW = "@txtPersist",
  REMOVE_PERSISTED_DATA = "@txtRemovePersist",
  DATA_FLOW = "@txtRun",
  INTELLIGENT_LOOKUP = "@txtRun",
  TRANSFORMATION_FLOW = "@txtRun",
  REPLICATION_FLOW = "@txtRun",
  DELTA_LOCAL_TABLE = "@txtDelete",
  TASK_CHAIN = "@txtRun",
  BW_PROCESS_CHAIN = "@txtRun",
  SQL_SCRIPT_PROCEDURE = "@txtRun",
}

export enum NewActivityTypes {
  REPLICATE = "@txtReplicate",
  PERSIST = "@txtPersist",
  EXECUTE = "@txtRun",
  RUN_CHAIN = "@txtRun",
  REMOVE_DELETED_RECORDS = "@txtDelete",
  RUN = "@txtRun",
  REMOVE_REPLICATED_DATA = "@txtRemoveData",
  REMOVE_PERSISTED_DATA = "@txtRemovePersist",
  MERGE_FILES = "@txtMerge",
  OPTIMIZE_FILES = "@txtOptimize",
  VACUUM_FILES = "@txtVacuum",
  SEND_EMAIL = "@txtSendEmail",
}

export enum NewActivityTypesFullText {
  REPLICATE_REMOTE_TABLES = "@txtReplicateTable",
  PERSIST_VIEWS = "@txtPersistView",
  EXECUTE_DATA_FLOWS = "@txtRunDataFlow",
  EXECUTE_INTELLIGENT_LOOKUP = "@txtRunIL",
  EXECUTE_REPLICATION_FLOWS = "@txtRunRF",
  EXECUTE_TRANSFORMATION_FLOWS = "@txtRunTF",
  RUN_CHAIN_TASK_CHAINS = "@txtRunTC",
  REMOVE_DELETED_RECORDS_LOCAL_TABLE = "@txtDelete",
  RUN_SQL_SCRIPT_PROCEDURE = "@txtRunSQLScriptProcedure",
  RUN_BW_PROCESS_CHAIN = "@txtRunBW",
  REMOVE_REPLICATED_DATA_REMOTE_TABLES = "@txtRemoveReplicatedData",
  REMOVE_PERSISTED_DATA_VIEWS = "@txtRemovePersistedData",
  MERGE_FILES_LOCAL_TABLE = "@txtMergeData",
  OPTIMIZE_FILES_LOCAL_TABLE = "@txtOptimizeData",
  VACUUM_FILES_LOCAL_TABLE = "@txtVacuumData",
  RUN_API = "@txtRunAPI",
  SEND_EMAIL_NOTIFICATION = "@txtSendEmailNotification",
}

export enum ActivityTypesFullText {
  REMOTE_TABLE = "@txtReplicateTable",
  REMOVE_REMOTE_DATA = "@txtRemoveReplicatedData",
  VIEW = "@txtPersistView",
  REMOVE_PERSISTED_DATA = "@txtRemovePersistedData",
  DATA_FLOW = "@txtRunDataFlow",
  INTELLIGENT_LOOKUP = "@txtRunIL",
  TRANSFORMATION_FLOW = "@txtRunTF",
  REPLICATION_FLOW = "@txtRunRF",
  DELTA_LOCAL_TABLE = "@txtDelete",
  TASK_CHAIN = "@txtRunTC",
  BW_PROCESS_CHAIN = "@txtRunBW",
  SQL_SCRIPT_PROCEDURE = "@txtRunSQLScriptProcedure",
}

export enum NotificationCondition {
  OFF = "OFF",
  COMPLETED = "COMPLETED",
  FAILED = "FAILED",
  ANY = "ANY",
}

export enum OutputFill {
  ANY = "@sapUiNeutralElement",
  FAILED = "@sapUiNegativeElement",
  COMPLETED = "@sapUiPositiveElement",
}

export enum OutputFillIcon {
  ANY = "sap-icon://complete",
  FAILED = "sap-icon://decline",
  COMPLETED = "sap-icon://accept",
}

export enum OutputType {
  ANY = "ANY",
  FAILED = "FAILED",
  COMPLETED = "COMPLETED",
  NONE = "NONE",
}

export enum OPERATORS {
  AND = "ALL",
  OR = "ANY",
}
export enum NODE_TYPE {
  TASK = "TASK",
  ALL = "ALL",
  ANY = "ANY",
  START = "START",
}

export enum emailNotification {
  MESSAGE = "@notificationTaskEmailMessageTemplate",
  SUBJECT = "@notificationTaskEmailSubjectTemplate",
}
/**
 * This function will fetch the repository object with all required details
 * @param object
 * @returns
 */
export async function getRepositoryObject(spaceName, object, oNode?) {
  const details = [
    "id",
    "name",
    "@EndUserText.label",
    "kind",
    "modification_date",
    "deployment_date",
    "#deploymentExecutionStatus",
    "#objectStatus",
    "releaseStateValue",
    "#technicalType",
    "typeId",
    "owner", // modifier
  ];

  if (object.isSQLScriptProcedure || object.isBWProcessChain) {
    object.typeId = oNode?.applicationId || object?.repositoryCSN?.typeId;
    return RepositoryUtils.getNonRepositoryObject(oNode, spaceName, object.typeId, object.name, 1);
  } else if (object.isCrossSpace && !object.isRestApi && !object.isNotificationTask) {
    if (object?.crossSpaceName) {
      // Sometimes the user doesn't have permission on the source space,
      // and designObject service also doesn't support recursive cross space sharing,
      // we have to directly use shares/targetSpace to load data.
      let file = await getArtefactSharesForTarget(spaceName, details, [`name:${object.name}`]); // It's better to also filter source space on backend, but backend for now only support to filter guid of the source space, and frontend only has space name when the user don't have permission on the source space
      if (file && file.results && file.results.length > 0) {
        file = (file.results as [any]).filter((f) => f.name === object.name && f.spaceName === object.crossSpaceName);
        if (file && file.length === 1) {
          file = file[0];
          file.csn = file.content;
          delete file.content;
          if (typeof file.properties === "object") {
            // copy technical type and other properties to the root object
            Object.assign(file, file.properties);
            delete file.properties;
          }
        } else {
          file = undefined;
        }
      } else {
        file = undefined;
      }
      return Promise.resolve(file);
    }
  }
  return RepositoryUtils.getRepositoryObject(spaceName, object.name, { details });
}

export async function getRepositoryObjects(spaceName, object, filters) {
  const details = [
    "id",
    "name",
    "@EndUserText.label",
    "kind",
    "modification_date",
    "deployment_date",
    "#deploymentExecutionStatus",
    "#objectStatus",
    "releaseStateValue",
    "#technicalType",
    "typeId",
    "owner", // modifier
  ];

  return Repo.getModelList(spaceName, details, filters);
}

export default class Utils {
  static USAGE_TASKCHAIN_MODELER = "TASKCHAINMODELER";

  static UsageActions = {
    EXECUTE_TASKCHAIN: "execute",
    AUTO_LAYOUT: "autoLayout",
    ADD_TASK: "addTask",
  };
  static isDataFlow(data): boolean {
    return !!(
      (data && data["#technicalType"] && data["#technicalType"] === "DWC_DATAFLOW") ||
      data?.attributesMap?.technical_type?.value === "DWC_DATAFLOW"
    );
  }
  static isRemoteTable(data): boolean {
    return !!(
      (data && data["#technicalType"] && data["#technicalType"] === "DWC_REMOTE_TABLE") ||
      data?.attributesMap?.technical_type?.value === "DWC_REMOTE_TABLE"
    );
  }
  static isView(data): boolean {
    return !!(
      (data && data["#technicalType"] && data["#technicalType"] === "DWC_VIEW") ||
      data?.attributesMap?.technical_type?.value === "DWC_VIEW"
    );
  }
  static isIntelligentLookup(data): boolean {
    return !!(
      (data && data["#technicalType"] && data["#technicalType"] === "DWC_IDT") ||
      data?.attributesMap?.technical_type?.value === "DWC_IDT"
    );
  }
  static isTransformationFlow(data): boolean {
    return !!(
      (data && data["#technicalType"] && data["#technicalType"] === "DWC_TRANSFORMATIONFLOW") ||
      data?.attributesMap?.technical_type?.value === "DWC_TRANSFORMATIONFLOW"
    );
  }
  static isReplicationFlow(data): boolean {
    return !!(
      (data && data["#technicalType"] && data["#technicalType"] === "DWC_REPLICATIONFLOW") ||
      data?.attributesMap?.technical_type?.value === "DWC_REPLICATIONFLOW"
    );
  }
  static isDeltaTable(data): boolean {
    return !!(
      (data && data["#technicalType"] && data["#technicalType"] === "DWC_LOCAL_TABLE") ||
      data?.attributesMap?.technical_type?.value === "DWC_LOCAL_TABLE"
    );
  }
  static isTaskChain(data): boolean {
    return !!(
      (data && data["#technicalType"] && data["#technicalType"] === "DWC_TASKCHAIN") ||
      data?.attributesMap?.technical_type?.value === "DWC_TASKCHAIN"
    );
  }
  static isBWProcessChain(data): boolean {
    return !!(
      (data && data.typeId && data.typeId === "BW_PROCESS_CHAIN") ||
      data?.attributesMap?.type?.value === "BW_PROCESS_CHAIN"
    );
  }
  static isSQLScriptProcedure(data): boolean {
    return !!(
      (data && data.typeId && data.typeId === "SQL_SCRIPT_PROCEDURE") ||
      data?.attributesMap?.type?.value === "SQL_SCRIPT_PROCEDURE"
    );
  }
  static isRemoveData(data): boolean {
    return !!(this.isRemoteTable(data) || this.isView(data));
  }
  static isCrossSpace(data): boolean {
    return !!(data && data.crossSpaceName && data.crossSpaceName !== getSpaceName());
  }

  static getTaskType(obj) {
    if (obj) {
      if (obj.isDataFlow) {
        return TaskTypes.DATA_FLOW;
      } else if (obj.isRemoteTable) {
        return TaskTypes.REMOTE_TABLE;
      } else if (obj.isView) {
        return TaskTypes.VIEW;
      } else if (obj.isIntelligentLookup) {
        return TaskTypes.INTELLIGENT_LOOKUP;
      } else if (obj.isTransformationFlow) {
        return TaskTypes.TRANSFORMATION_FLOW;
      } else if (obj.isReplicationFlow) {
        return TaskTypes.REPLICATION_FLOW;
      } else if (obj.isDeltaTable) {
        return TaskTypes.DELTA_LOCAL_TABLE;
      } else if (obj.isTaskChain) {
        return TaskTypes.TASK_CHAIN;
      } else if (obj.isBWProcessChain) {
        return TaskTypes.BW_PROCESS_CHAIN;
      } else if (obj.isSQLScriptProcedure) {
        return TaskTypes.SQL_SCRIPT_PROCEDURE;
      } else if (obj.isRestApi) {
        return TaskTypes.API;
      } else if (obj.isNotificationTask) {
        return TaskTypes.NOTIFICATION;
      }
    }
    return TaskTypes.UNDEFINED;
  }

  static async getObjActivity() {
    if (Utils.canSupportDeleteData()) {
      const activity = await getActivityData(getSpaceName());
      return activity;
    }
    return [];
  }

  static getApplicationId(obj) {
    if (Utils.canSupportDeleteData() && obj) {
      const data = obj.resource?.model?.applicationIdActivity;
      let applicationId: string | undefined;
      const technicalType = obj["#technicalType"] ? obj["#technicalType"] : obj?.repositoryCSN?.["#technicalType"];
      if (!technicalType && obj.applicationId !== undefined) {
        const filterItem = data.filter((x) => x.applicationId === obj.applicationId);
        return filterItem[0].applicationId;
      }
      data.forEach((item) => {
        if (technicalType && technicalType === item.technicalType && applicationId === undefined) {
          applicationId = item.applicationId;
        } else if (obj?.repositoryCSN?.typeId && obj?.repositoryCSN?.typeId === item.applicationId) {
          applicationId = item.applicationId;
        }
      });
      return applicationId;
    } else if (obj) {
      if (obj.isDataFlow) {
        return ApplicationId.DATA_FLOWS;
      } else if (obj.isRemoteTable) {
        return ApplicationId.REMOTE_TABLES;
      } else if (obj.isView) {
        return ApplicationId.VIEWS;
      } else if (obj.isIntelligentLookup) {
        return ApplicationId.INTELLIGENT_LOOKUP;
      } else if (obj.isTransformationFlow) {
        return ApplicationId.TRANSFORMATION_FLOWS;
      } else if (obj.isReplicationFlow) {
        return ApplicationId.REPLICATION_FLOWS;
      } else if (obj.isDeltaTable) {
        return ApplicationId.LOCAL_TABLE;
      } else if (obj.isTaskChain) {
        return ApplicationId.TASK_CHAINS;
      } else if (obj.isBWProcessChain) {
        return ApplicationId.BW_PROCESS_CHAIN;
      } else if (obj.isSQLScriptProcedure) {
        return ApplicationId.SQL_SCRIPT_PROCEDURE;
      }
    }
    return "";
  }

  static getActivity(obj) {
    if (Utils.canSupportDeleteData() && obj) {
      const data = obj.resource?.model?.applicationIdActivity;

      const technicalType = obj["#technicalType"] ? obj["#technicalType"] : obj?.repositoryCSN?.["#technicalType"];
      const filterItem =
        technicalType === undefined ? undefined : data.filter((x) => x.technicalType === technicalType);
      const filterLen = filterItem ? filterItem.length : 0;
      if (!filterItem && obj?.repositoryCSN?.typeId) {
        const filter = data.filter((x) => x.applicationId === obj?.repositoryCSN?.typeId);
        if (filter && filter.length === 1) {
          return filter[obj.selectedActivityIndex].activity;
        }
      } else if (filterLen > 0) {
        return filterItem[obj.selectedActivityIndex].activity;
      } else if (!filterItem && obj?.applicationId) {
        const filter = data.filter((x) => x.applicationId === obj.applicationId);
        return filter[0].activity;
      }
    } else if (obj) {
      if (obj.isDataFlow || obj.isIntelligentLookup || obj.isTransformationFlow) {
        return Activity.EXECUTE;
      } else if (obj.isRemoteTable) {
        return Activity.REPLICATE;
      } else if (obj.isReplicationFlow) {
        return Activity.EXECUTE;
      } else if (obj.isView) {
        return Activity.PERSIST;
      } else if (obj.isDeltaTable) {
        return Activity.REMOVE_DELETED_RECORDS;
      } else if (obj.isTaskChain) {
        return Activity.RUN_CHAIN;
      } else if (obj.isBWProcessChain) {
        return Activity.RUN_BW_CHAIN;
      } else if (obj.isSQLScriptProcedure) {
        return Activity.RUN_SQL_SCRIPT_PROCEDURE;
      }
    }
    return "";
  }

  static getActivityText(obj) {
    if (Utils.canSupportDeleteData() && obj) {
      const data = obj.resource?.model?.applicationIdActivity;

      let filterItem;
      if (obj.applicationId) {
        filterItem = data.filter((x) => x.applicationId === obj.applicationId);
      } else if (obj?.["#technicalType"] || obj?.repositoryCSN?.["#technicalType"]) {
        const technicalType = obj["#technicalType"] ? obj["#technicalType"] : obj?.repositoryCSN["#technicalType"];
        filterItem = data.filter((x) => x.technicalType === technicalType);
      }
      const filterLen = filterItem ? filterItem.length : 0;
      // Check for non-repository objects
      if (!filterItem && obj?.repositoryCSN?.typeId) {
        const filterItem = data.filter((x) => x.applicationId === obj?.repositoryCSN?.typeId);
        return NewActivityTypes[filterItem[0].activity];
      } else if (filterLen > 0) {
        // Check for repository objects
        return NewActivityTypes[filterItem[obj.selectedActivityIndex].activity];
      }
    } else if (obj) {
      if (obj.isDataFlow) {
        return ActivityTypes.DATA_FLOW;
      } else if (obj.isRemoteTable) {
        return ActivityTypes.REMOTE_TABLE;
      } else if (obj.isView) {
        return ActivityTypes.VIEW;
      } else if (obj.isIntelligentLookup) {
        return ActivityTypes.INTELLIGENT_LOOKUP;
      } else if (obj.isTransformationFlow) {
        return ActivityTypes.TRANSFORMATION_FLOW;
      } else if (obj.isReplicationFlow) {
        return ActivityTypes.REPLICATION_FLOW;
      } else if (obj.isDeltaTable) {
        return ActivityTypes.DELTA_LOCAL_TABLE;
      } else if (obj.isTaskChain) {
        return ActivityTypes.TASK_CHAIN;
      } else if (obj.isBWProcessChain) {
        return ActivityTypes.BW_PROCESS_CHAIN;
      } else if (obj.isSQLScriptProcedure) {
        return ActivityTypes.SQL_SCRIPT_PROCEDURE;
      }
    }
    return "";
  }

  static getActivityFullText(obj) {
    if (Utils.canSupportDeleteData() && obj) {
      const data = obj.resource?.model?.applicationIdActivity;

      let filterItem, val;
      if (obj.applicationId) {
        filterItem = data.filter((x) => x.applicationId === obj.applicationId);
      } else if (obj["#technicalType"] || obj?.repositoryCSN["#technicalType"]) {
        const technicalType = obj["#technicalType"] ? obj["#technicalType"] : obj?.repositoryCSN["#technicalType"];
        filterItem = data.filter((x) => x.technicalType === technicalType);
      }
      const filterLen = filterItem ? filterItem.length : 0;
      // Check for non-repository objects
      if (!filterItem && obj?.repositoryCSN?.typeId) {
        filterItem = data.filter((x) => x.applicationId === obj?.repositoryCSN?.typeId);
        val = filterItem ? filterItem[0].activity + "_" + filterItem[0].applicationId : "";
        return NewActivityTypesFullText[val];
      } else if (filterLen > 0) {
        // Check for repository objects
        val = filterItem ? filterItem[obj.selectedActivityIndex].activity + "_" + filterItem[0].applicationId : "";
        return NewActivityTypesFullText[val];
      }
    } else if (obj) {
      if (obj.isDataFlow) {
        return ActivityTypesFullText.DATA_FLOW;
      } else if (obj.isRemoteTable) {
        return ActivityTypesFullText.REMOTE_TABLE;
      } else if (obj.isView) {
        return ActivityTypesFullText.VIEW;
      } else if (obj.isIntelligentLookup) {
        return ActivityTypesFullText.INTELLIGENT_LOOKUP;
      } else if (obj.isTransformationFlow) {
        return ActivityTypesFullText.TRANSFORMATION_FLOW;
      } else if (obj.isReplicationFlow) {
        return ActivityTypesFullText.REPLICATION_FLOW;
      } else if (obj.isDeltaTable) {
        return ActivityTypesFullText.DELTA_LOCAL_TABLE;
      } else if (obj.isTaskChain) {
        return ActivityTypesFullText.TASK_CHAIN;
      } else if (obj.isBWProcessChain) {
        return ActivityTypesFullText.BW_PROCESS_CHAIN;
      } else if (obj.isSQLScriptProcedure) {
        return ActivityTypesFullText.SQL_SCRIPT_PROCEDURE;
      }
    }
    return "";
  }

  static getTypeText(obj) {
    if (obj) {
      if (obj.isDataFlow) {
        return TechnicalTypes.DATA_FLOW;
      } else if (obj.isRemoteTable) {
        return TechnicalTypes.REMOTE_TABLE;
      } else if (obj.isView) {
        return TechnicalTypes.VIEW;
      } else if (obj.isIntelligentLookup) {
        return TechnicalTypes.INTELLIGENT_LOOKUP;
      } else if (obj.isTransformationFlow) {
        return TechnicalTypes.TRANSFORMATION_FLOW;
      } else if (obj.isReplicationFlow) {
        return TechnicalTypes.REPLICATION_FLOW;
      } else if (obj.isDeltaTable) {
        return TechnicalTypes.DELTA_LOCAL_TABLE;
      } else if (obj.isTaskChain) {
        return TechnicalTypes.TASK_CHAIN;
      } else if (obj.isBWProcessChain) {
        return TechnicalTypes.BW_PROCESS_CHAIN;
      } else if (obj.isSQLScriptProcedure) {
        return TechnicalTypes.SQL_SCRIPT_PROCEDURE;
      } else if (obj.isRestApi) {
        return TechnicalTypes.API;
      } else if (obj.isNotificationTask) {
        return TechnicalTypes.NOTIFICATION;
      }
    }
    return "";
  }

  static getHeaderIcon(obj) {
    switch (Utils.getTaskType(obj)) {
      case TaskTypes.DATA_FLOW:
        return "sap-icon://" + getFileTypeIcon(FileType.DataFlow);
      case TaskTypes.REMOTE_TABLE:
        return "sap-icon://" + getFileTypeIcon(FileType.RemoteTable);
      case TaskTypes.VIEW:
        return "sap-icon://" + getFileTypeIcon(FileType.View);
      case TaskTypes.INTELLIGENT_LOOKUP:
        return "sap-icon://" + getIcon(Type.IntelligentLookup);
      case TaskTypes.TRANSFORMATION_FLOW:
        return "sap-icon://" + getIcon(Type.TransformationFlow);
      case TaskTypes.REPLICATION_FLOW:
        return "sap-icon://" + getIcon(Type.ReplicationFlow);
      case TaskTypes.DELTA_LOCAL_TABLE:
        return "sap-icon://" + getIcon(Type.LocalTable);
      case TaskTypes.TASK_CHAIN:
        return "sap-icon://" + getIcon(Type.TaskChain);
      case TaskTypes.BW_PROCESS_CHAIN:
        return "sap-icon://" + getIcon(Type.BWProcessChain);
      case TaskTypes.SQL_SCRIPT_PROCEDURE:
        return "sap-icon://" + getIcon(Type.SQLScriptProcedure);
      case TaskTypes.API:
        return "sap-icon://" + getIcon(Type.API);
      case TaskTypes.NOTIFICATION:
        return "sap-icon://" + getIcon(Type.Notification);

      default:
        break;
    }
    if (obj?.classDefinition?.name === "Model") {
      return "sap-icon://sac/requirement-diagram";
    }
    if (obj?.isOperator) {
      return "sap-icon://SAP-icons-TNT/aggregator";
    }
    return "sap-icon://" + getIcon(Type.Unknown);
  }

  static fillTaskType(oNode, oNodeObject) {
    const objApplicationId = oNode.taskIdentifier.applicationId;
    switch (objApplicationId) {
      case ApplicationId.REMOTE_TABLES:
        oNodeObject.isRemoteTable = true;
        oNodeObject.isRemoveData = Utils.canSupportDeleteData();
        oNodeObject.applicationId = TechnicalTypes.REMOTE_TABLE;
        oNodeObject.icon = getFileTypeIcon(FileType.RemoteTable);
        break;
      case ApplicationId.VIEWS:
        oNodeObject.isView = true;
        oNodeObject.isRemoveData = Utils.canSupportDeleteData();
        oNodeObject.icon = getFileTypeIcon(FileType.View);
        break;
      case ApplicationId.DATA_FLOWS:
        oNodeObject.isDataFlow = true;
        oNodeObject.icon = getFileTypeIcon(FileType.DataFlow);
        break;
      case ApplicationId.INTELLIGENT_LOOKUP:
        oNodeObject.isIntelligentLookup = true;
        oNodeObject.icon = getIcon(Type.IntelligentLookup);
        break;
      case ApplicationId.TRANSFORMATION_FLOWS:
        oNodeObject.isTransformationFlow = true;
        oNodeObject.icon = getIcon(Type.TransformationFlow);
        break;
      case ApplicationId.REPLICATION_FLOWS:
        oNodeObject.isReplicationFlow = true;
        oNodeObject.icon = getIcon(Type.ReplicationFlow);
        break;
      case ApplicationId.LOCAL_TABLE:
        oNodeObject.isDeltaTable = true;
        oNodeObject.icon = getIcon(Type.LocalTable);
        break;
      case ApplicationId.TASK_CHAINS:
        oNodeObject.isTaskChain = true;
        oNodeObject.icon = getIcon(Type.TaskChain);
        break;
      case ApplicationId.BW_PROCESS_CHAIN:
        oNodeObject.isBWProcessChain = true;
        oNodeObject.icon = getIcon(Type.BWProcessChain);
        break;
      case ApplicationId.SQL_SCRIPT_PROCEDURE:
        oNodeObject.isSQLScriptProcedure = true;
        oNodeObject.icon = getIcon(Type.SQLScriptProcedure);
        break;
      case ApplicationId.API:
        oNodeObject.isRestApi = true;
        oNodeObject.icon = getIcon(Type.API);
        break;
      case ApplicationId.NOTIFICATION:
        oNodeObject.isNotificationTask = true;
        oNodeObject.icon = getIcon(Type.Notification);
        break;
      default:
        break;
    }
    oNodeObject.applicationId = objApplicationId;
  }

  static assignTaskType(oData) {
    oData.isDataFlow = Utils.isDataFlow(oData);
    oData.isRemoteTable = Utils.isRemoteTable(oData);
    oData.isView = Utils.isView(oData);
    oData.isIntelligentLookup = Utils.isIntelligentLookup(oData);
    oData.isDeltaTable = Utils.isDeltaTable(oData);
    oData.isTaskChain = Utils.isTaskChain(oData);
    if (Utils.canSupportTransformationFlow()) {
      oData.isTransformationFlow = Utils.isTransformationFlow(oData);
    }
    if (Utils.canSupportReplicationFlow()) {
      oData.isReplicationFlow = Utils.isReplicationFlow(oData);
    }

    if (Utils.canSupportBWProcessChain()) {
      oData.isBWProcessChain = Utils.isBWProcessChain(oData);
    }
    if (Utils.canSupportSQLScriptProcedure()) {
      oData.isSQLScriptProcedure = Utils.isSQLScriptProcedure(oData);
    }
    if (Utils.canSupportDeleteData()) {
      oData.isRemoveData = Utils.isRemoveData(oData);
    }
    if (Utils.canSupportSharedTaskChain()) {
      oData.isCrossSpace = Utils.isCrossSpace(oData);
    }
  }

  static supportedTaskTypes(oNode) {
    return (
      oNode.isRemoteTable ||
      oNode.isDataFlow ||
      oNode.isView ||
      oNode.isIntelligentLookup ||
      oNode.isTaskChain ||
      (oNode.isTransformationFlow && Utils.canSupportTransformationFlow()) ||
      (oNode.isReplicationFlow && Utils.canSupportReplicationFlow()) ||
      oNode.isDeltaTable ||
      (oNode.isBWProcessChain && Utils.canSupportBWProcessChain()) ||
      (oNode.isSQLScriptProcedure && Utils.canSupportSQLScriptProcedure()) ||
      (oNode.isRestApi && Utils.canSupportAPITask()) ||
      (oNode.isNotificationTask && Utils.canSupportNotificationTask())
    );
  }

  static getBrowserOptions(): IBrowserOptions {
    return {
      editorCapabilities: [EditorCapabilities.REMOTES_CAPABILITIES_DIS],
      hideSharedObjects: !Utils.canSupportSharedTaskChain(),
      supportIntelligentLookup: true,
      supportTaskChain: true,
      remoteSourceSupported: false,
      supportDataFlows: true,
      supportTransformationFlows: Utils.canSupportTransformationFlow(),
      supportReplicationFlows: Utils.canSupportReplicationFlow(),
      hideLocalTables: false,
      supportDeltaTable: !Utils.canSupportLocalTables(),
      isOthersTabEnabled: Utils.canSupportOthersTab(),
      supportSharedTaskChain: Utils.canSupportSharedTaskChain(),
    };
  }

  static canSupportTransformationFlow() {
    return isTransformationFlowEnabled();
  }

  static canSupportReplicationFlow() {
    return isReplicationFlowEnabled();
  }

  static canSupportOthersTab() {
    let features: any;
    if (sap?.ui?.getCore instanceof Function) {
      features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
    }
    return !!(features && (features.DWCO_INFRA_TASKS_BW_PROCESS_CHAIN || features.DWCO_INFRA_TASKS_PROCEDURES));
  }

  static canSupportBWProcessChain() {
    let features: any;
    if (sap?.ui?.getCore instanceof Function) {
      features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
    }
    return !!(features && features.DWCO_INFRA_TASKS_BW_PROCESS_CHAIN);
  }

  static canSupportSQLScriptProcedure() {
    let features: any;
    if (sap?.ui?.getCore instanceof Function) {
      features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
    }
    return !!(features && features.DWCO_INFRA_TASKS_PROCEDURES);
  }

  static canSupportSharedTaskChain() {
    let features: any;
    if (sap?.ui?.getCore instanceof Function) {
      features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
    }
    return !!(features && features.DWCO_TASK_FRAMEWORK_SHARE_TASK_CHAINS);
  }

  static canSupportDeleteData() {
    let features: any;
    if (sap?.ui?.getCore instanceof Function) {
      features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
    }
    return !!(features && features.DWCO_TASK_CHAINS_REMOVE_DATA);
  }

  static canSupportAPITask() {
    let features: any;
    if (sap?.ui?.getCore instanceof Function) {
      features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
    }
    return !!(features && features.DWCO_INFRA_TASKS_API_TASK);
  }

  static canSupportLocalTables() {
    let isHdlfStorage = false;
    let features: any;
    if (sap?.ui?.getCore instanceof Function) {
      features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
    }
    if (features && features.DWCO_LOCAL_TABLE_FILES_BWPUSH_MERGE) {
      isHdlfStorage = UISpaceCapabilities.get().hasCapability(getSpaceName(), "hdlfStorage");
    }
    return !!isHdlfStorage;
  }

  static canSupportNotificationTask() {
    let features: any;
    if (sap?.ui?.getCore instanceof Function) {
      features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
    }
    return !!(features && features.DWCO_TASK_FRAMEWORK_NOTIFICATION_TASK);
  }
  /**
   *
   *
   * @static
   * @param {sap.ui.model.Model} model
   * @param {string} text
   * @param {any[]} [parameters=[]]
   * @returns {string}
   * @memberof Utils
   */
  static localizeText(model: sap.ui.model.Model, text: string, parameters: any[] = []): string {
    try {
      const i18nText = model.getResourceBundle().getText(text, parameters);
      return i18nText;
    } catch (e) {
      return text;
    }
  }

  // show only the basic status
  static getObjectDeploymentStatus(object): DeploymentStatus {
    if (object.deployment_date === null) {
      // RepositoryUtils.getObjectFileDeploymentStatus checks deployment_date = undefined
      // eslint-disable-next-line camelcase
      object.deployment_date = undefined;
    }
    return RepositoryUtils.getObjectFileDeploymentStatus(object);
  }

  // dataType conversion to cds
  static fnConvertType(sNewType): string {
    if (sNewType && sNewType.toLowerCase().indexOf("cds.") === 0) {
      return sNewType;
    }
    sNewType = sNewType && sNewType.toUpperCase();
    switch (sNewType) {
      case "INT":
      case "SMALLINT":
      case "TINYINT":
        sNewType = "INTEGER";
        break;
      case "CHAR":
      case "NCHAR":
      case "VARCHAR":
      case "NVARCHAR":
        sNewType = "STRING";
        break;
      case "NUMERIC":
      case "REAL":
      case "NUMBER": // from value
        sNewType = "DOUBLE";
        break;
      case "date,timestamp,seconddate":
        sNewType = "TIMESTAMP";
        break;
      case "BINARY":
      case "VARBINARY":
      case "BLOB":
      case "CLOB":
      case "NCLOB":
        sNewType = "BINARY";
        break;
      case "BIGINT":
        sNewType = "INTEGER64";
        break;
      case "ABAP DATE":
      case "SECONDDATE":
        sNewType = "DATE";
        break;
      case "SMALLDECIMAL":
        sNewType = "DECIMAL";
        break;
      case "ST_GEOMETRY":
        sNewType = "HANA_ST_GEOMETRY";
        break;
    }
    return (sNewType && CDSDataType && CDSDataType[sNewType]) || "UNKNOWN";
  }

  static getDWCMetadata(csnData: any): any {
    const details = {
      columnsMap: new Map(),
      columns: [],
    };
    if (csnData) {
      const parameter = csnData.metadata.parameters;
      Object.keys(parameter).forEach((key) => {
        const oParam = parameter[key];
        if (oParam) {
          const type = Utils.fnConvertType(oParam.type);
          if (type) {
            const column: any = {
              name: key,
              dataType: type,
              nativeDataType: oParam.type,
              length: oParam.length,
              precision: oParam.precision,
              scale: oParam.scale,
            };
            if (oParam.default) {
              column.default = oParam.default;
            }
            details.columns.push(column);
            details.columnsMap.set(column.name, column); // used in change management
          }
        }
      });
    }
    return details;
  }

  static removeObjectFromChangeManagement(oModel, oNode) {
    if (oNode && (oNode.isBWProcessChain || oNode.isSQLScriptProcedure) && oModel.changeManagement?.modifiedObjects) {
      for (const modifiedObjectsItem of oModel.changeManagement.modifiedObjects) {
        let index: undefined;
        for (const autoFixObjectsItem of modifiedObjectsItem.autofixedObjects) {
          // if procedure node is deleted
          if (oNode.isSQLScriptProcedure && autoFixObjectsItem.changeInfo.procedureDeletedNodes.length > 0) {
            for (const deletedNode of autoFixObjectsItem.changeInfo.procedureDeletedNodes) {
              if (deletedNode.elementName === oNode.name) {
                index = modifiedObjectsItem.autofixedObjects.indexOf(autoFixObjectsItem);
                break;
              }
            }
          } else if (oNode.isBWProcessChain && autoFixObjectsItem.changeInfo.bwDeletedNodes.length > 0) {
            // if BW node is deleted
            for (const deletedNode of autoFixObjectsItem.changeInfo.bwDeletedNodes) {
              if (deletedNode.elementName === oNode.name) {
                index = modifiedObjectsItem.autofixedObjects.indexOf(autoFixObjectsItem);
                break;
              }
            }
          }
          if (index !== undefined) {
            break;
          }
        }
        if (index !== undefined) {
          oModel.changeManagement.modifiedObjects.splice(index, 1);
          break;
        }
      }
    }
  }

  static callConnection<T>(url: string) {
    return ServiceCall.request<T>({
      url: `${url}`,
      type: HttpMethod.GET,
      dataType: DataType.JSON,
    }).then((p) => p?.data);
  }

  static async getConnectionList() {
    const spaceName = getSpaceName();
    const url = `tf/${spaceName}/taskchains/connections/http`;
    window["performanceLogger"]?.enterMethod({ name: `generichttp: space '${spaceName}'` }, { isService: false });
    const aChildren = await this.callConnection(url);
    window["performanceLogger"]?.leaveMethod({ name: `generichttp: space '${spaceName}'` });
    return aChildren;
  }

  static async getApiConnectionList(obj) {
    const connectionList = await this.getConnectionList();
    // obj.resource.model.connectionList = connectionList;
    return await connectionList;
  }

  static getApiSelectedConnection(obj) {
    let connectionList = obj?.resource?.model?.connectionList;
    const conn = connectionList?.find((elem) => elem.technicalName === obj.connectionName)?.technicalName;
    return conn ? (conn === "EMPTY_CONNECTION" ? "" : conn) : "";
  }

  static getConnectionUrl(obj, oModel?) {
    let connectionList = obj?.resource?.model?.connectionList || oModel?.connectionList;
    const selectedConnection = connectionList?.find((elem) => elem.technicalName === obj.connectionName);
    const url = selectedConnection?.url || "";
    return { url: url, state: url ? "None" : "Error" };
  }
}

interface IRESTAPI_Properties {
  connectionName: string;
  apiGenericProperties?: {
    request: IRESTAPI_Header[];
  };
  connectionNameValueState?: string;
  selectedConnectionURL?: string;
  executionStatus: any;
  executionErrorDetails?: any;
  technicalNameValueState?: string;
}

interface IRESTAPI_Header {
  key: string;
  value: string;
  isDefaultHeader: boolean;
  keyValueState?: string;
  valueValueState?: string;
}

interface IREST_Request {
  method: string;
  apiPath: string;
  mode?: string;
  csrfToken?: boolean;
  csrfTokenUrl?: string;
  body?: any;
  apiPathValueState?: string;
  csrfTokenUrlValueState?: string;
  bodyValueState?: string;
}

interface IREST_Response {
  from: string;
  responseSelectedIndex?: number;
  body?: any;
  jobIdJsonPath?: string;
  successIndicatorPath?: string;
  successIndicatorCondition?: string;
  successIndicatorValue?: string;
  errorIndicatorPath?: string;
  errorIndicatorCondition?: string;
  errorIndicatorValue?: string;
  errorReasonPath?: string;
  logsPath?: string;
  jobIDPathVS?: string;
  successIndicatorPathVS?: string;
  successIndicatorValueVS?: string;
  errorIndicatorPathVS?: string;
  errorIndicatorValueVS?: string;
  errorReasonPathVS?: string;
}

// Properties for REST API Type
export const RESTAPI_Properties = {
  connectionName: {
    dataType: sap.galilei.model.dataTypes.gString,
    defaultValue: "EMPTY_CONNECTION",
  },
  baseUrl: {
    dataType: sap.galilei.model.dataTypes.gString,
    defaultValue: "",
  },
  isRestApi: {
    dataType: sap.galilei.model.dataTypes.gBool,
    defaultValue: false,
  },
  connectionNameValueState: {
    dataType: sap.galilei.model.dataTypes.gString,
    defaultValue: "Error",
    onCascadeChange: function (oEventArgs) {
      const object = oEventArgs?.instance;
      const oModel = object?.resource?.model;
      oModel.validate();
    },
  },
  selectedConnectionURL: {
    dataType: sap.galilei.model.dataTypes.gString,
    defaultValue: "",
  },
  executionStatus: {
    name: "executionStatus",
    defaultValue: {},
  },
  executionErrorDetails: {
    name: "executionErrorDetails",
  },
  technicalNameValueState: {
    dataType: sap.galilei.model.dataTypes.gString,
    defaultValue: "None",
    onCascadeChange: function (oEventArgs) {
      const object = oEventArgs?.instance;
      const oModel = object?.resource?.model;
      oModel.validate();
    },
  },
};

// Properties for REST API - Request
export const REST_API_ReqProperties: { [key in keyof IREST_Request]: any } = {
  method: {
    dataType: sap.galilei.model.dataTypes.gString,
    defaultValue: "",
  },
  apiPath: {
    dataType: sap.galilei.model.dataTypes.gString,
    defaultValue: "",
  },
  mode: {
    dataType: sap.galilei.model.dataTypes.gString,
    defaultValue: "",
  },
  csrfToken: {
    dataType: sap.galilei.model.dataTypes.gBool,
    defaultValue: false,
    onCascadeChange: function (oEventArgs) {
      const object = oEventArgs?.instance;
      const oModel = object?.resource?.model;
      oModel.validate();
    },
  },
  csrfTokenUrl: {
    dataType: sap.galilei.model.dataTypes.gString,
    defaultValue: "",
  },
  body: {
    dataType: sap.galilei.model.dataTypes.gString,
    defaultValue: "",
  },
  apiPathValueState: {
    dataType: sap.galilei.model.dataTypes.gString,
    defaultValue: "Error",
    isVolatile: true,
    isInternal: true,
    onCascadeChange: function (oEventArgs) {
      const object = oEventArgs?.instance;
      const oModel = object?.resource?.model;
      oModel.validate();
      // sap.cdw.taskchainmodeler.Validation.validateTask(object, undefined, true);
    },
  },
  csrfTokenUrlValueState: {
    dataType: sap.galilei.model.dataTypes.gString,
    defaultValue: "None",
    isVolatile: true,
    isInternal: true,
    onCascadeChange: function (oEventArgs) {
      const object = oEventArgs?.instance;
      const oModel = object?.resource?.model;
      oModel.validate();
    },
  },
  bodyValueState: {
    dataType: sap.galilei.model.dataTypes.gString,
    defaultValue: "None",
    isVolatile: true,
    isInternal: true,
    onCascadeChange: function (oEventArgs) {
      const object = oEventArgs?.instance;
      const oModel = object?.resource?.model;
      oModel.validate();
    },
  },
};

// Properties for REST API API - Response
export const REST_API_ResProperties: { [key in keyof IREST_Response]: any } = {
  from: {
    dataType: sap.galilei.model.dataTypes.gString,
    defaultValue: "",
  },
  responseSelectedIndex: {
    dataType: sap.galilei.model.dataTypes.gInteger,
    defaultValue: 0,
  },
  body: {
    dataType: sap.galilei.model.dataTypes.gString,
    defaultValue: "",
  },
  jobIdJsonPath: {
    dataType: sap.galilei.model.dataTypes.gString,
    defaultValue: "",
  },
  successIndicatorCondition: {
    dataType: sap.galilei.model.dataTypes.gString,
    defaultValue: "",
  },
  successIndicatorValue: {
    dataType: sap.galilei.model.dataTypes.gString,
    defaultValue: "",
  },
  errorIndicatorPath: {
    dataType: sap.galilei.model.dataTypes.gString,
    defaultValue: "",
  },
  errorIndicatorCondition: {
    dataType: sap.galilei.model.dataTypes.gString,
    defaultValue: "",
  },
  errorIndicatorValue: {
    dataType: sap.galilei.model.dataTypes.gString,
    defaultValue: "",
  },
  errorReasonPath: {
    dataType: sap.galilei.model.dataTypes.gString,
    defaultValue: "",
  },
  logsPath: {
    dataType: sap.galilei.model.dataTypes.gString,
    defaultValue: "",
  },
  successIndicatorPath: {
    dataType: sap.galilei.model.dataTypes.gString,
    defaultValue: "",
  },
  jobIDPathVS: {
    dataType: sap.galilei.model.dataTypes.gString,
    defaultValue: "None",
    isVolatile: true,
    isInternal: true,
    onCascadeChange: function (oEventArgs) {
      const object = oEventArgs?.instance;
      const oModel = object?.resource?.model;
      oModel.validate();
    },
  },
  successIndicatorPathVS: {
    dataType: sap.galilei.model.dataTypes.gString,
    defaultValue: "None",
    isVolatile: true,
    isInternal: true,
    onCascadeChange: function (oEventArgs) {
      const object = oEventArgs?.instance;
      const oModel = object?.resource?.model;
      oModel.validate();
    },
  },
  successIndicatorValueVS: {
    dataType: sap.galilei.model.dataTypes.gString,
    defaultValue: "None",
    isVolatile: true,
    isInternal: true,
    onCascadeChange: function (oEventArgs) {
      const object = oEventArgs?.instance;
      const oModel = object?.resource?.model;
      oModel.validate();
    },
  },
  errorIndicatorPathVS: {
    dataType: sap.galilei.model.dataTypes.gString,
    defaultValue: "None",
    isVolatile: true,
    isInternal: true,
    onCascadeChange: function (oEventArgs) {
      const object = oEventArgs?.instance;
      const oModel = object?.resource?.model;
      oModel.validate();
    },
  },
  errorIndicatorValueVS: {
    dataType: sap.galilei.model.dataTypes.gString,
    defaultValue: "None",
    isVolatile: true,
    isInternal: true,
    onCascadeChange: function (oEventArgs) {
      const object = oEventArgs?.instance;
      const oModel = object?.resource?.model;
      oModel.validate();
    },
  },
  errorReasonPathVS: {
    dataType: sap.galilei.model.dataTypes.gString,
    defaultValue: "None",
    isVolatile: true,
    isInternal: true,
    onCascadeChange: function (oEventArgs) {
      const object = oEventArgs?.instance;
      const oModel = object?.resource?.model;
      oModel.validate();
    },
  },
};

// Properties for REST API - Header
export const REST_API_Header: { [key in keyof IRESTAPI_Header]: any } = {
  key: {
    dataType: sap.galilei.model.dataTypes.gString,
    defaultValue: "",
  },
  value: {
    dataType: sap.galilei.model.dataTypes.gString,
    defaultValue: "",
  },
  isDefaultHeader: {
    dataType: sap.galilei.model.dataTypes.gBool,
    defaultValue: false,
  },
  keyValueState: {
    dataType: sap.galilei.model.dataTypes.gString,
    defaultValue: "None",
    onCascadeChange: function (oEventArgs) {
      const object = oEventArgs?.instance;
      sap.cdw.taskchainmodeler.Validation.validateTask(object, undefined, true);
    },
  },
  valueValueState: {
    dataType: sap.galilei.model.dataTypes.gString,
    defaultValue: "None",
    onCascadeChange: function (oEventArgs) {
      const object = oEventArgs?.instance;
      sap.cdw.taskchainmodeler.Validation.validateTask(object, undefined, true);
    },
  },
};

export const REST_API_References = {
  requestHeaders: {
    contentType: "sap.cdw.taskchainmodeler.RequestHeader",
    isMany: true,
    isContainment: true,
  },
  invokeAPI: {
    contentType: "sap.cdw.taskchainmodeler.InvokeAPIRequest",
    isMany: false,
    isContainment: true,
  },
  statusAPI: {
    contentType: "sap.cdw.taskchainmodeler.StatusAPIRequest",
    isMany: false,
    isContainment: true,
  },
  logsAPI: {
    contentType: "sap.cdw.taskchainmodeler.LogsAPIRequest",
    isMany: false,
    isContainment: true,
  },
};

export const fromType = {
  body: "BODY",
  code: "CODE",
  codeandlocation: "CODE_AND_LOCATION_HEADER",
};

// Properties for Notification Task
interface INotification_Properties {
  isNotificationTask: boolean;
  technicalNameValueState?: string;
  notificationSubject?: string;
  notificationMessage?: string;
}

export const notificationProperties: { [key in keyof INotification_Properties]: any } = {
  isNotificationTask: {
    dataType: sap.galilei.model.dataTypes.gBool,
    defaultValue: false,
  },
  technicalNameValueState: {
    dataType: sap.galilei.model.dataTypes.gString,
    defaultValue: "None",
    isVolatile: true,
    isInternal: true,
    onCascadeChange: function (oEventArgs) {
      const object = oEventArgs?.instance;
      const oModel = object?.resource?.model;
      oModel.validate();
    },
  },
  notificationSubject: {
    dataType: sap.galilei.model.dataTypes.gString,
    defaultValue: emailNotification.SUBJECT,
  },
  notificationMessage: {
    dataType: sap.galilei.model.dataTypes.gString,
    defaultValue: emailNotification.MESSAGE,
  },
};

export async function fetchApplicationId(sSpaceId: string, parentView?: any): Promise<any> {
  if (sSpaceId) {
    const url = `/resources/spaces?spaceids=${encodeURIComponent(sSpaceId)}`;
    return ServiceCall.request<any>({
      url: url,
      type: HttpMethod.GET,
      contentType: ContentType.APPLICATION_JSON,
    })
      .then((resp) => resp.data[sSpaceId])
      .catch((error) => {
        // parentView.setBusy(false);
        MessageHandler.exception({
          message: error,
          exception: error,
        });
      });
  }
}

export async function fetchDefaultSparkSettingForDelete() {
  const url = `/tf/tasks/spark`;
  return ServiceCall.request<any>({
    url: url,
    type: HttpMethod.GET,
    contentType: ContentType.APPLICATION_JSON,
  })
    .then((resp) => resp.data)
    .catch((error) => {
      MessageHandler.exception({
        message: error,
        exception: error,
      });
    });
}

export function getApplicationInfo(
  applications: any[],
  bsetCustomModel?: any,
  settings?: any,
  view?: any,
  oObject?: any
) {
  const oApplicationInfo = [];
  applications?.forEach((app) => {
    oApplicationInfo.push({
      index: app.identifier.index,
      label: app.identifier.label,
      maxCore: app.calculated["spark.cores.max"],
      maxMemory: app.calculated["spark.memory.max"],
    });
  });
  let mergeDefault = "";
  let mergeCustom;
  let optimizeCustom;
  let optimizeDefault = "";
  let transformationDefault = "";
  let transformationCustom;
  applications?.forEach((app) => {
    if (settings) {
      if (settings?.MERGE_FILES?.sparkApplication === app.identifier.index) {
        mergeCustom = app.identifier.index;
      }
      if (settings?.OPTIMIZE_FILES?.sparkApplication === app.identifier.index) {
        optimizeCustom = app.identifier.index;
      }
      if (settings?.sparkRemoteSource === app.identifier.index) {
        transformationCustom = app.identifier.index;
      }
    }

    if (app.preference.merge === true) {
      mergeDefault = app.identifier.index;
    }
    if (app.preference.optimize === true) {
      optimizeDefault = app.identifier.index;
    }
    if (app.preference.transformation === true) {
      transformationDefault = app.identifier.index;
    }
  });
  if (bsetCustomModel) {
    view.getModel("ltfAppModel").setProperty("/mergeDefault", mergeDefault);
    view.getModel("ltfAppModel").setProperty("/mergeAppSelected", mergeCustom || mergeDefault);
    view.getModel("ltfAppModel").setProperty("/optimizeDefault", optimizeDefault);
    view.getModel("ltfAppModel").setProperty("/optimizeAppSelected", optimizeCustom || optimizeDefault);
    view
      .getModel("ltfAppModel")
      .setProperty("/transformationAppSelected", transformationCustom || transformationDefault);
    oObject.resource.applyUndoableAction(
      async function () {
        if (Utils.getActivityText(oObject) === NewActivityTypes.MERGE_FILES) {
          oObject.applicationDefault = mergeCustom || mergeDefault;
        } else if (Utils.getActivityText(oObject) === NewActivityTypes.OPTIMIZE_FILES) {
          oObject.applicationDefault = optimizeCustom || optimizeDefault;
        } else if (
          isSparkSelectionVacuumEnabled() &&
          Utils.getActivityText(oObject) === NewActivityTypes.VACUUM_FILES
        ) {
          const defaultSparkSettings = await fetchDefaultSparkSettingForDelete();
          defaultSparkSettings.forEach((item) => {
            if (item.activity === "VACUUM_FILES") {
              this.defaultVacuumSparkSetting = item.defaultSparkAppIndex;
            }
          });
          oObject.applicationDefault = this.defaultVacuumSparkSetting;
        } else if (oObject.isTransformationFlow) {
          oObject.applicationDefault = transformationCustom || transformationDefault;
        }
      },
      "update application Default",
      /* protectedFromUndo */ true
    );
  }
  return oApplicationInfo;
}

export async function fetchLocalTableMetrics(
  sSpaceId: string,
  objectId: string,
  bColumns: boolean = false
): Promise<any> {
  let url = `/monitor/${encodeURIComponent(sSpaceId)}/localTables/${encodeURIComponent(objectId)}`;
  url = bColumns ? `${url}/columns` : `${url}?includeBusinessNames=true`;
  return ServiceCall.request<any>({
    url: url,
    type: HttpMethod.GET,
    contentType: ContentType.APPLICATION_JSON,
  })
    .then((resp) => (bColumns ? (resp.data.columns as any) : (resp.data as any)))
    .catch((error) => {});
}

export async function fetchZorderColumns(sSpaceId: string, objectId: string): Promise<any> {
  const url = `/monitor/${encodeURIComponent(sSpaceId)}/localtables/${encodeURIComponent(objectId)}`;

  return ServiceCall.request<any>({
    url: url,
    type: HttpMethod.GET,
    contentType: ContentType.APPLICATION_JSON,
  })
    .then((resp) => resp.data as any)
    .catch((error) => {
      console.error("Error fetching local table metrics:", error);
      return null;
    });
}

export async function fetchTFMetrics(sSpaceId: string, objectId: string): Promise<any> {
  let url = `transformationflow/${encodeURIComponent(sSpaceId)}/runtimesettings/${encodeURIComponent(objectId)}`;
  return ServiceCall.request<any>({
    url: url,
    type: HttpMethod.GET,
    contentType: ContentType.APPLICATION_JSON,
  })
    .then((resp) => resp.data as any)
    .catch((error) => {});
}
