/**
 * Diagram Implementation
 * (c) Copyright 2019 SAP AG. All rights reserved
 *
 * @format
 */

import { Activity, ApplicationId } from "../../../tasklog/utility/Constants";
import Utils, { NODE_TYPE, OPERATORS, OutputFill, OutputType } from "../utils";

sap.galilei.namespace("sap.cdw.taskchainmodeler", function (nsLocal) {
  "use strict";

  const nsCommonModel = sap.cdw.commonmodel;

  /**
   * @class
   * DiagramImpl implements all methods related to Diagram object
   */
  nsLocal.DiagramImpl = sap.galilei.core.defineClass({
    fullClassName: "sap.cdw.taskchainmodeler.DiagramImpl",

    statics: {
      TASKCHAIN_DIAGRAM_CHANNEL: "taskChainDiagram",
      HIGHLIGHT_EVENT: "tcModelChanged",
      UNDO_REDO_EVENT: "tcUndoRedo",
      DIAGRAM_CHANNEL: "TaskchainDiagramChannel",
      PROCESSING_EVENT: "ProcessingEvent",

      /**
       * Perform an autolayout on the diagram
       * @function
       * @name autoLayout
       * @param {*} oDiagramEditor
       * @param {*} bShowGlobalView
       * @param {*} oSymbolSelection symbol to select if auto layout succeeded
       */
      autoLayout: function (oDiagramEditor, bShowGlobalView, oSymbolSelection) {
        const ANIMATION_DURATION = 500;
        const ANIMATION_DELAY = 100;
        const self = this,
          oDiagram = oDiagramEditor && oDiagramEditor.diagram,
          aAllSymbols = oDiagram && oDiagram.getAllSymbols(),
          aIsolatedSymbols = [],
          aConnectedSymbols = [],
          isVerticalView = oDiagram?.resource?.model.isVerticalView;
        let aLinks, index, max, oSymbol;

        if (oDiagram) {
          for (index = 0, max = aAllSymbols.length; index < max; index++) {
            oSymbol = aAllSymbols[index];
            if (oSymbol.isRowSymbol) {
              continue;
            }
            if (oSymbol.classDefinition.name === "BeginSymbol") {
              aConnectedSymbols.unshift(oSymbol);
            } else if (oSymbol.isBoundarySymbol) {
              // aIsolatedSymbols.push(oSymbol);
              continue;
            } else if (!oSymbol.isLinkSymbol) {
              aLinks = oSymbol.getAllLinkSymbolss ? oSymbol.getAllLinkSymbolss() : oSymbol.getLinkSymbols();
              if (!aLinks || aLinks.length === 0) {
                aIsolatedSymbols.push(oSymbol);
              } else {
                aConnectedSymbols.push(oSymbol);
              }
            } else {
              aConnectedSymbols.push(oSymbol);
            }
          }

          // Creates a multi-algorithme auto-layouter.
          const oMultiAutoLayout = new (sap.galilei.ui.editor.layout as any).DiagramMultiAutoLayout(); // ToDo: as 'any'
          if (!oSymbolSelection && oDiagramEditor.selectedSymbols.length > 0) {
            oSymbolSelection = oDiagramEditor.selectedSymbols.get(0);
          }
          // console.log("Auto Layout isVerticalView: " + isVerticalView);
          oDiagramEditor.unhighlightAllSymbols();
          oDiagramEditor.unselectAllSymbols();
          oMultiAutoLayout.multiLayoutDiagram(
            {
              isVerticalCenter: true, // Center vertically isolated and connected sub-graphs
              animationDuration: 500,
            },
            [
              aConnectedSymbols, // Connected symbols
              aIsolatedSymbols, // Isolated symbols
            ],
            [
              // use Layered layout for connected symbols
              {
                isSupportMultiEdges: true,
                isDirected: true,
                isSupportPorts: true,
                isSupportSubGraph: true,
                isHorizontalCenter: false,
                // Specify the layouter name and its options (klay.js options)
                layout: {
                  name: "klayjs", // klay.js only supports the "Layered" algorithm
                  // direction: "DOWN", // Layout direction
                  direction: isVerticalView ? "DOWN" : "RIGHT", // Layout direction
                  nodePlace: "SIMPLE", // LINEAR_SEGMENTS, SIMPLE
                  edgeRouting: "ORTHOGONAL",
                  spacing: 40, // Distance between nodes
                  edgeSpacingFactor: 0.4,
                },
              },
              // use Stack layout for isolated symbols
              {
                isSupportMultiEdges: true,
                isHorizontalCenter: true,
                layout: {
                  name: "Stack", // Use "Stack" layouter,
                  // orientation: "vertical",
                  orientation: isVerticalView ? "vertical" : "horizontal",
                  wrap: false,
                  maxWidth: 1920,
                },
              },
            ],
            oDiagramEditor,
            function (oAutoLayout) {
              // If success, show the global view.
              if (bShowGlobalView) {
                oDiagramEditor.showGlobalView(false);
              }
              setTimeout(function () {
                if (oSymbolSelection) {
                  oDiagramEditor.unselectAllSymbols();
                  oDiagramEditor.drawAllSymbols(
                    new (sap.galilei.ui.common as any).TransitionDefinition({
                      duration: ANIMATION_DURATION,
                      ease: "easeCubicOut",
                      delay: ANIMATION_DELAY,
                    })
                  ); // ToDo: as 'any'
                  setTimeout(function () {
                    oDiagramEditor.selectSymbol(oSymbolSelection);
                  }, ANIMATION_DURATION);
                }
                self.refreshValidationDecorators(oDiagram, true);
              }, 200);
            },
            function (error) {
              throw error;
            }
          );
        }
      },

      /**
       * Refreshes correpsponding diagram validations decoreators by given model
       * @param {*} oModel model instance
       * @param {*} bRecreate true to delete and create
       */
      refreshValidationDecoratorsByModel: function (oModel, bRecreate) {
        const oDiagram = nsLocal.DiagramImpl.getDiagram(oModel);
        if (oDiagram) {
          nsLocal.DiagramImpl.refreshValidationDecorators(oDiagram, bRecreate);
        }
      },

      refreshAllSymbols: function (oModel, diagramEditor) {
        const ANIMATION_DURATION = 500;
        const ANIMATION_DELAY = 100;
        if (diagramEditor) {
          diagramEditor.drawAllSymbols(
            new (sap.galilei.ui.common as any).TransitionDefinition({
              duration: ANIMATION_DURATION,
              ease: "easeCubicOut",
              delay: ANIMATION_DELAY,
            })
          );
        }
      },

      refreshValidationDecorators: function (oDiagram, bRecreate) {
        const aAllSymbols = oDiagram && oDiagram.getAllSymbols();
        if (sap.ui && sap.ui.getCore instanceof Function) {
          sap.ui
            .getCore()
            .getEventBus()
            .publish(nsCommonModel.Validation.VALIDATION_CHANNEL, nsCommonModel.Validation.REFRESH_VALIDATION_EVENT, {
              symbols: aAllSymbols,
              diagram: oDiagram,
              recreate: bRecreate,
            });
        }
      },

      findLinkSymbol: function (oDiagram, oSourceNode, oTargetNode) {
        const aSymbols = oDiagram.symbols,
          nLength = aSymbols && aSymbols.length;
        let oSymbol, nIndex;
        if (oDiagram) {
          for (nIndex = 0; nIndex < nLength; nIndex++) {
            oSymbol = aSymbols.get(nIndex);
            if (oSymbol.classDefinition.name === "LinkSymbol") {
              if (
                oSymbol.sourceSymbol &&
                oSymbol.targetSymbol &&
                oSymbol.getSourceSymbolObject() === oSourceNode &&
                oSymbol.getTargetSymbolObject() === oTargetNode
              ) {
                return oSymbol;
              }
            }
          }
        }
      },

      getDiagram: function (oModel) {
        let nIndex;
        const nLength = oModel && oModel.diagrams ? oModel.diagrams.length : 0;
        for (nIndex = 0; nIndex < nLength; nIndex++) {
          if (oModel.diagrams.get(nIndex).classDefinition.qualifiedName === "sap.cdw.taskchainmodeler.ui.Diagram") {
            return oModel.diagrams.get(nIndex);
          }
        }
      },

      /**
       * Tells if the given object class definition name is handled by this diagram
       * @param {*} sClassDefinitionName the object class definition qualified name
       */
      isSupportedObjectClass: function (sClassDefinitionName) {
        const oSupportedClasses = {
          "sap.cdw.taskchainmodeler.Node": true,
          "sap.cdw.taskchainmodeler.Link": true,
          "sap.cdw.taskchainmodeler.Task": true,
          "sap.cdw.taskchainmodeler.Operation": true,
          "sap.cdw.taskchainmodeler.PlaceHolder": true,
          "sap.cdw.taskchainmodeler.Start": true,
          "sap.cdw.taskchainmodeler.Parameter": true,
        };
        return !!oSupportedClasses[sClassDefinitionName];
      },

      /**
       * Create symbols for new objects and launch an autolayout
       * @function
       * @name adjustdiagramContent
       * @param {*} oOptions for instance
       * {
       *  model: the model in case of first creation/load
       *  selection: symbol(s) selection if needed
       * }
       */
      adjustDiagramContent: function (oDiagramEditor, oOptions) {
        const oModel = oOptions.model || oDiagramEditor.model,
          oResource = oModel.resource,
          oSelection = oOptions && oOptions.selection,
          oNodes = (oModel && oModel.nodes?.toArray()) || [];
        let oDiagram = oModel && nsLocal.DiagramImpl.getDiagram(oModel),
          bAutolayout = oOptions?.autoLayout,
          oSymbol,
          oNode,
          oTargetSymbol,
          aTargetSymbols,
          oLinkSymbol,
          oSymbolSelection,
          nIndex,
          bBeginSymbolExists,
          nLength,
          isVerticalView = oModel.isVerticalView,
          bRedrawDiagram = oModel.bRedrawDiagram;
        nsLocal.DiagramImpl.publishDiagramProcessing({ finished: false });
        if (!oDiagram) {
          // create a new diagram
          oDiagram = new sap.cdw.taskchainmodeler.ui.Diagram(oResource); // ToDo: as 'any'
          // Adds the diagram in model
          oModel.diagrams.push(oDiagram);
          oDiagramEditor.diagram = oDiagram;
        }

        // remove all orphelin link symbols
        const aLinkSymbols = oDiagram.symbols.selectAllObjects({
          "classDefinition.name": "LinkSymbol",
        });
        nLength = aLinkSymbols ? aLinkSymbols.length : 0;
        for (nIndex = 0; nIndex < nLength; nIndex++) {
          oLinkSymbol = aLinkSymbols.get(nIndex);
          if (oLinkSymbol?.object?.isPlaceHolder) {
            continue;
          }
          if (
            !oLinkSymbol.sourceSymbol ||
            !oLinkSymbol.targetSymbol ||
            !oLinkSymbol.getSourceSymbolObject() ||
            !oLinkSymbol.getTargetSymbolObject()
          ) {
            if (!bRedrawDiagram) {
              oDiagramEditor.detachSymbol(oLinkSymbol);
              oLinkSymbol.deleteObject();
            }
          } else if (!oLinkSymbol.object || (oLinkSymbol.object && oLinkSymbol.object.isDeleted)) {
            if (!bRedrawDiagram) {
              oDiagramEditor.detachSymbol(oLinkSymbol);
              oLinkSymbol.deleteObject();
            }
          }
        }

        if (oModel.startNode) {
          oNodes.push(oModel.startNode);
        }
        nLength = oNodes ? oNodes.length : 0;
        // check if all objects have their symbols
        for (nIndex = 0; nIndex < nLength; nIndex++) {
          oNode = oNodes[nIndex];
          if (!nsLocal.DiagramImpl.isSupportedObjectClass(oNode.classDefinition.qualifiedName)) {
            // not supported object
            continue;
          }
          if (nsLocal.DiagramImpl.relatedSymbols(oNode, oDiagram).length === 0) {
            // New object not yet displayed, we need to create a symbol
            if (oNode.isStart) {
              oSymbol = this.createBeginSymbol(oDiagramEditor);
            } else {
              oSymbol = nsLocal.DiagramImpl.createSymbol(oDiagramEditor, oNode);
            }
          } else {
            oSymbol = nsLocal.DiagramImpl.relatedSymbols(oNode, oDiagram)[0];
            if (oSymbol && oSymbol.container !== oDiagram) {
              const nOldPos = oSymbol.container && oSymbol.container.symbols.indexOf(oSymbol);
              if (nOldPos >= 0) {
                oSymbol.container.symbols.removeAt(nOldPos);
              }
              oDiagram.symbols.push(oSymbol);
            }
          }

          if (
            oSymbol &&
            oNode.classDefinition.qualifiedName === "sap.cdw.taskchainmodeler.Task" &&
            oSymbol.boundarySymbols?.length === 0
          ) {
            nsLocal.DiagramImpl.addBoundarySymbols(oSymbol, oDiagram);
          }
          // if (oSymbol && !(oSymbol.isDrawn || bRedrawDiagram)) {
          if (oSymbol && (!oSymbol.isDrawn || bRedrawDiagram)) {
            oDiagramEditor.drawSymbol(oSymbol);
            if (oNode.classDefinition.qualifiedName === "sap.cdw.taskchainmodeler.Task") {
              oSymbol.updateBoundarySymbols();
            }
            bAutolayout = true;
          }
          if (
            oNode.classDefinition.qualifiedName === "sap.cdw.taskchainmodeler.Operation" ||
            oNode.classDefinition.qualifiedName === "sap.cdw.taskchainmodeler.Start" ||
            oNode.classDefinition.qualifiedName === "sap.cdw.taskchainmodeler.PlaceHolder" ||
            oNode.classDefinition.qualifiedName === "sap.cdw.taskchainmodeler.Output"
          ) {
            oSymbol.updateBoundarySymbols();
          }

          // create link symbols
          const aLinks = oNode.links;
          const len = aLinks.length || 0;
          for (let index = 0; index < len; index++) {
            const oLink = aLinks.get(index);
            if (oSymbol && oLink.target && !nsLocal.DiagramImpl.findLinkSymbol(oDiagram, oNode, oLink.target)) {
              aTargetSymbols = nsLocal.DiagramImpl.relatedSymbols(oLink.target, oDiagram);
              if (aTargetSymbols.length > 0) {
                oTargetSymbol = aTargetSymbols[0];
              } else {
                // eslint-disable-next-line no-underscore-dangle
                oTargetSymbol = nsLocal.DiagramImpl._createSymbol(oDiagramEditor, oLink.target);
              }
              if (
                oTargetSymbol &&
                oNode.classDefinition.qualifiedName === "sap.cdw.taskchainmodeler.Task" &&
                oTargetSymbol.boundarySymbols?.length === 0
              ) {
                nsLocal.DiagramImpl.addBoundarySymbols(oTargetSymbol, oDiagram);
              }
              if (oTargetSymbol && (!oTargetSymbol.isDrawn || bRedrawDiagram)) {
                oDiagramEditor.drawSymbol(oTargetSymbol);
                if (oLink.target.classDefinition.qualifiedName === "sap.cdw.taskchainmodeler.Task") {
                  oTargetSymbol.updateBoundarySymbols();
                }
                bAutolayout = true;
              }
              // create also association
              if (oTargetSymbol) {
                // eslint-disable-next-line no-underscore-dangle
                const oLinkSymbols = nsLocal.DiagramImpl.relatedSymbols(oLink, oDiagram);
                if (oLinkSymbols.length > 0) {
                  oLinkSymbol = oLinkSymbols[0];
                } else {
                  oLinkSymbol = nsLocal.DiagramImpl._createSymbol(
                    oDiagramEditor,
                    oLink,
                    "sap.cdw.taskchainmodeler.ui.LinkSymbol"
                  );
                }
                if (oLinkSymbol) {
                  oLinkSymbol.sourceSymbol = nsLocal.DiagramImpl.getOutputSymbol(oSymbol, oLink);
                  oLinkSymbol.targetSymbol = nsLocal.DiagramImpl.getInputSymbol(oTargetSymbol);
                  oDiagramEditor.drawSymbol(oLinkSymbol);
                  bAutolayout = true;
                }
              }
            }
          }

          // Create place holder symbols
          if (
            oSymbol &&
            oNode.classDefinition.qualifiedName === "sap.cdw.taskchainmodeler.Task" /* && oNode.placeHolderNodes > 0*/
          ) {
            nsLocal.DiagramImpl.addPlaceHolderSymbols(oSymbol, oDiagram, oDiagramEditor);
          }
        }
        if (bRedrawDiagram) {
          this.bRedrawDiagram = false;
          // reset to bRedrawDiagram = false
        }
        if (bAutolayout) {
          // A change has been detected, we need to relayout the diagram
          if (oSelection) {
            oSymbolSelection = nsLocal.DiagramImpl.relatedSymbols(oSelection, oDiagram);
            if (oSymbolSelection && oSymbolSelection.length > 0) {
              oSymbolSelection = oSymbolSelection[0];
            } else {
              oSymbolSelection = undefined;
            }
          }
          nsLocal.DiagramImpl.autoLayout(oDiagramEditor, /** bShowGloabalView*/ false, oSymbolSelection);
          nsLocal.DiagramImpl.publishDiagramProcessing({ finished: true, delay: 2000 });
        } else {
          nsLocal.DiagramImpl.publishDiagramProcessing({ finished: true, delay: 50 });
        }
      },

      deleteObjectAndSymbols: function (editor, options) {
        const object = options.object;
        const relatedSymbols = object?.relatedSymbols;
        if (relatedSymbols?.length > 0) {
          editor.deleteSymbols(relatedSymbols, options.deleteObject);
        } else {
          object?.deleteObject();
        }
      },

      createBeginSymbol: function (oDiagramEditor, oSymbol) {
        const oDiagram = oDiagramEditor.diagram;
        const { oBeginSymbol, oOutputSymbol } = nsLocal.DiagramImpl._createBeginSymbol(oDiagramEditor, oDiagram);
        if (oSymbol) {
          const oLink = nsLocal.ModelImpl.createObject(
            "sap.cdw.taskchainmodeler.Link",
            {
              statusRequired: OutputType.NONE,
            },
            oDiagram.resource.model
          );
          const oLinkSymbol2 = nsLocal.ModelImpl.createObject(
            "sap.cdw.taskchainmodeler.ui.LinkSymbol",
            {
              object: oLink,
            },
            oDiagram
          );
          oLinkSymbol2.sourceSymbol = oOutputSymbol;
          oLinkSymbol2.targetSymbol = nsLocal.DiagramImpl.getInputSymbol(oSymbol);
          oDiagramEditor.drawSymbol(oLinkSymbol2);
        }
        return oBeginSymbol;
      },

      /**
       * Diagram structure is being to change, publish the event
       * @function
       * @name publishIntermediateNodeBeingCreated
       * @param {*} oOptions the options for instance {
       *  finished: true/false,
       *  delay: (optional) delay in milliseconds
       * }
       */
      publishDiagramProcessing: function (oOptions) {
        if (sap.ui && sap.ui.getCore instanceof Function) {
          sap.ui
            .getCore()
            .getEventBus()
            .publish(nsLocal.DiagramImpl.DIAGRAM_CHANNEL, nsLocal.DiagramImpl.PROCESSING_EVENT, oOptions);
        }
      },

      /**
       * Returns related symbols of the given object and context (diagram)
       * @param {*} oObject the galilei object
       * @param {*} oDiagram the context
       */
      relatedSymbols(oObject, oDiagram) {
        return sap.galilei.ui.symbol.getObjectSymbols(oObject, oDiagram);
      },

      /**
       * Returns the symbol class name from model object
       *
       * @param {*} oGalileiObject
       */
      _getSymbolClassName: function (oGalileiObject) {
        let sName;
        const oClass = oGalileiObject && oGalileiObject.classDefinition,
          sObjectClassName = oClass && oClass.name;
        if (sObjectClassName === "Task") {
          sName = "sap.cdw.taskchainmodeler.ui.TaskSymbol";
        } else if (sObjectClassName === "Operation") {
          sName = "sap.cdw.taskchainmodeler.ui.OperationSymbol";
        } else if (sObjectClassName === "Link") {
          sName = "sap.cdw.taskchainmodeler.ui.LinkSymbol";
        } else if (sObjectClassName === "Output") {
          sName = "sap.cdw.taskchainmodeler.ui.OutputSymbol";
        } else if (sObjectClassName === "Input") {
          sName = "sap.cdw.taskchainmodeler.ui.InputSymbol";
        }
        return sName;
      },

      /**
       * Returns the object class name from symbol qualified class name
       *
       * @param {*} sSymbolQualifiedName symbol qualified name
       */
      _getObjectClassName: function (sSymbolQualifiedName) {
        let sName;
        if (sSymbolQualifiedName === "sap.cdw.taskchainmodeler.ui.TaskSymbol") {
          sName = "sap.cdw.taskchainmodeler.Task";
        } else if (sSymbolQualifiedName === "sap.cdw.taskchainmodeler.ui.OperationSymbol") {
          sName = "sap.cdw.taskchainmodeler.Operation";
        } else if (sSymbolQualifiedName === "sap.cdw.taskchainmodeler.ui.LinkSymbol") {
          sName = "sap.cdw.taskchainmodeler.Link";
        } else if (sSymbolQualifiedName === "sap.cdw.taskchainmodeler.ui.PlaceHolderSymbol") {
          sName = "sap.cdw.taskchainmodeler.PlaceHolder";
        } else if (sSymbolQualifiedName === "sap.cdw.taskchainmodeler.ui.OutputSymbol") {
          sName = "sap.cdw.taskchainmodeler.Output";
        } else if (sSymbolQualifiedName === "sap.cdw.taskchainmodeler.ui.BeginSymbol") {
          sName = "sap.cdw.taskchainmodeler.Start";
        }
        return sName;
      },

      /**
       * Creates the symbol
       * @param {*} oDiagram diagram instance
       * @param {*} oGalileiObject the model object
       * @param {*} sSymbolClassName the symbol class name
       */
      _createSymbol: function (oDiagramEditor, oGalileiObject, sSymbolClassName) {
        let oNewSymbol;
        const oDiagram = oDiagramEditor.diagram,
          // eslint-disable-next-line no-underscore-dangle
          sEffectiveSymbolClassName = sSymbolClassName || nsLocal.DiagramImpl._getSymbolClassName(oGalileiObject);
        if (sEffectiveSymbolClassName) {
          oNewSymbol = nsLocal.ModelImpl.createObject(
            sEffectiveSymbolClassName,
            {
              object: oGalileiObject, // Attaches the symbol to the object
            },
            oDiagram
          );
          // Create Input and output symbols
          if (
            oNewSymbol &&
            (sEffectiveSymbolClassName === "sap.cdw.taskchainmodeler.ui.TaskSymbol" ||
              sEffectiveSymbolClassName === "sap.cdw.taskchainmodeler.ui.OperationSymbol")
          ) {
            nsLocal.DiagramImpl.addBoundarySymbols(oNewSymbol, oDiagram);
          }
        }
        if (oNewSymbol && oDiagramEditor) {
          oDiagramEditor.registerSymbolEvents(oNewSymbol);
        }
        return oNewSymbol;
      },

      addBoundarySymbols: function (oSymbol, oDiagram) {
        if (oSymbol?.boundarySymbols?.length) {
          return;
        }
        const oInputSymbol = nsLocal.ModelImpl.createObject("sap.cdw.taskchainmodeler.ui.InputSymbol", {}, oDiagram);
        oSymbol.symbols.push(oInputSymbol);
        function addOutputSymbol(status) {
          const key = Object.keys(OutputType)[Object.values(OutputType).indexOf(status)];
          const oGalileiOutputObject = nsLocal.ModelImpl.createObject(
            "sap.cdw.taskchainmodeler.Output",
            { statusRequired: status, fill: OutputFill[key] },
            oSymbol.object
          );
          const oOutputSymbol = nsLocal.ModelImpl.createObject(
            "sap.cdw.taskchainmodeler.ui.OutputSymbol",
            { object: oGalileiOutputObject },
            oDiagram
          );
          oSymbol.symbols.push(oOutputSymbol);
        }
        if (oSymbol?.classDefinition?.name === "TaskSymbol") {
          addOutputSymbol(OutputType.ANY);
          addOutputSymbol(OutputType.COMPLETED);
          addOutputSymbol(OutputType.FAILED);
        } else if (oSymbol?.classDefinition?.name === "OperationSymbol") {
          const oGalileiOutputObject = nsLocal.ModelImpl.createObject(
            "sap.cdw.taskchainmodeler.Output",
            { statusRequired: OutputType.NONE, fill: "none" },
            oSymbol.object
          );
          const oOutputSymbol = nsLocal.ModelImpl.createObject(
            "sap.cdw.taskchainmodeler.ui.OutputSymbol",
            { object: oGalileiOutputObject },
            oDiagram
          );
          oSymbol.symbols.push(oOutputSymbol);
        }
        oSymbol.updateBoundarySymbols();
      },

      addPlaceHolderSymbols: function (oSymbol, oDiagram, oDiagramEditor) {
        const totalNodeCount = oSymbol?.object?.placeHolderNodes || 0;
        const totalSymbolCount = oSymbol?.getPlaceHolderSymbols()?.length || 0;
        if (totalNodeCount > totalSymbolCount) {
          for (let i = totalSymbolCount; i < totalNodeCount; i++) {
            const oPlaceHolder = nsLocal.ModelImpl.createPlaceHolderObject(oDiagram.resource.model);
            const oPlaceHolderSymbol = nsLocal.ModelImpl.createObject(
              "sap.cdw.taskchainmodeler.ui.PlaceHolderSymbol",
              { object: oPlaceHolder },
              oDiagram
            );
            const oInputSymbol = nsLocal.ModelImpl.createObject(
              "sap.cdw.taskchainmodeler.ui.InputSymbol",
              {},
              oDiagram
            );
            oPlaceHolderSymbol.symbols.push(oInputSymbol);
            oDiagramEditor.drawSymbol(oInputSymbol);
            oDiagramEditor.drawSymbol(oPlaceHolderSymbol);
            oPlaceHolderSymbol.updateBoundarySymbols();
            if (oPlaceHolderSymbol && oDiagramEditor) {
              oDiagramEditor.registerSymbolEvents(oPlaceHolderSymbol);
            }
            const oLink = nsLocal.ModelImpl.createObject(
              "sap.cdw.taskchainmodeler.Link",
              {
                source: oSymbol.object,
                statusRequired: OutputType.COMPLETED,
                target: oPlaceHolder,
              },
              oDiagram.resource.model
            );
            const oLinkSymbol = nsLocal.ModelImpl.createObject(
              "sap.cdw.taskchainmodeler.ui.LinkSymbol",
              {
                object: oLink,
              },
              oDiagram
            );

            oLinkSymbol.sourceSymbol = nsLocal.DiagramImpl.getOutputSymbol(oSymbol, oLinkSymbol.object);
            oLinkSymbol.targetSymbol = nsLocal.DiagramImpl.getInputSymbol(oPlaceHolderSymbol);
            oDiagramEditor.drawSymbol(oLinkSymbol);
            if (oLinkSymbol && oDiagramEditor) {
              oDiagramEditor.registerSymbolEvents(oLinkSymbol);
            }
          }
        }
      },

      addOperationSymbol: function (oSymbol, oDiagramEditor, operator) {
        const oDiagram = oDiagramEditor.diagram;
        const aOpers = oSymbol.resource.model.nodes.filter((obj) => obj.isOperator);
        const operatorName = nsLocal.ModelImpl.getUniqueAlias(aOpers, operator);
        const oOperation = nsLocal.ModelImpl.createObject(
          "sap.cdw.taskchainmodeler.Operation",
          { operationType: operator, name: operatorName },
          oSymbol.resource.model
        );
        const oOperationSymbol = nsLocal.ModelImpl.createObject(
          "sap.cdw.taskchainmodeler.ui.OperationSymbol",
          { object: oOperation },
          oDiagram
        );
        const oInputSymbol = nsLocal.ModelImpl.createObject("sap.cdw.taskchainmodeler.ui.InputSymbol", {}, oDiagram);
        const oGalileiOutputObject = nsLocal.ModelImpl.createObject(
          "sap.cdw.taskchainmodeler.Output",
          { statusRequired: OutputType.NONE, fill: "none" },
          oOperationSymbol.object
        );
        const oOutputSymbol = nsLocal.ModelImpl.createObject(
          "sap.cdw.taskchainmodeler.ui.OutputSymbol",
          { object: oGalileiOutputObject },
          oDiagram
        );
        oOperationSymbol.symbols.push(oInputSymbol);
        oOperationSymbol.symbols.push(oOutputSymbol);
        if (oOperationSymbol && oDiagramEditor) {
          oDiagramEditor.registerSymbolEvents(oOperationSymbol);
        }
        oDiagramEditor.drawSymbol(oOperationSymbol);
        oDiagramEditor.drawSymbol(oInputSymbol);
        oDiagramEditor.drawSymbol(oOutputSymbol);
        const oLink = nsLocal.ModelImpl.createLink(oSymbol.object, oOperation);
        oLink.statusRequired = OutputType.COMPLETED;
        const oLinkSymbol = nsLocal.ModelImpl.createObject(
          "sap.cdw.taskchainmodeler.ui.LinkSymbol",
          {
            object: oLink,
          },
          oDiagram
        );

        oLinkSymbol.sourceSymbol = nsLocal.DiagramImpl.getOutputSymbol(oSymbol, oLinkSymbol.object);
        oLinkSymbol.targetSymbol = nsLocal.DiagramImpl.getInputSymbol(oOperationSymbol);
        oDiagramEditor.drawSymbol(oLinkSymbol);
        if (oLinkSymbol && oDiagramEditor) {
          oDiagramEditor.registerSymbolEvents(oLinkSymbol);
        }
        oOperationSymbol.updateBoundarySymbols();
      },

      /**
       *  Helper function to create a symbol from an object
       * @function
       * @name createSymbol
       * @param {*} oDiagramEditor the diagram editor
       * @param {*} oObject galilei object
       */
      createSymbol: function (oDiagramEditor, oObject) {
        const oDiagram = oDiagramEditor.diagram,
          oResource = oDiagram && oDiagram.resource;
        let aRelatedSymbols, oCreatedSymbol;

        if (oObject && oResource) {
          aRelatedSymbols = nsLocal.DiagramImpl.relatedSymbols(oObject, oDiagram);
          if (aRelatedSymbols.length === 0) {
            // eslint-disable-next-line no-underscore-dangle
            oCreatedSymbol = nsLocal.DiagramImpl._createSymbol(oDiagramEditor, oObject);
            if (oCreatedSymbol && oDiagramEditor) {
              oDiagramEditor.registerSymbolEvents(oCreatedSymbol);
            }
          } else {
            oCreatedSymbol = aRelatedSymbols[0];
          }
        }
        return oCreatedSymbol;
      },

      /**
       *
       * @param graphContent - JSON content of task chain
       * @param oModel - OModel to be updated
       */
      buildGraph: function (graphContent, oModel, oDiagramEditor) {
        nsLocal.DiagramImpl.createModelFromJSON(graphContent, oModel);
        nsLocal.DiagramImpl.drawGraph(oModel, oDiagramEditor);
      },

      /**
       *
       * @param graphContent - JSON content of task chain
       * @param oModel - OModel to be updated
       */
      createModelFromJSON: function (graphContent, oModel) {
        let startNodeExists = false;
        for (const oNodeIndex in graphContent.nodes) {
          if (graphContent.nodes[oNodeIndex].type === NODE_TYPE.START) {
            startNodeExists = true;
          }
        }
        for (const oNodeIndex in graphContent.nodes) {
          const oNodeObject = nsLocal.DiagramImpl.createNode(graphContent.nodes[oNodeIndex], oModel);
        }
        let isFirstLink = true;
        let oFirstLinkObject;
        for (const oLinkIndex in graphContent.links) {
          const oLinkObject = nsLocal.DiagramImpl.createLink(graphContent.links[oLinkIndex], oModel);
          if (isFirstLink) {
            oFirstLinkObject = oLinkObject;
          }
          isFirstLink = false;
        }
        if (!startNodeExists) {
          const startNode = nsLocal.ModelImpl.getOrCreateStartObject(oModel);
          if (oFirstLinkObject?.source || oModel.nodes?.length === 1) {
            const targetNode = oFirstLinkObject?.source || oModel.nodes.get(0);
            const oLinkObject = nsLocal.ModelImpl.createLink(startNode, targetNode);
            oLinkObject ? (oLinkObject.statusRequired = OutputType.NONE) : undefined;
          }
        }
        const oFeatures = sap.ui.getCore().getModel("featureflags").getData();
        if (graphContent.options?.layout && oFeatures.DWCO_TASKCHAIN_DIGRAM_ORIENTATION_MODE) {
          const oIsVerticalView = graphContent.options?.layout === "VERTICAL";
          oModel.isVerticalView = oIsVerticalView;
          window.sessionStorage.setItem("isVerticalView", JSON.stringify(oIsVerticalView));
        }
      },

      createNode: function (oNode, oModel) {
        const obj = {
          nodeIndex: oNode.id,
        } as any;
        let oNodeObject;
        if (Object.keys(OPERATORS).includes(oNode.type)) {
          obj.operationType = OPERATORS[oNode.type];
          const aOpers = oModel?.nodes?.filter((obj) => obj.isOperator) || [];
          const operatorName = nsLocal.ModelImpl.getUniqueAlias(aOpers, obj.operationType);
          obj.name = operatorName;
          oNodeObject = nsLocal.ModelImpl.createObject("sap.cdw.taskchainmodeler.Operation", obj, oModel);
        } else if (oNode.type === NODE_TYPE.START) {
          obj.name = nsLocal.ModelImpl.getLocalizedText("txtBegin");
          oNodeObject = nsLocal.ModelImpl.createObject("sap.cdw.taskchainmodeler.Start", obj, oModel);
        } else if (oNode.type === NODE_TYPE.TASK) {
          obj.name = oNode.taskIdentifier.objectId;
          if (
            oNode.taskIdentifier?.parameters?.retentionTimeInDays &&
            oNode.taskIdentifier?.parameters?.retentionTimeInDays?.val !== 90
          ) {
            obj.numberOfRetentionDays = oNode.taskIdentifier.parameters.retentionTimeInDays.val;
          }
          if (oNode.taskIdentifier?.parameters?.retentionTimeInDays?.val) {
            obj.retentionOptionSelected = true;
          }
          if (oNode.taskIdentifier?.parameters?.SAP_sparkapplication?.val) {
            obj.defaultApacheSelected = false;
            obj.customApacheValueSelected = oNode.taskIdentifier.parameters.SAP_sparkapplication.val;
          }
          if (oNode.taskIdentifier.activity === Activity.REMOVE_DELETED_RECORDS) {
            obj.name = oNode.taskIdentifier.objectId;
          }
          if (oModel.applicationIdActivity.filter((obj) => obj.activity === oNode.taskIdentifier.activity).length > 0) {
            const filterItem = oModel.applicationIdActivity.filter(
              (obj) => obj.applicationId === oNode.taskIdentifier.applicationId
            );
            obj.selectedActivityIndex = filterItem.findIndex((x) => x.activity === oNode.taskIdentifier.activity);
          }
          if (oNode.taskIdentifier.applicationId === ApplicationId.NOTIFICATION) {
            obj.notificationMessage = oNode.configuration?.body;
            obj.notificationSubject = oNode.configuration?.subject;
          }
          /** Additional check, if oNode.taskIdentifier.spaceId is different from oModel.spaceName.
           * This handles cases during CSN file import where a child task chain has a cross-space name
           * matching the current space, preventing the creation of an undefined task chain.
           * */
          if (oNode.taskIdentifier.spaceId && oNode.taskIdentifier.spaceId !== oModel.spaceName) {
            obj.isCrossSpace = true;
            obj.crossSpaceName = oNode.taskIdentifier.spaceId;
          }
          if (oNode.taskIdentifier.applicationId === ApplicationId.BW_PROCESS_CHAIN) {
            obj.spaceName = oNode.taskIdentifier.spaceId;
          }
          oNodeObject = nsLocal.ModelImpl.createObject("sap.cdw.taskchainmodeler.Task", obj, oModel);
          if (
            oNode.taskIdentifier.activity === Activity.RUN_SQL_SCRIPT_PROCEDURE &&
            oNode.taskIdentifier.applicationId === ApplicationId.SQL_SCRIPT_PROCEDURE
          ) {
            obj.parameters = oNode.taskIdentifier.parameters.procedureParameters;
            const paramObj = nsLocal.ModelImpl.createParameters(oNodeObject, obj.parameters);
          }

          Utils.fillTaskType(oNode, oNodeObject);
          if (oNode.taskIdentifier.applicationId === "API") {
            nsLocal.DiagramImpl.createRestApiTask(oNode, oNodeObject, oModel);
          }
        }
        return oNodeObject;
      },

      createLink: function (oLink, oModel) {
        let oLinkObject;
        let aSourceNode = oModel.nodes.selectAllObjects({ nodeIndex: oLink.startNode.nodeId })?.toArray();
        if (aSourceNode.length === 0 && oLink.startNode.nodeId === 0) {
          aSourceNode = [oModel.startNode];
        }
        const aTargetNode = oModel.nodes.selectAllObjects({ nodeIndex: oLink.endNode.nodeId })?.toArray();
        if (aSourceNode.length && aTargetNode.length) {
          oLinkObject = nsLocal.ModelImpl.createLink(aSourceNode[0], aTargetNode[0]);
          if (oLinkObject && (aSourceNode[0].isStart || aSourceNode[0].isOperator)) {
            oLinkObject.statusRequired = OutputType.ANY;
          } else if (oLink.statusRequired && oLinkObject) {
            oLinkObject.statusRequired = oLink.statusRequired;
          }
        }
        return oLinkObject;
      },

      createRestApiTask: function (oNode, oNodeObject, oModel) {
        oNodeObject.isRestApi = true;
        if (oNode.configuration) {
          let flag = "invokeAPI";
          const config = oNode.configuration;
          oNodeObject.baseUrl = config.baseUrl || "www.sap.com";
          oNodeObject.connectionName = config.connectionName;
          const { url, state } = Utils.getConnectionUrl(oNodeObject, oModel);
          oNodeObject.baseUrl = url;
          oNodeObject.connectionNameValueState = state;
          if (config.apiGenericSettings?.requestHeaders) {
            const headers = config.apiGenericSettings.requestHeaders;
            Object.keys(headers).forEach((key) => {
              if (key !== "") {
                const value = headers[key];
                const headerObject = nsLocal.ModelImpl.createObject(
                  "sap.cdw.taskchainmodeler.RequestHeader",
                  { key: key, value: value },
                  oNodeObject
                );
              }
              // oNodeObject.requestHeaders.push(headerObject);
              // oNodeObject.headers = headerObject;
            });
          }
          if (config.invokeAPI) {
            const invokeApiObject = nsLocal.ModelImpl.createObject(
              "sap.cdw.taskchainmodeler.InvokeAPIRequest",
              {},
              oNodeObject
            );
            nsLocal.DiagramImpl.createRequestObject(config.invokeAPI.request, invokeApiObject, flag);
            nsLocal.DiagramImpl.createResponseObject(config.invokeAPI.expectedResponse, invokeApiObject);
            oNodeObject.invokeAPI = invokeApiObject;
          }
          const statusApiObject = nsLocal.ModelImpl.createObject(
            "sap.cdw.taskchainmodeler.StatusAPIRequest",
            {},
            oNodeObject
          );
          flag = "statusAPI";
          nsLocal.DiagramImpl.createRequestObject(config.statusAPI?.request || {}, statusApiObject, flag);
          nsLocal.DiagramImpl.createResponseObject(config.statusAPI?.expectedResponse || {}, statusApiObject);
          oNodeObject.statusAPI = statusApiObject;
          if (config.logsAPI) {
            const logsApiObject = nsLocal.ModelImpl.createObject(
              "sap.cdw.taskchainmodeler.LogsAPIRequest",
              {},
              oNodeObject
            );
            nsLocal.DiagramImpl.createRequestObject(config.logsAPI.request, logsApiObject);
            nsLocal.DiagramImpl.createResponseObject(config.logsAPI.expectedResponse, logsApiObject);
            oNodeObject.logsAPI = logsApiObject;
          }
        }
      },

      /**
       *
       * @param irequest - Request Object to be updated
       * @param oContainer - Container where request object to be added
       */
      createRequestObject: function (irequest, oContainer, flag) {
        const obj = {
          method: irequest?.method,
          apiPath: irequest?.apiPath,
          mode: irequest?.mode,
          csrfToken: irequest?.csrfTokenRequired,
          csrfTokenUrl: irequest?.csrfTokenUrl,
          body: JSON.stringify(irequest?.body || {}),
          apiPathValueState: "Error",
        };
        const oRegex =
          flag === "invokeAPI"
            ? /^(?!.*\.\.)(?!.*\.$)(?!.*\.\.$)(?!.*[\{\}])(?!$)[^\s]*$/
            : /^(?!.*\.\.)(?!.*\.$)(?!.*\.\.$)(?!.*\{\})(?!$)[^\s]*(\{.+\}){1}[^\s]*$/;
        if (obj.apiPath && oRegex.test(obj.apiPath)) {
          obj.apiPathValueState = "None";
        }
        if (flag === "statusAPI" && irequest === undefined) {
          obj.method = "GET";
        }
        const requestObject = nsLocal.ModelImpl.createObject("sap.cdw.taskchainmodeler.APIRequest", obj, oContainer);
        oContainer.request = requestObject;
      },

      /**
       *
       * @param iresponse - Response Object to be updated
       * @param oContainer - Container where request object to be added
       */
      createResponseObject: function (iresponse, oContainer) {
        const obj = {
          from: iresponse?.from,
          body: JSON.stringify(iresponse?.body || {}),
          jobIdJsonPath: iresponse?.body?.jobIdJsonPath,
          successIndicatorPath: iresponse?.body?.successJsonPath,
          successIndicatorCondition: iresponse?.body?.successCondition || "EQUAL",
          successIndicatorValue: iresponse?.body?.successExpectedValue,
          errorIndicatorPath: iresponse?.body?.errorJsonPath,
          errorIndicatorCondition: iresponse?.body?.errorCondition || "EQUAL",
          errorIndicatorValue: iresponse?.body?.errorExpectedValue,
          errorReasonPath: iresponse?.body?.errorReasonJsonPath,
          logsPath: iresponse?.body?.jobIdJsonPath,
        };
        const responseObject = nsLocal.ModelImpl.createObject("sap.cdw.taskchainmodeler.APIResponse", obj, oContainer);
        oContainer.response = responseObject;
      },

      /**
       *
       * @param oModel - OModel to be updated
       */
      drawGraph: function (oModel, oDiagramEditor) {
        nsLocal.DiagramImpl.adjustDiagramContent(oDiagramEditor, { model: oModel });
      },

      getOutputSymbol: function (oSymbol, oLink) {
        for (let index = 0, max = oSymbol.boundarySymbols.length; index < max; index++) {
          const oSym = oSymbol.boundarySymbols.get(index);
          if (
            oSym.isBoundarySymbol &&
            oSym.classDefinition?.name === "OutputSymbol" &&
            (oSymbol.classDefinition.name === "OperationSymbol" ||
              oSymbol.classDefinition.name === "BeginSymbol" ||
              oSym.object?.statusRequired === oLink.statusRequired)
          ) {
            return oSym;
          }
        }
      },

      getInputSymbol: function (oSymbol) {
        for (let index = 0, max = oSymbol.boundarySymbols.length; index < max; index++) {
          const oSym = oSymbol.boundarySymbols.get(index);
          if (oSym.isBoundarySymbol && oSym.classDefinition?.name === "InputSymbol") {
            return oSym;
          }
        }
      },

      /**
       * get all link symbols from input and output symbols
       */
      getAllLinkSymbolss: function (oSymbol, bIncludeSource, bIncludeTarget) {
        let aLinkSymbols = [];
        for (let index = 0, max = oSymbol.boundarySymbols.length; index < max; index++) {
          const oSym = oSymbol.boundarySymbols.get(index);
          if (
            oSym.isBoundarySymbol &&
            (oSym.classDefinition?.name === "InputSymbol" || oSym.classDefinition?.name === "OutputSymbol")
          ) {
            const linkSymbols = oSym.getLinkSymbols(bIncludeSource, bIncludeTarget);
            if (oSym.getLinkSymbols && linkSymbols?.length > 0) {
              aLinkSymbols = [...aLinkSymbols, ...linkSymbols];
            }
          }
        }
        return aLinkSymbols;
      },

      initiateDiagram: function (oDiagramEditor) {
        const oDiagram = oDiagramEditor?.diagram;
        if (oDiagram) {
          const { oBeginSymbol, oOutputSymbol } = nsLocal.DiagramImpl._createBeginSymbol(oDiagramEditor, oDiagram);
          oBeginSymbol.object.placeHolderNodes = 1;
          const oPlaceHolder = nsLocal.ModelImpl.createPlaceHolderObject(oDiagram.resource.model);
          const oLink = nsLocal.ModelImpl.createLink(oBeginSymbol.object, oPlaceHolder);
          oLink ? (oLink.statusRequired = OutputType.NONE) : undefined;
          const oLinkSymbol2 = nsLocal.ModelImpl.createObject(
            "sap.cdw.taskchainmodeler.ui.LinkSymbol",
            {
              object: oLink,
            },
            oDiagram
          );
          const oPlaceHolderSymbol = nsLocal.ModelImpl.createObject(
            "sap.cdw.taskchainmodeler.ui.PlaceHolderSymbol",
            { object: oPlaceHolder },
            oDiagram
          );
          const oInputSymbol = nsLocal.ModelImpl.createObject("sap.cdw.taskchainmodeler.ui.InputSymbol", {}, oDiagram);
          oPlaceHolderSymbol.symbols.push(oInputSymbol);
          oDiagramEditor.drawSymbol(oInputSymbol);
          oDiagramEditor.drawSymbol(oPlaceHolderSymbol);
          oPlaceHolderSymbol.updateBoundarySymbols();
          oLinkSymbol2.sourceSymbol = oOutputSymbol;
          oLinkSymbol2.targetSymbol = nsLocal.DiagramImpl.getInputSymbol(oPlaceHolderSymbol);
          oDiagramEditor.drawSymbol(oLinkSymbol2);
          if (oPlaceHolderSymbol && oDiagramEditor) {
            oDiagramEditor.registerSymbolEvents(oPlaceHolderSymbol);
          }
        }
      },

      _createBeginSymbol: function (oDiagramEditor, oDiagram) {
        let oBeginSymbol, oOutputSymbol;
        const startNode = nsLocal.ModelImpl.getOrCreateStartObject(oDiagram.resource.model);
        if (startNode) {
          if (nsLocal.DiagramImpl.relatedSymbols(startNode, oDiagram).length === 0) {
            // New object not yet displayed, we need to create a symbol
            oBeginSymbol = nsLocal.ModelImpl.createObject(
              "sap.cdw.taskchainmodeler.ui.BeginSymbol",
              { object: startNode },
              oDiagram
            );
            const oGalileiOutputObject = nsLocal.ModelImpl.createObject(
              "sap.cdw.taskchainmodeler.Output",
              { statusRequired: OutputType.NONE, fill: "none" },
              oBeginSymbol
            );
            oOutputSymbol = nsLocal.ModelImpl.createObject(
              "sap.cdw.taskchainmodeler.ui.OutputSymbol",
              { object: oGalileiOutputObject },
              oDiagram
            );
            oBeginSymbol.symbols.push(oOutputSymbol);
            oDiagramEditor.drawSymbol(oBeginSymbol);
            oBeginSymbol.updateBoundarySymbols();
          } else {
            oBeginSymbol = nsLocal.DiagramImpl.relatedSymbols(startNode, oDiagram)[0];
            if (oBeginSymbol?.boundarySymbols?.length) {
              oOutputSymbol = oBeginSymbol.boundarySymbols.get(0);
            }
          }
          return { oBeginSymbol, oOutputSymbol };
        }
      },
    },
  });
});
