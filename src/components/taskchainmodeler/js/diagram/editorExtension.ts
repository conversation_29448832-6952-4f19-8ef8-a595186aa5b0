/** @format */

import { ShellContainer } from "../../../shell/utility/Container";
import { DWCFeature, EventType } from "../../../shell/utility/ShellUsageCollectionService";
import { OPERATORS } from "../utils";

const USAGE_VIEW_BUILDER = "View Builder";

/**
 * CDS/CSN CDW query builder editor extension.
 * (c) Copyright 2019 SAP AG. All rights reserved
 */

sap.galilei.namespace("sap.cdw.taskchainmodeler.ui", function (nsLocal) {
  "use strict";

  const nsCommonModel = sap.cdw.commonmodel;
  const nsTaskchainmodeler = sap.cdw.taskchainmodeler;

  /**
   * @class
   * The diagram editor extension for CDW Query Builder
   * @name DiagramEditorExtension
   */
  nsLocal.DiagramEditorExtension = sap.galilei.ui.editor.defineDiagramEditorExtension({
    // Define class name
    fullClassName: "sap.cdw.taskchainmodeler.ui.DiagramEditorExtension",

    // Define parent
    parent: (sap.cdw.ermodeler.ui as any).BaseDiagramEditorExtension, // ToDo 'as any'

    // Define properties
    properties: {
      SYMBOL_NAMESPACE: "sap.cdw.taskchainmodeler.ui",
      TASK_SYMBOL: "sap.cdw.taskchainmodeler.ui.TaskSymbol",
      OPERATION_SYMBOL: "sap.cdw.taskchainmodeler.ui.OperationSymbol",
      PLACEHOLDER_SYMBOL: "sap.cdw.taskchainmodeler.ui.PlaceHolderSymbol",
      LINK_SYMBOL: "sap.cdw.taskchainmodeler.ui.LinkSymbol",
      INPUT_SYMBOL: "sap.cdw.taskchainmodeler.ui.InputSymbol",
      OUTPUT_SYMBOL: "sap.cdw.taskchainmodeler.ui.OutputSymbol",
      BEGIN_SYMBOL: "sap.cdw.taskchainmodeler.ui.BeginSymbol",
      OBJECT_NAMESPACE: "sap.cdw.taskchainmodeler",
      MODEL_OBJECT: "sap.cdw.taskchainmodeler.Model",
      TASK_OBJECT: "sap.cdw.taskchainmodeler.Task",
      OPERATION_OBJECT: "sap.cdw.taskchainmodeler.Operation",
      LINK_OBJECT: "sap.cdw.taskchainmodeler.Link",

      EDIT_WITH_COMMAND: "sap.cdw.taskchainmodeler.EditWithCommand",
      DELETE_SYMBOL_COMMAND: "sap.cdw.taskchainmodeler.DeleteSymbol",
      IMPACT_LINEAGE_COMMAND: "sap.cdw.taskchainmodeler.ImpactLineageCommand",
      PLACEHOLDER_SYMBOL_COMMAND: "sap.cdw.taskchainmodeler.AddPlaceHolderCommand",
      PARALLEL_SYMBOL_COMMAND: "sap.cdw.taskchainmodeler.AddParallelBranchCommand",
      OPERATION_SYMBOL_COMMAND: "sap.cdw.taskchainmodeler.AddOperationCommand",
      OPERATION_ANY_SYMBOL_COMMAND: "sap.cdw.taskchainmodeler.AddAnyOperationCommand",

      ELEMENTS_REFERENCE: "elements",

      ENTITY_OBJECT_PARAM: undefined,
      OPERATION_OBJECT_PARAM: undefined,

      DROP_TASK_ON_TASK: 0,
      DROP_ON_DIAGRAM: 1,
      DROP_TASK_ON_LINK: 2,
      DROP_TASK_ON_PLACEHOLDER: 3,
      DROP_LINK_ON_TASK: 4,
      DROP_LINK_ON_PLACEHOLDER: 5,
      DROP_TASK_ON_BEGIN: 6,
      DROP_TASK_ON_OPERATION: 7,
      DROP_OPERATION_ON_TASK: 8,
      DROP_NOT_PERMITTED: -1,

      imagesFolder: "components/taskchainmodeler/images",
      /** holds current dragged entity */
      currentDraggedOnSymbol: undefined,
      currentDraggedOnSymbolHTML: undefined,
      /** prefix of the page containing the join/union control -- needed for inheriting to subclasses */
      sPrefixId: "taskChainModeler--",
    },

    // Define methods
    methods: {
      /**
       * Performs initialization of the extension.
       * @function
       * @name onInitialize
       * @memberOf sap.cdw.taskchainmodeler.ui.DiagramEditorExtension#
       */
      onInitialize: function () {
        this.defaultOnInitialize();

        // Add drop shadow filter
        this.createDropShadowFilter("entityDropShadow");
        this.createDropShadowFilter("taskDropShadow");

        // subscribe to adjust diagram requests
        // eslint-disable-next-line no-underscore-dangle
        if (this._adjustDiagramContentProxy) {
          sap.ui
            .getCore()
            .getEventBus()
            .unsubscribe(
              nsTaskchainmodeler.ModelImpl.JSONMODEL_CHANNEL,
              // eslint-disable-next-line no-underscore-dangle
              (nsTaskchainmodeler.ModelImpl as any).JSONMODEL_CHANGED_EVENT,
              this._adjustDiagramContentProxy
            ); // ToDo 'as any'
        }
        // eslint-disable-next-line no-underscore-dangle
        this._adjustDiagramContentProxy = this.adjustDiagramContent.bind(this);
        sap.ui
          .getCore()
          .getEventBus()
          .subscribe(
            nsTaskchainmodeler.ModelImpl.JSONMODEL_CHANNEL,
            // eslint-disable-next-line no-underscore-dangle
            (nsTaskchainmodeler.ModelImpl as any).JSONMODEL_CHANGED_EVENT,
            this._adjustDiagramContentProxy
          ); // ToDo 'as any'

        if (this._deleteObjectAndSymbolsProxy) {
          sap.ui
            .getCore()
            .getEventBus()
            .unsubscribe(
              nsTaskchainmodeler.ModelImpl.JSONMODEL_CHANNEL,
              // eslint-disable-next-line no-underscore-dangle
              (nsTaskchainmodeler.ModelImpl as any).JSONMODEL_DELETE_OBJECTANDSYMBOLS,
              this._deleteObjectAndSymbolsProxy
            );
        }
        // eslint-disable-next-line no-underscore-dangle
        this._deleteObjectAndSymbolsProxy = this.deleteObjectAndSymbols.bind(this);
        sap.ui
          .getCore()
          .getEventBus()
          .subscribe(
            nsTaskchainmodeler.ModelImpl.JSONMODEL_CHANNEL,
            // eslint-disable-next-line no-underscore-dangle
            (nsTaskchainmodeler.ModelImpl as any).JSONMODEL_DELETE_OBJECTANDSYMBOLS,
            this._deleteObjectAndSymbolsProxy
          );

        // subscribe to new model requests
        // eslint-disable-next-line no-underscore-dangle
        if (this._newModelProxy) {
          sap.ui
            .getCore()
            .getEventBus()
            .unsubscribe(
              nsTaskchainmodeler.ModelImpl.JSONMODEL_CHANNEL,
              // eslint-disable-next-line no-underscore-dangle
              (nsTaskchainmodeler.ModelImpl as any).JSONMODEL_NEWMODEL_EVENT,
              this._newModelProxy
            ); // ToDo 'as any'
        }
        // eslint-disable-next-line no-underscore-dangle
        this._newModelProxy = this.onNewModel.bind(this);
        sap.ui
          .getCore()
          .getEventBus()
          .subscribe(
            nsTaskchainmodeler.ModelImpl.JSONMODEL_CHANNEL,
            // eslint-disable-next-line no-underscore-dangle
            (nsTaskchainmodeler.ModelImpl as any).JSONMODEL_NEWMODEL_EVENT,
            this._newModelProxy
          ); // ToDo 'as any'

        // subscribe to highlights requests
        // eslint-disable-next-line no-underscore-dangle
        if (this._highlightProxy) {
          sap.ui
            .getCore()
            .getEventBus()
            .unsubscribe(
              nsTaskchainmodeler.DiagramImpl.TASKCHAIN_DIAGRAM_CHANNEL,
              // eslint-disable-next-line no-underscore-dangle
              (nsTaskchainmodeler.DiagramImpl as any).HIGHLIGHT_EVENT,
              this._highlightProxy
            ); // ToDo 'as any'
        }
        // eslint-disable-next-line no-underscore-dangle
        this._highlightProxy = this.highlightSymbols.bind(this);
        sap.ui
          .getCore()
          .getEventBus()
          .subscribe(
            nsTaskchainmodeler.DiagramImpl.TASKCHAIN_DIAGRAM_CHANNEL,
            // eslint-disable-next-line no-underscore-dangle
            (nsTaskchainmodeler.DiagramImpl as any).HIGHLIGHT_EVENT,
            this._highlightProxy
          ); // ToDo 'as any'

        // subscribe to refreshValidation requests
        // eslint-disable-next-line no-underscore-dangle
        if (this._refreshValidationProxy) {
          sap.ui.getCore().getEventBus().unsubscribe(
            nsCommonModel.Validation.VALIDATION_CHANNEL,
            // eslint-disable-next-line no-underscore-dangle
            nsCommonModel.Validation.REFRESH_VALIDATION_EVENT,
            this._refreshValidationProxy
          );
        }
        // eslint-disable-next-line no-underscore-dangle
        this._refreshValidationProxy = this.refreshValidationDecorators.bind(this);
        sap.ui.getCore().getEventBus().subscribe(
          nsCommonModel.Validation.VALIDATION_CHANNEL,
          // eslint-disable-next-line no-underscore-dangle
          nsCommonModel.Validation.REFRESH_VALIDATION_EVENT,
          this._refreshValidationProxy
        );

        // subscribe to createPlaceHolder requests
        // eslint-disable-next-line no-underscore-dangle
        if (this._createPlaceHolderProxy) {
          sap.ui.getCore().getEventBus().unsubscribe(
            nsTaskchainmodeler.ModelImpl.JSONMODEL_CHANNEL,
            // eslint-disable-next-line no-underscore-dangle
            nsTaskchainmodeler.ModelImpl.CREATE_PLACEHOLDER_EVENT,
            this._createPlaceHolderProxy
          );
        }
        // eslint-disable-next-line no-underscore-dangle
        this._createPlaceHolderProxy = this.createPlaceHolder.bind(this);
        sap.ui.getCore().getEventBus().subscribe(
          nsTaskchainmodeler.ModelImpl.JSONMODEL_CHANNEL,
          // eslint-disable-next-line no-underscore-dangle
          nsTaskchainmodeler.ModelImpl.CREATE_PLACEHOLDER_EVENT,
          this._createPlaceHolderProxy
        );

        // subscribe to selection
        sap.galilei.core.Event.subscribe("symbol.selected", this.onSelectSymbols, /* scope*/ this, this.editor);
        sap.galilei.core.Event.subscribe("symbol.unselected", this.onUnselectSymbols, /* scope*/ this, this.editor);
        sap.galilei.core.Event.subscribe("diagram.mouseup", this.onDiagramMouseUp, /* scope*/ this, this.editor);
        this.initializeNewParallelReplaceSelector();
      },

      /**
       * Sets the default name and display name
       * @function
       * @name setDefaultObjectName
       * @memberOf sap.galilei.ui.editor.DiagramEditorExtension#
       * @param {Object} oObject The object.
       */
      setDefaultObjectName: function (oObject) {
        (nsTaskchainmodeler.ModelImpl as any).setDefaultObjectName(oObject); // ToDo 'as any'
      },

      dispose: function () {
        this.defaultDispose();
        sap.ui
          .getCore()
          .getEventBus()
          .unsubscribe(
            nsTaskchainmodeler.ModelImpl.JSONMODEL_CHANNEL,
            (nsTaskchainmodeler.ModelImpl as any).JSONMODEL_CHANGED_EVENT,
            this.adjustDiagramContent.bind(this)
          ); // ToDo 'as any'
      },

      /**
       * Called after an undo is performed
       * @param {*} aImpactedSymbols
       * @param {*} aActions
       */
      postUndo: function (aImpactedSymbols, aActions) {
        const skipModelValidation = false;
        if (!skipModelValidation) {
          this.editor.resource.model.validate();
        }
        const that = this;
        setTimeout(function () {
          console.log("requestAdjustDiagramsContent....");
          that.editor.resource.model?.resource.applyUndoableAction(
            function () {
              that.editor.resource.model.bRedrawDiagram = true;
              nsTaskchainmodeler.ModelImpl.requestAdjustDiagramsContent({ autoLayout: true });
            },
            "postUndo",
            true
          );
        }, 100);
        if (sap.ui && sap.ui.getCore instanceof Function) {
          sap.ui
            .getCore()
            .getEventBus()
            .publish(
              nsTaskchainmodeler.DiagramImpl.TASKCHAIN_DIAGRAM_CHANNEL,
              nsTaskchainmodeler.DiagramImpl.UNDO_REDO_EVENT,
              {
                symbols: aImpactedSymbols,
                actions: aActions,
                isUndo: true,
              }
            );
        }
      },

      /**
       * Called after a redo is performed
       * @param {*} aImpactedSymbols
       * @param {*} aActions
       */
      postRedo: function (aImpactedSymbols, aActions) {
        const skipModelValidation = false;
        const that = this;
        if (!skipModelValidation) {
          this.editor.resource.model.validate();
        }
        setTimeout(function () {
          that.editor.resource.model?.resource.applyUndoableAction(
            function () {
              that.editor.resource.model.bRedrawDiagram = true;
              nsTaskchainmodeler.ModelImpl.requestAdjustDiagramsContent({ autoLayout: true });
            },
            "postRedo",
            true
          );
        }, 100);
        if (sap.ui && sap.ui.getCore instanceof Function) {
          sap.ui
            .getCore()
            .getEventBus()
            .publish(
              nsTaskchainmodeler.DiagramImpl.TASKCHAIN_DIAGRAM_CHANNEL,
              nsTaskchainmodeler.DiagramImpl.UNDO_REDO_EVENT,
              {
                symbols: aImpactedSymbols,
                actions: aActions,
                isRedo: true,
              }
            );
        }
      },

      /**
       * Called when a symbol(s) getting selected
       */
      onSelectSymbols: function (oData) {
        this.editor.unhighlightAllSymbols();
        const oSelectedSymbol = this.editor.selectedSymbols.length && this.editor.selectedSymbols[0];
        const oSelectedObject = oSelectedSymbol?.object;
        this.editor?.controller?.getParentController?.()._onSymbolSelectionChange();

        if (this.editor?.selectedSymbols?.length === 1) {
          if (oSelectedObject) {
            const predecessorNodes = oSelectedObject.predecessorNodes || [];
            const successorNodes = oSelectedObject.successorNodes || [];
            const allLinkSymbols = nsTaskchainmodeler.DiagramImpl.getAllLinkSymbolss(
              this.editor.selectedSymbols[0],
              true,
              true
            );
            const allConncetedNodes = [...predecessorNodes, ...successorNodes];
            const oSymbols = [...allLinkSymbols];
            for (let index = 0; index < allConncetedNodes.length; index++) {
              const oNode = allConncetedNodes[index];
              if (nsTaskchainmodeler.DiagramImpl.relatedSymbols(oNode, this.editor.diagram)?.length) {
                const oSymbol = nsTaskchainmodeler.DiagramImpl.relatedSymbols(oNode, this.editor.diagram)[0];
                if (oSymbol?.isSymbol) {
                  oSymbols.push(oSymbol);
                }
              }
            }
            if (oSymbols.length) {
              this.editor.highlightSymbols(oSymbols, undefined, undefined, true, [oSelectedSymbol]);
            }
          }
        }
      },

      /**
       * Called when a symbol(s) gettting unselected
       */
      onUnselectSymbols: function (oData) {
        this.editor.unhighlightAllSymbols();
        const oSelectedSymbol = this.editor.selectedSymbols.length && this.editor.selectedSymbols[0];

        this.editor?.controller?.getParentController?.()._onSymbolSelectionChange();
      },

      /**
       * Called when a mouse up event triggered
       */
      onDiagramMouseUp: function (oData) {
        this.editor?.controller?.getParentController?.()._onSymbolSelectionChange();
      },

      /**
       * Forward the adjust Diagram content to diagram helper
       * @function
       * @name adjustdiagramContent
       * @param {*} oOptions options for adjust to content for instance
       * {
       *  selection: galilei object to select after adjusting symbols
       *  model: in case of loading data
       * }
       */
      adjustDiagramContent: function (sChannelId, sEventId, oOptions) {
        this.hideNewParallelReplaceSelector();
        let that = this;
        if (oOptions.protectFromUndoRedo && oOptions.model?.resource) {
          oOptions.model?.resource.applyUndoableAction(
            function () {
              oOptions.model.bRedrawDiagram = true;
              (nsTaskchainmodeler.DiagramImpl as any).adjustDiagramContent(that.editor, oOptions); // ToDo 'as any'
            },
            "adjustDiagramContent",
            true
          );
        } else {
          (nsTaskchainmodeler.DiagramImpl as any).adjustDiagramContent(this.editor, oOptions); // ToDo 'as any'
        }
      },

      deleteObjectAndSymbols: function (sChannelId, sEventId, oOptions) {
        (nsTaskchainmodeler.DiagramImpl as any).deleteObjectAndSymbols(this.editor, oOptions);
      },

      /**
       * Trigerred when a model is created/loaded
       * Forward the refresh Validation Decorators request
       * @function
       * @name onNewModel
       * @param {*} oOptions the options for instance {
       *  model: the new model
       * }
       */
      onNewModel: function (sChannelId, sEventId, oOptions) {
        this.hideNewParallelReplaceSelector();
        if (oOptions.model) {
          nsTaskchainmodeler.DiagramImpl.refreshValidationDecoratorsByModel(oOptions.model);
        }
      },

      /**
       * Highlights symbols, called when a highlight event is published
       * @param {*} sChannelId
       * @param {*} sEventId
       * @param {*} oOptions
       */
      highlightSymbols: function (sChannelId, sEventId, oOptions) {
        this.editor.unhighlightAllSymbols();
        if (oOptions.symbols) {
          this.editor.highlightSymbols(oOptions.symbols, undefined, undefined, true, oOptions.exclude);
        }
      },

      refreshValidationDecorators: function (sChannelId, sEventId, oOptions) {
        if (oOptions.symbols && oOptions.diagram && this.editor.diagram === oOptions.diagram) {
          this.defaultRefreshValidationDecorators(oOptions.symbols, oOptions.recreate);
        } else if (oOptions.model && oOptions.model === this.editor.model) {
          nsTaskchainmodeler.DiagramImpl.refreshValidationDecoratorsByModel(oOptions.model);
        }
      },

      createPlaceHolder: function (sChannelId, sEventId, oOptions) {
        const self = this;
        if (this.editor?.diagram) {
          if (oOptions.model?.startNode?.successorNodes?.length === 0) {
            nsTaskchainmodeler.DiagramImpl.initiateDiagram(self.editor);
            nsTaskchainmodeler.ModelImpl.requestAdjustDiagramsContent({ autoLayout: true });
          }
        }
      },

      /**
       * Creates a drop shadow filter with a specific filter id.
       * @param {*} sFilterId
       */
      createDropShadowFilter: function (sFilterId) {
        // Add drop shadow filter
        const dropShodowFilter = new sap.galilei.ui.common.style.DropShadow({
          id: sFilterId,
          color: "#222222",
          size: 3,
          dx: 3,
          dy: 3,
        });
        dropShodowFilter.create(this.viewer);
      },

      /**
       * (Override)
       * @param {*} oEvent
       */
      onKeyDown: function (oEvent) {
        let bProcessed = false;
        switch (oEvent && oEvent.keyCode) {
          case 27: // Escape
            bProcessed = true;
            this.hideNewParallelReplaceSelector();
            break;
          case 46: // Delete
            bProcessed = true;
            this.editor.controller.onDelete();
            break;
        }
        return bProcessed || this.editor.defaultOnKeyDown(oEvent);
      },

      deleteSelection: function () {
        if (this.editor.selectedSymbols && this.editor.selectedSymbols.length) {
          this.editor.deleteSymbols(this.editor.selectedSymbols);
        }
      },

      /**
       * Performs the object creation
       * @function
       * @name createObject
       * @param {String} sObjectClassName The qualified class name or class name without namespace.
       * @param {Object} oParam
       */
      createObject: function (sObjectClassName, oParam) {
        const oObject = this.editor.defaultCreateObject(sObjectClassName, oParam);
        let oSymbol, oSelectedObject;
        const self = this;

        if (oObject) {
          // eslint-disable-next-line no-underscore-dangle
          oObject._creationTimestamp = Date.now();
        }
        if (oObject && oParam) {
          switch (sObjectClassName) {
            case this.TASK_OBJECT:
              nsTaskchainmodeler.ModelImpl.onDropDataSource(oObject, oParam);
              break;
            default:
              break;
          }
        }
        return oObject;
      },

      /**
       * Performs the object creation
       * @function
       * @name createObject
       * @param {String} sSymbolClassName The qualified symbol class name or class name without namespace.
       * @param {Object} oParam
       */
      createSymbol: function (sSymbolClassName, oParam) {
        const oObject = oParam.object;
        if (!sSymbolClassName || oObject.relatedSymbols?.length > 0) {
          // missing sSymbolClassName or the symbol is already created
          return;
        }
        if (sSymbolClassName.indexOf(".") === -1) {
          sSymbolClassName = this.SYMBOL_NAMESPACE + "." + sSymbolClassName;
        }
        if (oParam && oParam.diagram) {
          delete oParam.diagram; // Avoid internal exception with read-only diagram
        }
        return this.createObject(sSymbolClassName, oParam);
      },

      /**
       * Performs transformation after the creation of a node symbol.
       * @function
       * @name postCreateSymbol
       * @memberOf com.sap.powerdesigner.web.galilei.eam.diagram.EditorExtension#
       * @param {Symbol} oSymbol The symbol.
       */
      postCreateSymbol: function (oSymbol) {
        const self = this;
        this.basePostCreateSymbol(oSymbol);
        const sSymbolClassName = oSymbol.classDefinition.name,
          oParentSymbol = oSymbol.parentSymbol,
          sParentSymbolClass = oParentSymbol && oParentSymbol.classDefinition.name;
        if (sSymbolClassName === "TaskSymbol" || sSymbolClassName === "OperationSymbol") {
          oSymbol.resource.applyUndoableAction(function () {
            nsTaskchainmodeler.DiagramImpl.addBoundarySymbols(oSymbol, self.editor);
          });
        }
        if (sSymbolClassName === "TaskSymbol" && sParentSymbolClass === "TaskSymbol") {
          oSymbol.resource.applyUndoableAction(function () {
            (nsTaskchainmodeler.ModelImpl as any).onPostDropDataSource(oSymbol.object, oParentSymbol.object);
          });
        } else if (sSymbolClassName === "TaskSymbol" && sParentSymbolClass === "LinkSymbol") {
          oSymbol.resource.applyUndoableAction(function () {
            (nsTaskchainmodeler.ModelImpl as any).onPostDropDataSource(oSymbol.object, oParentSymbol.object.source);
          });
        } else if (sSymbolClassName === "TaskSymbol" && sParentSymbolClass === "PlaceHolderSymbol") {
          oSymbol.resource.applyUndoableAction(function () {
            const oLinkSymbol = nsTaskchainmodeler.DiagramImpl.getAllLinkSymbolss(oParentSymbol, false, true).filter(
              (oSym) => {
                return oSym.sourceSymbol?.parentSymbol?.classDefinition?.name === "BeginSymbol";
              }
            );
            if (oLinkSymbol && oLinkSymbol.length) {
              // oSymbol.object.isFirstObject = true;
            }
            (nsTaskchainmodeler.ModelImpl as any).onPostDropDataSource(oSymbol.object, oParentSymbol, {
              isPlaceHolderTarget: true,
              droppedSymbol: oSymbol,
              editor: self.editor,
            }); // ToDo 'as any'
          });
        } else if (sSymbolClassName === "TaskSymbol" && sParentSymbolClass === "BeginSymbol") {
          oSymbol.resource.applyUndoableAction(function () {
            (nsTaskchainmodeler.ModelImpl as any).onPostDropDataSource(oSymbol.object, oParentSymbol.object.source);
          });
        } else if (sSymbolClassName === "TaskSymbol" && sParentSymbolClass === "OperationSymbol") {
          oSymbol.resource.applyUndoableAction(function () {
            (nsTaskchainmodeler.ModelImpl as any).onPostDropDataSource(oSymbol.object, oParentSymbol.object.source);
          });
        } else if (sSymbolClassName === "TaskSymbol" && sParentSymbolClass === undefined) {
          if (this.currentDraggedOnSymbolHTML) {
            const currentDraggedOnSymbolName = this.currentDraggedOnSymbolHTML.classDefinition?.name;
            if (currentDraggedOnSymbolName === "BeginSymbol" || currentDraggedOnSymbolName === "OperationSymbol") {
              oSymbol.resource.applyUndoableAction(function () {
                (nsTaskchainmodeler.ModelImpl as any).onPostDropDataSource(
                  oSymbol.object,
                  self.currentDraggedOnSymbolHTML.object,
                  { isDragDropFromTree: true }
                ); // ToDo 'as any'
              });
            } else if (currentDraggedOnSymbolName === "TaskSymbol") {
              oSymbol.resource.applyUndoableAction(function () {
                (nsTaskchainmodeler.ModelImpl as any).onPostDropDataSource(
                  oSymbol.object,
                  self.currentDraggedOnSymbolHTML.object,
                  { operation: "AddNew" }
                ); // ToDo 'as any'
              });
            } else if (currentDraggedOnSymbolName === "PlaceHolderSymbol") {
              oSymbol.resource.applyUndoableAction(function () {
                (nsTaskchainmodeler.ModelImpl as any).onPostDropDataSource(
                  oSymbol.object,
                  self.currentDraggedOnSymbolHTML,
                  { isPlaceHolderTarget: true, droppedSymbol: oSymbol, editor: self.editor }
                ); // ToDo 'as any'
              });
            }
            this.currentDraggedOnSymbolHTML = undefined;
          }
        }

        if (sSymbolClassName === "TaskSymbol" && oSymbol.object) {
          (nsTaskchainmodeler.ModelImpl as any).checkObjectChainable(
            oSymbol.object,
            this.editor?.controller?.getParentController().getSpaceName()
          );
        }
        this.currentDraggedOnSymbolHTML = undefined;

        // Record usage
        switch (sSymbolClassName) {
          case "TaskSymbol":
          case "LinkSymbol":
          case "OperationSymbol":
            const action = "create" + sSymbolClassName.replace("Symbol", "");
            ShellContainer.get()
              .getUsageCollectionService()
              .recordAction({
                action: action,
                feature: DWCFeature.DATA_BUILDER,
                eventtype: EventType.CLICK,
                options: [
                  {
                    param: "target",
                    value: USAGE_VIEW_BUILDER,
                  },
                ],
              });
            break;
        }
      },

      onUpdateSymbol: function (oEvent) {
        // eslint-disable-next-line no-underscore-dangle
        if (this._countUpdate === undefined) {
          // eslint-disable-next-line no-underscore-dangle
          this._countUpdate = 0;
        }

        // eslint-disable-next-line no-underscore-dangle
        if (this._countUpdate > 20) {
          // Infinite loop?
        } else {
          // eslint-disable-next-line no-underscore-dangle
          this._countUpdate++;
          this.defaultOnUpdateSymbol(oEvent);
          // eslint-disable-next-line no-underscore-dangle
          this._countUpdate--;
        }
      },

      /**
       * Checks whether a symbol can be pasted.
       * @function
       * @name canPasteSymbols
       * @memberOf sap.galilei.ui.editor.DiagramEditorExtension#
       * @param {oClipboardContent} oClipboardContent clipboard content contains copyObjects, pasteObjects, copySymbols, pasteSymbols.
       * @returns {boolean} true if the symbol can be pasted.
       */
      canPasteSymbols: function (oEditor, oClipboardContent) {
        return false;
      },

      /**
       * Checks whether a symbol can be deleted.
       * @function
       * @name canDeleteSymbol
       * @memberOf sap.galilei.ui.editor.DiagramEditorExtension#
       * @param {Symbol} oSymbol The symbol to detach.
       * @returns {boolean} true if the symbol can be deleted.
       */
      canDeleteSymbol: function (oSymbol) {
        const sSymbolClassName = oSymbol && oSymbol.classDefinition && oSymbol.classDefinition.name;
        let bCanDelete;

        if (
          sSymbolClassName === "BeginSymbol" ||
          (sSymbolClassName === "LinkSymbol" &&
            oSymbol.object?.source?.isStart &&
            oSymbol.object?.target?.classDefinition?.name === "PlaceHolder")
        ) {
          bCanDelete = false;
        } else if (sSymbolClassName === "PlaceHolderSymbol") {
          const objects =
            oSymbol.object?.container?.links
              ?.selectAllObjects({
                "target.objectId": oSymbol.object?.objectId,
              })
              .toArray()
              .map((oLink) => oLink.source) || [];
          if (objects?.[0]?.isStart && objects?.[0].successorNodes.length === 1) {
            bCanDelete = false;
          } else {
            bCanDelete = this.defaultCanDeleteSymbol(oSymbol);
          }
        } else {
          bCanDelete = this.defaultCanDeleteSymbol(oSymbol);
        }
        return bCanDelete;
      },

      /**
       * Before deleting one symbol.
       */
      preDeleteSymbol: function (oContainer, oDeletedSymbol, bDeleteObject, bPreserveLink, bUseUndoAction) {
        if (oDeletedSymbol?.getAllLinkSymbolss?.().length > 0) {
          oDeletedSymbol.resource.applyUndoableAction(function () {
            oDeletedSymbol.getAllLinkSymbolss().forEach((linkSymbol) => {
              if (linkSymbol.object?.source && oDeletedSymbol.classDefinition?.name === "PlaceHolderSymbol") {
                linkSymbol.object.source.placeHolderNodes = linkSymbol.object.source.placeHolderNodes - 1;
              }
              // Setting link object to undefined
              linkSymbol.object = undefined;
              if (
                linkSymbol.targetSymbol?.isBoundarySymbol &&
                linkSymbol.targetSymbol?.parentSymbol?.classDefinition?.name === "PlaceHolderSymbol" &&
                oDeletedSymbol !== linkSymbol.targetSymbol.parentSymbol
              ) {
                linkSymbol.targetSymbol.parentSymbol.deleteSymbol();
              }
            });
          });
        }
        return this.defaultPreDeleteSymbol(oContainer, oDeletedSymbol, bDeleteObject, bPreserveLink, bUseUndoAction);
      },

      /**
       * Checks whether a symbol can be created at a specific point under a parent symbol.
       * @function
       * @name canCreateSymbol
       * @memberOf com.sap.powerdesigner.web.galilei.eam.diagram.EditorExtension#
       * @param {String} sSymbolClass The symbol class qualified name.
       * @param {Object} oCreateParam The input and output parameters. The parameters contain:
       * {
       *     point: (in/out) The view point where the symbol should be created.
       *     parentSymbol: (in/out) The parent symbol.
       * }
       * @returns {Boolean} true if the symbol can be created.
       */
      canCreateSymbol: function (sSymbolClass, oCreateParam) {
        let oParentSymbol, sParentSymbolClass;

        if (sSymbolClass && oCreateParam) {
          oParentSymbol = oCreateParam.parentSymbol;
          if (oParentSymbol && oParentSymbol.isSymbol) {
            sParentSymbolClass = oParentSymbol.classDefinition.qualifiedName;
          } else if (oCreateParam.parentSymbolClass) {
            sParentSymbolClass = oCreateParam.parentSymbolClass.qualifiedName;
          }
        }
        // eslint-disable-next-line no-underscore-dangle
        return this._checkDropOperation(sParentSymbolClass, sSymbolClass).operation !== this.DROP_NOT_PERMITTED;
      },

      /**
       * This function creates a node symbol and its object.
       * The symbol is added under a parent symbol or the diagram.
       * The object is added under the parent object or the model.
       * @function
       * @name createSymbolAndObject
       * @memberOf sap.galilei.ui.editor.DiagramEditorExtension#
       * @param {String} sSymbolClass The local or qualified class name of the symbol.
       * @param {Object} oSymbolParams (optional) The parameters for the symbol. If oSymbolParams contains objectId, it will be used as the objectId.
       * @param {sap.galilei.ui.diagram.Symbol} oSymbolParent (optional) The parent symbol. If it is not specified, the symbol will be added under the diagram.
       * @param {Number} [nSymbolIndex=-1] The symbol index in the symbols collection.
       * @param {String} sObjectClass The local or qualified class name of the object.
       * @param {Object} oObjectParams (optional) The parameters for the object. If oObjectParams contains objectId, it will be used as the objectId.
       * @param {sap.galilei.model.Object} oObjectParent (optional) The parent object. If it is not specified, the object will be added under the model.
       * @param {String} sObjectReference The name of the reference (collection) in which the object is added.
       * @param {Number} [nObjectIndex=-1] The object index in the objects collection.
       * @returns {sap.galilei.ui.diagram.Symbol} The created symbol.
       */
      createSymbolAndObject: function (
        sSymbolClass,
        oSymbolParams,
        oSymbolParent,
        nSymbolIndex,
        sObjectClass,
        oObjectParams,
        oObjectParent,
        sObjectReference,
        nObjectIndex
      ) {
        return this.defaultCreateSymbolAndObject(
          sSymbolClass,
          oSymbolParams,
          oSymbolParent,
          nSymbolIndex,
          sObjectClass,
          oObjectParams,
          oObjectParent,
          sObjectReference,
          nObjectIndex
        );
      },

      /**
       * This function creates a link object and a link symbol.
       * The link object is added under the parent object or the model.
       * The link symbol is added under a parent symbol or the diagram.
       * @function
       * @name createLinkSymbolAndObject
       * @memberOf sap.galilei.ui.editor.DiagramEditorExtension#
       * @param oSourceSymbol {sap.galilei.ui.diagram.Symbol} The source symbol.
       * @param oTargetSymbol {sap.galilei.ui.diagram.Symbol} The target symbol.
       * @param {String} sLinkSymbolClass The link symbol class qualified name.
       * @param {Object} oLinkSymbolParams The link symbol parameters. If oLinkSymbolParams contains objectId, it will be used as the objectId.
       * @param {sap.galilei.ui.diagram.Symbol} oLinkSymbolParent The parent of link symbol .
       * @param {String} sLinkObjectClass The link object class qualified name.
       * @param {Object} oLinkObjectParams The link object parameters. If oLinkObjectParams contains objectId, it will be used as the objectId.
       * @param {sap.galilei.model.Object} oLinkObjectParent The parent of the link object.
       * @param {String} sLinkObjectReference The reference name for the link object where the link object should be added.
       * @param {sap.galilei.model.Object} oLinkObjectParent2 Another parent of the link object.
       * @param {String} sLinkObjectReference2 Another reference name for the link object where the link object should be added.
       * @returns {sap.galilei.ui.diagram.LinkSymbol} The created link symbol.
       */
      createLinkSymbolAndObject: function (
        oSourceSymbol,
        oTargetSymbol,
        sLinkSymbolClass,
        oLinkSymbolParams,
        oLinkSymbolParent,
        sLinkObjectClass,
        oLinkObjectParams,
        oLinkObjectParent,
        sLinkObjectReference,
        oLinkObjectParent2,
        sLinkObjectReference2
      ) {
        if (oSourceSymbol.object && oTargetSymbol.object) {
          if (this._checkOperationAllowed(oSourceSymbol, oTargetSymbol, oSourceSymbol.object)) {
            nsTaskchainmodeler.ModelImpl.createLinkByConnector(oTargetSymbol.object, oSourceSymbol.object, true);
          }
        }
        return false; // this.defaultCreateLinkSymbolAndObject(oSourceSymbol, oTargetSymbol, sLinkSymbolClass, oLinkSymbolParams, oLinkSymbolParent, sLinkObjectClass, oLinkObjectParams, oLinkObjectParent, sLinkObjectReference, oLinkObjectParent2, sLinkObjectReference2);
      },

      /**
       * Checks whether a node symbol can be added to the diagram or a parent symbol
       * @function
       * @name canAttachSymbol
       * @memberOf com.sap.powerdesigner.web.galilei.eam.diagram.EditorExtension#
       * @param {Object} oParent The parent diagram or symbol.
       * @param {Object} oSymbol The target symbol.
       * @param {Object} oAttachParam The attach symbol input and output parameters. The parameters contain:
       * {
       *     point: (in/out) The view point where the symbol should be attached.
       *     delta: (in) The dx and dy
       *     checkOverlap: (in) In case oSymbol cannot be added as subsymbol, checks whether oSymbol can overlap oParent.
       * }
       * @returns {Boolean} True if can attach symbol.
       */
      canAttachSymbol: function (oParent, oSymbol, oAttachParam) {
        const oParentSymbolOrDiagram = oParent,
          sSymbolClass = oSymbol.classDefinition.qualifiedName;
        let sParentSymbolClass;

        this.hideNewParallelReplaceSelector();
        if (oParentSymbolOrDiagram && oParentSymbolOrDiagram.isSymbol) {
          sParentSymbolClass = oParentSymbolOrDiagram.classDefinition.qualifiedName;
        }
        // eslint-disable-next-line no-underscore-dangle
        const oCheckDropOperationResult = this._checkDropOperation(
          sParentSymbolClass,
          sSymbolClass,
          oParentSymbolOrDiagram,
          oSymbol
        );
        if (oCheckDropOperationResult.operation !== this.DROP_NOT_PERMITTED) {
          if (oCheckDropOperationResult.chooseOperationNeeded) {
            this.currentDraggedOnSymbol = oParent;
            this.currentDraggedSymbol = oSymbol;
            let hasParallelOption = true;
            if (oParent.object?.predecessorNodes?.[0] === oSymbol.object?.predecessorNodes?.[0]) {
              hasParallelOption = false;
            }
            this.initializeNewParallelReplaceSelectorForMouseEvents();
            this.openByNewParallelReplaceSelector(
              oParent.shape.svgNode,
              oCheckDropOperationResult.draggedNodeHasOnePredecessorNode,
              oCheckDropOperationResult.draggedNodeIsTask,
              oCheckDropOperationResult.draggedOnNodeIsTask,
              oParent,
              hasParallelOption
            );
          }
          return true;
        }
        return oCheckDropOperationResult.operation !== this.DROP_NOT_PERMITTED;
      },

      /**
       * Tests whether the current symbol can be moved to the drop position.
       * The editor extension could implement canMoveSymbol() to override the move symbol test behavior.
       * @function
       * @name canMoveSymbol
       * @memberOf sap.galilei.ui.editor.DiagramEditorExtension#
       * @param {sap.galilei.ui.editor.tracker.RectangularSymbolTracker} oTracker The symbol tracker.
       * @param {Object} oEvent The event.
       * @returns {boolean} true or false if the can move symbol check is handled by the callback function. undefined if it is not handled.
       */
      canMoveSymbol: function (oTracker, oEvent) {
        const oParentSymbolOrDiagram = oEvent.parentSymbolOrDiagram,
          oSymbol = oTracker.symbol,
          sSymbolClass = oSymbol && oSymbol.classDefinition.qualifiedName;
        let sParentSymbolClass;

        if (oParentSymbolOrDiagram && oParentSymbolOrDiagram.isSymbol) {
          sParentSymbolClass = oParentSymbolOrDiagram.classDefinition.qualifiedName;
        }
        if (
          sSymbolClass &&
          this._checkDropOperation(sParentSymbolClass, sSymbolClass, oParentSymbolOrDiagram, oSymbol).operation ===
            this.DROP_NOT_PERMITTED
        ) {
          nsTaskchainmodeler.DiagramImpl.refreshValidationDecoratorsByModel(this.editor.model, /* bRecreate*/ true);
        }
        // eslint-disable-next-line no-underscore-dangle
        return sSymbolClass
          ? this._checkDropOperation(sParentSymbolClass, sSymbolClass, oParentSymbolOrDiagram, oSymbol).operation !==
              this.DROP_NOT_PERMITTED
          : true;
      },

      /**
       * Tests whether the current link symbol can be moved to the drop position.
       * @function
       * @name canMoveLinkSymbol
       * @memberOf sap.galilei.ui.editor.DiagramEditorExtension#
       * @param {sap.galilei.ui.editor.tracker.RectangularSymbolTracker} oTracker The symbol tracker.
       * @param {Object} oEvent The event.
       * @returns {boolean} true or false if the can move symbol check is handled by the callback function. undefined if it is not handled.
       */
      canMoveLinkSymbol: function (oTracker, oEvent) {
        return true;
      },

      /**
       * Moves the symbol callback function. The editor extension could implement onMoveSymbol() to override the default move symbol behavior.
       * @function
       * @name onMoveSymbol
       * @memberOf sap.galilei.ui.editor.DiagramEditorExtension#
       * @param {sap.galilei.ui.editor.tracker.RectangularSymbolTracker} oTracker The symbol tracker.
       * @param {Object} oEvent The event.
       * @returns {boolean} true if the move symbol is handled by the callback function.
       */
      onMoveSymbol: function (oTracker, oEvent) {
        const oParentSymbolOrDiagram = oEvent.parentSymbolOrDiagram,
          oSymbol = oTracker.symbol,
          sSymbolClass = oSymbol.classDefinition.qualifiedName,
          self = this;
        let sParentSymbolClass;

        if (oParentSymbolOrDiagram?.classDefinition?.name === "Diagram") {
          this.hideNewParallelReplaceSelector();
        }

        if (oParentSymbolOrDiagram && oParentSymbolOrDiagram.isSymbol) {
          sParentSymbolClass = oParentSymbolOrDiagram.classDefinition.qualifiedName;
        }

        // eslint-disable-next-line no-underscore-dangle
        switch (this._checkDropOperation(sParentSymbolClass, sSymbolClass, oParentSymbolOrDiagram, oSymbol).operation) {
          case this.DROP_TASK_ON_TASK:
          case this.DROP_TASK_ON_BEGIN:
          case this.DROP_TASK_ON_OPERATION:
          case this.DROP_OPERATION_ON_TASK:
            oSymbol.resource.applyUndoableAction(function () {
              (nsTaskchainmodeler.ModelImpl as any).onPostDropDataSource(oSymbol.object, oParentSymbolOrDiagram.object);
            });
            return true;
          case this.DROP_TASK_ON_LINK:
            oSymbol.resource.applyUndoableAction(function () {
              (nsTaskchainmodeler.ModelImpl as any).onPostDropDataSource(
                oSymbol.object,
                oParentSymbolOrDiagram.object.source
              );
            });
            return true;
          case this.DROP_TASK_ON_PLACEHOLDER:
            oSymbol.resource.applyUndoableAction(function () {
              (nsTaskchainmodeler.ModelImpl as any).onPostDropDataSource(oSymbol.object, oParentSymbolOrDiagram, {
                isPlaceHolderTarget: true,
                droppedSymbol: oSymbol,
                editor: self.editor,
              });
            });
            return true;
          case this.DROP_LINK_ON_TASK:
          case this.DROP_LINK_ON_OPERATION:
            oSymbol.resource.applyUndoableAction(function () {
              (nsTaskchainmodeler.ModelImpl as any).onPostDropDataSource(oSymbol.object, oParentSymbolOrDiagram, {
                droppedSymbol: oSymbol,
              });
            });
            return true;
          case this.DROP_ON_DIAGRAM:
            this.hideNewParallelReplaceSelector();
            return false;
          case this.DROP_NOT_PERMITTED:
            return true;
          default:
            break;
        }

        return false;
      },

      onHTML5SymbolDragleave: function (oGalileiEvent, oSymbol) {
        this.hideNewParallelReplaceSelector();
      },

      /**
       * Checks if drop operation is permitted
       * @param {*} sParentSymbolClass parent' symbol class qualified name
       * @param {*} sSymbolClass symbol' class qualified name
       * @param {symbol} oOptionalParentSymbol the parent symbol
       * @param {symbol} oOptionalSymbol the dragged symbol
       */
      _checkDropOperation: function (sParentSymbolClass, sSymbolClass, oOptionalParentSymbol?, oOptionalSymbol?) {
        let nOperation = this.DROP_NOT_PERMITTED,
          bChooseOperationNeeded = false;
        if (
          sSymbolClass === "sap.cdw.taskchainmodeler.ui.TaskSymbol" &&
          sParentSymbolClass === "sap.cdw.taskchainmodeler.ui.TaskSymbol"
        ) {
          nOperation = this.DROP_TASK_ON_TASK;
          bChooseOperationNeeded = true;
        } else if (
          sSymbolClass === "sap.cdw.taskchainmodeler.ui.TaskSymbol" &&
          sParentSymbolClass === "sap.cdw.taskchainmodeler.ui.LinkSymbol"
        ) {
          nOperation = this.DROP_TASK_ON_LINK;
        } else if (sSymbolClass === this.LINK_SYMBOL && sParentSymbolClass === this.TASK_SYMBOL) {
          if (this._checkOperationAllowed(oOptionalSymbol, oOptionalParentSymbol)) {
            nOperation = this.DROP_LINK_ON_TASK;
          }
        } else if (sSymbolClass === this.LINK_SYMBOL && sParentSymbolClass === this.OPERATION_SYMBOL) {
          if (this._checkOperationAllowed(oOptionalSymbol, oOptionalParentSymbol)) {
            nOperation = this.DROP_LINK_ON_OPERATION;
          }
        } else if (sSymbolClass === this.TASK_SYMBOL && sParentSymbolClass === this.PLACEHOLDER_SYMBOL) {
          nOperation = this.DROP_TASK_ON_PLACEHOLDER;
        } else if (sSymbolClass === this.TASK_SYMBOL && sParentSymbolClass === this.BEGIN_SYMBOL) {
          if (this.currentDraggedOnSymbolHTML || oOptionalSymbol?.object?.isDanglingNode) {
            nOperation = this.DROP_TASK_ON_BEGIN;
          }
        } else if (sSymbolClass === this.TASK_SYMBOL && sParentSymbolClass === this.OPERATION_SYMBOL) {
          // Allow Drag and drop task symbol on operation symbol only from tree
          // Or Dangling object
          if (this.currentDraggedOnSymbolHTML || oOptionalSymbol?.object?.isDanglingNode) {
            nOperation = this.DROP_TASK_ON_OPERATION;
          }
        } else if (sSymbolClass === this.OPERATION_SYMBOL && sParentSymbolClass === this.TASK_SYMBOL) {
          // Allow Drag and drop operation symbol on task symbol
          // Or Dangling object
          if (oOptionalSymbol?.object?.isDanglingNode) {
            nOperation = this.DROP_OPERATION_ON_TASK;
          }
        }
        if (!sParentSymbolClass) {
          nOperation = this.DROP_ON_DIAGRAM;
          if (
            [
              "sap.cdw.taskchainmodeler.ui.TaskSymbol",
              "sap.cdw.taskchainmodeler.ui.OperationSymbol",
              "sap.cdw.taskchainmodeler.ui.PlaceHolderSymbol",
            ].includes(sSymbolClass) &&
            oOptionalSymbol &&
            oOptionalSymbol.getAllLinkSymbolss &&
            oOptionalSymbol.getAllLinkSymbolss().length
          ) {
            nOperation = this.DROP_NOT_PERMITTED;
          }
        }
        return {
          operation: nOperation,
          chooseOperationNeeded: bChooseOperationNeeded,
          draggedNodeIsTask: sSymbolClass === "sap.cdw.taskchainmodeler.ui.TaskSymbol",
          draggedOnNodeIsTask: sParentSymbolClass === "sap.cdw.taskchainmodeler.ui.TaskSymbol",
          draggedNodeHasOnePredecessorNode: oOptionalParentSymbol?.object?.predecessorNodes?.length === 1,
        };
      },

      /**
       * Checks if drop operation is allowed for link symbol
       * @param {symbol} oDraggedSymbol the dragged symbol
       * @param {symbol} oSymbol the parent symbol
       */
      _checkOperationAllowed: function (oDraggedSymbol, oSymbol, source?) {
        if ((oDraggedSymbol || source) && oSymbol) {
          const oSource = source || oDraggedSymbol.object.source;
          if (oSource && oSymbol.object) {
            const oTarget = oSymbol.object;
            const linksAlreadyExist = oSource.resource.model.links.selectAllObjects({
              "source.objectId": oSource.objectId,
              "target.objectId": oTarget.objectId,
            }).length;
            if (linksAlreadyExist > 0) {
              return false;
            }
            if (oSource.isStart && oTarget.isTask) {
              return true;
            }
            const aObj = this._getBackwardNodes(oSource);
            if (aObj.includes(oTarget)) {
              return false;
            }
            if (oSource.isStart && oTarget.isOperator) {
              return false;
            }
            if (oSource.isDanglingNode || oSource.isDanglingBranch) {
              if (oTarget.isDanglingNode || oTarget.isDanglingBranch) {
                return true;
              }
              return false;
            }
          }
        }
        return true;
      },

      /**
       * Get all objects connected backwards
       */
      _getBackwardNodes: function (object) {
        if (object) {
          return object.allPredecessorNodes ? object.allPredecessorNodes : [];
        }
      },

      /**
       * Defines a gradient.
       * @function
       * @name addLinearGradient
       * @memberOf sap.cdw.taskchainmodeler.ui.DiagramEditorExtension#
       */
      addLinearGradient: function (sGradientId, sStartColor, sStopColor) {
        const oGradient = new sap.galilei.ui.common.style.LinearGradient({
          id: sGradientId,
          stops: [
            {
              offset: "5%",
              color: sStartColor,
            },
            {
              offset: "85%",
              color: sStopColor,
            },
          ],
        });

        oGradient.createGradient(this.viewer);
      },

      /**
       * Checks whether a symbol can be insert on a link symbol at the position aPoint.
       * @function
       * @name canInsertSymbolOnLinkSymbol
       * @memberOf sap.cdw.taskchainmodeler.ui.DiagramEditorExtension#
       * @param {String} sSymbolClass The node symbol class qualified name.
       * @param {Object} oCreateParam The input and output parameters. The parameters contain:
       * {
       *     point: (in/out) The view point where the symbol should be created.
       *     linkSymbol: (in/out) The link symbol.
       *     defaultPosition: (in) If true, the create position is not specified.
       *     symbol: (in) The node symbol to insert (move).
       * }
       * @returns {Boolean} true if the symbol can be inserted.
       */
      canInsertSymbolOnLinkSymbol: function (sSymbolClass, oCreateParam) {
        return true;
      },

      /**
       * Checks whether a link symbol can be created between the source symbol and the target symbol using the link symbol tool definition.
       * @function
       * @name canCreateLinkSymbol
       * @memberOf sap.modeling.bpmn.ui.DiagramEditorExtension#
       * @param {Symbol} oSourceSymbol The source symbol.
       * @param {Symbol} oTargetSymbol The target symbol.
       * @param {Object} oLinkTool The link symbol tool definition.
       * @param {Symbol} [oLinkSymbol] The existing link symbol in case of moving attach point.
       * @returns {boolean} true if the link symbol can be created.
       */
      canCreateLinkSymbol: function (oSourceSymbol, oTargetSymbol, oLinkTool, oLinkSymbol) {
        if (oSourceSymbol === oTargetSymbol) {
          return false;
        } else {
          const bCanCreateLinkSymbol = this.defaultCanCreateLinkSymbol(
            oSourceSymbol,
            oTargetSymbol,
            oLinkTool,
            oLinkSymbol
          );
          const oCheckDropOperationResult = this._checkOperationAllowed(
            oSourceSymbol,
            oTargetSymbol,
            oSourceSymbol.object
          );
          return oCheckDropOperationResult && bCanCreateLinkSymbol;
        }
      },

      /**
       * Performs transformation after the creation of a link symbol.
       * @function
       * @name postCreateLinkSymbol
       * @memberOf com.sap.powerdesigner.web.galilei.eam.diagram.EditorExtension#
       * @param {oSourceSymbol} sourceSymbol The Source symbol.
       * @param {oTargetSymbol} targetSymbol The Target symbol.
       * @param {oLinkSymbol} sourceSymbol The Link symbol.
       */
      postCreateLinkSymbol: function (oSourceSymbol, oTargetSymbol, oLinkSymbol) {
        if (
          oLinkSymbol &&
          oLinkSymbol.classDefinition &&
          oLinkSymbol.classDefinition.qualifiedName === this.LINK_SYMBOL
        ) {
          nsTaskchainmodeler.ModelImpl.createOrMergeLink(oLinkSymbol.object.source, oLinkSymbol.object);
        }
      },

      /**
       * Checks whether the source link symbol should be preserved and attached to the target of the target link symbol.
       * @function
       * @name canPreserveLinkAfterDelete
       * @memberOf sap.cdw.taskchainmodeler.ui.DiagramEditorExtension#
       * @param {Symbol} oSymbol The node symbol.
       * @returns {boolean}
       */
      canPreserveLinkAfterDelete: function (oSymbol) {
        return true;
      },

      /**
       * Selects a link symbol tool definition between the source symbol and target symbol.
       * @function
       * @name selectLinkSymbolDefinition
       * @memberOf sap.cdw.taskchainmodeler.ui.DiagramEditorExtension#
       * @param {Symbol} oSourceSymbol The source symbol.
       * @param {Symbol} oTargetSymbol The target symbol.
       * @returns {Object} The link symbol definition to use.
       */
      selectLinkSymbolDefinition: function (oSourceSymbol, oTargetSymbol) {
        // TODO
        const oTool = sap.galilei.ui.editor.tool.getTool(this.LINK_SYMBOL);

        if (oTool && oTool.selectLinkSymbolDefinition) {
          return oTool.selectLinkSymbolDefinition(oSourceSymbol, oTargetSymbol);
        }
        return {
          linkSymbolClass: this.LINK_SYMBOL,
        };
      },

      /**
       * Gets the context button pad definition for symbol.
       * @function
       * @name getContextButtonPad
       * @memberOf sap.cdw.taskchainmodeler.ui.DiagramEditorExtension#
       * @param {Object} oSymbol The symbol.
       */
      getContextButtonPad: function (oSymbol) {
        const aButtons = [],
          oNode = oSymbol.object;
        let isLinkSymbol = false,
          isAddAllOperation = false,
          isAddOrOperation = false,
          isAddParallelBranch = false,
          isAddPlaceHolder = false,
          isEditSymbolCommand = false,
          isDeleteSymbolCommand = false,
          isImpactLineageCommand = false;
        const i18nModel = this.editor.controller.getView().getModel("i18n"),
          oFeatures = sap.ui.getCore().getModel("featureflags").getData();
        if (oSymbol) {
          switch (oSymbol.classDefinition.qualifiedName) {
            case this.OPERATION_SYMBOL:
              isLinkSymbol = true;
              if (oSymbol.object?.predecessorNodes?.length === 1) {
                isAddParallelBranch = true;
              }
              isAddPlaceHolder = true;
              if (oFeatures.DWCO_TASKCHAIN_DIGRAM_ORIENTATION_MODE) {
                isDeleteSymbolCommand = true;
              }
              break;
            case this.TASK_SYMBOL:
              isLinkSymbol = true;
              isAddAllOperation = true;
              isAddOrOperation = true;
              if (oSymbol.object?.predecessorNodes?.length === 1) {
                isAddParallelBranch = true;
              }
              isAddPlaceHolder = true;
              if (
                !(
                  oSymbol.object?.isSQLScriptProcedure ||
                  oSymbol.object?.isBWProcessChain ||
                  oSymbol.object?.isRestApi ||
                  oSymbol.object?.isNotificationTask
                )
              ) {
                isEditSymbolCommand = true;
              }
              isDeleteSymbolCommand = true;
              // Impact & Lineage button
              if (
                !(
                  oSymbol.object?.isSQLScriptProcedure ||
                  oSymbol.object?.isBWProcessChain ||
                  oSymbol.object?.isRestApi ||
                  oSymbol.object?.isNotificationTask
                )
              ) {
                isImpactLineageCommand = true;
              }
              break;
            case this.BEGIN_SYMBOL:
              isLinkSymbol = true;
              break;
            case this.LINK_SYMBOL:
              return aButtons;
          }
        }

        if (isLinkSymbol) {
          aButtons.push({
            toolName: this.LINK_SYMBOL,
            tooltip: i18nModel ? i18nModel.getProperty("txtConnectToTaskOperator") : "",
          });
        }
        if (isAddAllOperation) {
          aButtons.push({
            commandName: this.OPERATION_SYMBOL_COMMAND,
            tooltip: i18nModel ? i18nModel.getProperty("addALLOperation") : "ALL",
            text: OPERATORS.AND,
            supportMultiSelection: false,
          });
        }
        if (isAddOrOperation) {
          aButtons.push({
            commandName: this.OPERATION_ANY_SYMBOL_COMMAND,
            tooltip: i18nModel ? i18nModel.getProperty("addOROperation") : "ANY",
            text: OPERATORS.OR,
            supportMultiSelection: false,
          });
        }
        if (isAddParallelBranch) {
          aButtons.push({
            commandName: this.PARALLEL_SYMBOL_COMMAND,
            tooltip: i18nModel ? i18nModel.getProperty("addparallelbranch") : "",
            smallIcon: "sap-icon://sac/network",
          });
        }
        if (isAddPlaceHolder) {
          aButtons.push({
            commandName: this.PLACEHOLDER_SYMBOL_COMMAND,
            tooltip: i18nModel ? i18nModel.getProperty("addplaceholder") : "",
            smallIcon: "sap-icon://add",
          });
        }
        if (isEditSymbolCommand) {
          aButtons.push({
            commandName: this.EDIT_WITH_COMMAND,
            tooltip: i18nModel.getProperty("openInNewTab"),
            smallIcon: "sap-icon://inspect",
          });
        }
        if (isDeleteSymbolCommand) {
          aButtons.push({
            commandName: this.DELETE_SYMBOL_COMMAND,
            tooltip: i18nModel && i18nModel.getProperty("delete"),
            smallIcon: "sap-icon://delete",
          });
        }
        if (isImpactLineageCommand) {
          aButtons.unshift({
            commandName: this.IMPACT_LINEAGE_COMMAND,
            tooltip: i18nModel.getProperty("txtOpenImpactLineage"),
            smallIcon: "sap-icon://sac/linked-analysis-tree",
          });
        }

        this.addContextButtonSettings(aButtons);
        this.addImagesFolder(aButtons);
        return aButtons;
      },

      /**
       * Gets the commands definition. The definition is an array of command definition.
       * Command definition has the parameters:
       * name: <The command name>
       * displayName: <The command display name>
       * tooltip: <The command tooltip>
       * type: <(optional) The command type>
       * isEnabled: <Indicates if the command is enabled>
       * isHidden: <Indicates if the command is visible>
       * canExecute: function (oParam), where oParam contains editor, diagram, symbol
       * execute: function (oParam)
       * smallIcon: <The small icon URL, usually 16x16>
       * largeIcon: <(optional) The large icon URL, usually 32x32>
       * @function
       * @name getCommandsDefinition
       * @memberOf sap.cdw.ermodeler.ui.DiagramEditorExtension#
       * @param {Array} The commands definition.
       */
      getCommandsDefinition: function () {
        const self = this;
        const i18nModel = this.editor.controller.getView().getModel("i18n");
        let oCommandsDef;

        oCommandsDef = [
          {
            // Defines an add attribute command
            name: this.DELETE_SYMBOL_COMMAND,
            className: "sap.galilei.ui.editor.command.DeleteSymbol",
            tooltip: i18nModel ? i18nModel.getProperty("delete") : "",
            smallIcon: "sap-icon://delete",
          },
          {
            // Defines an add placeholder command
            name: this.PLACEHOLDER_SYMBOL_COMMAND,
            className: "sap.cdw.taskchainmodeler.AddPlaceHolderCommand",
            tooltip: i18nModel ? i18nModel.getProperty("addplaceholder") : "Add Placeholder",
            smallIcon: "sap-icon://add",
          },
          {
            // Defines an add parallel branch command
            name: this.PARALLEL_SYMBOL_COMMAND,
            className: "sap.cdw.taskchainmodeler.AddParallelBranchCommand",
            tooltip: i18nModel ? i18nModel.getProperty("addparallelbranch") : "Add Parallel Branch",
            smallIcon: "sap-icon://sac/network",
          },
          {
            // Defines an add operation command
            name: this.OPERATION_SYMBOL_COMMAND,
            className: "sap.cdw.taskchainmodeler.AddOperationCommand",
            tooltip: i18nModel ? i18nModel.getProperty("addOperator") : "Add Operator",
            supportMultiSelection: false,
          },
          {
            // Defines an add operation command
            name: this.OPERATION_ANY_SYMBOL_COMMAND,
            className: "sap.cdw.taskchainmodeler.AddAnyOperationCommand",
            tooltip: i18nModel ? i18nModel.getProperty("addOperator") : "Add Operator",
            supportMultiSelection: false,
          },
        ];

        this.addImagesFolder(oCommandsDef);
        return oCommandsDef;
      },

      /**
       * Gets the tools definition. The definition is an array of tool definition.
       * Create node symbol tool definition the parameters:
       * name: <(optional) The tool name>,
       * type: sap.galilei.ui.editor.tool.Types.createNodeSymbolTool
       * symbolClass: <The symbol class qualified name>
       * symbolParam: <(optional) The symbol property values>
       * objectClass: < (optional) The object class qualified name>
       * objectParam:  <(optional) The object property values>
       * objectReference: <(optional) The name of the reference where the object should be added>
       * smallIcon: <The small icon URL, usually 16x16>
       * largeIcon: <(optional) The large icon URL, usually 32x32>
       * cursor: <(optional) The cursor URL, usually 32x32>
       *
       * Create link symbol tool definition the parameters:
       * name: <The tool name>
       * type: sap.galilei.ui.editor.tool.Types.createLinkSymbolTool
       * linksDefinition: <Array of supported link symbols>
       * {
       * sourceSymbol: <The source symbol class qualified name>
       * targetSymbol: <The target symbol class qualified name>
       * linkSymbolClass: <The link symbol class qualified name>
       * linkSymbolParam: <(optional) The link symbol property values>
       * linkObjectClass: < (optional) The link object class qualified name>
       * linkObjectParam:  <(optional) The link object property values>
       * linkObjectReference: <(optional) The name of the reference where the link object should be added>
       * }
       * smallIcon: <The small icon URL, usually 16x16>
       * largeIcon: <(optional) The large icon URL, usually 32x32>
       * cursor: <The cursor URL>
       *
       * Normal tool definition the parameters:
       * name: <The tool name>
       * type: sap.galilei.ui.editor.tool.Types.tool
       * canExecute: function (oParam), where oParam contains editor, diagram, symbol
       * execute: function (oParam)
       * smallIcon: <The small icon URL, usually 16x16>
       * largeIcon: <(optional) The large icon URL, usually 32x32>
       * cursor: <The cursor URL>
       * @function
       * @name getToolsDefinition
       * @memberOf sap.cdw.taskchainmodeler.ui.DiagramEditorExtension#
       * @param {Array} The tools definition.
       */
      getToolsDefinition: function () {
        const controller = this.editor.controller,
          oToolsDef = [
            // Entity tool
            {
              name: this.TASK_SYMBOL,
              type: sap.galilei.ui.editor.tool.Types.createNodeSymbolTool,
              tooltip: controller && controller.localizeERMessage && controller.localizeERMessage("@createEntity"),
              symbolClass: this.TASK_SYMBOL,
              objectParam: this.TASK_OBJECT,
              smallIcon: "sap-icon://sac/new-table",
            },
            // Operation tool
            {
              name: this.OPERATION_SYMBOL,
              type: sap.galilei.ui.editor.tool.Types.createNodeSymbolTool,
              tooltip: controller && controller.localizeERMessage && controller.localizeERMessage("@createEntity"),
              symbolClass: this.OPERATION_SYMBOL,
              objectParam: this.OPERATION_OBJECT,
              smallIcon: "sap-icon://sac/new-table",
            },
            // Link tool
            {
              name: this.LINK_SYMBOL,
              type: sap.galilei.ui.editor.tool.Types.createLinkSymbolTool,
              tooltip: controller && controller.localizeERMessage && controller.localizeERMessage("@createLink"),
              smallIcon: "sap-icon://arrow-right",
              linksDefinition: [
                {
                  sourceSymbol: this.TASK_SYMBOL,
                  targetSymbol: this.TASK_SYMBOL,
                  linkSymbolClass: this.LINK_SYMBOL,
                  linkObjectParam: this.LINK_OBJECT,
                },
                {
                  sourceSymbol: this.OPERATION_SYMBOL,
                  targetSymbol: this.TASK_SYMBOL,
                  linkSymbolClass: this.LINK_SYMBOL,
                  linkObjectParam: this.LINK_OBJECT,
                },
                {
                  sourceSymbol: this.TASK_SYMBOL,
                  targetSymbol: this.OPERATION_SYMBOL,
                  linkSymbolClass: this.LINK_SYMBOL,
                  linkObjectParam: this.LINK_OBJECT,
                },
                {
                  sourceSymbol: this.OUTPUT_SYMBOL,
                  targetSymbol: this.TASK_SYMBOL,
                  linkSymbolClass: this.LINK_SYMBOL,
                  linkObjectParam: this.LINK_OBJECT,
                },
                {
                  targetSymbol: this.OUTPUT_SYMBOL,
                  sourceSymbol: this.INPUT_SYMBOL,
                  linkSymbolClass: this.LINK_SYMBOL,
                  linkObjectParam: this.LINK_OBJECT,
                },
                {
                  targetSymbol: this.INPUT_SYMBOL,
                  sourceSymbol: this.OUTPUT_SYMBOL,
                  linkSymbolClass: this.LINK_SYMBOL,
                  linkObjectParam: this.LINK_OBJECT,
                },
                {
                  targetSymbol: this.INPUT_SYMBOL,
                  sourceSymbol: this.BEGIN_SYMBOL,
                  linkSymbolClass: this.LINK_SYMBOL,
                  linkObjectParam: this.LINK_OBJECT,
                },
                {
                  targetSymbol: this.TASK_SYMBOL,
                  sourceSymbol: this.BEGIN_SYMBOL,
                  linkSymbolClass: this.LINK_SYMBOL,
                  linkObjectParam: this.LINK_OBJECT,
                },
                {
                  targetSymbol: this.OPERATION_SYMBOL,
                  sourceSymbol: this.OPERATION_SYMBOL,
                  linkSymbolClass: this.LINK_SYMBOL,
                  linkObjectParam: this.LINK_OBJECT,
                },
              ],
            },
          ];

        this.addImagesFolder(oToolsDef);
        return oToolsDef;
      },

      /**
       * (Override)
       * Registers the diagram specific symbol creation tools.
       * @function
       * @name registerDiagramTools
       * @memberOf sap.galilei.ui.editor.DiagramEditorExtension#
       */
      registerDiagramTools: function () {
        this.editor.defaultRegisterDiagramTools();
        // this._registerCommonTools();
      },

      initializeNewParallelReplaceSelectorForHTML5DnD: function () {
        const self = this;
        const oNewParallelReplaceSelector = jQuery(
            "#" + this.sPrefixId + "taskChainNewParallelReplaceSelectorContainer"
          ),
          oAddNewSelector = oNewParallelReplaceSelector.find("#" + this.sPrefixId + "addNewSelector"),
          oParallelSelector = oNewParallelReplaceSelector.find("#" + this.sPrefixId + "addParallelSelector"),
          oReplaceSelector = oNewParallelReplaceSelector.find("#" + this.sPrefixId + "replaceSelector");

        // On Drop of entity, by default join creation is triggered, so add selector class for join
        oAddNewSelector.addClass("newParallelReplaceSelectordragging");
        oParallelSelector.removeClass("newParallelReplaceSelectordragging");
        oReplaceSelector.removeClass("newParallelReplaceSelectordragging");

        // Add New
        // oAddNewSelector.removeClass("newParallelReplaceSelectordragging");
        oAddNewSelector.off("dragover");
        oAddNewSelector.on("dragover", function (event) {
          self.DRAGGING = true;
          event.preventDefault();
          event.stopPropagation();
          // remove selector class from join/replace
          // in case of union, removing the join/replace selector classes
          oParallelSelector.removeClass("newParallelReplaceSelectordragging");

          // add class to join
          $(this).addClass("newParallelReplaceSelectordragging");
        });
        oAddNewSelector.off("drop");
        oAddNewSelector.on("drop", function (event) {
          self.onHTML5PDropOnNewParallelReplaceSelector(self.currentDraggedOnSymbol, self.TASK_SYMBOL, "AddNew");
          event.preventDefault();
          event.stopPropagation();
        });
        oAddNewSelector.off("dragleave");
        oAddNewSelector.on("dragleave", function (event) {
          event.preventDefault();
          event.stopPropagation();
          $(this).removeClass("newParallelReplaceSelectordragging");
        });
        // join
        oParallelSelector.off("dragover");
        oParallelSelector.on("dragover", function (event) {
          self.DRAGGING = true;
          event.preventDefault();
          event.stopPropagation();
          // remove selector class from union/replace
          // in case of join, removing the union/replace selector classes
          oAddNewSelector.removeClass("newParallelReplaceSelectordragging");
          oReplaceSelector.removeClass("newParallelReplaceSelectordragging");
          // add class to join
          $(this).addClass("newParallelReplaceSelectordragging");
        });
        oParallelSelector.off("drop");
        oParallelSelector.on("drop", function (event) {
          self.onHTML5PDropOnNewParallelReplaceSelector(self.currentDraggedOnSymbol, self.TASK_SYMBOL, "Parallel");
          event.preventDefault();
          event.stopPropagation();
        });
        oParallelSelector.off("dragleave");
        oParallelSelector.on("dragleave", function (event) {
          event.preventDefault();
          event.stopPropagation();
          $(this).removeClass("newParallelReplaceSelectordragging");
        });
        // replace
        oReplaceSelector.removeClass("newParallelReplaceSelectordragging");
        oReplaceSelector.off("dragover");
        oReplaceSelector.on("dragover", function (event) {
          self.DRAGGING = true;
          event.preventDefault();
          event.stopPropagation();
          // remove selector class from join/union
          // in case of replace, removing the join/replace selector classes
          oParallelSelector.removeClass("newParallelReplaceSelectordragging");
          oAddNewSelector.removeClass("newParallelReplaceSelectordragging");
          // add class to replace
          $(this).addClass("newParallelReplaceSelectordragging");
        });
        oReplaceSelector.off("drop");
        oReplaceSelector.on("drop", function (event) {
          self.onHTML5PDropOnNewParallelReplaceSelector(self.currentDraggedOnSymbol, self.TASK_SYMBOL, "Replace");
          event.preventDefault();
          event.stopPropagation();
        });
        oReplaceSelector.off("dragleave");
        oReplaceSelector.on("dragleave", function (event) {
          event.preventDefault();
          event.stopPropagation();
          $(this).removeClass("newParallelReplaceSelectordragging");
        });

        oAddNewSelector.off("click");
        oAddNewSelector.on("click", function (event) {
          self.onHTML5PDropOnNewParallelReplaceSelector(self.currentDraggedOnSymbol, self.TASK_SYMBOL, "AddNew");
          event.preventDefault();
          event.stopPropagation();
        });

        oParallelSelector.off("click");
        oParallelSelector.on("click", function (event) {
          self.onHTML5PDropOnNewParallelReplaceSelector(self.currentDraggedOnSymbol, self.TASK_SYMBOL, "Parallel");
          event.preventDefault();
          event.stopPropagation();
        });

        oReplaceSelector.off("click");
        oReplaceSelector.on("click", function (event) {
          self.onHTML5PDropOnNewParallelReplaceSelector(self.currentDraggedOnSymbol, self.TASK_SYMBOL, "Replace");
          event.preventDefault();
          event.stopPropagation();
        });

        // union, join, replace
        oNewParallelReplaceSelector.off("dragleave");
        oNewParallelReplaceSelector.on("dragleave", function (event) {
          self.DRAGGING = false;
          setTimeout(function () {
            if (!self.DRAGGING) {
              self.hideNewParallelReplaceSelector();
            }
          }, 1000);

          event.preventDefault();
          event.stopPropagation();
        });
      },

      initializeNewParallelReplaceSelectorForMouseEvents: function () {
        const self = this,
          oNewParallelReplaceSelector = jQuery("#" + this.sPrefixId + "taskChainNewParallelReplaceSelectorContainer"),
          oAddNewSelector = oNewParallelReplaceSelector.find("#" + this.sPrefixId + "addNewSelector"),
          oParallelSelector = oNewParallelReplaceSelector.find("#" + this.sPrefixId + "addParallelSelector"),
          oReplaceSelector = oNewParallelReplaceSelector.find("#" + this.sPrefixId + "replaceSelector");

        // On Drop of entity, By default add new task creation triggers, So Added selector class for addNew
        oAddNewSelector.addClass("newParallelReplaceSelectordragging");
        oParallelSelector.removeClass("newParallelReplaceSelectordragging");
        oReplaceSelector.removeClass("newParallelReplaceSelectordragging");

        // addNew
        oAddNewSelector.off("mouseover");
        oAddNewSelector.on("mouseover", function (event) {
          event.preventDefault();
          event.stopPropagation();
          // remove selector class from parallel/replace
          // in case of union, removing the parallel/replace selector classes
          oParallelSelector.removeClass("newParallelReplaceSelectordragging");
          oReplaceSelector.removeClass("newParallelReplaceSelectordragging");
          // add class to addNew
          $(this).addClass("newParallelReplaceSelectordragging");
        });
        oAddNewSelector.off("mouseup");
        oAddNewSelector.on("mouseup", function (event) {
          if (self.currentDraggedSymbol && self.currentDraggedOnSymbol) {
            self.onDropOnNewParallelReplaceSelector(self.currentDraggedSymbol, self.currentDraggedOnSymbol, "AddNew");
          }
          self.currentDraggedSymbol = undefined;
          self.currentDraggedOnSymbol = undefined;
          self.currentDraggedOnSymbolHTML = undefined;
          event.preventDefault();
          event.stopPropagation();
        });
        oAddNewSelector.off("mouseleave");
        oAddNewSelector.on("mouseleave", function (event) {
          event.preventDefault();
          $(this).removeClass("newParallelReplaceSelectordragging");
        });

        // parallel
        oParallelSelector.off("mouseover");
        oParallelSelector.on("mouseover", function (event) {
          event.preventDefault();
          event.stopPropagation();
          // remove selector class from union/replace
          // in case of parallel, removing the new/replace selector classes
          oAddNewSelector.removeClass("newParallelReplaceSelectordragging");
          oReplaceSelector.removeClass("newParallelReplaceSelectordragging");
          // add class to join
          $(this).addClass("newParallelReplaceSelectordragging");
        });
        oParallelSelector.off("mouseup");
        oParallelSelector.on("mouseup", function (event) {
          if (self.currentDraggedSymbol && self.currentDraggedOnSymbol) {
            self.onDropOnNewParallelReplaceSelector(self.currentDraggedSymbol, self.currentDraggedOnSymbol, "Parallel");
          }
          self.currentDraggedSymbol = undefined;
          self.currentDraggedOnSymbol = undefined;
          event.preventDefault();
          event.stopPropagation();
        });
        oParallelSelector.off("mouseleave");
        oParallelSelector.on("mouseleave", function (event) {
          event.preventDefault();
          $(this).removeClass("newParallelReplaceSelectordragging");
        });

        // replace
        oReplaceSelector.removeClass("newParallelReplaceSelectordragging");
        oReplaceSelector.off("mouseover");
        oReplaceSelector.on("mouseover", function (event) {
          event.preventDefault();
          event.stopPropagation();
          // remove selector class from parallel/addnew
          // in case of replace, removing the parallel/addnew selector classes
          oParallelSelector.removeClass("newParallelReplaceSelectordragging");
          oAddNewSelector.removeClass("newParallelReplaceSelectordragging");
          // add class to replace
          $(this).addClass("newParallelReplaceSelectordragging");
        });
        oReplaceSelector.off("mouseup");
        oReplaceSelector.on("mouseup", function (event) {
          if (self.currentDraggedSymbol && self.currentDraggedOnSymbol) {
            self.onDropOnNewParallelReplaceSelector(self.currentDraggedSymbol, self.currentDraggedOnSymbol, "Replace");
          }
          self.currentDraggedSymbol = undefined;
          self.currentDraggedOnSymbol = undefined;
          event.preventDefault();
          event.stopPropagation();
        });
        oReplaceSelector.off("mouseleave");
        oReplaceSelector.on("mouseleave", function (event) {
          event.preventDefault();
          $(this).removeClass("newParallelReplaceSelectordragging");
        });

        // for all
        oNewParallelReplaceSelector.off("mouseleave");
        oNewParallelReplaceSelector.on("mouseleave", function (event) {
          self.hideNewParallelReplaceSelector();
          event.preventDefault();
          event.stopPropagation();
        });
        oNewParallelReplaceSelector.off("mouseover");
        oNewParallelReplaceSelector.on("mouseover", function (event) {
          event.preventDefault();
          event.stopPropagation();
        });

        // mouseup would not be triggered in case if user is dragging the symbol from diagram to the menu
        // so we need to handle the dragend event publised by the dragged symbol
        if (!self._onSymbolDragEnd) {
          self._onSymbolDragEnd = function (event) {
            clearTimeout(self._onSymbolDragEndTimeout);
            self._onSymbolDragEndTimeout = setTimeout(function () {
              const srcElement = event.gesture?.srcEvent?.srcElement;
              if (!srcElement) return;
              let type;
              if (oAddNewSelector[0]?.contains(srcElement)) {
                type = "AddNew";
                self.currentDraggedOnSymbolHTML = undefined;
              } else if (oParallelSelector[0]?.contains(srcElement)) {
                type = "Parallel";
              } else if (oReplaceSelector[0]?.contains(srcElement)) {
                type = "Replace";
              }
              if (type) {
                if (self.currentDraggedSymbol && self.currentDraggedOnSymbol) {
                  self.onDropOnNewParallelReplaceSelector(self.currentDraggedSymbol, self.currentDraggedOnSymbol, type);
                }
                self.currentDraggedSymbol = undefined;
                self.currentDraggedOnSymbol = undefined;
              }
            });
          };
        }
        document.removeEventListener("dragend", self._onSymbolDragEnd);
        document.addEventListener("dragend", self._onSymbolDragEnd);
      },

      initializeNewParallelReplaceSelector: function () {
        const appendLabel = function (sId, sIcon, sLabel) {
          const hboxitems = new sap.m.HBox({
            renderType: sap.m.FlexRendertype.Bare,
            items: [
              // sap-icon://[collection-name]/[icon-name]
              new sap.ui.core.Icon({
                src: "sap-icon://" + sIcon,
              }),
              // font & unicode char
              new sap.m.Text({
                text: sLabel,
              }).addStyleClass("sapiconfont"),
            ],
          });
          hboxitems.addStyleClass("sapUiTinyMarginBottom");
          hboxitems.placeAt(sId);
        };
        const controller = this.editor.controller;
        appendLabel(
          this.sPrefixId + "addNewSelector",
          "add",
          controller.getParentController?.().localizeText("addNewSelector")
        );
        appendLabel(
          this.sPrefixId + "addParallelSelector",
          "sac/network",
          controller.getParentController?.().localizeText("parallelSelector")
        );
        appendLabel(
          this.sPrefixId + "replaceSelector",
          "sac/replace",
          controller.getParentController?.().localizeText("replaceSelector")
        );
      },

      openByNewParallelReplaceSelector: function (
        oOpenBySymbol,
        draggedNodeHasOnePredecessorNode: boolean,
        draggedNodeIsTask: boolean,
        draggedOnNodeIsTask: boolean,
        oSymbol,
        hasParallelOption: boolean = true
      ) {
        const zoomScale = this.viewer?.zoomScale || 1;
        const OFFSET_TOP = 20,
          OFFSET_LEFT = (5 + 4) * zoomScale, // 4 means the threshold value to sanp to grid.
          ITEM_HEIGHT = 34,
          BORDER_WIDTH = 2, // 1 is too narrow for dark mode (bottom border)
          oNewParallelReplaceSelector = jQuery("#" + this.sPrefixId + "taskChainNewParallelReplaceSelectorContainer"),
          oOpenBy = jQuery(oOpenBySymbol),
          oBoundingClientRect = oOpenBy[0].getBoundingClientRect(),
          nWidth = oBoundingClientRect.width,
          oPosition = oOpenBy.position(),
          oParentOffset = jQuery(oNewParallelReplaceSelector.offsetParent()).offset();

        oNewParallelReplaceSelector.addClass("newParallelReplaceSelectorContainerVisible");
        oNewParallelReplaceSelector.css("top", oPosition.top - oParentOffset.top - OFFSET_TOP + "px");
        oNewParallelReplaceSelector.css("left", oPosition.left - oParentOffset.left - OFFSET_LEFT + nWidth + "px");

        // item visibility / pad-height (depends on number of items)
        // join
        let numberOfItems = 0;
        // union
        const oAddNewSelector = oNewParallelReplaceSelector.find("#" + this.sPrefixId + "addNewSelector");
        numberOfItems++;
        oAddNewSelector.css("visibility", "visible");
        oAddNewSelector.css("height", ITEM_HEIGHT + "px");
        // replace

        const oReplaceSelector = oNewParallelReplaceSelector.find("#" + this.sPrefixId + "replaceSelector");
        if (
          draggedNodeIsTask &&
          draggedOnNodeIsTask &&
          (oSymbol.object?.predecessorNodes?.length > 0 || oSymbol.object?.successorNodes?.length > 0)
        ) {
          numberOfItems++;
          oReplaceSelector.css("visibility", "visible");
          oReplaceSelector.css("height", ITEM_HEIGHT + "px");
        } else {
          oReplaceSelector.css("visibility", "hidden");
          oReplaceSelector.css("height", "0px");
        }

        const oParallelSelector = oNewParallelReplaceSelector.find("#" + this.sPrefixId + "addParallelSelector");
        if (draggedNodeHasOnePredecessorNode && hasParallelOption) {
          numberOfItems++;
          oParallelSelector.css("visibility", "visible");
          oParallelSelector.css("height", ITEM_HEIGHT + "px");
        } else {
          oParallelSelector.css("visibility", "hidden");
          oParallelSelector.css("height", "0px");
        }

        const height = ITEM_HEIGHT * numberOfItems + 2 * BORDER_WIDTH;
        oNewParallelReplaceSelector.css("height", height + "px");
      },

      hideNewParallelReplaceSelector: function () {
        this.currentDraggedOnSymbolHTML = undefined;
        const oNewParallelReplaceSelector = jQuery(
          "#" + this.sPrefixId + "taskChainNewParallelReplaceSelectorContainer"
        );
        oNewParallelReplaceSelector.removeClass("newParallelReplaceSelectorContainerVisible");
        oNewParallelReplaceSelector.css("top", "-200px");
        oNewParallelReplaceSelector.css("left", "-200px");
        oNewParallelReplaceSelector.css("height", "40px");
      },

      // HTML5 Drag&Drop
      /**
       * Called when HTML5 drop event is happened in diagram
       * here we close the choose' popover if exists
       */
      onHTML5Drop: function () {
        this.hideNewParallelReplaceSelector();
      },

      onHTML5PDropOnNewParallelReplaceSelector: function (oDroppedOnSymbol, sDroppedClassName, sOperation) {
        const self = this;
        if (self.currentDraggingData?.definitions?.then) {
          self.currentDraggingData.definitions.then((definitions) => {
            self.currentDraggingData.definitions = definitions?.csn?.definitions;
            self._onHTML5PDropOnNewParallelReplaceSelector.apply(self, [
              oDroppedOnSymbol,
              sDroppedClassName,
              sOperation,
            ]);
          });
        } else {
          self._onHTML5PDropOnNewParallelReplaceSelector.apply(self, [oDroppedOnSymbol, sDroppedClassName, sOperation]);
        }
      },

      _onHTML5PDropOnNewParallelReplaceSelector: function (oDroppedOnSymbol, sDroppedClassName, sOperation) {
        let sObjectClassName;
        let oObject;
        const self = this;
        const oResource = oDroppedOnSymbol.resource;
        self.editor.selectPointerTool();
        if (self.currentDraggingData) {
          oResource.applyUndoableAction(function () {
            // eslint-disable-next-line no-underscore-dangle
            sObjectClassName = (nsTaskchainmodeler.DiagramImpl as any)._getObjectClassName(sDroppedClassName); // ToDo 'as any'
            if (sObjectClassName) {
              oObject = self.createObject(sObjectClassName, self.currentDraggingData);
              const csnDefinitions = self.currentDraggingData && self.currentDraggingData.definitions;
              if (csnDefinitions && csnDefinitions[oObject.name] && csnDefinitions[oObject.name].then) {
                oObject.asyncReadyness = csnDefinitions && csnDefinitions[oObject.name];
              }
              let targetObject = oDroppedOnSymbol.object;
              if (sOperation === "Parallel" && targetObject?.predecessorNodes?.length) {
                targetObject = targetObject.predecessorNodes[0] || targetObject;
              }
              (nsTaskchainmodeler.ModelImpl as any).onPostDropDataSource(oObject, targetObject, {
                operation: sOperation,
              }); // ToDo 'as any'
              self.hideNewParallelReplaceSelector();
              if (oObject) {
                (nsTaskchainmodeler.ModelImpl as any).checkObjectChainable(
                  oObject,
                  self.editor?.controller?.getParentController().getSpaceName()
                );
              }
              if (oObject?.isNotificationTask || oObject?.isRestApi) {
                (self.editor.controller.getView().getModel("workbenchEnv") as sap.ui.model.json.JSONModel).setProperty(
                  "/isDragging",
                  false
                );
              }
            } else {
              delete self.currentDraggingData;
            }
          });
        }
      },

      onDropOnNewParallelReplaceSelector: function (oDraggedSymbol, oDroppedOnSymbol, sOperation) {
        const self = this;
        const oResource = oDroppedOnSymbol.resource;
        oResource.applyUndoableAction(function () {
          let targetObject = oDroppedOnSymbol.object;
          if (sOperation === "Parallel" && targetObject?.predecessorNodes?.length) {
            targetObject = targetObject.predecessorNodes[0] || targetObject;
          }
          (nsTaskchainmodeler.ModelImpl as any).onPostDropDataSource(oDraggedSymbol.object, targetObject, {
            operation: sOperation,
          }); // ToDo 'as any'
          self.hideNewParallelReplaceSelector();
        });
      },

      /**
       * Called when a HTML5 drag enter event on symbol happened
       */
      onHTML5SymbolDragenter: function (oGalileiEvent, oSymbol, bCanDrop, oCreateTool) {
        this.hideNewParallelReplaceSelector();
        // eslint-disable-next-line no-underscore-dangle
        this.currentDraggedOnSymbolHTML = oSymbol;
        const oCheckDropOperationResult = this._checkDropOperation(
          oSymbol.classDefinition.qualifiedName,
          oCreateTool.name,
          oSymbol
        );
        this.currentDraggedOnSymbolHTML = undefined;

        if (
          oCheckDropOperationResult.operation === this.DROP_NOT_PERMITTED ||
          !oCheckDropOperationResult.chooseOperationNeeded
        ) {
          // cannot drop Or no need for popover to choose the operation
          if (oCheckDropOperationResult.operation !== this.DROP_NOT_PERMITTED) {
            this.currentDraggedOnSymbolHTML = oSymbol;
            if (sap.ui && sap.ui.getCore instanceof Function) {
              sap.ui
                .getCore()
                .getEventBus()
                .publish(
                  nsTaskchainmodeler.DiagramImpl.TASKCHAIN_DIAGRAM_CHANNEL,
                  (nsTaskchainmodeler.DiagramImpl as any).HIGHLIGHT_EVENT,
                  {
                    symbols: [oSymbol],
                  }
                );
            }
            return true;
          } else {
            this.currentDraggedOnSymbolHTML = undefined;
            return;
          }
        } else {
          if (oCheckDropOperationResult.operation !== this.DROP_NOT_PERMITTED) {
            this.currentDraggedOnSymbolHTML = oSymbol;
            if (sap.ui && sap.ui.getCore instanceof Function) {
              sap.ui
                .getCore()
                .getEventBus()
                .publish(
                  nsTaskchainmodeler.DiagramImpl.TASKCHAIN_DIAGRAM_CHANNEL,
                  (nsTaskchainmodeler.DiagramImpl as any).HIGHLIGHT_EVENT,
                  {
                    symbols: [oSymbol],
                  }
                );
            }
          } else {
            this.currentDraggedOnSymbolHTML = undefined;
          }
        }

        this.initializeNewParallelReplaceSelectorForHTML5DnD();
        this.currentDraggedOnSymbol = oSymbol;
        this.openByNewParallelReplaceSelector(
          oSymbol.shape.svgNode,
          oCheckDropOperationResult.draggedNodeHasOnePredecessorNode,
          oCheckDropOperationResult.draggedNodeIsTask,
          oCheckDropOperationResult.draggedOnNodeIsTask,
          oSymbol
        );
        return true;
      },
    },
  });
});
