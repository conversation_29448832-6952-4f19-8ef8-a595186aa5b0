/** @format */

import { IEditorAggregatedValidations } from "../../abstractbuilder/api";
import { ValidationsControllerClass } from "../../abstractbuilder/controller/Validations.controller";
import { smartExtend } from "../../basecomponent/controller/BaseController.controller";
import { ILocalizedValidationMessages } from "../../commonui/control/validation/ValidationMessages";
import { NameUsage } from "../../commonui/utility/NameInputValidator";
import { MessageHandler } from "../../reuse/utility/MessageHandler";
import { NamingHelper } from "../../reuse/utility/NamingHelper";
import { ContentType, HttpMethod, ServiceCall } from "../../reuse/utility/ServiceCall";
import { showDialog } from "../../reuse/utility/UIHelper";
import { ModelToJSON } from "../js/model/ModelToJSON";
import Utils, { fromType } from "../js/utils";
import { TaskChainCommonProperties, TaskChainCommonPropertiesClass } from "./TaskChainCommonProperties.controller";

export class RESTTaskPropertiesClass extends TaskChainCommonPropertiesClass {
  panelCounter = 0;
  private galileiModel;
  private isDeployAndRun = false;

  public onInit(): void {
    super.onInit();

    sap.ui
      .getCore()
      .getEventBus()
      .subscribe("propertyPanel", "updateStatus", (channel, event, oResponse) => {
        if (oResponse && oResponse.data) {
          this.updateStatus(oResponse);
        } else {
          setTimeout(() => {
            this.fetchStatus();
          }, 1000);
        }
      });

    const editorIds = ["requestBody", "statusRequestBody"];

    editorIds.forEach((editorId) => {
      const editor = this.getView().byId(editorId);
      const aceEditor = editor["getAceEditor"]();
      aceEditor.getSession().on("changeAnnotation", () => {
        const error = aceEditor
          .getSession()
          .getAnnotations()
          .filter((a) => a.type === "error");
        this.validateJson(error, editorId, aceEditor);
      });
    });
  }

  setObjectModel(oObject) {
    const nodeObject = oObject;

    super.setObjectModel(oObject);
    if (this.galileiModel !== nodeObject) {
      this.galileiModel = nodeObject;
      if (!this.galileiModel.isNew) {
        this.fetchStatus();
      }
    }
  }

  public refreshModel() {
    if (!this.currentObject) {
      // undo / redo causing empty node to get created due to the way we create nodes on drag and drop
      return;
    }
    const model = {
      isConnectionHasError: false,
      displayName: "REST Task",
      selectedConnectionURL: "EMPTY_URL",
      connectionList: [],
      defaultHeaders: [
        { key: "accept", value: "application/json" },
        { key: "Content-Type", value: "application/json" },
      ],
      methodList: [
        { name: "POST", key: "POST" },
        { name: "PUT", key: "PUT" },
      ],
      mode: [
        { name: "Synchronous", key: "SYNC" },
        { name: "Asynchronous", key: "ASYNC" },
      ],
      methodType: "POST",
      indicatorCondition: [
        { statement: "equal to", condition: "EQUAL" },
        { statement: "not equal to", condition: "NOT_EQUAL" },
      ],
      statusMethodList: [
        { name: "GET", key: "GET" },
        { name: "POST", key: "POST" },
      ],
    };
    model.displayName = this.currentObject.name;
    model.connectionList = this.currentObject.resource?.model?.connectionList;
    const { url, state } = Utils.getConnectionUrl(this.currentObject);
    model.isConnectionHasError = this.currentObject.resource?.model?.isConnectionHasError ? true : false;

    const self = this;
    self.currentObject.resource.applyUndoableAction(
      function () {
        self.currentObject.selectedConnectionURL = url;
        self.currentObject.connectionNameValueState = state;

        if (self.currentObject.statusAPI?.request?.method === "GET") {
          self.currentObject.statusAPI.request.apiPathValueState = "None";
        }

        // Validate the input value
        if (
          self.currentObject.invokeAPI?.request?.apiPath &&
          /^(?!.*\.\.)(?!.*\.$)(?!.*\.\.$)(?!.*[\{\}])(?!$)[^\s]*$/.test(self.currentObject.invokeAPI.request.apiPath)
        ) {
          self.currentObject.invokeAPI.request.apiPathValueState = "None";
        }

        if (
          self.currentObject.statusAPI?.request?.apiPath &&
          /^(?!.*\.\.)(?!.*\.$)(?!.*\.\.$)(?!.*\{\})(?!$)[^\s]*(\{.+\}){1}[^\s]*$/.test(
            self.currentObject.statusAPI.request.apiPath
          )
        ) {
          self.currentObject.statusAPI.request.apiPathValueState = "None";
        }
      },
      undefined,
      true
    );

    const oModel = new sap.ui.model.json.JSONModel(model);
    oModel.setSizeLimit(1000);
    this.getView().setModel(oModel);

    this.visibilityFormatter(
      this.currentObject.invokeAPI.request.mode,
      this.currentObject.invokeAPI.response.from,
      this.currentObject.statusAPI?.request?.method
    );
    this.removeHeaderPanels();
    this.createHeaderPanels(this.currentObject);
  }

  public onTechnicalNameChange(oEvent) {
    // validate technical name should be unique for rest task inside the task chain
    const sNewValue = oEvent.getParameter("value");
    const input = this.getView().byId("technicalName") as sap.m.Input;
    const oModel = this.currentObject.resource?.model;
    const aNodes = oModel.nodes;
    const obj = oEvent.getSource();
    const i18nResourceBundle = this.getView().getModel("i18n").getResourceBundle();
    const object = this.getView().getModel("galileiModel").getData();

    // Check if the display name is unique
    const bUnique = this.isDisplayNameUnique(aNodes, sNewValue);

    const nameValidator = NamingHelper.getNameInputValidator();
    const error = nameValidator.onTechnicalNameChanged(
      object,
      input,
      "displayName",
      {
        duplicatedTechnicalName: i18nResourceBundle.getText("technicalNameValidation"),
        emptyTechnicalName: i18nResourceBundle.getText("technicalNameValidation"),
      },
      oModel,
      NameUsage.entity
    );
    if (bUnique && error.error === undefined && error.isEmpty === undefined) {
      input["lastValue"] = input["_lastValue"];
      this.getView().getModel("galileiModel").setProperty("/displayName", sNewValue);
      this.currentObject.name = sNewValue;
      this.currentObject.technicalNameValueState = "None";
    } else if (!bUnique) {
      this.currentObject.displayName = sNewValue;
      this.currentObject.technicalNameValueState = "Error";
      input.setValueStateText(i18nResourceBundle.getText("technicalNameValidation"));
    }
  }

  public onTechnicalNameSubmit(oEvent) {
    const i18nResourceBundle = this.getView().getModel("i18n").getResourceBundle();
    const object = this.getView().getModel("galileiModel").getData();
    const oModel = this.currentObject.resource?.model;
    const input = this.getView().byId("technicalName") as sap.m.Input;
    const nameValidator = NamingHelper.getNameInputValidator();
    nameValidator.onTechnicalNameSubmit(
      object,
      input,
      "displayName",
      {
        duplicatedTechnicalName: i18nResourceBundle.getText("technicalNameValidation"),
        emptyTechnicalName: i18nResourceBundle.getText("technicalNameValidation"),
      },
      oModel,
      undefined,
      NameUsage.entity
    );
    object.validate();
  }

  // Helper function to check if the display name is unique
  public isDisplayNameUnique(aNodes, sNewName) {
    // Iterate over each node in the model
    let unique = true;
    aNodes.forEach((node) => {
      // Check if the display name matches the new name
      if (node.displayName === sNewName && sNewName !== this.currentObject.name) {
        unique = false;
        return unique;
      }
    });

    return unique;
  }

  public connectionErrorBtnVisiblity(connList?) {
    const connectionSelect = this.getView().byId("connectionList") as sap.m.Select;

    const btn = this.getView().byId("connectionErrorBtn") as sap.m.Button;
    if (this.currentObject?.resource?.model?.isConnectionHasError) {
      btn.setVisible(true);
      connectionSelect.setValueState(sap.ui.core.ValueState.Error);
      return true;
    }

    return false;
  }

  onConnectionErrorPress() {
    const i18nTC = new sap.ui.model.resource.ResourceModel({
      bundleName: require("../../taskchainmodeler/i18n/i18n.properties"),
    });
    this.getView().setModel(i18nTC, "i18nTC");
    if (this.currentObject.resource.model.isConnectionHasError) {
      const oBundle = (this.getView().getModel("i18nTC") as sap.ui.model.resource.ResourceModel).getResourceBundle();
      MessageHandler.exception({
        exception: this.currentObject.resource.model.connectionError?.[0],
        message: oBundle.getText(this.currentObject.resource.model.connectionError?.[0]?.responseJSON?.code),
        id: "connectionErrorMsgbox",
      });
    }
  }

  public onAddFieldPress() {
    // Get the container where panels will be added

    const oRestTask = this.getView().getModel("galileiModel").getData();
    const objRequestHeader = sap.cdw.taskchainmodeler.ModelImpl.createObject(
      "sap.cdw.taskchainmodeler.RequestHeader",
      {},
      oRestTask
    );
    this.createHeaderPanel(oRestTask, objRequestHeader);
  }

  public createHeaderPanels(oRestTask) {
    const self = this;
    oRestTask.requestHeaders.forEach((objHeader: any) => {
      self.createHeaderPanel(oRestTask, objHeader);
    });
  }

  public removeHeaderPanels() {
    const oContainer = this.byId("fieldsContainer");
    oContainer.removeAllItems();
  }

  public createHeaderPanel(oRestTask, objRequestHeader) {
    const self = this;
    self.currentObject.resource.applyUndoableAction(
      function () {
        self.panelCounter++;
        const index = oRestTask.requestHeaders.indexOf(objRequestHeader);
        const i18nResourceBundle = self.getView().getModel("i18n").getResourceBundle();
        const oContainer = self.byId("fieldsContainer");

        // Generate a unique ID for the new fields
        const sUniqueKeyId = self.createId("keyInput" + self.panelCounter);
        const sUniqueValueId = self.createId("valueInput" + self.panelCounter);

        // Create a new Panel for each key-value field pair
        const oPanel = new sap.m.Panel({
          headerText: i18nResourceBundle.getText("@lblAdditionalHeader") + " " + self.panelCounter,
          expanded: true,
          expandable: true,
        }).addStyleClass("panelBgColor sapUiTinyMarginTop");

        // overflow toolbar for panel deletion
        const oOverflowToolbar = new sap.m.OverflowToolbar();
        const oToolbarSpacer = new sap.m.ToolbarSpacer();
        const oPanelTitle = new sap.m.Title({
          text: i18nResourceBundle.getText("@lblAdditionalHeader") + " " + self.panelCounter,
        }).addStyleClass("panelHeaderText");
        const oDeleteButton = new sap.m.Button({
          id: "deleteButton" + self.panelCounter,
          icon: "sap-icon://delete",
          press: function () {
            self.onDeleteFieldPress(oPanel, objRequestHeader);
          },
        });

        // Create a vertical layout for organizing the fields inside the panel
        const oLayout = new sap.m.VBox();

        // Create the Key label and input field
        const oKeyLabel = new sap.m.Label({
          text: i18nResourceBundle.getText("@lblKey") + ":",
          labelFor: sUniqueKeyId,
        });
        const oKeyInput = new sap.m.Input({
          placeholder: i18nResourceBundle.getText("keyPlaceholder"),
          value: "{galileiModel>key}",
          id: sUniqueKeyId,
          required: true,
          valueState:
            !objRequestHeader.key || objRequestHeader.key === ""
              ? sap.ui.core.ValueState.Error
              : sap.ui.core.ValueState.None,
          valueStateText: i18nResourceBundle.getText("KeyRequired"),
          liveChange: self.keyValueValidation.bind(self),
          enabled: self.getView().getModel("workbenchEnv").getProperty("/isVersioningReadOnlyMode") ? false : true,
        });
        oKeyInput.bindElement("galileiModel>/requestHeaders/" + index);

        // Create the Value label and input field
        const oValueLabel = new sap.m.Label({
          text: i18nResourceBundle.getText("@lblValue") + ":",
          labelFor: sUniqueValueId,
        }).addStyleClass("sapUiTinyMarginTop");
        const oValueInput = new sap.m.Input({
          placeholder: i18nResourceBundle.getText("indicatorValue"),
          id: sUniqueValueId,
          value: "{galileiModel>value}",
          required: true,
          valueState:
            !objRequestHeader.value || objRequestHeader.value === ""
              ? sap.ui.core.ValueState.Error
              : sap.ui.core.ValueState.None,
          valueStateText: i18nResourceBundle.getText("valueRequired"),
          liveChange: self.keyValueValidation.bind(self),
          enabled: self.getView().getModel("workbenchEnv").getProperty("/isVersioningReadOnlyMode") ? false : true,
        });
        oValueInput.bindElement("galileiModel>/requestHeaders/" + index);

        // Add the fields to the layout
        oLayout.addItem(oKeyLabel);
        oLayout.addItem(oKeyInput);
        oLayout.addItem(oValueLabel);
        oLayout.addItem(oValueInput);

        // Add the layout to the panel
        oOverflowToolbar.addContent(oPanelTitle);
        oOverflowToolbar.addContent(oToolbarSpacer);
        oOverflowToolbar.addContent(oDeleteButton);
        oPanel.setHeaderToolbar(oOverflowToolbar);
        oPanel.addContent(oLayout);

        // Add the panel to the container
        oContainer.addItem(oPanel);
        self.renumberPanels();
      },
      undefined,
      true
    );
  }

  public onDeleteFieldPress(oPanel, objRequestHeader) {
    const oContainer = this.byId("fieldsContainer");

    const index = objRequestHeader.container.requestHeaders.indexOf(objRequestHeader);
    if (index >= 0) {
      objRequestHeader.container.requestHeaders.removeAt(index);
      objRequestHeader.deleteObject();
    }
    // Remove the panel from the container
    oContainer.removeItem(oPanel);

    // Renumber the remaining panels
    this.renumberPanels();
  }

  private renumberPanels() {
    const self = this;
    self.currentObject.resource.applyUndoableAction(
      function () {
        const oContainer = self.byId("fieldsContainer");
        const i18nResourceBundle = self.getView().getModel("i18n").getResourceBundle();
        let panelIndex = 1;

        // Iterate over each panel in the container and update the header text
        oContainer.getItems().forEach((oPanel) => {
          // Update the header text with the new panel index
          const oHeader = oPanel.getHeaderToolbar();
          const oTitle = oHeader.getContent()[0]; // The Title control
          oTitle.setText(i18nResourceBundle.getText("@lblAdditionalHeader") + " " + panelIndex);
          let itemIndex = panelIndex - 1;
          oContainer
            .getItems()
            [itemIndex].getContent()[0]
            .getItems()[1]
            .bindElement("galileiModel>/requestHeaders/" + itemIndex);
          oContainer
            .getItems()
            [itemIndex].getContent()[0]
            .getItems()[3]
            .bindElement("galileiModel>/requestHeaders/" + itemIndex);
          panelIndex++;
        });
      },
      undefined,
      true
    );
  }

  private keyValueValidation(oEvent) {
    const oSource = oEvent.getSource(); // Get the input field that triggered the event
    const sId = oSource.getId(); // Get the ID of the field (either Key or Value)
    const i18nResourceBundle = this.getView().getModel("i18n").getResourceBundle();

    if (sId.includes("keyInput")) {
      // Validate Key input field
      const sKeyValue = oSource.getValue();
      const keyPattern = /^(prefer|x-(?!forwarded-host)[a-zA-Z0-9_-]+)$/;

      if (!keyPattern.test(sKeyValue)) {
        oSource.setValueState(sap.ui.core.ValueState.Error);
        const text = new sap.m.FormattedText({ htmlText: i18nResourceBundle.getText("invalidKeyFormat") });
        oSource.setFormattedValueStateText(text);
      }
      // else if (sKeyValue) {
      //   oSource.setValueState(sap.ui.core.ValueState.Error);
      //   const text = new sap.m.FormattedText({ htmlText: i18nResourceBundle.getText("duplicateKey") });
      //   oSource.setFormattedValueStateText(text);
      // }
      else {
        oSource.setValueState(sap.ui.core.ValueState.None);
      }
    } else if (sId.includes("valueInput")) {
      // Validate Value input field
      const sValueValue = oSource.getValue();
      const valuePattern = /^[a-zA-Z0-9\s;:-_,?/*\.]+$/;

      if (!valuePattern.test(sValueValue)) {
        oSource.setValueState(sap.ui.core.ValueState.Error);
        oSource.setValueStateText(i18nResourceBundle.getText("invalidValueCharacters"));
      } else {
        oSource.setValueState(sap.ui.core.ValueState.None);
      }
    }
  }

  public urlFormatter() {
    const connectionList = this.getView().getModel().getData().connectionList;
    const connectionSelect = this.getView().byId("connectionList") as sap.m.Select;
    const selectedItem = connectionSelect.getSelectedItem();
    const i18nResourceBundle = this.getView().getModel("i18n").getResourceBundle();

    if (selectedItem === null) {
      return "";
    }

    const baseURl = this.getView().byId("url") as sap.m.Input;
    const statusUrl = this.getView().byId("statusUrl") as sap.m.Input;
    const selectedIndex = connectionSelect.indexOfItem(selectedItem);

    const selectedValue = connectionList[selectedIndex].url;
    const selectedConnectionName = connectionList[selectedIndex].technicalName;
    this.getView().getModel("galileiModel").setProperty("/connectionName", selectedConnectionName);
    this.getView().getModel("galileiModel").setProperty("/selectedConnectionURL", selectedValue);
    baseURl.setValue(connectionList[selectedIndex].url);
    statusUrl.setValue(connectionList[selectedIndex].url);
    if (
      connectionList?.[selectedIndex]?.ccmReplicationStatus !== undefined &&
      connectionList?.[selectedIndex]?.ccmReplicationStatus !== "SUCCESSFULLY_REPLICATED"
    ) {
      connectionSelect.setValueState(sap.ui.core.ValueState.Warning);
      connectionSelect.setValueStateText(i18nResourceBundle.getText("connectionNotReplicated"));
    } else if (selectedIndex === 0 && selectedConnectionName === "EMPTY_CONNECTION") {
      connectionSelect.setValueState(sap.ui.core.ValueState.Error);
      connectionSelect.setValueStateText(i18nResourceBundle.getText("selectConnection"));
    } else {
      connectionSelect.setValueState(sap.ui.core.ValueState.None);
    }
  }

  public onApiPathChange(oEvent) {
    const oInput = oEvent.getSource();
    const sValue = oInput.getValue();
    const i18nResourceBundle = this.getView().getModel("i18n").getResourceBundle();

    // Define the regular expression for API Path validation
    const oRegex = /^(?!.*\.\.)(?!.*\.$)(?!.*\.\.$)(?!.*[\{\}])(?!$)[^\s]*$/;

    // Validate the input value
    if (oRegex.test(sValue)) {
      // If valid, set the input as "None"
      oInput.setValueState(sap.ui.core.ValueState.None);
    } else {
      // If invalid, set the input to "Error" state with a custom message
      oInput.setValueState(sap.ui.core.ValueState.Error);
      oInput.setValueStateText(i18nResourceBundle.getText("apiPathValidation"));
    }

    /**
     * The properties isInternal and isVolatile were added to galileiModel to prevent errors during undo and redo operations.
     * However, these properties caused the onCascadeChange function to stop working.
     * Therefore, we explicitly call the validate method here to ensure proper functionality.
     * */
    const object = this.getView().getModel("galileiModel") && this.getView().getModel("galileiModel").getData();
    object.validate();
  }

  public onModeInfoPress(oEvent) {
    // implement popover with information if select Synchronous mode then there we don't need status API details
    const i18nResourceBundle = this.getView().getModel("i18n").getResourceBundle();
    const popover = new sap.m.Popover({
      content: new sap.m.Text({
        text: i18nResourceBundle.getText("modeInfoText"),
      }).addStyleClass("sapUiSmallMarginBegin sapUiSmallMarginEnd"),
      placement: sap.m.PlacementType.Bottom,
      resizable: true,
      showHeader: false,
    });

    if (popover) {
      popover.openBy(oEvent.getSource(), true);
    }
  }

  public onCSRFTokenInfoPress(oEvent) {
    const i18nResourceBundle = this.getView().getModel("i18n").getResourceBundle();
    const popover = new sap.m.Popover({
      content: new sap.m.Text({
        text: i18nResourceBundle.getText("@csrfTokenInfoText"),
      }).addStyleClass("sapUiSmallMarginBegin sapUiSmallMarginEnd"),
      placement: sap.m.PlacementType.Top,
      resizable: true,
      showHeader: false,
    });

    if (popover) {
      popover.openBy(oEvent.getSource(), true);
    }
  }

  public onJSONPathChange(oEvent) {
    const oInput = oEvent.getSource();
    const sValue = oInput.getValue();
    const i18nResourceBundle = this.getView().getModel("i18n").getResourceBundle();

    // Define the regular expression for JSON Path validation
    const oRegex = /^(?!\s)(?!$)(?!^\$)(?!.*\[\].*$)(.*|\[[^\[\]]+\])*$/;

    // Validate the input value
    if (oRegex.test(sValue)) {
      // If valid, set the input as "None"
      oInput.setValueState(sap.ui.core.ValueState.None);
    } else {
      // If invalid, set the input to "Error" state with a custom message
      oInput.setValueState(sap.ui.core.ValueState.Error);
      oInput.setValueStateText(i18nResourceBundle.getText("jsonPathValidation"));
    }
    /**
     * The properties isInternal and isVolatile were added to galileiModel to prevent errors during undo and redo operations.
     * However, these properties caused the onCascadeChange function to stop working.
     * Therefore, we explicitly call the validate method here to ensure proper functionality.
     * */
    const object = this.getView().getModel("galileiModel") && this.getView().getModel("galileiModel").getData();
    object.validate();
  }

  public onIndicatorValueChange(oEvent) {
    const oSource = oEvent.getSource(); // Get the input field that triggered the event
    const i18nResourceBundle = this.getView().getModel("i18n").getResourceBundle();
    const sValueValue = oSource.getValue();
    const valuePattern = /^[a-zA-Z0-9][a-zA-Z0-9_-]*$/;

    if (!valuePattern.test(sValueValue)) {
      oSource.setValueState(sap.ui.core.ValueState.Error);
      oSource.setValueStateText(i18nResourceBundle.getText("indicatorValueValidation"));
    } else {
      oSource.setValueState(sap.ui.core.ValueState.None);
    }
    /**
     * The properties isInternal and isVolatile were added to galileiModel to prevent errors during undo and redo operations.
     * However, these properties caused the onCascadeChange function to stop working.
     * Therefore, we explicitly call the validate method here to ensure proper functionality.
     * */
    const object = this.getView().getModel("galileiModel") && this.getView().getModel("galileiModel").getData();
    object.validate();
  }

  public onCSRFTokenChange(oEvent) {
    const tokenState = oEvent.getParameters().state;
    const csrfToken = this.getView().byId("CSRFTokenVBox") as sap.m.VBox;
    const csrfTokenUrl = this.getView().byId("CSRFtokenURL") as sap.m.Input;

    if (tokenState === true) {
      csrfToken.setVisible(true);
    } else {
      csrfToken.setVisible(false);
      csrfTokenUrl.setValue("");
    }
  }

  public onCSRFTokenValidation(oEvent) {
    const oInput = oEvent.getSource();
    const sValue = oInput.getValue();
    const i18nResourceBundle = this.getView().getModel("i18n").getResourceBundle();

    // Regular expression to validate a full HTTPS URL
    const regex =
      /^https:\/\/(?:[A-Z0-9](?:[A-Z0-9-]*[A-Z0-9])?\.)+(?:[A-Z]{2,6}\.?|[A-Z0-9-]{2,}\.)(?:\/[^\s]*)?(?:[#?][^\s]*)?$/i;

    // Validate if the URL starts with "https" and is a complete valid URL
    const bIsValid = regex.test(sValue);

    if (!bIsValid) {
      oInput.setValueState("Error");
      oInput.setValueStateText(i18nResourceBundle.getText("validationCSRFTokenURL"));
    } else {
      oInput.setValueState("None");
    }
    /**
     * The properties isInternal and isVolatile were added to galileiModel to prevent errors during undo and redo operations.
     * However, these properties caused the onCascadeChange function to stop working.
     * Therefore, we explicitly call the validate method here to ensure proper functionality.
     * */
    const object = this.getView().getModel("galileiModel") && this.getView().getModel("galileiModel").getData();
    object.validate();
  }

  public onFormat(oEvent) {
    const eventId = oEvent.getParameters().id;
    const btnId = eventId.trim().split("--").slice(-1)[0];
    if (btnId === "btnFormat" || btnId === "requestBody") {
      const editor = this.getView().byId("requestBody") as sap.ui.codeeditor.CodeEditor;
      editor.prettyPrint();
    } else if (btnId === "statusBtnFormat" || btnId === "statusRequestBody") {
      const editor = this.getView().byId("statusRequestBody") as sap.ui.codeeditor.CodeEditor;
      editor.prettyPrint();
    }
  }

  public onRequestBodyChange(oEvent) {
    const editorId = oEvent.getSource().getId();
    let editor;
    if (editorId.includes("requestBody")) {
      editor = this.getView().byId("requestBody") as sap.ui.codeeditor.CodeEditor;
    } else if (editorId.includes("statusRequestBody")) {
      editor = this.getView().byId("statusRequestBody") as sap.ui.codeeditor.CodeEditor;
    }

    try {
      JSON.parse(editor.getValue());
      if (editorId.includes("requestBody")) {
        this.getView().getModel("galileiModel").setProperty("/invokeAPI/request/bodyValueState", "None");
      } else if (editorId.includes("statusRequestBody")) {
        this.getView().getModel("galileiModel").setProperty("/statusAPI/request/bodyValueState", "None");
      }
    } catch (e) {
      if (editorId.includes("requestBody")) {
        this.getView().getModel("galileiModel").setProperty("/invokeAPI/request/bodyValueState", "Error");
      } else if (editorId.includes("statusRequestBody")) {
        this.getView().getModel("galileiModel").setProperty("/statusAPI/request/bodyValueState", "Error");
      }
    }
    /**
     * The properties isInternal and isVolatile were added to galileiModel to prevent errors during undo and redo operations.
     * However, these properties caused the onCascadeChange function to stop working.
     * Therefore, we explicitly call the validate method here to ensure proper functionality.
     * */
    const object = this.getView().getModel("galileiModel") && this.getView().getModel("galileiModel").getData();
    object.validate();
  }

  public requestBodyVisibilty() {
    const methodType = (this.getView().byId("statusMethod") as sap.m.RadioButtonGroup).getSelectedButton().getText();
    if (methodType === "POST") {
      return true;
    }
    return false;
  }

  public onEditorDialogPress(oEvent) {
    const eventId = oEvent.getParameters().id;
    const btnId = eventId.trim().split("--").slice(-1)[0];
    let editor, editorId;
    if (btnId === "openEditorDialog") {
      editorId = "requestBody";
    } else if (btnId === "openStatusEditorDialog") {
      editorId = "statusRequestBody";
    }
    editor = this.getView().byId(editorId) as sap.ui.codeeditor.CodeEditor;

    const aValue = editor.getCurrentValue();
    this["_currentEditor"] = editor;
    if (!this["oDefaultDialog"]) {
      const i18nResourceBundle = this.getView().getModel("i18n").getResourceBundle();
      const HBox = new sap.m.HBox({
        alignItems: sap.m.FlexAlignItems.Center,
        justifyContent: sap.m.FlexJustifyContent.SpaceBetween,
      }).addStyleClass("sapUiSmallMarginBegin sapUiTinyMarginEnd");
      const headerText = new sap.m.Label({ text: i18nResourceBundle.getText("@lblRequestBody") + ":" }).addStyleClass(
        "dialogLabel"
      );
      const oButtonFormat = new sap.m.Button({
        text: i18nResourceBundle.getText("@lblFormat"),
        id: "dialogBtnFormat",
        type: sap.m.ButtonType.Transparent,
        press: function () {
          bodyEditor.prettyPrint();
          bodyEditor.focus();
        },
      }).addStyleClass("dialogButton");
      // const oldValue = bodyEditor.getCurrentValue();
      const CodeEditor = ((sap.ui as any).codeeditor as any).CodeEditor;
      const bodyEditor = new CodeEditor({
        type: "json",
        id: editorId + "Dialog",
        colorTheme: "textmate",
        width: "700px",
        height: "400px",
        lineNumbers: true,
        editable: this.getView().getModel("workbenchEnv").getProperty("/isVersioningReadOnlyMode") ? false : true,
      }).addStyleClass("ExpressionBuilderCodeEditor");
      bodyEditor.setValue(aValue);
      HBox.addItem(headerText);
      HBox.addItem(oButtonFormat);

      this["oDefaultDialog"] = new sap.m.Dialog({
        title: i18nResourceBundle.getText("@lblEditJSON"),
        contentWidth: "760px",
        contentHeight: "450px",
        resizable: true,
        content: [
          new sap.m.VBox({
            justifyContent: sap.m.FlexJustifyContent.SpaceBetween,
            items: [
              HBox,
              new sap.m.VBox({
                alignItems: sap.m.FlexAlignItems.Center,
                justifyContent: sap.m.FlexJustifyContent.SpaceBetween,
                items: bodyEditor,
              }),
            ],
          }),
        ],
        beginButton: new sap.m.Button({
          id: "dialogBtnOK",
          type: sap.m.ButtonType.Emphasized,
          text: i18nResourceBundle.getText("btnOk"),
          press: function () {
            this._currentEditor.setValue(bodyEditor.getCurrentValue());
            this.oDefaultDialog.close();
          }.bind(this),
        }),
        endButton: new sap.m.Button({
          id: "dialogBtnCancel",
          text: i18nResourceBundle.getText("btnCancel"),
          press: function () {
            this.oDefaultDialog.close();
          }.bind(this),
        }),
      });

      this["oDefaultDialog"].attachAfterOpen(() => {
        bodyEditor.focus();
      });
    } else {
      const dialogBody = this["oDefaultDialog"]
        .getContent()[0]
        .getItems()[1]
        .getItems()[0] as sap.ui.codeeditor.CodeEditor;
      dialogBody.setValue(aValue);
    }
    // Ensure the dialog is opened
    if (this["oDefaultDialog"] && this["oDefaultDialog"].isOpen() === false) {
      this["oDefaultDialog"].open();
    }
  }

  public validateJson(error, editorId, aceEditor) {
    const errorIcon = this.getView().byId(editorId + "Error") as sap.ui.core.Icon;
    if (error.length > 0) {
      errorIcon.setVisible(true);
      const rowNumber = error[0].row + 1;
      const errorMessage = error[0].text + " in line " + rowNumber + " column " + error[0].column;
      errorIcon.setTooltip(errorMessage);

      // Create a popover dynamically for each editor
      if (!this["_popovers"]) {
        this["_popovers"] = {};
      }

      if (!this["_popovers"][editorId]) {
        const popoverContent = new sap.m.Text({
          text: errorMessage,
          textAlign: sap.ui.core.TextAlign.Center,
          wrapping: true,
          width: "200px",
        });

        this["_popovers"][editorId] = new sap.m.Popover({
          content: popoverContent,
          placement: sap.m.PlacementType.Auto,
          resizable: true,
          showHeader: false,
        });

        this.getView().addDependent(this["_popovers"][editorId]);
      } else {
        this["_popovers"][editorId].getContent()[0].setText(errorMessage);
      }
    } else if (aceEditor.isFocused()) {
      errorIcon.setVisible(false);
      if (this["_popovers"] && this["_popovers"][editorId]) {
        this["_popovers"][editorId].close();
      }
    }
  }

  // Error icon press handler (shows more info in the popover)
  public onErrorIconPress(oEvent) {
    const errorIcon = oEvent.getSource();
    const editorId = errorIcon.getId();
    const lastIndex = editorId.lastIndexOf("--");
    const simpleId = editorId.substring(lastIndex + 2).replace("Error", "");

    if (this["_popovers"] && this["_popovers"][simpleId]) {
      this["_popovers"][simpleId].openBy(errorIcon);
    }
  }

  public onStatusApiPathChange(oEvent) {
    const oInput = oEvent.getSource();
    const sValue = oInput.getValue();
    const i18nResourceBundle = this.getView().getModel("i18n").getResourceBundle();

    // Regular expression to validate that the API path contains at least one placeholder
    const regex = /^(?!.*\.\.)(?!.*\.$)(?!.*\.\.$)(?!.*\{\})(?!$)[^\s]*(\{.+\}){1}[^\s]*$/;

    // Validate if the API path contains at least one placeholder in the correct format
    const bIsValid = regex.test(sValue);

    if (!bIsValid) {
      oInput.setValueState("Error");
      oInput.setValueStateText(i18nResourceBundle.getText("validationStatusAPIPath"));
    } else {
      oInput.setValueState("None");
    }
    /**
     * The properties isInternal and isVolatile were added to galileiModel to prevent errors during undo and redo operations.
     * However, these properties caused the onCascadeChange function to stop working.
     * Therefore, we explicitly call the validate method here to ensure proper functionality.
     * */
    const object = this.getView().getModel("galileiModel") && this.getView().getModel("galileiModel").getData();
    object.validate();
  }

  public onRadioButtonChange() {
    const selectedResponse = this.getView().byId("responseType")["getSelectedIndex"]();
    const mode = this.getView().byId("mode") as sap.m.RadioButtonGroup;
    const selectedMode = mode.getSelectedIndex();
    const statusMethod = this.getView().byId("statusMethod")["getSelectedIndex"]();
    const method = this.getView().byId("method")["getSelectedIndex"]();

    let from = fromType.body;
    switch (selectedMode) {
      case 0:
        this.currentObject.invokeAPI.request.mode = "SYNC";
        if (selectedResponse === 0) {
          from = fromType.code;
        }
        break;
      case 1:
        this.currentObject.invokeAPI.request.mode = "ASYNC";
        if (selectedResponse === 0) {
          from = fromType.codeandlocation;
        }
        break;
    }
    this.currentObject.invokeAPI.response.from = from;
    this.currentObject.statusAPI.request.method = statusMethod === 0 ? "GET" : "POST";
    this.currentObject.invokeAPI.request.method = method === 0 ? "POST" : "PUT";

    this.currentObject.statusAPI.response.from =
      this.getView().byId("statusResponseType")["getSelectedIndex"]() === 1 ? fromType.body : fromType.code;

    this.visibilityFormatter(
      this.currentObject.invokeAPI.request.mode,
      this.currentObject.invokeAPI.response.from,
      this.currentObject.statusAPI?.request?.method
    );
  }

  public visibilityFormatter(invokeMode, invokeResponse, statusMethod) {
    const self = this;
    self.currentObject.resource.applyUndoableAction(
      function () {
        const i18nResourceBundle = self.getView().getModel("i18n").getResourceBundle();
        const invokeResponseRBtn = self.getView().byId("locationHeader") as sap.m.RadioButton;
        switch (invokeMode) {
          case "SYNC":
            self.getView().byId("statusPanel")["setVisible"](false);
            invokeResponseRBtn.setText(i18nResourceBundle.getText("statusCode"));
            if (invokeResponse === "BODY") {
              self.indicatorValueState("invokeAPI", true);
              self.getView().getModel("galileiModel").setProperty("/invokeAPI/response/jobIdPath", "");
            } else {
              self.indicatorValueState("invokeAPI", false);
            }
            break;
          case "ASYNC":
            invokeResponseRBtn.setText(i18nResourceBundle.getText("locationHeader"));
            self.getView().byId("statusPanel")["setVisible"](true);
            if (invokeResponse === "BODY") {
              self
                .getView()
                .getModel("galileiModel")
                .setProperty(
                  "/invokeAPI/response/jobIDPathVS",
                  /^(?!\s)(?!$)(?!^\$)(?!.*\[\]$)(.*|\[[^\[\]]+\])*$/.test(
                    self.currentObject.invokeAPI.response.jobIdJsonPath
                  )
                    ? "None"
                    : "Error"
                );
              self.indicatorValueState("invokeAPI", false);
              self.statusPanelQuartetFormatter(true);
            } else {
              self.statusPanelQuartetFormatter(false);
            }
            if (self.currentObject.statusAPI.response.from === "BODY") {
              self.indicatorValueState("statusAPI", true);
            } else {
              self.getView().byId("statusIndicator")["setVisible"](false);
              self.indicatorValueState("statusAPI", false);
            }
            break;
        }
      },
      undefined,
      true
    );
    /**
     * The properties isInternal and isVolatile were added to galileiModel to prevent errors during undo and redo operations.
     * However, these properties caused the onCascadeChange function to stop working.
     * Therefore, we explicitly call the validate method here to ensure proper functionality.
     * */
    const object = this.getView().getModel("galileiModel") && this.getView().getModel("galileiModel").getData();
    object.validate();
  }

  public indicatorValueState(panel: string, flag: boolean) {
    const self = this;
    const pathRegex = /^(?!\s)(?!$)(?!^\$)(?!.*\[\].*$)(.*|\[[^\[\]]+\])*$/;
    const valueRegex = /^[a-zA-Z0-9][a-zA-Z0-9_-]*$/;
    self.currentObject.resource.applyUndoableAction(
      function () {
        if (!flag) {
          self
            .getView()
            .getModel("galileiModel")
            .setProperty("/" + panel + "/response/successIndicatorPath", "");
          self
            .getView()
            .getModel("galileiModel")
            .setProperty("/" + panel + "/response/successIndicatorValue", "");
          self
            .getView()
            .getModel("galileiModel")
            .setProperty("/" + panel + "/response/errorIndicatorPath", "");
          self
            .getView()
            .getModel("galileiModel")
            .setProperty("/" + panel + "/response/errorIndicatorValue", "");
          self
            .getView()
            .getModel("galileiModel")
            .setProperty("/" + panel + "/response/errorReasonPath", "");
        }

        self
          .getView()
          .getModel("galileiModel")
          .setProperty(
            "/" + panel + "/response/successIndicatorPathVS",
            pathRegex.test(self.currentObject[panel].response.successIndicatorPath) ? "None" : "Error"
          );
        self
          .getView()
          .getModel("galileiModel")
          .setProperty(
            "/" + panel + "/response/successIndicatorValueVS",
            valueRegex.test(self.currentObject[panel].response.successIndicatorValue) ? "None" : "Error"
          );
        self
          .getView()
          .getModel("galileiModel")
          .setProperty(
            "/" + panel + "/response/errorIndicatorPathVS",
            pathRegex.test(self.currentObject[panel].response.errorIndicatorPath) ? "None" : "Error"
          );
        self
          .getView()
          .getModel("galileiModel")
          .setProperty(
            "/" + panel + "/response/errorIndicatorValueVS",
            valueRegex.test(self.currentObject[panel].response.errorIndicatorValue) ? "None" : "Error"
          );
        if (self.currentObject[panel].response.errorReasonPath !== "") {
          self
            .getView()
            .getModel("galileiModel")
            .setProperty(
              "/" + panel + "/response/errorReasonPathVS",
              pathRegex.test(self.currentObject[panel].response.errorReasonPath) ? "None" : "Error"
            );
        }
        /**
         * The properties isInternal and isVolatile were added to galileiModel to prevent errors during undo and redo operations.
         * However, these properties caused the onCascadeChange function to stop working.
         * Therefore, we explicitly call the validate method here to ensure proper functionality.
         * */
        const object = self.getView().getModel("galileiModel") && self.getView().getModel("galileiModel").getData();
        object.validate();
      },
      undefined,
      true
    );
  }

  public statusPanelQuartetFormatter(condition: boolean) {
    const self = this;
    self.currentObject.resource.applyUndoableAction(
      function () {
        const statusApiPath = self.getView().byId("statusApiPath") as sap.m.Input;
        const statusApiPathLabel = self.getView().byId("statusApiPathLabel") as sap.m.Label;
        const statusBody = self.getView().byId("statusRequestBody") as sap.ui.codeeditor.CodeEditor;
        const statusUrl = self.getView().byId("statusUrl") as sap.m.Input;
        const statusMethod = self.getView().byId("statusMethod") as sap.m.RadioButtonGroup;
        const statusBodyErrorIcon = self.getView().byId("statusRequestBodyError") as sap.ui.core.Icon;

        statusUrl.setVisible(true);
        statusUrl.setRequired(condition);
        statusApiPathLabel.setVisible(true);
        statusApiPath.setVisible(true);
        statusApiPath.setRequired(condition);
        statusApiPath.setEnabled(
          condition && !self.getView().getModel("workbenchEnv").getProperty("/isVersioningReadOnlyMode")
        );
        statusBody.setEditable(
          condition && !self.getView().getModel("workbenchEnv").getProperty("/isVersioningReadOnlyMode")
        );
        statusMethod.setEnabled(
          condition && !self.getView().getModel("workbenchEnv").getProperty("/isVersioningReadOnlyMode")
        );

        if (condition) {
          if (statusApiPath.getValue() === "") {
            statusApiPath.setValueState(sap.ui.core.ValueState.Error);
          } else {
            const regex = /^(.*\{[a-zA-Z0-9-_]+\}.*)$/;

            // Validate if the API path contains at least one placeholder in the correct format
            const bIsValid = regex.test(statusApiPath.getValue());

            if (!bIsValid) {
              statusApiPath.setValueState(sap.ui.core.ValueState.Error);
            } else {
              statusApiPath.setValueState(sap.ui.core.ValueState.None);
            }
          }
          if (statusMethod.getEnabled() === true && statusMethod.getSelectedIndex() === 0) {
            statusBody.setValue("");
            statusBody.setEditable(false);
            self.getView().getModel("galileiModel").setProperty("/statusAPI/request/bodyValueState", "None");
            if (statusBodyErrorIcon.getVisible() === true) {
              statusBodyErrorIcon.setVisible(false);
            }
          }
        } else {
          statusUrl.setValue("");
          statusApiPath.setValue("");
          statusApiPath.setValueState(sap.ui.core.ValueState.None);
          statusBody.setValue("");
          self.getView().getModel("galileiModel").setProperty("/statusAPI/request/bodyValueState", "None");
          if (statusBodyErrorIcon.getVisible() === true) {
            statusBodyErrorIcon.setVisible(false);
          }
        }

        /**
         * The properties isInternal and isVolatile were added to galileiModel to prevent errors during undo and redo operations.
         * However, these properties caused the onCascadeChange function to stop working.
         * Therefore, we explicitly call the validate method here to ensure proper functionality.
         * */
        const object = self.getView().getModel("galileiModel") && self.getView().getModel("galileiModel").getData();
        object.validate();
      },
      undefined,
      true
    );
  }

  public async runApiTask(oEvent) {
    const oNode: any = this.currentObject;
    const nodeValAggregation = sap.cdw.commonmodel.Validation.getAggregatedValidation(oNode);
    const i18nResourceBundle = this.getView().getModel("i18n").getResourceBundle();

    if (this.currentObject?.executionStatus?.status !== undefined) {
      await this.refreshApiStatus();
    }

    if (!oNode.resource.model.isNew) {
      if (nodeValAggregation.status === sap.cdw.commonmodel.ValidationStatus.STATUS_ERROR) {
        this.displayValidationsDialog(false, nodeValAggregation, oNode, oEvent);
      } else if (this.currentObject.executionStatus.status === "RUNNING") {
        if (!this["oAPIDialog"]) {
          const text = i18nResourceBundle.getText("apiTaskRunning");
          this["oAPIDialog"] = new sap.m.Dialog({
            id: "apiTaskRunningDialog",
            title: i18nResourceBundle.getText("@executeConfirmDialog"),
            contentWidth: "auto",
            contentHeight: "auto",
            resizable: true,
            content: [
              new sap.m.Text({
                text: text,
              }).addStyleClass("sapUiTinyMarginBegin sapUiSmallMarginEnd"),
            ],
            beginButton: new sap.m.Button({
              id: "dialogBtnOK",
              type: sap.m.ButtonType.Emphasized,
              text: i18nResourceBundle.getText("btnOk"),
              press: function () {
                this.oAPIDialog.close();
                this.triggerApiRun(oNode);
              }.bind(this),
            }),

            endButton: new sap.m.Button({
              id: "dialogBtnCancel",
              text: i18nResourceBundle.getText("btnCancel"),
              press: function () {
                this.oAPIDialog.close();
              }.bind(this),
            }),
          });
        }
        if (this["oAPIDialog"]) {
          this["oAPIDialog"].open();
        }
      } else {
        this.triggerApiRun(oNode);
      }
    } else {
      const modelValAggregation = sap.cdw.commonmodel.Validation.getAggregatedValidation(
        oNode.resource.model,
        oNode.resource.model.nodes
      );

      if (
        (modelValAggregation.status === sap.cdw.commonmodel.ValidationStatus.STATUS_ERROR ||
          modelValAggregation.status === sap.cdw.commonmodel.ValidationStatus.STATUS_WARN) &&
        modelValAggregation.validations.length > 0
      ) {
        this.displayValidationsDialog(true, modelValAggregation, oNode, oEvent);
      } else {
        MessageHandler.uiError(
          i18nResourceBundle.getText("taskChainNotSaved"),
          null,
          null,
          null,
          "35%",
          null,
          null,
          "taskChainNotSavedMsgBox"
        );
      }
    }
  }

  public triggerApiRun(oNode) {
    const i18nResourceBundle = this.getView().getModel("i18n").getResourceBundle();
    const spaceName = this.getSpaceName();
    const runPayload = {
      objectId: oNode.resource.model.name + "." + oNode.displayName,
      activity: "TEST_RUN",
      applicationId: "API",
      spaceId: spaceName,
      volatileParameters: {},
    };
    this.showBusyDialog(
      i18nResourceBundle.getText("@titleExecuteBusy"),
      i18nResourceBundle.getText("@msgAPIExecuteBusy")
    );
    ModelToJSON.processRestApi(runPayload.volatileParameters, oNode);
    const url = `/tf/directexecute`;
    ServiceCall.post(url, { contentType: ContentType.APPLICATION_JSON }, true, JSON.stringify(runPayload)).then(
      () => {
        // update status in model property panel
        sap.ui.getCore().getEventBus().publish("propertyPanel", "updateStatus");
        const toastMessage = "@msgAPITestRunSuccess";
        // success executing
        sap.m.MessageToast.show(this.getText(toastMessage));
        if (this["oBusyDialog"]) {
          this["oBusyDialog"].close();
        }
      },
      (error) => {
        if (this["oBusyDialog"]) {
          this["oBusyDialog"].close();
        }
        const toastMessage = "@failedToRunAPI";
        const errorMsg = this.getText(toastMessage);
        MessageHandler.exception({ exception: error, message: errorMsg, id: "executeFailedErrorMsgbox" });
      }
    );
  }

  public refreshApiStatus() {
    this.fetchStatus();
  }

  private fetchStatus() {
    const self = this;
    self.currentObject.resource.applyUndoableAction(
      function () {
        const sSpaceId = self.getSpaceName();
        const oNode = self.currentObject;
        const sUrl =
          "tf/" + sSpaceId + "/logs?objectId=" + oNode.resource.model.name + "." + self.currentObject.displayName;

        if (!self.currentObject.resource.model.isNew) {
          const oStatusPanel: any = self.getView().byId("runTestPanel");
          oStatusPanel.setBusy(true);
          oStatusPanel.setBusyIndicatorDelay(0);
          ServiceCall.request<any>({
            url: sUrl,
            type: HttpMethod.GET,
            contentType: ContentType.APPLICATION_JSON,
          })
            .then(async (oResponse) => {
              self.updateStatus(oResponse);

              self.currentObject.executionErrorDetails = undefined;

              self.getView().getModel("galileiModel").refresh(true);
              oStatusPanel.setBusy(false);
            })
            .catch((oError) => {
              self.currentObject.executionErrorDetails = oError;
              self.getView().getModel("galileiModel").refresh(true);
              // eslint-disable-next-line no-console
              oStatusPanel.setBusy(false);
              console.log(oError);
            });
        }
      },
      undefined,
      true
    );
  }

  public onPressViewInRestTaskMonitor() {
    const oNode = this.currentObject;
    const spaceName = this.getSpaceName();
    const logId = oNode.executionStatus.logId;
    const objectId = oNode.resource.model.name + "." + oNode.displayName;
    const applicationId = oNode.executionStatus.applicationId;
    const shellHash = "dataintegration&/di/logdetails";
    const targetHash = `${encodeURIComponent(spaceName)}/${encodeURIComponent(applicationId)}/${encodeURIComponent(
      objectId
    )}/${encodeURIComponent(logId)}`;
    const sUrl = `#/${shellHash}/${targetHash}`;

    sap.m.URLHelper.redirect(sUrl, false);
  }

  public displayValidationsDialog(
    save: boolean,
    aggregationValidations: IEditorAggregatedValidations,
    oNode: any,
    oEvent
  ): Promise<ILocalizedValidationMessages> {
    const self = this;
    let controller: ValidationsControllerClass;
    this["tcResourceModel"] = new sap.ui.model.resource.ResourceModel({
      bundleName: require("../../taskchainmodeler/i18n/i18n.properties"),
    });
    return new Promise((resolve, reject) => {
      showDialog(
        require("../../abstractbuilder/view/validations.view.xml"),
        save ? self.getText("@validateModel") : oNode.displayName + " " + self.getText("@validateModel"),
        save ? self.getText("saveAnyway") : undefined,
        self.getText(
          save
            ? "btnCancel"
            : aggregationValidations?.status === sap.cdw.commonmodel.ValidationStatus.STATUS_ERROR
            ? "btnOk"
            : "btnCancel"
        ),
        /* viewData*/ undefined,
        /* contentWidth*/ "500px",
        /* contentHeight*/ "300px",
        /* i18nModel*/ undefined,
        {
          galileiModel: {
            aggregatedValidations: aggregationValidations,
          },
          i18nResources: {
            i18n_commonmodel: new sap.ui.model.resource.ResourceModel({
              bundleName: require("../../commonmodel/i18n/i18n.properties"),
            }),
            i18n_tc: this["tcResourceModel"],
          },
        },
        /* state*/ undefined,
        /* onAfterOpen*/ (dialog) => {
          const validationView = dialog.getContent()[0] as sap.ui.core.mvc.View;
          controller = validationView.getController() as ValidationsControllerClass;
          validationView.setModel(self.getView().getModel("i18n"), "i18n");
          const okButton = validationView?.byId("ok") as sap.m.Button;
          okButton?.setEnabled(true);
          if (okButton && aggregationValidations?.isBlocker) {
            okButton.setTooltip(this.getText("savePrevented"));
            okButton.setEnabled(false);
          }
        },
        undefined,
        undefined,
        /* onBeforeOpen*/ (dialog) => {
          const validationView = dialog?.getContent()?.[0] as sap.ui.core.mvc.View;
          const okButton = validationView?.byId("ok") as sap.m.Button;
          okButton?.setEnabled(false);
        }
      )
        .then(() => {
          const worbench = this.getDatabuilderWorkbench();
          if (worbench) {
            worbench.onSave(oEvent);
          }
          if (aggregationValidations?.status === sap.cdw.commonmodel.ValidationStatus.STATUS_ERROR) {
            const nodeValAggregation = sap.cdw.commonmodel.Validation.getAggregatedValidation(
              oNode.resource.model,
              oNode,
              true
            );
            if (nodeValAggregation.status === sap.cdw.commonmodel.ValidationStatus.STATUS_ERROR) {
              this.displayValidationsDialog(true, nodeValAggregation, oNode, oEvent);
            }
          }
          this.triggerApiRun(oNode);
          resolve(controller?.getLocalizedMessages());
          controller?.initMessages();
        })
        .catch((e) => {
          // Cancel save
          if (e) {
            // should never happen
            console.log("[ERROR] unexpected error", e);
          }
          reject("Canceled");
        });
    });
  }

  public showBusyDialog(title?: string, msg?: string) {
    if (!this["oBusyDialog"]) {
      this["oBusyDialog"] = new sap.m.BusyDialog({
        title: title,
        contentWidth: "760px",
        contentHeight: "450px",
        resizable: true,
        text: msg,
      });
      this["oBusyDialog"].open();
    } else {
      this["oBusyDialog"].open();
    }
  }
}

export const RESTTaskProperties = smartExtend(
  TaskChainCommonProperties,
  "sap.cdw.components.taskchainmodeler.properties.RESTTaskProperties",
  RESTTaskPropertiesClass
);

sap.ui.define("sap/cdw/components/taskchainmodeler/properties/RESTTaskproperties.controller", [], function () {
  return RESTTaskProperties;
});
