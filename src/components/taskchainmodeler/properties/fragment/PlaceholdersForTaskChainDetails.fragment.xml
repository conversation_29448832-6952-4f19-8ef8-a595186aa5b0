<c:FragmentDefinition
  xmlns:c="sap.ui.core"
  xmlns="sap.m"
>
  <Popover
    id="Popover"
    title="{i18n>@taskchainDetailsPlaceholder}"
    placement="PreferredTopOrFlip"
    class="sapUiContentPadding"
    contentWidth="600px"
    contentHeight="auto"
  >
    <content>
      <Table
        items="{emailNotificationModel>/taskChainDetailsPlaceholders}"
      >
        <columns>
          <Column>
            <Text text="{i18n>@placeholder}" />
          </Column>
          <Column>
            <Text text="{i18n>@description}" />
          </Column>
          <Column width="50px">
            <Text text="" />
          </Column>
        </columns>
        <items>
          <ColumnListItem>
            <cells>
              <Text text="{emailNotificationModel>placeholder}" />
              <Text text="{emailNotificationModel>description}" />
              <Button
                id="placeholderCopyBtn"
                icon="sap-icon://copy"
                tooltip="{i18n>@copyText}"
                press="copyPlaceholderText(${emailNotificationModel>})"
                type="Transparent"
                width="50px"
              />
            </cells>
          </ColumnListItem>
        </items>
      </Table>
    </content>
  </Popover>
</c:FragmentDefinition>
