<c:FragmentDefinition
    xmlns:c="sap.ui.core"
    xmlns="sap.m"
    xmlns:l="sap.ui.layout"
    xmlns:f="sap.ui.layout.form"
    xmlns:html="http://www.w3.org/1999/xhtml"
    xmlns:dol="sap.cdw.components.reuse.control.dwcobjectlink"
    xmlns:dos="sap.cdw.components.reuse.control.dwcobjectstatus"
>
  <Popover
      placement="HorizontalPreferredLeft"
      showHeader="false"
      >
      <l:VerticalLayout>
        <f:SimpleForm editable="false">
          <c:Title
            id="businessNameInfo"
            icon="{path:'/', formatter:'.headerIconFormatter'}"
            text="{= '  ' + ${/displayName}}"
            visible="{= ${/isTask} ? true : false }" />
          <!-- Business Name-->
          <Label text="{i18n>@lblBusinessName}"
             visible="{= ${/isTask} &amp;&amp; !(${/isBWProcessChain} || ${/isSQLScriptProcedure} || ${/isRestApi} || ${/isNotificationTask}) ? true : false }"/>
          <dol:DWCObjectLink
            id="businessNameLink"
            showSimpleTooltip="true"
            text="{= ${/label} === undefined ? ${/name} : ${/label}}"
            target="_blank"
            technicalName="{/name}"
            spaceName="{= ${/isCrossSpace} === true ? ${/crossSpaceName} : undefined }"
            visible="{= ${/isTask} &amp;&amp; !(${/isBWProcessChain} || ${/isSQLScriptProcedure} || ${/isRestApi} || ${/isNotificationTask}) ? true : false }"
          />
          <!-- technical Name-->
          <Label text="{i18n>@lblTechnicalName}" />
          <Text id="technicalName" class="nodeText" text="{/name}" />
          <!-- Type -->
          <Label text="{i18n_erd>@type}" />
          <Text id="type" class="nodeText" text="{path:'/', formatter:'.technicalTypeValueFormatter'}" />
          <!-- Status -->
          <Label text="{i18n_erd>@objectStatus}"
            visible="{= ${/isTask} &amp;&amp; !(${/isBWProcessChain} || ${/isSQLScriptProcedure} || ${/isRestApi} || ${/isNotificationTask}) ? true : false }"/>
          <HBox id="objectStatus"
           visible="{= ${/isTask} &amp;&amp; !(${/isBWProcessChain} || ${/isSQLScriptProcedure} || ${/isRestApi} || ${/isNotificationTask}) ? true : false }">
           <dos:DWCObjectStatus id="objectStatusText" class="sapUiTinyMarginEnd" statusType ="{/#objectStatus}" />
          </HBox>
          <!-- Data Access -->
          <Label text="{i18n_erd>activity}" visible="{= ${/isTask} ? true : false }"/>
          <Text
            id="dataAccess"
            class="nodeText"
            text="{path:'/', formatter:'.activityValueFormatter'}"
            visible="{= ${/isTask} ? true : false }"
          />
        </f:SimpleForm>
      </l:VerticalLayout>
	</Popover>
</c:FragmentDefinition>
