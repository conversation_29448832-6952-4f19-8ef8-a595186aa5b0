/**
 * Copyright 2020 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { State } from "@sap/dwc-circuit-breaker";
import { ShellNavigationService, ShellUIService } from "@sap/orca-shell";
import { getAllUsers } from "../../../services/metadata";
import { IWorkbenchController } from "../../abstractbuilder/api";
import { smartExtend } from "../../basecomponent/controller/BaseController.controller";
import { NameUsage } from "../../commonui/utility/NameInputValidator";
import * as commonUtils from "../../ermodeler/js/utility/CommonUtils";
import { listHeight, toggleLabel } from "../../ermodeler/js/utility/commonFormatter";
import {
  dateFormatter,
  objectStatusIconFormatter,
  objectStatusTextFormatter,
  objectStatusTooltipFormatter,
  revertObjectStatusIconFormatter,
  revertObjectStatusTextFormatter,
  revertObjectStatusVisible,
} from "../../ermodeler/js/utility/sharedFunctions";
import { ToolName } from "../../reuse/utility/Constants";
import { Format } from "../../reuse/utility/Format";
import { MessageHandler } from "../../reuse/utility/MessageHandler";
import { NamingHelper } from "../../reuse/utility/NamingHelper";
import { ContentType, DataType, HttpMethod, ServiceCall } from "../../reuse/utility/ServiceCall";
import { isCircuitBreakerYellowStateEnabled } from "../../shell/featureState/CircuitBreakerYellowStateFeatureState";
import { ShellContainer } from "../../shell/utility/Container";
import { Repo } from "../../shell/utility/Repo";
import { Activity, ApplicationId } from "../../tasklog/utility/Constants";
import { ITTaskScheduleController, ITaskScheduleRequest } from "../../taskscheduler/controller/TaskSchedule.controller";
import { getTaskScheduer, recordAction } from "../../taskscheduler/utility/ScheduleUtil";
import Utils, { NotificationCondition } from "../js/utils";
import { TaskChainCommonProperties, TaskChainCommonPropertiesClass } from "./TaskChainCommonProperties.controller";
const Filter = sap.ui.require("sap/ui/model/Filter");
const FilterOperator = sap.ui.require("sap/ui/model/FilterOperator");

export class ModelPropertiesClass extends TaskChainCommonPropertiesClass {
  private galileiModel;
  selectEmailRecipientDialog: sap.m.Dialog;
  taskChainDetailsPopover: sap.m.Popover;
  isReusableTaskScheduleFFEnabled: boolean;
  isAdoptionOfTaskSchedulerEnabled: boolean;

  public onInit(): void {
    super.onInit();
    this.isReusableTaskScheduleFFEnabled = sap.ui
      .getCore()
      .getModel("featureflags")
      .getProperty("/DWCO_REUSABLE_TASK_SCHEDULING_UI");
    this.isAdoptionOfTaskSchedulerEnabled = sap.ui
      .getCore()
      .getModel("featureflags")
      .getProperty("/DWCO_REUSABLE_TASK_SCHEDULER_ADOPTION");
    if (this.isAdoptionOfTaskSchedulerEnabled) {
      const fragmentName = require("../../taskscheduler/view/TaskScheduleAuth.view.xml");
    }
    sap.ui
      .getCore()
      .getEventBus()
      .subscribe("propertyPanel", "updateStatus", (channel, event, oResponse) => {
        if (oResponse && oResponse.data) {
          this.updateStatus(oResponse);
        } else if (!(this.currentObject?.applicationId === ApplicationId.API)) {
          setTimeout(() => {
            this.fetchStatus();
          }, 1000);
        }
      });
    sap.ui
      .getCore()
      .getEventBus()
      .subscribe("propertyPanel", "updateDeploymentStatus", () => {
        this.getView().getModel("galileiModel")?.refresh(true);
        this.setupScheduleActionsModel();
        this.updateNotificationSetting();
        this.refreshNotificationMailList();
      });

    sap.ui
      .getCore()
      .getEventBus()
      .subscribe("propertyPanel", "updateChainableObjects", () => {
        this.getView().getModel("galileiModel")?.refresh(true);
      });
    this.attachHanaStateChangeHandler();
  }

  public formatters = {
    objectStatusIconFormatter,
    objectStatusIconColorFormatter: Format.objectStatusIconColorFormatter.bind(this),
    objectStatusTextFormatter,
    objectStatusTooltipFormatter,
    dateFormatter,
    revertObjectStatusIconFormatter,
    revertObjectStatusTextFormatter,
    revertObjectStatusVisible,
    listHeight,
    toggleLabel,
    hanaStateFormatter: function (hanaState: State) {
      if (hanaState === State.Red) {
        return State.Red;
      }
      return State.Green;
    },
  };

  public setObjectModel(oObject): void {
    let modelObject = oObject;

    if (
      oObject &&
      oObject.classDefinition &&
      (oObject.classDefinition.name === "Task" ||
        oObject.classDefinition.name === "Link" ||
        oObject.classDefinition.name === "Operation" ||
        oObject.classDefinition.name === "Parameters")
    ) {
      modelObject = oObject.resource.model;
      this.setModelPageVisible(true);
    }
    super.setObjectModel(oObject);
    if (this.galileiModel !== modelObject) {
      this.galileiModel = modelObject;
      if (!this.galileiModel.isNew) {
        this.fetchStatus();
        this.getSchedule();
      }
    }
    const taskChainSearchField = this.byId("taskChainSearchField") as sap.m.SearchField;
    if (taskChainSearchField) {
      taskChainSearchField["clear"]();
    }
    // set data preview warning visibility
    this.setDataPreviewWarningVisibility();
    this.updateRepopackageInfoToGalileiModel();
    // set Schedule Model Actions
    if (oObject.classDefinition.name === "Model") {
      this.setupScheduleActionsModel();
      this.setEmailNotificationModel();
      this.updateNotificationSetting();
    }
    this.setupSpaceStatusModel();
  }

  private updateRepopackageInfoToGalileiModel() {
    const model = this.getView().getModel("galileiModel");
    const workbenchEnvModel = this.getView().getModel("workbenchEnv");
    const packages = workbenchEnvModel.getProperty("/packages");
    model.setProperty("/listOfPackages", packages);
  }

  public displayPackageSelector(packages) {
    // packages list has at least "none" element, so when the length equals one that means no other package can be selected
    // at this scenario we should hide this package selector
    if (packages?.length > 1) {
      return true;
    } else {
      return false;
    }
  }

  /**
   * update combobox status
   *
   * @param {boolean} hasPrivileges
   * @param {string} forceStatus
   * @returns {string}
   */
  public forceStatusPackageSelectorModel(hasPrivileges: boolean, forceStatus: string): string {
    if (!hasPrivileges) {
      return "disabled";
    }
    return forceStatus ?? "byOthers";
  }

  /**
   * Package selection change handler
   *
   * @public
   * @param {*} event
   */
  public packageSelectionChange(event) {
    const object = this.getView().getModel("galileiModel") && this.getView().getModel("galileiModel").getData();
    object.validate();
  }

  /**
   * handler for search
   * @param {sap.ui.base.Event} event
   */
  public onSearchChange(event: sap.ui.base.Event) {
    const table: sap.m.Table = this.byId("nodesTokenList") as sap.m.Table;
    const query: string = (event.getSource() as sap.m.SearchField).getValue();
    const oModel: any = this.getView().getModel("galileiModel");
    this.applyFilter(query, table, oModel);
  }

  /**
   * apply search filter
   * @param {any} query
   * @param {any} table
   * @param {any} oModel
   */
  public applyFilter(query: any, table: any, oModel: any) {
    const aFilters = [];
    if (query && query.length > 0) {
      const filter = new sap.ui.model.Filter("displayName", sap.ui.model.FilterOperator.Contains, query);
      aFilters.push(filter);
    }
    // update list binding
    const oBinding: sap.galilei.ui5.GalileiListBinding = table.getBinding(
      "items"
    ) as unknown as sap.galilei.ui5.GalileiListBinding;
    oBinding.filter(aFilters, "Application");
    this.updateSearchField();
  }

  public updateSearchField() {
    const table: sap.m.Table = this.byId("nodesTokenList") as sap.m.Table;
    const taskChainSearchField = this.byId("taskChainSearchField") as sap.m.SearchField;
    const query: string = taskChainSearchField.getValue();
    const oModel: any = this.getView().getModel("galileiModel");
    const tableCount = !(query && query.length > 0) ? oModel.getProperty("/nodes").length : table.getItems().length;
    const txtValue = (this.getView().getModel("i18n") as sap.ui.model.resource.ResourceModel)
      .getResourceBundle()
      .getText("txtNodes", [tableCount.toString()]);
    this.getView().byId("NodesPanelTitle")["setText"](txtValue);
    return txtValue;
  }

  public setEmailNotificationModel() {
    const i18nResourceBundle = this.getView().getModel("i18n").getResourceBundle();
    const taskChainDetailsPlaceholders = [
      {
        placeholder: "$$status$$",
        description: i18nResourceBundle.getText("@phStatus"),
      },
      {
        placeholder: "$$taskChainName$$",
        description: i18nResourceBundle.getText("@phTaskChainTName"),
      },
      {
        placeholder: "$$businessName$$",
        description: i18nResourceBundle.getText("@phTaskChainBName"),
      },
      {
        placeholder: "$$logId$$",
        description: i18nResourceBundle.getText("@phLogId"),
      },
      {
        placeholder: "$$user$$",
        description: i18nResourceBundle.getText("@phUser"),
      },
      {
        placeholder: "$$uiLink$$",
        description: i18nResourceBundle.getText("@phLogUILink"),
      },
      {
        placeholder: "$$startTime$$",
        description: i18nResourceBundle.getText("@phStartTime"),
      },
      {
        placeholder: "$$endTime$$",
        description: i18nResourceBundle.getText("@phEndTime"),
      },
      {
        placeholder: "$$spaceId$$",
        description: i18nResourceBundle.getText("@phSpaceName"),
      },
    ];
    const notificationSettings = [
      {
        id: NotificationCondition.OFF,
        label: i18nResourceBundle.getText("@nsOFF"),
      },
      {
        id: NotificationCondition.FAILED,
        label: i18nResourceBundle.getText("@nsFAILED"),
      },
      {
        id: NotificationCondition.COMPLETED,
        label: i18nResourceBundle.getText("@nsCOMPLETED"),
      },
      {
        id: NotificationCondition.ANY,
        label: i18nResourceBundle.getText("@nsANY"),
      },
    ];
    const emailPanel: any = this.getView().byId("taskchainEmailNotification");
    const oEmailNotificationModel = new sap.ui.model.json.JSONModel({
      selectedNotificationSetting: NotificationCondition.OFF,
      emailSubject: i18nResourceBundle.getText("@emailSubjectTemplateText"),
      emailMessage: i18nResourceBundle.getText("@emailMessageTemplateText"),
      notificationSettings,
      allUsers: [],
      selectedMembers: [],
      tenantMembers: [],
      nonTenantMembers: [{ id: "", name: "" }],
      taskChainDetailsPlaceholders,
      maxMembersAlreadyAdded: false,
      othersTabHeader: "",
      tenantMemberSearch: "",
      tenantOwnerDomain: "",
    });
    const placeholdersArr = oEmailNotificationModel.getProperty("/taskChainDetailsPlaceholders");
    const isSDPEnabled = sap.ui.getCore().getModel("featureflags").getProperty("/DWC_DUMMY_SPACE_PERMISSIONS");
    if (!isSDPEnabled) {
      const dataIntegrationPrivilege = ShellContainer.get()
        .getPrivilegeService()
        .getPrivilegesByType("DWC_DATAINTEGRATION");
      const hasEmailNotificationPrivilege =
        dataIntegrationPrivilege && dataIntegrationPrivilege.read === true && dataIntegrationPrivilege.update === true;
      oEmailNotificationModel.setProperty("/hasEmailNotificationPrivilege", hasEmailNotificationPrivilege);
    }

    const placeholderSet = new Set();
    placeholdersArr.forEach((placeHolderObj) => {
      placeholderSet.add(placeHolderObj.placeholder);
    });

    oEmailNotificationModel.setSizeLimit(1000000);
    oEmailNotificationModel.setProperty("/placeholderSet", placeholderSet);
    this.updateOthersTabHeader();
    this.getView().setModel(oEmailNotificationModel, "emailNotificationModel");
    this.addPanelEmailValidator();
    this.loadNotificationUsersInfo();
  }

  loadAllTenantUsers(): Promise<void> {
    const isSDPEnabled = sap.ui.getCore().getModel("featureflags").getProperty("/DWC_DUMMY_SPACE_PERMISSIONS");
    const hasPrivilege = isSDPEnabled
      ? this.getView().getModel("privilege").getProperty("/USER") ||
        this.getView().getModel("privilege").getProperty("/TEAM")
      : this.getView().getModel("privilege").getProperty("/TEAM");
    return new Promise((resolve) => {
      if (hasPrivilege && hasPrivilege.read) {
        getAllUsers(false).then((users) => {
          users.forEach((user) => {
            user.name = `${user.displayName} (${user.id})`;
          });
          this.getView().getModel("emailNotificationModel").setProperty("/allUsers", users);
          resolve();
        });
      } else {
        resolve();
      }
    });
  }

  private attachHanaStateChangeHandler() {
    const circuitBreakerModel = sap.ui.getCore().getModel("circuitbreaker");
    if (circuitBreakerModel) {
      const binding = new sap.ui.model.Binding(
        circuitBreakerModel,
        "/DataHANA",
        (circuitBreakerModel as any).getContext("/"),
        {}
      );
      binding.attachChange(() => {
        this.loadNotificationUsersInfo();
      });
    }
  }

  loadNotificationUsersInfo() {
    const circuitBreakerModel = sap.ui.getCore().getModel("circuitbreaker");
    const hanaState = circuitBreakerModel?.getProperty("/DataHANA");
    const isHanaDown: boolean =
      // On FF removal of isCircuitBreakerYellowStateEnabled be sure to keep boolean logic consistent
      hanaState === State.Red || (hanaState === State.Yellow && !isCircuitBreakerYellowStateEnabled());
    const dataHANAProvisioningState = circuitBreakerModel?.getProperty("/DataHANAProvisioningState");
    const isHanaUpgradeInProgress: boolean =
      dataHANAProvisioningState === State.Red ||
      // On FF removal of isCircuitBreakerYellowStateEnabled be sure to keep boolean logic consistent
      (dataHANAProvisioningState === State.Yellow && !isCircuitBreakerYellowStateEnabled());
    if (!(isHanaDown || isHanaUpgradeInProgress)) {
      const emailPanel: any = this.getView().byId("taskchainEmailNotification");
      emailPanel.setBusy(true);
      emailPanel.setBusyIndicatorDelay(0);
      Promise.all([this.loadAllTenantUsers(), this.getNotificationMailList(), this.getTenantOwner()])
        .then((values) => {
          const tenantOwner = values[2];
          const tenantOwnerDomain = tenantOwner.split("@")[1]?.split(".")?.splice(-2)?.join(".");
          this.getView().getModel("emailNotificationModel").setProperty("/tenantOwnerDomain", tenantOwnerDomain);
          this.updateOthersTabHeader();
          emailPanel.setBusy(false);
        })
        .catch(() => {
          emailPanel.setBusy(false);
        });
    }
  }

  updateNotificationSetting() {
    const model = this.getView().getModel("galileiModel");
    const options = model.getProperty("/options");
    const deploymentDate = this.galileiModel?.deploymentDate;
    const isDeployed = deploymentDate ? true : false;
    const emailNotificationModel = this.getView().getModel("emailNotificationModel");
    emailNotificationModel.setProperty("/isRecipientInputEnabled", isDeployed);
    if (options && options.emailNotifications) {
      const settings = options.emailNotifications;
      emailNotificationModel.setProperty("/selectedNotificationSetting", settings.notificationCondition);
      emailNotificationModel.setProperty("/emailMessage", settings.body);
      emailNotificationModel.setProperty("/emailSubject", settings.subject);
    }
  }

  getTokenId(email) {
    const tokenId = email.replace(/[^a-zA-Z0-9]/g, "");
    const tokenSuffix = Math.floor(Math.random() * 1000);
    return `token_${tokenId}${tokenSuffix}`;
  }

  addPanelEmailValidator() {
    const emailMultiInput = this.getView().byId("recipientEmailAddrInput") as sap.m.MultiInput;
    emailMultiInput.removeAllValidators();
    emailMultiInput.removeAllTokens();
    emailMultiInput.setValueState(sap.ui.core.ValueState.None);
    emailMultiInput.setValueStateText("");
    emailMultiInput.addValidator((args) => {
      const email = args.text?.toLowerCase();
      const emailNotificationModel = this.getView().getModel("emailNotificationModel");
      const nonTenantMembers = emailNotificationModel.getProperty("/nonTenantMembers");
      const selectedMembers = emailNotificationModel.getProperty("/selectedMembers");
      const tokenId = this.getTokenId(email);
      if (selectedMembers.length >= 20) {
        const message = this.getView().getModel("i18n").getResourceBundle().getText("@maxMembersAlreadyAdded");
        sap.m.MessageToast.show(message);
        return;
      }
      if (
        this.validateEmailAddress(email) &&
        this.isTenantOwnerDomain(email) &&
        !this.isDuplicateEmail(email, nonTenantMembers)
      ) {
        const tenantMembers = emailNotificationModel.getProperty("/tenantMembers");
        nonTenantMembers.push({ id: email, name: email });
        emailNotificationModel.setProperty("/nonTenantMembers", nonTenantMembers);
        this.setTenantTypeFlag(tenantMembers, nonTenantMembers);
        return new sap.m.Token(tokenId, { key: email, text: email });
      } else if (!this.validateEmailAddress(email)) {
        return new sap.m.Token(tokenId, { key: `invalid:${email}`, text: email });
      } else if (!this.isTenantOwnerDomain(email)) {
        return new sap.m.Token(tokenId, { key: `invalidDomain:${email}`, text: email });
      } else if (this.isDuplicateEmail(email, nonTenantMembers)) {
        const token = `${tokenId}${Math.random()}`;
        return new sap.m.Token(token, { key: `duplicate:${email}`, text: email });
      }
    });
  }

  addDialogEmailValidator() {
    const emailMultiInput = this.byId(
      "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--taskchainmodeler-properties-ModelProperties--subStatus--selectedEmailAddress"
    ) as sap.m.MultiInput;
    emailMultiInput.removeAllValidators();
    emailMultiInput.addValidator((args) => {
      const email = args.text?.toLowerCase();
      const emailNotificationModel = this.getView().getModel("emailNotificationModel");
      const nonTenantMembers = emailNotificationModel.getProperty("/nonTenantMembers");
      const selectedMembers = emailNotificationModel.getProperty("/selectedMembers");
      const tokenId = this.getTokenId(email);
      if (selectedMembers.length >= 20) {
        const message = this.getView().getModel("i18n").getResourceBundle().getText("@maxMembersAlreadyAdded");
        sap.m.MessageToast.show(message);
        return;
      }
      if (
        this.validateEmailAddress(email) &&
        this.isTenantOwnerDomain(email) &&
        !this.isDuplicateEmail(email, nonTenantMembers)
      ) {
        const tenantMembers = emailNotificationModel.getProperty("/tenantMembers");
        nonTenantMembers.push({ id: email, name: email });
        emailNotificationModel.setProperty("/nonTenantMembers", nonTenantMembers);
        this.setTenantTypeFlag(tenantMembers, nonTenantMembers);
        this.updateOthersTabHeader();
        return new sap.m.Token(tokenId, { key: email, text: email });
      } else if (!this.validateEmailAddress(email)) {
        return new sap.m.Token(tokenId, { key: `invalid:${email}`, text: email });
      } else if (!this.isTenantOwnerDomain(email)) {
        return new sap.m.Token(tokenId, { key: `invalidDomain:${email}`, text: email });
      } else if (this.isDuplicateEmail(email, nonTenantMembers)) {
        const token = `${tokenId}${Math.random()}`;
        return new sap.m.Token(token, { key: `duplicate:${email}`, text: email });
      }
    });
  }

  updateGalileiModelNotificationSetting() {
    const model = this.getView().getModel("galileiModel");
    const emailNotificationModel = this.getView().getModel("emailNotificationModel");
    const options = {
      emailNotifications: {
        notificationCondition: emailNotificationModel.getProperty("/selectedNotificationSetting"),
        body: emailNotificationModel.getProperty("/emailMessage"),
        subject: emailNotificationModel.getProperty("/emailSubject"),
      },
    };
    model.setProperty("/options", options);
  }

  setModelDirtyState() {
    ShellUIService.setDirty(ToolName.DataBuilder, true);
    this.getView().getModel("galileiModel").setProperty("/bDirty", true);
  }

  public onNotificationSettingChange(event) {
    const selectedKey = event.getParameter("selectedItem").getKey();
    if (selectedKey === NotificationCondition.OFF) {
      this.getView().getModel("emailNotificationModel").setProperty("/selectedMembers", []);
      this.getView().getModel("emailNotificationModel").setProperty("/tenantMembers", []);
      this.getView()
        .getModel("emailNotificationModel")
        .setProperty("/nonTenantMembers", [{ id: "", name: "" }]);
    } else {
      this.refreshNotificationMailList();
    }
    this.getView().getModel("emailNotificationModel").setProperty("/selectedNotificationSetting", selectedKey);
    this.updateGalileiModelNotificationSetting();
    this.setModelDirtyState();
  }

  public onEmailSubjectChange(event) {
    const value = event.getParameter("value");
    this.getView().getModel("emailNotificationModel").setProperty("/emailSubject", value);
    this.updateGalileiModelNotificationSetting();
    this.setModelDirtyState();
  }

  public onTenantMemberSearch(oEvent) {
    const aFilters = [];
    const sQuery = oEvent.getSource().getValue();
    if (sQuery && sQuery.length > 0) {
      const displayNameFiler = new Filter("name", FilterOperator.Contains, sQuery);
      const filter = new Filter({
        filters: [displayNameFiler],
      });
      aFilters.push(filter);
    }
    const oList = this.getView().byId(
      "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--taskchainmodeler-properties-ModelProperties--subStatus--tenantMemberList"
    );
    const oBinding = oList.getBinding("items") as any;
    oBinding.filter(aFilters, "Application");
  }

  public onTenantMemberSelection(oEvent) {
    const oList = oEvent.getSource() as sap.m.List;
    const aContexts = oList.getSelectedContexts(true);
    const selectedMembers = this.getView().getModel("emailNotificationModel").getProperty("/selectedMembers");
    if (oEvent.getParameter("selected") && selectedMembers.length >= 20) {
      this.getView().getModel("emailNotificationModel").setProperty("/maxMembersAlreadyAdded", true);
      const message = this.getView().getModel("i18n").getResourceBundle().getText("@maxMembersAlreadyAdded");
      sap.m.MessageToast.show(message);
      const selectedItem = oEvent.getParameter("listItem").getTitle();
      const lastSelectedItemIndex = oList.getSelectedItems().findIndex((item) => item["getTitle"]() === selectedItem);
      const lastSelectedItem = oList.getSelectedItems()[lastSelectedItemIndex];
      oList.setSelectedItem(lastSelectedItem, false);
      return;
    } else {
      this.getView().getModel("emailNotificationModel").setProperty("/maxMembersAlreadyAdded", false);
    }
    const tenantMembers = aContexts.map((context) => {
      const memberObject = context.getObject();
      return { id: memberObject.id, name: memberObject.displayName };
    });
    this.getView().getModel("emailNotificationModel").setProperty("/tenantMembers", tenantMembers);
    const nonTenantMembers = this.getView()
      .getModel("emailNotificationModel")
      .getProperty("/nonTenantMembers")
      .filter((member) => member?.name !== "");
    this.setTenantTypeFlag(tenantMembers, nonTenantMembers);
    this.getView()
      .getModel("emailNotificationModel")
      .setProperty("/selectedMembers", [...tenantMembers, ...nonTenantMembers]);
  }

  public onAddNonTenantMember() {
    const selectedMembers = this.getView().getModel("emailNotificationModel").getProperty("/selectedMembers");
    if (selectedMembers.length >= 20) {
      this.getView().getModel("emailNotificationModel").setProperty("/maxMembersAlreadyAdded", true);
      const message = this.getView().getModel("i18n").getResourceBundle().getText("@maxMembersAlreadyAdded");
      sap.m.MessageToast.show(message);
      return;
    } else {
      this.getView().getModel("emailNotificationModel").setProperty("/maxMembersAlreadyAdded", false);
    }
    const nonTenantMembers = this.getView().getModel("emailNotificationModel").getProperty("/nonTenantMembers");
    nonTenantMembers.push({ id: "", name: "" });
    this.getView().getModel("emailNotificationModel").setProperty("/nonTenantMembers", nonTenantMembers);
  }

  private validateEmailAddress(emailId) {
    const mailRegex = /^\w+[\w-+\.]*\@\w+([-\.]\w+)*\.[a-zA-Z]{2,}$/;
    return emailId.match(mailRegex);
  }

  isDuplicateEmail(email, membersList) {
    const index = membersList.findIndex((member) => member.id.toLowerCase() === email.toLowerCase());
    return index !== -1;
  }

  isTenantOwnerDomain(email) {
    const tenantOwnerDomain = this.getView().getModel("emailNotificationModel").getProperty("/tenantOwnerDomain");
    const emailDomain = email.split("@")[1]?.split(".")?.splice(-2)?.join(".");
    return tenantOwnerDomain.toLowerCase() === emailDomain?.toLowerCase();
  }

  public onNonTenantMemberChange(oEvent) {
    const input = oEvent.getSource() as sap.m.Input;
    const emailValue = oEvent.getParameters().value.toLowerCase();
    const emailValidCheck = this.validateEmailAddress(emailValue);
    const oBundle = this.getView().getModel("i18n").getResourceBundle();
    const nonTenantMembers = this.getView()
      .getModel("emailNotificationModel")
      .getProperty("/nonTenantMembers")
      .filter((member) => member?.name !== "");
    if (this.isDuplicateEmail(emailValue, nonTenantMembers)) {
      const errorTxt = oBundle.getText("@duplicateEmailErrorText");
      input.setValueState(sap.ui.core.ValueState.Error);
      input.setValueStateText(errorTxt);
      return;
    }
    if (!emailValidCheck) {
      const errorTxt = oBundle.getText("@emailFormatError");
      input.setValueState(sap.ui.core.ValueState.Error);
      input.setValueStateText(errorTxt);
      return;
    }
    if (!this.isTenantOwnerDomain(emailValue)) {
      const tenantOwnerDomain = this.getView().getModel("emailNotificationModel").getProperty("/tenantOwnerDomain");
      const errorTxt = oBundle.getText("@tenantOwnerDomainMatchErrorText", [tenantOwnerDomain]);
      input.setValueState(sap.ui.core.ValueState.Error);
      input.setValueStateText(errorTxt);
      return;
    } else {
      input.setValueState(sap.ui.core.ValueState.None);
    }
    const selectedItem = oEvent?.getSource()?.getBindingContext().getPath();
    const listIndex = selectedItem.split("/")[2];

    const tenantMembers = this.getView().getModel("emailNotificationModel").getProperty("/tenantMembers");
    nonTenantMembers.splice(+listIndex, 1, { id: emailValue, name: emailValue });
    this.getView().getModel("emailNotificationModel").setProperty("/nonTenantMembers", nonTenantMembers);
    this.setTenantTypeFlag(tenantMembers, nonTenantMembers);
    this.getView()
      .getModel("emailNotificationModel")
      .setProperty("/selectedMembers", [...tenantMembers, ...nonTenantMembers]);
    this.updateOthersTabHeader();
  }

  private setTenantTypeFlag(tenantMembers: any[], nonTenantMembers: any[]) {
    tenantMembers.forEach((obj) => {
      obj.tenantFlag = true;
    });
    nonTenantMembers.forEach((obj) => {
      obj.tenantFlag = false;
    });
  }

  public onRemoveNonTenantMember(oEvent) {
    this.getView().getModel("emailNotificationModel").setProperty("/maxMembersAlreadyAdded", false);
    const nonTenantMembers = this.getView().getModel("emailNotificationModel").getProperty("/nonTenantMembers");
    const tenantMembers = this.getView().getModel("emailNotificationModel").getProperty("/tenantMembers");
    const sPath = oEvent.getParameters().listItem.getBindingContextPath();
    const itemIndex = sPath.split("/")[2];
    nonTenantMembers.splice(itemIndex, 1);
    this.getView().getModel("emailNotificationModel").setProperty("/nonTenantMembers", nonTenantMembers);
    const filteredMembersList = nonTenantMembers.filter((member) => member?.name !== "");
    this.getView()
      .getModel("emailNotificationModel")
      .setProperty("/selectedMembers", [...tenantMembers, ...filteredMembersList]);
    this.updateOthersTabHeader();
  }

  public updateOthersTabHeader() {
    const model = this.getView().getModel("emailNotificationModel");
    if (model) {
      const nonTenantMembers = model.getProperty("/nonTenantMembers")?.filter((member) => member.id !== "");
      const totalNonTenantMembers = nonTenantMembers?.length;
      const header = this.getView().getModel("i18n").getResourceBundle().getText("@others", [totalNonTenantMembers]);
      model.setProperty("/othersTabHeader", header);
    }
  }

  public displayNonTenantMemberCount(nonTenantMembers) {
    nonTenantMembers = nonTenantMembers?.filter((member) => member.id !== "");
    const totalNonTenantMembers = nonTenantMembers?.length;
    return this.getView().getModel("i18n").getResourceBundle().getText("@others", [totalNonTenantMembers]);
  }

  updateRemovedMember(removedTokens) {
    const removedToken = removedTokens[0].getKey();
    const tenantMembers = this.getView().getModel("emailNotificationModel").getProperty("/tenantMembers");
    let nonTenantMembers = this.getView()
      .getModel("emailNotificationModel")
      .getProperty("/nonTenantMembers")
      .filter((member) => member?.name !== "");
    const tokenIndex = tenantMembers.findIndex((member) => member.id === removedToken);
    if (tokenIndex !== -1) {
      tenantMembers.splice(tokenIndex, 1);
      this.getView().getModel("emailNotificationModel").setProperty("/tenantMembers", tenantMembers);
    } else {
      nonTenantMembers = nonTenantMembers.filter((member) => member.id !== removedToken);
      this.getView().getModel("emailNotificationModel").setProperty("/nonTenantMembers", nonTenantMembers);
    }
    this.getView()
      .getModel("emailNotificationModel")
      .setProperty("/selectedMembers", [...tenantMembers, ...nonTenantMembers]);
  }

  hasInvalidEmail(tokens) {
    const index = tokens.findIndex((token) => {
      const key = token.mProperties.key;
      return key.split(":")[0] === "invalid";
    });
    return index !== -1;
  }

  hasInvalidDomain(tokens) {
    const index = tokens.findIndex((token) => {
      const key = token.mProperties.key;
      return key.split(":")[0] === "invalidDomain";
    });
    return index !== -1;
  }

  hasDuplicateEmailToken(tokens) {
    const index = tokens.findIndex((token) => {
      const key = token.mProperties.key;
      return key.split(":")[0] === "duplicate";
    });
    return index !== -1;
  }

  public onMemberTokenUpdateInValueHelp(oEvent) {
    const emailNotificationModel = this.getView().getModel("emailNotificationModel");
    const removedTokens = oEvent.getParameter("removedTokens");
    const emailMultiInput = oEvent.getSource();
    const oBundle = this.getView().getModel("i18n").getResourceBundle();
    const tokens = emailMultiInput
      .getTokens()
      .filter((token) => removedTokens.findIndex((remToken) => remToken.mProperties.key === token.mProperties.key));
    emailNotificationModel.setProperty("/maxMembersAlreadyAdded", false);
    if (removedTokens.length > 0) {
      this.updateRemovedMember(removedTokens);
      this.setSelectedTenantMembers();
    }
    if (this.hasInvalidEmail(tokens)) {
      const errorText = oBundle.getText("@emailFormatError");
      emailMultiInput.setValueState(sap.ui.core.ValueState.Error);
      emailMultiInput.setValueStateText(errorText);
    } else if (this.hasInvalidDomain(tokens)) {
      const tenantOwnerDomain = emailNotificationModel.getProperty("/tenantOwnerDomain");
      const errorText = oBundle.getText("@tenantOwnerDomainMatchErrorText", [tenantOwnerDomain]);
      emailMultiInput.setValueState(sap.ui.core.ValueState.Error);
      emailMultiInput.setValueStateText(errorText);
    } else if (this.hasDuplicateEmailToken(tokens)) {
      const errorText = oBundle.getText("@duplicateEmailErrorText");
      emailMultiInput.setValueState(sap.ui.core.ValueState.Error);
      emailMultiInput.setValueStateText(errorText);
    } else {
      const nonTenantMembers = emailNotificationModel.getProperty("/nonTenantMembers");
      const tenantMembers = emailNotificationModel.getProperty("/tenantMembers");
      emailNotificationModel.setProperty("/selectedMembers", [...tenantMembers, ...nonTenantMembers]);
      emailMultiInput.setValueState(sap.ui.core.ValueState.None);
    }
    this.updateOthersTabHeader();
  }

  public onMemberTokenUpdateInPanel(oEvent) {
    const oBundle = this.getView().getModel("i18n").getResourceBundle();
    const emailMultiInput = oEvent.getSource();
    const emailNotificationModel = this.getView().getModel("emailNotificationModel");
    const removedTokens = oEvent.getParameter("removedTokens");
    let doSave = false;
    const saveEmail = () => {
      this.saveEmailRecipients(
        () => {
          emailMultiInput.setBusy(true);
        },
        () => {
          emailMultiInput.setBusy(false);
        },
        () => {
          emailMultiInput.setBusy(false);
        }
      );
    };
    const tokens = emailMultiInput
      .getTokens()
      .filter((token) => removedTokens.findIndex((remToken) => remToken.mProperties.key === token.mProperties.key));
    emailNotificationModel.setProperty("/maxMembersAlreadyAdded", false);
    if (removedTokens.length > 0) {
      this.updateRemovedMember(removedTokens);
      doSave = true;
    }
    if (this.hasInvalidEmail(tokens)) {
      const errorText = oBundle.getText("@emailFormatError");
      emailMultiInput.setValueState(sap.ui.core.ValueState.Error);
      emailMultiInput.setValueStateText(errorText);
    } else if (this.hasInvalidDomain(tokens)) {
      const tenantOwnerDomain = emailNotificationModel.getProperty("/tenantOwnerDomain");
      const errorText = oBundle.getText("@tenantOwnerDomainMatchErrorText", [tenantOwnerDomain]);
      emailMultiInput.setValueState(sap.ui.core.ValueState.Error);
      emailMultiInput.setValueStateText(errorText);
    } else if (this.hasDuplicateEmailToken(tokens)) {
      const errorText = oBundle.getText("@duplicateEmailErrorText");
      emailMultiInput.setValueState(sap.ui.core.ValueState.Error);
      emailMultiInput.setValueStateText(errorText);
    } else {
      const nonTenantMembers = emailNotificationModel.getProperty("/nonTenantMembers");
      const tenantMembers = emailNotificationModel.getProperty("/tenantMembers");
      emailNotificationModel.setProperty("/selectedMembers", [...tenantMembers, ...nonTenantMembers]);
      emailMultiInput.setValueState(sap.ui.core.ValueState.None);
      doSave = true;
    }
    if (doSave) {
      saveEmail();
    }
  }

  public onEmailMessageChange(event) {
    const value = event.getParameter("value");
    const placeholderSet = this.getView().getModel("emailNotificationModel").getProperty("/placeholderSet");
    const placeholderRegx = /<[a-zA-Z0-9_@#$%^&*()-=+!?]*>/g;
    const matchedPlaceHolders = value.matchAll(placeholderRegx);
    for (const placeholder of matchedPlaceHolders) {
      if (!placeholderSet.has(placeholder[0])) {
        const message = this.getView()
          .getModel("i18n")
          .getResourceBundle()
          .getText("@inCorrectPlaceHolder", [placeholder]);
        sap.m.MessageToast.show(message);
        return;
      }
    }
    this.getView().getModel("emailNotificationModel").setProperty("/emailMessage", value);
    this.updateGalileiModelNotificationSetting();
    this.setModelDirtyState();
  }

  public onResetEmailMsg() {
    const i18nResourceBundle = this.getView().getModel("i18n").getResourceBundle();
    const emailMsg = i18nResourceBundle.getText("@emailMessageTemplateText");
    this.getView().getModel("emailNotificationModel").setProperty("/emailMessage", emailMsg);
    this.updateGalileiModelNotificationSetting();
    this.setModelDirtyState();
  }

  public handleRecipientEmailValueHelp() {
    const emailNotificationModel = this.getView().getModel("emailNotificationModel");
    const oFragment = require("./fragment/SelectEmailRecipient.fragment.xml");
    if (!this.selectEmailRecipientDialog) {
      this.selectEmailRecipientDialog = sap.ui.xmlfragment(
        this.getView().getId() + "--subStatus",
        oFragment,
        this
      ) as sap.m.Dialog;
      this.getView().addDependent(this.selectEmailRecipientDialog);
      this.selectEmailRecipientDialog.setEscapeHandler(() => {
        this.onCancelEmailRecipients();
      });
    }
    this.selectEmailRecipientDialog.setModel(emailNotificationModel);
    this.selectEmailRecipientDialog.open();

    this.resetValueHelpIconTab();
    this.resetSelectedMembersField();
    this.setSelectedTenantMembers();
    this.clearSearchField();
    this.addDialogEmailValidator();
    this.updateOthersTabHeader();
    this.updateOthersAddButton();
    this.clearEmptyNonTenantMembers();
  }

  updateOthersAddButton() {
    const selectedMembers = this.getView().getModel("emailNotificationModel").getProperty("/selectedMembers");
    const maxMembersAlreadyAdded = selectedMembers.length >= 20;
    this.getView().getModel("emailNotificationModel").setProperty("/maxMembersAlreadyAdded", maxMembersAlreadyAdded);
  }

  clearEmptyNonTenantMembers() {
    const nonTenantMembers = this.getView().getModel("emailNotificationModel").getProperty("/nonTenantMembers");
    const filterList = nonTenantMembers.filter((member) => member.id.length > 0);
    this.getView().getModel("emailNotificationModel").setProperty("/nonTenantMembers", filterList);
  }

  clearSearchField() {
    const oList = this.getView().byId(
      "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--taskchainmodeler-properties-ModelProperties--subStatus--tenantMemberList"
    );
    const model = this.getView().getModel("emailNotificationModel");
    if (model) {
      const oBinding = oList.getBinding("items") as any;
      oBinding.filter([]);
      model.setProperty("/tenantMemberSearch", "");
    }
  }

  resetValueHelpIconTab() {
    const iconTabBarId =
      "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--taskchainmodeler-properties-ModelProperties--subStatus--emailRecipientsTabs";
    const iconTab = this.getView().byId(iconTabBarId) as sap.m.IconTabBar;
    iconTab.setSelectedKey("tenantMembers");
  }

  resetSelectedMembersField() {
    const multiInputId =
      "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--taskchainmodeler-properties-ModelProperties--subStatus--selectedEmailAddress";
    const multiInput = this.getView().byId(multiInputId) as sap.m.MultiInput;
    this.getView().getModel("emailNotificationModel").setProperty("/selectedMembers", []);
    multiInput.setValueState(sap.ui.core.ValueState.None);
    multiInput.setValueStateText("");
    const emailNotificationModel = this.getView().getModel("emailNotificationModel");
    const nonTenantMembers = emailNotificationModel.getProperty("/nonTenantMembers");
    const tenantMembers = emailNotificationModel.getProperty("/tenantMembers");
    this.getView()
      .getModel("emailNotificationModel")
      .setProperty("/selectedMembers", [...tenantMembers, ...nonTenantMembers]);
  }

  setSelectedTenantMembers() {
    const oList = this.getView().byId(
      "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--taskchainmodeler-properties-ModelProperties--subStatus--tenantMemberList"
    ) as sap.m.List;
    // const oBinding = oList.getBinding("items") as any;
    const tenantMembers = this.getView().getModel("emailNotificationModel").getProperty("/tenantMembers");
    const listItems = oList.getItems();
    oList.getSelectedItems().forEach((item) => {
      oList.setSelectedItem(item, false);
    });
    tenantMembers.forEach((member) => {
      const selectedMember = listItems.find((item) => (item as any).getTitle() === `${member.name} (${member.id})`);
      oList.setSelectedItem(selectedMember);
    });
  }

  saveEmailRecipients(onBeforeSave: () => void, onSuccess: () => void, onError: (err) => void) {
    const spaceId = this.getSpaceName();
    const model = this.getView().getModel("galileiModel").getProperty("/");
    const taskChainName = model.name;
    const sUrl = `tf/${spaceId}/taskchains/${taskChainName}/updatemailinglist`;
    const selectedMembers = this.getView().getModel("emailNotificationModel").getProperty("/selectedMembers");
    const tenantMembers = selectedMembers.filter((obj) => obj.tenantFlag).map((obj) => obj.id);
    const nonTenantMembers = selectedMembers.filter((obj) => !obj.tenantFlag).map((obj) => obj.id);
    const membersData = { tenantMembers: tenantMembers, others: nonTenantMembers };
    onBeforeSave();
    ServiceCall.request<any>({
      url: sUrl,
      type: HttpMethod.PUT,
      contentType: ContentType.APPLICATION_JSON,
      dataType: DataType.TEXT,
      data: JSON.stringify(membersData),
    })
      .then((res) => {
        onSuccess();
        const self = this;
        this.galileiModel.resource.applyUndoableAction(
          function () {
            if (selectedMembers.length < 1) {
              self.getView().getModel("galileiModel").setProperty("/isEmailRecipientEmpty", true);
              model.validate();
            } else {
              self.getView().getModel("galileiModel").setProperty("/isEmailRecipientEmpty", false);
              model.validate();
            }
          },
          "Update email recipients",
          true
        );
      })
      .catch((err) => {
        onError(err);
      });
  }

  hasDuplicateMembers(members) {
    let hasDuplicates = false;
    members.forEach((member, index) => {
      const findIndex = members.findIndex(
        (mem, ind) => mem.id.length > 0 && mem.id.toLowerCase() === member.id.toLowerCase() && ind !== index
      );
      if (findIndex > -1) {
        hasDuplicates = true;
      }
    });
    return hasDuplicates;
  }

  hasInvalidNonTenantMembers() {
    const nonTenantMembers = this.getView().getModel("emailNotificationModel").getProperty("/nonTenantMembers");
    const invalidNonTenantMember = nonTenantMembers.findIndex(
      (member) =>
        member.id.length > 0 && (!this.validateEmailAddress(member.id) || !this.isTenantOwnerDomain(member.id))
    );
    return this.hasDuplicateMembers(nonTenantMembers) || invalidNonTenantMember > -1;
  }

  public onSaveEmailRecipients() {
    if (this.hasInvalidNonTenantMembers()) {
      const oBundle = this.getView().getModel("i18n").getResourceBundle();
      const errorTxt = oBundle.getText("@emailFormatErrorInListText");
      sap.m.MessageToast.show(errorTxt);
    } else {
      const emailMultiInput = this.getView().byId("recipientEmailAddrInput") as sap.m.MultiInput;
      this.saveEmailRecipients(
        () => {
          // this.selectEmailRecipientDialog.setBusy(true);
          emailMultiInput.setBusy(true);
          this.selectEmailRecipientDialog.close();
        },
        () => {
          // this.selectEmailRecipientDialog.setBusy(false);
          // this.selectEmailRecipientDialog.close();
          emailMultiInput.setBusy(false);
        },
        (error) => {
          const oBundle = this.getView().getModel("i18n").getResourceBundle();
          const errMsg = oBundle.getText("@emailUpdateError");
          MessageHandler.exception({ exception: error, message: errMsg });
          emailMultiInput.setBusy(false);
          this.refreshNotificationMailList();
        }
      );
    }
  }

  refreshNotificationMailList() {
    const emailMultiInput = this.getView().byId("recipientEmailAddrInput") as sap.m.MultiInput;
    if (!this.isHanaDown()) {
      emailMultiInput.setBusy(true);
      this.getNotificationMailList().then(() => {
        emailMultiInput.setBusy(false);
        this.updateOthersTabHeader();
      });
    }
  }

  isHanaDown() {
    const circuitBreakerModel = sap.ui.getCore().getModel("circuitbreaker");
    if (circuitBreakerModel) {
      const hanaState = circuitBreakerModel?.getProperty("/DataHANA");
      const isHanaDown: boolean =
        // On FF removal of isCircuitBreakerYellowStateEnabled be sure to keep boolean logic consistent
        hanaState === State.Red || (hanaState === State.Yellow && !isCircuitBreakerYellowStateEnabled());
      const dataHANAProvisioningState = circuitBreakerModel?.getProperty("/DataHANAProvisioningState");
      const isHanaUpgradeInProgress: boolean =
        dataHANAProvisioningState === State.Red ||
        // On FF removal of isCircuitBreakerYellowStateEnabled be sure to keep boolean logic consistent
        (dataHANAProvisioningState === State.Yellow && !isCircuitBreakerYellowStateEnabled());
      if (isHanaDown || isHanaUpgradeInProgress) {
        return true;
      }
    }
    return false;
  }

  getNotificationMailList() {
    if (this.isHanaDown()) {
      return;
    }
    return new Promise<void>((resolve, reject) => {
      const spaceId = this.getSpaceName();
      const model = this.getView().getModel("galileiModel").getProperty("/");
      const taskChainName = model.name;
      const sUrl = `tf/${spaceId}/taskchains/${taskChainName}/getmailinglist`;
      ServiceCall.request<any>({
        url: sUrl,
        type: HttpMethod.GET,
        dataType: DataType.JSON,
      })
        .then((resp) => {
          const allUsers = this.getView().getModel("emailNotificationModel").getProperty("/allUsers");
          let bUsersNotExits = false;
          if ((Array.isArray(allUsers) && allUsers.length === 0) || !allUsers) {
            bUsersNotExits = true;
          }
          const tenantMembers = resp.data.tenantMembers
            .map((member) => {
              const user = allUsers.find((userObj) => userObj.id === member);
              return {
                name: user && user.displayName ? user.displayName : member,
                id: member,
                tenantFlag: true,
                isActive: user !== undefined || bUsersNotExits ? true : false,
              };
            })
            .filter((user) => user.isActive);
          const nonTenantMembers = resp.data.others.map((member) => ({ name: member, id: member, tenantFlag: false }));
          this.getView().getModel("emailNotificationModel").setProperty("/tenantMembers", tenantMembers);
          this.getView().getModel("emailNotificationModel").setProperty("/nonTenantMembers", nonTenantMembers);
          this.getView()
            .getModel("emailNotificationModel")
            .setProperty("/selectedMembers", [...tenantMembers, ...nonTenantMembers]);
          const selectedMembersLength = this.getView()
            .getModel("emailNotificationModel")
            .getProperty("/selectedMembers").length;
          const self = this;
          this.galileiModel.resource.applyUndoableAction(
            function () {
              if (selectedMembersLength < 1) {
                self.getView().getModel("galileiModel").setProperty("/isEmailRecipientEmpty", true);
                model.validate();
              } else {
                self.getView().getModel("galileiModel").setProperty("/isEmailRecipientEmpty", false);
                model.validate();
              }
            },
            "Gets email recipients",
            true
          );

          resolve();
        })
        .catch((err) => {
          reject();
        });
    });
  }

  getTenantOwner() {
    return new Promise<string>((resolve, reject) => {
      const sUrl = "customerInfo";
      const tenantOwnerRequest = {
        action: "getParam",
        data: {
          name: "ownerEmail",
        },
      };
      ServiceCall.post(
        sUrl,
        {
          contentType: ContentType.APPLICATION_JSON,
          dataType: DataType.JSON,
        },
        true,
        JSON.stringify(tenantOwnerRequest)
      )
        .then((p) => {
          let responseBody: any = {};
          if (typeof p.data === "string") {
            responseBody = JSON.parse(p.data);
          } else {
            responseBody = p.data;
          }
          resolve(responseBody.value);
        })
        .catch((err) => resolve(""));
    });
  }

  public onCancelEmailRecipients() {
    this.refreshNotificationMailList();
    this.selectEmailRecipientDialog.close();
  }

  public onEmailMessageInfoClick(event) {
    const sourceControl = event.getSource();
    const oFragment = require("./fragment/PlaceholdersForTaskChainDetails.fragment.xml");
    if (!this.taskChainDetailsPopover) {
      this.taskChainDetailsPopover = sap.ui.xmlfragment(
        this.getView().getId() + "--popover",
        oFragment,
        this
      ) as sap.m.Popover;
      this.getView().addDependent(this.taskChainDetailsPopover);
    }
    this.taskChainDetailsPopover.openBy(sourceControl, false);
  }

  public async copyPlaceholderText(rowData) {
    await navigator.clipboard.writeText(rowData.placeholder);
    const msg = this.getView().getModel("i18n").getResourceBundle().getText("@placeholderCopied");
    sap.m.MessageToast.show(msg);
  }
  public setDataPreviewWarningVisibility() {
    const oWorkbenModel = this.getView().getModel("workbenchEnv");
    oWorkbenModel.setProperty("/detailsPage/messageStrip/visible", false);
  }

  public onBusinessNameChange(event) {
    const nameValidator = NamingHelper.getNameInputValidator();
    const technicalNameInput = this.getView().byId("technicalName") as sap.m.Input;
    const model = this.getView().getModel("galileiModel").getProperty("/");
    if (model) {
      model.label = event.getParameter("newValue");
    }
    nameValidator.onBusinessNameChange(event, technicalNameInput, NameUsage.entity, undefined, model.name);
  }

  public onTechnicalNameChange(event) {
    const nameValidator = NamingHelper.getNameInputValidator();
    nameValidator.onTechnicalNameChange(event, NameUsage.entity);
  }

  private fetchStatus() {
    const sSpaceId = this.getSpaceName();
    const sUrl = "tf/" + sSpaceId + "/logs?objectId=" + this.galileiModel.name;
    if (!this.galileiModel.isNew) {
      const oStatusPanel: any = this.getView().byId("nodePropertyStatusPanel");
      oStatusPanel.setBusy(true);
      oStatusPanel.setBusyIndicatorDelay(0);
      ServiceCall.request<any>({
        url: sUrl,
        type: HttpMethod.GET,
        contentType: ContentType.APPLICATION_JSON,
      })
        .then(async (oResponse) => {
          this.updateStatus(oResponse);
          this.galileiModel.executionErrorDetails = undefined;
          this.getView().getModel("galileiModel").refresh(true);
          oStatusPanel.setBusy(false);
        })
        .catch((oError) => {
          this.galileiModel.executionErrorDetails = oError;
          this.getView().getModel("galileiModel").refresh(true);
          // eslint-disable-next-line no-console
          oStatusPanel.setBusy(false);
          console.log(oError);
        });
    }
  }

  public navToMonitoring() {
    const params = {
      objectId: this.galileiModel.name,
      spaceId: this.getSpaceName(),
    };
    const semanticObject = "dataintegration";
    const targetParameter = "routeTo";
    params[targetParameter] = "taskChainMonitorLog";
    ShellNavigationService.toExternal({
      target: {
        semanticObject: semanticObject,
      },
      params,
    });
  }

  public refreshStatus() {
    this.getSchedule();
    this.fetchStatus();
  }

  public showErrorDetailsFormatter(errorDetails: any): boolean {
    if (errorDetails) {
      return true;
    }
    return false;
  }

  /**
   * showDeploymentDateFormatter
   * @param {any} deploymentDate Deployment date of task chain
   * @returns {boolean} whether to show deployment date or not
   */
  public showDeploymentDateFormatter(deploymentDate: any): boolean {
    let showDate = false;
    if (deploymentDate) {
      showDate = true;
    }
    return showDate;
  }

  public localizeText(model: string, text: string, parameters: any[] = []): string {
    return Utils.localizeText(this.getView().getModel(model), text, parameters);
  }

  /**
   * Confirm user to redeploy older version of taskchain
   */
  public openRevertDeploymentDialog() {
    const entity = this.getView().getModel("galileiModel").getData();
    entity.technicalName = entity.name;
    commonUtils.openRevertDeploymentDialog(entity);
  }

  /**
   * Setting up Space Status Model properties in task chain
   * @memberof ModelPropertiesClass
   */
  public setupSpaceStatusModel() {
    const spaceModel = new sap.ui.model.json.JSONModel({
      isUnlocked: true,
    });
    Repo.getSpaceDetails(this.getSpaceName(), ["status"]).then((details) => {
      const spaceLocked = details && details.status === "locked";
      spaceModel.setProperty("/isSpaceUnlocked", !spaceLocked);
      this.getView().setModel(spaceModel, "spaceModel");
    });
  }

  /**
   * Setting up Schedule Model properties in task chain which contain i18nTask model and scheduleAction Model
   * @memberof ModelPropertiesClass
   */
  public setupScheduleActionsModel() {
    const i18nTaskModel = new sap.ui.model.resource.ResourceModel({
      bundleName: require("../../taskscheduler/i18n/i18n.properties"),
    });
    this.getView().setModel(i18nTaskModel, "i18n_task");
    const oScheduleActionModel = new sap.ui.model.json.JSONModel({
      isScheduled: false,
    });
    this.getView().setModel(oScheduleActionModel, "scheduleActionModel");
    // update scheduleActionModel if galileiModel is not new
    if (!this.galileiModel.isNew) {
      this.getSchedule();
    }

    // Setting up the schedule menu visibility
    const isGalileiModelNew: boolean = this.galileiModel.isNew;
    // // Setting up the schedule menu visibility
    const deploymentDate = this.galileiModel?.deploymentDate;
    let isDeployed = false;
    if (deploymentDate) {
      isDeployed = true;
    }

    // Checking for scheduling integration privilege
    const isSDPEnabled = sap.ui.getCore().getModel("featureflags").getProperty("/DWC_DUMMY_SPACE_PERMISSIONS");
    if (isSDPEnabled) {
      this.getView().getModel("scheduleActionModel").setProperty("/isDeployed", isDeployed);
    } else {
      const dataintegrationPrivilege = ShellContainer.get()
        .getPrivilegeService()
        .getPrivilegesByType("DWC_DATAINTEGRATION");
      const hasSchedulingIntegrationPrivilege =
        dataintegrationPrivilege && dataintegrationPrivilege.execute === true ? true : false;
      this.getView()
        .getModel("scheduleActionModel")
        .setProperty("/hasSchedulingIntegrationPrivilege", hasSchedulingIntegrationPrivilege);
      const isScheduleMenuVisible = hasSchedulingIntegrationPrivilege && isDeployed;
      this.getView().getModel("scheduleActionModel").setProperty("/isVisible", isScheduleMenuVisible);
    }
  }

  public resetScheduleModel(): void {
    const scheduleModel = this.getView().getModel("scheduleActionModel");
    scheduleModel.setProperty("/isScheduled", false);
  }

  /**
   * Setting up the isSchedule Property of schedule Action model.
   * @memberof ModelPropertiesClass
   */
  public getSchedule() {
    const sSpaceId = this.getSpaceName();
    const formattedName = this.galileiModel.name;

    const sUrl =
      "/tf/" +
      sSpaceId +
      "/schedules?applicationId=" +
      ApplicationId.TASK_CHAINS +
      "&objectId=" +
      formattedName +
      "&activity=" +
      Activity.RUN_CHAIN;
    return ServiceCall.request<any>({
      url: sUrl,
      type: HttpMethod.GET,
      contentType: ContentType.APPLICATION_JSON,
    })
      .then(async (oResponse) => {
        if (oResponse.data[0] && oResponse.data[0].scheduleId) {
          const scheduleModel = this.getView().getModel("scheduleActionModel");
          scheduleModel.setProperty("/isScheduled", true);
        } else {
          this.resetScheduleModel();
        }
      })
      .catch(() => {
        // error
        this.resetScheduleModel();
      });
  }

  async initScheduleDialog() {
    if (!this["newScheduleDialog"]) {
      this["newScheduleDialog"] = await getTaskScheduer("taskChainEditorTaskScheduler");
    }
  }

  /**
   * Create a new schedule
   * @memberof ModelPropertiesClass
   */
  public async onCreateSchedule(): Promise<void> {
    const formattedName = this.galileiModel.name;
    const sSpaceId = this.getSpaceName();
    const data: ITaskScheduleRequest = {
      objectId: formattedName,
      applicationId: ApplicationId.TASK_CHAINS,
      activity: Activity.RUN_CHAIN,
      description: "Create Schedule Task Chain",
      activationStatus: "ENABLED",
    };
    let scheduleDialog;
    if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
      await this.initScheduleDialog();
      scheduleDialog = this["newScheduleDialog"];
      recordAction(`createTaskSchedule: ${data.applicationId}`, "taskSchedule", "onCreate");
    } else {
      scheduleDialog = (
        this.getView().byId("scheduleDialog") as sap.ui.core.mvc.View
      ).getController() as ITTaskScheduleController;
    }
    const oResourceBundle = this.getView().getModel("i18n_task").getResourceBundle();
    const oDataBuilderWorkbenchController = (
      sap.ui.getCore().byId("shellMainContent---databuilderComponent---databuilderWorkbench") as sap.ui.core.mvc.View
    ).getController() as IWorkbenchController;
    oDataBuilderWorkbenchController.setBusy(true);

    scheduleDialog.createTaskSchedule(
      data,
      sSpaceId,
      ApplicationId.TASK_CHAINS,
      () => {
        if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
          recordAction(`saveTaskSchedule: ${data.applicationId}`, "taskSchedule", "save");
        }
        const msg = oResourceBundle.getText("createScheduleSuccess");
        MessageHandler.success(msg);
        oDataBuilderWorkbenchController.setBusy(false);
        this.refreshStatus();
      },
      (error) => {
        if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
          recordAction(`saveTaskSchedule: ${data.applicationId}`, "taskSchedule", "save");
          MessageHandler.exception({
            exception: error.error,
            message: error.message,
          });
        }
        oDataBuilderWorkbenchController.setBusy(false);
      },
      () => {
        oDataBuilderWorkbenchController.setBusy(false);
      }
    );
  }

  /**
   * Edit an existing schedule
   * @memberof ModelPropertiesClass
   */
  public async onEditSchedule(): Promise<void> {
    const formattedName = this.galileiModel.name;
    const sSpaceId = this.getSpaceName();
    const data: ITaskScheduleRequest = {
      objectId: formattedName,
      applicationId: ApplicationId.TASK_CHAINS,
      activity: Activity.RUN_CHAIN,
      description: "Edit Schedule Task Chain",
      activationStatus: "ENABLED",
    };
    let scheduleDialog;
    if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
      await this.initScheduleDialog();
      scheduleDialog = this["newScheduleDialog"];
      recordAction(`editTaskSchedule: ${data.applicationId}`, "taskSchedule", "onEdit");
    } else {
      scheduleDialog = (
        this.getView().byId("scheduleDialog") as sap.ui.core.mvc.View
      ).getController() as ITTaskScheduleController;
    }
    const oResourceBundle = this.getView().getModel("i18n_task").getResourceBundle();
    const oDataBuilderWorkbenchController = (
      sap.ui.getCore().byId("shellMainContent---databuilderComponent---databuilderWorkbench") as sap.ui.core.mvc.View
    ).getController() as IWorkbenchController;
    oDataBuilderWorkbenchController.setBusy(true);

    scheduleDialog.changeTaskSchedule(
      data,
      sSpaceId,
      ApplicationId.TASK_CHAINS,
      () => {
        if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
          recordAction(`saveTaskSchedule: ${data.applicationId}`, "taskSchedule", "save");
        }
        const msg = oResourceBundle.getText("updateScheduleSuccess");
        MessageHandler.success(msg);
        oDataBuilderWorkbenchController.setBusy(false);
        this.refreshStatus();
      },
      (error) => {
        if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
          recordAction(`saveTaskSchedule: ${data.applicationId}`, "taskSchedule", "save");
          MessageHandler.exception({
            exception: error.error,
            message: error.message,
          });
        }
        oDataBuilderWorkbenchController.setBusy(false);
      },
      () => {
        oDataBuilderWorkbenchController.setBusy(false);
      }
    );
  }

  /**
   * Delete an existing schedule
   * @memberof ModelPropertiesClass
   */
  public async onDeleteSchedule(): Promise<void> {
    const formattedName = this.galileiModel.name;
    const sSpaceId = this.getSpaceName();
    const data: ITaskScheduleRequest = {
      objectId: formattedName,
      applicationId: ApplicationId.TASK_CHAINS,
      activity: Activity.RUN_CHAIN,
      description: "Delete Schedule Task Chain",
      activationStatus: "ENABLED",
    };
    let scheduleDialog;
    if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
      await this.initScheduleDialog();
      scheduleDialog = this["newScheduleDialog"];
    } else {
      scheduleDialog = (
        this.getView().byId("scheduleDialog") as sap.ui.core.mvc.View
      ).getController() as ITTaskScheduleController;
    }
    const oResourceBundle = this.getView().getModel("i18n_task").getResourceBundle();
    const oDataBuilderWorkbenchController = (
      sap.ui.getCore().byId("shellMainContent---databuilderComponent---databuilderWorkbench") as sap.ui.core.mvc.View
    ).getController() as IWorkbenchController;
    oDataBuilderWorkbenchController.setBusy(true);

    scheduleDialog.deleteSchedule(
      data,
      sSpaceId,
      ApplicationId.TASK_CHAINS,
      () => {
        const msg = oResourceBundle.getText("deleteScheduleSuccess");
        MessageHandler.success(msg);
        oDataBuilderWorkbenchController.setBusy(false);
        this.refreshStatus();
      },
      (error) => {
        oDataBuilderWorkbenchController.setBusy(false);
      },
      () => {
        oDataBuilderWorkbenchController.setBusy(false);
      }
    );
  }

  public nodesHeaderTextFormatter() {
    return this.updateSearchField();
  }

  public onPressDeleteNodes() {
    const self = this;
    const objectList = this.getView().byId("nodesTokenList");
    const aSelectedContexts = (objectList as any)?.getSelectedContexts("galileiModel");
    if (aSelectedContexts && aSelectedContexts.length) {
      const aSelectedObjects = aSelectedContexts.map((context) => context.getObject());
      this.galileiModel.resource.applyUndoableAction(function () {
        aSelectedObjects.map((obj) => sap.cdw.taskchainmodeler.ModelImpl.deleteObject(obj, true));
        sap.cdw.taskchainmodeler.ModelImpl.requestAdjustDiagramsContent({});
      });
      setTimeout(() => {
        self.updateSearchField();
      }, 100);
    }
  }

  public onAfterRendering() {
    this.onSelectionChange();
  }

  public onSelectionChange(): void {
    const selectedItems = (this.getView().byId("nodesTokenList") as any).getSelectedContexts("galileiModel");
    this.getView()
      .getModel("galileiModel")
      .setProperty("/isDeleteVisible", selectedItems?.length >= 1 ? true : false);
  }
}
export const ModelProperties = smartExtend(
  TaskChainCommonProperties,
  "sap.cdw.components.taskchainmodeler.properties.ModelProperties",
  ModelPropertiesClass
);

sap.ui.define("sap/cdw/components/taskchainmodeler/properties/ModelProperties.controller", [], function () {
  return ModelProperties;
});
