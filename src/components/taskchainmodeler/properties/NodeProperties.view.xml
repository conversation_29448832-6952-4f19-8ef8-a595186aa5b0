<mvc:View
  xmlns:core="sap.ui.core"
  xmlns="sap.m"
  xmlns:l="sap.ui.layout"
  xmlns:f="sap.ui.layout.form"
  xmlns:mvc="sap.ui.core.mvc"
  xmlns:ce="sap.ui.codeeditor"
  xmlns:tokenListItem="sap.cdw.components.tokenListItem.control"
  xmlns:cd="http://schemas.sap.com/sapui5/extension/sap.ui.core.CustomData/1"
  xmlns:cb="sap.cdw.components.reuse.control.circuitbreaker"
  xmlns:dos="sap.cdw.components.reuse.control.dwcobjectstatus"
  controllerName="sap.cdw.components.taskchainmodeler.properties.NodeProperties"
  class="propertyPanel--content"
>
  <VBox
    visible="true"
    class="entityVBoxContainer calcVBoxContainer"
  >
    <VBox class="NodePropertiesContainer propertyPanel--scrollable">
      <Panel
        id="nodePropertyPanel"
        class="generalPanel tc-generalPanel"
        expandable="true"
        expanded="true"
        headerText="{i18n>@lblGeneral}"
      >
        <headerToolbar>
          <OverflowToolbar>
            <Title
              level="H3"
              text="{i18n>@lblGeneral}"
            />
          </OverflowToolbar>
        </headerToolbar>
        <f:SimpleForm
          editable="false"
          id="tcNodeForm"
          class="taskchainFormNode"
          layout="ResponsiveGridLayout"
          labelSpanXL="3"
          labelSpanL="3"
          labelSpanM="3"
          labelSpanS="12"
          adjustLabelSpan="true"
          emptySpanXL="4"
          emptySpanL="4"
          emptySpanM="4"
          emptySpanS="0"
          columnsXL="1"
          columnsL="1"
          columnsM="1"
          singleContainerFullSize="false"
        >
          <f:content>
            <Label
              text="{i18n>@lblBusinessName}"
              visible="{= (${galileiModel>/isBWProcessChain} === true || ${galileiModel>/isSQLScriptProcedure} === true || ${galileiModel>/isNotificationTask} === true)? false : true}"
            />
            <Input
              id="businessName"
              class="inputPadding"
              value="{/businessName}"
              enabled="false"
              editable="false"
              visible="{= (${galileiModel>/isBWProcessChain} === true || ${galileiModel>/isSQLScriptProcedure} === true || ${galileiModel>/isNotificationTask} === true)? false : true}"
            />
            <Label text="{i18n>@lblTechnicalName}" />
            <Input
              id="technicalName"
              class="inputPadding"
              value="{/technicalName}"
              enabled="false"
              editable="false"
              visible="{= (${galileiModel>/isNotificationTask} === true)? false : true}"
            />
            <!-- Specific for Notification Task as we have to do unique value validation-->
            <Input
              id="technicalNameNotification"
              class="inputPadding"
              liveChange="onTechnicalNameChange"
              change="onTechnicalNameSubmit"
              value="{
                  path: 'galileiModel>/displayName',
                  mode: 'OneWay'
                  }"
              maxLength="1000"
              editable="true"
              enabled="{= ${workbenchEnv>/isVersioningReadOnlyMode} ? false : true}"
              valueState="{galileiModel>/technicalNameValueState}"
              visible="{= ${featureflags>/DWCO_TASK_FRAMEWORK_NOTIFICATION_TASK} === true &amp;&amp; ${galileiModel>/isNotificationTask} === true}"
            />
            <!-- Space Name if object is in different space -->
            <Label
              text="{i18n>@lblSpace}"
              visible="{= ( ${featureflags>/DWCO_TASK_FRAMEWORK_SHARE_TASK_CHAINS} === true &amp;&amp; ${/isCrossSpace} === true &amp;&amp; ${galileiModel>/isTaskChain} === true) ? true : false}"
            />
            <Input
              id="spaceOrContextName"
              class="inputPadding"
              editable="false"
              enabled="false"
              visible="{= (${featureflags>/DWCO_TASK_FRAMEWORK_SHARE_TASK_CHAINS} === true &amp;&amp; ${/isCrossSpace} &amp;&amp; ${galileiModel>/isTaskChain} === true) ? true: false}"
              value="{= ${/crossSpaceName}}"
            />
            <Label
              text="{i18n>@lblDescription}"
              visible="{= ${galileiModel>/isBWProcessChain} === true ? true : false}"
            />
            <TextArea
              id="nrDescription"
              class="inputPadding"
              value="{/description}"
              growing="true"
              enabled="false"
              editable="false"
              visible="{= ${galileiModel>/isBWProcessChain} === true ? true : false}"
            />
            <Label
              text="{i18n>@lblConnectionName}"
              visible="{= ${/showConnectionName} === true ? true : false}"
            />
            <Text
              id="tbConnectionName"
              text="{/connectionId}"
              class="nodeText inputPadding"
              visible="{= ${/showConnectionName} === true ? true : false}"
            />
            <!-- Type -->
            <Label text="{i18n>@lblType}" />
            <Text
              id="typeText"
              class="inputPadding"
              text="{path:'galileiModel>/', formatter:'.technicalTypeValueFormatter'}"
            />
            <!-- Storage Type -->
            <Label
              text="{i18n>@lblStorageType}"
              visible="{= (${path:'galileiModel>/', formatter:'.getHDLFStorage'} &amp;&amp; ${featureflags>/DWCO_LOCAL_TABLE_FILES_MONITOR} === true )? true : false}"
            />
            <Text
              id="storageTypeText"
              class="inputPadding"
              text="{path:'galileiModel>/', formatter:'.hdflStorageValueFormatter'}"
              visible="{= (${path:'galileiModel>/', formatter:'.getHDLFStorage'} &amp;&amp; ${featureflags>/DWCO_LOCAL_TABLE_FILES_MONITOR} === true)? true : false}"
            />
            <!-- Activity -->
            <Label text="{i18n>@lblActivity}" />
            <HBox>
              <RadioButtonGroup
                id="activityRadioButtonGroup"
                columns="2"
                visible="{= ${/numberOfActivities} > 1 ? true : false}"
                selectedIndex="{= ${galileiModel>/selectedActivityIndex}}"
                select="onChangeActivity"
                buttons="{ path: '/activity', templateShareable: false }"
                class="groupRadioButtonPadding"
                enabled="{= ${workbenchEnv>/isVersioningReadOnlyMode} ? false : true}"
              >
                <RadioButton
                  class="radioButtonRightPadding"
                  text="{text}"
                />
              </RadioButtonGroup>
              <Text
                id="activityText"
                class="inputPadding"
                visible="{= ${/numberOfActivities} > 1 ? false : true}"
                text="{path:'galileiModel>/', formatter:'.activityValueFormatter'}"
              />
            </HBox>
            <!-- Object status -->
            <Label
              text="{i18n_erd>@objectStatus}"
              visible="{= (${/showObjectStatus} === true  &amp;&amp; (${galileiModel>/isBWProcessChain} === false &amp;&amp; ${galileiModel>/isSQLScriptProcedure} === false &amp;&amp; ${galileiModel>/isNotificationTask} === false)) ? true : false}"
            />
            <HBox
              id="objectStatus"
              visible="{= (${/showObjectStatus} === true  &amp;&amp; (${galileiModel>/isBWProcessChain} === false &amp;&amp; ${galileiModel>/isSQLScriptProcedure} === false &amp;&amp; ${galileiModel>/isNotificationTask} === false))? true : false}"
            >
              <dos:DWCObjectStatus
                id="objectStatusText"
                class="sapUiTinyMarginEnd nodeText inputPadding"
                statusType="{galileiModel>/#objectStatus}"
              />
            </HBox>
          </f:content>
        </f:SimpleForm>
      </Panel>
      <Panel
        id="numberOfRetentionDaysSetting"
        class="generalPanel tc-generalPanel"
        expandable="true"
        expanded="true"
        headerText="{i18n>@lblSetting}"
        visible="{= (${path: 'galileiModel>/', formatter:'.getHDLFStorage'} === false) ? true : false}"
      >
        <headerToolbar>
          <OverflowToolbar>
            <Title
              level="H3"
              text="{i18n>@lblSetting}"
            />
          </OverflowToolbar>
        </headerToolbar>
        <f:SimpleForm
          editable="{header>/editable}"
          id="numberOfRetentionDays"
          class="taskchainFormNode"
          layout="ResponsiveGridLayout"
          labelSpanXL="4"
          labelSpanL="4"
          labelSpanM="4"
          labelSpanS="12"
          adjustLabelSpan="true"
          emptySpanXL="0"
          emptySpanL="4"
          emptySpanM="0"
          emptySpanS="0"
          columnsXL="4"
          columnsL="4"
          columnsM="4"
          singleContainerFullSize="false"
        >
          <f:content>
            <Label text="{i18n>txtDeltaTableDeletion}" />

            <StepInput
              id="inputNumberOfRetentionDays"
              min="0"
              max="999"
              width="100%"
              step="1"
              enabled="{= ${workbenchEnv>/isVersioningReadOnlyMode} ? false : true}"
              editable="true"
              description="{i18n>desDeltaTableDeletion}"
              fieldWidth="30%"
              textAlign="Right"
              value="{galileiModel>/numberOfRetentionDays}"
              valueState="{= ${galileiModel>/numberOfRetentionDays} > 999 ? 'Error' : 'None'}"
              change="onRetentionDaysChange"
              validationMode="LiveChange"
            />
          </f:content>
        </f:SimpleForm>
      </Panel>
      <Panel
        id="numberOfRetentionDaysSettingVaccum"
        class="generalPanel tc-generalPanel deletionSettingVaccum"
        expandable="true"
        expanded="true"
        headerText="{i18n>@lblSetting}"
        visible="{parts:[{path:'galileiModel>/'},{path:'galileiModel>/selectedActivityIndex'}], formatter:'.getDeleteVaccumVisible'}"
      >
        <headerToolbar>
          <OverflowToolbar>
            <Title
              level="H3"
              text="{i18n>@lblSetting}"
            />
          </OverflowToolbar>
        </headerToolbar>

        <!-- For LTF table-->
        <VBox visible="{= ${featureflags>/DWCO_LOCAL_TABLE_FILES_VACUUM} ? true : false}">
          <!--logical deletion LTF-->
          <!--RadioButton id="deleteAllLogicalLTFRB" -->
    <!-- For LTF table-->
        <!-- <VBox> -->
      <!--logical deletion LTF-->
      <!--RadioButton id="deleteAllLogicalLTFRB"
      text="{i18n_erd>deleteLogical}"
      class="radioButtonRightPadding"
      selected="{= !${galileiModel>/retentionOptionSelected}}"
      select="onLogicalLTFSelected"
      />
      <Text class="sapUiMediumMarginBegin sapUiSmallMarginBottom"
      text="{i18n_erd>deleteLogicalLTFSubText}">
      </Text-->
          <!--Housekeeping Delete LTF-->
          <!--RadioButton id="deleteAllRetentionLTFRB"
      text="{i18n_erd>deleteHouseKeepingLTF}"
      class="radioButtonRightPadding longTextRB"
      select="onLogicalLTFRetentionSelected"
      selected="{= ${galileiModel>/retentionOptionSelected}}"/-->
          <Label
            text="{i18n_erd>deleteHouseKeepingLTF}"
            class="sapUiMediumMarginBegin longTextRB"
          />
          <StepInput
            id="stepInputIdLTF"
            min="7"
            max="999"
            width="8rem"
            step="1"
            enabled="{= ${workbenchEnv>/isVersioningReadOnlyMode} ? false : true}"
            editable="true"
            fieldWidth="30%"
            textAlign="Right"
            value="{galileiModel>/numberOfRetentionDays}"
            valueState="{= ${galileiModel>/numberOfRetentionDays} > 999 ? 'Error' : 'None'}"
            change="onRetentionDaysChangeVaccum"
            validationMode="LiveChange"
            class="sapUiMediumMarginBegin"
          />
          <Text
            class="sapUiMediumMarginBegin sapUiSmallMarginBottom sapUiTinyMarginTop"
            text="{i18n_erd>deleteHouseKeepingLTFSubText}"
          >
          </Text>

        </VBox>
      </Panel>
      <Panel
        id="ApacheSettings"
        class="generalPanel tc-generalPanel"
        expandable="true"
        expanded="true"
        headerText="{i18n>txtHeaderApacheSettings}"
        visible="{parts:[{path:'galileiModel>/'},{path:'galileiModel>/selectedActivityIndex'}], formatter:'.getApacheSettingsVisible'}"
      >
        <headerToolbar>
          <OverflowToolbar>
            <Title
              level="H3"
              text="{i18n>txtHeaderApacheSettings}"
            />
          </OverflowToolbar>
        </headerToolbar>
        <VBox>
          <RadioButton
            id="apacheSparkDefaultRB"
            select="onApacheDefaultOptionSelect"
            text="{i18n>txtUseDefault}"
            selected="{= ${galileiModel>/defaultApacheSelected}}"
          />

          <f:SimpleForm
            editable="false"
            class="taskchainFormSettings"
            layout="ResponsiveGridLayout"
            labelSpanXL="3"
            labelSpanL="3"
            labelSpanM="3"
            labelSpanS="4"
            adjustLabelSpan="true"
            emptySpanXL="4"
            emptySpanL="4"
            emptySpanM="4"
            emptySpanS="0"
            columnsXL="2"
            columnsL="2"
            columnsM="2"
            singleContainerFullSize="false"
          >
            <f:content>
              <Label
                text="{i18n>txtApplication}"
                class="apacheAppLabel"
              />
              <Input
                id="apacheSparkDefaultInput"
                value="{parts:[{path:'galileiModel>/'},{path:'ltfAppModel>/transformationAppSelected'},{path:'ltfAppModel>/mergeAppSelected'},{path:'ltfAppModel>/optimizeAppSelected'}], formatter:'.getApacheSettingsDefault'}"
                enabled="false"
              />
            </f:content>
          </f:SimpleForm>
          <RadioButton
            id="apacheSparkCustomRB"
            text="{i18n>txtNewSettings}"
            selected="{= !${galileiModel>/defaultApacheSelected}}"
            select="onApacheCustomOptionSelect"
          />
          <f:SimpleForm
            editable="false"
            class="taskchainFormSettings"
            layout="ResponsiveGridLayout"
            labelSpanXL="3"
            labelSpanL="3"
            labelSpanM="3"
            labelSpanS="4"
            adjustLabelSpan="true"
            emptySpanXL="4"
            emptySpanL="4"
            emptySpanM="4"
            emptySpanS="0"
            columnsXL="2"
            columnsL="2"
            columnsM="2"
            singleContainerFullSize="false"
          >
            <f:content>
              <Label
                text="{i18n>txtApplication}"
                class="apacheAppLabel"
              />
              <Select
                id="apacheSparkCustomInput"
                selectedKey="{parts:[{path:'galileiModel>/'}, {path:'galileiModel>/customApacheValueSelected'}], formatter:'.selectedKeyFormatter'}"
                items="{path:'ltfAppModel>/appData', templateShareable: false }"
                change=".apacheSparkCustomInputChange"
                enabled="{= !${galileiModel>/defaultApacheSelected}}"
                showSecondaryValues="true"
              >
                <core:ListItem
                  key="{ltfAppModel>index}"
                  text="{ltfAppModel>index}"
                  additionalText="{parts:[
                    {path: 'ltfAppModel>maxCore' },
                    {path: 'ltfAppModel>maxMemory'}
                    ],
                    formatter:'.appDetailsListFormatter'
                  }"
                >
                </core:ListItem>
              </Select>
            </f:content>
          </f:SimpleForm>
        </VBox>
      </Panel>
      <!-- INPUT PARAMETERS panel -->
      <Panel
        id="ParameterPanel"
        class="generalPanel tc-generalPanel"
        expandable="true"
        expanded="true"
        visible="{= ( ${featureflags>/DWCO_INFRA_TASKS_PROCEDURES} === true &amp;&amp; ${galileiModel>/isSQLScriptProcedure} === true ) }"
      >
        <headerToolbar>
          <Toolbar height="48px">
            <content>
              <Title
                id="ParameterPanelTitle"
                text="{i18n>lblInputParameters}"
              ></Title>
              <ToolbarSpacer />
              <Button
                id="editParametersButton"
                tooltip="{i18n>lblInputParameters}"
                icon="sap-icon://edit"
                press="onEditParameters"
                enabled="{= ${workbenchEnv>/isVersioningReadOnlyMode} ? false : true}"
              ></Button>
            </content>
          </Toolbar>
        </headerToolbar>
        <VBox
          class="propertyPanel--scrollable"
          visible="true"
          height="{path: 'galileiModel>/parameters', formatter:'.formatters.listHeight'}"
        >
          <ScrollContainer
            vertical="true"
            height="100%"
          >
            <List
              id="parametersList"
              growing="true"
              growingThreshold="50"
              rememberSelections="false"
              mode="MultiSelect"
              showSeparators="None"
              items="{galileiModel>/parameters}"
              noDataText="{i18n>@noParametersMsg}"
              selectionChange="onParameterSelectionChange"
            >
              <tokenListItem:TokenListItem
                showLeftIcon="false"
                leftIcon="sap-icon://vertical-grip"
                title="{galileiModel>displayName}"
                icon="{path:'galileiModel>primitiveDataType', formatter:'.dataTypeIconFormatter'}"
                showDescription="true"
                hideActionButtonsOnMouseOut="true"
                description="{i18n>ip_value}: {galileiModel>value}"
              >
                <tokenListItem:rightButton>
                </tokenListItem:rightButton>
                <tokenListItem:actionButtons>
                  <Button
                    id="infoButton"
                    icon="sap-icon://sac/info"
                    type="Transparent"
                    press="openInputParameterInfoPopover"
                  ></Button>
                </tokenListItem:actionButtons>
                <tokenListItem:customData>
                  <core:CustomData
                    key="objectId"
                    value="{header>objectId}"
                  />
                </tokenListItem:customData>
              </tokenListItem:TokenListItem>
            </List>
          </ScrollContainer>
        </VBox>
      </Panel>
      <!-- ZORDER COLUMNS Panel -->
      <Panel
        id="ZOrderColumnsPanel"
        class="generalPanel tc-generalPanel"
        expandable="true"
        expanded="true"
        visible="{= ( ${featureflags>/DWCO_LOCAL_TABLE_FILES_ZORDER} === true &amp;&amp; ${galileiModel>/isDeltaTable} === true &amp;&amp; ${galileiModel>/selectedActivityIndex}===1 ) }"
      >
        <headerToolbar>
          <Toolbar height="48px">
            <content>
              <Title
                id="ZOrderColumnsPanelTitle"
                text="{i18n>@txtZorderTitle}"
              />
              <ToolbarSpacer />
            </content>
          </Toolbar>
        </headerToolbar>

        <VBox
          class="propertyPanel--scrollable"
          visible="true"
          height="auto"
        >
          <ScrollContainer
            vertical="true"
            height="100%"
          >
            <List
              id="zOrderColumnsList"
              growing="true"
              growingThreshold="50"
              showSeparators="None"
              items="{path: 'galileiModel>/Zorder'}"
              noDataText="{i18n>@txtZorderNoColumn}"
              mode="None"
            >
              <tokenListItem:TokenListItem
                showLeftIcon="true"
                title="{galileiModel>name}"
                icon="{path: 'galileiModel>type', formatter: '.dataTypeIconFormatter'}"
                showDescription="false"
              >
                <tokenListItem:rightButton>
                  <Button
                    id="zOrderColumnPrimarykey"
                    icon="sap-icon://key"
                    type="Transparent"
                    tooltip="{i18n>@txtZorderColumnPrimaryKey}"
                    visible="{= ${galileiModel>keyPosition} === 1 }"
                  />
                </tokenListItem:rightButton>
              </tokenListItem:TokenListItem>
            </List>
          </ScrollContainer>
        </VBox>
      </Panel>
<!-- Notification Panel -->
      <Panel
        id="notificationTaskPanel"
        class="generalPanel tc-generalPanel"
        expandable="true"
        expanded="true"
        visible="{= ( ${featureflags>/DWCO_TASK_FRAMEWORK_NOTIFICATION_TASK} === true &amp;&amp; ${galileiModel>/isNotificationTask} === true ) }"
      >
        <headerToolbar>
          <OverflowToolbar height="40px">
            <Title
              level="H3"
              text="{i18n>@emailNotifications}"
            />
          </OverflowToolbar>
        </headerToolbar>
        <f:SimpleForm
          id="taskchainEmailNotificationSettings"
          class="taskchainForm"
          layout="ResponsiveGridLayout"
          labelSpanXL="3"
          labelSpanL="3"
          labelSpanM="3"
          labelSpanS="12"
          adjustLabelSpan="true"
          emptySpanXL="4"
          emptySpanL="4"
          emptySpanM="4"
          emptySpanS="0"
          columnsXL="1"
          columnsL="1"
          columnsM="1"
          singleContainerFullSize="false"
        >
          <f:content>
            <VBox>
              <VBox class="sapUiSmallMarginBottom">
                <HBox>
                  <Label text="{i18n>@recipientEmailAddr}" />
                  <core:Icon
                    id="recipientInfo"
                    class="sapUiTinyMarginBegin"
                    src="sap-icon://message-information"
                    visible="true"
                    press="onRecipientInfoPress"
                  />
                </HBox>
                <MultiInput
                  id="recipientEmailAddressInput"
                  tooltip="{= ${galileiModel>/isRecipientInputEnabled} ? '' : ${i18n>@emailInputDisabledText} }"
                  editable="false"
                >
                </MultiInput>
              </VBox>
              <VBox class="sapUiSmallMarginBottom">
                <Label text="{i18n>@emailSubject}" />
                <Input
                  id="emailSubject"
                  value="{galileiModel>/notificationSubject}"
                  change="onEmailSubjectChange"
                  enabled="{workbenchEnv>/canUpdateModel}"
                  width="100%"
                />
              </VBox>
              <VBox class="sapUiSmallMarginBottom">
                <HBox
                  alignItems="Center"
                  justifyContent="SpaceBetween"
                >
                  <Label text="{i18n>@emailMessage}" />
                  <Button
                    id="emailMessageInfo"
                    type="Transparent"
                    text="{i18n>@placeholderInfo}"
                    press="onEmailMessageInfoClick"
                  ></Button>
                </HBox>
                <VBox
                  width="100%"
                  height="100%"
                >
                  <ce:CodeEditor
                    id="emailMessageBody"
                    class="emailMessageEditorCls"
                    type="text"
                    lineNumbers="false"
                    maxLines="12"
                    value="{galileiModel>/notificationMessage}"
                    change="onEmailMessageChange"
                    editable="{workbenchEnv>/canUpdateModel}"
                  />
                  <MessageStrip
                    text="{i18n>@emailMsgWarning}"
                    type="Warning"
                    showIcon="true"
                    visible="{= ${galileiModel>/notificationMessage} === '' ? true : false}"
                    showCloseButton="true"
                  >
                  </MessageStrip>
                </VBox>
                <HBox
                  width="100%"
                  direction="RowReverse"
                >
                  <Button
                    id="resetMsgBody"
                    type="Transparent"
                    text="{i18n>@txtReset}"
                    press="onResetEmailMsg"
                    width="100%"
                    enabled="{workbenchEnv>/canUpdateModel}"
                  ></Button>
                </HBox>
              </VBox>
            </VBox>
          </f:content>
        </f:SimpleForm>
      </Panel>
    </VBox>

  </VBox>
</mvc:View>
