<mvc:View
  xmlns:core="sap.ui.core"
  xmlns="sap.m"
  xmlns:l="sap.ui.layout"
  xmlns:f="sap.ui.layout.form"
  xmlns:mvc="sap.ui.core.mvc"
  xmlns:code="sap.ui.codeeditor"
  xmlns:tokenListItem="sap.cdw.components.tokenListItem.control"
  xmlns:cb="sap.cdw.components.reuse.control.circuitbreaker"
  controllerName="sap.cdw.components.taskchainmodeler.properties.RESTTaskProperties"
  class="propertyPanel--content"
>
  <ScrollContainer
    id="restTaskPropertiesScrollContainer"
    class="calcTableScroller"
    height="100%"
    vertical="true"
  >
    <VBox class="RESTTaslPropertiesContainer sapUiSmallMarginTop">
      <Panel
        id="restTaskPropertyPanel"
        class="generalPanel tc-generalPanel"
        expandable="true"
        expanded="true"
        headerText="{i18n>@lblGeneral}"
      >
        <headerToolbar>
          <ToolbarSpacer />
          <OverflowToolbar>
            <Title
              level="H3"
              text="{i18n>@lblGeneral}"
            />
          </OverflowToolbar>
        </headerToolbar>
        <f:SimpleForm
          editable="false"
          id="tcRestTaskForm"
          class="taskchainFormNode"
          layout="ResponsiveGridLayout"
          labelSpanXL="3"
          labelSpanL="3"
          labelSpanM="3"
          labelSpanS="12"
          adjustLabelSpan="true"
          emptySpanXL="4"
          emptySpanL="4"
          emptySpanM="4"
          emptySpanS="0"
          columnsXL="1"
          columnsL="1"
          columnsM="1"
          singleContainerFullSize="false"
        >
          <f:content>
            <Label text="{i18n>@lblTechnicalName}" />
            <Input
              id="technicalName"
              liveChange="onTechnicalNameChange"
              value="{
                path: 'galileiModel>/displayName',
                mode: 'OneWay'
              }"
              maxLength="1000"
              editable="true"
              enabled="{= ${workbenchEnv>/isVersioningReadOnlyMode} ? false : true}"
              valueState="{galileiModel>/technicalNameValueState}"
              change="onTechnicalNameSubmit"
            />
            <!-- Type -->
            <Label text="{i18n>@lblType}" />
            <Text
              id="typeText"
              class="inputPadding"
              text="{path:'galileiModel>/', formatter:'.technicalTypeValueFormatter'}"
            />
            <!-- Activity -->
            <Label text="{i18n>@lblActivity}" />
            <Text
              id="activityText"
              class="inputPadding"
              text="{path:'galileiModel>/', formatter:'.activityValueFormatter'}"
            />
          </f:content>
        </f:SimpleForm>
      </Panel>
      <!-- Connection Details Panel -->
      <Panel
        id="connectionPanel"
        class="generalPanel tc-generalPanel"
        expandable="true"
        expanded="true"
        headerText="{i18n>@lblConnectionName}"
      >
        <Label
          text="{i18n>@lblHTTPConnection}:"
          labelFor="connectionList"
        />
        <Select
          id="connectionList"
          required="true"
          forceSelection="false"
          items="{path: '/connectionList'}"
          width="92%"
          selectedKey="{galileiModel>/connectionName}"
          change="urlFormatter"
          valueState="{galileiModel>/connectionNameValueState}"
          valueStateText="{i18n>selectConnection}"
          enabled="{= ${workbenchEnv>/isVersioningReadOnlyMode} ? false : true}"
        >


          <core:Item
            key="{technicalName}"
            text="{businessName}"
          />
        </Select>
        <core:Icon
          id="connectionErrorBtn"
          class="sapUiSmallMarginBegin sapUiTinyMarginTop"
          src="sap-icon://message-error"
          color="red"
          visible="{path: '/connectionList', formatter: '.connectionErrorBtnVisiblity'}"
          press="onConnectionErrorPress"
        />
      </Panel>
      <!-- API Settings Panel -->
      <Panel
        id="APISettingPanel"
        class="generalPanel tc-generalPanel"
        expandable="true"
        expanded="true"
        headerText="{i18n>@lblAPISettings}"
      >
        <Label text="{i18n>@header}:" />
        <!-- Default Header Panel -->
        <Panel
          id="defaultHeader"
          expandable="true"
          expanded="false"
          headerText="{i18n>@lblDefaultHeader}"
          class="panelBgColor"
        >
          <headerToolbar>
            <ToolbarSpacer />
            <OverflowToolbar>
              <Title
                text="{i18n>@lblDefaultHeader}"
                class="panelHeaderText"
              />
            </OverflowToolbar>
          </headerToolbar>

          <VBox id="defaultHeaderKeyValue">
            <List
              items="{path: '/defaultHeaders'}"
              backgroundDesign="Transparent"
            >
              <CustomListItem>
                <Label
                  text="{i18n>@lblKey}:"
                  class="sapUiTinyMarginTop"
                />
                <Input
                  value="{key}"
                  editable="false"
                />

                <Label
                  text="{i18n>@lblValue}:"
                  class="sapUiTinyMarginTop"
                />
                <Input
                  value="{value}"
                  editable="false"
                />
              </CustomListItem>
            </List>
          </VBox>

        </Panel>
        <!-- Container for Dynamically Added Panels -->
        <VBox id="fieldsContainer"/>

        <!-- Add Header Field Button -->
        <Button
          id="addFieldBtn"
          text="{i18n>btnAddField}"
          press="onAddFieldPress"
          class="sapUiTinyMarginTop"
          enabled="{= ${workbenchEnv>/isVersioningReadOnlyMode} ? false : true}"
        />
      </Panel>
      <!-- Invoke API Panel -->
      <Panel
        id="invokePanel"
        class="generalPanel tc-generalPanel"
        expandable="true"
        expanded="true"
        headerText="{i18n>@lblInvoke}"
      >
        <Label text="{i18n>@lblMethod}:" />
        <VBox>
          <RadioButtonGroup
            id="method"
            columns="1"
            class="groupRadioButtonPadding"
            selectedIndex="{= ${galileiModel>/invokeAPI/request/method} === 'POST' ? 0 : 1}"
            select=".onRadioButtonChange"
            buttons="{ path: '/methodList', templateShareable: false }"
            enabled="{= ${workbenchEnv>/isVersioningReadOnlyMode} ? false : true}"
          >
            <RadioButton
              class="sapUiSmallMarginEnd"
              text="{name}"
            ></RadioButton>
          </RadioButtonGroup>
        </VBox>
        <Label
          text="{i18n>@lblUrl}:"
          required="true"
        />
        <Input
          id="url"
          required="true"
          value="{galileiModel>/selectedConnectionURL}"
          tooltip="{galileiModel>/selectedConnectionURL}"
          editable="false"
          enabled="false"
        />
        <Label
          text="{i18n>@lblAPIPath}:"
          class="sapUiTinyMarginTop"
          labelFor="apiPath"
        />
        <Input
          id="apiPath"
          required="true"
          placeholder="{i18n>apiPath}"
          editable="true"
          enabled="{= ${workbenchEnv>/isVersioningReadOnlyMode} ? false : true}"
          value="{galileiModel>/invokeAPI/request/apiPath}"
          tooltip="{galileiModel>/invokeAPI/request/apiPath}"
          type="Text"
          valueState="{galileiModel>/invokeAPI/request/apiPathValueState}"
          valueStateText="{i18n>apiPathRequired}"
          liveChange="onApiPathChange"
        />
        <HBox>
          <Label
            text="{i18n>@lblMode}"
            class="sapUiTinyMarginTop"
          />
          <core:Icon
            id="modeInfo"
            class="sapUiTinyMarginTop sapUiTinyMarginBegin"
            src="sap-icon://message-information"
            visible="true"
            press="onModeInfoPress"
          />
        </HBox>
        <VBox>
          <RadioButtonGroup
            id="mode"
            columns="1"
            class="groupRadioButtonPadding"
            selectedIndex="{= ${galileiModel>/invokeAPI/request/mode} === 'ASYNC' ? 1 : 0}"
            buttons="{ path: '/mode', templateShareable: false }"
            select=".onRadioButtonChange"
            enabled="{= ${workbenchEnv>/isVersioningReadOnlyMode} ? false : true}"
          >
            <RadioButton
              class="radioButtonRightPadding"
              text="{name}"
            />
          </RadioButtonGroup>
        </VBox>
        <HBox class="sapUiSmallMarginTop">
          <Switch
            id="CSRFToken"
            state="{galileiModel>/invokeAPI/request/csrfToken}"
            customTextOn=" "
            customTextOff=" "
            type="Default"
            change="onCSRFTokenChange"
            enabled="{= ${workbenchEnv>/isVersioningReadOnlyMode} ? false : true}"
          />
          <Label
            text="{i18n>@lblCSRFToken}"
            class="sapUiTinyMarginBegin sapUiTinyMarginTop"
          />
        </HBox>
        <VBox
          id="CSRFTokenVBox"
          visible="{galileiModel>/invokeAPI/request/csrfToken}"
          class="sapUiTinyMarginTop"
        >
          <HBox>
            <Label
              text="{i18n>@lblTokenURL}"
              class="sapUiTinyMarginTop"
            />
            <core:Icon
              id="csrfTokenInfo"
              class="sapUiTinyMarginTop sapUiTinyMarginBegin"
              src="sap-icon://message-information"
              visible="true"
              press="onCSRFTokenInfoPress"
            />
          </HBox>
          <Input
            id="CSRFtokenURL"
            placeholder="{i18n>csrfTokenURL}"
            editable="true"
            value="{galileiModel>/invokeAPI/request/csrfTokenUrl}"
            tooltip="{galileiModel>/invokeAPI/request/csrfTokenUrl}"
            valueState="{galileiModel>/invokeAPI/request/csrfTokenUrlValueState}"
            enabled="{= ${workbenchEnv>/isVersioningReadOnlyMode} ? false : true}"
            liveChange="onCSRFTokenValidation"
          />
        </VBox>
        <VBox id="invokeRequestBody">
          <Toolbar
            id="iconTabHeader"
            class="sapUiTinyMarginTop"
          >

            <Label text="{i18n>@lblRequestBody}" />
            <ToolbarSpacer />
            <HBox>
              <core:Icon
                id="requestBodyError"
                class="sapUiTinyMarginTop sapUiTinyMarginEnd"
                src="sap-icon://high-priority"
                color="Critical"
                visible="false"
                press="onErrorIconPress"
              />
              <Button
                id="btnFormat"
                text="{i18n>@lblFormat}"
                type="Transparent"
                press="onFormat"
              >
              </Button>
              <Button
                id="openEditorDialog"
                tooltip="{i18n>codeEditorTooltip}"
                icon="sap-icon://SAP-icons-TNT/subject"
                press="onEditorDialogPress"
                type="Transparent"
                enabled="{= ${workbenchEnv>/isVersioningReadOnlyMode} ? false : true}"
              ></Button>
            </HBox>
          </Toolbar>

          <code:CodeEditor
            type="json"
            id="requestBody"
            class="ExpressionBuilderCodeEditor"
            colorTheme="textmate"
            width="100%"
            height="250px"
            lineNumbers="true"
            value="{galileiModel>/invokeAPI/request/body}"
            busyIndicatorDelay='0'
            liveChange="onRequestBodyChange"
            change=".onFormat"
            editable="{= ${workbenchEnv>/isVersioningReadOnlyMode} ? false : true}"
          />
        </VBox>
        <!-- Response -->

        <Label
          text="{i18n>@lblResponse}:"
          class="sapUiTinyMarginTop"
        />
        <VBox>
          <RadioButtonGroup
            id="responseType"
            class="groupRadioButtonPadding"
            selectedIndex="{= ${galileiModel>/invokeAPI/response/from} === 'BODY' ? 1 : 0 }"
            select=".onRadioButtonChange"
            enabled="{= ${workbenchEnv>/isVersioningReadOnlyMode} ? false : true}"
          >
            <RadioButton
              id="locationHeader"
              class="radioButtonRightPadding"
              text="{= ${galileiModel>/invokeAPI/request/mode} === 'ASYNC' ? ${i18n>locationHeader} : ${i18n>statusCode}}"
            ></RadioButton>
            <RadioButton
              id="responseBody"
              class="radioButtonRightPadding"
              text="{i18n>responseBody}"
            ></RadioButton>
          </RadioButtonGroup>
        </VBox>
        <VBox
          id="responseIdVBox"
          visible="{= ${galileiModel>/invokeAPI/response/from} === 'BODY' &amp;&amp; ${galileiModel>/invokeAPI/request/mode} === 'ASYNC' ? true : false }"
        >
          <Label
            text="{i18n>@lblId}:"
            class="sapUiTinyMarginTop"
            labelFor="responseId"
          />
          <Input
            id="responseId"
            placeholder="{i18n>idPlaceholder}"
            value="{galileiModel>/invokeAPI/response/jobIdJsonPath}"
            editable="true"
            enabled="{= ${workbenchEnv>/isVersioningReadOnlyMode} ? false : true}"
            required="true"
            valueState="{galileiModel>/invokeAPI/response/jobIDPathVS}"
            valueStateText="{i18n>jsonPathRequired}"
            liveChange="onJSONPathChange"
          />
        </VBox>
        <VBox
          id="indicator"
          visible="{= ${galileiModel>/invokeAPI/response/from} === 'BODY' &amp;&amp; ${galileiModel>/invokeAPI/request/mode} === 'SYNC' ? true : false }"
        >
          <Label
            text="{i18n>@lblSuccessIndicator}:"
            class="sapUiTinyMarginTop"
            required="true"
          />
          <HBox>
            <Input
              id="successVariable"
              editable="true"
              enabled="{= ${workbenchEnv>/isVersioningReadOnlyMode} ? false : true}"
              value="{galileiModel>/invokeAPI/response/successIndicatorPath}"
              placeholder="{i18n>idPlaceholder}"
              required="true"
              valueState="{galileiModel>/invokeAPI/response/successIndicatorPathVS}"
              valueStateText="{i18n>jsonPathRequired}"
              liveChange="onJSONPathChange"
            />
            <Select
              id="successCondition"
              forceSelection="false"
              selectedKey="{galileiModel>/invokeAPI/response/successIndicatorCondition}"
              items="{path: '/indicatorCondition'}"
              class="sapUiSmallMarginBegin sapUiSmallMarginEnd"
              enabled="{= ${workbenchEnv>/isVersioningReadOnlyMode} ? false : true}"
            >
              <core:Item
                key="{condition}"
                text="{statement}"
              />
            </Select>
            <Input
              id="successValue"
              editable="true"
              enabled="{= ${workbenchEnv>/isVersioningReadOnlyMode} ? false : true}"
              value="{galileiModel>/invokeAPI/response/successIndicatorValue}"
              placeholder="{i18n>indicatorValue}"
              required="true"
              valueState="{galileiModel>/invokeAPI/response/successIndicatorValueVS}"
              valueStateText="{i18n>valueRequired}"
              liveChange="onIndicatorValueChange"
            />
          </HBox>
          <Label
            text="{i18n>@lblErrorIndicator}:"
            class="sapUiTinyMarginTop"
            required="true"
          />
          <HBox>
            <Input
              id="errorVariable"
              editable="true"
              enabled="{= ${workbenchEnv>/isVersioningReadOnlyMode} ? false : true}"
              value="{galileiModel>/invokeAPI/response/errorIndicatorPath}"
              placeholder="{i18n>idPlaceholder}"
              required="true"
              valueState="{galileiModel>/invokeAPI/response/errorIndicatorPathVS}"
              valueStateText="{i18n>jsonPathRequired}"
              liveChange="onJSONPathChange"
            />
            <Select
              id="errorCondition"
              forceSelection="false"
              selectedKey="{galileiModel>/invokeAPI/response/errorIndicatorCondition}"
              items="{path: '/indicatorCondition'}"
              class="sapUiSmallMarginBegin sapUiSmallMarginEnd"
              enabled="{= ${workbenchEnv>/isVersioningReadOnlyMode} ? false : true}"
            >
              <core:Item
                key="{condition}"
                text="{statement}"
              />
            </Select>
            <Input
              id="errorValue"
              editable="true"
              enabled="{= ${workbenchEnv>/isVersioningReadOnlyMode} ? false : true}"
              value="{galileiModel>/invokeAPI/response/errorIndicatorValue}"
              placeholder="{i18n>indicatorValue}"
              required="true"
              valueState="{galileiModel>/invokeAPI/response/errorIndicatorValueVS}"
              valueStateText="{i18n>valueRequired}"
              liveChange="onIndicatorValueChange"
            />
          </HBox>
          <Label
            text="{i18n>@lblErrorReason}:"
            class="sapUiTinyMarginTop"
          />
          <Input
            id="errorReason"
            editable="true"
            enabled="{= ${workbenchEnv>/isVersioningReadOnlyMode} ? false : true}"
            value="{galileiModel>/invokeAPI/response/errorReasonPath}"
            valueState="{galileiModel>/invokeAPI/response/errorReasonPathVS}"
            placeholder="{i18n>idPlaceholder}"
            liveChange="onJSONPathChange"
          />
        </VBox>

      </Panel>
      <!-- Status API Panel -->
      <Panel
        id="statusPanel"
        class="generalPanel tc-generalPanel"
        expandable="true"
        expanded="true"
        headerText="{i18n>@lblStatus}"
        visible="{= ${galileiModel>/invokeAPI/request/mode} === 'ASYNC' ? true : false}"
      >
        <Label text="{i18n>@lblMethod}:" />
        <VBox>
          <RadioButtonGroup
            id="statusMethod"
            columns="1"
            class="groupRadioButtonPadding sapUITinyMarginEnd"
            selectedIndex="{= ${galileiModel>/statusAPI/request/method} === 'GET' ? 0 : 1}"
            buttons="{ path: '/statusMethodList', templateShareable: false }"
            select="onRadioButtonChange"
            enabled="{= ${workbenchEnv>/isVersioningReadOnlyMode} ? false : true}"
          >
            <RadioButton
              class="radioButtonRightPadding"
              text="{name}"
            ></RadioButton>
          </RadioButtonGroup>
        </VBox>
        <Label
          text="{i18n>@lblUrl}:"
          labelFor="statusUrl"
        />
        <Input
          id="statusUrl"
          required="true"
          value="{= (${galileiModel>/invokeAPI/response/from} === 'BODY' &amp;&amp; ${galileiModel>/invokeAPI/request/mode} === 'ASYNC') ? ${galileiModel>/selectedConnectionURL} : ''}"
          tooltip="{= (${galileiModel>/invokeAPI/response/from} === 'BODY' &amp;&amp; ${galileiModel>/invokeAPI/request/mode} === 'ASYNC') ? ${galileiModel>/selectedConnectionURL} : ''}"
          editable="false"
          enabled="false"
        />
        <Label
          id="statusApiPathLabel"
          text="{i18n>@lblAPIPath}:"
          class="sapUiTinyMarginTop"
          labelFor="statusApiPath"
        />
        <Input
          id="statusApiPath"
          required="true"
          placeholder="{i18n>statusAPIPath}"
          editable="true"
          enabled="{= ${workbenchEnv>/isVersioningReadOnlyMode} ? false : true}"
          value="{galileiModel>/statusAPI/request/apiPath}"
          tooltip="{galileiModel>/statusAPI/request/apiPath}"
          type="Text"
          valueState="{galileiModel>/statusAPI/request/apiPathValueState}"
          valueStateText="{i18n>apiPathRequired}"
          liveChange="onStatusApiPathChange"
        />
        <VBox id="statusRequestBodyCode">
          <Toolbar
            id="statusIconTabHeader"
            class="sapUiTinyMarginTop"
          >

            <Label text="{i18n>@lblRequestBody}" />
            <ToolbarSpacer />
            <HBox>
              <core:Icon
                id="statusRequestBodyError"
                class="sapUiTinyMarginTop sapUiTinyMarginEnd"
                src="sap-icon://high-priority"
                color="Critical"
                visible="false"
                press="onErrorIconPress"
              />
              <Button
                id="statusBtnFormat"
                text="{i18n>@lblFormat}"
                type="Transparent"
                enabled="{=  ${galileiModel>/statusAPI/request/method} === 'GET' ? false : true}"
              >
              </Button>
              <Button
                id="openStatusEditorDialog"
                tooltip="{i18n>codeEditorTooltip}"
                icon="sap-icon://SAP-icons-TNT/subject"
                press="onEditorDialogPress"
                type="Transparent"
                enabled="{= (${workbenchEnv>/isVersioningReadOnlyMode} || ${galileiModel>/statusAPI/request/method} === 'GET') ? false : true}"
              ></Button>
            </HBox>
          </Toolbar>

          <code:CodeEditor
            type="json"
            id="statusRequestBody"
            editable="{= ${workbenchEnv>/isVersioningReadOnlyMode} ? false : true}"
            class="ExpressionBuilderCodeEditor"
            colorTheme="textmate"
            width="100%"
            height="250px"
            lineNumbers="true"
            value="{galileiModel>/statusAPI/request/body}"
            busyIndicatorDelay='0'
            liveChange="onRequestBodyChange"
            change=".onFormat"
          />
        </VBox>
        <!-- Response -->
        <Label
          text="{i18n>@lblResponse}:"
          class="sapUiTinyMarginTop"
        />
        <VBox>
          <RadioButtonGroup
            id="statusResponseType"
            class="groupRadioButtonPadding"
            selectedIndex="{= ${galileiModel>/statusAPI/response/from} === 'BODY' ? 1 : 0 }"
            select=".onRadioButtonChange"
            enabled="{= ${workbenchEnv>/isVersioningReadOnlyMode} ? false : true}"
          >
            <RadioButton
              id="statusResponseCode"
              class="radioButtonRightPadding"
              text="{i18n>statusCode}"
            ></RadioButton>
            <RadioButton
              id="statusResponseBody"
              class="radioButtonRightPadding"
              text="{i18n>responseBody}"
            ></RadioButton>
          </RadioButtonGroup>
        </VBox>
        <VBox
          id="statusIndicator"
          visible="{= ${galileiModel>/statusAPI/response/from} === 'BODY' ? true : false }"
        >
          <Label
            text="{i18n>@lblSuccessIndicator}:"
            class="sapUiTinyMarginTop"
            required="true"
          />
          <HBox>
            <Input
              id="statusSuccessVariable"
              editable="true"
              enabled="{= ${workbenchEnv>/isVersioningReadOnlyMode} ? false : true}"
              value="{galileiModel>/statusAPI/response/successIndicatorPath}"
              placeholder="{i18n>idPlaceholder}"
              required="true"
              valueState="{galileiModel>/statusAPI/response/successIndicatorPathVS}"
              valueStateText="{i18n>jsonPathRequired}"
              liveChange="onJSONPathChange"
            />
            <Select
              id="statusSuccessCondition"
              forceSelection="false"
              selectedKey="{galileiModel>/statusAPI/response/successIndicatorCondition}"
              items="{path: '/indicatorCondition'}"
              class="sapUiSmallMarginBegin sapUiSmallMarginEnd"
              enabled="{= ${workbenchEnv>/isVersioningReadOnlyMode} ? false : true}"
            >
              <core:Item
                key="{condition}"
                text="{statement}"
              />
            </Select>
            <Input
              id="statusSuccessValue"
              editable="true"
              value="{galileiModel>/statusAPI/response/successIndicatorValue}"
              enabled="{= ${workbenchEnv>/isVersioningReadOnlyMode} ? false : true}"
              required="true"
              placeholder="{i18n>indicatorValue}"
              valueState="{galileiModel>/statusAPI/response/successIndicatorValueVS}"
              valueStateText="{i18n>valueRequired}"
              liveChange="onIndicatorValueChange"
            />
          </HBox>
          <Label
            text="{i18n>@lblErrorIndicator}:"
            class="sapUiTinyMarginTop"
            required="true"
          />
          <HBox>
            <Input
              id="statusErrorVariable"
              editable="true"
              enabled="{= ${workbenchEnv>/isVersioningReadOnlyMode} ? false : true}"
              value="{galileiModel>/statusAPI/response/errorIndicatorPath}"
              placeholder="{i18n>idPlaceholder}"
              required="true"
              valueState="{galileiModel>/statusAPI/response/errorIndicatorPathVS}"
              valueStateText="{i18n>jsonPathRequired}"
              liveChange="onJSONPathChange"
            />
            <Select
              id="statusErrorCondition"
              forceSelection="false"
              selectedKey="{galileiModel>/statusAPI/response/errorIndicatorCondition}"
              items="{path: '/indicatorCondition'}"
              class="sapUiSmallMarginBegin sapUiSmallMarginEnd"
              enabled="{= ${workbenchEnv>/isVersioningReadOnlyMode} ? false : true}"
            >
              <core:Item
                key="{condition}"
                text="{statement}"
              />
            </Select>
            <Input
              id="statusErrorValue"
              editable="true"
              enabled="{= ${workbenchEnv>/isVersioningReadOnlyMode} ? false : true}"
              value="{galileiModel>/statusAPI/response/errorIndicatorValue}"
              placeholder="{i18n>indicatorValue}"
              required="true"
              valueState="{galileiModel>/statusAPI/response/errorIndicatorValueVS}"
              valueStateText="{i18n>valueRequired}"
              liveChange="onIndicatorValueChange"
            />
          </HBox>
          <Label
            text="{i18n>@lblErrorReason}:"
            class="sapUiTinyMarginTop"
          />
          <Input
            id="statusErrorReason"
            editable="true"
            enabled="{= ${workbenchEnv>/isVersioningReadOnlyMode} ? false : true}"
            value="{galileiModel>/statusAPI/response/errorReasonPath}"
            placeholder="{i18n>idPlaceholder}"
            liveChange="onJSONPathChange"
          />
        </VBox>
      </Panel>
      <Panel
        id="testRunPanel"
        class="generalPanel tc-generalPanel"
        expandable="true"
        expanded="true"
      >
        <headerToolbar>
          <OverflowToolbar>
            <content>
              <Title text="{i18n>@lblApiTestRun}" />
              <ToolbarSpacer />
              <Button
                id="statusRefreshBtn"
                icon="sap-icon://refresh"
                enabled="{= ${circuitbreaker>/DataHANA} === 'Green'}"
                press="refreshApiStatus"
              />
              <Button
                id="restTaskMonitorBtn"
                icon="sap-icon://sac/simulate"
                tooltip="{i18n>@navToMonitoring}"
                press="onPressViewInRestTaskMonitor"
                type="Transparent"
                visible="{
                  parts:[
                  {path:'galileiModel>/executionStatus/status'},
                  {path:'galileiModel>/executionErrorDetails'}
                  ],
                  formatter: '.showNavIconFormatter'}"
              ></Button>
            </content>
          </OverflowToolbar>
        </headerToolbar>
        <f:SimpleForm
          id="runTestPanel"
          class="taskchainForm"
          layout="ResponsiveGridLayout"
          labelSpanXL="4"
          labelSpanL="4"
          labelSpanM="4"
          labelSpanS="12"
          adjustLabelSpan="true"
          emptySpanXL="0"
          emptySpanL="4"
          emptySpanM="0"
          emptySpanS="0"
          columnsXL="4"
          columnsL="4"
          columnsM="4"
          singleContainerFullSize="false"
        >
          <f:content>
            <Label
              text="{i18n>@lblLastExecuted}"
              visible="{
                  parts:[
                  {path:'galileiModel>/executionStatus/status'},
                  {path:'galileiModel>/executionErrorDetails'}
                  ],
                  formatter: '.showNavIconFormatter'}"
            />
            <Text
              text="{galileiModel>/executionStatus/formattedStarted}"
              class="modelPropertiesRunStatus"
              visible="{
                  parts:[
                  {path:'galileiModel>/executionStatus/status'},
                  {path:'galileiModel>/executionErrorDetails'}
                  ],
                   formatter: '.showNavIconFormatter'}"
            />

            <Label
              text="{i18n>@status_lbl}"
              visible="{= ${galileiModel>/executionErrorDetails} ? false : true }"
            >
            </Label>
            <ObjectStatus
              id="runStatus"
              visible="{= ${galileiModel>/executionErrorDetails} ? false : true }"
              text="{path: 'galileiModel>/executionStatus/status', formatter: '.runStatusFormatter'}"
              state="{path: 'galileiModel>/executionStatus/status', formatter: '.runStatusColorFormatter'}"
              class="modelPropertiesRunStatus"
            >
              <layoutData>
                <l:GridData span="XL3 L3 M3 S3" />
              </layoutData>
            </ObjectStatus>
            <Link
              id="viewFailedDetails"
              visible="{path: 'galileiModel>/executionStatus/status', formatter:'.showViewDetailsLinkFormatter'}"
              text="{i18n>@viewDetails}"
              press="onViewDetailPress"
            >
            </Link>
            <HBox>
              <Button
                id="runTest"
                text="{i18n>@lblTestRun}"
                press="runApiTask"
                enabled="{= ${workbenchEnv>/isVersioningReadOnlyMode} ? false : true}"
              />
            </HBox>
          </f:content>
        </f:SimpleForm>
      </Panel>

    </VBox>
  </ScrollContainer>
</mvc:View>
