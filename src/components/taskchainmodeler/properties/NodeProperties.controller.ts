/**
 * Copyright 2020 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { State } from "@sap/dwc-circuit-breaker";
import { getAllUsers } from "../../../services/metadata";
import { isSparkSelectionVacuumEnabled } from "../../abstractbuilder/commonservices/FeatureFlagCheck";
import { smartExtend } from "../../basecomponent/controller/BaseController.controller";
import { NameUsage } from "../../commonui/utility/NameInputValidator";
import {
  dateFormatter,
  objectStatusIconFormatter,
  objectStatusTextFormatter,
  objectStatusTooltipFormatter,
} from "../../ermodeler/js/utility/sharedFunctions";
import { Format } from "../../reuse/utility/Format";
import { NamingHelper } from "../../reuse/utility/NamingHelper";
import { DataType, HttpMethod, ServiceCall } from "../../reuse/utility/ServiceCall";
import { CDS_TO_ICON, SIMPLE_TYPE_ICON } from "../../reuse/utility/Types";
import Utils, { NewActivityTypes, fetchZorderColumns } from "../js/utils";
import { TaskChainCommonProperties, TaskChainCommonPropertiesClass } from "./TaskChainCommonProperties.controller";

export class NodePropertiesClass extends TaskChainCommonPropertiesClass {
  private taskChainDetailsPopover: sap.m.Popover;

  public onInit(): void {
    super.onInit();
  }

  setObjectModel(oObject) {
    super.setObjectModel(oObject);
    if (oObject.isDeltaTable) {
      this.setzorderModelForNode(oObject);
    }
  }

  public refreshModel() {
    if (!this.currentObject) {
      // undo / redo causing empty node to get created due to the way we create nodes on drag and drop
      return;
    }
    const i18nResourceBundle = this.getView().getModel("i18n").getResourceBundle();
    const model = {
      connectionId: "",
      qualifiedName: "",
      remoteObjectReference: {},
      technicalName: "",
      businessName: "",
      description: "",
      isNew: false,
      hideLabel: false,
      showBusinessName: false,
      showTechnicalName: false,
      showObjectStatus: true,
      showType: true,
      elementCount: 0,
      activity: [],
      isCrossSpace: false,
      crossSpaceName: "",
      numberOfActivities: 0,
      zOrder: [],
      allUsers: [],
      selectedMembers: [],
      tenantMembers: [],
      nonTenantMembers: [{ id: "", name: "" }],
      taskChainDetailsPlaceholders: [],
      placeholderSet: new Set(),
    };
    model.technicalName = this.currentObject.name;
    model.businessName = this.currentObject.label;
    model.elementCount = this.currentObject.elementCount;
    model.crossSpaceName = this.currentObject.repositoryCSN?.crossSpaceName
      ? this.currentObject.repositoryCSN?.crossSpaceName
      : this.currentObject.repositoryCSN?.spaceName;
    model.isCrossSpace = this.currentObject.isCrossSpace !== undefined ? this.currentObject.isCrossSpace : false;
    model.description = this.currentObject?.isBWProcessChain
      ? this.currentObject?.repositoryCSN?.metadata?.description
      : undefined;
    const self = this;
    self.currentObject.resource.applyUndoableAction(
      function () {
        self.currentObject.notificationMessage =
          self?.currentObject?.notificationMessage !== ""
            ? self?.currentObject?.notificationMessage === "@notificationTaskEmailMessageTemplate"
              ? i18nResourceBundle.getText("@notificationTaskEmailMessageTemplate")
              : self.currentObject.notificationMessage
            : i18nResourceBundle.getText("@notificationTaskEmailMessageTemplate");
        self.currentObject.notificationSubject =
          self?.currentObject?.notificationSubject !== ""
            ? self?.currentObject?.notificationSubject === "@notificationTaskEmailSubjectTemplate"
              ? i18nResourceBundle.getText("@notificationTaskEmailSubjectTemplate")
              : self.currentObject.notificationSubject
            : i18nResourceBundle.getText("@notificationTaskEmailSubjectTemplate");
      },
      "Update email message and subject",
      true
    );

    // for object with more than 2 activity radio buttons will be set
    const data = this.currentObject.resource?.model?.applicationIdActivity;
    const technicalType = this.currentObject?.repositoryCSN?.["#technicalType"];
    const filterData = data.filter((x) => x.technicalType === technicalType);

    if (
      filterData.length > 1 &&
      (this.currentObject?.isRemoveData ||
        sap.ui.getCore().getModel("featureflags").getProperty("/DWCO_LOCAL_TABLE_FILES_MONITOR")) &&
      !(
        this.currentObject?.isBWProcessChain ||
        this.currentObject?.isSQLScriptProcedure ||
        this.currentObject?.isNotificationTask
      )
    ) {
      model.activity = [];
      filterData.forEach((element) => {
        const activity = element.activity;

        const oRadioButton = {
          text: this.localizeMessage("i18n", NewActivityTypes[activity]), // Set text for each radio button
          key: activity,
        };
        model.activity.push(oRadioButton);
      });
      model.numberOfActivities = model.activity.length;
    }

    if (this.currentObject?.isNotificationTask) {
      model.taskChainDetailsPlaceholders = [
        {
          placeholder: "$$objectId$$",
          description: i18nResourceBundle.getText("@phObjectId"),
        },
        {
          placeholder: "$$chainName$$",
          description: i18nResourceBundle.getText("@phTaskChainTName"),
        },
        {
          placeholder: "$$user$$",
          description: i18nResourceBundle.getText("@phUser"),
        },
        {
          placeholder: "$$uiLink$$",
          description: i18nResourceBundle.getText("@phLogUILink"),
        },
        {
          placeholder: "$$spaceId$$",
          description: i18nResourceBundle.getText("@phSpaceName"),
        },
      ];
      const placeholdersArr = model.taskChainDetailsPlaceholders;
      const placeholderSet = new Set();
      placeholdersArr.forEach((placeHolderObj) => {
        placeholderSet.add(placeHolderObj.placeholder);
      });

      model.placeholderSet = placeholderSet;

      const self = this;
      self.currentObject.resource.applyUndoableAction(
        function () {
          if (self.currentObject?.notificationMessage === "@notificationTaskEmailMessageTemplate") {
            self.currentObject.notificationMessage = i18nResourceBundle.getText(
              "@notificationTaskEmailMessageTemplate"
            );
          }
          if (self.currentObject?.notificationSubject === "@notificationTaskEmailSubjectTemplate") {
            self.currentObject.notificationSubject = i18nResourceBundle.getText(
              "@notificationTaskEmailSubjectTemplate"
            );
          }
        },
        undefined,
        true
      );
      this.loadNotificationUsersInfo();
    }

    const oModel = new sap.ui.model.json.JSONModel(model);
    oModel.setSizeLimit(1000);
    this.getView().setModel(oModel);
  }

  public taskPositionFormatter(obj: any): number {
    return obj.resource.model.nodes.indexOf(obj);
  }

  public formatters = {
    objectStatusIconFormatter,
    objectStatusIconColorFormatter: Format.objectStatusIconColorFormatter.bind(this),
    objectStatusTextFormatter,
    objectStatusTooltipFormatter,
    dateFormatter,
  };

  public onRetentionDaysChange() {
    const retentionDays = this.getView().getModel("galileiModel").getProperty("/numberOfRetentionDays");
    if (retentionDays < 0) {
      this.getView().getModel("galileiModel").setProperty("/numberOfRetentionDays", 0);
    } else if (retentionDays > 999) {
      this.getView().getModel("galileiModel").setProperty("/numberOfRetentionDays", 999);
    }
  }

  public onRetentionDaysChangeVaccum() {
    const retentionDays = this.getView().getModel("galileiModel").getProperty("/numberOfRetentionDays");
    if (retentionDays < 7) {
      this.getView().getModel("galileiModel").setProperty("/numberOfRetentionDays", 7);
    } else if (retentionDays > 999) {
      this.getView().getModel("galileiModel").setProperty("/numberOfRetentionDays", 999);
    }
  }

  public apacheSparkCustomInputChange() {
    // apacheSparkCustomInput
    const currentObj = this.getView().getModel("galileiModel").getData();
    const apacheSparkCustomInput = this.getView().byId("apacheSparkCustomInput") as sap.m.Input;
    const selectedKey = apacheSparkCustomInput?.getSelectedKey();
    if (Utils.getActivityText(currentObj) === NewActivityTypes.MERGE_FILES) {
      this.getView().getModel("galileiModel").setProperty("/customApacheValueMerge", selectedKey);
    } else if (Utils.getActivityText(currentObj) === NewActivityTypes.OPTIMIZE_FILES) {
      this.getView().getModel("galileiModel").setProperty("/customApacheValueOptimize", selectedKey);
    } else if (isSparkSelectionVacuumEnabled() && Utils.getActivityText(currentObj) === NewActivityTypes.VACUUM_FILES) {
      this.getView().getModel("galileiModel").setProperty("/customApacheValueVacuum", selectedKey);
    } else if (currentObj.isTransformationFlow) {
      this.getView().getModel("galileiModel").setProperty("/customApacheValueTransformation", selectedKey);
    }
    this.getView().getModel("galileiModel").setProperty("/customApacheValueSelected", selectedKey);
  }

  public selectedKeyFormatter(obj, customApacheValueSelected) {
    if (obj.isTransformationFlow) {
      return (
        this.getView().getModel("galileiModel").getProperty("/customApacheValueTransformation") ||
        customApacheValueSelected
      );
    }
    if (Utils.getActivityText(obj) === NewActivityTypes.MERGE_FILES) {
      return (
        this.getView().getModel("galileiModel").getProperty("/customApacheValueMerge") || customApacheValueSelected
      );
    } else if (isSparkSelectionVacuumEnabled() && Utils.getActivityText(obj) === NewActivityTypes.VACUUM_FILES) {
      return (
        this.getView().getModel("galileiModel").getProperty("/customApacheValueVacuum") || customApacheValueSelected
      );
    } else {
      return (
        this.getView().getModel("galileiModel").getProperty("/customApacheValueOptimize") || customApacheValueSelected
      );
    }
  }

  public onEditParameters() {
    const oModel = this.getView().getModel("galileiModel");
    const param = sap.cdw.taskchainmodeler.ModelImpl.getListOfParametersInSQLScriptProcedure(oModel["oData"]);

    sap.cdw.taskchainmodeler.ModelImpl.showIPPopUP(oModel["oData"], param);
  }

  public async setzorderModelForNode(oObject: any): Promise<void> {
    if (this.currentObject.zOrder !== undefined) {
      return;
    }

    let tableData, tableMetrics;
    const oPanel = this.byId("ZOrderColumnsPanel");

    oPanel.setBusy(true);
    tableData = await fetchZorderColumns(this.getSpaceName(), oObject.name);
    tableMetrics = tableData?.localTables?.[0];
    oPanel.setBusy(false);
    const self = this;

    self.currentObject.resource.applyUndoableAction(
      function () {
        self.currentObject.zOrder = tableMetrics?.settings?.OPTIMIZE_FILES?.zOrderby || [];
        self.getView().getModel("galileiModel").setProperty("/Zorder", self.currentObject.zOrder);
      },
      undefined,
      true
    );
  }

  public openInputParameterInfoPopover(event: IEvent<sap.m.Button, { id: string }>): void {
    const sourceControl = event.getSource();

    if (!this["parameterInfoPopover"]) {
      require("./fragment/InputParameterInfoPopover.fragment.xml");
      this["parameterInfoPopover"] = sap.ui.xmlfragment(
        this.createId("ParameterInfoPopover"),
        "sap.cdw.components.taskchainmodeler.properties.fragment.InputParameterInfoPopover",
        this
      ) as sap.m.Popover;
      this.getView().addDependent(this["parameterInfoPopover"]);
    }

    const bindingContext = sourceControl.getBindingContext("galileiModel");
    this["parameterInfoPopover"].bindElement(`galileiModel>${bindingContext.getPath()}`);
    this["parameterInfoPopover"].openBy(sourceControl, false);
  }

  public dataTypeIconFormatter(primitiveDatatype: string): string {
    return CDS_TO_ICON[primitiveDatatype] || SIMPLE_TYPE_ICON;
  }

  public onLogicalLTFSelected(event: any) {
    const sourceControl = event.getSource();
    if (sourceControl?.getSelected() === true) {
      this.currentObject.retentionOptionSelected = false;
    } else {
      this.currentObject.retentionOptionSelected = true;
    }
  }

  public onLogicalLTFRetentionSelected(event: any) {
    const sourceControl = event.getSource();
    if (sourceControl?.getSelected() === true) {
      this.currentObject.retentionOptionSelected = true;
    } else {
      this.currentObject.retentionOptionSelected = false;
    }
  }

  public onApacheDefaultOptionSelect(event: any) {
    const sourceControl = event.getSource();
    if (sourceControl?.getSelected() === true) {
      this.currentObject.defaultApacheSelected = true;
    } else {
      this.currentObject.defaultApacheSelected = false;
    }
  }

  public onApacheCustomOptionSelect(event: any) {
    const sourceControl = event.getSource();
    if (sourceControl?.getSelected() === true) {
      this.currentObject.defaultApacheSelected = false;
    } else {
      this.currentObject.defaultApacheSelected = true;
    }
  }

  public appDetailsListFormatter(maxCore: any, maxMemory: any) {
    return `${maxCore} CPU / ${maxMemory.replace("g", "")} GB`.toUpperCase();
  }

  public onChangeActivity(oEvent) {
    const isSparkFFEnabled = sap.ui
      .getCore()
      .getModel("featureflags")
      .getProperty("/DWCO_LARGE_SYSTEMS_SPARK_SELECTION");
    const selectedIndex = oEvent.getParameters().selectedIndex;

    this.getView().getModel("galileiModel").setProperty("/selectedActivityIndex", selectedIndex);
    this.activityValueFormatter(this.getView().getModel("galileiModel"));
    if (isSparkFFEnabled) {
      this.updateApacheSettings();
    }
    const object = this.getView().getModel("galileiModel") && this.getView().getModel("galileiModel").getData();
    object.validate();
  }

  public updateApacheSettings() {
    const currentObj = this.getView().getModel("galileiModel").getProperty("/");
    let latestValue;
    if (Utils.getActivityText(currentObj) === NewActivityTypes.MERGE_FILES) {
      latestValue = this.getView().getModel("galileiModel").getProperty("/customApacheValueMerge");
    } else if (Utils.getActivityText(currentObj) === NewActivityTypes.OPTIMIZE_FILES) {
      latestValue = this.getView().getModel("galileiModel").getProperty("/customApacheValueOptimize");
    } else if (currentObj.isTransformationFlow) {
      latestValue = this.getView().getModel("galileiModel").getProperty("/customApacheValueTransformation");
    } else if (
      isSparkSelectionVacuumEnabled() &&
      Utils.getActivityText(currentObj) === NewActivityTypes.VACUUM_FILES &&
      !!this.getHDLFStorage(currentObj)
    ) {
      latestValue = this.getView().getModel("galileiModel").getProperty("/customApacheValueVacuum");
    }
    if (latestValue && latestValue !== "") {
      this.getView().getModel("galileiModel").setProperty("/customApacheValueSelected", latestValue);
    }
  }

  public onTechnicalNameChange(oEvent) {
    // validate technical name should be unique for rest task inside the task chain
    const sNewValue = oEvent.getParameter("value");
    const input = this.getView().byId("technicalNameNotification") as sap.m.Input;
    const oModel = this.currentObject.resource?.model;
    const aNodes = oModel.nodes;
    const i18nResourceBundle = this.getView().getModel("i18n").getResourceBundle();
    const object = this.getView().getModel("galileiModel").getData();

    // Check if the display name is unique
    const bUnique = this.isDisplayNameUnique(aNodes, sNewValue);

    const nameValidator = NamingHelper.getNameInputValidator();
    const error = nameValidator.onTechnicalNameChanged(
      object,
      input,
      "displayName",
      {
        duplicatedTechnicalName: i18nResourceBundle.getText("technicalNameValidation"),
        emptyTechnicalName: i18nResourceBundle.getText("technicalNameValidation"),
      },
      oModel,
      NameUsage.entity
    );
    if (bUnique && error.error === undefined && error.isEmpty === undefined) {
      input["lastValue"] = input["_lastValue"];
      this.getView().getModel("galileiModel").setProperty("/displayName", sNewValue);
      this.currentObject.name = sNewValue;
      this.currentObject.technicalNameValueState = "None";
    } else if (!bUnique) {
      this.currentObject.displayName = sNewValue;
      this.currentObject.technicalNameValueState = "Error";
      input.setValueStateText(i18nResourceBundle.getText("technicalNameValidation"));
    }
  }

  public onTechnicalNameSubmit(oEvent) {
    const i18nResourceBundle = this.getView().getModel("i18n").getResourceBundle();
    const object = this.getView().getModel("galileiModel").getData();
    const oModel = this.currentObject.resource?.model;
    const input = this.getView().byId("technicalNameNotification") as sap.m.Input;
    const nameValidator = NamingHelper.getNameInputValidator();
    nameValidator.onTechnicalNameSubmit(
      object,
      input,
      "displayName",
      {
        duplicatedTechnicalName: i18nResourceBundle.getText("technicalNameValidation"),
        emptyTechnicalName: i18nResourceBundle.getText("technicalNameValidation"),
      },
      oModel,
      undefined,
      NameUsage.entity
    );
    object.validate();
  }
  // Helper function to check if the display name is unique
  public isDisplayNameUnique(aNodes, sNewName) {
    // Iterate over each node in the model
    let unique = true;
    aNodes.forEach((node) => {
      // Check if the display name matches the new name
      if (node.displayName === sNewName && sNewName !== this.currentObject.name) {
        unique = false;
        return unique;
      }
    });

    return unique;
  }

  loadAllTenantUsers(): Promise<void> {
    const isSDPEnabled = sap.ui.getCore().getModel("featureflags").getProperty("/DWC_DUMMY_SPACE_PERMISSIONS");
    const hasPrivilege = isSDPEnabled
      ? this.getView().getModel("privilege").getProperty("/USER") ||
        this.getView().getModel("privilege").getProperty("/TEAM")
      : this.getView().getModel("privilege").getProperty("/TEAM");
    return new Promise((resolve) => {
      if (hasPrivilege && hasPrivilege.read) {
        getAllUsers(false).then((users) => {
          users.forEach((user) => {
            user.name = `${user.displayName} (${user.id})`;
          });
          this.getView().getModel("galileiModel").setProperty("/allUsers", users);
          resolve();
        });
      } else {
        resolve();
      }
    });
  }

  /**
   * Get the notification mail list
   */
  public getNotificationMailList() {
    if (this.isHanaDown()) {
      return;
    }
    return new Promise<void>((resolve, reject) => {
      const spaceId = this.getSpaceName();
      const model = this.getView().getModel("galileiModel").getProperty("/");
      const taskChainName = model.resource.model.name;
      const sUrl = `tf/${spaceId}/taskchains/${taskChainName}/getmailinglist`;
      ServiceCall.request<any>({
        url: sUrl,
        type: HttpMethod.GET,
        dataType: DataType.JSON,
      })
        .then((resp) => {
          const allUsers = this.getView().getModel("galileiModel").getProperty("/allUsers");
          let bUsersNotExits = false;
          if ((Array.isArray(allUsers) && allUsers.length === 0) || !allUsers) {
            bUsersNotExits = true;
          }
          const tenantMembers = resp.data.tenantMembers
            .map((member) => {
              const user = allUsers.find((userObj) => userObj.id === member);
              return {
                name: user && user.displayName ? user.displayName : member,
                id: member,
                tenantFlag: true,
                isActive: user !== undefined || bUsersNotExits ? true : false,
              };
            })
            .filter((user) => user.isActive);
          const nonTenantMembers = resp.data.others.map((member) => ({ name: member, id: member, tenantFlag: false }));
          this.getView().getModel("galileiModel").setProperty("/tenantMembers", tenantMembers);
          this.getView().getModel("galileiModel").setProperty("/nonTenantMembers", nonTenantMembers);
          this.getView()
            .getModel("galileiModel")
            .setProperty("/selectedMembers", [...tenantMembers, ...nonTenantMembers]);
          const selectedMembers = this.getView().getModel("galileiModel").getProperty("/selectedMembers");

          const input = this.getView().byId("recipientEmailAddressInput") as sap.m.MultiInput;
          input.removeAllTokens();
          const notificationName = this.currentObject.displayName;

          selectedMembers.forEach((member) => {
            const token = new sap.m.Token({
              key: `${notificationName}_${member.id}`,
              text: member.tenantFlag ? member.name + " (" + member.id + ")" : member.id,
            });
            input.addToken(token);
          });
          resolve();
        })
        .catch((err) => {
          reject();
        });
    });
  }

  public loadNotificationUsersInfo() {
    const circuitBreakerModel = sap.ui.getCore().getModel("circuitbreaker");
    const hanaState = circuitBreakerModel?.getProperty("/DataHANA");
    const isHanaDown: boolean = hanaState === State.Red || hanaState === State.Yellow;
    const dataHANAProvisioningState = circuitBreakerModel?.getProperty("/DataHANAProvisioningState");
    const isHanaUpgradeInProgress: boolean =
      dataHANAProvisioningState === State.Red || dataHANAProvisioningState === State.Yellow;
    if (!(isHanaDown || isHanaUpgradeInProgress)) {
      const emailPanel = this.getView().byId("notificationTaskPanel") as sap.m.Panel;
      emailPanel.setBusy(true);
      emailPanel.setBusyIndicatorDelay(0);
      Promise.all([this.getNotificationMailList(), this.loadAllTenantUsers()])
        .then(() => {
          emailPanel.setBusy(false);
        })
        .catch(() => {
          emailPanel.setBusy(false);
        });
    }
  }

  public onEmailSubjectChange(event) {
    const value = event.getParameter("value");
    this.getView().getModel("galileiModel").setProperty("/notificationSubject", value);
  }

  public onEmailMessageChange(event) {
    const value = event.getParameter("value");
    const placeholderSet = this.getView().getModel("galileiModel").getProperty("/placeholderSet");
    const placeholderRegx = /<[a-zA-Z0-9_@#$%^&*()-=+!?]*>/g;
    const matchedPlaceHolders = value.matchAll(placeholderRegx);
    for (const placeholder of matchedPlaceHolders) {
      if (!placeholderSet.has(placeholder[0])) {
        const message = this.getView()
          .getModel("i18n")
          .getResourceBundle()
          .getText("@inCorrectPlaceHolder", [placeholder]);
        sap.m.MessageToast.show(message);
        return;
      }
    }
    this.getView().getModel("galileiModel").setProperty("/notificationMessage", value);
  }

  public onResetEmailMsg() {
    const i18nResourceBundle = this.getView().getModel("i18n").getResourceBundle();
    const emailMsg = i18nResourceBundle.getText("@notificationTaskEmailMessageTemplate");
    this.getView().getModel("galileiModel").setProperty("/notificationMessage", emailMsg);
  }

  isHanaDown() {
    const circuitBreakerModel = sap.ui.getCore().getModel("circuitbreaker");
    if (circuitBreakerModel) {
      const hanaState = circuitBreakerModel?.getProperty("/DataHANA");
      const isHanaDown: boolean = hanaState === State.Red || hanaState === State.Yellow;
      const dataHANAProvisioningState = circuitBreakerModel?.getProperty("/DataHANAProvisioningState");
      const isHanaUpgradeInProgress: boolean =
        dataHANAProvisioningState === State.Red || dataHANAProvisioningState === State.Yellow;
      if (isHanaDown || isHanaUpgradeInProgress) {
        return true;
      }
    }
    return false;
  }

  public onEmailMessageInfoClick(event) {
    const sourceControl = event.getSource();
    // new model has been set because PlaceholdersForTaskChainDetails.fragment.xml is using emailNotificationModel
    const model = this.getView().getModel();
    this.getView().setModel(model, "emailNotificationModel");
    const oFragment = require("./fragment/PlaceholdersForTaskChainDetails.fragment.xml");
    if (!this.taskChainDetailsPopover) {
      this.taskChainDetailsPopover = sap.ui.xmlfragment(
        this.getView().getId() + "--popover",
        oFragment,
        this
      ) as sap.m.Popover;
      this.getView().addDependent(this.taskChainDetailsPopover);
      const title = this.getView().getModel("i18n").getResourceBundle().getText("@notificationTaskPlaceholder");
      this.taskChainDetailsPopover.setTitle(title);
    }
    this.taskChainDetailsPopover.openBy(sourceControl, false);
  }

  public copyPlaceholderText(rowData) {
    navigator.clipboard.writeText(rowData.placeholder);
    const msg = this.getView().getModel("i18n").getResourceBundle().getText("@placeholderCopied");
    sap.m.MessageToast.show(msg);
  }

  public onRecipientInfoPress(oEvent) {
    const i18nResourceBundle = this.getView().getModel("i18n").getResourceBundle();
    const popover = new sap.m.Popover({
      content: new sap.m.Text({
        text: i18nResourceBundle.getText("recipientInfoText"),
      }).addStyleClass("sapUiSmallMarginBegin sapUiSmallMarginEnd"),
      placement: sap.m.PlacementType.Bottom,
      resizable: true,
      showHeader: false,
    });

    if (popover) {
      popover.openBy(oEvent.getSource(), true);
    }
  }
}

export const NodeProperties = smartExtend(
  TaskChainCommonProperties,
  "sap.cdw.components.taskchainmodeler.properties.NodeProperties",
  NodePropertiesClass
);

sap.ui.define("sap/cdw/components/taskchainmodeler/properties/Nodeproperties.controller", [], function () {
  return NodeProperties;
});
