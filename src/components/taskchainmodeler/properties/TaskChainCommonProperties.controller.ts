/**
 * Copyright 2020 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { isSparkSelectionVacuumEnabled } from "../../abstractbuilder/commonservices/FeatureFlagCheck";
import { AbstractControllerClass } from "../../abstractbuilder/controller/AbstractController.controller";
import { BaseController, smartExtend } from "../../basecomponent/controller/BaseController.controller";
import { AbstractDataBuilderEditorComponentClass } from "../../databuilder/AbstractDataBuilderEditorComponent";
import { DataBuilderWorkbench } from "../../databuilder/controller/DataBuilderWorkbench.controller";
import {
  getDatabuilderWorkbench,
  openDatabuilderValidationsPopoverBy,
} from "../../databuilder/utility/DatabuilderHelper";
import { SidepanelMode } from "../../ermodeler/js/statics/const/EntityPropertiesSidepanel";
import {
  aggregationValidationCount,
  aggregationValidationType,
  aggregationValidationTypeCustomStyle,
} from "../../ermodeler/js/utility/commonFormatter";
import {
  objectStatusIconFormatter,
  objectStatusTextFormatter,
  objectStatusTooltipFormatter,
} from "../../ermodeler/js/utility/sharedFunctions";
import { Format } from "../../reuse/utility/Format";
import { MessageHandler } from "../../reuse/utility/MessageHandler";
import { loadResourceBundle } from "../../reuse/utility/MessageLoader";
import { TaskChainModelerClass } from "../controller/TaskChainModeler.controller";
import Utils, { NewActivityTypes, fetchLocalTableMetrics, fetchTFMetrics, getApplicationInfo } from "../js/utils";

export enum Pages {
  ModelPage = "ModelPage",
  NodePage = "nodeDetailsPage",
  LinkPage = "linkDetailsPage",
  OperatorPage = "operatorDetailsPage",
  RESTTaskPage = "RESTTaskDetailsPage",
}

const MAP_MESSAGEBUNDLE_IDS = {
  "sap.dwc.remoteTable": "../../remotetablemonitor/i18n/i18n.properties",
  "sap.dwc.persistedView": "../../viewmonitor/i18n/i18n.properties",
  "sap.dwc.ilTaskLogs": "../../intelligentlookup/i18n/i18n.properties",
  // eslint-disable-next-line camelcase
  i18n_rf: "../../replicationflow/i18n/i18n.properties",
};

export class TaskChainCommonPropertiesClass extends AbstractControllerClass {
  static smallWidth: sap.ui.core.CSSSize = "460px";
  public oResourceModel: sap.ui.model.resource.ResourceModel;
  public highlightCounter: number;
  public getCurrentNode: any;
  public isEditable: boolean;
  public isCanModifyDefaultWidth: boolean = false;
  public bModelPageVisible: boolean = false;
  public isNavNotBreadcrumb: boolean = false;
  protected currentObject: any;
  protected subscriptionsDone: boolean; // make sure we only subscribe once per event
  protected objectInfoPopover: sap.m.Popover;
  private databuilderWorkbench: DataBuilderWorkbench;
  diagramEditor: any;
  private currentView: string;

  public onDefaultInit(): void {
    const bundleName = require("../i18n/i18n.properties");
    this.oResourceModel = new sap.ui.model.resource.ResourceModel({
      bundleName: bundleName,
    });
    this.getView().setModel(this.oResourceModel, "i18n");
    // load er modeler resource bundle to avoid duplication of deployment status
    const erBundleName = require("../../ermodeler/i18n/i18n.properties");
    const oERResourceModel = new sap.ui.model.resource.ResourceModel({
      bundleName: erBundleName,
    });
    this.getView().setModel(oERResourceModel, "i18n_erd");

    loadResourceBundle("sap.cdw.components.remotetablemonitor.i18n.i18n", false).then((viewBundle) => {
      // const remoteTablebundleName = require("../../remotetablemonitor/i18n/i18n.properties");
      const oRemoteTableResourceModel = new sap.ui.model.resource.ResourceModel({
        bundle: viewBundle,
      });
      this.getView().setModel(oRemoteTableResourceModel, "sap.dwc.remoteTable");
    });
    loadResourceBundle("sap.cdw.components.viewmonitor.i18n.i18n", false).then((viewBundle) => {
      // const viewPersistBundleName = require("../../viewmonitor/i18n/i18n.properties");
      const oViewPersistResourceModel = new sap.ui.model.resource.ResourceModel({
        bundle: viewBundle,
      });
      this.getView().setModel(oViewPersistResourceModel, "sap.dwc.persistedView");
    });
    loadResourceBundle("sap.cdw.components.intelligentlookup.i18n.i18n", false).then((ilBundle) => {
      const oILRunResourceModel = new sap.ui.model.resource.ResourceModel({
        bundle: ilBundle,
      });
      this.getView().setModel(oILRunResourceModel, "sap.dwc.ilTaskLogs");
    });
    loadResourceBundle("sap.cdw.components.replicationflow.i18n.i18n", false).then((rfBundle) => {
      const oRFResourceModel = new sap.ui.model.resource.ResourceModel({
        bundle: rfBundle,
      });
      this.getView().setModel(oRFResourceModel, "i18n_rf");
    });
    const model = this.getView().getModel();
    const columnsModel = this.getView().getModel("columnsModel");
    if (!model) {
      // add an empty default model
      this.getView().setModel(new sap.ui.model.json.JSONModel({}));
    }
    if (!columnsModel) {
      // add an empty columns model
      this.getView().setModel(new sap.ui.model.json.JSONModel({ columns: [] }), "columnsModel");
    }
    this.getView().setModel(new sap.ui.model.json.JSONModel({ appData: {} }), "ltfAppModel");
    this.currentView = this.getView()["sViewName"];
  }

  public onInit(): void {
    this.onDefaultInit();
    super.onInit();
    sap.ui
      .getCore()
      .getEventBus()
      .subscribe("TASKCHAIN_MODELER", "PROPERTY_PANEL_RELOAD", this.onReloadEvent.bind(this));
  }

  public onAfterRendering() {
    if (this.bModelPageVisible) {
    }
  }

  public onNodeEdit(event: sap.ui.base.Event): void {
    const model: sap.galilei.ui5.GalileiModel = this.getView().getModel("galileiModel") as sap.galilei.ui5.GalileiModel;
    const bindingPath: string = (event.getSource() as sap.m.Button).getBindingContext("galileiModel").getPath();
    const oSelectedElement = bindingPath && model.getProperty(bindingPath);
    const isRestApiEnabled = sap.ui.getCore().getModel("featureflags").getProperty("/DWCO_INFRA_TASKS_API_TASK");
    model.setProperty("/selectedNode", oSelectedElement);
    if (oSelectedElement?.isOperator) {
      this.switchPage(Pages.OperatorPage);
    } else if (oSelectedElement?.isRestApi && isRestApiEnabled) {
      this.switchPage(Pages.RESTTaskPage);
    } else {
      this.switchPage(Pages.NodePage);
    }
    // else if (oSelectedElement?.isRESTTask) {
    //   this.switchPage(Pages.RESTTaskPage);
    // }
  }

  public openObjectInfoPopover(event: IEvent<sap.m.Button, { id: string }>): void {
    const sourceControl = event.getSource();

    if (!this.objectInfoPopover) {
      const oFragment = require("./fragment/ObjectInfoPopover.fragment.xml");
      this.objectInfoPopover = sap.ui.xmlfragment(this.createId("ObjectInfoPopover"), oFragment, this) as sap.m.Popover;
      this.getView().addDependent(this.objectInfoPopover);
    }
    const bindingContext = sourceControl.getBindingContext("galileiModel");
    const object = bindingContext.getObject();
    if (object) {
      const oModel = new sap.galilei.ui5.GalileiModel(object);
      this.objectInfoPopover.setModel(oModel);
      this.objectInfoPopover.openBy(sourceControl, false);
    }
  }

  public activityValueFormatter(obj: any): string {
    return obj.isTask ? this.localizeMessage("i18n", Utils.getActivityText(obj)) : "";
  }

  public hdflStorageValueFormatter(obj: any): string {
    return this.getHDLFStorage(obj) ? this.localizeMessage("i18n", "hdlfStorage") : "";
  }

  public getDeleteVaccumVisible(obj, flag) {
    if (obj && Utils.getActivityText(obj) === NewActivityTypes.VACUUM_FILES) {
      return !!this.getHDLFStorage(obj);
    }
    return false;
  }

  public getApacheSettingsVisible(obj, flag) {
    const isSparkFFEnabled = sap.ui
      .getCore()
      .getModel("featureflags")
      .getProperty("/DWCO_LARGE_SYSTEMS_SPARK_SELECTION");
    if (
      (isSparkFFEnabled &&
        obj &&
        ([NewActivityTypes.MERGE_FILES, NewActivityTypes.OPTIMIZE_FILES].includes(Utils.getActivityText(obj)) ||
          obj.isTransformationFlow)) ||
      (isSparkSelectionVacuumEnabled() && obj && NewActivityTypes.VACUUM_FILES === Utils.getActivityText(obj))
    ) {
      return !!this.getHDLFStorage(obj, true);
    }
    return false;
  }

  public getApacheSettingsDefault(obj, flag) {
    return obj.applicationDefault;
  }

  public getHDLFStorage(obj, checkTF?) {
    if (Utils.canSupportDeleteData() && obj) {
      const data = obj.resource?.model?.applicationIdActivity;

      let filterItem;
      if (checkTF) {
        filterItem = data.filter((x) => x.applicationId === "LOCAL_TABLE" && x.activity === "MERGE_FILES");
      } else if (obj.applicationId) {
        filterItem = data.filter((x) => x.applicationId === obj.applicationId);
      } else if (obj["#technicalType"] || obj?.repositoryCSN?.["#technicalType"]) {
        const technicalType = obj["#technicalType"] ? obj["#technicalType"] : obj?.repositoryCSN["#technicalType"];
        filterItem = data.filter((x) => x.technicalType === technicalType);
      }
      const filterLen = filterItem ? filterItem.length : 0;
      if (filterLen > 0 && (obj.isDeltaTable === true || (checkTF && obj.isTransformationFlow))) {
        return filterItem[0]?.capabilities ? filterItem[0]?.capabilities?.[0]?.hdlfStorage : false;
      }
    } else if (obj && obj.isDeltaTable === true) {
      return false;
    }

    return undefined;
  }

  public technicalTypeValueFormatter(obj: any): string {
    if (obj?.isOperator) {
      return obj.operationType;
    }
    return obj.isTask ? this.localizeMessage("i18n", Utils.getTypeText(obj)) : "";
  }

  public switchPage(toPage: string): void {
    const navCon = this.byId("navCon") as sap.m.NavContainer;
    const model: sap.galilei.ui5.GalileiModel = this.getView().getModel("galileiModel") as sap.galilei.ui5.GalileiModel;
    const oNode = model.getProperty("/selectedNode");
    let directionTo = true;
    if (toPage === Pages.ModelPage) {
      directionTo = false;
      const oModel = model.getData();
      const parentModel = oModel.resource.model;
      this.refreshObject(parentModel);
      this.setHeaderModel(parentModel);
    } else {
      if (toPage === Pages.NodePage && !oNode.isRestApi) {
        // Node Properties page
        this.setHeaderModel(oNode);
        this.setNodeModel(oNode, "nodeDetailsContainer");
      } else if (toPage === Pages.LinkPage && oNode) {
        // Link Properties page
        this.setHeaderModel(oNode);
        this.setNodeModel(oNode, "linkDetailsContainer");
      } else if (toPage === Pages.OperatorPage && oNode) {
        // Operator Properties page
        this.setHeaderModel(oNode);
        this.setNodeModel(oNode, "operatorDetailsContainer");
      } else if (toPage === Pages.RESTTaskPage && oNode) {
        // REST API Properties page
        this.setHeaderModel(oNode);
        this.setNodeModel(oNode, "RESTTaskDetailsContainer");
      }
    }
    if (toPage) {
      if (directionTo) {
        navCon.to(this.byId(toPage) as sap.m.Page, "slide");
      } else {
        navCon.backToTop(this.byId(toPage) as sap.m.Page, "slide");
      }
    } else {
      navCon.back();
    }
  }

  public onBackToFields() {
    this.switchPage(Pages.ModelPage);
    this.setModelPageVisible(true);
    if (!this.isNavNotBreadcrumb) {
      this.getDiagramBuilder().unselectAllSymbols();
    }
  }

  public setNodeModel(oObject: any, viewId: string): void {
    const nodeView = (this.getView() as sap.ui.core.mvc.XMLView).byId(viewId) as sap.ui.core.mvc.XMLView;
    (nodeView.getController() as any).setModelPageVisible(false);
    (nodeView.getController() as any).setObjectModel(oObject);
    this.refreshObject(oObject);
  }

  public localizeMessage(sModelName: string, sMessageId: string): string {
    try {
      const i18nText = this.getView().getModel(sModelName).getProperty(sMessageId);
      if (i18nText) {
        return i18nText;
      }
      return sMessageId;
    } catch (e) {
      return sMessageId;
    }
  }

  public localizeText(model: string, text: string, parameters: any[] = []): string {
    return Utils.localizeText(this.getView().getModel(model) as sap.ui.model.resource.ResourceModel, text, parameters);
  }

  public setModelPageVisible(bVisible): void {
    this.bModelPageVisible = bVisible;
  }

  public refreshObject(oObject): void {
    this.currentObject = oObject;
    this.setHeaderModel(oObject);
    this.refreshGalileiModel();
    const uistateModel = this.getView().getModel("UiState");
    if (!uistateModel) {
      this.createUiStateModel();
    }
    this.refreshModel();
  }

  public async setAppModel(oObject: any): Promise<void> {
    if (
      this.getSpaceName() &&
      oObject?.isTask &&
      oObject.applicationDefault === "" &&
      oObject.applicationDefault !== "PROCESSING..."
    ) {
      // oObject.applicationDefault = "PROCESSING...";
      this.getView().setModel(new sap.ui.model.json.JSONModel({ appData: {} }), "ltfAppModel");
      // const appData = await fetchApplicationId(this.getSpaceName(), this.getView());
      let tableData, tableMetrics;
      if (oObject?.name && (oObject.isDeltaTable || oObject.isTransformationFlow)) {
        this.getView().setBusy(true);
        if (oObject.isDeltaTable) {
          tableData = await fetchLocalTableMetrics(this.getSpaceName(), oObject?.name, false);
          tableMetrics = tableData?.localTables?.[0];
        } else if (oObject.isTransformationFlow) {
          tableData = await fetchTFMetrics(this.getSpaceName(), oObject?.name);
        }
        this.getView().setBusy(false);
      }
      this["appData"] = getApplicationInfo(
        oObject?.resource?.model?.applications,
        true,
        tableMetrics?.settings || tableData,
        this.getView(),
        oObject
      );
      this.getView().getModel("ltfAppModel").setProperty("/appData", this["appData"]);
      this.getView().getModel("ltfAppModel").refresh(true);
    }
  }

  public setObjectModel(oObject): void {
    let modelObject = oObject;
    const objectClassName = oObject?.classDefinition?.name;
    const isSparkFFEnabled = sap.ui
      .getCore()
      .getModel("featureflags")
      .getProperty("/DWCO_LARGE_SYSTEMS_SPARK_SELECTION");
    if (isSparkFFEnabled) {
      this.setAppModel(oObject);
    }
    if (
      this.bModelPageVisible === true &&
      objectClassName &&
      (objectClassName === "Task" || objectClassName === "Link" || objectClassName === "Operation")
    ) {
      modelObject = oObject.resource.model;
    }
    this.refreshObject(oObject);
    if (
      this.bModelPageVisible === true &&
      objectClassName &&
      (objectClassName === "Task" || objectClassName === "Link" || objectClassName === "Operation")
    ) {
      const model: sap.galilei.ui5.GalileiModel = this.getView().getModel(
        "galileiModel"
      ) as sap.galilei.ui5.GalileiModel;
      model.setProperty("/selectedNode", oObject);
      if (oObject.classDefinition.name === "Task" && !oObject.isRestApi) {
        this.switchPage(Pages.NodePage);
      } else if (oObject.classDefinition.name === "Link") {
        this.switchPage(Pages.LinkPage);
      } else if (oObject.classDefinition.name === "Operation") {
        this.switchPage(Pages.OperatorPage);
      } else if (oObject.isRestApi) {
        this.switchPage(Pages.RESTTaskPage);
      } // need to add for REST task
    } else {
      const navCon = this.byId("navCon") as sap.m.NavContainer;
      const pageId = navCon?.getCurrentPage().getId();
      if (
        oObject &&
        objectClassName === "Model" &&
        pageId &&
        (pageId.includes("nodeDetailsPage") ||
          pageId.includes("linkDetailsPage") ||
          pageId.includes(Pages.OperatorPage) ||
          pageId.includes(Pages.RESTTaskPage))
      ) {
        this.isNavNotBreadcrumb = true;
        this.onBackToFields();
        this.isNavNotBreadcrumb = false;
      }
    }
  }

  public createUiStateModel() {
    const uiStateModel = new sap.ui.model.json.JSONModel({
      editable: this.getHasPrivileges(),
    });
    const eventBus = sap.ui.getCore().getEventBus();
    // eventBus.subscribe(Utils.EVENTS.DF_DIAGRAM_CHANNEL, Utils.EVENTS.UNDO_REDO_EVENT, this.resetUiState.bind(this));
    this.getView().setModel(uiStateModel, "UiState");
    return uiStateModel;
  }

  public resetUiState() {
    // refresh only the currentObject properties
    const propertyPanelController = this.getDiagramBuilder()
      ?.controller?.getParentController()
      ?.getPropertyPanelController();
    if (
      propertyPanelController &&
      propertyPanelController.currentObject &&
      this.currentObject &&
      propertyPanelController.currentObject !== this.currentObject
    ) {
      return;
    }
    // swicth to properties page
    if (this.currentObject) {
      this.refreshGalileiModel();
      this.refreshModel();
    }
  }

  public refreshGalileiModel() {
    const oModel: sap.galilei.ui5.GalileiModel = this.createModel(this.currentObject);
    this.getView().setModel(oModel, "galileiModel");
  }

  public refreshModel() {
    // the function should be implemented in the child class
  }

  public createModel(object: object): sap.galilei.ui5.GalileiModel {
    const oModel: sap.galilei.ui5.GalileiModel = new sap.galilei.ui5.GalileiModel(object);
    oModel.setSizeLimit(500);
    return oModel;
  }

  public setHeaderModel(object?: object) {
    const modelObject: any = object || {};
    const oModel: sap.galilei.ui5.GalileiModel = new sap.galilei.ui5.GalileiModel(modelObject);
    this.isEditable = modelObject.isReadOnlyObject !== true && this.getHasPrivileges();
    oModel.setProperty("/editable", this.isEditable);
    if (
      modelObject &&
      modelObject.classDefinition &&
      (modelObject.classDefinition.name === "Task" ||
        modelObject.classDefinition.name === "Link" ||
        modelObject.classDefinition.name === "Operation")
    ) {
      oModel.setProperty("/backText", modelObject.resource.model.displayName);
      if (modelObject.classDefinition.name === "Link") {
        const displayName = this.localizeText("i18n", "@linkTo", [
          modelObject.source?.displayName || modelObject.source?.name,
          modelObject.target?.displayName || modelObject.target?.name,
        ]);
        oModel.setProperty("/displayName", displayName);
        modelObject.displayName = displayName;
      }
    }
    this.getView().setModel(oModel, "header");
  }

  public showBreadCrumbsFormatter(oObject) {
    if (
      oObject &&
      oObject.classDefinition &&
      (oObject.classDefinition.name === "Task" ||
        oObject.classDefinition.name === "Link" ||
        oObject.classDefinition.name === "Operation")
    ) {
      return true;
    } else {
      return false;
    }
  }

  private getHasPrivileges(): boolean {
    // Check the feature flag
    let hasPrivileges: boolean;
    const workbenchEnvModel = this.getView().getModel("workbenchEnv");
    if (workbenchEnvModel) {
      hasPrivileges = workbenchEnvModel.getProperty("/canCreateOrUpdateModel");
    } else {
      const privileges = sap.ui.getCore().getModel("privilege").getData().DWC_DATABUILDER;
      hasPrivileges = privileges.create || privileges.update;
    }
    return hasPrivileges;
  }

  public onToggleFullScreen(/* event: any*/) {
    sap.ui.getCore().getEventBus().publish("propertyPanel", "toggleFullScreen", {
      // put "normal" width here if it is not the default
    });
  }

  /**
   * Sets the default width.
   */
  public getWidth(): sap.ui.core.CSSSize {
    return TaskChainCommonPropertiesClass.smallWidth;
  }

  public isFullScreenFormatter(mode: SidepanelMode): boolean {
    return mode === SidepanelMode.fullScreen;
  }

  public fullScreenIconFormatter(mode: SidepanelMode) {
    // return "sap-icon://" + (mode === SidepanelMode.fullScreen) ? "exit-full-screen" : "full-screen";
    return sap.ui.core.IconPool.getIconURI(mode === SidepanelMode.fullScreen ? "exit-full-screen" : "full-screen");
  }

  protected getDatabuilderWorkbench(): DataBuilderWorkbench {
    if (this.databuilderWorkbench) {
      return this.databuilderWorkbench;
    }
    this.databuilderWorkbench = getDatabuilderWorkbench(this.getView());
    return this.databuilderWorkbench;
  }

  protected getEditorComponent(): AbstractDataBuilderEditorComponentClass {
    const workbench = this.getDatabuilderWorkbench();
    return workbench?.getActiveEditor();
  }

  public onReloadEvent(_channel, _eventName, data) {
    if (data && data.oNode === this.currentObject) {
      this.refreshModel();
    }
  }

  public aggregationValidationType(aValidations): string {
    return aggregationValidationType(aValidations);
  }

  public aggregationValidationCount(aValidations): string {
    return aggregationValidationCount(aValidations);
  }

  public aggregationValidationTypeCustomStyle(aValidations): string {
    return aggregationValidationTypeCustomStyle(aValidations);
  }

  public getSpaceName(): string {
    return this.getDiagramBuilder()?.controller?.getParentController()?.getSpaceName();
  }

  /**
   * Object status icon, based on '#objectStatus'
   */
  public async objectStatusIconFormatter(objectStatus, isNew) {
    return objectStatusIconFormatter(objectStatus, isNew);
  }
  /**
   * Object status icon color, based on '#objectStatus'
   */
  public async objectStatusIconColorFormatter(objectStatus, isNew) {
    return Format.objectStatusIconColorFormatter(objectStatus, isNew);
  }
  /**
   * Object status text, based on '#objectStatus'
   */
  public async objectStatusTextFormatter(objectStatus, isNew) {
    return objectStatusTextFormatter(objectStatus, isNew);
  }
  /**
   * Object status tooltip, based on '#objectStatus'
   */
  public async objectStatusTooltipFormatter(objectStatus, isNew) {
    return objectStatusTooltipFormatter(objectStatus, isNew);
  }

  public headerIconFormatter(obj) {
    return Utils.getHeaderIcon(obj);
  }

  /**
   * Triggered when header validation button is clicked
   */
  public onShowValidations(oEvent): void {
    const oControl = oEvent.getParameter("oSource") || oEvent.getSource();
    openDatabuilderValidationsPopoverBy(this.currentObject, oControl, this.getI18nResources());
  }

  public getI18nResources(): any {
    const aResourceModels = [];
    const oResourceModel = this.getView().getModel("i18n");
    aResourceModels.push({
      model: oResourceModel,
      name: "i18n_tc",
    });

    aResourceModels.push({
      model: this.getView().getModel("sap.dwc.persistedView"),
      name: "sap.dwc.persistedView",
    });

    aResourceModels.push({
      model: this.getView().getModel("sap.dwc.remoteTable"),
      name: "sap.dwc.remoteTable",
    });

    aResourceModels.push({
      model: this.getView().getModel("sap.dwc.ilTaskLogs"),
      name: "sap.dwc.ilTaskLogs",
    });
    aResourceModels.push({
      model: this.getView().getModel("i18n_rf"),
      name: "i18n_rf",
    });
    return aResourceModels;
  }

  public getEditorController(): TaskChainModelerClass {
    const controller = this.getDatabuilderWorkbench()
      ?.getActiveEditor()
      ?.view()
      .getController() as TaskChainModelerClass;
    return controller;
  }

  public getDiagramBuilder() {
    const controller = this.getEditorController();
    if (controller) {
      return controller.getDiagramEditor();
    }
  }

  public updateStatus(oResponse) {
    const logs = oResponse.data.logs || oResponse.data;
    if (logs && logs.length) {
      let firstItem = logs[logs.length - 1];
      if (firstItem.status?.toLowerCase() !== "running" && logs.length > 0) {
        firstItem = logs[0];
      }
      // set formatted time to response
      firstItem.formattedStarted = this.formatDateTime(firstItem.startTime);
      const self = this;
      this.currentObject.resource.applyUndoableAction(
        () => {
          self["galileiModel"].executionStatus = firstItem;
          // force refresh the model to update the value in the UI
          self.getView().getModel("galileiModel").refresh(true);
        },
        undefined,
        true
      );
    }
  }

  public showNavIconFormatter(status: string, errorDetails: any): boolean {
    if (
      !status ||
      status === this.getView().getModel("i18n").getResourceBundle().getText("@lblNotExecuted") ||
      errorDetails
    ) {
      return false;
    }
    return true;
  }

  public runStatusFormatter(sStatus: string): string {
    const oBundle = this.getView().getModel("i18n").getResourceBundle();
    if (sStatus) {
      switch (sStatus.toLowerCase()) {
        case "completed":
          return oBundle.getText("@statusCompleted");
        case "running":
          return oBundle.getText("@statusRunning");
        case "failed":
          return oBundle.getText("@statusFailed");
        case "stopped":
          return oBundle.getText("@statusStopped");
        case "stopping":
          return oBundle.getText("@statusStopping");
        default:
          return oBundle.getText("@lblNotExecuted");
      }
    } else {
      return oBundle.getText("@lblNotExecuted");
    }
  }

  public handleViewDetailPress() {
    MessageHandler.exception({
      exception: this["galileiModel"].executionErrorDetails,
      message: this.localizeText("i18n", "backendErrorMsg"),
      id: "executionErrorDetailsMsgBox",
    });
  }

  /**
   * View details for failed execution formatter
   * @param sStatus
   * @returns
   */
  public showViewDetailsLinkFormatter(sStatus: string): boolean {
    if (sStatus === "Failed") {
      return true;
    }
    return false;
  }

  public onViewDetailPress() {
    let message = this["galileiModel"].executionStatus.messages;
    if (Array.isArray(message) && message.length) {
      message = message[message.length - 1].text;
    }
    MessageHandler.uiError(message, null, null, null, "50%", null, null, "failedExecutionMsgBox");
  }

  public runStatusColorFormatter(sStatus: string): any {
    if (sStatus) {
      switch (sStatus.toLowerCase()) {
        case "failed":
          return sap.ui.core.ValueState.Error;
        case "completed":
          return sap.ui.core.ValueState.Success;
        default:
          return sap.ui.core.ValueState.None;
      }
    } else {
      return sap.ui.core.ValueState.None;
    }
  }
}

export const TaskChainCommonProperties = smartExtend(
  BaseController,
  "sap.cdw.components.taskchainmodeler.properties.TaskChainCommonProperties",
  TaskChainCommonPropertiesClass
);
