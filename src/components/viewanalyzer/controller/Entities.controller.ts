/** @format */

import { State } from "@sap/dwc-circuit-breaker";
import { BaseController, smartExtend } from "../../basecomponent/controller/BaseController.controller";
import { MessageHandler } from "../../reuse/utility/MessageHandler";
import { HttpMethod, ServiceCall } from "../../reuse/utility/ServiceCall";
import { isCircuitBreakerYellowStateEnabled } from "../../shell/featureState/CircuitBreakerYellowStateFeatureState";
import { UISpaceCapabilities } from "../../shell/utility/UISpaceCapabilities";
import { TaskLogClass } from "../../tasklog/controller/TaskLog.controller";
import { openPartiallyPersistedInfo } from "../../viewmonitor/viewpersistencyservice/ServiceConsumption";
import { EntityLabel, getIconByType, getLevel, payLoadExcludeList } from "../utility/Helper";
import { AnalyzerDetailsDialog } from "./AnalyzerDetailsDialog.controller";
import { StartAnalyzerDialog } from "./StartAnalyzerDialog.controller";

export class EntitiesClass extends BaseController {
  view: sap.ui.core.mvc.View;
  spaceId: any;
  logId: any;
  lineageView: any;
  objectId: any;
  entityDetailsView: any;
  entityPayloadObject: {};
  analyzerView: any;
  analyzerDialog: StartAnalyzerDialog;
  persistencyAnalyzerView: any;
  analyzerDetailsDialog: AnalyzerDetailsDialog;
  detailsData: any;
  entitiesTable: sap.m.Table;
  i18nModel: sap.ui.model.resource.ResourceModel;
  viewSettingsDialogs = {};
  entitySortDialog: any;
  entityFilterDialog: any;
  taskController: any;
  taskview: sap.ui.core.mvc.View;
  isVersion2 = false;

  onInit(): void {
    require("../css/style.css");
    super.onInit();
    this.view = this.getView();
    this.setupModels();
    this.onViewChange(null);
    this.attachHanaStateChangeHandler();

    this.createErrorWarningMessagePopover();
  }
  setupModels() {
    this.view.setModel(new sap.ui.model.json.JSONModel({}), "entitiesModel");
    this.view.setModel(sap.ui.getCore().getModel("featureflags"), "featureflags");
    this.view.setModel(new sap.ui.model.json.JSONModel({}), "actionControlModel");
    this.view.getModel("actionControlModel").setProperty("/enableStart", false);
    this.view.getModel("actionControlModel").setProperty("/enableNavigation", false);
    this.view.getModel("actionControlModel").setProperty("/showMemoryColumns", false);
    this.i18nModel = new sap.ui.model.resource.ResourceModel({
      bundleName: require("../i18n/i18n.properties"),
    });
    this.view.setModel(this.i18nModel, "i18n");
  }
  setDetails(objectId, spaceId) {
    this.objectId = objectId;
    this.spaceId = spaceId;
  }
  resetModelData() {
    this.view.getModel("entitiesModel").setProperty("/", {});
    this.view.getModel("actionControlModel").setProperty("/enableStart", false);
    this.view.getModel("actionControlModel").setProperty("/enableNavigation", false);
    this.view.getModel("actionControlModel").setProperty("/enableActions", false);
    this.view.getModel("actionControlModel").setProperty("/enableFilter", false);
    this.view.getModel("actionControlModel").setProperty("/enableSort", false);
    this.view.getModel("actionControlModel").setProperty("/showMemoryColumns", false);
  }

  private attachHanaStateChangeHandler() {
    const circuitBreakerModel = sap.ui.getCore().getModel("circuitbreaker");
    if (circuitBreakerModel) {
      const binding = new sap.ui.model.Binding(
        circuitBreakerModel,
        "/DataHANA",
        (circuitBreakerModel as any).getContext("/"),
        {}
      );
      binding.attachChange(() => {
        const hanaState = circuitBreakerModel?.getProperty("/DataHANA");
        const isHanaDown: boolean =
          // On FF removal of isCircuitBreakerYellowStateEnabled be sure to keep boolean logic consistent
          hanaState === State.Red || (hanaState === State.Yellow && !isCircuitBreakerYellowStateEnabled());
        const dataHANAProvisioningState = circuitBreakerModel?.getProperty("/DataHANAProvisioningState");
        const isHanaUpgradeInProgress: boolean =
          dataHANAProvisioningState === State.Red ||
          // On FF removal of isCircuitBreakerYellowStateEnabled be sure to keep boolean logic consistent
          (dataHANAProvisioningState === State.Yellow && !isCircuitBreakerYellowStateEnabled());
        if (isHanaDown || isHanaUpgradeInProgress) {
          this.resetModelData();
        }
      });
    }
  }
  public async getResults(spaceId, logId) {
    this.entitiesTable = this.view.byId("entitiesListView--entitiesListTable") as sap.m.Table;
    this.entitiesTable?.setBusy(true);
    this.entitiesTable?.removeSelections(true);
    this.resetModelData();
    const url = `advisor/${spaceId}/result/${logId}`;
    let entityWithWarnings = [];
    let entityWithErrors = [];
    ServiceCall.request<any>({
      url: url,
      type: HttpMethod.GET,
    }).then(
      (response) => {
        this.detailsData = response.data;
        this.isVersion2 = this.detailsData.identifier === "Persistency.Advice.2";
        const stats = response.data.entityStats;

        const ObjLength = this.detailsData.entityStats?.length > 0 ? this.detailsData.entityStats?.length : "0";
        this.view.getModel("entitiesModel").setProperty("/ObjectHeaderText", this.getText("objectsHeader", ObjLength));

        let globalPayload: string = "";
        if (response.data.globalPayload) {
          for (const payload of response.data.globalPayload) {
            globalPayload += this.getText(payload.messageKey, payload.params) + " ";
          }
        }
        this.view.getModel("selectedModel")?.setProperty("/globalPayload", globalPayload);
        this.view.getModel("selectedModel")?.setProperty("/showglobalPayload", globalPayload !== "");
        stats?.forEach((element) => {
          element.sortjoins = element.joins > 0 ? element.joins : 0;
          element.sortpartitions = element.partitions > 0 ? element.partitions : 0;
          element.sortpeakMemoryinMB = element.peakMemoryinMB > 0 ? element.peakMemoryinMB : 0;
          element.sortnumberRecords = element.numberRecords > 0 ? element.numberRecords : 0;
          element.sortpersistencyRuntime = element.persistencyRuntime > 0 ? element.persistencyRuntime : 0;
          element.sortCandidateScore = element.persistencyCandidateScore > 0 ? element.persistencyCandidateScore : 0;

          element.type = this.getText(element.label);

          element.dataAccess = this.getText(element.accessStatus);

          if (element.peakMemoryinMB || element.numberRecords || element.numberRecords || element.persistencyRuntime) {
            this.view.getModel("actionControlModel").setProperty("/showMemoryColumns", true);
          }
          if (element.peakMemoryinMB) {
            element.peakMemoryinMB = this.formatNumber(element.peakMemoryinMB);
          }
          if (element.numberRecords) {
            element.numberRecords = this.formatNumber(element.numberRecords);
          }

          if (element.persistencyCandidateScore) {
            element.persistencyScoreColumn =
              element.persistencyCandidate === "NOT_APPLICABLE"
                ? this.getText("NOT_APPLICABLE")
                : element.persistencyCandidateScore + " " + this.getText(element.persistencyCandidate);
            element.candidateState = (() => {
              switch (element.persistencyCandidate) {
                case "BEST_CANDIDATE":
                  return "Success";
                case "BEST_ALTERNATIVE_CANDIDATE":
                  return "Warning";
                case "GOOD_CANDIDATE":
                case "NOT_APPLICABLE":
                  return "Indication20";
                default:
                  return "None";
              }
            })();
          } else {
            element.persistencyScoreColumn = this.getText("NOT_APPLICABLE");
            element.candidateState = "Indication20";
          }
          element.entityIcon = getIconByType(element?.label);

          if (element.persistencyRuntime) {
            element.duration = this.formatTimespanHHMMSS(element.persistencyRuntime);
          }

          if (element.analysisStatus === "W") {
            this.isVersion2
              ? entityWithWarnings.push(element.space + "." + element.entity)
              : entityWithWarnings.push(element.entity);
          }

          if (element.analysisStatus === "E") {
            this.isVersion2
              ? entityWithErrors.push(element.space + "." + element.entity)
              : entityWithErrors.push(element.entity);
          }
          element.isShared = element.space !== this.spaceId && this.isVersion2;
        });
        this.view.getModel("entitiesModel").setProperty("/items", stats);
        this.processPayload(stats, response.data.payload);
        this.collectWarningsAndErrors(entityWithWarnings, entityWithErrors);
        this.entitiesTable?.setBusy(false);
        this.view.getModel("actionControlModel").setProperty("/enableActions", true);
        this.view.getModel("actionControlModel").setProperty("/enableFilter", true);
        this.view.getModel("actionControlModel").setProperty("/enableSort", true);
        this.view.getModel("actionControlModel").setProperty("/advisorVersion", this.detailsData.identifier);
      },
      (error) => {
        this.resetModelData();
        this.entitiesTable?.setBusy(false);
        const errorCode = error[0]?.status;
        if (errorCode === 404) {
          this.view.getModel("selectedModel")?.setProperty("/globalPayload", this.getText("PG_NO_RESULT"));
          this.view.getModel("selectedModel")?.setProperty("/showglobalPayload", true);
        } else {
          MessageHandler.exception({
            message: this.getText("getResultsError"),
            exception: error,
          });
        }
      }
    );
  }

  private collectWarningsAndErrors(entityWithWarnings, entityWithErrors) {
    const messageArray = [];
    this.view.getModel("entitiesModel").setProperty("/warningMsgCount", entityWithWarnings.length);
    this.view.getModel("entitiesModel").setProperty("/errorMsgCount", entityWithErrors.length);
    this.view
      .getModel("entitiesModel")
      .setProperty("/msgButtonType", entityWithErrors.length === 0 ? "Critical" : "Negative");
    this.view
      .getModel("entitiesModel")
      .setProperty(
        "/msgButtonIcon",
        entityWithErrors.length === 0 ? "sap-icon://message-warning" : "sap-icon://message-error"
      );
    this.view
      .getModel("entitiesModel")
      .setProperty(
        "/msgButtonCount",
        entityWithErrors.length === 0 ? entityWithWarnings.length : entityWithErrors.length
      );
    this.view
      .getModel("entitiesModel")
      .setProperty(
        "/msgButtonTooltip",
        entityWithErrors.length === 0 ? this.getText("warningMsgButtonTooltip") : this.getText("errorMsgButtonTooltip")
      );
    entityWithErrors.forEach((element) => {
      // filter error from payload
      const errors = this.entityPayloadObject[element]?.payload?.filter((e) => e.Category === "Error");
      const space = this.entityPayloadObject[element]?.stat?.space;
      errors?.forEach((err) => {
        messageArray.push({
          message: err.Msg,
          entity: element,
          space: space,
          type: sap.ui.core.MessageType.Error,
        });
      });
    });
    entityWithWarnings.forEach((element) => {
      // filter warning from payload
      const warnings = this.entityPayloadObject[element]?.payload?.filter((e) => e.Category === "Warning");
      const space = this.entityPayloadObject[element]?.stat?.space;
      warnings?.forEach((warn) => {
        messageArray.push({
          message: warn.Msg,
          entity: element,
          space: space,
          type: sap.ui.core.MessageType.Warning,
        });
      });
    });
    this.view.getModel("entitiesModel").setProperty("/totalMsgCount", messageArray.length);
    this.view.getModel("entitiesModel").setProperty("/warningsAndErrors", messageArray);
  }

  async processPayload(stat, payLoad: any) {
    this.entityPayloadObject = {};
    stat.forEach((element) => {
      let elementName = this.isVersion2 && element.space ? element.space + "." + element.entity : element.entity;
      this.entityPayloadObject[elementName] = {
        stat: element,
        payload: [],
      };
    });

    for (let i = 0; i < payLoad.length; i++) {
      const obj = payLoad[i];
      const entityNameWithSpace = this.isVersion2 && obj.space ? obj.space + "." + obj.entityName : obj.entityName;
      if (!payLoadExcludeList.includes(obj.messageKey)) {
        const text = this.getText(obj.messageKey, obj.params);
        if (text !== "") {
          this.entityPayloadObject[entityNameWithSpace]?.payload.push({
            Category: getLevel(obj.level),
            Msg: text,
          });
        }
      }
    }
  }

  onTableSelectionChange() {
    const selectedNo = this.entitiesTable?.getSelectedItems()?.length;
    const selectedEntities = [];
    const selectedEntitiesObject = [];
    this.view.getModel("entitiesModel").setProperty("/showStartAnalyzerWarning", false);
    if (selectedNo > 0) {
      this.view.getModel("actionControlModel").setProperty("/enableStart", true);
      this.entitiesTable?.getSelectedItems()?.forEach((element) => {
        const obj = element.getBindingContext("entitiesModel").getObject();
        selectedEntities.push(obj.entity);
        selectedEntitiesObject.push(obj);
        if (![EntityLabel.VIEW, EntityLabel.SQL_SCRIPT_VIEW].includes(obj?.label)) {
          this.view.getModel("entitiesModel").setProperty("/showStartAnalyzerWarning", true);
        }
      });
      this.view.getModel("entitiesModel").setProperty("/selectedEntities", selectedEntities);
      this.view.getModel("entitiesModel").setProperty("/selectedEntitiesObject", selectedEntitiesObject);
    } else {
      this.view.getModel("actionControlModel").setProperty("/enableStart", false);
    }
    this.view.getModel("actionControlModel").setProperty("/enableMonitorNavigation", true);
    if (
      selectedNo === 1 &&
      [
        EntityLabel.VIEW,
        EntityLabel.REMOTE_TABLE,
        EntityLabel.SQL_SCRIPT_VIEW,
        EntityLabel.INTELLIGENT_LOOKUP,
        EntityLabel.LOCAL_TABLE,
        EntityLabel.LOCAL_TABLE_FILE,
      ].includes(selectedEntitiesObject[0]?.label)
    ) {
      this.view.getModel("actionControlModel").setProperty("/enableNavigation", true);
      if (selectedEntitiesObject[0].entity === this.objectId && selectedEntitiesObject[0].space === this.spaceId) {
        this.view.getModel("actionControlModel").setProperty("/enableMonitorNavigation", false);
      }
    } else {
      this.view.getModel("actionControlModel").setProperty("/enableNavigation", false);
    }
  }

  public async showThirdColumnDetails(entityName, space?) {
    const entityNameWithSpace = space ? space + "." + entityName : entityName;
    const data = this.entityPayloadObject[entityNameWithSpace];
    if (data) {
      if (!this.taskController) {
        this.taskview = sap.ui
          .getCore()
          .byId("shellMainContent---dataIntegrationComponent---taskLog") as sap.ui.core.mvc.View;
        this.taskController = this.taskview.getController() as TaskLogClass;
      }
      const felxColumn = this.taskview?.getContent()[1];
      const endColumnPage = felxColumn?.getContent()[0]?.getEndColumnPages()[0] as sap.m.ScrollContainer;
      endColumnPage?.getContent()[0]?.setVisible(false);

      if (!this.entityDetailsView) {
        let viewName = require("../../viewanalyzer/view/EntityDetailsCard.view.xml");
        this.entityDetailsView = await sap.ui.core.mvc.View.create({
          type: sap.ui.core.mvc.ViewType.XML,
          id: "entityDetailsView",
          viewName: viewName,
        });
        endColumnPage?.addContent(this.entityDetailsView);
      }
      this.taskController.setLayout("ThreeColumnsMidExpanded");
      this.entityDetailsView?.getController()?.setDetailsData(data);
    }
  }

  private createErrorWarningMessagePopover(): void {
    this["ErrorWarningMessagePopOver"] = new sap.m.MessagePopover({
      id: "warningMessagePopover",
      items: {
        path: "entitiesModel>/warningsAndErrors",
        template: new sap.m.MessageItem({
          title: "{entitiesModel>message}",
          subtitle: "{entitiesModel>entity}",
          type: "{entitiesModel>type}",
          markupDescription: true,
          activeTitle: true,
        }),
      },
      activeTitlePress: (oEvent) => {
        const item = oEvent.getParameters().item;
        const message = item.getBindingContext("entitiesModel").getObject();

        const rowId = this.getEntityRowId(message);
        this.navigateToField(rowId);
        this.selectRowField(rowId);
      },
      groupItems: true,
    }).addStyleClass("warningMessagePopover");

    this.getView().byId("errorWarningMsgButton").addDependent(this["ErrorWarningMessagePopOver"]);
  }

  private selectRowField(rowId: any) {
    const table = this.entitiesTable;
    table.removeSelections();
    table.setSelectedItemById(rowId);
  }

  private getEntityRowId(msg) {
    const items = this.entitiesTable.getItems();
    const entityName = msg?.entity.split(".")[1];
    // eslint-disable-next-line @typescript-eslint/prefer-for-of
    for (let i = 0; i < items.length; i++) {
      const obj = items[i].getBindingContext("entitiesModel").getObject();
      if (obj.entity === entityName && obj.space === msg.space) {
        return items[i].getId();
      }
    }
  }

  public openWarningMessagePopover(oEvent) {
    this["ErrorWarningMessagePopOver"].toggle(oEvent.getSource() as sap.m.Button);
  }

  private navigateToField(fieldId: any) {
    const oField = sap.ui.getCore().byId(fieldId);
    if (oField) {
      const element = oField.$().get(0);
      const y = element.getBoundingClientRect().top;
      if (y > window.innerHeight) {
        element.scrollIntoView();
      } else {
        const msgPopoverBtn = this.getView().byId("errorWarningMsgButton") as sap.m.Button;
        msgPopoverBtn.firePress();
      }
    }
  }

  openPartiallyPersistedLink(oEvent: any, inputParametersDetails: any, latestUpdate: any): void {
    const link = oEvent.getSource();
    openPartiallyPersistedInfo(link, inputParametersDetails, latestUpdate, this);
  }

  async onEntitySelection(oEvent) {
    const entityObject = oEvent.getSource().getBindingContext("entitiesModel").getObject();
    this.showThirdColumnDetails(entityObject.entity, entityObject.space);
  }

  public async initializeLineageView() {
    if (!this.lineageView) {
      this.lineageView = await sap.ui.core.mvc.View.create({
        type: sap.ui.core.mvc.ViewType.XML,
        id: "lineageView",
        viewName: require("../../viewanalyzer/view/Lineage.view.xml"),
        viewData: {
          spaceId: this.spaceId,
          objectId: this.objectId,
        },
      });
    }
    await this.lineageView?.getController()?.analyseView(this.spaceId, this.objectId);
  }

  async onViewChange(oEvent) {
    const container = this.view.byId("entitiesListView--lineageViewContainer") as sap.m.VBox;
    // Lineage view
    if (oEvent?.getParameters()?.item?.getKey() === "lineage") {
      container.addItem(this.lineageView);
      this.lineageView.getController().updateSearchModel();
      this.entitiesTable?.setVisible(false);
      container.setVisible(true);
      this.view.getModel("actionControlModel").setProperty("/enableFilter", false);
      this.view.getModel("actionControlModel").setProperty("/enableSort", false);
      this.lineageView?.getController()?.handleSelections();
    } else {
      // List view
      container.setVisible(false);
      this.entitiesTable?.setVisible(true);
      const seg = this.view.byId("entitiesListView--entityViewButton") as sap.m.SegmentedButton;
      this.view.getModel("actionControlModel").setProperty("/enableFilter", true);
      this.view.getModel("actionControlModel").setProperty("/enableSort", true);
      if (this.lineageView && this.lineageView.getController()) {
        this.entitiesTable?.removeSelections(true);
        const selItems = this.lineageView.getController().getSelectedEntities();
        const items = new Set(selItems);
        const selItemIndex = [];
        Array.from(items.values()).forEach((element) => {
          this.entitiesTable?.getItems().forEach((item) => {
            const obj = item.getBindingContext("entitiesModel").getObject().entity;
            if (obj === element) {
              item.setSelected(true);
            }
          });
        });
        setTimeout(() => {
          seg?.getButtons()[0]?.firePress();
          seg?.setSelectedItem(seg?.getItems()[0]);
        }, 100);
      }
    }
  }

  private getStartAnalyzerDialog() {
    const dialog = require("../view/StartAnalyzerDialog.view.xml");
    this.analyzerDialog?.dialog?.destroy();
    this.analyzerView = undefined;
    if (!this.analyzerView) {
      this.analyzerView = sap.ui.view({
        id: this.getView().getId() + "--startAnalyzerrDialog",
        type: sap.ui.core.mvc.ViewType.XML,
        viewName: dialog,
      });
    }
    this.analyzerDialog = this.analyzerView.getController() as StartAnalyzerDialog;
    return this.analyzerDialog;
  }

  startAnalyzerForSelectedEntities() {
    // call the same start analyzer dialog
    const name = this.view.getModel("detailsModel").getProperty("/displayName");
    const selectedEntities = this.view.getModel("entitiesModel").getProperty("/selectedEntities");
    const selectedEntitiesObject = this.view.getModel("entitiesModel").getProperty("/selectedEntitiesObject");
    const showAnalyzerWarning = this.view.getModel("entitiesModel").getProperty("/showStartAnalyzerWarning");
    if (showAnalyzerWarning) {
      sap.m.MessageBox.show(this.getText("analyzerWarningMsg"), {
        icon: sap.m.MessageBox.Icon.WARNING,
        title: this.getText("analyzerWarning"),
        actions: [sap.m.MessageBox.Action.OK],
        onClose: async (action: sap.m.MessageBox.Action) => {
          this.destroy();
        },
      });
    } else {
      const startDialog = this.getStartAnalyzerDialog();
      startDialog.openDialog(false, this.objectId, this.spaceId, selectedEntitiesObject);
    }
  }

  private getPersistencyAnalyzerDialog() {
    const dialog = require("../view/AnalyzerDetailsDialog.view.xml");
    if (!this.persistencyAnalyzerView) {
      this.persistencyAnalyzerView = sap.ui.view({
        id: this.getView().getId() + "--AnalyzerDetailsDialog",
        type: sap.ui.core.mvc.ViewType.XML,
        viewName: dialog,
      });
    }
    this.analyzerDetailsDialog = this.persistencyAnalyzerView.getController() as AnalyzerDetailsDialog;
    return this.analyzerDetailsDialog;
  }

  inspectView() {
    const perAdv = this.getPersistencyAnalyzerDialog();
    const name = this.view.getModel("detailsModel").getProperty("/displayName");
    this.detailsData.entityStats = this.detailsData.entityStats.filter((e) => e.entity !== "unauthorized");
    perAdv.startPersistencyAdvisor(this.objectId, this.spaceId, name, this.detailsData);
  }

  toMonitor() {
    const selectedView = (this.view.byId("entityViewButton") as sap.m.SegmentedButton).getSelectedKey();
    let entity;
    if (selectedView === "lineage") {
      const selEntities = this.view.getModel("entitiesModel").getProperty("/selectedEntities");

      let selectedelementName = selEntities[0].split("/")[0];
      let selectedElementSpace = selEntities[0].split("/")[1];

      entity = this.view
        .getModel("entitiesModel")
        ?.getData()
        ?.items.filter((e) => e.entity === selectedelementName);
      entity = entity ? entity[0] : {};
    } else {
      entity = this.entitiesTable.getSelectedItem().getBindingContext("entitiesModel").getObject();
    }
    const spaceName = entity.space ? entity.space : this.spaceId;
    let shellHash;
    let targetHash;
    switch (entity?.label) {
      case EntityLabel.SQL_SCRIPT_VIEW:
      case EntityLabel.VIEW:
        shellHash = "dataintegration&/di";
        targetHash = `${encodeURIComponent(spaceName)}/viewMonitor/${encodeURIComponent(entity.entity)}`;
        break;
      case EntityLabel.LOCAL_TABLE:
        shellHash = "dataintegration&/di";
        targetHash = `${encodeURIComponent(spaceName)}/localTableMonitor/${encodeURIComponent(entity.entity)}`;
        break;
      case EntityLabel.LOCAL_TABLE_FILE:
        shellHash = "dataintegration&/di";
        const ishdlfspace = UISpaceCapabilities.get().hasCapability(spaceName, "hdlfStorage");
        if (ishdlfspace) {
          targetHash = `${encodeURIComponent(spaceName)}/localTableMonitor/${encodeURIComponent(entity.entity)}`;
        } else {
          targetHash = `${encodeURIComponent(spaceName)}/localTableMonitor`;
        }
        break;
      case EntityLabel.REMOTE_TABLE:
        shellHash = "dataintegration&/di";
        targetHash = `${encodeURIComponent(spaceName)}/remoteTableMonitor/${encodeURIComponent(entity.entity)}`;
        break;
      default:
        break;
    }
    const sUrl = `#/${shellHash}/${targetHash}`;

    sap.m.URLHelper.redirect(sUrl, false);
  }

  toEditor() {
    const selectedView = (this.view.byId("entityViewButton") as sap.m.SegmentedButton).getSelectedKey();
    let entity;
    if (selectedView === "lineage") {
      const selEntities = this.view.getModel("entitiesModel").getProperty("/selectedEntities");

      let selectedelementName = selEntities[0].split("/")[0];
      let selectedElementSpace = selEntities[0].split("/")[1];

      entity = this.view
        .getModel("entitiesModel")
        ?.getData()
        ?.items.filter((e) => {
          return e.entity === selectedelementName;
        });
      entity = entity ? entity[0] : {};
    } else {
      entity = this.entitiesTable.getSelectedItem().getBindingContext("entitiesModel").getObject();
    }
    const spaceName = entity.space ? entity.space : this.spaceId;
    let shellHash = "databuilder&/db";
    let targetHash = `${encodeURIComponent(spaceName)}/${encodeURIComponent(entity.entity)}`;
    const sUrl = `#/${shellHash}/${targetHash}`;
    sap.m.URLHelper.redirect(sUrl, false);
  }

  handleSortButtonPressed() {
    if (!this.entitySortDialog) {
      let fragmentID;
      fragmentID = require(`../view/settings/EntitiesSortDialog.fragment.xml`);
      this.entitySortDialog = sap.ui.xmlfragment("Settings", fragmentID, this) as any;
      this.getView().addDependent(this.entitySortDialog);
    }
    this.entitySortDialog.open();
  }

  handleFilterButtonPressed() {
    if (!this.entityFilterDialog) {
      let fragmentID;
      fragmentID = require(`../view/settings/EntitiesFilterDialog.fragment.xml`);
      this.entityFilterDialog = sap.ui.xmlfragment("Settings", fragmentID, this) as any;
      this.getView().addDependent(this.entityFilterDialog);
    }
    this.entityFilterDialog.open();
  }

  handleSortDialogConfirm(oEvent) {
    const mParams = oEvent.getParameters(),
      oBinding = this.entitiesTable.getBinding("items") as sap.ui.model.ListBinding,
      sPath = mParams.sortItem.getKey(),
      aSorters = [],
      bDescending = mParams.sortDescending;
    aSorters.push(new sap.ui.model.Sorter(sPath, bDescending));
    oBinding.sort(aSorters);
  }

  resetSearchField(searchFieldId) {
    const searchField = this.view.byId(searchFieldId) as sap.m.SearchField;
    searchField?.["clear"]();
  }

  handleFilterDialogConfirm(oEvent) {
    const mParams = oEvent.getParameters(),
      oBinding = this.entitiesTable.getBinding("items") as sap.ui.model.json.JSONListBinding,
      aFilters = [];
    const input = oEvent?.getSource()?.getFilterItems()[0]?.getCustomControl();
    let filterString = mParams.filterString || this.getText("FilteredByTxt");
    const inputValue = input.getValue();
    if (inputValue && inputValue !== "") {
      aFilters.push(new sap.ui.model.Filter("entity", sap.ui.model.FilterOperator.Contains, inputValue));
      filterString +=
        (filterString !== this.getText("FilteredByTxt") ? ", " : "") +
        ` ${this.getText("entityName")} (*${inputValue}*)`;
    }
    const input1 = oEvent?.getSource()?.getFilterItems()[1]?.getCustomControl();
    const inputValue1 = input1.getValue();
    if (inputValue1 && inputValue1 !== "") {
      aFilters.push(new sap.ui.model.Filter("space", sap.ui.model.FilterOperator.Contains, inputValue1));
      filterString +=
        (filterString !== this.getText("FilteredByTxt") ? ", " : "") +
        ` ${this.getText("spaceName")} (*${inputValue1}*)`;
    }
    mParams.filterItems.forEach(function (oItem) {
      let aSplit = oItem.getKey().split("___"),
        sOperator = aSplit[1],
        sValue1 = aSplit[2],
        sValue2 = aSplit[3],
        sPath = aSplit[0];
      if (sValue1 === "true" || sValue1 === "false") {
        sValue1 = sValue1 === "true" ? true : false;
      }
      if (!isNaN(sValue1)) {
        sValue1 = Number(sValue1);
      }
      const oFilter = new sap.ui.model.Filter(sPath, sOperator, sValue1, sValue2);
      aFilters.push(oFilter);
    });
    // apply filter settings
    oBinding.filter(aFilters);
    // filter strip
    const isFiltered = aFilters.length > 0;
    this.byId("entitiesTable-FilterBar").setVisible(isFiltered);
    this.byId("entitiesTableFilterBarText").setText(isFiltered ? filterString : "");
  }

  handleResetFilters() {
    var oCustomFilter = (sap.ui.getCore().byId("Settings--entityFilterDialog") as any)?.getFilterItems()[0],
      inputBox = oCustomFilter.getCustomControl();
    inputBox.setValue("");
    var oCustomFilter1 = (sap.ui.getCore().byId("Settings--entityFilterDialog") as any)?.getFilterItems()[1],
      inputBox1 = oCustomFilter1.getCustomControl();
    inputBox1.setValue("");
    oCustomFilter.setFilterCount(0);
    oCustomFilter.setSelected(false);
    oCustomFilter1.setFilterCount(0);
    oCustomFilter1.setSelected(false);
  }

  handleClearFilter() {
    // Get the binding of the entities table
    var oTable = this.byId("entitiesListView--entitiesListTable");
    var oBinding = oTable.getBinding("items");
    // Clear all filters
    oBinding.filter([]);
    // Reset the filter bar visibility and text
    this.byId("entitiesTable-FilterBar").setVisible(false);
    this.byId("entitiesTableFilterBarText").setText("");
    // Reset the filter dialog if it is open
    if (this.entityFilterDialog) {
      this.handleResetFilters();
    }
  }
}

export const entitiesController = smartExtend(
  BaseController,
  "sap.cdw.components.viewanalyzer.controller.Entities",
  EntitiesClass
);

sap.ui.define("sap/cdw/components/viewanalyzer/controller/Entities.controller", [], function () {
  return entitiesController;
});
