#XFLD: Label for Dialog Title
DialogTitle=Настройки анализа ракурса
#XFLD: Text for Intro in Dialog
DialogIntroText=Анализ ракурса может улучшать модель данных. Он позволяет анализировать потребление памяти, но это увеличивает загрузку ресурсов и время выполнения.
#XFLD: Label
StartViewAnalyzerText=Запустить анализ ракурса:
#XFLD: Label for with Memory Consumption
withMC=С потреблением памяти
#XFLD: Label for without Memory Consumption
withoutMC=Без потребления памяти
#XFLD: Label for generate plan viz
generatePlanVizBtnTxt=Сгенерировать файл плана анализа SQL
#XFLD: Label for plan viz information
planvizTxt=Для создания файла плана анализа SQL потребуются дополнительные системные ресурсы. Необходимо установить инструмент анализа SQL для SAP HANA, чтобы визуализировать его.
#XFLD: Label for with Memory Consumption for selected entities
withMCForSelectedEntities=С потреблением памяти для выбранных сущностей
#XFLD: Label for warning
MCWarning=Анализ ракурсов остановится, если один из анализируемых ракурсов превысит максимальное потребление памяти при моделировании устойчивого сохранения данных.
#XFLD: Label for Max memory
MaxMC=Максимальное потребление памяти:
#XFLD: Label for Start
Start=Запустить
#XFLD: Label for Cancel
Cancel=Отменить
#XMSG Message toast
startAnalysisSuccess=Выполняется анализ ракурсов
startAnalyserWithoutMemory=Анализ ракурсов выполняется без потребления памяти
startAnalyserWithoutMemoryWithExplainPlan=Анализ ракурсов выполняется для генерации плана Explain.
startAnalyserWithMemory=Анализ ракурсов выполняется с потреблением памяти
startAnalyserWithPlanviz=Анализ ракурса выполняется с генерацией плана SQL
#XFLD: Label
gib=ГиБ
#XFLD: Label
percent=%
#XMSG: Failure message
startAnalysisFailed=Не удалось запустить анализ ракурсов
#XMSG: Tooltip
maxMemoryTooltip=Максимальное потребление памяти ограничено лимитом памяти инструкций из конфигурации рабочей нагрузки пространства, если он задан. Если он не задан, то используется общая доступная память системы.

#XFLD: Label for View Analyzer Findings
viewanalyzerfindings=Результаты анализа ракурсов
#XFLD: Label for Type
type=Тип
#XFLD: Label for Number of Joins
noofjoins=Число соединений
#XFLD: Label for Number of Joins
noofpartitions=Разделы ракурса
#XFLD: Label for Peak Memory
peakmemory=Пиковая память
#XFLD: Label for Used in-Memory
usedinmemory=Использование памяти для данных
#XFLD: Label
useddisk=Использование диска для данных
#XFLD: Label
noofrows=Число строк
#XFLD: Label for Messages
messages=Сообщения
#XFLD: Label for category
category=Категория
#XFLD: Label for Sort
sortTxt=Сортировка
#XFLD: Label for Filter
filterTxt=Фильтр
#XFLD: Label for Filtered By:
FilteredByTxt=Отфильтровано по:
#XFLD: Label for button
enterFullScreenTxt=Перейти в полноэкранный режим
#XFLD: Label for button
exitFullScreenTxt=Выйти из полноэкранного режима
#XFLD: Label for button
closeRightColumn=Закрыть правый столбец
#XFLD: Label for Column
spaceName=Пространство



#~~~~~~~~~~~~~~~~~~Texts for View Analyzer~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Label for category
Category=Категория
#XFLD: Label for message
Message=Сообщение
#XFLD: Label for type
Type=Тип

#XMSG Entity stats Information messages for advisor
VIEW_PERSISTENCY_ADVISOR=Результаты анализа для ракурса
#XBUT: Button to close View Analyzer dialog
CLOSE=Закрыть
#XBUT: Button to export result of View Analyzer as file in CSV format
EXPORT=Экспорт CSV


#XFLD: Label for warning message
warning=Предупреждение
#XFLD: Label for error message
error=Ошибка

#XFLD: Label for object of type "View"
View=Ракурс
#XFLD: Label for object of type "Remote Table"
RemoteTable=Дистанционная таблица
#XFLD: Label for object of type "Local table"
LocalTable=Локальная таблица
#XFLD: Label for object of type "Local table (file)"
LocalTableFile=Локальная таблица (файловая)
#XFLD: Label for object of type "External"
External=Внешний
#XFLD: Label for object of type "Data Access Control"
DataAccessControl=Контроль доступа к данным
#XFLD: Label for object of type "SQLScript View"
SqlScriptView=Ракурс SQLScript
#XFLD: Label for object shared from another space
SharedObject=Общий объект
#XFLD: Label for IntelligentLookup
IntelligentLookup=Интеллектуальный поиск
#XFLD: Label for External entity
ExternalEntity=Внешняя сущность

#XMSG: Information on persistency status of a view
isPersisted=Устойчиво сохранено: {0}
#XMSG: Memory consumption in Mebibyte
memoryUsed=Использование памяти: {0} МиБ
#XMSG: Disk storage used in Mebibyte
diskUsed=Использование диска: {0} МиБ
#XMSG: Memory consumption in Mebibyte
UImemsizeinMB=Использование памяти: {0} МиБ
#XMSG: Disk storage used in Mebibyte
UIdisksizeinMB=Использование диска: {0} МиБ
#XMSG: Number of join operations
joins=Соединения: {0}
#XMSG: Number of union operations
unions=Объединения: {0}
#XMSG: Number of partitions
partitions=Разделы: {0}
#XMSG: Adapters used for a remote table
adapterName=Используемый адаптер: {0}
#XMSG: Message indicating data access (virtual, persisted , remote ..)
accessStatus=Доступ к данным: {0}
#XMSG: Peak Memory
peakMemoryinMB=Пиковая память: {0} МиБ
#XMSG: Number of rows
numberRecords=Строки: {0}
#XLBL: Generate Explain plan
generateExplainPlan=Сгенерировать план Explain


#XMSG: Remote table with limited capabilities such as filter pushdown, and information on the connection and adapter used
PA_ADAPTER=Дистанционная таблица "{0}" в соединении "{2}" использует адаптер "{1}" с отсутствием ограничительных возможностей, например, передачи фильтров или поддержки лимитов.
#XMSG: View uses View Persistency Partitioning feature, and consumes entities based on adapter with limited capabilities
PA_ADAPTER_CONSUMER=Ракурс "{0}" разделен и потребляет сущности на основе адаптера с ограничительными возможностями.
#XMSG: View contains JOIN and UNION operators
PA_VIEW_COMPLEXITY=Ракурс "{0}" имеет операторов соединения: {1} и операторов объединения: {2}.
#XMSG: For a given entity, the required (descent entities in the lineage graph) are displayed
PA_DEPENDENCIES=Сущность "{0}" требует следующих сущностей: {1}
#XMSG: For view based on SQL script, no evaluation will be done
PA_SQLscript_SKIP=Обнаружен ракурс SQLScript "{0}". Анализ не выполняется для операторов соединения и объединения.
#XMSG: For shared object, no evaluation will be done
PA_SHARED_OBJECT_SKIP=Обнаружен общий объект "{0}", анализ не выполняется.
#XMSG: For external entities in Open SQL schema, no evaluation will be done
PA_EXTERNAL_ENTITY_SKIP=Обнаружена внешняя сущность "{0}", анализ не выполняется.
#XMSG: Message indicating missing of transported statistics for a remote table
PA_STATISTICS=Дистанционная таблица "{0}" не содержит перенесенную статистику.
#XMSG: View cannot be persisted, because it has input parameters
PA_VIEW_HAS_PARAMETERS=Ракурс "{0}" недоступен для устойчивого сохранения, так как имеет параметры ввода.
#XMSG: Entity is not fully deployed
PA_NOT_FULLY_DEPLOYED=Сущность "{0}" развернута не полностью.
#XMSG: Entity has design time error and will not be analyzed.
PA_DESIGN_TIME_ERROR=Сущность "{0}" имеет ошибку времени дизайна и не может быть проанализирована.
#XMSG: View is persisted and numbers of JOINs, UNIONs and remote tables hidden are displayed
PA_PERSIST_HIDES=Ракурс "{0}" устойчиво сохранен. Кэш скрывает соединения ({1}), объединения ({2}) и дистанционные таблицы ({3}).
#XMSG: Number of Joins, Unions, Remote Tables, limited adapters, replicated and persisted views found during evaluation
PA_AGGREGATED=Анализ охватывает соединения ({0}), объединения ({1}), дистанционные таблицы ({2}) ({3} дистанционных с {4} ограниченными адаптерами, {5} тиражированных) и устойчивые ракурсы ({6}).
#XMSG: Message in case of error, with refererence to Task Log for more details. */
PA_ERROR_MESSAGE=Ошибка при анализе сущности {0}. Подробнее см. в журнале задач.
#XMSG: Message in case of warning, with refererence to Task Log for more details. */
PA_WARNING_MESSAGE=Предупреждение при анализе сущности {0}. Подробнее см. в журнале задач.
#XMSG: No analysis is performed for entities outside space {0}
PA_NO_MEMORYANALYSIS=Анализ памяти выполняется только для сущностей в пространстве "{0}".
#XMSG: Message in case of error with refererence to Task Log for more details. */
PG_NO_RESULT=Произошла ошибка, и не удалось определить сущности. Подробнее см. в журнале задач.

#XFLD: Label indicating virtual data access
PA_VIRTUAL=Виртуально
#XFLD: Label indicating persisted data access
PA_PERSISTED=Устойчиво
#XFLD: Label indicating partially persisted data access
PA_PARTIALLY_PERSISTED=Частично устойчиво сохранено
#XFLD: Label indicating replicated (snapshot) data access
PA_SNAPSHOT=Тиражировано (мгновенный снимок)
#XFLD: Label indicating replicated (real-time) data access
PA_REPLICATED=Тиражировано (в реальном времени)
#XFLD: Label indicating remote data access
PA_REMOTE=Дистанционно
#XFLD: Label indicating local data access
PA_LOCAL=Локально

#XFLD: Label for busy dialog
busydialog-analyzer=Извлекаем сведения о памяти для пространства...
#XFLD: Label for Messages header
messagesHeader=Сообщения ({0})
#XTIT: Label for Objects header
objectsHeader=Объекты ({0})

#XFLD Entities Tab Name
entitiesTab=Сущности
#XFLD Start Analyzer button
startAnalyzer=Запустить анализ
#XFLD inspect
Inspect=Проверить
#XFLD Navigate to  monitor
gotoMonitor=Перейти к монитору
#XFLD Navigate to editor
gotoEditor=Перейти к редактору
#XFLD list view
list=Ракурс списка
#XFLD lineage view
lineage=Ракурс происхождения
#XFLD name
entityName=Имя сущности
#XFLD persisted column
persisted=Устойчиво
#XFLD no of joins
joinsColumn=Соединения
#XFLD no of partitions
partitionsColumn=Разделы ракурса
#XFLD Last run peak memory column
lastRunPeakmemoryColumn=Пиковая память последнего прогона (МиБ)
#XFLD rows
rows=Строки
#XFLD Last run duration
lastRunDuration=Продолжительность последнего прогона
#XFLD adapter
adapter=Адаптер
#XFLD Navigate to monitor/editor
navigationMenu=Открыть в мониторе/редакторе
#XFLD warnings button tooltip
warningMsgButtonTooltip=Предупреждения
#XFLD errors button tooltip
errorMsgButtonTooltip=Ошибки



#~~~~~~~~~~~~~~~~~~Filter text View Analyzer~~~~~~~~~~~~~~~~~~~~~~~
#XFLD Filter text
true=ИСТИНА
#XFLD Filter text
false=ЛОЖЬ
#XFLD Filter text
lessthan5Joins=Меньше 5 соединений
#XFLD Filter text
morethan5Joins=Больше 5 соединений
#XFLD Filter text
lessthan5Partitions=Меньше 5 разделов
#XFLD Filter text
lessthan10Partitions=Меньше 10 разделов
#XFLD Filter text
morethan10Partitions=Больше 10 разделов
#XFLD Filter text
lessthan100=Меньше 100 МБ
#XFLD Filter text
morethan100=Больше 100 МБ
#XFLD Filter text
lessthan500=Меньше 500 МБ
#XFLD Filter text
morethan500=Больше 500 МБ
#XFLD Filter text
Warning=Предупреждение
#XFLD Filter text
Error=Ошибка

analyzerWarningMsg=Анализ не поддерживается для некоторых из выбранных сущностей. Выберите сущности типа "Ракурс" или "Ракурс SQLScript".
analyzerWarning=Предупреждение
getResultsError=Ошибка при считывании результатов анализа ракурса.
entitiesNotAnalysed=Если анализ ракурса выполняется без потребления памяти, некоторые поля могут быть пустыми.


#~~~~~~~~~View Analyzer Persistency Guidance~~~~~~~~~~~~~~~~

#XFLD Data Access
DATA_ACCESS=Доступ к данным
#XFLD Data Persistence Candidate Score
DATA_PERSISTENCE_CANDIDATE_SCORE=Оценка кандидата для устойчивого сохранения данных
#XFLD Sumber of sources
NUMBER_SOURCES=Источники
#XFLD Number of targets
NUMBER_TARGETS=Цели
#XFLD Overall number of sources
OVERALL_NUMBER_SOURCES=Общее число источников
#XFLD Number of federated remote tables (with limited adapters)
NUMBER_REMOTE_TABLES_LIMITED_ADAPTERS=Федеративные дистанционные таблицы (с ограниченными возможностями адаптера)
#XFLD Number of local tables (file)
NUMBER_LOCAL_TABLES_FILE=Локальные таблицы (файловые)
#XFLD Simulation status
STATUS_LAST_PERSISTENCY_TASK=Статус последней задачи устойчивого сохранения данных
#XFLD Simulation status
STATUS_PERSISTENCY_SIMULATION=Статус моделирования
#XFLD Deployement status
NOT_FULLY_DEPLOYED=Не полностью развернуто
#XFLD Input parameters
INPUT_PARAMETERS=Параметры ввода
#XFLD Number of Parameters (with Default Value)
NUMBER_INPUT_PARAMETERS_DEFAULT_VALUE=Число параметров (со значением по умолчанию)
#XFLD View has DAC
SOURCE_VIEW_HAS_DAC=Контроль доступа к данным
#XFLD View uses remote tables with user propagation
REMOTE_TABLES_USER_PROPAGATION=Дистанционные таблицы с распространением пользователей
#XFLD: Label for Type
ENTITY_TYPE=Тип
#XFLD: Label for Number of Partitions
PARTITIONS=Разделы
#XFLD Data lineage
DATA_LINEAGE=Происхождение данных
#XFLD Number Joins
NUMBER_JOINS=Число соединений
#XFLD Number Unions
NUMBER_UNIONS=Число объединений
#XFLD Peak Memory in  (MiB)
PEAK_MEMORY_MIB=Пиковая память (МиБ)
#XFLD Number of rows
NUMBER_ROWS=Число строк
#XFLD Memory used for storage in MiB
MEMORY_USED_FOR_STORAGE_MIB=Использование памяти для данных (МиБ)
#XFLD Disk used for storage in MiB
DISK_USED_FOR_STORAGE_MIB=Использование диска для данных (МиБ)
#XFLD collapse persisted views button label
collapsePersistedViews=Свернуть устойчиво сохраненные ракурсы


#XFLD Best Candidate
BEST_CANDIDATE=Лучшая опция
#XFLD Best alternative candidate
BEST_ALTERNATIVE_CANDIDATE=Лучшая альтернатива
#XFLD Good candidate
GOOD_CANDIDATE=Хорошо
#XFLD Not applicable
NOT_APPLICABLE=Неприменимо
#XFLD Open Monitor
openMonitor=Открыть монитор
NOT_PERSISTABLE=Недоступно для устойчивого сохранения

#XFLD Failed
FAILED=Не выполнено
#XFLD true
TRUE=истина
#XFLD: tooltip for table column
txtViewDataAccessTooltip=Указывает доступность ракурса

#XMSG: Persisting this view is likely to materially improve the performance of analyzed view
PG_BEST_CANDIDATE=Устойчивое сохранение этого ракурса, вероятно, значительно повысит производительность анализируемого ракурса.
#XMSG: Persisting this view is likely to materially improve the performance of analyzed view
PG_BEST_ALTERNATIVE=Устойчивое сохранение этого ракурса, вероятно, повысит производительность анализируемого ракурса.
#XMSG: Persisting this view is likely to materially improve the performance of analyzed view
PG_GOOD_CANDIDATE=Устойчивое сохранение этого ракурса может повысить производительность анализируемого ракурса.
#XMSG: However, it could not be persisted during the last run.
PG_PERSISTENCY_FAILED=Однако его не удалось устойчиво сохранить во время последнего прогона.
#XMSG: However, we could not simulate persisting its data during the analysis.
PG_SIMULATION_FAILED=Однако нам не удалось смоделировать устойчивое сохранение его данных во время анализа.
#XMSG: This source cannot be persisted because it got input parameters.
PG_INPUT_PARAMETER=Этот ракурс недоступен для устойчивого сохранения, так как имеет параметры ввода.
#XMSG: View cannot be persisted because it has more than one parameter,
PG_MULTIPLE_INPUT_PARAMETERS=Этот ракурс недоступен для устойчивого сохранения, так как имеет больше одного параметра.
#XMSG: View cannot be persisted because it has a parameter with no default value.
PG_INPUT_PARAMETER_WITHOUT_DEFAULT_VALUE=Этот ракурс недоступен для устойчивого сохранения, так как имеет параметр без значения по умолчанию.
#XMSG: View can only be partially persisted because it has an input parameter with a default value.
PG_INPUT_PARAMETER_WITH_DEFAULT_VALUE=Этот ракурс доступен только для частичного устойчивого сохранения, так как имеет параметр ввода со значением по умолчанию.
#XMSG: This view cannot be persisted, because it consumes views with data access control.
PG_SOURCE_VIEW_HAS_DAC=Этот ракурс недоступен для устойчивого сохранения, так как потребляет ракурсы с контролем доступа к данным.
#XMSG: This view cannot be persisted, because it consumes remote tables with user propagation.
PG_VIEW_HAS_REMOTE_TABLES_USER_PROPAGATION=Этот ракурс недоступен для устойчивого сохранения, так как он потребляет дистанционные таблицы с распространением пользователей.
#XMSG: Message when the view with the  best persistence candidate score cannot be displayed
PG_BEST_CANDIDATE_NOT_VISIBLE=Невозможно просмотреть ракурс с лучшей оценкой кандидата для устойчивого сохранения данных. Он в другом пространстве, на которое у вас нет полномочий на просмотр. Чтобы его увидеть, попросите администратора дать вам необходимые полномочия.
#XMSG: Message when entities cannot be displayed due to missing permissions
PA_UNAUTHORIZED=Некоторые сущности невозможно просмотреть, так как они в другом пространстве, на которое у вас нет полномочий на просмотр. Чтобы их увидеть, попросите администратора дать вам необходимые полномочия.
#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Настройки, использованные для последнего выполнения устойчивого сохранения данных:
#XMSG: Message for input parameter name
inputParameterLabel=Параметр ввода
#XMSG: Message for input parameter value
inputParameterValueLabel=Значение
#XMSG: Message for persisted data
inputParameterPersistedLabel=Устойчиво сохранено в
#XMSG: This view is already persisted
PG_VIEW_PERSISTED=Этот ракурс уже устойчиво сохранен.
#XMSG: This view is already partially persisted
PG_VIEW_PARTIALLY_PERSISTED=Этот ракурс уже устойчиво сохранен частично.
#XMSG: Remote tables are not analyzed for persistence
PG_REMOTE_TABLES=Дистанционные таблицы не проанализированы для устойчивого сохранения.
#XMSG: Local tables are not analyzed for persistence
PG_LOCAL_TABLES=Локальные таблицы не проанализированы для устойчивого сохранения.
#XMSG: This entity is not analyzed for persistence
PG_NOT_ANALYZED=Эта сущность не проанализирована для устойчивого сохранения.
#XMSG: This view is not analyzed for persistency because it belongs to a persisted branch
PG_BELONG_PERSISTED_BRANCH=Этот ракурс не проанализирован для устойчивого сохранения, так как он относится к устойчиво сохраненной ветви.

#~~~~~~~~~~~~View Analyzer Explain plan~~~~~~~~~~~~~~~~
#XFLD: Label for Explain plan
ExplainPlan=План Explain
#NOTR: Label for OperatorName
OperatorName=OPERATOR_NAME
#NOTR: Label for OperatorDetails
OperatorDetails=OPERATOR_DETAILS
#NOTR: Label for OperatorProperties
OperatorProperties=OPERATOR_PROPERTIES
#NOTR: Label for ExecutionEngine
ExecutionEngine=EXECUTION_ENGINE
#NOTR: Label for DatabaseName
DatabaseName=DATABASE_NAME
#NOTR: Label for SchemaName
SchemaName=SCHEMA_NAME
#NOTR: Label for TableName
TableName=TABLE_NAME
#NOTR: Label for TableType
TableType=TABLE_TYPE
#NOTR: Label for TableSize
TableSize=TABLE_SIZE
#NOTR: Label for Output Size
OutputSize=OUTPUT_SIZE
#NOTR: Label for Subtree Cost
SubtreeCost=SUBTREE_COST
#XFLD: Label for close
close=Закрыть
#XFLD: Label for download
downloadExplainPlan=Выгрузить
#XFLD: Label for expandAll
expandAll=Развернуть все
#XFLD: Label for collapseAll
collapseAll=Свернуть все
#XMSG: Message for Explain plan
Morethan1000records=Сгенерированный план Explain невозможно отобразить. Он содержит более 1000 строк. Чтобы просмотреть все строки, выгрузите файл.
#XFLD LABEL for Information
Information=Информация
#XMSG Message for Explain plan
downloadCompleted=Выгрузка завершена
#XFLD: Label for Clear Filter
ClearFilter=Очистить фильтр

navigateToMonitor=Перейти к сведениям монитора ракурсов?
navigateToMonitorTitle=Монитор ракурсов
errorWhileFetchingExplainPlan=Ошибка при вызове плана Explain


