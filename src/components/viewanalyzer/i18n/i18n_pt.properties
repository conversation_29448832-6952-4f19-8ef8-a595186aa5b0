#XFLD: Label for Dialog Title
DialogTitle=Configurações do Analisador de visões
#XFLD: Text for Intro in Dialog
DialogIntroText=O Analisador de visões ajuda você a melhorar seu modelo de dados. Ele pode analisar o consumo de memória, mas isso aumentará os recursos consumidos e o tempo de execução.
#XFLD: Label
StartViewAnalyzerText=Iniciar Analisador de visões:
#XFLD: Label for with Memory Consumption
withMC=Com consumo de memória
#XFLD: Label for without Memory Consumption
withoutMC=Sem consumo de memória
#XFLD: Label for generate plan viz
generatePlanVizBtnTxt=Gerar arquivo de plano de Analisador de SQL
#XFLD: Label for plan viz information
planvizTxt=O Analisador demandará recursos de sistema adicionais para criar o arquivo de plano do Analisador de SQL. Observe que você deve ter a ferramenta Analisador de SQL para SAP HANA instalada para visualizá-lo.
#XFLD: Label for with Memory Consumption for selected entities
withMCForSelectedEntities=Com consumo de memória para entidades selecionadas
#XFLD: Label for warning
MCWarning=O Analisador de visões será interrompido se uma das visões analisadas exceder o consumo máximo de memória enquanto a persistência de dados é simulada.
#XFLD: Label for Max memory
MaxMC=Consumo máximo de memória:
#XFLD: Label for Start
Start=Iniciar
#XFLD: Label for Cancel
Cancel=Cancelar
#XMSG Message toast
startAnalysisSuccess=Estamos executando o Analisador de visões
startAnalyserWithoutMemory=O Analisador de visões está executando sem consumo de memória
startAnalyserWithoutMemoryWithExplainPlan=O Analisador de visões está executando para gerar o plano explicativo.
startAnalyserWithMemory=O Analisador de visões está executando com consumo de memória
startAnalyserWithPlanviz=O Analisador de visões está executando com geração de plano SQL
#XFLD: Label
gib=GiB
#XFLD: Label
percent=%
#XMSG: Failure message
startAnalysisFailed=Falha ao iniciar o Analisador de visões
#XMSG: Tooltip
maxMemoryTooltip=O consumo máximo de memória é limitado pelo limite de memória de instrução da configuração de carga de trabalho da área, se especificado. Caso isso não esteja definido, ele é limitado pelo total de memória de sistema disponível.

#XFLD: Label for View Analyzer Findings
viewanalyzerfindings=Resultados do Analisador de visões
#XFLD: Label for Type
type=Tipo
#XFLD: Label for Number of Joins
noofjoins=Número de junções
#XFLD: Label for Number of Joins
noofpartitions=Partições de visão
#XFLD: Label for Peak Memory
peakmemory=Pico de memória
#XFLD: Label for Used in-Memory
usedinmemory=Memória usada para armazenamento
#XFLD: Label
useddisk=Disco usado para armazenamento
#XFLD: Label
noofrows=Número de linhas
#XFLD: Label for Messages
messages=Mensagens
#XFLD: Label for category
category=Categoria
#XFLD: Label for Sort
sortTxt=Ordenar
#XFLD: Label for Filter
filterTxt=Filtrar
#XFLD: Label for Filtered By:
FilteredByTxt=Filtrado por:
#XFLD: Label for button
enterFullScreenTxt=Entrar em modo de tela inteira
#XFLD: Label for button
exitFullScreenTxt=Sair do modo de tela inteira
#XFLD: Label for button
closeRightColumn=Fechar coluna direita
#XFLD: Label for Column
spaceName=Área



#~~~~~~~~~~~~~~~~~~Texts for View Analyzer~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Label for category
Category=Categoria
#XFLD: Label for message
Message=Mensagem
#XFLD: Label for type
Type=Tipo

#XMSG Entity stats Information messages for advisor
VIEW_PERSISTENCY_ADVISOR=Resultados do Analyzer para visualização
#XBUT: Button to close View Analyzer dialog
CLOSE=Fechar
#XBUT: Button to export result of View Analyzer as file in CSV format
EXPORT=Exportar CSV


#XFLD: Label for warning message
warning=Aviso
#XFLD: Label for error message
error=Erro

#XFLD: Label for object of type "View"
View=Exibir
#XFLD: Label for object of type "Remote Table"
RemoteTable=Tabela remota
#XFLD: Label for object of type "Local table"
LocalTable=Tabela local
#XFLD: Label for object of type "Local table (file)"
LocalTableFile=Tabela local (arquivo)
#XFLD: Label for object of type "External"
External=Externo
#XFLD: Label for object of type "Data Access Control"
DataAccessControl=Controle de acesso a dados
#XFLD: Label for object of type "SQLScript View"
SqlScriptView=Visão SQLScript
#XFLD: Label for object shared from another space
SharedObject=Objeto compartilhado
#XFLD: Label for IntelligentLookup
IntelligentLookup=Pesquisa inteligente
#XFLD: Label for External entity
ExternalEntity=Entidade externa

#XMSG: Information on persistency status of a view
isPersisted=Persistidos: {0}
#XMSG: Memory consumption in Mebibyte
memoryUsed=Consumo de memória: {0} MiB
#XMSG: Disk storage used in Mebibyte
diskUsed=Utilização de disco: {0} MiB
#XMSG: Memory consumption in Mebibyte
UImemsizeinMB=Consumo de memória: {0} MiB
#XMSG: Disk storage used in Mebibyte
UIdisksizeinMB=Utilização de disco: {0} MiB
#XMSG: Number of join operations
joins=Junções: {0}
#XMSG: Number of union operations
unions=Uniões: {0}
#XMSG: Number of partitions
partitions=Partições: {0}
#XMSG: Adapters used for a remote table
adapterName=Adaptador usado: {0}
#XMSG: Message indicating data access (virtual, persisted , remote ..)
accessStatus=Acesso aos dados: {0}
#XMSG: Peak Memory
peakMemoryinMB=Pico de memória: {0} MiB
#XMSG: Number of rows
numberRecords=Linhas: {0}
#XLBL: Generate Explain plan
generateExplainPlan=Gerar plano explicativo


#XMSG: Remote table with limited capabilities such as filter pushdown, and information on the connection and adapter used
PA_ADAPTER=A tabela remota "{0}" na conexão "{2}" usa o adaptador "{1}" com recursos limitados, por exemplo, sem condições de filtro ou suporte a LIMIT.
#XMSG: View uses View Persistency Partitioning feature, and consumes entities based on adapter with limited capabilities
PA_ADAPTER_CONSUMER=A visão "{0}" é particionada e consome entidades com base em um adaptador com recursos limitados.
#XMSG: View contains JOIN and UNION operators
PA_VIEW_COMPLEXITY=A visão "{0}" tem {1} operadores JOIN e {2} UNION.
#XMSG: For a given entity, the required (descent entities in the lineage graph) are displayed
PA_DEPENDENCIES=A entidade "{0}" requer as seguintes entidades: {1}
#XMSG: For view based on SQL script, no evaluation will be done
PA_SQLscript_SKIP=Visão SQLScript "{0}" detectada, nenhuma avaliação será feita para os operadores JOIN e UNION.
#XMSG: For shared object, no evaluation will be done
PA_SHARED_OBJECT_SKIP=Objeto compartilhado "{0}" detectado, nenhuma avaliação será realizada.
#XMSG: For external entities in Open SQL schema, no evaluation will be done
PA_EXTERNAL_ENTITY_SKIP=Entidade externa "{0}" detectada, nenhuma avaliação será feita.
#XMSG: Message indicating missing of transported statistics for a remote table
PA_STATISTICS=A tabela remota "{0}" não contém estatísticas transportadas.
#XMSG: View cannot be persisted, because it has input parameters
PA_VIEW_HAS_PARAMETERS=A visão "{0}" não pode ser persistida, ela tem parâmetros de entrada.
#XMSG: Entity is not fully deployed
PA_NOT_FULLY_DEPLOYED=Entidade "{0}" não implementada totalmente.
#XMSG: Entity has design time error and will not be analyzed.
PA_DESIGN_TIME_ERROR=A entidade "{0}" tem um erro de tempo de design e não pode ser analisado.
#XMSG: View is persisted and numbers of JOINs, UNIONs and remote tables hidden are displayed
PA_PERSIST_HIDES=Visão "{0}" persistida. O cache oculta {1} JOINs, {2} UNIONs e {3} tabelas remotas.
#XMSG: Number of Joins, Unions, Remote Tables, limited adapters, replicated and persisted views found during evaluation
PA_AGGREGATED=A avaliação alcança {0} JOINs, {1} UNIONs, {2} tabelas remotas ({3} remotas com {4} adaptadores limitados, {5} replicadas) e {6} VIEWs persistidas.
#XMSG: Message in case of error, with refererence to Task Log for more details. */
PA_ERROR_MESSAGE=Ocorreu um erro ao analisar a entidade {0}. Consulte Log de tarefa para obter mais detalhes.
#XMSG: Message in case of warning, with refererence to Task Log for more details. */
PA_WARNING_MESSAGE=Ocorreu um aviso ao analisar a entidade {0}. Consulte Log de tarefa para obter mais detalhes.
#XMSG: No analysis is performed for entities outside space {0}
PA_NO_MEMORYANALYSIS=Análise da memória só é realizada para entidades na área "{0}".
#XMSG: Message in case of error with refererence to Task Log for more details. */
PG_NO_RESULT=Ocorreu um erro e não foi possível determinar as entidades. Consulte o Log de tarefas para obter mais detalhes.

#XFLD: Label indicating virtual data access
PA_VIRTUAL=Virtual
#XFLD: Label indicating persisted data access
PA_PERSISTED=Persistido
#XFLD: Label indicating partially persisted data access
PA_PARTIALLY_PERSISTED=Persistido parcialmente
#XFLD: Label indicating replicated (snapshot) data access
PA_SNAPSHOT=Replicado (instantâneo)
#XFLD: Label indicating replicated (real-time) data access
PA_REPLICATED=Replicado (tempo real)
#XFLD: Label indicating remote data access
PA_REMOTE=Remoto
#XFLD: Label indicating local data access
PA_LOCAL=Local

#XFLD: Label for busy dialog
busydialog-analyzer=Buscando detalhes de memória da área...
#XFLD: Label for Messages header
messagesHeader=Mensagens ({0})
#XTIT: Label for Objects header
objectsHeader=Objetos ({0})

#XFLD Entities Tab Name
entitiesTab=Entidades
#XFLD Start Analyzer button
startAnalyzer=Iniciar Analisador
#XFLD inspect
Inspect=Inspecionar
#XFLD Navigate to  monitor
gotoMonitor=Ir para monitor
#XFLD Navigate to editor
gotoEditor=Ir para editor
#XFLD list view
list=Visão de lista
#XFLD lineage view
lineage=Visão de linhagem
#XFLD name
entityName=Nome da entidade
#XFLD persisted column
persisted=Persistido
#XFLD no of joins
joinsColumn=Junções
#XFLD no of partitions
partitionsColumn=Partições de visão
#XFLD Last run peak memory column
lastRunPeakmemoryColumn=Pico de memória da última execução (MiB)
#XFLD rows
rows=Linhas
#XFLD Last run duration
lastRunDuration=Duração da última execução
#XFLD adapter
adapter=Adaptador
#XFLD Navigate to monitor/editor
navigationMenu=Abrir no Monitor/Editor
#XFLD warnings button tooltip
warningMsgButtonTooltip=Avisos
#XFLD errors button tooltip
errorMsgButtonTooltip=Erros



#~~~~~~~~~~~~~~~~~~Filter text View Analyzer~~~~~~~~~~~~~~~~~~~~~~~
#XFLD Filter text
true=VERDADEIRO
#XFLD Filter text
false=FALSO
#XFLD Filter text
lessthan5Joins=Menos de 5 junções
#XFLD Filter text
morethan5Joins=Mais de 5 junções
#XFLD Filter text
lessthan5Partitions=Menos de 5 partições
#XFLD Filter text
lessthan10Partitions=Menos de 10 partições
#XFLD Filter text
morethan10Partitions=Mais de 10 partições
#XFLD Filter text
lessthan100=Menos de 100 MB
#XFLD Filter text
morethan100=Mais de 100 MB
#XFLD Filter text
lessthan500=Menos de 500 MB
#XFLD Filter text
morethan500=Mais de 500 MB
#XFLD Filter text
Warning=Aviso
#XFLD Filter text
Error=Erro

analyzerWarningMsg=A análise não é suportada por algumas entidades selecionadas. Selecione entidades do tipo Visão ou Visão SQLScript.
analyzerWarning=Aviso
getResultsError=Erro ao ler os resultados da análise de visão.
entitiesNotAnalysed=Quando a visão é analisada sem consumo de memória, alguns campos podem ficar vazios.


#~~~~~~~~~View Analyzer Persistency Guidance~~~~~~~~~~~~~~~~

#XFLD Data Access
DATA_ACCESS=Acesso aos dados
#XFLD Data Persistence Candidate Score
DATA_PERSISTENCE_CANDIDATE_SCORE=Pontuação de candidato de persistência de dados
#XFLD Sumber of sources
NUMBER_SOURCES=Origens
#XFLD Number of targets
NUMBER_TARGETS=Destinos
#XFLD Overall number of sources
OVERALL_NUMBER_SOURCES=Número total de origens
#XFLD Number of federated remote tables (with limited adapters)
NUMBER_REMOTE_TABLES_LIMITED_ADAPTERS=Tabelas remotas federadas (com recursos de adaptador limitados)
#XFLD Number of local tables (file)
NUMBER_LOCAL_TABLES_FILE=Tabelas locais (arquivo)
#XFLD Simulation status
STATUS_LAST_PERSISTENCY_TASK=Status da última tarefa de persistência de dados
#XFLD Simulation status
STATUS_PERSISTENCY_SIMULATION=Status de simulação
#XFLD Deployement status
NOT_FULLY_DEPLOYED=Não implementado completamente
#XFLD Input parameters
INPUT_PARAMETERS=Parâmetros de entrada
#XFLD Number of Parameters (with Default Value)
NUMBER_INPUT_PARAMETERS_DEFAULT_VALUE=Número de parâmetros (com valor padrão)
#XFLD View has DAC
SOURCE_VIEW_HAS_DAC=Controle de acesso aos dados
#XFLD View uses remote tables with user propagation
REMOTE_TABLES_USER_PROPAGATION=Tabela remotas com propagação de usuários
#XFLD: Label for Type
ENTITY_TYPE=Tipo
#XFLD: Label for Number of Partitions
PARTITIONS=Partições
#XFLD Data lineage
DATA_LINEAGE=Linhagem de dados
#XFLD Number Joins
NUMBER_JOINS=Número de junções
#XFLD Number Unions
NUMBER_UNIONS=Número de uniões
#XFLD Peak Memory in  (MiB)
PEAK_MEMORY_MIB=Pico de memória (MiB)
#XFLD Number of rows
NUMBER_ROWS=Número de linhas
#XFLD Memory used for storage in MiB
MEMORY_USED_FOR_STORAGE_MIB=Memória usada para armazenamento (MiB)
#XFLD Disk used for storage in MiB
DISK_USED_FOR_STORAGE_MIB=Disco usado para armazenamento (MiB)
#XFLD collapse persisted views button label
collapsePersistedViews=Comprimir visões persistidas


#XFLD Best Candidate
BEST_CANDIDATE=Melhor opção
#XFLD Best alternative candidate
BEST_ALTERNATIVE_CANDIDATE=Melhor alternativa
#XFLD Good candidate
GOOD_CANDIDATE=Bom
#XFLD Not applicable
NOT_APPLICABLE=N/A
#XFLD Open Monitor
openMonitor=Abrir Monitor
NOT_PERSISTABLE=Não persistível

#XFLD Failed
FAILED=Com falha
#XFLD true
TRUE=verdadeiro
#XFLD: tooltip for table column
txtViewDataAccessTooltip=Indica a disponibilidade da visão

#XMSG: Persisting this view is likely to materially improve the performance of analyzed view
PG_BEST_CANDIDATE=A persistência desta visão vai provavelmente melhorar de forma substancial o desempenho da visão analisada.
#XMSG: Persisting this view is likely to materially improve the performance of analyzed view
PG_BEST_ALTERNATIVE=A persistência desta visão vai provavelmente melhorar o desempenho da visão analisada.
#XMSG: Persisting this view is likely to materially improve the performance of analyzed view
PG_GOOD_CANDIDATE=A persistência desta visão pode melhorar o desempenho da visão analisada.
#XMSG: However, it could not be persisted during the last run.
PG_PERSISTENCY_FAILED=No entanto, não foi possível persisti-la durante a última execução.
#XMSG: However, we could not simulate persisting its data during the analysis.
PG_SIMULATION_FAILED=No entanto, não foi possível simular a persistência dos dados durante a análise.
#XMSG: This source cannot be persisted because it got input parameters.
PG_INPUT_PARAMETER=Não é possível persistir esta visão, ela recebeu parâmetros de entrada.
#XMSG: View cannot be persisted because it has more than one parameter,
PG_MULTIPLE_INPUT_PARAMETERS=Não é possível persistir esta visão, ela tem mais de um parâmetro.
#XMSG: View cannot be persisted because it has a parameter with no default value.
PG_INPUT_PARAMETER_WITHOUT_DEFAULT_VALUE=Não é possível persistir esta visão, ela tem um parâmetro sem valor padrão.
#XMSG: View can only be partially persisted because it has an input parameter with a default value.
PG_INPUT_PARAMETER_WITH_DEFAULT_VALUE=Esta visão só pode ser persistida parcialmente, ela tem um parâmetro de entrada sem valor padrão.
#XMSG: This view cannot be persisted, because it consumes views with data access control.
PG_SOURCE_VIEW_HAS_DAC=Não é possível persistir esta visão, ela consome visões com controle de acesso aos dados.
#XMSG: This view cannot be persisted, because it consumes remote tables with user propagation.
PG_VIEW_HAS_REMOTE_TABLES_USER_PROPAGATION=Não é possível persistir esta visão, ela consome tabelas remotas com propagação de usuários.
#XMSG: Message when the view with the  best persistence candidate score cannot be displayed
PG_BEST_CANDIDATE_NOT_VISIBLE=Não é possível exibir a visão com a melhor pontuação de candidato a persistência de dados. Ela está em outra área na qual você não tem permissão de visualizar. Para acessá-la, solicite os privilégios necessários a um administrador.
#XMSG: Message when entities cannot be displayed due to missing permissions
PA_UNAUTHORIZED=Algumas entidades não podem ser exibidas porque elas estão em uma área na qual você não tem permissão para visualizar. Para acessá-las, solicite os privilégios necessários a um administrador.
#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Configurações usadas na última execução de persistência de dados:
#XMSG: Message for input parameter name
inputParameterLabel=Parâmetro de entrada
#XMSG: Message for input parameter value
inputParameterValueLabel=Valor
#XMSG: Message for persisted data
inputParameterPersistedLabel=Persistido às
#XMSG: This view is already persisted
PG_VIEW_PERSISTED=Esta visão já está persistida.
#XMSG: This view is already partially persisted
PG_VIEW_PARTIALLY_PERSISTED=Esta visão já está parcialmente persistida.
#XMSG: Remote tables are not analyzed for persistence
PG_REMOTE_TABLES=Tabelas remotas não foram analisadas para persistência.
#XMSG: Local tables are not analyzed for persistence
PG_LOCAL_TABLES=Tabelas locais não foram analisadas para persistência.
#XMSG: This entity is not analyzed for persistence
PG_NOT_ANALYZED=Essa entidade não foi analisada para persistência.
#XMSG: This view is not analyzed for persistency because it belongs to a persisted branch
PG_BELONG_PERSISTED_BRANCH=Esta visão não foi analisada para persistência porque ela pertence a uma ramificação persistida.

#~~~~~~~~~~~~View Analyzer Explain plan~~~~~~~~~~~~~~~~
#XFLD: Label for Explain plan
ExplainPlan=Plano explicativo
#NOTR: Label for OperatorName
OperatorName=OPERATOR_NAME
#NOTR: Label for OperatorDetails
OperatorDetails=OPERATOR_DETAILS
#NOTR: Label for OperatorProperties
OperatorProperties=OPERATOR_PROPERTIES
#NOTR: Label for ExecutionEngine
ExecutionEngine=EXECUTION_ENGINE
#NOTR: Label for DatabaseName
DatabaseName=DATABASE_NAME
#NOTR: Label for SchemaName
SchemaName=SCHEMA_NAME
#NOTR: Label for TableName
TableName=TABLE_NAME
#NOTR: Label for TableType
TableType=TABLE_TYPE
#NOTR: Label for TableSize
TableSize=TABLE_SIZE
#NOTR: Label for Output Size
OutputSize=OUTPUT_SIZE
#NOTR: Label for Subtree Cost
SubtreeCost=SUBTREE_COST
#XFLD: Label for close
close=Fechar
#XFLD: Label for download
downloadExplainPlan=Baixar
#XFLD: Label for expandAll
expandAll=Expandir tudo
#XFLD: Label for collapseAll
collapseAll=Comprimir tudo
#XMSG: Message for Explain plan
Morethan1000records=O plano explicativo gerado não pode ser exibido porque contém mais de 1000 linhas. Baixe o arquivo para ver todas as linhas.
#XFLD LABEL for Information
Information=Informações
#XMSG Message for Explain plan
downloadCompleted=Download concluído
#XFLD: Label for Clear Filter
ClearFilter=Limpar filtro

navigateToMonitor=Deseja navegar para Detalhes do monitor de visões?
navigateToMonitorTitle=Monitor de visões
errorWhileFetchingExplainPlan=Ocorreu um erro ao buscar o plano explicativo


