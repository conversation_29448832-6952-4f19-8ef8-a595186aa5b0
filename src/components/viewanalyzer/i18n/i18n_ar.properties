#XFLD: Label for Dialog Title
DialogTitle=إعدادات محلل العرض
#XFLD: Text for Intro in Dialog
DialogIntroText=يساعدك محلل العرض في تحسين نموذج بياناتك. يمكن أن يحلل استهلاك الذاكرة، ولكن هذا سيزيد من الموارد المستهلكة ووقت التشغيل.
#XFLD: Label
StartViewAnalyzerText=بدء محلل العرض:
#XFLD: Label for with Memory Consumption
withMC=مع استهلاك الذاكرة
#XFLD: Label for without Memory Consumption
withoutMC=دون استهلاك الذاكرة
#XFLD: Label for generate plan viz
generatePlanVizBtnTxt=إنشاء ملف خطة محلِل SQL
#XFLD: Label for plan viz information
planvizTxt=يتطلب المحلِل موارد نظام إضافية لإنشاء ملف خطة محلِل SQL. لاحظ أن يجب أن تكون قد قمت بتثبيت أداة محلِل SQL لـ SAP HANA لتتمكن من عرضه.
#XFLD: Label for with Memory Consumption for selected entities
withMCForSelectedEntities=مع استهلاك الذاكرة للكيانات المحددة
#XFLD: Label for warning
MCWarning=سيتوقف محلل العرض إذا تجاوز أحد العروض التي تم تحليلها الحد الأقصى لاستهلاك الذاكرة، بينما تتم محاكاة التخزين الدائم للبيانات الخاصة به.
#XFLD: Label for Max memory
MaxMC=الحد الأقصى لاستهلاك الذاكرة:
#XFLD: Label for Start
Start=بدء
#XFLD: Label for Cancel
Cancel=إلغاء
#XMSG Message toast
startAnalysisSuccess=نحن نقوم بتشغيل محلل العرض
startAnalyserWithoutMemory=محلل العرض قيد التشغيل بدون استهلاك الذاكرة
startAnalyserWithoutMemoryWithExplainPlan=محلل العرض قيد التشغيل لإنشاء خطة توضيح.
startAnalyserWithMemory=محلل العرض قيد التشغيل مع استهلاك الذاكرة
startAnalyserWithPlanviz=محلِل العرض قيد التشغيل مع إنشاء خطة SQL
#XFLD: Label
gib=جيبي بايت
#XFLD: Label
percent=%
#XMSG: Failure message
startAnalysisFailed=فشل بدء محلل العرض
#XMSG: Tooltip
maxMemoryTooltip=الحد الأقصى لاستهلاك الذاكرة يقتصر على حد ذاكرة العبارة لتكوين حمل العمل للمساحة، إذا تم تحديده. إذا لم يتم تحديده هناك، فإنه محدود بإجمالي ذاكرة النظام المتوفرة.

#XFLD: Label for View Analyzer Findings
viewanalyzerfindings=نتائج محلل العرض
#XFLD: Label for Type
type=النوع
#XFLD: Label for Number of Joins
noofjoins=عدد عمليات الربط
#XFLD: Label for Number of Joins
noofpartitions=أقسام العرض
#XFLD: Label for Peak Memory
peakmemory=أقصى ذاكرة
#XFLD: Label for Used in-Memory
usedinmemory=الذاكرة المستخدمة للتخزين
#XFLD: Label
useddisk=القرص المستخدم للتخزين
#XFLD: Label
noofrows=عدد الصفوف
#XFLD: Label for Messages
messages=الرسائل
#XFLD: Label for category
category=الفئة
#XFLD: Label for Sort
sortTxt=ترتيب
#XFLD: Label for Filter
filterTxt=تصفية
#XFLD: Label for Filtered By:
FilteredByTxt=تمت التصفية حسب:
#XFLD: Label for button
enterFullScreenTxt=إدخال عرض بملء الشاشة
#XFLD: Label for button
exitFullScreenTxt=الخروج من ملء الشاشة
#XFLD: Label for button
closeRightColumn=إغلاق العمود الأيسر
#XFLD: Label for Column
spaceName=المساحة



#~~~~~~~~~~~~~~~~~~Texts for View Analyzer~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Label for category
Category=الفئة
#XFLD: Label for message
Message=الرسالة
#XFLD: Label for type
Type=النوع

#XMSG Entity stats Information messages for advisor
VIEW_PERSISTENCY_ADVISOR=نتائج المحلل للعرض
#XBUT: Button to close View Analyzer dialog
CLOSE=إغلاق
#XBUT: Button to export result of View Analyzer as file in CSV format
EXPORT=تصدير القيم المفصولة بفواصل


#XFLD: Label for warning message
warning=تحذير
#XFLD: Label for error message
error=خطأ

#XFLD: Label for object of type "View"
View=عرض
#XFLD: Label for object of type "Remote Table"
RemoteTable=الجدول البعيد
#XFLD: Label for object of type "Local table"
LocalTable=الجدول المحلي
#XFLD: Label for object of type "Local table (file)"
LocalTableFile=جدول محلي (ملف)
#XFLD: Label for object of type "External"
External=خارجي
#XFLD: Label for object of type "Data Access Control"
DataAccessControl=عنصر التحكم في الوصول إلى البيانات
#XFLD: Label for object of type "SQLScript View"
SqlScriptView=عرض SQLScript
#XFLD: Label for object shared from another space
SharedObject=الكائن المشترك
#XFLD: Label for IntelligentLookup
IntelligentLookup=البحث الذكي
#XFLD: Label for External entity
ExternalEntity=الكيان الخارجي

#XMSG: Information on persistency status of a view
isPersisted=مخزَّن بصفة دائمة: {0}
#XMSG: Memory consumption in Mebibyte
memoryUsed=استهلاك الذاكرة: {0} ميجابايت
#XMSG: Disk storage used in Mebibyte
diskUsed=القرص المستخدم: {0} ميجابايت
#XMSG: Memory consumption in Mebibyte
UImemsizeinMB=استهلاك الذاكرة: {0} ميجابايت
#XMSG: Disk storage used in Mebibyte
UIdisksizeinMB=القرص المستخدم: {0} ميجابايت
#XMSG: Number of join operations
joins=عمليات الربط: {0}
#XMSG: Number of union operations
unions=عمليات التوحيد: {0}
#XMSG: Number of partitions
partitions=التقسيمات: {0}
#XMSG: Adapters used for a remote table
adapterName=المحوِّل المستخدم: {0}
#XMSG: Message indicating data access (virtual, persisted , remote ..)
accessStatus=الوصول إلى البيانات: {0}
#XMSG: Peak Memory
peakMemoryinMB=أقصى ذاكرة: {0} ميجابايت
#XMSG: Number of rows
numberRecords=الصفوف: {0}
#XLBL: Generate Explain plan
generateExplainPlan=إنشاء خطة توضيح


#XMSG: Remote table with limited capabilities such as filter pushdown, and information on the connection and adapter used
PA_ADAPTER=الجدول البعيد "{0}" في الاتصال "{2}"يستخدم المحوِّل "{1}" مع إمكانات محدودة مفقودة، على سبيل المثال، الضغط على عامل التصفية أو دعم LIMIT.
#XMSG: View uses View Persistency Partitioning feature, and consumes entities based on adapter with limited capabilities
PA_ADAPTER_CONSUMER=العرض "{0}" ذو تقسيم ويستهلك الكيانات على أساس محوِّل ذي إمكانات محدودة.
#XMSG: View contains JOIN and UNION operators
PA_VIEW_COMPLEXITY=العرض "{0}" يحتوي على المعاملين {1} JOIN و{2} UNION.
#XMSG: For a given entity, the required (descent entities in the lineage graph) are displayed
PA_DEPENDENCIES=الكيان "{0}" يتطلب الكيانات التالية: {1}
#XMSG: For view based on SQL script, no evaluation will be done
PA_SQLscript_SKIP=تم الكشف عن عرض SQLscript "{0}". لن يتم إجراء أي تقييم لمعاملي JOIN وUNION.
#XMSG: For shared object, no evaluation will be done
PA_SHARED_OBJECT_SKIP=تم الكشف عن الكائن المشترك "{0}"، لن يتم إجراء أي تقييم.
#XMSG: For external entities in Open SQL schema, no evaluation will be done
PA_EXTERNAL_ENTITY_SKIP=تم الكشف عن الكيان الخارجي "{0}". لن يتم إجراء أي تقييم.
#XMSG: Message indicating missing of transported statistics for a remote table
PA_STATISTICS=الجدول البعيد "{0}" لا يحتوي على الإحصائيات المنقولة.
#XMSG: View cannot be persisted, because it has input parameters
PA_VIEW_HAS_PARAMETERS=لا يمكن للعرض ''{0}'' التخزين الدائم للبيانات لأنه يحتوي على معامِلات إدخال.
#XMSG: Entity is not fully deployed
PA_NOT_FULLY_DEPLOYED=الكيان ''{0}'' غير منشور بالكامل.
#XMSG: Entity has design time error and will not be analyzed.
PA_DESIGN_TIME_ERROR=الكيان ''{0}'' يوجد به خطأ في وقت التصميم ولا يمكن تحليله.
#XMSG: View is persisted and numbers of JOINs, UNIONs and remote tables hidden are displayed
PA_PERSIST_HIDES=العرض ''{0}'' مخزَّن بصفة دائم. تخفي ذاكرة التخزين المؤقت معاملات JOIN {1} ومعاملات UNION {2}، والجداول البعيدة {3}.
#XMSG: Number of Joins, Unions, Remote Tables, limited adapters, replicated and persisted views found during evaluation
PA_AGGREGATED=التقييم يصل إلى معاملات JOIN {0} ومعاملات UNION {1} والجداول البعيدة {2} ({3} عن بُعد مع المحولات المحدودة {4} و{5} المنسوخ نسخًا متماثلاً) وعروض {6} VIEW المخزنة بصفة دائمة.
#XMSG: Message in case of error, with refererence to Task Log for more details. */
PA_ERROR_MESSAGE=حدث خطأ أثناء تحليل الكيان {0}. راجع سجل المهام لمزيد من التفاصيل.
#XMSG: Message in case of warning, with refererence to Task Log for more details. */
PA_WARNING_MESSAGE=حدث تحذير أثناء تحليل الكيان {0}. راجع سجل المهام لمزيد من التفاصيل.
#XMSG: No analysis is performed for entities outside space {0}
PA_NO_MEMORYANALYSIS=يتم تنفيذ تحليل الذاكرة للكيانات في المساحة ''{0}'' فقط.
#XMSG: Message in case of error with refererence to Task Log for more details. */
PG_NO_RESULT=حدث خطأ ولم يكن من الممكن تحديد الكيانات. راجع سجل المهام لمزيد من التفاصيل.

#XFLD: Label indicating virtual data access
PA_VIRTUAL=افتراضي
#XFLD: Label indicating persisted data access
PA_PERSISTED=مخزَّن بصفة دائمة
#XFLD: Label indicating partially persisted data access
PA_PARTIALLY_PERSISTED=تم التخزين الدائم للبيانات جزئيًا
#XFLD: Label indicating replicated (snapshot) data access
PA_SNAPSHOT=منسوخ نسخًا متماثلًا (لقطة)
#XFLD: Label indicating replicated (real-time) data access
PA_REPLICATED=منسوخ نسخًا متماثلًا (الوقت الفعلي)
#XFLD: Label indicating remote data access
PA_REMOTE=بعيد
#XFLD: Label indicating local data access
PA_LOCAL=محلي

#XFLD: Label for busy dialog
busydialog-analyzer=جارٍ استدعاء تفاصيل الذاكرة بالمساحة...
#XFLD: Label for Messages header
messagesHeader=الرسائل ({0})
#XTIT: Label for Objects header
objectsHeader=الكائنات ({0})

#XFLD Entities Tab Name
entitiesTab=الكيانات
#XFLD Start Analyzer button
startAnalyzer=محلل البدء
#XFLD inspect
Inspect=فحص
#XFLD Navigate to  monitor
gotoMonitor=الانتقال إلى مراقب
#XFLD Navigate to editor
gotoEditor=الانتقال إلى محرِر
#XFLD list view
list=عرض القائمة
#XFLD lineage view
lineage=عرض أصل البيانات
#XFLD name
entityName=اسم الكيان
#XFLD persisted column
persisted=مخزَّن بصفة دائمة
#XFLD no of joins
joinsColumn=عمليات الربط
#XFLD no of partitions
partitionsColumn=أقسام العرض
#XFLD Last run peak memory column
lastRunPeakmemoryColumn=آخر تشغيل لأقصى ذاكرة (ميبي بايت)
#XFLD rows
rows=الصفوف
#XFLD Last run duration
lastRunDuration=مدة آخر تشغيل
#XFLD adapter
adapter=المحول
#XFLD Navigate to monitor/editor
navigationMenu=فتح في المراقبة/المحرر
#XFLD warnings button tooltip
warningMsgButtonTooltip=التحذيرات
#XFLD errors button tooltip
errorMsgButtonTooltip=الأخطاء



#~~~~~~~~~~~~~~~~~~Filter text View Analyzer~~~~~~~~~~~~~~~~~~~~~~~
#XFLD Filter text
true=صواب
#XFLD Filter text
false=خطأ
#XFLD Filter text
lessthan5Joins=أقل من 5 عمليات ربط
#XFLD Filter text
morethan5Joins=أكثر من 5 عمليات ربط
#XFLD Filter text
lessthan5Partitions=أقل من 5 أقسام
#XFLD Filter text
lessthan10Partitions=أقل من 10 أقسام
#XFLD Filter text
morethan10Partitions=أكثر من 10 أقسام
#XFLD Filter text
lessthan100=أقل من 100 ميجابايت
#XFLD Filter text
morethan100=أكثر من 100 ميجابايت
#XFLD Filter text
lessthan500=أقل من 500 ميجابايت
#XFLD Filter text
morethan500=أكثر من 500 ميجابايت
#XFLD Filter text
Warning=تحذير
#XFLD Filter text
Error=خطأ

analyzerWarningMsg=التحليل غير مدعوم لبعض الكيانات المحددة. يُرجى التأكد من تحديد الكيانات بالنوع View أو SQL Script View.
analyzerWarning=تحذير
getResultsError=حدث خطأ أثناء قراءة نتائج عرض التحاليل.
entitiesNotAnalysed=إذا تم تحليل العرض بدون استهلاك الذاكرة، فقد تكون بعض الحقول فارغة.


#~~~~~~~~~View Analyzer Persistency Guidance~~~~~~~~~~~~~~~~

#XFLD Data Access
DATA_ACCESS=الوصول إلى البيانات
#XFLD Data Persistence Candidate Score
DATA_PERSISTENCE_CANDIDATE_SCORE=مجموع نقاط مرشح التخزين الدائم للبيانات
#XFLD Sumber of sources
NUMBER_SOURCES=المصادر
#XFLD Number of targets
NUMBER_TARGETS=الأهداف
#XFLD Overall number of sources
OVERALL_NUMBER_SOURCES=العدد الإجمالي للمصادر
#XFLD Number of federated remote tables (with limited adapters)
NUMBER_REMOTE_TABLES_LIMITED_ADAPTERS=الجداول البعيدة الموحدة (بإمكانات محدودة للمحوِّل)
#XFLD Number of local tables (file)
NUMBER_LOCAL_TABLES_FILE=الجداول المحلية (الملف)
#XFLD Simulation status
STATUS_LAST_PERSISTENCY_TASK=حالة آخر مهمة تخزين دائم للبيانات
#XFLD Simulation status
STATUS_PERSISTENCY_SIMULATION=حالة المحاكاة
#XFLD Deployement status
NOT_FULLY_DEPLOYED=غير منشور بالكامل
#XFLD Input parameters
INPUT_PARAMETERS=معامِلات الإدخال
#XFLD Number of Parameters (with Default Value)
NUMBER_INPUT_PARAMETERS_DEFAULT_VALUE=عدد المعامِلات (بقيمة افتراضية)
#XFLD View has DAC
SOURCE_VIEW_HAS_DAC=عنصر التحكم في الوصول إلى البيانات
#XFLD View uses remote tables with user propagation
REMOTE_TABLES_USER_PROPAGATION=الجداول البعيدة بنشر المستخدم
#XFLD: Label for Type
ENTITY_TYPE=النوع
#XFLD: Label for Number of Partitions
PARTITIONS=التقسيمات
#XFLD Data lineage
DATA_LINEAGE=أصل البيانات
#XFLD Number Joins
NUMBER_JOINS=عدد عمليات الربط
#XFLD Number Unions
NUMBER_UNIONS=عدد عمليات الاتحاد
#XFLD Peak Memory in  (MiB)
PEAK_MEMORY_MIB=أقصى ذاكرة (ميبي بايت)
#XFLD Number of rows
NUMBER_ROWS=عدد الصفوف
#XFLD Memory used for storage in MiB
MEMORY_USED_FOR_STORAGE_MIB=الذاكرة المستخدمة للتخزين (ميبي بايت)
#XFLD Disk used for storage in MiB
DISK_USED_FOR_STORAGE_MIB=القرص المستخدم للتخزين (ميبي بايت)
#XFLD collapse persisted views button label
collapsePersistedViews=طي طرق العرض المخزنة دائمًا


#XFLD Best Candidate
BEST_CANDIDATE=الخيار الأفضل
#XFLD Best alternative candidate
BEST_ALTERNATIVE_CANDIDATE=البديل الأفضل
#XFLD Good candidate
GOOD_CANDIDATE=جيد
#XFLD Not applicable
NOT_APPLICABLE=غير قابل للتطبيق
#XFLD Open Monitor
openMonitor=فتح المراقبة
NOT_PERSISTABLE=غير قابل للتخزين الدائم

#XFLD Failed
FAILED=فاشل
#XFLD true
TRUE=صواب
#XFLD: tooltip for table column
txtViewDataAccessTooltip=يشير ذلك إلى توفر العرض

#XMSG: Persisting this view is likely to materially improve the performance of analyzed view
PG_BEST_CANDIDATE=من المحتمل أن يؤدي التخزين الدائم لطريقة العرض هذه إلى تحسين مادي في أداء طريقة العرض التي تم تحليلها ماديًا.
#XMSG: Persisting this view is likely to materially improve the performance of analyzed view
PG_BEST_ALTERNATIVE=من المحتمل أن يؤدي التخزين الدائم لطريقة العرض هذه إلى تحسين أداء طريقة العرض التي تم تحليلها.
#XMSG: Persisting this view is likely to materially improve the performance of analyzed view
PG_GOOD_CANDIDATE=قد يؤدي التخزين الدائم لطريقة العرض هذه إلى تحسين أداء طريقة العرض التي تم تحليلها.
#XMSG: However, it could not be persisted during the last run.
PG_PERSISTENCY_FAILED=ومع ذلك، تعذر تخزينها تخزينًا دائمًا أثناء التشغيل الأخير.
#XMSG: However, we could not simulate persisting its data during the analysis.
PG_SIMULATION_FAILED=ومع ذلك، لم نتمكن من محاكاة التخزين الدائم لبياناتها أثناء التحليل.
#XMSG: This source cannot be persisted because it got input parameters.
PG_INPUT_PARAMETER=لا يمكن التخزين الدائم لطريقة العرض هذه لأنها تحتوي على معامِلات إدخال.
#XMSG: View cannot be persisted because it has more than one parameter,
PG_MULTIPLE_INPUT_PARAMETERS=لا يمكن تخزين طريقة العرض هذه تخزينًا دائمًا لأنها تحتوي على أكثر من معامل واحد.
#XMSG: View cannot be persisted because it has a parameter with no default value.
PG_INPUT_PARAMETER_WITHOUT_DEFAULT_VALUE=لا يمكن تخزين طريقة العرض هذه تخزينًا دائمًا لأنها تحتوي على معامل دون قيمة افتراضية.
#XMSG: View can only be partially persisted because it has an input parameter with a default value.
PG_INPUT_PARAMETER_WITH_DEFAULT_VALUE=لا يمكن تخزين طريقة العرض هذه تخزينًا دائمًا جزئيًا لأنها تحتوي على معامل إدخال بقيمة افتراضية.
#XMSG: This view cannot be persisted, because it consumes views with data access control.
PG_SOURCE_VIEW_HAS_DAC=لا يمكن التخزين الدائم لطريقة العرض هذه لأنها تستخدم طرق عرض بعناصر تحكم في الوصول إلى البيانات.
#XMSG: This view cannot be persisted, because it consumes remote tables with user propagation.
PG_VIEW_HAS_REMOTE_TABLES_USER_PROPAGATION=لا يمكن التخزين الدائم لطريقة العرض هذه، لأنه يستهلك جداول بعيدة مع نشر المستخدم.
#XMSG: Message when the view with the  best persistence candidate score cannot be displayed
PG_BEST_CANDIDATE_NOT_VISIBLE=لا يمكن إظهار العرض الذي يحتوي على أفضل مجموع نقاط لمرشح التخزين الدائم للبيانات. إنه في مساحة أخرى ليس لديك إذن بعرضه. ولرؤيته، اطلب من مسؤول تزويدك بالامتيازات المفقودة.
#XMSG: Message when entities cannot be displayed due to missing permissions
PA_UNAUTHORIZED=لا يمكن عرض بعض الكيانات لأنها في مساحة غير مسموح لك بعرضها. للاطلاع عليها، اطلب من المسؤول تزويدك بالامتيازات المفقودة.
#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=الإعدادات المستخدمة لآخر تشغيل للتخزين الدائم للبيانات:
#XMSG: Message for input parameter name
inputParameterLabel=معامل الإدخال
#XMSG: Message for input parameter value
inputParameterValueLabel=القيمة
#XMSG: Message for persisted data
inputParameterPersistedLabel=تم التخزين الدائم للبيانات في
#XMSG: This view is already persisted
PG_VIEW_PERSISTED=يتم تخزين طريقة العرض هذه تخزينًا دائمًا بالفعل.
#XMSG: This view is already partially persisted
PG_VIEW_PARTIALLY_PERSISTED=تم تخزين طريقة العرض هذه تخزينًا دائمًا جزئيًا بالفعل.
#XMSG: Remote tables are not analyzed for persistence
PG_REMOTE_TABLES=لم يتم تحليل الجداول البعيدة للتخزين الدائم للبيانات.
#XMSG: Local tables are not analyzed for persistence
PG_LOCAL_TABLES=لم يتم تحليل الجداول المحلية للتخزين الدائم للبيانات.
#XMSG: This entity is not analyzed for persistence
PG_NOT_ANALYZED=لم يتم تحليل هذا الكيان للتخزين الدائم للبيانات.
#XMSG: This view is not analyzed for persistency because it belongs to a persisted branch
PG_BELONG_PERSISTED_BRANCH=لم يتم تحليل طريقة العرض هذه للتخزين الدائم لأنها تنتمي إلى فرع مخزَّن دائمًا.

#~~~~~~~~~~~~View Analyzer Explain plan~~~~~~~~~~~~~~~~
#XFLD: Label for Explain plan
ExplainPlan=خطة التوضيح
#NOTR: Label for OperatorName
OperatorName=OPERATOR_NAME
#NOTR: Label for OperatorDetails
OperatorDetails=OPERATOR_DETAILS
#NOTR: Label for OperatorProperties
OperatorProperties=OPERATOR_PROPERTIES
#NOTR: Label for ExecutionEngine
ExecutionEngine=EXECUTION_ENGINE
#NOTR: Label for DatabaseName
DatabaseName=DATABASE_NAME
#NOTR: Label for SchemaName
SchemaName=SCHEMA_NAME
#NOTR: Label for TableName
TableName=TABLE_NAME
#NOTR: Label for TableType
TableType=TABLE_TYPE
#NOTR: Label for TableSize
TableSize=TABLE_SIZE
#NOTR: Label for Output Size
OutputSize=OUTPUT_SIZE
#NOTR: Label for Subtree Cost
SubtreeCost=SUBTREE_COST
#XFLD: Label for close
close=إغلاق
#XFLD: Label for download
downloadExplainPlan=تنزيل
#XFLD: Label for expandAll
expandAll=توسيع الكل
#XFLD: Label for collapseAll
collapseAll=طي الكل
#XMSG: Message for Explain plan
Morethan1000records=لا يمكن عرض خطة التفسير المنشأة؛ حيث إنها تحتوي على أكثر من 1000 صف. قم بتنزيل الملف لعرض جميع الصفوف.
#XFLD LABEL for Information
Information=معلومات
#XMSG Message for Explain plan
downloadCompleted=اكتمل التنزيل
#XFLD: Label for Clear Filter
ClearFilter=مسح عامل التصفية

navigateToMonitor=هل تريد الانتقال إلى "عرض تفاصيل المراقب"؟
navigateToMonitorTitle=عرض المراقب
errorWhileFetchingExplainPlan=حدث خطأ أثناء استدعاء خطة التوضيح


