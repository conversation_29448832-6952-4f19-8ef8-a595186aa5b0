#XFLD: Label for Dialog Title
DialogTitle=Skatu analizatora iestatījumi
#XFLD: Text for Intro in Dialog
DialogIntroText=Skatu analizators palīdz uzlabot jūsu datu modeli. Tas var analizēt atmiņas patēriņu, taču tas palielinās patērētos resursus un izpildlaiku.
#XFLD: Label
StartViewAnalyzerText=Startēt skatu analizatoru:
#XFLD: Label for with Memory Consumption
withMC=Ar atmiņas patēriņu
#XFLD: Label for without Memory Consumption
withoutMC=Bez atmiņas patēriņa
#XFLD: Label for generate plan viz
generatePlanVizBtnTxt=Ģenerēt SQL analizētāja plāna failu
#XFLD: Label for plan viz information
planvizTxt=Analizētājam būs nepieciešami papildu sistēmas resursi, lai izveidot SQL analizētāja plāna failu. Ņemiet vērā, ka ir jābūt instalētam SQL analizētāja rīkam platformai SAP HANA, lai to vizualizētu.
#XFLD: Label for with Memory Consumption for selected entities
withMCForSelectedEntities=Ar atmiņas patēriņu atlasītajām entītijām
#XFLD: Label for warning
MCWarning=Skatu analizatora darbība tiks apturēta, ja kāds no analizētajiem skatiem pārsniegs maksimālo atmiņas patēriņu, kamēr tiek simulēts tā datu pastāvīgums.
#XFLD: Label for Max memory
MaxMC=Maksimālais atmiņas patēriņš:
#XFLD: Label for Start
Start=Startēt
#XFLD: Label for Cancel
Cancel=Atcelt
#XMSG Message toast
startAnalysisSuccess=Mēs darbinām skatu analizatoru
startAnalyserWithoutMemory=Skatu analizators darbojas bez atmiņas patēriņa.
startAnalyserWithoutMemoryWithExplainPlan=Skatu analizators darbojas, lai ģenerētu plāna skaidrojumu.
startAnalyserWithMemory=Skatu analizators darbojas ar atmiņas patēriņu.
startAnalyserWithPlanviz=Skatu analizētājs darbojas ar SQL plāna ģenerēšanu
#XFLD: Label
gib=GiB
#XFLD: Label
percent=%
#XMSG: Failure message
startAnalysisFailed=Neizdevās startēt skatu analizatoru
#XMSG: Tooltip
maxMemoryTooltip=Maksimālais atmiņas patēriņš ir ierobežots līdz vietas darba slodzes konfigurācijas priekšrakstu atmiņas limitam. Ja tas šeit nav definēts, to ierobežo kopējā pieejamā sistēmas atmiņa.

#XFLD: Label for View Analyzer Findings
viewanalyzerfindings=Skatu analizatora konstatējumi
#XFLD: Label for Type
type=Tips
#XFLD: Label for Number of Joins
noofjoins=Savienojumu skaits
#XFLD: Label for Number of Joins
noofpartitions=Skata nodalījumi
#XFLD: Label for Peak Memory
peakmemory=Maksimālā atmiņa
#XFLD: Label for Used in-Memory
usedinmemory=Krātuvei izmantotā atmiņa
#XFLD: Label
useddisk=Krātuvei izmantotais disks
#XFLD: Label
noofrows=Rindu skaits
#XFLD: Label for Messages
messages=Ziņojumi
#XFLD: Label for category
category=Kategorija
#XFLD: Label for Sort
sortTxt=Kārtot
#XFLD: Label for Filter
filterTxt=Filtrēt
#XFLD: Label for Filtered By:
FilteredByTxt=Filtrēts pēc:
#XFLD: Label for button
enterFullScreenTxt=Ieiet pilnekrānā
#XFLD: Label for button
exitFullScreenTxt=Iziet no pilnekrāna
#XFLD: Label for button
closeRightColumn=Aizvērt labo kolonnu
#XFLD: Label for Column
spaceName=Vieta



#~~~~~~~~~~~~~~~~~~Texts for View Analyzer~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Label for category
Category=Kategorija
#XFLD: Label for message
Message=Ziņojums
#XFLD: Label for type
Type=Tips

#XMSG Entity stats Information messages for advisor
VIEW_PERSISTENCY_ADVISOR=Skata analizatora rezultāti
#XBUT: Button to close View Analyzer dialog
CLOSE=Aizvērt
#XBUT: Button to export result of View Analyzer as file in CSV format
EXPORT=Eksportēt CSV


#XFLD: Label for warning message
warning=Brīdinājums
#XFLD: Label for error message
error=Kļūda

#XFLD: Label for object of type "View"
View=Skats
#XFLD: Label for object of type "Remote Table"
RemoteTable=Attālā tabula
#XFLD: Label for object of type "Local table"
LocalTable=Lokālā tabula
#XFLD: Label for object of type "Local table (file)"
LocalTableFile=Lokālā tabula (fails)
#XFLD: Label for object of type "External"
External=Ārējs
#XFLD: Label for object of type "Data Access Control"
DataAccessControl=Datu piekļuves vadība
#XFLD: Label for object of type "SQLScript View"
SqlScriptView=SQLScript skats
#XFLD: Label for object shared from another space
SharedObject=Koplietots objekts
#XFLD: Label for IntelligentLookup
IntelligentLookup=Intelektiskā uzmeklēšana
#XFLD: Label for External entity
ExternalEntity=Ārējā entītija

#XMSG: Information on persistency status of a view
isPersisted=Pastāvošs: {0}
#XMSG: Memory consumption in Mebibyte
memoryUsed=Atmiņas patēriņš: {0} MiB
#XMSG: Disk storage used in Mebibyte
diskUsed=Izmantotā vieta diskā: {0} MiB
#XMSG: Memory consumption in Mebibyte
UImemsizeinMB=Atmiņas patēriņš: {0} MiB
#XMSG: Disk storage used in Mebibyte
UIdisksizeinMB=Izmantotā vieta diskā: {0} MiB
#XMSG: Number of join operations
joins=Savienojumi: {0}
#XMSG: Number of union operations
unions=Apvienības: {0}
#XMSG: Number of partitions
partitions=Nodalījumi: {0}
#XMSG: Adapters used for a remote table
adapterName=Izmantotais adapters: {0}
#XMSG: Message indicating data access (virtual, persisted , remote ..)
accessStatus=Datu piekļuve: {0}
#XMSG: Peak Memory
peakMemoryinMB=Maksimālā atmiņa: {0} MiB
#XMSG: Number of rows
numberRecords=Rindas: {0}
#XLBL: Generate Explain plan
generateExplainPlan=Ģenerēt plāna skaidrojumu


#XMSG: Remote table with limited capabilities such as filter pushdown, and information on the connection and adapter used
PA_ADAPTER=Attālā tabula “{0}” savienojumā “{2}” izmanto adapteri “{1}” ar trūkstošām ierobežotām iespējām, piem., filtra stumšanu vai LIMIT atbalstu.
#XMSG: View uses View Persistency Partitioning feature, and consumes entities based on adapter with limited capabilities
PA_ADAPTER_CONSUMER=Skats “{0}” ir nodalīts un patērē entītijas, pamatojoties uz adapteru ar ierobežotām iespējām.
#XMSG: View contains JOIN and UNION operators
PA_VIEW_COMPLEXITY=Skatam “{0}” ir {1} JOIN un {2} UNION operators(i).
#XMSG: For a given entity, the required (descent entities in the lineage graph) are displayed
PA_DEPENDENCIES=Entītijai “{0}” ir vajadzīgas šādas entītijas: {1}
#XMSG: For view based on SQL script, no evaluation will be done
PA_SQLscript_SKIP=Ir noteikts SQLscript skats ''{0}''. JOIN un UNION operatoriem netiks veikta novērtēšana.
#XMSG: For shared object, no evaluation will be done
PA_SHARED_OBJECT_SKIP=Ir noteikts koplietojams objekts “{0}”. Novērtējums netiks veikts.
#XMSG: For external entities in Open SQL schema, no evaluation will be done
PA_EXTERNAL_ENTITY_SKIP=Ir noteikta ārējā entītija “{0}”, novērtējums netiks veikts.
#XMSG: Message indicating missing of transported statistics for a remote table
PA_STATISTICS=Attālajā tabulā “{0}” nav transportēšanas statistikas.
#XMSG: View cannot be persisted, because it has input parameters
PA_VIEW_HAS_PARAMETERS=Skats “{0}” nevar būt pastāvīgs, jo tam ir ievades parametri.
#XMSG: Entity is not fully deployed
PA_NOT_FULLY_DEPLOYED=Entītija “{0}”nav pilnībā izvietota.
#XMSG: Entity has design time error and will not be analyzed.
PA_DESIGN_TIME_ERROR=Entītijai "{0}" ir izstrādes laika kļūda, un to nevar analizēt.
#XMSG: View is persisted and numbers of JOINs, UNIONs and remote tables hidden are displayed
PA_PERSIST_HIDES=Skats “{0}” ir pastāvīgs. Kešatmiņa paslēpj {1} JOIN, {2} UNION, un {3} attālās tabulas.
#XMSG: Number of Joins, Unions, Remote Tables, limited adapters, replicated and persisted views found during evaluation
PA_AGGREGATED=Novērtējums pieskaras {0} JOIN, {1} UNION, {2} attālajām tabulām ({3} attālās ar {4} limitētiem adapteriem, {5} replicēti) un {6} pastāvības VIEW.
#XMSG: Message in case of error, with refererence to Task Log for more details. */
PA_ERROR_MESSAGE=Analizējot entītiju {0}, radās kļūda. Lai iegūtu papildu detalizētu informāciju, skatiet uzdevumu žurnālu.
#XMSG: Message in case of warning, with refererence to Task Log for more details. */
PA_WARNING_MESSAGE=Analizējot entītiju {0}, radās brīdinājums. Papildinformāciju sk. uzdevumu žurnālā.
#XMSG: No analysis is performed for entities outside space {0}
PA_NO_MEMORYANALYSIS=Atmiņas analīze tiek veikta tikai entītijām vietā "{0}".
#XMSG: Message in case of error with refererence to Task Log for more details. */
PG_NO_RESULT=Radās kļūda, un entītijas nebija iespējams noteikt. Plašāku informāciju skatiet uzdevumu žurnālā.

#XFLD: Label indicating virtual data access
PA_VIRTUAL=Virtuāls
#XFLD: Label indicating persisted data access
PA_PERSISTED=Pastāvošs
#XFLD: Label indicating partially persisted data access
PA_PARTIALLY_PERSISTED=Daļēji pastāvīgs
#XFLD: Label indicating replicated (snapshot) data access
PA_SNAPSHOT=Replicēts (momentuzņēmums)
#XFLD: Label indicating replicated (real-time) data access
PA_REPLICATED=Replicēts (reāllaika)
#XFLD: Label indicating remote data access
PA_REMOTE=Attāls
#XFLD: Label indicating local data access
PA_LOCAL=Vietējs

#XFLD: Label for busy dialog
busydialog-analyzer=Notiek vietas detalizētās informācijas par atmiņu ienešana...
#XFLD: Label for Messages header
messagesHeader=Ziņojumi ({0})
#XTIT: Label for Objects header
objectsHeader=Objekti ({0})

#XFLD Entities Tab Name
entitiesTab=Entītijas
#XFLD Start Analyzer button
startAnalyzer=Startēt analizatoru
#XFLD inspect
Inspect=Pārbaudīt
#XFLD Navigate to  monitor
gotoMonitor=Doties uz pārraugu
#XFLD Navigate to editor
gotoEditor=Doties uz redaktoru
#XFLD list view
list=Saraksta skats
#XFLD lineage view
lineage=Pārmantojamības skats
#XFLD name
entityName=Entītijas nosaukums
#XFLD persisted column
persisted=Pastāvošs
#XFLD no of joins
joinsColumn=Savienojumi
#XFLD no of partitions
partitionsColumn=Skata nodalījumi
#XFLD Last run peak memory column
lastRunPeakmemoryColumn=Pēdējās izpildes maksimālā atmiņa (MiB)
#XFLD rows
rows=Rindas
#XFLD Last run duration
lastRunDuration=Pēdējās izpildes ilgums
#XFLD adapter
adapter=Adapteris
#XFLD Navigate to monitor/editor
navigationMenu=Atvērt pārraugā/redaktorā
#XFLD warnings button tooltip
warningMsgButtonTooltip=Brīdinājumi
#XFLD errors button tooltip
errorMsgButtonTooltip=Kļūdas



#~~~~~~~~~~~~~~~~~~Filter text View Analyzer~~~~~~~~~~~~~~~~~~~~~~~
#XFLD Filter text
true=PATIESS
#XFLD Filter text
false=APLAMS
#XFLD Filter text
lessthan5Joins=Mazāk par 5 savienojumiem
#XFLD Filter text
morethan5Joins=Vairāk par 5 savienojumiem
#XFLD Filter text
lessthan5Partitions=Mazāk par 5 nodalījumiem
#XFLD Filter text
lessthan10Partitions=Mazāk par 10 nodalījumiem
#XFLD Filter text
morethan10Partitions=Vairāk par 10 nodalījumiem
#XFLD Filter text
lessthan100=Mazāk par 100 MB
#XFLD Filter text
morethan100=Vairāk par 100 MB
#XFLD Filter text
lessthan500=Mazāk par 500 MB
#XFLD Filter text
morethan500=Vairāk par 500 MB
#XFLD Filter text
Warning=Brīdinājums
#XFLD Filter text
Error=Kļūda

analyzerWarningMsg=Dažām atlasītajām entītijām nav pieejams atbalsts. Pārliecinieties, vai atlasāt entītijas ar tipu Skats vai SQL Script skats.
analyzerWarning=Brīdinājums
getResultsError=Lasot skata analīzes rezultātus, radās kļūda.
entitiesNotAnalysed=Ja skats tiek analizēts bez atmiņas patēriņa, noteikti lauki var būt tukši.


#~~~~~~~~~View Analyzer Persistency Guidance~~~~~~~~~~~~~~~~

#XFLD Data Access
DATA_ACCESS=Datu piekļuve
#XFLD Data Persistence Candidate Score
DATA_PERSISTENCE_CANDIDATE_SCORE=Datu pastāvības kandidāta punkti
#XFLD Sumber of sources
NUMBER_SOURCES=Avoti
#XFLD Number of targets
NUMBER_TARGETS=Mērķi
#XFLD Overall number of sources
OVERALL_NUMBER_SOURCES=Vispārējais avotu skaits
#XFLD Number of federated remote tables (with limited adapters)
NUMBER_REMOTE_TABLES_LIMITED_ADAPTERS=Federatīvās attālās tabulas (ar ierobežotām adapteru iespējām)
#XFLD Number of local tables (file)
NUMBER_LOCAL_TABLES_FILE=Lokālās tabulas (fails)
#XFLD Simulation status
STATUS_LAST_PERSISTENCY_TASK=Pēdējā datu pastāvības uzdevuma status
#XFLD Simulation status
STATUS_PERSISTENCY_SIMULATION=Simulācijas statuss
#XFLD Deployement status
NOT_FULLY_DEPLOYED=Nav izvietots pilnībā
#XFLD Input parameters
INPUT_PARAMETERS=Ievades parametri
#XFLD Number of Parameters (with Default Value)
NUMBER_INPUT_PARAMETERS_DEFAULT_VALUE=Parametru skaits (ar noklusējuma vērtību)
#XFLD View has DAC
SOURCE_VIEW_HAS_DAC=Datu piekļuves vadība
#XFLD View uses remote tables with user propagation
REMOTE_TABLES_USER_PROPAGATION=Attālās tabulas ar lietotāju izplatīšanu
#XFLD: Label for Type
ENTITY_TYPE=Tips
#XFLD: Label for Number of Partitions
PARTITIONS=Nodalījumi
#XFLD Data lineage
DATA_LINEAGE=Datu pārmantojamība
#XFLD Number Joins
NUMBER_JOINS=Savienojumu skaits
#XFLD Number Unions
NUMBER_UNIONS=Apvienību skaits
#XFLD Peak Memory in  (MiB)
PEAK_MEMORY_MIB=Maksimālā atmiņa (MiB)
#XFLD Number of rows
NUMBER_ROWS=Rindu skaits
#XFLD Memory used for storage in MiB
MEMORY_USED_FOR_STORAGE_MIB=Krātuvei izmantotā atmiņa (MiB)
#XFLD Disk used for storage in MiB
DISK_USED_FOR_STORAGE_MIB=Krātuvei izmantotais disks (MiB)
#XFLD collapse persisted views button label
collapsePersistedViews=Sakļaut pastāvīgotos skatus


#XFLD Best Candidate
BEST_CANDIDATE=Labākā iespēja
#XFLD Best alternative candidate
BEST_ALTERNATIVE_CANDIDATE=Labākā alternatīva
#XFLD Good candidate
GOOD_CANDIDATE=Labi
#XFLD Not applicable
NOT_APPLICABLE=N/A
#XFLD Open Monitor
openMonitor=Atvērt pārraugu
NOT_PERSISTABLE=Nav pastāvīgs

#XFLD Failed
FAILED=Neizdevās
#XFLD true
TRUE=true
#XFLD: tooltip for table column
txtViewDataAccessTooltip=Šis norāda skata pieejamību

#XMSG: Persisting this view is likely to materially improve the performance of analyzed view
PG_BEST_CANDIDATE=Šī skata pastāvīgums, visticamāk, būtiski uzlabos analizētā skata veiktspēju.
#XMSG: Persisting this view is likely to materially improve the performance of analyzed view
PG_BEST_ALTERNATIVE=Šī skata pastāvīgums, visticamāk, uzlabos analizētā skata veiktspēju.
#XMSG: Persisting this view is likely to materially improve the performance of analyzed view
PG_GOOD_CANDIDATE=Šī skata pastāvīgums varētu uzlabot analizētā skata veiktspēju.
#XMSG: However, it could not be persisted during the last run.
PG_PERSISTENCY_FAILED=Taču tam nevarēja izmantot pastāvīgumu pēdējās izpildes laikā.
#XMSG: However, we could not simulate persisting its data during the analysis.
PG_SIMULATION_FAILED=Taču nevarēja simulēt tā datu pastāvīgumu analīzes laikā.
#XMSG: This source cannot be persisted because it got input parameters.
PG_INPUT_PARAMETER=Šis skats nevar būt pastāvīgs, jo tam ir ievades parametri.
#XMSG: View cannot be persisted because it has more than one parameter,
PG_MULTIPLE_INPUT_PARAMETERS=Šis skats nevar būt pastāvīgs, jo tam ir vairāki parametri.
#XMSG: View cannot be persisted because it has a parameter with no default value.
PG_INPUT_PARAMETER_WITHOUT_DEFAULT_VALUE=Šis skats nevar būt pastāvīgs, jo tam ir parametrs bez noklusējuma vērtības.
#XMSG: View can only be partially persisted because it has an input parameter with a default value.
PG_INPUT_PARAMETER_WITH_DEFAULT_VALUE=Šis skats var būt tikai daļēji pastāvīgs, jo tam ir ievades parametrs ar noklusējuma vērtību.
#XMSG: This view cannot be persisted, because it consumes views with data access control.
PG_SOURCE_VIEW_HAS_DAC=Šis skats nevar būt pastāvīgs, jo tas patērē skatus ar datu piekļuves vadību.
#XMSG: This view cannot be persisted, because it consumes remote tables with user propagation.
PG_VIEW_HAS_REMOTE_TABLES_USER_PROPAGATION=Šo skatu nevar pastāvīgot, jo tas patērē attālās tabulas ar lietotāju izplatīšanu.
#XMSG: Message when the view with the  best persistence candidate score cannot be displayed
PG_BEST_CANDIDATE_NOT_VISIBLE=Skatu var vislabāko datu pastāvīguma kandidāta vērtējumu nevar parādīt. Tas atrodas citā vietā, kuru jums nav atļaujas skatīt. Lai to redzētu, lūdziet administratoram piešķirt jums trūkstošās privilēģijas.
#XMSG: Message when entities cannot be displayed due to missing permissions
PA_UNAUTHORIZED=Noteiktas entītijas nevar parādīt, jo tās atrodas vietā, kuru jums nav atļauts skatīt. Lai tās redzētu, lūdziet administratoram piešķirt jums trūkstošās privilēģijas.
#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Pēdējai datu pastāvīgošanas izpildei izmantotie iestatījumi:
#XMSG: Message for input parameter name
inputParameterLabel=Ievades parametrs
#XMSG: Message for input parameter value
inputParameterValueLabel=Vērtība
#XMSG: Message for persisted data
inputParameterPersistedLabel=Pastāvīgots
#XMSG: This view is already persisted
PG_VIEW_PERSISTED=Šis skats jau ir pastāvīgots.
#XMSG: This view is already partially persisted
PG_VIEW_PARTIALLY_PERSISTED=Šis skats jau ir daļēji pastāvīgots.
#XMSG: Remote tables are not analyzed for persistence
PG_REMOTE_TABLES=Attālās tabulas netiek analizētas attiecībā uz pastāvīgumu.
#XMSG: Local tables are not analyzed for persistence
PG_LOCAL_TABLES=Lokālās tabulas netiek analizētas attiecībā uz pastāvīgumu.
#XMSG: This entity is not analyzed for persistence
PG_NOT_ANALYZED=Šī entītija netiek analizēta attiecībā uz pastāvīgumu.
#XMSG: This view is not analyzed for persistency because it belongs to a persisted branch
PG_BELONG_PERSISTED_BRANCH=Šis skats netiek analizēts attiecībā uz pastāvīgumu, jo tas pieder pastāvīgotam zaram.

#~~~~~~~~~~~~View Analyzer Explain plan~~~~~~~~~~~~~~~~
#XFLD: Label for Explain plan
ExplainPlan=Plāna skaidrojums
#NOTR: Label for OperatorName
OperatorName=OPERATOR_NAME
#NOTR: Label for OperatorDetails
OperatorDetails=OPERATOR_DETAILS
#NOTR: Label for OperatorProperties
OperatorProperties=OPERATOR_PROPERTIES
#NOTR: Label for ExecutionEngine
ExecutionEngine=EXECUTION_ENGINE
#NOTR: Label for DatabaseName
DatabaseName=DATABASE_NAME
#NOTR: Label for SchemaName
SchemaName=SCHEMA_NAME
#NOTR: Label for TableName
TableName=TABLE_NAME
#NOTR: Label for TableType
TableType=TABLE_TYPE
#NOTR: Label for TableSize
TableSize=TABLE_SIZE
#NOTR: Label for Output Size
OutputSize=OUTPUT_SIZE
#NOTR: Label for Subtree Cost
SubtreeCost=SUBTREE_COST
#XFLD: Label for close
close=Aizvērt
#XFLD: Label for download
downloadExplainPlan=Lejupielādēt
#XFLD: Label for expandAll
expandAll=Izvērst visus
#XFLD: Label for collapseAll
collapseAll=Sakļaut visus
#XMSG: Message for Explain plan
Morethan1000records=Ģenerēto plāna skaidrojumu nevar attēlot. Tajā ir vairāk par 1000 rindām. Lejupielādējiet failu, lai skatītu visas rindas.
#XFLD LABEL for Information
Information=Informācija
#XMSG Message for Explain plan
downloadCompleted=Lejupielāde pabeigta
#XFLD: Label for Clear Filter
ClearFilter=Notīrīt filtru

navigateToMonitor=Vai vēlaties doties uz “Skatīt detalizētu informāciju par monitoru”?
navigateToMonitorTitle=Skatu pārraugs
errorWhileFetchingExplainPlan=Ienesot plāna skaidrojumu, radās kļūda.


