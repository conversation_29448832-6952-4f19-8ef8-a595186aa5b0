#XTIT: Dynamic Page title
pageTitle=[[[Ŵēĺċŏɱē ţŏ ţĥē Ŝŷşţēɱ Μŏŋįţŏŗ∙∙∙∙∙∙∙∙∙]]]
#XTIT: Dynamic Page subtitle
pageSubtitle=[[[Μŏŋįţŏŗ ţĥē ρēŗƒŏŗɱąŋċē ŏƒ ŷŏűŗ şŷşţēɱ ąŋƌ įƌēŋţįƒŷ şţŏŗąğē, ţąşķ, ŏűţ-ŏƒ-ɱēɱŏŗŷ, ąŋƌ ŏţĥēŗ įşşűēş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XTIT: Messagestrip Enable Statement tracing
messagestripEnableStatementTracing=[[[Ţŏ ɱŏŋįţŏŗ ŏűţ-ŏƒ-ɱēɱŏŗŷ ēŗŗŏŗş ąŋƌ ŏţĥēŗ şţąţēɱēŋţ įŋƒŏŗɱąţįŏŋ, ρĺēąşē ēŋąƃĺē ēχρēŋşįʋē şţąţēɱēŋţ ţŗąċįŋğ įŋ Ĉŏŋƒįğűŗąţįŏŋ/Μŏŋįţŏŗįŋğ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Dashboard text
dashBoardText=[[[Ďąşĥƃŏąŗƌ∙∙∙∙∙]]]
#XFLD: TaskLog text
taskLogText=[[[Ļŏğş]]]
#XTIT: card title: Storage Distribution
storageDistribution=[[[Ďįşķ Ŝţŏŗąğē Ůşēƌ∙∙∙∙∙∙∙]]]
#XTIT: card title: Memory Distribution
memoryDistribution=[[[Μēɱŏŗŷ Ďįşţŗįƃűţįŏŋ∙∙∙∙∙]]]
#XTIT: card title:Used Disk Capacity
usedStorageCapacity=[[[Ďįşķ Ůşēƌ ƃŷ Ŝρąċēş ƒŏŗ Ŝţŏŗąğē∙∙∙∙∙∙∙∙∙∙]]]
#XTIT: card title: Used Memory Capacity
usedMemoryCapacity=[[[Μēɱŏŗŷ Ůşēƌ ƃŷ Ŝρąċēş ƒŏŗ Ŝţŏŗąğē∙∙∙∙∙∙∙∙∙∙∙]]]
#XTIT: card title:Assigned Disk Capacity
assignedStorageCapacity=[[[Ďįşķ Āşşįğŋēƌ ţŏ Ŝρąċēş ƒŏŗ Ŝţŏŗąğē∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XTIT: card title: Assigned Memory Capacity
assignedMemoryCapacity=[[[Μēɱŏŗŷ Āşşįğŋēƌ ţŏ Ŝρąċēş ƒŏŗ Ŝţŏŗąğē∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XTIT: card title: Errors
errors=[[[Ƒąįĺēƌ Ţąşķş∙∙∙∙∙∙∙]]]
#XTIT: card title: Configuration
ecnConfiguration=[[[Ĉŏŋƒįğűŗąţįŏŋ∙∙∙∙∙∙]]]
#XTIT: card title: ECN Monthly Uptime
upTime=[[[Μŏŋţĥĺŷ Ůρţįɱē∙∙∙∙∙]]]
#XTIT: card title: ECN Average Memory
ECNAvgMemory=[[[Āʋēŗąğē Μēɱŏŗŷ∙∙∙∙∙]]]
#XTIT: card title: ECN Average CPU
ECNAvgCPU=[[[Āʋēŗąğē ĈƤŮ∙∙∙∙∙∙∙∙]]]
#XTIT: card Sub title: ECN Hours Subtitle
ECNHoursSubtitle=[[[Ļąşţ {0} Ĥŏűŗş]]]
#XTIT: card Title: ECN Last 5 Start Tasks
ecnTop5StartStop=[[[ĔĈŃ Ļąşţ 5 Ŝţąŗţ/Ŝţŏρ Ţąşķş∙∙∙∙∙∙∙∙]]]
#XTIT: card Title: ECN Last 5 Stop Tasks
ecnTop5Stop=[[[ĔĈŃ Ļąşţ 5 Ŝţŏρ Ţąşķş∙∙∙∙∙]]]
#XTIT: card title: Out of Memory Events (all)
OoMEvents=[[[Ŏűţ-ŏƒ-Μēɱŏŗŷ Ĕŗŗŏŗş∙∙∙∙]]]
#XTIT: card title: MDS
OoMMDSQueries=[[[Ŏűţ-ŏƒ-Μēɱŏŗŷ Ĕŗŗŏŗş (ΜĎŜ Řēƣűēşţş)∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XTIT: card title: Top 5 Memory Consumption (Statements)
memoryConsumptionStatements=[[[Ţŏρ 5 Ŝţąţēɱēŋţş ƃŷ Ƥŗŏċēşşįŋğ Μēɱŏŗŷ Ĉŏŋşűɱρţįŏŋ∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XTIT: card title: Out of Memory Events (Workload Class) by Space
OoMEventsByWorkloadClass=[[[Ţŏρ 5 Ŏűţ-ŏƒ-Μēɱŏŗŷ Ĕŗŗŏŗş (Ŵŏŗķĺŏąƌ Ĉĺąşş) ƃŷ Ŝρąċē∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XTIT: card title: Run Duration
runDuration=[[[Ţŏρ 5 Ţąşķş ƃŷ Řűŋ Ďűŗąţįŏŋ∙∙∙∙∙∙∙∙]]]
#XTIT: card title: Top 5 Tasks by Memory Consumption
tasksBymemoryConsumption=[[[Ţŏρ 5 Ţąşķş ƃŷ Ƥŗŏċēşşįŋğ Μēɱŏŗŷ Ĉŏŋşűɱρţįŏŋ∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XTIT: card title: Memory Consumption
memoryConsumption=[[[Μēɱŏŗŷ Ĉŏŋşűɱρţįŏŋ∙∙∙∙∙∙]]]
#XTIT: card title: Top 5 Memory Consumption (MDS)
memoryConsumptionSAC=[[[Ţŏρ 5 ΜĎŜ Řēƣűēşţş ƃŷ Ƥŗŏċēşşįŋğ Μēɱŏŗŷ Ĉŏŋşűɱρţįŏŋ∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=[[[Āűƌįţ Ļŏğ Ďąţą∙∙∙∙∙]]]
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=[[[Āƌɱįŋįşţŗąţįʋē Ďąţą∙∙∙∙∙]]]
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=[[[Ŏţĥēŗ Ďąţą∙∙∙∙]]]
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=[[[Ďąţą įŋ Ŝρąċēş∙∙∙∙∙]]]
#XFLD: ResourceCategory Available Memory
ResourceCategoryAvailableMemory=[[[Ůŋűşēƌ Μēɱŏŗŷ∙∙∙∙∙∙]]]
#XFLD: ResourceCategory Memory Used by Consumption
ResourceCategoryMemoryConsumption=[[[Μēɱŏŗŷ Ůşēƌ ƒŏŗ Ƥŗŏċēşşįŋğ∙∙∙∙∙∙∙]]]
#XFLD: ResourceCategory Memory Used by Replication
ResourceCategoryMemoryReplication=[[[Μēɱŏŗŷ Ůşēƌ ƒŏŗ Ďąţą Řēρĺįċąţįŏŋ∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: ResourceCategory unknown
unknown=[[[Ůŋķŋŏŵŋ∙∙∙∙∙∙∙]]]
#XBUT: Card footer view list button
viewList=[[[Ʋįēŵ Ļŏğş∙∙∙∙∙]]]
#NOTR: placeholder for dev-purposes
test=TEST
#XTIT: card bar label: hours
hours=[[[Ĥŏűŗş∙∙∙∙∙∙∙∙∙]]]
#XTIT: card subtitle: now
now=[[[Ńŏŵ∙]]]
#XTIT: card subtitle: last 24 hours
last24Hours=[[[Ļąşţ 24 Ĥŏűŗş∙∙∙∙∙∙]]]
#XTIT: card subtitle: last 48 hours
last48Hours=[[[Ļąşţ 48 Ĥŏűŗş∙∙∙∙∙∙]]]
#XTIT: card subtitle: last week
lastWeek=[[[Ļąşţ 7 Ďąŷş∙∙∙∙∙∙∙∙]]]
#XTIT: card subtitle: last month
lastMonth=[[[Ļąşţ Μŏŋţĥ∙∙∙∙]]]
#XFLD: Close
close=[[[Ĉĺŏşē∙∙∙∙∙∙∙∙∙]]]
#XFLD: Today
today=[[[Ţŏƌąŷ∙∙∙∙∙∙∙∙∙]]]
#XTIT: text for task tab
taskTab=[[[Ţąşķş∙∙∙∙∙∙∙∙∙]]]
#XTIT: text for statement tab
statementTab=[[[Ŝţąţēɱēŋţş∙∙∙∙]]]
#XTIT: text fo nav link in table cell
viewStatements=[[[Ʋįēŵ]]]
#XTIT: text to more link in table cell
more=[[[Μŏŗē]]]
#XTIT: text for statement dialog header
statementDlgTitle=[[[Ŝţąţēɱēŋţ∙∙∙∙∙]]]
#XBUT: text for close btn on dialog
closeBtn=[[[Ĉĺŏşē∙∙∙∙∙∙∙∙∙]]]
#XBUT: text for copy btn on dialog
copyBtn=[[[Ĉŏρŷ]]]
#XTIT: Copy success text on toast msg
copiedToClipboard=[[[Ĉŏρįēƌ ţŏ Ĉĺįρƃŏąŗƌ∙∙∙∙∙]]]
#XTIT: test for download btn on dialog
downloadBtn=[[[Ďŏŵŋĺŏąƌ∙∙∙∙∙∙]]]

#XTIT: Monitoring table column names
startTime=[[[Ŝţąŗţ Ţįɱē∙∙∙∙]]]
startDate=[[[Ŝţąŗţ Ďąţē∙∙∙∙]]]
duration=[[[Ďűŗąţįŏŋ∙∙∙∙∙∙]]]
objectType=[[[Ŏƃĵēċţ Ţŷρē∙∙∙∙∙∙∙∙]]]
activity=[[[Āċţįʋįţŷ∙∙∙∙∙∙]]]
spaceName=[[[Ŝρąċē Ńąɱē∙∙∙∙]]]
objectName=[[[Ŏƃĵēċţ Ńąɱē∙∙∙∙∙∙∙∙]]]
peakMemory=[[[ŜĀƤ ĤĀŃĀ Ƥēąķ Μēɱŏŗŷ∙∙∙∙]]]
peakCPU=[[[ŜĀƤ ĤĀŃĀ ĈƤŮ Ţįɱē∙∙∙∙∙∙∙]]]
noOfRecords=[[[Řēċŏŗƌş∙∙∙∙∙∙∙]]]
usedMemory=[[[ŜĀƤ ĤĀŃĀ Ůşēƌ Μēɱŏŗŷ∙∙∙∙]]]
usedDisk=[[[ŜĀƤ ĤĀŃĀ Ůşēƌ Ďįşķ∙∙∙∙∙∙]]]
status=[[[Ŝţąţűş∙∙∙∙∙∙∙∙]]]
subStatus=[[[Ŝűƃşţąţűş∙∙∙∙∙]]]
user=[[[Ůşēŗ]]]
targetTable=[[[Ţąŗğēţ Ţąƃĺē∙∙∙∙∙∙∙]]]
statements=[[[Ŝţąţēɱēŋţş∙∙∙∙]]]
outOfMemory=[[[Ŏűţ-ŏƒ-Μēɱŏŗŷ∙∙∙∙∙∙]]]
taskLogId=[[[Ţąşķ Ļŏğ ĬĎ∙∙∙∙∙∙∙∙]]]
statementDetails=[[[Ŝţąţēɱēŋţ Ďēţąįĺş∙∙∙∙∙∙∙]]]
parameters=[[[Ƥąŗąɱēţēŗş∙∙∙∙]]]
workloadClass=[[[Ŵŏŗķĺŏąƌ Ĉĺąşş∙∙∙∙∙]]]
errorCode=[[[Ĕŗŗŏŗ Ĉŏƌē∙∙∙∙]]]
errorText=[[[Ĕŗŗŏŗ Μēşşąğē∙∙∙∙∙∙]]]
dbUser=[[[Ďąţąƃąşē Ůşēŗ∙∙∙∙∙∙]]]
connectionID=[[[Ĉŏŋŋēċţįŏŋ ĬĎ∙∙∙∙∙∙]]]
statementID=[[[Ŝţąţēɱēŋţ ĬĎ∙∙∙∙∙∙∙]]]
elasticComputeNode=[[[Ĕĺąşţįċ Ĉŏɱρűţē Ńŏƌē∙∙∙∙]]]


#Localization for @sap/dragonet-ui5-table-editor node module
#XTIT: Title for column settings dialog
columnsSettingsDialogTitle=[[[Ĉŏĺűɱŋş Ŝēţţįŋğş∙∙∙∙∙∙∙∙]]]
#XFLD: Title for Error type
messagesTableType=[[[Ţŷρē]]]
#XFLD: Title for Error Message
messagesTableMessage=[[[Μēşşąğē∙∙∙∙∙∙∙]]]
#XFLD: Title for filter
filteredBy=[[[Ƒįĺţēŗēƌ ƃŷ:∙∙∙∙∙∙∙]]]
#XTIT: text for values contained in filter
filterContains=[[[ċŏŋţąįŋş∙∙∙∙∙∙]]]
#XTIT: text for values starting with in filter
filterStartsWith=[[[şţąŗţş ŵįţĥ∙∙∙∙∙∙∙∙]]]
#XTIT: text for values ending with in filter
filterEndsWith=[[[ēŋƌş ŵįţĥ∙∙∙∙∙]]]
#XTIT: Title for search in data preview toolbar
toolbarSearch=[[[Ŝēąŗċĥ∙∙∙∙∙∙∙∙]]]
#XBUT: Button to clear filter
clearFilter=[[[Ĉĺēąŗ Ƒįĺţēŗ∙∙∙∙∙∙∙]]]
#XBUT: Button to cancel the operation
cancel=[[[Ĉąŋċēĺ∙∙∙∙∙∙∙∙]]]
#XBUT: Button to save the operation
ok=[[[ŎĶ∙∙]]]
#XBUT: Button to restore the data
toolbarRestoreButton=[[[Řēƒŗēşĥ∙∙∙∙∙∙∙]]]
#XBUT: Button to cancel running task
cancelBtnText=[[[Ĉąŋċēĺ Ţąşķ∙∙∙∙∙∙∙∙]]]
#XBUT: tooltip for Button to restore the data
toolbarRestoreButtonTooltip=[[[Ůρƌąţē ţĥē Ƥąğē∙∙∙∙]]]
#XMSG: Busy dialog for Columns Settings dialog
PROCESSING=[[[Ƥŗŏċēşşįŋğ...∙∙∙∙∙∙]]]
#XMSG: Message Confirmation
confirmation=[[[Ĉŏŋƒįŗɱąţįŏŋ∙∙∙∙∙∙∙]]]
#XMSG: Message for refresh successful
refreshSuccess=[[[Ŝűċċēşşƒűĺĺŷ Řēƒŗēşĥēƌ∙∙∙∙∙]]]
#XMSG: Message for refresh successful
refreshSuccessful=[[[Řēƒŗēşĥēƌ∙∙∙∙∙]]]
#XMSG: Message for restore successful
restoreSuccessful=[[[Ŝűċċēşşƒűĺĺŷ Řēşţŏŗēƌ∙∙∙∙∙]]]
#XMSG: Error message when null value found on non-nullable field
mdm-emptyValueError=[[["{0}" ċąŋŋŏţ ƃē ēɱρţŷ. {1}.]]]
#XTIT: Sort Ascending
mdm-sortAscending=[[[Ŝŏŗţ Āşċēŋƌįŋğ∙∙∙∙∙]]]
#XTIT: Sort Descending
mdm-sortDescending=[[[Ŝŏŗţ Ďēşċēŋƌįŋğ∙∙∙∙]]]
#XTIT: Filter
mdm-Filter=[[[Ƒįĺţēŗ∙∙∙∙∙∙∙∙]]]
#XBUT: Button Cancel
mdm-cancel=[[[Ĉąŋċēĺ∙∙∙∙∙∙∙∙]]]
#XBUT: Button Add
mdm-Add=[[[Āƌƌ∙]]]
#XMSG: and inside error message
and=[[[ąŋƌ∙]]]
#XMSG: Error message when no column selected
mdm-allColumnUnselected=[[[Ŝēĺēċţ ŏŋē ŏŗ ɱŏŗē ċŏĺűɱŋş ţŏ ċŏŋţįŋűē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]

#Localization keys not needed for System Monitor but required for @sap/dragonet-ui5-table-editor node module to remove console errors
#XTXT: Message information strip for filtering and sorting
mdm-InfoStrip=[[[Ƒįĺţēŗ, Ŝŏŗţ, Ďēĺēţē, ąŋƌ Ţąƃĺē Ŝēţţįŋğ ţŏŏĺş ąŗē ƌįşąƃĺēƌ ƃŷ űŋşąʋēƌ ċĥąŋğēş. Ŝąʋē ŷŏűŗ ċĥąŋğēş ţŏ ēŋąƃĺē ţĥēɱ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XBUT: Button for file upload in mdm data preview
toolbarUploadButton=[[[Ůρĺŏąƌ Ďąţą∙∙∙∙∙∙∙∙]]]
#XBUT: Button for duplicating rows in mdm data preview
toolbarDuplicateButton=[[[Ďűρĺįċąţē∙∙∙∙∙]]]
#XBUT: Button for deleting rows in mdm data preview
toolbarDeleteButton=[[[Ďēĺēţē∙∙∙∙∙∙∙∙]]]
#XBUT: Button for adding rows in mdm data preview
toolbarAddButton=[[[Āƌƌ∙]]]

#XTIT: MDM Empty value Handling in Table Editor Label
mdmInsertMissingString=[[[Ĭŋşēŗţ Μįşşįŋğ Ŝţŗįŋğ Ʋąĺűē ąş:∙∙∙∙∙∙∙∙∙∙]]]
#XTIT: MDM Empty value Handling in Table Editor Null Combobox item
mdmNullText=[[[ŃŮĻĻ]]]
#XTIT: MDM Empty value Handling in Table Editor Empty string Combobox item
mdmEmptyStringText=[[[Ĕɱρţŷ şţŗįŋğ∙∙∙∙∙∙∙]]]
#XTXT: Message information strip for Insert Missing String Value
mdmInfoStripMissingString=[[[Μįşşįŋğ şţŗįŋğ ʋąĺűēş ąŗē įŋşēŗţēƌ ŏŋĺŷ įŋ ʋįşįƃĺē şţŗįŋğ ċŏĺűɱŋş įŋ ŋēŵ ąŋƌ ēƌįţēƌ ŗŏŵş. Ůşē Ĉŏĺűɱŋş Ŝēţţįŋğş ţŏ ƌįşρĺąŷ ąĺĺ ŗēĺēʋąŋţ ċŏĺűɱŋş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XTXT: Message information strip for filtering sorting and Insert Missing String Value
mdm-InfoStripWithMissingString=[[[Ƒįĺţēŗ, Ŝŏŗţ, Ĭŋşēŗţ Μįşşįŋğ Ŝţŗįŋğ Ʋąĺűē, ąŋƌ Ţąƃĺē Ŝēţţįŋğ ţŏŏĺş ąŗē ƌįşąƃĺēƌ ƃŷ űŋşąʋēƌ ċĥąŋğēş. Ŝąʋē ŷŏűŗ ċĥąŋğēş ţŏ ēŋąƃĺē ţĥēɱ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Open SAP HANA Cockpit
openHanaCockpit=[[[Ďąţąƃąşē Ŏʋēŗʋįēŵ (ŜĀƤ ĤĀŃĀ Ĉŏċķρįţ)∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XBUT: Open SAP HANA Performance Monitor Cockpit
openHanaCockpitPerfMonitor=[[[Ƥēŗƒŏŗɱąŋċē Μŏŋįţŏŗ∙∙∙∙∙]]]
openHanaCockpitPerfMonitorTooltip=[[[Ŏρēŋ Ƥēŗƒŏŗɱąŋċē Μŏŋįţŏŗ įŋ ŜĀƤ ĤĀŃĀ Ĉŏċķρįţ∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XTIT: card title: Admission Control Rejection Events
acRejectedEvents=[[[Āƌɱįşşįŏŋ Ĉŏŋţŗŏĺ Řēĵēċţįŏŋ Ĕʋēŋţş∙∙∙∙∙∙∙∙∙∙∙]]]
#XTIT: card title: Admission Control Queuing Events
acQueuedEvents=[[[Āƌɱįşşįŏŋ Ĉŏŋţŗŏĺ Ǭűēűįŋğ Ĕʋēŋţş∙∙∙∙∙∙∙∙∙∙]]]

#XTIT: card title: Top 5 Admission Control Rejection Events by Space
acRejectionEventsBySpace=[[[Ţŏρ 5 Āƌɱįşşįŏŋ Ĉŏŋţŗŏĺ Řēĵēċţįŏŋ Ĕʋēŋţş ƃŷ Ŝρąċē∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XTIT: card title: Top 5 Admission Control Queuing Events by Space
acQueueingEventsBySpace=[[[Ţŏρ 5 Āƌɱįşşįŏŋ Ĉŏŋţŗŏĺ Ǭűēűįŋğ Ĕʋēŋţş ƃŷ Ŝρąċē∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]

#XTIT: payload Table and List texts
payloadDownloadText=[[[Ƥąŷĺŏąƌ Ďŏŵŋĺŏąƌēƌ∙∙∙∙∙∙]]]
dataSourceText=[[[Ďąţą Ŝŏűŗċēş∙∙∙∙∙∙∙]]]
dataSourcesObject=[[[Ńąɱē]]]
dataSourcesSchema=[[[Ŝρąċē Ńąɱē∙∙∙∙]]]
payloadListHeader=[[[Ƥąŷĺŏąƌş∙∙∙∙∙∙]]]
payloadStoryIdText=[[[Ŝţŏŗŷ ĬĎ∙∙∙∙∙∙]]]
payloadStoryNameText=[[[Ŝţŏŗŷ Ńąɱē∙∙∙∙]]]
payloadDialogTitleText=[[[Μŏŗē Ĭŋƒŏŗɱąţįŏŋ∙∙∙∙∙∙∙∙]]]
payloadTextAreaTitle=[[[Ƥąŷĺŏąƌ∙∙∙∙∙∙∙]]]
NO_MDS_DETAIL=[[[Ńŏ Ďąţą Āʋąįĺąƃĺē∙∙∙∙∙∙∙]]]
MDS_DATA_FETCH_FAILED=[[[Ŵē ċŏűĺƌ ŋŏţ ƌįşρĺąŷ įŋƒŏŗɱąţįŏŋ ŏŋ ΜĎŜ şţąţēɱēŋţş ƃēċąűşē ŏƒ ą şēŗʋēŗ ēŗŗŏŗ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
GBUnit=[[[ĢƁ∙∙]]]
enabled=[[[Ĕŋąƃĺēƌ∙∙∙∙∙∙∙]]]
notEnabled=[[[Ńŏţ Ĕŋąƃĺēƌ∙∙∙∙∙∙∙∙]]]
#XTIT: ECN Monitoring Dashboard Texts
ecnStatus=[[[Ŝţąţűş∙∙∙∙∙∙∙∙]]]
ecnBlock=[[[Ĉŏɱρűţē Ɓĺŏċķş∙∙∙∙∙]]]
ecnSize=[[[Ŝįžē]]]
ecnConfigMemory=[[[Μēɱŏŗŷ∙∙∙∙∙∙∙∙]]]
ecnConfigStorage=[[[Ŝţŏŗąğē∙∙∙∙∙∙∙]]]
ecnConfigSpacesList=[[[Ļįşţ ŏƒ Ŝρąċēş∙∙∙∙∙]]]
#XFLD: ECN phase Ready
ecnReady=[[[Řēąƌŷ∙∙∙∙∙∙∙∙∙]]]
#XFLD: ECN phase Running
ecnRunning=[[[Řűŋŋįŋğ∙∙∙∙∙∙∙]]]
#XFLD: ECN phase Initial
ecnInitial=[[[Ńŏţ Řēąƌŷ∙∙∙∙∙]]]
#XFLD: ECN phase Starting
ecnStarting=[[[Ŝţąŗţįŋğ∙∙∙∙∙∙]]]
#XFLD: ECN phase Stopping
ecnStopping=[[[Ŝţŏρρįŋğ∙∙∙∙∙∙]]]
#XFLD: ECN phase Unknown
ecnUnknown=[[[Ůŋķŋŏŵŋ∙∙∙∙∙∙∙]]]
#XFLD: ECN Last Run Details
lastRun=[[[Ļąţēşţ Řűŋ∙∙∙∙]]]
#XFLD: ECN Previous Run Details
previousRun=[[[Ƥŗēʋįŏűş Řűŋ∙∙∙∙∙∙∙]]]
#XFLD: ECN Manage Button
manage=[[[Μąŋąğē Ĕĺąşţįċ Ĉŏɱρűţē Ńŏƌē∙∙∙∙∙∙∙∙]]]
#XFLD: ECN Run Details Widget
days=[[[Ďąŷş]]]
#XFLD: ECN Run Details Widget Title
runDetails=[[[Řűŋ Ďēţąįĺş∙∙∙∙∙∙∙∙]]]
#XFLD: ECN Performance Class Label
performanceClass=[[[Ƥēŗƒŏŗɱąŋċē Ĉĺąşş∙∙∙∙∙∙∙]]]
start=[[[Ŝţąŗţ∙∙∙∙∙∙∙∙∙]]]
stop=[[[Ŝţŏρ]]]
ecnRunDetailsUpTime=[[[Ůρţįɱē∙∙∙∙∙∙∙∙]]]
ecnConfigurationCPU=[[[Ńűɱƃēŗ ŏƒ ʋĈƤŮş∙∙∙∙]]]
ecnTechnicalName=[[[Ţēċĥŋįċąĺ Ńąɱē∙∙∙∙∙]]]
currentMonth=[[[Ĉűŗŗēŋţ Μŏŋţĥ∙∙∙∙∙∙]]]
previousMonth=[[[Ƥŗēʋįŏűş Μŏŋţĥ∙∙∙∙∙]]]
ecnBlockHours=[[[Ɓĺŏċķ-Ĥŏűŗş∙∙∙∙∙∙∙∙]]]
upTimeOverall=[[[Ţŏţąĺ Ůρţįɱē∙∙∙∙∙∙∙]]]
refresh=[[[Řēƒŗēşĥ∙∙∙∙∙∙∙]]]
top5MemConsumptionDataFetchErrorText=[[[Ƒąįĺēƌ ţŏ ğēţ ƌąţą ƒŏŗ ɱēɱŏŗŷ ċŏŋşűɱρţįŏŋ∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
ecnDashboardText=[[[Ĕĺąşţįċ Ĉŏɱρűţē Ńŏƌēş∙∙∙∙∙]]]
#XMSG: Message Title when ECN is not in ruuning state
memoryDistributionErrorTitle=[[[Ńŏţ Āρρĺįċąƃĺē∙∙∙∙∙]]]
#XMSG: Warning Message when ECN is not in ruuning state
memoryDistributionErrorMessage=[[[Μēɱŏŗŷ ƌįşţŗįƃűţįŏŋ įş ƌįşρĺąŷēƌ ŏŋĺŷ ŵĥēŋ ţĥē ēĺąşţįċ ċŏɱρűţē ŋŏƌē įş įŋ ą ŗűŋŋįŋğ şţąţē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Title for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageTitle=[[[Ńŏ Ĕĺąşţįċ Ĉŏɱρűţē Ńŏƌē Ŝēĺēċţēƌ∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Description for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageDescription=[[[Ŝţąŗţ ƃŷ şēĺēċţįŋğ ŏŋē ƒŗŏɱ ţĥē ĺįşţ įŋ ţĥē ĥēąƌēŗ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Tab label for Statememt Logs
statementLogsText=[[[Ŝţąţēɱēŋţ Ļŏğş∙∙∙∙∙]]]
#XMSG: Tab label for Task Logs
taskLogsText=[[[Ţąşķ Ļŏğş∙∙∙∙∙]]]
#MSG: Label for Date Time Quick Filter
labelForTimeQuickFilter=[[[Ďąţē ąŋƌ Ţįɱē Řąŋğē:∙∙∙∙]]]
labelForSpaceQuickFilter=[[[Ŝρąċēş∙∙∙∙∙∙∙∙]]]
labelForStatusQuickFilter=[[[Ŝţąţűşēş∙∙∙∙∙∙]]]
#XTIT: Cancel Task Started toast msg
cancelTaskStarted=[[[Ţąşķ ċąŋċēĺąţįŏŋ şţąŗţēƌ∙∙∙∙∙∙]]]
#YMSE: Cancel Task Error
taskCancelError=[[[Ţĥē ţąşķ ċąŋŋŏţ ƃē ċąŋċēĺēƌ ƃēċąűşē įţ ĥąş ąĺŗēąƌŷ ƒįŋįşĥēƌ ŗűŋŋįŋğ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#LSA Monitor Tab Name
lsaMonitorText=[[[Ŏƃĵēċţ Ŝţŏŗē∙∙∙∙∙∙∙]]]
#ID for LSA Storage By Space Widget
hdlfStorageSpace=[[[ŜĀƤ ĤĀŃĀ Ďąţą Ļąķē Ƒįĺēş: Ŝţŏŗąğē Ůţįĺįžąţįŏŋ∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#ID for LSA Storage for All Spaces Widget
hdlfStorageSpaceTotal=[[[ŜĀƤ ĤĀŃĀ Ďąţą Ļąķē Ƒįĺēş: Ŝţŏŗąğē Ůţįĺįžąţįŏŋ ŏƒ Āĺĺ Ŝρąċēş∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#Labels for LSA Monitor
lsaSparkTableApplicationColumnText=[[[Āρρĺįċąţįŏŋ∙∙∙∙∙∙∙∙]]]
lsaSparkTableNoOfTasksColumnText=[[[Ńűɱƃēŗ ŏƒ Ţąşķş∙∙∙∙]]]
sparkApplicationConfigurationTitle=[[[Āρρĺįċąţįŏŋ Ĉŏŋƒįğűŗąţįŏŋ∙∙∙∙∙∙∙]]]
sparkExecutorCPU=[[[Ĕχēċűţŏŗ ĈƤŮ∙∙∙∙∙∙∙]]]
sparkExecutorMemory=[[[Ĕχēċűţŏŗ Μēɱŏŗŷ∙∙∙∙]]]
sparkDriverCPU=[[[Ďŗįʋēŗ ĈƤŮ∙∙∙∙]]]
sparkDriverMemory=[[[Ďŗįʋēŗ Μēɱŏŗŷ∙∙∙∙∙∙]]]
sparkMaximumCPU=[[[Μąχįɱűɱ ĈƤŮ∙∙∙∙∙∙∙∙]]]
sparkMaximumMemory=[[[Μąχįɱűɱ Μēɱŏŗŷ∙∙∙∙∙]]]
sparkMinExecutors=[[[Μįŋįɱűɱ Ĕχēċűţŏŗş∙∙∙∙∙∙∙]]]
sparkMaxExecutors=[[[Μąχįɱűɱ Ĕχēċűţŏŗş∙∙∙∙∙∙∙]]]
sparkAppTableNoDataIMTitle=[[[Ńŏ Ŝρąċē Ŝēĺēċţēƌ∙∙∙∙∙∙∙]]]
sparkAppTableNoDataIMDesc=[[[Ŝţąŗţ ƃŷ şēĺēċţįŋğ ŏŋē ƒŗŏɱ ţĥē ĺįşţ įŋ ţĥē ƒįĺţēŗ ƃąŗ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
TBUnit=[[[ŢƁ∙∙]]]
sparkTaskTableNoDataIMTitle=[[[Ńŏ Ţąşķş Āʋąįĺąƃĺē∙∙∙∙∙∙]]]
sparkTaskTableNoDataIMDesc=[[[Ƒŏŗ ţĥē şēĺēċţēƌ ąρρĺįċąţįŏŋ, ţĥēŗē ąŗē ŋŏ ţąşķş ąʋąįĺąƃĺē. Ŝēĺēċţ ąŋŏţĥēŗ ąρρĺįċąţįŏŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
taskActivity=[[[Ţąşķ Āċţįʋįţŷ∙∙∙∙∙∙]]]
sparkTableNoSearchResultsTitle=[[[Ńŏ Ŝēąŗċĥ Řēşűĺţş∙∙∙∙∙∙∙]]]
sparkTableNoSearchResultsDesc=[[[Ĉĥąŋğē ţĥē şēąŗċĥ ţēŗɱ ąŋƌ ţŗŷ ąğąįŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
sparkTableNoFilterResultsTitle=[[[Ńŏ Ƒįĺţēŗ Řēşűĺţş∙∙∙∙∙∙∙]]]
sparkTableNoFilterResultsDesc=[[[Ĉĥąŋğē ţĥē ƒįĺţēŗ ċŗįţēŗįą ąŋƌ ţŗŷ ąğąįŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
removeFilterText=[[[Ĉĺēąŗ Ƒįĺţēŗ∙∙∙∙∙∙∙]]]
sortTableText=[[[Ŝŏŗţ Ţąƃĺē∙∙∙∙]]]
filterTableText=[[[Ƒįĺţēŗ∙∙∙∙∙∙∙∙]]]
apacheSparkTableText=[[[Āρąċĥē Ŝρąŗķ: Ţąşķş∙∙∙∙∙]]]
closeColumnText=[[[Ĉĺŏşē Ĉŏĺűɱŋ∙∙∙∙∙∙∙]]]
