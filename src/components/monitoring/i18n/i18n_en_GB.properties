#XTIT: Dynamic Page title
pageTitle=Welcome to the System Monitor
#XTIT: Dynamic Page subtitle
pageSubtitle=Monitor the performance of your system and identify storage, task, out-of-memory and other issues.
#XTIT: Messagestrip Enable Statement tracing
messagestripEnableStatementTracing=To monitor out-of-memory errors and other statement information, please enable expensive statement tracing in Configuration/Monitoring.
#XFLD: Dashboard text
dashBoardText=Dashboard
#XFLD: TaskLog text
taskLogText=Logs
#XTIT: card title: Storage Distribution
storageDistribution=Disc Storage Used
#XTIT: card title: Memory Distribution
memoryDistribution=Memory Distribution
#XTIT: card title:Used Disk Capacity
usedStorageCapacity=Disc Used by Spaces for Storage
#XTIT: card title: Used Memory Capacity
usedMemoryCapacity=Memory Used by Spaces for Storage
#XTIT: card title:Assigned Disk Capacity
assignedStorageCapacity=Disc Assigned to Spaces for Storage
#XTIT: card title: Assigned Memory Capacity
assignedMemoryCapacity=Memory Assigned to Spaces for Storage
#XTIT: card title: Errors
errors=Failed Tasks
#XTIT: card title: Configuration
ecnConfiguration=Configuration
#XTIT: card title: ECN Monthly Uptime
upTime=Monthly Uptime
#XTIT: card title: ECN Average Memory
ECNAvgMemory=Average Memory
#XTIT: card title: ECN Average CPU
ECNAvgCPU=Average CPU
#XTIT: card Sub title: ECN Hours Subtitle
ECNHoursSubtitle=Last {0} Hours
#XTIT: card Title: ECN Last 5 Start Tasks
ecnTop5StartStop=ECN Last 5 Start/Stop Tasks
#XTIT: card Title: ECN Last 5 Stop Tasks
ecnTop5Stop=ECN Last 5 Stop Tasks
#XTIT: card title: Out of Memory Events (all)
OoMEvents=Out-of-Memory Errors
#XTIT: card title: MDS
OoMMDSQueries=Out-of-Memory Errors (MDS Requests)
#XTIT: card title: Top 5 Memory Consumption (Statements)
memoryConsumptionStatements=Top 5 Statements by Processing Memory Consumption
#XTIT: card title: Out of Memory Events (Workload Class) by Space
OoMEventsByWorkloadClass=Top 5 Out-of-Memory Errors (Workload Class) by Space
#XTIT: card title: Run Duration
runDuration=Top 5 Tasks by Run Duration
#XTIT: card title: Top 5 Tasks by Memory Consumption
tasksBymemoryConsumption=Top 5 Tasks by Processing Memory Consumption
#XTIT: card title: Memory Consumption
memoryConsumption=Memory Consumption
#XTIT: card title: Top 5 Memory Consumption (MDS)
memoryConsumptionSAC=Top 5 MDS Requests by Processing Memory Consumption
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Audit Log Data
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Administrative Data
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Other Data
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Data in Spaces
#XFLD: ResourceCategory Available Memory
ResourceCategoryAvailableMemory=Unused Memory
#XFLD: ResourceCategory Memory Used by Consumption
ResourceCategoryMemoryConsumption=Memory Used for Processing
#XFLD: ResourceCategory Memory Used by Replication
ResourceCategoryMemoryReplication=Memory Used for Data Replication
#XFLD: ResourceCategory unknown
unknown=Unknown
#XBUT: Card footer view list button
viewList=View Logs
#NOTR: placeholder for dev-purposes
test=TEST
#XTIT: card bar label: hours
hours=Hours
#XTIT: card subtitle: now
now=Now
#XTIT: card subtitle: last 24 hours
last24Hours=Last 24 Hours
#XTIT: card subtitle: last 48 hours
last48Hours=Last 48 Hours
#XTIT: card subtitle: last week
lastWeek=Last 7 Days
#XTIT: card subtitle: last month
lastMonth=Last Month
#XFLD: Close
close=Close
#XFLD: Today
today=Today
#XTIT: text for task tab
taskTab=Tasks
#XTIT: text for statement tab
statementTab=Statements
#XTIT: text fo nav link in table cell
viewStatements=View
#XTIT: text to more link in table cell
more=More
#XTIT: text for statement dialog header
statementDlgTitle=Statement
#XBUT: text for close btn on dialog
closeBtn=Close
#XBUT: text for copy btn on dialog
copyBtn=Copy
#XTIT: Copy success text on toast msg
copiedToClipboard=Copied to Clipboard
#XTIT: test for download btn on dialog
downloadBtn=Download

#XTIT: Monitoring table column names
startTime=Start Time
startDate=Start Date
duration=Duration
objectType=Object Type
activity=Activity
spaceName=Space Name
objectName=Object Name
peakMemory=SAP HANA Peak Memory
peakCPU=SAP HANA CPU Time
noOfRecords=Records
usedMemory=SAP HANA Used Memory
usedDisk=SAP HANA Used Disk
status=Status
subStatus=Substatus
user=User
targetTable=Target Table
statements=Statements
outOfMemory=Out-of-Memory
taskLogId=Task Log ID
statementDetails=Statement Details
parameters=Parameters
workloadClass=Workload Class
errorCode=Error Code
errorText=Error Message
dbUser=Database User
connectionID=Connection ID
statementID=Statement ID
elasticComputeNode=Elastic Compute Node


#Localization for @sap/dragonet-ui5-table-editor node module
#XTIT: Title for column settings dialog
columnsSettingsDialogTitle=Columns Settings
#XFLD: Title for Error type
messagesTableType=Type
#XFLD: Title for Error Message
messagesTableMessage=Message
#XFLD: Title for filter
filteredBy=Filtered by:
#XTIT: text for values contained in filter
filterContains=contains
#XTIT: text for values starting with in filter
filterStartsWith=starts with
#XTIT: text for values ending with in filter
filterEndsWith=ends with
#XTIT: Title for search in data preview toolbar
toolbarSearch=Search
#XBUT: Button to clear filter
clearFilter=Clear Filter
#XBUT: Button to cancel the operation
cancel=Cancel
#XBUT: Button to save the operation
ok=OK
#XBUT: Button to restore the data
toolbarRestoreButton=Refresh
#XBUT: Button to cancel running task
cancelBtnText=Cancel Task
#XBUT: tooltip for Button to restore the data
toolbarRestoreButtonTooltip=Update the Page
#XMSG: Busy dialog for Columns Settings dialog
PROCESSING=Processing...
#XMSG: Message Confirmation
confirmation=Confirmation
#XMSG: Message for refresh successful
refreshSuccess=Successfully Refreshed
#XMSG: Message for refresh successful
refreshSuccessful=Refreshed
#XMSG: Message for restore successful
restoreSuccessful=Successfully Restored
#XMSG: Error message when null value found on non-nullable field
mdm-emptyValueError="{0}" cannot be empty. {1}.
#XTIT: Sort Ascending
mdm-sortAscending=Sort Ascending
#XTIT: Sort Descending
mdm-sortDescending=Sort Descending
#XTIT: Filter
mdm-Filter=Filter
#XBUT: Button Cancel
mdm-cancel=Cancel
#XBUT: Button Add
mdm-Add=Add
#XMSG: and inside error message
and=and
#XMSG: Error message when no column selected
mdm-allColumnUnselected=Select one or more columns to continue.

#Localization keys not needed for System Monitor but required for @sap/dragonet-ui5-table-editor node module to remove console errors
#XTXT: Message information strip for filtering and sorting
mdm-InfoStrip=Filter, Sort, Delete and Table Setting tools are disabled by unsaved changes. Save your changes to enable them.
#XBUT: Button for file upload in mdm data preview
toolbarUploadButton=Upload Data
#XBUT: Button for duplicating rows in mdm data preview
toolbarDuplicateButton=Duplicate
#XBUT: Button for deleting rows in mdm data preview
toolbarDeleteButton=Delete
#XBUT: Button for adding rows in mdm data preview
toolbarAddButton=Add

#XTIT: MDM Empty value Handling in Table Editor Label
mdmInsertMissingString=Insert Missing String Value as:
#XTIT: MDM Empty value Handling in Table Editor Null Combobox item
mdmNullText=NULL
#XTIT: MDM Empty value Handling in Table Editor Empty string Combobox item
mdmEmptyStringText=Empty string
#XTXT: Message information strip for Insert Missing String Value
mdmInfoStripMissingString=Missing string values are inserted only in visible string columns in new and edited rows. Use Columns Settings to display all relevant columns.
#XTXT: Message information strip for filtering sorting and Insert Missing String Value
mdm-InfoStripWithMissingString=Filter, Sort, Insert Missing String Value and Table Setting tools are disabled by unsaved changes. Save your changes to enable them.
#XFLD: Open SAP HANA Cockpit
openHanaCockpit=Database Overview (SAP HANA Cockpit)
#XBUT: Open SAP HANA Performance Monitor Cockpit
openHanaCockpitPerfMonitor=Performance Monitor
openHanaCockpitPerfMonitorTooltip=Open Performance Monitor in SAP HANA Cockpit
#XTIT: card title: Admission Control Rejection Events
acRejectedEvents=Admission Control Rejection Events
#XTIT: card title: Admission Control Queuing Events
acQueuedEvents=Admission Control Queuing Events

#XTIT: card title: Top 5 Admission Control Rejection Events by Space
acRejectionEventsBySpace=Top 5 Admission Control Rejection Events by Space
#XTIT: card title: Top 5 Admission Control Queuing Events by Space
acQueueingEventsBySpace=Top 5 Admission Control Queuing Events by Space

#XTIT: payload Table and List texts
payloadDownloadText=Payload Downloaded
dataSourceText=Data Sources
dataSourcesObject=Name
dataSourcesSchema=Space Name
payloadListHeader=Payloads
payloadStoryIdText=Story ID
payloadStoryNameText=Story Name
payloadDialogTitleText=More Information
payloadTextAreaTitle=Payload
NO_MDS_DETAIL=No Data Available
MDS_DATA_FETCH_FAILED=We could not display information on MDS statements because of a server error.
GBUnit=GB
enabled=Enabled
notEnabled=Not Enabled
#XTIT: ECN Monitoring Dashboard Texts
ecnStatus=Status
ecnBlock=Compute Blocks
ecnSize=Size
ecnConfigMemory=Memory
ecnConfigStorage=Storage
ecnConfigSpacesList=List of Spaces
#XFLD: ECN phase Ready
ecnReady=Ready
#XFLD: ECN phase Running
ecnRunning=Running
#XFLD: ECN phase Initial
ecnInitial=Not Ready
#XFLD: ECN phase Starting
ecnStarting=Starting
#XFLD: ECN phase Stopping
ecnStopping=Stopping
#XFLD: ECN phase Unknown
ecnUnknown=Unknown
#XFLD: ECN Last Run Details
lastRun=Latest Run
#XFLD: ECN Previous Run Details
previousRun=Previous Run
#XFLD: ECN Manage Button
manage=Manage Elastic Compute Node
#XFLD: ECN Run Details Widget
days=Days
#XFLD: ECN Run Details Widget Title
runDetails=Run Details
#XFLD: ECN Performance Class Label
performanceClass=Performance Class
start=Start
stop=Stop
ecnRunDetailsUpTime=Uptime
ecnConfigurationCPU=Number of vCPUs
ecnTechnicalName=Technical Name
currentMonth=Current Month
previousMonth=Previous Month
ecnBlockHours=Block-Hours
upTimeOverall=Total Uptime
refresh=Refresh
top5MemConsumptionDataFetchErrorText=Failed to get data for memory consumption
ecnDashboardText=Elastic Compute Nodes
#XMSG: Message Title when ECN is not in ruuning state
memoryDistributionErrorTitle=Not Applicable
#XMSG: Warning Message when ECN is not in ruuning state
memoryDistributionErrorMessage=Memory distribution is displayed only when the elastic compute node is in a running state.
#XMSG: Title for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageTitle=No Elastic Compute Node Selected
#XMSG: Description for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageDescription=Start by selecting one from the list in the header.
#XMSG: Tab label for Statememt Logs
statementLogsText=Statement Logs
#XMSG: Tab label for Task Logs
taskLogsText=Task Logs
#MSG: Label for Date Time Quick Filter
labelForTimeQuickFilter=Date and Time Range:
labelForSpaceQuickFilter=Spaces
labelForStatusQuickFilter=Statuses
#XTIT: Cancel Task Started toast msg
cancelTaskStarted=Task cancellation started
#YMSE: Cancel Task Error
taskCancelError=The task cannot be cancelled because it has already finished running.
#LSA Monitor Tab Name
lsaMonitorText=Object Store
#ID for LSA Storage By Space Widget
hdlfStorageSpace=SAP HANA Data Lake Files: Storage Utilisation
#ID for LSA Storage for All Spaces Widget
hdlfStorageSpaceTotal=SAP HANA Data Lake Files: Storage Utilisation of All Spaces
#Labels for LSA Monitor
lsaSparkTableApplicationColumnText=Application
lsaSparkTableNoOfTasksColumnText=Number of Tasks
sparkApplicationConfigurationTitle=Application Configuration
sparkExecutorCPU=Executor CPU
sparkExecutorMemory=Executor Memory
sparkDriverCPU=Driver CPU
sparkDriverMemory=Driver Memory
sparkMaximumCPU=Maximum CPU
sparkMaximumMemory=Maximum Memory
sparkMinExecutors=Minimum Executors
sparkMaxExecutors=Maximum Executors
sparkAppTableNoDataIMTitle=No Space Selected
sparkAppTableNoDataIMDesc=Start by selecting one from the list in the filter bar.
TBUnit=TB
sparkTaskTableNoDataIMTitle=No Tasks Available
sparkTaskTableNoDataIMDesc=For the selected application, there are no tasks available. Select another application.
taskActivity=Task Activity
sparkTableNoSearchResultsTitle=No Search Results
sparkTableNoSearchResultsDesc=Change the search term and try again.
sparkTableNoFilterResultsTitle=No Filter Results
sparkTableNoFilterResultsDesc=Change the filter criteria and try again.
removeFilterText=Clear Filter
sortTableText=Sort Table
filterTableText=Filter
apacheSparkTableText=Apache Spark: Tasks
closeColumnText=Close Column
