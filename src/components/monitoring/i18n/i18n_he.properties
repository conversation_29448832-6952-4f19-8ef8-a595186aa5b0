#XTIT: Dynamic Page title
pageTitle=ברוך הבא למעקב מערכת
#XTIT: Dynamic Page subtitle
pageSubtitle=עקבו אחר הביצועים של המערכת וזהה בעיות אחסון, בעיות במשימות, בעיות של חוסר זיכרון פנוי ובעיות נוספות.
#XTIT: Messagestrip Enable Statement tracing
messagestripEnableStatementTracing=כדי לעקוב אחר שגיאות של חוסר זיכרון פנוי ואחר פרטי דוח נוספים, יש להפעיל מעקב אחר דוחות יקרים בתצורה/מעקב.
#XFLD: Dashboard text
dashBoardText=לוח מחוונים
#XFLD: TaskLog text
taskLogText=יומנים
#XTIT: card title: Storage Distribution
storageDistribution=אחסון דיסק בשימוש
#XTIT: card title: Memory Distribution
memoryDistribution=חלוקת זיכרון
#XTIT: card title:Used Disk Capacity
usedStorageCapacity=דיסק בשימוש ע"י מרחבים לאחסון
#XTIT: card title: Used Memory Capacity
usedMemoryCapacity=זיכרון בשימוש ע"י מרחבים לאחסון
#XTIT: card title:Assigned Disk Capacity
assignedStorageCapacity=דיסק שהוקצה למרחבים עבור אחסון
#XTIT: card title: Assigned Memory Capacity
assignedMemoryCapacity=זיכרון שהוקצה למרחבים עבור אחסון
#XTIT: card title: Errors
errors=משימות שנכשלו
#XTIT: card title: Configuration
ecnConfiguration=תצורה
#XTIT: card title: ECN Monthly Uptime
upTime=זמן פעילות חודשי
#XTIT: card title: ECN Average Memory
ECNAvgMemory=זיכרון ממוצע
#XTIT: card title: ECN Average CPU
ECNAvgCPU=CPU ממוצע
#XTIT: card Sub title: ECN Hours Subtitle
ECNHoursSubtitle={0} השעות האחרונות
#XTIT: card Title: ECN Last 5 Start Tasks
ecnTop5StartStop=5 משימות התחלה/הפסקה אחרונות של ECN
#XTIT: card Title: ECN Last 5 Stop Tasks
ecnTop5Stop=5 משימות הפסקה אחרונות של ECN
#XTIT: card title: Out of Memory Events (all)
OoMEvents=שגיאות חוסר זיכרון פנוי
#XTIT: card title: MDS
OoMMDSQueries=שגיאות חוסר זיכרון פנוי (בקשות MDS)
#XTIT: card title: Top 5 Memory Consumption (Statements)
memoryConsumptionStatements=5 הדוחות המובילים לפי עיבוד צריכת זכרון
#XTIT: card title: Out of Memory Events (Workload Class) by Space
OoMEventsByWorkloadClass=5 שגיאות חוסר הזיכרון הפנוי המובילות (סיווג עומס עבודה) לפי מרחב
#XTIT: card title: Run Duration
runDuration=5 המשימות המובילות לפי משך זמן הפעלה
#XTIT: card title: Top 5 Tasks by Memory Consumption
tasksBymemoryConsumption=5 המשימות המובילות לפי עיבוד צריכת זיכרון
#XTIT: card title: Memory Consumption
memoryConsumption=צריכת זיכרון
#XTIT: card title: Top 5 Memory Consumption (MDS)
memoryConsumptionSAC=5 בקשות MDS המובילות לפי עיבוד צריכת זיכרון
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=נתוני יומן ביקורת
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=נתונים מנהליים
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=נתונים אחרים
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=נתונים במרחבים
#XFLD: ResourceCategory Available Memory
ResourceCategoryAvailableMemory=זיכרון שאינו בשימוש
#XFLD: ResourceCategory Memory Used by Consumption
ResourceCategoryMemoryConsumption=זיכרון בשימוש עבור עיבוד
#XFLD: ResourceCategory Memory Used by Replication
ResourceCategoryMemoryReplication=זיכרון בשימוש עבור שכפול נתונים
#XFLD: ResourceCategory unknown
unknown=לא ידוע
#XBUT: Card footer view list button
viewList=הצג יומנים
#NOTR: placeholder for dev-purposes
test=TEST
#XTIT: card bar label: hours
hours=שעות
#XTIT: card subtitle: now
now=עכשיו
#XTIT: card subtitle: last 24 hours
last24Hours=ב-24 השעות האחרונות
#XTIT: card subtitle: last 48 hours
last48Hours=ב-48 השעות האחרונות
#XTIT: card subtitle: last week
lastWeek=7 הימים האחרונים
#XTIT: card subtitle: last month
lastMonth=חודש אחרון
#XFLD: Close
close=סגור
#XFLD: Today
today=היום
#XTIT: text for task tab
taskTab=משימות
#XTIT: text for statement tab
statementTab=דוחות
#XTIT: text fo nav link in table cell
viewStatements=תצוגה
#XTIT: text to more link in table cell
more=עוד
#XTIT: text for statement dialog header
statementDlgTitle=דוח
#XBUT: text for close btn on dialog
closeBtn=סגור
#XBUT: text for copy btn on dialog
copyBtn=העתק
#XTIT: Copy success text on toast msg
copiedToClipboard=הועתק אל לוח העריכה
#XTIT: test for download btn on dialog
downloadBtn=הורד

#XTIT: Monitoring table column names
startTime=שעת התחלה
startDate=תאריך התחלה
duration=משך זמן
objectType=סוג אובייקט
activity=פעילות
spaceName=שם מרחב
objectName=שם אובייקט
peakMemory=שיא שימוש בזיכרון - SAP HANA
peakCPU=SAP HANA CPU Time
noOfRecords=רשומות
usedMemory=זיכרון בשימוש - SAP HANA
usedDisk=דיסק בשימוש - SAP HANA
status=סטאטוס
subStatus=סטאטוס משנה
user=משתמש
targetTable=טבלת יעדים
statements=דוחות
outOfMemory=חוסר זיכרון פנוי
taskLogId=זיהוי יומן משימות
statementDetails=פרטי דף מצב חשבון
parameters=פרמטרים
workloadClass=סיווג עומס עבודה
errorCode=קוד שגיאה
errorText=הודעת שגיאה
dbUser=משתמש של בסיס נתונים
connectionID=זיהוי חיבור
statementID=מזהה משפט
elasticComputeNode=צומת חישוב גמיש


#Localization for @sap/dragonet-ui5-table-editor node module
#XTIT: Title for column settings dialog
columnsSettingsDialogTitle=הגדרות עמודות
#XFLD: Title for Error type
messagesTableType=סוג
#XFLD: Title for Error Message
messagesTableMessage=הודעה
#XFLD: Title for filter
filteredBy=סונן על ידי:
#XTIT: text for values contained in filter
filterContains=מכיל
#XTIT: text for values starting with in filter
filterStartsWith=מתחיל ב-
#XTIT: text for values ending with in filter
filterEndsWith=מסתיים ב-
#XTIT: Title for search in data preview toolbar
toolbarSearch=חפש
#XBUT: Button to clear filter
clearFilter=נקה מסנן
#XBUT: Button to cancel the operation
cancel=בטל
#XBUT: Button to save the operation
ok=OK
#XBUT: Button to restore the data
toolbarRestoreButton=רענן
#XBUT: Button to cancel running task
cancelBtnText=בטל משימה
#XBUT: tooltip for Button to restore the data
toolbarRestoreButtonTooltip=עדכן את הדף
#XMSG: Busy dialog for Columns Settings dialog
PROCESSING=מעבד...
#XMSG: Message Confirmation
confirmation=אישור
#XMSG: Message for refresh successful
refreshSuccess=רענון בוצע בהצלחה
#XMSG: Message for refresh successful
refreshSuccessful=בוצע רענון
#XMSG: Message for restore successful
restoreSuccessful=שוחזר בהצלחה
#XMSG: Error message when null value found on non-nullable field
mdm-emptyValueError=''{0}'' לא יכול להיות ריק. {1}.
#XTIT: Sort Ascending
mdm-sortAscending=מיין בסדר עולה
#XTIT: Sort Descending
mdm-sortDescending=מיין בסדר יורד
#XTIT: Filter
mdm-Filter=מסנן
#XBUT: Button Cancel
mdm-cancel=בטל
#XBUT: Button Add
mdm-Add=הוסף
#XMSG: and inside error message
and=ו-
#XMSG: Error message when no column selected
mdm-allColumnUnselected=בחר עמודה אחת או יותר כדי להמשיך.

#Localization keys not needed for System Monitor but required for @sap/dragonet-ui5-table-editor node module to remove console errors
#XTXT: Message information strip for filtering and sorting
mdm-InfoStrip=סנן, מיין, מחק וכלי הגדרות טבלה מושבתים ע"י שינויים שלא נשמרו. שמור את השינויים שלך כדי להפעיל אותם.
#XBUT: Button for file upload in mdm data preview
toolbarUploadButton=העלה נתונים
#XBUT: Button for duplicating rows in mdm data preview
toolbarDuplicateButton=שכפל
#XBUT: Button for deleting rows in mdm data preview
toolbarDeleteButton=מחק
#XBUT: Button for adding rows in mdm data preview
toolbarAddButton=הוסף

#XTIT: MDM Empty value Handling in Table Editor Label
mdmInsertMissingString=הוסף ערך מחרוזת חסר כמו:
#XTIT: MDM Empty value Handling in Table Editor Null Combobox item
mdmNullText=NULL
#XTIT: MDM Empty value Handling in Table Editor Empty string Combobox item
mdmEmptyStringText=מחרוזת ריקה
#XTXT: Message information strip for Insert Missing String Value
mdmInfoStripMissingString=ערכי מחרוזת חסרים  מתווספים רק בעמודות מחרוזות גלויות בשורות חדשות וערוכות. השתמש בהגדרות עמודות כדי להציג את כל העמודות הרלוונטיות.
#XTXT: Message information strip for filtering sorting and Insert Missing String Value
mdm-InfoStripWithMissingString=סנן, מיין, הוסף ערכי מחרוזת חסרים וכלי הגדרות טבלה מושבתים ע"י שינויים שלא נשמרו. שמור את השינויים שלך כדי להפעיל אותם.
#XFLD: Open SAP HANA Cockpit
openHanaCockpit=סקירת בסיס נתונים (מרכז השליטה של SAP HANA)
#XBUT: Open SAP HANA Performance Monitor Cockpit
openHanaCockpitPerfMonitor=מעקב אחרי ביצועים
openHanaCockpitPerfMonitorTooltip=פתח מעקב אחר ביצועים במרכז השליטה של SAP HANA
#XTIT: card title: Admission Control Rejection Events
acRejectedEvents=אירועי דחייה - בקרת קבלה
#XTIT: card title: Admission Control Queuing Events
acQueuedEvents=אירועי הכנסה לתור - בקרת קבלה

#XTIT: card title: Top 5 Admission Control Rejection Events by Space
acRejectionEventsBySpace=5 אירועי הדחייה המובילים לפי מרחב - בקרת קבלה
#XTIT: card title: Top 5 Admission Control Queuing Events by Space
acQueueingEventsBySpace=5 אירועי הכנסה לתור המובילים לפי מרחב - בקרת קבלה

#XTIT: payload Table and List texts
payloadDownloadText=תוכן מנה הורד
dataSourceText=מקורות נתונים
dataSourcesObject=שם
dataSourcesSchema=שם מרחב
payloadListHeader=תוכן מנה
payloadStoryIdText=זיהוי סיפור
payloadStoryNameText=שם סיפור
payloadDialogTitleText=מידע נוסף
payloadTextAreaTitle=תוכן מנה
NO_MDS_DETAIL=אין נתונים זמינים
MDS_DATA_FETCH_FAILED=לא ניתן היה להציג מידע על דוחות MDS בגלל שגיאת שרת.
GBUnit=GB
enabled=פועל
notEnabled=לא זמין
#XTIT: ECN Monitoring Dashboard Texts
ecnStatus=סטאטוס
ecnBlock=חשב בלוקים
ecnSize=גודל
ecnConfigMemory=זיכרון
ecnConfigStorage=אחסון
ecnConfigSpacesList=רשימת מרחבים
#XFLD: ECN phase Ready
ecnReady=מוכן
#XFLD: ECN phase Running
ecnRunning=פועל
#XFLD: ECN phase Initial
ecnInitial=לא מוכן
#XFLD: ECN phase Starting
ecnStarting=מתחיל
#XFLD: ECN phase Stopping
ecnStopping=עוצר
#XFLD: ECN phase Unknown
ecnUnknown=לא ידוע
#XFLD: ECN Last Run Details
lastRun=הפעלה אחרונה
#XFLD: ECN Previous Run Details
previousRun=הפעלה קודמת
#XFLD: ECN Manage Button
manage=נהל צומת חישוב גמיש
#XFLD: ECN Run Details Widget
days=ימים
#XFLD: ECN Run Details Widget Title
runDetails=פרטי הפעלה
#XFLD: ECN Performance Class Label
performanceClass=סיווג ביצוע
start=התחל
stop=הפסק
ecnRunDetailsUpTime=זמן פעילות
ecnConfigurationCPU=מספר vCPUs
ecnTechnicalName=שם טכני
currentMonth=חודש נוכחי
previousMonth=החודש שעבר
ecnBlockHours=שעות חסימה
upTimeOverall=סה"כ זמן פעילות
refresh=רענן
top5MemConsumptionDataFetchErrorText=קבלת הנתונים עבור צריכת הזיכרון נכשלה
ecnDashboardText=צמתי חישוב גמיש
#XMSG: Message Title when ECN is not in ruuning state
memoryDistributionErrorTitle=לא ישים
#XMSG: Warning Message when ECN is not in ruuning state
memoryDistributionErrorMessage=חלוקת זיכרון מוצגת רק כאשר צומת החישוב הגמיש במצב הפעלה.
#XMSG: Title for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageTitle=צומת חישוב אלסטי גמיש נבחר
#XMSG: Description for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageDescription=התחל בבחירת צומת מהרשימה בכותרת.
#XMSG: Tab label for Statememt Logs
statementLogsText=יומני דוחות
#XMSG: Tab label for Task Logs
taskLogsText=יומני משימות
#MSG: Label for Date Time Quick Filter
labelForTimeQuickFilter=טווח תאריכים ושעות
labelForSpaceQuickFilter=מרחבים
labelForStatusQuickFilter=סטאטוסים
#XTIT: Cancel Task Started toast msg
cancelTaskStarted=ביטול המשימה החל
#YMSE: Cancel Task Error
taskCancelError=לא ניתן לבטל את המשימה משום שהפעלתה כבר הסתיימה.
#LSA Monitor Tab Name
lsaMonitorText=חנות אובייקטים
#ID for LSA Storage By Space Widget
hdlfStorageSpace=קובצי SAP HANA Data Lake: ניצול אחסון
#ID for LSA Storage for All Spaces Widget
hdlfStorageSpaceTotal=קובצי SAP HANA Data Lake: ניצול אחסון של כל המרחבים
#Labels for LSA Monitor
lsaSparkTableApplicationColumnText=יישום
lsaSparkTableNoOfTasksColumnText=מספר משימות
sparkApplicationConfigurationTitle=קביעת תצורה של יישום
sparkExecutorCPU=CPU של מבצע
sparkExecutorMemory=זיכרון של מבצע
sparkDriverCPU=CPU של מנהל התקן
sparkDriverMemory=זיכרון של מנהל התקן
sparkMaximumCPU=CPU מקסימלי
sparkMaximumMemory=CPU מינימלי
sparkMinExecutors=מינימום מבצעים
sparkMaxExecutors=מקסימום מבצעים
sparkAppTableNoDataIMTitle=לא נבחר מרחב
sparkAppTableNoDataIMDesc=התחל בבחירת אחד מהרשימה בסרגל הסינון.
TBUnit=TB
sparkTaskTableNoDataIMTitle=אין משימות זמינות
sparkTaskTableNoDataIMDesc=עבור היישום 'בחר', אין משימות זמינות. בחר יישום אחר.
taskActivity=פעילות משימה
sparkTableNoSearchResultsTitle=אין תוצאות חיפוש
sparkTableNoSearchResultsDesc=שנה את מונח החיפוש ונסה שוב.
sparkTableNoFilterResultsTitle=אין תוצאות סינון
sparkTableNoFilterResultsDesc=שנה את קריטריוני הסינון ונסה שוב.
removeFilterText=נקה מסנן
sortTableText=מיין טבלה
filterTableText=סנן את הטבלה
apacheSparkTableText=Apache Spark: משימות
closeColumnText=סגור עמודה
