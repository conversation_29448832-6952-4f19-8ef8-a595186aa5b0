#XTIT: Dynamic Page title
pageTitle=ยินดีต้อนรับสู่ตัวติดตามตรวจสอบระบบ
#XTIT: Dynamic Page subtitle
pageSubtitle=ติดตามตรวจสอบประสิทธิภาพในระบบของคุณและระบุพื้นที่จัดเก็บ งาน หน่วยความจำไม่เพียงพอ และปัญหาอื่นๆ
#XTIT: Messagestrip Enable Statement tracing
messagestripEnableStatementTracing=เมื่อต้องการติดตามตรวจสอบข้อผิดพลาดของหน่วยความจำไม่เพียงพอและข้อมูลคำสั่งอื่นๆ กรุณาเปิดใช้งานการติดตามคำสั่งที่ใช้เวลานานในการกำหนดรูปแบบ/การติดตามตรวจสอบ
#XFLD: Dashboard text
dashBoardText=แดชบอร์ด
#XFLD: TaskLog text
taskLogText=ล็อก
#XTIT: card title: Storage Distribution
storageDistribution=พื้นที่จัดเก็บในดิสก์ที่ใช้
#XTIT: card title: Memory Distribution
memoryDistribution=การกระจายหน่วยความจำ
#XTIT: card title:Used Disk Capacity
usedStorageCapacity=ดิสก์ที่ใช้โดยพื้นที่จัดเก็บ
#XTIT: card title: Used Memory Capacity
usedMemoryCapacity=หน่วยความจำที่ใช้โดยพื้นที่จัดเก็บ
#XTIT: card title:Assigned Disk Capacity
assignedStorageCapacity=ดิสก์ที่กำหนดให้กับพื้นที่จัดเก็บ
#XTIT: card title: Assigned Memory Capacity
assignedMemoryCapacity=หน่วยความจำที่กำหนดให้กับพื้นที่จัดเก็บ
#XTIT: card title: Errors
errors=งานที่ล้มเหลว
#XTIT: card title: Configuration
ecnConfiguration=การกำหนดรูปแบบ
#XTIT: card title: ECN Monthly Uptime
upTime=อัพไทม์รายเดือน
#XTIT: card title: ECN Average Memory
ECNAvgMemory=หน่วยความจำเฉลี่ย
#XTIT: card title: ECN Average CPU
ECNAvgCPU=CPU เฉลี่ย
#XTIT: card Sub title: ECN Hours Subtitle
ECNHoursSubtitle={0} ชั่วโมงล่าสุด
#XTIT: card Title: ECN Last 5 Start Tasks
ecnTop5StartStop=5 งานที่เริ่มต้น/หยุดล่าสุดของ ECN
#XTIT: card Title: ECN Last 5 Stop Tasks
ecnTop5Stop=5 งานที่หยุดล่าสุดของ ECN
#XTIT: card title: Out of Memory Events (all)
OoMEvents=ข้อผิดพลาดของหน่วยความจำไม่เพียงพอ
#XTIT: card title: MDS
OoMMDSQueries=ข้อผิดพลาดของหน่วยความจำไม่เพียงพอ (คำขอ MDS)
#XTIT: card title: Top 5 Memory Consumption (Statements)
memoryConsumptionStatements=คำสั่ง 5 อันดับแรกตามการใช้หน่วยความจำในการประมวลผล
#XTIT: card title: Out of Memory Events (Workload Class) by Space
OoMEventsByWorkloadClass=ข้อผิดพลาดของหน่วยความจำไม่เพียงพอ 5 อันดับแรก (คลาสปริมาณงาน) ตามพื้นที่
#XTIT: card title: Run Duration
runDuration=งาน 5 อันดับแรกตามระยะเวลาการดำเนินการ
#XTIT: card title: Top 5 Tasks by Memory Consumption
tasksBymemoryConsumption=งาน 5 อันดับแรกตามการใช้หน่วยความจำในการประมวลผล
#XTIT: card title: Memory Consumption
memoryConsumption=การใช้หน่วยความจำ
#XTIT: card title: Top 5 Memory Consumption (MDS)
memoryConsumptionSAC=คำขอ MDS 5 อันดับแรกตามตามการใช้หน่วยความจำในการประมวลผล
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=ข้อมูลล็อกการตรวจสอบ
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=ข้อมูลด้านการจัดการ
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=ข้อมูลอื่นๆ
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=ข้อมูลในพื้นที่
#XFLD: ResourceCategory Available Memory
ResourceCategoryAvailableMemory=หน่วยความจำที่ไม่ได้ใช้
#XFLD: ResourceCategory Memory Used by Consumption
ResourceCategoryMemoryConsumption=หน่วยความจำที่ใช้สำหรับการประมวลผล
#XFLD: ResourceCategory Memory Used by Replication
ResourceCategoryMemoryReplication=หน่วยความจำที่ใช้สำหรับการทำสำเนาข้อมูล
#XFLD: ResourceCategory unknown
unknown=ไม่รู้จัก
#XBUT: Card footer view list button
viewList=ดูล็อก
#NOTR: placeholder for dev-purposes
test=TEST
#XTIT: card bar label: hours
hours=ชั่วโมง
#XTIT: card subtitle: now
now=ขณะนี้
#XTIT: card subtitle: last 24 hours
last24Hours=24 ชั่วโมงที่ผ่านมา
#XTIT: card subtitle: last 48 hours
last48Hours=48 ชั่วโมงที่ผ่านมา
#XTIT: card subtitle: last week
lastWeek=7 วันที่ผ่านมา
#XTIT: card subtitle: last month
lastMonth=เดือนที่แล้ว
#XFLD: Close
close=ปิด
#XFLD: Today
today=วันนี้
#XTIT: text for task tab
taskTab=งาน
#XTIT: text for statement tab
statementTab=คำสั่ง
#XTIT: text fo nav link in table cell
viewStatements=มุมมอง
#XTIT: text to more link in table cell
more=เพิ่มเติม
#XTIT: text for statement dialog header
statementDlgTitle=คำสั่ง
#XBUT: text for close btn on dialog
closeBtn=ปิด
#XBUT: text for copy btn on dialog
copyBtn=คัดลอก
#XTIT: Copy success text on toast msg
copiedToClipboard=คัดลอกไปยังคลิปบอร์ดแล้ว
#XTIT: test for download btn on dialog
downloadBtn=ดาวน์โหลด

#XTIT: Monitoring table column names
startTime=เวลาเริ่มต้น
startDate=วันที่เริ่มต้น
duration=ระยะเวลา
objectType=ประเภทออบเจค
activity=กิจกรรม
spaceName=ชื่อพื้นที่
objectName=ชื่อออบเจค
peakMemory=ช่วงที่ใช้หน่วยความจำสูงสุดของ SAP HANA
peakCPU=เวลา CPU ของ SAP HANA
noOfRecords=เรคคอร์ด
usedMemory=หน่วยความจำที่ใช้ของ SAP HANA
usedDisk=ดิสก์ที่ใช้ของ SAP HANA
status=สถานะ
subStatus=สถานะย่อย
user=ผู้ใช้
targetTable=ตารางเป้าหมาย
statements=คำสั่ง
outOfMemory=หน่วยความจำไม่เพียงพอ
taskLogId=ID ล็อกของงาน
statementDetails=รายละเอียดคำสั่ง
parameters=พารามิเตอร์
workloadClass=คลาสปริมาณงาน
errorCode=รหัสข้อผิดพลาด
errorText=ข้อความแสดงข้อผิดพลาด
dbUser=ผู้ใช้ฐานข้อมูล
connectionID=ID การเชื่อมต่อ
statementID=ID คำสั่ง
elasticComputeNode=โหนดการคำนวณแบบยืดหยุ่น


#Localization for @sap/dragonet-ui5-table-editor node module
#XTIT: Title for column settings dialog
columnsSettingsDialogTitle=การกำหนดค่าคอลัมน์
#XFLD: Title for Error type
messagesTableType=ประเภท
#XFLD: Title for Error Message
messagesTableMessage=ข้อความ
#XFLD: Title for filter
filteredBy=ฟิลเตอร์ตาม:
#XTIT: text for values contained in filter
filterContains=มี
#XTIT: text for values starting with in filter
filterStartsWith=เริ่มต้นด้วย
#XTIT: text for values ending with in filter
filterEndsWith=ลงท้ายด้วย
#XTIT: Title for search in data preview toolbar
toolbarSearch=ค้นหา
#XBUT: Button to clear filter
clearFilter=ล้างฟิลเตอร์
#XBUT: Button to cancel the operation
cancel=ยกเลิก
#XBUT: Button to save the operation
ok=ตกลง
#XBUT: Button to restore the data
toolbarRestoreButton=รีเฟรช
#XBUT: Button to cancel running task
cancelBtnText=ยกเลิกงาน
#XBUT: tooltip for Button to restore the data
toolbarRestoreButtonTooltip=อัพเดทหน้า
#XMSG: Busy dialog for Columns Settings dialog
PROCESSING=กำลังประมวลผล...
#XMSG: Message Confirmation
confirmation=การยืนยัน
#XMSG: Message for refresh successful
refreshSuccess=รีเฟรชได้สำเร็จ
#XMSG: Message for refresh successful
refreshSuccessful=รีเฟรชแล้ว
#XMSG: Message for restore successful
restoreSuccessful=คืนค่าได้สำเร็จ
#XMSG: Error message when null value found on non-nullable field
mdm-emptyValueError="{0}" ต้องไม่เว้นว่างไว้ {1}
#XTIT: Sort Ascending
mdm-sortAscending=จัดเรียงจากน้อยไปหามาก
#XTIT: Sort Descending
mdm-sortDescending=จัดเรียงจากมากไปหาน้อย
#XTIT: Filter
mdm-Filter=ฟิลเตอร์
#XBUT: Button Cancel
mdm-cancel=ยกเลิก
#XBUT: Button Add
mdm-Add=เพิ่ม
#XMSG: and inside error message
and=และ
#XMSG: Error message when no column selected
mdm-allColumnUnselected=เลือกอย่างน้อยหนึ่งคอลัมน์เพื่อดำเนินการต่อ

#Localization keys not needed for System Monitor but required for @sap/dragonet-ui5-table-editor node module to remove console errors
#XTXT: Message information strip for filtering and sorting
mdm-InfoStrip=เครื่องมือ 'ฟิลเตอร์' 'จัดเรียง' 'ลบ' และ 'การกำหนดค่าตาราง' ถูกปิดใช้งานเนื่องจากมีการเปลี่ยนแปลงที่ยังไม่ได้เก็บบันทึก กรุณาเก็บบันทึกการเปลี่ยนแปลงของคุณเพื่อเปิดใช้งาน
#XBUT: Button for file upload in mdm data preview
toolbarUploadButton=อัพโหลดข้อมูล
#XBUT: Button for duplicating rows in mdm data preview
toolbarDuplicateButton=ทำซ้ำ
#XBUT: Button for deleting rows in mdm data preview
toolbarDeleteButton=ลบ
#XBUT: Button for adding rows in mdm data preview
toolbarAddButton=เพิ่ม

#XTIT: MDM Empty value Handling in Table Editor Label
mdmInsertMissingString=แทรกค่าสตริงที่ขาดหายไปเป็น:
#XTIT: MDM Empty value Handling in Table Editor Null Combobox item
mdmNullText=ค่า Null
#XTIT: MDM Empty value Handling in Table Editor Empty string Combobox item
mdmEmptyStringText=สตริงว่าง
#XTXT: Message information strip for Insert Missing String Value
mdmInfoStripMissingString=ค่าสตริงที่ขาดหายไปจะถูกแทรกเฉพาะในคอลัมน์สตริงที่มองเห็นได้ในแถวใหม่และแถวที่แก้ไข ใช้การกำหนดค่าคอลัมน์เพื่อแสดงคอลัมน์ที่เกี่ยวข้องทั้งหมด
#XTXT: Message information strip for filtering sorting and Insert Missing String Value
mdm-InfoStripWithMissingString=เครื่องมือ 'ฟิลเตอร์' 'จัดเรียง' 'แทรกค่าสตริงที่ขาดหายไป' และ 'การกำหนดค่าตาราง' ถูกปิดใช้งานเนื่องจากมีการเปลี่ยนแปลงที่ยังไม่ได้เก็บบันทึก กรุณาเก็บบันทึกการเปลี่ยนแปลงของคุณเพื่อเปิดใช้งาน
#XFLD: Open SAP HANA Cockpit
openHanaCockpit=ภาพรวมฐานข้อมูล (SAP HANA Cockpit)
#XBUT: Open SAP HANA Performance Monitor Cockpit
openHanaCockpitPerfMonitor=ตัวติดตามตรวจสอบประสิทธิภาพ
openHanaCockpitPerfMonitorTooltip=เปิดตัวติดตามตรวจสอบประสิทธิภาพใน SAP HANA Cockpit
#XTIT: card title: Admission Control Rejection Events
acRejectedEvents=การควบคุมการเข้าถึง - เหตุการณ์การปฏิเสธ
#XTIT: card title: Admission Control Queuing Events
acQueuedEvents=การควบคุมการเข้าถึง - เหตุการณ์ที่อยู่ในคิว

#XTIT: card title: Top 5 Admission Control Rejection Events by Space
acRejectionEventsBySpace=การควบคุมการเข้าถึง - เหตุการณ์การปฏิเสธ 5 อันดับแรกตามพื้นที่
#XTIT: card title: Top 5 Admission Control Queuing Events by Space
acQueueingEventsBySpace=การควบคุมการเข้าถึง - เหตุการณ์การอยู่ในคิว 5 อันดับแรกตามพื้นที่

#XTIT: payload Table and List texts
payloadDownloadText=Payload ที่ดาวน์โหลด
dataSourceText=แหล่งข้อมูล
dataSourcesObject=ชื่อ
dataSourcesSchema=ชื่อพื้นที่
payloadListHeader=Payload
payloadStoryIdText=ID เรื่อง
payloadStoryNameText=ชื่อเนื้อหา
payloadDialogTitleText=ข้อมูลเพิ่มเติม
payloadTextAreaTitle=Payload
NO_MDS_DETAIL=ไม่มีข้อมูล
MDS_DATA_FETCH_FAILED=เราไม่สามารถแสดงข้อมูลในคำสั่ง MDS ได้เนื่องจากข้อผิดพลาดของเซิร์ฟเวอร์
GBUnit=GB
enabled=เปิดใช้งาน
notEnabled=ไม่ได้เปิดใช้งาน
#XTIT: ECN Monitoring Dashboard Texts
ecnStatus=สถานะ
ecnBlock=บล็อคที่คำนวณ
ecnSize=ขนาด
ecnConfigMemory=หน่วยความจำ
ecnConfigStorage=พื้นที่จัดเก็บ
ecnConfigSpacesList=รายการพื้นที่
#XFLD: ECN phase Ready
ecnReady=พร้อม
#XFLD: ECN phase Running
ecnRunning=กำลังดำเนินการ
#XFLD: ECN phase Initial
ecnInitial=ไม่พร้อม
#XFLD: ECN phase Starting
ecnStarting=เริ่มต้น
#XFLD: ECN phase Stopping
ecnStopping=กำลังหยุด
#XFLD: ECN phase Unknown
ecnUnknown=ไม่รู้จัก
#XFLD: ECN Last Run Details
lastRun=การดำเนินการล่าสุด
#XFLD: ECN Previous Run Details
previousRun=การดำเนินการก่อนหน้า
#XFLD: ECN Manage Button
manage=จัดการโหนดการคำนวณแบบยืดหยุ่น
#XFLD: ECN Run Details Widget
days=วัน
#XFLD: ECN Run Details Widget Title
runDetails=รายละเอียดการดำเนินการ
#XFLD: ECN Performance Class Label
performanceClass=คลาสประสิทธิภาพ
start=เริ่มต้น
stop=หยุด
ecnRunDetailsUpTime=อัพไทม์
ecnConfigurationCPU=จำนวน vCPU
ecnTechnicalName=ชื่อทางเทคนิค
currentMonth=เดือนปัจจุบัน
previousMonth=เดือนก่อนหน้า
ecnBlockHours=จำนวนชั่วโมงที่บล็อคไว้
upTimeOverall=อัพไทม์ทั้งหมด
refresh=รีเฟรช
top5MemConsumptionDataFetchErrorText=ไม่สามารถดึงข้อมูลการใช้หน่วยความจำ
ecnDashboardText=โหนดการคำนวณแบบยืดหยุ่น
#XMSG: Message Title when ECN is not in ruuning state
memoryDistributionErrorTitle=ไม่เกี่ยวข้อง
#XMSG: Warning Message when ECN is not in ruuning state
memoryDistributionErrorMessage=การกระจายหน่วยความจำจะแสดงก็ต่อเมื่อโหนดการคำนวณแบบยืดหยุ่นอยู่ในสถานะกำลังดำเนินการ
#XMSG: Title for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageTitle=ไม่ได้เลือกโหนดการคำนวณแบบยืดหยุ่น
#XMSG: Description for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageDescription=เริ่มต้นโดยการเลือกหนึ่งรายการจากรายการในส่วนหัว
#XMSG: Tab label for Statememt Logs
statementLogsText=ล็อกของคำสั่ง
#XMSG: Tab label for Task Logs
taskLogsText=ล็อกของงาน
#MSG: Label for Date Time Quick Filter
labelForTimeQuickFilter=ช่วงวันที่และเวลา:
labelForSpaceQuickFilter=พื้นที่
labelForStatusQuickFilter=สถานะ
#XTIT: Cancel Task Started toast msg
cancelTaskStarted=การยกเลิกงานเริ่มต้นแล้ว
#YMSE: Cancel Task Error
taskCancelError=ไม่สามารถยกเลิกงานได้เนื่องจากดำเนินการเสร็จสิ้นแล้ว
#LSA Monitor Tab Name
lsaMonitorText=ที่จัดเก็บออบเจค
#ID for LSA Storage By Space Widget
hdlfStorageSpace=SAP HANA, ไฟล์ Data Lake: การใช้พื้นที่จัดเก็บ
#ID for LSA Storage for All Spaces Widget
hdlfStorageSpaceTotal=SAP HANA, ไฟล์ Data Lake: การใช้พื้นที่จัดเก็บของพื้นที่ทั้งหมด
#Labels for LSA Monitor
lsaSparkTableApplicationColumnText=แอพพลิเคชัน
lsaSparkTableNoOfTasksColumnText=จำนวนงาน
sparkApplicationConfigurationTitle=การกำหนดรูปแบบแอพพลิเคชัน
sparkExecutorCPU=CPU ของตัวดำเนินการ
sparkExecutorMemory=หน่วยความจำของตัวดำเนินการ
sparkDriverCPU=CPU ของไดรเวอร์
sparkDriverMemory=หน่วยความจำของไดรเวอร์
sparkMaximumCPU=CPU สูงสุด
sparkMaximumMemory=หน่วยความจำสูงสุด
sparkMinExecutors=ตัวดำเนินการต่ำสุด
sparkMaxExecutors=ตัวดำเนินการสูงสุด
sparkAppTableNoDataIMTitle=ไม่ได้เลือกพื้นที่
sparkAppTableNoDataIMDesc=เริ่มต้นโดยการเลือกหนึ่งรายการจากรายการในแถบฟิลเตอร์
TBUnit=TB
sparkTaskTableNoDataIMTitle=ไม่มีงานที่พร้อมใช้งาน
sparkTaskTableNoDataIMDesc=ไม่มีงานที่พร้อมใช้งานสำหรับแอพพลิเคชันที่เลือก กรุณาเลือกแอพพลิเคชันอื่น
taskActivity=กิจกรรมของงาน
sparkTableNoSearchResultsTitle=ไม่มีผลลัพธ์การค้นหา
sparkTableNoSearchResultsDesc=เปลี่ยนแปลงคำที่ใช้ค้นหาแล้วลองอีกครั้ง
sparkTableNoFilterResultsTitle=ไม่มีผลลัพธ์ฟิลเตอร์
sparkTableNoFilterResultsDesc=เปลี่ยนแปลงเกณฑ์ฟิลเตอร์แล้วลองอีกครั้ง
removeFilterText=ล้างฟิลเตอร์
sortTableText=จัดเรียงตาราง
filterTableText=ฟิลเตอร์
apacheSparkTableText=Apache Spark: งาน
closeColumnText=ปิดคอลัมน์
