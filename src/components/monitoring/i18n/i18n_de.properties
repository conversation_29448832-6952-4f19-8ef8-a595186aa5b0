#XTIT: Dynamic Page title
pageTitle=<PERSON>z<PERSON> willkommen beim Systemmonitor
#XTIT: Dynamic Page subtitle
pageSubtitle=Überwachen Sie die Performance Ihres Systems, und ermitteln Sie Speicher-, Aufgaben-, Out-of-Memory- und andere Probleme.
#XTIT: Messagestrip Enable Statement tracing
messagestripEnableStatementTracing=Um Out-of-Memory-Fehler und andere Anweisungsinformationen zu überwachen, aktivieren Sie das Tracing für aufwendige Anweisungen in der Konfiguration bzw. im Monitoring.
#XFLD: Dashboard text
dashBoardText=Dashboard
#XFLD: TaskLog text
taskLogText=Protokolle
#XTIT: card title: Storage Distribution
storageDistribution=Verwendeter Festplattenspeicher
#XTIT: card title: Memory Distribution
memoryDistribution=Arbeitsspeicherverteilung
#XTIT: card title:Used Disk Capacity
usedStorageCapacity=Von Spaces für Speicherung verwendeter Festplattenspeicher 
#XTIT: card title: Used Memory Capacity
usedMemoryCapacity=Von Spaces für Speicherung verwendeter Arbeitsspeicher 
#XTIT: card title:Assigned Disk Capacity
assignedStorageCapacity=Spaces für Speicherung zugeordneter Festplattenspeicher
#XTIT: card title: Assigned Memory Capacity
assignedMemoryCapacity=Spaces für Speicherung zugeordneter Arbeitsspeicher
#XTIT: card title: Errors
errors=Fehlgeschlagene Aufgaben
#XTIT: card title: Configuration
ecnConfiguration=Konfiguration
#XTIT: card title: ECN Monthly Uptime
upTime=Monatliche Produktivzeit
#XTIT: card title: ECN Average Memory
ECNAvgMemory=Durchschnittliche Speicherauslastung
#XTIT: card title: ECN Average CPU
ECNAvgCPU=Durchschnittliche CPU-Auslastung
#XTIT: card Sub title: ECN Hours Subtitle
ECNHoursSubtitle=Letzte {0} Stunden
#XTIT: card Title: ECN Last 5 Start Tasks
ecnTop5StartStop=ECN: Letzte 5 begonnene/beendete Aufgaben
#XTIT: card Title: ECN Last 5 Stop Tasks
ecnTop5Stop=ECN: Letzte 5 beendete Aufgaben
#XTIT: card title: Out of Memory Events (all)
OoMEvents=Fehler aufgrund von unzureichendem Arbeitsspeicher
#XTIT: card title: MDS
OoMMDSQueries=Fehler aufgrund von unzureichendem Arbeitsspeicher (MDS-Anforderungen)
#XTIT: card title: Top 5 Memory Consumption (Statements)
memoryConsumptionStatements=Top-5-Anweisungen nach Speicherverbrauch für Verarbeitung
#XTIT: card title: Out of Memory Events (Workload Class) by Space
OoMEventsByWorkloadClass=Top-5-Fehler aufgrund von unzureichendem Arbeitsspeicher (Workload-Klasse) nach Space
#XTIT: card title: Run Duration
runDuration=Top-5-Aufgaben nach Ausführungsdauer
#XTIT: card title: Top 5 Tasks by Memory Consumption
tasksBymemoryConsumption=Top-5-Aufgaben nach Speicherverbrauch für Verarbeitung
#XTIT: card title: Memory Consumption
memoryConsumption=Speicherverbrauch
#XTIT: card title: Top 5 Memory Consumption (MDS)
memoryConsumptionSAC=Top-5-MDS-Anforderungen nach Speicherverbrauch für Verarbeitung
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Auditprotokolldaten
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Verwaltungsdaten
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Sonstige Daten
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Daten in Spaces
#XFLD: ResourceCategory Available Memory
ResourceCategoryAvailableMemory=Nicht verwendeter Speicher
#XFLD: ResourceCategory Memory Used by Consumption
ResourceCategoryMemoryConsumption=Für Verarbeitung verwendeter Speicher 
#XFLD: ResourceCategory Memory Used by Replication
ResourceCategoryMemoryReplication=Für Datenreplikation verwendeter Speicher
#XFLD: ResourceCategory unknown
unknown=Unbekannt
#XBUT: Card footer view list button
viewList=Protokolle anzeigen
#NOTR: placeholder for dev-purposes
test=TEST
#XTIT: card bar label: hours
hours=Stunden
#XTIT: card subtitle: now
now=Jetzt
#XTIT: card subtitle: last 24 hours
last24Hours=Letzte 24 Stunden
#XTIT: card subtitle: last 48 hours
last48Hours=Letzte 48 Stunden
#XTIT: card subtitle: last week
lastWeek=Letzte 7 Tage
#XTIT: card subtitle: last month
lastMonth=Letzter Monat
#XFLD: Close
close=Schließen
#XFLD: Today
today=Heute
#XTIT: text for task tab
taskTab=Aufgaben
#XTIT: text for statement tab
statementTab=Anweisungen
#XTIT: text fo nav link in table cell
viewStatements=View
#XTIT: text to more link in table cell
more=Mehr
#XTIT: text for statement dialog header
statementDlgTitle=Anweisung
#XBUT: text for close btn on dialog
closeBtn=Schließen
#XBUT: text for copy btn on dialog
copyBtn=Kopieren
#XTIT: Copy success text on toast msg
copiedToClipboard=In Zwischenablage kopiert
#XTIT: test for download btn on dialog
downloadBtn=Herunterladen

#XTIT: Monitoring table column names
startTime=Startzeit
startDate=Startdatum
duration=Dauer
objectType=Objekttyp
activity=Aktivität
spaceName=Space-Name
objectName=Objektname
peakMemory=Maximale Speicherauslastung für SAP HANA
peakCPU=SAP-HANA-CPU-Zeit
noOfRecords=Datensätze
usedMemory=Verwendeter Arbeitsspeicher für SAP HANA
usedDisk=Verwendeter Festplattenspeicher für SAP HANA
status=Status
subStatus=Unterstatus
user=Benutzer
targetTable=Zieltabelle
statements=Anweisungen
outOfMemory=Nicht genügend Arbeitsspeicher
taskLogId=Aufgabenprotokoll-ID
statementDetails=Anweisungsdetails
parameters=Parameter
workloadClass=Workload-Klasse
errorCode=Fehlercode
errorText=Fehlermeldung
dbUser=Datenbankbenutzer
connectionID=Verbindungs-ID
statementID=Anweisungs-ID
elasticComputeNode=Elastischer Rechenleistungsknoten


#Localization for @sap/dragonet-ui5-table-editor node module
#XTIT: Title for column settings dialog
columnsSettingsDialogTitle=Spalteneinstellungen
#XFLD: Title for Error type
messagesTableType=Typ
#XFLD: Title for Error Message
messagesTableMessage=Meldung
#XFLD: Title for filter
filteredBy=Gefiltert nach:
#XTIT: text for values contained in filter
filterContains=enthält
#XTIT: text for values starting with in filter
filterStartsWith=beginnt mit
#XTIT: text for values ending with in filter
filterEndsWith=endet auf
#XTIT: Title for search in data preview toolbar
toolbarSearch=Suchen
#XBUT: Button to clear filter
clearFilter=Filter zurücksetzen
#XBUT: Button to cancel the operation
cancel=Abbrechen
#XBUT: Button to save the operation
ok=OK
#XBUT: Button to restore the data
toolbarRestoreButton=Aktualisieren
#XBUT: Button to cancel running task
cancelBtnText=Aufgabe abbrechen
#XBUT: tooltip for Button to restore the data
toolbarRestoreButtonTooltip=Seite aktualisieren
#XMSG: Busy dialog for Columns Settings dialog
PROCESSING=Verarbeitung läuft …
#XMSG: Message Confirmation
confirmation=Bestätigung
#XMSG: Message for refresh successful
refreshSuccess=Erfolgreich aktualisiert
#XMSG: Message for refresh successful
refreshSuccessful=Aktualisiert
#XMSG: Message for restore successful
restoreSuccessful=Erfolgreich wiederhergestellt
#XMSG: Error message when null value found on non-nullable field
mdm-emptyValueError="{0}" darf nicht leer sein. {1}.
#XTIT: Sort Ascending
mdm-sortAscending=Aufsteigend sortieren
#XTIT: Sort Descending
mdm-sortDescending=Absteigend sortieren
#XTIT: Filter
mdm-Filter=Filtern
#XBUT: Button Cancel
mdm-cancel=Abbrechen
#XBUT: Button Add
mdm-Add=Hinzufügen
#XMSG: and inside error message
and=und
#XMSG: Error message when no column selected
mdm-allColumnUnselected=Wählen Sie eine oder mehrere Spalten aus, um fortzufahren.

#Localization keys not needed for System Monitor but required for @sap/dragonet-ui5-table-editor node module to remove console errors
#XTXT: Message information strip for filtering and sorting
mdm-InfoStrip=Die Werkzeuge Filtern, Sortieren, Löschen und Tabelleneinstellungen sind aufgrund nicht gesicherter Änderungen deaktiviert. Sichern Sie die Änderungen, um sie zu aktivieren.
#XBUT: Button for file upload in mdm data preview
toolbarUploadButton=Daten hochladen
#XBUT: Button for duplicating rows in mdm data preview
toolbarDuplicateButton=Duplizieren
#XBUT: Button for deleting rows in mdm data preview
toolbarDeleteButton=Löschen
#XBUT: Button for adding rows in mdm data preview
toolbarAddButton=Hinzufügen

#XTIT: MDM Empty value Handling in Table Editor Label
mdmInsertMissingString=Fehlenden Zeichenfolgenwert einfügen als:
#XTIT: MDM Empty value Handling in Table Editor Null Combobox item
mdmNullText=NULL
#XTIT: MDM Empty value Handling in Table Editor Empty string Combobox item
mdmEmptyStringText=Leere Zeichenfolge
#XTXT: Message information strip for Insert Missing String Value
mdmInfoStripMissingString=Fehlende Zeichenfolgenwerte werden in neuen und bearbeiteten Zeilen nur in sichtbare Zeichenfolgenspalten eingefügt. Verwenden Sie Spalteneinstellungen, um alle relevanten Spalten anzuzeigen.
#XTXT: Message information strip for filtering sorting and Insert Missing String Value
mdm-InfoStripWithMissingString=Die Werkzeuge Filtern, Sortieren, fehlende Zeichenfolge einfügen, Löschen und Tabelleneinstellungen sind aufgrund nicht gesicherter Änderungen deaktiviert. Sichern Sie die Änderungen, um sie zu aktivieren.
#XFLD: Open SAP HANA Cockpit
openHanaCockpit=Datenbankübersicht (SAP HANA Cockpit)
#XBUT: Open SAP HANA Performance Monitor Cockpit
openHanaCockpitPerfMonitor=Performance-Monitor
openHanaCockpitPerfMonitorTooltip=Performance-Monitor in SAP HANA Cockpit öffnen
#XTIT: card title: Admission Control Rejection Events
acRejectedEvents=Zugangssteuerung – abgelehnte Ereignisse
#XTIT: card title: Admission Control Queuing Events
acQueuedEvents=Zugangssteuerung – in Warteschlange aufgenommene Ereignisse

#XTIT: card title: Top 5 Admission Control Rejection Events by Space
acRejectionEventsBySpace=Zugangssteuerung – oberste 5 abgelehnte Ereignisse nach Space
#XTIT: card title: Top 5 Admission Control Queuing Events by Space
acQueueingEventsBySpace=Zugangssteuerung – oberste 5 in Warteschlange aufgenommene Ereignisse nach Space

#XTIT: payload Table and List texts
payloadDownloadText=Payload heruntergeladen
dataSourceText=Datenquellen
dataSourcesObject=Name
dataSourcesSchema=Space-Name
payloadListHeader=Payloads
payloadStoryIdText=Story-ID
payloadStoryNameText=Story-Name
payloadDialogTitleText=Weitere Informationen
payloadTextAreaTitle=Payload
NO_MDS_DETAIL=Keine Daten verfügbar
MDS_DATA_FETCH_FAILED=Die Informationen zu MDS-Anweisungen konnten aufgrund eines Serverfehlers nicht angezeigt werden.
GBUnit=GB
enabled=Aktiviert
notEnabled=Nicht aktiviert
#XTIT: ECN Monitoring Dashboard Texts
ecnStatus=Status
ecnBlock=Rechenleistungsblöcke
ecnSize=Größe
ecnConfigMemory=Arbeitsspeicher
ecnConfigStorage=Speicher
ecnConfigSpacesList=Liste der Spaces
#XFLD: ECN phase Ready
ecnReady=Bereit
#XFLD: ECN phase Running
ecnRunning=Wird ausgeführt
#XFLD: ECN phase Initial
ecnInitial=Nicht bereit
#XFLD: ECN phase Starting
ecnStarting=Startet
#XFLD: ECN phase Stopping
ecnStopping=Wird gestoppt
#XFLD: ECN phase Unknown
ecnUnknown=Unbekannt
#XFLD: ECN Last Run Details
lastRun=Letzter Lauf
#XFLD: ECN Previous Run Details
previousRun=Vorheriger Lauf
#XFLD: ECN Manage Button
manage=Elastischen Rechenleistungsknoten verwalten
#XFLD: ECN Run Details Widget
days=Tage
#XFLD: ECN Run Details Widget Title
runDetails=Laufdetails
#XFLD: ECN Performance Class Label
performanceClass=Leistungsklasse
start=Starten
stop=Stoppen
ecnRunDetailsUpTime=Produktivzeit
ecnConfigurationCPU=Anzahl vCPUs
ecnTechnicalName=Technischer Name
currentMonth=Laufender Monat
previousMonth=Letzter Monat
ecnBlockHours=Blockstunden
upTimeOverall=Produktivzeit gesamt
refresh=Aktualisieren
top5MemConsumptionDataFetchErrorText=Daten für Speicherverbrauch konnten nicht abgerufen werden
ecnDashboardText=Elastische Rechenleistungsknoten
#XMSG: Message Title when ECN is not in ruuning state
memoryDistributionErrorTitle=Nicht zutreffend
#XMSG: Warning Message when ECN is not in ruuning state
memoryDistributionErrorMessage=Die Arbeitsspeicherverteilung wird nur angezeigt, wenn der elastische Rechenleistungsknoten den Status "Wird ausgeführt" aufweist.
#XMSG: Title for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageTitle=Elastischer Rechenleistungsknoten nicht ausgewählt
#XMSG: Description for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageDescription=Wählen Sie zunächst einen Eintrag aus der Liste in der Kopfzeile aus.
#XMSG: Tab label for Statememt Logs
statementLogsText=Anweisungsprotokolle
#XMSG: Tab label for Task Logs
taskLogsText=Aufgabenprotokolle
#MSG: Label for Date Time Quick Filter
labelForTimeQuickFilter=Datums- und Zeitbereich:
labelForSpaceQuickFilter=Spaces
labelForStatusQuickFilter=Status
#XTIT: Cancel Task Started toast msg
cancelTaskStarted=Abbrechen der Aufgabe gestartet
#YMSE: Cancel Task Error
taskCancelError=Die Aufgabe kann nicht abgebrochen werden, da sie bereits abgeschlossen wurde.
#LSA Monitor Tab Name
lsaMonitorText=Objektspeicher
#ID for LSA Storage By Space Widget
hdlfStorageSpace=SAP-HANA-Data-Lake-Dateien: Speichernutzung
#ID for LSA Storage for All Spaces Widget
hdlfStorageSpaceTotal=SAP-HANA-Data-Lake-Dateien: Speichernutzung aller Spaces
#Labels for LSA Monitor
lsaSparkTableApplicationColumnText=Anwendung
lsaSparkTableNoOfTasksColumnText=Anzahl der Aufgaben
sparkApplicationConfigurationTitle=Anwendungskonfiguration
sparkExecutorCPU=Ausführer CPU
sparkExecutorMemory=Ausführer Speicher
sparkDriverCPU=Treiber CPU
sparkDriverMemory=Treiber Speicher
sparkMaximumCPU=Maximum CPU
sparkMaximumMemory=Maximum Speicher
sparkMinExecutors=Minimum Ausführer
sparkMaxExecutors=Maximum Ausführer
sparkAppTableNoDataIMTitle=Kein Space ausgewählt
sparkAppTableNoDataIMDesc=Wählen Sie zunächst einen Eintrag aus der Liste in der Filterleiste aus.
TBUnit=TB
sparkTaskTableNoDataIMTitle=Keine Aufgaben verfügbar
sparkTaskTableNoDataIMDesc=Für die ausgewählte Anwendung sind keine Aufgaben verfügbar. Wählen Sie eine andere Anwendung aus.
taskActivity=Aufgabenaktivität
sparkTableNoSearchResultsTitle=Keine Suchergebnisse
sparkTableNoSearchResultsDesc=Ändern Sie den Suchbegriff und versuchen Sie es erneut.
sparkTableNoFilterResultsTitle=Keine Filterergebnisse
sparkTableNoFilterResultsDesc=Ändern Sie die Filterkriterien und versuchen Sie es erneut.
removeFilterText=Filter zurücksetzen
sortTableText=Tabelle sortieren
filterTableText=Filtern
apacheSparkTableText=Apache Spark: Aufgaben
closeColumnText=Spalte schließen
