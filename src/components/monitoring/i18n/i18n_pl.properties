#XTIT: Dynamic Page title
pageTitle=Witamy w Monitorze systemu
#XTIT: Dynamic Page subtitle
pageSubtitle=<PERSON><PERSON><PERSON><PERSON> monitorować wydajność swojego systemu oraz identyfikować problemy dotyczące przechowywania, zadań, braku pamięci oraz inne problemy.
#XTIT: Messagestrip Enable Statement tracing
messagestripEnableStatementTracing=Aby monitorować błędy braku pamięci oraz inne informacje dotyczące instrukcji, włącz śledzenie czasochłonnych instrukcji w sekcji Konfiguracja/monitorowanie.
#XFLD: Dashboard text
dashBoardText=Pulpit
#XFLD: TaskLog text
taskLogText=Logi
#XTIT: card title: Storage Distribution
storageDistribution=Wykorzystywana pamięć dyskowa
#XTIT: card title: Memory Distribution
memoryDistribution=Rozdział pamięci
#XTIT: card title:Used Disk Capacity
usedStorageCapacity=Dysk wykorzystany przez przestrzenie do przechowywania
#XTIT: card title: Used Memory Capacity
usedMemoryCapacity=Pamięć wykorzystywana przez przestrzenie do przechowywania
#XTIT: card title:Assigned Disk Capacity
assignedStorageCapacity=Dysk przypisany do przestrzeni do przechowywania
#XTIT: card title: Assigned Memory Capacity
assignedMemoryCapacity=Pamięć przypisana do przestrzeni do przechowywania
#XTIT: card title: Errors
errors=Zadania zakończone niepowodzeniem
#XTIT: card title: Configuration
ecnConfiguration=Konfiguracja
#XTIT: card title: ECN Monthly Uptime
upTime=Czas aktywności w miesiącu
#XTIT: card title: ECN Average Memory
ECNAvgMemory=Pamięć — średnio
#XTIT: card title: ECN Average CPU
ECNAvgCPU=CPU — średnio
#XTIT: card Sub title: ECN Hours Subtitle
ECNHoursSubtitle=Ost. {0} godz.
#XTIT: card Title: ECN Last 5 Start Tasks
ecnTop5StartStop=ECN - ost. 5 zadań rozpoczęcia/zatrzymania
#XTIT: card Title: ECN Last 5 Stop Tasks
ecnTop5Stop=ECN - ost. 5 zadań zatrzymania
#XTIT: card title: Out of Memory Events (all)
OoMEvents=Błędy braku pamięci
#XTIT: card title: MDS
OoMMDSQueries=Błędy braku pamięci (żądania MDS)
#XTIT: card title: Top 5 Memory Consumption (Statements)
memoryConsumptionStatements=5 pierwszych instrukcji według wykorzystania pamięci podczas przetwarzania
#XTIT: card title: Out of Memory Events (Workload Class) by Space
OoMEventsByWorkloadClass=5 pierwszych błędów braku pamięci (klasa obciążenia) według przestrzeni
#XTIT: card title: Run Duration
runDuration=5 pierwszych zadań według czasu trwania przebiegu
#XTIT: card title: Top 5 Tasks by Memory Consumption
tasksBymemoryConsumption=5 pierwszych zadań według wykorzystania pamięci podczas przetwarzania
#XTIT: card title: Memory Consumption
memoryConsumption=Zużycie pamięci
#XTIT: card title: Top 5 Memory Consumption (MDS)
memoryConsumptionSAC=5 pierwszych żądań MDS według wykorzystania pamięci podczas przetwarzania
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Dane logów audytu
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Dane administracyjne
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Inne dane
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Dane w przestrzeniach
#XFLD: ResourceCategory Available Memory
ResourceCategoryAvailableMemory=Niewykorzystana pamięć
#XFLD: ResourceCategory Memory Used by Consumption
ResourceCategoryMemoryConsumption=Pamięć wykorzystana do przetwarzania
#XFLD: ResourceCategory Memory Used by Replication
ResourceCategoryMemoryReplication=Pamięć wykorzystana do replikacji danych
#XFLD: ResourceCategory unknown
unknown=Nieznane
#XBUT: Card footer view list button
viewList=Wyświetl logi
#NOTR: placeholder for dev-purposes
test=TEST
#XTIT: card bar label: hours
hours=Godz.
#XTIT: card subtitle: now
now=Teraz
#XTIT: card subtitle: last 24 hours
last24Hours=Ostatnie 24 godziny
#XTIT: card subtitle: last 48 hours
last48Hours=Ostatnie 48 godziny
#XTIT: card subtitle: last week
lastWeek=Ostatnie 7 dni
#XTIT: card subtitle: last month
lastMonth=Ostatni miesiąc
#XFLD: Close
close=Zamknij
#XFLD: Today
today=Dzisiaj
#XTIT: text for task tab
taskTab=Zadania
#XTIT: text for statement tab
statementTab=Instrukcje
#XTIT: text fo nav link in table cell
viewStatements=Widok
#XTIT: text to more link in table cell
more=Więcej
#XTIT: text for statement dialog header
statementDlgTitle=Instrukcja
#XBUT: text for close btn on dialog
closeBtn=Zamknij
#XBUT: text for copy btn on dialog
copyBtn=Kopiuj
#XTIT: Copy success text on toast msg
copiedToClipboard=Skopiowano do schowka
#XTIT: test for download btn on dialog
downloadBtn=Pobierz

#XTIT: Monitoring table column names
startTime=Czas rozpoczęcia
startDate=Data rozpoczęcia
duration=Czas trwania
objectType=Typ obiektu
activity=Działanie
spaceName=Nazwa przestrzeni
objectName=Nazwa obiektu
peakMemory=SAP HANA - pamięć szczytowa
peakCPU=Czas procesora SAP HANA
noOfRecords=Rekordy
usedMemory=SAP HANA - wykorzystana pamięć
usedDisk=SAP HANA - wykorzystany dysk
status=Status
subStatus=Status częściowy
user=Użytkownik
targetTable=Tabela docelowa
statements=Instrukcje
outOfMemory=Brak pamięci
taskLogId=ID logu zadań
statementDetails=Szczegóły instrukcji
parameters=Parametry
workloadClass=Klasa obciążenia
errorCode=Kod błędu
errorText=Komunikat o błędzie
dbUser=Użytkownik bazy danych
connectionID=ID połączenia
statementID=ID instrukcji
elasticComputeNode=Elastyczny węzeł obliczeniowy


#Localization for @sap/dragonet-ui5-table-editor node module
#XTIT: Title for column settings dialog
columnsSettingsDialogTitle=Ustawienia kolumn
#XFLD: Title for Error type
messagesTableType=Typ
#XFLD: Title for Error Message
messagesTableMessage=Komunikat
#XFLD: Title for filter
filteredBy=Filtrowanie według:
#XTIT: text for values contained in filter
filterContains=zawiera
#XTIT: text for values starting with in filter
filterStartsWith=rozpoczyna się od
#XTIT: text for values ending with in filter
filterEndsWith=kończy się na
#XTIT: Title for search in data preview toolbar
toolbarSearch=Wyszukiwanie
#XBUT: Button to clear filter
clearFilter=Wyczyść filtr
#XBUT: Button to cancel the operation
cancel=Anuluj
#XBUT: Button to save the operation
ok=OK
#XBUT: Button to restore the data
toolbarRestoreButton=Odśwież
#XBUT: Button to cancel running task
cancelBtnText=Anuluj zadanie
#XBUT: tooltip for Button to restore the data
toolbarRestoreButtonTooltip=Zaktualizuj stronę
#XMSG: Busy dialog for Columns Settings dialog
PROCESSING=Przetwarzanie...
#XMSG: Message Confirmation
confirmation=Potwierdzenie
#XMSG: Message for refresh successful
refreshSuccess=Pomyślnie odświeżono
#XMSG: Message for refresh successful
refreshSuccessful=Odświeżone
#XMSG: Message for restore successful
restoreSuccessful=Pomyślnie przywrócono
#XMSG: Error message when null value found on non-nullable field
mdm-emptyValueError=Pole "{0}" nie może być puste. {1}.
#XTIT: Sort Ascending
mdm-sortAscending=Sortuj rosnąco
#XTIT: Sort Descending
mdm-sortDescending=Sortuj malejąco
#XTIT: Filter
mdm-Filter=Filtruj
#XBUT: Button Cancel
mdm-cancel=Anuluj
#XBUT: Button Add
mdm-Add=Dodaj
#XMSG: and inside error message
and=oraz
#XMSG: Error message when no column selected
mdm-allColumnUnselected=Wybierz co najmniej jedną kolumnę, aby kontynuować.

#Localization keys not needed for System Monitor but required for @sap/dragonet-ui5-table-editor node module to remove console errors
#XTXT: Message information strip for filtering and sorting
mdm-InfoStrip=Funkcja filtrowania, sortowania, usuwania i narzędzia ustawień tabeli są nieaktywne z powodu niezapisanych zmian. Zapisz zmiany, aby je aktywować.
#XBUT: Button for file upload in mdm data preview
toolbarUploadButton=Prześlij dane
#XBUT: Button for duplicating rows in mdm data preview
toolbarDuplicateButton=Duplikowanie
#XBUT: Button for deleting rows in mdm data preview
toolbarDeleteButton=Usuwanie
#XBUT: Button for adding rows in mdm data preview
toolbarAddButton=Dodawanie

#XTIT: MDM Empty value Handling in Table Editor Label
mdmInsertMissingString=Wstaw brakującą wartość ciągu znaków jako:
#XTIT: MDM Empty value Handling in Table Editor Null Combobox item
mdmNullText=NULL
#XTIT: MDM Empty value Handling in Table Editor Empty string Combobox item
mdmEmptyStringText=Pusty ciąg znaków
#XTXT: Message information strip for Insert Missing String Value
mdmInfoStripMissingString=Brakujące wartości ciągu znaków są wstawiane tylko w widocznych kolumnach ciągów znaków. Użyj narzędzia Ustawienia kolumn, aby wyświetlić wszystkie istotne kolumny.
#XTXT: Message information strip for filtering sorting and Insert Missing String Value
mdm-InfoStripWithMissingString=Funkcja filtrowania, sortowania, wstawiania brakującej wartości ciągu znaków i narzędzia ustawień tabeli są nieaktywne z powodu niezapisanych zmian. Zapisz zmiany, aby je aktywować.
#XFLD: Open SAP HANA Cockpit
openHanaCockpit=Przegląd bazy danych (kokpit SAP HANA)
#XBUT: Open SAP HANA Performance Monitor Cockpit
openHanaCockpitPerfMonitor=Monitor wydajności
openHanaCockpitPerfMonitorTooltip=Otwórz Monitor wydajności w kokpicie SAP HANA
#XTIT: card title: Admission Control Rejection Events
acRejectedEvents=Kontrola przyjęcia - zdarzenia odrzucenia
#XTIT: card title: Admission Control Queuing Events
acQueuedEvents=Kontrola przyjęcia - zdarzenia w kolejce

#XTIT: card title: Top 5 Admission Control Rejection Events by Space
acRejectionEventsBySpace=Kontrola przyjęcia - 5 pierwszych zdarzeń odrzucenia według przestrzeni
#XTIT: card title: Top 5 Admission Control Queuing Events by Space
acQueueingEventsBySpace=Kontrola przyjęcia - 5 pierwszych zdarzeń w kolejce według przestrzeni

#XTIT: payload Table and List texts
payloadDownloadText=Pobrano dane biznesowe
dataSourceText=Źródła danych
dataSourcesObject=Nazwa
dataSourcesSchema=Nazwa przestrzeni
payloadListHeader=Dane biznesowe
payloadStoryIdText=ID raportu
payloadStoryNameText=Nazwa raportu
payloadDialogTitleText=Więcej informacji
payloadTextAreaTitle=Dane biznesowe
NO_MDS_DETAIL=Dane niedostępne
MDS_DATA_FETCH_FAILED=Nie można było wyświetlić informacji o instrukcjach MDS z powodu błędu serwera.
GBUnit=GB
enabled=Aktywowano
notEnabled=Nie aktywowano
#XTIT: ECN Monitoring Dashboard Texts
ecnStatus=Status
ecnBlock=Bloki obliczania
ecnSize=Rozmiar
ecnConfigMemory=Pamięć
ecnConfigStorage=Pamięć
ecnConfigSpacesList=Lista przestrzeni
#XFLD: ECN phase Ready
ecnReady=Gotowe
#XFLD: ECN phase Running
ecnRunning=W toku
#XFLD: ECN phase Initial
ecnInitial=Niegotowe
#XFLD: ECN phase Starting
ecnStarting=Uruchamianie
#XFLD: ECN phase Stopping
ecnStopping=Zatrzymywanie
#XFLD: ECN phase Unknown
ecnUnknown=Nieznane
#XFLD: ECN Last Run Details
lastRun=Ostatnie uruchomienie
#XFLD: ECN Previous Run Details
previousRun=Poprzedni przebieg
#XFLD: ECN Manage Button
manage=Zarządzaj elastycznym węzłem obliczeniowym
#XFLD: ECN Run Details Widget
days=Dni
#XFLD: ECN Run Details Widget Title
runDetails=Szczegóły przebiegu
#XFLD: ECN Performance Class Label
performanceClass=Klasa wydajności
start=Rozpocznij
stop=Zatrzymaj
ecnRunDetailsUpTime=Czas aktywności
ecnConfigurationCPU=Liczba wirtualnych procesorów
ecnTechnicalName=Nazwa techniczna
currentMonth=Bieżący miesiąc
previousMonth=Poprzedni miesiąc
ecnBlockHours=Godziny bloku
upTimeOverall=Łączny czas aktywności
refresh=Odśwież
top5MemConsumptionDataFetchErrorText=Nie udało się pobrać danych wykorzystania pamięci
ecnDashboardText=Elastyczne węzły obliczeniowe
#XMSG: Message Title when ECN is not in ruuning state
memoryDistributionErrorTitle=Nie dotyczy
#XMSG: Warning Message when ECN is not in ruuning state
memoryDistributionErrorMessage=Rozdział pamięci jest wyświetlany tylko, jeśli elastyczny węzeł obliczeniowy ma status aktywny.
#XMSG: Title for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageTitle=Nie wybrano elastycznego węzła obliczeniowego
#XMSG: Description for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageDescription=Rozpocznij od wybrania jednego z listy w nagłówku.
#XMSG: Tab label for Statememt Logs
statementLogsText=Logi oświadczeń
#XMSG: Tab label for Task Logs
taskLogsText=Logi zadań
#MSG: Label for Date Time Quick Filter
labelForTimeQuickFilter=Zakres dat i godzin:
labelForSpaceQuickFilter=Przestrzenie
labelForStatusQuickFilter=Statusy
#XTIT: Cancel Task Started toast msg
cancelTaskStarted=Rozpoczęto anulowanie zadania
#YMSE: Cancel Task Error
taskCancelError=Zadania nie można anulować, ponieważ już się zakończyło.
#LSA Monitor Tab Name
lsaMonitorText=Pamięć obiektów
#ID for LSA Storage By Space Widget
hdlfStorageSpace=Pliki SAP HANA Data Lake: Wykorzystanie pamięci
#ID for LSA Storage for All Spaces Widget
hdlfStorageSpaceTotal=Pliki SAP HANA Data Lake: Wykorzystanie pamięci wszystkich przestrzeni
#Labels for LSA Monitor
lsaSparkTableApplicationColumnText=Aplikacja
lsaSparkTableNoOfTasksColumnText=Liczba zadań
sparkApplicationConfigurationTitle=Konfiguracja aplikacji
sparkExecutorCPU=Wykonawca – CPU
sparkExecutorMemory=Wykonawca – pamięć
sparkDriverCPU=Sterownik – CPU
sparkDriverMemory=Sterownik – pamięć
sparkMaximumCPU=Maksimum – CPU
sparkMaximumMemory=Maksymalna pamięć
sparkMinExecutors=Minimum – wykonawcy
sparkMaxExecutors=Maksimum – wykonawcy
sparkAppTableNoDataIMTitle=Nie wybrano przestrzeni
sparkAppTableNoDataIMDesc=Rozpocznij od wybrania jednej pozycji z listy na pasku filtrowania..
TBUnit=TB
sparkTaskTableNoDataIMTitle=Brak dostępnych zadań
sparkTaskTableNoDataIMDesc=Dla wybranej aplikacji nie ma dostępnych zadań. Wybierz inną aplikację.
taskActivity=Zadanie w ramach działania
sparkTableNoSearchResultsTitle=Brak wyników wyszukiwania
sparkTableNoSearchResultsDesc=Zmień wyszukiwany termin i spróbuj ponownie.
sparkTableNoFilterResultsTitle=Brak wyników filtrowania
sparkTableNoFilterResultsDesc=Zmień kryteria filtrowania i spróbuj ponownie.
removeFilterText=Wyczyść filtr
sortTableText=Sortuj tabelę
filterTableText=Filtruj
apacheSparkTableText=Apache Spark: Zadania
closeColumnText=Zamknij kolumnę
