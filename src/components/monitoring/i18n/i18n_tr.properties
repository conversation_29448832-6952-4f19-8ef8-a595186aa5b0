#XTIT: Dynamic Page title
pageTitle=Sistem izlemeye hoş geldiniz
#XTIT: Dynamic Page subtitle
pageSubtitle=Sisteminizin performansını izleyin ve depolama, görev, yetersiz bellek sorunları ile diğer sorunları belirleyin.
#XTIT: Messagestrip Enable Statement tracing
messagestripEnableStatementTracing=Yetersiz bellek hatalarını ve diğer ifade bilgilerini izlemek için konfigürasyon/izleme kapsamında zaman alan ifade izlemeyi etkinleştirin.
#XFLD: Dashboard text
dashBoardText=Gösterge tablosu
#XFLD: TaskLog text
taskLogText=Günlükler
#XTIT: card title: Storage Distribution
storageDistribution=Kullanılan disk depolaması
#XTIT: card title: Memory Distribution
memoryDistribution=Bellek dağıtımı
#XTIT: card title:Used Disk Capacity
usedStorageCapacity=Alanlar tarafından depolama için kullanılan disk
#XTIT: card title: Used Memory Capacity
usedMemoryCapacity=Alanlar tarafından depolama için kullanılan bellek
#XTIT: card title:Assigned Disk Capacity
assignedStorageCapacity=Alanlara depolama için tayin edilen disk
#XTIT: card title: Assigned Memory Capacity
assignedMemoryCapacity=Alanlara depolama için tayin edilen bellek
#XTIT: card title: Errors
errors=Başarısız olan görevler
#XTIT: card title: Configuration
ecnConfiguration=Konfigürasyon
#XTIT: card title: ECN Monthly Uptime
upTime=Aylık çalışma süresi
#XTIT: card title: ECN Average Memory
ECNAvgMemory=Ortalama bellek
#XTIT: card title: ECN Average CPU
ECNAvgCPU=Ortalama CPU
#XTIT: card Sub title: ECN Hours Subtitle
ECNHoursSubtitle=Son {0} saat
#XTIT: card Title: ECN Last 5 Start Tasks
ecnTop5StartStop=ECN son 5 başlatma/durdurma görevi
#XTIT: card Title: ECN Last 5 Stop Tasks
ecnTop5Stop=ECN son 5 durdurma görevi
#XTIT: card title: Out of Memory Events (all)
OoMEvents=Yetersiz bellek hataları
#XTIT: card title: MDS
OoMMDSQueries=Yetersiz bellek hataları (MDS talepleri)
#XTIT: card title: Top 5 Memory Consumption (Statements)
memoryConsumptionStatements=İşleme belleği kullanımına göre ilk 5 ifade
#XTIT: card title: Out of Memory Events (Workload Class) by Space
OoMEventsByWorkloadClass=Alana göre ilk 5 yetersiz bellek hatası (iş yükü sınıfı)
#XTIT: card title: Run Duration
runDuration=Çalıştırma süresine göre ilk 5 görev
#XTIT: card title: Top 5 Tasks by Memory Consumption
tasksBymemoryConsumption=İşleme belleği kullanımına göre ilk 5 görev
#XTIT: card title: Memory Consumption
memoryConsumption=Bellek kullanımı
#XTIT: card title: Top 5 Memory Consumption (MDS)
memoryConsumptionSAC=İşleme belleği kullanımına göre ilk 5 MDS talebi
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Denetim günlüğü verileri
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Yönetim verileri
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Diğer veriler
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Alanlardaki veriler
#XFLD: ResourceCategory Available Memory
ResourceCategoryAvailableMemory=Kullanılmayan bellek
#XFLD: ResourceCategory Memory Used by Consumption
ResourceCategoryMemoryConsumption=İşleme için kullanılan bellek
#XFLD: ResourceCategory Memory Used by Replication
ResourceCategoryMemoryReplication=Veri çoğaltma için kullanılan bellek
#XFLD: ResourceCategory unknown
unknown=Bilinmiyor
#XBUT: Card footer view list button
viewList=Günlükleri görüntüle
#NOTR: placeholder for dev-purposes
test=TEST
#XTIT: card bar label: hours
hours=Saat
#XTIT: card subtitle: now
now=Şimdi
#XTIT: card subtitle: last 24 hours
last24Hours=Son 24 saat
#XTIT: card subtitle: last 48 hours
last48Hours=Son 48 saat
#XTIT: card subtitle: last week
lastWeek=Son 7 gün
#XTIT: card subtitle: last month
lastMonth=Son ay
#XFLD: Close
close=Kapat
#XFLD: Today
today=Bugün
#XTIT: text for task tab
taskTab=Görevler
#XTIT: text for statement tab
statementTab=İfadeler
#XTIT: text fo nav link in table cell
viewStatements=Görüntüle
#XTIT: text to more link in table cell
more=Daha fazla
#XTIT: text for statement dialog header
statementDlgTitle=İfade
#XBUT: text for close btn on dialog
closeBtn=Kapat
#XBUT: text for copy btn on dialog
copyBtn=Kopyala
#XTIT: Copy success text on toast msg
copiedToClipboard=Panoya kopyalandı
#XTIT: test for download btn on dialog
downloadBtn=İndir

#XTIT: Monitoring table column names
startTime=Başlangıç zamanı
startDate=Başlangıç tarihi
duration=Süre
objectType=Nesne türü
activity=Aktivite
spaceName=Alan adı
objectName=Nesne adı
peakMemory=SAP HANA için azami bellek
peakCPU=SAP HANA CPU zamanı
noOfRecords=Kayıtlar
usedMemory=SAP HANA için kullanılan bellek
usedDisk=SAP HANA için kullanılan disk
status=Durum
subStatus=Alt durum
user=Kullanıcı
targetTable=Hedef tablo
statements=İfadeler
outOfMemory=Yetersiz bellek
taskLogId=Görev günlüğü tanıtıcısı
statementDetails=İfade ayrıntıları
parameters=Parametreler
workloadClass=İş yükü sınıfı
errorCode=Hata kodu
errorText=Hata iletisi
dbUser=Veri tabanı kullanıcısı
connectionID=Bağlantı tanıtıcısı
statementID=Deyim tanıtıcısı
elasticComputeNode=Esnek işlem düğümü


#Localization for @sap/dragonet-ui5-table-editor node module
#XTIT: Title for column settings dialog
columnsSettingsDialogTitle=Sütun ayarları
#XFLD: Title for Error type
messagesTableType=Tür
#XFLD: Title for Error Message
messagesTableMessage=İleti
#XFLD: Title for filter
filteredBy=Filtre ölçütü:
#XTIT: text for values contained in filter
filterContains=şunu içerir
#XTIT: text for values starting with in filter
filterStartsWith=şununla başlar
#XTIT: text for values ending with in filter
filterEndsWith=şununla biter
#XTIT: Title for search in data preview toolbar
toolbarSearch=Ara
#XBUT: Button to clear filter
clearFilter=Filtreyi temizle
#XBUT: Button to cancel the operation
cancel=İptal
#XBUT: Button to save the operation
ok=Tamam
#XBUT: Button to restore the data
toolbarRestoreButton=Yenile
#XBUT: Button to cancel running task
cancelBtnText=Görevi iptal et
#XBUT: tooltip for Button to restore the data
toolbarRestoreButtonTooltip=Sayfayı güncelle
#XMSG: Busy dialog for Columns Settings dialog
PROCESSING=İşleniyor...
#XMSG: Message Confirmation
confirmation=Teyit
#XMSG: Message for refresh successful
refreshSuccess=Başarıyla yenilendi
#XMSG: Message for refresh successful
refreshSuccessful=Yenilendi
#XMSG: Message for restore successful
restoreSuccessful=Başarıyla geri yüklendi
#XMSG: Error message when null value found on non-nullable field
mdm-emptyValueError="{0}" boş olamaz. {1}.
#XTIT: Sort Ascending
mdm-sortAscending=Artan düzende sırala
#XTIT: Sort Descending
mdm-sortDescending=Azalan düzende sırala
#XTIT: Filter
mdm-Filter=Filtrele
#XBUT: Button Cancel
mdm-cancel=İptal
#XBUT: Button Add
mdm-Add=Ekle
#XMSG: and inside error message
and=ve
#XMSG: Error message when no column selected
mdm-allColumnUnselected=Devam etmek için bir veya daha fazla sütun seçin.

#Localization keys not needed for System Monitor but required for @sap/dragonet-ui5-table-editor node module to remove console errors
#XTXT: Message information strip for filtering and sorting
mdm-InfoStrip=Filtrele, sırala, sil ve tablo ayarı araçları, kaydedilmeyen değişiklikler nedeniyle devre dışı bırakıldı. Etkinleştirmek için değişikliklerinizi kaydedin.
#XBUT: Button for file upload in mdm data preview
toolbarUploadButton=Verileri yükle
#XBUT: Button for duplicating rows in mdm data preview
toolbarDuplicateButton=Çoğalt
#XBUT: Button for deleting rows in mdm data preview
toolbarDeleteButton=Sil
#XBUT: Button for adding rows in mdm data preview
toolbarAddButton=Ekle

#XTIT: MDM Empty value Handling in Table Editor Label
mdmInsertMissingString=Eksik dize değerini şu şekilde ekle:
#XTIT: MDM Empty value Handling in Table Editor Null Combobox item
mdmNullText=NULL
#XTIT: MDM Empty value Handling in Table Editor Empty string Combobox item
mdmEmptyStringText=Boş dize
#XTXT: Message information strip for Insert Missing String Value
mdmInfoStripMissingString=Eksik dize değerleri yalnızca yeni ve düzenlenen satırlardaki görünür dizelere eklenebilir. Tüm ilgili sütunları görüntülemek için sütun ayarlarını kullanın.
#XTXT: Message information strip for filtering sorting and Insert Missing String Value
mdm-InfoStripWithMissingString=Filtrele, sırala, eksik dize değerini ekle ve tablo ayarı araçları kaydedilmeyen değişiklikler tarafından devre dışı bırakıldı. Etkinleştirmek için değişikliklerinizi kaydedin.
#XFLD: Open SAP HANA Cockpit
openHanaCockpit=Veri tabanına genel bakış (SAP HANA kokpiti)
#XBUT: Open SAP HANA Performance Monitor Cockpit
openHanaCockpitPerfMonitor=Performans izleme
openHanaCockpitPerfMonitorTooltip=SAP HANA kokpitinde performans izleme bölümünü aç
#XTIT: card title: Admission Control Rejection Events
acRejectedEvents=Giriş kontrolü ret olayları
#XTIT: card title: Admission Control Queuing Events
acQueuedEvents=Giriş kontrolü kuyruğa alma olayları

#XTIT: card title: Top 5 Admission Control Rejection Events by Space
acRejectionEventsBySpace=Alana göre ilk 5 giriş kontrolü ret olayı
#XTIT: card title: Top 5 Admission Control Queuing Events by Space
acQueueingEventsBySpace=Alana göre ilk 5 giriş kontrolü kuyruğa alma olayı

#XTIT: payload Table and List texts
payloadDownloadText=Payload indirildi
dataSourceText=Veri kaynakları
dataSourcesObject=Ad
dataSourcesSchema=Alan adı
payloadListHeader=Payload'lar
payloadStoryIdText=Hikaye tanıtıcısı
payloadStoryNameText=Hikaye adı
payloadDialogTitleText=Daha fazla bilgi
payloadTextAreaTitle=Payload
NO_MDS_DETAIL=Kullanılabilir veri yok
MDS_DATA_FETCH_FAILED=Sunucu hatası nedeniyle MDS ifadeleriyle ilgili bilgileri görüntüleyemedik.
GBUnit=GB
enabled=Etkin
notEnabled=Etkin değil
#XTIT: ECN Monitoring Dashboard Texts
ecnStatus=Durum
ecnBlock=İşlem blokları
ecnSize=Büyüklük
ecnConfigMemory=Bellek
ecnConfigStorage=Depolama
ecnConfigSpacesList=Alan listesi
#XFLD: ECN phase Ready
ecnReady=Hazır
#XFLD: ECN phase Running
ecnRunning=Çalışıyor
#XFLD: ECN phase Initial
ecnInitial=Hazır değil
#XFLD: ECN phase Starting
ecnStarting=Başlatılıyor
#XFLD: ECN phase Stopping
ecnStopping=Durduruluyor
#XFLD: ECN phase Unknown
ecnUnknown=Bilinmiyor
#XFLD: ECN Last Run Details
lastRun=Son çalıştırma
#XFLD: ECN Previous Run Details
previousRun=Önceki çalıştırma
#XFLD: ECN Manage Button
manage=Esnek işlem düğümünü yönet
#XFLD: ECN Run Details Widget
days=Gün
#XFLD: ECN Run Details Widget Title
runDetails=Çalıştırma ayrıntıları
#XFLD: ECN Performance Class Label
performanceClass=Performans sınıfı
start=Başlat
stop=Durdur
ecnRunDetailsUpTime=Çalışma süresi
ecnConfigurationCPU=vCPU sayısı
ecnTechnicalName=Teknik ad
currentMonth=Geçerli ay
previousMonth=Önceki ay
ecnBlockHours=Blok saatler
upTimeOverall=Toplam çalışma süresi
refresh=Yenile
top5MemConsumptionDataFetchErrorText=Bellek kullanımı verileri alınamadı
ecnDashboardText=Esnek işlem düğümleri
#XMSG: Message Title when ECN is not in ruuning state
memoryDistributionErrorTitle=Uygun değil
#XMSG: Warning Message when ECN is not in ruuning state
memoryDistributionErrorMessage=Bellek dağıtımı yalnızca esnek işlem düğümü çalışan durumda olduğunda görüntülenir.
#XMSG: Title for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageTitle=Esnek işlem düğümü seçilmedi
#XMSG: Description for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageDescription=Başlıktaki listeden bir tane seçerek başlayın.
#XMSG: Tab label for Statememt Logs
statementLogsText=İfade günlükleri
#XMSG: Tab label for Task Logs
taskLogsText=Görev günlükleri
#MSG: Label for Date Time Quick Filter
labelForTimeQuickFilter=Tarih ve zaman aralığı:
labelForSpaceQuickFilter=Alanlar
labelForStatusQuickFilter=Durumlar
#XTIT: Cancel Task Started toast msg
cancelTaskStarted=Görev iptali başlatıldı
#YMSE: Cancel Task Error
taskCancelError=Görevin çalıştırılması zaten tamamlandığından görev iptal edilemiyor.
#LSA Monitor Tab Name
lsaMonitorText=Nesne deposu
#ID for LSA Storage By Space Widget
hdlfStorageSpace=SAP HANA veri gölü dosyaları: depolama kullanımı
#ID for LSA Storage for All Spaces Widget
hdlfStorageSpaceTotal=SAP HANA veri gölü dosyaları: tüm alanların depolama kullanımı
#Labels for LSA Monitor
lsaSparkTableApplicationColumnText=Uygulama
lsaSparkTableNoOfTasksColumnText=Görev sayısı
sparkApplicationConfigurationTitle=Uygulama konfigürasyonu
sparkExecutorCPU=Yürütücü CPU'su
sparkExecutorMemory=Yürütücü belleği
sparkDriverCPU=Sürücü CPU'su
sparkDriverMemory=Sürücü belleği
sparkMaximumCPU=Azami CPU
sparkMaximumMemory=Azami bellek
sparkMinExecutors=Asgari yürütücü sayısı
sparkMaxExecutors=Azami yürütücü sayısı
sparkAppTableNoDataIMTitle=Alan seçilmedi
sparkAppTableNoDataIMDesc=Filtre çubuğundaki listeden bir tane seçerek başlayın.
TBUnit=TB
sparkTaskTableNoDataIMTitle=Görev mevcut değil
sparkTaskTableNoDataIMDesc=Seçilen uygulama için görev mevcut değil. Başka bir uygulama seçin.
taskActivity=Görev aktivitesi
sparkTableNoSearchResultsTitle=Arama sonucu yok
sparkTableNoSearchResultsDesc=Arama terimini değiştirip tekrar deneyin.
sparkTableNoFilterResultsTitle=Filtre sonucu yok
sparkTableNoFilterResultsDesc=Filtre ölçütlerini değiştirip tekrar deneyin.
removeFilterText=Filtreyi temizle
sortTableText=Tabloyu sırala
filterTableText=Filtrele
apacheSparkTableText=Apache Spark: görevler
closeColumnText=Sütunu kapat
