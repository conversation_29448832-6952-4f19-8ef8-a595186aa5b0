#XTIT: Dynamic Page title
pageTitle=Tervetuloa järjestelmänvalvontaan
#XTIT: Dynamic Page subtitle
pageSubtitle=Valvo järjestelmäsi suorituskykyä ja tunnista muisti, teht<PERSON><PERSON><PERSON>, muistin loppuminen ja muut tapaukset.
#XTIT: Messagestrip Enable Statement tracing
messagestripEnableStatementTracing=Voit valvota muistin loppumisen virheitä ja muita lausevirheitä aktivoimalla kalliiden lauseiden jäljityksen Konfiguroinnissa/Valvonnassa.
#XFLD: Dashboard text
dashBoardText=Kojetaulu
#XFLD: TaskLog text
taskLogText=Lokit
#XTIT: card title: Storage Distribution
storageDistribution=Levymuistia käytetty
#XTIT: card title: Memory Distribution
memoryDistribution=Muistin hajauttaminen
#XTIT: card title:Used Disk Capacity
usedStorageCapacity=Tilojen tallennukseen käyttämä levymuisti
#XTIT: card title: Used Memory Capacity
usedMemoryCapacity=Tilojen tallennukseen käyttämä muisti
#XTIT: card title:Assigned Disk Capacity
assignedStorageCapacity=<PERSON> kohdistettu tiloihin tallennukseen
#XTIT: card title: Assigned Memory Capacity
assignedMemoryCapacity=Muisti kohdistettu tiloihin tallennukseen
#XTIT: card title: Errors
errors=Epäonnistuneet tehtävät
#XTIT: card title: Configuration
ecnConfiguration=Konfiguraatio
#XTIT: card title: ECN Monthly Uptime
upTime=Kuukausittainen tuotantoaika
#XTIT: card title: ECN Average Memory
ECNAvgMemory=Keskimääräinen muisti
#XTIT: card title: ECN Average CPU
ECNAvgCPU=Keskimääräinen CPU
#XTIT: card Sub title: ECN Hours Subtitle
ECNHoursSubtitle=Viimeksi kuluneet {0} tuntia
#XTIT: card Title: ECN Last 5 Start Tasks
ecnTop5StartStop=ECN: viimeiset 5 käynnistys-/pysäytystehtävää
#XTIT: card Title: ECN Last 5 Stop Tasks
ecnTop5Stop=ECN: viimeiset 5 pysäytystehtävää
#XTIT: card title: Out of Memory Events (all)
OoMEvents=Muistin loppumisen virheet
#XTIT: card title: MDS
OoMMDSQueries=Muistin loppumisen virheet (MDS-pyynnöt)
#XTIT: card title: Top 5 Memory Consumption (Statements)
memoryConsumptionStatements=Top 5 -lauseet muistin käytön käsittelyn mukaan
#XTIT: card title: Out of Memory Events (Workload Class) by Space
OoMEventsByWorkloadClass=Top 5 -muistin loppumisen virheet (työkuormitus) tilan mukaan
#XTIT: card title: Run Duration
runDuration=Top 5 -tehtävät ajon keston mukaan
#XTIT: card title: Top 5 Tasks by Memory Consumption
tasksBymemoryConsumption=Top 5 -tehtävät muistin käytön käsittelyn mukaan
#XTIT: card title: Memory Consumption
memoryConsumption=Muistin käyttö
#XTIT: card title: Top 5 Memory Consumption (MDS)
memoryConsumptionSAC=Top 5 MDS-pyynnöt muistin käytön käsittelyn mukaan
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Tarkastuslokin tiedot
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Hallintatiedot
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Muut tiedot
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Tilojen tiedot
#XFLD: ResourceCategory Available Memory
ResourceCategoryAvailableMemory=Käyttämätön muisti
#XFLD: ResourceCategory Memory Used by Consumption
ResourceCategoryMemoryConsumption=Käsittelyyn käytetty muisti
#XFLD: ResourceCategory Memory Used by Replication
ResourceCategoryMemoryReplication=Tietojen replikointiin käytetty muisti
#XFLD: ResourceCategory unknown
unknown=Tuntematon
#XBUT: Card footer view list button
viewList=Näytä lokit
#NOTR: placeholder for dev-purposes
test=TEST
#XTIT: card bar label: hours
hours=Tunnit
#XTIT: card subtitle: now
now=Nyt
#XTIT: card subtitle: last 24 hours
last24Hours=Viimeiset 24 tuntia
#XTIT: card subtitle: last 48 hours
last48Hours=Viimeiset 48 tuntia
#XTIT: card subtitle: last week
lastWeek=Viimeiset 7 päivää
#XTIT: card subtitle: last month
lastMonth=Viime kuukausi
#XFLD: Close
close=Sulje
#XFLD: Today
today=Tänään
#XTIT: text for task tab
taskTab=Tehtävät
#XTIT: text for statement tab
statementTab=Lauseet
#XTIT: text fo nav link in table cell
viewStatements=Näkymä
#XTIT: text to more link in table cell
more=Lisää
#XTIT: text for statement dialog header
statementDlgTitle=Lause
#XBUT: text for close btn on dialog
closeBtn=Sulje
#XBUT: text for copy btn on dialog
copyBtn=Kopioi
#XTIT: Copy success text on toast msg
copiedToClipboard=Kopioitu leikepöydälle
#XTIT: test for download btn on dialog
downloadBtn=Lataa paikallisesti

#XTIT: Monitoring table column names
startTime=Alkuaika
startDate=Alkupäivämäärä
duration=Kesto
objectType=Objektityyppi
activity=Toiminto
spaceName=Tilan nimi
objectName=Objektin nimi
peakMemory=SAP HANA -huippumuisti
peakCPU=SAP HANA CPU -aika
noOfRecords=Tietueet
usedMemory=Käytetty SAP HANA -muisti
usedDisk=Käytetty SAP HANA -levy
status=Tila
subStatus=Alitila
user=Käyttäjä
targetTable=Kohdetaulu
statements=Lauseet
outOfMemory=Muistin loppuminen
taskLogId=Tehtävälokin tunnus
statementDetails=Lauseen lisätiedot
parameters=Parametrit
workloadClass=Työkuormituksen luokka
errorCode=Virhekoodi
errorText=Virheilmoitus
dbUser=Tietokantakäyttäjä
connectionID=Yhteyden tunnus
statementID=Lauseen tunnus
elasticComputeNode=Joustava laskentasolmu


#Localization for @sap/dragonet-ui5-table-editor node module
#XTIT: Title for column settings dialog
columnsSettingsDialogTitle=Sarakeasetukset
#XFLD: Title for Error type
messagesTableType=Tyyppi
#XFLD: Title for Error Message
messagesTableMessage=Ilmoitus
#XFLD: Title for filter
filteredBy=Suodatusperuste:
#XTIT: text for values contained in filter
filterContains=sisältää
#XTIT: text for values starting with in filter
filterStartsWith=alussa
#XTIT: text for values ending with in filter
filterEndsWith=lopussa
#XTIT: Title for search in data preview toolbar
toolbarSearch=Hae
#XBUT: Button to clear filter
clearFilter=Tyhjennä suodatin
#XBUT: Button to cancel the operation
cancel=Peruuta
#XBUT: Button to save the operation
ok=OK
#XBUT: Button to restore the data
toolbarRestoreButton=Päivitä
#XBUT: Button to cancel running task
cancelBtnText=Peruuta tehtävä
#XBUT: tooltip for Button to restore the data
toolbarRestoreButtonTooltip=Sivun päivitys
#XMSG: Busy dialog for Columns Settings dialog
PROCESSING=Käsitellään...
#XMSG: Message Confirmation
confirmation=Vahvistus
#XMSG: Message for refresh successful
refreshSuccess=Päivitys onnistui
#XMSG: Message for refresh successful
refreshSuccessful=Päivitetty
#XMSG: Message for restore successful
restoreSuccessful=Palautettu onnistuneesti
#XMSG: Error message when null value found on non-nullable field
mdm-emptyValueError="{0}" ei voi olla tyhjä. {1}.
#XTIT: Sort Ascending
mdm-sortAscending=Lajittele nousevasti
#XTIT: Sort Descending
mdm-sortDescending=Lajittele laskevasti
#XTIT: Filter
mdm-Filter=Suodatin
#XBUT: Button Cancel
mdm-cancel=Peruuta
#XBUT: Button Add
mdm-Add=Lisää
#XMSG: and inside error message
and=ja
#XMSG: Error message when no column selected
mdm-allColumnUnselected=Jatka valitsemalla yksi tai useampi sarake.

#Localization keys not needed for System Monitor but required for @sap/dragonet-ui5-table-editor node module to remove console errors
#XTXT: Message information strip for filtering and sorting
mdm-InfoStrip=Tallentamattomat muutokset ovat poistaneet työkalut Suodata, Poista ja Taulukkoasetukset käytöstä. Aktivoi ne tallentamalla muutokset.
#XBUT: Button for file upload in mdm data preview
toolbarUploadButton=Lataa tiedot palvelimeen
#XBUT: Button for duplicating rows in mdm data preview
toolbarDuplicateButton=Kopioi
#XBUT: Button for deleting rows in mdm data preview
toolbarDeleteButton=Poista
#XBUT: Button for adding rows in mdm data preview
toolbarAddButton=Lisää

#XTIT: MDM Empty value Handling in Table Editor Label
mdmInsertMissingString=Lisää puuttuva merkkijonoarvo muodossa:
#XTIT: MDM Empty value Handling in Table Editor Null Combobox item
mdmNullText=TYHJÄ
#XTIT: MDM Empty value Handling in Table Editor Empty string Combobox item
mdmEmptyStringText=Tyhjä merkkijono
#XTXT: Message information strip for Insert Missing String Value
mdmInfoStripMissingString=Puuttuvia merkkijonoarvoja lisätään vain näkyviin merkkijonosarakkeisiin uusilla ja muokatuilla riveillä. Kaikki relevantit sarakkeet voidaan näyttää sarakeasetusten avulla.
#XTXT: Message information strip for filtering sorting and Insert Missing String Value
mdm-InfoStripWithMissingString=Tallentamattomat muutokset ovat poistaneet työkalut Suodata, Poista, Lisää puuttuva merkkijonoarvo ja Taulukkoasetukset käytöstä. Aktivoi ne tallentamalla muutoksesi.
#XFLD: Open SAP HANA Cockpit
openHanaCockpit=Tietokannan yleistiedot (SAP HANA -ohjaamo)
#XBUT: Open SAP HANA Performance Monitor Cockpit
openHanaCockpitPerfMonitor=Suorituskyvyn valvonta
openHanaCockpitPerfMonitorTooltip=Avaa suorituskyvyn valvonta SAP HANA -ohjaamossa
#XTIT: card title: Admission Control Rejection Events
acRejectedEvents=Pääsyvalvonnan hylkäystapahtumat
#XTIT: card title: Admission Control Queuing Events
acQueuedEvents=Pääsyvalvonnan jonotapahtumat

#XTIT: card title: Top 5 Admission Control Rejection Events by Space
acRejectionEventsBySpace=Pääsyvalvonnan viisi ylintä hylkäystapahtumaa tilan mukaan
#XTIT: card title: Top 5 Admission Control Queuing Events by Space
acQueueingEventsBySpace=Pääsyvalvonnan viisi ylintä jonotapahtumaa tilan mukaan

#XTIT: payload Table and List texts
payloadDownloadText=Hyötykuorma ladattu paikallisesti
dataSourceText=Tietolähteet
dataSourcesObject=Nimi
dataSourcesSchema=Tilan nimi
payloadListHeader=Hyötykuormat
payloadStoryIdText=Kuvaustunnus
payloadStoryNameText=Kuvauksen nimi
payloadDialogTitleText=Lisätietoja
payloadTextAreaTitle=Hyötykuorma
NO_MDS_DETAIL=Tietoja ei ole käytettävissä
MDS_DATA_FETCH_FAILED=Emme voineet näyttää tietoja MDS-lauseista palvelinvirheen vuoksi.
GBUnit=GB
enabled=Aktivoitu
notEnabled=Ei aktivoitu
#XTIT: ECN Monitoring Dashboard Texts
ecnStatus=Tila
ecnBlock=Laskentalohkot
ecnSize=Koko
ecnConfigMemory=Muisti
ecnConfigStorage=Tallennustila
ecnConfigSpacesList=Luettelo tiloista
#XFLD: ECN phase Ready
ecnReady=Valmis
#XFLD: ECN phase Running
ecnRunning=Käynnissä
#XFLD: ECN phase Initial
ecnInitial=Ei valmiina
#XFLD: ECN phase Starting
ecnStarting=Käynnistetään
#XFLD: ECN phase Stopping
ecnStopping=Pysäytetään
#XFLD: ECN phase Unknown
ecnUnknown=Tuntematon
#XFLD: ECN Last Run Details
lastRun=Viimeinen ajo
#XFLD: ECN Previous Run Details
previousRun=Edellinen ajo
#XFLD: ECN Manage Button
manage=Hallinnoi joustavaa laskentasolmua
#XFLD: ECN Run Details Widget
days=Pv
#XFLD: ECN Run Details Widget Title
runDetails=Ajon lisätiedot
#XFLD: ECN Performance Class Label
performanceClass=Suorituskykyluokka
start=Käynnistä
stop=Pysäytä
ecnRunDetailsUpTime=Tuotantoaika
ecnConfigurationCPU=vCPU-lukumäärä
ecnTechnicalName=Tekninen nimi
currentMonth=Kuluva kuukausi
previousMonth=Edellinen kuukausi
ecnBlockHours=Blokkitunnit
upTimeOverall=Kokonaistuotantoaika
refresh=Päivitä
top5MemConsumptionDataFetchErrorText=Tietojen saanti muistin käyttöä varten epäonnistui
ecnDashboardText=Joustavat laskentasolmut
#XMSG: Message Title when ECN is not in ruuning state
memoryDistributionErrorTitle=Ei-relevantti
#XMSG: Warning Message when ECN is not in ruuning state
memoryDistributionErrorMessage=Muistin hajauttaminen näytetään vain, kun joustava laskentasolmu on Käynnissä-tilassa.
#XMSG: Title for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageTitle=Joustavaa laskentasolmua ei ole valittu
#XMSG: Description for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageDescription=Aloita valitsemalla sellainen otsikon luettelosta.
#XMSG: Tab label for Statememt Logs
statementLogsText=Lauselokit
#XMSG: Tab label for Task Logs
taskLogsText=Tehtävälokit
#MSG: Label for Date Time Quick Filter
labelForTimeQuickFilter=Päivämäärä- ja kellonaikaväli:
labelForSpaceQuickFilter=Tilat
labelForStatusQuickFilter=Tilat
#XTIT: Cancel Task Started toast msg
cancelTaskStarted=Tehtävän peruutus aloitettu
#YMSE: Cancel Task Error
taskCancelError=Tehtävää ei voi peruuttaa, koska sen suoritus on jo päättynyt.
#LSA Monitor Tab Name
lsaMonitorText=Object Store
#ID for LSA Storage By Space Widget
hdlfStorageSpace=SAP HANA -tietojärven tiedostot: tallennustilan käyttö
#ID for LSA Storage for All Spaces Widget
hdlfStorageSpaceTotal=SAP HANA -tietojärven tiedostot: kaikkien tilojen tallennustilan käyttö
#Labels for LSA Monitor
lsaSparkTableApplicationColumnText=Sovellus
lsaSparkTableNoOfTasksColumnText=Tehtävien lukumäärä
sparkApplicationConfigurationTitle=Sovelluskonfiguraatio
sparkExecutorCPU=Suorittajan CPU
sparkExecutorMemory=Suorittajan muisti
sparkDriverCPU=Ajurin CPU
sparkDriverMemory=Ajurin muisti
sparkMaximumCPU=CPU-maksimimäärä
sparkMaximumMemory=Suurin sallittu muisti
sparkMinExecutors=Suorittajien minimimäärä
sparkMaxExecutors=Suorittajien maksimimäärä
sparkAppTableNoDataIMTitle=Tilaa ei valittu
sparkAppTableNoDataIMDesc=Aloita valitsemalla jokin suodatinpalkin luettelosta.
TBUnit=TB
sparkTaskTableNoDataIMTitle=Tehtäviä ei ole käytettävissä
sparkTaskTableNoDataIMDesc=Valitulla sovelluksella ei ole tehtäviä käytettävissä. Valitse toinen sovellus.
taskActivity=Tehtävän toiminto
sparkTableNoSearchResultsTitle=Ei hakutuloksia
sparkTableNoSearchResultsDesc=Muuta hakuperustetta ja yritä uudelleen.
sparkTableNoFilterResultsTitle=Ei suodatustuloksia
sparkTableNoFilterResultsDesc=Muuta suodatusperusteita ja yritä uudelleen.
removeFilterText=Tyhjennä suodatin
sortTableText=Lajittele taulukko
filterTableText=Suodata
apacheSparkTableText=Apache Spark: tehtävät
closeColumnText=Sulje sarake
