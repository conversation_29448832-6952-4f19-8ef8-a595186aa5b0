#XTIT: Dynamic Page title
pageTitle=Selamat Datang ke Pemantau Sistem
#XTIT: Dynamic Page subtitle
pageSubtitle=Pantau kinerja sistem Anda dan identifikasi penyimpanan, tugas, memori yang habis, dan masalah yang lain.
#XTIT: Messagestrip Enable Statement tracing
messagestripEnableStatementTracing=Untuk memantau kesalahan memori yang habis dan informasi pernyataan yang lain, silakan aktifkan pelacakan pernyataan yang kompleks dalam Konfigurasi/Pemantauan.
#XFLD: Dashboard text
dashBoardText=Dasbor
#XFLD: TaskLog text
taskLogText=Log
#XTIT: card title: Storage Distribution
storageDistribution=Penyimpanan Disk yang Digunakan
#XTIT: card title: Memory Distribution
memoryDistribution=Distribusi Memori
#XTIT: card title:Used Disk Capacity
usedStorageCapacity=Disk yang Digunakan oleh Ruang untuk Penyimpanan
#XTIT: card title: Used Memory Capacity
usedMemoryCapacity=Memori yang Digunakan oleh Ruang untuk Penyimpanan
#XTIT: card title:Assigned Disk Capacity
assignedStorageCapacity=Disk yang Ditetapkan ke Ruang untuk Penyimpanan
#XTIT: card title: Assigned Memory Capacity
assignedMemoryCapacity=Memori yang Ditetapkan ke Ruang untuk Penyimpanan
#XTIT: card title: Errors
errors=Tugas Gagal
#XTIT: card title: Configuration
ecnConfiguration=Konfigurasi
#XTIT: card title: ECN Monthly Uptime
upTime=Waktu Aktif Bulanan
#XTIT: card title: ECN Average Memory
ECNAvgMemory=Rata-Rata Memori
#XTIT: card title: ECN Average CPU
ECNAvgCPU=Rata-Rata CPU
#XTIT: card Sub title: ECN Hours Subtitle
ECNHoursSubtitle={0} Jam Terakhir
#XTIT: card Title: ECN Last 5 Start Tasks
ecnTop5StartStop=5 Tugas Terakhir yang Dimulai/Dihentikan dalam ECN
#XTIT: card Title: ECN Last 5 Stop Tasks
ecnTop5Stop=5 Tugas Terakhir yang Dihentikan dalam ECN
#XTIT: card title: Out of Memory Events (all)
OoMEvents=Kesalahan Memori Habis
#XTIT: card title: MDS
OoMMDSQueries=Kesalahan Memori Habis (Permintaan MDS)
#XTIT: card title: Top 5 Memory Consumption (Statements)
memoryConsumptionStatements=5 Pernyataan Teratas berdasarkan Pemakaian Memori selama Pemrosesan
#XTIT: card title: Out of Memory Events (Workload Class) by Space
OoMEventsByWorkloadClass=5 Teratas Kesalahan Habis Memori (Kelas Beban Kerja) berdasarkan Ruang
#XTIT: card title: Run Duration
runDuration=5 Teratas Tugas berdasarkan Durasi Eksekusi
#XTIT: card title: Top 5 Tasks by Memory Consumption
tasksBymemoryConsumption=5 Tugas Teratas berdasarkan Pemakaian Memori selama Pemrosesan
#XTIT: card title: Memory Consumption
memoryConsumption=Pemakaian Memori
#XTIT: card title: Top 5 Memory Consumption (MDS)
memoryConsumptionSAC=5 Permintaan MDS Teratas berdasarkan Pemakaian Memori selama Pemrosesan
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Data Log Audit
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Data Administratif
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Data Lain
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Data dalam Ruang
#XFLD: ResourceCategory Available Memory
ResourceCategoryAvailableMemory=Memori yang Tidak Digunakan
#XFLD: ResourceCategory Memory Used by Consumption
ResourceCategoryMemoryConsumption=Memori Digunakan untuk Pemrosesan
#XFLD: ResourceCategory Memory Used by Replication
ResourceCategoryMemoryReplication=Memori Digunakan untuk Replikasi Data
#XFLD: ResourceCategory unknown
unknown=Tidak Diketahui
#XBUT: Card footer view list button
viewList=Tampilkan Log
#NOTR: placeholder for dev-purposes
test=TEST
#XTIT: card bar label: hours
hours=Jam
#XTIT: card subtitle: now
now=Sekarang
#XTIT: card subtitle: last 24 hours
last24Hours=24 Jam Terakhir
#XTIT: card subtitle: last 48 hours
last48Hours=48 Jam Terakhir
#XTIT: card subtitle: last week
lastWeek=7 Hari Terakhir
#XTIT: card subtitle: last month
lastMonth=Bulan Terakhir
#XFLD: Close
close=Tutup
#XFLD: Today
today=Hari Ini
#XTIT: text for task tab
taskTab=Tugas
#XTIT: text for statement tab
statementTab=Pernyataan
#XTIT: text fo nav link in table cell
viewStatements=Lihat
#XTIT: text to more link in table cell
more=Selengkapnya
#XTIT: text for statement dialog header
statementDlgTitle=Pernyataan
#XBUT: text for close btn on dialog
closeBtn=Tutup
#XBUT: text for copy btn on dialog
copyBtn=Salin
#XTIT: Copy success text on toast msg
copiedToClipboard=Disalin ke Clipboard
#XTIT: test for download btn on dialog
downloadBtn=Unduh

#XTIT: Monitoring table column names
startTime=Waktu Mulai
startDate=Tanggal Mulai
duration=Durasi
objectType=Tipe Objek
activity=Aktivitas
spaceName=Nama Ruang
objectName=Nama Objek
peakMemory=Memori Puncak SAP HANA
peakCPU=Waktu CPU SAP HANA
noOfRecords=Catatan
usedMemory=Memori yang Digunakan SAP HANA
usedDisk=Disk yang Digunakan SAP HANA
status=Status
subStatus=Substatus
user=Pengguna
targetTable=Tabel Target
statements=Pernyataan
outOfMemory=Memori yang Habis
taskLogId=ID Log Tugas
statementDetails=Rincian Pernyataan
parameters=Parameter
workloadClass=Kelas Beban Kerja
errorCode=Kode Kesalahan
errorText=Pesan Kesalahan
dbUser=Pengguna Basis Data
connectionID=ID Koneksi
statementID=ID Pernyataan
elasticComputeNode=Node Komputasi Elastis


#Localization for @sap/dragonet-ui5-table-editor node module
#XTIT: Title for column settings dialog
columnsSettingsDialogTitle=Pengaturan Kolom
#XFLD: Title for Error type
messagesTableType=Tipe
#XFLD: Title for Error Message
messagesTableMessage=Pesan
#XFLD: Title for filter
filteredBy=Difilter berdasarkan:
#XTIT: text for values contained in filter
filterContains=berisi
#XTIT: text for values starting with in filter
filterStartsWith=dimulai dengan
#XTIT: text for values ending with in filter
filterEndsWith=diakhiri dengan
#XTIT: Title for search in data preview toolbar
toolbarSearch=Cari
#XBUT: Button to clear filter
clearFilter=Hapus Filter
#XBUT: Button to cancel the operation
cancel=Batalkan
#XBUT: Button to save the operation
ok=OKE
#XBUT: Button to restore the data
toolbarRestoreButton=Segarkan
#XBUT: Button to cancel running task
cancelBtnText=Batalkan Tugas
#XBUT: tooltip for Button to restore the data
toolbarRestoreButtonTooltip=Perbarui Halaman
#XMSG: Busy dialog for Columns Settings dialog
PROCESSING=Memproses...
#XMSG: Message Confirmation
confirmation=Konfirmasi
#XMSG: Message for refresh successful
refreshSuccess=Berhasil Disegarkan
#XMSG: Message for refresh successful
refreshSuccessful=Disegarkan
#XMSG: Message for restore successful
restoreSuccessful=Berhasil Dipulihkan
#XMSG: Error message when null value found on non-nullable field
mdm-emptyValueError="{0}" tidak boleh kosong. {1}.
#XTIT: Sort Ascending
mdm-sortAscending=Urutkan Naik
#XTIT: Sort Descending
mdm-sortDescending=Urutkan Menurun
#XTIT: Filter
mdm-Filter=Filter
#XBUT: Button Cancel
mdm-cancel=Batalkan
#XBUT: Button Add
mdm-Add=Tambahkan
#XMSG: and inside error message
and=dan
#XMSG: Error message when no column selected
mdm-allColumnUnselected=Pilih satu atau beberapa kolom untuk melanjutkan.

#Localization keys not needed for System Monitor but required for @sap/dragonet-ui5-table-editor node module to remove console errors
#XTXT: Message information strip for filtering and sorting
mdm-InfoStrip=Alat Filter, Urutkan, Hapus, dan Pengaturan Tabel dinonaktifkan oleh perubahan yang belum disimpan. Simpan perubahan Anda untuk mengaktifkannya.
#XBUT: Button for file upload in mdm data preview
toolbarUploadButton=Unggah Data
#XBUT: Button for duplicating rows in mdm data preview
toolbarDuplicateButton=Duplikasikan
#XBUT: Button for deleting rows in mdm data preview
toolbarDeleteButton=Hapus Permanen
#XBUT: Button for adding rows in mdm data preview
toolbarAddButton=Tambahkan

#XTIT: MDM Empty value Handling in Table Editor Label
mdmInsertMissingString=Sisipkan Nilai String yang Hilang sebagai:
#XTIT: MDM Empty value Handling in Table Editor Null Combobox item
mdmNullText=KOSONG
#XTIT: MDM Empty value Handling in Table Editor Empty string Combobox item
mdmEmptyStringText=String kosong
#XTXT: Message information strip for Insert Missing String Value
mdmInfoStripMissingString=Nilai string yang hilang hanya dapat disisipkan di kolom string yang dapat dilihat. Gunakan Pengaturan Kolom untuk menampilkan semua kolom yang relevan.
#XTXT: Message information strip for filtering sorting and Insert Missing String Value
mdm-InfoStripWithMissingString=Alat Filter, Urutkan, Sisipkan Nilai String yang Hilang, dan Pengaturan Tabel dinonaktifkan oleh perubahan yang belum disimpan. Simpan perubahan Anda untuk mengaktifkannya.
#XFLD: Open SAP HANA Cockpit
openHanaCockpit=Gambaran Umum Basis Data (Kokpit SAP HANA)
#XBUT: Open SAP HANA Performance Monitor Cockpit
openHanaCockpitPerfMonitor=Pemantau Kinerja
openHanaCockpitPerfMonitorTooltip=Buka Pemantau Kinerja di Kokpit SAP HANA
#XTIT: card title: Admission Control Rejection Events
acRejectedEvents=Kontrol Penerimaan Peristiwa Penolakan
#XTIT: card title: Admission Control Queuing Events
acQueuedEvents=Kontrol Penerimaan Peristiwa Antrean

#XTIT: card title: Top 5 Admission Control Rejection Events by Space
acRejectionEventsBySpace=5 Kontrol Penerimaan Teratas Peristiwa Penolakan berdasarkan Ruang
#XTIT: card title: Top 5 Admission Control Queuing Events by Space
acQueueingEventsBySpace=5 Kontrol Penerimaan Teratas Peristiwa Antrean berdasarkan Ruang

#XTIT: payload Table and List texts
payloadDownloadText=Muatan Diunduh
dataSourceText=Sumber Data
dataSourcesObject=Nama
dataSourcesSchema=Nama Ruang
payloadListHeader=Muatan
payloadStoryIdText=ID Stori
payloadStoryNameText=Nama Stori
payloadDialogTitleText=Informasi Selengkapnya
payloadTextAreaTitle=Muatan
NO_MDS_DETAIL=Tidak Ada Data yang Tersedia
MDS_DATA_FETCH_FAILED=Kami tidak dapat menampilkan informasi tentang laporan MDS karena adanya kesalahan server.
GBUnit=GB
enabled=Diaktifkan
notEnabled=Dinonaktifkan
#XTIT: ECN Monitoring Dashboard Texts
ecnStatus=Status
ecnBlock=Blok Komputasi
ecnSize=Ukuran
ecnConfigMemory=Memori
ecnConfigStorage=Penyimpanan
ecnConfigSpacesList=Daftar Ruang
#XFLD: ECN phase Ready
ecnReady=Siap
#XFLD: ECN phase Running
ecnRunning=Sedang Dieksekusi
#XFLD: ECN phase Initial
ecnInitial=Belum Siap
#XFLD: ECN phase Starting
ecnStarting=Memulai
#XFLD: ECN phase Stopping
ecnStopping=Menghentikan
#XFLD: ECN phase Unknown
ecnUnknown=Tidak Diketahui
#XFLD: ECN Last Run Details
lastRun=Eksekusi Terbaru
#XFLD: ECN Previous Run Details
previousRun=Eksekusi Sebelumnya
#XFLD: ECN Manage Button
manage=Kelola Node Komputasi Elastis
#XFLD: ECN Run Details Widget
days=Hari
#XFLD: ECN Run Details Widget Title
runDetails=Rincian Eksekusi
#XFLD: ECN Performance Class Label
performanceClass=Kelas Kinerja
start=Mulai
stop=Hentikan
ecnRunDetailsUpTime=Waktu Aktif
ecnConfigurationCPU=Jumlah vCPU
ecnTechnicalName=Nama Teknis
currentMonth=Bulan Ini
previousMonth=Bulan Sebelumnya
ecnBlockHours=Blok Jam
upTimeOverall=Total Waktu Aktif
refresh=Segarkan
top5MemConsumptionDataFetchErrorText=Gagal mendapatkan data untuk pemakaian memori
ecnDashboardText=Node Komputasi Elastis
#XMSG: Message Title when ECN is not in ruuning state
memoryDistributionErrorTitle=Tidak Berlaku
#XMSG: Warning Message when ECN is not in ruuning state
memoryDistributionErrorMessage=Distribusi memori hanya akan ditampilkan saat node komputasi elastis dalam status sedang dieksekusi.
#XMSG: Title for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageTitle=Tidak Ada Node Komputasi Elastis yang Dipilih
#XMSG: Description for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageDescription=Mulailah dengan memilih satu dari daftar pada header.
#XMSG: Tab label for Statememt Logs
statementLogsText=Log Pernyataan
#XMSG: Tab label for Task Logs
taskLogsText=Log Tugas
#MSG: Label for Date Time Quick Filter
labelForTimeQuickFilter=Rentang Tanggal dan Waktu:
labelForSpaceQuickFilter=Ruang
labelForStatusQuickFilter=Status
#XTIT: Cancel Task Started toast msg
cancelTaskStarted=Pembatalan tugas dimulai
#YMSE: Cancel Task Error
taskCancelError=Tugas tidak dapat dibatalkan karena telah selesai dieksekusi.
#LSA Monitor Tab Name
lsaMonitorText=Penyimpanan Objek
#ID for LSA Storage By Space Widget
hdlfStorageSpace=File Data Lake SAP HANA: Pemanfaatan Penyimpanan
#ID for LSA Storage for All Spaces Widget
hdlfStorageSpaceTotal=File Data Lake SAP HANA: Pemanfaatan Penyimpanan untuk Semua Ruang
#Labels for LSA Monitor
lsaSparkTableApplicationColumnText=Aplikasi
lsaSparkTableNoOfTasksColumnText=Jumlah Tugas
sparkApplicationConfigurationTitle=Konfigurasi Aplikasi
sparkExecutorCPU=CPU Pelaksana
sparkExecutorMemory=Memori Pelaksana
sparkDriverCPU=CPU Penentu
sparkDriverMemory=Memori Penentu
sparkMaximumCPU=CPU Maksimum
sparkMaximumMemory=Memori Maksimum
sparkMinExecutors=Jumlah Minimum Pelaksana
sparkMaxExecutors=Jumlah Maksimum Pelaksana
sparkAppTableNoDataIMTitle=Tidak Ada Ruang yang Dipilih
sparkAppTableNoDataIMDesc=Mulailah dengan memilih salah satu dari daftar di bilah filter.
TBUnit=TB
sparkTaskTableNoDataIMTitle=Tidak Ada Tugas yang Tersedia
sparkTaskTableNoDataIMDesc=Tidak ada tugas yang tersedia untuk aplikasi yang dipilih. Silakan pilih aplikasi lain.
taskActivity=Aktivitas Tugas
sparkTableNoSearchResultsTitle=Hasil Pencarian Tidak Ada
sparkTableNoSearchResultsDesc=Ubah istilah pencarian dan coba lagi.
sparkTableNoFilterResultsTitle=Hasil Filter Tidak Ada
sparkTableNoFilterResultsDesc=Ubah kriteria filter dan coba lagi.
removeFilterText=Hapus Filter
sortTableText=Urutkan Tabel
filterTableText=Filter Tabel
apacheSparkTableText=Apache Spark: Tugas
closeColumnText=Tutup Kolom
