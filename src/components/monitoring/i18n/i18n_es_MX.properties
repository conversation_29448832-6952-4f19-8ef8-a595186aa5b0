#XTIT: Dynamic Page title
pageTitle=Le damos la bienvenida a Supervisión del sistema
#XTIT: Dynamic Page subtitle
pageSubtitle=Supervise el rendimiento de su sistema e identifique problemas de almacenamiento, tareas y memoria insuficiente, entre otros problemas.
#XTIT: Messagestrip Enable Statement tracing
messagestripEnableStatementTracing=Para supervisar errores de memoria insuficiente y demás información de instrucciones, habilite el seguimiento de instrucciones costosas en Configuración/Supervisión.
#XFLD: Dashboard text
dashBoardText=Panel
#XFLD: TaskLog text
taskLogText=Registros
#XTIT: card title: Storage Distribution
storageDistribution=Almacenamiento de disco usado
#XTIT: card title: Memory Distribution
memoryDistribution=Distribución de memoria
#XTIT: card title:Used Disk Capacity
usedStorageCapacity=Disco usado por espacios para almacenamiento
#XTIT: card title: Used Memory Capacity
usedMemoryCapacity=Memoria usada por espacios para almacenamiento
#XTIT: card title:Assigned Disk Capacity
assignedStorageCapacity=Disco asignado a espacios para almacenamiento
#XTIT: card title: Assigned Memory Capacity
assignedMemoryCapacity=Memoria asignada a espacios para almacenamiento
#XTIT: card title: Errors
errors=Tareas fallidas
#XTIT: card title: Configuration
ecnConfiguration=Configuración
#XTIT: card title: ECN Monthly Uptime
upTime=Tiempo de actividad mensual
#XTIT: card title: ECN Average Memory
ECNAvgMemory=Memoria promedio
#XTIT: card title: ECN Average CPU
ECNAvgCPU=CPU promedio
#XTIT: card Sub title: ECN Hours Subtitle
ECNHoursSubtitle=Últimas {0} horas
#XTIT: card Title: ECN Last 5 Start Tasks
ecnTop5StartStop=Últimas 5 tareas de inicio/detención de ECN
#XTIT: card Title: ECN Last 5 Stop Tasks
ecnTop5Stop=Últimas 5 tareas de detención de ECN
#XTIT: card title: Out of Memory Events (all)
OoMEvents=Errores de memoria insuficiente
#XTIT: card title: MDS
OoMMDSQueries=Errores de memoria insuficiente (solicitudes de MDS)
#XTIT: card title: Top 5 Memory Consumption (Statements)
memoryConsumptionStatements=Las 5 instrucciones principales por consumo de memoria de procesamiento
#XTIT: card title: Out of Memory Events (Workload Class) by Space
OoMEventsByWorkloadClass=Los 5 errores de memoria insuficiente principales (clase de carga de trabajo) por espacio
#XTIT: card title: Run Duration
runDuration=Las 5 tareas principales por duración de ejecución
#XTIT: card title: Top 5 Tasks by Memory Consumption
tasksBymemoryConsumption=Las 5 tareas principales por consumo de memoria de procesamiento
#XTIT: card title: Memory Consumption
memoryConsumption=Consumo de memoria
#XTIT: card title: Top 5 Memory Consumption (MDS)
memoryConsumptionSAC=Las 5 solicitudes de MDS principales por consumo de memoria de procesamiento
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Datos de registro de auditoría
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Datos administrativos
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Otros datos
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Datos en espacios
#XFLD: ResourceCategory Available Memory
ResourceCategoryAvailableMemory=Memoria no usada
#XFLD: ResourceCategory Memory Used by Consumption
ResourceCategoryMemoryConsumption=Memoria usada para procesamiento
#XFLD: ResourceCategory Memory Used by Replication
ResourceCategoryMemoryReplication=Memoria usada para replicación de datos
#XFLD: ResourceCategory unknown
unknown=Desconocido
#XBUT: Card footer view list button
viewList=Ver registros
#NOTR: placeholder for dev-purposes
test=TEST
#XTIT: card bar label: hours
hours=Horas
#XTIT: card subtitle: now
now=Ahora
#XTIT: card subtitle: last 24 hours
last24Hours=Últimas 24 horas
#XTIT: card subtitle: last 48 hours
last48Hours=Últimas 48 horas
#XTIT: card subtitle: last week
lastWeek=Últimos 7 días
#XTIT: card subtitle: last month
lastMonth=Último mes
#XFLD: Close
close=Cerrar
#XFLD: Today
today=Hoy
#XTIT: text for task tab
taskTab=Tareas
#XTIT: text for statement tab
statementTab=Instrucciones
#XTIT: text fo nav link in table cell
viewStatements=Vista
#XTIT: text to more link in table cell
more=Más
#XTIT: text for statement dialog header
statementDlgTitle=Instrucción
#XBUT: text for close btn on dialog
closeBtn=Cerrar
#XBUT: text for copy btn on dialog
copyBtn=Copiar
#XTIT: Copy success text on toast msg
copiedToClipboard=Copiado al portapapeles
#XTIT: test for download btn on dialog
downloadBtn=Descargar

#XTIT: Monitoring table column names
startTime=Hora de inicio
startDate=Fecha de inicio
duration=Duración
objectType=Tipo de objeto
activity=Actividad
spaceName=Nombre de espacio
objectName=Nombre de objeto
peakMemory=Memoria máxima de SAP HANA
peakCPU=Tiempo de CPU de SAP HANA
noOfRecords=Registros
usedMemory=Memoria usada de SAP HANA
usedDisk=Uso en disco de SAP HANA
status=Estado
subStatus=Subestado
user=Usuario
targetTable=Tabla de destino
statements=Instrucciones
outOfMemory=Memoria insuficiente
taskLogId=ID de registro de tarea
statementDetails=Detalles de instrucción
parameters=Parámetros
workloadClass=Clase de carga de trabajo
errorCode=Código de error
errorText=Mensaje de error
dbUser=Usuario de base de datos
connectionID=ID de conexión
statementID=ID de instrucción
elasticComputeNode=Nodo de procesamiento elástico


#Localization for @sap/dragonet-ui5-table-editor node module
#XTIT: Title for column settings dialog
columnsSettingsDialogTitle=Configuración de columnas
#XFLD: Title for Error type
messagesTableType=Tipo
#XFLD: Title for Error Message
messagesTableMessage=Mensaje
#XFLD: Title for filter
filteredBy=Filtrado por:
#XTIT: text for values contained in filter
filterContains=contiene
#XTIT: text for values starting with in filter
filterStartsWith=empieza con
#XTIT: text for values ending with in filter
filterEndsWith=termina con
#XTIT: Title for search in data preview toolbar
toolbarSearch=Buscar
#XBUT: Button to clear filter
clearFilter=Borrar filtro
#XBUT: Button to cancel the operation
cancel=Cancelar
#XBUT: Button to save the operation
ok=OK
#XBUT: Button to restore the data
toolbarRestoreButton=Actualizar
#XBUT: Button to cancel running task
cancelBtnText=Cancelar tarea
#XBUT: tooltip for Button to restore the data
toolbarRestoreButtonTooltip=Actualizar la página
#XMSG: Busy dialog for Columns Settings dialog
PROCESSING=Procesando…
#XMSG: Message Confirmation
confirmation=Confirmación
#XMSG: Message for refresh successful
refreshSuccess=Actualización correcta
#XMSG: Message for refresh successful
refreshSuccessful=Actualizado
#XMSG: Message for restore successful
restoreSuccessful=Restauración correcta
#XMSG: Error message when null value found on non-nullable field
mdm-emptyValueError="{0}" no puede estar vacío. {1}.
#XTIT: Sort Ascending
mdm-sortAscending=Orden ascendente
#XTIT: Sort Descending
mdm-sortDescending=Orden descendente
#XTIT: Filter
mdm-Filter=Filtro
#XBUT: Button Cancel
mdm-cancel=Cancelar
#XBUT: Button Add
mdm-Add=Agregar
#XMSG: and inside error message
and=y
#XMSG: Error message when no column selected
mdm-allColumnUnselected=Seleccione una o más columnas para continuar.

#Localization keys not needed for System Monitor but required for @sap/dragonet-ui5-table-editor node module to remove console errors
#XTXT: Message information strip for filtering and sorting
mdm-InfoStrip=Las herramientas Filtrar, Ordenar, Eliminar y Configuración de tabla están deshabilitadas porque hay cambios sin guardar. Guarde los cambios para habilitarlas.
#XBUT: Button for file upload in mdm data preview
toolbarUploadButton=Cargar datos
#XBUT: Button for duplicating rows in mdm data preview
toolbarDuplicateButton=Duplicar
#XBUT: Button for deleting rows in mdm data preview
toolbarDeleteButton=Eliminar
#XBUT: Button for adding rows in mdm data preview
toolbarAddButton=Agregar

#XTIT: MDM Empty value Handling in Table Editor Label
mdmInsertMissingString=Insertar valor de string faltante como:
#XTIT: MDM Empty value Handling in Table Editor Null Combobox item
mdmNullText=NULO
#XTIT: MDM Empty value Handling in Table Editor Empty string Combobox item
mdmEmptyStringText=String vacía
#XTXT: Message information strip for Insert Missing String Value
mdmInfoStripMissingString=Los valores de string faltantes solo se pueden insertar en columnas de strings visibles en filas nuevas o editadas. Use Configuración de columnas para mostrar todas las columnas relevantes.
#XTXT: Message information strip for filtering sorting and Insert Missing String Value
mdm-InfoStripWithMissingString=Las herramientas Filtrar, Ordenar, Insertar valor de string faltante y Configuración de tabla están deshabilitadas porque hay cambios sin guardar. Guarde los cambios para habilitarlas.
#XFLD: Open SAP HANA Cockpit
openHanaCockpit=Descripción general de la base de datos (cockpit de SAP HANA)
#XBUT: Open SAP HANA Performance Monitor Cockpit
openHanaCockpitPerfMonitor=Supervisor de rendimiento
openHanaCockpitPerfMonitorTooltip=Abrir el supervisor de rendimiento en el cockpit de SAP HANA
#XTIT: card title: Admission Control Rejection Events
acRejectedEvents=Eventos de rechazo del control de admisión
#XTIT: card title: Admission Control Queuing Events
acQueuedEvents=Eventos de cola del control de admisión

#XTIT: card title: Top 5 Admission Control Rejection Events by Space
acRejectionEventsBySpace=Principales 5 eventos de rechazo del control de admisión por espacio
#XTIT: card title: Top 5 Admission Control Queuing Events by Space
acQueueingEventsBySpace=Principales 5 eventos de cola del control de admisión por espacio

#XTIT: payload Table and List texts
payloadDownloadText=Carga descargada
dataSourceText=Orígenes de datos
dataSourcesObject=Nombre
dataSourcesSchema=Nombre de espacio
payloadListHeader=Cargas
payloadStoryIdText=ID de historia
payloadStoryNameText=Nombre de historia
payloadDialogTitleText=Más información
payloadTextAreaTitle=Carga
NO_MDS_DETAIL=No hay datos disponibles
MDS_DATA_FETCH_FAILED=No pudimos mostrar información sobre instrucciones MDS debido a un error del servidor.
GBUnit=GB
enabled=Activado
notEnabled=No activado
#XTIT: ECN Monitoring Dashboard Texts
ecnStatus=Estado
ecnBlock=Bloques de procesamiento
ecnSize=Tamaño
ecnConfigMemory=Memoria
ecnConfigStorage=Almacenamiento
ecnConfigSpacesList=Lista de espacios
#XFLD: ECN phase Ready
ecnReady=Listo
#XFLD: ECN phase Running
ecnRunning=En ejecución
#XFLD: ECN phase Initial
ecnInitial=No listo
#XFLD: ECN phase Starting
ecnStarting=Iniciando
#XFLD: ECN phase Stopping
ecnStopping=Detención en curso
#XFLD: ECN phase Unknown
ecnUnknown=Desconocido
#XFLD: ECN Last Run Details
lastRun=Última ejecución
#XFLD: ECN Previous Run Details
previousRun=Ejecución anterior
#XFLD: ECN Manage Button
manage=Gestionar nodo de procesamiento elástico
#XFLD: ECN Run Details Widget
days=Días
#XFLD: ECN Run Details Widget Title
runDetails=Detalles de ejecución
#XFLD: ECN Performance Class Label
performanceClass=Clase de rendimiento
start=Iniciar
stop=Detener
ecnRunDetailsUpTime=Tiempo de actividad
ecnConfigurationCPU=Número de vCPU
ecnTechnicalName=Nombre técnico
currentMonth=Mes actual
previousMonth=Mes anterior
ecnBlockHours=Horas de bloque
upTimeOverall=Tiempo de actividad total
refresh=Actualizar
top5MemConsumptionDataFetchErrorText=No se pudieron obtener los datos de consumo de memoria
ecnDashboardText=Nodos de procesamiento elástico
#XMSG: Message Title when ECN is not in ruuning state
memoryDistributionErrorTitle=No aplicable
#XMSG: Warning Message when ECN is not in ruuning state
memoryDistributionErrorMessage=La distribución de memoria solo se muestra cuando el nodo de procesamiento elástico está en estado de ejecución.
#XMSG: Title for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageTitle=Ningún nodo de procesamiento elástico seleccionado
#XMSG: Description for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageDescription=Comience por seleccionar uno de la lista en el encabezado.
#XMSG: Tab label for Statememt Logs
statementLogsText=Registros de instrucciones
#XMSG: Tab label for Task Logs
taskLogsText=Registros de tareas
#MSG: Label for Date Time Quick Filter
labelForTimeQuickFilter=Rango de fecha y hora:
labelForSpaceQuickFilter=Espacios
labelForStatusQuickFilter=Estados
#XTIT: Cancel Task Started toast msg
cancelTaskStarted=Se inició la cancelación de la tarea
#YMSE: Cancel Task Error
taskCancelError=La tarea no se puede cancelar porque ya finalizó la ejecución.
#LSA Monitor Tab Name
lsaMonitorText=Almacén de objetos
#ID for LSA Storage By Space Widget
hdlfStorageSpace=Archivos de lago de datos de SAP HANA: Utilización de almacenamiento
#ID for LSA Storage for All Spaces Widget
hdlfStorageSpaceTotal=Archivos de lago de datos de SAP HANA: Utilización de almacenamiento de todos los espacios
#Labels for LSA Monitor
lsaSparkTableApplicationColumnText=Aplicación
lsaSparkTableNoOfTasksColumnText=Número de tareas
sparkApplicationConfigurationTitle=Configuración de aplicación
sparkExecutorCPU=CPU de ejecutor
sparkExecutorMemory=Memoria de ejecutor
sparkDriverCPU=CPU de controlador
sparkDriverMemory=Memoria de controlador
sparkMaximumCPU=CPU máxima
sparkMaximumMemory=Memoria máxima
sparkMinExecutors=Ejecutores mínimos
sparkMaxExecutors=Ejecutores máximos
sparkAppTableNoDataIMTitle=No hay espacios seleccionados
sparkAppTableNoDataIMDesc=Comience por seleccionar uno de la lista en la barra de filtro.
TBUnit=TB
sparkTaskTableNoDataIMTitle=No hay tareas disponibles
sparkTaskTableNoDataIMDesc=Para la aplicación seleccionada, no hay tareas disponibles. Seleccione otra aplicación.
taskActivity=Actividad de tarea
sparkTableNoSearchResultsTitle=No hay resultados de búsqueda
sparkTableNoSearchResultsDesc=Cambie el término de búsqueda y vuelva a intentarlo.
sparkTableNoFilterResultsTitle=No hay resultados de filtro
sparkTableNoFilterResultsDesc=Cambie los criterios de filtro y vuelva a intentarlo.
removeFilterText=Borrar filtro
sortTableText=Ordenar tabla
filterTableText=Filtro
apacheSparkTableText=Apache Spark: Tareas
closeColumnText=Cerrar columna
