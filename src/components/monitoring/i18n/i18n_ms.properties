#XTIT: Dynamic Page title
pageTitle=Selamat Datang ke Pemantau Sistem
#XTIT: Dynamic Page subtitle
pageSubtitle=Pantau prestasi sistem anda dan kenal pasti storan, tugas, ingatan penuh dan masalah lain.
#XTIT: Messagestrip Enable Statement tracing
messagestripEnableStatementTracing=Untuk memantau ralat ingatan penuh dan maklumat penyata lain, sila dayakan penjejakan penyata muatan tinggi dalam Konfigurasi/Pemantauan.
#XFLD: Dashboard text
dashBoardText=Dashboard
#XFLD: TaskLog text
taskLogText=Log
#XTIT: card title: Storage Distribution
storageDistribution=Storan Cakera Digunakan
#XTIT: card title: Memory Distribution
memoryDistribution=Pengagihan Ingatan
#XTIT: card title:Used Disk Capacity
usedStorageCapacity=Cakera Digunakan oleh Ruang untuk Storan
#XTIT: card title: Used Memory Capacity
usedMemoryCapacity=Ingatan Digunakan oleh Ruang untuk Storan
#XTIT: card title:Assigned Disk Capacity
assignedStorageCapacity=Cakera Diumpukkan kepada Ruang untuk Storan
#XTIT: card title: Assigned Memory Capacity
assignedMemoryCapacity=Ingatan Diumpukkan kepada Ruang untuk Storan
#XTIT: card title: Errors
errors=Tugas Tidak Berjaya
#XTIT: card title: Configuration
ecnConfiguration=Konfigurasi
#XTIT: card title: ECN Monthly Uptime
upTime=Masa Hidup Bulanan
#XTIT: card title: ECN Average Memory
ECNAvgMemory=Purata Ingatan
#XTIT: card title: ECN Average CPU
ECNAvgCPU=Purata CPU
#XTIT: card Sub title: ECN Hours Subtitle
ECNHoursSubtitle={0} Jam Terakhir
#XTIT: card Title: ECN Last 5 Start Tasks
ecnTop5StartStop=5 Tugas Mula/Henti Terakhir ECN
#XTIT: card Title: ECN Last 5 Stop Tasks
ecnTop5Stop=5 Tugas Henti Terakhir ECN
#XTIT: card title: Out of Memory Events (all)
OoMEvents=Ralat Ingatan Penuh
#XTIT: card title: MDS
OoMMDSQueries=Ralat Ingatan Penuh (Permintaan MDS)
#XTIT: card title: Top 5 Memory Consumption (Statements)
memoryConsumptionStatements=Penyata 5 Teratas mengikut Pemprosesan Penggunaan Memori
#XTIT: card title: Out of Memory Events (Workload Class) by Space
OoMEventsByWorkloadClass=Ralat Ingatan Penuh 5 Teratas (Kelas Muatan Kerja) mengikut Ruang
#XTIT: card title: Run Duration
runDuration=Tugas 5 Teratas mengikut Jangka Masa Jalanan
#XTIT: card title: Top 5 Tasks by Memory Consumption
tasksBymemoryConsumption=Tugas 5 Teratas mengikut Pemprosesan Penggunaan Memori
#XTIT: card title: Memory Consumption
memoryConsumption=Penggunaan Memori
#XTIT: card title: Top 5 Memory Consumption (MDS)
memoryConsumptionSAC=Permintaan MDS 5 Teratas mengikut Pemprosesan Penggunaan Memori
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Data Log Audit
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Data Pentadbiran
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Data Lain
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Data dalam Ruang
#XFLD: ResourceCategory Available Memory
ResourceCategoryAvailableMemory=Ingatan Tidak Digunakan
#XFLD: ResourceCategory Memory Used by Consumption
ResourceCategoryMemoryConsumption=Ingatan Digunakan untuk Pemprosesan
#XFLD: ResourceCategory Memory Used by Replication
ResourceCategoryMemoryReplication=Ingatan Digunakan untuk Replikasi Data
#XFLD: ResourceCategory unknown
unknown=Tidak diketahui
#XBUT: Card footer view list button
viewList=Paparkan Log
#NOTR: placeholder for dev-purposes
test=TEST
#XTIT: card bar label: hours
hours=Jam
#XTIT: card subtitle: now
now=Sekarang
#XTIT: card subtitle: last 24 hours
last24Hours=24 Jam Terakhir
#XTIT: card subtitle: last 48 hours
last48Hours=48 Jam Terakhir
#XTIT: card subtitle: last week
lastWeek=7 Hari Lalu
#XTIT: card subtitle: last month
lastMonth=Bulan Lepas
#XFLD: Close
close=Tutup
#XFLD: Today
today=Hari Ini
#XTIT: text for task tab
taskTab=Tugas
#XTIT: text for statement tab
statementTab=Penyata
#XTIT: text fo nav link in table cell
viewStatements=Papar
#XTIT: text to more link in table cell
more=Selanjutnya
#XTIT: text for statement dialog header
statementDlgTitle=Penyata
#XBUT: text for close btn on dialog
closeBtn=Tutup
#XBUT: text for copy btn on dialog
copyBtn=Salin
#XTIT: Copy success text on toast msg
copiedToClipboard=Disalin ke Papan Klip
#XTIT: test for download btn on dialog
downloadBtn=Muat Turun

#XTIT: Monitoring table column names
startTime=Masa Mula
startDate=Tarikh Mula
duration=Jangka masa
objectType=Jenis Objek
activity=Aktiviti
spaceName=Nama Ruang
objectName=Nama Objek
peakMemory=Ingatan Puncak SAP HANA
peakCPU=Masa CPU SAP HANA
noOfRecords=Rekod
usedMemory=Penggunaan Ingatan SAP HANA
usedDisk=Penggunaan Cakera SAP HANA
status=Status
subStatus=Substatus
user=Pengguna
targetTable=Jadual Sasaran
statements=Penyata
outOfMemory=Ingatan Penuh
taskLogId=ID Log Tugas
statementDetails=Butiran Penyata
parameters=Parameter
workloadClass=Kelas Muatan Kerja
errorCode=Kod Ralat
errorText=Mesej Ralat
dbUser=Pengguna Pangkalan Data
connectionID=ID Sambungan
statementID=ID Kenyataan
elasticComputeNode=Nod Kira Elastik


#Localization for @sap/dragonet-ui5-table-editor node module
#XTIT: Title for column settings dialog
columnsSettingsDialogTitle=Tetapan Lajur
#XFLD: Title for Error type
messagesTableType=Jenis
#XFLD: Title for Error Message
messagesTableMessage=Mesej
#XFLD: Title for filter
filteredBy=Ditapis oleh:
#XTIT: text for values contained in filter
filterContains=mengandungi
#XTIT: text for values starting with in filter
filterStartsWith=bermula dengan
#XTIT: text for values ending with in filter
filterEndsWith=berakhir dengan
#XTIT: Title for search in data preview toolbar
toolbarSearch=Cari
#XBUT: Button to clear filter
clearFilter=Kosongkan Penapis
#XBUT: Button to cancel the operation
cancel=Batalkan
#XBUT: Button to save the operation
ok=OK
#XBUT: Button to restore the data
toolbarRestoreButton=Segar Semula
#XBUT: Button to cancel running task
cancelBtnText=Batalkan Tugas
#XBUT: tooltip for Button to restore the data
toolbarRestoreButtonTooltip=Kemas Kini Halaman
#XMSG: Busy dialog for Columns Settings dialog
PROCESSING=Memproses...
#XMSG: Message Confirmation
confirmation=Pengesahan
#XMSG: Message for refresh successful
refreshSuccess=Berjaya Disegar Semula
#XMSG: Message for refresh successful
refreshSuccessful=Disegar Semula
#XMSG: Message for restore successful
restoreSuccessful=Berjaya Dipulihkan
#XMSG: Error message when null value found on non-nullable field
mdm-emptyValueError="{0}" tidak boleh kosong. {1}.
#XTIT: Sort Ascending
mdm-sortAscending=Isih Menaik
#XTIT: Sort Descending
mdm-sortDescending=Isih Menurun
#XTIT: Filter
mdm-Filter=Tapis
#XBUT: Button Cancel
mdm-cancel=Batalkan
#XBUT: Button Add
mdm-Add=Tambah
#XMSG: and inside error message
and=dan
#XMSG: Error message when no column selected
mdm-allColumnUnselected=Pilih satu atau lebih lajur untuk teruskan.

#Localization keys not needed for System Monitor but required for @sap/dragonet-ui5-table-editor node module to remove console errors
#XTXT: Message information strip for filtering and sorting
mdm-InfoStrip=Alat Tetapan Jadual, Tapis, Isih dan Padam dinyahdayakan mengikut perubahan tidak disimpan. Simpan perubahan anda untuk mendayakannya.
#XBUT: Button for file upload in mdm data preview
toolbarUploadButton=Muat Naik Data
#XBUT: Button for duplicating rows in mdm data preview
toolbarDuplicateButton=Pendua
#XBUT: Button for deleting rows in mdm data preview
toolbarDeleteButton=Padam
#XBUT: Button for adding rows in mdm data preview
toolbarAddButton=Tambah

#XTIT: MDM Empty value Handling in Table Editor Label
mdmInsertMissingString=Masukkan Nilai Rentetan yang Tiada sebagai:
#XTIT: MDM Empty value Handling in Table Editor Null Combobox item
mdmNullText=SIFAR
#XTIT: MDM Empty value Handling in Table Editor Empty string Combobox item
mdmEmptyStringText=Rentetan kosong
#XTXT: Message information strip for Insert Missing String Value
mdmInfoStripMissingString=Nilai rentetan yang tiada dimasukkan hanya dalam lajur rentetan yang boleh dilihat dalam baris baharu dan diedit. Gunakan Tetapan Lajur untuk memaparkan semua lajur yang berkaitan.
#XTXT: Message information strip for filtering sorting and Insert Missing String Value
mdm-InfoStripWithMissingString=Alat Tetapan Jadual, Tapis, Isih dan Masukkan Nilai Rentetan Hilang dinyahdayakan mengikut perubahan tidak disimpan. Simpan perubahan anda untuk mendayakannya.
#XFLD: Open SAP HANA Cockpit
openHanaCockpit=Paparan Keseluruhan Pangkalan Data (Kokpit SAP HANA)
#XBUT: Open SAP HANA Performance Monitor Cockpit
openHanaCockpitPerfMonitor=Pemantau Prestasi
openHanaCockpitPerfMonitorTooltip=Buka Pemantau Prestasi dalam Kokpit SAP HANA
#XTIT: card title: Admission Control Rejection Events
acRejectedEvents=Acara Penolakan Kawalan Kemasukan
#XTIT: card title: Admission Control Queuing Events
acQueuedEvents=Acara Penggiliran Kawalan Kemasukan

#XTIT: card title: Top 5 Admission Control Rejection Events by Space
acRejectionEventsBySpace=5 Acara Penolakan Kawalan Kemasukan Teratas mengikut Ruang
#XTIT: card title: Top 5 Admission Control Queuing Events by Space
acQueueingEventsBySpace=5 Acara Penggiliran Kawalan Kemasukan Teratas mengikut Ruang

#XTIT: payload Table and List texts
payloadDownloadText=Muatan Dimuat Turun
dataSourceText=Sumber Data
dataSourcesObject=Nama
dataSourcesSchema=Nama Ruang
payloadListHeader=Muatan
payloadStoryIdText=ID Cerita
payloadStoryNameText=Nama Cerita
payloadDialogTitleText=Maklumat Lanjut
payloadTextAreaTitle=Muatan
NO_MDS_DETAIL=Tiada Data Tersedia
MDS_DATA_FETCH_FAILED=Tiada paparan maklumat tentang penyata MDS kerana ralat pelayan.
GBUnit=GB
enabled=Didayakan
notEnabled=Tidak Didayakan
#XTIT: ECN Monitoring Dashboard Texts
ecnStatus=Status
ecnBlock=Sekatan Pengiraan
ecnSize=Saiz
ecnConfigMemory=Ingatan
ecnConfigStorage=Storan
ecnConfigSpacesList=Senarai Ruang
#XFLD: ECN phase Ready
ecnReady=Sedia
#XFLD: ECN phase Running
ecnRunning=Sedang Berjalan
#XFLD: ECN phase Initial
ecnInitial=Tidak Tersedia
#XFLD: ECN phase Starting
ecnStarting=Bermula
#XFLD: ECN phase Stopping
ecnStopping=Pemberhentian
#XFLD: ECN phase Unknown
ecnUnknown=Tidak diketahui
#XFLD: ECN Last Run Details
lastRun=Jalanan Terkini
#XFLD: ECN Previous Run Details
previousRun=Jalanan Sebelumnya
#XFLD: ECN Manage Button
manage=Uruskan Nod Kira Elastik
#XFLD: ECN Run Details Widget
days=Hari
#XFLD: ECN Run Details Widget Title
runDetails=Butiran Jalanan
#XFLD: ECN Performance Class Label
performanceClass=Kelas Prestasi
start=Mula
stop=Henti
ecnRunDetailsUpTime=Masa Hidup
ecnConfigurationCPU=Bilangan vCPU
ecnTechnicalName=Nama Teknikal
currentMonth=Bulan Semasa
previousMonth=Bulan Sebelumnya
ecnBlockHours=Jam Sekatan
upTimeOverall=Jumlah Masa Hidup
refresh=Segar Semula
top5MemConsumptionDataFetchErrorText=Cuba mendapatkan semula data untuk penggunaan ingatan
ecnDashboardText=Nod Kira Elastik
#XMSG: Message Title when ECN is not in ruuning state
memoryDistributionErrorTitle=Tidak Berkaitan
#XMSG: Warning Message when ECN is not in ruuning state
memoryDistributionErrorMessage=Pengagihan ingatan dipaparkan hanya apabila nod pengiraan anjal berada dalam status sedang berjalan.
#XMSG: Title for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageTitle=Tiada Nod Kira Elastik Dipilih
#XMSG: Description for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageDescription=Mulakan dengan memilih satu daripada senarai dalam pengepala.
#XMSG: Tab label for Statememt Logs
statementLogsText=Log Penyata
#XMSG: Tab label for Task Logs
taskLogsText=Log Tugas
#MSG: Label for Date Time Quick Filter
labelForTimeQuickFilter=Julat Tarikh dan Masa:
labelForSpaceQuickFilter=Ruang
labelForStatusQuickFilter=Status
#XTIT: Cancel Task Started toast msg
cancelTaskStarted=Pembatalan tugas bermula
#YMSE: Cancel Task Error
taskCancelError=Tugas tidak boleh dibatalkan kerana ia telah siap berjalan.
#LSA Monitor Tab Name
lsaMonitorText=Simpanan Objek
#ID for LSA Storage By Space Widget
hdlfStorageSpace=Fail Data Lake SAP HANA: Penggunaan Storan
#ID for LSA Storage for All Spaces Widget
hdlfStorageSpaceTotal=Fail Data Lake SAP HANA: Penggunaan Storan Semua Ruang
#Labels for LSA Monitor
lsaSparkTableApplicationColumnText=Aplikasi
lsaSparkTableNoOfTasksColumnText=Bilangan Tugas
sparkApplicationConfigurationTitle=Konfigurasi Aplikasi
sparkExecutorCPU=CPU Pelaksana
sparkExecutorMemory=Ingatan Pelaksana
sparkDriverCPU=CPU Pemacu
sparkDriverMemory=Ingatan Pemacu
sparkMaximumCPU=CPU Maksimum
sparkMaximumMemory=Ingatan Maksimum
sparkMinExecutors=Pelaksana Minimum
sparkMaxExecutors=Pelaksana Maksimum
sparkAppTableNoDataIMTitle=Tiada Ruang Dipilih
sparkAppTableNoDataIMDesc=Mulakan dengan memilih satu daripada senarai dalam bar penapis.
TBUnit=TB
sparkTaskTableNoDataIMTitle=Tiada Tugas Tersedia
sparkTaskTableNoDataIMDesc=Untuk aplikasi yang dipilih, tiada tugas tersedia. Pilih aplikasi yang lain.
taskActivity=Aktiviti Tugas
sparkTableNoSearchResultsTitle=Tiada Hasil Carian
sparkTableNoSearchResultsDesc=Ubah istilah carian dan cuba lagi.
sparkTableNoFilterResultsTitle=Tiada Hasil Penapis
sparkTableNoFilterResultsDesc=Ubah kriteria penapis dan cuba lagi.
removeFilterText=Kosongkan Penapis
sortTableText=Isih Jadual
filterTableText=Tapis
apacheSparkTableText=Apache Spark: Tugas
closeColumnText=Tutup Lajur
