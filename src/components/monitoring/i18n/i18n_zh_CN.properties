#XTIT: Dynamic Page title
pageTitle=欢迎使用系统监控器
#XTIT: Dynamic Page subtitle
pageSubtitle=监控系统性能，并识别存储、任务、内存不足和其他问题。
#XTIT: Messagestrip Enable Statement tracing
messagestripEnableStatementTracing=要监控内存不足错误及其他语句信息，请在 "配置" / "监控" 中启用资源消耗型语句的跟踪。
#XFLD: Dashboard text
dashBoardText=仪表盘
#XFLD: TaskLog text
taskLogText=日志
#XTIT: card title: Storage Distribution
storageDistribution=已用磁盘存储
#XTIT: card title: Memory Distribution
memoryDistribution=内存分配
#XTIT: card title:Used Disk Capacity
usedStorageCapacity=空间已用磁盘存储
#XTIT: card title: Used Memory Capacity
usedMemoryCapacity=空间已用内存存储
#XTIT: card title:Assigned Disk Capacity
assignedStorageCapacity=向空间分配的磁盘存储
#XTIT: card title: Assigned Memory Capacity
assignedMemoryCapacity=向空间分配的内存存储
#XTIT: card title: Errors
errors=失败的任务
#XTIT: card title: Configuration
ecnConfiguration=配置
#XTIT: card title: ECN Monthly Uptime
upTime=月度正常运行时间
#XTIT: card title: ECN Average Memory
ECNAvgMemory=平均内存
#XTIT: card title: ECN Average CPU
ECNAvgCPU=平均 CPU
#XTIT: card Sub title: ECN Hours Subtitle
ECNHoursSubtitle=最近 {0} 小时
#XTIT: card Title: ECN Last 5 Start Tasks
ecnTop5StartStop=弹性计算节点最近 5 个启动/停止任务
#XTIT: card Title: ECN Last 5 Stop Tasks
ecnTop5Stop=弹性计算节点最近 5 个停止任务
#XTIT: card title: Out of Memory Events (all)
OoMEvents=内存不足错误
#XTIT: card title: MDS
OoMMDSQueries=内存不足错误（多维服务请求）
#XTIT: card title: Top 5 Memory Consumption (Statements)
memoryConsumptionStatements=处理内存使用量最高的 5 个语句
#XTIT: card title: Out of Memory Events (Workload Class) by Space
OoMEventsByWorkloadClass=各空间的前 5 个内存不足错误（工作负载类）
#XTIT: card title: Run Duration
runDuration=运行持续时间最长的 5 个任务
#XTIT: card title: Top 5 Tasks by Memory Consumption
tasksBymemoryConsumption=处理内存使用量最高的 5 个任务
#XTIT: card title: Memory Consumption
memoryConsumption=内存使用量
#XTIT: card title: Top 5 Memory Consumption (MDS)
memoryConsumptionSAC=处理内存使用量最高的 5 个多维服务请求
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=审计日志数据
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=管理数据
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=其他数据
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=空间中的数据
#XFLD: ResourceCategory Available Memory
ResourceCategoryAvailableMemory=未用内存
#XFLD: ResourceCategory Memory Used by Consumption
ResourceCategoryMemoryConsumption=用于处理的内存
#XFLD: ResourceCategory Memory Used by Replication
ResourceCategoryMemoryReplication=用于数据复制的内存
#XFLD: ResourceCategory unknown
unknown=未知
#XBUT: Card footer view list button
viewList=查看日志
#NOTR: placeholder for dev-purposes
test=TEST
#XTIT: card bar label: hours
hours=小时
#XTIT: card subtitle: now
now=现在
#XTIT: card subtitle: last 24 hours
last24Hours=过去 24 小时
#XTIT: card subtitle: last 48 hours
last48Hours=过去 48 小时
#XTIT: card subtitle: last week
lastWeek=过去 7 天
#XTIT: card subtitle: last month
lastMonth=上个月
#XFLD: Close
close=关闭
#XFLD: Today
today=今天
#XTIT: text for task tab
taskTab=任务
#XTIT: text for statement tab
statementTab=语句
#XTIT: text fo nav link in table cell
viewStatements=视图
#XTIT: text to more link in table cell
more=更多
#XTIT: text for statement dialog header
statementDlgTitle=语句
#XBUT: text for close btn on dialog
closeBtn=关闭
#XBUT: text for copy btn on dialog
copyBtn=复制
#XTIT: Copy success text on toast msg
copiedToClipboard=已复制到剪贴板
#XTIT: test for download btn on dialog
downloadBtn=下载

#XTIT: Monitoring table column names
startTime=开始时间
startDate=开始日期
duration=持续时间
objectType=对象类型
activity=活动
spaceName=空间名称
objectName=对象名称
peakMemory=SAP HANA 峰值内存
peakCPU=SAP HANA CPU 使用时长
noOfRecords=记录
usedMemory=SAP HANA 已用内存
usedDisk=SAP HANA 已用磁盘
status=状态
subStatus=子状态
user=用户
targetTable=目标表
statements=语句
outOfMemory=内存不足
taskLogId=任务日志 ID
statementDetails=语句详细信息
parameters=参数
workloadClass=工作负载类
errorCode=错误代码
errorText=错误消息
dbUser=数据库用户
connectionID=连接 ID
statementID=语句 ID
elasticComputeNode=弹性计算节点


#Localization for @sap/dragonet-ui5-table-editor node module
#XTIT: Title for column settings dialog
columnsSettingsDialogTitle=列设置
#XFLD: Title for Error type
messagesTableType=类型
#XFLD: Title for Error Message
messagesTableMessage=消息
#XFLD: Title for filter
filteredBy=筛选条件：
#XTIT: text for values contained in filter
filterContains=包含
#XTIT: text for values starting with in filter
filterStartsWith=开头为
#XTIT: text for values ending with in filter
filterEndsWith=结尾为
#XTIT: Title for search in data preview toolbar
toolbarSearch=搜索
#XBUT: Button to clear filter
clearFilter=清除筛选器
#XBUT: Button to cancel the operation
cancel=取消
#XBUT: Button to save the operation
ok=确定
#XBUT: Button to restore the data
toolbarRestoreButton=刷新
#XBUT: Button to cancel running task
cancelBtnText=取消任务
#XBUT: tooltip for Button to restore the data
toolbarRestoreButtonTooltip=更新页面
#XMSG: Busy dialog for Columns Settings dialog
PROCESSING=正在处理……
#XMSG: Message Confirmation
confirmation=确认
#XMSG: Message for refresh successful
refreshSuccess=已成功刷新
#XMSG: Message for refresh successful
refreshSuccessful=已刷新
#XMSG: Message for restore successful
restoreSuccessful=已成功还原
#XMSG: Error message when null value found on non-nullable field
mdm-emptyValueError="{0}" 不能为空。{1}。
#XTIT: Sort Ascending
mdm-sortAscending=升序排序
#XTIT: Sort Descending
mdm-sortDescending=降序排序
#XTIT: Filter
mdm-Filter=筛选
#XBUT: Button Cancel
mdm-cancel=取消
#XBUT: Button Add
mdm-Add=添加
#XMSG: and inside error message
and=与
#XMSG: Error message when no column selected
mdm-allColumnUnselected=请选择一列或多列以继续。

#Localization keys not needed for System Monitor but required for @sap/dragonet-ui5-table-editor node module to remove console errors
#XTXT: Message information strip for filtering and sorting
mdm-InfoStrip=因为存在没有保存的更改，筛选、排序、删除和表设置工具已禁用。请保存更改，以启用这些工具。
#XBUT: Button for file upload in mdm data preview
toolbarUploadButton=上载数据
#XBUT: Button for duplicating rows in mdm data preview
toolbarDuplicateButton=复制
#XBUT: Button for deleting rows in mdm data preview
toolbarDeleteButton=删除
#XBUT: Button for adding rows in mdm data preview
toolbarAddButton=添加

#XTIT: MDM Empty value Handling in Table Editor Label
mdmInsertMissingString=插入缺少的字符串值作为：
#XTIT: MDM Empty value Handling in Table Editor Null Combobox item
mdmNullText=NULL
#XTIT: MDM Empty value Handling in Table Editor Empty string Combobox item
mdmEmptyStringText=空字符串
#XTXT: Message information strip for Insert Missing String Value
mdmInfoStripMissingString=缺少的字符串值只可以插入新行或已编辑行中的可见字符串列。使用 "列设置" 显示所有相关列。
#XTXT: Message information strip for filtering sorting and Insert Missing String Value
mdm-InfoStripWithMissingString=因为存在没有保存的更改，筛选、排序、"插入缺少的字符串值" 和表设置工具已禁用。请保存更改，以启用这些工具。
#XFLD: Open SAP HANA Cockpit
openHanaCockpit=数据库概览（SAP HANA 主控室）
#XBUT: Open SAP HANA Performance Monitor Cockpit
openHanaCockpitPerfMonitor=性能监控器
openHanaCockpitPerfMonitorTooltip=在 SAP HANA 主控室中打开性能监控器
#XTIT: card title: Admission Control Rejection Events
acRejectedEvents=许可控制 - 拒绝事件
#XTIT: card title: Admission Control Queuing Events
acQueuedEvents=许可控制 - 排队事件

#XTIT: card title: Top 5 Admission Control Rejection Events by Space
acRejectionEventsBySpace=许可控制 - 各空间的前 5 个拒绝事件
#XTIT: card title: Top 5 Admission Control Queuing Events by Space
acQueueingEventsBySpace=许可控制 - 各空间的前 5 个排队事件

#XTIT: payload Table and List texts
payloadDownloadText=已下载有效负载
dataSourceText=数据源
dataSourcesObject=名称
dataSourcesSchema=空间名称
payloadListHeader=有效负载
payloadStoryIdText=故事标识
payloadStoryNameText=故事名称
payloadDialogTitleText=更多信息
payloadTextAreaTitle=有效负载
NO_MDS_DETAIL=无可用数据
MDS_DATA_FETCH_FAILED=由于服务器错误，无法显示 MDS 语句的信息。
GBUnit=GB
enabled=已启用
notEnabled=未启用
#XTIT: ECN Monitoring Dashboard Texts
ecnStatus=状态
ecnBlock=计算区块
ecnSize=大小
ecnConfigMemory=内存
ecnConfigStorage=存储
ecnConfigSpacesList=空间列表
#XFLD: ECN phase Ready
ecnReady=就绪
#XFLD: ECN phase Running
ecnRunning=运行中
#XFLD: ECN phase Initial
ecnInitial=未就绪
#XFLD: ECN phase Starting
ecnStarting=正在启动
#XFLD: ECN phase Stopping
ecnStopping=正在停止
#XFLD: ECN phase Unknown
ecnUnknown=未知
#XFLD: ECN Last Run Details
lastRun=最新运行
#XFLD: ECN Previous Run Details
previousRun=先前运行
#XFLD: ECN Manage Button
manage=管理弹性计算节点
#XFLD: ECN Run Details Widget
days=天
#XFLD: ECN Run Details Widget Title
runDetails=运行详细信息
#XFLD: ECN Performance Class Label
performanceClass=性能类
start=启动
stop=停止
ecnRunDetailsUpTime=正常运行时间
ecnConfigurationCPU=vCPU 数
ecnTechnicalName=技术名称
currentMonth=当月
previousMonth=上月
ecnBlockHours=计算区块时数
upTimeOverall=总计正常运行时间
refresh=刷新
top5MemConsumptionDataFetchErrorText=没能获取内存使用数据
ecnDashboardText=弹性计算节点
#XMSG: Message Title when ECN is not in ruuning state
memoryDistributionErrorTitle=不适用
#XMSG: Warning Message when ECN is not in ruuning state
memoryDistributionErrorMessage=只有当弹性计算节点处于运行状态时，才会显示内存分配。
#XMSG: Title for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageTitle=没有选择弹性计算节点
#XMSG: Description for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageDescription=请先从标题的列表中选择一个。
#XMSG: Tab label for Statememt Logs
statementLogsText=声明日志
#XMSG: Tab label for Task Logs
taskLogsText=任务日志
#MSG: Label for Date Time Quick Filter
labelForTimeQuickFilter=日期和时间范围：
labelForSpaceQuickFilter=空间
labelForStatusQuickFilter=状态
#XTIT: Cancel Task Started toast msg
cancelTaskStarted=任务取消操作已开始
#YMSE: Cancel Task Error
taskCancelError=没能取消任务，因为这项任务已经完成运行。
#LSA Monitor Tab Name
lsaMonitorText=对象存储
#ID for LSA Storage By Space Widget
hdlfStorageSpace=SAP HANA 数据湖文件：存储利用率
#ID for LSA Storage for All Spaces Widget
hdlfStorageSpaceTotal=SAP HANA 数据湖文件：全空间存储利用率
#Labels for LSA Monitor
lsaSparkTableApplicationColumnText=应用程序
lsaSparkTableNoOfTasksColumnText=任务数
sparkApplicationConfigurationTitle=应用程序配置
sparkExecutorCPU=执行程序 CPU
sparkExecutorMemory=执行程序内存
sparkDriverCPU=驱动程序 CPU
sparkDriverMemory=驱动程序内存
sparkMaximumCPU=最大 CPU
sparkMaximumMemory=最大内存
sparkMinExecutors=最小执行程序数
sparkMaxExecutors=最大执行程序数
sparkAppTableNoDataIMTitle=未选择空间
sparkAppTableNoDataIMDesc=请先从筛选器栏的列表中选择一个。
TBUnit=TB
sparkTaskTableNoDataIMTitle=没有可用任务
sparkTaskTableNoDataIMDesc=对于所选应用程序，当前没有可用任务。请选择其他应用程序。
taskActivity=任务活动
sparkTableNoSearchResultsTitle=没有搜索结果
sparkTableNoSearchResultsDesc=请更改搜索词并重试。
sparkTableNoFilterResultsTitle=没有筛选结果
sparkTableNoFilterResultsDesc=更改筛选条件并重试。
removeFilterText=清除筛选器
sortTableText=排序表
filterTableText=筛选器
apacheSparkTableText=Apache Spark：任务
closeColumnText=关闭列
