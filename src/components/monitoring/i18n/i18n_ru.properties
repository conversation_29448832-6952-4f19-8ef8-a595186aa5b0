#XTIT: Dynamic Page title
pageTitle=Вас приветствует монитор системы!
#XTIT: Dynamic Page subtitle
pageSubtitle=Отслеживайте производительность системы и определяйте проблемы с памятью, задачей, нехватку памяти и другие проблемы.
#XTIT: Messagestrip Enable Statement tracing
messagestripEnableStatementTracing=Чтобы отслеживать ошибки нехватки памяти и другую информацию об инструкциях, активируйте трассировку ресурсоемких инструкций в конфигурации/мониторинге.
#XFLD: Dashboard text
dashBoardText=Инструментальная панель
#XFLD: TaskLog text
taskLogText=Журналы
#XTIT: card title: Storage Distribution
storageDistribution=Использование места на диске
#XTIT: card title: Memory Distribution
memoryDistribution=Распределение оперативной памяти
#XTIT: card title:Used Disk Capacity
usedStorageCapacity=Использование диска для данных пространств
#XTIT: card title: Used Memory Capacity
usedMemoryCapacity=Использование памяти для данных пространств
#XTIT: card title:Assigned Disk Capacity
assignedStorageCapacity=Присвоение диска для данных пространств
#XTIT: card title: Assigned Memory Capacity
assignedMemoryCapacity=Присвоение памяти для данных пространств
#XTIT: card title: Errors
errors=Не выполненные задачи
#XTIT: card title: Configuration
ecnConfiguration=Конфигурация
#XTIT: card title: ECN Monthly Uptime
upTime=Продуктивное время в месяц
#XTIT: card title: ECN Average Memory
ECNAvgMemory=Средняя память
#XTIT: card title: ECN Average CPU
ECNAvgCPU=Средний ЦП
#XTIT: card Sub title: ECN Hours Subtitle
ECNHoursSubtitle=Последние {0} ч
#XTIT: card Title: ECN Last 5 Start Tasks
ecnTop5StartStop=Последние 5 запущенных/остановленных задач ЭУВ
#XTIT: card Title: ECN Last 5 Stop Tasks
ecnTop5Stop=Последние 5 остановленных задач ЭУВ
#XTIT: card title: Out of Memory Events (all)
OoMEvents=Ошибки нехватки памяти
#XTIT: card title: MDS
OoMMDSQueries=Ошибки нехватки памяти (запросы MDS)
#XTIT: card title: Top 5 Memory Consumption (Statements)
memoryConsumptionStatements=Первые 5 инструкций по потреблению памяти при обработке
#XTIT: card title: Out of Memory Events (Workload Class) by Space
OoMEventsByWorkloadClass=Первые 5 ошибок нехватки памяти (класс рабочей нагрузки) по пространству
#XTIT: card title: Run Duration
runDuration=Первые 5 задач по продолжительности выполнения
#XTIT: card title: Top 5 Tasks by Memory Consumption
tasksBymemoryConsumption=Первые 5 задач по потреблению памяти при обработке
#XTIT: card title: Memory Consumption
memoryConsumption=Потребление памяти
#XTIT: card title: Top 5 Memory Consumption (MDS)
memoryConsumptionSAC=Первые 5 запросов MDS по потреблению памяти при обработке
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Данные журнала аудита
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Административные данные
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Прочие данные
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Данные в пространствах
#XFLD: ResourceCategory Available Memory
ResourceCategoryAvailableMemory=Неиспользуемая память
#XFLD: ResourceCategory Memory Used by Consumption
ResourceCategoryMemoryConsumption=Использование памяти для обработки
#XFLD: ResourceCategory Memory Used by Replication
ResourceCategoryMemoryReplication=Использование памяти для тиражирования данных
#XFLD: ResourceCategory unknown
unknown=Неизвестно
#XBUT: Card footer view list button
viewList=Просмотреть журналы
#NOTR: placeholder for dev-purposes
test=TEST
#XTIT: card bar label: hours
hours=ч
#XTIT: card subtitle: now
now=Сейчас
#XTIT: card subtitle: last 24 hours
last24Hours=Последние 24 часа
#XTIT: card subtitle: last 48 hours
last48Hours=Последние 48 часов
#XTIT: card subtitle: last week
lastWeek=Последние 7 дней
#XTIT: card subtitle: last month
lastMonth=Последний месяц
#XFLD: Close
close=Закрыть
#XFLD: Today
today=Сегодня
#XTIT: text for task tab
taskTab=Задачи
#XTIT: text for statement tab
statementTab=Инструкции
#XTIT: text fo nav link in table cell
viewStatements=Просмотреть
#XTIT: text to more link in table cell
more=Больше
#XTIT: text for statement dialog header
statementDlgTitle=Инструкция
#XBUT: text for close btn on dialog
closeBtn=Закрыть
#XBUT: text for copy btn on dialog
copyBtn=Скопировать
#XTIT: Copy success text on toast msg
copiedToClipboard=Скопировано в буфер обмена
#XTIT: test for download btn on dialog
downloadBtn=Выгрузить

#XTIT: Monitoring table column names
startTime=Время начала
startDate=Дата начала
duration=Продолжительность
objectType=Тип объекта
activity=Операция
spaceName=Имя пространства
objectName=Имя объекта
peakMemory=Пиковая память SAP HANA
peakCPU=Время ЦП SAP HANA
noOfRecords=Записи
usedMemory=Использование памяти SAP HANA
usedDisk=Использование диска SAP HANA
status=Статус
subStatus=Подстатус
user=Пользователь
targetTable=Целевая таблица
statements=Инструкции
outOfMemory=Нехватка памяти
taskLogId=Ид. журнала задач
statementDetails=Сведения об инструкции
parameters=Параметры
workloadClass=Класс рабочей нагрузки
errorCode=Код ошибки
errorText=Сообщение об ошибке
dbUser=Пользователь базы данных
connectionID=Ид. соединения
statementID=Ид. инструкции
elasticComputeNode=Эластичный узел вычислений


#Localization for @sap/dragonet-ui5-table-editor node module
#XTIT: Title for column settings dialog
columnsSettingsDialogTitle=Настройки столбцов
#XFLD: Title for Error type
messagesTableType=Тип
#XFLD: Title for Error Message
messagesTableMessage=Сообщение
#XFLD: Title for filter
filteredBy=Отфильтровано по:
#XTIT: text for values contained in filter
filterContains=содержит
#XTIT: text for values starting with in filter
filterStartsWith=начинается с
#XTIT: text for values ending with in filter
filterEndsWith=заканчивается на
#XTIT: Title for search in data preview toolbar
toolbarSearch=Поиск
#XBUT: Button to clear filter
clearFilter=Очистить фильтр
#XBUT: Button to cancel the operation
cancel=Отменить
#XBUT: Button to save the operation
ok=ОК
#XBUT: Button to restore the data
toolbarRestoreButton=Обновить
#XBUT: Button to cancel running task
cancelBtnText=Отменить задачу
#XBUT: tooltip for Button to restore the data
toolbarRestoreButtonTooltip=Обновить страницу
#XMSG: Busy dialog for Columns Settings dialog
PROCESSING=Обрабатывается...
#XMSG: Message Confirmation
confirmation=Подтверждение
#XMSG: Message for refresh successful
refreshSuccess=Успешно обновлено
#XMSG: Message for refresh successful
refreshSuccessful=Обновлено
#XMSG: Message for restore successful
restoreSuccessful=Успешно восстановлено
#XMSG: Error message when null value found on non-nullable field
mdm-emptyValueError=Ввод "{0}" обязателен. {1}.
#XTIT: Sort Ascending
mdm-sortAscending=Сортировать по восходящей
#XTIT: Sort Descending
mdm-sortDescending=Сортировать по нисходящей
#XTIT: Filter
mdm-Filter=Фильтр
#XBUT: Button Cancel
mdm-cancel=Отменить
#XBUT: Button Add
mdm-Add=Добавить
#XMSG: and inside error message
and=и
#XMSG: Error message when no column selected
mdm-allColumnUnselected=Для продолжения выберите один или несколько столбцов.

#Localization keys not needed for System Monitor but required for @sap/dragonet-ui5-table-editor node module to remove console errors
#XTXT: Message information strip for filtering and sorting
mdm-InfoStrip=Изменения не сохранены, поэтому инструменты фильтрации, сортировки, удаления и настройки таблиц деактивированы. Сохраните изменения, чтобы активировать их.
#XBUT: Button for file upload in mdm data preview
toolbarUploadButton=Загрузить данные
#XBUT: Button for duplicating rows in mdm data preview
toolbarDuplicateButton=Дублировать
#XBUT: Button for deleting rows in mdm data preview
toolbarDeleteButton=Удалить
#XBUT: Button for adding rows in mdm data preview
toolbarAddButton=Добавить

#XTIT: MDM Empty value Handling in Table Editor Label
mdmInsertMissingString=Вставить пропущенное значение строки как:
#XTIT: MDM Empty value Handling in Table Editor Null Combobox item
mdmNullText=НОЛЬ
#XTIT: MDM Empty value Handling in Table Editor Empty string Combobox item
mdmEmptyStringText=Пустая строка
#XTXT: Message information strip for Insert Missing String Value
mdmInfoStripMissingString=Пропущенные значения строки вставляются только в видимые строковые столбцы новых и отредактированных строк. Используйте настройки столбцов, чтобы отобразить все релевантные столбцы.
#XTXT: Message information strip for filtering sorting and Insert Missing String Value
mdm-InfoStripWithMissingString=Несохраненные изменения деактивируют инструменты фильтра, сортировки, вставки пропущенного значения строки и настройки таблицы. Сохраните изменения, чтобы активировать их.
#XFLD: Open SAP HANA Cockpit
openHanaCockpit=Обзор базы данных (пульт управления SAP HANA)
#XBUT: Open SAP HANA Performance Monitor Cockpit
openHanaCockpitPerfMonitor=Монитор производительности
openHanaCockpitPerfMonitorTooltip=Открыть монитор производительности в пульте управления SAP HANA
#XTIT: card title: Admission Control Rejection Events
acRejectedEvents=Контроль допуска: события отклонения
#XTIT: card title: Admission Control Queuing Events
acQueuedEvents=Контроль допуска: события постановки в очередь

#XTIT: card title: Top 5 Admission Control Rejection Events by Space
acRejectionEventsBySpace=5 первых событий отклонения контроля допуска по пространству
#XTIT: card title: Top 5 Admission Control Queuing Events by Space
acQueueingEventsBySpace=5 первых событий постановки в очередь контроля допуска по пространству

#XTIT: payload Table and List texts
payloadDownloadText=Полезная нагрузка выгружена
dataSourceText=Источники данных
dataSourcesObject=Имя
dataSourcesSchema=Имя пространства
payloadListHeader=Полезные данные
payloadStoryIdText=Ид. журнала
payloadStoryNameText=Имя журнала
payloadDialogTitleText=Подробная информация
payloadTextAreaTitle=Полезные данные
NO_MDS_DETAIL=Нет доступных данных
MDS_DATA_FETCH_FAILED=Не удалось отобразить информацию об инструкциях MDS из-за ошибки сервера.
GBUnit=ГБ
enabled=Активировано
notEnabled=Не активировано
#XTIT: ECN Monitoring Dashboard Texts
ecnStatus=Статус
ecnBlock=Блоки вычислений
ecnSize=Размер
ecnConfigMemory=Память
ecnConfigStorage=Память
ecnConfigSpacesList=Список пространств
#XFLD: ECN phase Ready
ecnReady=Готово
#XFLD: ECN phase Running
ecnRunning=Выполняется
#XFLD: ECN phase Initial
ecnInitial=Не готово
#XFLD: ECN phase Starting
ecnStarting=Запускается
#XFLD: ECN phase Stopping
ecnStopping=Останавливается
#XFLD: ECN phase Unknown
ecnUnknown=Неизвестно
#XFLD: ECN Last Run Details
lastRun=Последнее выполнение
#XFLD: ECN Previous Run Details
previousRun=Предыдущее выполнение
#XFLD: ECN Manage Button
manage=Управление узлом эластичных вычислений
#XFLD: ECN Run Details Widget
days=дн.
#XFLD: ECN Run Details Widget Title
runDetails=Сведения о прогоне
#XFLD: ECN Performance Class Label
performanceClass=Класс производительности
start=Запустить
stop=Остановить
ecnRunDetailsUpTime=Продуктивное время
ecnConfigurationCPU=Число ВЦП
ecnTechnicalName=Техническое имя
currentMonth=Текущий месяц
previousMonth=Предыдущий месяц
ecnBlockHours=Блокочасы
upTimeOverall=Общее продуктивное время
refresh=Обновить
top5MemConsumptionDataFetchErrorText=Не удалось вызвать данные для потребления памяти
ecnDashboardText=Узлы эластичных вычислений
#XMSG: Message Title when ECN is not in ruuning state
memoryDistributionErrorTitle=Неприменимо
#XMSG: Warning Message when ECN is not in ruuning state
memoryDistributionErrorMessage=Распределение памяти отображается только при статусе "Выполняется" узла эластичных вычислений.
#XMSG: Title for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageTitle=Не выбран узел эластичных вычислений
#XMSG: Description for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageDescription=Начните с его выбора из списка в заголовке.
#XMSG: Tab label for Statememt Logs
statementLogsText=Журналы инструкций
#XMSG: Tab label for Task Logs
taskLogsText=Журналы задач
#MSG: Label for Date Time Quick Filter
labelForTimeQuickFilter=Диапазон дат и времени:
labelForSpaceQuickFilter=Пространства
labelForStatusQuickFilter=Статусы
#XTIT: Cancel Task Started toast msg
cancelTaskStarted=Отмена задачи запущена
#YMSE: Cancel Task Error
taskCancelError=Задачу невозможно отменить, так как ее выполнение уже завершено.
#LSA Monitor Tab Name
lsaMonitorText=Хранилище объектов
#ID for LSA Storage By Space Widget
hdlfStorageSpace=Файлы озера данных SAP HANA: использование хранилища
#ID for LSA Storage for All Spaces Widget
hdlfStorageSpaceTotal=Файлы озера данных SAP HANA: использование хранилища для всех пространств
#Labels for LSA Monitor
lsaSparkTableApplicationColumnText=Приложение
lsaSparkTableNoOfTasksColumnText=Число задач
sparkApplicationConfigurationTitle=Конфигурация приложения
sparkExecutorCPU=ЦП исполнителя
sparkExecutorMemory=Память исполнителя
sparkDriverCPU=ЦП драйвера
sparkDriverMemory=Память драйвера
sparkMaximumCPU=Максимум ЦП
sparkMaximumMemory=Максимум памяти
sparkMinExecutors=Минимум исполнителей
sparkMaxExecutors=Максимум исполнителей
sparkAppTableNoDataIMTitle=Не выбрано пространство
sparkAppTableNoDataIMDesc=Начните с его выбора из списка на панели фильтров.
TBUnit=ТБ
sparkTaskTableNoDataIMTitle=Отсутствуют задачи
sparkTaskTableNoDataIMDesc=Для выбранного приложения отсутствуют задачи. Выберите другое приложение.
taskActivity=Операция задачи
sparkTableNoSearchResultsTitle=Поиск не дал результатов
sparkTableNoSearchResultsDesc=Измените условие поиска и повторите попытку.
sparkTableNoFilterResultsTitle=Нет результатов фильтрации
sparkTableNoFilterResultsDesc=Измените критерии фильтрации и повторите попытку.
removeFilterText=Очистить фильтр
sortTableText=Сортировать таблицу
filterTableText=Фильтр
apacheSparkTableText=Apache Spark: задачи
closeColumnText=Закрыть столбец
