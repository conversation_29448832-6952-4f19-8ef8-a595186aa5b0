#XTIT: Dynamic Page title
pageTitle=Bem-vindo(a) ao Monitor de sistema
#XTIT: Dynamic Page subtitle
pageSubtitle=Monitore o desempenho do seu sistema e identifique problemas de armazenamento, tarefa e memória insuficiente, entre outros.
#XTIT: Messagestrip Enable Statement tracing
messagestripEnableStatementTracing=Para monitorar os erros de memória insuficiente e outras informações sobre instruções, ative o rastreamento de instrução pesada em Configuração/Monitoramento.
#XFLD: Dashboard text
dashBoardText=Dashboard
#XFLD: TaskLog text
taskLogText=Logs
#XTIT: card title: Storage Distribution
storageDistribution=Armazenamento em disco usado
#XTIT: card title: Memory Distribution
memoryDistribution=Distribuição de memória
#XTIT: card title:Used Disk Capacity
usedStorageCapacity=Disco usado por áreas para armazenamento
#XTIT: card title: Used Memory Capacity
usedMemoryCapacity=Memória usada por áreas para armazenamento
#XTIT: card title:Assigned Disk Capacity
assignedStorageCapacity=Disco atribuído a áreas para armazenamento
#XTIT: card title: Assigned Memory Capacity
assignedMemoryCapacity=Memória atribuída a áreas para armazenamento
#XTIT: card title: Errors
errors=Tarefas com falha
#XTIT: card title: Configuration
ecnConfiguration=Configuração
#XTIT: card title: ECN Monthly Uptime
upTime=Tempo em operação mensal
#XTIT: card title: ECN Average Memory
ECNAvgMemory=Média de memória
#XTIT: card title: ECN Average CPU
ECNAvgCPU=Média de CPU
#XTIT: card Sub title: ECN Hours Subtitle
ECNHoursSubtitle=Últimas {0} horas
#XTIT: card Title: ECN Last 5 Start Tasks
ecnTop5StartStop=Últimos 5 inícios/interrupções de tarefas
#XTIT: card Title: ECN Last 5 Stop Tasks
ecnTop5Stop=Últimas 5 interrupções de tarefas
#XTIT: card title: Out of Memory Events (all)
OoMEvents=Erros de memória insuficiente
#XTIT: card title: MDS
OoMMDSQueries=Erros de memória insuficiente (solicitações MDS)
#XTIT: card title: Top 5 Memory Consumption (Statements)
memoryConsumptionStatements=Cinco instruções com maior consumo de memória de processamento
#XTIT: card title: Out of Memory Events (Workload Class) by Space
OoMEventsByWorkloadClass=Cinco principais erros de memória insuficiente (classe de carga de trabalho) por área
#XTIT: card title: Run Duration
runDuration=Cinco tarefas com maior duração de execução
#XTIT: card title: Top 5 Tasks by Memory Consumption
tasksBymemoryConsumption=Cinco tarefas com maior consumo de memória de processamento
#XTIT: card title: Memory Consumption
memoryConsumption=Consumo de memória
#XTIT: card title: Top 5 Memory Consumption (MDS)
memoryConsumptionSAC=Cinco solicitações MDS com maior consumo de memória de processamento
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Dados do log de auditoria
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Dados administrativos
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Outros dados
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Dados nas áreas
#XFLD: ResourceCategory Available Memory
ResourceCategoryAvailableMemory=Memória não usada
#XFLD: ResourceCategory Memory Used by Consumption
ResourceCategoryMemoryConsumption=Memória usada para processamento
#XFLD: ResourceCategory Memory Used by Replication
ResourceCategoryMemoryReplication=Memória usada para replicação de dados
#XFLD: ResourceCategory unknown
unknown=Desconhecido
#XBUT: Card footer view list button
viewList=Visualizar logs
#NOTR: placeholder for dev-purposes
test=TEST
#XTIT: card bar label: hours
hours=Horas
#XTIT: card subtitle: now
now=Agora
#XTIT: card subtitle: last 24 hours
last24Hours=Últimas 24 horas
#XTIT: card subtitle: last 48 hours
last48Hours=Últimas 48 horas
#XTIT: card subtitle: last week
lastWeek=Últimos 7 dias
#XTIT: card subtitle: last month
lastMonth=Último mês
#XFLD: Close
close=Fechar
#XFLD: Today
today=Hoje
#XTIT: text for task tab
taskTab=Tarefas
#XTIT: text for statement tab
statementTab=Instruções
#XTIT: text fo nav link in table cell
viewStatements=Visualizar
#XTIT: text to more link in table cell
more=Mais
#XTIT: text for statement dialog header
statementDlgTitle=Instrução
#XBUT: text for close btn on dialog
closeBtn=Fechar
#XBUT: text for copy btn on dialog
copyBtn=Copiar
#XTIT: Copy success text on toast msg
copiedToClipboard=Copiado para clipboard
#XTIT: test for download btn on dialog
downloadBtn=Baixar

#XTIT: Monitoring table column names
startTime=Hora de início
startDate=Data de início
duration=Duração
objectType=Tipo de objeto
activity=Atividade
spaceName=Nome da área
objectName=Nome do objeto
peakMemory=Pico de memória do SAP HANA
peakCPU=Tempo de CPU do SAP HANA
noOfRecords=Registros
usedMemory=Memória ocupada do SAP HANA
usedDisk=Disco usado do SAP HANA
status=Status
subStatus=Substatus
user=Usuário
targetTable=Tabela de destino
statements=Instruções
outOfMemory=Memória insuficiente
taskLogId=ID do log de tarefas
statementDetails=Detalhes de instrução
parameters=Parâmetros
workloadClass=Classe de carga de trabalho
errorCode=Código de erro
errorText=Mensagem de erro
dbUser=Usuário do banco de dados
connectionID=ID da conexão
statementID=ID da instrução
elasticComputeNode=Nó de computação elástica


#Localization for @sap/dragonet-ui5-table-editor node module
#XTIT: Title for column settings dialog
columnsSettingsDialogTitle=Configurações de colunas
#XFLD: Title for Error type
messagesTableType=Tipo
#XFLD: Title for Error Message
messagesTableMessage=Mensagem
#XFLD: Title for filter
filteredBy=Filtrado por:
#XTIT: text for values contained in filter
filterContains=contém
#XTIT: text for values starting with in filter
filterStartsWith=começa com
#XTIT: text for values ending with in filter
filterEndsWith=termina com
#XTIT: Title for search in data preview toolbar
toolbarSearch=Procurar
#XBUT: Button to clear filter
clearFilter=Limpar filtro
#XBUT: Button to cancel the operation
cancel=Cancelar
#XBUT: Button to save the operation
ok=OK
#XBUT: Button to restore the data
toolbarRestoreButton=Atualizar
#XBUT: Button to cancel running task
cancelBtnText=Cancelar tarefa
#XBUT: tooltip for Button to restore the data
toolbarRestoreButtonTooltip=Atualizar a página
#XMSG: Busy dialog for Columns Settings dialog
PROCESSING=Processando...
#XMSG: Message Confirmation
confirmation=Confirmação
#XMSG: Message for refresh successful
refreshSuccess=Atualizado com sucesso
#XMSG: Message for refresh successful
refreshSuccessful=Atualizado
#XMSG: Message for restore successful
restoreSuccessful=Restaurado com sucesso
#XMSG: Error message when null value found on non-nullable field
mdm-emptyValueError="{0}" não pode estar vazio. {1}.
#XTIT: Sort Ascending
mdm-sortAscending=Organizar em ordem crescente
#XTIT: Sort Descending
mdm-sortDescending=Organizar em ordem decrescente
#XTIT: Filter
mdm-Filter=Filtro
#XBUT: Button Cancel
mdm-cancel=Cancelar
#XBUT: Button Add
mdm-Add=Adicionar
#XMSG: and inside error message
and=e
#XMSG: Error message when no column selected
mdm-allColumnUnselected=Selecione uma ou mais colunas para continuar.

#Localization keys not needed for System Monitor but required for @sap/dragonet-ui5-table-editor node module to remove console errors
#XTXT: Message information strip for filtering and sorting
mdm-InfoStrip=As ferramentas Filtro, Organizar, Excluir e Configuração de tabela estão desativadas porque existem alterações não salvas. Salve suas alterações para ativá-las.
#XBUT: Button for file upload in mdm data preview
toolbarUploadButton=Carregar dados
#XBUT: Button for duplicating rows in mdm data preview
toolbarDuplicateButton=Duplicar
#XBUT: Button for deleting rows in mdm data preview
toolbarDeleteButton=Excluir
#XBUT: Button for adding rows in mdm data preview
toolbarAddButton=Adicionar

#XTIT: MDM Empty value Handling in Table Editor Label
mdmInsertMissingString=Inserir valor de string ausente como:
#XTIT: MDM Empty value Handling in Table Editor Null Combobox item
mdmNullText=NULO
#XTIT: MDM Empty value Handling in Table Editor Empty string Combobox item
mdmEmptyStringText=String vazia
#XTXT: Message information strip for Insert Missing String Value
mdmInfoStripMissingString=Os valores de string ausentes são inseridos apenas nas colunas de string visíveis em linhas novas e editadas. Use Configurações de colunas para exibir todas as colunas relevantes.
#XTXT: Message information strip for filtering sorting and Insert Missing String Value
mdm-InfoStripWithMissingString=As ferramentas Filtro, Organizar, Inserir valor de string ausente e Configuração de tabela estão desativadas porque existem alterações não salvas. Salve suas alterações para ativá-las.
#XFLD: Open SAP HANA Cockpit
openHanaCockpit=Visão geral do banco de dados (cockpit do SAP HANA)
#XBUT: Open SAP HANA Performance Monitor Cockpit
openHanaCockpitPerfMonitor=Monitor de desempenho
openHanaCockpitPerfMonitorTooltip=Abrir Monitor de desempenho no cockpit do SAP HANA
#XTIT: card title: Admission Control Rejection Events
acRejectedEvents=Eventos de rejeição do controle de admissão
#XTIT: card title: Admission Control Queuing Events
acQueuedEvents=Eventos de entrada em fila do controle de admissão

#XTIT: card title: Top 5 Admission Control Rejection Events by Space
acRejectionEventsBySpace=5 primeiros eventos de rejeição do controle de admissão por área
#XTIT: card title: Top 5 Admission Control Queuing Events by Space
acQueueingEventsBySpace=5 primeiros eventos de entrada em fila do controle de admissão por área

#XTIT: payload Table and List texts
payloadDownloadText=Payload baixado
dataSourceText=Fontes de dados
dataSourcesObject=Nome
dataSourcesSchema=Nome da área
payloadListHeader=Payloads
payloadStoryIdText=ID da história
payloadStoryNameText=Nome da história
payloadDialogTitleText=Mais informações
payloadTextAreaTitle=Payload
NO_MDS_DETAIL=Dados não disponíveis
MDS_DATA_FETCH_FAILED=Não foi possível exibir informações sobre as instruções de MDS devido a um erro de servidor.
GBUnit=GB
enabled=Ativado
notEnabled=Não ativado
#XTIT: ECN Monitoring Dashboard Texts
ecnStatus=Status
ecnBlock=Blocos de computação
ecnSize=Tamanho
ecnConfigMemory=Memória
ecnConfigStorage=Armazenamento
ecnConfigSpacesList=Lista de áreas
#XFLD: ECN phase Ready
ecnReady=Pronto
#XFLD: ECN phase Running
ecnRunning=Em execução
#XFLD: ECN phase Initial
ecnInitial=Não pronto
#XFLD: ECN phase Starting
ecnStarting=Iniciando
#XFLD: ECN phase Stopping
ecnStopping=Interrompendo
#XFLD: ECN phase Unknown
ecnUnknown=Desconhecido
#XFLD: ECN Last Run Details
lastRun=Última execução
#XFLD: ECN Previous Run Details
previousRun=Execução anterior
#XFLD: ECN Manage Button
manage=Gerenciar nó de computação elástica
#XFLD: ECN Run Details Widget
days=Dias
#XFLD: ECN Run Details Widget Title
runDetails=Detalhes de execução
#XFLD: ECN Performance Class Label
performanceClass=Classe de desempenho
start=Iniciar
stop=Interromper
ecnRunDetailsUpTime=Tempo em operação
ecnConfigurationCPU=Número de vCPUs
ecnTechnicalName=Nome técnico
currentMonth=Mês atual
previousMonth=Mês anterior
ecnBlockHours=Horas de bloco
upTimeOverall=Tempo total em operação
refresh=Atualizar
top5MemConsumptionDataFetchErrorText=Falha ao obter dados para consumo de memória
ecnDashboardText=Nós de computação elástica
#XMSG: Message Title when ECN is not in ruuning state
memoryDistributionErrorTitle=Não aplicável
#XMSG: Warning Message when ECN is not in ruuning state
memoryDistributionErrorMessage=A distribuição de memória só é exibida quando o nó de computação elástica está em estado de execução.
#XMSG: Title for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageTitle=Nenhum nó de computação elástica selecionado
#XMSG: Description for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageDescription=Comece selecionando um da lista no cabeçalho.
#XMSG: Tab label for Statememt Logs
statementLogsText=Logs de instruções
#XMSG: Tab label for Task Logs
taskLogsText=Logs de tarefas
#MSG: Label for Date Time Quick Filter
labelForTimeQuickFilter=Intervalo de data e hora:
labelForSpaceQuickFilter=Áreas
labelForStatusQuickFilter=Status
#XTIT: Cancel Task Started toast msg
cancelTaskStarted=Cancelamento de tarefa iniciado
#YMSE: Cancel Task Error
taskCancelError=Não é possível cancelar a tarefa porque ela já está com a execução finalizada.
#LSA Monitor Tab Name
lsaMonitorText=Repositório de objetos
#ID for LSA Storage By Space Widget
hdlfStorageSpace=Arquivos de data lake do SAP HANA: Utilização de armazenamento
#ID for LSA Storage for All Spaces Widget
hdlfStorageSpaceTotal=Arquivos de data lake do SAP HANA: Utilização de armazenamento de todas as áreas
#Labels for LSA Monitor
lsaSparkTableApplicationColumnText=Aplicativo
lsaSparkTableNoOfTasksColumnText=Número de tarefas
sparkApplicationConfigurationTitle=Configuração do aplicativo
sparkExecutorCPU=CPU do executor
sparkExecutorMemory=Memória do executor
sparkDriverCPU=CPU do driver
sparkDriverMemory=Memória do driver
sparkMaximumCPU=Máximo de CPU
sparkMaximumMemory=Máximo de memória
sparkMinExecutors=Mínimo de executores
sparkMaxExecutors=Máximo de executores
sparkAppTableNoDataIMTitle=Nenhuma área selecionada
sparkAppTableNoDataIMDesc=Comece selecionando uma da lista na barra de filtros.
TBUnit=TB
sparkTaskTableNoDataIMTitle=Nenhuma tarefa disponível
sparkTaskTableNoDataIMDesc=Para o aplicativo selecionado, não há tarefas disponíveis. Selecione outro aplicativo.
taskActivity=Atividade de tarefa
sparkTableNoSearchResultsTitle=Nenhum resultado de pesquisa
sparkTableNoSearchResultsDesc=Altere o termo de pesquisa e tente novamente.
sparkTableNoFilterResultsTitle=Nenhum resultado de filtro
sparkTableNoFilterResultsDesc=Altere os critérios de filtro e tente novamente.
removeFilterText=Limpar filtro
sortTableText=Ordenar tabela
filterTableText=Filtro
apacheSparkTableText=Apache Spark: Tarefas
closeColumnText=Fechar coluna
