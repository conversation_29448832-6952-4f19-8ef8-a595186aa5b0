#XTIT: Dynamic Page title
pageTitle=Velkommen til systemmonitoren
#XTIT: Dynamic Page subtitle
pageSubtitle=Overvåg dit systems ydeevne, og identificer lager, opgave, manglende hukommelse og andre problemer.
#XTIT: Messagestrip Enable Statement tracing
messagestripEnableStatementTracing=Hvis du vil overvåge problemer med manglende hukommelse og andre sætningsoplysninger, bedes du aktivere sporing for tidskrævende sætninger i konfiguration/overvågning.
#XFLD: Dashboard text
dashBoardText=Dashboard
#XFLD: TaskLog text
taskLogText=Logge
#XTIT: card title: Storage Distribution
storageDistribution=Disklager brugt
#XTIT: card title: Memory Distribution
memoryDistribution=Hukommelsesdistribution
#XTIT: card title:Used Disk Capacity
usedStorageCapacity=Disk brugt af spaces til lager
#XTIT: card title: Used Memory Capacity
usedMemoryCapacity=Hukommelse brugt af spaces til lager
#XTIT: card title:Assigned Disk Capacity
assignedStorageCapacity=Disk tildelt til spaces til lager
#XTIT: card title: Assigned Memory Capacity
assignedMemoryCapacity=Hukommelse tildelt til spaces til lager
#XTIT: card title: Errors
errors=Mislykkede opgaver
#XTIT: card title: Configuration
ecnConfiguration=Konfiguration
#XTIT: card title: ECN Monthly Uptime
upTime=Månedlig oppetid
#XTIT: card title: ECN Average Memory
ECNAvgMemory=Gennemsnitshukommelse
#XTIT: card title: ECN Average CPU
ECNAvgCPU=Gennemsnits-CPU
#XTIT: card Sub title: ECN Hours Subtitle
ECNHoursSubtitle=Sidste {0} timer
#XTIT: card Title: ECN Last 5 Start Tasks
ecnTop5StartStop=Seneste 5 start-/stopopgaver for ECN
#XTIT: card Title: ECN Last 5 Stop Tasks
ecnTop5Stop=Seneste 5 stopopgaver for ECN
#XTIT: card title: Out of Memory Events (all)
OoMEvents=Problemer med manglende hukommelse
#XTIT: card title: MDS
OoMMDSQueries=Problemer med manglende hukommelse (MDS-anmodninger)
#XTIT: card title: Top 5 Memory Consumption (Statements)
memoryConsumptionStatements=Top 5-sætninger efter behandlingshukommelsesforbrug
#XTIT: card title: Out of Memory Events (Workload Class) by Space
OoMEventsByWorkloadClass=Top 5-problemer med manglende hukommelse (arbejdsbelastningsklasse) efter space
#XTIT: card title: Run Duration
runDuration=Top 5-opgaver efter kørselsvarighed
#XTIT: card title: Top 5 Tasks by Memory Consumption
tasksBymemoryConsumption=Top 5-opgaver efter behandlingshukommelsesforbrug
#XTIT: card title: Memory Consumption
memoryConsumption=Hukommelsesforbrug
#XTIT: card title: Top 5 Memory Consumption (MDS)
memoryConsumptionSAC=Top 5-MDS-anmodninger efter behandlingshukommelsesforbrug
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Revisionsprotokoldata
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Administrationsdata
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Andre data
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Data i spaces
#XFLD: ResourceCategory Available Memory
ResourceCategoryAvailableMemory=Ubrugt hukommelse
#XFLD: ResourceCategory Memory Used by Consumption
ResourceCategoryMemoryConsumption=Hukommelse, der bruges til behandling
#XFLD: ResourceCategory Memory Used by Replication
ResourceCategoryMemoryReplication=Hukommelse, der bruges til datareplikering
#XFLD: ResourceCategory unknown
unknown=Ukendt
#XBUT: Card footer view list button
viewList=Vis logge
#NOTR: placeholder for dev-purposes
test=TEST
#XTIT: card bar label: hours
hours=Timer
#XTIT: card subtitle: now
now=Nu
#XTIT: card subtitle: last 24 hours
last24Hours=Sidste 24 timer
#XTIT: card subtitle: last 48 hours
last48Hours=Sidste 48 timer
#XTIT: card subtitle: last week
lastWeek=Sidste 7 dage
#XTIT: card subtitle: last month
lastMonth=Sidste måned
#XFLD: Close
close=Luk
#XFLD: Today
today=I dag
#XTIT: text for task tab
taskTab=Opgaver
#XTIT: text for statement tab
statementTab=Sætninger
#XTIT: text fo nav link in table cell
viewStatements=Vis
#XTIT: text to more link in table cell
more=Mere
#XTIT: text for statement dialog header
statementDlgTitle=Sætning
#XBUT: text for close btn on dialog
closeBtn=Luk
#XBUT: text for copy btn on dialog
copyBtn=Kopiér
#XTIT: Copy success text on toast msg
copiedToClipboard=Kopieret til udklipsholder
#XTIT: test for download btn on dialog
downloadBtn=Download

#XTIT: Monitoring table column names
startTime=Starttidspunkt
startDate=Startdato
duration=Varighed
objectType=Objekttype
activity=Aktivitet
spaceName=Navn på space
objectName=Objektnavn
peakMemory=Maksimal hukommelse for SAP HANA
peakCPU=SAP HANA-CPU-tid
noOfRecords=Records
usedMemory=Udnyttet hukommelse for SAP HANA
usedDisk=Udnyttet harddisk for SAP HANA
status=Status
subStatus=Delstatus
user=Bruger
targetTable=Måltabel
statements=Sætninger
outOfMemory=Manglende hukommelse
taskLogId=Opgave-log-id
statementDetails=Sætningsdetaljer
parameters=Parametre
workloadClass=Arbejdsbelastningsklasse
errorCode=Fejlkode
errorText=Fejlmeddelelse
dbUser=Databasebruger
connectionID=Forbindelses-id
statementID=Sætnings-id
elasticComputeNode=Elastisk beregningsnode


#Localization for @sap/dragonet-ui5-table-editor node module
#XTIT: Title for column settings dialog
columnsSettingsDialogTitle=Kolonneindstillinger
#XFLD: Title for Error type
messagesTableType=Type
#XFLD: Title for Error Message
messagesTableMessage=Meddelelse
#XFLD: Title for filter
filteredBy=Filtreret efter:
#XTIT: text for values contained in filter
filterContains=indeholder
#XTIT: text for values starting with in filter
filterStartsWith=starter med
#XTIT: text for values ending with in filter
filterEndsWith=slutter med
#XTIT: Title for search in data preview toolbar
toolbarSearch=Søg
#XBUT: Button to clear filter
clearFilter=Nulstil filter
#XBUT: Button to cancel the operation
cancel=Annuller
#XBUT: Button to save the operation
ok=OK
#XBUT: Button to restore the data
toolbarRestoreButton=Opdater
#XBUT: Button to cancel running task
cancelBtnText=Annuller opgave
#XBUT: tooltip for Button to restore the data
toolbarRestoreButtonTooltip=Opdater siden
#XMSG: Busy dialog for Columns Settings dialog
PROCESSING=Behandler...
#XMSG: Message Confirmation
confirmation=Bekræftelse
#XMSG: Message for refresh successful
refreshSuccess=Opdateret
#XMSG: Message for refresh successful
refreshSuccessful=Opdateret
#XMSG: Message for restore successful
restoreSuccessful=Gendannet
#XMSG: Error message when null value found on non-nullable field
mdm-emptyValueError="{0}" må ikke være tom. {1}.
#XTIT: Sort Ascending
mdm-sortAscending=Sorter stigende
#XTIT: Sort Descending
mdm-sortDescending=Sorter faldende
#XTIT: Filter
mdm-Filter=Filtrér
#XBUT: Button Cancel
mdm-cancel=Annuller
#XBUT: Button Add
mdm-Add=Tilføj
#XMSG: and inside error message
and=og
#XMSG: Error message when no column selected
mdm-allColumnUnselected=Vælg en eller flere kolonner for at fortsætte.

#Localization keys not needed for System Monitor but required for @sap/dragonet-ui5-table-editor node module to remove console errors
#XTXT: Message information strip for filtering and sorting
mdm-InfoStrip=Værktøjer til filtrering, sortering, sletning og tabelindstilling deaktiveres i forbindelse med ikke-gemte ændringer. Gem ændringerne for at aktivere dem.
#XBUT: Button for file upload in mdm data preview
toolbarUploadButton=Upload data
#XBUT: Button for duplicating rows in mdm data preview
toolbarDuplicateButton=Dubler
#XBUT: Button for deleting rows in mdm data preview
toolbarDeleteButton=Slet
#XBUT: Button for adding rows in mdm data preview
toolbarAddButton=Tilføj

#XTIT: MDM Empty value Handling in Table Editor Label
mdmInsertMissingString=Indsæt manglende strengværdi som:
#XTIT: MDM Empty value Handling in Table Editor Null Combobox item
mdmNullText=NULL
#XTIT: MDM Empty value Handling in Table Editor Empty string Combobox item
mdmEmptyStringText=Tom streng
#XTXT: Message information strip for Insert Missing String Value
mdmInfoStripMissingString=Manglende strengværdier indsættes kun i synlige strengkolonner i nye og redigerede rækker. Brug kolonneindstillinger til at vise alle relevante kolonner.
#XTXT: Message information strip for filtering sorting and Insert Missing String Value
mdm-InfoStripWithMissingString=Værktøjerne Filtrering, Sortering, Indsæt manglende strengværdi og Tabelindstilling deaktiveres i forbindelse med ikkegemte ændringer. Gem ændringerne for at aktivere dem.
#XFLD: Open SAP HANA Cockpit
openHanaCockpit=Databaseoverblik (SAP HANA-cockpit)
#XBUT: Open SAP HANA Performance Monitor Cockpit
openHanaCockpitPerfMonitor=Ydeevne-monitor
openHanaCockpitPerfMonitorTooltip=Åbn ydeevne-monitor i SAP HANA Cockpit
#XTIT: card title: Admission Control Rejection Events
acRejectedEvents=Afvisningshændelser for adgangskontrol
#XTIT: card title: Admission Control Queuing Events
acQueuedEvents=Køhændelser for adgangskontrol

#XTIT: card title: Top 5 Admission Control Rejection Events by Space
acRejectionEventsBySpace=Top 5-afvisningshændelser for adgangskontrol efter space
#XTIT: card title: Top 5 Admission Control Queuing Events by Space
acQueueingEventsBySpace=Top 5-køhændelser for adgangskontrol efter space

#XTIT: payload Table and List texts
payloadDownloadText=Payload er downloadet
dataSourceText=Datakilder
dataSourcesObject=Navn
dataSourcesSchema=Navn på space
payloadListHeader=Payloads
payloadStoryIdText=Story-ID
payloadStoryNameText=Storynavn
payloadDialogTitleText=Yderligere oplysninger
payloadTextAreaTitle=Payload
NO_MDS_DETAIL=Ingen tilgængelige data
MDS_DATA_FETCH_FAILED=Vi kunne ikke vise oplysninger om MDS-sætninger på grund af en serverfejl.
GBUnit=GB
enabled=Aktiveret
notEnabled=Ikke aktiveret
#XTIT: ECN Monitoring Dashboard Texts
ecnStatus=Status
ecnBlock=Beregningsblokke
ecnSize=Størrelse
ecnConfigMemory=Hukommelse
ecnConfigStorage=Lager
ecnConfigSpacesList=Liste over spaces
#XFLD: ECN phase Ready
ecnReady=Klar
#XFLD: ECN phase Running
ecnRunning=Kører
#XFLD: ECN phase Initial
ecnInitial=Ikke klar
#XFLD: ECN phase Starting
ecnStarting=Starter
#XFLD: ECN phase Stopping
ecnStopping=Stopper
#XFLD: ECN phase Unknown
ecnUnknown=Ukendt
#XFLD: ECN Last Run Details
lastRun=Seneste kørsel
#XFLD: ECN Previous Run Details
previousRun=Foregående kørsel
#XFLD: ECN Manage Button
manage=Administrer elastisk beregningsnode
#XFLD: ECN Run Details Widget
days=Dage
#XFLD: ECN Run Details Widget Title
runDetails=Kørselsdetaljer
#XFLD: ECN Performance Class Label
performanceClass=Ydeevneklasse
start=Start
stop=Stop
ecnRunDetailsUpTime=Oppetid
ecnConfigurationCPU=Antal vCPU'er
ecnTechnicalName=Teknisk navn
currentMonth=Aktuel måned
previousMonth=Forrige måned
ecnBlockHours=Blok-timer
upTimeOverall=Samlet oppetid
refresh=Opdater
top5MemConsumptionDataFetchErrorText=Kunne ikke hente data for hukommelsesforbrug
ecnDashboardText=Elastiske beregningsnoder
#XMSG: Message Title when ECN is not in ruuning state
memoryDistributionErrorTitle=Ikke relevant
#XMSG: Warning Message when ECN is not in ruuning state
memoryDistributionErrorMessage=Hukommelsesdistribution vises kun, når den elastiske beregningsnode er i kørselstilstand.
#XMSG: Title for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageTitle=Ingen elastisk beregningsnode valgt
#XMSG: Description for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageDescription=Start med at vælge en fra listen i sidehovedet.
#XMSG: Tab label for Statememt Logs
statementLogsText=Sætningslogge
#XMSG: Tab label for Task Logs
taskLogsText=Opgavelogge
#MSG: Label for Date Time Quick Filter
labelForTimeQuickFilter=Område for dato og klokkeslæt:
labelForSpaceQuickFilter=Spaces
labelForStatusQuickFilter=Statusser
#XTIT: Cancel Task Started toast msg
cancelTaskStarted=Opgaveannullering startet
#YMSE: Cancel Task Error
taskCancelError=Opgaven kan ikke annulleres, fordi den allerede er kørt.
#LSA Monitor Tab Name
lsaMonitorText=Object Store
#ID for LSA Storage By Space Widget
hdlfStorageSpace=SAP HANA, datasøfiler: udnyttelse af lagring
#ID for LSA Storage for All Spaces Widget
hdlfStorageSpaceTotal=SAP HANA, datasøfiler: udnyttelse af lagring for alle spaces
#Labels for LSA Monitor
lsaSparkTableApplicationColumnText=Applikation
lsaSparkTableNoOfTasksColumnText=Antal opgaver
sparkApplicationConfigurationTitle=Applikationskonfiguration
sparkExecutorCPU=Udfører CPU
sparkExecutorMemory=Udfører hukommelse
sparkDriverCPU=CPU til driver
sparkDriverMemory=Driverhukommelse
sparkMaximumCPU=Maks. CPU
sparkMaximumMemory=Maksimal hukommelse
sparkMinExecutors=Minimum antal udførere
sparkMaxExecutors=Maksimalt antal udførere
sparkAppTableNoDataIMTitle=Der er ikke valgt et space
sparkAppTableNoDataIMDesc=Start med at vælge et fra listen på filterlinjen.
TBUnit=TB
sparkTaskTableNoDataIMTitle=Der er ingen tilgængelige opgaver
sparkTaskTableNoDataIMDesc=Der er ingen tilgængelige opgaver for den valgte applikation. Vælg en anden applikation.
taskActivity=Opgaveaktivitet
sparkTableNoSearchResultsTitle=Der er ingen søgeresultater
sparkTableNoSearchResultsDesc=Ændr søgekriteriet, og prøv igen.
sparkTableNoFilterResultsTitle=Ingen filterresultater
sparkTableNoFilterResultsDesc=Ændr filterkriteriet, og prøv igen.
removeFilterText=Ryd filter
sortTableText=Sorter tabel
filterTableText=Filtrer
apacheSparkTableText=Apache Spark: Opgaver
closeColumnText=Luk kolonne
