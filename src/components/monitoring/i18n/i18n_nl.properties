#XTIT: Dynamic Page title
pageTitle=Welkom bij systeemmonitor
#XTIT: Dynamic Page subtitle
pageSubtitle=Bewaak de prestaties van uw systeem en identificeer problemen op het gebied van opslag, taken, onvoldoende geheugen, enz.
#XTIT: Messagestrip Enable Statement tracing
messagestripEnableStatementTracing=Om fouten op het gebied van onvoldoende geheugen en andere statementinformatie te bewaken, moet u Tracering van tijdrovende statements inschakelen in Configuratie/bewaking.
#XFLD: Dashboard text
dashBoardText=Dashboard
#XFLD: TaskLog text
taskLogText=Logs
#XTIT: card title: Storage Distribution
storageDistribution=Opslag schijfruimte gebruikt
#XTIT: card title: Memory Distribution
memoryDistribution=Geheugendistributie
#XTIT: card title:Used Disk Capacity
usedStorageCapacity=Schijfruimte gebruikt door ruimten voor opslag
#XTIT: card title: Used Memory Capacity
usedMemoryCapacity=Geheugen gebruikt door ruimten voor opslag
#XTIT: card title:Assigned Disk Capacity
assignedStorageCapacity=Schijfruimte toegewezen voor ruimten voor opslag
#XTIT: card title: Assigned Memory Capacity
assignedMemoryCapacity=Geheugen toegewezen aan ruimten voor opslag
#XTIT: card title: Errors
errors=Mislukte taken
#XTIT: card title: Configuration
ecnConfiguration=Configuratie
#XTIT: card title: ECN Monthly Uptime
upTime=Maandelijkse uptime
#XTIT: card title: ECN Average Memory
ECNAvgMemory=Gemiddeld geheugen
#XTIT: card title: ECN Average CPU
ECNAvgCPU=Gemiddelde CPU
#XTIT: card Sub title: ECN Hours Subtitle
ECNHoursSubtitle=Laatste {0} uur
#XTIT: card Title: ECN Last 5 Start Tasks
ecnTop5StartStop=ECN - laatste 5 begin-/stoptaken
#XTIT: card Title: ECN Last 5 Stop Tasks
ecnTop5Stop=ECN - laatste 5 stoptaken
#XTIT: card title: Out of Memory Events (all)
OoMEvents=Fouten op het gebied van onvoldoende geheugen
#XTIT: card title: MDS
OoMMDSQueries=Fouten op het gebied van onvoldoende geheugen (MDS-aanvragen)
#XTIT: card title: Top 5 Memory Consumption (Statements)
memoryConsumptionStatements=Top 5 verklaringen door geheugengebruik verwerking
#XTIT: card title: Out of Memory Events (Workload Class) by Space
OoMEventsByWorkloadClass=Top 5 fouten op het gebied van onvoldoende geheugen (werkbelastingklasse) op ruimte
#XTIT: card title: Run Duration
runDuration=Top 5 taken op uitvoeringsduur
#XTIT: card title: Top 5 Tasks by Memory Consumption
tasksBymemoryConsumption=Top 5 taken door geheugengebruik verwerking
#XTIT: card title: Memory Consumption
memoryConsumption=Geheugengebruik
#XTIT: card title: Top 5 Memory Consumption (MDS)
memoryConsumptionSAC=Top 5 MDS-aanvragen door geheugengebruik verwerking
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Auditloggegevens
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Beheergegevens
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Andere gegevens
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Gegevens in ruimten
#XFLD: ResourceCategory Available Memory
ResourceCategoryAvailableMemory=Ongebruikt geheugen
#XFLD: ResourceCategory Memory Used by Consumption
ResourceCategoryMemoryConsumption=Geheugen gebruikt voor verwerking
#XFLD: ResourceCategory Memory Used by Replication
ResourceCategoryMemoryReplication=Geheugen gebruikt voor gegevensreplicatie
#XFLD: ResourceCategory unknown
unknown=Onbekend
#XBUT: Card footer view list button
viewList=Logs weergeven
#NOTR: placeholder for dev-purposes
test=TEST
#XTIT: card bar label: hours
hours=Uren
#XTIT: card subtitle: now
now=Nu
#XTIT: card subtitle: last 24 hours
last24Hours=Afgelopen 24 uur
#XTIT: card subtitle: last 48 hours
last48Hours=Afgelopen 48 uur
#XTIT: card subtitle: last week
lastWeek=Afgelopen 7 dagen
#XTIT: card subtitle: last month
lastMonth=Afgelopen maand
#XFLD: Close
close=Sluiten
#XFLD: Today
today=Vandaag
#XTIT: text for task tab
taskTab=Taken
#XTIT: text for statement tab
statementTab=Statements
#XTIT: text fo nav link in table cell
viewStatements=View
#XTIT: text to more link in table cell
more=Meer
#XTIT: text for statement dialog header
statementDlgTitle=Statement
#XBUT: text for close btn on dialog
closeBtn=Sluiten
#XBUT: text for copy btn on dialog
copyBtn=Kopiëren
#XTIT: Copy success text on toast msg
copiedToClipboard=Gekopieerd naar klembord
#XTIT: test for download btn on dialog
downloadBtn=Downloaden

#XTIT: Monitoring table column names
startTime=Begintijd
startDate=Begindatum
duration=Duur
objectType=Objecttype
activity=Activiteit
spaceName=Naam ruimte
objectName=Objectnaam
peakMemory=SAP HANA: piekgeheugen
peakCPU=SAP HANA CPU-tijd
noOfRecords=Records
usedMemory=SAP HANA: gebruikt geheugen
usedDisk=SAP HANA: gebruikte schijf
status=Status
subStatus=Substatus
user=Gebruiker
targetTable=Doeltabel
statements=Statements
outOfMemory=Onvoldoende geheugen
taskLogId=Taaklog-ID
statementDetails=Statementdetails
parameters=Parameters
workloadClass=Werkbelastingklasse
errorCode=Foutcode
errorText=Foutmelding
dbUser=Databasegebruiker
connectionID=Verbindings-ID
statementID=Statement-ID
elasticComputeNode=Knooppunt elastische berekening


#Localization for @sap/dragonet-ui5-table-editor node module
#XTIT: Title for column settings dialog
columnsSettingsDialogTitle=Kolominstellingen
#XFLD: Title for Error type
messagesTableType=Type
#XFLD: Title for Error Message
messagesTableMessage=Melding
#XFLD: Title for filter
filteredBy=Gefilterd op:
#XTIT: text for values contained in filter
filterContains=bevat
#XTIT: text for values starting with in filter
filterStartsWith=begint met
#XTIT: text for values ending with in filter
filterEndsWith=eindigt met
#XTIT: Title for search in data preview toolbar
toolbarSearch=Zoeken
#XBUT: Button to clear filter
clearFilter=Filter wissen
#XBUT: Button to cancel the operation
cancel=Annuleren
#XBUT: Button to save the operation
ok=OK
#XBUT: Button to restore the data
toolbarRestoreButton=Vernieuwen
#XBUT: Button to cancel running task
cancelBtnText=Taak annuleren
#XBUT: tooltip for Button to restore the data
toolbarRestoreButtonTooltip=De pagina bijwerken
#XMSG: Busy dialog for Columns Settings dialog
PROCESSING=Bezig met verwerken...
#XMSG: Message Confirmation
confirmation=Bevestiging
#XMSG: Message for refresh successful
refreshSuccess=Succesvol vernieuwd
#XMSG: Message for refresh successful
refreshSuccessful=Vernieuwd
#XMSG: Message for restore successful
restoreSuccessful=Succesvol hersteld
#XMSG: Error message when null value found on non-nullable field
mdm-emptyValueError="{0}" mag niet leeg zijn. {1}.
#XTIT: Sort Ascending
mdm-sortAscending=Oplopend sorteren
#XTIT: Sort Descending
mdm-sortDescending=Aflopend sorteren
#XTIT: Filter
mdm-Filter=Filteren
#XBUT: Button Cancel
mdm-cancel=Annuleren
#XBUT: Button Add
mdm-Add=Toevoegen
#XMSG: and inside error message
and=en
#XMSG: Error message when no column selected
mdm-allColumnUnselected=Selecteer ten minste één kolom om door te gaan.

#Localization keys not needed for System Monitor but required for @sap/dragonet-ui5-table-editor node module to remove console errors
#XTXT: Message information strip for filtering and sorting
mdm-InfoStrip=Tools voor filteren, sorteren, verwijderen en tabelinstellingen zijn uitgeschakeld door niet-opgeslagen wijzigingen. Sla uw wijzigingen op om deze in te schakelen.
#XBUT: Button for file upload in mdm data preview
toolbarUploadButton=Gegevens uploaden
#XBUT: Button for duplicating rows in mdm data preview
toolbarDuplicateButton=Dupliceren
#XBUT: Button for deleting rows in mdm data preview
toolbarDeleteButton=Verwijderen
#XBUT: Button for adding rows in mdm data preview
toolbarAddButton=Toevoegen

#XTIT: MDM Empty value Handling in Table Editor Label
mdmInsertMissingString=Voeg ontbrekende stringwaarde in als:
#XTIT: MDM Empty value Handling in Table Editor Null Combobox item
mdmNullText=NULL
#XTIT: MDM Empty value Handling in Table Editor Empty string Combobox item
mdmEmptyStringText=Lege string
#XTXT: Message information strip for Insert Missing String Value
mdmInfoStripMissingString=Ontbrekende stringwaarden worden alleen ingevoegd in zichtbare stringkolommen in nieuwe en bewerkte rijen. Gebruik Kolominstellingen om alle relevante kolommen weer te geven.
#XTXT: Message information strip for filtering sorting and Insert Missing String Value
mdm-InfoStripWithMissingString=Tools voor filteren, sorteren, ontbrekende stringwaarde invoegen en tabelinstellingen zijn uitgeschakeld door niet-opgeslagen wijzigingen. Sla uw wijzigingen op om deze in te schakelen.
#XFLD: Open SAP HANA Cockpit
openHanaCockpit=Databaseoverzicht (SAP HANA Cockpit)
#XBUT: Open SAP HANA Performance Monitor Cockpit
openHanaCockpitPerfMonitor=Prestatiemonitor
openHanaCockpitPerfMonitorTooltip=Prestatiemonitor openen in SAP HANA Cockpit
#XTIT: card title: Admission Control Rejection Events
acRejectedEvents=Toegangscontrole: afwijzingsevents
#XTIT: card title: Admission Control Queuing Events
acQueuedEvents=Toegangscontrole: events in wachtrij

#XTIT: card title: Top 5 Admission Control Rejection Events by Space
acRejectionEventsBySpace=Toegangscontrole: top 5 afwijzingsevents op ruimte
#XTIT: card title: Top 5 Admission Control Queuing Events by Space
acQueueingEventsBySpace=Toegangscontrole: top 5 events in wachtrij op ruimte

#XTIT: payload Table and List texts
payloadDownloadText=Payload gedownload
dataSourceText=Gegevensbronnen
dataSourcesObject=Naam
dataSourcesSchema=Naam ruimte
payloadListHeader=Payloads
payloadStoryIdText=Scenario-ID
payloadStoryNameText=Scenarionaam
payloadDialogTitleText=Meer informatie
payloadTextAreaTitle=Payload
NO_MDS_DETAIL=Geen gegevens beschikbaar
MDS_DATA_FETCH_FAILED=We kunnen vanwege een serverfout geen informatie weergeven over MDS-statements.
GBUnit=GB
enabled=Ingeschakeld
notEnabled=Niet ingeschakeld
#XTIT: ECN Monitoring Dashboard Texts
ecnStatus=Status
ecnBlock=Berekeningsblokken
ecnSize=Grootte
ecnConfigMemory=Geheugen
ecnConfigStorage=Geheugen
ecnConfigSpacesList=Lijst met ruimten
#XFLD: ECN phase Ready
ecnReady=Gereed
#XFLD: ECN phase Running
ecnRunning=Wordt uitgevoerd
#XFLD: ECN phase Initial
ecnInitial=Niet gereed
#XFLD: ECN phase Starting
ecnStarting=Wordt gestart
#XFLD: ECN phase Stopping
ecnStopping=Wordt gestopt
#XFLD: ECN phase Unknown
ecnUnknown=Onbekend
#XFLD: ECN Last Run Details
lastRun=Laatste run
#XFLD: ECN Previous Run Details
previousRun=Vorige run
#XFLD: ECN Manage Button
manage=Knooppunt elastische berekening beheren
#XFLD: ECN Run Details Widget
days=Dagen
#XFLD: ECN Run Details Widget Title
runDetails=Rundetails
#XFLD: ECN Performance Class Label
performanceClass=Performanceklasse
start=Start
stop=Stoppen
ecnRunDetailsUpTime=Uptime
ecnConfigurationCPU=Aantal vCPU's
ecnTechnicalName=Technische naam
currentMonth=Huidige maand
previousMonth=Vorige maand
ecnBlockHours=Blokuren
upTimeOverall=Totale uptime
refresh=Vernieuwen
top5MemConsumptionDataFetchErrorText=Gegevens voor geheugengebruik ophalen mislukt
ecnDashboardText=Knooppunten elastische berekening
#XMSG: Message Title when ECN is not in ruuning state
memoryDistributionErrorTitle=Niet van toepassing
#XMSG: Warning Message when ECN is not in ruuning state
memoryDistributionErrorMessage=De geheugendistributie wordt alleen weergegeven wanneer het knooppunt elastische berekening wordt uitgevoerd.
#XMSG: Title for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageTitle=Geen knooppunt elastische berekening geselecteerd
#XMSG: Description for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageDescription=Begin door er een uit de lijst in de kop te selecteren.
#XMSG: Tab label for Statememt Logs
statementLogsText=Statementlogs
#XMSG: Tab label for Task Logs
taskLogsText=Takenlogs
#MSG: Label for Date Time Quick Filter
labelForTimeQuickFilter=Datum- en tijdbereik
labelForSpaceQuickFilter=Ruimten
labelForStatusQuickFilter=Statussen
#XTIT: Cancel Task Started toast msg
cancelTaskStarted=Annuleren taak gestart
#YMSE: Cancel Task Error
taskCancelError=De taak kan niet worden geannuleerd omdat de taak al is uitgevoerd.
#LSA Monitor Tab Name
lsaMonitorText=Objectopslag
#ID for LSA Storage By Space Widget
hdlfStorageSpace=SAP HANA Data Lake-bestanden: opslaggebruik
#ID for LSA Storage for All Spaces Widget
hdlfStorageSpaceTotal=SAP HANA Data Lake-bestanden: opslaggebruik van alle ruimten
#Labels for LSA Monitor
lsaSparkTableApplicationColumnText=Applicatie
lsaSparkTableNoOfTasksColumnText=Aantal taken
sparkApplicationConfigurationTitle=Applicatieconfiguratie
sparkExecutorCPU=CPU uitvoerder
sparkExecutorMemory=Uitvoerdergeheugen
sparkDriverCPU=CPU stuurprogramma
sparkDriverMemory=Geheugen stuurprogramma
sparkMaximumCPU=Maximum CPU
sparkMaximumMemory=Maximum geheugen
sparkMinExecutors=Minimum uitvoerders
sparkMaxExecutors=Maximum uitvoerders
sparkAppTableNoDataIMTitle=Geen ruimte geselecteerd
sparkAppTableNoDataIMDesc=Selecteer er eerst een uit de lijst in de filterbalk.
TBUnit=TB
sparkTaskTableNoDataIMTitle=Geen taken beschikbaar
sparkTaskTableNoDataIMDesc=Voor de geselecteerde applicatie zijn er geen taken beschikbaar. Selecteer een andere applicatie.
taskActivity=Taakactiviteit
sparkTableNoSearchResultsTitle=Geen zoekresultaten
sparkTableNoSearchResultsDesc=Wijzig de zoekterm en probeer het opnieuw
sparkTableNoFilterResultsTitle=Geen filterresultaten
sparkTableNoFilterResultsDesc=Wijzig filtercriteria en probeer opnieuw.
removeFilterText=Filter wissen
sortTableText=Tabel sorteren
filterTableText=Filteren
apacheSparkTableText=Apache Spark: taken
closeColumnText=Kolom sluiten
