#XTIT: Dynamic Page title
pageTitle=歡迎使用系統監控器
#XTIT: Dynamic Page subtitle
pageSubtitle=監控系統效能，並識別儲存、工作細項、記憶體不足和其他問題。
#XTIT: Messagestrip Enable Statement tracing
messagestripEnableStatementTracing=若要監控記憶體不足錯誤和其他陳述式資訊，請於組態/監控中啟用消耗資源的陳述式追蹤。
#XFLD: Dashboard text
dashBoardText=儀表板
#XFLD: TaskLog text
taskLogText=日誌
#XTIT: card title: Storage Distribution
storageDistribution=使用的硬碟儲存
#XTIT: card title: Memory Distribution
memoryDistribution=記憶體分配
#XTIT: card title:Used Disk Capacity
usedStorageCapacity=空間用於儲存的硬碟
#XTIT: card title: Used Memory Capacity
usedMemoryCapacity=空間用於儲存的記憶體
#XTIT: card title:Assigned Disk Capacity
assignedStorageCapacity=指派給空間用於儲存的硬碟
#XTIT: card title: Assigned Memory Capacity
assignedMemoryCapacity=指派給空間用於儲存的記憶體
#XTIT: card title: Errors
errors=失敗的工作細項
#XTIT: card title: Configuration
ecnConfiguration=組態
#XTIT: card title: ECN Monthly Uptime
upTime=每月工作時間
#XTIT: card title: ECN Average Memory
ECNAvgMemory=平均記憶體
#XTIT: card title: ECN Average CPU
ECNAvgCPU=平均 CPU
#XTIT: card Sub title: ECN Hours Subtitle
ECNHoursSubtitle=最後 {0} 小時
#XTIT: card Title: ECN Last 5 Start Tasks
ecnTop5StartStop=ECN 最後 5 個開始/停止工作細項
#XTIT: card Title: ECN Last 5 Stop Tasks
ecnTop5Stop=ECN 最後 5 個停止工作細項
#XTIT: card title: Out of Memory Events (all)
OoMEvents=記憶體不足錯誤
#XTIT: card title: MDS
OoMMDSQueries=記憶體不足錯誤 (MDS 請求)
#XTIT: card title: Top 5 Memory Consumption (Statements)
memoryConsumptionStatements=各處理中記憶體使用的前 5 個陳述式
#XTIT: card title: Out of Memory Events (Workload Class) by Space
OoMEventsByWorkloadClass=各空間的前 5 個記憶體不足錯誤 (工作負荷類別)
#XTIT: card title: Run Duration
runDuration=各執行持續期的前 5 個工作細項
#XTIT: card title: Top 5 Tasks by Memory Consumption
tasksBymemoryConsumption=各處理中記憶體使用的前 5 個工作細項
#XTIT: card title: Memory Consumption
memoryConsumption=記憶體使用
#XTIT: card title: Top 5 Memory Consumption (MDS)
memoryConsumptionSAC=各處理中記憶體使用的前 5 個 MDS 請求
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=稽核日誌資料
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=管理資料
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=其他資料
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=空間中的資料
#XFLD: ResourceCategory Available Memory
ResourceCategoryAvailableMemory=未使用的記憶體
#XFLD: ResourceCategory Memory Used by Consumption
ResourceCategoryMemoryConsumption=用於處理的記憶體
#XFLD: ResourceCategory Memory Used by Replication
ResourceCategoryMemoryReplication=用於資料複製的記憶體
#XFLD: ResourceCategory unknown
unknown=未知
#XBUT: Card footer view list button
viewList=檢視日誌
#NOTR: placeholder for dev-purposes
test=TEST
#XTIT: card bar label: hours
hours=小時
#XTIT: card subtitle: now
now=現在
#XTIT: card subtitle: last 24 hours
last24Hours=過去 24 小時
#XTIT: card subtitle: last 48 hours
last48Hours=過去 48 小時
#XTIT: card subtitle: last week
lastWeek=過去 7 天
#XTIT: card subtitle: last month
lastMonth=上個月
#XFLD: Close
close=關閉
#XFLD: Today
today=今天
#XTIT: text for task tab
taskTab=工作細項
#XTIT: text for statement tab
statementTab=陳述式
#XTIT: text fo nav link in table cell
viewStatements=檢視
#XTIT: text to more link in table cell
more=更多
#XTIT: text for statement dialog header
statementDlgTitle=陳述式
#XBUT: text for close btn on dialog
closeBtn=關閉
#XBUT: text for copy btn on dialog
copyBtn=複製
#XTIT: Copy success text on toast msg
copiedToClipboard=已複製到剪貼簿
#XTIT: test for download btn on dialog
downloadBtn=下載

#XTIT: Monitoring table column names
startTime=開始時間
startDate=開始日期
duration=持續期
objectType=物件類型
activity=活動
spaceName=空間名稱
objectName=物件名稱
peakMemory=SAP HANA 尖峰記憶體
peakCPU=SAP HANA CPU 時間
noOfRecords=記錄
usedMemory=SAP HANA 使用的記憶體
usedDisk=SAP HANA 使用的磁碟
status=狀態
subStatus=子狀態
user=使用者
targetTable=目標表格
statements=陳述式
outOfMemory=記憶體不足
taskLogId=工作細項日誌 ID
statementDetails=陳述式明細
parameters=參數
workloadClass=工作負荷類別
errorCode=錯誤代碼
errorText=錯誤訊息
dbUser=資料庫使用者
connectionID=連線 ID
statementID=陳述式 ID
elasticComputeNode=彈性計算節點


#Localization for @sap/dragonet-ui5-table-editor node module
#XTIT: Title for column settings dialog
columnsSettingsDialogTitle=欄設定
#XFLD: Title for Error type
messagesTableType=類型
#XFLD: Title for Error Message
messagesTableMessage=訊息
#XFLD: Title for filter
filteredBy=篩選依據：
#XTIT: text for values contained in filter
filterContains=包含
#XTIT: text for values starting with in filter
filterStartsWith=開頭為
#XTIT: text for values ending with in filter
filterEndsWith=結尾為
#XTIT: Title for search in data preview toolbar
toolbarSearch=搜尋
#XBUT: Button to clear filter
clearFilter=清除篩選
#XBUT: Button to cancel the operation
cancel=取消
#XBUT: Button to save the operation
ok=確定
#XBUT: Button to restore the data
toolbarRestoreButton=重新整理
#XBUT: Button to cancel running task
cancelBtnText=取消工作細項
#XBUT: tooltip for Button to restore the data
toolbarRestoreButtonTooltip=更新頁面
#XMSG: Busy dialog for Columns Settings dialog
PROCESSING=正在處理...
#XMSG: Message Confirmation
confirmation=確認
#XMSG: Message for refresh successful
refreshSuccess=已成功重新整理
#XMSG: Message for refresh successful
refreshSuccessful=已重新整理
#XMSG: Message for restore successful
restoreSuccessful=已成功還原
#XMSG: Error message when null value found on non-nullable field
mdm-emptyValueError="{0}" 不得空白。{1}.
#XTIT: Sort Ascending
mdm-sortAscending=升冪排序
#XTIT: Sort Descending
mdm-sortDescending=降冪排序
#XTIT: Filter
mdm-Filter=篩選
#XBUT: Button Cancel
mdm-cancel=取消
#XBUT: Button Add
mdm-Add=新增
#XMSG: and inside error message
and=和
#XMSG: Error message when no column selected
mdm-allColumnUnselected=選擇一或多個欄以繼續。

#Localization keys not needed for System Monitor but required for @sap/dragonet-ui5-table-editor node module to remove console errors
#XTXT: Message information strip for filtering and sorting
mdm-InfoStrip=未儲存的更改內容將停用篩選、排序、刪除和表格設定工具。請儲存更改內容後，才能啟用。
#XBUT: Button for file upload in mdm data preview
toolbarUploadButton=上傳資料
#XBUT: Button for duplicating rows in mdm data preview
toolbarDuplicateButton=複製
#XBUT: Button for deleting rows in mdm data preview
toolbarDeleteButton=刪除
#XBUT: Button for adding rows in mdm data preview
toolbarAddButton=新增

#XTIT: MDM Empty value Handling in Table Editor Label
mdmInsertMissingString=插入缺少的字串值為：
#XTIT: MDM Empty value Handling in Table Editor Null Combobox item
mdmNullText=NULL
#XTIT: MDM Empty value Handling in Table Editor Empty string Combobox item
mdmEmptyStringText=空白字串
#XTXT: Message information strip for Insert Missing String Value
mdmInfoStripMissingString=缺少字串值只能在新和編輯列中的可檢視字串欄插入。使用「欄設定」顯示所有相關欄。
#XTXT: Message information strip for filtering sorting and Insert Missing String Value
mdm-InfoStripWithMissingString=未儲存的更改內容將停用篩選、排序、插入缺少字串值、和表格設定工具。請儲存更改內容後，才能啟用。
#XFLD: Open SAP HANA Cockpit
openHanaCockpit=資料庫概觀 (SAP HANA 控制台)
#XBUT: Open SAP HANA Performance Monitor Cockpit
openHanaCockpitPerfMonitor=效能監控器
openHanaCockpitPerfMonitorTooltip=在 SAP HANA 控制台中開啟效能監控器
#XTIT: card title: Admission Control Rejection Events
acRejectedEvents=許可控制拒絕事件
#XTIT: card title: Admission Control Queuing Events
acQueuedEvents=許可控制排入佇列事件

#XTIT: card title: Top 5 Admission Control Rejection Events by Space
acRejectionEventsBySpace=各空間前 5 個許可控制拒絕事件
#XTIT: card title: Top 5 Admission Control Queuing Events by Space
acQueueingEventsBySpace=各空間前 5 個許可控制排入佇列事件

#XTIT: payload Table and List texts
payloadDownloadText=已下載承載資料
dataSourceText=資料來源
dataSourcesObject=名稱
dataSourcesSchema=空間名稱
payloadListHeader=承載資料
payloadStoryIdText=故事 ID
payloadStoryNameText=故事名稱
payloadDialogTitleText=更多資訊
payloadTextAreaTitle=承載資料
NO_MDS_DETAIL=沒有資料
MDS_DATA_FETCH_FAILED=由於伺服器發生錯誤，因此無法於 MDS 陳述式顯示資訊。
GBUnit=GB
enabled=已啟用
notEnabled=未啟用
#XTIT: ECN Monitoring Dashboard Texts
ecnStatus=狀態
ecnBlock=計算區塊
ecnSize=大小
ecnConfigMemory=記憶體
ecnConfigStorage=儲存
ecnConfigSpacesList=空間清單
#XFLD: ECN phase Ready
ecnReady=就緒
#XFLD: ECN phase Running
ecnRunning=正在執行中
#XFLD: ECN phase Initial
ecnInitial=未就緒
#XFLD: ECN phase Starting
ecnStarting=正在開始
#XFLD: ECN phase Stopping
ecnStopping=正在停止
#XFLD: ECN phase Unknown
ecnUnknown=未知
#XFLD: ECN Last Run Details
lastRun=上次執行
#XFLD: ECN Previous Run Details
previousRun=先前執行
#XFLD: ECN Manage Button
manage=管理彈性計算節點
#XFLD: ECN Run Details Widget
days=天
#XFLD: ECN Run Details Widget Title
runDetails=執行明細
#XFLD: ECN Performance Class Label
performanceClass=效能類別
start=開始
stop=停止
ecnRunDetailsUpTime=工作時間
ecnConfigurationCPU=vCPU 數量
ecnTechnicalName=技術名稱
currentMonth=本月
previousMonth=上個月
ecnBlockHours=區塊時數
upTimeOverall=總計工作時間
refresh=重新整理
top5MemConsumptionDataFetchErrorText=無法取得記憶體使用的資料
ecnDashboardText=彈性計算節點
#XMSG: Message Title when ECN is not in ruuning state
memoryDistributionErrorTitle=不適用
#XMSG: Warning Message when ECN is not in ruuning state
memoryDistributionErrorMessage=僅會在彈性計算節點處於執行中狀態時，才顯示記憶體分配。
#XMSG: Title for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageTitle=未選擇彈性計算節點
#XMSG: Description for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageDescription=從表頭中的清單選擇一項來開始。
#XMSG: Tab label for Statememt Logs
statementLogsText=陳述式日誌
#XMSG: Tab label for Task Logs
taskLogsText=工作細項日誌
#MSG: Label for Date Time Quick Filter
labelForTimeQuickFilter=日期和時間範圍：
labelForSpaceQuickFilter=空間
labelForStatusQuickFilter=狀態
#XTIT: Cancel Task Started toast msg
cancelTaskStarted=工作細項取消已開始
#YMSE: Cancel Task Error
taskCancelError=由於工作細項已結束執行，因此無法取消。
#LSA Monitor Tab Name
lsaMonitorText=物件儲存
#ID for LSA Storage By Space Widget
hdlfStorageSpace=SAP HANA 資料湖檔案：儲存運用
#ID for LSA Storage for All Spaces Widget
hdlfStorageSpaceTotal=SAP HANA 資料湖檔案：所有空間運用
#Labels for LSA Monitor
lsaSparkTableApplicationColumnText=應用程式
lsaSparkTableNoOfTasksColumnText=工作細項數量
sparkApplicationConfigurationTitle=應用程式組態
sparkExecutorCPU=執行程序 CPU
sparkExecutorMemory=執行程序記憶體
sparkDriverCPU=驅動程式 CPU
sparkDriverMemory=驅動程式記憶體
sparkMaximumCPU=最大 CPU
sparkMaximumMemory=最大記憶體
sparkMinExecutors=最小執行程序
sparkMaxExecutors=最大執行程序
sparkAppTableNoDataIMTitle=未選擇空間
sparkAppTableNoDataIMDesc=從篩選列的清單選擇一項來開始。
TBUnit=TB
sparkTaskTableNoDataIMTitle=沒有工作細項
sparkTaskTableNoDataIMDesc=所選應用程式沒有可用的工作細項。請選擇其他應用程式。
taskActivity=工作細項作業
sparkTableNoSearchResultsTitle=沒有搜尋結果
sparkTableNoSearchResultsDesc=更改搜尋術語並重試。
sparkTableNoFilterResultsTitle=沒有篩選結果
sparkTableNoFilterResultsDesc=更改篩選準則並重試。
removeFilterText=清除篩選
sortTableText=排序表格
filterTableText=篩選
apacheSparkTableText=Apache Spark：工作細項
closeColumnText=關閉欄
