#XTIT: Dynamic Page title
pageTitle=सिस्टम मॉनिटर के लिए स्वागत करें
#XTIT: Dynamic Page subtitle
pageSubtitle=अपने सिस्टम और पहचान संग्रहन, कार्य, आउट ऑफ मेमरी और अन्य समस्या के निष्पादन को मॉनिटर करें.
#XTIT: Messagestrip Enable Statement tracing
messagestripEnableStatementTracing=आउट ऑफ मेमरी त्रुटियों और अन्य विवरण जानकारी को मॉनिटर करने के लिए, कृपया कॉन्फ़िगरेशन/मॉनिटरिंग में कीमती विवरण ट्रैसिंग सक्षम करें.
#XFLD: Dashboard text
dashBoardText=डैशबोर्ड
#XFLD: TaskLog text
taskLogText=लॉग
#XTIT: card title: Storage Distribution
storageDistribution=उपयोग किया गया डिस्क संग्रहण
#XTIT: card title: Memory Distribution
memoryDistribution=स्मृति वितरण
#XTIT: card title:Used Disk Capacity
usedStorageCapacity=संग्रहण के लिए रिक्त स्थान द्वारा प्रयुक्त डिस्क
#XTIT: card title: Used Memory Capacity
usedMemoryCapacity=भंडारण के लिए रिक्त स्थान द्वारा उपयोग की जाने वाली मेमोरी के लिए रिक्त स्थान द्वारा उपयोग की जाने वाली मेमोरी
#XTIT: card title:Assigned Disk Capacity
assignedStorageCapacity=संग्रहण के लिए रिक्त स्थान को असाइन किया गया डिस्क
#XTIT: card title: Assigned Memory Capacity
assignedMemoryCapacity=संग्रहण के लिए रिक्त स्थान को असाइन की गई मेमोरी 
#XTIT: card title: Errors
errors=कार्य विफल हुआ
#XTIT: card title: Configuration
ecnConfiguration=कॉन्फ़िगरेशन
#XTIT: card title: ECN Monthly Uptime
upTime=मासिक अपटाइम
#XTIT: card title: ECN Average Memory
ECNAvgMemory=औसत स्मृति
#XTIT: card title: ECN Average CPU
ECNAvgCPU=औसत CPU
#XTIT: card Sub title: ECN Hours Subtitle
ECNHoursSubtitle=अंतिम {0} घंटे
#XTIT: card Title: ECN Last 5 Start Tasks
ecnTop5StartStop=ECN अंतिम 5 प्रारंभ करें/रोकें कार्य
#XTIT: card Title: ECN Last 5 Stop Tasks
ecnTop5Stop=ECN अंतिम 5 रोकें कार्य
#XTIT: card title: Out of Memory Events (all)
OoMEvents=आउट-ऑफ मेमरी त्रुटियां
#XTIT: card title: MDS
OoMMDSQueries=आउट-ऑफ मेमरी त्रुटियां (MDS अनुरोध)
#XTIT: card title: Top 5 Memory Consumption (Statements)
memoryConsumptionStatements=मेमोरी उपभोग की प्रोसेसिंग द्वारा शीर्ष 5 विवरण
#XTIT: card title: Out of Memory Events (Workload Class) by Space
OoMEventsByWorkloadClass=स्थान द्वारा टॉप 5 आउट ऑफ मेमरी त्रुटियां (कार्यलोड वर्ग)
#XTIT: card title: Run Duration
runDuration=रन अवधि द्वारा टॉप 5 कार्य
#XTIT: card title: Top 5 Tasks by Memory Consumption
tasksBymemoryConsumption=मेमोरी उपभोग की प्रोसेसिंग द्वारा शीर्ष 5 कार्य
#XTIT: card title: Memory Consumption
memoryConsumption=स्मृति उपभोग
#XTIT: card title: Top 5 Memory Consumption (MDS)
memoryConsumptionSAC=मेमोरी उपभोग को प्रोसेसिंग करके शीर्ष 5 MDS अनुरोध
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=ऑडिट लॉग डेटा
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=व्यवस्थापकीय डेटा
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=दूसरा डेटा
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=स्थान में डेटा
#XFLD: ResourceCategory Available Memory
ResourceCategoryAvailableMemory=अप्रयुक्त मेमोरी
#XFLD: ResourceCategory Memory Used by Consumption
ResourceCategoryMemoryConsumption=प्रोसेसिंग के लिए उपयोग की जाने वाली मेमोरी
#XFLD: ResourceCategory Memory Used by Replication
ResourceCategoryMemoryReplication=डेटा प्रतिकृति के लिए उपयोग की जाने वाली मेमोरी
#XFLD: ResourceCategory unknown
unknown=अज्ञात
#XBUT: Card footer view list button
viewList=लॉग देखें
#NOTR: placeholder for dev-purposes
test=TEST
#XTIT: card bar label: hours
hours=घंटे
#XTIT: card subtitle: now
now=अब
#XTIT: card subtitle: last 24 hours
last24Hours=आखिरी 24 घंटे
#XTIT: card subtitle: last 48 hours
last48Hours=आखिरी 48 घंटे
#XTIT: card subtitle: last week
lastWeek=पिछले 7 दिन
#XTIT: card subtitle: last month
lastMonth=अंतिम माह
#XFLD: Close
close=बंद करें
#XFLD: Today
today=आज
#XTIT: text for task tab
taskTab=कार्य
#XTIT: text for statement tab
statementTab=विवरण
#XTIT: text fo nav link in table cell
viewStatements=दृश्य
#XTIT: text to more link in table cell
more=अधिक
#XTIT: text for statement dialog header
statementDlgTitle=विवरण
#XBUT: text for close btn on dialog
closeBtn=बंद करें
#XBUT: text for copy btn on dialog
copyBtn=कॉपी करें
#XTIT: Copy success text on toast msg
copiedToClipboard=क्लिपबोर्ड में कॉपी करें
#XTIT: test for download btn on dialog
downloadBtn=डाउनलोड करें

#XTIT: Monitoring table column names
startTime=प्रारंभ समय
startDate=प्रारंभ दिनांक
duration=अवधि
objectType=ऑब्जेक्ट प्रकार
activity=गतिविधि
spaceName=स्पेस का नाम
objectName=ऑब्जेक्ट नाम
peakMemory=SAP HANA पिक स्मृति
peakCPU=SAP HANA CPU समय
noOfRecords=रिकॉर्ड
usedMemory=SAP HANA उपयोग की गई स्मृति
usedDisk=SAP HANA उपयोग की गई डिस्क
status=स्थिति
subStatus=उपस्थिति
user=उपयोगकर्ता
targetTable=लक्ष्य तालिका
statements=विवरण
outOfMemory=आउट ऑफ मेमरी
taskLogId=कार्य लॉग ID
statementDetails=कथन विवरण
parameters=पैरामीटर
workloadClass=कार्यभार वर्ग
errorCode=त्रुटि कोड
errorText=त्रुटि संदेश
dbUser=डेटाबेस उपयोगकर्ता
connectionID=कनेक्शन ID
statementID=कथन ID
elasticComputeNode=मूल्य-सापेक्षता कम्प्यूट नोड


#Localization for @sap/dragonet-ui5-table-editor node module
#XTIT: Title for column settings dialog
columnsSettingsDialogTitle=कॉलम सेटिंग
#XFLD: Title for Error type
messagesTableType=प्रकार
#XFLD: Title for Error Message
messagesTableMessage=संदेश
#XFLD: Title for filter
filteredBy=इसके द्वारा फ़िल्टर किया गया:
#XTIT: text for values contained in filter
filterContains=इसमें शामिल है
#XTIT: text for values starting with in filter
filterStartsWith=इसके साथ शुरू करता है
#XTIT: text for values ending with in filter
filterEndsWith=इसके साथ समाप्त होता है
#XTIT: Title for search in data preview toolbar
toolbarSearch=खोजें
#XBUT: Button to clear filter
clearFilter=फ़िल्टर क्लियर करें
#XBUT: Button to cancel the operation
cancel=रद्द करें
#XBUT: Button to save the operation
ok=ठीक
#XBUT: Button to restore the data
toolbarRestoreButton=रीफ़्रेश करें
#XBUT: Button to cancel running task
cancelBtnText=टास्क रद्द करें
#XBUT: tooltip for Button to restore the data
toolbarRestoreButtonTooltip=पृष्ठ अपडेट करें
#XMSG: Busy dialog for Columns Settings dialog
PROCESSING=प्रोसेस हो रहा है...
#XMSG: Message Confirmation
confirmation=पुष्टि
#XMSG: Message for refresh successful
refreshSuccess=सफ़लतापूर्वक रीफ़्रेश किया गया
#XMSG: Message for refresh successful
refreshSuccessful=रीफ़्रेश किया गया
#XMSG: Message for restore successful
restoreSuccessful=सफ़लतापूर्वक पुनर्स्थापित किया गया
#XMSG: Error message when null value found on non-nullable field
mdm-emptyValueError="{0}" रिक्त नहीं हो सकता है. {1}.
#XTIT: Sort Ascending
mdm-sortAscending=आरोही क्रमित करें
#XTIT: Sort Descending
mdm-sortDescending=अवरोही क्रम में क्रमित करें
#XTIT: Filter
mdm-Filter=फ़िल्टर करें
#XBUT: Button Cancel
mdm-cancel=रद्द करें
#XBUT: Button Add
mdm-Add=जोड़ें
#XMSG: and inside error message
and=और
#XMSG: Error message when no column selected
mdm-allColumnUnselected=जारी रखने के लिए एक या अधिक स्तंभ का चयन करें.

#Localization keys not needed for System Monitor but required for @sap/dragonet-ui5-table-editor node module to remove console errors
#XTXT: Message information strip for filtering and sorting
mdm-InfoStrip=फ़िल्टर, सॉर्ट, हटाना, और तालिका सेटिंग टूल बिना किसी बदलाव के अक्षम किए गए हैं. उन्हें सक्षम करने के लिए अपने परिवर्तनों को सहेजें.
#XBUT: Button for file upload in mdm data preview
toolbarUploadButton=डेटा अपलोड करें
#XBUT: Button for duplicating rows in mdm data preview
toolbarDuplicateButton=डुप्लिकेट
#XBUT: Button for deleting rows in mdm data preview
toolbarDeleteButton=हटाएं
#XBUT: Button for adding rows in mdm data preview
toolbarAddButton=जोड़ें

#XTIT: MDM Empty value Handling in Table Editor Label
mdmInsertMissingString=इसके रूप में गुम स्ट्रिंग मान सम्मिलित करेंः
#XTIT: MDM Empty value Handling in Table Editor Null Combobox item
mdmNullText=नल
#XTIT: MDM Empty value Handling in Table Editor Empty string Combobox item
mdmEmptyStringText=खाली स्ट्रिंग
#XTXT: Message information strip for Insert Missing String Value
mdmInfoStripMissingString=अनुपलब्ध स्ट्रिंग मान केवल नई और संपादित पंक्तियों में दृश्यमान स्ट्रिंग स्तंभ में डाले जाते हैं. सभी प्रासंगिक स्तंभ प्रदर्शित करने के लिए स्तंभ सेटिंग का उपयोग करें.
#XTXT: Message information strip for filtering sorting and Insert Missing String Value
mdm-InfoStripWithMissingString=फ़िल्टर, क्रमण, गुम स्ट्रिंग मान सम्मिलित करें, और तालिका सेटिंग टूल बिना सहेजे परिवर्तनों को अक्षम किया गया हैं. उन्हें सक्षम करने के लिए अपने परिवर्तनों को सहेजें.
#XFLD: Open SAP HANA Cockpit
openHanaCockpit=डेटाबेस ओवरव्यू (SAP HANA कॉकपिट)
#XBUT: Open SAP HANA Performance Monitor Cockpit
openHanaCockpitPerfMonitor=प्रदर्शन मॉनिटर
openHanaCockpitPerfMonitorTooltip=SAP HANA कॉकपिट में प्रदर्शन मॉनिटर
#XTIT: card title: Admission Control Rejection Events
acRejectedEvents=एडमिशन नियंत्रण अस्वीकृति इवेंट
#XTIT: card title: Admission Control Queuing Events
acQueuedEvents=एडमिशन नियंत्रण कतार इवेंट

#XTIT: card title: Top 5 Admission Control Rejection Events by Space
acRejectionEventsBySpace=स्थान द्वारा शीर्ष 5 एडमिशन नियंत्रण अस्वीकृति इवेंट
#XTIT: card title: Top 5 Admission Control Queuing Events by Space
acQueueingEventsBySpace=स्थान द्वारा शीर्ष 5 एडमिशन नियंत्रण कतार इवेंट

#XTIT: payload Table and List texts
payloadDownloadText=पेलोड डाउनलोड किया गया
dataSourceText=डेटा स्रोत
dataSourcesObject=नाम
dataSourcesSchema=स्पेस का नाम
payloadListHeader=पेलोड
payloadStoryIdText=स्टोरी ID
payloadStoryNameText=स्टोरी नाम
payloadDialogTitleText=अधिक जानकारी
payloadTextAreaTitle=पेलोड
NO_MDS_DETAIL=कोई डेटा उपलब्ध नही है
MDS_DATA_FETCH_FAILED=हमें सर्वर त्रुटि के कारण MDS विवरणों पर जानकारी प्रदर्शित नहीं की जा सकती.
GBUnit=GB
enabled=सक्षम
notEnabled=सक्षम नहीं किया गया
#XTIT: ECN Monitoring Dashboard Texts
ecnStatus=स्थिति
ecnBlock=कंप्यूट ब्लॉक
ecnSize=आकार
ecnConfigMemory=स्मृति
ecnConfigStorage=संग्रहण
ecnConfigSpacesList=स्पेस की सूची
#XFLD: ECN phase Ready
ecnReady=तैयार
#XFLD: ECN phase Running
ecnRunning=रन हो रहा है
#XFLD: ECN phase Initial
ecnInitial=तैयार नहीं
#XFLD: ECN phase Starting
ecnStarting=प्रारंभ कर रहा है
#XFLD: ECN phase Stopping
ecnStopping=रोका जा रहा है
#XFLD: ECN phase Unknown
ecnUnknown=अज्ञात
#XFLD: ECN Last Run Details
lastRun=नविनतम रन
#XFLD: ECN Previous Run Details
previousRun=पिछला रन
#XFLD: ECN Manage Button
manage=मूल्य-सापेक्षता कम्प्यूट नोड प्रबंधित करें
#XFLD: ECN Run Details Widget
days=दिन
#XFLD: ECN Run Details Widget Title
runDetails=रन विवरण
#XFLD: ECN Performance Class Label
performanceClass=प्रदर्शन वर्ग
start=प्रारंभ करें
stop=रुकें
ecnRunDetailsUpTime=अपटाइम
ecnConfigurationCPU=vCPUs की संख्या 
ecnTechnicalName=तकनीकी नाम
currentMonth=वर्तमान महीना
previousMonth=पिछला माह
ecnBlockHours=ब्लॉक-घंटे
upTimeOverall=कुल अपटाइम
refresh=रीफ़्रेश करें
top5MemConsumptionDataFetchErrorText=स्मृति उपभोग के लिए डेटा प्राप्त करने में विफल
ecnDashboardText=इलास्टिक कंप्यूट नोड
#XMSG: Message Title when ECN is not in ruuning state
memoryDistributionErrorTitle=लागू नहीं
#XMSG: Warning Message when ECN is not in ruuning state
memoryDistributionErrorMessage=मेमोरी वितरण केवल तभी प्रदर्शित होता है जब इलास्टिक कंप्यूट नोड रनिंग स्थिति में होता है.
#XMSG: Title for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageTitle=मूल्य-सापेक्षता कम्प्यूट नोड चयनित नहीं है
#XMSG: Description for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageDescription=शीर्षलेख में मौजूद सूची में से चुनकर प्रारंभ करें
#XMSG: Tab label for Statememt Logs
statementLogsText=विवरण लॉग
#XMSG: Tab label for Task Logs
taskLogsText=टास्क लॉग
#MSG: Label for Date Time Quick Filter
labelForTimeQuickFilter=दिनांक और समय सीमा:
labelForSpaceQuickFilter=स्पेस
labelForStatusQuickFilter=स्थितियां
#XTIT: Cancel Task Started toast msg
cancelTaskStarted=कार्य रद्द करना प्रारंभ हो गया
#YMSE: Cancel Task Error
taskCancelError=कार्य को रद्द नहीं किया जा सकता क्योंकि यह पहले ही समाप्त हो चुका है.
#LSA Monitor Tab Name
lsaMonitorText=ऑब्जेक्ट स्टोर
#ID for LSA Storage By Space Widget
hdlfStorageSpace=SAP HANA Data Lake फ़ाइल: संग्रहण उपयोग
#ID for LSA Storage for All Spaces Widget
hdlfStorageSpaceTotal=SAP HANA Data Lake फ़ाइल: सभी स्थानों का संग्रहण उपयोग
#Labels for LSA Monitor
lsaSparkTableApplicationColumnText=एप्लिकेशन
lsaSparkTableNoOfTasksColumnText=कार्यों की संख्या
sparkApplicationConfigurationTitle=एप्लिकेशन कॉन्फ़िगरेशन
sparkExecutorCPU=निष्पादक CPU
sparkExecutorMemory=निष्पादक स्मृति
sparkDriverCPU=ड्राइवर CPU
sparkDriverMemory=ड्राइवर स्मृति
sparkMaximumCPU=अधिकतम CPU
sparkMaximumMemory=अधिकतम स्मृति
sparkMinExecutors=न्यूनतम निष्पादक
sparkMaxExecutors=अधिकतम निष्पादक
sparkAppTableNoDataIMTitle=कोई स्थान चयनित नहीं
sparkAppTableNoDataIMDesc=फ़िल्टर बार में सूची में से किसी एक का चयन करके प्रारंभ करें.
TBUnit=TB
sparkTaskTableNoDataIMTitle=कोई कार्य उपलब्ध नहीं है
sparkTaskTableNoDataIMDesc=चयनित एप्लिकेशन के लिए, कोई कार्य उपलब्ध नहीं है. कोई अन्य एप्लिकेशन का चयन करें.
taskActivity=कार्य गतिविधि
sparkTableNoSearchResultsTitle=कोई खोज परिणाम नहीं
sparkTableNoSearchResultsDesc=खोज शब्द बदलें और पुनः प्रयास करें.
sparkTableNoFilterResultsTitle=कोई फ़िल्टर परिणाम नहीं
sparkTableNoFilterResultsDesc=फ़िल्टर मानदंड बदलें और पुनर्प्रयास करें.
removeFilterText=फिल्टर क्लियर करें
sortTableText=तालिका क्रमित करें
filterTableText=फिल्टर करें
apacheSparkTableText=Apache Spark: कार्य
closeColumnText=स्तंभ बंद करें
