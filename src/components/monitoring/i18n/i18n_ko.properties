#XTIT: Dynamic Page title
pageTitle=시스템 모니터 시작
#XTIT: Dynamic Page subtitle
pageSubtitle=시스템 성능을 모니터하고 저장소, 태스크, 메모리 부족, 기타 이슈를 식별합니다.
#XTIT: Messagestrip Enable Statement tracing
messagestripEnableStatementTracing=메모리 부족 오류와 기타 명령문 정보를 모니터하려면 구성/모니터링에서 고부하 명령문 추적을 활성화하십시오.
#XFLD: Dashboard text
dashBoardText=대시보드
#XFLD: TaskLog text
taskLogText=로그
#XTIT: card title: Storage Distribution
storageDistribution=사용된 디스크 저장소
#XTIT: card title: Memory Distribution
memoryDistribution=메모리 분배
#XTIT: card title:Used Disk Capacity
usedStorageCapacity=저장소 공간에 사용된 디스크
#XTIT: card title: Used Memory Capacity
usedMemoryCapacity=저장소 공간에 사용된 메모리
#XTIT: card title:Assigned Disk Capacity
assignedStorageCapacity=저장소 공간에 지정된 디스크
#XTIT: card title: Assigned Memory Capacity
assignedMemoryCapacity=저장소 공간에 지정된 메모리
#XTIT: card title: Errors
errors=실패한 태스크
#XTIT: card title: Configuration
ecnConfiguration=구성
#XTIT: card title: ECN Monthly Uptime
upTime=월별 업타임
#XTIT: card title: ECN Average Memory
ECNAvgMemory=평균 메모리
#XTIT: card title: ECN Average CPU
ECNAvgCPU=평균 CPU
#XTIT: card Sub title: ECN Hours Subtitle
ECNHoursSubtitle=최근 {0}시간
#XTIT: card Title: ECN Last 5 Start Tasks
ecnTop5StartStop=ECN 최종 5개 시작/중지 태스크
#XTIT: card Title: ECN Last 5 Stop Tasks
ecnTop5Stop=ECN 최종 5개 중지 태스크
#XTIT: card title: Out of Memory Events (all)
OoMEvents=메모리 부족 오류
#XTIT: card title: MDS
OoMMDSQueries=메모리 부족 오류(MDS 요청)
#XTIT: card title: Top 5 Memory Consumption (Statements)
memoryConsumptionStatements=처리 메모리 사용별 상위 5개 명령문
#XTIT: card title: Out of Memory Events (Workload Class) by Space
OoMEventsByWorkloadClass=공간별 상위 5개의 메모리 부족 오류(작업 부하 클래스)
#XTIT: card title: Run Duration
runDuration=실행 기간별 상위 5개의 태스크
#XTIT: card title: Top 5 Tasks by Memory Consumption
tasksBymemoryConsumption=처리 메모리 사용별 상위 5개 태스크
#XTIT: card title: Memory Consumption
memoryConsumption=메모리 사용
#XTIT: card title: Top 5 Memory Consumption (MDS)
memoryConsumptionSAC=처리 메모리 사용별 상위 5개 MDS 요청
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=감사 로그 데이터
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=관리 데이터
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=기타 데이터
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=공간 내 데이터
#XFLD: ResourceCategory Available Memory
ResourceCategoryAvailableMemory=미사용 메모리
#XFLD: ResourceCategory Memory Used by Consumption
ResourceCategoryMemoryConsumption=처리에 사용된 메모리
#XFLD: ResourceCategory Memory Used by Replication
ResourceCategoryMemoryReplication=데이터 복제에 사용된 메모리
#XFLD: ResourceCategory unknown
unknown=알 수 없음
#XBUT: Card footer view list button
viewList=로그 보기
#NOTR: placeholder for dev-purposes
test=TEST
#XTIT: card bar label: hours
hours=시간
#XTIT: card subtitle: now
now=지금
#XTIT: card subtitle: last 24 hours
last24Hours=지난 24시간
#XTIT: card subtitle: last 48 hours
last48Hours=지난 48시간
#XTIT: card subtitle: last week
lastWeek=지난 7일
#XTIT: card subtitle: last month
lastMonth=지난 달
#XFLD: Close
close=닫기
#XFLD: Today
today=오늘
#XTIT: text for task tab
taskTab=태스크
#XTIT: text for statement tab
statementTab=명령문
#XTIT: text fo nav link in table cell
viewStatements=보기
#XTIT: text to more link in table cell
more=더 보기
#XTIT: text for statement dialog header
statementDlgTitle=명령문
#XBUT: text for close btn on dialog
closeBtn=닫기
#XBUT: text for copy btn on dialog
copyBtn=복사
#XTIT: Copy success text on toast msg
copiedToClipboard=클립보드에 복사됨
#XTIT: test for download btn on dialog
downloadBtn=다운로드

#XTIT: Monitoring table column names
startTime=시작 시간
startDate=시작일
duration=기간
objectType=오브젝트 유형
activity=액티비티
spaceName=공간 이름
objectName=오브젝트 이름
peakMemory=SAP HANA 최고 메모리
peakCPU=SAP HANA CPU 시간
noOfRecords=레코드
usedMemory=SAP HANA 사용된 메모리
usedDisk=SAP HANA 사용된 디스크
status=상태
subStatus=하위 상태
user=사용자
targetTable=대상 테이블
statements=명령문
outOfMemory=메모리 부족
taskLogId=태스크 로그 ID
statementDetails=명령문 세부사항
parameters=매개변수
workloadClass=작업 부하 클래스
errorCode=오류 코드
errorText=오류 메시지
dbUser=데이터베이스 사용자
connectionID=연결 ID
statementID=명령문 ID
elasticComputeNode=탄력적 컴퓨팅 노드


#Localization for @sap/dragonet-ui5-table-editor node module
#XTIT: Title for column settings dialog
columnsSettingsDialogTitle=열 설정
#XFLD: Title for Error type
messagesTableType=유형
#XFLD: Title for Error Message
messagesTableMessage=메시지
#XFLD: Title for filter
filteredBy=필터링 기준:
#XTIT: text for values contained in filter
filterContains=포함
#XTIT: text for values starting with in filter
filterStartsWith=다음으로 시작
#XTIT: text for values ending with in filter
filterEndsWith=다음으로 끝남
#XTIT: Title for search in data preview toolbar
toolbarSearch=검색
#XBUT: Button to clear filter
clearFilter=필터 지우기
#XBUT: Button to cancel the operation
cancel=취소
#XBUT: Button to save the operation
ok=확인
#XBUT: Button to restore the data
toolbarRestoreButton=새로 고침
#XBUT: Button to cancel running task
cancelBtnText=태스크 취소
#XBUT: tooltip for Button to restore the data
toolbarRestoreButtonTooltip=페이지 업데이트
#XMSG: Busy dialog for Columns Settings dialog
PROCESSING=처리 중...
#XMSG: Message Confirmation
confirmation=확인
#XMSG: Message for refresh successful
refreshSuccess=새로 고침 완료
#XMSG: Message for refresh successful
refreshSuccessful=새로 고침
#XMSG: Message for restore successful
restoreSuccessful=복원이 완료되었습니다.
#XMSG: Error message when null value found on non-nullable field
mdm-emptyValueError="{0}"을(를) 비워 두면 안 됩니다. {1}.
#XTIT: Sort Ascending
mdm-sortAscending=오름차순 정렬
#XTIT: Sort Descending
mdm-sortDescending=내림차순 정렬
#XTIT: Filter
mdm-Filter=필터
#XBUT: Button Cancel
mdm-cancel=취소
#XBUT: Button Add
mdm-Add=추가
#XMSG: and inside error message
and=및
#XMSG: Error message when no column selected
mdm-allColumnUnselected=계속하려면 열을 하나 이상 선택하십시오.

#Localization keys not needed for System Monitor but required for @sap/dragonet-ui5-table-editor node module to remove console errors
#XTXT: Message information strip for filtering and sorting
mdm-InfoStrip=저장되지 않은 변경사항으로 인해 필터, 정렬, 삭제 및 테이블 설정 툴이 비활성화되었습니다. 활성화하려면 변경사항을 저장하십시오.
#XBUT: Button for file upload in mdm data preview
toolbarUploadButton=데이터 업로드
#XBUT: Button for duplicating rows in mdm data preview
toolbarDuplicateButton=복제
#XBUT: Button for deleting rows in mdm data preview
toolbarDeleteButton=삭제
#XBUT: Button for adding rows in mdm data preview
toolbarAddButton=추가

#XTIT: MDM Empty value Handling in Table Editor Label
mdmInsertMissingString=누락된 문자열 값을 다음으로 삽입:
#XTIT: MDM Empty value Handling in Table Editor Null Combobox item
mdmNullText=NULL
#XTIT: MDM Empty value Handling in Table Editor Empty string Combobox item
mdmEmptyStringText=비어 있는 문자열
#XTXT: Message information strip for Insert Missing String Value
mdmInfoStripMissingString=누락 문자열 값은 신규 행과 편집된 행에서 표시 가능한 열에만 삽입됩니다 열 설정을 사용하여 모든 관련 열을 표시할 수 있습니다.
#XTXT: Message information strip for filtering sorting and Insert Missing String Value
mdm-InfoStripWithMissingString=저장되지 않은 변경사항으로 인해 필터, 정렬, 누락 문자열 값 삽입, 테이블 설정 툴이 비활성화되었습니다. 활성화하려면 변경사항을 저장하십시오.
#XFLD: Open SAP HANA Cockpit
openHanaCockpit=데이터베이스 개요(SAP HANA 콕피트)
#XBUT: Open SAP HANA Performance Monitor Cockpit
openHanaCockpitPerfMonitor=성능 모니터
openHanaCockpitPerfMonitorTooltip=SAP HANA 콕피트에서 성능 모니터 열기
#XTIT: card title: Admission Control Rejection Events
acRejectedEvents=허용 제어 거부 이벤트
#XTIT: card title: Admission Control Queuing Events
acQueuedEvents=허용 제어 대기 이벤트

#XTIT: card title: Top 5 Admission Control Rejection Events by Space
acRejectionEventsBySpace=공간별 허용 제어 상위 5개 거부 이벤트
#XTIT: card title: Top 5 Admission Control Queuing Events by Space
acQueueingEventsBySpace=공간별 허용 제어 상위 5개 대기 이벤트

#XTIT: payload Table and List texts
payloadDownloadText=페이로드 다운로드됨
dataSourceText=데이터 소스
dataSourcesObject=이름
dataSourcesSchema=공간 이름
payloadListHeader=페이로드
payloadStoryIdText=스토리 ID
payloadStoryNameText=스토리 이름
payloadDialogTitleText=추가 정보
payloadTextAreaTitle=페이로드
NO_MDS_DETAIL=사용 가능한 데이터 없음
MDS_DATA_FETCH_FAILED=서버 오류로 인해 MDS 문에 정보를 표시할 수 없습니다.
GBUnit=GB
enabled=사용
notEnabled=사용할 수 없음
#XTIT: ECN Monitoring Dashboard Texts
ecnStatus=상태
ecnBlock=컴퓨팅 블록
ecnSize=크기
ecnConfigMemory=메모리
ecnConfigStorage=저장소
ecnConfigSpacesList=공간 리스트
#XFLD: ECN phase Ready
ecnReady=준비됨
#XFLD: ECN phase Running
ecnRunning=실행 중
#XFLD: ECN phase Initial
ecnInitial=준비 중
#XFLD: ECN phase Starting
ecnStarting=시작
#XFLD: ECN phase Stopping
ecnStopping=중지
#XFLD: ECN phase Unknown
ecnUnknown=알 수 없음
#XFLD: ECN Last Run Details
lastRun=최종 실행
#XFLD: ECN Previous Run Details
previousRun=이전 실행
#XFLD: ECN Manage Button
manage=탄력적 계산 노드 관리
#XFLD: ECN Run Details Widget
days=일
#XFLD: ECN Run Details Widget Title
runDetails=실행 세부사항
#XFLD: ECN Performance Class Label
performanceClass=성능 클래스
start=시작
stop=중지
ecnRunDetailsUpTime=업타임
ecnConfigurationCPU=vCPU 수
ecnTechnicalName=기술적 이름
currentMonth=현재 월
previousMonth=이전 월
ecnBlockHours=블록 시간
upTimeOverall=총 업타임
refresh=새로 고침
top5MemConsumptionDataFetchErrorText=메모리 사용에 대한 데이터를 가져오지 못함
ecnDashboardText=탄력적 계산 노드
#XMSG: Message Title when ECN is not in ruuning state
memoryDistributionErrorTitle=해당 없음
#XMSG: Warning Message when ECN is not in ruuning state
memoryDistributionErrorMessage=메모리 분배는 탄력적 계산 노드가 실행 중일 때만 표시됩니다.
#XMSG: Title for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageTitle=탄력적 계산 노드를 선택하지 않음
#XMSG: Description for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageDescription=헤더의 리스트에서 하나를 선택하여 시작하십시오.
#XMSG: Tab label for Statememt Logs
statementLogsText=명령문 로그
#XMSG: Tab label for Task Logs
taskLogsText=태스크 로그
#MSG: Label for Date Time Quick Filter
labelForTimeQuickFilter=일자 및 시간 범위:
labelForSpaceQuickFilter=공간
labelForStatusQuickFilter=상태
#XTIT: Cancel Task Started toast msg
cancelTaskStarted=태스크 취소가 시작되었습니다.
#YMSE: Cancel Task Error
taskCancelError=태스크가 이미 실행 종료되었으므로 취소될 수 없습니다.
#LSA Monitor Tab Name
lsaMonitorText=오브젝트 저장소
#ID for LSA Storage By Space Widget
hdlfStorageSpace=SAP HANA 데이터 레이크 파일: 저장 사용률
#ID for LSA Storage for All Spaces Widget
hdlfStorageSpaceTotal=SAP HANA 데이터 레이크 파일: 모든 공간의 저장 사용률
#Labels for LSA Monitor
lsaSparkTableApplicationColumnText=어플리케이션
lsaSparkTableNoOfTasksColumnText=태스크 수
sparkApplicationConfigurationTitle=어플리케이션 구성
sparkExecutorCPU=실행자 CPU
sparkExecutorMemory=실행자 메모리
sparkDriverCPU=드라이버 CPU
sparkDriverMemory=드라이버 메모리
sparkMaximumCPU=최대 CPU
sparkMaximumMemory=최대 메모리
sparkMinExecutors=최소 실행자
sparkMaxExecutors=최대 실행자
sparkAppTableNoDataIMTitle=선택된 공간 없음
sparkAppTableNoDataIMDesc=필터 바의 리스트에서 하나를 선택하여 시작하십시오.
TBUnit=TB
sparkTaskTableNoDataIMTitle=사용 가능한 태스크 없음
sparkTaskTableNoDataIMDesc=선택한 어플리케이션에는 사용 가능한 태스크가 없습니다. 다른 어플리케이션을 선택하십시오.
taskActivity=태스크 액티비티
sparkTableNoSearchResultsTitle=검색 결과 없음
sparkTableNoSearchResultsDesc=검색어를 변경하고 다시 시도하십시오.
sparkTableNoFilterResultsTitle=필터 결과 없음
sparkTableNoFilterResultsDesc=필터 기준을 변경하고 다시 시도하십시오.
removeFilterText=필터 지우기
sortTableText=테이블 정렬
filterTableText=테이블 필터링
apacheSparkTableText=Apache Spark: 태스크
closeColumnText=열 닫기
