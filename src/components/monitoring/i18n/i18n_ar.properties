#XTIT: Dynamic Page title
pageTitle=مرحبًا بك في مراقبة النظام
#XTIT: Dynamic Page subtitle
pageSubtitle=راقب أداء نظامك وحدد مشكلات التخزين والمهام ونفاد الذاكرة وغيرها من المشكلات.
#XTIT: Messagestrip Enable Statement tracing
messagestripEnableStatementTracing=لمراقبة أخطاء نفاد الذاكرة ومعلومات العبارات الأخرى، يُرجى تمكين تتبع العبارات المستهلكة للوقت في التكوين/المراقبة.
#XFLD: Dashboard text
dashBoardText=لوحة المعلومات
#XFLD: TaskLog text
taskLogText=السجلات
#XTIT: card title: Storage Distribution
storageDistribution=تخزين القرص المستخدَم
#XTIT: card title: Memory Distribution
memoryDistribution=توزيع الذاكرة
#XTIT: card title:Used Disk Capacity
usedStorageCapacity=القرص المستخدم من المساحات للتخزين
#XTIT: card title: Used Memory Capacity
usedMemoryCapacity=الذاكرة المستخدمة من المساحات للتخزين
#XTIT: card title:Assigned Disk Capacity
assignedStorageCapacity=القرص المُعيَّن إلى المساحات للتخزين
#XTIT: card title: Assigned Memory Capacity
assignedMemoryCapacity=الذاكرة المُعيَّنة إلى المساحات للتخزين
#XTIT: card title: Errors
errors=المهام الفاشلة
#XTIT: card title: Configuration
ecnConfiguration=التكوين
#XTIT: card title: ECN Monthly Uptime
upTime=فترة التشغيل الشهرية
#XTIT: card title: ECN Average Memory
ECNAvgMemory=متوسط الذاكرة
#XTIT: card title: ECN Average CPU
ECNAvgCPU=متوسط وحدة المعالجة المركزية
#XTIT: card Sub title: ECN Hours Subtitle
ECNHoursSubtitle=آخر {0} من الساعات
#XTIT: card Title: ECN Last 5 Start Tasks
ecnTop5StartStop=آخر 5 مهام بدء/إيقاف ECN
#XTIT: card Title: ECN Last 5 Stop Tasks
ecnTop5Stop=آخر 5 مهام إيقاف ECN
#XTIT: card title: Out of Memory Events (all)
OoMEvents=أخطاء في نفاد الذاكرة
#XTIT: card title: MDS
OoMMDSQueries=أخطاء في نفاد الذاكرة (طلبات MDS)
#XTIT: card title: Top 5 Memory Consumption (Statements)
memoryConsumptionStatements=أعلى 5 عبارات حسب استهلاك ذاكرة المعالجة
#XTIT: card title: Out of Memory Events (Workload Class) by Space
OoMEventsByWorkloadClass=أعلى 5 أخطاء في نفاد الذاكرة (صنف حمل العمل) حسب المساحة
#XTIT: card title: Run Duration
runDuration=أعلى 5 مهام حسب مدة التشغيل
#XTIT: card title: Top 5 Tasks by Memory Consumption
tasksBymemoryConsumption=أعلى 5 مهام حسب استهلاك ذاكرة المعالجة
#XTIT: card title: Memory Consumption
memoryConsumption=استهلاك الذاكرة
#XTIT: card title: Top 5 Memory Consumption (MDS)
memoryConsumptionSAC=أعلى 5 طلبات خدمات متعددة الأبعاد (MDS) حسب استهلاك ذاكرة المعالجة
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=بيانات سجل التدقيق
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=البيانات الإدارية
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=بيانات أخرى
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=البيانات في المساحات
#XFLD: ResourceCategory Available Memory
ResourceCategoryAvailableMemory=الذاكرة غير المستخدمة
#XFLD: ResourceCategory Memory Used by Consumption
ResourceCategoryMemoryConsumption=الذاكرة المستخدمة للمعالجة
#XFLD: ResourceCategory Memory Used by Replication
ResourceCategoryMemoryReplication=الذاكرة المستخدمة للنسخ المتماثل للبيانات
#XFLD: ResourceCategory unknown
unknown=غير معروف
#XBUT: Card footer view list button
viewList=عرض السجلات
#NOTR: placeholder for dev-purposes
test=TEST
#XTIT: card bar label: hours
hours=ساعات
#XTIT: card subtitle: now
now=الآن
#XTIT: card subtitle: last 24 hours
last24Hours=آخر 24 ساعة
#XTIT: card subtitle: last 48 hours
last48Hours=آخر 48 ساعة
#XTIT: card subtitle: last week
lastWeek=آخر 7 يومًا
#XTIT: card subtitle: last month
lastMonth=الشهر الماضي
#XFLD: Close
close=إغلاق
#XFLD: Today
today=اليوم
#XTIT: text for task tab
taskTab=المهام
#XTIT: text for statement tab
statementTab=العبارات
#XTIT: text fo nav link in table cell
viewStatements=عرض
#XTIT: text to more link in table cell
more=المزيد
#XTIT: text for statement dialog header
statementDlgTitle=العبارة
#XBUT: text for close btn on dialog
closeBtn=إغلاق
#XBUT: text for copy btn on dialog
copyBtn=نسخ
#XTIT: Copy success text on toast msg
copiedToClipboard=منسوخ إلى الحافظة
#XTIT: test for download btn on dialog
downloadBtn=تنزيل

#XTIT: Monitoring table column names
startTime=وقت البدء
startDate=تاريخ البدء
duration=المدة
objectType=نوع الكائن
activity=النشاط
spaceName=اسم المساحة
objectName=اسم الكائن
peakMemory=أقصى ذاكرة لـ SAP HANA
peakCPU=وقت وحدة المعالجة المركزية لقاعدة البيانات SAP HANA
noOfRecords=السجلات
usedMemory=الذاكرة المستخدمة لـ SAP HANA
usedDisk=القرص المستخدم لـ SAP HANA
status=الحالة
subStatus=الحالة الفرعية
user=المستخدم
targetTable=الجدول المستهدف
statements=العبارات
outOfMemory=نفاد الذاكرة
taskLogId=معرف سجل المهام
statementDetails=تفاصيل كشف الحساب
parameters=المعامِلات
workloadClass=صنف حمل العمل
errorCode=رمز الخطأ
errorText=رسالة الخطأ
dbUser=مستخدم قاعدة البيانات
connectionID=معرف الاتصال
statementID=معرف العبارة
elasticComputeNode=عقدة حوسبة مرنة


#Localization for @sap/dragonet-ui5-table-editor node module
#XTIT: Title for column settings dialog
columnsSettingsDialogTitle=إعدادات الأعمدة
#XFLD: Title for Error type
messagesTableType=النوع
#XFLD: Title for Error Message
messagesTableMessage=الرسالة
#XFLD: Title for filter
filteredBy=تمت التصفية حسب:
#XTIT: text for values contained in filter
filterContains=يحتوي على
#XTIT: text for values starting with in filter
filterStartsWith=يبدأ بـ
#XTIT: text for values ending with in filter
filterEndsWith=ينتهي بـ
#XTIT: Title for search in data preview toolbar
toolbarSearch=بحث
#XBUT: Button to clear filter
clearFilter=مسح عامل التصفية
#XBUT: Button to cancel the operation
cancel=إلغاء
#XBUT: Button to save the operation
ok=موافق
#XBUT: Button to restore the data
toolbarRestoreButton=تحديث
#XBUT: Button to cancel running task
cancelBtnText=إلغاء المهمة
#XBUT: tooltip for Button to restore the data
toolbarRestoreButtonTooltip=تحديث الصفحة
#XMSG: Busy dialog for Columns Settings dialog
PROCESSING=جارٍ المعالجة...
#XMSG: Message Confirmation
confirmation=التأكيد
#XMSG: Message for refresh successful
refreshSuccess=تم التحديث بنجاح
#XMSG: Message for refresh successful
refreshSuccessful=تم التحديث
#XMSG: Message for restore successful
restoreSuccessful=تمت الاستعادة بنجاح
#XMSG: Error message when null value found on non-nullable field
mdm-emptyValueError="{0}" لا يمكن أن يكون فارغًا. {1}.
#XTIT: Sort Ascending
mdm-sortAscending=ترتيب تصاعدي
#XTIT: Sort Descending
mdm-sortDescending=ترتيب تنازلي
#XTIT: Filter
mdm-Filter=عامل التصفية
#XBUT: Button Cancel
mdm-cancel=إلغاء
#XBUT: Button Add
mdm-Add=إضافة
#XMSG: and inside error message
and=و
#XMSG: Error message when no column selected
mdm-allColumnUnselected=حدد عمودًا واحدًا أو أكثر للمتابعة.

#Localization keys not needed for System Monitor but required for @sap/dragonet-ui5-table-editor node module to remove console errors
#XTXT: Message information strip for filtering and sorting
mdm-InfoStrip=يتم تعطيل أدوات إعداد الجدول والحذف والترتيب والتصفية بواسطة التغييرات غير المحفوظة. احفظ تغييراتك لتمكينها.
#XBUT: Button for file upload in mdm data preview
toolbarUploadButton=تحميل بيانات
#XBUT: Button for duplicating rows in mdm data preview
toolbarDuplicateButton=تكرار
#XBUT: Button for deleting rows in mdm data preview
toolbarDeleteButton=حذف
#XBUT: Button for adding rows in mdm data preview
toolbarAddButton=إضافة

#XTIT: MDM Empty value Handling in Table Editor Label
mdmInsertMissingString=إدراج قيمة سلسلة مفقودة كـ:
#XTIT: MDM Empty value Handling in Table Editor Null Combobox item
mdmNullText=خالٍ
#XTIT: MDM Empty value Handling in Table Editor Empty string Combobox item
mdmEmptyStringText=سلسلة فارغة
#XTXT: Message information strip for Insert Missing String Value
mdmInfoStripMissingString=يتم إدراج قيم السلسلة المفقودة فقط في أعمدة السلسلة المرئية في الصفوف الجديدة والمحررة. استخدم إعدادات الأعمدة لعرض جميع الأعمدة ذات الصلة.
#XTXT: Message information strip for filtering sorting and Insert Missing String Value
mdm-InfoStripWithMissingString=يتم تعطيل أدوات إعداد الجدول وتصفية قيمة السلسلة المفقودة وترتيبها وإدراجها بواسطة التغييرات غير المحفوظة. احفظ تغييراتك لتمكينها.
#XFLD: Open SAP HANA Cockpit
openHanaCockpit=نظرة عامة على قاعدة البيانات (وحدة تحكم SAP HANA)
#XBUT: Open SAP HANA Performance Monitor Cockpit
openHanaCockpitPerfMonitor=مراقبة الأداء
openHanaCockpitPerfMonitorTooltip=فتح مراقبة الأداء في وحدة تحكم SAP HANA
#XTIT: card title: Admission Control Rejection Events
acRejectedEvents=أحداث الرفض للتحكم بالدخول
#XTIT: card title: Admission Control Queuing Events
acQueuedEvents=أحداث في قائمة الانتظار للتحكم بالدخول

#XTIT: card title: Top 5 Admission Control Rejection Events by Space
acRejectionEventsBySpace=أهم 5 أحداث رفض للتحكم بالدخول حسب المساحة
#XTIT: card title: Top 5 Admission Control Queuing Events by Space
acQueueingEventsBySpace=أهم 5 أحداث في قائمة الانتظار للتحكم بالدخول حسب المساحة

#XTIT: payload Table and List texts
payloadDownloadText=تم تنزيل حمل البيانات
dataSourceText=مصادر البيانات
dataSourcesObject=الاسم
dataSourcesSchema=اسم المساحة
payloadListHeader=أحمال البيانات
payloadStoryIdText=معرف السجل
payloadStoryNameText=اسم القصة
payloadDialogTitleText=مزيد من المعلومات
payloadTextAreaTitle=حمل البيانات
NO_MDS_DETAIL=لا تتوفر بيانات
MDS_DATA_FETCH_FAILED=تعذر علينا عرض معلومات على عبارات MDS بسبب خطأ في الخادم.
GBUnit=جيجا بايت
enabled=ممكّن
notEnabled=غير ممكَّن
#XTIT: ECN Monitoring Dashboard Texts
ecnStatus=الحالة
ecnBlock=مجموعات الحوسبة
ecnSize=الحجم
ecnConfigMemory=الذاكرة
ecnConfigStorage=التخزين
ecnConfigSpacesList=قائمة المساحات
#XFLD: ECN phase Ready
ecnReady=جاهز
#XFLD: ECN phase Running
ecnRunning=قيد التشغيل
#XFLD: ECN phase Initial
ecnInitial=غير جاهز
#XFLD: ECN phase Starting
ecnStarting=بدء
#XFLD: ECN phase Stopping
ecnStopping=جارٍ الإيقاف
#XFLD: ECN phase Unknown
ecnUnknown=غير معروف
#XFLD: ECN Last Run Details
lastRun=آخر تشغيل
#XFLD: ECN Previous Run Details
previousRun=التشغيل السابق
#XFLD: ECN Manage Button
manage=إدارة عقدة الاحتساب المرنة
#XFLD: ECN Run Details Widget
days=أيام
#XFLD: ECN Run Details Widget Title
runDetails=تفاصيل التشغيل
#XFLD: ECN Performance Class Label
performanceClass=صف الأداء
start=بدء
stop=إيقاف
ecnRunDetailsUpTime=فترة التشغيل
ecnConfigurationCPU=عدد وحدات المعالجة المركزية الافتراضية
ecnTechnicalName=الاسم التقني
currentMonth=الشهر الحالي
previousMonth=الشهر السابق
ecnBlockHours=ساعات الرحلة
upTimeOverall=إجمالي فترة التشغيل
refresh=تحديث
top5MemConsumptionDataFetchErrorText=فشل الحصول على بيانات استهلاك الذاكرة
ecnDashboardText=عُقد الاحتساب المرنة
#XMSG: Message Title when ECN is not in ruuning state
memoryDistributionErrorTitle=غير قابل للتطبيق
#XMSG: Warning Message when ECN is not in ruuning state
memoryDistributionErrorMessage=يتم عرض توزيع الذاكرة فقط عندما تكون عقدة الحوسبة المرنة في حالة تشغيل.
#XMSG: Title for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageTitle=لم يتم تحديد عقدة الاحتساب المرنة
#XMSG: Description for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageDescription=ابدأ بتحديد عقدة واحدة من القائمة في المقدمة.
#XMSG: Tab label for Statememt Logs
statementLogsText=سجلات العبارات
#XMSG: Tab label for Task Logs
taskLogsText=سجلات المهام
#MSG: Label for Date Time Quick Filter
labelForTimeQuickFilter=نطاق التاريخ والوقت:
labelForSpaceQuickFilter=المساحات
labelForStatusQuickFilter=الحالات
#XTIT: Cancel Task Started toast msg
cancelTaskStarted=تم بدء إلغاء المهمة
#YMSE: Cancel Task Error
taskCancelError=لا يمكن إلغاء المهمة لأنها انتهت بالفعل من التشغيل.
#LSA Monitor Tab Name
lsaMonitorText=مخزن الكائنات
#ID for LSA Storage By Space Widget
hdlfStorageSpace=ملفات مستودع بيانات SAP HANA: استخدام التخزين
#ID for LSA Storage for All Spaces Widget
hdlfStorageSpaceTotal=ملفات مستودع بيانات SAP HANA: استخدام تخزين جميع المساحات
#Labels for LSA Monitor
lsaSparkTableApplicationColumnText=التطبيق
lsaSparkTableNoOfTasksColumnText=عدد المهام
sparkApplicationConfigurationTitle=تكوين التطبيق
sparkExecutorCPU=وحدة المعالجة المركزية للمنفِّذ
sparkExecutorMemory=ذاكرة المنفِّذ
sparkDriverCPU=وحدة المعالجة المركزية للمشغِّل
sparkDriverMemory=ذاكرة المشغِّل
sparkMaximumCPU=الحد الأقصى لوحدة المعالجة المركزية
sparkMaximumMemory=الحد الأقصى للذاكرة
sparkMinExecutors=الحد الأدنى للمنفِّذين
sparkMaxExecutors=الحد الأقصى للمنفِّذين
sparkAppTableNoDataIMTitle=لم يتم تحديد مساحة
sparkAppTableNoDataIMDesc=ابدأ بتحديد واحد من القائمة في شريط عوامل التصفية.
TBUnit=تيرابايت
sparkTaskTableNoDataIMTitle=لا تتوفر أي مهام
sparkTaskTableNoDataIMDesc=لا تتوفر أي مهام للتطبيق المحدد. حدد تطبيقًا آخر.
taskActivity=نشاط المهمة
sparkTableNoSearchResultsTitle=لا توجد نتائج بحث
sparkTableNoSearchResultsDesc=قم بتغيير مصطلح البحث وحاول مرة أخرى.
sparkTableNoFilterResultsTitle=لا توجد نتائج تصفية
sparkTableNoFilterResultsDesc=قم بتغيير معايير التصفية وحاول مرة أخرى.
removeFilterText=مسح عامل التصفية
sortTableText=ترتيب الجدول
filterTableText=عامل التصفية
apacheSparkTableText=Apache Spark: المهام
closeColumnText=إغلاق العمود
