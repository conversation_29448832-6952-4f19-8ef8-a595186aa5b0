#XTIT: Dynamic Page title
pageTitle=Chào mừng bạn đến với Giám sát hệ thống
#XTIT: Dynamic Page subtitle
pageSubtitle=Giám sát hiệu suất hệ thống của bạn và xác định các vấn đề lưu trữ, tá<PERSON> vụ, hết bộ nhớ và các vấn đề khác.
#XTIT: Messagestrip Enable Statement tracing
messagestripEnableStatementTracing=Để giám sát các lỗi hết bộ nhớ và thông tin câu lệnh khác, vui lòng bật tính năng theo dõi báo cáo chi phí trong Cấu hình/Giám sát.
#XFLD: Dashboard text
dashBoardText=Bảng điều khiển
#XFLD: TaskLog text
taskLogText=Nhật ký
#XTIT: card title: Storage Distribution
storageDistribution=Mức lưu trữ đĩa đã sử dụng
#XTIT: card title: Memory Distribution
memoryDistribution=Phân bổ bộ nhớ
#XTIT: card title:Used Disk Capacity
usedStorageCapacity=<PERSON><PERSON>a được vùng dữ liệu sử dụng để lưu trữ
#XTIT: card title: Used Memory Capacity
usedMemoryCapacity=Bộ nhớ được vùng dữ liệu sử dụng để lưu trữ
#XTIT: card title:Assigned Disk Capacity
assignedStorageCapacity=Đĩa được gán cho vùng dữ liệu để lưu trữ
#XTIT: card title: Assigned Memory Capacity
assignedMemoryCapacity=Bộ nhớ được gán cho vùng dữ liệu để lưu trữ
#XTIT: card title: Errors
errors=Tác vụ không thành công
#XTIT: card title: Configuration
ecnConfiguration=Cấu hình
#XTIT: card title: ECN Monthly Uptime
upTime=Thời gian hoạt động liên tục hàng tháng
#XTIT: card title: ECN Average Memory
ECNAvgMemory=Bộ nhớ trung bình
#XTIT: card title: ECN Average CPU
ECNAvgCPU=CPU trung bình
#XTIT: card Sub title: ECN Hours Subtitle
ECNHoursSubtitle={0} giờ cuối
#XTIT: card Title: ECN Last 5 Start Tasks
ecnTop5StartStop=5 tác vụ bắt đầu/dừng cuối ECN
#XTIT: card Title: ECN Last 5 Stop Tasks
ecnTop5Stop=5 tác vụ dừng cuối ECN
#XTIT: card title: Out of Memory Events (all)
OoMEvents=Lỗi hết bộ nhớ
#XTIT: card title: MDS
OoMMDSQueries=Lỗi hết bộ nhớ (Yêu cầu MDS)
#XTIT: card title: Top 5 Memory Consumption (Statements)
memoryConsumptionStatements=5 câu lệnh hàng đầu theo mức tiêu thụ bộ nhớ xử lý
#XTIT: card title: Out of Memory Events (Workload Class) by Space
OoMEventsByWorkloadClass=5 lỗi hết bộ nhớ hàng đầu (Lớp khối lượng công việc) theo vùng dữ liệu
#XTIT: card title: Run Duration
runDuration=5 tác vụ hàng đầu theo thời lượng chạy
#XTIT: card title: Top 5 Tasks by Memory Consumption
tasksBymemoryConsumption=5 tác vụ hàng đầu theo mức tiêu thụ bộ nhớ xử lý
#XTIT: card title: Memory Consumption
memoryConsumption=Tiêu thụ bộ nhớ
#XTIT: card title: Top 5 Memory Consumption (MDS)
memoryConsumptionSAC=5 yêu cầu MDS hàng đầu theo mức tiêu thụ bộ nhớ xử lý
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Kiểm tra dữ liệu nhật ký
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Dữ liệu quản trị
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Dữ liệu khác
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Dữ liệu trong vùng dữ liệu
#XFLD: ResourceCategory Available Memory
ResourceCategoryAvailableMemory=Bộ nhớ chưa sử dụng
#XFLD: ResourceCategory Memory Used by Consumption
ResourceCategoryMemoryConsumption=Bộ nhớ được sử dụng để xử lý
#XFLD: ResourceCategory Memory Used by Replication
ResourceCategoryMemoryReplication=Bộ nhớ được sử dụng để sao chép dữ liệu
#XFLD: ResourceCategory unknown
unknown=Không xác định
#XBUT: Card footer view list button
viewList=Xem nhật ký
#NOTR: placeholder for dev-purposes
test=TEST
#XTIT: card bar label: hours
hours=Giờ
#XTIT: card subtitle: now
now=Bây giờ
#XTIT: card subtitle: last 24 hours
last24Hours=24 giờ qua
#XTIT: card subtitle: last 48 hours
last48Hours=48 giờ qua
#XTIT: card subtitle: last week
lastWeek=7 ngày trước
#XTIT: card subtitle: last month
lastMonth=Tháng trước
#XFLD: Close
close=Đóng
#XFLD: Today
today=Hôm nay
#XTIT: text for task tab
taskTab=Tác vụ
#XTIT: text for statement tab
statementTab=Câu lệnh
#XTIT: text fo nav link in table cell
viewStatements=Màn hình
#XTIT: text to more link in table cell
more=Thêm
#XTIT: text for statement dialog header
statementDlgTitle=Câu lệnh
#XBUT: text for close btn on dialog
closeBtn=Đóng
#XBUT: text for copy btn on dialog
copyBtn=Sao chép
#XTIT: Copy success text on toast msg
copiedToClipboard=Được sao chép vào Clipboard
#XTIT: test for download btn on dialog
downloadBtn=Tải xuống

#XTIT: Monitoring table column names
startTime=Thời gian bắt đầu
startDate=Ngày bắt đầu
duration=Khoảng thời gian
objectType=Kiểu đối tượng
activity=Hoạt động
spaceName=Tên vùng dữ liệu
objectName=Tên đối tượng
peakMemory=Bộ nhớ cao nhất của SAP HANA
peakCPU=SAP HANA, thời gian CPU
noOfRecords=Bản ghi
usedMemory=Bộ nhớ đã sử dụng của SAP HANA
usedDisk=Ổ đĩa đã sử dụng của SAP HANA
status=Trạng thái
subStatus=Trạng thái phụ
user=Người dùng
targetTable=Bảng đích
statements=Câu lệnh
outOfMemory=Hết bộ nhớ
taskLogId=ID nhật ký tác vụ
statementDetails=Chi tiết báo cáo
parameters=Tham số
workloadClass=Lớp khối lượng công việc
errorCode=Mã lỗi
errorText=Thông báo lỗi
dbUser=Người dùng cơ sở dữ liệu
connectionID=ID kết nối
statementID=ID câu lệnh
elasticComputeNode=Nút Elastic Compute


#Localization for @sap/dragonet-ui5-table-editor node module
#XTIT: Title for column settings dialog
columnsSettingsDialogTitle=Thiết lập cột
#XFLD: Title for Error type
messagesTableType=Kiểu
#XFLD: Title for Error Message
messagesTableMessage=Thông báo
#XFLD: Title for filter
filteredBy=Được lọc theo:
#XTIT: text for values contained in filter
filterContains=bao gồm
#XTIT: text for values starting with in filter
filterStartsWith=bắt đầu với
#XTIT: text for values ending with in filter
filterEndsWith=kết thúc với
#XTIT: Title for search in data preview toolbar
toolbarSearch=Tìm kiếm
#XBUT: Button to clear filter
clearFilter=Xóa bộ lọc
#XBUT: Button to cancel the operation
cancel=Hủy
#XBUT: Button to save the operation
ok=OK
#XBUT: Button to restore the data
toolbarRestoreButton=Làm mới
#XBUT: Button to cancel running task
cancelBtnText=Hủy tác vụ
#XBUT: tooltip for Button to restore the data
toolbarRestoreButtonTooltip=Cập nhật trang
#XMSG: Busy dialog for Columns Settings dialog
PROCESSING=Đang xử lý...
#XMSG: Message Confirmation
confirmation=Xác nhận
#XMSG: Message for refresh successful
refreshSuccess=Đã làm mới thành công
#XMSG: Message for refresh successful
refreshSuccessful=Đã làm mới
#XMSG: Message for restore successful
restoreSuccessful=Được khôi phục thành công
#XMSG: Error message when null value found on non-nullable field
mdm-emptyValueError="{0}" không thể trống. {1}.
#XTIT: Sort Ascending
mdm-sortAscending=Sắp xếp tăng
#XTIT: Sort Descending
mdm-sortDescending=Sắp xếp giảm
#XTIT: Filter
mdm-Filter=Bộ lọc
#XBUT: Button Cancel
mdm-cancel=Hủy
#XBUT: Button Add
mdm-Add=Thêm
#XMSG: and inside error message
and=và
#XMSG: Error message when no column selected
mdm-allColumnUnselected=Chọn một hoặc nhiều cột để tiếp tục.

#Localization keys not needed for System Monitor but required for @sap/dragonet-ui5-table-editor node module to remove console errors
#XTXT: Message information strip for filtering and sorting
mdm-InfoStrip=Công cụ lọc, sắp xếp, xóa và thiết lập bảng bị tắt bởi các thay đổi chưa lưu. Lưu các thay đổi để bật chúng.
#XBUT: Button for file upload in mdm data preview
toolbarUploadButton=Tải lên dữ liệu
#XBUT: Button for duplicating rows in mdm data preview
toolbarDuplicateButton=Sao chép
#XBUT: Button for deleting rows in mdm data preview
toolbarDeleteButton=Xóa
#XBUT: Button for adding rows in mdm data preview
toolbarAddButton=Thêm

#XTIT: MDM Empty value Handling in Table Editor Label
mdmInsertMissingString=Chèn giá trị chuỗi bị thiếu dưới dạng:
#XTIT: MDM Empty value Handling in Table Editor Null Combobox item
mdmNullText=RỖNG
#XTIT: MDM Empty value Handling in Table Editor Empty string Combobox item
mdmEmptyStringText=Chuỗi trống
#XTXT: Message information strip for Insert Missing String Value
mdmInfoStripMissingString=Chỉ có thể chèn giá trị chuỗi bị thiếu vào cột chuỗi hiển thị trong hàng mới và được hiệu chỉnh. Sử dụng Thiết lập cột để hiển thị tất cả cột liên quan.
#XTXT: Message information strip for filtering sorting and Insert Missing String Value
mdm-InfoStripWithMissingString=Công cụ Lọc, Sắp xếp, Chèn giá trị chuỗi và Thiết lập bảng bị vô hiệu hóa bởi các thay đổi chưa lưu. Lưu các thay đổi để bật chúng.
#XFLD: Open SAP HANA Cockpit
openHanaCockpit=Tổng quan cơ sở dữ liệu (SAP HANA Cockpit)
#XBUT: Open SAP HANA Performance Monitor Cockpit
openHanaCockpitPerfMonitor=Giám sát hiệu suất
openHanaCockpitPerfMonitorTooltip=Mở giám sát hiệu suất trong SAP HANA Cockpit
#XTIT: card title: Admission Control Rejection Events
acRejectedEvents=Kiểm soát thu nhận: Sự kiện từ chối
#XTIT: card title: Admission Control Queuing Events
acQueuedEvents=Kiểm soát thu nhận: Sự kiện hàng đợi

#XTIT: card title: Top 5 Admission Control Rejection Events by Space
acRejectionEventsBySpace=5 sự kiện từ chối kiểm soát thu nhận hàng đầu theo vùng dữ liệu
#XTIT: card title: Top 5 Admission Control Queuing Events by Space
acQueueingEventsBySpace=5 sự kiện hàng đợi kiểm soát thu nhận hàng đầu theo vùng dữ liệu

#XTIT: payload Table and List texts
payloadDownloadText=Trọng tải được tải xuống
dataSourceText=Nguồn dữ liệu
dataSourcesObject=Tên
dataSourcesSchema=Tên vùng dữ liệu
payloadListHeader=Trọng tải
payloadStoryIdText=ID tập hợp
payloadStoryNameText=Tên tập hợp
payloadDialogTitleText=Thông tin khác
payloadTextAreaTitle=Trọng tải
NO_MDS_DETAIL=Không có sẵn dữ liệu
MDS_DATA_FETCH_FAILED=Chúng tôi không thể hiển thị thông tin trên báo cáo MDS do lỗi máy chủ.
GBUnit=GB
enabled=Đã bật
notEnabled=Chưa bật
#XTIT: ECN Monitoring Dashboard Texts
ecnStatus=Trạng thái
ecnBlock=Tính toán khối
ecnSize=Kích cỡ
ecnConfigMemory=Bộ nhớ
ecnConfigStorage=Lưu trữ
ecnConfigSpacesList=Danh sách vùng dữ liệu
#XFLD: ECN phase Ready
ecnReady=Sẵn sàng
#XFLD: ECN phase Running
ecnRunning=Chạy
#XFLD: ECN phase Initial
ecnInitial=Chưa sẵn sàng
#XFLD: ECN phase Starting
ecnStarting=Bắt đầu
#XFLD: ECN phase Stopping
ecnStopping=Đang dừng
#XFLD: ECN phase Unknown
ecnUnknown=Không xác định
#XFLD: ECN Last Run Details
lastRun=Lần chạy gần nhất
#XFLD: ECN Previous Run Details
previousRun=Thực hiện lần trước
#XFLD: ECN Manage Button
manage=Quản lý nút tính toán linh hoạt
#XFLD: ECN Run Details Widget
days=Ngày
#XFLD: ECN Run Details Widget Title
runDetails=Chi tiết thực hiện
#XFLD: ECN Performance Class Label
performanceClass=Lớp hiệu suất
start=Bắt đầu
stop=Dừng
ecnRunDetailsUpTime=Thời gian hoạt động liên tục
ecnConfigurationCPU=Số lượng vCPU
ecnTechnicalName=Tên kỹ thuật
currentMonth=Tháng hiện tại
previousMonth=Tháng trước
ecnBlockHours=Khối giờ
upTimeOverall=Tổng thời gian hoạt động liên tục
refresh=Làm mới
top5MemConsumptionDataFetchErrorText=Không thể lấy dữ liệu sử dụng bộ nhớ
ecnDashboardText=Nút tính toán linh hoạt
#XMSG: Message Title when ECN is not in ruuning state
memoryDistributionErrorTitle=Không áp dụng
#XMSG: Warning Message when ECN is not in ruuning state
memoryDistributionErrorMessage=Phân bổ bộ nhớ chỉ được hiển thị khi nút tính toán linh hoạt ở trạng thái đang chạy.
#XMSG: Title for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageTitle=Không chọn nút tính toán linh hoạt
#XMSG: Description for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageDescription=Bắt đầu bằng cách chọn một nút từ danh sách trong tiêu đề.
#XMSG: Tab label for Statememt Logs
statementLogsText=Nhật ký câu lệnh
#XMSG: Tab label for Task Logs
taskLogsText=Nhật ký tác vụ
#MSG: Label for Date Time Quick Filter
labelForTimeQuickFilter=Khoảng ngày và giờ:
labelForSpaceQuickFilter=Vùng dữ liệu
labelForStatusQuickFilter=Trạng thái
#XTIT: Cancel Task Started toast msg
cancelTaskStarted=Hủy tác vụ đã khởi động
#YMSE: Cancel Task Error
taskCancelError=Tác vụ không thể hủy vì nó đã hoàn tất chạy.
#LSA Monitor Tab Name
lsaMonitorText=Kho đối tượng
#ID for LSA Storage By Space Widget
hdlfStorageSpace=Tập tin hồ dữ liệu SAP HANA Data: Sử dụng dung lượng
#ID for LSA Storage for All Spaces Widget
hdlfStorageSpaceTotal=Tập tin hồ dữ liệu SAP HANA Data: Sử dụng dung lượng của tất cả vùng dữ liệu
#Labels for LSA Monitor
lsaSparkTableApplicationColumnText=Ứng dụng
lsaSparkTableNoOfTasksColumnText=Số tác vụ
sparkApplicationConfigurationTitle=Cấu hình ứng dụng
sparkExecutorCPU=CPU thực thi
sparkExecutorMemory=Bộ nhớ thực thi
sparkDriverCPU=CPU trình điều khiển
sparkDriverMemory=Bộ nhớ trình điều khiển
sparkMaximumCPU=CPU tối đa
sparkMaximumMemory=Bộ nhớ tối đa
sparkMinExecutors=Bộ thực thi tối đa
sparkMaxExecutors=Bộ thực thi tối thiểu
sparkAppTableNoDataIMTitle=Không có vùng dữ liệu được chọn
sparkAppTableNoDataIMDesc=Bắt đầu bằng cách chọn một từ danh sách trong thanh bộ lọc.
TBUnit=TB
sparkTaskTableNoDataIMTitle=Không có tác vụ có sẵn
sparkTaskTableNoDataIMDesc=Đối với ứng dụng đã chọn, không có tác vụ nào khả dụng. Hãy chọn ứng dụng khác.
taskActivity=Hoạt động tác vụ
sparkTableNoSearchResultsTitle=Không có kết quả tìm kiếm
sparkTableNoSearchResultsDesc=Thay đổi thuật ngữ tìm kiếm và thử lại lần nữa.
sparkTableNoFilterResultsTitle=Không có kết quả lọc
sparkTableNoFilterResultsDesc=Thay đổi tiêu chí lọc và thử lại.
removeFilterText=Xóa bộ lọc
sortTableText=Sắp xếp bảng
filterTableText=Bộ lọc
apacheSparkTableText=Apache Spark: Tác vụ
closeColumnText=Đóng cột
