#XTIT: Dynamic Page title
pageTitle=Bienvenue dans le moniteur du système
#XTIT: Dynamic Page subtitle
pageSubtitle=Suivez la performance de votre système et identifiez les problèmes de stockage, de tâches, de mémoire insuffisante et autres.
#XTIT: Messagestrip Enable Statement tracing
messagestripEnableStatementTracing=Pour suivre les erreurs de mémoire insuffisante et d'autres informations concernant les instructions, activez le traçage pour instruction coûteuse en temps dans Configuration/Suivi.
#XFLD: Dashboard text
dashBoardText=Tableau de bord
#XFLD: TaskLog text
taskLogText=Journaux
#XTIT: card title: Storage Distribution
storageDistribution=Stockage de disque utilisé
#XTIT: card title: Memory Distribution
memoryDistribution=Répartition de la mémoire
#XTIT: card title:Used Disk Capacity
usedStorageCapacity=Disque utilisé par les espaces pour le stockage
#XTIT: card title: Used Memory Capacity
usedMemoryCapacity=Mémoire utilisée par les espaces pour le stockage
#XTIT: card title:Assigned Disk Capacity
assignedStorageCapacity=Disque affecté aux espaces pour le stockage
#XTIT: card title: Assigned Memory Capacity
assignedMemoryCapacity=Mémoire affectée aux espaces pour le stockage
#XTIT: card title: Errors
errors=Tâches ayant échoué
#XTIT: card title: Configuration
ecnConfiguration=Configuration
#XTIT: card title: ECN Monthly Uptime
upTime=Temps d'exploitation mensuel
#XTIT: card title: ECN Average Memory
ECNAvgMemory=Utilisation moyenne de la mémoire
#XTIT: card title: ECN Average CPU
ECNAvgCPU=Utilisation moyenne de l'UC
#XTIT: card Sub title: ECN Hours Subtitle
ECNHoursSubtitle={0} dernières heures
#XTIT: card Title: ECN Last 5 Start Tasks
ecnTop5StartStop=5 dernières tâches de démarrage/d'arrêt du nœud de calcul flexible
#XTIT: card Title: ECN Last 5 Stop Tasks
ecnTop5Stop=5 dernières tâches d'arrêt du nœud de calcul flexible
#XTIT: card title: Out of Memory Events (all)
OoMEvents=Erreurs de mémoire insuffisante
#XTIT: card title: MDS
OoMMDSQueries=Erreurs de mémoire insuffisante (demandes MDS)
#XTIT: card title: Top 5 Memory Consumption (Statements)
memoryConsumptionStatements=Top 5 des instructions par utilisation de la mémoire pendant le traitement
#XTIT: card title: Out of Memory Events (Workload Class) by Space
OoMEventsByWorkloadClass=Top 5 des erreurs de mémoire insuffisante (classe de charge de travail) par espace
#XTIT: card title: Run Duration
runDuration=Top 5 des tâches par durée d'exécution
#XTIT: card title: Top 5 Tasks by Memory Consumption
tasksBymemoryConsumption=Top 5 des tâches par utilisation de la mémoire pendant le traitement
#XTIT: card title: Memory Consumption
memoryConsumption=Utilisation de la mémoire
#XTIT: card title: Top 5 Memory Consumption (MDS)
memoryConsumptionSAC=Top 5 des demandes MDS par utilisation de la mémoire pendant le traitement
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Données du journal d'audit
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Métadonnées de gestion
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Autres données
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Données dans les espaces
#XFLD: ResourceCategory Available Memory
ResourceCategoryAvailableMemory=Mémoire non utilisée
#XFLD: ResourceCategory Memory Used by Consumption
ResourceCategoryMemoryConsumption=Mémoire utilisée pour le traitement
#XFLD: ResourceCategory Memory Used by Replication
ResourceCategoryMemoryReplication=Mémoire utilisée pour la réplication de données
#XFLD: ResourceCategory unknown
unknown=Inconnu
#XBUT: Card footer view list button
viewList=Afficher les journaux
#NOTR: placeholder for dev-purposes
test=TEST
#XTIT: card bar label: hours
hours=Heures
#XTIT: card subtitle: now
now=Maintenant
#XTIT: card subtitle: last 24 hours
last24Hours=24 dernières heures
#XTIT: card subtitle: last 48 hours
last48Hours=48 dernières heures
#XTIT: card subtitle: last week
lastWeek=7 derniers jours
#XTIT: card subtitle: last month
lastMonth=Mois dernier
#XFLD: Close
close=Fermer
#XFLD: Today
today=Aujourd'hui
#XTIT: text for task tab
taskTab=Tâches
#XTIT: text for statement tab
statementTab=Instructions
#XTIT: text fo nav link in table cell
viewStatements=Afficher
#XTIT: text to more link in table cell
more=Plus
#XTIT: text for statement dialog header
statementDlgTitle=Instruction
#XBUT: text for close btn on dialog
closeBtn=Fermer
#XBUT: text for copy btn on dialog
copyBtn=Copier
#XTIT: Copy success text on toast msg
copiedToClipboard=Copié dans le presse-papiers
#XTIT: test for download btn on dialog
downloadBtn=Télécharger

#XTIT: Monitoring table column names
startTime=Heure de début
startDate=Date/Heure de début
duration=Durée
objectType=Type d'objet
activity=Activité
spaceName=Nom de l'espace
objectName=Nom de l'objet
peakMemory=Pic de mémoire - SAP HANA
peakCPU=Temps processeur - SAP HANA
noOfRecords=Enregistrements
usedMemory=Mémoire utilisée - SAP HANA
usedDisk=Disque utilisé - SAP HANA
status=Statut
subStatus=Statut partiel
user=Utilisateur
targetTable=Table cible
statements=Instructions
outOfMemory=Mémoire insuffisante
taskLogId=ID de journal des tâches
statementDetails=Détails de l'instruction
parameters=Paramètres
workloadClass=Classe de charge de travail
errorCode=Code d'erreur
errorText=Message d'erreur
dbUser=Utilisateur de base de données
connectionID=ID de connexion
statementID=ID d'instruction
elasticComputeNode=Nœud de calcul flexible


#Localization for @sap/dragonet-ui5-table-editor node module
#XTIT: Title for column settings dialog
columnsSettingsDialogTitle=Paramètres des colonnes
#XFLD: Title for Error type
messagesTableType=Type
#XFLD: Title for Error Message
messagesTableMessage=Message
#XFLD: Title for filter
filteredBy=Filtré par :
#XTIT: text for values contained in filter
filterContains=contient
#XTIT: text for values starting with in filter
filterStartsWith=commence par
#XTIT: text for values ending with in filter
filterEndsWith=se termine par
#XTIT: Title for search in data preview toolbar
toolbarSearch=Rechercher
#XBUT: Button to clear filter
clearFilter=Réinitialiser le filtre
#XBUT: Button to cancel the operation
cancel=Annuler
#XBUT: Button to save the operation
ok=OK
#XBUT: Button to restore the data
toolbarRestoreButton=Actualiser
#XBUT: Button to cancel running task
cancelBtnText=Annuler la tâche
#XBUT: tooltip for Button to restore the data
toolbarRestoreButtonTooltip=Mettre à jour la page
#XMSG: Busy dialog for Columns Settings dialog
PROCESSING=Traitement en cours...
#XMSG: Message Confirmation
confirmation=Confirmation
#XMSG: Message for refresh successful
refreshSuccess=Correctement actualisé
#XMSG: Message for refresh successful
refreshSuccessful=Actualisé
#XMSG: Message for restore successful
restoreSuccessful=Correctement restauré
#XMSG: Error message when null value found on non-nullable field
mdm-emptyValueError="{0}" ne peut pas être vide. {1}.
#XTIT: Sort Ascending
mdm-sortAscending=Trier par ordre croissant
#XTIT: Sort Descending
mdm-sortDescending=Trier par ordre décroissant
#XTIT: Filter
mdm-Filter=Filtrer
#XBUT: Button Cancel
mdm-cancel=Annuler
#XBUT: Button Add
mdm-Add=Ajouter
#XMSG: and inside error message
and=et
#XMSG: Error message when no column selected
mdm-allColumnUnselected=Sélectionnez une ou plusieurs colonnes pour poursuivre.

#Localization keys not needed for System Monitor but required for @sap/dragonet-ui5-table-editor node module to remove console errors
#XTXT: Message information strip for filtering and sorting
mdm-InfoStrip=Les outils Filtrer, Trier, Supprimer et Paramètres de table sont désactivés lorsque les modifications ne sont pas enregistrées. Enregistrez vos modifications pour les activer.
#XBUT: Button for file upload in mdm data preview
toolbarUploadButton=Téléverser les données
#XBUT: Button for duplicating rows in mdm data preview
toolbarDuplicateButton=Dupliquer
#XBUT: Button for deleting rows in mdm data preview
toolbarDeleteButton=Supprimer
#XBUT: Button for adding rows in mdm data preview
toolbarAddButton=Ajouter

#XTIT: MDM Empty value Handling in Table Editor Label
mdmInsertMissingString=Insérer la valeur de chaîne manquante en tant que :
#XTIT: MDM Empty value Handling in Table Editor Null Combobox item
mdmNullText=Valeur NULL
#XTIT: MDM Empty value Handling in Table Editor Empty string Combobox item
mdmEmptyStringText=Chaîne vide
#XTXT: Message information strip for Insert Missing String Value
mdmInfoStripMissingString=Les valeurs de chaîne manquantes sont insérées uniquement dans les colonnes de chaîne visibles de lignes nouvelles et modifiées. Utilisez Paramètres des colonnes pour afficher toutes les colonnes pertinentes.
#XTXT: Message information strip for filtering sorting and Insert Missing String Value
mdm-InfoStripWithMissingString=Les outils Filtrer, Trier, Insérer la valeur de chaîne manquante et Paramètres de table sont désactivés lorsque les modifications ne sont pas enregistrées. Enregistrez vos modifications pour les activer.
#XFLD: Open SAP HANA Cockpit
openHanaCockpit=Aperçu de la base de données (Cockpit SAP HANA)
#XBUT: Open SAP HANA Performance Monitor Cockpit
openHanaCockpitPerfMonitor=Moniteur des performances
openHanaCockpitPerfMonitorTooltip=Ouvrir le moniteur des performances dans le cockpit SAP HANA
#XTIT: card title: Admission Control Rejection Events
acRejectedEvents=Contrôle d'accès pour le rejet d'événements
#XTIT: card title: Admission Control Queuing Events
acQueuedEvents=Contrôle d'accès pour la mise en attente d'événements

#XTIT: card title: Top 5 Admission Control Rejection Events by Space
acRejectionEventsBySpace=Top 5 des contrôles d'accès pour le rejet d'événements par espace
#XTIT: card title: Top 5 Admission Control Queuing Events by Space
acQueueingEventsBySpace=Top 5 des contrôles d'accès pour la mise en attente d'événements par espace

#XTIT: payload Table and List texts
payloadDownloadText=Données utiles téléchargées
dataSourceText=Sources de données
dataSourcesObject=Nom
dataSourcesSchema=Nom de l'espace
payloadListHeader=Données utiles
payloadStoryIdText=ID de présentation
payloadStoryNameText=Nom de la présentation
payloadDialogTitleText=En savoir plus
payloadTextAreaTitle=Données utiles
NO_MDS_DETAIL=Aucune donnée disponible
MDS_DATA_FETCH_FAILED=En raison d'une erreur de serveur, nous n'avons pas pu afficher les informations sur les instructions MDS.
GBUnit=Go
enabled=Activé
notEnabled=Non activé
#XTIT: ECN Monitoring Dashboard Texts
ecnStatus=Statut
ecnBlock=Blocs de calcul
ecnSize=Taille
ecnConfigMemory=Mémoire
ecnConfigStorage=Stockage
ecnConfigSpacesList=Liste des espaces
#XFLD: ECN phase Ready
ecnReady=Prêt
#XFLD: ECN phase Running
ecnRunning=En cours d'exécution
#XFLD: ECN phase Initial
ecnInitial=Pas prêt
#XFLD: ECN phase Starting
ecnStarting=Lancement
#XFLD: ECN phase Stopping
ecnStopping=Arrêt en cours
#XFLD: ECN phase Unknown
ecnUnknown=Inconnu
#XFLD: ECN Last Run Details
lastRun=Dernière exécution
#XFLD: ECN Previous Run Details
previousRun=Exécution précédente
#XFLD: ECN Manage Button
manage=Gérer le nœud de calcul flexible
#XFLD: ECN Run Details Widget
days=Jours
#XFLD: ECN Run Details Widget Title
runDetails=Détails de l'exécution
#XFLD: ECN Performance Class Label
performanceClass=Classe de performance
start=Lancer
stop=Arrêter
ecnRunDetailsUpTime=Temps d'exploitation
ecnConfigurationCPU=Nombre d'unités centrales virtuelles
ecnTechnicalName=Nom technique
currentMonth=Mois en cours
previousMonth=Mois précédent
ecnBlockHours=Temps de calcul par bloc
upTimeOverall=Temps d'exploitation total
refresh=Actualiser
top5MemConsumptionDataFetchErrorText=Échec de l'obtention des données relatives à l'utilisation de la mémoire
ecnDashboardText=Nœuds de calcul flexibles
#XMSG: Message Title when ECN is not in ruuning state
memoryDistributionErrorTitle=Non applicable
#XMSG: Warning Message when ECN is not in ruuning state
memoryDistributionErrorMessage=La distribution de mémoire est affichée uniquement lorsque le nœud de calcul flexible est en mode d'exécution.
#XMSG: Title for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageTitle=Aucun nœud de calcul flexible sélectionné
#XMSG: Description for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageDescription=Commencez par en sélectionner un dans la liste dans l'en-tête.
#XMSG: Tab label for Statememt Logs
statementLogsText=Journaux des déclarations
#XMSG: Tab label for Task Logs
taskLogsText=Journaux des tâches
#MSG: Label for Date Time Quick Filter
labelForTimeQuickFilter=Plage de dates/heures :
labelForSpaceQuickFilter=Espaces
labelForStatusQuickFilter=Statuts
#XTIT: Cancel Task Started toast msg
cancelTaskStarted=Annulation de la tâche lancée
#YMSE: Cancel Task Error
taskCancelError=La tâche ne peut pas être annulée car son exécution est déjà terminée.
#LSA Monitor Tab Name
lsaMonitorText=Object Store
#ID for LSA Storage By Space Widget
hdlfStorageSpace=SAP HANA, lac de données - Fichiers : utilisation du stockage
#ID for LSA Storage for All Spaces Widget
hdlfStorageSpaceTotal=SAP HANA, lac de données - Fichiers : utilisation du stockage de tous les espaces
#Labels for LSA Monitor
lsaSparkTableApplicationColumnText=Application
lsaSparkTableNoOfTasksColumnText=Nombre de tâches
sparkApplicationConfigurationTitle=Configuration d'application
sparkExecutorCPU=UC de l'exécuteur
sparkExecutorMemory=Mémoire de l'exécuteur
sparkDriverCPU=UC du pilote
sparkDriverMemory=Mémoire du pilote
sparkMaximumCPU=UC maximale
sparkMaximumMemory=Mémoire maximale
sparkMinExecutors=Exécuteurs minimum
sparkMaxExecutors=Exécuteurs maximum
sparkAppTableNoDataIMTitle=Aucun espace sélectionné
sparkAppTableNoDataIMDesc=Commencez par en sélectionner un dans la barre de filtres.
TBUnit=To
sparkTaskTableNoDataIMTitle=Aucune tâche disponible
sparkTaskTableNoDataIMDesc=Pour l'application sélectionnée, aucune tâche n'est disponible. Sélectionnez une autre application.
taskActivity=Activité de tâche
sparkTableNoSearchResultsTitle=Aucun résultat de recherche
sparkTableNoSearchResultsDesc=Modifiez le critère de recherche et réessayez.
sparkTableNoFilterResultsTitle=Aucun résultat de filtre
sparkTableNoFilterResultsDesc=Modifiez les critères du filtre et réessayez.
removeFilterText=Réinitialiser le filtre
sortTableText=Trier la table
filterTableText=Filtrer la table
apacheSparkTableText=Apache Spark : tâches
closeColumnText=Fermer la colonne
