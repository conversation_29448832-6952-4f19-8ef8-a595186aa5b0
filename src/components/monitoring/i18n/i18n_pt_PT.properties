#XTIT: Dynamic Page title
pageTitle=Bem-vindo ao monitor do sistema
#XTIT: Dynamic Page subtitle
pageSubtitle=Monitorize o desempenho do seu sistema e identifique problemas de armazenamento, tarefa, memória esgotada, etc.
#XTIT: Messagestrip Enable Statement tracing
messagestripEnableStatementTracing=Para monitorizar erros de memória esgotada e outras informações de instruções, ative o rastreio de instruções demoradas em Configuração/Monitorização.
#XFLD: Dashboard text
dashBoardText=Dashboard
#XFLD: TaskLog text
taskLogText=Registos
#XTIT: card title: Storage Distribution
storageDistribution=Armazenamento de disco utilizado
#XTIT: card title: Memory Distribution
memoryDistribution=Distribuição de memória
#XTIT: card title:Used Disk Capacity
usedStorageCapacity=Disco utilizado por espaços para armazenamento
#XTIT: card title: Used Memory Capacity
usedMemoryCapacity=Memória utilizada por espaços para armazenamento
#XTIT: card title:Assigned Disk Capacity
assignedStorageCapacity=Disco atribuído a espaços para armazenamento
#XTIT: card title: Assigned Memory Capacity
assignedMemoryCapacity=Memória atribuída a espaços para armazenamento
#XTIT: card title: Errors
errors=Tarefas falhadas
#XTIT: card title: Configuration
ecnConfiguration=Configuração
#XTIT: card title: ECN Monthly Uptime
upTime=Tempo de atividade mensal
#XTIT: card title: ECN Average Memory
ECNAvgMemory=Memória média
#XTIT: card title: ECN Average CPU
ECNAvgCPU=CPU média
#XTIT: card Sub title: ECN Hours Subtitle
ECNHoursSubtitle=Últimas {0} horas
#XTIT: card Title: ECN Last 5 Start Tasks
ecnTop5StartStop=Últimas 5 tarefas de início/paragem ECN
#XTIT: card Title: ECN Last 5 Stop Tasks
ecnTop5Stop=Últimas 5 tarefas de paragem ECN
#XTIT: card title: Out of Memory Events (all)
OoMEvents=Erros de memória esgotada
#XTIT: card title: MDS
OoMMDSQueries=Erros de memória esgotada (pedidos MDS)
#XTIT: card title: Top 5 Memory Consumption (Statements)
memoryConsumptionStatements=5 principais instruções por consumo de memória de processamento
#XTIT: card title: Out of Memory Events (Workload Class) by Space
OoMEventsByWorkloadClass=5 principais erros de memória esgotada (classe de carga de trabalho) por espaço
#XTIT: card title: Run Duration
runDuration=5 principais tarefas por duração de execução
#XTIT: card title: Top 5 Tasks by Memory Consumption
tasksBymemoryConsumption=5 principais tarefas por consumo de memória de processamento
#XTIT: card title: Memory Consumption
memoryConsumption=Consumo de memória
#XTIT: card title: Top 5 Memory Consumption (MDS)
memoryConsumptionSAC=5 principais pedidos MDS por consumo de memória de processamento
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Dados do registo de auditoria
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Dados administrativos
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Outros dados
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Dados em espaços
#XFLD: ResourceCategory Available Memory
ResourceCategoryAvailableMemory=Memória não utilizada
#XFLD: ResourceCategory Memory Used by Consumption
ResourceCategoryMemoryConsumption=Memória utilizada para processamento
#XFLD: ResourceCategory Memory Used by Replication
ResourceCategoryMemoryReplication=Memória utilizada para replicação de dados
#XFLD: ResourceCategory unknown
unknown=Desconhecido
#XBUT: Card footer view list button
viewList=Visualizar registos
#NOTR: placeholder for dev-purposes
test=TEST
#XTIT: card bar label: hours
hours=Horas
#XTIT: card subtitle: now
now=Agora
#XTIT: card subtitle: last 24 hours
last24Hours=Últimas 24 horas
#XTIT: card subtitle: last 48 hours
last48Hours=Últimas 48 horas
#XTIT: card subtitle: last week
lastWeek=Últimos 7 dias
#XTIT: card subtitle: last month
lastMonth=Último mês
#XFLD: Close
close=Fechar
#XFLD: Today
today=Hoje
#XTIT: text for task tab
taskTab=Tarefas
#XTIT: text for statement tab
statementTab=Instruções
#XTIT: text fo nav link in table cell
viewStatements=Vista
#XTIT: text to more link in table cell
more=Mais
#XTIT: text for statement dialog header
statementDlgTitle=Instrução
#XBUT: text for close btn on dialog
closeBtn=Fechar
#XBUT: text for copy btn on dialog
copyBtn=Copiar
#XTIT: Copy success text on toast msg
copiedToClipboard=Copiado para a área de transferência
#XTIT: test for download btn on dialog
downloadBtn=Transferir

#XTIT: Monitoring table column names
startTime=Hora de início
startDate=Data de início
duration=Duração
objectType=Tipo de objeto
activity=Atividade
spaceName=Nome do espaço
objectName=Nome do objeto
peakMemory=Memória de pico SAP HANA
peakCPU=Tempo de CPU SAP HANA
noOfRecords=Registos
usedMemory=Memória utilizada SAP HANA
usedDisk=Disco utilizado SAP HANA
status=Estado
subStatus=Subestado
user=Utilizador
targetTable=Tabela de destino
statements=Instruções
outOfMemory=Memória esgotada
taskLogId=ID de registo de tarefa
statementDetails=Detalhes da instrução
parameters=Parâmetros
workloadClass=Classe de carga de trabalho
errorCode=Código de erro
errorText=Mensagem de erro
dbUser=Utilizador da base de dados
connectionID=ID de ligação
statementID=ID da instrução
elasticComputeNode=Nó de computação elástico


#Localization for @sap/dragonet-ui5-table-editor node module
#XTIT: Title for column settings dialog
columnsSettingsDialogTitle=Definições de colunas
#XFLD: Title for Error type
messagesTableType=Tipo
#XFLD: Title for Error Message
messagesTableMessage=Mensagem
#XFLD: Title for filter
filteredBy=Filtrado por:
#XTIT: text for values contained in filter
filterContains=contém
#XTIT: text for values starting with in filter
filterStartsWith=começa com
#XTIT: text for values ending with in filter
filterEndsWith=termina com
#XTIT: Title for search in data preview toolbar
toolbarSearch=Procurar
#XBUT: Button to clear filter
clearFilter=Limpar filtro
#XBUT: Button to cancel the operation
cancel=Cancelar
#XBUT: Button to save the operation
ok=OK
#XBUT: Button to restore the data
toolbarRestoreButton=Atualizar
#XBUT: Button to cancel running task
cancelBtnText=Cancelar tarefa
#XBUT: tooltip for Button to restore the data
toolbarRestoreButtonTooltip=Atualizar a página
#XMSG: Busy dialog for Columns Settings dialog
PROCESSING=A processar...
#XMSG: Message Confirmation
confirmation=Confirmação
#XMSG: Message for refresh successful
refreshSuccess=Atualizado com êxito
#XMSG: Message for refresh successful
refreshSuccessful=Atualizado
#XMSG: Message for restore successful
restoreSuccessful=Restaurado com êxito
#XMSG: Error message when null value found on non-nullable field
mdm-emptyValueError="{0}" não pode estar vazio. {1}.
#XTIT: Sort Ascending
mdm-sortAscending=Ordenação ascendente
#XTIT: Sort Descending
mdm-sortDescending=Ordenação descendente
#XTIT: Filter
mdm-Filter=Filtrar
#XBUT: Button Cancel
mdm-cancel=Cancelar
#XBUT: Button Add
mdm-Add=Adicionar
#XMSG: and inside error message
and=e
#XMSG: Error message when no column selected
mdm-allColumnUnselected=Selecione uma ou mais colunas para continuar.

#Localization keys not needed for System Monitor but required for @sap/dragonet-ui5-table-editor node module to remove console errors
#XTXT: Message information strip for filtering and sorting
mdm-InfoStrip=As ferramentas Filtrar, Eliminar e Definição de tabela estão desativadas por alterações não guardadas.
#XBUT: Button for file upload in mdm data preview
toolbarUploadButton=Carregar dados
#XBUT: Button for duplicating rows in mdm data preview
toolbarDuplicateButton=Duplicar
#XBUT: Button for deleting rows in mdm data preview
toolbarDeleteButton=Eliminar
#XBUT: Button for adding rows in mdm data preview
toolbarAddButton=Adicionar

#XTIT: MDM Empty value Handling in Table Editor Label
mdmInsertMissingString=Inserir valor de cadeia em falta como:
#XTIT: MDM Empty value Handling in Table Editor Null Combobox item
mdmNullText=NULL
#XTIT: MDM Empty value Handling in Table Editor Empty string Combobox item
mdmEmptyStringText=Cadeia vazia
#XTXT: Message information strip for Insert Missing String Value
mdmInfoStripMissingString=Os valores de cadeia em falta são inseridos apenas em colunas de cadeia visíveis em linhas novas ou editadas. Utilize as configurações de coluna para apresentar todas as colunas relevantes.
#XTXT: Message information strip for filtering sorting and Insert Missing String Value
mdm-InfoStripWithMissingString=As ferramentas Filtrar, Ordenar, Inserir valor de cadeia em falta e Definição de tabela estão desativadas por alterações não guardadas. Para as ativar, guarde as alterações.
#XFLD: Open SAP HANA Cockpit
openHanaCockpit=Visão geral da base de dados (cockpit do SAP HANA)
#XBUT: Open SAP HANA Performance Monitor Cockpit
openHanaCockpitPerfMonitor=Monitor de desempenho
openHanaCockpitPerfMonitorTooltip=Abrir o monitor de desempenho no cockpit do SAP HANA
#XTIT: card title: Admission Control Rejection Events
acRejectedEvents=Eventos de rejeição do controlo de admissão
#XTIT: card title: Admission Control Queuing Events
acQueuedEvents=Eventos em fila do controlo de admissão

#XTIT: card title: Top 5 Admission Control Rejection Events by Space
acRejectionEventsBySpace=Top 5 de eventos de rejeição do controlo de admissão por espaço
#XTIT: card title: Top 5 Admission Control Queuing Events by Space
acQueueingEventsBySpace=Top 5 de eventos em fila do controlo de admissão por espaço

#XTIT: payload Table and List texts
payloadDownloadText=Payload transferido
dataSourceText=Origens de dados
dataSourcesObject=Nome
dataSourcesSchema=Nome do espaço
payloadListHeader=Payloads
payloadStoryIdText=ID da história
payloadStoryNameText=Nome da história
payloadDialogTitleText=Mais informações
payloadTextAreaTitle=Payload
NO_MDS_DETAIL=Sem dados disponíveis
MDS_DATA_FETCH_FAILED=Não conseguimos apresentar informações sobre instruções MDS devido a um erro do servidor.
GBUnit=GB
enabled=Ativado
notEnabled=Não ativado
#XTIT: ECN Monitoring Dashboard Texts
ecnStatus=Estado
ecnBlock=Blocos de computação
ecnSize=Tamanho
ecnConfigMemory=Memória
ecnConfigStorage=Armazenamento
ecnConfigSpacesList=Lista de espaços
#XFLD: ECN phase Ready
ecnReady=Pronto
#XFLD: ECN phase Running
ecnRunning=Em execução
#XFLD: ECN phase Initial
ecnInitial=Não pronto
#XFLD: ECN phase Starting
ecnStarting=A iniciar
#XFLD: ECN phase Stopping
ecnStopping=A parar
#XFLD: ECN phase Unknown
ecnUnknown=Desconhecido
#XFLD: ECN Last Run Details
lastRun=Última execução
#XFLD: ECN Previous Run Details
previousRun=Execução anterior
#XFLD: ECN Manage Button
manage=Gerir nó de computação elástico
#XFLD: ECN Run Details Widget
days=Dias
#XFLD: ECN Run Details Widget Title
runDetails=Detalhes da execução
#XFLD: ECN Performance Class Label
performanceClass=Classe de desempenho
start=Iniciar
stop=Parar
ecnRunDetailsUpTime=Tempo de atividade
ecnConfigurationCPU=Número de vCPUs
ecnTechnicalName=Nome técnico
currentMonth=Mês atual
previousMonth=Mês anterior
ecnBlockHours=Bloco de horas
upTimeOverall=Tempo total de atividade
refresh=Atualizar
top5MemConsumptionDataFetchErrorText=Falha ao obter dados para o consumo de memória
ecnDashboardText=Nós de computação elásticos
#XMSG: Message Title when ECN is not in ruuning state
memoryDistributionErrorTitle=Não aplicável
#XMSG: Warning Message when ECN is not in ruuning state
memoryDistributionErrorMessage=A distribuição da memória é apresentada apenas quando o nó de computação elático está num estado de execução.
#XMSG: Title for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageTitle=Nenhum nó de computação elástico selecionado
#XMSG: Description for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageDescription=Comece por selecionar um na lista do cabeçalho.
#XMSG: Tab label for Statememt Logs
statementLogsText=Registos de instruções
#XMSG: Tab label for Task Logs
taskLogsText=Registos de tarefa
#MSG: Label for Date Time Quick Filter
labelForTimeQuickFilter=Intervalo de datas e horas:
labelForSpaceQuickFilter=Espaços
labelForStatusQuickFilter=Estados
#XTIT: Cancel Task Started toast msg
cancelTaskStarted=Cancelamento da tarefa iniciado
#YMSE: Cancel Task Error
taskCancelError=A tarefa não pode ser cancelada por a sua execução já foi concluída.
#LSA Monitor Tab Name
lsaMonitorText=Armazenamento de objetos
#ID for LSA Storage By Space Widget
hdlfStorageSpace=Ficheiros Data Lake SAP HANA: utilização de armazenamento
#ID for LSA Storage for All Spaces Widget
hdlfStorageSpaceTotal=Ficheiros Data Lake SAP HANA: utilização de armazenamento de todos os espaços
#Labels for LSA Monitor
lsaSparkTableApplicationColumnText=Aplicação
lsaSparkTableNoOfTasksColumnText=Número de tarefas
sparkApplicationConfigurationTitle=Configuração da aplicação
sparkExecutorCPU=CPU de executor
sparkExecutorMemory=Memória de executor
sparkDriverCPU=CPU de controlador
sparkDriverMemory=Memória de controlador
sparkMaximumCPU=CPU máximo
sparkMaximumMemory=Memória máxima
sparkMinExecutors=Executores mínimos
sparkMaxExecutors=Executores máximos
sparkAppTableNoDataIMTitle=Nenhum espaço selecionado
sparkAppTableNoDataIMDesc=Comece por selecionar um na lista da barra de filtros.
TBUnit=TB
sparkTaskTableNoDataIMTitle=Nenhuma tarefa disponível
sparkTaskTableNoDataIMDesc=Não existem tarefas disponíveis para a aplicação selecionada. Selecione outra aplicação.
taskActivity=Atividade da tarefa
sparkTableNoSearchResultsTitle=Nenhum resultado de pesquisa
sparkTableNoSearchResultsDesc=Altere o termo de pesquisa e tente novamente.
sparkTableNoFilterResultsTitle=Nenhum resultado de filtro
sparkTableNoFilterResultsDesc=Altere os critérios do filtro e tente novamente.
removeFilterText=Limpar filtro
sortTableText=Ordenar tabela
filterTableText=Filtro
apacheSparkTableText=Apache Spark: tarefas
closeColumnText=Fechar coluna
