#XTIT: Dynamic Page title
pageTitle=Esiet sveicināts sistēmas pārraugā!
#XTIT: Dynamic Page subtitle
pageSubtitle=Pārraugiet savas sistēmas veiktspēju un nosakiet problēmas saistībā ar krātuvi, u<PERSON><PERSON><PERSON><PERSON><PERSON>, trū<PERSON><PERSON><PERSON>o atmiņu u.c.
#XTIT: Messagestrip Enable Statement tracing
messagestripEnableStatementTracing=Lai pārraudzītu trūkstošās atmiņas kļūdas un citu priekšrakstu informāciju, iespējojiet dārgo priekšrakstu izsekošanu sadaļā Konfigurācija/pārraudzība.
#XFLD: Dashboard text
dashBoardText=Informācijas panelis
#XFLD: TaskLog text
taskLogText=Žurnāli
#XTIT: card title: Storage Distribution
storageDistribution=Izmantotā diska krātuve
#XTIT: card title: Memory Distribution
memoryDistribution=Atmiņas sadale
#XTIT: card title:Used Disk Capacity
usedStorageCapacity=Vietu krātuvei izmantotais disks
#XTIT: card title: Used Memory Capacity
usedMemoryCapacity=Vietu krātuvei izmantotā atmiņa
#XTIT: card title:Assigned Disk Capacity
assignedStorageCapacity=Vietām piešķirtais disks krātuvei
#XTIT: card title: Assigned Memory Capacity
assignedMemoryCapacity=Vietām piešķirtā atmiņa krātuvei
#XTIT: card title: Errors
errors=Nesekmīgie uzdevumi
#XTIT: card title: Configuration
ecnConfiguration=Konfigurācija
#XTIT: card title: ECN Monthly Uptime
upTime=Mēneša darbības laiks
#XTIT: card title: ECN Average Memory
ECNAvgMemory=Vidējā atmiņa
#XTIT: card title: ECN Average CPU
ECNAvgCPU=Vidējais CPU
#XTIT: card Sub title: ECN Hours Subtitle
ECNHoursSubtitle=Pēdējās {0} stundas
#XTIT: card Title: ECN Last 5 Start Tasks
ecnTop5StartStop=ECN pēdējie 5 sākšanas/beigšanas uzdevumi
#XTIT: card Title: ECN Last 5 Stop Tasks
ecnTop5Stop=ECN pēdējie 5 beigšanas uzdevumi
#XTIT: card title: Out of Memory Events (all)
OoMEvents=Trūkstošās atmiņas kļūdas
#XTIT: card title: MDS
OoMMDSQueries=Trūkstošās atmiņas kļūdas (MDS pieprasījumi)
#XTIT: card title: Top 5 Memory Consumption (Statements)
memoryConsumptionStatements=5 galvenie priekšraksti pēc apstrādāšanas atmiņas patēriņa
#XTIT: card title: Out of Memory Events (Workload Class) by Space
OoMEventsByWorkloadClass=5 galvenās trūkstošās atmiņas kļūdas (darba slodzes klase) pēc vietas
#XTIT: card title: Run Duration
runDuration=5 galvenie uzdevumi pēc izpildes ilguma
#XTIT: card title: Top 5 Tasks by Memory Consumption
tasksBymemoryConsumption=5 galvenie uzdevumi pēc apstrādāšanas atmiņas patēriņa
#XTIT: card title: Memory Consumption
memoryConsumption=Atmiņas patēriņš
#XTIT: card title: Top 5 Memory Consumption (MDS)
memoryConsumptionSAC=5 galvenie MDS pieprasījumi pēc apstrādāšanas atmiņas patēriņa
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Audita žurnāla dati
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Administratīvie dati
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Citi dati
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Dati vietās
#XFLD: ResourceCategory Available Memory
ResourceCategoryAvailableMemory=Neizmantotā atmiņa
#XFLD: ResourceCategory Memory Used by Consumption
ResourceCategoryMemoryConsumption=Apstrādei izmantotā atmiņa
#XFLD: ResourceCategory Memory Used by Replication
ResourceCategoryMemoryReplication=Datu replicēšanai izmantotā atmiņa
#XFLD: ResourceCategory unknown
unknown=Nezināms
#XBUT: Card footer view list button
viewList=Skatīt žurnālus
#NOTR: placeholder for dev-purposes
test=TEST
#XTIT: card bar label: hours
hours=Stundas
#XTIT: card subtitle: now
now=Tagad
#XTIT: card subtitle: last 24 hours
last24Hours=Pēdējās 24 stundas
#XTIT: card subtitle: last 48 hours
last48Hours=Pēdējās 48 stundas
#XTIT: card subtitle: last week
lastWeek=Pēdējās 7 dienas
#XTIT: card subtitle: last month
lastMonth=Pēdējais mēnesis
#XFLD: Close
close=Aizvērt
#XFLD: Today
today=Šodien
#XTIT: text for task tab
taskTab=Uzdevumi
#XTIT: text for statement tab
statementTab=Priekšraksti
#XTIT: text fo nav link in table cell
viewStatements=Skats
#XTIT: text to more link in table cell
more=Vairāk
#XTIT: text for statement dialog header
statementDlgTitle=Priekšraksts
#XBUT: text for close btn on dialog
closeBtn=Aizvērt
#XBUT: text for copy btn on dialog
copyBtn=Kopēt
#XTIT: Copy success text on toast msg
copiedToClipboard=Kopēts uz starpliktuvi
#XTIT: test for download btn on dialog
downloadBtn=Lejupielādēt

#XTIT: Monitoring table column names
startTime=Sākuma laiks
startDate=Sākuma datums
duration=Ilgums
objectType=Objekta tips
activity=Darbība
spaceName=Vietas nosaukums
objectName=Objekta nosaukums
peakMemory=SAP HANA maksimālā atmiņa
peakCPU=SAP HANA CPU laiks
noOfRecords=Ieraksti
usedMemory=SAP HANA izmantotā atmiņa
usedDisk=SAP HANA izmantotā vieta diskā
status=Statuss
subStatus=Apakšstatuss
user=Lietotājs
targetTable=Mērķa tabula
statements=Priekšraksti
outOfMemory=Trūkstošā atmiņa
taskLogId=Uzdevumu žurnāla ID
statementDetails=Detalizēta informācija par priekšrakstu
parameters=Parametri
workloadClass=Darba slodzes klase
errorCode=Kļūdas kods
errorText=Kļūdas ziņojums
dbUser=Datu bāzes lietotājs
connectionID=Savienojuma ID
statementID=Priekšraksta ID
elasticComputeNode=Elastīgās aprēķināšanas mezgls


#Localization for @sap/dragonet-ui5-table-editor node module
#XTIT: Title for column settings dialog
columnsSettingsDialogTitle=Kolonnu iestatījumi
#XFLD: Title for Error type
messagesTableType=Tips
#XFLD: Title for Error Message
messagesTableMessage=Ziņojums
#XFLD: Title for filter
filteredBy=Filtrēts pēc:
#XTIT: text for values contained in filter
filterContains=ietver
#XTIT: text for values starting with in filter
filterStartsWith=sākas ar
#XTIT: text for values ending with in filter
filterEndsWith=beidzas ar
#XTIT: Title for search in data preview toolbar
toolbarSearch=Meklēt
#XBUT: Button to clear filter
clearFilter=Notīrīt filtru
#XBUT: Button to cancel the operation
cancel=Atcelt
#XBUT: Button to save the operation
ok=Labi
#XBUT: Button to restore the data
toolbarRestoreButton=Atsvaidzināt
#XBUT: Button to cancel running task
cancelBtnText=Atcelt uzdevumu
#XBUT: tooltip for Button to restore the data
toolbarRestoreButtonTooltip=Atjaunināt lapu
#XMSG: Busy dialog for Columns Settings dialog
PROCESSING=Notiek apstrāde...
#XMSG: Message Confirmation
confirmation=Apstiprinājums
#XMSG: Message for refresh successful
refreshSuccess=Sekmīgi atsvaidzināts
#XMSG: Message for refresh successful
refreshSuccessful=Atsvaidzināts
#XMSG: Message for restore successful
restoreSuccessful=Sekmīgi atjaunots
#XMSG: Error message when null value found on non-nullable field
mdm-emptyValueError=“{0}” nevar būt tukšs. {1}.
#XTIT: Sort Ascending
mdm-sortAscending=Kārtot augošā secībā
#XTIT: Sort Descending
mdm-sortDescending=Kārtot dilstošā secībā
#XTIT: Filter
mdm-Filter=Filtrēt
#XBUT: Button Cancel
mdm-cancel=Atcelt
#XBUT: Button Add
mdm-Add=Pievienot
#XMSG: and inside error message
and=un
#XMSG: Error message when no column selected
mdm-allColumnUnselected=Atlasiet vienu vai vairākas kolonnas, lai turpinātu.

#Localization keys not needed for System Monitor but required for @sap/dragonet-ui5-table-editor node module to remove console errors
#XTXT: Message information strip for filtering and sorting
mdm-InfoStrip=Rīkus Filtrēt, Kārtot, Dzēst un Tabulas iestatījumi atspējoja nesaglabātās izmaiņas. Lai tos iespējotu, saglabājiet izmaiņas.
#XBUT: Button for file upload in mdm data preview
toolbarUploadButton=Augšupielādēt datus
#XBUT: Button for duplicating rows in mdm data preview
toolbarDuplicateButton=Dublēt
#XBUT: Button for deleting rows in mdm data preview
toolbarDeleteButton=Dzēst
#XBUT: Button for adding rows in mdm data preview
toolbarAddButton=Pievienot

#XTIT: MDM Empty value Handling in Table Editor Label
mdmInsertMissingString=Ievietojiet trūkstošo virknes vērtību kā:
#XTIT: MDM Empty value Handling in Table Editor Null Combobox item
mdmNullText=NULL
#XTIT: MDM Empty value Handling in Table Editor Empty string Combobox item
mdmEmptyStringText=Tukša virkne
#XTXT: Message information strip for Insert Missing String Value
mdmInfoStripMissingString=Trūkstošās virkņu vērtības ir ievietotas tikai redzamo virkņu kolonnās jaunās un rediģētās rindās. Lai parādītu visas attiecīgās kolonnas, izmantojiet kolonnu iestatījumus.
#XTXT: Message information strip for filtering sorting and Insert Missing String Value
mdm-InfoStripWithMissingString=Rīkus Filtrēt, Kārtot, Ievietot trūkstošo virknes vērtību un Tabulas iestatījumi atspējoja nesaglabātās izmaiņas. Lai tos iespējotu, saglabājiet izmaiņas.
#XFLD: Open SAP HANA Cockpit
openHanaCockpit=Datu bāzu apskats (SAP HANA kontrolpults)
#XBUT: Open SAP HANA Performance Monitor Cockpit
openHanaCockpitPerfMonitor=Veiktspējas pārraugs
openHanaCockpitPerfMonitorTooltip=Atvērt veiktspējas pārraugu SAP HANA kontrolpultī
#XTIT: card title: Admission Control Rejection Events
acRejectedEvents=Ieejas vadības noraidītie notikumi
#XTIT: card title: Admission Control Queuing Events
acQueuedEvents=Ieejas vadības ierindotie notikumi

#XTIT: card title: Top 5 Admission Control Rejection Events by Space
acRejectionEventsBySpace=Ieejas vadības pirmie 5 noraidīšanas notikumi pēc vietas
#XTIT: card title: Top 5 Admission Control Queuing Events by Space
acQueueingEventsBySpace=Ieejas vadības pirmie 5 ierindošanas notikumi pēc vietas

#XTIT: payload Table and List texts
payloadDownloadText=Lejupielādētais vērtums
dataSourceText=Datu avoti
dataSourcesObject=Nosaukums
dataSourcesSchema=Vietas nosaukums
payloadListHeader=Vērtumi
payloadStoryIdText=Vēstījuma ID
payloadStoryNameText=Vēstījuma nosaukums
payloadDialogTitleText=Papildinformācija
payloadTextAreaTitle=Vērtums
NO_MDS_DETAIL=Nekādi dati nav pieejami
MDS_DATA_FETCH_FAILED=Neizdevās attēlot informāciju par MDS priekšrakstiem servera kļūdas dēļ.
GBUnit=GB
enabled=Iespējots
notEnabled=Nav iespējots
#XTIT: ECN Monitoring Dashboard Texts
ecnStatus=Statuss
ecnBlock=Skaitļošanas bloki
ecnSize=Lielums
ecnConfigMemory=Atmiņa
ecnConfigStorage=Uzglabāšana
ecnConfigSpacesList=Vietu saraksts
#XFLD: ECN phase Ready
ecnReady=Gatavs
#XFLD: ECN phase Running
ecnRunning=Tiek izpildīts
#XFLD: ECN phase Initial
ecnInitial=Nav gatavs
#XFLD: ECN phase Starting
ecnStarting=Sākšana
#XFLD: ECN phase Stopping
ecnStopping=Tiek apturēts
#XFLD: ECN phase Unknown
ecnUnknown=Nezināms
#XFLD: ECN Last Run Details
lastRun=Pēdējā izpilde
#XFLD: ECN Previous Run Details
previousRun=Iepriekšējā izpilde
#XFLD: ECN Manage Button
manage=Pārvaldīt elastīgās aprēķināšanas mezglu
#XFLD: ECN Run Details Widget
days=Dienas
#XFLD: ECN Run Details Widget Title
runDetails=Detalizēta informācija par izpildi
#XFLD: ECN Performance Class Label
performanceClass=Veiktspējas klase
start=Sākt
stop=Pārtraukt
ecnRunDetailsUpTime=Darbības laiks
ecnConfigurationCPU=vCPU skaits
ecnTechnicalName=Tehniskais nosaukums
currentMonth=Pašreizējais mēnesis
previousMonth=Iepriekšējais mēnesis
ecnBlockHours=Blokstundas
upTimeOverall=Kopējais darbības laiks
refresh=Atsvaidzināt
top5MemConsumptionDataFetchErrorText=Atmiņas patēriņam neizdevās iegūt datus
ecnDashboardText=Elastīgās aprēķināšanas mezgli
#XMSG: Message Title when ECN is not in ruuning state
memoryDistributionErrorTitle=Nav attiecināms
#XMSG: Warning Message when ECN is not in ruuning state
memoryDistributionErrorMessage=Atmiņas sadalījums tiek rādīts tikai tad, ja elastīgās aprēķināšanas mezgls nav izpildes stāvoklī.
#XMSG: Title for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageTitle=Nav atlasīts neviens elastīgās aprēķināšanas mezgls
#XMSG: Description for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageDescription=Vispirms atlasiet vienu no saraksta galvenē.
#XMSG: Tab label for Statememt Logs
statementLogsText=Priekšrakstu žurnāli
#XMSG: Tab label for Task Logs
taskLogsText=Uzdevumu žurnāli
#MSG: Label for Date Time Quick Filter
labelForTimeQuickFilter=Datumu un laika diapazons:
labelForSpaceQuickFilter=Vietas
labelForStatusQuickFilter=Statusi
#XTIT: Cancel Task Started toast msg
cancelTaskStarted=Uzdevuma atcelšana sākta
#YMSE: Cancel Task Error
taskCancelError=Uzdevumu atcelt nevar, jo tā izpilde jau ir pabeigta.
#LSA Monitor Tab Name
lsaMonitorText=Objektu krātuve
#ID for LSA Storage By Space Widget
hdlfStorageSpace=SAP HANA Data Lake faili: Krātuves lietojums
#ID for LSA Storage for All Spaces Widget
hdlfStorageSpaceTotal=SAP HANA Data Lake faili: Visu vietu krātuves lietojums
#Labels for LSA Monitor
lsaSparkTableApplicationColumnText=Lietojumprogramma
lsaSparkTableNoOfTasksColumnText=Uzdevumu skaits
sparkApplicationConfigurationTitle=Lietojumprogrammas konfigurācija
sparkExecutorCPU=Izpildītāja CPU
sparkExecutorMemory=Izpildītāja atmiņa
sparkDriverCPU=Draivera CPU
sparkDriverMemory=Draivera atmiņa
sparkMaximumCPU=Maksimālais CPU
sparkMaximumMemory=Maksimālā atmiņa
sparkMinExecutors=Minimālie izpildītāji
sparkMaxExecutors=Maksimālie izpildītāji
sparkAppTableNoDataIMTitle=Neviena vieta nav atlasīta
sparkAppTableNoDataIMDesc=Vispirms atlasiet vienu no saraksta filtra joslā.
TBUnit=TB
sparkTaskTableNoDataIMTitle=Nekādi uzdevumi nav pieejami
sparkTaskTableNoDataIMDesc=Atlasītajai lietojumprogrammai nekādi uzdevumi nav pieejami. Atlasiet citu lietojumprogrammu.
taskActivity=Uzdevuma aktivitāte
sparkTableNoSearchResultsTitle=Nav meklēšanas rezultātu
sparkTableNoSearchResultsDesc=Mainiet meklēšanas kritēriju un mēģiniet vēlreiz.
sparkTableNoFilterResultsTitle=Nav filtrēšanas rezultātu
sparkTableNoFilterResultsDesc=Mainiet filtrēšanas kritērijus un mēģiniet vēlreiz.
removeFilterText=Notīrīt filtru
sortTableText=Kārtot tabulu
filterTableText=Filtrēt
apacheSparkTableText=Apache Spark: Uzdevumi
closeColumnText=Aizvērt kolonnu
