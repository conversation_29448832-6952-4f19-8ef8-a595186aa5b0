#XTIT: Dynamic Page title
pageTitle=システムモニタへようこそ
#XTIT: Dynamic Page subtitle
pageSubtitle=システムのパフォーマンスを監視し、ストレージ、タスク、メモリ不足の各問題、およびその他の問題を特定します。
#XTIT: Messagestrip Enable Statement tracing
messagestripEnableStatementTracing=メモリ不足エラーおよびその他の文情報を監視するには、設定/監視で実行負荷の高い文の追跡を有効化してください。
#XFLD: Dashboard text
dashBoardText=ダッシュボード
#XFLD: TaskLog text
taskLogText=ログ
#XTIT: card title: Storage Distribution
storageDistribution=使用されているディスクストレージ
#XTIT: card title: Memory Distribution
memoryDistribution=メモリ配分
#XTIT: card title:Used Disk Capacity
usedStorageCapacity=ストレージのスペースによって使用されているディスク
#XTIT: card title: Used Memory Capacity
usedMemoryCapacity=ストレージに使用されているメモリ
#XTIT: card title:Assigned Disk Capacity
assignedStorageCapacity=ストレージのスペースに割り当てられているディスク
#XTIT: card title: Assigned Memory Capacity
assignedMemoryCapacity=ストレージのスペースに割り当てられているメモリ
#XTIT: card title: Errors
errors=失敗タスク
#XTIT: card title: Configuration
ecnConfiguration=設定
#XTIT: card title: ECN Monthly Uptime
upTime=月次稼働時間
#XTIT: card title: ECN Average Memory
ECNAvgMemory=平均メモリ
#XTIT: card title: ECN Average CPU
ECNAvgCPU=平均 CPU
#XTIT: card Sub title: ECN Hours Subtitle
ECNHoursSubtitle=最近 {0} 時間
#XTIT: card Title: ECN Last 5 Start Tasks
ecnTop5StartStop=ECN 開始/停止タスク最近 5 件
#XTIT: card Title: ECN Last 5 Stop Tasks
ecnTop5Stop=ECN 停止タスク最近 5 件
#XTIT: card title: Out of Memory Events (all)
OoMEvents=メモリ不足エラー
#XTIT: card title: MDS
OoMMDSQueries=メモリ不足エラー (MDS リクエスト)
#XTIT: card title: Top 5 Memory Consumption (Statements)
memoryConsumptionStatements=処理メモリ消費別上位 5 件の文
#XTIT: card title: Out of Memory Events (Workload Class) by Space
OoMEventsByWorkloadClass=スペース別上位 5 件のメモリ不足エラー (ワークロードクラス)
#XTIT: card title: Run Duration
runDuration=実行期間別上位 5 件のタスク
#XTIT: card title: Top 5 Tasks by Memory Consumption
tasksBymemoryConsumption=処理メモリ消費別上位 5 件のタスク
#XTIT: card title: Memory Consumption
memoryConsumption=メモリ消費
#XTIT: card title: Top 5 Memory Consumption (MDS)
memoryConsumptionSAC=処理メモリ消費別上位 5 件の MDS リクエスト
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=監査ログデータ
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=管理データ
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=他のデータ
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=スペース内のデータ
#XFLD: ResourceCategory Available Memory
ResourceCategoryAvailableMemory=未使用メモリ
#XFLD: ResourceCategory Memory Used by Consumption
ResourceCategoryMemoryConsumption=処理に使用されているメモリ
#XFLD: ResourceCategory Memory Used by Replication
ResourceCategoryMemoryReplication=データ複製に使用されているメモリ
#XFLD: ResourceCategory unknown
unknown=不明
#XBUT: Card footer view list button
viewList=ログの表示
#NOTR: placeholder for dev-purposes
test=TEST
#XTIT: card bar label: hours
hours=時
#XTIT: card subtitle: now
now=現在
#XTIT: card subtitle: last 24 hours
last24Hours=過去 24 時間
#XTIT: card subtitle: last 48 hours
last48Hours=過去 48 時間
#XTIT: card subtitle: last week
lastWeek=過去 7 日間
#XTIT: card subtitle: last month
lastMonth=先月
#XFLD: Close
close=閉じる
#XFLD: Today
today=本日
#XTIT: text for task tab
taskTab=タスク
#XTIT: text for statement tab
statementTab=文
#XTIT: text fo nav link in table cell
viewStatements=表示
#XTIT: text to more link in table cell
more=詳細
#XTIT: text for statement dialog header
statementDlgTitle=文
#XBUT: text for close btn on dialog
closeBtn=閉じる
#XBUT: text for copy btn on dialog
copyBtn=コピー
#XTIT: Copy success text on toast msg
copiedToClipboard=クリップボードにコピー
#XTIT: test for download btn on dialog
downloadBtn=ダウンロード

#XTIT: Monitoring table column names
startTime=開始時刻
startDate=開始日付
duration=期間
objectType=オブジェクトタイプ
activity=アクティビティ
spaceName=スペース名
objectName=オブジェクト名
peakMemory=SAP HAMA ピーク時メモリ
peakCPU=SAP HANA CPU 時間
noOfRecords=レコード
usedMemory=SAP HAMA 使用済みメモリ
usedDisk=SAP HANA 使用済みディスク
status=ステータス
subStatus=サブステータス
user=ユーザ
targetTable=ターゲットテーブル
statements=文
outOfMemory=メモリ不足
taskLogId=タスクログ ID
statementDetails=文の詳細
parameters=パラメータ
workloadClass=ワークロードクラス
errorCode=エラーコード
errorText=エラーメッセージ
dbUser=データベースユーザ
connectionID=接続 ID
statementID=文 ID
elasticComputeNode=Elastic Compute ノード


#Localization for @sap/dragonet-ui5-table-editor node module
#XTIT: Title for column settings dialog
columnsSettingsDialogTitle=列の設定
#XFLD: Title for Error type
messagesTableType=タイプ
#XFLD: Title for Error Message
messagesTableMessage=メッセージ
#XFLD: Title for filter
filteredBy=フィルタ基準:
#XTIT: text for values contained in filter
filterContains=含む
#XTIT: text for values starting with in filter
filterStartsWith=次で始まる
#XTIT: text for values ending with in filter
filterEndsWith=次で終わる
#XTIT: Title for search in data preview toolbar
toolbarSearch=検索
#XBUT: Button to clear filter
clearFilter=フィルタをクリア
#XBUT: Button to cancel the operation
cancel=キャンセル
#XBUT: Button to save the operation
ok=OK
#XBUT: Button to restore the data
toolbarRestoreButton=リフレッシュ
#XBUT: Button to cancel running task
cancelBtnText=タスクをキャンセル
#XBUT: tooltip for Button to restore the data
toolbarRestoreButtonTooltip=ページを更新
#XMSG: Busy dialog for Columns Settings dialog
PROCESSING=処理しています...
#XMSG: Message Confirmation
confirmation=確認
#XMSG: Message for refresh successful
refreshSuccess=正常にリフレッシュ済み
#XMSG: Message for refresh successful
refreshSuccessful=リフレッシュ済み
#XMSG: Message for restore successful
restoreSuccessful=正常に復元済み
#XMSG: Error message when null value found on non-nullable field
mdm-emptyValueError="{0}" は空にできません。{1}。
#XTIT: Sort Ascending
mdm-sortAscending=昇順ソート
#XTIT: Sort Descending
mdm-sortDescending=降順ソート
#XTIT: Filter
mdm-Filter=フィルタ
#XBUT: Button Cancel
mdm-cancel=キャンセル
#XBUT: Button Add
mdm-Add=追加
#XMSG: and inside error message
and=かつ (AND)
#XMSG: Error message when no column selected
mdm-allColumnUnselected=続行するには、1 行または複数行を選択してください。

#Localization keys not needed for System Monitor but required for @sap/dragonet-ui5-table-editor node module to remove console errors
#XTXT: Message information strip for filtering and sorting
mdm-InfoStrip=保存されていない変更がある場合は、フィルタ、ソート、削除、テーブル設定の各ツールを使用できません。変更を保存してツールを有効化してください。
#XBUT: Button for file upload in mdm data preview
toolbarUploadButton=データのアップロード
#XBUT: Button for duplicating rows in mdm data preview
toolbarDuplicateButton=重複
#XBUT: Button for deleting rows in mdm data preview
toolbarDeleteButton=削除
#XBUT: Button for adding rows in mdm data preview
toolbarAddButton=追加

#XTIT: MDM Empty value Handling in Table Editor Label
mdmInsertMissingString=不足した文字列値を挿入する形式:
#XTIT: MDM Empty value Handling in Table Editor Null Combobox item
mdmNullText=NULL
#XTIT: MDM Empty value Handling in Table Editor Empty string Combobox item
mdmEmptyStringText=空の文字列
#XTXT: Message information strip for Insert Missing String Value
mdmInfoStripMissingString=不足している文字列値は、新規および編集された行で表示されている文字列の列のみに挿入できます。関連するすべての列を表示するには、"列の設定" を使用します。
#XTXT: Message information strip for filtering sorting and Insert Missing String Value
mdm-InfoStripWithMissingString=保存されていない変更がある場合は、フィルタ、ソート、不足している文字列値の挿入、テーブル設定の各ツールは無効化されています。これらを有効化するには、変更を保存してください。
#XFLD: Open SAP HANA Cockpit
openHanaCockpit=データベース概要 (SAP HANA コックピット)
#XBUT: Open SAP HANA Performance Monitor Cockpit
openHanaCockpitPerfMonitor=パフォーマンスモニタ
openHanaCockpitPerfMonitorTooltip=SAP HANA コックピットでパフォーマンスモニタを開く
#XTIT: card title: Admission Control Rejection Events
acRejectedEvents=受付制御の却下イベント
#XTIT: card title: Admission Control Queuing Events
acQueuedEvents=受付制御のキューイベント

#XTIT: card title: Top 5 Admission Control Rejection Events by Space
acRejectionEventsBySpace=受付制御のスペース別の上位 5 件の却下イベント
#XTIT: card title: Top 5 Admission Control Queuing Events by Space
acQueueingEventsBySpace=受付制御のスペース別の上位 5 件のキューイベント

#XTIT: payload Table and List texts
payloadDownloadText=ペイロードがダウンロードされました
dataSourceText=データソース
dataSourcesObject=名前
dataSourcesSchema=スペース名
payloadListHeader=ペイロード
payloadStoryIdText=ストーリー ID
payloadStoryNameText=ストーリー名
payloadDialogTitleText=詳細情報
payloadTextAreaTitle=ペイロード
NO_MDS_DETAIL=利用可能データなし
MDS_DATA_FETCH_FAILED=サーバエラーのため、MDS 文に関する情報を表示できませんでした。
GBUnit=GB
enabled=有効化
notEnabled=無効
#XTIT: ECN Monitoring Dashboard Texts
ecnStatus=ステータス
ecnBlock=コンピュートブロック
ecnSize=サイズ
ecnConfigMemory=メモリ
ecnConfigStorage=ストレージ
ecnConfigSpacesList=スペースの一覧
#XFLD: ECN phase Ready
ecnReady=準備完了
#XFLD: ECN phase Running
ecnRunning=実行しています
#XFLD: ECN phase Initial
ecnInitial=準備中
#XFLD: ECN phase Starting
ecnStarting=開始しています
#XFLD: ECN phase Stopping
ecnStopping=停止しています
#XFLD: ECN phase Unknown
ecnUnknown=不明
#XFLD: ECN Last Run Details
lastRun=最終実行
#XFLD: ECN Previous Run Details
previousRun=以前の実行
#XFLD: ECN Manage Button
manage=Elastic Compute ノードを管理
#XFLD: ECN Run Details Widget
days=日
#XFLD: ECN Run Details Widget Title
runDetails=実行詳細
#XFLD: ECN Performance Class Label
performanceClass=パフォーマンスクラス
start=開始
stop=停止
ecnRunDetailsUpTime=稼働時間
ecnConfigurationCPU=vCPU 数
ecnTechnicalName=技術名
currentMonth=当月
previousMonth=先月
ecnBlockHours=ブロック時間
upTimeOverall=合計稼働時間
refresh=リフレッシュ
top5MemConsumptionDataFetchErrorText=メモリ消費のデータを取得できませんでした
ecnDashboardText=Elastic Compute ノード
#XMSG: Message Title when ECN is not in ruuning state
memoryDistributionErrorTitle=適用なし
#XMSG: Warning Message when ECN is not in ruuning state
memoryDistributionErrorMessage=メモリ配分が表示されるのは、Elastic Compute ノードが実行中ステータスにある場合のみです。
#XMSG: Title for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageTitle=Elastic Compute ノードが選択されていません
#XMSG: Description for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageDescription=ヘッダの一覧から弾力性計算ノードを選択して開始してください。
#XMSG: Tab label for Statememt Logs
statementLogsText=文ログ
#XMSG: Tab label for Task Logs
taskLogsText=タスクログ
#MSG: Label for Date Time Quick Filter
labelForTimeQuickFilter=日付と時刻の範囲: 
labelForSpaceQuickFilter=スペース
labelForStatusQuickFilter=ステータス
#XTIT: Cancel Task Started toast msg
cancelTaskStarted=タスクのキャンセルが開始されました
#YMSE: Cancel Task Error
taskCancelError=タスクはすでに実行を終了しているため、キャンセルできません。
#LSA Monitor Tab Name
lsaMonitorText=Object Store
#ID for LSA Storage By Space Widget
hdlfStorageSpace=SAP HANA データレイクファイル: ストレージ使用
#ID for LSA Storage for All Spaces Widget
hdlfStorageSpaceTotal=SAP HANA データレイクファイル: すべてのスペースのストレージ使用
#Labels for LSA Monitor
lsaSparkTableApplicationColumnText=アプリケーション
lsaSparkTableNoOfTasksColumnText=タスク数
sparkApplicationConfigurationTitle=アプリケーション設定
sparkExecutorCPU=実行者 CPU
sparkExecutorMemory=実行者メモリ
sparkDriverCPU=ドライバ CPU
sparkDriverMemory=ドライバメモリ
sparkMaximumCPU=最大 CPU
sparkMaximumMemory=最大メモリ
sparkMinExecutors=最小実行者
sparkMaxExecutors=最大実行者
sparkAppTableNoDataIMTitle=選択されたスペースなし
sparkAppTableNoDataIMDesc=最初にフィルタバーの一覧から選択してください。
TBUnit=TB
sparkTaskTableNoDataIMTitle=利用可能なタスクなし
sparkTaskTableNoDataIMDesc=選択したアプリケーションには利用可能なタスクがありません。別のアプリケーションを選択してください。
taskActivity=タスクアクティビティ
sparkTableNoSearchResultsTitle=検索結果なし
sparkTableNoSearchResultsDesc=検索用語を変更し、もう一度実行してください。
sparkTableNoFilterResultsTitle=フィルタ結果なし
sparkTableNoFilterResultsDesc=フィルタ基準を変更し、もう一度実行してください。
removeFilterText=フィルタをクリア
sortTableText=テーブルをソート
filterTableText=テーブルをフィルタ
apacheSparkTableText=Apache Spark: タスク
closeColumnText=列を閉じる
