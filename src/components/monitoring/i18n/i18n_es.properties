#XTIT: Dynamic Page title
pageTitle=Bienvenido al monitor de sistema
#XTIT: Dynamic Page subtitle
pageSubtitle=Supervise el rendimiento de su sistema e identifique el almacenamiento, las tareas, la falta de memoria y otros problemas.
#XTIT: Messagestrip Enable Statement tracing
messagestripEnableStatementTracing=Para supervisar errores de memoria insuficiente y otra información de sentencias, active el tracing de sentencia costosa en Configuración/Supervisión.
#XFLD: Dashboard text
dashBoardText=Dashboard
#XFLD: TaskLog text
taskLogText=Registros
#XTIT: card title: Storage Distribution
storageDistribution=Almacenamiento en disco utilizado
#XTIT: card title: Memory Distribution
memoryDistribution=Distribución de memoria
#XTIT: card title:Used Disk Capacity
usedStorageCapacity=Disco utilizado por espacios para el almacenamiento
#XTIT: card title: Used Memory Capacity
usedMemoryCapacity=Memoria utilizada por espacios para el almacenamiento
#XTIT: card title:Assigned Disk Capacity
assignedStorageCapacity=Disco asignado a espacios para el almacenamiento
#XTIT: card title: Assigned Memory Capacity
assignedMemoryCapacity=Memoria asignada a espacios para el almacenamiento
#XTIT: card title: Errors
errors=Tareas fallidas
#XTIT: card title: Configuration
ecnConfiguration=Configuración
#XTIT: card title: ECN Monthly Uptime
upTime=Tiempo productivo mensual
#XTIT: card title: ECN Average Memory
ECNAvgMemory=Memoria media
#XTIT: card title: ECN Average CPU
ECNAvgCPU=CPU media
#XTIT: card Sub title: ECN Hours Subtitle
ECNHoursSubtitle=Últimas {0} horas
#XTIT: card Title: ECN Last 5 Start Tasks
ecnTop5StartStop=Últimas 5 tareas de inicio/parada ECN
#XTIT: card Title: ECN Last 5 Stop Tasks
ecnTop5Stop=Últimas 5 tareas de parada ECN
#XTIT: card title: Out of Memory Events (all)
OoMEvents=Errores de memoria insuficiente
#XTIT: card title: MDS
OoMMDSQueries=Errores de memoria insuficiente (solicitudes MDS)
#XTIT: card title: Top 5 Memory Consumption (Statements)
memoryConsumptionStatements=Las 5 sentencias principales por consumo de memoria para el procesamiento
#XTIT: card title: Out of Memory Events (Workload Class) by Space
OoMEventsByWorkloadClass=Los 5 principales errores de memoria insuficiente (clase de carga de trabajo) por espacio
#XTIT: card title: Run Duration
runDuration=Las 5 tareas principales por duración de la ejecución
#XTIT: card title: Top 5 Tasks by Memory Consumption
tasksBymemoryConsumption=Las 5 tareas principales por consumo de memoria para el procesamiento
#XTIT: card title: Memory Consumption
memoryConsumption=Consumo de memoria
#XTIT: card title: Top 5 Memory Consumption (MDS)
memoryConsumptionSAC=Las 5 solicitudes de MDS principales por consumo de memoria para el procesamiento
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Datos de log de auditoría
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Datos administrativos
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Otros datos
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Datos en espacios
#XFLD: ResourceCategory Available Memory
ResourceCategoryAvailableMemory=Memoria no utilizada
#XFLD: ResourceCategory Memory Used by Consumption
ResourceCategoryMemoryConsumption=Memoria utilizada para el procesamiento
#XFLD: ResourceCategory Memory Used by Replication
ResourceCategoryMemoryReplication=Memoria utilizada para la replicación de datos
#XFLD: ResourceCategory unknown
unknown=Desconocido
#XBUT: Card footer view list button
viewList=Ver logs
#NOTR: placeholder for dev-purposes
test=TEST
#XTIT: card bar label: hours
hours=Horas
#XTIT: card subtitle: now
now=Ahora
#XTIT: card subtitle: last 24 hours
last24Hours=Últimas 24 horas
#XTIT: card subtitle: last 48 hours
last48Hours=Últimas 48 horas
#XTIT: card subtitle: last week
lastWeek=Últimos 7 días
#XTIT: card subtitle: last month
lastMonth=Último mes
#XFLD: Close
close=Cerrar
#XFLD: Today
today=Hoy
#XTIT: text for task tab
taskTab=Tareas
#XTIT: text for statement tab
statementTab=Sentencias
#XTIT: text fo nav link in table cell
viewStatements=Ver
#XTIT: text to more link in table cell
more=Más
#XTIT: text for statement dialog header
statementDlgTitle=Sentencia
#XBUT: text for close btn on dialog
closeBtn=Cerrar
#XBUT: text for copy btn on dialog
copyBtn=Copiar
#XTIT: Copy success text on toast msg
copiedToClipboard=Copiado en el portapapeles
#XTIT: test for download btn on dialog
downloadBtn=Descargar

#XTIT: Monitoring table column names
startTime=Hora de inicio
startDate=Fecha de inicio
duration=Duración
objectType=Tipo de objeto
activity=Actividad
spaceName=Nombre de espacio
objectName=Nombre del objeto
peakMemory=Memoria máxima de SAP HANA
peakCPU=Tiempo CPU de SAP HANA
noOfRecords=Registros
usedMemory=Memoria utilizada de SAP HANA
usedDisk=Disco utilizado de SAP HANA
status=Estado
subStatus=Subestado
user=Usuario
targetTable=Tabla de destino
statements=Sentencias
outOfMemory=Memoria insuficiente
taskLogId=ID de log de tareas
statementDetails=Detalles de sentencia
parameters=Parámetros
workloadClass=Clase de carga de trabajo
errorCode=Código de error
errorText=Mensaje de error
dbUser=Usuario de base de datos
connectionID=ID de conexión
statementID=ID de sentencia
elasticComputeNode=Nodo de computación elástico


#Localization for @sap/dragonet-ui5-table-editor node module
#XTIT: Title for column settings dialog
columnsSettingsDialogTitle=Opciones de columnas
#XFLD: Title for Error type
messagesTableType=Tipo
#XFLD: Title for Error Message
messagesTableMessage=Mensaje
#XFLD: Title for filter
filteredBy=Filtrado por:
#XTIT: text for values contained in filter
filterContains=contiene
#XTIT: text for values starting with in filter
filterStartsWith=empieza por
#XTIT: text for values ending with in filter
filterEndsWith=termina con
#XTIT: Title for search in data preview toolbar
toolbarSearch=Buscar
#XBUT: Button to clear filter
clearFilter=Borrar filtro
#XBUT: Button to cancel the operation
cancel=Cancelar
#XBUT: Button to save the operation
ok=OK
#XBUT: Button to restore the data
toolbarRestoreButton=Actualizar
#XBUT: Button to cancel running task
cancelBtnText=Cancelar tarea
#XBUT: tooltip for Button to restore the data
toolbarRestoreButtonTooltip=Actualizar la página
#XMSG: Busy dialog for Columns Settings dialog
PROCESSING=Procesando...
#XMSG: Message Confirmation
confirmation=Confirmación
#XMSG: Message for refresh successful
refreshSuccess=Actualizado correctamente
#XMSG: Message for refresh successful
refreshSuccessful=Actualizado
#XMSG: Message for restore successful
restoreSuccessful=Restaurado correctamente
#XMSG: Error message when null value found on non-nullable field
mdm-emptyValueError="{0}" no puede estar vacío. {1}.
#XTIT: Sort Ascending
mdm-sortAscending=Orden ascendente
#XTIT: Sort Descending
mdm-sortDescending=Orden descendente
#XTIT: Filter
mdm-Filter=Filtro
#XBUT: Button Cancel
mdm-cancel=Cancelar
#XBUT: Button Add
mdm-Add=Añadir
#XMSG: and inside error message
and=y
#XMSG: Error message when no column selected
mdm-allColumnUnselected=Seleccione una o más columnas para continuar.

#Localization keys not needed for System Monitor but required for @sap/dragonet-ui5-table-editor node module to remove console errors
#XTXT: Message information strip for filtering and sorting
mdm-InfoStrip=Las herramientas Filtrar, Ordenar, Eliminar y Opciones de tabla están desactivadas por cambios no guardados. Guarde sus cambios para activarlas.
#XBUT: Button for file upload in mdm data preview
toolbarUploadButton=Cargar archivo
#XBUT: Button for duplicating rows in mdm data preview
toolbarDuplicateButton=Duplicar
#XBUT: Button for deleting rows in mdm data preview
toolbarDeleteButton=Eliminar
#XBUT: Button for adding rows in mdm data preview
toolbarAddButton=Añadir

#XTIT: MDM Empty value Handling in Table Editor Label
mdmInsertMissingString=Inserte el valor de cadena que falta como:
#XTIT: MDM Empty value Handling in Table Editor Null Combobox item
mdmNullText=NULO
#XTIT: MDM Empty value Handling in Table Editor Empty string Combobox item
mdmEmptyStringText=Cadena vacía
#XTXT: Message information strip for Insert Missing String Value
mdmInfoStripMissingString=Los valores de cadena que faltan solo se pueden insertar en columnas de cadena visibles en filas nuevas y editadas. Use las opciones de columna para visualizar todas las columnas relevantes.
#XTXT: Message information strip for filtering sorting and Insert Missing String Value
mdm-InfoStripWithMissingString=Las herramientas Filtrar, Ordenar, Insertar valor de cadena que falta y Configuración de tabla están desactivadas por cambios no guardados. Guarde sus cambios para activarlas.
#XFLD: Open SAP HANA Cockpit
openHanaCockpit=Resumen de la base de datos (cockpit de SAP HANA)
#XBUT: Open SAP HANA Performance Monitor Cockpit
openHanaCockpitPerfMonitor=Monitor de rendimiento
openHanaCockpitPerfMonitorTooltip=Abrir el monitor de rendimiento en el cockpit de SAP HANA
#XTIT: card title: Admission Control Rejection Events
acRejectedEvents=Eventos de rechazo del control de admisión
#XTIT: card title: Admission Control Queuing Events
acQueuedEvents=Eventos en cola del control de admisión

#XTIT: card title: Top 5 Admission Control Rejection Events by Space
acRejectionEventsBySpace=Los 5 eventos de rechazo principales del control de admisión por espacio
#XTIT: card title: Top 5 Admission Control Queuing Events by Space
acQueueingEventsBySpace=Los 5 eventos en cola principales del control de admisión por espacio

#XTIT: payload Table and List texts
payloadDownloadText=Payload descargado
dataSourceText=Fuentes de datos
dataSourcesObject=Nombre
dataSourcesSchema=Nombre de espacio
payloadListHeader=Payloads
payloadStoryIdText=ID de historia
payloadStoryNameText=Nombre de historia
payloadDialogTitleText=Más información
payloadTextAreaTitle=Payload
NO_MDS_DETAIL=No hay datos disponibles
MDS_DATA_FETCH_FAILED=No se ha podido mostrar información sobre las declaraciones MDS debido a un error del servidor.
GBUnit=GB
enabled=Activado
notEnabled=No activado
#XTIT: ECN Monitoring Dashboard Texts
ecnStatus=Estado
ecnBlock=Bloques de capacidad informática
ecnSize=Tamaño
ecnConfigMemory=Memoria
ecnConfigStorage=Almacenamiento
ecnConfigSpacesList=Lista de espacios
#XFLD: ECN phase Ready
ecnReady=Listo
#XFLD: ECN phase Running
ecnRunning=En ejecución
#XFLD: ECN phase Initial
ecnInitial=No listo
#XFLD: ECN phase Starting
ecnStarting=Iniciando
#XFLD: ECN phase Stopping
ecnStopping=Parando
#XFLD: ECN phase Unknown
ecnUnknown=Desconocido
#XFLD: ECN Last Run Details
lastRun=Última ejecución
#XFLD: ECN Previous Run Details
previousRun=Ejecución anterior
#XFLD: ECN Manage Button
manage=Gestionar nodo de cálculo elástico
#XFLD: ECN Run Details Widget
days=días
#XFLD: ECN Run Details Widget Title
runDetails=Detalles de ejecución
#XFLD: ECN Performance Class Label
performanceClass=Clase de rendimiento
start=Iniciar
stop=Detener
ecnRunDetailsUpTime=Tiempo productivo
ecnConfigurationCPU=Número de vCPU
ecnTechnicalName=Nombre técnico
currentMonth=Mes actual
previousMonth=Mes anterior
ecnBlockHours=Horas de bloque
upTimeOverall=Tiempo productivo total
refresh=Actualizar
top5MemConsumptionDataFetchErrorText=Error al obtener datos para el consumo de memoria
ecnDashboardText=Nodos de cálculo elásticos
#XMSG: Message Title when ECN is not in ruuning state
memoryDistributionErrorTitle=No aplicable
#XMSG: Warning Message when ECN is not in ruuning state
memoryDistributionErrorMessage=La distribución de la memoria se muestra solo cuando el nodo de cálculo flexible está en estado En ejecución.
#XMSG: Title for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageTitle=No se ha seleccionado ningún nodo de cálculo elástico
#XMSG: Description for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageDescription=Para empezar, seleccione uno en la lista de la cabecera.
#XMSG: Tab label for Statememt Logs
statementLogsText=Registros de sentencia
#XMSG: Tab label for Task Logs
taskLogsText=Registros de tarea
#MSG: Label for Date Time Quick Filter
labelForTimeQuickFilter=Intervalo de fechas y horas:
labelForSpaceQuickFilter=Espacios
labelForStatusQuickFilter=Estados
#XTIT: Cancel Task Started toast msg
cancelTaskStarted=Se ha iniciado la cancelación de la tarea
#YMSE: Cancel Task Error
taskCancelError=La tarea no puede cancelarse porque ya ha acabado de ejecutarse.
#LSA Monitor Tab Name
lsaMonitorText=Archivo de objetos
#ID for LSA Storage By Space Widget
hdlfStorageSpace=Archivos de SAP HANA Data Lake: utilización del almacenamiento
#ID for LSA Storage for All Spaces Widget
hdlfStorageSpaceTotal=Archivos de SAP HANA Data Lake: utilización del almacenamiento de todos los espacios
#Labels for LSA Monitor
lsaSparkTableApplicationColumnText=Aplicación
lsaSparkTableNoOfTasksColumnText=Número de tareas
sparkApplicationConfigurationTitle=Configuración de aplicación
sparkExecutorCPU=CPU del ejecutor
sparkExecutorMemory=Memoria del ejecutor
sparkDriverCPU=CPU del driver
sparkDriverMemory=Memoria del driver
sparkMaximumCPU=CPU máxima
sparkMaximumMemory=Memoria máxima
sparkMinExecutors=Ejecutores mínimos
sparkMaxExecutors=Ejecutores máximos
sparkAppTableNoDataIMTitle=No se ha seleccionado ningún espacio
sparkAppTableNoDataIMDesc=Para empezar, seleccione uno en la lista de la barra de filtros.
TBUnit=TB
sparkTaskTableNoDataIMTitle=No hay tareas disponibles
sparkTaskTableNoDataIMDesc=No hay tareas disponibles para la aplicación seleccionada. Elija otra aplicación.
taskActivity=Actividad de tarea
sparkTableNoSearchResultsTitle=Sin resultados de búsqueda
sparkTableNoSearchResultsDesc=Cambie el término de búsqueda y vuelva a intentarlo.
sparkTableNoFilterResultsTitle=No Filter Results
sparkTableNoFilterResultsDesc=Change the filter criteria and try again.
removeFilterText=Borrar filtro
sortTableText=Sort Table
filterTableText=Filter Table
apacheSparkTableText=Apache Spark: Tasks
closeColumnText=Cerrar columna
