#XTIT: Dynamic Page title
pageTitle=Benvenuto/a nel Monitor di sistema
#XTIT: Dynamic Page subtitle
pageSubtitle=Monitorare le prestazioni del sistema e identificare problemi di archiviazione, task, memoria esaurita e altri problemi.
#XTIT: Messagestrip Enable Statement tracing
messagestripEnableStatementTracing=Per monitorare gli errori di memoria esaurita e le altre informazioni relative alle istruzioni, abilitare il monitoraggio istruzioni dispendiose in Configurazione/Monitoraggio.
#XFLD: Dashboard text
dashBoardText=Cruscotto
#XFLD: TaskLog text
taskLogText=Registri
#XTIT: card title: Storage Distribution
storageDistribution=Archivio su disco utilizzato
#XTIT: card title: Memory Distribution
memoryDistribution=Distribuzione memoria
#XTIT: card title:Used Disk Capacity
usedStorageCapacity=Disco utilizzato dagli spazi per archivio
#XTIT: card title: Used Memory Capacity
usedMemoryCapacity=Memoria utilizzata dagli spazi per archivio
#XTIT: card title:Assigned Disk Capacity
assignedStorageCapacity=Disco assegnato agli spazi per archivio
#XTIT: card title: Assigned Memory Capacity
assignedMemoryCapacity=Memoria assegnata agli spazi per archivio
#XTIT: card title: Errors
errors=Task non riusciti
#XTIT: card title: Configuration
ecnConfiguration=Configurazione
#XTIT: card title: ECN Monthly Uptime
upTime=Tempo di attività mensile
#XTIT: card title: ECN Average Memory
ECNAvgMemory=Memoria media
#XTIT: card title: ECN Average CPU
ECNAvgCPU=CPU media
#XTIT: card Sub title: ECN Hours Subtitle
ECNHoursSubtitle=Ultime {0} ore
#XTIT: card Title: ECN Last 5 Start Tasks
ecnTop5StartStop=Ultimi 5 task di avvio/arresto ECN
#XTIT: card Title: ECN Last 5 Stop Tasks
ecnTop5Stop=Ultimi 5 task di arresto ECN
#XTIT: card title: Out of Memory Events (all)
OoMEvents=Errori di memoria esaurita
#XTIT: card title: MDS
OoMMDSQueries=Errori di memoria esaurita (richieste MDS)
#XTIT: card title: Top 5 Memory Consumption (Statements)
memoryConsumptionStatements=Prime 5 istruzioni per consumo di memoria per elaborazione
#XTIT: card title: Out of Memory Events (Workload Class) by Space
OoMEventsByWorkloadClass=Primi 5 errori di memoria esaurita (classe del carico di lavoro) per spazio
#XTIT: card title: Run Duration
runDuration=Primi 5 task per durata di esecuzione
#XTIT: card title: Top 5 Tasks by Memory Consumption
tasksBymemoryConsumption=Primi 5 task per consumo di memoria per elaborazione
#XTIT: card title: Memory Consumption
memoryConsumption=Consumo di memoria
#XTIT: card title: Top 5 Memory Consumption (MDS)
memoryConsumptionSAC=Prime 5 richieste MDS per consumo di memoria per elaborazione
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Dati registro di audit
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Dati amministrativi
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Altri dati
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Dati negli spazi
#XFLD: ResourceCategory Available Memory
ResourceCategoryAvailableMemory=Memoria non utilizzata
#XFLD: ResourceCategory Memory Used by Consumption
ResourceCategoryMemoryConsumption=Memoria utilizzata per l'elaborazione
#XFLD: ResourceCategory Memory Used by Replication
ResourceCategoryMemoryReplication=Memoria utilizzata per la replicazione dei dati
#XFLD: ResourceCategory unknown
unknown=Sconosciuto
#XBUT: Card footer view list button
viewList=Visualizza registri
#NOTR: placeholder for dev-purposes
test=TEST
#XTIT: card bar label: hours
hours=Ore
#XTIT: card subtitle: now
now=Ora
#XTIT: card subtitle: last 24 hours
last24Hours=Ultime 24 ore
#XTIT: card subtitle: last 48 hours
last48Hours=Ultime 48 ore
#XTIT: card subtitle: last week
lastWeek=Ultimi 7 giorni
#XTIT: card subtitle: last month
lastMonth=Ultimo mese
#XFLD: Close
close=Chiudi
#XFLD: Today
today=Oggi
#XTIT: text for task tab
taskTab=Task
#XTIT: text for statement tab
statementTab=Istruzioni
#XTIT: text fo nav link in table cell
viewStatements=Visualizza
#XTIT: text to more link in table cell
more=Altro
#XTIT: text for statement dialog header
statementDlgTitle=Istruzione
#XBUT: text for close btn on dialog
closeBtn=Chiudi
#XBUT: text for copy btn on dialog
copyBtn=Copia
#XTIT: Copy success text on toast msg
copiedToClipboard=Copiato nel clipboard
#XTIT: test for download btn on dialog
downloadBtn=Scarica

#XTIT: Monitoring table column names
startTime=Ora di inizio
startDate=Data di inizio
duration=Durata
objectType=Tipo di oggetto
activity=Attività
spaceName=Nome spazio
objectName=Nome oggetto
peakMemory=Memoria massima SAP HANA
peakCPU=Tempo CPU SAP HANA
noOfRecords=Record
usedMemory=Memoria utilizzata SAP HANA
usedDisk=Disco utilizzato SAP HANA
status=Stato
subStatus=Sottostato
user=Utenti
targetTable=Tabella di destinazione
statements=Istruzioni
outOfMemory=Memoria esaurita
taskLogId=ID registro task
statementDetails=Dettagli istruzione
parameters=Parametri
workloadClass=Classe workload
errorCode=Codice errore
errorText=Messaggio di errore
dbUser=Utente database
connectionID=ID connessione
statementID=ID istruzione
elasticComputeNode=Nodo di calcolo elastico


#Localization for @sap/dragonet-ui5-table-editor node module
#XTIT: Title for column settings dialog
columnsSettingsDialogTitle=Impostazioni colonna
#XFLD: Title for Error type
messagesTableType=Tipo
#XFLD: Title for Error Message
messagesTableMessage=Messaggio
#XFLD: Title for filter
filteredBy=Filtrato per:
#XTIT: text for values contained in filter
filterContains=contiene
#XTIT: text for values starting with in filter
filterStartsWith=inizia con
#XTIT: text for values ending with in filter
filterEndsWith=finisce con
#XTIT: Title for search in data preview toolbar
toolbarSearch=Cerca
#XBUT: Button to clear filter
clearFilter=Cancella filtro
#XBUT: Button to cancel the operation
cancel=Annulla
#XBUT: Button to save the operation
ok=OK
#XBUT: Button to restore the data
toolbarRestoreButton=Aggiorna
#XBUT: Button to cancel running task
cancelBtnText=Annulla task
#XBUT: tooltip for Button to restore the data
toolbarRestoreButtonTooltip=Aggiorna la pagina
#XMSG: Busy dialog for Columns Settings dialog
PROCESSING=Elaborazione in corso...
#XMSG: Message Confirmation
confirmation=Conferma
#XMSG: Message for refresh successful
refreshSuccess=Aggiornamento riuscito
#XMSG: Message for refresh successful
refreshSuccessful=Aggiornato
#XMSG: Message for restore successful
restoreSuccessful=Ripristino riuscito
#XMSG: Error message when null value found on non-nullable field
mdm-emptyValueError="{0}" non può essere vuoto. {1}.
#XTIT: Sort Ascending
mdm-sortAscending=Classifica in ordine crescente
#XTIT: Sort Descending
mdm-sortDescending=Classifica in ordine decrescente
#XTIT: Filter
mdm-Filter=Filtra
#XBUT: Button Cancel
mdm-cancel=Annulla
#XBUT: Button Add
mdm-Add=Aggiungi
#XMSG: and inside error message
and=e
#XMSG: Error message when no column selected
mdm-allColumnUnselected=Selezionare una o più colonne per continuare.

#Localization keys not needed for System Monitor but required for @sap/dragonet-ui5-table-editor node module to remove console errors
#XTXT: Message information strip for filtering and sorting
mdm-InfoStrip=Gli strumenti di filtro, ordinamento, eliminazione e impostazione tabella sono disattivati da modifiche non salvate. Salvare le modifiche per attivarli.
#XBUT: Button for file upload in mdm data preview
toolbarUploadButton=Carica dati
#XBUT: Button for duplicating rows in mdm data preview
toolbarDuplicateButton=Duplica
#XBUT: Button for deleting rows in mdm data preview
toolbarDeleteButton=Elimina
#XBUT: Button for adding rows in mdm data preview
toolbarAddButton=Aggiungi

#XTIT: MDM Empty value Handling in Table Editor Label
mdmInsertMissingString=Immetti valore stringa mancante come:
#XTIT: MDM Empty value Handling in Table Editor Null Combobox item
mdmNullText=NULL
#XTIT: MDM Empty value Handling in Table Editor Empty string Combobox item
mdmEmptyStringText=Stringa vuota
#XTXT: Message information strip for Insert Missing String Value
mdmInfoStripMissingString=I valori stringa mancanti vengono inseriti solo in colonne stringa visibili. Utilizzare le impostazioni delle colonne per visualizzare tutte le colonne rilevanti.
#XTXT: Message information strip for filtering sorting and Insert Missing String Value
mdm-InfoStripWithMissingString=Gli strumenti di filtro, ordinamento, inserimento di valore stringa mancante e impostazione tabella sono disabilitati a causa di modifiche non salvate. Salvare le modifiche per abilitarli.
#XFLD: Open SAP HANA Cockpit
openHanaCockpit=Panoramica database (cockpit SAP HANA)
#XBUT: Open SAP HANA Performance Monitor Cockpit
openHanaCockpitPerfMonitor=Monitor delle prestazioni
openHanaCockpitPerfMonitorTooltip=Apri monitor delle prestazioni (cockpit SAP HANA)
#XTIT: card title: Admission Control Rejection Events
acRejectedEvents=Eventi di rifiuto del controllo accettazione
#XTIT: card title: Admission Control Queuing Events
acQueuedEvents=Eventi in coda del controllo accettazione

#XTIT: card title: Top 5 Admission Control Rejection Events by Space
acRejectionEventsBySpace=Primi 5 eventi di rifiuto del controllo accettazione in base a spazio
#XTIT: card title: Top 5 Admission Control Queuing Events by Space
acQueueingEventsBySpace=Primi 5 eventi in coda del controllo accettazione in base a spazio

#XTIT: payload Table and List texts
payloadDownloadText=Payload scaricato
dataSourceText=Origini dati
dataSourcesObject=Nome
dataSourcesSchema=Nome spazio
payloadListHeader=Payloads
payloadStoryIdText=ID storia
payloadStoryNameText=Nome storia
payloadDialogTitleText=Ulteriori informazioni
payloadTextAreaTitle=Payload
NO_MDS_DETAIL=Non sono disponibili dati
MDS_DATA_FETCH_FAILED=Impossibile visualizzare le informazioni sulle istruzioni MDS a causa di un errore del server.
GBUnit=GB
enabled=Attivato
notEnabled=Non attivato
#XTIT: ECN Monitoring Dashboard Texts
ecnStatus=Stato
ecnBlock=Blocchi calcolo
ecnSize=Dimensione
ecnConfigMemory=Memoria
ecnConfigStorage=Archivio
ecnConfigSpacesList=Elenco di spazi
#XFLD: ECN phase Ready
ecnReady=Pronto
#XFLD: ECN phase Running
ecnRunning=In esecuzione
#XFLD: ECN phase Initial
ecnInitial=Non pronto
#XFLD: ECN phase Starting
ecnStarting=Avvio in corso
#XFLD: ECN phase Stopping
ecnStopping=Interruzione in corso
#XFLD: ECN phase Unknown
ecnUnknown=Sconosciuto
#XFLD: ECN Last Run Details
lastRun=Ultima esecuzione
#XFLD: ECN Previous Run Details
previousRun=Esecuzione precedente
#XFLD: ECN Manage Button
manage=Gestisci nodo di calcolo elastico
#XFLD: ECN Run Details Widget
days=Giorni
#XFLD: ECN Run Details Widget Title
runDetails=Dettagli esecuzione
#XFLD: ECN Performance Class Label
performanceClass=Classe di prestazione
start=Avvia
stop=Interrompi
ecnRunDetailsUpTime=Tempo di attività
ecnConfigurationCPU=Numero di vCPU
ecnTechnicalName=Nome tecnico
currentMonth=Mese corrente
previousMonth=Mese precedente
ecnBlockHours=Ore di blocco
upTimeOverall=Tempo di attività totale
refresh=Aggiorna
top5MemConsumptionDataFetchErrorText=Impossibile ottenere dati per consumo di memoria
ecnDashboardText=Nodi di calcolo elastico
#XMSG: Message Title when ECN is not in ruuning state
memoryDistributionErrorTitle=Non rilevante
#XMSG: Warning Message when ECN is not in ruuning state
memoryDistributionErrorMessage=Distribuzione della memoria visualizzata solo quando lo stato del nodo di calcolo elastico è In esecuzione.
#XMSG: Title for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageTitle=Nessun nodo di calcolo elastico selezionato
#XMSG: Description for ECN Selection Illustrated Message
ecnSelectionIllustratedMessageDescription=Iniziare selezionandone uno dall'elenco dell'intestazione.
#XMSG: Tab label for Statememt Logs
statementLogsText=Registri istruzioni
#XMSG: Tab label for Task Logs
taskLogsText=Registri task
#MSG: Label for Date Time Quick Filter
labelForTimeQuickFilter=Intervallo data e ora:
labelForSpaceQuickFilter=Spazi
labelForStatusQuickFilter=Stati
#XTIT: Cancel Task Started toast msg
cancelTaskStarted=Annullamento task avviato
#YMSE: Cancel Task Error
taskCancelError=Il task non può essere annullato perché la sua esecuzione è già conclusa.
#LSA Monitor Tab Name
lsaMonitorText=Object Store
#ID for LSA Storage By Space Widget
hdlfStorageSpace=File SAP HANA Data Lake: utilizzo archivio
#ID for LSA Storage for All Spaces Widget
hdlfStorageSpaceTotal=File SAP HANA Data Lake: utilizzo archivio di tutti gli spazi
#Labels for LSA Monitor
lsaSparkTableApplicationColumnText=Applicazione
lsaSparkTableNoOfTasksColumnText=Numero di task
sparkApplicationConfigurationTitle=Configurazione dell'applicazione
sparkExecutorCPU=CPU esecutore
sparkExecutorMemory=Memoria esecutore
sparkDriverCPU=CPU driver
sparkDriverMemory=Memoria driver
sparkMaximumCPU=CPU massima
sparkMaximumMemory=Memoria massima
sparkMinExecutors=Esecutori minimi
sparkMaxExecutors=Esecutori massimi
sparkAppTableNoDataIMTitle=Nessuno spazio selezionato
sparkAppTableNoDataIMDesc=Iniziare selezionandone uno dall'elenco nella barra dei filtri.
TBUnit=TB
sparkTaskTableNoDataIMTitle=Nessun task disponibile
sparkTaskTableNoDataIMDesc=Per l'applicazione selezionata, non sono disponibili task. Selezionare un'altra applicazione.
taskActivity=Attività task
sparkTableNoSearchResultsTitle=Nessun risultato di ricerca
sparkTableNoSearchResultsDesc=Cambiare il termine di ricerca e riprovare.
sparkTableNoFilterResultsTitle=I filtri non hanno restituito risultati
sparkTableNoFilterResultsDesc=Modificare i criteri di filtro e riprovare.
removeFilterText=Cancella filtro
sortTableText=Ordina tabella
filterTableText=Filtra tabella
apacheSparkTableText=Apache Spark: task
closeColumnText=Chiudi colonna
