/**
 * Copyright 2021 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { UI5Require } from "@sap/dwc-uihelper";
import {
  BaseController,
  BaseControllerClass,
  smartExtend,
} from "../../basecomponent/controller/BaseController.controller";
import { createCancelTaskButton } from "../../commonui/utility/CommonUtils";
import { MessageHandler } from "../../reuse/utility/MessageHandler";
import { isRuntimeAvailable } from "../../reuse/utility/Runtime";
import { ContentType, ServiceCall, getBaseUrl } from "../../reuse/utility/ServiceCall";
import { ShellContainer } from "../../shell/utility/Container";
import { setHelpScreenId } from "../../shell/utility/WebAssistantHelper";
import { MonitoringComponentClass } from "../Component";
import "../css/styles.css";
import { LogsFormatter } from "../formatter/LogsFormatter";
import { MDM_FEATURE_LIST, columnDefinitions, dateTimeFilterOptions, statusFilterOptions } from "../utility/Constants";
import { ODataEndpoints, Operator, Views, serviceAvailabilityStatuses } from "../utility/Enums";
import {
  addCustomOptionsForDateTimeFilter,
  createDateTimeFilter,
  filterFilterObjects,
  generateFilters,
  generateQuickFilterObject,
  generateSorters,
  getIndexOfPreviousFilter,
  getLocalizedColumnName,
  getMultiSelectedFilterObjects,
  mapOperator,
  updateStartTimeFilterDateValues,
} from "../utility/TaskLogUtility";
import { MonitoringQueryParameters } from "../utility/Types";

export class TaskLogsClass extends BaseControllerClass {
  private view: sap.ui.core.mvc.View;
  private router: sap.m.routing.Router;
  private selectedView: string;
  private i18nResourceModel: sap.ui.model.resource.ResourceModel;
  private i18nTaskLogModel: sap.ui.model.resource.ResourceModel;
  codeEditor: sap.ui.codeeditor.CodeEditor;
  popUp: sap.m.Popover;
  private ecnTabs: any;
  public DataPreview: any;
  private mdmTableSetting = {
    TASKS: {
      filters: [],
      sorters: [],
      columns: columnDefinitions.TASKS,
    },
  };
  public onInit(): void {
    super.onInit();
    this.setupViews();
    this.setupRouter();
    this.setupModels();
    addCustomOptionsForDateTimeFilter(this.view);
    this.getCancellableTasks();
    UI5Require<typeof sap.ui.codeeditor.CodeEditor>("sap/ui/codeeditor/CodeEditor").then((Ce) => {
      this.codeEditor = new Ce("tasksStatementStringUI", {
        type: "sql",
        maxLines: 20,
        height: "auto",
        lineNumbers: false,
        editable: false,
        width: "auto",
      } as any); // the reuse sap.ui.codeeditor.CodeEditor doesn't have the correct constructor yet....
    });
  }

  private setupViews(): void {
    this.view = this.getView();
    const spaceQuickFilter = this.byId("spaceQuickFilter") as sap.m.MultiComboBox;
    spaceQuickFilter.setFilterFunction((sTerm, oItem) => {
      const sItemText = oItem.getText().toLowerCase(),
        sSearchTerm = sTerm.toLowerCase();
      return sItemText.includes(sSearchTerm);
    });
  }

  private setupRouter(): void {
    this.router = sap.ui.core.UIComponent.getRouterFor(this) as sap.m.routing.Router;
    this.router.getRoute("monitoring").attachPatternMatched(async (event: sap.ui.base.Event) => {
      const query: MonitoringQueryParameters = event.getParameter("arguments")["?query"] as MonitoringQueryParameters;
      const monitoringComponentModel = this.getOwnerComponent().getModel("monitoringComponent");
      monitoringComponentModel.setProperty("/selectedTab", query.view);
      const component = sap.ui.core.routing.History.getInstance().getPreviousHash()?.split("&")[0];
      if (component && component !== "monitoring") {
        monitoringComponentModel.setProperty("/applicationChanged", true);
      } else {
        monitoringComponentModel.setProperty("/applicationChanged", false);
      }
      if (this.DataPreview) {
        this.DataPreview.destroy();
        this.DataPreview = null;
      }
      if (query.view === Views.TASKLOGS) {
        setHelpScreenId("tasklogs");
        const oDataView = query.oDataView?.split("/")[1] || "TASKS";
        let isOOMView = false;
        if (oDataView === "OOM") {
          isOOMView = true;
        }
        this.selectedView = oDataView;
        this.clearQuickFilters();
        this.mdmTableSetting[this.selectedView].filters = query.filter ? generateFilters(query.filter) : [];
        this.mdmTableSetting[this.selectedView].sorters = query.orderBy ? generateSorters(query.orderBy) : [];
        if (isOOMView) {
          this.mdmTableSetting[this.selectedView].filters.push({
            key: "OOM",
            text: `${mapOperator("EQ")}${(sap.ui.getCore() as any).getLibraryResourceBundle().getText("YES")}`,
            exclude: false,
            operation: sap.ui.model.FilterOperator.EQ,
            keyField: "OOM",
            value1: true,
            value2: null,
            showIfGrouped: true,
          });
        }
        this.loadMonitoringTable();
      }
    });
  }

  private async getCancellableTasks(): Promise<void> {
    try {
      const cancellableTasks = await ServiceCall.get<any>("tf/tasks/cancellable");
      this.getOwnerComponent().getModel("monitoringComponent").setProperty("/cancellableTasks", cancellableTasks?.data);
    } catch (error) {
      return;
    }
  }

  private async fetchSpacesList(): Promise<void> {
    const url = `repository/search/$all?$top=100000&$skip=0&$select=name&$orderby=name&$apply=filter(Search.search(query='SCOPE:SEARCH_SPACE (NOT space_type:EQ(S):marketplace)'))`;
    const spacesList = (await ServiceCall.get(url)) as any;
    this.getOwnerComponent()
      .getModel("quickFilterModel")
      .setProperty(
        "/spacesFilterOptions",
        spacesList?.data?.value?.map((space) => ({ SPACE_ID: space.name }))
      );
  }

  private async setupModels(): Promise<void> {
    this.i18nResourceModel = new sap.ui.model.resource.ResourceModel({
      bundleName: "sap.cdw.components.monitoring.i18n.i18n",
    });
    this.i18nTaskLogModel = new sap.ui.model.resource.ResourceModel({
      bundleName: require("../../tasklog/i18n/i18n.properties"),
    });
    this.view.setModel(this.i18nResourceModel, "i18n");
    this.view.setModel(this.i18nTaskLogModel, "i18n_taskLog");
    this.ecnTabs = this.getOwnerComponent()
      .getModel("monitoringComponent")
      .getProperty("/ecns")
      ?.map((item) => item.ecnId);
    sap.ui.getCore().setModel(new sap.ui.model.json.JSONModel(this.ecnTabs), "ecnTabs");
    this.view.setModel(sap.ui.getCore().getModel("featureflags"), "featureflags");
    this.getOwnerComponent().setModel(new sap.ui.model.json.JSONModel({}), "quickFilterModel");
    const quickFilterModel = this.getOwnerComponent().getModel("quickFilterModel");
    quickFilterModel.setSizeLimit(1000);
    quickFilterModel.setProperty("/dateTimeFilterOptions", dateTimeFilterOptions);
    quickFilterModel.setProperty("/statusFilterOptions", statusFilterOptions);
    this.fetchSpacesList();
  }

  public getText(key: string, parameters?: string[], model: string = "i18n"): string {
    return key
      ? (this.view.getModel(model) as sap.ui.model.resource.ResourceModel).getResourceBundle().getText(key, parameters)
      : key;
  }

  onTimeQuickFilterChange(oEvent: sap.ui.base.Event): void {
    let oDynamicDateRange = oEvent["oSource"],
      bValid = oEvent.getParameter("valid"),
      oValue,
      oFilter;
    if (bValid) {
      oValue = oEvent.getParameter("value");
      if (!oValue) {
        const filtersToKeep = filterFilterObjects(this.mdmTableSetting[this.selectedView].filters, "START_TIME");
        this.mdmTableSetting[this.selectedView].filters = filtersToKeep;
      } else {
        oFilter = createDateTimeFilter(oDynamicDateRange, oValue);
        let operator = sap.ui.model.FilterOperator.BT;
        const oFilterISO = oFilter.map((date) => date.oDate);
        const previousFilterIndex = getIndexOfPreviousFilter(
          this.mdmTableSetting[this.selectedView].filters,
          "START_TIME"
        );
        if (oFilter.length === 1 && (oValue.operator === "TO" || oValue.operator === "TODATETIME")) {
          operator = sap.ui.model.FilterOperator.LE;
        } else if (oFilter.length === 1 && (oValue.operator === "FROM" || oValue.operator === "FROMDATETIME")) {
          operator = sap.ui.model.FilterOperator.GE;
        }
        const filterObject = generateQuickFilterObject("START_TIME", operator, oFilterISO, "dateTime");
        if (previousFilterIndex === -1) {
          this.mdmTableSetting[this.selectedView].filters.push(filterObject);
        } else {
          this.mdmTableSetting[this.selectedView].filters[previousFilterIndex] = filterObject;
        }
      }
      this.loadMonitoringTable();
    }
  }

  handleFilterSelectionFinish(oEvent: sap.ui.base.Event): void {
    const oSource = oEvent.getSource();
    const oValue = oSource["getSelectedKeys"]();
    let filterKey;
    const filterFrom = oSource.getId().split("--")[4];
    filterKey = filterFrom === "spaceQuickFilter" ? "SPACE_ID" : filterKey;
    filterKey = filterFrom === "statusQuickFilter" ? "STATUS" : filterKey;
    let otherFilters = filterFilterObjects(this.mdmTableSetting[this.selectedView].filters, filterKey);
    otherFilters = updateStartTimeFilterDateValues(otherFilters);
    let allFilters = [];
    const filterObjects = getMultiSelectedFilterObjects(filterKey, oValue);
    allFilters = [...otherFilters, ...filterObjects];
    oSource["setSelectedItems"](oSource["getSelectedItems"]());
    if (!this.compareIfFiltersAreEqual(this.mdmTableSetting[this.selectedView].filters, allFilters)) {
      this.mdmTableSetting[this.selectedView].filters = allFilters;
      this.loadMonitoringTable();
    }
  }

  compareIfFiltersAreEqual(filters1: any[], filters2: any[]) {
    if (filters1.length !== filters2.length) {
      return false;
    } else {
      return filters1.every((filter1) =>
        filters2.some((filter2) => filter1.key === filter2.key && filter1.value1 === filter2.value1)
      );
    }
  }

  loadMonitoringTable() {
    if (isRuntimeAvailable()) {
      if (this.DataPreview) {
        this.DataPreview.destroy();
        this.DataPreview = null;
      }
      const content = this.getView().byId("monitoringTasksOverview") as sap.m.Page;
      content.removeAllContent();
      sap.ui.require(["sap/mdm/tableEditor/Editor"], (PreviewEditor: any) => {
        if (!this.DataPreview) {
          this.DataPreview = this.createMDMEditor(PreviewEditor);
          this.DataPreview.setModel(this.i18nResourceModel, "i18n");
          this.DataPreview.resolveLabel = this.resolveLabel.bind(this);
          const cancelTaskBtn = createCancelTaskButton();
          cancelTaskBtn.attachPress(onCancelTaskBtn.bind(this));
          this.DataPreview.extendPreviewToolbar(cancelTaskBtn, 1);
          if (this.DataPreview.getTable()?.getExtension()) {
            this.DataPreview.getTable()
              .getExtension()
              .forEach((ext: any) => {
                if (ext.resolveLabel) {
                  ext.resolveLabel = this.resolveLabel.bind(this);
                }
              });
          }
          content.addContent(this.DataPreview);
        }
      });
    }
  }

  resolveLabel(columnName: string) {
    return this.getText(getLocalizedColumnName(columnName, this.selectedView));
  }

  mdmTableSettingChange(event: sap.ui.base.Event) {
    this.clearQuickFilters();
    this.mdmTableSetting[this.selectedView] = event.getParameter("setting");
  }

  clearQuickFilters() {
    this.byId("timeQuickFilter").setValue(null);
    this.byId("spaceQuickFilter").setSelectedKeys([]);
    this.byId("statusQuickFilter").setSelectedKeys([]);
    if (this.mdmTableSetting[this.selectedView]) {
      this.mdmTableSetting[this.selectedView].filters = [];
    }
  }

  public createMDMEditor(PreviewEditor): any {
    const MDM_FEATURE_FLAGS = {};
    MDM_FEATURE_LIST.forEach((el) => {
      MDM_FEATURE_FLAGS[el] = true;
    });
    const entityName = this.selectedView;
    const configurations: {
      columnFields: Record<string, any>;
      fullPageAppReadMode?: boolean;
      useLocalTimeZone?: boolean;
    } = this.getColumnConfigurations(this.selectedView);
    configurations.fullPageAppReadMode = true;
    configurations.useLocalTimeZone = true;
    const MDMEditor = new PreviewEditor({
      serviceUrl: `${getBaseUrl()}monitoring/${entityName}/`,
      previewModelName: entityName,
      previewModelLabel: entityName,
      mdmFeatureFlags: MDM_FEATURE_FLAGS,
      configurations,
      editable: false,
      maxColumns: 11,
      mdmTableSettingChange: this.mdmTableSettingChange.bind(this),
      mdmTableSetting: this.mdmTableSetting[this.selectedView],
      tableSelectionMode: sap.ui.table.SelectionMode.Single,
    });
    MDMEditor.getTable().attachEvent("rowSelectionChange", "customHandler", () => {
      const selectedIndices = MDMEditor.getTable().getSelectedIndices();
      if (selectedIndices.length === 0) {
        this.getOwnerComponent().getModel("monitoringComponent").setProperty("/canCancel", false);
        return;
      } else {
        selectedIndices.forEach((index) => {
          const rowContext = MDMEditor.getTable().getContextByIndex(index);
          const rowData = rowContext.getObject();
          const cancellableTasks = this.getOwnerComponent()
            .getModel("monitoringComponent")
            .getProperty("/cancellableTasks");
          const isCancellable = cancellableTasks.some(
            (task) =>
              task.applicationId === rowData.APPLICATION_NAME &&
              task.activity === rowData.ACTIVITY &&
              rowData.STATUS === "RUNNING"
          );
          this.getOwnerComponent().getModel("monitoringComponent").setProperty("/canCancel", isCancellable);
          this.getOwnerComponent().getModel("monitoringComponent").setProperty("/cancelledItem", rowData);
        });
      }
    });
    return MDMEditor;
  }

  private getColumnConfigurations(entity: string) {
    const getFilesizeCell = (column: string) =>
      new sap.m.Text(undefined, {
        text: {
          path: `data>${column}`,
          formatter: (value) =>
            value ? LogsFormatter.getFormattedFilesize.apply(this, [value.replace(/,/g, "")]) : value,
        },
        wrapping: false,
      });
    const getTimestampCellms = (column: string) =>
      new sap.m.Text(undefined, {
        text: {
          path: `data>${column}`,
          formatter: (value) =>
            value ? LogsFormatter.getFormattedTimeStampMs.apply(this, [value.replace(/,/g, "")]) : value,
        },
        wrapping: false,
      });
    const getTimestampCellsec = (column: string) =>
      new sap.m.Text(undefined, {
        text: {
          path: `data>${column}`,
          formatter: (value) =>
            value ? LogsFormatter.getFormattedTimeStampSec.apply(this, [value.replace(/,/g, "")]) : value,
        },
        wrapping: false,
      });
    const getStartTime = (column: string) =>
      new sap.m.Text(undefined, {
        text: {
          path: `data>${column}`,
        },
        wrapping: false,
      });
    const numberFormattedValue = (value: string) => {
      if (value) {
        return LogsFormatter.getNumberFormattedValue.apply(this, [value.replace(/,/g, "")]);
      }
      return "";
    };
    if (entity === "TASKS") {
      return {
        columnFields: {
          START_TIME: () => getStartTime("START_TIME"),
          RUN_TIME: () => getTimestampCellsec("RUN_TIME"),
          PEAK_MEMORY: () => getFilesizeCell("PEAK_MEMORY"),
          PEAK_CPU: () => getTimestampCellms("PEAK_CPU"),
          RECORDS: () =>
            new sap.m.Text({
              text: {
                path: `data>RECORDS`,
                formatter: (value) => numberFormattedValue(value),
              },
            }),
          USED_IN_MEMORY: () => getFilesizeCell("USED_IN_MEMORY"),
          USED_IN_DISK: () => getFilesizeCell("USED_IN_DISK"),
          TASK_LOG_ID: () =>
            new sap.m.Text({
              text: {
                path: `data>TASK_LOG_ID`,
                formatter: (value) => numberFormattedValue(value),
              },
            }),
          ACTIVITY: function () {
            return new sap.m.Link(undefined, {
              text: {
                parts: [{ path: `data>ACTIVITY` }, { path: `data>TASK_LOG_ID` }, { path: `data>START_TIME` }],
                formatter: function () {
                  let activity = "";
                  let deepseaServiceAvailability: Boolean;
                  if (
                    sap.ui.getCore().getModel("circuitbreaker").getProperty("/Deepsea") ===
                    serviceAvailabilityStatuses.AVAILABLE
                  ) {
                    deepseaServiceAvailability = true;
                  } else {
                    deepseaServiceAvailability = false;
                  }
                  if (this.getParent()?.getRowBindingContext()) {
                    const objectTypeMapping = {
                      REMOTE_TABLES: "remoteTableMonitor",
                      VIEWS: "viewMonitor",
                      DATA_FLOWS: "dataFlowMonitor",
                      TASK_CHAINS: "taskChainMonitor",
                      REMOTE_TABLE_STATISTICS: "remoteTableStatisticsMonitor",
                      TRANSFORMATION_FLOWS: "transformationFlowMonitorDetails",
                      REPLICATION_FLOWS: "replicationFlowMonitorDetails",
                    };

                    const localTableEnabledActivities = [
                      "DELETE_DATA",
                      "REMOVE_DELETED_RECORDS",
                      "MERGE_FILES",
                      "OPTIMIZE_FILES",
                      "FIND_AND_REPLACE",
                      "VACUUM_FILES",
                      "TRUNCATE_FILES",
                    ];
                    const space = this.getParent().getRowBindingContext().getProperty("SPACE_ID");
                    let objectType = this.getParent().getRowBindingContext().getProperty("APPLICATION_NAME");
                    const objectId = this.getParent().getRowBindingContext().getProperty("OBJECT_ID");
                    const taskId = this.getParent().getRowBindingContext().getProperty("TASK_LOG_ID");
                    activity = this.getParent().getRowBindingContext().getProperty("ACTIVITY");
                    if (objectType === "REMOTE_TABLES" && activity?.includes("STATISTICS")) {
                      objectType = "REMOTE_TABLE_STATISTICS";
                    }

                    if (objectTypeMapping[objectType] && deepseaServiceAvailability) {
                      (this as sap.m.Link).setEnabled(true);
                      (this as sap.m.Link).setHref(
                        `#/dataintegration&/di/${space}/${objectTypeMapping[objectType]}/${objectId}/${taskId}`
                      );
                    } else {
                      (this as sap.m.Link).setEnabled(false);
                    }
                    if (
                      deepseaServiceAvailability &&
                      space === "$$ecn$$" &&
                      ShellContainer.get().getPrivilegeService().getPrivilegesByType("SYSTEMINFO").read
                    ) {
                      if (objectType === "TASK_CHAINS") {
                        (this as sap.m.Link).setHref(`#/monitoring&/m/taskchain/${objectId}/${taskId}`);
                      } else {
                        (this as sap.m.Link).setHref(`#/monitoring&/m/logs/${objectType}/${objectId}/${taskId}`);
                      }
                      (this as sap.m.Link).setEnabled(true);
                    }
                    if (
                      deepseaServiceAvailability &&
                      ((activity?.includes("VALIDATE") && objectType === "VALIDATION_SERVICE") ||
                        (activity?.includes("UPLOAD_DATA") && objectType === "LOCAL_TABLE") ||
                        (activity?.includes("RUN") && objectType === "SQL_SCRIPT_PROCEDURE") ||
                        (activity?.includes("RUN") && objectType === "BW_PROCESS_CHAIN") ||
                        (activity?.includes("TEST_RUN") && objectType === "API") ||
                        (activity?.includes("RUN") && objectType === "API") ||
                        (activity?.includes("SEND_EMAIL") &&
                          objectType === "NOTIFICATION" &&
                          sap.ui
                            .getCore()
                            .getModel("featureflags")
                            ?.getProperty("/DWCO_TASK_FRAMEWORK_NOTIFICATION_TASK")) ||
                        (activity?.includes("VALIDATE") &&
                          objectType === "VIEWS" &&
                          sap.ui.getCore().getModel("featureflags")?.getProperty("/DWCO_MODEL_VALIDATION")))
                    ) {
                      (this as sap.m.Link).setHref(`#/monitoring&/m/logdetails/${space}/${objectType}/${taskId}`);
                      (this as sap.m.Link).setEnabled(true);
                    }
                    if (
                      deepseaServiceAvailability &&
                      objectType === "LOCAL_TABLE" &&
                      localTableEnabledActivities.includes(activity)
                    ) {
                      (this as sap.m.Link).setHref(
                        `#/dataintegration&/di/${space}/localTableMonitor/${objectId}/${taskId}`
                      );
                      (this as sap.m.Link).setEnabled(true);
                      if (
                        activity === "MERGE_FILES" ||
                        activity === "OPTIMIZE_FILES" ||
                        activity === "VACUUM_FILES" ||
                        activity === "FIND_AND_REPLACE" ||
                        activity === "TRUNCATE_FILES"
                      ) {
                        (this as sap.m.Link).setEnabled(false);
                      }
                    }
                    if (
                      deepseaServiceAvailability &&
                      activity === "DELETE_DATA" &&
                      objectType === "LOCAL_TABLE_VARIANT"
                    ) {
                      const objecttype = objectId.split(":")[0];
                      (this as sap.m.Link).setHref(
                        `#/dataintegration&/di/${space}/localTableMonitor/${objecttype}/${taskId}`
                      );
                      (this as sap.m.Link).setEnabled(true);
                    }
                    if (deepseaServiceAvailability && objectType === "REPLICATION_FLOWS") {
                      (this as sap.m.Link).setHref(
                        `#/dataintegration&/di/${space}/${objectTypeMapping[objectType]}/${encodeURIComponent(
                          objectId
                        )}/${taskId}`
                      );
                      (this as sap.m.Link).setEnabled(true);
                    }
                  }
                  return activity;
                },
              },
            });
          },
          OBJECT_ID: function () {
            return new sap.m.Link(undefined, {
              text: {
                parts: [{ path: `data>OBJECT_ID` }, { path: `data>TASK_LOG_ID` }, { path: `data>START_TIME` }],
                formatter: function (value: string | null) {
                  let objectId = "";
                  let deepseaServiceAvailability: Boolean;
                  if (
                    sap.ui.getCore().getModel("circuitbreaker").getProperty("/Deepsea") ===
                    serviceAvailabilityStatuses.AVAILABLE
                  ) {
                    deepseaServiceAvailability = true;
                  } else {
                    deepseaServiceAvailability = false;
                  }
                  if (this.getParent()?.getRowBindingContext()) {
                    const allowedObjectType = [
                      "REMOTE_TABLES",
                      "VIEWS",
                      "DATA_FLOWS",
                      "INTELLIGENT_LOOKUP",
                      "TASK_CHAINS",
                      "TRANSFORMATION_FLOWS",
                      "LOCAL_TABLE",
                      "ELASTIC_COMPUTE_NODE",
                      "REPLICATION_FLOWS",
                    ];
                    const space = this.getParent().getRowBindingContext().getProperty("SPACE_ID");
                    const activity = this.getParent().getRowBindingContext().getProperty("ACTIVITY");
                    const objectType = this.getParent().getRowBindingContext().getProperty("APPLICATION_NAME");
                    objectId = this.getParent().getRowBindingContext().getProperty("OBJECT_ID");
                    if (allowedObjectType.includes(objectType)) {
                      const ecnTabs =
                        Object.keys(sap.ui.getCore().getModel("ecnTabs")?.["oData"]).length > 0
                          ? sap.ui.getCore().getModel("ecnTabs")?.["oData"]
                          : [];
                      deepseaServiceAvailability
                        ? (this as sap.m.Link).setEnabled(true)
                        : (this as sap.m.Link).setEnabled(false);
                      if (space === "$$ecn$$" && activity === "RUN_CHAIN_TECHNICAL") {
                        const objectName = objectId;
                        const ecnName = objectName.includes("_START")
                          ? objectName.slice(0, -6)
                          : objectName.slice(0, -5);
                        if (ecnTabs.includes(ecnName)) {
                          (this as sap.m.Link).setHref(`#/managespaces&/ms/overview?view=tile&ecn=${ecnName}`);
                        } else {
                          (this as sap.m.Link).setEnabled(false);
                        }
                      } else if (
                        deepseaServiceAvailability &&
                        space === "$$ecn$$" &&
                        objectType === "ELASTIC_COMPUTE_NODE" &&
                        ecnTabs.includes(objectId)
                      ) {
                        (this as sap.m.Link).setEnabled(true);
                        (this as sap.m.Link).setHref(`#/managespaces&/ms/overview?view=tile&ecn=${objectId}`);
                      } else if (
                        deepseaServiceAvailability &&
                        space === "$$ecn$$" &&
                        (activity === "CREATE_REPLICA" || activity === "DROP_REPLICA")
                      ) {
                        const splittedObject = objectId.split(".");
                        const ecnSpaceName = splittedObject[0];
                        const ecnObjectName =
                          splittedObject.length > 2 ? splittedObject.slice(1).join(".") : splittedObject[1];
                        (this as sap.m.Link).setEnabled(true);
                        (this as sap.m.Link).setHref(`#/databuilder&/db/${ecnSpaceName}/${ecnObjectName}`);
                      } else if (space === "$$ecn$$") {
                        (this as sap.m.Link).setEnabled(false);
                      } else if (
                        space === "$$ecn$$" &&
                        (activity === "ADD" ||
                          activity === "REMOVE" ||
                          activity === "GENERATE_START_CHAIN" ||
                          activity === "RUN_CHAIN_TECHNICAL" ||
                          activity === "GENERATE_STOP_CHAIN")
                      ) {
                        (this as sap.m.Link).setEnabled(false);
                      } else if (space === "$$ecn$$" && !ecnTabs?.includes(objectId)) {
                        (this as sap.m.Link).setEnabled(false);
                      } else if (objectType === "REPLICATION_FLOWS") {
                        (this as sap.m.Link).setEnabled(true);
                        (this as sap.m.Link).setHref(`#/databuilder&/db/${space}/${encodeURIComponent(objectId)}`);
                      } else {
                        deepseaServiceAvailability
                          ? (this as sap.m.Link).setEnabled(true)
                          : (this as sap.m.Link).setEnabled(false);
                        (this as sap.m.Link).setHref(`#/databuilder&/db/${space}/${objectId}`);
                      }
                    } else if (
                      deepseaServiceAvailability &&
                      space === "$$ecn$$" &&
                      objectType === "SPACE" &&
                      (activity === "ROUTE_COORDINATOR" || activity === "ROUTE_COMPUTE_SERVER")
                    ) {
                      (this as sap.m.Link).setEnabled(true);
                      (this as sap.m.Link).setHref(`#/managespaces&/ms/${objectId}`);
                    } else if (
                      deepseaServiceAvailability &&
                      activity === "DELETE_DATA" &&
                      objectType === "LOCAL_TABLE_VARIANT"
                    ) {
                      const objecttype = objectId.split(":")[0];
                      (this as sap.m.Link).setEnabled(true);
                      (this as sap.m.Link).setHref(`#/databuilder&/db/${space}/${objecttype}`);
                    } else {
                      (this as sap.m.Link).setEnabled(false);
                    }
                  }
                  return objectId;
                },
              },
            });
          },
          STATEMENTS: function () {
            return new sap.m.Link(undefined, {
              text: {
                path: `data>STATEMENTS`,
                formatter: function () {
                  const isStatementTracingEnabled = this.getParent()
                    .getModel("monitoringComponent")
                    ?.getProperty("/isStatementTracingEnabled");
                  if (!isStatementTracingEnabled) {
                    (this as sap.m.Link).setEnabled(false);
                  }
                  return this.getModel("i18n").getProperty("viewStatements");
                },
              },
              press: (event: any) => {
                const oData = {
                  label: "[SPACE_ID].[OBJECT_ID] ([TASK_LOG_ID])",
                  value: "TASK_LOG_ID",
                  endpoint: ODataEndpoints.STATEMENTS,
                  $filter: {
                    TASK_LOG_ID: {
                      operator: Operator.EQUAL,
                      operand: event.getSource().getParent().getBindingContext("data").getProperty("TASK_LOG_ID"),
                    },
                  },
                };
                this.router.navTo(MonitoringComponentClass.NAME, {
                  query: {
                    view: Views.STATEMENTSLOGS,
                    oDataView: oData.endpoint,
                    filter: JSON.stringify(oData.$filter),
                  } as MonitoringQueryParameters,
                });
              },
            });
          }.bind(this),
          STATUS: function () {
            return new sap.m.Text(undefined, {
              text: {
                path: `data>STATUS`,
                formatter: function (value: string | null) {
                  if (value === "FAILED") {
                    this.addStyleClass("m_errorText").removeStyleClass("m_successText").removeStyleClass("m_infoText");
                  } else if (value === "RUNNING") {
                    (this as sap.m.Text)
                      .addStyleClass("m_infoText")
                      .removeStyleClass("m_errorText")
                      .removeStyleClass("m_successText");
                  } else if (value === "COMPLETED") {
                    this.addStyleClass("m_successText").removeStyleClass("m_errorText").removeStyleClass("m_infoText");
                  }
                  return value;
                },
              },
              wrapping: false,
            });
          },
          SUB_STATUS: function () {
            return new sap.m.Link(undefined, {
              text: {
                path: `data>SUB_STATUS`,
                formatter: function (value: string | null) {
                  if (value) {
                    this.addStyleClass("m_errorText");
                    this.addStyleClass("linkBlur");
                    return value;
                  }
                  return "";
                },
              },
              press: (event: any) => {
                const popUpId = "subStatusPopUp";
                const popUpOldRef = sap.ui.getCore().byId(popUpId);
                if (popUpOldRef) {
                  popUpOldRef.destroy();
                }
                const subStatusValue = event
                  .getSource()
                  .getParent()
                  .getBindingContext("data")
                  .getProperty("SUB_STATUS");
                const subStatusDescription = this.getView()
                  .getModel("i18n_taskLog")
                  .getProperty(`${subStatusValue}_ERR`);
                this.oText = new sap.m.Text({ text: subStatusDescription });
                this.popUp = new sap.m.Popover(popUpId, {
                  showHeader: false,
                  placement: sap.m.PlacementType.PreferredBottomOrFlip,
                  content: this.oText,
                  contentWidth: "300px",
                  enableScrolling: false,
                });
                this.popUp.addStyleClass("sapUiContentPadding");
                this.getView().addDependent(this.popUp);
                this.popUp.attachAfterClose(() => {
                  this.popUp.destroyContent();
                });
                this.popUp.openBy(event.getSource());
                event.preventDefault();

                document.querySelector('[id*="--dataTable-vsb"]').addEventListener("scroll", () => {
                  if (this.popUp.isOpen()) {
                    this.popUp.destroyContent();
                    this.popUp.close();
                  }
                });
              },
              wrapping: false,
            });
          }.bind(this),
        },
      };
    }
  }
}
async function onCancelTaskBtn() {
  const monitoringPage = this.byId("monitoringTasksOverview");
  monitoringPage.setBusyIndicatorDelay(0);
  monitoringPage.setBusy(true);
  sap.m.MessageToast.show(this.getText("cancelTaskStarted"));
  const cancelledItem = this.getOwnerComponent().getModel("monitoringComponent").getProperty("/cancelledItem");
  const reqBody = {
    applicationId: cancelledItem.APPLICATION_NAME,
    activity: cancelledItem.ACTIVITY,
    objectId: cancelledItem.OBJECT_ID,
    spaceId: cancelledItem.SPACE_ID,
    logId: cancelledItem.TASK_LOG_ID,
  };
  try {
    const cancelResponse = await ServiceCall.post<any>(
      "tf/cancelexecute",
      {
        contentType: ContentType.APPLICATION_JSON,
      },
      true,
      JSON.stringify(reqBody)
    );
    this.loadMonitoringTable();
    return (cancelResponse as any).data;
  } catch (error) {
    const errorMsg = this.getText("taskCancelError");
    MessageHandler.exception({ exception: error, message: errorMsg, id: "taskCancelError" });
    return;
  } finally {
    this.getOwnerComponent().getModel("monitoringComponent").setProperty("/canCancel", false);
    monitoringPage.setBusy(false);
  }
}
export const TaskLogs = smartExtend(BaseController, "sap.cdw.components.monitoring.controller.TaskLogs", TaskLogsClass);
sap.ui.define(
  "sap/cdw/components/monitoring/controller/TaskLogs.controller",
  ["sap.cdw.components.monitoring.CustomLastXHours"],
  () => TaskLogs
);
