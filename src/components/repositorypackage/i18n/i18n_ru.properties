# https://wiki.wdf.sap.corp/wiki/display/DWAAS/How+to+add+text+classifications+for+translatable+texts
#
# use the following text types for short texts (up to 120 characters)
# XACT Accessibility
# XBUT Button
# XCKL Checkbox
# XCOL Column header
# XFLD Label
# XGRP Group title
# XLNK Hyperlink
# XLOG Log entry
# XLST List box item
# XMIT Menu item
# XMSG Message
# XRBL Radio button
# XSEL Selection
# XTIT Table title
# XTOL Tooltip
#
# for longer texts (more than 120 characters) use
# YINS Instruction
# YMSG Message

#=============from skyline============
#XFLD general tab of package editor
Tab_General=Общее
#XFLD objects tab of package editor
Tab_Objects=Объекты
#XFLD required packages tab of package editor
Tab_RequiredPkgs=Обязательные пакеты
#XFLD label for business name input
BusinessName=Бизнес-имя
#XFLD label for technical name input
TechnicalName=Техническое имя
#XFLD label for purpose input
BusinessPurpose=Бизнес-назначение
#XTIT title of save dialog
Dilaog_Save=Сохранить
#XBUT save button of save dialog
Btn_Save=Сохранить
#XBUT cancel button of save dialog
Btn_Cancel=Отменить
#XFLD title of objects section
Objects=Объекты
#XFLD title of required packages section
RequiredPackages=Обязательные пакеты
#XTIT title of package selection dialog
Dialog_SelectPackages=Выбрать пакеты
#XMSG no data text of package selection dialog
Dilaog_NoDataText=Пакеты репозитария не найдены
#XMSG message of adding objects
Msg_ObjectAdded={0} объекта(ов) добавлено
#XMSG message of removing objects
Msg_ObjectRemoved={0} объекта(ов) удалено
#XMSG message of adding required packages
Msg_RequiredPackageAdded={0} обязательных пакета(ов) добавлено
#XMSG message of removing required packages
Msg_RequiredPackageRemoved={0} обязательных пакета(ов) удалено
#XBUT add button in toolbar
Toolbar_Btn_Add=Добавить
#XBUT remove button in toolbar
Toolbar_Btn_Remove=Удалить
#XCOL status column of objects and required packages table
Table_Col_Status=Результат
#XLNK more link of status details
Status_More_Link=Показать сведения...
#XFLD folder name
FolderName=Имя
#XFLD folder description
FolderDescription=Описание
#XBUT ok button
Btn_OK=ОК
#XFLD title of export location dialog
ExportLocationDialog=Местоположение экспорта
#XFLD lable of category field
Category=Категория
#XFLD label of location field
Location=Целевое местоположение
#XFLD lable of version field
SemanticVersion=Версия
#XFLD label of current exported version
CurrentExportedPackage=Текущая экспортируемая версия
#XFLD label of status field
ExportStatus=Статус
#XFLD tooltip of export button
Tooltip_Export=Экспорт
#XFLD tooltip of save button
Tooltip_Save=Сохранить
#XFLD tooltip of validate button
Tooltip_Validate=Проверить
#XMSG
InvalidVersion=Введите версию в формате "x.y.z".
#XMSG
VersionLowerThanBefore=Версия не может быть ниже предыдущей.
#XMSG
Empty_Version=Введите версию пакета.
#XMSG
Package_Missing_Technical_Name=Введите техническое имя пакета.
#=============from skyline============

#XFLD lable for space combo selection
Spaces=Пространство:
#XFLD section title of landing page
Packages=Пакеты
#XCOL business name column
Table_Col_BusinessName=Бизнес-имя
#XCOL technical name column
Table_Col_TechnicalName=Техническое имя
#XCOL sapce column
Table_Col_Space=Пространство
#XCOL create on column
Table_Col_CreatedOn=Дата создания
#XCOL entity type column
Table_Col_Type=Тип
#XCOL entity type column
Table_Col_Object_Status=Статус
#XMSG message of deleting packages
Msg_PackageRemoved={0} пакета(ов) удалено
#XMIT menu item of all spaces
Op_AllSpaces=Все пространства
#XFLD default business name of a new package
NewPackage_BusinessName=Пакет 1
#XFLD default technical name of a new package
NewPackage_TechnicalName=Package_1
#XFLD default name of a new package displayed in breadcrumb
Breadcrumb_NewPackage_Name=Новый пакет
#XFLD title of object selection dialog
ObjectsDialog_Title=Добавить объекты
#XMSG dependencies are fully resolved
Dependency_Resolved=Готово к добавлению (все зависимости развернуты ({0}))
#XMSG dependencies are partially resolved
Dependency_Partial_Resolved=Готово к добавлению (некоторые зависимости не развернуты ({0}/{1}))
#XMSG dependencies are not found
Dependency_Not_Found=Зависимости не найдены
#XMSG dependencies are in other package
Dependency_In_Other_Package=Невозможно добавить: уже в пакете "{0}"
#XMSG dependencies are in other space
Dependency_In_Other_Space=Невозможно добавить: в пространстве {0}
#XMSG
Cannot_Add_Managed_Content=Невозможно добавить: управляемый контент
#XMSG
Cannot_Add_Due_To_Dependency_Has_Managed_Content=Невозможно добавить: зависимость имеет управляемый контент
#XMSG dependencies are in current space
Dependency_In_Current_Package=Уже в пакете
#XMSG dependencies are in required package
Dependency_In_Required_Package=Уже в обязательном пакете
#XMSG package arelady exists
Package_Duplicate_Name=Пакет "{0}" уже есть в репозитарии. Введите другое имя.
#XMSG package name is required
Package_Name_Missing=Введите имя пакета.
#XMSG package version is required
Package_Version_Missing=Введите версию пакета.
#XMSG package is drafted
Package_Draft_Warning=Черновик пакета. Нажмите "Сохранить", чтобы проверить зависимости и подтвердить изменения.
#XFLD title of validation message dialog
Validation_MessageBox_Title=Сообщения о проверке
#XMSG content message of validation message dialog
Validation_MessageBox_Content="{0}"недействительный и не может быть экспортирован. Полностью разверните все зависимости объекта, добавив необходимые объекты вручную или через обязательный пакет.
#XBUT save anyway button
Save_Anyway=Все равно сохранить
#XMSG package is valid
Valid_Package_Message=Пакет действителен.
#XMSG
Object_In_Other_Pacakge=Объект "{0}" в пакете "{1}".
#XMSG
Dependent_Object_In_Other_Pacakge=Объект "{0}" в пакете "{1}", заданном как обязательный пакет.
#XMSG
Dependent_Object_Missing=Объект "{0}" в пространстве {1}.
#XMSG
Dependent_Missing_Pattern=Объект "{0}" зависит от:\r\n{1}
#XMSG: error message for saving the package
Save_Error_Message=Не удалось сохранить пакет. {0}
#XFLD title of save busy dialog
Title_Save_Busy_Dialog=Сохранение
#XMSG: message of it takes long time to save
Long_Time_Save_Busy_Dialog=Сохранение занимает длительное время. Ожидайте.
#XMSG: erro message of missing dependency
Dependency_Missing=Зависимость отсутствует.
#XMSG: erro message of missing dependency
Object_Dependency_Missing=Зависимость "{0}" отсутствует.
#XMSG message of validating busy dialog
Validating_Package=Проверяем пакет
#XFLD title of validating busy dialog
Title_Validating_Busy_Dialog=Обработка
#XMSG %%0 is a placeholder, don’t change it
Missing_Dependency=Отсутствующие зависимости %%0
#XMSG %%0 is a placeholder, don’t change it
Circular_Dependency=Циклическая зависимость %%0
#XMSG Object is added to package successfully
Successfully_Add=Добавлено
#XMSG
Circular_Dependency_Detail="{0}": невозможно сохранить из-за циклической зависимости с пакетом "{1}".
#XFLD label of searching objects in space
Search_In_Sapce=Поиск в: {0}
#XFLD title of deleting package confirm dialog
Package_Deleting_Dialog_Title=Удалить
#XMSG content message of deleting package confirm dialog
Package_Deleting_Dialog_Message=Удалить выбранные пакеты?
#XMSG
Package_Cannot_Delete=Выбранный пакет обязателен для других пакетов, удаление невозможно.
#XMSG
Package_Required_By_Singular=(обязательно для {0} пакета)
#XMSG
Package_Required_By_Plural=(обязательно для {0} пакетов)
#XFLD display name of owner column
owner=Владелец
#XFLD title of deleting package error dialog
Deleting_Error_Dialog_Title=Ошибка
#XFLD title of deleting package alert dialog
Deleting_Alert_Dialog_Title=Предупреждение
#XBUT
Deleting_Alert_Dialog_Ok=ОК
#XFLD
MyContent=Мой контент
#XFLD
NotExported=Не экспортировано
#XFLD
Exporting=Экспорт
#XFLD
Exported=Экспортировано
#XFLD
DesignTimeError=Ошибка времени дизайна
#XFLD
ExportFailed=Экспорт не удался
#XFLD
Cancelling=Отменяем
#XFLD
ChangesToExport=Изменения для экспорта
#XMSG
Exporting_Package=Экспорт {0}. Вы получите уведомление о завершении.
#XFLD: Object type is businessEntityVariant
TypeBusinessEntityVariant=Версия бизнес-сущности
#XFLD: Object type is businessEntity
TypeBusinessEntity=Бизнес-сущность
#XFLD
Tooltip_Add=Добавить
#XFLD
Tooltip_Edit=Редактировать
#XFLD
Tooltip_Delete=Удалить
#XFLD
Tooltip_Refresh=Обновить
#XMSG
ExportToOverwrite=Этот пакет уже был экспортирован в сеть контента с версией {0}. Перезаписать его?
# XFLD Column businessName
Ext_Selection_Col_Name=Имя
# XFLD Column Location
Ext_Selection_Col_Location=Местоположение
# XFLD
Col_Name=Имя
# XFLD
Col_Description=Описание
# XFLD Label
MoveTo_Label=Переместить в папку
#XMIT Add versions menu button text in Data Builder
versions=Версии
createVersion=Создать версию
versionHistory=История версий
#XMSG
Package_Depends_On_DP=Этот пакет зависит от продуктов данных
#XMSG
Package_Depends_On_DP_Warning=Прежде чем импортировать этот пакет, убедитесь, что в целевом пространстве доступны следующие продукты данных:\r\n\r\n{0}
# XFLD
Export_Confirm_Dailog_Title=Подтверждение
#XMSG
Package_Contains_DataObjects=Этот пакет включает следующие объекты, содержащие данные для переноса:\r\n\r\n{0} \r\n\r\nУбедитесь, что в результате переноса этих объектов в пакете не будут ненадлежащим образом раскрыты персональные или конфиденциальные данные.
