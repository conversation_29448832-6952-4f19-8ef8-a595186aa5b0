# https://wiki.wdf.sap.corp/wiki/display/DWAAS/How+to+add+text+classifications+for+translatable+texts
#
# use the following text types for short texts (up to 120 characters)
# XACT Accessibility
# XBUT Button
# XCKL Checkbox
# XCOL Column header
# XFLD Label
# XGRP Group title
# XLNK Hyperlink
# XLOG Log entry
# XLST List box item
# XMIT Menu item
# XMSG Message
# XRBL Radio button
# XSEL Selection
# XTIT Table title
# XTOL Tooltip
#
# for longer texts (more than 120 characters) use
# YINS Instruction
# YMSG Message

#=============from skyline============
#XFLD general tab of package editor
Tab_General=Γενικό
#XFLD objects tab of package editor
Tab_Objects=Αντικείμενα
#XFLD required packages tab of package editor
Tab_RequiredPkgs=Απαιτούμενα Πακέτα
#XFLD label for business name input
BusinessName=Επωνυμία Επιχείρησης
#XFLD label for technical name input
TechnicalName=Τεχνικό Ονομα
#XFLD label for purpose input
BusinessPurpose=Επιχειρηματικός Σκοπός
#XTIT title of save dialog
Dilaog_Save=Αποθήκευση
#XBUT save button of save dialog
Btn_Save=Αποθήκευση
#XBUT cancel button of save dialog
Btn_Cancel=Ακύρωση
#XFLD title of objects section
Objects=Αντικείμενα
#XFLD title of required packages section
RequiredPackages=Απαιτούμενα Πακέτα
#XTIT title of package selection dialog
Dialog_SelectPackages=Επιλογή Πακέτων
#XMSG no data text of package selection dialog
Dilaog_NoDataText=Δεν βρέθηκαν Πακέτα Αποθήκης
#XMSG message of adding objects
Msg_ObjectAdded={0} αντικείμενα προστέθηκαν
#XMSG message of removing objects
Msg_ObjectRemoved={0} αντικείμενα διαγράφηκαν
#XMSG message of adding required packages
Msg_RequiredPackageAdded={0} απαιτούμενα πακέτα προστέθηκαν
#XMSG message of removing required packages
Msg_RequiredPackageRemoved={0} απαιτούμενα πακέτα διαγράφηκαν
#XBUT add button in toolbar
Toolbar_Btn_Add=Προσθήκη
#XBUT remove button in toolbar
Toolbar_Btn_Remove=Διαγραφή
#XCOL status column of objects and required packages table
Table_Col_Status=Αποτέλεσμα
#XLNK more link of status details
Status_More_Link=Εμφάνιση στοιχείων...
#XFLD folder name
FolderName=Ονομα
#XFLD folder description
FolderDescription=Περιγραφή
#XBUT ok button
Btn_OK=ΟΚ
#XFLD title of export location dialog
ExportLocationDialog=Εξαγωγή Τοποθεσίας
#XFLD lable of category field
Category=Κατηγορία
#XFLD label of location field
Location=Τοποθεσία στόχου
#XFLD lable of version field
SemanticVersion=Εκδοση
#XFLD label of current exported version
CurrentExportedPackage=Τρέχουσα Εξαχθείσα Εκδοση
#XFLD label of status field
ExportStatus=Κατάσταση
#XFLD tooltip of export button
Tooltip_Export=Εξαγωγή
#XFLD tooltip of save button
Tooltip_Save=Αποθήκευση
#XFLD tooltip of validate button
Tooltip_Validate=Επικύρωση
#XMSG
InvalidVersion=Εισαγωγή έκδοσης με τη μορφή "x.y.z".
#XMSG
VersionLowerThanBefore=Η έκδοση δεν πρέπει να είναι μικρότερη από την προηγούμενη έκδοση.
#XMSG
Empty_Version=Εισάγετε μία έκδοση πακέτου.
#XMSG
Package_Missing_Technical_Name=Εισάγετε τεχνικό όνομα για το πακέτο.
#=============from skyline============

#XFLD lable for space combo selection
Spaces=Χώρος:
#XFLD section title of landing page
Packages=Πακέτα
#XCOL business name column
Table_Col_BusinessName=Επωνυμία Επιχείρησης
#XCOL technical name column
Table_Col_TechnicalName=Τεχνικό Ονομα
#XCOL sapce column
Table_Col_Space=Χώρος
#XCOL create on column
Table_Col_CreatedOn=Δημιουργημ.στις
#XCOL entity type column
Table_Col_Type=Τύπος
#XCOL entity type column
Table_Col_Object_Status=Κατάσταση
#XMSG message of deleting packages
Msg_PackageRemoved={0} πακέτα διαγράφηκαν
#XMIT menu item of all spaces
Op_AllSpaces=Ολοι οι Χώροι
#XFLD default business name of a new package
NewPackage_BusinessName=Πακέτο 1
#XFLD default technical name of a new package
NewPackage_TechnicalName=Πακέτο_1
#XFLD default name of a new package displayed in breadcrumb
Breadcrumb_NewPackage_Name=Νέο Πακέτο
#XFLD title of object selection dialog
ObjectsDialog_Title=Προσθήκη Αντικειμένων
#XMSG dependencies are fully resolved
Dependency_Resolved=Ετοιμο για προσθήκη (Ολες οι Εξαρτήσεις Αναλύθηκαν ({0}))
#XMSG dependencies are partially resolved
Dependency_Partial_Resolved=Ετοιμο για Προσθήκη (Ορισμένες Εξαρτήσεις Δεν Αναλύθηκαν ({0}/{1})
#XMSG dependencies are not found
Dependency_Not_Found=Δεν βρέθηκαν εξαρτήσεις
#XMSG dependencies are in other package
Dependency_In_Other_Package=Αδύνατη προσθήκη: Ηδη σε Πακέτο "{0}"
#XMSG dependencies are in other space
Dependency_In_Other_Space=Αδύνατη Προσθήκη: Σε Χώρο {0}
#XMSG
Cannot_Add_Managed_Content=Δεν Μπορείτε να Προσθέσετε: Περιεχόμενο Υπό Διαχείριση
#XMSG
Cannot_Add_Due_To_Dependency_Has_Managed_Content=Δεν Μπορείτε να Προσθέσετε: Η Εξάρτηση Έχει Περιεχόμενο Υπό Διαχείριση
#XMSG dependencies are in current space
Dependency_In_Current_Package=Ηδη σε Πακέτο
#XMSG dependencies are in required package
Dependency_In_Required_Package=Ηδη σε Απαιτούμενο Πακέτο
#XMSG package arelady exists
Package_Duplicate_Name=Το πακέτο ''{0}'' υπάρχει ήδη στην αποθήκη. Εισάγετε άλλο όνομα.
#XMSG package name is required
Package_Name_Missing=Εισάγετε ένα όνομα πακέτου.
#XMSG package version is required
Package_Version_Missing=Εισάγετε μία έκδοση πακέτου.
#XMSG package is drafted
Package_Draft_Warning=Αρχικό πακέτο. Πατήστε «Αποθήκευση» για να επαληθεύσετε εξαρτήσεις και να επιβεβαιώσετε τις αλλαγές σας.
#XFLD title of validation message dialog
Validation_MessageBox_Title=Μηνύματα Επικύρωσης
#XMSG content message of validation message dialog
Validation_MessageBox_Content="{0}" είναι άκυρο και δεν μπορεί να εξαχθεί. Πρέπει να επιλύσετε πλήρως όλες τις εξαρτήσεις αντικειμένου προσθέτοντας τα απαραίτητα αντικείμενα μη αυτόματα ή μέσω του απαιτούμενου πακέτου.
#XBUT save anyway button
Save_Anyway=Αποθήκευση
#XMSG package is valid
Valid_Package_Message=Το πακέτο είναι άκυρο.
#XMSG
Object_In_Other_Pacakge=Το αντικείμενο "{0}" είναι σε πακέτο "{1}".
#XMSG
Dependent_Object_In_Other_Pacakge=Το αντικείμενο "{0}" σε πακέτο "{1}", που δεν καθορίστηκε ως απαιτούμενο πακέτο.
#XMSG
Dependent_Object_Missing=Αντικείμενο "{0}" σε χώρο {1}.
#XMSG
Dependent_Missing_Pattern=Αντικείμενο "{0}" εξαρτάται από:\r\n{1}
#XMSG: error message for saving the package
Save_Error_Message=Αδύνατη αποθήκευση πακέτου. {0}
#XFLD title of save busy dialog
Title_Save_Busy_Dialog=Αποθήκευση
#XMSG: message of it takes long time to save
Long_Time_Save_Busy_Dialog=Η αποθήκευση διαρκεί περισσότερο από ό,τι συνήθως. Παρακαλούμε περιμένετε.
#XMSG: erro message of missing dependency
Dependency_Missing=Η εξάρτηση λείπει.
#XMSG: erro message of missing dependency
Object_Dependency_Missing=Η εξάρτηση "{0}" λείπει.
#XMSG message of validating busy dialog
Validating_Package=Επαλήθευση Πακέτου
#XFLD title of validating busy dialog
Title_Validating_Busy_Dialog=Επεξεργασία
#XMSG %%0 is a placeholder, don’t change it
Missing_Dependency=Απουσία εξαρτήσεων %%0
#XMSG %%0 is a placeholder, don’t change it
Circular_Dependency=Κυκλική εξάρτηση %%0
#XMSG Object is added to package successfully
Successfully_Add=Προστέθηκε
#XMSG
Circular_Dependency_Detail="{0}" δεν μπορεί να αποθηκευτεί γιατί έχει κυκλική εξάρτηση με πακέτο "{1}".
#XFLD label of searching objects in space
Search_In_Sapce=Αναζήτηση σε: {0}
#XFLD title of deleting package confirm dialog
Package_Deleting_Dialog_Title=Διαγραφή
#XMSG content message of deleting package confirm dialog
Package_Deleting_Dialog_Message=Θέλετε να διαγράψετε τα επιλεγμένα πακέτα;
#XMSG
Package_Cannot_Delete=Το επιλεγμένο πακέτο είναι απαραίτητο από άλλα πακέτα και δεν μπορεί να διαγραφεί.
#XMSG
Package_Required_By_Singular=(απαιτούμενο από {0} πακέτο)
#XMSG
Package_Required_By_Plural=(απαιτούμενο από {0} πακέτα)
#XFLD display name of owner column
owner=Κάτοχος
#XFLD title of deleting package error dialog
Deleting_Error_Dialog_Title=Σφάλμα
#XFLD title of deleting package alert dialog
Deleting_Alert_Dialog_Title=Ειδοποίηση
#XBUT
Deleting_Alert_Dialog_Ok=ΟΚ
#XFLD
MyContent=Το Περιεχόμενό Μου
#XFLD
NotExported=Δεν Εξήχθη
#XFLD
Exporting=Εξαγωγή σε εξέλιξη
#XFLD
Exported=Εξάχθηκε
#XFLD
DesignTimeError=Σφάλμα Χρόνου Σχεδίασης
#XFLD
ExportFailed=Εξαγωγή Απέτυχε
#XFLD
Cancelling=Γίνεται ακύρωση
#XFLD
ChangesToExport=Αλλαγές για Εξαγωγή
#XMSG
Exporting_Package=Γίνεται εξαγωγή {0}. Θα σας ενημερώσουμε όταν η διαδικασία ολοκληρωθεί.
#XFLD: Object type is businessEntityVariant
TypeBusinessEntityVariant=Εκδοση Επιχειρηματικής Οντότητας
#XFLD: Object type is businessEntity
TypeBusinessEntity=Επιχειρηματική Οντότητα
#XFLD
Tooltip_Add=Προσθήκη
#XFLD
Tooltip_Edit=Επεξεργασία
#XFLD
Tooltip_Delete=Διαγραφή
#XFLD
Tooltip_Refresh=Ανανέωση
#XMSG
ExportToOverwrite=Αυτό το πακέτο εξήχθη ήδη στο Δίκτυο Περιεχομένων με έκδοση {0}. Θέλετε να το αντικαταστήσετε;
# XFLD Column businessName
Ext_Selection_Col_Name=Ονομα
# XFLD Column Location
Ext_Selection_Col_Location=Τοποθεσία
# XFLD
Col_Name=Ονομα
# XFLD
Col_Description=Περιγραφή
# XFLD Label
MoveTo_Label=Μετακίνηση σε Φάκελο
#XMIT Add versions menu button text in Data Builder
versions=Εκδόσεις
createVersion=Δημιουργία Έκδοσης
versionHistory=Ιστορικό Εκδόσεων
#XMSG
Package_Depends_On_DP=Αυτό το πακέτο εξαρτάται από προϊόντα δεδομένων
#XMSG
Package_Depends_On_DP_Warning=Πριν εισαγάγετε αυτό το προϊόν, βεβαιωθείτε ότι τα παρακάτω προϊόντα δεδομένων είναι διαθέσιμα στον χώρο στόχο:\r\n\r\n{0}
# XFLD
Export_Confirm_Dailog_Title=Επιβεβαίωση
#XMSG
Package_Contains_DataObjects=Αυτό το πακέτο περιλαμβάνει τα ακόλουθα αντικείμενα που περιέχουν δεδομένα για μεταφορά:\r\n\r\n{0} \r\n\r\nΒεβαιωθείτε ότι δεν θα εκτεθούν λανθασμένα προσωπικά ή ευαίσθητα δεδομένα ως αποτέλεσμα της μεταφοράς αυτών των αντικειμένων στο πακέτο σας.
