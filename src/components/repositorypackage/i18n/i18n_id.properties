# https://wiki.wdf.sap.corp/wiki/display/DWAAS/How+to+add+text+classifications+for+translatable+texts
#
# use the following text types for short texts (up to 120 characters)
# XACT Accessibility
# XBUT Button
# XCKL Checkbox
# XCOL Column header
# XFLD Label
# XGRP Group title
# XLNK Hyperlink
# XLOG Log entry
# XLST List box item
# XMIT Menu item
# XMSG Message
# XRBL Radio button
# XSEL Selection
# XTIT Table title
# XTOL Tooltip
#
# for longer texts (more than 120 characters) use
# YINS Instruction
# YMSG Message

#=============from skyline============
#XFLD general tab of package editor
Tab_General=Umum
#XFLD objects tab of package editor
Tab_Objects=Objek
#XFLD required packages tab of package editor
Tab_RequiredPkgs=Paket yang Diperlukan
#XFLD label for business name input
BusinessName=Nama Bisnis
#XFLD label for technical name input
TechnicalName=Nama Te<PERSON>nis
#XFLD label for purpose input
BusinessPurpose=Tujuan Bisnis
#XTIT title of save dialog
Dilaog_Save=Simpan
#XBUT save button of save dialog
Btn_Save=Simpan
#XBUT cancel button of save dialog
Btn_Cancel=Batalkan
#XFLD title of objects section
Objects=Objek
#XFLD title of required packages section
RequiredPackages=Paket yang Diperlukan
#XTIT title of package selection dialog
Dialog_SelectPackages=Pilih Paket
#XMSG no data text of package selection dialog
Dilaog_NoDataText=Paket Repositori Tidak Ditemukan
#XMSG message of adding objects
Msg_ObjectAdded={0} objek ditambahkan
#XMSG message of removing objects
Msg_ObjectRemoved={0} objek dihapus
#XMSG message of adding required packages
Msg_RequiredPackageAdded={0} paket yang diperlukan ditambahkan
#XMSG message of removing required packages
Msg_RequiredPackageRemoved={0} paket yang diperlukan dihapus
#XBUT add button in toolbar
Toolbar_Btn_Add=Tambahkan
#XBUT remove button in toolbar
Toolbar_Btn_Remove=Hapus
#XCOL status column of objects and required packages table
Table_Col_Status=Hasil
#XLNK more link of status details
Status_More_Link=Tampilkan rincian...
#XFLD folder name
FolderName=Nama
#XFLD folder description
FolderDescription=Deskripsi
#XBUT ok button
Btn_OK=OKE
#XFLD title of export location dialog
ExportLocationDialog=Lokasi Ekspor
#XFLD lable of category field
Category=Kategori
#XFLD label of location field
Location=Lokasi Target
#XFLD lable of version field
SemanticVersion=Versi
#XFLD label of current exported version
CurrentExportedPackage=Versi yang Diekspor Saat Ini
#XFLD label of status field
ExportStatus=Status
#XFLD tooltip of export button
Tooltip_Export=Ekspor
#XFLD tooltip of save button
Tooltip_Save=Simpan
#XFLD tooltip of validate button
Tooltip_Validate=Validasi
#XMSG
InvalidVersion=Masukkan versi dalam format "x.y.z".
#XMSG
VersionLowerThanBefore=Versi tidak dapat lebih rendah dari versi sebelumnya.
#XMSG
Empty_Version=Masukkan versi paket.
#XMSG
Package_Missing_Technical_Name=Masukkan nama teknis untuk paket.
#=============from skyline============

#XFLD lable for space combo selection
Spaces=Ruang:
#XFLD section title of landing page
Packages=Paket
#XCOL business name column
Table_Col_BusinessName=Nama Bisnis
#XCOL technical name column
Table_Col_TechnicalName=Nama Teknis
#XCOL sapce column
Table_Col_Space=Ruang
#XCOL create on column
Table_Col_CreatedOn=Dibuat pada
#XCOL entity type column
Table_Col_Type=Tipe
#XCOL entity type column
Table_Col_Object_Status=Status
#XMSG message of deleting packages
Msg_PackageRemoved={0} paket dihapus
#XMIT menu item of all spaces
Op_AllSpaces=Semua Ruang
#XFLD default business name of a new package
NewPackage_BusinessName=Paket 1
#XFLD default technical name of a new package
NewPackage_TechnicalName=Paket_1
#XFLD default name of a new package displayed in breadcrumb
Breadcrumb_NewPackage_Name=Paket Baru
#XFLD title of object selection dialog
ObjectsDialog_Title=Tambahkan Objek
#XMSG dependencies are fully resolved
Dependency_Resolved=Siap Ditambahkan (Semua Dependensi Terselesaikan ({0}))
#XMSG dependencies are partially resolved
Dependency_Partial_Resolved=Siap Ditambahkan (Beberapa Dependensi Tidak Terselesaikan ({0}/{1})
#XMSG dependencies are not found
Dependency_Not_Found=Dependensi tidak ditemukan
#XMSG dependencies are in other package
Dependency_In_Other_Package=Tidak Dapat Menambahkan: Sudah Ada dalam Paket "{0}"
#XMSG dependencies are in other space
Dependency_In_Other_Space=Tidak Dapat Menambahkan: Dalam Ruang {0}
#XMSG
Cannot_Add_Managed_Content=Tidak Dapat Menambahkan: Konten yang Dikelola
#XMSG
Cannot_Add_Due_To_Dependency_Has_Managed_Content=Tidak Dapat Menambahkan: Dependensi Memiliki Konten yang Dikelola
#XMSG dependencies are in current space
Dependency_In_Current_Package=Sudah Ada dalam Paket
#XMSG dependencies are in required package
Dependency_In_Required_Package=Sudah Ada dalam Paket yang Diperlukan
#XMSG package arelady exists
Package_Duplicate_Name=Paket ''{0}'' sudah ada dalam repositori. Silakan masukkan nama yang lain.
#XMSG package name is required
Package_Name_Missing=Masukkan nama paket.
#XMSG package version is required
Package_Version_Missing=Masukkan versi paket.
#XMSG package is drafted
Package_Draft_Warning=Paket konsep. Klik "Simpan" untuk memverifikasi dependensi dan konfirmasi perubahan Anda.
#XFLD title of validation message dialog
Validation_MessageBox_Title=Pesan Validasi
#XMSG content message of validation message dialog
Validation_MessageBox_Content="{0}" tidak valid dan tidak dapat diekspor. Anda harus sepenuhnya menyelesaikan semua dependensi objek dengan menambahkan objek yang diperlukan secara manual atau melalui paket yang diperlukan.
#XBUT save anyway button
Save_Anyway=Tetap Simpan
#XMSG package is valid
Valid_Package_Message=Paket valid.
#XMSG
Object_In_Other_Pacakge=Objek "{0}" berada dalam paket "{1}".
#XMSG
Dependent_Object_In_Other_Pacakge=Objek "{0}" dalam paket "{1}", yang tidak ditetapkan sebagai paket yang diperlukan.
#XMSG
Dependent_Object_Missing=Objek "{0}" dalam ruang {1}.
#XMSG
Dependent_Missing_Pattern=Objek "{0}" bergantung pada:\r\n{1}
#XMSG: error message for saving the package
Save_Error_Message=Gagal menyimpan paket. {0}
#XFLD title of save busy dialog
Title_Save_Busy_Dialog=Menyimpan
#XMSG: message of it takes long time to save
Long_Time_Save_Busy_Dialog=Penyimpanan membutuhkan waktu lebih lama dari biasanya. Harap tunggu.
#XMSG: erro message of missing dependency
Dependency_Missing=Dependensi hilang.
#XMSG: erro message of missing dependency
Object_Dependency_Missing=Dependensi "{0}" hilang.
#XMSG message of validating busy dialog
Validating_Package=Memvalidasi Paket
#XFLD title of validating busy dialog
Title_Validating_Busy_Dialog=Memproses
#XMSG %%0 is a placeholder, don’t change it
Missing_Dependency=Dependensi hilang %%0
#XMSG %%0 is a placeholder, don’t change it
Circular_Dependency=Dependensi sirkular %%0
#XMSG Object is added to package successfully
Successfully_Add=Ditambahkan
#XMSG
Circular_Dependency_Detail="{0}" tidak dapat disimpan karena memiliki dependensi sirkular dengan paket "{1}".
#XFLD label of searching objects in space
Search_In_Sapce=Cari dalam: {0}
#XFLD title of deleting package confirm dialog
Package_Deleting_Dialog_Title=Hapus Permanen
#XMSG content message of deleting package confirm dialog
Package_Deleting_Dialog_Message=Apakah Anda ingin menghapus permanen paket yang dipilih?
#XMSG
Package_Cannot_Delete=Paket yang dipilih diperlukan oleh paket lain dan tidak dapat dihapus permanen.
#XMSG
Package_Required_By_Singular=(diperlukan oleh {0} paket)
#XMSG
Package_Required_By_Plural=(diperlukan oleh {0} paket)
#XFLD display name of owner column
owner=Pemilik
#XFLD title of deleting package error dialog
Deleting_Error_Dialog_Title=Kesalahan
#XFLD title of deleting package alert dialog
Deleting_Alert_Dialog_Title=Peringatan
#XBUT
Deleting_Alert_Dialog_Ok=OKE
#XFLD
MyContent=Konten Saya
#XFLD
NotExported=Tidak Diekspor
#XFLD
Exporting=Mengekspor
#XFLD
Exported=Diekspor
#XFLD
DesignTimeError=Kesalahan Waktu Rancangan
#XFLD
ExportFailed=Ekspor Gagal
#XFLD
Cancelling=Membatalkan
#XFLD
ChangesToExport=Perubahan untuk Diekspor
#XMSG
Exporting_Package=Mengekspor {0}. Kami akan memberi tahu Anda jika proses selesai.
#XFLD: Object type is businessEntityVariant
TypeBusinessEntityVariant=Versi Entitas Bisnis
#XFLD: Object type is businessEntity
TypeBusinessEntity=Entitas Bisnis
#XFLD
Tooltip_Add=Tambahkan
#XFLD
Tooltip_Edit=Edit
#XFLD
Tooltip_Delete=Hapus Permanen
#XFLD
Tooltip_Refresh=Segarkan
#XMSG
ExportToOverwrite=Paket ini telah diekspor ke Jaringan Konten dengan versi {0}. Apakah Anda ingin menimpanya?
# XFLD Column businessName
Ext_Selection_Col_Name=Nama
# XFLD Column Location
Ext_Selection_Col_Location=Lokasi
# XFLD
Col_Name=Nama
# XFLD
Col_Description=Deskripsi
# XFLD Label
MoveTo_Label=Pindahkan ke Folder
#XMIT Add versions menu button text in Data Builder
versions=Versi
createVersion=Buat Versi
versionHistory=Riwayat Versi
#XMSG
Package_Depends_On_DP=Paket ini bergantung pada produk data
#XMSG
Package_Depends_On_DP_Warning=Sebelum mengimpor paket ini, pastikan produk data berikut tersedia di ruang target:\r\n\r\n{0}
# XFLD
Export_Confirm_Dailog_Title=Konfirmasi
#XMSG
Package_Contains_DataObjects=Paket ini mencakup objek-objek berikut yang memuat data untuk dipindahkan:\r\n\r\n{0}\r\n\r\nHarap pastikan bahwa tidak ada data pribadi atau sensitif yang akan tersedia tanpa izin akibat pemindahan objek-objek ini dalam paket Anda.
