# https://wiki.wdf.sap.corp/wiki/display/DWAAS/How+to+add+text+classifications+for+translatable+texts
#
# use the following text types for short texts (up to 120 characters)
# XACT Accessibility
# XBUT Button
# XCKL Checkbox
# XCOL Column header
# XFLD Label
# XGRP Group title
# XLNK Hyperlink
# XLOG Log entry
# XLST List box item
# XMIT Menu item
# XMSG Message
# XRBL Radio button
# XSEL Selection
# XTIT Table title
# XTOL Tooltip
#
# for longer texts (more than 120 characters) use
# YINS Instruction
# YMSG Message

#=============from skyline============
#XFLD general tab of package editor
Tab_General=Allmänt
#XFLD objects tab of package editor
Tab_Objects=Objekt
#XFLD required packages tab of package editor
Tab_RequiredPkgs=Obligatoriska paket
#XFLD label for business name input
BusinessName=Affärsnamn
#XFLD label for technical name input
TechnicalName=Tekniskt namn
#XFLD label for purpose input
BusinessPurpose=Affärssyfte
#XTIT title of save dialog
Dilaog_Save=Spara
#XBUT save button of save dialog
Btn_Save=Spara
#XBUT cancel button of save dialog
Btn_Cancel=Avbryt
#XFLD title of objects section
Objects=Objekt
#XFLD title of required packages section
RequiredPackages=Obligatoriska paket
#XTIT title of package selection dialog
Dialog_SelectPackages=Välj paket
#XMSG no data text of package selection dialog
Dilaog_NoDataText=Inga repositorypaket hittades
#XMSG message of adding objects
Msg_ObjectAdded={0} objekt har lagts till
#XMSG message of removing objects
Msg_ObjectRemoved={0} objekt har tagits bort
#XMSG message of adding required packages
Msg_RequiredPackageAdded={0} obligatoriska paket har lagts till
#XMSG message of removing required packages
Msg_RequiredPackageRemoved={0} obligatoriska paket har tagits bort
#XBUT add button in toolbar
Toolbar_Btn_Add=Lägg till
#XBUT remove button in toolbar
Toolbar_Btn_Remove=Ta bort
#XCOL status column of objects and required packages table
Table_Col_Status=Resultat
#XLNK more link of status details
Status_More_Link=Visa detaljer...
#XFLD folder name
FolderName=Namn
#XFLD folder description
FolderDescription=Beskrivning
#XBUT ok button
Btn_OK=OK
#XFLD title of export location dialog
ExportLocationDialog=Exportplacering
#XFLD lable of category field
Category=Kategori
#XFLD label of location field
Location=Målplacering
#XFLD lable of version field
SemanticVersion=Version
#XFLD label of current exported version
CurrentExportedPackage=Aktuell exporterad version
#XFLD label of status field
ExportStatus=Status
#XFLD tooltip of export button
Tooltip_Export=Exportera
#XFLD tooltip of save button
Tooltip_Save=Spara
#XFLD tooltip of validate button
Tooltip_Validate=Validera
#XMSG
InvalidVersion=Ange en version i formen "x.y.z".
#XMSG
VersionLowerThanBefore=Version kan inte vara lägre än föregående version.
#XMSG
Empty_Version=Ange paketversion.
#XMSG
Package_Missing_Technical_Name=Ange ett tekniskt namn på paketet.
#=============from skyline============

#XFLD lable for space combo selection
Spaces=Utrymme:
#XFLD section title of landing page
Packages=Paket
#XCOL business name column
Table_Col_BusinessName=Affärsnamn
#XCOL technical name column
Table_Col_TechnicalName=Tekniskt namn
#XCOL sapce column
Table_Col_Space=Utrymme
#XCOL create on column
Table_Col_CreatedOn=Uppläggning den
#XCOL entity type column
Table_Col_Type=Typ
#XCOL entity type column
Table_Col_Object_Status=Status
#XMSG message of deleting packages
Msg_PackageRemoved={0} paket har tagits bort
#XMIT menu item of all spaces
Op_AllSpaces=Alla utrymmen
#XFLD default business name of a new package
NewPackage_BusinessName=Paket 1
#XFLD default technical name of a new package
NewPackage_TechnicalName=Package_1
#XFLD default name of a new package displayed in breadcrumb
Breadcrumb_NewPackage_Name=Nytt paket
#XFLD title of object selection dialog
ObjectsDialog_Title=Lägg till objekt
#XMSG dependencies are fully resolved
Dependency_Resolved=Klar att lägga till (alla beroenden har åtgärdats ({0}))
#XMSG dependencies are partially resolved
Dependency_Partial_Resolved=Klar att lägga till (vissa beroenden har inte åtgärdats ({0}/{1})
#XMSG dependencies are not found
Dependency_Not_Found=Beroenden hittades ej
#XMSG dependencies are in other package
Dependency_In_Other_Package=Kan inte lägga till: Redan i paket "{0}"
#XMSG dependencies are in other space
Dependency_In_Other_Space=Kan inte lägga till: I utrymme {0}
#XMSG
Cannot_Add_Managed_Content=Kan inte lägga till: Hanterat innehåll
#XMSG
Cannot_Add_Due_To_Dependency_Has_Managed_Content=Kan inte lägga till: Beroende har hanterat innehåll
#XMSG dependencies are in current space
Dependency_In_Current_Package=Redan i paket
#XMSG dependencies are in required package
Dependency_In_Required_Package=Redan i obligatoriskt paket
#XMSG package arelady exists
Package_Duplicate_Name=Paket ''{0}'' finns redan i repository. Ange ett annat namn.
#XMSG package name is required
Package_Name_Missing=Ange paketnamn.
#XMSG package version is required
Package_Version_Missing=Ange paketversion.
#XMSG package is drafted
Package_Draft_Warning=Utkastpaket. Klicka på "Spara" för att verifiera beroenden och bekräfta dina ändringar.
#XFLD title of validation message dialog
Validation_MessageBox_Title=Valideringsmeddelanden
#XMSG content message of validation message dialog
Validation_MessageBox_Content="{0}" är ogiltigt och kan inte exporteras. Du måste åtgärda alla objektberoenden genom att lägga till nödvändiga objekt manuellt eller via ett obligatoriskt paket.
#XBUT save anyway button
Save_Anyway=Spara ändå
#XMSG package is valid
Valid_Package_Message=Paketet är giltigt.
#XMSG
Object_In_Other_Pacakge=Objekt "{0}" finns i paket "{1}".
#XMSG
Dependent_Object_In_Other_Pacakge=Objekt "{0}" i paket "{1}", som inte har angetts som ett obligatoriskt paket.
#XMSG
Dependent_Object_Missing=Objekt "{0}" i utrymme {1}.
#XMSG
Dependent_Missing_Pattern=Objekt "{0}" är beroende av:\r\n{1}
#XMSG: error message for saving the package
Save_Error_Message=Paket kunde inte sparas. {0}
#XFLD title of save busy dialog
Title_Save_Busy_Dialog=Sparar
#XMSG: message of it takes long time to save
Long_Time_Save_Busy_Dialog=Lagring tar längre tid än vanligt. Vänta.
#XMSG: erro message of missing dependency
Dependency_Missing=Beroende saknas.
#XMSG: erro message of missing dependency
Object_Dependency_Missing=Beroende saknas för "{0}".
#XMSG message of validating busy dialog
Validating_Package=Validerar paket
#XFLD title of validating busy dialog
Title_Validating_Busy_Dialog=Bearbetar
#XMSG %%0 is a placeholder, don’t change it
Missing_Dependency=Beroenden som saknas %%0
#XMSG %%0 is a placeholder, don’t change it
Circular_Dependency=Cirkelberoende %%0
#XMSG Object is added to package successfully
Successfully_Add=Har lagts till
#XMSG
Circular_Dependency_Detail="{0}" kan inte sparas eftersom det finns ett cirkelberoende med paket "{1}".
#XFLD label of searching objects in space
Search_In_Sapce=Sökning i: {0}
#XFLD title of deleting package confirm dialog
Package_Deleting_Dialog_Title=Radera
#XMSG content message of deleting package confirm dialog
Package_Deleting_Dialog_Message=Vill du radera de valda paketen?
#XMSG
Package_Cannot_Delete=Valt paket krävs av andra paket och kan inte raderas.
#XMSG
Package_Required_By_Singular=(krävs av {0} paket)
#XMSG
Package_Required_By_Plural=(krävs av {0} paket)
#XFLD display name of owner column
owner=Ägare
#XFLD title of deleting package error dialog
Deleting_Error_Dialog_Title=Fel
#XFLD title of deleting package alert dialog
Deleting_Alert_Dialog_Title=Varning
#XBUT
Deleting_Alert_Dialog_Ok=OK
#XFLD
MyContent=Mitt innehåll
#XFLD
NotExported=Inte exporterat
#XFLD
Exporting=Exporterar
#XFLD
Exported=Exporterat
#XFLD
DesignTimeError=Designtidsfel
#XFLD
ExportFailed=Export misslyckades
#XFLD
Cancelling=Avbryter
#XFLD
ChangesToExport=Ändringar av export
#XMSG
Exporting_Package=Exporterar {0}. Vi meddelar dig när processen slutförts.
#XFLD: Object type is businessEntityVariant
TypeBusinessEntityVariant=Affärsentitetsversion
#XFLD: Object type is businessEntity
TypeBusinessEntity=Affärsentitet
#XFLD
Tooltip_Add=Lägg till
#XFLD
Tooltip_Edit=Redigera
#XFLD
Tooltip_Delete=Radera
#XFLD
Tooltip_Refresh=Uppdatera
#XMSG
ExportToOverwrite=Det här paketet har redan exporterats till innehållsnätverket med version {0}. Vill du skriva över det?
# XFLD Column businessName
Ext_Selection_Col_Name=Namn
# XFLD Column Location
Ext_Selection_Col_Location=Placering
# XFLD
Col_Name=Namn
# XFLD
Col_Description=Beskrivning
# XFLD Label
MoveTo_Label=Flytta till mapp
#XMIT Add versions menu button text in Data Builder
versions=Versioner
createVersion=Skapa version
versionHistory=Versionshistorik
#XMSG
Package_Depends_On_DP=Det här paketet är beroende av dataprodukter
#XMSG
Package_Depends_On_DP_Warning=Säkerställ att följande dataprodukter är tillgängliga för målutrymmet innan du importerar paketet:\r\n\r\n{0}
# XFLD
Export_Confirm_Dailog_Title=Bekräftelse
#XMSG
Package_Contains_DataObjects=Paketet inkluderar följande objekt som innehåller data för transport:\r\n\r\n{0}\r\n\r\nSäkerställ att inga personuppgifter eller känsliga uppgifter exponeras felaktigt till följd av transport av dessa objekt i ditt paket.
