# https://wiki.wdf.sap.corp/wiki/display/DWAAS/How+to+add+text+classifications+for+translatable+texts
#
# use the following text types for short texts (up to 120 characters)
# XACT Accessibility
# XBUT Button
# XCKL Checkbox
# XCOL Column header
# XFLD Label
# XGRP Group title
# XLNK Hyperlink
# XLOG Log entry
# XLST List box item
# XMIT Menu item
# XMSG Message
# XRBL Radio button
# XSEL Selection
# XTIT Table title
# XTOL Tooltip
#
# for longer texts (more than 120 characters) use
# YINS Instruction
# YMSG Message

#=============from skyline============
#XFLD general tab of package editor
Tab_General=Vispārīgi
#XFLD objects tab of package editor
Tab_Objects=Objekti
#XFLD required packages tab of package editor
Tab_RequiredPkgs=Vajadzīgās pakotnes
#XFLD label for business name input
BusinessName=Biznesa nosaukums
#XFLD label for technical name input
TechnicalName=Tehniskais nosaukums
#XFLD label for purpose input
BusinessPurpose=Biznesa nolūks
#XTIT title of save dialog
Dilaog_Save=Saglabāt
#XBUT save button of save dialog
Btn_Save=Saglabāt
#XBUT cancel button of save dialog
Btn_Cancel=Atcelt
#XFLD title of objects section
Objects=Objekti
#XFLD title of required packages section
RequiredPackages=Vajadzīgās pakotnes
#XTIT title of package selection dialog
Dialog_SelectPackages=Atlasīt pakotnes
#XMSG no data text of package selection dialog
Dilaog_NoDataText=Nav atrasta neviena repozitorija pakotne
#XMSG message of adding objects
Msg_ObjectAdded=Pievienoti {0} objekti
#XMSG message of removing objects
Msg_ObjectRemoved=Noņemti {0} objekti
#XMSG message of adding required packages
Msg_RequiredPackageAdded=Pievienotas {0} vajadzīgās pakotnes
#XMSG message of removing required packages
Msg_RequiredPackageRemoved=Noņemtas {0} vajadzīgās pakotnes
#XBUT add button in toolbar
Toolbar_Btn_Add=Pievienot
#XBUT remove button in toolbar
Toolbar_Btn_Remove=Noņemt
#XCOL status column of objects and required packages table
Table_Col_Status=Rezultāts
#XLNK more link of status details
Status_More_Link=Rādīt detalizētu informāciju...
#XFLD folder name
FolderName=Nosaukums
#XFLD folder description
FolderDescription=Apraksts
#XBUT ok button
Btn_OK=Labi
#XFLD title of export location dialog
ExportLocationDialog=Eksportēt atrašanās vietu
#XFLD lable of category field
Category=Kategorija
#XFLD label of location field
Location=Mērķa atrašanās vieta
#XFLD lable of version field
SemanticVersion=Versija
#XFLD label of current exported version
CurrentExportedPackage=Pašreizējā eksportētā versija
#XFLD label of status field
ExportStatus=Statuss
#XFLD tooltip of export button
Tooltip_Export=Eksportēt
#XFLD tooltip of save button
Tooltip_Save=Saglabāt
#XFLD tooltip of validate button
Tooltip_Validate=Pārbaudīt
#XMSG
InvalidVersion=Ievadiet versiju formā “x.y.z”.
#XMSG
VersionLowerThanBefore=Versija nevar būt zemāka par iepriekšējo versiju.
#XMSG
Empty_Version=Ievadiet pakotnes versiju.
#XMSG
Package_Missing_Technical_Name=Ievadiet tehnisko nosaukumu pakotnei.
#=============from skyline============

#XFLD lable for space combo selection
Spaces=Vieta:
#XFLD section title of landing page
Packages=Pakotnes
#XCOL business name column
Table_Col_BusinessName=Biznesa nosaukums
#XCOL technical name column
Table_Col_TechnicalName=Tehniskais nosaukums
#XCOL sapce column
Table_Col_Space=Vieta
#XCOL create on column
Table_Col_CreatedOn=Izveides datums
#XCOL entity type column
Table_Col_Type=Tips
#XCOL entity type column
Table_Col_Object_Status=Statuss
#XMSG message of deleting packages
Msg_PackageRemoved=Noņemtas {0} pakotnes
#XMIT menu item of all spaces
Op_AllSpaces=Visas vietas
#XFLD default business name of a new package
NewPackage_BusinessName=Pakotne 1
#XFLD default technical name of a new package
NewPackage_TechnicalName=Package_1
#XFLD default name of a new package displayed in breadcrumb
Breadcrumb_NewPackage_Name=Jauna pakotne
#XFLD title of object selection dialog
ObjectsDialog_Title=Pievienot objektus
#XMSG dependencies are fully resolved
Dependency_Resolved=Gatavs pievienošanai (visas atkarības ir atrisinātas ({0}))
#XMSG dependencies are partially resolved
Dependency_Partial_Resolved=Gatavs pievienošanai (dažas atkarības nav atrisinātas ({0}/{1})
#XMSG dependencies are not found
Dependency_Not_Found=Atkarības nevar atrast
#XMSG dependencies are in other package
Dependency_In_Other_Package=Nevar pievienot: jau pakotnē “{0}”
#XMSG dependencies are in other space
Dependency_In_Other_Space=Nevar pievienot: vietā {0}
#XMSG
Cannot_Add_Managed_Content=Nevar pievienot: Pārvaldīts saturs
#XMSG
Cannot_Add_Due_To_Dependency_Has_Managed_Content=Nevar pievienot: Atkarībai ir pārvaldīts saturs
#XMSG dependencies are in current space
Dependency_In_Current_Package=Jau pakotnē
#XMSG dependencies are in required package
Dependency_In_Required_Package=Jau ir vajadzīgajā pakotnē
#XMSG package arelady exists
Package_Duplicate_Name=Pakotne “{0}” jau pastāv repozitorijā. Lūdzu, ievadiet citu nosaukumu.
#XMSG package name is required
Package_Name_Missing=Ievadiet pakotnes nosaukumu.
#XMSG package version is required
Package_Version_Missing=Ievadiet pakotnes versiju.
#XMSG package is drafted
Package_Draft_Warning=Melnraksta pakotne. Noklikšķiniet uz “Saglabāt”, lai pārbaudītu atkarības un apstiprinātu izmaiņas, ko veicāt.
#XFLD title of validation message dialog
Validation_MessageBox_Title=Pārbaudes ziņojumi
#XMSG content message of validation message dialog
Validation_MessageBox_Content=Vienums “{0}” ir nederīgs, un to nevar eksportēt. Pilnībā atrisiniet visas objektu atkarības, manuāli pievienojot nepieciešamos objektus vai izmantojot vajadzīgo pakotni.
#XBUT save anyway button
Save_Anyway=Vienalga saglabāt
#XMSG package is valid
Valid_Package_Message=Pakotne ir derīga.
#XMSG
Object_In_Other_Pacakge=Objekts “{0}” ir pakotnē “{1}”.
#XMSG
Dependent_Object_In_Other_Pacakge=Objekts “{0}” pakotnē “{1}”, kas nav norādīta kā vajadzīgā pakotne.
#XMSG
Dependent_Object_Missing=Objekts “{0}” vietā {1}.
#XMSG
Dependent_Missing_Pattern=Objekts “{0}” ir atkarīgs no:\r\n{1}
#XMSG: error message for saving the package
Save_Error_Message=Neizdevās saglabāt pakotni. {0}
#XFLD title of save busy dialog
Title_Save_Busy_Dialog=Saglabāšana
#XMSG: message of it takes long time to save
Long_Time_Save_Busy_Dialog=Saglabāšana aizņem vairāk laika, nekā parasti. Lūdzu, uzgaidiet.
#XMSG: erro message of missing dependency
Dependency_Missing=Trūkst atkarības.
#XMSG: erro message of missing dependency
Object_Dependency_Missing=Trūkst “{0}” atkarības.
#XMSG message of validating busy dialog
Validating_Package=Notiek pakotnes pārbaude
#XFLD title of validating busy dialog
Title_Validating_Busy_Dialog=Apstrāde
#XMSG %%0 is a placeholder, don’t change it
Missing_Dependency=Trūkst atkarību %%0
#XMSG %%0 is a placeholder, don’t change it
Circular_Dependency=Cirkulārā atkarība %%0
#XMSG Object is added to package successfully
Successfully_Add=Pievienots
#XMSG
Circular_Dependency_Detail=Vienumu “{0}” nevar saglabāt, jo tam ir cirkulārā atkarība ar pakotni “{1}”.
#XFLD label of searching objects in space
Search_In_Sapce=Meklēt šajā: {0}
#XFLD title of deleting package confirm dialog
Package_Deleting_Dialog_Title=Dzēst
#XMSG content message of deleting package confirm dialog
Package_Deleting_Dialog_Message=Vai vēlaties dzēst atlasītās pakotnes?
#XMSG
Package_Cannot_Delete=Citām pakotnēm ir vajadzīga atlasītā pakotne, un to nevar dzēst.
#XMSG
Package_Required_By_Singular=(vajadzīga {0} pakotnei)
#XMSG
Package_Required_By_Plural=(vajadzīga {0} pakotnēm)
#XFLD display name of owner column
owner=Īpašnieks
#XFLD title of deleting package error dialog
Deleting_Error_Dialog_Title=Kļūda
#XFLD title of deleting package alert dialog
Deleting_Alert_Dialog_Title=Brīdinājums
#XBUT
Deleting_Alert_Dialog_Ok=Labi
#XFLD
MyContent=Mans saturs
#XFLD
NotExported=Nav eksportēts
#XFLD
Exporting=Notiek eksportēšana
#XFLD
Exported=Eksportēts
#XFLD
DesignTimeError=Dizaina laika kļūda
#XFLD
ExportFailed=Eksportēšana neizdevās
#XFLD
Cancelling=Notiek atcelšana
#XFLD
ChangesToExport=Izmaiņas eksportēšanā
#XMSG
Exporting_Package=Notiek {0} eksportēšana. Mēs jūs informēsim, kad šis process tiks pabeigts.
#XFLD: Object type is businessEntityVariant
TypeBusinessEntityVariant=Saimnieciskās vienības versija
#XFLD: Object type is businessEntity
TypeBusinessEntity=Saimnieciskā vienība
#XFLD
Tooltip_Add=Pievienot
#XFLD
Tooltip_Edit=Rediģēt
#XFLD
Tooltip_Delete=Dzēst
#XFLD
Tooltip_Refresh=Atsvaidzināt
#XMSG
ExportToOverwrite=Šī pakotne jau tika eksportēta uz satura tīklu ar versiju {0}. Vai vēlaties to pārrakstīt?
# XFLD Column businessName
Ext_Selection_Col_Name=Nosaukums
# XFLD Column Location
Ext_Selection_Col_Location=Atrašanās vieta
# XFLD
Col_Name=Nosaukums
# XFLD
Col_Description=Apraksts
# XFLD Label
MoveTo_Label=Pārvietot uz mapi
#XMIT Add versions menu button text in Data Builder
versions=Versijas
createVersion=Izveidot versiju
versionHistory=Versiju vēsture
#XMSG
Package_Depends_On_DP=Šī pakotne ir atkarīga no datu produktiem
#XMSG
Package_Depends_On_DP_Warning=Pirms importējat šo pakotni, nodrošiniet, ka mērķa vietai ir pieejami šādi datu produkti:\r\n\r\n{0}
# XFLD
Export_Confirm_Dailog_Title=Apstiprinājums
#XMSG
Package_Contains_DataObjects=Šajā pakotnē ir iekļauti šādi objekti, kuros ir transportēšanai paredzētie dati:\r\n\r\n{0} \r\n\r\nLūdzu, nodrošiniet, ka ar šo objektu transportēšanu jūsu pakotnē netiek nepiemērotā veidā atklāti nekādi personas vai sensitīvie dati.
