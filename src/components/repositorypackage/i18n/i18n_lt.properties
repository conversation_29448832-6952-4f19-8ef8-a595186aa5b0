# https://wiki.wdf.sap.corp/wiki/display/DWAAS/How+to+add+text+classifications+for+translatable+texts
#
# use the following text types for short texts (up to 120 characters)
# XACT Accessibility
# XBUT Button
# XCKL Checkbox
# XCOL Column header
# XFLD Label
# XGRP Group title
# XLNK Hyperlink
# XLOG Log entry
# XLST List box item
# XMIT Menu item
# XMSG Message
# XRBL Radio button
# XSEL Selection
# XTIT Table title
# XTOL Tooltip
#
# for longer texts (more than 120 characters) use
# YINS Instruction
# YMSG Message

#=============from skyline============
#XFLD general tab of package editor
Tab_General=Bendra
#XFLD objects tab of package editor
Tab_Objects=Objektai
#XFLD required packages tab of package editor
Tab_RequiredPkgs=Būtini paketai
#XFLD label for business name input
BusinessName=Verslo pavadinimas
#XFLD label for technical name input
TechnicalName=Techninis pavadinimas
#XFLD label for purpose input
BusinessPurpose=Verslo tikslas
#XTIT title of save dialog
Dilaog_Save=Įrašyti
#XBUT save button of save dialog
Btn_Save=Įrašyti
#XBUT cancel button of save dialog
Btn_Cancel=Atšaukti
#XFLD title of objects section
Objects=Objektai
#XFLD title of required packages section
RequiredPackages=Būtini paketai
#XTIT title of package selection dialog
Dialog_SelectPackages=Pasirinkti paketą
#XMSG no data text of package selection dialog
Dilaog_NoDataText=Saugyklos paketų nerasta
#XMSG message of adding objects
Msg_ObjectAdded=Pridėta objektų: {0}
#XMSG message of removing objects
Msg_ObjectRemoved=Pašalinta objektų: {0}
#XMSG message of adding required packages
Msg_RequiredPackageAdded=Pridėta būtinų objektų: {0}
#XMSG message of removing required packages
Msg_RequiredPackageRemoved=Pašalinta būtinų objektų: {0}
#XBUT add button in toolbar
Toolbar_Btn_Add=Pridėti
#XBUT remove button in toolbar
Toolbar_Btn_Remove=Pašalinti
#XCOL status column of objects and required packages table
Table_Col_Status=Rezultatas
#XLNK more link of status details
Status_More_Link=Rodyti išsamią informaciją...
#XFLD folder name
FolderName=Pavadinimas
#XFLD folder description
FolderDescription=Aprašas
#XBUT ok button
Btn_OK=Gerai
#XFLD title of export location dialog
ExportLocationDialog=Eksportavimo vieta
#XFLD lable of category field
Category=Kategorija
#XFLD label of location field
Location=Tikslinė vieta
#XFLD lable of version field
SemanticVersion=Versija
#XFLD label of current exported version
CurrentExportedPackage=Dabartinė eksportuota versija
#XFLD label of status field
ExportStatus=Būsena
#XFLD tooltip of export button
Tooltip_Export=Eksportuoti
#XFLD tooltip of save button
Tooltip_Save=Įrašyti
#XFLD tooltip of validate button
Tooltip_Validate=Tikrinti
#XMSG
InvalidVersion=Įveskite versiją „x.y.z“ formatu.
#XMSG
VersionLowerThanBefore=Versija negali būti senesnė negu ankstesnė versija.
#XMSG
Empty_Version=Įveskite paketo versiją.
#XMSG
Package_Missing_Technical_Name=Įveskite techninį paketo pavadinimą.
#=============from skyline============

#XFLD lable for space combo selection
Spaces=Vieta:
#XFLD section title of landing page
Packages=Paketai
#XCOL business name column
Table_Col_BusinessName=Verslo pavadinimas
#XCOL technical name column
Table_Col_TechnicalName=Techninis pavadinimas
#XCOL sapce column
Table_Col_Space=Sritis
#XCOL create on column
Table_Col_CreatedOn=Sukūrimo data
#XCOL entity type column
Table_Col_Type=Tipas
#XCOL entity type column
Table_Col_Object_Status=Būsena
#XMSG message of deleting packages
Msg_PackageRemoved=Pašalinta paketų: {0}
#XMIT menu item of all spaces
Op_AllSpaces=Visos sritys
#XFLD default business name of a new package
NewPackage_BusinessName=1 paketas
#XFLD default technical name of a new package
NewPackage_TechnicalName=Package_1
#XFLD default name of a new package displayed in breadcrumb
Breadcrumb_NewPackage_Name=Naujas paketas
#XFLD title of object selection dialog
ObjectsDialog_Title=Pridėti objektus
#XMSG dependencies are fully resolved
Dependency_Resolved=Paruošta pridėti (išspręstos visos priklausomybės ({0}))
#XMSG dependencies are partially resolved
Dependency_Partial_Resolved=Paruošta pridėti (kai kurios priklausomybės neišspręstos ({0}/{1})
#XMSG dependencies are not found
Dependency_Not_Found=Priklausomybių nerasta
#XMSG dependencies are in other package
Dependency_In_Other_Package=Nepavyko pridėti: jau yra pakete „{0}“
#XMSG dependencies are in other space
Dependency_In_Other_Space=Nepavyko pridėti: srityje {0}
#XMSG
Cannot_Add_Managed_Content=Nepavyko pridėti: tvarkomas turinys
#XMSG
Cannot_Add_Due_To_Dependency_Has_Managed_Content=Nepavyko pridėti: priklausomas elementas turi tvarkomo turinio
#XMSG dependencies are in current space
Dependency_In_Current_Package=Jau yra pakete
#XMSG dependencies are in required package
Dependency_In_Required_Package=Jau yra būtiname pakete
#XMSG package arelady exists
Package_Duplicate_Name=Paketas „{0}“ jau yra saugykloje. Įveskite kitą pavadinimą.
#XMSG package name is required
Package_Name_Missing=Įveskite paketo pavadinimą.
#XMSG package version is required
Package_Version_Missing=Įveskite paketo versiją.
#XMSG package is drafted
Package_Draft_Warning=Paketo juodraštis. Spustelėkite „Įrašyti“, kad patikrintumėte priklausomybes ir patvirtintumėte pakeitimus.
#XFLD title of validation message dialog
Validation_MessageBox_Title=Tikrinimo pranešimai
#XMSG content message of validation message dialog
Validation_MessageBox_Content=„{0}“ yra netinkamas ir negali būti eksportuojamas. Turite visiškai išspręsti visas objektų priklausomybes pridėdami reikiamus objektus rankiniu būdu arba naudodami būtiną paketą.
#XBUT save anyway button
Save_Anyway=Vis tiek įrašyti
#XMSG package is valid
Valid_Package_Message=Paketas yra tinkamas.
#XMSG
Object_In_Other_Pacakge=Objektas „{0}“ yra pakete „{1}“.
#XMSG
Dependent_Object_In_Other_Pacakge=Objektas „{0}“ yra pakete „{1}“, kuris nėra nurodytas kaip būtinas paketas.
#XMSG
Dependent_Object_Missing=Objektas „{0}“ yra srityje „{1}“.
#XMSG
Dependent_Missing_Pattern=Objektas „{0}“ priklauso nuo:\r\n{1}
#XMSG: error message for saving the package
Save_Error_Message=Paketo įrašyti nepavyko. {0}
#XFLD title of save busy dialog
Title_Save_Busy_Dialog=Įrašoma
#XMSG: message of it takes long time to save
Long_Time_Save_Busy_Dialog=Įrašymas trunka ilgiau nei įprastai. Palaukite.
#XMSG: erro message of missing dependency
Dependency_Missing=Trūksta priklausomybės.
#XMSG: erro message of missing dependency
Object_Dependency_Missing=Trūksta „{0}“ priklausomybės.
#XMSG message of validating busy dialog
Validating_Package=Tikrinamas paketas
#XFLD title of validating busy dialog
Title_Validating_Busy_Dialog=Apdorojama
#XMSG %%0 is a placeholder, don’t change it
Missing_Dependency=Trūksta priklausomybių %%0
#XMSG %%0 is a placeholder, don’t change it
Circular_Dependency=Ciklinė priklausomybė %%0
#XMSG Object is added to package successfully
Successfully_Add=Pridėta
#XMSG
Circular_Dependency_Detail=„{0}“ įrašyti nepavyko, nes turi ciklinę priklausomybę su paketu „{1}“.
#XFLD label of searching objects in space
Search_In_Sapce=Ieškoti: {0}
#XFLD title of deleting package confirm dialog
Package_Deleting_Dialog_Title=Naikinti
#XMSG content message of deleting package confirm dialog
Package_Deleting_Dialog_Message=Ar norite panaikinti pasirinktus paketus?
#XMSG
Package_Cannot_Delete=Pasirinkto paketo reikia kitiems paketams, todėl jo negalima panaikinti.
#XMSG
Package_Required_By_Singular=(būtinas {0} paketui)
#XMSG
Package_Required_By_Plural=(būtinas {0} paketams)
#XFLD display name of owner column
owner=Savininkas
#XFLD title of deleting package error dialog
Deleting_Error_Dialog_Title=Klaida
#XFLD title of deleting package alert dialog
Deleting_Alert_Dialog_Title=Įspėjimas
#XBUT
Deleting_Alert_Dialog_Ok=Gerai
#XFLD
MyContent=Mano turinys
#XFLD
NotExported=Neeksportuota
#XFLD
Exporting=Eksportuojama
#XFLD
Exported=Eksportuota
#XFLD
DesignTimeError=Kūrimo laiko klaida
#XFLD
ExportFailed=Eksportuoti nepavyko
#XFLD
Cancelling=Atšaukiama
#XFLD
ChangesToExport=Eksportuotini keitimai
#XMSG
Exporting_Package=Eksportuojama {0}. Pranešime, kai procesas bus baigtas.
#XFLD: Object type is businessEntityVariant
TypeBusinessEntityVariant=Verslo vieneto versija
#XFLD: Object type is businessEntity
TypeBusinessEntity=Verslo vienetas
#XFLD
Tooltip_Add=Pridėti
#XFLD
Tooltip_Edit=Redaguoti
#XFLD
Tooltip_Delete=Naikinti
#XFLD
Tooltip_Refresh=Atnaujinti
#XMSG
ExportToOverwrite=Šis paketas jau buvo eksportuotas į turinio tinklą naudojant versiją {0}. Ar norite jį perrašyti?
# XFLD Column businessName
Ext_Selection_Col_Name=Pavadinimas
# XFLD Column Location
Ext_Selection_Col_Location=Vieta
# XFLD
Col_Name=Pavadinimas
# XFLD
Col_Description=Aprašas
# XFLD Label
MoveTo_Label=Perkelti į aplanką
#XMIT Add versions menu button text in Data Builder
versions=Versijos
createVersion=Kurti versiją
versionHistory=Versijos istorija
#XMSG
Package_Depends_On_DP=Šis paketas priklauso nuo duomenų produktų
#XMSG
Package_Depends_On_DP_Warning=Prieš importuodami šį paketą, užtikrinkite, kad toliau pateikti duomenų produktai prieinami tikslinėje erdvėje:\r\n\r\n{0}
# XFLD
Export_Confirm_Dailog_Title=Patvirtinimas
#XMSG
Package_Contains_DataObjects=Šiame pakete yra šie objektai, kuriuose yra transportuotini duomenys:\r\n\r\n{0} \r\n\r\nUžtikrinkite, kad dėl šių objektų perkėlimo jūsų pakete nebūtų netinkamai prieinami asmeniniai ar neskelbtini duomenys.
