# https://wiki.wdf.sap.corp/wiki/display/DWAAS/How+to+add+text+classifications+for+translatable+texts
#
# use the following text types for short texts (up to 120 characters)
# XACT Accessibility
# XBUT Button
# XCKL Checkbox
# XCOL Column header
# XFLD Label
# XGRP Group title
# XLNK Hyperlink
# XLOG Log entry
# XLST List box item
# XMIT Menu item
# XMSG Message
# XRBL Radio button
# XSEL Selection
# XTIT Table title
# XTOL Tooltip
#
# for longer texts (more than 120 characters) use
# YINS Instruction
# YMSG Message

#=============from skyline============
#XFLD general tab of package editor
Tab_General=一般
#XFLD objects tab of package editor
Tab_Objects=物件
#XFLD required packages tab of package editor
Tab_RequiredPkgs=必要套件
#XFLD label for business name input
BusinessName=業務名稱
#XFLD label for technical name input
TechnicalName=技術名稱
#XFLD label for purpose input
BusinessPurpose=業務用途
#XTIT title of save dialog
Dilaog_Save=儲存
#XBUT save button of save dialog
Btn_Save=儲存
#XBUT cancel button of save dialog
Btn_Cancel=取消
#XFLD title of objects section
Objects=物件
#XFLD title of required packages section
RequiredPackages=必要套件
#XTIT title of package selection dialog
Dialog_SelectPackages=選擇套件
#XMSG no data text of package selection dialog
Dilaog_NoDataText=找不到儲藏庫套件
#XMSG message of adding objects
Msg_ObjectAdded=已新增 {0} 個物件
#XMSG message of removing objects
Msg_ObjectRemoved=已移除 {0} 個物件
#XMSG message of adding required packages
Msg_RequiredPackageAdded=已新增 {0} 個必要套件
#XMSG message of removing required packages
Msg_RequiredPackageRemoved=已移除 {0} 個必要套件
#XBUT add button in toolbar
Toolbar_Btn_Add=新增
#XBUT remove button in toolbar
Toolbar_Btn_Remove=移除
#XCOL status column of objects and required packages table
Table_Col_Status=結果
#XLNK more link of status details
Status_More_Link=顯示詳細資料...
#XFLD folder name
FolderName=名稱
#XFLD folder description
FolderDescription=說明
#XBUT ok button
Btn_OK=確定
#XFLD title of export location dialog
ExportLocationDialog=匯出位置
#XFLD lable of category field
Category=種類
#XFLD label of location field
Location=目標位置
#XFLD lable of version field
SemanticVersion=版本
#XFLD label of current exported version
CurrentExportedPackage=目前匯出的版本
#XFLD label of status field
ExportStatus=狀態
#XFLD tooltip of export button
Tooltip_Export=匯出
#XFLD tooltip of save button
Tooltip_Save=儲存
#XFLD tooltip of validate button
Tooltip_Validate=驗證
#XMSG
InvalidVersion=請以格式 "x.y.z" 輸入版本。
#XMSG
VersionLowerThanBefore=版本不可低於上一版本。
#XMSG
Empty_Version=請輸入套件版本。
#XMSG
Package_Missing_Technical_Name=輸入套件的技術名稱。
#=============from skyline============

#XFLD lable for space combo selection
Spaces=空間：
#XFLD section title of landing page
Packages=套件
#XCOL business name column
Table_Col_BusinessName=業務名稱
#XCOL technical name column
Table_Col_TechnicalName=技術名稱
#XCOL sapce column
Table_Col_Space=空間
#XCOL create on column
Table_Col_CreatedOn=建立日期
#XCOL entity type column
Table_Col_Type=類型
#XCOL entity type column
Table_Col_Object_Status=狀態
#XMSG message of deleting packages
Msg_PackageRemoved=已移除 {0} 個套件
#XMIT menu item of all spaces
Op_AllSpaces=所有空間
#XFLD default business name of a new package
NewPackage_BusinessName=套件 1
#XFLD default technical name of a new package
NewPackage_TechnicalName=Package_1
#XFLD default name of a new package displayed in breadcrumb
Breadcrumb_NewPackage_Name=新套件
#XFLD title of object selection dialog
ObjectsDialog_Title=新增物件
#XMSG dependencies are fully resolved
Dependency_Resolved=就緒可新增 (所有已解析的相關性 ({0}))
#XMSG dependencies are partially resolved
Dependency_Partial_Resolved=就緒可新增 (部份已解析的相關性 ({0}/{1})
#XMSG dependencies are not found
Dependency_Not_Found=找不到相關性
#XMSG dependencies are in other package
Dependency_In_Other_Package=無法新增：已在套件「{0}」中
#XMSG dependencies are in other space
Dependency_In_Other_Space=無法新增：已在空間 {0} 中
#XMSG
Cannot_Add_Managed_Content=無法新增：管理的內容
#XMSG
Cannot_Add_Due_To_Dependency_Has_Managed_Content=無法新增：相關性有管理的內容
#XMSG dependencies are in current space
Dependency_In_Current_Package=已在套件中
#XMSG dependencies are in required package
Dependency_In_Required_Package=已在必要套件中
#XMSG package arelady exists
Package_Duplicate_Name=儲藏庫中已有套件「{0}」。請輸入其他名稱。
#XMSG package name is required
Package_Name_Missing=請輸入套件名稱。
#XMSG package version is required
Package_Version_Missing=請輸入套件版本。
#XMSG package is drafted
Package_Draft_Warning=草稿套件。按一下「儲存」驗證相關性，並確認更改內容。
#XFLD title of validation message dialog
Validation_MessageBox_Title=驗證訊息
#XMSG content message of validation message dialog
Validation_MessageBox_Content=「{0}」無效且無法匯出。您必須手動新增所需物件或透過必要套件，完全解析所有物件相關性。
#XBUT save anyway button
Save_Anyway=一律儲存
#XMSG package is valid
Valid_Package_Message=套件無效。
#XMSG
Object_In_Other_Pacakge=物件「{0}」位於套件「{1}」中。
#XMSG
Dependent_Object_In_Other_Pacakge=物件「{0}」位於未指定為必要套件的套件「{1}」中。
#XMSG
Dependent_Object_Missing=物件「{0}」位於空間 {1} 中。
#XMSG
Dependent_Missing_Pattern=物件「{0}」根據：\r\n{1}
#XMSG: error message for saving the package
Save_Error_Message=無法儲存套件。{0}
#XFLD title of save busy dialog
Title_Save_Busy_Dialog=儲存中
#XMSG: message of it takes long time to save
Long_Time_Save_Busy_Dialog=儲存作業花費較長時間，請稍候。
#XMSG: erro message of missing dependency
Dependency_Missing=缺少相關性。
#XMSG: erro message of missing dependency
Object_Dependency_Missing=缺少「{0}」的相關性。
#XMSG message of validating busy dialog
Validating_Package=驗證套件中
#XFLD title of validating busy dialog
Title_Validating_Busy_Dialog=處理中
#XMSG %%0 is a placeholder, don’t change it
Missing_Dependency=缺少相關性 %%0
#XMSG %%0 is a placeholder, don’t change it
Circular_Dependency=循環相關性 %%0
#XMSG Object is added to package successfully
Successfully_Add=已新增
#XMSG
Circular_Dependency_Detail=由於「{0}」具有與套件「{1}」的循環相關性，因此無法儲存。
#XFLD label of searching objects in space
Search_In_Sapce=搜尋：{0}
#XFLD title of deleting package confirm dialog
Package_Deleting_Dialog_Title=刪除
#XMSG content message of deleting package confirm dialog
Package_Deleting_Dialog_Message=您要刪除所選套件嗎？
#XMSG
Package_Cannot_Delete=其他套件需要所選套件，無法刪除。
#XMSG
Package_Required_By_Singular=({0} 個套件需要)
#XMSG
Package_Required_By_Plural=({0} 個套件需要)
#XFLD display name of owner column
owner=所有人
#XFLD title of deleting package error dialog
Deleting_Error_Dialog_Title=錯誤
#XFLD title of deleting package alert dialog
Deleting_Alert_Dialog_Title=警示
#XBUT
Deleting_Alert_Dialog_Ok=確定
#XFLD
MyContent=我的內容
#XFLD
NotExported=未匯出
#XFLD
Exporting=匯出中
#XFLD
Exported=已匯出
#XFLD
DesignTimeError=設計時期錯誤
#XFLD
ExportFailed=匯出失敗
#XFLD
Cancelling=取消中
#XFLD
ChangesToExport=要匯出的更改
#XMSG
Exporting_Package=正在匯出 {0}，程序完成時將會通知您。
#XFLD: Object type is businessEntityVariant
TypeBusinessEntityVariant=業務實體版本
#XFLD: Object type is businessEntity
TypeBusinessEntity=業務實體
#XFLD
Tooltip_Add=新增
#XFLD
Tooltip_Edit=編輯
#XFLD
Tooltip_Delete=刪除
#XFLD
Tooltip_Refresh=重新整理
#XMSG
ExportToOverwrite=此套件已匯出至內容網路，版本為 {0}。您要覆寫嗎？
# XFLD Column businessName
Ext_Selection_Col_Name=名稱
# XFLD Column Location
Ext_Selection_Col_Location=位置
# XFLD
Col_Name=名稱
# XFLD
Col_Description=說明
# XFLD Label
MoveTo_Label=移至資料夾
#XMIT Add versions menu button text in Data Builder
versions=版本
createVersion=建立版本
versionHistory=版本歷史記錄
#XMSG
Package_Depends_On_DP=此套件根據資料產品而定
#XMSG
Package_Depends_On_DP_Warning=匯入此套件前，請確保目標空間可使用下列資料產品：\r\n\r\n{0}
# XFLD
Export_Confirm_Dailog_Title=確認
#XMSG
Package_Contains_DataObjects=此套件包含下列包含要傳輸資料的物件：\r\n\r\n{0}\r\n\r\n請在套件中傳輸這些物件後，確保不會不當公開個人或敏感資料。
