# https://wiki.wdf.sap.corp/wiki/display/DWAAS/How+to+add+text+classifications+for+translatable+texts
#
# use the following text types for short texts (up to 120 characters)
# XACT Accessibility
# XBUT Button
# XCKL Checkbox
# XCOL Column header
# XFLD Label
# XGRP Group title
# XLNK Hyperlink
# XLOG Log entry
# XLST List box item
# XMIT Menu item
# XMSG Message
# XRBL Radio button
# XSEL Selection
# XTIT Table title
# XTOL Tooltip
#
# for longer texts (more than 120 characters) use
# YINS Instruction
# YMSG Message

#=============from skyline============
#XFLD general tab of package editor
Tab_General=Yleinen
#XFLD objects tab of package editor
Tab_Objects=Objektit
#XFLD required packages tab of package editor
Tab_RequiredPkgs=Pakolliset paketit
#XFLD label for business name input
BusinessName=Liiketoiminnallinen nimi
#XFLD label for technical name input
TechnicalName=Tekninen nimi
#XFLD label for purpose input
BusinessPurpose=Liiketoiminnallinen tarkoitus
#XTIT title of save dialog
Dilaog_Save=Tallenna
#XBUT save button of save dialog
Btn_Save=Tallenna
#XBUT cancel button of save dialog
Btn_Cancel=Peruuta
#XFLD title of objects section
Objects=Objektit
#XFLD title of required packages section
RequiredPackages=Pakolliset paketit
#XTIT title of package selection dialog
Dialog_SelectPackages=Valitse paketit
#XMSG no data text of package selection dialog
Dilaog_NoDataText=Tietohakemistopaketteja ei löytynyt
#XMSG message of adding objects
Msg_ObjectAdded={0} objektia lisätty
#XMSG message of removing objects
Msg_ObjectRemoved={0} objektia poistettu
#XMSG message of adding required packages
Msg_RequiredPackageAdded={0} pakollista pakettia lisätty
#XMSG message of removing required packages
Msg_RequiredPackageRemoved={0} pakollista pakettia poistettu
#XBUT add button in toolbar
Toolbar_Btn_Add=Lisää
#XBUT remove button in toolbar
Toolbar_Btn_Remove=Poista
#XCOL status column of objects and required packages table
Table_Col_Status=Tulos
#XLNK more link of status details
Status_More_Link=Näytä lisätiedot...
#XFLD folder name
FolderName=Nimi
#XFLD folder description
FolderDescription=Kuvaus
#XBUT ok button
Btn_OK=OK
#XFLD title of export location dialog
ExportLocationDialog=Viennin sijainti
#XFLD lable of category field
Category=Luokka
#XFLD label of location field
Location=Kohdesijainti
#XFLD lable of version field
SemanticVersion=Versio
#XFLD label of current exported version
CurrentExportedPackage=Nykyinen viety versio
#XFLD label of status field
ExportStatus=Tila
#XFLD tooltip of export button
Tooltip_Export=Vie
#XFLD tooltip of save button
Tooltip_Save=Tallenna
#XFLD tooltip of validate button
Tooltip_Validate=Validoi
#XMSG
InvalidVersion=Syötä versio muodossa ”x.y.z”.
#XMSG
VersionLowerThanBefore=Versio ei voi olla aiempi kuin edeltävä versio.
#XMSG
Empty_Version=Syötä pakettiversio.
#XMSG
Package_Missing_Technical_Name=Syötä paketin tekninen nimi.
#=============from skyline============

#XFLD lable for space combo selection
Spaces=Tila:
#XFLD section title of landing page
Packages=Paketit
#XCOL business name column
Table_Col_BusinessName=Liiketoiminnallinen nimi
#XCOL technical name column
Table_Col_TechnicalName=Tekninen nimi
#XCOL sapce column
Table_Col_Space=Tila
#XCOL create on column
Table_Col_CreatedOn=Luontipäivämäärä
#XCOL entity type column
Table_Col_Type=Tyyppi
#XCOL entity type column
Table_Col_Object_Status=Tila
#XMSG message of deleting packages
Msg_PackageRemoved={0} pakettia poistettu
#XMIT menu item of all spaces
Op_AllSpaces=Kaikki tilat
#XFLD default business name of a new package
NewPackage_BusinessName=Paketti 1
#XFLD default technical name of a new package
NewPackage_TechnicalName=Paketti_1
#XFLD default name of a new package displayed in breadcrumb
Breadcrumb_NewPackage_Name=Uusi paketti
#XFLD title of object selection dialog
ObjectsDialog_Title=Lisää objekteja
#XMSG dependencies are fully resolved
Dependency_Resolved=Valmis lisättäväksi (kaikki sidonnaisuudet ratkaistu ({0}))
#XMSG dependencies are partially resolved
Dependency_Partial_Resolved=Valmis lisättäväksi (joitakin sidonnaisuuksia ei ratkaistu ({0}/{1}))
#XMSG dependencies are not found
Dependency_Not_Found=Sidonnaisuuksia ei löydy
#XMSG dependencies are in other package
Dependency_In_Other_Package=Ei voi lisätä: on jo paketissa ”{0}”
#XMSG dependencies are in other space
Dependency_In_Other_Space=Ei voi lisätä: tilassa {0}
#XMSG
Cannot_Add_Managed_Content=Ei voi lisätä: hallinnoitu sisältö
#XMSG
Cannot_Add_Due_To_Dependency_Has_Managed_Content=Ei voi lisätä: sidonnaisuudella on hallinnoitua sisältöä
#XMSG dependencies are in current space
Dependency_In_Current_Package=On jo paketissa
#XMSG dependencies are in required package
Dependency_In_Required_Package=On jo pakollisessa paketissa
#XMSG package arelady exists
Package_Duplicate_Name=Paketti ''{0}'' on jo olemassa tietohakemistossa. Syötä toinen nimi.
#XMSG package name is required
Package_Name_Missing=Syötä paketin nimi.
#XMSG package version is required
Package_Version_Missing=Syötä pakettiversio.
#XMSG package is drafted
Package_Draft_Warning=Luonnospaketti. Napsauta ”Tallenna” vahvistaaksesi sidonnaisuudet ja tekemäsi muutokset.
#XFLD title of validation message dialog
Validation_MessageBox_Title=Validointi-ilmoitukset
#XMSG content message of validation message dialog
Validation_MessageBox_Content=”{0}” ei kelpaa, eikä sitä voi viedä. Sinun täytyy ratkaista kaikki objektisidonnaisuudet lisäämällä tarvittavat objektit manuaalisesti tai pakollisen paketin kautta.
#XBUT save anyway button
Save_Anyway=Tallenna silti
#XMSG package is valid
Valid_Package_Message=Paketti on kelpaava.
#XMSG
Object_In_Other_Pacakge=Objekti ”{0}” on paketissa ”{1}”.
#XMSG
Dependent_Object_In_Other_Pacakge=Objekti ”{0}” paketissa ”{1}”, jota ei ole määritetty pakolliseksi paketiksi.
#XMSG
Dependent_Object_Missing=Objekti ”{0}” tilassa {1}.
#XMSG
Dependent_Missing_Pattern=Objekti ”{0}” riippuu:\r\n{1}
#XMSG: error message for saving the package
Save_Error_Message=Paketin tallennus epäonnistui. {0}
#XFLD title of save busy dialog
Title_Save_Busy_Dialog=Tallennetaan
#XMSG: message of it takes long time to save
Long_Time_Save_Busy_Dialog=Tallennus kestää tavallista kauemmin. Odota.
#XMSG: erro message of missing dependency
Dependency_Missing=Sidonnaisuus puuttuu.
#XMSG: erro message of missing dependency
Object_Dependency_Missing=Kohteen ”{0}” sidonnaisuus puuttuu.
#XMSG message of validating busy dialog
Validating_Package=Validoidaan pakettia
#XFLD title of validating busy dialog
Title_Validating_Busy_Dialog=Käsitellään
#XMSG %%0 is a placeholder, don’t change it
Missing_Dependency=Puuttuvat sidonnaisuudet %%0
#XMSG %%0 is a placeholder, don’t change it
Circular_Dependency=Silmukkariippuvuus %%0
#XMSG Object is added to package successfully
Successfully_Add=Lisätty
#XMSG
Circular_Dependency_Detail=Kohdetta ”{0}” ei voi tallentaa, koska sillä on silmukkariippuvuus paketin ”{1}” kanssa.
#XFLD label of searching objects in space
Search_In_Sapce=Hae kohteesta: {0}
#XFLD title of deleting package confirm dialog
Package_Deleting_Dialog_Title=Poista
#XMSG content message of deleting package confirm dialog
Package_Deleting_Dialog_Message=Haluatko poistaa valitut paketit?
#XMSG
Package_Cannot_Delete=Valittu paketti on pakollinen muille paketeille eikä sitä voi poistaa.
#XMSG
Package_Required_By_Singular=(pakollinen - {0} paketti)
#XMSG
Package_Required_By_Plural=(pakollinen - {0} pakettia)
#XFLD display name of owner column
owner=Omistaja
#XFLD title of deleting package error dialog
Deleting_Error_Dialog_Title=Virhe
#XFLD title of deleting package alert dialog
Deleting_Alert_Dialog_Title=Hälytys
#XBUT
Deleting_Alert_Dialog_Ok=OK
#XFLD
MyContent=Oma sisältö
#XFLD
NotExported=Ei viety
#XFLD
Exporting=Vienti
#XFLD
Exported=Viety
#XFLD
DesignTimeError=Suunnitteluaikavirhe
#XFLD
ExportFailed=Vienti epäonnistui
#XFLD
Cancelling=Peruutetaan
#XFLD
ChangesToExport=Muutoksia vientiin
#XMSG
Exporting_Package=Viedään {0}. Ilmoitamme, kun prosessi on päättynyt.
#XFLD: Object type is businessEntityVariant
TypeBusinessEntityVariant=Liiketoimintaentiteetin versio
#XFLD: Object type is businessEntity
TypeBusinessEntity=Liiketoimintaentiteetti
#XFLD
Tooltip_Add=Lisää
#XFLD
Tooltip_Edit=Muokkaa
#XFLD
Tooltip_Delete=Poista
#XFLD
Tooltip_Refresh=Päivitä
#XMSG
ExportToOverwrite=Tämä paketti on jo viety sisällön verkostoon versiolla {0}. Haluatko korvata sen?
# XFLD Column businessName
Ext_Selection_Col_Name=Nimi
# XFLD Column Location
Ext_Selection_Col_Location=Sijainti
# XFLD
Col_Name=Nimi
# XFLD
Col_Description=Kuvaus
# XFLD Label
MoveTo_Label=Siirrä kansioon
#XMIT Add versions menu button text in Data Builder
versions=Versiot
createVersion=Luo versio
versionHistory=Versiohistoria
#XMSG
Package_Depends_On_DP=Tämä paketti riippuu tietotuotteista
#XMSG
Package_Depends_On_DP_Warning=Varmista ennen tämän paketin tuontia, että seuraavat tietotuotteet ovat käytettävissä kohdetilaa varten:\r\n\r\n{0}
# XFLD
Export_Confirm_Dailog_Title=Vahvistus
#XMSG
Package_Contains_DataObjects=Tämä paketti sisältää seuraavat objektit, jotka sisältävät tietoja siirtoa varten:\r\n\r\n{0} \r\n\r\nVarmista, että henkilökohtaisia tai arkaluonteisia tietoja ei paljasteta väärin näiden objektien paketissa siirron seurauksena.
