# https://wiki.wdf.sap.corp/wiki/display/DWAAS/How+to+add+text+classifications+for+translatable+texts
#
# use the following text types for short texts (up to 120 characters)
# XACT Accessibility
# XBUT Button
# XCKL Checkbox
# XCOL Column header
# XFLD Label
# XGRP Group title
# XLNK Hyperlink
# XLOG Log entry
# XLST List box item
# XMIT Menu item
# XMSG Message
# XRBL Radio button
# XSEL Selection
# XTIT Table title
# XTOL Tooltip
#
# for longer texts (more than 120 characters) use
# YINS Instruction
# YMSG Message

#=============from skyline============
#XFLD general tab of package editor
Tab_General=Generelt
#XFLD objects tab of package editor
Tab_Objects=Objekter
#XFLD required packages tab of package editor
Tab_RequiredPkgs=Obligatoriske pakker
#XFLD label for business name input
BusinessName=Forretningsnavn
#XFLD label for technical name input
TechnicalName=Teknisk navn
#XFLD label for purpose input
BusinessPurpose=Forretningsformål
#XTIT title of save dialog
Dilaog_Save=Gem
#XBUT save button of save dialog
Btn_Save=Gem
#XBUT cancel button of save dialog
Btn_Cancel=Annuller
#XFLD title of objects section
Objects=Objekter
#XFLD title of required packages section
RequiredPackages=Obligatoriske pakker
#XTIT title of package selection dialog
Dialog_SelectPackages=Vælg pakker
#XMSG no data text of package selection dialog
Dilaog_NoDataText=Fandt ingen lagerpakker
#XMSG message of adding objects
Msg_ObjectAdded={0} objekter tilføjet
#XMSG message of removing objects
Msg_ObjectRemoved={0} objekter fjernet
#XMSG message of adding required packages
Msg_RequiredPackageAdded={0} obligatoriske pakker tilføjet
#XMSG message of removing required packages
Msg_RequiredPackageRemoved={0} obligatoriske pakker fjernet
#XBUT add button in toolbar
Toolbar_Btn_Add=Tilføj
#XBUT remove button in toolbar
Toolbar_Btn_Remove=Fjern
#XCOL status column of objects and required packages table
Table_Col_Status=Resultat
#XLNK more link of status details
Status_More_Link=Vis detaljer...
#XFLD folder name
FolderName=Navn
#XFLD folder description
FolderDescription=Beskrivelse
#XBUT ok button
Btn_OK=OK
#XFLD title of export location dialog
ExportLocationDialog=Eksportplacering
#XFLD lable of category field
Category=Kategori
#XFLD label of location field
Location=Målplacering
#XFLD lable of version field
SemanticVersion=Version
#XFLD label of current exported version
CurrentExportedPackage=Aktuel eksporteret version
#XFLD label of status field
ExportStatus=Status
#XFLD tooltip of export button
Tooltip_Export=Eksportér
#XFLD tooltip of save button
Tooltip_Save=Gem
#XFLD tooltip of validate button
Tooltip_Validate=Valider
#XMSG
InvalidVersion=Indtast en version i formatet "x.y.z".
#XMSG
VersionLowerThanBefore=Version må ikke være lavere end foregående version.
#XMSG
Empty_Version=Indtast en pakkeversion.
#XMSG
Package_Missing_Technical_Name=Indtast et teknisk navn for pakken.
#=============from skyline============

#XFLD lable for space combo selection
Spaces=Space:
#XFLD section title of landing page
Packages=Pakker
#XCOL business name column
Table_Col_BusinessName=Forretningsnavn
#XCOL technical name column
Table_Col_TechnicalName=Teknisk navn
#XCOL sapce column
Table_Col_Space=Space
#XCOL create on column
Table_Col_CreatedOn=Oprettet den
#XCOL entity type column
Table_Col_Type=Type
#XCOL entity type column
Table_Col_Object_Status=Status
#XMSG message of deleting packages
Msg_PackageRemoved={0} pakker fjernet
#XMIT menu item of all spaces
Op_AllSpaces=Alle spaces
#XFLD default business name of a new package
NewPackage_BusinessName=Pakke 1
#XFLD default technical name of a new package
NewPackage_TechnicalName=Pakke_1
#XFLD default name of a new package displayed in breadcrumb
Breadcrumb_NewPackage_Name=Ny pakke
#XFLD title of object selection dialog
ObjectsDialog_Title=Tilføj objekter
#XMSG dependencies are fully resolved
Dependency_Resolved=Klar til tilføjelse (alle afhængigheder løst ({0}))
#XMSG dependencies are partially resolved
Dependency_Partial_Resolved=Klar til tilføjelse (nogle afhængigheder ikke løst ({0}/{1}))
#XMSG dependencies are not found
Dependency_Not_Found=Kan ikke finde afhængigheder
#XMSG dependencies are in other package
Dependency_In_Other_Package=Kan ikke tilføje: Allerede i pakke "{0}"
#XMSG dependencies are in other space
Dependency_In_Other_Space=Kan ikke tilføje: I space {0}
#XMSG
Cannot_Add_Managed_Content=Kan ikke tilføje: Administreret indhold
#XMSG
Cannot_Add_Due_To_Dependency_Has_Managed_Content=Kan ikke tilføje: Afhængighed har administreret indhold
#XMSG dependencies are in current space
Dependency_In_Current_Package=Allerede i pakke
#XMSG dependencies are in required package
Dependency_In_Required_Package=Allerede i obligatorisk pakke
#XMSG package arelady exists
Package_Duplicate_Name=Pakken "{0}" findes allerede i lageret. Indtast et andet navn.
#XMSG package name is required
Package_Name_Missing=Indtast et pakkenavn.
#XMSG package version is required
Package_Version_Missing=Indtast en pakkeversion.
#XMSG package is drafted
Package_Draft_Warning=Udkastpakke. Klik på "Gem" for at verificere afhængigheder og bekræfte dine ændringer.
#XFLD title of validation message dialog
Validation_MessageBox_Title=Valideringsmeddelelser
#XMSG content message of validation message dialog
Validation_MessageBox_Content="{0}" er ugyldig og kan ikke eksporteres. Du skal løse alle objektafhængigheder helt ved at tilføje de nødvendige objekter manuelt eller via en obligatorisk pakke.
#XBUT save anyway button
Save_Anyway=Gem alligevel
#XMSG package is valid
Valid_Package_Message=Pakken er gyldig.
#XMSG
Object_In_Other_Pacakge=Objekt "{0}" er i pakke "{1}".
#XMSG
Dependent_Object_In_Other_Pacakge=Objekt "{0}" i pakke "{1}", som ikke er angivet som en obligatorisk pakke.
#XMSG
Dependent_Object_Missing=Objekt "{0}" i space {1}.
#XMSG
Dependent_Missing_Pattern=Objekt "{0}" afhænger af:\r\n{1}
#XMSG: error message for saving the package
Save_Error_Message=Pakken kunne ikke gemmes. {0}
#XFLD title of save busy dialog
Title_Save_Busy_Dialog=Gemmer
#XMSG: message of it takes long time to save
Long_Time_Save_Busy_Dialog=Det tager længere tid at gemme end normalt. Vent.
#XMSG: erro message of missing dependency
Dependency_Missing=Afhængighed mangler.
#XMSG: erro message of missing dependency
Object_Dependency_Missing=Afhængighed for "{0}" mangler.
#XMSG message of validating busy dialog
Validating_Package=Validerer pakke
#XFLD title of validating busy dialog
Title_Validating_Busy_Dialog=Behandler
#XMSG %%0 is a placeholder, don’t change it
Missing_Dependency=Manglende afhængigheder %%0
#XMSG %%0 is a placeholder, don’t change it
Circular_Dependency=Cirkulær afhængighed %%0
#XMSG Object is added to package successfully
Successfully_Add=Tilføjet
#XMSG
Circular_Dependency_Detail="{0}" kan ikke gemmes, fordi den har en cirkulær afhængighed med pakke "{1}".
#XFLD label of searching objects in space
Search_In_Sapce=Søg i: {0}
#XFLD title of deleting package confirm dialog
Package_Deleting_Dialog_Title=Slet
#XMSG content message of deleting package confirm dialog
Package_Deleting_Dialog_Message=Vil du slette de valgte pakker?
#XMSG
Package_Cannot_Delete=Den valgte pakke kræves af andre pakker og kan ikke slettes.
#XMSG
Package_Required_By_Singular=(kræves af {0} pakke)
#XMSG
Package_Required_By_Plural=(kræves af {0} pakker)
#XFLD display name of owner column
owner=Ejer
#XFLD title of deleting package error dialog
Deleting_Error_Dialog_Title=Fejl
#XFLD title of deleting package alert dialog
Deleting_Alert_Dialog_Title=Advarsel
#XBUT
Deleting_Alert_Dialog_Ok=OK
#XFLD
MyContent=Mit indhold
#XFLD
NotExported=Ikke eksporteret
#XFLD
Exporting=Eksporterer
#XFLD
Exported=Eksporteret
#XFLD
DesignTimeError=Designtidsfejl
#XFLD
ExportFailed=Kunne ikke eksporteres
#XFLD
Cancelling=Annullerer
#XFLD
ChangesToExport=Ændringer i eksport
#XMSG
Exporting_Package=Eksporterer {0}. Vi giver dig besked, når processen er afsluttet.
#XFLD: Object type is businessEntityVariant
TypeBusinessEntityVariant=Forretningsentitetsversion
#XFLD: Object type is businessEntity
TypeBusinessEntity=Forretningsentitet
#XFLD
Tooltip_Add=Tilføj
#XFLD
Tooltip_Edit=Rediger
#XFLD
Tooltip_Delete=Slet
#XFLD
Tooltip_Refresh=Opdater
#XMSG
ExportToOverwrite=Denne pakke er allerede eksporteret til Content Network med versionen {0}. Vil du overskrive den?
# XFLD Column businessName
Ext_Selection_Col_Name=Navn
# XFLD Column Location
Ext_Selection_Col_Location=Placering
# XFLD
Col_Name=Navn
# XFLD
Col_Description=Beskrivelse
# XFLD Label
MoveTo_Label=Flyt til mappe
#XMIT Add versions menu button text in Data Builder
versions=Versioner
createVersion=Opret version
versionHistory=Versionshistorik
#XMSG
Package_Depends_On_DP=Denne pakke afhænger af dataprodukter
#XMSG
Package_Depends_On_DP_Warning=Før du importerer denne pakke, skal du sørge for, at følgende dataprodukter er tilgængelige for målspacet:\r\n\r\n{0}
# XFLD
Export_Confirm_Dailog_Title=Bekræftelse
#XMSG
Package_Contains_DataObjects=Denne pakke indeholder følgende objekter, der indeholder data til transport:\r\n\r\n{0}\r\n\r\nSørg for, at ingen personlige eller følsomme data eksponeres forkert som følge af transport af disse objekter i din pakke.
