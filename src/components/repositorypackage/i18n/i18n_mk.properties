# https://wiki.wdf.sap.corp/wiki/display/DWAAS/How+to+add+text+classifications+for+translatable+texts
#
# use the following text types for short texts (up to 120 characters)
# XACT Accessibility
# XBUT Button
# XCKL Checkbox
# XCOL Column header
# XFLD Label
# XGRP Group title
# XLNK Hyperlink
# XLOG Log entry
# XLST List box item
# XMIT Menu item
# XMSG Message
# XRBL Radio button
# XSEL Selection
# XTIT Table title
# XTOL Tooltip
#
# for longer texts (more than 120 characters) use
# YINS Instruction
# YMSG Message

#=============from skyline============
#XFLD general tab of package editor
Tab_General=Општо
#XFLD objects tab of package editor
Tab_Objects=Објекти
#XFLD required packages tab of package editor
Tab_RequiredPkgs=Задолжителни пакети
#XFLD label for business name input
BusinessName=Деловен назив
#XFLD label for technical name input
TechnicalName=Технички назив
#XFLD label for purpose input
BusinessPurpose=Деловна цел
#XTIT title of save dialog
Dilaog_Save=Зачувај
#XBUT save button of save dialog
Btn_Save=Зачувај
#XBUT cancel button of save dialog
Btn_Cancel=Откажи
#XFLD title of objects section
Objects=Објекти
#XFLD title of required packages section
RequiredPackages=Задолжителни пакети
#XTIT title of package selection dialog
Dialog_SelectPackages=Избери пакети
#XMSG no data text of package selection dialog
Dilaog_NoDataText=Не се пронајдени пакети во репозиториум
#XMSG message of adding objects
Msg_ObjectAdded={0} додадени објекти
#XMSG message of removing objects
Msg_ObjectRemoved={0} отстранети објекти
#XMSG message of adding required packages
Msg_RequiredPackageAdded={0} додадени задолжителни пакети
#XMSG message of removing required packages
Msg_RequiredPackageRemoved={0} отстранети задолжителни пакети
#XBUT add button in toolbar
Toolbar_Btn_Add=Додај
#XBUT remove button in toolbar
Toolbar_Btn_Remove=Отстрани
#XCOL status column of objects and required packages table
Table_Col_Status=Резултат
#XLNK more link of status details
Status_More_Link=Покажи ги деталите...
#XFLD folder name
FolderName=Назив
#XFLD folder description
FolderDescription=Опис
#XBUT ok button
Btn_OK=Во ред
#XFLD title of export location dialog
ExportLocationDialog=Извези локација
#XFLD lable of category field
Category=Категорија
#XFLD label of location field
Location=Целна локација
#XFLD lable of version field
SemanticVersion=Верзија
#XFLD label of current exported version
CurrentExportedPackage=Тековна извезена верзија
#XFLD label of status field
ExportStatus=Статус
#XFLD tooltip of export button
Tooltip_Export=Извези
#XFLD tooltip of save button
Tooltip_Save=Зачувај
#XFLD tooltip of validate button
Tooltip_Validate=Потврди
#XMSG
InvalidVersion=Внесете верзија во формат „x.y.z“.
#XMSG
VersionLowerThanBefore=Верзијата не може да биде постара од претходната верзија.
#XMSG
Empty_Version=Внесете верзија на пакет.
#XMSG
Package_Missing_Technical_Name=Внесете технички назив за пакетот.
#=============from skyline============

#XFLD lable for space combo selection
Spaces=Простор:
#XFLD section title of landing page
Packages=Пакети
#XCOL business name column
Table_Col_BusinessName=Деловен назив
#XCOL technical name column
Table_Col_TechnicalName=Технички назив
#XCOL sapce column
Table_Col_Space=Простор
#XCOL create on column
Table_Col_CreatedOn=Создадено на
#XCOL entity type column
Table_Col_Type=Тип
#XCOL entity type column
Table_Col_Object_Status=Статус
#XMSG message of deleting packages
Msg_PackageRemoved={0} отстранети пакети
#XMIT menu item of all spaces
Op_AllSpaces=Сите простори
#XFLD default business name of a new package
NewPackage_BusinessName=Пакет 1
#XFLD default technical name of a new package
NewPackage_TechnicalName=Пакет_1
#XFLD default name of a new package displayed in breadcrumb
Breadcrumb_NewPackage_Name=Нов пакет
#XFLD title of object selection dialog
ObjectsDialog_Title=Додај објекти
#XMSG dependencies are fully resolved
Dependency_Resolved=Подготвено за додавање (Сите зависности се решени ({0}))
#XMSG dependencies are partially resolved
Dependency_Partial_Resolved=Подготвено за додавање (Некои зависности не се решени ({0}){1})
#XMSG dependencies are not found
Dependency_Not_Found=Не може да се пронајдат зависности
#XMSG dependencies are in other package
Dependency_In_Other_Package=Не може да се додаде: веќе е во пакет „{0}“
#XMSG dependencies are in other space
Dependency_In_Other_Space=Не може да се додаде: во простор {0}
#XMSG
Cannot_Add_Managed_Content=Не може да се додаде: управувана содржина
#XMSG
Cannot_Add_Due_To_Dependency_Has_Managed_Content=Не може да се додаде: зависноста има управувана содржина
#XMSG dependencies are in current space
Dependency_In_Current_Package=Веќе е во пакет
#XMSG dependencies are in required package
Dependency_In_Required_Package=Веќе е во задолжителен пакет
#XMSG package arelady exists
Package_Duplicate_Name=Пакетот „{0}“ веќе постои во репозиториумот. Внесете друг назив.
#XMSG package name is required
Package_Name_Missing=Внесете назив на пакет. 
#XMSG package version is required
Package_Version_Missing=Внесете верзија на пакет.
#XMSG package is drafted
Package_Draft_Warning=Нацрт-пакет. Кликнете на „Зачувај“ за да ги потврдите зависностите и да ги потврдите промените.
#XFLD title of validation message dialog
Validation_MessageBox_Title=Пораки за потврдување
#XMSG content message of validation message dialog
Validation_MessageBox_Content=„{0}“ е неважечки и не може да се извезе. Мора целосно да ги решите сите зависности на објектот со додавање на потребните објекти рачно или преку задолжителен пакет.
#XBUT save anyway button
Save_Anyway=Сепак зачувај
#XMSG package is valid
Valid_Package_Message=Пакетот е важечки.
#XMSG
Object_In_Other_Pacakge=Објектот „{0}“ е во пакетот „{1}“.
#XMSG
Dependent_Object_In_Other_Pacakge=Објектот „{0}“ во пакетот „{1}“, кој не е наведен како задолжителен пакет.
#XMSG
Dependent_Object_Missing=Објект {0} во простор {1}.
#XMSG
Dependent_Missing_Pattern=Објектот „{0}“ зависи од:\r\n{1}
#XMSG: error message for saving the package
Save_Error_Message=Зачувувањето на пакетот е неуспешно.{0}
#XFLD title of save busy dialog
Title_Save_Busy_Dialog=Се зачувува
#XMSG: message of it takes long time to save
Long_Time_Save_Busy_Dialog=Зачувувањето трае подолго од вообичаеното. Почекајте.
#XMSG: erro message of missing dependency
Dependency_Missing=Недостига зависност.
#XMSG: erro message of missing dependency
Object_Dependency_Missing=Недостига зависност „{0}“.
#XMSG message of validating busy dialog
Validating_Package=Потврдување на пакет
#XFLD title of validating busy dialog
Title_Validating_Busy_Dialog=Се обработува
#XMSG %%0 is a placeholder, don’t change it
Missing_Dependency=Зависности што недостигаат %%0
#XMSG %%0 is a placeholder, don’t change it
Circular_Dependency=Кружна зависност %%0
#XMSG Object is added to package successfully
Successfully_Add=Додадено
#XMSG
Circular_Dependency_Detail=„{0}“ не може да се зачува бидејќи има кружна зависност со пакетот „{1}“.
#XFLD label of searching objects in space
Search_In_Sapce=Пребарувај во: {0}
#XFLD title of deleting package confirm dialog
Package_Deleting_Dialog_Title=Избриши
#XMSG content message of deleting package confirm dialog
Package_Deleting_Dialog_Message=Дали сакате да ги избришете избраните пакети?
#XMSG
Package_Cannot_Delete=Избраниот пакет е задолжителен за други пакети и не може да се избрише.
#XMSG
Package_Required_By_Singular=(задолжителен за {0} пакет)
#XMSG
Package_Required_By_Plural=(задолжително за {0} пакети)
#XFLD display name of owner column
owner=Сопственик
#XFLD title of deleting package error dialog
Deleting_Error_Dialog_Title=Грешка
#XFLD title of deleting package alert dialog
Deleting_Alert_Dialog_Title=Предупредување
#XBUT
Deleting_Alert_Dialog_Ok=Во ред
#XFLD
MyContent=Моја содржина
#XFLD
NotExported=Не е извезено
#XFLD
Exporting=Извезување
#XFLD
Exported=Извезено
#XFLD
DesignTimeError=Грешка во времето на дизајнот
#XFLD
ExportFailed=Извезувањето е неуспешно
#XFLD
Cancelling=Се откажува
#XFLD
ChangesToExport=Промени во извозот
#XMSG
Exporting_Package=Извезување {0}. Ќе ве известиме кога процесот ќе заврши.
#XFLD: Object type is businessEntityVariant
TypeBusinessEntityVariant=Верзија на деловен ентитет
#XFLD: Object type is businessEntity
TypeBusinessEntity=Деловен ентитет
#XFLD
Tooltip_Add=Додај
#XFLD
Tooltip_Edit=Уреди
#XFLD
Tooltip_Delete=Избриши
#XFLD
Tooltip_Refresh=Освежи
#XMSG
ExportToOverwrite=Овој пакет е веќе извезен во мрежата за содржина со верзија {0}. Дали сакате да го замените?
# XFLD Column businessName
Ext_Selection_Col_Name=Назив
# XFLD Column Location
Ext_Selection_Col_Location=Локација
# XFLD
Col_Name=Назив
# XFLD
Col_Description=Опис
# XFLD Label
MoveTo_Label=Премести во папка
#XMIT Add versions menu button text in Data Builder
versions=Верзии
createVersion=Создај верзија
versionHistory=Историја на верзијата
#XMSG
Package_Depends_On_DP=Овој пакет зависи од производите со податоци
#XMSG
Package_Depends_On_DP_Warning=Пред да го увезете овој пакет, проверете дали следните производи со податоци се достапни за целниот простор:\r\n\r\n{0}
# XFLD
Export_Confirm_Dailog_Title=Потврда
#XMSG
Package_Contains_DataObjects=Пакетот ги вклучува следниве објекти што содржат податоци за пренос:\r\n\r\n{0}\r\n\r\nУверете се дека никакви лични или чувствителни податоци нема неправилно да се откријат како резултат на преносот на овие објекти во пакетот. 
