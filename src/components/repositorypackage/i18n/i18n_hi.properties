# https://wiki.wdf.sap.corp/wiki/display/DWAAS/How+to+add+text+classifications+for+translatable+texts
#
# use the following text types for short texts (up to 120 characters)
# XACT Accessibility
# XBUT Button
# XCKL Checkbox
# XCOL Column header
# XFLD Label
# XGRP Group title
# XLNK Hyperlink
# XLOG Log entry
# XLST List box item
# XMIT Menu item
# XMSG Message
# XRBL Radio button
# XSEL Selection
# XTIT Table title
# XTOL Tooltip
#
# for longer texts (more than 120 characters) use
# YINS Instruction
# YMSG Message

#=============from skyline============
#XFLD general tab of package editor
Tab_General=सामान्य
#XFLD objects tab of package editor
Tab_Objects=ऑब्जेक्ट
#XFLD required packages tab of package editor
Tab_RequiredPkgs=आवश्यक पैकेज
#XFLD label for business name input
BusinessName=व्यवसाय नाम
#XFLD label for technical name input
TechnicalName=तकनीकी नाम
#XFLD label for purpose input
BusinessPurpose=व्यवसाय का उद्देश्य
#XTIT title of save dialog
Dilaog_Save=सहेजें
#XBUT save button of save dialog
Btn_Save=सहेजें
#XBUT cancel button of save dialog
Btn_Cancel=रद्द करें
#XFLD title of objects section
Objects=ऑब्जेक्ट
#XFLD title of required packages section
RequiredPackages=आवश्यक पैकेज
#XTIT title of package selection dialog
Dialog_SelectPackages=पैकेज का चयन करें
#XMSG no data text of package selection dialog
Dilaog_NoDataText=कोई कोष पैकेज नहीं मिला
#XMSG message of adding objects
Msg_ObjectAdded={0} ऑब्जेक्ट जोड़े गए
#XMSG message of removing objects
Msg_ObjectRemoved={0} ऑब्जेक्ट निकाले गए
#XMSG message of adding required packages
Msg_RequiredPackageAdded={0} आवश्यक पैकेज जोड़े गए
#XMSG message of removing required packages
Msg_RequiredPackageRemoved={0} आवश्यक पैकेज निकाले गए
#XBUT add button in toolbar
Toolbar_Btn_Add=जोड़ें
#XBUT remove button in toolbar
Toolbar_Btn_Remove=निकालें
#XCOL status column of objects and required packages table
Table_Col_Status=परिणाम
#XLNK more link of status details
Status_More_Link=विवरण दिखाएं...
#XFLD folder name
FolderName=नाम
#XFLD folder description
FolderDescription=वर्णन
#XBUT ok button
Btn_OK=ठीक
#XFLD title of export location dialog
ExportLocationDialog=स्थान निर्यात करें
#XFLD lable of category field
Category=श्रेणी
#XFLD label of location field
Location=लक्ष्य स्थान
#XFLD lable of version field
SemanticVersion=संस्करण
#XFLD label of current exported version
CurrentExportedPackage=वर्तमान निर्यातित संस्करण
#XFLD label of status field
ExportStatus=स्थिति
#XFLD tooltip of export button
Tooltip_Export=निर्यात करें
#XFLD tooltip of save button
Tooltip_Save=सहेजें
#XFLD tooltip of validate button
Tooltip_Validate=सत्यापित करें
#XMSG
InvalidVersion="x.y.z" प्रपत्र में कोई संस्करण दर्ज करें.
#XMSG
VersionLowerThanBefore=पिछले संस्करण से संस्करण कम नहीं हो सकता.
#XMSG
Empty_Version=कोई पैकेज संस्करण दर्ज करें.
#XMSG
Package_Missing_Technical_Name=पैकेज के लिए कोई तकनीकी नाम दर्ज करें.
#=============from skyline============

#XFLD lable for space combo selection
Spaces=स्पेस:
#XFLD section title of landing page
Packages=पैकेज
#XCOL business name column
Table_Col_BusinessName=व्यवसाय नाम
#XCOL technical name column
Table_Col_TechnicalName=तकनीकी नाम
#XCOL sapce column
Table_Col_Space=स्पेस
#XCOL create on column
Table_Col_CreatedOn=निर्माण दिनांक
#XCOL entity type column
Table_Col_Type=प्रकार
#XCOL entity type column
Table_Col_Object_Status=स्थिति
#XMSG message of deleting packages
Msg_PackageRemoved={0} पैकेज निकाले गए
#XMIT menu item of all spaces
Op_AllSpaces=सभी स्थान
#XFLD default business name of a new package
NewPackage_BusinessName=पैकेज 1
#XFLD default technical name of a new package
NewPackage_TechnicalName=पैकेज_1
#XFLD default name of a new package displayed in breadcrumb
Breadcrumb_NewPackage_Name=नया पैकेज
#XFLD title of object selection dialog
ObjectsDialog_Title=ऑब्जेक्ट जोड़ें
#XMSG dependencies are fully resolved
Dependency_Resolved=जोड़ने के लिए तैयार (सभी निर्भरताएं हल हो गईं ({0}))
#XMSG dependencies are partially resolved
Dependency_Partial_Resolved=जोड़ने के लिए तैयार (कुछ निर्भरताएं हल नहीं हुईं ({0}/{1})
#XMSG dependencies are not found
Dependency_Not_Found=निर्भरताएं नहीं पाई जा सकतीं
#XMSG dependencies are in other package
Dependency_In_Other_Package=जोड़ा नहीं जा सकता: पैकेज पहले से ही "{0}" में है
#XMSG dependencies are in other space
Dependency_In_Other_Space=जोड़ा नहीं जा सकता: स्थान {0} में है
#XMSG
Cannot_Add_Managed_Content=जोड़ा नहीं जा सकता: प्रबंधित सामग्री
#XMSG
Cannot_Add_Due_To_Dependency_Has_Managed_Content=जोड़ा नहीं जा सकता: निर्भरता में प्रबंधित सामग्री है
#XMSG dependencies are in current space
Dependency_In_Current_Package=पहले से ही पैकेज में है
#XMSG dependencies are in required package
Dependency_In_Required_Package=पहले से ही आवश्यक पैकेज में है
#XMSG package arelady exists
Package_Duplicate_Name=पैकेज ''{0}'' पहले से ही संग्राहक में मौजूद है. कृपया दूसरा नाम दर्ज करें.
#XMSG package name is required
Package_Name_Missing=कोई पैकेज नाम दर्ज करें.
#XMSG package version is required
Package_Version_Missing=कोई पैकेज संस्करण दर्ज करें.
#XMSG package is drafted
Package_Draft_Warning=ड्राफ़्ट पैकेज. निर्भरताओं को सत्यापित करने और अपने परिवर्तनों की पुष्टि करने के लिए "सहेजें" पर क्लिक करें.
#XFLD title of validation message dialog
Validation_MessageBox_Title=सत्यापन संदेश
#XMSG content message of validation message dialog
Validation_MessageBox_Content="{0}" अमान्य है और निर्यात नहीं किया जा सकता. आपको आवश्यक ऑब्जेक्ट को मैन्युअल रूप से या आवश्यक पैकेज के माध्यम से जोड़कर सभी ऑब्जेक्ट निर्भरताओं का पूर्ण रूप से समाधान करना होगा.
#XBUT save anyway button
Save_Anyway=कैसे भी सहेजें
#XMSG package is valid
Valid_Package_Message=पैकेज अमान्य है.
#XMSG
Object_In_Other_Pacakge=ऑब्जेक्ट "{0}" पैकेज "{1}" में है.
#XMSG
Dependent_Object_In_Other_Pacakge=पैकेज "{1}" में ऑब्जेक्ट "{0}", जो आवश्यक पैकेज के रूप में निर्दिष्ट नहीं है.
#XMSG
Dependent_Object_Missing=स्थान {1} में ऑब्जेक्ट "{0}".
#XMSG
Dependent_Missing_Pattern=ऑब्जेक्ट "{0}" इस पर निर्भर करता है:\r\n{1}
#XMSG: error message for saving the package
Save_Error_Message=पैकेज सहेजना विफल रहा. {0}
#XFLD title of save busy dialog
Title_Save_Busy_Dialog=सहेजना
#XMSG: message of it takes long time to save
Long_Time_Save_Busy_Dialog=सहेजने में समान्य से अधिक समय लग रहा है. कृपया प्रतीक्षा करें.
#XMSG: erro message of missing dependency
Dependency_Missing=निर्भरता गुम है.
#XMSG: erro message of missing dependency
Object_Dependency_Missing="{0}" की निर्भरता गुम है.
#XMSG message of validating busy dialog
Validating_Package=पैकेज का सत्यापन
#XFLD title of validating busy dialog
Title_Validating_Busy_Dialog=प्रोसेसिंग
#XMSG %%0 is a placeholder, don’t change it
Missing_Dependency=गुम निर्भरताएं %%0
#XMSG %%0 is a placeholder, don’t change it
Circular_Dependency=सर्कुलर निर्भरता %%0
#XMSG Object is added to package successfully
Successfully_Add=जोड़ा गया
#XMSG
Circular_Dependency_Detail="{0}" में पैकेज "{1}" के साथ कोई सर्कुलर निर्भरता होने के कारण इसको सहेजा नहीं जा सकता.
#XFLD label of searching objects in space
Search_In_Sapce=यहां खोजें: {0}
#XFLD title of deleting package confirm dialog
Package_Deleting_Dialog_Title=हटाएं
#XMSG content message of deleting package confirm dialog
Package_Deleting_Dialog_Message=क्या आप चयनित पैकेज को हटाना चाहते हैं?
#XMSG
Package_Cannot_Delete=अन्य पैकेज के लिए चयनित पैकेज आवश्यक है और इसे हटाया नहीं जा सकता.
#XMSG
Package_Required_By_Singular=({0} पैकेज द्वारा आवश्यक)
#XMSG
Package_Required_By_Plural=({0} पैकेजों द्वारा आवश्यक)
#XFLD display name of owner column
owner=मालिक
#XFLD title of deleting package error dialog
Deleting_Error_Dialog_Title=त्रुटि
#XFLD title of deleting package alert dialog
Deleting_Alert_Dialog_Title=अलर्ट
#XBUT
Deleting_Alert_Dialog_Ok=ठीक
#XFLD
MyContent=मेरी सामग्री
#XFLD
NotExported=निर्यात नहीं किया गया
#XFLD
Exporting=निर्यात किया जा रहा है
#XFLD
Exported=निर्यात किया गया
#XFLD
DesignTimeError=समय त्रुटि डिज़ाइन करें
#XFLD
ExportFailed=निर्यात विफल
#XFLD
Cancelling=रद्द हो रहा है
#XFLD
ChangesToExport=निर्यात में परिवर्तन
#XMSG
Exporting_Package={0} को निर्यात करना. प्रक्रिया पूरी होने पर हम आपको सूचित करेंगे.
#XFLD: Object type is businessEntityVariant
TypeBusinessEntityVariant=व्यवसाय निकाय संस्करण
#XFLD: Object type is businessEntity
TypeBusinessEntity=व्यवसाय निकाय
#XFLD
Tooltip_Add=जोड़ें
#XFLD
Tooltip_Edit=संपादित करें
#XFLD
Tooltip_Delete=हटाएं
#XFLD
Tooltip_Refresh=रीफ़्रेश करें
#XMSG
ExportToOverwrite=यह पैकेज पहले ही संस्करण {0} के साथ सामग्री नेटवर्क पर निर्यात किया जा चुका है. क्या आप इसे अधिलेखित करना चाहते हैं?
# XFLD Column businessName
Ext_Selection_Col_Name=नाम
# XFLD Column Location
Ext_Selection_Col_Location=स्थान
# XFLD
Col_Name=नाम
# XFLD
Col_Description=वर्णन
# XFLD Label
MoveTo_Label=फ़ोल्डर में ले जाएं
#XMIT Add versions menu button text in Data Builder
versions=संस्करण
createVersion=संस्करण बनाएं
versionHistory=संस्करण इतिहास
#XMSG
Package_Depends_On_DP=यह पैकेज डेटा उत्पादों पर निर्भर करता है
#XMSG
Package_Depends_On_DP_Warning=इस पैकेज को आयात करने से पहले, सुनिश्चित करें कि लक्ष्य स्थान पर निम्न डेटा उत्पाद उपलब्ध हैं:\r\n\r\n{0}
# XFLD
Export_Confirm_Dailog_Title=पुष्टि करें
#XMSG
Package_Contains_DataObjects=इस पैकेज में परिवहन के लिए डेटा युक्त निम्नलिखित ऑब्जेक्ट शामिल हैं:\r\n\r\n{0}\r\n\r\nकृपया सुनिश्चित करें कि आपके पैकेज में इन वस्तुओं के परिवहन के परिणामस्वरूप कोई भी निजी या संवेदनशील डेटा अनुचित रूप से एक्सपोज नहीं होगा.
