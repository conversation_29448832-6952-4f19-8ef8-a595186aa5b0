# https://wiki.wdf.sap.corp/wiki/display/DWAAS/How+to+add+text+classifications+for+translatable+texts
#
# use the following text types for short texts (up to 120 characters)
# XACT Accessibility
# XBUT Button
# XCKL Checkbox
# XCOL Column header
# XFLD Label
# XGRP Group title
# XLNK Hyperlink
# XLOG Log entry
# XLST List box item
# XMIT Menu item
# XMSG Message
# XRBL Radio button
# XSEL Selection
# XTIT Table title
# XTOL Tooltip
#
# for longer texts (more than 120 characters) use
# YINS Instruction
# YMSG Message

#=============from skyline============
#XFLD general tab of package editor
Tab_General=כללי
#XFLD objects tab of package editor
Tab_Objects=אובייקטים
#XFLD required packages tab of package editor
Tab_RequiredPkgs=חבילות נדרשות
#XFLD label for business name input
BusinessName=שם עסקי
#XFLD label for technical name input
TechnicalName=שם טכני
#XFLD label for purpose input
BusinessPurpose=תכלית עסקית
#XTIT title of save dialog
Dilaog_Save=שמור
#XBUT save button of save dialog
Btn_Save=שמור
#XBUT cancel button of save dialog
Btn_Cancel=בטל
#XFLD title of objects section
Objects=אובייקטים
#XFLD title of required packages section
RequiredPackages=חבילות נדרשות
#XTIT title of package selection dialog
Dialog_SelectPackages=בחר חבילות
#XMSG no data text of package selection dialog
Dilaog_NoDataText=לא נמצאו חבילות מאגר
#XMSG message of adding objects
Msg_ObjectAdded={0} אובייקטים נוספו
#XMSG message of removing objects
Msg_ObjectRemoved={0} אובייקטים הוסרו
#XMSG message of adding required packages
Msg_RequiredPackageAdded={0} חבילות נדרשות נוספו
#XMSG message of removing required packages
Msg_RequiredPackageRemoved={0} חבילות נדרשות הוסרו
#XBUT add button in toolbar
Toolbar_Btn_Add=הוסף
#XBUT remove button in toolbar
Toolbar_Btn_Remove=הסר
#XCOL status column of objects and required packages table
Table_Col_Status=תוצאה
#XLNK more link of status details
Status_More_Link=הצג פרטים...
#XFLD folder name
FolderName=שם
#XFLD folder description
FolderDescription=תיאור
#XBUT ok button
Btn_OK=OK
#XFLD title of export location dialog
ExportLocationDialog=מיקום ייצוא
#XFLD lable of category field
Category=קטגוריה
#XFLD label of location field
Location=מיקום יעד
#XFLD lable of version field
SemanticVersion=גרסה
#XFLD label of current exported version
CurrentExportedPackage=גרסה נוכחית שבוצע לה ייצוא
#XFLD label of status field
ExportStatus=סטאטוס
#XFLD tooltip of export button
Tooltip_Export=יצא
#XFLD tooltip of save button
Tooltip_Save=שמור
#XFLD tooltip of validate button
Tooltip_Validate=בדוק תקינות
#XMSG
InvalidVersion=הזן גרסה בפורמט "x.y.z".
#XMSG
VersionLowerThanBefore=גרסה לא יכולה נמוכה יותר מהגרסה הקודמת.
#XMSG
Empty_Version=הזן גרסת חבילה.
#XMSG
Package_Missing_Technical_Name=הזן שם טכני עבור החבילה.
#=============from skyline============

#XFLD lable for space combo selection
Spaces=מרחב:
#XFLD section title of landing page
Packages=חבילות
#XCOL business name column
Table_Col_BusinessName=שם עסקי
#XCOL technical name column
Table_Col_TechnicalName=שם טכני
#XCOL sapce column
Table_Col_Space=מרחב
#XCOL create on column
Table_Col_CreatedOn=נוצר בתאריך
#XCOL entity type column
Table_Col_Type=סוג
#XCOL entity type column
Table_Col_Object_Status=סטאטוס
#XMSG message of deleting packages
Msg_PackageRemoved={0} חבילות הוסרו
#XMIT menu item of all spaces
Op_AllSpaces=כל המרחבים
#XFLD default business name of a new package
NewPackage_BusinessName=חבילה 1
#XFLD default technical name of a new package
NewPackage_TechnicalName=Package_1
#XFLD default name of a new package displayed in breadcrumb
Breadcrumb_NewPackage_Name=חבילה חדשה
#XFLD title of object selection dialog
ObjectsDialog_Title=הוסף אובייקטים
#XMSG dependencies are fully resolved
Dependency_Resolved=מוכן להוספה (כל יחסי התלות נפתרו ({0}))
#XMSG dependencies are partially resolved
Dependency_Partial_Resolved=מוכן להוספה (חלק מיחסי התלות נפתרו ({0}/{1}))
#XMSG dependencies are not found
Dependency_Not_Found=יחסי תלות לא נמצאו
#XMSG dependencies are in other package
Dependency_In_Other_Package=לא ניתן לבצע הוספה: כבר נמצא בחבילה "{0}"
#XMSG dependencies are in other space
Dependency_In_Other_Space=לא ניתן לבצע הוספה: במרחב {0}
#XMSG
Cannot_Add_Managed_Content=לא ניתן להוסיף: תוכן מנוהל
#XMSG
Cannot_Add_Due_To_Dependency_Has_Managed_Content=לא ניתן להוסיף: תוכן מנוהל של תלות
#XMSG dependencies are in current space
Dependency_In_Current_Package=כבר נמצא בחבילה
#XMSG dependencies are in required package
Dependency_In_Required_Package=כבר נמצא בחבילה נדרשת
#XMSG package arelady exists
Package_Duplicate_Name=חבילה ''{0}'' כבר קיימת במאגר. הזן שם אחר.
#XMSG package name is required
Package_Name_Missing=הזן שם חבילה.
#XMSG package version is required
Package_Version_Missing=הזן גרסת חבילה.
#XMSG package is drafted
Package_Draft_Warning=חבילת טיוטה. לחץ על "שמור" כדי לבדוק תקינות של יחסי תלות ולאשר את השינויים שלך.
#XFLD title of validation message dialog
Validation_MessageBox_Title=הודעות בדיקת תקינות
#XMSG content message of validation message dialog
Validation_MessageBox_Content="{0}" לא חוקי ואינו ניתן לייצוא. עליך לפתור את כל יחסי התלות של האובייקט במלואן על-ידי הוספת האובייקטים הנחוצים ידנית או באמצעות חבילה נדרשת.
#XBUT save anyway button
Save_Anyway=שמור בכל זאת
#XMSG package is valid
Valid_Package_Message=החבילה חוקית.
#XMSG
Object_In_Other_Pacakge=אובייקט "{0}" נמצא בחבילה "{1}".
#XMSG
Dependent_Object_In_Other_Pacakge=אובייקט "{0}" בחבילה "{1}", שלא צוין כתבילה נדרשת.
#XMSG
Dependent_Object_Missing=אובייקט "{0}" במרחב {1}.
#XMSG
Dependent_Missing_Pattern=אובייקט "{0}" תלוי ב:\r\n{1}
#XMSG: error message for saving the package
Save_Error_Message=שמירת החבילה נכשלה. {0}
#XFLD title of save busy dialog
Title_Save_Busy_Dialog=שומר
#XMSG: message of it takes long time to save
Long_Time_Save_Busy_Dialog=השמירה אורכת יותר זמן מהרגיל. יש להמתין.
#XMSG: erro message of missing dependency
Dependency_Missing=חסרה תלות.
#XMSG: erro message of missing dependency
Object_Dependency_Missing=חסרה תלות של "{0}".
#XMSG message of validating busy dialog
Validating_Package=מבצע בדיקת תקינות של חבילה
#XFLD title of validating busy dialog
Title_Validating_Busy_Dialog=בתהליך עיבוד
#XMSG %%0 is a placeholder, don’t change it
Missing_Dependency=יחסי תלות חסרים %%0
#XMSG %%0 is a placeholder, don’t change it
Circular_Dependency=תלות מעגלית %%0
#XMSG Object is added to package successfully
Successfully_Add=נוסף
#XMSG
Circular_Dependency_Detail="{0}" אינו ניתן לשמירה כיוון שיש לו תלות מעגלית עם חבילה "{1}".
#XFLD label of searching objects in space
Search_In_Sapce=חפש ב: {0}
#XFLD title of deleting package confirm dialog
Package_Deleting_Dialog_Title=מחק
#XMSG content message of deleting package confirm dialog
Package_Deleting_Dialog_Message=האם ברצונך למחוק את החבילות שנבחרו?
#XMSG
Package_Cannot_Delete=החבילה הנבחרת נדרשת על-ידי חבילות אחרות ולא ניתן למחוק אותה.
#XMSG
Package_Required_By_Singular=(נדרש על-ידי חבילה {0})
#XMSG
Package_Required_By_Plural=(נדרש על-ידי {0} חבילות)
#XFLD display name of owner column
owner=בעלים
#XFLD title of deleting package error dialog
Deleting_Error_Dialog_Title=שגיאה
#XFLD title of deleting package alert dialog
Deleting_Alert_Dialog_Title=התראה
#XBUT
Deleting_Alert_Dialog_Ok=OK
#XFLD
MyContent=התוכן שלי
#XFLD
NotExported=לא בוצע יצוא
#XFLD
Exporting=מייצא
#XFLD
Exported=יוצא
#XFLD
DesignTimeError=שגיאת זמן תיכון
#XFLD
ExportFailed=יצוא נכשל
#XFLD
Cancelling=מבצע ביטול
#XFLD
ChangesToExport=שינויים לייצוא
#XMSG
Exporting_Package=מתבצע יצוא של {0}. נודיע לך כשהתהליך יושלם.
#XFLD: Object type is businessEntityVariant
TypeBusinessEntityVariant=גרסת ישות עסקית
#XFLD: Object type is businessEntity
TypeBusinessEntity=ישות עסקית
#XFLD
Tooltip_Add=הוסף
#XFLD
Tooltip_Edit=ערוך
#XFLD
Tooltip_Delete=מחק
#XFLD
Tooltip_Refresh=רענן
#XMSG
ExportToOverwrite=חבילה זו כבר יוצאה לרשת תוכן עם הגרסה {0}. האם ברצונך לשכתב אותה?
# XFLD Column businessName
Ext_Selection_Col_Name=שם
# XFLD Column Location
Ext_Selection_Col_Location=מיקום
# XFLD
Col_Name=שם
# XFLD
Col_Description=תיאור
# XFLD Label
MoveTo_Label=העבר לתיקייה
#XMIT Add versions menu button text in Data Builder
versions=גרסאות
createVersion=צור גרסה
versionHistory=היסטוריית גרסאות
#XMSG
Package_Depends_On_DP=חבילה זו תלויה במוצרי נתונים
#XMSG
Package_Depends_On_DP_Warning=לפני יבוא חבילה זו, ודא שמוצרי הנתונים הבאים זמינים למרחב היעד:\r\n\r\n{0}
# XFLD
Export_Confirm_Dailog_Title=אישור
#XMSG
Package_Contains_DataObjects=חבילה זו כוללת את האובייקטים הבאים המכילים נתונים לטרנספורט:\r\n\r\n{0} \r\n\r\nודא שלא ייחשפו כראוי נתונים אישיים או רגישים כתוצאה מהעברת אובייקטים אלה בחבילה שלך.
