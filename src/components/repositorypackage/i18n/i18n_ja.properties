# https://wiki.wdf.sap.corp/wiki/display/DWAAS/How+to+add+text+classifications+for+translatable+texts
#
# use the following text types for short texts (up to 120 characters)
# XACT Accessibility
# XBUT Button
# XCKL Checkbox
# XCOL Column header
# XFLD Label
# XGRP Group title
# XLNK Hyperlink
# XLOG Log entry
# XLST List box item
# XMIT Menu item
# XMSG Message
# XRBL Radio button
# XSEL Selection
# XTIT Table title
# XTOL Tooltip
#
# for longer texts (more than 120 characters) use
# YINS Instruction
# YMSG Message

#=============from skyline============
#XFLD general tab of package editor
Tab_General=一般
#XFLD objects tab of package editor
Tab_Objects=オブジェクト
#XFLD required packages tab of package editor
Tab_RequiredPkgs=必要なパッケージ
#XFLD label for business name input
BusinessName=ビジネス名
#XFLD label for technical name input
TechnicalName=技術名
#XFLD label for purpose input
BusinessPurpose=ビジネス目的
#XTIT title of save dialog
Dilaog_Save=保存
#XBUT save button of save dialog
Btn_Save=保存
#XBUT cancel button of save dialog
Btn_Cancel=キャンセル
#XFLD title of objects section
Objects=オブジェクト
#XFLD title of required packages section
RequiredPackages=必要なパッケージ
#XTIT title of package selection dialog
Dialog_SelectPackages=パッケージの選択
#XMSG no data text of package selection dialog
Dilaog_NoDataText=リポジトリパッケージが見つかりません
#XMSG message of adding objects
Msg_ObjectAdded={0} 個のオブジェクトが追加されました
#XMSG message of removing objects
Msg_ObjectRemoved={0} 個のオブジェクトが削除されました
#XMSG message of adding required packages
Msg_RequiredPackageAdded={0} 個の必要なパッケージが追加されました
#XMSG message of removing required packages
Msg_RequiredPackageRemoved={0} 個の必要なパッケージが削除されました
#XBUT add button in toolbar
Toolbar_Btn_Add=追加
#XBUT remove button in toolbar
Toolbar_Btn_Remove=削除
#XCOL status column of objects and required packages table
Table_Col_Status=結果
#XLNK more link of status details
Status_More_Link=詳細を表示...
#XFLD folder name
FolderName=名前
#XFLD folder description
FolderDescription=説明
#XBUT ok button
Btn_OK=OK
#XFLD title of export location dialog
ExportLocationDialog=エクスポートロケーション
#XFLD lable of category field
Category=カテゴリ
#XFLD label of location field
Location=ターゲットロケーション
#XFLD lable of version field
SemanticVersion=バージョン
#XFLD label of current exported version
CurrentExportedPackage=現在のエクスポート済みバージョン
#XFLD label of status field
ExportStatus=ステータス
#XFLD tooltip of export button
Tooltip_Export=エクスポート
#XFLD tooltip of save button
Tooltip_Save=保存
#XFLD tooltip of validate button
Tooltip_Validate=チェック
#XMSG
InvalidVersion=バージョンを "x.y.z" という書式で入力してください。
#XMSG
VersionLowerThanBefore=バージョンを前バージョンよりも下にすることはできません。
#XMSG
Empty_Version=パッケージバージョンを入力してください。
#XMSG
Package_Missing_Technical_Name=パッケージの技術名を入力してください。
#=============from skyline============

#XFLD lable for space combo selection
Spaces=スペース:
#XFLD section title of landing page
Packages=パッケージ
#XCOL business name column
Table_Col_BusinessName=ビジネス名
#XCOL technical name column
Table_Col_TechnicalName=技術名
#XCOL sapce column
Table_Col_Space=スペース
#XCOL create on column
Table_Col_CreatedOn=作成日付
#XCOL entity type column
Table_Col_Type=タイプ
#XCOL entity type column
Table_Col_Object_Status=ステータス
#XMSG message of deleting packages
Msg_PackageRemoved={0} 個のパッケージが削除されました
#XMIT menu item of all spaces
Op_AllSpaces=すべてのスペース
#XFLD default business name of a new package
NewPackage_BusinessName=パッケージ 1
#XFLD default technical name of a new package
NewPackage_TechnicalName=Package_1
#XFLD default name of a new package displayed in breadcrumb
Breadcrumb_NewPackage_Name=新規パッケージ
#XFLD title of object selection dialog
ObjectsDialog_Title=オブジェクトの追加
#XMSG dependencies are fully resolved
Dependency_Resolved=追加可能 (すべての依存関係が解決されました ({0}))
#XMSG dependencies are partially resolved
Dependency_Partial_Resolved=追加可能 (一部の依存関係が解決されていません ({0}/{1}))
#XMSG dependencies are not found
Dependency_Not_Found=依存関係が見つかりません
#XMSG dependencies are in other package
Dependency_In_Other_Package=追加できません: パッケージ "{0}" にすでに存在します
#XMSG dependencies are in other space
Dependency_In_Other_Space=追加できません: スペース {0} に存在します
#XMSG
Cannot_Add_Managed_Content=追加できません: 管理対象コンテンツです
#XMSG
Cannot_Add_Due_To_Dependency_Has_Managed_Content=追加できません: 依存関係に管理対象コンテンツがあります
#XMSG dependencies are in current space
Dependency_In_Current_Package=パッケージにすでに存在します
#XMSG dependencies are in required package
Dependency_In_Required_Package=必要なパッケージにすでに存在します
#XMSG package arelady exists
Package_Duplicate_Name=パッケージ "{0}" はすでにリポジトリに存在します。別の名前を入力してください。
#XMSG package name is required
Package_Name_Missing=パッケージ名を入力してください。
#XMSG package version is required
Package_Version_Missing=パッケージバージョンを入力してください。
#XMSG package is drafted
Package_Draft_Warning=ドラフトパッケージです。"保存" をクリックして依存関係を検証し、変更を確認してください。
#XFLD title of validation message dialog
Validation_MessageBox_Title=チェックメッセージ
#XMSG content message of validation message dialog
Validation_MessageBox_Content="{0}" は無効であり、エクスポートできません。必要なオブジェクトをマニュアルで追加するか、必要なパッケージを使用することで、すべてのオブジェクト依存関係を完全に解決する必要があります。
#XBUT save anyway button
Save_Anyway=保存
#XMSG package is valid
Valid_Package_Message=パッケージは有効です。
#XMSG
Object_In_Other_Pacakge=オブジェクト "{0}" はパッケージ "{1}" に存在します。
#XMSG
Dependent_Object_In_Other_Pacakge=オブジェクト "{0}" はパッケージ "{1}" に存在します。このパッケージは必要なパッケージとして指定されていません。
#XMSG
Dependent_Object_Missing=オブジェクト "{0}" はスペース "{1}" に存在します。
#XMSG
Dependent_Missing_Pattern=オブジェクト "{0}" は以下に依存しています。\r\n{1}
#XMSG: error message for saving the package
Save_Error_Message=パッケージの保存に失敗しました。{0}
#XFLD title of save busy dialog
Title_Save_Busy_Dialog=保存しています
#XMSG: message of it takes long time to save
Long_Time_Save_Busy_Dialog=保存に通常よりも時間がかかっています。お待ちください。
#XMSG: erro message of missing dependency
Dependency_Missing=依存関係がありません。
#XMSG: erro message of missing dependency
Object_Dependency_Missing="{0}" の依存関係がありません。
#XMSG message of validating busy dialog
Validating_Package=パッケージをチェックしています
#XFLD title of validating busy dialog
Title_Validating_Busy_Dialog=処理しています
#XMSG %%0 is a placeholder, don’t change it
Missing_Dependency=依存関係 %%0 がありません
#XMSG %%0 is a placeholder, don’t change it
Circular_Dependency=循環依存関係 %%0
#XMSG Object is added to package successfully
Successfully_Add=追加されました
#XMSG
Circular_Dependency_Detail="{0}" はパッケージ "{1}" との循環依存関係があるため、保存できません。
#XFLD label of searching objects in space
Search_In_Sapce=検索範囲: {0}
#XFLD title of deleting package confirm dialog
Package_Deleting_Dialog_Title=削除
#XMSG content message of deleting package confirm dialog
Package_Deleting_Dialog_Message=選択したパッケージを削除しますか?
#XMSG
Package_Cannot_Delete=選択したパッケージは他のオブジェクトで必要であり、削除できません。
#XMSG
Package_Required_By_Singular=({0} パッケージで必要)
#XMSG
Package_Required_By_Plural=({0} パッケージで必要)
#XFLD display name of owner column
owner=所有者
#XFLD title of deleting package error dialog
Deleting_Error_Dialog_Title=エラー
#XFLD title of deleting package alert dialog
Deleting_Alert_Dialog_Title=アラート
#XBUT
Deleting_Alert_Dialog_Ok=OK
#XFLD
MyContent=マイコンテンツ
#XFLD
NotExported=エクスポート未実行
#XFLD
Exporting=エクスポートしています
#XFLD
Exported=エクスポートされました
#XFLD
DesignTimeError=設計時エラー
#XFLD
ExportFailed=エクスポートが失敗しました
#XFLD
Cancelling=キャンセルしています
#XFLD
ChangesToExport=エクスポート対象の変更
#XMSG
Exporting_Package={0} をエクスポートしています。処理が完了したら通知します。
#XFLD: Object type is businessEntityVariant
TypeBusinessEntityVariant=ビジネスエンティティバージョン
#XFLD: Object type is businessEntity
TypeBusinessEntity=ビジネスエンティティ
#XFLD
Tooltip_Add=追加
#XFLD
Tooltip_Edit=編集
#XFLD
Tooltip_Delete=削除
#XFLD
Tooltip_Refresh=リフレッシュ
#XMSG
ExportToOverwrite=このパッケージはバージョン {0} のコンテンツネットワークにすでにエクスポートされています。上書きしますか?
# XFLD Column businessName
Ext_Selection_Col_Name=名前
# XFLD Column Location
Ext_Selection_Col_Location=ロケーション
# XFLD
Col_Name=名前
# XFLD
Col_Description=説明
# XFLD Label
MoveTo_Label=フォルダに移動
#XMIT Add versions menu button text in Data Builder
versions=バージョン
createVersion=バージョンを作成
versionHistory=バージョン履歴
#XMSG
Package_Depends_On_DP=このパッケージはデータプロダクトに依存します
#XMSG
Package_Depends_On_DP_Warning=このパッケージをインポートする前に、以下のデータプロダクトがターゲットスペースで利用可能であることを確認してください。\r\n\r\n{0}
# XFLD
Export_Confirm_Dailog_Title=確認
#XMSG
Package_Contains_DataObjects=このパッケージには、移送対象データを含む以下のオブジェクトが含まれています。\r\n\r\n{0} \r\n\r\nパッケージでこれらのオブジェクトを移送した結果、個人データや機密データが不適切に公開されないようにしてください。
