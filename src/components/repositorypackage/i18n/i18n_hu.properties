# https://wiki.wdf.sap.corp/wiki/display/DWAAS/How+to+add+text+classifications+for+translatable+texts
#
# use the following text types for short texts (up to 120 characters)
# XACT Accessibility
# XBUT Button
# XCKL Checkbox
# XCOL Column header
# XFLD Label
# XGRP Group title
# XLNK Hyperlink
# XLOG Log entry
# XLST List box item
# XMIT Menu item
# XMSG Message
# XRBL Radio button
# XSEL Selection
# XTIT Table title
# XTOL Tooltip
#
# for longer texts (more than 120 characters) use
# YINS Instruction
# YMSG Message

#=============from skyline============
#XFLD general tab of package editor
Tab_General=Általános
#XFLD objects tab of package editor
Tab_Objects=Objektumok
#XFLD required packages tab of package editor
Tab_RequiredPkgs=Kötelező csomagok
#XFLD label for business name input
BusinessName=Üzleti név
#XFLD label for technical name input
TechnicalName=Technikai név
#XFLD label for purpose input
BusinessPurpose=Üzleti cél
#XTIT title of save dialog
Dilaog_Save=Mentés
#XBUT save button of save dialog
Btn_Save=Mentés
#XBUT cancel button of save dialog
Btn_Cancel=Mégse
#XFLD title of objects section
Objects=Objektumok
#XFLD title of required packages section
RequiredPackages=Kötelező csomagok
#XTIT title of package selection dialog
Dialog_SelectPackages=Csomagok kiválasztása
#XMSG no data text of package selection dialog
Dilaog_NoDataText=Nem találhatók tárházcsomagok
#XMSG message of adding objects
Msg_ObjectAdded={0} objektum hozzáadva
#XMSG message of removing objects
Msg_ObjectRemoved={0} objektum eltávolítva
#XMSG message of adding required packages
Msg_RequiredPackageAdded={0} kötelező csomag hozzáadva
#XMSG message of removing required packages
Msg_RequiredPackageRemoved={0} kötelező csomag eltávolítva
#XBUT add button in toolbar
Toolbar_Btn_Add=Hozzáadás
#XBUT remove button in toolbar
Toolbar_Btn_Remove=Eltávolítás
#XCOL status column of objects and required packages table
Table_Col_Status=Eredmény
#XLNK more link of status details
Status_More_Link=Részletek megjelenítése...
#XFLD folder name
FolderName=Név
#XFLD folder description
FolderDescription=Leírás
#XBUT ok button
Btn_OK=OK
#XFLD title of export location dialog
ExportLocationDialog=Exportálás helye
#XFLD lable of category field
Category=Kategória
#XFLD label of location field
Location=Célhely
#XFLD lable of version field
SemanticVersion=Verzió
#XFLD label of current exported version
CurrentExportedPackage=Jelenleg exportált verzió
#XFLD label of status field
ExportStatus=Állapot
#XFLD tooltip of export button
Tooltip_Export=Exportálás
#XFLD tooltip of save button
Tooltip_Save=Mentés
#XFLD tooltip of validate button
Tooltip_Validate=Validálás
#XMSG
InvalidVersion=A verziószámot a következő formában adja meg: "x.y.z".
#XMSG
VersionLowerThanBefore=A verziószám nem lehet kisebb az előzőnél.
#XMSG
Empty_Version=Adja meg a csomag verzióját.
#XMSG
Package_Missing_Technical_Name=Adjon technikai nevet a csomagnak.
#=============from skyline============

#XFLD lable for space combo selection
Spaces=Tér:
#XFLD section title of landing page
Packages=Csomagok
#XCOL business name column
Table_Col_BusinessName=Üzleti név
#XCOL technical name column
Table_Col_TechnicalName=Technikai név
#XCOL sapce column
Table_Col_Space=Tér
#XCOL create on column
Table_Col_CreatedOn=Létrehozás dátuma
#XCOL entity type column
Table_Col_Type=Típus
#XCOL entity type column
Table_Col_Object_Status=Állapot
#XMSG message of deleting packages
Msg_PackageRemoved={0} csomag eltávolítva
#XMIT menu item of all spaces
Op_AllSpaces=Minden tér
#XFLD default business name of a new package
NewPackage_BusinessName=1. csomag
#XFLD default technical name of a new package
NewPackage_TechnicalName=Csomag_1
#XFLD default name of a new package displayed in breadcrumb
Breadcrumb_NewPackage_Name=Új csomag
#XFLD title of object selection dialog
ObjectsDialog_Title=Objektumok hozzáadása
#XMSG dependencies are fully resolved
Dependency_Resolved=Hozzáadásra kész (minden függőség megszüntetve ({0}))
#XMSG dependencies are partially resolved
Dependency_Partial_Resolved=Hozzáadásra kész (egyes függőségek megszüntetve ({0}/{1}))
#XMSG dependencies are not found
Dependency_Not_Found=Nem találhatók függőségek
#XMSG dependencies are in other package
Dependency_In_Other_Package=Nem lehet hozzáadni. Már a(z) {0} csomagban van
#XMSG dependencies are in other space
Dependency_In_Other_Space=Nem lehet hozzáadni: a(z) {0} térben van
#XMSG
Cannot_Add_Managed_Content=Nem lehet hozzáadni: kezelt tartalom
#XMSG
Cannot_Add_Due_To_Dependency_Has_Managed_Content=Nem lehet hozzáadni: a függőség kezelt tartalommal rendelkezik
#XMSG dependencies are in current space
Dependency_In_Current_Package=Már a csomagban van
#XMSG dependencies are in required package
Dependency_In_Required_Package=Már a kötelező csomagban van
#XMSG package arelady exists
Package_Duplicate_Name=Már van {0} nevű csomag a tárházban. Adjon meg más nevet.
#XMSG package name is required
Package_Name_Missing=Adja meg a csomag nevét.
#XMSG package version is required
Package_Version_Missing=Adja meg a csomag verzióját.
#XMSG package is drafted
Package_Draft_Warning=Csomagpiszkozat. Kattintson a Mentés gombra a függőségek ellenőrzéséhez és a módosítások megerősítéséhez.
#XFLD title of validation message dialog
Validation_MessageBox_Title=Validálási üzenetek
#XMSG content message of validation message dialog
Validation_MessageBox_Content=A(z) {0} érvénytelen, és nem lehet exportálni. Teljesen meg kell szüntetnie az összes objektumfüggőséget a szükséges objektumokat manuálisan vagy egy kötelező csomag által hozzáadva.
#XBUT save anyway button
Save_Anyway=Mentés mégis
#XMSG package is valid
Valid_Package_Message=A csomag érvényes.
#XMSG
Object_In_Other_Pacakge=A(z) {0} objektum a(z) {1} csomagban van.
#XMSG
Dependent_Object_In_Other_Pacakge=A(z) {0} objektum a(z) {1} csomagban van, ami nincs kötelező csomagként megadva.
#XMSG
Dependent_Object_Missing=A(z) {0} objektum a(z) {1} térben van.
#XMSG
Dependent_Missing_Pattern=A(z) {0} objektum a következőtől függ:\r\n{1}
#XMSG: error message for saving the package
Save_Error_Message=Nem sikerült menteni a csomagot. {0}
#XFLD title of save busy dialog
Title_Save_Busy_Dialog=Mentés
#XMSG: message of it takes long time to save
Long_Time_Save_Busy_Dialog=A mentés a megszokottnál tovább tart. Kis türelmet.
#XMSG: erro message of missing dependency
Dependency_Missing=Hiányzik a függőség.
#XMSG: erro message of missing dependency
Object_Dependency_Missing={0} függősége hiányzik.
#XMSG message of validating busy dialog
Validating_Package=Csomag érvényesítése
#XFLD title of validating busy dialog
Title_Validating_Busy_Dialog=Feldolgozás alatt
#XMSG %%0 is a placeholder, don’t change it
Missing_Dependency=Hiányzó függőségek %%0
#XMSG %%0 is a placeholder, don’t change it
Circular_Dependency=Körkörös függőség %%0
#XMSG Object is added to package successfully
Successfully_Add=Hozzáadva
#XMSG
Circular_Dependency_Detail=A(z) {0} nem menthető, mert körkörös függősége van a(z) {1} csomaggal.
#XFLD label of searching objects in space
Search_In_Sapce=Keresés helye: {0}
#XFLD title of deleting package confirm dialog
Package_Deleting_Dialog_Title=Törlés
#XMSG content message of deleting package confirm dialog
Package_Deleting_Dialog_Message=Törli a kijelölt csomagokat?
#XMSG
Package_Cannot_Delete=A kijelölt csomag követelménye más csomagoknak, ezért nem törölhető.
#XMSG
Package_Required_By_Singular=(követelménye {0} csomagnak)
#XMSG
Package_Required_By_Plural=(követelménye {0} csomagnak)
#XFLD display name of owner column
owner=Tulajdonos
#XFLD title of deleting package error dialog
Deleting_Error_Dialog_Title=Hiba
#XFLD title of deleting package alert dialog
Deleting_Alert_Dialog_Title=Riasztás
#XBUT
Deleting_Alert_Dialog_Ok=OK
#XFLD
MyContent=Saját tartalom
#XFLD
NotExported=Nincs exportálva
#XFLD
Exporting=Exportálás
#XFLD
Exported=Exportálva
#XFLD
DesignTimeError=Tervezési idejű hiba
#XFLD
ExportFailed=Az exportálás sikertelen
#XFLD
Cancelling=Megszakítás
#XFLD
ChangesToExport=Exportálandó módosítások
#XMSG
Exporting_Package={0} exportálása folyamatban. Értesítjük, amikor befejeződik a folyamat.
#XFLD: Object type is businessEntityVariant
TypeBusinessEntityVariant=Üzletientitás-verzió
#XFLD: Object type is businessEntity
TypeBusinessEntity=Üzleti entitás
#XFLD
Tooltip_Add=Hozzáadás
#XFLD
Tooltip_Edit=Szerkesztés
#XFLD
Tooltip_Delete=Törlés
#XFLD
Tooltip_Refresh=Frissítés
#XMSG
ExportToOverwrite=Ezt a csomagot már exportálták a Tartalomhálózatra a következő verzióban: {0}. Felülírja?
# XFLD Column businessName
Ext_Selection_Col_Name=Név
# XFLD Column Location
Ext_Selection_Col_Location=Hely
# XFLD
Col_Name=Név
# XFLD
Col_Description=Leírás
# XFLD Label
MoveTo_Label=Áthelyezés másik mappába
#XMIT Add versions menu button text in Data Builder
versions=Verziók
createVersion=Verzió létrehozása
versionHistory=Verziótörténet
#XMSG
Package_Depends_On_DP=Ez a csomag adatfeldolgozási termékektől függ
#XMSG
Package_Depends_On_DP_Warning=A csomag importálása előtt gondoskodjon róla, hogy a következő adatfeldolgozási termékek elérhetők legyenek a céltér számára:\r\n\r\n{0}
# XFLD
Export_Confirm_Dailog_Title=Megerősítés
#XMSG
Package_Contains_DataObjects=Ez a csomag a következő transzportálálandó adatokat tartalmazó objektumokat foglalja magában:\r\n\r\n{0} \r\n\r\nGondoskodjon róla, hogy ezen objektumok csomagba transzportálása következtében ne váljanak szabályellenesen elérhetővé személyes vagy bizalmas adatok.
