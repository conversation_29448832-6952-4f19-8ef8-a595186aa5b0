# https://wiki.wdf.sap.corp/wiki/display/DWAAS/How+to+add+text+classifications+for+translatable+texts
#
# use the following text types for short texts (up to 120 characters)
# XACT Accessibility
# XBUT Button
# XCKL Checkbox
# XCOL Column header
# XFLD Label
# XGRP Group title
# XLNK Hyperlink
# XLOG Log entry
# XLST List box item
# XMIT Menu item
# XMSG Message
# XRBL Radio button
# XSEL Selection
# XTIT Table title
# XTOL Tooltip
#
# for longer texts (more than 120 characters) use
# YINS Instruction
# YMSG Message

#=============from skyline============
#XFLD general tab of package editor
Tab_General=常规
#XFLD objects tab of package editor
Tab_Objects=对象
#XFLD required packages tab of package editor
Tab_RequiredPkgs=必需的包
#XFLD label for business name input
BusinessName=业务名称
#XFLD label for technical name input
TechnicalName=技术名称
#XFLD label for purpose input
BusinessPurpose=业务用途
#XTIT title of save dialog
Dilaog_Save=保存
#XBUT save button of save dialog
Btn_Save=保存
#XBUT cancel button of save dialog
Btn_Cancel=取消
#XFLD title of objects section
Objects=对象
#XFLD title of required packages section
RequiredPackages=必需的包
#XTIT title of package selection dialog
Dialog_SelectPackages=选择包
#XMSG no data text of package selection dialog
Dilaog_NoDataText=没有找到资源库包
#XMSG message of adding objects
Msg_ObjectAdded=已添加 {0} 个对象
#XMSG message of removing objects
Msg_ObjectRemoved=已移除 {0} 个对象
#XMSG message of adding required packages
Msg_RequiredPackageAdded=已添加 {0} 个必需的包
#XMSG message of removing required packages
Msg_RequiredPackageRemoved=已移除 {0} 个必需的包
#XBUT add button in toolbar
Toolbar_Btn_Add=添加
#XBUT remove button in toolbar
Toolbar_Btn_Remove=移除
#XCOL status column of objects and required packages table
Table_Col_Status=结果
#XLNK more link of status details
Status_More_Link=显示详细信息...
#XFLD folder name
FolderName=名称
#XFLD folder description
FolderDescription=说明
#XBUT ok button
Btn_OK=确定
#XFLD title of export location dialog
ExportLocationDialog=导出位置
#XFLD lable of category field
Category=类别
#XFLD label of location field
Location=目标位置
#XFLD lable of version field
SemanticVersion=版本
#XFLD label of current exported version
CurrentExportedPackage=当前导出的版本
#XFLD label of status field
ExportStatus=状态
#XFLD tooltip of export button
Tooltip_Export=导出
#XFLD tooltip of save button
Tooltip_Save=保存
#XFLD tooltip of validate button
Tooltip_Validate=验证
#XMSG
InvalidVersion=请以 "x.y.z" 格式输入版本。
#XMSG
VersionLowerThanBefore=版本不能低于以前的版本。
#XMSG
Empty_Version=请输入包版本。
#XMSG
Package_Missing_Technical_Name=请输入包的技术名称。
#=============from skyline============

#XFLD lable for space combo selection
Spaces=空间：
#XFLD section title of landing page
Packages=包
#XCOL business name column
Table_Col_BusinessName=业务名称
#XCOL technical name column
Table_Col_TechnicalName=技术名称
#XCOL sapce column
Table_Col_Space=空间
#XCOL create on column
Table_Col_CreatedOn=创建日期
#XCOL entity type column
Table_Col_Type=类型
#XCOL entity type column
Table_Col_Object_Status=状态
#XMSG message of deleting packages
Msg_PackageRemoved=已移除 {0} 个包
#XMIT menu item of all spaces
Op_AllSpaces=全部空间
#XFLD default business name of a new package
NewPackage_BusinessName=包 1
#XFLD default technical name of a new package
NewPackage_TechnicalName=Package_1
#XFLD default name of a new package displayed in breadcrumb
Breadcrumb_NewPackage_Name=新建包
#XFLD title of object selection dialog
ObjectsDialog_Title=添加对象
#XMSG dependencies are fully resolved
Dependency_Resolved=准备添加（所有依赖项均已解决 ({0})）
#XMSG dependencies are partially resolved
Dependency_Partial_Resolved=准备添加（部分依赖项未解决 ({0}/{1})）
#XMSG dependencies are not found
Dependency_Not_Found=找不到依赖项
#XMSG dependencies are in other package
Dependency_In_Other_Package=没能添加：已在包 "{0}" 中
#XMSG dependencies are in other space
Dependency_In_Other_Space=没能添加：在空间 {0} 中
#XMSG
Cannot_Add_Managed_Content=没能添加：托管内容
#XMSG
Cannot_Add_Due_To_Dependency_Has_Managed_Content=没能添加：依赖项包含托管内容
#XMSG dependencies are in current space
Dependency_In_Current_Package=已在包中
#XMSG dependencies are in required package
Dependency_In_Required_Package=已在必需的包中
#XMSG package arelady exists
Package_Duplicate_Name=资源库中已经存在包 "{0}"。请输入其他名称。
#XMSG package name is required
Package_Name_Missing=请输入包名称。
#XMSG package version is required
Package_Version_Missing=请输入包版本。
#XMSG package is drafted
Package_Draft_Warning=草稿包。点击 "保存"，验证依赖项并确认更改。
#XFLD title of validation message dialog
Validation_MessageBox_Title=验证消息
#XMSG content message of validation message dialog
Validation_MessageBox_Content="{0}" 无效，无法导出。请手动或通过必需的包添加必要对象，完全解决所有对象依赖项。
#XBUT save anyway button
Save_Anyway=仍然保存
#XMSG package is valid
Valid_Package_Message=包有效。
#XMSG
Object_In_Other_Pacakge=对象 "{0}" 存在于包 "{1}" 中。
#XMSG
Dependent_Object_In_Other_Pacakge=对象 "{0}" 存在于包 "{1}" 中，这个包未指定为必需的包。
#XMSG
Dependent_Object_Missing=对象 "{0}" 存在于空间 {1} 中。
#XMSG
Dependent_Missing_Pattern=对象 "{0}" 依赖于：\r\n{1}
#XMSG: error message for saving the package
Save_Error_Message=没能保存包。{0}
#XFLD title of save busy dialog
Title_Save_Busy_Dialog=正在保存
#XMSG: message of it takes long time to save
Long_Time_Save_Busy_Dialog=保存所用的时间比平时长。请稍候。
#XMSG: erro message of missing dependency
Dependency_Missing=缺少依赖项。
#XMSG: erro message of missing dependency
Object_Dependency_Missing=缺少 "{0}" 的依赖项。
#XMSG message of validating busy dialog
Validating_Package=正在验证包
#XFLD title of validating busy dialog
Title_Validating_Busy_Dialog=处理中
#XMSG %%0 is a placeholder, don’t change it
Missing_Dependency=缺少依赖项 %%0
#XMSG %%0 is a placeholder, don’t change it
Circular_Dependency=循环依赖项 %%0
#XMSG Object is added to package successfully
Successfully_Add=已添加
#XMSG
Circular_Dependency_Detail=由于存在包 "{1}" 的循环依赖项，没能保存 "{0}"。
#XFLD label of searching objects in space
Search_In_Sapce=搜索范围：{0}
#XFLD title of deleting package confirm dialog
Package_Deleting_Dialog_Title=删除
#XMSG content message of deleting package confirm dialog
Package_Deleting_Dialog_Message=是否要删除选定包？
#XMSG
Package_Cannot_Delete=其他对象需要选定包，没能删除。
#XMSG
Package_Required_By_Singular=（{0} 个包需要）
#XMSG
Package_Required_By_Plural=（{0} 个包需要）
#XFLD display name of owner column
owner=所有者
#XFLD title of deleting package error dialog
Deleting_Error_Dialog_Title=错误
#XFLD title of deleting package alert dialog
Deleting_Alert_Dialog_Title=警报
#XBUT
Deleting_Alert_Dialog_Ok=确定
#XFLD
MyContent=我的内容
#XFLD
NotExported=未导出
#XFLD
Exporting=正在导出
#XFLD
Exported=已导出
#XFLD
DesignTimeError=设计时错误
#XFLD
ExportFailed=导出失败
#XFLD
Cancelling=正在取消
#XFLD
ChangesToExport=待导出更改
#XMSG
Exporting_Package=正在导出 {0}。处理过程完成后，我们将会通知你。
#XFLD: Object type is businessEntityVariant
TypeBusinessEntityVariant=业务实体版本
#XFLD: Object type is businessEntity
TypeBusinessEntity=业务实体
#XFLD
Tooltip_Add=添加
#XFLD
Tooltip_Edit=编辑
#XFLD
Tooltip_Delete=删除
#XFLD
Tooltip_Refresh=刷新
#XMSG
ExportToOverwrite=该包已导出到内容网络，版本为 {0}。是否要覆盖？
# XFLD Column businessName
Ext_Selection_Col_Name=名称
# XFLD Column Location
Ext_Selection_Col_Location=位置
# XFLD
Col_Name=名称
# XFLD
Col_Description=说明
# XFLD Label
MoveTo_Label=移动到文件夹
#XMIT Add versions menu button text in Data Builder
versions=版本
createVersion=创建版本
versionHistory=版本历史记录
#XMSG
Package_Depends_On_DP=这个包依赖于数据产品
#XMSG
Package_Depends_On_DP_Warning=导入这个包之前，请确保以下数据产品在目标空间中可用： \r\n\r\n{0} 
# XFLD
Export_Confirm_Dailog_Title=确认
#XMSG
Package_Contains_DataObjects=这个包包含以下对象，其中包含要传输的数据：\r\n\r\n{0} \r\n\r\n请确保在传输包中的这些对象时，不会造成个人或敏感数据的不当泄露。
