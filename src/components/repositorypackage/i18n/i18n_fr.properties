# https://wiki.wdf.sap.corp/wiki/display/DWAAS/How+to+add+text+classifications+for+translatable+texts
#
# use the following text types for short texts (up to 120 characters)
# XACT Accessibility
# XBUT Button
# XCKL Checkbox
# XCOL Column header
# XFLD Label
# XGRP Group title
# XLNK Hyperlink
# XLOG Log entry
# XLST List box item
# XMIT Menu item
# XMSG Message
# XRBL Radio button
# XSEL Selection
# XTIT Table title
# XTOL Tooltip
#
# for longer texts (more than 120 characters) use
# YINS Instruction
# YMSG Message

#=============from skyline============
#XFLD general tab of package editor
Tab_General=Général
#XFLD objects tab of package editor
Tab_Objects=Objets
#XFLD required packages tab of package editor
Tab_RequiredPkgs=Packages requis
#XFLD label for business name input
BusinessName=Appellation
#XFLD label for technical name input
TechnicalName=Nom technique
#XFLD label for purpose input
BusinessPurpose=Objectif commercial
#XTIT title of save dialog
Dilaog_Save=Sauvegarder
#XBUT save button of save dialog
Btn_Save=Sauvegarder
#XBUT cancel button of save dialog
Btn_Cancel=Annuler
#XFLD title of objects section
Objects=Objets
#XFLD title of required packages section
RequiredPackages=Packages requis
#XTIT title of package selection dialog
Dialog_SelectPackages=Sélectionner des packages
#XMSG no data text of package selection dialog
Dilaog_NoDataText=Aucun package de référentiel trouvé
#XMSG message of adding objects
Msg_ObjectAdded={0} objets ajoutés
#XMSG message of removing objects
Msg_ObjectRemoved={0} objets supprimés
#XMSG message of adding required packages
Msg_RequiredPackageAdded={0} packages requis ajoutés
#XMSG message of removing required packages
Msg_RequiredPackageRemoved={0} packages requis supprimés
#XBUT add button in toolbar
Toolbar_Btn_Add=Ajouter
#XBUT remove button in toolbar
Toolbar_Btn_Remove=Retirer
#XCOL status column of objects and required packages table
Table_Col_Status=Résultat
#XLNK more link of status details
Status_More_Link=Afficher les détails...
#XFLD folder name
FolderName=Nom
#XFLD folder description
FolderDescription=Description
#XBUT ok button
Btn_OK=OK
#XFLD title of export location dialog
ExportLocationDialog=Exporter l'emplacement
#XFLD lable of category field
Category=Catégorie
#XFLD label of location field
Location=Emplacement cible
#XFLD lable of version field
SemanticVersion=Version
#XFLD label of current exported version
CurrentExportedPackage=Version exportée actuelle
#XFLD label of status field
ExportStatus=Statut
#XFLD tooltip of export button
Tooltip_Export=Exporter
#XFLD tooltip of save button
Tooltip_Save=Sauvegarder
#XFLD tooltip of validate button
Tooltip_Validate=Valider
#XMSG
InvalidVersion=Saisissez une version au format "x.y.z".
#XMSG
VersionLowerThanBefore=La version ne peut pas être inférieure à la version précédente.
#XMSG
Empty_Version=Saisissez une version de package.
#XMSG
Package_Missing_Technical_Name=Saisissez un nom technique pour le package.
#=============from skyline============

#XFLD lable for space combo selection
Spaces=Espace :
#XFLD section title of landing page
Packages=Packages
#XCOL business name column
Table_Col_BusinessName=Appellation
#XCOL technical name column
Table_Col_TechnicalName=Nom technique
#XCOL sapce column
Table_Col_Space=Espace
#XCOL create on column
Table_Col_CreatedOn=Date/Heure de création
#XCOL entity type column
Table_Col_Type=Type
#XCOL entity type column
Table_Col_Object_Status=Statut
#XMSG message of deleting packages
Msg_PackageRemoved={0} packages supprimés
#XMIT menu item of all spaces
Op_AllSpaces=Tous les espaces
#XFLD default business name of a new package
NewPackage_BusinessName=Package 1
#XFLD default technical name of a new package
NewPackage_TechnicalName=Package_1
#XFLD default name of a new package displayed in breadcrumb
Breadcrumb_NewPackage_Name=Nouveau package
#XFLD title of object selection dialog
ObjectsDialog_Title=Ajouter des objets
#XMSG dependencies are fully resolved
Dependency_Resolved=Prêt pour l''ajout (toutes les dépendances ont été résolues ({0}))
#XMSG dependencies are partially resolved
Dependency_Partial_Resolved=Prêt pour l''ajout (certaines dépendances n''ont pas été résolues ({0}/{1}))
#XMSG dependencies are not found
Dependency_Not_Found=Dépendances introuvables
#XMSG dependencies are in other package
Dependency_In_Other_Package=Ajout impossible : déjà dans le package "{0}"
#XMSG dependencies are in other space
Dependency_In_Other_Space=Ajout impossible : dans l''espace {0}
#XMSG
Cannot_Add_Managed_Content=Ajout impossible : contenu géré
#XMSG
Cannot_Add_Due_To_Dependency_Has_Managed_Content=Ajout impossible : la dépendance a un contenu géré
#XMSG dependencies are in current space
Dependency_In_Current_Package=Déjà dans le package
#XMSG dependencies are in required package
Dependency_In_Required_Package=Déjà dans le package requis
#XMSG package arelady exists
Package_Duplicate_Name=Le package ''{0}'' existe déjà dans le référentiel. Veuillez saisir un autre nom.
#XMSG package name is required
Package_Name_Missing=Saisissez un nom de package.
#XMSG package version is required
Package_Version_Missing=Saisissez une version de package.
#XMSG package is drafted
Package_Draft_Warning=Package en version préliminaire. Cliquez sur "Sauvegarder" pour vérifier les dépendances et confirmer vos modifications.
#XFLD title of validation message dialog
Validation_MessageBox_Title=Messages de validation
#XMSG content message of validation message dialog
Validation_MessageBox_Content="{0}" n''est pas valide et ne peut pas être exporté. Vous devez complètement résoudre toutes les dépendances d''objet en ajoutant les objets nécessaires manuellement ou via un package requis.
#XBUT save anyway button
Save_Anyway=Sauvegarder quand même
#XMSG package is valid
Valid_Package_Message=Le package est valide.
#XMSG
Object_In_Other_Pacakge=L''objet "{0}" est dans le package "{1}".
#XMSG
Dependent_Object_In_Other_Pacakge=Objet "{0}" dans le package "{1}", qui n''est pas indiqué comme package requis.
#XMSG
Dependent_Object_Missing=Objet "{0}" dans l''espace {1}.
#XMSG
Dependent_Missing_Pattern=L''objet "{0}" dépend de \r\n{1}
#XMSG: error message for saving the package
Save_Error_Message=Échec de la sauvegarde du package. {0}
#XFLD title of save busy dialog
Title_Save_Busy_Dialog=Sauvegarde en cours
#XMSG: message of it takes long time to save
Long_Time_Save_Busy_Dialog=La sauvegarde prend plus de temps que d'habitude. Veuillez patienter.
#XMSG: erro message of missing dependency
Dependency_Missing=Dépendance manquante.
#XMSG: erro message of missing dependency
Object_Dependency_Missing=Dépendance de "{0}" manquante.
#XMSG message of validating busy dialog
Validating_Package=Validation du package en cours
#XFLD title of validating busy dialog
Title_Validating_Busy_Dialog=En cours de traitement
#XMSG %%0 is a placeholder, don’t change it
Missing_Dependency=Dépendances manquantes %%0
#XMSG %%0 is a placeholder, don’t change it
Circular_Dependency=Dépendance circulaire %%0
#XMSG Object is added to package successfully
Successfully_Add=Ajouté
#XMSG
Circular_Dependency_Detail=Impossible de sauvegarder "{0}" car il a une dépendance circulaire avec le package "{1}".
#XFLD label of searching objects in space
Search_In_Sapce=Recherche dans : {0}
#XFLD title of deleting package confirm dialog
Package_Deleting_Dialog_Title=Supprimer
#XMSG content message of deleting package confirm dialog
Package_Deleting_Dialog_Message=Souhaitez-vous supprimer les packages sélectionnés ?
#XMSG
Package_Cannot_Delete=Le package sélectionné est requis par d'autres packages et ne peut pas être supprimé.
#XMSG
Package_Required_By_Singular=(requis par {0} package)
#XMSG
Package_Required_By_Plural=(requis par {0} packages)
#XFLD display name of owner column
owner=Propriétaire
#XFLD title of deleting package error dialog
Deleting_Error_Dialog_Title=Erreur
#XFLD title of deleting package alert dialog
Deleting_Alert_Dialog_Title=Alerte
#XBUT
Deleting_Alert_Dialog_Ok=OK
#XFLD
MyContent=Mon contenu
#XFLD
NotExported=Non exporté
#XFLD
Exporting=Export en cours
#XFLD
Exported=Exporté
#XFLD
DesignTimeError=Erreur au moment de la conception
#XFLD
ExportFailed=Échec de l'export
#XFLD
Cancelling=Annulation
#XFLD
ChangesToExport=Modifications à exporter
#XMSG
Exporting_Package=Export de {0} en cours. Nous vous informerons lorsque le processus sera terminé.
#XFLD: Object type is businessEntityVariant
TypeBusinessEntityVariant=Version de l'entité commerciale
#XFLD: Object type is businessEntity
TypeBusinessEntity=Entité commerciale
#XFLD
Tooltip_Add=Ajouter
#XFLD
Tooltip_Edit=Modifier
#XFLD
Tooltip_Delete=Supprimer
#XFLD
Tooltip_Refresh=Actualiser
#XMSG
ExportToOverwrite=Ce package a déjà été exporté dans le réseau de contenus avec la version {0}. Voulez-vous l''écraser ?
# XFLD Column businessName
Ext_Selection_Col_Name=Nom
# XFLD Column Location
Ext_Selection_Col_Location=Emplacement
# XFLD
Col_Name=Nom
# XFLD
Col_Description=Description
# XFLD Label
MoveTo_Label=Déplacer dans dossier
#XMIT Add versions menu button text in Data Builder
versions=Versions
createVersion=Créer une version
versionHistory=Historique de la version
#XMSG
Package_Depends_On_DP=Le package dépend des produits de données.
#XMSG
Package_Depends_On_DP_Warning=Avant d''importer ce package, assurez-vous que les produits de données suivants sont disponibles dans l''espace cible : \r\n\r\n{0}.
# XFLD
Export_Confirm_Dailog_Title=Confirmation
#XMSG
Package_Contains_DataObjects=Ce package inclut les objets suivants contenant des données pour le transport :\r\n\r\n{0} \r\n\r\nAssurez-vous qu''aucune donnée personnelle ou sensible ne sera exposée de manière inappropriée suite au transport de ces objets dans votre package.
