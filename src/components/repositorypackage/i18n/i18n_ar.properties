# https://wiki.wdf.sap.corp/wiki/display/DWAAS/How+to+add+text+classifications+for+translatable+texts
#
# use the following text types for short texts (up to 120 characters)
# XACT Accessibility
# XBUT Button
# XCKL Checkbox
# XCOL Column header
# XFLD Label
# XGRP Group title
# XLNK Hyperlink
# XLOG Log entry
# XLST List box item
# XMIT Menu item
# XMSG Message
# XRBL Radio button
# XSEL Selection
# XTIT Table title
# XTOL Tooltip
#
# for longer texts (more than 120 characters) use
# YINS Instruction
# YMSG Message

#=============from skyline============
#XFLD general tab of package editor
Tab_General=عام
#XFLD objects tab of package editor
Tab_Objects=الكائنات
#XFLD required packages tab of package editor
Tab_RequiredPkgs=الحزم المطلوبة
#XFLD label for business name input
BusinessName=اسم الشركة
#XFLD label for technical name input
TechnicalName=الاسم التقني
#XFLD label for purpose input
BusinessPurpose=الغرض التجاري
#XTIT title of save dialog
Dilaog_Save=حفظ
#XBUT save button of save dialog
Btn_Save=حفظ
#XBUT cancel button of save dialog
Btn_Cancel=إلغاء
#XFLD title of objects section
Objects=الكائنات
#XFLD title of required packages section
RequiredPackages=الحزم المطلوبة
#XTIT title of package selection dialog
Dialog_SelectPackages=تحديد الحزم
#XMSG no data text of package selection dialog
Dilaog_NoDataText=لم يتم العثور على حزم مستودع
#XMSG message of adding objects
Msg_ObjectAdded=تمت إضافة {0} من الكائنات
#XMSG message of removing objects
Msg_ObjectRemoved=تمت إزالة {0} من الكائنات
#XMSG message of adding required packages
Msg_RequiredPackageAdded=تمت إضافة {0} من الحزم المطلوبة
#XMSG message of removing required packages
Msg_RequiredPackageRemoved=تمت إزالة {0} من الحزم المطلوبة
#XBUT add button in toolbar
Toolbar_Btn_Add=إضافة
#XBUT remove button in toolbar
Toolbar_Btn_Remove=إزالة
#XCOL status column of objects and required packages table
Table_Col_Status=النتيجة
#XLNK more link of status details
Status_More_Link=جارٍ إظهار التفاصيل...
#XFLD folder name
FolderName=الاسم
#XFLD folder description
FolderDescription=الوصف
#XBUT ok button
Btn_OK=موافق
#XFLD title of export location dialog
ExportLocationDialog=موقع التصدير
#XFLD lable of category field
Category=الفئة
#XFLD label of location field
Location=الموقع المستهدف
#XFLD lable of version field
SemanticVersion=الإصدار
#XFLD label of current exported version
CurrentExportedPackage=الإصدار المصدَّر الحالي
#XFLD label of status field
ExportStatus=الحالة
#XFLD tooltip of export button
Tooltip_Export=تصدير
#XFLD tooltip of save button
Tooltip_Save=حفظ
#XFLD tooltip of validate button
Tooltip_Validate=تحقق من الصحة
#XMSG
InvalidVersion=أدخل إصدارًا في شكل "x.y.z".
#XMSG
VersionLowerThanBefore=لا يمكن أن يكون الإصدار أقل من الإصدار السابق.
#XMSG
Empty_Version=أدخل إصدار الحزمة.
#XMSG
Package_Missing_Technical_Name=أدخل اسمًا تقنيًا للحزمة.
#=============from skyline============

#XFLD lable for space combo selection
Spaces=المساحة:
#XFLD section title of landing page
Packages=الحزم
#XCOL business name column
Table_Col_BusinessName=اسم الشركة
#XCOL technical name column
Table_Col_TechnicalName=الاسم التقني
#XCOL sapce column
Table_Col_Space=المساحة
#XCOL create on column
Table_Col_CreatedOn=تاريخ الإنشاء
#XCOL entity type column
Table_Col_Type=النوع
#XCOL entity type column
Table_Col_Object_Status=الحالة
#XMSG message of deleting packages
Msg_PackageRemoved=تمت إزالة {0} من الحزم
#XMIT menu item of all spaces
Op_AllSpaces=كل المساحات
#XFLD default business name of a new package
NewPackage_BusinessName=الحزمة 1
#XFLD default technical name of a new package
NewPackage_TechnicalName=Package_1
#XFLD default name of a new package displayed in breadcrumb
Breadcrumb_NewPackage_Name=حزمة جديدة
#XFLD title of object selection dialog
ObjectsDialog_Title=إضافة الكائنات
#XMSG dependencies are fully resolved
Dependency_Resolved=جاهز للإضافة (تم حل جميع التبعيات ({0}))
#XMSG dependencies are partially resolved
Dependency_Partial_Resolved=جاهز للإضافة (لم يتم حل بعض التبعيات ({0}/{1})
#XMSG dependencies are not found
Dependency_Not_Found=لا يمكن العثور على التبعيات
#XMSG dependencies are in other package
Dependency_In_Other_Package=لا يمكن الإضافة: موجود بالفعل في الحزمة "{0}"
#XMSG dependencies are in other space
Dependency_In_Other_Space=لا يمكن الإضافة: في المساحة {0}
#XMSG
Cannot_Add_Managed_Content=تتعذر إضافة: المحتوى المُدار
#XMSG
Cannot_Add_Due_To_Dependency_Has_Managed_Content=تتعذر الإضافة: تحتوي التبعية على محتوى مُدار
#XMSG dependencies are in current space
Dependency_In_Current_Package=موجود في الحزمة بالفعل
#XMSG dependencies are in required package
Dependency_In_Required_Package=موجود في الحزمة المطلوبة بالفعل
#XMSG package arelady exists
Package_Duplicate_Name=الحزمة "{0}" موجودة بالفعل في المستودع. يُرجى إدخال اسم آخر.
#XMSG package name is required
Package_Name_Missing=أدخل اسم الحزمة.
#XMSG package version is required
Package_Version_Missing=أدخل إصدار الحزمة.
#XMSG package is drafted
Package_Draft_Warning=حزمة المسودة. انقر فوق "حفظ" للتحقق من التبعيات وتأكيد تغييراتك.
#XFLD title of validation message dialog
Validation_MessageBox_Title=رسائل التحقق من الصحة
#XMSG content message of validation message dialog
Validation_MessageBox_Content="{0}" غير صالح ولا يمكن تصديره. يجب حل جميع تبعيات الكائنات بشكل كامل من خلال إضافة الكائنات الضرورية يدويًا أو من خلال حزمة مطلوبة.
#XBUT save anyway button
Save_Anyway=حفظ على أي حال
#XMSG package is valid
Valid_Package_Message=الحزمة صالحة.
#XMSG
Object_In_Other_Pacakge=الكائن "{0}" في الحزمة "{1}".
#XMSG
Dependent_Object_In_Other_Pacakge=الكائن "{0}" في الحزمة "{1}" غير المحددة كحزمة مطلوبة.
#XMSG
Dependent_Object_Missing=الكائن "{0}" في المساحة {1}.
#XMSG
Dependent_Missing_Pattern=يعتمد الكائن "{0}" على‎:\r\n{1}
#XMSG: error message for saving the package
Save_Error_Message=فشل حفظ الحزمة. {0}
#XFLD title of save busy dialog
Title_Save_Busy_Dialog=حفظ
#XMSG: message of it takes long time to save
Long_Time_Save_Busy_Dialog=يستغرق الحفظ وقتًا أطول من المعتاد. يرجى الانتظار.
#XMSG: erro message of missing dependency
Dependency_Missing=التبعية مفقودة.
#XMSG: erro message of missing dependency
Object_Dependency_Missing=تبعية "{0}" مفقودة.
#XMSG message of validating busy dialog
Validating_Package=جارٍ التحقق من صحة الحزمة
#XFLD title of validating busy dialog
Title_Validating_Busy_Dialog=معالجة
#XMSG %%0 is a placeholder, don’t change it
Missing_Dependency=التبعيات المفقودة %%0
#XMSG %%0 is a placeholder, don’t change it
Circular_Dependency=تبعية دائرية %%0
#XMSG Object is added to package successfully
Successfully_Add=مُضاف
#XMSG
Circular_Dependency_Detail=لا يمكن حفظ "{0}" لأنه يحتوي على تبعية دائرية بالحزمة "{1}".
#XFLD label of searching objects in space
Search_In_Sapce=بحث في: {0}
#XFLD title of deleting package confirm dialog
Package_Deleting_Dialog_Title=حذف
#XMSG content message of deleting package confirm dialog
Package_Deleting_Dialog_Message=هل تريد حذف الحزم المحددة؟
#XMSG
Package_Cannot_Delete=الحزمة المحددة مطلوبة بواسطة حزم أخرى ولا يمكن حذفها.
#XMSG
Package_Required_By_Singular=(مطلوب بواسطة {0} حزمة)
#XMSG
Package_Required_By_Plural=(مطلوب بواسطة  {0} من الحزم)
#XFLD display name of owner column
owner=المالك
#XFLD title of deleting package error dialog
Deleting_Error_Dialog_Title=خطأ
#XFLD title of deleting package alert dialog
Deleting_Alert_Dialog_Title=تنبيه
#XBUT
Deleting_Alert_Dialog_Ok=موافق
#XFLD
MyContent=المحتوى الخاص بي
#XFLD
NotExported=غير مُصدَّر
#XFLD
Exporting=جارٍ التصدير
#XFLD
Exported=مصدَّر
#XFLD
DesignTimeError=خطأ في وقت التصميم
#XFLD
ExportFailed=فشل التصدير
#XFLD
Cancelling=قيد الإلغاء
#XFLD
ChangesToExport=تغييرات التصدير
#XMSG
Exporting_Package=جارٍ تصدير {0}. سنقوم بإشعارك عند اكتمال العملية.
#XFLD: Object type is businessEntityVariant
TypeBusinessEntityVariant=إصدار كيان الأعمال
#XFLD: Object type is businessEntity
TypeBusinessEntity=كيان الأعمال
#XFLD
Tooltip_Add=إضافة
#XFLD
Tooltip_Edit=تحرير
#XFLD
Tooltip_Delete=حذف
#XFLD
Tooltip_Refresh=تحديث
#XMSG
ExportToOverwrite=تم تصدير هذه الحزمة بالفعل إلى شبكة المحتوى بالإصدار {0}. هل تريد استبداله؟
# XFLD Column businessName
Ext_Selection_Col_Name=الاسم
# XFLD Column Location
Ext_Selection_Col_Location=الموقع
# XFLD
Col_Name=الاسم
# XFLD
Col_Description=الوصف
# XFLD Label
MoveTo_Label=نقل إلى المجلد
#XMIT Add versions menu button text in Data Builder
versions=الإصدارات
createVersion=إنشاء إصدار
versionHistory=سجل الإصدارات
#XMSG
Package_Depends_On_DP=تعتمد هذه الحزمة على منتجات البيانات
#XMSG
Package_Depends_On_DP_Warning=قبل استيراد هذه الحزمة، تأكد من توفر منتجات البيانات التالية للمساحة المستهدفة:\r\n\r\n{0}
# XFLD
Export_Confirm_Dailog_Title=التأكيد
#XMSG
Package_Contains_DataObjects=تتضمن هذه الحزمة الكائنات التالية التي تحتوي على بيانات للنقل:\r\n\r\n{0} \r\n\r\nيُرجى التأكد من عدم عرض أي بيانات شخصية أو حساسة بشكل غير صحيح نتيجة لنقل هذه الكائنات في حزمتك.
