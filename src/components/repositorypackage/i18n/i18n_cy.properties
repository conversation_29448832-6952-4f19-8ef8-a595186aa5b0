# https://wiki.wdf.sap.corp/wiki/display/DWAAS/How+to+add+text+classifications+for+translatable+texts
#
# use the following text types for short texts (up to 120 characters)
# XACT Accessibility
# XBUT Button
# XCKL Checkbox
# XCOL Column header
# XFLD Label
# XGRP Group title
# XLNK Hyperlink
# XLOG Log entry
# XLST List box item
# XMIT Menu item
# XMSG Message
# XRBL Radio button
# XSEL Selection
# XTIT Table title
# XTOL Tooltip
#
# for longer texts (more than 120 characters) use
# YINS Instruction
# YMSG Message

#=============from skyline============
#XFLD general tab of package editor
Tab_General=Cyffredinol
#XFLD objects tab of package editor
Tab_Objects=Gwrthrychau
#XFLD required packages tab of package editor
Tab_RequiredPkgs=Pecynnau Gofynnol
#XFLD label for business name input
BusinessName=Enw Busnes
#XFLD label for technical name input
TechnicalName=Enw Technegol
#XFLD label for purpose input
BusinessPurpose=Pwrpas Busnes
#XTIT title of save dialog
Dilaog_Save=Cadw
#XBUT save button of save dialog
Btn_Save=Cadw
#XBUT cancel button of save dialog
Btn_Cancel=Canslo
#XFLD title of objects section
Objects=Gwrthrychau
#XFLD title of required packages section
RequiredPackages=Pecynnau Gofynnol
#XTIT title of package selection dialog
Dialog_SelectPackages=Dewis Pecynnau
#XMSG no data text of package selection dialog
Dilaog_NoDataText=Heb ddod o hyd i Becynnau Ystorfa
#XMSG message of adding objects
Msg_ObjectAdded={0} gwrthrych wedi''i ychwanegu
#XMSG message of removing objects
Msg_ObjectRemoved={0} gwrthrych wedi''i dynnu
#XMSG message of adding required packages
Msg_RequiredPackageAdded={0} pecyn gofynnol wedi''i ychwanegu
#XMSG message of removing required packages
Msg_RequiredPackageRemoved={0} pecyn gofynnol wedi''i dynnu
#XBUT add button in toolbar
Toolbar_Btn_Add=Ychwanegu
#XBUT remove button in toolbar
Toolbar_Btn_Remove=Tynnu
#XCOL status column of objects and required packages table
Table_Col_Status=Canlyniad
#XLNK more link of status details
Status_More_Link=Dangos manylion...
#XFLD folder name
FolderName=Enw
#XFLD folder description
FolderDescription=Disgrifiad
#XBUT ok button
Btn_OK=Iawn
#XFLD title of export location dialog
ExportLocationDialog=Lleoliad Allgludo
#XFLD lable of category field
Category=Categori
#XFLD label of location field
Location=Lleoliad Targed
#XFLD lable of version field
SemanticVersion=Fersiwn
#XFLD label of current exported version
CurrentExportedPackage=Fersiwn Bresennol wedi'i Hallgludo
#XFLD label of status field
ExportStatus=Statws
#XFLD tooltip of export button
Tooltip_Export=Allgludo
#XFLD tooltip of save button
Tooltip_Save=Cadw
#XFLD tooltip of validate button
Tooltip_Validate=Dilysu
#XMSG
InvalidVersion=Rhowch fersiwn ar ffurf "x.y.z".
#XMSG
VersionLowerThanBefore=Does dim modd i fersiwn fod yn is na'r fersiwn flaenorol.
#XMSG
Empty_Version=Rhowch fersiwn pecyn.
#XMSG
Package_Missing_Technical_Name=Rhowch enw technegol ar gyfer y pecyn.
#=============from skyline============

#XFLD lable for space combo selection
Spaces=Gofod:
#XFLD section title of landing page
Packages=Pecynnau
#XCOL business name column
Table_Col_BusinessName=Enw Busnes
#XCOL technical name column
Table_Col_TechnicalName=Enw Technegol
#XCOL sapce column
Table_Col_Space=Gofod
#XCOL create on column
Table_Col_CreatedOn=Wedi'i Greu Ar
#XCOL entity type column
Table_Col_Type=Math
#XCOL entity type column
Table_Col_Object_Status=Statws
#XMSG message of deleting packages
Msg_PackageRemoved={0} pecyn wedi''i dynnu
#XMIT menu item of all spaces
Op_AllSpaces=Pob Gofod
#XFLD default business name of a new package
NewPackage_BusinessName=Pecyn 1
#XFLD default technical name of a new package
NewPackage_TechnicalName=Pecyn_1
#XFLD default name of a new package displayed in breadcrumb
Breadcrumb_NewPackage_Name=Pecyn Newydd
#XFLD title of object selection dialog
ObjectsDialog_Title=Ychwanegu Gwrthrychau
#XMSG dependencies are fully resolved
Dependency_Resolved=Parod i Ychwanegu (Pob Dibyniaeth wedi''i Datrys ({0}))
#XMSG dependencies are partially resolved
Dependency_Partial_Resolved=Parod i Ychwanegu (Rhai Dibyniaethau heb eu Datrys ({0}/{1})
#XMSG dependencies are not found
Dependency_Not_Found=Methu dod o hyd i ddibyniaethau
#XMSG dependencies are in other package
Dependency_In_Other_Package=Methu Ychwanegu: Eisoes ym Mhecyn "{0}"
#XMSG dependencies are in other space
Dependency_In_Other_Space=Methu Ychwanegu: Yng Ngofod {0}
#XMSG
Cannot_Add_Managed_Content=Methu Ychwanegu: Cynnwys wedi'i Reoli
#XMSG
Cannot_Add_Due_To_Dependency_Has_Managed_Content=Methu Ychwanegu: Mae gan Ddibynnydd Gynnwys wedi'i Reoli
#XMSG dependencies are in current space
Dependency_In_Current_Package=Eisoes ym Mhecyn
#XMSG dependencies are in required package
Dependency_In_Required_Package=Eisoes yn y Pecyn Gofynnol
#XMSG package arelady exists
Package_Duplicate_Name=Mae pecyn "{0}" eisoes yn bodoli yn yr ystorfa. Rhowch enw arall.
#XMSG package name is required
Package_Name_Missing=Rhowch enw pecyn.
#XMSG package version is required
Package_Version_Missing=Rhowch fersiwn pecyn.
#XMSG package is drafted
Package_Draft_Warning=Pecyn drafft. Cliciwch "Cadw" i wirio dibyniaethau a chadarnhau eich newidiadau.
#XFLD title of validation message dialog
Validation_MessageBox_Title=Negeseuon Dilysu
#XMSG content message of validation message dialog
Validation_MessageBox_Content=Mae "{0}" yn annilys a does dim modd ei allgludo. Rhaid i chi ddatrys pob dibyniaeth gwrthrych yn llawn drwy ychwanegu''r gwrthrychau angenrheidiol eich hun neu drwy becyn gofynnol.
#XBUT save anyway button
Save_Anyway=Cadw Beth Bynnag
#XMSG package is valid
Valid_Package_Message=Mae'r pecyn yn ddilys.
#XMSG
Object_In_Other_Pacakge=Mae gwrthrych "{0}" ym mhecyn "{1}".
#XMSG
Dependent_Object_In_Other_Pacakge=Mae gwrthrych "{0}" ym mhecyn "{1}", sydd ddim wedi''i nodi fel pecyn gofynnol.
#XMSG
Dependent_Object_Missing=Mae gwrthrych "{0}" yng ngofod "{1}".
#XMSG
Dependent_Missing_Pattern=Mae gwrthrych "{0}" yn dibynnu ar:\r\n{1}
#XMSG: error message for saving the package
Save_Error_Message=Wedi methu cadw''r pecyn. {0}
#XFLD title of save busy dialog
Title_Save_Busy_Dialog=Wrthi'n cadw
#XMSG: message of it takes long time to save
Long_Time_Save_Busy_Dialog=Mae cadw yn cymryd hirach nag arfer. Arhoswch.
#XMSG: erro message of missing dependency
Dependency_Missing=Dibyniaeth ar goll.
#XMSG: erro message of missing dependency
Object_Dependency_Missing=Mae dibyniaeth "{0}" ar goll.
#XMSG message of validating busy dialog
Validating_Package=Wrthi'n dilysu pecyn
#XFLD title of validating busy dialog
Title_Validating_Busy_Dialog=Wrthi'n prosesu
#XMSG %%0 is a placeholder, don’t change it
Missing_Dependency=Dibyniaethau ar goll %%0
#XMSG %%0 is a placeholder, don’t change it
Circular_Dependency=Dibyniaeth gylchol %%0
#XMSG Object is added to package successfully
Successfully_Add=Wedi ychwanegu
#XMSG
Circular_Dependency_Detail=Does dim modd cadw "{0}" oherwydd fod ganddo ddibyniaeth gylchol gyda phecyn "{1}".
#XFLD label of searching objects in space
Search_In_Sapce=Chwilio yn: {0}
#XFLD title of deleting package confirm dialog
Package_Deleting_Dialog_Title=Dileu
#XMSG content message of deleting package confirm dialog
Package_Deleting_Dialog_Message=Ydych chi am ddileu’r pecynnau dan sylw?
#XMSG
Package_Cannot_Delete=Mae pecynnau eraill eisiau'r pecyn dan sylw a does dim modd ei ddileu.
#XMSG
Package_Required_By_Singular=(gofynnol gan {0} pecyn)
#XMSG
Package_Required_By_Plural=(gofynnol gan {0} pecyn)
#XFLD display name of owner column
owner=Perchennog
#XFLD title of deleting package error dialog
Deleting_Error_Dialog_Title=Gwall
#XFLD title of deleting package alert dialog
Deleting_Alert_Dialog_Title=Rhybudd
#XBUT
Deleting_Alert_Dialog_Ok=Iawn
#XFLD
MyContent=Fy Nghynnwys
#XFLD
NotExported=Heb ei Allgludo
#XFLD
Exporting=Wrthi'n Allgludo
#XFLD
Exported=Wedi Allgludo
#XFLD
DesignTimeError=Gwall Amser Dyluniad
#XFLD
ExportFailed=Wedi methu Allgludo
#XFLD
Cancelling=Wrthi'n Canslo
#XFLD
ChangesToExport=Newidiadau I Allgludo
#XMSG
Exporting_Package=Wrthi''n allgludo {0}. Byddwn yn rhoi gwybod i chi pan fydd y broses wedi gorffen.
#XFLD: Object type is businessEntityVariant
TypeBusinessEntityVariant=Fersiwn Endid Busnes
#XFLD: Object type is businessEntity
TypeBusinessEntity=Endid Busnes
#XFLD
Tooltip_Add=Ychwanegu
#XFLD
Tooltip_Edit=Golygu
#XFLD
Tooltip_Delete=Dileu
#XFLD
Tooltip_Refresh=Adnewyddu
#XMSG
ExportToOverwrite=Mae''r pecyn hwn wedi''i allgludo''n barod i''r Rhwydwaith Cynnwys gyda fersiwn {0}. Ydych chi am ei ddisodli?
# XFLD Column businessName
Ext_Selection_Col_Name=Enw
# XFLD Column Location
Ext_Selection_Col_Location=Lleoliad
# XFLD
Col_Name=Enw
# XFLD
Col_Description=Disgrifiad
# XFLD Label
MoveTo_Label=Symud i Ffolder
#XMIT Add versions menu button text in Data Builder
versions=Fersiynau
createVersion=Creu Fersiwn
versionHistory=Hanes Fersiwn
#XMSG
Package_Depends_On_DP=Mae'r pecyn hwn yn dibynnu ar gynhyrchion data
#XMSG
Package_Depends_On_DP_Warning=Cyn mewngludo''r pecyn hwn, gwnewch yn siŵr fod y cynhyrchion data canlynol ar gael i''r gofod targed:\r\n\r\n{0}
# XFLD
Export_Confirm_Dailog_Title=Cadarnhad
#XMSG
Package_Contains_DataObjects=Mae''r pecyn hwn yn cynnwys y gwrthrychau canlynol sy''n cynnwys data ar gyfer cludiant:\r\n\r\n{0}\r\n\r\nGwnewch yn siŵr na fydd unrhyw ddata personol na sensitif yn cael ei ddatgelu’n amhriodol o ganlyniad i gludo’r gwrthrychau hyn yn eich pecyn.
