# https://wiki.wdf.sap.corp/wiki/display/DWAAS/How+to+add+text+classifications+for+translatable+texts
#
# use the following text types for short texts (up to 120 characters)
# XACT Accessibility
# XBUT Button
# XCKL Checkbox
# XCOL Column header
# XFLD Label
# XGRP Group title
# XLNK Hyperlink
# XLOG Log entry
# XLST List box item
# XMIT Menu item
# XMSG Message
# XRBL Radio button
# XSEL Selection
# XTIT Table title
# XTOL Tooltip
#
# for longer texts (more than 120 characters) use
# YINS Instruction
# YMSG Message

#=============from skyline============
#XFLD general tab of package editor
Tab_General=General
#XFLD objects tab of package editor
Tab_Objects=Objetos
#XFLD required packages tab of package editor
Tab_RequiredPkgs=Paquetes requeridos
#XFLD label for business name input
BusinessName=Nombre empresarial
#XFLD label for technical name input
TechnicalName=Nombre técnico
#XFLD label for purpose input
BusinessPurpose=Propósito empresarial
#XTIT title of save dialog
Dilaog_Save=Guardar
#XBUT save button of save dialog
Btn_Save=Guardar
#XBUT cancel button of save dialog
Btn_Cancel=Cancelar
#XFLD title of objects section
Objects=Objetos
#XFLD title of required packages section
RequiredPackages=Paquetes requeridos
#XTIT title of package selection dialog
Dialog_SelectPackages=Seleccionar paquetes
#XMSG no data text of package selection dialog
Dilaog_NoDataText=No se encontraron paquetes de repositorio
#XMSG message of adding objects
Msg_ObjectAdded={0} objetos agregados
#XMSG message of removing objects
Msg_ObjectRemoved={0} objetos quitados
#XMSG message of adding required packages
Msg_RequiredPackageAdded={0} paquetes requeridos agregados
#XMSG message of removing required packages
Msg_RequiredPackageRemoved={0} paquetes requeridos quitados
#XBUT add button in toolbar
Toolbar_Btn_Add=Agregar
#XBUT remove button in toolbar
Toolbar_Btn_Remove=Quitar
#XCOL status column of objects and required packages table
Table_Col_Status=Resultado
#XLNK more link of status details
Status_More_Link=Mostrar detalles…
#XFLD folder name
FolderName=Nombre
#XFLD folder description
FolderDescription=Descripción
#XBUT ok button
Btn_OK=OK
#XFLD title of export location dialog
ExportLocationDialog=Exportar ubicación
#XFLD lable of category field
Category=Categoría
#XFLD label of location field
Location=Ubicación de destino
#XFLD lable of version field
SemanticVersion=Versión
#XFLD label of current exported version
CurrentExportedPackage=Versión exportada actual
#XFLD label of status field
ExportStatus=Estado
#XFLD tooltip of export button
Tooltip_Export=Exportar
#XFLD tooltip of save button
Tooltip_Save=Guardar
#XFLD tooltip of validate button
Tooltip_Validate=Validar
#XMSG
InvalidVersion=Ingrese una versión en el formato "x.y.z".
#XMSG
VersionLowerThanBefore=La versión no puede ser inferior a la anterior.
#XMSG
Empty_Version=Ingrese una versión de paquete.
#XMSG
Package_Missing_Technical_Name=Ingrese un nombre técnico para el paquete.
#=============from skyline============

#XFLD lable for space combo selection
Spaces=Espacio:
#XFLD section title of landing page
Packages=Paquetes
#XCOL business name column
Table_Col_BusinessName=Nombre empresarial
#XCOL technical name column
Table_Col_TechnicalName=Nombre técnico
#XCOL sapce column
Table_Col_Space=Espacio
#XCOL create on column
Table_Col_CreatedOn=Fecha de creación
#XCOL entity type column
Table_Col_Type=Tipo
#XCOL entity type column
Table_Col_Object_Status=Estado
#XMSG message of deleting packages
Msg_PackageRemoved={0} paquetes quitados
#XMIT menu item of all spaces
Op_AllSpaces=Todos los espacios
#XFLD default business name of a new package
NewPackage_BusinessName=Paquete 1
#XFLD default technical name of a new package
NewPackage_TechnicalName=Paquete_1
#XFLD default name of a new package displayed in breadcrumb
Breadcrumb_NewPackage_Name=Nuevo paquete
#XFLD title of object selection dialog
ObjectsDialog_Title=Agregar objetos
#XMSG dependencies are fully resolved
Dependency_Resolved=Listo para agregar (todas las dependencias resueltas ({0}))
#XMSG dependencies are partially resolved
Dependency_Partial_Resolved=Listo para agregar (algunas dependencias no resueltas ({0}/{1})
#XMSG dependencies are not found
Dependency_Not_Found=No se pueden encontrar las dependencias
#XMSG dependencies are in other package
Dependency_In_Other_Package=No se puede agregar: ya en el paquete "{0}"
#XMSG dependencies are in other space
Dependency_In_Other_Space=No se puede agregar: en el espacio {0}
#XMSG
Cannot_Add_Managed_Content=No se puede agregar: contenido administrado
#XMSG
Cannot_Add_Due_To_Dependency_Has_Managed_Content=No se puede agregar: la dependencia tiene contenido administrado
#XMSG dependencies are in current space
Dependency_In_Current_Package=Ya en el paquete
#XMSG dependencies are in required package
Dependency_In_Required_Package=Ya en el paquete requerido
#XMSG package arelady exists
Package_Duplicate_Name=El paquete {0} ya existe en el repositorio. Ingrese otro nombre.
#XMSG package name is required
Package_Name_Missing=Ingrese un nombre de paquete.
#XMSG package version is required
Package_Version_Missing=Ingrese una versión de paquete.
#XMSG package is drafted
Package_Draft_Warning=Paquete borrador. Haga clic en "Guardar" para verificar las dependencias y confirmar sus cambios.
#XFLD title of validation message dialog
Validation_MessageBox_Title=Mensajes de validación
#XMSG content message of validation message dialog
Validation_MessageBox_Content="{0}" no es válido y no se puede exportar. Debe resolver completamente todas las dependencias de objeto agregando los objetos necesarios manualmente o a través del paquete requerido.
#XBUT save anyway button
Save_Anyway=Guardar de todas maneras
#XMSG package is valid
Valid_Package_Message=El paquete es válido.
#XMSG
Object_In_Other_Pacakge=El objeto "{0}" se encuentra en el paquete "{1}".
#XMSG
Dependent_Object_In_Other_Pacakge=El objeto "{0}" se encuentra en el paquete "{1}", que no está especificado como un paquete requerido.
#XMSG
Dependent_Object_Missing=El objeto "{0}" se encuentra en el espacio {1}.
#XMSG
Dependent_Missing_Pattern=El objeto "{0}" depende de:\r\n{1}
#XMSG: error message for saving the package
Save_Error_Message=No se pudo guardar el paquete. {0}
#XFLD title of save busy dialog
Title_Save_Busy_Dialog=Guardando
#XMSG: message of it takes long time to save
Long_Time_Save_Busy_Dialog=El guardado está tardando más de lo habitual. Espere.
#XMSG: erro message of missing dependency
Dependency_Missing=Falta la dependencia.
#XMSG: erro message of missing dependency
Object_Dependency_Missing=Falta la dependencia de "{0}".
#XMSG message of validating busy dialog
Validating_Package=Validando paquete
#XFLD title of validating busy dialog
Title_Validating_Busy_Dialog=Procesando
#XMSG %%0 is a placeholder, don’t change it
Missing_Dependency=Dependencias faltantes %%0
#XMSG %%0 is a placeholder, don’t change it
Circular_Dependency=Dependencia circular %%0
#XMSG Object is added to package successfully
Successfully_Add=Agregado
#XMSG
Circular_Dependency_Detail="{0}" no se puede guardar porque tiene una dependencia circular con el paquete "{1}".
#XFLD label of searching objects in space
Search_In_Sapce=Buscar en: {0}
#XFLD title of deleting package confirm dialog
Package_Deleting_Dialog_Title=Eliminar
#XMSG content message of deleting package confirm dialog
Package_Deleting_Dialog_Message=¿Quiere eliminar los paquetes seleccionados?
#XMSG
Package_Cannot_Delete=El paquete seleccionado es requerido por otros paquetes y no se puede eliminar.
#XMSG
Package_Required_By_Singular=(requerido por {0} paquete)
#XMSG
Package_Required_By_Plural=(requerido por {0} paquetes)
#XFLD display name of owner column
owner=Propietario
#XFLD title of deleting package error dialog
Deleting_Error_Dialog_Title=Error
#XFLD title of deleting package alert dialog
Deleting_Alert_Dialog_Title=Alerta
#XBUT
Deleting_Alert_Dialog_Ok=OK
#XFLD
MyContent=Mi contenido
#XFLD
NotExported=No exportado
#XFLD
Exporting=Exportando
#XFLD
Exported=Exportado
#XFLD
DesignTimeError=Error de tiempo de diseño
#XFLD
ExportFailed=Error de exportación
#XFLD
Cancelling=Cancelando
#XFLD
ChangesToExport=Cambios para exportar
#XMSG
Exporting_Package=Exportando {0}. Lo notificaremos cuando se complete el proceso.
#XFLD: Object type is businessEntityVariant
TypeBusinessEntityVariant=Versión de entidad empresarial
#XFLD: Object type is businessEntity
TypeBusinessEntity=Entidad empresarial
#XFLD
Tooltip_Add=Agregar
#XFLD
Tooltip_Edit=Editar
#XFLD
Tooltip_Delete=Eliminar
#XFLD
Tooltip_Refresh=Actualizar
#XMSG
ExportToOverwrite=Este paquete ya se exportó a la Red de contenido con la versión {0}. ¿Quiere sobrescribirlo?
# XFLD Column businessName
Ext_Selection_Col_Name=Nombre
# XFLD Column Location
Ext_Selection_Col_Location=Ubicación
# XFLD
Col_Name=Nombre
# XFLD
Col_Description=Descripción
# XFLD Label
MoveTo_Label=Mover a carpeta
#XMIT Add versions menu button text in Data Builder
versions=Versiones
createVersion=Crear versión
versionHistory=Historial de versiones
#XMSG
Package_Depends_On_DP=Este paquete depende de productos de datos
#XMSG
Package_Depends_On_DP_Warning=Antes de importar este paquete, asegúrese de que los siguientes productos de datos estén disponibles para el espacio de destino:\r\n\r\n{0}
# XFLD
Export_Confirm_Dailog_Title=Confirmación
#XMSG
Package_Contains_DataObjects=Este paquete incluye los siguientes objetos que contienen datos para transporte:\r\n\r\n{0}\r\n\r\nAsegúrese de que no se expongan datos personales o confidenciales incorrectamente como resultado del transporte de estos objetos en su paquete.
