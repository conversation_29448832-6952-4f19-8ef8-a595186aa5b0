# https://wiki.wdf.sap.corp/wiki/display/DWAAS/How+to+add+text+classifications+for+translatable+texts
#
# use the following text types for short texts (up to 120 characters)
# XACT Accessibility
# XBUT Button
# XCKL Checkbox
# XCOL Column header
# XFLD Label
# XGRP Group title
# XLNK Hyperlink
# XLOG Log entry
# XLST List box item
# XMIT Menu item
# XMSG Message
# XRBL Radio button
# XSEL Selection
# XTIT Table title
# XTOL Tooltip
#
# for longer texts (more than 120 characters) use
# YINS Instruction
# YMSG Message

#=============from skyline============
#XFLD general tab of package editor
Tab_General=Allgemein
#XFLD objects tab of package editor
Tab_Objects=Objekte
#XFLD required packages tab of package editor
Tab_RequiredPkgs=Erforderliche Pakete
#XFLD label for business name input
BusinessName=Betriebswirtschaftlicher Name
#XFLD label for technical name input
TechnicalName=Technischer Name
#XFLD label for purpose input
BusinessPurpose=Geschäftszweck
#XTIT title of save dialog
Dilaog_Save=Sichern
#XBUT save button of save dialog
Btn_Save=Sichern
#XBUT cancel button of save dialog
Btn_Cancel=Abbrechen
#XFLD title of objects section
Objects=Objekte
#XFLD title of required packages section
RequiredPackages=Erforderliche Pakete
#XTIT title of package selection dialog
Dialog_SelectPackages=Pakete auswählen
#XMSG no data text of package selection dialog
Dilaog_NoDataText=Keine Repository-Pakete gefunden
#XMSG message of adding objects
Msg_ObjectAdded={0} Objekte hinzugefügt
#XMSG message of removing objects
Msg_ObjectRemoved={0} Objekte entfernt
#XMSG message of adding required packages
Msg_RequiredPackageAdded={0} erforderliche Pakete hinzugefügt
#XMSG message of removing required packages
Msg_RequiredPackageRemoved={0} erforderliche Pakete entfernt
#XBUT add button in toolbar
Toolbar_Btn_Add=Hinzufügen
#XBUT remove button in toolbar
Toolbar_Btn_Remove=Entfernen
#XCOL status column of objects and required packages table
Table_Col_Status=Ergebnis
#XLNK more link of status details
Status_More_Link=Details anzeigen...
#XFLD folder name
FolderName=Name
#XFLD folder description
FolderDescription=Beschreibung
#XBUT ok button
Btn_OK=OK
#XFLD title of export location dialog
ExportLocationDialog=Exportspeicherort
#XFLD lable of category field
Category=Kategorie
#XFLD label of location field
Location=Zielspeicherort
#XFLD lable of version field
SemanticVersion=Version
#XFLD label of current exported version
CurrentExportedPackage=Aktuelle exportierte Version
#XFLD label of status field
ExportStatus=Status
#XFLD tooltip of export button
Tooltip_Export=Exportieren
#XFLD tooltip of save button
Tooltip_Save=Sichern
#XFLD tooltip of validate button
Tooltip_Validate=Validieren
#XMSG
InvalidVersion=Geben Sie eine Version im Format "x.y.z." ein.
#XMSG
VersionLowerThanBefore=Version darf nicht niedriger als die frühere Version sein.
#XMSG
Empty_Version=Geben Sie eine Paketversion ein.
#XMSG
Package_Missing_Technical_Name=Geben Sie einen technischen Namen für das Paket ein.
#=============from skyline============

#XFLD lable for space combo selection
Spaces=Space:
#XFLD section title of landing page
Packages=Pakete
#XCOL business name column
Table_Col_BusinessName=Betriebswirtschaftlicher Name
#XCOL technical name column
Table_Col_TechnicalName=Technischer Name
#XCOL sapce column
Table_Col_Space=Space
#XCOL create on column
Table_Col_CreatedOn=Angelegt am
#XCOL entity type column
Table_Col_Type=Typ
#XCOL entity type column
Table_Col_Object_Status=Status
#XMSG message of deleting packages
Msg_PackageRemoved={0} Pakete entfernt
#XMIT menu item of all spaces
Op_AllSpaces=alle Spaces
#XFLD default business name of a new package
NewPackage_BusinessName=Paket 1
#XFLD default technical name of a new package
NewPackage_TechnicalName=Paket_1
#XFLD default name of a new package displayed in breadcrumb
Breadcrumb_NewPackage_Name=Neues Paket
#XFLD title of object selection dialog
ObjectsDialog_Title=Objekte hinzufügen
#XMSG dependencies are fully resolved
Dependency_Resolved=Bereit zum Hinzufügen (alle Abhängigkeiten gelöst ({0}))
#XMSG dependencies are partially resolved
Dependency_Partial_Resolved=Bereit zum Hinzufügen (einige Abhängigkeiten nicht gelöst ({0}/{1}))
#XMSG dependencies are not found
Dependency_Not_Found=Abhängigkeiten nicht gefunden
#XMSG dependencies are in other package
Dependency_In_Other_Package=Hinzufügen nicht möglich: bereits in Paket "{0}" vorhanden
#XMSG dependencies are in other space
Dependency_In_Other_Space=Hinzufügen nicht möglich: in Space {0} vorhanden
#XMSG
Cannot_Add_Managed_Content=Hinzufügen nicht möglich: verwalteter Inhalt
#XMSG
Cannot_Add_Due_To_Dependency_Has_Managed_Content=Hinzufügen nicht möglich: Abhängigkeit enthält verwalteten Inhalt
#XMSG dependencies are in current space
Dependency_In_Current_Package=Bereits in Paket
#XMSG dependencies are in required package
Dependency_In_Required_Package=Bereits in erforderlichem Paket
#XMSG package arelady exists
Package_Duplicate_Name=Paket "{0}" ist bereits im Repository vorhanden. Geben Sie einen anderen Namen ein.
#XMSG package name is required
Package_Name_Missing=Geben Sie einen Paketnamen ein.
#XMSG package version is required
Package_Version_Missing=Geben Sie eine Paketversion ein.
#XMSG package is drafted
Package_Draft_Warning=Entwurfspaket. Wählen Sie "Sichern", um die Abhängigkeiten zu verifizieren und Ihre Änderungen zu bestätigen.
#XFLD title of validation message dialog
Validation_MessageBox_Title=Validierungsmeldungen
#XMSG content message of validation message dialog
Validation_MessageBox_Content="{0}" ist ungültig und kann nicht exportiert werden. Sie müssen alle Objektabhängigkeiten vollständig lösen, indem Sie die erforderlichen Objekte manuell oder über ein erforderliches Paket hinzufügen.
#XBUT save anyway button
Save_Anyway=Dennoch sichern
#XMSG package is valid
Valid_Package_Message=Das Paket ist gültig.
#XMSG
Object_In_Other_Pacakge=Objekt "{0}" ist in Paket "{1}" vorhanden.
#XMSG
Dependent_Object_In_Other_Pacakge=Objekt "{0}" ist in Paket "{1}" vorhanden, das nicht als erforderliches Paket angegeben ist.
#XMSG
Dependent_Object_Missing=Objekt "{0}" ist in Space {1} vorhanden.
#XMSG
Dependent_Missing_Pattern=Objekt "{0}" ist abhängig von:\r\n{1}
#XMSG: error message for saving the package
Save_Error_Message=Paket konnte nicht gesichert werden. {0}
#XFLD title of save busy dialog
Title_Save_Busy_Dialog=Wird gesichert
#XMSG: message of it takes long time to save
Long_Time_Save_Busy_Dialog=Das Sichern braucht länger als üblich. Bitte warten.
#XMSG: erro message of missing dependency
Dependency_Missing=Abhängigkeit fehlt.
#XMSG: erro message of missing dependency
Object_Dependency_Missing=Abhängigkeit von "{0}" fehlt.
#XMSG message of validating busy dialog
Validating_Package=Paket wird validiert
#XFLD title of validating busy dialog
Title_Validating_Busy_Dialog=Wird verarbeitet
#XMSG %%0 is a placeholder, don’t change it
Missing_Dependency=Fehlende Abhängigkeiten %%0
#XMSG %%0 is a placeholder, don’t change it
Circular_Dependency=Zirkuläre Abhängigkeit %%0
#XMSG Object is added to package successfully
Successfully_Add=Hinzugefügt
#XMSG
Circular_Dependency_Detail="{0}" kann nicht gesichert werden, da es eine zirkuläre Abhängigkeit mit dem Paket "{1}" hat.
#XFLD label of searching objects in space
Search_In_Sapce=Suche in: {0}
#XFLD title of deleting package confirm dialog
Package_Deleting_Dialog_Title=Löschen
#XMSG content message of deleting package confirm dialog
Package_Deleting_Dialog_Message=Möchten Sie die ausgewählten Pakete löschen?
#XMSG
Package_Cannot_Delete=Das ausgewählte Paket wird von anderen Paketen erfordert und kann nicht gelöscht werden.
#XMSG
Package_Required_By_Singular=(erfordert von {0} Paket)
#XMSG
Package_Required_By_Plural=(erfordert von {0} Paketen)
#XFLD display name of owner column
owner=Eigentümer
#XFLD title of deleting package error dialog
Deleting_Error_Dialog_Title=Fehler
#XFLD title of deleting package alert dialog
Deleting_Alert_Dialog_Title=Alert
#XBUT
Deleting_Alert_Dialog_Ok=OK
#XFLD
MyContent=Meine Inhalte
#XFLD
NotExported=Nicht exportiert
#XFLD
Exporting=Wird exportiert
#XFLD
Exported=Exportiert
#XFLD
DesignTimeError=Design-Zeitfehler
#XFLD
ExportFailed=Export fehlgeschlagen
#XFLD
Cancelling=Wird abgebrochen
#XFLD
ChangesToExport=Zu exportierende Änderungen
#XMSG
Exporting_Package={0} wird exportiert. Sie erhalten eine Benachrichtigung, wenn der Prozess abgeschlossen ist.
#XFLD: Object type is businessEntityVariant
TypeBusinessEntityVariant=Geschäftsentitätsversion
#XFLD: Object type is businessEntity
TypeBusinessEntity=Geschäftsentität
#XFLD
Tooltip_Add=Hinzufügen
#XFLD
Tooltip_Edit=Bearbeiten
#XFLD
Tooltip_Delete=Löschen
#XFLD
Tooltip_Refresh=Aktualisieren
#XMSG
ExportToOverwrite=Dieses Paket wurde bereits mit der Version {0} in das Content-Netzwerk exportiert. Möchten Sie es überschreiben?
# XFLD Column businessName
Ext_Selection_Col_Name=Name
# XFLD Column Location
Ext_Selection_Col_Location=Speicherort
# XFLD
Col_Name=Name
# XFLD
Col_Description=Beschreibung
# XFLD Label
MoveTo_Label=In Ordner verschieben
#XMIT Add versions menu button text in Data Builder
versions=Versionen
createVersion=Version anlegen
versionHistory=Versionsverlauf
#XMSG
Package_Depends_On_DP=Dieses Paket ist abhängig von Datenprodukten
#XMSG
Package_Depends_On_DP_Warning=Vergewissern Sie sich vor dem Import dieses Pakets, dass die folgenden Datenprodukte für den Ziel-Space verfügbar sind:\r\n\r\n{0}
# XFLD
Export_Confirm_Dailog_Title=Bestätigung
#XMSG
Package_Contains_DataObjects=Dieses Paket enthält die folgenden Objekte, die Daten für den Transport enthalten:\r\n\r\n{0} \r\n\r\nStellen Sie sicher, dass aufgrund des Transports dieser Objekte in Ihr Paket keine personenbezogenen oder sensiblen Daten unbeabsichtigt offengelegt werden.
