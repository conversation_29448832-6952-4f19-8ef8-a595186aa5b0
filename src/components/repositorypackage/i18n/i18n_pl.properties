# https://wiki.wdf.sap.corp/wiki/display/DWAAS/How+to+add+text+classifications+for+translatable+texts
#
# use the following text types for short texts (up to 120 characters)
# XACT Accessibility
# XBUT Button
# XCKL Checkbox
# XCOL Column header
# XFLD Label
# XGRP Group title
# XLNK Hyperlink
# XLOG Log entry
# XLST List box item
# XMIT Menu item
# XMSG Message
# XRBL Radio button
# XSEL Selection
# XTIT Table title
# XTOL Tooltip
#
# for longer texts (more than 120 characters) use
# YINS Instruction
# YMSG Message

#=============from skyline============
#XFLD general tab of package editor
Tab_General=Ogólne
#XFLD objects tab of package editor
Tab_Objects=Obiekty
#XFLD required packages tab of package editor
Tab_RequiredPkgs=Wymagane pakiety
#XFLD label for business name input
BusinessName=Nazwa biznesowa
#XFLD label for technical name input
TechnicalName=Nazwa techniczna
#XFLD label for purpose input
BusinessPurpose=Cel biznesowy
#XTIT title of save dialog
Dilaog_Save=Zapisz
#XBUT save button of save dialog
Btn_Save=Zapisz
#XBUT cancel button of save dialog
Btn_Cancel=Anuluj
#XFLD title of objects section
Objects=Obiekty
#XFLD title of required packages section
RequiredPackages=Wymagane pakiety
#XTIT title of package selection dialog
Dialog_SelectPackages=Wybierz pakiety
#XMSG no data text of package selection dialog
Dilaog_NoDataText=Nie znaleziono pakietów repozytorium
#XMSG message of adding objects
Msg_ObjectAdded=Dodano obiekty: {0}
#XMSG message of removing objects
Msg_ObjectRemoved=Usunięto obiekty: {0}
#XMSG message of adding required packages
Msg_RequiredPackageAdded=Dodano wymagane pakiety: {0}
#XMSG message of removing required packages
Msg_RequiredPackageRemoved=Usunięto wymagane pakiety: {0}
#XBUT add button in toolbar
Toolbar_Btn_Add=Dodaj
#XBUT remove button in toolbar
Toolbar_Btn_Remove=Usuń
#XCOL status column of objects and required packages table
Table_Col_Status=Wynik
#XLNK more link of status details
Status_More_Link=Wyświetl szczegóły...
#XFLD folder name
FolderName=Nazwa
#XFLD folder description
FolderDescription=Opis
#XBUT ok button
Btn_OK=OK
#XFLD title of export location dialog
ExportLocationDialog=Eksport lokalizacji
#XFLD lable of category field
Category=Kategoria
#XFLD label of location field
Location=Lokalizacja docelowa
#XFLD lable of version field
SemanticVersion=Wersja
#XFLD label of current exported version
CurrentExportedPackage=Aktualna wyeksportowana wersja
#XFLD label of status field
ExportStatus=Status
#XFLD tooltip of export button
Tooltip_Export=Eksport
#XFLD tooltip of save button
Tooltip_Save=Zapisz
#XFLD tooltip of validate button
Tooltip_Validate=Waliduj
#XMSG
InvalidVersion=Wprowadź wersję w formie "x.y.z".
#XMSG
VersionLowerThanBefore=Wersja nie może być starsza od poprzedniej wersji.
#XMSG
Empty_Version=Wprowadź wersję pakietu.
#XMSG
Package_Missing_Technical_Name=Wprowadź nazwę techniczną dla pakietu.
#=============from skyline============

#XFLD lable for space combo selection
Spaces=Przestrzeń:
#XFLD section title of landing page
Packages=Pakiety
#XCOL business name column
Table_Col_BusinessName=Nazwa biznesowa
#XCOL technical name column
Table_Col_TechnicalName=Nazwa techniczna
#XCOL sapce column
Table_Col_Space=Przestrzeń
#XCOL create on column
Table_Col_CreatedOn=Data utworzenia
#XCOL entity type column
Table_Col_Type=Typ
#XCOL entity type column
Table_Col_Object_Status=Status
#XMSG message of deleting packages
Msg_PackageRemoved=Usunięte pakiety: {0}
#XMIT menu item of all spaces
Op_AllSpaces=Wszystkie przestrzenie
#XFLD default business name of a new package
NewPackage_BusinessName=Pakiet 1
#XFLD default technical name of a new package
NewPackage_TechnicalName=Package_1
#XFLD default name of a new package displayed in breadcrumb
Breadcrumb_NewPackage_Name=Nowy pakiet
#XFLD title of object selection dialog
ObjectsDialog_Title=Dodaj obiekty
#XMSG dependencies are fully resolved
Dependency_Resolved=Gotowe do dodania (wszystkie zależności zostały rozwiązane ({0}))
#XMSG dependencies are partially resolved
Dependency_Partial_Resolved=Gotowe do dodania (nie rozwiązano niektórych zależności ({0}/{1})
#XMSG dependencies are not found
Dependency_Not_Found=Nie można znaleźć zależności
#XMSG dependencies are in other package
Dependency_In_Other_Package=Nie można dodać: już w pakiecie "{0}"
#XMSG dependencies are in other space
Dependency_In_Other_Space=Nie można dodać: w przestrzeni {0}
#XMSG
Cannot_Add_Managed_Content=Nie można dodać: Zarządzana zawartość
#XMSG
Cannot_Add_Due_To_Dependency_Has_Managed_Content=Nie można dodać: Zależność zawiera zarządzaną zawartość
#XMSG dependencies are in current space
Dependency_In_Current_Package=Już w pakiecie
#XMSG dependencies are in required package
Dependency_In_Required_Package=Już w wymaganym pakiecie
#XMSG package arelady exists
Package_Duplicate_Name=Pakiet "{0}" już istnieje w repozytorium. Wprowadź inną nazwę.
#XMSG package name is required
Package_Name_Missing=Wprowadź nazwę pakietu.
#XMSG package version is required
Package_Version_Missing=Wprowadź wersję pakietu.
#XMSG package is drafted
Package_Draft_Warning=Wersja robocza pakietu. Kliknij opcję "Zapisz", aby zweryfikować zależności i potwierdzić zmiany.
#XFLD title of validation message dialog
Validation_MessageBox_Title=Komunikaty walidacji
#XMSG content message of validation message dialog
Validation_MessageBox_Content=Pakiet "{0}" jest nieprawidłowy i nie można go wyeksportować. Musisz w pełni rozwiązać wszystkie zależności obiektów, dodając ręcznie niezbędne obiekty lub za pośrednictwem wymaganego pakietu.
#XBUT save anyway button
Save_Anyway=Zapisz mimo to
#XMSG package is valid
Valid_Package_Message=Pakiet jest prawidłowy.
#XMSG
Object_In_Other_Pacakge=Obiekt "{0}" jest w pakiecie "{1}".
#XMSG
Dependent_Object_In_Other_Pacakge=Obiekt "{0}" w pakiecie "{1}", który nie został wskazany jako wymagany pakiet.
#XMSG
Dependent_Object_Missing=Obiekt "{0}" w przestrzeni {1}.
#XMSG
Dependent_Missing_Pattern=Obiekt "{0}" zależy od:\r\n{1}
#XMSG: error message for saving the package
Save_Error_Message=Zapisanie pakietu nie powiodło się. {0}
#XFLD title of save busy dialog
Title_Save_Busy_Dialog=Zapisywanie
#XMSG: message of it takes long time to save
Long_Time_Save_Busy_Dialog=Zapisywanie trwa dłużej niż zwykle. Poczekaj.
#XMSG: erro message of missing dependency
Dependency_Missing=Brak zależności.
#XMSG: erro message of missing dependency
Object_Dependency_Missing=Brak zależności "{0}".
#XMSG message of validating busy dialog
Validating_Package=Walidacja pakietu
#XFLD title of validating busy dialog
Title_Validating_Busy_Dialog=Przetwarzanie
#XMSG %%0 is a placeholder, don’t change it
Missing_Dependency=Brak zależności %%0
#XMSG %%0 is a placeholder, don’t change it
Circular_Dependency=Zależność cykliczna %%0
#XMSG Object is added to package successfully
Successfully_Add=Dodano
#XMSG
Circular_Dependency_Detail=Nie można zapisać "{0}", ponieważ ma cykliczną zależność z pakietem "{1}".
#XFLD label of searching objects in space
Search_In_Sapce=Szukaj w: {0}
#XFLD title of deleting package confirm dialog
Package_Deleting_Dialog_Title=Usuń
#XMSG content message of deleting package confirm dialog
Package_Deleting_Dialog_Message=Czy chcesz usunąć wybrane pakiety?
#XMSG
Package_Cannot_Delete=Wybrany pakiet jest wymagany przez inne pakiety i nie można go usunąć.
#XMSG
Package_Required_By_Singular=(wymagany przez {0} pakiet)
#XMSG
Package_Required_By_Plural=(wymagany przez następującą liczbę pakietów: {0})
#XFLD display name of owner column
owner=Właściciel
#XFLD title of deleting package error dialog
Deleting_Error_Dialog_Title=Błąd
#XFLD title of deleting package alert dialog
Deleting_Alert_Dialog_Title=Alarm
#XBUT
Deleting_Alert_Dialog_Ok=OK
#XFLD
MyContent=Moja zawartość
#XFLD
NotExported=Nie wyeksportowano
#XFLD
Exporting=Eksport
#XFLD
Exported=Wyeksportowano
#XFLD
DesignTimeError=Błąd czasu projektowania
#XFLD
ExportFailed=Eksport nie powiódł się
#XFLD
Cancelling=Anulowanie
#XFLD
ChangesToExport=Zmiany do eksportu
#XMSG
Exporting_Package=Eksportowanie {0}. Gdy proces zostanie zakończony, pojawi się odpowiedni komunikat.
#XFLD: Object type is businessEntityVariant
TypeBusinessEntityVariant=Wersja encji biznesowej
#XFLD: Object type is businessEntity
TypeBusinessEntity=Encja biznesowa
#XFLD
Tooltip_Add=Dodaj
#XFLD
Tooltip_Edit=Edytuj
#XFLD
Tooltip_Delete=Usuń
#XFLD
Tooltip_Refresh=Odśwież
#XMSG
ExportToOverwrite=Ten pakiet został już wyeksportowany do sieci zawartości z wersją {0}. Czy chcesz go nadpisać?
# XFLD Column businessName
Ext_Selection_Col_Name=Nazwa
# XFLD Column Location
Ext_Selection_Col_Location=Lokalizacja
# XFLD
Col_Name=Nazwa
# XFLD
Col_Description=Opis
# XFLD Label
MoveTo_Label=Przenoszenie do folderu
#XMIT Add versions menu button text in Data Builder
versions=Wersje
createVersion=Utwórz wersję
versionHistory=Historia wersji
#XMSG
Package_Depends_On_DP=Ten pakiet jest zależny od produktów danych
#XMSG
Package_Depends_On_DP_Warning=Przed zaimportowaniem tego pakietu, upewnij się, że następujące produkty danych są dostępne w przestrzeni docelowej:\r\n\r\n{0}
# XFLD
Export_Confirm_Dailog_Title=Potwierdzenie
#XMSG
Package_Contains_DataObjects=Ten pakiet zawiera następujące obiekty zawierające dane do transportu:\r\n\r\n{0}\r\n\r\nUpewnij się, że żadne dane osobowe ani wrażliwe dane nie zostaną udostępnione w niewłaściwy sposób w wyniku transportu tych obiektów w pakiecie.
