# https://wiki.wdf.sap.corp/wiki/display/DWAAS/How+to+add+text+classifications+for+translatable+texts
#
# use the following text types for short texts (up to 120 characters)
# XACT Accessibility
# XBUT Button
# XCKL Checkbox
# XCOL Column header
# XFLD Label
# XGRP Group title
# XLNK Hyperlink
# XLOG Log entry
# XLST List box item
# XMIT Menu item
# XMSG Message
# XRBL Radio button
# XSEL Selection
# XTIT Table title
# XTOL Tooltip
#
# for longer texts (more than 120 characters) use
# YINS Instruction
# YMSG Message

#=============from skyline============
#XFLD general tab of package editor
Tab_General=General
#XFLD objects tab of package editor
Tab_Objects=Objetos
#XFLD required packages tab of package editor
Tab_RequiredPkgs=Paquetes requeridos
#XFLD label for business name input
BusinessName=Nombre empresarial
#XFLD label for technical name input
TechnicalName=Nombre técnico
#XFLD label for purpose input
BusinessPurpose=Objetivo comercial
#XTIT title of save dialog
Dilaog_Save=Guardar
#XBUT save button of save dialog
Btn_Save=Guardar
#XBUT cancel button of save dialog
Btn_Cancel=Cancelar
#XFLD title of objects section
Objects=Objetos
#XFLD title of required packages section
RequiredPackages=Paquetes requeridos
#XTIT title of package selection dialog
Dialog_SelectPackages=Seleccionar paquetes
#XMSG no data text of package selection dialog
Dilaog_NoDataText=No se ha encontrado ningún paquete del repositorio
#XMSG message of adding objects
Msg_ObjectAdded=Se han añadido {0} objetos
#XMSG message of removing objects
Msg_ObjectRemoved=Se han quitado {0} objetos
#XMSG message of adding required packages
Msg_RequiredPackageAdded=Se han añadido {0} objetos requeridos
#XMSG message of removing required packages
Msg_RequiredPackageRemoved=Se han quitado {0} objetos requeridos
#XBUT add button in toolbar
Toolbar_Btn_Add=Añadir
#XBUT remove button in toolbar
Toolbar_Btn_Remove=Quitar
#XCOL status column of objects and required packages table
Table_Col_Status=Resultado
#XLNK more link of status details
Status_More_Link=Mostrar detalles...
#XFLD folder name
FolderName=Nombre
#XFLD folder description
FolderDescription=Descripción
#XBUT ok button
Btn_OK=OK
#XFLD title of export location dialog
ExportLocationDialog=Ubicación de exportación
#XFLD lable of category field
Category=Categoría
#XFLD label of location field
Location=Ubicación de destino
#XFLD lable of version field
SemanticVersion=Versión
#XFLD label of current exported version
CurrentExportedPackage=Versión exportada actual
#XFLD label of status field
ExportStatus=Estado
#XFLD tooltip of export button
Tooltip_Export=Exportar
#XFLD tooltip of save button
Tooltip_Save=Guardar
#XFLD tooltip of validate button
Tooltip_Validate=Validar
#XMSG
InvalidVersion=Introduzca una versión con el formato "x.y.z".
#XMSG
VersionLowerThanBefore=La versión no puede ser inferior a la anterior.
#XMSG
Empty_Version=Introduzca una versión del paquete.
#XMSG
Package_Missing_Technical_Name=Introduzca un nombre técnico para el paquete.
#=============from skyline============

#XFLD lable for space combo selection
Spaces=Espacio:
#XFLD section title of landing page
Packages=Paquetes
#XCOL business name column
Table_Col_BusinessName=Nombre empresarial
#XCOL technical name column
Table_Col_TechnicalName=Nombre técnico
#XCOL sapce column
Table_Col_Space=Espacio
#XCOL create on column
Table_Col_CreatedOn=Creado el
#XCOL entity type column
Table_Col_Type=Tipo
#XCOL entity type column
Table_Col_Object_Status=Estado
#XMSG message of deleting packages
Msg_PackageRemoved=Se han quitado {0} paquetes
#XMIT menu item of all spaces
Op_AllSpaces=Todos los espacios
#XFLD default business name of a new package
NewPackage_BusinessName=Paquete 1
#XFLD default technical name of a new package
NewPackage_TechnicalName=Paquete_1
#XFLD default name of a new package displayed in breadcrumb
Breadcrumb_NewPackage_Name=Paquete nuevo
#XFLD title of object selection dialog
ObjectsDialog_Title=Añadir objetos
#XMSG dependencies are fully resolved
Dependency_Resolved=Listo para añadir (todas las dependencias están resueltas [{0}])
#XMSG dependencies are partially resolved
Dependency_Partial_Resolved=Listo para añadir (algunas dependencias no están resueltas [{0}/{1}])
#XMSG dependencies are not found
Dependency_Not_Found=No se encuentran las dependencias
#XMSG dependencies are in other package
Dependency_In_Other_Package=No se puede añadir: ya está en el paquete "{0}"
#XMSG dependencies are in other space
Dependency_In_Other_Space=No se puede añadir: en el espacio {0}
#XMSG
Cannot_Add_Managed_Content=No se puede añadir: contenido gestionado
#XMSG
Cannot_Add_Due_To_Dependency_Has_Managed_Content=No se puede añadir: la dependencia tiene contenido gestionado
#XMSG dependencies are in current space
Dependency_In_Current_Package=Ya está en el paquete
#XMSG dependencies are in required package
Dependency_In_Required_Package=Ya está en el paquete requerido
#XMSG package arelady exists
Package_Duplicate_Name=El paquete "{0}" ya existe en el repositorio. Escriba otro nombre.
#XMSG package name is required
Package_Name_Missing=Introduzca el nombre del paquete.
#XMSG package version is required
Package_Version_Missing=Introduzca una versión del paquete.
#XMSG package is drafted
Package_Draft_Warning=El paquete es un borrador. Haga clic en "Guardar" para verificar las dependencias y confirmar las modificaciones.
#XFLD title of validation message dialog
Validation_MessageBox_Title=Mensajes de validación
#XMSG content message of validation message dialog
Validation_MessageBox_Content="{0}" no es válido y no se puede exportar. Debe resolver todas las dependencias de objeto añadiendo los objetos necesarios manualmente o mediante un paquete requerido.
#XBUT save anyway button
Save_Anyway=Guardar de todas formas
#XMSG package is valid
Valid_Package_Message=El paquete es válido.
#XMSG
Object_In_Other_Pacakge=El objeto "{0}" está en el paquete "{1}".
#XMSG
Dependent_Object_In_Other_Pacakge=El objeto "{0}" está en el paquete "{1}", el cual no se ha especificado como paquete requerido.
#XMSG
Dependent_Object_Missing=Objeto "{0}" en espacio {1}.
#XMSG
Dependent_Missing_Pattern=El objeto "{0}" depende de:\r\n{1}
#XMSG: error message for saving the package
Save_Error_Message=No se ha podido guardar el paquete. {0}
#XFLD title of save busy dialog
Title_Save_Busy_Dialog=Guardando
#XMSG: message of it takes long time to save
Long_Time_Save_Busy_Dialog=La operación de guardado está tardando más de lo habitual. Espere un momento.
#XMSG: erro message of missing dependency
Dependency_Missing=Falta la dependencia.
#XMSG: erro message of missing dependency
Object_Dependency_Missing=Falta la dependencia de "{0}".
#XMSG message of validating busy dialog
Validating_Package=Validando el paquete
#XFLD title of validating busy dialog
Title_Validating_Busy_Dialog=En procesamiento
#XMSG %%0 is a placeholder, don’t change it
Missing_Dependency=Faltan dependencias %%0
#XMSG %%0 is a placeholder, don’t change it
Circular_Dependency=Dependencia circular %%0
#XMSG Object is added to package successfully
Successfully_Add=Añadido
#XMSG
Circular_Dependency_Detail="{0}" no se puede guardar porque tiene una dependencia circular con el paquete "{1}".
#XFLD label of searching objects in space
Search_In_Sapce=Búsqueda en: {0}
#XFLD title of deleting package confirm dialog
Package_Deleting_Dialog_Title=Eliminar
#XMSG content message of deleting package confirm dialog
Package_Deleting_Dialog_Message=¿Desea eliminar los paquetes seleccionados?
#XMSG
Package_Cannot_Delete=Otros paquetes requieren el paquete seleccionado y no se puede eliminar.
#XMSG
Package_Required_By_Singular=(Requerido por {0} paquete)
#XMSG
Package_Required_By_Plural=(Requerido por {0} paquetes)
#XFLD display name of owner column
owner=Propietario
#XFLD title of deleting package error dialog
Deleting_Error_Dialog_Title=Error
#XFLD title of deleting package alert dialog
Deleting_Alert_Dialog_Title=Alerta
#XBUT
Deleting_Alert_Dialog_Ok=OK
#XFLD
MyContent=Mi contenido
#XFLD
NotExported=No exportado
#XFLD
Exporting=Exportando
#XFLD
Exported=Exportado
#XFLD
DesignTimeError=Error en tiempo de diseño
#XFLD
ExportFailed=No se ha podido realizar la exportación
#XFLD
Cancelling=Cancelado
#XFLD
ChangesToExport=Modificaciones para exportar
#XMSG
Exporting_Package=Exportando {0}. Se le notificará cuando el proceso haya finalizado.
#XFLD: Object type is businessEntityVariant
TypeBusinessEntityVariant=Versión de entidad empresarial
#XFLD: Object type is businessEntity
TypeBusinessEntity=Entidad empresarial
#XFLD
Tooltip_Add=Añadir
#XFLD
Tooltip_Edit=Editar
#XFLD
Tooltip_Delete=Eliminar
#XFLD
Tooltip_Refresh=Actualizar
#XMSG
ExportToOverwrite=Este paquete ya se ha exportado a la Red de contenidos con la versión {0}. ¿Desea sobrescribirlo?
# XFLD Column businessName
Ext_Selection_Col_Name=Nombre
# XFLD Column Location
Ext_Selection_Col_Location=Ubicación
# XFLD
Col_Name=Nombre
# XFLD
Col_Description=Descripción
# XFLD Label
MoveTo_Label=Mover a carpeta
#XMIT Add versions menu button text in Data Builder
versions=Versiones
createVersion=Crear versión
versionHistory=Historial de versiones
#XMSG
Package_Depends_On_DP=Este paquete depende de los productos de datos
#XMSG
Package_Depends_On_DP_Warning=Antes de importar este paquete, asegúrese de que los siguientes productos de datos estén disponibles para el espacio de destino:\r\n\r\n{0}
# XFLD
Export_Confirm_Dailog_Title=Confirmación
#XMSG
Package_Contains_DataObjects=Este paquete incluye los siguientes objetos que contienen datos para transporte:\r\n\r\n{0} \r\n\r\nAsegúrese de que no se expongan incorrectamente datos personales o confidenciales como resultado del transporte de estos objetos en el paquete.
