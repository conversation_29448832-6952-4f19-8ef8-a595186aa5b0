# https://wiki.wdf.sap.corp/wiki/display/DWAAS/How+to+add+text+classifications+for+translatable+texts
#
# use the following text types for short texts (up to 120 characters)
# XACT Accessibility
# XBUT Button
# XCKL Checkbox
# XCOL Column header
# XFLD Label
# XGRP Group title
# XLNK Hyperlink
# XLOG Log entry
# XLST List box item
# XMIT Menu item
# XMSG Message
# XRBL Radio button
# XSEL Selection
# XTIT Table title
# XTOL Tooltip
#
# for longer texts (more than 120 characters) use
# YINS Instruction
# YMSG Message

#=============from skyline============
#XFLD general tab of package editor
Tab_General=Generalități
#XFLD objects tab of package editor
Tab_Objects=Obiecte
#XFLD required packages tab of package editor
Tab_RequiredPkgs=Pachete necesare
#XFLD label for business name input
BusinessName=Nume comercial
#XFLD label for technical name input
TechnicalName=Nume tehnic
#XFLD label for purpose input
BusinessPurpose=Obiectiv comercial
#XTIT title of save dialog
Dilaog_Save=Salvare
#XBUT save button of save dialog
Btn_Save=Salvare
#XBUT cancel button of save dialog
Btn_Cancel=Anulare
#XFLD title of objects section
Objects=Obiecte
#XFLD title of required packages section
RequiredPackages=Pachete necesare
#XTIT title of package selection dialog
Dialog_SelectPackages=Selectare pachete
#XMSG no data text of package selection dialog
Dilaog_NoDataText=Niciun pachet de registre găsit
#XMSG message of adding objects
Msg_ObjectAdded={0} obiecte adăugate
#XMSG message of removing objects
Msg_ObjectRemoved={0} obiecte eliminate
#XMSG message of adding required packages
Msg_RequiredPackageAdded={0} pachete necesare adăugate
#XMSG message of removing required packages
Msg_RequiredPackageRemoved={0} pachete necesare eliminate
#XBUT add button in toolbar
Toolbar_Btn_Add=Adăugare
#XBUT remove button in toolbar
Toolbar_Btn_Remove=Eliminare
#XCOL status column of objects and required packages table
Table_Col_Status=Rezultat
#XLNK more link of status details
Status_More_Link=Afișare detalii...
#XFLD folder name
FolderName=Nume
#XFLD folder description
FolderDescription=Descriere
#XBUT ok button
Btn_OK=OK
#XFLD title of export location dialog
ExportLocationDialog=Locație de export
#XFLD lable of category field
Category=Categorie
#XFLD label of location field
Location=Locație țintă
#XFLD lable of version field
SemanticVersion=Versiune
#XFLD label of current exported version
CurrentExportedPackage=Versiune exportă curentă
#XFLD label of status field
ExportStatus=Stare
#XFLD tooltip of export button
Tooltip_Export=Exportare
#XFLD tooltip of save button
Tooltip_Save=Salvare
#XFLD tooltip of validate button
Tooltip_Validate=Validare
#XMSG
InvalidVersion=Introduceți o versiune în forma "x.y.z".
#XMSG
VersionLowerThanBefore=Versiunea nu poate fi inferioară versiunii anterioare.
#XMSG
Empty_Version=Introduceți o versiune de pachet.
#XMSG
Package_Missing_Technical_Name=Introduceți un nume tehnic pentru pachet.
#=============from skyline============

#XFLD lable for space combo selection
Spaces=Spațiu:
#XFLD section title of landing page
Packages=Pachete
#XCOL business name column
Table_Col_BusinessName=Nume comercial
#XCOL technical name column
Table_Col_TechnicalName=Nume tehnic
#XCOL sapce column
Table_Col_Space=Spațiu
#XCOL create on column
Table_Col_CreatedOn=Creat pe
#XCOL entity type column
Table_Col_Type=Tip
#XCOL entity type column
Table_Col_Object_Status=Stare
#XMSG message of deleting packages
Msg_PackageRemoved={0} pachete eliminate
#XMIT menu item of all spaces
Op_AllSpaces=Toate spațiile
#XFLD default business name of a new package
NewPackage_BusinessName=Pachet 1
#XFLD default technical name of a new package
NewPackage_TechnicalName=Pachet_1
#XFLD default name of a new package displayed in breadcrumb
Breadcrumb_NewPackage_Name=Pachet nou
#XFLD title of object selection dialog
ObjectsDialog_Title=Adăugare obiecte
#XMSG dependencies are fully resolved
Dependency_Resolved=Pregătit de adăugat (toate dependențele rezolvate ({0}))
#XMSG dependencies are partially resolved
Dependency_Partial_Resolved=Pregătit de adăugat (unele dependențe nu au fost rezolvate ({0}/{1}))
#XMSG dependencies are not found
Dependency_Not_Found=Dependențele nu pot fi adăugate
#XMSG dependencies are in other package
Dependency_In_Other_Package=Imposibil de adăugat: este deja în pachetul "{0}"
#XMSG dependencies are in other space
Dependency_In_Other_Space=Imposibil de adăugat: în spațiul {0}
#XMSG
Cannot_Add_Managed_Content=Imposibil de adăugat: conținut gestionat
#XMSG
Cannot_Add_Due_To_Dependency_Has_Managed_Content=Imposibil de adăugat: dependență are conținut gestionat
#XMSG dependencies are in current space
Dependency_In_Current_Package=Deja în pachet
#XMSG dependencies are in required package
Dependency_In_Required_Package=Deja în pachet necesar
#XMSG package arelady exists
Package_Duplicate_Name=Pachetul "{0}" există deja în registru. Introduceți alt nume.
#XMSG package name is required
Package_Name_Missing=Introduceți un nume de pachet.
#XMSG package version is required
Package_Version_Missing=Introduceți o versiune de pachet.
#XMSG package is drafted
Package_Draft_Warning=Pachet în versiune preliminară. Efectuați click pe "Salvare" pentru a verifica dependențele și a confirma modificările dvs.
#XFLD title of validation message dialog
Validation_MessageBox_Title=Mesaje de validare
#XMSG content message of validation message dialog
Validation_MessageBox_Content="{0}" este nevalabil și nu poate fi exportat. Mai întâi, trebuie să rezolvați complet toate dependențele de obiect prin adăugarea manuală a obiectelor necesare sau printr-un pachet necesar.
#XBUT save anyway button
Save_Anyway=Salvare în orice caz
#XMSG package is valid
Valid_Package_Message=Pachetul este valabil.
#XMSG
Object_In_Other_Pacakge=Obiectul "{0}" este în pachetul "{1}".
#XMSG
Dependent_Object_In_Other_Pacakge=Obiectul "{0}" din pachetul "{1}", care nu este specificat ca pachet obligatoriu.
#XMSG
Dependent_Object_Missing=Obiectul "{0}" din spațiul {1}.
#XMSG
Dependent_Missing_Pattern=Obiectul "{0}" depinde de:\r\n{1}
#XMSG: error message for saving the package
Save_Error_Message=Eroare la salvare pachet. {0}
#XFLD title of save busy dialog
Title_Save_Busy_Dialog=Salvare în curs
#XMSG: message of it takes long time to save
Long_Time_Save_Busy_Dialog=Salvarea durează mai mult decât de obicei. Așteptați.
#XMSG: erro message of missing dependency
Dependency_Missing=Dependența lipsește.
#XMSG: erro message of missing dependency
Object_Dependency_Missing=Dependența "{0}" lipsește.
#XMSG message of validating busy dialog
Validating_Package=Validare pachet...
#XFLD title of validating busy dialog
Title_Validating_Busy_Dialog=Prelucrare
#XMSG %%0 is a placeholder, don’t change it
Missing_Dependency=Dependențe lipsă %%0
#XMSG %%0 is a placeholder, don’t change it
Circular_Dependency=Dependență circulară %%0
#XMSG Object is added to package successfully
Successfully_Add=Adăugat
#XMSG
Circular_Dependency_Detail="{0}" nu poate fi salvată, deoarece are o dependență circulară cu pachetul "{1}".
#XFLD label of searching objects in space
Search_In_Sapce=Căutare în: {0}
#XFLD title of deleting package confirm dialog
Package_Deleting_Dialog_Title=Ștergere
#XMSG content message of deleting package confirm dialog
Package_Deleting_Dialog_Message=Doriți să ștergeți pachetele selectate?
#XMSG
Package_Cannot_Delete=Pachetul selectat este solicitat de alte pachete și nu poate fi șters.
#XMSG
Package_Required_By_Singular=(solicitat de {0} pachet)
#XMSG
Package_Required_By_Plural=(solicitat de {0} pachete)
#XFLD display name of owner column
owner=Proprietar
#XFLD title of deleting package error dialog
Deleting_Error_Dialog_Title=Eroare
#XFLD title of deleting package alert dialog
Deleting_Alert_Dialog_Title=Alarmă
#XBUT
Deleting_Alert_Dialog_Ok=OK
#XFLD
MyContent=Conținutul meu
#XFLD
NotExported=Neexportat
#XFLD
Exporting=Exportare...
#XFLD
Exported=Exportat
#XFLD
DesignTimeError=Eroare de timp de design
#XFLD
ExportFailed=Export nereușit
#XFLD
Cancelling=Anulare...
#XFLD
ChangesToExport=Modificări pentru export
#XMSG
Exporting_Package=Exportare {0} în curs. Vă vom notifica când procesul este terminat.
#XFLD: Object type is businessEntityVariant
TypeBusinessEntityVariant=Versiune entitate de afaceri
#XFLD: Object type is businessEntity
TypeBusinessEntity=Entitate de afaceri
#XFLD
Tooltip_Add=Adăugare
#XFLD
Tooltip_Edit=Editare
#XFLD
Tooltip_Delete=Ștergere
#XFLD
Tooltip_Refresh=Împrospătare
#XMSG
ExportToOverwrite=Acest pachet a fost deja exportat în rețeaua de conținut cu versiunea {0}. Doriți să îl suprascrieți?
# XFLD Column businessName
Ext_Selection_Col_Name=Nume
# XFLD Column Location
Ext_Selection_Col_Location=Locație
# XFLD
Col_Name=Nume
# XFLD
Col_Description=Descriere
# XFLD Label
MoveTo_Label=Deplasare în folder
#XMIT Add versions menu button text in Data Builder
versions=Versiuni
createVersion=Creare versiune
versionHistory=Istoric versiuni
#XMSG
Package_Depends_On_DP=Acest pachet depinde de produse de date
#XMSG
Package_Depends_On_DP_Warning=Înainte de a importa acest pachet, asigurați-vă că următoarele produse de date sunt disponibile pentru spațiul țintă:\r\n\r\n{0}
# XFLD
Export_Confirm_Dailog_Title=Confirmare
#XMSG
Package_Contains_DataObjects=Acest pachet include următoarele obiecte care conțin date pentru transport:\r\n\r\n{0} \r\n\r\nAsigurați-vă că nu vor fi expuse necorespunzător date personale sau sensibile ca urmare a transportării acestor obiecte în pachetul dvs.
