# https://wiki.wdf.sap.corp/wiki/display/DWAAS/How+to+add+text+classifications+for+translatable+texts
#
# use the following text types for short texts (up to 120 characters)
# XACT Accessibility
# XBUT Button
# XCKL Checkbox
# XCOL Column header
# XFLD Label
# XGRP Group title
# XLNK Hyperlink
# XLOG Log entry
# XLST List box item
# XMIT Menu item
# XMSG Message
# XRBL Radio button
# XSEL Selection
# XTIT Table title
# XTOL Tooltip
#
# for longer texts (more than 120 characters) use
# YINS Instruction
# YMSG Message

#=============from skyline============
#XFLD general tab of package editor
Tab_General=[[[Ģēŋēŗąĺ∙∙∙∙∙∙∙]]]
#XFLD objects tab of package editor
Tab_Objects=[[[Ŏƃĵēċţş∙∙∙∙∙∙∙]]]
#XFLD required packages tab of package editor
Tab_RequiredPkgs=[[[Řēƣűįŗēƌ Ƥąċķąğēş∙∙∙∙∙∙∙]]]
#XFLD label for business name input
BusinessName=[[[Ɓűşįŋēşş Ńąɱē∙∙∙∙∙∙]]]
#XFLD label for technical name input
TechnicalName=[[[Ţēċĥŋįċąĺ Ńąɱē∙∙∙∙∙]]]
#XFLD label for purpose input
BusinessPurpose=[[[Ɓűşįŋēşş Ƥűŗρŏşē∙∙∙∙∙∙∙∙]]]
#XTIT title of save dialog
Dilaog_Save=[[[Ŝąʋē]]]
#XBUT save button of save dialog
Btn_Save=[[[Ŝąʋē]]]
#XBUT cancel button of save dialog
Btn_Cancel=[[[Ĉąŋċēĺ∙∙∙∙∙∙∙∙]]]
#XFLD title of objects section
Objects=[[[Ŏƃĵēċţş∙∙∙∙∙∙∙]]]
#XFLD title of required packages section
RequiredPackages=[[[Řēƣűįŗēƌ Ƥąċķąğēş∙∙∙∙∙∙∙]]]
#XTIT title of package selection dialog
Dialog_SelectPackages=[[[Ŝēĺēċţ Ƥąċķąğēş∙∙∙∙]]]
#XMSG no data text of package selection dialog
Dilaog_NoDataText=[[[Ńŏ Řēρŏşįţŏŗŷ Ƥąċķąğēş Ƒŏűŋƌ∙∙∙∙∙∙∙∙]]]
#XMSG message of adding objects
Msg_ObjectAdded=[[[{0} ŏƃĵēċţş ąƌƌēƌ]]]
#XMSG message of removing objects
Msg_ObjectRemoved=[[[{0} ŏƃĵēċţş ŗēɱŏʋēƌ]]]
#XMSG message of adding required packages
Msg_RequiredPackageAdded=[[[{0} ŗēƣűįŗēƌ ρąċķąğēş ąƌƌēƌ]]]
#XMSG message of removing required packages
Msg_RequiredPackageRemoved=[[[{0} ŗēƣűįŗēƌ ρąċķąğēş ŗēɱŏʋēƌ]]]
#XBUT add button in toolbar
Toolbar_Btn_Add=[[[Āƌƌ∙]]]
#XBUT remove button in toolbar
Toolbar_Btn_Remove=[[[Řēɱŏʋē∙∙∙∙∙∙∙∙]]]
#XCOL status column of objects and required packages table
Table_Col_Status=[[[Řēşűĺţ∙∙∙∙∙∙∙∙]]]
#XLNK more link of status details
Status_More_Link=[[[Ŝĥŏŵ ƌēţąįĺş...∙∙∙∙]]]
#XFLD folder name
FolderName=[[[Ńąɱē]]]
#XFLD folder description
FolderDescription=[[[Ďēşċŗįρţįŏŋ∙∙∙∙∙∙∙∙]]]
#XBUT ok button
Btn_OK=[[[ŎĶ∙∙]]]
#XFLD title of export location dialog
ExportLocationDialog=[[[Ĕχρŏŗţ Ļŏċąţįŏŋ∙∙∙∙]]]
#XFLD lable of category field
Category=[[[Ĉąţēğŏŗŷ∙∙∙∙∙∙]]]
#XFLD label of location field
Location=[[[Ţąŗğēţ Ļŏċąţįŏŋ∙∙∙∙]]]
#XFLD lable of version field
SemanticVersion=[[[Ʋēŗşįŏŋ∙∙∙∙∙∙∙]]]
#XFLD label of current exported version
CurrentExportedPackage=[[[Ĉűŗŗēŋţ Ĕχρŏŗţēƌ Ʋēŗşįŏŋ∙∙∙∙∙∙]]]
#XFLD label of status field
ExportStatus=[[[Ŝţąţűş∙∙∙∙∙∙∙∙]]]
#XFLD tooltip of export button
Tooltip_Export=[[[Ĕχρŏŗţ∙∙∙∙∙∙∙∙]]]
#XFLD tooltip of save button
Tooltip_Save=[[[Ŝąʋē]]]
#XFLD tooltip of validate button
Tooltip_Validate=[[[Ʋąĺįƌąţē∙∙∙∙∙∙]]]
#XMSG
InvalidVersion=[[[Ĕŋţēŗ ą ʋēŗşįŏŋ įŋ ţĥē ƒŏŗɱ "χ.ŷ.ž".∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
VersionLowerThanBefore=[[[Ʋēŗşįŏŋ ċąŋŋŏţ ƃē ĺŏŵēŗ ţĥąŋ ρŗēʋįŏűş ʋēŗşįŏŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
Empty_Version=[[[Ĕŋţēŗ ą ρąċķąğē ʋēŗşįŏŋ.∙∙∙∙∙∙]]]
#XMSG
Package_Missing_Technical_Name=[[[Ĕŋţēŗ ą ţēċĥŋįċąĺ ŋąɱē ƒŏŗ ţĥē ρąċķąğē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#=============from skyline============

#XFLD lable for space combo selection
Spaces=[[[Ŝρąċē:∙∙∙∙∙∙∙∙]]]
#XFLD section title of landing page
Packages=[[[Ƥąċķąğēş∙∙∙∙∙∙]]]
#XCOL business name column
Table_Col_BusinessName=[[[Ɓűşįŋēşş Ńąɱē∙∙∙∙∙∙]]]
#XCOL technical name column
Table_Col_TechnicalName=[[[Ţēċĥŋįċąĺ Ńąɱē∙∙∙∙∙]]]
#XCOL sapce column
Table_Col_Space=[[[Ŝρąċē∙∙∙∙∙∙∙∙∙]]]
#XCOL create on column
Table_Col_CreatedOn=[[[Ĉŗēąţēƌ Ŏŋ∙∙∙∙]]]
#XCOL entity type column
Table_Col_Type=[[[Ţŷρē]]]
#XCOL entity type column
Table_Col_Object_Status=[[[Ŝţąţűş∙∙∙∙∙∙∙∙]]]
#XMSG message of deleting packages
Msg_PackageRemoved=[[[{0} ρąċķąğēş ŗēɱŏʋēƌ]]]
#XMIT menu item of all spaces
Op_AllSpaces=[[[Āĺĺ Ŝρąċēş∙∙∙∙]]]
#XFLD default business name of a new package
NewPackage_BusinessName=[[[Ƥąċķąğē 1∙∙∙∙∙]]]
#XFLD default technical name of a new package
NewPackage_TechnicalName=[[[Ƥąċķąğē_1∙∙∙∙∙]]]
#XFLD default name of a new package displayed in breadcrumb
Breadcrumb_NewPackage_Name=[[[Ńēŵ Ƥąċķąğē∙∙∙∙∙∙∙∙]]]
#XFLD title of object selection dialog
ObjectsDialog_Title=[[[Āƌƌ Ŏƃĵēċţş∙∙∙∙∙∙∙∙]]]
#XMSG dependencies are fully resolved
Dependency_Resolved=[[[Řēąƌŷ ţŏ Āƌƌ (Āĺĺ Ďēρēŋƌēŋċįēş Řēşŏĺʋēƌ ({0}))]]]
#XMSG dependencies are partially resolved
Dependency_Partial_Resolved=[[[Řēąƌŷ ţŏ Āƌƌ (Ŝŏɱē Ďēρēŋƌēŋċįēş Ńŏţ Řēşŏĺʋēƌ ({0}/{1})]]]
#XMSG dependencies are not found
Dependency_Not_Found=[[[Ďēρēŋƌēŋċįēş ċąŋŋŏţ ƃē ƒŏűŋƌ∙∙∙∙∙∙∙∙]]]
#XMSG dependencies are in other package
Dependency_In_Other_Package=[[[Ĉąŋŋŏţ Āƌƌ: Āĺŗēąƌŷ įŋ Ƥąċķąğē "{0}"]]]
#XMSG dependencies are in other space
Dependency_In_Other_Space=[[[Ĉąŋŋŏţ Āƌƌ: Ĭŋ Ŝρąċē {0}]]]
#XMSG
Cannot_Add_Managed_Content=[[[Ĉąŋŋŏţ Āƌƌ: Μąŋąğēƌ Ĉŏŋţēŋţ∙∙∙∙∙∙∙∙]]]
#XMSG
Cannot_Add_Due_To_Dependency_Has_Managed_Content=[[[Ĉąŋŋŏţ Āƌƌ: Ďēρēŋƌēŋċŷ Ĥąş Μąŋąğēƌ Ĉŏŋţēŋţ∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG dependencies are in current space
Dependency_In_Current_Package=[[[Āĺŗēąƌŷ įŋ Ƥąċķąğē∙∙∙∙∙∙]]]
#XMSG dependencies are in required package
Dependency_In_Required_Package=[[[Āĺŗēąƌŷ įŋ Řēƣűįŗēƌ Ƥąċķąğē∙∙∙∙∙∙∙∙]]]
#XMSG package arelady exists
Package_Duplicate_Name=[[[Ƥąċķąğē "{0}" ąĺŗēąƌŷ ēχįşţş įŋ ţĥē ŗēρŏşįţŏŗŷ. Ƥĺēąşē ēŋţēŗ ąŋŏţĥēŗ ŋąɱē.]]]
#XMSG package name is required
Package_Name_Missing=[[[Ĕŋţēŗ ą ρąċķąğē ŋąɱē.∙∙∙∙∙]]]
#XMSG package version is required
Package_Version_Missing=[[[Ĕŋţēŗ ą ρąċķąğē ʋēŗşįŏŋ.∙∙∙∙∙∙]]]
#XMSG package is drafted
Package_Draft_Warning=[[[Ďŗąƒţ ρąċķąğē. Ĉĺįċķ "Ŝąʋē" ţŏ ʋēŗįƒŷ ƌēρēŋƌēŋċįēş ąŋƌ ċŏŋƒįŗɱ ŷŏűŗ ċĥąŋğēş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD title of validation message dialog
Validation_MessageBox_Title=[[[Ʋąĺįƌąţįŏŋ Μēşşąğēş∙∙∙∙∙]]]
#XMSG content message of validation message dialog
Validation_MessageBox_Content=[[["{0}" įş įŋʋąĺįƌ ąŋƌ ċąŋŋŏţ ƃē ēχρŏŗţēƌ. Ŷŏű ɱűşţ ƒűĺĺŷ ŗēşŏĺʋē ąĺĺ ŏƃĵēċţ ƌēρēŋƌēŋċįēş ƃŷ ąƌƌįŋğ ţĥē ŋēċēşşąŗŷ ŏƃĵēċţş ɱąŋűąĺĺŷ ŏŗ ʋįą ą ŗēƣűįŗēƌ ρąċķąğē.]]]
#XBUT save anyway button
Save_Anyway=[[[Ŝąʋē Āŋŷŵąŷ∙∙∙∙∙∙∙∙]]]
#XMSG package is valid
Valid_Package_Message=[[[Ţĥē ρąċķąğē įş ʋąĺįƌ.∙∙∙∙∙]]]
#XMSG
Object_In_Other_Pacakge=[[[Ŏƃĵēċţ "{0}" įş įŋ ρąċķąğē "{1}".]]]
#XMSG
Dependent_Object_In_Other_Pacakge=[[[Ŏƃĵēċţ "{0}" įŋ ρąċķąğē "{1}", ŵĥįċĥ įş ŋŏţ şρēċįƒįēƌ ąş ą ŗēƣűįŗēƌ ρąċķąğē.]]]
#XMSG
Dependent_Object_Missing=[[[Ŏƃĵēċţ "{0}" įŋ şρąċē {1}.]]]
#XMSG
Dependent_Missing_Pattern=[[[Ŏƃĵēċţ "{0}" ƌēρēŋƌş ŏŋ:\\u0157\\u014B{1}]]]
#XMSG: error message for saving the package
Save_Error_Message=[[[Ƒąįĺēƌ ţŏ şąʋē ţĥē ρąċķąğē. {0}]]]
#XFLD title of save busy dialog
Title_Save_Busy_Dialog=[[[Ŝąʋįŋğ∙∙∙∙∙∙∙∙]]]
#XMSG: message of it takes long time to save
Long_Time_Save_Busy_Dialog=[[[Ŝąʋįŋğ įş ţąķįŋğ ĺŏŋğēŗ ţĥąŋ űşűąĺ. Ƥĺēąşē ŵąįţ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: erro message of missing dependency
Dependency_Missing=[[[Ďēρēŋƌēŋċŷ įş ɱįşşįŋğ.∙∙∙∙∙]]]
#XMSG: erro message of missing dependency
Object_Dependency_Missing=[[[Ďēρēŋƌēŋċŷ ŏƒ "{0}" įş ɱįşşįŋğ.]]]
#XMSG message of validating busy dialog
Validating_Package=[[[Ʋąĺįƌąţįŋğ Ƥąċķąğē∙∙∙∙∙∙]]]
#XFLD title of validating busy dialog
Title_Validating_Busy_Dialog=[[[Ƥŗŏċēşşįŋğ∙∙∙∙]]]
#XMSG %%0 is a placeholder, don’t change it
Missing_Dependency=[[[Μįşşįŋğ ƌēρēŋƌēŋċįēş %%0]]]
#XMSG %%0 is a placeholder, don’t change it
Circular_Dependency=[[[Ĉįŗċűĺąŗ ƌēρēŋƌēŋċŷ %%0]]]
#XMSG Object is added to package successfully
Successfully_Add=[[[Āƌƌēƌ∙∙∙∙∙∙∙∙∙]]]
#XMSG
Circular_Dependency_Detail=[[["{0}" ċąŋŋŏţ ƃē şąʋēƌ ƃēċąűşē įţ ĥąş ą ċįŗċűĺąŗ ƌēρēŋƌēŋċŷ ŵįţĥ ρąċķąğē "{1}".]]]
#XFLD label of searching objects in space
Search_In_Sapce=[[[Ŝēąŗċĥ įŋ: {0}]]]
#XFLD title of deleting package confirm dialog
Package_Deleting_Dialog_Title=[[[Ďēĺēţē∙∙∙∙∙∙∙∙]]]
#XMSG content message of deleting package confirm dialog
Package_Deleting_Dialog_Message=[[[Ďŏ ŷŏű ŵąŋţ ţŏ ƌēĺēţē ţĥē şēĺēċţēƌ ρąċķąğēş?∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
Package_Cannot_Delete=[[[Ţĥē şēĺēċţēƌ ρąċķąğē įş ŗēƣűįŗēƌ ƃŷ ŏţĥēŗ ρąċķąğēş ąŋƌ ċąŋŋŏţ ƃē ƌēĺēţēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
Package_Required_By_Singular=[[[(ŗēƣűįŗēƌ ƃŷ {0} ρąċķąğē)]]]
#XMSG
Package_Required_By_Plural=[[[(ŗēƣűįŗēƌ ƃŷ {0} ρąċķąğēş)]]]
#XFLD display name of owner column
owner=[[[Ŏŵŋēŗ∙∙∙∙∙∙∙∙∙]]]
#XFLD title of deleting package error dialog
Deleting_Error_Dialog_Title=[[[Ĕŗŗŏŗ∙∙∙∙∙∙∙∙∙]]]
#XFLD title of deleting package alert dialog
Deleting_Alert_Dialog_Title=[[[Āĺēŗţ∙∙∙∙∙∙∙∙∙]]]
#XBUT
Deleting_Alert_Dialog_Ok=[[[ŎĶ∙∙]]]
#XFLD
MyContent=[[[Μŷ Ĉŏŋţēŋţ∙∙∙∙]]]
#XFLD
NotExported=[[[Ńŏţ Ĕχρŏŗţēƌ∙∙∙∙∙∙∙]]]
#XFLD
Exporting=[[[Ĕχρŏŗţįŋğ∙∙∙∙∙]]]
#XFLD
Exported=[[[Ĕχρŏŗţēƌ∙∙∙∙∙∙]]]
#XFLD
DesignTimeError=[[[Ďēşįğŋ Ţįɱē Ĕŗŗŏŗ∙∙∙∙∙∙∙]]]
#XFLD
ExportFailed=[[[Ĕχρŏŗţ Ƒąįĺēƌ∙∙∙∙∙∙]]]
#XFLD
Cancelling=[[[Ĉąŋċēĺįŋğ∙∙∙∙∙]]]
#XFLD
ChangesToExport=[[[Ĉĥąŋğēş Ţŏ Ĕχρŏŗţ∙∙∙∙∙∙∙]]]
#XMSG
Exporting_Package=[[[Ĕχρŏŗţįŋğ {0}. Ŵē ŵįĺĺ ŋŏţįƒŷ ŷŏű ŵĥēŋ ţĥē ρŗŏċēşş įş ċŏɱρĺēţē.]]]
#XFLD: Object type is businessEntityVariant
TypeBusinessEntityVariant=[[[Ɓűşįŋēşş Ĕŋţįţŷ Ʋēŗşįŏŋ∙∙∙∙∙∙]]]
#XFLD: Object type is businessEntity
TypeBusinessEntity=[[[Ɓűşįŋēşş Ĕŋţįţŷ∙∙∙∙]]]
#XFLD
Tooltip_Add=[[[Āƌƌ∙]]]
#XFLD
Tooltip_Edit=[[[Ĕƌįţ]]]
#XFLD
Tooltip_Delete=[[[Ďēĺēţē∙∙∙∙∙∙∙∙]]]
#XFLD
Tooltip_Refresh=[[[Řēƒŗēşĥ∙∙∙∙∙∙∙]]]
#XMSG
ExportToOverwrite=[[[Ţĥįş ρąċķąğē ĥąş ąĺŗēąƌŷ ƃēēŋ ēχρŏŗţēƌ ţŏ ţĥē Ĉŏŋţēŋţ Ńēţŵŏŗķ ŵįţĥ ʋēŗşįŏŋ {0}. Ďŏ ŷŏű ŵąŋţ ţŏ ŏʋēŗŵŗįţē įţ?]]]
# XFLD Column businessName
Ext_Selection_Col_Name=[[[Ńąɱē]]]
# XFLD Column Location
Ext_Selection_Col_Location=[[[Ļŏċąţįŏŋ∙∙∙∙∙∙]]]
# XFLD
Col_Name=[[[Ńąɱē]]]
# XFLD
Col_Description=[[[Ďēşċŗįρţįŏŋ∙∙∙∙∙∙∙∙]]]
# XFLD Label
MoveTo_Label=[[[Μŏʋē Ţŏ Ƒŏĺƌēŗ∙∙∙∙∙]]]
#XMIT Add versions menu button text in Data Builder
versions=[[[Ʋēŗşįŏŋş∙∙∙∙∙∙]]]
createVersion=[[[Ĉŗēąţē Ʋēŗşįŏŋ∙∙∙∙∙]]]
versionHistory=[[[Ʋēŗşįŏŋ Ĥįşţŏŗŷ∙∙∙∙]]]
#XMSG
Package_Depends_On_DP=[[[Ţĥįş ρąċķąğē ƌēρēŋƌş ŏŋ ƌąţą ρŗŏƌűċţş∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
Package_Depends_On_DP_Warning=[[[Ɓēƒŏŗē įɱρŏŗţįŋğ ţĥįş ρąċķąğē, ēŋşűŗē ţĥąţ ţĥē ƒŏĺĺŏŵįŋğ ƌąţą ρŗŏƌűċţş ąŗē ąʋąįĺąƃĺē ţŏ ţĥē ţąŗğēţ şρąċē:\\u0157\\u014B\\u0157\\u014B{0}]]]
# XFLD
Export_Confirm_Dailog_Title=[[[Ĉŏŋƒįŗɱąţįŏŋ∙∙∙∙∙∙∙]]]
#XMSG
Package_Contains_DataObjects=[[[Ţĥįş ρąċķąğē įŋċĺűƌēş ţĥē ƒŏĺĺŏŵįŋğ ŏƃĵēċţş ċŏŋţąįŋįŋğ ƌąţą ƒŏŗ ţŗąŋşρŏŗţ:\\u0157\\u014B\\u0157\\u014B{0}\\u0157\\u014B\\u0157\\u014BƤĺēąşē ēŋşűŗē ţĥąţ ŋŏ ρēŗşŏŋąĺ ŏŗ şēŋşįţįʋē ƌąţą ŵįĺĺ ƃē įɱρŗŏρēŗĺŷ ēχρŏşēƌ ąş ą ŗēşűĺţ ŏƒ ţŗąŋşρŏŗţįŋğ ţĥēşē ŏƃĵēċţş įŋ ŷŏűŗ ρąċķąğē.]]]
