# https://wiki.wdf.sap.corp/wiki/display/DWAAS/How+to+add+text+classifications+for+translatable+texts
#
# use the following text types for short texts (up to 120 characters)
# XACT Accessibility
# XBUT Button
# XCKL Checkbox
# XCOL Column header
# XFLD Label
# XGRP Group title
# XLNK Hyperlink
# XLOG Log entry
# XLST List box item
# XMIT Menu item
# XMSG Message
# XRBL Radio button
# XSEL Selection
# XTIT Table title
# XTOL Tooltip
#
# for longer texts (more than 120 characters) use
# YINS Instruction
# YMSG Message

#=============from skyline============
#XFLD general tab of package editor
Tab_General=Chung
#XFLD objects tab of package editor
Tab_Objects=Đối tượng
#XFLD required packages tab of package editor
Tab_RequiredPkgs=Gói bắt buộc
#XFLD label for business name input
BusinessName=Tên doanh nghiệp
#XFLD label for technical name input
TechnicalName=Tên kỹ thuật
#XFLD label for purpose input
BusinessPurpose=Mục đích kinh doanh
#XTIT title of save dialog
Dilaog_Save=Lưu
#XBUT save button of save dialog
Btn_Save=Lưu
#XBUT cancel button of save dialog
Btn_Cancel=Hủy
#XFLD title of objects section
Objects=Đối tượng
#XFLD title of required packages section
RequiredPackages=Gói bắt buộc
#XTIT title of package selection dialog
Dialog_SelectPackages=Chọn gói
#XMSG no data text of package selection dialog
Dilaog_NoDataText=Không tìm thấy gói kho chứa
#XMSG message of adding objects
Msg_ObjectAdded=Đã thêm {0} đối tượng
#XMSG message of removing objects
Msg_ObjectRemoved=Đã loại bỏ {0} đối tượng
#XMSG message of adding required packages
Msg_RequiredPackageAdded=Đã thêm {0} gói bắt buộc
#XMSG message of removing required packages
Msg_RequiredPackageRemoved=Đã loại bỏ {0} gói bắt buộc
#XBUT add button in toolbar
Toolbar_Btn_Add=Thêm
#XBUT remove button in toolbar
Toolbar_Btn_Remove=Loại bỏ
#XCOL status column of objects and required packages table
Table_Col_Status=Kết quả
#XLNK more link of status details
Status_More_Link=Hiển thị chi tiết...
#XFLD folder name
FolderName=Tên
#XFLD folder description
FolderDescription=Mô tả
#XBUT ok button
Btn_OK=OK
#XFLD title of export location dialog
ExportLocationDialog=Xuất địa điểm
#XFLD lable of category field
Category=Danh mục
#XFLD label of location field
Location=Địa điểm đích
#XFLD lable of version field
SemanticVersion=Phiên bản
#XFLD label of current exported version
CurrentExportedPackage=Phiên bản đã xuất hiện tại
#XFLD label of status field
ExportStatus=Trạng thái
#XFLD tooltip of export button
Tooltip_Export=Xuất
#XFLD tooltip of save button
Tooltip_Save=Lưu
#XFLD tooltip of validate button
Tooltip_Validate=Xác thực
#XMSG
InvalidVersion=Nhập phiên bản vào biểu mẫu "x.y.z".
#XMSG
VersionLowerThanBefore=Phiên bản không thể thấp hơn phiên bản trước.
#XMSG
Empty_Version=Nhập một phiên bản gói.
#XMSG
Package_Missing_Technical_Name=Nhập tên kỹ thuật cho gói.
#=============from skyline============

#XFLD lable for space combo selection
Spaces=Vùng dữ liệu:
#XFLD section title of landing page
Packages=Gói
#XCOL business name column
Table_Col_BusinessName=Tên doanh nghiệp
#XCOL technical name column
Table_Col_TechnicalName=Tên kỹ thuật
#XCOL sapce column
Table_Col_Space=Vùng dữ liệu
#XCOL create on column
Table_Col_CreatedOn=Được tạo vào
#XCOL entity type column
Table_Col_Type=Kiểu
#XCOL entity type column
Table_Col_Object_Status=Trạng thái
#XMSG message of deleting packages
Msg_PackageRemoved=Đã loại bỏ {0} gói
#XMIT menu item of all spaces
Op_AllSpaces=Tất cả vùng dữ liệu
#XFLD default business name of a new package
NewPackage_BusinessName=Gói 1
#XFLD default technical name of a new package
NewPackage_TechnicalName=Gói_1
#XFLD default name of a new package displayed in breadcrumb
Breadcrumb_NewPackage_Name=Gói mới
#XFLD title of object selection dialog
ObjectsDialog_Title=Thêm đối tượng
#XMSG dependencies are fully resolved
Dependency_Resolved=Sẵn sàng thêm (Tất cả các phụ thuộc đã được giải quyết ({0}))
#XMSG dependencies are partially resolved
Dependency_Partial_Resolved=Sẵn sàng thêm (Một số phụ thuộc chưa được giải quyết ({0}/{1})
#XMSG dependencies are not found
Dependency_Not_Found=Không thể tìm thấy phụ thuộc
#XMSG dependencies are in other package
Dependency_In_Other_Package=Không thể thêm: Đã có trong gói "{0}"
#XMSG dependencies are in other space
Dependency_In_Other_Space=Không thể thêm: Trong vùng dữ liệu {0}
#XMSG
Cannot_Add_Managed_Content=Không thể thêm: Nội dung được quản lý
#XMSG
Cannot_Add_Due_To_Dependency_Has_Managed_Content=Không thể thêm: Phụ thuộc có nội dung được quản lý
#XMSG dependencies are in current space
Dependency_In_Current_Package=Đã có trong Gói
#XMSG dependencies are in required package
Dependency_In_Required_Package=Đã có trong Gói bắt buộc
#XMSG package arelady exists
Package_Duplicate_Name=Gói "{0}” đã tồn tại trong kho lưu trữ. Vui lòng nhập tên khác.
#XMSG package name is required
Package_Name_Missing=Nhập tên gói.
#XMSG package version is required
Package_Version_Missing=Nhập một phiên bản gói.
#XMSG package is drafted
Package_Draft_Warning=Gói bản nháp. Nhấp vào "Lưu" để xác minh các phụ thuộc và xác nhận các thay đổi của bạn.
#XFLD title of validation message dialog
Validation_MessageBox_Title=Thông báo xác thực
#XMSG content message of validation message dialog
Validation_MessageBox_Content="{0}" không hợp lệ và không thể xuất. Bạn phải giải quyết hoàn toàn tất cả các phụ thuộc của đối tượng bằng cách thêm các đối tượng cần thiết theo cách thủ công hoặc thông qua gói bắt buộc.
#XBUT save anyway button
Save_Anyway=Cứ lưu
#XMSG package is valid
Valid_Package_Message=Gói này hợp lệ.
#XMSG
Object_In_Other_Pacakge=Đối tượng "{0}" trong gói "{1}".
#XMSG
Dependent_Object_In_Other_Pacakge=Đối tượng "{0}" trong gói "{1}" không được chỉ định là gói bắt buộc.
#XMSG
Dependent_Object_Missing=Đối tượng "{0}" trong vùng dữ liệu {1}.
#XMSG
Dependent_Missing_Pattern=Đối tượng "{0}" phụ thuộc vào:\r\n{1}
#XMSG: error message for saving the package
Save_Error_Message=Không thể lưu gói. {0}
#XFLD title of save busy dialog
Title_Save_Busy_Dialog=Lưu
#XMSG: message of it takes long time to save
Long_Time_Save_Busy_Dialog=Lưu mất nhiều thời gian hơn thường lệ. Vui lòng chờ.
#XMSG: erro message of missing dependency
Dependency_Missing=Thiếu phụ thuộc.
#XMSG: erro message of missing dependency
Object_Dependency_Missing=Thiếu phụ thuộc “{0}”.
#XMSG message of validating busy dialog
Validating_Package=Đang xác thực gói
#XFLD title of validating busy dialog
Title_Validating_Busy_Dialog=Đang xử lý
#XMSG %%0 is a placeholder, don’t change it
Missing_Dependency=Thiếu phụ thuộc %%0
#XMSG %%0 is a placeholder, don’t change it
Circular_Dependency=Phụ thuộc tuần hoàn %%0
#XMSG Object is added to package successfully
Successfully_Add=Đã thêm
#XMSG
Circular_Dependency_Detail=Không thể lưu "{0}" vì nó có phụ thuộc tuần hoàn với gói "{1}".
#XFLD label of searching objects in space
Search_In_Sapce=Tìm kiếm trong: {0}
#XFLD title of deleting package confirm dialog
Package_Deleting_Dialog_Title=Xóa
#XMSG content message of deleting package confirm dialog
Package_Deleting_Dialog_Message=Bạn có muốn xóa gói được chọn không?
#XMSG
Package_Cannot_Delete=Gói đã chọn được yêu cầu bởi các gói khác và không thể xóa.
#XMSG
Package_Required_By_Singular=(được yêu cầu bởi {0} gói)
#XMSG
Package_Required_By_Plural=(được yêu cầu bởi {0} gói)
#XFLD display name of owner column
owner=Chủ sở hữu
#XFLD title of deleting package error dialog
Deleting_Error_Dialog_Title=Lỗi
#XFLD title of deleting package alert dialog
Deleting_Alert_Dialog_Title=Cảnh báo
#XBUT
Deleting_Alert_Dialog_Ok=OK
#XFLD
MyContent=Nội dung của tôi
#XFLD
NotExported=Chưa được xuất
#XFLD
Exporting=Đang xuất
#XFLD
Exported=Đã xuất
#XFLD
DesignTimeError=Lỗi thời gian thiết kế
#XFLD
ExportFailed=Không thể xuất
#XFLD
Cancelling=Đang hủy
#XFLD
ChangesToExport=Thay đổi thành Xuất
#XMSG
Exporting_Package=Đang xuất {0}. Chúng tôi sẽ thông báo cho bạn khi quy trình hoàn tất.
#XFLD: Object type is businessEntityVariant
TypeBusinessEntityVariant=Phiên bản thực thể kinh doanh
#XFLD: Object type is businessEntity
TypeBusinessEntity=Thực thể kinh doanh
#XFLD
Tooltip_Add=Thêm
#XFLD
Tooltip_Edit=Hiệu chỉnh
#XFLD
Tooltip_Delete=Xóa
#XFLD
Tooltip_Refresh=Làm mới
#XMSG
ExportToOverwrite=Gói này đã được xuất sang Mạng nội dung với phiên bản {0}. Bạn có muốn ghi đè lên nó không?
# XFLD Column businessName
Ext_Selection_Col_Name=Tên
# XFLD Column Location
Ext_Selection_Col_Location=Địa điểm
# XFLD
Col_Name=Tên
# XFLD
Col_Description=Mô tả
# XFLD Label
MoveTo_Label=Di chuyển đến Thư mục
#XMIT Add versions menu button text in Data Builder
versions=Phiên bản
createVersion=Tạo phiên bản
versionHistory=Lịch sử phiên bản
#XMSG
Package_Depends_On_DP=Gói này phụ thuộc vào các sản phẩm dữ liệu
#XMSG
Package_Depends_On_DP_Warning=Trước khi nhập gói này, hãy đảm bảo rằng các sản phẩm dữ liệu sau đây có sẵn cho vùng dữ liệu đích:\r\n\r\n{0}
# XFLD
Export_Confirm_Dailog_Title=Xác nhận
#XMSG
Package_Contains_DataObjects=Gói này bao gồm các đối tượng sau đây chứa dữ liệu để chuyển:\r\n\r\n{0} \r\n\r\nHãy đảm bảo rằng không có dữ liệu cá nhân hoặc nhạy cảm nào được hiển thị không đúng cách do vận chuyển các đối tượng này trong gói của bạn.
