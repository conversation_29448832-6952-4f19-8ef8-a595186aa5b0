# https://wiki.wdf.sap.corp/wiki/display/DWAAS/How+to+add+text+classifications+for+translatable+texts
#
# use the following text types for short texts (up to 120 characters)
# XACT Accessibility
# XBUT Button
# XCKL Checkbox
# XCOL Column header
# XFLD Label
# XGRP Group title
# XLNK Hyperlink
# XLOG Log entry
# XLST List box item
# XMIT Menu item
# XMSG Message
# XRBL Radio button
# XSEL Selection
# XTIT Table title
# XTOL Tooltip
#
# for longer texts (more than 120 characters) use
# YINS Instruction
# YMSG Message

#=============from skyline============
#XFLD general tab of package editor
Tab_General=일반
#XFLD objects tab of package editor
Tab_Objects=오브젝트
#XFLD required packages tab of package editor
Tab_RequiredPkgs=필수 패키지
#XFLD label for business name input
BusinessName=업무 이름
#XFLD label for technical name input
TechnicalName=기술적 이름
#XFLD label for purpose input
BusinessPurpose=비즈니스 목적
#XTIT title of save dialog
Dilaog_Save=저장
#XBUT save button of save dialog
Btn_Save=저장
#XBUT cancel button of save dialog
Btn_Cancel=취소
#XFLD title of objects section
Objects=오브젝트
#XFLD title of required packages section
RequiredPackages=필수 패키지
#XTIT title of package selection dialog
Dialog_SelectPackages=패키지 선택
#XMSG no data text of package selection dialog
Dilaog_NoDataText=저장소 패키지를 찾을 수 없음
#XMSG message of adding objects
Msg_ObjectAdded={0}개 오브젝트가 추가됨
#XMSG message of removing objects
Msg_ObjectRemoved={0}개 오브젝트가 제거됨
#XMSG message of adding required packages
Msg_RequiredPackageAdded={0}개의 필수 패키지가 추가됨
#XMSG message of removing required packages
Msg_RequiredPackageRemoved={0}개의 필수 패키지가 제거됨
#XBUT add button in toolbar
Toolbar_Btn_Add=추가
#XBUT remove button in toolbar
Toolbar_Btn_Remove=제거
#XCOL status column of objects and required packages table
Table_Col_Status=결과
#XLNK more link of status details
Status_More_Link=세부사항 표시...
#XFLD folder name
FolderName=이름
#XFLD folder description
FolderDescription=설명
#XBUT ok button
Btn_OK=OK
#XFLD title of export location dialog
ExportLocationDialog=엑스포트 위치
#XFLD lable of category field
Category=범주
#XFLD label of location field
Location=대상 위치
#XFLD lable of version field
SemanticVersion=버전
#XFLD label of current exported version
CurrentExportedPackage=현재 엑스포트된 버전
#XFLD label of status field
ExportStatus=상태
#XFLD tooltip of export button
Tooltip_Export=엑스포트
#XFLD tooltip of save button
Tooltip_Save=저장
#XFLD tooltip of validate button
Tooltip_Validate=유효성 확인
#XMSG
InvalidVersion="x.y.z" 형식으로 버전을 입력하십시오.
#XMSG
VersionLowerThanBefore=버전은 이전 버전보다 낮을 수 없습니다.
#XMSG
Empty_Version=패키지 버전을 입력하십시오.
#XMSG
Package_Missing_Technical_Name=패키지의 기술적 이름을 입력하십시오.
#=============from skyline============

#XFLD lable for space combo selection
Spaces=공간:
#XFLD section title of landing page
Packages=패키지
#XCOL business name column
Table_Col_BusinessName=업무 이름
#XCOL technical name column
Table_Col_TechnicalName=기술적 이름
#XCOL sapce column
Table_Col_Space=공간
#XCOL create on column
Table_Col_CreatedOn=생성일
#XCOL entity type column
Table_Col_Type=유형
#XCOL entity type column
Table_Col_Object_Status=상태
#XMSG message of deleting packages
Msg_PackageRemoved={0} 패키지가 제거됨
#XMIT menu item of all spaces
Op_AllSpaces=모든 공간
#XFLD default business name of a new package
NewPackage_BusinessName=패키지 1
#XFLD default technical name of a new package
NewPackage_TechnicalName=Package_1
#XFLD default name of a new package displayed in breadcrumb
Breadcrumb_NewPackage_Name=새 패키지
#XFLD title of object selection dialog
ObjectsDialog_Title=오브젝트 추가
#XMSG dependencies are fully resolved
Dependency_Resolved=추가 가능(모든 종속성이 해결됨({0}))
#XMSG dependencies are partially resolved
Dependency_Partial_Resolved=추가 가능(일부 종속성이 해결되지 않음({0}/{1})
#XMSG dependencies are not found
Dependency_Not_Found=종속성을 찾을 수 없음
#XMSG dependencies are in other package
Dependency_In_Other_Package=추가할 수 없음: 이미 패키지 "{0}"에 있음
#XMSG dependencies are in other space
Dependency_In_Other_Space=추가할 수 없음: {0} 공간 내
#XMSG
Cannot_Add_Managed_Content=추가할 수 없음: 관리 대상 컨텐트
#XMSG
Cannot_Add_Due_To_Dependency_Has_Managed_Content=추가할 수 없음: 종속성에 관리 대상 컨텐트 있음
#XMSG dependencies are in current space
Dependency_In_Current_Package=이미 패키지에 있음
#XMSG dependencies are in required package
Dependency_In_Required_Package=이미 필수 패키지에 있음
#XMSG package arelady exists
Package_Duplicate_Name=패키지 ''{0}''이(가) 저장소에 이미 있습니다. 다른 이름을 입력하십시오.
#XMSG package name is required
Package_Name_Missing=패키지 이름을 입력하십시오.
#XMSG package version is required
Package_Version_Missing=패키지 버전을 입력하십시오.
#XMSG package is drafted
Package_Draft_Warning=드래프트 패키지. 종속성의 유효성을 확인하고 변 사항을 확인하려면 "저장"을 클릭하십시오.
#XFLD title of validation message dialog
Validation_MessageBox_Title=유효성 확인 메시지
#XMSG content message of validation message dialog
Validation_MessageBox_Content="{0}"은(는) 유효하지 않아 엑스포트할 수 없습니다. 필요한 오브젝트를 수동으로 추가하거나 필수 패키지를 통해 모든 오브젝트 종속성을 완전히 해결해야 합니다.
#XBUT save anyway button
Save_Anyway=확인 없이 저장
#XMSG package is valid
Valid_Package_Message=패키지가 유효합니다.
#XMSG
Object_In_Other_Pacakge=오브젝트 "{0}"은(는) "{1}" 패키지에 있습니다.
#XMSG
Dependent_Object_In_Other_Pacakge=필수 패키지로 지정되지 않은 "{1}" 패키지의 "{0}" 오브젝트입니다.
#XMSG
Dependent_Object_Missing={1} 공간의 오브젝트 "{0}"입니다.
#XMSG
Dependent_Missing_Pattern=오브젝트 "{0}"은(는) 다음에 의존함:\r\n{1}
#XMSG: error message for saving the package
Save_Error_Message=패키지를 저장하지 못했습니다. {0}
#XFLD title of save busy dialog
Title_Save_Busy_Dialog=저장 중
#XMSG: message of it takes long time to save
Long_Time_Save_Busy_Dialog=저장 시간이 평소보다 오래 걸리고 있습니다. 기다려 주십시오.
#XMSG: erro message of missing dependency
Dependency_Missing=종속성이 누락되었습니다.
#XMSG: erro message of missing dependency
Object_Dependency_Missing="{0}"의 종속성이 누락되었습니다.
#XMSG message of validating busy dialog
Validating_Package=패키지 유효성 확인 중
#XFLD title of validating busy dialog
Title_Validating_Busy_Dialog=처리 중
#XMSG %%0 is a placeholder, don’t change it
Missing_Dependency=누락 종속성 %%0
#XMSG %%0 is a placeholder, don’t change it
Circular_Dependency=순환 종속성 %%0
#XMSG Object is added to package successfully
Successfully_Add=추가됨
#XMSG
Circular_Dependency_Detail="{0}"은(는) "{1}" 패키지와 순환 종속성을 갖고 있으므로 저장할 수 없습니다.
#XFLD label of searching objects in space
Search_In_Sapce=검색 위치: {0}
#XFLD title of deleting package confirm dialog
Package_Deleting_Dialog_Title=삭제
#XMSG content message of deleting package confirm dialog
Package_Deleting_Dialog_Message=선택된 패키지를 삭제하시겠습니까?
#XMSG
Package_Cannot_Delete=선택한 패키지는 다른 패키지에 필요하므로 삭제할 수 없습니다.
#XMSG
Package_Required_By_Singular=({0} 패키지에 필요)
#XMSG
Package_Required_By_Plural=({0} 패키지에 필요)
#XFLD display name of owner column
owner=소유자
#XFLD title of deleting package error dialog
Deleting_Error_Dialog_Title=오류
#XFLD title of deleting package alert dialog
Deleting_Alert_Dialog_Title=경고
#XBUT
Deleting_Alert_Dialog_Ok=OK
#XFLD
MyContent=내 컨텐트
#XFLD
NotExported=엑스포트되지 않음
#XFLD
Exporting=엑스포트 중
#XFLD
Exported=엑스포트됨
#XFLD
DesignTimeError=디자인 타임 오류
#XFLD
ExportFailed=엑스포트하지 못함
#XFLD
Cancelling=취소 중
#XFLD
ChangesToExport=엑스포트 변경사항
#XMSG
Exporting_Package={0}을(를) 엑스포트하는 중입니다. 프로세스가 완료되면 알림이 표시됩니다.
#XFLD: Object type is businessEntityVariant
TypeBusinessEntityVariant=비즈니스 엔티티 버전
#XFLD: Object type is businessEntity
TypeBusinessEntity=비즈니스 엔티티
#XFLD
Tooltip_Add=추가
#XFLD
Tooltip_Edit=편집
#XFLD
Tooltip_Delete=삭제
#XFLD
Tooltip_Refresh=새로 고침
#XMSG
ExportToOverwrite=이 패키지는 이미 {0} 버전을 사용하여 컨텐트 네트워크로 엑스포트되었습니다. 덮어쓰시겠습니까?
# XFLD Column businessName
Ext_Selection_Col_Name=이름
# XFLD Column Location
Ext_Selection_Col_Location=위치
# XFLD
Col_Name=이름
# XFLD
Col_Description=설명
# XFLD Label
MoveTo_Label=폴더로 이동
#XMIT Add versions menu button text in Data Builder
versions=버전
createVersion=버전 생성
versionHistory=버전 이력
#XMSG
Package_Depends_On_DP=이 패키지는 데이터 제품에 따라 달라집니다.
#XMSG
Package_Depends_On_DP_Warning=이 패키지를 임포트하기 전에 대상 공간에서 다음 데이터 제품을 사용할 수 있는지 확인하십시오.\r\n\r\n{0}
# XFLD
Export_Confirm_Dailog_Title=확인
#XMSG
Package_Contains_DataObjects=이 패키지에는 전송할 데이터가 포함된 다음 오브젝트가 포함되어 있습니다.\r\n\r\n{0} \r\n\r\n이러한 오브젝트를 패키지로 전송하면서 개인 데이터나 민감한 데이터가 부적절하게 노출되지 않도록 하십시오.
