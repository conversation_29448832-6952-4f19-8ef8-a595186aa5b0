# https://wiki.wdf.sap.corp/wiki/display/DWAAS/How+to+add+text+classifications+for+translatable+texts
#
# use the following text types for short texts (up to 120 characters)
# XACT Accessibility
# XBUT Button
# XCKL Checkbox
# XCOL Column header
# XFLD Label
# XGRP Group title
# XLNK Hyperlink
# XLOG Log entry
# XLST List box item
# XMIT Menu item
# XMSG Message
# XRBL Radio button
# XSEL Selection
# XTIT Table title
# XTOL Tooltip
#
# for longer texts (more than 120 characters) use
# YINS Instruction
# YMSG Message

#=============from skyline============
#XFLD general tab of package editor
Tab_General=Všeobecně
#XFLD objects tab of package editor
Tab_Objects=Objekty
#XFLD required packages tab of package editor
Tab_RequiredPkgs=Požadované pakety
#XFLD label for business name input
BusinessName=Business název
#XFLD label for technical name input
TechnicalName=Technický název
#XFLD label for purpose input
BusinessPurpose=Obchodní účel
#XTIT title of save dialog
Dilaog_Save=Uložit
#XBUT save button of save dialog
Btn_Save=Uložit
#XBUT cancel button of save dialog
Btn_Cancel=Zrušit
#XFLD title of objects section
Objects=Objekty
#XFLD title of required packages section
RequiredPackages=Požadované pakety
#XTIT title of package selection dialog
Dialog_SelectPackages=Vybrat pakety
#XMSG no data text of package selection dialog
Dilaog_NoDataText=Žádné pakety na úložišti nenalezeny
#XMSG message of adding objects
Msg_ObjectAdded={0} objekty přidány
#XMSG message of removing objects
Msg_ObjectRemoved={0} objekty odebrány
#XMSG message of adding required packages
Msg_RequiredPackageAdded={0} požadované pakety přidány
#XMSG message of removing required packages
Msg_RequiredPackageRemoved={0} požadované pakety odebrány
#XBUT add button in toolbar
Toolbar_Btn_Add=Přidat
#XBUT remove button in toolbar
Toolbar_Btn_Remove=Odebrat
#XCOL status column of objects and required packages table
Table_Col_Status=Výsledek
#XLNK more link of status details
Status_More_Link=Zobrazit detaily...
#XFLD folder name
FolderName=Název
#XFLD folder description
FolderDescription=Popis
#XBUT ok button
Btn_OK=OK
#XFLD title of export location dialog
ExportLocationDialog=Lokace exportu
#XFLD lable of category field
Category=Kategorie
#XFLD label of location field
Location=Cílová lokace
#XFLD lable of version field
SemanticVersion=Verze
#XFLD label of current exported version
CurrentExportedPackage=Aktuální exportovaná verze
#XFLD label of status field
ExportStatus=Status
#XFLD tooltip of export button
Tooltip_Export=Exportovat
#XFLD tooltip of save button
Tooltip_Save=Uložit
#XFLD tooltip of validate button
Tooltip_Validate=Ověřit
#XMSG
InvalidVersion=Zadejte verzi ve formě "x.y.z".
#XMSG
VersionLowerThanBefore=Verze nemůže být nižší než předchozí verze.
#XMSG
Empty_Version=Zadejte verzi paketu.
#XMSG
Package_Missing_Technical_Name=Zadejte technický název pro paket.
#=============from skyline============

#XFLD lable for space combo selection
Spaces=Prostor:
#XFLD section title of landing page
Packages=Pakety
#XCOL business name column
Table_Col_BusinessName=Business název
#XCOL technical name column
Table_Col_TechnicalName=Technický název
#XCOL sapce column
Table_Col_Space=Prostor
#XCOL create on column
Table_Col_CreatedOn=Vytvořeno dne
#XCOL entity type column
Table_Col_Type=Typ
#XCOL entity type column
Table_Col_Object_Status=Status
#XMSG message of deleting packages
Msg_PackageRemoved={0} pakety odebrány
#XMIT menu item of all spaces
Op_AllSpaces=Všechny prostory
#XFLD default business name of a new package
NewPackage_BusinessName=Paket 1
#XFLD default technical name of a new package
NewPackage_TechnicalName=Paket_1
#XFLD default name of a new package displayed in breadcrumb
Breadcrumb_NewPackage_Name=Nový paket
#XFLD title of object selection dialog
ObjectsDialog_Title=Přidat objekty
#XMSG dependencies are fully resolved
Dependency_Resolved=Připraveno k přidání (všechny závislosti vyřešeny ({0}))
#XMSG dependencies are partially resolved
Dependency_Partial_Resolved=Připraveno k přidání (některé závislosti nevyřešeny ({0}/{1})
#XMSG dependencies are not found
Dependency_Not_Found=Závislosti nelze najít
#XMSG dependencies are in other package
Dependency_In_Other_Package=Nelze přidat: Již je v paketu "{0}"
#XMSG dependencies are in other space
Dependency_In_Other_Space=Nelze přidat: V prostoru {0}
#XMSG
Cannot_Add_Managed_Content=Nelze přidat: Řízený obsah
#XMSG
Cannot_Add_Due_To_Dependency_Has_Managed_Content=Nelze přidat: Závislost má řízený obsah
#XMSG dependencies are in current space
Dependency_In_Current_Package=Již v paketu
#XMSG dependencies are in required package
Dependency_In_Required_Package=Již v požadovaném paketu
#XMSG package arelady exists
Package_Duplicate_Name=Paket ''{0}'' již v úložišti existuje. Zadejte jiný název.
#XMSG package name is required
Package_Name_Missing=Zadejte název paketu.
#XMSG package version is required
Package_Version_Missing=Zadejte verzi paketu.
#XMSG package is drafted
Package_Draft_Warning=Návrh paketu. Kliknutím na „Uložit“ ověříte závislosti a potvrdíte vaše změny.
#XFLD title of validation message dialog
Validation_MessageBox_Title=Ověřovací zprávy
#XMSG content message of validation message dialog
Validation_MessageBox_Content="{0}" je neplatný a nelze jej exportovat. Musíte kompletně vyřešit všechny závislosti objektu manuálním přidáním nutných objektů nebo pomocí požadovaného paketu.
#XBUT save anyway button
Save_Anyway=Přesto uložit
#XMSG package is valid
Valid_Package_Message=Paket je platný.
#XMSG
Object_In_Other_Pacakge=Objekt "{0}" je v paketu "{1}".
#XMSG
Dependent_Object_In_Other_Pacakge=Objekt "{0}" v paketu "{1}", který není specifikován jako požadovaný paket.
#XMSG
Dependent_Object_Missing=Objekt "{0}" v prostoru {1}.
#XMSG
Dependent_Missing_Pattern=Objekt "{0}" závisí na:\r\n{1}
#XMSG: error message for saving the package
Save_Error_Message=Nezdařilo se uložit paket. {0}
#XFLD title of save busy dialog
Title_Save_Busy_Dialog=Ukládání
#XMSG: message of it takes long time to save
Long_Time_Save_Busy_Dialog=Ukládání bude trvat déle než obvykle. Čekejte, prosím.
#XMSG: erro message of missing dependency
Dependency_Missing=Závislost chybí.
#XMSG: erro message of missing dependency
Object_Dependency_Missing=Závislost "{0}" chybí.
#XMSG message of validating busy dialog
Validating_Package=Ověření paketu
#XFLD title of validating busy dialog
Title_Validating_Busy_Dialog=Zpracování
#XMSG %%0 is a placeholder, don’t change it
Missing_Dependency=Chybí závislosti %%0
#XMSG %%0 is a placeholder, don’t change it
Circular_Dependency=Cyklická závislost %%0
#XMSG Object is added to package successfully
Successfully_Add=Přidáno
#XMSG
Circular_Dependency_Detail="{0}" nelze uložit, protože má cyklickou závislost s paketem "{1}".
#XFLD label of searching objects in space
Search_In_Sapce=Hledat v: {0}
#XFLD title of deleting package confirm dialog
Package_Deleting_Dialog_Title=Odstranit
#XMSG content message of deleting package confirm dialog
Package_Deleting_Dialog_Message=Chcete odstranit vybrané pakety?
#XMSG
Package_Cannot_Delete=Vybraný paket je vyžadován jinými pakety a nelze ho odstranit.
#XMSG
Package_Required_By_Singular=(vyžadováno {0} paketem)
#XMSG
Package_Required_By_Plural=(vyžadováno {0} pakety)
#XFLD display name of owner column
owner=Vlastník
#XFLD title of deleting package error dialog
Deleting_Error_Dialog_Title=Chyba
#XFLD title of deleting package alert dialog
Deleting_Alert_Dialog_Title=Výstraha
#XBUT
Deleting_Alert_Dialog_Ok=OK
#XFLD
MyContent=Můj obsah
#XFLD
NotExported=Neexportováno
#XFLD
Exporting=Export
#XFLD
Exported=Exportováno
#XFLD
DesignTimeError=Chyba doby návrhu
#XFLD
ExportFailed=Export neúspěšný
#XFLD
Cancelling=Zrušení
#XFLD
ChangesToExport=Změny k exportu
#XMSG
Exporting_Package=Exportuje se {0}. O dokončení tohoto procesu vás budeme informovat.
#XFLD: Object type is businessEntityVariant
TypeBusinessEntityVariant=Verze business entity
#XFLD: Object type is businessEntity
TypeBusinessEntity=Business entita
#XFLD
Tooltip_Add=Přidat
#XFLD
Tooltip_Edit=Upravit
#XFLD
Tooltip_Delete=Odstranit
#XFLD
Tooltip_Refresh=Aktualizovat
#XMSG
ExportToOverwrite=Tento paket již byl exportován do obsahové sítě s verzí {0}. Chcete ho přepsat?
# XFLD Column businessName
Ext_Selection_Col_Name=Název
# XFLD Column Location
Ext_Selection_Col_Location=Lokace
# XFLD
Col_Name=Název
# XFLD
Col_Description=Popis
# XFLD Label
MoveTo_Label=Přesunout do složky
#XMIT Add versions menu button text in Data Builder
versions=Verze
createVersion=Vytvořit verzi
versionHistory=Historie verze
#XMSG
Package_Depends_On_DP=Tento paket závisí na datových produktech
#XMSG
Package_Depends_On_DP_Warning=Před importem tohoto paketu zajistěte, aby byly pro cílový prostor dostupné následující datové produkty:\r\n\r\n{0}
# XFLD
Export_Confirm_Dailog_Title=Potvrzení
#XMSG
Package_Contains_DataObjects=Tento balíček obsahuje následující objekty obsahující data k přenosu:\r\n\r\n{0} \r\n\r\nV důsledku transportu těchto objektů ve vašem balíčku zajistěte, aby nebyla správně vystavena žádná osobní nebo citlivá data.
