# https://wiki.wdf.sap.corp/wiki/display/DWAAS/How+to+add+text+classifications+for+translatable+texts
#
# use the following text types for short texts (up to 120 characters)
# XACT Accessibility
# XBUT Button
# XCKL Checkbox
# XCOL Column header
# XFLD Label
# XGRP Group title
# XLNK Hyperlink
# XLOG Log entry
# XLST List box item
# XMIT Menu item
# XMSG Message
# XRBL Radio button
# XSEL Selection
# XTIT Table title
# XTOL Tooltip
#
# for longer texts (more than 120 characters) use
# YINS Instruction
# YMSG Message

#=============from skyline============
#XFLD general tab of package editor
Tab_General=Общи
#XFLD objects tab of package editor
Tab_Objects=Обекти
#XFLD required packages tab of package editor
Tab_RequiredPkgs=Задължителни пакети
#XFLD label for business name input
BusinessName=Бизнес наименование
#XFLD label for technical name input
TechnicalName=Техническо име
#XFLD label for purpose input
BusinessPurpose=Бизнес цел
#XTIT title of save dialog
Dilaog_Save=Запазване
#XBUT save button of save dialog
Btn_Save=Запазване
#XBUT cancel button of save dialog
Btn_Cancel=Отказ
#XFLD title of objects section
Objects=Обекти
#XFLD title of required packages section
RequiredPackages=Задължителни пакети
#XTIT title of package selection dialog
Dialog_SelectPackages=Избор на пакети
#XMSG no data text of package selection dialog
Dilaog_NoDataText=Не са намерени пакети на репозитара
#XMSG message of adding objects
Msg_ObjectAdded=Добавена са {0} обекта
#XMSG message of removing objects
Msg_ObjectRemoved=Премахнати са {0} обекта
#XMSG message of adding required packages
Msg_RequiredPackageAdded=Добавени са {0} задължителни пакета
#XMSG message of removing required packages
Msg_RequiredPackageRemoved=Премахнати са {0} задължителни пакета
#XBUT add button in toolbar
Toolbar_Btn_Add=Добавяне
#XBUT remove button in toolbar
Toolbar_Btn_Remove=Премахване
#XCOL status column of objects and required packages table
Table_Col_Status=Резултат
#XLNK more link of status details
Status_More_Link=Показване на подробни данни...
#XFLD folder name
FolderName=Име
#XFLD folder description
FolderDescription=Описание
#XBUT ok button
Btn_OK=OK
#XFLD title of export location dialog
ExportLocationDialog=Местоположение на експорт
#XFLD lable of category field
Category=Категория
#XFLD label of location field
Location=Местоположение на цел
#XFLD lable of version field
SemanticVersion=Версия
#XFLD label of current exported version
CurrentExportedPackage=Текуща експортирана версия
#XFLD label of status field
ExportStatus=Статус
#XFLD tooltip of export button
Tooltip_Export=Експорт
#XFLD tooltip of save button
Tooltip_Save=Запазване
#XFLD tooltip of validate button
Tooltip_Validate=Проверка
#XMSG
InvalidVersion=Въведете версия във вида "x.y.z".
#XMSG
VersionLowerThanBefore=Версията не може да е по-ниска от предишната.
#XMSG
Empty_Version=Въведете версия на пакета.
#XMSG
Package_Missing_Technical_Name=Въведете техническо име за пакета.
#=============from skyline============

#XFLD lable for space combo selection
Spaces=Пространство:
#XFLD section title of landing page
Packages=Пакети
#XCOL business name column
Table_Col_BusinessName=Бизнес наименование
#XCOL technical name column
Table_Col_TechnicalName=Техническо име
#XCOL sapce column
Table_Col_Space=Пространство
#XCOL create on column
Table_Col_CreatedOn=Създадено на
#XCOL entity type column
Table_Col_Type=Вид
#XCOL entity type column
Table_Col_Object_Status=Статус
#XMSG message of deleting packages
Msg_PackageRemoved=Премахнати са {0} пакета
#XMIT menu item of all spaces
Op_AllSpaces=Всички пространства
#XFLD default business name of a new package
NewPackage_BusinessName=Пакет1
#XFLD default technical name of a new package
NewPackage_TechnicalName=Пакет_1
#XFLD default name of a new package displayed in breadcrumb
Breadcrumb_NewPackage_Name=Нов пакет
#XFLD title of object selection dialog
ObjectsDialog_Title=Добавяне на обекти
#XMSG dependencies are fully resolved
Dependency_Resolved=Готово за добавяне (всички зависимости са решени ({0}))
#XMSG dependencies are partially resolved
Dependency_Partial_Resolved=Готово за добавяне (някои зависимости не са решени ({0}/{1})
#XMSG dependencies are not found
Dependency_Not_Found=Зависимостите не могат да бъдат намерени
#XMSG dependencies are in other package
Dependency_In_Other_Package=Не може да се добави: вече е в пакета „{0}”
#XMSG dependencies are in other space
Dependency_In_Other_Space=Не може да се добави: в пространството {0}
#XMSG
Cannot_Add_Managed_Content=Не може да се добави: управлявано съдържание
#XMSG
Cannot_Add_Due_To_Dependency_Has_Managed_Content=Не може да се добави: зависимостта има управлявано съдържание
#XMSG dependencies are in current space
Dependency_In_Current_Package=Вече е в пакета
#XMSG dependencies are in required package
Dependency_In_Required_Package=Вече е в задължителния пакет
#XMSG package arelady exists
Package_Duplicate_Name=В хранилището вече има пакет „{0}”. Моля, въведете друго име.
#XMSG package name is required
Package_Name_Missing=Въведете име на пакета.
#XMSG package version is required
Package_Version_Missing=Въведете версия на пакета.
#XMSG package is drafted
Package_Draft_Warning=Чернова на пакета. Кликнете върху „Запазване”, за да проверите зависимостите и да потвърдите промените.
#XFLD title of validation message dialog
Validation_MessageBox_Title=Съобщения от проверката
#XMSG content message of validation message dialog
Validation_MessageBox_Content=„{0}” е невалидно и не може да се експортира. Трябва да решите изцяло всички зависимости на обектите, като добавите необходимите обекти ръчно или чрез задължителен пакет.
#XBUT save anyway button
Save_Anyway=Запазване въпреки всичко
#XMSG package is valid
Valid_Package_Message=Пакетът е валиден.
#XMSG
Object_In_Other_Pacakge=Обектът „{0}” е в пакет „{1}”.
#XMSG
Dependent_Object_In_Other_Pacakge=Обект „{0}” в пакета „{1}”, който не е посочен като задълителен пакет.
#XMSG
Dependent_Object_Missing=Обектът „{0}” в пространство {1}.
#XMSG
Dependent_Missing_Pattern=Обектът „{0}” зависи от:\r\n{1}
#XMSG: error message for saving the package
Save_Error_Message=Неуспешно запазване на пакета. {0}
#XFLD title of save busy dialog
Title_Save_Busy_Dialog=Запазване
#XMSG: message of it takes long time to save
Long_Time_Save_Busy_Dialog=Запазването отнема повече време от обичайното. Моля, изчакайте.
#XMSG: erro message of missing dependency
Dependency_Missing=Липсва зависимост.
#XMSG: erro message of missing dependency
Object_Dependency_Missing=Липсва зависимост за „{0}”.
#XMSG message of validating busy dialog
Validating_Package=Проверка на пакета
#XFLD title of validating busy dialog
Title_Validating_Busy_Dialog=Обработва се
#XMSG %%0 is a placeholder, don’t change it
Missing_Dependency=Липсващи зависимости %%0
#XMSG %%0 is a placeholder, don’t change it
Circular_Dependency=Кръгова зависимост %%0
#XMSG Object is added to package successfully
Successfully_Add=Добавенo
#XMSG
Circular_Dependency_Detail=„{0}” не може да се запази, тъй като има кръгова зависимост с пакета „{1}”.
#XFLD label of searching objects in space
Search_In_Sapce=Търсене в: {0}
#XFLD title of deleting package confirm dialog
Package_Deleting_Dialog_Title=Изтриване
#XMSG content message of deleting package confirm dialog
Package_Deleting_Dialog_Message=Желаете ли да изтриете избраните пакети?
#XMSG
Package_Cannot_Delete=Избраният пакет се изисква от други пакети и не може да се изтрива.
#XMSG
Package_Required_By_Singular=(изисквано от {0} пакет)
#XMSG
Package_Required_By_Plural=(изисквано от {0} пакета)
#XFLD display name of owner column
owner=Собственик
#XFLD title of deleting package error dialog
Deleting_Error_Dialog_Title=Грешка
#XFLD title of deleting package alert dialog
Deleting_Alert_Dialog_Title=Предупреждение
#XBUT
Deleting_Alert_Dialog_Ok=OK
#XFLD
MyContent=Моето съдържание
#XFLD
NotExported=Не е експортирано
#XFLD
Exporting=Експортиране
#XFLD
Exported=Експортирано
#XFLD
DesignTimeError=Грешка във време на дизайн
#XFLD
ExportFailed=Неуспешен експорт
#XFLD
Cancelling=Отмяна
#XFLD
ChangesToExport=Промени в експорта
#XMSG
Exporting_Package=Експортираме {0}. Ще ви известим, щом процесът приключи.
#XFLD: Object type is businessEntityVariant
TypeBusinessEntityVariant=Версия на бизнес единица
#XFLD: Object type is businessEntity
TypeBusinessEntity=Бизнес единица
#XFLD
Tooltip_Add=Добавяне
#XFLD
Tooltip_Edit=Редактиране
#XFLD
Tooltip_Delete=Изтриване
#XFLD
Tooltip_Refresh=Опресняване
#XMSG
ExportToOverwrite=Този пакет вече е експортиран в мрежата за съдържание с версия {0}. Искате ли да го заместите?
# XFLD Column businessName
Ext_Selection_Col_Name=Име
# XFLD Column Location
Ext_Selection_Col_Location=Местоположение
# XFLD
Col_Name=Име
# XFLD
Col_Description=Описание
# XFLD Label
MoveTo_Label=Преместване в папка
#XMIT Add versions menu button text in Data Builder
versions=Версии
createVersion=Създаване на версия
versionHistory=История на версия
#XMSG
Package_Depends_On_DP=Този пакет зависи от продукти от данни
#XMSG
Package_Depends_On_DP_Warning=Преди да импортирате този пакет, проверете дали следните продукти от данни са достъпни за целевото пространство:\r\n\r\n{0}
# XFLD
Export_Confirm_Dailog_Title=Потвърждение
#XMSG
Package_Contains_DataObjects=Този пакет включва следните обекти, съдържащи данни за пренос:\r\n\r\n{0} \r\n\r\nМоля, уверете се, че няма да бъдат разкрити неправомерно лични или чувствителни данни в резултат на преноса на тези обекти във вашия пакет.
