# https://wiki.wdf.sap.corp/wiki/display/DWAAS/How+to+add+text+classifications+for+translatable+texts
#
# use the following text types for short texts (up to 120 characters)
# XACT Accessibility
# XBUT Button
# XCKL Checkbox
# XCOL Column header
# XFLD Label
# XGRP Group title
# XLNK Hyperlink
# XLOG Log entry
# XLST List box item
# XMIT Menu item
# XMSG Message
# XRBL Radio button
# XSEL Selection
# XTIT Table title
# XTOL Tooltip
#
# for longer texts (more than 120 characters) use
# YINS Instruction
# YMSG Message

#=============from skyline============
#XFLD general tab of package editor
Tab_General=Genel
#XFLD objects tab of package editor
Tab_Objects=Nesneler
#XFLD required packages tab of package editor
Tab_RequiredPkgs=Gerekli paketler
#XFLD label for business name input
BusinessName=İş adı
#XFLD label for technical name input
TechnicalName=Teknik ad
#XFLD label for purpose input
BusinessPurpose=<PERSON>ş amacı
#XTIT title of save dialog
Dilaog_Save=Kaydet
#XBUT save button of save dialog
Btn_Save=Kaydet
#XBUT cancel button of save dialog
Btn_Cancel=İptal
#XFLD title of objects section
Objects=Nesneler
#XFLD title of required packages section
RequiredPackages=Gerekli paketler
#XTIT title of package selection dialog
Dialog_SelectPackages=Paket seçin
#XMSG no data text of package selection dialog
Dilaog_NoDataText=Havuz paketi bulunamadı
#XMSG message of adding objects
Msg_ObjectAdded={0} nesne eklendi
#XMSG message of removing objects
Msg_ObjectRemoved={0} nesne kaldırıldı
#XMSG message of adding required packages
Msg_RequiredPackageAdded=Gerekli {0} paket eklendi
#XMSG message of removing required packages
Msg_RequiredPackageRemoved=Gerekli {0} paket kaldırıldı
#XBUT add button in toolbar
Toolbar_Btn_Add=Ekle
#XBUT remove button in toolbar
Toolbar_Btn_Remove=Kaldır
#XCOL status column of objects and required packages table
Table_Col_Status=Sonuç
#XLNK more link of status details
Status_More_Link=Ayrıntıları göster...
#XFLD folder name
FolderName=Ad
#XFLD folder description
FolderDescription=Tanım
#XBUT ok button
Btn_OK=Tamam
#XFLD title of export location dialog
ExportLocationDialog=Dışa aktarma konumu
#XFLD lable of category field
Category=Kategori
#XFLD label of location field
Location=Hedef konum
#XFLD lable of version field
SemanticVersion=Versiyon
#XFLD label of current exported version
CurrentExportedPackage=Dışa aktarılmış durumdaki versiyon
#XFLD label of status field
ExportStatus=Durum
#XFLD tooltip of export button
Tooltip_Export=Dışa aktar
#XFLD tooltip of save button
Tooltip_Save=Kaydet
#XFLD tooltip of validate button
Tooltip_Validate=Doğrula
#XMSG
InvalidVersion="x.y.z" biçiminde bir versiyon girin.
#XMSG
VersionLowerThanBefore=Versiyon, önceki versiyondan düşük olamaz.
#XMSG
Empty_Version=Paket versiyonu girin.
#XMSG
Package_Missing_Technical_Name=Paket için teknik ad girin.
#=============from skyline============

#XFLD lable for space combo selection
Spaces=Alan:
#XFLD section title of landing page
Packages=Paketler
#XCOL business name column
Table_Col_BusinessName=İş adı
#XCOL technical name column
Table_Col_TechnicalName=Teknik ad
#XCOL sapce column
Table_Col_Space=Alan
#XCOL create on column
Table_Col_CreatedOn=Oluşturma tarihi
#XCOL entity type column
Table_Col_Type=Tür
#XCOL entity type column
Table_Col_Object_Status=Durum
#XMSG message of deleting packages
Msg_PackageRemoved={0} paket kaldırıldı
#XMIT menu item of all spaces
Op_AllSpaces=Tüm alanlar
#XFLD default business name of a new package
NewPackage_BusinessName=Paket 1
#XFLD default technical name of a new package
NewPackage_TechnicalName=Paket_1
#XFLD default name of a new package displayed in breadcrumb
Breadcrumb_NewPackage_Name=Yeni Paket
#XFLD title of object selection dialog
ObjectsDialog_Title=Nesne ekle
#XMSG dependencies are fully resolved
Dependency_Resolved=Eklenmeye hazır (tüm bağlılıklar çözümlendi ({0}))
#XMSG dependencies are partially resolved
Dependency_Partial_Resolved=Eklenmeye hazır (bazı bağlılıklar çözümlenmedi ({0}/{1})
#XMSG dependencies are not found
Dependency_Not_Found=Bağlılıklar bulunamıyor
#XMSG dependencies are in other package
Dependency_In_Other_Package=Eklenemiyor: Zaten "{0}" paketinde
#XMSG dependencies are in other space
Dependency_In_Other_Space=Eklenemiyor: {0} alanında
#XMSG
Cannot_Add_Managed_Content=Eklenemiyor: Yönetilen içerik
#XMSG
Cannot_Add_Due_To_Dependency_Has_Managed_Content=Eklenemiyor: Bağlılıkta yönetilen içerik var
#XMSG dependencies are in current space
Dependency_In_Current_Package=Zaten pakette
#XMSG dependencies are in required package
Dependency_In_Required_Package=Zaten gerekli pakette
#XMSG package arelady exists
Package_Duplicate_Name=Paket "{0}" havuzda zaten var. Başka ad girin.
#XMSG package name is required
Package_Name_Missing=Paket adı girin.
#XMSG package version is required
Package_Version_Missing=Paket versiyonu girin.
#XMSG package is drafted
Package_Draft_Warning=Taslak paket. Bağlılıkları doğrulamak ve değişikliklerinizi teyit etmek için "Kaydet"e tıklayın.
#XFLD title of validation message dialog
Validation_MessageBox_Title=Doğrulama iletileri
#XMSG content message of validation message dialog
Validation_MessageBox_Content="{0}" geçersiz ve dışa aktarılamıyor. Gerekli nesneleri manüel şekilde veya gerekli paket aracılığıyla ekleyerek tüm nesne bağlılıklarını tam olarak çözümlemeniz gerekir.
#XBUT save anyway button
Save_Anyway=Yine de kaydet
#XMSG package is valid
Valid_Package_Message=Paket geçersiz.
#XMSG
Object_In_Other_Pacakge=Nesne "{0}", "{1}" paketinde yer alıyor.
#XMSG
Dependent_Object_In_Other_Pacakge=Nesne "{0}", gerekli paket olarak belirtilmeyen "{1}" paketinde yer alıyor.
#XMSG
Dependent_Object_Missing=Nesne "{0}", {1} alanında yer alıyor.
#XMSG
Dependent_Missing_Pattern="{0}" nesnesi şuna bağlı:\r\n{1}
#XMSG: error message for saving the package
Save_Error_Message=Paket kaydedilemedi. {0}
#XFLD title of save busy dialog
Title_Save_Busy_Dialog=Kaydediliyor
#XMSG: message of it takes long time to save
Long_Time_Save_Busy_Dialog=Kaydetme normalden uzun sürüyor. Lütfen bekleyin.
#XMSG: erro message of missing dependency
Dependency_Missing=Bağlılık eksik.
#XMSG: erro message of missing dependency
Object_Dependency_Missing="{0}" bağlılığı eksik.
#XMSG message of validating busy dialog
Validating_Package=Paket doğrulanıyor
#XFLD title of validating busy dialog
Title_Validating_Busy_Dialog=İşleniyor
#XMSG %%0 is a placeholder, don’t change it
Missing_Dependency=Eksik bağlılıklar %%0
#XMSG %%0 is a placeholder, don’t change it
Circular_Dependency=Döngüsel bağlılık %%0
#XMSG Object is added to package successfully
Successfully_Add=Eklendi
#XMSG
Circular_Dependency_Detail="{0}", "{1}" paketiyle döngüsel bağlılık içerdiğinden kaydedilemiyor.
#XFLD label of searching objects in space
Search_In_Sapce=Arama yeri: {0}
#XFLD title of deleting package confirm dialog
Package_Deleting_Dialog_Title=Sil
#XMSG content message of deleting package confirm dialog
Package_Deleting_Dialog_Message=Seçilen paketleri silmek istiyor musunuz?
#XMSG
Package_Cannot_Delete=Seçilen paket başka paketler için gerekli ve silinemez.
#XMSG
Package_Required_By_Singular=({0} paket için gerekli)
#XMSG
Package_Required_By_Plural=({0} paket için gerekli)
#XFLD display name of owner column
owner=Sorumlu
#XFLD title of deleting package error dialog
Deleting_Error_Dialog_Title=Hata
#XFLD title of deleting package alert dialog
Deleting_Alert_Dialog_Title=Uyarı
#XBUT
Deleting_Alert_Dialog_Ok=Tamam
#XFLD
MyContent=İçeriğim
#XFLD
NotExported=Dışa aktarılmadı
#XFLD
Exporting=Dışa aktarılıyor
#XFLD
Exported=Dışa aktarıldı
#XFLD
DesignTimeError=Tasarım zamanı hatası
#XFLD
ExportFailed=Dışa aktarım başarısız oldu
#XFLD
Cancelling=İptal ediliyor
#XFLD
ChangesToExport=Dışa aktarılacak değişiklikler
#XMSG
Exporting_Package={0} dışa aktarılıyor. İşlem tamamlandığında sizi bilgilendireceğiz.
#XFLD: Object type is businessEntityVariant
TypeBusinessEntityVariant=İş birimi versiyonu
#XFLD: Object type is businessEntity
TypeBusinessEntity=İş birimi
#XFLD
Tooltip_Add=Ekle
#XFLD
Tooltip_Edit=Düzenle
#XFLD
Tooltip_Delete=Sil
#XFLD
Tooltip_Refresh=Yenile
#XMSG
ExportToOverwrite=Bu paket, içerik ağına {0} versiyonuyla zaten aktarılmış. Üzerine yazmak istiyor musunuz?
# XFLD Column businessName
Ext_Selection_Col_Name=Ad
# XFLD Column Location
Ext_Selection_Col_Location=Konum
# XFLD
Col_Name=Ad
# XFLD
Col_Description=Tanım
# XFLD Label
MoveTo_Label=Klasöre taşı
#XMIT Add versions menu button text in Data Builder
versions=Versiyonlar
createVersion=Versiyon oluştur
versionHistory=Versiyon geçmişi
#XMSG
Package_Depends_On_DP=Bu paket, veri ürünlerine bağlıdır
#XMSG
Package_Depends_On_DP_Warning=Bu paketi içe aktarmadan önce şu veri ürünlerinin hedef alan tarafından kullanılabileceğinden emin olun: \r\n\r\n{0}
# XFLD
Export_Confirm_Dailog_Title=Teyit
#XMSG
Package_Contains_DataObjects=Bu paket, taşıma için veri içeren şu nesneleri içerir:\r\n\r\n{0}\r\n\r\nPaketinizde bu nesnelerin taşınması sonucunda kişisel veya hassas verilerin uygunsuz şekilde gösterilmediğinden emin olun.
