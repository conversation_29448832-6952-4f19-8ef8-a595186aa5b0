# https://wiki.wdf.sap.corp/wiki/display/DWAAS/How+to+add+text+classifications+for+translatable+texts
#
# use the following text types for short texts (up to 120 characters)
# XACT Accessibility
# XBUT Button
# XCKL Checkbox
# XCOL Column header
# XFLD Label
# XGRP Group title
# XLNK Hyperlink
# XLOG Log entry
# XLST List box item
# XMIT Menu item
# XMSG Message
# XRBL Radio button
# XSEL Selection
# XTIT Table title
# XTOL Tooltip
#
# for longer texts (more than 120 characters) use
# YINS Instruction
# YMSG Message

#=============from skyline============
#XFLD general tab of package editor
Tab_General=Geral
#XFLD objects tab of package editor
Tab_Objects=Objetos
#XFLD required packages tab of package editor
Tab_RequiredPkgs=Pacotes obrigatórios
#XFLD label for business name input
BusinessName=Nome comercial
#XFLD label for technical name input
TechnicalName=Nome técnico
#XFLD label for purpose input
BusinessPurpose=Finalidade do negócio
#XTIT title of save dialog
Dilaog_Save=Salvar
#XBUT save button of save dialog
Btn_Save=Salvar
#XBUT cancel button of save dialog
Btn_Cancel=Cancelar
#XFLD title of objects section
Objects=Objetos
#XFLD title of required packages section
RequiredPackages=Pacotes obrigatórios
#XTIT title of package selection dialog
Dialog_SelectPackages=Selecionar pacotes
#XMSG no data text of package selection dialog
Dilaog_NoDataText=Nenhum pacote de repositório encontrado
#XMSG message of adding objects
Msg_ObjectAdded={0} objetos adicionados
#XMSG message of removing objects
Msg_ObjectRemoved={0} objetos removidos
#XMSG message of adding required packages
Msg_RequiredPackageAdded={0} pacotes obrigatórios adicionados
#XMSG message of removing required packages
Msg_RequiredPackageRemoved={0} pacotes obrigatórios removidos
#XBUT add button in toolbar
Toolbar_Btn_Add=Adicionar
#XBUT remove button in toolbar
Toolbar_Btn_Remove=Remover
#XCOL status column of objects and required packages table
Table_Col_Status=Resultado
#XLNK more link of status details
Status_More_Link=Mostrar detalhes...
#XFLD folder name
FolderName=Nome
#XFLD folder description
FolderDescription=Descrição
#XBUT ok button
Btn_OK=OK
#XFLD title of export location dialog
ExportLocationDialog=Local de exportação
#XFLD lable of category field
Category=Categoria
#XFLD label of location field
Location=Local de destino
#XFLD lable of version field
SemanticVersion=Versão
#XFLD label of current exported version
CurrentExportedPackage=Versão exportada atual
#XFLD label of status field
ExportStatus=Status
#XFLD tooltip of export button
Tooltip_Export=Exportar
#XFLD tooltip of save button
Tooltip_Save=Salvar
#XFLD tooltip of validate button
Tooltip_Validate=Validar
#XMSG
InvalidVersion=Insira uma versão no formato "x.y.z".
#XMSG
VersionLowerThanBefore=A versão não pode ser menor do que a versão anterior.
#XMSG
Empty_Version=Insira uma versão de pacote.
#XMSG
Package_Missing_Technical_Name=Informe um nome técnico para o pacote.
#=============from skyline============

#XFLD lable for space combo selection
Spaces=Área:
#XFLD section title of landing page
Packages=Pacotes
#XCOL business name column
Table_Col_BusinessName=Nome comercial
#XCOL technical name column
Table_Col_TechnicalName=Nome técnico
#XCOL sapce column
Table_Col_Space=Área
#XCOL create on column
Table_Col_CreatedOn=Criado em
#XCOL entity type column
Table_Col_Type=Tipo
#XCOL entity type column
Table_Col_Object_Status=Status
#XMSG message of deleting packages
Msg_PackageRemoved={0} pacotes removidos
#XMIT menu item of all spaces
Op_AllSpaces=Todas as áreas
#XFLD default business name of a new package
NewPackage_BusinessName=Pacote 1
#XFLD default technical name of a new package
NewPackage_TechnicalName=Pacote_1
#XFLD default name of a new package displayed in breadcrumb
Breadcrumb_NewPackage_Name=Novo pacote
#XFLD title of object selection dialog
ObjectsDialog_Title=Adicionar objetos
#XMSG dependencies are fully resolved
Dependency_Resolved=Pronto para adicionar (todas as dependências resolvidas ({0}))
#XMSG dependencies are partially resolved
Dependency_Partial_Resolved=Pronto para adicionar (algumas dependências não resolvidas ({0}/{1}))
#XMSG dependencies are not found
Dependency_Not_Found=Não é possível encontrar dependências
#XMSG dependencies are in other package
Dependency_In_Other_Package=Não é possível adicionar: já no pacote "{0}"
#XMSG dependencies are in other space
Dependency_In_Other_Space=Não é possível adicionar: na área {0}
#XMSG
Cannot_Add_Managed_Content=Não é possível adicionar: conteúdo gerenciado
#XMSG
Cannot_Add_Due_To_Dependency_Has_Managed_Content=Não é possível adicionar: a dependência tem conteúdo gerenciado
#XMSG dependencies are in current space
Dependency_In_Current_Package=Já no pacote
#XMSG dependencies are in required package
Dependency_In_Required_Package=Já no pacote obrigatório
#XMSG package arelady exists
Package_Duplicate_Name=O pacote "{0}" já existe no repositório. Informe outro nome.
#XMSG package name is required
Package_Name_Missing=Informe o nome do pacote.
#XMSG package version is required
Package_Version_Missing=Insira uma versão de pacote.
#XMSG package is drafted
Package_Draft_Warning=Pacote de rascunho. Clique em "Salvar" para verificar as dependências e confirmar suas alterações.
#XFLD title of validation message dialog
Validation_MessageBox_Title=Mensagens de validação
#XMSG content message of validation message dialog
Validation_MessageBox_Content="{0}" é inválido e não pode ser exportado. Você precisa resolver completamente todas as dependência de objetos adicionando os objetos necessários de forma manual ou por meio de um pacote obrigatório.
#XBUT save anyway button
Save_Anyway=Salvar mesmo assim
#XMSG package is valid
Valid_Package_Message=O pacote é válido.
#XMSG
Object_In_Other_Pacakge=O objeto "{0}" está no pacote "{1}".
#XMSG
Dependent_Object_In_Other_Pacakge=Objeto "{0}" no pacote "{1}", que não está especificado como pacote obrigatório.
#XMSG
Dependent_Object_Missing=Objeto "{0}" na área {1}.
#XMSG
Dependent_Missing_Pattern=O objeto "{0}" depende de:\r\n{1}
#XMSG: error message for saving the package
Save_Error_Message=Falha ao salvar o pacote. {0}
#XFLD title of save busy dialog
Title_Save_Busy_Dialog=Salvando
#XMSG: message of it takes long time to save
Long_Time_Save_Busy_Dialog=A operação salvar está levando mais tempo do que o usual. Aguarde.
#XMSG: erro message of missing dependency
Dependency_Missing=Dependência ausente.
#XMSG: erro message of missing dependency
Object_Dependency_Missing=Dependência de "{0}" ausente.
#XMSG message of validating busy dialog
Validating_Package=Validando pacote
#XFLD title of validating busy dialog
Title_Validating_Busy_Dialog=Em processamento
#XMSG %%0 is a placeholder, don’t change it
Missing_Dependency=Dependências ausentes %%0
#XMSG %%0 is a placeholder, don’t change it
Circular_Dependency=Dependência circular %%0
#XMSG Object is added to package successfully
Successfully_Add=Adicionado
#XMSG
Circular_Dependency_Detail=Não é possível salvar "{0}", ele tem uma dependência circular com o pacote "{1}".
#XFLD label of searching objects in space
Search_In_Sapce=Pesquisar em: {0}
#XFLD title of deleting package confirm dialog
Package_Deleting_Dialog_Title=Excluir
#XMSG content message of deleting package confirm dialog
Package_Deleting_Dialog_Message=Deseja excluir os pacotes selecionados?
#XMSG
Package_Cannot_Delete=O pacote selecionado é obrigatório em outros pacotes e não pode ser excluído.
#XMSG
Package_Required_By_Singular=(obrigatório em {0} pacote)
#XMSG
Package_Required_By_Plural=(obrigatório em {0} pacotes)
#XFLD display name of owner column
owner=Proprietário
#XFLD title of deleting package error dialog
Deleting_Error_Dialog_Title=Erro
#XFLD title of deleting package alert dialog
Deleting_Alert_Dialog_Title=Alerta
#XBUT
Deleting_Alert_Dialog_Ok=OK
#XFLD
MyContent=Meu conteúdo
#XFLD
NotExported=Não exportado
#XFLD
Exporting=Exportando
#XFLD
Exported=Exportado
#XFLD
DesignTimeError=Erro em tempo de design
#XFLD
ExportFailed=Falha ao exportar
#XFLD
Cancelling=Cancelando
#XFLD
ChangesToExport=Alterações a exportar
#XMSG
Exporting_Package=Exportando {0}. Você receberá uma notificação quando o processo for concluído.
#XFLD: Object type is businessEntityVariant
TypeBusinessEntityVariant=Versão de entidade empresarial
#XFLD: Object type is businessEntity
TypeBusinessEntity=Entidade empresarial
#XFLD
Tooltip_Add=Adicionar
#XFLD
Tooltip_Edit=Editar
#XFLD
Tooltip_Delete=Excluir
#XFLD
Tooltip_Refresh=Atualizar
#XMSG
ExportToOverwrite=Este pacote ja foi exportado para a Rede de conteúdo com a versão {0}. Deseja sobregravá-lo?
# XFLD Column businessName
Ext_Selection_Col_Name=Nome
# XFLD Column Location
Ext_Selection_Col_Location=Local
# XFLD
Col_Name=Nome
# XFLD
Col_Description=Descrição
# XFLD Label
MoveTo_Label=Mover para pasta
#XMIT Add versions menu button text in Data Builder
versions=Versões
createVersion=Criar versão
versionHistory=Histórico de versões
#XMSG
Package_Depends_On_DP=Este pacote depende os produtos de dados
#XMSG
Package_Depends_On_DP_Warning=Antes de importar este pacote, verifique se os seguintes produtos de dados estão disponíveis para a área de destino:\r\n\r\n{0}
# XFLD
Export_Confirm_Dailog_Title=Confirmação
#XMSG
Package_Contains_DataObjects=Este pacote inclui os seguintes objetos que contêm dados para transporte:\r\n\r\n{0}\r\n\r\Verifique se algum dado pessoal ou confidencial será exposto incorretamente como resultado do transporte desses objetos em seu pacote.
