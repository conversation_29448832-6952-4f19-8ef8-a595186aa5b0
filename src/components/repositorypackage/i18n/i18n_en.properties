# https://wiki.wdf.sap.corp/wiki/display/DWAAS/How+to+add+text+classifications+for+translatable+texts
#
# use the following text types for short texts (up to 120 characters)
# XACT Accessibility
# XBUT Button
# XCKL Checkbox
# XCOL Column header
# XFLD Label
# XGRP Group title
# XLNK Hyperlink
# XLOG Log entry
# XLST List box item
# XMIT Menu item
# XMSG Message
# XRBL Radio button
# XSEL Selection
# XTIT Table title
# XTOL Tooltip
#
# for longer texts (more than 120 characters) use
# YINS Instruction
# YMSG Message

#=============from skyline============
#XFLD general tab of package editor
Tab_General=General
#XFLD objects tab of package editor
Tab_Objects=Objects
#XFLD required packages tab of package editor
Tab_RequiredPkgs=Required Packages
#XFLD label for business name input
BusinessName=Business Name
#XFLD label for technical name input
TechnicalName=Technical Name
#XFLD label for purpose input
BusinessPurpose=Business Purpose
#XTIT title of save dialog
Dilaog_Save=Save
#XBUT save button of save dialog
Btn_Save=Save
#XBUT cancel button of save dialog
Btn_Cancel=Cancel
#XFLD title of objects section
Objects=Objects
#XFLD title of required packages section
RequiredPackages=Required Packages
#XTIT title of package selection dialog
Dialog_SelectPackages=Select Packages
#XMSG no data text of package selection dialog
Dilaog_NoDataText=No Repository Packages Found
#XMSG message of adding objects
Msg_ObjectAdded={0} objects added
#XMSG message of removing objects
Msg_ObjectRemoved={0} objects removed
#XMSG message of adding required packages
Msg_RequiredPackageAdded={0} required packages added
#XMSG message of removing required packages
Msg_RequiredPackageRemoved={0} required packages removed
#XBUT add button in toolbar
Toolbar_Btn_Add=Add
#XBUT remove button in toolbar
Toolbar_Btn_Remove=Remove
#XCOL status column of objects and required packages table
Table_Col_Status=Result
#XLNK more link of status details
Status_More_Link=Show details...
#XFLD folder name
FolderName=Name
#XFLD folder description
FolderDescription=Description
#XBUT ok button
Btn_OK=OK
#XFLD title of export location dialog
ExportLocationDialog=Export Location
#XFLD lable of category field
Category=Category
#XFLD label of location field
Location=Target Location
#XFLD lable of version field
SemanticVersion=Version
#XFLD label of current exported version
CurrentExportedPackage=Current Exported Version
#XFLD label of status field
ExportStatus=Status
#XFLD tooltip of export button
Tooltip_Export=Export
#XFLD tooltip of save button
Tooltip_Save=Save
#XFLD tooltip of validate button
Tooltip_Validate=Validate
#XMSG
InvalidVersion=Enter a version in the form "x.y.z".
#XMSG
VersionLowerThanBefore=Version cannot be lower than previous version.
#XMSG
Empty_Version=Enter a package version.
#XMSG
Package_Missing_Technical_Name=Enter a technical name for the package.
#=============from skyline============

#XFLD lable for space combo selection
Spaces=Space:
#XFLD section title of landing page
Packages=Packages
#XCOL business name column
Table_Col_BusinessName=Business Name
#XCOL technical name column
Table_Col_TechnicalName=Technical Name
#XCOL sapce column
Table_Col_Space=Space
#XCOL create on column
Table_Col_CreatedOn=Created On
#XCOL entity type column
Table_Col_Type=Type
#XCOL entity type column
Table_Col_Object_Status=Status
#XMSG message of deleting packages
Msg_PackageRemoved={0} packages removed
#XMIT menu item of all spaces
Op_AllSpaces=All Spaces
#XFLD default business name of a new package
NewPackage_BusinessName=Package 1
#XFLD default technical name of a new package
NewPackage_TechnicalName=Package_1
#XFLD default name of a new package displayed in breadcrumb
Breadcrumb_NewPackage_Name=New Package
#XFLD title of object selection dialog
ObjectsDialog_Title=Add Objects
#XMSG dependencies are fully resolved
Dependency_Resolved=Ready to Add (All Dependencies Resolved ({0}))
#XMSG dependencies are partially resolved
Dependency_Partial_Resolved=Ready to Add (Some Dependencies Not Resolved ({0}/{1})
#XMSG dependencies are not found
Dependency_Not_Found=Dependencies cannot be found
#XMSG dependencies are in other package
Dependency_In_Other_Package=Cannot Add: Already in Package "{0}"
#XMSG dependencies are in other space
Dependency_In_Other_Space=Cannot Add: In Space {0}
#XMSG
Cannot_Add_Managed_Content=Cannot Add: Managed Content
#XMSG
Cannot_Add_Due_To_Dependency_Has_Managed_Content=Cannot Add: Dependency Has Managed Content
#XMSG dependencies are in current space
Dependency_In_Current_Package=Already in Package
#XMSG dependencies are in required package
Dependency_In_Required_Package=Already in Required Package
#XMSG package arelady exists
Package_Duplicate_Name=Package "{0}" already exists in the repository. Please enter another name.
#XMSG package name is required
Package_Name_Missing=Enter a package name.
#XMSG package version is required
Package_Version_Missing=Enter a package version.
#XMSG package is drafted
Package_Draft_Warning=Draft package. Click "Save" to verify dependencies and confirm your changes.
#XFLD title of validation message dialog
Validation_MessageBox_Title=Validation Messages
#XMSG content message of validation message dialog
Validation_MessageBox_Content="{0}" is invalid and cannot be exported. You must fully resolve all object dependencies by adding the necessary objects manually or via a required package.
#XBUT save anyway button
Save_Anyway=Save Anyway
#XMSG package is valid
Valid_Package_Message=The package is valid.
#XMSG
Object_In_Other_Pacakge=Object "{0}" is in package "{1}".
#XMSG
Dependent_Object_In_Other_Pacakge=Object "{0}" in package "{1}", which is not specified as a required package.
#XMSG
Dependent_Object_Missing=Object "{0}" in space {1}.
#XMSG
Dependent_Missing_Pattern=Object "{0}" depends on:\r\n{1}
#XMSG: error message for saving the package
Save_Error_Message=Failed to save the package. {0}
#XFLD title of save busy dialog
Title_Save_Busy_Dialog=Saving
#XMSG: message of it takes long time to save
Long_Time_Save_Busy_Dialog=Saving is taking longer than usual. Please wait.
#XMSG: erro message of missing dependency
Dependency_Missing=Dependency is missing.
#XMSG: erro message of missing dependency
Object_Dependency_Missing=Dependency of "{0}" is missing.
#XMSG message of validating busy dialog
Validating_Package=Validating Package
#XFLD title of validating busy dialog
Title_Validating_Busy_Dialog=Processing
#XMSG %%0 is a placeholder, don’t change it
Missing_Dependency=Missing dependencies %%0
#XMSG %%0 is a placeholder, don’t change it
Circular_Dependency=Circular dependency %%0
#XMSG Object is added to package successfully
Successfully_Add=Added
#XMSG
Circular_Dependency_Detail="{0}" cannot be saved because it has a circular dependency with package "{1}".
#XFLD label of searching objects in space
Search_In_Sapce=Search in: {0}
#XFLD title of deleting package confirm dialog
Package_Deleting_Dialog_Title=Delete
#XMSG content message of deleting package confirm dialog
Package_Deleting_Dialog_Message=Do you want to delete the selected packages?
#XMSG
Package_Cannot_Delete=The selected package is required by other packages and cannot be deleted.
#XMSG
Package_Required_By_Singular=(required by {0} package)
#XMSG
Package_Required_By_Plural=(required by {0} packages)
#XFLD display name of owner column
owner=Owner
#XFLD title of deleting package error dialog
Deleting_Error_Dialog_Title=Error
#XFLD title of deleting package alert dialog
Deleting_Alert_Dialog_Title=Alert
#XBUT
Deleting_Alert_Dialog_Ok=OK
#XFLD
MyContent=My Content
#XFLD
NotExported=Not Exported
#XFLD
Exporting=Exporting
#XFLD
Exported=Exported
#XFLD
DesignTimeError=Design Time Error
#XFLD
ExportFailed=Export Failed
#XFLD
Cancelling=Canceling
#XFLD
ChangesToExport=Changes To Export
#XMSG
Exporting_Package=Exporting {0}. We will notify you when the process is complete.
#XFLD: Object type is businessEntityVariant
TypeBusinessEntityVariant=Business Entity Version
#XFLD: Object type is businessEntity
TypeBusinessEntity=Business Entity
#XFLD
Tooltip_Add=Add
#XFLD
Tooltip_Edit=Edit
#XFLD
Tooltip_Delete=Delete
#XFLD
Tooltip_Refresh=Refresh
#XMSG
ExportToOverwrite=This package has already been exported to the Content Network with version {0}. Do you want to overwrite it?
# XFLD Column businessName
Ext_Selection_Col_Name=Name
# XFLD Column Location
Ext_Selection_Col_Location=Location
# XFLD
Col_Name=Name
# XFLD
Col_Description=Description
# XFLD Label
MoveTo_Label=Move To Folder
#XMIT Add versions menu button text in Data Builder
versions=Versions
createVersion=Create Version
versionHistory=Version History
#XMSG
Package_Depends_On_DP=This package depends on data products
#XMSG
Package_Depends_On_DP_Warning=Before importing this package, ensure that the following data products are available to the target space:\r\n\r\n{0}
# XFLD
Export_Confirm_Dailog_Title=Confirmation
#XMSG
Package_Contains_DataObjects=This package includes the following objects containing data for transport:\r\n\r\n{0}\r\n\r\nPlease ensure that no personal or sensitive data will be improperly exposed as a result of transporting these objects in your package.
