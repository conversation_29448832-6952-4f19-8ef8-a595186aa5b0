# https://wiki.wdf.sap.corp/wiki/display/DWAAS/How+to+add+text+classifications+for+translatable+texts
#
# use the following text types for short texts (up to 120 characters)
# XACT Accessibility
# XBUT Button
# XCKL Checkbox
# XCOL Column header
# XFLD Label
# XGRP Group title
# XLNK Hyperlink
# XLOG Log entry
# XLST List box item
# XMIT Menu item
# XMSG Message
# XRBL Radio button
# XSEL Selection
# XTIT Table title
# XTOL Tooltip
#
# for longer texts (more than 120 characters) use
# YINS Instruction
# YMSG Message

#=============from skyline============
#XFLD general tab of package editor
Tab_General=General
#XFLD objects tab of package editor
Tab_Objects=Objectes
#XFLD required packages tab of package editor
Tab_RequiredPkgs=Paquets requerits
#XFLD label for business name input
BusinessName=Nom empresarial
#XFLD label for technical name input
TechnicalName=Nom tècnic
#XFLD label for purpose input
BusinessPurpose=Objectiu empresarial
#XTIT title of save dialog
Dilaog_Save=Desar
#XBUT save button of save dialog
Btn_Save=Desar
#XBUT cancel button of save dialog
Btn_Cancel=Cancel·lar
#XFLD title of objects section
Objects=Objectes
#XFLD title of required packages section
RequiredPackages=Paquets requerits
#XTIT title of package selection dialog
Dialog_SelectPackages=Seleccionar paquets
#XMSG no data text of package selection dialog
Dilaog_NoDataText=No existeixen paquets de dipòsit
#XMSG message of adding objects
Msg_ObjectAdded={0} objectes afegits
#XMSG message of removing objects
Msg_ObjectRemoved={0} objectes suprimits
#XMSG message of adding required packages
Msg_RequiredPackageAdded={0} paquets requerits afegits
#XMSG message of removing required packages
Msg_RequiredPackageRemoved={0} paquets requerits eliminats
#XBUT add button in toolbar
Toolbar_Btn_Add=Afegir
#XBUT remove button in toolbar
Toolbar_Btn_Remove=Eliminar
#XCOL status column of objects and required packages table
Table_Col_Status=Resultat
#XLNK more link of status details
Status_More_Link=Mostrar detalls...
#XFLD folder name
FolderName=Nom
#XFLD folder description
FolderDescription=Descripció
#XBUT ok button
Btn_OK=D'acord
#XFLD title of export location dialog
ExportLocationDialog=Exportar ubicació
#XFLD lable of category field
Category=Categoria
#XFLD label of location field
Location=Ubicació de destinació
#XFLD lable of version field
SemanticVersion=Versió
#XFLD label of current exported version
CurrentExportedPackage=Versió actual exportada
#XFLD label of status field
ExportStatus=Estat
#XFLD tooltip of export button
Tooltip_Export=Exportar
#XFLD tooltip of save button
Tooltip_Save=Desar
#XFLD tooltip of validate button
Tooltip_Validate=Validar
#XMSG
InvalidVersion=Introduïu una versió amb la forma "x.y.z".
#XMSG
VersionLowerThanBefore=La versió no pot ser inferior a l'anterior.
#XMSG
Empty_Version=Indiqueu una versió de paquet.
#XMSG
Package_Missing_Technical_Name=Introduïu un nom tècnic per al paquet.
#=============from skyline============

#XFLD lable for space combo selection
Spaces=Espai:
#XFLD section title of landing page
Packages=Paquets
#XCOL business name column
Table_Col_BusinessName=Nom empresarial
#XCOL technical name column
Table_Col_TechnicalName=Nom tècnic
#XCOL sapce column
Table_Col_Space=Espai
#XCOL create on column
Table_Col_CreatedOn=Data de creació
#XCOL entity type column
Table_Col_Type=Tipus
#XCOL entity type column
Table_Col_Object_Status=Estat
#XMSG message of deleting packages
Msg_PackageRemoved={0} paquets eliminats
#XMIT menu item of all spaces
Op_AllSpaces=Tots els espais
#XFLD default business name of a new package
NewPackage_BusinessName=Paquet 1
#XFLD default technical name of a new package
NewPackage_TechnicalName=Package_1
#XFLD default name of a new package displayed in breadcrumb
Breadcrumb_NewPackage_Name=Paquet nou
#XFLD title of object selection dialog
ObjectsDialog_Title=Afegir objectes
#XMSG dependencies are fully resolved
Dependency_Resolved=Preparat per afegir (s''han resolt totes les dependències ({0}))
#XMSG dependencies are partially resolved
Dependency_Partial_Resolved=Preparat per afegir (algunes dependències no s''han resolt ({0}/{1}))
#XMSG dependencies are not found
Dependency_Not_Found=No existeixen dependències
#XMSG dependencies are in other package
Dependency_In_Other_Package=No es pot afegir: Ja al paquet "{0}"
#XMSG dependencies are in other space
Dependency_In_Other_Space=No es pot afegir: A l''espai {0}
#XMSG
Cannot_Add_Managed_Content=No es pot afegir: contigut gestionat
#XMSG
Cannot_Add_Due_To_Dependency_Has_Managed_Content=No es pot afegir: la dependència té contigut gestionat
#XMSG dependencies are in current space
Dependency_In_Current_Package=Ja al paquet
#XMSG dependencies are in required package
Dependency_In_Required_Package=Ja és al paquet requerit
#XMSG package arelady exists
Package_Duplicate_Name=El paquet "{0}" ja existeix al dipòsit. Indiqueu un altre nom.
#XMSG package name is required
Package_Name_Missing=Indiqueu un nom de paquet.
#XMSG package version is required
Package_Version_Missing=Indiqueu una versió de paquet.
#XMSG package is drafted
Package_Draft_Warning=Esborrany de paquet. Feu clic a "Desar" per verificar les dependències i confirmar els canvis.
#XFLD title of validation message dialog
Validation_MessageBox_Title=Missatges de validació
#XMSG content message of validation message dialog
Validation_MessageBox_Content="{0}" no és vàlid i no es pot exportar. Cal que resoleu íntegrament totes les dependències d''objecte afegint els objectes necessaris manualment o mitjançant un paquet requerit.
#XBUT save anyway button
Save_Anyway=Desar de totes maneres
#XMSG package is valid
Valid_Package_Message=El paquet és vàlid.
#XMSG
Object_In_Other_Pacakge=L''objecte "{0}" es troba al paquet "{1}".
#XMSG
Dependent_Object_In_Other_Pacakge=L''objecte "{0}" es troba al paquet "{1}", que no està especificat com a requerit.
#XMSG
Dependent_Object_Missing=L''objecte "{0}" es troba a l''espai {1}.
#XMSG
Dependent_Missing_Pattern=L''objecte "{0}" depèn de:\r\n{1}
#XMSG: error message for saving the package
Save_Error_Message=Error en desar el paquet. {0}
#XFLD title of save busy dialog
Title_Save_Busy_Dialog=Desar
#XMSG: message of it takes long time to save
Long_Time_Save_Busy_Dialog=L’operació de desar triga més de l’habitual. Espereu un moment.
#XMSG: erro message of missing dependency
Dependency_Missing=Falta la dependència.
#XMSG: erro message of missing dependency
Object_Dependency_Missing=Falta la dependència de "{0}".
#XMSG message of validating busy dialog
Validating_Package=Validació del paquet
#XFLD title of validating busy dialog
Title_Validating_Busy_Dialog=En processament
#XMSG %%0 is a placeholder, don’t change it
Missing_Dependency=Dependències que falten %%0
#XMSG %%0 is a placeholder, don’t change it
Circular_Dependency=Dependència circular %%0
#XMSG Object is added to package successfully
Successfully_Add=Afegit
#XMSG
Circular_Dependency_Detail="{0}" no es pot desar perquè té una dependència circular amb el paquet "{1}".
#XFLD label of searching objects in space
Search_In_Sapce=Cercar a: {0}
#XFLD title of deleting package confirm dialog
Package_Deleting_Dialog_Title=Suprimir
#XMSG content message of deleting package confirm dialog
Package_Deleting_Dialog_Message=Voleu suprimir els paquets seleccionats?
#XMSG
Package_Cannot_Delete=Altres paquets requereixen el paquet seleccionat i per això no es pot suprimir.
#XMSG
Package_Required_By_Singular=(requerit per {0} paquet)
#XMSG
Package_Required_By_Plural=(requerit per {0} paquets)
#XFLD display name of owner column
owner=Propietari
#XFLD title of deleting package error dialog
Deleting_Error_Dialog_Title=Error
#XFLD title of deleting package alert dialog
Deleting_Alert_Dialog_Title=Alerta
#XBUT
Deleting_Alert_Dialog_Ok=D'acord
#XFLD
MyContent=El meu contingut
#XFLD
NotExported=No exportat
#XFLD
Exporting=Exportant
#XFLD
Exported=Exportat
#XFLD
DesignTimeError=Error de temps de disseny
#XFLD
ExportFailed=Error en exportar
#XFLD
Cancelling=Cancel·lant
#XFLD
ChangesToExport=Canvis a l'exportació
#XMSG
Exporting_Package=Exportant {0}. Us avisarem quan hagi acabat el procés.
#XFLD: Object type is businessEntityVariant
TypeBusinessEntityVariant=Versió d'entitat empresarial
#XFLD: Object type is businessEntity
TypeBusinessEntity=Entitat empresarial
#XFLD
Tooltip_Add=Afegir
#XFLD
Tooltip_Edit=Editar
#XFLD
Tooltip_Delete=Suprimir
#XFLD
Tooltip_Refresh=Actualitzar
#XMSG
ExportToOverwrite=Aquest paquet ja ha estat exportat a la Xarxa de contingut amb la versió {0}. Voleu sobreescriure''l?
# XFLD Column businessName
Ext_Selection_Col_Name=Nom
# XFLD Column Location
Ext_Selection_Col_Location=Ubicació
# XFLD
Col_Name=Nom
# XFLD
Col_Description=Descripció
# XFLD Label
MoveTo_Label=Moure a carpeta
#XMIT Add versions menu button text in Data Builder
versions=Versions
createVersion=Crear versió
versionHistory=Historial de versions
#XMSG
Package_Depends_On_DP=Aquest paquet depèn de productes de dades
#XMSG
Package_Depends_On_DP_Warning=Abans d''importar aquest paquet, comproveu que els següents productes de dades estiguin disponibles per a l''espai de destinació:\r\n\r\n{0}
# XFLD
Export_Confirm_Dailog_Title=Confirmació
#XMSG
Package_Contains_DataObjects=Aquest paquet inclou els següents objectes que contenen dades per al transport:\r\n\r\n{0} \r\n\r\nAssegureu-vos que no s''exposaran dades personals o confidencials de manera incorrecta com a resultat del transport d’aquests objectes al vostre paquet.
