# https://wiki.wdf.sap.corp/wiki/display/DWAAS/How+to+add+text+classifications+for+translatable+texts
#
# use the following text types for short texts (up to 120 characters)
# XACT Accessibility
# XBUT Button
# XCKL Checkbox
# XCOL Column header
# XFLD Label
# XGRP Group title
# XLNK Hyperlink
# XLOG Log entry
# XLST List box item
# XMIT Menu item
# XMSG Message
# XRBL Radio button
# XSEL Selection
# XTIT Table title
# XTOL Tooltip
#
# for longer texts (more than 120 characters) use
# YINS Instruction
# YMSG Message

#=============from skyline============
#XFLD general tab of package editor
Tab_General=Generale
#XFLD objects tab of package editor
Tab_Objects=Oggetti
#XFLD required packages tab of package editor
Tab_RequiredPkgs=Pacchetti richiesti
#XFLD label for business name input
BusinessName=Nome aziendale
#XFLD label for technical name input
TechnicalName=Nome tecnico
#XFLD label for purpose input
BusinessPurpose=Scopo commerciale
#XTIT title of save dialog
Dilaog_Save=Salva
#XBUT save button of save dialog
Btn_Save=Salva
#XBUT cancel button of save dialog
Btn_Cancel=Annulla
#XFLD title of objects section
Objects=Oggetti
#XFLD title of required packages section
RequiredPackages=Pacchetti richiesti
#XTIT title of package selection dialog
Dialog_SelectPackages=Seleziona pacchetti
#XMSG no data text of package selection dialog
Dilaog_NoDataText=Nessun pacchetto di repository trovato
#XMSG message of adding objects
Msg_ObjectAdded={0} oggetti aggiunti
#XMSG message of removing objects
Msg_ObjectRemoved={0} oggetti rimossi
#XMSG message of adding required packages
Msg_RequiredPackageAdded={0} pacchetti richiesti aggiunti
#XMSG message of removing required packages
Msg_RequiredPackageRemoved={0} pacchetti richiesti rimossi
#XBUT add button in toolbar
Toolbar_Btn_Add=Aggiungi
#XBUT remove button in toolbar
Toolbar_Btn_Remove=Rimuovi
#XCOL status column of objects and required packages table
Table_Col_Status=Risultato
#XLNK more link of status details
Status_More_Link=Mostra dettagli...
#XFLD folder name
FolderName=Nome
#XFLD folder description
FolderDescription=Descrizione
#XBUT ok button
Btn_OK=OK
#XFLD title of export location dialog
ExportLocationDialog=Posizione di esportazione
#XFLD lable of category field
Category=Categoria
#XFLD label of location field
Location=Posizione di destinazione
#XFLD lable of version field
SemanticVersion=Versione
#XFLD label of current exported version
CurrentExportedPackage=Versione esportata corrente
#XFLD label of status field
ExportStatus=Stato
#XFLD tooltip of export button
Tooltip_Export=Esporta
#XFLD tooltip of save button
Tooltip_Save=Salva
#XFLD tooltip of validate button
Tooltip_Validate=Convalida
#XMSG
InvalidVersion=Immettere una versione nel formato "x.y.z".
#XMSG
VersionLowerThanBefore=La versione non può essere inferiore alla versione precedente.
#XMSG
Empty_Version=Immettere una versione pacchetto.
#XMSG
Package_Missing_Technical_Name=Immettere un nome tecnico per il pacchetto.
#=============from skyline============

#XFLD lable for space combo selection
Spaces=Spazio:
#XFLD section title of landing page
Packages=Pacchetti
#XCOL business name column
Table_Col_BusinessName=Nome aziendale
#XCOL technical name column
Table_Col_TechnicalName=Nome tecnico
#XCOL sapce column
Table_Col_Space=Spazio
#XCOL create on column
Table_Col_CreatedOn=Data di creazione
#XCOL entity type column
Table_Col_Type=Tipo
#XCOL entity type column
Table_Col_Object_Status=Stato
#XMSG message of deleting packages
Msg_PackageRemoved={0} pacchetti rimossi
#XMIT menu item of all spaces
Op_AllSpaces=Tutti gli spazi
#XFLD default business name of a new package
NewPackage_BusinessName=Pacchetto 1
#XFLD default technical name of a new package
NewPackage_TechnicalName=Pacchetto_1
#XFLD default name of a new package displayed in breadcrumb
Breadcrumb_NewPackage_Name=Nuovo pacchetto
#XFLD title of object selection dialog
ObjectsDialog_Title=Aggiungi oggetti
#XMSG dependencies are fully resolved
Dependency_Resolved=Pronto all''aggiunta (tutte le dipendenze risolte ({0}))
#XMSG dependencies are partially resolved
Dependency_Partial_Resolved=Pronto all''aggiunta (alcune dipendenze non risolte ({0}/{1})
#XMSG dependencies are not found
Dependency_Not_Found=Impossibile trovare le dipendenze
#XMSG dependencies are in other package
Dependency_In_Other_Package=Aggiunta impossibile: già presente nel pacchetto "{0}"
#XMSG dependencies are in other space
Dependency_In_Other_Space=Aggiunta impossibile: nello spazio {0}
#XMSG
Cannot_Add_Managed_Content=Aggiunta impossibile: contenuto gestito
#XMSG
Cannot_Add_Due_To_Dependency_Has_Managed_Content=Aggiunta impossibile: dipendenza con contenuto gestito
#XMSG dependencies are in current space
Dependency_In_Current_Package=Già presente nel pacchetto
#XMSG dependencies are in required package
Dependency_In_Required_Package=Già presente nel pacchetto richiesto
#XMSG package arelady exists
Package_Duplicate_Name=Pacchetto "{0}" già esistente nel repository. Immettere un altro nome.
#XMSG package name is required
Package_Name_Missing=Immettere un nome pacchetto.
#XMSG package version is required
Package_Version_Missing=Immettere una versione pacchetto.
#XMSG package is drafted
Package_Draft_Warning=Pacchetto in bozza. Fare clic su "Salva" per verificare le dipendenze e confermare le modifiche.
#XFLD title of validation message dialog
Validation_MessageBox_Title=Messaggi di convalida
#XMSG content message of validation message dialog
Validation_MessageBox_Content="{0}" non è valido e non può essere esportato. Occorre risolvere completamente tutte le dipendenze tra oggetti aggiungendo gli oggetti necessari manualmente o mediante un pacchetto richiesto.
#XBUT save anyway button
Save_Anyway=Salva comunque
#XMSG package is valid
Valid_Package_Message=Il pacchetto è valido.
#XMSG
Object_In_Other_Pacakge=L''oggetto "{0}" è nel pacchetto "{1}".
#XMSG
Dependent_Object_In_Other_Pacakge=L''oggetto "{0}" è nel pacchetto "{1}", non specificato come pacchetto richiesto.
#XMSG
Dependent_Object_Missing=Oggetto "{0}" nello spazio {1}.
#XMSG
Dependent_Missing_Pattern=L''oggetto "{0}" dipende da:\r\n{1}
#XMSG: error message for saving the package
Save_Error_Message=Salvataggio del pacchetto non riuscito. {0}
#XFLD title of save busy dialog
Title_Save_Busy_Dialog=Salvataggio in corso
#XMSG: message of it takes long time to save
Long_Time_Save_Busy_Dialog=Il salvataggio sta impiegando più tempo del normale. Attendere.
#XMSG: erro message of missing dependency
Dependency_Missing=Dipendenza mancante.
#XMSG: erro message of missing dependency
Object_Dependency_Missing=Dipendenza di "{0}" mancante.
#XMSG message of validating busy dialog
Validating_Package=Convalida del pacchetto in corso
#XFLD title of validating busy dialog
Title_Validating_Busy_Dialog=Elaborazione in corso
#XMSG %%0 is a placeholder, don’t change it
Missing_Dependency=Dipendenze mancanti %%0
#XMSG %%0 is a placeholder, don’t change it
Circular_Dependency=Dipendenza circolare %%0
#XMSG Object is added to package successfully
Successfully_Add=Aggiunto
#XMSG
Circular_Dependency_Detail=Impossibile salvare "{0}" perché presenta una dipendenza circolare con il pacchetto "{1}".
#XFLD label of searching objects in space
Search_In_Sapce=Cerca in: {0}
#XFLD title of deleting package confirm dialog
Package_Deleting_Dialog_Title=Elimina
#XMSG content message of deleting package confirm dialog
Package_Deleting_Dialog_Message=Eliminare i pacchetti selezionati?
#XMSG
Package_Cannot_Delete=Il pacchetto selezionato è richiesto da altri pacchetti e non può essere eliminato.
#XMSG
Package_Required_By_Singular=(richiesto da {0} pacchetto)
#XMSG
Package_Required_By_Plural=(richiesto da {0} pacchetti)
#XFLD display name of owner column
owner=Responsabile
#XFLD title of deleting package error dialog
Deleting_Error_Dialog_Title=Errore
#XFLD title of deleting package alert dialog
Deleting_Alert_Dialog_Title=Allerta
#XBUT
Deleting_Alert_Dialog_Ok=OK
#XFLD
MyContent=I miei contenuti
#XFLD
NotExported=Non esportato
#XFLD
Exporting=Esportazione in corso
#XFLD
Exported=Esportato
#XFLD
DesignTimeError=Errore fase di progettazione
#XFLD
ExportFailed=Esportazione non riuscita
#XFLD
Cancelling=Annullamento in corso
#XFLD
ChangesToExport=Modifiche da esportare
#XMSG
Exporting_Package=Esportazione di {0}. L''utente riceverà una notifica al completamento del processo.
#XFLD: Object type is businessEntityVariant
TypeBusinessEntityVariant=Versione entità aziendale
#XFLD: Object type is businessEntity
TypeBusinessEntity=Entità aziendale
#XFLD
Tooltip_Add=Aggiungi
#XFLD
Tooltip_Edit=Modifica
#XFLD
Tooltip_Delete=Elimina
#XFLD
Tooltip_Refresh=Aggiorna
#XMSG
ExportToOverwrite=Questo pacchetto è già stato esportato nella Rete di contenuti con la versione {0}. Sovrascriverlo?
# XFLD Column businessName
Ext_Selection_Col_Name=Nome
# XFLD Column Location
Ext_Selection_Col_Location=Posizione
# XFLD
Col_Name=Nome
# XFLD
Col_Description=Descrizione
# XFLD Label
MoveTo_Label=Sposta nella cartella
#XMIT Add versions menu button text in Data Builder
versions=Versioni
createVersion=Crea versione
versionHistory=Cronologia versioni
#XMSG
Package_Depends_On_DP=Questo pacchetto dipende da prodotti dati
#XMSG
Package_Depends_On_DP_Warning=Prima di importare questo pacchetto, assicurarsi che i seguenti prodotti dati siano disponibili per lo spazio di destinazione \r\n\r\n{0}
# XFLD
Export_Confirm_Dailog_Title=Conferma
#XMSG
Package_Contains_DataObjects=Questo pacchetto include i seguenti oggetti contenenti dati per il trasporto:\r\n\r\n{0} \r\n\r\nAssicurarsi che non vengano esposti in modo improprio dati personali o sensibili a seguito del trasporto di questi oggetti nel pacchetto.
