# https://wiki.wdf.sap.corp/wiki/display/DWAAS/How+to+add+text+classifications+for+translatable+texts
#
# use the following text types for short texts (up to 120 characters)
# XACT Accessibility
# XBUT Button
# XCKL Checkbox
# XCOL Column header
# XFLD Label
# XGRP Group title
# XLNK Hyperlink
# XLOG Log entry
# XLST List box item
# XMIT Menu item
# XMSG Message
# XRBL Radio button
# XSEL Selection
# XTIT Table title
# XTOL Tooltip
#
# for longer texts (more than 120 characters) use
# YINS Instruction
# YMSG Message

#=============from skyline============
#XFLD general tab of package editor
Tab_General=Algemeen
#XFLD objects tab of package editor
Tab_Objects=Objecten
#XFLD required packages tab of package editor
Tab_RequiredPkgs=Vereiste pakketten
#XFLD label for business name input
BusinessName=Objectnaam
#XFLD label for technical name input
TechnicalName=Technische naam
#XFLD label for purpose input
BusinessPurpose=Zakelijk doel
#XTIT title of save dialog
Dilaog_Save=Opslaan
#XBUT save button of save dialog
Btn_Save=Opslaan
#XBUT cancel button of save dialog
Btn_Cancel=Annuleren
#XFLD title of objects section
Objects=Objecten
#XFLD title of required packages section
RequiredPackages=Vereiste pakketten
#XTIT title of package selection dialog
Dialog_SelectPackages=Pakketten selecteren
#XMSG no data text of package selection dialog
Dilaog_NoDataText=Geen repositorypakketten gevonden
#XMSG message of adding objects
Msg_ObjectAdded={0} objecten toegevoegd
#XMSG message of removing objects
Msg_ObjectRemoved={0} objecten verwijderd
#XMSG message of adding required packages
Msg_RequiredPackageAdded={0} vereiste pakketten toegevoegd
#XMSG message of removing required packages
Msg_RequiredPackageRemoved={0} vereiste pakketten verwijderd
#XBUT add button in toolbar
Toolbar_Btn_Add=Toevoegen
#XBUT remove button in toolbar
Toolbar_Btn_Remove=Verwijderen
#XCOL status column of objects and required packages table
Table_Col_Status=Resultaat
#XLNK more link of status details
Status_More_Link=Details weergeven
#XFLD folder name
FolderName=Naam
#XFLD folder description
FolderDescription=Omschrijving
#XBUT ok button
Btn_OK=OK
#XFLD title of export location dialog
ExportLocationDialog=Exportlocatie
#XFLD lable of category field
Category=Categorie
#XFLD label of location field
Location=Doellocatie
#XFLD lable of version field
SemanticVersion=Versie
#XFLD label of current exported version
CurrentExportedPackage=Huidige geëxporteerde versie
#XFLD label of status field
ExportStatus=Status
#XFLD tooltip of export button
Tooltip_Export=Exporteren
#XFLD tooltip of save button
Tooltip_Save=Opslaan
#XFLD tooltip of validate button
Tooltip_Validate=Valideren
#XMSG
InvalidVersion=Een versie invoeren in de vorm van "x.y.z".
#XMSG
VersionLowerThanBefore=Versie kan niet lager zijn dan vorige versie.
#XMSG
Empty_Version=Voer een pakketversie in.
#XMSG
Package_Missing_Technical_Name=Voer een technische naam in voor het pakket.
#=============from skyline============

#XFLD lable for space combo selection
Spaces=Ruimte:
#XFLD section title of landing page
Packages=Pakketten
#XCOL business name column
Table_Col_BusinessName=Objectnaam
#XCOL technical name column
Table_Col_TechnicalName=Technische naam
#XCOL sapce column
Table_Col_Space=Ruimte
#XCOL create on column
Table_Col_CreatedOn=Gemaakt op
#XCOL entity type column
Table_Col_Type=Type
#XCOL entity type column
Table_Col_Object_Status=Status
#XMSG message of deleting packages
Msg_PackageRemoved={0} pakketten verwijderd
#XMIT menu item of all spaces
Op_AllSpaces=Alle ruimten
#XFLD default business name of a new package
NewPackage_BusinessName=Pakket 1
#XFLD default technical name of a new package
NewPackage_TechnicalName=Pakket 1
#XFLD default name of a new package displayed in breadcrumb
Breadcrumb_NewPackage_Name=Nieuw pakket
#XFLD title of object selection dialog
ObjectsDialog_Title=Objecten toevoegen
#XMSG dependencies are fully resolved
Dependency_Resolved=Gereed om toe te voegen (alle afhankelijkheden opgelost ({0}))
#XMSG dependencies are partially resolved
Dependency_Partial_Resolved=Gereed om toe te voegen (sommige afhankelijkheden niet opgelost ({0}/{1})
#XMSG dependencies are not found
Dependency_Not_Found=Afhankelijkheden niet gevonden
#XMSG dependencies are in other package
Dependency_In_Other_Package=Kan niet toevoegen: al in pakket "{0}"
#XMSG dependencies are in other space
Dependency_In_Other_Space=Kan niet toevoegen: in ruimte {0}
#XMSG
Cannot_Add_Managed_Content=Kan niet toevoegen: beheerde content
#XMSG
Cannot_Add_Due_To_Dependency_Has_Managed_Content=Kan niet toevoegen: afhankelijkheid heeft beheerde content
#XMSG dependencies are in current space
Dependency_In_Current_Package=Al in pakket
#XMSG dependencies are in required package
Dependency_In_Required_Package=Al in vereist pakket
#XMSG package arelady exists
Package_Duplicate_Name=Pakket "{0}" bestaat al in de repository. Voer een andere naam in.
#XMSG package name is required
Package_Name_Missing=Voer een pakketnaam in.
#XMSG package version is required
Package_Version_Missing=Voer een pakketversie in.
#XMSG package is drafted
Package_Draft_Warning=Conceptpakket. Klik op "Opslaan" om afhankelijkheden te verifiëren en uw wijzigingen te bevestigen.
#XFLD title of validation message dialog
Validation_MessageBox_Title=Validatiemeldingen
#XMSG content message of validation message dialog
Validation_MessageBox_Content="{0}" is ongeldig en kan niet worden geëxporteerd. U moet alle objectafhankelijkheden volledig oplossen door de vereiste objecten handmatig toe te voegen of via een vereist pakket.
#XBUT save anyway button
Save_Anyway=Toch opslaan
#XMSG package is valid
Valid_Package_Message=Het pakket is geldig.
#XMSG
Object_In_Other_Pacakge=Object "{0}" is in pakket "{1}".
#XMSG
Dependent_Object_In_Other_Pacakge=Object "{0}" in pakket "{1}" dat niet opgegeven is als vereist pakket.
#XMSG
Dependent_Object_Missing=Object "{0}" in ruimte {1}.
#XMSG
Dependent_Missing_Pattern=Object "{0}" is afhankelijk van:\r\n{1}
#XMSG: error message for saving the package
Save_Error_Message=Kon pakket niet opslaan. {0}
#XFLD title of save busy dialog
Title_Save_Busy_Dialog=Bezig met opslaan
#XMSG: message of it takes long time to save
Long_Time_Save_Busy_Dialog=Opslaan duurt langer dan normaal. Even geduld.
#XMSG: erro message of missing dependency
Dependency_Missing=Afhankelijkheid ontbreekt.
#XMSG: erro message of missing dependency
Object_Dependency_Missing=Afhankelijkheid van "{0}" ontbreekt.
#XMSG message of validating busy dialog
Validating_Package=Bezig met valideren pakket
#XFLD title of validating busy dialog
Title_Validating_Busy_Dialog=Bezig met verwerken
#XMSG %%0 is a placeholder, don’t change it
Missing_Dependency=Ontbrekende afhankelijkheden %%0
#XMSG %%0 is a placeholder, don’t change it
Circular_Dependency=Circulaire afhankelijkheid %%0
#XMSG Object is added to package successfully
Successfully_Add=Toegevoegd
#XMSG
Circular_Dependency_Detail="{0}" kan niet worden opgeslagen omdat het een circulaire afhankelijkheid heeft van pakket "{1}".
#XFLD label of searching objects in space
Search_In_Sapce=Zoeken in: {0}
#XFLD title of deleting package confirm dialog
Package_Deleting_Dialog_Title=Verwijderen
#XMSG content message of deleting package confirm dialog
Package_Deleting_Dialog_Message=Wilt u de geselecteerde pakketten verwijderen?
#XMSG
Package_Cannot_Delete=Het geselecteerde pakket wordt door andere pakketten gebruikt en kan niet worden verwijderd.
#XMSG
Package_Required_By_Singular=(vereist door {0} pakket)
#XMSG
Package_Required_By_Plural=(vereist door {0} pakketten)
#XFLD display name of owner column
owner=Eigenaar
#XFLD title of deleting package error dialog
Deleting_Error_Dialog_Title=Fout
#XFLD title of deleting package alert dialog
Deleting_Alert_Dialog_Title=Waarschuwing
#XBUT
Deleting_Alert_Dialog_Ok=OK
#XFLD
MyContent=Mijn inhoud
#XFLD
NotExported=Niet-geëxporteerd
#XFLD
Exporting=Exporteren
#XFLD
Exported=Geëxporteerd
#XFLD
DesignTimeError=Ontwerptijdfout
#XFLD
ExportFailed=Export mislukt
#XFLD
Cancelling=Bezig met annuleren
#XFLD
ChangesToExport=Te exporteren wijzigingen
#XMSG
Exporting_Package={0} wordt geëxporteerd. U krijgt een melding zodra het proces is voltooid.
#XFLD: Object type is businessEntityVariant
TypeBusinessEntityVariant=Businessentiteitsversie
#XFLD: Object type is businessEntity
TypeBusinessEntity=Businessentiteit
#XFLD
Tooltip_Add=Toevoegen
#XFLD
Tooltip_Edit=Bewerken
#XFLD
Tooltip_Delete=Verwijderen
#XFLD
Tooltip_Refresh=Vernieuwen
#XMSG
ExportToOverwrite=Dit pakket is al geëxporteerd naar het Content Network met versie {0}. Overschrijven?
# XFLD Column businessName
Ext_Selection_Col_Name=Naam
# XFLD Column Location
Ext_Selection_Col_Location=Locatie
# XFLD
Col_Name=Naam
# XFLD
Col_Description=Omschrijving
# XFLD Label
MoveTo_Label=Verplaatsen naar map
#XMIT Add versions menu button text in Data Builder
versions=Versies
createVersion=Versie creëren
versionHistory=Versiehistorie
#XMSG
Package_Depends_On_DP=Die pakket is afhankelijk van gegevensproducten
#XMSG
Package_Depends_On_DP_Warning=Voordat u dit pakket importeert, zorgt u dat de volgende gegevensproducten beschikbaar zijn in de doelruimte:\r\n\r\n{0}
# XFLD
Export_Confirm_Dailog_Title=Bevestiging
#XMSG
Package_Contains_DataObjects=Dit pakket bevat de volgende objecten met gegevens voor transport:\r\n\r\n{0} \r\n\r\nZorg ervoor dat persoonlijke of gevoelige gegevens niet onjuist worden weergegeven als gevolg van het transporteren van deze objecten in uw pakket.
