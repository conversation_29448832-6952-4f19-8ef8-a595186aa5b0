# https://wiki.wdf.sap.corp/wiki/display/DWAAS/How+to+add+text+classifications+for+translatable+texts
#
# use the following text types for short texts (up to 120 characters)
# XACT Accessibility
# XBUT Button
# XCKL Checkbox
# XCOL Column header
# XFLD Label
# XGRP Group title
# XLNK Hyperlink
# XLOG Log entry
# XLST List box item
# XMIT Menu item
# XMSG Message
# XRBL Radio button
# XSEL Selection
# XTIT Table title
# XTOL Tooltip
#
# for longer texts (more than 120 characters) use
# YINS Instruction
# YMSG Message

#=============from skyline============
#XFLD general tab of package editor
Tab_General=Umum
#XFLD objects tab of package editor
Tab_Objects=Objek
#XFLD required packages tab of package editor
Tab_RequiredPkgs=Pakej Diperlukan
#XFLD label for business name input
BusinessName=Nama Perniagaan
#XFLD label for technical name input
TechnicalName=Nama Teknikal
#XFLD label for purpose input
BusinessPurpose=Tujuan Perniagaan
#XTIT title of save dialog
Dilaog_Save=Simpan
#XBUT save button of save dialog
Btn_Save=Simpan
#XBUT cancel button of save dialog
Btn_Cancel=Batalkan
#XFLD title of objects section
Objects=Objek
#XFLD title of required packages section
RequiredPackages=Pakej Diperlukan
#XTIT title of package selection dialog
Dialog_SelectPackages=Pilih Pakej
#XMSG no data text of package selection dialog
Dilaog_NoDataText=Cari Pakej Repositori Semula
#XMSG message of adding objects
Msg_ObjectAdded=Anda telah menambah {0} objek
#XMSG message of removing objects
Msg_ObjectRemoved=Anda telah mengeluarkan {0} objek
#XMSG message of adding required packages
Msg_RequiredPackageAdded=Anda telah menambah {0} objek yang diperlukan
#XMSG message of removing required packages
Msg_RequiredPackageRemoved=Anda telah mengeluarkan {0} objek yang diperlukan
#XBUT add button in toolbar
Toolbar_Btn_Add=Tambah
#XBUT remove button in toolbar
Toolbar_Btn_Remove=Keluarkan
#XCOL status column of objects and required packages table
Table_Col_Status=Hasil
#XLNK more link of status details
Status_More_Link=Tunjukkan butiran...
#XFLD folder name
FolderName=Nama
#XFLD folder description
FolderDescription=Perihalan
#XBUT ok button
Btn_OK=OK
#XFLD title of export location dialog
ExportLocationDialog=Lokasi Eksport
#XFLD lable of category field
Category=Kategori
#XFLD label of location field
Location=Lokasi Sasaran
#XFLD lable of version field
SemanticVersion=Versi
#XFLD label of current exported version
CurrentExportedPackage=Versi Eksport Semasa
#XFLD label of status field
ExportStatus=Status
#XFLD tooltip of export button
Tooltip_Export=Eksport
#XFLD tooltip of save button
Tooltip_Save=Simpan
#XFLD tooltip of validate button
Tooltip_Validate=Sahkan
#XMSG
InvalidVersion=Masukkan versi dalam bentuk "x.y.z".
#XMSG
VersionLowerThanBefore=Versi mesti lebih tinggi daripada versi sebelumnya.
#XMSG
Empty_Version=Masukkan versi pakej.
#XMSG
Package_Missing_Technical_Name=Masukkan nama teknikal untuk pakej.
#=============from skyline============

#XFLD lable for space combo selection
Spaces=Ruang:
#XFLD section title of landing page
Packages=Pakej
#XCOL business name column
Table_Col_BusinessName=Nama Perniagaan
#XCOL technical name column
Table_Col_TechnicalName=Nama Teknikal
#XCOL sapce column
Table_Col_Space=Ruang
#XCOL create on column
Table_Col_CreatedOn=Dicipta pada
#XCOL entity type column
Table_Col_Type=Jenis
#XCOL entity type column
Table_Col_Object_Status=Status
#XMSG message of deleting packages
Msg_PackageRemoved={0} pakej dikeluarkan
#XMIT menu item of all spaces
Op_AllSpaces=Semua Ruang
#XFLD default business name of a new package
NewPackage_BusinessName=Pakej 1
#XFLD default technical name of a new package
NewPackage_TechnicalName=Package_1
#XFLD default name of a new package displayed in breadcrumb
Breadcrumb_NewPackage_Name=Pakej Baharu
#XFLD title of object selection dialog
ObjectsDialog_Title=Tambah Objek
#XMSG dependencies are fully resolved
Dependency_Resolved=Sedia untuk Tambah (Semua Kebersandaran Selesai ({0}))
#XMSG dependencies are partially resolved
Dependency_Partial_Resolved=Sedia untuk Tambah (Beberapa Kebersandaran Tidak Selesai ({0}/{1})
#XMSG dependencies are not found
Dependency_Not_Found=Cari kebersandaran semula
#XMSG dependencies are in other package
Dependency_In_Other_Package=Tidak Boleh Tambah: Sudah Berada dalam Pakej "{0}"
#XMSG dependencies are in other space
Dependency_In_Other_Space=Tidak Boleh Tambah: Dalam Ruang {0}
#XMSG
Cannot_Add_Managed_Content=Tidak Boleh Tambah: Kandungan Diuruskan
#XMSG
Cannot_Add_Due_To_Dependency_Has_Managed_Content=Tidak Boleh Tambah: Kebersandaran Mempunyai Kandungan Diuruskan
#XMSG dependencies are in current space
Dependency_In_Current_Package=Sudah Berada dalam Pakej
#XMSG dependencies are in required package
Dependency_In_Required_Package=Sudah Berada dalam Pakej yang Diperlukan
#XMSG package arelady exists
Package_Duplicate_Name=Pakej "{0}" telah wujud dalam repositori. Masukkan nama lain.
#XMSG package name is required
Package_Name_Missing=Masukkan nama pakej.
#XMSG package version is required
Package_Version_Missing=Masukkan versi pakej.
#XMSG package is drafted
Package_Draft_Warning=Pakej draf. Klik "Simpan" untuk sahkan kebersandaran dan perubahan anda.
#XFLD title of validation message dialog
Validation_MessageBox_Title=Mesej Pengesahan
#XMSG content message of validation message dialog
Validation_MessageBox_Content=Masukkan "{0}" yang sah untuk dieksport. Anda mesti selesaikan sepenuhnya semua kebersandaran objek dengan menambah objek yang perlu secara manual melalui pakej yang diperlukan.
#XBUT save anyway button
Save_Anyway=Simpan Juga
#XMSG package is valid
Valid_Package_Message=Pakej sah.
#XMSG
Object_In_Other_Pacakge=Objek "{0}" berada dalam pakej "{1}".
#XMSG
Dependent_Object_In_Other_Pacakge=Objek "{0}" dalam pakej "{1}", yang tidak ditentukan sebagai pakej yang diperlukan.
#XMSG
Dependent_Object_Missing=Objek "{0}" dalam ruang {1}.
#XMSG
Dependent_Missing_Pattern=Objek "{0}" bersandar pada:\r\n{1}
#XMSG: error message for saving the package
Save_Error_Message=Simpan pakej semula. {0}
#XFLD title of save busy dialog
Title_Save_Busy_Dialog=Menyimpan
#XMSG: message of it takes long time to save
Long_Time_Save_Busy_Dialog=Perlukan lebih masa daripada biasa untuk simpan. Tunggu.
#XMSG: erro message of missing dependency
Dependency_Missing=Masukkan kebersandaran.
#XMSG: erro message of missing dependency
Object_Dependency_Missing=Masukkan kebersandaran "{0}".
#XMSG message of validating busy dialog
Validating_Package=Mengesahkan Pakej
#XFLD title of validating busy dialog
Title_Validating_Busy_Dialog=Memproses
#XMSG %%0 is a placeholder, don’t change it
Missing_Dependency=Masukkan kebersandaran %%0
#XMSG %%0 is a placeholder, don’t change it
Circular_Dependency=Kebersandaran pekeliling %%0
#XMSG Object is added to package successfully
Successfully_Add=Ditambah
#XMSG
Circular_Dependency_Detail=Simpan "{0}" semula kerana ia mengandungi kebersandaran pekeliling dengan pakej "{1}".
#XFLD label of searching objects in space
Search_In_Sapce=Carian dalam: {0}
#XFLD title of deleting package confirm dialog
Package_Deleting_Dialog_Title=Padam
#XMSG content message of deleting package confirm dialog
Package_Deleting_Dialog_Message=Adakah anda ingin memadam pakej dipilih?
#XMSG
Package_Cannot_Delete=Pakej dipilih diperlukan oleh pakej lain dan tidak berjaya dipadam.
#XMSG
Package_Required_By_Singular=(diperlukan oleh {0} pakej)
#XMSG
Package_Required_By_Plural=(diperlukan oleh {0} pakej)
#XFLD display name of owner column
owner=Pemilik
#XFLD title of deleting package error dialog
Deleting_Error_Dialog_Title=Ralat
#XFLD title of deleting package alert dialog
Deleting_Alert_Dialog_Title=Amaran
#XBUT
Deleting_Alert_Dialog_Ok=OK
#XFLD
MyContent=Kandungan Saya
#XFLD
NotExported=Tidak Dieksport
#XFLD
Exporting=Mengeksport
#XFLD
Exported=Dieksport
#XFLD
DesignTimeError=Ralat Masa Reka Bentuk
#XFLD
ExportFailed=Cuba Eksport Semula
#XFLD
Cancelling=Membatalkan
#XFLD
ChangesToExport=Perubahan kepada Eksport
#XMSG
Exporting_Package=Mengeksport {0}. Kami akan memaklumkan anda apabila proses selesai.
#XFLD: Object type is businessEntityVariant
TypeBusinessEntityVariant=Versi Entiti Perniagaan
#XFLD: Object type is businessEntity
TypeBusinessEntity=Entiti Perniagaan
#XFLD
Tooltip_Add=Tambah
#XFLD
Tooltip_Edit=Edit
#XFLD
Tooltip_Delete=Padam
#XFLD
Tooltip_Refresh=Segar Semula
#XMSG
ExportToOverwrite=Anda telah mengeksport pakej ini kepada Rangkaian Kandungan dengan versi {0}. Anda mahu tulis ganti?
# XFLD Column businessName
Ext_Selection_Col_Name=Nama
# XFLD Column Location
Ext_Selection_Col_Location=Lokasi
# XFLD
Col_Name=Nama
# XFLD
Col_Description=Perihalan
# XFLD Label
MoveTo_Label=Alih ke Folder
#XMIT Add versions menu button text in Data Builder
versions=Versi
createVersion=Cipta Versi
versionHistory=Sejarah Versi
#XMSG
Package_Depends_On_DP=Pakej ini bergantung pada produk data
#XMSG
Package_Depends_On_DP_Warning=Sebelum mengimport pakej ini, pastikan produk data berikut tersedia untuk ruang sasaran:\r\n\r\n{0}
# XFLD
Export_Confirm_Dailog_Title=Pengesahan
#XMSG
Package_Contains_DataObjects=Pakej ini termasuk objek berikut yang mengandungi data untuk pindahan:\r\n\r\n{0} \r\n\r\nPastikan tiada data peribadi atau sensitif yang tidak akan didedahkan secara tidak betul sebagai hasil pindahan objek ini dalam pakej anda.
