# https://wiki.wdf.sap.corp/wiki/display/DWAAS/How+to+add+text+classifications+for+translatable+texts
#
# use the following text types for short texts (up to 120 characters)
# XACT Accessibility
# XBUT Button
# XCKL Checkbox
# XCOL Column header
# XFLD Label
# XGRP Group title
# XLNK Hyperlink
# XLOG Log entry
# XLST List box item
# XMIT Menu item
# XMSG Message
# XRBL Radio button
# XSEL Selection
# XTIT Table title
# XTOL Tooltip
#
# for longer texts (more than 120 characters) use
# YINS Instruction
# YMSG Message

#=============from skyline============
#XFLD general tab of package editor
Tab_General=ทั่วไป
#XFLD objects tab of package editor
Tab_Objects=ออบเจค
#XFLD required packages tab of package editor
Tab_RequiredPkgs=แพคเกจที่ต้องการ
#XFLD label for business name input
BusinessName=ชื่อทางธุรกิจ
#XFLD label for technical name input
TechnicalName=ชื่อทางเทคนิค
#XFLD label for purpose input
BusinessPurpose=วัตถุประสงค์ทางธุรกิจ
#XTIT title of save dialog
Dilaog_Save=เก็บบันทึก
#XBUT save button of save dialog
Btn_Save=เก็บบันทึก
#XBUT cancel button of save dialog
Btn_Cancel=ยกเลิก
#XFLD title of objects section
Objects=ออบเจค
#XFLD title of required packages section
RequiredPackages=แพคเกจที่ต้องการ
#XTIT title of package selection dialog
Dialog_SelectPackages=เลือกแพคเกจ
#XMSG no data text of package selection dialog
Dilaog_NoDataText=ไม่พบแพคเกจพื้นที่เก็บข้อมูล
#XMSG message of adding objects
Msg_ObjectAdded=เพิ่มออบเจคแล้ว {0} รายการ
#XMSG message of removing objects
Msg_ObjectRemoved=ย้ายออบเจคออกแล้ว {0} รายการ
#XMSG message of adding required packages
Msg_RequiredPackageAdded=เพิ่มแพคเกจที่ต้องการแล้ว {0} รายการ
#XMSG message of removing required packages
Msg_RequiredPackageRemoved=ย้ายแพคเกจที่ต้องการออกแล้ว {0} รายการ
#XBUT add button in toolbar
Toolbar_Btn_Add=เพิ่ม
#XBUT remove button in toolbar
Toolbar_Btn_Remove=ย้ายออก
#XCOL status column of objects and required packages table
Table_Col_Status=ผลลัพธ์
#XLNK more link of status details
Status_More_Link=แสดงรายละเอียด...
#XFLD folder name
FolderName=ชื่อ
#XFLD folder description
FolderDescription=คำอธิบาย
#XBUT ok button
Btn_OK=ตกลง
#XFLD title of export location dialog
ExportLocationDialog=เอ็กซ์ปอร์ตที่ตั้ง
#XFLD lable of category field
Category=หมวด
#XFLD label of location field
Location=ที่ตั้งเป้าหมาย
#XFLD lable of version field
SemanticVersion=เวอร์ชัน
#XFLD label of current exported version
CurrentExportedPackage=เวอร์ชันปัจจุบันที่เอ็กซ์ปอร์ต
#XFLD label of status field
ExportStatus=สถานะ
#XFLD tooltip of export button
Tooltip_Export=เอ็กซ์ปอร์ต
#XFLD tooltip of save button
Tooltip_Save=เก็บบันทึก
#XFLD tooltip of validate button
Tooltip_Validate=ตรวจสอบความถูกต้อง
#XMSG
InvalidVersion=ป้อนเวอร์ชันในรูปแบบ "x.y.z"
#XMSG
VersionLowerThanBefore=เวอร์ชันต้องไม่ต่ำกว่าเวอร์ชันก่อนหน้า
#XMSG
Empty_Version=ป้อนเวอร์ชันของแพคเกจ
#XMSG
Package_Missing_Technical_Name=ป้อนชื่อทางเทคนิคของแพคเกจ
#=============from skyline============

#XFLD lable for space combo selection
Spaces=พื้นที่:
#XFLD section title of landing page
Packages=แพคเกจ
#XCOL business name column
Table_Col_BusinessName=ชื่อทางธุรกิจ
#XCOL technical name column
Table_Col_TechnicalName=ชื่อทางเทคนิค
#XCOL sapce column
Table_Col_Space=พื้นที่
#XCOL create on column
Table_Col_CreatedOn=สร้างเมื่อ
#XCOL entity type column
Table_Col_Type=ประเภท
#XCOL entity type column
Table_Col_Object_Status=สถานะ
#XMSG message of deleting packages
Msg_PackageRemoved=ย้ายแพคเกจออกแล้ว {0} รายการ
#XMIT menu item of all spaces
Op_AllSpaces=พื้นที่ทั้งหมด
#XFLD default business name of a new package
NewPackage_BusinessName=แพคเกจ 1
#XFLD default technical name of a new package
NewPackage_TechnicalName=Package_1
#XFLD default name of a new package displayed in breadcrumb
Breadcrumb_NewPackage_Name=ออบเจคใหม่
#XFLD title of object selection dialog
ObjectsDialog_Title=เพิ่มออบเจค
#XMSG dependencies are fully resolved
Dependency_Resolved=พร้อมที่จะเพิ่ม (แก้ไขความสัมพันธ์ทั้งหมดแล้ว ({0}))
#XMSG dependencies are partially resolved
Dependency_Partial_Resolved=พร้อมที่จะเพิ่ม (ความสัมพันธ์บางรายการไม่ได้รับการแก้ไข ({0}/{1}))
#XMSG dependencies are not found
Dependency_Not_Found=ไม่พบความสัมพันธ์
#XMSG dependencies are in other package
Dependency_In_Other_Package=ไม่สามารถเพิ่มได้: อยู่ในแพคเกจ "{0}" แล้ว
#XMSG dependencies are in other space
Dependency_In_Other_Space=ไม่สามารถเพิ่มได้: ในพื้นที่ {0}
#XMSG
Cannot_Add_Managed_Content=ไม่สามารถเพิ่มได้: เนื้อหาที่มีการจัดการ
#XMSG
Cannot_Add_Due_To_Dependency_Has_Managed_Content=ไม่สามารถเพิ่มได้: ความสัมพันธ์มีเนื้อหาที่มีการจัดการ
#XMSG dependencies are in current space
Dependency_In_Current_Package=อยู่ในแพคเกจแล้ว
#XMSG dependencies are in required package
Dependency_In_Required_Package=อยู่ในแพคเกจที่ต้องการแล้ว
#XMSG package arelady exists
Package_Duplicate_Name=แพคเกจ ''{0}'' มีอยู่ในพื้นที่เก็บข้อมูลแล้ว กรุณาป้อนชื่ออื่น
#XMSG package name is required
Package_Name_Missing=ป้อนชื่อแพคเกจ
#XMSG package version is required
Package_Version_Missing=ป้อนเวอร์ชันของแพคเกจ
#XMSG package is drafted
Package_Draft_Warning=แพคเกจแบบร่าง คลิก "เก็บบันทึก" เพื่อตรวจสอบความสัมพันธ์และยืนยันการเปลี่ยนแปลงของคุณ
#XFLD title of validation message dialog
Validation_MessageBox_Title=ข้อความการตรวจสอบความถูกต้อง
#XMSG content message of validation message dialog
Validation_MessageBox_Content="{0}" ไม่ถูกต้องและไม่สามารถเอ็กซ์ปอร์ตได้ คุณต้องแก้ไขความสัมพันธ์ของออบเจคทั้งหมดให้ครบโดยการเพิ่มออบเจคที่จำเป็นด้วยตนเองหรือผ่านแพคเกจที่ต้องการ
#XBUT save anyway button
Save_Anyway=ยืนยันการเก็บบันทึก
#XMSG package is valid
Valid_Package_Message=แพคเกจถูกต้อง
#XMSG
Object_In_Other_Pacakge=ออบเจค "{0}" อยู่ในแพคเกจ "{1}"
#XMSG
Dependent_Object_In_Other_Pacakge=ออบเจค "{0}" ในแพคเกจ "{1}" ซึ่งไม่ได้ระบุเป็นแพคเกจที่ต้องการ
#XMSG
Dependent_Object_Missing=ออบเจค "{0}" ในพื้นที่ {1}
#XMSG
Dependent_Missing_Pattern=ออบเจค "{0}" ขึ้นอยู่กับ:\r\n{1}
#XMSG: error message for saving the package
Save_Error_Message=ไม่สามารถเก็บบันทึกแพคเกจ {0}
#XFLD title of save busy dialog
Title_Save_Busy_Dialog=กำลังเก็บบันทึก
#XMSG: message of it takes long time to save
Long_Time_Save_Busy_Dialog=การเก็บบันทึกใช้เวลานานกว่าปกติ กรุณารอสักครู่
#XMSG: erro message of missing dependency
Dependency_Missing=ความสัมพันธ์ขาดหายไป
#XMSG: erro message of missing dependency
Object_Dependency_Missing=ความสัมพันธ์ของ "{0}" ขาดหายไป
#XMSG message of validating busy dialog
Validating_Package=กำลังตรวจสอบความถูกต้องของแพคเกจ
#XFLD title of validating busy dialog
Title_Validating_Busy_Dialog=กำลังประมวลผล
#XMSG %%0 is a placeholder, don’t change it
Missing_Dependency=ความสัมพันธ์ที่ขาดหายไป %%0
#XMSG %%0 is a placeholder, don’t change it
Circular_Dependency=ความสัมพันธ์แบบวงกลม %%0
#XMSG Object is added to package successfully
Successfully_Add=เพิ่มแล้ว
#XMSG
Circular_Dependency_Detail=ไม่สามารถเก็บบันทึก "{0}" ได้เนื่องจากมีความสัมพันธ์แบบวงกลมกับแพคเกจ "{1}"
#XFLD label of searching objects in space
Search_In_Sapce=ค้นหาใน: {0}
#XFLD title of deleting package confirm dialog
Package_Deleting_Dialog_Title=ลบ
#XMSG content message of deleting package confirm dialog
Package_Deleting_Dialog_Message=คุณต้องการลบแพคเกจที่เลือกหรือไม่?
#XMSG
Package_Cannot_Delete=แพคเกจที่เลือกจำเป็นสำหรับแพคเกจอื่นและไม่สามารถลบได้
#XMSG
Package_Required_By_Singular=(จำเป็นสำหรับ {0} แพคเกจ)
#XMSG
Package_Required_By_Plural=(จำเป็นสำหรับ {0} แพคเกจ)
#XFLD display name of owner column
owner=เจ้าของ
#XFLD title of deleting package error dialog
Deleting_Error_Dialog_Title=ข้อผิดพลาด
#XFLD title of deleting package alert dialog
Deleting_Alert_Dialog_Title=การแจ้งเตือน
#XBUT
Deleting_Alert_Dialog_Ok=ตกลง
#XFLD
MyContent=เนื้อหาของฉัน
#XFLD
NotExported=ไม่ได้เอ็กซ์ปอร์ต
#XFLD
Exporting=กำลังเอ็กซ์ปอร์ต
#XFLD
Exported=เอ็กซ์ปอร์ตแล้ว
#XFLD
DesignTimeError=ข้อผิดพลาดของ Design Time
#XFLD
ExportFailed=การเอ็กซ์ปอร์ตล้มเหลว
#XFLD
Cancelling=กำลังยกเลิก
#XFLD
ChangesToExport=การเปลี่ยนแปลงการเอ็กซ์ปอร์ต
#XMSG
Exporting_Package=กำลังเอ็กซ์ปอร์ต {0} เราจะแจ้งให้คุณทราบเมื่อกระบวนการเสร็จสมบูรณ์
#XFLD: Object type is businessEntityVariant
TypeBusinessEntityVariant=เวอร์ชันของเอนทิตี้ธุรกิจ
#XFLD: Object type is businessEntity
TypeBusinessEntity=เอนทิตี้ธุรกิจ
#XFLD
Tooltip_Add=เพิ่ม
#XFLD
Tooltip_Edit=แก้ไข
#XFLD
Tooltip_Delete=ลบ
#XFLD
Tooltip_Refresh=รีเฟรช
#XMSG
ExportToOverwrite=แพคเกจนี้ถูกเอ็กซ์ปอร์ตไปยังเครือข่ายเนื้อหาเวอร์ชัน {0} แล้ว คุณต้องการเขียนทับหรือไม่?
# XFLD Column businessName
Ext_Selection_Col_Name=ชื่อ
# XFLD Column Location
Ext_Selection_Col_Location=ที่ตั้ง
# XFLD
Col_Name=ชื่อ
# XFLD
Col_Description=คำอธิบาย
# XFLD Label
MoveTo_Label=ย้ายไปยังแฟ้ม
#XMIT Add versions menu button text in Data Builder
versions=เวอร์ชัน
createVersion=สร้างเวอร์ชัน
versionHistory=ประวัติเวอร์ชัน
#XMSG
Package_Depends_On_DP=แพคเกจนี้ขึ้นอยู่กับผลิตภัณฑ์ข้อมูล
#XMSG
Package_Depends_On_DP_Warning=ก่อนที่จะอิมปอร์ตแพคเกจนี้ กรุณาตรวจสอบให้แน่ใจว่าผลิตภัณฑ์ข้อมูลต่อไปนี้พร้อมใช้งานในพื้นที่เป้าหมาย:\r\n\r\n{0}
# XFLD
Export_Confirm_Dailog_Title=การยืนยัน
#XMSG
Package_Contains_DataObjects=แพคเกจนี้มีออบเจคต่อไปนี้ซึ่งมีข้อมูลสำหรับการทรานสปอร์ต:\r\n\r\n{0} \r\n\r\nกรุณาตรวจสอบให้แน่ใจว่าจะไม่มีการแสดงข้อมูลส่วนบุคคลหรือข้อมูลที่ละเอียดอ่อนอย่างไม่เหมาะสมเนื่องจากการทรานสปอร์ตออบเจคเหล่านี้ในแพคเกจของคุณ
