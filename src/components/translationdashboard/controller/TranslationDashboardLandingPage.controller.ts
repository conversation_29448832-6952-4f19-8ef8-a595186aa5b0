/**
 * Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { AppExtensionsManager } from "@sap/app-extension-fwk";
import { RepositoryObjectType } from "@sap/deepsea-types";
import { ShellUIService } from "@sap/orca-shell";
import {
  BaseController,
  BaseControllerClass,
  smartExtend,
} from "../../basecomponent/controller/BaseController.controller";
import { openExplorerSelectorDialogExt } from "../../reuse/control/explorerselectordialog/openExplorerSelectorDialogExt";
import { PrivilegeType } from "../../reuse/utility/Constants";
import { ContentType, DataType, ServiceCall } from "../../reuse/utility/ServiceCall";
import { ShellContainer } from "../../shell/utility/Container";
import { Crud } from "../../shell/utility/Crud";
import { LanguagePack } from "../../shell/utility/LanguagePack";
import { Repo } from "../../shell/utility/Repo";
import { User } from "../../shell/utility/User";

export class TranslationDashboardLandingClass extends BaseControllerClass {
  private spaceId: string;
  private router: sap.m.routing.Router;
  private dashboard: any;
  private configObj: any;
  private SDPInfo: any;
  private oObjectSelectionButton: any;
  private oNewDelete: any;

  public onInit(): void {
    super.onInit();
    this.setupViews();
    this.setupRouter();

    const innerAppData = ShellUIService.getSelectedToolContext().innerAppData;
    this.spaceId = innerAppData?.arguments?.["spaceId"];
    const i18nModel = new sap.ui.model.resource.ResourceModel({
      bundleName: require("../i18n/i18n.properties"),
    });
    this.getView().setModel(i18nModel, "i18nModel");
    this.loadTSPlugin();
  }

  public loadTSPlugin() {
    const user = User.getInstance();
    const lang =
      user.getPreference("LANGUAGE") && LanguagePack.getLanguages().includes(user.getPreference("LANGUAGE"))
        ? user.getPreference("LANGUAGE")
        : "en";
    const manager = new AppExtensionsManager({
      language: lang,
    });
    const pluginDescriptor = {
      enabled: true,
      isTranslation: true,
      // baseUrl: this.getOwnerComponent().getModel("translationservice")?.getProperty("/url"),
      baseUrl: "assets/translationdashboard/ts-ui-2025.12.0-plugin",
    };
    manager.addPlugin(pluginDescriptor);
    manager
      .importModule("sap.translation.ui.dashboard" as never)
      .then(this.onDashboardLoaded.bind(this), this.onDashboardLoadFailure.bind(this));
  }

  public updateSDPInfo(spaceId: string): any {
    const pService = ShellContainer.get().getPrivilegeService();
    const isSpaceAdminRole = pService.hasPrivilegeInScope(spaceId, PrivilegeType.DWC_SPACES, "update");
    const isModelerRole = pService.hasPrivilegeInScope(spaceId, PrivilegeType.DWC_DATABUILDER, "update");
    if (isSpaceAdminRole) {
      this.SDPInfo = {
        read: true,
        create: true,
        delete: true,
      };
    } else if (isModelerRole) {
      this.SDPInfo = {
        read: true,
        create: false,
        delete: false,
      };
    } else {
      this.SDPInfo = {
        read: false,
        create: false,
        delete: false,
      };
    }
  }

  public onDashboardLoaded(dashboard) {
    // const pService = ShellContainer.get().getPrivilegeService();
    // const translationPrivilegeData = pService.getPrivilegesByType("TRANSLATION");
    // const isSpaceAdminRole = pService.hasPrivilegeInScope(this.spaceId, PrivilegeType.DWC_SPACES, "update");
    const i18nModel = this.getView().getModel("i18nModel");
    const oBundle = i18nModel.getResourceBundle();
    const Constants = {
      RESOURCE_TYPE_TECHICAL_TYPE: {
        LOCAL_TABLE: RepositoryObjectType.DWC_LOCAL_TABLE,
        REMOTE_TABLE: RepositoryObjectType.DWC_REMOTE_TABLE,
        VIEW: RepositoryObjectType.DWC_VIEW,
        PERSPECTIVE: RepositoryObjectType.DWC_PERSPECTIVE,
        ANALYTIC_MODEL: RepositoryObjectType.DWC_ANALYTIC_MODEL,
      },
      RESOURCE_TYPE_SEMANTIC_USAGE: {
        DWC_DIMENSION: "DWC_DIMENSION",
        DWC_HIERARCHY: "DWC_HIERARCHY",
        DWC_CUBE: "DWC_CUBE",
      },
      COLUMN_ID: {
        NAME: "name",
        DESCRIPTION: "description",
        TYPE: "type",
        PATH: "path",
        CREATOR: "creator",
        SOURCELANGUAGE: "sourceLanguage",
        REQUESTEDON: "requestedOn",
        TRANSLATEDON: "translatedOn",
        STATUS: "status",
      },
      COLUMN_PROPERTY: {
        TEXT: "text",
        TOOLTIP: "tooltip",
        VISIBLE: "visible",
      },
    };

    const spaceId = this.spaceId;
    this.updateSDPInfo(spaceId);
    const self = this;
    this.configObj = {
      getGroupId: function () {
        return spaceId;
      },
      getUserProps: function () {
        return {
          getDateFormat: function () {
            return "";
          },
          getTimeFormat: function () {
            return "";
          },
          hasTranslationPrivilege: function (privilegeType) {
            return self.SDPInfo[privilegeType];
          },
        };
      },
      // eslint-disable-next-line @typescript-eslint/no-empty-function
      handleMessage: function () {},
      getButtonVisibility: function (buttonName) {
        switch (buttonName) {
          case "DELETE":
            return false;
          case "REFRESH":
          case "EDIT":
          case "DOWNLOAD_XLIFF":
          case "UPLOAD_XLIFF":
          case "SHOW":
          case "FILTER":
          case "SEARCH":
            return true;
        }
      },
      // eslint-disable-next-line @typescript-eslint/no-empty-function
      getArtifactTypeDisplayName: function (sResourceType, sResourceSubType) {
        // for DWC, the sResourceType will always be "DWC" and we use sResourceSubType to determine icon
        // The currently available DWC artifact types on Translation side are the following
        // 1. DWC_LOCAL_TABLE
        // 2. DWC_REMOTE_TABLE
        // 3. DWC_VIEW
        // 4. DWC_ANALYTIC_MODEL
        // 5. DWC_PERSPECTIVE

        // sResourceSubType now may has two format: <Technical Type> or <Technical Type> <Semantic Usage>
        const subTypeArray = sResourceSubType.split(" ");
        let translatedString = "";
        const techType = subTypeArray[0];
        switch (techType) {
          case Constants.RESOURCE_TYPE_TECHICAL_TYPE.LOCAL_TABLE:
            translatedString = oBundle.getText("LOCAL_TABLE_TYPE");
            break;
          case Constants.RESOURCE_TYPE_TECHICAL_TYPE.REMOTE_TABLE:
            translatedString = oBundle.getText("REMOTE_TABLE_TYPE");
            break;
          case Constants.RESOURCE_TYPE_TECHICAL_TYPE.VIEW:
            translatedString = oBundle.getText("VIEW_TYPE");
            break;
          case Constants.RESOURCE_TYPE_TECHICAL_TYPE.PERSPECTIVE:
            translatedString = oBundle.getText("PERSPECTIVE_TYPE");
            break;
          case Constants.RESOURCE_TYPE_TECHICAL_TYPE.ANALYTIC_MODEL:
            translatedString = oBundle.getText("ANALYTIC_MODEL_TYPE");
            break;
          default:
            translatedString = oBundle.getText("UNKNOWN_TYPE");
            break;
        }
        const isContainSemanticUsage = subTypeArray.length > 1;
        if (isContainSemanticUsage) {
          const semanticUsage = subTypeArray[1];
          translatedString += " (";
          switch (semanticUsage) {
            case Constants.RESOURCE_TYPE_SEMANTIC_USAGE.DWC_DIMENSION:
              translatedString += oBundle.getText("DWC_DIMENSION");
              break;
            case Constants.RESOURCE_TYPE_SEMANTIC_USAGE.DWC_HIERARCHY:
              translatedString += oBundle.getText("DWC_HIERARCHY");
              break;
            case Constants.RESOURCE_TYPE_SEMANTIC_USAGE.DWC_CUBE:
              translatedString += oBundle.getText("DWC_CUBE");
              break;
            default:
              translatedString += oBundle.getText("UNKNOWN_TYPE");
              break;
          }
          translatedString += ")";
        }
        return translatedString;
      },
      // eslint-disable-next-line @typescript-eslint/no-empty-function
      getSelectionMode: function () {
        // DONOT delete this or the translationservice plugin cannot be loaded
      },
      getArtifactTypeIconUri: function (sResourceType, sResourceSubType) {
        // sResourceSubType now may has two format: <Technical Type> or <Technical Type> <Semantic Usage>
        const subTypeArray = sResourceSubType.split(" ");
        const techType = subTypeArray[0];
        switch (techType) {
          case Constants.RESOURCE_TYPE_TECHICAL_TYPE.LOCAL_TABLE:
            return "sap-icon://sac/table";
          case Constants.RESOURCE_TYPE_TECHICAL_TYPE.REMOTE_TABLE:
            return "sap-icon://sac/live-table";
          case Constants.RESOURCE_TYPE_TECHICAL_TYPE.VIEW:
            return "sap-icon://sac/table-view";
          case Constants.RESOURCE_TYPE_TECHICAL_TYPE.PERSPECTIVE:
            return "sap-icon://sac/models";
          case Constants.RESOURCE_TYPE_TECHICAL_TYPE.ANALYTIC_MODEL:
            return "sap-icon://sac/models";
          default:
            return "";
        }
      },
      getProperty: function (internalColumnId, propertyType) {
        switch (internalColumnId) {
          case Constants.COLUMN_ID.NAME:
            switch (propertyType) {
              // cant modify ""visible" property for Name Column. Its set as always true
              case Constants.COLUMN_PROPERTY.TEXT:
                return oBundle.getText("technicalName");
              case Constants.COLUMN_PROPERTY.TOOLTIP:
                return oBundle.getText("technicalName");
            }
            break;
          case Constants.COLUMN_ID.DESCRIPTION:
            switch (propertyType) {
              case Constants.COLUMN_PROPERTY.VISIBLE:
                return true;
              case Constants.COLUMN_PROPERTY.TEXT:
                return oBundle.getText("businessName");
              case Constants.COLUMN_PROPERTY.TOOLTIP:
                return oBundle.getText("businessName");
            }
            break;
          case Constants.COLUMN_ID.TYPE:
            switch (propertyType) {
              case Constants.COLUMN_PROPERTY.VISIBLE:
                return true;
              case Constants.COLUMN_PROPERTY.TEXT:
                return oBundle.getText("type");
              case Constants.COLUMN_PROPERTY.TOOLTIP:
                return oBundle.getText("type");
            }
            break;
          case Constants.COLUMN_ID.PATH:
            switch (propertyType) {
              case Constants.COLUMN_PROPERTY.VISIBLE:
                return true;
              case Constants.COLUMN_PROPERTY.TEXT:
                return oBundle.getText("path");
              case Constants.COLUMN_PROPERTY.TOOLTIP:
                return oBundle.getText("path");
            }
            break;
          case Constants.COLUMN_ID.CREATOR:
            switch (propertyType) {
              case Constants.COLUMN_PROPERTY.VISIBLE:
                return true;
              case Constants.COLUMN_PROPERTY.TEXT:
                return oBundle.getText("creator");
              case Constants.COLUMN_PROPERTY.TOOLTIP:
                return oBundle.getText("creator");
            }
            break;
          case Constants.COLUMN_ID.SOURCELANGUAGE:
            switch (propertyType) {
              case Constants.COLUMN_PROPERTY.VISIBLE:
                return false;
              case Constants.COLUMN_PROPERTY.TEXT:
                return oBundle.getText("sourceLanguage");
              case Constants.COLUMN_PROPERTY.TOOLTIP:
                return oBundle.getText("sourceLanguage");
            }
            break;
          case Constants.COLUMN_ID.REQUESTEDON:
            switch (propertyType) {
              case Constants.COLUMN_PROPERTY.VISIBLE:
                return true;
              case Constants.COLUMN_PROPERTY.TEXT:
                return oBundle.getText("requestedOn");
              case Constants.COLUMN_PROPERTY.TOOLTIP:
                return oBundle.getText("requestedOn");
            }
            break;
          case Constants.COLUMN_ID.TRANSLATEDON:
            switch (propertyType) {
              case Constants.COLUMN_PROPERTY.VISIBLE:
                return true;
              case Constants.COLUMN_PROPERTY.TEXT:
                return oBundle.getText("changedOn");
              case Constants.COLUMN_PROPERTY.TOOLTIP:
                return oBundle.getText("changedOn");
            }
            break;
          case Constants.COLUMN_ID.STATUS:
            switch (propertyType) {
              case Constants.COLUMN_PROPERTY.VISIBLE:
                return true;
              case Constants.COLUMN_PROPERTY.TEXT:
                return oBundle.getText("status");
              case Constants.COLUMN_PROPERTY.TOOLTIP:
                return oBundle.getText("status");
            }
            break;
        }
      },
      icons: {
        refreshBtn: { uri: "sap-icon://refresh" },
        editBtn: { uri: "sap-icon://edit" },
        deleteBtn: { uri: "sap-icon://delete" },
        importBtn: { uri: "sap-icon://upload" },
        exportBtn: { uri: "sap-icon://download" },
        showBtn: { uri: "sap-icon://show" },
        copyBtn: { uri: "sap-icon://copy" },
        filterBtn: { uri: "sap-icon://filter" },
        success: { uri: "sap-icon://message-success" },
        warning: { uri: "sap-icon://warning" },
        alert: { uri: "sap-icon://alert" },
        sortAscending: { uri: "sap-icon://sort-ascending" },
        sortDescending: { uri: "sap-icon://sort-descending" },
      },
    };
    dashboard.setConfig(this.configObj);
    const dashboardView = dashboard.getView();
    this.dashboard = dashboard;
    const toolbar = dashboardView.getContent()[0]?.getContent()[2];
    // hide delete button in the plugin since this is used to delete translation text, dwc do not need it
    toolbar.getContent()[2]?.setVisible(false);
    this.oObjectSelectionButton = new sap.m.Button("translationDashboard--objectSelection", {
      tooltip: "",
      icon: "sap-icon://add",
      type: sap.m.ButtonType.Transparent,
      enabled: this.SDPInfo.create,
      visible: true,
      press: () => {
        ShellContainer.get()
          .getUsageCollectionService()
          .recordAction({
            action: "objSelection",
            feature: ShellUIService.getSelectedToolContext()?.toolType,
            eventtype: "click",
            options: [
              {
                param: "target",
                value: "this.getTypeofObjectInSelectedRow()",
              },
            ],
          });
        this.onOpenObjectSelectionDialog();
      },
    });

    this.oNewDelete = new sap.m.Button("translationDashboard--newDelete", {
      tooltip: "Delete",
      icon: "sap-icon://delete",
      type: sap.m.ButtonType.Transparent,
      enabled: false,
      visible: true,
      press: () => {
        ShellContainer.get()
          .getUsageCollectionService()
          .recordAction({
            action: "objectDelete",
            feature: ShellUIService.getSelectedToolContext()?.toolType,
            eventtype: "click",
            options: [
              {
                param: "target",
                value: "this.getTypeofObjectInSelectedRow()",
              },
            ],
          });
        this.onObjectDelete();
      },
    });
    toolbar.insertContent(this.oNewDelete, 2);
    toolbar.insertContent(this.oObjectSelectionButton);

    const table = dashboard.oContentList;
    table.attachRowSelectionChange(function () {
      if (self.SDPInfo.create) {
        const selectedIndices = this.getSelectedIndices();
        if (selectedIndices.length > 0) {
          self.oNewDelete.setEnabled(true);
        } else {
          self.oNewDelete.setEnabled(false);
        }
      }
    });
    table.attachRowsUpdated(function () {
      if (self.SDPInfo.create) {
        const selectedIndices = this.getSelectedIndices();
        if (selectedIndices.length === 0) {
          self.oNewDelete.setEnabled(false);
        }
      }
    });
    dashboardView.placeAt(
      "shellMainContent---translationDashboardComponent---translationDashboardLandingPage--tdScrollContainer"
    );
  }

  public onDashboardLoadFailure(error) {
    console.log("===onDashboardLoadFailure===, error---" + error);
    this.router.navTo("error", { spaceId: this.spaceId }, true);
  }

  public async getAttachedArtifactsList(): Promise<any> {
    return new Promise((resolve) => {
      const ajaxOptions: JQueryAjaxSettings = {
        url: "translation/" + this.spaceId + "/metadata",
        method: "POST",
        dataType: DataType.JSON,
      };
      const body = { artifactIds: [], retrieveAll: true };
      ajaxOptions.data = JSON.stringify(body);
      ajaxOptions.contentType = ContentType.APPLICATION_JSON;
      ServiceCall.request(ajaxOptions, true, undefined, () => "deepsea")
        .then((artifactsList) => {
          resolve((artifactsList?.data as any)?.results);
        })
        .catch(() => {
          resolve({ results: {} });
        });
    });
  }

  private setupViews() {
    this["view"] = this.getView();
  }

  private setupRouter() {
    this.router = sap.ui.core.UIComponent.getRouterFor(this) as sap.m.routing.Router;
    this.router.getRoute("landing").attachPatternMatched((params) => {
      const { spaceId } = params?.getParameter("arguments");
      this.spaceId = spaceId;

      if (this.dashboard) {
        this.updateSDPInfo(spaceId);
        this.oObjectSelectionButton.setEnabled(this.SDPInfo.create);
        this.oNewDelete.setEnabled(this.SDPInfo.create);

        const dashboardUtils = this.dashboard?.oTranslationServiceUtils;
        dashboardUtils?.setGroupId(spaceId);

        const self = this;
        this.configObj.getUserProps = function () {
          return {
            getDateFormat: function () {
              return "";
            },
            getTimeFormat: function () {
              return "";
            },
            hasTranslationPrivilege: function (privilegeType) {
              return self.SDPInfo[privilegeType];
            },
          };
        };
        // eslint-disable-next-line no-underscore-dangle
        this.dashboard?._refreshTable();
      }
    }, this);
  }

  public onObjectDelete() {
    const view = sap.ui
      .getCore()
      .byId("shellMainContent---translationDashboardComponent---translationDashboardLandingPage--tdScrollContainer");
    const selectedKeyList = this.dashboard.oContentList.getSelectedIndices();
    const artifactIds = [];
    selectedKeyList?.forEach((key) => {
      const artifactID = this.dashboard.oContentList.getContextByIndex(key).getProperty("artifactTitle");
      artifactIds.push(artifactID);
    });

    // For now, the backend API will detach all artifacts if UI send an empty array so we make a checking here
    // to avoid some unexpected scenarios happen
    if (artifactIds.length > 0) {
      const i18nModel = this.getView().getModel("i18nModel");
      const oBundle = i18nModel.getResourceBundle();
      sap.ui.require(["sap/m/MessageBox"], (MessageBox) => {
        MessageBox.confirm(oBundle.getText("DetachConfirmMsg"), {
          id: "detachArtifactMsgbox",
          styleClass: "sapUiSizeCompact",
          actions: [MessageBox.Action.OK, MessageBox.Action.CANCEL],
          initialFocus: MessageBox.Action.OK,
          onClose: (action: sap.m.MessageBox.Action) => {
            if (action === MessageBox.Action.OK) {
              view.setBusy(true);
              this.onArtifactAttachDetach(false, artifactIds).then(() => {
                // eslint-disable-next-line no-underscore-dangle
                this.dashboard?._refreshTable();
                view.setBusy(false);
              });
            }
            sap.ui.getCore().byId("detachArtifactMsgbox").destroy();
          },
        });
      });
    }
  }

  public customRowEnablement(artiList, currentRow) {
    const technicalName = currentRow?.attributesMap?.name?.value;
    // artiList contains the artifacts that already attached to the dashboard so we need to disible these lines to avoid attach them again
    if (artiList[technicalName]) {
      return false;
    } else {
      return true;
    }
  }

  public async onArtifactAttachDetach(isAttach: boolean = true, selectedObjNameList: string[]): Promise<any> {
    const spaceName = this.spaceId;
    const spaceId = await Crud.get().getGUID([{ type: Repo.space, name: spaceName }]);
    return new Promise((resolve) => {
      const ajaxOptions: JQueryAjaxSettings = {
        url: "repository/objects",
        method: "POST",
        dataType: DataType.JSON,
      };
      const body = {
        data: {
          // eslint-disable-next-line camelcase
          space_id: spaceId,
          content: {
            i18nDashboard: {
              "I18n.space.dashboard": {
                _meta: {
                  dependencies: {
                    "sap.i18n.translation": {
                      insert: isAttach ? selectedObjNameList : [],
                      remove: isAttach ? [] : selectedObjNameList,
                    },
                  },
                },
              },
            },
          },
          saveAction: "save",
        },
      };
      ajaxOptions.data = JSON.stringify(body);
      ajaxOptions.contentType = ContentType.APPLICATION_JSON;
      ServiceCall.request(ajaxOptions, true, undefined, () => "deepsea").then((data) => {
        resolve(data);
      });
    });
  }

  public onObjectSelect(evt) {
    const selectedObj = evt.getParameter("selection");
    const selectedObjNameList = [];
    selectedObj.forEach((obj) => selectedObjNameList.push(obj?.technicalName));
    const view = sap.ui
      .getCore()
      .byId("shellMainContent---translationDashboardComponent---translationDashboardLandingPage--tdScrollContainer");
    view.setBusy(true);
    this.onArtifactAttachDetach(true, selectedObjNameList).then(() => {
      // eslint-disable-next-line no-underscore-dangle
      this.dashboard?._refreshTable();
      view.setBusy(false);
    });
  }

  private async onOpenObjectSelectionDialog() {
    const spaceName = this.spaceId;

    // update latest attached artifacts list before open object selection dialog,
    // then use the list to filter out the artifacts that already attached
    const artiList = await this.getAttachedArtifactsList();
    sap.ui.require(
      [
        "sap/esh/search/ui/sinaNexTS/sina/SimpleCondition",
        "sap/esh/search/ui/sinaNexTS/sina/ComparisonOperator",
        "sap/esh/search/ui/sinaNexTS/sina/ComplexCondition",
        "sap/esh/search/ui/sinaNexTS/sina/LogicalOperator",
      ],
      (SimpleConditionModule, ComparisonOperatorModule) => {
        openExplorerSelectorDialogExt({
          styleClass: "translationDashboardSearchContainer",
          spaceName: spaceName,
          onSelect: this.onObjectSelect.bind(this),
          customRowEnablement: this.customRowEnablement.bind(this, artiList),
          configure: (configuration) => {
            configuration.isSearchUrl = function (url) {
              return url.indexOf("#/translationdashboard") === 0;
            };
            configuration.resultViewTypes = ["searchResultTable"];
            configuration.searchOnStart = true;
            const querySuffix = configuration.sinaConfiguration.querySuffix;

            const SimpleCondition = SimpleConditionModule.SimpleCondition;
            const ComparisonOperator = ComparisonOperatorModule.ComparisonOperator;

            const AnalyticModelCondition = new SimpleCondition({
              operator: ComparisonOperator.Eq,
              attribute: "technical_type",
              value: RepositoryObjectType.DWC_ANALYTIC_MODEL,
            });
            // querySuffix has a default technicalTypes defined in repository expolorer side, we should remove this condition first
            querySuffix.removeConditionAt(0);
            querySuffix.addCondition(AnalyticModelCondition);
          },
        });
      }
    );
  }
}

export const TranslationDashboardLanding: typeof TranslationDashboardLandingClass = smartExtend(
  BaseController,
  "sap.cdw.components.translationdashboard.controller.TranslationDashboardLandingPage",
  TranslationDashboardLandingClass
);

sap.ui.define(
  "sap/cdw/components/translationdashboard/controller/TranslationDashboardLandingPage.controller",
  [],
  function () {
    return TranslationDashboardLanding;
  }
);
