/** @format */

// import { IConfigCustomization } from "@sap/skyline-tools/dist/resources/sap/skyline/tools/expressionEditor/ValueHelpManager";
import { IValueHelpConfig } from "@sap/skyline-tools/dist/resources/sap/skyline/tools/valuehelp/ValueHelpDialog";
import { isNull } from "lodash";
import {
  AnalyticModelAttributeType,
  AnalyticModelCrossCalculationType,
  AnalyticModelExceptionAggregationType,
  AnalyticModelParameterType,
  AnalyticModelParameterValueBaseType,
  AnalyticModelSourceParameterMappingType,
  AnalyticModelSourceType,
  IAnalyticModelDimensionSource,
  IAnalyticModelDimensionSourceAttribute,
  IAnalyticModelSourceParameterMapping_CONSTANTVALUE,
  IAnalyticModelSourceParameterMapping_VARIABLE,
  IAnalyticModelVariableDerivation,
  IAnalyticModelVariableParameterBinding,
} from "../../../../shared/queryBuilder/AnalyticModel";
import { ReverseExceptionAggregationTypeMapping } from "../../../../shared/queryBuilder/AnalyticModelCsnMappings";
import { IParameterMappings } from "../../../../shared/queryBuilder/ConvertAnalyticalDataSetApi";
import { IDataEntityParameter } from "../../../../shared/queryBuilder/DataEntityDetails";
import { CONSTANT_VALUE_TYPES } from "../../../../shared/queryBuilder/QueryModel";
import { ValidationMessage, ValidationMessageType } from "../../../../shared/queryBuilder/QueryModelValidator";
import { BuilderConfig } from "../../abstractbuilder/api";
import { getCurrentOrCrossSpaceObjectByName } from "../../abstractbuilder/utility/RepositoryUtils";
import { smartExtend } from "../../basecomponent/controller/BaseController.controller";
import { CsnAnnotations } from "../../commonmodel/csn/csnAnnotations";
import { NameUsage } from "../../commonui/utility/NameInputValidator";
import { getDependentObjectListData } from "../../databuilder/utility/DependencyHelper";
import { SidepanelMode } from "../../ermodeler/js/statics/const/EntityPropertiesSidepanel";
import { getObjectStatusSeverity } from "../../ermodeler/js/utility/sharedFunctions";
import { MessageHandler } from "../../reuse/utility/MessageHandler";
import { EventType } from "../../shell/utility/ShellUsageCollectionService";
import { User } from "../../shell/utility/User";
import { ObjectNameDisplay } from "../../userSettings/utility/Constants";
import { UpdateParameterMappings } from "../command/sourceModel/UpdateParameterMappings";
import { SetVariable } from "../command/variables/SetVariable";
import { AnalyticModelBase, AnalyticModelBaseClass } from "../controller/AnalyticModelBase.controller";
import CubeBuilderObjects from "../utility/CubeBuilderObjects";
import {
  BreadCrumbs,
  ExpressionEditor,
  ListWithItemBase,
  NodeParameters,
  TargetEntityParams,
  ValueHelpDialogAM,
} from "../utility/CubeBuilderTypes";
import {
  ActionMappingType,
  AnalyticModelEventBusChannels,
  AnalyticModelEventBusEvents,
  AnalyticModelUIVariableSelectionType,
  CurrencyUnitConversionViewsFieldNames,
  ModelUIProperties,
} from "../utility/Enum";
import {
  attributeSupportsFilter,
  getAttributeDataType,
  getDisplayDataType,
  getSourcesFromAttribute,
} from "../utility/ModelUtils";
import { USAGE_ACTIONS, getCurrentFeatureForUsageTracking, recordUsageTrackingEvent } from "../utility/UsageTracking";
import Util from "../utility/Util";
import { AnalyticModelPropertiesWithValidationSubscription } from "./IAnalyticModelPropertiesWithValidationSubscription.controller";

const MessageBox: typeof sap.m.MessageBox = sap.ui.require("sap/m/MessageBox");

enum DEFAULT_VALUE_DIALOG_ID {
  INPUT_PARAMETER_DEFAULT_VALUE = "analyticalModelDefaultValDialog",
  LOOK_UP_ENTITY_DEFAULT_VALUE = "lookUpEntity--analyticalModelDefaultValDialog",
}

const SORT_DEPENDENT_OBJECTS_BUTTON_DATA = {
  ascending: { icon: "sap-icon://sort-ascending", tooltipResourceId: "sortAscending" },
  descending: { icon: "sap-icon://sort-descending", tooltipResourceId: "sortDescending" },
  bySeverity: { icon: "sap-icon://shield", tooltipResourceId: "sortBySeverity" },
};

export class AnalyticModelPropertiesBaseClass
  extends AnalyticModelBaseClass
  implements AnalyticModelPropertiesWithValidationSubscription
{
  public navCon: sap.m.NavContainer;

  public constructor() {
    super();
  }

  public onBeforeHide() {
    // Unsubscribe from Validator Events
    sap.ui
      .getCore()
      .getEventBus()
      .unsubscribe(
        AnalyticModelEventBusChannels.ANALYTIC_MODEL_VALIDATOR,
        AnalyticModelEventBusEvents.VALIDATOR_EXECUTED,
        this.validationListener,
        this
      );
  }

  public onBeforeShow() {
    // Subscribe to Validator Events
    sap.ui
      .getCore()
      .getEventBus()
      .subscribe(
        AnalyticModelEventBusChannels.ANALYTIC_MODEL_VALIDATOR,
        AnalyticModelEventBusEvents.VALIDATOR_EXECUTED,
        this.validationListener,
        this
      );
  }

  public onInit(): void {
    // Registers a callback to the onBeforeSow and onBeforeHide event from the view
    this.getView().addEventDelegate(
      {
        onBeforeShow: this.onBeforeShow.bind(this),
        onBeforeHide: this.onBeforeHide.bind(this),
      },
      this
    );

    super.onInit();
    // in the property views, we need additionally the breadcrumb and the header
    // the settings for the breadcrumbs and the header are by default those of the model
    // inheriting controllers should call updateHeader with the appropriate parameters.
    const breadcrumbs = {
      currentText: this.model.getBusinessName(),
      links: [],
    };
    this.uiModel.setProperty("/breadcrumbs", breadcrumbs);

    // TODO - apply the formatter to adjust according to user setting: bus vs. tech. name
    const header = {
      displayName: this.model.getBusinessName(),
      displayIcon: "sap-icon://sac/fact-view",
    };
    this.uiModel.setProperty("/header", header);
    this.view = this.getView();
  }

  /**
   * @inheritdoc
   * @implements
   */
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  public validationListener(channel: string, eventName: string, validationMessages: ValidationMessage[]) {}

  // #region Common Column List helper functions
  protected getAttributesColumnList(attributeKeysToAdd: string[] = []) {
    const allSourceDataEntityDetails = this.uiModel.getProperty("/allSourceDataEntityDetails");
    const attributes = this.model.getAttributes();

    // exclude the attribute selected as record type field for non-cumulative measures from other usages in the model
    const recordTypeAttributeKey = this.model.getNonCumulativeSettings()?.recordTypeAttributeKey;
    if (!!recordTypeAttributeKey) {
      delete attributes[recordTypeAttributeKey];
    }

    const attributeColumnList = [];
    for (const attribute in attributes) {
      if (attributeSupportsFilter(attributes[attribute], this.model, allSourceDataEntityDetails)) {
        const attributeColumn: any = {};
        this.setAttributeColumnText(attribute, attributes, attributeColumn);
        attributeColumn.key = attribute;
        attributeColumn.icon = undefined;
        attributeColumnList.push(attributeColumn);
      }
    }

    const existingAttributeKeys = new Set(attributeColumnList.map((attribute) => attribute.key));
    for (const attributeKey of attributeKeysToAdd) {
      if (!existingAttributeKeys.has(attributeKey)) {
        attributeColumnList.push({
          key: attributeKey,
          name: attributeKey,
          text: attributeKey,
        });
      }
    }
    return attributeColumnList;
  }

  protected setAttributeColumnText(attribute: string, attributes: any, attributeColumn: any) {
    const attributeDetails = this.model.getAttribute(attribute);
    const attributeInModel = attributes[attribute];
    const displayNameConfig = User.getInstance().getObjectNameDisplay();
    const showTechnicalNames = displayNameConfig === ObjectNameDisplay.technicalName;
    const { source, sourceKey } = getSourcesFromAttribute(attributeDetails, this.model);
    attributeColumn.text = `${attributeInModel.text} (${source.text})`;
    if (this.model.getDimensionHandlingCapability() && showTechnicalNames) {
      attributeColumn.name = `${attribute} (${sourceKey})`;
    } else {
      attributeColumn.name = `${attributeInModel.text} (${source.text})`;
    }
  }

  protected setSuggestionsForAttributes(
    detailsModel: sap.ui.model.json.JSONModel,
    attributes: Array<{ key: string }>,
    path = "/attributeSuggestions"
  ) {
    detailsModel.setProperty(
      path,
      attributes.map((attribute) => ({
        text: attribute.key,
      }))
    );
  }

  protected getFilterVariablesColumnList() {
    const variables = this.model.getVariables();
    const filterVariables: Array<{
      key: string;
      text: string;
      entry: string;
      icon: string;
    }> = [];
    for (const variableKey in variables) {
      const variable = variables[variableKey];
      if (variable.parameterType === AnalyticModelParameterType.Filter) {
        filterVariables.push({
          key: variableKey,
          text: variable.text,
          entry: ":" + sap.cdw.querybuilder.ViewModelToCsn.quoteName(variableKey),
          icon: "sap-icon://sac/variable",
        });
      }
    }
    return filterVariables;
  }

  protected getInputVariablesColumnList() {
    const variables = this.model.getVariables();
    const filterVariables: Array<{
      key: string;
      text: string;
      entry: string;
      icon: string;
      value?: AnalyticModelParameterValueBaseType;
    }> = [];
    for (const variableKey in variables) {
      const variable = variables[variableKey];
      if (variable.parameterType === AnalyticModelParameterType.Input) {
        filterVariables.push({
          key: variableKey,
          text: variable.text,
          entry: ":" + sap.cdw.querybuilder.ViewModelToCsn.quoteName(variableKey),
          icon: "sap-icon://sac/variable",
          value: "defaultValue" in variable ? variable.defaultValue : undefined,
        });
      }
    }
    return filterVariables;
  }

  protected setSuggestionsForVariables(
    detailsModel: sap.ui.model.json.JSONModel,
    variables: Array<{ key: string; entry: string }>,
    path = "/variableSuggestions"
  ) {
    detailsModel.setProperty(
      path,
      variables.map((variable) => ({
        text: variable.key,
        key: variable.entry,
      }))
    );
  }
  // #endregion

  private valueHelpUnavailableDialog(): void {
    MessageHandler.uiError(this.getText("valueHelpUnavailableInputParametersNotSet"));
  }

  // Value help in expression editor of restricted measure, calculated measure and global filter
  // public valueHelpCustomizer(columnName: string): IConfigCustomization {
  public valueHelpCustomizer(columnName: string): any {
    const attribute = this.model.getAttribute(columnName);
    if (!attribute) {
      MessageBox.error(this.getText("valueHelpDimensionNotFound", [columnName]));
      return undefined;
    }
    const attributeSource = getSourcesFromAttribute(attribute, this.model);

    const parameterValuesForValueHelp = this.getParameterValuesForValueHelp();

    if (!parameterValuesForValueHelp.allParametersHaveValues) {
      this.valueHelpUnavailableDialog();
      return;
    }

    const parameterValues = parameterValuesForValueHelp.parametersWithValues;

    // get reference date variable date
    const timeDependencyDate = this.getDefaultValueForReferenceDateVariable();

    // determine source attribute name for renamed attributes
    const attributeKey = this.getAttributeKey(columnName, this.model.getAttribute(columnName));
    const allSourceDataEntityDetails = this.uiModel.getProperty("/allSourceDataEntityDetails");
    const attributeType = getAttributeDataType(
      attributeSource.source.dataEntity.key,
      attributeKey,
      allSourceDataEntityDetails
    );
    recordUsageTrackingEvent({
      action: USAGE_ACTIONS.OPEN_VALUE_HELP,
      feature: getCurrentFeatureForUsageTracking(),
      eventtype: EventType.CLICK,
    });
    return {
      parameterValues,
      columnName: attributeKey,
      entityName: attributeSource.source.dataEntity.key,
      dataType: attributeType,
      spaceName: super.getSpaceName(),
      timeDependencyDate,
    };
  }

  // Submit value help: Format value help result before inserted into expression
  public valueHelpSubmitCustomizer(values = [], dialogConfig: IValueHelpConfig): string {
    let customizedValues = values;
    let customizedExpression = "";
    const valueHelpDialog = this.byId("expressionValueHelpDialog") as ValueHelpDialogAM;
    if (!valueHelpDialog || valueHelpDialog.getValueState() === "Error") {
      return customizedExpression;
    }
    if (dialogConfig.dataType === "cds.String" || dialogConfig.dataType === "cds.Date") {
      customizedValues = customizedValues.map((value) => "'" + (value as string) + "'");
    }
    if (values && dialogConfig.operator === "BETWEEN") {
      customizedExpression = customizedValues.join(" AND ");
    } else if (values) {
      customizedExpression = customizedValues.join(",");
    }
    if (dialogConfig.operator === "IN") {
      customizedExpression = "(" + customizedExpression + ")";
    }
    return customizedExpression;
  }

  public onValueHelpCalculatedMeasureSelected() {
    const valueHelpDialog = this.byId("expressionValueHelpDialogCalculated");
    this.submitSelectionValueHelp(valueHelpDialog);
  }

  public onValueHelpSelected() {
    const valueHelpDialog = this.byId("expressionValueHelpDialog");
    this.submitSelectionValueHelp(valueHelpDialog);
  }

  private submitSelectionValueHelp(valueHelpDialog: ValueHelpDialogAM) {
    const uiModel = valueHelpDialog.getUiModel();
    const selectedValues = uiModel.getProperty("/selectedValues");
    valueHelpDialog.setValueState(sap.ui.core.ValueState.None);
    if (!selectedValues || selectedValues.length === 0) {
      valueHelpDialog.setValueState(sap.ui.core.ValueState.Error);
      valueHelpDialog.setValueStateText(this.getText("valueHelpStateSelectionEmpty"));
    } else if (valueHelpDialog.getProperty("operator") === "BETWEEN" && selectedValues.length !== 2) {
      valueHelpDialog.setValueState(sap.ui.core.ValueState.Error);
      valueHelpDialog.setValueStateText(this.getText("valueHelpStateSelectionBetweenMismatch"));
    } else {
      valueHelpDialog.close();
    }
  }

  /**
   * Handler for expression editor attribute column list details (for measures and the global filter)
   * */
  public onBeforeAttributePopover(
    event: sap.ui.base.Event,
    detailsModel: sap.ui.model.json.JSONModel,
    path = "/attributeInfo"
  ) {
    const attributeTechnicalName = event.getParameter("columnName");
    const attributeDetails = this.model.getAttribute(attributeTechnicalName);
    if (!!attributeDetails) {
      const sourceLabel =
        attributeDetails.attributeType === AnalyticModelAttributeType.FactSourceAttribute
          ? this.getText("factSource")
          : this.getText("dimensionSource");
      const sourceText =
        attributeDetails.attributeType === AnalyticModelAttributeType.FactSourceAttribute
          ? this.model.getFirstFactSource().text
          : this.model.getDimensionSource((attributeDetails as IAnalyticModelDimensionSourceAttribute).sourceKey).text;
      const attributeInfoItems = [
        {
          label: this.getText("businessName"),
          value: attributeDetails.text,
        },
        {
          label: this.getText("technicalName"),
          value: attributeTechnicalName,
        },
        {
          label: sourceLabel,
          value: sourceText,
        },
      ];
      const attributeInfo = {
        sectionNum: 1,
        sections: [
          {
            title: this.getText("attributeDetails"),
            items: attributeInfoItems,
          },
        ],
      };
      detailsModel.setProperty(path, attributeInfo);
    }
    detailsModel.refresh(true);
  }

  public getFirstValueObjectFromClientValueHelpResult(result) {
    const value = Object.values(result?.value ?? {})?.[0];
    if (!value) {
      return undefined;
    }
    const updatedVal = value[CurrencyUnitConversionViewsFieldNames.MANDT];
    return this.getConversionValueObjFromValueHelpResult(updatedVal);
  }

  public getConversionValueObjFromValueHelpResult(key: string, value?: string) {
    let updatedVal = key;
    if (isNull(key)) {
      updatedVal = "NULL";
    } else if (key?.toString().trim() === "" || key === undefined) {
      updatedVal = "<empty>";
    }
    const obj = { key: updatedVal, value: !value ? key : value };
    return obj;
  }

  /**
   * Called from PropertyPanel to  transfer the object to get its properties
   * */
  public setObjectModel(oObject) {
    // does nothing here, since the models are already set and are accessible
  }

  /* needed by PropertyPanel to set the appropriate width */
  public getWidth(): sap.ui.core.CSSSize {
    return BuilderConfig.PROPERTIES_DEFAULT_WIDTH;
  }
  public onToggleFullScreen() {
    sap.ui.getCore().getEventBus().publish("propertyPanel", "toggleFullScreen", {
      // put "normal" width here if it is not the default
    });
  }
  /*
   *
   */
  public fullScreenIconFormatter(mode: SidepanelMode) {
    return sap.ui.core.IconPool.getIconURI(mode === SidepanelMode.fullScreen ? "exit-full-screen" : "full-screen");
  }

  /**
   * Replace parameters in messages (also in case placeholders do not include {0})
   * */
  public replaceParameters(text: string, parameters: string[]) {
    let replacedText = text;
    for (let i = 0; i < parameters.length; i++) {
      replacedText = replacedText.replace("{" + i.toString() + "}", parameters[i]);
    }
    return replacedText;
  }

  public getDimensionSemanticType(dimensionSourceId: string, dimensionSource: IAnalyticModelDimensionSource) {
    const attributeDetails = this.getDimensionAsAttributeDetails(dimensionSourceId, dimensionSource);
    if (!attributeDetails) {
      return "";
    }
    return attributeDetails.semanticType;
  }

  public dimensionHasTimeDependency(sourceInfo, dimensionType) {
    if (dimensionType === AnalyticModelSourceType.Fact) {
      return false;
    }
    return this.hasTimeDependency(sourceInfo);
  }

  public dimensionHasTextElementAndHasTextAssociation(dimension) {
    if (dimension.type === AnalyticModelSourceType.Fact) {
      return { hasTextElement: false, hasTextAssociation: false };
    }

    const parentSource = this.getParentSourceDetails(dimension);
    const usedAssociationName = dimension.associationContexts[0].associationSteps[0];
    const usedAssociationDetails = parentSource.associations.find((assoc) => assoc.key === usedAssociationName);
    const hasTextAssociation = Boolean(usedAssociationDetails?.hasTextAssociation);
    const hasTextElement = Boolean(usedAssociationDetails?.hasTextElement);
    return { hasTextElement, hasTextAssociation };
  }

  public async onLoadDependentObjectsList(entityName: string): Promise<any> {
    (this.getView().byId("dependentObjectPanel") as sap.m.Panel).setBusy(true);

    const objectDocument = await getCurrentOrCrossSpaceObjectByName(this.spaceName, entityName);
    const oObject = {
      classDefinition: {
        name: "Entity",
      },
      name: objectDocument.name,
      label: objectDocument.name,
      qualifiedName: objectDocument.qualified_name,
      "#objectStatus": objectDocument["#objectStatus"],
    };

    return getDependentObjectListData(this.spaceName, await this.getSpaceGUID(), oObject, 2)
      .then((dependencies) => {
        this.uiModel.setProperty("/modelDependentObjects", dependencies);
        (this.getView().byId("dependentObjectPanel") as sap.m.Panel).setBusy(false);
      })
      .catch((error) => {
        this.uiModel.setProperty("/modelDependentObjects", {});
        (this.getView().byId("dependentObjectPanel") as sap.m.Panel).setBusy(false);
      });
  }

  public async onSearchDependentObject() {
    const listBinding = (this.byId("dependentObjectList") as ListWithItemBase).getBinding("items");

    const query = this.uiModel.getProperty("/modelDependentObjectsSearch");
    const searchFilter = [];
    if (query && query.length > 0) {
      searchFilter.push(new sap.ui.model.Filter("displayName", sap.ui.model.FilterOperator.Contains, query));
      searchFilter.push(new sap.ui.model.Filter("name", sap.ui.model.FilterOperator.Contains, query));

      listBinding.filter([
        new sap.ui.model.Filter({
          filters: searchFilter,
          and: false,
        }),
      ]);
    } else {
      listBinding.filter([]);
    }
  }

  public async onSortDependentObjectsList() {
    const listBinding = (this.byId("dependentObjectList") as ListWithItemBase).getBinding("items");

    let sortButtonData;
    const sortButtonIcon = (this.byId("sortDependentObjectsListButton") as sap.m.Button).getIcon();
    if (sortButtonIcon === SORT_DEPENDENT_OBJECTS_BUTTON_DATA.bySeverity.icon) {
      sortButtonData = SORT_DEPENDENT_OBJECTS_BUTTON_DATA.ascending;
      listBinding.sort([new sap.ui.model.Sorter("name", false)]);
    } else if (sortButtonIcon === SORT_DEPENDENT_OBJECTS_BUTTON_DATA.ascending.icon) {
      sortButtonData = SORT_DEPENDENT_OBJECTS_BUTTON_DATA.descending;
      listBinding.sort([new sap.ui.model.Sorter("name", true)]);
    } else {
      sortButtonData = SORT_DEPENDENT_OBJECTS_BUTTON_DATA.bySeverity;
      const sorter = new sap.ui.model.Sorter("#objectStatus", null);
      sorter["fnCompare"] = function (status1, status2) {
        return getObjectStatusSeverity(status2) - getObjectStatusSeverity(status1);
      };
      listBinding.sort(sorter);
    }

    (this.byId("sortDependentObjectsListButton") as sap.m.Button).setIcon(sortButtonData.icon);
    (this.byId("sortDependentObjectsListButton") as sap.m.Button).setTooltip(
      this.getText(sortButtonData.tooltipResourceId)
    );
  }

  public onBreadcrumbsLinkPress(oEvent: sap.ui.base.Event) {
    const oLink = oEvent.getSource() as sap.m.Breadcrumbs;
    const id = this.uiModel.getProperty(oLink.getBindingContext("ui")["sPath"]).id;
    const index = Number(oLink.getBindingContext("ui").getPath().split("/").pop());
    const text = this.uiModel.getProperty("/breadcrumbs/links")[Number(index)].text;
    let links: BreadCrumbs = this.uiModel.getProperty("/breadcrumbs/links");
    links = links.slice(0, index);
    this.updateBreadcrumb(text, links);
    // should always be model as this is only used for node properties
    if (id === "model") {
      Util.Component.router.navToModelPanel();
    } else {
      this.navCon.to(this.byId(id));
    }
  }

  /**
   * Updates variable details view with the new used in list for variables
   * @param technicalName
   */
  public setUsedInParamList(technicalName: string) {
    const usedInList = this.dependencies.getUsedInParamList(technicalName);
    this.uiModel.setProperty("/usedInParamList", usedInList);
  }

  /**
   * Checks if a new business name is valid for a given variable
   * Checks for duplication and empty business name
   * @param newBusinessName
   * @param originalTechnicalName
   */
  public isVariableBusinessNameValid(
    newBusinessName: string,
    originalTechnicalName: string
  ): { isValid: boolean; isDuplicated: boolean; isEmpty: boolean } {
    const response = { isValid: false, isEmpty: false, isDuplicated: false };

    // Empty business name
    if (!newBusinessName || newBusinessName === "") {
      response.isEmpty = true;
      return response;
    }

    // Checks the new business name against the existing variables for collision
    let isBusinessNameDuplicated = false;
    const variables = Object.keys(this.model.getVariables());
    for (const variableKey of variables) {
      // Ignore current variable that is being edited
      if (variableKey !== originalTechnicalName) {
        const variable = this.model.getVariable(variableKey);
        // Filter variables are not checked for collision, since they don't have a business name (text)
        isBusinessNameDuplicated =
          variable.parameterType !== AnalyticModelParameterType.StoryFilter && variable.text === newBusinessName;
        if (isBusinessNameDuplicated) {
          break;
        }
      }
    }

    if (isBusinessNameDuplicated) {
      response.isDuplicated = true;
      return response;
    }
    response.isValid = true;
    return response;
  }

  /**
   * Checks if a new technical name is valid for a given variable
   * Checks for duplication and empty technical name
   * @param newTechnicalName
   * @param originalTechnicalName
   */
  public isVariableTechnicalNameValid(
    newTechnicalName: string,
    originalTechnicalName: string
  ): { isValid: boolean; isDuplicated: boolean; isEmpty: boolean; errorMessages: string } {
    const result = { isValid: false, isEmpty: false, isDuplicated: false, errorMessages: "" };
    if (originalTechnicalName === newTechnicalName) {
      result.isValid = true;
      return result;
    }
    if (!newTechnicalName || newTechnicalName === "") {
      result.isEmpty = true;
      return result;
    }

    // Checks the new technical name against the existing variables
    const variableTechnicalNames = Object.keys(this.model.getVariables());
    const variableKeyIndex = variableTechnicalNames.indexOf(newTechnicalName);

    // Checks the new technical name against the existing attributes
    const attributeTechnicalNames = Object.keys(this.model.getAttributes());
    const attributeKeyIndex = attributeTechnicalNames.indexOf(newTechnicalName);

    const isUnique = variableKeyIndex === -1 && attributeKeyIndex === -1;

    if (!isUnique) {
      result.isDuplicated = true;
      return result;
    }

    if (this.enableNewNameChecks()) {
      const errorMessages = this.getNameValidatorErrorMessages(
        newTechnicalName,
        NameUsage.parameter,
        "variableTechnicalNameEmpty"
      );
      if (errorMessages) {
        result.errorMessages = errorMessages;
        return result;
      }
    }

    result.isValid = true;
    return result;
  }

  public setDefaultValueDialogModelData(parameterEntity: any, isLookUpParameters = false) {
    const valueHelpDefinition = parameterEntity[CsnAnnotations.Consumption.valueHelpDefinition]?.[0]?.entity;
    let nodeParamData = {};
    const variableParamData: any = {
      title:
        !isLookUpParameters && this.isStackedModel() && this.featureflags.DWCO_MODELING_AM_SET_VALUE_STACKING_VARIABLE
          ? this.getText("txtSetValueForStackedVariable", [parameterEntity.text ?? parameterEntity.displayText])
          : this.getText("txtSetValueForParam", [parameterEntity.text ?? parameterEntity.displayText]),
      type: parameterEntity.type,
      key: parameterEntity.key,
      selectedType: AnalyticModelSourceParameterMappingType.ConstantValue,
      length: parameterEntity.length,
      precision: parameterEntity.precision,
      scale: parameterEntity.scale,
      defaultValue: this.getParameterValueByMappingType(parameterEntity) ?? parameterEntity.default,
      valueHelpColumn: valueHelpDefinition?.element || parameterEntity?.valueHelpColumn,
      valueHelpSource: valueHelpDefinition?.name || parameterEntity?.valueHelpSource,
      referenceAttribute: parameterEntity.referenceAttribute,
    };
    if (
      !isLookUpParameters &&
      this.isStackedModel() &&
      this.featureflags.DWCO_MODELING_AM_SET_VALUE_STACKING_VARIABLE
    ) {
      // Validate the enablement of the OK button in the Set Value dialog by verifying the default value for each parameter type
      variableParamData.enableSetParameterDefaultValue = this.stackedVariable.enablementOfSetValueButton(
        parameterEntity,
        variableParamData.defaultValue
      );
      this.valueDefinitionUtils.setupInputConfigFromStackedVariable(
        parameterEntity,
        new sap.ui.model.json.JSONModel(variableParamData),
        ""
      );
      variableParamData.parameterType = parameterEntity.parameterType;
      variableParamData.selectedType = this.stackedVariable.computeMappingType(variableParamData);
      if (
        variableParamData.parameterType === AnalyticModelParameterType.Filter ||
        variableParamData.parameterType === AnalyticModelParameterType.StoryFilter
      ) {
        if (parameterEntity.referenceAttribute) {
          const modelAttribute = this.model.getAttribute(parameterEntity.referenceAttribute);
          if (modelAttribute) {
            const attribute = getSourcesFromAttribute(modelAttribute, this.model);
            variableParamData.referenceAttributeSource = attribute?.source.dataEntity.key;
          }
        }
        const parameterValues = this.getParameterValuesForValueHelp();
        variableParamData.allParametersHaveValues = parameterValues?.allParametersHaveValues;
      }
    }
    nodeParamData = {
      ...variableParamData,
      displayType: getDisplayDataType(parameterEntity),
      value: this.getParameterValueByMappingType(parameterEntity),
    };
    this.getView().addDependent(this.parameterDefaultValueDialog);
    // Bind model
    const oModel = new sap.ui.model.json.JSONModel();
    oModel.setData(!isLookUpParameters ? nodeParamData : variableParamData);
    this.parameterDefaultValueDialog.setModel(oModel);
    this.parameterDefaultValueDialog.open();
  }

  /**
   * triggered when we set a value for Parameter from popup
   */
  public async onSetParameterDefaultValue() {
    const sourceParameter = this.parameterDefaultValueDialog.getModel().getData();
    if (sourceParameter) {
      if (this.parameterDefaultValueDialog.getId() === DEFAULT_VALUE_DIALOG_ID.INPUT_PARAMETER_DEFAULT_VALUE) {
        await this.updateParameterBindingForInputParameters(sourceParameter, ActionMappingType.CONSTANT_VALUE);
        sourceParameter.targetParameter = undefined;
      } else if (this.parameterDefaultValueDialog.getId() === DEFAULT_VALUE_DIALOG_ID.LOOK_UP_ENTITY_DEFAULT_VALUE) {
        await this.updateParameterBindingForVariables(sourceParameter);
      }
    }
    this.parameterDefaultValueDialog.close();
  }

  /**
   * called when Variables should be Update based on the new changes in Parameter
   * @param isMapped
   * @param sourceParameter
   */
  public async updateParameterBindingForVariables(sourceParameter: any) {
    (this.uiModel.getProperty("/targetEntityParam") as TargetEntityParams).forEach((item, index) => {
      if (item.key === sourceParameter.key) {
        this.uiModel.setProperty(`/targetEntityParam/${index}/default`, sourceParameter.value);
        this.uiModel.setProperty(`/targetEntityParam/${index}/targetParameter`, undefined);
        this.uiModel.setProperty(
          `/targetEntityParam/${index}/selectedType`,
          AnalyticModelSourceParameterMappingType.ConstantValue
        );
      }
    });
    await this.setParameterBinding();
    this.validateMappedVariableHasError();
  }
  /**
   * called when UiModel and Model should be Update based on the new changes in Parameter
   * @param isMapped
   * @param sourceParameter
   */
  public async updateParameterBindingForInputParameters(
    sourceParameter: IDataEntityParameter,
    actionMappingType: ActionMappingType
  ) {
    const parameterMapping: IParameterMappings = this.computeParameterMapping(sourceParameter, actionMappingType);
    const command = new UpdateParameterMappings(
      this.model.getFirstFactSourceKey(),
      sourceParameter,
      parameterMapping[sourceParameter.key] as IAnalyticModelSourceParameterMapping_VARIABLE | CONSTANT_VALUE_TYPES
    );
    const response = this.stack.execute(command);
    if (response.success && response.result.parameterMapping) {
      parameterMapping[sourceParameter.key] = response.result.parameterMapping;
    }

    await Util.Component.modelChangeHandler([ModelUIProperties.VARIABLES]);

    const index = (this.uiModel.getProperty("/parameters") as NodeParameters).findIndex(
      (param) => param.key === sourceParameter.key
    );
    this.uiModel.setProperty(`/parameters/${index}/parameterMappingType`, parameterMapping);
    this.uiModel.setProperty(
      `/parameters/${index}/parameterDescription`,
      this.getParameterMappingDescription(parameterMapping[sourceParameter.key], sourceParameter.selectionType)
    );
    if (this.isStackedModel()) {
      if (this.featureflags.DWCO_MODELING_AM_SET_VALUE_STACKING_VARIABLE) {
        const index = (this.uiModel.getProperty("/parameters") as NodeParameters).findIndex(
          (param) => param.key === sourceParameter.key
        );
        this.uiModel.setProperty(`/parameters/${index}/selectionType`, sourceParameter.selectionType);
      }
      this.stackedVariable.setupNodeVariablesList();
    }
    const selectedSourceNode = this.uiModel.getProperty("/diagramSelectedSource");
    this.updateParameterErrorProperty(selectedSourceNode);
  }

  private computeParameterMapping(sourceParameter: IDataEntityParameter, actionMappingType: ActionMappingType) {
    const variableName = this.getTargetParameterKey(sourceParameter);
    let parameterMapping: IParameterMappings = {};

    switch (actionMappingType) {
      case ActionMappingType.MAPPED:
        parameterMapping = this.createMappedParameterMapping(sourceParameter, variableName);
        break;
      case ActionMappingType.CONSTANT_VALUE:
        parameterMapping = this.createConstantValueParameterMapping(sourceParameter);
        break;
      case ActionMappingType.INHERITED:
        parameterMapping = this.createInheritedParameterMapping(sourceParameter);
        break;
    }

    return parameterMapping;
  }

  private createMappedParameterMapping(sourceParameter: IDataEntityParameter, variableName: string) {
    return {
      [sourceParameter.key]: {
        mappingType: AnalyticModelSourceParameterMappingType.MapToSourceParameter,
        variableName,
      },
    };
  }

  private createConstantValueParameterMapping(sourceParameter: IDataEntityParameter) {
    let defaultValue;
    let mappingType;
    if (this.isStackedModel() && this.featureflags.DWCO_MODELING_AM_SET_VALUE_STACKING_VARIABLE) {
      defaultValue = this.valueDefinitionUtils.computeDefaultValueForVariable(
        sourceParameter,
        sourceParameter.value as AnalyticModelParameterValueBaseType
      );
      mappingType = this.stackedVariable.computeMappingType(sourceParameter);
    } else {
      defaultValue = sourceParameter.value;
      mappingType = AnalyticModelSourceParameterMappingType.ConstantValue;
    }

    return {
      [sourceParameter.key]: {
        mappingType: mappingType,
        constantValue: defaultValue,
      },
    };
  }

  private createInheritedParameterMapping(sourceParameter: IDataEntityParameter) {
    return {
      [sourceParameter.key]: {
        mappingType: AnalyticModelSourceParameterMappingType.Inherited,
      },
    };
  }
  public onCancelParameterDefaultValue() {
    this.parameterDefaultValueDialog.close();
  }
  public getTargetParameterKey(sourceParameter: any) {
    const targetParam = sourceParameter.targetParameter;
    if (targetParam) {
      return Object.keys(targetParam)[0];
    }
  }

  public getParameterBinding(targetEntityParam: TargetEntityParams): IAnalyticModelVariableParameterBinding {
    const parameterBinding: IAnalyticModelVariableParameterBinding = {};

    targetEntityParam.forEach((key, val) => {
      if (targetEntityParam[val].selectedType === AnalyticModelSourceParameterMappingType.MapToSourceParameter) {
        parameterBinding[targetEntityParam[val].key] = {
          mappingType: AnalyticModelSourceParameterMappingType.MapToSourceParameter,
          variableName: targetEntityParam[val].technicalNameTargetParameter,
        } as IAnalyticModelSourceParameterMapping_VARIABLE;
      }
      if (targetEntityParam[val].selectedType === AnalyticModelSourceParameterMappingType.ConstantValue) {
        parameterBinding[targetEntityParam[val].key] = {
          mappingType: AnalyticModelSourceParameterMappingType.ConstantValue,
          constantValue: targetEntityParam[val].default,
        } as IAnalyticModelSourceParameterMapping_CONSTANTVALUE;
      }
      if (targetEntityParam[val].key && targetEntityParam[val].selectedType === undefined) {
        parameterBinding[targetEntityParam[val].key] = {
          mappingType: AnalyticModelSourceParameterMappingType.MapToSourceParameter,
          variableName: undefined,
        } as IAnalyticModelSourceParameterMapping_VARIABLE;
      }
    });
    return parameterBinding;
  }

  public async setParameterBinding() {
    this.updateVariableDetailsParameterMapping();
    this.updateDerivedValueOnModel();
    await Util.Component.modelChangeHandler([ModelUIProperties.VARIABLES]);
  }

  public updateVariableDetailsParameterMapping() {
    const targetEntityParam = this.uiModel.getProperty("/targetEntityParam");
    const parameterBinding = this.getParameterBinding(targetEntityParam);

    this.uiModel.setProperty("/variableDetails/parameterBinding", parameterBinding);
  }

  public updateDerivedValueOnModel() {
    const varKey = this.uiModel.getProperty("/variableDetails/key");
    const lookupEntity = this.uiModel.getProperty("/variableDetails/lookupEntity");

    const variable = this.model.getVariable(varKey);
    (variable as IAnalyticModelVariableDerivation).lookupEntity = lookupEntity;
    (variable as IAnalyticModelVariableDerivation).parameterBinding = this.uiModel.getProperty(
      "/variableDetails/parameterBinding"
    );
    const setVariableCommand = new SetVariable(varKey, variable);
    this.stack.execute(setVariableCommand);
  }

  public getParameterValueByMappingType(sourceParameter: any) {
    const mapping = sourceParameter.parameterMappingType?.[sourceParameter.key];
    if (mapping && mapping.mappingType === AnalyticModelSourceParameterMappingType.Inherited) {
      return sourceParameter?.default;
    } else if (mapping && CubeBuilderObjects.ConstantMappingTypes.includes(mapping.mappingType)) {
      return mapping.constantValue;
    } else {
      return undefined;
    }
  }

  public validateMappedVariableHasError() {
    (this.uiModel.getProperty("/targetEntityParam") as TargetEntityParams).forEach((item, index) => {
      if (
        (item.selectedType === AnalyticModelSourceParameterMappingType.MapToSourceParameter &&
          item.targetParameter !== undefined) ||
        (item.selectedType === AnalyticModelSourceParameterMappingType.ConstantValue && item.default !== undefined)
      ) {
        this.uiModel.setProperty(`/targetEntityParam/${index}/hasError`, undefined);
      }
      if (
        item.selectedType === AnalyticModelSourceParameterMappingType.MapToSourceParameter &&
        this.model.getVariable(item.targetParameter)
      ) {
        this.uiModel.setProperty(`/targetEntityParam/${index}/hasError`, undefined);
      } else {
        this.uiModel.setProperty(`/targetEntityParam/${index}/hasError`, ValidationMessageType.ERROR);
      }
    });
  }
  /* END */

  /**
   * shared function to show the warning popup while change of technical name
   * @param isMeasure Distinguish between measure and variable
   * @returns
   */
  public async technicalNameChangeWarning(messageI18nKey: string): Promise<boolean> {
    const warningMessage = this.getText(messageI18nKey);
    return new Promise<boolean>((resolve) => {
      MessageBox.warning(warningMessage, {
        id: "technicalNameChangeWarningDialog",
        title: this.getText("warning"),
        actions: [MessageBox.Action.OK, MessageBox.Action.CANCEL],
        emphasizedAction: MessageBox.Action.OK,
        onClose: (action: typeof MessageBox.Action.CANCEL) => {
          const dialog = sap.ui.getCore().byId("technicalNameChangeWarningDialog") as sap.m.Dialog;
          if (dialog) {
            dialog.destroy();
          }
          resolve(action === MessageBox.Action.CANCEL);
        },
      });
    });
  }

  /**
   * shared function to validate the technical Name of Variables and Measures
   * @param returnState
   * @param isMeasure Distinguish between measure and variable
   * @returns
   */
  public validateTechnicalName(isMeasure: boolean, technicalNameInput: sap.m.Input) {
    if (this.enableNewNameChecks()) {
      const errorMessages = this.getNameValidatorErrorMessages(
        technicalNameInput.getValue(),
        isMeasure ? NameUsage.element : NameUsage.parameter,
        isMeasure ? "analyticMeasureTechnicalNameEmpty" : "variableTechnicalNameEmpty"
      );
      if (errorMessages) {
        technicalNameInput.setValueState(sap.ui.core.ValueState.Error);
        technicalNameInput.setValueStateText(errorMessages);
        technicalNameInput.openValueStateMessage();
        return technicalNameInput.getValueState() !== sap.ui.core.ValueState.None;
      }
    }

    if (technicalNameInput.getValue() === "") {
      technicalNameInput.setValueState(sap.ui.core.ValueState.Error);
      technicalNameInput.setValueStateText(
        isMeasure ? this.getText("analyticMeasureTechnicalNameEmpty") : this.getText("variableTechnicalNameEmpty")
      );
      technicalNameInput.openValueStateMessage();
    } else if (
      this.model.getMeasure(technicalNameInput.getValue()) ||
      this.model.getVariable(technicalNameInput.getValue())
    ) {
      if (isMeasure && this.model.getMeasure(technicalNameInput.getValue())) {
        if (technicalNameInput.getValue() !== this.uiModel.getProperty("/measureDetails/technicalName")) {
          technicalNameInput.setValueState(sap.ui.core.ValueState.Error);
          technicalNameInput.setValueStateText(this.getText("analyticMeasureTechnicalNameNotUniqueInMeasures"));
          technicalNameInput.openValueStateMessage();
        } else {
          this.setValueStateToNone(technicalNameInput);
        }
      } else if (!isMeasure && this.model.getVariable(technicalNameInput.getValue())) {
        if (technicalNameInput.getValue() !== this.uiModel.getProperty("/variableDetails/originalTechnicalName")) {
          technicalNameInput.setValueState(sap.ui.core.ValueState.Error);
          technicalNameInput.setValueStateText(this.getText("variableTechnicalNotUnique"));
          technicalNameInput.openValueStateMessage();
        } else {
          this.setValueStateToNone(technicalNameInput);
        }
      }
    } else if (this.model.getAttribute(technicalNameInput.getValue())) {
      technicalNameInput.setValueState(sap.ui.core.ValueState.Error);
      technicalNameInput.setValueStateText(
        isMeasure
          ? this.getText("analyticMeasureTechnicalNameNotUniqueInDimensions")
          : this.getText("variableTechnicalNameNotUniqueInDimensions")
      );
      technicalNameInput.openValueStateMessage();
    } else if (isMeasure && this.model.getCrossCalculation(technicalNameInput.getValue())) {
      technicalNameInput.setValueState(sap.ui.core.ValueState.Error);
      technicalNameInput.setValueStateText(this.getText("analyticMeasureTechnicalNameNotUniqueInCrossCalculations"));
      technicalNameInput.openValueStateMessage();
    } else {
      this.setValueStateToNone(technicalNameInput);
    }
    return technicalNameInput.getValueState() !== sap.ui.core.ValueState.None;
  }

  private setValueStateToNone(technicalNameInput: sap.m.Input) {
    technicalNameInput.setValueState(sap.ui.core.ValueState.None);
    technicalNameInput.setValueStateText(undefined);
    technicalNameInput.closeValueStateMessage();
  }
  public getVariableSelectionType() {
    return [
      { key: AnalyticModelUIVariableSelectionType.SINGLE, text: this.getText("filterVarSelectionTypeSingle") },
      {
        key: AnalyticModelUIVariableSelectionType.MULTIPLE_SINGLE,
        text: this.getText("filterVarSelectionTypeMultiple"),
      }, // updates boolean property multipleSelections
      { key: AnalyticModelUIVariableSelectionType.INTERVAL, text: this.getText("filterVarSelectionTypeInterval") },
      { key: AnalyticModelUIVariableSelectionType.RANGE, text: this.getText("filterVarSelectionTypeRange") },
      {
        key: AnalyticModelUIVariableSelectionType.SINGLE_RANGE,
        text: this.getText("filterVarSelectionTypeSingleRange"),
      }, // selection type single range value offered for old models
    ];
  }

  // #region Common Formatting Functions
  protected getDecimalPlaces() {
    const decimalPlaces = [{ key: undefined, text: this.getText("default") }];
    for (let i = 0; i <= 9; i++) {
      decimalPlaces.push({ key: i, text: String(i) });
    }
    return decimalPlaces;
  }

  protected getScaleType() {
    return [
      { key: undefined, text: this.getText("default") },
      { key: 3, text: this.getText("txtThousand") },
      { key: 6, text: this.getText("txtMillion") },
      { key: 9, text: this.getText("txtBillion") },
      { key: -2, text: this.getText("txtPercent") },
    ];
  }
  // #endregion

  // #region Common Expression Editor Functions
  protected resetExpressionEditorMessageStrip(expressionEditor: ExpressionEditor) {
    expressionEditor.setMessageStripVisible(false);
    this.uiModel.setProperty("/crossCalculationDetails/formulaValidation", []);
    this.uiModel.setProperty("/measureDetails/formulaValidation", []);
    this.uiModel.setProperty("/filterDetails/formulaValidation", []);
  }
  // #endregion

  // #region Common Exception Aggregation Functions
  protected getExceptionAggregationTypes(): Array<{
    key: string;
    text: string;
    additionalText?: string;
  }> {
    const exceptionAggregationTypes: Array<{
      key: string;
      text: string;
      additionalText?: string;
    }> = [];

    for (const [key] of ReverseExceptionAggregationTypeMapping.entries()) {
      if (key === AnalyticModelExceptionAggregationType.NONE) {
        exceptionAggregationTypes.push({
          key: key,
          text: this.getText("exception_aggregation_type_none_description"),
        });
      } else {
        exceptionAggregationTypes.push({
          key: key,
          text: ReverseExceptionAggregationTypeMapping.get(key).replace(/_/g, " "),
          additionalText: this.getText(
            "exception_aggregation_type_" +
              ReverseExceptionAggregationTypeMapping.get(key).toLowerCase() +
              "_description"
          ),
        });
      }
    }
    return exceptionAggregationTypes;
  }
  // #endregion

  public recordCrossCalculationDeletions(keys: string[]) {
    keys.forEach((key) => {
      this.recordCrossCalculationDeletion(key);
    });
  }

  private recordCrossCalculationDeletion(key: string) {
    const crossCalculationType: AnalyticModelCrossCalculationType =
      this.model.getCrossCalculation(key).crossCalculationType;
    const crossCalculationDeleteActionMap = {
      [AnalyticModelCrossCalculationType.FactSourceCrossCalculation]:
        USAGE_ACTIONS.DELETE_FACT_SOURCE_CROSS_CALCULATION,
      [AnalyticModelCrossCalculationType.CalculatedCrossCalculation]: USAGE_ACTIONS.DELETE_CALCULATED_CROSS_CALCULATION,
      [AnalyticModelCrossCalculationType.RestrictedCrossCalculation]: USAGE_ACTIONS.DELETE_RESTRICTED_CROSS_CALCULATION,
    };
    recordUsageTrackingEvent({
      action: crossCalculationDeleteActionMap[crossCalculationType],
      feature: getCurrentFeatureForUsageTracking(),
      eventtype: EventType.CLICK,
    });
  }
}

export const AnalyticModelPropertiesBase = smartExtend(
  AnalyticModelBase,
  "sap.cdw.components.cubebuilder.properties.AnalyticModelPropertiesBase",
  AnalyticModelPropertiesBaseClass
);

sap.ui.define("sap/cdw/components/cubebuilder/properties/AnalyticModelPropertiesBase.controller", [], function () {
  return AnalyticModelPropertiesBase;
});
