/** @format */

import { RepositoryObjectType } from "@sap/deepsea-types";
import { ShellNavigationService } from "@sap/orca-shell";
import { isEqual } from "lodash";
import {
  AnalyticModelVariable,
  IParameterValues,
  ParameterSelectionType,
  VariableOperator,
} from "../../../../shared/odata/paramDefinitions";
import {
  AllQueryParameter,
  AnalyticModelAttributeType,
  AnalyticModelMeasureType,
  AnalyticModelParameterType,
  AnalyticModelSourceParameterMappingType,
  AnalyticModelSourceType,
  AnalyticModelVariableProcessingType,
  AnalyticModelVariableSelectionType,
  IAnalyticModelDimensionSourceAttribute,
  IAnalyticModelFactSource,
  IAnalyticModelFactSourceAttribute,
  IAnalyticModelRepositoryEntity,
  IAnalyticModelSource,
  IAnalyticModelSourceMeasure,
  IAnalyticModelSourceParameter_LOOKUP,
  IAnalyticModelSourceParameter_MANUALINPUT,
} from "../../../../shared/queryBuilder/AnalyticModel";
import {
  CreateCubeModelFromAnalyticalDatasetParameter,
  CreateCubeModelFromAnalyticalDatasetResult,
  IParameterMappings,
} from "../../../../shared/queryBuilder/ConvertAnalyticalDataSetApi";
import { isAnalyticModelVersionBackwardsCompatible } from "../../../../shared/queryBuilder/CsnXprToAMHelper";
import {
  IAnalyticModelDimensionAssociation,
  IDataEntity,
  IDataEntityAssociation,
  IDataEntityCrossCalculation,
  IDataEntityDetailsResponse,
  IDataEntityMeasure,
  IDataEntityParameter,
  IDataEntityProperty,
} from "../../../../shared/queryBuilder/DataEntityDetails";
import { IPreconditionResult } from "../../../../shared/queryBuilder/PreviewDeploymentApi";
import { CONSTANT_VALUE_TYPES, ErrorCodes } from "../../../../shared/queryBuilder/QueryModel";
import { ValidationMessage, ValidationMessageType } from "../../../../shared/queryBuilder/QueryModelValidator";
import { SpaceStatus } from "../../../../shared/spaces/types";
import { getArtefactSharesForTarget } from "../../../services/metadata";
import { ValidationStatus } from "../../abstractbuilder/api";
import { AbstractWorkbench } from "../../abstractbuilder/controller/AbstractWorkbench.controller";
import { smartExtend } from "../../basecomponent/controller/BaseController.controller";
import { RepoPackageService } from "../../commonmodel/api/RepoPackageService";
import { CsnAnnotations } from "../../commonmodel/csn/csnAnnotations";
import { CDSDataType } from "../../commonmodel/model/types/cds.types";
import { parseNotificationData } from "../../commonmodel/utility/DeploymentUtils";
import { NewModelTypes } from "../../databuilder/utility/Constants";
import { getIsHanaDown, openObjectByName } from "../../databuilder/utility/DatabuilderHelper";
import { validateDecimalInput } from "../../ermodeler/js/utility/CommonUtils";
import { notificationsEventChannel, notificationsFetched } from "../../notifications/utility/Types";
import { IInputParameter } from "../../reuse/odata/ODataDialog";
import {
  AnalyticModelDefaultIntervalOption,
  oDataOperatorIntervalMapping,
  oDataOperatorMapping,
  oDataSelectionTypeMapping,
} from "../../reuse/odata/utils/mappings";
import { IMessageHandlerExceptionParams, MessageHandler } from "../../reuse/utility/MessageHandler";
import { ContentType, DataType, HttpMethod, ServiceCall } from "../../reuse/utility/ServiceCall";
import { ObjectStatus } from "../../reuse/utility/Types";
import { Crud } from "../../shell/utility/Crud";
import { ModelType, SpaceType } from "../../shell/utility/Repo";
import { DWCFeature, EventType } from "../../shell/utility/ShellUsageCollectionService";
import { User } from "../../shell/utility/User";
import { ObjectNameDisplay } from "../../userSettings/utility/Constants";
import { CdsFormulaToExprService } from "../api/CdsFormulaToExpr.service";
import { DataEntityDetailsService } from "../api/DataEntityDetails.service";
import {
  DependencyServiceAnalyticModel,
  IAnalyticModelDependencies,
  IAnalyticModelElementDependencies,
} from "../api/Dependency.service";
import { IsCanaryService } from "../api/IsCanary.service";
import { Command } from "../command/AnalyticModelCommand";
import { UpdateModel } from "../command/model/UpdateModel";
import { RepairDimensionSource } from "../command/sourceModel/RepairDimensionSource";
import { ReplaceFactSource } from "../command/sourceModel/ReplaceFactSource";
import { CreateVariable } from "../command/variables/CreateVariable";
import { AnalyticModelBase, AnalyticModelBaseClass } from "../controller/AnalyticModelBase.controller";
import "../css/styles.less";
import { ChangedElementsForImpact, ImpactedElements } from "../extensions/dependencies/ImpactedElements";
import Query from "../model/Query";
import QueryUIModel from "../model/QueryUIModel";
import * as CubeBuilder from "../utility/CubeBuilder";
import CubeBuilderObjects from "../utility/CubeBuilderObjects";
import {
  AttributePropertiesView,
  DiagramSource,
  DimensionTreeNode,
  ModelPropertiesView,
  MultiDimModel,
  MultiDimModelConstructor,
  NodeAttributes,
  NodeCrossCalculations,
  NodeMeasures,
  NodeParameter,
  NodeParameters,
  NodePropertiesView,
  TakeOverDialogModelData,
  TakeOverDialogParameter,
  TypedSource,
  UICrossCalculations,
  UIDacs,
  UIMeasures,
  UIValidation,
  UIValidations,
  UIVariables,
  UiDataEntityAttribute,
} from "../utility/CubeBuilderTypes";
import {
  AnalyticModelObjectType,
  CubeBuilderEditorTabs,
  ModelPropertiesViews,
  ModelRoutes,
  PropertiesPanels,
} from "../utility/Enum";
import Formatter from "../utility/Formatter";
import {
  createAttributeKey,
  doesFiscalVariantVariableExist,
  filterValidationsForSources,
  getAttributeDisplaySource,
  getDisplayDataType,
  getInitialValues,
  getSourcesFromAttribute,
  isNonForeignKeyAssociation,
  isVariableDerivationOption,
} from "../utility/ModelUtils";
import {
  USAGE_ACTIONS,
  editorTabToLaunchUsageAction,
  editorTabToOpenOnUsageAction,
  getCurrentFeatureForUsageTracking,
  recordUsageTrackingEvent,
} from "../utility/UsageTracking";
import Util from "../utility/Util";

const Fragment: typeof sap.ui.core.Fragment = sap.ui.require("sap/ui/core/Fragment");
const MessageBox: typeof sap.m.MessageBox = sap.ui.require("sap/m/MessageBox");

export class CubeBuilderEditorClass extends AnalyticModelBaseClass {
  public amFormatter: Formatter = Formatter;
  public cubeBuilderSelectedTab: string;

  private user: User;
  private workbench: AbstractWorkbench;

  private validationMessagesPopover: sap.m.PopoverUpdated;
  private abortControllerPreview: AbortController;
  private modelWarningMessageBox: sap.m.MessageBox;
  private replaceSourceDialog: sap.m.Dialog;
  private selectSourceDialog: sap.m.Dialog;
  private repairDimensionDialog: sap.m.Dialog;

  private impactedElements: ImpactedElements;

  private dragAndDropEventsRegistered = false;
  public onInit() {
    super.onInit();
    // set busy indicator
    this.view = this.getView();
    this.view.setBusyIndicatorDelay(0);

    // get user instance
    this.user = User.getInstance();
    this.workbench = this.getWorkbenchController();

    // register diagram in Util
    Util.registerDiagramControl(this.view.byId("CubeBuilderDiagram"));
    this.abortControllerPreview = null;
  }
  /**
   * toggle the appropriate view depending on currentEditorOption
   */
  public async setEditorTab(): Promise<any> {
    // make the appropriate page and ui parts visible - this is a actually a simulation of routing
    // 1. set appropriate view depending on editor option
    if (Util.Component.currentEditorOption === CubeBuilderEditorTabs.cubeDataPreview) {
      this.cubeBuilderSelectedTab = CubeBuilderEditorTabs.cubeDataPreview;
    } else {
      this.cubeBuilderSelectedTab = CubeBuilderEditorTabs.cubeQueryModel;
    }
    Util.Component.changeUIStateforAMSelectedPage(this.cubeBuilderSelectedTab === CubeBuilderEditorTabs.cubeQueryModel);
    this.uiModel.setProperty("/analyticModelViewMode", this.cubeBuilderSelectedTab);

    // 2. load appropriate data to fill the appropriate view
    // if convertFromADS - the AM is built by a backend call AND following calls fill /allSourceDataEntityDetails
    // and validate model
    if (Util.Component.isConvertedFromADS) {
      const technicalName = Util.Component.currentEditorOption.split("=").pop();
      await this.loadDataEntityAndAnalyticalDataSet(technicalName, this.getSpaceName());
    } else {
      // read metadata for the model and put info into uiModel: /allSourceDataEntityDetails
      // update validation messages, which are computed in the backend by this call
      await this.getSourceEntityDetails(this.model.getSourceModel());
      this.setupImpactedElements();
      if (!this.uiModel.getProperty("/isNew")) {
        if (this.featureflags.DWCO_MODELING_ANALYTIC_MODEL_STACKING_LIFECYCLE) {
          await Util.Component.modelChangeHandler([]);
        } else {
          await Util.Component.getAndUpdateModelValidations(this.model);
        }
      }
    }

    // if data preview trigger "additionally" the appropriate calls to load and display data
    if (this.cubeBuilderSelectedTab === CubeBuilderEditorTabs.cubeDataPreview) {
      if (!this.workbench.getPanelType() || this.workbench.getPanelType() !== ModelRoutes.preview) {
        Util.Component.router.navToPreview(true);
      } else {
        this.workbench.setPanelType(ModelRoutes.model); // simulate a switch
        await Util.Component.changePanel(
          ModelRoutes.preview,
          this.workbench.getPanelProperty(),
          Util.Component.router.getCurrentModelOption(),
          true
        );
      }
    }

    // 3. In case of Model view adjust uiModel according to data in this.model, draw the diagram
    if (this.cubeBuilderSelectedTab === CubeBuilderEditorTabs.cubeQueryModel) {
      if (!this.uiModel.getProperty("/isNew") || Util.Component.isConvertedFromADS) {
        this.view.setBusy(true);
        this.setupUiProperties();
        // then draw the diagram
        await Util.Diagram.drawDiagram();
        this.view.setBusy(false);
      }
      if (!this.workbench.getPanelType() || this.workbench.getPanelType() === ModelRoutes.preview) {
        Util.Component.router.navToModelPanel(true);
      } else {
        await Util.Component.changePanel(
          this.workbench.getPanelType(),
          this.workbench.getPanelProperty(),
          Util.Component.router.getCurrentModelOption(),
          true
        );
      }
    }
  }

  /**
   * a wrapper method for loadAnalyticalDataSet - only used in convertFromADs scenario
   * @param technicalName: a name of already available AM
   * @param spaceId: space name
   */
  private async loadDataEntityAndAnalyticalDataSet(technicalName, spaceId) {
    this.view.setBusy(true);
    try {
      const dataEntity: IAnalyticModelRepositoryEntity = {
        key: technicalName,
      };
      const sourceDataEntity = await DataEntityDetailsService.getInstance().loadDataEntityDetails(
        [dataEntity],
        spaceId
      );

      let associationsToDimensions: string[] = [];
      const parameterMappings: IParameterMappings = {};
      if (sourceDataEntity) {
        associationsToDimensions = sourceDataEntity[technicalName].associations
          .filter(
            (association) =>
              !this.featureflags.DWCO_MODELING_AM_NEW_DIM_HANDLING ||
              !isNonForeignKeyAssociation(association, sourceDataEntity[technicalName], AnalyticModelSourceType.Fact)
          )
          .map((index) => index.key);
        /* Create Mapped parameter for each parameter in DataEntity */
        sourceDataEntity[technicalName].parameters.forEach((param) => {
          parameterMappings[param.key] = {
            // via import, the MappingType should be MapTo- incase parameter is added implicitly because of changes from underlying ADS the MappingType should be constat value
            mappingType: AnalyticModelSourceParameterMappingType.MapToSourceParameter,
            variableName: param.key,
          };
        });
      }
      this.setAssociateDialogUiProperties(true);
      this.setCrossCalculationsCheckboxState(sourceDataEntity[technicalName]);
      await this.loadAnalyticalDataSet(dataEntity, associationsToDimensions, parameterMappings);
    } catch (e) {
      MessageHandler.uiError(this.getText("dataEntityDetailsError"), e?.[0]?.responseJSON?.details?.message);
    } finally {
      // no catch because the request methods already show an error if they fail
      this.view.setBusy(false);
    }
  }

  public onAfterRendering() {
    this.setupDragAndDrop();
  }

  private clearDataPreviewProperties(): void {
    // save dragonfly configuration so no control errors happen
    let dragonfly = this.uiModel.getProperty("/preview/dragonfly");
    if (!dragonfly?.configObject) {
      dragonfly = {
        configObject: require("../flex-analysis-preview-config.json"),
      };
    }
    // initialize preview structure
    const initPreview = {
      noDataBox: false,
      preconditionNotSatisfied: false,
      previewFlexBox: false,
      errorProperty: {
        hasError: false,
        errorDetails: null,
        errorMessage: "",
      },
      notDeployedObjects: [],
      dragonfly: dragonfly,
    };
    this.uiModel.setProperty("/preview", initPreview);
  }

  private resetDataPreview(): void {
    this.uiModel.setProperty("/preview/noDataBox", false);
    this.uiModel.setProperty("/preview/preconditionNotSatisfied", false);
    this.uiModel.setProperty("/preview/previewFlexBox", false);
  }

  private async handleDrop(event) {
    const object = this.uiModel.getProperty("/sourcesListDraggedObject");
    await this.onAddSource(event, object);
  }

  public subscribeOnNotificationChannel() {
    sap.ui
      .getCore()
      .getEventBus()
      .subscribe(notificationsEventChannel, notificationsFetched, this.onDeployNotification.bind(this), this);
  }

  public async toggleSelectedView() {
    // make the appropriate page visible - this is a actually a simulaion of routing
    this.uiModel.setProperty("/analyticModelViewMode", this.cubeBuilderSelectedTab);

    // adjust workbench toolbar buttons
    if (this.cubeBuilderSelectedTab === CubeBuilderEditorTabs.cubeDataPreview) {
      Util.Component.changeUIStateforAMSelectedPage(false);
      if (this.featureflags.DWCO_MODELING_ANALYTIC_MODEL_STACKING_LIFECYCLE) {
        await Util.Component.modelChangeHandler([]);
      } else {
        await Util.Component.getAndUpdateModelValidations(this.model);
      }
      this.abortControllerPreview = new AbortController();
      await this.updateDataPreviewPage();
    } else {
      Util.Component.changeUIStateforAMSelectedPage(true);
      // if there is a pre-deployment for preview running, we need to stop it!
      this.abortControllerPreview?.abort();
      // then reset preview stuff
      this.resetDataPreview();
      // and reset the abort controller, because if the same instance has aborted once, it will not resent the abot signal again
      this.abortControllerPreview = null;

      if (!this.uiModel.getProperty("/isNew")) {
        this.setupUiProperties();
        await Util.Diagram.drawDiagram();
      }
    }
  }

  public setupDragAndDrop() {
    if (!this.dragAndDropEventsRegistered) {
      sap.ui.getCore().getEventBus().subscribe("SOURCES_LIST", "DRAG_ENTER", this.onSourceListDragEnter.bind(this));
      sap.ui.getCore().getEventBus().subscribe("SOURCES_LIST", "DRAG_END", this.onSourcesListDragEnd.bind(this));
      (this.view.getDomRef() as HTMLElement).addEventListener("dragover", async (evt) => {
        if (!this.modelHasSources()) {
          evt.preventDefault();
        }
        await Util.Diagram.highlightFactSourceNodeIfDragIsOnTop(evt);
      });
      (this.view.getDomRef() as HTMLElement).addEventListener("drop", (event) => this.handleDrop(event));
      this.dragAndDropEventsRegistered = true;
    }
  }

  public onSourceListDragEnter(channel: string, eventName: string, data: any) {
    this.uiModel.setProperty("/sourcesListDraggedObject", data);
  }

  public onSourcesListDragEnd(channel: string, eventName: string, data: any) {
    // reset dragged object for next drop
    this.uiModel.setProperty("/sourcesListDraggedObject", undefined);
  }

  /**
   * Updates the aggregatedValidations properties on Workbench model
   */
  private clearValidationMessages() {
    const aggregatedValidations = {
      status: ValidationStatus.STATUS_OK,
      validations: [],
    };
    this.workbench.getToolbarModel().setProperty("/aggregatedValidations", aggregatedValidations);
  }

  /**
   * Opens the validation result popover.
   */
  public async openValidationsPopover(event: IEvent<sap.m.Button, any>) {
    const source = event.getSource();
    const messagePopoverId = this.view.getId() + "--messagePopover";
    if (this.validationMessagesPopover) {
      if (this.validationMessagesPopover.isOpen()) {
        this.validationMessagesPopover.close();
        return;
      }
      this.validationMessagesPopover.destroy();
      this.validationMessagesPopover = undefined;
    }
    if (!this.validationMessagesPopover) {
      const oPopover = new sap.m.Popover(messagePopoverId, {
        showHeader: false,
        placement: sap.m.PlacementType.Bottom,
      });
      oPopover.attachAfterClose(function () {
        oPopover.destroy();
      });

      const sFragmentName = await require("../view/ValidationsPopover.fragment.xml");
      const oContent = (await Fragment.load({
        name: sFragmentName,
        controller: this,
      })) as sap.m.VBox;
      oPopover.addContent(oContent);
      this.validationMessagesPopover = oPopover;
    }
    this.view.addDependent(this.validationMessagesPopover);
    this.validationMessagesPopover.setInitialFocus(this.validationMessagesPopover);
    this.validationMessagesPopover.openBy(source, false);

    setTimeout(() => {
      const noDataTextElement = (this.validationMessagesPopover.getDomRef() as HTMLElement).querySelector(
        ".sapMListNoData"
      );
      if (noDataTextElement) {
        noDataTextElement.textContent = this.getText("validationMessageValidModel");
      }
    }, 0);
  }

  // this method gets from backend the details to a sourceModel ressources -
  // without the enhancements from this call, the UI is missing data - e.g. business names -
  // diagram should only display its nodes, when the response of this method is available.
  // TODO: shift View.setBusy to outside to keep the method independant on the view -
  // it might make sense to call it in the component before setting the model
  public async onAfterReceiveQueryModel() {
    // AM version check
    const version = this.model.getVersion();
    if (!isAnalyticModelVersionBackwardsCompatible(version)) {
      sap.m.MessageBox.error(this.getText("olderModelVersion"), {
        onClose: async function () {
          await ShellNavigationService.historyBack();
        },
      });
      return Promise.resolve();
    }
    // clear uiModel properties related to selected diagram, query model and initialize technical and business names
    const isNew = this.uiModel.getProperty("/isNew");

    // updates toolbar model with dirty state, object status and validations
    const objectStatus = this.uiModel.getProperty("/objectStatus");
    const deploymentExecutionStatus = this.uiModel.getProperty("/deploymentExecutionStatus");
    this.workbench.getToolbarModel().setData({
      isNew,
      "#objectStatus": objectStatus,
      "#deploymentExecutionStatus": deploymentExecutionStatus,
      aggregatedValidations: { status: "OK", validations: [] },
    });
    this.resetUiModel(isNew);

    if (this.isDependentObjectsValidationEnabled()) {
      DependencyServiceAnalyticModel.getInstance().loadDependentModels(
        this.model.getTechnicalName(),
        this.getSpaceName()
      );
    }

    await this.setEditorTab();
    recordUsageTrackingEvent({
      action: editorTabToOpenOnUsageAction[this.cubeBuilderSelectedTab],
      feature: getCurrentFeatureForUsageTracking(),
      eventtype: EventType.CLICK,
    });

    await this.displayOpeningModelErrors();
  }

  // only display this dialog for association validations and when opening the model (don't display when switching tabs)
  public async displayOpeningModelErrors() {
    const featureFlags = (sap.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel).getData();
    const dimensionSources = this.model.getDimensionSources();

    const aggregatedValidations: UIValidations = { validations: [], status: ValidationStatus.STATUS_OK };
    for (const dimensionKey in dimensionSources) {
      const validationMessages = Util.Component.getPropertyValidationMessages(
        `/sourceModel/dimensionSources/${encodeURIComponent(dimensionKey)}`
      )?.validations;
      if (!validationMessages || validationMessages.length === 0) {
        continue;
      }
      const validationsWithMessageAndDescription = validationMessages.map((validation) => {
        validation.message = this.getText(validation.messageKey, validation.parameters);
        validation.description = this.getText(validation.descriptionKey, validation.parameters);
        return validation;
      });
      aggregatedValidations.validations.push(...validationsWithMessageAndDescription);
    }
    if (this.model.getDimensionHandlingCapability() && featureFlags.DWCO_MODELING_ANALYTIC_MODEL_STACKING) {
      const factSources = this.model.getFactSources();
      for (const factSourceKey in factSources) {
        const validationMessages = Util.Component.getPropertyValidationMessages(
          `/sourceModel/factSources/${encodeURIComponent(factSourceKey)}`
        )?.validations;
        const filteredMessages = filterValidationsForSources(AnalyticModelSourceType.Fact, validationMessages);
        if (!filteredMessages || filteredMessages.length === 0) {
          continue;
        }
        const validationsWithMessageAndDescription = filteredMessages.map((validation) => {
          validation.message = this.getText(validation.messageKey, validation.parameters);
          validation.description = this.getText(validation.descriptionKey, validation.parameters);
          return validation;
        });
        aggregatedValidations.validations.push(...validationsWithMessageAndDescription);
      }
    }
    if (
      aggregatedValidations.validations.length === 0 ||
      this.cubeBuilderSelectedTab !== CubeBuilderEditorTabs.cubeQueryModel ||
      aggregatedValidations.validations.every((validation) => validation.type !== ValidationMessageType.ERROR)
    ) {
      return;
    }
    await Util.Component.displayValidationsDialog(aggregatedValidations, true);
  }

  private isSameAnalyticModelHash(hash: string): boolean {
    if (hash === undefined) {
      return true;
    }

    const splittedHash = hash.split("/");
    const isModelPage = splittedHash?.length >= 2;
    const isCurrentSpace = splittedHash?.[0] === encodeURIComponent(Util.Component["spaceId"]);
    const isCurrentModel = splittedHash?.[1] === encodeURIComponent(Util.Component["modelId"]);
    return isModelPage && isCurrentSpace && isCurrentModel;
  }

  /**
   * Changes the current entity that is open in the properties panel.
   * The method calls the appropriate method to update the properties panel, which setups the ui properties for the selected entity
   * Example of arguments:
   *  panelType = ModelRoutes.dimension, panelProperty = "1" -> opens dimension with key 1
   *  panelType = ModelRoutes.model -> opens main output properties panel
   * @param {ModelRoutes} panelType type of object to route to.
   * @param {string | undefined} panelProperty what object from the type we want to route to.
   * @param {string | undefined} option the option to route to.
   * @returns {Promise<boolean>} true if the routing should happen, false if we want to reload the model.
   */
  public async changePanel(
    panelType: ModelRoutes,
    panelProperty: string | undefined,
    option: string | undefined,
    force?: boolean
  ): Promise<boolean> {
    if (!force && !this.isSameAnalyticModelHash(Util.Component.router.getPreviousHash())) {
      return;
    }
    const lastPanel = this.workbench.getPanelType();
    this.workbench.setPanelType(panelType);
    this.workbench.setPanelProperty(panelProperty);

    // check if model option was changed, so we reload the model
    // if new option and panel type is the preview, continue routing
    if (!option) {
      option = CubeBuilderEditorTabs.cubeQueryModel;
    }
    if (
      option !== Util.Component.router.getCurrentModelOption() &&
      (option !== CubeBuilderEditorTabs.cubeDataPreview || panelType !== ModelRoutes.preview)
    ) {
      return false;
    }

    if (!(await Util.Diagram.isSymbolAlreadySelected(panelType, panelProperty))) {
      await Util.Diagram.unselectAllSymbols();
    }

    // update selected key in toolbar, option in URL and update selected tab
    let switched = false;
    if (panelType === ModelRoutes.preview) {
      (this.workbench.getView().byId("modelPreviewButton") as sap.m.SegmentedButton).setSelectedKey(
        CubeBuilderEditorTabs.cubeDataPreview
      );
      if (lastPanel !== ModelRoutes.preview) {
        switched = true;
      }
    } else {
      (this.workbench.getView().byId("modelPreviewButton") as sap.m.SegmentedButton).setSelectedKey(
        CubeBuilderEditorTabs.cubeQueryModel
      );
      if (lastPanel === ModelRoutes.preview) {
        switched = true;
      }
    }
    if (switched) {
      const currentOption = (
        this.workbench.getView().byId("modelPreviewButton") as sap.m.SegmentedButton
      ).getSelectedKey();
      this.workbench.setCurrentEditorOption(currentOption);
      await this.updateModelPreviewTab(currentOption);
    }

    // setup panel
    if (panelType === ModelRoutes.attribute) {
      this.uiModel.setProperty("/attributeDetails", {});
      const attribute = this.model.getAttribute(panelProperty);
      if (!attribute) {
        await this.resetPanel();
        return true;
      }
      const routingFromTechnicalNameUpdate = Boolean(this.uiModel.getProperty("/routingFromTechnicalNameUpdate"));
      this.uiModel.setProperty("/routingFromTechnicalNameUpdate", false);

      const attributeDetails = {
        ...attribute,
        keyOnModel: panelProperty,
        derivable: !routingFromTechnicalNameUpdate,
      };
      this.uiModel.setProperty("/attributeDetails", attributeDetails);

      const attributeToNavTo = {
        ...attributeDetails,
        classDefinition: { qualifiedName: "sap.cdw.analyticmodel.Attribute" },
      };
      Util.Component.updatePropertyPanel([attributeToNavTo]);
      await Util.Component.updateDataPreviewPanel(undefined);
    } else if (panelType === ModelRoutes.dimension || panelType === ModelRoutes.fact) {
      const sourceKey = panelType === ModelRoutes.dimension ? panelProperty : this.model.getFirstFactSourceKey();
      const type: AnalyticModelSourceType =
        panelType === ModelRoutes.dimension ? AnalyticModelSourceType.Dimension : AnalyticModelSourceType.Fact;
      const source =
        panelType === ModelRoutes.dimension
          ? this.model.getDimensionSource(sourceKey)
          : this.model.getFactSource(sourceKey);
      if (!source) {
        await this.resetPanel();
        return true;
      }

      const gObject = await Util.Diagram.getNode(type, sourceKey);
      this.uiModel.setProperty("/diagramSelectedSource", { ...gObject.originalObject });
      const routingFromTechnicalNameUpdate = Boolean(this.uiModel.getProperty("/routingFromTechnicalNameUpdate"));
      this.uiModel.setProperty("/routingFromTechnicalNameUpdate", false);
      this.uiModel.setProperty("/diagramSelectedSource/technicalNameDerivable", !routingFromTechnicalNameUpdate);
      await Util.Diagram.selectNodeByTypeAndId(type, sourceKey);
      Util.Component.updatePropertyPanel([gObject]);
      await Util.Component.updateDataPreviewPanel(source.dataEntity.key);
    } else if (panelType !== ModelRoutes.preview) {
      // we are going to route to model properties, so we just need to check if the current object is already in model properties to simplify routing
      if (this.areObjectsInSamePropertyPanel(this.getPanelInfo().type, AnalyticModelObjectType.MODEL)) {
        this.getModelPropertiesView().getController().updateModelHeaderInfo();
        this.getModelPropertiesView().getController().setObjectForInternalRouting(panelType, panelProperty);
      } else {
        Util.Component.updatePropertyPanel([Util.Component.getDefaultModelClassDef()]);
      }
      await Util.Component.updateDataPreviewPanel(undefined);
    }
    return true;
  }

  public async onModelPreviewButtonPress(
    event: IEvent<sap.m.SegmentedButton, { item: sap.m.SegmentedButtonItem }>
  ): Promise<any> {
    const selectedTab = event.getSource().getSelectedItem();
    if (selectedTab === CubeBuilderEditorTabs.cubeDataPreview) {
      Util.Component.router.navToPreview();
    } else {
      Util.Component.router.navToModelPanel();
    }
  }

  public async updateModelPreviewTab(tab: string) {
    this.view.setBusy(true);
    this.cubeBuilderSelectedTab = tab;
    // update the hash
    Util.Component.updateCurrentEditorOption(this.cubeBuilderSelectedTab);
    // make the needed ui changes
    await this.toggleSelectedView();
    this.view.setBusy(false);
    recordUsageTrackingEvent({
      action: editorTabToLaunchUsageAction[this.cubeBuilderSelectedTab],
      feature: getCurrentFeatureForUsageTracking(),
      eventtype: EventType.CLICK,
    });
  }

  public async goToIncorrectProperty(event: IEvent<sap.m.Link, {}>) {
    const propertyPath = event.getSource().getCustomData()[0].getValue().propertyPath;
    if (!propertyPath) {
      return;
    }

    const { objectType, objectName } = this.getObjectTypeAndNameFromValidationPropertyPath(propertyPath);

    this.goToProperty(objectType, objectName);

    if (this.validationMessagesPopover) {
      if (this.validationMessagesPopover.isOpen()) {
        this.validationMessagesPopover.close();
        return;
      }
      this.validationMessagesPopover.destroy();
      this.validationMessagesPopover = undefined;
    }
  }

  public getIncorrectPropertyText(validation: ValidationMessage) {
    if (!validation?.propertyPath) {
      return "";
    }
    const { objectType } = this.getObjectTypeAndNameFromValidationPropertyPath(validation.propertyPath);
    return Formatter.goToPropertyFormatter(objectType);
  }

  public incorrectPropertyTypeVisibleFormatter(path: string) {
    if (!path) {
      return false;
    }
    return Boolean(this.getObjectTypeAndNameFromValidationPropertyPath(path).objectType);
  }

  public getObjectTypeAndNameFromValidationPropertyPath(path: string): {
    objectType: AnalyticModelObjectType;
    objectName: string | undefined;
  } {
    // paths can be like /sourceModel/dimensionSources/1 or /measures/1
    // so the first part and the second part can be used to identify the object type
    // and the third part to identify the specific object from the that type
    const pathArray = path.split("/");
    const firstPath = pathArray?.[1];
    const secondPath = pathArray?.[2];
    const thirdPath = pathArray?.[3];

    const propertyPathTypeToObjectType = {
      measures: AnalyticModelObjectType.MEASURE,
      attributes: AnalyticModelObjectType.ATTRIBUTE,
      variables: AnalyticModelObjectType.VARIABLE,
      filter: AnalyticModelObjectType.FILTER,
      dataAccessControls: AnalyticModelObjectType.DAC,
      crossCalculations: AnalyticModelObjectType.CROSS_CALCULATION,
    };
    let objectType = propertyPathTypeToObjectType[firstPath];
    let encodedObjectName;
    if (objectType && objectType !== AnalyticModelObjectType.FILTER) {
      encodedObjectName = secondPath;
    } else if (firstPath === "sourceModel" && secondPath === "dimensionSources") {
      objectType = AnalyticModelObjectType.DIMENSION;
      encodedObjectName = thirdPath;
    } else if (firstPath === "sourceModel" && secondPath === "factSources") {
      objectType = AnalyticModelObjectType.FACT;
      encodedObjectName = thirdPath;
    } else if (firstPath === "nonCumulativeSettings") {
      const measures = this.model.getMeasures();
      const nonCumulativeMeasure = Object.keys(measures).find(
        (key) => measures[key].measureType === AnalyticModelMeasureType.NonCumulativeMeasure
      );
      if (nonCumulativeMeasure) {
        objectType = AnalyticModelObjectType.MEASURE;
        encodedObjectName = encodeURIComponent(nonCumulativeMeasure);
      }
    }
    const objectName = encodedObjectName ? decodeURIComponent(encodedObjectName) : undefined;
    return { objectType, objectName };
  }

  public resetModelDependentObjects() {
    this.resetDependentObjectsProperties();
    this.getModelPropertiesView()?.byId("dependentObjectPanel")?.setExpanded(false);
  }

  public async updateObjectStatusWithRepoInfo(resetLastVersion: boolean = true): Promise<any> {
    this.clearCrudCache();
    return Crud.get()
      .getDetails(
        [
          { type: SpaceType, name: this.getSpaceName() },
          { type: ModelType, name: this.model.getTechnicalName() },
        ],
        []
      )
      .then((details) => {
        const objectStatus: number = !!details["#objectStatus"]
          ? parseInt(details["#objectStatus"].toString(), 10)
          : ObjectStatus.notDeployed;
        this.uiModel.setProperty("/objectStatus", objectStatus);
        this.uiModel.setProperty("/deploymentDate", details?.deployment_date);
        if (resetLastVersion) {
          this.uiModel.setProperty("/revertLastVersion", false);
        }
      });
  }

  public async updatePackageStatusWithRepoInfo(): Promise<any> {
    const packageValue = this.uiModel.getProperty("/package");
    const pendingErrors = await RepoPackageService.getInstance().getDependentPendingErrors(
      this.getSpaceName(),
      packageValue,
      this.model.getTechnicalName()
    );
    await Util.Component.setPackageInfo(packageValue, pendingErrors);
  }

  public async onUndo() {
    if (!this.stack.canUndo()) {
      return;
    }

    const command = this.stack.undo();

    await this.undoRedoChangeHandler(command, true);
  }

  public async onRedo() {
    if (!this.stack.canRedo()) {
      return;
    }

    const command = this.stack.redo();

    await this.undoRedoChangeHandler(command, false);
  }

  public async undoRedoChangeHandler(command: Command<QueryUIModel>, isUndo: boolean) {
    const panelInfo = this.getPanelInfo();
    const commandInfo = command.getMetadata();

    // for rename operations we should not change panel, because the object does not exist anymore but the key was renamed
    const oldObjectId = panelInfo.object;
    if (commandInfo.isTechnicalNameChangeCommand) {
      const renameInfo = commandInfo.renameData?.find(
        (data) => data.type === panelInfo.type && (isUndo ? data.newKey : data.oldKey) === panelInfo.object
      );
      if (renameInfo) {
        panelInfo.object = isUndo ? renameInfo.oldKey : renameInfo.newKey;
      }
    }

    // for create operations in undo and delete operations in redo we should change panel if we are in the same panel
    let resetPanel = false;
    const objectExists = Boolean(this.getObjectFromPanelInfo(panelInfo));
    if (!objectExists) {
      resetPanel = true;
    }

    const shouldRouteToObject = oldObjectId !== panelInfo.object;

    // recompute ui model from specific panel we are in
    await this.setUiModelForUndoRedo(panelInfo.type, panelInfo.object, resetPanel, shouldRouteToObject);
  }

  public async setUiModelForUndoRedo(
    panelType: AnalyticModelObjectType,
    objectId: string,
    resetPanelToModel: boolean,
    shouldRouteToObject: boolean
  ) {
    // run validations and recompute output screen
    await Util.Component.modelChangeHandler();

    if (resetPanelToModel) {
      return this.resetPanel();
    }

    this.uiModel.setProperty("/routingFromSameObjectUndoRedo", !shouldRouteToObject);
    if (shouldRouteToObject) {
      return this.goToProperty(panelType, objectId, true);
    }

    if (panelType === AnalyticModelObjectType.ATTRIBUTE) {
      this.uiModel.setProperty("/attributeDetails", {});
      const attribute = this.model.getAttribute(objectId);
      if (!attribute) {
        return this.resetPanel();
      }

      const attributeDetails = {
        ...attribute,
        keyOnModel: objectId,
      };
      this.uiModel.setProperty("/attributeDetails", attributeDetails);

      this.updateModelDisplayName();
      const headerDisplay = { businessName: attribute.text, technicalName: objectId };
      this.uiModel.setProperty("/header/displayName", headerDisplay);

      const attributeSource = getAttributeDisplaySource(attribute, this.model);

      this.createAndUpdateAttributeBreadcrumbInfo(attributeDetails, this.modelDisplayName, attributeSource);
      await this.getAttributePropertiesView().byId("attributeDetails").getController().onBeforeShow();
    } else if (panelType === AnalyticModelObjectType.DIMENSION || panelType === AnalyticModelObjectType.FACT) {
      let type: AnalyticModelSourceType;
      let sourceKey: string;
      let source: IAnalyticModelSource;
      if (panelType === AnalyticModelObjectType.FACT) {
        type = AnalyticModelSourceType.Fact;
        sourceKey = objectId;
        source = this.model.getFactSource(sourceKey);
      } else {
        type = AnalyticModelSourceType.Dimension;
        sourceKey = objectId;
        source = this.model.getDimensionSource(sourceKey);
      }

      if (!source) {
        return this.resetPanel();
      }

      if (!(await Util.Diagram.isDiagramNodeSelected(type, sourceKey))) {
        // requires reselecting the node
        // no routing as route should already have the node
        await Util.Diagram.selectNodeByTypeAndId(type, sourceKey);
        await Util.Component.updateDataPreviewPanel(source.dataEntity.key);
      }
      // only update properties

      const gObject = await Util.Diagram.getNode(type, sourceKey);
      this.uiModel.setProperty("/diagramSelectedSource", gObject.originalObject);
      this.setNodeDetails();
      const originalTechnicalName = this.uiModel.getProperty("/nodeDetails/id");
      this.uiModel.setProperty("/nodeDetails/originalTechnicalName", originalTechnicalName);
      await this.setupUIModelForNodeProperties(gObject.originalObject.dataEntity, gObject.originalObject);

      // node properties header doesn't have technical name if dimension handling capability is false
      const validTechnicalName = this.model.getDimensionHandlingCapability()
        ? originalTechnicalName
        : this.uiModel.getProperty("/diagramSelectedSource/text");

      const headerInfo = {
        businessName: this.uiModel.getProperty("/diagramSelectedSource/text"),
        technicalName: validTechnicalName,
      };
      // update the breadcrumb and header info
      this.updateModelDisplayName();
      this.createAndUpdateSourceBreadcrumbInfo(headerInfo, this.modelDisplayName);
      this.uiModel.setProperty("/header/displayName", this.getSourceHeader(headerInfo, type));

      await this.getNodePropertiesView().getController().onBeforeShow();
    } else {
      await this.setUiModelForModelProperties(panelType, objectId);
    }
  }

  private async resetPanel(replaceHash = true) {
    Util.Component.router.navToModelPanel(replaceHash);
  }

  public async setUiModelForModelProperties(panelType: AnalyticModelObjectType, panelProperty: string) {
    if (panelType === AnalyticModelObjectType.MEASURE) {
      await this.setUiModelForMeasurePanel(panelProperty);
    } else if (panelType === AnalyticModelObjectType.VARIABLE) {
      await this.setUiModelForVariablePanel(panelProperty);
    } else if (panelType === AnalyticModelObjectType.FILTER) {
      await this.setUiModelForFilterPanel();
    } else if (panelType === AnalyticModelObjectType.DAC) {
      await this.setUiModelForDataAccessControlPanel(panelProperty);
    } else if (panelType === AnalyticModelObjectType.MODEL) {
      this.updateModelHeaderInfo();
    } else if (panelType === AnalyticModelObjectType.CROSS_CALCULATION) {
      await this.setUIModelForCrossCalculation(panelProperty);
    }
  }

  public async setUIModelForCrossCalculation(crossCalculationId: string) {
    const crossCalculation = Util.clone(
      (this.uiModel.getProperty("/crossCalculations") as UICrossCalculations).find(
        (item) => item.technicalName === crossCalculationId
      )
    );
    if (!crossCalculation) {
      return Util.Component.router.navToModelPanel(true);
    }
    this.setupCrossCalculationDetails(crossCalculation);
    await this.getModelPropertiesView().byId("crossCalculationDetails").getController().onBeforeShow();
  }

  public async setUiModelForMeasurePanel(measureId, newMeasure = false) {
    const measureDetails = (this.uiModel.getProperty("/modelMeasures") as UIMeasures).find(
      (item) => item.technicalName === measureId
    );
    if (!measureDetails) {
      return Util.Component.router.navToModelPanel(true);
    }
    this.setupMeasureDetails(measureDetails, newMeasure);
    await this.getModelPropertiesView().byId("measureDetails").getController().onBeforeShow();
  }

  public async setUiModelForVariablePanel(variableId) {
    const variableModel = (this.uiModel.getProperty("/variableModel") as UIVariables).find(
      (item) => item.key === variableId
    );
    if (!variableModel) {
      return Util.Component.router.navToModelPanel(true);
    }
    this.setupVariableDetails(variableModel);
    await this.getModelPropertiesView().byId("variableDetails").getController().onBeforeShow();
  }

  public async setUiModelForFilterPanel() {
    const globalFilter = this.uiModel.getProperty("/globalFilter");
    if (!globalFilter?.[0]) {
      return Util.Component.router.navToModelPanel(true);
    }
    this.setupFilterDetails(globalFilter);
    await this.getModelPropertiesView().byId("globalFilterDetails").getController().onBeforeShow();
  }

  public async setUiModelForDataAccessControlPanel(dataAccessControlId: string) {
    const dataAccessControl = (this.uiModel.getProperty("/dataAccessControls") as UIDacs).find(
      (item) => item.key === dataAccessControlId
    );
    if (!dataAccessControl) {
      return Util.Component.router.navToModelPanel(true);
    }
    this.setupDacDetails(dataAccessControl);
    await this.getModelPropertiesView().byId("dataAccessControlDetails").getController().onBeforeShow();
  }

  public getPanelInfo(): { type: AnalyticModelObjectType; object: string } {
    const panel = this.workbench.getPropertiesController()?.["currentObject"];
    const panelName = panel?.classDefinition.qualifiedName;

    let type;
    let object;
    if (panelName === PropertiesPanels.MODEL) {
      const navContainerView = this.getModelPropertiesView().byId("outputNavContainer").getCurrentPage()["sViewName"];
      if (navContainerView === ModelPropertiesViews.MEASURE) {
        type = AnalyticModelObjectType.MEASURE;
        object = this.uiModel.getProperty("/measureDetails/technicalName");
      } else if (navContainerView === ModelPropertiesViews.VARIABLE) {
        type = AnalyticModelObjectType.VARIABLE;
        object = this.uiModel.getProperty("/variableDetails/originalTechnicalName");
      } else if (navContainerView === ModelPropertiesViews.DAC) {
        type = AnalyticModelObjectType.DAC;
        object = this.uiModel.getProperty("/dataAccessControlDetails/key");
      } else if (navContainerView === ModelPropertiesViews.FILTER) {
        type = AnalyticModelObjectType.FILTER;
      } else if (navContainerView === ModelPropertiesViews.CROSS_CALCULATION) {
        type = AnalyticModelObjectType.CROSS_CALCULATION;
        object = this.uiModel.getProperty("/crossCalculationDetails/technicalName");
      } else {
        type = AnalyticModelObjectType.MODEL;
      }
    } else if (panelName === PropertiesPanels.ATTRIBUTE) {
      type = AnalyticModelObjectType.ATTRIBUTE;
      object = this.uiModel.getProperty("/attributeDetails/keyOnModel");
    } else if (panelName === PropertiesPanels.NODE) {
      object = this.uiModel.getProperty("/nodeDetails/id");
      const nodeType = this.uiModel.getProperty("/nodeDetails/type");
      if (nodeType === AnalyticModelSourceType.Fact) {
        type = AnalyticModelObjectType.FACT;
      } else {
        type = AnalyticModelObjectType.DIMENSION;
      }
    }

    return {
      type,
      object,
    };
  }

  public areObjectsInSamePropertyPanel(objectType1: AnalyticModelObjectType, objectType2: AnalyticModelObjectType) {
    const properties1 = CubeBuilderObjects.ObjectTypeToPropertyPanelType[objectType1];
    const properties2 = CubeBuilderObjects.ObjectTypeToPropertyPanelType[objectType2];
    return properties1 && properties2 && properties1 === properties2;
  }

  public getObjectFromPanelInfo(panelInfo: { type: AnalyticModelObjectType; object: string }) {
    if (panelInfo.type === AnalyticModelObjectType.ATTRIBUTE) {
      return this.model.getAttribute(panelInfo.object);
    }
    if (panelInfo.type === AnalyticModelObjectType.DIMENSION) {
      return this.model.getDimensionSource(panelInfo.object);
    }
    if (panelInfo.type === AnalyticModelObjectType.FACT) {
      return this.model.getFactSource(panelInfo.object);
    }
    if (panelInfo.type === AnalyticModelObjectType.FILTER) {
      return this.model.getGlobalFilter();
    }
    if (panelInfo.type === AnalyticModelObjectType.MEASURE) {
      return this.model.getMeasure(panelInfo.object);
    }
    if (panelInfo.type === AnalyticModelObjectType.VARIABLE) {
      return this.model.getVariable(panelInfo.object);
    }
    if (panelInfo.type === AnalyticModelObjectType.DAC) {
      return this.model.getDataAccessControl(panelInfo.object);
    }
    if (panelInfo.type === AnalyticModelObjectType.CROSS_CALCULATION) {
      return this.model.getCrossCalculation(panelInfo.object);
    }
    if (panelInfo.type === AnalyticModelObjectType.MODEL) {
      // we can return anything that evaluates to true here
      return this.model;
    }
  }

  public getModelPropertiesView(): ModelPropertiesView {
    return this.workbench
      .getView()
      .byId("graphSplitView--PropertyPanel--cubebuilder-properties-ModelProperties") as unknown as ModelPropertiesView;
  }

  public getNodePropertiesView(): NodePropertiesView {
    return this.workbench
      .getView()
      ?.byId("graphSplitView--PropertyPanel--cubebuilder-properties-NodeProperties") as unknown as NodePropertiesView;
  }

  public getAttributePropertiesView(): AttributePropertiesView {
    return this.workbench
      .getView()
      .byId(
        "graphSplitView--PropertyPanel--cubebuilder-properties-AttributeProperties"
      ) as unknown as AttributePropertiesView;
  }

  /**
   * the AM/cubeModel is constructed in the backend given needed data
   * This method is called twice: when converting a view to an AM and when importingAssociations i.e. creating a new model
   * @param factSource
   * @param associationsToDimensions
   * @param parameterMappings
   */
  private async loadAnalyticalDataSet(
    factSource: IAnalyticModelRepositoryEntity,
    associationsToDimensions: string[],
    parameterMappings: IParameterMappings
  ) {
    if (this.uiModel.getProperty("/addAllMeasuresSelected")) {
      recordUsageTrackingEvent({
        action: USAGE_ACTIONS.ADD_ALL_FACT_SOURCE_MEASURES,
        feature: getCurrentFeatureForUsageTracking(),
        eventtype: EventType.CLICK,
      });
    }
    if (this.uiModel.getProperty("/addAllAttributesSelected")) {
      recordUsageTrackingEvent({
        action: USAGE_ACTIONS.ADD_ALL_ATTRIBUTES,
        feature: getCurrentFeatureForUsageTracking(),
        eventtype: EventType.CLICK,
      });
    }
    associationsToDimensions.forEach((dimension) => {
      recordUsageTrackingEvent({
        action: USAGE_ACTIONS.ADD_ASSOCIATED_DIMENSION,
        feature: getCurrentFeatureForUsageTracking(),
        eventtype: EventType.CLICK,
        options: [
          {
            param: "target",
            value: dimension,
          },
        ],
      });
    });

    const elements: CreateCubeModelFromAnalyticalDatasetResult = await this.prepareAnalyticalDataForConversion(
      parameterMappings,
      factSource,
      associationsToDimensions
    );
    return this.setQueryModelProperties(elements);
  }

  private async prepareAnalyticalDataForConversion(
    parameterMappings: IParameterMappings,
    factSource: IAnalyticModelRepositoryEntity,
    associationsToDimensions: string[]
  ) {
    /** Filter over the parameterMappings to not have Inherited type  */
    const filteredParameterMappings = Object.keys(parameterMappings).reduce((acc, key) => {
      if (parameterMappings[key].mappingType !== AnalyticModelSourceParameterMappingType.Inherited) {
        acc[key] = parameterMappings[key];
      }
      return acc;
    }, {});
    const analyticalDataSet: CreateCubeModelFromAnalyticalDatasetParameter = {
      analyticalDataSet: factSource,
      associationsToDimensions: associationsToDimensions,
      allMeasures: this.uiModel.getProperty("/addAllMeasuresSelected"),
      allAttributes: this.uiModel.getProperty("/addAllAttributesSelected"),
      allCrossCalculations: this.uiModel.getProperty("/addAllCrossCalculationsSelected"),
      parameterMappings: filteredParameterMappings,
    };
    const elements: CreateCubeModelFromAnalyticalDatasetResult = await this.convertAnalyticalDataSet(analyticalDataSet);
    return elements;
  }

  private getParameterMapping() {
    const parameterMappings: IParameterMappings = {};
    (
      (this.takeOverDialog as sap.m.Dialog)
        .getModel("takeOverDialogModel")
        .getProperty("/parameters") as TakeOverDialogModelData["parameters"]
    ).forEach((param) => {
      const mappingType = param.selectedType;
      parameterMappings[param.key] = {
        mappingType: mappingType,
        constantValue: CubeBuilderObjects.ConstantMappingTypes.includes(mappingType) ? param.value : undefined,
        variableName:
          mappingType === AnalyticModelSourceParameterMappingType.MapToSourceParameter ? param.key : undefined,
      };
    });
    return parameterMappings;
  }

  /**
   * Fetches the parameters from the variables
   * @returns
   */
  public getInputParametersFromVariables(): IInputParameter[] {
    const parameters: IInputParameter[] = [];
    const variables = this.model.getVariables();

    for (const variableKey in variables) {
      const variable = variables[variableKey];
      if (
        variable.variableProcessingType === AnalyticModelVariableProcessingType.LOOKUP ||
        variable.parameterType === AnalyticModelParameterType.FiscalVariant
      ) {
        continue;
      }

      const paramType = getDisplayDataType(
        this.getVariableTypeInfoFromSource(variableKey, variable.parameterType, (variable as any).referenceAttribute)
      );

      const selectionType = this.getSelectionType(variable);

      const defaultValues = !isVariableDerivationOption(variable)
        ? this.getDefaultValue(selectionType, variable.defaultValue)
        : [{ value: undefined }];

      parameters.push({
        name: variableKey,
        dataType: paramType,
        processingType: variable.variableProcessingType,
        defaultValues,
        order: variable.order,
        selectionType: selectionType,
        values: getInitialValues(selectionType, defaultValues, paramType),
      });
    }

    const stackedVariables = this.stackedVariable.getStackedVariables();
    if (stackedVariables) {
      for (const stackedVariable of Object.values(stackedVariables)) {
        if (stackedVariable.variableProcessingType === AnalyticModelVariableProcessingType.LOOKUP) {
          continue;
        }

        const selectionType = this.getSelectionType(stackedVariable);
        const defaultValues = !isVariableDerivationOption(stackedVariable)
          ? this.getDefaultValue(selectionType, stackedVariable.defaultValue)
          : [{ value: undefined }];

        parameters.push({
          name: stackedVariable.key,
          dataType: stackedVariable.dataType,
          defaultValues,
          order: stackedVariable.order,
          selectionType,
          values: getInitialValues(selectionType, defaultValues, stackedVariable.parameterType),
        });
      }
    }

    if (parameters[0]?.order !== undefined) {
      parameters.sort((a, b) => a.order - b.order);
    }

    return parameters;
  }

  private getDefaultValue(selectionType: ParameterSelectionType, defaultValue: any): IParameterValues[] {
    if (defaultValue === undefined || (Array.isArray(defaultValue) && !defaultValue.length)) {
      return [{ value: undefined }];
    }

    if (selectionType === ParameterSelectionType.MULTIPLE_SINGLE_VALUE) {
      if (!Array.isArray(defaultValue)) {
        return [{ value: undefined }];
      }
      return defaultValue.map((value) => ({ value }));
    }

    if (selectionType === ParameterSelectionType.INTERVAL) {
      if (!this.featureflags.DWCO_MODELING_AM_MULTI_RANGE) {
        return [
          {
            value: defaultValue.lowValue,
            valueHigh: defaultValue.highValue,
            operator: VariableOperator.IN,
          },
        ];
      }

      const index =
        defaultValue.highValue !== undefined
          ? AnalyticModelDefaultIntervalOption.BT
          : AnalyticModelDefaultIntervalOption.EQ;
      return [
        {
          value: defaultValue.lowValue,
          valueHigh: defaultValue.highValue,
          operator: oDataOperatorIntervalMapping[index],
        },
      ];
    }

    if (selectionType === ParameterSelectionType.RANGE) {
      if (this.featureflags.DWCO_MODELING_AM_MULTI_RANGE && Array.isArray(defaultValue)) {
        return defaultValue.map(({ lowValue, highValue, option }) => ({
          value: lowValue,
          valueHigh: highValue,
          operator: oDataOperatorMapping[option],
        }));
      }

      const { lowValue, highValue, option } = defaultValue[0];
      return [
        {
          value: lowValue,
          valueHigh: highValue,
          operator: oDataOperatorMapping[option],
        },
      ];
    }

    return [{ value: defaultValue }];
  }

  private getSelectionType(variable: AllQueryParameter): ParameterSelectionType {
    if (!this.isMultiValued(variable)) {
      return ParameterSelectionType.SINGLE_VALUE;
    }

    const multipleSelections = variable.multipleSelections;
    const selectionType = variable.selectionType;
    return oDataSelectionTypeMapping(multipleSelections)[selectionType];
  }

  private isMultiValued(variable: object): variable is AnalyticModelVariable {
    return variable.hasOwnProperty("multipleSelections") && variable.hasOwnProperty("selectionType");
  }

  private async setQueryModelProperties(elements: CreateCubeModelFromAnalyticalDatasetResult) {
    // TODO - elements.hasError is not considered here!
    const newQueryModel = this.createQueryFromElements(elements);
    if (elements.hasWarnings) {
      this.displayTakeOverWarnings(elements.warnings);
    }
    const updateModelCommand = new UpdateModel(newQueryModel as Query);
    this.stack.execute(updateModelCommand);
    // clear whatever data related to old selection
    // this.clearSelectedEntityUiProperties();
    return this.getSourceEntityDetails(this.model.getSourceModel()).then(async () => {
      this.setIsStackedModel();
      await Util.Component.modelChangeHandler(undefined, true);

      // need be called after modelChangeHandler to ensure the model's attributeTree property is set
      this.addFiscalVariantVariable();

      if (this.uiModel.getProperty("/isStackedModel")) {
        recordUsageTrackingEvent({
          action: USAGE_ACTIONS.CREATE_STACKED_MODEL,
          feature: getCurrentFeatureForUsageTracking(),
          eventtype: EventType.CLICK,
        });
      } else {
        this.recordAnalyticModelCreationEvent();
      }
    });
  }

  private createQueryFromElements(elements: CreateCubeModelFromAnalyticalDatasetResult) {
    const oldIdentifier = this.model.getIdentifier();
    const oldBusinessName = this.model.getBusinessName();
    const newQueryModel = elements.cubeModel;
    newQueryModel.identifier = oldIdentifier;
    newQueryModel.text = oldBusinessName;
    return newQueryModel;
  }

  private recordAnalyticModelCreationEvent() {
    if (!this.uiModel.getProperty("/isConvertedFromADS")) {
      return;
    }

    const allSourceDataEntityDetails = this.uiModel.getProperty("/allSourceDataEntityDetails");
    const factSourceKey = this.model.getFirstFactSourceKey();
    const technicalType = this.getTechnicalType(allSourceDataEntityDetails, factSourceKey);

    if (!(technicalType === RepositoryObjectType.DWC_LOCAL_TABLE || technicalType === RepositoryObjectType.DWC_VIEW)) {
      return;
    }

    recordUsageTrackingEvent({
      action: USAGE_ACTIONS.CREATE_NEW_QUERY_MODEL,
      feature: DWCFeature.DATA_BUILDER,
      eventtype: EventType.CLICK,
      options: [
        {
          param: "target",
          value: NewModelTypes.QUERYMODEL,
        },
      ],
    });
  }

  private recordAddAllCrossCalculationsUsage() {
    const addAllCrossCalculations = this.uiModel.getProperty("/addAllCrossCalculationsSelected");
    if (!addAllCrossCalculations) {
      return;
    }

    recordUsageTrackingEvent({
      action: USAGE_ACTIONS.ADD_ALL_FACT_SOURCE_CROSS_CALCULATIONS,
      feature: getCurrentFeatureForUsageTracking(),
      eventtype: EventType.CLICK,
    });
  }

  private getTechnicalType(allSourceDataEntityDetails: IDataEntityDetailsResponse, key: string): RepositoryObjectType {
    const dataEntity = allSourceDataEntityDetails[key];
    return dataEntity?.technicalType as RepositoryObjectType;
  }

  private async convertAnalyticalDataSet(analyticalDataSet): Promise<CreateCubeModelFromAnalyticalDatasetResult> {
    try {
      const oRequestOptions: JQueryAjaxSettings = {
        url: `querybuilder/${super.getSpaceName()}/convertanalyticaldataset`,
        type: HttpMethod.POST,
        dataType: DataType.JSON,
        data: JSON.stringify(analyticalDataSet),
        contentType: ContentType.APPLICATION_JSON,
      };
      return await ServiceCall.request(oRequestOptions).then(
        (response) => response.data as CreateCubeModelFromAnalyticalDatasetResult
      );
    } catch (e) {
      await MessageHandler.exception({ message: this.getText("loadAnalyticalDataSetError"), exception: e });
    }
  }

  private clearCrudCache() {
    Crud.get().clearCache([
      { type: SpaceType, name: this.getSpaceName() },
      { type: ModelType, name: this.model.getTechnicalName() },
    ]);
  }

  public async onDeployNotification(channel: string, eventName: string, data: any): Promise<void> {
    if (!(channel === notificationsEventChannel && eventName === notificationsFetched && data?.length)) {
      return;
    }
    const parsedInfo: any = parseNotificationData(
      {
        spaceId: this.spaceName,
        modelId: this.model.getTechnicalName(),
      },
      data
    );
    if (parsedInfo.processed) {
      await this.updateObjectStatusWithRepoInfo();
    }
  }

  private resetUiModel(isNew: boolean) {
    // reset edit mode
    this.uiModel.setProperty("/isEditMode", isNew ? false : true);
    this.uiModel.setProperty("/newTechnicalName", false);

    // clear selected entity
    this.clearSelectedEntityUiProperties();

    // clear AM properties
    this.uiModel.setProperty("/attributeGrouping", []);
    this.uiModel.setProperty("/modelMeasures", []);
    this.uiModel.setProperty("/variableModel", []);
    this.uiModel.setProperty("/dataAccessControls", []);
    this.uiModel.setProperty("/globalFilter", []);
    this.uiModel.setProperty("/modelFilters", []);
    this.uiModel.setProperty("/allSourceDataEntityDetails", {});
    this.uiModel.setProperty("/attributeTree", []);
    this.uiModel.setProperty("/attributeModelMap", {});
    this.uiModel.setProperty("/measureModelMap", {});
    this.uiModel.setProperty("/package", "_NONE_KEY_PACKAGE_");
    this.uiModel.setProperty("/packageStatus", "");
    this.uiModel.setProperty("/crossCalculations", []);
    this.uiModel.setProperty("/crossCalculationModelMap", {});
    this.uiModel.setProperty("/collisionHandlingPriority", undefined);

    // clear validation messages
    this.clearValidationMessages();

    // clear preview ui and reset visibility
    this.clearDataPreviewProperties();
    this.resetDataPreview();
  }

  private modelHasSources(): boolean {
    const currentFactSources = this.model.getFactSources();
    // temporary check to limit number of selected fact sources to one (later use the code below)
    return currentFactSources && Object.keys(currentFactSources).length > 0;
  }

  /**
   * Handler for dragging and dropping a source to the editor
   *
   * @param {any} selectedSource object dropped into the editor
   */
  public async onAddSource(event, selectedSource) {
    // check for when selecting a section instead of an object
    if (!selectedSource) {
      return;
    }
    if (this.modelHasSources()) {
      // replace the fact source if a new fact source is dropped on the fact source node in the diagram
      if (this.featureflags.DWCO_MODELING_AM_REPLACE_SOURCES && this.featureflags.DWCO_MODELING_AM_NEW_DIM_HANDLING) {
        if (await Util.Diagram.isCoordinateInsideFactSourceNode(event.clientX, event.clientY)) {
          // dropped on fact source node: validation and confirmation
          const factSource = this.model.getFirstFactSource().dataEntity.key;
          if (selectedSource.qualifiedName === factSource) {
            sap.m.MessageToast.show(this.getText("noFactSourceReplaceItself"));
            return;
          }
          if (selectedSource["#technicalType"] === "DWC_ANALYTIC_MODEL") {
            sap.m.MessageToast.show(this.getText("replaceForStackedFactSourceNotSupported"));
            return;
          }
          await Util.Diagram.removeAnalyticModelDropOnFactSourceHighlight();
          this.uiModel.setProperty("/diagramDropTarget", Object.keys(this.model.getFactSources())[0]);
          await this.validateFactSourceReplacement(selectedSource.qualifiedName);
        } else {
          sap.m.MessageToast.show(this.getText("dropOnFactSourceNode"));
        }
        return;
      } else {
        const shouldDisplayTechnicalName = this.user.getObjectNameDisplay() === ObjectNameDisplay.technicalName;
        const displayName = shouldDisplayTechnicalName ? selectedSource.qualifiedName : selectedSource.label;
        const alreadySelectedMessage = this.getText("alreadySelected");
        sap.m.MessageToast.show(`${alreadySelectedMessage}: ${displayName}`);
        return;
      }
    }
    try {
      this.view.setBusy(true);

      const spaceName = this.getSpaceName();
      let objectDetails;
      const isSharedObject = selectedSource.crossSpaceName && selectedSource.crossSpaceName !== spaceName;
      if (isSharedObject) {
        const shareForTarget: { results: Array<{ spaceName: string; name: string; properties: any }> } =
          await getArtefactSharesForTarget(
            spaceName,
            ["csn", "#isToolingHidden"], // csn is required in the request to compute isToolingHidden
            [`name:${selectedSource.name}`]
          );
        objectDetails = shareForTarget?.results?.find(
          (object) => object.spaceName === selectedSource.crossSpaceName && object.name === selectedSource.name
        )?.properties;
      } else {
        objectDetails = await Crud.get().getDetails(
          [
            { type: SpaceType, name: this.getSpaceName() },
            { type: ModelType, name: selectedSource.qualifiedName },
          ],
          ["#isToolingHidden"]
        );
      }

      const isToolingHidden = objectDetails["#isToolingHidden"];
      if (
        (typeof isToolingHidden === "string" && isToolingHidden === "true") ||
        (typeof isToolingHidden === "boolean" && isToolingHidden)
      ) {
        const deltaObjectsNotSupportedMessage = this.getText("deltaTablesNotSupported");
        sap.m.MessageToast.show(deltaObjectsNotSupportedMessage);
        return;
      }
    } catch (error) {
      await MessageHandler.exception({
        exception: error,
        title: this.getText("internalServerError"),
        message: this.getText("dataEntityDetailsSelectedObjectError"),
      });
      return;
    } finally {
      this.view.setBusy(false);
    }

    const originSourceKey = this.addSourceToModel(selectedSource);
    await this.takeOverFromFactSource(selectedSource, originSourceKey);
  }

  private setAllMeasuresAndAttributesProperty(sourceDataEntity) {
    sourceDataEntity.attributes.length > 0
      ? this.uiModel.setProperty("/visibleSelectAllAttributes", true)
      : this.uiModel.setProperty("/visibleSelectAllAttributes", false);
    sourceDataEntity.measures.length > 0
      ? this.uiModel.setProperty("/visibleSelectAllMeasures", true)
      : this.uiModel.setProperty("/visibleSelectAllMeasures", false);
  }

  private setEnableImportDimensionsAndParametersProperty(enableParametersAndAssociations) {
    // TODO remove the property when we introduce dimensions and parameters for stacking
    this.uiModel.setProperty("/visibleImportDimensions", enableParametersAndAssociations);
    this.uiModel.setProperty("/visibleImportParameters", enableParametersAndAssociations);
  }

  private async takeOverFromFactSource(selectedSource, originSourceKey: string) {
    try {
      const key = selectedSource.qualifiedName;
      this.view.setBusy(true);
      const sourceDataEntity = await DataEntityDetailsService.getInstance().loadDataEntityDetails(
        [{ key }],
        super.getSpaceName()
      );

      await this.updateIsStackedModelProperty(sourceDataEntity[key]);
      // const factSourceTechnicalType = selectedSource["#technicalType"];
      const enableParametersAndAssociations = true; // factSourceTechnicalType !== RepositoryObjectType.DWC_ANALYTIC_MODEL;
      let dimensionAssociations: IAnalyticModelDimensionAssociation[] = [];
      let dimensionParameters: TakeOverDialogParameter[] = [];

      this.setAllMeasuresAndAttributesProperty(sourceDataEntity[key]);
      this.setEnableImportDimensionsAndParametersProperty(enableParametersAndAssociations);

      const source = sourceDataEntity[key];
      const { associations, parameters, measures, crossCalculations } = source;
      const sourceViewName = selectedSource.displayLabel;
      this.setAssociateDialogUiProperties(true);

      if (enableParametersAndAssociations) {
        if (parameters) {
          dimensionParameters = this.setupTakeOverDialogParameters(parameters, measures, crossCalculations);
          // DS00-4070# For stacked variables with filter types Range/Interval, value help requires referenceAttribute and its mapping details in model.
          // Hence, convertAnalyticalDataSet is invoked here to ensure accurate referenceAttributeSource information.
          if (this.isStackedModel() && this.featureflags.DWCO_MODELING_AM_SET_VALUE_STACKING_VARIABLE) {
            await this.loadDataSetForStackVariableValueHelp(dimensionParameters, sourceDataEntity, key);
          }
          this.uiModel.setProperty("/dataEntityParameters", dimensionParameters);
          /** setting up the ActionType selection , this should happen after updateIsStackedModelProperty call to update the isStackedModel */
        }
        if (associations) {
          dimensionAssociations = associations
            .map((association) => ({
              businessType: association.businessType,
              sourceKey: originSourceKey,
              targetEntity: {
                text: association.targetEntity.text,
                key: association.targetEntity.key,
                spaceKey: super.getSpaceName(),
                icon: "sap-icon://sac/perspectives",
              },
              foreignKey: association.foreignKeys,
              targetKey: association.targetKeys,
              associationName: {
                text: association.text,
                key: association.key,
                icon: "sap-icon://sac/association",
              },
              dimensionSourceAlias: association.dimensionSourceAlias,
              isForeignKeyAssociation:
                !this.featureflags.DWCO_MODELING_AM_NEW_DIM_HANDLING ||
                !isNonForeignKeyAssociation(association, source, AnalyticModelSourceType.Fact),
            }))
            .filter((association) => {
              const isAssociationToDimension = association.businessType === "DWC_DIMENSION";
              const isForeignKeyAssociation = association.isForeignKeyAssociation;
              return isAssociationToDimension && isForeignKeyAssociation;
            });
        }
      }

      if (!this.takeOverDialog) {
        const fragmentId = require("../view/TakeOverDialog.fragment.xml");
        this.takeOverDialog = sap.ui.xmlfragment("TakeOverDialog", fragmentId, this) as sap.m.Dialog;
        this.view.addDependent(this.takeOverDialog);
      }

      this.takeOverDialog.setBusy(true);

      const dialogModel = new sap.ui.model.json.JSONModel({
        associations: dimensionAssociations,
        parameters: dimensionParameters,
        sourceViewName: sourceViewName,
        variablesKeysUsedByCrossCalculations: this.getListOfVariablesKeysUsedBy(source.crossCalculations),
        variablesKeysUsedByMeasures: this.getListOfVariablesKeysUsedBy(source.measures),
      } as TakeOverDialogModelData);
      this.takeOverDialog.setModel(dialogModel, "takeOverDialogModel");
      this.takeOverDialog.open();

      const table = sap.ui.getCore().byId("TakeOverDialog--addAssociationTable") as sap.ui.table.Table;
      table.selectAll();

      this.sortTakeoverDialogParametersTableByDisplayName();

      this.setCrossCalculationsCheckboxState(source);
    } catch (err: unknown) {
      if (this.isOutdatedModelError(err)) {
        await MessageHandler.exception({
          message: this.getText("loadDataEntityOutdatedModel"),
          exception: err,
        });
      } else {
        await MessageHandler.exception({ message: this.getText("loadDataEntityAssociationsError"), exception: err });
      }
    } finally {
      this.view.setBusy(false);
      this.takeOverDialog.setBusy(false);
    }
  }

  private async loadDataSetForStackVariableValueHelp(
    parameters: TakeOverDialogParameter[],
    sourceDataEntity: IDataEntityDetailsResponse,
    sourceKey: string
  ) {
    // Check if there is a Filter or Story Filter variable with a referenceAttribute and return true if found
    const hasReferenceAttribute = parameters.some(
      (parameter) =>
        (parameter.parameterType === AnalyticModelParameterType.Filter ||
          parameter.parameterType === AnalyticModelParameterType.StoryFilter) &&
        parameter.referenceAttribute
    );
    if (hasReferenceAttribute) {
      const factSource = this.uiModel.getProperty("/uiFactSourceModel").dataEntity;
      const associationsToDimensions = sourceDataEntity[sourceKey].associations
        .filter(
          (association) =>
            !this.featureflags.DWCO_MODELING_AM_NEW_DIM_HANDLING ||
            !isNonForeignKeyAssociation(association, sourceDataEntity[sourceKey], AnalyticModelSourceType.Fact)
        )
        .map((index) => index.key);
      const elements: CreateCubeModelFromAnalyticalDatasetResult = await this.prepareAnalyticalDataForConversion(
        {},
        factSource,
        associationsToDimensions
      );
      if (elements && elements?.cubeModel) {
        const queryModel = new QueryUIModel(this.createQueryFromElements(elements) as Query);
        parameters.forEach((parameter) => {
          if (parameter.referenceAttribute) {
            const modelAttribute = elements?.cubeModel?.attributes?.[parameter.referenceAttribute];
            if (modelAttribute) {
              const attribute = getSourcesFromAttribute(modelAttribute, queryModel);
              parameter.referenceAttributeSource = attribute?.source.dataEntity.key;
            }
          }
        });
      }
    }
  }

  private sortTakeoverDialogParametersTableByDisplayName() {
    const parameterTable = sap.ui.getCore().byId("TakeOverDialog--importParameterList") as sap.ui.table.Table;
    const parameterTableBinding = parameterTable.getBinding("rows") as sap.ui.model.json.JSONListBinding;
    parameterTableBinding.sort(new sap.ui.model.Sorter("displayName"));
  }

  /**
   * Retrieves a unique list of variable keys used by the provided sources.
   *
   * @param sources - An array of objects, each containing an optional `elements` array of strings.
   * @returns An array of unique variable keys extracted from the `elements` arrays of the sources.
   */
  private getListOfVariablesKeysUsedBy(sources?: Array<{ elements?: string[] }>): string[] {
    if (!sources) {
      return [];
    }

    const variablesKeys = new Set<string>();
    for (const source of sources) {
      const variablesKey = source.elements || [];
      for (const variableKey of variablesKey) {
        variablesKeys.add(variableKey);
      }
    }
    return Array.from(variablesKeys);
  }

  private isOutdatedModelError(err: unknown): boolean {
    return Array.isArray(err) && "responseJSON" in err[0] && err[0].responseJSON?.code === ErrorCodes.OUTDATED_MODEL;
  }

  private setupActionType(takeOverParameter: TakeOverDialogParameter) {
    const isStacked = this.isStackedModel();
    const isSetValueFFActive = this.featureflags.DWCO_MODELING_AM_SET_VALUE_STACKING_VARIABLE;
    if (isStacked) {
      const actionType = [{ type: AnalyticModelSourceParameterMappingType.Inherited, text: "txtInherit" }];
      if (
        isSetValueFFActive &&
        takeOverParameter.variableProcessingType !== AnalyticModelVariableProcessingType.LOOKUP &&
        takeOverParameter.parameterType !== AnalyticModelParameterType.KeyDate &&
        takeOverParameter.parameterType !== AnalyticModelParameterType.FiscalVariant
      ) {
        actionType.push({ type: takeOverParameter?.mappingType, text: "txtSetValue" });
      }
      return actionType;
    } else {
      return [
        { type: AnalyticModelSourceParameterMappingType.ConstantValue, text: "txtSetValue" },
        { type: AnalyticModelSourceParameterMappingType.MapToSourceParameter, text: "txtMapTo" },
      ];
    }
  }

  public async allMeasureSelectionChange() {
    if (this.isStackedModel()) {
      this.recalculateVariablesTable();
    }
    (this.takeOverDialog.getModel("takeOverDialogModel") as sap.ui.model.json.JSONModel).updateBindings(true);
  }

  /**
   * update isStackedModel in uiModel
   */
  private async updateIsStackedModelProperty(source) {
    if (
      this.featureflags.DWCO_MODELING_ANALYTIC_MODEL_STACKING &&
      this.featureflags.DWCO_MODELING_AM_NEW_DIM_HANDLING
    ) {
      this.uiModel.setProperty(
        "/isStackedModel",
        Boolean((source?.csn["@ObjectModel.modelingPattern"]?.["#"] ?? "") === "ANALYTICAL_CUBE")
      );
    } else {
      this.uiModel.setProperty("/isStackedModel", false);
    }
  }

  private setupTakeOverDialogParameters(
    parameters: IDataEntityParameter[],
    measures: IDataEntityMeasure[],
    crossCalculations?: IDataEntityCrossCalculation[]
  ): TakeOverDialogParameter[] {
    let paramsArray: TakeOverDialogParameter[] = [];
    parameters.forEach((param: any) => {
      const valueHelpDefinition = param[CsnAnnotations.Consumption.valueHelpDefinition]?.[0]?.entity;
      const takeOverParameter: TakeOverDialogParameter = {
        ...param,
        displayName: this.user.getObjectNameDisplay() === ObjectNameDisplay.technicalName ? param.key : param.text,
        length: param.length ? param.length : 500,
        dataType: param.type,
        selectedType: this.isStackedModel()
          ? AnalyticModelSourceParameterMappingType.Inherited
          : AnalyticModelSourceParameterMappingType.MapToSourceParameter,
        displayType: getDisplayDataType(param),
        defaultValue: param.default,
        value: this.isStackedModel() ? undefined : this.getText("txtNewInputParameter"),
        valueHelpColumn: valueHelpDefinition?.element || param?.valueHelpColumn,
        valueHelpSource: valueHelpDefinition?.name || param?.valueHelpSource,
        actionType: [],
      };
      if (this.isStackedModel() && this.featureflags.DWCO_MODELING_AM_SET_VALUE_STACKING_VARIABLE) {
        if (
          param.parameterType === AnalyticModelParameterType.Filter ||
          param.parameterType === AnalyticModelParameterType.StoryFilter
        ) {
          const parameterValues = this.stackedVariable.getParameterValuesForTakeOverDialogValueHelp(parameters);
          takeOverParameter.allParametersHaveValues = parameterValues?.allParametersHaveValues;
        }
        this.valueDefinitionUtils.setupInputConfigFromStackedVariable(
          param,
          new sap.ui.model.json.JSONModel(takeOverParameter),
          ""
        );
        takeOverParameter.mappingType = this.stackedVariable.computeMappingType(takeOverParameter);
      }
      takeOverParameter.actionType = this.setupActionType(takeOverParameter);
      paramsArray.push(takeOverParameter);
    });
    if (this.isStackedModel()) {
      // In Copy Property dialog: needs to display only the variables that are required or active.
      // Active variables are determined based on their usage on the data entity measures/crossCalculations in the model.
      // For that need to call setupActiveVariableListForTakeOverDialog to get the active variables and compare them with the takeOverParameters.
      this.stackedVariable.setupActiveVariableListForTakeOverDialog(
        measures,
        parameters as NodeParameters,
        crossCalculations
      );
      const activeParameterKeys = new Set(
        (this.uiModel.getProperty("/activeParameters") as NodeParameters).map((activeParam) => activeParam.key)
      );
      // Filter the paramsArray to only include parameters that are in the active list
      paramsArray = paramsArray.filter((param) => activeParameterKeys.has(param.key));
    }
    return paramsArray;
  }

  public onActionChangeType(oEvent: IEvent<sap.m.Select, any>) {
    const index = oEvent.getSource().getBindingContext("takeOverDialogModel").getPath().split("/")[2];
    const selectedObject = this.takeOverDialog.getModel("takeOverDialogModel").getData().parameters[index];
    const selectedKey = oEvent.getSource().getSelectedItem().getKey();
    selectedObject.selectedType = selectedKey;
    switch (selectedKey) {
      case AnalyticModelSourceParameterMappingType.MapToSourceParameter:
        selectedObject.targetParameter = this.getText("txtNewInputParameter");
        selectedObject.value = this.getText("txtNewInputParameter");
        break;
      case AnalyticModelSourceParameterMappingType.ConstantValue:
      case AnalyticModelSourceParameterMappingType.ConstantValues:
      case AnalyticModelSourceParameterMappingType.ConstantRange:
      case AnalyticModelSourceParameterMappingType.ConstantRanges:
      case AnalyticModelSourceParameterMappingType.ConstantInterval:
        const selectControl = oEvent.getSource();
        const defaultItem: sap.ui.core.Item[] = selectControl
          .getItems()
          .filter((item) =>
            CubeBuilderObjects.ConstantMappingTypes.includes(item.getKey() as AnalyticModelSourceParameterMappingType)
          );
        if (defaultItem?.length === 1) {
          selectControl.setSelectedItem(defaultItem[0]);
          if (this.isStackedModel() && this.featureflags.DWCO_MODELING_AM_SET_VALUE_STACKING_VARIABLE) {
            const defaultValue = this.stackedVariable.computeDefaultValueForVariable(
              selectedObject,
              selectedObject.defaultValue
            );
            selectedObject.value = defaultValue ?? selectedObject.defaultValue;
          } else {
            selectedObject.value = selectedObject.defaultValue;
          }
          selectedObject.targetParameter = undefined;
        }
        break;
      case AnalyticModelSourceParameterMappingType.Inherited:
        selectedObject.targetParameter = this.getText("txtInherit");
        selectedObject.value = undefined;
        break;
      default:
    }
    (this.takeOverDialog.getModel("takeOverDialogModel") as sap.ui.model.json.JSONModel).updateBindings(true);
  }

  public onValidateDecimal(oEvent: IEvent<sap.m.Input, any>) {
    const bindingContextObject = oEvent.getSource()?.getBindingContext("takeOverDialogModel")?.getObject();
    const type = bindingContextObject?.primitiveDataType;
    if (type === CDSDataType.DECIMAL) {
      const precision = bindingContextObject?.precision;
      const scale = bindingContextObject?.scale;
      if (precision && scale) {
        const errorText = this.getText("VAL_ENTER_VALID_DECIMAL_GEN", [precision, scale]);
        validateDecimalInput(oEvent, parseInt(precision, 10), parseInt(scale, 10), errorText);
      }
    }
  }

  private setAssociateDialogUiProperties(bValue) {
    this.uiModel.setProperty("/addAllMeasuresSelected", bValue);
    this.uiModel.setProperty("/addAllAttributesSelected", bValue);
  }

  private handleAssociationSearch(oEvent: IEvent<sap.m.Input, any>) {
    const table: sap.ui.table.Table = sap.ui
      .getCore()
      .byId("TakeOverDialog--addAssociationTable") as sap.ui.table.Table;
    const query = oEvent.getSource().getValue().trim();
    let nameFilter;
    if (query.length > 0) {
      nameFilter = new sap.ui.model.Filter({
        filters: [
          new sap.ui.model.Filter({
            path: "associationName/text",
            operator: sap.ui.model.FilterOperator.Contains,
            value1: query,
          }),
          new sap.ui.model.Filter({
            path: "targetEntity/text",
            operator: sap.ui.model.FilterOperator.Contains,
            value1: query,
          }),
        ],
        and: false,
      });
    }
    (table.getBinding("rows") as sap.ui.model.json.JSONListBinding).filter(nameFilter ? [nameFilter] : []);
  }

  public hasFollowUpAssociation(association) {
    const dimensions = this.model.getDimensionSources();
    const assocTargetKey = Object.keys(dimensions).find((key) => {
      const dimension = dimensions[key];
      return dimension.dataEntity.key === association.targetEntity.key;
    });

    return !Object.keys(dimensions).some((key) => {
      const dimension = dimensions[key];
      return dimension.associationContexts.some(
        (ctx) => ctx.sourceType === AnalyticModelSourceType.Dimension && ctx.sourceKey === assocTargetKey
      );
    });
  }

  public onAssociationDialogCancel() {
    this.takeOverDialog.close();
  }

  /**
   * Callback from the takeover dialog,
   */
  public async onImportAssociation() {
    this.view.setBusy(true);
    const associationTable: sap.ui.table.Table = sap.ui
      .getCore()
      .byId("TakeOverDialog--addAssociationTable") as sap.ui.table.Table;
    const selectedAssociationContext = associationTable.getSelectedIndices();
    const factSource = this.uiModel.getProperty("/uiFactSourceModel").dataEntity;

    let associationsToDimensions: string[] = [];
    if (selectedAssociationContext) {
      associationsToDimensions = selectedAssociationContext.map((index) => {
        const selectedAssociation =
          associationTable.getContextByIndex(index) && associationTable.getContextByIndex(index).getObject();
        return selectedAssociation.associationName.key;
      });
    }
    this.takeOverDialog.close();
    // the construction of the analyticModel depends on the selected factSource and the associations taken over there
    // TODO - separate this method into what is needed for model and what is needed just for UI
    const parameterMappings: IParameterMappings = this.getParameterMapping();
    await this.loadAnalyticalDataSet(factSource, associationsToDimensions, parameterMappings);
    await Util.Component.modelChangeHandler(undefined, true);
    await Util.Diagram.drawDiagram();
    this.setupProtectDataExportFlag();
    this.recordAddAllCrossCalculationsUsage();
    this.view.setBusy(false);
  }

  private displayTakeOverWarnings(warnings: CreateCubeModelFromAnalyticalDatasetResult["warnings"]) {
    const message = warnings.map((warning) => this.getText(warning.warning, warning.parameter)).join("\n\n");
    this.modelWarningMessageBox = MessageBox.warning(message, {
      id: "removeWarningDialog",
      title: this.getText("warning"),
      actions: [MessageBox.Action.CLOSE],
      emphasizedAction: MessageBox.Action.CLOSE,
      onClose: async (sAction: string) => {
        if (sAction === MessageBox.Action.CLOSE) {
          return;
        }
        (sap.ui.getCore().byId("removeWarningDialog") as sap.m.Dialog).destroy();
      },
    });
  }

  // for a node in the model get the next subnode to be used, i. e. one above the highest (e. g. ["1", "4", "6"] --> "7")
  private getNextSubnode(modelNode: any): string {
    const nodeKeys = Object.keys(modelNode || {});
    let highestKey = 0;
    nodeKeys.forEach((nodeKey) => {
      if (Number(nodeKey) > highestKey) {
        highestKey = Number(nodeKey);
      }
    });
    ++highestKey;
    return highestKey.toString();
  }

  private addSourceToModel(selectedSource: any): string {
    const selectedMeasureSources = this.model.getFactSources();
    const key = this.getNextSubnode(selectedMeasureSources);

    const newFactSourceEntry: IAnalyticModelFactSource = {
      key,
      text: selectedSource.businessName,
      dataEntity: {
        key: selectedSource.qualifiedName,
      },
    };
    this.uiModel.setProperty("/uiFactSourceModel", newFactSourceEntry);
    return key;
  }

  public clearSelectedEntityUiProperties() {
    this.uiModel.setProperty("/diagramSelectedSource", undefined);
    this.uiModel.setProperty("/attributes", []);
    this.uiModel.setProperty("/measures", []);
    this.uiModel.setProperty("/associations", []);
    this.uiModel.setProperty("/parameters", []);
    this.uiModel.setProperty("/selectedMeasures", []);
    this.uiModel.setProperty("/selectedParameters", []);
    this.uiModel.setProperty("/selectedDimensions", []);
    this.uiModel.setProperty("/selectedDataAccessControls", []);
    this.uiModel.setProperty("/selectedFilters", []);
    this.clearSelectedCrossCalculations();
    this.uiModel.setProperty("/activeParameters", []);
    this.uiModel.setProperty("/inactiveParameters", []);
    DataEntityDetailsService.getInstance().clearCache();
    CdsFormulaToExprService.getInstance().clearCacheAndFormulas();
    if (this.featureflags.DWCO_MODELING_ANALYTIC_MODEL_STACKING_LIFECYCLE) {
      DependencyServiceAnalyticModel.getInstance().clearCache();
    }
  }

  public async updateDataPreviewPage() {
    this.resetDataPreview();

    // Data Preview tab: Display preview component if selection complete
    const aggregatedValidations = this.workbench.getToolbarModel().getProperty("/aggregatedValidations");
    if (aggregatedValidations?.status === ValidationStatus.STATUS_ERROR) {
      this.view.setBusy(true);
      this.setNoDataMessage("inconsistentAnalyticModelPreview");
      this.uiModel.setProperty("/preview/noDataBox", true);
      this.view.setBusy(false);
    } else {
      try {
        await this.startDataPreview();
      } catch (error) {
        this.resetDataPreview();
      } finally {
        this.view.setBusy(false);
      }
    }
  }

  public setNoDataMessage(message, parameters?) {
    // prepares no data message for Source Data tab or Data Preview tab
    const noDataBox = this.view.byId("previewNoDataBox") as sap.m.VBox;
    noDataBox.destroyItems();
    const noDataMessage = this.getText(message, parameters);
    const noDataPanelId = "previewPanel";
    const innerNoDataBox = new sap.m.VBox({
      items: [
        new sap.m.Image({
          src: require("../../reuse/images/empty_folder_illu.svg"),
        }),
        new sap.m.Label({
          textAlign: sap.ui.core.TextAlign.Center,
          text: noDataMessage,
        }),
      ],
      alignItems: sap.m.FlexAlignItems.Center,
    });
    const noDataFlexBox = new sap.m.FlexBox({
      alignItems: sap.m.FlexAlignItems.Center,
      justifyContent: sap.m.FlexJustifyContent.Center,
    });
    noDataFlexBox.addItem(innerNoDataBox);
    const noDataPanel = new sap.m.Panel(noDataPanelId);
    noDataPanel.addStyleClass("noDataText");
    noDataPanel.addContent(noDataFlexBox);
    noDataBox.addItem(noDataPanel);
  }

  public async startDataPreview() {
    const errorProperty = {
      hasError: false,
      errorDetails: null,
      errorMessage: "",
    };
    this.uiModel.setProperty("/preview/errorProperty", errorProperty);
    this.uiModel.setProperty("/preview/notDeployedObjects", []);

    const isHanaDown = getIsHanaDown();
    if (isHanaDown) {
      this.setNoDataMessage("hanaDown");
      this.uiModel.setProperty("/preview/noDataBox", true);
      return;
    }

    const isSpaceLocked = this.workbench.workbenchModel.getProperty("/spaceAccessInfo")?.status === SpaceStatus.Locked;
    const hasWritePrivileges = Util.Component.hasWritePrivileges();
    if (this.uiModel.getProperty("/isNew")) {
      // new analytic model: Preliminary deployment if has write privileges then display preview
      if (!hasWritePrivileges) {
        this.view.setBusy(true);
        this.setNoDataMessage("txtNoPrivilegesPreviewData");
        this.uiModel.setProperty("/preview/noDataBox", true);
        this.view.setBusy(false);
      } else if (isSpaceLocked) {
        this.view.setBusy(true);
        this.setNoDataMessage("cannotPreviewNotDeployed", this.model.getBusinessName());
        this.uiModel.setProperty("/preview/noDataBox", true);
        this.view.setBusy(false);
      } else {
        return this.predeployDataPreview();
      }
    } else {
      this.view.setBusy(true);
      await this.updateObjectStatusWithRepoInfo(false); // this sets /objectStatus
      if (this.uiModel.getProperty("/objectStatus") === ObjectStatus.deployed && !Util.Component.isModelDirty()) {
        // deployed and not changed analytic model: Display preview directly

        await this.displayDataPreview(this.model.getTechnicalName());
        this.view.setBusy(false);
      } else {
        // not deployed or changed analytic model: Preliminary deployment if has write privileges then display preview
        if (!hasWritePrivileges) {
          this.view.setBusy(true);
          this.setNoDataMessage("txtNoPrivilegesPreviewData");
          this.uiModel.setProperty("/preview/noDataBox", true);
          this.view.setBusy(false);
        } else if (isSpaceLocked) {
          this.view.setBusy(true);
          this.setNoDataMessage("cannotPreviewNotDeployed", this.model.getBusinessName());
          this.uiModel.setProperty("/preview/noDataBox", true);
          this.view.setBusy(false);
        } else {
          return this.predeployDataPreview();
        }
      }
    }
  }

  public async checkPreconditions(): Promise<IPreconditionResult[]> {
    // preview UI defaults
    // this.uiModel.setProperty("/preview/previewFlexBox", true);
    this.uiModel.setProperty("/preview/preconditionNotSatisfied", false);

    const oRequestOptions: JQueryAjaxSettings = {
      url: `querybuilder/${super.getSpaceName()}/preconditionpreviewdeployment`,
      type: HttpMethod.POST,
      dataType: DataType.TEXT,
      data: JSON.stringify(this.model.getData()),
      contentType: ContentType.APPLICATION_JSON,
    };
    const abortSignal = this.abortControllerPreview.signal;
    try {
      this.view.setBusy(true);
      const response = await ServiceCall.request<string>(oRequestOptions, true, abortSignal);
      // Parse data to the IPreconditionResult array and treat objectStatus type
      if (response.data) {
        return JSON.parse(response.data, (key, value) =>
          key === "objectStatus" ? (parseInt(value, 10) as ObjectStatus) : value
        );
      }
      return [];
    } catch (error) {
      const xhr = error[0];
      if (xhr.statusText === "abort") {
        this.uiModel.setProperty("/preview/previewFlexBox", false);
        throw error;
      } else {
        await MessageHandler.exception({ message: this.getText("preconditionsLoadingError"), exception: error });
      }
    } finally {
      this.view.setBusy(false);
    }
  }

  onPressNavigateToSource(event: sap.ui.base.Event) {
    const context = event.getSource().getBindingContext("ui");
    const qualifiedName = context.getProperty("qualifiedName");
    const spaceName = context.getProperty("spaceName");

    void CubeBuilder.navigateToSourceObject(spaceName, qualifiedName);
  }

  public async predeployDataPreview() {
    try {
      const preconditionsResult = await this.checkPreconditions();
      if (preconditionsResult && preconditionsResult.length > 0) {
        this.uiModel.setProperty("/preview/notDeployedObjects", preconditionsResult);
        this.uiModel.setProperty("/preview/preconditionNotSatisfied", true);
        return;
      }

      let defaultKey = false;
      // special case: No identifier/key, as the selection is not saved: Use default key
      if (!this.model.getTechnicalName()) {
        // not using stack because we don't want this to be undoable
        this.model.setTechnicalName("TEMPORARY_DEPLOYMENT_KEY");
        defaultKey = true;
      }

      const oRequestOptions: JQueryAjaxSettings = {
        url: `querybuilder/${super.getSpaceName()}/previewDeployment`,
        type: HttpMethod.POST,
        dataType: DataType.TEXT,
        data: JSON.stringify(this.model.getData()),
        contentType: ContentType.APPLICATION_JSON,
      };
      if (defaultKey) {
        // not using stack because we don't want this to be undoable
        this.model.setTechnicalName("");
      }
      const abortSignal = this.abortControllerPreview.signal;
      this.view.setBusy(true);
      const response = await ServiceCall.request(oRequestOptions, true, abortSignal);
      const predeployKey = JSON.parse(response.data as string).entityDetails.uniqueHashName;
      await this.displayDataPreview(predeployKey);
    } catch (error) {
      const xhr = error[0];
      if (xhr.statusText === "abort") {
        this.uiModel.setProperty("/preview/previewFlexBox", false);
        throw error;
      } else {
        const message = (error && error[0]?.responseText) || "unknown Error reason";
        const dataPreviewErrorProperty = {
          hasError: true,
          errorDetails: error,
          errorMessage:
            message === "unknown Error reason"
              ? this.getText("dataPreviewUnknownErrorReason")
              : JSON.parse(message)?.details?.message || this.getText("previewDeploymentErrorMoreDetails"),
        };
        this.uiModel.setProperty("/preview/errorProperty", dataPreviewErrorProperty);

        this.uiModel.setProperty("/preview/preconditionNotSatisfied", true);
        this.uiModel.setProperty("/preview/previewFlexBox", false);
      }
    } finally {
      this.view.setBusy(false);
    }
  }

  /**
   * Handles showing error details dialog box for failed execution
   *
   * @memberof CubeBuilderEditorClass
   */
  public onViewDetailPress() {
    const oError = this.uiModel.getProperty("/preview/errorProperty/errorDetails");
    const errorMessage =
      oError && oError[0]?.responseText ? JSON.parse(oError[0]?.responseText)?.entityDetails?.errorMessage : "";
    const params: IMessageHandlerExceptionParams = {
      exception: oError,
      message: this.getText("previewDeploymentError"),
    };
    const errorInfo = MessageHandler.guessExceptionDetails(params.exception);
    const details = this.getText("technicalErrorDetails", [
      errorInfo.correlationId,
      errorMessage || errorInfo.responseJSON.details.message,
      errorInfo.responseJSON.code,
      errorInfo.httpStatus,
    ]);
    MessageHandler.uiError(details);
  }

  private async displayDataPreview(queryKey: string) {
    const getSystemName = async () => {
      const isMultiStructureEnabled = this.featureflags.DWCO_MODELING_AM_MULTI_STRUCTURE;
      const isCanary = await IsCanaryService.getInstance().isCanary();

      if (isCanary && isMultiStructureEnabled) {
        return "DWCLocal";
      } else {
        return this.featureflags.DWC_LCS_INA_ADOPTION ? "DWCLCS" : "DWCLocal";
      }
    };
    const systemName = await getSystemName();
    const previewFlexBox = this.view.byId("previewFlexBox") as sap.m.FlexBox;
    previewFlexBox.setBusy(true);
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    this.logPreviewCall();
    this.uiModel.setProperty("/preview/previewFlexBox", true);

    /**
     * ugly overwrite of default function due to wrong usage of UShellContainer inside of standard constructor
     */
    // eslint-disable-next-line no-underscore-dangle
    (sap as any).sac.df.model.MultiDimModel.prototype._getUserProfile = async () => {
      let userLanguage = User.getInstance().getPreference("DATA_ACCESS_LANGUAGE");
      if (!userLanguage || userLanguage === "") {
        userLanguage = "EN";
      }
      let dateFormat = User.getInstance().getPreference("DATE_FORMAT");
      if (!dateFormat || dateFormat === "") {
        dateFormat = "MM/DD/YYYY";
      } else {
        dateFormat = JSON.parse(dateFormat)?.dateFormat?.pattern || "MM/DD/YYYY";
      }
      let timeFormat = User.getInstance().getPreference("TIME_FORMAT");
      if (!timeFormat || timeFormat === "") {
        timeFormat = "24hrs";
      }
      const decimalFormat = User.getInstance().getPreference("DECIMAL_FORMAT");
      const { groupingSeparator, decimalSeparator } = this.parseDecimalAndScaleFormat(decimalFormat);
      return JSON.stringify({
        defaults: {
          dateFormat: dateFormat,
          groupingSeparator: groupingSeparator,
          decimalSeparator: decimalSeparator,
          timeFormat: timeFormat,
        },
        header: {
          language: userLanguage,
        },
      });
    };
    const schemaName: string = super.getSpaceName();
    if (previewFlexBox.getModel("om") === undefined || previewFlexBox.getModel("om") === null) {
      const MultiDimConstructor: MultiDimModelConstructor = (sap as any).sac.df.model.MultiDimModel;
      const analysisModel = new MultiDimConstructor({
        DataProviders: {
          dataProvider: {
            dataSourceName: queryKey,
            systemName: systemName,
            packageName: "",
            schemaName: schemaName,
            dataSourceType: "InAModel",
          },
        },
        systemLandscape: {
          Systems: [
            {
              systemName: "DWCLocal",
              systemType: "DWC",
              protocol: window.location.protocol === "http:" ? "HTTP" : "HTTPS",
              host: window.location.hostname,
              port: window.location.port,
              path: "dwaas-core",
              language: User.getInstance().getPreference("DATA_ACCESS_LANGUAGE"),
              authentication: "NONE",
            },
            {
              systemName: "DWCLCS",
              systemType: "DWC",
              protocol: window.location.protocol === "http:" ? "HTTP" : "HTTPS",
              host: window.location.hostname,
              port: window.location.port,
              path: "lcs/dwc",
              language: User.getInstance().getPreference("DATA_ACCESS_LANGUAGE"),
              authentication: "NONE",
            },
          ],
        },
        masterSystem: systemName,
      });
      previewFlexBox.setModel(analysisModel, "om");
    } else {
      // invalidate metadata and recreate data provider
      const model = previewFlexBox.getModel("om") as MultiDimModel;
      const oDataProvider = model.getDataProvider("dataProvider");
      if (oDataProvider) {
        oDataProvider.invalidateMetaData();
        model.addDataProvider("dataProvider", queryKey, systemName, "", schemaName, "InAModel");
      }
    }

    // set logging for analysis component (can't be done in view)
    const analysisComponent = this.view.byId("analysisComponent") as sap.sac.df.FlexAnalysis;
    analysisComponent["logActive"] = true;
    previewFlexBox.setBusy(false);
  }

  private parseDecimalAndScaleFormat(decimalFormat: string) {
    // default value in Language & Region settings is 1,234.56
    const defaultReturn = {
      groupingSeparator: ",",
      decimalSeparator: ".",
    };
    if (!decimalFormat) {
      return defaultReturn;
    }
    try {
      const decimalFormatObject = JSON.parse(decimalFormat);
      const parsedDecimalFormat = decimalFormatObject.decimalFormat;
      const decimalSeparator = parsedDecimalFormat.decimalSeparator.symbol;
      const groupingSeparator = parsedDecimalFormat.groupingSeparator.symbol;
      if (!decimalSeparator || !groupingSeparator) {
        return defaultReturn;
      }
      return {
        groupingSeparator,
        decimalSeparator,
      };
    } catch (e) {
      return defaultReturn;
    }
  }

  /**
   * Register an audit log event for opening the preview.
   * This isn't done in preconditionpreviewdeployment because we don't call it if the deployed version is up to date.
   */
  private async logPreviewCall() {
    const auditInformation = {
      entity: this.model.getTechnicalName(),
    };
    try {
      const oRequestOptions: JQueryAjaxSettings = {
        url: `querybuilder/${super.getSpaceName()}/logpreviewcall`,
        type: HttpMethod.POST,
        dataType: DataType.JSON,
        data: JSON.stringify(auditInformation),
        contentType: ContentType.APPLICATION_JSON,
      };
      await ServiceCall.request(oRequestOptions);
    } catch (e) {
      // don't show the user an error for failed logging attempt
    }
  }

  /*
   *  Method triggered when user select a node on the diagram
   *  Updates the property panel to display the information of selected node or the model
   *  Loads metadata from the node to be displayed on the property panel
   */

  public async onSymbolSelectCallBack(event: any) {
    if (this.uiModel.getProperty("/skipSymbolCallback")) {
      return;
    }
    let selectedObject;
    let dataEntity;
    let gObject;

    if (event) {
      gObject = event.mParameters.object;

      // Case the selected object is not a node in the graph (i.e.: selecting an arrow)
      // In the future we could open the association details for the selected arrow
      // For now we ignore that selection
      if (!gObject) {
        Util.Component.router.navToModelPanel();
        return;
      }

      selectedObject = gObject.originalObject;
      dataEntity = selectedObject?.dataEntity;
      if (!selectedObject || !dataEntity) {
        // to enable unselecting when clicking on fact source surrounding area
        this.uiModel.setProperty("/diagramSelectedSource", {});

        Util.Component.router.navToModelPanel(true);
        return;
      }
    } else {
      // TODO workaround to simulate a selection - needed for now for managing undo/redo
      // we might not need it when ILAnalzer offers a way to manipulate the graph and select a node
      selectedObject = this.uiModel.getProperty("/diagramSelectedSource");
      if (!selectedObject) {
        Util.Component.updatePropertyPanel([Util.getGalileiModel()]);
        await Util.Component.updateDataPreviewPanel(undefined);
        return;
      }
      dataEntity = selectedObject?.dataEntity;
      // look for gObject from the current Model - if available go ahead -
      // if not updatePropertyPanel with the model and return.
      for (let index = 0; index < Util.getGalileiModel().allnodes.length; index++) {
        const oNode = Util.getGalileiModel().allnodes.get(index);
        if (isEqual(oNode.originalObject, selectedObject)) {
          gObject = oNode;
          break;
        }
      }
      if (!gObject) {
        Util.Component.updatePropertyPanel([Util.getGalileiModel()]);
        await Util.Component.updateDataPreviewPanel(undefined);
        return;
      }
    }

    this.view.setBusy(true);

    if (!this.model.getDimensionHandlingCapability()) {
      this.uiModel.setProperty("/diagramSelectedSource", selectedObject);
    }

    await this.setupUIModelForNodeProperties(dataEntity, selectedObject);

    this.view.setBusy(false);
    // display the workbench property panel
    const sourceType: AnalyticModelSourceType = selectedObject.type;
    const sourceKey =
      sourceType === AnalyticModelSourceType.Fact ? this.model.getFirstFactSourceKey() : selectedObject.id;
    Util.Component.router.navToSourcePanel(sourceType, sourceKey);
  }

  private async setupUIModelForNodeProperties(dataEntity: { key: string }, selectedObject: TypedSource) {
    // load Details of selected Object
    const dataEntityDetails = await DataEntityDetailsService.getInstance().loadDataEntityDetails(
      [dataEntity],
      super.getSpaceName()
    );

    // add keyOnModel for attribute,measures and parameters - keyOnModel is either the key of the property itself.
    // or if the key already exists, it will be concatenated with the id of the dataEntity in the sourceModel
    const aElements = dataEntityDetails[dataEntity.key] ?? ({} as IDataEntity);
    aElements.attributes =
      aElements.attributes?.map((attribute) => ({
        ...attribute,
        keyOnModel: createAttributeKey(this.model, attribute, selectedObject, this.featureflags),
      })) ?? [];
    aElements.measures =
      aElements.measures?.map((measure) => ({
        ...measure,
        keyOnModel: this.createMeasureKey(measure, selectedObject),
      })) ?? [];
    aElements.parameters =
      aElements.parameters?.map((parameter) => ({
        ...parameter,
        keyOnModel: this.createParameterKey(parameter, selectedObject),
      })) ?? [];
    aElements.associations = aElements.associations ?? [];

    aElements.crossCalculations =
      aElements.crossCalculations?.map((crossCalculation) => ({
        ...crossCalculation,
        keyOnModel: this.createCrossCalculationKey(crossCalculation, selectedObject),
      })) ?? [];

    // enhance with ui needed properties and put into the uiModel
    await this.setUiAssociationProperties(aElements, selectedObject);
    this.setUiAttributeProperties(aElements.attributes as NodeAttributes, selectedObject);
    this.setUiMeasureProperties(aElements.measures as NodeMeasures, selectedObject);
    this.setNodeCrossCalculationsList(aElements.crossCalculations as NodeCrossCalculations, selectedObject);
    await this.setUiParameterProperties(aElements.parameters, selectedObject);
    this.updateSearchProperties();
  }

  public updateSearchProperties(searchString: string = "") {
    if (!this.getNodePropertiesView()) {
      return;
    }

    if (!searchString) {
      searchString = this.getNodePropertiesView().byId("availablePropertiesSearchInput").getValue();
    }

    const filter = this.getSourcePropertyFilter(searchString);
    // TODO: should be typed as sap.ui.model.odata.v4.ODataContextBinding but there is no sap.ui.model.odata yet
    const measures = this.getNodePropertiesView().byId("availableMeasuresList").getBinding("items");
    measures.filter(filter);
    // update the filtered measures to get the correct state for the multiselect checkbox
    const filteredMeasures = measures
      .getAllCurrentContexts()
      .map((measure) => this.uiModel.getProperty(measure["sPath"]));
    this.uiModel.setProperty("/filteredMeasures", []);
    this.uiModel.setProperty("/filteredMeasures", filteredMeasures);

    // TODO: should be typed as sap.ui.model.odata.v4.ODataContextBinding but there is no sap.ui.model.odata yet
    const attributes = this.getNodePropertiesView().byId("availableAttributesList").getBinding("items");
    attributes.filter(filter);
    // update the filtered attributes to get the correct state for the multiselect checkbox
    const filteredAttributes = attributes
      .getAllCurrentContexts()
      .map((attribute) => this.uiModel.getProperty(attribute["sPath"]));
    this.uiModel.setProperty("/filteredAttributes", []);
    this.uiModel.setProperty("/filteredAttributes", filteredAttributes);

    // TODO: should be typed as sap.ui.model.odata.v4.ODataContextBinding but there is no sap.ui.model.odata yet
    const associations = this.getNodePropertiesView().byId("availableAssociationsList").getBinding("items");
    associations.filter(filter);
    // update the filtered associations to get the correct state for the multiselect checkbox
    const filteredAssociations = associations
      .getAllCurrentContexts()
      .map((association) => this.uiModel.getProperty(association["sPath"]));
    this.uiModel.setProperty("/filteredAssociations", []);
    this.uiModel.setProperty("/filteredAssociations", filteredAssociations);

    if (this.model.getDimensionHandlingCapability()) {
      const nonForeignKeyAssociations = this.getNodePropertiesView()
        .byId("availableAssociationsListNonForeignKey")
        .getBinding("items");
      nonForeignKeyAssociations.filter(filter);
      // update the filtered associations to get the correct state for the multiselect checkbox
      const filteredNonForeignKeyAssociations = nonForeignKeyAssociations
        .getAllCurrentContexts()
        .map((association) => this.uiModel.getProperty(association["sPath"]));
      this.uiModel.setProperty("/filteredNonForeignKeyAssociations", []);
      this.uiModel.setProperty("/filteredNonForeignKeyAssociations", filteredNonForeignKeyAssociations);
    }

    // TODO: should be typed as sap.ui.model.odata.v4.ODataContextBinding but there is no sap.ui.model.odata yet
    const crossCalculations = this.getNodePropertiesView().byId("availableCrossCalculationsList").getBinding("items");
    crossCalculations.filter(filter);
    // update the filtered cross calculations to get the correct state for the multiselect checkbox
    const filteredCrossCalculations = crossCalculations
      .getAllCurrentContexts()
      .map((crossCalculation) => this.uiModel.getProperty(crossCalculation["sPath"]));
    this.uiModel.setProperty("/filteredCrossCalculations", []);
    this.uiModel.setProperty("/filteredCrossCalculations", filteredCrossCalculations);
  }

  /*
   *  Method triggered when user select the whitespace on the diagram
   *  Updates the property panel to display the information of the analytic model
   */
  public async onBackgroundTapCallBack(event: any) {
    if (this.uiModel.getProperty("/skipSymbolCallback")) {
      return;
    }
    await this.resetPanel(false);
  }

  /*
   *  Method to prepare the validation and confirmation dialog for fact source replace
   *  when a fact source dropped on the existing fact source in the diagram
   */
  async validateFactSourceReplacement(replacementFactSourceName: string) {
    if (!this.replaceSourceDialog) {
      const fragmentName = require("../view/ReplaceSourceDialog.fragment.xml");
      this.replaceSourceDialog = sap.ui.xmlfragment("replaceSource", fragmentName, this) as sap.m.Dialog;
    }
    let missingMeasureString = "";
    let missingMeasureCount = 0;
    let missingAttributeString = "";
    let missingAttributeCount = 0;
    let missingAssociationString = "";
    let missingAssociationCount = 0;
    let missingTargetString = "";
    let missingTargetCount = 0;
    let missingVariableString = "";
    let missingVariableCount = 0;
    const sources: IAnalyticModelRepositoryEntity[] = [];
    const factSource = {
      key: replacementFactSourceName,
    };
    sources.push(factSource);
    let newFactSource = {} as IDataEntityDetailsResponse;
    try {
      this.setWholeComponentBusy(true);
      newFactSource = await DataEntityDetailsService.getInstance().loadDataEntityDetails(sources, this.getSpaceName());
      const newSourceMeasures = Object.values(newFactSource[replacementFactSourceName].measures);
      Object.values(this.model.getMeasures() ?? {}).forEach((measureValue) => {
        const modelMeasure = measureValue;
        if (modelMeasure.measureType === AnalyticModelMeasureType.FactSourceMeasure) {
          if (
            !newSourceMeasures.some(
              (newSourceMeasure) => (newSourceMeasure as IAnalyticModelSourceMeasure).key === modelMeasure.key
            )
          ) {
            missingMeasureString =
              missingMeasureString === "" ? modelMeasure.key : missingMeasureString + "; " + modelMeasure.key;
            missingMeasureCount++;
          }
        }
      });
      const newSourceAttributes = Object.values(
        newFactSource[replacementFactSourceName].attributes
      ) as UiDataEntityAttribute[];
      const oldSourceAttributes = Object.values(this.model.getAttributes() ?? {}).filter(
        (value) => value.attributeType === AnalyticModelAttributeType.FactSourceAttribute
      ) as IAnalyticModelFactSourceAttribute[];
      const oldFactSourceName = this.uiModel.getProperty("/diagramDropTarget")
        ? this.uiModel.getProperty("/diagramDropTarget")
        : this.uiModel.getProperty("/diagramSelectedSource").id;
      oldSourceAttributes.forEach((attributeValue) => {
        const oldAttributeKey = attributeValue.attributeMapping[oldFactSourceName].key;
        if (!newSourceAttributes.some((newSourceAttribute) => newSourceAttribute.key === oldAttributeKey)) {
          missingAttributeString =
            missingAttributeString === "" ? oldAttributeKey : missingAttributeString + "; " + oldAttributeKey;
          missingAttributeCount++;
        }
      });
      const newSourceAssociations = Object.values(newFactSource[replacementFactSourceName].associations);
      const mappedOldFactSourceName = this.model.getFactSource(oldFactSourceName).dataEntity.key;
      const oldSourceAssociations =
        this.uiModel.getData().allSourceDataEntityDetails[mappedOldFactSourceName].associations;
      Object.values(oldSourceAssociations ?? {}).forEach((associationValue) => {
        const modelAssociation = associationValue as IDataEntityAssociation;
        const foreignKey = newSourceAttributes.find((value) => value.key === modelAssociation.attributeForAssociation);
        // association is missing if not in new fact source or not assigned to foreign key
        if (
          !newSourceAssociations.some((newSourceAssociation) => newSourceAssociation.key === modelAssociation.key) ||
          foreignKey?.foreignKeyAssociation !== modelAssociation.key
        ) {
          missingAssociationString =
            missingAssociationString === ""
              ? modelAssociation.key
              : missingAssociationString + ", " + modelAssociation.key;
          missingAssociationCount++;
        }
        if (
          !newSourceAssociations.some(
            (newSourceAssociation) =>
              newSourceAssociation.attributeForAssociation === modelAssociation.attributeForAssociation &&
              newSourceAssociation.targetEntity.key === modelAssociation.targetEntity.key
          )
        ) {
          missingTargetString =
            missingTargetString === ""
              ? modelAssociation.targetEntity.key
              : missingTargetString + ", " + modelAssociation.targetEntity.key;
          missingTargetCount++;
        }
      });
      const newSourceParameters = Object.values(newFactSource[replacementFactSourceName].parameters);
      const parameterMappings = this.model.getFactSource(oldFactSourceName).parameterMappings ?? {};
      const parameterMappingsKeys = Object.keys(parameterMappings);
      parameterMappingsKeys.forEach((variableKey) => {
        if (!newSourceParameters.some((newSourceParameter) => newSourceParameter.key === variableKey)) {
          missingVariableString =
            missingVariableString === "" ? variableKey : missingVariableString + "; " + variableKey;
          missingVariableCount++;
        }
      });
      const verificationTable = [];
      const baseMeasures = Object.values(this.model.getMeasures()).filter(
        (measure) => measure.measureType === AnalyticModelMeasureType.FactSourceMeasure
      );
      const measureVerification = {
        objectType: this.getText("measuresReplace"),
        missingObjectsCount: missingMeasureCount,
        totalObjectsCount: baseMeasures.length,
        missingObjectsList: missingMeasureString === "" ? "-" : missingMeasureString + ";",
      };
      verificationTable.push(measureVerification);
      const attributeVerification = {
        objectType: this.getText("attributesReplace"),
        missingObjectsCount: missingAttributeCount,
        totalObjectsCount: oldSourceAttributes.length,
        missingObjectsList: missingAttributeString === "" ? "-" : missingAttributeString + ";",
      };
      verificationTable.push(attributeVerification);
      const associationVerification = {
        objectType: this.getText("associationsReplace"),
        missingObjectsCount: missingAssociationCount,
        totalObjectsCount: oldSourceAssociations.length,
        missingObjectsList: missingAssociationString === "" ? "-" : missingAssociationString + ";",
      };
      verificationTable.push(associationVerification);
      const targetVerification = {
        objectType: this.getText("associatedObjectsReplace"),
        missingObjectsCount: missingTargetCount,
        totalObjectsCount: oldSourceAssociations.length,
        missingObjectsList: missingTargetString === "" ? "-" : missingTargetString + ";",
      };
      verificationTable.push(targetVerification);
      const variableVerification = {
        objectType: this.getText("inputParametersReplace"),
        missingObjectsCount: missingVariableCount,
        totalObjectsCount: parameterMappingsKeys.length,
        missingObjectsList: missingVariableString === "" ? "-" : missingVariableString + ";",
      };
      verificationTable.push(variableVerification);
      this.uiModel.setProperty("/replacementVerification", verificationTable);
      this.getView().addDependent(this.replaceSourceDialog);
      this.uiModel.setProperty("/replacementFactSource", newFactSource[replacementFactSourceName]);
      this.uiModel.setProperty(
        "/replacementFactSource/oldFactSourceBusinessName",
        this.model.getFactSource(oldFactSourceName).text
      );
      this.uiModel.setProperty("/replacementFactSource/oldFactSourceTechnicalName", oldFactSourceName);
      this.replaceSourceDialog.open();
    } catch (e) {
      MessageHandler.uiError(this.getText("dataEntityDetailsError"));
    } finally {
      this.setWholeComponentBusy(false);
    }
  }

  // /*
  //  *  Method triggered when the user clicks the Replace button in the summary confirmation dialog
  //  *  Starts the replacement of the fact source by the selected fact source in the analytic model
  //  */
  public async onReplaceFactSource() {
    const selectedSource = this.uiModel.getProperty("/diagramDropTarget")
      ? this.uiModel.getProperty("/diagramDropTarget")
      : this.uiModel.getProperty("/diagramSelectedSource/id");
    this.uiModel.setProperty("/diagramDropTarget", undefined);
    const replacementFactSourceName = this.uiModel.getProperty("/replacementFactSource/technicalName");
    const command = new ReplaceFactSource(
      selectedSource,
      replacementFactSourceName,
      this.uiModel.getProperty("/replacementFactSource/businessName")
    );

    this.stack.execute(command);
    await this.getSourceEntityDetails(this.model.getSourceModel());
    this.setIsStackedModel();
    await this.resetPanel();
    await Util.Component.modelChangeHandler();
    this.replaceSourceDialog.close();

    recordUsageTrackingEvent({
      action: USAGE_ACTIONS.REPLACE_FACT_SOURCE,
      feature: getCurrentFeatureForUsageTracking(),
      eventtype: EventType.CLICK,
    });
  }

  public onReplaceDialogClose() {
    this.uiModel.setProperty("/diagramDropTarget", undefined);
    this.replaceSourceDialog.close();
  }

  /*
   *  Method triggered when user repairs a dimension source node on the diagram
   *  Displays a dialog to explain and confirm the fix of the dimension source
   */
  public async onSymbolRepairCallBack() {
    this.setWholeComponentBusy(true);
    // dialog to propose repair: Replace association or target entity
    if (!this.repairDimensionDialog) {
      const fragmentName = require("../view/RepairDimensionDialog.fragment.xml");
      this.repairDimensionDialog = sap.ui.xmlfragment("repairDimension", fragmentName, this) as sap.m.Dialog;
    }
    this.getView().addDependent(this.repairDimensionDialog);
    this.repairDimensionDialog.open();
    this.setWholeComponentBusy(false);
  }

  /*
   *  Method triggered when the user clicks the Repair button in the confirmation dialog
   *  Starts the replacement of the dimension source and deletion of missing attributes
   */
  public async onRepairDimensionSource() {
    this.setWholeComponentBusy(true);
    this.repairDimensionDialog.close();

    const dimensionKey = this.uiModel.getProperty("/replacedDimension/id");
    const replacementAssociation = this.uiModel.getProperty("/replacementAssociation");
    const missingAttributes = [];
    // update source entities and get model attributes missing in dimension source
    const sourceDimensionDetails = await DataEntityDetailsService.getInstance().loadDataEntityDetails(
      [replacementAssociation.targetEntity],
      super.getSpaceName()
    );
    if (sourceDimensionDetails[replacementAssociation.targetEntity.key].attributes) {
      const sourceDimensionAttributes = Object.values(
        sourceDimensionDetails[replacementAssociation.targetEntity.key].attributes
      );
      // collect the keys of attributes in the model dimension missing in the dimension entity
      const modelDimensionAttributes2 = Object.entries(this.model.getAttributes()).filter(
        ([key, attribute]) =>
          attribute.attributeType === AnalyticModelAttributeType.DimensionSourceAttribute &&
          attribute.sourceKey === dimensionKey
      ) as Array<[string, IAnalyticModelDimensionSourceAttribute]>;
      modelDimensionAttributes2.forEach((modelDimensionAttribute2) => {
        if (
          !sourceDimensionAttributes.find(
            (sourceDimensionAttribute) => sourceDimensionAttribute.key === modelDimensionAttribute2[1].key
          )
        ) {
          missingAttributes.push(modelDimensionAttribute2[0]);
        }
      });
    }
    const nodeDetails = this.uiModel.getProperty("/nodeDetails");
    const repairDimensionCommand = new RepairDimensionSource(nodeDetails, replacementAssociation, missingAttributes);
    this.stack.execute(repairDimensionCommand);
    this.uiModel.setProperty("/diagramSelectedSource/dataEntity/key", replacementAssociation.targetEntity.key);
    this.uiModel.setProperty(
      "/diagramSelectedSource/associationContexts/0/associationSteps/0",
      replacementAssociation.key
    );
    await this.getSourceEntityDetails(this.model.getSourceModel());
    await this.resetPanel(true);
    await Util.Component.modelChangeHandler();
    this.setWholeComponentBusy(false);
  }

  public onRepairDialogClose() {
    this.repairDimensionDialog.close();
  }

  /*
   *  Method triggered when user deletes a node on the diagram
   *  Updates the property panel to display the information of the analytic model
   */
  public async onSymbolDeleteCallBack() {
    const selectedSource: DiagramSource = this.uiModel.getProperty("/diagramSelectedSource");

    if (selectedSource.type === AnalyticModelSourceType.Fact) {
      await this.removeSourceFromQueryModel(selectedSource.type);
    } else if (selectedSource.type === AnalyticModelSourceType.Dimension) {
      const modelDimensionSources = this.model.getDimensionSources();
      const dimensionSourceKey = Object.keys(modelDimensionSources).find(
        (index) =>
          modelDimensionSources[index].dataEntity.key === selectedSource.dataEntity.key &&
          modelDimensionSources[index].associationContexts.some(
            (ctx) =>
              ctx.associationSteps.some(
                (step) => step === selectedSource.associationContexts[0].associationSteps.slice().pop()
              ) &&
              ctx.sourceKey === selectedSource.associationContexts[0].sourceKey &&
              ctx.sourceType === selectedSource.associationContexts[0].sourceType
          )
      );
      if (dimensionSourceKey) {
        await this.removeSourceFromQueryModel(selectedSource.type, dimensionSourceKey);
        recordUsageTrackingEvent({
          action: USAGE_ACTIONS.DELETE_ASSOCIATED_DIMENSION,
          feature: getCurrentFeatureForUsageTracking(),
          eventtype: EventType.CLICK,
          options: [
            {
              param: "target",
              value: selectedSource?.text,
            },
          ],
        });
      }
    }

    Util.Component.router.navToModelPanel(true);
  }

  private async setUiParameterProperties(parameters: IDataEntityParameter[], selectedObject: TypedSource) {
    let uiParameters: NodeParameters = [];
    const factSources = this.model.getFactSources();

    Object.entries(factSources).forEach(([factSourceKey, factSource]) => {
      const parameterMappings = factSource.parameterMappings || {};
      // For each fact source in the model, check if there are parameterMappings
      // to parameters that are missing in the underlying ADS
      const missingParametersFromADS = [];
      Object.keys(parameterMappings).forEach((parameterMappingKey) => {
        if (
          !parameters.length ||
          !parameters.some((sourceDetailsParameter) => sourceDetailsParameter.key === parameterMappingKey)
        ) {
          missingParametersFromADS.push(
            this.creatingMissingADSParameter(parameterMappingKey, factSource, factSourceKey)
          );
        }
      });
      if (parameters && parameters.length) {
        uiParameters = parameters.map((parameter) => {
          const param = this.createInputParameter(parameter) as NodeParameter;
          param.defaultSelectionType = parameter.selectionType; // Store the default selectionType for parameter Info Popup
          param.parameterMappingType = {};
          if (!!parameterMappings) {
            const mappingType = parameterMappings[parameter.key]?.mappingType;
            if (
              this.isStackedModel() &&
              this.featureflags.DWCO_MODELING_AM_SET_VALUE_STACKING_VARIABLE &&
              CubeBuilderObjects.ConstantMappingTypes.includes(mappingType)
            ) {
              // determine the selection type based on the mapping type and set the proper multipleSelections value
              param.selectionType = this.determineStackedVariableSelectionType(mappingType);
              param.multipleSelections = [
                AnalyticModelSourceParameterMappingType.ConstantValues,
                AnalyticModelSourceParameterMappingType.ConstantRanges,
              ].includes(mappingType);
              if (parameterMappings[parameter.key] && "constantValue" in parameterMappings[parameter.key]) {
                this.valueDefinitionUtils.setupDefaultValue(
                  param,
                  (parameterMappings[parameter.key] as CONSTANT_VALUE_TYPES).constantValue,
                  new sap.ui.model.json.JSONModel(param),
                  "",
                  true /** For UI parameters of type NodeParameter, the property is named 'value' instead of 'defaultValue'. Update 'value' in the UI model for these cases. */
                );
              }
            }
            if (!Object.keys(parameterMappings).includes(parameter.key)) {
              this.createMissingInputParameter(param, parameter);
            } else {
              param.parameterMappingType[parameter.key] = parameterMappings[parameter.key];
              param.parameterDescription = this.getParameterMappingDescription(
                parameterMappings[parameter.key],
                param.selectionType
              );
            }
          } else {
            this.createMissingInputParameter(param, parameter);
          }
          return param;
        });
      }
      // concatenate missing parameters from ads to the original list
      uiParameters = uiParameters.concat(missingParametersFromADS);
    });
    this.uiModel.setProperty("/parameters", uiParameters);
    if (this.isStackedModel()) {
      this.stackedVariable.setupNodeVariablesList();
    }

    if (this.featureflags.DWCO_MODELING_ANALYTIC_MODEL_STACKING_LIFECYCLE) {
      await Util.Component.modelChangeHandler([]);
    } else {
      await Util.Component.getAndUpdateModelValidations(this.model);
    }
    this.updateParameterErrorProperty(selectedObject);
  }

  private determineStackedVariableSelectionType(
    mappingType: AnalyticModelSourceParameterMappingType
  ): AnalyticModelVariableSelectionType {
    const mappingTypeToSelectionType = {
      [AnalyticModelSourceParameterMappingType.ConstantValue]: AnalyticModelVariableSelectionType.SINGLE,
      [AnalyticModelSourceParameterMappingType.ConstantValues]: AnalyticModelVariableSelectionType.MULTIPLE_SINGLE,
      [AnalyticModelSourceParameterMappingType.ConstantInterval]: AnalyticModelVariableSelectionType.INTERVAL,
      [AnalyticModelSourceParameterMappingType.ConstantRange]: AnalyticModelVariableSelectionType.RANGE,
      [AnalyticModelSourceParameterMappingType.ConstantRanges]: AnalyticModelVariableSelectionType.RANGE,
    };
    return mappingTypeToSelectionType[mappingType];
  }

  private createMissingInputParameter(param: any, parameter: IDataEntityParameter) {
    if (this.isStackedModel()) {
      param.parameterMappingType[parameter.key] = {
        mappingType: AnalyticModelSourceParameterMappingType.Inherited,
      };
      param.parameterDescription = this.getText("txtInherit");
      return; // exit early if inherited type is found
    }
    param.parameterMappingType[parameter.key] = {
      mappingType: AnalyticModelSourceParameterMappingType.ConstantValue,
    };
    param.parameterDescription = this.getText("txtValueWithPlaceHolder", [this.getText("txtnoValueAssigned")]);
  }

  /**
   * Creates a parameter for the ui model for a parameterMapping present in the model and that is
   * missing in the ADS, so the user can delete this entry in the UI to remove the mapping and fix the model
   */
  private creatingMissingADSParameter(parameterMappingKey, factSource, factSourceKey) {
    const missingParam = {
      key: parameterMappingKey,
      text: parameterMappingKey,
      parameterDescription: this.getText("parameterMappingMissingADSInputParameter"),
      isMissingInADS: true,
      modelPath: `/sourceModel/factSources/${factSourceKey}/parameterMappings/${parameterMappingKey}`,
      factSourceKey,
      parameterKey: parameterMappingKey,
    };
    return missingParam;
  }

  private createInputParameter(parameter: IDataEntityParameter) {
    const param = {
      ...parameter,
      isKey: true,
      displayText:
        this.user.getObjectNameDisplay() === ObjectNameDisplay.technicalName ? parameter.key : parameter.text,
      _validationMessageType: ValidationMessageType.OK,
    };
    return param;
  }

  private createParameterKey(property: IDataEntityProperty, selectedObject) {
    let newPropertyKey = property.key;
    const modelParameter = this.model.getVariable(newPropertyKey);
    if (modelParameter) {
      newPropertyKey = `${property.key}${selectedObject.id}`;
    }
    return newPropertyKey;
  }

  public parametersSelectedFormatter(parameter, diagramSelectedSource, modelParameters) {
    return this.getUiParameterMap(modelParameters).has(
      `${diagramSelectedSource.id}:${diagramSelectedSource.type}:${parameter.key}`
    );
  }

  private getUiParameterMap(
    modelParameters: IAnalyticModelSourceParameter_MANUALINPUT | IAnalyticModelSourceParameter_LOOKUP
  ) {
    // creates a map linking the ui attributes to the keys on /attributes on the model
    const uiParametersToModelMap = new Map<string, string>();
    if (!modelParameters) {
      return uiParametersToModelMap;
    }
    Object.entries(modelParameters).forEach(([modelKey, modelParameter]) => {
      // for now we just support single fact sources, so will use always the first source
      const sourceKey = this.model.getFirstFactSourceKey();
      const uiKey = `${sourceKey}:${AnalyticModelSourceType.Fact}:${modelParameter.sourceMapping[0].key}`;
      uiParametersToModelMap.set(uiKey, modelKey);
    });
    return uiParametersToModelMap;
  }

  private setupUiProperties() {
    this.setupUIMeasureList();
    this.setupVariableList();
    this.setupUIFilterList();
    this.setupAttributeGroupingBySource();
    this.setupDataAccessControlList();
    this.setupCrossCalculationUiModelProperties();
    this.setupUICollisionHandlingPriority();
  }

  private setupImpactedElements() {
    if (this.isDependentObjectsValidationEnabled()) {
      this.updateImpactedElements();
    }
  }

  public updateImpactedElements() {
    this.impactedElements = new ImpactedElements(this.model, this.stack.getInitialState());
  }

  public onItemSelect() {
    if (!this.isDependentObjectsValidationEnabled()) {
      return;
    }
    setTimeout(() => {
      const validationDetailMessagesDom = window.document.querySelectorAll(".validationMessageLineObjectLink");
      for (let i = 0; i < validationDetailMessagesDom?.length; i++) {
        const classList = validationDetailMessagesDom[i].classList;
        const href = classList[classList.length - 1];
        validationDetailMessagesDom[i].addEventListener(
          "pointerdown",
          (event) => {
            openObjectByName(undefined, undefined, true, href);
            event.preventDefault();
            event.stopPropagation();
          },
          { capture: true }
        );
      }
    }, 250);
  }

  // check if a dependent object may be affected by a model change
  // these checks should be outside of normal validation path as they should only run when a change is done.
  public async validateDependentObjects() {
    if (!this.isDependentObjectsValidationEnabled()) {
      return;
    }

    if (DependencyServiceAnalyticModel.getInstance().doesAnalyticModelHaveNoDependentAnalyticModels()) {
      return;
    }

    const changedElementsForImpact: ChangedElementsForImpact =
      this.impactedElements?.getChangedElementsForImpact() ?? ({} as ChangedElementsForImpact);
    const changedElements: Array<{ entity: string; key: string }> = [];
    Object.values(changedElementsForImpact).forEach((object) => {
      Object.values(object).forEach((impactedElement) => {
        changedElements.push(...Object.values(impactedElement ?? {}));
      });
    });

    // get cached dependencies first and add those before making the request
    const dependenciesFromCache =
      DependencyServiceAnalyticModel.getInstance().loadDependenciesFromCache(changedElements);
    this.updateValidationsWithDependencies(dependenciesFromCache, changedElementsForImpact);

    // get dependencies that need a request
    // we shouldn't wait for these
    // as waiting for that would add a considerable delay to the model change
    void DependencyServiceAnalyticModel.getInstance()
      .loadDependencies(changedElements, true)
      .then((newDependencies) => {
        this.updateValidationsWithDependencies(newDependencies, changedElementsForImpact);
      });
  }

  public updateValidationsWithDependencies(
    dependencies: IAnalyticModelDependencies,
    changedElementsForImpact: ChangedElementsForImpact
  ) {
    const aggregatedValidations: UIValidations = this.workbench.getToolbarModel().getProperty("/aggregatedValidations");
    const currentStatus = aggregatedValidations.status;
    const repoValidations: UIValidation[] = [];

    const shouldDisplayTechnicalName = this.user.getObjectNameDisplay() === ObjectNameDisplay.technicalName;

    const createValidationForElement = (
      key: string,
      objectType: AnalyticModelObjectType,
      elementDependencies: IAnalyticModelElementDependencies
    ) => {
      const dependency = elementDependencies?.dependencies;
      if (!dependency?.length) {
        return;
      }

      const dependencyLinks = dependency.map((dep) => {
        const link = `#/databuilder&/db/${dep.spaceId}/${dep.technicalName}`;
        return {
          title: shouldDisplayTechnicalName ? dep.technicalName : dep.businessName,
          href: link,
          text: shouldDisplayTechnicalName ? dep.technicalName : dep.businessName,
        };
      });

      // reuse markup generation from view builder, without release state as analytic models do not support that
      const markupList = (
        sap.cdw.querybuilder.Validation as any as {
          generateMarkupLinkList(dependencyLinks: Array<{ title: string; href: string; text: string }>);
        }
      ).generateMarkupLinkList(dependencyLinks);

      const messageKey = CubeBuilderObjects.DependentObjectsValidation;
      const parameters = [Formatter.objectTypeNameFormatter(objectType), key];
      const validation: UIValidation = {
        type: ValidationMessageType.WARN,
        messageKey: messageKey,
        descriptionKey: "",
        parameters: parameters,
        message: this.getText(messageKey, parameters),
        description: markupList,
        markupDescription: true,
      };
      repoValidations.push(validation);
    };

    Object.entries(changedElementsForImpact).forEach(([objectType, object]) => {
      Object.entries(object).forEach(([key, impactedElements]) => {
        for (const impactedElementKey in impactedElements) {
          const impactedElement = impactedElements[impactedElementKey];
          const elementDependencies = dependencies?.[impactedElement.entity]?.elements?.[impactedElement.key];
          if (elementDependencies?.dependencies?.length) {
            createValidationForElement(key, objectType as AnalyticModelObjectType, elementDependencies);
            break;
          }
        }
      });
    });

    const factSourceEntities = Object.values(this.model.getFactSources() ?? {}).map((source) => source.dataEntity.key);
    const modelDependencyKeys =
      dependencies?.[this.model.getData().identifier.key]?.elements[""]?.dependencies.map((d) => d.technicalName) ?? [];
    factSourceEntities.forEach((key) => {
      if (modelDependencyKeys.includes(key)) {
        const validation: UIValidation = {
          type: ValidationMessageType.ERROR,
          messageKey: CubeBuilderObjects.DependentFactSourceValidation,
          descriptionKey: "",
          parameters: [key],
          message: this.getText(CubeBuilderObjects.DependentFactSourceValidation, [key]),
        };
        repoValidations.push(validation);
      }
    });

    if (repoValidations.length > 0) {
      const newValidations = [...aggregatedValidations.validations, ...repoValidations];
      const hasErrors = repoValidations.some((validation) => validation.type === ValidationMessageType.ERROR);
      const newStatus =
        currentStatus === ValidationStatus.STATUS_ERROR || hasErrors
          ? ValidationStatus.STATUS_ERROR
          : ValidationStatus.STATUS_WARN;

      const toolbarModel = this.workbench.getToolbarModel();
      const newAggregatedValidations: UIValidations = {
        status: newStatus,
        validations: newValidations,
      };
      toolbarModel.setProperty("/aggregatedValidations", newAggregatedValidations);
      if (this.featureflags.DWCO_MODELING_AM_RESTORE_VERSION) {
        this.workbench.getWorkbenchEnvModel().setProperty("/editorValidationsStatus", newAggregatedValidations.status);
      }
    }
  }

  public isDependentObjectsValidationEnabled(): boolean {
    return Boolean(
      this.featureflags.DWCO_MODELING_ANALYTIC_MODEL_STACKING_LIFECYCLE &&
        !this.uiModel.getProperty("/isNew") &&
        !Util.Component.isOldVersion &&
        !Util.Component.isRestoreVersion &&
        Util.Component.hasWritePrivileges()
    );
  }

  public allCrossCalculationsSelectionChange() {
    this.recalculateVariablesTable();
    (this.takeOverDialog.getModel("takeOverDialogModel") as sap.ui.model.json.JSONModel).updateBindings(true);
  }

  private setCrossCalculationsCheckboxState(source: IDataEntity) {
    const value = this.featureflags.DWCO_MODELING_AM_MULTI_STRUCTURE && source.crossCalculations?.length > 0;
    this.uiModel.setProperty("/visibleSelectAllCrossCalculations", value);
    this.uiModel.setProperty("/addAllCrossCalculationsSelected", value);
  }

  /**
   * Recalculates the Takeover Dialog variables table.
   *
   * - Always includes mapped variables.
   * - Optionally adds variables used by measures and cross calculations based on the checkboxes state.
   *
   * Updates the "/parameters" property in the "takeOverDialogModel".
   */
  private recalculateVariablesTable() {
    const variablesInUse = new Map<string, TakeOverDialogParameter>();
    const variables: Map<string, TakeOverDialogParameter> = this.getDataEntityVariables();

    // Mapped variables are always inherited
    for (const variable of variables.values()) {
      if (
        (variable.parameterType === AnalyticModelParameterType.Input && variable.sourceMappingType) ||
        [
          AnalyticModelParameterType.StoryFilter,
          AnalyticModelParameterType.KeyDate,
          AnalyticModelParameterType.FiscalVariant,
        ].includes(variable.parameterType)
      ) {
        variablesInUse.set(variable.key, variable);

        for (const bindingKey of Object.keys(variable.parameterBinding ?? {})) {
          const binding = variable.parameterBinding[bindingKey];
          if (binding.mappingType === AnalyticModelSourceParameterMappingType.MapToSourceParameter) {
            const mappedVariable = variables.get(binding.variableName);
            if (mappedVariable) {
              variablesInUse.set(mappedVariable.key, mappedVariable);
            }
          }
        }
      }
    }

    const addVariablesUsedByMeasures = this.uiModel.getProperty("/addAllMeasuresSelected");
    if (addVariablesUsedByMeasures) {
      const variablesKeys = this.getVariablesKeysUsedByMeasures();
      for (const variableKey of variablesKeys) {
        const variable = variables.get(variableKey);
        if (variable) {
          variablesInUse.set(variable.key, variable);
        }
      }
    }

    const addVariablesUsedByCrossCalculations = this.uiModel.getProperty("/addAllCrossCalculationsSelected");
    if (addVariablesUsedByCrossCalculations) {
      const variablesKeys = this.getVariablesKeysUsedByCrossCalculations();
      for (const variableKey of variablesKeys) {
        const variable = variables.get(variableKey);
        if (variable) {
          variablesInUse.set(variable.key, variable);
        }
      }
    }

    const parameters = Array.from(variablesInUse.values());
    this.takeOverDialog.getModel("takeOverDialogModel").setProperty("/parameters", parameters);
  }

  private getVariablesKeysUsedByMeasures(): string[] {
    return this.takeOverDialog.getModel("takeOverDialogModel").getProperty("/variablesKeysUsedByMeasures");
  }

  private getVariablesKeysUsedByCrossCalculations(): string[] {
    return this.takeOverDialog.getModel("takeOverDialogModel").getProperty("/variablesKeysUsedByCrossCalculations");
  }

  private getDataEntityVariables() {
    const allVariables: TakeOverDialogParameter[] = this.uiModel.getProperty("/dataEntityParameters");
    const variableKeyToVariable = new Map<string, TakeOverDialogParameter>();
    for (const variable of allVariables) {
      variableKeyToVariable.set(variable.key, variable);
    }
    return variableKeyToVariable;
  }

  private addFiscalVariantVariable() {
    if (!this.featureflags.DWCO_MODELING_SUPPORT_FISCAL_TIME) {
      return;
    }

    if (!this.uiModel.getProperty("/isNew")) {
      return;
    }

    const attributeTree: DimensionTreeNode[] = this.uiModel.getProperty("/attributeTree");
    if (!attributeTree.some((attribute) => attribute.isFiscalDimension)) {
      return;
    }

    const allSourceDataEntityDetails = this.uiModel.getProperty(
      "/allSourceDataEntityDetails"
    ) as IDataEntityDetailsResponse;

    const fiscalVariantVariableExists = doesFiscalVariantVariableExist(this.model, allSourceDataEntityDetails);
    if (fiscalVariantVariableExists) {
      return;
    }

    new CreateVariable(AnalyticModelParameterType.FiscalVariant).execute(this.model, {
      featureFlags: this.featureflags,
      i18n: Util.Component.getModel("i18n"),
      allSourceDataEntityDetails: allSourceDataEntityDetails,
      expressions: undefined,
    });
    sap.m.MessageToast.show(this.getText("fiscalVariantVariableCreated"));
  }

  private setupUICollisionHandlingPriority() {
    if (this.featureflags.DWCO_MODELING_AM_MULTI_STRUCTURE) {
      const collisionHandlingPriority = this.model.getCollisionHandlingPriority();
      this.uiModel.setProperty("/collisionHandlingPriority", collisionHandlingPriority);
    }
  }
}

export const CubeBuilderEditor = smartExtend(
  AnalyticModelBase,
  "sap.cdw.components.cubebuilder.controller.CubeBuilderEditor",
  CubeBuilderEditorClass
);
sap.ui.define("sap/cdw/components/cubebuilder/controller/CubeBuilderEditor.controller", [], () => CubeBuilderEditor);
