/** @format */

import _, { cloneDeep } from "lodash";
import {
  AllAnalyticModelAttributes,
  AllQueryParameter,
  AnalyticModelAttributeType,
  AnalyticModelConversionTypeType,
  AnalyticModelCrossCalculationType,
  AnalyticModelDefaultRangeOption,
  AnalyticModelDimensionAffixType,
  AnalyticModelFilterOperandType,
  AnalyticModelMeasureType,
  AnalyticModelParameterType,
  AnalyticModelParameterValueBaseType,
  AnalyticModelReferenceDateType,
  AnalyticModelSourceParameterMappingType,
  AnalyticModelSourceType,
  AnalyticModelTargetCurrencyType,
  AnalyticModelTargetUnitType,
  AnalyticModelVariableProcessingType,
  AnalyticModelVariableSelectionType,
  IAnalyticAttributes,
  IAnalyticModelAttribute,
  IAnalyticModelAttributeKey,
  IAnalyticModelConstantValue,
  IAnalyticModelDefaultInterval,
  IAnalyticModelDefaultRange,
  IAnalyticModelDimensionAffix,
  IAnalyticModelDimensionSource,
  IAnalyticModelDimensionSourceAttribute,
  IAnalyticModelFactSourceAttribute,
  IAnalyticModelKeyDateVariable_MANUALINPUT,
  IAnalyticModelMeasure,
  IAnalyticModelRepositoryEntity,
  IAnalyticModelSource,
  IAnalyticModelSourceMeasure,
  IAnalyticModelSourceModel,
  IAnalyticModelSourceParameterBase,
  IAnalyticModelSourceParameterMapping_VARIABLE,
  IAnalyticModelSourceParameter_MANUALINPUT,
  IAnalyticModelSqlFunctionName,
  IAnalyticModelVariable,
  IAnalyticModelVariableKey,
  IAnalyticModelVariableManualInput,
} from "../../../../shared/queryBuilder/AnalyticModel";
import AnalyticModelSharedHelper from "../../../../shared/queryBuilder/AnalyticModelSharedHelper";
import {
  IDataEntity,
  IDataEntityAssociation,
  IDataEntityAttribute,
  IDataEntityDetailsResponse,
  IDataEntityFilterStack,
  IDataEntityParameter,
  IDataEntityProperty,
} from "../../../../shared/queryBuilder/DataEntityDetails";
import { ValidationMessage, ValidationMessageType } from "../../../../shared/queryBuilder/QueryModelValidator";
import {
  AbstractController,
  AbstractControllerClass,
} from "../../abstractbuilder/controller/AbstractController.controller";
import { smartExtend } from "../../basecomponent/controller/BaseController.controller";
import { CsnAnnotations } from "../../commonmodel/csn/csnAnnotations";
import { SemanticType } from "../../commonmodel/model/types/cds.types";
import { NameUsage } from "../../commonui/utility/NameInputValidator";
import { MultiInputItem } from "../../reuse/control/input/MultiInput";
import { ValueHelpDialog } from "../../reuse/control/valueHelpDialog/ValueHelpDialog";
import { Format } from "../../reuse/utility/Format";
import { MessageHandler } from "../../reuse/utility/MessageHandler";
import { NamingHelper } from "../../reuse/utility/NamingHelper";
import { EventType } from "../../shell/utility/ShellUsageCollectionService";
import { User } from "../../shell/utility/User";
import { ObjectNameDisplay } from "../../userSettings/utility/Constants";
import { DataEntityDetailsService } from "../api/DataEntityDetails.service";
import CommandStack from "../command/AnalyticModelCommandStack";
import { DeleteDimensionSource } from "../command/sourceModel/DeleteDimensionSource";
import { DeleteFactSourceFromModel } from "../command/sourceModel/DeleteFactSourceFromModel";
import { Dependencies } from "../extensions/dependencies/Dependencies";
import { StackedVariable } from "../extensions/variables/StackedVariable";
import { ValueDefinitionUtils } from "../extensions/variables/ValueDefinitionUtils";
import QueryUIModel from "../model/QueryUIModel";
import CubeBuilderObjects from "../utility/CubeBuilderObjects";
import {
  AttributeForMeasureOrVariable,
  AttributeGrouping,
  AttributeTree,
  AttributeTreeNode,
  AttributeWithKeyOnModel,
  AttributesByConversionDetail,
  AttributesByCurrencyDetail,
  AttributesForMeasureOrVariable,
  DefaultValueInterval,
  DefaultValueRange,
  DimensionKeys,
  DimensionSourceDimensionKey,
  DimensionTreeNode,
  GenericCallback,
  GraphSplitView,
  NodeAttributes,
  NodeCrossCalculation,
  NodeCrossCalculations,
  NodeMeasure,
  NodeMeasures,
  NodeParameters,
  TimeDimensionField,
  TimeDimensionFields,
  TypedSource,
  UICrossCalculation,
  UICrossCalculations,
  UIDac,
  UIDacs,
  UIFilter,
  UIFilters,
  UIGlobalFilter,
  UIMeasure,
  UIMeasures,
  UIStackedVariable,
  UIVariables,
  UsedInItem,
  ValueHelpDialogAM,
} from "../utility/CubeBuilderTypes";
import { USAGE_ACTIONS, getCurrentFeatureForUsageTracking, recordUsageTrackingEvent } from "../utility/UsageTracking";
import Util from "../utility/Util";

import { CONSTANT_VALUE_TYPES } from "../../../../shared/queryBuilder/QueryModel";
import {
  AnalyticModelObjectType,
  AnalyticModelUISourceType,
  ModelRoutes,
  UIVariableSubTypes,
  UsedInType,
} from "../utility/Enum";
import Formatter from "../utility/Formatter";
import {
  attributeSupportsFilter,
  doesFiscalVariantVariableExist,
  enableNewNameChecks,
  getAttributeDataType,
  getAttributeDetails,
  getAttributeDisplaySource,
  getDisplayDataType,
  getMeasureFromDataEntityDetails,
  getReferenceAttributes,
  getSourceValidation,
  getSourcesFromAttribute,
  getVariableBusinessName,
  getVariablesUsingVariable,
  isAttributeUsedInNonForeignKeyAssociation,
  isDuplicatedPropertyTechnicalName,
  isNonForeignKeyAssociation,
  isVariableDerivationOption,
} from "../utility/ModelUtils";

const Filter: typeof sap.ui.model.Filter = sap.ui.require("sap/ui/model/Filter");
const FilterOperator: typeof sap.ui.model.FilterOperator = sap.ui.require("sap/ui/model/FilterOperator");

export class AnalyticModelBaseClass extends AbstractControllerClass {
  protected model: QueryUIModel;
  protected uiModel: sap.ui.model.json.JSONModel;
  protected amFormatter: Formatter = Formatter;
  protected view: sap.ui.core.mvc.View;
  public isEditable: boolean;
  protected featureflags;
  public parameterDefaultValueDialog: sap.m.Dialog;
  protected modelDisplayName: { technicalName: string; businessName: string } | string;
  public stack: CommandStack<QueryUIModel>;
  protected stackedVariable: StackedVariable;
  protected valueDefinitionUtils: ValueDefinitionUtils;
  protected dependencies: Dependencies;
  protected takeOverDialog: sap.m.Dialog;

  public onInit(): void {
    super.onInit();
    this.featureflags = (sap.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel).getData();
    // set view models: this.model = the analyticModel, this.uiModel = the UI Model to control the UI
    this.getView().setModel(Util.Component.getModel("i18n"), "i18n");
    this.uiModel = Util.Component.getModel("ui") as sap.ui.model.json.JSONModel;
    this.getView().setModel(this.uiModel, "ui");
    this.model = Util.Component.getModel("analyticModel") as QueryUIModel;
    this.getView().setModel(this.model);
    this.view = this.getView();
    this.view.setBusyIndicatorDelay(0);
    this.registerForUserPrefChanges();
    this.stack = Util.Component.stack;
    this.stackedVariable = new StackedVariable(this);
    this.valueDefinitionUtils = new ValueDefinitionUtils();
    this.dependencies = new Dependencies(this.model);
  }

  /**
   * Subscribe to user preference changes
   */
  protected registerForUserPrefChanges() {
    sap.ui
      .getCore()
      .getEventBus()
      .subscribe(
        "userPreferences",
        "change",
        async () => {
          this.uiModel.updateBindings(true);
          this.model.updateBindings(true);
        },
        this
      );
  }

  /**
   * Loads metadata from all sources of a given model
   * Makes use of the /loadDataEntityDetails api to fetch details from all sources
   * Adds the metadata to the /allSourceDataEntityDetails property on the uiModel
   * @param {IAnalyticModelSourceModel} [sourceModel] - Source Model from Analytic Model to load metadata from
   */
  public async getSourceEntityDetails(sourceModel?: IAnalyticModelSourceModel) {
    const allSources: IAnalyticModelRepositoryEntity[] = [];

    if (!sourceModel) {
      sourceModel = this.model.getSourceModel();
    }

    if (!sourceModel) {
      return Promise.resolve();
    }
    if (sourceModel.dimensionSources) {
      for (const dimensionSource of Object.values(sourceModel.dimensionSources)) {
        allSources.push(dimensionSource.dataEntity);
      }
    }
    if (sourceModel.factSources) {
      for (const factSource of Object.values(sourceModel.factSources)) {
        allSources.push(factSource.dataEntity);
      }
    }

    // get all lookup entity names for variables
    const allVariables = this.model.getVariables();
    for (const variable of Object.values(allVariables)) {
      if ("variableProcessingType" in variable && isVariableDerivationOption(variable)) {
        allSources.push({ key: variable.lookupEntity });
      }
    }

    // get all dacs
    if (this.featureflags.DWCO_MODELING_AM_DAC_SUPPORT && this.model.getDimensionHandlingCapability()) {
      const allDacs = this.model.getDataAccessControls();
      for (const dacKey in allDacs) {
        allSources.push({ key: dacKey });
      }
    }

    if (!allSources.length) {
      return Promise.resolve();
    }

    let allSourceDetails = {};
    try {
      this.setWholeComponentBusy(true);
      allSourceDetails = await DataEntityDetailsService.getInstance().loadDataEntityDetails(
        allSources,
        super.getSpaceName()
      );
    } catch (e) {
      MessageHandler.uiError(this.getText("dataEntityDetailsError"));
    } finally {
      const allSourceDataEntityDetails = this.uiModel.getProperty("/allSourceDataEntityDetails") ?? {};
      allSourceDetails = { ...allSourceDataEntityDetails, ...allSourceDetails };
      this.uiModel.setProperty("/allSourceDataEntityDetails", allSourceDetails);

      this.setWholeComponentBusy(false);
    }
  }

  /**
   * Reads the variable from this.model and sets its property on /variableModel into the uiModel
   */
  public setupVariableList() {
    const variableModel = [];
    const selectedVariables = [];

    // map is used for checking if the values were selected previously
    // it works on the assumption that we call this after doing changes to the model,
    const oldVariablesMap = {};
    (this.uiModel.getProperty("/variableModel") as UIVariables)?.forEach((variable) => {
      oldVariablesMap[variable.key] = variable;
    });

    const variables = this.model.getVariables();
    for (const variableKey in variables) {
      const variable = variables[variableKey];
      const selected = Boolean(oldVariablesMap[variableKey]?.selected);

      let hidden = variable.variableProcessingType === AnalyticModelVariableProcessingType.LOOKUP;
      if (this.featureflags.DWCO_MODELING_SUPPORT_FISCAL_TIME) {
        hidden = hidden || variable.parameterType === AnalyticModelParameterType.FiscalVariant;
      }

      const variableData = {
        ...variable,
        key: variableKey,
        selected,
        hidden,
        text: (variable as IAnalyticModelVariable).text,
        // eslint-disable-next-line no-underscore-dangle
        _validationMessageType: this.getPropertyValidation(`/variables/${encodeURIComponent(variableKey)}`),
      };
      // For story filter variable, we need to get the attribute text to handle whether showing business/technical name
      if (variable.parameterType === AnalyticModelParameterType.StoryFilter) {
        const attribute = this.model.getAttribute(variable.referenceAttribute);
        variableData.text = attribute?.text;
      }

      variableModel.push(variableData);

      if (selected) {
        selectedVariables.push(variableData);
      }
    }
    /** set up stacked variables from runtime csn and add to ui model to show in output structure */
    const stackedVariables = this.stackedVariable.getStackedVariables();
    variableModel.push(...stackedVariables);
    this.uiModel.setProperty("/variableModel", variableModel);
    this.uiModel.setProperty("/selectedParameters", selectedVariables);
  }

  /**
   * Creates a list of measures from this.model and sets it as property /modelMeasures into the uiModel
   * Also updates non-cumulative settings
   * Does not perform changes to this.model.
   * The method also sets the property /measureModelMap in the uiModel, which has the fact source measure keys from the model
   */
  public setupUIMeasureList() {
    const modelMeasures = [];
    const selectedMeasures = [];
    const measureModelMap: { [key: string]: string[] } = {};
    const measures = this.model.getMeasures();

    // map is used for checking if the values were selected previously
    // it works on the assumption that we call this after doing changes to the model,
    const oldMeasureMap = {};
    (this.uiModel.getProperty("/modelMeasures") as UIMeasures)?.forEach((measure) => {
      oldMeasureMap[measure.technicalName] = measure;
    });

    for (const measureKey in measures) {
      const allSourceDataEntityDetails = this.uiModel.getProperty("/allSourceDataEntityDetails");
      const measureProperties = measures[measureKey];
      this.updateMeasureModelMap(measureModelMap, measureKey, measureProperties);
      let key = "";
      const typeIcon = Formatter.measureTypeIcon(measureProperties.measureType);
      const measureFromDataEntityDetails = getMeasureFromDataEntityDetails(
        this.model,
        allSourceDataEntityDetails,
        (measureProperties as IAnalyticModelSourceMeasure).key
      );
      // aggregation type determination: 1. From measure properties (explicitly assigned)
      let aggregationText = Formatter.measureAggregationText(measureProperties.aggregation);
      // aggregation type determination (fact source measure only): 2. From entity source measure (directly inherited)
      if (
        measureProperties.measureType === AnalyticModelMeasureType.FactSourceMeasure &&
        (aggregationText === null || aggregationText === "")
      ) {
        aggregationText = measureFromDataEntityDetails ? measureFromDataEntityDetails.aggregation : "";
        // aggregation type determination (restricted measure only): 3. From source measure (directly inherited)
        // or entity source measure (indirectly inherited)
      } else if (
        measureProperties.measureType === AnalyticModelMeasureType.RestrictedMeasure &&
        (aggregationText === null || aggregationText === "")
      ) {
        if (!!measureProperties.key) {
          const sourceMeasure = this.model.getMeasure(measureProperties.key) as IAnalyticModelSourceMeasure;
          aggregationText = sourceMeasure ? Formatter.measureAggregationText(sourceMeasure.aggregation) : "";
          const sourceMeasureFromDataEntityDetails = getMeasureFromDataEntityDetails(
            this.model,
            allSourceDataEntityDetails,
            sourceMeasure?.key
          );
          if (!aggregationText && sourceMeasure?.measureType === AnalyticModelMeasureType.FactSourceMeasure) {
            aggregationText = sourceMeasureFromDataEntityDetails ? sourceMeasureFromDataEntityDetails.aggregation : "";
          }
        }
      }
      // semantic type determination (fact source measures only)
      let semanticType = "";
      let unitColumn = "";
      if (
        measureProperties.measureType === AnalyticModelMeasureType.FactSourceMeasure &&
        measureFromDataEntityDetails?.semanticType
      ) {
        semanticType = measureFromDataEntityDetails.semanticType;
        unitColumn = measureFromDataEntityDetails.unitColumn;
      }

      if (
        measureProperties.measureType === AnalyticModelMeasureType.RestrictedMeasure ||
        measureProperties.measureType === AnalyticModelMeasureType.FactSourceMeasure ||
        measureProperties.measureType === AnalyticModelMeasureType.CurrencyConversionMeasure ||
        measureProperties.measureType === AnalyticModelMeasureType.UnitConversionMeasure ||
        measureProperties.measureType === AnalyticModelMeasureType.NonCumulativeMeasure
      ) {
        key = measureProperties.key;
      }

      const selected = Boolean(oldMeasureMap[measureKey]?.selected);

      let validationMessageType = this.getPropertyValidation(`/measures/${encodeURIComponent(measureKey)}`);
      if (
        measureProperties.measureType === AnalyticModelMeasureType.NonCumulativeMeasure &&
        validationMessageType === ValidationMessageType.OK
      ) {
        validationMessageType = this.getPropertyValidation(`/nonCumulativeSettings`);
      }

      const newModelMeasure: UIMeasure = {
        technicalName: measureKey,
        text: measureProperties.text,
        isAuxiliary: measureProperties.isAuxiliary,
        measureType: measureProperties.measureType,
        key: key,
        typeIcon: typeIcon,
        aggregationText: aggregationText,
        semanticType: semanticType,
        unitColumn: unitColumn,
        selected: selected,
        // eslint-disable-next-line no-underscore-dangle
        _validationMessageType: validationMessageType,
      };
      modelMeasures.push(newModelMeasure);

      if (selected) {
        selectedMeasures.push(newModelMeasure);
      }
    }
    this.uiModel.setProperty("/modelMeasures", modelMeasures);
    this.uiModel.setProperty("/measureModelMap", measureModelMap);
    this.uiModel.setProperty("/selectedMeasures", selectedMeasures);

    // update non cumulative settings
    this.setNonCumulativeUiSettings();
  }

  private updateMeasureModelMap(
    measureModelMap: { [key: string]: string[] },
    measureKey: string,
    measureProperties: IAnalyticModelMeasure
  ) {
    if (measureProperties.measureType === AnalyticModelMeasureType.FactSourceMeasure) {
      // for now we just support single fact sources, so will use always the first source
      const sourceKey = this.model.getFirstFactSourceKey();
      const uiKey = `${sourceKey}:${AnalyticModelSourceType.Fact}:${
        (measureProperties as IAnalyticModelSourceMeasure).key
      }`;
      if (!measureModelMap[uiKey]) {
        measureModelMap[uiKey] = [];
      }
      measureModelMap[uiKey].push(measureKey);
    }
  }

  /**
   * Determines and sets the non-cumulative settings for the UI from the non-cumulative settings in the model
   *  */
  public setNonCumulativeUiSettings() {
    const nonCumulativeSettings = this.model.getNonCumulativeSettings();
    if (!nonCumulativeSettings) {
      this.uiModel.setProperty("/nonCumulativeSettings", {});
      return;
    }

    const timeDimensionFields = this.getTimeDimensionFields();
    const timeDimensionKey = nonCumulativeSettings.timeDimensionKey;
    const timeDimensionField = timeDimensionFields.find(({ sourceKey }) => sourceKey === timeDimensionKey);
    const nonCumulativeUiSettings = {
      timeDimensionKey: timeDimensionField?.key,
      timeDimensionText: timeDimensionField?.text,
      recordTypeAttributeKey: nonCumulativeSettings.recordTypeAttributeKey,
      recordTypeAttributeText: this.model.getAttribute(nonCumulativeSettings.recordTypeAttributeKey)?.text,
      reportingMinStartTime: nonCumulativeSettings.reportingMinStartTime
        ? nonCumulativeSettings.reportingMinStartTime.value
        : "",
      reportingMaxEndTime: nonCumulativeSettings.reportingMaxEndTime
        ? nonCumulativeSettings.reportingMaxEndTime.value
        : "",
    };
    this.uiModel.setProperty("/nonCumulativeSettings", nonCumulativeUiSettings);
  }

  /**
   * Creates a global filter list (0 or 1 entries) from this.model and sets it as property /globalFilter in uiModel
   */
  public setupCurrentModelGlobalFilterList() {
    const globalFilterList: UIGlobalFilter[] = [];
    const globalFilter = this.model.getGlobalFilter();
    if (!!globalFilter) {
      const modelFilter: UIGlobalFilter = {
        text: globalFilter.text,
        formula: globalFilter.formula,
        formulaRaw: globalFilter.formulaRaw,
        _validationMessageType: this.getPropertyValidation("/filter"),
      };
      globalFilterList.push(modelFilter);
    }
    this.uiModel.setProperty("/globalFilter", globalFilterList);
  }

  /**
   * Creates a list of filters from this model and underlying models and sets it as property /modelFilters in uiModel
   */
  public setupUIFilterList() {
    this.setupCurrentModelGlobalFilterList();
    const globalFilterList = this.uiModel.getProperty("/globalFilter") as UIGlobalFilter[];
    const underlyingFilters = this.getFilteredModelsInStack();

    const selectedFilters = [];
    const modelFilters = [];

    const getUIFilterKey = (filter: UIFilter): string => {
      if (filter.type === AnalyticModelUISourceType.UNDERLYING) {
        return `${filter.type}.${filter.sourceTechnicalName}`;
      }
      return `${filter.type}`;
    };

    const oldFilterMap = {};
    (this.uiModel.getProperty("/modelFilters") as UIFilters)?.forEach((filter) => {
      oldFilterMap[getUIFilterKey(filter)] = filter;
    });

    underlyingFilters.forEach((underlyingFilter) => {
      const convertedFilter = this.convertUnderlyingFilterToUIFilter(underlyingFilter);
      convertedFilter.selected = Boolean(oldFilterMap[getUIFilterKey(convertedFilter)]?.selected);
      modelFilters.push(convertedFilter);

      if (convertedFilter.selected) {
        selectedFilters.push(convertedFilter);
      }
    });

    globalFilterList.forEach((globalFilter) => {
      const convertedFilter = this.convertGlobalFilterToUIFilter(globalFilter);
      convertedFilter.selected = Boolean(oldFilterMap[getUIFilterKey(convertedFilter)]?.selected);
      modelFilters.push(convertedFilter);

      if (convertedFilter.selected) {
        selectedFilters.push(convertedFilter);
      }
    });

    this.uiModel.setProperty("/modelFilters", modelFilters);
    this.uiModel.setProperty("/selectedFilters", selectedFilters);
  }

  /**
   * Get filters in stack from underlying models
   */
  public getFilteredModelsInStack(): IDataEntityFilterStack[] {
    const factSource = this.model.getFirstFactSource();
    const factSourceName = factSource?.dataEntity?.key;
    const dataEntityDetails = this.uiModel.getProperty("/allSourceDataEntityDetails")[factSourceName] as IDataEntity;

    return dataEntityDetails?.filteredModelsInStack ?? [];
  }

  private convertGlobalFilterToUIFilter(globalFilter: UIGlobalFilter): UIFilter {
    return {
      title: globalFilter.text,
      description: globalFilter.formulaRaw,
      type: AnalyticModelUISourceType.OUTPUT,
      selected: false,
      // eslint-disable-next-line no-underscore-dangle
      _validationMessageType: globalFilter._validationMessageType,
      formattedExpression: this.globalFilterExpressionFormatter(),
    };
  }

  private convertUnderlyingFilterToUIFilter(underlyingFilter: IDataEntityFilterStack): UIFilter {
    return {
      title: underlyingFilter.filterText,
      description: underlyingFilter.filterExpression,
      type: AnalyticModelUISourceType.UNDERLYING,
      selected: false,
      _validationMessageType: ValidationMessageType.OK,
      sourceBusinessName: underlyingFilter.businessName,
      sourceTechnicalName: underlyingFilter.technicalName,
    };
  }

  private globalFilterExpressionFormatter(): string {
    if (!this.featureflags.DWCO_MODELING_ANALYTIC_MODEL_STACKING) {
      return;
    }

    const globalFilter = this.model.getGlobalFilter();
    const expressionElements = Object.keys(globalFilter.elements ?? {});
    if (!expressionElements || expressionElements.length === 0) {
      return globalFilter.formulaRaw;
    }

    let replacedExpression = (globalFilter?.formula ?? "").toUpperCase();

    expressionElements.forEach((index) => {
      const element = globalFilter.elements[index];
      if (element.operandType === AnalyticModelFilterOperandType.Attribute) {
        const attribute = this.model.getAttribute(element.key);
        const name = this.getDisplayNameForElementInExpression(
          element.key,
          attribute?.text,
          AnalyticModelObjectType.ATTRIBUTE
        );
        replacedExpression = replacedExpression.replace(`[${index}]`, `${name}`);
      } else if (element.operandType === AnalyticModelFilterOperandType.ConstantValue) {
        const value = element.value;
        const isString = typeof value === "string";
        const name = isString ? `'${value}'` : String(value);
        replacedExpression = replacedExpression.replace(`[${index}]`, name);
      }
    });

    return replacedExpression;
  }

  public getDisplayNameForElementInExpression(
    elementKey: string,
    elementText: string,
    type: AnalyticModelObjectType
  ): string {
    const isVariable = type === AnalyticModelObjectType.VARIABLE;
    const businessName = isVariable ? "':" + elementText + "'" : "'" + elementText + "'";
    const shouldDisplayTechnicalName = User.getInstance().getObjectNameDisplay() === ObjectNameDisplay.technicalName;
    const technicalNameForDisplay = isVariable ? `:${elementKey}` : elementKey;
    const displayName = shouldDisplayTechnicalName ? technicalNameForDisplay : businessName;
    const objectRoute = CubeBuilderObjects.ObjectTypeToModelRoute[type];
    const name = this.getPropertyLink(objectRoute, elementKey, displayName);
    return name;
  }

  public setupDataAccessControlList() {
    const dacs = this.model.getDataAccessControls();
    if (!dacs || !this.featureflags.DWCO_MODELING_AM_DAC_SUPPORT) {
      this.uiModel.setProperty("/dac", []);
      this.uiModel.setProperty("/selectedDataAccessControls", []);
      return;
    }

    // map is used for checking if the values were selected previously
    // it works on the assumption that we call this after doing changes to the model,
    const oldDacsMap = {};
    (this.uiModel.getProperty("/dataAccessControls") as UIDacs)?.forEach((dac) => {
      oldDacsMap[dac.key] = dac;
    });

    const dacList: UIDacs = [];
    const selectedDacs: UIDacs = [];
    for (const dacKey in dacs) {
      const allSourceDataEntityDetails = this.uiModel.getProperty(`/allSourceDataEntityDetails`);
      const dacEntity = allSourceDataEntityDetails[dacKey] as IDataEntity;
      const selected = Boolean(oldDacsMap[dacKey]?.selected);

      const uiDac: UIDac = {
        key: dacKey,
        text: dacEntity.businessName,
        selected,
        // eslint-disable-next-line no-underscore-dangle
        _validationMessageType: this.getPropertyValidation(`/dataAccessControls/${encodeURIComponent(dacKey)}`),
      };
      dacList.push(uiDac);
      if (selected) {
        selectedDacs.push(uiDac);
      }
    }

    this.uiModel.setProperty("/dataAccessControls", dacList);
    this.uiModel.setProperty("/selectedDataAccessControls", selectedDacs);
  }

  /**
   * Checks if the model is a stacked model and sets the UI property accordingly
   */
  public setIsStackedModel() {
    let isStackedModel = false;
    if (
      this.featureflags.DWCO_MODELING_ANALYTIC_MODEL_STACKING &&
      this.featureflags.DWCO_MODELING_AM_NEW_DIM_HANDLING
    ) {
      const factSource = this.model.getFirstFactSource();
      if (factSource) {
        const factSourceName = factSource.dataEntity.key;
        const factSourceCsn = (this.uiModel.getProperty("/allSourceDataEntityDetails") as IDataEntityDetailsResponse)[
          factSourceName
        ]?.csn;
        isStackedModel = Boolean(factSourceCsn?.["@ObjectModel.modelingPattern"]?.["#"] === "ANALYTICAL_CUBE");
      }
    }
    this.uiModel.setProperty("/isStackedModel", isStackedModel);
  }

  /**
   * Checks if the model is stacked and update the preventAnalyticsCloudDataExport flag
   */
  public setupProtectDataExportFlag() {
    let preventAnalyticsCloudDataExport: boolean;
    if (this.isStackedModel()) {
      const factSourceKey = this.model.getFirstFactSource().dataEntity.key;
      const factSourceDataEntity = this.uiModel.getProperty("/allSourceDataEntityDetails")[factSourceKey];
      preventAnalyticsCloudDataExport = factSourceDataEntity.preventAnalyticsCloudDataExport;
    } else {
      preventAnalyticsCloudDataExport = this.model.getProtectDataExportFlag();
    }

    this.uiModel.setProperty("/allowDataExport", Boolean(!preventAnalyticsCloudDataExport));
  }

  public setupPackage() {
    const packageObject = this.model.getPackage();
    if (packageObject) {
      this.uiModel.setProperty("/package", packageObject.packageValue);
      this.uiModel.setProperty("/packageStatus", packageObject.packageStatus);
      if (this.getWorkbenchController().workbenchModel.getProperty("/forceStatus") === "enabled") {
        // force update
        this.getWorkbenchController().workbenchModel.setProperty("/forceStatus", "");
        this.getWorkbenchController().workbenchModel.setProperty("/forceStatus", "enabled");
      }
    }
  }

  public hasTimeDependency(source) {
    const elements = source.csn.elements;
    const associations = source.associations;

    if (elements) {
      for (const element of Object.values(elements)) {
        if (element[SemanticType.BUSINESS_DATE_FROM]) {
          return true;
        }
      }
    }

    if (associations) {
      for (const association of associations) {
        if (association.hasTimeDependency) {
          return true;
        }
      }
    }

    return false;
  }

  public getAttributeTechnicalNameInputValidation(
    technicalNameInput: sap.m.Input,
    currentTechnicalName: string,
    newTechnicalName: string
  ) {
    if (this.enableNewNameChecks()) {
      const errorMessages = this.getNameValidatorErrorMessages(
        technicalNameInput.getValue(),
        NameUsage.element,
        "attributeTechnicalNameEmpty"
      );
      if (errorMessages) {
        return errorMessages;
      }
    } else {
      NamingHelper.getNameInputValidator().onTechnicalNameChanged(technicalNameInput, technicalNameInput, "name", {
        duplicatedTechnicalName: this.getText("attributeTechnicalNameNotUnique"),
        emptyTechnicalName: this.getText("attributeTechnicalNameEmpty"),
      });
    }

    const modelAttribute = this.model.getAttribute(currentTechnicalName);
    // Validates technical name not empty
    if (!newTechnicalName) {
      return this.getText("attributeTechnicalNameEmpty");
      // Validates that affix has not been removed
    } else if (modelAttribute?.attributeType === AnalyticModelAttributeType.DimensionSourceAttribute) {
      const sourceKey = modelAttribute.sourceKey;
      const sourceTechnicalAffix = this.model.getDimensionSource(sourceKey).technicalAffix;
      if (!this.validateNameWithAffix(newTechnicalName, sourceTechnicalAffix)) {
        return this.getText("technicalNameWithoutAffix");
      }
    }

    // If there is no change to technical name return no error
    if (newTechnicalName === currentTechnicalName) {
      return "";
    }

    // Validates uniqueness of technical name
    if (this.model.getDimensionHandlingCapability()) {
      const attributeUsingNewTechnicalName = isDuplicatedPropertyTechnicalName(
        this.model,
        newTechnicalName,
        AnalyticModelObjectType.ATTRIBUTE,
        currentTechnicalName
      );
      if (attributeUsingNewTechnicalName) {
        return this.getText("attributeTechnicalNameNotUnique");
      }
    } else {
      // Validates uniqueness of technical name against attributes
      const attributeUsingNewTechnicallName = this.model.getAttribute(newTechnicalName);
      if (attributeUsingNewTechnicallName) {
        return this.getText("attributeTechnicalNameNotUnique");
      }
    }

    // Otherwise, new technical name is valid, we return no error
    return "";
  }

  public validateNameWithAffix(newName: string, sourceAffix: IAnalyticModelDimensionAffix) {
    if (sourceAffix?.type === AnalyticModelDimensionAffixType.PREFIX) {
      return newName.startsWith(sourceAffix.text);
    } else if (sourceAffix?.type === AnalyticModelDimensionAffixType.SUFFIX) {
      return newName.endsWith(sourceAffix.text);
    }
    return true;
  }

  public getNameValidatorErrorMessages(
    newTechnicalName: string,
    nameUsage: NameUsage,
    emptyTechnicalNameError: string
  ): string {
    const validator = NamingHelper.getNameInputValidator();
    const validationResult = validator.doDeriveTechnicalName(
      newTechnicalName,
      nameUsage,
      { skipAllErrors: false, skipInvalidError: false, skipTrailingErrors: true },
      [],
      {
        duplicatedTechnicalName: "",
        emptyTechnicalName: this.getText(emptyTechnicalNameError),
      }
    );

    const hasErrorMessages = validationResult.errors?.length > 0 && validationResult._errorMessages?.length > 0;
    const isElementOrAssociation = nameUsage === NameUsage.element || nameUsage === NameUsage.association;
    if (hasErrorMessages && isElementOrAssociation) {
      // get errors for ∞ and ignore them by overwriting the message
      const invalidChars = validator["getInvalidChars"](validationResult);
      const indexOfInfinity = invalidChars.indexOf("∞");
      if (indexOfInfinity > -1) {
        // remove old message
        const oldErrorMessage = this.getErrorMessageForInvalidChars(invalidChars);
        const indexOfOldErrorMessage = validationResult._errorMessages.indexOf(oldErrorMessage);
        validationResult._errorMessages.splice(indexOfOldErrorMessage, 1);

        // add new message if there are still invalid chars
        invalidChars.splice(indexOfInfinity, 1);
        if (invalidChars.length > 0) {
          const newErrorMessage = this.getErrorMessageForInvalidChars(invalidChars);
          validationResult._errorMessages.push(newErrorMessage);
        }
      }
    }

    const errorMessages = validator["combineErrorMessages"](validationResult);
    return errorMessages;
  }

  private getErrorMessageForInvalidChars(invalidChars: string[]) {
    const validator = NamingHelper.getNameInputValidator();
    const text = invalidChars.length > 1 ? "EnteredCharsNotAllowedPlural" : "EnteredCharsNotAllowedSingular";
    const invalidCharsString = validator["formatIncorrectCharString"](invalidChars);
    return validator.localizeText(text, [invalidCharsString]);
  }

  public getDimensionKeys(sourceKey: string, sourceInfo: IAnalyticModelDimensionSource): DimensionKeys {
    // No keys when the fact source is selected
    if (!sourceInfo.associationContexts) {
      return [];
    }
    if (this.model.getDimensionHandlingCapability()) {
      const dataEntity = this.uiModel.getProperty("/allSourceDataEntityDetails")[
        sourceInfo.dataEntity.key
      ] as IDataEntity;
      const attributes = this.model.getAttributes();
      const keyAttributes: DimensionKeys = Object.entries(attributes)
        .filter(([, attribute]) => attribute.usedForDimensionSourceKey === sourceKey)
        .map(([attributeKey, attribute]) => {
          const attributeSource = getSourcesFromAttribute(attribute, this.model);
          const attributeForeignKey = this.getAttributeKey(attributeKey, attribute);
          const targetAttribute = this.getTargetKeyFromAttribute(
            attributeForeignKey,
            attributeSource.sourceKey,
            attributeSource.type,
            sourceInfo
          );
          return {
            ...attribute,
            keyOnModel: attributeKey,
            targetAttribute,
            parentSourceDisplayName: this.getSourceDisplayName(attributeSource.sourceKey, attributeSource.source),
          };
        })
        .sort((a, b) => {
          const targetAttributeA = a.targetAttribute;
          const targetAttributeB = b.targetAttribute;
          const compoundKeySequence: Array<{ "=": string }> = dataEntity.csn["@DataWarehouse.compoundKeySequence"];
          if (!compoundKeySequence) {
            return 0;
          }
          const indexA = compoundKeySequence.findIndex((key) => key["="] === targetAttributeA);
          const indexB = compoundKeySequence.findIndex((key) => key["="] === targetAttributeB);
          if (indexA === -1 || indexB === -1) {
            return 0;
          }
          return indexA - indexB;
        });
      return keyAttributes;
    }

    if (sourceInfo && sourceInfo.associationContexts && sourceInfo.associationContexts.length) {
      let keyAttributes = this.getAttributesUsedInAssociation(sourceInfo) as DimensionKeys;
      keyAttributes = keyAttributes.map((attribute) => {
        const { source } = getSourcesFromAttribute(attribute, this.model);
        const parentSourceDisplayName = source.text;
        return { ...attribute, parentSourceDisplayName };
      });
      return keyAttributes;
    }
    return [];
  }

  /**
   * Returns the target key of an attribute used in an association to target
   * given the attribute information from the attribute that is used in the analytic model
   */
  public getTargetKeyFromAttribute(
    attributeForeignKey: string,
    attributeSourceKey: string,
    attributeSourceType: AnalyticModelSourceType,
    target: IAnalyticModelDimensionSource
  ) {
    // get parent source that maps to target
    const parentType = target.associationContexts[0].sourceType;
    const parentKey = target.associationContexts[0].sourceKey;
    const parentSource =
      parentType === AnalyticModelSourceType.Fact
        ? this.model.getFactSource(parentKey)
        : this.model.getDimensionSource(parentKey);

    // if parent is not the source of attribute, get target key from parent recursively
    if (attributeSourceType !== parentType || attributeSourceKey !== parentKey) {
      if (parentType === AnalyticModelSourceType.Fact) {
        // association to fact not possible
        return undefined;
      }
      const targetKeyFromParent = this.getTargetKeyFromAttribute(
        attributeForeignKey,
        attributeSourceKey,
        attributeSourceType,
        parentSource as IAnalyticModelDimensionSource
      );
      attributeForeignKey = targetKeyFromParent;
    }

    // match foreign key of parent to target key of target
    const sourceDetails = this.uiModel.getProperty("/allSourceDataEntityDetails")[
      parentSource.dataEntity.key
    ] as IDataEntity;
    const associationDetails = sourceDetails.associations.find(
      (association) => association.key === target.associationContexts[0].associationSteps[0]
    );
    const targetAttributeName = associationDetails?.targetKeys.find((_, index) => {
      const foreignKey = associationDetails.foreignKeys[index];
      return foreignKey === attributeForeignKey;
    });
    return targetAttributeName;
  }

  public getDimensionRepresentativeKey(sourceInfo) {
    const representativeKey =
      this.uiModel.getProperty("/allSourceDataEntityDetails")[sourceInfo.technicalName]?.csn?.[
        "@ObjectModel.representativeKey"
      ]?.["="] ?? undefined;
    return representativeKey;
  }

  public getParentForeignKeyForAssociatedDimension(sourceKey: string, source: IAnalyticModelDimensionSource) {
    const dimensionKeys = this.getDimensionKeys(sourceKey, source);
    const dimensionSource = this.model.getDimensionSource(sourceKey);
    return this.getRepresentativeKeyFromDimensionKeys(dimensionKeys, dimensionSource);
  }

  public getRepresentativeKeyFromDimensionKeys(dimensionKeys: DimensionKeys, dimensionSource) {
    if (dimensionKeys?.length > 1) {
      const sourceDetails = this.uiModel.getProperty("/allSourceDataEntityDetails")[dimensionSource.dataEntity.key];
      const parentSource = this.getParentSourceDetails(dimensionSource);
      const representativeKey = this.getDimensionRepresentativeKey(sourceDetails);

      const sourceAssociationKey = dimensionSource.associationContexts[0].associationSteps[0];
      const sourceKey: string = dimensionSource.associationContexts[0].sourceKey;
      const sourceAssociation = parentSource?.associations.find(
        (association) => association.key === sourceAssociationKey
      );
      if (!sourceAssociation) {
        // association was removed, return non duplicate key as representative
        return dimensionKeys.find((dimensionKey) => !dimensionKey.duplicated)?.keyOnModel;
      }

      const keyIndex = sourceAssociation.targetKeys.indexOf(representativeKey);
      const parentSourceAttribute = sourceAssociation.foreignKeys[keyIndex];

      return dimensionKeys.find((dimensionKey) => {
        let attributeKey;
        let attributeSourceKey: string;
        if (dimensionKey.attributeType === AnalyticModelAttributeType.FactSourceAttribute) {
          attributeKey = dimensionKey.attributeMapping[this.model.getFirstFactSourceKey()].key;
          attributeSourceKey = Object.keys(dimensionKey.attributeMapping)[0];
        } else {
          attributeKey = (dimensionKey as DimensionSourceDimensionKey).key;
          attributeSourceKey = (dimensionKey as DimensionSourceDimensionKey).sourceKey;
        }
        return attributeKey === parentSourceAttribute && attributeSourceKey === sourceKey;
      })?.keyOnModel;
    } else if (dimensionKeys?.length === 1) {
      return dimensionKeys[0].keyOnModel;
    } else {
      // no keys will be found if association was removed in original source
      return undefined;
    }
  }

  public getDimensionAsAttributeDetails(sourceKey: string, source: IAnalyticModelDimensionSource) {
    const sourceAttributeKey = this.getParentForeignKeyForAssociatedDimension(sourceKey, source);
    const allSourceDataEntityDetails = this.uiModel.getProperty("/allSourceDataEntityDetails");
    if (!sourceAttributeKey) {
      return undefined;
    }
    return getAttributeDetails(this.model.getAttribute(sourceAttributeKey), this.model, allSourceDataEntityDetails);
  }

  protected setNodeDetails(onlyCopyDiagramProperties: boolean = false): void {
    const diagramSelectedSource = this.uiModel.getProperty("/diagramSelectedSource");
    if (diagramSelectedSource.type === AnalyticModelSourceType.Dimension) {
      const businessAffix = this.model.getDimensionSource(diagramSelectedSource.id).businessAffix;
      const technicalAffix = this.model.getDimensionSource(diagramSelectedSource.id).technicalAffix;

      this.uiModel.setProperty("/diagramSelectedSource/businessAffix", businessAffix);
      this.uiModel.setProperty("/diagramSelectedSource/technicalAffix", technicalAffix);
    }

    if (this.uiModel.getProperty("/nodeDetails") && onlyCopyDiagramProperties) {
      this.uiModel.setProperty("/nodeDetails/dataEntity", diagramSelectedSource.dataEntity);
      this.uiModel.setProperty("/nodeDetails/text", diagramSelectedSource.text);
      this.uiModel.setProperty("/nodeDetails/type", diagramSelectedSource.type);
      this.uiModel.setProperty("/nodeDetails/id", diagramSelectedSource.id);
      this.uiModel.setProperty("/nodeDetails/key", diagramSelectedSource.key);
      this.uiModel.setProperty("/nodeDetails/parameterMappings", diagramSelectedSource.parameterMappings);
      this.uiModel.setProperty("/nodeDetails/associationContexts", diagramSelectedSource.associationContexts);
      this.uiModel.setProperty("/nodeDetails/businessAffix", diagramSelectedSource.businessAffix);
      this.uiModel.setProperty("/nodeDetails/technicalAffix", diagramSelectedSource.technicalAffix);
    } else {
      const nodeDetails = cloneDeep(this.uiModel.getProperty("/diagramSelectedSource"));
      this.uiModel.setProperty("/nodeDetails", nodeDetails);
    }
  }

  public getMeasureModelKeys(measure, source) {
    if (!source || !measure) {
      return false;
    }

    const measureModelMap = this.uiModel.getProperty("/measureModelMap");
    return measureModelMap[`${source.id}:${source.type}:${measure.key}`];
  }

  public measureSelectedFormatter(measure, diagramSelectedSource) {
    if (!measure || !diagramSelectedSource) {
      return false;
    }

    const measureModelMap = this.uiModel.getProperty("/measureModelMap");
    return !!measureModelMap[`${diagramSelectedSource.id}:${diagramSelectedSource.type}:${measure.key}`]?.length;
  }

  protected setUiMeasureProperties(measures: NodeMeasures, selectedObject) {
    if (selectedObject.type === AnalyticModelSourceType.Dimension) {
      this.uiModel.setProperty("/measures", []);
      return;
    }

    const uiMeasures = measures.reduce((uiMeasures: NodeMeasures, oMeasure: NodeMeasure) => {
      oMeasure.selected = this.measureSelectedFormatter(oMeasure, selectedObject);
      const measureName = {
        key: oMeasure.key,
        text: oMeasure.text,
      };
      if (oMeasure.selected) {
        const measureKeyInModel = this.getMeasureModelKeys(oMeasure, selectedObject)?.[0];
        const measureInModel = this.model.getMeasure(measureKeyInModel);
        if (measureInModel) {
          measureName.key = measureKeyInModel;
          measureName.text = measureInModel.text;
        }
      }
      if (this.uiModel.getProperty("/addAllMeasuresSelected")) {
        oMeasure.isKey = true;
      }
      oMeasure.displayText =
        User.getInstance().getObjectNameDisplay() === ObjectNameDisplay.technicalName
          ? measureName.key
          : measureName.text;

      if (!oMeasure.isHidden || oMeasure.selected) {
        uiMeasures.push(oMeasure);
      }

      return uiMeasures;
    }, []);
    this.uiModel.setProperty("/measures", uiMeasures);
  }

  /**
   * Updates breadcrumb and header based on the user selection to see technical/business name
   */
  public updateModelHeaderInfo() {
    this.updateModelDisplayName();
    this.updateBreadcrumb(this.modelDisplayName, []);
    this.updateHeader(this.modelDisplayName, "sap-icon://database");
  }

  /**
   * Creates a grouping to establish an hierarchy of attributes below its sources
   * ie: {Source1 : { attributes: [attrib1, attrib2]}, Source2: { attributes: [attrib4, attrib5]} }
   * Reads the attributes from this.model and composes a map grouping the attributes under each source
   * Sets the computed value to /attributeGrouping property of te uiModel
   */
  public setupAttributeGroupingBySource() {
    const attributes: IAnalyticAttributes = this.model.getAttributes();
    const groupedAttributes: Map<string, DimensionTreeNode> = new Map();
    const firstLevelAttributes = [];
    const attributeModelMap: { [key: string]: string } = {};
    const selectedDimensions = [];

    // maps are used for checking if the values were selected previously
    // it works on the assumption that we call this after doing changes to the model,
    const dimensionTreeMap = {};
    const attributeTreeMap = {};
    (this.uiModel.getProperty("/attributeTree") as AttributeTree)?.forEach((dimension) => {
      if (dimension.isDimension) {
        dimensionTreeMap[dimension.sourceKey] = dimension;
        dimension.nodes?.forEach((node) => {
          if (node.isAttribute) {
            attributeTreeMap[node.keyOnModel] = node;
          }
        });
      }
      if (dimension.isAttribute) {
        attributeTreeMap[dimension.keyOnModel] = dimension;
      }
    });

    // Creates a group for each dimension source
    const dimensionSources = this.model.getDimensionSources();
    for (const [dimensionKey, dimension] of Object.entries(dimensionSources)) {
      if (!groupedAttributes.get(dimensionKey)) {
        const parentSource = this.getParentSourceDetails(dimension);
        const usedAssociationName = dimension.associationContexts[0].associationSteps[0];
        const usedAssociationDetails = parentSource.associations.find((assoc) => assoc.key === usedAssociationName);
        // Derives text indicator from the association used to add current source to the model
        const hasTextAssociation = Boolean(usedAssociationDetails?.hasTextAssociation);
        const hasTextElement = Boolean(usedAssociationDetails?.hasTextElement);
        const hasHierarchies = Boolean(usedAssociationDetails?.hasHierarchies);
        const hasTimeDependency = Boolean(usedAssociationDetails?.hasTimeDependency);
        // for selected we get the previous tree map and check if was selected previously
        const selected = Boolean(dimensionTreeMap[dimensionKey]?.selected);

        const associationValidation = this.getSourceValidation(dimensionKey, AnalyticModelSourceType.Dimension);
        const validationMessageType = associationValidation?.type ?? ValidationMessageType.OK;
        const sortingTechnicalName =
          this.model.getDimensionHandlingCapability() === true ? dimensionKey : dimension.text;

        const isFiscalDimension =
          this.featureflags.DWCO_MODELING_SUPPORT_FISCAL_TIME && Boolean(usedAssociationDetails?.fiscalVariant);

        const dimensionSourceNode: DimensionTreeNode = {
          hasTextAssociation,
          hasTextElement,
          isDimension: true,
          isAttribute: false,
          sourceKey: dimensionKey,
          text: dimension.text,
          icon: "sap-icon://sac/perspectives",
          removeButton: true,
          nodes: [],
          type: AnalyticModelSourceType.Dimension,
          hasHierarchies,
          hasTimeDependency,
          selected,
          // eslint-disable-next-line no-underscore-dangle
          _validationMessageType: validationMessageType,
          sortingTechnicalName,
          isFiscalDimension,
        };
        groupedAttributes.set(dimensionKey, dimensionSourceNode);

        if (selected) {
          selectedDimensions.push(dimensionSourceNode);
        }
      }
    }

    // Puts each attribute into the respective group
    for (const keyOnModel of Object.keys(attributes)) {
      const allSourceDataEntityDetails = this.uiModel.getProperty("/allSourceDataEntityDetails");
      const currentAttribute = attributes[keyOnModel];
      const attributeDetails = getAttributeDetails(currentAttribute, this.model, allSourceDataEntityDetails);

      this.updateAttributeModelMap(attributeModelMap, keyOnModel, currentAttribute);

      // If the attribute is used in associations, hide it from the ui by
      // not adding it to the grouping
      if (this.model.getDimensionHandlingCapability()) {
        if (currentAttribute.usedForDimensionSourceKey) {
          const isAttributeKeyInAssociation = this.model.dimensionSourceExists(
            currentAttribute.usedForDimensionSourceKey
          );
          if (isAttributeKeyInAssociation) {
            continue;
          }
        }
      } else {
        const isAttributeKeyInAssociation = this.isAttributeRepresentativeKey(currentAttribute);
        if (isAttributeKeyInAssociation) {
          continue;
        }
      }

      // for selected we get the previous tree map and check if was selected previously
      const selected = Boolean(attributeTreeMap[keyOnModel]?.selected);

      const isHidden = Boolean(currentAttribute.isAuxiliary) || this.isAttributeHiddenByMeasure(currentAttribute);

      const { attributeType, text } = currentAttribute;
      const sortingTechnicalName = this.model.getDimensionHandlingCapability() === true ? keyOnModel : text;

      const enhancedAttribute: AttributeTreeNode = {
        nodes: [],
        text,
        icon: currentAttribute.duplicated ? "sap-icon://primary-key" : undefined,
        duplicated: currentAttribute.duplicated,
        attributeType,
        isDimension: false,
        isAttribute: true,
        isHidden,
        keyOnModel,
        selected,
        hasTextAssociation: attributeDetails ? attributeDetails.hasTextAssociation : false,
        hasTextElement: attributeDetails ? attributeDetails.hasTextElement : false,
        semanticType: attributeDetails ? attributeDetails.semanticType : undefined,
        primitiveType: attributeDetails ? attributeDetails.primitiveType : undefined,
        _validationMessageType: this.getPropertyValidation(`/attributes/${keyOnModel}`),
        sortingTechnicalName,
      };
      // New dimension handling activated, so we need to group the attributes by the usedForDimensionSourceKey
      if (this.model.getDimensionHandlingCapability() && currentAttribute.duplicated) {
        const key = currentAttribute.usedForDimensionSourceKey;
        enhancedAttribute.sourceKey = currentAttribute.usedForDimensionSourceKey;
        groupedAttributes.get(key).nodes.push(enhancedAttribute);
      } else {
        if (currentAttribute.attributeType === AnalyticModelAttributeType.FactSourceAttribute) {
          firstLevelAttributes.push(enhancedAttribute);
        }
        if (currentAttribute.attributeType === AnalyticModelAttributeType.DimensionSourceAttribute) {
          const key = currentAttribute.sourceKey;
          enhancedAttribute.sourceKey = currentAttribute.sourceKey;
          groupedAttributes.get(key).nodes.push(enhancedAttribute);
        }

        if (selected) {
          selectedDimensions.push(enhancedAttribute);
        }
      }
    }

    this.uiModel.setProperty("/attributeTree", [...groupedAttributes.values(), ...firstLevelAttributes]);
    this.uiModel.setProperty("/attributeModelMap", attributeModelMap);
    this.uiModel.setProperty("/selectedDimensions", selectedDimensions);
    this.updateDimensionsList(this.uiModel.getProperty("/outputListSearchValue"));
  }

  private isAttributeHiddenByMeasure(attribute: AllAnalyticModelAttributes) {
    if (attribute.attributeType !== AnalyticModelAttributeType.FactSourceAttribute) {
      return false;
    }
    const attributeNameInFactSource = attribute.attributeMapping[this.model.getFirstFactSourceKey()].key;
    const measures = this.model.getMeasures();
    return Object.values(measures).some((measure) => {
      if (measure.measureType !== AnalyticModelMeasureType.FactSourceMeasure) {
        return false;
      }
      const dataEntityMeasure = getMeasureFromDataEntityDetails(
        this.model,
        this.uiModel.getProperty("/allSourceDataEntityDetails"),
        measure.key
      );
      return dataEntityMeasure?.auxiliaryAttributes?.includes(attributeNameInFactSource);
    });
  }

  private updateAttributeModelMap(
    attributeModelMap: { [key: string]: string },
    keyOnModel: string,
    currentAttribute: AllAnalyticModelAttributes
  ) {
    // New Dimension Handling - We don't need to update the map for duplicated attributes,
    // since they are not displayed on the available attribute list.
    // This also fixes navigation from attribute list to always go to the base attribute
    if (this.model.getDimensionHandlingCapability() && currentAttribute.duplicated) {
      return;
    }
    // check if dimension attribute source has foreign key association to dimension which uses the attribute
    // if not then don't add the attribute to the map
    if (
      this.model.getDimensionHandlingCapability() &&
      isAttributeUsedInNonForeignKeyAssociation(this.model, currentAttribute)
    ) {
      return;
    }

    const source = getSourcesFromAttribute(currentAttribute, this.model);
    let technicalName;
    let sourceType;
    if (currentAttribute.attributeType === AnalyticModelAttributeType.FactSourceAttribute) {
      technicalName = currentAttribute.attributeMapping[source.sourceKey].key;
      sourceType = AnalyticModelSourceType.Fact;
    }
    if (currentAttribute.attributeType === AnalyticModelAttributeType.DimensionSourceAttribute) {
      technicalName = currentAttribute.key;
      sourceType = AnalyticModelSourceType.Dimension;
    }
    if (sourceType && technicalName) {
      const uiKey = `${source.sourceKey}:${sourceType}:${technicalName}`;
      attributeModelMap[uiKey] = keyOnModel;
    }
  }

  public updateDimensionsList(searchInput = ""): void {
    this.updateDimensionsFilter(searchInput);
    this.setDimensionDisplaysInnerAttributes();
    this.expandedAttributeByCurrencyCode();
    this.expandedAttributeByConversionCode();
    this.uiModel.refresh(true);
  }

  private updateDimensionsFilter(input: string): void {
    // TODO check if this method is still needed
    const lowerCaseInput = input.toLowerCase();
    const attributeGrouping: AttributeGrouping = this.uiModel.getProperty("/attributeGrouping");
    attributeGrouping.forEach((dimension) => {
      let hasVisibleAttributes = false;
      if (dimension.attributes.length > 0) {
        dimension.attributes.forEach((attribute) => {
          if (
            attribute.text.toLowerCase().includes(lowerCaseInput) ||
            attribute.keyOnModel.toLowerCase().includes(lowerCaseInput)
          ) {
            attribute.visibleInList = true;
            dimension.visibleInList = true;
            hasVisibleAttributes = true;
          } else {
            attribute.visibleInList = false;
          }
        });
      }
      if (!hasVisibleAttributes) {
        dimension.visibleInList = dimension.text.toLowerCase().includes(lowerCaseInput);
      }
    });
  }

  /**
   * This function is expanding attributes with 2 new properties: currencyCode and type
   * the /attributesByCurrencyDetail is used to Dimensions tab in value helps of Currency conversion
   */
  private expandedAttributeByCurrencyCode(): void {
    // clone Attributes from model to the new property in uiModel
    const attributes: IAnalyticAttributes = this.model.getAttributes();
    const expandedAttributeByCurrencyDetail: AttributesByCurrencyDetail = Object.fromEntries(
      Object.entries(attributes).map(([key, attribute]) => {
        const attributeDetails = getAttributeDetails(
          attribute,
          this.model,
          this.uiModel.getProperty("/allSourceDataEntityDetails")
        );
        return [
          key,
          {
            ...attribute,
            semanticType: attributeDetails?.semanticType,
            primitiveType: attributeDetails?.primitiveType,
          },
        ];
      })
    );
    this.uiModel.setProperty("/attributesByCurrencyDetail", expandedAttributeByCurrencyDetail);
  }

  private expandedAttributeByConversionCode(): void {
    const attributes: IAnalyticAttributes = this.model.getAttributes();
    // clone Attributes from model to the new property in uiModel
    const expandedAttributeByUnitDetail = { ...attributes } as AttributesByConversionDetail;
    const sourceAttributes: AttributeTree = this.uiModel.getProperty("/attributeTree");
    const sourceAttributesWithNodes: AttributeTreeNode[] = [];
    // This is a Recursive call to get the Node of Attributes
    this.getNodeOfAttributes(sourceAttributes, sourceAttributesWithNodes);
    this.setConversionCode(sourceAttributesWithNodes, expandedAttributeByUnitDetail);
  }

  private getNodeOfAttributes(sourceAttributes: AttributeTree, sourceAttributesWithNodes: AttributeTreeNode[]) {
    sourceAttributes.forEach((attribute) => {
      if (attribute && attribute.isAttribute) {
        sourceAttributesWithNodes.push(attribute);
      }
      if (attribute && attribute.isDimension) {
        this.getNodeOfAttributes(attribute.nodes, sourceAttributesWithNodes);
      }
    });
  }

  private setCurrencyCode(
    sourceAttributes: AttributeTreeNode[],
    expandedAttributeByCurrencyDetail: AttributesByCurrencyDetail
  ) {
    sourceAttributes.forEach((attribute) => {
      if (expandedAttributeByCurrencyDetail && expandedAttributeByCurrencyDetail[attribute.keyOnModel]) {
        expandedAttributeByCurrencyDetail[attribute.keyOnModel].semanticType = attribute.semanticType;
        expandedAttributeByCurrencyDetail[attribute.keyOnModel].primitiveType = attribute.primitiveType;
      }
    });
    this.uiModel.setProperty("/attributesByCurrencyDetail", expandedAttributeByCurrencyDetail);
  }
  private setConversionCode(
    sourceAttributes: AttributeTreeNode[],
    expandedAttributeByConversionDetail: AttributesByConversionDetail
  ) {
    sourceAttributes.forEach((attribute) => {
      if (expandedAttributeByConversionDetail && expandedAttributeByConversionDetail[attribute.keyOnModel]) {
        expandedAttributeByConversionDetail[attribute.keyOnModel].semanticType = attribute.semanticType;
        expandedAttributeByConversionDetail[attribute.keyOnModel].primitiveType = attribute.primitiveType;
      }
    });
    this.uiModel.setProperty("/attributesByConversionDetail", expandedAttributeByConversionDetail);
  }

  private setDimensionDisplaysInnerAttributes(): void {
    const attributeGrouping: AttributeGrouping = this.uiModel.getProperty("/attributeGrouping");
    attributeGrouping.forEach((dimension) => {
      const attributes = dimension.attributes;
      dimension.displaysInnerAttributes = attributes.length !== 0 ? attributes.some((att) => att.visibleInList) : false;
    });
  }

  public getKeyAttributeDetails(source) {
    if (!source) {
      return false;
    }
    const allSourceDataEntityDetails: IDataEntityDetailsResponse =
      this.uiModel.getProperty("/allSourceDataEntityDetails");
    const sourceDetails = allSourceDataEntityDetails[source.dataEntity.key];
    if (!sourceDetails || !sourceDetails.attributes) {
      return false;
    }
    const keyAttributes = sourceDetails.attributes.filter((attr) => attr.isKey);
    return keyAttributes;
  }

  public canAttributeBeRemoved(attribute: AllAnalyticModelAttributes, attributeKeyOnModel: string) {
    if (this.model.getDimensionHandlingCapability()) {
      // If we have a duplicated attribute from the original one, it means that it's being used as
      // compounding key in the model, so we can't remove the association
      const referenceAttributes = getReferenceAttributes(attributeKeyOnModel, this.model);
      return !referenceAttributes || Object.keys(referenceAttributes).length === 0;
    } else {
      return !this.isAttributeUsedInModelDimensionSources(attribute);
    }
  }

  public isAttributeUsedInModelDimensionSources(attribute: AllAnalyticModelAttributes) {
    let attributeKey, attributeSourceType, attributeSourceKey: string;
    if (attribute.attributeType === AnalyticModelAttributeType.CalculatedAttribute) {
      return false;
    }
    if (attribute.attributeType === AnalyticModelAttributeType.FactSourceAttribute) {
      const attributeMapping = attribute.attributeMapping;
      attributeSourceKey = Object.keys(attributeMapping)[0];
      attributeKey = attributeMapping[attributeSourceKey].key;
      attributeSourceType = AnalyticModelSourceType.Fact;
    } else if (attribute.attributeType === AnalyticModelAttributeType.DimensionSourceAttribute) {
      attributeSourceKey = attribute.sourceKey;
      attributeKey = attribute.key;
      attributeSourceType = AnalyticModelSourceType.Dimension;
    }

    const dimensionSources = this.model.getDimensionSources();

    // Get all associations currently in use by the model
    const associations = [];
    for (const dimensionSource of Object.values(dimensionSources)) {
      for (const association of Object.values(dimensionSource.associationContexts)) {
        associations.push(association);
      }
    }
    const entityDetails: IDataEntityDetailsResponse = this.uiModel.getProperty("/allSourceDataEntityDetails");
    const associationForeignKeys = [];
    for (const association of associations) {
      let source;
      const sourceKey = association.sourceKey;
      if (association.sourceType === AnalyticModelSourceType.Dimension) {
        source = this.model.getDimensionSource(sourceKey);
      } else {
        source = this.model.getFactSource(sourceKey);
      }
      // If we remove an source from the top of model tree we can have some inconsistencies
      // in this case we can have an association pointin to a source that is not in the model anymore, in this case we
      // skip that association
      if (source) {
        const sourceTechnicalName = source.dataEntity.key;
        const sourceEntityDetails = entityDetails[sourceTechnicalName];
        const associationTechnicalName = association.associationSteps[0];
        const associationDetails =
          sourceEntityDetails.associations.find((association) => association.key === associationTechnicalName) ||
          ({} as IDataEntityAssociation);
        if (associationDetails && associationDetails.foreignKeys) {
          for (const associationForeignKey of associationDetails.foreignKeys) {
            associationForeignKeys.push({
              attributeKey: associationForeignKey,
              sourceKey: sourceKey,
              sourceType: association.sourceType,
            });
          }
        }
      }
    }
    return associationForeignKeys.some(
      (foreignKeyDetail) =>
        foreignKeyDetail.attributeKey === attributeKey &&
        foreignKeyDetail.sourceType === attributeSourceType &&
        foreignKeyDetail.sourceKey === attributeSourceKey
    );
  }

  public isAttributeRepresentativeKey(attribute: AllAnalyticModelAttributes) {
    let attributeKey, attributeSourceType, attributeSourceKey: string;
    if (attribute.attributeType === AnalyticModelAttributeType.CalculatedAttribute) {
      return false;
    }
    if (attribute.attributeType === AnalyticModelAttributeType.FactSourceAttribute) {
      const attributeMapping = attribute.attributeMapping;
      attributeSourceKey = Object.keys(attributeMapping)[0];
      attributeKey = attributeMapping[attributeSourceKey].key;
      attributeSourceType = AnalyticModelSourceType.Fact;
    } else if (attribute.attributeType === AnalyticModelAttributeType.DimensionSourceAttribute) {
      attributeSourceKey = attribute.sourceKey;
      attributeKey = attribute.key;
      attributeSourceType = AnalyticModelSourceType.Dimension;
    }

    const dimensionSources = this.model.getDimensionSources();

    // Get all associations currently in use by the model
    const associations = [];
    for (const dimensionSource of Object.values(dimensionSources)) {
      for (const association of Object.values(dimensionSource.associationContexts)) {
        associations.push(association);
      }
    }
    const entityDetails: IDataEntityDetailsResponse = this.uiModel.getProperty("/allSourceDataEntityDetails");
    const representativeKeys = [];
    for (const association of associations) {
      let source;
      const sourceKey = association.sourceKey;
      if (association.sourceType === AnalyticModelSourceType.Dimension) {
        source = this.model.getDimensionSource(sourceKey);
      } else {
        source = this.model.getFactSource(sourceKey);
      }
      // If we remove an source from the top of model tree we can have some inconsistencies
      // in this case we can have an association pointing to a source that is not in the model anymore, in this case we
      // skip that association
      if (source) {
        const sourceTechnicalName = source.dataEntity.key;
        const sourceEntityDetails = entityDetails[sourceTechnicalName];
        const associationTechnicalName = association.associationSteps[0];
        const associationDetails =
          sourceEntityDetails.associations.find((association) => association.key === associationTechnicalName) ||
          ({} as IDataEntityAssociation);
        if (associationDetails && associationDetails.attributeForAssociation) {
          representativeKeys.push({
            attributeKey: associationDetails.attributeForAssociation,
            sourceKey: sourceKey,
            sourceType: association.sourceType,
          });
        }
      }
    }
    return representativeKeys.some(
      (foreignKeyDetail) =>
        foreignKeyDetail.attributeKey === attributeKey &&
        foreignKeyDetail.sourceType === attributeSourceType &&
        foreignKeyDetail.sourceKey === attributeSourceKey
    );
  }

  public getAttributeList() {
    const attributes = this.model.getAttributes();
    const attributeColumnList: AttributesForMeasureOrVariable = [];
    const allSourceDataEntityDetails = this.uiModel.getProperty("/allSourceDataEntityDetails");
    // exclude the attribute selected as record type field for non-cumulative measures from other usages in the model
    const recordTypeAttributeKey = this.model.getNonCumulativeSettings()?.recordTypeAttributeKey;
    if (!!recordTypeAttributeKey) {
      delete attributes[recordTypeAttributeKey];
    }
    for (const attribute in attributes) {
      if (attributeSupportsFilter(attributes[attribute], this.model, allSourceDataEntityDetails)) {
        const attributeColumn = {} as AttributeForMeasureOrVariable;
        const attributeSource = getSourcesFromAttribute(attributes[attribute], this.model);
        let attributeKey;
        if (attributeSource.type === AnalyticModelSourceType.Fact) {
          attributeKey = (attributes[attribute] as IAnalyticModelFactSourceAttribute).attributeMapping[
            this.model.getFirstFactSourceKey()
          ].key;
        } else {
          attributeKey = (attributes[attribute] as IAnalyticModelDimensionSourceAttribute).key;
        }
        const attributeType = getAttributeDataType(
          attributeSource.source.dataEntity.key,
          attributeKey,
          allSourceDataEntityDetails
        );
        attributeColumn.type = attributeType;
        attributeColumn.key = attribute;
        attributeColumn.text = attributes[attribute].text;

        if (attributeSource.type === AnalyticModelSourceType.Dimension) {
          const attributeSourceBusinessName = attributeSource.source.text;
          const attributeSourceTechnicalName = this.model.getDimensionHandlingCapability()
            ? attributeSource.sourceKey
            : attributeSource.source.text;
          attributeColumn.displayText = (attributeSource.source as IAnalyticModelDimensionSource).businessAffix
            ? `${attributes[attribute].text}`
            : `${attributes[attribute].text} (${attributeSourceBusinessName})`;
          attributeColumn.displayTechnicalText = (attributeSource.source as IAnalyticModelDimensionSource)
            .technicalAffix
            ? `${attribute}`
            : `${attribute} (${attributeSourceTechnicalName})`;
        } else {
          attributeColumn.displayText = `${attributes[attribute].text} (${attributeSource.source.text})`;
          attributeColumn.displayTechnicalText = `${attribute} (${attributeSource.source.text})`;
        }

        attributeColumn.source = attributeSource.source.dataEntity.key;
        attributeColumnList.push(attributeColumn);
      }
    }
    return attributeColumnList;
  }

  public resetDependentObjectsProperties() {
    this.uiModel.setProperty("/modelDependentObjects/", {});
    this.uiModel.setProperty("/modelDependentObjectsSearch", "");
  }

  public getSourcePropertyFilter(value: string) {
    // TODO: Refactor the ui model so all lists have the same property as technical name for objects
    const filters = [
      new Filter("text", FilterOperator.Contains, value),
      new Filter("key", FilterOperator.Contains, value),
      new Filter("technicalName", FilterOperator.Contains, value),
      new Filter("keyOnModel", FilterOperator.Contains, value),
      new Filter("displayText", FilterOperator.Contains, value),
      new Filter("targetEntity/text", FilterOperator.Contains, value),
      new Filter("title", FilterOperator.Contains, value),
    ];

    return new Filter({
      filters: filters,
      and: false,
    });
  }

  public createMeasureKey(property: IDataEntityProperty, selectedObject) {
    let newPropertyKey = property.key;
    const modelMeasure = this.model.getMeasure(newPropertyKey);
    if (modelMeasure) {
      newPropertyKey = `${property.key}${selectedObject.id}`;
    }
    return newPropertyKey;
  }

  public setUiAttributeProperties(attributes?: NodeAttributes, selectedObject?) {
    if (!attributes) {
      attributes = this.uiModel.getProperty("/attributes");
    }
    if (!selectedObject) {
      selectedObject = this.uiModel.getProperty("/diagramSelectedSource");
    }
    // Filter attributes (Dimensions should not have their keys visible, as they'll be exposed by using the corresponding association)
    const filteredUiAttributes =
      selectedObject?.type === AnalyticModelSourceType.Dimension
        ? attributes.filter((attribute) => !attribute.isKey)
        : attributes;

    const user = User.getInstance();

    const allSourceDataEntityDetails = this.uiModel.getProperty("/allSourceDataEntityDetails");

    const uiAttributes = filteredUiAttributes.reduce((attributeList, oAttribute) => {
      oAttribute.selected = this.isAttributeInModel(oAttribute, selectedObject);
      const attributeKeyOnModel = this.getAttributeModelKey(oAttribute, selectedObject);
      let modelAttributeDisplayText;
      if (attributeKeyOnModel) {
        oAttribute.attributeInModel = this.model.getAttribute(attributeKeyOnModel);
        oAttribute.keyOnModel = attributeKeyOnModel;
      }
      // if the attribute is on model, thus selected, we need to check if it is used in an association to disable its removal
      let foreignKeyAssociationNotAdded = false;
      if (oAttribute.selected) {
        const attributeModelKey = this.getAttributeModelKey(oAttribute, selectedObject);

        if (attributeModelKey) {
          const attributeInModel = this.model.getAttribute(attributeModelKey);
          oAttribute.attributeInModel = attributeInModel;
          oAttribute.enabled = this.canAttributeBeRemoved(attributeInModel, attributeKeyOnModel);
          modelAttributeDisplayText =
            user.getObjectNameDisplay() === ObjectNameDisplay.technicalName ? attributeModelKey : attributeInModel.text;
          if (this.model.getDimensionHandlingCapability()) {
            if (!attributeInModel.usedForDimensionSourceKey) {
              foreignKeyAssociationNotAdded = true;
            }
          }
        }
      }
      // if the attribute is not on model, it can be selected, so its enabled by default
      else {
        oAttribute.enabled = true;
      }

      oAttribute.foreignKeyAssociationNotAdded = foreignKeyAssociationNotAdded;

      if (oAttribute.selected) {
        oAttribute.displayText = modelAttributeDisplayText;
      } else {
        oAttribute.displayText =
          user.getObjectNameDisplay() === ObjectNameDisplay.technicalName ? oAttribute.key : oAttribute.text;
      }

      if (this.model.getDimensionHandlingCapability()) {
        const dataEntity = allSourceDataEntityDetails[selectedObject?.dataEntity?.key] as IDataEntity;
        const association = dataEntity?.associations.find(
          (association) => association.key === oAttribute.foreignKeyAssociation
        );
        oAttribute.hasTimeDependency = association?.hasTimeDependency ?? false;
        oAttribute.hasHierarchies = association?.hasHierarchies ?? false;
        oAttribute.associationHasTextElement = association?.hasTextElement ?? false;
        oAttribute.associationHasTextAssociation = association?.hasTextAssociation ?? false;
        oAttribute.isFiscalDimension = Boolean(association?.fiscalVariant);
      }

      // If attribute is hidden or it's type is not supported
      // When the attribute is already in the model, we add it to the list so the user is able to remove it
      // Otherwise it won't appear on the available attribute list since these scenarios are not supported
      if (oAttribute.isHidden || oAttribute.isNotSupportedPrimitiveTypes) {
        if (oAttribute.selected) {
          attributeList.push(oAttribute);
        }
      } else {
        attributeList.push(oAttribute);
      }

      return attributeList;
    }, []);

    this.uiModel.setProperty("/attributes", uiAttributes);
  }

  protected async setUiAssociationProperties(entityDetails: IDataEntity, selectedObject: TypedSource) {
    const associations = entityDetails.associations;
    const uiAssociations = associations.map((oAssociation) => {
      const associationNameAndSelected = this.associationNameAndSelectedFormatter(
        oAssociation,
        selectedObject.type,
        selectedObject.id,
        this.model.getSourceModel()
      );

      return {
        ...oAssociation,
        displayText: associationNameAndSelected.name,
        targetEntity: {
          ...oAssociation.targetEntity,
          displayText:
            User.getInstance().getObjectNameDisplay() === ObjectNameDisplay.technicalName
              ? oAssociation.targetEntity.key
              : oAssociation.targetEntity.text,
        },
        selected: associationNameAndSelected.selected,
        isNonForeignKeyAssociation: this.model.getDimensionHandlingCapability()
          ? isNonForeignKeyAssociation(oAssociation, entityDetails, selectedObject.type)
          : false,
      };
    });
    const uiNonForeignKeyAssociations = uiAssociations.filter((association) => association.isNonForeignKeyAssociation);

    this.uiModel.setProperty("/associations", uiAssociations);
    this.uiModel.setProperty("/nonForeignKeyAssociations", uiNonForeignKeyAssociations);
  }

  public associationNameAndSelectedFormatter(
    association,
    sourceType,
    sourceKey,
    sourceModel: IAnalyticModelSourceModel
  ) {
    const originalName = association.dimensionSourceAlias;
    const nameAndSelected = {
      name: originalName,
      selected: false,
    };
    const shouldDisplayTechnicalName =
      User.getInstance().getObjectNameDisplay() === ObjectNameDisplay.technicalName &&
      this.model.getDimensionHandlingCapability();
    Object.keys(sourceModel?.dimensionSources || {}).forEach((key) => {
      const source = sourceModel.dimensionSources[key];
      if (source?.dataEntity?.key === association?.targetEntity?.key) {
        const isSelected = source?.associationContexts.some(
          (ctx) =>
            ctx.sourceType === sourceType &&
            ctx.sourceKey === sourceKey &&
            _.isEqual(ctx.associationSteps, [association.key])
        );
        if (isSelected) {
          nameAndSelected.name = shouldDisplayTechnicalName ? key : source.text;
          nameAndSelected.selected = isSelected;
          return; // end forEach
        }
      }
    });
    return nameAndSelected;
  }

  public associationFromEntityExistsInModel(
    entityDetails: IDataEntity,
    association: IDataEntityAssociation,
    model: QueryUIModel
  ): boolean {
    return Object.values(model.getDimensionSources()).some((dimensionSource) =>
      dimensionSource.associationContexts.find((ctx) => {
        let parentSource: IAnalyticModelSource;
        if (dimensionSource.associationContexts[0].sourceType === AnalyticModelSourceType.Fact) {
          parentSource = model.getFactSource(dimensionSource.associationContexts[0].sourceKey);
        } else {
          parentSource = model.getDimensionSource(dimensionSource.associationContexts[0].sourceKey);
        }
        return (
          ctx.associationSteps[0] === association.key && parentSource?.dataEntity.key === entityDetails.technicalName
        );
      })
    );
  }

  public isAttributeInModel(attribute, diagramSelectedSource) {
    if (!attribute || !diagramSelectedSource) {
      return false;
    }
    const attributeModelMap = this.uiModel.getProperty("/attributeModelMap");
    return !!attributeModelMap[`${diagramSelectedSource.id}:${diagramSelectedSource.type}:${attribute.key}`];
  }
  public getAttributeModelKey(attribute: { key: string }, source: { id: string; type: string }) {
    if (!source || !attribute) {
      return false;
    }
    const attributeModelMap = this.uiModel.getProperty("/attributeModelMap");
    return attributeModelMap[`${source.id}:${source.type}:${attribute.key}`];
  }

  public getAttributeKey(attributeName: string, attribute: IAnalyticModelAttribute): string {
    let attributeKey = attributeName;

    if (attribute && attribute.attributeType === AnalyticModelAttributeType.FactSourceAttribute) {
      attributeKey = (attribute as IAnalyticModelFactSourceAttribute).attributeMapping[
        this.model.getFirstFactSourceKey()
      ].key;
    } else if (attribute && attribute.attributeType === AnalyticModelAttributeType.DimensionSourceAttribute) {
      attributeKey = (attribute as IAnalyticModelDimensionSourceAttribute).key;
    }
    return attributeKey;
  }

  protected setupVariableDetails(
    variableModel,
    newVariable = false,
    referenceDateAlreadyExists = false,
    derivable = true
  ) {
    this.uiModel.setProperty("/variableDetails", {});
    const variableInfo = this.getVariableTypeInfoFromSource(
      variableModel.key,
      variableModel.parameterType,
      variableModel.referenceAttribute
    );
    let attributeSource;
    if (variableModel.referenceAttribute) {
      const modelAttribute = this.model.getAttribute(variableModel.referenceAttribute);
      if (modelAttribute) {
        const attribute = getSourcesFromAttribute(modelAttribute, this.model);
        attributeSource = attribute?.source.dataEntity.key;
      }
    }
    const variableDetails = Object.assign({}, variableInfo, variableModel, {
      key: variableModel.key,
      referenceAttributeSource: attributeSource,
    });
    this.uiModel.setProperty("/variableDetails", variableDetails);

    if (!variableModel.variableProcessingType) {
      this.uiModel.setProperty(
        "/variableDetails/variableProcessingType",
        AnalyticModelVariableProcessingType.MANUAL_INPUT
      );
    }
    this.uiModel.setProperty("/variableDetails/text", getVariableBusinessName(variableDetails, this.model));
    if (variableDetails.parameterType === AnalyticModelParameterType.Input) {
      const valueHelpDefinitions = this.getParameterValueHelpDefinitions(variableDetails);
      variableDetails.valueHelpEntity = valueHelpDefinitions?.entityName;
      variableDetails.valueHelpColumn = valueHelpDefinitions?.elementName;
    } else if (
      variableDetails.parameterType === AnalyticModelParameterType.Filter ||
      variableDetails.parameterType === AnalyticModelParameterType.StoryFilter
    ) {
      const parameterValues = this.getParameterValuesForValueHelp();
      variableDetails.parameterValues = parameterValues?.parametersWithValues;
      variableDetails.allParametersHaveValues = parameterValues?.allParametersHaveValues;
    }
    this.updateEntityHeaderInfo(variableDetails, AnalyticModelObjectType.VARIABLE);
    this.uiModel.setProperty("/variableDetails/newVariable", newVariable);
    this.uiModel.setProperty("/referenceDateAlreadyExists", referenceDateAlreadyExists);
    this.uiModel.setProperty("/variableDetails/derivable", derivable);

    if (this.featureflags.DWCO_MODELING_SUPPORT_FISCAL_TIME) {
      const variables = this.model.getVariables();
      const variableValues = Object.values(variables);
      const referenceDateVariableExists = variableValues.some(
        (variable) => variable.parameterType === AnalyticModelParameterType.KeyDate
      );
      const fiscalVariableExists = doesFiscalVariantVariableExist(
        this.model,
        this.uiModel.getProperty("/allSourceDataEntityDetails")
      );
      const subTypes = [{ key: UIVariableSubTypes.STANDARD, text: this.getText("standardVariableType") }];
      if (!referenceDateVariableExists || variableDetails.parameterType === AnalyticModelParameterType.KeyDate) {
        subTypes.push({
          key: UIVariableSubTypes.REFERENCE_DATE,
          text: this.getText("referenceDateVariableType"),
        });
      }
      if (!fiscalVariableExists || variableDetails.parameterType === AnalyticModelParameterType.FiscalVariant) {
        subTypes.push({
          key: UIVariableSubTypes.FISCAL,
          text: this.getText("fiscalVariantVariableType"),
        });
      }

      this.uiModel.setProperty("/variableDetails/subTypes", subTypes);
      const selectedSubType = this.getVariableSubType(variableDetails.parameterType);
      this.uiModel.setProperty("/variableDetails/subType", selectedSubType);
    }
  }

  public getVariableSubType(parameterType: AnalyticModelParameterType): UIVariableSubTypes {
    switch (parameterType) {
      case AnalyticModelParameterType.KeyDate:
        return UIVariableSubTypes.REFERENCE_DATE;
      case AnalyticModelParameterType.FiscalVariant:
        return UIVariableSubTypes.FISCAL;
      case AnalyticModelParameterType.Input:
        return UIVariableSubTypes.STANDARD;
      default:
        return undefined;
    }
  }

  protected setupMeasureDetails(measureDetails, newMeasure = false, derivable = true) {
    this.uiModel.setProperty("/measureDetails/newMeasure", newMeasure);
    this.uiModel.setProperty("/measureDetails/measureType", measureDetails.measureType);
    this.uiModel.setProperty("/measureDetails/technicalName", measureDetails.technicalName);
    this.uiModel.setProperty("/measureDetails/measureBusinessName", measureDetails.text);
    this.uiModel.setProperty("/measureDetails/semanticType", measureDetails.semanticType);
    this.uiModel.setProperty("/measureDetails/unitColumn", measureDetails.unitColumn);
    this.uiModel.setProperty("/measureDetails/derivable", derivable);
    this.updateEntityHeaderInfo(measureDetails, AnalyticModelObjectType.MEASURE);
  }

  protected setupDacDetails(dacDetails: UIDac) {
    this.uiModel.setProperty("/dataAccessControlDetails", dacDetails);
    this.updateEntityHeaderInfo(dacDetails, AnalyticModelObjectType.DAC);
  }

  protected setupFilterDetails(globalFilter) {
    this.updateEntityHeaderInfo(globalFilter, AnalyticModelObjectType.FILTER);
  }

  protected setupCrossCalculationDetails(crossCalculationDetails: UICrossCalculation) {
    this.uiModel.setProperty("/crossCalculationDetails", crossCalculationDetails);
    this.updateEntityHeaderInfo(crossCalculationDetails, AnalyticModelObjectType.CROSS_CALCULATION);
  }

  /**
   * Updates breadcrumb and header for the detail page of an attribute,
   * measure or variable, based on the user selection to see technical/business name
   */
  protected updateEntityHeaderInfo(entity: any, entityType: AnalyticModelObjectType) {
    this.updateModelDisplayName();

    const links = [{ text: this.modelDisplayName, id: "model", target: undefined }];
    const entityDisplay = { businessName: "", technicalName: "" },
      headerDisplay = { businessName: "", technicalName: "" };
    let icon;
    const infoIcons = { semanticType: undefined };
    if (entityType === AnalyticModelObjectType.VARIABLE) {
      headerDisplay.businessName = headerDisplay.technicalName = this.variableTypeHeader(entity.parameterType);
      entityDisplay.technicalName = entity.key;
      entityDisplay.businessName = entity.text;
      icon = "sap-icon://sac/variable";
    }
    if (entityType === AnalyticModelObjectType.ATTRIBUTE) {
      const attributeSource = getAttributeDisplaySource(entity, this.model);
      links.push({ text: attributeSource.source.text, id: "source", target: attributeSource });
      headerDisplay.businessName = entityDisplay.businessName = entity.text;
      headerDisplay.technicalName = entityDisplay.technicalName = entity.keyOnModel;
      icon = undefined;
      this.updateHeader(this.getAttributeHeader(headerDisplay, entity.attributeType), icon, infoIcons);
      this.createAndUpdateAttributeBreadcrumbInfo(entity, this.modelDisplayName, attributeSource);
      return;
    }
    if (entityType === AnalyticModelObjectType.MEASURE) {
      const measureType = this.uiModel.getProperty("/measureDetails/measureType");
      const semanticType = this.uiModel.getProperty("/measureDetails/semanticType");
      headerDisplay.businessName = headerDisplay.technicalName = Formatter.measureTypeTextFormatter(measureType);
      entityDisplay.businessName = entity.text;
      entityDisplay.technicalName = entity.technicalName;
      icon = Formatter.measureTypeIcon(measureType);
      infoIcons.semanticType = semanticType;
    }
    if (entityType === AnalyticModelObjectType.FILTER) {
      headerDisplay.businessName = headerDisplay.technicalName = this.getText("globalFilter");
      entityDisplay.businessName = entityDisplay.technicalName = entity[0].text;
      icon = "sap-icon://sac/filter";
    }
    if (entityType === AnalyticModelObjectType.DAC) {
      headerDisplay.businessName = headerDisplay.technicalName = this.getText("dataAccessControl");
      entityDisplay.businessName = entity.text;
      entityDisplay.technicalName = entity.key;
      icon = "sap-icon://permission";
    }
    if (entityType === AnalyticModelObjectType.CROSS_CALCULATION) {
      const crossCalculationType = this.uiModel.getProperty("/crossCalculationDetails/crossCalculationType");

      headerDisplay.businessName = headerDisplay.technicalName =
        Formatter.crossCalculation.crossCalculationTypeTextFormatter(crossCalculationType);
      entityDisplay.businessName = entity.text;
      entityDisplay.technicalName = entity.technicalName;
      icon = Formatter.crossCalculation.typeIcon(crossCalculationType);
    }

    this.updateHeader(headerDisplay, icon, infoIcons, entityType);
    this.updateBreadcrumb(entityDisplay, links);
  }

  public getAttributeHeader(
    names: { businessName: string; technicalName: string },
    attributeType: AnalyticModelAttributeType
  ): { businessName: string; technicalName: string } {
    // when we stop supporting old models (without capability) the following check can be removed
    // and also the 'names' parameter can be removed, and the calls to this functions should be adapted.
    if (!this.model.getDimensionHandlingCapability()) {
      return names;
    }

    let text = this.getText("dimensionSourceAttribute");
    if (attributeType === AnalyticModelAttributeType.FactSourceAttribute) {
      text = this.getText("factSourceAttribute");
    }

    return { businessName: text, technicalName: text };
  }

  public getSourceHeader(
    names: { businessName: string; technicalName: string },
    sourceType: AnalyticModelSourceType
  ): { businessName: string; technicalName: string } {
    // when we stop supporting old models (without capability) the following check can be removed
    // and also the 'names' parameter can be removed, and the calls to this functions should be adapted.
    if (!this.model.getDimensionHandlingCapability()) {
      return names;
    }

    let text = this.getText("dimensionSource");
    if (sourceType === AnalyticModelSourceType.Fact) {
      text = this.getText("factSource");
    }

    return { businessName: text, technicalName: text };
  }

  /**
   * Updates model display name internal attribute to be used in header/breadcrumb
   */
  protected updateModelDisplayName() {
    this.modelDisplayName = {
      businessName: this.model.getBusinessName(),
      technicalName: this.model.getTechnicalName(),
    };
  }

  public variableTypeHeader(variableType: AnalyticModelParameterType): string {
    switch (variableType) {
      case AnalyticModelParameterType.Input:
        return this.getText("standardVariable");
      case AnalyticModelParameterType.Filter:
        return this.featureflags?.DWCO_MODELING_AM_MULTI_STRUCTURE
          ? this.getText("restrictionVariable")
          : this.getText("restrictedMeasureVariable");
      case AnalyticModelParameterType.StoryFilter:
        return this.getText("storyFilterVariable");
      case AnalyticModelParameterType.KeyDate:
        return this.getText("referenceDateVariable");
      case AnalyticModelParameterType.FiscalVariant:
        return this.getText("fiscalVariantVariable");
      default:
        return this.getText("formulaVariable");
    }
  }

  protected updateHeader(displayName, displayIcon, infoIcons = undefined, objectType?: AnalyticModelObjectType) {
    this.uiModel.setProperty("/header/displayName", displayName);
    this.uiModel.setProperty("/header/displayIcon", displayIcon);
    this.uiModel.setProperty("/header/semanticType", infoIcons?.semanticType);
    this.uiModel.setProperty("/header/hierarchy", infoIcons?.hierarchy);
    this.uiModel.setProperty("/header/hasTextAssociation", infoIcons?.hasTextAssociation);
    this.uiModel.setProperty("/header/hasTextElement", infoIcons?.hasTextElement);
    this.uiModel.setProperty("/header/timeDependency", infoIcons?.timeDependency);
    this.uiModel.setProperty("/header/isFiscalDimension", infoIcons?.isFiscalDimension);
    this.uiModel.setProperty("/header/objectType", objectType);
  }

  protected updateBreadcrumb(
    currentText: string | { businessName: string; technicalName: string },
    links: Array<{
      text: string | { businessName: string; technicalName: string };
      id: string;
      target?: { source: IAnalyticModelSource; sourceKey: string; type: AnalyticModelSourceType };
    }>
  ) {
    // TODO - text are not formatted to technical or business -> I would propose to not make this in a formatter function, rather define in the model a new property displayName which calls the formatter function to decide for the appropriate name
    // the ui elements binds then all to displayName without caring about what is behind
    this.uiModel.setProperty("/breadcrumbs/currentText", currentText);
    this.uiModel.setProperty("/breadcrumbs/links", links);
  }

  protected createAndUpdateAttributeBreadcrumbInfo(oObject, modelDisplayName, attributeSource) {
    const attributeSourceInformation = {
      businessName: attributeSource.source.text,
      technicalName: attributeSource.sourceKey,
    };

    const attributeNameInfo = this.model.getDimensionHandlingCapability()
      ? attributeSourceInformation
      : attributeSource.source.text;

    const attributeCurrentInfo = this.model.getDimensionHandlingCapability()
      ? { businessName: oObject.text, technicalName: oObject.keyOnModel }
      : oObject.text;

    this.updateBreadcrumb(attributeCurrentInfo, [
      { text: modelDisplayName, id: "model" },
      { text: attributeNameInfo, id: "source", target: attributeSource },
    ]);
  }

  protected createAndUpdateSourceBreadcrumbInfo(breadcrumbInfo, modelDisplayName) {
    this.model.getDimensionHandlingCapability()
      ? this.updateBreadcrumb(breadcrumbInfo, [{ text: modelDisplayName, id: "model" }])
      : this.updateBreadcrumb(breadcrumbInfo.businessName, [{ text: modelDisplayName, id: "model" }]);
  }

  public mapIPVisibilityFormatter(selectedType: AnalyticModelSourceParameterMappingType) {
    if (
      selectedType === AnalyticModelSourceParameterMappingType.MapToSourceParameter ||
      selectedType === AnalyticModelSourceParameterMappingType.Inherited
    ) {
      return true;
    }
    return false;
  }

  public enableValueHelpSourceVariable(columnName, entityName) {
    const isValueHelpSet = columnName && entityName;
    return !!isValueHelpSet;
  }

  public determineJsType(type: string | undefined): string {
    switch (type) {
      case "cds.Integer":
      case "cds.Integer64":
      case "cds.Decimal":
      case "cds.DecimalFloat":
      case "cds.Double":
      case "cds.hana.SMALLINT":
      case "cds.hana.TINYINT":
      case "cds.hana.SMALLDECIMAL":
        return "number";
      case "cds.Boolean":
        return "boolean";
      case "cds.Date":
      case "cds.Time":
      case "cds.DateTime":
      case "cds.Timestamp":
        return "date";
      default:
        return "string";
    }
  }

  public getVariableTypeInfoFromSource(
    variableKey,
    parameterType,
    referenceAttribute,
    findInheritedDataType: boolean = false
  ) {
    let variableTypeInfo;
    if (parameterType === AnalyticModelParameterType.Input) {
      const variable = this.model.getVariable(variableKey);
      const parameter = this.getDataEntityParameterForSourceVariable(variableKey);
      const currencyConversionParameter = this.getVariableDataTypeUsedInMeasure(variableKey);
      // The inherited data type either comes from the original input parameter or is created in the currency conversion measure
      // For those who still need to have a chance to find the inherited data type and not replace it with the new data type is distinguish with findInheritedDataType property
      if ((variable as IAnalyticModelSourceParameterBase).dataType && !findInheritedDataType) {
        variableTypeInfo = (variable as IAnalyticModelSourceParameterBase).dataType;
      } else if (parameter) {
        variableTypeInfo = parameter;
      } else if (currencyConversionParameter) {
        variableTypeInfo = currencyConversionParameter;
      }
    } else if (
      parameterType === AnalyticModelParameterType.Filter ||
      parameterType === AnalyticModelParameterType.StoryFilter
    ) {
      const refAttribute = this.model.getAttribute(referenceAttribute);
      if (refAttribute) {
        const { source } = getSourcesFromAttribute(refAttribute, this.model);
        const allSourceDataEntityDetails = this.uiModel.getProperty("/allSourceDataEntityDetails");
        const dimensionDetails = allSourceDataEntityDetails?.[source.dataEntity.key];
        const attributeDataEntityDetails = getAttributeDetails(refAttribute, this.model, allSourceDataEntityDetails);
        variableTypeInfo = this.getAttributeTypeInfoFromSource(attributeDataEntityDetails, dimensionDetails);
      }
    } else if (parameterType === AnalyticModelParameterType.KeyDate) {
      variableTypeInfo = { type: "cds.Date" };
    } else if (parameterType === AnalyticModelParameterType.FiscalVariant) {
      const allSourceDataEntityDetails = this.uiModel.getProperty(
        "/allSourceDataEntityDetails"
      ) as IDataEntityDetailsResponse;
      const sourceModelByName = Object.entries(allSourceDataEntityDetails).reduce((acc, [key, value]) => {
        acc[key] = value.csn;
        return acc;
      }, {} as ICsnDefinitions);
      variableTypeInfo = AnalyticModelSharedHelper.determineAnalyticModelFiscalVariableDatatypeFromModel(
        this.model.getData(),
        sourceModelByName
      );
    }
    if (!variableTypeInfo) {
      variableTypeInfo = this.getDefaultVariableType(parameterType);
    }
    return variableTypeInfo;
  }

  public getAttributeTypeInfoFromSource(attributeDataEntityDetails: IDataEntityAttribute, dataEntity: IDataEntity) {
    // remove dataEntity parameter when DWCO_MODELING_ANALYTIC_MODEL_STACKING gets removed
    let attributeWithType;
    if (this.featureflags.DWCO_MODELING_ANALYTIC_MODEL_STACKING) {
      attributeWithType = attributeDataEntityDetails;
    } else {
      const csnElements = dataEntity.csn.elements;
      const attributeFromCsn = csnElements?.[attributeDataEntityDetails?.key];
      attributeWithType = attributeFromCsn;
    }
    return attributeWithType ? { ...attributeWithType } : undefined;
  }

  public getDefaultVariableType(parameterType) {
    if (parameterType === AnalyticModelParameterType.Input) {
      return { type: "unknown", length: null, scale: null, precision: null };
    } else {
      return { type: "cds.String", length: 500, scale: null, precision: null };
    }
  }

  /**
   * Get data type details of variable that is used in currency conversion measure
   * @param variableKey
   */
  public getVariableDataTypeUsedInMeasure(variableKey) {
    const variablesUsedInMeasure: any[] = this.getVariablesIsUsedInCurrencyMeasure(variableKey);
    if (variablesUsedInMeasure && variablesUsedInMeasure.length > 0) {
      if (variablesUsedInMeasure[0].currencyType === AnalyticModelTargetCurrencyType.variable) {
        return { type: "cds.String", length: 3, scale: null, precision: null };
      }
      if (variablesUsedInMeasure[0].currencyType === AnalyticModelReferenceDateType.variable) {
        return { type: "cds.Date", length: null, scale: null, precision: null };
      }
      if (variablesUsedInMeasure[0].currencyType === AnalyticModelConversionTypeType.variable) {
        return { type: "cds.String", length: 4, scale: null, precision: null };
      }
      if (variablesUsedInMeasure[0].currencyType === AnalyticModelTargetUnitType.variable) {
        return { type: "cds.String", length: 3, scale: null, precision: null };
      }
    }
  }

  public getVariablesIsUsedInCurrencyMeasure(variableKey: any) {
    const variablesUsedInMeasure: any[] = [];
    for (const [measureKey, measure] of Object.entries(this.model.getMeasures() ?? {})) {
      if (measure.measureType === AnalyticModelMeasureType.CurrencyConversionMeasure) {
        this.dependencies.getCurrencyConversionMeasureUsingVariable(
          measure,
          measureKey,
          "",
          [],
          [variableKey],
          variablesUsedInMeasure
        );
      } else if (measure.measureType === AnalyticModelMeasureType.UnitConversionMeasure) {
        this.dependencies.getUnitConversionMeasureUsingVariable(
          measure,
          measureKey,
          "",
          [],
          [variableKey],
          variablesUsedInMeasure
        );
      }
    }
    return variablesUsedInMeasure;
  }
  /* returns the parameter that is being referenced by this source variable.
   * May be a
   *  - data entity parameter, if variable is mapped to an input parameter
   *  - lookup entity parameter, if variable is mapped to an input parameter of lookup entity
   *  - undefined, if source variable is not mapped
   */
  public getDataEntityParameterForSourceVariable(variableKey) {
    let paramFromSource;
    const allSourceDataEntityDetails: IDataEntityDetailsResponse =
      this.uiModel.getProperty("/allSourceDataEntityDetails");
    const usedInVariables = getVariablesUsingVariable(variableKey, this.model, allSourceDataEntityDetails);
    const usedIn = usedInVariables?.[0]; // always use the first used in list to get the details of value help
    if (!usedIn) {
      return paramFromSource;
    }
    if (usedIn.type === UsedInType.INPUT_PARAMETER) {
      const factSource = this.model.getFirstFactSource();
      const parameterMappings = factSource?.parameterMappings ?? {};
      const paramKeys = Object.keys(parameterMappings).filter(
        (paramKey) =>
          parameterMappings[paramKey].mappingType === AnalyticModelSourceParameterMappingType.MapToSourceParameter &&
          variableKey === (parameterMappings[paramKey] as IAnalyticModelSourceParameterMapping_VARIABLE).variableName
      );
      const paramBindingKey = paramKeys?.[0];
      const sourceDetails = allSourceDataEntityDetails[factSource?.dataEntity.key]?.parameters;
      paramFromSource = sourceDetails?.filter((item) => item.key === paramBindingKey)?.[0];
    }
    if (usedIn.type === UsedInType.VARIABLE) {
      const technicalName = usedIn.technicalName;
      const variable = this.model.getVariable(technicalName);
      if (isVariableDerivationOption(variable) && variable.lookupEntity && variable.parameterBinding) {
        // get the param binding key which is mapped to the current variable key
        const paramBindingKey = Object.keys(variable.parameterBinding ?? {}).find((paramName) => {
          const param = variable.parameterBinding[paramName];
          return (param as IAnalyticModelSourceParameterMapping_VARIABLE).variableName === variableKey;
        });
        const sourceDetails: IDataEntityParameter[] =
          this.uiModel.getProperty("/allSourceDataEntityDetails")?.[variable.lookupEntity]?.parameters;
        paramFromSource = sourceDetails?.filter((item) => item.key === paramBindingKey)?.[0];
      }
    }
    return paramFromSource;
  }

  protected async removeSourceFromQueryModel(sourceType: AnalyticModelSourceType, sourceKey?: string) {
    if (sourceType === AnalyticModelSourceType.Fact) {
      // since we're currently only supporting single facts, just clear the whole model
      const deleteFactSourceFromModelCommand = new DeleteFactSourceFromModel();
      this.stack.execute(deleteFactSourceFromModelCommand);
      this.uiModel.setProperty("/diagramSelectedSource", {});
      this.uiModel.setProperty("/selectedMeasures", []);
      this.uiModel.setProperty("/selectedParameters", []);
      this.uiModel.setProperty("/selectedDimensions", []);
      this.uiModel.setProperty("/selectedDataAccessControls", []);
      this.uiModel.setProperty("/selectedFilters", []);
      this.clearSelectedCrossCalculations();
      await Util.Component.modelChangeHandler();
    } else if (sourceType === AnalyticModelSourceType.Dimension) {
      if (sourceKey) {
        const command = new DeleteDimensionSource(sourceKey);
        this.stack.execute(command);
        await Util.Component.modelChangeHandler();
      }
    }

    // re-calculate the attributes from model
    this.setUiAttributeProperties();
  }

  /**
   * Retrieves the details from a given source in the model
   * @param source source from AM
   * @returns detailed information on the parent source
   */
  public getSourceDetails(source: IAnalyticModelSource): IDataEntity {
    const key = source?.dataEntity?.key;
    return this.uiModel.getProperty("/allSourceDataEntityDetails")[key];
  }

  /**
   * Retrieves the details from the parent source in the model
   * @param source dimension source from AM
   * @returns detailed information on the parent source
   */
  public getParentSourceDetails(source: IAnalyticModelDimensionSource): IDataEntity {
    const parentSource = this.getParentSource(source);
    return this.getSourceDetails(parentSource);
  }

  /**
   * Retrieves the parent source from a given dimension in the model
   * @param source dimension source from AM
   * @returns parent source
   */
  public getParentSource(source: IAnalyticModelDimensionSource): IAnalyticModelDimensionSource {
    let parentSource;
    if (source.associationContexts[0].sourceType === AnalyticModelSourceType.Fact) {
      parentSource = this.model.getFactSource(source.associationContexts[0].sourceKey);
    } else {
      parentSource = this.model.getDimensionSource(source.associationContexts[0].sourceKey);
    }
    return parentSource;
  }

  /**
   * Retrieves the details from foreign and target keys used in the association to a given dimension source
   * @param source dimension source from AM
   * @returns detailed information on foreign and target keys
   */

  public getAttributesUsedInAssociation(
    source: IAnalyticModelDimensionSource,
    missingForeignKey?: string,
    checkForAttributesInSourceDetails?: boolean
  ): AttributeWithKeyOnModel[] {
    if (!source.associationContexts) {
      return [];
    }

    let attributesUsedInAssociation = [];
    const parentSource = this.getParentSource(source);
    const parentSourceDetails = this.getParentSourceDetails(source);
    const parentSourceType = source?.associationContexts?.[0]?.sourceType;
    const parentSourceText = parentSource?.text;
    let foreignKeysToConsider = [];

    const associationDetails = parentSourceDetails.associations.find(
      (association) => association.key === source.associationContexts[0].associationSteps[0]
    );
    if (associationDetails) {
      const { targetKeys, foreignKeys } = associationDetails;
      // Looks for the foreing key from child dimension
      if (missingForeignKey) {
        // Finds the index of desired key from child dimension
        const keyIndex = targetKeys.indexOf(missingForeignKey);
        // Uses the name of the field on parent dimension (foreignKey)
        foreignKeysToConsider = [foreignKeys[keyIndex]];
      } else {
        foreignKeysToConsider = associationDetails.foreignKeys;
      }
    }
    let attributes: AttributeWithKeyOnModel[];
    if (checkForAttributesInSourceDetails) {
      attributes = parentSourceDetails.attributes.filter(
        (attribute) => parentSourceType === AnalyticModelSourceType.Fact || !attribute.isKey
      ) as unknown as AttributeWithKeyOnModel[];
    } else {
      const attributesOnModel = this.model.getAttributes();
      attributes = Object.keys(attributesOnModel).map((attributeKey) => ({
        keyOnModel: attributeKey,
        ...attributesOnModel[attributeKey],
      }));
    }

    for (const foreingKey of foreignKeysToConsider) {
      const attributeUsedInModel = attributes.find((attribute: any) => {
        let attributeForeingKey, attributeSourceKey;
        if (checkForAttributesInSourceDetails) {
          attributeForeingKey = attribute.key;
          return attributeForeingKey === foreingKey;
        } else {
          if (attribute.attributeType === AnalyticModelAttributeType.FactSourceAttribute) {
            attributeSourceKey = Object.keys(attribute.attributeMapping)[0];
            attributeForeingKey = attribute.attributeMapping[attributeSourceKey].key;
          } else {
            attributeForeingKey = attribute.key;
            attributeSourceKey = attribute.sourceKey;
          }
          // Found attribute using the same key and source to map to DL object
          return attributeForeingKey === foreingKey && attributeSourceKey === source.associationContexts[0].sourceKey;
        }
      });
      if (attributeUsedInModel) {
        let attributeNames = {};
        if (checkForAttributesInSourceDetails) {
          attributeNames = this.getAttributeNamesFromDataEntityAttribute(attributeUsedInModel);
        }
        attributesUsedInAssociation.push({
          ...attributeUsedInModel,
          ...attributeNames,
          parentSourceType,
          parentSourceText,
        });
      } else {
        attributesUsedInAssociation = attributesUsedInAssociation.concat(
          this.getAttributesUsedInAssociation(parentSource, foreingKey, checkForAttributesInSourceDetails)
        );
      }
    }
    return attributesUsedInAssociation;
  }

  public getAttributeNamesFromDataEntityAttribute(attributeFromDataEntity) {
    let keyOnModel = attributeFromDataEntity.key;
    let text = attributeFromDataEntity.text;
    const attributes = this.model.getAttributes();

    const attributeKey = Object.keys(attributes).find((attributeKey) => {
      const attribute = attributes[attributeKey];
      if (attribute.attributeType === AnalyticModelAttributeType.FactSourceAttribute) {
        const attributeMapping = attribute.attributeMapping;
        return attributeMapping?.[this.model.getFirstFactSourceKey()]?.key === keyOnModel;
      }
      if (attribute.attributeType === AnalyticModelAttributeType.DimensionSourceAttribute) {
        return keyOnModel === attribute.key;
      }
    });
    if (attributeKey) {
      const attribute = attributes[attributeKey];
      keyOnModel = attributeKey;
      text = attribute.text;
    }
    return { keyOnModel, text };
  }

  public getRecordTypeFields() {
    const factSourceAttributeValues = Object.values(this.uiModel.getData().attributeTree) as any;
    const recordTypeFieldList = [];
    for (const factSourceAttribute of factSourceAttributeValues) {
      if (
        factSourceAttribute.isDimension === false &&
        factSourceAttribute.attributeType === AnalyticModelAttributeType.FactSourceAttribute &&
        (factSourceAttribute.primitiveType === "cds.Integer" || factSourceAttribute.primitiveType === "cds.Integer64")
      ) {
        const recordTypeField = {
          key: factSourceAttribute.keyOnModel,
          text: factSourceAttribute.text,
        };
        recordTypeFieldList.push(recordTypeField);
      }
    }

    // add current record type field to the list if not available
    const currentRecordTypeField = this.model.getNonCumulativeSettings()?.recordTypeAttributeKey;
    if (currentRecordTypeField) {
      const keyAlreadyExists = recordTypeFieldList.some(
        (recordTypeField) => recordTypeField.key === currentRecordTypeField
      );
      if (!keyAlreadyExists) {
        const recordTypeField = {
          key: currentRecordTypeField,
          text: this.model.getAttribute(currentRecordTypeField)?.text ?? currentRecordTypeField,
        };
        recordTypeFieldList.push(recordTypeField);
      }
    }

    return recordTypeFieldList;
  }

  public getTimeDimensionFields(): TimeDimensionFields {
    const dimensionSources = this.model.getDimensionSources();
    const timeDimensionFieldList: TimeDimensionFields = [];
    for (const dimensionSourceKey in dimensionSources) {
      const dimensionSource = dimensionSources[dimensionSourceKey];
      const dimensionDetails = this.getDimensionAsAttributeDetails(dimensionSourceKey, dimensionSource);
      if (dimensionDetails?.type === "cds.Date") {
        const timeDimension: TimeDimensionField = {
          text: dimensionSource.text,
          key: dimensionDetails.key,
          sourceKey: dimensionSourceKey,
        };
        timeDimensionFieldList.push(timeDimension);
      }
    }

    // add current dimension key to the list if not available
    const currentTimeDimensionKey = this.model.getNonCumulativeSettings()?.timeDimensionKey;
    if (currentTimeDimensionKey) {
      const keyAlreadyExists = timeDimensionFieldList.some(
        (recordTypeField) => recordTypeField.sourceKey === currentTimeDimensionKey
      );
      if (!keyAlreadyExists) {
        let text = currentTimeDimensionKey;
        let key = currentTimeDimensionKey;
        if (dimensionSources[currentTimeDimensionKey]) {
          text = dimensionSources[currentTimeDimensionKey].text;
          const dimensionDetails = this.getDimensionAsAttributeDetails(
            currentTimeDimensionKey,
            dimensionSources[currentTimeDimensionKey]
          );
          if (dimensionDetails) {
            key = dimensionDetails.key;
          }
        }
        const timeDimension: TimeDimensionField = {
          text,
          key,
          sourceKey: currentTimeDimensionKey,
        };
        timeDimensionFieldList.push(timeDimension);
      }
    }

    return timeDimensionFieldList;
  }

  public updateParameterErrorProperty(selectedSource: TypedSource) {
    const uiParameters: NodeParameters = this.uiModel.getProperty("/parameters");
    let propertyPathPrefix = "";
    if (selectedSource.type === AnalyticModelSourceType.Fact) {
      propertyPathPrefix = `/sourceModel/factSources/${selectedSource.id}`;
    }
    if (!!uiParameters) {
      uiParameters.forEach((param) => {
        if (param) {
          const propertyPath = `${propertyPathPrefix}/parameterMappings/${encodeURIComponent(param.key)}`;
          // eslint-disable-next-line no-underscore-dangle
          param._validationMessageType = this.getPropertyValidation(propertyPath);
        }
      });

      this.uiModel.setProperty("/parameters", []);
      this.uiModel.setProperty("/parameters", uiParameters);
    }
  }

  public getSourceVariables(): {
    [key: string]: AllQueryParameter & IAnalyticModelSourceParameter_MANUALINPUT;
  } {
    const allVariables = this.model.getVariables();
    const sourceVariables = {};
    Object.keys(allVariables).forEach((variable) => {
      if (allVariables[variable].parameterType === AnalyticModelParameterType.Input) {
        sourceVariables[variable] = {
          ...allVariables[variable],
        };
      }
    });
    return sourceVariables;
  }

  /**
   * Called to show the correct description of Parameter in Source, whether they are mapped or have Value
   * @param parameterMappingType
   */
  public getParameterMappingDescription(parameterMappingType, selectionType: AnalyticModelVariableSelectionType) {
    let description = "";
    if (parameterMappingType?.mappingType === AnalyticModelSourceParameterMappingType.MapToSourceParameter) {
      /* Toggle business & technical ParameterMapping variableNames */
      const updatedVariableName = Formatter.uiParameterMappingNameFormatterByVariables(
        this.getSourceVariables(),
        parameterMappingType.variableName
      );
      description =
        // eslint-disable-next-line @typescript-eslint/restrict-plus-operands
        this.getText("txtMappedTo", [updatedVariableName || parameterMappingType.variableName]);
    } else if (CubeBuilderObjects.ConstantMappingTypes.includes(parameterMappingType?.mappingType)) {
      const defaultValueText = Formatter.variableDefaultValueTextFormatter(
        selectionType,
        parameterMappingType.constantValue,
        this.featureflags["DWCO_MODELING_AM_MULTI_RANGE"]
      );
      description = this.getText("txtValueWithPlaceHolder", [
        String(defaultValueText) || this.getText("txtnoValueAssigned"),
      ]);
    } else if (parameterMappingType?.mappingType === AnalyticModelSourceParameterMappingType.Inherited) {
      description = this.getText("txtInherit");
    } else {
      description = this.getText("txtnotmapped");
    }
    return description;
  }

  /**
   * Based on the validation messages, creates a map in the UIModel that can be
   * used to verify if a given attribute/filter/measure/ in the model
   * has any error or warning and highlight it in the UI
   * @param validationMessages
   */
  public getPropertyValidation(propertyPath: string) {
    const validationMap = Util.Component.validationMap;
    if (!validationMap || !propertyPath) {
      return;
    }
    const messages = validationMap.get(propertyPath);
    return messages?.highestPriorityValidation.type ?? ValidationMessageType.OK;
  }

  public getSourceValidation(sourceKey: string, type: AnalyticModelSourceType): ValidationMessage | undefined {
    if (
      type === AnalyticModelSourceType.Dimension ||
      (type === AnalyticModelSourceType.Fact &&
        this.model.getDimensionHandlingCapability() &&
        this.featureflags.DWCO_MODELING_ANALYTIC_MODEL_STACKING)
    ) {
      return getSourceValidation(sourceKey, type, Util.Component.validationMap);
    }

    return undefined;
  }

  public clearSelectedDimensionsAndAttributes() {
    const selectedDimensions: AttributeTree = this.uiModel.getProperty("/selectedDimensions");
    selectedDimensions?.forEach((node) => {
      node.selected = false;
    });
  }

  public getDefaultValueForReferenceDateVariable() {
    return (
      Object.values(this.model.getVariables()).find(
        (parameter: IAnalyticModelKeyDateVariable_MANUALINPUT) =>
          parameter.parameterType === AnalyticModelParameterType.KeyDate &&
          (!parameter.variableProcessingType ||
            parameter.variableProcessingType === AnalyticModelVariableProcessingType.MANUAL_INPUT) &&
          parameter.defaultValue
      ) as IAnalyticModelKeyDateVariable_MANUALINPUT
    )?.defaultValue;
  }

  public getParameterValueHelpDefinitions(parameter: AllQueryParameter) {
    if (parameter.parameterType !== AnalyticModelParameterType.Input) {
      return undefined;
    }

    // assumes only one fact source
    const factSource = this.model.getFirstFactSource();
    const parameterMappings = factSource?.parameterMappings ?? {};
    const paramKeys = Object.keys(parameterMappings).filter(
      (paramKey) =>
        parameterMappings[paramKey].mappingType === AnalyticModelSourceParameterMappingType.MapToSourceParameter &&
        (parameter as any).key ===
          (parameterMappings[paramKey] as IAnalyticModelSourceParameterMapping_VARIABLE).variableName
    );
    if (paramKeys?.length !== 1) {
      return undefined;
    }
    const paramKey = paramKeys[0];

    const dataEntityKey = factSource.dataEntity.key;
    const allSourceDetails: IDataEntityDetailsResponse = this.uiModel.getProperty("/allSourceDataEntityDetails");
    const paramsFromSource = allSourceDetails?.[dataEntityKey]?.parameters?.filter((param) => param.key === paramKey);
    if (paramsFromSource?.length !== 1) {
      return undefined;
    }
    const paramFromSource = paramsFromSource[0];
    const valueHelpDefinitionFromCsn = paramFromSource[CsnAnnotations.Consumption.valueHelpDefinition]?.[0]?.entity;
    const valueHelpDefinition = valueHelpDefinitionFromCsn
      ? {
          entityName: valueHelpDefinitionFromCsn.name,
          elementName: valueHelpDefinitionFromCsn.element,
        }
      : undefined;
    return valueHelpDefinition;
  }
  /**
   * Retrieves the parameter values required for value help functionality.
   *
   * This method is essential for value help to work, as it first fetches the underlying model
   * and extracts parameter values based on their mapping types. It checks for constant values,
   * mapped source parameters, and handles stacked models if the relevant feature flag is enabled.
   *
   * @returns An object containing:
   *   - `allParametersHaveValues`: A boolean indicating if all required parameters have values set.
   *   - `parametersWithValues`: A mapping of parameter keys to their resolved values.
   */
  public getParameterValuesForValueHelp() {
    // assumes only one fact source
    const factSource = this.model.getFirstFactSource();
    const parameterMappings = factSource?.parameterMappings ?? {};
    const parametersWithValues = {};
    let allParametersHaveValues = true;
    Object.keys(parameterMappings).forEach((parameterKey) => {
      const parameter = parameterMappings[parameterKey];
      if (CubeBuilderObjects.ConstantMappingTypes.includes(parameter.mappingType)) {
        if ("constantValue" in parameter) {
          parametersWithValues[parameterKey] = parameter.constantValue;
        }
      } else if (parameter.mappingType === AnalyticModelSourceParameterMappingType.MapToSourceParameter) {
        // MapToSourceParameter, requires checking AM variables for value
        const variableName = parameter.variableName;
        const variable = this.model.getVariable(variableName);

        if (isVariableDerivationOption(variable) && variable.lookupEntity && variable.parameterBinding) {
          // for lookup variables, we need to check the parameterBinding and set the value for the source input parameter
          // get the param binding key which is mapped to the current variable key and call the function again to retrieve the value
          allParametersHaveValues = this.extractConstantValuesFromParameterMappings(
            variable.parameterBinding ?? {},
            parametersWithValues,
            allParametersHaveValues,
            parameterKey
          );
        } else {
          const defaultValue = variable?.hasOwnProperty("defaultValue");
          if (defaultValue) {
            parametersWithValues[parameterKey] = (variable as IAnalyticModelVariableManualInput).defaultValue;
          } else {
            allParametersHaveValues = false;
          }
        }
      }
    });
    if (this.featureflags.DWCO_MODELING_ANALYTIC_MODEL_STACKING && this.isStackedModel()) {
      const stackedVariables: UIStackedVariable[] = this.stackedVariable.getStackedVariables();
      allParametersHaveValues =
        allParametersHaveValues && this.doesStackedModelFactHaveAllParameterValuesSet(stackedVariables);
    }

    return { allParametersHaveValues, parametersWithValues };
  }

  private extractConstantValuesFromParameterMappings(
    parameterMappings: {
      [parameterName: string]: CONSTANT_VALUE_TYPES | IAnalyticModelSourceParameterMapping_VARIABLE;
    },
    parametersWithValues: {},
    allParametersHaveValues: boolean,
    sourceInputParameterKey?: string
  ): boolean {
    Object.keys(parameterMappings).forEach((parameterKey) => {
      const parameter = parameterMappings[parameterKey];
      if (CubeBuilderObjects.ConstantMappingTypes.includes(parameter.mappingType)) {
        if ("constantValue" in parameter) {
          parametersWithValues[sourceInputParameterKey] = parameter.constantValue;
        }
      } else if (parameter.mappingType === AnalyticModelSourceParameterMappingType.MapToSourceParameter) {
        // MapToSourceParameter, requires checking AM variables for value
        const variableName = parameter.variableName;
        const variable = this.model.getVariable(variableName);

        if (isVariableDerivationOption(variable) && variable.lookupEntity && variable.parameterBinding) {
          // for lookup variables, we need to check the parameterBinding and set the value for the source input parameter
          // get the param binding key which is mapped to the current variable key and call the function again to retrieve the value
          allParametersHaveValues = this.extractConstantValuesFromParameterMappings(
            variable.parameterBinding,
            parametersWithValues,
            allParametersHaveValues,
            sourceInputParameterKey
          );
        } else {
          const defaultValue = variable?.hasOwnProperty("defaultValue");
          if (defaultValue) {
            parametersWithValues[sourceInputParameterKey] = (
              variable as IAnalyticModelVariableManualInput
            ).defaultValue;
          } else {
            allParametersHaveValues = false;
          }
        }
      }
    });
    return allParametersHaveValues;
  }

  public doesStackedModelFactHaveAllParameterValuesSet(
    stackedVariables: UIStackedVariable[] | IDataEntityParameter[]
  ): boolean {
    for (const variable of stackedVariables) {
      if (variable.variableProcessingType === AnalyticModelVariableProcessingType.LOOKUP) {
        continue;
      } else if (variable.variableProcessingType === AnalyticModelVariableProcessingType.DYNAMIC_DEFAULT) {
        // Blocked to having value help for Dynamic variables derive their value from a lookup entity as odata needs all variables
        // should support this case with backlog DW15-5619
        return false;
      }
      if (variable.mandatory === false) {
        continue;
      }
      const defaultValue = (variable as UIStackedVariable).defaultValue ?? (variable as IDataEntityParameter).default;
      let valuesArray:
        | AnalyticModelParameterValueBaseType[]
        | IAnalyticModelDefaultInterval[]
        | IAnalyticModelDefaultRange[];
      if (variable.multipleSelections) {
        valuesArray = defaultValue as
          | AnalyticModelParameterValueBaseType[]
          | IAnalyticModelDefaultInterval[]
          | IAnalyticModelDefaultRange[];
      } else {
        valuesArray = [defaultValue] as
          | AnalyticModelParameterValueBaseType[]
          | IAnalyticModelDefaultInterval[]
          | IAnalyticModelDefaultRange[];
      }
      if (valuesArray.length === 0) {
        return false;
      }

      const areAllValuesValid = this.areAllValuesValid(valuesArray, variable);
      if (!areAllValuesValid) {
        return false;
      }
    }
    return true;
  }

  /**
   * validates if all values in the provided array are valid based on the variable's selection type.
   * For SINGLE selection type, it checks if the value is defined and not empty.
   * For RANGE selection type, it checks if the low and high values are valid based on the option.
   * For INTERVAL selection type, it checks if either low or high value is valid.
   * @param valuesArray
   * @param variable
   * @returns
   */
  private areAllValuesValid(
    valuesArray: AnalyticModelParameterValueBaseType[] | IAnalyticModelDefaultRange[] | IAnalyticModelDefaultInterval[],
    variable: UIStackedVariable | IDataEntityParameter
  ) {
    return valuesArray.every((value) => {
      const isValueValid = (value) => value !== undefined && value !== "";
      if (variable.selectionType === AnalyticModelVariableSelectionType.SINGLE) {
        return isValueValid(value);
      } else if (variable.selectionType === AnalyticModelVariableSelectionType.RANGE) {
        const defaultValueRange = value as IAnalyticModelDefaultRange;
        if (!defaultValueRange) {
          return false;
        }
        const isLowValueValid = isValueValid(defaultValueRange.lowValue);
        const isHighValueValid = isValueValid(defaultValueRange.highValue);
        const isBetweenOption = defaultValueRange.option === AnalyticModelDefaultRangeOption.BT;
        const hasHighValue = defaultValueRange.hasOwnProperty("highValue");
        // For "Between" option, if Hight is present, both low and high values must be valid
        if (isBetweenOption) {
          if (hasHighValue) {
            return isLowValueValid && isHighValueValid;
          }
          return isLowValueValid;
        }
        // For options without highValue, only low value is required
        return isLowValueValid;
      } else if (variable.selectionType === AnalyticModelVariableSelectionType.INTERVAL) {
        const defaultValueInterval = value as IAnalyticModelDefaultInterval;
        return (
          defaultValueInterval &&
          (isValueValid(defaultValueInterval.lowValue) || isValueValid(defaultValueInterval.highValue))
        );
      }
      return true;
    });
  }

  public setWholeComponentBusy(busy: boolean, noDelay = false) {
    const graphSplitView = Util.Component.getWorkbench()["oGraphSplitView"] as unknown as GraphSplitView;
    if (noDelay) {
      graphSplitView.getParent().setBusyIndicatorDelay(0);
      graphSplitView.setBusyIndicatorDelay(0);
    }
    graphSplitView.getParent().setBusy(busy);
    graphSplitView.setBusy(busy);
    if (!busy) {
      // reset delay
      graphSplitView.getParent().setBusyIndicatorDelay(1000);
      graphSplitView.setBusyIndicatorDelay(1000);
    }
  }

  public getPropertyLink(propertyType: string, propertyTechnicalName: string, propertyDisplayName: string): string {
    const link = Util.Component.router.createLinkForModelProperty(propertyType, propertyTechnicalName);
    return `<a target="_self" href=${link}>${propertyDisplayName}</a>`;
  }

  /**
   * Returns the display name from an attribute with corresponding source
   * Examples: AttributeName1 (Fact Sources) / AttributeName2 (Dimension Name)
   * @param attributeKey
   */
  public getAttributeDisplayNameWithSource(attributeKey: string) {
    const attribute = this.model.getAttribute(attributeKey);
    const displayNameConfig = User.getInstance().getObjectNameDisplay();
    const attributeDisplayName = displayNameConfig === ObjectNameDisplay.technicalName ? attributeKey : attribute.text;
    const attributeSource = getSourcesFromAttribute(attribute, this.model);
    const dimensionSourceDisplayName = this.getSourceDisplayName(attributeSource.sourceKey, attributeSource.source);
    if (!attributeDisplayName || !dimensionSourceDisplayName) {
      return "";
    }
    return `${attributeDisplayName} (${dimensionSourceDisplayName})`;
  }

  public goToProperty(propertyType: AnalyticModelObjectType, propertyName: string, replaceHash: boolean = false) {
    const route = CubeBuilderObjects.ObjectTypeToModelRoute[propertyType];
    if (!route) {
      Util.Component.router.navToModelPanel(replaceHash);
      return;
    }

    switch (route) {
      case ModelRoutes.measure:
        Util.Component.router.navToMeasurePanel(propertyName, replaceHash);
        break;
      case ModelRoutes.attribute:
        Util.Component.router.navToAttributePanel(propertyName, replaceHash);
        break;
      case ModelRoutes.variable:
        Util.Component.router.navToVariablePanel(propertyName, replaceHash);
        break;
      case ModelRoutes.filter:
        Util.Component.router.navToGlobalFilterPanel();
        break;
      case ModelRoutes.dimension:
        Util.Component.router.navToDimensionSourcePanel(propertyName, replaceHash);
        break;
      case ModelRoutes.fact:
        Util.Component.router.navToFactSourcePanel(propertyName, replaceHash);
        break;
      case ModelRoutes.dac:
        Util.Component.router.navToDataAccessControlPanel(propertyName);
        break;
      case ModelRoutes.crossCalculation:
        Util.Component.router.navToCrossCalculationPanel(propertyName);
        break;
      default:
        Util.Component.router.navToModelPanel(replaceHash);
        break;
    }
  }

  public goToUsedInProperty(type: UsedInType, technicalName: string | undefined) {
    const objectType = CubeBuilderObjects.UsedInTypeToObjectType[type];
    if (objectType === AnalyticModelObjectType.FACT) {
      technicalName = this.model.getFirstFactSourceKey();
    }
    this.goToProperty(objectType, technicalName);
  }

  public getSourceDisplayName(sourceKey: string, source: IAnalyticModelSource): string {
    const showTechnicalNamesForSources =
      User.getInstance().getObjectNameDisplay() === ObjectNameDisplay.technicalName &&
      this.model.getDimensionHandlingCapability();
    return showTechnicalNamesForSources ? sourceKey : source.text;
  }

  public enableNewNameChecks() {
    return enableNewNameChecks(this.model);
  }

  public isStackedModel() {
    return this.uiModel.getProperty("/isStackedModel");
  }

  /**
   * Returns a value help for the manual input of variables
   * @param variableKey
   */
  public getVariableValueHelp(variableKey) {
    const parameter = this.getDataEntityParameterForSourceVariable(variableKey);
    const valueHelp = parameter?.[CsnAnnotations.Consumption.valueHelpDefinition]?.[0]?.entity;
    if (valueHelp) {
      return {
        technicalName: `${valueHelp.element} (${valueHelp.name})`,
        businessName: `${valueHelp.elementText} (${valueHelp.text})`,
      };
    }
    return undefined;
  }

  public getExpandableTextForAttributeList(attributes: string[]) {
    let index = 0;
    let attributesListText = "";
    let maxCharacters = 200;
    if (!attributes) {
      return { attributesListText: attributesListText, maxCharacters: maxCharacters };
    }
    for (const attribute of attributes) {
      index++;
      const modelAttribute = this.model.getAttribute(attribute);
      let attributeSourceName = "";
      if (modelAttribute) {
        const attributeSource = getSourcesFromAttribute(modelAttribute, this.model);
        const displayTechnicalName =
          User.getInstance().getObjectNameDisplay() === (ObjectNameDisplay.technicalName as string);
        let attributeName = attribute;
        attributeSourceName = attributeSource?.source.text;
        if (this.model.getDimensionHandlingCapability()) {
          if (displayTechnicalName) {
            attributeSourceName = attributeSource?.sourceKey;
          } else {
            attributeName = modelAttribute.text;
          }
        }

        attributesListText = attributesListText + attributeName + " (" + attributeSourceName + ")\n";
        if (index === 2 && attributes.length > 2) {
          maxCharacters = attributesListText.length;
        }
      }
    }
    return { attributesListText: attributesListText, maxCharacters: maxCharacters };
  }

  public getConversionNames(currency: any, currencyType: any) {
    let currencyBusinessName = "";
    let currencyTechnicalName = "";
    switch (currencyType) {
      case AnalyticModelTargetCurrencyType.constantValue:
      case AnalyticModelTargetUnitType.constantValue:
      case AnalyticModelConversionTypeType.constantValue:
        currencyTechnicalName = (currency as IAnalyticModelConstantValue).value;
        currencyBusinessName = (currency as IAnalyticModelConstantValue).value;
        return { currencyBusinessName: currencyBusinessName, currencyTechnicalName: currencyTechnicalName };
      case AnalyticModelReferenceDateType.constantValue:
        const fixedDate = (currency as IAnalyticModelConstantValue).value;
        const fixedReferenceDateUtc =
          fixedDate?.substring(0, 4) + "-" + fixedDate?.substring(4, 6) + "-" + fixedDate?.substring(6, 8);
        const referenceDateText = fixedReferenceDateUtc
          ? fixedReferenceDateUtc === "--"
            ? fixedReferenceDateUtc
            : Format.toLocalDate(fixedReferenceDateUtc)
          : fixedDate;
        return { currencyBusinessName: referenceDateText, currencyTechnicalName: referenceDateText };
      case AnalyticModelTargetCurrencyType.attribute:
      case AnalyticModelTargetUnitType.attribute:
      case AnalyticModelReferenceDateType.attribute:
      case AnalyticModelConversionTypeType.attribute:
        const attributes = this.model.getAttributes();
        const attributeKey = (currency as IAnalyticModelAttributeKey).key;
        const currencyAttribute = attributes[attributeKey];
        if (!!currencyAttribute) {
          const { source, sourceKey } = getSourcesFromAttribute(currencyAttribute, this.model);
          const sourceName = source.text;
          const sourceTechnicalName = this.model.getDimensionHandlingCapability() ? sourceKey : source.text;
          currencyTechnicalName = `${attributeKey} (${sourceTechnicalName})`;
          currencyBusinessName = `${currencyAttribute.text} (${sourceName})`;
        } else {
          currencyTechnicalName = attributeKey;
          currencyBusinessName = attributeKey;
        }
        return { currencyBusinessName: currencyBusinessName, currencyTechnicalName: currencyTechnicalName };
      case AnalyticModelTargetCurrencyType.variable:
      case AnalyticModelTargetUnitType.variable:
      case AnalyticModelReferenceDateType.variable:
      case AnalyticModelConversionTypeType.variable:
        const variables = this.model.getVariables();
        const variableKey = (currency as IAnalyticModelVariableKey).key;
        currencyTechnicalName = variableKey;
        const currencyVariable = variables[variableKey];
        if (!!currencyVariable) {
          currencyBusinessName = (currencyVariable as any).text;
        }
        return { currencyBusinessName: currencyBusinessName, currencyTechnicalName: currencyTechnicalName };
      case AnalyticModelReferenceDateType.sqlFunction:
        currencyTechnicalName = (currency as IAnalyticModelSqlFunctionName).functionName;
        currencyBusinessName = this.getText("ccTypesCurrentDate");
        return { currencyBusinessName: currencyBusinessName, currencyTechnicalName: currencyTechnicalName };
    }
    return { currencyBusinessName: currencyBusinessName, currencyTechnicalName: currencyTechnicalName };
  }

  public getFormattedUsedIn(usedIn: UsedInItem[]): string[] {
    if (usedIn?.length === 0) {
      return ["-"];
    }

    return this.getFormattedUsedInWithLinks(usedIn);
  }

  private getFormattedUsedInWithLinks(usedIn: UsedInItem[]): string[] {
    const usedInFormatted: string[] = usedIn.map((element) => {
      const displayText = Formatter.usedInAttributesPopoverFormatter(
        element.technicalName,
        element.businessName,
        element.fullName,
        element.type,
        element.subType,
        this.featureflags?.DWCO_MODELING_AM_MULTI_STRUCTURE
      );
      const objectType = CubeBuilderObjects.UsedInTypeToObjectType[element.type];
      if (!objectType) {
        return displayText;
      }
      let technicalName = element.technicalName;
      if (objectType === AnalyticModelObjectType.FACT) {
        technicalName = this.model.getFirstFactSourceKey();
      }
      const modelRoute = CubeBuilderObjects.ObjectTypeToModelRoute[objectType];
      return this.getPropertyLink(modelRoute, technicalName, displayText);
    });
    return usedInFormatted;
  }

  public getDimensionDataType(dimensionSourceId: string, dimensionSource: IAnalyticModelDimensionSource) {
    const parentSource = this.getParentSourceDetails(dimensionSource);
    const dimensionAsAttributeDetails = this.getDimensionAsAttributeDetails(dimensionSourceId, dimensionSource);
    if (!dimensionAsAttributeDetails) {
      return this.getText("noDataType");
    }
    return getDisplayDataType(this.getAttributeTypeInfoFromSource(dimensionAsAttributeDetails, parentSource));
  }

  /** Functions to handle FilterType change and event changes related to "Set Value" Range/Interval for stacked variables */
  public getSelectedObjectInTakeOverDialog(source: sap.ui.core.Control) {
    const index = source.getBindingContext("takeOverDialogModel").getPath().split("/")[2];
    return this.takeOverDialog.getModel("takeOverDialogModel").getData().parameters[index];
  }

  public onChangeParameterFilterType(event: sap.ui.base.Event) {
    const newValue: string = event.getParameters().selectedItem.mProperties.key;
    const { selectedObject, model }: { selectedObject: any; model: sap.ui.model.json.JSONModel } =
      this.getParameterDialogModel(event);
    this.stackedVariable.updateStackedVariableFilterType(newValue, selectedObject);
    this.stackedVariable.updateSelectedObjectValue(selectedObject, model);
  }

  public onChangeOptionForMultipleRangeStackedVariable(event: sap.ui.base.Event) {
    const { selectedObject, model }: { selectedObject: any; model: sap.ui.model.json.JSONModel } =
      this.getParameterDialogModel(event);
    this.stackedVariable.updateSelectedObjectValue(selectedObject, model);
  }

  public onChangeOptionForIntervalVariableStackedVariable(event: sap.ui.base.Event) {
    const { selectedObject, model }: { selectedObject: any; model: sap.ui.model.json.JSONModel } =
      this.getParameterDialogModel(event);
    this.stackedVariable.updateSelectedObjectValue(selectedObject, model);
  }

  public onChangeStackedVariableMultipleRangeDefaultValue(event: sap.ui.base.Event) {
    const { selectedObject, model }: { selectedObject: any; model: sap.ui.model.json.JSONModel } =
      this.getParameterDialogModel(event);
    const { newValue, index, low } = event.getParameters();
    const defaultValueRanges: DefaultValueRange[] = selectedObject.defaultValuesRange;
    if (low) {
      defaultValueRanges[index].lowValue = newValue;
    } else {
      defaultValueRanges[index].highValue = newValue;
    }
    this.stackedVariable.updateSelectedObjectValue(selectedObject, model);
  }

  public onChangeStackedVariableIntervalDefaultValue(event: sap.ui.base.Event) {
    const { selectedObject, model }: { selectedObject: any; model: sap.ui.model.json.JSONModel } =
      this.getParameterDialogModel(event);
    const { newValue, low } = event.getParameters();
    const defaultValueInterval: DefaultValueInterval = selectedObject.defaultValuesInterval;
    if (low) {
      defaultValueInterval.lowValue = newValue;
    } else {
      defaultValueInterval.highValue = newValue;
    }
    this.stackedVariable.updateSelectedObjectValue(selectedObject, model);
  }

  /**
   * This function is used to get the selected object and model for the parameter dialog either for TakeOverDialog or ParameterDefaultValueDialog
   * @param event
   * @returns
   */
  private getParameterDialogModel(event: sap.ui.base.Event) {
    const oSource = event.getSource();
    const isCopyPropertyDialog = oSource.getId().includes("TakeOverDialog");
    let selectedObject;
    let model: sap.ui.model.json.JSONModel;
    let isLookUpParameters =
      false; /** Indicates if the function is called while Lookup Entity Parameter Mapping of variable form output list */
    if (isCopyPropertyDialog) {
      selectedObject = this.getSelectedObjectInTakeOverDialog(oSource);
      model = this.takeOverDialog.getModel("takeOverDialogModel") as sap.ui.model.json.JSONModel;
    } else {
      isLookUpParameters = this.parameterDefaultValueDialog.getId() === "lookUpEntity--analyticalModelDefaultValDialog";
      selectedObject = this.parameterDefaultValueDialog.getModel().getData();
      model = this.parameterDefaultValueDialog.getModel() as sap.ui.model.json.JSONModel;
    }
    return { selectedObject, model, isLookUpParameters };
  }

  public onParameterAddDefaultValueRange(event: sap.ui.base.Event) {
    const { selectedObject, model }: { selectedObject: any; model: sap.ui.model.json.JSONModel } =
      this.getParameterDialogModel(event);
    const defaultValuesRange: DefaultValueRange[] = cloneDeep(selectedObject.defaultValuesRange);
    defaultValuesRange.push({
      option: AnalyticModelDefaultRangeOption.EQ,
      lowValue: "",
      valueStateText: "",
    });
    selectedObject.defaultValuesRange = defaultValuesRange;
    this.stackedVariable.updateSelectedObjectValue(selectedObject, model);
  }

  public onParameterDeleteDefaultValueRange(event: sap.ui.base.Event) {
    const index = event.getParameter("index");
    const { selectedObject, model }: { selectedObject: any; model: sap.ui.model.json.JSONModel } =
      this.getParameterDialogModel(event);
    const defaultValuesRange: DefaultValueRange[] = cloneDeep(selectedObject.defaultValuesRange);
    defaultValuesRange.splice(index, 1);
    selectedObject.defaultValuesRange = defaultValuesRange;
    this.stackedVariable.updateSelectedObjectValue(selectedObject, model);
  }

  public onParameterDeleteDefaultValue(event: sap.ui.base.Event) {
    const index = event.getParameter("index");
    const { selectedObject, model }: { selectedObject: any; model: sap.ui.model.json.JSONModel } =
      this.getParameterDialogModel(event);
    const defaultValuesWithState: MultiInputItem[] = cloneDeep(selectedObject.defaultValuesWithState);
    defaultValuesWithState.splice(index, 1);
    selectedObject.defaultValuesWithState = defaultValuesWithState;
    this.stackedVariable.updateSelectedObjectValue(selectedObject, model);
  }

  public onParameterAddDefaultValue(event: sap.ui.base.Event) {
    const { selectedObject, model }: { selectedObject: any; model: sap.ui.model.json.JSONModel } =
      this.getParameterDialogModel(event);
    const defaultValuesWithState: MultiInputItem[] = cloneDeep(selectedObject.defaultValuesWithState);
    defaultValuesWithState.push({
      value: "",
      valueStateText: "",
    });
    selectedObject.defaultValuesWithState = defaultValuesWithState;
    this.stackedVariable.updateSelectedObjectValue(selectedObject, model);
  }

  public onParameterDefaultValueChange(event: sap.ui.base.Event) {
    const { selectedObject, model, isLookUpParameters } = this.getParameterDialogModel(event);
    const value =
      event.getParameters().mParameters.selectedItem?.mProperties.key ?? event.getParameters().mParameters.newValue;
    selectedObject.value =
      this.isStackedModel() && this.featureflags.DWCO_MODELING_AM_SET_VALUE_STACKING_VARIABLE
        ? this.valueDefinitionUtils.computeDefaultValueForVariable(selectedObject, value) ?? selectedObject.defaultValue
        : value;
    selectedObject.targetParameter = undefined;
    // Validate the enablement of the OK button in the Set Value dialog by verifying the default value for each parameter type
    selectedObject.enableSetParameterDefaultValue =
      !isLookUpParameters && this.isStackedModel() && this.featureflags.DWCO_MODELING_AM_SET_VALUE_STACKING_VARIABLE
        ? this.stackedVariable.enablementOfSetValueButton(selectedObject, selectedObject.value)
        : !!selectedObject.value;
    model.updateBindings(true);
  }

  private onParameterValueHelpRequest(
    dialogModel: sap.ui.model.Model,
    bindingPath: string,
    takeOverParameterPath: string,
    oldValue: string,
    callback: GenericCallback,
    isParameterSingleInput: boolean = false
  ) {
    const valueHelpDialog = new ValueHelpDialog() as ValueHelpDialogAM;
    const parameterType = dialogModel.getProperty(
      takeOverParameterPath ? `${takeOverParameterPath}/parameterType` : "/parameterType"
    );
    let columnName: string;
    let entityName: string;
    if (
      parameterType === AnalyticModelParameterType.Filter ||
      parameterType === AnalyticModelParameterType.StoryFilter
    ) {
      const referenceAttribute = dialogModel.getProperty(
        takeOverParameterPath ? `${takeOverParameterPath}/referenceAttribute` : "/referenceAttribute"
      );
      entityName = dialogModel.getProperty(
        takeOverParameterPath ? `${takeOverParameterPath}/referenceAttributeSource` : "/referenceAttributeSource"
      );
      columnName =
        this.getAttributeKey(referenceAttribute, this.model.getAttribute(referenceAttribute)) || referenceAttribute;
    }
    if (parameterType === AnalyticModelParameterType.Input || (!parameterType && isParameterSingleInput)) {
      entityName = dialogModel.getProperty(
        takeOverParameterPath ? `${takeOverParameterPath}/valueHelpSource` : "/valueHelpSource"
      );
      columnName = dialogModel.getProperty(
        takeOverParameterPath ? `${takeOverParameterPath}/valueHelpColumn` : "/valueHelpColumn"
      );
    }

    valueHelpDialog.setProperty("columnName", columnName);
    valueHelpDialog.setProperty("entityName", entityName);
    valueHelpDialog.setProperty(
      "dataType",
      dialogModel.getProperty(takeOverParameterPath ? `${takeOverParameterPath}/type` : "/type")
    );
    valueHelpDialog.setProperty("selectedValue", oldValue);
    valueHelpDialog.setProperty("timeDependencyDate", this.getDefaultValueForReferenceDateVariable());
    valueHelpDialog.attachOnSubmitSelection(async (submitEvent: IEvent<ValueHelpDialogAM, any>) => {
      const selectedValue = submitEvent.getParameters().values;
      const value = selectedValue.length === 1 ? selectedValue[0].value : "";
      dialogModel.setProperty(takeOverParameterPath ? `${takeOverParameterPath}/${bindingPath}` : bindingPath, value);
      submitEvent.getSource().close();
      if (callback) {
        callback();
      }
    });
    valueHelpDialog.open();
    recordUsageTrackingEvent({
      action: USAGE_ACTIONS.OPEN_VALUE_HELP,
      feature: getCurrentFeatureForUsageTracking(),
      eventtype: EventType.CLICK,
    });
  }

  public onSingleInputValueHelpRequest(requestEvent: sap.ui.base.Event) {
    const { selectedObject, model } = this.getParameterDialogModel(requestEvent);
    let takeOverParameterPath;
    if (requestEvent.getSource().getId().includes("TakeOverDialog")) {
      takeOverParameterPath = requestEvent.getSource().getBindingContext("takeOverDialogModel").getPath();
    }
    const binding = requestEvent.getSource().getBindingPath("value");
    const fullPath = takeOverParameterPath ? `${takeOverParameterPath}/${binding}` : binding;
    this.onParameterValueHelpRequest(
      model,
      binding,
      takeOverParameterPath,
      String(model.getProperty(`${fullPath}`)),
      () => {
        const defaultValue = takeOverParameterPath
          ? model.getProperty(`${takeOverParameterPath}/value`)
          : model.getProperty("/value");
        model.setProperty(takeOverParameterPath ? `${takeOverParameterPath}/value` : "/value", []);
        model.setProperty(takeOverParameterPath ? `${takeOverParameterPath}/value` : "/value", defaultValue);
        // Validate the enablement of the OK button in the Set Value dialog by verifying the default value
        selectedObject.enableSetParameterDefaultValue = !!selectedObject.value;
        model.updateBindings(true);
      },
      true
    );
  }

  public onParameterDefaultValueHelpRequestRange(requestEvent: sap.ui.base.Event) {
    const { selectedObject, model } = this.getParameterDialogModel(requestEvent);
    let takeOverParameterPath;
    if (requestEvent.getSource().getId().includes("TakeOverDialog")) {
      takeOverParameterPath = requestEvent.getSource().getBindingContext("takeOverDialogModel").getPath();
    }
    const binding = requestEvent.getParameters().path;
    const fullPath = takeOverParameterPath ? `${takeOverParameterPath}/${binding}` : binding;
    this.onParameterValueHelpRequest(
      model,
      binding,
      takeOverParameterPath,
      String(model.getProperty(`${fullPath}`)),
      () => {
        const defaultValues = takeOverParameterPath
          ? model.getProperty(`${takeOverParameterPath}/defaultValuesRange`)
          : model.getProperty("/defaultValuesRange");
        model.setProperty(
          takeOverParameterPath ? `${takeOverParameterPath}/defaultValuesRange` : "/defaultValuesRange",
          []
        );
        model.setProperty(
          takeOverParameterPath ? `${takeOverParameterPath}/defaultValuesRange` : "/defaultValuesRange",
          defaultValues
        );
        this.stackedVariable.updateSelectedObjectValue(selectedObject, model);
      }
    );
  }

  public onParameterDefaultValueHelpRequestInterval(requestEvent: sap.ui.base.Event) {
    const { selectedObject, model } = this.getParameterDialogModel(requestEvent);
    let takeOverParameterPath;
    if (requestEvent.getSource().getId().includes("TakeOverDialog")) {
      takeOverParameterPath = requestEvent.getSource().getBindingContext("takeOverDialogModel").getPath();
    }
    const binding = requestEvent.getParameters().path;
    const fullPath = takeOverParameterPath ? `${takeOverParameterPath}/${binding}` : binding;
    this.onParameterValueHelpRequest(
      model,
      binding,
      takeOverParameterPath,
      String(model.getProperty(`${fullPath}`)),
      () => {
        const defaultValues = takeOverParameterPath
          ? model.getProperty(`${takeOverParameterPath}/defaultValuesInterval`)
          : model.getProperty("/defaultValuesInterval");
        model.setProperty(
          takeOverParameterPath ? `${takeOverParameterPath}/defaultValuesInterval` : "/defaultValuesInterval",
          []
        );
        model.setProperty(
          takeOverParameterPath ? `${takeOverParameterPath}/defaultValuesInterval` : "/defaultValuesInterval",
          defaultValues
        );
        this.stackedVariable.updateSelectedObjectValue(selectedObject, model);
      }
    );
  }

  public onParameterMultiInputValueHelpRequest(requestEvent: sap.ui.base.Event) {
    const { selectedObject, model } = this.getParameterDialogModel(requestEvent);
    const { index } = requestEvent.getParameters();
    let takeOverParameterPath = "";
    if (requestEvent.getSource().getId().includes("TakeOverDialog")) {
      takeOverParameterPath = requestEvent.getSource().getBindingContext("takeOverDialogModel").getPath();
    }
    const bindingPath = `defaultValuesWithState/${index}/value`;
    const fullPath = takeOverParameterPath ? `${takeOverParameterPath}/${bindingPath}` : bindingPath;
    this.onParameterValueHelpRequest(
      model,
      bindingPath,
      takeOverParameterPath,
      String(model.getProperty(`${fullPath}`)),
      () => {
        const defaultValues = takeOverParameterPath
          ? model.getProperty(`${takeOverParameterPath}/defaultValuesWithState`)
          : model.getProperty("/defaultValuesWithState");
        model.setProperty(
          takeOverParameterPath ? `${takeOverParameterPath}/defaultValuesWithState` : "/defaultValuesWithState",
          []
        );
        model.setProperty(
          takeOverParameterPath ? `${takeOverParameterPath}/defaultValuesWithState` : "/defaultValuesWithState",
          defaultValues
        );
        this.stackedVariable.updateSelectedObjectValue(selectedObject, model);
      }
    );
  }
  /** END of functions */

  // #region Cross Calculation
  /**
   * Entry point to set the UI model properties related to cross calculations.
   */
  public setupCrossCalculationUiModelProperties() {
    const crossCalculations = this.model.getCrossCalculations();
    if (!crossCalculations || !this.featureflags.DWCO_MODELING_AM_MULTI_STRUCTURE) {
      this.uiModel.setProperty("/crossCalculations", []);
      this.uiModel.setProperty("/crossCalculationModelMap", {});
      return;
    }
    this.setupCrossCalculations();
    this.setupCrossCalculationModelMap();
  }

  /**
   * Create a list of cross calculations based on the Query Model.
   */
  private setupCrossCalculations() {
    const crossCalculations = this.model.getCrossCalculations();

    const uiModelCrossCalculations: UICrossCalculations = [];
    Object.keys(crossCalculations).forEach((crossCalculationKey) => {
      const crossCalculation = crossCalculations[crossCalculationKey];
      const getPropertyIfExists = (name: string) => (name in crossCalculation ? crossCalculation[name] : undefined);

      const uiCrossCalculation: UICrossCalculation = {
        text: crossCalculation.text,
        technicalName: crossCalculationKey,
        crossCalculationType: crossCalculation.crossCalculationType,
        typeIcon: Formatter.crossCalculation.typeIcon(crossCalculation.crossCalculationType),
        exceptionAggregationAttributes: getPropertyIfExists("exceptionAggregationAttributes"),
        exceptionAggregationType: getPropertyIfExists("exceptionAggregationType"),
        constantSelectionType: getPropertyIfExists("constantSelectionType"),
        constantSelectionAttributes: getPropertyIfExists("constantSelectionAttributes"),
        isAuxiliary: crossCalculation.isAuxiliary,
        validationStatus: this.getPropertyValidation(`/crossCalculations/${encodeURIComponent(crossCalculationKey)}`),
      };
      uiModelCrossCalculations.push(uiCrossCalculation);
    });
    this.uiModel.setProperty("/crossCalculations", uiModelCrossCalculations);
  }

  /**
   * Create a key-value map of fact source cross calculation key and the model cross calculation key,
   * to link inherited cross calculations in use on the model with different technical names.
   * Currently supporting only single-fact AMs.
   */
  private setupCrossCalculationModelMap() {
    const crossCalculations = this.model.getCrossCalculations();

    const crossCalculationModelMap: { [key: string]: string[] } = {};
    Object.keys(crossCalculations).forEach((crossCalculationKey) => {
      const crossCalculation = crossCalculations[crossCalculationKey];

      if (crossCalculation.crossCalculationType === AnalyticModelCrossCalculationType.FactSourceCrossCalculation) {
        const sourceKey = this.model.getFirstFactSourceKey();
        const uiKey = `${sourceKey}:${AnalyticModelSourceType.Fact}:${crossCalculation.key}`;
        if (!crossCalculationModelMap[uiKey]) {
          crossCalculationModelMap[uiKey] = [];
        }
        crossCalculationModelMap[uiKey].push(crossCalculationKey);
      }
    });
    this.uiModel.setProperty("/crossCalculationModelMap", crossCalculationModelMap);
  }

  /**
   * Set the cross calculation list for a given source.
   */
  protected setNodeCrossCalculationsList(crossCalculations: NodeCrossCalculations, source: TypedSource) {
    if (source.type === AnalyticModelSourceType.Dimension) {
      this.uiModel.setProperty("/nodeCrossCalculations", []);
      return;
    }

    const nodeCrossCalculations: NodeCrossCalculations = [];
    for (const nodeCrossCalculation of crossCalculations) {
      if (nodeCrossCalculation.isHidden) {
        continue;
      }

      nodeCrossCalculations.push({
        ...nodeCrossCalculation,
        selected: this.getNodeCrossCalculationSelected(nodeCrossCalculation, source),
        displayText: this.getNodeCrossCalculationDisplayText(nodeCrossCalculation, source),
      });
    }
    this.uiModel.setProperty("/nodeCrossCalculations", nodeCrossCalculations);
  }

  /**
   * Helper to get the display text for a cross calculation in a node list.
   */
  private getNodeCrossCalculationDisplayText(nodeCrossCalculation: NodeCrossCalculation, source: TypedSource): string {
    let crossCalculationTechnicalName = nodeCrossCalculation.key;
    let crossCalculationBusinessName = nodeCrossCalculation.text;

    const modelCrossCalculationTechnicalName = this.getCrossCalculationModelKeys(nodeCrossCalculation, source)[0];
    const modelCrossCalculation = this.model.getCrossCalculation(modelCrossCalculationTechnicalName);

    if (modelCrossCalculation) {
      crossCalculationTechnicalName = modelCrossCalculationTechnicalName;
      crossCalculationBusinessName = modelCrossCalculation.text;
    }

    return Formatter.entityNameFormatter(crossCalculationBusinessName, crossCalculationTechnicalName);
  }

  /**
   * Helper to get the selected property for a cross calculation in a node list.
   */
  private getNodeCrossCalculationSelected(crossCalculation: NodeCrossCalculation, source: TypedSource): boolean {
    if (!crossCalculation || !source) {
      return false;
    }

    const getCrossCalculationModelKeys = this.getCrossCalculationModelKeys(crossCalculation, source);
    return getCrossCalculationModelKeys.length > 0;
  }

  /**
   * Helper to get the model cross calculation keys for a given cross calculation and source.
   */
  protected getCrossCalculationModelKeys(crossCalculation: NodeCrossCalculation, source: TypedSource): string[] {
    if (!source || !crossCalculation) {
      return [];
    }

    const crossCalculationModelMap = this.uiModel.getProperty("/crossCalculationModelMap");
    return crossCalculationModelMap[`${source.id}:${source.type}:${crossCalculation.key}`] ?? [];
  }

  /**
   * Helper to create a unique cross calculation key for a given property and selected object.
   */
  protected createCrossCalculationKey(property: IDataEntityProperty, selectedObject: TypedSource) {
    if (this.model.crossCalculationExists(property.key)) {
      return `${property.key}${selectedObject.id}`;
    }
    return property.key;
  }

  /**
   * Helper to clear the selected cross calculations of the AM output cross calculation list.
   */
  protected clearSelectedCrossCalculations() {
    this.uiModel.setProperty("/selectedCrossCalculations", []);
  }
  // #endregion
}

export const AnalyticModelBase = smartExtend(
  AbstractController,
  "sap.cdw.components.cubebuilder.controller.AnalyticModelBase",
  AnalyticModelBaseClass
);

sap.ui.define("sap/cdw/components/cubebuilder/properties/AnalyticModelBase.controller", [], function () {
  return AnalyticModelBase;
});
