/** @format */

import {
  AnalyticModelConstantSelectionType,
  AnalyticModelCrossCalculationType,
  AnalyticModelExceptionAggregationType,
  AnalyticModelParameterValueBaseType,
  IAnalyticModelCalculatedCrossCalculation,
  IAnalyticModelRestrictedCrossCalculation,
} from "../../../../shared/queryBuilder/AnalyticModel";
import { TechnicalNameMustNotBeMeasureValues } from "../../../../shared/queryBuilder/analyticModelValidators/crossCalculation/validations/common/TechnicalNameMustNotBeMeasureValues";
import { IConvertFormulaResult } from "../../../../shared/queryBuilder/CsnXprToAMHelper";
import {
  IDataEntity,
  IDataEntityCrossCalculation,
  IDataEntityDetailsResponse,
} from "../../../../shared/queryBuilder/DataEntityDetails";
import { MeasureValidationHelper } from "../../../../shared/queryBuilder/MeasureValidationHelper";
import { QueryModelValidator, ValidationMessage } from "../../../../shared/queryBuilder/QueryModelValidator";
import { smartExtend } from "../../basecomponent/controller/BaseController.controller";
import { NameUsage } from "../../commonui/utility/NameInputValidator";
import { NamingHelper } from "../../reuse/utility/NamingHelper";
import { convertFormulaToCsnXpr } from "../api/CdsFormulaToExpr.service";
import { UpdateCrossCalculationBusinessName } from "../command/crossCalculations/update/UpdateCrossCalculationBusinessName";
import { UpdateCrossCalculationConstantSelectionAttributes } from "../command/crossCalculations/update/UpdateCrossCalculationConstantSelectionAttributes";
import { UpdateCrossCalculationConstantSelectionType } from "../command/crossCalculations/update/UpdateCrossCalculationConstantSelectionType";
import { UpdateCrossCalculationDecimalPlaces } from "../command/crossCalculations/update/UpdateCrossCalculationDecimalPlaces";
import { UpdateCrossCalculationExceptionAggregationAttributes } from "../command/crossCalculations/update/UpdateCrossCalculationExceptionAggregationAttributes";
import { UpdateCrossCalculationExceptionAggregationType } from "../command/crossCalculations/update/UpdateCrossCalculationExceptionAggregationType";
import { UpdateCrossCalculationExpression } from "../command/crossCalculations/update/UpdateCrossCalculationExpression";
import { UpdateCrossCalculationIsAuxiliary } from "../command/crossCalculations/update/UpdateCrossCalculationIsAuxiliary";
import { UpdateCrossCalculationScale } from "../command/crossCalculations/update/UpdateCrossCalculationScale";
import { UpdateCrossCalculationTechnicalName } from "../command/crossCalculations/update/UpdateCrossCalculationTechnicalName";
import {
  AnalyticModelPropertiesBase,
  AnalyticModelPropertiesBaseClass,
} from "../properties/AnalyticModelPropertiesBase.controller";
import { ExpressionEditor } from "../utility/CubeBuilderTypes";
import { debounce } from "../utility/Debounce";
import { AnalyticModelObjectType, UsedInType } from "../utility/Enum";
import Formatter from "../utility/Formatter";
import { getNameUsageForObject, getSourceObjectLink, updateExpressionFormula } from "../utility/ModelUtils";
import Util from "../utility/Util";

export class CrossCalculationDetailsClass extends AnalyticModelPropertiesBaseClass {
  private shouldDerive: boolean = true;

  // #region View Setup
  onInit(): void {
    super.onInit();
  }

  public async onBeforeShow(): Promise<void> {
    super.onBeforeShow();
    await this.setCrossCalculation();
    this.setExpressionEditor();
    await this.validateExpressionAfterEditorLoads();
    this.setExceptionAggregationTypes();
    this.setUsedInList();
    this.setValidationBindings();
    this.clearTechnicalNameInputState();
    this.shouldDerive = true;
  }

  // #region Cross Calculation Setup
  /**
   * Set cross calculation properties used in the CrossCalculationDetails view based on the model's cross calculation
   */
  protected async setCrossCalculation() {
    await this.setSourceInformation();
    this.setTechnicalNameReference();
    this.setBusinessNameReference();
    this.setExpressionText();
    if (this.featureflags.DWCO_MODELING_AM_FORMAT_OF_MEASURES) {
      this.setFormatting();
    }
  }

  /**
   * Set the source information for the cross calculation, only applicable when the fact source is an Analytic Model.
   */
  private async setSourceInformation() {
    const sourceModel = this.getSourceModel();
    const sourceCrossCalculation = this.getSourceCrossCalculation();
    if (!sourceModel || !sourceCrossCalculation) {
      return;
    }

    const sourceName = Formatter.entityNameFormatter(sourceModel.businessName, sourceModel.technicalName);
    this.uiModel.setProperty("/crossCalculationDetails/sourceName", sourceName);

    const sourceLink = await getSourceObjectLink(sourceModel.technicalName, this.getSpaceName());
    this.uiModel.setProperty("/crossCalculationDetails/sourceLink", sourceLink);

    const sourceField = Formatter.entityNameFormatter(sourceCrossCalculation.text, sourceCrossCalculation.key);
    this.uiModel.setProperty("/crossCalculationDetails/sourceField", sourceField);
  }

  /**
   * Set a reference to be used if technical name is changed to a invalid name
   */
  private setTechnicalNameReference() {
    this.uiModel.setProperty("/crossCalculationDetails/oldTechnicalName", this.getTechnicalName());
  }

  /**
   * Set a reference to be used if business name is changed to a invalid name
   */
  private setBusinessNameReference() {
    this.uiModel.setProperty("/crossCalculationDetails/oldBusinessName", this.getTechnicalName());
  }

  /**
   * Set the binding used by the expression editor on the CrossCalculationExpressionEditor fragment
   */
  private setExpressionText() {
    const crossCalculation = this.model.getCrossCalculation(this.getTechnicalName());
    if ("formulaRaw" in crossCalculation) {
      this.uiModel.setProperty("/crossCalculationDetails/expression", crossCalculation.formulaRaw);
    }
  }

  /**
   * Set the bindings used by the CrossCalculationStandardFormat fragment
   */
  private setFormatting() {
    const { formatting } = this.model.getCrossCalculation(this.getTechnicalName());
    const scaleType = formatting?.scaleType;
    const decimalPlaces = formatting?.decimalPlaces;
    this.uiModel.setProperty("/crossCalculationScaleType", this.getScaleType());
    this.uiModel.setProperty("/crossCalculationDecimalPlaces", this.getDecimalPlaces());
    this.uiModel.setProperty("/crossCalculationDetails/formatting", { scaleType, decimalPlaces });
  }

  // #endregion

  // #region Expression Editor Setup
  /*
   * Wait to validate expression since initialization of expression editor is async
   * Waiting is only necessary for AM internal routing because user can open editor in a cross calculation.
   */
  public async validateExpressionAfterEditorLoads() {
    const expressionEditor = this.getExpressionEditor();
    if (expressionEditor.loaded) {
      await this.validateExpression();
      return;
    }
    const waitToValidate = () => {
      setTimeout(() => {
        if (!expressionEditor.loaded) {
          waitToValidate();
        } else {
          void this.validateExpression();
        }
      }, 0);
    };
    waitToValidate();
  }

  private setExpressionEditor() {
    this.setColumns();
    this.setSuggestions();
    this.uiModel.refresh(true);
  }

  private setColumns() {
    this.setCrossCalculationsColumn();
    this.setAttributesColumn();
    this.setVariablesColumn();
  }

  private setCrossCalculationsColumn() {
    const crossCalculations = this.getCrossCalculationsColumnList();
    this.uiModel.setProperty("/crossCalculationDetails/crossCalculationColumns", crossCalculations);
  }

  private getCrossCalculationsColumnList() {
    const technicalName = this.getTechnicalName();
    const filteredCrossCalculations: Array<{ key: string; text: string; icon: string }> = [];
    const crossCalculations = this.model.getCrossCalculations();
    for (const crossCalculationKey in crossCalculations) {
      const crossCalculation = crossCalculations[crossCalculationKey];
      if (crossCalculationKey !== technicalName) {
        filteredCrossCalculations.push({
          key: crossCalculationKey,
          text: crossCalculation.text,
          icon: Formatter.crossCalculation.typeIcon(crossCalculation.crossCalculationType),
        });
      }
    }
    return filteredCrossCalculations;
  }

  private setAttributesColumn() {
    const selectedAttributeKeys = this.uiModel.getProperty("/crossCalculationDetails/exceptionAggregationAttributes");
    const attributes = this.getAttributesColumnList(selectedAttributeKeys);
    this.uiModel.setProperty("/crossCalculationDetails/attributeColumns", attributes);
  }

  private setVariablesColumn() {
    const crossCalculationType = this.uiModel.getProperty("/crossCalculationDetails/crossCalculationType");

    let variables: Array<{
      key: string;
      text: string;
      entry: string;
      icon: string;
      value?: AnalyticModelParameterValueBaseType;
    }>;
    if (crossCalculationType === AnalyticModelCrossCalculationType.RestrictedCrossCalculation) {
      variables = this.getFilterVariablesColumnList();
    } else {
      variables = this.getInputVariablesColumnList();
    }

    this.uiModel.setProperty("/crossCalculationDetails/variablesColumn", variables);
  }

  private setSuggestions() {
    this.setCrossCalculationsSuggestions();
    this.setAttributesSuggestions();
    this.setVariablesSuggestions();
  }

  private setCrossCalculationsSuggestions() {
    const crossCalculations: Array<{ key: string }> = this.uiModel.getProperty(
      "/crossCalculationDetails/crossCalculationColumns"
    );
    this.uiModel.setProperty(
      "/crossCalculationDetails/crossCalculationSuggestions",
      crossCalculations.map((crossCalculation) => ({
        text: crossCalculation.key,
      }))
    );
  }

  private setAttributesSuggestions() {
    const attributes = this.uiModel.getProperty("/crossCalculationDetails/attributeColumns");
    this.setSuggestionsForAttributes(this.uiModel, attributes, "/crossCalculationDetails/attributeSuggestions");
  }

  private setVariablesSuggestions() {
    const variables = this.uiModel.getProperty("/crossCalculationDetails/variablesColumn");
    this.setSuggestionsForVariables(this.uiModel, variables, "/crossCalculationDetails/variableSuggestions");
  }
  // #endregion

  private setExceptionAggregationTypes() {
    const exceptionAggregationTypes = this.getExceptionAggregationTypes();
    this.uiModel.setProperty("/crossCalculationDetails/exceptionAggregationTypes", exceptionAggregationTypes);

    if (!this.featureflags.DWCO_MODELING_AM_UNBOOKED_EXCEPTION_AGGREGATION) {
      this.uiModel.setProperty(
        "/crossCalculationDetails/exceptionAggregationTypes",
        exceptionAggregationTypes.filter((item) => !item.key.includes("_OF_DIMENSION"))
      );
    }
  }

  private setUsedInList() {
    const usedInList = this.dependencies.getCrossCalculationsUsedIn(this.getTechnicalName());
    this.uiModel.setProperty("/crossCalculationDetails/usedIn", usedInList);
  }
  // #endregion

  // #region General Handlers
  public async onCrossCalculationBusinessNameChange(event: IEvent<sap.m.Input, { value: string }>): Promise<void> {
    const input = event.getSource();
    const newBusinessName = event.getParameters().value.trim();

    const technicalName = this.uiModel.getProperty("/crossCalculationDetails/oldTechnicalName");

    if (newBusinessName === "") {
      input.setValueState(sap.ui.core.ValueState.Error);
      input.setValueStateText(this.getText("crossCalculationBusinessNameEmpty"));
      input.openValueStateMessage();
      this.resetBusinessName();
      return;
    } else {
      input.setValueState(sap.ui.core.ValueState.None);
      input.setValueStateText(undefined);
      input.closeValueStateMessage();
    }

    let newTechnicalName = technicalName;
    if (this.isNewCrossCalculation() && this.shouldDerive) {
      newTechnicalName = this.getTechnicalName();
    }

    const updateCrossCalculationBusinessNameCommand = new UpdateCrossCalculationBusinessName(
      technicalName,
      newBusinessName,
      newTechnicalName
    );
    this.stack.execute(updateCrossCalculationBusinessNameCommand);

    this.updateBreadcrumb(
      {
        businessName: newBusinessName,
        technicalName: newTechnicalName,
      },
      this.uiModel.getProperty("/breadcrumbs/links")
    );
    this.setTechnicalNameReference();

    // DW15-5767 Review this and all modelChangeHandler calls made from Cross Calculation files prior to GA release
    await Util.Component.modelChangeHandler();
  }

  public async onCrossCalculationBusinessNameLiveChange(event: IEvent<sap.m.Input, { value: string }>): Promise<void> {
    const input = event.getSource();
    const newBusinessName = event.getParameters().value.trim();

    if (newBusinessName === "") {
      input.setValueState(sap.ui.core.ValueState.Error);
      input.setValueStateText(this.getText("crossCalculationBusinessNameEmpty"));
      input.openValueStateMessage();
      return;
    } else {
      input.setValueState(sap.ui.core.ValueState.None);
      input.setValueStateText(undefined);
      input.closeValueStateMessage();
    }

    if (this.isNewCrossCalculation() && this.shouldDerive) {
      const nameValidator = NamingHelper.getNameInputValidator();
      const newTechnicalName = nameValidator.deriveTechnicalName(
        newBusinessName,
        getNameUsageForObject(AnalyticModelObjectType.CROSS_CALCULATION, this.model)
      );
      const errorMessage = this.validateCrossCalculationTechnicalName(newTechnicalName);
      if (!errorMessage) {
        this.uiModel.setProperty("/crossCalculationDetails/technicalName", newTechnicalName);
      }
    }
  }

  public async onCrossCalculationTechnicalNameChange(): Promise<void> {
    this.shouldDerive = false;

    if (!this.isNewCrossCalculation()) {
      const cancelChange = await this.technicalNameChangeWarning("crossCalculationTechnicalNameChangeWarning");
      if (cancelChange) {
        this.resetTechnicalName();
        return;
      }
    }

    const newTechnicalName = this.getTechnicalName();
    const errorMessage = this.validateCrossCalculationTechnicalName(newTechnicalName);
    if (errorMessage) {
      this.resetTechnicalName();
      return;
    }

    const oldTechnicalName = this.uiModel.getProperty("/crossCalculationDetails/oldTechnicalName");
    const updateCrossCalculationTechnicalNameCommand = new UpdateCrossCalculationTechnicalName(
      oldTechnicalName,
      newTechnicalName
    );
    this.stack.execute(updateCrossCalculationTechnicalNameCommand);

    this.setTechnicalNameReference();
    this.updateBreadcrumb(
      {
        businessName: this.uiModel.getProperty("/crossCalculationDetails/text"),
        technicalName: newTechnicalName,
      },
      this.uiModel.getProperty("/breadcrumbs/links")
    );

    // DW15-5767 Review this and all modelChangeHandler calls made from Cross Calculation files prior to GA release
    await Util.Component.modelChangeHandler();
  }

  public onCrossCalculationTechnicalNameLiveChange(event: IEvent<sap.m.Input, { value: string }>): void {
    const input = event.getSource();
    const newTechnicalName = event.getParameters().value;
    const errorMessage = this.validateCrossCalculationTechnicalName(newTechnicalName);
    if (errorMessage) {
      input.setValueState(sap.ui.core.ValueState.Error);
      input.setValueStateText(errorMessage);
      input.openValueStateMessage();
    } else {
      this.clearTechnicalNameInputState();
    }
  }

  private validateCrossCalculationTechnicalName(newTechnicalName: string): string | undefined {
    const oldTechnicalName = this.uiModel.getProperty("/crossCalculationDetails/oldTechnicalName");
    if (newTechnicalName === oldTechnicalName) {
      return;
    }

    const validator = NamingHelper.getNameInputValidator();
    // Checks if the technical name is empty
    // Checks if the technical name contains invalid characters
    // Checks if the technical name is too long
    const validationResult = validator.doDeriveTechnicalName(
      newTechnicalName,
      NameUsage.element,
      { skipAllErrors: false, skipInvalidError: false, skipTrailingErrors: true },
      [],
      {
        duplicatedTechnicalName: "",
        emptyTechnicalName: this.getText("crossCalculationTechnicalNameEmpty"),
      }
    );

    // Check if the technical name is unique among cross calculations, attributes, and measures.
    if (this.model.getCrossCalculation(newTechnicalName)) {
      // eslint-disable-next-line no-underscore-dangle
      validationResult._errorMessages.push(
        this.getText("crossCalculationTechnicalNameIsNotUnique", ["cross calculation"])
      );
    }
    if (this.model.getAttribute(newTechnicalName)) {
      // eslint-disable-next-line no-underscore-dangle
      validationResult._errorMessages.push(this.getText("crossCalculationTechnicalNameIsNotUnique", ["dimension"]));
    }
    if (this.model.getMeasure(newTechnicalName)) {
      // eslint-disable-next-line no-underscore-dangle
      validationResult._errorMessages.push(this.getText("crossCalculationTechnicalNameIsNotUnique", ["measure"]));
    }
    if (newTechnicalName === TechnicalNameMustNotBeMeasureValues.reservedTechnicalName) {
      // eslint-disable-next-line no-underscore-dangle
      validationResult._errorMessages.push(
        this.getText("validationMessageDescriptionMeasureValuesIsReservedTechnicalName")
      );
    }

    // eslint-disable-next-line dot-notation
    return validator["combineErrorMessages"](validationResult);
  }

  public async onIsAuxiliaryChange(event: IEvent<sap.m.CheckBox, { selected: boolean }>): Promise<void> {
    const selected = event.getParameters().selected;
    const technicalName = this.getTechnicalName();
    const crossCalculationExists = this.model.crossCalculationExists(technicalName);
    if (crossCalculationExists) {
      const updateIsAuxiliaryCommand = new UpdateCrossCalculationIsAuxiliary(technicalName, selected);
      this.stack.execute(updateIsAuxiliaryCommand);
      await Util.Component.modelChangeHandler();
    }
  }
  // #endregion

  // #region Validations
  // @override
  public validationListener(_: string, __: string, ___: ValidationMessage[]): void {
    this.setValidationBindings();
  }

  private setValidationBindings() {
    this.clearValidationBindings();
    const messages = this.getValidationMessagesToBind();

    for (const message of messages) {
      this.uiModel.setProperty(
        `/crossCalculationDetails/validationMessages/${message.field}ValueState`,
        message.valueState
      );
      this.uiModel.setProperty(
        `/crossCalculationDetails/validationMessages/${message.field}ValueStateText`,
        message.valueStateText
      );
    }
  }

  private getValidationMessagesToBind() {
    const messages = this.getApplicableValidationMessages();
    const propertyPath = this.getValidationMessagePropertyPath();
    return messages.map((message) => ({
      field: message.propertyPath.replace(propertyPath, ""),
      valueState: message.type,
      valueStateText: this.getText(message.descriptionKey, [...message.parameters]),
    }));
  }

  private getApplicableValidationMessages(): ValidationMessage[] {
    const allValidationMessages: ValidationMessage[] = this.getValidationMessages();
    const propertyPath = this.getValidationMessagePropertyPath();
    return allValidationMessages.filter((message) => message.propertyPath?.includes(propertyPath));
  }

  private getValidationMessages(): ValidationMessage[] {
    return this.uiModel.getProperty("/validationMessages");
  }

  private getValidationMessagePropertyPath(): string {
    const technicalName = this.getTechnicalName();
    return `/crossCalculations/${encodeURIComponent(technicalName)}/`;
  }

  private clearValidationBindings() {
    this.uiModel.setProperty("/crossCalculationDetails/validationMessages", {});
  }
  // #endregion

  // #region Expression Editor
  public async onExpressionChange(event: IEvent<ExpressionEditor, object>) {
    this.resetExpressionEditorMessageStrip(event.getSource());
    debounce(() => {
      void this.validateExpression(true);
    }, 2000)();
  }

  public async onValidateExpression(event: IEvent<ExpressionEditor, object>) {
    this.resetExpressionEditorMessageStrip(event.getSource());
    debounce(() => {
      void this.validateExpression();
    }, 2000)();
  }

  public async validateExpression(change = false) {
    const expressionEditor = this.getExpressionEditor();
    const expression = expressionEditor.codeEditor.getValue();
    // Set the expression text to sync the Expression Editor expression binding as it's not two-way
    this.uiModel.setProperty("/crossCalculationDetails/expression", expression);

    let convertFormulaResult: IConvertFormulaResult;
    if (this.isCalculatedCrossCalculation()) {
      convertFormulaResult = await MeasureValidationHelper.convertStringToCalculatedCrossCalculation(
        expression,
        convertFormulaToCsnXpr
      );
    } else {
      convertFormulaResult = await MeasureValidationHelper.convertStringToRestrictedCrossCalculation(
        expression,
        convertFormulaToCsnXpr
      );
    }

    const technicalName = this.getTechnicalName();
    const formulaMessages = QueryModelValidator.validateParsedCrossCalculationFormula(
      technicalName,
      this.model.getData(),
      convertFormulaResult
    );

    // 2. Assign parsed formula and elements if no errors (must be assigned for complete validation) and validate
    const shouldResetFormula = formulaMessages.length !== 0;
    const newExpression = {
      formula: convertFormulaResult?.crossCalculation?.formula,
      elements: convertFormulaResult?.crossCalculation?.elements,
      formulaRaw: expression,
      shouldResetFormula,
    };

    let message: ValidationMessage;
    expressionEditor.setMessageStripVisible(true);
    if (!change) {
      const model = new sap.ui.model.json.JSONModel(this.model.getData());
      const modelFilter = model.getProperty("/crossCalculations")[technicalName] as
        | IAnalyticModelCalculatedCrossCalculation
        | IAnalyticModelRestrictedCrossCalculation;
      updateExpressionFormula(modelFilter, newExpression);

      const validationMessages = await Util.Component.validateModel(this.model);
      message = Util.Component.getLocalValidationByPath(
        `/crossCalculations/${encodeURIComponent(technicalName)}/expression`,
        validationMessages
      );
    } else {
      const updateCrossCalculationExpressionCommand = new UpdateCrossCalculationExpression(
        technicalName,
        newExpression
      );
      this.stack.execute(updateCrossCalculationExpressionCommand);
      await Util.Component.modelChangeHandler();
    }

    // 3. Error handling based on validation map updated in complete model validation
    // this message attribution should be moved to the last else condition (where change is true) after removing the DWCO_MODELING_ANALYTIC_MODEL_STACKING_LIFECYCLE FF
    if (change || !this.featureflags.DWCO_MODELING_ANALYTIC_MODEL_STACKING_LIFECYCLE) {
      message = Util.Component.getPropertyValidationMessage(
        `/crossCalculations/${encodeURIComponent(technicalName)}/expression`
      );
    }

    const validationMessages = [];
    if (!!message) {
      const errorMessage = !!message.parameters
        ? this.replaceParameters(this.getText(message.descriptionKey), message.parameters)
        : this.getText(message.descriptionKey);
      const messageType = message.type;
      validationMessages.push({ message: errorMessage, type: messageType });
    }
    // 4. Set validationMessages for the message strip text
    this.uiModel.setProperty("/crossCalculationDetails/formulaValidation", validationMessages);
    if (
      validationMessages.length === 0 ||
      (validationMessages.length === 1 && message.descriptionKey === "calculatedMeasureEmptyExpression")
    ) {
      setTimeout(() => {
        expressionEditor.setMessageStripVisible(false);
      }, 2000);
    }
  }

  /**
   * Used in the expression editor. Handles the event before showing the cross calculation popover,
   * fetching and formatting cross calculation details for display.
   */
  public onBeforeCrossCalculationPopover(event: sap.ui.base.Event) {
    const crossCalculationTechnicalName = event.getParameter("columnName");
    const crossCalculationDetails = this.model.getCrossCalculation(crossCalculationTechnicalName);
    if (!!crossCalculationDetails) {
      const crossCalculationInfoItems = [
        {
          label: this.getText("businessName"),
          value: crossCalculationDetails.text,
        },
        {
          label: this.getText("technicalName"),
          value: crossCalculationTechnicalName,
        },
        {
          label: this.getText("crossCalculationType"),
          value: Formatter.crossCalculation.crossCalculationTypeTextFormatter(
            crossCalculationDetails.crossCalculationType
          ),
        },
      ];
      const crossCalculationInfo = {
        sectionNum: 1,
        sections: [
          {
            title: this.getText("crossCalculationDetails"),
            items: crossCalculationInfoItems,
          },
        ],
      };
      this.uiModel.setProperty("/crossCalculationDetails/crossCalculationInfo", crossCalculationInfo);
    }
    this.uiModel.refresh(true);
  }

  public onBeforeAttributePopoverCrossCalculation(event: sap.ui.base.Event) {
    this.onBeforeAttributePopover(event, this.uiModel, "/crossCalculationDetails/attributeInfo");
  }

  public onBeforeVariablePopover(event: sap.ui.base.Event) {
    const variableTechnicalName = event.getParameter("columnName");
    const variableDetails = this.model.getVariable(variableTechnicalName);
    if (!variableDetails) {
      this.uiModel.setProperty("/crossCalculationDetails", {});
    }

    const variableInfo = {
      sectionNum: 1,
      sections: [
        {
          title: this.getText("variableDetails"),
          items: [
            {
              label: this.getText("businessName"),
              value: "text" in variableDetails ? variableDetails?.text : "",
            },
            {
              label: this.getText("technicalName"),
              value: variableTechnicalName,
            },
          ],
        },
      ],
    };
    this.uiModel.setProperty("/crossCalculationDetails", variableInfo);
    this.uiModel.refresh(true);
  }
  // #endregion

  // #region Exception Aggregation
  public async onExceptionAggregationTypeChange(
    event: IEvent<sap.m.Select, { selectedItem: sap.ui.core.Item }>
  ): Promise<void> {
    const exceptionAggregationType = event
      .getParameters()
      .selectedItem.getKey() as AnalyticModelExceptionAggregationType;

    const technicalName = this.getTechnicalName();
    if (!this.model.crossCalculationExists(technicalName)) {
      return;
    }

    if (exceptionAggregationType === AnalyticModelExceptionAggregationType.NONE) {
      this.uiModel.setProperty("/crossCalculationDetails/exceptionAggregationAttributes", []);
    }

    const updateExceptionAggregationTypeCommand = new UpdateCrossCalculationExceptionAggregationType(
      technicalName,
      exceptionAggregationType
    );
    this.stack.execute(updateExceptionAggregationTypeCommand);
    await Util.Component.modelChangeHandler();
  }

  public async onSelectionFinishExceptionAggregationAttributes(
    event: IEvent<sap.m.MultiComboBox, { selectedItems: sap.ui.core.Item[] }>
  ): Promise<void> {
    const selectedAttributes = event.getParameter("selectedItems").map((item) => item.getKey()) ?? [];

    const technicalName = this.getTechnicalName();
    if (!this.model.crossCalculationExists(technicalName)) {
      return;
    }

    const updateExceptionAggregationAttributesCommand = new UpdateCrossCalculationExceptionAggregationAttributes(
      technicalName,
      selectedAttributes
    );
    this.stack.execute(updateExceptionAggregationAttributesCommand);
    await Util.Component.modelChangeHandler();
  }
  // #endregion

  // #region Constant Selection
  public async onConstantSelectionTypeChange(
    event: IEvent<sap.m.Select, { selectedItem: sap.ui.core.Item }>
  ): Promise<void> {
    const selectedConstantSelectionType = event
      .getParameters()
      .selectedItem.getKey() as AnalyticModelConstantSelectionType;

    const technicalName = this.getTechnicalName();
    if (!this.model.crossCalculationExists(technicalName)) {
      return;
    }

    this.uiModel.setProperty("/crossCalculationDetails/constantSelectionAttributes", []);

    const updateConstantSelectionTypeCommand = new UpdateCrossCalculationConstantSelectionType(
      technicalName,
      selectedConstantSelectionType
    );
    this.stack.execute(updateConstantSelectionTypeCommand);
    await Util.Component.modelChangeHandler();
  }

  public async onFinishConstantSelectionAttributes(
    event: IEvent<sap.m.MultiComboBox, { selectedItems: sap.ui.core.Item[] }>
  ): Promise<void> {
    const selectedKeys = event.getParameters().selectedItems.map((item) => item.getKey());

    const technicalName = this.getTechnicalName();
    if (!this.model.crossCalculationExists(technicalName)) {
      return;
    }

    const updateConstantSelectionAttributesCommand = new UpdateCrossCalculationConstantSelectionAttributes(
      technicalName,
      selectedKeys
    );
    this.stack.execute(updateConstantSelectionAttributesCommand);
    await Util.Component.modelChangeHandler();
  }
  // #endregion

  // #region Standard Format
  public async onChangeCrossCalculationScaleType(event: IEvent<sap.m.Select, object>): Promise<void> {
    const key = event.getSource().getSelectedKey();
    const newValue = key ? Number(key) : undefined;
    const technicalName = this.uiModel.getProperty("/crossCalculationDetails/technicalName");
    const updateMeasureScaleCommand = new UpdateCrossCalculationScale(technicalName, newValue);
    this.stack.execute(updateMeasureScaleCommand);
    await Util.Component.modelChangeHandler();
  }

  public async onChangeCrossCalculationDecimalPlaces(event: IEvent<sap.m.Select, object>): Promise<void> {
    const key = event.getSource().getSelectedKey();
    const newValue = key ? Number(key) : undefined;
    const technicalName = this.uiModel.getProperty("/crossCalculationDetails/technicalName");
    const updateCrossCalculationDecimalPlacesCommand = new UpdateCrossCalculationDecimalPlaces(technicalName, newValue);
    this.stack.execute(updateCrossCalculationDecimalPlacesCommand);
    await Util.Component.modelChangeHandler();
  }
  // #endregion

  // #region Used In
  public async onUsedInCrossCalculationPropertyPress(event: sap.ui.base.Event) {
    const bindingContextPath = event.getSource().getBindingContext("ui").getPath();
    const usedInObject = this.uiModel.getProperty(bindingContextPath);
    const technicalName = usedInObject.technicalName;
    this.goToUsedInProperty(UsedInType.CROSS_CALCULATION, technicalName);
  }
  // #endregion

  // #region Helpers
  private getTechnicalName(): string {
    return this.uiModel.getProperty("/crossCalculationDetails/technicalName");
  }

  private isRestrictedCrossCalculation(): boolean {
    return (
      this.uiModel.getProperty("/crossCalculationDetails/crossCalculationType") ===
      AnalyticModelCrossCalculationType.RestrictedCrossCalculation
    );
  }

  private isCalculatedCrossCalculation(): boolean {
    return (
      this.uiModel.getProperty("/crossCalculationDetails/crossCalculationType") ===
      AnalyticModelCrossCalculationType.CalculatedCrossCalculation
    );
  }

  private getExpressionEditor(): ExpressionEditor {
    return this.isCalculatedCrossCalculation()
      ? this.byId("calculatedCrossCalculationExpressionEditor")
      : this.byId("restrictedCrossCalculationExpressionEditor");
  }

  /**
   * Get the fact source of inherited cross calculation, only applicable when the fact source is an Analytic Model.
   * Facts does not have cross calculations.
   */
  private getSourceModel(): IDataEntity | undefined {
    const crossCalculation = this.model.getCrossCalculation(this.getTechnicalName());
    if (crossCalculation.crossCalculationType !== AnalyticModelCrossCalculationType.FactSourceCrossCalculation) {
      return;
    }

    const sourceModelKey = this.model.getFactSource(crossCalculation.sourceKey).dataEntity.key;
    const allDataSourceEntityDetails = this.getAllSourceDataEntityDetails();
    const sourceModel = allDataSourceEntityDetails[sourceModelKey];
    return sourceModel;
  }

  /**
   * Get the source of the current inherited cross calculation, only applicable for Fact Source Cross Calculations.
   */
  private getSourceCrossCalculation(): IDataEntityCrossCalculation | undefined {
    const sourceModel = this.getSourceModel();
    if (!sourceModel) {
      return;
    }

    const crossCalculation = this.model.getCrossCalculation(this.getTechnicalName());
    if (crossCalculation.crossCalculationType !== AnalyticModelCrossCalculationType.FactSourceCrossCalculation) {
      return;
    }

    return sourceModel.crossCalculations?.find(
      (sourceCrossCalculation) => sourceCrossCalculation.key === crossCalculation.key
    );
  }

  private getAllSourceDataEntityDetails(): IDataEntityDetailsResponse {
    return this.uiModel.getProperty("/allSourceDataEntityDetails");
  }

  private isNewCrossCalculation(): boolean {
    const technicalName = this.uiModel.getProperty("/crossCalculationDetails/oldTechnicalName");
    return !this.stack.getInitialState().crossCalculationExists(technicalName);
  }

  private resetBusinessName(): void {
    const oldBusinessName = this.uiModel.getProperty("/crossCalculationDetails/oldBusinessName");
    this.uiModel.setProperty("/crossCalculationDetails/text", oldBusinessName);
  }

  private resetTechnicalName(): void {
    const oldTechnicalName = this.uiModel.getProperty("/crossCalculationDetails/oldTechnicalName");
    this.uiModel.setProperty("/crossCalculationDetails/technicalName", oldTechnicalName);
  }

  private clearTechnicalNameInputState(): void {
    const technicalNameInput = this.byId("crossCalculationDetailsTechnicalName") as sap.m.Input;
    technicalNameInput.setValueState(sap.ui.core.ValueState.None);
    technicalNameInput.setValueStateText(undefined);
    technicalNameInput.closeValueStateMessage();
  }

  // #endregion
}

export const CrossCalculationDetails = smartExtend(
  AnalyticModelPropertiesBase,
  "sap.cdw.components.cubebuilder.controller.CrossCalculationDetails",
  CrossCalculationDetailsClass
);

sap.ui.define("sap/cdw/components/cubebuilder/controller/CrossCalculationDetails.controller", [], function () {
  return CrossCalculationDetails;
});
