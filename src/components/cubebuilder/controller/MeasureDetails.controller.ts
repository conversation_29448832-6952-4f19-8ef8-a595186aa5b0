/** @format */

import { RepositoryObjectType } from "@sap/deepsea-types";
import { Logger } from "@sap/dwc-uihelper";
import {
  AnalyticModelAggregationTypes,
  AnalyticModelConstantSelectionType,
  AnalyticModelConversionTypeType,
  AnalyticModelErrorHandling,
  AnalyticModelExceptionAggregationNcumType,
  AnalyticModelExceptionAggregationType,
  AnalyticModelMeasureType,
  AnalyticModelReferenceDateType,
  AnalyticModelTargetCurrencyType,
  AnalyticModelTargetUnitType,
  IAnalyticModelAttributeKey,
  IAnalyticModelCalculatedMeasure,
  IAnalyticModelConstantValue,
  IAnalyticModelCountDistinctMeasure,
  IAnalyticModelCurrencyConversionMeasure,
  IAnalyticModelFormatting,
  IAnalyticModelNonCumulativeMeasure,
  IAnalyticModelRestrictedMeasure,
  IAnalyticModelSourceMeasure,
  IAnalyticModelSqlFunctionName,
  IAnalyticModelUnitConversionMeasure,
  IAnalyticModelVariableKey,
} from "../../../../shared/queryBuilder/AnalyticModel";
import { ReverseAggregationTypeMapping } from "../../../../shared/queryBuilder/AnalyticModelCsnMappings";
import { CsnXprToAMHelper, IConvertFormulaResult } from "../../../../shared/queryBuilder/CsnXprToAMHelper";
import { IDataEntityDetailsResponse } from "../../../../shared/queryBuilder/DataEntityDetails";
import { MeasureValidationHelper } from "../../../../shared/queryBuilder/MeasureValidationHelper";
import { QueryModelValidator, ValidationMessage } from "../../../../shared/queryBuilder/QueryModelValidator";
import { valueHelpService } from "../../../services/metadata";
import { smartExtend } from "../../basecomponent/controller/BaseController.controller";
import { SemanticType } from "../../commonmodel/model/types/cds.types";
import { Format } from "../../reuse/utility/Format";
import { NamingHelper } from "../../reuse/utility/NamingHelper";
import { User } from "../../shell/utility/User";
import { ObjectNameDisplay } from "../../userSettings/utility/Constants";
import { convertFormulaToCsnXpr } from "../api/CdsFormulaToExpr.service";
import { UpdateAggregation } from "../command/measures/update/UpdateAggregation";
import { UpdateClientId } from "../command/measures/update/UpdateClientId";
import { UpdateConstantSelectionAttributes } from "../command/measures/update/UpdateConstantSelectionAttributes";
import { UpdateConstantSelectionType } from "../command/measures/update/UpdateConstantSelectionType";
import { UpdateConversion } from "../command/measures/update/UpdateConversion";
import { UpdateDefaultConversionMeasure } from "../command/measures/update/UpdateDefaultConversionMeasure";
import { UpdateErrorHandling } from "../command/measures/update/UpdateErrorHandling";
import { UpdateExceptionAggregationAttributes } from "../command/measures/update/UpdateExceptionAggregationAttributes";
import { UpdateExceptionAggregationNcumType } from "../command/measures/update/UpdateExceptionAggregationNcumType";
import { UpdateExceptionAggregationType } from "../command/measures/update/UpdateExceptionAggregationType";
import { UpdateIsAuxiliary } from "../command/measures/update/UpdateIsAuxiliary";
import { UpdateMeasureBusinessName } from "../command/measures/update/UpdateMeasureBusinessName";
import { UpdateMeasureDecimalPlaces } from "../command/measures/update/UpdateMeasureDecimalPlaces";
import { UpdateMeasureExpression } from "../command/measures/update/UpdateMeasureExpression";
import { UpdateMeasureScale } from "../command/measures/update/UpdateMeasureScale";
import { UpdateMeasureTechnicalName } from "../command/measures/update/UpdateMeasureTechnicalName";
import { UpdateSourceMeasure } from "../command/measures/update/UpdateSourceMeasure";
import { UpdateUnbookedDeltaHandling } from "../command/measures/update/UpdateUnbookedDeltaHandling";
import { UpdateRecordTypeAttribute } from "../command/nonCumulativeSettings/UpdateRecordTypeAttribute";
import { UpdateReportingMaxEndTime } from "../command/nonCumulativeSettings/UpdateReportingMaxEndTime";
import { UpdateReportingMinStartTime } from "../command/nonCumulativeSettings/UpdateReportingMinStartTime";
import { UpdateTimeDimension } from "../command/nonCumulativeSettings/UpdateTimeDimension";
import { DebugService } from "../debug/DebugService";
import {
  AnalyticModelPropertiesBase,
  AnalyticModelPropertiesBaseClass,
} from "../properties/AnalyticModelPropertiesBase.controller";
import { getIconForModel } from "../utility/CubeBuilder";
import {
  ConversionUpdate,
  ConversionValueUpdate,
  ConversionVariableCreation,
  CurrencyConversionData,
  DialogButton,
  ExpressionEditor,
  TimeDimensionFields,
  UnitConversionData,
  UpdatedUnifiedDateRange,
} from "../utility/CubeBuilderTypes";
import { debounce } from "../utility/Debounce";
import {
  AnalyticModelEventBusChannels,
  AnalyticModelEventBusEvents,
  AnalyticModelObjectType,
  CONVERSION_TYPES,
  CONVERSION_TYPE_KEYS,
  CONVERSION_TYPE_PROPERTIES,
  ConversionUpdateType,
  CurrencyConversionViews,
  CurrencyUnitConversionViewsFieldNames,
  ModelUIProperties,
  UnitConversionViews,
  UsedInType,
} from "../utility/Enum";
import Formatter from "../utility/Formatter";
import {
  getMeasureFromDataEntityDetails,
  getNameUsageForObject,
  getParsedExceptionAggregationNcumTypes,
  getSourceForMeasure,
  getSourceMeasuresAvailableForMeasure,
  getSourceObjectLink,
  getVariableDefaultNameSequenced,
  shouldExceptionAggregationApply,
  updateExpressionFormula,
} from "../utility/ModelUtils";
import Util from "../utility/Util";

export class MeasureDetailsClass extends AnalyticModelPropertiesBaseClass {
  protected measureModel: sap.ui.model.json.JSONModel;
  protected featureflags;
  private targetCurrencyDialog: sap.m.Dialog;
  private targetUnitDialog: sap.m.Dialog;
  private referenceDateDialog: sap.m.Dialog;
  private conversionTypeDialog: sap.m.Dialog;
  private currencyConversionData: CurrencyConversionData;
  private unitConversionData: UnitConversionData;
  private measureModelData;

  public onInit(): void {
    super.onInit();
    this.measureModel = Util.Component.getModel("measure") as sap.ui.model.json.JSONModel;
    this.getView().setModel(this.measureModel, "measure");
    this.measureModelData = this.measureModel.getData();
    this.currencyConversionData = {
      isOkEnabled: false,
      currencyConversionTablesNotAvailable: false,
      currencyConversionTablesEmpty: false,
      sourceVariable: {
        key: "",
        text: "",
      },
      clientID: [],
      targetCurrencyDialog: {
        title: this.getText("CC_targetCurrencyDialogTitle"),
        currencyTypes: [
          { key: CONVERSION_TYPE_KEYS.CONSTANT_VALUE, text: this.getText("ccTypesFixedValuesWithNum", ["0"]) },
          { key: CONVERSION_TYPE_KEYS.ATTRIBUTE, text: this.getText("ccTypesDimensionsWithNum", ["0"]) },
          {
            key: CONVERSION_TYPE_KEYS.VARIABLES,
            text: this.getText("ccTypesVariablesWithNum", ["0"]),
          },
        ],
        currencyType: CONVERSION_TYPE_KEYS.CONSTANT_VALUE,
      },
      referenceDateDialog: {
        title: this.getText("CC_referenceDateDialogTitle"),
        referenceDateTypes: [
          { key: CONVERSION_TYPE_KEYS.CONSTANT_VALUE, text: this.getText("ccTypesFixedDate") },
          {
            key: CONVERSION_TYPE_KEYS.ATTRIBUTE,
            text: this.getText("ccTypesDimensionsWithNum", ["0"]),
          },
          { key: CONVERSION_TYPE_KEYS.CURRENT_DATE, text: this.getText("ccTypesCurrentDate") },
          {
            key: CONVERSION_TYPE_KEYS.VARIABLES,
            text: this.getText("ccTypesVariablesWithNum", ["0"]),
          },
        ],
        referenceDateType: CONVERSION_TYPE_KEYS.CONSTANT_VALUE,
      },
      errorHandling: [
        { key: AnalyticModelErrorHandling.null, text: this.getText("CC_setToNull") },
        { key: AnalyticModelErrorHandling.fail, text: this.getText("CC_failOnError") },
        { key: AnalyticModelErrorHandling.copyOriginal, text: this.getText("CC_keepUnconverted") },
      ],
      conversionTypeDialog: {
        title: this.getText("CC_conversionTypeDialogTitle", ["0"]),
        conversionType: CONVERSION_TYPE_KEYS.CONSTANT_VALUE,
        conversionTypeTypes: [
          { key: CONVERSION_TYPE_KEYS.CONSTANT_VALUE, text: this.getText("ccTypesFixedValuesWithNum", ["0"]) },
          { key: CONVERSION_TYPE_KEYS.ATTRIBUTE, text: this.getText("ccTypesDimensionsWithNum", ["0"]) },
          {
            key: CONVERSION_TYPE_KEYS.VARIABLES,
            text: this.getText("ccTypesVariablesWithNum", ["0"]),
          },
        ],
      },
    };
    this.unitConversionData = {
      isOkEnabled: false,
      unitConversionTablesNotAvailable: false,
      unitConversionTablesEmpty: false,
      sourceVariable: {
        key: "",
        text: "",
      },
      clientID: [],
      targetUnitDialog: {
        title: this.getText("UC_targetCurrencyDialogTitle"),
        unitTypes: [
          { key: CONVERSION_TYPE_KEYS.CONSTANT_VALUE, text: this.getText("ccTypesFixedValuesWithNum", ["0"]) },
          { key: CONVERSION_TYPE_KEYS.ATTRIBUTE, text: this.getText("ccTypesDimensionsWithNum", ["0"]) },
          {
            key: CONVERSION_TYPE_KEYS.VARIABLES,
            text: this.getText("ccTypesVariablesWithNum", ["0"]),
          },
        ],
        unitType: CONVERSION_TYPE_KEYS.CONSTANT_VALUE,
      },
      errorHandling: [
        { key: AnalyticModelErrorHandling.null, text: this.getText("CC_setToNull") },
        { key: AnalyticModelErrorHandling.fail, text: this.getText("CC_failOnError") },
        { key: AnalyticModelErrorHandling.copyOriginal, text: this.getText("CC_keepUnconverted") },
      ],
    };
  }

  // @overrides
  public validationListener(channel: string, eventName: string, validationMessages: ValidationMessage[]) {
    if (
      channel === AnalyticModelEventBusChannels.ANALYTIC_MODEL_VALIDATOR &&
      eventName === AnalyticModelEventBusEvents.VALIDATOR_EXECUTED
    ) {
      this.setUIModelValidations(validationMessages);
    }
  }

  /**
   * Set properties for fields to be highlighted in the view based on the ValidationMessages
   * @param validationMessages
   */
  private setUIModelValidations(validationMessages?: ValidationMessage[]) {
    if (!validationMessages) {
      validationMessages = this.uiModel.getProperty("/validationMessages");
    }
    const currentMeasureKey = this.uiModel.getProperty("/measureDetails/technicalName");
    const currentContexts = [`/measures/${encodeURIComponent(currentMeasureKey)}/`, `/nonCumulativeSettings/`];
    const currentContextValidationMessages = validationMessages.filter((message) =>
      currentContexts.some((currentContext) => message.propertyPath?.includes(currentContext))
    );
    // Update validation messages by additionalPath for measures
    const additionalPropertyPaths: ValidationMessage[] = [];
    validationMessages.forEach((message: ValidationMessage) => {
      if (message.additionalPropertyPath && message.additionalPropertyPath.length > 0) {
        const additionalValidationMessages = message.additionalPropertyPath.filter((path) =>
          currentContexts.some((currentContext) => path.startsWith(`${currentContext}`))
        );
        if (additionalValidationMessages.length > 0) {
          additionalPropertyPaths.push(message);
        }
      }
    });
    this.uiModel.setProperty("/measureDetails/validationMessages", {});
    (additionalPropertyPaths ?? []).forEach((message) => {
      (message?.additionalPropertyPath ?? []).forEach((path) => {
        currentContexts.forEach((currentContext) => {
          if (!path.startsWith(currentContext)) {
            return;
          }
          const field = path.split(currentContext).pop();
          this.updateMeasureDetailsValidationMessage(field, message);
        });
      });
    });
    for (const message of currentContextValidationMessages) {
      currentContexts.forEach((currentContext) => {
        if (!message.propertyPath.startsWith(currentContext)) {
          return;
        }
        const field = message.propertyPath.split(currentContext).pop();
        this.updateMeasureDetailsValidationMessage(field, message);
      });
    }
  }

  private updateMeasureDetailsValidationMessage(field: string, message: ValidationMessage) {
    if (field.includes("/")) {
      const fieldPrefix = field.split("/").at(0);
      if (!this.uiModel.getProperty(`/measureDetails/validationMessages/${fieldPrefix}`)) {
        this.uiModel.setProperty(`/measureDetails/validationMessages/${fieldPrefix}`, {});
      }
    }

    const valueState = message.type;
    const valueStateText = this.getText(message.descriptionKey, [...message.parameters]);
    this.uiModel.setProperty(`/measureDetails/validationMessages/${field}ValueState`, valueState);
    this.uiModel.setProperty(`/measureDetails/validationMessages/${field}ValueStateText`, valueStateText);
  }

  // *** Methods called initially to prepare the view display ***
  public async onBeforeShow(): Promise<void> {
    DebugService.getInstance().addOnBeforeShowStartLog(this.model, this.uiModel, AnalyticModelObjectType.MEASURE);
    super.onBeforeShow();
    // called each time before the Measure Details view is displayed
    this.getView().setBusy(true);
    await this.setMeasureDetailsFromModel();
    const technicalName = this.uiModel.getProperty("/measureDetails/technicalName");
    const modelMeasure = this.model.getMeasure(technicalName);
    this.setUIModelValidations();
    if (this.shouldExceptionAggregationApply()) {
      this.setExceptionAggregationTypes();
    }

    if (this.featureflags.DWCO_MODELING_AM_FORMAT_OF_MEASURES) {
      this.setFormatOnBeforeShow(modelMeasure.formatting);
    }

    this.measureModel.setProperty("/isNewSourceInputEnable", false);
    this.measureModel.setProperty("/aggregationTypeVisible", false);
    this.measureModel.setProperty("/exceptionAggregationVisible", true);
    if (this.isCalculatedMeasure()) {
      (this.byId("calculatedMeasureExpressionEditor") as ExpressionEditor).setMessageStripVisible(false);
      await this.validateExpressionAfterEditorLoads();
    } else if (this.isNonCumulativeMeasure()) {
      this.measureModel.setProperty("/exceptionAggregationVisible", false);
      const recordTypeFields = this.getRecordTypeFields();
      const timeDimensionFields = this.getTimeDimensionFields();
      const nonCumulativeEnabled = recordTypeFields.length > 0 && timeDimensionFields.length > 0;
      this.uiModel.setProperty("/nonCumulativeEnabled", nonCumulativeEnabled);
      if (this.uiModel.getProperty("/nonCumulativeEnabled")) {
        this.getExceptionAggregationNcumTypes();
        this.measureModel.setProperty(
          "/setUnbookedDeltaToZero",
          (modelMeasure as IAnalyticModelNonCumulativeMeasure).setUnbookedDeltaToZero
        );
        this.measureModel.setProperty(
          "/exceptionAggregationNcumType",
          (modelMeasure as IAnalyticModelNonCumulativeMeasure).exceptionAggregationNcumType
        );
        const unbookedDeltaHandlingCheckBox = this.byId("unbookedDeltaHandlingCheckBox") as sap.m.CheckBox;
        unbookedDeltaHandlingCheckBox.setSelected(this.measureModel.getProperty("/setUnbookedDeltaToZero"));
        this.prepareNonCumulative();
      }
    }
    if (this.isRestrictedMeasure() || this.isCurrencyConversionMeasure() || this.isUnitConversionMeasure()) {
      if (this.isRestrictedMeasure()) {
        (this.byId("restrictedMeasureExpressionEditor") as ExpressionEditor).setMessageStripVisible(false);
        await this.validateExpressionAfterEditorLoads();
      }
      const aggregationTypeSelect = this.byId("measureDetailsAggregationType") as sap.m.Select;
      const sourceMeasures = this.model.getMeasures();
      const sourceMeasureKey = (modelMeasure as IAnalyticModelRestrictedMeasure).key;
      if (Object.keys(sourceMeasures).includes(sourceMeasureKey)) {
        this.measureModel.setProperty("/selectedSourceMeasure", sourceMeasureKey);
        this.setAggregationTypeVisibility(sourceMeasureKey);
        const inheritedAggregationType = this.getFactSourceMeasureAggregationText(
          this.model.getMeasure(sourceMeasureKey) as IAnalyticModelSourceMeasure
        );
        this.getAggregationTypes(inheritedAggregationType);
        const selectedKey = modelMeasure?.hasOwnProperty("aggregation") ? modelMeasure.aggregation : "Inherited";
        aggregationTypeSelect.setSelectedKey(selectedKey);
      } else {
        this.measureModel.setProperty("/selectedSourceMeasure", sourceMeasureKey);
      }
      this.setExceptionAggregationVisibility(sourceMeasureKey);
    }

    this.measureModel.setProperty("/derivable", this.uiModel.getProperty("/measureDetails/derivable"));

    this.clearExpressionEditor();
    this.getView().setBusy(false);
    DebugService.getInstance().addOnBeforeShowEndLog(this.model, this.uiModel, AnalyticModelObjectType.MEASURE);
  }

  public async setMeasureDetailsFromModel() {
    // sets the values from the model (default values in case of create) in the measure details fields
    const technicalName = this.uiModel.getProperty("/measureDetails/technicalName");
    const measureDetails = this.model.getMeasure(technicalName);

    this.setColumnsAndSuggestions();
    const businessNameInput = this.byId("measureDetailsBusinessName") as sap.m.Input;
    businessNameInput.setValue(measureDetails.text);
    const technicalNameInput = this.byId("measureDetailsTechnicalName") as sap.m.Input;
    technicalNameInput.setValue(technicalName);
    const isAuxiliary = this.byId("isAuxiliaryCheckBox") as sap.m.CheckBox;
    isAuxiliary.setSelected(measureDetails.isAuxiliary);
    this.measureModel.setProperty("/selectedSourceMeasure", (measureDetails as any).key);
    // adding Exception Aggregation properties for Calculated/Restricted and FactSource measures
    if (this.shouldExceptionAggregationApply()) {
      this.measureModel.setProperty("/exceptionAggregationType", (measureDetails as any).exceptionAggregationType);
      if ((measureDetails as any).exceptionAggregationType === AnalyticModelExceptionAggregationType.NONE) {
        this.measureModel.setProperty("/exceptionAggregationAttributes", []);
      } else {
        this.measureModel.setProperty(
          "/exceptionAggregationAttributes",
          (measureDetails as any).exceptionAggregationAttributes
        );
      }
    }
    if (this.isCountDistinctMeasure()) {
      this.measureModel.setProperty(
        "/exceptionAggregationAttributes",
        (measureDetails as IAnalyticModelCountDistinctMeasure).countDistinctAttributes
      );
    }
    // setup currency conversion OR unit conversion measure
    if (this.isCurrencyConversionMeasure() || this.isUnitConversionMeasure()) {
      const sourceMeasureSemanticType = ((this.measureModelData.sourceMeasures as any[]) ?? []).find(
        (s) =>
          s.key ===
          (measureDetails as IAnalyticModelCurrencyConversionMeasure | IAnalyticModelUnitConversionMeasure).key
      )?.semanticType;
      await this.initCurrencyUnitConversion(sourceMeasureSemanticType);
      this.setConversionFromModel(measureDetails, sourceMeasureSemanticType);
    }
    if (this.isCalculatedMeasure() || this.isRestrictedMeasure()) {
      const attributes = this.model.getAttributes();
      const clonedAttributes = [];
      for (const attribute in attributes) {
        clonedAttributes.push({
          key: attribute,
          text: attributes[attribute].text,
        });
      }
      this.measureModel.setProperty("/clonedAttributes", clonedAttributes);

      const expressionInput: ExpressionEditor = this.isRestrictedMeasure()
        ? this.byId("restrictedMeasureExpressionEditor")
        : this.byId("calculatedMeasureExpressionEditor");
      let formulaString = "";
      if (!!(measureDetails as IAnalyticModelRestrictedMeasure | IAnalyticModelCalculatedMeasure).formulaRaw) {
        formulaString = (measureDetails as IAnalyticModelRestrictedMeasure | IAnalyticModelCalculatedMeasure)
          .formulaRaw;
      } else if (!!(measureDetails as IAnalyticModelRestrictedMeasure | IAnalyticModelCalculatedMeasure).formula) {
        formulaString = CsnXprToAMHelper.convertFormulaToString(
          measureDetails as IAnalyticModelRestrictedMeasure | IAnalyticModelCalculatedMeasure
        );
      }
      expressionInput.setExpression(formulaString);
      if (this.isRestrictedMeasure()) {
        const aggregationTypeSelect = this.byId("measureDetailsAggregationType") as sap.m.Select;
        aggregationTypeSelect.setSelectedKey(measureDetails.aggregation);
        aggregationTypeSelect.setEditable(true);
        const constantSelectionType = (measureDetails as IAnalyticModelRestrictedMeasure).constantSelectionType;
        const constantSelectionAttributes = (measureDetails as IAnalyticModelRestrictedMeasure)
          .constantSelectionAttributes;
        this.measureModel.setProperty(
          "/constantSelectionVisible",
          constantSelectionType === AnalyticModelConstantSelectionType.Selected
        );
        const constantSelectionTypeSelect = this.byId("constantSelectionType") as sap.m.Select;
        if (constantSelectionType === AnalyticModelConstantSelectionType.Selected) {
          constantSelectionTypeSelect.setSelectedKey("selected");
          const constantSelectionCombobox = this.byId("constantSelectionDimensions") as sap.m.MultiComboBox;
          if (!!constantSelectionAttributes) {
            constantSelectionCombobox.setSelectedKeys(constantSelectionAttributes);
          } else {
            constantSelectionCombobox.setSelectedKeys([]);
          }
        } else if (constantSelectionType === AnalyticModelConstantSelectionType.All) {
          constantSelectionTypeSelect.setSelectedKey("all");
        } else {
          constantSelectionTypeSelect.setSelectedKey("none");
        }
      }
    } else {
      if (this.isFactSourceMeasure()) {
        const allSourceDataEntityDetails: IDataEntityDetailsResponse =
          this.uiModel.getProperty("/allSourceDataEntityDetails");
        const measureDataEntityDetails = getMeasureFromDataEntityDetails(
          this.model,
          allSourceDataEntityDetails,
          (measureDetails as IAnalyticModelSourceMeasure).key
        );
        const inheritedAggregationType = measureDataEntityDetails ? measureDataEntityDetails.aggregation : "";
        this.getAggregationTypes(inheritedAggregationType);
        const aggregationTypeSelect = this.byId("measureDetailsAggregationType") as sap.m.Select;
        const selectedKey = measureDetails.hasOwnProperty("aggregation") ? measureDetails.aggregation : "Inherited";
        aggregationTypeSelect.setSelectedKey(selectedKey);
        const source = getSourceForMeasure(measureDetails as IAnalyticModelSourceMeasure, this.model);
        const sourceDetails = allSourceDataEntityDetails[source.dataEntity.key];
        const sourceBusinessName = sourceDetails?.businessName ?? source.dataEntity.key;
        const sourceName =
          User.getInstance().getObjectNameDisplay() === ObjectNameDisplay.technicalName
            ? source.dataEntity.key
            : sourceBusinessName;
        this.measureModel.setProperty("/sourceName", sourceName);
        const sourceFieldName =
          User.getInstance().getObjectNameDisplay() === ObjectNameDisplay.technicalName
            ? measureDataEntityDetails?.key
            : measureDataEntityDetails?.text;
        this.measureModel.setProperty("/sourceField", sourceFieldName);
        const sourceIcon = getIconForModel(sourceDetails?.technicalType as RepositoryObjectType);
        this.measureModel.setProperty("/sourceIcon", sourceIcon);
        const sourceLink = await getSourceObjectLink(source.dataEntity.key, this.getSpaceName());
        this.measureModel.setProperty("/sourceLink", sourceLink);
      }
      this.uiModel.setProperty("/measureDetails/formulaValidation", []);
    }
    let usedInList = [];
    usedInList = this.dependencies.getUsedInListForMeasure(technicalName);
    this.measureModel.setProperty("/usedIn", usedInList);
  }

  private setColumnsAndSuggestions() {
    this.setColumns();
    this.setSuggestions();
    this.measureModel.refresh(true);
  }

  private setColumns() {
    this.setMeasuresColumn();
    this.setAttributesColumn();
    this.setVariablesColumn();
  }

  private setMeasuresColumn() {
    const allSourceDataEntityDetails = this.uiModel.getProperty("/allSourceDataEntityDetails");
    const currentMeasure = this.uiModel.getProperty("/measureDetails/technicalName");
    const measureType = this.uiModel.getProperty("/measureDetails/measureType");
    const filteredMeasures = getSourceMeasuresAvailableForMeasure(
      this.model,
      allSourceDataEntityDetails,
      currentMeasure,
      measureType,
      this.featureflags
    );

    // Used in Calculated Measure
    this.measureModel.setProperty("/measureColumns", filteredMeasures);
    // Used in Non-Cumulative Measure, Currency Conversion Measure and Restricted Measure
    this.measureModel.setProperty("/sourceMeasures", filteredMeasures);
  }

  private getAttributesMissingFromMeasure() {
    const measureTechnicalName = this.uiModel.getProperty("/measureDetails/technicalName");
    const measure = this.model.getMeasure(measureTechnicalName);
    if (!measure) {
      return [];
    }

    if (measure.measureType === AnalyticModelMeasureType.NonCumulativeMeasure) {
      return [];
    }

    let attributes = [];

    const exceptionAggregationAttributes = measure.exceptionAggregationAttributes ?? [];

    let constantSelectionAttributes = [];
    if (measure.measureType === AnalyticModelMeasureType.RestrictedMeasure) {
      const constantSelectionSelected = measure.constantSelectionType === AnalyticModelConstantSelectionType.Selected;
      if (constantSelectionSelected) {
        constantSelectionAttributes = measure.constantSelectionAttributes ?? [];
      }
    }

    let countDistinctAttributes = [];
    if (measure.measureType === AnalyticModelMeasureType.CountDistinct) {
      countDistinctAttributes = measure.countDistinctAttributes ?? [];
    }
    attributes = [...exceptionAggregationAttributes, ...constantSelectionAttributes, ...countDistinctAttributes];
    return attributes.filter((attribute) => !this.model.attributeExists(attribute));
  }

  private setAttributesColumn() {
    const measureType = this.uiModel.getProperty("/measureDetails/measureType");
    if (
      measureType === AnalyticModelMeasureType.RestrictedMeasure ||
      measureType === AnalyticModelMeasureType.CalculatedMeasure ||
      measureType === AnalyticModelMeasureType.CurrencyConversionMeasure ||
      measureType === AnalyticModelMeasureType.UnitConversionMeasure ||
      measureType === AnalyticModelMeasureType.CountDistinct ||
      measureType === AnalyticModelMeasureType.FactSourceMeasure
    ) {
      const attributeKeysToAdd = this.getAttributesMissingFromMeasure();
      const attributeColumn = this.getAttributesColumnList(attributeKeysToAdd);
      this.measureModel.setProperty("/attributeColumns", attributeColumn);

      if (
        measureType === AnalyticModelMeasureType.CurrencyConversionMeasure ||
        measureType === AnalyticModelMeasureType.UnitConversionMeasure
      ) {
        const {
          targetCurrencySourceDimensions: targetSourceDimensions,
          targetUnitSourceDimensions: targetUnitSourceDimensions,
          referenceDateDimensions,
        } = this.getConversionDimensionColumnList();
        this.measureModel.setProperty("/referenceDateDimensions", referenceDateDimensions);
        this.measureModel.setProperty("/targetDimensions", targetSourceDimensions);
        if (this.featureflags.DWCO_MODELING_AM_UNIT_CONVERSION) {
          this.measureModel.setProperty("/targetUnitDimensions", targetUnitSourceDimensions);
        }
      }
    }
  }

  private setVariablesColumn() {
    const measureType = this.uiModel.getProperty("/measureDetails/measureType");

    if (measureType === AnalyticModelMeasureType.NonCumulativeMeasure) {
      return;
    }

    if (measureType === AnalyticModelMeasureType.RestrictedMeasure) {
      const filterVariables = this.getFilterVariablesColumnList();
      this.measureModel.setProperty("/variableColumns", filterVariables);
      return;
    }

    const inputVariables = this.getInputVariablesColumnList();
    this.measureModel.setProperty("/sourceVariableColumns", inputVariables);
  }

  private getConversionDimensionColumnList() {
    const targetCurrencySourceDimensions = [];
    const targetUnitSourceDimensions = [];
    const referenceDateDimensions = [];
    const currencyConversionAttributes = this.uiModel.getProperty("/attributesByCurrencyDetail");
    for (const attribute in currencyConversionAttributes) {
      const attributeColumn: any = {};
      attributeColumn.key = attribute;
      this.setAttributeColumnText(attribute, currencyConversionAttributes, attributeColumn);
      attributeColumn.icon = undefined;
      attributeColumn.semanticType = currencyConversionAttributes[attribute].semanticType;
      attributeColumn.primitiveType = currencyConversionAttributes[attribute].primitiveType;
      if (currencyConversionAttributes[attribute].semanticType === SemanticType.CURRENCY_CODE) {
        targetCurrencySourceDimensions.push(attributeColumn);
      }
      if (currencyConversionAttributes[attribute].semanticType === SemanticType.UNIT_OF_MEASURE) {
        targetUnitSourceDimensions.push(attributeColumn);
      }
      if (currencyConversionAttributes[attribute].primitiveType === "cds.Date") {
        referenceDateDimensions.push(attributeColumn);
      }
    }
    return {
      targetCurrencySourceDimensions: targetCurrencySourceDimensions,
      targetUnitSourceDimensions: targetUnitSourceDimensions,
      referenceDateDimensions,
    };
  }

  private setSuggestions() {
    this.setMeasuresSuggestions();
    this.setAttributesSuggestions();
    this.setVariablesSuggestions();
  }

  private setMeasuresSuggestions() {
    const measureType = this.uiModel.getProperty("/measureDetails/measureType");
    if (measureType === AnalyticModelMeasureType.CalculatedMeasure) {
      const measures: Array<{ key: string }> = this.measureModel.getProperty("/measureColumns");
      this.measureModel.setProperty(
        "/measureSuggestions",
        measures.map((measure) => ({
          text: measure.key,
        }))
      );
    }
  }

  private setAttributesSuggestions() {
    const measureType = this.uiModel.getProperty("/measureDetails/measureType");
    if (
      measureType === AnalyticModelMeasureType.CalculatedMeasure ||
      measureType === AnalyticModelMeasureType.RestrictedMeasure
    ) {
      const attributes = this.measureModel.getProperty("/attributeColumns");
      this.setSuggestionsForAttributes(this.measureModel, attributes);
    }
  }

  private setVariablesSuggestions() {
    const measureType = this.uiModel.getProperty("/measureDetails/measureType");
    if (
      measureType === AnalyticModelMeasureType.CalculatedMeasure ||
      measureType === AnalyticModelMeasureType.RestrictedMeasure
    ) {
      let variables: Array<{ key: string; entry: string }>;
      if (measureType === AnalyticModelMeasureType.RestrictedMeasure) {
        variables = this.measureModel.getProperty("/variableColumns");
      } else {
        variables = this.measureModel.getProperty("/sourceVariableColumns");
      }

      this.setSuggestionsForVariables(this.measureModel, variables);
    }
  }

  private getSelectedMeasureName() {
    return this.uiModel.getProperty("/measureDetails/technicalName");
  }

  /**
   * Currency/Unit Conversion preparation
   */
  private async initCurrencyUnitConversion(sourceMeasureSemanticType: string) {
    this.measureModel.setProperty("/currencyConversionData", this.currencyConversionData);
    if (this.featureflags.DWCO_MODELING_AM_UNIT_CONVERSION) {
      this.measureModel.setProperty("/unitConversionData", this.unitConversionData);
    }
    const conversionPropertyValue =
      sourceMeasureSemanticType === SemanticType.QUANTITY_WITH_UNIT ? CONVERSION_TYPES.UNIT : CONVERSION_TYPES.CURRENCY;
    if (
      this.getIsNewMeasure() ||
      this.conversionDialogValuesUndefinedOrEmpty(
        this.measureModel.getProperty(`/${conversionPropertyValue}ConversionData/clientID`)
      )
    ) {
      /* retrieve currency conversion selection values */
      await this.setupConversionDialog(
        CONVERSION_TYPE_PROPERTIES.CLIENT,
        conversionPropertyValue,
        undefined,
        this.getIsNewMeasure()
      );
    }
  }

  /**
   * Set up currency/unit conversion values from TCUR/ T006 views
   * @param conversionType
   * @returns
   */
  private async setupConversionDialog(
    conversionType: CONVERSION_TYPE_PROPERTIES,
    conversionPropertyValue: CONVERSION_TYPES,
    constantValueContent?: sap.ui.core.Control,
    newMeasure: boolean = false
  ) {
    const upperCaseConversionTypeValue =
      conversionPropertyValue[0].toUpperCase() + conversionPropertyValue.slice(1, conversionPropertyValue.length);
    const selectedClientID = this.measureModel.getProperty(
      `/${conversionPropertyValue}ConversionData/selectedClientID`
    );
    const columnFound = [CurrencyUnitConversionViewsFieldNames.MANDT];
    if (conversionType === CONVERSION_TYPE_PROPERTIES.TARGET_CURRENCY) {
      columnFound.push(CurrencyUnitConversionViewsFieldNames.WAERS);
    } else if (conversionType === CONVERSION_TYPE_PROPERTIES.CONVERSION_TYPE) {
      columnFound.push(CurrencyUnitConversionViewsFieldNames.KURST);
    } else if (conversionType === CONVERSION_TYPE_PROPERTIES.TARGET_UNIT) {
      columnFound.push(CurrencyUnitConversionViewsFieldNames.MSEHI);
      columnFound.push(CurrencyUnitConversionViewsFieldNames.MSEHL);
    }

    const filters = [
      {
        fieldName: CurrencyUnitConversionViewsFieldNames.MANDT,
        conditions: [
          {
            sign: "I",
            option: "icp",
            low: selectedClientID,
          },
        ],
      },
    ];
    try {
      let values = [];
      if (newMeasure && conversionType === CONVERSION_TYPE_PROPERTIES.CLIENT) {
        values = this.uiModel.getProperty(`/${conversionPropertyValue}Values`);
        if (this.conversionDialogValuesUndefinedOrEmpty(values)) {
          throw new Error(`Failed to retrieve ${conversionPropertyValue} column values`);
        }
      } else {
        // Because annotations are needed, we will fetch T006A instead of T006
        const usedView =
          conversionType === CONVERSION_TYPE_PROPERTIES.CONVERSION_TYPE
            ? CurrencyConversionViews.TCURV
            : conversionPropertyValue === CONVERSION_TYPES.UNIT
            ? UnitConversionViews.T006A
            : CurrencyConversionViews.TCURC;

        values = await valueHelpService(
          columnFound,
          conversionType === CONVERSION_TYPE_PROPERTIES.CLIENT ? [] : filters,
          undefined,
          super.getSpaceName(),
          usedView,
          conversionType === CONVERSION_TYPE_PROPERTIES.CLIENT ? true : false,
          1000
        );
        this.uiModel.setProperty(`/${conversionPropertyValue}Values`, values);
      }

      if (conversionType === CONVERSION_TYPE_PROPERTIES.CLIENT) {
        const conversionValues = this.setConversionValueModel(
          values,
          CurrencyUnitConversionViewsFieldNames.MANDT,
          conversionPropertyValue === CONVERSION_TYPES.CURRENCY
            ? AnalyticModelMeasureType.CurrencyConversionMeasure
            : AnalyticModelMeasureType.UnitConversionMeasure
        );
        if (conversionValues.length > 0) {
          this.measureModel.setProperty(
            `/${conversionPropertyValue}ConversionData/selectedClientID`,
            conversionValues[0].key
          );
        }
        if (this.isClientIdEmpty()) {
          this.measureModel.setProperty(`/${conversionPropertyValue}ConversionData/clientID`, conversionValues);
        }
      } else {
        const usedTargetProperty =
          conversionType === CONVERSION_TYPE_PROPERTIES.TARGET_CURRENCY
            ? CurrencyUnitConversionViewsFieldNames.WAERS
            : conversionType === CONVERSION_TYPE_PROPERTIES.TARGET_UNIT
            ? CurrencyUnitConversionViewsFieldNames.MSEHI //Key
            : CurrencyUnitConversionViewsFieldNames.KURST;

        const conversionValues = this.setConversionValueModel(
          values,
          usedTargetProperty,
          conversionPropertyValue === CONVERSION_TYPES.CURRENCY
            ? AnalyticModelMeasureType.CurrencyConversionMeasure
            : AnalyticModelMeasureType.UnitConversionMeasure,
          conversionType === CONVERSION_TYPE_PROPERTIES.TARGET_UNIT
            ? CurrencyUnitConversionViewsFieldNames.MSEHL // Value
            : undefined
        );

        this.measureModel.setProperty(
          `/${conversionPropertyValue}ConversionData/${conversionType}Dialog/objects`,
          conversionValues
        );

        const fixedValues: any[] = this.measureModel.getProperty(
          `/${conversionPropertyValue}ConversionData/${conversionType}Dialog/objects`
        );
        if (
          conversionType === CONVERSION_TYPE_PROPERTIES.TARGET_CURRENCY ||
          conversionType === CONVERSION_TYPE_PROPERTIES.TARGET_UNIT
        ) {
          this.measureModel.setProperty(
            `/${conversionPropertyValue}ConversionData/target${upperCaseConversionTypeValue}Dialog/${conversionPropertyValue}Types/0/text`,
            this.getText("ccTypesFixedValuesWithNum", [(fixedValues ? fixedValues.length : 0).toString()])
          );
        } else {
          this.measureModel.setProperty(
            `/currencyConversionData/conversionTypeDialog/conversionTypeTypes/0/text`,
            this.getText("ccTypesFixedValuesWithNum", [(fixedValues ? fixedValues.length : 0).toString()])
          );
        }
      }
    } catch (e) {
      this.uiModel.setProperty(`/${conversionPropertyValue}Values`, undefined);
      this.measureModel.setProperty(
        `/${conversionPropertyValue}ConversionData/${conversionPropertyValue}ConversionTablesNotAvailable`,
        true
      );
      const errorMessage = e.responseJSON?.error?.message ?? e.message;
      Logger.logError(`Failed to retrieve ${conversionPropertyValue} column values`, errorMessage);
    } finally {
      constantValueContent?.setBusy(false) ?? this.getView()?.setBusy(false);
    }
  }

  private setConversionValueModel(
    result,
    keyColumn: CurrencyUnitConversionViewsFieldNames,
    measureType: AnalyticModelMeasureType.CurrencyConversionMeasure | AnalyticModelMeasureType.UnitConversionMeasure,
    valueColumn?: CurrencyUnitConversionViewsFieldNames
  ) {
    const values = [];
    for (const item of result.value) {
      const updatedVal = item[keyColumn];
      values.push(this.getConversionValueObjFromValueHelpResult(updatedVal, item[valueColumn]));
    }
    if (values && values.length === 0) {
      const conversionTypeValue =
        measureType === AnalyticModelMeasureType.CurrencyConversionMeasure
          ? CONVERSION_TYPES.CURRENCY
          : CONVERSION_TYPES.UNIT;
      this.measureModel.setProperty(
        `/${conversionTypeValue}ConversionData/${conversionTypeValue}ConversionTablesEmpty`,
        true
      );
    }
    return values;
  }

  private getConversionValueByConversionType(
    conversion: any,
    conversionType:
      | AnalyticModelTargetCurrencyType
      | AnalyticModelReferenceDateType
      | AnalyticModelConversionTypeType
      | AnalyticModelTargetUnitType
  ): string {
    let conversionValue = "";
    if (conversionType && conversionType.includes("AnalyticModelTargetCurrencyType")) {
      switch (conversionType) {
        case AnalyticModelTargetCurrencyType.constantValue:
          conversionValue = (conversion as IAnalyticModelConstantValue).value;
          return conversionValue;
        case AnalyticModelTargetCurrencyType.attribute:
          conversionValue = (conversion as IAnalyticModelAttributeKey).key;
          return conversionValue;
        case AnalyticModelTargetCurrencyType.variable:
          conversionValue = (conversion as IAnalyticModelVariableKey).key;
          return conversionValue;
      }
    } else if (conversionType && conversionType.includes("AnalyticModelReferenceDateType")) {
      switch (conversionType) {
        case AnalyticModelReferenceDateType.constantValue:
          conversionValue = (conversion as IAnalyticModelConstantValue).value;
          return conversionValue;
        case AnalyticModelReferenceDateType.attribute:
          conversionValue = (conversion as IAnalyticModelAttributeKey).key;
          return conversionValue;
        case AnalyticModelReferenceDateType.variable:
          conversionValue = (conversion as IAnalyticModelVariableKey).key;
          return conversionValue;
        case AnalyticModelReferenceDateType.sqlFunction:
          conversionValue = (conversion as IAnalyticModelSqlFunctionName).functionName;
          return conversionValue;
      }
    } else if (conversionType && conversionType.includes("AnalyticModelConversionTypeType")) {
      switch (conversionType) {
        case AnalyticModelConversionTypeType.constantValue:
          conversionValue = (conversion as IAnalyticModelConstantValue).value;
          return conversionValue;
        case AnalyticModelConversionTypeType.attribute:
          conversionValue = (conversion as IAnalyticModelAttributeKey).key;
          return conversionValue;
        case AnalyticModelConversionTypeType.variable:
          conversionValue = (conversion as IAnalyticModelVariableKey).key;
          return conversionValue;
      }
    } else if (conversionType && conversionType.includes("AnalyticModelTargetUnitType")) {
      switch (conversionType) {
        case AnalyticModelTargetUnitType.constantValue:
          conversionValue = (conversion as IAnalyticModelConstantValue).value;
          return conversionValue;
        case AnalyticModelTargetUnitType.attribute:
          conversionValue = (conversion as IAnalyticModelAttributeKey).key;
          return conversionValue;
        case AnalyticModelTargetUnitType.variable:
          conversionValue = (conversion as IAnalyticModelVariableKey).key;
          return conversionValue;
      }
    }
    return conversionValue;
  }
  private setConversionNames(
    measureDetails: IAnalyticModelCurrencyConversionMeasure | IAnalyticModelUnitConversionMeasure
  ) {
    if (this.isCurrencyConversionMeasure()) {
      const targetCurrencyNames = this.getConversionNames(
        (measureDetails as IAnalyticModelCurrencyConversionMeasure).targetCurrency,
        (measureDetails as IAnalyticModelCurrencyConversionMeasure).targetCurrencyType
      );
      this.measureModel.setProperty(
        `/currencyConversionData/targetCurrencyDialog/technicalName`,
        targetCurrencyNames.currencyTechnicalName
      );
      this.measureModel.setProperty(
        `/currencyConversionData/targetCurrencyDialog/businessName`,
        targetCurrencyNames.currencyBusinessName
      );
      const referenceDateNames = this.getConversionNames(
        (measureDetails as IAnalyticModelCurrencyConversionMeasure).referenceDate,
        (measureDetails as IAnalyticModelCurrencyConversionMeasure).referenceDateType
      );
      this.measureModel.setProperty(
        `/currencyConversionData/referenceDateDialog/technicalName`,
        referenceDateNames.currencyTechnicalName
      );
      this.measureModel.setProperty(
        `/currencyConversionData/referenceDateDialog/businessName`,
        referenceDateNames.currencyBusinessName
      );
      const conversionTypeNames = this.getConversionNames(
        (measureDetails as IAnalyticModelCurrencyConversionMeasure).conversionType,
        (measureDetails as IAnalyticModelCurrencyConversionMeasure).conversionTypeType
      );
      this.measureModel.setProperty(
        `/currencyConversionData/conversionTypeDialog/technicalName`,
        conversionTypeNames.currencyTechnicalName
      );
      this.measureModel.setProperty(
        `/currencyConversionData/conversionTypeDialog/businessName`,
        conversionTypeNames.currencyBusinessName
      );
    } else if (this.isUnitConversionMeasure()) {
      const targetCurrencyNames = this.getConversionNames(
        (measureDetails as IAnalyticModelUnitConversionMeasure).targetUnit,
        (measureDetails as IAnalyticModelUnitConversionMeasure).targetUnitType
      );
      this.measureModel.setProperty(
        `/unitConversionData/targetUnitDialog/technicalName`,
        targetCurrencyNames.currencyTechnicalName
      );
      this.measureModel.setProperty(
        `/unitConversionData/targetUnitDialog/businessName`,
        targetCurrencyNames.currencyBusinessName
      );
    }
  }

  private setConversionFromModel(measureDetails, sourceMeasureSemanticType: string) {
    const conversionPropertyValue =
      sourceMeasureSemanticType === SemanticType.QUANTITY_WITH_UNIT ? CONVERSION_TYPES.UNIT : CONVERSION_TYPES.CURRENCY;

    let referenceDateValue = "";
    if (conversionPropertyValue === CONVERSION_TYPES.CURRENCY) {
      if (measureDetails.referenceDate && measureDetails.referenceDateType) {
        if (measureDetails.referenceDateType === AnalyticModelReferenceDateType.constantValue) {
          const dateValue = (measureDetails.referenceDate as IAnalyticModelConstantValue).value;
          if (!!dateValue) {
            referenceDateValue = [dateValue.slice(0, 4), "-", dateValue.slice(4, 6), "-", dateValue.slice(6, 8)].join(
              ""
            );
            this.measureModel.setProperty(
              "/currencyConversionData/referenceDateDialog/selectedDate",
              referenceDateValue
            );
          }
        } else {
          referenceDateValue = this.getConversionValueByConversionType(
            measureDetails.referenceDate,
            measureDetails.referenceDateType
          );
          this.measureModel.setProperty(
            "/currencyConversionData/referenceDateDialog/selectedDate",
            this.getText("CC_txtNoDateSelected")
          );
        }
      }
      this.measureModel.setProperty("/currencyConversionData/referenceDateDialog/technicalName", referenceDateValue);
      this.measureModel.setProperty(
        "/currencyConversionData/conversionTypeDialog/technicalName",
        this.getConversionValueByConversionType(measureDetails.conversionType, measureDetails.conversionTypeType)
      );
      this.measureModel.setProperty(
        "/currencyConversionData/targetCurrencyDialog/technicalName",
        this.getConversionValueByConversionType(measureDetails.targetCurrency, measureDetails.targetCurrencyType)
      );
      this.setConversionNames(measureDetails);
      this.measureModel.setProperty(
        `/currencyConversionData/targetCurrencyDialog/targetDimensions`,
        // eslint-disable-next-line @typescript-eslint/no-unsafe-call
        this.measureModel
          .getProperty("/targetDimensions")
          ?.filter((target) => target.semanticType === SemanticType.CURRENCY_CODE)
      );
    } else if (
      this.featureflags.DWCO_MODELING_AM_UNIT_CONVERSION &&
      conversionPropertyValue === CONVERSION_TYPES.UNIT
    ) {
      this.measureModel.setProperty(
        "/unitConversionData/targetUnitDialog/technicalName",
        this.getConversionValueByConversionType(measureDetails.targetUnit, measureDetails.targetUnitType)
      );
      this.setConversionNames(measureDetails);
      this.measureModel.setProperty(
        `/unitConversionData/targetUnitDialog/targetUnitDimensions`,
        // eslint-disable-next-line @typescript-eslint/no-unsafe-call
        this.measureModel
          .getProperty("/targetUnitDimensions")
          ?.filter((target) => target.semanticType === SemanticType.UNIT_OF_MEASURE)
      );
    }
    this.measureModel.setProperty(
      `/${conversionPropertyValue}ConversionData/selectedErrorHandling`,
      measureDetails.errorHandling ? measureDetails.errorHandling : AnalyticModelErrorHandling.null
    );
    this.measureModel.setProperty(
      `/${conversionPropertyValue}ConversionData/selectedClientID`,
      measureDetails.client
        ? measureDetails.client
        : this.measureModel.getProperty(`/${conversionPropertyValue}ConversionData/selectedClientID`)
    );
  }

  private prepareNonCumulative() {
    this.uiModel.setProperty("/recordTypeFields", this.getRecordTypeFields());
    this.uiModel.setProperty("/timeDimensionFields", this.getTimeDimensionFields());
  }

  // *** Action handlers for the Measure Details view controls ***
  public async onMeasureBusinessNameChange() {
    const oldTechnicalName = this.getSelectedMeasureName();
    const measureExists = this.model.measureExists(oldTechnicalName);
    if (!measureExists) {
      return;
    }

    const technicalNameInput = this.byId("measureDetailsTechnicalName") as sap.m.Input;
    const businessNameInput = this.byId("measureDetailsBusinessName") as sap.m.Input;

    // update technical name
    let newTechnicalName;
    let validationResult;
    if (this.getIsNewMeasure() && this.measureModel.getProperty("/derivable")) {
      validationResult = await this.onTechnicalNameChangeValidation();
      if (!validationResult.valid) {
        technicalNameInput.setValue(validationResult.oldTechnicalName);
        this.validateTechnicalName(true, technicalNameInput);
      }
    }

    const businessNameError = this.checkMeasureBusinessName(true);
    if (businessNameError) {
      const oldBusinessName = this.model.getMeasure(oldTechnicalName).text;
      businessNameInput.setValue(oldBusinessName);
      this.checkMeasureBusinessName(false);
      return;
    }

    const newBusinessName = businessNameInput.getValue().trim();
    businessNameInput.setValue(newBusinessName);

    if (validationResult?.valid) {
      newTechnicalName = validationResult.newTechnicalName;

      this.uiModel.setProperty("/measureDetails/technicalName", newTechnicalName);
    }

    // valid: update model
    const updateMeasureBusinessNameCommand = new UpdateMeasureBusinessName(
      oldTechnicalName,
      newBusinessName,
      newTechnicalName
    );
    this.stack.execute(updateMeasureBusinessNameCommand);
    const currentTextBreadcrumbs = {
      businessName: newBusinessName,
      technicalName: validationResult?.valid ? newTechnicalName : oldTechnicalName,
    };
    const links = this.uiModel.getProperty("/breadcrumbs/links");
    this.updateBreadcrumb(currentTextBreadcrumbs, links);
    await Util.Component.modelChangeHandler([ModelUIProperties.MEASURES]);
    if (newTechnicalName) {
      this.navToMeasureAfterTechnicalNameChange(newTechnicalName);
    }
  }

  public onMeasureBusinessNameLiveChange() {
    const businessNameInput = this.byId("measureDetailsBusinessName") as sap.m.Input;
    if (this.getIsNewMeasure() && this.measureModel.getProperty("/derivable")) {
      const businessName = businessNameInput.getValue().trim();
      const technicalNameInput = this.byId("measureDetailsTechnicalName") as sap.m.Input;
      const nameValidator = NamingHelper.getNameInputValidator();
      const newTechnicalName = nameValidator.deriveTechnicalName(
        businessName,
        getNameUsageForObject(AnalyticModelObjectType.MEASURE, this.model)
      );
      technicalNameInput.setValue(newTechnicalName);
    }
    this.checkMeasureBusinessName(false);
  }

  private getIsNewMeasure() {
    if (this.model.getDimensionHandlingCapability()) {
      const technicalName = this.uiModel.getProperty("/measureDetails/technicalName");
      return !this.stack.getInitialState().measureExists(technicalName);
    }
    return this.uiModel.getProperty("/measureDetails/newMeasure");
  }

  public checkMeasureBusinessName(returnState: boolean) {
    const businessNameInput = this.byId("measureDetailsBusinessName") as sap.m.Input;
    const businessName = businessNameInput.getValue().trim();

    // TODO no longer used, remove
    this.uiModel.setProperty("/measureDetails/measureBusinessName", businessNameInput.getValue());
    if (businessName === "") {
      businessNameInput.setValueState(sap.ui.core.ValueState.Error);
      businessNameInput.setValueStateText(this.getText("analyticMeasureBusinessNameEmpty"));
      businessNameInput.openValueStateMessage();
    } else {
      businessNameInput.setValueState(sap.ui.core.ValueState.None);
      businessNameInput.setValueStateText(undefined);
      businessNameInput.closeValueStateMessage();
    }

    if (returnState) {
      return businessNameInput.getValueState() !== sap.ui.core.ValueState.None;
    }
    // also validate technical name in case it was derived from business name
    if (this.measureModel.getProperty("/derivable")) {
      this.validateTechnicalName(true, this.byId("measureDetailsTechnicalName") as sap.m.Input);
    }
  }

  public async onMeasureTechnicalNameChange() {
    const validationResult = await this.onTechnicalNameChangeValidation();
    if (!validationResult.valid) {
      const technicalNameInput = this.byId("measureDetailsTechnicalName") as sap.m.Input;
      technicalNameInput.setValue(validationResult.oldTechnicalName);
      this.validateTechnicalName(true, technicalNameInput);
      return;
    }
    const oldTechnicalName = validationResult.oldTechnicalName;
    const newTechnicalName = validationResult.newTechnicalName;

    this.uiModel.setProperty("/measureDetails/technicalName", newTechnicalName);
    this.measureModel.setProperty("/derivable", false);

    const updateMeasureTechnicalNameCommand = new UpdateMeasureTechnicalName(oldTechnicalName, newTechnicalName);
    this.stack.execute(updateMeasureTechnicalNameCommand);
    const currentMeasureText = {
      businessName: this.model.getMeasure(newTechnicalName).text,
      technicalName: newTechnicalName,
    };

    const links = this.uiModel.getProperty("/breadcrumbs/links");
    this.updateBreadcrumb(currentMeasureText, links);
    await Util.Component.modelChangeHandler();

    this.navToMeasureAfterTechnicalNameChange(newTechnicalName, true);
  }

  private async onTechnicalNameChangeValidation(): Promise<{
    valid: boolean;
    oldTechnicalName: string;
    newTechnicalName: string;
  }> {
    const oldTechnicalName = this.uiModel.getProperty("/measureDetails/technicalName");
    const technicalNameInput = this.byId("measureDetailsTechnicalName") as sap.m.Input;
    const newTechnicalName = technicalNameInput.getValue();
    const response = {
      valid: false,
      oldTechnicalName,
      newTechnicalName,
    };
    // check if technical name was changed
    if (newTechnicalName === oldTechnicalName) {
      return response;
    }

    const technicalNameError = this.validateTechnicalName(true, technicalNameInput);
    if (technicalNameError) {
      return response;
    }

    if (!this.getIsNewMeasure()) {
      const isStackingEnabled = this.featureflags.DWCO_MODELING_ANALYTIC_MODEL_STACKING;
      const warningMessage = isStackingEnabled
        ? "change_measure_technical_name_warning_stacking"
        : "change_measure_technical_name_warning";
      const cancelled = await this.technicalNameChangeWarning(warningMessage);
      if (cancelled) {
        return response;
      }
    }
    const oldMeasure = this.model.getMeasure(oldTechnicalName);
    if (!oldMeasure) {
      return response;
    }

    response.valid = true;
    return response;
  }

  public onMeasureTechnicalNameLiveChange() {
    const technicalNameInput = this.byId("measureDetailsTechnicalName") as sap.m.Input;
    this.validateTechnicalName(true, technicalNameInput);
  }

  public navToMeasureAfterTechnicalNameChange(newTechnicalName: string, technicalNameInputWasUsed: boolean = false) {
    // measure can be closed, in that case dont reroute to it
    const panel = Util.Component.controller().getPanelInfo();
    const isMeasureOpen = panel.type === AnalyticModelObjectType.MEASURE;
    if (!isMeasureOpen) {
      return;
    }

    // keep new measure information
    this.uiModel.setProperty("/newProperty", this.getIsNewMeasure());
    this.uiModel.setProperty("/routingFromTechnicalNameUpdate", technicalNameInputWasUsed);
    this.uiModel.setProperty("/routingFromSameObject", true);
    Util.Component.router.navToMeasurePanel(newTechnicalName, true);
  }

  public async onExceptionAggregationNcumTypeChange() {
    const exceptionAggregationNcumSelect = this.byId("measureDetailsExceptionAggregationNcumSelect") as sap.m.Select;
    const exceptionAggregationNcumType =
      exceptionAggregationNcumSelect.getSelectedKey() as AnalyticModelExceptionAggregationNcumType;
    const measureTechnicalName = this.getSelectedMeasureName();
    const command = new UpdateExceptionAggregationNcumType(measureTechnicalName, exceptionAggregationNcumType);
    this.stack.execute(command);
    await Util.Component.modelChangeHandler([ModelUIProperties.MEASURES]);
  }

  public async onUnbookedDeltaHandlingChange() {
    const unbookedDeltaHandlingCheckBox = this.byId("unbookedDeltaHandlingCheckBox") as sap.m.CheckBox;
    const unbookedDeltaHandling = unbookedDeltaHandlingCheckBox.getSelected();
    const measureTechnicalName = this.getSelectedMeasureName();
    const measureExists = this.model.measureExists(measureTechnicalName);
    if (!!measureExists && unbookedDeltaHandling !== undefined) {
      const command = new UpdateUnbookedDeltaHandling(measureTechnicalName, unbookedDeltaHandling);
      this.stack.execute(command);
      await Util.Component.modelChangeHandler([ModelUIProperties.MEASURES]);
    }
  }

  public async onIsAuxiliaryChange() {
    const isAuxiliaryCheckBox = this.byId("isAuxiliaryCheckBox") as sap.m.CheckBox;
    const isAuxiliary = isAuxiliaryCheckBox.getSelected();
    const selectedMeasureTechnicalName = this.getSelectedMeasureName();
    const measureExists = this.model.measureExists(selectedMeasureTechnicalName);
    if (!!measureExists && isAuxiliary !== undefined) {
      const updateIsAuxiliaryCommand = new UpdateIsAuxiliary(selectedMeasureTechnicalName, isAuxiliary);
      this.stack.execute(updateIsAuxiliaryCommand);
      await Util.Component.modelChangeHandler([ModelUIProperties.MEASURES]);
    }
  }

  public async onSourceMeasureChange() {
    const sourceMeasureKey = this.measureModel.getProperty("/selectedSourceMeasure");
    const technicalName = this.uiModel.getProperty("/measureDetails/technicalName");
    const measureExists = this.model.measureExists(technicalName);
    let availableSourceMeasure;
    if (!!measureExists && !!sourceMeasureKey) {
      if (this.featureflags.DWCO_MODELING_AM_UNIT_CONVERSION) {
        if (this.measureModelData && this.measureModelData.sourceMeasures) {
          availableSourceMeasure = ((this.measureModelData?.sourceMeasures as any[]) ?? []).find(
            (s) => s.key === sourceMeasureKey
          );
        }
        if (this.isCurrencyConversionMeasure() || this.isUnitConversionMeasure()) {
          const measure = this.model.getMeasure(technicalName) as
            | IAnalyticModelCurrencyConversionMeasure
            | IAnalyticModelUnitConversionMeasure;
          await this.updateMeasureDetailBasedOnSemanticType(measure, availableSourceMeasure?.semanticType);
        }
      }
      if (!this.isNonCumulativeMeasure()) {
        this.setAggregationTypeVisibility(sourceMeasureKey);
        this.setExceptionAggregationVisibility(sourceMeasureKey);
        if (this.measureModel.getProperty("/aggregationTypeVisible")) {
          const inheritedAggregationType = this.getFactSourceMeasureAggregationText(
            this.model.getMeasure(sourceMeasureKey) as IAnalyticModelSourceMeasure
          );
          this.getAggregationTypes(inheritedAggregationType);
          const aggregationTypeSelect = this.byId("measureDetailsAggregationType") as sap.m.Select;
          aggregationTypeSelect.setSelectedKey("Inherited");
        }
      }
      if (
        this.featureflags.DWCO_MODELING_AM_UNIT_CONVERSION &&
        (this.isCurrencyConversionMeasure() || this.isUnitConversionMeasure())
      ) {
        const command = new UpdateDefaultConversionMeasure(technicalName, availableSourceMeasure);
        this.stack.execute(command);
      } else {
        const command = new UpdateSourceMeasure(technicalName, sourceMeasureKey);
        this.stack.execute(command);
      }
      await Util.Component.modelChangeHandler([ModelUIProperties.MEASURES]);
    }
  }

  /**
   * Initializes the Unit/Currency details when the source measure changes and update displayIcon & header displayName for measure
   * @param measure The measure to update
   * @param semanticType The semantic type of the measure
   */
  private async updateMeasureDetailBasedOnSemanticType(
    measure: IAnalyticModelCurrencyConversionMeasure | IAnalyticModelUnitConversionMeasure,
    semanticType: string
  ) {
    const isUnitConversion = semanticType === SemanticType.QUANTITY_WITH_UNIT;
    const measureType = isUnitConversion
      ? AnalyticModelMeasureType.UnitConversionMeasure
      : AnalyticModelMeasureType.CurrencyConversionMeasure;
    this.uiModel.setProperty("/measureDetails/measureType", measureType);
    this.uiModel.setProperty(
      "/header/displayIcon",
      isUnitConversion ? "sap-icon://SAP-icons-TNT/unit" : "sap-icon://lead"
    );
    const displayName = isUnitConversion
      ? this.getText("unitConversionMeasure")
      : this.getText("currencyConversionMeasure");
    this.uiModel.setProperty("/header/displayName", displayName);
    const conversionTypeValue = isUnitConversion ? CONVERSION_TYPES.UNIT : CONVERSION_TYPES.CURRENCY;
    await this.initCurrencyUnitConversion(semanticType);
    this.setConversionFromModel(measure, semanticType);
    this.measureModel.setProperty(
      `/${isUnitConversion ? "unit" : "currency"}ConversionData/selectedErrorHandling`,
      AnalyticModelErrorHandling.null
    );
    this.resetCurrencyAndUnitConversionPropertyDialog(conversionTypeValue);
  }

  public async onAggregationTypeChange() {
    const aggregationTypeSelect = this.byId("measureDetailsAggregationType") as sap.m.Select;
    const aggregationType = aggregationTypeSelect.getSelectedKey() as AnalyticModelAggregationTypes | "Inherited";
    const selectedMeasureTechnicalName = this.getSelectedMeasureName();
    const measureExists = this.model.measureExists(selectedMeasureTechnicalName);
    if (!!measureExists && !!aggregationType) {
      const updateAggregationCommand = new UpdateAggregation(selectedMeasureTechnicalName, aggregationType);
      this.stack.execute(updateAggregationCommand);
      await Util.Component.modelChangeHandler([ModelUIProperties.MEASURES]);
    }
  }

  public async onExceptionAggregationTypeChange() {
    const exceptionAggregationTypeSelect = this.byId("measureDetailsExceptionAggregationType") as sap.m.Select;
    let exceptionAggregationType = exceptionAggregationTypeSelect.getSelectedKey();
    const selectedMeasureTechnicalName = this.getSelectedMeasureName();
    const measureExists = this.model.measureExists(selectedMeasureTechnicalName);
    if (!!measureExists && !!exceptionAggregationType) {
      if (exceptionAggregationType === this.getText("exception_aggregation_type_none_description")) {
        exceptionAggregationType = AnalyticModelExceptionAggregationType.NONE;
        this.measureModel.setProperty("/exceptionAggregationAttributes", []);
        (this.byId("measureDetailsAttributes") as sap.m.MultiComboBox).setVisible(false);
      }
      const updateExceptionAggregationTypeCommand = new UpdateExceptionAggregationType(
        selectedMeasureTechnicalName,
        exceptionAggregationType as AnalyticModelExceptionAggregationType
      );
      this.stack.execute(updateExceptionAggregationTypeCommand);
    }
    await Util.Component.modelChangeHandler([ModelUIProperties.MEASURES]);
  }

  public async onChangeMeasureScaleType(event: IEvent<sap.m.Select, any>) {
    const key = event.getSource().getSelectedKey();
    const newValue = key ? Number(key) : undefined;
    await this.updateScaleType(newValue);
  }

  public async onChangeMeasureDecimalPlaces(event: IEvent<sap.m.Select, any>) {
    const key = event.getSource().getSelectedKey();
    const newValue = key ? Number(key) : undefined;
    await this.updateDecimalPlaces(newValue);
  }

  public async updateDecimalPlaces(newValue: number) {
    const updateMeasureDecimalPlacesCommand = new UpdateMeasureDecimalPlaces(this.getSelectedMeasureName(), newValue);
    this.stack.execute(updateMeasureDecimalPlacesCommand);
    await Util.Component.modelChangeHandler([ModelUIProperties.MEASURES]);
  }

  public async updateScaleType(newValue: number) {
    const updateMeasureScaleCommand = new UpdateMeasureScale(this.getSelectedMeasureName(), newValue);
    this.stack.execute(updateMeasureScaleCommand);
    await Util.Component.modelChangeHandler([ModelUIProperties.MEASURES]);
  }

  public setFormatOnBeforeShow(formatting: IAnalyticModelFormatting) {
    const scaleType = formatting?.scaleType;
    const decimalPlaces = formatting?.decimalPlaces;

    this.uiModel.setProperty("/measureScaleType", this.getScaleType());
    this.uiModel.setProperty("/measureDecimalPlaces", this.getDecimalPlaces());
    this.uiModel.setProperty("/measureDetails/formatting", { scaleType, decimalPlaces });
  }

  public async onSelectionFinishExceptionAggregationAttributes(
    event: IEvent<sap.m.MultiComboBox, { selectedItems: sap.ui.core.Item[] }>
  ) {
    const selectedAttributes = event.getParameter("selectedItems")?.map((item) => item.getKey()) ?? [];
    const selectedMeasureTechnicalName = this.getSelectedMeasureName();
    const measureExists = this.model.getMeasure(selectedMeasureTechnicalName) as IAnalyticModelCountDistinctMeasure;
    if (!measureExists) {
      return;
    }
    const updateExceptionAggregationAttributesCommand = new UpdateExceptionAggregationAttributes(
      selectedMeasureTechnicalName,
      selectedAttributes
    );
    this.stack.execute(updateExceptionAggregationAttributesCommand);
    await Util.Component.modelChangeHandler([ModelUIProperties.MEASURES]);
  }

  public async onConstantSelectionTypeChange() {
    const constantSelectionTypeSelect = this.byId("constantSelectionType") as sap.m.Select;
    const constantSelectionType = constantSelectionTypeSelect.getSelectedKey();
    this.measureModel.setProperty("/constantSelectionVisible", constantSelectionType === "selected");
    const selectedMeasureTechnicalName = this.getSelectedMeasureName();
    const constantSelectionTypeMap = {
      selected: AnalyticModelConstantSelectionType.Selected,
      all: AnalyticModelConstantSelectionType.All,
    };
    const constantSelectionConverted =
      constantSelectionTypeMap[constantSelectionType] ?? AnalyticModelConstantSelectionType.None;
    const constantSelectionCombobox = this.byId("constantSelectionDimensions") as sap.m.MultiComboBox;
    constantSelectionCombobox.setSelectedKeys([]);

    const updateConstantSelectionTypeCommand = new UpdateConstantSelectionType(
      selectedMeasureTechnicalName,
      constantSelectionConverted
    );
    this.stack.execute(updateConstantSelectionTypeCommand);
    await Util.Component.modelChangeHandler([ModelUIProperties.MEASURES]);
  }

  public async onFinishConstantSelectionDimensions(
    event: IEvent<sap.m.MultiComboBox, { selectedItems: sap.ui.core.Item[] }>
  ) {
    const selectedKeys = event.getParameter("selectedItems")?.map((item) => item.getKey()) ?? [];
    const selectedMeasureTechnicalName = this.getSelectedMeasureName();
    const measureExists = this.model.getMeasure(selectedMeasureTechnicalName) as IAnalyticModelRestrictedMeasure;
    if (!measureExists) {
      return;
    }

    const updateConstantSelectionAttributesCommand = new UpdateConstantSelectionAttributes(
      selectedMeasureTechnicalName,
      selectedKeys
    );
    this.stack.execute(updateConstantSelectionAttributesCommand);
    await Util.Component.modelChangeHandler([ModelUIProperties.MEASURES]);
  }

  public async onExpressionChange(event: IEvent<ExpressionEditor, object>) {
    this.resetExpressionEditorMessageStrip(event.getSource());
    debounce(() => {
      void this.validateExpression(true);
    }, 2000)();
  }

  public async onValidateExpression(event: IEvent<ExpressionEditor, object>) {
    this.resetExpressionEditorMessageStrip(event.getSource());
    debounce(() => {
      void this.validateExpression();
    }, 2000)();
  }

  // *** Helper functions to prepare the value lists ***
  private getAggregationTypes(inheritedAggregationType: string) {
    const aggregationDisplayTypes = [];
    for (const [key] of ReverseAggregationTypeMapping.entries()) {
      aggregationDisplayTypes.push({
        key: key,
        text: ReverseAggregationTypeMapping.get(key),
      });
    }
    const inheritedAggregationText = this.getText("inherited", [inheritedAggregationType]);
    aggregationDisplayTypes.push({
      key: "Inherited",
      text: inheritedAggregationText,
    });
    this.measureModel.setProperty("/aggregationTypes", aggregationDisplayTypes);
  }

  private setExceptionAggregationTypes() {
    const exceptionAggregationTypes = this.getExceptionAggregationTypes();
    this.measureModel.setProperty("/exceptionAggregationTypes", exceptionAggregationTypes);

    if (
      !this.featureflags.DWCO_MODELING_AM_UNBOOKED_EXCEPTION_AGGREGATION ||
      !(this.isFactSourceMeasure() || this.isRestrictedMeasure())
    ) {
      this.measureModel.setProperty(
        "/exceptionAggregationTypes",
        exceptionAggregationTypes.filter((item) => !item.key.includes("_OF_DIMENSION"))
      );
    }
  }

  private getExceptionAggregationNcumTypes() {
    if (!this.measureModel.getProperty("/exceptionAggregationNcumTypes")) {
      const exceptionAggregationNcumTypes = getParsedExceptionAggregationNcumTypes();
      this.measureModel.setProperty("/exceptionAggregationNcumTypes", exceptionAggregationNcumTypes);
    }
  }

  // *** Helper functions for the expression editor and validation ***
  public onBeforeMeasurePopover(event: sap.ui.base.Event) {
    const measureTechnicalName = event.getParameter("columnName");
    const measureDetails = this.model.getMeasure(measureTechnicalName);
    if (!!measureDetails) {
      const measureInfoItems = [
        {
          label: this.getText("businessName"),
          value: measureDetails.text,
        },
        {
          label: this.getText("technicalName"),
          value: measureTechnicalName,
        },
        {
          label: this.getText("measureType"),
          value: Formatter.measureTypeTextFormatter(measureDetails.measureType),
        },
      ];
      if (measureDetails.measureType === AnalyticModelMeasureType.RestrictedMeasure) {
        const sourceInfoItem = {
          label: this.getText("sourceMeasure"),
          value: measureDetails.key,
        };
        measureInfoItems.push(sourceInfoItem);
      }
      const measureInfo = {
        sectionNum: 1,
        sections: [
          {
            title: this.getText("measureDetails"),
            items: measureInfoItems,
          },
        ],
      };
      this.measureModel.setProperty("/measureInfo", measureInfo);
    }
    this.measureModel.refresh(true);
  }

  public onBeforeAttributePopoverMeasure(event: sap.ui.base.Event) {
    this.onBeforeAttributePopover(event, this.measureModel);
  }

  public onBeforeVariablePopover(event: sap.ui.base.Event) {
    const variableTechnicalName = event.getParameter("columnName");
    const variableDetails = this.model.getVariable(variableTechnicalName);
    if (!!variableDetails) {
      const variableInfoItems = [
        {
          label: this.getText("businessName"),
          value: (variableDetails as any).text,
        },
        {
          label: this.getText("technicalName"),
          value: variableTechnicalName,
        },
      ];
      const variableInfo = {
        sectionNum: 1,
        sections: [
          {
            title: this.getText("variableDetails"),
            items: variableInfoItems,
          },
        ],
      };
      this.measureModel.setProperty("/variableInfo", variableInfo);
    }
    this.measureModel.refresh(true);
  }

  private isFactSourceMeasure(): boolean {
    return this.uiModel.getProperty("/measureDetails/measureType") === AnalyticModelMeasureType.FactSourceMeasure;
  }
  private isRestrictedMeasure(): boolean {
    return this.uiModel.getProperty("/measureDetails/measureType") === AnalyticModelMeasureType.RestrictedMeasure;
  }
  private isCalculatedMeasure(): boolean {
    return this.uiModel.getProperty("/measureDetails/measureType") === AnalyticModelMeasureType.CalculatedMeasure;
  }
  private isCountDistinctMeasure(): boolean {
    return this.uiModel.getProperty("/measureDetails/measureType") === AnalyticModelMeasureType.CountDistinct;
  }
  private isCurrencyConversionMeasure(): boolean {
    return (
      this.uiModel.getProperty("/measureDetails/measureType") === AnalyticModelMeasureType.CurrencyConversionMeasure
    );
  }
  private isUnitConversionMeasure(): boolean {
    return (
      this.featureflags.DWCO_MODELING_AM_UNIT_CONVERSION &&
      this.uiModel.getProperty("/measureDetails/measureType") === AnalyticModelMeasureType.UnitConversionMeasure
    );
  }
  private isNonCumulativeMeasure(): boolean {
    return this.uiModel.getProperty("/measureDetails/measureType") === AnalyticModelMeasureType.NonCumulativeMeasure;
  }

  private getFactSourceMeasureAggregationText(factSourceMeasure: IAnalyticModelSourceMeasure) {
    let aggregationText = factSourceMeasure ? Formatter.measureAggregationText(factSourceMeasure.aggregation) : "";
    const allSourceDataEntityDetails = this.uiModel.getProperty("/allSourceDataEntityDetails");
    const sourceMeasureFromDataEntityDetails = getMeasureFromDataEntityDetails(
      this.model,
      allSourceDataEntityDetails,
      factSourceMeasure.key
    );
    if (!aggregationText) {
      aggregationText = sourceMeasureFromDataEntityDetails ? sourceMeasureFromDataEntityDetails.aggregation : "";
    }
    return aggregationText;
  }

  public clearExpressionEditor() {
    let expressionEditor: ExpressionEditor;
    if (this.isRestrictedMeasure()) {
      expressionEditor = this.byId("restrictedMeasureExpressionEditor");
    } else if (this.isCalculatedMeasure()) {
      expressionEditor = this.byId("calculatedMeasureExpressionEditor");
    }

    const routingFromSameObject = this.uiModel.getProperty("/routingFromSameObjectUndoRedo");
    if (expressionEditor && !routingFromSameObject) {
      // clean up expression editor on change information because we are reusing the same control
      expressionEditor.firedOnChangeInfo = undefined;
      expressionEditor.cleanAutoCompletions();
      expressionEditor.clearSearchInputs();
      expressionEditor.resetSelectedTool();
    }
  }

  public async validateExpression(change = false) {
    let message: ValidationMessage;
    // 1. Parse entered formula and check for errors
    const expressionInput: ExpressionEditor = this.isRestrictedMeasure()
      ? this.byId("restrictedMeasureExpressionEditor")
      : this.byId("calculatedMeasureExpressionEditor");
    const technicalName = this.uiModel.getProperty("/measureDetails/technicalName");
    let convertFormulaResult: IConvertFormulaResult;
    if (this.isCalculatedMeasure()) {
      convertFormulaResult = await MeasureValidationHelper.convertStringToCalculatedMeasure(
        expressionInput.codeEditor.getValue(),
        convertFormulaToCsnXpr
      );
    } else {
      convertFormulaResult = await MeasureValidationHelper.convertStringToRestrictedMeasure(
        expressionInput.codeEditor.getValue(),
        convertFormulaToCsnXpr
      );
    }
    expressionInput.setMessageStripVisible(true);
    const formulaMessages = QueryModelValidator.validateParsedFormula(
      technicalName,
      this.model.getData(),
      convertFormulaResult
    );
    // 2. Assign parsed formula and elements if no errors (must be assigned for complete validation) and validate
    const shouldResetFormula = formulaMessages.length !== 0;
    const newExpression = {
      formula: convertFormulaResult?.measure?.formula,
      elements: convertFormulaResult?.measure?.elements,
      formulaRaw: expressionInput.codeEditor.getValue(),
      shouldResetFormula,
    };
    if (!change) {
      // validate a temporary model
      const model = new sap.ui.model.json.JSONModel(this.model.getData());
      const modelFilter = model.getProperty("/measures")[technicalName] as
        | IAnalyticModelCalculatedMeasure
        | IAnalyticModelRestrictedMeasure;
      updateExpressionFormula(modelFilter, newExpression);
      if (this.featureflags.DWCO_MODELING_ANALYTIC_MODEL_STACKING_LIFECYCLE) {
        const validationMessages = await Util.Component.validateModel(this.model);
        message = Util.Component.getLocalValidationByPath(
          `/measures/${encodeURIComponent(technicalName)}/expression`,
          validationMessages
        );
      } else {
        await Util.Component.getAndUpdateModelValidations(model);
      }
    } else {
      const updateMeasureExpressionCommand = new UpdateMeasureExpression(technicalName, newExpression);
      this.stack.execute(updateMeasureExpressionCommand);
      await Util.Component.modelChangeHandler();
    }
    // 3. Error handling based on validation map updated in complete model validation
    // this message attribution should be moved to the last else condition (where change is true) after removing the DWCO_MODELING_ANALYTIC_MODEL_STACKING_LIFECYCLE FF
    if (change || !this.featureflags.DWCO_MODELING_ANALYTIC_MODEL_STACKING_LIFECYCLE) {
      message = Util.Component.getPropertyValidationMessage(
        `/measures/${encodeURIComponent(technicalName)}/expression`
      );
    }
    const validationMessages = [];
    if (!!message) {
      const errorMessage = !!message.parameters
        ? this.replaceParameters(this.getText(message.descriptionKey), message.parameters)
        : this.getText(message.descriptionKey);
      const messageType = message.type;
      validationMessages.push({ message: errorMessage, type: messageType });
    }
    // 4. Set validationMessages for the message strip text
    this.uiModel.setProperty("/measureDetails/formulaValidation", validationMessages);
    if (
      validationMessages.length === 0 ||
      (validationMessages.length === 1 && message.descriptionKey === "calculatedMeasureEmptyExpression")
    ) {
      setTimeout(() => {
        expressionInput.setMessageStripVisible(false);
      }, 2000);
    }
  }

  private setAggregationTypeVisibility(sourceMeasure: string) {
    // aggregation type can only be selected for restricted measures directly on fact source measures
    const measure = this.model.getMeasure(sourceMeasure);
    const sourceMeasureType = measure?.measureType;
    const aggregationTypeVisible = sourceMeasureType === AnalyticModelMeasureType.FactSourceMeasure;
    if (!(this.isCurrencyConversionMeasure() || this.isUnitConversionMeasure())) {
      this.measureModel.setProperty("/aggregationTypeVisible", aggregationTypeVisible);
    }
  }

  private setExceptionAggregationVisibility(sourceMeasure: string) {
    // exception aggregation can only be selected for restricted measures directly on fact source measures
    const measure = this.model.getMeasure(sourceMeasure);
    const sourceMeasureType = measure?.measureType;
    const exceptionAggregationVisible = sourceMeasureType === AnalyticModelMeasureType.FactSourceMeasure;
    if (this.isRestrictedMeasure()) {
      this.measureModel.setProperty("/exceptionAggregationVisible", exceptionAggregationVisible);
    } else if (this.isCurrencyConversionMeasure() || this.isUnitConversionMeasure()) {
      this.measureModel.setProperty("/exceptionAggregationVisible", false);
    } else {
      this.measureModel.setProperty("/exceptionAggregationVisible", true);
    }
  }

  private shouldExceptionAggregationApply(): boolean {
    return shouldExceptionAggregationApply(this.uiModel.getProperty("/measureDetails"));
  }

  /**
   * Currency Conversion functions
   */
  public async onClientIdChange() {
    const clientIdSelect = this.byId("analyticModelClientId") as sap.m.Select;
    const conversionTypeValue = this.isCurrencyConversionMeasure() ? CONVERSION_TYPES.CURRENCY : CONVERSION_TYPES.UNIT;
    const clientID = clientIdSelect.getSelectedKey();
    const technicalName = this.getSelectedMeasureName();
    const measureExists = this.model.measureExists(technicalName);
    if (!!measureExists && !!clientID) {
      this.measureModel.setProperty(`/${conversionTypeValue}ConversionData/selectedClientID`, clientID);
      this.resetCurrencyAndUnitConversionPropertyDialog(conversionTypeValue);
      const updateClientIdCommand = new UpdateClientId(technicalName, clientID, conversionTypeValue);
      this.stack.execute(updateClientIdCommand);
      await Util.Component.modelChangeHandler([ModelUIProperties.MEASURES]);
    }
  }

  private resetCurrencyAndUnitConversionPropertyDialog(conversionTypeValue: CONVERSION_TYPES) {
    const resetProperties = (path: string, properties: string[]) => {
      properties.forEach((property) => this.measureModel.setProperty(`${path}/${property}`, null));
    };
    if (conversionTypeValue === CONVERSION_TYPES.CURRENCY) {
      resetProperties("/currencyConversionData/targetCurrencyDialog", ["technicalName", "businessName"]);
      resetProperties("/currencyConversionData/conversionTypeDialog", ["technicalName", "businessName"]);
      resetProperties("/currencyConversionData/referenceDateDialog", ["technicalName", "businessName"]);
      this.measureModel.setProperty("/currencyConversionData/conversionTypeDialog/objects", []);
      this.measureModel.setProperty("/currencyConversionData/targetCurrencyDialog/objects", []);
      this.measureModel.setProperty("/currencyConversionData/currencyConversionTablesEmpty", false);
    } else if (this.featureflags.DWCO_MODELING_AM_UNIT_CONVERSION && conversionTypeValue === CONVERSION_TYPES.UNIT) {
      resetProperties("/unitConversionData/targetUnitDialog", ["technicalName", "businessName"]);
      this.measureModel.setProperty("/unitConversionData/targetUnitDialog/objects", []);
    }
  }

  public async onErrorHandlingSelection(event: IEvent<sap.m.Select, any>) {
    const errorType = event.getSource().getSelectedKey() as AnalyticModelErrorHandling;
    const technicalName = this.getSelectedMeasureName();
    const measureExists = this.model.measureExists(technicalName);
    if (!!measureExists && !!errorType) {
      const updateErrorHandlingCommand = new UpdateErrorHandling(technicalName, errorType);
      this.stack.execute(updateErrorHandlingCommand);
      await Util.Component.modelChangeHandler([ModelUIProperties.MEASURES]);
    }
  }

  /**
   * Open Exchange Rate Type Value Help Dialog
   */
  public async onExchangeRateTypeDialogRequest() {
    if (!this.conversionTypeDialog) {
      const fragmentName = require("../view/CurrencyConversionConversionRateTypeDialog.fragment.xml");
      this.conversionTypeDialog = sap.ui.xmlfragment("conversionType", fragmentName, this) as sap.m.Dialog;
    }
    this.initConversionSourceVariable(CONVERSION_TYPE_PROPERTIES.CONVERSION_TYPE);
    this.measureModelData.currencyConversionData.conversionTypeDialog.conversionType = this.getConversionTypeType();
    this.getView().addDependent(this.conversionTypeDialog);
    this.conversionTypeDialog.open();
    // sets the currency conversion related values from the model
    this.getColumnsCount(CONVERSION_TYPE_PROPERTIES.CONVERSION_TYPE);
    this.setConversionDialogModel(CONVERSION_TYPE_PROPERTIES.CONVERSION_TYPE);
    if (this.getConversionTypeType() === CONVERSION_TYPE_KEYS.CONSTANT_VALUE) {
      const constantValueTable = sap.ui.getCore().byId("conversionType--conversionTable") as sap.ui.table.Table;
      await this.setupConversionValues(CONVERSION_TYPE_PROPERTIES.CONVERSION_TYPE, constantValueTable);
    }
  }

  private conversionDialogValuesUndefinedOrEmpty(property: any): boolean {
    if (!property || (Array.isArray(property) && !property.length)) {
      return true;
    }
    return false;
  }

  public onCancelCurrencyDialog(oEvent: IEvent<DialogButton, any>) {
    const dialog = oEvent?.getSource()?.getParent();
    const id = oEvent?.getSource()?.getId()?.replace("cancel", "searchField");
    // reset search
    const searchField = sap.ui.getCore().byId(id) as sap.m.SearchField;
    if (searchField?.getValue() && searchField?.getValue() !== "") {
      searchField.setValue("");
      this.clearTableFilterInConversionDialog(oEvent?.getSource()?.getId()?.split("--")[0], null);
    }
    if (dialog) {
      dialog.close();
    }
  }

  /**
   * select value from currency dialog (Target, Source, conversion Type)
   * this function is working based on DialogId and Table Id either conversionType targetCurrency or targetUnit
   * @param oEvent
   */
  public async onSelectCurrencyDialog(oEvent: IEvent<DialogButton, { id: string }>) {
    const technicalName = this.getSelectedMeasureName();
    const measureExists = this.model.measureExists(technicalName);
    let tableContexts: sap.ui.model.Context;
    const dialog = oEvent.getSource().getParent();
    // eslint-disable-next-line @typescript-eslint/restrict-plus-operands
    const dialogName = dialog.getId().split("--")[0] + "Dialog";
    const isUnitConversion = dialog.getId().split("--")[0] === CONVERSION_TYPE_PROPERTIES.TARGET_UNIT;
    const conversionDataPath = `${isUnitConversion ? CONVERSION_TYPES.UNIT : CONVERSION_TYPES.CURRENCY}ConversionData`;
    const type: CONVERSION_TYPE_KEYS =
      dialog.getId().split("--")[0] === CONVERSION_TYPE_PROPERTIES.CONVERSION_TYPE
        ? this.currencyConversionData[dialogName].conversionType
        : isUnitConversion
        ? this.unitConversionData[dialogName].unitType
        : this.currencyConversionData[dialogName].currencyType;

    if (oEvent.getParameter("id")) {
      const propertyName = oEvent.getParameter("id").split("--")[0] as CONVERSION_TYPE_PROPERTIES;
      const { tableId, rowValueName } = this.getConversionTableId(propertyName, type);
      // eslint-disable-next-line @typescript-eslint/restrict-plus-operands
      const table: sap.ui.table.Table = sap.ui.getCore().byId(propertyName + "--" + tableId);
      const selectedIndex = table.getSelectedIndex();
      if (selectedIndex !== -1) {
        tableContexts = table.getContextByIndex(selectedIndex);
      }
      let currencyUpdate: ConversionUpdate;
      if (!!measureExists) {
        if (type === CONVERSION_TYPE_KEYS.VARIABLES && this.measureModel.getProperty("/isNewSourceInputEnable")) {
          const variableBusinessName: string = this.measureModelData[conversionDataPath].sourceVariable.text;
          const variableTechnicalName: string = this.measureModelData[conversionDataPath].sourceVariable.key;

          currencyUpdate = {
            type: ConversionUpdateType.ConversionVariableCreation,
            variableBusinessName,
            variableTechnicalName,
          } as ConversionVariableCreation;
        } else {
          const selectedObjectValue: string = tableContexts?.getObject()[rowValueName] ?? "";

          if (selectedObjectValue) {
            currencyUpdate = {
              type: ConversionUpdateType.ConversionValueUpdate,
              value: selectedObjectValue,
            } as ConversionValueUpdate;
          }
        }
      }
      if (currencyUpdate) {
        const command = new UpdateConversion(technicalName, type, propertyName, currencyUpdate);
        const response = this.stack.execute(command);
        if (response.success) {
          const value = response.result.value as string;
          const measure = response.result.measure as
            | IAnalyticModelCurrencyConversionMeasure
            | IAnalyticModelUnitConversionMeasure;
          this.measureModel.setProperty(`/${conversionDataPath}/${dialogName}/technicalName`, value);
          this.setConversionNames(measure);
          await Util.Component.modelChangeHandler();
        }
      }

      // reset search
      const searchField = sap.ui.getCore().byId(`${propertyName}--searchField`) as sap.m.SearchField;
      if (searchField && searchField?.getValue() !== "") {
        searchField.setValue("");
        this.clearTableFilterInConversionDialog(propertyName, type);
      }
    }
    const inputId = isUnitConversion ? "analyticModelTargetUnit" : "analyticModelTargetCurrency";
    (this.view.byId(inputId) as sap.m.Input)?.closeValueStateMessage();
    if (!isUnitConversion) {
      (this.view.byId("conversionType") as sap.m.Input)?.closeValueStateMessage();
    }
    dialog?.close();
  }

  /**
   * Open TargetCurrency/TargetUnit Value Help Dialog
   */
  private setConversionDialogModel(currencyTypeProperty: any) {
    let selectedCurrencyType: any;
    let tableData;
    const technicalName = this.getSelectedMeasureName();
    const selectedModelMeasures = this.model.getMeasure(technicalName) as
      | IAnalyticModelCurrencyConversionMeasure
      | IAnalyticModelUnitConversionMeasure;
    const dialogName = currencyTypeProperty + "Dialog";
    const isUnitConversion = currencyTypeProperty === CONVERSION_TYPE_PROPERTIES.TARGET_UNIT;
    const conversionDataPath = `${isUnitConversion ? CONVERSION_TYPES.UNIT : CONVERSION_TYPES.CURRENCY}ConversionData`;
    if (!!selectedModelMeasures) {
      selectedCurrencyType =
        currencyTypeProperty === CONVERSION_TYPE_PROPERTIES.TARGET_CURRENCY ||
        currencyTypeProperty === CONVERSION_TYPE_PROPERTIES.TARGET_UNIT
          ? this.getTargetConversionType()
          : this.getConversionTypeType();
      if (currencyTypeProperty === CONVERSION_TYPE_PROPERTIES.TARGET_CURRENCY) {
        this.measureModelData.currencyConversionData.targetCurrencyDialog.currencyType = selectedCurrencyType;
      } else if (currencyTypeProperty === CONVERSION_TYPE_PROPERTIES.TARGET_UNIT) {
        this.measureModelData.unitConversionData.targetUnitDialog.unitType = selectedCurrencyType;
      } else {
        this.measureModelData.currencyConversionData.conversionTypeDialog.conversionType = selectedCurrencyType;
      }
      if (selectedCurrencyType === CONVERSION_TYPE_KEYS.ATTRIBUTE) {
        tableData =
          currencyTypeProperty === CONVERSION_TYPE_PROPERTIES.TARGET_CURRENCY
            ? this.measureModelData.targetDimensions
              ? currencyTypeProperty === CONVERSION_TYPE_PROPERTIES.TARGET_UNIT
              : this.measureModelData.targetUnitDimensions
            : this.measureModelData.attributeColumns;
      } else if (selectedCurrencyType === CONVERSION_TYPE_KEYS.CONSTANT_VALUE) {
        tableData = this.measureModelData[conversionDataPath][dialogName].objects;
      } else if (selectedCurrencyType === CONVERSION_TYPE_KEYS.VARIABLES) {
        tableData = this.measureModelData.sourceVariableColumns;
      } else {
        // targetCurrency/targetUnit has NO value
        this.measureModel.setProperty(`/${conversionDataPath}/isOkEnabled`, false);
      }
      const { tableId } = this.getConversionTableId(currencyTypeProperty, selectedCurrencyType);
      if (selectedCurrencyType) {
        const hasSelected = this.getTableSelectedRow(
          currencyTypeProperty + "--" + tableId,
          tableData,
          this.measureModelData[conversionDataPath][dialogName].technicalName,
          true
        );
        this.measureModel.setProperty(`/${conversionDataPath}/isOkEnabled`, hasSelected);
      } else {
        this.measureModel.setProperty(`/${conversionDataPath}/isOkEnabled`, false);
      }
    }
  }

  /**
   * This function counting the length of currency tables: constant values/ Dimensions/ Variables
   * @param conversionTypeProperty
   */
  private getColumnsCount(conversionTypeProperty) {
    this.setVariablesColumn();
    const countVariables: number = this.measureModelData.sourceVariableColumns
      ? this.measureModelData.sourceVariableColumns.length
      : 0;
    if (conversionTypeProperty === CONVERSION_TYPE_PROPERTIES.TARGET_CURRENCY) {
      const currencyTargetDimensions =
        this.measureModelData.currencyConversionData.targetCurrencyDialog.targetDimensions;
      const countDimensions: number =
        currencyTargetDimensions && Array.isArray(currencyTargetDimensions)
          ? currencyTargetDimensions.length
          : currencyTargetDimensions
          ? 1
          : 0;
      this.currencyConversionData.targetCurrencyDialog.currencyTypes[1].text = this.getText(
        "ccTypesDimensionsWithNum",
        [countDimensions.toString()]
      );
      this.currencyConversionData.targetCurrencyDialog.currencyTypes[2].text = this.getText("ccTypesVariablesWithNum", [
        countVariables.toString(),
      ]);
    } else if (conversionTypeProperty === CONVERSION_TYPE_PROPERTIES.REFERENCE_DATE) {
      const countDimensions: number = this.measureModelData.referenceDateDimensions
        ? this.measureModelData.referenceDateDimensions.length
        : 0;
      this.currencyConversionData.referenceDateDialog.referenceDateTypes[1].text = this.getText(
        "ccTypesDimensionsWithNum",
        [countDimensions.toString()]
      );
      this.currencyConversionData.referenceDateDialog.referenceDateTypes[3].text = this.getText(
        "ccTypesVariablesWithNum",
        [countVariables.toString()]
      );
    } else if (conversionTypeProperty === CONVERSION_TYPE_PROPERTIES.CONVERSION_TYPE) {
      const countDimensions: number = this.measureModelData.attributeColumns
        ? this.measureModelData.attributeColumns.length
        : 0;
      this.currencyConversionData.conversionTypeDialog.conversionTypeTypes[1].text = this.getText(
        "ccTypesDimensionsWithNum",
        [countDimensions.toString()]
      );
      this.currencyConversionData.conversionTypeDialog.conversionTypeTypes[2].text = this.getText(
        "ccTypesVariablesWithNum",
        [countVariables.toString()]
      );
    } else if (conversionTypeProperty === CONVERSION_TYPE_PROPERTIES.TARGET_UNIT) {
      const unitTargetDimensions = this.measureModelData.unitConversionData.targetUnitDialog.targetUnitDimensions;
      const countDimensions: number =
        unitTargetDimensions && Array.isArray(unitTargetDimensions)
          ? unitTargetDimensions.length
          : unitTargetDimensions
          ? 1
          : 0;
      this.unitConversionData.targetUnitDialog.unitTypes[1].text = this.getText("ccTypesDimensionsWithNum", [
        countDimensions.toString(),
      ]);
      this.unitConversionData.targetUnitDialog.unitTypes[2].text = this.getText("ccTypesVariablesWithNum", [
        countVariables.toString(),
      ]);
    }
  }

  public getTableSelectedRow(
    tableId: string,
    tableData: any[],
    selectedValue: string,
    isConversionTypeAttribute: boolean
  ): boolean {
    const oTable = sap.ui.getCore().byId(tableId) as sap.ui.table.Table;

    let selectedIndex = -1;
    if (tableData) {
      selectedIndex = isConversionTypeAttribute
        ? tableData.findIndex((value) => value.key === selectedValue)
        : tableData
            .map(function (data) {
              return data.value;
            })
            .indexOf(selectedValue);
    }
    if (selectedIndex > -1) {
      oTable.setSelectedIndex(selectedIndex);
      return true;
    }
    oTable.clearSelection();
    return false;
  }

  /**
   * Open TargetConversion Value Help Dialog
   */
  public async onTargetConversionDialogRequest() {
    const conversionTypeValue = this.isCurrencyConversionMeasure() ? CONVERSION_TYPES.CURRENCY : CONVERSION_TYPES.UNIT;
    const conversionTypeValueUpper =
      conversionTypeValue[0].toUpperCase() + conversionTypeValue.slice(1, conversionTypeValue.length);
    // sets the currency/unit conversion related values from the model

    const targetedProperty = this.isCurrencyConversionMeasure()
      ? CONVERSION_TYPE_PROPERTIES.TARGET_CURRENCY
      : CONVERSION_TYPE_PROPERTIES.TARGET_UNIT;

    const fragmentName = require(`../view/${conversionTypeValueUpper}ConversionTarget${conversionTypeValueUpper}Dialog.fragment.xml`);
    if (this.isCurrencyConversionMeasure() && !this.targetCurrencyDialog) {
      this.targetCurrencyDialog = sap.ui.xmlfragment(`targetCurrency`, fragmentName, this) as sap.m.Dialog;
    } else if (this.isUnitConversionMeasure() && !this.targetUnitDialog) {
      this.targetUnitDialog = sap.ui.xmlfragment(`targetUnit`, fragmentName, this) as sap.m.Dialog;
    }

    this.measureModel.setProperty(
      `/${conversionTypeValue}ConversionData/target${conversionTypeValueUpper}Dialog/${conversionTypeValue}Type`,
      this.getTargetConversionType()
    );

    this.getColumnsCount(targetedProperty);
    this.setConversionDialogModel(targetedProperty);
    if (this.isCurrencyConversionMeasure()) {
      this.getView().addDependent(this.targetCurrencyDialog);
      this.targetCurrencyDialog.open();
    } else if (this.isUnitConversionMeasure()) {
      this.getView().addDependent(this.targetUnitDialog);
      this.targetUnitDialog.open();
    }
    if (this.getTargetConversionType() === CONVERSION_TYPE_KEYS.VARIABLES) {
      this.initConversionSourceVariable(targetedProperty);
      this.updateSourceVariableRadioButtonByCurrencyType(targetedProperty);
    }
    if (this.getTargetConversionType() === CONVERSION_TYPE_KEYS.CONSTANT_VALUE) {
      const constantValueTable = sap.ui
        .getCore()
        .byId(`target${conversionTypeValueUpper}--fixed${conversionTypeValueUpper}ValueTable`) as sap.m.Table;
      await this.setupConversionValues(targetedProperty, constantValueTable);
    }
  }

  /**
   * trigger when conversion values (TCUR views or T006 views) are not loaded
   * @param conversionTypeProperty either TargetCurrency/Unit or Conversion Type
   */
  private async setupConversionValues(conversionTypeProperty, dialogContent: sap.ui.core.Control) {
    const conversionPropertyValue =
      conversionTypeProperty === CONVERSION_TYPE_PROPERTIES.TARGET_UNIT
        ? CONVERSION_TYPES.UNIT
        : CONVERSION_TYPES.CURRENCY;
    if (
      this.getIsNewMeasure() ||
      this.conversionDialogValuesUndefinedOrEmpty(
        this.measureModel.getProperty(`/currencyConversionData/${conversionTypeProperty}Dialog/objects`)
      )
    ) {
      dialogContent?.setBusy(true);
      await this.setupConversionDialog(conversionTypeProperty, conversionPropertyValue, dialogContent);
    }
  }

  /**
   * handle different list by click on Fixed or Dimension buttons in Conversion dialogs
   * @param oEvent
   */
  public async onConversionTypeChange(oEvent: IEvent<sap.m.Select | sap.m.SegmentedButtonItem, any>) {
    const selectButtonId = oEvent.getSource()?.getId();
    const conversionType = selectButtonId.split("--")[0];
    const conversionTypeValue = this.isCurrencyConversionMeasure() ? CONVERSION_TYPES.CURRENCY : CONVERSION_TYPES.UNIT;
    let selectedKey;
    if (conversionType === CONVERSION_TYPE_PROPERTIES.REFERENCE_DATE) {
      selectedKey = (oEvent.getSource() as sap.m.Select).getSelectedKey();
      if (selectedKey === CONVERSION_TYPE_KEYS.ATTRIBUTE) {
        const hasSelected = this.getTableSelectedRow(
          CONVERSION_TYPE_PROPERTIES.REFERENCE_DATE + "--referenceDateColumnsTable",
          this.measureModelData.referenceDateDimensions,
          this.measureModelData.currencyConversionData.referenceDateDialog.technicalName,
          true
        );
        this.measureModel.setProperty("/currencyConversionData/isOkEnabled", hasSelected);
      } else if (selectedKey === CONVERSION_TYPE_KEYS.CONSTANT_VALUE) {
        this.resetSelectedDateValue();
      } else if (selectedKey === CONVERSION_TYPE_KEYS.VARIABLES) {
        this.initConversionSourceVariable(conversionType);
      } else {
        this.measureModel.setProperty("/currencyConversionData/isOkEnabled", true);
      }
    } else {
      selectedKey = (oEvent.getSource() as sap.m.Select).getSelectedKey();
      const { tableId } = this.getConversionTableId(conversionType, selectedKey);
      if (selectedKey !== CONVERSION_TYPE_KEYS.VARIABLES) {
        // setup unit values from value help service in currencyType/unitType change to Fixed value
        if (selectedKey === CONVERSION_TYPE_KEYS.CONSTANT_VALUE) {
          let targetDialog;
          switch (conversionType) {
            case CONVERSION_TYPE_PROPERTIES.TARGET_CURRENCY: {
              targetDialog = this.targetCurrencyDialog;
              break;
            }
            case CONVERSION_TYPE_PROPERTIES.CONVERSION_TYPE: {
              targetDialog = this.conversionTypeDialog;
              break;
            }
            case CONVERSION_TYPE_PROPERTIES.TARGET_UNIT: {
              targetDialog = this.targetUnitDialog;
              break;
            }
          }
          await this.setupConversionValues(conversionType, targetDialog);
        }
        this.resetEnabledSelectButton(conversionType + "--" + tableId);
      } else {
        this.initConversionSourceVariable(conversionType);
      }
    }
    let conversionTypeInModelName;

    switch (conversionType) {
      case CONVERSION_TYPE_PROPERTIES.TARGET_CURRENCY:
        conversionTypeInModelName = "currencyType";
        break;
      case CONVERSION_TYPE_PROPERTIES.REFERENCE_DATE:
        conversionTypeInModelName = "referenceDateType";
        break;
      case CONVERSION_TYPE_PROPERTIES.CONVERSION_TYPE:
        conversionTypeInModelName = "conversionType";
        break;
      case CONVERSION_TYPE_PROPERTIES.TARGET_UNIT:
        conversionTypeInModelName = "unitType";
        break;
    }

    const conversionDialog = conversionType + "Dialog";
    // clear search text and remove table filter first
    const previousSelected = this.measureModel.getProperty(
      `/${conversionTypeValue}ConversionData/` + conversionDialog + "/" + conversionTypeInModelName
    );
    if (
      [CONVERSION_TYPE_KEYS.CONSTANT_VALUE, CONVERSION_TYPE_KEYS.ATTRIBUTE, CONVERSION_TYPE_KEYS.VARIABLES].includes(
        previousSelected
      )
    ) {
      const searchField = sap.ui
        .getCore()
        .byId(
          conversionType +
            `--search${
              conversionType !== "conversionType"
                ? conversionTypeValue[0].toUpperCase() + conversionTypeValue.slice(1, conversionTypeValue.length)
                : ""
            }Field`
        ) as sap.m.SearchField;
      if (searchField && searchField?.getValue() !== "") {
        searchField.setValue("");
        this.clearTableFilterInConversionDialog(conversionTypeValue, selectedKey);
      }
    }
    // set new value to model
    this.measureModel.setProperty(
      `/${conversionTypeValue}ConversionData/` + conversionDialog + "/" + conversionTypeInModelName,
      selectedKey
    );
  }

  private resetEnabledSelectButton(tableId: string) {
    const oTable = sap.ui.getCore().byId(tableId) as sap.ui.table.Table;

    const conversionTypeValue = this.isCurrencyConversionMeasure() ? CONVERSION_TYPES.CURRENCY : CONVERSION_TYPES.UNIT;

    const isOkEnabled = oTable.getSelectedIndex() > -1 ? true : false;
    this.measureModel.setProperty(`/${conversionTypeValue}ConversionData/isOkEnabled`, isOkEnabled);
  }

  private getReferenceDateType() {
    const technicalName = this.getSelectedMeasureName();
    const selectedMeasure = this.model.getMeasure(technicalName) as IAnalyticModelCurrencyConversionMeasure;
    switch (selectedMeasure.referenceDateType) {
      case AnalyticModelReferenceDateType.attribute:
        const hasSelected = this.getTableSelectedRow(
          CONVERSION_TYPE_PROPERTIES.REFERENCE_DATE + "--referenceDateColumnsTable",
          this.measureModelData.referenceDateDimensions,
          this.measureModelData.currencyConversionData.referenceDateDialog.technicalName,
          true
        );
        this.measureModel.setProperty("/currencyConversionData/isOkEnabled", hasSelected);
        return CONVERSION_TYPE_KEYS.ATTRIBUTE;
      case AnalyticModelReferenceDateType.sqlFunction:
        return CONVERSION_TYPE_KEYS.CURRENT_DATE;
      case AnalyticModelReferenceDateType.constantValue:
        return CONVERSION_TYPE_KEYS.CONSTANT_VALUE;
      case AnalyticModelReferenceDateType.variable:
        const hasVariableSelected = this.getTableSelectedRow(
          CONVERSION_TYPE_PROPERTIES.REFERENCE_DATE + "--referenceDateVariableTable",
          this.measureModelData.sourceVariableColumns,
          this.measureModelData.currencyConversionData.referenceDateDialog.technicalName,
          true
        );
        this.measureModel.setProperty("/currencyConversionData/isOkEnabled", hasVariableSelected);
        this.updateSourceVariableRadioButtonByCurrencyType(CONVERSION_TYPE_PROPERTIES.REFERENCE_DATE);
        return CONVERSION_TYPE_KEYS.VARIABLES;
      default:
        return CONVERSION_TYPE_KEYS.CONSTANT_VALUE;
    }
  }

  private getTargetConversionType() {
    const technicalName = this.getSelectedMeasureName();
    const selectedMeasure = this.model.getMeasure(technicalName) as
      | IAnalyticModelCurrencyConversionMeasure
      | IAnalyticModelUnitConversionMeasure;

    if (!selectedMeasure) {
      return CONVERSION_TYPE_KEYS.CONSTANT_VALUE;
    }
    const isCurrencyConversion = selectedMeasure.measureType === AnalyticModelMeasureType.CurrencyConversionMeasure;
    const targetType = isCurrencyConversion ? selectedMeasure.targetCurrencyType : selectedMeasure.targetUnitType;
    switch (targetType) {
      case AnalyticModelTargetCurrencyType.attribute:
      case AnalyticModelTargetUnitType.attribute:
        return CONVERSION_TYPE_KEYS.ATTRIBUTE;
      case AnalyticModelTargetCurrencyType.constantValue:
      case AnalyticModelTargetUnitType.constantValue:
        return CONVERSION_TYPE_KEYS.CONSTANT_VALUE;
      case AnalyticModelTargetCurrencyType.variable:
      case AnalyticModelTargetUnitType.variable:
        this.updateSourceVariableRadioButtonByCurrencyType(
          isCurrencyConversion ? CONVERSION_TYPE_PROPERTIES.TARGET_CURRENCY : CONVERSION_TYPE_PROPERTIES.TARGET_UNIT
        );
        return CONVERSION_TYPE_KEYS.VARIABLES;
      default:
        return CONVERSION_TYPE_KEYS.CONSTANT_VALUE;
    }
  }

  private updateSourceVariableRadioButtonByCurrencyType(conversionType: CONVERSION_TYPE_PROPERTIES) {
    const existingSourceRadioButton = sap.ui
      .getCore()
      .byId(`${conversionType}--existingSourceVariableRadioButton`) as sap.m.RadioButton;
    const newSourceRadioButton = sap.ui
      .getCore()
      .byId(`${conversionType}--newSourceVariableRadioButton`) as sap.m.RadioButton;
    this.measureModel.setProperty("/isNewSourceInputEnable", false);
    newSourceRadioButton.setSelected(false);
    existingSourceRadioButton.setSelected(true);
  }

  private getConversionTypeType() {
    const technicalName = this.getSelectedMeasureName();
    const selectedMeasure = this.model.getMeasure(technicalName) as IAnalyticModelCurrencyConversionMeasure;
    if (!selectedMeasure || !selectedMeasure.conversionTypeType) {
      return CONVERSION_TYPE_KEYS.CONSTANT_VALUE;
    }
    switch (selectedMeasure.conversionTypeType) {
      case AnalyticModelConversionTypeType.attribute:
        return CONVERSION_TYPE_KEYS.ATTRIBUTE;
      case AnalyticModelConversionTypeType.constantValue:
        return CONVERSION_TYPE_KEYS.CONSTANT_VALUE;
      case AnalyticModelConversionTypeType.variable:
        this.updateSourceVariableRadioButtonByCurrencyType(CONVERSION_TYPE_PROPERTIES.CONVERSION_TYPE);
        return CONVERSION_TYPE_KEYS.VARIABLES;
      default:
        return CONVERSION_TYPE_KEYS.CONSTANT_VALUE;
    }
  }

  /**
   * Open ReferenceDate Value Help Dialog
   */
  public onReferenceDateDialogRequest(): void {
    if (!this.referenceDateDialog) {
      const fragmentName = require("../view/CurrencyConversionReferenceDateDialog.fragment.xml");
      this.referenceDateDialog = sap.ui.xmlfragment("referenceDate", fragmentName, this) as sap.m.Dialog;
    }
    this.initConversionSourceVariable(CONVERSION_TYPE_PROPERTIES.REFERENCE_DATE);
    // sets the currency conversion related values from the model
    this.getColumnsCount(CONVERSION_TYPE_PROPERTIES.REFERENCE_DATE);
    this.measureModel.setProperty(
      "/currencyConversionData/referenceDateDialog/referenceDateTypes",
      this.currencyConversionData.referenceDateDialog.referenceDateTypes
    );
    // reset selectedDate in new CC measure
    const uiSelectedDate = this.resetSelectedDateValue();
    const technicalName = this.getSelectedMeasureName();
    const measureSelectedDate = (this.model.getMeasure(technicalName) as IAnalyticModelCurrencyConversionMeasure)
      .referenceDate;
    let selectedDate;
    if (!measureSelectedDate) {
      selectedDate = this.getText("CC_txtNoDateSelected");
    } else {
      selectedDate = uiSelectedDate;
    }
    this.measureModel.setProperty(
      "/currencyConversionData/referenceDateDialog/selectedDate",
      !selectedDate ? this.getText("CC_txtNoDateSelected") : selectedDate
    );
    const customizedDate = Format.toLocalDate(selectedDate);
    const referenceDateText = customizedDate ? customizedDate : selectedDate;
    this.measureModel.setProperty(
      "/currencyConversionData/referenceDateDialog/customizedDate",
      !referenceDateText ? this.getText("CC_txtNoDateSelected") : referenceDateText
    );
    this.measureModel.setProperty(
      "/currencyConversionData/referenceDateDialog/referenceDateType",
      this.getReferenceDateType()
    );
    this.getView().addDependent(this.referenceDateDialog);
    this.referenceDateDialog.open();
  }

  /**
   * the function is triggered to reset the UI Select date value
   * @returns selected date
   */
  private resetSelectedDateValue() {
    let uiSelectedDate = this.measureModel.getProperty("/currencyConversionData/referenceDateDialog/selectedDate");
    if (this.getIsNewMeasure()) {
      uiSelectedDate = this.getText("CC_txtNoDateSelected");
    }
    if (!uiSelectedDate || uiSelectedDate === this.getText("CC_txtNoDateSelected")) {
      this.measureModel.setProperty("/currencyConversionData/isOkEnabled", false);
      this.measureModel.setProperty(
        "/currencyConversionData/referenceDateDialog/selectedDate",
        this.getText("CC_txtNoDateSelected")
      );
      this.measureModel.setProperty(
        "/currencyConversionData/referenceDateDialog/customizedDate",
        this.getText("CC_txtNoDateSelected")
      );
    }
    return uiSelectedDate;
  }

  public onSelectToday(date: any) {
    const oCalendar: sap.ui.unified.Calendar = sap.ui.getCore().byId("referenceDate--calendar");
    oCalendar.removeAllSelectedDates();
    const dr = new sap.ui.unified.DateRange() as UpdatedUnifiedDateRange;
    if (typeof date === "string") {
      dr.setStartDate(new Date(date));
    } else {
      dr.setStartDate(new Date());
    }
    oCalendar.addSelectedDate(dr);
    this.updateSelectedDateText(oCalendar);
  }

  public updateSelectedDateText(oCalendar: sap.ui.unified.Calendar) {
    const oDate = oCalendar.getSelectedDates()[0]?.getStartDate() as Date;
    const formatter = sap.ui.core.format.DateFormat.getDateInstance({ pattern: "yyyy-MM-dd" });
    const utcDate = formatter.format(oDate);
    const customizedDate = Format.toLocalDate(utcDate);
    const referenceDateText = customizedDate ? customizedDate : utcDate;
    this.measureModel.setProperty("/currencyConversionData/isOkEnabled", true);
    this.measureModel.setProperty("/currencyConversionData/referenceDateDialog/selectedDate", utcDate);
    this.measureModel.setProperty("/currencyConversionData/referenceDateDialog/customizedDate", referenceDateText);
  }

  public onCalendarSelect(oEvent: IEvent<sap.ui.unified.Calendar, any>) {
    const oCalendar = oEvent.getSource();
    this.updateSelectedDateText(oCalendar);
  }

  public async onSelectReferenceDate(oEvent: IEvent<DialogButton, any>) {
    const dataType = this.measureModel.getProperty("/currencyConversionData/referenceDateDialog/referenceDateType");
    const technicalName = this.getSelectedMeasureName();
    const measureExists = this.model.measureExists(technicalName);
    let currencyUpdate: ConversionUpdate;
    if (!!measureExists) {
      if (dataType === CONVERSION_TYPE_KEYS.CONSTANT_VALUE) {
        const value = this.measureModel.getProperty("/currencyConversionData/referenceDateDialog/selectedDate");
        currencyUpdate = { type: ConversionUpdateType.ConversionValueUpdate, value } as ConversionValueUpdate;
        (sap.ui.getCore().byId("referenceDate--calendar") as sap.ui.unified.Calendar)?.displayDate(new Date());
      } else if (dataType === CONVERSION_TYPE_KEYS.ATTRIBUTE) {
        const value = this.getSelectReferenceDateObjectFromTable(
          CONVERSION_TYPE_PROPERTIES.REFERENCE_DATE + "--referenceDateColumnsTable"
        );
        currencyUpdate = { type: ConversionUpdateType.ConversionValueUpdate, value } as ConversionValueUpdate;
      } else if (dataType === CONVERSION_TYPE_KEYS.CURRENT_DATE) {
        currencyUpdate = {
          type: ConversionUpdateType.ConversionValueUpdate,
          value: "CURRENT_DATE",
        } as ConversionValueUpdate;
      } else if (dataType === CONVERSION_TYPE_KEYS.VARIABLES) {
        if (!this.measureModel.getProperty("/isNewSourceInputEnable")) {
          const value = this.getSelectReferenceDateObjectFromTable(
            CONVERSION_TYPE_PROPERTIES.REFERENCE_DATE + "--referenceDateVariableTable"
          );
          currencyUpdate = { type: ConversionUpdateType.ConversionValueUpdate, value } as ConversionValueUpdate;
        } else {
          const variableBusinessName = this.measureModelData.currencyConversionData.sourceVariable.text;
          const variableTechnicalName = this.measureModelData.currencyConversionData.sourceVariable.key;
          currencyUpdate = {
            type: ConversionUpdateType.ConversionVariableCreation,
            variableBusinessName,
            variableTechnicalName,
          } as ConversionVariableCreation;
        }
      }
    }
    if (currencyUpdate) {
      const command = new UpdateConversion(
        technicalName,
        dataType,
        CONVERSION_TYPE_PROPERTIES.REFERENCE_DATE,
        currencyUpdate
      );
      const response = this.stack.execute(command);
      if (response.success) {
        const value = response.result.value as string;
        const measure = response.result.measure as IAnalyticModelCurrencyConversionMeasure;
        this.measureModel.setProperty(`/currencyConversionData/referenceDateDialog/technicalName`, value);
        this.setConversionNames(measure);
        await Util.Component.modelChangeHandler();
      }
    }
    // reset search
    const searchField = sap.ui
      .getCore()
      .byId(CONVERSION_TYPE_PROPERTIES.REFERENCE_DATE + "--searchField") as sap.m.SearchField;
    if (searchField && searchField?.getValue() !== "") {
      searchField.setValue("");
      this.clearTableFilterInConversionDialog(CONVERSION_TYPE_PROPERTIES.REFERENCE_DATE, dataType);
    }
    const dialog = oEvent.getSource().getParent();
    (this.view.byId("analyticModelReferenceDate") as sap.m.Input)?.closeValueStateMessage();
    dialog?.close();
  }

  public getSelectReferenceDateObjectFromTable(tableId: string): string | undefined {
    const table: sap.ui.table.Table = sap.ui.getCore().byId(tableId);
    const selectedIndex = table.getSelectedIndex();
    if (selectedIndex === -1) {
      return undefined;
    }
    const tableContexts = table.getContextByIndex(selectedIndex);
    return tableContexts?.getObject().key ?? tableContexts?.getObject().text;
  }

  public onRowSelectionChange(oEvent: sap.ui.base.Event) {
    const oTable = oEvent.getSource() as sap.ui.table.Table;
    const conversionTypeValue = this.isCurrencyConversionMeasure() ? CONVERSION_TYPES.CURRENCY : CONVERSION_TYPES.UNIT;
    const iSelectedIndex = oTable.getSelectedIndex();
    const isEnabled = iSelectedIndex !== -1;
    this.measureModel.setProperty(`/${conversionTypeValue}ConversionData/isOkEnabled`, isEnabled);
  }

  public onSelectDialogSearch(oEvent: IEvent<sap.m.SearchField, { newValue: any; id: string }>): void {
    const sValue = oEvent?.getParameter("newValue");

    if (oEvent.getParameter("id")) {
      const propertyName = oEvent.getParameter("id").split("--")[0];
      const conversionTypeValue = this.isCurrencyConversionMeasure()
        ? CONVERSION_TYPES.CURRENCY
        : CONVERSION_TYPES.UNIT;
      const conversionDialogString = `/${conversionTypeValue}ConversionData/` + propertyName + "Dialog/";
      const conversionType = this.measureModel.getProperty(conversionDialogString + conversionTypeValue + "Type")
        ? this.measureModel.getProperty(conversionDialogString + conversionTypeValue + "Type")
        : this.measureModel.getProperty(conversionDialogString + "conversionType");

      const { tableId } = this.getConversionTableId(propertyName, conversionType);
      const table: sap.m.Table = sap.ui.getCore().byId(propertyName + "--" + tableId) as sap.m.Table;
      const oBinding = table?.getBinding("rows") as sap.ui.model.ListBinding;
      if (table && oBinding) {
        let columnNameUI1, columnNameUI2;
        switch (propertyName) {
          case CONVERSION_TYPE_PROPERTIES.TARGET_CURRENCY:
            switch (tableId) {
              case "targetCurrencyColumnsTable":
              case "targetCurrencyVariableTable":
                columnNameUI1 = "text";
                columnNameUI2 = "key";
                break;
              case "fixedValueTable":
                columnNameUI1 = "value";
                columnNameUI2 = "value";
                break;
            }
            break;
          case CONVERSION_TYPE_PROPERTIES.REFERENCE_DATE:
            switch (tableId) {
              case "referenceDateColumnsTable":
              case "referenceDateVariableTable":
                columnNameUI1 = "text";
                columnNameUI2 = "key";
                break;
            }
            break;
          case CONVERSION_TYPE_PROPERTIES.CONVERSION_TYPE:
            switch (tableId) {
              case "conversionTypeAttributeTable":
              case "conversionTypeVariableTable":
                columnNameUI1 = "text";
                columnNameUI2 = "key";
                break;
              case "conversionTable":
                columnNameUI1 = "value";
                columnNameUI2 = "value";
                break;
            }
            break;
          case CONVERSION_TYPE_PROPERTIES.TARGET_UNIT:
            switch (tableId) {
              case "targetUnitColumnsTable":
              case "targetUnitVariableTable":
                columnNameUI1 = "text";
                columnNameUI2 = "key";
                break;
              case "fixedUnitValueTable":
                columnNameUI1 = "value";
                columnNameUI2 = "key";
                break;
            }
            break;
        }
        const filters = [
          new sap.ui.model.Filter(columnNameUI1, sap.ui.model.FilterOperator.Contains, sValue),
          new sap.ui.model.Filter(columnNameUI2, sap.ui.model.FilterOperator.Contains, sValue),
        ];
        const oFilter = new sap.ui.model.Filter({ aFilters: filters, bAnd: false, _bMultiFilter: true });
        oBinding.filter(oFilter);
      }
    }
  }
  private getConversionTableId(propertyName: any, selectedConversionType: string) {
    let tableId = "";
    let rowValueName = "";
    switch (selectedConversionType) {
      case null: {
        const dialogName = propertyName + "Dialog";

        if (propertyName === CONVERSION_TYPE_PROPERTIES.REFERENCE_DATE) {
          selectedConversionType = this.currencyConversionData[dialogName].referenceDateType;
        } else if (propertyName === CONVERSION_TYPE_PROPERTIES.TARGET_CURRENCY) {
          selectedConversionType = this.currencyConversionData[dialogName].currencyType;
        } else if (propertyName === CONVERSION_TYPE_PROPERTIES.CONVERSION_TYPE) {
          selectedConversionType = this.currencyConversionData[dialogName].conversionType;
        } else if (propertyName === CONVERSION_TYPE_PROPERTIES.TARGET_UNIT) {
          selectedConversionType = this.unitConversionData[dialogName].unitType;
        }

        break;
      }
      case CONVERSION_TYPE_KEYS.ATTRIBUTE: {
        if (propertyName === CONVERSION_TYPE_PROPERTIES.TARGET_CURRENCY) {
          tableId = "targetCurrencyColumnsTable";
          rowValueName = "key";
        } else if (propertyName === CONVERSION_TYPE_PROPERTIES.REFERENCE_DATE) {
          tableId = "referenceDateColumnsTable";
          rowValueName = "key";
        } else if (propertyName === CONVERSION_TYPE_PROPERTIES.CONVERSION_TYPE) {
          tableId = "conversionTypeAttributeTable";
          rowValueName = "key";
        } else if (propertyName === CONVERSION_TYPE_PROPERTIES.TARGET_UNIT) {
          tableId = "targetUnitColumnsTable";
          rowValueName = "key";
        }
        break;
      }
      case CONVERSION_TYPE_KEYS.CONSTANT_VALUE: {
        if (propertyName === CONVERSION_TYPE_PROPERTIES.CONVERSION_TYPE) {
          tableId = "conversionTable";
          rowValueName = "value";
        } else if (propertyName === CONVERSION_TYPE_PROPERTIES.TARGET_CURRENCY) {
          tableId = "fixedValueTable";
          rowValueName = "value";
        } else if (propertyName === CONVERSION_TYPE_PROPERTIES.TARGET_UNIT) {
          tableId = "fixedUnitValueTable";
          rowValueName = "key";
        }
        break;
      }
      case CONVERSION_TYPE_KEYS.VARIABLES: {
        if (propertyName === CONVERSION_TYPE_PROPERTIES.CONVERSION_TYPE) {
          tableId = "conversionTypeVariableTable";
          rowValueName = "key";
        } else if (propertyName === CONVERSION_TYPE_PROPERTIES.TARGET_CURRENCY) {
          tableId = "targetCurrencyVariableTable";
          rowValueName = "key";
        } else if (propertyName === CONVERSION_TYPE_PROPERTIES.REFERENCE_DATE) {
          tableId = "referenceDateVariableTable";
          rowValueName = "key";
        } else if (propertyName === CONVERSION_TYPE_PROPERTIES.TARGET_UNIT) {
          tableId = "targetUnitVariableTable";
          rowValueName = "key";
        }
        break;
      }
    }

    return { tableId, rowValueName };
  }

  public clearTableFilterInConversionDialog(propertyName: string, selectedConversionType: string) {
    const { tableId } = this.getConversionTableId(propertyName, selectedConversionType);
    if (tableId) {
      const table = sap.ui.getCore().byId(propertyName + "--" + tableId) as sap.m.Table;
      const oBinding = table?.getBinding("rows") as sap.ui.model.ListBinding;
      oBinding.filter([]);
    }
  }
  /**
   * Loads variable section action in currency/unit conversion dialog
   * Sets default business name and technical name for the new variable to be created
   * @param conversionTypeProperty
   */
  public initConversionSourceVariable(conversionTypeProperty: string) {
    const conversionTypeValue =
      conversionTypeProperty === CONVERSION_TYPE_PROPERTIES.TARGET_UNIT
        ? CONVERSION_TYPES.UNIT
        : CONVERSION_TYPES.CURRENCY;

    this.measureModel.setProperty(`/${conversionTypeValue}ConversionData/sourceVariable/businessNameStateText`, "");
    this.measureModel.setProperty(`/${conversionTypeValue}ConversionData/sourceVariable/technicalNameStateText`, "");
    this.uiModel.setProperty("/attributeList", this.getAttributeList());

    const newSourceRadioButton: sap.m.RadioButton = sap.ui
      .getCore()
      .byId(conversionTypeProperty + "--" + "newSourceVariableRadioButton") as sap.m.RadioButton;
    newSourceRadioButton.setSelected(true);
    this.measureModel.setProperty("/isNewSourceInputEnable", true);
    let defaultVariableInput;
    switch (conversionTypeProperty) {
      case CONVERSION_TYPE_PROPERTIES.TARGET_CURRENCY:
        defaultVariableInput = this.getText("defaultNameTargetCurrencyVariableInput");
        break;
      case CONVERSION_TYPE_PROPERTIES.REFERENCE_DATE:
        defaultVariableInput = this.getText("defaultNameReferenceDateVariableInput");
        break;
      case CONVERSION_TYPE_PROPERTIES.CONVERSION_TYPE:
        defaultVariableInput = this.getText("defaultNameConversionTypeVariableInput");
        break;
      case CONVERSION_TYPE_PROPERTIES.TARGET_UNIT:
        defaultVariableInput = this.getText("defaultNameTargetUnitVariableInput");
        break;
    }
    const getDefaultVariableName = getVariableDefaultNameSequenced(defaultVariableInput, this.model);
    let newTechnicalName;
    const nameValidator = NamingHelper.getNameInputValidator();
    newTechnicalName = nameValidator
      .deriveTechnicalName(getDefaultVariableName, getNameUsageForObject(AnalyticModelObjectType.VARIABLE, this.model))
      .replace(/\s/g, "_")
      .toUpperCase();

    if (newTechnicalName === "" || !isNaN(Number(newTechnicalName))) {
      newTechnicalName = getVariableDefaultNameSequenced("SOURCE_VARIABLE_{0}", this.model);
    }
    if (!newTechnicalName || !getDefaultVariableName) {
      this.measureModel.setProperty(`/${conversionTypeValue}ConversionData/isOkEnabled`, false);
    }
    // Sets the initial state for technical and business names
    this.measureModel.setProperty(`/${conversionTypeValue}ConversionData/sourceVariable/key`, newTechnicalName);
    this.measureModel.setProperty(`/${conversionTypeValue}ConversionData/sourceVariable/text`, getDefaultVariableName);
    // Sets the original values for technical and business names to be used for reset
    this.measureModel.setProperty(
      `/${conversionTypeValue}ConversionData/sourceVariable/originalTechnicalName`,
      newTechnicalName
    );
    this.measureModel.setProperty(
      `/${conversionTypeValue}ConversionData/sourceVariable/originalBusinessName`,
      getDefaultVariableName
    );
    this.measureModel.setProperty(`/${conversionTypeValue}ConversionData/isOkEnabled`, true);
  }

  public onSelectNewSourceVariable(oEvent: IEvent<sap.m.RadioButton, { selected: boolean }>): void {
    const selectedRadioButton = oEvent.getParameter("selected");
    const selectRadioButtonId = oEvent.getSource()?.getId();
    const conversionType = selectRadioButtonId.split("--")[0];
    const conversionTypeValue =
      conversionType === CONVERSION_TYPE_PROPERTIES.TARGET_UNIT ? CONVERSION_TYPES.UNIT : CONVERSION_TYPES.CURRENCY;
    if (selectedRadioButton) {
      this.resetSourceVariableTable(conversionType);
      this.measureModel.setProperty("/isNewSourceInputEnable", true);

      if (
        !!this.measureModel.getProperty(`/${conversionTypeValue}ConversionData/sourceVariable/key`) &&
        !!this.measureModel.getProperty(`/${conversionTypeValue}ConversionData/sourceVariable/text`)
      ) {
        this.measureModel.setProperty(`/${conversionTypeValue}ConversionData/isOkEnabled`, true);
      }
    }
  }

  private resetSourceVariableTable(currencyType) {
    const conversionTypeValue =
      currencyType === CONVERSION_TYPE_PROPERTIES.TARGET_UNIT ? CONVERSION_TYPES.UNIT : CONVERSION_TYPES.CURRENCY;
    const { tableId } = this.getConversionTableId(currencyType, CONVERSION_TYPE_KEYS.VARIABLES);
    const oTable = sap.ui
      .getCore()
      // eslint-disable-next-line @typescript-eslint/restrict-plus-operands
      .byId(currencyType + "--" + tableId) as sap.ui.table.Table;
    this.measureModel.setProperty(`/${conversionTypeValue}ConversionData/isOkEnabled`, false);
    oTable.clearSelection();
  }

  public onSelectExistingSourceVariable(oEvent: IEvent<sap.m.RadioButton, { selected: boolean }>): void {
    const selectedRadioButton = oEvent.getParameter("selected");
    const selectRadioButtonId = oEvent.getSource()?.getId();
    const conversionType = selectRadioButtonId.split("--")[0];
    if (selectedRadioButton) {
      this.measureModel.setProperty("/isNewSourceInputEnable", false);
      this.resetSourceVariableTable(conversionType);
    }
  }

  /**
   * Runs when the user leaves the focus from currency/unit conversion variable technical name
   * Checks for validation messages and resets the technical name if there is an error
   */
  public async onCCVariableTechnicalNameChange() {
    const conversionTypeValue = this.isCurrencyConversionMeasure() ? CONVERSION_TYPES.CURRENCY : CONVERSION_TYPES.UNIT;

    const technicalNameStateText = this.measureModel.getProperty(
      `/${conversionTypeValue}ConversionData/sourceVariable/technicalNameStateText`
    );
    // Checks for the state text from the input for validation
    if (technicalNameStateText) {
      this.resetCVariableTechnicalName();
      return;
    }
    // If there is no error, sets the original technical name to be used for reset
    // The sourceVariable/key will remain the same as informed by the user since its valid and will be used as
    // technical name when the user submits the dialog
    const currentTechnicalName = this.measureModel.getProperty(
      `/${conversionTypeValue}ConversionData/sourceVariable/key`
    );
    this.measureModel.setProperty(
      `/${conversionTypeValue}ConversionData/sourceVariable/originalTechnicalName`,
      currentTechnicalName
    );
  }

  /**
   * Runs when the user leaves the focus from currency/unit conversion variable business name
   * Checks for validation messages and resets the business and technical names if there is an error
   */
  public async onCCVariableBusinessNameChange() {
    const conversionTypeValue = this.isCurrencyConversionMeasure() ? CONVERSION_TYPES.CURRENCY : CONVERSION_TYPES.UNIT;

    const businessNameStateText = this.measureModel.getProperty(
      `/${conversionTypeValue}ConversionData/sourceVariable/businessNameStateText`
    );
    const technicalNameStateText = this.measureModel.getProperty(
      `/${conversionTypeValue}ConversionData/sourceVariable/technicalNameStateText`
    );
    // Checks the state texts from the inputs for validation
    if (businessNameStateText || technicalNameStateText) {
      this.resetCVariableBusinessAndTechnicalName();
      return;
    }

    // If there is no error, sets the original business and technical names to be used for reset
    // The sourceVariable/text and /key will remain the same as informed by the user since its valid and will be used as
    // business name and technical names when the user submits the dialog
    const currentBusinessName = this.measureModel.getProperty(
      `/${conversionTypeValue}ConversionData/sourceVariable/text`
    );
    const currentTechnicalName = this.measureModel.getProperty(
      `/${conversionTypeValue}ConversionData/sourceVariable/key`
    );
    this.measureModel.setProperty(
      `/${conversionTypeValue}ConversionData/sourceVariable/originalBusinessName`,
      currentBusinessName
    );
    this.measureModel.setProperty(
      `/${conversionTypeValue}ConversionData/sourceVariable/originalTechnicalName`,
      currentTechnicalName
    );
  }

  /**
   * Resets the business and technical names in the view to the original values
   * Used in the CurrencyConversionConversionRateTypeDialog.fragment.xml
   */
  public resetCVariableBusinessAndTechnicalName() {
    const conversionTypeValue = this.isCurrencyConversionMeasure() ? CONVERSION_TYPES.CURRENCY : CONVERSION_TYPES.UNIT;
    const originalTechnicalName = this.measureModel.getProperty(
      `/${conversionTypeValue}ConversionData/sourceVariable/originalTechnicalName`
    );
    const originalBusinessName = this.measureModel.getProperty(
      `/${conversionTypeValue}ConversionData/sourceVariable/originalBusinessName`
    );
    setTimeout(() => {
      this.measureModel.setProperty(`/${conversionTypeValue}ConversionData/sourceVariable/text`, originalBusinessName);
      this.measureModel.setProperty(`/${conversionTypeValue}ConversionData/sourceVariable/key`, originalTechnicalName);
      this.measureModel.setProperty(`/${conversionTypeValue}ConversionData/isOkEnabled`, true);
      this.measureModel.setProperty(`/${conversionTypeValue}ConversionData/sourceVariable/businessNameStateText`, "");
      this.measureModel.setProperty(`/${conversionTypeValue}ConversionData/sourceVariable/technicalNameStateText`, "");
    }, 2000);
  }

  /**
   * Resets the technical name in the view to the original values
   * Used in the CurrencyConversionConversionRateTypeDialog.fragment.xml
   */
  public resetCVariableTechnicalName() {
    const conversionTypeValue = this.isCurrencyConversionMeasure() ? CONVERSION_TYPES.CURRENCY : CONVERSION_TYPES.UNIT;
    const originalValue = this.measureModel.getProperty(
      `/${conversionTypeValue}ConversionData/sourceVariable/originalTechnicalName`
    );
    setTimeout(() => {
      this.measureModel.setProperty(`/${conversionTypeValue}ConversionData/sourceVariable/key`, originalValue);
      this.measureModel.setProperty(`/${conversionTypeValue}ConversionData/sourceVariable/technicalNameStateText`, "");
      this.measureModel.setProperty(`/${conversionTypeValue}ConversionData/isOkEnabled`, true);
    }, 2000);
  }

  /**
   * Runs after each keystroke from the user in the currency/unit conversion variable business name input
   * Derives the technical name from the business name and sets it in the view
   * Runs validations for names and sets value state text for the inputs if they are not valid
   * @param oEvent
   */
  public handleCCBusinessNameLiveChange(oEvent: IEvent<sap.m.Input, { id: string }>) {
    const conversionTypeValue = this.isCurrencyConversionMeasure() ? CONVERSION_TYPES.CURRENCY : CONVERSION_TYPES.UNIT;
    const businessName = oEvent.getSource().getValue();
    const originalTechnicalName = this.measureModel.getProperty(
      `/${conversionTypeValue}ConversionData/sourceVariable/originalTechnicalName`
    );

    const nameValidator = NamingHelper.getNameInputValidator();
    const propertyName = oEvent.getParameter("id").split("--")[0];
    const businessNameInputId: sap.m.Input = sap.ui
      .getCore()
      .byId(propertyName + "--" + "sourceVariableBusinessName") as sap.m.Input;
    const technicalNameInputId: sap.m.Input = sap.ui
      .getCore()
      .byId(propertyName + "--" + "sourceVariableTechnicalName") as sap.m.Input;

    const technicalName = nameValidator
      .deriveTechnicalName(
        businessNameInputId.getValue(),
        getNameUsageForObject(AnalyticModelObjectType.VARIABLE, this.model)
      )
      .toUpperCase();
    technicalNameInputId.setValue(technicalName);

    const validationBusinessNameResult = this.isVariableBusinessNameValid(businessName, originalTechnicalName);
    const validationTechnicalNameResult = this.isVariableTechnicalNameValid(technicalName, originalTechnicalName);

    if (validationBusinessNameResult.isValid) {
      this.measureModel.setProperty(`/${conversionTypeValue}ConversionData/sourceVariable/businessNameStateText`, "");
    } else if (validationBusinessNameResult.isEmpty) {
      this.measureModel.setProperty(
        `/${conversionTypeValue}ConversionData/sourceVariable/businessNameStateText`,
        this.getText("variableBusinessNameEmpty")
      );
    } else if (validationBusinessNameResult.isDuplicated) {
      this.measureModel.setProperty(
        `/${conversionTypeValue}ConversionData/sourceVariable/businessNameStateText`,
        this.getText("variableBusinessNotUnique")
      );
    }

    if (validationTechnicalNameResult.isValid) {
      this.measureModel.setProperty(`/${conversionTypeValue}ConversionData/sourceVariable/technicalNameStateText`, "");
    } else if (validationTechnicalNameResult.isEmpty) {
      this.measureModel.setProperty(
        `/${conversionTypeValue}ConversionData/sourceVariable/technicalNameStateText`,
        this.getText("variableTechnicalNameEmpty")
      );
    } else if (validationTechnicalNameResult.isDuplicated) {
      this.measureModel.setProperty(
        `/${conversionTypeValue}ConversionData/sourceVariable/technicalNameStateText`,
        this.getText("variableTechnicalNotUnique")
      );
    }
    this.measureModel.setProperty(
      `/${conversionTypeValue}ConversionData/isOkEnabled`,
      validationBusinessNameResult.isValid && validationTechnicalNameResult.isValid
    );
  }

  /**
   * Runs after each keystroke from the user in the currency/unit conversion variable technical name input
   * Derives the technical name from the input (removes special characters and converts to uppercase)
   * Runs validations for technical name and sets value state text for the input
   * @param oEvent
   */
  public handleCCTechnicalNameLiveChange(oEvent: IEvent<sap.m.Input, { id: string }>) {
    const conversionTypeValue = this.isCurrencyConversionMeasure() ? CONVERSION_TYPES.CURRENCY : CONVERSION_TYPES.UNIT;

    const originalValue = this.measureModel.getProperty(
      `/${conversionTypeValue}ConversionData/sourceVariable/originalTechnicalName`
    );
    const nameValidator = NamingHelper.getNameInputValidator();
    const propertyName = oEvent.getParameter("id").split("--")[0];
    const technicalNameInput: sap.m.Input = sap.ui
      .getCore()
      .byId(propertyName + "--" + "sourceVariableTechnicalName") as sap.m.Input;

    if (!this.enableNewNameChecks()) {
      nameValidator.onTechnicalNameChanged(technicalNameInput, technicalNameInput, "name", {
        duplicatedTechnicalName: this.getText("variableTechnicalNotUnique"),
        emptyTechnicalName: this.getText("variableTechnicalNameEmpty"),
      });
    }

    const newTechnicalName = oEvent.getSource().getValue().toUpperCase();

    const inputElementFocus = technicalNameInput.getFocusDomRef() as HTMLInputElement;
    const selectionStart = inputElementFocus.selectionStart;
    const selectionEnd = inputElementFocus.selectionEnd;

    technicalNameInput.setValue(newTechnicalName);
    technicalNameInput.setLastValue(originalValue);

    inputElementFocus.setSelectionRange(selectionStart, selectionEnd);

    const validationResult = this.isVariableTechnicalNameValid(newTechnicalName, originalValue);

    if (validationResult.isValid && originalValue !== newTechnicalName) {
      this.measureModel.setProperty(`/${conversionTypeValue}ConversionData/sourceVariable/technicalNameStateText`, "");
      this.measureModel.setProperty(`/${conversionTypeValue}ConversionData/isOkEnabled`, true);
    } else if (validationResult.isEmpty) {
      this.measureModel.setProperty(
        `/${conversionTypeValue}ConversionData/sourceVariable/technicalNameStateText`,
        this.getText("variableTechnicalNameEmpty")
      );
      this.measureModel.setProperty(`/currencyConversionData/isOkEnabled`, false);
    } else if (validationResult.isDuplicated) {
      this.measureModel.setProperty(
        `/${conversionTypeValue}ConversionData/sourceVariable/technicalNameStateText`,
        this.getText("variableTechnicalNotUnique")
      );
      this.measureModel.setProperty(`/currencyConversionData/isOkEnabled`, false);
    } else if (!validationResult.isValid) {
      this.measureModel.setProperty(
        `/${conversionTypeValue}ConversionData/sourceVariable/technicalNameStateText`,
        validationResult.errorMessages
      );
      this.measureModel.setProperty(`/${conversionTypeValue}ConversionData/isOkEnabled`, false);
    }
  }

  public async onRecordTypeFieldChange() {
    const recordTypeFieldSelect = this.byId("recordTypeFieldSelect") as sap.m.Select;
    const recordTypeField = recordTypeFieldSelect.getSelectedKey();
    const oldRecordTypeField = this.model.getNonCumulativeSettings().recordTypeAttributeKey;
    const command = new UpdateRecordTypeAttribute(recordTypeField, oldRecordTypeField);
    this.stack.execute(command);
    await Util.Component.modelChangeHandler([ModelUIProperties.MEASURES, ModelUIProperties.ATTRIBUTES]);
  }

  public async onTimeDimensionChange() {
    const timeDimensionSelect = this.byId("timeDimensionSelect") as sap.m.Select;
    const timeDimension = timeDimensionSelect.getSelectedKey();
    const timeDimensionFields: TimeDimensionFields = this.uiModel.getProperty("/timeDimensionFields");
    const timeDimensionField = timeDimensionFields.find(({ key }) => key === timeDimension);
    const command = new UpdateTimeDimension(timeDimensionField.sourceKey);
    this.stack.execute(command);
    await Util.Component.modelChangeHandler([ModelUIProperties.MEASURES]);
  }

  public async onReportingMinStartTimeChange() {
    const reportingMinStartDatePicker = this.byId("reportingMinStartTime") as sap.m.DatePicker;
    const reportingMinStartDateValue = reportingMinStartDatePicker.getDateValue();
    const formatter = sap.ui.core.format.DateFormat.getDateInstance({ pattern: "yyyy-MM-dd" });
    const reportingMinStartDateUtc = !!reportingMinStartDateValue ? formatter.format(reportingMinStartDateValue) : "";
    const command = new UpdateReportingMinStartTime(reportingMinStartDateUtc);
    this.stack.execute(command);
    await Util.Component.modelChangeHandler([ModelUIProperties.MEASURES]);
  }

  public async onReportingMaxEndTimeChange() {
    const reportingMaxEndDatePicker = this.byId("reportingMaxEndTime") as sap.m.DatePicker;
    const reportingMaxEndDateValue = reportingMaxEndDatePicker.getDateValue();
    const formatter = sap.ui.core.format.DateFormat.getDateInstance({ pattern: "yyyy-MM-dd" });
    const reportingMaxEndDateUtc = !!reportingMaxEndDateValue ? formatter.format(reportingMaxEndDateValue) : "";
    const command = new UpdateReportingMaxEndTime(reportingMaxEndDateUtc);
    this.stack.execute(command);
    await Util.Component.modelChangeHandler([ModelUIProperties.MEASURES]);
  }

  /*
   * Wait to validate expression since initialization of expression editor is async
   * Waiting is only necessary for AM internal routing because user can open editor in a measure.
   */
  public async validateExpressionAfterEditorLoads(change: boolean = false) {
    const expressionEditor = this.isRestrictedMeasure()
      ? this.byId("restrictedMeasureExpressionEditor")
      : this.byId("calculatedMeasureExpressionEditor");

    if (expressionEditor.loaded) {
      await this.validateExpression(change);
      return;
    }
    const waitToValidate = () => {
      setTimeout(() => {
        if (!expressionEditor.loaded) {
          waitToValidate();
        } else {
          void this.validateExpression(change);
        }
      }, 0);
    };
    waitToValidate();
  }

  public async onUsedInMeasurePropertyPress(event: sap.ui.base.Event) {
    const usedInObject = this.measureModel.getProperty(event.getSource().getBindingContext("measure").getPath());
    const technicalName = usedInObject.technicalName;
    this.goToUsedInProperty(UsedInType.MEASURE, technicalName);
  }

  private isClientIdEmpty(): boolean {
    const conversionDataPath = this.isCurrencyConversionMeasure()
      ? "/currencyConversionData/clientID"
      : "/unitConversionData/clientID";

    const clientID = this.measureModel.getProperty(conversionDataPath);
    return !clientID || (Array.isArray(clientID) && clientID.length === 0);
  }
}

export const MeasureDetails = smartExtend(
  AnalyticModelPropertiesBase,
  "sap.cdw.components.cubebuilder.controller.MeasureDetails",
  MeasureDetailsClass
);

sap.ui.define("sap/cdw/components/cubebuilder/controller/MeasureDetails.controller", [], function () {
  return MeasureDetails;
});
