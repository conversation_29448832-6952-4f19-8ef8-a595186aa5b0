#XTIT: Title for cube builder
cubeBuilderTitle=Welcome to the Query Builder
#YMSG: Description for cube builder
cubeBuilderDescription=
#XTIT: New Cube Tile
newCube=New Query
#~~~~~~~~~~~ Space Selection ~~~~~~~~~~~~~~~~~~~
#XTIT: Title for cube builder
spaceSelectionTitle=Welcome to the Query Builder
#YMSG: Description for cube builder
spaceSelectionDescription=
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Section header general
generalSetting=General
#XFLD: Save
save=Save
#XFLD: Save As
saveAs=Save As
#XFLD: Save Anyway
saveAnyway=Save Anyway
#XFLD: no default value in list
txtNoDefaultValue= No Default Value
#XFLD: Deploy
deploy=Deploy
#XFLD: Deploy Anyway
deployAnyway=Deploy Anyway
#XFLD: Section header edit
edit=Edit
#XFLD: Import
import=Import
#XFLD: Import CSN
importCSN=Import CSN
#XFLD: Export
export=Export
#XFLD: Export CSN
exportCSN=Export CSN
importRemoteSource=Import from Connection
about=About
resultCount={0} results
#XFLD Scale formatting option
txtScale=Scale
#XFLD Unformatted scale option
txtUnformatted=Unformatted
#XFLD Thousand scale option
txtThousand=Thousand
#XFLD Million scale option
txtMillion=Million
#XFLD Billion scale option
txtBillion=Billion
#XFLD Percent scale option
txtPercent=Percent
#XFLD Decimal Places formatting option
txtDecimalPlaces=Decimal Places
#XTIT: Title for measure standard formatting section
formatTitle=Formatting
#XFLD: Add cube source
addButton=Add
#XFLD: Preview cube source data
previewButton=Preview
#XFLD: Preview data in table format
dataPreviewTable=Data Preview
#XFLD: Preview data in diagram format
dataPreviewDiagram=Diagram
#XTOL: Tooltip for preview button when it is disabled due to version mode
previewButtonTooltipReadOnly=The preview is not available for past versions.
#XTOL: Tooltip for preview button when it is enabled
previewButtonTooltip=Show the data preview
#XFLD: Label of item in layout menu list
LeftToRight=Left-Right
#XFLD: Label of item in layout menu list
TopToBottom=Top-Bottom
#XFLD: Label of item in layout menu list
BottomToTop=Bottom-Top
#XFLD: Label of item in layout menu list
RightToLeft=Right-Left
#XTOL
expandOrCollapsToolbar=Expand or collapse toolbar
#XMSG message explaining why the support information cannot be exported
modelIsNotValidCantExportSupportInformation=Support information cannot be exported because the model is not valid. Please fix the errors in the model before exporting.
#XFLD: Load more keys
loadMoreKeys=Load more keys
#XFLD: Load more keys
moreElementsList={0} more

#XTIT: Title for measure dependencies
measureDependencies=Measure Dependencies

#XTIT: Title for cross calculation dependencies
crossCalculationDependencies=Cross Calculation Dependencies

#XLBL: Placeholder for display options
displayOptionsPlaceholder=Show Additional Nodes

#XMSG: Error message for view when view object is not deployed yet
cannotViewNotDeployed=Object "{0}" has never been deployed. Please deploy it before viewing.

getCubesError=An error occurred while trying to retrieve the selected query.
dataPreviewUnavailable=Data preview not yet available for this type of entity.
sourceDataPreviewError=A data preview visualization for the selected source could not be retrieved.

#XMSG: Error message for preview when preview object is not deployed yet
cannotPreviewNotDeployed=Object "{0}" has never been deployed. Please deploy it before preview.

#XMSG: Error message for preview when hana is down
hanaDown=This feature is disabled as the run-time database is unavailable.

## Semantic types
#XFLD
@Semantics.currencyCode=Currency Code
#XFLD
@Semantics.imageUrl=Image URL
#XFLD
@Semantics.unitOfMeasure=Unit of Measure
#XFLD
@Semantics.amount.currencyCode=Amount with Currency
#XFLD
@Semantics.quantity.unitOfMeasure=Quantity with Unit
#XFLD
@Semantics.text=Text
#XFLD
@Semantics.businessDate.from=Business Date - From
#XFLD
@Semantics.businessDate.to=Business Date - To
#XFLD
@Semantics.businessDate.at=Business Date - At
#XFLD
@Semantics.fiscal.year=Fiscal - Year
#XFLD
@Semantics.fiscal.period=Fiscal - Period
#XFLD
@Semantics.fiscal.yearPeriod=Fiscal - Year Period
#XFLD
@Semantics.fiscal.yearVariant=Fiscal - Year Variant
#XFLD
@Semantics.calendar.dayOfMonth=Calendar - Day of Month
#XFLD
@Semantics.calendar.dayOfYear=Calendar - Day of Year
#XFLD
@Semantics.date=Calendar - Date
#XFLD
@Semantics.calendar.week=Calendar - Week
#XFLD
@Semantics.calendar.month=Calendar - Month
#XFLD
@Semantics.calendar.quarter=Calendar - Quarter
#XFLD
@Semantics.calendar.halfyear=Calendar - Half Year
#XFLD
@Semantics.calendar.year=Calendar - Year
#XFLD
@Semantics.calendar.yearWeek=Calendar - Year Week
#XFLD
@Semantics.calendar.yearMonth=Calendar - Year Month
#XFLD
@Semantics.calendar.yearQuarter=Calendar - Year Quarter
#XFLD
@Semantics.calendar.yearHalfyear=Calendar - Year Half Year
#XFLD
@Semantics.language=Language
#XFLD
@Semantics.systemDate.createdAt=System Date - Created At
#XFLD
@Semantics.systemDate.lastChangedAt=System Date - Last Changed At
#XFLD
@Semantics.geoLocation.longitude=Geolocation - Longitude
#XFLD
@Semantics.geoLocation.latitude=Geolocation - Latitude
#XFLD
@Semantics.geoLocation.cartoId=Geolocation - Cartoid
#XFLD
@Semantics.geoLocation.normalizedName=Geolocation - Normalized Name

@revert=Revert to Deployed Version
@revertDeployedVersion=Revert to Deployed Version
#XMSG MessageBox text when it is failed to restore deployed version since backend cannot not find it
@revertDeployedVersionNotFound=Unable to restore deployed version as it cannot be found.
@msgRevertDeployedVersion1=Reverting to the deployed version will undo any local changes. This action cannot be undone.
@msgRevertDeployedVersion2=Do you want to continue?
#XFLD label for package selector
@txtPackage=Package
#XMSG warning for repository package assignment changed.
VAL_PACKAGE_CHANGED=You have assigned this object to package "{1}". Click “Save” to confirm and validate this change. Note that assignment to a package cannot be undone in this editor after you saved.
#XMSG warning for repository package dependency issue.
MISSING_DEPENDENCY=Dependencies of object "{0}" cannot be resolved in the context of package "{1}".
close=Close
repository=Repository
remoteSources=Sources
txtPreviewSQL=Preview SQL
txtDataPreview=Data Preview
txtNoData=No data found.
searchResultEmpty=No data can be found in your search result.
emptyList=No data can be found on the list.
#XFLD: Default value for business name input
defaultBusinessName=New Analytic Model
#XTIT: Title for source data tab
sourceData=Source Data
cubeSourcesTitle=Sources
measureSourcesListTitle=Measure Sources ({0})
dimensionSourcesListTitle=Dimension Sources ({0})
createdBy=Created by:
#XCKL: Label for is auxiliary checkbox
isAuxiliary=Is Auxiliary
#XFLD: Label for object status field in source tile
status=Status:
description=Description:
clearFilter=Clear
refresh=Refresh
customizeColumnsButton=Columns
#XTIT: Title for analytic model tab
queryModel=Analytic Model
#XBUT: Title for Analytic Model button
queryModelButton=Model
#XTIT: Title for perspective tab
perspectives=Perspectives
#XTIT: Title for summary tab
summary=Summary & Settings
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XTIT: Summary tab title
summaryTabTitle=General
#XFLD: Label for query business name input
summaryBusinessName=Name
#XFLD: Label for query technical name input
summaryTechnicalName=Technical Name
#XFLD: Label for query authorization scenarios multi-input
summaryAddAuthorizationScenario=Add Authorization Scenarios
#XFLD: Label for query settings
summaryQuerySettings=Analytic Model Settings
#XFLD: Label for public data access switch
allowDataAccess=Allow public data access
txtType=Type
txtMessage=Message
txtLocation=Location
typeForCubeFiles=Perspective
#XMSG
txtProblems=Errors
txtNoProblems=No problems
cubeSaved=Analytic model was saved.
cubeRestored=Analytic model was restored.
#XFLD measure semantic type
amountWithCurrency=Amount with Currency
#XFLD measure semantic type
quantityWithUnit=Quantity with Unit
#XFLD measure semantic type
unknownSemanticType=Unknown Semantic Type
#XFLD measure semantic type
noSemanticType=None
#XFLD data type none (only used when we can't find it because association was deleted)
noDataType=None
#XFLD value help none (only used when param doesn't have value help)
noValueHelp=None
#XFLD None
none=None
#XFLD Default
default=Default
#XFLD: Object status (repo: "#objectStatus")
@objectStatus=Status
#XFLD: Object status, model has been changed but changes are not yet deployed (repo: "#objectStatus")
@statusChangesToDeploy=Changes to Deploy
#XFLD: Object status, deployed model has run-time errors, example: data type of used source has been changed (repo: "#objectStatus")
@statusRunTimeError=Runtime Error
#XFLD: Object status, deployed model is OK current/changed model is not consistent, example: error in expression of calc. column, after changing the model (repo: "#objectStatus")
@statusDesignTimeError=Design Time Error
#XFLD: Object status, model has never been deployed up to now / no deployment date exists (repo: "#objectStatus")
@statusNew=Not Deployed
#XFLD: Object status, currently some repository objects like "dataflow" to not provide an object status (repo: "#objectStatus")
@hasNoObjectStatus=Object Type w/o Status
#XFLD: Object status, model has  been deployed / deployment date exists (repo: "#objectStatus")
@statusActive=Deployed
#XFLD: Object status (deployment status)
@statusRevised=Local Updates
#XFLD: Object status (deployment status), model deployment failed
@statusFailed=Failed
#XFLD: Object status (deployment status), model is still being deployed
@statusPending=Deploying...
@itemsNoMoreThan=Cannot display more than {0} items.
#XMSG: Warning message in source tree connection in case replication fails.
replicationWarning=This connection cannot be used.
loading=Loading...
#XTIT: Root node for all connections
remotesources=Connections
landingBreadcrumb={spaceId}
createBreadcrumb=New Analytic Model
editorBreadcrumb={model}
#XTIT: No data for add associated column in dimension source
associatedColumnNoDataText=No associations to add
#XTIT: Add association dialog title
associationDialogTitle=Select Properties To Be Copied
#XTIT: Add association dialog Select button
associationDialogSelectButton=Select
#XTIT: Add association dialog Select button
associationDialogCancelButton=Cancel
#XTIT: select data access control
selectDataAccessControlTitle=Select Data Access Control
#XBUT:To select data access control in dialog
selectDataAccessControlOk=Select
#XTIT: header title for data access control
dataAccessControl=Data Access Control
#XTIT: header title for data access controls
outputDataAccessControlsListTitle=Data Access Controls ({0})
#XBUT:add data access control tooltip
addDataAccessControl=Add Data Access Control
#XBUT:edit data access control tooltip
editDataAccessControl=Edit Data Access Control
#XTIT: header title for data access join
joinDataAccessControl=Join
#XTIT: Add association dialog add all measure checkbox
associatedAllMeasures=Add all measures to analytic model
#XTIT: Add association dialog add all  attribute checkbox
associatedAllAttributes=Add all attributes to analytic model
#XTIT: Add association dialog add all  dimensions checkbox
associatedAllDimensions=Add all dimensions to analytic model
#XMSG: Error message when retrieving CubeModel from Analytical Dataset Result
loadAnalyticalDataSetError=An error occurred while trying to retrieve data from analytical data set.
#XMSG: Message informing that no association for selected source
noAssociationData=No associated dimension available
#XMSG: Message informing that no parameter for selected source
noParameterData=No parameter available
#XTIT: Add association dialog column name
associatedDimensions=Associated Dimensions
#XLBL: Associated dimenension source label
associatedDimensionSource=Technical Name of Dimension
#XTIT: Add association dialog target column name
targetEntity=Target Business Entity
#XTIT: Add association dialog foreing key column name
foreignKey=Foreign Key Members
#XCOL: Parameters table column title for Parameter name
parameter=Parameter
#XCOL: Parameters table column title for Action name
txtAction=Action
#XCOL: Parameters table column title for Value name
txtValue=Value
#XBUT: label for Action combobox set value
txtSetValue=Set Value
#XBUT: label for Action combobox set system date
txtSetSystemDate=Set System Date
#XBUT: label for Action combobox map to
txtMapTo=Map to
#XBUT: label for Action combobox inherit
txtInherit=Inherit
#XBUT: label for segment button active
txtActive=Active ({0})
#XBUT: label for segment button Inactive
txtInActive=Inactive ({0})
#XBUT: label for map to combobox
txtNewInputParameter=New Variable
#XLBL: Parameter Status: Not Mapped
txtnotmapped=Not Mapped
#XLBL: Parameter Status: Not Used
txtnotused=Not Used
#XLBL: Parameter Status: Not value assigned
txtnoValueAssigned=No value assigned
#XTIT: Default title for set value dialog
txtSetValueForParam=Set Value for Input Parameter {0}
#XTIT: Default title for set value dialog
txtSetValueForStackedVariable=Set Value for Variable {0}
txtNoParameters=There are no parameters available
#XBUT: tooltip
more=More...
#XBUT: tooltip for measure edit
editMeasure=Edit Measure
#XBUT: tooltip for dimension edit
editDimension=Edit Dimension
#XBUT: tooltip for attribute edit
editAttribute=Edit Attribute
#XBUT: tooltip for info popover
moreInfo=More Information
#XBUT: tooltip for variable edit
editVariable=Edit Variable
#XBUT: tooltip for global filter edit
editGlobalFilter=Edit Filter
#XFLD
txtBaseType=Base Type
#XFLD
txtDefaultValue=Default Value
#XFLD
txtNewValue=New Value
#XLBL
txtMappedTo=Mapped to: {0}
#XLBL
txtValueWithPlaceHolder=Value: {0}
#XLBL
txtValueLabel=Value
#XMSG
VAL_ENTER_VALID_DECIMAL_GEN=Enter Valid Decimal value with precision {0} and scale {1}.
#XMSG: Message to enter correct Precision, Scale and Length value
VAL_ENTER_VALID_PRECISION=Enter a value between 1 and 38.
VAL_ENTER_VALID_SCALE=Enter a value between 0 and Precision value.
VAL_ENTER_VALID_LENGTH=Enter a value between 1 and 5000.
#XMSG
txtParameterMappingInfo=This source contains input parameters. For each parameter, you can either set a value or map it to a variable in your analytic model.
#XMSG
txtStackedVariableMappingInfo=The variables from the source are inherited.
#XMSG: Error message when model technical name is invalid
enterValidModelName=Please enter a valid model name.
#XMSG: Error message when no model name provided
enterModelName=Please enter a model name.
#XMSG: Error message for error when saving analytic model
saveQueryModelFailure=The analytic model couldn’t be saved.
#XMSG: Error message when an invalid space is selected
invalidSpaceError=Invalid space selected, please selected a valid space to continue.
#XMSG: Message informing saving action is in progress
saving=Saving...
#XCOL: Sources table view column title for business name
sourcesTableBusinessName=Business Name
#XCOL: Sources table view column title for technical name
sourcesTableTechnicalName=Technical Name
#XCOL: Sources table view column title for type
sourcesTableType=Type (Semantic Usage)
#XCOL: Sources table view column title for owner
sourcesTableOwner=Created by
#XCOL: Sources table view column title for changed by
sourcesTableChangedBy=Changed by
#XCOL: Sources table view column title for status
sourcesTableStatus=Status
#XCOL: Sources table view column title for details
sourcesTableDetails=Details
#XLNK: Sources table view column show details link
sourcesTableShowDetails=Learn More
#XLNK: link to show less
showLess=Show less
#XLNK: link to show more
showMore=Show more
#XTEXT: Ellipsis for truncated text
ellipsis=...
#XBUT: Button add source
addSourceButton=Add
#XMIT: Status filter menu item for all sources (unfiltered)
statusAll=All Statuses
#XMIT: Status filter menu item for deployed sources
statusDeployed=Deployed
#XMIT: Status filter menu item for not deployed sources
statusNotDeployed=Not Deployed
#XMSG: Message informing deployment was triggered
deploymentStarted=Deploying {0}. We will notify you when the process is complete.
#XMSG: Message informing deploy error
deployError=Deploy error
#XMSG: Message informing get runtime CSN error
runtimeCsnError=Runtime CSN Error
#XMSG: Message informing load model has failed
failedLoadingQueryModel=The analytic model object metadata couldn’t be loaded.
#XTIT: Placeholder for selected source in diagram
placeholderSelectedSource=Select node to see details
#XTIT: Title for Output Section on Analytic Model Tab
cubeOutputTitle=Output
#XTIT: Title for Associated Dimensions section on Output
detailsAssociatedDimensionListTitle=Associated Dimensions ({0})
#XTIT: Title for Non Foreign Key Associated Dimensions section on Output
nonForeignKeyAssociationTitle=Unlinked Associated Dimensions ({0})
#XTIT: Title for Measures section on Output
outputMeasureListTitle=Measures ({0})
#XTIT: Title for Filter section on Output
outputFilterListTitle=Filter ({0})
#XTIT: Title for Filters section on Output in stacked models
stackedOutputFilterListTitle=Filters ({0})
#XTIT: Title for Dependent Objects section on Output
outputDependentObjectsListTitle=Dependent Objects
#XTOL: Tooltip for the hidden in story icon
hiddenInStory=Hidden in Story
#XFLD: Context menu: Hide in story menu entry
hideInStory=Hide in Story
#XFLD: Context menu: Show in story menu entry
showInStory=Show in Story
#XTOL: Tooltip for the menu button
addAnalyticMeasure=Add Measure
#XFLD: Calculated measure menu entry
calculatedMeasure=Calculated Measure
#XFLD: Restricted measure menu entry
restrictedMeasure=Restricted Measure
#XFLD: Count distinct measure menu entry
countDistinctMeasure=Count Distinct Measure
#XFLD: Currency Conversion measure menu entry
currencyConversionMeasure=Currency Conversion Measure
#XFLD: Unit Conversion measure menu entry
unitConversionMeasure=Unit Conversion Measure
#XFLD:  Conversion measure menu entry
conversionMeasure=Conversion Measure
#XFLD: Non cumulative measure menu entry
nonCumulativeMeasure=Non-Cumulative Measure
#XFLD: Fact source measure menu entry
factSourceMeasure=Fact Source Measure
#XFLD: Unknown measure type menu entry
unknownMeasureType=Unknown Measure Type
#XFLD: Default calculated measure business name
defaultCalculatedMeasureBusinessName=Calculated Measure
#XFLD: Default restricted measure business name
defaultRestrictedMeasureBusinessName=Restricted Measure
#XMSG: MessageStrip for restore object
modelRestoredSuccessful=Restored version {0}.
#XFLD: Default count distinct measure business name
defaultCountDistinctMeasureBusinessName=Count Distinct Measure
#XFLD: Default currency conversion measure business name
defaultCurrencyConversionMeasureBusinessName=Currency Conversion Measure
#XFLD: Default currency/unit conversion measure business name
defaultConversionMeasureBusinessName=Conversion Measure
#XFLD: Default non-cumulative measure business name
defaultNonCumulativeMeasureBusinessName=Non Cumulative Measure
#XTIT: Default title for new measure in details form
newAnalyticMeasure=New Analytic Measure
#XFLD: Label for business name input field
businessName=Business Name
#XFLD: Label for selected node alias input field
selectedNodeAlias=Alias
#XFLD: Label for technical name input field
technicalName=Technical Name
#XFLD: Label for dimension technical name input field
dimensionTechnicalName=Technical Name of Dimension
#XFLD: Label for attribute technical name input field
attributeTechnicalName=Technical Name of Attribute
#XFLD: Label for affix overview
affixExample=Preview
#XFLD: Label for semantic tpye input field
semanticType=Semantic Type
#XFLD: Label for unit column input field
unitColumn=Unit Column
#XFLD: Label for measure type input field
measureType=Measure Type
#XFLD: Label for data type input field
dataType=Data Type
#XFLD: Label for precision (of decimal) input field
precision=Precision
#XFLD: Label for scale (of decimal) input field
scale=Scale
#XFLD: Label for source measure input field
sourceMeasure=Source Measure
#XFLD: Label for aggregation type input field
aggregationType=Aggregation Type
#XFLD: Special entry in aggregation type list: Aggregation type not fix, but inherited from underlying source measure
inherited=Inherited ({0})
#XTIT: Title for non-cumulative properties panel (for all non-cumulative measures in the model)
nonCumulativePropertiesTitle=Common Non-Cumulative Properties
#XTEXT: Explanation text for common properties of all non-cumulative measures of the model
nonCumulativePropertiesText=Changes in this section apply to all non-cumulative measures in the model.
#XTEXT: Message strip error text in case preconditions for non-cumulative measures not fulfilled
modelNotEnabledForNonCumulative=The model does not meet the prerequisites for non-cumulative measures: Record type field (integer) and time dimension (date).
#XMSG: Message for dimension used as record type
recordTypeFieldNoDeletion=The dimension "{0}" is used as record type field in non-cumulative measures and cannot be deleted.
#XFLD: Label for record type field (for non-cumulative measure) input field
recordTypeField=Record Type Field
#XFLD: Label for time dimension (of non-cumulative measure) input field
timeDimension=Time Dimension
#XFLD: Label for earliest reporting start date (for non-cumulative measure) input field
earliestReportingStartTime=Earliest Reporting Start Date
#XFLD: Label for latest reporting end date (for non-cumulative measure) input field
latestReportingEndTime=Latest Reporting End Date
#XFLD: Label for unbooked value handling checkbox
unbookedValuesHandling=Set Unbooked Values to 0
#XFLD: Label for non-cumulative measure exception aggregation type input field
exceptionAggregationNcumType=Exception Aggregation Type (for Time Dimension)
#XFLD: Label for exception aggregation type input field
exceptionAggregationType=Exception Aggregation Type
#XFLD: Label for exception aggregation dimensions combo box
exceptionAggregationAttributes=Exception Aggregation Dimensions
#XFLD: Label for X_OF_DIMENSION exception aggregation dimension combo box
exceptionAggregationAttribute=Exception Aggregation Dimension
#XTIT: Title for constant selection panel
constantSelectionTitle=Constant Selection
#XFLD: Label for dropdown to select constant selection type
constantSelectionType=Constant Dimensions
#XLST: Item in constant selection type dropdown for no constant selection
constantSelectionTypeNone=None
#XLST: Item in constant selection type dropdown for constant selection of all dimensions
constantSelectionTypeAll=All Dimensions
#XLST: Item in constant selection type dropdown for constant selection of selected dimensions
constantSelectionTypeSelected=Selected Dimensions
#XFLD: Label for multi combo box to select the dimensions where constant selection is applied
constantSelectionSelectedDimensions=Selected Constant Dimensions
#XMSG: Message for empty Used In Measures list in measure details
usedInMeasuresNoData=Measure is not being used.
#XLBL Label for used in (in info popover) with counter of usages
usedInCount=Used in ({0})
#XLBL Label for base attribute properties
baseAttributeProperties=Base Attribute Properties
#XLST: Entry prefix for function used as currency conversion reference date (e. g. Function - CURRENT_DATE)
functionEntry=Function
#XLST: Entry prefix for variable used as currency conversion reference date (e. g. Variable - REFERENCE_DATE)
variableEntry=Variable
#XFLD: Label for dimensions combo box (dimensions to be counted)
dimensions=Dimensions
#XLBL: Label for attributes in display options of dependency graph
attributes=Attributes
#XTIT: Title for functions tab in expression editor tools
functions=Functions
#XTIT: Title for measures tab in expression editor tools
measures=Measures
#XFLD: label for measure
measure=Measure
#XTIT: Title for variables (input parameters) tab in expression editor tools
variables=Variables
#XTIT: Title for operators tab in expression editor tools
operators=Operators
#XTIT: Title for measure in object info of expression editor tools
measureDetails=Measure Details
#XTIT: Title for attribute in object info of expression editor tools
attributeDetails=Dimension Details
#XTIT: Title for variable in object info of expression editor tools
variableDetails=Variable Details
#XFLD: Label for fact source field
factSource=Fact Source
#XFLD: Label for dimension source field
dimensionSource=Dimension Source
#XTIT: Title for fact source attribute
factSourceAttribute=Fact Source Attribute
#XTIT: Title for dimension source attribute
dimensionSourceAttribute=Dimension Source Attribute
#XMSG: Error message analytic measure business name empty
analyticMeasureBusinessNameEmpty=The business name of the analytic measure is empty.
#XMSG: Error message analytic measure technical name empty
analyticMeasureTechnicalNameEmpty=The technical name of the analytic measure is empty.
#XMSG: Error message analytic measure technical name not unique in measures
analyticMeasureTechnicalNameNotUniqueInMeasures=The technical name of the analytic measure is already used in measures.
#XMSG: Error message analytic measure technical name not unique in dimensions
analyticMeasureTechnicalNameNotUniqueInDimensions=The technical name of the analytic measure is already used in dimensions.
#XMSG: Error message analytic measure technical name not unique in cross calculations
analyticMeasureTechnicalNameNotUniqueInCrossCalculations=The technical name of the analytic measure is already used in cross calculations.
#XMSG: Error message variable technical name not unique in dimensions
variableTechnicalNameNotUniqueInDimensions=The technical name of the variable is already used in dimensions.
#XMSG: Error message source measure name empty
analyticMeasureSourceMeasureEmpty=Source measure must be selected.
#XMSG: Error message reference in expression cannot be found in measures or dimensions
referenceInExpressionNotFound=Measure or dimension "{0}" cannot be found.
#XMSG: Error message result column for lookup entity cannot be found
resultColumnInLookupEntityNotFound=Column "{0}" cannot be found.
#XMSG: Error message when variable derivation is not supported for restricted measure and filter variable
variableDerivationIsNotSupported=Variable derivation not supported.
#XMSG: Error when range and interval are not supported filter types
rangeAndIntervalFilterNotSupported=Range and Interval filter types not supported.
#XMSG: Error message lookup entity cannot be found
lookupEntityNotFound=Lookup entity "{0}" cannot be found.
#XMSG: Error message reference in expression cannot be found in dimensions
dimensionInExpressionNotFound=Dimension "{0}" cannot be found.
#XMSG: Error message reference in expression to the measure itself
measureSelfReference=The measure itself must not be referenced in the expression.
#XMSG: Error message function in expression cannot be found in function list
functionInExpressionNotFound=Function "{0}" cannot be found.
#XMSG: Validation message for Restricted Measure Variable cannot be found in variables
validationDescriptionRestrictedMeasureVariableMissing=Use the restricted measure variable in a restricted measure or delete the variable.
#XMSG: Validation message for Restriction Variable cannot be found in variables
validationDescriptionRestrictionVariableMissing=Use the restriction variable in a restricted measure/structure member or delete the variable.
#XMSG: Error message referenced restricted measure variable or standard variable in the formula that is incorrect used by type
measureContainsIncorrectVariableType=Expression for measure "{0}" contains variable "{1}" with incorrect type.
#XMSG: Error message function in expression has incorrect number of parameters
functionInExpressionParameterMismatch=The function "{0}" has an incorrect number of parameters.
#XMSG: Error message function in expression contains incorrect type
functionInExpressionIncorrectType=The function "{0}" contains an incorrect type "{1}".\nCorrect types are INTEGER, INT, DECIMAL, REAL, DOUBLE.
#XMSG: Error message invalid function in restricted measure expression
functionInRestrictedMeasureExpression=Functions must not be used in the restricted measure expression.
#XMSG: Error message expression for calculated measure empty
calculatedMeasureEmptyExpression=Expression for calculated measure must not be empty.
#XMSG: Error message reference in expression cannot be found in measures or dimensions
measureExpressionReferenceNotFound=Measure or dimension "{1}" cannot be found.
#XMSG: Error message reference in expression cannot be found in dimensions
measureExpressionDimensionNotFound=Dimension "{1}" cannot be found.
#XMSG: Error message function in expression cannot be found in function list
measureExpressionFunctionNotFound=Function "{1}" cannot be found.
#XMSG: Error message referenced restricted measure variable in formula cannot be found in variables
measureExpressionVariableNotFound=The restricted measure variable "{1}" cannot be found.
#XMSG: Error message referenced calculated measure variable in formula cannot be found in variables
calculatedMeasureExpressionVariableNotFound=The calculated measure variable "{1}" cannot be found.
#XMSG: Error message function in expression has incorrect number of parameters
measureExpressionFunctionParameterMismatch=The function "{1}" has an incorrect number of parameters.
#XMSG: Error message function in expression contains incorrect type
measureExpressionFunctionIncorrectType=The function "{1}" contains an incorrect type "{2}".\nCorrect types are INTEGER, INT, DECIMAL, REAL, DOUBLE.
#XMSG: Generic validation message for passed backend messages on measure and filter expressions
measureExpressionGenericBackendError={1}
#XMSG: Error message invalid function in global filter measure expression
functionInGlobalFilterExpression=Functions must not be used in the filter expression.
#XMSG: Error message expression for filter empty
globalFilterEmptyExpression=Expression for filter must not be empty.
#XMSG: Success message for expression validation
expressionValidationSuccess=The expression is valid.
#XTIT: Title for expression editor value help
expressionEditorValueHelpTitle=Value Help
#XMSG: Value state error text for value help selection with no selected values
valueHelpStateSelectionEmpty=Select a value or cancel.
#XMSG: Value state error text for value help selection for operator between with not two selected values
valueHelpStateSelectionBetweenMismatch=Select two values for the operator BETWEEN.
#XMSG: The current dimension, for which the value help is requested, cannot be found
valueHelpDimensionNotFound=Dimension "{0}" cannot be found.\n Select or enter a valid dimension for the value help.
#XMSG: Value help unavailable when the model’s fact source has an input parameter
valueHelpUnavailableInputParameter=Value help is not available for sources with input parameters.
#XMSG: Value help with parameter values not set when the model’s fact source has an input parameter
valueHelpUnavailableInputParametersNotSet=Value help is not available for sources with input parameters without set values.
#XTIT: Title for Attributes section on Output
outputSectionAttributesListTitle=Dimensions
#XTIT: Title for available Attributes
availableAttributesListTitle=Attributes ({0})
#XTIT: Title for available Attributes
availableDimensionsListTitle=Dimensions ({0})
#XTIT: Title for Parameters section on Output
outputParametersListTitle=Input Parameters ({0})
#XLBL: label for input parameter type
inputParameter=Input Parameter
#XTIT: Title for Association section on Output
outputAssociationListTitle=Association ({0})
#XTIT: Title for Variable section on Output
outputVariablesListTitle=Variables ({0})
#XMSG: Error message variable business name empty
variableBusinessNameEmpty=The business name of the variable is empty.
#XMSG: Error message variable business name not unique
variableBusinessNotUnique=The business name of the variable is not unique.
#XMSG: Error message variable technical name empty
variableTechnicalNameEmpty=The technical name of the variable is empty.
#XMSG: Error message variable technical name not unique
variableTechnicalNotUnique=The technical name of the variable is not unique.
#XFLD: Mandatory Variable detail page menu title
variableMandatory=Mandatory
#XTIT: Value Definition for variable detail page
variableValueDefinition=Value Definition
#XFLD: Variable lookup entity type label for variable detail page
lookupEntityLabel=Lookup Entity
#XTIT: Lookup Entity Dialog title
lookupEntityDialogTitle=Lookup Entity
#XTIT: Lookup Entity Parameter Mapping dialog title
lookupParamMappingTitle=Lookup Entity Parameter Mapping
#XTIT: Lookup Entity Parameter Name
lookupEntity=Lookup Entity
#XTIT: Settings for variable detail page
settings=Settings
#XTIT: General panel for variable detail page
gneral=General
#XFLD: Target Entity Column title
ResultColumnTitle=Result Column
#XFLD: Label for variable filled by type
parameterFilledBy=Variable filled by
#XFLD: Parameter filled by type option - manual input
manualInput=Manual Input
#XFLD: Parameter filled by type option - derive value
deriveValue=Derive Value
#XFLD: Parameter filled by type option - dynamic default
dynamicDefault=Dynamic Default
#XTOL: Tooltip for variable menu button
addAnalyticVariable=Add Variable
#XFLD: Source Variable menu title
sourceVariable=Source Variable
#XFLD: Standard Variable menu title
standardVariable=Standard Variable
#XFLD: Restricted Measure Variable menu title
restrictedMeasureVariable=Restricted Measure Variable
#XFLD: Restriction Variable menu title
restrictionVariable=Restriction Variable
#XFLD: Story Filter Variable menu title
storyFilterVariable=Filter Variable
#XFLD: Formula Variable menu title
formulaVariable=Formula Variable
#XFLD: Fiscal Variant menu title
fiscalVariantVariable=Fiscal Variant Variable
#XFLD: Reference Date menu title
referenceDateVariable=Reference Date Variable
#XLBL: Label for Fiscal Variant Variable message toast
fiscalVariantVariableCreated="Fiscal Variant" variable added.
#XMSG: Reference Date used in control description
referenceDateVariableUsedIn=Time dependent filter on {0} entities
#XMSG: Msg in used in variable list for derived variable to use the existing variable to get value help
txtEmptyUsedList=Use the variable to get value help for the value definition
#XMSG: Variable decimal precision scale value message
variableDecimalErrorMsg=Enter a valid value for decimal with precision {0} and scale {1}
#XMSG: Variable string error value message
variableStringErrorMsg=Enter a valid value for string with length {0}
#XMSG: Variable string error value message
variableIntegerErrorMsg= Enter an integer value of 0 or higher
#XMSG: Variable default value error message
defaultValueValidationError=Value is invalid for type {0}
#XMSG: Message displayed as description for parameter mapping with missing input parameter in source of look up entity
parameterMappingInLookupEntity=Parameter missing in lookup entity.
#XFLD: Label for length (of parameter) input field
length=Length
#XFLD: Label for srid
srid=Srid
#XFLD: Label for default value (of parameter) input field
defaultValue=Default Value
#XFLD:Default variable name for source variable
defaultNameVariableInput=Source Variable {0}
#XFLD:Default variable name for standard variable
defaultNameStandardVariableInput=Standard Variable {0}
#XFLD:Default name for Restricted Measure Variable
defaultNameVariableRestrictedMeasure=Restricted Measure Variable {0}
#XFLD:Default name for Restriction Variable
defaultNameVariableRestriction=Restriction Variable {0}
#XFLD: Default name for Reference Date Variable
defaultKeyDateName=Reference Date
#XFLD: Default name for Fiscal Variant Variable
defaultFiscalVariantName=Fiscal Variant
#XMSG: Message informing the user that a reference date variable already exists
reference_date_variable_already_exists=There can only be one reference date variable. You can edit the existing one.
#XMSG: Warning when model doesn’t have any time dependencies
model_does_not_have_time_dependencies=Variable is obsolete, the analytic model does not refer to any time dependent dimension or text table.
#XMSG: Warning when model doesn’t have any fiscal time dimensions
model_does_not_have_fiscal_time_dimension=Variable is obsolete, the analytic model does not refer to any fiscal time dimension.
#XMSG: Warning when changing a measure technical name
change_measure_technical_name_warning=Changing the technical name of the measure might affect existing stories.
#XMSG: Warning when changing a variable technical name
change_variable_technical_name_warning=Changing the technical name of the variable might affect existing stories.
#XMSG: Warning when changing a measure technical name with stacking
change_measure_technical_name_warning_stacking=Changing the technical name of the measure might affect existing stories or analytic models.
#XMSG: Warning when changing a variable technical name with stacking
change_variable_technical_name_warning_stacking=Changing the technical name of the variable might affect existing stories or analytic models.
#XFLD: Label for filter type (of parameter) input field
filterType=Filter Type
#XFLD: Label for attribute (of parameter) input field
variableDimension=Dimension
#XFLD: Label for used in Sources (of variable info popover) input field
usedInSources=Used in Sources
#XFLD: Label for grouped under dimension(of variable info popover for story filter variable) input field
groupUnderDimension=Grouped Under Dimension
#XFLD: Label for grouped under for fact source dimension (of variable info popover for story filter variable) input field
groupUnder=Grouped Under
#XFLD: Text for fact source dimension - groupped under dimension for story filter variable (as of today we use single fact source)
factsourceDimensions=Fact Source Dimensions
#XFLD: Label for used in Restricted Sources (of variable info popover) input field
usedInRestrictedMeasure=Used in Restricted Measures
#XFLD: Label for used in dimension (story filter variable info popover) input field
usedInDimension=Used for Dimension
#XFLD: Label for Exposed (of variable info popover) input field
exposed=Exposed
#XFLD: Label for title of input variable info popover input field
variableInfoInput=Input Variable
#XFLD: None type for boolean default input field
booleanType_defaultValue_none=none
#XFLD: Select type for boolean default input field true
booleanType_defaultValue_true=true
#XFLD: Select type for boolean default input field false
booleanType_defaultValue_false=false
#XFLD: Select type for variable selection type Single
filterVarSelectionTypeSingle=Single Value
#XFLD: Select type for variable selection type Interval
filterVarSelectionTypeInterval=Interval
#XFLD: Select type for variable selection type Range
filterVarSelectionTypeRange=Range
#XFLD: Select type for variable selection type Multiple Single Values
filterVarSelectionTypeMultiple=Multiple Single Values
#XFLD: Select type for variable selection type Single range values offered for old models
filterVarSelectionTypeSingleRange=Single Range (Deprecated)
#XFLD: Label for default value range option
rangeOption=Option
#XMSG: Error msg for range default low high value
rangeErrorMsg=Default "From" value must be lower than "To" value
#XFLD: Label for name input field of the global filter
globalFilterName=Name
#XFLD: Label for deployement date
deploymentDate=Deployed On
#XFLD: Label for allowing data export
allowDataExport=Allow Data Export
#XTIT: Global filter details title
globalFilter=Filter
#XTIT: Inherited filter details
inheritedFilter = Inherited Filter
#XTIT: stacked variable details title
inheritedVariable=Inherited
#XMSG: Error message global filter name empty
globalFilterNameEmpty=The name of the filter is empty.
#XMSG: Message describing where to add sources to an empty model
outputEmptyModelDescription=Add data from the Sources page to build your consumption model.
#XMSG: Error message when retrieving associations from a selected source model
loadDataEntityAssociationsError=An error ocurred while trying to retrieve associations.
#XMSG: Error message when using an outdated AM as fact source
loadDataEntityOutdatedModel=This analytic model is not up-to-date in its processing capabilities.\nSave and deploy the analytic model to update the way it is processed, then you can use it as a source.
#XBUT: Button and dialog remove source
remove=Remove
#XMSG: Message informing source to be added is already selected: Source is already selected (temporary)
alreadySelected=Multiple measure sources are not yet supported
#XMSG: Message informing replace for model fact source (stacking case) is not yet supported (temporary)
modelFactSourceNotSupported=Replace of model fact sources (stacking case) is not yet supported
#XMSG: Message informing that it is not possible to replace a fact source by itself
noFactSourceReplaceItself=A fact source cannot be replaced by itself.
#XMSG: Message informing that replacement is not supported for stacked models
replaceForStackedFactSourceNotSupported=Replacement for stacked models is not supported.
#XMSG: Message informing how to replace a fact source with drag & drop
dropOnFactSourceNode=To replace a fact source, drop the object onto a fact source node.
#XMSG: Message informing delta tables can not be used as fact sources
deltaTablesNotSupported=Delta tables are not supported as fact sources in the analytic model.
#XMSG: Message informing not exactly one source is selected
multipleSelected=Select one source
#XTIT: Title for dialog to select a replacement fact source
selectFactSource = Select Fact Source
#XBUT: Button to select replacement fact source
select=Select
#XTIT: Title for dialog to verify and confirm fact source replacement
replaceFactSource=Replace Fact Source "{0}" by "{1}"
#XBUT: Button to replace fact source
replace=Replace
#XTIT: Title for dialog to verify and confirm dimension source repair
repairDimensionSource=Repair Dimension Source "{0}"
#XBUT: Button to repair dimension source
repair=Repair
#XMSG: Message strip text for the replace fact source evaluation
explanationReplaceFactSource=When you replace a fact source, the analytic model assumes that the new fact source entity uses the same technical names for measures, attributes and variables.
#XMSG: Header text for the replace fact source evaluation table
headerReplaceFactSource={0} objects referenced in the analytic model do not have a corresponding object in the new fact source.
#XMSG: Alternative header text for the replace fact source evaluation table
headerReplaceFactSource2=All objects in the analytic model have a corresponding object in the new fact source.
#XCOL: Object type column header in replace evaluation table
objectTypeReplace=Object Type
#XCOL: Number of missing objects column header in replace evaluation table
missingObjectsNumber=Number of Missing Objects
#XCOL: Missing objects column header in replace evaluation table
missingObjectsReplace=Missing Objects
#XFLD: Object type entry for fact source measures in replace evaluation table
measuresReplace=Fact Source Measures
#XFLD: Object type entry for fact source attributes in replace evaluation table
attributesReplace=Fact Source Attributes
#XFLD: Object type entry for associations in replace evaluation table
associationsReplace=Associations
#XFLD: Object type entry for associated dimensions in replace evaluation table
associatedObjectsReplace=Associated Dimensions
#XFLD: Object type entry for variables in replace evaluation table
inputParametersReplace=Variables
#XFLD: Number of issues in replaced fact source among total objects
issuesCountReplace={0} of {1}
#XMSG: Text for the replace association result
replaceAssociationResult=For fact source attribute "{0}" the assigned text/association was changed from "{1}" to "{2}".
#XMSG: Text for the replace target entity result
replaceTargetEntityResult=For fact source attribute "{0}" the association target of association "{1}" was changed from "{2}" to "{3}".
#XMSG: Text for the replace association and target entity result
replaceAssociationAndTargetResult=For fact source attribute "{0}" the assigned text/association was changed from "{1}" to "{2}" and the association target was changed from "{3}" to "{4}".
#XMSG: Text for the repair option
repairDimensionSourceOption=Repair the dimension source and adapt it to the fact source changes?
#XTOL: Tooltip for time dependency icon
hasTimeDependency=Dimension has time dependency
#XTOL: Tooltip for hierarchy icon
hasHierarchy=Dimension has {0} hierarchies
#XTOL: Tooltip for hierarchy icon
hasHierarchySingular=Dimension has 1 hierarchy
#XTOL: Tooltip for attribute semantic type
attributeUnityOfMeasure=Dimension is a unit of measure
#XTOL: Tooltip for attribute semantic type
attributeCurrencyCode=Dimension is a currency code
#XTOL: Tooltip for fiscal time dimension
isFiscalTimeDimension=Dimension is a fiscal time dimension
#XTOL: Tooltip for measure semantic type
measureQuantityWithUnity=Measure is a quantity with unit
#XTOL: Tooltip for measure semantic type
measureAmountWithCurrency=Measure is an amount with currency
#XTOL: Tooltip for the menu button
actions=Actions
#XTOL: Tooltip for the hidden in param
hiddenInParam=Hidden in parameter dialog of story
#XBUT: Menu button to rename the alias
renameAlias=Rename Alias
#XTIT: Title for rename measure source dialog
renameMeasureSourceDialogTitle=Rename Measure Source Alias
#XFLD: Label for measure source alias input field
measureSourceAlias=Measure Source Alias
#XTIT: Title for rename dimension source dialog
renameDimensionSourceDialogTitle=Rename Dimension Source Alias
#XFLD: Label for dimension source alias input field
dimensionSourceAlias=Dimension Source Alias
#XBUT: Button ok
ok=OK
#XBUT: Button cancel
cancel=Cancel
#XBUT: Button back
back=Back
#XBUT: Button add
add=Add
#XMSG: Source data not available message
sourcesNoData=No analytical dataset / dimension or relational dataset found
#XMSG: Data preview not possible due to inconsistency in modeling (i.e. missing measures)
inconsistentAnalyticModelPreview=The analytic model definition is inconsistent.\nFix the errors in the analytic model.
#XMSG Not suffcient privileges to preview data
txtNoPrivilegesPreviewData=You do not have sufficient privileges to preview this data
#XMSG Not suffcient privileges to view data
txtNoPrivilegesViewData=You aren’t allowed to view this data.
#XMSG: Error message when deploying a query model to display preview data
previewDeploymentError=An error occurred during the preview deployment.
#XMSG: Message to access the help link for more details
previewDeploymentErrorMoreDetails=For more information, see the link.
#XMSG: Error message when ocurred a problem comunicating to precondition’s service
preconditionsLoadingError=An error occurred while validating the deployment status of the referenced objects.
#XMSG: Error message when the preconditions for deployment are not satistfied
missingPreconditionsError=The preview can only be called when all referenced objects are deployed.\nPlease deploy the following objects:
#XBUT: Retry Button
retry=Retry
#XFLD: view Details link
viewDetails=View Details
#XFLD: Label for filter - all types
allTypesFilter=All
#XFLD: Label for filter - relational datasets
relationalDatasetTypeFilter=Relational Dataset
#XFLD: Label for filter - analytical datasets
analyticalDatasetTypeFilter=Analytical Dataset
#XFLD: Label for filter - dimensions
dimensionTypeFilter=Dimension
#XMSG: Validation message for a model with no attributes
validationMessageNoAttributes=No attributes provided
#XMSG: Validation detailed message for a model with no attributes
validationDescriptionNoAttributes=The analytic model has no attributes, add at least one attribute
#XMSG: Validation message for a model with no measures
validationMessageNoMeasures=No measures provided
#XMSG: Validation detailed message for a model with no measures
validationDescriptionNoMeasures=The analytic model has no measures, add at least one measure
#XMSG: Validation message for a model with no sources
validationMessageNoSources=No sources provided
#XMSG: Validation detailed message for a model with no sources
validationDescriptionNoSources=The analytic model has no sources, add at least one
#XMSG: Validation message for a model with multiple measure sources
validationMessageMultipleMeasureSources=Only single measure sources are supported
#XMSG: Validation detailed message for a model with with multiple measure sources
validationDescriptionMultipleMeasureSources=The analytic model has multiple measure sources, remove additional sources
#XMSG: Validation message for a model with no technical name
validationMessageMissingTechnicalName=Technical name missing
#XMSG: Validation detailed message for a model with no technical name
validationDescriptionMissingTechnicalName=The analytic model has no technical name, add the technical name
#XMSG: Validation message for an empty model
validationMessageEmptyModel=Empty Model
#XMSG: Validation detailed message for an empty model
validationDescriptionEmptyModel=The analytic model is empty, add sources, measures and attributes
#XMSG: Validation message for a restricted measure without source measure
validationMessageRestrictedInvalidSourceMeasure=Restricted measure "{0}": Invalid source measure
#XMSG: Validation detailed message for a restricted measure without source measure
validationDescriptionRestrictedInvalidSourceMeasure=Select a valid source measure as restricted measure.
#XMSG: Validation message for a restricted measure with source measure that does not exist
validationMessageRestrictedNonExistentSourceMeasure=Restricted measure "{0}": source measure "{1}" does not exist in analytic model
#XMSG: Validation message for a restricted measure with invalid aggregation type
validationMessageRestrictedInvalidAggregation=Restricted measure "{0}": Invalid aggregation type
#XMSG: Validation detailed message for a restricted measure with invalid aggregation type
validationDescriptionRestrictedInvalidAggregation=Select a valid aggregation type for the restricted measure
#XMSG: Validation message for a calculated measure with invalid formula
validationMessageCalculatedInvalidExpression=Calculated measure "{0}": Invalid expression
#XMSG: Validation detailed message for a calculated measure with invalid formula
validationDescriptionCalculatedInvalidExpression=The expression of the calculated measure is invalid, provide a valid formula
#XMSG: Validation message for a calculated measure with invalid function parameter
validationMessageCalculatedInvalidParameter=Calculated measure "{0}": Invalid parameter type
#XMSG: Validation detailed message for a calculated measure or a calulated cross calculation with invalid function parameter
validationDescriptionCalculatedInvalidParameter=Parameter no. {1} of used function "{2}" has a wrong type. Please use a parameter with correct type.
#XMSG: Validation message for a restricted measure with invalid formula
validationMessageRestrictedInvalidExpression=Restricted measure "{0}": Invalid expression
#XMSG: Validation detailed message for a restricted measure with invalid formula
validationDescriptionRestrictedInvalidExpression=The expression of the restricted measure is invalid, provide a valid formula
#XMSG: Validation message for restricted measure with incorrect constant selection
validationMessageRestrictedConstantSelectionAttributesIncorrect=Restricted measure "{0}": Invalid constant selection
#XMSG: Validation detailed message for restricted measure with constant selection selected, but with no selected dimensions
validationDescriptionRestrictedConstantSelectionAttributesEmpty=Selected dimensions: Select at least one constant dimension
#XMSG: Validation detailed message for restricted measure with not existing dimensions in constant selection or exception aggregation
validationDescriptionRestrictedMeasureSelectionAttributesMissing=Selected dimensions do not exist: "{1}". Change the selection
#XMSG: Validation message for non-cumulative settings with an invalid record type field
validationMessageNonCumulativeRecordTypeInvalid=Non-cumulative settings: Record type field is invalid
#XMSG: Validation detailed message for non-cumulative settings with an invalid record type field
validationDescriptionNonCumulativeRecordTypeInvalid=Please maintain a valid record type in the common properties of a non-cumulative measure.
#XMSG: Validation message for non-cumulative settings with a record type field with incorrect data type
validationMessageNonCumulativeRecordTypeDataType=Non-cumulative settings: Record type field has incorrect data type
#XMSG: Validation detailed message for non-cumulative settings with a record type field with incorrect data type
validationDescriptionNonCumulativeRecordTypeDataType=Please maintain a record type with data type integer in the common properties of a non-cumulative measure.
#XMSG: Validation message for non-cumulative settings with an invalid time dimension
validationMessageNonCumulativeTimeDimensionInvalid=Non-cumulative settings: Time dimension is invalid
#XMSG: Validation detailed message for non-cumulative settings with an invalid time dimension
validationDescriptionNonCumulativeTimeDimensionInvalid=Please maintain a valid time dimension in the common properties of a non-cumulative measure.
#XMSG: Validation message for non-cumulative settings with a time dimension with incorrect data type
validationMessageNonCumulativeTimeDimensionDataType=Non-cumulative settings: Time dimension has incorrect data type
#XMSG: Validation detailed message for non-cumulative settings with a time dimension with incorrect data type
validationDescriptionNonCumulativeTimeDimensionDataType=Please maintain a time dimension with data type date in the common properties of a non-cumulative measure.
#XMSG: Validation message for non-cumulative settings with reporting start time, but without reporting end time
validationMessageNonCumulativeStartTimeWithoutEndTime=Non-cumulative settings: Reporting start time without reporting end time
#XMSG: Validation detailed message for non-cumulative settings with reporting start time, but without reporting end time
validationDescriptionNonCumulativeStartTimeWithoutEndTime=Please maintain the reporting end time in the common properties of a non-cumulative measure.
#XMSG: Validation message for non-cumulative settings with reporting end time, but without reporting start time
validationMessageNonCumulativeEndTimeWithoutStartTime=Non-cumulative settings: Reporting end time without reporting start time
#XMSG: Validation detailed message for non-cumulative settings with reporting end time, but without reporting start time
validationDescriptionNonCumulativeEndTimeWithoutStartTime=Please maintain the reporting start time in the common properties of a non-cumulative measure.
#XMSG: Validation message for non-cumulative settings with a not valid reporting start time
validationMessageNonCumulativeStartTimeInvalid=Non-cumulative settings: Reporting start time invalid
#XMSG: Validation detailed message for non-cumulative settings with a not valid reporting start time
validationDescriptionNonCumulativeStartTimeInvalid=Please maintain a valid reporting start time in the common properties of a non-cumulative measure.
#XMSG: Validation message for non-cumulative settings with a not valid reporting end time
validationMessageNonCumulativeEndTimeInvalid=Non-cumulative settings: Reporting end time invalid
#XMSG: Validation detailed message for non-cumulative settings with a not valid reporting end time
validationDescriptionNonCumulativeEndTimeInvalid=Please maintain a valid reporting end time in the common properties of a non-cumulative measure.
#XMSG: Validation message for non-cumulative settings with a reporting end time earlier than the reporting start time
validationMessageNonCumulativeEndTimeEarlierStartTime=Non-cumulative settings: Reporting end time is earlier than reporting start time
#XMSG: Validation detailed message for non-cumulative settings with a reporting end time earlier than the reporting start time
validationDescriptionNonCumulativeEndTimeEarlierStartTime=Please maintain valid reporting times in the common properties of a non-cumulative measure.
#XMSG: Validation message for a non-cumulative measure without source measure
validationMessageNonCumulativeInvalidSourceMeasure=Non-cumulative measure "{0}": Invalid source measure
#XMSG: Validation detailed message for a non-cumulative measure without source measure
validationDescriptionNonCumulatedInvalidSourceMeasure=Select a valid measure as source for the non-cumulative measure.
#XMSG: Validation detailed message for a non-cumulative measure with source measure that does not exist
validationMessageNonCumulatedNonExistentSourceMeasure=Non-cumulative measure "{0}": source measure "{1}" does not exist in analytic model
#XMSG: Validation message for a non-cumulative measure with invalid exception aggregation type
validationMessageExceptionAggregationNcumNotSupported=Non-cumulative measure "{0}": Invalid exception aggregation type
#XMSG: Validation detailed message for a non-cumulative measure with invalid exception aggregation type
validationDescriptionExceptionAggregationNcumNotSupported=Select a valid exception aggregation type for the non-cumulative measure.
#XMSG: Validation message for a non-cumulative measure without common non-cumulative settings
validationMessageNonCumulativeMissingSettings=Non-cumulative measure "{0}": Missing common non-cumulative properties.
#XMSG: Validation detailed message for a non-cumulative measure without common non-cumulative settings
validationDescriptionNonCumulativeMissingSettings=Maintain common non-cumulative properties for the non-cumulative measure.
#XMSG: Validation message for filter with invalid formula
validationMessageFilterInvalidExpression=Filter "{0}": Invalid expression
#XMSG: Validation detailed message for filter with invalid formula
validationDescriptionFilterInvalidExpression=The expression of the filter is invalid, provide a valid formula.
#XMSG: Validation detailed message for a leveled hierarchy attribute missing
validationMessageLeveledHierarchyAttributeMissing=Leveled Hierarchy Attribute "{0}" of source "{1}" is missing
#XMSG: Validation message for a leveled hierarchy attribute missing
validationDescriptionLeveledHierarchyAttributeMissing=Leveled Hierarchy Attribute missing
#XMSG: Validation message for a Count Distinct measure attribute missing
validationMessageCountDistinctInvalidExpression=Count Distinct measure "{0}": Attribute missing
#XMSG: Validation detailed message for a Count Distinct measure attribute missing
validationDescriptionCountDistinctInvalidExpression=The Count Distinct measure "{0}" has no attributes; assign at least one attribute
#XMSG: Validation message for a Count Distinct measure wrong attribute
validationMessageCountDistinctInvalidAttribute=Count Distinct measure "{0}": Dimension "{1}" does not exist
#XMSG: Validation detailed message for a Count Distinct measure wrong attribute
validationDescriptionCountDistinctInvalidAttribute=The Count Distinct measure "{0}" dimension list contains a non existing dimension "{1}", please adapt the list.


#XMSG: Validation description for restricted measure with constant selection and global filter
validationDescriptionRestrictedConstantSelectionGlobalFilter=Restricted measure "{0}": Potentially unexpected value in result due to usage of constant selection and global filter
#XMSG: Validation message for restricted measure with constant selection and global filter
validationMessageRestrictedConstantSelectionGlobalFilter=Restricted measure "{0}": The dimension(s) "{1}" are used in constant selections and in global filter
#XMSG: Validation message for restricted measure with constant selection ALL and global filter
validationMessageRestrictedConstantSelectionAllGlobalFilter=Restricted measure "{0}": A constant selection for all dimensions is used and there is a global filter


#XMSG: Validation message for hidden parent child hierarchy elements
validationMessageParentChildHierarchyAttributeHidden=Dimension "{0}" can’t be used. Attribute "{1}" used in parent-child hierarchy "{2}" is hidden for story consumption
#XMSG: Validation description for hidden parent child hierarchy elements
validationDescriptionParentChildHierarchyAttributeHidden=Remove the dimension source "{0}" from the analytic model or adjust the dimension. Set property "Show in Story" for attribute "{1}" in dimension "{3}".

#XMSG: Validation message for hidden leveled hierarchy elements
validationMessageLeveledHierarchyAttributeHidden=Dimension "{0}" can’t be used. Attribute "{1}" used in level based hierarchy "{2}" is hidden for story consumption
#XMSG: Validation description for hidden leveled hierarchy elements
validationDescriptionLeveledHierarchyAttributeHidden=Remove the dimension source "{0}" from the analytic model, or adjust the dimension. Set property "Show in Story" for attribute "{1}" in dimension "{3}".

#XMSG: Validation message for hidden text elements of dimension keys
validationMessageTextElementHiddenForKey=Dimension "{0}" can’t be used. Attribute "{1}", used as text field, is hidden for story consumption
#XMSG: Validation detailed message for hidden text elements of dimension keys
validationDescriptionTextElementHiddenForDimKey=Remove the dimension source "{2}" from the analytic model, or adjust the dimension definition. Set property "Show in Story" for attribute "{1}" in dimension "{0}".

#XMSG: Validation message for hidden text elements of dimension attributes
validationMessageTextElementHiddenForAttribute=Attribute "{0}" of dimension "{1}" can’t be used. Label column "{2}" is hidden for story consumption
#XMSG: Validation detailed message for hidden text elements of dimension attributes
validationDescriptionTextElementHiddenForAttribute=Remove attribute "{3}" from the analytic model, or adjust the dimension definition. Set property "Show in Story" for attribute "{2}" in dimension "{1}".

#XMSG: Validation message for hidden key
validationMessageKeyHidden=Dimension "{0}" can’t be used. Key "{1}" is hidden for story consumption
#XMSG: Validation detailed message for hidden key
validationDescriptionKeyHidden=Remove dimension "{2}" from the analytic model, or adjust the dimension definition. Set property "Show in Story" for key "{3}".

#XMSG: Validation message for dependent objects
warningDependentObjectMessage={0} "{1}" has been modified and may impact dependent objects:
#XMSG: Validation message for using dependent model as fact source
errorFactSourceIsDependentModel="{0}" is a dependent analytic model and can’t be used as a fact source.

#XMSG: Validation message for empty level hierarchies
validationMessageLeveledHierarchyNoLevels=Dimension "{0}" can’t be used. Level-based hierarchy "{1}" contains no levels
#XMSG: Validation detailed message for empty level hierarchies
validationDescriptionLeveledHierarchyNoLevels=Remove dimension "{2}" from the analytic model, or adjust the dimension definition.

#XMSG: Validation message for empty level hierarchies
validationMessageParentChildHierarchyEmpty=Dimension "{0}" can’t be used. Parent-child hierarchy "{1}" contains no parent or child attributes
#XMSG: Validation detailed message for empty level hierarchies
validationDescriptionParentChildHierarchyEmpty=Remove dimension "{2}" from the analytic model, or adjust the dimension definition.

#XMSG: Validation message for wrong semantic of text association target of dimension
validationMessageWrongSemanticForTextTargetOfDim=Attribute "{0}" in dimension source "{1}" has conflicting setting for the text resolution
#XMSG: Validation message for wrong semantic of text association target of fact source
validationMessageWrongSemanticForTextTargetOfFact=Attribute "{0}" in fact source "{1}" has conflicting setting for the text resolution
#XMSG: Validation detailed message for for wrong semantic of text association target
validationDescriptionWrongSemanticForTextTargetOfDimOrFact=The target entity "{2}" of association "{3}" does not have semantic type "Text". Please correct the association definition in the "{1}" so that it points to a target entity with semantic type "Text", or change the semantic type of entity "{2}" to "Text".
#XMSG: Validation detailed message for conflicting settings for text resolution
validationDescriptionConflictingTextResolvingSetting=The entity "{1}" has conflicting settings for text resolution. Change the assigned Text/Dimension in dimension so that if refers either to a text field or a text association or an association to a dimension.

#XMSG: Validation message for representative keys without element
validationMessageRepKeyDoesNotExist=Representative key "{0}" of dimension "{1}" does not exist.

#XMSG: Validation message for restrited measure using = null or != null
validationMessageRestrictedMeasureEqualOrNotEqualsNull=Restricted measure "{0}" has no valid expression. Operator "{1}" does not support NULL as right side operand.

#XMSG: Validation descriptions for restrited measures using = null or != null
validationDescriptionRestrictedMeasureEqualsNull=Restricted measure "{0}" has no valid expression. Operator "{1}" does not support NULL as right side operand. IS NULL should be used instead.
validationDescriptionRestrictedMeasureNotEqualsNull=Restricted measure "{0}" has no valid expression. Operator "{1}" does not support NULL as right side operand. IS NOT NULL should be used instead.

#XMSG: Validation description for dimension source technical name exceeding maximum length
validationMessageDimSourceTechnicalNameExceedingMaxLength=The technical name of dimension "{0}" is exceeding the maximum number of characters, which may result in an error.
#XMSG: Warning message number of characters exceeded
warningTechnicalNameMaxCharactersExceeded=Number of characters exceeded
#XMSG: Validation of input field for technical name of model
NameMaxLengthExceeded=The name exceeds the maximum length {0}.

#XMSG: Validation description for representative keys without element
validationDescriptionRepKeyDoesNotExist=The representative key of dimension "{1}" is supposed to be "{0}", however no field with this name exists in the dimension. Please adjust the representative key in the dimension.

#XMSG: Validation message for dimensions with no mapping attribute (new dim. handling)
validationMessageNoDimensionSourcesAttributeFound=Invalid Analytic Model - no mapping attribute found for dimension "{0}"
#XMSG: Validation description for dimensions with no mapping attribute (new dim. handling)
validationDescriptionNoDimensionSourcesAttributeFound=Remove the dimension and add it again to the analytic model.

#XMSG: Validation message for not found namespaces
validationMessageNamespaceNotFound=Invalid analytic model - namespace "{0}" not found
#XMSG: Validation description for not found namespaces
validationDescriptionNamespaceNotFound=The technical name of the analytic model ("{1}") contains namespace "{0}". However this namespace could not be found. Please remove the namespace or adjust it to a valid one.

####Currency Conversion
#XMSG: Validation message for Currency Conversion measure is not valid
VAL_CC_INVALID=Currency Conversion measure "{0}" is not valid
#XMSG: Validation message for Unit Conversion measure is not valid
VAL_UC_INVALID=Unit Conversion measure "{0}" is not valid
#XMSG: Config Currency tables are not found in repository
VAL_CC_CAN_NOT_FIND_CONFIG_TABLES=The currency conversion views do not exist yet. Analytic models with currency conversion measures cannot yet be deployed. Create the currency conversion views from the Data Builder landing page.
#XMSG: Config Currency tables are empty
VAL_CC_CAN_NOT_FIND_CONFIG_VALUES=The currency conversion views do not contain any data. Please provide data first before continuing with the definition of currency conversion measures.
#XMSG: Config Unit tables are not found in repository
VAL_UC_CAN_NOT_FIND_CONFIG_TABLES=The unit conversion views do not exist yet. Analytic models with unit conversion measures cannot yet be deployed. Create the unit conversion views from the Data Builder landing page.
#XMSG: Unit Config tables are empty
VAL_UC_CAN_NOT_FIND_CONFIG_VALUES=The unit conversion views do not contain any data. Please provide data first before continuing with the definition of unit conversion measures.
#XMSG: reference date is empty
VAL_CC_EMPTY_REFERENCE_DATE=Enter a reference date for currency conversion measure "{0}".
#XMSG: source currency is empty if the type is constantValue
VAL_CC_EMPTY_CONVERSION_TYPE=Enter an exchange rate type for currency conversion measure "{0}".
#XMSG: target currency is empty
VAL_CC_EMPTY_TARGET_CURRENCY=The target currency of currency conversion measure "{0}" is empty.
#XMSG: target unit is empty
VAL_UC_EMPTY_TARGET_UNIT=The target unit of unit conversion measure "{0}" is empty.
#XMSG: target unit uses variable with wrong type
VAL_UC_TARGET_UNIT_WRONG_VARIABLE_TYPE=The target unit variable used must have sub type None.
#XMSG: Validation message for a currency conversion measure without source measure
validationMessageCurrencyConversionInvalidSourceMeasure=Currency conversion measure "{0}": Invalid source measure
#XMSG: Validation message for a unit conversion measure without source measure
validationMessageUnitConversionInvalidSourceMeasure=Unit conversion measure "{0}": Invalid source measure
#XMSG: Validation detailed message for a currency conversion measure without source measure
validationDescriptionCurrencyConversionInvalidSourceMeasure=Select a valid source measure for the currency conversion measure
#XMSG: Validation detailed message for a unit conversion measure without source measure
validationDescriptionUnitConversionInvalidSourceMeasure=Select a valid source measure for the unit conversion measure
#XMSG: Validation detailed message for a unit conversion measure without source measure
validationMessageUnitConversiondNoUnitAnnotation=Select a valid source unit for the unit conversion measure
#XMSG: Validation detailed message for a currency conversion measure with source measure that does not exist
validationMessageCurrencyConversionNonExistentSourceMeasure=Currency conversion measure "{0}": source measure "{1}" does not exist in analytic model
#XMSG: Validation detailed message for a unit conversion measure with source measure that does not exist
validationMessageUnitConversionNonExistentSourceMeasure=Unit conversion measure "{0}": source measure "{1}" does not exist in analytic model
#XMSG: Validation message for currency conversion with incorrect Target Type Dimension
validationMessageCurrencyConversionTargetDimensionsIncorrect=Currency conversion measure "{0}": Invalid constant dimension selection
#XMSG: Validation message for unit conversion with incorrect Target Type Dimension
validationMessageUnitConversionTargetDimensionsIncorrect=Unit conversion measure "{0}": Invalid constant dimension selection
#XMSG: Validation detailed message for restricted measure with not existing dimensions in constant selection or exception aggregation
validationDescriptionCurrencyConversionTargetDimensionsMissing=Selected dimensions do not exist: "{1}". Change the selection
#XMSG: Validation detailed message for restricted measure with not existing dimensions in constant selection or exception aggregation
validationDescriptionUnitConversionTargetDimensionMissing=Selected dimension do not exist: "{1}". Change the selection
#XMSG: Validation message for currency conversion with incorrect variables
validationMessageCurrencyConversionVariablesIncorrect=Currency conversion measure "{0}": Invalid variable
#XMSG: Validation message for unit conversion with incorrect variables
validationMessageUnitConversionVariableIncorrect=Unit conversion measure "{0}": Invalid variable
#XMSG: Validation detailed message for currency conversion with missing variables
validationDescriptionCurrencyConversionVariablesMissing=Selected variables do not exist: "{1}". Change the selection
#XMSG: Validation message for measure exception aggregation dimensions missing
validationExceptionAggregationAttributeMissing=Exception aggregation dimensions of measure "{0}" missing
#XMSG: Validation detailed message for exception aggregation dimensions missing
validationDescriptionExceptionAggregationAttributeMissing=Add at least one dimension for the exception aggregation.
#XMSG: Validation detailed message for exception aggregation dimensions missing for X_OF_DIMENSION
validationDescriptionOfDimensionExceptionAggregationAttributeMissing=Add one dimension for the exception aggregation.
#XMSG: Validation message for exception aggregation for type FIRST_OF_DIMENSION, LAST_OF_DIMENSION, AVERAGE_OF_DIMENSION with too many options
validationExceptionAggregationMoreThanOneDimension=Too many dimensions selected.
#XMSG: Validation detailed message for exception aggregation for type FIRST_OF_DIMENSION, LAST_OF_DIMENSION, AVERAGE_OF_DIMENSION with too many options
validationExceptionAggregationMoreThanOneDimensionDescription=Only one dimension is allowed for the exception aggregation type "{0}".
#XMSG: Validation message for exception aggregation for type FIRST_OF_DIMENSION, LAST_OF_DIMENSION, AVERAGE_OF_DIMENSION with wrong data type
validationExceptionAggregationIncorrectType=Selected dimension should be of type Date.
#XMSG: Validation detailed message for exception aggregation for type FIRST_OF_DIMENSION, LAST_OF_DIMENSION, AVERAGE_OF_DIMENSION with wrong data type
validationExceptionAggregationIncorrectTypeDescription=Selected dimension should be of type Date for exception aggregation type "{0}".
#XMSG: Validation message for exception aggregation for type FIRST_OF_DIMENSION, LAST_OF_DIMENSION, AVERAGE_OF_DIMENSION with DWCO_MODELING_AM_UNBOOKED_EXCEPTION_AGGREGATION deactivated
validationExceptionAggregationUnsupportedType=Exception aggregation type "{0}" is not supported.
#XMSG: Validation detailed message for exception aggregation for type FIRST_OF_DIMENSION, LAST_OF_DIMENSION, AVERAGE_OF_DIMENSION with DWCO_MODELING_AM_UNBOOKED_EXCEPTION_AGGREGATION deactivated
validationExceptionAggregationUnsupportedTypeDescription=The selected exception aggregation type "{0}" is not supported for "{1}" measures.
#XMSG: Validation message for exception aggregation for type FIRST_OF_DIMENSION, LAST_OF_DIMENSION, AVERAGE_OF_DIMENSION on Restricted Measures with source measure having an exception aggregation defined
validationExceptionAggregationSourceHasExceptionAggregation=Source measure "{0}" has exception aggregation defined.
#XMSG: Validation detailed message for exception aggregation for type FIRST_OF_DIMENSION, LAST_OF_DIMENSION, AVERAGE_OF_DIMENSION on Restricted Measures with source measure having an exception aggregation defined
validationExceptionAggregationSourceHasExceptionAggregationDescription=The selected exception aggregation type "{1}" does not support source measures with exception aggregation defined.
#XMSG: Validation detailed message for association text missing
validationMessageAssociationTextMissing=Text of associated dimension "{0}" is missing
#XMSG: Validation message for association text missing
validationDescriptionAssociationTextMissing=Text of associated dimension missing
#XMSG: Validation detailed message for derived parameter with lookup entity parameter not defined
validationMessageLookupEntityParameterMissing=Variable "{0}" has no lookup entity defined.
#XMSG: Validation detailed message for derived parameter with lookup entity parameter not defined description
validationDescriptionLookupEntityParameterMissing=The definition for lookup entity is missing in the value help definition.
#XMSG: Validation message for derived parameter with lookup entity defined without column
validationMessageLookupEntityColumnMissing=Variable "{0}" lookup entity column is not selected
#XMSG: Validation detailed message for derived parameter with lookup entity defined without column
validationDescriptionLookupEntityColumnMissing=Select the column for the lookup entity.
#XMSG: Validation detailed message for derived parameter with missing mapping for lookup entity parameter
validationMessageLookupEntityParameterErrorMappingMissing=Lookup entity parameter "{0}" in variable "{1}" has error in value definition.
#XMSG: Validation detailed for derived parameter with missing mapping for lookup entity parameter
validationDescriptionLookupEntityParameterMappingMissing=The input parameter to access the lookup entity has an error. Map or set value for parameters in lookup entity parameter mapping section.
#XMSG: Validation detailed message for input parameter with missing mapping
validationMessageInputParameterMappingMissing=Variable "{0}" is not used
#XMSG: Validation message for input parameter with missing mapping
validationDescriptionInputParameterMappingMissing=Add a usage of the variable or delete the variable.

#XMSG: Validation short message for invalid filter variable technical name
validationMessageInvalidFilterVariableName=Variable name "{0}" with leading digit not allowed.
#XMSG: Validation description message for invalid filter variable technical name
validationDescriptionInvalidFilterVariableName=Technical name with leading digit is not allowed. Start with a character.

#XMSG: Validation short message for story filter variable dimension refering to non existing attribute
validationMessageRefNonExistingDimension=Variable "{0}" refers to non existing dimension
#XMSG: Validation detailed message for story filter variable dimension refering to non existing attribute
validationDescriptionRefNonExistingDimension=Add the dimension or delete the variable.

#XMSG: Validation short message for story filter variable validating case sensitivity check
validateStoryFilterVariableCaseSensitivity=Technical names of variable "{0}" and "{1}" not valid
#XMSG: Validation detailed message for story filter variable validating case sensitivity check
validateDescStoryFilterVariableCaseSensitivity=Variables "{0}" and "{1}" have the same technical name - variables name uniqueness is checked case-insensitive. A filter variable uses the name of the referenced dimension, in this case you can add variables using a technical name with lower case characters. Remove the filter variable, or rename the technical name of the other variable.

#XMSG: Validation short message for derived variable with lookup entity parameter mapping to non existing variable
validationMessageLookupEntityParameterRefNonExistingVariable=Variable "{0}" lookup entity parameter mapping refers to non existing variable
#XMSG: Validation detailed message for derived variable with lookup entity parameter mapping to non existing variable
validationMessageDescriptionLookupEntityParameterRefNonExistingVariable=Variable mapped to lookup entity parameter mapping no longer exists.

#XMSG: Validation short message for dynamic derived variable with lookup entity parameter mapping to a manual input variable
validationMessageDynamicLookupEntityParameterRefConstantVariable=Parameter binding for lookup in Variable "{0}" is invalid.
#XMSG: Validation detailed message for dynamic derived variable with lookup entity parameter mapping to a manual input variable
validationMessageDescriptionDynamicLookupEntityParameterRefConstantVariable=The parameter binding in the lookup for the dynamic default value of variable "{0}" uses manual Input variable "{1}" for parameter "{2}". The derivation of a dynamic default value takes place before the variable dialog is shown. Therefore a variable filled by manual input is not allowed.

#XMSG: Validation short message for derived variable with lookup entity parameter mapping to non existing parameter
validationMessageLookupEntityParameterRefNonExistingParameter=Variable "{0}" lookup entity parameter no longer exists
#XMSG: Validation detailed message for derived variable with lookup entity parameter mapping to non existing parameter
validationMessageDescriptionLookupEntityParameterRefNonExistingParameter=Parameter "{1}" missing in lookup entity "{2}". Please delete the parameter from the variable "{0}" in this analytic model or create the missing parameter in the lookup entity.

#XMSG: Validation detailed message for variable invalid default value
validationMessageVariableDefaultValue=Variable "{0}" does not have a valid default value
#XMSG: Validation detailed description for stacked variable invalid default value
validationDescriptionVariableDefaultValue=Variable "{0}" is used in the expression of measure "{1}". Usage is restricted to comparisons with constant expressions. Please change the filter type or use "Inherit" for this variable in the properties of the source node.
#XMSG: Validation message for missing from or to value in variable
validationMessageVariableFromOrToValueMissing=Variable "{0}" is missing "From" or "To" value
#XMSG: Validation message for missing from or to value in variable
validationDescriptionVariableFromOrToValueMissing=Variable "{0}" is missing "From" or "To" value. Both values need to be set for the interval option.

#XMSG: Validation detailed message for variable invalid default value
validationMessageParameterMappingVariableMissing=Parameter "{0}" of source "{1}" is mapped to non-existent Variable "{2}"
#XMSG: Validation message for invalid defualt value of variable
validationDescriptionParameterMappingVariableMissing=Variable mapped to parameter no longer exists.

#XMSG: Validation detailed message for input parameter with mismatching types
validationMessageInputParameterTypeMismatch=Type mismatch found in mapped parameters for input parameter "{0}"
#XMSG: Validation message for input parameter with mismatching types
validationMessageTypeMismatchInputParam=Variable "{0}": Multiple usages with different data types are not supported
#XMSG: Validation message long text for input parameter with mismatching types
validationDescriptionTypeMismatchInputParam=The variable is used in multiple places, this is only allowed when the data type and value help definition are identical. Adapt the data types in the usages or use different variables.\n\nConflicting Usages: \n\n- {1} "{2}" ("{3}"), Data type: {4}, Value help: "{5}"\n- "{6}" "{7}" ("{8}"), Data type: {9}, Value help: "{10}"
#XMSG: Validation message long text for input parameter with mismatching types in case is used in CC variable
validationDescriptionTypeMismatchCurrencyVariableWithParam= The variable is used in multiple places, this is only allowed when the data type and valuehelp definition are identical. Adapt the datatypes in the usages or use different variables.\n\nConflicting Usages: \n\n- {1} "{2}" ("{3}"), Data type: {4}, Valuehelp: "{5}"
#XMSG: Validation message for source variable with mismatching types
validationMessageSourceVariableTypeMismatch=Variable "{0}": Datatype does not match the data type of its usage
#XMSG: Validation message long text for input parameter with mismatching types in case is used in CC variable
validationDescriptionSourceVariableTypeMismatch= The variable has manual entered data type "{1}", This data type does not match the datatype of the usage. Adapt the datatype of the variable that it matches the usage, or set the data type of the variable to "Inherited".\n\nConflicting Usages: \n\n- {2} "{3}" ("{4}"), Data type: {5}
#XMSG: Validation message
validationMessageTypeMismatchCurrencyVariable = The variable is used in multiple places, this is only allowed when the data type and valuehelp definition are identical. Adapt the datatypes in the usages or use different variables.
#XMSG: Validation message for input parameter with mismatching types
validationMessageTypeUnknownCalculated=Variable with inherited data type is not supported.
#XMSG: Validation message for input parameter with mismatching types
validationDescriptionTypeUnknownCalculatedMeasureVariable=The variable is used in calculated measure "{0}" and has no data type. Please specify data type.
#XMSG: Validation short message for variable with non existing lookup entity
validationMessageRefNonExistingLookupEntity=Variable "{0}" refers to non existing lookup entity
#XMSG: Validation detailed message for variable with non existing lookup entity column
validationDescriptionRefNonExistingLookupEntity=Select valid lookup entity.

#XMSG: Validation short message for variable with lookup entity result column data type matches with variable data type
validationMessageLookupEntityResultColumnType = Variable "{0}" result column data type does not match
#XMSG: Validation detailed message for variable with lookup entity result column data type matches with variable data type
validationDescriptionMessageLookupEntityResultColumnType= The result column  "{1}" of data type {2} from lookup entity "{3}" does not match the data type of its usage.\n\nConflicting Usage:\n- "{4}", Data type: {5}

#XMSG: Validation short message for variable with non existing lookup entity column
validationMessageRefNonExistingEntityColumn=Variable "{0}" refers to non existing lookup entity result column
#XMSG: Validation detailed message for variable with non existing lookup entity column
validationDescriptionRefNonExistingEntityColumn=Result column is hidden or deleted.

#XMSG: Validation short message to remove the variable derivation support for filter and restriction variable
validationMessageVariableDerivationNotsupported=Variable derivation for "{0}" is not supported
#XMSG:Validation short message to say that the filter types Range and Interval are not supported for variable derivation in filter variables
validationMessageRangeIntervalNotSupported=Filter Types Range and Interval are not supported for variable derivation in "{0}"
#XMSG: Validation detailed message to remove the variable derivation support for filter and restricted measure variable
validationDescriptionVariableDerivationNotsupported=Variable derivation support for filter or restricted measure variable is not supported.
#XMSG: Validation detailed message to remove the variable derivation support for filter and restriction variable
validationDescriptionVariableRestrictionDerivationNotsupported=Variable derivation is not supported for filter or restriction variables.

#XMSG: Validation detailed message to say that the filter types Range and Interval are not supported for variable derivation in filter variables
validationDescriptionRangeIntervalNotSupported=Filter types Range and Interval are not supported for "{0}", please choose between Single Value or Multiple Single Values.

#XMSG: Validation detailed message for invalid variable technical name
validationMessageVariableTechnicalName=Variable "{0}" does not support lowercase characters

#XMSG: Validation message for fact source semantic usage
validationMessageFactSourceADS=The semantic usage of fact source "{0}" has to be "Fact"
#XMSG: Validation description for fact source semantic usage
validationDescriptionFactSourceADS=The semantic usage for the source was changed after creation of the analytic model. Change the semantic usage back to "Fact" or remove the fact source from the analytic model.

#XMSG: Validation message for fact source semantic usage
validationMessageDimensionSourceDimension=The semantic usage of source "{0}" has to be "Dimension"
#XMSG: Validation description for fact source semantic usage
validationDescriptionDimensionSourceDimension=The semantic usage for the source was changed after creation of the analytic model. Change the semantic usage back to "Dimension" or remove the source from the analytic model.

#XMSG: Validation message for attribute and measure with the same technical name
validationMessageAttributeMeasureTechnicalName=Measures and attributes must not have the same technical name ({0})
#XMSG: Validation description for fact source semantic usage
validationDescriptionAttributeMeasureTechnicalName=Measures and attributes should not have the same technical name. In this case there is a measure and an attribute with the technical name "{0}". Change either attribute "{1}" or measure "{2}"

#XMSG: Generic validation message for passed backend messages on measure and filter expressions
validationMessageBackendGeneric={0}
#XMSG: Generic validation description for passed backend messages on measure and filter expressions in general validator
validationDescriptionBackendGeneric={1}

#XMSG: Validation message for parts of a measure or filter expression which are not a valid condition
validationMessageNoValidFilterCondition="{0}" isn’t a valid condition. Missing operator, expecting !=, *, +, -, /, <, <=, <>, =, >, >=, ||, BETWEEN, IN, IS, LIKE, NOT.
#XMSG: Validation description for parts of a measure or filter expression which are not a valid condition
validationDescriptionNoValidFilterCondition="{0}" isn’t a valid condition. Missing operator, expecting !=, *, +, -, /, <, <=, <>, =, >, >=, ||, BETWEEN, IN, IS, LIKE, NOT.
#XMSG: Validation message for case expressions in calculated measures with missing WHEN operator after CASE
validationMessageNoValidCaseExpression=The CASE expression is not valid. After CASE, the operator WHEN is missing.
#XMSG: Validation description for case expressions in calculated measures with missing WHEN operator after CASE
validationDescriptionNoValidCaseExpression=The CASE expression is not valid. After CASE the operator WHEN is missing.

#XMSG: Validation message for in expressions in with missing brackets after in operator
validationMessageNoValidInExpression=The IN expression is not valid. After IN ( is missing.
#XMSG: Validation description for in expressions in with missing ( after in)
validationDescriptionNoValidInExpression=The IN expression is not valid. After IN ( is missing).


#XMSG: Validation message for incomplete mapping for data access controls
validationMessageDacMappingMissing=Incomplete mapping in data access control "{0}"
#XMSG: Validation description for incomplete mapping for data access controls
validationDescriptionDacMappingMissing=Data access control "{0}" has unmapped criterion. You must map one dimension to each data access control criterion.

#XMSG: Validation message for attribute used in data access control mapping not existing in the model
validationMessageDacSourceAttributeMissing=Dimension "{0}" used in data access control "{1}" does not exist in the analytic model
#XMSG: Validation description for attribute used in data access control mapping not existing in the model
validationDescriptionDacSourceAttributeMissing=Dimension "{0}" used in data access control "{1}" does not exist in the analytic model. Remove the mapping using the Dimension.

#XMSG: Validation message for criterion used in data access control mapping not existing in the DAC
validationMessageDacTargetCriterionMissing=Criterion "{0}" of data access control "{1}" does not exist in target
#XMSG: Validation description for criterion used in data access control mapping not existing in the DAC
validationDescriptionDacTargetCriterionMissing=Criterion "{0}" of data access control "{1}" does not exist in target. Remove the mapping using the criterion.

#XMSG: Validation message for type mismatch in mapping for data access controls
validationMessageDacTypeMismatch=Data access control "{0}" has type mismatch for mapping with dimension "{1}" and criterion "{3}"
#XMSG: Validation description for type mismatch in mapping for data access controls
validationDescriptionDacTypeMismatch=Data access control "{0}" has type mismatch for mapping with dimension "{1}" with type "{2}" and criterion "{3}" with type "{4}".

#XMSG: Validation message for conflicting boolean and hierachy data access controls
validationMessageDacBooleanHierarchiesNotSupported=Analytic model contains operator and values data access control and hierarchy data access control
#XMSG: Validation description for conflicting boolean and hierachy data access controls
validationDescriptionDacBooleanHierarchiesNotSupported=Analytic model contains operator and values data access control and hierarchy data access control. Both types are not supported.\n\nOperator and values data access controls: {0}\nHierarchy data access controls: {1}

#XMSG: Validation message for conflicting boolean and single values data access controls
validationMessageDacBooleanSingleValuesNotSupported=Analytic model contains operator and values data access control and single values data access control
#XMSG: Validation description for conflicting boolean and single values data access controls
validationDescriptionDacBooleanSingleValuesNotSupported=Analytic model contains operator and values data access control and single values data access control. Both types are not supported.\n\nOperator and values data access controls: {0}\nSingle values data access controls: {1}

#XMSG: Validation message for unsupported mapping of data access control to dimension attribute
validationMessageDacMappingToDimensionAttributeNotSupported=DAC "{0}" has unsupported mapping to dimension attribute.
#XMSG: Validation description for unsupported mapping of data access control to dimension attribute
validationDescriptionDacMappingToDimensionAttributeNotSupported=For analytic models containing standard or reference date variables, mapping a data access control to a dimension attribute is not supported.
#XMSG: Validation description for unsupported mapping of data access control to dimension attribute (fiscal calendar support)
validationDescriptionDacMappingToDimensionAttributeNotSupportedFiscal=For analytic models containing standard, reference date or fiscal variant variables, mapping a data access control to a dimension attribute is not supported.

#XMSG: Validation message for boolean filter and story filter variables not using single value filter type
validationMessageVariableFiltersBooleanAttributeWithoutSingleValueFilter=Variable "{0}" refers to boolean dimension with incorrect filter type
#XMSG: Validation description for boolean filter and story filter variables not using single value filter type
validationDescriptionVariableFiltersBooleanAttributeWithoutSingleValueFilter=Variable "{0}" refers to boolean dimension "{1}" with incorrect filter type. Variable should use the single value filter type.

#XMSG: Validation message for parts of a filter expression which are not a valid condition due to different reference attributes
validationMessageNoValidFilterConditionDifferentReferenceAttributesFilter=Invalid condition: Referenced attributes "{0}" "{1}" must be the same
#XMSG: Validation description for parts of a filter expression which are not a valid condition due to different reference attributes
validationDescriptionNoValidFilterConditionDifferentReferenceAttributesFilter=Invalid condition
#XMSG: Validation message for parts of a restricted measure expression which are not a valid condition due to different reference attributes
validationMessageNoValidFilterConditionDifferentReferenceAttributesRKF=Invalid condition: Referenced attributes "{0}" "{1}" must be the same
#XMSG: Validation description for parts of a restricted measure expression which are not a valid condition due to different reference attributes
validationDescriptionNoValidFilterConditionDifferentReferenceAttributesRKF=Invalid condition
#XMSG: Validation message for true and false as constants are currently not supported in expressions
validationMessageNoValidFilterConditionNotSupportedBooleanConstants=Invalid condition: true and false as Boolean constants are not supported yet. Please use numeric values 1 and 0 as constant values.
#XMSG: Validation description for true and false as constants are currently not supported in expressions
validationDescriptionNoValidFilterConditionNotSupportedBooleanConstants=Invalid condition
#XMSG: Validation message for parts of a filter expression which are not a valid condition due to incompatible types
validationMessageNoValidFilterConditionIncompatibleTypesFilter=Invalid condition in filter
#XMSG: Validation description for parts of a filter expression which are not a valid condition due to incompatible types
validationDescriptionNoValidFilterConditionIncompatibleTypesFilter=Invalid condition: Operand "{0}" with type "{1}" is not compatible to operand "{2}" with type "{3}"
#XMSG: Validation message for parts of a restricted measure expression which are not a valid condition due to incompatible types
validationMessageNoValidFilterConditionIncompatibleTypesRKF=Invalid condition in restricted measure "{4}"
#XMSG: Validation message for parts of a calculated measure expression which are not a valid condition due to incompatible types
invalidCalculatedMeasureFilterConditionTypeMismatch=Invalid condition in calculated measure "{4}"
#XMSG for calc element
validationMessageNoValidOperator=The calculated column "{0}" cannot support comparison operators like "=", "!=", "<>", "<", ">", "<=", ">=", "IS" without CASE expression
#XMSG: Validation description for parts of a restricted measure expression which are not a valid condition due to incompatible types
validationDescriptionNoValidFilterConditionIncompatibleTypesRKF=Operand "{0}" with type "{1}" is not compatible to operand "{2}" with type "{3}"
#XMSG: Validation description for parts of a restricted measure expression which are not a valid condition due to wrong left side operand
validationDescriptionNoValidFilterConditionLeftSideNotDimension=Left side operand "{0}" of "{1}" must be a dimension
#XMSG: Validation description for parts of a restricted measure expression which are not a valid condition due to wrong right side operand
validationDescriptionNoValidFilterConditionRideSideDimension=Right side operand "{0}" of "{1}" must not be a dimension
#XMSG: Validation description for parts of a restricted measure expression which are not a valid condition due to not allowed operators
validationDescriptionNoValidFilterConditionDisallowedOperator=Operator "{0}" not allowed
#XMSG: Validation message for parts of a calculated measure expression which are not a valid condition due to not allowed operators for non numerc types
validationMessageNonNumericTypes=Calculated measure "{1}" uses element with data type "{2}". Calculated measures should have a numeric result type.
#XMSG: Validation message for non-existent fact source
validationMessageFactSourceDoesNotExist=Fact source "{0}" does not exist.
#XMSG: Validation description for non-existent fact source
validationDescriptionFactSourceDoesNotExist=Fact source "{0}" does not exist.
#XMSG: Validation message for non-existent dimension source
validationMessageDimensionSourceDoesNotExist=Dimension source "{0}" does not exist.
#XMSG: Validation description for non-existent dimension source
validationDescriptionDimensionSourceDoesNotExist=Dimension source "{0}" does not exist.

#XMSG: Validation message for dimension sources without key
validationMessageDimensionSourceNoKey=Dimension source "{0}" has no key attribute
#XMSG: Validation description for dimension sources without key
validationDescriptionDimensionSourceNoKey=Dimension source "{0}" has no key attribute
#XMSG: Validation message for mapping element used as attribute
validationMessageAssocTargetIsAttribute=Mapping element "{0}" used as attribute
#XMSG: Validation description for mapping element used as attribute
validationDescriptionAssocTargetIsAttribute=Mapping element "{0}" of dimension "{1}" is also used as attribute. Remove the attribute from the model.
#XMSG: Validation message for compoundings mapping on time dependency
validationMessageAssocSourceFieldNotSelected=The mapping element "{0}" of the entity "{1}" isn’t marked as attribute.
#XMSG: Validation description for compoundings mapping on time dependency
validationDescriptionAssocSourceFieldNotSelected=The mapping element "{0}" of the entity "{1}" isn’t marked as attribute.
#XMSG: Validation message for non-existent fact source attribute
validationMessageAttributeMissingInFactSource=Attribute "{0}" does not exist in fact source "{1}"
#XMSG: Validation description for non-existent fact source attribute
validationDescriptionAttributeMissingInFactSource=Attribute "{0}" does not exist in fact source "{1}".
#XMSG: Validation message for non-existent dimension source attribute
validationMessageAttributeMissingInDimensionSource=Attribute "{0}" does not exist in dimension source "{1}".
#XMSG: Validation description for non-existent dimension source attribute
validationDescriptionAttributeMissingInDimensionSource=Attribute "{0}" does not exist in dimension source "{1}".
#XMSG: Validation message for hidden fact source attribute
validationMessageAttributeHiddenInFactSource=Attribute "{0}" is hidden for analytics consumption in fact source "{1}"
#XMSG: Validation description for hidden fact source attribute
validationDescriptionAttributeHiddenInFactSource=Remove dimension "{0}" from the analytic model, or correct the referenced fact source "{1}". Select the property "Show in Story" for attribute "{2}" in "{3}".
#XMSG: Validation message for hidden dimension attribute
validationMessageAttributeHiddenInDimensionSource=Attribute "{0}" is hidden for analytics consumption in dimension source "{1}"
#XMSG: Validation description for hidden dimension attribute
validationDescriptionAttributeHiddenInDimensionSource=Remove dimension "{0}"  from the analytic model, or correct the referenced dimension source "{1}". Tick the checkbox for property "Show in Story" for attribute "{2}" in "{3}".
#XMSG: Validation message for hidden association key
validationMessageKeyHiddenInDimensionSource=Attribute "{0}" used in association "{1}" to dimension source "{2}" is hidden
#XMSG: Validation description for hidden association key
validationDescriptionKeyHiddenInDimensionSource=Remove Dimension "{2}" from the analytic model, or correct the referenced source "{3}". Tick the checkbox for property "Show in Story" for attribute "{0}" in "{3}".
#XMSG: Validation message for unsupported fact source attribute
validationMessageAttributeNotSupportedInFactSource=Attribute type from attribute "{0}" in fact source "{1}" is not supported
#XMSG: Validation description for unsupported fact source attribute
validationDescriptionAttributeNotSupportedInFactSource=Attribute type from attribute "{0}" in fact source "{1}" is not supported. Remove the attribute from the model.
#XMSG: Validation message for unsupported dimension attribute
validationMessageAttributeNotSupportedInDimensionSource=Attribute type from attribute "{0}" in dimension source "{1}" is not supported
#XMSG: Validation description for unsupported dimension attribute
validationDescriptionAttributeNotSupportedInDimensionSource=Attribute type from attribute "{0}" in dimension source "{1}" is not supported. Remove the attribute from the model.
#XMSG: Validation message for non-existent fact source measure
validationMessageMeasureMissingInFactSource=Measure "{0}" does not exist in fact source "{1}".
#XMSG: Validation description for non-existent fact source measure
validationDescriptionMeasureMissingInFactSource=Measure "{0}" does not exist in fact source "{1}".
#XMSG: Validation message for auxiliary fact source measure
validationMessageMeasureIsAuxiliaryInFactSource=Measure "{0}" is auxiliary in fact source "{1}".
#XMSG: Validation description for auxiliary fact source measure
validationDescriptionMeasureIsAuxiliaryFactSource=Measure "{0}" is auxiliary in fact source "{1}".
#XMSG: Validation message for non-existent AM measure
validationMessageMeasureMissingInAnalyticModel=Measure "{0}" does not exist in analytic model.
#XMSG: Validation description for non-existent AM measure
validationDescriptionMeasureMissingInAnalyticModel=Measure "{0}" does not exist in analytic model. It is referenced in restricted measure "{1}".
#XMSG: Validation message for for non-supported exception aggregation on restricted measure
validationMessageExceptionAggregationNotSupported=Restricted measure "{0}" contains unsupported exception aggregation
#XMSG: Validation description for non-supported exception aggregation on restricted measure
validationDescriptionExceptionAggregationNotSupported=Exception aggregation at restricted measures is only allowed when the source measure is a fact source measure. Please delete the restricted measure and recreate the measure.
#XMSG: Validation message for non-existent AM attribute
validationMessageAttributeMissingInAnalyticModel=Attribute "{0}" does not exist in analytic model.
#XMSG: Validation description for non-existent AM attribute
validationDescriptionAttributeMissingInAnalyticModel=Attribute "{0}" does not exist in analytic model. It is referenced in restricted measure "{1}".
#XMSG: Validation message for non-existent AM filter variable
validationMessageFilterVariableMissingInAnalyticModel=Filter variable "{0}" does not exist in analytic model.
#XMSG: Validation description for non-existent AM filter variable
validationDescriptionFilterVariableMissingInAnalyticModel=Filter variable "{0}" does not exist in analytic model. It is referenced in restricted measure "{1}".
#XMSG: Validation message for non-existent associations
validationMessageAssociationDoesNotExist=Associated dimension "{2}" does not exist.
#XMSG: Validation description for non-existent associations
validationDescriptionAssociationDoesNotExist=Association "{0}" does not exist in source "{1}". Please remove dimension "{2}" from the analytic model and add it again if necessary.
#XMSG: Validation message for compoundings without representative key
validationMessageCompoundDimNoRepresentativeKey=Dimension source "{0}" has no representative key
#XMSG: Validation description for compoundings without representative key
validationDescriptionCompoundDimNoRepresentativeKey=Dimension source "{0}" has no representative key.
#XMSG: Validation message for compoundings without representative key
validationMessageCompoundDimNoCompoundKeySequence=Dimension source "{0}" has no compound key sequence
#XMSG: Validation description for compoundings without representative key
validationDescriptionCompoundDimNoCompoundKeySequence=Dimension source "{0}" has no compound key sequence.
#XMSG: Validation message for compoundings with non-key mappings
validationMessageCompoundAssocFieldNotKey=The mapping element "{0}" of the entity "{1}" isn’t marked as key.
#XMSG: Validation description for compoundings with non-key mappings
validationDescriptionCompoundAssocFieldNotKey=The dimension "{1}" has keys, but the mapping element "{0}" isn’t marked as key. Mark it as key or change the association.
#XMSG: Validation message for compoundings with a key missing in the join
validationMessageCompoundKeyMissing=Key "{0}" of dimension "{1}" not part of association mapping
#XMSG: Validation description for compoundings with a key missing in the join
validationDescriptionCompoundKeyMissing=Key "{0}" of dimension "{1}" not part of association mapping from source "{2}".
#XMSG: Validation message for compoundings mapping on time dependency
validationMessageCompoundKeyTimeDependency=Key "{0}" of dimension "{1}" is marked as time dependent.
#XMSG: Validation description for compoundings mapping on time dependency
validationDescriptionCompoundKeyTimeDependency=Key "{0}" of dimension "{1}" is marked as time dependent.
#XMSG: Validation message for association targets mismatching the dataEntity key
validationMessageAssociationWrongTarget=Association "{0}" of entity "{1}" points to "{2}", but "{3}" was expected
#XMSG: Validation description for association targets mismatching the dataEntity key
validationDescriptionAssociationWrongTarget=This might result from changing associations in entity "{1}". You can either undo the changes in "{1}" or remove dimension "{4}" from the model.

#XMSG: Validation message for cyclic dependencies in variables
validationMessageParameterCyclicDependency=Parameters "{0}" and "{1}" have a cyclic dependency
#XMSG: Validation description for cyclic dependencies in variables
validationDescriptionParameterCyclicDependency=Remove the cyclic dependency between the parameters.

#XMSG: Validation message for changing a foreign key association to a non foreign key association
validationMessageForeignKeyAssociationToNonForeignKeyAssociation=Associated dimension "{2}" was added as a foreign key association in the analytic model
#XMSG: Validation description for changing a foreign key association to a non foreign key association
validationDescriptionForeignKeyAssociationToNonForeignKeyAssociation=Association "{0}" is an unlinked association in source "{1}". Please remove dimension "{2}" from the analytic model and add it again.
#XMSG: Validation message for changing a non foreign key association to a foreign key association
validationMessageNonForeignKeyAssociationToForeignKeyAssociation=Associated dimension "{2}" was added as an unlinked association in the analytic model
#XMSG: Validation description for changing a non foreign key association to a foreign key association
validationDescriptionNonForeignKeyAssociationToForeignKeyAssociation=Association "{0}" is a foreign key association in source "{1}". Please remove dimension "{2}" from the analytic model and add it again.

#XMSG: Validation message for multiple associations per attribute (produdct / product 2 scenario)
validationMessageMultipleAssociationsPerAttribute=Multiple associations for field "{0}" in entity "{1}" will not be available in a future version of Analytic Models
#XMSG: Validation description for multiple associations per attribute (produdct / product 2 scenario)
validationDescriptionMultipleAssociationsPerAttribute=Multiple associations per attribute will not be supported in a future update. To have both associations each would need their own mapped attribute.

#XMSG: Validation message for multiple associations per attribute (produdct / product 2 scenario)
validationMessageUnlinkedAssociation=Unlinked association "{0}" from "{1}" to dimension "{2}" will not be available in a future version of the analytic model
#XMSG: Validation description for multiple associations per attribute (produdct / product 2 scenario)
validationDescriptionUnlinkedAssociation=Unlinked associations will not be supported in a future update. Each association needs to be selected as the "Text / Association" for an attribute.

#XMSG: Validation message for Design Time Error Fact Sources (Stacking)
validationMessageDesignTimeErrorFactSource=Fact source "{0}" has a design time error or runtime error. Fix the error first.
#XMSG: Validation description for Design Time Error Fact Sources (Stacking)
validationDescriptionDesignTimeErrorFactSource=The analytic model used as fact source has a design time error or runtime error. To be able to stack another analytic model on top, fix the error in fact source "{0}" first, then reload this browser window.

#XMSG: Validation message for hidden fact source attribute
validationMessageMeasureHiddenInFactSource=Measure "{0}" is hidden in fact source "{1}"
#XMSG: Validation description for hidden fact source attribute
validationDescriptionMeasureHiddenInFactSource=Remove measure "{0}" from the analytic model or correct the referenced fact source "{1}". Select the property "Show in Story" for measure "{2}" in "{3}".

#XMSG: Validation message for RKF with exc. aggregation on base with AVG
validationMessageRestrictedExceptionOnBaseWithAVG=Restricted measure "{0}" with exception aggregation on base measure "{1}" with aggregation type "AVG" is not supported
#XMSG: Validation description for RKF with exc. aggregation on base with AVG. remove rkf, if applicable replace with calculated measure or change aggregation type of base measure
validationDescriptionRestrictedExceptionOnBaseWithAVG=Please remove the restricted measure or replace it with a calculated measure, if applicable. Alternativly change the aggregation type of the base measure.

#XMSG: Create Analytic Model text
createAnalyticModel=Create Analytic Model
#XMSG: Model validation success message
validationMessageValidModel=Your model definition looks good.
#XTOL: Tooltip for open lookup entity button
openLookupEntity=Open lookup entity
#XTOL: Tooltip for text icon indicator on dimensions
dimensionTextIndependent=Dimension has language independent text
#XTOL: Tooltip for text icon indicator on dimensions
dimensionTextDependent=Dimension has language dependent text
#XTOL: Tooltip for hierarchy icon indicator on dimensions
dimensionHasHierarchy=Dimension has hierarchies
#XTOL: Tooltip for text icon indicator on attributes
attributeTextIndependent=Attribute has language independent text
#XTOL: Tooltip for text icon indicator on attributes
attributeTextDependent=Attribute has language dependent text
#XTIT: Title for Fact Sources group on output pannel
outputFactSourceGroup=Fact Source Dimensions
#XTIT: Title for Fact Sources group node on the model diagram
diagramFactSourceNode=Fact Sources
#XMSG: Error message when retrieving data entity details
dataEntityDetailsError=An error occurred while loading details from the model.
#XMSG: Error message when retrieving data entity details from selected object
dataEntityDetailsSelectedObjectError=An error ocurred while loading details from selected object.
#XTIT: Title Dimension Source or Fact Source property Panel
nodePropertyTitle={0} Properties
#XTIT: Title for analytic model property Panel
analyticModelPropertiesTitle=Analytic Model Properties
#XMSG: Empty model title message
outputEmptyModelTitle=It looks like you haven’t added any data yet.
#XTIT: Title for From in Association Details
from=From
#XTIT: Title for To in Association Details
to=To
#XTIT: Title for Panel Mappings in Association Details
mappings=Mappings
#XTIT: Segment in segmented button of Panel Mappings in Association Details
all=All
#XTIT: Segment in segmented button of Panel Mappings in Association Details
mapped=Mapped
#XTIT: Segment in segmented button of Panel Mappings in Association Details
unmapped=Unmapped
#XTOL: tooltip for open button on impact and lineage context pad
tooltipOpen=Open In New Tab
#XTOL: tooltip for open button on impact and lineage context pad
tooltipOpenSameTab=Open In Analytic Model
#XMSG: Error message for when there is an error fetching CSN definition
@getCsnFail=Couldn’t get CSN definition.
#XMSG
welcomeText=Drag and drop data from the left panel over to this canvas.
#XMSG
txtPropertyPanelWelcomeText=Drag and drop your data from the panel on the left over to this canvas.
#XMSG
welcomeTextNoData=Looks like you haven’t added any data yet.
#XLBL Label for source in attribute details page
attributeSource=Source
#XLBL Label for source field in attribute details page
attributeSourceField=Source Field
#XLBL Label for key
key=Key
#XLBL Label for hierarchies
hierarchies=Hierarchies
#XLBL Label for text associations
textAssociations=Text Associations
#XMSG: Error message attribute business name empty
businessNameEmpty=The business name of the dimension is empty.
#XMSG: Error message attribute technical name empty
attributeTechnicalNameEmpty=The technical name of the dimension is empty.
#XMSG: Error message analytic measure technical name not unique
attributeTechnicalNameNotUnique=The technical name of the dimension is not unique.
#XMSG: Error message when the model could not be saved properly
saveError=Analytic model could not be saved
#XMSG: Error code generic internal server error
internalServerError=Internal server error
#XTOL: Tooltip for disabled attribute due to being used by association
attributeDisabledViaAssociation=This attribute is already exposed via associated dimension
#XMSG: message for empty associated dimension list
associatedDimensionNoData=No associated dimension found.
#XMSG: message for empty used in list
usedInAttributesNoData=Dimension is not being used.
#XMSG
technicalErrorDetails=The following information may assist either you or an SAP engineer to solve the issue: \n\nCorrelation ID: {0}\nHTTP Status: {3}\nError Code: {2}\nTechnical Message: {1}
#XMSG: Validation for input parameter mapping when has no value or mapped- message title
validationMessageNoMappedParameter=Input parameters are not mapped and have no value assigned ({0})
#XMSG: Validation detailed message for input parameter mapping when has no value or mapped
validationDescriptionNoMappedParameter=Map or set value for the input parameter.
#XMSG: Validation for reference date variable when its exist in stacked analytic model
validationMessageReferenceDateVariable=Reference date variable ({0}) already exists in the underlying analytic model ({1})
#XMSG: Validation for fileter variable when its exist in stacked analytic model
validationMessageFilterVariable=Filter variable ({0}) already exists in the underlying analytic model ({1})
#XMSG: Validation for reference date variable when its exist in stacked analytic model (description)
validationDescriptionReferenceDateVariable=It is not allowed to create a reference date variable in an analytic model based on another analytic model, if a reference date variable already exists in the base analytic model. Please delete the new reference date variable.
#XMSG: Validation for reference date variable when its exist in stacked analytic model (description)
validationDescriptionFilterVariable=It is not allowed to create a filter variable in an analytic model based on another analytic model, if a filter variable already exists in the base analytic model. Please delete the new filter variable.
#XMSG: Validation for input parameter mapping when has no value or mapped- message title
validationMessageADSMissingInputParameter=Input parameter ({0}) is missing in the underlying fact source ({1})
#XMSG: Validation detailed message for input parameter mapping when has no value or mapped
validationDescriptionADSMissingInputParameter=Input parameter ({0}) missing in the underlying fact source ({1}). Please delete the parameter from the source in this analytic model or create the missing parameter in the fact source.

variableSubType=Sub Type

standardVariableType=None
referenceDateVariableType=Reference Date
fiscalVariantVariableType=Fiscal Variant

#XMSG: Validation message for duplicated variable names
validationMessageStackedVariableNameNotUnique=The technical name of the variable "{0}" is not unique
#XMSG: Validation description for duplicated variable names
validationDescriptionStackedVariableNameNotUnique=Technical names of variables need to be unique throughout the analytic model stack. Please rename the variable.
#XMSG: Validation message for duplicated variable names for variables that are not inherited
validationMessageStackedVariableNameConflict=The technical name of the variable "{0}" may cause name conflicts
#XMSG: Validation description for duplicated variable names for variables that are not inherited
validationDescriptionStackedVariableNameConflict=Technical names of variables need to be unique throughout the analytic model stack. The technical name of the variable "{0}" already exists in the analytic model stack, but it's not inherited currently. Please rename the variable to avoid name conflicts in the future.

#XMSG: Validation message for no fiscal variant variable
validationMessageNoFiscalVariable=There is no fiscal variant variable in the analytic model
#XMSG: Validation description for no fiscal variant variable
validationDescriptionNoFiscalVariable=Analytic model has fiscal time dimension "{0}" but no fiscal variant variable. Please add a fiscal variant variable.

#XMSG: Validation message for reference date variable usage
validationMessageReferenceDateVariableUsed=Reference date variable "{0}" is used in other measures or variables.
#XMSG: Validation description for reference date variable usage
validationDescriptionReferenceDateVariableUsed=Change semantic type of variable "{0}" to standard or remove usages.

#XMSG: Validation message for more than one fiscal variant variable
validationMessageMoreThanOneFiscalVariable=There is more than one fiscal variable in the analytic model
#XMSG: Validation description for more than one fiscal variant variable
validationDescriptionMoreThanOneFiscalVariable=There is more than one fiscal variable in the analytic model.

#XMSG: Validation message for fiscal variant variable without default value
validationMessageFiscalVariantVariableNoDefaultValue=Fiscal variant variable has no default value
#XMSG: Validation description for fiscal variant variable without default value
validationDescriptionFiscalVariantVariableNoDefaultValue=Fiscal variant variable has no default value. Please set a default value.

#XMSG: Validation message for fiscal variant variable with dynamic default value
validationMessageFiscalVariantVariableDynamicDefault=Fiscal variant variable has dynamic default value
#XMSG: Validation description for fiscal variant variable with dynamic default value
validationDescriptionFiscalVariantVariableDynamicDefault=Dynamic default value option is not allowed for fiscal variant variable.

#XMSG: Validation message for fiscal variant variable usage
validationMessageFiscalVariantVariableUsed=Fiscal variant variable "{0}" is used in other measures or variables.
#XMSG: Validation description for fiscal variant variable usage
validationDescriptionFiscalVariantVariableUsed=Change semantic type of variable "{0}" to standard or remove usages.

#XMSG: Validation message for fiscal variant variable with invalid data type
validationMessageFiscalVariantVariableDatatype=Fiscal variant variable "{0}" has invalid data type
#XMSG: Validation description for fiscal variant variable with invalid data type
validationDescriptionFiscalVariantVariableDatatype=Analytic model has more than one fiscal time dimension and fiscal variant types are not compatible.

#XMSG: Validation message for existing fiscal variant variable in stacked model
validationMessageFiscalVariantVariable=Fiscal Variant variable ({0}) already exists in the underlying analytic model ({1})
#XMSG: Validation description for existing fiscal variant variable in stacked model
validationDescriptionFiscalVariantVariable=It is not allowed to create a fiscal variant variable in an analytic model based on another analytic model, if a fiscal variant variable already exists in the base analytic model. Please delete the new fiscal variant variable.

#XMSG: Validation message for stacked calculated measure use dimension which is not part of the stack
validationMessageStackCalculatedMeasureUseDimension= The calculated measure "{0}" uses dimension "{1}" which is not part of the analytic model stack.
#XMSG: Validation description for stacked calculated measure use dimension which is not part of the stack
validationDescriptionStackCalculatedMeasureUseDimension= Calculated measure "{0}" refers to dimension "{1}". Dimension "{1}" is not is not part of current model or used as exception aggregation dimension. Formulas are calculated after aggregation. When the dimension is not in the drill down, the dimension value will be null. This may lead to unexpected formula results. You can enable the formula calculation by adding dimension "{1}" as exception aggregation dimension or include it in the model.
#XMSG: Message displayed as description for parameter mapping with missing ADS input parameter
parameterMappingMissingADSInputParameter=Parameter missing in fact source.
#XTIT Deploy Dialog with errors
dialogTitleValidationMessages=Validation Messages
#XMSG:
variableIsUsed=The selected variables: ({0}) are mapped to parameter and cannot be deleted, Please unmap them first.
#XMSG:
variableUsedInMeasure=The selected variable: ({0}) is used in measure ({1}) and cannot be deleted. To delete it, please make sure that this variable is not used.
#XMSG: Validation for cycles in measures long text (description)
validationMessageCycleInMeasureDescription=Measure ({0}) uses itself via this path: ({1}). Remove the cycle.
#XMSG: Validation for cycles in measures
validationMessageCycleInMeasure=Cycle in definition of measure ({0}) is not supported.
#XMSG: Error message displayed when user is trying to access the AM with the feature flag disabled
analyticModelFFDisabled=The Analytic Model Feature is disabled.
#XMSG: Error message displayed when the model accessed is of an older version and it’s not supported
olderModelVersion=There was an error loading the analytic model. If the error persits, please open a support ticket.
#XMSG: Warning message cannot create Analytic model via Link without saving
saveChangesFirst=Please save your changes first, then try again.
#XMSG: Warning message that a story filter will be ignore because there is a name clash with a parameter
storyFilterIgnoredSameParameterName=The story filter for attribute {0} will be ignored because a parameter with the same case insensitive name exists
#XMSG: Warning message that an attribute is added because it has a story filter attached
attributeAddedDueToAttachedStoryFilter=The attribute {0} was added to the analytic model because it has a story filter attached.
#XTIT: Title for warning msg box
warning=Warning
#XTIT: Title Exception Aggregation property Panel
exceptionAggregationPropertyTitle=Exception Aggregation
#XTIT: Count Distinct Attributes
countDistinctAttributesTitle=Attributes
#XTIT: Title setting property Panel
settingPropertyTitle=Settings
#XTIT: Title expression property Panel
expressionPropertyTitle=Expression
#XLBL Label for dimension alias in dimension informatiom popover
dimensionAlias=Dimension
#XLBL Label for dimension alias in dac information popover
criterion=Criterion
#XLBL Label for attribute alias in attribute information popover
attributeAlias=Attribute
#XLBL Label for associated dimension alias in association information popover
associationAlias=Associated Dimension
#XLBL Label for association source dimension alias in dimension informatiom popover
associationSourceAlias=Associated From
#XLBL Label for association source fields in dimension informatiom popover
foreignSourceFields=Association Source Fields (Foreign Keys)
#XLBL Label for Attribute Source Fields in dimension informatiom popover
targetSourceFields=Attribute Source Fields (Target Keys)
#XTOL A tooltip showing the business name and the technical name of an object. {0} holds the business name. {1} holds the technical name. \n stands for line break. * used to specify what is the current display name mode setting
dimensionNameTooltip=Business Name: {0}\nTechnical Name: {1}
#XLBL Label for Attribute Source Technical Name in attribute informatiom popover
sourceTechnicalName=Technical Name of Source
#XLBL Label for Attribute Source Business Name in attribute informatiom popover
sourceBusinessName=Business Name of Source
#XLBL Label for Attribute Source Technical Name in attribute informatiom popover
outputTechnicalName=Technical Name of Output
#XLBL Label for Attribute Source Business Name in attribute informatiom popover
outputBusinessName=Business Name of Output
#XLBL Label for Keys in node property panel
keys=Keys
#XLBL Label for used in
usedIn=Used In
#XLBL Label for text element
textElement=Text Element
#XLBL Label for text association
textAssociation=Text Association
#XMSG: Error message when deploying a query model
deploymentTriggerError=An error occurred while triggering the deployment of the analytic model.
#XLBL Label for attributes that were renamed (Attribute as RenamedAttribute)
renamedAttributeAs={0} <strong>as</strong> {1}
#XLBL Label for dimensions that were renamed (Dimension as RenamedDimension)
renamedDimensionAs={0} as {1}
#XFLD boolean true value
yes=Yes
#XFLD boolean false value
no=No
#XSEL
exception_aggregation_type_sum_description=Sum
#XSEL
exception_aggregation_type_min_description=Minimum
#XSEL
exception_aggregation_type_none_description=No Exception Aggregation
#XSEL
exception_aggregation_type_max_description=Maximum
#XSEL
exception_aggregation_type_count_description=Count
#XSEL
exception_aggregation_type_countnull_description=Count Excluding Null
#XSEL
exception_aggregation_type_countnullzero_description=Count Excluding Null And Zero
#XSEL
exception_aggregation_type_avg_description=Average
#XSEL
exception_aggregation_type_averagenull_description=Average Excluding Null
#XSEL
exception_aggregation_type_averagenullzero_description=Average Excluding Null and Zero
#XSEL
exception_aggregation_type_std_description=Standard Deviation
#XSEL
exception_aggregation_type_first_description=First
#XSEL
exception_aggregation_type_last_description=Last
#XSEL
exception_aggregation_type_first_of_dimension_description=First Including Null
#XSEL
exception_aggregation_type_last_of_dimension_description=Last Including Null
#XSEL
exception_aggregation_type_average_of_dimension_description=Average Including Null

exception_aggregation_type_error=Error on Selection
#CC: Currency Conversion
#XFLD: Label for dropdown to select Target currency type
CC_txtTargetType=Target Type
#XTIT: Title Currency Conversion Properties Panel
CC_currencyPropertiesTitle=Currency Properties
#XTIT: Title Advanced Currency Conversion Properties Panel
CC_advancedCurrencyTitle=Advanced
#XFLD: source Currency Conversion Property
CC_txtSourceCurrency=Source Currency
#XFLD: target Currency Conversion Property
CC_txtTargetCurrency=Target Currency
#XFLD: reference date Currency Conversion Property
CC_txtReferenceDate=Reference Date
#XTIT: title of target Currency Conversion dialog
CC_targetCurrencyDialogTitle=Select Target Currency
#XSEL: Error Handling select box
CC_failOnError=Fail on Error
#XSEL: Error Handling select box
CC_setToNull=Set to Null
#XSEL: Error Handling select box
CC_keepUnconverted=Keep Unconverted
#XTIT: cliend Id property
CC_txtClient=Client
#XTIT: conversion type property
CC_txtExchangeRateType=Exchange Rate Type
#XTIT: Error Handling text
CC_txtErrorHandling=Error
#XTIT: title of Exchange Rate Type dialog
CC_conversionTypeDialogTitle=Select Exchange Rate Type
#XTIT: title of Currency Conversion Measure Target Currency
CC_currencyConversionMeasureTargetCurrency=Currency Conversion Measure Target Currency
#XTIT: title of Currency Conversion Measure Reference Date
CC_currencyConversionMeasureReferenceDate=Currency Conversion Measure Reference Date
#XTIT: title of Currency Conversion Measure Conversion Type
CC_currencyConversionMeasureConversionType=Currency Conversion Measure Conversion Type
#XTEXT: text for no object found in target,source,date dialog
noObjectsFound=No Objects Found
#XSEL: text of currencyTypes
ccTypesFixedValuesWithNum=Fixed ({0})
#XSEL: label of currencyTypes Fixed Values
ccTypesFixedValues=Fixed Values
#XSEL: label of Dimensions  in target,source,date dialog
ccTypesDimensions=Dimensions
#XSEL: Entry for reference date to always select the actual date
ccTypesCurrentDate=Current Date
#XTIT: Currency dialog Select button
ccSelectButton=Select
#XSEL: label of currencyTypes Dimensions
ccTypesDimensionsWithNum=Dimension ({0})
#XSEL: label of currencyTypes Variables
ccTypesVariablesWithNum=Variable ({0})
#XFLD: label of selectDate in Reference date dialog
CC_txtSelectedDate=Selected Date:
#XFLD:
CC_txtNoDateSelected=No Date Selected
#XBUT: text of select today button in Reference date dialog
CC_txtSelectToday=Select Today
#XTIT: title of  Reference date dialog
CC_referenceDateDialogTitle=Select Reference Date
#XSEL: text for referenceDateTypes
ccTypesFixedDate=Fixed Date
#XMSG: place holder of SearchField
CC_searchEntities=Search
#XTIT: label of Currency Name
CC_txtCurrencyName=Currency Name
#XMSG
CC_txtCurrentDateDialog=The reference date will be calculated with the current date
#XMSG: Error message target currency empty
CC_targetCurrencyEmpty=Target currency must be selected.
#XMSG: Error message exchange rate type empty
CC_conversionTypeEmpty=Exchange rate type must be selected.
#XMSG: Error message reference date empty
CC_referenceDateEmpty=Reference date must be selected.
#XMSG: Error message client empty
CC_clientEmpty=Client must be selected.
#XMSG: Message heading for remote table preview
mdm-previewWarningTitle=Delayed Data Previewing
#XMSG: Message Description for remote table preview
mdm-previewWarningDesc=Previewing data might take a while and impact the remote system’s performance. This is due to the view not being persisted and/or remote data not being replicated. Persisting and replicating objects can solve data previewing delays. Do you want to preview data anyway?
#XBUT: Button Load data preview
mdm-loadData=Preview Data
#XBUT: Button to close the data preview section
toolbarCloseButton=Close
#XTOL: add filter tooltip
addFilterTooltip=Add Filter
#XTOL: delete filter tooltip
deleteFilterTooltip=Delete Filter
#XTOL: delete parameter tooltip
deleteParameterMappingTooltip=Delete Parameter
#XTOL: delete dimensions tooltip
deleteDimensionTooltip=Delete Dimensions
#XTOL: delete variables tooltip
deleteVariablesTooltip=Delete Variables
#XTOL: delete measures tooltip
deleteMeasuresTooltip=Delete Measures
#XTOL: delete data access controls tooltip
deleteDataAccessControlTooltip=Delete Data Access Controls
#XBUT: delete dimensions tooltip when a reference attribute is selected
deleteDimensionTooltipWithReferenceAttributesSelected=Selected reference attributes cannot be deleted
#XBUT: delete dimensions tooltip when a base attibute selected
deleteDimensionTooltipWithBaseAttributesSelected=Base attributes cannot be deleted
#XRBL: radio button to create new source variable
CC_newSourceVariable=Create new Source Variable
#XRBL: radio button to use existing source variable
CC_existingSourceVariable=Use existing Source Variable
#XFLD:Default variable name for target currency
defaultNameTargetCurrencyVariableInput=Target Currency {0}
#XFLD:Default variable name for reference date
defaultNameReferenceDateVariableInput=Reference Date {0}
#XFLD:Default variable name for conversion type
defaultNameConversionTypeVariableInput=Exchange Rate Type {0}
#XTEXT: pattern for default value display with interval (from: 1 to: 100)
variableInfoDefaultValueIntervalText=From: {0} To: {1}
#XLBL: label for the mandatory field on variable popover
variableDetailsPopoverMandatory=Is Mandatory
#XMSG: message for empty scenario in the used in list on variable details
variableDetailsEmptyUsedInList=Variable is not being used.
#XLBL: label for the derived value on variable popover
variableInfoDerivedValue=Derived Value
#XMSG: message pattern for the derived value on variable popover (Column1 from Source1)
variableInfoDerivedColumnFromSource={0} ({1})
#XLBL: Label for Lookup Entity Parameter Mapping field
lookupEntityParam=Lookup Entity Parameter
#XTEXT: pattern for filter based on filter variable (Filter on AttributeName (SourceName))
variableInfoFilterOnAttributeText=Filter on {0} ({1})
#XLBL: Label for value help variable info popover
valueHelp=Value Help
#XTIT: title entry for time dependency
timeDependency=Time Dependency
#XTEXT: value definition information (manual)
variableInfoManual=Manual
#XTEXT: value definition information (variable derivation)
variableInfoDerivedColumn=Derived from "{0}"
#XTEXT: value definition information (dynamic variable derivation)
variableInfoDynamicDerivedColumn=Dynamically derived from "{0}"
#XTEXT: value definition information (variable derivation type)
variableInfoDerivedColumnType=Derived
#XTEXT: value definition information (dynamic variable derivation type)
variableInfoDynamicDerivedColumnType=Dynamically derived
#XLBL: use association dimension text for checkbox in attribute details view
useAssociatedDimension=Use Associated Dimension
#XMSG: Warning message for reference attribute details view displaying the base attribute name and source
referenceAttributeWarningMessage=This attribute is a reference attribute for the "{0}" attribute in the "{1}" source.
#XLBL: Base Attribute label
baseAttributeLabel=Base Attribute
#XTOL: tooltip for copy
copy=Copy
#XTOL: tooltip for copy measure
copyMeasureTooltip=Copy Measure
#XBUT: tooltip for copy when more than one measure is selected
copyButtonDisabledTooltip=Copy is disabled when more than one measure is selected
#XBUT: tooltip for when a fact soure measure is selected
copyFactSourceMeasureButtonDisabledTooltip=Copy is disabled for fact source measures
#XTEXT: Invalid entry for character validation
invalidEntry=Invalid entry
#XBUT: Tooltip for sorting object list in descending order button
sortDescending=Sort Descending
#XBUT: Tooltip for sorting object list in ascending order button
sortAscending=Sort Ascending
#XBUT: Tooltip for sorting object list without sort
noSort=Initial Order
#XBUT: Tooltip for sorting dependent object list by severity button
sortBySeverity=Sort by Severity of Status
#XTEXT: text for link to go to incorrect property in validations
goToIncorrectProperty=Go to Incorrect Property
#XBUT: Tooltip for internal navigation
goToProperty=Go to Property
#XBUT: Tooltip for internal navigation to attribute
goToAttribute=Go to Attribute
#XBUT: Tooltip for internal navigation to dimension source
goToDimension=Go to Dimension Source
#XBUT: Tooltip for internal navigation to measure
goToMeasure=Go to Measure
#XBUT: Tooltip for internal navigation to variable
goToVariable=Go to Variable
#XBUT: Tooltip for internal navigation to filter
goToFilter=Go to Filter
#XBUT: Tooltip for internal navigation to fact source
goToFact=Go to Fact Source
#XBUT: Tooltip for internal navigation to data acess control
goToDac=Go to Data Access Control
#XFLD Not applicable
NOT_APPLICABLE=N/A
#~~~~~~~~~~~ Dimension Affixes ~~~~~~~~~~~~~~~~~~~
#XTIT: Title for dimension affix edit dialog
dimensionAffixDialogTitle=Qualify Attribute Names
#XLBL: Label for business name affix input field
labelForBusinessNameAffix=Qualified Business Name
#XLBL: Label for technical name affix input field
labelForTechnicalNameAffix=Qualified Technical Name
#XTEXT: Type of dimension affix "None"
dimensionAffixOptionNone=None
#XTEXT: Type of dimension affix "Prefix"
dimensionAffixOptionPrefix=Prefix
#XTEXT: Type of dimension affix "Suffix"
dimensionAffixOptionSuffix=Suffix
#XLBL: Label for preview of attribute technical or business name with affix
labelForAffixPreview=Preview
#XMSG: Message for when the input prefix is not valid
invalidAttributePrefix=Attribute prefix cannot begin with '_'.
#XMSG: Warning message for the technical name attribute affix when the model has already been deployed
editingTechnicalAffixForDeployedModel=This model has already been deployed. Changing this property might affect existing stories in SAP Analytics Cloud.
#XMSG: Warning message for the technical name attribute affix when the model has already been deployed
editingTechnicalAffixForDeployedModelStacking=This model has already been deployed. Changing this property might affect existing stories in SAP Analytics Cloud or analytic models.
#XMSG: Error message for when the affix is manually edited out of the business name
businessNameWithoutAffix=Prefix/suffix cannot be removed from the business name.
#XMSG: Error message for when the technical affix is edited to one already in use
technicalAffixAlreadyExists=This technical {0} is already in use.
#XMSG: Error message for when the technical affix is invalid
invalidTechnicalAffix={0} is not valid.
#XMSG: Message for when the UI displays technical names and the business name has an affix
existingBusinessAffix=Business names have "{0}" as {1}.
#XMSG: Message for when the UI displays business names and the technical name has an affix
existingTechnicalAffix=Technical names have "{0}" as {1}.
#XMSG: Error message for when the affix is manually edited out of the technical name
technicalNameWithoutAffix=Prefix/suffix cannot be removed from the technical name.
#XMSG: Error message for when an affix is empty
affixIsEmpty={0} may not be empty.
#LBL: Checkbox for resetting a dimension's attributes technical names to the source
resetAttributeNames=Reset all attributes to the original source names
#XTOL: Tooltip for help button
help=Help
#XTOL: Tooltip for edit affixes button
editAffixesTooltip=Edit Attribute Names
#XMSG: Help text for resetting dimension's attributes technical names to the source
resetAttributeNamesHelp=When you select "Reset all attributes to the original source names", you can reset the technical names. When an attribute’s technical name is renamed with a counter because of conflicts, this function removes that counter by resetting the technical name to match the source. This does not affect prefixes and suffixes.
#XLBL: Label for source in inherited filter info popover
filterSource=Source
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

# MARK: Cross Calculation
#XFLD: Default calculated cross calculation business name
defaultCalculatedCrossCalculationBusinessName=Calculated Cross Calculation
#XFLD: Default restricted cross calculation business name
defaultRestrictedCrossCalculationBusinessName=Restricted Cross Calculation
#XTIT: header title for cross calculation
crossCalculation=Cross Calculation
#XFLD: Label for cross calculation type
crossCalculationType=Cross Calculation Type
#XFLD: Calculated Cross Calculation menu entry
calculatedCrossCalculation=Calculated Cross Calculation
#XFLD: Restricted Cross Calculation menu entry
restrictedCrossCalculation=Restricted Cross Calculation
#XFLD: Unknown Cross Calculation type menu entry
unknownCrossCalculationType=Unknown Cross Calculation Type
#XTIT: Title for cross calculation in object info of expression editor tools
crossCalculationDetails=Cross Calculation Details
#XTOL: Tooltip for the Cross Calculations menu button
addCrossCalculation=Add Cross Calculation
#XBUT: tooltip for cross calculation edit
editCrossCalculation=Edit Cross Calculation
#XTIT: Title for Cross Calculations section on Output
outputCrossCalculationsListTitle=Cross Calculations ({0})
#XTOL: delete cross calculations tooltip
deleteCrossCalculationsTooltip=Delete Cross Calculations
#XTIT: Checkbox to add all cross calculations on the takeover dialog
associatedAllCrossCalculations=Add all cross calculations to analytic model
#XBUT: Tooltip for internal navigation to cross calculation
goToCrossCalculation=Go to Cross Calculation
#XMSG: Message for empty Used In Cross Calculation list in cross calculation details
usedInCrossCalculationsNoData=Cross Calculation is not being used.
#XMSG: Validation message for a calculated cross calculation with invalid function parameter
validationMessageCalculatedCrossCalculationInvalidParameter=Calculated cross calculation "{0}": Invalid parameter type
#XMSG: Validation message for a calculated cross calculation with invalid formula
validationMessageCalculatedCrossCalculationInvalidExpression=Calculated cross calculation "{0}": Invalid expression
#XMSG: Error message expression for calculated cross calculation empty
calculatedCrossCalculationEmptyExpression=Expression for calculated cross calculation must not be empty.
#XMSG: Validation message for restricted cross calculation with incorrect constant selection
validationMessageRestrictedCrossCalculationConstantSelectionAttributesIncorrect=Restricted cross calculation "{0}": Invalid constant selection
#XMSG: Validation message for restricted cross calculation with constant selection ALL and global filter
validationMessageRestrictedCrossCalculationConstantSelectionAllGlobalFilter=Restricted cross calculation "{0}": A constant selection for all dimensions is used and there is a global filter
#XMSG: Validation description for restricted cross calculation with constant selection and global filter
validationDescriptionRestrictedCrossCalculationConstantSelectionGlobalFilter=Restricted cross calculation "{0}": Potentially unexpected value in result due to usage of constant selection and global filter
#XMSG: Validation message for restricted cross calculation with constant selection and global filter
validationMessageRestrictedCrossCalculationConstantSelectionGlobalFilter=Restricted cross calculation "{0}": The dimension(s) "{1}" are used in constant selections and in global filter
#XMSG: Validation message for a restricted cross calculation with invalid formula
validationMessageRestrictedCrossCalculationInvalidExpression=Restricted cross calculation "{0}": Invalid expression
#XMSG: Validation message for cross calculation exception aggregation dimensions missing
validationCrossCalculationExceptionAggregationAttributeMissing=Exception aggregation dimensions of cross calculation "{0}" missing
#XMSG: Error message reference in expression cannot be found in cross calculations or dimensions
crossCalculationExpressionReferenceNotFound=Cross calculation or dimension "{1}" cannot be found.
#XMSG: Error message reference in expression to the cross calculation itself
crossCalculationSelfReference=The cross calculation itself must not be referenced in the expression.
#XMSG: Error message referenced calculated cross calculation variable in formula cannot be found in variables
calculatedCrossCalculationExpressionVariableNotFound=The calculated cross calculation variable "{1}" cannot be found.
#XMSG: Error message referenced restricted cross calculation variable in formula cannot be found in variables
restrictedCrossCalculationExpressionVariableNotFound=The restricted cross calculation variable "{1}" cannot be found.
#XMSG: Error message referenced restriction variable or standard variable that is incorrect used in formula
crossCalculationContainsIncorrectVariableType=Expression for cross calculation "{0}" contains variable "{1}" with incorrect type.
#XMSG: Validation message for parts of a calculated cross calculation expression which are not a valid condition due to not allowed operators for non numerc types
validationMessageCalculatedCrossCalculationNonNumericTypes=Calculated cross calculation "{1}" uses element with data type "{2}". Calculated cross calculation should have a numeric result type.
#XMSG: Validation message for parts of a calculated cross calculation expression which are not a valid condition due to incompatible types
invalidCalculatedCrossCalculationFilterConditionTypeMismatch=Invalid condition in calculated cross calculation "{4}".
#XMSG: Validation message for parts of a restricted cross calculation expression which are not a valid condition due to incompatible types
invalidConditionRestrictedCrossCalculation=Invalid condition in restricted cross calculation "{4}".
#XMSG: Validation descriptions for restrited cross calculatuins using = null
validationDescriptionRestrictedCrossCalculationEqualsNull=Restricted cross calculation "{0}" has no valid expression. Operator "{1}" does not support NULL as right side operand. IS NULL should be used instead.
#XMSG: Validation descriptions for restrited cross calculatuins using != null
validationDescriptionRestrictedCrossCalculationNotEqualsNull=Restricted cross calculation "{0}" has no valid expression. Operator "{1}" does not support NULL as right side operand. IS NOT NULL should be used instead.
#XMSG: Validation message for restrited cross calculation using = null or != null
validationMessageRestrictedCrossCalculationEqualOrNotEqualsNull=Restricted cross calculation "{0}" has no valid expression. Operator "{1}" does not support NULL as right side operand.
# XMSG: Validation message for restricted formula where the IN operator list contains a dimension
validationDescriptionRestrictedFormulaInOperatorListMustNotContainDimension=List of IN operator must not contain a dimension.
#XMSG: Validation detailed message for a restricted cross calculation with invalid formula
validationDescriptionRestrictedCrossCalculationInvalidExpression=The expression of the restricted cross calculation is invalid, provide a valid formula.
#XMSG: Validation message for parts of a restricted cross calculation expression which are not a valid condition due to incompatible types
invalidRestrictedCrossCalculationTypeMismatch=Invalid condition in restricted cross calculation "{4}".
#XFLD: Fact source cross calculation menu entry
factSourceCrossCalculation=Fact Source Cross Calculation
#XMSG: Validation message for non-existent fact source cross calculation
validationMessageCrossCalculationMissingInFactSource=Source field of cross calculation "{0}" not found.
#XMSG: Validation description for non-existent fact source cross calculation
validationDescriptionCrossCalculationMissingInFactSource=Source field "{1}" of cross calculation "{0}" does not exist in fact source "{2}".
#XMSG: Validation message for auxiliary fact source cross calculation
validationMessageCrossCalculationIsAuxiliaryInFactSource=Source field of cross calculation "{0}" is auxiliary.
#XMSG: Validation description for auxiliary fact source cross calculation
validationDescriptionCrossCalculationIsAuxiliaryFactSource=Source field "{1}" of cross calculation "{0}" is auxiliary in fact source "{2}".
#XMSG: Validation for cycles in cross calculations
validationMessageCycleInCrossCalculations=Cross calculation "{0}" has a cyclic reference.
#XMSG: Validation for cycles in cross calculations long text (description)
validationMessageCycleInCrossCalculationsDescription=Cross calculation "{0}" uses itself via this path: "{1}".
#XMSG: Error message cross calculation technical name empty
crossCalculationTechnicalNameEmpty=The technical name of the cross calculation is empty.
#XMSG: Error message cross calculation business name empty
crossCalculationBusinessNameEmpty=The business name of the cross calculation is empty.
#XMSG: Error message cross calculation technical name is already used in measures, attributes/dimensions or cross calculations
crossCalculationTechnicalNameIsNotUnique=The technical name is already used in another {0}.
#XMSG: Warning when changing a cross calculatuon technical name with stacking
crossCalculationTechnicalNameChangeWarning=Changing the technical name of the cross calculation might affect existing stories or analytic models.
#XTOL: Tooltip for the collision handling configuration button
collisionHandlingConfiguration=Collision handling configuration
#XMSG Information for collision handling configuration
collisionHandlingConfigurationInformation=Models with both measures and cross calculations can have conflicting properties. For example, if measures and cross calculations have different formula definitions, setting a structure priority helps you define whether measure or cross calculations formula should take precedence.
#XLBL: Label for collision handling configuration select
collisionHandlingConfigurationSelectLabel=Prioritize properties and formulas from
#XLST: Text for cross calculations options
crossCalculations=Cross Calculations
#XMSG: Validation message for cross calculation technical name is reserved
validationMessageMeasureValuesIsReservedTechnicalName=Cross calculation "{0}" has an invalid technical name.
#XMSG: Validation description for cross calculation technical name is reserved
validationMessageDescriptionMeasureValuesIsReservedTechnicalName=Technical name 'MeasureValues' is reserved and cannot be used.
#XTOL: tooltip for copy cross calculation
copyCrossCalculationTooltip=Copy Cross Calculation
#XBUT: tooltip for copy when more than one cross calculation is selected
copyCrossCalculationButtonDisabledTooltip=Copy is disabled when more than one cross calculation is selected
#XBUT: tooltip for when a fact soure cross calculation is selected
copyFactSourceCrossCalculationButtonDisabledTooltip=Copy is disabled for fact source cross calculations
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#MARK: Unit Conversion
UC_txtTargetUnit=Target Unit
#XTIT: Title Unit Conversion Properties Panel
UC_unitPropertiesTitle=Unit Properties
#XTIT: label of Unit Name
UC_txtUnitName=Unit Name
#XTIT: label of Unit Key
UC_txtUnitKey=Unit Key
#XTIT: title of target Unit Conversion dialog
UC_targetCurrencyDialogTitle=Select Target Unit
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD:Default variable name for target unit
defaultNameTargetUnitVariableInput=Target Unit {0}
