/** @format */

import { RepositoryObjectType } from "@sap/deepsea-types";
import {
  AnalyticModelAttributeType,
  AnalyticModelCalculatedMeasureOperandType,
  AnalyticModelConstantSelectionType,
  AnalyticModelExceptionAggregationType,
  AnalyticModelMeasureType,
  AnalyticModelParameterType,
  AnalyticModelRestrictedMeasureOperandType,
  IAnalyticModelAttributeKey,
  IAnalyticModelCalculatedMeasure,
  IAnalyticModelConstantValue,
  IAnalyticModelCountDistinctMeasure,
  IAnalyticModelCurrencyConversionMeasure,
  IAnalyticModelFormatting,
  IAnalyticModelMeasure,
  IAnalyticModelNonCumulativeMeasure,
  IAnalyticModelRestrictedMeasure,
  IAnalyticModelRestrictedMeasureOperandVariable,
  IAnalyticModelSource,
  IAnalyticModelSourceMeasure,
  IAnalyticModelSourceParameterBase,
  IAnalyticModelSqlFunctionName,
  IAnalyticModelUnitConversionMeasure,
  IAnalyticModelVariableKey,
} from "../../../../../shared/queryBuilder/AnalyticModel";
import { IDataEntity, IDataEntityDetailsResponse } from "../../../../../shared/queryBuilder/DataEntityDetails";
import { VariablePathHelper } from "../../../../../shared/queryBuilder/VariablePathHelper";
import { MessageHandler } from "../../../reuse/utility/MessageHandler";
import { User } from "../../../shell/utility/User";
import { ObjectNameDisplay } from "../../../userSettings/utility/Constants";
import { DataEntityDetailsService } from "../../api/DataEntityDetails.service";
import { CubeBuilderEditorClass } from "../../controller/CubeBuilderEditor.controller";
import QueryUIModel from "../../model/QueryUIModel";
import { getIconForModel } from "../../utility/CubeBuilder";
import {
  DimensionKeys,
  DimensionTreeNode,
  UIFilter,
  UIMeasure,
  UIMeasures,
  UIVariable,
  UIVariables,
  UsedInItem,
} from "../../utility/CubeBuilderTypes";
import { AnalyticModelObjectType } from "../../utility/Enum";
import Formatter from "../../utility/Formatter";
import {
  formatExpressionFormulaToUIFunctionNames,
  getAttributeDetails,
  getAttributeKey,
  getBaseAttribute,
  getDisplayDataType,
  getMeasureFromDataEntityDetails,
  getParameterFromDataEntityDetails,
  getSourceForMeasure,
  getSourceObjectLink,
  getSourcesFromAttribute,
  getVariableBusinessName,
  isVariableDerivationOption,
} from "../../utility/ModelUtils";
import Util from "../../utility/Util";
import { ExtensionBase } from "../ExtensionBase";
import { Dependencies } from "../dependencies/Dependencies";

interface PopoverOptions {
  disableLinks?: boolean;
  isStacked?: boolean;
}

interface SetupOptions {
  fragmentName?: string;
  popoverId?: string;
}

export class InfoPopover extends ExtensionBase {
  private controller: CubeBuilderEditorClass;
  private dependencies: Dependencies;

  private measureInfoPopover: sap.m.Popover;
  private attributeInfoPopover: sap.m.Popover;
  private variableInfoPopover: sap.m.Popover;
  private filterInfoPopover: sap.m.Popover;
  private dacInfoPopover: sap.m.Popover;
  private dimensionInfoPopover: sap.m.Popover;

  constructor(protected model: QueryUIModel) {
    super();
    this.model = model;
    this.controller = Util.Component.controller();
    this.dependencies = new Dependencies(this.model);
  }

  private destroyPopover(popover: sap.m.Popover, type: AnalyticModelObjectType) {
    if (!popover) {
      return;
    }

    this.setPopover(type, undefined);
    popover.destroy();
  }

  private closePopover(type: AnalyticModelObjectType): boolean {
    const popover = this.getPopover(type);
    if (popover && popover.isOpen()) {
      popover.close();
      return true;
    }
  }

  private setup(
    sourceControl: HTMLElement,
    view: sap.ui.core.mvc.View,
    type: AnalyticModelObjectType,
    options?: SetupOptions
  ) {
    let popover = this.getPopover(type);
    const viewController = view.getController();
    const popoverName = this.getPopoverName(type);
    popover = sap.ui.xmlfragment(
      viewController.createId(options?.popoverId ?? popoverName),
      `sap.cdw.components.cubebuilder.view.${options?.fragmentName ?? popoverName}`,
      viewController
    ) as sap.m.Popover;
    this.setPopover(type, popover);

    view.addDependent(popover);
    sourceControl.classList.add("popoverButtonVisible");
    popover.attachAfterClose(() => {
      sourceControl.classList.remove("popoverButtonVisible");
      this.destroyPopover(popover, type);
    });
  }

  private open(popover: sap.m.Popover, sourceControl: HTMLElement, data: object, options: PopoverOptions) {
    if (!popover) {
      return;
    }

    popover.setModel(new sap.ui.model.json.JSONModel(data));
    popover.setModel(new sap.ui.model.json.JSONModel(options), "options");

    popover.setInitialFocus(popover);
    popover.openBy(sourceControl, false);
  }

  // requests that may need to be performed
  private async setPopoverSourceLink(popover: sap.m.Popover, sourceKey: string) {
    const sourceLink = await getSourceObjectLink(sourceKey, this.controller.getSpaceName());
    popover.getModel().setProperty("/sourceLink", sourceLink);
  }

  private getPopover(type: AnalyticModelObjectType): sap.m.Popover {
    return {
      [AnalyticModelObjectType.MEASURE]: this.measureInfoPopover,
      [AnalyticModelObjectType.ATTRIBUTE]: this.attributeInfoPopover,
      [AnalyticModelObjectType.VARIABLE]: this.variableInfoPopover,
      [AnalyticModelObjectType.FILTER]: this.filterInfoPopover,
      [AnalyticModelObjectType.DAC]: this.dacInfoPopover,
      [AnalyticModelObjectType.DIMENSION]: this.dimensionInfoPopover,
    }[type];
  }

  private setPopover(type: AnalyticModelObjectType, popover: sap.m.Popover) {
    if (type === AnalyticModelObjectType.MEASURE) {
      this.measureInfoPopover = popover;
    } else if (type === AnalyticModelObjectType.ATTRIBUTE) {
      this.attributeInfoPopover = popover;
    } else if (type === AnalyticModelObjectType.VARIABLE) {
      this.variableInfoPopover = popover;
    } else if (type === AnalyticModelObjectType.FILTER) {
      this.filterInfoPopover = popover;
    } else if (type === AnalyticModelObjectType.DAC) {
      this.dacInfoPopover = popover;
    } else if (type === AnalyticModelObjectType.DIMENSION) {
      this.dimensionInfoPopover = popover;
    } else {
      throw new Error("Invalid popover type");
    }
  }

  private getPopoverName(type: AnalyticModelObjectType): string {
    const capitalizedType = type.charAt(0).toUpperCase() + type.slice(1).toLowerCase();
    return `${capitalizedType}InfoPopover`;
  }

  private getUIMeasure(technicalName: string): UIMeasure {
    const modelMeasures: UIMeasures = this.uiModel.getProperty("/modelMeasures");
    return modelMeasures.find((measure) => measure.technicalName === technicalName);
  }

  private getUIVariable(technicalName: string, isStackedVariable?: boolean): UIVariable {
    const modelVariables: UIVariables = this.uiModel.getProperty("/variableModel");
    return modelVariables.find(
      (variable) => variable.key === technicalName && variable.isStackedVariable === isStackedVariable
    );
  }

  private getUIDimension(sourceKey: string): DimensionTreeNode {
    const attributeTree: DimensionTreeNode[] = this.uiModel.getProperty("/attributeTree");
    return attributeTree.find((dimension) => dimension.sourceKey === sourceKey && dimension.isDimension);
  }

  private getMeasureData(technicalName: string): object {
    const measureDetails: any = this.getUIMeasure(technicalName);
    if (
      measureDetails.measureType === AnalyticModelMeasureType.FactSourceMeasure ||
      measureDetails.measureType === AnalyticModelMeasureType.CalculatedMeasure ||
      measureDetails.measureType === AnalyticModelMeasureType.RestrictedMeasure
    ) {
      const exceptionAggregationList = this.getExceptionAggregationText(measureDetails.technicalName);
      measureDetails.exceptionAggregationText = exceptionAggregationList.exceptionAggregationText;
      measureDetails.exceptionAggregationMax = exceptionAggregationList.maxCharacters;
    }
    if (measureDetails.measureType === AnalyticModelMeasureType.RestrictedMeasure) {
      const constantSelectionList = this.getConstantSelectionText(measureDetails.technicalName);
      measureDetails.constantSelectionText = constantSelectionList.constantSelectionText;
      measureDetails.constantSelectionMax = constantSelectionList.maxCharacters;
    }
    if (measureDetails.measureType === AnalyticModelMeasureType.CountDistinct) {
      const countDistinctDimensionsList = this.getCountDistinctDimensionsText(measureDetails.technicalName);
      measureDetails.countDistinctDimensionsText = countDistinctDimensionsList.countDistinctDimensionsText;
      measureDetails.countDistinctDimensionsMax = countDistinctDimensionsList.maxCharacters;
    }
    if (measureDetails.measureType === AnalyticModelMeasureType.CurrencyConversionMeasure) {
      const currencyProperties = this.getCurrencyProperties(measureDetails.technicalName);
      measureDetails.targetCurrencyText = currencyProperties.targetCurrencyText;
      measureDetails.referenceDateText = currencyProperties.referenceDateText;
      measureDetails.exchangeRateTypeText = currencyProperties.exchangeRateTypeText;
    }
    if (
      this.featureflags["DWCO_MODELING_AM_UNIT_CONVERSION"] &&
      measureDetails.measureType === AnalyticModelMeasureType.UnitConversionMeasure
    ) {
      const currencyProperties = this.getUnitProperties(measureDetails.technicalName);
      measureDetails.targetUnitText = currencyProperties.targetUnitText;
    }
    if (measureDetails.measureType === AnalyticModelMeasureType.NonCumulativeMeasure) {
      const nonCumulativeMeasure = this.model.getMeasure(
        measureDetails.technicalName
      ) as IAnalyticModelNonCumulativeMeasure;
      measureDetails.exceptionAggregationNcumText = nonCumulativeMeasure?.exceptionAggregationNcumType
        ? nonCumulativeMeasure.exceptionAggregationNcumType.split(".")[1]
        : this.getText("constantSelectionTypeNone");
      measureDetails.setUnbookedDeltaToZero = nonCumulativeMeasure?.setUnbookedDeltaToZero;
    }
    if (
      measureDetails.measureType === AnalyticModelMeasureType.CalculatedMeasure ||
      measureDetails.measureType === AnalyticModelMeasureType.RestrictedMeasure
    ) {
      measureDetails.formattedExpression = this.measureExpressionFormatter(measureDetails.technicalName);
    }

    measureDetails.formatting = this.getMeasureFormatting(measureDetails.technicalName);

    const usedInList = this.getUsedInTextForMeasure(measureDetails.technicalName);
    measureDetails.usedInText = usedInList.usedInListText;
    measureDetails.usedInMax = usedInList.maxCharacters;
    measureDetails.usedInCount = usedInList.usedInCount;
    const dimensionHandlingCapabilityEnabled = this.model.getDimensionHandlingCapability();

    return { ...measureDetails, dimensionHandlingCapabilityEnabled };
  }

  private async updateMeasureDataWithRequestInfo(measureDetails: any) {
    if (measureDetails.measureType === AnalyticModelMeasureType.FactSourceMeasure) {
      const measure = this.model.getMeasure(measureDetails.technicalName) as IAnalyticModelSourceMeasure;
      const source = getSourceForMeasure(measure, this.model);
      const allSourceDataEntityDetails: IDataEntityDetailsResponse =
        this.uiModel.getProperty("/allSourceDataEntityDetails");
      const sourceDetails = allSourceDataEntityDetails[source.dataEntity.key];
      const sourceBusinessName = sourceDetails?.businessName ?? source.dataEntity.key;
      const sourceName = {
        key: source.dataEntity.key,
        text: sourceBusinessName,
      };
      this.measureInfoPopover.getModel().setProperty("/sourceName", sourceName);

      const measureDataEntityDetails = getMeasureFromDataEntityDetails(
        this.model,
        allSourceDataEntityDetails,
        (measureDetails as IAnalyticModelSourceMeasure).key
      );
      const sourceFieldName = {
        key: measureDataEntityDetails?.key ?? measureDetails.key,
        text: measureDataEntityDetails?.text ?? measureDetails.key,
      };
      this.measureInfoPopover.getModel().setProperty("/sourceField", sourceFieldName);

      const sourceIcon = getIconForModel(sourceDetails?.technicalType as RepositoryObjectType);
      this.measureInfoPopover.getModel().setProperty("/sourceIcon", sourceIcon);

      await this.setPopoverSourceLink(this.measureInfoPopover, source.dataEntity.key);
    }
  }

  public async openMeasureInfoPopover(
    technicalName: string,
    sourceControl: HTMLElement,
    view: sap.ui.core.mvc.View,
    options?: PopoverOptions
  ) {
    if (this.closePopover(AnalyticModelObjectType.MEASURE)) {
      return;
    }
    this.setup(sourceControl, view, AnalyticModelObjectType.MEASURE);

    const data = this.getMeasureData(technicalName);
    this.open(this.measureInfoPopover, sourceControl, data, options);
    this.measureInfoPopover.setBusyIndicatorDelay(0);
    this.measureInfoPopover.setBusy(true);
    await this.updateMeasureDataWithRequestInfo(data);
    this.measureInfoPopover.setBusy(false);
  }

  private getAttributeData(technicalName: string): object {
    const attributeDetails: any = {
      keyOnModel: technicalName,
    };

    const attributeFromModel = this.model.getAttribute(attributeDetails.keyOnModel);
    attributeDetails.text = attributeFromModel.text;
    attributeDetails.attributeType = attributeFromModel.attributeType;
    const { source } = getSourcesFromAttribute(attributeFromModel, this.model);
    const allSourceDataEntityDetails = this.uiModel.getProperty("/allSourceDataEntityDetails");
    const dimensionDetails = allSourceDataEntityDetails?.[source.dataEntity.key];

    const sourceBusinessName = dimensionDetails ? dimensionDetails.businessName : "";
    attributeDetails.sourceName = {
      key: source.dataEntity.key,
      text: sourceBusinessName,
    };

    const attributeDataEntityDetails = getAttributeDetails(attributeFromModel, this.model, allSourceDataEntityDetails);
    if (attributeDataEntityDetails) {
      attributeDetails.sourceField = {
        technicalName: attributeDataEntityDetails?.key,
        businessName: attributeDataEntityDetails?.text,
      };
    } else {
      const attributeKey = getAttributeKey(attributeFromModel);
      attributeDetails.sourceField = {
        technicalName: attributeKey,
        businessName: attributeKey,
      };
    }

    attributeDetails.duplicated = attributeFromModel.duplicated;
    const isReferenceAttribute = Boolean(attributeDetails.duplicated);
    if (isReferenceAttribute) {
      const baseAttribute = getBaseAttribute(attributeDetails.keyOnModel, this.model, allSourceDataEntityDetails);
      attributeDetails.baseAttribute = {
        ...baseAttribute.value,
        keyOnModel: baseAttribute.key,
        keyOfSource: baseAttribute.sourceKey,
        sourceText: baseAttribute.source.text,
      };
    }

    const sourceTechnicalType = dimensionDetails.technicalType;
    const repoType = sourceTechnicalType as RepositoryObjectType;
    const icon = getIconForModel(repoType);
    attributeDetails.sourceIcon = icon;

    if (attributeDataEntityDetails) {
      attributeDetails.semanticType = attributeDataEntityDetails.semanticType;
      attributeDetails.displayDataType = getDisplayDataType(
        this.controller.getAttributeTypeInfoFromSource(attributeDataEntityDetails, dimensionDetails)
      );
    }
    const usedIn = this.dependencies.getAttributeUsedIn(attributeDetails.keyOnModel);
    const usedInInfo = this.getUsedInTextCountAndMaxCharacters(usedIn);
    attributeDetails.usedInCount = usedInInfo.usedInCount;
    attributeDetails.usedInMaxChars = usedInInfo.maxCharacters;
    attributeDetails.usedIn = usedInInfo.usedInListText;

    attributeDetails.dimensionHandlingCapability = this.model.getDimensionHandlingCapability();

    return attributeDetails;
  }

  private async updateAttributeDataWithRequestInfo(attributeDetails: any) {
    await this.setPopoverSourceLink(this.attributeInfoPopover, attributeDetails?.sourceName?.key);
  }

  public async openAttributeInfoPopover(
    technicalName: string,
    sourceControl: HTMLElement,
    view: sap.ui.core.mvc.View,
    options?: PopoverOptions
  ) {
    if (this.closePopover(AnalyticModelObjectType.ATTRIBUTE)) {
      return;
    }
    this.setup(sourceControl, view, AnalyticModelObjectType.ATTRIBUTE);

    const data = this.getAttributeData(technicalName);
    this.open(this.attributeInfoPopover, sourceControl, data, options);
    this.attributeInfoPopover.setBusyIndicatorDelay(0);
    this.attributeInfoPopover.setBusy(true);
    await this.updateAttributeDataWithRequestInfo(data);
    this.attributeInfoPopover.setBusy(false);
  }

  private getVariableData(technicalName: string, isStackedVariable?: boolean): object {
    const variableDetails: any = this.getUIVariable(technicalName, isStackedVariable);

    // For all types of variables, the mandatory value is true, except for Filter and Story Filter, which have a special check box for it.
    if (
      variableDetails.parameterType !== AnalyticModelParameterType.Filter &&
      variableDetails.parameterType !== AnalyticModelParameterType.StoryFilter
    ) {
      variableDetails.mandatory = true;
    }
    const modelData = { ...variableDetails };

    // handle info popover for stacked variables
    if (variableDetails.isStackedVariable) {
      let dataType = variableDetails.dataType;
      if (variableDetails.parameterType === AnalyticModelParameterType.Input) {
        const selectedKey = this.getStackedVariableDataTypeSelectedKey(variableDetails);
        if (selectedKey === "Inherited") {
          dataType = this.getText("inherited", [dataType]);
        }
      }
      modelData.dataType = dataType;

      if (
        variableDetails.parameterType === AnalyticModelParameterType.Filter ||
        variableDetails.parameterType === AnalyticModelParameterType.StoryFilter
      ) {
        const source = this.model.getFirstFactSource();
        const allSourceDataEntityDetails: IDataEntityDetailsResponse =
          this.uiModel.getProperty("/allSourceDataEntityDetails");
        const dataEntityDetails = allSourceDataEntityDetails[source.dataEntity.key];
        const currentAttributeKey = VariablePathHelper.getMatchingAnalyticModelAttribute(
          this.model.getData(),
          dataEntityDetails?.csn,
          variableDetails.referenceAttribute,
          this.model.getFirstFactSourceKey()
        );
        if (currentAttributeKey) {
          modelData.dimension = this.controller.getAttributeDisplayNameWithSource(currentAttributeKey);
        } else {
          modelData.dimension = variableDetails.referenceAttribute;
        }
      }
    } else {
      const { usedIn, usedInCount, usedInMaxChars, valueHelp, dataType, dimension, text } =
        this.getVariableInfoModelDetails(variableDetails);

      Object.assign(modelData, { usedIn, usedInCount, usedInMaxChars, valueHelp, dataType, dimension, text });
    }

    // in case of Data type "unknown", remove default value to not show it in the popover
    if ((modelData.dataType as string)?.includes("unknown")) {
      delete modelData.defaultValue;
    }
    return modelData;
  }

  private async updateLookupEntityDetails(variableDetails: any) {
    let lookupEntityText;
    if (isVariableDerivationOption(variableDetails) && variableDetails.lookupEntity) {
      // load the lookup entity details if not already loaded
      if (!this.uiModel.getProperty("/allSourceDataEntityDetails")[variableDetails.lookupEntity]) {
        try {
          const lookupEntitySource = await DataEntityDetailsService.getInstance().loadDataEntityDetails(
            [{ key: variableDetails.lookupEntity }],
            this.controller.getSpaceName()
          );
          const allSourceDataEntityDetails = this.uiModel.getProperty("/allSourceDataEntityDetails") ?? {};
          this.uiModel.setProperty("/allSourceDataEntityDetails", {
            ...allSourceDataEntityDetails,
            ...lookupEntitySource,
          });
        } catch (e) {
          await MessageHandler.exception({ message: this.getText("loadDataEntityAssociationsError"), exception: e });
        }
      }
      lookupEntityText =
        this.uiModel.getProperty("/allSourceDataEntityDetails")[variableDetails.lookupEntity]?.businessName ??
        variableDetails.lookupEntity;
    }
    this.variableInfoPopover.getModel().setProperty("/lookupEntityText", lookupEntityText);
  }

  private async updateVariableDataWithRequestInfo(variableDetails: any) {
    // setup source details for stacked variables
    if (variableDetails.isStackedVariable) {
      await this.setSourceNameForStackedVariable(variableDetails);
    }
  }

  public async openVariableInfoPopover(
    technicalName: string,
    sourceControl: HTMLElement,
    view: sap.ui.core.mvc.View,
    options?: PopoverOptions
  ) {
    if (this.closePopover(AnalyticModelObjectType.VARIABLE)) {
      return;
    }
    this.setup(sourceControl, view, AnalyticModelObjectType.VARIABLE);

    const data = this.getVariableData(technicalName, options.isStacked);
    this.open(this.variableInfoPopover, sourceControl, data, options);
    this.variableInfoPopover.setBusyIndicatorDelay(0);
    this.variableInfoPopover.setBusy(true);
    await this.updateLookupEntityDetails(data);
    await this.updateVariableDataWithRequestInfo(data);
    this.variableInfoPopover.setBusy(false);
  }

  private getFilterData(filter: UIFilter): object {
    const sourceIcon = getIconForModel(RepositoryObjectType.DWC_ANALYTIC_MODEL);
    return { ...filter, sourceIcon };
  }

  private async updateFilterDataWithRequestInfo(filter: any) {
    await this.setPopoverSourceLink(this.filterInfoPopover, filter.sourceTechnicalName);
  }

  public async openFilterInfoPopover(
    filter: UIFilter,
    sourceControl: HTMLElement,
    view: sap.ui.core.mvc.View,
    options?: PopoverOptions
  ) {
    if (this.closePopover(AnalyticModelObjectType.FILTER)) {
      return;
    }
    this.setup(sourceControl, view, AnalyticModelObjectType.FILTER);

    const data = this.getFilterData(filter);
    this.open(this.filterInfoPopover, sourceControl, data, options);
    this.filterInfoPopover.setBusyIndicatorDelay(0);
    this.filterInfoPopover.setBusy(true);
    await this.updateFilterDataWithRequestInfo(data);
    this.filterInfoPopover.setBusy(false);
  }

  private getDacData(technicalName: string): object {
    const dacEntity = this.uiModel.getProperty(`/allSourceDataEntityDetails`)[technicalName] as IDataEntity;
    const businessName = dacEntity.businessName;

    const source: any = {};
    const repoType = dacEntity?.technicalType as RepositoryObjectType;
    const icon = getIconForModel(repoType);
    source.businessName = businessName;
    source.technicalName = technicalName;
    source.icon = icon;

    const dacPopoverModel = {
      association: {
        text: businessName,
        key: technicalName,
        targetEntity: {
          text: businessName,
          key: technicalName,
        },
        associationType: this.getText("dataAccessControl"),
      },
      dataEntity: { key: this.model.getTechnicalName() },
      source: source,
    };

    return dacPopoverModel;
  }

  private async updateDacDataWithRequestInfo(dac: any) {
    await this.setPopoverSourceLink(this.dacInfoPopover, dac.association.key);
  }

  public async openDacInfoPopover(
    technicalName: string,
    sourceControl: HTMLElement,
    view: sap.ui.core.mvc.View,
    options?: PopoverOptions
  ) {
    if (this.closePopover(AnalyticModelObjectType.DAC)) {
      return;
    }
    this.setup(sourceControl, view, AnalyticModelObjectType.DAC, {
      fragmentName: "AssociationInfoPopover",
      popoverId: "DacInfoPopover",
    });

    const data = this.getDacData(technicalName);
    this.open(this.dacInfoPopover, sourceControl, data, options);
    this.dacInfoPopover.setBusyIndicatorDelay(0);
    this.dacInfoPopover.setBusy(true);
    await this.updateDacDataWithRequestInfo(data);
    this.dacInfoPopover.setBusy(false);
  }

  private getDimensionData(sourceKey: string): object {
    const dimension: any = this.getUIDimension(sourceKey);

    dimension.dimensionHandlingCapability = this.model.getDimensionHandlingCapability();

    const dimensionSource = this.model.getDimensionSource(dimension.sourceKey);
    dimension.sourceDetails = this.uiModel.getProperty("/allSourceDataEntityDetails")[dimensionSource.dataEntity.key];
    const dimensionAsAttributeDetails = this.controller.getDimensionAsAttributeDetails(
      dimension.sourceKey,
      dimensionSource
    );

    dimension.semanticType = dimensionAsAttributeDetails?.semanticType;
    dimension.dataType = this.controller.getDimensionDataType(dimension.sourceKey, dimensionSource);

    dimension.sourceIcon = getIconForModel(dimension.sourceDetails.technicalType as RepositoryObjectType);

    const keyAttributes = this.controller.getDimensionKeys(dimension.sourceKey, dimensionSource);
    const keysFormatted = this.getFormattedDimensionKeys(keyAttributes);
    dimension.keysMaxChars = Formatter.maxCharsExpandableTextForListFormatter(keysFormatted);
    dimension.keys = keysFormatted.join("\n").concat("\n");

    if (dimension.dimensionHandlingCapability) {
      if (keyAttributes?.length > 1) {
        const representativeKey = this.controller.getRepresentativeKeyFromDimensionKeys(keyAttributes, dimensionSource);
        dimension.dimensionAttributeTechnicalName = representativeKey;
      } else {
        dimension.dimensionAttributeTechnicalName = keyAttributes?.[0].keyOnModel;
      }
    }

    return dimension;
  }

  private async updateDimensionDataWithRequestInfo(dimension: any) {
    await this.setPopoverSourceLink(this.dimensionInfoPopover, dimension.sourceDetails.technicalName);
  }

  public async openDimensionInfoPopover(
    sourceKey: string,
    sourceControl: HTMLElement,
    view: sap.ui.core.mvc.View,
    options?: PopoverOptions
  ) {
    if (this.closePopover(AnalyticModelObjectType.DIMENSION)) {
      return;
    }
    this.setup(sourceControl, view, AnalyticModelObjectType.DIMENSION);

    const data = this.getDimensionData(sourceKey);
    this.open(this.dimensionInfoPopover, sourceControl, data, options);
    this.dimensionInfoPopover.setBusyIndicatorDelay(0);
    this.dimensionInfoPopover.setBusy(true);
    await this.updateDimensionDataWithRequestInfo(data);
    this.dimensionInfoPopover.setBusy(false);
  }

  private getExceptionAggregationText(measure: string) {
    const modelMeasure = this.model.getMeasure(measure) as
      | IAnalyticModelRestrictedMeasure
      | IAnalyticModelCalculatedMeasure
      | IAnalyticModelSourceMeasure;
    let exceptionAggregationText = this.getText("constantSelectionTypeNone");
    let maxCharacters = 200;
    if (!modelMeasure.exceptionAggregationType) {
      const exceptionAggregationFromUnderlyingModel = this.getExceptionAggregationTextFromUnderlyingModel(
        measure,
        modelMeasure
      );
      return (
        exceptionAggregationFromUnderlyingModel ?? {
          exceptionAggregationText: exceptionAggregationText,
          maxCharacters: maxCharacters,
        }
      );
    }
    if (!modelMeasure.exceptionAggregationAttributes) {
      return { exceptionAggregationText: "-", maxCharacters: maxCharacters };
    }

    return this.getExceptionAggregationTextFromTypeAndAttributes(
      modelMeasure.exceptionAggregationType,
      modelMeasure.exceptionAggregationAttributes
    );
  }

  private getExceptionAggregationTextFromTypeAndAttributes(
    exceptionAggregationType: AnalyticModelExceptionAggregationType,
    exceptionAggregationAttributes: string[],
    externalAttributes: boolean = false
  ) {
    const exceptionAggregationTypeText = exceptionAggregationType.split(".")[1] + " -\n";
    const attributesList = this.getExpandableTextForAttributeList(exceptionAggregationAttributes, externalAttributes);
    const exceptionAggregationAttributesText = attributesList.attributesListText;
    const maxCharacters = attributesList.maxCharacters + exceptionAggregationTypeText.length;
    const exceptionAggregationText = exceptionAggregationTypeText + exceptionAggregationAttributesText;
    return { exceptionAggregationText, maxCharacters };
  }

  private getExceptionAggregationTextFromUnderlyingModel(
    measureKey: string,
    measure: IAnalyticModelMeasure
  ): {
    exceptionAggregationText: string;
    maxCharacters: number;
  } {
    if (!this.featureflags["DWCO_MODELING_ANALYTIC_MODEL_STACKING"]) {
      return undefined;
    }
    if (measure.measureType !== AnalyticModelMeasureType.FactSourceMeasure) {
      return undefined;
    }

    const allSourceDataEntityDetails = this.uiModel.getProperty(
      "/allSourceDataEntityDetails"
    ) as IDataEntityDetailsResponse;

    const dataEntityMeasure = getMeasureFromDataEntityDetails(this.model, allSourceDataEntityDetails, measureKey);

    if (!dataEntityMeasure?.exceptionAggregationType || !dataEntityMeasure?.exceptionAggregationAttributes) {
      return undefined;
    }

    return this.getExceptionAggregationTextFromTypeAndAttributes(
      dataEntityMeasure.exceptionAggregationType,
      dataEntityMeasure.exceptionAggregationAttributes,
      true
    );
  }

  public getExpandableTextForAttributeList(attributes: string[], externalAttributes: boolean = false) {
    let index = 0;
    let attributesListText = "";
    let maxCharacters = 200;
    if (!attributes) {
      return { attributesListText: attributesListText, maxCharacters: maxCharacters };
    }
    for (const attribute of attributes) {
      index++;
      let attributeSourceName = "";
      let attributeName = attribute;
      if (!externalAttributes) {
        const modelAttribute = this.model.getAttribute(attribute);
        if (modelAttribute) {
          const attributeSource = getSourcesFromAttribute(modelAttribute, this.model);
          const displayTechnicalName =
            User.getInstance().getObjectNameDisplay() === (ObjectNameDisplay.technicalName as string);
          attributeSourceName = attributeSource?.source.text;
          if (this.model.getDimensionHandlingCapability()) {
            if (displayTechnicalName) {
              attributeSourceName = attributeSource?.sourceKey;
            } else {
              attributeName = modelAttribute.text;
            }
          }
        }
      }

      const attributeSourceNamePart = attributeSourceName ? " (" + attributeSourceName + ")" : "";
      attributesListText = attributesListText + attributeName + attributeSourceNamePart + "\n";
      if (index === 2 && attributes.length > 2) {
        maxCharacters = attributesListText.length;
      }
    }
    return { attributesListText: attributesListText, maxCharacters: maxCharacters };
  }

  private getMeasureFormatting(technicalName: string): IAnalyticModelFormatting {
    const formatting = { scaleType: undefined, decimalPlaces: undefined };
    if (!this.featureflags["DWCO_MODELING_AM_FORMAT_OF_MEASURES"]) {
      return formatting;
    }

    const measure = this.model.getMeasure(technicalName);
    if (!measure) {
      return formatting;
    }

    if (measure.formatting) {
      formatting.decimalPlaces = measure.formatting.decimalPlaces;
      formatting.scaleType = measure.formatting.scaleType;
    } else if (measure.measureType === AnalyticModelMeasureType.FactSourceMeasure) {
      const measureFromDataEntityDetails = getMeasureFromDataEntityDetails(
        this.model,
        this.uiModel.getProperty("/allSourceDataEntityDetails"),
        technicalName
      );
      formatting.decimalPlaces = measureFromDataEntityDetails?.decimalPlaces;
      formatting.scaleType = measureFromDataEntityDetails?.scaleType;
    }

    return formatting;
  }

  private getConstantSelectionText(measure: string) {
    const modelMeasure = this.model.getMeasure(measure) as IAnalyticModelRestrictedMeasure;
    let constantSelectionText = this.getText("constantSelectionTypeNone");
    let maxCharacters = 200;
    if (!!modelMeasure.constantSelectionType) {
      if (modelMeasure.constantSelectionType === AnalyticModelConstantSelectionType.All) {
        constantSelectionText = this.getText("constantSelectionTypeAll");
      } else if (modelMeasure.constantSelectionType === AnalyticModelConstantSelectionType.Selected) {
        if (!!modelMeasure.constantSelectionAttributes) {
          const attributesList = this.getExpandableTextForAttributeList(modelMeasure.constantSelectionAttributes);
          constantSelectionText = attributesList.attributesListText;
          maxCharacters = attributesList.maxCharacters;
        } else {
          constantSelectionText = "-";
        }
      }
    }
    return { constantSelectionText: constantSelectionText, maxCharacters: maxCharacters };
  }

  private getCountDistinctDimensionsText(measure: string) {
    const modelMeasure = this.model.getMeasure(measure) as IAnalyticModelCountDistinctMeasure;
    let countDistinctDimensionsText = "";
    let maxCharacters = 200;
    if (!modelMeasure.countDistinctAttributes || modelMeasure.countDistinctAttributes.length === 0) {
      return { constantSelectionText: "-", maxCharacters: maxCharacters };
    }
    const attributesList = this.getExpandableTextForAttributeList(modelMeasure.countDistinctAttributes);
    countDistinctDimensionsText = attributesList.attributesListText;
    maxCharacters = attributesList.maxCharacters;
    return { countDistinctDimensionsText: countDistinctDimensionsText, maxCharacters: maxCharacters };
  }

  private getCurrencyProperties(measure: string) {
    const measureDetails = this.model.getMeasure(measure) as IAnalyticModelCurrencyConversionMeasure;
    let targetCurrencyText = "";
    let referenceDateText = "";
    let exchangeRateTypeText = "";
    const displayBusinessName =
      User.getInstance().getObjectNameDisplay() === (ObjectNameDisplay.businessName as string);
    if (
      !measureDetails.targetCurrency ||
      (!(measureDetails.targetCurrency as IAnalyticModelConstantValue).value &&
        !(measureDetails.targetCurrency as IAnalyticModelAttributeKey).key &&
        !(measureDetails.targetCurrency as IAnalyticModelVariableKey).key)
    ) {
      targetCurrencyText = "-";
    } else {
      const targetCurrencyNames = this.controller.getConversionNames(
        measureDetails.targetCurrency,
        measureDetails.targetCurrencyType
      );
      targetCurrencyText = displayBusinessName
        ? targetCurrencyNames.currencyBusinessName
        : targetCurrencyNames.currencyTechnicalName;
    }
    if (
      !measureDetails.referenceDateType ||
      (!(measureDetails.referenceDate as IAnalyticModelConstantValue).value &&
        !(measureDetails.referenceDate as IAnalyticModelAttributeKey).key &&
        !(measureDetails.referenceDate as IAnalyticModelSqlFunctionName).functionName)
    ) {
      referenceDateText = "-";
    } else {
      const referenceDateNames = this.controller.getConversionNames(
        measureDetails.referenceDate,
        measureDetails.referenceDateType
      );
      referenceDateText = displayBusinessName
        ? referenceDateNames.currencyBusinessName
        : referenceDateNames.currencyTechnicalName;
    }
    if (
      !measureDetails.conversionTypeType ||
      (!(measureDetails.conversionType as IAnalyticModelConstantValue).value &&
        !(measureDetails.conversionType as IAnalyticModelAttributeKey).key &&
        !(measureDetails.conversionType as IAnalyticModelVariableKey).key)
    ) {
      exchangeRateTypeText = "-";
    } else {
      const exchangeRateTypeNames = this.controller.getConversionNames(
        measureDetails.conversionType,
        measureDetails.conversionTypeType
      );
      exchangeRateTypeText = displayBusinessName
        ? exchangeRateTypeNames.currencyBusinessName
        : exchangeRateTypeNames.currencyTechnicalName;
    }
    return {
      targetCurrencyText: targetCurrencyText,
      referenceDateText: referenceDateText,
      exchangeRateTypeText: exchangeRateTypeText,
    };
  }

  private getUnitProperties(measure: string) {
    const measureDetails = this.model.getMeasure(measure) as IAnalyticModelUnitConversionMeasure;
    let targetUnitText = "";
    const displayBusinessName =
      User.getInstance().getObjectNameDisplay() === (ObjectNameDisplay.businessName as string);
    if (
      !measureDetails.targetUnit ||
      (!(measureDetails.targetUnit as IAnalyticModelConstantValue).value &&
        !(measureDetails.targetUnit as IAnalyticModelAttributeKey).key &&
        !(measureDetails.targetUnit as IAnalyticModelVariableKey).key)
    ) {
      targetUnitText = "-";
    } else {
      const targetUnitNames = this.controller.getConversionNames(
        measureDetails.targetUnit,
        measureDetails.targetUnitType
      );
      targetUnitText = displayBusinessName
        ? targetUnitNames.currencyBusinessName
        : targetUnitNames.currencyTechnicalName;
    }
    return {
      targetUnitText: targetUnitText,
    };
  }

  private getUsedInTextForMeasure(usingMeasure: string): {
    usedInListText: string;
    maxCharacters: number;
    usedInCount: number;
  } {
    const usedIn = this.dependencies.getUsedInListForMeasure(usingMeasure);
    return this.getUsedInTextCountAndMaxCharacters(usedIn);
  }

  private getUsedInTextCountAndMaxCharacters(usedInList: UsedInItem[]): {
    usedInListText: string;
    maxCharacters: number;
    usedInCount: number;
  } {
    const usedInCount = usedInList?.length ?? 0;
    const usedInFormatted = this.controller.getFormattedUsedIn(usedInList);
    const usedInMaxChars = Formatter.maxCharsExpandableTextForListFormatter(usedInFormatted, 4);
    const usedInListText = usedInFormatted.join("<br>").concat("<br>");
    return { usedInListText: usedInListText, maxCharacters: usedInMaxChars, usedInCount: usedInCount };
  }

  public measureExpressionFormatter(technicalName: string): string {
    const measure = this.model.getMeasure(technicalName) as
      | IAnalyticModelCalculatedMeasure
      | IAnalyticModelRestrictedMeasure;
    if (!measure) {
      return "-";
    } else if (!measure.elements || Object.keys(measure.elements).length === 0) {
      return !!measure.formulaRaw ? measure.formulaRaw : "-";
    }
    let replacedExpression = formatExpressionFormulaToUIFunctionNames(measure.formula, this.featureflags);
    const defaultExpression = measure.formulaRaw;
    const expressionElements = Object.keys(measure.elements);
    if (!expressionElements) {
      return defaultExpression;
    } else if (measure.measureType === AnalyticModelMeasureType.CalculatedMeasure) {
      expressionElements.forEach((element, index) => {
        const calculatedMeasureElement = measure.elements[Number(element)];
        if (calculatedMeasureElement.operandType === AnalyticModelCalculatedMeasureOperandType.Element) {
          const measureInExpression = this.model.getMeasure(calculatedMeasureElement.key) as any;
          if (!!measureInExpression) {
            const name = this.controller.getDisplayNameForElementInExpression(
              calculatedMeasureElement.key,
              measureInExpression.text,
              AnalyticModelObjectType.MEASURE
            );
            replacedExpression = replacedExpression.replace(`[${index}]`, name);
          } else {
            const attributeInCalculatedExpression = this.model.getAttribute(calculatedMeasureElement.key) as any;
            if (!!attributeInCalculatedExpression) {
              const name = this.controller.getDisplayNameForElementInExpression(
                calculatedMeasureElement.key,
                attributeInCalculatedExpression.text,
                AnalyticModelObjectType.ATTRIBUTE
              );
              replacedExpression = replacedExpression.replace(`[${index}]`, name);
            } else {
              replacedExpression = replacedExpression.replace(`[${index}]`, calculatedMeasureElement.key);
            }
          }
        } else if (calculatedMeasureElement.operandType === AnalyticModelCalculatedMeasureOperandType.FormulaVariable) {
          const variableInExpression = this.model.getVariable(calculatedMeasureElement.key);
          if (!!variableInExpression) {
            const name = this.controller.getDisplayNameForElementInExpression(
              calculatedMeasureElement.key,
              (variableInExpression as any).text,
              AnalyticModelObjectType.VARIABLE
            );
            replacedExpression = replacedExpression.replace(`[${index}]`, name);
          } else {
            replacedExpression = replacedExpression.replace(`[${index}]`, calculatedMeasureElement.key);
          }
        } else if (calculatedMeasureElement.operandType === AnalyticModelCalculatedMeasureOperandType.ConstantValue) {
          const value = calculatedMeasureElement.value;
          const isString = typeof value === "string";
          const name = isString ? `'${value}'` : String(value);
          replacedExpression = replacedExpression.replace(`[${index}]`, name);
        }
      });
      return replacedExpression;
    } else if (measure.measureType === AnalyticModelMeasureType.RestrictedMeasure) {
      expressionElements.forEach((element, index) => {
        const restrictedMeasureElement = measure.elements[Number(element)];
        if (restrictedMeasureElement.operandType === AnalyticModelRestrictedMeasureOperandType.Attribute) {
          const attributeInRestrictedExpression = this.model.getAttribute(restrictedMeasureElement.key) as any;
          if (!!attributeInRestrictedExpression) {
            const name = this.controller.getDisplayNameForElementInExpression(
              restrictedMeasureElement.key,
              attributeInRestrictedExpression.text,
              AnalyticModelObjectType.ATTRIBUTE
            );
            replacedExpression = replacedExpression.replace(`[${index}]`, name);
          } else {
            replacedExpression = replacedExpression.replace(`[${index}]`, restrictedMeasureElement.key);
          }
        } else if (restrictedMeasureElement.operandType === AnalyticModelRestrictedMeasureOperandType.FilterVariable) {
          const variableInExpression = this.model.getVariable(restrictedMeasureElement.key);
          if (!!variableInExpression) {
            const name = this.controller.getDisplayNameForElementInExpression(
              restrictedMeasureElement.key,
              (variableInExpression as any).text,
              AnalyticModelObjectType.VARIABLE
            );
            replacedExpression = replacedExpression.replace(`[${index}]`, name);
          } else {
            replacedExpression = replacedExpression.replace(`[${index}]`, restrictedMeasureElement.key);
          }
        } else if (restrictedMeasureElement.operandType === AnalyticModelRestrictedMeasureOperandType.ConstantValue) {
          const value = restrictedMeasureElement.value;
          const isString = typeof value === "string";
          const name = isString ? `'${value}'` : String(value);
          replacedExpression = replacedExpression.replace(`[${index}]`, name);
        }
      });
      return replacedExpression;
    } else {
      return defaultExpression;
    }
  }

  private getStackedVariableDataTypeSelectedKey(variableModel: any): string {
    if (variableModel && variableModel.dataType && variableModel.dataType !== "unknown") {
      return variableModel.dataType;
    } else {
      return "Inherited";
    }
  }

  private getVariableInfoModelDetails(variableModel: any) {
    const usedInSourceName = this.getUsedInSourceName(variableModel, variableModel.key);
    variableModel.usedInSource = this.usedForDimension(variableModel, usedInSourceName);
    variableModel.groupUnderDimension = usedInSourceName;

    const variableUsedIn = this.dependencies.getUsedInParamList(variableModel.key);
    const usedInInfo = this.getUsedInTextCountAndMaxCharacters(variableUsedIn);
    const usedInCount = usedInInfo.usedInCount;
    const usedInMaxChars = usedInInfo.maxCharacters;
    const usedIn = usedInInfo.usedInListText;
    const valueHelp = this.controller.getVariableValueHelp(variableModel.key) ?? "-";
    const variableDataTypeInfo = this.controller.getVariableTypeInfoFromSource(
      variableModel.key,
      variableModel.parameterType,
      variableModel.referenceAttribute
    );
    let dataType = getDisplayDataType(variableDataTypeInfo);
    if (variableModel.parameterType === AnalyticModelParameterType.Input) {
      const selectedKey = this.getVariableDataTypeSelectedKey(variableModel);
      if (selectedKey === "Inherited") {
        dataType = this.getText("inherited", [dataType]);
      }
    }

    let dimension: string;
    if (
      (variableModel.parameterType === AnalyticModelParameterType.Filter ||
        variableModel.parameterType === AnalyticModelParameterType.StoryFilter) &&
      variableModel.referenceAttribute
    ) {
      dimension = this.controller.getAttributeDisplayNameWithSource(variableModel.referenceAttribute);
    }

    const text = getVariableBusinessName(variableModel, this.model);

    return { usedIn, usedInCount, usedInMaxChars, valueHelp, dataType, dimension, text };
  }

  private usedForDimension(variable, usedInSourceName) {
    if (variable.parameterType === AnalyticModelParameterType.StoryFilter) {
      return variable.referenceAttribute ? variable.referenceAttribute : ""; // return ref attribute for story filter variable
    } else {
      return usedInSourceName;
    }
  }

  private getVariableDataTypeSelectedKey(variableModel: any): string {
    const variableDataTypeInfo = (this.model.getVariable(variableModel.key) as IAnalyticModelSourceParameterBase)
      .dataType;
    if (variableDataTypeInfo && variableDataTypeInfo.type && variableDataTypeInfo.type !== "unknown") {
      return variableDataTypeInfo.type;
    } else {
      return "Inherited";
    }
  }

  private async setSourceNameForStackedVariable(variableModel: any) {
    const factSources = this.model.getFactSources();
    const source = factSources[variableModel.sourceKey];
    const allSourceDataEntityDetails: IDataEntityDetailsResponse =
      this.uiModel.getProperty("/allSourceDataEntityDetails");
    const sourceDetails = allSourceDataEntityDetails[source.dataEntity.key];
    const sourceBusinessName = sourceDetails?.businessName ?? source.dataEntity.key;
    const sourceName = {
      key: source.dataEntity.key,
      text: sourceBusinessName,
    };
    const sourceIcon = getIconForModel(RepositoryObjectType.DWC_ANALYTIC_MODEL);
    const parameterDataEntityDetails = getParameterFromDataEntityDetails(
      this.model,
      allSourceDataEntityDetails,
      variableModel.key
    );
    this.variableInfoPopover.getModel().setProperty("/sourceName", sourceName);
    this.variableInfoPopover.getModel().setProperty("/sourceIcon", sourceIcon);
    const sourceFieldName = {
      key: parameterDataEntityDetails.key,
      text: parameterDataEntityDetails.text,
    };
    this.variableInfoPopover.getModel().setProperty("/sourceField", sourceFieldName);

    await this.setPopoverSourceLink(this.variableInfoPopover, source.dataEntity.key);
  }

  // Refactor getUsedInSourceName and getSourceUsingVariable to use single method and return source for measure
  private getUsedInSourceName(variable, variableKey) {
    let source;
    if (variable.parameterType === AnalyticModelParameterType.Filter) {
      const measures = Object.values(this.model.getMeasures());
      const restrictedMeasure = measures.filter(
        (item) => item.measureType === AnalyticModelMeasureType.RestrictedMeasure
      ) as IAnalyticModelRestrictedMeasure[];
      for (const measure of restrictedMeasure) {
        Object.values(measure.elements || []).forEach((item) => {
          if (
            item &&
            item.operandType === AnalyticModelRestrictedMeasureOperandType.FilterVariable &&
            (item as IAnalyticModelRestrictedMeasureOperandVariable).key === variableKey
          ) {
            source = measure;
          }
        });
      }
      return source ? source.text : "";
    } else {
      const variableSource = this.getSourceUsingVariable(variableKey);
      return variableSource;
    }
  }

  /**
   * Returns the source referenced within a given variable
   * @param variableKey variable key in the model (technical name)
   * @returns {IAnalyticModelSource}
   */
  private getSourceUsingVariable(variableKey: string): IAnalyticModelSource {
    const variable = this.model.getVariable(variableKey);
    if (
      variable.parameterType === AnalyticModelParameterType.Input ||
      variable.parameterType === AnalyticModelParameterType.KeyDate ||
      variable.parameterType === AnalyticModelParameterType.FiscalVariant
    ) {
      const factSource = this.model.getFirstFactSource();
      return factSource;
    }

    if (variable.parameterType === AnalyticModelParameterType.StoryFilter) {
      const attribute = this.model.getAttribute(variableKey);
      if (attribute) {
        if (attribute.attributeType === AnalyticModelAttributeType.DimensionSourceAttribute) {
          const dimensionSource = this.model.getDimensionSource(attribute.sourceKey);
          return dimensionSource;
        } else if (attribute.attributeType === AnalyticModelAttributeType.FactSourceAttribute) {
          const factSource = this.model.getFirstFactSource();
          return factSource;
        }
      }
    }
  }

  public getFormattedDimensionKeys(dimensionKeys: DimensionKeys) {
    let dimensionKeysFormatted: string[];

    if (!dimensionKeys?.length) {
      // should only happen if keys are not in the model
      dimensionKeysFormatted = ["-"];
    } else {
      dimensionKeysFormatted = dimensionKeys.map((element) =>
        Formatter.dimensionNameFormatter(element.keyOnModel, element.text, element.parentSourceDisplayName)
      );
    }
    return dimensionKeysFormatted;
  }
}
