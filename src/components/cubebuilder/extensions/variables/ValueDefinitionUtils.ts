/** @format */

import moment from "moment";
import {
  AllQueryParameter,
  AnalyticModelDefaultRangeOption,
  AnalyticModelDefaultRangeSign,
  AnalyticModelParameterType,
  AnalyticModelParameterValueBaseType,
  AnalyticModelVariableProcessingType,
  AnalyticModelVariableSelectionType,
  IAnalyticModelDefaultInterval,
  IAnalyticModelDefaultRange,
  IAnalyticModelFilterVariable,
  IAnalyticModelFilterVariableRangeMultiSelection_MANUALINPUT,
  IAnalyticModelFilterVariableRangeSingleSelection_MANUALINPUT,
  IAnalyticModelFilterVariableSingleValueSingleSelection_MANUALINPUT,
  IAnalyticModelFiscalVariantVariable_MANUALINPUT,
  IAnalyticModelKeyDateVariable_MANUALINPUT,
  IAnalyticModelSourceParameter_MANUALINPUT,
  IAnalyticModelStoryFilterVariable,
  IAnalyticModelStoryFilterVariableRangeMultiSelection_MANUALINPUT,
  IAnalyticModelStoryFilterVariableRangeSingleSelection_MANUALINPUT,
  IAnalyticModelStoryFilterVariableSingleValueSingleSelection_MANUALINPUT,
  IAnalyticModelVariableManualInput,
} from "../../../../../shared/queryBuilder/AnalyticModel";
import { IDataEntityParameter } from "../../../../../shared/queryBuilder/DataEntityDetails";
import { IAnalyticModelDefaultValueType } from "../../../../../shared/queryBuilder/QueryModel";
import { MultiInputItem } from "../../../reuse/control/input/MultiInput";
import { DefaultValueInterval, DefaultValueRange } from "../../utility/CubeBuilderTypes";
import { AnalyticModelUIVariableSelectionType } from "../../utility/Enum";
import { isFilterOrRestrictedMeasureVariable } from "../../utility/ModelUtils";
import { ExtensionBase } from "../ExtensionBase";

export class ValueDefinitionUtils extends ExtensionBase {
  /**
   * set default value for stack variables in copy property dialog or fact source details
   * @param variable
   * @param parameterModel
   */
  public setupInputConfigFromStackedVariable(
    variable: IDataEntityParameter,
    model: sap.ui.model.json.JSONModel,
    path: string
  ) {
    const defaultValue = model.getProperty(`${path}/defaultValue`);
    this.setupDefaultValue(
      variable as (IAnalyticModelVariableManualInput & AllQueryParameter) | IDataEntityParameter,
      defaultValue,
      model,
      path
    );
    if (this.isFilterOrStoryFilterVariable(variable)) {
      this.setupSelectionType(
        variable as IAnalyticModelStoryFilterVariable | IAnalyticModelFilterVariable | IDataEntityParameter,
        model,
        path
      );
    }
  }

  public setupManualInputConfigFromVariable(
    variable: AllQueryParameter,
    model: sap.ui.model.json.JSONModel,
    path: string
  ) {
    const defaultValue = (variable as IAnalyticModelVariableManualInput & AllQueryParameter).defaultValue;
    this.setupDefaultValue(
      variable as IAnalyticModelVariableManualInput & AllQueryParameter,
      defaultValue,
      model,
      path
    );

    if (this.isFilterOrStoryFilterVariable(variable)) {
      this.setupSelectionType(
        variable as IAnalyticModelStoryFilterVariable | IAnalyticModelFilterVariable | IDataEntityParameter,
        model,
        path
      );
    }
  }

  public setupDefaultValue(
    variable: (IAnalyticModelVariableManualInput & AllQueryParameter) | IDataEntityParameter,
    defaultValue: IAnalyticModelDefaultValueType,
    model: sap.ui.model.json.JSONModel,
    path: string,
    isUsedForUiParameters: boolean = false /** This flag is getting true when setUiParameterProperties function is called to update value property instead of defaultValue */
  ) {
    if (this.isFilterOrStoryFilterVariable(variable)) {
      if (variable.selectionType === AnalyticModelVariableSelectionType.MULTIPLE_SINGLE) {
        if ((defaultValue as AnalyticModelParameterValueBaseType[])?.length > 0) {
          const defaultValuesWithState = (defaultValue as AnalyticModelParameterValueBaseType[]).map((value) => ({
            value,
            valueStateText: this.getDefaultSingleValueValidation(value),
          }));
          if ("defaultValuesWithState" in variable && variable.defaultValuesWithState) {
            model.setProperty(`${path}/defaultValuesWithState`, variable.defaultValuesWithState);
          } else {
            model.setProperty(`${path}/defaultValuesWithState`, defaultValuesWithState);
          }
        } else {
          const initialState = [{ value: "", valueStateText: "" }];
          model.setProperty(`${path}/defaultValuesWithState`, initialState);
        }
      } else if (
        this.featureflags["DWCO_MODELING_AM_MULTI_RANGE"] &&
        variable.selectionType === AnalyticModelVariableSelectionType.RANGE &&
        variable.multipleSelections
      ) {
        const defaultValueRange = defaultValue as IAnalyticModelDefaultRange[];
        let defaultValueRangeWithState: DefaultValueRange[] = [
          { lowValue: "", option: AnalyticModelDefaultRangeOption.EQ, valueStateText: "" },
        ];
        if (defaultValueRange && defaultValueRange.length > 0) {
          defaultValueRangeWithState = defaultValueRange.map((range) => ({
            lowValue: range.lowValue,
            highValue: range.highValue,
            option: range.option,
            valueStateText: this.getValueStateLowHighDefaultValue(range.lowValue, range.highValue),
          }));
        }
        model.setProperty(`${path}/defaultValuesRange`, defaultValueRangeWithState);
      } else if (
        this.featureflags["DWCO_MODELING_AM_MULTI_RANGE"] &&
        variable.selectionType === AnalyticModelVariableSelectionType.INTERVAL
      ) {
        const defaultValuesInterval = defaultValue as IAnalyticModelDefaultInterval;
        let lowValue: AnalyticModelParameterValueBaseType = "";
        let highValue: AnalyticModelParameterValueBaseType = "";
        if (defaultValuesInterval) {
          lowValue = defaultValuesInterval.lowValue;
          highValue = defaultValuesInterval.highValue;
        }

        const defaultValuesIntervalWithState: DefaultValueInterval[] = [
          {
            lowValue,
            highValue,
            option: AnalyticModelDefaultRangeOption.BT,
            valueStateText: this.getValueStateLowHighDefaultValue(lowValue, highValue),
          },
        ];

        if (highValue === undefined) {
          defaultValuesIntervalWithState[0].option = AnalyticModelDefaultRangeOption.EQ;
        }
        model.setProperty(`${path}/defaultValuesInterval`, defaultValuesIntervalWithState);
      } else if (
        variable.selectionType === AnalyticModelVariableSelectionType.RANGE ||
        variable.selectionType === AnalyticModelVariableSelectionType.INTERVAL
      ) {
        if (variable.multipleSelections) {
          defaultValue = defaultValue?.[0];
        }
        const defaultValueRangeOrInterval = defaultValue as IAnalyticModelDefaultRange | IAnalyticModelDefaultInterval;
        model.setProperty(`${path}/defaultValueIntervalLowValue`, defaultValueRangeOrInterval?.lowValue);
        model.setProperty(`${path}/defaultValueIntervalHighValue`, defaultValueRangeOrInterval?.highValue);
      } else {
        model.setProperty(`${path}/${isUsedForUiParameters ? "value" : "defaultValue"}`, defaultValue);
      }
    } else {
      model.setProperty(`${path}/${isUsedForUiParameters ? "value" : "defaultValue"}`, defaultValue);
    }
    this.setupDefaultValueState(variable);
  }

  private setupDefaultValueState(
    variable: (AllQueryParameter & IAnalyticModelVariableManualInput) | IDataEntityParameter
  ) {
    this.uiModel.setProperty("/variableDetails/defaultValueState", undefined);
    if (!(variable as IAnalyticModelVariableManualInput).defaultValue) {
      return;
    }
    if (variable.parameterType === AnalyticModelParameterType.Input) {
      this.updateDefaultSingleValueValidation((variable as IAnalyticModelSourceParameter_MANUALINPUT).defaultValue);
    } else if (variable.parameterType === AnalyticModelParameterType.KeyDate) {
      this.updateDefaultSingleValueValidation((variable as IAnalyticModelKeyDateVariable_MANUALINPUT).defaultValue);
    } else if (variable.parameterType === AnalyticModelParameterType.FiscalVariant) {
      this.updateDefaultSingleValueValidation(
        (variable as IAnalyticModelFiscalVariantVariable_MANUALINPUT).defaultValue
      );
    } else if (this.isFilterOrStoryFilterVariable(variable)) {
      if (variable.selectionType === AnalyticModelVariableSelectionType.RANGE) {
        if (variable.multipleSelections) {
          const range = (
            variable as
              | IAnalyticModelFilterVariableRangeMultiSelection_MANUALINPUT
              | IAnalyticModelStoryFilterVariableRangeMultiSelection_MANUALINPUT
          ).defaultValue;
          const rangeValue = range?.[0];
          this.validateLowHighDefaultValue(rangeValue?.lowValue, rangeValue?.highValue);
        } else {
          const range = (
            variable as
              | IAnalyticModelFilterVariableRangeSingleSelection_MANUALINPUT
              | IAnalyticModelStoryFilterVariableRangeSingleSelection_MANUALINPUT
          ).defaultValue;
          this.validateLowHighDefaultValue(range?.lowValue, range?.highValue);
        }
      } else if (variable.selectionType === AnalyticModelVariableSelectionType.INTERVAL) {
        const interval = (variable as IAnalyticModelVariableManualInput).defaultValue as IAnalyticModelDefaultInterval;
        this.validateLowHighDefaultValue(interval?.lowValue, interval?.highValue);
      } else if (variable.selectionType === AnalyticModelVariableSelectionType.SINGLE) {
        this.updateDefaultSingleValueValidation(
          (
            variable as
              | IAnalyticModelFilterVariableSingleValueSingleSelection_MANUALINPUT
              | IAnalyticModelStoryFilterVariableSingleValueSingleSelection_MANUALINPUT
          ).defaultValue
        );
      }
    }
  }

  private updateDefaultSingleValueValidation(defaultValue: AnalyticModelParameterValueBaseType | undefined) {
    const validation = this.getDefaultSingleValueValidation(defaultValue);
    this.uiModel.setProperty("/variableDetails/defaultValueState", validation);
  }

  private setupVariableSelectionTypeOptions(
    variable: IAnalyticModelStoryFilterVariable | IAnalyticModelFilterVariable | IDataEntityParameter
  ) {
    let singleRangeValue: Array<{ key: string; text: string }> = [];
    let options;
    // deprecated singleRangeValue
    if (variable.selectionType === AnalyticModelVariableSelectionType.RANGE && !variable.multipleSelections) {
      singleRangeValue = [
        {
          key: AnalyticModelUIVariableSelectionType.SINGLE_RANGE,
          text: this.getText("filterVarSelectionTypeSingleRange"),
        },
      ];
    }
    const variableProcessingType = this.uiModel.getProperty("/variableDetails/variableProcessingType");
    const selectedFilterType = variable.selectionType;
    const isStoryFilterOrRestrictedMeasureVariable = isFilterOrRestrictedMeasureVariable(
      this.uiModel.getProperty("/variableDetails")
    );

    if (
      this.featureflags["DWCO_MODELING_AM_DERIVATION_RESTRICTED_AND_FILTER"] &&
      variableProcessingType === AnalyticModelVariableProcessingType.LOOKUP &&
      isStoryFilterOrRestrictedMeasureVariable &&
      (selectedFilterType === AnalyticModelVariableSelectionType.SINGLE ||
        selectedFilterType === AnalyticModelVariableSelectionType.MULTIPLE_SINGLE)
    ) {
      options = [
        { key: AnalyticModelUIVariableSelectionType.SINGLE, text: this.getText("filterVarSelectionTypeSingle") },
        {
          key: AnalyticModelUIVariableSelectionType.MULTIPLE_SINGLE,
          text: this.getText("filterVarSelectionTypeMultiple"),
        },
      ];
    } else if (
      variableProcessingType === AnalyticModelVariableProcessingType.LOOKUP &&
      (selectedFilterType === AnalyticModelVariableSelectionType.RANGE ||
        selectedFilterType === AnalyticModelVariableSelectionType.INTERVAL) &&
      isStoryFilterOrRestrictedMeasureVariable &&
      this.featureflags["DWCO_MODELING_AM_DERIVATION_RESTRICTED_AND_FILTER"]
    ) {
      options = [
        { key: AnalyticModelUIVariableSelectionType.SINGLE, text: this.getText("filterVarSelectionTypeSingle") },
        {
          key: AnalyticModelUIVariableSelectionType.MULTIPLE_SINGLE,
          text: this.getText("filterVarSelectionTypeMultiple"),
        },
        {
          key: selectedFilterType,
          text:
            selectedFilterType === AnalyticModelVariableSelectionType.RANGE
              ? this.getText("filterVarSelectionTypeRange")
              : this.getText("filterVarSelectionTypeInterval"),
        },
      ];
    } else {
      options = [
        { key: AnalyticModelUIVariableSelectionType.SINGLE, text: this.getText("filterVarSelectionTypeSingle") },
        {
          key: AnalyticModelUIVariableSelectionType.MULTIPLE_SINGLE,
          text: this.getText("filterVarSelectionTypeMultiple"),
        }, // updates boolean property multipleSelections
        { key: AnalyticModelUIVariableSelectionType.INTERVAL, text: this.getText("filterVarSelectionTypeInterval") },
        { key: AnalyticModelUIVariableSelectionType.RANGE, text: this.getText("filterVarSelectionTypeRange") },
        ...singleRangeValue,
      ];
    }

    this.uiModel.setProperty("/variableSelectionType", options);
  }

  private setupRangeOptions() {
    let rangeOptionType = [
      { key: AnalyticModelDefaultRangeOption.EQ, text: "=" },
      { key: AnalyticModelDefaultRangeOption.BT, text: "[ ]" },
      { key: AnalyticModelDefaultRangeOption.LE, text: "<=" },
      { key: AnalyticModelDefaultRangeOption.GE, text: ">=" },
      { key: AnalyticModelDefaultRangeOption.NE, text: "≠" },
      { key: AnalyticModelDefaultRangeOption.GT, text: ">" },
      { key: AnalyticModelDefaultRangeOption.LT, text: "<" },
    ];
    const isBooleanVariable = this.uiModel.getProperty("/variableDetails/type") === "cds.Boolean";
    if (isBooleanVariable) {
      rangeOptionType = rangeOptionType.filter((item) =>
        [AnalyticModelDefaultRangeOption.NE, AnalyticModelDefaultRangeOption.EQ].includes(item.key)
      );
    }
    this.uiModel.setProperty("/variableRangeOptionType", rangeOptionType);
  }

  public setupSelectionType(
    variable: IAnalyticModelStoryFilterVariable | IAnalyticModelFilterVariable | IDataEntityParameter,
    model: sap.ui.model.json.JSONModel,
    path: string
  ) {
    this.setupVariableSelectionTypeOptions(variable);

    if (variable.selectionType === AnalyticModelVariableSelectionType.RANGE) {
      let option: AnalyticModelDefaultRangeOption;
      if (variable.multipleSelections) {
        const rangeVariable = variable as
          | IAnalyticModelFilterVariableRangeMultiSelection_MANUALINPUT
          | IAnalyticModelStoryFilterVariableRangeMultiSelection_MANUALINPUT;
        option = rangeVariable.defaultValue?.[0]?.option;
      } else {
        const rangeVariable = variable as
          | IAnalyticModelFilterVariableRangeSingleSelection_MANUALINPUT
          | IAnalyticModelStoryFilterVariableRangeSingleSelection_MANUALINPUT;
        option = rangeVariable.defaultValue?.option;
      }
      if (!this.featureflags["DWCO_MODELING_AM_MULTI_RANGE"] || !variable.multipleSelections) {
        this.setupRangeOptions();
        model.setProperty(`${path}/variableDefaultRangeOption`, option);
      }
    }

    let selectionType: string = variable.selectionType;
    if (variable.selectionType === AnalyticModelVariableSelectionType.RANGE && !variable.multipleSelections) {
      // Translate single range values to be UI compatible
      selectionType = AnalyticModelUIVariableSelectionType.SINGLE_RANGE;
    }

    model.setProperty(`${path}/selectionType`, selectionType);
    model.setProperty(`${path}/multipleSelections`, variable.multipleSelections);
  }

  private getDefaultSingleValueValidation(defaultValue: AnalyticModelParameterValueBaseType | undefined) {
    const defaultValueType = this.uiModel.getProperty("/variableDetails/type");
    if (!defaultValueType || this.isDefaultValueEmpty(defaultValue)) {
      return undefined;
    }

    let errorString: string | undefined;
    switch (defaultValueType) {
      case "cds.Integer":
      case "cds.Integer64":
      case "cds.hana.SMALLINT":
      case "cds.hana.TINYINT":
        // if value is not floating
        if (isNaN(defaultValue as number) || defaultValue.toString().includes(".")) {
          errorString = this.getText("variableIntegerErrorMsg", [defaultValueType]);
        }
        break;
      case "cds.Decimal":
        let precisionError: boolean;
        let scaleError: boolean;
        const scale = this.uiModel.getProperty("/variableDetails/scale");
        const precision = this.uiModel.getProperty("/variableDetails/precision");
        const precisionInput = defaultValue.toString().split(".")[0];
        const scaleInput = defaultValue.toString().split(".")[1];
        if (precisionInput) {
          precisionError = precisionInput.length > precision;
        }
        if (scaleInput && scale) {
          scaleError = scaleInput.length > scale;
        }
        errorString =
          !precisionError && !scaleError ? undefined : this.getText("variableDecimalErrorMsg", [precision, scale]);
        break;
      case "cds.String":
      case "cds.Binary":
      case "dwc.Principal":
      case "dwc.PrincipalType":
      case "cds.hana.BINARY":
        const length = this.uiModel.getProperty("/variableDetails/length");
        if (length) {
          errorString =
            (defaultValue as string).length > length ? this.getText("variableStringErrorMsg", [length]) : undefined;
        }
        break;
      case "cds.Date":
        if (defaultValue) {
          const isValid = this.validateDate(defaultValue);
          errorString = isValid ? undefined : this.getText("defaultValueValidationError", [defaultValueType]);
        }
        break;
      case "cds.DateTime":
      case "cds.Timestamp":
        if (defaultValue) {
          const date = defaultValue.toString().split(" ")[0];
          const time = defaultValue.toString().split(" ")[1];
          if (this.validateDate(date) && this.validateTime(time)) {
            errorString = undefined;
          } else {
            errorString = this.getText("defaultValueValidationError", [defaultValueType]);
          }
        }
        break;
      default:
        break;
    }

    return errorString;
  }

  private validateDate(sParamValue) {
    return moment(sParamValue, "YYYY-MM-DD", true).isValid();
  }

  private validateTime(time: string) {
    if (!time) {
      return false;
    }
    const [iHours, iMinute] = time.split(/[:]/);
    if (Number(iHours) > 24 || Number(iMinute) > 60) {
      return false;
    }
    return true;
  }

  private validateLowHighDefaultValue(
    lowValue: AnalyticModelParameterValueBaseType | undefined,
    highValue: AnalyticModelParameterValueBaseType | undefined
  ) {
    if (this.featureflags["DWCO_MODELING_AM_MULTI_RANGE"]) {
      return;
    }
    const currentValueState = this.getValueStateLowHighDefaultValue(lowValue, highValue);
    this.uiModel.setProperty("/variableDetails/defaultValueState", currentValueState);
  }

  public getValueStateLowHighDefaultValue(
    lowValue: AnalyticModelParameterValueBaseType | undefined,
    highValue: AnalyticModelParameterValueBaseType | undefined
  ) {
    let currentValueState: string | undefined;
    const updateCurrentValue = (newState: string) => {
      if (!currentValueState) {
        currentValueState = newState;
      }
    };

    if (lowValue) {
      updateCurrentValue(this.getDefaultSingleValueValidation(lowValue));
    }
    if (highValue) {
      updateCurrentValue(this.getDefaultSingleValueValidation(highValue));
    }
    if (!this.isDefaultValueEmpty(lowValue) && !this.isDefaultValueEmpty(highValue) && lowValue > highValue) {
      updateCurrentValue(this.getText("rangeErrorMsg"));
    }
    return currentValueState;
  }

  public isFilterOrStoryFilterVariable(
    variable: AllQueryParameter | IDataEntityParameter
  ): variable is AllQueryParameter & (IAnalyticModelFilterVariable | IAnalyticModelStoryFilterVariable) {
    return (
      variable.parameterType === AnalyticModelParameterType.Filter ||
      variable.parameterType === AnalyticModelParameterType.StoryFilter
    );
  }

  private isDefaultValueEmpty(defaultValue: AnalyticModelParameterValueBaseType | undefined) {
    return defaultValue === "" || defaultValue === undefined;
  }

  public computeDefaultValueForVariable(
    variableDetails: any,
    baseTypeDefaultValue: AnalyticModelParameterValueBaseType
  ) {
    const parameterType = variableDetails.parameterType as AnalyticModelParameterType;
    let defaultValue: IAnalyticModelVariableManualInput["defaultValue"];
    if (
      parameterType === AnalyticModelParameterType.Input ||
      parameterType === AnalyticModelParameterType.KeyDate ||
      parameterType === AnalyticModelParameterType.FiscalVariant
    ) {
      defaultValue = this.parseDefaultValue(baseTypeDefaultValue);
    } else if (
      parameterType === AnalyticModelParameterType.Filter ||
      parameterType === AnalyticModelParameterType.StoryFilter
    ) {
      const selectionType = variableDetails.selectionType as AnalyticModelUIVariableSelectionType;
      const multipleSelections = variableDetails.multipleSelections as boolean;
      if (
        selectionType === AnalyticModelUIVariableSelectionType.SINGLE ||
        selectionType === AnalyticModelUIVariableSelectionType.MULTIPLE_SINGLE
      ) {
        defaultValue = this.parseDefaultValue(baseTypeDefaultValue);
        if (selectionType === AnalyticModelUIVariableSelectionType.MULTIPLE_SINGLE) {
          defaultValue = (variableDetails.defaultValuesWithState as MultiInputItem[])?.map((val) => val.value);
          if (defaultValue?.length === 1 && defaultValue[0] === "") {
            defaultValue = [];
          }
        }
      } else if (selectionType === AnalyticModelUIVariableSelectionType.INTERVAL) {
        if (this.featureflags["DWCO_MODELING_AM_MULTI_RANGE"]) {
          const interval: DefaultValueInterval = variableDetails.defaultValuesInterval[0];
          const lowValue = this.parseDefaultValue(interval.lowValue) ?? "";
          const highValue = this.parseDefaultValue(interval.highValue) ?? "";
          defaultValue = { lowValue, highValue };
          if (interval.option === AnalyticModelDefaultRangeOption.EQ) {
            delete (defaultValue as IAnalyticModelDefaultInterval).highValue;
          }
        } else {
          const lowValue = this.parseDefaultValue(variableDetails.defaultValueIntervalLowValue) ?? "";
          const highValue = this.parseDefaultValue(variableDetails.defaultValueIntervalHighValue) ?? "";
          if (!this.isDefaultValueEmpty(lowValue) || !this.isDefaultValueEmpty(highValue)) {
            defaultValue = { lowValue, highValue };
          } else {
            defaultValue = undefined;
          }
        }
      } else if (
        selectionType === AnalyticModelUIVariableSelectionType.RANGE ||
        selectionType === AnalyticModelUIVariableSelectionType.SINGLE_RANGE
      ) {
        if (this.featureflags["DWCO_MODELING_AM_MULTI_RANGE"] && multipleSelections) {
          const defaultValuesRange = variableDetails.defaultValuesRange as DefaultValueRange[];
          if (defaultValuesRange && defaultValuesRange.length > 0) {
            defaultValue = defaultValuesRange.map((range) => {
              const rangeOption = range.option;
              const lowValue = this.parseDefaultValue(range.lowValue) ?? "";
              const highValue = this.parseDefaultValue(range.highValue) ?? "";
              return this.createDefaultValueForRangeVariable(rangeOption, lowValue, highValue);
            });
          }
        } else {
          const rangeOption = variableDetails.variableDefaultRangeOption as AnalyticModelDefaultRangeOption;
          const lowValue = this.parseDefaultValue(variableDetails.defaultValueIntervalLowValue) ?? "";
          const highValue = this.parseDefaultValue(variableDetails.defaultValueIntervalHighValue) ?? "";
          defaultValue = this.createDefaultValueForRangeVariable(rangeOption, lowValue, highValue);
          if (multipleSelections) {
            defaultValue = [defaultValue];
          }
        }
      }
    }

    return defaultValue;
  }

  private createDefaultValueForRangeVariable(
    rangeOption: AnalyticModelDefaultRangeOption,
    lowValue: AnalyticModelParameterValueBaseType,
    highValue?: AnalyticModelParameterValueBaseType
  ): IAnalyticModelDefaultRange {
    if (rangeOption === AnalyticModelDefaultRangeOption.BT) {
      return {
        lowValue: lowValue,
        highValue: highValue,
        sign: AnalyticModelDefaultRangeSign.INCLUDE,
        option: rangeOption,
      };
    } else {
      return {
        lowValue: lowValue,
        sign: AnalyticModelDefaultRangeSign.INCLUDE,
        option: rangeOption,
      };
    }
  }

  private parseDefaultValue(defaultValue: AnalyticModelParameterValueBaseType | undefined) {
    return !this.isDefaultValueEmpty(defaultValue) ? defaultValue : undefined;
  }
}
