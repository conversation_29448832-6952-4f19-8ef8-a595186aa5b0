/** @format */

import {
  AnalyticModelCrossCalculationType,
  AnalyticModelMeasureType,
  AnalyticModelParameterType,
  AnalyticModelSourceParameterMappingType,
  AnalyticModelVariableProcessingType,
  AnalyticModelVariableSelectionType,
  IAnalyticModelSourceParameterMapping_VARIABLE,
} from "../../../../../shared/queryBuilder/AnalyticModel";
import {
  IDataEntityCrossCalculation,
  IDataEntityMeasure,
  IDataEntityParameter,
} from "../../../../../shared/queryBuilder/DataEntityDetails";
import {
  AnalyticsMeasureValues,
  CONSTANT_VALUE_TYPES,
  IAnalyticModelDefaultValueType,
} from "../../../../../shared/queryBuilder/QueryModel";
import { AnalyticModelBaseClass } from "../../controller/AnalyticModelBase.controller";
import CubeBuilderObjects from "../../utility/CubeBuilderObjects";
import {
  NodeParameter,
  NodeParameters,
  TakeOverDialogParameter,
  parameterActionType,
} from "../../utility/CubeBuilderTypes";
import { AnalyticModelUIVariableSelectionType, PARAMETER_SEGMENT_SELECTED_KEY } from "../../utility/Enum";
import Formatter from "../../utility/Formatter";
import { getDefaultValue } from "../../utility/ModelUtils";
import { runtimeCsnVariables } from "../odataEditor/RuntimeCsnVariables";
import { ValueDefinitionUtils } from "./ValueDefinitionUtils";

export interface VariablesUsageResult {
  [id: string]: NodeParameters;
}
export class StackedVariable extends ValueDefinitionUtils {
  constructor(private _controller: AnalyticModelBaseClass) {
    super();
  }

  /**
   * Recalculate /activeParameters and /inactiveParameters bindings then set /stackedParameters with updated /activeParameters.
   * Reset /selectedParameterSegmentKey to active parameter list.
   */
  public setupNodeVariablesList() {
    this.recalculateParametersListsBindings();

    this.uiModel.setProperty("/stackedParameters", this.uiModel.getProperty("/activeParameters"));
    this.uiModel.setProperty("/selectedParameterSegmentKey", PARAMETER_SEGMENT_SELECTED_KEY.ACTIVE_PARAMETER_LIST);
  }

  private recalculateParametersListsBindings() {
    this.setupActiveVariablesList();
    this.setupInactiveVariablesList();
  }

  /**
   * Setup /activeParameters binding with the parameters that are used in measures and cross calculations.
   */
  private setupActiveVariablesList() {
    const parameters: NodeParameters = this.uiModel.getProperty("/parameters");
    const dataEntityMeasures = this.getDataEntityMeasures();
    // Gets variables used by Measures first
    const variablesUsedByMeasures = this.getVariablesUsedInMeasure(dataEntityMeasures, parameters);
    const factSourceMeasuresNames = this.getFactSourceMeasureNamesInModel();
    const activeParamsArray: NodeParameters = this.getActiveParameters(
      parameters,
      variablesUsedByMeasures,
      factSourceMeasuresNames
    );
    if (this.featureflags["DWCO_MODELING_AM_MULTI_STRUCTURE"]) {
      const crossCalculationKeys = this.getFactSourceCrossCalculationKeys();
      // Then adds missing variables used by Cross Calculations
      this.addVariablesUsedByCrossCalculations(
        activeParamsArray,
        parameters,
        this.getDataEntityCrossCalculations(),
        crossCalculationKeys
      );
    }
    this.uiModel.setProperty("/activeParameters", activeParamsArray);
  }

  /**
   * Sets up /activeParameters with variables used in Copy Property dialog.
   * This is used to determine visibility of variables in the Copy Property dialog.
   */
  public setupActiveVariableListForTakeOverDialog(
    dataEntityMeasures: IDataEntityMeasure[],
    parameters: NodeParameters,
    dataEntityCrossCalculations?: IDataEntityCrossCalculation[]
  ) {
    // Gets variables used by Measures first
    const variablesUsedByMeasures = this.getVariablesUsedInMeasure(dataEntityMeasures, parameters);
    const dataEntityMeasuresKeys = this.getNotHiddenDateEntityMeasureNames(dataEntityMeasures);
    const activeParamsArray: NodeParameters = this.getActiveParameters(
      parameters,
      variablesUsedByMeasures,
      dataEntityMeasuresKeys
    );
    if (this.featureflags["DWCO_MODELING_AM_MULTI_STRUCTURE"]) {
      const crossCalculationKeys = this.getNotHiddenDateEntityCrossCalculationKeys(dataEntityCrossCalculations);
      // Then adds missing variables used by Cross Calculations
      this.addVariablesUsedByCrossCalculations(
        activeParamsArray,
        parameters,
        dataEntityCrossCalculations,
        crossCalculationKeys
      );
    }
    this.uiModel.setProperty("/activeParameters", activeParamsArray);
  }

  private getDataEntityMeasures(): IDataEntityMeasure[] {
    return (
      this.uiModel.getProperty("/allSourceDataEntityDetails")[this.model.getFirstFactSource().dataEntity.key]
        ?.measures ?? []
    );
  }

  /**
   * Setup /inactiveParameters binding with the parameters that are not in /activeParameters.
   */
  private setupInactiveVariablesList() {
    const unusedVariables = this.getUnusedVariables();
    this.uiModel.setProperty("/inactiveParameters", unusedVariables);
  }

  /**
   * Returns the /parameters that are not in /activeParameters with updated description.
   */
  private getUnusedVariables(): NodeParameters {
    const parameters: NodeParameters = this.uiModel.getProperty("/parameters");
    const activeParamsArray: NodeParameters = this.uiModel.getProperty("/activeParameters");

    const unusedVariables: NodeParameters = [];
    for (const param of parameters) {
      if (!activeParamsArray.some((activeParam) => activeParam.key === param.key)) {
        param.parameterDescription = this.getText("txtnotused");
        unusedVariables.push(param);
      }
    }
    return unusedVariables;
  }

  /**
   * find the variables that is used in lookup entity
   * @param variablesUsedInMeasure
   * @param runtimeVariables
   * @returns
   */
  private getVariablesUsedInLookupEntity(
    variables: NodeParameters,
    runtimeVariables?: NodeParameters | any[]
  ): VariablesUsageResult {
    const lookupEntityParams = new Map<string, any>();

    variables.forEach((param) => {
      if (param?.parameterBinding) {
        Object.values(param.parameterBinding).forEach((value) => {
          if (value?.mappingType === AnalyticModelSourceParameterMappingType.MapToSourceParameter) {
            const variable = runtimeVariables.find((v) => v.key === value.variableName);
            if (variable) {
              lookupEntityParams.set(variable.key, variable);
            }
          }
        });
      }
    });

    if (lookupEntityParams.size > 0) {
      const newVariables = this.getVariablesUsedInLookupEntity(
        Array.from(lookupEntityParams.values()),
        runtimeVariables
      );
      Object.keys(newVariables).forEach((key) => {
        lookupEntityParams.set(key, newVariables[key]);
      });
    }

    return Object.fromEntries(lookupEntityParams);
  }

  /**
   * this method will get the active parameters
   * @param parameters
   * @param variablesUsedInMeasure
   * @returns
   */
  private getActiveParameters(
    parameters: NodeParameters | any[],
    variablesUsedInMeasure: VariablesUsageResult,
    factSourceMeasuresNames: Set<string>
  ) {
    const activeParamsArray: NodeParameters = [];
    parameters.forEach((param: any) => {
      if (
        !param.parameterType ||
        (param.parameterType === AnalyticModelParameterType.Input && param.sourceMappingType) ||
        [
          AnalyticModelParameterType.StoryFilter,
          AnalyticModelParameterType.KeyDate,
          AnalyticModelParameterType.FiscalVariant,
        ].includes(param.parameterType)
      ) {
        activeParamsArray.push(param);
      }
    });

    this.mergeLookupEntityParameters(activeParamsArray, parameters);
    Object.keys(variablesUsedInMeasure ?? {}).forEach((key) => {
      const measureIsInModel = factSourceMeasuresNames.has(key);
      variablesUsedInMeasure[key].forEach((param) => {
        if (
          this.isParameterUsedByMeasures(measureIsInModel, param, key, variablesUsedInMeasure, factSourceMeasuresNames)
        ) {
          // if the parameter is not a stacked variable, then get the parameter description
          if (param.isStackedVariable !== true) {
            param.parameterDescription = this._controller.getParameterMappingDescription(
              param.parameterMappingType?.[param.key],
              param.selectionType
            );
          }
          if (!activeParamsArray.find((activeParam) => activeParam.key === param.key)) {
            activeParamsArray.push(param);
          }
        }
      });
    });
    return activeParamsArray;
  }

  private isParameterUsedInAnotherNotHiddenMeasure(
    paramKey: string,
    currentMeasureKey: string,
    variablesUsedInMeasure: VariablesUsageResult,
    factSourceMeasuresNames: Set<string>
  ): boolean {
    return Object.keys(variablesUsedInMeasure ?? {}).some((key) => {
      const variables: NodeParameters = variablesUsedInMeasure[key];
      return variables.some(
        (variable) => variable.key === paramKey && key !== currentMeasureKey && factSourceMeasuresNames.has(key)
      );
    });
  }

  /**
   * Checks if a given parameter is used in any measure, hidden measure or has mapping
   */
  private isParameterUsedByMeasures(
    measureIsInModel: boolean,
    param: NodeParameter,
    currentMeasureKey: string,
    variablesUsedInMeasure: VariablesUsageResult,
    factSourceMeasuresNames: Set<string>
  ): boolean {
    // Determine Mappings to fact sources
    const factSources = this.model.getFactSources();
    let hasMapping = false;
    Object.entries(factSources ?? {}).forEach(([_, value]) => {
      if (
        "parameterMappings" in value &&
        Object.entries(value.parameterMappings ?? {}).some(
          ([, value]) =>
            (value as IAnalyticModelSourceParameterMapping_VARIABLE).variableName === param.key &&
            value.mappingType === AnalyticModelSourceParameterMappingType.MapToSourceParameter
        )
      ) {
        hasMapping = true;
      }
    });
    // Check if the parameter is used in another not hidden measure
    const paramUsedInNotHiddenMeasure = this.isParameterUsedInAnotherNotHiddenMeasure(
      param.key,
      currentMeasureKey,
      variablesUsedInMeasure,
      factSourceMeasuresNames
    );
    return measureIsInModel || hasMapping || paramUsedInNotHiddenMeasure;
  }

  private getFactSourceMeasureNamesInModel(): Set<string> {
    const measures = this.model.getMeasures();
    const factSourceMeasuresNames = Object.values(measures).reduce((acc, measure) => {
      if (measure.measureType === AnalyticModelMeasureType.FactSourceMeasure) {
        acc.add(measure.key);
      }
      return acc;
    }, new Set<string>());
    return factSourceMeasuresNames;
  }

  /**
   * Retrieves the names of measures that are not marked as hidden.
   * This is used to determine visibility of variables in the Copy Property dialog.
   * @param dataEntityMeasures List of data entity measures to filter.
   * @returns A set of measure keys that are not hidden.
   */
  private getNotHiddenDateEntityMeasureNames(dataEntityMeasures: IDataEntityMeasure[] = []): Set<string> {
    return new Set(dataEntityMeasures.filter((measure) => measure?.isHidden === false).map((measure) => measure.key));
  }

  /**
   * find the variables that is used in stacked measures
   * @param clickedMeasure
   * @param allMeasures
   * @param parameters
   * @returns
   */
  private getVariablesUsedInMeasure(dataEntityMeasures: IDataEntityMeasure[], parameters: NodeParameters | any[]) {
    const result: VariablesUsageResult = (dataEntityMeasures || []).reduce((acc, measure) => {
      const { elements = [], key, measureType } = measure;

      if (measureType === AnalyticsMeasureValues.RESTRICTION || measureType === AnalyticsMeasureValues.CALCULATION) {
        const filteredParams = parameters.filter((param) => elements.includes(param.key));
        if (filteredParams.length > 0) {
          // get the lookup entity parameters and add them to the result
          this.mergeLookupEntityParameters(filteredParams, parameters);
          acc[key] = filteredParams;
        }
      }

      return acc;
    }, {});

    return result;
  }

  private mergeLookupEntityParameters(activeParameters: any[], parameters: any[] | NodeParameters) {
    const lookupEntityParams = this.getVariablesUsedInLookupEntity(activeParameters, parameters);
    Object.keys(lookupEntityParams ?? {}).forEach((key) => {
      if (activeParameters.every((param) => param.key !== key)) {
        activeParameters.push(lookupEntityParams[key]);
      }
    });
  }

  /**
   * refresh the UI parameters based on the segment selection
   * @param selection
   */
  public refreshUiParametersBySegmentSelection(selection: any) {
    let parameters: NodeParameters;
    switch (selection) {
      case PARAMETER_SEGMENT_SELECTED_KEY.ACTIVE_PARAMETER_LIST:
        parameters = this.uiModel.getProperty("/activeParameters");
        break;
      case PARAMETER_SEGMENT_SELECTED_KEY.IN_ACTIVE_PARAMETER_LIST:
        parameters = this.uiModel.getProperty("/inactiveParameters");
        break;
      default:
        parameters = this.uiModel.getProperty("/parameters");
    }
    /** setting the current value of "/stackedParameters" by selectedKey of segmentButton to show parameters in active/inactive List*/
    this.uiModel.setProperty("/stackedParameters", parameters);
  }

  /**
   * this function is getting the stacked variables to setting up into ui in the output structure variable section
   */
  public getStackedVariables() {
    const variableModel = [];
    const constantParameters = new Set<string>();
    const allSourceDataEntityDetails = this.uiModel.getProperty("/allSourceDataEntityDetails") ?? {};
    const factSources = this.model.getSourceModel()?.factSources;
    Object.entries(factSources ?? {}).forEach(([factSourceKey, factSource]) => {
      if (factSource.parameterMappings) {
        Object.keys(factSource.parameterMappings ?? {}).forEach((key) => {
          if (CubeBuilderObjects.ConstantMappingTypes.includes(factSource.parameterMappings[key].mappingType)) {
            constantParameters.add(key);
          }
        });
      }
      if (factSource.dataEntity) {
        if (!!allSourceDataEntityDetails?.[factSource.dataEntity.key]) {
          const {
            measures: dataEntityMeasures,
            parameters: dataEntityParameters,
            csn: sourceModelCsn,
            crossCalculations: dataEntityCrossCalculations,
          } = allSourceDataEntityDetails?.[factSource.dataEntity.key];
          const isAnalyticModel = (sourceModelCsn?.["@ObjectModel.modelingPattern"]?.["#"] ?? "") === "ANALYTICAL_CUBE";
          if (!isAnalyticModel) {
            return;
          }
          const runtimeVariables = runtimeCsnVariables.getStackedVariables(sourceModelCsn);
          const nonConstantRuntimeVariables = runtimeVariables
            .filter((param) => !constantParameters.has(param.key))
            .map((param) => ({ ...param }));
          // get active parameter list
          const result = this.getVariablesUsedInMeasure(dataEntityMeasures, nonConstantRuntimeVariables);
          const factSourceMeasuresNames = this.getFactSourceMeasureNamesInModel();
          const activeParamsArray: NodeParameters = this.getActiveParameters(
            dataEntityParameters,
            result,
            factSourceMeasuresNames
          );

          const crossCalculationKeys = this.getFactSourceCrossCalculationKeys();
          // Manipulates activeParamsArray inside the function by adding the given variables used by the given cross calculations
          this.addVariablesUsedByCrossCalculations(
            activeParamsArray,
            nonConstantRuntimeVariables,
            dataEntityCrossCalculations || [],
            crossCalculationKeys
          );

          const matchedParams = nonConstantRuntimeVariables.filter((param) =>
            activeParamsArray.some((p) => p.key === param.key)
          );
          // set Object Key of FactSource in Source Model to stacked variables
          matchedParams.forEach((param) => {
            param.sourceKey = factSourceKey;
          });

          variableModel.push(...matchedParams);
        }
      }
    });
    return variableModel;
  }

  public computeMappingType(
    parameterModel: TakeOverDialogParameter | IDataEntityParameter
  ): AnalyticModelSourceParameterMappingType {
    const { parameterType, selectionType, multipleSelections } = parameterModel;
    if (
      parameterType === AnalyticModelParameterType.Input ||
      parameterType === AnalyticModelParameterType.KeyDate ||
      parameterType === AnalyticModelParameterType.FiscalVariant
    ) {
      return AnalyticModelSourceParameterMappingType.ConstantValue;
    }

    if (
      parameterType === AnalyticModelParameterType.Filter ||
      parameterType === AnalyticModelParameterType.StoryFilter
    ) {
      switch (selectionType as any) {
        case AnalyticModelVariableSelectionType.SINGLE:
          return AnalyticModelSourceParameterMappingType.ConstantValue;
        case AnalyticModelVariableSelectionType.MULTIPLE_SINGLE:
          return AnalyticModelSourceParameterMappingType.ConstantValues;
        case AnalyticModelVariableSelectionType.INTERVAL:
          return AnalyticModelSourceParameterMappingType.ConstantInterval;
        case AnalyticModelVariableSelectionType.RANGE:
          return multipleSelections
            ? AnalyticModelSourceParameterMappingType.ConstantRanges
            : AnalyticModelSourceParameterMappingType.ConstantRange;
      }
    }
  }

  public updateStackedVariableFilterType(newValue, selectedObject) {
    switch (newValue) {
      case AnalyticModelVariableSelectionType.MULTIPLE_SINGLE:
        selectedObject.multipleSelections = true;
        selectedObject.selectionType = AnalyticModelVariableSelectionType.MULTIPLE_SINGLE;
        break;
      case AnalyticModelUIVariableSelectionType.SINGLE_RANGE:
        selectedObject.multipleSelections = false;
        selectedObject.selectionType = AnalyticModelVariableSelectionType.RANGE;
        break;
      case AnalyticModelVariableSelectionType.RANGE:
        selectedObject.multipleSelections = true;
        selectedObject.selectionType = AnalyticModelVariableSelectionType.RANGE;
        break;
      default:
        selectedObject.multipleSelections = false;
        selectedObject.selectionType = newValue;
    }
    this.updateDefaultValueByFilterTypeChange(selectedObject);
    this.setupInputConfigFromStackedVariable(selectedObject, new sap.ui.model.json.JSONModel(selectedObject), "");
    // Update the action type to reflect the correct input for the selected filter type.
    // This ensures the action type key is updated to match the new filter type.
    if (selectedObject.actionType) {
      selectedObject.actionType = (selectedObject.actionType as parameterActionType[]).filter(
        (action: parameterActionType) => action.type !== selectedObject.selectedType
      );
    }
    selectedObject.selectedType = this.computeMappingType(selectedObject);
    // update the action type key by new filter type
    if (selectedObject.actionType) {
      (selectedObject.actionType as parameterActionType[]).push({
        type: selectedObject.selectedType,
        text: "txtSetValue",
      });
    }
  }

  private updateDefaultValueByFilterTypeChange(selectedObject: any) {
    const parameters = (this.uiModel.getProperty("/parameters") as NodeParameters) ?? [];
    const param = parameters?.find((param: NodeParameter) => param.key === selectedObject.key);
    if (selectedObject.selectionType === AnalyticModelVariableSelectionType.SINGLE) {
      selectedObject.defaultValue = param && param.value && !Array.isArray(param.value) ? param.value : undefined;
    } else if (
      selectedObject.selectionType === AnalyticModelVariableSelectionType.RANGE &&
      "defaultValuesRange" in selectedObject &&
      selectedObject.defaultValuesRange
    ) {
      selectedObject.defaultValue = selectedObject.defaultValuesRange;
    } else if (
      selectedObject.selectionType === AnalyticModelVariableSelectionType.MULTIPLE_SINGLE &&
      "defaultValuesWithState" in selectedObject &&
      selectedObject.defaultValuesWithState
    ) {
      selectedObject.defaultValue = selectedObject.defaultValuesWithState;
    } else if (
      selectedObject.selectionType === AnalyticModelVariableSelectionType.INTERVAL &&
      "defaultValuesInterval" in selectedObject &&
      selectedObject.defaultValuesInterval
    ) {
      selectedObject.defaultValue = Array.isArray(selectedObject.defaultValuesInterval)
        ? selectedObject.defaultValuesInterval[0]
        : selectedObject.defaultValuesInterval;
    } else {
      // reset the default value in case of no default value in ui parameters
      selectedObject.defaultValue = getDefaultValue(selectedObject);
    }
  }

  /**
   * This function is used to enable or disable the Set Value button in the Set Value dialog
   * It calls the formatter function to check if the default value is valid based on each parameter type and selection type
   * @param parameterEntity
   * @param defaultValue
   * @returns
   */
  public enablementOfSetValueButton(parameterEntity: any, defaultValue: IAnalyticModelDefaultValueType) {
    const enableSetParameterDefaultValue = Formatter.variableDefaultValueVisibleFormatter(
      parameterEntity.parameterType,
      parameterEntity.selectionType,
      defaultValue,
      parameterEntity.variableProcessingType
    );
    return enableSetParameterDefaultValue;
  }

  public updateSelectedObjectValue(selectedObject: any, parameterModel: sap.ui.model.json.JSONModel) {
    const defaultValue = this.computeDefaultValueForVariable(selectedObject, selectedObject.defaultValue);
    selectedObject.value = defaultValue ?? selectedObject.defaultValue;
    // Validate the enablement of the OK button in the Set Value dialog by verifying the default value for each parameter type
    selectedObject.enableSetParameterDefaultValue = this.enablementOfSetValueButton(
      selectedObject,
      selectedObject.value
    );
    parameterModel.updateBindings(true);
  }

  // #region Cross calculation related functions
  /**
   * Manipulates the activeParamsArray by adding the variables used by cross calculations
   * @param activeParamsArray Array to be manipulated
   * @param variables
   * @param crossCalculations
   */
  private addVariablesUsedByCrossCalculations(
    activeParamsArray: NodeParameters,
    variables: NodeParameters,
    crossCalculations: IDataEntityCrossCalculation[],
    crossCalculationKeys: Set<string>
  ): void {
    // Returns an object with cross calculation keys as keys and the variables used in the cross calculation as values
    const variablesUsedInCrossCalculations = this.getVariablesUsedInCrossCalculations(crossCalculations, variables);
    for (const crossCalculationKey in variablesUsedInCrossCalculations) {
      const variables = variablesUsedInCrossCalculations[crossCalculationKey];

      const isCrossCalculationInFactSource = crossCalculationKeys.has(crossCalculationKey);

      variables?.forEach((variable) => {
        if (this.shouldAddToActiveList(isCrossCalculationInFactSource, variable)) {
          if (!variable.isStackedVariable) {
            this.updateVariableDescription(variable);
          }
          if (!activeParamsArray.find((activeParam) => activeParam.key === variable.key)) {
            activeParamsArray.push(variable);
          }
        }
      });
    }
  }

  private getVariablesUsedInCrossCalculations(
    crossCalculations: IDataEntityCrossCalculation[],
    variables: NodeParameters
  ): VariablesUsageResult {
    const variablesInUse: VariablesUsageResult = {};
    for (const crossCalculation of crossCalculations) {
      const filteredParams = variables.filter((param) => crossCalculation.elements?.includes(param.key));
      if (filteredParams.length > 0) {
        variablesInUse[crossCalculation.key] = filteredParams;
      }
    }
    return variablesInUse;
  }

  private getFactSourceCrossCalculationKeys(): Set<string> {
    const crossCalculations = this.model.getCrossCalculations();
    const crossCalculationsKeys = new Set<string>();

    for (const crossCalculationKey in crossCalculations) {
      const crossCalculation = crossCalculations[crossCalculationKey];
      if (crossCalculation.crossCalculationType === AnalyticModelCrossCalculationType.FactSourceCrossCalculation) {
        crossCalculationsKeys.add(crossCalculationKey);
      }
    }

    return crossCalculationsKeys;
  }

  /**
   * Retrieves the keys of cross calculations that are not marked as hidden.
   * This is used to determine visibility of variables in the Copy Property dialog.
   * @param crossCalculations List of cross calculations to filter.
   * @returns A set of keys for cross calculations that are not hidden.
   */
  private getNotHiddenDateEntityCrossCalculationKeys(
    crossCalculations: IDataEntityCrossCalculation[] = []
  ): Set<string> {
    return new Set(
      crossCalculations
        .filter((crossCalculation) => crossCalculation?.isHidden === false)
        .map((crossCalculation) => crossCalculation.key)
    );
  }

  private shouldAddToActiveList(
    isCrossCalculationInFactSource: boolean,
    variable: NodeParameter
    // variableUsedInCrossCalculationKey: string, // DS00-4102 DW15-6029 not checking this scenario yet
    // variablesUsedInCrossCalculations: VariablesUsageResult // DS00-4102 DW15-6029 not checking this scenario yet
  ): boolean {
    const factSources = this.model.getFactSources();
    let hasMapping = false;
    Object.entries(factSources ?? {}).forEach(([_, factSource]) => {
      if ("parameterMappings" in factSource) {
        for (const parameterMappingKey in factSource.parameterMappings) {
          const parameterMapping = factSource.parameterMappings[parameterMappingKey];
          if (
            "variableName" in parameterMapping &&
            parameterMapping.variableName === variable.key &&
            parameterMapping.mappingType === AnalyticModelSourceParameterMappingType.MapToSourceParameter
          ) {
            hasMapping = true;
            break;
          }
        }
      }
    });

    // DS00-4102 DW15-6029 not checking this scenario yet
    // Check if the parameter is used in another not hidden cross calculation
    // const paramUsedInNotHiddenCrossCalculation = this.isParameterUsedInAnotherNotHiddenCrossCalculation(
    //   param.key,
    //   currentCrossCalculationKey,
    //   variablesUsedInCrossCalculation
    // );

    return isCrossCalculationInFactSource || hasMapping;
  }

  private updateVariableDescription(variable: NodeParameter) {
    const parameterMappingType = variable.parameterMappingType?.[variable.key];
    const selectionType = variable.selectionType;

    variable.parameterDescription = this._controller.getParameterMappingDescription(
      parameterMappingType,
      selectionType
    );
  }

  private getDataEntityCrossCalculations(): IDataEntityCrossCalculation[] {
    const dataEntityCrossCalculations: IDataEntityCrossCalculation[] = [];

    const allSourceDataEntityDetails = this.uiModel.getProperty("/allSourceDataEntityDetails");
    if (!allSourceDataEntityDetails) {
      return [];
    }

    const factSources = this.model.getFactSources();
    for (const factSourceKey in factSources) {
      const factSource = this.model.getFactSource(factSourceKey);
      const factSourceCrossCalculations = allSourceDataEntityDetails[factSource.dataEntity.key]?.crossCalculations;
      if (!factSourceCrossCalculations) {
        continue;
      }
      dataEntityCrossCalculations.push(...factSourceCrossCalculations);
    }

    return dataEntityCrossCalculations;
  }
  // #endregion

  // #region Value help related functions for copy property dialog
  /**
   * Retrieves the parameter values required for value help functionality in copy property dialog.
   * As in copy property dialog we don't have data in model, we need to retrieve the values from the DataEntityParameters
   * This method is essential for value help to work, as it first fetches the underlying model
   * and extracts parameter values based on their mapping types. It checks for constant values,
   * mapped source parameters, and handles stacked models if the relevant feature flag is enabled.
   *
   * @returns An object containing:
   *   - `allParametersHaveValues`: A boolean indicating if all required parameters have values set.
   *   - `parametersWithValues`: A mapping of parameter keys to their resolved values.
   */
  public getParameterValuesForTakeOverDialogValueHelp(parameters: IDataEntityParameter[]) {
    const parametersWithValues = {};
    let allParametersHaveValues = true;
    parameters.forEach((parameter) => {
      if (
        parameter.sourceMappingType &&
        parameter.sourceMappingType === AnalyticModelSourceParameterMappingType.MapToSourceParameter
      ) {
        if (this.isDataEntityParameterHasLookupOrDynamic(parameter)) {
          // for lookup variables, we need to check the parameterBinding and set the value for the source input parameter
          // get the param binding key which is mapped to the current variable key and call the function again to retrieve the value
          allParametersHaveValues = this.extractValuesFromParameterMappingsByDataEntityParameter(
            parameters,
            parameter.parameterBinding ?? {},
            parametersWithValues,
            allParametersHaveValues,
            parameter.key
          );
        } else {
          const defaultValue = parameter?.hasOwnProperty("default");
          if (defaultValue) {
            parametersWithValues[parameter.key] = parameter.default;
          } else {
            allParametersHaveValues = false;
          }
        }
      }
    });
    allParametersHaveValues =
      allParametersHaveValues && this._controller.doesStackedModelFactHaveAllParameterValuesSet(parameters);
    return { allParametersHaveValues, parametersWithValues };
  }

  private extractValuesFromParameterMappingsByDataEntityParameter(
    parameters: IDataEntityParameter[],
    parameterMappings: {
      [parameterName: string]: CONSTANT_VALUE_TYPES | IAnalyticModelSourceParameterMapping_VARIABLE;
    },
    parametersWithValues: {},
    allParametersHaveValues: boolean,
    sourceInputParameterKey?: string
  ): boolean {
    Object.keys(parameterMappings ?? {}).forEach((parameterKey) => {
      const parameter = parameterMappings[parameterKey];
      if (CubeBuilderObjects.ConstantMappingTypes.includes(parameter.mappingType)) {
        if ("constantValue" in parameter) {
          parametersWithValues[sourceInputParameterKey] = parameter.constantValue;
        }
      } else if (parameter.mappingType === AnalyticModelSourceParameterMappingType.MapToSourceParameter) {
        // MapToSourceParameter, requires checking AM variables for value
        const variableName = parameter.variableName;
        const lookUpVariable = parameters.find((param) => param.key === variableName);
        if (this.isDataEntityParameterHasLookupOrDynamic(lookUpVariable)) {
          // for lookup variables, we need to check the parameterBinding and set the value for the source input parameter
          // get the param binding key which is mapped to the current variable key and call the function again to retrieve the value
          allParametersHaveValues = this.extractValuesFromParameterMappingsByDataEntityParameter(
            parameters,
            lookUpVariable.parameterBinding ?? {},
            parametersWithValues,
            allParametersHaveValues,
            sourceInputParameterKey
          );
        } else {
          const defaultValue = lookUpVariable?.hasOwnProperty("default");
          if (defaultValue) {
            parametersWithValues[sourceInputParameterKey] = lookUpVariable.default;
          } else {
            allParametersHaveValues = false;
          }
        }
      }
    });
    return allParametersHaveValues;
  }

  private isDataEntityParameterHasLookupOrDynamic(dataEntityParameter: IDataEntityParameter) {
    return (
      dataEntityParameter.variableProcessingType &&
      (dataEntityParameter.variableProcessingType === AnalyticModelVariableProcessingType.LOOKUP ||
        dataEntityParameter.variableProcessingType === AnalyticModelVariableProcessingType.DYNAMIC_DEFAULT) &&
      dataEntityParameter.parameterBinding &&
      dataEntityParameter.lookupEntity
    );
  }
  // #endregion
}
