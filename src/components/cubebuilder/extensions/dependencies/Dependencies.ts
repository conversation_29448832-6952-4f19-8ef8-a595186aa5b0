/** @format */

import {
  AnalyticModelCalculatedCrossCalculationOperandType,
  AnalyticModelCalculatedMeasureOperandType,
  AnalyticModelConstantSelectionType,
  AnalyticModelConversionTypeType,
  AnalyticModelCrossCalculationType,
  AnalyticModelFilterOperandType,
  AnalyticModelMeasureType,
  AnalyticModelParameterType,
  AnalyticModelReferenceDateType,
  AnalyticModelRestrictedCrossCalculationOperandType,
  AnalyticModelRestrictedMeasureOperandType,
  AnalyticModelSourceType,
  AnalyticModelTargetCurrencyType,
  AnalyticModelTargetUnitType,
  IAnalyticModelAttributeKey,
  IAnalyticModelCurrencyConversionMeasure,
  IAnalyticModelUnitConversionMeasure,
  IAnalyticModelVariableKey,
} from "../../../../../shared/queryBuilder/AnalyticModel";
import { IDataEntity, IDataEntityDetailsResponse } from "../../../../../shared/queryBuilder/DataEntityDetails";
import { User } from "../../../shell/utility/User";
import { ObjectNameDisplay } from "../../../userSettings/utility/Constants";
import { AttributeDetails } from "../../controller/AttributeDetails.controller";
import QueryUIModel from "../../model/QueryUIModel";
import CubeBuilderObjects from "../../utility/CubeBuilderObjects";
import {
  AnalyticModelObjectDependenciesGraph,
  AnalyticModelObjectDependenciesNodeKey,
  MeasureWithExceptionAggregation,
  UsedInItem,
} from "../../utility/CubeBuilderTypes";
import { AnalyticModelObjectType, UsedInType } from "../../utility/Enum";
import Formatter from "../../utility/Formatter";
import {
  getReferenceAttributes,
  getSourcesFromAttribute,
  getVariableBusinessName,
  getVariablesUsingVariable,
  isAttributeUsedInNonForeignKeyAssociation,
  modelHasTimeDependency,
} from "../../utility/ModelUtils";
import Util from "../../utility/Util";
import { ExtensionBase } from "../ExtensionBase";

export class Dependencies extends ExtensionBase {
  constructor(protected model: QueryUIModel) {
    super();
    this.model = model;
  }

  public getDependencyGraph(): AnalyticModelObjectDependenciesGraph {
    const measuresUsedIn = Object.keys(this.model.getMeasures()).reduce((map, key) => {
      map[key] = this.getUsedInListForMeasure(key);
      return map;
    }, {} as { [key: string]: UsedInItem[] });

    const attributesUsedIn = Object.keys(this.model.getAttributes()).reduce((map, key) => {
      map[key] = this.getAttributeUsedIn(key);
      return map;
    }, {} as { [key: string]: UsedInItem[] });

    const variablesUsedIn = Object.keys(this.model.getVariables()).reduce((map, key) => {
      map[key] = this.getUsedInParamList(key);
      return map;
    }, {} as { [key: string]: UsedInItem[] });

    const crossCalculationsUsedIn = Object.keys(this.model.getCrossCalculations()).reduce((map, key) => {
      map[key] = this.getCrossCalculationsUsedIn(key);
      return map;
    }, {} as { [key: string]: UsedInItem[] });

    const nodes: AnalyticModelObjectDependenciesGraph = {};

    const createNewNode = (
      key: string,
      type: AnalyticModelObjectType,
      text: string,
      icon: string,
      subtype?: string
    ) => {
      nodes[this.getObjectNodeKey(key, type)] = {
        technicalName: key,
        businessName: text,
        type,
        subtype: subtype ?? "",
        dependentBy: {},
        dependencies: {},
        icon,
      };
    };

    const createDependenciesFromUsedInList = (
      key: string,
      type: AnalyticModelObjectType,
      text: string,
      icon: string,
      usedInList: UsedInItem[],
      subtype?: string
    ) => {
      if (!nodes[this.getObjectNodeKey(key, type)]) {
        createNewNode(key, type, text, icon, subtype);
      }

      usedInList.forEach((item) => {
        if (item.type === UsedInType.FILTER_FROM_VARIABLE || item.type === UsedInType.TIME_DEPENDENCY) {
          return;
        }

        const objectType = CubeBuilderObjects.UsedInTypeToObjectType[item.type];
        if (!objectType) {
          return;
        }

        let itemTechnicalName = item.technicalName;
        if (objectType === AnalyticModelObjectType.FACT) {
          itemTechnicalName = this.model.getFirstFactSourceKey();
        }

        if (!nodes[this.getObjectNodeKey(itemTechnicalName, objectType)]) {
          createNewNode(
            itemTechnicalName,
            objectType,
            item.businessName,
            Formatter.objectTypeIconFormatter(objectType, item.subType),
            item.subType
          );
        }

        const targetNodeKey = this.getObjectNodeKey(key, type);
        const sourceNodeKey = this.getObjectNodeKey(itemTechnicalName, objectType);

        nodes[targetNodeKey].dependentBy[sourceNodeKey] = true;
        nodes[sourceNodeKey].dependencies[targetNodeKey] = true;
      });
    };

    Object.entries(measuresUsedIn).forEach(([key, usedInList]) => {
      const measure = this.model.getMeasure(key);
      const type = AnalyticModelObjectType.MEASURE;
      const text = measure.text;
      const icon = Formatter.measureTypeIcon(measure.measureType);
      const subtype = measure.measureType;
      createDependenciesFromUsedInList(key, type, text, icon, usedInList, subtype);
    });

    Object.entries(attributesUsedIn).forEach(([key, usedInList]) => {
      const attribute = this.model.getAttribute(key);
      const type = AnalyticModelObjectType.ATTRIBUTE;
      const text = attribute.text;
      const icon = "";
      createDependenciesFromUsedInList(key, type, text, icon, usedInList);
    });

    Object.entries(variablesUsedIn).forEach(([key, usedInList]) => {
      const variable = this.model.getVariable(key);
      const type = AnalyticModelObjectType.VARIABLE;
      let text = "";
      if (variable.parameterType === AnalyticModelParameterType.StoryFilter) {
        const referenceAttribute = this.model.getAttribute(variable.referenceAttribute);
        text = referenceAttribute?.text ?? "";
      } else {
        text = variable.text;
      }
      const icon = "sap-icon://sac/variable";
      createDependenciesFromUsedInList(key, type, text, icon, usedInList);
    });

    Object.entries(crossCalculationsUsedIn).forEach(([key, usedInList]) => {
      const crossCalculation = this.model.getCrossCalculation(key);
      const type = AnalyticModelObjectType.CROSS_CALCULATION;
      const text = crossCalculation.text;
      const icon = Formatter.crossCalculation.typeIcon(crossCalculation.crossCalculationType);
      const subtype = crossCalculation.crossCalculationType;
      createDependenciesFromUsedInList(key, type, text, icon, usedInList, subtype);
    });

    return nodes;
  }

  public getObjectNodeKey(key: string, type: AnalyticModelObjectType): AnalyticModelObjectDependenciesNodeKey {
    return `${type}.${key}`;
  }

  public getObjectTypeAndKeyFromNodeKey(nodeKey: AnalyticModelObjectDependenciesNodeKey): {
    type: AnalyticModelObjectType;
    key: string;
  } {
    const firstDot = nodeKey.indexOf(".");
    const type = nodeKey.substring(0, firstDot) as AnalyticModelObjectType;
    const key = nodeKey.substring(firstDot + 1);
    return { type, key };
  }

  public getUsedInListForMeasure(usingMeasure: string): UsedInItem[] {
    const usedInList: UsedInItem[] = [];
    const measures = this.model.getMeasures();
    measureLoop: for (const measureKey in measures) {
      const measureProperties = measures[measureKey];
      if (
        measureProperties.measureType === AnalyticModelMeasureType.RestrictedMeasure ||
        measureProperties.measureType === AnalyticModelMeasureType.CurrencyConversionMeasure ||
        measureProperties.measureType === AnalyticModelMeasureType.UnitConversionMeasure ||
        measureProperties.measureType === AnalyticModelMeasureType.NonCumulativeMeasure
      ) {
        if (measureProperties.key === usingMeasure) {
          usedInList.push({
            type: UsedInType.MEASURE,
            technicalName: measureKey,
            businessName: measures[measureKey].text,
            subType: measures[measureKey].measureType,
            typeIcon: Formatter.measureTypeIcon(measures[measureKey].measureType),
          });
          continue measureLoop;
        }
      }
      if (measureProperties.measureType === AnalyticModelMeasureType.CalculatedMeasure && !!measureProperties.formula) {
        const elements = Object.values(measureProperties.elements);
        for (const element of elements) {
          if (
            element.operandType === AnalyticModelCalculatedMeasureOperandType.Element &&
            element.key === usingMeasure
          ) {
            usedInList.push({
              type: UsedInType.MEASURE,
              technicalName: measureKey,
              businessName: measures[measureKey].text,
              subType: measures[measureKey].measureType,
              typeIcon: Formatter.measureTypeIcon(measures[measureKey].measureType),
            });
            continue measureLoop;
          }
        }
      }
    }
    return usedInList;
  }

  public getAttributeUsedIn(attributeKey) {
    const usedInMeasures = this.getMeasuresUsingAttribute(attributeKey);
    const usedInVariables = this.getVariablesUsingAttribute(attributeKey);
    const usedInGlobalFilter = this.getFilterUsingAttribute(attributeKey);
    const usedInDimensions = this.getDimensionsUsingAttribute(attributeKey);
    const usedInDataAccessControls = this.getDataAccessControlsUsingAttribute(attributeKey);
    const usedInCrossCalculations = this.getCrossCalculationsUsingAttribute(attributeKey);
    const usedIn = [
      ...usedInMeasures,
      ...usedInVariables,
      ...usedInGlobalFilter,
      ...usedInDimensions,
      ...usedInDataAccessControls,
      ...usedInCrossCalculations,
    ];
    return usedIn;
  }

  public getMeasuresUsingAttribute(attributeKey): AttributeDetails["usedIn"] {
    // using a map here to not have duplicate items
    const usedIn = new Map<string, UsedInItem>();
    const measures = this.model.getMeasures();
    for (const measureKey in measures) {
      const measure = measures[measureKey];
      if (
        measure.measureType === AnalyticModelMeasureType.CalculatedMeasure ||
        measure.measureType === AnalyticModelMeasureType.RestrictedMeasure
      ) {
        const elements = measure.elements ?? {};
        Object.values(elements).forEach((element) => {
          if (
            (element.operandType === AnalyticModelRestrictedMeasureOperandType.Attribute ||
              element.operandType === AnalyticModelCalculatedMeasureOperandType.Element) &&
            element.key === attributeKey
          ) {
            usedIn.set(measureKey, {
              type: UsedInType.MEASURE,
              subType: measure.measureType,
              businessName: measure.text,
              technicalName: measureKey,
              typeIcon: Formatter.measureTypeIcon(measure.measureType),
            });
          }
        });
      }
      if (measure.measureType === AnalyticModelMeasureType.CurrencyConversionMeasure) {
        if (
          measure.referenceDateType === AnalyticModelReferenceDateType.attribute &&
          (measure.referenceDate as IAnalyticModelAttributeKey).key === attributeKey
        ) {
          usedIn.set(measureKey, {
            type: UsedInType.MEASURE,
            subType: measure.measureType,
            businessName: measure.text,
            technicalName: measureKey,
            typeIcon: Formatter.measureTypeIcon(measure.measureType),
          });
        }
        if (
          measure.targetCurrencyType === AnalyticModelTargetCurrencyType.attribute &&
          (measure.targetCurrency as IAnalyticModelAttributeKey).key === attributeKey
        ) {
          usedIn.set(measureKey, {
            type: UsedInType.MEASURE,
            subType: measure.measureType,
            businessName: measure.text,
            technicalName: measureKey,
            typeIcon: Formatter.measureTypeIcon(measure.measureType),
          });
        }
        if (
          measure.conversionTypeType === AnalyticModelConversionTypeType.attribute &&
          (measure.conversionType as IAnalyticModelAttributeKey).key === attributeKey
        ) {
          usedIn.set(measureKey, {
            type: UsedInType.MEASURE,
            subType: measure.measureType,
            businessName: measure.text,
            technicalName: measureKey,
            typeIcon: Formatter.measureTypeIcon(measure.measureType),
          });
        }
      }
      if (measure.measureType === AnalyticModelMeasureType.UnitConversionMeasure) {
        if (
          measure.targetUnitType === AnalyticModelTargetUnitType.attribute &&
          (measure.targetUnit as IAnalyticModelAttributeKey).key === attributeKey
        ) {
          usedIn.set(measureKey, {
            type: UsedInType.MEASURE,
            subType: measure.measureType,
            businessName: measure.text,
            technicalName: measureKey,
            typeIcon: Formatter.measureTypeIcon(measure.measureType),
          });
        }
      }
      if (
        measure.measureType === AnalyticModelMeasureType.CountDistinct &&
        measure.countDistinctAttributes.includes(attributeKey)
      ) {
        usedIn.set(measureKey, {
          type: UsedInType.MEASURE,
          subType: measure.measureType,
          businessName: measure.text,
          technicalName: measureKey,
          typeIcon: Formatter.measureTypeIcon(measure.measureType),
        });
      }

      if ((measure as MeasureWithExceptionAggregation).exceptionAggregationAttributes?.includes(attributeKey)) {
        usedIn.set(measureKey, {
          type: UsedInType.MEASURE,
          subType: measure.measureType,
          businessName: measure.text,
          technicalName: measureKey,
          typeIcon: Formatter.measureTypeIcon(measure.measureType),
        });
      }
      if (
        measure.measureType === AnalyticModelMeasureType.RestrictedMeasure &&
        measure.constantSelectionType === AnalyticModelConstantSelectionType.Selected &&
        measure.constantSelectionAttributes?.includes(attributeKey)
      ) {
        usedIn.set(measureKey, {
          type: UsedInType.MEASURE,
          subType: measure.measureType,
          businessName: measure.text,
          technicalName: measureKey,
          typeIcon: Formatter.measureTypeIcon(measure.measureType),
        });
      }

      if (measure.measureType === AnalyticModelMeasureType.NonCumulativeMeasure) {
        const nonCumulativeSettings = this.model.getNonCumulativeSettings();
        const attribute = this.model.getAttribute(attributeKey);

        const isRecordTypeKeyMatched = nonCumulativeSettings?.recordTypeAttributeKey === attributeKey;
        const isTimeDimensionKeyMatched =
          nonCumulativeSettings?.timeDimensionKey === attribute.usedForDimensionSourceKey;
        if (!!nonCumulativeSettings && (isRecordTypeKeyMatched || isTimeDimensionKeyMatched)) {
          usedIn.set(measureKey, {
            type: UsedInType.MEASURE,
            subType: measure.measureType,
            businessName: measure.text,
            technicalName: measureKey,
            typeIcon: Formatter.measureTypeIcon(measure.measureType),
          });
        }
      }
    }
    return Array.from(usedIn.values());
  }

  public getVariablesUsingAttribute(attributeKey) {
    const usedIn: AttributeDetails["usedIn"] = [];
    const variables = this.model.getVariables();
    for (const variableKey in variables) {
      const variable = variables[variableKey];
      if (
        (variable.parameterType === AnalyticModelParameterType.Filter ||
          variable.parameterType === AnalyticModelParameterType.StoryFilter) &&
        variable.referenceAttribute === attributeKey
      ) {
        usedIn.push({
          type: UsedInType.VARIABLE,
          subType: variable.parameterType,
          businessName: getVariableBusinessName(variable, this.model),
          technicalName: variableKey,
          typeIcon: "sap-icon://sac/variable",
        });
      }
    }
    return usedIn;
  }

  public getFilterUsingAttribute(attributeKey) {
    const usedIn: AttributeDetails["usedIn"] = [];
    const globalFilter = this.model.getGlobalFilter();
    const elements = globalFilter?.elements ?? {};
    Object.values(elements).forEach((element) => {
      if (element.operandType === AnalyticModelFilterOperandType.Attribute && element.key === attributeKey) {
        usedIn.push({
          type: UsedInType.FILTER,
          businessName: globalFilter.text,
          technicalName: globalFilter.text,
          typeIcon: "sap-icon://sac/filter",
        });
      }
    });
    return usedIn;
  }

  public getDimensionsUsingAttribute(attributeKey: string): AttributeDetails["usedIn"] {
    if (!this.model.getDimensionHandlingCapability()) {
      return [];
    }

    const attribute = this.model.getAttribute(attributeKey);
    const isBaseAttribute = !attribute.duplicated;
    const isUsedInNonForeignKeyAssociation = isAttributeUsedInNonForeignKeyAssociation(this.model, attribute);
    if (!isBaseAttribute || isUsedInNonForeignKeyAssociation) {
      return [];
    }

    const referenceAttributes = getReferenceAttributes(attributeKey, this.model);
    const usedIn: AttributeDetails["usedIn"] = [];
    Object.values(referenceAttributes).forEach((referenceAttribute) => {
      const dimensionSource = this.model.getDimensionSource(referenceAttribute.usedForDimensionSourceKey);
      usedIn.push({
        type: UsedInType.DIMENSION,
        businessName: dimensionSource?.text,
        technicalName: referenceAttribute.usedForDimensionSourceKey,
        typeIcon: "sap-icon://sac/perspectives",
      });
    });
    return usedIn;
  }

  public getDataAccessControlsUsingAttribute(attributeKey: string): AttributeDetails["usedIn"] {
    if (!this.featureflags["DWCO_MODELING_AM_DAC_SUPPORT"] || !this.model.getDimensionHandlingCapability()) {
      return [];
    }
    const dataAccessControls = this.model.getDataAccessControls();
    const usedIn: AttributeDetails["usedIn"] = [];

    Object.entries(dataAccessControls).forEach(([key, dataAccessControl]) => {
      if (Object.values(dataAccessControl.dacMapping ?? {}).includes(attributeKey)) {
        const dacEntity = this.uiModel.getProperty("/allSourceDataEntityDetails")?.[key] as IDataEntity;
        usedIn.push({
          type: UsedInType.DAC,
          businessName: dacEntity?.businessName ?? key,
          technicalName: key,
          typeIcon: "sap-icon://permission",
        });
      }
    });
    return usedIn;
  }

  /**
   * Returns a list of cross calculations where a given attribute is used in
   * @param attributeKey - technical name of the attribute
   */
  public getCrossCalculationsUsingAttribute(attributeKey: string): AttributeDetails["usedIn"] {
    // using a map here to not have duplicate items
    const usedIn = new Map<string, UsedInItem>();
    const crossCalculations = this.model.getCrossCalculations();
    for (const crossCalculationKey in crossCalculations) {
      const crossCalculation = crossCalculations[crossCalculationKey];
      if (crossCalculation.crossCalculationType === AnalyticModelCrossCalculationType.FactSourceCrossCalculation) {
        continue;
      }

      const elements = crossCalculation.elements || {};
      for (const elementKey in elements) {
        const element = elements[elementKey];
        // Expression
        const isAttributeElement =
          element.operandType === AnalyticModelRestrictedCrossCalculationOperandType.Attribute ||
          element.operandType === AnalyticModelCalculatedCrossCalculationOperandType.Element;
        if (isAttributeElement && element.key === attributeKey) {
          usedIn.set(crossCalculationKey, {
            type: UsedInType.CROSS_CALCULATION,
            subType: crossCalculation.crossCalculationType,
            businessName: crossCalculation.text,
            technicalName: crossCalculationKey,
            typeIcon: Formatter.crossCalculation.typeIcon(crossCalculation.crossCalculationType),
          });
        }
      }
      // Exception Aggregation
      if (
        crossCalculation.crossCalculationType === AnalyticModelCrossCalculationType.CalculatedCrossCalculation &&
        crossCalculation.exceptionAggregationAttributes?.includes(attributeKey)
      ) {
        usedIn.set(crossCalculationKey, {
          type: UsedInType.CROSS_CALCULATION,
          subType: crossCalculation.crossCalculationType,
          businessName: crossCalculation.text,
          technicalName: crossCalculationKey,
          typeIcon: Formatter.crossCalculation.typeIcon(crossCalculation.crossCalculationType),
        });
      }
      // Constant Selection
      if (
        crossCalculation.crossCalculationType === AnalyticModelCrossCalculationType.RestrictedCrossCalculation &&
        crossCalculation.constantSelectionType === AnalyticModelConstantSelectionType.Selected &&
        crossCalculation.constantSelectionAttributes?.includes(attributeKey)
      ) {
        usedIn.set(crossCalculationKey, {
          type: UsedInType.CROSS_CALCULATION,
          subType: crossCalculation.crossCalculationType,
          businessName: crossCalculation.text,
          technicalName: crossCalculationKey,
          typeIcon: Formatter.crossCalculation.typeIcon(crossCalculation.crossCalculationType),
        });
      }
    }
    return Array.from(usedIn.values());
  }

  public getUsedInParamList(technicalName: string): UsedInItem[] {
    const allSourceDataEntityDetails = this.uiModel.getProperty("/allSourceDataEntityDetails");
    let usedInList = getVariablesUsingVariable(technicalName, this.model, allSourceDataEntityDetails);
    const measures = this.getMeasuresUsingVariable(technicalName);
    const filters = this.getFilterUsingVariable(technicalName);
    const fiscalTimeDimensions = this.getFiscalTimeDimensionsUsingVariable(technicalName);
    const timeDependencies = this.getTimeDependencyUsingVariable(technicalName);
    const crossCalculations = this.getCrossCalculationsUsingVariable(technicalName);
    usedInList = [
      ...filters,
      ...timeDependencies,
      ...usedInList,
      ...measures,
      ...fiscalTimeDimensions,
      ...crossCalculations,
    ];
    return usedInList;
  }

  /**
   * Returns a list of measures where a given variable is used in
   * @param variableKey
   */
  public getMeasuresUsingVariable(variableKey: string): UsedInItem[] {
    // check if variable is been used in parameterBinding (i.e used in derived variable)
    const usedIn: UsedInItem[] = [];
    const measures = this.model.getMeasures();
    for (const measureKey of Object.keys(measures)) {
      const measure = measures[measureKey];
      if (measure.measureType === AnalyticModelMeasureType.RestrictedMeasure) {
        Object.values(measure.elements || []).forEach((item) => {
          if (
            item.operandType === AnalyticModelRestrictedMeasureOperandType.FilterVariable &&
            item.key === variableKey
          ) {
            this.addUsedVariableToList(usedIn, measure, measureKey);
          }
        });
      } else if (
        /** Check if variable is used in currency conversion measure */
        measure.measureType === AnalyticModelMeasureType.CurrencyConversionMeasure
      ) {
        this.getCurrencyConversionMeasureUsingVariable(
          measure,
          measureKey,
          variableKey,
          usedIn,
          /* variableKeyToRemove*/ [],
          /* variablesUsedInMeasure*/ []
        );
      } else if (
        /** Check if variable is used in unit conversion measure */
        measure.measureType === AnalyticModelMeasureType.UnitConversionMeasure
      ) {
        this.getUnitConversionMeasureUsingVariable(
          measure,
          measureKey,
          variableKey,
          usedIn,
          /* variableKeyToRemove*/ [],
          /* variablesUsedInMeasure*/ []
        );
      } else if (measure.measureType === AnalyticModelMeasureType.CalculatedMeasure) {
        // check if variable is used in calculated measure
        Object.values(measure.elements || []).forEach((item) => {
          if (
            item.operandType === AnalyticModelCalculatedMeasureOperandType.FormulaVariable &&
            item.key === variableKey
          ) {
            this.addUsedVariableToList(usedIn, measure, measureKey);
          }
        });
      }
    }
    return usedIn;
  }

  /**
   * Returns a list with the pseudo filter (NOT THE GLOBAL FILTER) where a given variable is used in
   * This is used to illustrate the filter that is automatically created when filter variables exists
   * example: Filter on ${attributeName} (${sourceName})
   * @param variableKey
   */
  public getFilterUsingVariable(variableKey: string): UsedInItem[] {
    const usedIn: UsedInItem[] = [];
    const variable = this.model.getVariable(variableKey);

    if (variable.parameterType === AnalyticModelParameterType.StoryFilter) {
      const attribute = this.model.getAttribute(variable.referenceAttribute);
      if (!attribute) {
        return usedIn;
      }
      const technicalName = variable.referenceAttribute;
      const { source, sourceKey } = getSourcesFromAttribute(attribute, this.model);
      const shouldDisplayBusinessName = User.getInstance().getObjectNameDisplay() === ObjectNameDisplay.businessName;
      const sourceName =
        shouldDisplayBusinessName || !this.model.getDimensionHandlingCapability() ? source.text : sourceKey;
      const textParameters = shouldDisplayBusinessName
        ? [attribute.text, sourceName]
        : [variable.referenceAttribute, sourceName];
      const fullName = Util.Component.getText("variableInfoFilterOnAttributeText", textParameters);

      usedIn.push({
        type: UsedInType.FILTER_FROM_VARIABLE,
        subType: UsedInType.FILTER_FROM_VARIABLE,
        businessName: "", // won't be used because fullName is set
        technicalName,
        fullName,
        textParameters,
        typeIcon: "sap-icon://sac/filter",
      });
    }

    return usedIn;
  }

  /**
   * Returns a list with the time dependency where a given variable is used in
   * This is used to illustrate time dependency when a reference date variable exists
   * @param variableKey
   */
  public getTimeDependencyUsingVariable(variableKey: string): UsedInItem[] {
    const usedIn: UsedInItem[] = [];
    const variable = this.model.getVariable(variableKey);

    if (variable?.parameterType === AnalyticModelParameterType.KeyDate) {
      if (modelHasTimeDependency(this.uiModel)) {
        const timeDependencies = this.getModelTimeDependencies();
        usedIn.push({
          type: UsedInType.TIME_DEPENDENCY,
          subType: UsedInType.TIME_DEPENDENCY,
          // businessName and technicalName won't be used because fullName is set
          businessName: "",
          technicalName: "",
          textParameters: [timeDependencies.length.toString()],
          fullName: Formatter.objectUsedInDescription(
            UsedInType.TIME_DEPENDENCY,
            [timeDependencies.length.toString()],
            this.featureflags?.["DWCO_MODELING_AM_MULTI_STRUCTURE"]
          ),
          typeIcon: "sap-icon://sac/time",
        });
      }
    }

    return usedIn;
  }

  public getFiscalTimeDimensionsUsingVariable(variableKey: string): UsedInItem[] {
    if (!this.featureflags["DWCO_MODELING_SUPPORT_FISCAL_TIME"]) {
      return [];
    }

    const variable = this.model.getVariable(variableKey);
    if (variable.parameterType !== AnalyticModelParameterType.FiscalVariant) {
      return [];
    }

    const usedInItems = Object.entries(this.model.getDimensionSources()).reduce((usedInList, [sourceKey, source]) => {
      const parentType = source.associationContexts[0].sourceType;
      const parentKey = source.associationContexts[0].sourceKey;
      const associationName = source.associationContexts[0].associationSteps[0];

      let parentSource;
      if (parentType === AnalyticModelSourceType.Fact) {
        parentSource = this.model.getFactSource(parentKey);
      } else {
        parentSource = this.model.getDimensionSource(parentKey);
      }
      const parentDataEntity = parentSource.dataEntity.key;

      const association = (this.uiModel.getProperty("/allSourceDataEntityDetails") as IDataEntityDetailsResponse)[
        parentDataEntity
      ]?.associations.find((association) => association.key === associationName);

      const isFiscalDimension = association?.fiscalVariant;

      if (isFiscalDimension) {
        usedInList.push({
          type: UsedInType.DIMENSION,
          businessName: source.text,
          technicalName: sourceKey,
          typeIcon: "sap-icon://sac/perspectives",
        });
      }

      return usedInList;
    }, [] as UsedInItem[]);

    return usedInItems;
  }

  /**
   * This function is used for updating variable details view with the new used in list for variables &
   * check If they are used in Measures while variable deletion
   * @param measure
   * @param measureKey
   * @param existingVariable
   * @param usedIn
   * @param variableKeyToRemove
   * @param variablesUsedInMeasure
   */
  public getCurrencyConversionMeasureUsingVariable(
    measure: IAnalyticModelCurrencyConversionMeasure,
    measureKey: string,
    existingVariable: string,
    usedIn: UsedInItem[],
    variableKeyToRemove: string[],
    variablesUsedInMeasure: any[]
  ) {
    if (
      measure.targetCurrencyType === AnalyticModelTargetCurrencyType.variable &&
      ((measure.targetCurrency as IAnalyticModelVariableKey).key === existingVariable ||
        variableKeyToRemove.includes((measure.targetCurrency as IAnalyticModelVariableKey).key))
    ) {
      this.addUsedVariableToList(usedIn, measure, measureKey);
      variablesUsedInMeasure.push({
        variableKey: (measure.targetCurrency as IAnalyticModelVariableKey).key,
        measureKey: measureKey,
        currencyType: AnalyticModelTargetCurrencyType.variable,
      });
    }
    if (
      measure.referenceDateType === AnalyticModelReferenceDateType.variable &&
      ((measure.referenceDate as IAnalyticModelVariableKey).key === existingVariable ||
        variableKeyToRemove.includes((measure.referenceDate as IAnalyticModelVariableKey).key))
    ) {
      this.addUsedVariableToList(usedIn, measure, measureKey);
      variablesUsedInMeasure.push({
        variableKey: (measure.referenceDate as IAnalyticModelVariableKey).key,
        measureKey: measureKey,
        currencyType: AnalyticModelReferenceDateType.variable,
      });
    }
    if (
      measure.conversionTypeType === AnalyticModelConversionTypeType.variable &&
      ((measure.conversionType as IAnalyticModelVariableKey).key === existingVariable ||
        variableKeyToRemove.includes((measure.conversionType as IAnalyticModelVariableKey).key))
    ) {
      this.addUsedVariableToList(usedIn, measure, measureKey);
      variablesUsedInMeasure.push({
        variableKey: (measure.conversionType as IAnalyticModelVariableKey).key,
        measureKey: measureKey,
        currencyType: AnalyticModelConversionTypeType.variable,
      });
    }
  }

  /**
   * Returns a list of cross calculations where a given variable is used in
   * @param variableKey - technical name of the variable
   */
  public getCrossCalculationsUsingVariable(variableKey: string): UsedInItem[] {
    const usedIn: UsedInItem[] = [];
    const crossCalculations = this.model.getCrossCalculations();
    for (const crossCalculationKey in crossCalculations) {
      const crossCalculation = crossCalculations[crossCalculationKey];
      if (crossCalculation.crossCalculationType === AnalyticModelCrossCalculationType.FactSourceCrossCalculation) {
        continue;
      }

      const elements = crossCalculation.elements || {};
      for (const elementKey in elements) {
        const element = elements[elementKey];
        const isVariableElement =
          element.operandType === AnalyticModelCalculatedCrossCalculationOperandType.FormulaVariable ||
          element.operandType === AnalyticModelRestrictedCrossCalculationOperandType.FilterVariable;
        if (isVariableElement && element.key === variableKey) {
          usedIn.push({
            type: UsedInType.CROSS_CALCULATION,
            subType: crossCalculation.crossCalculationType,
            businessName: crossCalculation.text,
            technicalName: crossCalculationKey,
            typeIcon: Formatter.crossCalculation.typeIcon(crossCalculation.crossCalculationType),
          });
        }
      }
    }
    return usedIn;
  }

  public getUnitConversionMeasureUsingVariable(
    measure: IAnalyticModelUnitConversionMeasure,
    measureKey: string,
    existingVariable: string,
    usedIn: UsedInItem[],
    variableKeyToRemove: string[],
    variablesUsedInMeasure: any[]
  ) {
    // If the feature flag for unit conversion is not active, do nothing
    if (!this.featureflags["DWCO_MODELING_AM_UNIT_CONVERSION"]) {
      return;
    }
    if (
      measure.targetUnitType === AnalyticModelTargetUnitType.variable &&
      ((measure.targetUnit as IAnalyticModelVariableKey).key === existingVariable ||
        variableKeyToRemove.includes((measure.targetUnit as IAnalyticModelVariableKey).key))
    ) {
      this.addUsedVariableToList(usedIn, measure, measureKey);
      variablesUsedInMeasure.push({
        variableKey: (measure.targetUnit as IAnalyticModelVariableKey).key,
        measureKey: measureKey,
        currencyType: AnalyticModelTargetUnitType.variable,
      });
    }
  }

  public addUsedVariableToList(usedIn: UsedInItem[], measure: any, measureKey: any) {
    return usedIn.push({
      type: UsedInType.MEASURE,
      subType: measure.measureType,
      businessName: measure.text,
      technicalName: measureKey,
      typeIcon: Formatter.measureTypeIcon(measure.measureType),
    });
  }

  public getModelTimeDependencies(): string[] {
    const allSourceDataEntityDetails = this.uiModel.getProperty(
      "/allSourceDataEntityDetails"
    ) as IDataEntityDetailsResponse;
    const allSources = [
      ...Object.values(this.model.getDimensionSources()).map((source) => source.dataEntity.key),
      ...Object.values(this.model.getFactSources()).map((source) => source.dataEntity.key),
    ];
    let timeDependencies: string[] = [];
    for (const source of allSources) {
      allSourceDataEntityDetails?.[source]?.associations.forEach((association) => {
        if (association.hasTimeDependency) {
          timeDependencies.push(association.targetEntity.key);
        }
        timeDependencies = timeDependencies.concat(association.timeDependencies);
      });
    }

    return [...new Set(timeDependencies)];
  }

  public getCrossCalculationsUsedIn(usingCrossCalculation: string): UsedInItem[] {
    const usedIn: UsedInItem[] = [];
    const crossCalculations = this.model.getCrossCalculations();
    for (const crossCalculationKey in crossCalculations) {
      const crossCalculation = crossCalculations[crossCalculationKey];

      if (
        crossCalculation.crossCalculationType === AnalyticModelCrossCalculationType.CalculatedCrossCalculation &&
        !!crossCalculation.formula
      ) {
        const elements = Object.values(crossCalculation.elements);
        for (const element of elements) {
          if (
            element.operandType === AnalyticModelCalculatedCrossCalculationOperandType.Element &&
            element.key === usingCrossCalculation
          ) {
            usedIn.push({
              type: UsedInType.CROSS_CALCULATION,
              technicalName: crossCalculationKey,
              businessName: crossCalculation.text,
              subType: crossCalculation.crossCalculationType,
              typeIcon: Formatter.crossCalculation.typeIcon(crossCalculation.crossCalculationType),
            });
          }
        }
      }
    }
    return usedIn;
  }
}
