###################################################################
### Translation Texts for Notifications coming from the Backend ###
### Please maintain a section for each team/tool                ###
###################################################################

# ~~~~~~ Start of ACN Notifications ~~~~~~
#XMSG ACN job title for pending import jobs, parameter 0 is for package name
notificationACNImportTitlePENDING=Uvoz paketa {0} je na čekanju
#XMSG ACN job title for running import jobs, parameter 0 is for package name
notificationACNImportTitleEXECUTING=Uvoz paketa {0} je u toku
#XMSG ACN job title for completed import jobs with warnings, parameter 0 is for package name
notificationACNImportTitleWARNING=Uvoz paketa {0} zav<PERSON><PERSON>en sa upozorenjem
#XMSG ACN job title for completed import jobs, parameter 0 is for package name
notificationACNImportTitleDONE=Uvoz paketa {0} zavr<PERSON>en
#XMSG ACN job title for failed import jobs, parameter 0 is for package name
notificationACNImportTitleFAILED=Uvoz paketa {0} nije uspio
#XMSG ACN job title for pending export jobs, parameter 0 is for package name
notificationACNExportTitlePENDING=Izvoz paketa {0} je na čekanju
#XMSG ACN job title for running export jobs, parameter 0 is for package name
notificationACNExportTitleEXECUTING=Izvoz paketa {0} je u toku
#XMSG ACN job title for completed export jobs with warnings, parameter 0 is for package name
notificationACNExportTitleWARNING=Izvoz paketa {0} završen sa upozorenjem
#XMSG ACN job title for completed export jobs, parameter 0 is for package name
notificationACNExportTitleDONE=Izvoz paketa {0} završen
#XMSG ACN job title for failed export jobs, parameter 0 is for package name
notificationACNExportTitleFAILED=Izvoz paketa {0} nije uspio
#XMSG ACN job title for pending download jobs, parameter 0 is for package name
notificationACNDownloadTitlePENDING=Prenos paketa {0} sa servera čeka na izvršenje
#XMSG ACN job title for running download jobs, parameter 0 is for package name
notificationACNDownloadTitleEXECUTING=Prenos paketa {0} sa servera u toku
#XMSG ACN job title for completed download jobs with warnings, parameter 0 is for package name
notificationACNDownloadTitleWARNING=Prenos paketa {0} sa servera završen sa upozorenjem
#XMSG ACN job title for completed download jobs, parameter 0 is for package name
notificationACNDownloadTitleDONE=Prenos paketa {0} sa servera završen
#XMSG ACN job title for Downloaded download jobs, parameter 0 is for package name
notificationACNDownloadTitleDOWNLOADED=Prenos paketa {0} sa servera završen
#XMSG ACN job title for failed download jobs, parameter 0 is for package name
notificationACNDownloadTitleFAILED=Prenos paketa {0} sa servera nije uspio
#XMSG ACN job title for pending upload jobs, parameter 0 is for package name
notificationACNUploadTitlePENDING=Prenos paketa {0} na server čeka na izvršenje
#XMSG ACN job title for running upload jobs, parameter 0 is for package name
notificationACNUploadTitleEXECUTING=Prenos paketa {0} na server u toku
#XMSG ACN job title for completed upload jobs with warnings, parameter 0 is for package name
notificationACNUploadTitleWARNING=Prenos paketa {0} na server završen sa upozorenjem
#XMSG ACN job title for completed upload jobs, parameter 0 is for package name
notificationACNUploadTitleDONE=Prenos paketa {0} na server završen
#XMSG ACN job title for failed upload jobs, parameter 0 is for package name
notificationACNUploadTitleFAILED=Prenos paketa {0} na server nije uspio
#XMSG ACN job title for pending upload jobs, parameter 0 is for package name
notificationACNGitPullTitlePENDING=Povlačenje paketa {0} čeka na izvršenje
#XMSG ACN job title for running upload jobs, parameter 0 is for package name
notificationACNGitPullTitleEXECUTING=Povlačenje paketa {0} u toku
#XMSG ACN job title for completed upload jobs with warnings, parameter 0 is for package name
notificationACNGitPullTitleWARNING=Povlačenje paketa {0} završeno sa upozorenjem
#XMSG ACN job title for completed upload jobs, parameter 0 is for package name
notificationACNGitPullTitleDONE=Povlačenje paketa {0} završeno
#XMSG ACN job title for failed upload jobs, parameter 0 is for package name
notificationACNGitPullTitleFAILED=Povlačenje paketa {0} nije uspjelo
#XMSG ACN job title for pending upload jobs, parameter 0 is for package name
notificationACNGitPushTitlePENDING=Proslijeđivanje paketa {0} čeka na izvršenje
#XMSG ACN job title for running upload jobs, parameter 0 is for package name
notificationACNGitPushTitleEXECUTING=Proslijeđivanje paketa {0} u toku
#XMSG ACN job title for completed upload jobs with warnings, parameter 0 is for package name
notificationACNGitPushTitleWARNING=Proslijeđivanje paketa {0} završeno sa upozorenjem
#XMSG ACN job title for completed upload jobs, parameter 0 is for package name
notificationACNGitPushTitleDONE=Proslijeđivanje paketa {0} završeno
#XMSG ACN job title for failed upload jobs, parameter 0 is for package name
notificationACNGitPushTitleFAILED=Proslijeđivanje paketa {0} nije uspjelo
#XMSG ACN job title for pending composite job- publish to ctms, parameter 0 is for package name, 1 is for node
notificationACNCompositeTitlePENDING=Prenos paketa {0} na server u {1} čeka na izvršenje.
#XMSG ACN job title for running composite job- publish to ctms, parameter 0 is for package name, 1 is for node
notificationACNCompositeTitleEXECUTING=Prenos paketa {0} na server u {1} je u toku.
#XMSG ACN job title for composite job- publish to ctms completed with warnings, parameter 0 is for package name, 1 is for node
notificationACNCompositeTitleWARNING=Prenos paketa {0} na server u {1} je završen sa upozorenjem.
#XMSG ACN job title for completed composite job- publish to ctms, parameter 0 is for package name, 1 is for node
notificationACNCompositeTitleDONE=Prenos paketa {0} na server u {1} je završen.
#XMSG ACN job title for failed composite job- publish to ctms, parameter 0 is for package name, 1 is for node
notificationACNCompositeTitleFAILED=Prenos paketa {0} na server u {1} nije uspio.
#YMSG ACN job details for jobs, parameter 0 is for current number of objects, parameter 1 for total number of objects
notificationACNBody=Obrađeno objekata {0} / {1}
#XMSG: ACN .Package validity date.
ACN_PACKAGE_DOWNLOAD_VALIDTY=Paket je važeći za prenos na server na mrežu sadržaja do {0}.
# ~~~~~~ End of ACN Notifications ~~~~~~

# ~~~~~~ Start of DP Agent Notifications ~~~~~~
#XTXT: DP Agent Connection Status Notification - connected
DPAgentConnectionStatusNotificationConnected=Agent distribucije podataka {0} je povezan.
#XTXT: DP Agent Connection Status Notification - disconnected
DPAgentConnectionStatusNotificationDisconnected=Veza agenta distribucije podataka {0} je prekinuta. Provjerite.
# ~~~~~~ End of DP Agent Notifications ~~~~~~

# ~~~~~~ Start of Spaces Notifications ~~~~~~
#XMSG Notification title for a successful space deployment
spaceDeploymentSuccessful=Prostor "{0}" je implementiran.
#XMSG Notification title for an unsuccessful space deployment
spaceDeploymentUnsuccessful=Prostor "{0}" se ne može implementirati.
#XMSG Notification title for an unsuccessful space content copy
spaceContentCopyUnsuccessful=Prostor "{1}" kreiran i implementiran, ali nije moguće kopirati objekte iz prostora "{0}" u prostor "{1}".
#XMSG Notification title for an unsuccessful space content deployment
spaceContentDeploymentUnsuccessful=Prostor "{1}" kreiran i implementiran. Svi objekti kopirani iz prostora "{0}" u prostor "{1}" ali neke nije moguće implementirati.
#XMSG Notification body for a successful space deployment
openSpace=Otvorite {0}.
#XMSG Notification title for a successful copy space and deployment
spaceCopyAndDeploySuccessful=Prostor "{0}" je kopiran u prostor "{1}". Objekti u prostoru "{1}" su implementirani.
#XMSG Notification title for a successful copy of space content
spaceContentCopySuccessful=Prostor "{1}" kreiran i implementiran. Svi objekti kopirani iz prostora "{0}" u prostor "{1}".
#XMSG Notification title for a successful deployment of space content
spaceContentCopyAndDeploymentSuccessful=Prostor "{1}" kreiran i implementiran. Svi objekti kopirani iz prostora "{0}" u prostor "{1}" i implementirani.
#XMSG Notification title for a successful space deletion
spaceDeletionSuccessful=Prostor "{0}" je izbrisan.
# ~~~~~~ End of Spaces Notifications ~~~~~~

# ~~~~~~ Start of Cross Architecture Notifications ~~~~~~
#XMSG: Notification message to inform the user that a new survey is available
longSurveyInitialNotification=Zdravo {0}, recite nam kako nam ide i šta mislite u našoj anketi o korisničkom iskustvu. U naredne tri nedjelje možete pristupiti anketi tako što ćete kliknuti na "Pruži povratne informacije" u traci okruženja.
#XMSG: Notification reminder message to inform the user that the long survey is about to expire
longSurveyReminderNotification=Zdravo {0}, imate još {1} dana da popunite našu anketu. Hvala vam ako ste je već popunili.
#XMSG: Notification reminder message to inform the user that the long survey is about to expire tomorrow
longSurveySecondLastReminderNotification=Zdravo {0}, imate još 1 dan da popunite našu anketu. Hvala vam ako ste je već popunili.
#XMSG: Notification reminder message to inform the user that the long survey is about to expire today.
longSurveyLastReminderNotification=Zdravo {0}, danas je posljednja prilika da popunite našu anketu. Hvala vam ako ste je već popunili.
# ~~~~~~ End of Cross Architecture Notifications ~~~~~~

# ~~~~~~ Start of Cockpit Notifications ~~~~~~
#XMSG: Notification message to inform the user that a activation failed
packageFailedInstallation=Aktiviranje paketa podataka {0} nije uspjelo. Pokušajte ponovo kasnije.
#XMSG: Notification message to inform the user that a activation succeeded
packageInstalled=Paket podataka {0} je aktiviran.
#XMSG: Notification message to inform the user that a deactivation failed
packageFailedUninstallation=Deaktiviranje paketa podataka {0} nije uspjelo. Pokušajte ponovo kasnije.
#XMSG: Notification message to inform the user that a deactivation succeeded
packageUninstalled=Paket podataka {0} je deaktiviran.
# ~~~~~~ End of Cockpit Notifications ~~~~~~


#### Begin Catalog component

#XMSG: Notification reminder message to inform the user that the import is successful. {0} is the file name of the import
txtOdcCatalogImportResultSuccessful={0} je uvezeno.
#YMSG: Notification reminder message to inform the user that the import is unsuccessful. {0} is the file name of the import
errOdcCatalogImportResultFailed=Uvoz {0} nije uspio.

#XMSG: Notification reminder title for sharing a data product. {0} is the share name. {1} is the data product name.
txtOdcDPApiShareCreateSuccessTitle=Dijeljenje {0} je kreirano za proizvod podataka {1}

#YMSG: Error notification reminder title for sharing a data product. {0} is the share name. {1} is the data product name.
errOdcDPApiShareCreateErrorTitle=Dijeljenje {0} se ne može kreirati za proizvod podataka {1}
#YMSG: {0} optional partner specific message that is not translated.  \n will be added to the end of the placeholder (if any).
errOdcDPApiShareCreateErrorBody={0}Pogledajte stranicu s detaljima proizvoda podataka

#YMSG: Error notification reminder title for sharing a data product. {0} is the share name. {1} is the data product name.
errOdcDPApiShareCreateTimeoutTitle=Dijeljenje {0} se ne može kreirati za proizvod podataka {1}
#YMSG:
errOdcDPApiShareCreateTimeoutBody=Dijeljenje nije uspjelo da se završi u očekivano vrijeme. Pogledajte stranicu s detaljima proizvoda podataka.

#XMSG: Notification reminder title for updating a data product share. {0} is the share name. {1} is the data product name.
txtOdcDPApiShareUpdateSuccessTitle=Dijeljenje {0} je ažurirano za proizvod podataka {1}

#YMSG: Error notification reminder title for updating a data product share. {0} is the share name. {1} is the data product name.
errOdcDPApiShareUpdateErrorTitle=Dijeljenje {0} se ne može ažurirati za proizvod podataka {1}
#YMSG: {0} optional partner specific message that is not translated.  \n will be added to the end of the placeholder (if any).
errOdcDPApiShareUpdateErrorBody={0}Pogledajte stranicu s detaljima proizvoda podataka

#YMSG: Error notification reminder title for updating a data product share. {0} is the share name. {1} is the data product name.
errOdcDPApiShareUpdateTimeoutTitle=Dijeljenje {0} se ne može ažurirati za proizvod podataka {1}
#YMSG:
errOdcDPApiShareUpdateTimeoutBody=Ažuriranje nije uspjelo da se završi u očekivano vrijeme. Pogledajte stranicu s detaljima proizvoda podataka.

#XMSG: Notification reminder title for deleting a data product share. {0} is the share name. {1} is the data product name.
txtOdcDPApiShareDeleteSuccessTitle=Dijeljenje {0} je izbrisano za proizvod podataka {1}

#YMSG: Error notification reminder title for deleting a data product share. {0} is the share name. {1} is the data product name.
errOdcDPApiShareDeleteErrorTitle=Dijeljenje {0} se ne može izbrisati za proizvod podataka {1}
#YMSG:
errOdcDPApiShareDeleteErrorBody=Došlo je do greške pri brisanju dijeljenja. Pogledajte stranicu s detaljima proizvoda podataka.

#YMSG: Error notification reminder title for deleting a data product share. {0} is the share name. {1} is the data product name.
errOdcDPApiShareDeleteTimeoutTitle=Dijeljenje {0} se ne može izbrisati za proizvod podataka {1}
#YMSG:
errOdcDPApiShareDeleteTimeoutBody=Brisanje dijeljenja nije uspjelo u očekivano vrijeme. Pogledajte stranicu s detaljima proizvoda podataka.

#YMSG:
errOdcDPApiShareGenericError=Nije moguće dijeliti proizvod podataka. Pogledajte stranicu s detaljima proizvoda podataka.
#YMSG:
errOdcDPApiShareNotFoundError=Nije nađeno dijeljenje proizvoda podataka. Pogledajte stranicu s detaljima proizvoda podataka.

#YMSG: Notification body message to inform the user they can click on the notification to go to the data product page
txtOdcDPApiViewDataProductPageSuggestion=Pogledajte stranicu s detaljima proizvoda podataka.

#XMSG: Notification reminder title for partially sharing a data product. {0} is the share name. {1} is the data product name.
errOdcDPApiShareCreatePartiallyTitle=Dijeljenje {0} je djelimično kreirano za proizvod podataka {1}
#YMSG:
errOdcDPApiShareCreatePartiallyBody=Nije moguće završiti dio zahtjeva za dijeljenje. Pogledajte stranicu s detaljima proizvoda podataka.

#XMSG: Notification reminder title for partially sharing a data product. {0} is the share name. {1} is the data product name.
errOdcDPApiShareUpdatePartiallyTitle=Dijeljenje {0} je djelimično ažurirano za proizvod podataka {1}
#YMSG:
errOdcDPApiShareUpdatePartiallyBody=Nije moguće završiti dio zahtjeva za dijeljenje. Pogledajte stranicu s detaljima proizvoda podataka.

#XMSG: Notification reminder that a recovery of the system has just completed, a manual synchronization is needed to get the system up to date
txtOdcCatalogRecoverySuccessManualSyncNeeded=Obnova sistema je potpuna. Izvršite ručnu sinhronizaciju da bi se osiguralo da su svi metapodaci za sva sredstva kataloga ažurirani.

#XMSG:
txtOdcSharedCatalogSetup=Klijent sada dijeli skladište kataloga s klijentom {0}

#XMSG:
txtOdcSharedCatalogRevoke=Klijent je zaustavio dijeljenje skladišta kataloga s klijentom {0}

#XMSG: Notification title message indicating the execution status of a task, including execution type, applicationId, activity and objectId.
taskExecutionCompleted={0} izvođenje {1}/{2} za {3} je završeno.
taskExecutionFailed={0} izvođenje {1}/{2} za {3} nije uspjelo.
#XMSG: Notification body message indicating the status of a task, including applicationId, activity, objectId and spaceId.
taskExecutionCompletedWithoutSubstatus=Zadatak {0}/{1} u objektu {2} u prostoru {3} je završen.
taskExecutionFailedWithoutSubstatus=Zadatak {0}/{1} u objektu {2} u prostoru {3} nije uspio.
#XMSG: Notification body message indicating the status and substatus of a task, including applicationId, activity, objectId, spaceId and substatus.
taskExecutionCompletedWithSubstatus=Zadatak {0}/{1} u objektu {2} u prostoru {3} je završen s podstatusom {4}.
taskExecutionFailedWithSubstatus=Zadatak {0}/{1} u objektu {2} u prostoru {3} nije uspio s podstatusom {4}.

#XMSG: Notification title message for task chain indicating the execution status of a task, including execution type, applicationId, activity and objectId.
taskChainExecutionCompleted={0} izvođenje {1}/{2} za {3} je završeno.
taskChainExecutionFailed={0} izvođenje {1}/{2} za {3} nije uspjelo.
#XMSG: Notification body message for task chain indicating the execution status of a task, including applicationId, activity, objectId and spaceId.
taskChainExecutionCompletedWithoutSubstatus=Zadatak {0}/{1} u objektu {2} u prostoru {3} je završen.
taskChainExecutionFailedWithoutSubstatus=Zadatak {0}/{1} u objektu {2} u prostoru {3} nije uspio.
#XMSG: Notification body message for task chain indicating the execution status and substatus of a task, including applicationId, activity, objectId, spaceId and substatus.
taskChainExecutionCompletedWithSubstatus=Zadatak {0}/{1} u objektu {2} u prostoru {3} je završen s podstatusom {4}.
taskChainExecutionFailedWithSubstatus=Zadatak {0}/{1} u objektu {2} u prostoru {3} nije uspio s podstatusom {4}.

# XMSG: Notification for Completed Schedules
scheduleCompletedPause=Broj pauziranih rasporeda: {0}.
scheduleCompletedResume=Broj nastavljenih rasporeda: {0}.
scheduleCompletedDelete=Broj izbrisanih rasporeda: {0}.
scheduleCompletedChangeOwner=Broj rasporeda sa ažuriranim vlasništvom: {0}.

# XMSG: Notification for Failed Schedules
scheduleFailedPause=Broj rasporeda koji se nijesu mogli pauzirati: {0} od {1}.
scheduleFailedResume=Broj rasporeda koji se nijesu mogli nastaviti: {0} od {1}.
scheduleFailedDelete=Broj rasporeda koji se nijesu mogli nastaviti: {0} od {1}.
scheduleFailedChangeOwner=Broj rasporeda za koje se nije moglo ažurirati vlasništvo: {0} od {1}.

#### End Catalog component
