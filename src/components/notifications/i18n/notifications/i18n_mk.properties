###################################################################
### Translation Texts for Notifications coming from the Backend ###
### Please maintain a section for each team/tool                ###
###################################################################

# ~~~~~~ Start of ACN Notifications ~~~~~~
#XMSG ACN job title for pending import jobs, parameter 0 is for package name
notificationACNImportTitlePENDING=Увозот на пакетот {0} е на чекање
#XMSG ACN job title for running import jobs, parameter 0 is for package name
notificationACNImportTitleEXECUTING=Увозот на пакетот {0} е во тек
#XMSG ACN job title for completed import jobs with warnings, parameter 0 is for package name
notificationACNImportTitleWARNING=Увозот на пакетот {0} заврши со предупредување
#XMSG ACN job title for completed import jobs, parameter 0 is for package name
notificationACNImportTitleDONE=Увозот на пакетот {0} заврши
#XMSG ACN job title for failed import jobs, parameter 0 is for package name
notificationACNImportTitleFAILED=Увозот на пакетот {0} заврши неуспешно
#XMSG ACN job title for pending export jobs, parameter 0 is for package name
notificationACNExportTitlePENDING=Извозот на пакетот {0} е на чекање
#XMSG ACN job title for running export jobs, parameter 0 is for package name
notificationACNExportTitleEXECUTING=Извозот на пакетот {0} е во тек
#XMSG ACN job title for completed export jobs with warnings, parameter 0 is for package name
notificationACNExportTitleWARNING=Извозот на пакетот {0} заврши со предупредување
#XMSG ACN job title for completed export jobs, parameter 0 is for package name
notificationACNExportTitleDONE=Извозот на пакетот {0} заврши
#XMSG ACN job title for failed export jobs, parameter 0 is for package name
notificationACNExportTitleFAILED=Извозот на пакетот {0} заврши неуспешно
#XMSG ACN job title for pending download jobs, parameter 0 is for package name
notificationACNDownloadTitlePENDING=Преземањето на пакетот {0} е на чекање за извршување
#XMSG ACN job title for running download jobs, parameter 0 is for package name
notificationACNDownloadTitleEXECUTING=Преземањето на пакетот {0} е во тек
#XMSG ACN job title for completed download jobs with warnings, parameter 0 is for package name
notificationACNDownloadTitleWARNING=Преземањето на пакетот {0} заврши со предупредување
#XMSG ACN job title for completed download jobs, parameter 0 is for package name
notificationACNDownloadTitleDONE=Преземањето на пакетот {0} заврши
#XMSG ACN job title for Downloaded download jobs, parameter 0 is for package name
notificationACNDownloadTitleDOWNLOADED=Преземањето на пакетот {0} заврши
#XMSG ACN job title for failed download jobs, parameter 0 is for package name
notificationACNDownloadTitleFAILED=Преземањето на пакетот {0} заврши неуспешно
#XMSG ACN job title for pending upload jobs, parameter 0 is for package name
notificationACNUploadTitlePENDING=Поставувањето на пакетот {0} е на чекање за извршување
#XMSG ACN job title for running upload jobs, parameter 0 is for package name
notificationACNUploadTitleEXECUTING=Поставувањето на пакетот {0} е во тек
#XMSG ACN job title for completed upload jobs with warnings, parameter 0 is for package name
notificationACNUploadTitleWARNING=Поставувањето на пакетот {0} заврши со предупредување
#XMSG ACN job title for completed upload jobs, parameter 0 is for package name
notificationACNUploadTitleDONE=Поставувањето на пакетот {0} заврши
#XMSG ACN job title for failed upload jobs, parameter 0 is for package name
notificationACNUploadTitleFAILED=Поставувањето на пакетот {0} заврши неуспешно
#XMSG ACN job title for pending upload jobs, parameter 0 is for package name
notificationACNGitPullTitlePENDING=Влечењето на пакетот {0} е на чекање за извршување
#XMSG ACN job title for running upload jobs, parameter 0 is for package name
notificationACNGitPullTitleEXECUTING=Влечењето на пакетот {0} е во тек
#XMSG ACN job title for completed upload jobs with warnings, parameter 0 is for package name
notificationACNGitPullTitleWARNING=Влечењето на пакетот {0} заврши со предупредување
#XMSG ACN job title for completed upload jobs, parameter 0 is for package name
notificationACNGitPullTitleDONE=Влечењето на пакетот {0} заврши
#XMSG ACN job title for failed upload jobs, parameter 0 is for package name
notificationACNGitPullTitleFAILED=Влечењето на пакетот {0} заврши неуспешно
#XMSG ACN job title for pending upload jobs, parameter 0 is for package name
notificationACNGitPushTitlePENDING=Туркањето на пакетот {0} е на чекање за извршување
#XMSG ACN job title for running upload jobs, parameter 0 is for package name
notificationACNGitPushTitleEXECUTING=Туркањето на пакетот {0} е во тек
#XMSG ACN job title for completed upload jobs with warnings, parameter 0 is for package name
notificationACNGitPushTitleWARNING=Туркањето на пакетот {0} заврши со предупредување
#XMSG ACN job title for completed upload jobs, parameter 0 is for package name
notificationACNGitPushTitleDONE=Туркањето на пакетот {0} заврши
#XMSG ACN job title for failed upload jobs, parameter 0 is for package name
notificationACNGitPushTitleFAILED=Туркањето на пакетот {0} заврши неуспешно
#XMSG ACN job title for pending composite job- publish to ctms, parameter 0 is for package name, 1 is for node
notificationACNCompositeTitlePENDING=Поставувањето на {0} во {1} чека да се изврши.
#XMSG ACN job title for running composite job- publish to ctms, parameter 0 is for package name, 1 is for node
notificationACNCompositeTitleEXECUTING=Поставувањето на {0} во {1} е во тек.
#XMSG ACN job title for composite job- publish to ctms completed with warnings, parameter 0 is for package name, 1 is for node
notificationACNCompositeTitleWARNING=Поставувањето на {0} во {1} заврши со предупредување.
#XMSG ACN job title for completed composite job- publish to ctms, parameter 0 is for package name, 1 is for node
notificationACNCompositeTitleDONE=Поставувањето на {0} во {1} заврши.
#XMSG ACN job title for failed composite job- publish to ctms, parameter 0 is for package name, 1 is for node
notificationACNCompositeTitleFAILED=Поставувањето на {0} во {1} не успеа.
#YMSG ACN job details for jobs, parameter 0 is for current number of objects, parameter 1 for total number of objects
notificationACNBody=Обработени објекти {0} / {1}
#XMSG: ACN .Package validity date.
ACN_PACKAGE_DOWNLOAD_VALIDTY=Пакетот е важечки за поставување на Мрежата за содржина до {0}.
# ~~~~~~ End of ACN Notifications ~~~~~~

# ~~~~~~ Start of DP Agent Notifications ~~~~~~
#XTXT: DP Agent Connection Status Notification - connected
DPAgentConnectionStatusNotificationConnected=Агентот за дистрибуција на податоци {0} е поврзан. 
#XTXT: DP Agent Connection Status Notification - disconnected
DPAgentConnectionStatusNotificationDisconnected=Агентот за дистрибуција на податоци {0} не е поврзан. Направете проверка.
# ~~~~~~ End of DP Agent Notifications ~~~~~~

# ~~~~~~ Start of Spaces Notifications ~~~~~~
#XMSG Notification title for a successful space deployment
spaceDeploymentSuccessful=Просторот „{0}“ е применет.
#XMSG Notification title for an unsuccessful space deployment
spaceDeploymentUnsuccessful=Просторот „{0}“ не може да се примени.
#XMSG Notification title for an unsuccessful space content copy
spaceContentCopyUnsuccessful=Просторот „{1}“ е создаден и применет но објектите не можат да се копираат од просторот „{0}“ во просторот „{1}“.
#XMSG Notification title for an unsuccessful space content deployment
spaceContentDeploymentUnsuccessful=Просторот „{1}“ е создаден и применет. Сите објекти се копирани од просторот „{0}“ во просторот „{1}“, но некои не може да се применат.
#XMSG Notification body for a successful space deployment
openSpace=Отвори {0}.
#XMSG Notification title for a successful copy space and deployment
spaceCopyAndDeploySuccessful=Просторот „{0}“ е ископиран во просторот „{1}“. Објектите од просторот „{1}“ се применети.
#XMSG Notification title for a successful copy of space content
spaceContentCopySuccessful=Просторот „{1}“ е создаден и применет. Сите објекти се копирани од просторот „{0}“ во просторот „{1}“.
#XMSG Notification title for a successful deployment of space content
spaceContentCopyAndDeploymentSuccessful=Просторот „{1}“ е создаден и применет. Сите објекти се копирани од просторот „{0}“ во просторот „{1}“ и се применети.
#XMSG Notification title for a successful space deletion
spaceDeletionSuccessful=Просторот „{0}“ е избришан.
# ~~~~~~ End of Spaces Notifications ~~~~~~

# ~~~~~~ Start of Cross Architecture Notifications ~~~~~~
#XMSG: Notification message to inform the user that a new survey is available
longSurveyInitialNotification=Здраво {0}, кажете ни како ни оди и споделете ги Вашите мислења преку нашата анкета за корисничко искуство. За претстојните три недели, може да пристапите до анкетата со кликнување на „Сподели повратни информации“ во лентата од процесорот на наредби.
#XMSG: Notification reminder message to inform the user that the long survey is about to expire
longSurveyReminderNotification=Здраво {0}, имате уште {1} дена за да ја пополните нашата анкета. Ви благодариме ако веќе сте ја завршиле.
#XMSG: Notification reminder message to inform the user that the long survey is about to expire tomorrow
longSurveySecondLastReminderNotification=Здраво {0}, имате уште 1 ден за да ја пополните нашата анкета. Ви благодариме ако веќе сте ја завршиле.
#XMSG: Notification reminder message to inform the user that the long survey is about to expire today.
longSurveyLastReminderNotification=Здраво {0}, денес имате последна шанса да ја пополните нашата анкета. Ви благодариме ако веќе сте ја завршиле.
# ~~~~~~ End of Cross Architecture Notifications ~~~~~~

# ~~~~~~ Start of Cockpit Notifications ~~~~~~
#XMSG: Notification message to inform the user that a activation failed
packageFailedInstallation=Активацијата на пакетот со податоци {0} не успеа. Обидете се повторно подоцна.
#XMSG: Notification message to inform the user that a activation succeeded
packageInstalled=Пакетот со податоци {0} е активиран.
#XMSG: Notification message to inform the user that a deactivation failed
packageFailedUninstallation=Деактивацијата на пакетот со податоци {0} не успеа. Обидете се повторно подоцна.
#XMSG: Notification message to inform the user that a deactivation succeeded
packageUninstalled=Пакетот со податоци {0} е деактивиран.
# ~~~~~~ End of Cockpit Notifications ~~~~~~


#### Begin Catalog component

#XMSG: Notification reminder message to inform the user that the import is successful. {0} is the file name of the import
txtOdcCatalogImportResultSuccessful={0} е увезено.
#YMSG: Notification reminder message to inform the user that the import is unsuccessful. {0} is the file name of the import
errOdcCatalogImportResultFailed=Увезувањето на {0} е неуспешно.

#XMSG: Notification reminder title for sharing a data product. {0} is the share name. {1} is the data product name.
txtOdcDPApiShareCreateSuccessTitle=Споделувањето {0} е создадено за податочниот производ {1}

#YMSG: Error notification reminder title for sharing a data product. {0} is the share name. {1} is the data product name.
errOdcDPApiShareCreateErrorTitle=Не може да се создаде споделување {0} за податочниот производ {1}
#YMSG: {0} optional partner specific message that is not translated.  \n will be added to the end of the placeholder (if any).
errOdcDPApiShareCreateErrorBody={0}Проверете ја страницата со детали за податочниот производ

#YMSG: Error notification reminder title for sharing a data product. {0} is the share name. {1} is the data product name.
errOdcDPApiShareCreateTimeoutTitle=Не може да се создаде споделување {0} за податочниот производ {1}
#YMSG:
errOdcDPApiShareCreateTimeoutBody=Споделувањето не успеа да заврши во очекуваното време. Проверете ја страницата со детали за податочниот производ.

#XMSG: Notification reminder title for updating a data product share. {0} is the share name. {1} is the data product name.
txtOdcDPApiShareUpdateSuccessTitle=Споделувањето {0} е ажурирано за податочниот производ {1}

#YMSG: Error notification reminder title for updating a data product share. {0} is the share name. {1} is the data product name.
errOdcDPApiShareUpdateErrorTitle=Не може да се ажурира споделувањето {0} за податочниот производ {1}
#YMSG: {0} optional partner specific message that is not translated.  \n will be added to the end of the placeholder (if any).
errOdcDPApiShareUpdateErrorBody={0}Проверете ја страницата со детали за податочниот производ

#YMSG: Error notification reminder title for updating a data product share. {0} is the share name. {1} is the data product name.
errOdcDPApiShareUpdateTimeoutTitle=Не може да се ажурира споделувањето {0} за податочниот производ {1}
#YMSG:
errOdcDPApiShareUpdateTimeoutBody=Ажурирањето не успеа да заврши во очекуваното време. Проверете ја страницата со детали за податочниот производ.

#XMSG: Notification reminder title for deleting a data product share. {0} is the share name. {1} is the data product name.
txtOdcDPApiShareDeleteSuccessTitle=Споделувањето {0} е избришано за податочниот производ {1}

#YMSG: Error notification reminder title for deleting a data product share. {0} is the share name. {1} is the data product name.
errOdcDPApiShareDeleteErrorTitle=Не може да се избрише споделувањето {0} за податочниот производ {1}
#YMSG:
errOdcDPApiShareDeleteErrorBody=Настана грешка при бришење на споделувањето. Проверете ја страницата со детали за податочниот производ.

#YMSG: Error notification reminder title for deleting a data product share. {0} is the share name. {1} is the data product name.
errOdcDPApiShareDeleteTimeoutTitle=Не може да се избрише споделувањето {0} за податочниот производ {1}
#YMSG:
errOdcDPApiShareDeleteTimeoutBody=Бришењето на споделувањето не успеа да заврши во очекуваното време. Проверете ја страницата со детали за податочниот производ.

#YMSG:
errOdcDPApiShareGenericError=Податочниот производ не може да се сподели. Проверете ја страницата со детали за податочниот производ.
#YMSG:
errOdcDPApiShareNotFoundError=Споделувањето на податочниот производ не може да се најде. Проверете ја страницата со детали за податочниот производ.

#YMSG: Notification body message to inform the user they can click on the notification to go to the data product page
txtOdcDPApiViewDataProductPageSuggestion=Проверете ја страницата со детали за податочниот производ.

#XMSG: Notification reminder title for partially sharing a data product. {0} is the share name. {1} is the data product name.
errOdcDPApiShareCreatePartiallyTitle=Споделувањето {0} е делумно создадено за податочниот производ {1}
#YMSG:
errOdcDPApiShareCreatePartiallyBody=Дел од барањето за споделување не може да се заврши. Проверете ја страницата со детали за податочниот производ.

#XMSG: Notification reminder title for partially sharing a data product. {0} is the share name. {1} is the data product name.
errOdcDPApiShareUpdatePartiallyTitle=Споделувањето {0} е делумно ажурирано за податочниот производ {1}
#YMSG:
errOdcDPApiShareUpdatePartiallyBody=Дел од барањето за споделување не може да се заврши. Проверете ја страницата со детали за податочниот производ.

#XMSG: Notification reminder that a recovery of the system has just completed, a manual synchronization is needed to get the system up to date
txtOdcCatalogRecoverySuccessManualSyncNeeded=Заврши обновувањето на системот. Извршете рачна синхронизација за да бидете сигурни дека метаподатоците за сите каталошки ресурси се ажурирани.

#XMSG:
txtOdcSharedCatalogSetup=Закупецот сега го споделува складот за каталози со закупецот {0}

#XMSG:
txtOdcSharedCatalogRevoke=Закупецот престана да го споделува складот за каталози со закупецот {0}

#XMSG: Notification title message indicating the execution status of a task, including execution type, applicationId, activity and objectId.
taskExecutionCompleted={0} извршување на {1}/{2} за {3} е завршено.
taskExecutionFailed={0} извршување на {1}/{2} за {3} не успеа.
#XMSG: Notification body message indicating the status of a task, including applicationId, activity, objectId and spaceId.
taskExecutionCompletedWithoutSubstatus=Задачата {0}/{1} на објектот {2} во просторот {3} е завршена.
taskExecutionFailedWithoutSubstatus=Задачата {0}/{1} на објектот {2} во просторот {3} не успеа.
#XMSG: Notification body message indicating the status and substatus of a task, including applicationId, activity, objectId, spaceId and substatus.
taskExecutionCompletedWithSubstatus=Задачата {0}/{1} на објектот {2} во просторот {3} е завршена со потстатус {4}.
taskExecutionFailedWithSubstatus=Задачата {0}/{1} на објектот {2} во просторот {3} не успеа со потстатус {4}.

#XMSG: Notification title message for task chain indicating the execution status of a task, including execution type, applicationId, activity and objectId.
taskChainExecutionCompleted={0} извршување на {1}/{2} за {3} е завршено.
taskChainExecutionFailed={0} извршување на {1}/{2} за {3} не успеа.
#XMSG: Notification body message for task chain indicating the execution status of a task, including applicationId, activity, objectId and spaceId.
taskChainExecutionCompletedWithoutSubstatus=Задачата {0}/{1} на објектот {2} во просторот {3} е завршена.
taskChainExecutionFailedWithoutSubstatus=Задачата {0}/{1} на објектот {2} во просторот {3} не успеа.
#XMSG: Notification body message for task chain indicating the execution status and substatus of a task, including applicationId, activity, objectId, spaceId and substatus.
taskChainExecutionCompletedWithSubstatus=Задачата {0}/{1} на објектот {2} во просторот {3} е завршена со потстатус {4}.
taskChainExecutionFailedWithSubstatus=Задачата {0}/{1} на објектот {2} во просторот {3} не успеа со потстатус {4}.

# XMSG: Notification for Completed Schedules
scheduleCompletedPause=Број на паузирани распореди: {0}.
scheduleCompletedResume=Број на продолжени распореди: {0}.
scheduleCompletedDelete=Број на избришани распореди: {0}.
scheduleCompletedChangeOwner=Број на распореди со ажурирана сопственост: {0}.

# XMSG: Notification for Failed Schedules
scheduleFailedPause=Број на распореди што не успеаја да се паузираат: {0} од {1}.
scheduleFailedResume=Број на распореди што не успеаја да продолжат: {0} од {1}.
scheduleFailedDelete=Број на распореди што не успеаја да се избришат: {0} од {1}.
scheduleFailedChangeOwner=Број на распореди со неуспешно ажурирање на сопственоста: {0} од {1}.

#### End Catalog component
