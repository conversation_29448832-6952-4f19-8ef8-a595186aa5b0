###################################################################
### Translation Texts for Notifications coming from the Backend ###
### Please maintain a section for each team/tool                ###
###################################################################

# ~~~~~~ Start of ACN Notifications ~~~~~~
#XMSG ACN job title for pending import jobs, parameter 0 is for package name
notificationACNImportTitlePENDING=La importación del paquete {0} está pendiente
#XMSG ACN job title for running import jobs, parameter 0 is for package name
notificationACNImportTitleEXECUTING=La importación del paquete {0} está en curso
#XMSG ACN job title for completed import jobs with warnings, parameter 0 is for package name
notificationACNImportTitleWARNING=La importación del paquete {0} ha concluido con advertencia
#XMSG ACN job title for completed import jobs, parameter 0 is for package name
notificationACNImportTitleDONE=La importación del paquete {0} ha concluido
#XMSG ACN job title for failed import jobs, parameter 0 is for package name
notificationACNImportTitleFAILED=Error en la importación del paquete {0}
#XMSG ACN job title for pending export jobs, parameter 0 is for package name
notificationACNExportTitlePENDING=La exportación del paquete {0} está pendiente
#XMSG ACN job title for running export jobs, parameter 0 is for package name
notificationACNExportTitleEXECUTING=La exportación del paquete {0} está en curso
#XMSG ACN job title for completed export jobs with warnings, parameter 0 is for package name
notificationACNExportTitleWARNING=La exportación del paquete {0} ha concluido con advertencia
#XMSG ACN job title for completed export jobs, parameter 0 is for package name
notificationACNExportTitleDONE=La exportación del paquete {0} ha concluido
#XMSG ACN job title for failed export jobs, parameter 0 is for package name
notificationACNExportTitleFAILED=Error en la exportación del paquete {0}
#XMSG ACN job title for pending download jobs, parameter 0 is for package name
notificationACNDownloadTitlePENDING=La descarga del paquete {0} está pendiente de ejecución
#XMSG ACN job title for running download jobs, parameter 0 is for package name
notificationACNDownloadTitleEXECUTING=La descarga del paquete {0} está en curso
#XMSG ACN job title for completed download jobs with warnings, parameter 0 is for package name
notificationACNDownloadTitleWARNING=La descarga del paquete {0} ha concluido con advertencia
#XMSG ACN job title for completed download jobs, parameter 0 is for package name
notificationACNDownloadTitleDONE=La descarga del paquete {0} ha concluido
#XMSG ACN job title for Downloaded download jobs, parameter 0 is for package name
notificationACNDownloadTitleDOWNLOADED=La descarga del paquete {0} ha concluido
#XMSG ACN job title for failed download jobs, parameter 0 is for package name
notificationACNDownloadTitleFAILED=No se ha podido descargar el paquete {0}
#XMSG ACN job title for pending upload jobs, parameter 0 is for package name
notificationACNUploadTitlePENDING=La carga del paquete {0} está pendiente de ejecución
#XMSG ACN job title for running upload jobs, parameter 0 is for package name
notificationACNUploadTitleEXECUTING=La carga del paquete {0} está en curso
#XMSG ACN job title for completed upload jobs with warnings, parameter 0 is for package name
notificationACNUploadTitleWARNING=La carga del paquete {0} ha concluido con advertencia
#XMSG ACN job title for completed upload jobs, parameter 0 is for package name
notificationACNUploadTitleDONE=La carga del paquete {0} ha concluido
#XMSG ACN job title for failed upload jobs, parameter 0 is for package name
notificationACNUploadTitleFAILED=No se ha podido cargar el paquete {0}
#XMSG ACN job title for pending upload jobs, parameter 0 is for package name
notificationACNGitPullTitlePENDING=El pull del paquete {0} está pendiente de ejecución
#XMSG ACN job title for running upload jobs, parameter 0 is for package name
notificationACNGitPullTitleEXECUTING=El pull del paquete {0} está en curso
#XMSG ACN job title for completed upload jobs with warnings, parameter 0 is for package name
notificationACNGitPullTitleWARNING=El pull del paquete {0} ha concluido con advertencia
#XMSG ACN job title for completed upload jobs, parameter 0 is for package name
notificationACNGitPullTitleDONE=El pull del paquete {0} ha concluido
#XMSG ACN job title for failed upload jobs, parameter 0 is for package name
notificationACNGitPullTitleFAILED=No se ha podido llevar a cabo el pull del paquete {0}
#XMSG ACN job title for pending upload jobs, parameter 0 is for package name
notificationACNGitPushTitlePENDING=El push del paquete {0} está pendiente de ejecución
#XMSG ACN job title for running upload jobs, parameter 0 is for package name
notificationACNGitPushTitleEXECUTING=El push del paquete {0} está en curso
#XMSG ACN job title for completed upload jobs with warnings, parameter 0 is for package name
notificationACNGitPushTitleWARNING=El push del paquete {0} ha concluido con advertencia
#XMSG ACN job title for completed upload jobs, parameter 0 is for package name
notificationACNGitPushTitleDONE=El push del paquete {0} ha concluido
#XMSG ACN job title for failed upload jobs, parameter 0 is for package name
notificationACNGitPushTitleFAILED=No se ha podido llevar a cabo el push del paquete {0}
#XMSG ACN job title for pending composite job- publish to ctms, parameter 0 is for package name, 1 is for node
notificationACNCompositeTitlePENDING=La carga de {0} en {1} está pendiente de ejecutarse.
#XMSG ACN job title for running composite job- publish to ctms, parameter 0 is for package name, 1 is for node
notificationACNCompositeTitleEXECUTING=La carga de {0} en {1} está en curso.
#XMSG ACN job title for composite job- publish to ctms completed with warnings, parameter 0 is for package name, 1 is for node
notificationACNCompositeTitleWARNING=La carga de {0} en {1} ha concluido con una advertencia.
#XMSG ACN job title for completed composite job- publish to ctms, parameter 0 is for package name, 1 is for node
notificationACNCompositeTitleDONE=La carga de {0} en {1} ha concluido.
#XMSG ACN job title for failed composite job- publish to ctms, parameter 0 is for package name, 1 is for node
notificationACNCompositeTitleFAILED=No se ha podido cargar {0} en {1}.
#YMSG ACN job details for jobs, parameter 0 is for current number of objects, parameter 1 for total number of objects
notificationACNBody=Objetos procesados: {0} / {1}
#XMSG: ACN .Package validity date.
ACN_PACKAGE_DOWNLOAD_VALIDTY=El paquete es válido para cargarlo en la red de contenido hasta el {0}.
# ~~~~~~ End of ACN Notifications ~~~~~~

# ~~~~~~ Start of DP Agent Notifications ~~~~~~
#XTXT: DP Agent Connection Status Notification - connected
DPAgentConnectionStatusNotificationConnected=El agente de aprovisionamiento de datos {0} está conectado.
#XTXT: DP Agent Connection Status Notification - disconnected
DPAgentConnectionStatusNotificationDisconnected=El agente de aprovisionamiento de datos {0} está desconectado. Compruébelo.
# ~~~~~~ End of DP Agent Notifications ~~~~~~

# ~~~~~~ Start of Spaces Notifications ~~~~~~
#XMSG Notification title for a successful space deployment
spaceDeploymentSuccessful=Se ha desplegado el espacio "{0}".
#XMSG Notification title for an unsuccessful space deployment
spaceDeploymentUnsuccessful=No se ha podido desplegar el espacio "{0}".
#XMSG Notification title for an unsuccessful space content copy
spaceContentCopyUnsuccessful=Se ha creado e implementado el espacio "{1}", pero los objetos no se pudieron copiar desde el espacio "{0}" hasta el espacio "{1}".
#XMSG Notification title for an unsuccessful space content deployment
spaceContentDeploymentUnsuccessful=Se ha creado e implementado el espacio "{1}". Se han copiado todos los objetos desde el espacio "{0}" hasta el espacio "{1}", pero algunos no se pudieron implementar.
#XMSG Notification body for a successful space deployment
openSpace=Abra {0}.
#XMSG Notification title for a successful copy space and deployment
spaceCopyAndDeploySuccessful=El espacio "{0}" se ha copiado en el espacio "{1}". Se han desplegado los objetos del espacio "{1}".
#XMSG Notification title for a successful copy of space content
spaceContentCopySuccessful=Se ha creado e implementado el espacio "{1}", pero los objetos no se pudieron copiar desde el espacio "{0}" hasta el espacio "{1}".
#XMSG Notification title for a successful deployment of space content
spaceContentCopyAndDeploymentSuccessful=Se ha creado e implementado el espacio "{1}". Se han copiado todos los objetos desde el espacio "{0}" hasta el espacio "{1}" y se han implementado.
#XMSG Notification title for a successful space deletion
spaceDeletionSuccessful=Se ha eliminado el espacio "{0}".
# ~~~~~~ End of Spaces Notifications ~~~~~~

# ~~~~~~ Start of Cross Architecture Notifications ~~~~~~
#XMSG: Notification message to inform the user that a new survey is available
longSurveyInitialNotification=Hola, {0}: Explíquenos cómo lo estamos haciendo y denos su opinión en nuestra encuesta sobre la experiencia del usuario. Durante las próximas tres semanas, podrá acceder a la encuesta haciendo clic en "Proporcionar feedback" en la barra shell.
#XMSG: Notification reminder message to inform the user that the long survey is about to expire
longSurveyReminderNotification=Hola, {0}: Le quedan {1} días para hacer nuestra encuesta. Le damos las gracias si ya la ha hecho.
#XMSG: Notification reminder message to inform the user that the long survey is about to expire tomorrow
longSurveySecondLastReminderNotification=Hola, {0}: Le queda 1 día para hacer nuestra encuesta. Le damos las gracias si ya la ha hecho.
#XMSG: Notification reminder message to inform the user that the long survey is about to expire today.
longSurveyLastReminderNotification=Hola, {0}: Hoy es el último día en el que podrá hacer nuestra encuesta. Le damos las gracias si ya la ha hecho.
# ~~~~~~ End of Cross Architecture Notifications ~~~~~~

# ~~~~~~ Start of Cockpit Notifications ~~~~~~
#XMSG: Notification message to inform the user that a activation failed
packageFailedInstallation=No se ha podido activar el paquete de datos {0}. Inténtelo de nuevo más tarde.
#XMSG: Notification message to inform the user that a activation succeeded
packageInstalled=Se ha activado el paquete de datos {0}.
#XMSG: Notification message to inform the user that a deactivation failed
packageFailedUninstallation=No se ha podido desactivar del paquete de datos {0}. Inténtelo de nuevo más tarde.
#XMSG: Notification message to inform the user that a deactivation succeeded
packageUninstalled=Se ha desactivado el paquete de datos {0}.
# ~~~~~~ End of Cockpit Notifications ~~~~~~


#### Begin Catalog component

#XMSG: Notification reminder message to inform the user that the import is successful. {0} is the file name of the import
txtOdcCatalogImportResultSuccessful={0} se ha importado.
#YMSG: Notification reminder message to inform the user that the import is unsuccessful. {0} is the file name of the import
errOdcCatalogImportResultFailed=Error al importar {0}.

#XMSG: Notification reminder title for sharing a data product. {0} is the share name. {1} is the data product name.
txtOdcDPApiShareCreateSuccessTitle=Se ha creado la compartición {0} para el producto de datos {1}

#YMSG: Error notification reminder title for sharing a data product. {0} is the share name. {1} is the data product name.
errOdcDPApiShareCreateErrorTitle=No se ha podido crear la compartición {0} para el producto de datos {1}
#YMSG: {0} optional partner specific message that is not translated.  \n will be added to the end of the placeholder (if any).
errOdcDPApiShareCreateErrorBody={0}Consulte la página de detalles del producto de datos

#YMSG: Error notification reminder title for sharing a data product. {0} is the share name. {1} is the data product name.
errOdcDPApiShareCreateTimeoutTitle=No se ha podido crear la compartición {0} para el producto de datos {1}
#YMSG:
errOdcDPApiShareCreateTimeoutBody=No se ha podido efectuar la operación de compartir en el período de tiempo previsto. Consulte la página de detalles del producto de datos.

#XMSG: Notification reminder title for updating a data product share. {0} is the share name. {1} is the data product name.
txtOdcDPApiShareUpdateSuccessTitle=Se ha actualizado la compartición {0} para el producto de datos {1}

#YMSG: Error notification reminder title for updating a data product share. {0} is the share name. {1} is the data product name.
errOdcDPApiShareUpdateErrorTitle=No se ha podido actualizar la compartición {0} para el producto de datos {1}
#YMSG: {0} optional partner specific message that is not translated.  \n will be added to the end of the placeholder (if any).
errOdcDPApiShareUpdateErrorBody={0}Consulte la página de detalles del producto de datos

#YMSG: Error notification reminder title for updating a data product share. {0} is the share name. {1} is the data product name.
errOdcDPApiShareUpdateTimeoutTitle=No se ha podido actualizar la compartición {0} para el producto de datos {1}
#YMSG:
errOdcDPApiShareUpdateTimeoutBody=No se ha podido efectuar la operación de actualizar en el período de tiempo previsto. Consulte la página de detalles del producto de datos.

#XMSG: Notification reminder title for deleting a data product share. {0} is the share name. {1} is the data product name.
txtOdcDPApiShareDeleteSuccessTitle=Se ha eliminado la compartición {0} para el producto de datos {1}

#YMSG: Error notification reminder title for deleting a data product share. {0} is the share name. {1} is the data product name.
errOdcDPApiShareDeleteErrorTitle=No se ha podido eliminar la compartición {0} para el producto de datos {1}
#YMSG:
errOdcDPApiShareDeleteErrorBody=Se ha producido un error al eliminar la compartición. Consulte la página de detalles del producto de datos.

#YMSG: Error notification reminder title for deleting a data product share. {0} is the share name. {1} is the data product name.
errOdcDPApiShareDeleteTimeoutTitle=No se ha podido eliminar la compartición {0} para el producto de datos {1}
#YMSG:
errOdcDPApiShareDeleteTimeoutBody=No se ha podido eliminar la compartición en el período de tiempo previsto. Consulte la página de detalles del producto de datos.

#YMSG:
errOdcDPApiShareGenericError=No se ha podido compartir el producto de datos. Consulte la página de detalles del producto de datos.
#YMSG:
errOdcDPApiShareNotFoundError=No se ha encontrado la compartición del producto de datos. Consulte la página de detalles del producto de datos.

#YMSG: Notification body message to inform the user they can click on the notification to go to the data product page
txtOdcDPApiViewDataProductPageSuggestion=Consulte la página de detalles del producto de datos.

#XMSG: Notification reminder title for partially sharing a data product. {0} is the share name. {1} is the data product name.
errOdcDPApiShareCreatePartiallyTitle=Se ha creado la compartición {0} parcialmente para el producto de datos {1}
#YMSG:
errOdcDPApiShareCreatePartiallyBody=No se ha podido completar parte de la solicitud de compartición. Consulte la página de detalles del producto de datos.

#XMSG: Notification reminder title for partially sharing a data product. {0} is the share name. {1} is the data product name.
errOdcDPApiShareUpdatePartiallyTitle=Se ha actualizado la compartición {0} parcialmente para el producto de datos {1}
#YMSG:
errOdcDPApiShareUpdatePartiallyBody=No se ha podido completar parte de la solicitud de compartición. Consulte la página de detalles del producto de datos.

#XMSG: Notification reminder that a recovery of the system has just completed, a manual synchronization is needed to get the system up to date
txtOdcCatalogRecoverySuccessManualSyncNeeded=La recuperación del sistema ha finalizado. Ejecute una sincronización manual para garantizar que los metadatos de todos los objetos del catálogo estén actualizados.

#XMSG:
txtOdcSharedCatalogSetup=El arrendatario ahora está compartiendo el almacenamiento del catálogo con el arrendatario {0}

#XMSG:
txtOdcSharedCatalogRevoke=El arrendatario ha detenido la compartición del almacenamiento del catálogo con el arrendatario {0}

#XMSG: Notification title message indicating the execution status of a task, including execution type, applicationId, activity and objectId.
taskExecutionCompleted=Se ha completado {0} ejecución de {1}/{2} para {3}.
taskExecutionFailed=Se ha producido un error en {0} ejecución de {1}/{2} para {3}.
#XMSG: Notification body message indicating the status of a task, including applicationId, activity, objectId and spaceId.
taskExecutionCompletedWithoutSubstatus=Se ha completado la tarea {0}/{1} en el objeto {2} en el espacio {3}.
taskExecutionFailedWithoutSubstatus=Se ha producido un error en la tarea {0}/{1} en el objeto {2} en el espacio {3}.
#XMSG: Notification body message indicating the status and substatus of a task, including applicationId, activity, objectId, spaceId and substatus.
taskExecutionCompletedWithSubstatus=Se ha completado la tarea {0}/{1} en el objeto {2} en el espacio {3} con el subestado {4}.
taskExecutionFailedWithSubstatus=Se ha producido un error en la tarea {0}/{1} en el objeto {2} en el espacio {3} con el subestado {4}.

#XMSG: Notification title message for task chain indicating the execution status of a task, including execution type, applicationId, activity and objectId.
taskChainExecutionCompleted=Se ha completado {0} ejecución de {1}/{2} para {3}.
taskChainExecutionFailed=Se ha producido un error en {0} ejecución de {1}/{2} para {3}.
#XMSG: Notification body message for task chain indicating the execution status of a task, including applicationId, activity, objectId and spaceId.
taskChainExecutionCompletedWithoutSubstatus=Se ha completado la tarea {0}/{1} en el objeto {2} en el espacio {3}.
taskChainExecutionFailedWithoutSubstatus=Se ha producido un error en la tarea {0}/{1} en el objeto {2} en el espacio {3}.
#XMSG: Notification body message for task chain indicating the execution status and substatus of a task, including applicationId, activity, objectId, spaceId and substatus.
taskChainExecutionCompletedWithSubstatus=Se ha completado la tarea {0}/{1} en el objeto {2} en el espacio {3} con el subestado {4}.
taskChainExecutionFailedWithSubstatus=Se ha producido un error en la tarea {0}/{1} en el objeto {2} en el espacio {3} con el subestado {4}.

# XMSG: Notification for Completed Schedules
scheduleCompletedPause=Número de programaciones en pausa: {0}.
scheduleCompletedResume=Número de programaciones reanudadas: {0}.
scheduleCompletedDelete=Número de programaciones eliminadas: {0}.
scheduleCompletedChangeOwner=Número de programaciones con propiedad actualizada: {0}.

# XMSG: Notification for Failed Schedules
scheduleFailedPause=Número de programaciones que no se han podido interrumpir: {0} de {1}.
scheduleFailedResume=Número de programaciones que no se han podido reanudar: {0} de {1}.
scheduleFailedDelete=Número de programaciones que no se han podido eliminar: {0} de {1}.
scheduleFailedChangeOwner=Número de programaciones con actualización de propiedad fallida: {0} de {1}.

#### End Catalog component
