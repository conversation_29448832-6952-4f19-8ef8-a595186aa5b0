###################################################################
### Translation Texts for Notifications coming from the Backend ###
### Please maintain a section for each team/tool                ###
###################################################################

# ~~~~~~ Start of ACN Notifications ~~~~~~
#XMSG ACN job title for pending import jobs, parameter 0 is for package name
notificationACNImportTitlePENDING=Importação do pacote {0} pendente
#XMSG ACN job title for running import jobs, parameter 0 is for package name
notificationACNImportTitleEXECUTING=Importação do pacote {0} em curso
#XMSG ACN job title for completed import jobs with warnings, parameter 0 is for package name
notificationACNImportTitleWARNING=Importação do pacote {0} concluída com aviso
#XMSG ACN job title for completed import jobs, parameter 0 is for package name
notificationACNImportTitleDONE=Importação do pacote {0} concluída
#XMSG ACN job title for failed import jobs, parameter 0 is for package name
notificationACNImportTitleFAILED=Importação do pacote {0} falhada
#XMSG ACN job title for pending export jobs, parameter 0 is for package name
notificationACNExportTitlePENDING=Exportação do pacote {0} pendente
#XMSG ACN job title for running export jobs, parameter 0 is for package name
notificationACNExportTitleEXECUTING=Exportação do pacote {0} em curso
#XMSG ACN job title for completed export jobs with warnings, parameter 0 is for package name
notificationACNExportTitleWARNING=Exportação do pacote {0} concluída com aviso
#XMSG ACN job title for completed export jobs, parameter 0 is for package name
notificationACNExportTitleDONE=Exportação do pacote {0} concluída
#XMSG ACN job title for failed export jobs, parameter 0 is for package name
notificationACNExportTitleFAILED=Exportação do pacote {0} falhada
#XMSG ACN job title for pending download jobs, parameter 0 is for package name
notificationACNDownloadTitlePENDING=Transferência do pacote {0} pendente para execução
#XMSG ACN job title for running download jobs, parameter 0 is for package name
notificationACNDownloadTitleEXECUTING=Transferência do pacote {0} em curso
#XMSG ACN job title for completed download jobs with warnings, parameter 0 is for package name
notificationACNDownloadTitleWARNING=Transferência do pacote {0} concluída com aviso
#XMSG ACN job title for completed download jobs, parameter 0 is for package name
notificationACNDownloadTitleDONE=Transferência do pacote {0} concluída
#XMSG ACN job title for Downloaded download jobs, parameter 0 is for package name
notificationACNDownloadTitleDOWNLOADED=Transferência do pacote {0} concluída
#XMSG ACN job title for failed download jobs, parameter 0 is for package name
notificationACNDownloadTitleFAILED=Transferência do pacote {0} falhada
#XMSG ACN job title for pending upload jobs, parameter 0 is for package name
notificationACNUploadTitlePENDING=Carregamento do pacote {0} pendente para execução
#XMSG ACN job title for running upload jobs, parameter 0 is for package name
notificationACNUploadTitleEXECUTING=Carregamento do pacote {0} em curso
#XMSG ACN job title for completed upload jobs with warnings, parameter 0 is for package name
notificationACNUploadTitleWARNING=Carregamento do pacote {0} concluído com aviso
#XMSG ACN job title for completed upload jobs, parameter 0 is for package name
notificationACNUploadTitleDONE=Carregamento do pacote {0} concluído
#XMSG ACN job title for failed upload jobs, parameter 0 is for package name
notificationACNUploadTitleFAILED=Carregamento do pacote {0} falhado
#XMSG ACN job title for pending upload jobs, parameter 0 is for package name
notificationACNGitPullTitlePENDING=Pull do pacote {0} pendente para execução
#XMSG ACN job title for running upload jobs, parameter 0 is for package name
notificationACNGitPullTitleEXECUTING=Pull do pacote {0} em curso
#XMSG ACN job title for completed upload jobs with warnings, parameter 0 is for package name
notificationACNGitPullTitleWARNING=Pull do pacote {0} concluído com aviso
#XMSG ACN job title for completed upload jobs, parameter 0 is for package name
notificationACNGitPullTitleDONE=Pull do pacote {0} concluído
#XMSG ACN job title for failed upload jobs, parameter 0 is for package name
notificationACNGitPullTitleFAILED=Pull do pacote {0} falhado
#XMSG ACN job title for pending upload jobs, parameter 0 is for package name
notificationACNGitPushTitlePENDING=Push do pacote {0} pendente para execução
#XMSG ACN job title for running upload jobs, parameter 0 is for package name
notificationACNGitPushTitleEXECUTING=Push do pacote {0} em curso
#XMSG ACN job title for completed upload jobs with warnings, parameter 0 is for package name
notificationACNGitPushTitleWARNING=Push do pacote {0} concluído com aviso
#XMSG ACN job title for completed upload jobs, parameter 0 is for package name
notificationACNGitPushTitleDONE=Push do pacote {0} concluído
#XMSG ACN job title for failed upload jobs, parameter 0 is for package name
notificationACNGitPushTitleFAILED=Push do pacote {0} falhado
#XMSG ACN job title for pending composite job- publish to ctms, parameter 0 is for package name, 1 is for node
notificationACNCompositeTitlePENDING=O carregamento de {0} para {1} tem a execução pendente.
#XMSG ACN job title for running composite job- publish to ctms, parameter 0 is for package name, 1 is for node
notificationACNCompositeTitleEXECUTING=O carregamento de {0} para {1} está em curso.
#XMSG ACN job title for composite job- publish to ctms completed with warnings, parameter 0 is for package name, 1 is for node
notificationACNCompositeTitleWARNING=O carregamento de {0} para {1} foi concluído com um aviso.
#XMSG ACN job title for completed composite job- publish to ctms, parameter 0 is for package name, 1 is for node
notificationACNCompositeTitleDONE=O carregamento de {0} para {1} foi concluído.
#XMSG ACN job title for failed composite job- publish to ctms, parameter 0 is for package name, 1 is for node
notificationACNCompositeTitleFAILED=O carregamento de {0} para {1} falhou.
#YMSG ACN job details for jobs, parameter 0 is for current number of objects, parameter 1 for total number of objects
notificationACNBody=Objetos processados {0} / {1}
#XMSG: ACN .Package validity date.
ACN_PACKAGE_DOWNLOAD_VALIDTY=O pacote é válido para carregamento para a rede de conteúdo até {0}.
# ~~~~~~ End of ACN Notifications ~~~~~~

# ~~~~~~ Start of DP Agent Notifications ~~~~~~
#XTXT: DP Agent Connection Status Notification - connected
DPAgentConnectionStatusNotificationConnected=O agente de provisionamento de dados {0} está ligado.
#XTXT: DP Agent Connection Status Notification - disconnected
DPAgentConnectionStatusNotificationDisconnected=O agente de provisionamento de dados {0} está desligado. Verifique.
# ~~~~~~ End of DP Agent Notifications ~~~~~~

# ~~~~~~ Start of Spaces Notifications ~~~~~~
#XMSG Notification title for a successful space deployment
spaceDeploymentSuccessful=O espaço "{0}" foi implementado.
#XMSG Notification title for an unsuccessful space deployment
spaceDeploymentUnsuccessful=Não foi possível implementar o espaço "{0}".
#XMSG Notification title for an unsuccessful space content copy
spaceContentCopyUnsuccessful=Espaço "{1}" criado e implementado, mas não foi possível copiar objetos do espaço "{0}" para o espaço "{1}".
#XMSG Notification title for an unsuccessful space content deployment
spaceContentDeploymentUnsuccessful=Espaço "{1}" criado e implementado. Todos os objetos copiados do espaço "{0}" para o espaço "{1}" mas não foi possível implementar alguns.
#XMSG Notification body for a successful space deployment
openSpace=Abrir {0}.
#XMSG Notification title for a successful copy space and deployment
spaceCopyAndDeploySuccessful=O espaço "{0}" foi copiado para o espaço "{1}". Os objetos no espaço "{1}" foram implementados.
#XMSG Notification title for a successful copy of space content
spaceContentCopySuccessful=Espaço "{1}" criado e implementado. Todos os objetos copiados do espaço "{0}" para o espaço "{1}".
#XMSG Notification title for a successful deployment of space content
spaceContentCopyAndDeploymentSuccessful=Espaço "{1}" criado e implementado. Todos os objetos copiados do espaço "{0}" para o espaço "{1}" e implementados.
#XMSG Notification title for a successful space deletion
spaceDeletionSuccessful=O espaço "{0}" foi eliminado.
# ~~~~~~ End of Spaces Notifications ~~~~~~

# ~~~~~~ Start of Cross Architecture Notifications ~~~~~~
#XMSG: Notification message to inform the user that a new survey is available
longSurveyInitialNotification=Olá {0}, dê a sua opinião sobre o nosso desempenho e partilhe as suas ideias através do nosso inquérito sobre a experiência do usuário. Durante as próximas três semanas, pode aceder ao inquérito ao clicar em "Dar feedback" na barra de cabeçalho.
#XMSG: Notification reminder message to inform the user that the long survey is about to expire
longSurveyReminderNotification=Olá {0}, tem {1} dias para realizar o nosso inquérito. Agradecemos, caso já tenha contribuído.
#XMSG: Notification reminder message to inform the user that the long survey is about to expire tomorrow
longSurveySecondLastReminderNotification=Olá {0}, tem 1 dia para realizar o nosso inquérito. Agradecemos, caso já tenha contribuído.
#XMSG: Notification reminder message to inform the user that the long survey is about to expire today.
longSurveyLastReminderNotification=Olá {0}, hoje é a sua última oportunidade para realizar o nosso inquérito. Agradecemos, caso já tenha contribuído.
# ~~~~~~ End of Cross Architecture Notifications ~~~~~~

# ~~~~~~ Start of Cockpit Notifications ~~~~~~
#XMSG: Notification message to inform the user that a activation failed
packageFailedInstallation=A ativação do pacote de dados {0} falhou. Tente novamente mais tarde.
#XMSG: Notification message to inform the user that a activation succeeded
packageInstalled=O pacote de dados {0} foi ativado.
#XMSG: Notification message to inform the user that a deactivation failed
packageFailedUninstallation=A desativação do pacote de dados {0} falhou. Tente novamente mais tarde.
#XMSG: Notification message to inform the user that a deactivation succeeded
packageUninstalled=O pacote de dados {0} foi desativado.
# ~~~~~~ End of Cockpit Notifications ~~~~~~


#### Begin Catalog component

#XMSG: Notification reminder message to inform the user that the import is successful. {0} is the file name of the import
txtOdcCatalogImportResultSuccessful={0} foi importado.
#YMSG: Notification reminder message to inform the user that the import is unsuccessful. {0} is the file name of the import
errOdcCatalogImportResultFailed=A importação de {0} falhou.

#XMSG: Notification reminder title for sharing a data product. {0} is the share name. {1} is the data product name.
txtOdcDPApiShareCreateSuccessTitle=A partilha {0} foi criada para o produto de dados {1}

#YMSG: Error notification reminder title for sharing a data product. {0} is the share name. {1} is the data product name.
errOdcDPApiShareCreateErrorTitle=Não foi possível criar a partilha {0} para o produto de dados {1}
#YMSG: {0} optional partner specific message that is not translated.  \n will be added to the end of the placeholder (if any).
errOdcDPApiShareCreateErrorBody={0}Verifique a página de detalhes do produto de dados

#YMSG: Error notification reminder title for sharing a data product. {0} is the share name. {1} is the data product name.
errOdcDPApiShareCreateTimeoutTitle=Não foi possível criar a partilha {0} para o produto de dados {1}
#YMSG:
errOdcDPApiShareCreateTimeoutBody=A partilha não foi concluída no prazo previsto. Verifique a página de detalhes do produto de dados.

#XMSG: Notification reminder title for updating a data product share. {0} is the share name. {1} is the data product name.
txtOdcDPApiShareUpdateSuccessTitle=A partilha {0} foi atualizada para o produto de dados {1}

#YMSG: Error notification reminder title for updating a data product share. {0} is the share name. {1} is the data product name.
errOdcDPApiShareUpdateErrorTitle=Não foi possível atualizar a partilha {0} para o produto de dados {1}
#YMSG: {0} optional partner specific message that is not translated.  \n will be added to the end of the placeholder (if any).
errOdcDPApiShareUpdateErrorBody={0}Verifique a página de detalhes do produto de dados

#YMSG: Error notification reminder title for updating a data product share. {0} is the share name. {1} is the data product name.
errOdcDPApiShareUpdateTimeoutTitle=Não foi possível atualizar a partilha {0} para o produto de dados {1}
#YMSG:
errOdcDPApiShareUpdateTimeoutBody=A atualização não foi concluída no prazo previsto. Verifique a página de detalhes do produto de dados.

#XMSG: Notification reminder title for deleting a data product share. {0} is the share name. {1} is the data product name.
txtOdcDPApiShareDeleteSuccessTitle=A partilha {0} foi eliminada para o produto de dados {1}

#YMSG: Error notification reminder title for deleting a data product share. {0} is the share name. {1} is the data product name.
errOdcDPApiShareDeleteErrorTitle=Não foi possível eliminar a partilha {0} para o produto de dados {1}
#YMSG:
errOdcDPApiShareDeleteErrorBody=Ocorreu um erro ao eliminar a partilha. Verifique a página de detalhes do produto de dados.

#YMSG: Error notification reminder title for deleting a data product share. {0} is the share name. {1} is the data product name.
errOdcDPApiShareDeleteTimeoutTitle=Não foi possível eliminar a partilha {0} para o produto de dados {1}
#YMSG:
errOdcDPApiShareDeleteTimeoutBody=Falha ao eliminar a partilha no prazo previsto. Verifique a página de detalhes do produto de dados.

#YMSG:
errOdcDPApiShareGenericError=Não foi possível partilhar o produto de dados. Verifique a página de detalhes do produto de dados.
#YMSG:
errOdcDPApiShareNotFoundError=Não foi possível encontrar a partilha do produto de dados. Verifique a página de detalhes do produto de dados.

#YMSG: Notification body message to inform the user they can click on the notification to go to the data product page
txtOdcDPApiViewDataProductPageSuggestion=Verifique a página de detalhes do produto de dados.

#XMSG: Notification reminder title for partially sharing a data product. {0} is the share name. {1} is the data product name.
errOdcDPApiShareCreatePartiallyTitle=A partilha {0} foi criada parcialmente para o produto de dados {1}
#YMSG:
errOdcDPApiShareCreatePartiallyBody=Não foi possível concluir parte do pedido de partilha. Verifique a página de detalhes do produto de dados.

#XMSG: Notification reminder title for partially sharing a data product. {0} is the share name. {1} is the data product name.
errOdcDPApiShareUpdatePartiallyTitle=A partilha {0} foi atualizada parcialmente para o produto de dados {1}
#YMSG:
errOdcDPApiShareUpdatePartiallyBody=Não foi possível concluir parte do pedido de partilha. Verifique a página de detalhes do produto de dados.

#XMSG: Notification reminder that a recovery of the system has just completed, a manual synchronization is needed to get the system up to date
txtOdcCatalogRecoverySuccessManualSyncNeeded=A recuperação do sistema está completa. Execute uma sincronização manual para assegurar que os metadados para todos ativos de catálogo estão atualizados.

#XMSG:
txtOdcSharedCatalogSetup=O inquilino está agora a partilhar o armazenamento do catálogo com o inquilino {0}

#XMSG:
txtOdcSharedCatalogRevoke=O inquilino deixou de partilhar o armazenamento do catálogo com o inquilino {0}

#XMSG: Notification title message indicating the execution status of a task, including execution type, applicationId, activity and objectId.
taskExecutionCompleted=A execução {0} de {1}/{2} para {3} está concluída.
taskExecutionFailed=A execução {0} de {1}/{2} para {3} falhou.
#XMSG: Notification body message indicating the status of a task, including applicationId, activity, objectId and spaceId.
taskExecutionCompletedWithoutSubstatus=A tarefa {0}/{1} no objeto {2} no espaço {3} está concluída.
taskExecutionFailedWithoutSubstatus=A tarefa {0}/{1} no objeto {2} no espaço {3} falhou.
#XMSG: Notification body message indicating the status and substatus of a task, including applicationId, activity, objectId, spaceId and substatus.
taskExecutionCompletedWithSubstatus=A tarefa {0}/{1} no objeto {2} no espaço {3} está concluída com o subestado {4}.
taskExecutionFailedWithSubstatus=A tarefa {0}/{1} no objeto {2} no espaço {3} falhou com o subestado {4}.

#XMSG: Notification title message for task chain indicating the execution status of a task, including execution type, applicationId, activity and objectId.
taskChainExecutionCompleted=A execução {0} de {1}/{2} para {3} está concluída.
taskChainExecutionFailed=A execução {0} de {1}/{2} para {3} falhou.
#XMSG: Notification body message for task chain indicating the execution status of a task, including applicationId, activity, objectId and spaceId.
taskChainExecutionCompletedWithoutSubstatus=A tarefa {0}/{1} no objeto {2} no espaço {3} está concluída.
taskChainExecutionFailedWithoutSubstatus=A tarefa {0}/{1} no objeto {2} no espaço {3} falhou.
#XMSG: Notification body message for task chain indicating the execution status and substatus of a task, including applicationId, activity, objectId, spaceId and substatus.
taskChainExecutionCompletedWithSubstatus=A tarefa {0}/{1} no objeto {2} no espaço {3} está concluída com o subestado {4}.
taskChainExecutionFailedWithSubstatus=A tarefa {0}/{1} no objeto {2} no espaço {3} falhou com o subestado {4}.

# XMSG: Notification for Completed Schedules
scheduleCompletedPause=Número de agendamentos interrompidos: {0}.
scheduleCompletedResume=Número de agendamentos retomados: {0}.
scheduleCompletedDelete=Número de agendamentos eliminados: {0}.
scheduleCompletedChangeOwner=Número de agendamentos com responsabilidade atualizada: {0}.

# XMSG: Notification for Failed Schedules
scheduleFailedPause=Número de agendamentos que não foram interrompidos: {0} de {1}.
scheduleFailedResume=Número de agendamentos que não foram retomados: {0} de {1}.
scheduleFailedDelete=Número de agendamentos que não foram eliminados: {0} de {1}.
scheduleFailedChangeOwner=Número de agendamentos com falha na atualização da responsabilidade: {0} de {1}.

#### End Catalog component
