###################################################################
### Translation Texts for Notifications coming from the Backend ###
### Please maintain a section for each team/tool                ###
###################################################################

# ~~~~~~ Start of ACN Notifications ~~~~~~
#XMSG ACN job title for pending import jobs, parameter 0 is for package name
notificationACNImportTitlePENDING=Увоз пакета {0} је на чекању
#XMSG ACN job title for running import jobs, parameter 0 is for package name
notificationACNImportTitleEXECUTING=Увоз пакета {0} је у току
#XMSG ACN job title for completed import jobs with warnings, parameter 0 is for package name
notificationACNImportTitleWARNING=Увоз пакета {0} завршен са упозорењем
#XMSG ACN job title for completed import jobs, parameter 0 is for package name
notificationACNImportTitleDONE=Увоз пакета {0} завршен
#XMSG ACN job title for failed import jobs, parameter 0 is for package name
notificationACNImportTitleFAILED=Увоз пакета {0} није успео
#XMSG ACN job title for pending export jobs, parameter 0 is for package name
notificationACNExportTitlePENDING=Извоз пакета {0} је на чекању
#XMSG ACN job title for running export jobs, parameter 0 is for package name
notificationACNExportTitleEXECUTING=Извоз пакета {0} је у току
#XMSG ACN job title for completed export jobs with warnings, parameter 0 is for package name
notificationACNExportTitleWARNING=Извоз пакета {0} завршен са упозорењем
#XMSG ACN job title for completed export jobs, parameter 0 is for package name
notificationACNExportTitleDONE=Извоз пакета {0} завршен
#XMSG ACN job title for failed export jobs, parameter 0 is for package name
notificationACNExportTitleFAILED=Извоз пакета {0} није успео
#XMSG ACN job title for pending download jobs, parameter 0 is for package name
notificationACNDownloadTitlePENDING=Пренос пакета {0} са сервера чека на извршење
#XMSG ACN job title for running download jobs, parameter 0 is for package name
notificationACNDownloadTitleEXECUTING=Пренос пакета {0} са сервера у току
#XMSG ACN job title for completed download jobs with warnings, parameter 0 is for package name
notificationACNDownloadTitleWARNING=Пренос пакета {0} са сервера завршен са упозорењем
#XMSG ACN job title for completed download jobs, parameter 0 is for package name
notificationACNDownloadTitleDONE=Пренос пакета {0} са сервера завршен
#XMSG ACN job title for Downloaded download jobs, parameter 0 is for package name
notificationACNDownloadTitleDOWNLOADED=Пренос пакета {0} са сервера завршен
#XMSG ACN job title for failed download jobs, parameter 0 is for package name
notificationACNDownloadTitleFAILED=Пренос пакета {0} са сервера није успео
#XMSG ACN job title for pending upload jobs, parameter 0 is for package name
notificationACNUploadTitlePENDING=Пренос пакета {0} на сервер чека на извршење
#XMSG ACN job title for running upload jobs, parameter 0 is for package name
notificationACNUploadTitleEXECUTING=Пренос пакета {0} на сервер у току
#XMSG ACN job title for completed upload jobs with warnings, parameter 0 is for package name
notificationACNUploadTitleWARNING=Пренос пакета {0} на сервер завршен са упозорењем
#XMSG ACN job title for completed upload jobs, parameter 0 is for package name
notificationACNUploadTitleDONE=Пренос пакета {0} на сервер завршен
#XMSG ACN job title for failed upload jobs, parameter 0 is for package name
notificationACNUploadTitleFAILED=Пренос пакета {0} на сервер није успео
#XMSG ACN job title for pending upload jobs, parameter 0 is for package name
notificationACNGitPullTitlePENDING=Повлачење пакета {0} чека на извршење
#XMSG ACN job title for running upload jobs, parameter 0 is for package name
notificationACNGitPullTitleEXECUTING=Повлачење пакета {0} у току
#XMSG ACN job title for completed upload jobs with warnings, parameter 0 is for package name
notificationACNGitPullTitleWARNING=Повлачење пакета {0} завршено са упозорењем
#XMSG ACN job title for completed upload jobs, parameter 0 is for package name
notificationACNGitPullTitleDONE=Повлачење пакета {0} завршено
#XMSG ACN job title for failed upload jobs, parameter 0 is for package name
notificationACNGitPullTitleFAILED=Повлачење пакета {0} није успело
#XMSG ACN job title for pending upload jobs, parameter 0 is for package name
notificationACNGitPushTitlePENDING=Прослеђивање пакета {0} чека на извршење
#XMSG ACN job title for running upload jobs, parameter 0 is for package name
notificationACNGitPushTitleEXECUTING=Прослеђивање пакета {0} у току
#XMSG ACN job title for completed upload jobs with warnings, parameter 0 is for package name
notificationACNGitPushTitleWARNING=Прослеђивање пакета {0} завршено са упозорењем
#XMSG ACN job title for completed upload jobs, parameter 0 is for package name
notificationACNGitPushTitleDONE=Прослеђивање пакета {0} завршено
#XMSG ACN job title for failed upload jobs, parameter 0 is for package name
notificationACNGitPushTitleFAILED=Прослеђивање пакета {0} није успело
#XMSG ACN job title for pending composite job- publish to ctms, parameter 0 is for package name, 1 is for node
notificationACNCompositeTitlePENDING=Пренос пакета {0} на сервер у {1} чека на извршење.
#XMSG ACN job title for running composite job- publish to ctms, parameter 0 is for package name, 1 is for node
notificationACNCompositeTitleEXECUTING=Пренос пакета {0} на сервер у {1} је у току.
#XMSG ACN job title for composite job- publish to ctms completed with warnings, parameter 0 is for package name, 1 is for node
notificationACNCompositeTitleWARNING=Пренос пакета {0} на сервер у {1} је завршен са упозорењем.
#XMSG ACN job title for completed composite job- publish to ctms, parameter 0 is for package name, 1 is for node
notificationACNCompositeTitleDONE=Пренос пакета {0} на сервер у {1} је завршен.
#XMSG ACN job title for failed composite job- publish to ctms, parameter 0 is for package name, 1 is for node
notificationACNCompositeTitleFAILED=Пренос пакета {0} на сервер у {1} није успео.
#YMSG ACN job details for jobs, parameter 0 is for current number of objects, parameter 1 for total number of objects
notificationACNBody=Обрађено објеката {0} / {1}
#XMSG: ACN .Package validity date.
ACN_PACKAGE_DOWNLOAD_VALIDTY=Пакет је важећи за пренос на сервер на мрежу садржаја до {0}.
# ~~~~~~ End of ACN Notifications ~~~~~~

# ~~~~~~ Start of DP Agent Notifications ~~~~~~
#XTXT: DP Agent Connection Status Notification - connected
DPAgentConnectionStatusNotificationConnected=Агент дистрибуције података {0} је повезан.
#XTXT: DP Agent Connection Status Notification - disconnected
DPAgentConnectionStatusNotificationDisconnected=Веза агента дистрибуције података {0} је прекинута. Проверите.
# ~~~~~~ End of DP Agent Notifications ~~~~~~

# ~~~~~~ Start of Spaces Notifications ~~~~~~
#XMSG Notification title for a successful space deployment
spaceDeploymentSuccessful=Простор "{0}" је имплементиран.
#XMSG Notification title for an unsuccessful space deployment
spaceDeploymentUnsuccessful=Простор "{0}" се не може имплементирати.
#XMSG Notification title for an unsuccessful space content copy
spaceContentCopyUnsuccessful=Простор "{1}" креиран и имплементиран, али није могуће копирати објекте из простора "{0}" у простор "{1}".
#XMSG Notification title for an unsuccessful space content deployment
spaceContentDeploymentUnsuccessful=Простор "{1}" креиран и имплементиран. Сви објекти копирани из простора "{0}" у простор "{1}" али неке није могуће имплементирати.
#XMSG Notification body for a successful space deployment
openSpace=Отворите {0}.
#XMSG Notification title for a successful copy space and deployment
spaceCopyAndDeploySuccessful=Простор "{0}" је копиран у простор "{1}". Објекти у простору "{1}" су имплементирани.
#XMSG Notification title for a successful copy of space content
spaceContentCopySuccessful=Простор "{1}" креиран и имплементиран. Сви објекти копирани из простора "{0}" у простор "{1}".
#XMSG Notification title for a successful deployment of space content
spaceContentCopyAndDeploymentSuccessful=Простор "{1}" креиран и имплементиран. Сви објекти копирани из простора "{0}" у простор "{1}" и имплементирани.
#XMSG Notification title for a successful space deletion
spaceDeletionSuccessful=Простор "{0}" је избрисан.
# ~~~~~~ End of Spaces Notifications ~~~~~~

# ~~~~~~ Start of Cross Architecture Notifications ~~~~~~
#XMSG: Notification message to inform the user that a new survey is available
longSurveyInitialNotification=Здраво {0}, реците нам како нам иде и шта мислите у нашој анкети о корисничком искуству. У наредне три недеље можете приступити анкети тако што ћете кликнути на "Пружи повратне информације" у траци окружења.
#XMSG: Notification reminder message to inform the user that the long survey is about to expire
longSurveyReminderNotification=Здраво {0}, имате још {1} дана да попуните нашу анкету. Хвала вам ако сте је већ попунили.
#XMSG: Notification reminder message to inform the user that the long survey is about to expire tomorrow
longSurveySecondLastReminderNotification=Здраво {0}, имате још 1 дан да попуните нашу анкету. Хвала вам ако сте је већ попунили.
#XMSG: Notification reminder message to inform the user that the long survey is about to expire today.
longSurveyLastReminderNotification=Здраво {0}, данас је последња прилика да попуните нашу анкету. Хвала вам ако сте је већ попунили.
# ~~~~~~ End of Cross Architecture Notifications ~~~~~~

# ~~~~~~ Start of Cockpit Notifications ~~~~~~
#XMSG: Notification message to inform the user that a activation failed
packageFailedInstallation=Активирање пакета података {0} није успело. Покушајте поново касније.
#XMSG: Notification message to inform the user that a activation succeeded
packageInstalled=Пакет података {0} је активиран.
#XMSG: Notification message to inform the user that a deactivation failed
packageFailedUninstallation=Деактивирање пакета података {0} није успело. Покушајте поново касније.
#XMSG: Notification message to inform the user that a deactivation succeeded
packageUninstalled=Пакет података {0} је деактивиран.
# ~~~~~~ End of Cockpit Notifications ~~~~~~


#### Begin Catalog component

#XMSG: Notification reminder message to inform the user that the import is successful. {0} is the file name of the import
txtOdcCatalogImportResultSuccessful={0} је увезено.
#YMSG: Notification reminder message to inform the user that the import is unsuccessful. {0} is the file name of the import
errOdcCatalogImportResultFailed=Увоз {0} није успео.

#XMSG: Notification reminder title for sharing a data product. {0} is the share name. {1} is the data product name.
txtOdcDPApiShareCreateSuccessTitle=Дељење {0} је креирано за производ података {1}

#YMSG: Error notification reminder title for sharing a data product. {0} is the share name. {1} is the data product name.
errOdcDPApiShareCreateErrorTitle=Дељење {0} се не може креирати за производ података {1}
#YMSG: {0} optional partner specific message that is not translated.  \n will be added to the end of the placeholder (if any).
errOdcDPApiShareCreateErrorBody={0}Погледајте страницу с детаљима производа података

#YMSG: Error notification reminder title for sharing a data product. {0} is the share name. {1} is the data product name.
errOdcDPApiShareCreateTimeoutTitle=Дељење {0} се не може креирати за производ података {1}
#YMSG:
errOdcDPApiShareCreateTimeoutBody=Дељење није успело да се заврши у очекивано време. Погледајте страницу с детаљима производа података.

#XMSG: Notification reminder title for updating a data product share. {0} is the share name. {1} is the data product name.
txtOdcDPApiShareUpdateSuccessTitle=Дељење {0} је ажурирано за производ података {1}

#YMSG: Error notification reminder title for updating a data product share. {0} is the share name. {1} is the data product name.
errOdcDPApiShareUpdateErrorTitle=Дељење {0} се не може ажурирати за производ података {1}
#YMSG: {0} optional partner specific message that is not translated.  \n will be added to the end of the placeholder (if any).
errOdcDPApiShareUpdateErrorBody={0}Погледајте страницу с детаљима производа података

#YMSG: Error notification reminder title for updating a data product share. {0} is the share name. {1} is the data product name.
errOdcDPApiShareUpdateTimeoutTitle=Дељење {0} се не може ажурирати за производ података {1}
#YMSG:
errOdcDPApiShareUpdateTimeoutBody=Ажурирање није успело да се заврши у очекивано време. Погледајте страницу с детаљима производа података.

#XMSG: Notification reminder title for deleting a data product share. {0} is the share name. {1} is the data product name.
txtOdcDPApiShareDeleteSuccessTitle=Дељење {0} је избрисано за производ података {1}

#YMSG: Error notification reminder title for deleting a data product share. {0} is the share name. {1} is the data product name.
errOdcDPApiShareDeleteErrorTitle=Дељење {0} се не може избрисати за производ података {1}
#YMSG:
errOdcDPApiShareDeleteErrorBody=Дошло је до грешке при брисању дељења. Погледајте страницу с детаљима производа података.

#YMSG: Error notification reminder title for deleting a data product share. {0} is the share name. {1} is the data product name.
errOdcDPApiShareDeleteTimeoutTitle=Дељење {0} се не може избрисати за производ података {1}
#YMSG:
errOdcDPApiShareDeleteTimeoutBody=Брисање дељења није успело у очекивано време. Погледајте страницу с детаљима производа података.

#YMSG:
errOdcDPApiShareGenericError=Није могуће делити производ података. Погледајте страницу с детаљима производа података.
#YMSG:
errOdcDPApiShareNotFoundError=Није нађено дељење производа података. Погледајте страницу с детаљима производа података.

#YMSG: Notification body message to inform the user they can click on the notification to go to the data product page
txtOdcDPApiViewDataProductPageSuggestion=Погледајте страницу с детаљима производа података.

#XMSG: Notification reminder title for partially sharing a data product. {0} is the share name. {1} is the data product name.
errOdcDPApiShareCreatePartiallyTitle=Дељење {0} је делимично креирано за производ података {1}
#YMSG:
errOdcDPApiShareCreatePartiallyBody=Није могуће завршити део захтева за дељење. Погледајте страницу с детаљима производа података.

#XMSG: Notification reminder title for partially sharing a data product. {0} is the share name. {1} is the data product name.
errOdcDPApiShareUpdatePartiallyTitle=Дељење {0} је делимично ажурирано за производ података {1}
#YMSG:
errOdcDPApiShareUpdatePartiallyBody=Није могуће завршити део захтева за дељење. Погледајте страницу с детаљима производа података.

#XMSG: Notification reminder that a recovery of the system has just completed, a manual synchronization is needed to get the system up to date
txtOdcCatalogRecoverySuccessManualSyncNeeded=Обнова система је потпуна. Извршите ручну синхронизацију да би се осигурало да су сви метаподаци за сва средства каталога ажурирани.

#XMSG:
txtOdcSharedCatalogSetup=Клијент сада дели складиште каталога с клијентом {0}

#XMSG:
txtOdcSharedCatalogRevoke=Клијент је зауставио дељење складишта каталога с клијентом {0}

#XMSG: Notification title message indicating the execution status of a task, including execution type, applicationId, activity and objectId.
taskExecutionCompleted={0} извођење {1}/{2} за {3} је завршено.
taskExecutionFailed={0} извођење {1}/{2} за {3} није успело.
#XMSG: Notification body message indicating the status of a task, including applicationId, activity, objectId and spaceId.
taskExecutionCompletedWithoutSubstatus=Задатак {0}/{1} у објекту {2} у простору {3} је завршен.
taskExecutionFailedWithoutSubstatus=Задатак {0}/{1} у објекту {2} у простору {3} није успео.
#XMSG: Notification body message indicating the status and substatus of a task, including applicationId, activity, objectId, spaceId and substatus.
taskExecutionCompletedWithSubstatus=Задатак {0}/{1} у објекту {2} у простору {3} је завршен с подстатусом {4}.
taskExecutionFailedWithSubstatus=Задатак {0}/{1} у објекту {2} у простору {3} није успео с подстатусом {4}.

#XMSG: Notification title message for task chain indicating the execution status of a task, including execution type, applicationId, activity and objectId.
taskChainExecutionCompleted={0} извођење {1}/{2} за {3} је завршено.
taskChainExecutionFailed={0} извођење {1}/{2} за {3} није успело.
#XMSG: Notification body message for task chain indicating the execution status of a task, including applicationId, activity, objectId and spaceId.
taskChainExecutionCompletedWithoutSubstatus=Задатак {0}/{1} у објекту {2} у простору {3} је завршен.
taskChainExecutionFailedWithoutSubstatus=Задатак {0}/{1} у објекту {2} у простору {3} није успео.
#XMSG: Notification body message for task chain indicating the execution status and substatus of a task, including applicationId, activity, objectId, spaceId and substatus.
taskChainExecutionCompletedWithSubstatus=Задатак {0}/{1} у објекту {2} у простору {3} је завршен с подстатусом {4}.
taskChainExecutionFailedWithSubstatus=Задатак {0}/{1} у објекту {2} у простору {3} није успео с подстатусом {4}.

# XMSG: Notification for Completed Schedules
scheduleCompletedPause=Број паузираних распореда: {0}.
scheduleCompletedResume=Број настављених распореда: {0}.
scheduleCompletedDelete=Број избрисаних распореда: {0}.
scheduleCompletedChangeOwner=Број распореда са ажурираним власништвом: {0}.

# XMSG: Notification for Failed Schedules
scheduleFailedPause=Број распореда који се нису могли паузирати: {0} од {1}.
scheduleFailedResume=Број распореда који се нису могли наставити: {0} од {1}.
scheduleFailedDelete=Број распореда који се нису могли наставити: {0} од {1}.
scheduleFailedChangeOwner=Број распореда за које се није могло ажурирати власништво: {0} од {1}.

#### End Catalog component
