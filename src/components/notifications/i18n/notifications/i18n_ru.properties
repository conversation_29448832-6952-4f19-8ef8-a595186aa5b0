###################################################################
### Translation Texts for Notifications coming from the Backend ###
### Please maintain a section for each team/tool                ###
###################################################################

# ~~~~~~ Start of ACN Notifications ~~~~~~
#XMSG ACN job title for pending import jobs, parameter 0 is for package name
notificationACNImportTitlePENDING=Пакет {0} ожидает импорта
#XMSG ACN job title for running import jobs, parameter 0 is for package name
notificationACNImportTitleEXECUTING=Импорт пакета {0} выполняется
#XMSG ACN job title for completed import jobs with warnings, parameter 0 is for package name
notificationACNImportTitleWARNING=Импорт пакета {0} завершен с предупреждением
#XMSG ACN job title for completed import jobs, parameter 0 is for package name
notificationACNImportTitleDONE=Импорт пакета {0} завершен
#XMSG ACN job title for failed import jobs, parameter 0 is for package name
notificationACNImportTitleFAILED=Импорт пакета {0} не выполнен
#XMSG ACN job title for pending export jobs, parameter 0 is for package name
notificationACNExportTitlePENDING=Пакет {0} ожидает экспорта
#XMSG ACN job title for running export jobs, parameter 0 is for package name
notificationACNExportTitleEXECUTING=Экспорт пакета {0} выполняется
#XMSG ACN job title for completed export jobs with warnings, parameter 0 is for package name
notificationACNExportTitleWARNING=Экспорт пакета {0} завершен с предупреждением
#XMSG ACN job title for completed export jobs, parameter 0 is for package name
notificationACNExportTitleDONE=Экспорт пакета {0} завершен
#XMSG ACN job title for failed export jobs, parameter 0 is for package name
notificationACNExportTitleFAILED=Экспорт пакета {0} не выполнен
#XMSG ACN job title for pending download jobs, parameter 0 is for package name
notificationACNDownloadTitlePENDING=Выгрузка пакета {0} ожидает выполнения
#XMSG ACN job title for running download jobs, parameter 0 is for package name
notificationACNDownloadTitleEXECUTING=Выгрузка пакета {0} выполняется
#XMSG ACN job title for completed download jobs with warnings, parameter 0 is for package name
notificationACNDownloadTitleWARNING=Выгрузка пакета {0} завершена с предупреждением
#XMSG ACN job title for completed download jobs, parameter 0 is for package name
notificationACNDownloadTitleDONE=Выгрузка пакета {0} завершена
#XMSG ACN job title for Downloaded download jobs, parameter 0 is for package name
notificationACNDownloadTitleDOWNLOADED=Выгрузка пакета {0} завершена
#XMSG ACN job title for failed download jobs, parameter 0 is for package name
notificationACNDownloadTitleFAILED=Выгрузка пакета {0} не выполнена
#XMSG ACN job title for pending upload jobs, parameter 0 is for package name
notificationACNUploadTitlePENDING=Загрузка пакета {0} ожидает выполнения
#XMSG ACN job title for running upload jobs, parameter 0 is for package name
notificationACNUploadTitleEXECUTING=Загрузка пакета {0} выполняется
#XMSG ACN job title for completed upload jobs with warnings, parameter 0 is for package name
notificationACNUploadTitleWARNING=Загрузка пакета {0} завершена с предупреждением
#XMSG ACN job title for completed upload jobs, parameter 0 is for package name
notificationACNUploadTitleDONE=Загрузка пакета {0} завершена
#XMSG ACN job title for failed upload jobs, parameter 0 is for package name
notificationACNUploadTitleFAILED=Загрузка пакета {0} не выполнена
#XMSG ACN job title for pending upload jobs, parameter 0 is for package name
notificationACNGitPullTitlePENDING=Получение пакета {0} ожидает выполнения
#XMSG ACN job title for running upload jobs, parameter 0 is for package name
notificationACNGitPullTitleEXECUTING=Получение пакета {0} выполняется
#XMSG ACN job title for completed upload jobs with warnings, parameter 0 is for package name
notificationACNGitPullTitleWARNING=Получение пакета {0} завершено с предупреждением
#XMSG ACN job title for completed upload jobs, parameter 0 is for package name
notificationACNGitPullTitleDONE=Получение пакета {0} завершено
#XMSG ACN job title for failed upload jobs, parameter 0 is for package name
notificationACNGitPullTitleFAILED=Получение пакета {0} не выполнено
#XMSG ACN job title for pending upload jobs, parameter 0 is for package name
notificationACNGitPushTitlePENDING=Отправка пакета {0} ожидает выполнения
#XMSG ACN job title for running upload jobs, parameter 0 is for package name
notificationACNGitPushTitleEXECUTING=Отправка пакета {0} выполняется
#XMSG ACN job title for completed upload jobs with warnings, parameter 0 is for package name
notificationACNGitPushTitleWARNING=Отправка пакета {0} завершена с предупреждением
#XMSG ACN job title for completed upload jobs, parameter 0 is for package name
notificationACNGitPushTitleDONE=Отправка пакета {0} завершена
#XMSG ACN job title for failed upload jobs, parameter 0 is for package name
notificationACNGitPushTitleFAILED=Отправка пакета {0} не выполнена
#XMSG ACN job title for pending composite job- publish to ctms, parameter 0 is for package name, 1 is for node
notificationACNCompositeTitlePENDING=Загрузка {0} в {1} ожидает выполнения.
#XMSG ACN job title for running composite job- publish to ctms, parameter 0 is for package name, 1 is for node
notificationACNCompositeTitleEXECUTING=Загрузка {0} в {1} выполняется.
#XMSG ACN job title for composite job- publish to ctms completed with warnings, parameter 0 is for package name, 1 is for node
notificationACNCompositeTitleWARNING=Загрузка {0} в {1} завершена с предупреждением.
#XMSG ACN job title for completed composite job- publish to ctms, parameter 0 is for package name, 1 is for node
notificationACNCompositeTitleDONE=Загрузка {0} в {1} завершена.
#XMSG ACN job title for failed composite job- publish to ctms, parameter 0 is for package name, 1 is for node
notificationACNCompositeTitleFAILED=Загрузка {0} в {1} не выполнена.
#YMSG ACN job details for jobs, parameter 0 is for current number of objects, parameter 1 for total number of objects
notificationACNBody=Обработаны объекты: {0} из {1}
#XMSG: ACN .Package validity date.
ACN_PACKAGE_DOWNLOAD_VALIDTY=Это пакет действителен для загрузки в сеть контента до {0}.
# ~~~~~~ End of ACN Notifications ~~~~~~

# ~~~~~~ Start of DP Agent Notifications ~~~~~~
#XTXT: DP Agent Connection Status Notification - connected
DPAgentConnectionStatusNotificationConnected=Агент провизионирования данных {0} подключен.
#XTXT: DP Agent Connection Status Notification - disconnected
DPAgentConnectionStatusNotificationDisconnected=Агент провизионирования данных {0} отключен. Проверьте.
# ~~~~~~ End of DP Agent Notifications ~~~~~~

# ~~~~~~ Start of Spaces Notifications ~~~~~~
#XMSG Notification title for a successful space deployment
spaceDeploymentSuccessful=Пространство "{0}" развернуто.
#XMSG Notification title for an unsuccessful space deployment
spaceDeploymentUnsuccessful=Не удалось развернуть пространство "{0}".
#XMSG Notification title for an unsuccessful space content copy
spaceContentCopyUnsuccessful=Пространство "{1}" создано и развернуто, но объекты не удалось скопировать из пространства "{0}" в пространство "{1}".
#XMSG Notification title for an unsuccessful space content deployment
spaceContentDeploymentUnsuccessful=Пространство "{1}" создано и развернуто. Все объекты скопированы из пространства "{0}" в пространство "{1}", но некоторые не удалось развернуть.
#XMSG Notification body for a successful space deployment
openSpace={0} открыто.
#XMSG Notification title for a successful copy space and deployment
spaceCopyAndDeploySuccessful=Пространство "{0}" скопировано в пространство "{1}". Объекты в пространстве "{1}" развернуты.
#XMSG Notification title for a successful copy of space content
spaceContentCopySuccessful=Пространство "{1}" создано и развернуто. Все объекты скопированы из пространства "{0}" в пространство "{1}".
#XMSG Notification title for a successful deployment of space content
spaceContentCopyAndDeploymentSuccessful=Пространство "{1}" создано и развернуто. Все объекты скопированы из пространства "{0}" в пространство "{1}" и развернуты.
#XMSG Notification title for a successful space deletion
spaceDeletionSuccessful=Пространство "{0}" удалено.
# ~~~~~~ End of Spaces Notifications ~~~~~~

# ~~~~~~ Start of Cross Architecture Notifications ~~~~~~
#XMSG: Notification message to inform the user that a new survey is available
longSurveyInitialNotification=Здравствуйте, {0}! Расскажите о своих впечатлениях и поделитесь мыслями через наш опрос о пользовательском опыте. В течение ближайших трех недель вы сможете принять участие в опросе, нажав кнопку ''Оставить отзыв'' на панели оболочки.
#XMSG: Notification reminder message to inform the user that the long survey is about to expire
longSurveyReminderNotification=Здравствуйте, {0}! Наш опрос будет доступен еще {1} дн. Благодарим, если вы уже заполнили его.
#XMSG: Notification reminder message to inform the user that the long survey is about to expire tomorrow
longSurveySecondLastReminderNotification=Здравствуйте, {0}! Наш опрос будет доступен еще 1 день. Благодарим, если вы уже заполнили его.
#XMSG: Notification reminder message to inform the user that the long survey is about to expire today.
longSurveyLastReminderNotification=Здравствуйте, {0}! Сегодня последний день, когда можно заполнить наш опрос. Благодарим, если вы уже заполнили его.
# ~~~~~~ End of Cross Architecture Notifications ~~~~~~

# ~~~~~~ Start of Cockpit Notifications ~~~~~~
#XMSG: Notification message to inform the user that a activation failed
packageFailedInstallation=Не удалось активировать пакет данных {0}. Повторите попытку позднее.
#XMSG: Notification message to inform the user that a activation succeeded
packageInstalled=Пакет данных {0} активирован.
#XMSG: Notification message to inform the user that a deactivation failed
packageFailedUninstallation=Не удалось деактивировать пакет данных {0}. Повторите попытку позднее.
#XMSG: Notification message to inform the user that a deactivation succeeded
packageUninstalled=Пакет данных {0} деактивирован.
# ~~~~~~ End of Cockpit Notifications ~~~~~~


#### Begin Catalog component

#XMSG: Notification reminder message to inform the user that the import is successful. {0} is the file name of the import
txtOdcCatalogImportResultSuccessful=Файл {0} импортирован.
#YMSG: Notification reminder message to inform the user that the import is unsuccessful. {0} is the file name of the import
errOdcCatalogImportResultFailed=Импортировать файл {0} не удалось.

#XMSG: Notification reminder title for sharing a data product. {0} is the share name. {1} is the data product name.
txtOdcDPApiShareCreateSuccessTitle=Создан совместно используемый ресурс {0} для продукта данных {1}

#YMSG: Error notification reminder title for sharing a data product. {0} is the share name. {1} is the data product name.
errOdcDPApiShareCreateErrorTitle=Не удалось создать совместно используемый ресурс {0} для продукта данных {1}
#YMSG: {0} optional partner specific message that is not translated.  \n will be added to the end of the placeholder (if any).
errOdcDPApiShareCreateErrorBody={0}Просмотрите страницу сведений о продукте данных

#YMSG: Error notification reminder title for sharing a data product. {0} is the share name. {1} is the data product name.
errOdcDPApiShareCreateTimeoutTitle=Не удалось создать совместно используемый ресурс {0} для продукта данных {1}
#YMSG:
errOdcDPApiShareCreateTimeoutBody=Не удалось завершить совместно используемый ресурс в пределах ожидаемого времени. Просмотрите страницу сведений о продукте данных.

#XMSG: Notification reminder title for updating a data product share. {0} is the share name. {1} is the data product name.
txtOdcDPApiShareUpdateSuccessTitle=Обновлен совместно используемый ресурс {0} для продукта данных {1}

#YMSG: Error notification reminder title for updating a data product share. {0} is the share name. {1} is the data product name.
errOdcDPApiShareUpdateErrorTitle=Не удалось обновить совместно используемый ресурс {0} для продукта данных {1}
#YMSG: {0} optional partner specific message that is not translated.  \n will be added to the end of the placeholder (if any).
errOdcDPApiShareUpdateErrorBody={0}Просмотрите страницу сведений о продукте данных

#YMSG: Error notification reminder title for updating a data product share. {0} is the share name. {1} is the data product name.
errOdcDPApiShareUpdateTimeoutTitle=Не удалось обновить совместно используемый ресурс {0} для продукта данных {1}
#YMSG:
errOdcDPApiShareUpdateTimeoutBody=Не удалось завершить обновление в пределах ожидаемого времени. Просмотрите страницу сведений о продукте данных.

#XMSG: Notification reminder title for deleting a data product share. {0} is the share name. {1} is the data product name.
txtOdcDPApiShareDeleteSuccessTitle=Удален совместно используемый ресурс {0} для продукта данных {1}

#YMSG: Error notification reminder title for deleting a data product share. {0} is the share name. {1} is the data product name.
errOdcDPApiShareDeleteErrorTitle=Не удалось удалить совместно используемый ресурс {0} для продукта данных {1}
#YMSG:
errOdcDPApiShareDeleteErrorBody=Ошибка при удалении совместно используемого ресурса. Просмотрите страницу сведений о продукте данных.

#YMSG: Error notification reminder title for deleting a data product share. {0} is the share name. {1} is the data product name.
errOdcDPApiShareDeleteTimeoutTitle=Не удалось удалить совместно используемый ресурс {0} для продукта данных {1}
#YMSG:
errOdcDPApiShareDeleteTimeoutBody=Не удалось удалить совместно используемый ресурс в пределах ожидаемого времени. Просмотрите страницу сведений о продукте данных.

#YMSG:
errOdcDPApiShareGenericError=Не удалось открыть доступ к продукту данных. Просмотрите страницу сведений о продукте данных.
#YMSG:
errOdcDPApiShareNotFoundError=Не удалось найти совместно используемый ресурс продукта данных. Просмотрите страницу сведений о продукте данных.

#YMSG: Notification body message to inform the user they can click on the notification to go to the data product page
txtOdcDPApiViewDataProductPageSuggestion=Просмотрите страницу сведений о продукте данных.

#XMSG: Notification reminder title for partially sharing a data product. {0} is the share name. {1} is the data product name.
errOdcDPApiShareCreatePartiallyTitle=Частично создан совместно используемый ресурс {0} для продукта данных {1}
#YMSG:
errOdcDPApiShareCreatePartiallyBody=Не удалось завершить часть запроса для совместно используемого ресурса. Просмотрите страницу сведений о продукте данных.

#XMSG: Notification reminder title for partially sharing a data product. {0} is the share name. {1} is the data product name.
errOdcDPApiShareUpdatePartiallyTitle=Частично обновлен совместно используемый ресурс {0} для продукта данных {1}
#YMSG:
errOdcDPApiShareUpdatePartiallyBody=Не удалось завершить часть запроса для совместно используемого ресурса. Просмотрите страницу сведений о продукте данных.

#XMSG: Notification reminder that a recovery of the system has just completed, a manual synchronization is needed to get the system up to date
txtOdcCatalogRecoverySuccessManualSyncNeeded=Восстановление системы завершено. Выполните ручную синхронизацию, чтобы обеспечить актуальность метаданных для всех активов каталога.

#XMSG:
txtOdcSharedCatalogSetup=Арендатор теперь использует хранилище каталога совместно с арендатором {0}

#XMSG:
txtOdcSharedCatalogRevoke=Арендатор больше не использует хранилище каталога совместно с арендатором {0}

#XMSG: Notification title message indicating the execution status of a task, including execution type, applicationId, activity and objectId.
taskExecutionCompleted={0} выполнение из {1}/{2} для {3} завершено.
taskExecutionFailed={0} выполнение из {1}/{2} для {3} завершено с ошибкой.
#XMSG: Notification body message indicating the status of a task, including applicationId, activity, objectId and spaceId.
taskExecutionCompletedWithoutSubstatus=Задача {0}/{1} для объекта {2} в пространстве {3} завершена.
taskExecutionFailedWithoutSubstatus=Задача {0}/{1} для объекта {2} в пространстве {3} завершена с ошибкой.
#XMSG: Notification body message indicating the status and substatus of a task, including applicationId, activity, objectId, spaceId and substatus.
taskExecutionCompletedWithSubstatus=Задача {0}/{1} для объекта {2} в пространстве {3} завершена с подстатусом {4}.
taskExecutionFailedWithSubstatus=Задача {0}/{1} для объекта {2} в пространстве {3} завершена с ошибкой с подстатусом {4}.

#XMSG: Notification title message for task chain indicating the execution status of a task, including execution type, applicationId, activity and objectId.
taskChainExecutionCompleted={0} выполнение из {1}/{2} для {3} завершено.
taskChainExecutionFailed={0} выполнение из {1}/{2} для {3} завершено с ошибкой.
#XMSG: Notification body message for task chain indicating the execution status of a task, including applicationId, activity, objectId and spaceId.
taskChainExecutionCompletedWithoutSubstatus=Задача {0}/{1} для объекта {2} в пространстве {3} завершена.
taskChainExecutionFailedWithoutSubstatus=Задача {0}/{1} для объекта {2} в пространстве {3} завершена с ошибкой.
#XMSG: Notification body message for task chain indicating the execution status and substatus of a task, including applicationId, activity, objectId, spaceId and substatus.
taskChainExecutionCompletedWithSubstatus=Задача {0}/{1} для объекта {2} в пространстве {3} завершена с подстатусом {4}.
taskChainExecutionFailedWithSubstatus=Задача {0}/{1} для объекта {2} в пространстве {3} завершена с ошибкой с подстатусом {4}.

# XMSG: Notification for Completed Schedules
scheduleCompletedPause=Число приостановленных планирований: {0}.
scheduleCompletedResume=Число возобновленных планирований: {0}.
scheduleCompletedDelete=Число удаленных планирований: {0}.
scheduleCompletedChangeOwner=Число планирований с обновленным владением: {0}.

# XMSG: Notification for Failed Schedules
scheduleFailedPause=Число планирований, которые не удалось приостановить: {0} из {1}.
scheduleFailedResume=Число планирований, которые не удалось возобновить: {0} из {1}.
scheduleFailedDelete=Число планирований, которые не удалось удалить: {0} из {1}.
scheduleFailedChangeOwner=Число планирований с неудачным обновлением владения: {0} из {1}.

#### End Catalog component
