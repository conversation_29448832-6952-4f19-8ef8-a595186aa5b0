

#XFLD: Task Chain header
headerTxt=[[[Ţąşķ Ĉĥąįŋş({0})]]]
#XFLD: Task Chain header
headerTxtNew=[[[Ţąşķ Ĉĥąįŋş ({0})]]]
#XTXT: Text for Schedule label
scheduleTxt=[[[Ŝċĥēƌűĺē∙∙∙∙∙∙]]]
#XFLD: Text for Create schedule button
createScheduleTxt=[[[Ĉŗēąţē Ŝċĥēƌűĺē∙∙∙∙]]]
#XFLD: Text for edit schedule
editScheduleTxt=[[[Ĕƌįţ Ŝċĥēƌűĺē∙∙∙∙∙∙]]]
#XLFD: Text for delete schedule
deleteScheduleTxt=[[[Ďēĺēţē Ŝċĥēƌűĺē∙∙∙∙]]]
#XTXT: text for refresh button label
refrestTxt=[[[Řēƒŗēşĥ∙∙∙∙∙∙∙]]]
#XTXT: Text for Completed status
completedTxt=[[[Ĉŏɱρĺēţēƌ∙∙∙∙∙]]]
#XTX: Text for Running status
runningTxt=[[[Řűŋŋįŋğ∙∙∙∙∙∙∙]]]
#XTX: Text for failed status
failedTxt=[[[Ƒąįĺēƌ∙∙∙∙∙∙∙∙]]]
#XTX: Text for stopped status
stoppedTxt=[[[Ŝţŏρρēƌ∙∙∙∙∙∙∙]]]
#XTX: Text for stopping status
stoppingTxt=[[[Ŝţŏρρįŋğ∙∙∙∙∙∙]]]
#XFLD: Header for Chain name
chainNameLabel=[[[Ţąşķ Ĉĥąįŋ Ńąɱē∙∙∙∙]]]
#XFLD: Header for Chain name
chainNameLabelBus=[[[Ɓűşįŋēşş Ńąɱē∙∙∙∙∙∙]]]
#XFLD: Header for Chain name
chainNameLabelBusNew=[[[Ŏƃĵēċţ (Ɓűşįŋēşş Ńąɱē)∙∙∙∙∙]]]
#XFLD: Header for Chain name
chainNameLabelTech=[[[Ţēċĥŋįċąĺ Ńąɱē∙∙∙∙∙]]]
#XFLD: Header for Chain name
chainNameLabelTechNew=[[[Ŏƃĵēċţ (Ţēċĥŋįċąĺ Ńąɱē)∙∙∙∙∙∙]]]
#XFLD: Last Run Status label
lastRunStatuslabel=[[[Ļąşţ Řűŋ Ŝţąţűş∙∙∙∙]]]
#XFLD: Last Run Status label
lastRunStatuslabelNew=[[[Ŝţąţűş∙∙∙∙∙∙∙∙]]]
#XFLD: Frequency Label
frequencyLabel=[[[Ƒŗēƣűēŋċŷ∙∙∙∙∙]]]
#XFLD: Frequency Label
frequencyLabelNew=[[[Ŝċĥēƌűĺēƌ Ƒŗēƣűēŋċŷ∙∙∙∙∙]]]
#XFLD: Duration label
durationLabel=[[[Ďűŗąţįŏŋ∙∙∙∙∙∙]]]
#XFLD: Duration label
durationLabelNew=[[[Ļąşţ Řűŋ Ďűŗąţįŏŋ∙∙∙∙∙∙∙]]]
#XFLD: Run Start label
runStartLabel=[[[Ļąşţ Řűŋ Ŝţąŗţ∙∙∙∙∙]]]
#XFLD: Run end label
runEndLabel=[[[Ļąşţ Řűŋ Ĕŋƌ∙∙∙∙∙∙∙]]]
#XFLD: Next Run label
nextRunlabel=[[[Ńēχţ Řűŋ∙∙∙∙∙∙]]]
#XFLD: Next Run label
nextRunlabelNew=[[[Ŝċĥēƌűĺēƌ Ńēχţ Řűŋ∙∙∙∙∙∙]]]
#XTXT: Task chain logs link tooltip text
taskChainLogsTxt=[[[Ţąşķ Ĉĥąįŋ Ļŏğ Ďēţąįĺş∙∙∙∙∙]]]
#XTXT: Task chain logs link tooltip text
taskChainLogsTxtNew=[[[Ďēţąįĺş∙∙∙∙∙∙∙]]]
#XTXT: Scheduled text
scheduledTxt=[[[Ŝċĥēƌűĺēƌ∙∙∙∙∙]]]
#XTXT: Paused text
pausedTxt=[[[Ƥąűşēƌ∙∙∙∙∙∙∙∙]]]
#XTXT: Execute button label
runLabel=[[[Řűŋ∙]]]
#XTXT: Execute button label
runLabelNew=[[[Ŝţąŗţ Řűŋ∙∙∙∙∙]]]
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=[[[Řűŋ ţĥē ţąşķ ċĥąįŋ∙∙∙∙∙∙]]]
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=[[[Ţąşķ ċĥąįŋ ŗűŋ ĥąş şţąŗţēƌ.∙∙∙∙∙∙∙∙]]]
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=[[[Ţąşķ ċĥąįŋ ŗűŋ ĥąş şţąŗţēƌ ƒŏŗ {0}]]]
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=[[[Ƒąįĺēƌ ţŏ ŗűŋ ţĥē ţąşķ ċĥąįŋ.∙∙∙∙∙∙∙∙∙]]]
#XFLD: Label for schedule owner column
txtScheduleOwner=[[[Ŝċĥēƌűĺē Ŏŵŋēŗ∙∙∙∙∙]]]
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=[[[Ŝĥŏŵş ŵĥŏ ċŗēąţēƌ ţĥē şċĥēƌűĺē∙∙∙∙∙∙∙∙∙]]]
#XMSG: Task log message for start chain
startChain=[[[Ŝţąŗţįŋğ ţąşķ ċĥąįŋ ŗűŋ.∙∙∙∙∙∙]]]
#XMSG: Task log message for load chain from repository
loadChainFromRepository=[[[Ţąşķ ċĥąįŋ ĺŏąƌēƌ ąŋƌ įŋįţįąĺįžēƌ.∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Task log message started task
taskStarted=[[[Ţąşķ {0} ĥąş şţąŗţēƌ.]]]
#XMSG: Task log message for finished task
taskFinished=[[[Ţĥē ţąşķ {0} ēŋƌēƌ ŵįţĥ şţąţűş {1}.]]]
#XMSG: Task log message for chain preparation
chainLoadFromRepository=[[[Ļŏąƌįŋğ ţąşķ ċĥąįŋ ąŋƌ ρŗēρąŗįŋğ ţŏ ŗűŋ ą ţŏţąĺ ŏƒ {0} ţąşķş ţĥąţ ąŗē ρąŗţ ŏƒ ţĥįş ċĥąįŋ.]]]
#XMSG: Task log message for starting a subtask
chainStartSubtask=[[[Ţŗįğğēŗįŋğ ţąşķ {0} ţŏ ŗűŋ. Ţąşķ įƌ = {1}.]]]
#XMSG: Task log message for finishing a subtask
chainFinishSubtask=[[[Ţąşķ {0} ƒįŋįşĥēƌ ŵįţĥ şţąţűş {1}.]]]
#XMSG: Task log message for indicating chain success
chainCompleted=[[[Āĺĺ {0} ţąşķş ąŗē ċŏɱρĺēţēƌ. Ţĥē ţąşķ ċĥąįŋ şţąţűş įş şēţ ţŏ ċŏɱρĺēţēƌ.]]]
#XMSG: Task log message for indicating chain failure
chainFailed=[[[Ŏŋ ą ţŏţąĺ ŏƒ {0} ţąşķş, {1} ţąşķş ċŏűĺƌ ƃē ċŏɱρĺēţēƌ ąŋƌ {2} ţąşķş ƒąįĺēƌ. Ţĥē ţąşķ ċĥąįŋ şţąţűş įş şēţ ţŏ ƒąįĺēƌ.]]]
#XMSG: Task log message for indicating chain cancelation
chainCanceled=[[[Ţąşķ ċĥąįŋ ŗűŋ ċąŋċēĺąţįŏŋ ĥąş şţąŗţēƌ. Ļŏğ įƌ ŏƒ ċąŋċēĺ ţąşķ įş {0}.]]]
#XMSG: Task log message for indicating chain cancelation in the cancel task
chainCanceling=[[[Ĉąŋċēĺįŋğ ċĥąįŋ {0}.]]]
#XMSG: Task log message for general chain runtime error
chainError=[[[Āŋ űŋēχρēċţēƌ ēŗŗŏŗ ŏċċűŗŗēƌ ŵĥįĺē ŗűŋŋįŋğ ţĥē ţąşķ ċĥąįŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Task log message for starting the overwrite check.
chainOverwriteStart=[[[Ĉĥēċķįŋğ ŵĥēţĥēŗ ţąşķ ċĥąįŋ ŵįţĥ įƌ {0} ĥąş ƒįŋįşĥēƌ.]]]
#XMSG: Task log message indicating the chain is too old.
chainOverwriteTooOld=[[[Ńŏ ţąşķş ţŏ ŗűŋ ƒŏŗ ċĥąįŋ {0} ċŏűĺƌ ƃē ƒŏűŋƌ. Řűŋ įş {1} ɱįŋűţēş ŏĺƌ. Ţĥē ţąşķ ċĥąįŋ şţąţűş įş şēţ ţŏ ƒąįĺēƌ.]]]
#XMSG: Task log message indicating the chain was created too recently.
chainOverwriteTooRecent=[[[Ńŏ ţąşķş ţŏ ŗűŋ ƒŏŗ ċĥąįŋ {0} ċŏűĺƌ ƃē ƒŏűŋƌ. Řűŋ įş {1} ɱįŋűţēş ŏĺƌ. Ţĥē ţąşķ ċĥąįŋ įş şţįĺĺ ŗűŋŋįŋğ.]]]
#XMSG: Task log message indicating a recent heartbeat was found.
chainOverwriteHeartbeatAlive=[[[Ţąşķ ċĥąįŋ {0} įş şţįĺĺ ŗűŋŋįŋğ. Ĉŏɱρĺēţēƌ: {1}, Řűŋŋįŋğ: {2}, Ƒąįĺēƌ: {3}, Ţŗįğğēŗēƌ: {4}, Ńŏţ ţŗįğğēŗēƌ: {5}.]]]
#XMSG: Task log message for a chain that is not running anymore but not everything was triggered.
chainOverwriteNotAllTriggered=[[[Āŋ ēŗŗŏŗ ŏċċűŗŗēƌ ŵĥįĺē ŗűŋŋįŋğ ţąşķ ċĥąįŋ {0}, ąŋƌ ŋŏţ ąĺĺ ţąşķş ċŏűĺƌ ƃē ţŗįğğēŗēƌ. Ĉŏɱρĺēţēƌ: {1}, Řűŋŋįŋğ: {2}, Ƒąįĺēƌ: {3}, Ţŗįğğēŗēƌ: {4}, Ńŏţ ţŗįğğēŗēƌ: {5}. Ţĥįş ţąşķ ċĥąįŋ ĥąş ƃēēŋ ŏʋēŗŵŗįţţēŋ.]]]
#XMSG: Task log message for a chain that contains a failed subtask.
chainOverwriteAnySubtaskFailed=[[[Ţąşķ ċĥąįŋ {0} ƒįŋįşĥēƌ. Ŏŋē ţąşķ ŏƒ ţĥē ċĥąįŋ ƒąįĺēƌ, şēţţįŋğ ċĥąįŋ ţŏ ƒąįĺēƌ.]]]
#XMSG: Task log message could not determine status.
chainOverwriteNotDetermined=[[[Āŋ űŋēχρēċţēƌ ēŗŗŏŗ ρŗēʋēŋţş űş ƒŗŏɱ ąċċēşşįŋğ ţĥē şţąţűş ŏƒ ţĥē ţąşķ. Ţŗŷ ąğąįŋ ąŋƌ ċŏŋţąċţ ŷŏűŗ ŜĀƤ Ŝűρρŏŗţ įƒ ţĥē ēŗŗŏŗ ρēŗşįşţş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Task log message could not take over
failedTakeover=[[[Ƒąįĺēƌ ţŏ ţąķē ŏʋēŗ ēχįşţįŋğ ţąşķ.∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Task log parallel check error
parallelCheckError=[[[Ţĥē ţąşķ ċąŋ’ţ ƃē ρŗŏċēşşēƌ ƃēċąűşē ąŋŏţĥēŗ ţąşķ įş ŗűŋŋįŋğ ąŋƌ ąĺŗēąƌŷ ƃĺŏċķįŋğ ţĥįş ţąşķ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Task log parallel task runnig error
parallelTaskRunning=[[[Ā ċŏŋƒĺįċţįŋğ ţąşķ įş ąĺŗēąƌŷ ŗűŋŋįŋğ.∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=[[[Ŝţąţűş {0} ƌűŗįŋğ ŗűŋ ŵįţĥ ċŏŗŗēĺąţįŏŋ ĬĎ {1}.]]]
#XMSG: Task log message successful takeover
successTakeover=[[[Ļēƒţ ŏʋēŗ ĺŏċķ ĥąƌ ţŏ ƃē ŗēĺēąşēƌ. Ţĥē ŋēŵ ĺŏċķ ƒŏŗ ţĥįş ţąşķ įş şēţ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=[[[Ļŏċķ ŏƒ ţĥįş ţąşķ ŵąş ţąķēŋ ŏʋēŗ ƃŷ ąŋŏţĥēŗ ţąşķ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Schedule created alert message
createScheduleSuccess=[[[Ŝċĥēƌűĺē ċŗēąţēƌ∙∙∙∙∙∙∙∙]]]
#XMSG: Task log message for taskFinishedAt
taskFinishedAt=[[[Ţąşķ {0} ƒįŋįşĥēƌ ąţ {2} ŵįţĥ şţąţűş {1}.]]]
#XMSG: Placeholder symbol for empty columns
emptyCol=[[[--∙∙]]]
#XMSG: Paused Text
txtExpired=[[[Ĕχρįŗēƌ∙∙∙∙∙∙∙]]]
#XMSG: Update task schedule success Toast message
updateScheduleSuccess=[[[Ŝċĥēƌűĺē űρƌąţēƌ∙∙∙∙∙∙∙∙]]]
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=[[[Ŝċĥēƌűĺē ƌēĺēţēƌ.∙∙∙∙∙∙∙]]]
#XMSG: Task chain might have children error text
taskChainNoChildErrorText=[[[Ţĥē ţąşķ ċĥąįŋ ɱįğĥţ ĥąʋē ċĥįĺƌŗēŋ ŵĥįċĥ ąŗē ŋŏţ şĥŏŵŋ ƃēċąűşē ţĥē ţąşķ ƒąįĺēƌ ƃēƒŏŗē ţĥē ρĺąŋ ċŏűĺƌ ƃē ğēŋēŗąţēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Task chain repair recent failed run label
retryRunLabel=[[[Řēţŗŷ Ļąţēşţ Řűŋ∙∙∙∙∙∙∙∙]]]
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=[[[Ţąşķ ċĥąįŋ ŗēţŗŷ ŗűŋ ĥąş şţąŗţēƌ∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=[[[Ţąşķ ċĥąįŋ ŗēţŗŷ ŗűŋ ĥąş ƒąįĺēƌ∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: chain repair message
chainRetried=[[[Ţąşķ ċĥąįŋ ŗēţŗŷ ţŗįğğēŗēƌ ƃŷ űşēŗ {0}]]]
#XMSG: chain not found during run
chainNotFoundDuringRun=[[[Ĉŏűĺƌ ŋŏţ ƒįŋƌ ţąşķ ċĥąįŋ {0}. Ĉĥēċķ įƒ įţ ēχįşţş ʋįą ţĥē Ďąţą Ɓűįĺƌēŗ ąŋƌ ēŋşűŗē ţĥē ċĥąįŋ įş ƌēρĺŏŷēƌ.]]]
#XMSG: chain is not DAG
chainNotDag=[[[Ĉąŋŋŏţ şţąŗţ ţąşķ ċĥąįŋ {0}. Ĭţş şţŗűċţűŗē įş įŋʋąĺįƌ. Ĉĥēċķ ţĥē ţąşķ ċĥąįŋ įŋ ţĥē Ďąţą Ɓűįĺƌēŗ.]]]
#XMSG: chain has not valid parameters
notValidParameters=[[[Ĉąŋŋŏţ şţąŗţ ţąşķ ċĥąįŋ {0}. Ŏŋē ŏŗ ɱŏŗē ŏƒ įţş ρąŗąɱēţēŗş ąŗē įŋʋąĺįƌ. Ĉĥēċķ ţĥē ţąşķ ċĥąįŋ įŋ ţĥē Ďąţą Ɓűįĺƌēŗ.]]]
#XMSG: Configuration parameter value size exceeds the maximum allowed limit
configurationSizeLimitError=[[[Ĉąŋŋŏţ şţąŗţ ţąşķ ċĥąįŋ {0}. Ţĥē şįžē ŏƒ ą ţąşķ’ş ċŏŋƒįğűŗąţįŏŋ įŋ ţĥē ċĥąįŋ ēχċēēƌş ţĥē ɱąχįɱűɱ ąĺĺŏŵēƌ şįžē ŏƒ 100 ķįƃįƃŷţēş (ĶįƁ).]]]
#XMSG: Task {0} has input parameters
taskHasInputParameters=[[[Ţąşķ {0} ĥąş įŋρűţ ρąŗąɱēţēŗş.]]]
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=[[[Ā ċŏŋƒĺįċţįŋğ ţąşķ įş ąĺŗēąƌŷ ŗűŋŋįŋğ∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: error message for reading data from backend
txtReadBackendError=[[[Ĭţ ĺŏŏķş ĺįķē ţĥēŗē ŵąş ąŋ ēŗŗŏŗ ŵĥįĺē ŗēąƌįŋğ ƒŗŏɱ ţĥē ƃąċķ ēŋƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
##XMSG: error message for admission control rejection
admissionControlError=[[[Ţĥē ţąşķ ƒąįĺēƌ ƃēċąűşē ŏƒ ąŋ ŜĀƤ ĤĀŃĀ Āƌɱįşşįŏŋ Ĉŏŋţŗŏĺ Řēĵēċţįŏŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]

#XBUT: Assign schedule menu button label
assignScheduleLabel=[[[Āşşįğŋ Ŝċĥēƌűĺē ţŏ Μē∙∙∙∙∙]]]
#XBUT: Pause schedule menu label
pauseScheduleLabel=[[[Ƥąűşē Ŝċĥēƌűĺē∙∙∙∙∙]]]
#XBUT: Resume schedule menu label
resumeScheduleLabel=[[[Řēşűɱē Ŝċĥēƌűĺē∙∙∙∙]]]
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=[[[Āŋ ēŗŗŏŗ ŏċċűŗŗēƌ ŵĥįĺē ŗēɱŏʋįŋğ şċĥēƌűĺēş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=[[[Āŋ ēŗŗŏŗ ŏċċűŗŗēƌ ŵĥįĺē ąşşįğŋįŋğ şċĥēƌűĺēş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=[[[Āŋ ēŗŗŏŗ ŏċċűŗŗēƌ ŵĥįĺē ρąűşįŋğ şċĥēƌűĺēş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=[[[Āŋ ēŗŗŏŗ ŏċċűŗŗēƌ ŵĥįĺē ŗēşűɱįŋğ şċĥēƌűĺēş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=[[[Ďēĺēţįŋğ {0} şċĥēƌűĺēş]]]
#XMSG: Message for starting mass assign of schedules
massAssignStarted=[[[Ĉĥąŋğįŋğ ţĥē ŏŵŋēŗ ŏƒ {0} şċĥēƌűĺēş]]]
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=[[[Ƥąűşįŋğ {0} şċĥēƌűĺēş]]]
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=[[[Řēşűɱįŋğ {0} şċĥēƌűĺēş]]]
#XBUT: Select Columns Button
selectColumnsBtn=[[[Ŝēĺēċţ Ĉŏĺűɱŋş∙∙∙∙∙]]]
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=[[[Ĭţ ĺŏŏķş ĺįķē ţĥēŗē ŵąş ąŋ ēŗŗŏŗ ŵįţĥ ŗēţŗŷ ĺąţēşţ ŗűŋ ąş ţĥē ρŗēʋįŏűş ţąşķ ŗűŋ ƒąįĺēƌ ƃēƒŏŗē ţĥē ρĺąŋ ċŏűĺƌ ƃē ğēŋēŗąţēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Refresh tooltip
TEXT_REFRESH=[[[Řēƒŗēşĥ∙∙∙∙∙∙∙]]]
#XFLD: Select Columns tooltip
text_selectColumns=[[[Ŝēĺēċţ Ĉŏĺűɱŋş∙∙∙∙∙]]]

#Messages for BW Process Chain task log of the Data Integration Monitor
#XMSG: Task log message for successfully starting a BW process chain
startBwProcessChainSuccess=[[[Ţĥē ƁŴ ρŗŏċēşş ċĥąįŋ "{0}" ĥąş şţąŗţēƌ şűċċēşşƒűĺĺŷ įŋ ţĥē ŜĀƤ ƁŴ Ɓŗįƌğē ţēŋąŋţ.]]]
#XMSG: Task log message for skipping a BW process chain execution
bwProcessChainSkipped=[[[Ţĥē ƁŴ ρŗŏċēşş ċĥąįŋ "{0}" ŵąş şķįρρēƌ ēįţĥēŗ ƌűē ţŏ ţĥē űŋąʋąįĺąƃįĺįţŷ ŏƒ ŋēŵ ƌąţą ŏŗ ƒąįĺűŗē ŏƒ ą ρŗēʋįŏűş ēχēċűţįŏŋ.]]]
#XMSG: Task log message for failure in starting a BW process chain
startBwProcessChainFailure=[[[Ţĥē ƁŴ ρŗŏċēşş ċĥąįŋ "{0}" ĥąş ƒąįĺēƌ ţŏ şţąŗţ įŋ ţĥē ŜĀƤ ƁŴ Ɓŗįƌğē ţēŋąŋţ.]]]
#XMSG: Task log message for failure in getting the status of a BW process chain
getBwProcessChainStatusFailure=[[[Ţĥē ţąşķ ƒąįĺēƌ ąş ŵē ŵēŗē űŋąƃĺē ţŏ ŗēţŗįēʋē ţĥē şţąţűş ŏƒ ţĥē ƁŴ ρŗŏċēşş ċĥąįŋ "{0}" ƌűē ţŏ ą ρŗŏƃĺēɱ ēşţąƃĺįşĥįŋğ ą ċŏŋŋēċţįŏŋ ţŏ ŜĀƤ ƁŴ Ɓŗįƌğē. Ŏρēŋ ţĥē "Ĉŏŋŋēċţįŏŋş" ąρρ ąŋƌ ʋąĺįƌąţē ţĥē ŜĀƤ ƁŴ Ɓŗįƌğē ċŏŋŋēċţįŏŋ įŋ ţĥē "{1}" şρąċē. Ĭƒ ŷŏű ƌŏ ŋŏţ ĥąʋē ąċċēşş ţŏ ţĥē "Ĉŏŋŋēċţįŏŋş" ąρρ, ρĺēąşē ċŏŋţąċţ ŷŏűŗ ąƌɱįŋįşţŗąţŏŗ.]]]
#XMSG: Task log message for failure in completing a BW process chain
completeBwProcessChainFailure=[[[Ţĥē ƁŴ ρŗŏċēşş ċĥąįŋ "{0}" ĥąş ƒąįĺēƌ ţŏ ċŏɱρĺēţē įŋ ţĥē ŜĀƤ ƁŴ Ɓŗįƌğē ţēŋąŋţ.]]]
#XMSG: Task log message for the clickable text of the SAP BW Bridge Monitor link
bwBridgeMonitorLinkText=[[[Ʋįēŵ ƌēţąįĺş įŋ ŜĀƤ ƁŴ Ɓŗįƌğē Μŏŋįţŏŗ∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG warning message while retrieving the task log messages if the user does not have access to the SAP BW Bridge space or to the shared object's space
missingPrivilegeOnSpaceError=[[[Ţĥēŗē ŵąş ą ρŗŏƃĺēɱ ŗēţŗįēʋįŋğ ţĥē ɱēşşąğēş, ŏŗ ŷŏű ƌŏ ŋŏţ ĥąʋē ŋēċēşşąŗŷ ρēŗɱįşşįŏŋ ţŏ ʋįēŵ ţĥēɱ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]

#Messages for API task log of the Data Integration Monitor
#XMSG: Task log message for displaying the API response
viewResponse=[[[Řēşρŏŋşē ŗēċēįʋēƌ ƒŏŗ "{0}".]]]
#XMSG: Task log message for empty ID in the API response to retrieve the status
emptyJobId=[[[Ńŏ ĬĎ ŵąş ŗēţűŗŋēƌ įŋ ţĥē ŗēşρŏŋşē ƃŏƌŷ ƒŏŗ ţĥē ĴŜŎŃ ρąţĥ: "{0}".]]]
#XMSG: Task log message for empty success value in the API response
emptySuccessValue=[[[Ńŏ şűċċēşş ʋąĺűē ŵąş ŗēţűŗŋēƌ įŋ ţĥē ŗēşρŏŋşē ƃŏƌŷ ƒŏŗ ţĥē ĴŜŎŃ ρąţĥ: "{0}".]]]
#XMSG: Task log message for empty error value in the API response
emptyErrorValue=[[[Ńŏ ēŗŗŏŗ ʋąĺűē ŵąş ŗēţűŗŋēƌ įŋ ţĥē ŗēşρŏŋşē ƃŏƌŷ ƒŏŗ ţĥē ĴŜŎŃ ρąţĥ: "{0}".]]]
#XMSG: Task log message for none of the success/error conditions matching for the values in the API response
conditionsNotFulfilled=[[[Ńŏŋē ŏƒ ţĥē şρēċįƒįēƌ şűċċēşş ŏŗ ēŗŗŏŗ įŋƌįċąţŏŗ ċŏŋƌįţįŏŋş ɱąţċĥ ţĥē ʋąĺűēş įŋ ţĥē ŗēşρŏŋşē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Task log message for the API response status being unsuccessful
statusNotOk=[[[Ţĥē ŗēşρŏŋşē ƒŏŗ "{1}" ŗēţűŗŋēƌ ąŋ űŋşűċċēşşƒűĺ şţąţűş ċŏƌē: {0}.]]]
#XMSG: Task log message for the API response body being empty
emptyResponseBody=[[[Ţĥē ŗēşρŏŋşē ƃŏƌŷ ŵąş ēɱρţŷ.∙∙∙∙∙∙∙∙]]]
#XMSG: Task log message for the API response location header being invalid
emptyLocationHeader=[[[Ţĥē ĺŏċąţįŏŋ ĥēąƌēŗ įŋ ţĥē ŗēşρŏŋşē ŵąş ēɱρţŷ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Task log message for failure in retrieving the status of the invoked API
getStatusFailure=[[[Ţĥē şţąţűş ŏƒ ţĥē įŋʋŏķēƌ ĀƤĬ ċŏűĺƌ ŋŏţ ƃē ŗēţŗįēʋēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Task log message for failure in completing the API task
completionFailure=[[[Ţĥē ĀƤĬ ţąşķ "{0}" ĥąş ƒąįĺēƌ ţŏ ċŏɱρĺēţē.]]]
#XMSG: Task log message for a successful API task completion
apiCompleted=[[[Ţĥē ĀƤĬ ţąşķ "{0}" ĥąş ċŏɱρĺēţēƌ şűċċēşşƒűĺĺŷ.]]]
#XMSG: Task log message for an API task with an invalid configuration
invalidConfiguration=[[[Ţĥē ċŏŋƒįğűŗąţįŏŋ ŏƒ ţĥē ĀƤĬ ţąşķ "{0}" įş įŋʋąĺįƌ. Ƥĺēąşē ċĥēċķ ţĥē ċŏŋƒįğűŗąţįŏŋ ąŋƌ ţŗŷ ąğąįŋ.]]]
#XMSG: Task log message for the API task being canceled
cancelStart=[[[Řēƣűēşţēƌ ċąŋċēĺąţįŏŋ ŏƒ ţĥē ĀƤĬ ţąşķ "{0}" ŵįţĥ ĺŏğĬƌ {1}.]]]
#XMSG: Task log message for the cancelation of the an API task which is no longer running and cannot be canceled
unableToCancel=[[[Ţĥē ĀƤĬ ţąşķ "{0}" ŵįţĥ ĺŏğĬƌ {1} įş ŋŏ ĺŏŋğēŗ ŗűŋŋįŋğ ąŋƌ ċąŋŋŏţ ƃē ċąŋċēĺēƌ.]]]
#XMSG: Task log message when starting an API task. It should carry the name of the API task.
apiRunStart=[[[Ŝţąŗţįŋğ ţĥē ĀƤĬ ţąşķ "{0}".]]]
#XMSG: Task log message when the task preparation is taking too long and times out
preparationTimeout=[[[Ţĥē ţąşķ ρŗēρąŗąţįŏŋ įş ţąķįŋğ ţŏŏ ĺŏŋğ ąŋƌ ĥąş ţįɱēƌ ŏűţ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Task log message when a task failed because a cancel task was triggered
apiRunCancelled=[[[Ţĥē ŗűŋ ţąşķ ŵįţĥ ĺŏğĬƌ {0} ŵąş ċąŋċēĺēƌ ƃŷ ċąŋċēĺ ţąşķ {1}.]]]
#XMSG: Task log message when a cancel task failed to cancel a run task
cancelFailure=[[[Ţĥē ċąŋċēĺ ţąşķ ŵįţĥ ĺŏğĬƌ {1} ƒąįĺēƌ ţŏ ċąŋċēĺ ţĥē ŗűŋ ţąşķ ŵįţĥ ĺŏğĬƌ {0}.]]]
#XMSG: Task log message when configuration of the API task is too large
apiTaskconfigurationSizeLimitError=[[[Ţĥē  ĀƤĬ ţąşķ {0} ċąŋţ şţąŗţ ƃēċąűşē ţĥē şįžē ŏƒ įţş ţąşķ ċŏŋƒįğűŗąţįŏŋ ēχċēēƌş ţĥē ɱąχįɱűɱ ąĺĺŏŵēƌ şįžē ŏƒ 100 ķįƃįƃŷţēş (ĶįƁ).]]]
#XMSG: Task log message for failure in completing the Notification task
notificationTaskCompletionFailure=[[[Ţĥē Ńŏţįƒįċąţįŏŋ ţąşķ "{0}" ĥąş ƒąįĺēƌ ţŏ ċŏɱρĺēţē.]]]
#XMSG: Task log message when starting a Notification task.
notificationTaskRunStart=[[[Ŝţąŗţįŋğ ţĥē Ńŏţįƒįċąţįŏŋ ţąşķ "{0}".]]]
#XMSG: Task log message for a successful Notification task completion
notificationTaskCompletionSuccess=[[[Ţĥē Ńŏţįƒįċąţįŏŋ ţąşķ "{0}" ĥąş ċŏɱρĺēţēƌ.]]]
#XFLD: Label for frequency column
everyLabel=[[[Ĕʋēŗŷ∙∙∙∙∙∙∙∙∙]]]
#XFLD: Plural Recurrence text for Hour
hoursLabel=[[[Ĥŏűŗş∙∙∙∙∙∙∙∙∙]]]
#XFLD: Plural Recurrence text for Day
daysLabel=[[[Ďąŷş]]]
#XFLD: Plural Recurrence text for Month
monthsLabel=[[[Μŏŋţĥş∙∙∙∙∙∙∙∙]]]
#XFLD: Plural Recurrence text for Minutes
minutesLabel=[[[Μįŋűţēş∙∙∙∙∙∙∙]]]
