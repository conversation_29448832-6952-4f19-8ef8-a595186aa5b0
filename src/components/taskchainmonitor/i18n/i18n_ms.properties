

#XFLD: Task Chain header
headerTxt=Rantaian <PERSON>({0})
#XFLD: Task Chain header
headerTxtNew=Rantaian <PERSON> ({0})
#XTXT: Text for Schedule label
scheduleTxt=Jadualkan
#XFLD: Text for Create schedule button
createScheduleTxt=Cipta Jadual
#XFLD: Text for edit schedule
editScheduleTxt=Edit Jadual
#XLFD: Text for delete schedule
deleteScheduleTxt=Padam Jadual
#XTXT: text for refresh button label
refrestTxt=Segar Semula
#XTXT: Text for Completed status
completedTxt=Selesai
#XTX: Text for Running status
runningTxt=Sedang Berjalan
#XTX: Text for failed status
failedTxt=Gagal
#XTX: Text for stopped status
stoppedTxt=Dihentikan
#XTX: Text for stopping status
stoppingTxt=Pemberhentian
#XFLD: Header for Chain name
chainNameLabel=Nama Rantaian Tugas
#XFLD: Header for Chain name
chainNameLabelBus=Nama <PERSON>
#XFLD: Header for Chain name
chainNameLabelBusNew=Objek (Nama <PERSON>gaan)
#XFLD: Header for Chain name
chainNameLabelTech=Nama Teknikal
#XFLD: Header for Chain name
chainNameLabelTechNew=Objek (Nama Teknikal)
#XFLD: Last Run Status label
lastRunStatuslabel=Status Jalanan Akhir
#XFLD: Last Run Status label
lastRunStatuslabelNew=Status
#XFLD: Frequency Label
frequencyLabel=Kekerapan
#XFLD: Frequency Label
frequencyLabelNew=Kekerapan Berjadual
#XFLD: Duration label
durationLabel=Jangka masa
#XFLD: Duration label
durationLabelNew=Jangka Masa Jalanan Terakhir
#XFLD: Run Start label
runStartLabel=Permulaan Jalanan Akhir
#XFLD: Run end label
runEndLabel=Pengakhiran Jalanan Akhir
#XFLD: Next Run label
nextRunlabel=Jalanan Seterusnya
#XFLD: Next Run label
nextRunlabelNew=Jalanan Seterusnya Dijadualkan
#XTXT: Task chain logs link tooltip text
taskChainLogsTxt=Butiran Log Rantaian Tugas
#XTXT: Task chain logs link tooltip text
taskChainLogsTxtNew=Butiran
#XTXT: Scheduled text
scheduledTxt=Dijadualkan
#XTXT: Paused text
pausedTxt=Dihentikan Sementara
#XTXT: Execute button label
runLabel=Jalanan
#XTXT: Execute button label
runLabelNew=Mulakan Jalanan
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Jalankan rantaian tugas
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Jalanan rantaian tugas telah bermula.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Jalanan rantaian tugas telah bermula untuk {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Cuba semula jalanan rantaian tugas kemudian.
#XFLD: Label for schedule owner column
txtScheduleOwner=Pemilik Jadual
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Menunjukkan orang yang mencipta jadual
#XMSG: Task log message for start chain
startChain=Memulakan jalanan rantaian tugas.
#XMSG: Task log message for load chain from repository
loadChainFromRepository=Rantaian tugas dimuatkan dan dimulakan.
#XMSG: Task log message started task
taskStarted=Tugas {0} telah bermula.
#XMSG: Task log message for finished task
taskFinished=Tugas {0} ditamatkan dengan status {1}.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Memuatkan rantaian tugas dan bersedia untuk menjalankan sejumlah {0} tugas yang merupakan sebahagian daripada rantaian ini.
#XMSG: Task log message for starting a subtask
chainStartSubtask=Memicu tugas {0} untuk dijalankan. ID tugas = {1}.
#XMSG: Task log message for finishing a subtask
chainFinishSubtask=Tugas {0} selesai dengan status {1}.
#XMSG: Task log message for indicating chain success
chainCompleted=Semua {0} tugas telah selesai. Status rantaian tugas ditetapkan kepada selesai.
#XMSG: Task log message for indicating chain failure
chainFailed=Pada jumlah {0} tugas, {1} tugas dapat diselesaikan dan {2} tugas tidak berjaya. Status rantaian tugas ditetapkan kepada tidak berjaya.
#XMSG: Task log message for indicating chain cancelation
chainCanceled=Pembatalan jalanan rantaian tugas telah bermula. Id log tugas pembatalan ialah {0}.
#XMSG: Task log message for indicating chain cancelation in the cancel task
chainCanceling=Rantaian pembatalan {0}.
#XMSG: Task log message for general chain runtime error
chainError=Ralat berlaku semasa menjalankan rantaian tugas.
#XMSG: Task log message for starting the overwrite check.
chainOverwriteStart=Menyemak sama ada rantaian tugas dengan id {0} telah selesai.
#XMSG: Task log message indicating the chain is too old.
chainOverwriteTooOld=Tugas untuk dijalankan bagi rantaian {0} tidak ditemui. Jalanan adalah selama {1} minit. Status rantaian tugas ditetapkan kepada tidak berjaya.
#XMSG: Task log message indicating the chain was created too recently.
chainOverwriteTooRecent=Tugas untuk dijalankan bagi rantaian {0} tidak ditemui. Jalanan adalah selama {1} minit. Rantaian tugas masih berjalan.
#XMSG: Task log message indicating a recent heartbeat was found.
chainOverwriteHeartbeatAlive=Rantaian tugas {0} masih berjalan. Selesai: {1}, Berjalan: {2}, Tidak Berjaya: {3}, Dipicu: {4}, Tidak dipicu: {5}.
#XMSG: Task log message for a chain that is not running anymore but not everything was triggered.
chainOverwriteNotAllTriggered=Ralat berlaku semasa jalanan rantaian tugas {0}, dan bukan semua tugas boleh dipicu. Selesai: {1}, Berjalan: {2}, Tidak Berjaya: {3}, Dipicu: {4}, Tidak Dipicu: {5}. Rantaian tugas ini telah ditulis ganti.
#XMSG: Task log message for a chain that contains a failed subtask.
chainOverwriteAnySubtaskFailed=Rantai tugas {0} selesai. Satu daripada rantaian tidak berjaya, menetapkan rantai kepada tidak berjaya.
#XMSG: Task log message could not determine status.
chainOverwriteNotDetermined=Cuba semula capaian status tugas kemudian kerana ralat tidak dijangka berlaku. Cuba semula dan hubungi Sokongan SAP anda jika ralat berterusan.
#XMSG: Task log message could not take over
failedTakeover=Cuba ambil alih tugas sedia ada sekali lagi.
#XMSG: Task log parallel check error
parallelCheckError=Tugas tidak boleh diproses kerana tugas lain sedang dijalankan dan sudah menyekat tugas ini.
#XMSG: Task log parallel task runnig error
parallelTaskRunning=Percanggahan tugas telah berjalan.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Status {0} ketika jalanan dengan ID korelasi {1}.
#XMSG: Task log message successful takeover
successTakeover=Kunci sebelah kiri perlu dikeluarkan. Kunci baharu untuk tugas ini telah ditetapkan.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Kunci tugas telah diambil alih oleh tugas lain.
#XMSG: Schedule created alert message
createScheduleSuccess=Jadual dicipta
#XMSG: Task log message for taskFinishedAt
taskFinishedAt=Tugas {0} selesai pada {2} dengan status {1}.
#XMSG: Placeholder symbol for empty columns
emptyCol=--
#XMSG: Paused Text
txtExpired=Telah Tamat Tempoh
#XMSG: Update task schedule success Toast message
updateScheduleSuccess=Jadual dikemas kini
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Jadual dipadam.
#XMSG: Task chain might have children error text
taskChainNoChildErrorText=Cuba semula tugas untuk menjanakan rancangan supaya anda boleh tunjukkan anak bagi rantaian tugas.
#XMSG: Task chain repair recent failed run label
retryRunLabel=Cuba Semula Jalanan Terkini
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Jalanan cubaan semula rantaian tugas telah bermula
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Jalanan cubaan semula rantaian tugas telah gagal
#XMSG: chain repair message
chainRetried=Cubaan semula rantaian tugas dipicu oleh pengguna {0}
#XMSG: chain not found during run
chainNotFoundDuringRun=Cari semula rantaian tugas {0}. Semak jika ia wujud melalui Pembina Data dan pastikan anda mengatur duduk rantaian.
#XMSG: chain is not DAG
chainNotDag=Mulakan semula rantaian tugas {0}. Strukturnya tidak sah. Semak rantaian tugas dalam Pembina Data.
#XMSG: chain has not valid parameters
notValidParameters=Mulakan semula rantaian tugas {0}. Satu atau lebih parameternya tidak sah. Semak rantaian tugas dalam Pembina Data.
#XMSG: Configuration parameter value size exceeds the maximum allowed limit
configurationSizeLimitError=Mulakan semula rantaian tugas {0}. Saiz konfigurasi tugas dalam rantaian melebihi saiz maksimum yang dibenarkan iaitu 100 kibibait (KiB).
#XMSG: Task {0} has input parameters
taskHasInputParameters=Tugas {0} mempunyai parameter input.
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Percanggahan tugas telah berjalan
#XMSG: error message for reading data from backend
txtReadBackendError=Terdapat ralat semasa bacaan dari bahagian belakang.
##XMSG: error message for admission control rejection
admissionControlError=Tugas gagal kerana Penolakan Kawalan Kemasukan SAP HANA.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Umpukkan Jadual kepada Saya
#XBUT: Pause schedule menu label
pauseScheduleLabel=Hentikan Seketika Jadual
#XBUT: Resume schedule menu label
resumeScheduleLabel=Sambung Semula Jadual
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Ralat berlaku semasa mengeluarkan jadual.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Ralat berlaku semasa mengumpukkan jadual.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Ralat berlaku semasa menghentikan seketika jadual.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Ralat berlaku semasa menyambung semula jadual.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Memadam {0} jadual
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Mengubah pemilik bagi {0} jadual
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Menghentikan seketika {0} jadual
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Menyambung semula {0} jadual
#XBUT: Select Columns Button
selectColumnsBtn=Pilih Lajur
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Terdapat ralat dengan jalanan terkini cuba semula kerana jalanan tugas sebelumnya gagal sebelum penjanaan rancangan.
#XFLD: Refresh tooltip
TEXT_REFRESH=Segar Semula
#XFLD: Select Columns tooltip
text_selectColumns=Pilih Lajur

#Messages for BW Process Chain task log of the Data Integration Monitor
#XMSG: Task log message for successfully starting a BW process chain
startBwProcessChainSuccess=Rantaian proses BW "{0}" telah berjaya dimulakan dalam penyewa SAP BW Bridge.
#XMSG: Task log message for skipping a BW process chain execution
bwProcessChainSkipped=Rantaian proses BW "{0}" telah dilangkau sama ada disebabkan oleh ketidaksediaan data baharu atau pelaksanaan tidak berjaya sebelumnya.
#XMSG: Task log message for failure in starting a BW process chain
startBwProcessChainFailure=Cuba mulakan semula rantaian proses BW "{0}" dalam penyewa SAP BW Bridge.
#XMSG: Task log message for failure in getting the status of a BW process chain
getBwProcessChainStatusFailure=Tugas tidak berjaya kerana kami tidak dapat mendapatkan semula status rantaian proses BW "{0}" disebabkan masalah mewujudkan sambungan ke SAP BW Bridge. Buka aplikasi "Sambungan" dan sahkan sambungan SAP BW Bridge dalam ruang "{1}". Jika anda tidak mempunyai capaian kepada aplikasi "Sambungan", hubungi pentadbir anda.
#XMSG: Task log message for failure in completing a BW process chain
completeBwProcessChainFailure=Cuba selesaikan semula rantaian proses BW "{0}" dalam penyewa SAP BW Bridge.
#XMSG: Task log message for the clickable text of the SAP BW Bridge Monitor link
bwBridgeMonitorLinkText=Paparkan butiran dalam Pemantau SAP BW Bridge
#XMSG warning message while retrieving the task log messages if the user does not have access to the SAP BW Bridge space or to the shared object's space
missingPrivilegeOnSpaceError=Cuba dapatkan semula mesej, atau anda perlu mempunyai kebenaran yang diperlukan untuk memaparkannya.

#Messages for API task log of the Data Integration Monitor
#XMSG: Task log message for displaying the API response
viewResponse=Maklum balas diterima untuk "{0}".
#XMSG: Task log message for empty ID in the API response to retrieve the status
emptyJobId=Tiada ID dikembalikan dalam kandungan maklum balas untuk laluan JSON: "{0}".
#XMSG: Task log message for empty success value in the API response
emptySuccessValue=Tiada nilai kejayaan dikembalikan dalam kandungan maklum balas untuk laluan JSON: "{0}".
#XMSG: Task log message for empty error value in the API response
emptyErrorValue=Tiada nilai ralat dikembalikan dalam kandungan maklum balas untuk laluan JSON: "{0}".
#XMSG: Task log message for none of the success/error conditions matching for the values in the API response
conditionsNotFulfilled=Tiada syarat penunjuk kejayaan atau ralat yang ditentukan sepadan dengan nilai dalam maklum balas.
#XMSG: Task log message for the API response status being unsuccessful
statusNotOk=Maklum balas untuk "{1}" menyebabkan kod status tidak berjaya: {0}.
#XMSG: Task log message for the API response body being empty
emptyResponseBody=Kandungan maklum balas kosong.
#XMSG: Task log message for the API response location header being invalid
emptyLocationHeader=Pengepala lokasi dalam maklum balas kosong.
#XMSG: Task log message for failure in retrieving the status of the invoked API
getStatusFailure=Status API yang digunakan tidak berjaya didapatkan semula.
#XMSG: Task log message for failure in completing the API task
completionFailure=Tugas API "{0}" tidak berjaya diselesaikan.
#XMSG: Task log message for a successful API task completion
apiCompleted=Tugas API "{0}" telah berjaya diselesaikan.
#XMSG: Task log message for an API task with an invalid configuration
invalidConfiguration=Konfigurasi tugas API "{0}" tidak sah. Semak konfigurasi dan cuba lagi.
#XMSG: Task log message for the API task being canceled
cancelStart=Pembatalan tugas API "{0}" dengan logId {1} diminta.
#XMSG: Task log message for the cancelation of the an API task which is no longer running and cannot be canceled
unableToCancel=Tugas API "{0}" dengan logId {1} tidak lagi dijalankan dan tidak boleh dibatalkan.
#XMSG: Task log message when starting an API task. It should carry the name of the API task.
apiRunStart=Memulakan tugas API "{0}".
#XMSG: Task log message when the task preparation is taking too long and times out
preparationTimeout=Penyediaan tugas mengambil masa terlalu lama dan telah tamat masa.
#XMSG: Task log message when a task failed because a cancel task was triggered
apiRunCancelled=Tugas jalanan dengan logId {0} telah dibatalkan oleh tugas pembatalan {1}.
#XMSG: Task log message when a cancel task failed to cancel a run task
cancelFailure=Tugas pembatalan dengan logId {1} tidak berjaya membatalkan tugas jalanan dengan logId {0}.
#XMSG: Task log message when configuration of the API task is too large
apiTaskconfigurationSizeLimitError=Tugas API {0} tidak boleh dimulakan kerana saiz konfigurasi tugasnya melebihi saiz maksimum yang dibenarkan iaitu 100 kibibait (KiB).
#XMSG: Task log message for failure in completing the Notification task
notificationTaskCompletionFailure=Tugas Pemberitahuan "{0}" tidak berjaya diselesaikan.
#XMSG: Task log message when starting a Notification task.
notificationTaskRunStart=Memulakan tugas Pemberitahuan "{0}".
#XMSG: Task log message for a successful Notification task completion
notificationTaskCompletionSuccess=Tugas Pemberitahuan "{0}" telah selesai.
#XFLD: Label for frequency column
everyLabel=Setiap
#XFLD: Plural Recurrence text for Hour
hoursLabel=Jam
#XFLD: Plural Recurrence text for Day
daysLabel=Hari
#XFLD: Plural Recurrence text for Month
monthsLabel=Bulan
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minit
