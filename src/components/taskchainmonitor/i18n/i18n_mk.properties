

#XFLD: Task Chain header
headerTxt=Синџири од задачи({0}
#XFLD: Task Chain header
headerTxtNew=Синџири од задачи ({0})
#XTXT: Text for Schedule label
scheduleTxt=Распоред
#XFLD: Text for Create schedule button
createScheduleTxt=Создај распоред
#XFLD: Text for edit schedule
editScheduleTxt=Уреди го распоредот
#XLFD: Text for delete schedule
deleteScheduleTxt=Избриши го распоредот
#XTXT: text for refresh button label
refrestTxt=Освежи
#XTXT: Text for Completed status
completedTxt=Завршено
#XTX: Text for Running status
runningTxt=Се извршува
#XTX: Text for failed status
failedTxt=Неуспешно
#XTX: Text for stopped status
stoppedTxt=Сопрено
#XTX: Text for stopping status
stoppingTxt=Се сопира
#XFLD: Header for Chain name
chainNameLabel=Назив на синџир од задачи
#XFLD: Header for Chain name
chainNameLabelBus=Деловен назив
#XFLD: Header for Chain name
chainNameLabelBusNew=Објект (деловен назив)
#XFLD: Header for Chain name
chainNameLabelTech=Технички назив
#XFLD: Header for Chain name
chainNameLabelTechNew=Објект (технички назив)
#XFLD: Last Run Status label
lastRunStatuslabel=Статус на последното извршување
#XFLD: Last Run Status label
lastRunStatuslabelNew=Статус
#XFLD: Frequency Label
frequencyLabel=Зачестеност
#XFLD: Frequency Label
frequencyLabelNew=Распоредена зачестеност
#XFLD: Duration label
durationLabel=Времетраење
#XFLD: Duration label
durationLabelNew=Времетраење на последното извршување
#XFLD: Run Start label
runStartLabel=Почеток на последното извршување
#XFLD: Run end label
runEndLabel=Крај на последното извршување
#XFLD: Next Run label
nextRunlabel=Следно извршување
#XFLD: Next Run label
nextRunlabelNew=Закажано следно извршување
#XTXT: Task chain logs link tooltip text
taskChainLogsTxt=Детали за дневникот на синџирот од задачи
#XTXT: Task chain logs link tooltip text
taskChainLogsTxtNew=Детали
#XTXT: Scheduled text
scheduledTxt=Закажано
#XTXT: Paused text
pausedTxt=Паузирано
#XTXT: Execute button label
runLabel=Изврши
#XTXT: Execute button label
runLabelNew=Започни го извршувањето
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Изврши го синџирот од задачи
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Извршувањето на синџирот од задачи започна.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Извршувањето на синџирот од задачи започна за {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Не успеа да се изврши синџирот од задачи.
#XFLD: Label for schedule owner column
txtScheduleOwner=Сопственик на распоредот
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Покажува кој го создал распоредот
#XMSG: Task log message for start chain
startChain=Започна извршувањето на синџирот од задачи.
#XMSG: Task log message for load chain from repository
loadChainFromRepository=Синџирот од задачи е вчитан и започнат.
#XMSG: Task log message started task
taskStarted=Задачата {0} започна. 
#XMSG: Task log message for finished task
taskFinished=Задачата {0} заврши со статус {1}.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Се вчитува синџирот од задачи и се подготвува за извршување на вкупно {0} задачи коишто се дел од овој синџир.
#XMSG: Task log message for starting a subtask
chainStartSubtask=Активирање на задача {0} за извршување. ИД на задачата = {1}.
#XMSG: Task log message for finishing a subtask
chainFinishSubtask=Задачата {0} е завршена со статус {1}.
#XMSG: Task log message for indicating chain success
chainCompleted=Сите {0} задачи се завршени. Статусот на синџирот од задачи е поставен на Завршено.
#XMSG: Task log message for indicating chain failure
chainFailed=Од вкупно {0} задачи, {1} задачи завршија успешно, додека {2} задачи завршија неуспешно. Статусот на синџирот од задачи е поставен на Неуспешно.
#XMSG: Task log message for indicating chain cancelation
chainCanceled=Започна откажувањето на извршувањето на синџирот од задачи. ИД-бројот на задачата за откажување во дневникот е {0}.
#XMSG: Task log message for indicating chain cancelation in the cancel task
chainCanceling=Се откажува синџирот {0}.
#XMSG: Task log message for general chain runtime error
chainError=Настана неочекувана грешка при извршувањето на синџирот од задачи.
#XMSG: Task log message for starting the overwrite check.
chainOverwriteStart=Проверува дали синџирот од задачи со ИД {0} е завршен.
#XMSG: Task log message indicating the chain is too old.
chainOverwriteTooOld=Не може да се најдат задачи за извршување за синџирот {0}. Извршувањето трае {1} минути. Статусот на синџирот од задачи е поставен на Неуспешно.
#XMSG: Task log message indicating the chain was created too recently.
chainOverwriteTooRecent=Не може да се најдат задачи за извршување за синџирот {0}. Извршувањето трае {1} минути. Синџирот од задачи сè уште се извршува.
#XMSG: Task log message indicating a recent heartbeat was found.
chainOverwriteHeartbeatAlive=Синџирот на задачи {0} сè уште се извршува. Завршено: {1}, Се извршува: {2}, Неуспешно: {3}, Активирано: {4}, Не е активирано: {5}.
#XMSG: Task log message for a chain that is not running anymore but not everything was triggered.
chainOverwriteNotAllTriggered=Настана грешка при извршување на синџирот од задачи {0} и не може да се активираат сите задачи. Завршено: {1}, Се извршува: {2}, Неуспешно: {3}, Активирано: {4}, Не е активирано: {5}. Овој синџир од задачи е презапишан.
#XMSG: Task log message for a chain that contains a failed subtask.
chainOverwriteAnySubtaskFailed=Синџирот од задачи {0} е завршен. Една задача од синџирот не успеа, се поставува синџирот на Неуспешно.
#XMSG: Task log message could not determine status.
chainOverwriteNotDetermined=Неочекувана грешка нè спречува да пристапиме до статусот на задачата. Обидете се повторно и контактирајте со подршката на SAP ако грешката продолжи да се појавува.
#XMSG: Task log message could not take over
failedTakeover=Не успеа да се преземе постојната задача.
#XMSG: Task log parallel check error
parallelCheckError=Задачата не може да се обработи бидејќи друга задача се извршува и веќе ја блокира оваа задача.
#XMSG: Task log parallel task runnig error
parallelTaskRunning=Веќе се извршува задача во конфликт.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Статус {0} при извршување со ИД на колерација {1}.
#XMSG: Task log message successful takeover
successTakeover=Преостаната блокада се ослободи. Поставена е нова блокада за оваа задача.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Блокадата на оваа задача ја презеде друга задача.
#XMSG: Schedule created alert message
createScheduleSuccess=Распоредот е создаден
#XMSG: Task log message for taskFinishedAt
taskFinishedAt=Задачата {0} е завршена во {2} со статус {1}.
#XMSG: Placeholder symbol for empty columns
emptyCol=--
#XMSG: Paused Text
txtExpired=Истечено
#XMSG: Update task schedule success Toast message
updateScheduleSuccess=Распоредот е ажуриран
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Распоредот е избришан.
#XMSG: Task chain might have children error text
taskChainNoChildErrorText=Синџирот од задачи може да има подредени елементи што не се прикажани бидејќи задачата не успеа пред да се генерира планот.
#XMSG: Task chain repair recent failed run label
retryRunLabel=Обиди се повторно со последното извршување
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Обидот за повторно извршување на синџирот од задачи започна.
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Обидот за повторно извршување на синџирот од задачи е неуспешен.
#XMSG: chain repair message
chainRetried=Повторен обид за синџирот од задачи активиран од корисникот {0}
#XMSG: chain not found during run
chainNotFoundDuringRun=Не може да се најде синџирот од задачи {0}. Проверете дали постои преку Алатката за градење податоци и проверете дали синџирот е применет.
#XMSG: chain is not DAG
chainNotDag=Не може да започне синџирот од задачи {0}. Структурата е неважечка. Проверете го синџирот од задачи во Алатката за градење податоци.
#XMSG: chain has not valid parameters
notValidParameters=Не може да започне синџирот од задачи {0}. Еден или повеќе параметри се неважечки. Проверете го синџирот од задачи во Алатката за градење податоци.
#XMSG: Configuration parameter value size exceeds the maximum allowed limit
configurationSizeLimitError=Не може да се започне синџирот од задачи {0}. Големината на конфигурацијата на задачата во синџирот ја надминува макс. дозволена големина од 100 кибибајти (KiB).
#XMSG: Task {0} has input parameters
taskHasInputParameters=Задачата {0} има влезни параметри.
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Веќе се извршува задача во конфликт
#XMSG: error message for reading data from backend
txtReadBackendError=Изгледа дека настана грешка при читањето од серверскиот дел.
##XMSG: error message for admission control rejection
admissionControlError=Задачата не успеа поради одбивање на контролите кон SAP HANA.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Додели ми го распоредот
#XBUT: Pause schedule menu label
pauseScheduleLabel=Паузирај го распоредот
#XBUT: Resume schedule menu label
resumeScheduleLabel=Продолжи го распоредот
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Настана грешка при отстранување на распоредите.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Настана грешка при доделување на распоредите.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Настана грешка при паузирање на распоредите.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Настана грешка при продолжување на распоредите.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Се бришат {0} распореди
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Се менува сопственикот на {0} распореди
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Се паузираат {0} распореди
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Се продолжуваат {0} распореди
#XBUT: Select Columns Button
selectColumnsBtn=Избери колони
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Се чини дека имало грешка при повторниот обид на последното извршување бидејќи претходното извршување на задачата не успеа пред да се генерира планот.
#XFLD: Refresh tooltip
TEXT_REFRESH=Освежи
#XFLD: Select Columns tooltip
text_selectColumns=Избери колони

#Messages for BW Process Chain task log of the Data Integration Monitor
#XMSG: Task log message for successfully starting a BW process chain
startBwProcessChainSuccess=Синџирот од процеси на BW „{0}“ започна успешно во закупецот на SAP BW Bridge.
#XMSG: Task log message for skipping a BW process chain execution
bwProcessChainSkipped=Синџирот од процеси на BW „{0}“ е прескокнат поради недостапност на нови податоци или неуспешно претходно извршување.
#XMSG: Task log message for failure in starting a BW process chain
startBwProcessChainFailure=Синџирот од процеси на BW „{0}“ не успеа да започне во закупецот на SAP BW Bridge.
#XMSG: Task log message for failure in getting the status of a BW process chain
getBwProcessChainStatusFailure=Задачата не успеа бидејќи не може да го вчитаме статусот на синџирот од процеси на BW „{0}“ поради проблем при воспоставување на врската со SAP BW Bridge. Отворете ја апликацијата „Врски“ и потврдете ја врската SAP BW Bridge во просторот „{1}“. Ако немате пристап до апликацијата „Врски“, контактирајте со администраторот.
#XMSG: Task log message for failure in completing a BW process chain
completeBwProcessChainFailure=Синџирот од процеси на BW „{0}“ не успеа да заврши во закупецот на SAP BW Bridge.
#XMSG: Task log message for the clickable text of the SAP BW Bridge Monitor link
bwBridgeMonitorLinkText=Прикажи ги деталите во надзорот на системот SAP BW Bridge
#XMSG warning message while retrieving the task log messages if the user does not have access to the SAP BW Bridge space or to the shared object's space
missingPrivilegeOnSpaceError=Настана проблем при вчитување на пораките или пак ја немате потребната дозвола за да ги прикажете.

#Messages for API task log of the Data Integration Monitor
#XMSG: Task log message for displaying the API response
viewResponse=Добиен е одговор за „{0}“.
#XMSG: Task log message for empty ID in the API response to retrieve the status
emptyJobId=ИД-бројот не е вратен во содржината на одговорот за JSON-патеката: „{0}“.
#XMSG: Task log message for empty success value in the API response
emptySuccessValue=Вредноста на успехот не е вратена во содржината на одговорот за JSON-патеката: „{0}“.
#XMSG: Task log message for empty error value in the API response
emptyErrorValue=Вредноста на грешката не е вратена во содржината на одговорот за JSON-патеката: „{0}“.
#XMSG: Task log message for none of the success/error conditions matching for the values in the API response
conditionsNotFulfilled=Ниту еден од наведените услови за показатели на успех или грешка не се совпаѓаат со вредностите во одговорот.
#XMSG: Task log message for the API response status being unsuccessful
statusNotOk=Одговорот за „{1}“ врати неуспешен код на статус: {0}.
#XMSG: Task log message for the API response body being empty
emptyResponseBody=Содржината на одговорот е празна.
#XMSG: Task log message for the API response location header being invalid
emptyLocationHeader=Заглавието на локацијата во одговорот е празно.
#XMSG: Task log message for failure in retrieving the status of the invoked API
getStatusFailure=Не може да се вчита статусот на повиканиот API-интерфејс.
#XMSG: Task log message for failure in completing the API task
completionFailure=Задачата за API „{0}“ не успеа да заврши.
#XMSG: Task log message for a successful API task completion
apiCompleted=Задачата за API „{0}“ успеа да се заврши.
#XMSG: Task log message for an API task with an invalid configuration
invalidConfiguration=Конфигурацијата на задачата за API „{0}“ e неважечка. Проверете ја конфигурацијата и обидете се повторно.
#XMSG: Task log message for the API task being canceled
cancelStart=Побарано откажување на API-задачата „{0}“ со logid {1}.
#XMSG: Task log message for the cancelation of the an API task which is no longer running and cannot be canceled
unableToCancel=API-задачата „{0}“ со logid {1} повеќе не се извршува и не може да се откаже.
#XMSG: Task log message when starting an API task. It should carry the name of the API task.
apiRunStart=Започнува API-задачата „{0}“. 
#XMSG: Task log message when the task preparation is taking too long and times out
preparationTimeout=Подготовката на задачата трае предолго и времето истече.
#XMSG: Task log message when a task failed because a cancel task was triggered
apiRunCancelled=Задачата за извршување со logId {0} e откажана со задачата за откажување {1}.
#XMSG: Task log message when a cancel task failed to cancel a run task
cancelFailure=Задачата за откажување со logId {1} не успеа да ја откаже задачата за извршување со logId {0}.
#XMSG: Task log message when configuration of the API task is too large
apiTaskconfigurationSizeLimitError=Не може да се започне задачата за API {0} бидејќи големината на конфигурацијата на задачата ја надминува макс. дозволена големина од 100 кибибајти (KiB).
#XMSG: Task log message for failure in completing the Notification task
notificationTaskCompletionFailure=Задачата за известување „{0}“ не успеа да заврши.
#XMSG: Task log message when starting a Notification task.
notificationTaskRunStart=Започнува задачата за известување „{0}“.
#XMSG: Task log message for a successful Notification task completion
notificationTaskCompletionSuccess=Задачата за известување „{0}“ заврши.
#XFLD: Label for frequency column
everyLabel=Секој
#XFLD: Plural Recurrence text for Hour
hoursLabel=Часови
#XFLD: Plural Recurrence text for Day
daysLabel=Денови
#XFLD: Plural Recurrence text for Month
monthsLabel=Месеци
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Минути
