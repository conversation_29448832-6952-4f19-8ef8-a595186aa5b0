

#XFLD: Task Chain header
headerTxt=Tehtäväketjut ({0})
#XFLD: Task Chain header
headerTxtNew=Tehtäväketjut ({0})
#XTXT: Text for Schedule label
scheduleTxt=Ajoita
#XFLD: Text for Create schedule button
createScheduleTxt=Luo aikata<PERSON>
#XFLD: Text for edit schedule
editScheduleTxt=Muokkaa aikataulua
#XLFD: Text for delete schedule
deleteScheduleTxt=Poista aikataulu
#XTXT: text for refresh button label
refrestTxt=Päivitä
#XTXT: Text for Completed status
completedTxt=Suoritettu loppuun
#XTX: Text for Running status
runningTxt=Käynnissä
#XTX: Text for failed status
failedTxt=Epäonnistui
#XTX: Text for stopped status
stoppedTxt=Pysäytetty
#XTX: Text for stopping status
stoppingTxt=Pysäytetään
#XFLD: Header for Chain name
chainNameLabel=Tehtäväketjun nimi
#XFLD: Header for Chain name
chainNameLabelBus=Liiketoiminnallinen nimi
#XFLD: Header for Chain name
chainNameLabelBusNew=Objekti (liiketoiminnallinen nimi)
#XFLD: Header for Chain name
chainNameLabelTech=Tekninen nimi
#XFLD: Header for Chain name
chainNameLabelTechNew=Objekti (tekninen nimi)
#XFLD: Last Run Status label
lastRunStatuslabel=Edellisen ajon tila
#XFLD: Last Run Status label
lastRunStatuslabelNew=Tila
#XFLD: Frequency Label
frequencyLabel=Tiheys
#XFLD: Frequency Label
frequencyLabelNew=Suunniteltu tiheys
#XFLD: Duration label
durationLabel=Kesto
#XFLD: Duration label
durationLabelNew=Viimeisen ajon kesto
#XFLD: Run Start label
runStartLabel=Edellisen ajon käynnistys
#XFLD: Run end label
runEndLabel=Edellisen ajon loppu
#XFLD: Next Run label
nextRunlabel=Seuraava ajo
#XFLD: Next Run label
nextRunlabelNew=Seuraava suunniteltu ajo
#XTXT: Task chain logs link tooltip text
taskChainLogsTxt=Tehtäväketjulokin lisätiedot
#XTXT: Task chain logs link tooltip text
taskChainLogsTxtNew=Lisätiedot
#XTXT: Scheduled text
scheduledTxt=Ajoitettu
#XTXT: Paused text
pausedTxt=Keskeytetty
#XTXT: Execute button label
runLabel=Aja
#XTXT: Execute button label
runLabelNew=Käynnistä ajo
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Aja tehtäväketju
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Tehtäväketjun ajo on käynnistetty.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Tehtäväketjun ajo on käynnistetty kohteelle {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Tehtäväketjun ajo epäonnistui.
#XFLD: Label for schedule owner column
txtScheduleOwner=Aikataulun omistaja
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Näyttää, kuka loi aikataulun
#XMSG: Task log message for start chain
startChain=Käynnistetään tehtäväketjun ajoa.
#XMSG: Task log message for load chain from repository
loadChainFromRepository=Tehtäväketju ladattu ja alustettu.
#XMSG: Task log message started task
taskStarted=Tehtävä {0} käynnistetty.
#XMSG: Task log message for finished task
taskFinished=Tehtävä {0} päättyi tilaan {1}.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Ladataan tehtäväketjua ja valmistellaan yhteensä {0} sellaisen tehtävän ajoa, jotka ovat osa tätä ketjua.
#XMSG: Task log message for starting a subtask
chainStartSubtask=Käynnistetään tehtävää {0} ajoa varten. Tehtävän tunnus = {1}.
#XMSG: Task log message for finishing a subtask
chainFinishSubtask=Tehtävä {0} päättynyt, tila {1}.
#XMSG: Task log message for indicating chain success
chainCompleted=Kaikki {0} tehtävää on päätetty. Tehtäväketjun tilaksi asetetaan Päätetty.
#XMSG: Task log message for indicating chain failure
chainFailed=Yhteensä {0} tehtävästä {1} tehtävää voitiin päättää ja {2} tehtävää epäonnistui. Tehtäväketjun tilaksi asetetaan Epäonnistunut.
#XMSG: Task log message for indicating chain cancelation
chainCanceled=Tehtäväketjun ajon peruutus on käynnistynyt. Peruutustehtävän lokitunnus on {0}.
#XMSG: Task log message for indicating chain cancelation in the cancel task
chainCanceling=Perutetaan ketju {0}.
#XMSG: Task log message for general chain runtime error
chainError=Odottamaton virhe tapahtui tehtäväketjun ajon aikana.
#XMSG: Task log message for starting the overwrite check.
chainOverwriteStart=Tarkistetaan, onko tehtäväketju, jonka tunnus on {0}, päättynyt.
#XMSG: Task log message indicating the chain is too old.
chainOverwriteTooOld=Ketjulle {0} ajettavia tehtäviä ei löytynyt. Ajo on {1} minuuttia vanha. Tehtäväketjun tilaksi asetetaan Epäonnistunut.
#XMSG: Task log message indicating the chain was created too recently.
chainOverwriteTooRecent=Ketjulle {0} ajettavia tehtäviä ei löytynyt. Ajo on {1} minuuttia vanha. Tehtäväketju on vielä käynnissä.
#XMSG: Task log message indicating a recent heartbeat was found.
chainOverwriteHeartbeatAlive=Tehtäväketju {0} on vielä käynnissä. Päätetty: {1}, Käynnissä: {2}, Epäonnistunut: {3}, Käynnistetty: {4}, Ei käynnistetty: {5}.
#XMSG: Task log message for a chain that is not running anymore but not everything was triggered.
chainOverwriteNotAllTriggered=Virhe tehtäväketjun {0} ajossa, eikä kaikkia tehtäviä voitu käynnistää. Päätetty: {1}, Käynnissä: {2}, Epäonnistunut: {3}, Käynnistetty: {4}, Ei käynnistetty: {5}. Tämä tehtäväketju on korvattu.
#XMSG: Task log message for a chain that contains a failed subtask.
chainOverwriteAnySubtaskFailed=Tehtäväketju {0} päättynyt. Yksi ketjun tehtävistä epäonnistui, ketjun tilaksi asetetaan Epäonnistunut.
#XMSG: Task log message could not determine status.
chainOverwriteNotDetermined=Odottamaton virhe estää tehtävän tilan haun. Yritä uudelleen ja ota yhteys SAP-tukeen, jos virhe toistuu.
#XMSG: Task log message could not take over
failedTakeover=Olemassa olevaa tehtävää ei voitu ottaa käsiteltäväksi.
#XMSG: Task log parallel check error
parallelCheckError=Tehtävää ei voi käsitellä, koska toinen tehtävä on käynnissä ja estää tämän tehtävän.
#XMSG: Task log parallel task runnig error
parallelTaskRunning=Ristiriitainen tehtävä on jo käynnissä.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Tila {0} ajon aikana korrelaatiotunnuksen {1} kanssa.
#XMSG: Task log message successful takeover
successTakeover=Jäljelle jäänyt lukitus oli vapautettava. Tämän tehtävän uusi lukitus on määritetty.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Toinen tehtävä otti tämän tehtävän lukituksen käsiteltäväksi.
#XMSG: Schedule created alert message
createScheduleSuccess=Aikataulu luotu
#XMSG: Task log message for taskFinishedAt
taskFinishedAt=Tehtävä {0} suoritettu {2} , tila {1}.
#XMSG: Placeholder symbol for empty columns
emptyCol=--
#XMSG: Paused Text
txtExpired=Vanhentunut
#XMSG: Update task schedule success Toast message
updateScheduleSuccess=Aikataulu päivitetty
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Aikataulu poistettu.
#XMSG: Task chain might have children error text
taskChainNoChildErrorText=Tehtäväketjulla voi olla alatason kohteita, joita ei näytetä, koska tehtävä epäonnistui ennen kuin suunnitelma voitiin generoida.
#XMSG: Task chain repair recent failed run label
retryRunLabel=Yritä viimeistä ajoa uudelleen
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Tehtäväketjun uudelleenyritysajo on käynnistetty
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Tehtäväketjun uudelleenyritysajo on epäonnistunut
#XMSG: chain repair message
chainRetried=Käyttäjä {0} on käynnistänyt tehtäväketjun uudelleenyrityksen
#XMSG: chain not found during run
chainNotFoundDuringRun=Tehtäväketjua {0} ei löydy. Tarkista tietojen muodostimen kautta, onko ketju olemassa, ja varmista, että ketju on otettu käyttöön.
#XMSG: chain is not DAG
chainNotDag=Tehtäväketjua {0} ei voida käynnistää. Ketjun rakenne on virheellinen. Tarkista tehtäväketju tietojen muodostimessa.
#XMSG: chain has not valid parameters
notValidParameters=Tehtäväketjua {0} ei voida käynnistää. Vähintään yksi sen parametreista on virheellinen. Tarkista tehtäväketju tietojen muodostimessa.
#XMSG: Configuration parameter value size exceeds the maximum allowed limit
configurationSizeLimitError=Tehtäväketjua {0} ei voida käynnistää. Tehtävän konfiguraation koko ketjussa ylittää suurimman sallitun koon, joka on 100 kibibittiä (KiB).
#XMSG: Task {0} has input parameters
taskHasInputParameters=Tehtävällä {0} on syöttöparametreja.
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Ristiriitainen tehtävä on jo käynnissä.
#XMSG: error message for reading data from backend
txtReadBackendError=Vaikuttaa siltä, että taustasta luettaessa tapahtui virhe.
##XMSG: error message for admission control rejection
admissionControlError=Tehtävä epäonnistui SAP HANA -pääsynvalvonnan hylkäyksen vuoksi.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Kohdista aikataulu minulle
#XBUT: Pause schedule menu label
pauseScheduleLabel=Keskeytä aikataulu
#XBUT: Resume schedule menu label
resumeScheduleLabel=Palaa aikatauluun
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Aikatauluja poistaessa tapahtui virhe.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Aikatauluja kohdistaessa tapahtui virhe.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Aikatauluja keskeyttäessä tapahtui virhe.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Aikatauluihin palaamisessa tapahtui virhe.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Poistetaan {0} aikataulua
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Muutetaan {0} aikataulun omistaja
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Keskeytetään {0} aikataulua
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Jatketaan {0} aikataulua
#XBUT: Select Columns Button
selectColumnsBtn=Valitse sarakkeet
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Viimeisimmän ajon toistossa on ilmeisesti tapahtunut virhe, koska edellinen tehtävän ajo epäonnistui ennen kuin suunnitelma voitiin generoida.
#XFLD: Refresh tooltip
TEXT_REFRESH=Päivitä
#XFLD: Select Columns tooltip
text_selectColumns=Valitse sarakkeet

#Messages for BW Process Chain task log of the Data Integration Monitor
#XMSG: Task log message for successfully starting a BW process chain
startBwProcessChainSuccess=BW-prosessiketjun "{0}" käynnistys SAP BW -siltavuokralaisessa onnistui.
#XMSG: Task log message for skipping a BW process chain execution
bwProcessChainSkipped=BW-prosessiketju "{0}" ohitettiin, koska uusia tietoja ei ole saatavilla, tai koska edellinen suoritus epäonnistui.
#XMSG: Task log message for failure in starting a BW process chain
startBwProcessChainFailure=BW-prosessiketjun "{0}" käynnistys SAP BW -siltavuokralaisessa epäonnistui.
#XMSG: Task log message for failure in getting the status of a BW process chain
getBwProcessChainStatusFailure=Tehtävä epäonnisuti, sillä BW-prosessiketjun "{0}" tilaa ei voitu noutaa SAP BW -sillan yhteysongelman vuoksi. Avaa Yhteydet-sovellus ja vahvista SAP BW -sillan yhteys tilassa "{1}". Jos sinulla ei ole Yhteydet-sovelluksen käyttöoikeuksia, ota yhteys pääkäyttäjään.
#XMSG: Task log message for failure in completing a BW process chain
completeBwProcessChainFailure=BW-prosessiketjun "{0}" päättäminen SAP BW -siltavuokralaisessa epäonnistui.
#XMSG: Task log message for the clickable text of the SAP BW Bridge Monitor link
bwBridgeMonitorLinkText=Näytä SAP BW -siltavalvonnan lisätiedot
#XMSG warning message while retrieving the task log messages if the user does not have access to the SAP BW Bridge space or to the shared object's space
missingPrivilegeOnSpaceError=Sanomien noudossa ilmeni ongelma, tai sinulla ei ole niiden tarkasteluun tarvittavaa oikeutta.

#Messages for API task log of the Data Integration Monitor
#XMSG: Task log message for displaying the API response
viewResponse="{0}": vastaus vastaanotettu.
#XMSG: Task log message for empty ID in the API response to retrieve the status
emptyJobId=JSON-polun vastauksen rungossa ei palautettu tunnusta: "{0}".
#XMSG: Task log message for empty success value in the API response
emptySuccessValue=JSON-polun vastauksen rungossa ei palautettu onnistumisarvoa: "{0}".
#XMSG: Task log message for empty error value in the API response
emptyErrorValue=JSON-polun vastauksen rungossa ei palautettu virhearvoa: "{0}".
#XMSG: Task log message for none of the success/error conditions matching for the values in the API response
conditionsNotFulfilled=Mitkään määritetyistä onnistumis- tai virhetunnusehdoista eivät täsmää vastauksessa olevien arvojen kanssa.
#XMSG: Task log message for the API response status being unsuccessful
statusNotOk=Kohteen "{1}" vastaus palautti epäonnistuneen tilakoodin: {0}.
#XMSG: Task log message for the API response body being empty
emptyResponseBody=Vastauksen runko oli tyhjä.
#XMSG: Task log message for the API response location header being invalid
emptyLocationHeader=Sijainnin otsikko vastauksessa oli tyhjä.
#XMSG: Task log message for failure in retrieving the status of the invoked API
getStatusFailure=Kutsutun API:n tilaa ei voitu hakea.
#XMSG: Task log message for failure in completing the API task
completionFailure=API-tehtävän "{0}" päättäminen epäonnistui.
#XMSG: Task log message for a successful API task completion
apiCompleted=API-tehtävä "{0}" on päätetty onnistuneesti.
#XMSG: Task log message for an API task with an invalid configuration
invalidConfiguration=API-tehtävän "{0}" konfiguraatio on virheellinen. Tarkista konfiguraatio ja yritä uudelleen.
#XMSG: Task log message for the API task being canceled
cancelStart=On pyydetty peruutusta API-tehtävälle "{0}", jolla on logId {1}.
#XMSG: Task log message for the cancelation of the an API task which is no longer running and cannot be canceled
unableToCancel=API-tehtävä "{0}", jolla on logId {1}, ei ole enää käynnissä, eikä sitä voida peruuttaa.
#XMSG: Task log message when starting an API task. It should carry the name of the API task.
apiRunStart=Käynnistetään API-tehtävää "{0}".
#XMSG: Task log message when the task preparation is taking too long and times out
preparationTimeout=Tehtävän valmistelu kestää liian pitkään ja on aikakatkaistu.
#XMSG: Task log message when a task failed because a cancel task was triggered
apiRunCancelled=Ajotehtävä, jolla on logId {0}, peruutettiin peruutustehtävällä {1}.
#XMSG: Task log message when a cancel task failed to cancel a run task
cancelFailure=Peruutustehtävä, jolla on logId {1}, ei pystynyt peruuttamaan ajotehtävää, jolla on logId {0}.
#XMSG: Task log message when configuration of the API task is too large
apiTaskconfigurationSizeLimitError=API-tehtävää {0} ei voida käynnistää, koska sen tehtäväkonfiguraation koko ylittää suurimman sallitun koon, joka on 100 kibibittiä (KiB).
#XMSG: Task log message for failure in completing the Notification task
notificationTaskCompletionFailure=Ilmoitustehtävän "{0}" päättäminen epäonnistui.
#XMSG: Task log message when starting a Notification task.
notificationTaskRunStart=Käynnistetään ilmoitustehtävää "{0}".
#XMSG: Task log message for a successful Notification task completion
notificationTaskCompletionSuccess=Ilmoitustehtävä "{0}" on päätetty.
#XFLD: Label for frequency column
everyLabel=Aina
#XFLD: Plural Recurrence text for Hour
hoursLabel=Tunnit
#XFLD: Plural Recurrence text for Day
daysLabel=Pv
#XFLD: Plural Recurrence text for Month
monthsLabel=kuukauden välein
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minuutit
