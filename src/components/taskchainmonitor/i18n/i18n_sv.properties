

#XFLD: Task Chain header
headerTxt=Uppgiftskedjor({0})
#XFLD: Task Chain header
headerTxtNew=Uppgiftskedjor ({0})
#XTXT: Text for Schedule label
scheduleTxt=Planera in
#XFLD: Text for Create schedule button
createScheduleTxt=Skapa schema
#XFLD: Text for edit schedule
editScheduleTxt=Redigera schema
#XLFD: Text for delete schedule
deleteScheduleTxt=Radera schema
#XTXT: text for refresh button label
refrestTxt=Uppdatera
#XTXT: Text for Completed status
completedTxt=Slutförd
#XTX: Text for Running status
runningTxt=Körs
#XTX: Text for failed status
failedTxt=Misslyckades
#XTX: Text for stopped status
stoppedTxt=Stoppad
#XTX: Text for stopping status
stoppingTxt=Stoppas
#XFLD: Header for Chain name
chainNameLabel=Namn på uppgiftskedja
#XFLD: Header for Chain name
chainNameLabelBus=Affärsnamn
#XFLD: Header for Chain name
chainNameLabelBusNew=Objekt (affärsnamn)
#XFLD: Header for Chain name
chainNameLabelTech=Tekniskt namn
#XFLD: Header for Chain name
chainNameLabelTechNew=Objekt (tekniskt namn)
#XFLD: Last Run Status label
lastRunStatuslabel=Status för senaste körning
#XFLD: Last Run Status label
lastRunStatuslabelNew=Status
#XFLD: Frequency Label
frequencyLabel=Frekvens
#XFLD: Frequency Label
frequencyLabelNew=Inplanerad frekvens
#XFLD: Duration label
durationLabel=Tidslängd
#XFLD: Duration label
durationLabelNew=Tidslängd för senaste körning
#XFLD: Run Start label
runStartLabel=Start för senaste körning
#XFLD: Run end label
runEndLabel=Slut på senaste körning
#XFLD: Next Run label
nextRunlabel=Nästa körning
#XFLD: Next Run label
nextRunlabelNew=Inplanerad nästa körning
#XTXT: Task chain logs link tooltip text
taskChainLogsTxt=Protokolldetaljer för uppgiftskedja
#XTXT: Task chain logs link tooltip text
taskChainLogsTxtNew=Detaljer
#XTXT: Scheduled text
scheduledTxt=Inplanerad
#XTXT: Paused text
pausedTxt=Pausad
#XTXT: Execute button label
runLabel=Kör
#XTXT: Execute button label
runLabelNew=Starta körning
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Kör uppgiftskedja
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Körning av uppgiftskedja har startat.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Körning av uppgiftskedja har startat för {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Uppgiftskedja kunde inte köras.
#XFLD: Label for schedule owner column
txtScheduleOwner=Schemaägare
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Visar vem som skapade schemat
#XMSG: Task log message for start chain
startChain=Körning av uppgiftskedja startar.
#XMSG: Task log message for load chain from repository
loadChainFromRepository=Uppgiftskedja har lästs in och initialiserats.
#XMSG: Task log message started task
taskStarted=Uppgift {0} har startat.
#XMSG: Task log message for finished task
taskFinished=Uppgift {0} avslutades med status {1}.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Läser in uppgiftskedja och förbereder körning av totalt {0} uppgifter som ingår i denna kedja.
#XMSG: Task log message for starting a subtask
chainStartSubtask=Initierar körning av uppgift {0}. Uppgifts-ID = {1}.
#XMSG: Task log message for finishing a subtask
chainFinishSubtask=Uppgift {0} har avslutats med status {1}.
#XMSG: Task log message for indicating chain success
chainCompleted=Alla {0} uppgifter har slutförts. Status för uppgiftskedjan sätts till slutförd.
#XMSG: Task log message for indicating chain failure
chainFailed=Av totalt {0} uppgifter kunde {1} uppgifter slutföras och {2} misslyckades. Status för uppgiftskedjan sätts till Misslyckades.
#XMSG: Task log message for indicating chain cancelation
chainCanceled=Körning av uppgiftskedja har börjat avbrytas. Protokoll-ID för avbryt uppgift är {0}.
#XMSG: Task log message for indicating chain cancelation in the cancel task
chainCanceling=Avbryter kedja {0}.
#XMSG: Task log message for general chain runtime error
chainError=Ett oväntat fel inträffade vid körning av uppgiftskedjan.
#XMSG: Task log message for starting the overwrite check.
chainOverwriteStart=Kontrollerar om uppgiftskedja med ID {0} har slutförts.
#XMSG: Task log message indicating the chain is too old.
chainOverwriteTooOld=Inga uppgifter att köra hittades för kedja {0}. Körningen är {1} minuter gammal. Status för uppgiftskedjan sätts till Misslyckades.
#XMSG: Task log message indicating the chain was created too recently.
chainOverwriteTooRecent=Inga uppgifter att köra hittades för kedja {0}. Körningen är {1} minuter gammal. Uppgiftskedjan körs fortfarande.
#XMSG: Task log message indicating a recent heartbeat was found.
chainOverwriteHeartbeatAlive=Uppgiftskedja {0} körs fortfarande. Slutförda: {1}, körs: {2}, misslyckade: {3}, initierade: {4}, ej initierade: {5}.
#XMSG: Task log message for a chain that is not running anymore but not everything was triggered.
chainOverwriteNotAllTriggered=Ett fel inträffade vid körning av uppgiftskedja {0} och alla uppgifter kunde inte initieras. Slutförda: {1}, körs: {2}, misslyckades: {3}, initierade: {4}, ej initierade: {5}. Denna uppgiftskedja har skrivits över.
#XMSG: Task log message for a chain that contains a failed subtask.
chainOverwriteAnySubtaskFailed=Uppgiftskedja {0} har avslutats. En uppgift i kedjan misslyckades, så kedjan sätts till misslyckad.
#XMSG: Task log message could not determine status.
chainOverwriteNotDetermined=Ett oväntat fel förhindrar åtkomst till status för uppgiften. Försök igen och kontakta SAP-support om felet kvarstår.
#XMSG: Task log message could not take over
failedTakeover=Befintlig uppgift kunde inte övertas.
#XMSG: Task log parallel check error
parallelCheckError=Uppgift kan inte bearbetas eftersom en annan uppgift körs och redan spärrar denna uppgift.
#XMSG: Task log parallel task runnig error
parallelTaskRunning=Uppgift i konflikt körs redan.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Status {0} vid körning med korrelations-ID {1}.
#XMSG: Task log message successful takeover
successTakeover=Kvarlämnad spärr måste frisläppas. Ny spärr för denna uppgift har ställs in.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Spärr av denna uppgift har övertagits av en annan uppgift.
#XMSG: Schedule created alert message
createScheduleSuccess=Schema har skapats
#XMSG: Task log message for taskFinishedAt
taskFinishedAt=Uppgift {0} avslutades kl. {2} med status {1}.
#XMSG: Placeholder symbol for empty columns
emptyCol=--
#XMSG: Paused Text
txtExpired=Utgången
#XMSG: Update task schedule success Toast message
updateScheduleSuccess=Schema har uppdaterats
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Schema har raderats.
#XMSG: Task chain might have children error text
taskChainNoChildErrorText=Uppgiftskedjan kan ha underordnade som inte visas eftersom uppgiften misslyckades innan planen kunde genereras.
#XMSG: Task chain repair recent failed run label
retryRunLabel=Upprepa senaste körning
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Upprepning av körning av uppgiftskedja har startat.
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Upprepning av körning av uppgiftskedja har misslyckats.
#XMSG: chain repair message
chainRetried=Upprepning av körning av uppgiftskedja har initierats av användare {0}
#XMSG: chain not found during run
chainNotFoundDuringRun=Uppgiftskedja {0} hittades ej. Kontrollera om den finns via Data Builder och säkerställ att kedjan har distribuerats.
#XMSG: chain is not DAG
chainNotDag=Uppgiftskedja {0} kan inte startas. Dess struktur är ogiltig. Kontrollera uppgiftskedjan i Data Builder.
#XMSG: chain has not valid parameters
notValidParameters=Uppgiftskedja {0} kan inte startas. En eller flera av dess parametrar är ogiltig(a). Kontrollera uppgiftskedjan i Data Builder.
#XMSG: Configuration parameter value size exceeds the maximum allowed limit
configurationSizeLimitError=Uppgiftskedja {0} kan inte startas. Storleken på uppgiftens konfiguration i kedjan överskrider maximal tillåten storlek på 100 kibibyte (KiB).
#XMSG: Task {0} has input parameters
taskHasInputParameters=Uppgift ({0}) har inparametrar.
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Uppgift i konflikt körs redan
#XMSG: error message for reading data from backend
txtReadBackendError=Ett fel ser ut att ha inträffat vid läsning från backend.
##XMSG: error message for admission control rejection
admissionControlError=Uppgiften misslyckades på grund av avvisning av åtkomstkontroll för SAP HANA.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Allokera schema till mig
#XBUT: Pause schedule menu label
pauseScheduleLabel=Pausa schema
#XBUT: Resume schedule menu label
resumeScheduleLabel=Återuppta schema
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Ett fel inträffade när scheman skulle tas bort.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Ett fel inträffade när scheman skulle allokeras.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Ett fel inträffade när scheman skulle pausas.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Ett fel inträffade när scheman skulle återupptas.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Raderar {0} scheman
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Ändrar ägare för {0} scheman
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Pausar {0} scheman
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Återupptar {0} scheman
#XBUT: Select Columns Button
selectColumnsBtn=Välj kolumner
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Ett fel ser ut att ha inträffat med att upprepa den senaste körningen eftersom den tidigare uppgiftskörningen misslyckades innan planen kunde genereras.
#XFLD: Refresh tooltip
TEXT_REFRESH=Uppdatera
#XFLD: Select Columns tooltip
text_selectColumns=Välj kolumner

#Messages for BW Process Chain task log of the Data Integration Monitor
#XMSG: Task log message for successfully starting a BW process chain
startBwProcessChainSuccess=BW-processkedjan "{0}" har startats i SAP BW Bridge-tenanten.
#XMSG: Task log message for skipping a BW process chain execution
bwProcessChainSkipped=BW-processkedjan "{0}" hoppades över, antingen på grund av att nya data var otillgängliga eller att föregående utförande misslyckades.
#XMSG: Task log message for failure in starting a BW process chain
startBwProcessChainFailure=BW-processkedjan "{0}" kunde inte startas i SAP BW Bridge-tenanten.
#XMSG: Task log message for failure in getting the status of a BW process chain
getBwProcessChainStatusFailure=Uppgiften misslyckades då vi inte kunde hämta statusen för BW-processkedjan "{0}" på grund av ett problem med att upprätta anslutning till SAP BW Bridge. Öppna appen "Anslutningar" och validera SAP BW Bridge-anslutningen i utrymme "{1}". Kontakta administratören om du saknar åtkomst till appen "Anslutningar".
#XMSG: Task log message for failure in completing a BW process chain
completeBwProcessChainFailure=BW-processkedjan "{0}" kunde inte slutföras i SAP BW Bridge-tenanten.
#XMSG: Task log message for the clickable text of the SAP BW Bridge Monitor link
bwBridgeMonitorLinkText=Visa detaljer i SAP BW Bridge Monitor
#XMSG warning message while retrieving the task log messages if the user does not have access to the SAP BW Bridge space or to the shared object's space
missingPrivilegeOnSpaceError=Ett problem inträffade vid hämtning av meddelandena eller så saknar du rätt behörighet för att se dem.

#Messages for API task log of the Data Integration Monitor
#XMSG: Task log message for displaying the API response
viewResponse=Svar mottaget för "{0}".
#XMSG: Task log message for empty ID in the API response to retrieve the status
emptyJobId=Inget ID har returnerats i texten i svaret för JSON-sökvägen: "{0}".
#XMSG: Task log message for empty success value in the API response
emptySuccessValue=Inget resultatvärde har returnerats i texten i svaret för JSON-sökvägen: "{0}".
#XMSG: Task log message for empty error value in the API response
emptyErrorValue=Inget felvärde har returnerats i texten i svaret för JSON-sökvägen: "{0}".
#XMSG: Task log message for none of the success/error conditions matching for the values in the API response
conditionsNotFulfilled=Inget villkor för resultat- eller felindikator som angetts matchar värdet i svaret.
#XMSG: Task log message for the API response status being unsuccessful
statusNotOk=Svar för "{1}" returnerade statuskod för misslyckades: {0}.
#XMSG: Task log message for the API response body being empty
emptyResponseBody=Texten i svaret var tomt.
#XMSG: Task log message for the API response location header being invalid
emptyLocationHeader=Platshuvudet i svaret var tomt.
#XMSG: Task log message for failure in retrieving the status of the invoked API
getStatusFailure=Status för anropat API kunde inte hämtas.
#XMSG: Task log message for failure in completing the API task
completionFailure=API-uppgift "{0}" har inte slutförts.
#XMSG: Task log message for a successful API task completion
apiCompleted=API-uppgift "{0}" har slutförts.
#XMSG: Task log message for an API task with an invalid configuration
invalidConfiguration=Konfiguration för API-uppgift "{0}" är ogiltig. Kontrollera konfigurationen och försök igen.
#XMSG: Task log message for the API task being canceled
cancelStart=Annullering av API-uppgift "{0}" med protokoll-ID {1} begärd.
#XMSG: Task log message for the cancelation of the an API task which is no longer running and cannot be canceled
unableToCancel=API-uppgift "{0}" med protokoll-ID {1} körs inte längre och kan inte avbrytas.
#XMSG: Task log message when starting an API task. It should carry the name of the API task.
apiRunStart=Startar API-uppgift "{0}".
#XMSG: Task log message when the task preparation is taking too long and times out
preparationTimeout=Förberedelse av uppgift tog för lång tid och timeout inträffade.
#XMSG: Task log message when a task failed because a cancel task was triggered
apiRunCancelled=Körningsuppgift med protokoll-ID {0} har avbrutits av annulleringsuppgift {1}.
#XMSG: Task log message when a cancel task failed to cancel a run task
cancelFailure=Annulleringsuppgift med protokoll-ID {1} misslyckades med att avbryta körningsuppgift med protokoll-ID {0}.
#XMSG: Task log message when configuration of the API task is too large
apiTaskconfigurationSizeLimitError=API-uppgift {0} kan inte startas eftersom storleken på dess uppgiftskonfiguration överskrider maximal tillåten storlek på 100 kibibyte (KiB).
#XMSG: Task log message for failure in completing the Notification task
notificationTaskCompletionFailure=Aviseringsuppgift "{0}" kunde inte slutföras.
#XMSG: Task log message when starting a Notification task.
notificationTaskRunStart=Startar aviseringsuppgift "{0}".
#XMSG: Task log message for a successful Notification task completion
notificationTaskCompletionSuccess=Aviseringsuppgift "{0}" har slutförts.
#XFLD: Label for frequency column
everyLabel=Varje
#XFLD: Plural Recurrence text for Hour
hoursLabel=Timmar
#XFLD: Plural Recurrence text for Day
daysLabel=Dagar
#XFLD: Plural Recurrence text for Month
monthsLabel=Månader
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minuter
