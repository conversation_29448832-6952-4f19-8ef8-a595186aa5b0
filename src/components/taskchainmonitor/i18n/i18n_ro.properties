

#XFLD: Task Chain header
headerTxt=Lan<PERSON><PERSON> de sarcini ({0})
#XFLD: Task Chain header
headerTxtNew=Lan<PERSON><PERSON> de sarcini ({0})
#XTXT: Text for Schedule label
scheduleTxt=Programare
#XFLD: Text for Create schedule button
createScheduleTxt=Creare programare
#XFLD: Text for edit schedule
editScheduleTxt=Editare programare
#XLFD: Text for delete schedule
deleteScheduleTxt=Ștergere programare
#XTXT: text for refresh button label
refrestTxt=Împrospătare
#XTXT: Text for Completed status
completedTxt=Terminat
#XTX: Text for Running status
runningTxt=În execuție
#XTX: Text for failed status
failedTxt=Nereușit
#XTX: Text for stopped status
stoppedTxt=Oprit
#XTX: Text for stopping status
stoppingTxt=În curs de oprire
#XFLD: Header for Chain name
chainNameLabel=Nume lanț de sarcini
#XFLD: Header for Chain name
chainNameLabelBus=Nume comercial
#XFLD: Header for Chain name
chainNameLabelBusNew=Obiect (nume comercial)
#XFLD: Header for Chain name
chainNameLabelTech=Nume tehnic
#XFLD: Header for Chain name
chainNameLabelTechNew=Obiect (nume tehnic)
#XFLD: Last Run Status label
lastRunStatuslabel=Stare ultima execuție
#XFLD: Last Run Status label
lastRunStatuslabelNew=Stare
#XFLD: Frequency Label
frequencyLabel=Frecvență
#XFLD: Frequency Label
frequencyLabelNew=Frecvență programată
#XFLD: Duration label
durationLabel=Durată
#XFLD: Duration label
durationLabelNew=Durată ultima execuție
#XFLD: Run Start label
runStartLabel=Început ultima execuție
#XFLD: Run end label
runEndLabel=Sfârșit ultima execuție
#XFLD: Next Run label
nextRunlabel=Execuția următoare
#XFLD: Next Run label
nextRunlabelNew=Următoarea execuție programată
#XTXT: Task chain logs link tooltip text
taskChainLogsTxt=Detalii jurnal lanț de sarcini
#XTXT: Task chain logs link tooltip text
taskChainLogsTxtNew=Detalii
#XTXT: Scheduled text
scheduledTxt=Programat
#XTXT: Paused text
pausedTxt=Întrerupt
#XTXT: Execute button label
runLabel=Executare
#XTXT: Execute button label
runLabelNew=Lansare execuție
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Executați lanțul de sarcini
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Execuție lanț de sarcini a fost lansată.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Execuție lanț de sarcini a fost lansată pentru {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Eroare la executare lanț de sarcini.
#XFLD: Label for schedule owner column
txtScheduleOwner=Proprietar programare
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Afișează cine a creat programarea
#XMSG: Task log message for start chain
startChain=Lansare în curs execuție lanț de sarcini.
#XMSG: Task log message for load chain from repository
loadChainFromRepository=Lanț de sarcini încărcat și inițializat.
#XMSG: Task log message started task
taskStarted=Sarcina {0} a fost lansată.
#XMSG: Task log message for finished task
taskFinished=Sarcina {0} s-a terminat cu starea {1}.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Încărcare în curs lanț de sarcini și pregătire de execuție un total de {0} sarcini care fac parte din acest lanț.
#XMSG: Task log message for starting a subtask
chainStartSubtask=Declanșare execuție sarcină {0}. ID sarcină = {1}.
#XMSG: Task log message for finishing a subtask
chainFinishSubtask=Sarcina {0} a fost finalizată cu starea {1}.
#XMSG: Task log message for indicating chain success
chainCompleted=Toate cele {0} sarcini sunt terminate. Starea lanțului de sarcini este setată la terminată.
#XMSG: Task log message for indicating chain failure
chainFailed=Dintr-un total de {0} sarcini, {1} sarcini nu au putut fi terminate și {2} sarcini au eșuat. Stare lanț de sarcini este setată la nereușită.
#XMSG: Task log message for indicating chain cancelation
chainCanceled=Anularea execuției lanțului de sarcini a început. ID-ul de jurnal al sarcinii de anulare este {0}.
#XMSG: Task log message for indicating chain cancelation in the cancel task
chainCanceling=Se anulează lanțul {0}.
#XMSG: Task log message for general chain runtime error
chainError=A apărut o eroare neprevăzută la executare lanț de sarcini.
#XMSG: Task log message for starting the overwrite check.
chainOverwriteStart=Verificare în curs dacă lanț de sarcini cu ID {0} a fost finalizat.
#XMSG: Task log message indicating the chain is too old.
chainOverwriteTooOld=Nu a putut fi găsită nicio sarcină pentru executare lanț {0}. Execuția are {1} (de) minute vechime. Stare lanț de sarcini este setată la nereușită.
#XMSG: Task log message indicating the chain was created too recently.
chainOverwriteTooRecent=Nu a putut fi găsită nicio sarcină pentru executare lanț {0}. Execuția are {1} (de) minute vechime. Stare lanț de sarcini este încă în execuție.
#XMSG: Task log message indicating a recent heartbeat was found.
chainOverwriteHeartbeatAlive=Lanț de sarcini {0} este încă în execuție. Terminate: {1}, În execuție: {2}, Nereușite: {3}, Declanșate: {4}, Nedeclanșate: {5}.
#XMSG: Task log message for a chain that is not running anymore but not everything was triggered.
chainOverwriteNotAllTriggered=A apărut o eroare la executare lanț de sarcini {0} și unele sarcini nu au putut fi declanșate. Terminate: {1}, În execuție: {2}, Nereușite: {3}, Declanșate: {4}, Nedeclanșate: {5}. Acest lanț de sarcini a fost suprascris.
#XMSG: Task log message for a chain that contains a failed subtask.
chainOverwriteAnySubtaskFailed=Lanț de sarcini {0} finalizat. O sarcină a lanțului este nereușită; setare în curs lanț la nereușit.
#XMSG: Task log message could not determine status.
chainOverwriteNotDetermined=O eroare neprevăzută ne împiedică să accesăm starea sarcinii. Încercați din nou și contactați suportul SAP dacă eroarea persistă.
#XMSG: Task log message could not take over
failedTakeover=Eroare la preluare sarcină existentă.
#XMSG: Task log parallel check error
parallelCheckError=Sarcina nu poate fi prelucrată deoarece o altă sarcină este în execuție și deja blochează această sarcină.
#XMSG: Task log parallel task runnig error
parallelTaskRunning=O sarcină în conflict este deja în execuție.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Starea {0} la execuție cu ID corelare {1}.
#XMSG: Task log message successful takeover
successTakeover=Blocarea rămasă a trebuit să fie eliberată. Blocarea nouă pentru această sarcină este setată.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Blocarea acestei sarcini a fost preluată de o altă sarcină.
#XMSG: Schedule created alert message
createScheduleSuccess=Programare creată
#XMSG: Task log message for taskFinishedAt
taskFinishedAt=Sarcina {0} a fost finalizată la {2} cu starea {1}.
#XMSG: Placeholder symbol for empty columns
emptyCol=--
#XMSG: Paused Text
txtExpired=Expirat
#XMSG: Update task schedule success Toast message
updateScheduleSuccess=Programare actualizată
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Programare ștearsă.
#XMSG: Task chain might have children error text
taskChainNoChildErrorText=Lanțul de sarcini poate avea elemente subordonate care nu sunt afișate deoarece sarcina a eșuat înainte ca planul să poată fi generat.
#XMSG: Task chain repair recent failed run label
retryRunLabel=Reîncercare ultima execuție
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Reîncercare lanț de sarcini a fost lansată
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Reîncercare lanț de sarcini a eșuat
#XMSG: chain repair message
chainRetried=Reîncercare lanț de sarcini declanșată de utilizatorul {0}
#XMSG: chain not found during run
chainNotFoundDuringRun=Nu am putut găsi lanțul de sarcini {0}. Verificați dacă există prin generatorul de date și asigurați-vă că lanțul este implementat.
#XMSG: chain is not DAG
chainNotDag=Imposibil de lansat lanțul de sarcini {0}. Structura acestuia nu este valabilă. Verificați lanțul de sarcini în generatorul de date.
#XMSG: chain has not valid parameters
notValidParameters=Imposibil de lansat lanțul de sarcini {0}. Cel puțin unul dintre parametrii săi este nevalabil. Verificați lanțul de sarcini în generatorul de date.
#XMSG: Configuration parameter value size exceeds the maximum allowed limit
configurationSizeLimitError=Imposibil de lansat lanțul de sarcini {0}. Mărimea unei configurări de sarcină în lanț depășește mărimea maximă permisă de 100 kibibytes (KiB).
#XMSG: Task {0} has input parameters
taskHasInputParameters=Sarcina {0} are parametri de intrare.
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=O sarcină în conflict este deja în execuție
#XMSG: error message for reading data from backend
txtReadBackendError=Se pare că a apărut o eroare la citire din backend.
##XMSG: error message for admission control rejection
admissionControlError=Sarcina a eșuat din cauza unei respingeri a controlului de admitere SAP HANA.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Alocare programare la mine
#XBUT: Pause schedule menu label
pauseScheduleLabel=Suspendare programare
#XBUT: Resume schedule menu label
resumeScheduleLabel=Reluare programare
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=A apărut o eroare la eliminarea programărilor.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=A apărut o eroare la alocarea programărilor.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=A apărut o eroare la suspendarea programărilor.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=A apărut o eroare la reluarea programărilor.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Ștergeți {0} programări
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Modificați proprietarul pentru {0} programări
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Se suspendă {0} programări
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Se reiau {0} programări
#XBUT: Select Columns Button
selectColumnsBtn=Selectare coloane
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Se pare că a apărut o eroare la reîncercarea ultimei execuții, deoarece execuția sarcinii anterioare a eșuat înainte ca planul să fi putut fi generat.
#XFLD: Refresh tooltip
TEXT_REFRESH=Împrospătare
#XFLD: Select Columns tooltip
text_selectColumns=Selectare coloane

#Messages for BW Process Chain task log of the Data Integration Monitor
#XMSG: Task log message for successfully starting a BW process chain
startBwProcessChainSuccess=Lanțul de proces BW "{0}" a fost lansat cu succes în tenantul punții SAP BW.
#XMSG: Task log message for skipping a BW process chain execution
bwProcessChainSkipped=Lanțul de proces BW "{0}" a fost omis din cauza indisponibilității datelor noi sau eșecul unei execuții anterioare.
#XMSG: Task log message for failure in starting a BW process chain
startBwProcessChainFailure=Lanțul de proces BW "{0}" nu a fost lansat cu succes în tenantul punții SAP BW.
#XMSG: Task log message for failure in getting the status of a BW process chain
getBwProcessChainStatusFailure=Sarcina a eșuat deoarece nu am putut regăsi starea lanțului de proces BW "{0}" din cauza unei probleme în stabilirea unei conexiuni la SAP BW Bridge. Deschideți aplicația "Conexiuni" și validați conexiunea SAP BW Bridge în spațiul "{1}". Dacă nu aveți acces la aplicația "Conexiuni", contactați administratorul dvs.
#XMSG: Task log message for failure in completing a BW process chain
completeBwProcessChainFailure=Lanțul de proces BW "{0}" nu a fost terminat cu succes în tenantul punții SAP BW.
#XMSG: Task log message for the clickable text of the SAP BW Bridge Monitor link
bwBridgeMonitorLinkText=Vizualizare detalii în monitor punte SAP BW
#XMSG warning message while retrieving the task log messages if the user does not have access to the SAP BW Bridge space or to the shared object's space
missingPrivilegeOnSpaceError=A apărut o eroare la regăsirea mesajelor sau nu aveți permisiunea necesară pentru a le vizualiza.

#Messages for API task log of the Data Integration Monitor
#XMSG: Task log message for displaying the API response
viewResponse=Răspuns primit pentru "{0}".
#XMSG: Task log message for empty ID in the API response to retrieve the status
emptyJobId=Nu a fost returnat niciun ID în corpul de răspuns pentru calea JSON: "{0}".
#XMSG: Task log message for empty success value in the API response
emptySuccessValue=Nu a fost returnată nicio valoare de succes în corpul de răspuns pentru calea JSON: "{0}".
#XMSG: Task log message for empty error value in the API response
emptyErrorValue=Nu a fost returnată nicio valoare de eroare în corpul de răspuns pentru calea JSON: "{0}".
#XMSG: Task log message for none of the success/error conditions matching for the values in the API response
conditionsNotFulfilled=Niciuna din condițiile de indicator de succes sau de eroare specificate nu corespunde cu valorile din răspuns.
#XMSG: Task log message for the API response status being unsuccessful
statusNotOk=Răspunsul pentru „{1}” a returnat un cod de stare nereușit: {0}.
#XMSG: Task log message for the API response body being empty
emptyResponseBody=Corpul răspunsului a fost gol.
#XMSG: Task log message for the API response location header being invalid
emptyLocationHeader=Antetul de locație din răspuns a fost gol.
#XMSG: Task log message for failure in retrieving the status of the invoked API
getStatusFailure=Starea API-ului invocat nu a putut fi regăsită.
#XMSG: Task log message for failure in completing the API task
completionFailure=Sarcina API "{0}" nu a fost terminată cu succes.
#XMSG: Task log message for a successful API task completion
apiCompleted=Sarcina API „{0}” a fost terminată cu succes.
#XMSG: Task log message for an API task with an invalid configuration
invalidConfiguration=Configurarea sarcinii API „{0}” este nevalabilă. Verificați configurarea și încercați din nou.
#XMSG: Task log message for the API task being canceled
cancelStart=Anulare solicitată a sarcinii API „{0}” cu logId {1}.
#XMSG: Task log message for the cancelation of the an API task which is no longer running and cannot be canceled
unableToCancel=Sarcina API „{0}” cu logId {1} nu mai este în execuție și nu poate fi anulată.
#XMSG: Task log message when starting an API task. It should carry the name of the API task.
apiRunStart=Se lansează sarcina API „{0}”.
#XMSG: Task log message when the task preparation is taking too long and times out
preparationTimeout=Pregătirea sarcinii durează prea mult și a expirat.
#XMSG: Task log message when a task failed because a cancel task was triggered
apiRunCancelled=Sarcina de execuție cu logId {0} a fost anulată de sarcina de anulare {1}.
#XMSG: Task log message when a cancel task failed to cancel a run task
cancelFailure=Sarcina de anulare cu logId {1} nu a putut anula sarcina de execuție cu logId {0}.
#XMSG: Task log message when configuration of the API task is too large
apiTaskconfigurationSizeLimitError=Sarcina API {0} nu poate fi lansată deoarece mărimea configurării sale de sarcină depășește mărimea maximă permisă de 100 kibibytes (KiB).
#XMSG: Task log message for failure in completing the Notification task
notificationTaskCompletionFailure=Sarcina de notificare „{0}” nu a fost terminată.
#XMSG: Task log message when starting a Notification task.
notificationTaskRunStart=Se lansează sarcina de notificare „{0}”.
#XMSG: Task log message for a successful Notification task completion
notificationTaskCompletionSuccess=Sarcina de notificare „{0}” este terminată.
#XFLD: Label for frequency column
everyLabel=La fiecare
#XFLD: Plural Recurrence text for Hour
hoursLabel=Ore
#XFLD: Plural Recurrence text for Day
daysLabel=Zile
#XFLD: Plural Recurrence text for Month
monthsLabel=Luni
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minute
