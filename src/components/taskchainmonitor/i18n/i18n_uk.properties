

#XFLD: Task Chain header
headerTxt=Ланцюжки завдань({0})
#XFLD: Task Chain header
headerTxtNew=Ланцюжки завдань ({0})
#XTXT: Text for Schedule label
scheduleTxt=Запланувати
#XFLD: Text for Create schedule button
createScheduleTxt=Створити розклад
#XFLD: Text for edit schedule
editScheduleTxt=Змінити розклад
#XLFD: Text for delete schedule
deleteScheduleTxt=Видалити розклад
#XTXT: text for refresh button label
refrestTxt=Оновити
#XTXT: Text for Completed status
completedTxt=Завершено
#XTX: Text for Running status
runningTxt=Виконується
#XTX: Text for failed status
failedTxt=Помилка
#XTX: Text for stopped status
stoppedTxt=Зупинено
#XTX: Text for stopping status
stoppingTxt=Зупиняється
#XFLD: Header for Chain name
chainNameLabel=Ім’я ланцюжка завдань
#XFLD: Header for Chain name
chainNameLabelBus=Бізнес-ім'я
#XFLD: Header for Chain name
chainNameLabelBusNew=Об'єкт (бізнес-ім'я)
#XFLD: Header for Chain name
chainNameLabelTech=Технічне ім’я
#XFLD: Header for Chain name
chainNameLabelTechNew=Об'єкт (технічне ім'я)
#XFLD: Last Run Status label
lastRunStatuslabel=Стан останнього прогону
#XFLD: Last Run Status label
lastRunStatuslabelNew=Статус
#XFLD: Frequency Label
frequencyLabel=Частота
#XFLD: Frequency Label
frequencyLabelNew=Запланована частота
#XFLD: Duration label
durationLabel=Тривалість
#XFLD: Duration label
durationLabelNew=Тривалість останнього прогону
#XFLD: Run Start label
runStartLabel=Початок останнього прогону
#XFLD: Run end label
runEndLabel=Завершення останнього прогону
#XFLD: Next Run label
nextRunlabel=Наступний запуск
#XFLD: Next Run label
nextRunlabelNew=Наступний прогін за розкладом
#XTXT: Task chain logs link tooltip text
taskChainLogsTxt=Деталі журналу ланцюжка завдань
#XTXT: Task chain logs link tooltip text
taskChainLogsTxtNew=Подробиці
#XTXT: Scheduled text
scheduledTxt=Заплановано
#XTXT: Paused text
pausedTxt=Призупинено
#XTXT: Execute button label
runLabel=Прогін
#XTXT: Execute button label
runLabelNew=Почати прогін
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Прогін ланцюжка завдань
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Прогін ланцюжка завдань розпочато.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Прогін ланцюжка завдань розпочато для {0}.
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Не вдалося прогнати ланцюжок завдань.
#XFLD: Label for schedule owner column
txtScheduleOwner=Власник розкладу
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Показує, хто створив розклад
#XMSG: Task log message for start chain
startChain=Запуск ланцюжка завдань.
#XMSG: Task log message for load chain from repository
loadChainFromRepository=Ланцюжок завдань завантажено та ініціалізовано.
#XMSG: Task log message started task
taskStarted=Завдання {0} розпочато.
#XMSG: Task log message for finished task
taskFinished=Завдання {0} завершено зі статусом {1}.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Завантажте ланцюжок завдань і підготовку до прогону завдань ({0}), які є частиною цього ланцюжка.
#XMSG: Task log message for starting a subtask
chainStartSubtask=Запуск завдання {0} для прогону. Ідентифікатор завдання = {1}.
#XMSG: Task log message for finishing a subtask
chainFinishSubtask=Завдання {0} завершено зі статусом {1}.
#XMSG: Task log message for indicating chain success
chainCompleted=Усі завдання ({0}) виконано. Статус ланцюжка завдань змінено на "завершено".
#XMSG: Task log message for indicating chain failure
chainFailed=З {0} завдань вдалося виконати стільки завдань: {1}. Не вдалося виконати завдань: {2}. Для статусу ланцюжка завдань встановлено значення "не вдалося".
#XMSG: Task log message for indicating chain cancelation
chainCanceled=Розпочато скасування прогону ланцюжка завдань. Ідентифікатор журналу завдання скасування:{0}.
#XMSG: Task log message for indicating chain cancelation in the cancel task
chainCanceling=Скасування ланцюжка "{0}".
#XMSG: Task log message for general chain runtime error
chainError=Під час прогону ланцюжка завдань сталася неочікувана помилка.
#XMSG: Task log message for starting the overwrite check.
chainOverwriteStart=Перевірте, чи завершився ланцюжок завдань з ідентифікатором {0}.
#XMSG: Task log message indicating the chain is too old.
chainOverwriteTooOld=Не вдалося знайти жодних завдань для прогону ланцюжка {0}. Прогін почався {1} хв. тому. Для статусу ланцюжка завдань установлено значення "не вдалося".
#XMSG: Task log message indicating the chain was created too recently.
chainOverwriteTooRecent=Не вдалося знайти жодних завдань для прогону ланцюжка {0}. Прогін почався {1} хв. тому. Ланцюжок завдань ще триває.
#XMSG: Task log message indicating a recent heartbeat was found.
chainOverwriteHeartbeatAlive=Ланцюжок завдань {0} все ще триває. Завершено: {1}, триває: {2}, не вдалося: {3}, запущено: {4}, не запущено: {5}.
#XMSG: Task log message for a chain that is not running anymore but not everything was triggered.
chainOverwriteNotAllTriggered=Під час прогону ланцюжка завдань {0} сталася помилка, і не всі завдання вдалося запустити. Завершено: {1}, триває: {2}, не вдалося: {3}, запущено: {4}, не запущено: {5}. Цей ланцюжок завдань було перезаписано.
#XMSG: Task log message for a chain that contains a failed subtask.
chainOverwriteAnySubtaskFailed=Ланцюжок завдань {0} завершено. Одне завдання ланцюжка не виконано, для ланцюжка встановлено статус "не вдалося".
#XMSG: Task log message could not determine status.
chainOverwriteNotDetermined=Несподівана помилка заважає отримати доступ до статусу завдання. Повторіть спробу і зверніться до служби підтримки SAP, якщо помилка не зникне.
#XMSG: Task log message could not take over
failedTakeover=Не вдалося перенести існуюче завдання.
#XMSG: Task log parallel check error
parallelCheckError=Не вдається обробити завдання, оскільки виконується інше завдання й вже блокує це завдання.
#XMSG: Task log parallel task runnig error
parallelTaskRunning=Конфліктне завдання вже виконується.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Статус {0} під час прогону з ідентифікатором кореляції {1}.
#XMSG: Task log message successful takeover
successTakeover=Залишене блокування слід звільнити. Нове блокування для цього завдання встановлено.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Блокування цього завдання перейшло на інше завдання.
#XMSG: Schedule created alert message
createScheduleSuccess=Розклад створено
#XMSG: Task log message for taskFinishedAt
taskFinishedAt=Завдання {0} завершено о {2} зі статусом {1}.
#XMSG: Placeholder symbol for empty columns
emptyCol=--
#XMSG: Paused Text
txtExpired=Прострочено
#XMSG: Update task schedule success Toast message
updateScheduleSuccess=Розклад оновлено
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Розклад видалено.
#XMSG: Task chain might have children error text
taskChainNoChildErrorText=Ланцюжок завдань може мати дочірні елементи, які не відображаються, оскільки завдання не вдалося виконати до того, як можна було створити план.
#XMSG: Task chain repair recent failed run label
retryRunLabel=Повторити останній прогін
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Повторний прогін ланцюжка завдань розпочато
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Не вдалося повторити прогін ланцюжка завдань
#XMSG: chain repair message
chainRetried=Повторний прогін ланцюжка завдань активовано користувачем {0}
#XMSG: chain not found during run
chainNotFoundDuringRun=Не вдалося знайти ланцюжок завдань "{0}". За допомогою Конструктора моделей даних перевірте, чи він існує, і переконайтеся, що ланцюжок розгорнуто.
#XMSG: chain is not DAG
chainNotDag=Не вдалося запустити ланцюжок завдань "{0}". Його структура недійсна. Перевірте ланцюжок завдань у Конструкторі моделей даних.
#XMSG: chain has not valid parameters
notValidParameters=Не вдалося запустити ланцюжок завдань "{0}". Один або кілька його параметрів недійсні. Перевірте ланцюжок завдань у Конструкторі моделей даних.
#XMSG: Configuration parameter value size exceeds the maximum allowed limit
configurationSizeLimitError=Не вдалося запустити ланцюжок завдань "{0}". Розмір конфігурації завдання в ланцюжку перевищує максимально дозволений розмір 100 кібібайт (КіБ).
#XMSG: Task {0} has input parameters
taskHasInputParameters=Завдання "{0}" має параметри введення.
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Уже виконується конфліктне завдання
#XMSG: error message for reading data from backend
txtReadBackendError=Схоже, під час читання з бекенду сталася помилка.
##XMSG: error message for admission control rejection
admissionControlError=Не вдалося виконати завдання через відхилення контролю допуску SAP HANA.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Призначити мені розклад
#XBUT: Pause schedule menu label
pauseScheduleLabel=Призупинити розклад
#XBUT: Resume schedule menu label
resumeScheduleLabel=Відновити розклад
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Під час вилучення розкладів сталася помилка.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Під час призначення розкладів сталася помилка.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Під час призупинення розкладів сталася помилка.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Під час відновлення розкладів сталася помилка.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Видалення розкладів ({0})
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Змінення власника розкладів ({0})
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Призупинення розкладів ({0})
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Відновлення розкладів ({0})
#XBUT: Select Columns Button
selectColumnsBtn=Вибрати стовпчики
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Схоже, сталася помилка під час спроби повторити останній прогін, оскільки попередній прогін завдання завершився помилкою до того, як вдалося згенерувати план.
#XFLD: Refresh tooltip
TEXT_REFRESH=Оновити
#XFLD: Select Columns tooltip
text_selectColumns=Вибрати стовпчики

#Messages for BW Process Chain task log of the Data Integration Monitor
#XMSG: Task log message for successfully starting a BW process chain
startBwProcessChainSuccess=Ланцюг процесів BW "{0}" успішно розпочато в орендаторі мосту SAP BW.
#XMSG: Task log message for skipping a BW process chain execution
bwProcessChainSkipped=Ланцюг процесів BW "{0}" пропущено через недоступність нових даних або помилку попереднього виконання.
#XMSG: Task log message for failure in starting a BW process chain
startBwProcessChainFailure=Не вдалося розпочати ланцюг процесів BW "{0}" в орендаторі мосту SAP BW.
#XMSG: Task log message for failure in getting the status of a BW process chain
getBwProcessChainStatusFailure=Завдання завершилося помилкою, оскільки не вдалося отримати статус ланцюга процесу BW "{0}" через проблему з установленням з''єднання з мостом SAP BW. Відкрийте застосунок "З''єднання" і перевірте з''єднання з мостом SAP BW в просторі "{1}". Якщо застосунок "З''єднання" недоступний, зверніться до свого адміністратора.
#XMSG: Task log message for failure in completing a BW process chain
completeBwProcessChainFailure=Не вдалося завершити ланцюг процесів BW "{0}" в орендаторі мосту SAP BW.
#XMSG: Task log message for the clickable text of the SAP BW Bridge Monitor link
bwBridgeMonitorLinkText=Переглянути подробиці в моніторі мосту SAP BW
#XMSG warning message while retrieving the task log messages if the user does not have access to the SAP BW Bridge space or to the shared object's space
missingPrivilegeOnSpaceError=Виникла проблема з отриманням повідомлень, або у вас немає необхідного дозволу для їх перегляду.

#Messages for API task log of the Data Integration Monitor
#XMSG: Task log message for displaying the API response
viewResponse=Отримано відповідь для "{0}".
#XMSG: Task log message for empty ID in the API response to retrieve the status
emptyJobId=У тілі відповіді для шляху JSON не повернуто ідентифікатор: "{0}".
#XMSG: Task log message for empty success value in the API response
emptySuccessValue=У тілі відповіді для шляху JSON не повернуто значення успіху: "{0}".
#XMSG: Task log message for empty error value in the API response
emptyErrorValue=У тілі відповіді для шляху JSON не повернуто значення помилки: "{0}".
#XMSG: Task log message for none of the success/error conditions matching for the values in the API response
conditionsNotFulfilled=Жодна з указаних умов індикатора успіху або помилки не відповідає значенням у відповіді.
#XMSG: Task log message for the API response status being unsuccessful
statusNotOk=У відповіді для "{1}" повернуто код статусу "Не вдалося": "{0}".
#XMSG: Task log message for the API response body being empty
emptyResponseBody=Тіло відповіді було порожнім.
#XMSG: Task log message for the API response location header being invalid
emptyLocationHeader=Заголовок розташування у відповіді був порожнім.
#XMSG: Task log message for failure in retrieving the status of the invoked API
getStatusFailure=Не вдалося отримати статус викликаного інтерфейсу API.
#XMSG: Task log message for failure in completing the API task
completionFailure=Не вдалося завершити завдання API "{0}".
#XMSG: Task log message for a successful API task completion
apiCompleted=Завдання API "{0}" успішно завершено.
#XMSG: Task log message for an API task with an invalid configuration
invalidConfiguration=Конфігурація для завдання API "{0}" недійсна. Перевірте конфігурацію і спробуйте ще раз.
#XMSG: Task log message for the API task being canceled
cancelStart=Запитано скасування завдання API "{0}" з ідентифікатором журналу {1}.
#XMSG: Task log message for the cancelation of the an API task which is no longer running and cannot be canceled
unableToCancel=Завдання API "{0}" з ідентифікатором журналу {1} більше не виконується, тож його не можна скасувати.
#XMSG: Task log message when starting an API task. It should carry the name of the API task.
apiRunStart=Початок завдання API "{0}".
#XMSG: Task log message when the task preparation is taking too long and times out
preparationTimeout=Підготовка завдання тривала так довго, що для нього минув час очікування.
#XMSG: Task log message when a task failed because a cancel task was triggered
apiRunCancelled=Завдання прогону з ідентифікатором журналу {0} скасовано завданням скасування "{1}".
#XMSG: Task log message when a cancel task failed to cancel a run task
cancelFailure=Завданню скасування з ідентифікатором журналу {1} не вдалося скасувати завдання прогону з ідентифікатором журналу {0}.
#XMSG: Task log message when configuration of the API task is too large
apiTaskconfigurationSizeLimitError=Не вдалося запустити завдання API "{0}", оскільки розмір конфігурації завдання перевищує максимально дозволений розмір 100 кібібайт (КіБ).
#XMSG: Task log message for failure in completing the Notification task
notificationTaskCompletionFailure=Не вдалося завершити завдання сповіщення "{0}".
#XMSG: Task log message when starting a Notification task.
notificationTaskRunStart=Запуск завдання сповіщення "{0}".
#XMSG: Task log message for a successful Notification task completion
notificationTaskCompletionSuccess=Завдання сповіщення "{0}" завершено.
#XFLD: Label for frequency column
everyLabel=Кожні
#XFLD: Plural Recurrence text for Hour
hoursLabel=Години
#XFLD: Plural Recurrence text for Day
daysLabel=Дні
#XFLD: Plural Recurrence text for Month
monthsLabel=Місяці
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Хвилини
