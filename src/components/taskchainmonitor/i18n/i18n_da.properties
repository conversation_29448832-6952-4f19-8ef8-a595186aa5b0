

#XFLD: Task Chain header
headerTxt=Opgavekæder({0})
#XFLD: Task Chain header
headerTxtNew=Opgavekæder ({0})
#XTXT: Text for Schedule label
scheduleTxt=Tidsplan
#XFLD: Text for Create schedule button
createScheduleTxt=Opret tidsplan
#XFLD: Text for edit schedule
editScheduleTxt=Rediger tidsplan
#XLFD: Text for delete schedule
deleteScheduleTxt=Slet tidsplan
#XTXT: text for refresh button label
refrestTxt=Opdater
#XTXT: Text for Completed status
completedTxt=Fuldført
#XTX: Text for Running status
runningTxt=Kører
#XTX: Text for failed status
failedTxt=Mislykkedes
#XTX: Text for stopped status
stoppedTxt=Stoppet
#XTX: Text for stopping status
stoppingTxt=Stopper
#XFLD: Header for Chain name
chainNameLabel=Navn på opgavekæde
#XFLD: Header for Chain name
chainNameLabelBus=Forretningsnavn
#XFLD: Header for Chain name
chainNameLabelBusNew=Objekt (virksomhedsnavn)
#XFLD: Header for Chain name
chainNameLabelTech=Teknisk navn
#XFLD: Header for Chain name
chainNameLabelTechNew=Objekt (teknisk navn)
#XFLD: Last Run Status label
lastRunStatuslabel=Seneste kørselsstatus
#XFLD: Last Run Status label
lastRunStatuslabelNew=Status
#XFLD: Frequency Label
frequencyLabel=Frekvens
#XFLD: Frequency Label
frequencyLabelNew=Planlagt frekvens
#XFLD: Duration label
durationLabel=Varighed
#XFLD: Duration label
durationLabelNew=Varighed af seneste kørsel
#XFLD: Run Start label
runStartLabel=Start for seneste kørsel
#XFLD: Run end label
runEndLabel=Slut for seneste kørsel
#XFLD: Next Run label
nextRunlabel=Næste kørsel
#XFLD: Next Run label
nextRunlabelNew=Planlagt næste kørsel
#XTXT: Task chain logs link tooltip text
taskChainLogsTxt=Opgavekædes logdetaljer
#XTXT: Task chain logs link tooltip text
taskChainLogsTxtNew=Detaljer
#XTXT: Scheduled text
scheduledTxt=Planlagt
#XTXT: Paused text
pausedTxt=Sat på pause
#XTXT: Execute button label
runLabel=Kør
#XTXT: Execute button label
runLabelNew=Start kørsel
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Kør opgavekæden
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Kørslen af opgavekæden er startet.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Kørslen af opgavekæden er startet for {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Kørsel af opgavekæden mislykkedes.
#XFLD: Label for schedule owner column
txtScheduleOwner=Ejer af tidsplan
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Viser, hvem der har oprettet tidsplanen
#XMSG: Task log message for start chain
startChain=Starter kørsel af opgavekæde.
#XMSG: Task log message for load chain from repository
loadChainFromRepository=Opgavekæde er indlæst og initialiseret.
#XMSG: Task log message started task
taskStarted=Opgaven {0} er startet.
#XMSG: Task log message for finished task
taskFinished=Opgaven {0} sluttede med status {1}.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Indlæsning af opgavekæde og forberedelse til kørsel af i alt {0} opgaver, der er del af denne kæde.
#XMSG: Task log message for starting a subtask
chainStartSubtask=Udløser opgave {0} til kørsel. Opgave-id = {1}.
#XMSG: Task log message for finishing a subtask
chainFinishSubtask=Opgave {0} afsluttet med status {1}.
#XMSG: Task log message for indicating chain success
chainCompleted=Alle {0}-opgaver er afsluttet. Status for opgavekæden er angivet til afsluttet.
#XMSG: Task log message for indicating chain failure
chainFailed=Ud af et samlet antal på {0}-opgaver kunne {1} opgaver afsluttes, og {2} opgaver kunne ikke gennemføres. Status for opgavekæden er angivet til mislykket.
#XMSG: Task log message for indicating chain cancelation
chainCanceled=Annullering af opgavekædekørsel er startet. Log-id for annulleringsopgave er {0}.
#XMSG: Task log message for indicating chain cancelation in the cancel task
chainCanceling=Annullerer kæde {0}.
#XMSG: Task log message for general chain runtime error
chainError=Der opstod en uventet fejl under kørsel af opgavekæden.
#XMSG: Task log message for starting the overwrite check.
chainOverwriteStart=Kontrollerer, om opgavekæde med id {0} er afsluttet.
#XMSG: Task log message indicating the chain is too old.
chainOverwriteTooOld=Der kunne ikke findes opgaver, der skulle køres for kæden {0}. Kørslen er {1} minutter gammel. Status for opgavekæde er angivet til mislykket.
#XMSG: Task log message indicating the chain was created too recently.
chainOverwriteTooRecent=Der kunne ikke findes opgaver, der skulle køres for kæden {0}. Kørslen er {1} minutter gammel. Opgavekæden kører stadig.
#XMSG: Task log message indicating a recent heartbeat was found.
chainOverwriteHeartbeatAlive=Opgavekæden {0} kører stadig. Afsluttet: {1}, Kører: {2}, Mislykket: {3}, Udløst: {4}, Ikke udløst: {5}.
#XMSG: Task log message for a chain that is not running anymore but not everything was triggered.
chainOverwriteNotAllTriggered=Der opstod en fejl under kørsel af opgavekæde {0},og ikke alle opgaver kunne udløses. Afsluttet: {1}, Kører: {2}, Mislykket: {3}, Udløst: {4}, Ikke udløst: {5}. Denne opgavekæde er overskrevet.
#XMSG: Task log message for a chain that contains a failed subtask.
chainOverwriteAnySubtaskFailed=Opgavekæde {0} er afsluttet. En opgave i kæden kunne ikke udføres, angiver kæden til mislykket.
#XMSG: Task log message could not determine status.
chainOverwriteNotDetermined=En uventet fejl forhindrer os i at få adgang til opgavens status. Prøv igen, og kontakt SAP-support, hvis fejlen fortsat opstår.
#XMSG: Task log message could not take over
failedTakeover=Kunne ikke overtage eksisterende opgave.
#XMSG: Task log parallel check error
parallelCheckError=Opgaven kan ikke behandles, fordi en anden opgave kører og allerede spærrer for opgaven.
#XMSG: Task log parallel task runnig error
parallelTaskRunning=En opgave med konflikt kører allerede.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Status {0} under kørsel med korrelations-id {1}.
#XMSG: Task log message successful takeover
successTakeover=Den resterende spærre måtte frigives. Den nye spærre for denne opgave er angivet.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Spærring af denne opgave blev overtaget af en anden opgave.
#XMSG: Schedule created alert message
createScheduleSuccess=Tidsplan oprettet
#XMSG: Task log message for taskFinishedAt
taskFinishedAt=Opgave {0} afsluttet kl. {2} med status {1}.
#XMSG: Placeholder symbol for empty columns
emptyCol=--
#XMSG: Paused Text
txtExpired=Udløbet
#XMSG: Update task schedule success Toast message
updateScheduleSuccess=Tidsplan opdateret
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Tidsplan slettet.
#XMSG: Task chain might have children error text
taskChainNoChildErrorText=Opgavekæden kan have underordnede elementer, der ikke vises, fordi opgaven mislykkedes, før planen kunne genereres.
#XMSG: Task chain repair recent failed run label
retryRunLabel=Forsøg seneste kørsel igen
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Opgavekæden med forsøg kørsel er startet
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Opgavekæden med forsøg kørsel er mislykket
#XMSG: chain repair message
chainRetried=Opgavekæden med forsøg igen udløst af bruger {0}
#XMSG: chain not found during run
chainNotFoundDuringRun=Kunne ikke finde opgavekæde {0}. Kontroller, om den findes via datageneratoren, og sørg for, at kæden er implementeret.
#XMSG: chain is not DAG
chainNotDag=Kan ikke starte opgavekæde {0}. Dens struktur er ugyldig. Kontroller opgavekæden i datageneratoren.
#XMSG: chain has not valid parameters
notValidParameters=Kan ikke starte opgavekæde {0}. En eller flere af dens parametre er ugyldige. Kontroller opgavekæden i datageneratoren.
#XMSG: Configuration parameter value size exceeds the maximum allowed limit
configurationSizeLimitError=Kan ikke starte opgavekæde {0}. Størrelsen af en opgaves konfiguration i kæden overstiger den tilladte størrelse på 100 kibibytes (KiB).
#XMSG: Task {0} has input parameters
taskHasInputParameters=Opgave {0} har inputparametre.
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=En modstridende opgave kører allerede
#XMSG: error message for reading data from backend
txtReadBackendError=Det ser ud til, at der opstod en fejl under læsning fra backend.
##XMSG: error message for admission control rejection
admissionControlError=Opgaven blev ikke udført pga. en afvisning af SAP HANA-adgangskontrol.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Tildel tidsplan til mig
#XBUT: Pause schedule menu label
pauseScheduleLabel=Sæt tidsplan på pause
#XBUT: Resume schedule menu label
resumeScheduleLabel=Genoptag tidsplan
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Der opstod en fejl ved fjernelse af tidsplaner.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Der opstod en fejl ved tildeling af tidsplaner.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Der opstod en fejl, da tidsplaner blev sat på pause.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Der opstod en fejl ved genoptagelse af tidsplaner.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Sletter {0} tidsplaner
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Ændrer ejer af {0} tidsplaner
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Sætter {0} tidsplaner på pause
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Genoptager {0} tidsplaner
#XBUT: Select Columns Button
selectColumnsBtn=Vælg kolonner
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Det ser ud til, at der var en fejl med at forsøge den seneste kørsel igen, da den tidligere opgavekørsel mislykkedes, før planen kunne genereres.
#XFLD: Refresh tooltip
TEXT_REFRESH=Opdater
#XFLD: Select Columns tooltip
text_selectColumns=Vælg kolonner

#Messages for BW Process Chain task log of the Data Integration Monitor
#XMSG: Task log message for successfully starting a BW process chain
startBwProcessChainSuccess=BW-proceskæden "{0}" er startet i SAP BW Bridge-tenanten.
#XMSG: Task log message for skipping a BW process chain execution
bwProcessChainSkipped=BW-proceskæden "{0}" blev sprunget over på grund af utilgængeligheden af nye data eller en mislykket tidligere udførelse.
#XMSG: Task log message for failure in starting a BW process chain
startBwProcessChainFailure=BW-proceskæden "{0}" kunne ikke startes i SAP BW Bridge-tenanten.
#XMSG: Task log message for failure in getting the status of a BW process chain
getBwProcessChainStatusFailure=Opgaven kunne ikke udføres, da vi ikke kunne hente status for BW-proceskæden "{0}" på grund af et problem med at etablere en forbindelse til SAP BW Bridge. Åbn appen "Forbindelser", og valider SAP BW Bridge-forbindelsen i spacet "{1}". Hvis du ikke har adgang til appen "Forbindelser", bedes du kontakte din administrator.
#XMSG: Task log message for failure in completing a BW process chain
completeBwProcessChainFailure=BW-proceskæden "{0}" kunne ikke afsluttes i SAP BW Bridge-tenanten.
#XMSG: Task log message for the clickable text of the SAP BW Bridge Monitor link
bwBridgeMonitorLinkText=Vis detaljer i SAP BW Bridge Monitor
#XMSG warning message while retrieving the task log messages if the user does not have access to the SAP BW Bridge space or to the shared object's space
missingPrivilegeOnSpaceError=Der opstod et problem med at hente meddelelserne, eller du har ikke de nødvendige rettigheder til at se dem.

#Messages for API task log of the Data Integration Monitor
#XMSG: Task log message for displaying the API response
viewResponse=Svar modtaget for "{0}".
#XMSG: Task log message for empty ID in the API response to retrieve the status
emptyJobId=Der blev ikke returneret noget id i svarteksten for JSON-stien: "{0}".
#XMSG: Task log message for empty success value in the API response
emptySuccessValue=Der blev ikke returneret nogen succesværdi i svarteksten for JSON-stien: "{0}".
#XMSG: Task log message for empty error value in the API response
emptyErrorValue=Der blev ikke returneret nogen fejlværdi i svarteksten for JSON-stien: "{0}".
#XMSG: Task log message for none of the success/error conditions matching for the values in the API response
conditionsNotFulfilled=Ingen af de angivne succes- eller fejlindikatorbetingelser stemmer overens med værdierne i svaret.
#XMSG: Task log message for the API response status being unsuccessful
statusNotOk=Svaret for "{1}" returnerede statuskoden for mislykket: {0}.
#XMSG: Task log message for the API response body being empty
emptyResponseBody=Svarteksten var tom.
#XMSG: Task log message for the API response location header being invalid
emptyLocationHeader=Placeringssidehovedet i svaret var tomt.
#XMSG: Task log message for failure in retrieving the status of the invoked API
getStatusFailure=Status for den kaldte API kunne ikke hentes.
#XMSG: Task log message for failure in completing the API task
completionFailure=API-opgaven "{0}" blev ikke gennemført.
#XMSG: Task log message for a successful API task completion
apiCompleted=API-opgaven "{0}" er udført.
#XMSG: Task log message for an API task with an invalid configuration
invalidConfiguration=Konfigurationen af API-opgaven "{0}" er ugyldig. Tjek konfigurationen, og prøv igen.
#XMSG: Task log message for the API task being canceled
cancelStart=Ønsket annullering af API-opgaven "{0}" med logId {1}.
#XMSG: Task log message for the cancelation of the an API task which is no longer running and cannot be canceled
unableToCancel=API-opgaven "{0}" med logId {1} kører ikke længere og kan ikke annulleres.
#XMSG: Task log message when starting an API task. It should carry the name of the API task.
apiRunStart=Starter API-opgaven "{0}".
#XMSG: Task log message when the task preparation is taking too long and times out
preparationTimeout=Opgaveforberedelsen tager for lang tid og fik timeout.
#XMSG: Task log message when a task failed because a cancel task was triggered
apiRunCancelled=Kørselsopgaven med logId {0} blev annulleret af annulleringsopgave {1}.
#XMSG: Task log message when a cancel task failed to cancel a run task
cancelFailure=Annulleringsopgaven med logId {1} kunne ikke annullere kørselsopgaven med logId {0}.
#XMSG: Task log message when configuration of the API task is too large
apiTaskconfigurationSizeLimitError=API-opgaven {0} kan ikke starte, da størrelsen af dens opgavekonfiguration overstiger den tilladte størrelse på 100 kibibytes (KiB).
#XMSG: Task log message for failure in completing the Notification task
notificationTaskCompletionFailure=Meddelelsesopgaven "{0}" blev ikke udført.
#XMSG: Task log message when starting a Notification task.
notificationTaskRunStart=Starter meddelelsesopgaven "{0}".
#XMSG: Task log message for a successful Notification task completion
notificationTaskCompletionSuccess=Meddelelsesopgaven "{0}" er udført.
#XFLD: Label for frequency column
everyLabel=Hver
#XFLD: Plural Recurrence text for Hour
hoursLabel=Timer
#XFLD: Plural Recurrence text for Day
daysLabel=Dage
#XFLD: Plural Recurrence text for Month
monthsLabel=Måneder
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minutter
