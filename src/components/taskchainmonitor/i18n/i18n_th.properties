

#XFLD: Task Chain header
headerTxt=เชนงาน ({0})
#XFLD: Task Chain header
headerTxtNew=เชนงาน ({0})
#XTXT: Text for Schedule label
scheduleTxt=กำหนดการ
#XFLD: Text for Create schedule button
createScheduleTxt=สร้างกำหนดการ
#XFLD: Text for edit schedule
editScheduleTxt=แก้ไขกำหนดการ
#XLFD: Text for delete schedule
deleteScheduleTxt=ลบกำหนดการ
#XTXT: text for refresh button label
refrestTxt=รีเฟรช
#XTXT: Text for Completed status
completedTxt=เสร็จสมบูรณ์
#XTX: Text for Running status
runningTxt=กำลังดำเนินการ
#XTX: Text for failed status
failedTxt=ล้มเหลว
#XTX: Text for stopped status
stoppedTxt=ถูกหยุด
#XTX: Text for stopping status
stoppingTxt=กำลังหยุด
#XFLD: Header for Chain name
chainNameLabel=ชื่อเชนงาน
#XFLD: Header for Chain name
chainNameLabelBus=ชื่อทางธุรกิจ
#XFLD: Header for Chain name
chainNameLabelBusNew=ออบเจค (ชื่อทางธุรกิจ)
#XFLD: Header for Chain name
chainNameLabelTech=ชื่อทางเทคนิค
#XFLD: Header for Chain name
chainNameLabelTechNew=ออบเจค (ชื่อทางเทคนิค)
#XFLD: Last Run Status label
lastRunStatuslabel=สถานะการดำเนินการล่าสุด
#XFLD: Last Run Status label
lastRunStatuslabelNew=สถานะ
#XFLD: Frequency Label
frequencyLabel=ความถี่
#XFLD: Frequency Label
frequencyLabelNew=ความถี่ตามกำหนดการ
#XFLD: Duration label
durationLabel=ระยะเวลา
#XFLD: Duration label
durationLabelNew=ระยะเวลาดำเนินการครั้งล่าสุด
#XFLD: Run Start label
runStartLabel=เริ่มต้นการดำเนินการล่าสุด
#XFLD: Run end label
runEndLabel=สิ้นสุดการดำเนินการล่าสุด
#XFLD: Next Run label
nextRunlabel=การดำเนินการครั้งถัดไป
#XFLD: Next Run label
nextRunlabelNew=การดำเนินการตามกำหนดการครั้งถัดไป
#XTXT: Task chain logs link tooltip text
taskChainLogsTxt=รายละเอียดล็อกของเชนงาน
#XTXT: Task chain logs link tooltip text
taskChainLogsTxtNew=รายละเอียด
#XTXT: Scheduled text
scheduledTxt=จัดกำหนดการแล้ว
#XTXT: Paused text
pausedTxt=ถูกหยุดชั่วคราว
#XTXT: Execute button label
runLabel=ดำเนินการ
#XTXT: Execute button label
runLabelNew=เริ่มต้นการดำเนินการ
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=ดำเนินการเชนงาน
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=การดำเนินการเชนงานเริ่มต้นแล้ว
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=การดำเนินการเชนงานเริ่มต้นแล้วสำหรับ {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=ไม่สามารถดำเนินการเชนงานได้
#XFLD: Label for schedule owner column
txtScheduleOwner=เจ้าของกำหนดการ
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=แสดงผู้ที่สร้างกำหนดการ
#XMSG: Task log message for start chain
startChain=กำลังเริ่มต้นการดำเนินการเชนงาน
#XMSG: Task log message for load chain from repository
loadChainFromRepository=โหลดและกำหนดค่าเริ่มต้นของเชนงานแล้ว
#XMSG: Task log message started task
taskStarted=งาน {0} เริ่มต้นแล้ว
#XMSG: Task log message for finished task
taskFinished=งาน {0} สิ้นสุดโดยมีสถานะ ''{1}''
#XMSG: Task log message for chain preparation
chainLoadFromRepository=กำลังโหลดเชนงานและกำลังจัดเตรียมเพื่อดำเนินการงานทั้งหมด {0} รายการที่เป็นส่วนหนึ่งของเชนนี้
#XMSG: Task log message for starting a subtask
chainStartSubtask=กำลังทริกเกอร์งาน {0} เพื่อดำเนินการ ID งาน = {1}
#XMSG: Task log message for finishing a subtask
chainFinishSubtask=งาน {0} เสร็จสิ้นโดยมีสถานะ{1}
#XMSG: Task log message for indicating chain success
chainCompleted=งาน {0} ทั้งหมดเสร็จสมบูรณ์ สถานะเชนงานถูกกำหนดเป็นเสร็จสมบูรณ์
#XMSG: Task log message for indicating chain failure
chainFailed=สำหรับงานทั้งหมด {0} รายการ ทำให้เสร็จสมบูรณ์ได้ {1} งานและล้มเหลว {2} งาน สถานะเชนงานถูกกำหนดเป็นล้มเหลว
#XMSG: Task log message for indicating chain cancelation
chainCanceled=การยกเลิกการดำเนินการเชนงานเริ่มต้นแล้ว ID ล็อกของงานการยกเลิกคือ {0}
#XMSG: Task log message for indicating chain cancelation in the cancel task
chainCanceling=กำลังยกเลิกเชน {0}
#XMSG: Task log message for general chain runtime error
chainError=มีข้อผิดพลาดที่ไม่คาดคิดเกิดขึ้นขณะดำเนินการเชนงาน
#XMSG: Task log message for starting the overwrite check.
chainOverwriteStart=กำลังตรวจสอบว่าเชนงานที่มี ID {0} เสร็จสิ้นแล้วหรือไม่
#XMSG: Task log message indicating the chain is too old.
chainOverwriteTooOld=ไม่พบงานที่จะดำเนินการสำหรับเชน {0} การดำเนินการใช้เวลา {1} นาที สถานะเชนงานถูกกำหนดเป็นล้มเหลว
#XMSG: Task log message indicating the chain was created too recently.
chainOverwriteTooRecent=ไม่พบงานที่จะดำเนินการสำหรับเชน {0} การดำเนินการใช้เวลา {1} นาที เชนงานยังดำเนินการอยู่
#XMSG: Task log message indicating a recent heartbeat was found.
chainOverwriteHeartbeatAlive=เชนงาน {0} ยังดำเนินการอยู่ เสร็จสมบูรณ์: {1}, กำลังดำเนินการ: {2}, ล้มเหลว: {3}, ทริกเกอร์แล้ว: {4}, ยังไม่ได้ทริกเกอร์: {5}
#XMSG: Task log message for a chain that is not running anymore but not everything was triggered.
chainOverwriteNotAllTriggered=มีข้อผิดพลาดเกิดขึ้นขณะดำเนินการเชนงาน {0} และสามารถทริกเกอร์งานได้เพียงบางส่วน เสร็จสมบูรณ์: {1}, กำลังดำเนินการ: {2}, ล้มเหลว: {3}, ทริกเกอร์แล้ว: {4}, ยังไม่ได้ทริกเกอร์: {5} เชนงานนี้ถูกเขียนทับแล้ว
#XMSG: Task log message for a chain that contains a failed subtask.
chainOverwriteAnySubtaskFailed=เชนงาน {0} เสร็จสิ้น หนึ่งงานในเชนล้มเหลว กำลังกำหนดเชนเป็นล้มเหลว
#XMSG: Task log message could not determine status.
chainOverwriteNotDetermined=ข้อผิดพลาดที่ไม่คาดคิดทำให้เราไม่สามารถเข้าถึงสถานะของงานได้ กรุณาลองอีกครั้งและติดต่อ SAP Support หากข้อผิดพลาดยังคงมีอยู่
#XMSG: Task log message could not take over
failedTakeover=ไม่สามารถรับช่วงต่องานที่มีอยู่ได้
#XMSG: Task log parallel check error
parallelCheckError=ไม่สามารถดำเนินการงานได้เนื่องจากงานอื่นกำลังดำเนินการและขัดขวางงานนี้อยู่
#XMSG: Task log parallel task runnig error
parallelTaskRunning=งานที่ขัดแย้งกันกำลังดำเนินการอยู่
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=สถานะ {0} ระหว่างดำเนินการด้วย ID ความสัมพันธ์ {1}
#XMSG: Task log message successful takeover
successTakeover=การล็อคที่เหลือต้องถูกปลดล็อค มีการกำหนดการล็อคใหม่สำหรับงานนี้แล้ว
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=การล็อคงานนี้ถูกรับช่วงต่อโดยงานอื่น
#XMSG: Schedule created alert message
createScheduleSuccess=สร้างกำหนดการแล้ว
#XMSG: Task log message for taskFinishedAt
taskFinishedAt=งาน {0} เสร็จสิ้นเมื่อ {2} โดยมีสถานะ ''{1}''
#XMSG: Placeholder symbol for empty columns
emptyCol=--
#XMSG: Paused Text
txtExpired=หมดอายุแล้ว
#XMSG: Update task schedule success Toast message
updateScheduleSuccess=อัพเดทกำหนดการแล้ว
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=ลบกำหนดการแล้ว
#XMSG: Task chain might have children error text
taskChainNoChildErrorText=เชนงานอาจมีงานย่อยซึ่งไม่แสดงเนื่องจากงานนั้นล้มเหลวก่อนที่จะสร้างแผนได้
#XMSG: Task chain repair recent failed run label
retryRunLabel=ลองการดำเนินการล่าสุดใหม่
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=การดำเนินการลองใหม่ของเชนงานเริ่มต้นแล้ว
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=การดำเนินการลองใหม่ของเชนงานล้มเหลว
#XMSG: chain repair message
chainRetried=การลองใหม่ของเชนงานถูกทริกเกอร์โดยผู้ใช้ {0}
#XMSG: chain not found during run
chainNotFoundDuringRun=ไม่พบเชนงาน {0} กรุณาตรวจสอบว่ามีอยู่หรือไม่ผ่านตัวสร้างข้อมูล และตรวจสอบให้แน่ใจว่าได้ปรับใช้เชนแล้ว
#XMSG: chain is not DAG
chainNotDag=ไม่สามารถเริ่มต้นเชนงาน {0} โครงสร้างเชนงานไม่ถูกต้อง กรุณาตรวจสอบเชนงานในตัวสร้างข้อมูล
#XMSG: chain has not valid parameters
notValidParameters=ไม่สามารถเริ่มต้นเชนงาน {0} พารามิเตอร์ของเชนงานอย่างน้อยหนึ่งรายการไม่ถูกต้อง กรุณาตรวจสอบเชนงานในตัวสร้างข้อมูล
#XMSG: Configuration parameter value size exceeds the maximum allowed limit
configurationSizeLimitError=ไม่สามารถเริ่มต้นเชนงาน {0} ขนาดของการกำหนดรูปแบบงานในเชนเกินขนาดสูงสุดที่อนุญาตไว้ที่ 100 กิบิไบต์ (KiB)
#XMSG: Task {0} has input parameters
taskHasInputParameters=งาน {0} มีพารามิเตอร์ป้อนข้อมูล
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=งานที่ขัดแย้งกันกำลังดำเนินการอยู่
#XMSG: error message for reading data from backend
txtReadBackendError=ดูเหมือนว่ามีข้อผิดพลาดขณะอ่านจากแบคเอนด์
##XMSG: error message for admission control rejection
admissionControlError=งานล้มเหลวเนื่องจากการปฏิเสธการควบคุมการเข้าถึง SAP HANA

#XBUT: Assign schedule menu button label
assignScheduleLabel=กำหนดกำหนดการให้กับฉัน
#XBUT: Pause schedule menu label
pauseScheduleLabel=หยุดกำหนดการชั่วคราว
#XBUT: Resume schedule menu label
resumeScheduleLabel=ดำเนินการกำหนดการต่อ
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=มีข้อผิดพลาดเกิดขึ้นขณะย้ายกำหนดการออก
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=มีข้อผิดพลาดเกิดขึ้นขณะกำหนดกำหนดการ
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=มีข้อผิดพลาดเกิดขึ้นขณะหยุดกำหนดการชั่วคราว
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=มีข้อผิดพลาดเกิดขึ้นขณะดำเนินการกำหนดการต่อ
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=กำลังลบ {0} กำหนดการ
#XMSG: Message for starting mass assign of schedules
massAssignStarted=กำลังเปลี่ยนแปลงเจ้าของ {0} กำหนดการ
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=กำลังหยุด {0} กำหนดการชั่วคราว
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=กำลังดำเนินการ {0} กำหนดการต่อ
#XBUT: Select Columns Button
selectColumnsBtn=เลือกคอลัมน์
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=ดูเหมือนว่าจะมีข้อผิดพลาดในการลองดำเนินการครั้งล่าสุดอีกครั้ง เนื่องจากการดำเนินการงานก่อนหน้าล้มเหลวก่อนที่จะสามารถสร้างแผนได้
#XFLD: Refresh tooltip
TEXT_REFRESH=รีเฟรช
#XFLD: Select Columns tooltip
text_selectColumns=เลือกคอลัมน์

#Messages for BW Process Chain task log of the Data Integration Monitor
#XMSG: Task log message for successfully starting a BW process chain
startBwProcessChainSuccess=เริ่มต้นเชนกระบวนการ BW "{0}" ใน Tenant บริดจ์ของ SAP BW ได้สำเร็จ
#XMSG: Task log message for skipping a BW process chain execution
bwProcessChainSkipped=เชนกระบวนการ BW "{0}" ถูกข้ามเนื่องจากไม่มีข้อมูลใหม่หรือความล้มเหลวของการดำเนินการก่อนหน้า
#XMSG: Task log message for failure in starting a BW process chain
startBwProcessChainFailure=ไม่สามารถเริ่มต้นเชนกระบวนการ BW "{0}" ใน Tenant บริดจ์ของ SAP BW
#XMSG: Task log message for failure in getting the status of a BW process chain
getBwProcessChainStatusFailure=งานล้มเหลวเนื่องจากเราไม่สามารถดึงข้อมูลสถานะของเชนกระบวนการ BW "{0}" ได้เนื่องจากมีปัญหาในการสร้างการเชื่อมต่อกับบริดจ์ของ SAP BW เปิดแอพ "การเชื่อมต่อ" และตรวจสอบการเชื่อมต่อบริดจ์ของ SAP BW ในพื้นที่ "{1}" หากคุณไม่มีสิทธิเข้าถึงแอพ "การเชื่อมต่อ" กรุณาติดต่อผู้ดูแลระบบของคุณ
#XMSG: Task log message for failure in completing a BW process chain
completeBwProcessChainFailure=ไม่สามารถทำเชนกระบวนการ BW "{0}" ให้เสร็จสมบูรณ์ใน Tenant บริดจ์ของ SAP BW
#XMSG: Task log message for the clickable text of the SAP BW Bridge Monitor link
bwBridgeMonitorLinkText=ดูรายละเอียดในตัวติดตามตรวจสอบบริดจ์ของ SAP BW
#XMSG warning message while retrieving the task log messages if the user does not have access to the SAP BW Bridge space or to the shared object's space
missingPrivilegeOnSpaceError=มีปัญหาในการดึงข้อมูลข้อความ หรือคุณไม่มีสิทธิที่จำเป็นในการดูข้อความ

#Messages for API task log of the Data Integration Monitor
#XMSG: Task log message for displaying the API response
viewResponse=ได้รับการตอบกลับสำหรับ "{0}" แล้ว
#XMSG: Task log message for empty ID in the API response to retrieve the status
emptyJobId=ไม่มีการส่งคืน ID ในเนื้อหาการตอบกลับสำหรับพาธ JSON: "{0}"
#XMSG: Task log message for empty success value in the API response
emptySuccessValue=ไม่มีการส่งคืนค่าความสำเร็จในเนื้อหาการตอบกลับสำหรับพาธ JSON: "{0}"
#XMSG: Task log message for empty error value in the API response
emptyErrorValue=ไม่มีการส่งคืนค่าข้อผิดพลาดในเนื้อหาการตอบกลับสำหรับพาธ JSON: "{0}"
#XMSG: Task log message for none of the success/error conditions matching for the values in the API response
conditionsNotFulfilled=ไม่มีเงื่อนไขตัวบ่งชี้ความสำเร็จหรือข้อผิดพลาดรายการใดที่ระบุที่ตรงกับค่าในการตอบกลับ
#XMSG: Task log message for the API response status being unsuccessful
statusNotOk=การตอบกลับสำหรับ "{1}" ส่งคืนรหัสสถานะที่ไม่สำเร็จ: {0}
#XMSG: Task log message for the API response body being empty
emptyResponseBody=เนื้อหาการตอบกลับว่างเปล่า
#XMSG: Task log message for the API response location header being invalid
emptyLocationHeader=ส่วนหัวของที่ตั้งในการตอบกลับว่างเปล่า
#XMSG: Task log message for failure in retrieving the status of the invoked API
getStatusFailure=ไม่สามารถดึงข้อมูลสถานะของ API ที่เรียกใช้
#XMSG: Task log message for failure in completing the API task
completionFailure=ไม่สามารถทำงาน API "{0}" ให้เสร็จสมบูรณ์
#XMSG: Task log message for a successful API task completion
apiCompleted=งาน API "{0}" เสร็จสมบูรณ์แล้ว
#XMSG: Task log message for an API task with an invalid configuration
invalidConfiguration=การกำหนดรูปแบบงาน API "{0}" ไม่ถูกต้อง กรุณาตรวจสอบการกำหนดรูปแบบแล้วลองอีกครั้ง
#XMSG: Task log message for the API task being canceled
cancelStart=ขอยกเลิกงาน API "{0}" ที่มี logId {1} แล้ว
#XMSG: Task log message for the cancelation of the an API task which is no longer running and cannot be canceled
unableToCancel=งาน API "{0}" ที่มี logId {1} ไม่มีการดำเนินการอีกต่อไปและไม่สามารถยกเลิกได้
#XMSG: Task log message when starting an API task. It should carry the name of the API task.
apiRunStart=กำลังเริ่มต้นงาน API "{0}"
#XMSG: Task log message when the task preparation is taking too long and times out
preparationTimeout=การจัดเตรียมงานใช้เวลานานเกินไปและหมดเวลาแล้ว
#XMSG: Task log message when a task failed because a cancel task was triggered
apiRunCancelled=งานที่ดำเนินการที่มี logId {0} ถูกยกเลิกโดยงานการยกเลิก {1}
#XMSG: Task log message when a cancel task failed to cancel a run task
cancelFailure=งานการยกเลิกที่มี logId {1} ไม่สามารถยกเลิกงานที่ดำเนินการที่มี logId {0}
#XMSG: Task log message when configuration of the API task is too large
apiTaskconfigurationSizeLimitError=ไม่สามารถเริ่มต้นงาน API {0} ได้เนื่องจากขนาดของการกำหนดรูปแบบงานเกินขนาดสูงสุดที่อนุญาตไว้ที่ 100 กิบิไบต์ (KiB)
#XMSG: Task log message for failure in completing the Notification task
notificationTaskCompletionFailure=ไม่สามารถทำงานการแจ้ง "{0}" ให้เสร็จสมบูรณ์
#XMSG: Task log message when starting a Notification task.
notificationTaskRunStart=กำลังเริ่มต้นงานการแจ้ง "{0}"
#XMSG: Task log message for a successful Notification task completion
notificationTaskCompletionSuccess=งานการแจ้ง "{0}" เสร็จสมบูรณ์
#XFLD: Label for frequency column
everyLabel=ทุก
#XFLD: Plural Recurrence text for Hour
hoursLabel=ชั่วโมง
#XFLD: Plural Recurrence text for Day
daysLabel=วัน
#XFLD: Plural Recurrence text for Month
monthsLabel=เดือน
#XFLD: Plural Recurrence text for Minutes
minutesLabel=นาที
