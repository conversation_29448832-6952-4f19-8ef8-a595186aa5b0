

#XFLD: Task Chain header
headerTxt=工作細項鏈 ({0})
#XFLD: Task Chain header
headerTxtNew=工作細項鏈 ({0})
#XTXT: Text for Schedule label
scheduleTxt=排程
#XFLD: Text for Create schedule button
createScheduleTxt=建立排程
#XFLD: Text for edit schedule
editScheduleTxt=編輯排程
#XLFD: Text for delete schedule
deleteScheduleTxt=刪除排程
#XTXT: text for refresh button label
refrestTxt=重新整理
#XTXT: Text for Completed status
completedTxt=已完成
#XTX: Text for Running status
runningTxt=執行中
#XTX: Text for failed status
failedTxt=失敗
#XTX: Text for stopped status
stoppedTxt=已停止
#XTX: Text for stopping status
stoppingTxt=正在停止
#XFLD: Header for Chain name
chainNameLabel=工作細項鏈名稱
#XFLD: Header for Chain name
chainNameLabelBus=業務名稱
#XFLD: Header for Chain name
chainNameLabelBusNew=物件 (業務名稱)
#XFLD: Header for Chain name
chainNameLabelTech=技術名稱
#XFLD: Header for Chain name
chainNameLabelTechNew=物件 (技術名稱)
#XFLD: Last Run Status label
lastRunStatuslabel=最後執行狀態
#XFLD: Last Run Status label
lastRunStatuslabelNew=狀態
#XFLD: Frequency Label
frequencyLabel=頻率
#XFLD: Frequency Label
frequencyLabelNew=排程頻率
#XFLD: Duration label
durationLabel=持續期
#XFLD: Duration label
durationLabelNew=最後執行持續期
#XFLD: Run Start label
runStartLabel=最後執行開始時間
#XFLD: Run end label
runEndLabel=最後執行結束時間
#XFLD: Next Run label
nextRunlabel=下一次執行
#XFLD: Next Run label
nextRunlabelNew=排程下一次執行
#XTXT: Task chain logs link tooltip text
taskChainLogsTxt=工作細項鏈日誌明細
#XTXT: Task chain logs link tooltip text
taskChainLogsTxtNew=明細
#XTXT: Scheduled text
scheduledTxt=已排程
#XTXT: Paused text
pausedTxt=已暫停
#XTXT: Execute button label
runLabel=執行
#XTXT: Execute button label
runLabelNew=開始執行
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=執行工作細項鏈
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=已開始執行工作細項鏈。
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=已開始 {0} 的工作細項鏈執行
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=無法執行工作細項鏈。
#XFLD: Label for schedule owner column
txtScheduleOwner=排程所有人
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=顯示建立排程的人員
#XMSG: Task log message for start chain
startChain=開始工作細項鏈執行作業。
#XMSG: Task log message for load chain from repository
loadChainFromRepository=已載入並初始工作細項鏈。
#XMSG: Task log message started task
taskStarted=已開始工作細項 {0}。
#XMSG: Task log message for finished task
taskFinished=工作細項 {0} 已結束，狀態為 {1}。
#XMSG: Task log message for chain preparation
chainLoadFromRepository=載入工作細項鏈，並準備執行屬於此鏈的總計 {0} 個工作細項。
#XMSG: Task log message for starting a subtask
chainStartSubtask=驅動工作細項 {0} 執行。工作細項 ID = {1}。
#XMSG: Task log message for finishing a subtask
chainFinishSubtask=工作細項 {0} 已結束，狀態為 {1}。
#XMSG: Task log message for indicating chain success
chainCompleted=所有 {0} 個工作細項已完成。已將工作細項鏈狀態設定為完成。
#XMSG: Task log message for indicating chain failure
chainFailed=總計 {0} 個工作細項中的 {1} 個工作細項可完成，但 {2} 個工作細項失敗。已將工作細項鏈狀態設定為失敗。
#XMSG: Task log message for indicating chain cancelation
chainCanceled=工作細項鏈執行取消已開始。取消工作細項的日誌 ID 為 {0}。
#XMSG: Task log message for indicating chain cancelation in the cancel task
chainCanceling=取消鏈 {0}。
#XMSG: Task log message for general chain runtime error
chainError=執行工作細項鏈時，發生未預期錯誤。
#XMSG: Task log message for starting the overwrite check.
chainOverwriteStart=正在檢查 ID 為 {0} 的工作細項鏈是否結束。
#XMSG: Task log message indicating the chain is too old.
chainOverwriteTooOld=找不到鏈 {0} 要執行的工作細項。執行為 {1} 分鐘前。已將工作細項鏈狀態設定為失敗。
#XMSG: Task log message indicating the chain was created too recently.
chainOverwriteTooRecent=找不到鏈 {0} 要執行的工作細項。執行為 {1} 分鐘前。工作細項鏈狀態仍為執行中。
#XMSG: Task log message indicating a recent heartbeat was found.
chainOverwriteHeartbeatAlive=工作細項鏈 {0} 仍為執行中。已完成：{1}、執行中：{2}、失敗：{3}、已驅動：{4}、未驅動：{5}。
#XMSG: Task log message for a chain that is not running anymore but not everything was triggered.
chainOverwriteNotAllTriggered=執行工作細項鏈 {0} 時發生錯誤，無法驅動所有工作細項。已完成：{1}、執行中：{2}、失敗：{3}、已驅動：{4}、未驅動：{5}。已覆寫此工作細項鏈。
#XMSG: Task log message for a chain that contains a failed subtask.
chainOverwriteAnySubtaskFailed=工作細項鏈 {0} 已結束。其中一個鏈的工作細項失敗，正在將鏈設定為失敗。
#XMSG: Task log message could not determine status.
chainOverwriteNotDetermined=未預期的錯誤阻止系統存取工作細項狀態。請再試一次，若錯誤持續發生，請聯絡 SAP 支援。
#XMSG: Task log message could not take over
failedTakeover=無法取代現有工作細項。
#XMSG: Task log parallel check error
parallelCheckError=由於其他工作細項執行中且已凍結此工作細項，因此該工作細項無法處理。
#XMSG: Task log parallel task runnig error
parallelTaskRunning=衝突工作細項執行中。
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=執行關聯 ID {1} 期間出現狀態 {0}。
#XMSG: Task log message successful takeover
successTakeover=必須釋放剩餘加鎖。已設定此工作細項的新加鎖。
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=其他工作細項已接手鎖住此工作細項。
#XMSG: Schedule created alert message
createScheduleSuccess=已建立排程
#XMSG: Task log message for taskFinishedAt
taskFinishedAt=工作細項 {0} 已於 {2} 結束，狀態為 {1}。
#XMSG: Placeholder symbol for empty columns
emptyCol=--
#XMSG: Paused Text
txtExpired=已到期
#XMSG: Update task schedule success Toast message
updateScheduleSuccess=已更新排程
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=已刪除排程。
#XMSG: Task chain might have children error text
taskChainNoChildErrorText=由於工作細項在可產生計劃前失敗，因此工作細項鏈可具有未顯示的下層。
#XMSG: Task chain repair recent failed run label
retryRunLabel=重試最新執行
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=已開始重試執行工作細項鏈
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=重試執行工作細項鏈失敗
#XMSG: chain repair message
chainRetried=使用者 {0} 已驅動工作細項鏈重試
#XMSG: chain not found during run
chainNotFoundDuringRun=找不到工作細項鏈 {0}。請透過資料模型建立器確認是否存在，並確保工作細項鏈已部署。
#XMSG: chain is not DAG
chainNotDag=無法開始工作細項鏈 {0}，其結構無效。請在資料模型建立器中檢查工作細項鏈。
#XMSG: chain has not valid parameters
notValidParameters=無法開始工作細項鏈 {0}，其中一或多個參數無效。請在資料模型建立器中檢查工作細項鏈。
#XMSG: Configuration parameter value size exceeds the maximum allowed limit
configurationSizeLimitError=無法開始工作細項鏈 {0}。鏈中的工作細項組態大小超過最大允許的 100 千位元組 (KiB)。
#XMSG: Task {0} has input parameters
taskHasInputParameters=工作細項 {0} 有輸入參數。
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=衝突工作細項執行中
#XMSG: error message for reading data from backend
txtReadBackendError=自後端讀取資料時發生錯誤。
##XMSG: error message for admission control rejection
admissionControlError=由於 SAP HANA 許可控制拒絕，因此工作細項失敗。

#XBUT: Assign schedule menu button label
assignScheduleLabel=將排程指派給我
#XBUT: Pause schedule menu label
pauseScheduleLabel=暫停排程
#XBUT: Resume schedule menu label
resumeScheduleLabel=繼續排程
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=移除排程時發生錯誤。
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=指派排程時發生錯誤。
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=暫停排程時發生錯誤。
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=繼續排程時發生錯誤。
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=正在刪除 {0} 個排程
#XMSG: Message for starting mass assign of schedules
massAssignStarted=正在更改 {0} 個排程的所有人
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=正在暫停 {0} 個排程
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=正在繼續 {0} 個排程
#XBUT: Select Columns Button
selectColumnsBtn=選擇欄
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=由於上一個工作細項執行在可產生計劃前失敗，因此重試最後一個執行時似乎發生錯誤。
#XFLD: Refresh tooltip
TEXT_REFRESH=重新整理
#XFLD: Select Columns tooltip
text_selectColumns=選擇欄

#Messages for BW Process Chain task log of the Data Integration Monitor
#XMSG: Task log message for successfully starting a BW process chain
startBwProcessChainSuccess=已在 SAP BW 橋接租用戶中成功開始 BW 程序鏈 "{0}"。
#XMSG: Task log message for skipping a BW process chain execution
bwProcessChainSkipped=BW 程序鏈 "{0}" 已跳過，可能因為無法使用新資料或先前執行失敗。
#XMSG: Task log message for failure in starting a BW process chain
startBwProcessChainFailure=在 SAP BW 橋接租用戶中開始 BW 程序鏈 "{0}" 失敗。
#XMSG: Task log message for failure in getting the status of a BW process chain
getBwProcessChainStatusFailure=工作細項失敗，因為建立 SAP BW 橋接的連線時發生問題，無法檢索 BW 程序鏈 "{0}" 的狀態。請開啟「連線」應用程式，並驗證 "{1}" 空間中的 SAP BW 橋接連線。若您無法存取「連線」應用程式，請聯絡管理員。
#XMSG: Task log message for failure in completing a BW process chain
completeBwProcessChainFailure=在 SAP BW 橋接租用戶中完成 BW 程序鏈 "{0}" 失敗。
#XMSG: Task log message for the clickable text of the SAP BW Bridge Monitor link
bwBridgeMonitorLinkText=在 SAP BW 橋接監控器中檢視明細
#XMSG warning message while retrieving the task log messages if the user does not have access to the SAP BW Bridge space or to the shared object's space
missingPrivilegeOnSpaceError=檢索訊息時發生問題，或者您沒有必要權限無法檢視。

#Messages for API task log of the Data Integration Monitor
#XMSG: Task log message for displaying the API response
viewResponse=已收到 "{0}" 的回應。
#XMSG: Task log message for empty ID in the API response to retrieve the status
emptyJobId=JSON 路徑："{0}" 的回應本文中未傳回 ID。
#XMSG: Task log message for empty success value in the API response
emptySuccessValue=JSON 路徑："{0}" 的回應本文中未傳回成功值。
#XMSG: Task log message for empty error value in the API response
emptyErrorValue=JSON 路徑："{0}" 的回應本文中未傳回錯誤值。
#XMSG: Task log message for none of the success/error conditions matching for the values in the API response
conditionsNotFulfilled=指定的成功或錯誤指示碼條件皆不符合回應中的值。
#XMSG: Task log message for the API response status being unsuccessful
statusNotOk="{1}" 的回應已傳回失敗狀態代碼：{0}。
#XMSG: Task log message for the API response body being empty
emptyResponseBody=回應本文空白。
#XMSG: Task log message for the API response location header being invalid
emptyLocationHeader=回應中的位置表頭空白。
#XMSG: Task log message for failure in retrieving the status of the invoked API
getStatusFailure=無法檢索呼叫的 API 狀態。
#XMSG: Task log message for failure in completing the API task
completionFailure=API 工作細項 "{0}" 無法完成。
#XMSG: Task log message for a successful API task completion
apiCompleted=API 工作細項 "{0}" 已成功完成。
#XMSG: Task log message for an API task with an invalid configuration
invalidConfiguration=API 工作細項 "{0}" 的組態無效。請檢查組態並再試一次。
#XMSG: Task log message for the API task being canceled
cancelStart=請求取消 logId 為 {1} 的 API 工作細項 "{0}" 。
#XMSG: Task log message for the cancelation of the an API task which is no longer running and cannot be canceled
unableToCancel=logId 為 {1} 的 API 工作細項 "{0}''" 不再執行中，且無法取消。
#XMSG: Task log message when starting an API task. It should carry the name of the API task.
apiRunStart=正在開始 API 工作細項 "{0}"。
#XMSG: Task log message when the task preparation is taking too long and times out
preparationTimeout=工作細項準備過久且已逾時。
#XMSG: Task log message when a task failed because a cancel task was triggered
apiRunCancelled=取消工作細項 {1} 已取消 logId 為 {0} 的執行工作細項。
#XMSG: Task log message when a cancel task failed to cancel a run task
cancelFailure=logId 為 {1} 的取消工作細項無法取消 logId 為 {0} 的執行工作細項。
#XMSG: Task log message when configuration of the API task is too large
apiTaskconfigurationSizeLimitError=無法開始 API 工作細項 {0}，因為工作細項組態大小超過最大允許的 100 千位元組 (KiB)。
#XMSG: Task log message for failure in completing the Notification task
notificationTaskCompletionFailure=通知工作細項 "{0}" 無法完成。
#XMSG: Task log message when starting a Notification task.
notificationTaskRunStart=正在開始通知工作細項 "{0}"。
#XMSG: Task log message for a successful Notification task completion
notificationTaskCompletionSuccess=通知工作細項 "{0}" 已完成。
#XFLD: Label for frequency column
everyLabel=每
#XFLD: Plural Recurrence text for Hour
hoursLabel=小時
#XFLD: Plural Recurrence text for Day
daysLabel=天
#XFLD: Plural Recurrence text for Month
monthsLabel=個月
#XFLD: Plural Recurrence text for Minutes
minutesLabel=分鐘
