

#XFLD: Task Chain header
headerTxt=Uzdevumu ķēdes ({0})
#XFLD: Task Chain header
headerTxtNew=Uzdevumu ķēdes ({0})
#XTXT: Text for Schedule label
scheduleTxt=Grafiks
#XFLD: Text for Create schedule button
createScheduleTxt=Izveidot grafiku
#XFLD: Text for edit schedule
editScheduleTxt=Rediģēt grafiku
#XLFD: Text for delete schedule
deleteScheduleTxt=Dzēst grafiku
#XTXT: text for refresh button label
refrestTxt=Atsvaidzināt
#XTXT: Text for Completed status
completedTxt=Pabeigts
#XTX: Text for Running status
runningTxt=Tiek izpildīts
#XTX: Text for failed status
failedTxt=Neizdevās
#XTX: Text for stopped status
stoppedTxt=Apturēts
#XTX: Text for stopping status
stoppingTxt=Tiek apturēts
#XFLD: Header for Chain name
chainNameLabel=Uzdevumu ķēdes nosaukums
#XFLD: Header for Chain name
chainNameLabelBus=Biznesa nosaukums
#XFLD: Header for Chain name
chainNameLabelBusNew=Objekts (Biznesa nosaukums)
#XFLD: Header for Chain name
chainNameLabelTech=Tehniskais nosaukums
#XFLD: Header for Chain name
chainNameLabelTechNew=Objekts (Tehniskais nosaukums)
#XFLD: Last Run Status label
lastRunStatuslabel=Pēdējās izpildes statuss
#XFLD: Last Run Status label
lastRunStatuslabelNew=Statuss
#XFLD: Frequency Label
frequencyLabel=Biežums
#XFLD: Frequency Label
frequencyLabelNew=Ieplānotais biežums
#XFLD: Duration label
durationLabel=Ilgums
#XFLD: Duration label
durationLabelNew=Pēdējās izpildes ilgums
#XFLD: Run Start label
runStartLabel=Pēdējās izpildes sākums
#XFLD: Run end label
runEndLabel=Pēdējās izpildes beigas
#XFLD: Next Run label
nextRunlabel=Nākamā izpilde
#XFLD: Next Run label
nextRunlabelNew=Ieplānotā nākamā izpilde
#XTXT: Task chain logs link tooltip text
taskChainLogsTxt=Uzdevumu ķēdes žurnāla detalizētā informācija
#XTXT: Task chain logs link tooltip text
taskChainLogsTxtNew=Detalizēta informācija
#XTXT: Scheduled text
scheduledTxt=Ieplānots
#XTXT: Paused text
pausedTxt=Apturēts
#XTXT: Execute button label
runLabel=Izpildīt
#XTXT: Execute button label
runLabelNew=Sākt izpildi
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Izpildīt uzdevumu ķēdi
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Uzdevumu ķēdes izpilde ir sākta.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Uzdevumu ķēdes izpilde tika sākta šim: {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Neizdevās izpildīt uzdevumu ķēdi.
#XFLD: Label for schedule owner column
txtScheduleOwner=Grafika īpašnieks
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Rāda, kurš izveidoja grafiku
#XMSG: Task log message for start chain
startChain=Tiek sākta uzdevumu ķēdes izpilde.
#XMSG: Task log message for load chain from repository
loadChainFromRepository=Uzdevumu ķēde ir ielādēta un inicializēta.
#XMSG: Task log message started task
taskStarted=Uzdevums {0} ir sākts.
#XMSG: Task log message for finished task
taskFinished=Uzdevums {0} beidzās ar statusu {1}.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Notiek uzdevumu ķēdes ielāde un sagatavošanās kopā {0} uzdevumu izpildei, kas ir daļa no šīs ķēdes.
#XMSG: Task log message for starting a subtask
chainStartSubtask=Notiek uzdevuma {0} izpildes izraisīšana. Uzdevuma ID = {1}.
#XMSG: Task log message for finishing a subtask
chainFinishSubtask=Uzdevums {0} ir pabeigts ar statusu {1}.
#XMSG: Task log message for indicating chain success
chainCompleted=Visi {0} uzdevumi ir pabeigti. Uzdevumu ķēdes statuss ir iestatīts kā pabeigts.
#XMSG: Task log message for indicating chain failure
chainFailed=No kopumā {0} uzdevumiem {1} uzdevumus varēja pabeigt un {2} uzdevumi neizdevās. Uzdevumu ķēdes statuss ir iestatīts kā Neizdevās.
#XMSG: Task log message for indicating chain cancelation
chainCanceled=Uzdevumu ķēdes izpildes atcelšana ir sākusies. Atcelšanas uzdevuma žurnāla ID ir {0}.
#XMSG: Task log message for indicating chain cancelation in the cancel task
chainCanceling=Tiek atcelta ķēde {0}.
#XMSG: Task log message for general chain runtime error
chainError=Izpildot uzdevumu ķēdi, radās neparedzēta kļūda.
#XMSG: Task log message for starting the overwrite check.
chainOverwriteStart=Notiek pārbaude, vai uzdevumu ķēde ar ID {0} ir pabeigta.
#XMSG: Task log message indicating the chain is too old.
chainOverwriteTooOld=Nevarēja atrast nevienu uzdevumu, ko izpildīt ķēdei {0}. Izpilde ir {1} minūtes veca. Uzdevumu ķēdes statuss ir iestatīts kā Neizdevās.
#XMSG: Task log message indicating the chain was created too recently.
chainOverwriteTooRecent=Nevarēja atrast nevienu uzdevumu, ko izpildīt ķēdei {0}. Izpilde ir {1} minūtes veca. Uzdevumu ķēde joprojām tiek izpildīta.
#XMSG: Task log message indicating a recent heartbeat was found.
chainOverwriteHeartbeatAlive=Uzdevumu ķēde {0} joprojām tiek izpildīta. Pabeigts: {1}, tiek izpildīts: {2}, neizdevās: {3}, izraisīts: {4}, nav izraisīts: {5}.
#XMSG: Task log message for a chain that is not running anymore but not everything was triggered.
chainOverwriteNotAllTriggered=Izpildot uzdevumu ķēdi {0}, radās kļūda, un ne visus uzdevumus varēja izraisīt. Pabeigts: {1}, tiek izpildīts: {2}, neizdevās: {3}, izraisīts: {4}, nav izraisīts: {5}. Šī uzdevumu ķēde ir pārrakstīta.
#XMSG: Task log message for a chain that contains a failed subtask.
chainOverwriteAnySubtaskFailed=Uzdevumu ķēde {0} ir pabeigta. Viens ķēdes uzdevums neizdevās, iestatot ķēdi kā neizdevušos.
#XMSG: Task log message could not determine status.
chainOverwriteNotDetermined=Neparedzēta kļūda liedz mums piekļuvi šī uzdevuma statusam. Mēģiniet vēlreiz un, ja kļūda atkārtojas, sazinieties ar savu SAP atbalsta dienestu.
#XMSG: Task log message could not take over
failedTakeover=Neizdevās pārņemt esošo uzdevumu.
#XMSG: Task log parallel check error
parallelCheckError=Šo uzdevumu nevar apstrādāt, jo tiek izpildīts cits uzdevums un tas jau bloķē šo uzdevumu.
#XMSG: Task log parallel task runnig error
parallelTaskRunning=Konfliktējošais uzdevums jau tiek izpildīts.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Statuss {0} izpilde laikā ar korelācijas ID {1}.
#XMSG: Task log message successful takeover
successTakeover=Atlikusī bloķēšana ir jāatbrīvo. Šim uzdevumam ir iestatīta jauna bloķēšana.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Šī uzdevuma bloķēšanu pārņēma cits uzdevums.
#XMSG: Schedule created alert message
createScheduleSuccess=Grafiks ir izveidots
#XMSG: Task log message for taskFinishedAt
taskFinishedAt=Uzdevums {0} pabeigts plkst. {2} ar statusu {1}.
#XMSG: Placeholder symbol for empty columns
emptyCol=--
#XMSG: Paused Text
txtExpired=Beidzies derīgums
#XMSG: Update task schedule success Toast message
updateScheduleSuccess=Grafiks ir atjaunināts
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Grafiks ir izdzēsts.
#XMSG: Task chain might have children error text
taskChainNoChildErrorText=Uzdevumu ķēdei, iespējams, ir bērnelementi, kas netiek rādīti, jo uzdevums neizdevās, pirms varēja ģenerēt plānu.
#XMSG: Task chain repair recent failed run label
retryRunLabel=Vēlreiz mēģināt pēdējo izpildi
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Uzdevumu ķēdes atkārtotas mēģināšanas izpilde ir sākta.
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Uzdevumu ķēdes atkārtotas mēģināšanas izpilde neizdevās.
#XMSG: chain repair message
chainRetried=Uzdevumu ķēdes atkārtotu mēģināšanu izraisīja lietotājs {0}
#XMSG: chain not found during run
chainNotFoundDuringRun=Nevarēja atrast uzdevumu ķēdi {0}. Pārbaudiet, vai tā pastāv, izmantojot datu veidotāju, un nodrošiniet, ka ķēde ir izvietota.
#XMSG: chain is not DAG
chainNotDag=Nevar startēt uzdevumu ķēdi {0}. Tās struktūra ir nederīga. Pārbaudiet uzdevumu ķēdi datu veidotājā.
#XMSG: chain has not valid parameters
notValidParameters=Nevar startēt uzdevumu ķēdi {0}. Viens vai vairāki no tās parametriem ir nederīgi. Pārbaudiet uzdevumu ķēdi datu veidotājā.
#XMSG: Configuration parameter value size exceeds the maximum allowed limit
configurationSizeLimitError=Nevar startēt uzdevumu ķēdi {0}. Uzdevuma konfigurācijas lielums ķēdē pārsniedz maksimālo atļauto lielumu: 100 kibibaiti (KiB).
#XMSG: Task {0} has input parameters
taskHasInputParameters=Uzdevumam {0} ir ievades parametri.
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Konfliktējošais uzdevums jau tiek izpildīts
#XMSG: error message for reading data from backend
txtReadBackendError=Šķiet, ka, nolasot no aizmugursistēmas, radās kļūda.
##XMSG: error message for admission control rejection
admissionControlError=Uzdevums neizdevās SAP HANA ieejas vadības noraidījuma dēļ.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Piešķirt grafiku man
#XBUT: Pause schedule menu label
pauseScheduleLabel=Aizturēt grafiku
#XBUT: Resume schedule menu label
resumeScheduleLabel=Turpināt grafiku
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Noņemot grafikus, radās kļūda.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Piešķirot grafikus, radās kļūda.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Aizturot grafikus, radās kļūda.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Turpinot grafikus, radās kļūda.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted={0} grafiku dzēšana
#XMSG: Message for starting mass assign of schedules
massAssignStarted={0} grafiku īpašnieka maiņa
#XMSG: Message for starting mass pausing of schedules
massPauseStarted={0} grafiku aizturēšana
#XMSG: Message for starting mass resuming of schedules
massResumeStarted={0} grafiku turpināšana
#XBUT: Select Columns Button
selectColumnsBtn=Atlasīt kolonnas
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Šķiet, ka radusies kļūda ar pēdējo izpildi, jo iepriekšējā uzdevuma izpilde neizdevās pirms plāna ģenerēšanas.
#XFLD: Refresh tooltip
TEXT_REFRESH=Atsvaidzināt
#XFLD: Select Columns tooltip
text_selectColumns=Atlasīt kolonnas

#Messages for BW Process Chain task log of the Data Integration Monitor
#XMSG: Task log message for successfully starting a BW process chain
startBwProcessChainSuccess=BW procesu ķēde “{0}” ir sekmīgi sākta SAP BW tilta nomniekā.
#XMSG: Task log message for skipping a BW process chain execution
bwProcessChainSkipped=BW procesu ķēde "{0}" tika izlaista vai nu tāpēc, ka jauni dati nebija pieejami, vai tāpēc, ka iepriekšējā izpilde neizdevās.
#XMSG: Task log message for failure in starting a BW process chain
startBwProcessChainFailure=BW procesu ķēdei "{0}" neizdevās sākties SAP BW tilta nomniekā.
#XMSG: Task log message for failure in getting the status of a BW process chain
getBwProcessChainStatusFailure=Uzdevums neizdevās, jo nevarējām izgūt BW procesu ķēdes "{0}" statusu, tāpēc ka bija grūtības izveidot savienojumu ar SAP BW tiltu. Atveriet lietojumprogrammu "Savienojumi" un pārbaudiet SAP BW tilta savienojumu vietā "{1}". Ja jums nav piekļuves lietojumprogrammai "Savienojumi", lūdzu, sazinieties ar administratoru.
#XMSG: Task log message for failure in completing a BW process chain
completeBwProcessChainFailure=BW procesu ķēdi "{0}" neizdevās pabeigt SAP BW tilta nomniekā.
#XMSG: Task log message for the clickable text of the SAP BW Bridge Monitor link
bwBridgeMonitorLinkText=Detalizētu informāciju sk. SAP BW tilta pārraugā
#XMSG warning message while retrieving the task log messages if the user does not have access to the SAP BW Bridge space or to the shared object's space
missingPrivilegeOnSpaceError=Bija problēma izgūt ziņojumus, vai jums nav nepieciešamās atļaujas tos skatīt.

#Messages for API task log of the Data Integration Monitor
#XMSG: Task log message for displaying the API response
viewResponse=Atbilde saņemta attiecībā uz "{0}".
#XMSG: Task log message for empty ID in the API response to retrieve the status
emptyJobId=Nekāds ID netika atgriezts atbildes pamattekstā JSON ceļam: "{0}".
#XMSG: Task log message for empty success value in the API response
emptySuccessValue=Nekāda sekmīguma vērtība netika atgriezta atbildes pamattekstā JSON ceļam: "{0}".
#XMSG: Task log message for empty error value in the API response
emptyErrorValue=Nekāda kļūdas vērtība netika atgriezta atbildes pamattekstā JSON ceļam: "{0}".
#XMSG: Task log message for none of the success/error conditions matching for the values in the API response
conditionsNotFulfilled=Neviens no norādītajiem sekmīguma vai kļūdas indikatora nosacījumiem nesaskanēja ar vērtībām atbildē.
#XMSG: Task log message for the API response status being unsuccessful
statusNotOk=Atbilde attiecībā uz “{1}” atgrieza nesekmīga statusa kodu: {0}.
#XMSG: Task log message for the API response body being empty
emptyResponseBody=Atbildes pamatteksts bija tukšs.
#XMSG: Task log message for the API response location header being invalid
emptyLocationHeader=Atrašanās vietas galvene atbildē bija tukša.
#XMSG: Task log message for failure in retrieving the status of the invoked API
getStatusFailure=Izsauktās API statusu nevarēja izgūt.
#XMSG: Task log message for failure in completing the API task
completionFailure=API uzdevumu "{0}" neizdevās pabeigt.
#XMSG: Task log message for a successful API task completion
apiCompleted=API uzdevums “{0}” ir pabeigts sekmīgi.
#XMSG: Task log message for an API task with an invalid configuration
invalidConfiguration=API uzdevuma “{0}” konfigurācija ir nederīga. Lūdzu, pārbaudiet konfigurāciju un mēģiniet vēlreiz.
#XMSG: Task log message for the API task being canceled
cancelStart=Pieprasīts atcelt API uzdevumu "{0}" ar logId {1}.
#XMSG: Task log message for the cancelation of the an API task which is no longer running and cannot be canceled
unableToCancel=API uzdevums "{0}" ar logId {1} vairs nedarbojas, un to nevar atcelt.
#XMSG: Task log message when starting an API task. It should carry the name of the API task.
apiRunStart=Notiek API uzdevuma "{0}" startēšana.
#XMSG: Task log message when the task preparation is taking too long and times out
preparationTimeout=Uzdevuma sagatavošana aizņem pārāk ilgu laiku, un tai ir iestājies taimauts.
#XMSG: Task log message when a task failed because a cancel task was triggered
apiRunCancelled=Izpildes uzdevumu ar logId {0} atcēla atcelšanas uzdevums {1}.
#XMSG: Task log message when a cancel task failed to cancel a run task
cancelFailure=Atcelšanas uzdevumam ar logId {1} neizdevās atcelt izpildes uzdevumu ar logId {0}.
#XMSG: Task log message when configuration of the API task is too large
apiTaskconfigurationSizeLimitError=API uzdevumu {0} nevar startēt, jo tā uzdevuma konfigurācijas lielums pārsniedz maksimālo atļauto lielumu: 100 kibibaiti (KiB).
#XMSG: Task log message for failure in completing the Notification task
notificationTaskCompletionFailure=Paziņojuma uzdevumu “{0}” neizdevās pabeigt.
#XMSG: Task log message when starting a Notification task.
notificationTaskRunStart=Tiek startēts paziņojuma uzdevums “{0}”.
#XMSG: Task log message for a successful Notification task completion
notificationTaskCompletionSuccess=Paziņojuma uzdevums “{0}” ir pabeigts.
#XFLD: Label for frequency column
everyLabel=Ik pēc
#XFLD: Plural Recurrence text for Hour
hoursLabel=Stundām
#XFLD: Plural Recurrence text for Day
daysLabel=Dienām
#XFLD: Plural Recurrence text for Month
monthsLabel=Mēnešiem
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minūtēm
