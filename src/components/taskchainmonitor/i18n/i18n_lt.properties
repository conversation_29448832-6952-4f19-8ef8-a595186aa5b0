

#XFLD: Task Chain header
headerTxt=Užduočių grandinės ({0})
#XFLD: Task Chain header
headerTxtNew=Užduo<PERSON><PERSON>ų grandinės ({0})
#XTXT: Text for Schedule label
scheduleTxt=Tvarkaraštis
#XFLD: Text for Create schedule button
createScheduleTxt=Kurti tvarkaraštį
#XFLD: Text for edit schedule
editScheduleTxt=Redaguoti tvarkaraštį
#XLFD: Text for delete schedule
deleteScheduleTxt=Naikinti tvarkaraštį
#XTXT: text for refresh button label
refrestTxt=Atnaujinti
#XTXT: Text for Completed status
completedTxt=Užbaigta
#XTX: Text for Running status
runningTxt=Vykdoma
#XTX: Text for failed status
failedTxt=Nepavyko
#XTX: Text for stopped status
stoppedTxt=Sustabdyta
#XTX: Text for stopping status
stoppingTxt=Stabdoma
#XFLD: Header for Chain name
chainNameLabel=Užduočių grandinės pavadinimas
#XFLD: Header for Chain name
chainNameLabelBus=Verslo pavadinimas
#XFLD: Header for Chain name
chainNameLabelBusNew=Objektas (verslo pavadinimas)
#XFLD: Header for Chain name
chainNameLabelTech=Techninis pavadinimas
#XFLD: Header for Chain name
chainNameLabelTechNew=Objektas (techninis pavadinimas)
#XFLD: Last Run Status label
lastRunStatuslabel=Paskutinio vykdymo būsena
#XFLD: Last Run Status label
lastRunStatuslabelNew=Būsena
#XFLD: Frequency Label
frequencyLabel=Dažnumas
#XFLD: Frequency Label
frequencyLabelNew=Suplanuotas dažnumas
#XFLD: Duration label
durationLabel=Trukmė
#XFLD: Duration label
durationLabelNew=Paskutinio vykdymo trukmė
#XFLD: Run Start label
runStartLabel=Paskutinio vykdymo pradžia
#XFLD: Run end label
runEndLabel=Paskutinio vykdymo pabaiga
#XFLD: Next Run label
nextRunlabel=Kitas vykdymas
#XFLD: Next Run label
nextRunlabelNew=Suplanuotas kitas vykdymas
#XTXT: Task chain logs link tooltip text
taskChainLogsTxt=Užduočių grandinės žurnalo išsami informacija
#XTXT: Task chain logs link tooltip text
taskChainLogsTxtNew=Išsami informacija
#XTXT: Scheduled text
scheduledTxt=Suplanuota
#XTXT: Paused text
pausedTxt=Pristabdyta
#XTXT: Execute button label
runLabel=Vykdyti
#XTXT: Execute button label
runLabelNew=Pradėti vykdymą
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Vykdyti užduočių grandinę
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Užduočių grandinės vykdymas pradėtas.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Užduočių grandinės vykdymas pradėtas {0}.
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Nepavyko vykdyti užduočių grandinės.
#XFLD: Label for schedule owner column
txtScheduleOwner=Planuoti savininką
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Rodo tvarkaraščio autorių
#XMSG: Task log message for start chain
startChain=Pradedamas užduočių grandinės vykdymas.
#XMSG: Task log message for load chain from repository
loadChainFromRepository=Užduočių grandinė įkelta ir inicijuota.
#XMSG: Task log message started task
taskStarted=Užduotis {0} pradėta.
#XMSG: Task log message for finished task
taskFinished=Užduotis {0} pasibaigė su būsena {1}.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Įkeliama užduočių grandinė ir ruošiamasi vykdyti iš viso {0} užd., kurios priklauso šiai grandinei.
#XMSG: Task log message for starting a subtask
chainStartSubtask=Užduotis {0} inicijuojama vykdymui. Užduoties ID = {1}.
#XMSG: Task log message for finishing a subtask
chainFinishSubtask=Užduotis {0} užbaigta su būsena {1}.
#XMSG: Task log message for indicating chain success
chainCompleted=Visos {0} užduotys užbaigtos. Užduočių grandinės būsena nustatyta į „Užbaigta“.
#XMSG: Task log message for indicating chain failure
chainFailed=Iš {0} užduočių pavyko užbaigti {1} užd., o {2} užd. nepavyko. Užduočių grandinės būsena nustatyta į „Nepavyko“.
#XMSG: Task log message for indicating chain cancelation
chainCanceled=Užduočių grandinės vykdymo atšaukimas pradėtas. Atšaukimo užduoties žurnalo ID yra {0}.
#XMSG: Task log message for indicating chain cancelation in the cancel task
chainCanceling=Atšaukiama grandinė {0}.
#XMSG: Task log message for general chain runtime error
chainError=Vykdant užduočių grandinę įvyko nenumatyta klaida.
#XMSG: Task log message for starting the overwrite check.
chainOverwriteStart=Tikrinama, ar užduočių grandinė, kurios ID {0}, užbaigta.
#XMSG: Task log message indicating the chain is too old.
chainOverwriteTooOld=Nerasta grandinės {0} užduočių vykdymui. Vykdymas trunka {1} min. Užduočių grandinės būsena nustatyta į „Nepavyko“.
#XMSG: Task log message indicating the chain was created too recently.
chainOverwriteTooRecent=Nerasta grandinės {0} užduočių vykdymui. Vykdymas trunka {1} min. Užduočių grandinė vis dar vykdoma.
#XMSG: Task log message indicating a recent heartbeat was found.
chainOverwriteHeartbeatAlive=Užduočių grandinė {0} vis dar vykdoma. Užbaigta: {1}, vykdoma: {2}, nepavyko: {3}, inicijuota: {4}, neinicijuota: {5}.
#XMSG: Task log message for a chain that is not running anymore but not everything was triggered.
chainOverwriteNotAllTriggered=Vykdant užduočių grandinę {0} įvyko klaida ir ne visas užduotis pavyko inicijuoti. Užbaigta: {1}, vykdoma: {2}, nepavyko: {3}, inicijuota: {4}, neinicijuota: {5}. Ši užduočių grandinė buvo perrašyta.
#XMSG: Task log message for a chain that contains a failed subtask.
chainOverwriteAnySubtaskFailed=Užduočių grandinė {0} užbaigta. Viena grandinės užduotis nepavyko, grandinė nustatoma kaip nepavykusi.
#XMSG: Task log message could not determine status.
chainOverwriteNotDetermined=Nenumatyta klaida neleidžia pasiekti užduoties būsenos. Bandykite dar kartą ir, jei klaida kartosis, kreipkitės į „SAP pagalbos tarnybą“.
#XMSG: Task log message could not take over
failedTakeover=Nepavyko perimti esamos užduoties.
#XMSG: Task log parallel check error
parallelCheckError=Užduoties negalima apdoroti, nes vykdoma kita, jau blokuojanti šią užduotį.
#XMSG: Task log parallel task runnig error
parallelTaskRunning=Jau vykdoma konfliktinė užduotis.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Būsena {0} vykdymo metu su koreliacijos ID {1}.
#XMSG: Task log message successful takeover
successTakeover=Likusį užrakinimą reikėjo panaikinti. Nustatytas naujas šios užduoties užrakinimas.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Šios užduoties užrakinimą perėmė kita užduotis.
#XMSG: Schedule created alert message
createScheduleSuccess=Tvarkaraštis sukurtas
#XMSG: Task log message for taskFinishedAt
taskFinishedAt=Užduotis {0} užbaigta {2} su būsena {1}.
#XMSG: Placeholder symbol for empty columns
emptyCol=--
#XMSG: Paused Text
txtExpired=Nebegalioja
#XMSG: Update task schedule success Toast message
updateScheduleSuccess=Tvarkaraštis atnaujintas
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Tvarkaraštis panaikintas.
#XMSG: Task chain might have children error text
taskChainNoChildErrorText=Užduočių grandinėje gali būti antrinių elementų, kurie nerodomi, nes užduotis nepavyko prieš sugeneruojant planą.
#XMSG: Task chain repair recent failed run label
retryRunLabel=Kartoti naujausią vykdymą
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Užduočių grandinės kartojimo vykdymas pradėtas
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Užduočių grandinės kartojimo vykdymas nepavyko
#XMSG: chain repair message
chainRetried=Užduočių grandinės kartojimą inicijavo vartotojas {0}
#XMSG: chain not found during run
chainNotFoundDuringRun=Nepavyko rasti užduočių grandinės {0}. Naudodami duomenų daryklę patikrinkite, ar ji yra, ir įsitikinkite, kad grandinė įdiegta.
#XMSG: chain is not DAG
chainNotDag=Nepavyko pradėti užduočių grandinės {0}. Jos struktūra netinkama. Patikrinkite užduočių grandinę duomenų daryklėje.
#XMSG: chain has not valid parameters
notValidParameters=Nepavyko pradėti užduočių grandinės {0}. Vienas ar daugiau jos parametrų netinkami. Patikrinkite užduočių grandinę duomenų daryklėje.
#XMSG: Configuration parameter value size exceeds the maximum allowed limit
configurationSizeLimitError=Nepavyko pradėti užduočių grandinės {0}. Užduoties konfigūracijos grandinėje dydis viršija maksimalų leidžiamą dydį – 100 kibibaitų (KiB).
#XMSG: Task {0} has input parameters
taskHasInputParameters=Užduotyje {0} yra įvesties parametrų.
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Jau vykdoma konfliktinė užduotis
#XMSG: error message for reading data from backend
txtReadBackendError=Regis, įvyko klaida nuskaitant iš galutinio apdorojimo sistemos.
##XMSG: error message for admission control rejection
admissionControlError=Užduotis nepavyko dėl SAP HANA leidimų kontrolės atmetimo.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Priskirti tvarkaraštį man
#XBUT: Pause schedule menu label
pauseScheduleLabel=Pristabdyti tvarkaraštį
#XBUT: Resume schedule menu label
resumeScheduleLabel=Atkurti tvarkaraštį
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Šalinant tvarkaraščius įvyko klaida.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Priskiriant tvarkaraščius įvyko klaida.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Pristabdant tvarkaraščius įvyko klaida.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Atkuriant tvarkaraščius įvyko klaida.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Naikinti {0} tvarkaraščius
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Keičiamas {0} tvarkaraščių savininkas
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Pristabdomi {0} tvarkaraščiai
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Atkuriami {0} tvarkaraščiai
#XBUT: Select Columns Button
selectColumnsBtn=Pasirinkti stulpelius
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Panašu, kad paskutinį kartą bandant iš naujo įvyko klaida, nes ankstesnė užduotis nepavyko sugeneravus plano.
#XFLD: Refresh tooltip
TEXT_REFRESH=Atnaujinti
#XFLD: Select Columns tooltip
text_selectColumns=Pasirinkti stulpelius

#Messages for BW Process Chain task log of the Data Integration Monitor
#XMSG: Task log message for successfully starting a BW process chain
startBwProcessChainSuccess=BW proceso grandinė „{0}“ buvo sėkmingai pradėta SAP BW tilto kliente.
#XMSG: Task log message for skipping a BW process chain execution
bwProcessChainSkipped=BW proceso grandinė „{0}“ praleista dėl naujų duomenų neprieinamumo arba ankstesnio vykdymo klaidos.
#XMSG: Task log message for failure in starting a BW process chain
startBwProcessChainFailure=BW proceso grandinės „{0}“ nepavyko pradėti SAP BW tilto kliente.
#XMSG: Task log message for failure in getting the status of a BW process chain
getBwProcessChainStatusFailure=Užduotis neįvykdyta, nes nepavyko gauti BW procesų grandinės „{0}“ būsenos dėl problemos užmezgant ryšį su SAP BW tilto. Atidarykite programą „Ryšiai“ ir patvirtinkite SAP BW tilto ryšį „{1}“ erdvėje. Jei neturite prieigos prie „Ryšių“ programos, kreipkitės į administratorių.
#XMSG: Task log message for failure in completing a BW process chain
completeBwProcessChainFailure=BW proceso grandinės „{0}“ nepavyko užbaigti SAP BW tilto kliente.
#XMSG: Task log message for the clickable text of the SAP BW Bridge Monitor link
bwBridgeMonitorLinkText=Peržiūrėti išsamią informaciją SAP BW tilto monitoriuje
#XMSG warning message while retrieving the task log messages if the user does not have access to the SAP BW Bridge space or to the shared object's space
missingPrivilegeOnSpaceError=Nuskaitant pranešimus iškilo problema arba neturite reikiamo leidimo juos peržiūrėti.

#Messages for API task log of the Data Integration Monitor
#XMSG: Task log message for displaying the API response
viewResponse=Gautas „{0}“ atsakymas.
#XMSG: Task log message for empty ID in the API response to retrieve the status
emptyJobId=JSON kelio atsakymo tekste nepateikta ID: „{0}“.
#XMSG: Task log message for empty success value in the API response
emptySuccessValue=JSON kelio atsakymo tekste nepateikta sėkmingumo reikšmės: „{0}“.
#XMSG: Task log message for empty error value in the API response
emptyErrorValue=JSON kelio atsakymo tekste nepateikta klaidos reikšmės: „{0}“.
#XMSG: Task log message for none of the success/error conditions matching for the values in the API response
conditionsNotFulfilled=Nė viena nurodyta sėkmingumo arba klaidos indikatoriaus sąlyga neatitinka atsakymo reikšmių.
#XMSG: Task log message for the API response status being unsuccessful
statusNotOk=„{1}“ atsakyme pateiktas nesėkmingos būsenos kodas: {0}.
#XMSG: Task log message for the API response body being empty
emptyResponseBody=Atsakymo tekstas buvo tuščias.
#XMSG: Task log message for the API response location header being invalid
emptyLocationHeader=Atsakymo vietos antraštė buvo tuščia.
#XMSG: Task log message for failure in retrieving the status of the invoked API
getStatusFailure=Nepavyko gauti iškviestos API būsenos.
#XMSG: Task log message for failure in completing the API task
completionFailure=Nepavyko baigti API užduoties „{0}“.
#XMSG: Task log message for a successful API task completion
apiCompleted=API užduotis „{0}“ sėkmingai užbaigta.
#XMSG: Task log message for an API task with an invalid configuration
invalidConfiguration=API užduoties „{0}“ konfigūracija netinkama. Patikrinkite konfigūraciją ir bandykite dar kartą.
#XMSG: Task log message for the API task being canceled
cancelStart=Paprašyta atšaukti API užduotį „{0}“ su žurnalo ID {1}.
#XMSG: Task log message for the cancelation of the an API task which is no longer running and cannot be canceled
unableToCancel=API užduotis „{0}“ su žurnalo ID {1} nebevykdoma ir jos atšaukti negalima.
#XMSG: Task log message when starting an API task. It should carry the name of the API task.
apiRunStart=Pradedama API užduotis „{0}“.
#XMSG: Task log message when the task preparation is taking too long and times out
preparationTimeout=Užduoties paruošimas užtruko per ilgai ir baigėsi skirtasis laikas.
#XMSG: Task log message when a task failed because a cancel task was triggered
apiRunCancelled=Vykdymo užduotį su žurnalo ID {0} atšaukė atšaukimo užduotis {1}.
#XMSG: Task log message when a cancel task failed to cancel a run task
cancelFailure=Atšaukimo užduočiai su žurnalo ID {1} nepavyko atšaukti vykdymo užduoties su žurnalo ID {0}.
#XMSG: Task log message when configuration of the API task is too large
apiTaskconfigurationSizeLimitError=API užduoties {0} negalima pradėti, nes jos užduoties konfigūracijos dydis viršija maksimalų leidžiamą dydį – 100 kibibaitų (KiB).
#XMSG: Task log message for failure in completing the Notification task
notificationTaskCompletionFailure=Nepavyko baigti pranešimo užduoties „{0}“.
#XMSG: Task log message when starting a Notification task.
notificationTaskRunStart=Pranešimo užduotis „{0}“ pradedama.
#XMSG: Task log message for a successful Notification task completion
notificationTaskCompletionSuccess=Baigta pranešimo užduotis „{0}“.
#XFLD: Label for frequency column
everyLabel=Kas
#XFLD: Plural Recurrence text for Hour
hoursLabel=Valandos
#XFLD: Plural Recurrence text for Day
daysLabel=Dienos
#XFLD: Plural Recurrence text for Month
monthsLabel=Mėnesiai
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minutės
