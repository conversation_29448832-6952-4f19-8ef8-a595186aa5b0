

#XFLD: Task Chain header
headerTxt=Тапсырмалар тізбектері ({0})
#XFLD: Task Chain header
headerTxtNew=Тапсырмалар тізбектері ({0})
#XTXT: Text for Schedule label
scheduleTxt=Кесте
#XFLD: Text for Create schedule button
createScheduleTxt=Кесте жасау
#XFLD: Text for edit schedule
editScheduleTxt=Кестені өңдеу
#XLFD: Text for delete schedule
deleteScheduleTxt=Кестені жою
#XTXT: text for refresh button label
refrestTxt=Жаңарту
#XTXT: Text for Completed status
completedTxt=Орындалды
#XTX: Text for Running status
runningTxt=Орындалуда
#XTX: Text for failed status
failedTxt=Сәтсіз аяқталды
#XTX: Text for stopped status
stoppedTxt=Тоқтатылды
#XTX: Text for stopping status
stoppingTxt=Тоқтатылуда
#XFLD: Header for Chain name
chainNameLabel=Тапсырмалар тізбегінің атауы
#XFLD: Header for Chain name
chainNameLabelBus=Бизнес атау
#XFLD: Header for Chain name
chainNameLabelBusNew=Нысан (бизнес атау)
#XFLD: Header for Chain name
chainNameLabelTech=Техникалық атау
#XFLD: Header for Chain name
chainNameLabelTechNew=Нысан (техникалық атау)
#XFLD: Last Run Status label
lastRunStatuslabel=Соңғы сеанс күйі
#XFLD: Last Run Status label
lastRunStatuslabelNew=Күйі
#XFLD: Frequency Label
frequencyLabel=Жиілік
#XFLD: Frequency Label
frequencyLabelNew=Жоспарланған жиілік
#XFLD: Duration label
durationLabel=Ұзақтық
#XFLD: Duration label
durationLabelNew=Соңғы сеанс ұзақтығы
#XFLD: Run Start label
runStartLabel=Соңғы сеанстың басталуы
#XFLD: Run end label
runEndLabel=Соңғы сеанстың аяқталуы
#XFLD: Next Run label
nextRunlabel=Келесі сеанс
#XFLD: Next Run label
nextRunlabelNew=Келесі жоспарланған сеанс
#XTXT: Task chain logs link tooltip text
taskChainLogsTxt=Тапсырмалар тізбегі журналының мәліметтері
#XTXT: Task chain logs link tooltip text
taskChainLogsTxtNew=Мәліметтер
#XTXT: Scheduled text
scheduledTxt=Жоспарланды
#XTXT: Paused text
pausedTxt=Кідіртілді
#XTXT: Execute button label
runLabel=Орындау
#XTXT: Execute button label
runLabelNew=Сеансты бастау
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Тапсырмалар тізбегін орындау
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Тапсырмалар тізбегін орындау басталды.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg={0} үшін тапсырмалар тізбегін орындау басталды
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Тапсырмалар тізбегін орындау сәтсіз аяқталды.
#XFLD: Label for schedule owner column
txtScheduleOwner=Кестеге жауапты тұлға
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Кестені жасаған адамды көрсетеді
#XMSG: Task log message for start chain
startChain=Тапсырмалар тізбегін орындау басталуда.
#XMSG: Task log message for load chain from repository
loadChainFromRepository=Тапсырмалар тізбегі жүктелді және бапталды.
#XMSG: Task log message started task
taskStarted={0} тапсырмасы басталды.
#XMSG: Task log message for finished task
taskFinished={0} тапсырмасы {1} күйімен аяқталды.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Осы тізбектің бөлігі болып табылатын жалпы {0} тапсырмасын орындау үшін тапсырмалар тізбегі жүктелуде және дайындалуда.
#XMSG: Task log message for starting a subtask
chainStartSubtask=Орындау үшін {0} тапсырмасы іске қосылуда. Тапсырма идентификаторы: {1}.
#XMSG: Task log message for finishing a subtask
chainFinishSubtask={0} тапсырмасы {1} күйімен аяқталды.
#XMSG: Task log message for indicating chain success
chainCompleted=Барлық {0} тапсырма орындалды. Тапсырмалар тізбегінің күйі "Орындалды" деп орнатылды.
#XMSG: Task log message for indicating chain failure
chainFailed=Барлығы {0} тапсырма бойынша {1} тапсырма орындалды, ал {2} тапсырма орындалмады. Тапсырмалар тізбегі күйі "Орындалмады" деп орнатылды.
#XMSG: Task log message for indicating chain cancelation
chainCanceled=Тапсырмалар тізбегін орындаудан бас тарту басталмады. Тапсырмадан бас тарту журналының идентификаторы — {0}.
#XMSG: Task log message for indicating chain cancelation in the cancel task
chainCanceling={0} тізбегінен бас тартылуда.
#XMSG: Task log message for general chain runtime error
chainError=Тапсырмалар тізбегін орындау кезінде күтілмеген қате орын алды.
#XMSG: Task log message for starting the overwrite check.
chainOverwriteStart={0} идентификаторына ие тапсырмалар тізбегінің аяқталған не аяқталмағаны тексерілуде.
#XMSG: Task log message indicating the chain is too old.
chainOverwriteTooOld={0} тізбегі үшін орындалатын тапсырмаларды табу мүмкін болмады. Сеанс {1} минут бұрын басталды. Тапсырмалар тізбегінің күйі "Орындалмады" деп орнатылды.
#XMSG: Task log message indicating the chain was created too recently.
chainOverwriteTooRecent={0} тізбегі үшін орындалатын тапсырмаларды табу мүмкін болмады. Сеанс {1} минут бұрын басталды. Тапсырмалар тізбегі әлі орындалуда.
#XMSG: Task log message indicating a recent heartbeat was found.
chainOverwriteHeartbeatAlive={0} тапсырмалар тізбегі әлі орындалуда. Аяқталды: {1}, Орындалуда: {2}, Сәтсіз аяқталды: {3}, Іске қосылды: {4}, Іске қосылмады: {5}.
#XMSG: Task log message for a chain that is not running anymore but not everything was triggered.
chainOverwriteNotAllTriggered={0} тапсырмалар тізбегін орындау кезінде қате орын алды және барлық тапсырмаларды бірдей іске қосу мүмкін болмады. Аяқталды: {1}, Орындалуда: {2}, Сәтсіз аяқталды: {3}, Іске қосылды: {4}, Іске қосылмады: {5}. Осы тапсырмалар тізбегі қайта анықталды.
#XMSG: Task log message for a chain that contains a failed subtask.
chainOverwriteAnySubtaskFailed={0} тапсырмалар тізбегі аяқталды. Тізбектегі бір тапсырма сәтсіз аяқталды. Тізбек "Орындалмады" күйіне орнатылуда.
#XMSG: Task log message could not determine status.
chainOverwriteNotDetermined=Күтілмеген қате тапсырма күйіне қатынасуға тыйым салуда. Қайталап көріңіз және мәселе шешілмесе, SAP қолдау көрсету қызметіне хабарласыңыз.
#XMSG: Task log message could not take over
failedTakeover=Бар тапсырманы қабылдау мүмкін болмады.
#XMSG: Task log parallel check error
parallelCheckError=Тапсырманы өңдеу мүмкін емес, себебі басқа тапсырма орындалып жатыр және ол осы тапсырманы блоктап жатыр.
#XMSG: Task log parallel task runnig error
parallelTaskRunning=Қайшылықты тапсырма әлдеқашан орындалуда.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus={1} корреляция идентификаторымен орындау барысында {0} күйі орнатылды.
#XMSG: Task log message successful takeover
successTakeover=Қалған құлыпты босату керек. Бұл тапсырма үшін жаңа құлып орнатылды.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Бұл тапсырма құлпы басқа тапсырмаға ауысты.
#XMSG: Schedule created alert message
createScheduleSuccess=Кесте жасалды
#XMSG: Task log message for taskFinishedAt
taskFinishedAt={0} тапсырмасы {2} кезінде {1}күйімен аяқталды.
#XMSG: Placeholder symbol for empty columns
emptyCol=--
#XMSG: Paused Text
txtExpired=Мерзімі өткен
#XMSG: Update task schedule success Toast message
updateScheduleSuccess=Кесте жаңартылды
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Кесте жойылды.
#XMSG: Task chain might have children error text
taskChainNoChildErrorText=Тапсырмалар тізімінде көрсетілмейтін туынды тапсырмалар тізімі болуы мүмкін, себебі жоспар жасалғанға дейін тапсырманы орындау сәтсіз аяқталды.
#XMSG: Task chain repair recent failed run label
retryRunLabel=Соңғы сеансты қайта орындау
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Тапсырмалар тізбегін қайта орындау сеансы басталды
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Тапсырмалар тізбегін қайта орындау сеансы сәтсіз аяқталды
#XMSG: chain repair message
chainRetried=Тапсырмалар тізімін қайта орындау сеансын {0} деген пайдаланушы іске қосты
#XMSG: chain not found during run
chainNotFoundDuringRun={0} тапсырмалар тізбегін табу мүмкін болмады. Оның бар-жоғын дерек құрастырғыш арқылы тексеріңіз және тізбектің қолданысқа енгізілгеніне көз жеткізіңіз.
#XMSG: chain is not DAG
chainNotDag={0} тапсырмалар тізбегін бастау мүмкін емес. Оның құрылымы жарамсыз. Дерек құрастырғыштағы тапсырмалар тізбегін тексеріңіз.
#XMSG: chain has not valid parameters
notValidParameters={0} тапсырмалар тізбегін бастау мүмкін емес. Оның бір немесе бірнеше параметрі жарамсыз. Дерек құрастырғыштағы тапсырмалар тізбегін тексеріңіз.
#XMSG: Configuration parameter value size exceeds the maximum allowed limit
configurationSizeLimitError={0} тапсырмалар тізбегін бастау мүмкін емес. Тізбектегі тапсырма конфигурациясының өлшемі рұқсат етілген максималды 100 кибибайт (КиБ) өлшемінен асады.
#XMSG: Task {0} has input parameters
taskHasInputParameters={0} тапсырмасының кіріс параметрлері бар.
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Қайшылықты тапсырма әлдеқашан орындалуда
#XMSG: error message for reading data from backend
txtReadBackendError=Бэкэндтен оқу кезінде қате орын алған сияқты.
##XMSG: error message for admission control rejection
admissionControlError=SAP HANA рұқсаттарды бақылаудан бас тартуына байланысты тапсырманы орындау сәтсіз аяқталды.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Кестені маған тағайындау
#XBUT: Pause schedule menu label
pauseScheduleLabel=Кестені кідірту
#XBUT: Resume schedule menu label
resumeScheduleLabel=Кестені жалғастыру
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Кестелерді жою кезінде қате орын алды.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Кестелерді тағайындау кезінде қате орын алды.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Кестелерді кідірту кезінде қате орын алды.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Кестелерді жалғастыру кезінде қате орын алды.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted={0} кесте жойылуда
#XMSG: Message for starting mass assign of schedules
massAssignStarted={0} кесте иесі жойылуда
#XMSG: Message for starting mass pausing of schedules
massPauseStarted={0} кесте кідіртілуде
#XMSG: Message for starting mass resuming of schedules
massResumeStarted={0} кесте жалғастырылуда
#XBUT: Select Columns Button
selectColumnsBtn=Бағандарды таңдау
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Жоспар жасалмай тұрып, алдыңғы тапсырманы орындау сәтсіз аяқталатындықтан, соңғы сеансты қайталау кезінде қате орын алған сияқты.
#XFLD: Refresh tooltip
TEXT_REFRESH=Жаңарту
#XFLD: Select Columns tooltip
text_selectColumns=Бағандарды таңдау

#Messages for BW Process Chain task log of the Data Integration Monitor
#XMSG: Task log message for successfully starting a BW process chain
startBwProcessChainSuccess="{0}" BW процестер тізбегі SAP BW Bridge тенантында сәтті басталды.
#XMSG: Task log message for skipping a BW process chain execution
bwProcessChainSkipped="{0}" BW процестер тізбегі өткізіп жіберілді, себебі жаңа деректер қолжетімсіз немесе алдыңғы орындау сәтсіз аяқталды.
#XMSG: Task log message for failure in starting a BW process chain
startBwProcessChainFailure="{0}" BW процестер тізбегін SAP BW Bridge тенантында бастау сәтсіз аяқталды.
#XMSG: Task log message for failure in getting the status of a BW process chain
getBwProcessChainStatusFailure=Тапсырма орындалмады, себебі SAP BW Bridge қызметімен байланыс орнату ақауына байланысты "{0}" BW процестер тізбегінің күйін алу мүмкін болмады. "Қосылымдар" қолданбасын ашыңыз және "{1}" кеңістігінде SAP BW Bridge қосылымын растаңыз. "Қосылымдар" қолданбасына кіру мүмкіндігіңіз болмаса, әкімшіңізге хабарласыңыз.
#XMSG: Task log message for failure in completing a BW process chain
completeBwProcessChainFailure="{0}" BW процестер тізбегін SAP BW Bridge тенантында орындау сәтсіз аяқталды.
#XMSG: Task log message for the clickable text of the SAP BW Bridge Monitor link
bwBridgeMonitorLinkText=Мәліметтерді SAP BW Bridge мониторында көру
#XMSG warning message while retrieving the task log messages if the user does not have access to the SAP BW Bridge space or to the shared object's space
missingPrivilegeOnSpaceError=Хабарларды шығарып алу кезінде мәселе орын алды немесе сізде оларды көруге қажетті рұқсатыңыз жоқ.

#Messages for API task log of the Data Integration Monitor
#XMSG: Task log message for displaying the API response
viewResponse="{0}" үшін жауап алынды.
#XMSG: Task log message for empty ID in the API response to retrieve the status
emptyJobId=JSON жолына арналған жауап мәтінінде ешқандай идентификатор қайтарылмады: "{0}".
#XMSG: Task log message for empty success value in the API response
emptySuccessValue=JSON жолына арналған жауап мәтінінде ешқандай сәттілік мәні қайтарылмады: "{0}".
#XMSG: Task log message for empty error value in the API response
emptyErrorValue=JSON жолына арналған жауап мәтінінде ешқандай қате мәні қайтарылмады: "{0}".
#XMSG: Task log message for none of the success/error conditions matching for the values in the API response
conditionsNotFulfilled=Көрсетілген сәттілік немесе қате көрсеткіші шарттарының ешқайсысы жауаптағы мәндерге сәйкес келмейді.
#XMSG: Task log message for the API response status being unsuccessful
statusNotOk="{1}" үшін жауап сәтсіз күй кодын қайтарды: {0}.
#XMSG: Task log message for the API response body being empty
emptyResponseBody=Жауап мәтіні бос болды.
#XMSG: Task log message for the API response location header being invalid
emptyLocationHeader=Жауаптағы орналасқан жер тақырыбы бос болды.
#XMSG: Task log message for failure in retrieving the status of the invoked API
getStatusFailure=Шақырылған API күйін шығарып алу мүмкін болмады.
#XMSG: Task log message for failure in completing the API task
completionFailure="{0}" API тапсырмасын орындау сәтсіз аяқталды.
#XMSG: Task log message for a successful API task completion
apiCompleted="{0}" API тапсырмасын орындау сәтті аяқталды.
#XMSG: Task log message for an API task with an invalid configuration
invalidConfiguration="{0}" API тапсырмасының конфигурациясы жарамсыз. Конфигурацияны тексеріп, әрекетті қайталаңыз.
#XMSG: Task log message for the API task being canceled
cancelStart="{0}" API тапсырмасын ({1} журнал ид.) болдырмау сұралды.
#XMSG: Task log message for the cancelation of the an API task which is no longer running and cannot be canceled
unableToCancel="{0}" API тапсырмасы ({1} журнал ид.) ендігәрі орындалмайды және оны болдырмау мүмкін емес.
#XMSG: Task log message when starting an API task. It should carry the name of the API task.
apiRunStart="{0}" API тапсырмасы басталуда.
#XMSG: Task log message when the task preparation is taking too long and times out
preparationTimeout=Тапсырманы дайындау тым көп уақытты алады және оның мерзімі аяқталды.
#XMSG: Task log message when a task failed because a cancel task was triggered
apiRunCancelled=Орындау тапсырмасы ({0} журнал ид.) {1} болдырмау тапсырмасымен болдырылмады.
#XMSG: Task log message when a cancel task failed to cancel a run task
cancelFailure=Болдырмау тапсырмасының ({1} журнал ид.) орындау тапсырмасын ({0} журнал ид.) болдырмауы сәтсіз аяқталды.
#XMSG: Task log message when configuration of the API task is too large
apiTaskconfigurationSizeLimitError={0} API тапсырмасын бастау мүмкін емес, себебі оның тапсырма конфигурациясының өлшемі рұқсат етілген максималды 100 кибибайт (КиБ) өлшемінен асады.
#XMSG: Task log message for failure in completing the Notification task
notificationTaskCompletionFailure="{0}" хабарландыру тапсырмасын орындау сәтсіз аяқталды.
#XMSG: Task log message when starting a Notification task.
notificationTaskRunStart="{0}" хабарландыру тапсырмасын бастау.
#XMSG: Task log message for a successful Notification task completion
notificationTaskCompletionSuccess="{0}" хабарландыру тапсырмасы сәтті аяқталды.
#XFLD: Label for frequency column
everyLabel=Әр
#XFLD: Plural Recurrence text for Hour
hoursLabel=Сағат
#XFLD: Plural Recurrence text for Day
daysLabel=Күн
#XFLD: Plural Recurrence text for Month
monthsLabel=Ай
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Минут
