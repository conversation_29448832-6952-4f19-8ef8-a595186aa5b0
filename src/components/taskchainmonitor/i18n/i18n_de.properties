

#XFLD: Task Chain header
headerTxt=Aufgabenketten ({0})
#XFLD: Task Chain header
headerTxtNew=Aufgabenketten ({0})
#XTXT: Text for Schedule label
scheduleTxt=Zeitplan
#XFLD: Text for Create schedule button
createScheduleTxt=Zeitplan anlegen
#XFLD: Text for edit schedule
editScheduleTxt=Zeitplan bearbeiten
#XLFD: Text for delete schedule
deleteScheduleTxt=Zeitplan löschen
#XTXT: text for refresh button label
refrestTxt=Aktualisieren
#XTXT: Text for Completed status
completedTxt=Abgeschlossen
#XTX: Text for Running status
runningTxt=Wird ausgeführt
#XTX: Text for failed status
failedTxt=Fehlgeschlagen
#XTX: Text for stopped status
stoppedTxt=Gestoppt
#XTX: Text for stopping status
stoppingTxt=Wird gestoppt
#XFLD: Header for Chain name
chainNameLabel=Name der Aufgabenkette
#XFLD: Header for Chain name
chainNameLabelBus=Betriebswirtschaftlicher Name
#XFLD: Header for Chain name
chainNameLabelBusNew=Objekt (betriebswirtschaftlicher Name)
#XFLD: Header for Chain name
chainNameLabelTech=Technischer Name
#XFLD: Header for Chain name
chainNameLabelTechNew=Objekt (technischer Name)
#XFLD: Last Run Status label
lastRunStatuslabel=Status des letzten Laufs
#XFLD: Last Run Status label
lastRunStatuslabelNew=Status
#XFLD: Frequency Label
frequencyLabel=Häufigkeit
#XFLD: Frequency Label
frequencyLabelNew=Eingeplante Häufigkeit
#XFLD: Duration label
durationLabel=Dauer
#XFLD: Duration label
durationLabelNew=Dauer des letzten Laufs
#XFLD: Run Start label
runStartLabel=Start des letzten Laufs
#XFLD: Run end label
runEndLabel=Ende des letzten Laufs
#XFLD: Next Run label
nextRunlabel=Nächster Lauf
#XFLD: Next Run label
nextRunlabelNew=Nächster eingeplanter Lauf
#XTXT: Task chain logs link tooltip text
taskChainLogsTxt=Protokolldetails der Aufgabenkette
#XTXT: Task chain logs link tooltip text
taskChainLogsTxtNew=Details
#XTXT: Scheduled text
scheduledTxt=Eingeplant
#XTXT: Paused text
pausedTxt=Pausiert
#XTXT: Execute button label
runLabel=Ausführen
#XTXT: Execute button label
runLabelNew=Lauf starten
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Aufgabenkette ausführen
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Ausführung der Aufgabenkette wurde gestartet.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Ausführung der Aufgabenkette wurde für {0} gestartet.
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Aufgabenkette konnte nicht ausgeführt werden.
#XFLD: Label for schedule owner column
txtScheduleOwner=Eigentümer des Zeitplans
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Zeigt an, wer den Zeitplan angelegt hat.
#XMSG: Task log message for start chain
startChain=Ausführung der Aufgabenkette wird gestartet.
#XMSG: Task log message for load chain from repository
loadChainFromRepository=Aufgabenkette wurde geladen und initialisiert.
#XMSG: Task log message started task
taskStarted=Aufgabe {0} wurde gestartet.
#XMSG: Task log message for finished task
taskFinished=Die Aufgabe {0} endete mit Status {1}.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Aufgabenkette wird geladen und Ausführung von insgesamt {0} Aufgaben, die sich in dieser Kette befinden, wird vorbereitet.
#XMSG: Task log message for starting a subtask
chainStartSubtask=Ausführung von Aufgabe {0} wird angestoßen. Aufgaben-ID = {1}.
#XMSG: Task log message for finishing a subtask
chainFinishSubtask=Aufgabe {0} wurde mit dem Status {1} abgeschlossen.
#XMSG: Task log message for indicating chain success
chainCompleted=Alle {0} Aufgaben wurden abgeschlossen. Der Status der Aufgabenkette wird auf "abgeschlossen" gesetzt.
#XMSG: Task log message for indicating chain failure
chainFailed=Von insgesamt {0} Aufgaben konnten {1} Aufgaben abgeschlossen werden und {2} Aufgaben sind fehlgeschlagen. Der Status der Aufgabenkette wird auf "fehlgeschlagen" gesetzt.
#XMSG: Task log message for indicating chain cancelation
chainCanceled=Abbruch des Aufgabenkettenlaufs gestartet. Protokoll-ID des Abbruchs der Aufgabe lautet {0}.
#XMSG: Task log message for indicating chain cancelation in the cancel task
chainCanceling=Kette {0} wird abgebrochen.
#XMSG: Task log message for general chain runtime error
chainError=Beim Ausführen der Aufgabenkette ist ein unerwarteter Fehler aufgetreten.
#XMSG: Task log message for starting the overwrite check.
chainOverwriteStart=Überprüfung, ob die Aufgabenkette mit der ID {0} abgeschlossen wurde.
#XMSG: Task log message indicating the chain is too old.
chainOverwriteTooOld=Es wurden keine Aufgaben gefunden, die für die Kette {0} auszuführen sind. Der Lauf ist {1} Minuten alt. Der Status der Aufgabenkette wird auf "fehlgeschlagen" gesetzt.
#XMSG: Task log message indicating the chain was created too recently.
chainOverwriteTooRecent=Es wurden keine Aufgaben gefunden, die für die Kette {0} auszuführen sind. Der Lauf ist {1} Minuten alt. Die Aufgabenkette wird noch ausgeführt.
#XMSG: Task log message indicating a recent heartbeat was found.
chainOverwriteHeartbeatAlive=Die Aufgabenkette {0} wird noch ausgeführt. Abgeschlossen: {1}. Derzeit ausgeführt: {2}. Fehlgeschlagen: {3}. Angestoßen: {4}. Nicht angestoßen: {5}.
#XMSG: Task log message for a chain that is not running anymore but not everything was triggered.
chainOverwriteNotAllTriggered=Beim Ausführen der Aufgabenkette {0} ist ein Fehler aufgetreten und nicht alle Aufgaben konnten angestoßen werden. Abgeschlossen: {1}. Derzeit ausgeführt: {2}. Fehlgeschlagen: {3}. Angestoßen: {4}. Nicht angestoßen: {5}. Diese Aufgabenkette wurde überschrieben.
#XMSG: Task log message for a chain that contains a failed subtask.
chainOverwriteAnySubtaskFailed=Die Aufgabenkette {0} wurde abgeschlossen. Eine Aufgabe der Kette ist fehlgeschlagen, die Kette wird auf "fehlgeschlagen" gesetzt.
#XMSG: Task log message could not determine status.
chainOverwriteNotDetermined=Ein unerwarteter Fehler verhindert den Zugriff auf den Status der Aufgabe. Versuchen Sie es erneut, und wenden Sie sich an den SAP Support, wenn der Fehler weiterbesteht.
#XMSG: Task log message could not take over
failedTakeover=Vorhandene Aufgabe konnte nicht übernommen werden.
#XMSG: Task log parallel check error
parallelCheckError=Die Aufgabe kann nicht verarbeitet werden, da bereits eine andere Aufgabe ausgeführt wird, die diese Aufgabe blockiert.
#XMSG: Task log parallel task runnig error
parallelTaskRunning=Es wird bereits eine in Konflikt stehende Aufgabe ausgeführt.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Status {0} während Lauf mit Korrelations-ID {1}.
#XMSG: Task log message successful takeover
successTakeover=Die übriggebliebene Sperre musste aufgehoben werden. Die neue Sperre für diese Aufgabe wurde festgelegt.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Die Sperre der Aufgabe wurde durch eine andere Aufgabe übernommen.
#XMSG: Schedule created alert message
createScheduleSuccess=Zeitplan wurde angelegt
#XMSG: Task log message for taskFinishedAt
taskFinishedAt=Aufgabe {0} wurde um {2} mit dem Status {1} abgeschlossen.
#XMSG: Placeholder symbol for empty columns
emptyCol=--
#XMSG: Paused Text
txtExpired=Abgelaufen
#XMSG: Update task schedule success Toast message
updateScheduleSuccess=Zeitplan wurde aktualisiert
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Zeitplan wurde gelöscht.
#XMSG: Task chain might have children error text
taskChainNoChildErrorText=Die Aufgabenkette kann untergeordnete Elemente aufweisen, die nicht angezeigt werden, da die Aufgabe fehlgeschlagen ist, bevor der Plan generiert werden konnte.
#XMSG: Task chain repair recent failed run label
retryRunLabel=Letzten Lauf wiederholen
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Ausführung der Wiederholung der Aufgabenkette wurde gestartet.
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Ausführung der Wiederholung der Aufgabenkette ist fehlgeschlagen.
#XMSG: chain repair message
chainRetried=Ausführung der Wiederholung der Aufgabenkette wurde durch Benutzer {0} ausgelöst.
#XMSG: chain not found during run
chainNotFoundDuringRun=Aufgabenkette {0} konnte nicht gefunden werden. Prüfen Sie über den Data Builder, ob sie vorhanden ist und stellen Sie sicher, dass die Kette aktiviert wurde.
#XMSG: chain is not DAG
chainNotDag=Aufgabenkette {0} kann nicht gestartet werden. Ihre Struktur ist ungültig. Prüfen Sie die Aufgabenkette im Data Builder.
#XMSG: chain has not valid parameters
notValidParameters=Aufgabenkette {0} kann nicht gestartet werden. Mindestens einer ihrer Parameter ist ungültig. Prüfen Sie die Aufgabenkette im Data Builder.
#XMSG: Configuration parameter value size exceeds the maximum allowed limit
configurationSizeLimitError=Aufgabenkette {0} kann nicht gestartet werden. Die Größe der Konfiguration einer Aufgabe in der Kette überschreitet die maximal zulässige Größe von 100 Kibibyte (KiB).
#XMSG: Task {0} has input parameters
taskHasInputParameters=Aufgabe {0} weist Eingabeparameter auf.
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Es wird bereits eine in Konflikt stehende Aufgabe ausgeführt
#XMSG: error message for reading data from backend
txtReadBackendError=Offenbar ist beim Lesen des Backend ein Fehler aufgetreten.
##XMSG: error message for admission control rejection
admissionControlError=Die Aufgabe ist aufgrund einer Ablehnung der SAP-HANA-Zugangssteuerung fehlgeschlagen.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Zeitplan mir zuweisen
#XBUT: Pause schedule menu label
pauseScheduleLabel=Zeitplan pausieren
#XBUT: Resume schedule menu label
resumeScheduleLabel=Zeitplan fortsetzen
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Beim Entfernen von Zeitplänen ist ein Fehler aufgetreten.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Beim Zuweisen von Zeitplänen ist ein Fehler aufgetreten.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Beim Pausieren von Zeitplänen ist ein Fehler aufgetreten.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Beim Fortsetzten von Zeitplänen ist ein Fehler aufgetreten.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted={0} Zeitpläne werden gelöscht
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Eigentümer von {0} Zeitplänen wird geändert
#XMSG: Message for starting mass pausing of schedules
massPauseStarted={0} Zeitpläne werden pausiert
#XMSG: Message for starting mass resuming of schedules
massResumeStarted={0} Zeitpläne werden fortgesetzt
#XBUT: Select Columns Button
selectColumnsBtn=Spalten auswählen
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Offenbar ist bei der Wiederholung des letzten Laufs ein Fehler aufgetreten, da der Lauf der vorherigen Aufgabe fehlgeschlagen ist, bevor der Plan generiert werden konnte.
#XFLD: Refresh tooltip
TEXT_REFRESH=Aktualisieren
#XFLD: Select Columns tooltip
text_selectColumns=Spalten auswählen

#Messages for BW Process Chain task log of the Data Integration Monitor
#XMSG: Task log message for successfully starting a BW process chain
startBwProcessChainSuccess=Die BW-Prozesskette "{0}" wurde im SAP-BW-Bridge-Tenant gestartet.
#XMSG: Task log message for skipping a BW process chain execution
bwProcessChainSkipped=Die BW-Prozesskette "{0}" wurde übersprungen, weil entweder keine neuen Daten verfügbar sind oder weil eine vorhergehende Ausführung fehlgeschlagen ist.
#XMSG: Task log message for failure in starting a BW process chain
startBwProcessChainFailure=Die BW-Prozesskette "{0}" konnte im SAP-BW-Bridge-Tenant nicht gestartet werden.
#XMSG: Task log message for failure in getting the status of a BW process chain
getBwProcessChainStatusFailure=Die Aufgabe ist fehlgeschlagen, da der Status der BW-Prozesskette "{0}" aufgrund eines Problems beim Herstellen einer Verbindung zu SAP-BW-Bridge nicht abgerufen werden konnte. Öffnen Sie die App "Verbindungen", und überprüfen Sie die SAP-BW-Bridge-Verbindung im Space "{1}". Wenn Sie keinen Zugriff auf die App "Verbindungen" haben, wenden Sie sich an Ihren Administrator.
#XMSG: Task log message for failure in completing a BW process chain
completeBwProcessChainFailure=Die BW-Prozesskette "{0}" konnte im SAP-BW-Bridge-Tenant nicht abgeschlossen werden.
#XMSG: Task log message for the clickable text of the SAP BW Bridge Monitor link
bwBridgeMonitorLinkText=Details im SAP-BW-Bridge-Monitor anzeigen
#XMSG warning message while retrieving the task log messages if the user does not have access to the SAP BW Bridge space or to the shared object's space
missingPrivilegeOnSpaceError=Beim Abrufen der Nachrichten ist ein Fehler aufgetreten, oder Sie haben nicht die zum Anzeigen erforderliche Berechtigung.

#Messages for API task log of the Data Integration Monitor
#XMSG: Task log message for displaying the API response
viewResponse=Es wurde eine Antwort für "{0}" empfangen.
#XMSG: Task log message for empty ID in the API response to retrieve the status
emptyJobId=Es wurde keine ID im Antworttext für den JSON-Pfad zurückgegeben: "{0}".
#XMSG: Task log message for empty success value in the API response
emptySuccessValue=Es wurde kein Erfolgswert im Antworttext für den JSON-Pfad zurückgegeben: "{0}".
#XMSG: Task log message for empty error value in the API response
emptyErrorValue=Es wurde kein Fehlerwert im Antworttext für den JSON-Pfad zurückgegeben: "{0}".
#XMSG: Task log message for none of the success/error conditions matching for the values in the API response
conditionsNotFulfilled=Keine der angegebenen Erfolgs- oder Fehlerindikatorbedingungen stimmt mit den Werten in der Antwort überein.
#XMSG: Task log message for the API response status being unsuccessful
statusNotOk=Die Antwort "{1}" gab einen nicht erfolgreichen Statuscode zurück: {0}.
#XMSG: Task log message for the API response body being empty
emptyResponseBody=Der Antworttext war leer.
#XMSG: Task log message for the API response location header being invalid
emptyLocationHeader=Der Standort-Header in der Antwort war leer.
#XMSG: Task log message for failure in retrieving the status of the invoked API
getStatusFailure=Der Status der aufgerufenen API konnte nicht abgerufen werden.
#XMSG: Task log message for failure in completing the API task
completionFailure=Die API-Aufgabe "{0}" konnte nicht abgeschlossen werden.
#XMSG: Task log message for a successful API task completion
apiCompleted=Die API-Aufgabe "{0}" wurde erfolgreich abgeschlossen.
#XMSG: Task log message for an API task with an invalid configuration
invalidConfiguration=Die Konfiguration der API-Aufgabe "{0}" ist ungültig. Überprüfen Sie die Konfiguration und versuchen Sie es erneut.
#XMSG: Task log message for the API task being canceled
cancelStart=Abbruch der API-Aufgabe "{0}" mit logId {1} angefordert.
#XMSG: Task log message for the cancelation of the an API task which is no longer running and cannot be canceled
unableToCancel=Die API-Aufgabe "{0}" mit logId Y {1} wird nicht mehr ausgeführt und kann nicht abgebrochen werden.
#XMSG: Task log message when starting an API task. It should carry the name of the API task.
apiRunStart=API-Aufgabe "{0}" wird gestartet.
#XMSG: Task log message when the task preparation is taking too long and times out
preparationTimeout=Die Vorbereitung der Aufgabe nimmt zu viel Zeit in Anspruch und wurde aufgrund von Zeitüberschreitung abgebrochen.
#XMSG: Task log message when a task failed because a cancel task was triggered
apiRunCancelled=Die Aufgabe zum Ausführen mit logId {0} wurde durch die Aufgabe zum Abbrechen {1} abgebrochen.
#XMSG: Task log message when a cancel task failed to cancel a run task
cancelFailure=Die Aufgabe zum Abbrechen mit logId {1} konnte die Aufgabe zum Ausführen mit logId {0} nicht abbrechen.
#XMSG: Task log message when configuration of the API task is too large
apiTaskconfigurationSizeLimitError=Die Aufgabenkette {0} kann nicht gestartet werden, da die Größe ihrer Konfiguration die maximal zulässige Größe von 100 Kibibyte (KiB) überschreitet.
#XMSG: Task log message for failure in completing the Notification task
notificationTaskCompletionFailure=Die Benachrichtigungsaufgabe "{0}" konnte nicht abgeschlossen werden.
#XMSG: Task log message when starting a Notification task.
notificationTaskRunStart=Die Benachrichtigungsaufgabe "{0}" wird gestartet.
#XMSG: Task log message for a successful Notification task completion
notificationTaskCompletionSuccess=Die Benachrichtigungsaufgabe "{0}" wurde abgeschlossen.
#XFLD: Label for frequency column
everyLabel=Alle
#XFLD: Plural Recurrence text for Hour
hoursLabel=Stunden
#XFLD: Plural Recurrence text for Day
daysLabel=Tage
#XFLD: Plural Recurrence text for Month
monthsLabel=Monate
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minuten
