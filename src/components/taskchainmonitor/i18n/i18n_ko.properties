

#XFLD: Task Chain header
headerTxt=태스크 체인({0})
#XFLD: Task Chain header
headerTxtNew=태스크 체인({0})
#XTXT: Text for Schedule label
scheduleTxt=일정
#XFLD: Text for Create schedule button
createScheduleTxt=일정 생성
#XFLD: Text for edit schedule
editScheduleTxt=일정 편집
#XLFD: Text for delete schedule
deleteScheduleTxt=일정 삭제
#XTXT: text for refresh button label
refrestTxt=새로 고침
#XTXT: Text for Completed status
completedTxt=완료됨
#XTX: Text for Running status
runningTxt=실행 중
#XTX: Text for failed status
failedTxt=실패
#XTX: Text for stopped status
stoppedTxt=중지됨
#XTX: Text for stopping status
stoppingTxt=중지 중
#XFLD: Header for Chain name
chainNameLabel=태스크 체인 이름
#XFLD: Header for Chain name
chainNameLabelBus=업무 이름
#XFLD: Header for Chain name
chainNameLabelBusNew=오브젝트(업무 이름)
#XFLD: Header for Chain name
chainNameLabelTech=기술적 이름
#XFLD: Header for Chain name
chainNameLabelTechNew=오브젝트(기술적 이름)
#XFLD: Last Run Status label
lastRunStatuslabel=최종 실행 상태
#XFLD: Last Run Status label
lastRunStatuslabelNew=상태
#XFLD: Frequency Label
frequencyLabel=주기
#XFLD: Frequency Label
frequencyLabelNew=예정된 주기
#XFLD: Duration label
durationLabel=기간
#XFLD: Duration label
durationLabelNew=최종 실행 기간
#XFLD: Run Start label
runStartLabel=최종 실행 시작
#XFLD: Run end label
runEndLabel=최종 실행 종료
#XFLD: Next Run label
nextRunlabel=다음 실행
#XFLD: Next Run label
nextRunlabelNew=예정된 다음 실행
#XTXT: Task chain logs link tooltip text
taskChainLogsTxt=태스크 체인 로그 세부사항
#XTXT: Task chain logs link tooltip text
taskChainLogsTxtNew=세부사항
#XTXT: Scheduled text
scheduledTxt=예약됨
#XTXT: Paused text
pausedTxt=일시 중지됨
#XTXT: Execute button label
runLabel=실행
#XTXT: Execute button label
runLabelNew=실행 시작
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=태스크 체인 실행
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=태스크 체인 실행이 시작되었습니다.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg={0}에 대한 태스크 체인 실행이 시작되었습니다.
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=태스크 체인을 실행하지 못했습니다.
#XFLD: Label for schedule owner column
txtScheduleOwner=일정 소유자
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=일정을 생성한 사람 표시
#XMSG: Task log message for start chain
startChain=태스크 체인 실행을 시작하는 중입니다.
#XMSG: Task log message for load chain from repository
loadChainFromRepository=태스크 체인이 로드되고 초기화되었습니다.
#XMSG: Task log message started task
taskStarted=태스크 {0}이(가) 시작되었습니다.
#XMSG: Task log message for finished task
taskFinished=태스크 {0}이(가) {1} 상태로 종료되었습니다.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=태스크 체인을 로드하고 이 체인의 일부인 총 {0}개의 태스크 실행을 준비하고 있습니다.
#XMSG: Task log message for starting a subtask
chainStartSubtask=태스크 {0}의 실행을 트리거하고 있습니다. 태스크 ID는 {1}입니다.
#XMSG: Task log message for finishing a subtask
chainFinishSubtask=태스크 {0}이(가) {1} 상태로 종료되었습니다.
#XMSG: Task log message for indicating chain success
chainCompleted={0}개 태스크가 모두 완료되었습니다. 태스크 체인 상태는 완료됨으로 설정됩니다.
#XMSG: Task log message for indicating chain failure
chainFailed=총 {0}개의 태스크 중 {1}개 태스크는 완료할 수 있지만 {2}개 태스크는 실패했습니다. 태스크 체인 상태는 실패로 설정됩니다.
#XMSG: Task log message for indicating chain cancelation
chainCanceled=태스크 체인 실행 취소가 시작되었습니다. 취소 태스크 로그 ID가 {0}입니다.
#XMSG: Task log message for indicating chain cancelation in the cancel task
chainCanceling=체인 {0}을(를) 취소하는 중입니다.
#XMSG: Task log message for general chain runtime error
chainError=태스크 체인을 실행하는 동안 예기치 않은 오류가 발생했습니다.
#XMSG: Task log message for starting the overwrite check.
chainOverwriteStart=ID {0}의 태스크 체인이 종료되었는지 확인하고 있습니다.
#XMSG: Task log message indicating the chain is too old.
chainOverwriteTooOld=체인 {0}에 대해 실행할 태스크가 없습니다. 실행 시작 후 {1}분이 지났습니다. 태스크 체인 상태는 실패로 설정됩니다.
#XMSG: Task log message indicating the chain was created too recently.
chainOverwriteTooRecent=체인 {0}에 대해 실행할 태스크가 없습니다. 실행 시작 후 {1}분이 지났습니다. 태스크 체인이 아직 실행 중입니다.
#XMSG: Task log message indicating a recent heartbeat was found.
chainOverwriteHeartbeatAlive=태스크 체인 {0}이(가) 아직 실행 중입니다. 완료됨: {1}, 실행 중: {2}, 실패: {3}, 트리거됨: {4}, 트리거되지 않음: {5}
#XMSG: Task log message for a chain that is not running anymore but not everything was triggered.
chainOverwriteNotAllTriggered=태스크 체인 {0}을(를) 실행하는 동안 오류가 발생하여 일부 태스크를 트리거할 수 없습니다. 완료됨: {1}, 실행 중: {2}, 실패: {3}, 트리거됨: {4}, 트리거되지 않음: {5}. 이 태스크 체인을 덮어썼습니다.
#XMSG: Task log message for a chain that contains a failed subtask.
chainOverwriteAnySubtaskFailed=태스크 체인 {0}이(가) 종료되었습니다. 체인의 태스크 하나가 실패하여 체인을 실패로 설정합니다.
#XMSG: Task log message could not determine status.
chainOverwriteNotDetermined=예기치 않은 오류가 발생하여 태스크 상태에 액세스할 수 없습니다. 다시 시도하고 오류가 계속되는 경우 SAP 지원 팀에 문의하십시오.
#XMSG: Task log message could not take over
failedTakeover=기존 태스크를 가져오지 못했습니다.
#XMSG: Task log parallel check error
parallelCheckError=다른 태스크가 실행되고 있으며 이미 이 태스크를 보류하고 있으므로 이 태스크가 처리될 수 없습니다.
#XMSG: Task log parallel task runnig error
parallelTaskRunning=충돌 태스크가 이미 실행 중입니다.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=상관 관계 ID {1}(으)로 실행하는 동안 {0} 상태입니다.
#XMSG: Task log message successful takeover
successTakeover=남은 잠금을 릴리스해야 합니다. 이 태스크에 대한 신규 잠금이 설정되었습니다.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=이 태스크의 잠금이 다른 태스크에게 인수되었습니다.
#XMSG: Schedule created alert message
createScheduleSuccess=일정이 생성되었습니다.
#XMSG: Task log message for taskFinishedAt
taskFinishedAt=태스크 {0}이(가) {2}에 {1} 상태로 종료되었습니다.
#XMSG: Placeholder symbol for empty columns
emptyCol=--
#XMSG: Paused Text
txtExpired=만료됨
#XMSG: Update task schedule success Toast message
updateScheduleSuccess=일정이 업데이트되었습니다.
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=일정이 삭제되었습니다.
#XMSG: Task chain might have children error text
taskChainNoChildErrorText=계획이 생성되기 전에 태스크가 실패하여 표시되지 않은 하위 항목이 태스크 체인에 있을 수 있습니다.
#XMSG: Task chain repair recent failed run label
retryRunLabel=최근 실행 재시도
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=태스크 체인 재시도 실행이 시작되었습니다.
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=태스크 체인 재시도 실행이 실패했습니다.
#XMSG: chain repair message
chainRetried={0} 사용자가 태스크 체인 재시도를 트리거했습니다.
#XMSG: chain not found during run
chainNotFoundDuringRun={0} 태스크 체인이 없습니다. 데이터 빌더를 통해 태스크 체인이 존재하는지 확인하고 체인을 배포하십시오.
#XMSG: chain is not DAG
chainNotDag={0} 태스크 체인을 시작할 수 없습니다. 구조가 올바르지 않습니다. 데이터 빌더에서 태스크 체인을 확인하십시오.
#XMSG: chain has not valid parameters
notValidParameters={0} 태스크 체인을 시작할 수 없습니다. 해당 매개변수가 하나 이상 올바르지 않습니다. 데이터 빌더에서 태스크 체인을 확인하십시오.
#XMSG: Configuration parameter value size exceeds the maximum allowed limit
configurationSizeLimitError={0} 태스크 체인을 시작할 수 없습니다. 체인에서 태스크의 구성 크기가 최대 허용 크기인 100키비바이트(KiB)를 초과합니다.
#XMSG: Task {0} has input parameters
taskHasInputParameters={0} 태스크에 입력 매개변수가 있습니다.
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=충돌 태스크가 이미 실행 중입니다.
#XMSG: error message for reading data from backend
txtReadBackendError=백엔드에서 읽는 동안 오류가 발생한 것 같습니다.
##XMSG: error message for admission control rejection
admissionControlError='SAP HANA 허용 제어' 거부로 인해 태스크에 실패했습니다.

#XBUT: Assign schedule menu button label
assignScheduleLabel=나에게 일정 지정
#XBUT: Pause schedule menu label
pauseScheduleLabel=일정 일시 중지
#XBUT: Resume schedule menu label
resumeScheduleLabel=일정 다시 시작
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=일정을 제거하는 동안 오류가 발생했습니다.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=일정을 지정하는 동안 오류가 발생했습니다.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=일정을 일시 중지하는 동안 오류가 발생했습니다.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=일정을 다시 시작하는 동안 오류가 발생했습니다.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted={0}개의 일정을 삭제하는 중입니다.
#XMSG: Message for starting mass assign of schedules
massAssignStarted={0}개 일정의 소유자를 변경하는 중입니다.
#XMSG: Message for starting mass pausing of schedules
massPauseStarted={0}개의 일정을 일시 중지하는 중입니다.
#XMSG: Message for starting mass resuming of schedules
massResumeStarted={0}개의 일정을 다시 시작하는 중입니다.
#XBUT: Select Columns Button
selectColumnsBtn=열 선택
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=계획 생성 전에 이전 태스크 실행에 실패했으므로 최근 실행 재시도 시 오류가 발행한 것 같습니다.
#XFLD: Refresh tooltip
TEXT_REFRESH=새로 고침
#XFLD: Select Columns tooltip
text_selectColumns=열 선택

#Messages for BW Process Chain task log of the Data Integration Monitor
#XMSG: Task log message for successfully starting a BW process chain
startBwProcessChainSuccess=SAP BW 브리지 테넌트에서 BW 프로세스 체인 "{0}"이(가) 시작되었습니다.
#XMSG: Task log message for skipping a BW process chain execution
bwProcessChainSkipped=새로운 데이터를 사용할 수 없거나 이전 실행이 실패하여 BW 프로세스 체인 "{0}"이(가) 스킵되었습니다.
#XMSG: Task log message for failure in starting a BW process chain
startBwProcessChainFailure=SAP BW 브리지 테넌트에서 BW 프로세스 체인 "{0}"이(가) 시작되지 못했습니다.
#XMSG: Task log message for failure in getting the status of a BW process chain
getBwProcessChainStatusFailure=SAP BW 브리지에 대한 연결 설정 문제로 인해 BW 프로세스 체인 "{0}"의 상태를 검색할 수 없어서 태스크에 실패했습니다. "연결" 앱을 열고 "{1}" 공간에서 SAP BW 브리지 연결의 유효성을 확인합니다. "연결" 앱에 액세스할 수 없는 경우 관리자에게 문의하십시오.
#XMSG: Task log message for failure in completing a BW process chain
completeBwProcessChainFailure=SAP BW 브리지 테넌트에서 BW 프로세스 체인 "{0}"이(가) 완료되지 못했습니다.
#XMSG: Task log message for the clickable text of the SAP BW Bridge Monitor link
bwBridgeMonitorLinkText=SAP BW 브리지 모니터에서 세부사항 보기
#XMSG warning message while retrieving the task log messages if the user does not have access to the SAP BW Bridge space or to the shared object's space
missingPrivilegeOnSpaceError=메시지를 가져오는 동안 문제가 발생했거나 메시지를 보는 데 필요한 권한이 없습니다.

#Messages for API task log of the Data Integration Monitor
#XMSG: Task log message for displaying the API response
viewResponse="{0}"에 대해 응답이 수신되었습니다.
#XMSG: Task log message for empty ID in the API response to retrieve the status
emptyJobId=응답 본문에 JSON 경로 "{0}"에 대해 반환된 ID가 없습니다.
#XMSG: Task log message for empty success value in the API response
emptySuccessValue=응답 본문에 JSON 경로 "{0}"에 대해 반환된 성공 값이 없습니다.
#XMSG: Task log message for empty error value in the API response
emptyErrorValue=응답 본문에 JSON 경로 "{0}"에 대해 반환된 오류 값이 없습니다.
#XMSG: Task log message for none of the success/error conditions matching for the values in the API response
conditionsNotFulfilled=지정된 성공 또는 오류 지시자 조건 중에서 응답의 값과 일치하는 항목이 없습니다.
#XMSG: Task log message for the API response status being unsuccessful
statusNotOk="{1}"에 대한 응답에서 실패한 상태 코드 {0}이(가) 리턴되었습니다.
#XMSG: Task log message for the API response body being empty
emptyResponseBody=응답 본문이 비어 있습니다.
#XMSG: Task log message for the API response location header being invalid
emptyLocationHeader=응답의 위치 헤더가 비어 있습니다.
#XMSG: Task log message for failure in retrieving the status of the invoked API
getStatusFailure=호출된 API의 상태를 가져올 수 없습니다.
#XMSG: Task log message for failure in completing the API task
completionFailure=API 태스크 "{0}"을(를) 완료하지 못했습니다.
#XMSG: Task log message for a successful API task completion
apiCompleted=API 태스크 "{0}"이(가) 완료되었습니다.
#XMSG: Task log message for an API task with an invalid configuration
invalidConfiguration=API 태스크 "{0}"의 구성이 잘못되었습니다. 구성을 확인하고 다시 시도하십시오.
#XMSG: Task log message for the API task being canceled
cancelStart=logId {1}인 API 태스크 "{0}" 취소가 요청되었습니다.
#XMSG: Task log message for the cancelation of the an API task which is no longer running and cannot be canceled
unableToCancel=logId {1}인 API 태스크 "{0}"이(가) 더 이상 실행 중이지 않으며 취소될 수 없습니다. 
#XMSG: Task log message when starting an API task. It should carry the name of the API task.
apiRunStart=API 태스크 "{0}"을(를) 시작하는 중입니다.
#XMSG: Task log message when the task preparation is taking too long and times out
preparationTimeout=태스크 준비에 너무 오랜 시간이 소요되어 시간 초과가 발생했습니다.
#XMSG: Task log message when a task failed because a cancel task was triggered
apiRunCancelled=logId {0}인 실행 태스크가 취소 태스크 {1}에 의해 취소되었습니다.
#XMSG: Task log message when a cancel task failed to cancel a run task
cancelFailure=취소 태스크(logId {1})가 logId {0}(으)로 실행 태스크를 취소하지 못했습니다.
#XMSG: Task log message when configuration of the API task is too large
apiTaskconfigurationSizeLimitError=API 태스크 {0}은(는) 태스크 구성 크기가 최대 허용 크기인 100키비바이트(KiB)를 초과하기 때문에 시작할 수 없습니다.
#XMSG: Task log message for failure in completing the Notification task
notificationTaskCompletionFailure=통지 태스크 "{0}"을(를) 완료하지 못했습니다.
#XMSG: Task log message when starting a Notification task.
notificationTaskRunStart=통지 태스크 "{0}"을(를) 시작하고 있습니다.
#XMSG: Task log message for a successful Notification task completion
notificationTaskCompletionSuccess=통지 태스크 "{0}"이(가) 완료되었습니다.
#XFLD: Label for frequency column
everyLabel=매
#XFLD: Plural Recurrence text for Hour
hoursLabel=시간
#XFLD: Plural Recurrence text for Day
daysLabel=일
#XFLD: Plural Recurrence text for Month
monthsLabel=월
#XFLD: Plural Recurrence text for Minutes
minutesLabel=분
