

#XFLD: Task Chain header
headerTxt=任务链（{0}）
#XFLD: Task Chain header
headerTxtNew=任务链 （{0}）
#XTXT: Text for Schedule label
scheduleTxt=计划
#XFLD: Text for Create schedule button
createScheduleTxt=创建计划
#XFLD: Text for edit schedule
editScheduleTxt=编辑计划
#XLFD: Text for delete schedule
deleteScheduleTxt=删除计划
#XTXT: text for refresh button label
refrestTxt=刷新
#XTXT: Text for Completed status
completedTxt=已完成
#XTX: Text for Running status
runningTxt=运行中
#XTX: Text for failed status
failedTxt=失败
#XTX: Text for stopped status
stoppedTxt=已停止
#XTX: Text for stopping status
stoppingTxt=正在停止
#XFLD: Header for Chain name
chainNameLabel=任务链名称
#XFLD: Header for Chain name
chainNameLabelBus=业务名称
#XFLD: Header for Chain name
chainNameLabelBusNew=对象（业务名称）
#XFLD: Header for Chain name
chainNameLabelTech=技术名称
#XFLD: Header for Chain name
chainNameLabelTechNew=对象（技术名称）
#XFLD: Last Run Status label
lastRunStatuslabel=上次运行状态
#XFLD: Last Run Status label
lastRunStatuslabelNew=状态
#XFLD: Frequency Label
frequencyLabel=频率
#XFLD: Frequency Label
frequencyLabelNew=计划频率
#XFLD: Duration label
durationLabel=持续时间
#XFLD: Duration label
durationLabelNew=上次运行持续时间
#XFLD: Run Start label
runStartLabel=上次运行开始
#XFLD: Run end label
runEndLabel=上次运行结束
#XFLD: Next Run label
nextRunlabel=下次运行
#XFLD: Next Run label
nextRunlabelNew=计划下次运行
#XTXT: Task chain logs link tooltip text
taskChainLogsTxt=任务链日志详细信息
#XTXT: Task chain logs link tooltip text
taskChainLogsTxtNew=详细信息
#XTXT: Scheduled text
scheduledTxt=已计划
#XTXT: Paused text
pausedTxt=已暂停
#XTXT: Execute button label
runLabel=运行
#XTXT: Execute button label
runLabelNew=开始运行
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=运行任务链
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=任务链运行已开始。
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg={0} 的任务链运行已经开始。
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=无法运行任务链。
#XFLD: Label for schedule owner column
txtScheduleOwner=计划所有者
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=显示计划创建者
#XMSG: Task log message for start chain
startChain=正在开始运行任务链。
#XMSG: Task log message for load chain from repository
loadChainFromRepository=已加载并初始化任务链。
#XMSG: Task log message started task
taskStarted=任务{0}已开始。
#XMSG: Task log message for finished task
taskFinished=任务{0}已结束，状态为{1}。
#XMSG: Task log message for chain preparation
chainLoadFromRepository=正在加载任务链并准备运行此链中所含的全部 {0} 项任务。
#XMSG: Task log message for starting a subtask
chainStartSubtask=正在触发任务 {0} 运行。任务 ID = {1}。
#XMSG: Task log message for finishing a subtask
chainFinishSubtask=任务 {0} 已完成，状态为：{1}。
#XMSG: Task log message for indicating chain success
chainCompleted=全部 {0} 项任务已完成。任务链状态已设为已完成。
#XMSG: Task log message for indicating chain failure
chainFailed=在全部 {0} 项任务中，{1} 项任务可以完成，{2} 项任务失败。任务链状态已设为失败。
#XMSG: Task log message for indicating chain cancelation
chainCanceled=任务链运行取消已经开始。取消任务的日志 ID 为 {0}。
#XMSG: Task log message for indicating chain cancelation in the cancel task
chainCanceling=正在取消任务链 {0}。
#XMSG: Task log message for general chain runtime error
chainError=运行任务链时出现意外错误。
#XMSG: Task log message for starting the overwrite check.
chainOverwriteStart=正在检查 ID 为 {0} 的任务链是否已完成。
#XMSG: Task log message indicating the chain is too old.
chainOverwriteTooOld=无法找到链 {0} 的待运行任务。运行已经持续 {1} 分钟。任务链状态已设为失败。
#XMSG: Task log message indicating the chain was created too recently.
chainOverwriteTooRecent=无法找到链 {0} 的待运行任务。运行已经持续 {1} 分钟。任务链仍在运行。
#XMSG: Task log message indicating a recent heartbeat was found.
chainOverwriteHeartbeatAlive=任务链 {0} 仍在运行。已完成：{1}，运行中：{2}，失败：{3}，已触发：{4}，未触发：{5}。
#XMSG: Task log message for a chain that is not running anymore but not everything was triggered.
chainOverwriteNotAllTriggered=运行任务链 {0} 时出现错误，无法触发全部任务。已完成：{1}，运行中：{2}，失败：{3}，已触发：{4}，未触发：{5}。此任务链已被覆盖。
#XMSG: Task log message for a chain that contains a failed subtask.
chainOverwriteAnySubtaskFailed=任务链 {0} 已完成。此链中的一项任务失败，正在将链设为失败。
#XMSG: Task log message could not determine status.
chainOverwriteNotDetermined=意外错误阻止访问任务的状态。请重试，如果问题仍存在，请联系 SAP 支持人员。
#XMSG: Task log message could not take over
failedTakeover=无法接管现有任务。
#XMSG: Task log parallel check error
parallelCheckError=其他任务正在运行，此任务已被阻止，因此无法处理。
#XMSG: Task log parallel task runnig error
parallelTaskRunning=已在运行冲突任务。
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=运行期间出现状态 {0}，相关性 ID {1}。
#XMSG: Task log message successful takeover
successTakeover=必须解除遗留的锁定。已为此任务设置新锁定。
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=此任务的锁定已被其他任务取代。
#XMSG: Schedule created alert message
createScheduleSuccess=已创建计划
#XMSG: Task log message for taskFinishedAt
taskFinishedAt=任务 {0} 已经完成，完成时间为 {2}，状态为 {1}。
#XMSG: Placeholder symbol for empty columns
emptyCol=--
#XMSG: Paused Text
txtExpired=已过期
#XMSG: Update task schedule success Toast message
updateScheduleSuccess=已更新计划
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=已删除计划。
#XMSG: Task chain might have children error text
taskChainNoChildErrorText=由于可以生成在计划之前失败的任务，因此任务链可能有未显示的子项。
#XMSG: Task chain repair recent failed run label
retryRunLabel=重试最新运行
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=任务链运行重试已开始
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=任务链运行重试失败
#XMSG: chain repair message
chainRetried=用户 {0} 已触发任务链重试
#XMSG: chain not found during run
chainNotFoundDuringRun=没能找到任务链 {0}。请通过数据模型构建器检查它是否存在，并确保这个任务链已部署。
#XMSG: chain is not DAG
chainNotDag=没能启动任务链 {0}。它的结构无效。请在数据模型构建器中检查这个任务链。
#XMSG: chain has not valid parameters
notValidParameters=没能启动任务链 {0}。它的一个或多个参数无效。请在数据模型构建器中检查这个任务链。
#XMSG: Configuration parameter value size exceeds the maximum allowed limit
configurationSizeLimitError=没能启动任务链 {0}。任务链中某个任务的配置大小超过了允许的最大值 100 KiB。
#XMSG: Task {0} has input parameters
taskHasInputParameters=任务 {0} 具有输入参数。
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=冲突任务已在运行
#XMSG: error message for reading data from backend
txtReadBackendError=从后端读取时似乎出现错误。
##XMSG: error message for admission control rejection
admissionControlError=由于 SAP HANA 许可控制拒绝，任务失败。

#XBUT: Assign schedule menu button label
assignScheduleLabel=将计划分配给我
#XBUT: Pause schedule menu label
pauseScheduleLabel=暂停计划
#XBUT: Resume schedule menu label
resumeScheduleLabel=恢复计划
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=移除计划时出错。
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=分配计划时出错。
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=暂停计划时出错。
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=恢复计划时出错。
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=正在删除 {0} 个计划
#XMSG: Message for starting mass assign of schedules
massAssignStarted=正在更改 {0} 个计划的负责人
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=正在暂停 {0} 个计划
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=正在恢复 {0} 个计划
#XBUT: Select Columns Button
selectColumnsBtn=选择列
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=重试最新运行时出错，因为先前的任务运行在生成计划之前就已失败。
#XFLD: Refresh tooltip
TEXT_REFRESH=刷新
#XFLD: Select Columns tooltip
text_selectColumns=选择列

#Messages for BW Process Chain task log of the Data Integration Monitor
#XMSG: Task log message for successfully starting a BW process chain
startBwProcessChainSuccess=已在 SAP BW 网桥租户中成功启动 BW 流程链 "{0}"。
#XMSG: Task log message for skipping a BW process chain execution
bwProcessChainSkipped=由于缺乏新数据或之前的执行失败，已跳过 BW 流程链 "{0}" 。
#XMSG: Task log message for failure in starting a BW process chain
startBwProcessChainFailure=没能在 SAP BW 网桥租户中启动 BW 流程链 "{0}"。
#XMSG: Task log message for failure in getting the status of a BW process chain
getBwProcessChainStatusFailure=由于无法与 SAP BW 网桥建立连接，因此没能检索 BW 流程链 "{0}" 的状态，导致任务失败。请打开 "连接" 应用，并验证 "{1}" 空间中的 SAP BW 网桥连接。如果没有权限访问 "连接" 应用，请联系管理员。
#XMSG: Task log message for failure in completing a BW process chain
completeBwProcessChainFailure=没能在 SAP BW 网桥租户中完成 BW 流程链 "{0}"。
#XMSG: Task log message for the clickable text of the SAP BW Bridge Monitor link
bwBridgeMonitorLinkText=SAP BW 网桥监控器中的视图详细信息
#XMSG warning message while retrieving the task log messages if the user does not have access to the SAP BW Bridge space or to the shared object's space
missingPrivilegeOnSpaceError=检索消息时出现问题，或者你没有查看消息所需的权限。

#Messages for API task log of the Data Integration Monitor
#XMSG: Task log message for displaying the API response
viewResponse=已收到 "{0}" 的响应。
#XMSG: Task log message for empty ID in the API response to retrieve the status
emptyJobId=JSON 路径的响应正文中没有返回 ID："{0}"。
#XMSG: Task log message for empty success value in the API response
emptySuccessValue=JSON 路径的响应正文中没有返回成功值："{0}"。
#XMSG: Task log message for empty error value in the API response
emptyErrorValue=JSON 路径的响应正文中没有返回错误值："{0}"。
#XMSG: Task log message for none of the success/error conditions matching for the values in the API response
conditionsNotFulfilled=指定的成功或错误指示器条件与响应中的值都不匹配。
#XMSG: Task log message for the API response status being unsuccessful
statusNotOk=“{1}”的响应返回了不成功的状态码：{0}。
#XMSG: Task log message for the API response body being empty
emptyResponseBody=响应正文为空。
#XMSG: Task log message for the API response location header being invalid
emptyLocationHeader=响应中的位置标头为空。
#XMSG: Task log message for failure in retrieving the status of the invoked API
getStatusFailure=没能检索调用的 API 的状态。
#XMSG: Task log message for failure in completing the API task
completionFailure=API 任务 "{0}" 没能完成。
#XMSG: Task log message for a successful API task completion
apiCompleted=已成功完成 API 任务“{0}”。
#XMSG: Task log message for an API task with an invalid configuration
invalidConfiguration=API 任务“{0}”的配置无效。请检查配置并重试。
#XMSG: Task log message for the API task being canceled
cancelStart=请求取消 API 任务 "{0}"（日志 ID {1}）。
#XMSG: Task log message for the cancelation of the an API task which is no longer running and cannot be canceled
unableToCancel=API 任务 "{0}"（日志 ID {1}）已不再运行，没能取消。
#XMSG: Task log message when starting an API task. It should carry the name of the API task.
apiRunStart=正在启动 API task "{0}"。
#XMSG: Task log message when the task preparation is taking too long and times out
preparationTimeout=任务准备时间过长，已超时。
#XMSG: Task log message when a task failed because a cancel task was triggered
apiRunCancelled=日志 ID 为 {0} 的运行任务已由取消任务 {1} 取消。
#XMSG: Task log message when a cancel task failed to cancel a run task
cancelFailure=日志 ID 为 {1} 的取消任务没能取消日志 ID 为 {0} 的运行任务。
#XMSG: Task log message when configuration of the API task is too large
apiTaskconfigurationSizeLimitError=没能启动 API 任务 {0}，因为任务配置的大小超过了允许的最大值 100 KiB。
#XMSG: Task log message for failure in completing the Notification task
notificationTaskCompletionFailure=通知任务“{0}”没能完成。
#XMSG: Task log message when starting a Notification task.
notificationTaskRunStart=正在启动通知任务“{0}”。
#XMSG: Task log message for a successful Notification task completion
notificationTaskCompletionSuccess=通知任务“{0}”已完成。
#XFLD: Label for frequency column
everyLabel=每
#XFLD: Plural Recurrence text for Hour
hoursLabel=小时
#XFLD: Plural Recurrence text for Day
daysLabel=天
#XFLD: Plural Recurrence text for Month
monthsLabel=个月
#XFLD: Plural Recurrence text for Minutes
minutesLabel=分钟
