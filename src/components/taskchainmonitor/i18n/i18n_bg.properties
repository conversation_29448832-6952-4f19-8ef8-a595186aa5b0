

#XFLD: Task Chain header
headerTxt=Вериги задачи({0})
#XFLD: Task Chain header
headerTxtNew=Вериги от задачи({0})
#XTXT: Text for Schedule label
scheduleTxt=График
#XFLD: Text for Create schedule button
createScheduleTxt=Създаване на график
#XFLD: Text for edit schedule
editScheduleTxt=Редактиране на графика
#XLFD: Text for delete schedule
deleteScheduleTxt=Изтриване на график
#XTXT: text for refresh button label
refrestTxt=Опресняване
#XTXT: Text for Completed status
completedTxt=Завършено
#XTX: Text for Running status
runningTxt=Изпълнява се
#XTX: Text for failed status
failedTxt=Неуспешно
#XTX: Text for stopped status
stoppedTxt=Спряно
#XTX: Text for stopping status
stoppingTxt=Спира
#XFLD: Header for Chain name
chainNameLabel=Име на верига задачи
#XFLD: Header for Chain name
chainNameLabelBus=Бизнес наименование
#XFLD: Header for Chain name
chainNameLabelBusNew=Обект (бизнес наименованиe)
#XFLD: Header for Chain name
chainNameLabelTech=Техническо име
#XFLD: Header for Chain name
chainNameLabelTechNew=Обект (техническо име)
#XFLD: Last Run Status label
lastRunStatuslabel=Статус на последното изпълнение
#XFLD: Last Run Status label
lastRunStatuslabelNew=Статус
#XFLD: Frequency Label
frequencyLabel=Честота
#XFLD: Frequency Label
frequencyLabelNew=Планирана честота
#XFLD: Duration label
durationLabel=Времетраене
#XFLD: Duration label
durationLabelNew=Продължителност на последното изпълнение
#XFLD: Run Start label
runStartLabel=Начало на последното изпълнение
#XFLD: Run end label
runEndLabel=Край на последното изпълнение
#XFLD: Next Run label
nextRunlabel=Следващо изпълнение
#XFLD: Next Run label
nextRunlabelNew=Планирано следващо изпълнение
#XTXT: Task chain logs link tooltip text
taskChainLogsTxt=Подробни данни за журнал на верига задачи
#XTXT: Task chain logs link tooltip text
taskChainLogsTxtNew=Подробни данни
#XTXT: Scheduled text
scheduledTxt=Планирано
#XTXT: Paused text
pausedTxt=Пауза
#XTXT: Execute button label
runLabel=Изпълнение
#XTXT: Execute button label
runLabelNew=Стартиране на изпълнение
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Изпълнение на веригата от задачи
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Изпълнението на веригата от задачи стартира.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Изпълнението на веригата от задачи е стартирано за {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Неуспешно изпълнение на веригата от задачи.
#XFLD: Label for schedule owner column
txtScheduleOwner=Собственик на график
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Показва създателя на графика
#XMSG: Task log message for start chain
startChain=Стартиране изпълнение на верига задачи.
#XMSG: Task log message for load chain from repository
loadChainFromRepository=Веригата задачи е заредена и инициализирана.
#XMSG: Task log message started task
taskStarted=Задача {0} е стартирана.
#XMSG: Task log message for finished task
taskFinished=Задача {0} завърши със статус {1}.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Зареждане на верига задачи и подготовка за изпълнение на общо {0} задачи, които са част от тази верига.
#XMSG: Task log message for starting a subtask
chainStartSubtask=Иницииране на задача {0} за изпълнение. ИД на задача = {1}.
#XMSG: Task log message for finishing a subtask
chainFinishSubtask=Задача {0} завърши със статус {1}.
#XMSG: Task log message for indicating chain success
chainCompleted=Всички задачи за {0} са завършени. Статусът на веригата задачи е зададен като завършен.
#XMSG: Task log message for indicating chain failure
chainFailed=От общо {0} задачи, {1} от тях бяха завършени, а {2} бяха неуспешни. Статусът на веригата задачи е зададен като неуспешен.
#XMSG: Task log message for indicating chain cancelation
chainCanceled=Стартирана е отмяната на изпълнението на верига от задачи. ИД на журнала за задача за отмяна е {0}.
#XMSG: Task log message for indicating chain cancelation in the cancel task
chainCanceling=Отмяна на верига {0}.
#XMSG: Task log message for general chain runtime error
chainError=Възникна неочаквана грешка при изпълнението на веригата задачи.
#XMSG: Task log message for starting the overwrite check.
chainOverwriteStart=Проверка дали веригата задачи с ИД {0} е завършена.
#XMSG: Task log message indicating the chain is too old.
chainOverwriteTooOld=Не бяха намерени задачи за изпълнение за верига {0} . Изпълнението е от преди {1} минути. Статусът на веригата задачи е зададен като неуспешен.
#XMSG: Task log message indicating the chain was created too recently.
chainOverwriteTooRecent=Не бяха намерени задачи за изпълнение за верига {0} . Изпълнението е от преди {1} минути. Веригата задачи все още се изпълнява.
#XMSG: Task log message indicating a recent heartbeat was found.
chainOverwriteHeartbeatAlive=Веригата задачи {0} още се изпълнява. Завършени: {1}, в процес на изпълнение: {2}, неуспешни: {3}, инициирани: {4}, не са инициирани: {5}.
#XMSG: Task log message for a chain that is not running anymore but not everything was triggered.
chainOverwriteNotAllTriggered=Възникна грешка при изпълнението на веригата задачи {0} и не всички задачи бяха инициирани. Завършени: {1}, В процес на изпълнение: {2}, Неуспешни: {3}, Инициирани: {4}, Не са инициирани: {5}. Тази верига от задачи е презаписана
#XMSG: Task log message for a chain that contains a failed subtask.
chainOverwriteAnySubtaskFailed=Веригата задачи {0} завърши. Една задача от нея е неуспешна. Веригата е зададена като неуспешна.
#XMSG: Task log message could not determine status.
chainOverwriteNotDetermined=Заради неочаквана грешка нямаме достъп до статуса на задачата. Опитайте отново и се свържете с екипа по поддръжката на SAP, ако грешката не изчезне.
#XMSG: Task log message could not take over
failedTakeover=Неуспешно приемане на съществуваща задача.
#XMSG: Task log parallel check error
parallelCheckError=Задачата не може да бъде обработена, защото в момента се изпълнява друга задача, която вече блокира тази.
#XMSG: Task log parallel task runnig error
parallelTaskRunning=Вече се изпълнява конфликтна задача.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Статус {0} при изпълнение на ИД на корелация {1}.
#XMSG: Task log message successful takeover
successTakeover=Останалото заключване трябваше да бъде деблокирано. Зададено е ново заключване за тази задача.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Заключването на тази задача е прехвърлено към друга задача.
#XMSG: Schedule created alert message
createScheduleSuccess=Създаден е график
#XMSG: Task log message for taskFinishedAt
taskFinishedAt=Задача {0} завърши в {2} със статус {1}.
#XMSG: Placeholder symbol for empty columns
emptyCol=--
#XMSG: Paused Text
txtExpired=С изтекъл срок
#XMSG: Update task schedule success Toast message
updateScheduleSuccess=Графикът е актуализиран
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Графикът е изтрит.
#XMSG: Task chain might have children error text
taskChainNoChildErrorText=Веригата от задачи може да има подчинени елементи, които не се показват, защото задачата беше неуспешна преди генериране на плана.
#XMSG: Task chain repair recent failed run label
retryRunLabel=Повторен опит на последно изпълнение
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Повторният опит на изпълнението на веригата от задачи стартира
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Повторният опит на изпълнението на веригата от задачи е неуспешно
#XMSG: chain repair message
chainRetried=Повторният опит на веригата от задачи е инициирано от потребител {0}
#XMSG: chain not found during run
chainNotFoundDuringRun=Верига задачи {0} не беше намерена. Проверете дали съществува чрез конструктора на данни и се уверете, че веригата е разгърната.
#XMSG: chain is not DAG
chainNotDag=Невъзможно стартиране на верига задачи {0}. Структурата е невалидна. Проверете веригата задачи в конструктора на данни.
#XMSG: chain has not valid parameters
notValidParameters=Невъзможно стартиране на верига задачи {0}. Един или няколко от нейните параметри са невалидни. Проверете веригата задачи в конструктора на данни.
#XMSG: Configuration parameter value size exceeds the maximum allowed limit
configurationSizeLimitError=Верига задачи {0} не може да бъде стартирана. Размерът на конфигурацита на задачата във веригата надвишава максималния разрешен размер от 100 кибибайта (KiB).
#XMSG: Task {0} has input parameters
taskHasInputParameters=Задача {0} има входни параметри.
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Вече се изпълнява конфликтна задача
#XMSG: error message for reading data from backend
txtReadBackendError=Изглежда е възникнала грешка при четенето от бекенда.
##XMSG: error message for admission control rejection
admissionControlError=Задачата е неуспешна заради отхвърляне от контрола на приемане на SAP HANA.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Присъединяване на график към мен
#XBUT: Pause schedule menu label
pauseScheduleLabel=Поставяне на график на пауза
#XBUT: Resume schedule menu label
resumeScheduleLabel=График за възобновяване
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Възникна грешка при премахването на графиците.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Възникна грешка при присъединяването на графиците.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Възникна грешка при поставянето на пауза на графиците.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Възникна грешка при възобновяването на графиците.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Изтриване на графици: {0}
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Промяна на собственика на графици: {0}
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Графици за поставяне на пауза: {0}
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Възобновяване на графици: {0}
#XBUT: Select Columns Button
selectColumnsBtn=Избор на колони
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Изглежда се получи грешка при повторния опит на последното изпълнение, поради неуспешно предишно изпълнение на задачата, преди планът да бъде генериран.
#XFLD: Refresh tooltip
TEXT_REFRESH=Опресняване
#XFLD: Select Columns tooltip
text_selectColumns=Избор на колони

#Messages for BW Process Chain task log of the Data Integration Monitor
#XMSG: Task log message for successfully starting a BW process chain
startBwProcessChainSuccess=Веригата от процеси в BW „{0}“ стартира успешно в наемателя на свързващото приложение SAP BW.
#XMSG: Task log message for skipping a BW process chain execution
bwProcessChainSkipped=Веригата от процеси в BW „{0}“ е пропусната поради неналичност на нови данни или неуспешно предходно изпълнение.
#XMSG: Task log message for failure in starting a BW process chain
startBwProcessChainFailure=Веригата от процеси в BW „{0}“ не стартира успешно в наемателя на свързващото приложение SAP BW.
#XMSG: Task log message for failure in getting the status of a BW process chain
getBwProcessChainStatusFailure=Задата е неуспешна, защото не успяхме да извлечем статуса на веригата от процеси BW „{0}“ поради проблем с установяването на връзка със SAP BW Bridge. Отворете приложението „Връзки“ и валидирайте връзката със SAP BW Bridge в пространството „{1}“. Ако нямате достъп до приложението „Връзки“, се свържете с администратор.
#XMSG: Task log message for failure in completing a BW process chain
completeBwProcessChainFailure=Веригата от процеси в BW „{0}“ не завърши успешно в наемателя на свързващото приложение SAP BW.
#XMSG: Task log message for the clickable text of the SAP BW Bridge Monitor link
bwBridgeMonitorLinkText=Преглед на подробните данни в монитора на свързващото приложение SAP BW.
#XMSG warning message while retrieving the task log messages if the user does not have access to the SAP BW Bridge space or to the shared object's space
missingPrivilegeOnSpaceError=Възникна проблем при извличането на съобщенията. Възможно е да нямате необходимите права, за да ги видите.

#Messages for API task log of the Data Integration Monitor
#XMSG: Task log message for displaying the API response
viewResponse=Получен е отговор за „{0}“.
#XMSG: Task log message for empty ID in the API response to retrieve the status
emptyJobId=Не е върнат ИД в основната част на отговора за пътя на JSON „{0}“.
#XMSG: Task log message for empty success value in the API response
emptySuccessValue=Не е върната стойност за успешно изпълнение в основната част на отговора за пътя на JSON „{0}“.
#XMSG: Task log message for empty error value in the API response
emptyErrorValue=Не е върната стойност за грешка в основната част на отговора за пътя на JSON „{0}“.
#XMSG: Task log message for none of the success/error conditions matching for the values in the API response
conditionsNotFulfilled=Нито едно от посочените условия на индикатор за успешно изпълнени или грешка не отговаря на стойностите в отговора.
#XMSG: Task log message for the API response status being unsuccessful
statusNotOk=Отговорът за „{1}“ върна код за неуспешен статус: {0}.
#XMSG: Task log message for the API response body being empty
emptyResponseBody=Основната част на отговора беше празна.
#XMSG: Task log message for the API response location header being invalid
emptyLocationHeader=Заглавката местоположение в отговора беше празна.
#XMSG: Task log message for failure in retrieving the status of the invoked API
getStatusFailure=Статусът на извикания API не беше извлечен.
#XMSG: Task log message for failure in completing the API task
completionFailure=Задачата на API „{0}“ не беше завършена успешно.
#XMSG: Task log message for a successful API task completion
apiCompleted=Задачата на API „{0}“ е завършена успешно.
#XMSG: Task log message for an API task with an invalid configuration
invalidConfiguration=Конфигурацията на задачата на API „{0}“ е невалидна. Моля, проверете конфигурацията и опитайте отново.
#XMSG: Task log message for the API task being canceled
cancelStart=Изисква се отмяна на задачата на API „{0}“ с logId {1}.
#XMSG: Task log message for the cancelation of the an API task which is no longer running and cannot be canceled
unableToCancel=Задачата на API „{0}“ с logId {1} вече не се изпълнява и не може да бъде отменена.
#XMSG: Task log message when starting an API task. It should carry the name of the API task.
apiRunStart=Стартиране на задачата на API „{0}“.
#XMSG: Task log message when the task preparation is taking too long and times out
preparationTimeout=Подготовката на задачата отнема твърде много време и е прекратена поради изтичане на времето. 
#XMSG: Task log message when a task failed because a cancel task was triggered
apiRunCancelled=Задачата за изпълнение с logId {0} е отменена от задачата за отмяна {1}.
#XMSG: Task log message when a cancel task failed to cancel a run task
cancelFailure=Задачата за отмяна с logId {1} не успя да отмени задачата за изпълнение с logId {0}.
#XMSG: Task log message when configuration of the API task is too large
apiTaskconfigurationSizeLimitError=Задачата на API {0} не може да бъде стартирана, защото размерът на конфигурацията ѝ на задача надвишава максималния разрешен размер от 100 кибибайта (KiB).
#XMSG: Task log message for failure in completing the Notification task
notificationTaskCompletionFailure=Задачата на известие „{0}“ не беше завършена успешно.
#XMSG: Task log message when starting a Notification task.
notificationTaskRunStart=Стартиране на задачата на известие „{0}“.
#XMSG: Task log message for a successful Notification task completion
notificationTaskCompletionSuccess=Задачата на известие „{0}“ е завършена.
#XFLD: Label for frequency column
everyLabel=на всеки
#XFLD: Plural Recurrence text for Hour
hoursLabel=часа
#XFLD: Plural Recurrence text for Day
daysLabel=дни
#XFLD: Plural Recurrence text for Month
monthsLabel=месеца
#XFLD: Plural Recurrence text for Minutes
minutesLabel=минути
