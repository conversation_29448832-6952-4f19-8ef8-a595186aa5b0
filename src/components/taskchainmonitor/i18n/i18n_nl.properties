

#XFLD: Task Chain header
headerTxt=Taakketens ({0})
#XFLD: Task Chain header
headerTxtNew=Taakketens ({0})
#XTXT: Text for Schedule label
scheduleTxt=Plannen
#XFLD: Text for Create schedule button
createScheduleTxt=Planning maken
#XFLD: Text for edit schedule
editScheduleTxt=Planning bewerken
#XLFD: Text for delete schedule
deleteScheduleTxt=Planning verwijderen
#XTXT: text for refresh button label
refrestTxt=Vernieuwen
#XTXT: Text for Completed status
completedTxt=Voltooid
#XTX: Text for Running status
runningTxt=Wordt uitgevoerd
#XTX: Text for failed status
failedTxt=Mislukt
#XTX: Text for stopped status
stoppedTxt=Gestopt
#XTX: Text for stopping status
stoppingTxt=Wordt gestopt
#XFLD: Header for Chain name
chainNameLabel=Naam taakketen
#XFLD: Header for Chain name
chainNameLabelBus=Objectnaam
#XFLD: Header for Chain name
chainNameLabelBusNew=Object (bedrijfsnaam)
#XFLD: Header for Chain name
chainNameLabelTech=Technische naam
#XFLD: Header for Chain name
chainNameLabelTechNew=Object (technische naam)
#XFLD: Last Run Status label
lastRunStatuslabel=Status laatste uitvoering
#XFLD: Last Run Status label
lastRunStatuslabelNew=Status
#XFLD: Frequency Label
frequencyLabel=Frequentie
#XFLD: Frequency Label
frequencyLabelNew=Geplande frequentie
#XFLD: Duration label
durationLabel=Duur
#XFLD: Duration label
durationLabelNew=Duur laatste run
#XFLD: Run Start label
runStartLabel=Start laatste uitvoering
#XFLD: Run end label
runEndLabel=Einde laatste uitvoering
#XFLD: Next Run label
nextRunlabel=Volgende uitvoering
#XFLD: Next Run label
nextRunlabelNew=Geplande volgende run
#XTXT: Task chain logs link tooltip text
taskChainLogsTxt=Logdetails taakketen
#XTXT: Task chain logs link tooltip text
taskChainLogsTxtNew=Details
#XTXT: Scheduled text
scheduledTxt=Gepland
#XTXT: Paused text
pausedTxt=Gepauzeerd
#XTXT: Execute button label
runLabel=Uitvoeren
#XTXT: Execute button label
runLabelNew=Run uitvoeren
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Voer taakketen uit
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Uitvoering van taakketen is gestart.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Uitvoering van taakketen is gestart voor {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Uitvoering van taakketen is mislukt.
#XFLD: Label for schedule owner column
txtScheduleOwner=Planningseigenaar
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Geeft weer wie planning heeft gecreëerd
#XMSG: Task log message for start chain
startChain=Uitvoering van taakketen wordt gestart.
#XMSG: Task log message for load chain from repository
loadChainFromRepository=Taakketen is geladen en geïnitialiseerd.
#XMSG: Task log message started task
taskStarted=Taak {0} is gestart.
#XMSG: Task log message for finished task
taskFinished=De taak {0} is beëindigd met status {1}.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Bezig met laden van taakketen en voorbereiden van uitvoering van in totaal {0} taken die deel uitmaken van deze keten.
#XMSG: Task log message for starting a subtask
chainStartSubtask=Bezig met activeren van uitvoering van taak {0}. Taak-ID = {1}.
#XMSG: Task log message for finishing a subtask
chainFinishSubtask=Taak {0} voltooid met status {1}.
#XMSG: Task log message for indicating chain success
chainCompleted=Alle {0} taken zijn voltooid. De status van de taakketen is op voltooid gezet.
#XMSG: Task log message for indicating chain failure
chainFailed=Op een totaal van {0} taken zijn {1} taken voltooid en {2} taken mislukt. De status van de taakketen is op mislukt gezet.
#XMSG: Task log message for indicating chain cancelation
chainCanceled=Annuleren van taakketenrun is gestart. Log-ID van annuleringstaak is {0}.
#XMSG: Task log message for indicating chain cancelation in the cancel task
chainCanceling=Keten {0} wordt geannuleerd.
#XMSG: Task log message for general chain runtime error
chainError=Er is een onverwachte fout opgetreden bij het uitvoeren van de taakketen.
#XMSG: Task log message for starting the overwrite check.
chainOverwriteStart=Bezig met controleren of taakketen met ID {0} is voltooid.
#XMSG: Task log message indicating the chain is too old.
chainOverwriteTooOld=Er zijn geen taken gevonden die voor keten {0} moeten worden uitgevoerd. Run is {1} minuten oud. De status van de taakketen is op mislukt gezet.
#XMSG: Task log message indicating the chain was created too recently.
chainOverwriteTooRecent=Er zijn geen taken gevonden die voor keten {0} moeten worden uitgevoerd. Run is {1} minuten oud. De taakketen wordt nog uitgevoerd.
#XMSG: Task log message indicating a recent heartbeat was found.
chainOverwriteHeartbeatAlive=Taakketen {0} wordt nog uitgevoerd. Voltooid: {1}, In uitvoering: {2}, Mislukt: {3}, Gestart: {4}, Niet gestart: {5}.
#XMSG: Task log message for a chain that is not running anymore but not everything was triggered.
chainOverwriteNotAllTriggered=Er is een fout opgetreden bij het uitvoeren van taakketen {0}, en niet alle taken konden worden gestart. Voltooid: {1}, In uitvoering: {2}, Mislukt: {3}, Gestart: {4}, Niet gestart: {5}. Deze taakketen is overschreven.
#XMSG: Task log message for a chain that contains a failed subtask.
chainOverwriteAnySubtaskFailed=Taakketen {0} is voltooid. Een taak van de keten is mislukt. De status van de taakketen is op mislukt gezet.
#XMSG: Task log message could not determine status.
chainOverwriteNotDetermined=Een onverwachte fout zorgt ervoor dat wij geen toegang hebben tot de status van de taak. Probeer het opnieuw en neem contact op met SAP Support als de fout blijft bestaan.
#XMSG: Task log message could not take over
failedTakeover=Overnemen van bestaande taak is mislukt.
#XMSG: Task log parallel check error
parallelCheckError=De taak kan niet worden verwerkt omdat er al een andere taak wordt uitgevoerd. Deze andere taak blokkeert de huidige taak.
#XMSG: Task log parallel task runnig error
parallelTaskRunning=Er wordt al een conflicterende taak uitgevoerd.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Status {0} tijdens uitvoering met correlatie-ID {1}.
#XMSG: Task log message successful takeover
successTakeover=Resterende blokkering moest worden vrijgegeven. De nieuwe blokkering voor deze taak is ingesteld.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Blokkering van deze taak is overgenomen door andere taak.
#XMSG: Schedule created alert message
createScheduleSuccess=Planning gemaakt
#XMSG: Task log message for taskFinishedAt
taskFinishedAt=Taak {0} voltooid om {2} met status {1}.
#XMSG: Placeholder symbol for empty columns
emptyCol=--
#XMSG: Paused Text
txtExpired=Verlopen
#XMSG: Update task schedule success Toast message
updateScheduleSuccess=Planning bijgewerkt
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Planning verwijderd.
#XMSG: Task chain might have children error text
taskChainNoChildErrorText=De taakketen bevat mogelijk onderliggende elementen die niet worden weergegeven omdat de taak is mislukt voordat het plan kon worden gegenereerd.
#XMSG: Task chain repair recent failed run label
retryRunLabel=Laatste run opnieuw proberen
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Nieuwe run taakketen is gestart
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Nieuwe run taakketen is mislukt
#XMSG: chain repair message
chainRetried=Nieuw poging taakketen gestart door gebruiker {0}
#XMSG: chain not found during run
chainNotFoundDuringRun=Kan taakketen {0} niet vinden. Controleer of deze bestaat via de Data Builder en zorg ervoor dat de keten wordt geïmplementeerd.
#XMSG: chain is not DAG
chainNotDag=Kan taakketen {0} niet starten. De structuur ervan is ongeldig. Controleer de taakketen in de Data Builder.
#XMSG: chain has not valid parameters
notValidParameters=Kan taakketen {0} niet starten. Een of meerdere bijbehorende parameters zijn ongeldig. Controleer de taakketen in de Data Builder.
#XMSG: Configuration parameter value size exceeds the maximum allowed limit
configurationSizeLimitError=Kan taakketen {0} niet starten. De grootte van de configuratie van een taak in de keten overschrijdt de max. toegestane grootte van 100 kibibytes (KiB).
#XMSG: Task {0} has input parameters
taskHasInputParameters=Taak {0} heeft invoerparameters.
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Er wordt al een conflicterende taak uitgevoerd.
#XMSG: error message for reading data from backend
txtReadBackendError=Er is een fout opgetreden tijdens het lezen van gegevens uit de backend.
##XMSG: error message for admission control rejection
admissionControlError=De taak is mislukt vanwege een SAP HANA-toegangscontroleafwijzing.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Planning aan mij toewijzen
#XBUT: Pause schedule menu label
pauseScheduleLabel=Planning pauzeren
#XBUT: Resume schedule menu label
resumeScheduleLabel=Planning hervatten
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Fout opgetreden bij verwijderen planningen.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Fout opgetreden bij toewijzen planningen.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Fout opgetreden bij pauzeren planningen.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Fout opgetreden bij hervatten planningen.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted={0} planningen worden verwijderd
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Eigenaar van {0} planningen wordt gewijzigd
#XMSG: Message for starting mass pausing of schedules
massPauseStarted={0} planningen worden gepauzeerd
#XMSG: Message for starting mass resuming of schedules
massResumeStarted={0} planningen worden hervat
#XBUT: Select Columns Button
selectColumnsBtn=Kolommen selecteren
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Er was blijkbaar een fout bij het opnieuw proberen van de laatste run; de vorige taakrun is mislukt voordat het plan kon worden gegenereerd.
#XFLD: Refresh tooltip
TEXT_REFRESH=Vernieuwen
#XFLD: Select Columns tooltip
text_selectColumns=Kolommen selecteren

#Messages for BW Process Chain task log of the Data Integration Monitor
#XMSG: Task log message for successfully starting a BW process chain
startBwProcessChainSuccess=De BW-procesketen "{0}" is gestart in de SAP BW Bridge-tenant.
#XMSG: Task log message for skipping a BW process chain execution
bwProcessChainSkipped=BW-procesketen "{0}" is overgeslagen omdat er geen nieuwe gegevens beschikbaar zijn of omdat een vorige uitvoering is mislukt.
#XMSG: Task log message for failure in starting a BW process chain
startBwProcessChainFailure=De BW-procesketen "{0}" kon niet worden gestart in de SAP BW Bridge-tenant.
#XMSG: Task log message for failure in getting the status of a BW process chain
getBwProcessChainStatusFailure=Taak is mislukt omdat status van BW-procesketen "{0}" niet is opgehaald vanwege een probleem bij verbinding maken met de SAP BW-bridge. Open de app "Verbindingen" en valideer de verbinding met SAP BW-bridge in ruimte "{1}". Als u geen toegang hebt tot de app "Verbindingen" neemt u contact op met de beheerder.
#XMSG: Task log message for failure in completing a BW process chain
completeBwProcessChainFailure=De BW-procesketen "{0}" kon niet worden voltooid in de SAP BW Bridge-tenant.
#XMSG: Task log message for the clickable text of the SAP BW Bridge Monitor link
bwBridgeMonitorLinkText=Details weergeven in SAP BW Bridge Monitor
#XMSG warning message while retrieving the task log messages if the user does not have access to the SAP BW Bridge space or to the shared object's space
missingPrivilegeOnSpaceError=Er was een probleem bij het ophalen van de berichten of u hebt niet de vereiste machtiging om deze weer te geven.

#Messages for API task log of the Data Integration Monitor
#XMSG: Task log message for displaying the API response
viewResponse=Respons ontvangen voor "{0}".
#XMSG: Task log message for empty ID in the API response to retrieve the status
emptyJobId=Geen ID geretourneerd in responstekst voor JSON-pad: "{0}".
#XMSG: Task log message for empty success value in the API response
emptySuccessValue=Geen succeswaarde geretourneerd in responstekst voor JSON-pad: "{0}"
#XMSG: Task log message for empty error value in the API response
emptyErrorValue=Geen succeswaarde geretourneerd in responstekst voor JSON-pad: "{0}"
#XMSG: Task log message for none of the success/error conditions matching for the values in the API response
conditionsNotFulfilled=Geen van de opgegeven succes- of fouttekencondities stemmen overeen met de waarden in de respons.
#XMSG: Task log message for the API response status being unsuccessful
statusNotOk=De respons voor "{1}" gaf een niet-geslaagde statuscode terug: {0}
#XMSG: Task log message for the API response body being empty
emptyResponseBody=De responstekst was leeg.
#XMSG: Task log message for the API response location header being invalid
emptyLocationHeader=De locatiekop in de respons is leeg.
#XMSG: Task log message for failure in retrieving the status of the invoked API
getStatusFailure=De status van de opgeroepen API kon niet worden opgehaald.
#XMSG: Task log message for failure in completing the API task
completionFailure=De API-taak "{0}" kon niet worden voltooid.
#XMSG: Task log message for a successful API task completion
apiCompleted=De API-taak "{0}" is voltooid.
#XMSG: Task log message for an API task with an invalid configuration
invalidConfiguration=De configuratie van de API-taak "{0}" is ongeldig. Controleer de configuratie en probeer opnieuw.
#XMSG: Task log message for the API task being canceled
cancelStart=Aangevraagde annulering van API-taak "{0}" met logId {1}.
#XMSG: Task log message for the cancelation of the an API task which is no longer running and cannot be canceled
unableToCancel=API-taak "{0}" met logId {1} wordt niet meert uitgevoerd en kan niet meer worden geannuleerd.
#XMSG: Task log message when starting an API task. It should carry the name of the API task.
apiRunStart=API-taak "{0}" wordt gestart.
#XMSG: Task log message when the task preparation is taking too long and times out
preparationTimeout=De taakvoorbereiding duurt te lang en heeft time-out.
#XMSG: Task log message when a task failed because a cancel task was triggered
apiRunCancelled=De runtaak met logId {0} is geannuleerd door annuleringstaak {1}.
#XMSG: Task log message when a cancel task failed to cancel a run task
cancelFailure=De annuleringstaak met logId {1} is mislukt om de runtaak met logId {0} te annuleren.
#XMSG: Task log message when configuration of the API task is too large
apiTaskconfigurationSizeLimitError=Kan API-keten {0} niet starten omdat de grootte van de bijhorende taakconfiguratie de max. toegestane grootte van 100 kibibytes (KiB) overschrijdt.
#XMSG: Task log message for failure in completing the Notification task
notificationTaskCompletionFailure=De meldingstaak "{0}" kon niet worden voltooid.
#XMSG: Task log message when starting a Notification task.
notificationTaskRunStart=Bezig met starten van meldingstaak "{0}".
#XMSG: Task log message for a successful Notification task completion
notificationTaskCompletionSuccess=De meldingstaak "{0}" is voltooid.
#XFLD: Label for frequency column
everyLabel=Elke
#XFLD: Plural Recurrence text for Hour
hoursLabel=Uren
#XFLD: Plural Recurrence text for Day
daysLabel=Dagen
#XFLD: Plural Recurrence text for Month
monthsLabel=Maanden
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minuten
