

#XFLD: Task Chain header
headerTxt=Αλυσίδες Εργασιών({0})
#XFLD: Task Chain header
headerTxtNew=Αλυσίδες Εργασιών ({0})
#XTXT: Text for Schedule label
scheduleTxt=Χρονοδ/μα
#XFLD: Text for Create schedule button
createScheduleTxt=Δημιουργία Προγράμματος
#XFLD: Text for edit schedule
editScheduleTxt=Επεξεργασία Προγράμματος
#XLFD: Text for delete schedule
deleteScheduleTxt=Διαγραφή Προγράμματος
#XTXT: text for refresh button label
refrestTxt=Ανανέωση
#XTXT: Text for Completed status
completedTxt=Ολοκληρωμένο
#XTX: Text for Running status
runningTxt=Εκτελείται
#XTX: Text for failed status
failedTxt=Απέτυχε
#XTX: Text for stopped status
stoppedTxt=Διακόπηκε
#XTX: Text for stopping status
stoppingTxt=Διακόπηκε
#XFLD: Header for Chain name
chainNameLabel=Ονομα Αλυσίδας Εργασιών
#XFLD: Header for Chain name
chainNameLabelBus=Επωνυμία Επιχείρησης
#XFLD: Header for Chain name
chainNameLabelBusNew=Αντικείμενο (Επωνυμία Επιχείρησης)
#XFLD: Header for Chain name
chainNameLabelTech=Τεχνικό Ονομα
#XFLD: Header for Chain name
chainNameLabelTechNew=Αντικείμενο (Τεχνικό Ονομα)
#XFLD: Last Run Status label
lastRunStatuslabel=Κατάσταση Τελευταίας Εκτέλεσης
#XFLD: Last Run Status label
lastRunStatuslabelNew=Κατάσταση
#XFLD: Frequency Label
frequencyLabel=Συχνότητα
#XFLD: Frequency Label
frequencyLabelNew=Προγραμματισμένη Συχνότητα
#XFLD: Duration label
durationLabel=Διάρκεια
#XFLD: Duration label
durationLabelNew=Διάρκεια Τελευταίας Εκτέλεσης
#XFLD: Run Start label
runStartLabel=Έναρξη Τελευταίας Εκτέλεσης
#XFLD: Run end label
runEndLabel=Λήξη Τελευταίας Εκτέλεσης
#XFLD: Next Run label
nextRunlabel=Επόμενη Εκτέλεση
#XFLD: Next Run label
nextRunlabelNew=Επόμενη Προγραμματισμένη Εκτέλεση
#XTXT: Task chain logs link tooltip text
taskChainLogsTxt=Λεπτομέρειες για Ημερολόγιο Αλυσίδας Εργασιών
#XTXT: Task chain logs link tooltip text
taskChainLogsTxtNew=Λεπτομέρειες
#XTXT: Scheduled text
scheduledTxt=Προγρ/μένο
#XTXT: Paused text
pausedTxt=Διακ.
#XTXT: Execute button label
runLabel=Εκτέλεση
#XTXT: Execute button label
runLabelNew=Εναρξη Εκτέλεσης
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Η εκτέλεση της αλυσίδας εργασιών άρχισε
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Η εκτέλεση της αλυσίδας εργασιών άρχισε.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Η εκτέλεση της αλυσίδας εργασιών άρχισε για {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Αδύνατη εκτέλεση της αλυσίδας εργασιών.
#XFLD: Label for schedule owner column
txtScheduleOwner=Ιδιοκτήτης Προγράμματος
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Εμφάνιση δημιουργού προγράμματος
#XMSG: Task log message for start chain
startChain=Εναρξη εκτέλεσης αλυσίδας εργασιών.
#XMSG: Task log message for load chain from repository
loadChainFromRepository=Η αλυσίδα εργασιών φορτώθηκε και ξεκίνησε.
#XMSG: Task log message started task
taskStarted=Η εργασία {0} άρχισε.
#XMSG: Task log message for finished task
taskFinished=Η εργασία {0} έληξε με κατάσταση {1}.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Φόρτωση αλυσίδας εργασιών και προετοιμασία για εκτέλεση συνόλου {0} εργασιών που είναι μέρος αυτής της αλυσίδας.
#XMSG: Task log message for starting a subtask
chainStartSubtask=Εναρξη εργασίας {0} για εκτέλεση. Ιd εργασίας = {1}.
#XMSG: Task log message for finishing a subtask
chainFinishSubtask=Εργασία {0} ολοκληρώθηκε με κατάσταση {1}.
#XMSG: Task log message for indicating chain success
chainCompleted=Ολες οι {0} εργασίες ολοκληρώθηκαν. Η κατάσταση της αλυσίδας εργασιών ορίστηκε σε ολοκληρωμένη.
#XMSG: Task log message for indicating chain failure
chainFailed=Από συνολικά {0} εργασίες, {1} εργασίες δεν ολοκληρώθηκαν και {2} εργασίες απέτυχαν. Η κατάσταση αλυσίδας εργασιών ορίστηκε σε αποτυχημένη.
#XMSG: Task log message for indicating chain cancelation
chainCanceled=Η ακύρωση εκτέλεσης αλυσίδας εργασιών άρχισε. Το id ημερολογίου εργασίας ακύρωσης είναι {0}.
#XMSG: Task log message for indicating chain cancelation in the cancel task
chainCanceling=Ακύρώση αλυσίδας {0}.
#XMSG: Task log message for general chain runtime error
chainError=Απρόβλεπτο σφάλμα κατά την εκτέλεση της αλυσίδας εργασιών.
#XMSG: Task log message for starting the overwrite check.
chainOverwriteStart=Ελέγξτε αν η αλυσίδα εργασιών με id {0} ολοκληρώθηκε.
#XMSG: Task log message indicating the chain is too old.
chainOverwriteTooOld=Δεν βρέθηκαν εργασίες για εκτέλεση για αλυσίδα {0} . Η εκτέλεση έγινε {1} λεπτά πριν. Η κατάσταση της αλυσίδας εργασιών ορίστηκε σε αποτυχημένη.
#XMSG: Task log message indicating the chain was created too recently.
chainOverwriteTooRecent=Δεν βρέθηκαν εργασίες για εκτέλεση για αλυσίδα {0} . Η εκτέλεση έγινε {1} λεπτά πριν. Η αλυσίδα εργασιών εκτελείται ακόμα.
#XMSG: Task log message indicating a recent heartbeat was found.
chainOverwriteHeartbeatAlive=Η αλυσίδα εργασιών {0} εκτελείται ακόμα. Ολοκληρώθηκε: {1}, Εκτελείται: {2}, Αποτυχημένο: {3}, Ξεκίνησε: {4}, Δεν ξεκίνησε: {5}.
#XMSG: Task log message for a chain that is not running anymore but not everything was triggered.
chainOverwriteNotAllTriggered=Σφάλμα κατά την εκτέλεση αλυσίδας εργασιών {0}, και δεν ξεκίνησαν όλες οι εργασίες. Ολοκληρωμένο: {1}, Εκτελείται: {2}, Αποτυχημένο: {3}, Ξεκίνησε: {4}, Δεν ξεκίνησε: {5}. Αυτή η αλυσίδα εργασιών αντικαταστάθηκε.
#XMSG: Task log message for a chain that contains a failed subtask.
chainOverwriteAnySubtaskFailed=Η αλυσίδα εργασιών {0} ολοκληρώθηκε. Μία εργασία της αλυσίδας απέτυχε, καθορισμός αλυσίδας σε αποτυχημένη.
#XMSG: Task log message could not determine status.
chainOverwriteNotDetermined=Απρόβλεπτο σφάλμα δεν επιτρέπει την πρόσβαση σε κατάσταση εργασίας. Προσπαθήστε ξανά και επικοινωνήστε με το τμήμα Υποστήριξης SAP αν επιμένει το σφάλμα.
#XMSG: Task log message could not take over
failedTakeover=Αδύνατη ανάληψη υπάρχουσας εργασίας.
#XMSG: Task log parallel check error
parallelCheckError=Η εργασία δεν είναι επεξεργάσιμη γιατί εκτελείται μία άλλη εργασία και δεσμεύει αυτή την εργασία.
#XMSG: Task log parallel task runnig error
parallelTaskRunning=Μία αντίθετη εργασία εκτελείται ήδη.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Κατάσταση {0} κατά την εκτέλεση με ID συσχέτισης {1}.
#XMSG: Task log message successful takeover
successTakeover=Το υπόλοιπο κλείδωμα έπρεπε να ακυρωθεί. Το νέο κλείδωμα για αυτή την εργασία καθορίστηκε.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Το κλείδωμα αυτής της εργασίας αντικαταστάθηκε από άλλη εργασία.
#XMSG: Schedule created alert message
createScheduleSuccess=Πρόγραμμα δημιουργήθηκε
#XMSG: Task log message for taskFinishedAt
taskFinishedAt=Η εργασία {0} ολοκληρώθηκε στις {2} με κατάσταση {1}.
#XMSG: Placeholder symbol for empty columns
emptyCol=--
#XMSG: Paused Text
txtExpired=Λήξη
#XMSG: Update task schedule success Toast message
updateScheduleSuccess=Πρόγραμμα ενημερώθηκε
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Πρόγραμμα διαγράφηκε.
#XMSG: Task chain might have children error text
taskChainNoChildErrorText=Η αλυσίδα εργασιών ίσως έχει δευτερεύουσες αλυσίδες που δεν εμφανίζονται γιατί η εργασία απέτυχε πριν δημιουργηθεί το πρόγραμμα.
#XMSG: Task chain repair recent failed run label
retryRunLabel=Δοκιμάστε ξανά την τελευταία εκτέλεση
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Η νέα προσπάθεια εκτέλεσης της αλυσίδας εργασιών άρχισε
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Η νέα προσπάθεια εκτέλεσης της αλυσίδας εργασιών απέτυχε
#XMSG: chain repair message
chainRetried=Η νέα προσπάθεια αλυσίδας εργασιών άρχισε από χρήστη {0}
#XMSG: chain not found during run
chainNotFoundDuringRun=Δεν βρέθηκε Αλυσίδα Εργασιών {0}. Ελέγξτε αν υπάρχει μέσω του Δημιουργού Δεδομένων και βεβαιωθείτε ότι η αλυσίδα αναπτύχθηκε.
#XMSG: chain is not DAG
chainNotDag=Αδύνατη έναρξη αλυσίδας εργασιών {0}. Η δομή της είναι άκυρη. Ελέγξτε την αλυσίδα εργασιών στον δημιουργό δεδομένων.
#XMSG: chain has not valid parameters
notValidParameters=Αδύνατη έναρξη αλυσίδας εργασιών {0}. Μία ή περισσότερες παράμετροι είναι άκυρες. Ελέγξτε την αλυσίδα εργασιών στον δημιουργό δεδομένων.
#XMSG: Configuration parameter value size exceeds the maximum allowed limit
configurationSizeLimitError=Αδύνατη έναρξη αλυσίδας εργασιών {0}. Το μέγεθος διαμόρφωσης εργασίας στην αλυσίδα υπερβαίνει το μέγιστο επιτρεπτό μέγεθος των 100 kibibytes (KiB).
#XMSG: Task {0} has input parameters
taskHasInputParameters=Η εργασία {0} έχει παραμέτρους εισόδου.
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Μία αντίθετη εργασία εκτελείται ήδη
#XMSG: error message for reading data from backend
txtReadBackendError=Μάλλον υπήρχε σφάλμα κατά την ανάγνωση από το back end.
##XMSG: error message for admission control rejection
admissionControlError=Η εργασία απέτυχε λόγω Απόρριψης Ελέγχου Εισαγωγής SAP HANA.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Αντιστοίχιση Προγράμματος σε Εμένα
#XBUT: Pause schedule menu label
pauseScheduleLabel=Διακοπή προγράμματος
#XBUT: Resume schedule menu label
resumeScheduleLabel=Επανέναρξη Προγράμματος
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Σφάλμα κατά την διαγραφή προγραμμάτων.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Σφάλμα κατά την αντιστοίχιση προγραμμάτων.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Σφάλμα κατά την διακοπή προγραμμάτων.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Σφάλμα κατά την επανέναρξη προγραμμάτων.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Διαγραφή {0} προγραμμάτων
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Αλλαγή ιδιοκτήτη {0} προγραμμάτων
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Διακοπή {0} προγραμμάτων
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Επανέναρξη {0} προγραμμάτων
#XBUT: Select Columns Button
selectColumnsBtn=Επιλογή Στηλών
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Μάλλον υπήρχε σφάλμα με την τελευταία εκτέλεση καθώς η προηγούμενη εργασία απέτυχε πριν δημιουργηθεί το πρόγραμμα.
#XFLD: Refresh tooltip
TEXT_REFRESH=Ανανέωση
#XFLD: Select Columns tooltip
text_selectColumns=Επιλογή Στηλών

#Messages for BW Process Chain task log of the Data Integration Monitor
#XMSG: Task log message for successfully starting a BW process chain
startBwProcessChainSuccess=Η αλυσίδα διαδικασιών BW "{0}" άρχισε επιτυχώς στον μισθωτή SAP BW Bridge.
#XMSG: Task log message for skipping a BW process chain execution
bwProcessChainSkipped=Η αλυσίδα διαδικασιών BW "{0}" παραβλέφθηκε είτε λόγω μη διαθεσιμότητας των νέων δεδομένων ή αποτυχίας μίας προηγούμενης εκτέλεσης.
#XMSG: Task log message for failure in starting a BW process chain
startBwProcessChainFailure=Η αλυσίδα διαδικασιών BW "{0}" δεν μπόρεσε να αρχίσει στον μισθωτή SAP BW Bridge.
#XMSG: Task log message for failure in getting the status of a BW process chain
getBwProcessChainStatusFailure=Η εργασία απέτυχε καθώς δεν μπορέσαμε να ανακτήσουμε την κατάσταση της αλυσίδας διαδικασιών BW "{0}" λόγω προβλήματος στην σύνδεση με το SAP BW Bridge. Ανοίξτε την εφαρμογή "Συνδέσεις" και επαληθεύστε τη σύνδεση SAP BW Bridge στον χώρο "{1}". Αν δεν έχετε πρόσβαση στην εφαρμογή "Συνδέσεις", επικοινωνήστε με τον διαχειριστή σας.
#XMSG: Task log message for failure in completing a BW process chain
completeBwProcessChainFailure=Η αλυσίδα διαδικασιών BW "{0}" δεν μπόρεσε  να ολοκληρωθεί στον μισθωτή SAP BW Bridge.
#XMSG: Task log message for the clickable text of the SAP BW Bridge Monitor link
bwBridgeMonitorLinkText=Προβολή λεπτομερειών στην Οθόνη SAP BW Bridge 
#XMSG warning message while retrieving the task log messages if the user does not have access to the SAP BW Bridge space or to the shared object's space
missingPrivilegeOnSpaceError=Υπήρχε πρόβλημα στην ανάκτηση μηνυμάτων ή δεν έχετε την απαιτούμενη άδεια για να τα δείτε.

#Messages for API task log of the Data Integration Monitor
#XMSG: Task log message for displaying the API response
viewResponse=Απάντηση Ελήφθη για "{0}".
#XMSG: Task log message for empty ID in the API response to retrieve the status
emptyJobId=Δεν εμφανίστηκε ID στο κείμενο απάντησης για την διαδρομή JSON: "{0}".
#XMSG: Task log message for empty success value in the API response
emptySuccessValue=Δεν εμφανίστηκε τιμή επιτυχίας στο κείμενο απάντησης για την διαδρομή JSON: "{0}".
#XMSG: Task log message for empty error value in the API response
emptyErrorValue=Δεν εμφανίστηκε τιμή σφάλματος στο κείμενο απάντησης για την διαδρομή JSON: "{0}".
#XMSG: Task log message for none of the success/error conditions matching for the values in the API response
conditionsNotFulfilled=Δεν ταιριάζουν οι καθορισμένες συνθήκες δείκτη σφάλματος ή επιτυχίας με τις τιμές στην απάντηση.
#XMSG: Task log message for the API response status being unsuccessful
statusNotOk=Η απάντηση "{1}" εμφάνισε ανεπιτυχή κώδικα κατάστασης: {0}.
#XMSG: Task log message for the API response body being empty
emptyResponseBody=Το κείμενο απάντησης ήταν κενό.
#XMSG: Task log message for the API response location header being invalid
emptyLocationHeader=Η κεφαλίδα τοποθεσίας στην απάντηση ήταν κενή.
#XMSG: Task log message for failure in retrieving the status of the invoked API
getStatusFailure=Η κατάσταση του ανακληθέντος ΑΡΙ δεν ανακτήθηκε.
#XMSG: Task log message for failure in completing the API task
completionFailure=Η εργασία ΑΡΙ  "{0}" δεν μπόρεσε να ολοκληρωθεί.
#XMSG: Task log message for a successful API task completion
apiCompleted=Η εργασία ΑΡΙ "{0}" έχει ολοκληρωθεί επιτυχώς.
#XMSG: Task log message for an API task with an invalid configuration
invalidConfiguration=Η διαμόρφωση εργασίας ΑΡΙ "{0}" είναι άκυρη. Ελέγξτε τη διαμόρφωση και δοκιμάστε ξανά.
#XMSG: Task log message for the API task being canceled
cancelStart=Απαιτούμενη ακύρωση της εργασίας ΑΡΙ  "{0}" με logId {1}.
#XMSG: Task log message for the cancelation of the an API task which is no longer running and cannot be canceled
unableToCancel=Η εργασία ΑΡΙ "{0}" με logId {1} δεν εκτελείται πλέον και δεν μπορεί να ακυρωθεί.
#XMSG: Task log message when starting an API task. It should carry the name of the API task.
apiRunStart=Έναρξη εργασίας ΑΡΙ "{0}".
#XMSG: Task log message when the task preparation is taking too long and times out
preparationTimeout=Η προετοιμασία εργασίας διαρκεί πολύ και έχει διακοπεί.
#XMSG: Task log message when a task failed because a cancel task was triggered
apiRunCancelled=Η εργασία εκτέλεσης με {0} logId ακυρώθηκε από εργασία ακύρωσης {1}.
#XMSG: Task log message when a cancel task failed to cancel a run task
cancelFailure=Η εργασία εκτέλεσης με {1} logId  απέτυχε να ακυρώσει την εργασία εκτέλεσης με logId {0}.
#XMSG: Task log message when configuration of the API task is too large
apiTaskconfigurationSizeLimitError=Η εργασία ΑΡΙ {0} δεν μπορεί να αρχίσει γιατί το μέγεθος διαμόρφωσης εργασίας στην αλυσίδα υπερβαίνει το μέγιστο επιτρεπτό μέγεθος των 100 kibibytes (KiB).
#XMSG: Task log message for failure in completing the Notification task
notificationTaskCompletionFailure=Η εργασία Ειδοποίησης "{0}" δεν ολοκληρώθηκε.
#XMSG: Task log message when starting a Notification task.
notificationTaskRunStart=Έναρξη εργασίας Ειδοποίησης "{0}".
#XMSG: Task log message for a successful Notification task completion
notificationTaskCompletionSuccess=Η εργασία Ειδοποίησης "{0}"  ολοκληρώθηκε.
#XFLD: Label for frequency column
everyLabel=Κάθε
#XFLD: Plural Recurrence text for Hour
hoursLabel=Ωρες
#XFLD: Plural Recurrence text for Day
daysLabel=Ημέρες
#XFLD: Plural Recurrence text for Month
monthsLabel=Μήνες
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Λεπτά
