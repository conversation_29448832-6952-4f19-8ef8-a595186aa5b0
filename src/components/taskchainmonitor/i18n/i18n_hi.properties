

#XFLD: Task Chain header
headerTxt=कार्य श्रृंखला({0})
#XFLD: Task Chain header
headerTxtNew=कार्य श्रृंखला ({0})
#XTXT: Text for Schedule label
scheduleTxt=शेड्यूल
#XFLD: Text for Create schedule button
createScheduleTxt=शेड्यूल बनाएं
#XFLD: Text for edit schedule
editScheduleTxt=शेड्यूल संपादित करें
#XLFD: Text for delete schedule
deleteScheduleTxt=शेड्यूल हटाएं
#XTXT: text for refresh button label
refrestTxt=रीफ़्रेश करें
#XTXT: Text for Completed status
completedTxt=पूर्ण किया गया
#XTX: Text for Running status
runningTxt=चल रहा है
#XTX: Text for failed status
failedTxt=विफल
#XTX: Text for stopped status
stoppedTxt=रोका गया
#XTX: Text for stopping status
stoppingTxt=रोका जा रहा है
#XFLD: Header for Chain name
chainNameLabel=कार्य श्रृंखला नाम
#XFLD: Header for Chain name
chainNameLabelBus=बिज़नेस का नाम
#XFLD: Header for Chain name
chainNameLabelBusNew=ऑब्जेक्ट (व्यवसाय का नाम)
#XFLD: Header for Chain name
chainNameLabelTech=तकनीकी नाम
#XFLD: Header for Chain name
chainNameLabelTechNew=ऑब्जेक्ट (तकनीकी नाम)
#XFLD: Last Run Status label
lastRunStatuslabel=रन करने की अंतिम स्थिति
#XFLD: Last Run Status label
lastRunStatuslabelNew=स्थिति
#XFLD: Frequency Label
frequencyLabel=आवृत्ति
#XFLD: Frequency Label
frequencyLabelNew=शेड्यूल की गई आवृत्ति
#XFLD: Duration label
durationLabel=अवधि
#XFLD: Duration label
durationLabelNew=अंतिम रन अवधि
#XFLD: Run Start label
runStartLabel=अंतिम बार रन प्रारंभ करें
#XFLD: Run end label
runEndLabel=अंतिम बार रन समाप्त करें
#XFLD: Next Run label
nextRunlabel=अगला रन
#XFLD: Next Run label
nextRunlabelNew=अगला रन शेड्यूल किया गया
#XTXT: Task chain logs link tooltip text
taskChainLogsTxt=कार्य श्रृंखला लॉग विवरण
#XTXT: Task chain logs link tooltip text
taskChainLogsTxtNew=विवरण
#XTXT: Scheduled text
scheduledTxt=शेड्यूल किया गया
#XTXT: Paused text
pausedTxt=रोका गया
#XTXT: Execute button label
runLabel=रन
#XTXT: Execute button label
runLabelNew=रन प्रारंभ करें
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=कार्य श्रृंखला रन करें
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=कार्य श्रृंखला रन आरंभ किया गया.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg={0} के लिए कार्य श्रृंखला रन प्रारंभ किया गया.
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=कार्य श्रृंखला रन करने में विफल हुआ.
#XFLD: Label for schedule owner column
txtScheduleOwner=शेड्यूल स्वामी
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=दिखाता है कि शेड्यूल किसने बनाया है
#XMSG: Task log message for start chain
startChain=कार्य श्रृंखला रन आरंभ करें.
#XMSG: Task log message for load chain from repository
loadChainFromRepository=कार्य श्रृंखला लोड किया गया और आरंभिक है.
#XMSG: Task log message started task
taskStarted=कार्य {0} आरंभ किया गया.
#XMSG: Task log message for finished task
taskFinished=कार्य {0} स्थिति {1} के साथ समाप्त हुआ.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=कार्य श्रृंखला लोड हो रही है और कुल {0} कार्यों को रन करने की तैयारी है जो इस श्रृंखला का हिस्सा हैं.
#XMSG: Task log message for starting a subtask
chainStartSubtask=रन करने के लिए {0} ट्रिगरिंग कार्य. कार्य id = {1}.
#XMSG: Task log message for finishing a subtask
chainFinishSubtask=स्थिति {1} के साथ कार्य {0} समाप्त किया गया.
#XMSG: Task log message for indicating chain success
chainCompleted=सभी {0} कार्यों को पूरा किया गया. कार्य श्रृंखला स्थिति पूर्ण करने के लिए सेट है.
#XMSG: Task log message for indicating chain failure
chainFailed={0} कार्यों के कुल पर, {1} कार्य पूर्ण नहीं किया जा सकता और {2} कार्य विफल हुआ. कार्य श्रृंखला स्थिति सेट करने में विफल हुआ.
#XMSG: Task log message for indicating chain cancelation
chainCanceled=कार्य शृंखला चलाने का निरस्तीकरण शुरू हो गया है रद्द कार्य का लॉग ID {0} है.
#XMSG: Task log message for indicating chain cancelation in the cancel task
chainCanceling=शृंखला {0} रद्द करना.
#XMSG: Task log message for general chain runtime error
chainError=कार्य श्रृंखला रन करने के दौरान एक अपेक्षित त्रुटि हुई.
#XMSG: Task log message for starting the overwrite check.
chainOverwriteStart=जांच कर रहा है कि id {0} के साथ कार्य श्रृंखला समाप्त हो गई है या नहीं.
#XMSG: Task log message indicating the chain is too old.
chainOverwriteTooOld=श्रृंखला {0} रन करने के लिए कोई कार्य नहीं मिला. रन {1} मिनट पुराना है. कार्य श्रृंखला स्थिति विफल पर सेट है.
#XMSG: Task log message indicating the chain was created too recently.
chainOverwriteTooRecent=श्रृंखला {0} रन करने के लिए कोई कार्य नहीं मिल सकता. रन {1} मिनट पुराना है. कार्य श्रृंखला स्थिति विफल पर सेट है.
#XMSG: Task log message indicating a recent heartbeat was found.
chainOverwriteHeartbeatAlive=कार्य श्रृंखला {0} अभी भी रन कर रहा है. पूर्ण की गईः {1}, रन कर रहा है: {2}, विफल हुआ: {3}, ट्रिगर किया गया: {4}, ट्रिगर नहीं किया गया: {5}.
#XMSG: Task log message for a chain that is not running anymore but not everything was triggered.
chainOverwriteNotAllTriggered=कार्य श्रृंखला {0} रन करते समय एक त्रुटि हुई, और सभी कार्यों को ट्रिगर नहीं किया जा सका पूर्ण {1}: , रन कर रहा है: {2}, विफल: {3}, ट्रिगर किया गया: {4}, ट्रिगर नहीं हुआ: {5}. यह कार्य श्रृंखला अधिलेखित कर दी गई है.
#XMSG: Task log message for a chain that contains a failed subtask.
chainOverwriteAnySubtaskFailed=कार्य श्रृंखला {0} समाप्त किया गया. श्रृंखला का एक कार्य विफल हुआ, सेटिंग श्रृंखला विफल हुआ.
#XMSG: Task log message could not determine status.
chainOverwriteNotDetermined=एक अनपेक्षित त्रुटि हमें कार्य की स्थिति तक पहुंचने से रोकती है. पुन: प्रयास करें और यदि त्रुटि बनी रहती है तो अपने SAP समर्थन से संपर्क करें.
#XMSG: Task log message could not take over
failedTakeover=किसी मौजूदा कार्य को टेकओवर करने में विफ़ल.
#XMSG: Task log parallel check error
parallelCheckError=कार्य को संसाधित नहीं किया जा सकता क्योंकि कोई अन्य कार्य रन हो रहा है और पहले से ही इस कार्य को अवरुद्ध कर रहा है.
#XMSG: Task log parallel task runnig error
parallelTaskRunning=एक विवादित कार्य पहले से ही रन हो रहा है.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=सहसंबंध ID {1} के साथ रन करने के दौरान स्थिति {0}.
#XMSG: Task log message successful takeover
successTakeover=रिलीज़ होने वाला, लॉक किया हुआ कार्य अनलॉक हुआ. इस कार्य के लिए नए लॉक सेट है.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=इस कार्य के लॉक को दूसरे कार्य ने अपने हाथ में ले लिया.
#XMSG: Schedule created alert message
createScheduleSuccess=शेड्यूल बनाए गए
#XMSG: Task log message for taskFinishedAt
taskFinishedAt=स्थिति {1} के साथ {2} को कार्य {0} समाप्त किया गया.
#XMSG: Placeholder symbol for empty columns
emptyCol=--
#XMSG: Paused Text
txtExpired=समाप्त हुआ
#XMSG: Update task schedule success Toast message
updateScheduleSuccess=शेड्यूल अपडेट किए गए
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=शेड्यूल हटाया गया.
#XMSG: Task chain might have children error text
taskChainNoChildErrorText=कार्य श्रृंखला में ऐसे बच्चे हो सकते हैं जिन्हें दिखाया नहीं गया है क्योंकि योजना जनरेट होने से पहले कार्य विफल हो गया था.
#XMSG: Task chain repair recent failed run label
retryRunLabel=नवीनतम रन पुनःप्रयास करें
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=कार्य श्रृंखला पुनःप्रयास रन आरंभ किया गया
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=कार्य श्रृंखला पुनः प्रयास रन विफल हुआ
#XMSG: chain repair message
chainRetried=उपयोगकर्ता कार्य श्रृंखला पुनः प्रयास ट्रिगर किया गया {0}
#XMSG: chain not found during run
chainNotFoundDuringRun=कार्य श्रृंखला {0} नहीं ढूंढ पाए. जांच करें कि यह डेटा बिल्डर द्वारा मौजूद है या नहीं और सुनिश्चित करें कि श्रृंखला तैनात है.
#XMSG: chain is not DAG
chainNotDag=कार्य श्रृंखला {0} प्रारंभ नहीं हो सकती. इसकी संरचना अमान्य है. डेटा बिल्डर में कार्य श्रृंखला की जांच करें.
#XMSG: chain has not valid parameters
notValidParameters=कार्य शृंखला {0} शुरू नहीं की जा सकती. इसके एक या एक से अधिक पैरामीटर अमान्य हैं. डेटा बिल्डर में कार्य शृंखला की जांच करें.
#XMSG: Configuration parameter value size exceeds the maximum allowed limit
configurationSizeLimitError=कार्य श्रृंखला {0} प्रारंभ नहीं हो सकती . श्रृंखला में  कार्य के कॉन्फ़िगरेशन का आकार अधिकतम स्वीकृत आकार 100 किबिबाइट्स (KiB) से अधिक है.
#XMSG: Task {0} has input parameters
taskHasInputParameters=कार्य {0} में इनपुट पैरामीटर है.
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=एक विवादित कार्य पहले से ही रन हो रहा है.
#XMSG: error message for reading data from backend
txtReadBackendError=ऐसा लगता है कि वापस पीछे से पढ़ते समय कोई त्रुटि हुई है.
##XMSG: error message for admission control rejection
admissionControlError=SAP HANA प्रवेश नियंत्रण अस्वीकृति के कारण कार्य विफल हो गया.

#XBUT: Assign schedule menu button label
assignScheduleLabel=मुझे शेड्यूल असाइन करें
#XBUT: Pause schedule menu label
pauseScheduleLabel=शेड्यूल रोकें
#XBUT: Resume schedule menu label
resumeScheduleLabel=शेड्यूल फिर से शुरू करें
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=शेड्यूल निकालते समय त्रुटि उत्पन्न हुई.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=शेड्यूल असाइन करते समय त्रुटि उत्पन्न हुई.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=शेड्यूल रोकते समय त्रुटि उत्पन्न हुई.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=शेड्यूल फिर से शुरू करते समय त्रुटि उत्पन्न हुई.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted={0} शेड्यूल हटाना
#XMSG: Message for starting mass assign of schedules
massAssignStarted={0} शेड्यूल के स्वामी में परिवर्तन करना
#XMSG: Message for starting mass pausing of schedules
massPauseStarted={0} शेड्यूल रोकना
#XMSG: Message for starting mass resuming of schedules
massResumeStarted={0} शेड्यूल फिर से शुरू करना
#XBUT: Select Columns Button
selectColumnsBtn=स्तंभों का चयन करें
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=ऐसा लगता है कि नवीनतम रन के पुन: प्रयास में कोई त्रुटि थी क्योंकि योजना जनरेट होने से पहले पिछला कार्य रन विफल हो गया था.
#XFLD: Refresh tooltip
TEXT_REFRESH=रीफ़्रेश करें
#XFLD: Select Columns tooltip
text_selectColumns=स्तंभों का चयन करें

#Messages for BW Process Chain task log of the Data Integration Monitor
#XMSG: Task log message for successfully starting a BW process chain
startBwProcessChainSuccess=SAP BW Bridge टेनेंट में BW प्रक्रिया श्रृंखला "{0}" सफलतापूर्वक प्रारंभ हो गई है.
#XMSG: Task log message for skipping a BW process chain execution
bwProcessChainSkipped=नए डेटा की अनुपलब्धता या पिछले निष्पादन की विफलता के कारण  BW प्रक्रिया श्रृंखला  "{0}" को छोड़ दिया गया था.
#XMSG: Task log message for failure in starting a BW process chain
startBwProcessChainFailure=SAP BW Bridge टेनेंट में BW प्रक्रिया श्रृंखला "{0}" प्रारंभ होने में विफल रही है.
#XMSG: Task log message for failure in getting the status of a BW process chain
getBwProcessChainStatusFailure=कार्य विफल हो गया क्योंकि हम SAP BW Bridge से कनेक्शन स्थापित करने में समस्या के कारण BW प्रक्रिया श्रृंखला "{0}" की स्थिति पुनः प्राप्त करने में असमर्थ थे. "कनेक्शन" ऐप खोलें और  "{1}" स्पेस में SAP BW Bridge कनेक्शन को सत्यापित करें. यदि आपके पास "कनेक्शन" ऐप तक पहुँच नहीं है, तो कृपया अपने व्यवस्थापक से संपर्क करें.
#XMSG: Task log message for failure in completing a BW process chain
completeBwProcessChainFailure=SAP BW Bridge टेनेंट में BW प्रक्रिया श्रृंखला "{0}" पूर्ण होने में विफल रही है.
#XMSG: Task log message for the clickable text of the SAP BW Bridge Monitor link
bwBridgeMonitorLinkText=SAP BW Bridge Monitor में विवरण देखें
#XMSG warning message while retrieving the task log messages if the user does not have access to the SAP BW Bridge space or to the shared object's space
missingPrivilegeOnSpaceError=संदेशों को पुनः प्राप्त करने में समस्या थी, या आपके पास उन्हें देखने के लिए आवश्यक अनुमति नहीं है.

#Messages for API task log of the Data Integration Monitor
#XMSG: Task log message for displaying the API response
viewResponse="{0}" के लिए प्रतिक्रिया प्राप्त हुई.
#XMSG: Task log message for empty ID in the API response to retrieve the status
emptyJobId=JSON पथ के लिए प्रतिक्रिया निकाय में कोई ID नहीं लौटाई गई: ""{0}.
#XMSG: Task log message for empty success value in the API response
emptySuccessValue=JSON पथ के लिए प्रतिक्रिया निकाय में कोई सफलता मान नहीं लौटाया गया: "{0}".
#XMSG: Task log message for empty error value in the API response
emptyErrorValue=JSON पथ के लिए प्रतिक्रिया निकाय में कोई त्रुटि मान नहीं लौटाया गया: "{0}".
#XMSG: Task log message for none of the success/error conditions matching for the values in the API response
conditionsNotFulfilled=निर्दिष्ट सफलता या त्रुटि संकेतक स्थितियों में से कोई भी प्रतिक्रिया में मूल्यों से मिलान नहीं करती है.
#XMSG: Task log message for the API response status being unsuccessful
statusNotOk="{1}" के लिए प्रतिसाद ने एक असफल स्थिति कोड लौटाया: {0}
#XMSG: Task log message for the API response body being empty
emptyResponseBody=प्रतिक्रिया निकाय रिक्त था.
#XMSG: Task log message for the API response location header being invalid
emptyLocationHeader=प्रतिक्रिया में स्थान शीर्षलेख रिक्त था.
#XMSG: Task log message for failure in retrieving the status of the invoked API
getStatusFailure=लागू की गई API की स्थिति पुनर्प्राप्त नहीं की जा सकी.
#XMSG: Task log message for failure in completing the API task
completionFailure=API कार्य "{0}" पूरा होने में विफल रहा है.
#XMSG: Task log message for a successful API task completion
apiCompleted=API कार्य "{0}" सफलतापूर्वक पूरा हो गया है.
#XMSG: Task log message for an API task with an invalid configuration
invalidConfiguration=API कार्य "{0}" का कॉन्फ़िगरेशन अमान्य है. कृपया कॉन्फ़िगरेशन की जांच करें और पुनः प्रयास करें.
#XMSG: Task log message for the API task being canceled
cancelStart=लॉग Id {1}. वाला API कार्य "{0}"को रद्द करने का अनुरोध किया गया.
#XMSG: Task log message for the cancelation of the an API task which is no longer running and cannot be canceled
unableToCancel=लॉग Id  {1} वाला API कार्य "{0}" अब नहीं चल रहा है और उसे रद्द नहीं किया जा सकता.
#XMSG: Task log message when starting an API task. It should carry the name of the API task.
apiRunStart=API कार्य {0}" प्रारंभ करना.
#XMSG: Task log message when the task preparation is taking too long and times out
preparationTimeout=कार्य की तैयारी में बहुत अधिक समय लग रहा है और समय समाप्त हो गया है.
#XMSG: Task log message when a task failed because a cancel task was triggered
apiRunCancelled=लॉगId {0} के साथ रन कार्य {1}रद्द कार्य द्वारा रद्द कर दिया गया था.
#XMSG: Task log message when a cancel task failed to cancel a run task
cancelFailure=लॉगId {1}के साथ रद्द कार्य logId {0}के साथ चलाए जा रहे कार्य को रद्द करने में विफल रहा.
#XMSG: Task log message when configuration of the API task is too large
apiTaskconfigurationSizeLimitError=API कार्य {0} प्रारंभ नहीं हो सकता क्योंकि इसके कार्य कॉन्फ़िगरेशन का आकार अधिकतम स्वीकृत आकार 100 किबिबाइट्स (KiB) से अधिक है.
#XMSG: Task log message for failure in completing the Notification task
notificationTaskCompletionFailure=अधिसूचना कार्य "{0}" पूरा होने में विफल रहा है.
#XMSG: Task log message when starting a Notification task.
notificationTaskRunStart=अधिसूचना कार्य "{0}" का प्रारंभ होना
#XMSG: Task log message for a successful Notification task completion
notificationTaskCompletionSuccess=अधिसूचना कार्य "{0}" पूरा हो चुका है.
#XFLD: Label for frequency column
everyLabel=प्रत्येक
#XFLD: Plural Recurrence text for Hour
hoursLabel=घंटे
#XFLD: Plural Recurrence text for Day
daysLabel=दिन
#XFLD: Plural Recurrence text for Month
monthsLabel=महीने
#XFLD: Plural Recurrence text for Minutes
minutesLabel=मिनट
