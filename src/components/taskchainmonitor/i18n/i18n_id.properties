

#XFLD: Task Chain header
headerTxt=<PERSON><PERSON><PERSON>({0})
#XFLD: Task Chain header
headerTxtNew=<PERSON><PERSON><PERSON> ({0})
#XTXT: Text for Schedule label
scheduleTxt=Jadwal
#XFLD: Text for Create schedule button
createScheduleTxt=Buat Jadwal
#XFLD: Text for edit schedule
editScheduleTxt=Edit Jadwal
#XLFD: Text for delete schedule
deleteScheduleTxt=Hapus Permanen Jadwal
#XTXT: text for refresh button label
refrestTxt=Segarkan
#XTXT: Text for Completed status
completedTxt=Selesai
#XTX: Text for Running status
runningTxt=Sedang Dieksekusi
#XTX: Text for failed status
failedTxt=Gagal
#XTX: Text for stopped status
stoppedTxt=Be<PERSON>henti
#XTX: Text for stopping status
stoppingTxt=Menghentikan
#XFLD: Header for Chain name
chainNameLabel=Nama Rantai Tugas
#XFLD: Header for Chain name
chainNameLabelBus=Nama Bisnis
#XFLD: Header for Chain name
chainNameLabelBusNew=Objek (Nama Bisnis)
#XFLD: Header for Chain name
chainNameLabelTech=Nama Teknis
#XFLD: Header for Chain name
chainNameLabelTechNew=Objek (Nama Teknis)
#XFLD: Last Run Status label
lastRunStatuslabel=Status Eksekusi Terakhir
#XFLD: Last Run Status label
lastRunStatuslabelNew=Status
#XFLD: Frequency Label
frequencyLabel=Frekuensi
#XFLD: Frequency Label
frequencyLabelNew=Frekuensi yang Dijadwalkan
#XFLD: Duration label
durationLabel=Durasi
#XFLD: Duration label
durationLabelNew=Durasi Eksekusi Terakhir
#XFLD: Run Start label
runStartLabel=Mulai Eksekusi Terakhir
#XFLD: Run end label
runEndLabel=Berakhir Eksekusi Terakhir
#XFLD: Next Run label
nextRunlabel=Eksekusi Selanjutnya
#XFLD: Next Run label
nextRunlabelNew=Eksekusi Selanjutnya yang Dijadwalkan
#XTXT: Task chain logs link tooltip text
taskChainLogsTxt=Rincian Log Rantai Tugas
#XTXT: Task chain logs link tooltip text
taskChainLogsTxtNew=Rincian
#XTXT: Scheduled text
scheduledTxt=Dijadwalkan
#XTXT: Paused text
pausedTxt=Dijeda
#XTXT: Execute button label
runLabel=Eksekusi
#XTXT: Execute button label
runLabelNew=Mulai Eksekusi
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Eksekusi Rantai Tugas
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Eksekusi rantai tugas telah dimulai.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Eksekusi rantai tugas telah dimulai untuk {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Gagal mengeksekusi rantai tugas.
#XFLD: Label for schedule owner column
txtScheduleOwner=Pemilik Jadwal
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Menampilkan orang yang membuat jadwal
#XMSG: Task log message for start chain
startChain=Memulai eksekusi rantai tugas.
#XMSG: Task log message for load chain from repository
loadChainFromRepository=Rantai tugas dimuat dan dimulai.
#XMSG: Task log message started task
taskStarted=Tugas {0} telah dimulai.
#XMSG: Task log message for finished task
taskFinished=Tugas {0} berakhir dengan status {1}.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Memuat rantai tugas dan menyiapkan untuk mengeksekusi jumlah {0} tugas yang menjadi bagian rantai ini.
#XMSG: Task log message for starting a subtask
chainStartSubtask=Memicu tugas {0} untuk dieksekusi. Id task = {1}.
#XMSG: Task log message for finishing a subtask
chainFinishSubtask=Tugas {0} selesai dengan status {1}.
#XMSG: Task log message for indicating chain success
chainCompleted=Semua {0} tugas selesai. Status rantai tugas ditetapkan ke selesai.
#XMSG: Task log message for indicating chain failure
chainFailed=Dalam jumlah {0} tugas, {1} tugas dapat diselesaikan dan {2} tugas gagal. Status rantai tugas ditetapkan ke gagal.
#XMSG: Task log message for indicating chain cancelation
chainCanceled=Pembatalan eksekusi rantai tugas telah dimulai. Id log dari tugas pembatalan adalah {0}.
#XMSG: Task log message for indicating chain cancelation in the cancel task
chainCanceling=Membatalkan rantai {0}.
#XMSG: Task log message for general chain runtime error
chainError=Terjadi kesalahan yang tak terduga ketika mengeksekusi rantai tugas.
#XMSG: Task log message for starting the overwrite check.
chainOverwriteStart=Memeriksa apakah rantai tugas dengan id {0} telah selesai.
#XMSG: Task log message indicating the chain is too old.
chainOverwriteTooOld=Tidak ada tugas dieksekusi untuk rantai {0} yang dapat ditemukan. Eksekusi berjalan selama {1} menit. Status rantai tugas ditetapkan ke gagal.
#XMSG: Task log message indicating the chain was created too recently.
chainOverwriteTooRecent=Tidak ada tugas dieksekusi untuk rantai {0} yang dapat ditemukan. Eksekusi berjalan selama {1} menit. Rantai tugas masih berjalan.
#XMSG: Task log message indicating a recent heartbeat was found.
chainOverwriteHeartbeatAlive=Rantai tugas {0} masih dieksekusi. Selesai: {1}, Sedang Dieksekusi: {2}, Gagal:{3}, Dipicu: {4}, Tidak dipicu: {5}.
#XMSG: Task log message for a chain that is not running anymore but not everything was triggered.
chainOverwriteNotAllTriggered=Terjadi kesalahan saat mengeksekusi rantai tugas {0}, dan tidak semua tugas dapat dipicu. Selesai: {1}, Sedang Dieksekusi: {2}, Gagal: {3}, Dipicu: {4}, Tidak dipicu: {5}. Rantai tugas ini telah ditimpa.
#XMSG: Task log message for a chain that contains a failed subtask.
chainOverwriteAnySubtaskFailed=Rantai tugas {0} selesai. Satu tugas dari rantai gagal, rantai ditetapkan ke gagal.
#XMSG: Task log message could not determine status.
chainOverwriteNotDetermined=Terjadi kesalahan tidak terduga yang mencegah untuk mengakses status tugas. Coba lagi dan hubungi Dukungan SAP Anda jika masalah tetap berlanjut.
#XMSG: Task log message could not take over
failedTakeover=Gagal mengambil alih tugas yang sudah ada.
#XMSG: Task log parallel check error
parallelCheckError=Tugas tidak dapat diproses karena tugas lain sedang dieksekusi dan telah memblokir tugas ini.
#XMSG: Task log parallel task runnig error
parallelTaskRunning=Tugas yang bertentangan sudah dieksekusi.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Status {0} selama dieksekusi dengan ID korelasi {1}.
#XMSG: Task log message successful takeover
successTakeover=Kunci lama harus dirilis. Kunci baru ditetapkan untuk tugas ini.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Kunci tugas ini diambil alih oleh tugas lain.
#XMSG: Schedule created alert message
createScheduleSuccess=Jadwal dibuat
#XMSG: Task log message for taskFinishedAt
taskFinishedAt=Tugas {0} selesai pada {2} dengan status {1}.
#XMSG: Placeholder symbol for empty columns
emptyCol=--
#XMSG: Paused Text
txtExpired=Kedaluwarsa
#XMSG: Update task schedule success Toast message
updateScheduleSuccess=Jadwal diperbarui
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Jadwal dihapus permanen.
#XMSG: Task chain might have children error text
taskChainNoChildErrorText=Rantai tugas mungkin memiliki turunan yang tidak ditampilkan karena tugas mengalami kegagalan sebelum rencana dapat dibuat.
#XMSG: Task chain repair recent failed run label
retryRunLabel=Coba Ulang Eksekusi Terakhir
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Eksekusi coba ulang rantai tugas telah dimulai
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Eksekusi coba ulang rantai tugas telah gagal
#XMSG: chain repair message
chainRetried=Coba ulang rantai tugas yang dipicu oleh pengguna {0}
#XMSG: chain not found during run
chainNotFoundDuringRun=Tidak dapat menemukan rantai tugas {0}. Periksa apakah rantai tugas ada melalui Data Builder dan pastikan rantai disebarkan.
#XMSG: chain is not DAG
chainNotDag=Tidak dapat memulai rantai tugas {0}. Strukturnya tidak valid. Periksa rantai tugas di Data Builder.
#XMSG: chain has not valid parameters
notValidParameters=Tidak dapat memulai rantai tugas {0}. Satu atau beberapa parameternya tidak valid. Periksa rantai tugas di Data Builder.
#XMSG: Configuration parameter value size exceeds the maximum allowed limit
configurationSizeLimitError=Tidak dapat memulai rantai tugas {0}. Ukuran konfigurasi tugas dalam rantai melebihi ukuran maksimum yang diperbolehkan yaitu 100 kibibita (KiB).
#XMSG: Task {0} has input parameters
taskHasInputParameters=Tugas {0} memiliki parameter input.
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Tugas yang bertentangan sudah dieksekusi
#XMSG: error message for reading data from backend
txtReadBackendError=Tampaknya ada kesalahan saat membaca dari back end.
##XMSG: error message for admission control rejection
admissionControlError=Tugas gagal karena adanya Penolakan Kontrol Penerimaan SAP HANA.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Tetapkan Jadwal untuk Saya
#XBUT: Pause schedule menu label
pauseScheduleLabel=Jeda Jadwal
#XBUT: Resume schedule menu label
resumeScheduleLabel=Lanjutkan Jadwal
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Terjadi kesalahan saat menghapus jadwal.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Terjadi kesalahan saat menetapkan jadwal.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Terjadi kesalahan saat menjeda jadwal.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Terjadi kesalahan saat melanjutkan jadwal.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Menghapus permanen {0} jadwal
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Mengubah pemilik {0} jadwal
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Menjeda {0} jadwal
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Melanjutkan {0} jadwal
#XBUT: Select Columns Button
selectColumnsBtn=Pilih Kolom
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Tampaknya ada kesalahan saat mencoba kembali mengeksekusi tugas yang terbaru karena eksekusi tugas sebelumnya gagal sebelum rencana dapat dibuat.
#XFLD: Refresh tooltip
TEXT_REFRESH=Segarkan
#XFLD: Select Columns tooltip
text_selectColumns=Pilih Kolom

#Messages for BW Process Chain task log of the Data Integration Monitor
#XMSG: Task log message for successfully starting a BW process chain
startBwProcessChainSuccess=Rantai proses BW "{0}" telah berhasil dimulai dalam penyewa SAP BW Bridge.
#XMSG: Task log message for skipping a BW process chain execution
bwProcessChainSkipped=Rantai proses BW "{0}" dilewati karena tidak tersedianya data baru atau terjadi kegagalan pada proses pelaksanaan sebelumnya.
#XMSG: Task log message for failure in starting a BW process chain
startBwProcessChainFailure=Rantai proses BW "{0}" telah gagal dimulai dalam penyewa SAP BW Bridge.
#XMSG: Task log message for failure in getting the status of a BW process chain
getBwProcessChainStatusFailure=Tugas gagal diselesaikan karena kami tidak dapat mengambil status rantai proses BW "{0}" akibat terjadi masalah saat mengaktifkan koneksi ke SAP BW Bridge. Buka aplikasi "Koneksi" dan validasi koneksi SAP BW Bridge di ruang "{1}". Jika Anda tidak memiliki akses ke aplikasi "Koneksi," silakan hubungi administrator Anda.
#XMSG: Task log message for failure in completing a BW process chain
completeBwProcessChainFailure=Rantai proses BW "{0}" telah gagal diselesaikan dalam penyewa SAP BW Bridge.
#XMSG: Task log message for the clickable text of the SAP BW Bridge Monitor link
bwBridgeMonitorLinkText=Lihat rincian di Pemantau SAP BW Bridge
#XMSG warning message while retrieving the task log messages if the user does not have access to the SAP BW Bridge space or to the shared object's space
missingPrivilegeOnSpaceError=Terjadi masalah saat memuat pesan, atau Anda tidak memiliki izin yang diperlukan untuk melihatnya.

#Messages for API task log of the Data Integration Monitor
#XMSG: Task log message for displaying the API response
viewResponse=Respons diterima untuk "{0}".
#XMSG: Task log message for empty ID in the API response to retrieve the status
emptyJobId=Tidak ada ID yang dikembalikan dalam isi respons untuk jalur JSON: "{0}".
#XMSG: Task log message for empty success value in the API response
emptySuccessValue=Tidak ada nilai keberhasilan yang dikembalikan dalam isi respons untuk jalur JSON: "{0}".
#XMSG: Task log message for empty error value in the API response
emptyErrorValue=Tidak ada nilai kesalahan yang dikembalikan dalam isi respons untuk jalur JSON: "{0}".
#XMSG: Task log message for none of the success/error conditions matching for the values in the API response
conditionsNotFulfilled=Tidak ada kondisi indikator keberhasilan atau kesalahan yang ditentukan yang cocok dengan nilai yang dikembalikan dalam respons
#XMSG: Task log message for the API response status being unsuccessful
statusNotOk=Respons untuk "{1}" menghasilkan kode status kegagalan: {0}.
#XMSG: Task log message for the API response body being empty
emptyResponseBody=Isi respons kosong.
#XMSG: Task log message for the API response location header being invalid
emptyLocationHeader=Header lokasi dalam respons kosong.
#XMSG: Task log message for failure in retrieving the status of the invoked API
getStatusFailure=Status API yang dipanggil tidak dapat diambil.
#XMSG: Task log message for failure in completing the API task
completionFailure=Tugas API "{0}" gagal diselesaikan.
#XMSG: Task log message for a successful API task completion
apiCompleted=Tugas API "{0}" berhasil diselesaikan.
#XMSG: Task log message for an API task with an invalid configuration
invalidConfiguration=Konfigurasi tugas API "{0}" tidak valid. Silakan periksa konfigurasinya dan coba lagi.
#XMSG: Task log message for the API task being canceled
cancelStart=Permintaan pembatalan tugas API "{0}" dengan logId {1}.
#XMSG: Task log message for the cancelation of the an API task which is no longer running and cannot be canceled
unableToCancel=Tugas API "{0}" dengan logId {1} telah berhenti dieksekusi dan tidak dapat dibatalkan.
#XMSG: Task log message when starting an API task. It should carry the name of the API task.
apiRunStart=Memulai tugas API "{0}".
#XMSG: Task log message when the task preparation is taking too long and times out
preparationTimeout=Persiapan tugas memakan waktu terlalu lama dan melewati batas waktu.
#XMSG: Task log message when a task failed because a cancel task was triggered
apiRunCancelled=Tugas yang dieksekusi dengan logId {0} dibatalkan karena permintaan pembatalan tugas {1}.
#XMSG: Task log message when a cancel task failed to cancel a run task
cancelFailure=Permintaan pembatalan tugas dengan logId {1} gagal membatalkan tugas yang sedang dieksekusi dengan logId {0}.
#XMSG: Task log message when configuration of the API task is too large
apiTaskconfigurationSizeLimitError=Tugas API {0} tidak dapat dimulai karena ukuran konfigurasi tugasnya melebihi ukuran maksimum yang diperbolehkan yaitu 100 kibibita (KiB).
#XMSG: Task log message for failure in completing the Notification task
notificationTaskCompletionFailure=Tugas Pemberitahuan "{0}" gagal diselesaikan.
#XMSG: Task log message when starting a Notification task.
notificationTaskRunStart=Memulai tugas Pemberitahuan "{0}".
#XMSG: Task log message for a successful Notification task completion
notificationTaskCompletionSuccess=Tugas Pemberitahuan "{0}" telah selesai.
#XFLD: Label for frequency column
everyLabel=Setiap
#XFLD: Plural Recurrence text for Hour
hoursLabel=Jam
#XFLD: Plural Recurrence text for Day
daysLabel=Hari
#XFLD: Plural Recurrence text for Month
monthsLabel=Bulan
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Menit
