

#XFLD: Task Chain header
headerTxt=Cadeias de tarefas ({0})
#XFLD: Task Chain header
headerTxtNew=Cadeias de tarefas ({0})
#XTXT: Text for Schedule label
scheduleTxt=Programar
#XFLD: Text for Create schedule button
createScheduleTxt=Criar programação
#XFLD: Text for edit schedule
editScheduleTxt=Editar programação
#XLFD: Text for delete schedule
deleteScheduleTxt=Excluir programação
#XTXT: text for refresh button label
refrestTxt=Atualizar
#XTXT: Text for Completed status
completedTxt=Concluído
#XTX: Text for Running status
runningTxt=Em execução
#XTX: Text for failed status
failedTxt=Com falha
#XTX: Text for stopped status
stoppedTxt=Interrompido
#XTX: Text for stopping status
stoppingTxt=Interrompendo
#XFLD: Header for Chain name
chainNameLabel=Nome da cadeia de tarefas
#XFLD: Header for Chain name
chainNameLabelBus=Nome comercial
#XFLD: Header for Chain name
chainNameLabelBusNew=Objeto (nome comercial)
#XFLD: Header for Chain name
chainNameLabelTech=Nome técnico
#XFLD: Header for Chain name
chainNameLabelTechNew=Objeto (nome técnico)
#XFLD: Last Run Status label
lastRunStatuslabel=Status da última execução
#XFLD: Last Run Status label
lastRunStatuslabelNew=Status
#XFLD: Frequency Label
frequencyLabel=Frequência
#XFLD: Frequency Label
frequencyLabelNew=Frequência agendada
#XFLD: Duration label
durationLabel=Duração
#XFLD: Duration label
durationLabelNew=Duração da última execução
#XFLD: Run Start label
runStartLabel=Início da última execução
#XFLD: Run end label
runEndLabel=Término da última execução
#XFLD: Next Run label
nextRunlabel=Execução seguinte
#XFLD: Next Run label
nextRunlabelNew=Próxima execução programada
#XTXT: Task chain logs link tooltip text
taskChainLogsTxt=Detalhes do log da cadeia de tarefas
#XTXT: Task chain logs link tooltip text
taskChainLogsTxtNew=Detalhes
#XTXT: Scheduled text
scheduledTxt=Programado
#XTXT: Paused text
pausedTxt=Pausado
#XTXT: Execute button label
runLabel=Executar
#XTXT: Execute button label
runLabelNew=Iniciar execução
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Executar a cadeia de tarefas
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Execução da cadeia de tarefas iniciada.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Execução da cadeia de tarefas iniciada para {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Falha ao executar a cadeia de tarefas.
#XFLD: Label for schedule owner column
txtScheduleOwner=Proprietário da programação
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Mostra quem criou a programação
#XMSG: Task log message for start chain
startChain=Iniciando execução da cadeia de tarefas.
#XMSG: Task log message for load chain from repository
loadChainFromRepository=Cadeia de tarefas carregada e inicializada.
#XMSG: Task log message started task
taskStarted=Tarefa {0} iniciada.
#XMSG: Task log message for finished task
taskFinished=A tarefa {0} foi encerrada com status {1}.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Carregando cadeia de tarefas e preparando para executar um total de {0} tarefas que fazem parte dessa cadeia.
#XMSG: Task log message for starting a subtask
chainStartSubtask=Acionando tarefa {0} para execução. ID da tarefa = {1}.
#XMSG: Task log message for finishing a subtask
chainFinishSubtask=Tarefa {0} concluída com status {1}.
#XMSG: Task log message for indicating chain success
chainCompleted=Todas as {0} tarefas foram concluídas. O status da cadeia de tarefas foi definido como Concluído.
#XMSG: Task log message for indicating chain failure
chainFailed=De um total de {0} tarefas, foi possível concluir {1} tarefas e houve falha em {2} tarefas. O status da cadeia de tarefas foi definido como Com falha.
#XMSG: Task log message for indicating chain cancelation
chainCanceled=Cancelamento de execução de cadeia de tarefas iniciado. O ID do log da tarefa de cancelamento é {0}.
#XMSG: Task log message for indicating chain cancelation in the cancel task
chainCanceling=Cancelando cadeia {0}.
#XMSG: Task log message for general chain runtime error
chainError=Erro inesperado ao executar a cadeia de tarefas.
#XMSG: Task log message for starting the overwrite check.
chainOverwriteStart=Verificando se a cadeia de tarefas com ID {0} foi concluída.
#XMSG: Task log message indicating the chain is too old.
chainOverwriteTooOld=Não foi possível encontrar nenhuma tarefa da cadeia {0} para ser executada. A execução iniciou há {1} minutos. O status da cadeia de tarefas foi definido como Com falha.
#XMSG: Task log message indicating the chain was created too recently.
chainOverwriteTooRecent=Não foi possível encontrar nenhuma tarefa da cadeia {0} para ser executada. A execução iniciou há {1} minutos. A cadeia de tarefas ainda está em execução.
#XMSG: Task log message indicating a recent heartbeat was found.
chainOverwriteHeartbeatAlive=A cadeia de tarefas {0} ainda está em execução. Concluído: {1}, Em execução: {2}, Com falha: {3}, Acionado: {4}, Não acionado: {5}.
#XMSG: Task log message for a chain that is not running anymore but not everything was triggered.
chainOverwriteNotAllTriggered=Erro ao executar a cadeia de tarefas {0}. Também não foi possível acionar todas as tarefas. Concluído: {1}, Em execução: {2}, Com falha: {3}, Acionado: {4}, Não acionado: {5}. Essa cadeia de tarefas foi sobregravada.
#XMSG: Task log message for a chain that contains a failed subtask.
chainOverwriteAnySubtaskFailed=Cadeia de tarefas {0} concluída. Falha em uma tarefa da cadeia, definindo a cadeia como Com falha.
#XMSG: Task log message could not determine status.
chainOverwriteNotDetermined=Um erro inesperado nos impediu de acessar o status da tarefa. Tente novamente e entre em contato com o suporte da SAP caso o erro persista.
#XMSG: Task log message could not take over
failedTakeover=Falha ao assumir tarefa existente.
#XMSG: Task log parallel check error
parallelCheckError=Não é possível processar a tarefa porque outra tarefa está em execução e já está bloqueando essa tarefa.
#XMSG: Task log parallel task runnig error
parallelTaskRunning=Uma tarefa em conflito já está em execução.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Status {0} durante execução com ID de correlação {1}.
#XMSG: Task log message successful takeover
successTakeover=O bloqueio teve que ser liberado. O novo bloqueio para esta tarefa foi definido.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=O controle do bloqueio desta tarefa foi tomado por outra tarefa.
#XMSG: Schedule created alert message
createScheduleSuccess=Programação criada
#XMSG: Task log message for taskFinishedAt
taskFinishedAt=Tarefa {0} concluída em {2} com status {1}.
#XMSG: Placeholder symbol for empty columns
emptyCol=--
#XMSG: Paused Text
txtExpired=Expirado
#XMSG: Update task schedule success Toast message
updateScheduleSuccess=Programação atualizada
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Programação excluída.
#XMSG: Task chain might have children error text
taskChainNoChildErrorText=A cadeia de tarefas pode ter filhos não exibidos porque ocorreu uma falha na tarefa antes de ser possível gerar o plano.
#XMSG: Task chain repair recent failed run label
retryRunLabel=Repetir última execução
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Nova tentativa de execução da cadeia de tarefas iniciada
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Falha na nova tentativa de execução da cadeia de tarefas
#XMSG: chain repair message
chainRetried=Nova tentativa da cadeia de tarefas acionada pelo usuário {0}
#XMSG: chain not found during run
chainNotFoundDuringRun=Não foi possível encontrar a cadeia de tarefas {0}. Verifique se ela existe usando o Gerador de dados e certifique-se de que foi implementada.
#XMSG: chain is not DAG
chainNotDag=Não é possível iniciar a cadeia de tarefas {0}. A estrutura é inválida. Verifique a cadeia de tarefas no Gerador de dados.
#XMSG: chain has not valid parameters
notValidParameters=Não é possível iniciar a cadeia de tarefas {0}. Um ou mais parâmetros são inválidos. Verifique a cadeia de tarefas no Gerador de dados.
#XMSG: Configuration parameter value size exceeds the maximum allowed limit
configurationSizeLimitError=Não é possível iniciar a cadeia de tarefas {0}. O tamanho de uma configuração de tarefa na cadeia excede o tamanho máximo permitido de 100 kibibytes (KiB).
#XMSG: Task {0} has input parameters
taskHasInputParameters=A tarefa {0} tem parâmetros de entrada.
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Uma tarefa em conflito já está em execução
#XMSG: error message for reading data from backend
txtReadBackendError=Parece que houve um erro na leitura do back-end.
##XMSG: error message for admission control rejection
admissionControlError=Falha na tarefa, rejeição do controle de admissão do SAP HANA.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Atribuir programação a mim
#XBUT: Pause schedule menu label
pauseScheduleLabel=Pausar programação
#XBUT: Resume schedule menu label
resumeScheduleLabel=Retomar programação
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Ocorreu um erro ao remover as programações.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Ocorreu um erro ao atribuir as programações.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Ocorreu um erro ao pausar as programações.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Ocorreu um erro ao retomar as programações.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Excluindo {0} programações
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Alteração do proprietário de {0} programações
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Pausando {0} programações
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Retomando {0} programações
#XBUT: Select Columns Button
selectColumnsBtn=Selecionar colunas
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Aparentemente ocorreu um erro ao tentar novamente a última execução porque houve falha na execução da tarefa anterior, antes da geração do plano ser possível.
#XFLD: Refresh tooltip
TEXT_REFRESH=Atualizar
#XFLD: Select Columns tooltip
text_selectColumns=Selecionar colunas

#Messages for BW Process Chain task log of the Data Integration Monitor
#XMSG: Task log message for successfully starting a BW process chain
startBwProcessChainSuccess=A cadeia de processos BW "{0}" foi iniciada com sucesso no locatário da ponte do SAP BW.
#XMSG: Task log message for skipping a BW process chain execution
bwProcessChainSkipped=A cadeia de processos BW "{0}" foi ignorada devido à indisponibilidade de novos dados ou falha de uma execução anterior.
#XMSG: Task log message for failure in starting a BW process chain
startBwProcessChainFailure=Falha ao iniciar a cadeia de processos BW "{0}" no locatário da ponte do SAP BW.
#XMSG: Task log message for failure in getting the status of a BW process chain
getBwProcessChainStatusFailure=Falha na tarefa, não foi possível recuperar o status da cadeia de processos BW "{0}" devido a um problema para estabelecer a conexão com a ponte do SAP BW. Abra o app "Connections" e valide a conexão da ponte do SAP BW na área "{1}". Se você não tiver acesso ao app "Connections", entre em contato com a administrador.
#XMSG: Task log message for failure in completing a BW process chain
completeBwProcessChainFailure=Falha ao concluir a cadeia de processos BW "{0}" no locatário da ponte do SAP BW.
#XMSG: Task log message for the clickable text of the SAP BW Bridge Monitor link
bwBridgeMonitorLinkText=Exibir detalhes no Monitor da ponte do SAP BW
#XMSG warning message while retrieving the task log messages if the user does not have access to the SAP BW Bridge space or to the shared object's space
missingPrivilegeOnSpaceError=Ocorreu um problema ao recuperar as mensagens ou você não tem a permissão necessária para visualizá-las.

#Messages for API task log of the Data Integration Monitor
#XMSG: Task log message for displaying the API response
viewResponse=Resposta recebida para "{0}".
#XMSG: Task log message for empty ID in the API response to retrieve the status
emptyJobId=Nenhum ID foi retornado no corpo da resposta para o caminho JSON: "{0}".
#XMSG: Task log message for empty success value in the API response
emptySuccessValue=Nenhum valor de sucesso foi retornado no corpo da resposta para o caminho JSON: "{0}".
#XMSG: Task log message for empty error value in the API response
emptyErrorValue=Nenhum valor de erro foi retornado no corpo da resposta para o caminho JSON: "{0}".
#XMSG: Task log message for none of the success/error conditions matching for the values in the API response
conditionsNotFulfilled=Nenhuma das condições de indicador de sucesso ou erro especificadas corresponde aos valores na resposta.
#XMSG: Task log message for the API response status being unsuccessful
statusNotOk=A resposta para "{1}" retornou um código de status de falha: {0}.
#XMSG: Task log message for the API response body being empty
emptyResponseBody=O corpo da resposta estava vazio.
#XMSG: Task log message for the API response location header being invalid
emptyLocationHeader=O cabeçalho de local na resposta estava vazio.
#XMSG: Task log message for failure in retrieving the status of the invoked API
getStatusFailure=Não foi possível recuperar o status da API chamada.
#XMSG: Task log message for failure in completing the API task
completionFailure=Falha ao concluir a tarefa de API "{0}".
#XMSG: Task log message for a successful API task completion
apiCompleted=A tarefa de API "{0}" foi concluída com sucesso.
#XMSG: Task log message for an API task with an invalid configuration
invalidConfiguration=A configuração da tarefa de API "{0}" é inválida. Verifique a configuração e tente novamente.
#XMSG: Task log message for the API task being canceled
cancelStart=Cancelamento solicitado da tarefa de API "{0}" com logId {1}.
#XMSG: Task log message for the cancelation of the an API task which is no longer running and cannot be canceled
unableToCancel=A tarefa de API "{0}" com logID {1} não está mais em execução e não pode ser cancelada.
#XMSG: Task log message when starting an API task. It should carry the name of the API task.
apiRunStart=Iniciando a tarefa de API "{0}".
#XMSG: Task log message when the task preparation is taking too long and times out
preparationTimeout=A preparação da tarefa estava levando muito tempo e expirou.
#XMSG: Task log message when a task failed because a cancel task was triggered
apiRunCancelled=A tarefa de execução com logId {0} foi cancelada pela tarefa de cancelamento {1}.
#XMSG: Task log message when a cancel task failed to cancel a run task
cancelFailure=Falha na tarefa de cancelamento com logID {1} para cancelar a tarefa de execução com logID {0}.
#XMSG: Task log message when configuration of the API task is too large
apiTaskconfigurationSizeLimitError=Não é possível iniciar a tarefa de API {0} porque o tamanho da respectiva configuração de tarefa excede o tamanho máximo permitido de 100 kibibytes (KiB).
#XMSG: Task log message for failure in completing the Notification task
notificationTaskCompletionFailure=Falha ao concluir a tarefa de notificação "{0}".
#XMSG: Task log message when starting a Notification task.
notificationTaskRunStart=Iniciando a tarefa de notificação "{0}".
#XMSG: Task log message for a successful Notification task completion
notificationTaskCompletionSuccess=Tarefa de notificação "{0}" concluída.
#XFLD: Label for frequency column
everyLabel=A cada
#XFLD: Plural Recurrence text for Hour
hoursLabel=Horas
#XFLD: Plural Recurrence text for Day
daysLabel=Dias
#XFLD: Plural Recurrence text for Month
monthsLabel=Meses
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minutos
