

#XFLD: Task Chain header
headerTxt=<PERSON>ań<PERSON>chy zadań ({0})
#XFLD: Task Chain header
headerTxtNew=<PERSON><PERSON><PERSON><PERSON><PERSON> zadań ({0})
#XTXT: Text for Schedule label
scheduleTxt=Planuj
#XFLD: Text for Create schedule button
createScheduleTxt=Utwórz harmonogram
#XFLD: Text for edit schedule
editScheduleTxt=Edytuj harmonogram
#XLFD: Text for delete schedule
deleteScheduleTxt=Usuń harmonogram
#XTXT: text for refresh button label
refrestTxt=Odśwież
#XTXT: Text for Completed status
completedTxt=Zakończone
#XTX: Text for Running status
runningTxt=Aktywne
#XTX: Text for failed status
failedTxt=Niepowodzenie
#XTX: Text for stopped status
stoppedTxt=Zatrzymane
#XTX: Text for stopping status
stoppingTxt=Zatrzymywanie
#XFLD: Header for Chain name
chainNameLabel=Nazwa łańcucha zadań
#XFLD: Header for Chain name
chainNameLabelBus=Nazwa biznesowa
#XFLD: Header for Chain name
chainNameLabelBusNew=Obiekt (nazwa biznesowa)
#XFLD: Header for Chain name
chainNameLabelTech=Nazwa techniczna
#XFLD: Header for Chain name
chainNameLabelTechNew=Obiekt (nazwa techniczna)
#XFLD: Last Run Status label
lastRunStatuslabel=Status ostatniego przebiegu
#XFLD: Last Run Status label
lastRunStatuslabelNew=Status
#XFLD: Frequency Label
frequencyLabel=Częstotliwość
#XFLD: Frequency Label
frequencyLabelNew=Zaplanowana częstotliwość
#XFLD: Duration label
durationLabel=Czas trwania
#XFLD: Duration label
durationLabelNew=Czas trwania ostatniego przebiegu
#XFLD: Run Start label
runStartLabel=Rozpoczęcie ostatniego przebiegu
#XFLD: Run end label
runEndLabel=Zakończenie ostatniego przebiegu
#XFLD: Next Run label
nextRunlabel=Następny przebieg
#XFLD: Next Run label
nextRunlabelNew=Zaplanowany następny przebieg
#XTXT: Task chain logs link tooltip text
taskChainLogsTxt=Szczegóły logu łańcucha zadań
#XTXT: Task chain logs link tooltip text
taskChainLogsTxtNew=Szczegóły
#XTXT: Scheduled text
scheduledTxt=Zaplanowane
#XTXT: Paused text
pausedTxt=Wstrzymane
#XTXT: Execute button label
runLabel=Uruchom
#XTXT: Execute button label
runLabelNew=Rozpocznij przebieg
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Uruchom łańcuch zadań
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Łańcuch zadań został uruchomiony.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Łańcuch zadań został uruchomiony dla {0}.
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Uruchomienie łańcucha zadań nie powiodło się.
#XFLD: Label for schedule owner column
txtScheduleOwner=Osoba odpowiedzialna za harmonogram
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Wyświetla, kto utworzył harmonogram
#XMSG: Task log message for start chain
startChain=Rozpoczynanie przebiegu łańcucha zadań.
#XMSG: Task log message for load chain from repository
loadChainFromRepository=Łańcuch zadań został wczytany i zainicjalizowany.
#XMSG: Task log message started task
taskStarted=Zadanie {0} zostało rozpoczęte.
#XMSG: Task log message for finished task
taskFinished=Zadanie {0} zostało zakończone ze statusem {1}.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Wczytywanie łańcucha zadań i przygotowanie do uruchomienia łącznie {0} zadań należących do tego łańcucha.
#XMSG: Task log message for starting a subtask
chainStartSubtask=Wyzwalanie zadania {0} do uruchomienia. ID zadania = {1}.
#XMSG: Task log message for finishing a subtask
chainFinishSubtask=Zadanie {0} zostało zakończone ze statusem {1}.
#XMSG: Task log message for indicating chain success
chainCompleted=Wszystkie zadania {0} zostały zakończone Ustawiony status łańcucha zadań to Zakończone.
#XMSG: Task log message for indicating chain failure
chainFailed=Spośród łącznie {0} zadań powiodło się ukończenie {1} zadań, a wykonanie {2} zakończyło się niepowodzeniem. Ustawiony status łańcucha zadań to Nieudane.
#XMSG: Task log message for indicating chain cancelation
chainCanceled=Rozpoczęto anulowanie uruchamiania łańcucha zadań. ID logu zadania anulowania to {0}.
#XMSG: Task log message for indicating chain cancelation in the cancel task
chainCanceling=Anulowanie łańcucha {0}.
#XMSG: Task log message for general chain runtime error
chainError=Podczas wykonywania łańcucha zadań wystąpił nieoczekiwany błąd.
#XMSG: Task log message for starting the overwrite check.
chainOverwriteStart=Sprawdzanie, czy łańcuch zadań z ID {0} został zakończony.
#XMSG: Task log message indicating the chain is too old.
chainOverwriteTooOld=Nie znaleziono zadań do uruchomienia dla łańcucha {0}. Przebieg utworzono {1} min. temu. Ustawiony status łańcucha zadań to Nieudane.
#XMSG: Task log message indicating the chain was created too recently.
chainOverwriteTooRecent=Nie znaleziono zadań do uruchomienia dla łańcucha {0}. Przebieg utworzono {1} min. temu. Łańcuch zadań to jest wciąż wykonywany.
#XMSG: Task log message indicating a recent heartbeat was found.
chainOverwriteHeartbeatAlive=Łańcuch zadań {0} jest wciąż wykonywany. Zakończone: {1}, Uruchomione: {2}, Nieudane: {3}, Wyzwolone: {4}, Niewyzwolone {5}.
#XMSG: Task log message for a chain that is not running anymore but not everything was triggered.
chainOverwriteNotAllTriggered=Wystąpił błąd podczas uruchamiania łańcucha zadań {0} i nie można było wyzwolić wszystkich zadań. Zakończone: {1}, Uruchomione: {2}, Nieudane: {3}, Wyzwolone: {4}, Niewyzwolone: {5}. Ten łańcuch zadań został nadpisany.
#XMSG: Task log message for a chain that contains a failed subtask.
chainOverwriteAnySubtaskFailed=Zakończono łańcuch zadań {0}. Jedno zadanie łańcucha nie powiodło się; ustawianie łańcucha na status Nieudane.
#XMSG: Task log message could not determine status.
chainOverwriteNotDetermined=Nieoczekiwany błąd uniemożliwia nam dostęp do statusu zadania. Jeśli błąd będzie się powtarzać, spróbuj ponownie i skontaktuj się z działem wsparcia SAP.
#XMSG: Task log message could not take over
failedTakeover=Błąd przejmowania istniejącego zadania.
#XMSG: Task log parallel check error
parallelCheckError=Nie można przetworzyć zadania, ponieważ jest blokowane przez inne zadanie w toku.
#XMSG: Task log parallel task runnig error
parallelTaskRunning=Niezgodne zadanie jest już uruchomione.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Status {0} podczas przebiegu z ID korelacji {1}.
#XMSG: Task log message successful takeover
successTakeover=Pozostała blokada musiała zostać zwolniona. Ustawiono nową blokadę dla tego zadania.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Blokada tego zadania została przejęta przez inne zadanie.
#XMSG: Schedule created alert message
createScheduleSuccess=Utworzono harmonogram
#XMSG: Task log message for taskFinishedAt
taskFinishedAt=Zadanie {0} zostało zakończone o godzinie {2} ze statusem {1}.
#XMSG: Placeholder symbol for empty columns
emptyCol=--
#XMSG: Paused Text
txtExpired=Wygasłe
#XMSG: Update task schedule success Toast message
updateScheduleSuccess=Zaktualizowano harmonogram
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Usunięto harmonogram.
#XMSG: Task chain might have children error text
taskChainNoChildErrorText=Łańcuch zadań może mieć elementy podrzędne, które nie są wyświetlane, ponieważ zadanie nie powiodło się, zanim można było wygenerować plan.
#XMSG: Task chain repair recent failed run label
retryRunLabel=Ponów ostatni przebieg
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Rozpoczęto przebieg ponownej próby łańcucha
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Przebieg ponownej próby łańcucha nie powiódł się
#XMSG: chain repair message
chainRetried=Ponowna próba łańcucha zadań została wyzwolona przez użytkownika {0}
#XMSG: chain not found during run
chainNotFoundDuringRun=Nie można znaleźć łańcucha zadań {0}. Sprawdź, czy istnieje, za pomocą edytora danych i upewnij się, że wdrożono łańcuch.
#XMSG: chain is not DAG
chainNotDag=Nie można uruchomić łańcucha zadań {0}. Jego struktura jest nieprawidłowa. Sprawdź łańcuch zadań w edytorze danych.
#XMSG: chain has not valid parameters
notValidParameters=Nie można uruchomić łańcucha zadań {0}. Co najmniej jeden z jego parametrów jest nieprawidłowy. Sprawdź łańcuch zadań w edytorze danych.
#XMSG: Configuration parameter value size exceeds the maximum allowed limit
configurationSizeLimitError=Nie można rozpocząć łańcucha zadań {0}. Rozmiar konfiguracji zadań w łańcuchu zadań przekracza maksymalny limit 100 kibibajtów (KiB).
#XMSG: Task {0} has input parameters
taskHasInputParameters=Zadanie {0} ma parametry wejściowe.
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Niezgodne zadanie jest już uruchomione
#XMSG: error message for reading data from backend
txtReadBackendError=Prawdopodobnie wystąpił błąd podczas odczytu z backend.
##XMSG: error message for admission control rejection
admissionControlError=Zadanie nie powiodło się z powodu odrzucenia kontroli przyjęcia SAP HANA.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Przypisz harmonogram do mnie
#XBUT: Pause schedule menu label
pauseScheduleLabel=Wstrzymaj harmonogram
#XBUT: Resume schedule menu label
resumeScheduleLabel=Wznów harmonogram
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Wystąpił błąd podczas usuwania harmonogramów.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Wystąpił błąd podczas przypisywania harmonogramów.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Wystąpił błąd podczas wstrzymywania harmonogramów.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Wystąpił błąd podczas wznawiania harmonogramów.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Usuwanie {0} harmonogramów
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Zmiana osoby odpowiedzialnej dla {0} harmonogramów
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Wstrzymywanie {0} harmonogramów
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Wznawianie {0} harmonogramów
#XBUT: Select Columns Button
selectColumnsBtn=Wybierz kolumny
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Wygląda na to, że wystąpił błąd z ponowieniem ostatniego przebiegu, ponieważ poprzedni przebieg zadania nie powiódł się przed wygenerowaniem planu.
#XFLD: Refresh tooltip
TEXT_REFRESH=Odśwież
#XFLD: Select Columns tooltip
text_selectColumns=Wybierz kolumny

#Messages for BW Process Chain task log of the Data Integration Monitor
#XMSG: Task log message for successfully starting a BW process chain
startBwProcessChainSuccess=Pomyślnie uruchomiono łańcuch procesów BW "{0}" w tenancie SAP BW Bridge.
#XMSG: Task log message for skipping a BW process chain execution
bwProcessChainSkipped=Łańcuch procesu BW "{0}" pominięto z powodu niedostępności nowych danych lub niepowodzenia poprzedniego wykonania.
#XMSG: Task log message for failure in starting a BW process chain
startBwProcessChainFailure=Nie udało się rozpocząć łańcucha procesów BW "{0}" w tenancie SAP BW Bridge.
#XMSG: Task log message for failure in getting the status of a BW process chain
getBwProcessChainStatusFailure=Zadanie nie powiodło się, ponieważ nie udało się pobrać stanu łańcucha procesów BW "{0}" z powodu problemu z nawiązaniem połączenia z SAP BW Bridge. Otwórz aplikację "Połączenia" i zweryfikuj połączenie SAP BW Bridge w przestrzeni "{1}". W przypadku braku dostępu do aplikacji "Połączenia" należy skontaktuj się z administratorem.
#XMSG: Task log message for failure in completing a BW process chain
completeBwProcessChainFailure=Nie udało się ukończyć łańcucha procesów BW "{0}" w tenancie SAP BW Bridge.
#XMSG: Task log message for the clickable text of the SAP BW Bridge Monitor link
bwBridgeMonitorLinkText=Wyświetl szczegóły w monitorze SAP BW Bridge
#XMSG warning message while retrieving the task log messages if the user does not have access to the SAP BW Bridge space or to the shared object's space
missingPrivilegeOnSpaceError=Wystąpił problem z pobieraniem komunikatów lub nie masz uprawnień potrzebnych do ich wyświetlenia.

#Messages for API task log of the Data Integration Monitor
#XMSG: Task log message for displaying the API response
viewResponse=Otrzymano odpowiedź dotyczącą "{0}".
#XMSG: Task log message for empty ID in the API response to retrieve the status
emptyJobId=W treści odpowiedzi dla ścieżki JSON "{0}" nie został zwrócony żaden ID.
#XMSG: Task log message for empty success value in the API response
emptySuccessValue=W treści odpowiedzi dla ścieżki JSON "{0}" nie została zwrócona żadna wartość powodzenia.
#XMSG: Task log message for empty error value in the API response
emptyErrorValue=W treści odpowiedzi dla ścieżki JSON "{0}" nie została zwrócona żadna wartość błędu.
#XMSG: Task log message for none of the success/error conditions matching for the values in the API response
conditionsNotFulfilled=Żaden z podanych warunków wskaźnika sukcesu lub błędu nie pasuje do wartości w odpowiedzi.
#XMSG: Task log message for the API response status being unsuccessful
statusNotOk=Odpowiedź dla "{1}" zwróciła kod statusu niepowodzenia: {0}.
#XMSG: Task log message for the API response body being empty
emptyResponseBody=Treść odpowiedzi była pusta.
#XMSG: Task log message for the API response location header being invalid
emptyLocationHeader=Nagłówek lokalizacji w odpowiedzi był pusty.
#XMSG: Task log message for failure in retrieving the status of the invoked API
getStatusFailure=Uzyskanie statusu wywołanego interfejsu API było niemożliwe.
#XMSG: Task log message for failure in completing the API task
completionFailure=Ukończenie zadania "{0}" interfejsu API nie powiodło się.
#XMSG: Task log message for a successful API task completion
apiCompleted=Zadanie API "{0}" zostało ukończone pomyślnie.
#XMSG: Task log message for an API task with an invalid configuration
invalidConfiguration=Konfiguracja zadania API "{0}" jest nieprawidłowa. Sprawdź konfigurację i spróbuj ponownie.
#XMSG: Task log message for the API task being canceled
cancelStart=Zażądano anulowania zadania API "{0}" z ID logu {1}.
#XMSG: Task log message for the cancelation of the an API task which is no longer running and cannot be canceled
unableToCancel=Zadanie API "{0}" z ID logu {1} nie jest już wykonywane i nie można go anulować.
#XMSG: Task log message when starting an API task. It should carry the name of the API task.
apiRunStart=Rozpoczynanie zadania API "{0}".
#XMSG: Task log message when the task preparation is taking too long and times out
preparationTimeout=Przygotowanie zadania trwa zbyt długo i przekroczono limit czasu.
#XMSG: Task log message when a task failed because a cancel task was triggered
apiRunCancelled=Zadanie uruchomienia z ID logu {0} zostało anulowane przez zadanie anulowania {1}.
#XMSG: Task log message when a cancel task failed to cancel a run task
cancelFailure=Nie powiodło się anulowanie zadania uruchomienia z ID logu {0} przez zadanie anulowania z ID logu {1}.
#XMSG: Task log message when configuration of the API task is too large
apiTaskconfigurationSizeLimitError=Nie można rozpocząć zadania API {0}, ponieważ rozmiar jego konfiguracji zadań przekracza maksymalny limit 100 kibibajtów (KiB).
#XMSG: Task log message for failure in completing the Notification task
notificationTaskCompletionFailure=Ukończenie zadania powiadomienia "{0}" nie powiodło się.
#XMSG: Task log message when starting a Notification task.
notificationTaskRunStart=Rozpoczynanie zadania powiadomienia "{0}".
#XMSG: Task log message for a successful Notification task completion
notificationTaskCompletionSuccess=Zadanie powiadomienia "{0}" zostało ukończone pomyślnie.
#XFLD: Label for frequency column
everyLabel=Co
#XFLD: Plural Recurrence text for Hour
hoursLabel=godz.
#XFLD: Plural Recurrence text for Day
daysLabel=dni
#XFLD: Plural Recurrence text for Month
monthsLabel=mies.
#XFLD: Plural Recurrence text for Minutes
minutesLabel=min
