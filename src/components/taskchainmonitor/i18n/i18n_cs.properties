

#XFLD: Task Chain header
headerTxt=Řetězce úloh ({0})
#XFLD: Task Chain header
headerTxtNew=Řetězce úloh ({0})
#XTXT: Text for Schedule label
scheduleTxt=Naplánovat
#XFLD: Text for Create schedule button
createScheduleTxt=Vytvořit časový plán
#XFLD: Text for edit schedule
editScheduleTxt=Upravit časový plán
#XLFD: Text for delete schedule
deleteScheduleTxt=Odstranit plán
#XTXT: text for refresh button label
refrestTxt=Aktualizovat
#XTXT: Text for Completed status
completedTxt=Dokončeno
#XTX: Text for Running status
runningTxt=Probíhá
#XTX: Text for failed status
failedTxt=Neúspěšné
#XTX: Text for stopped status
stoppedTxt=Zastaveno
#XTX: Text for stopping status
stoppingTxt=Zastavuje se
#XFLD: Header for Chain name
chainNameLabel=Název řetězce úloh
#XFLD: Header for Chain name
chainNameLabelBus=Business název
#XFLD: Header for Chain name
chainNameLabelBusNew=Objekt (business název)
#XFLD: Header for Chain name
chainNameLabelTech=Technický název
#XFLD: Header for Chain name
chainNameLabelTechNew=Objekt (technický název)
#XFLD: Last Run Status label
lastRunStatuslabel=Status posledního běhu
#XFLD: Last Run Status label
lastRunStatuslabelNew=Status
#XFLD: Frequency Label
frequencyLabel=Frekvence
#XFLD: Frequency Label
frequencyLabelNew=Naplánovaná frekvence
#XFLD: Duration label
durationLabel=Trvání
#XFLD: Duration label
durationLabelNew=Trvání posledního běhu
#XFLD: Run Start label
runStartLabel=Začátek posledního běhu
#XFLD: Run end label
runEndLabel=Konec posledního běhu
#XFLD: Next Run label
nextRunlabel=Příští běh
#XFLD: Next Run label
nextRunlabelNew=Naplánovaný příští běh
#XTXT: Task chain logs link tooltip text
taskChainLogsTxt=Detaily protokolu řetězce úloh
#XTXT: Task chain logs link tooltip text
taskChainLogsTxtNew=Detaily
#XTXT: Scheduled text
scheduledTxt=Naplánováno
#XTXT: Paused text
pausedTxt=Pozastaveno
#XTXT: Execute button label
runLabel=Spustit
#XTXT: Execute button label
runLabelNew=Spustit běh
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Spustit řetězec úloh
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Běh řetězce úloh spuštěn.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Běh řetězce úloh spuštěn pro {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Nezdařilo se spustit řetězec úloh.
#XFLD: Label for schedule owner column
txtScheduleOwner=Vlastník plánu
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Zobrazuje, kdo vytvořil plán
#XMSG: Task log message for start chain
startChain=Spouštění běhu řetězce úloh
#XMSG: Task log message for load chain from repository
loadChainFromRepository=Řetězec úloh načten a inicializován.
#XMSG: Task log message started task
taskStarted=Úloha {0} spuštěna.
#XMSG: Task log message for finished task
taskFinished=Úloha {0} skončila se statusem {1}.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Načítání řetězce úloh a příprava na běh celkem {0} úloh, které jsou součástí toho řetězce.
#XMSG: Task log message for starting a subtask
chainStartSubtask=Spuštění úlohy {0} k běhu. ID úlohy = {1}.
#XMSG: Task log message for finishing a subtask
chainFinishSubtask=Úloha {0} ukončena se statusem {1}.
#XMSG: Task log message for indicating chain success
chainCompleted=Všech(ny) {0} úloh(y) dokončeno(y). Status řetězce úloh nastaven na Dokončeno.
#XMSG: Task log message for indicating chain failure
chainFailed=Z celkového počtu {0} úloh bylo možné dokončit {1} úloh(y) a {2} úlohy byly neúspěšné. Status řetězce úloh nastaven na Neúspěšné.
#XMSG: Task log message for indicating chain cancelation
chainCanceled=Stornování běhu řetězce úloh bylo spuštěno. ID protokolu úlohy stornování je {0}.
#XMSG: Task log message for indicating chain cancelation in the cancel task
chainCanceling=Stornování řetězce {0}.
#XMSG: Task log message for general chain runtime error
chainError=Při běhu řetězce úloh došlo k neočekávané chybě.
#XMSG: Task log message for starting the overwrite check.
chainOverwriteStart=Kontrola, zda byl řetězec úloh s ID {0} dokončen.
#XMSG: Task log message indicating the chain is too old.
chainOverwriteTooOld=Pro řetězec {0} nebylo možné najít žádnou úlohu. Běh je {1} minut starý. Status řetězce úloh je nastaven na Neúspěšný.
#XMSG: Task log message indicating the chain was created too recently.
chainOverwriteTooRecent=Pro řetězec {0} nebylo možné najít žádnou úlohu. Běh je {1} minut starý. Řetězec úloh ještě probíhá.
#XMSG: Task log message indicating a recent heartbeat was found.
chainOverwriteHeartbeatAlive=Řetězec úloh {0} ještě probíhá. Dokončeno: {1}, Probíhá: {2}, Neúspěšné: {3}, Spuštěno: {4}, Nespuštěno: {5}.
#XMSG: Task log message for a chain that is not running anymore but not everything was triggered.
chainOverwriteNotAllTriggered=Při běhu řetězce úloh {0} došlo k chybě a ne všechny úlohy bylo možné spustit. Dokončeno: {1}, Probíhá: {2}, Neúspěšné: {3}, Spuštěno: {4}, Nespuštěno: {5}. Tento řetězec úloh byl přepsán.
#XMSG: Task log message for a chain that contains a failed subtask.
chainOverwriteAnySubtaskFailed=Řetězec úloh {0} dokončen. Jedna úloha z řetězce neúspěšná, nastavení řetězce na Neúspěšné.
#XMSG: Task log message could not determine status.
chainOverwriteNotDetermined=Neočekávaná chyba nám zabránila v přístupu ke statusu úlohy. Zkuste to znovu a pokud chyba přetrvává, kontaktujte podporu poskytovanou společností SAP.
#XMSG: Task log message could not take over
failedTakeover=Nezdařilo se převzít existující úlohu.
#XMSG: Task log parallel check error
parallelCheckError=Úlohu nelze zpracovat, protože probíhá jiná úloha a již tuto úlohu blokuje.
#XMSG: Task log parallel task runnig error
parallelTaskRunning=Konfliktní úloha je již spuštěna.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Status {0} při běhu s ID korelace {1}.
#XMSG: Task log message successful takeover
successTakeover=Zbylé blokování muselo být uvolněno. Pro tuto úlohu je nastaveno nové blokování
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Blokování této úlohy bylo převzato jinou úlohou.
#XMSG: Schedule created alert message
createScheduleSuccess=Časový plán vytvořen
#XMSG: Task log message for taskFinishedAt
taskFinishedAt=Úloha {0} dokončena v {2} se statusem {1}.
#XMSG: Placeholder symbol for empty columns
emptyCol=--
#XMSG: Paused Text
txtExpired=Platnost vypršela
#XMSG: Update task schedule success Toast message
updateScheduleSuccess=Časový plán aktualizován
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Časový plán odstraněn.
#XMSG: Task chain might have children error text
taskChainNoChildErrorText=Řetězec úloh může mít podřízené uzly, které se nezobrazují, protože úloha selhala před vygenerováním plánu.
#XMSG: Task chain repair recent failed run label
retryRunLabel=Opakujte poslední běh
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Opakovaný běh řetězce úloh spuštěn
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Opakovaný běh řetězce úloh se nezdařil
#XMSG: chain repair message
chainRetried=Řetězec úloh opakovaně spuštěn uživatelem {0}
#XMSG: chain not found during run
chainNotFoundDuringRun=Řetězec úloh {0} nebylo možné najít. Pomocí editoru dat zkontrolujte, zda existuje, a zajistěte, aby byl nasazen.
#XMSG: chain is not DAG
chainNotDag=Řetězec úloh {0} nelze spustit. Jeho struktura je neplatná. Zkontrolujte řetězec úloh v editoru dat.
#XMSG: chain has not valid parameters
notValidParameters=Řetězec úloh {0} nelze spustit. Jeden nebo více jeho parametrů je neplatných. Zkontrolujte řetězec úloh v editoru dat.
#XMSG: Configuration parameter value size exceeds the maximum allowed limit
configurationSizeLimitError=Nelze spustit řetězec úloh {0}. Velikost konfigurace úloh v řetězci překračuje maximální povolenou velikost 100 kibibytů (KiB).
#XMSG: Task {0} has input parameters
taskHasInputParameters=Úloha {0} má vstupní parametry.
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Konfliktní úloha je již spuštěna
#XMSG: error message for reading data from backend
txtReadBackendError=Vypadá to, že při čtení z backendu došlo k chybě.
##XMSG: error message for admission control rejection
admissionControlError=Úloha se nezdařila z důvodu odmítnutí řízení přístupu SAP HANA.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Přiřadit plán mně
#XBUT: Pause schedule menu label
pauseScheduleLabel=Pozastavit plán
#XBUT: Resume schedule menu label
resumeScheduleLabel=Obnovit plán
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Při odebírání plánů došlo k chybě.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Při přiřazování plánů došlo k chybě.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Při pozastavování plánů došlo k chybě.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Při obnovování plánů došlo k chybě.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Odstraňování {0} plánů
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Změna vlastníka {0} plánů
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Pozastavování {0} plánů
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Obnovování {0} plánů
#XBUT: Select Columns Button
selectColumnsBtn=Vybrat sloupce
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Zdá se, že došlo k chybě při opakování posledního spuštění, protože předchozí spuštění úlohy se nezdařilo, než bylo možné vygenerovat plán.
#XFLD: Refresh tooltip
TEXT_REFRESH=Aktualizovat
#XFLD: Select Columns tooltip
text_selectColumns=Vybrat sloupce

#Messages for BW Process Chain task log of the Data Integration Monitor
#XMSG: Task log message for successfully starting a BW process chain
startBwProcessChainSuccess=Řetězec procesů BW "{0}" byl úspěšně zahájen v tenantu SAP BW Bridge.
#XMSG: Task log message for skipping a BW process chain execution
bwProcessChainSkipped=Řetězec procesů BW "{0}" byl přeskočen buď kvůli nedostupnosti nových dat, nebo kvůli chybě předchozího provedení..
#XMSG: Task log message for failure in starting a BW process chain
startBwProcessChainFailure=Spuštění řetězce procesů BW "{0}" v tenantu SAP BW Bridge se nezdařilo.
#XMSG: Task log message for failure in getting the status of a BW process chain
getBwProcessChainStatusFailure=Úloha byla neúspěšná, protože jsme nemohli načíst status řetězce procesů BW "{0}" kvůli problémovému zřizování připojení k SAP BW Bridge. Otevřete aplikaci "Připojení" a ověřte připojování SAP BW Bridge v prostoru "{1}". Nechcete-li přistupovat k aplikaci "Připojení", kontaktujte správce.
#XMSG: Task log message for failure in completing a BW process chain
completeBwProcessChainFailure=Řetězec procesů BW "{0}" v tenantu SAP BW Bridge se nezdařilo dokončit.
#XMSG: Task log message for the clickable text of the SAP BW Bridge Monitor link
bwBridgeMonitorLinkText=Zobrazit detaily v monitoru SAP BW Bridge
#XMSG warning message while retrieving the task log messages if the user does not have access to the SAP BW Bridge space or to the shared object's space
missingPrivilegeOnSpaceError=Byl problém s načítáním zpráv nebo nemáte oprávnění potřebné k jejich zobrazení.

#Messages for API task log of the Data Integration Monitor
#XMSG: Task log message for displaying the API response
viewResponse=Přijatá odpověď na "{0}".
#XMSG: Task log message for empty ID in the API response to retrieve the status
emptyJobId=Žádné ID nebylo vráceno v textu odpovědi pro cestu JSON: "{0}".
#XMSG: Task log message for empty success value in the API response
emptySuccessValue=Žádná úspěšná hodnota nebyla vrácena v textu odpovědi pro cestu JSON: "{0}".
#XMSG: Task log message for empty error value in the API response
emptyErrorValue=Žádná chybová hodnota nebyla vrácena v textu odpovědi pro cestu JSON: "{0}".
#XMSG: Task log message for none of the success/error conditions matching for the values in the API response
conditionsNotFulfilled=Žádná ze zadaných podmínek indikátoru úspěchu nebo chyby neodpovídá hodnotám v odpovědi.
#XMSG: Task log message for the API response status being unsuccessful
statusNotOk=Odpověď na "{1}" vrátila neúspěšný stavový kód: {0}.
#XMSG: Task log message for the API response body being empty
emptyResponseBody=Text odpovědi byl prázdný.
#XMSG: Task log message for the API response location header being invalid
emptyLocationHeader=Hlavička umístění v odpovědi byla prázdná.
#XMSG: Task log message for failure in retrieving the status of the invoked API
getStatusFailure=Status vyvolaného API nebylo možné načíst.
#XMSG: Task log message for failure in completing the API task
completionFailure=Úlohu API "{0}" se nepodařilo dokončit.
#XMSG: Task log message for a successful API task completion
apiCompleted=Úloha API "{0}" byla úspěšně dokončena.
#XMSG: Task log message for an API task with an invalid configuration
invalidConfiguration=Konfigurace úlohy API "{0}" je neplatná. Zkontrolujte konfiguraci a zkuste to znovu.
#XMSG: Task log message for the API task being canceled
cancelStart=Požadováno zrušení úlohy API "{0}" s logId {1}.
#XMSG: Task log message for the cancelation of the an API task which is no longer running and cannot be canceled
unableToCancel=Úloha API "{0}" s logId {1} již neprobíhá a nelze ji zrušit.
#XMSG: Task log message when starting an API task. It should carry the name of the API task.
apiRunStart=Spouštění úlohy API "{0}".
#XMSG: Task log message when the task preparation is taking too long and times out
preparationTimeout=Příprava úlohy trvá příliš dlouho a její čas vypršel.
#XMSG: Task log message when a task failed because a cancel task was triggered
apiRunCancelled=Úloha běhu s logId {0} byla zrušena stornovací úlohou {1}.
#XMSG: Task log message when a cancel task failed to cancel a run task
cancelFailure=Stornovací úloha logId {1} neúspěšná pro zrušení úlohy běhu s logId {0}.
#XMSG: Task log message when configuration of the API task is too large
apiTaskconfigurationSizeLimitError=Nelze spustit úlohu API {0}, protože velikost konfigurace úloh v řetězci překračuje maximální povolenou velikost 100 kibibytů (KiB).
#XMSG: Task log message for failure in completing the Notification task
notificationTaskCompletionFailure=Notifikační úlohu "{0}" se nepodařilo dokončit.
#XMSG: Task log message when starting a Notification task.
notificationTaskRunStart=Zahájení notifikační úlohy "{0}".
#XMSG: Task log message for a successful Notification task completion
notificationTaskCompletionSuccess=Notifikační úloha "{0}" byla dokončena.
#XFLD: Label for frequency column
everyLabel=Každé
#XFLD: Plural Recurrence text for Hour
hoursLabel=Hodiny
#XFLD: Plural Recurrence text for Day
daysLabel=Dny
#XFLD: Plural Recurrence text for Month
monthsLabel=Měsíce
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minuty
