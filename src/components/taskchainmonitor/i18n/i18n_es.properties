

#XFLD: Task Chain header
headerTxt=Cadenas de tareas ({0})
#XFLD: Task Chain header
headerTxtNew=Cadenas de tareas ({0})
#XTXT: Text for Schedule label
scheduleTxt=Programa
#XFLD: Text for Create schedule button
createScheduleTxt=Crear programación
#XFLD: Text for edit schedule
editScheduleTxt=Editar programación
#XLFD: Text for delete schedule
deleteScheduleTxt=Eliminar programación
#XTXT: text for refresh button label
refrestTxt=Actualizar
#XTXT: Text for Completed status
completedTxt=Completada
#XTX: Text for Running status
runningTxt=En ejecución
#XTX: Text for failed status
failedTxt=Error
#XTX: Text for stopped status
stoppedTxt=Parado
#XTX: Text for stopping status
stoppingTxt=Parando
#XFLD: Header for Chain name
chainNameLabel=Nombre de cadena de tareas
#XFLD: Header for Chain name
chainNameLabelBus=Nombre empresarial
#XFLD: Header for Chain name
chainNameLabelBusNew=Objeto (nombre empresarial)
#XFLD: Header for Chain name
chainNameLabelTech=Nombre técnico
#XFLD: Header for Chain name
chainNameLabelTechNew=Objeto (nombre técnico)
#XFLD: Last Run Status label
lastRunStatuslabel=Último estado de la ejecución
#XFLD: Last Run Status label
lastRunStatuslabelNew=Estado
#XFLD: Frequency Label
frequencyLabel=Frecuencia
#XFLD: Frequency Label
frequencyLabelNew=Frecuencia programada
#XFLD: Duration label
durationLabel=Duración
#XFLD: Duration label
durationLabelNew=Duración de la última ejecución
#XFLD: Run Start label
runStartLabel=Último inicio de la ejecución
#XFLD: Run end label
runEndLabel=Último fin de la ejecución
#XFLD: Next Run label
nextRunlabel=Siguiente ejecución
#XFLD: Next Run label
nextRunlabelNew=Próxima ejecución programada
#XTXT: Task chain logs link tooltip text
taskChainLogsTxt=Detalles del log de cadena de tareas
#XTXT: Task chain logs link tooltip text
taskChainLogsTxtNew=Detalles
#XTXT: Scheduled text
scheduledTxt=Programado
#XTXT: Paused text
pausedTxt=Interrumpida
#XTXT: Execute button label
runLabel=Ejecutar
#XTXT: Execute button label
runLabelNew=Iniciar ejecución
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Ejecutar la cadena de tareas
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Se ha iniciado la ejecución de la cadena de tareas.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Se ha iniciado la ejecución de la cadena de tareas de {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=No se ha podido ejecutar la cadena de tareas.
#XFLD: Label for schedule owner column
txtScheduleOwner=Propietario de la programación
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Muestra quién ha creado la programación
#XMSG: Task log message for start chain
startChain=Iniciando la ejecución de la cadena de tareas.
#XMSG: Task log message for load chain from repository
loadChainFromRepository=Se ha cargado e inicializado la cadena de tareas.
#XMSG: Task log message started task
taskStarted=Se ha iniciado la tarea {0}.
#XMSG: Task log message for finished task
taskFinished=La tarea {0} ha finalizado con el estado {1}.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Se está cargando la cadena de tareas y preparando la ejecución de un total de {0} tareas que forman parte de esta cadena.
#XMSG: Task log message for starting a subtask
chainStartSubtask=Se está iniciando la ejecución de la tarea {0}. ID de tarea = {1}.
#XMSG: Task log message for finishing a subtask
chainFinishSubtask=La tarea {0} ha finalizado con el estado {1}.
#XMSG: Task log message for indicating chain success
chainCompleted=Han concluido las {0} tareas. El estado de la cadena de tareas se ha fijado en "Concluido".
#XMSG: Task log message for indicating chain failure
chainFailed=De un total de {0} tareas, se han podido concluir {1} y {2} han resultado erróneas. El estado de la cadena de tareas se ha fijado en "Erróneo".
#XMSG: Task log message for indicating chain cancelation
chainCanceled=Se ha iniciado la cancelación de la ejecución de la cadena de tareas. El ID del log de la tarea cancelada es {0}.
#XMSG: Task log message for indicating chain cancelation in the cancel task
chainCanceling=Se está cancelando la cadena {0}.
#XMSG: Task log message for general chain runtime error
chainError=Se ha producido un error inesperado al ejecutar la cadena de tareas.
#XMSG: Task log message for starting the overwrite check.
chainOverwriteStart=Se está verificando si la cadena de tareas con el ID {0} ha concluido.
#XMSG: Task log message indicating the chain is too old.
chainOverwriteTooOld=No se ha encontrado ninguna tarea para ejecutarla en la cadena {0}. La ejecución lleva {1} minutos. El estado de la cadena de tareas se ha fijado en "Erróneo".
#XMSG: Task log message indicating the chain was created too recently.
chainOverwriteTooRecent=No se ha encontrado ninguna tarea para ejecutarla en la cadena {0}. La ejecución lleva {1} minutos. La cadena de tareas sigue en ejecución.
#XMSG: Task log message indicating a recent heartbeat was found.
chainOverwriteHeartbeatAlive=La cadena de tareas {0} sigue en ejecución. Concluidas: {1}. En ejecución: {2}. Erróneas: {3}. Iniciadas: {4}. No iniciadas: {5}.
#XMSG: Task log message for a chain that is not running anymore but not everything was triggered.
chainOverwriteNotAllTriggered=Se ha producido un error al ejecutar la cadena de tareas {0} y no se han podido iniciar todas las tareas. Concluidas: {1}. En ejecución: {2}. Erróneas: {3}. Iniciadas: {4}. No iniciadas: {5}. Se ha sobrescrito esta cadena de tareas.
#XMSG: Task log message for a chain that contains a failed subtask.
chainOverwriteAnySubtaskFailed=La cadena de tareas {0} ha concluido. Una tarea de la cadena ha sido errónea, por lo que se ha fijado el estado de la cadena en "Erróneo".
#XMSG: Task log message could not determine status.
chainOverwriteNotDetermined=Hay un error inesperado que nos impide acceder al estado de la tarea. Vuelva a intentarlo y póngase en contacto con el equipo de soporte de SAP si el error persiste.
#XMSG: Task log message could not take over
failedTakeover=Se ha producido un error al asumir una tarea existente.
#XMSG: Task log parallel check error
parallelCheckError=No se puede procesar la tarea porque hay otra tarea en ejecución y ya está bloqueándola.
#XMSG: Task log parallel task runnig error
parallelTaskRunning=Ya hay una tarea en conflicto en ejecución.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Se ha dado el estado {0} durante la ejecución con el ID de correlación {1}.
#XMSG: Task log message successful takeover
successTakeover=Ha sido necesario liberar el bloqueo izquierdo. Se establece el nuevo bloqueo para esta tarea.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Otra tarea ha asumido el bloqueo de esta tarea.
#XMSG: Schedule created alert message
createScheduleSuccess=Se ha creado la programación
#XMSG: Task log message for taskFinishedAt
taskFinishedAt=La tarea {0} ha finalizado a las {2} con el estado {1}.
#XMSG: Placeholder symbol for empty columns
emptyCol=--
#XMSG: Paused Text
txtExpired=Vencido
#XMSG: Update task schedule success Toast message
updateScheduleSuccess=Se ha actualizado la programación
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Se ha eliminado la programación.
#XMSG: Task chain might have children error text
taskChainNoChildErrorText=Es posible que la cadena de tareas tenga elementos inferiores que no se muestran porque se ha producido un error en la tarea antes de que se pudiera generar el plan.
#XMSG: Task chain repair recent failed run label
retryRunLabel=Reintentar última ejecución
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Se ha iniciado el reintento de la ejecución de la cadena de tareas.
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=No se ha podido reintentar la ejecución de la cadena de tareas.
#XMSG: chain repair message
chainRetried=El usuario {0} ha iniciado el reintento de ejecución de la cadena de tareas.
#XMSG: chain not found during run
chainNotFoundDuringRun=No se ha encontrado la cadena de tareas {0}. Compruebe si existe mediante el Generador de datos y asegúrese de que la cadena está desplegada.
#XMSG: chain is not DAG
chainNotDag=No se puede iniciar la tarea de cadenas {0}. Su estructura no es válida. Compruebe la cadena de tareas en el Generador de datos.
#XMSG: chain has not valid parameters
notValidParameters=No se puede iniciar la tarea de cadenas {0}. Uno o más de sus parámetros de entrada no es válida. Compruebe la cadena de tareas en el Generador de datos.
#XMSG: Configuration parameter value size exceeds the maximum allowed limit
configurationSizeLimitError=No se puede iniciar la cadena de tareas {0}. El tamaño de la configuración de una tarea de la cadena supera el tamaño máximo permitido de 100 kibibytes (KiB).
#XMSG: Task {0} has input parameters
taskHasInputParameters=La tarea {0} tiene parámetros de entrada.
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Ya hay una tarea en conflicto en ejecución
#XMSG: error message for reading data from backend
txtReadBackendError=Parece que se ha producido un error al leer desde el back end.
##XMSG: error message for admission control rejection
admissionControlError=No se ha podido realizar la tarea debido a un rechazo del control de admisión de SAP HANA.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Asignarme la programación
#XBUT: Pause schedule menu label
pauseScheduleLabel=Interrumpir programación
#XBUT: Resume schedule menu label
resumeScheduleLabel=Reanudar programación
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Se ha producido un error al quitar las programaciones.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Se ha producido un error al asignar las programaciones.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Se ha producido un error al interrumpir las programaciones.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Se ha producido un error al reanudar las programaciones.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Se están eliminando {0} programaciones
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Se está modificando el propietario de {0} programaciones
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Se están interrumpiendo {0} programaciones
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Se están reanudando {0} programaciones
#XBUT: Select Columns Button
selectColumnsBtn=Seleccionar columnas
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Parece que se ha producido un error con la ejecución del último reintento, ya que no se pudo ejecutar la tarea anterior antes de que se hubiese podido generar el plan.
#XFLD: Refresh tooltip
TEXT_REFRESH=Actualizar
#XFLD: Select Columns tooltip
text_selectColumns=Seleccionar columnas

#Messages for BW Process Chain task log of the Data Integration Monitor
#XMSG: Task log message for successfully starting a BW process chain
startBwProcessChainSuccess=La cadena de proceso de BW "{0}" ha empezado correctamente en el arrendatario de puente de SAP BW.
#XMSG: Task log message for skipping a BW process chain execution
bwProcessChainSkipped=La cadena de procesos BW "{0}" se ha omitido debido a la falta de disponibilidad de nuevos datos o a un error en una ejecución anterior.
#XMSG: Task log message for failure in starting a BW process chain
startBwProcessChainFailure=Se ha producido un error al iniciar la cadena de procesos BW "{0}" en el arrendatario de puente de SAP BW.
#XMSG: Task log message for failure in getting the status of a BW process chain
getBwProcessChainStatusFailure=La tarea ha fallado porque no hemos podido recuperar el estado de la cadena de procesos de BW "{0}" debido a un problema al establecer una conexión con el puente de SAP BW. Abra la aplicación "Conexiones" y valide la conexión con el puente de SAP BW en el espacio "{1}". Si no tiene acceso a la aplicación "Conexiones", póngase en contacto con el administrador.
#XMSG: Task log message for failure in completing a BW process chain
completeBwProcessChainFailure=Se ha producido un error al completar la cadena de procesos BW "{0}" en el arrendatario de puente de SAP BW.
#XMSG: Task log message for the clickable text of the SAP BW Bridge Monitor link
bwBridgeMonitorLinkText=Ver los detalles en el monitor de puente de SAP BW
#XMSG warning message while retrieving the task log messages if the user does not have access to the SAP BW Bridge space or to the shared object's space
missingPrivilegeOnSpaceError=Se ha producido un problema al recuperar los mensajes o no tiene el permiso necesario para verlos.

#Messages for API task log of the Data Integration Monitor
#XMSG: Task log message for displaying the API response
viewResponse=Respuesta recibida de "{0}".
#XMSG: Task log message for empty ID in the API response to retrieve the status
emptyJobId=No se ha devuelto ningún ID en el cuerpo de respuesta para la ruta JSON: "{0}".
#XMSG: Task log message for empty success value in the API response
emptySuccessValue=No se ha devuelto ningún valor de éxito en el cuerpo de respuesta para la ruta JSON: "{0}".
#XMSG: Task log message for empty error value in the API response
emptyErrorValue=No se ha devuelto ningún valor de error en el cuerpo de respuesta para la ruta JSON: "{0}".
#XMSG: Task log message for none of the success/error conditions matching for the values in the API response
conditionsNotFulfilled=Ninguna de las condiciones de indicador de éxito o error especificadas coincide con los valores de la respuesta.
#XMSG: Task log message for the API response status being unsuccessful
statusNotOk=La respuesta de "{1}" ha devuelto un código de estado erróneo: {0}.
#XMSG: Task log message for the API response body being empty
emptyResponseBody=El cuerpo de la respuesta estaba vacío.
#XMSG: Task log message for the API response location header being invalid
emptyLocationHeader=La cabecera de ubicación en la respuesta estaba vacía.
#XMSG: Task log message for failure in retrieving the status of the invoked API
getStatusFailure=No se ha podido recuperar el estado de la API invocada.
#XMSG: Task log message for failure in completing the API task
completionFailure=La tarea de la API "{0}" no se ha podido completar.
#XMSG: Task log message for a successful API task completion
apiCompleted=La tarea de API "{0}" ha concluido correctamente.
#XMSG: Task log message for an API task with an invalid configuration
invalidConfiguration=La configuración de la tarea de API "{0}" no es válida. Compruebe la configuración y vuelva a intentarlo.
#XMSG: Task log message for the API task being canceled
cancelStart=Se ha solicitado la cancelación de la tarea de API "{0}" con logId {1}.
#XMSG: Task log message for the cancelation of the an API task which is no longer running and cannot be canceled
unableToCancel=La tarea de API "{0}" con logId {1} ya no se está ejecutando y no se puede cancelar.
#XMSG: Task log message when starting an API task. It should carry the name of the API task.
apiRunStart=Iniciando la tarea de API "{0}".
#XMSG: Task log message when the task preparation is taking too long and times out
preparationTimeout=La preparación de la tarea está tardando demasiado y se ha agotado el tiempo.
#XMSG: Task log message when a task failed because a cancel task was triggered
apiRunCancelled=La tarea de ejecución con logId {0} ha sido cancelada por la tarea de cancelación {1}.
#XMSG: Task log message when a cancel task failed to cancel a run task
cancelFailure=La tarea de cancelación con logId {1} no puede cancelar la tarea de ejecución con logId {0}.
#XMSG: Task log message when configuration of the API task is too large
apiTaskconfigurationSizeLimitError=No se puede iniciar la tarea de API {0} porque el tamaño de la configuración de su tarea supera el tamaño máximo permitido de 100 kibibytes (KiB).
#XMSG: Task log message for failure in completing the Notification task
notificationTaskCompletionFailure=No se ha podido concluir la tarea de notificación "{0}".
#XMSG: Task log message when starting a Notification task.
notificationTaskRunStart=Se está iniciando la tarea de notificación "{0}".
#XMSG: Task log message for a successful Notification task completion
notificationTaskCompletionSuccess=La tarea de notificación "{0}" ha concluido.
#XFLD: Label for frequency column
everyLabel=Cada
#XFLD: Plural Recurrence text for Hour
hoursLabel=Horas
#XFLD: Plural Recurrence text for Day
daysLabel=Días
#XFLD: Plural Recurrence text for Month
monthsLabel=Meses
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minutos
