

#XFLD: Task Chain header
headerTxt=Görev zincirleri ({0})
#XFLD: Task Chain header
headerTxtNew=Görev zincirleri ({0})
#XTXT: Text for Schedule label
scheduleTxt=Planlama
#XFLD: Text for Create schedule button
createScheduleTxt=Planlama oluştur
#XFLD: Text for edit schedule
editScheduleTxt=Planlamayı düzenle
#XLFD: Text for delete schedule
deleteScheduleTxt=Planlamayı sil
#XTXT: text for refresh button label
refrestTxt=Yenile
#XTXT: Text for Completed status
completedTxt=Tamamlandı
#XTX: Text for Running status
runningTxt=Çalışıyor
#XTX: Text for failed status
failedTxt=Başarısız oldu
#XTX: Text for stopped status
stoppedTxt=Durduruldu
#XTX: Text for stopping status
stoppingTxt=Durduruluyor
#XFLD: Header for Chain name
chainNameLabel=Görev zinciri adı
#XFLD: Header for Chain name
chainNameLabelBus=İş adı
#XFLD: Header for Chain name
chainNameLabelBusNew=Nesne (iş adı)
#XFLD: Header for Chain name
chainNameLabelTech=Teknik ad
#XFLD: Header for Chain name
chainNameLabelTechNew=Nesne (teknik ad)
#XFLD: Last Run Status label
lastRunStatuslabel=Son çalıştırma durumu
#XFLD: Last Run Status label
lastRunStatuslabelNew=Durum
#XFLD: Frequency Label
frequencyLabel=Sıklık
#XFLD: Frequency Label
frequencyLabelNew=Planlanan sıklık
#XFLD: Duration label
durationLabel=Süre
#XFLD: Duration label
durationLabelNew=Son çalıştırma süresi
#XFLD: Run Start label
runStartLabel=Son çalıştırma başlangıcı
#XFLD: Run end label
runEndLabel=Son çalıştırma bitişi
#XFLD: Next Run label
nextRunlabel=Sonraki çalıştırma
#XFLD: Next Run label
nextRunlabelNew=Sonraki planlı çalıştırma
#XTXT: Task chain logs link tooltip text
taskChainLogsTxt=Görev zinciri günlük ayrıntıları
#XTXT: Task chain logs link tooltip text
taskChainLogsTxtNew=Ayrıntılar
#XTXT: Scheduled text
scheduledTxt=Planlandı
#XTXT: Paused text
pausedTxt=Duraklatıldı
#XTXT: Execute button label
runLabel=Çalıştır
#XTXT: Execute button label
runLabelNew=Çalıştırmayı başlat
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Görev zincirini çalıştır
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Görev zinciri çalıştırması başlatıldı.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg={0} için görev zinciri çalıştırması başlatıldı
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Görev zinciri çalıştırılamadı.
#XFLD: Label for schedule owner column
txtScheduleOwner=Planlama sorumlusu
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Planlamayı oluşturan kişiyi gösterir
#XMSG: Task log message for start chain
startChain=Görev zinciri çalıştırması başlatılıyor.
#XMSG: Task log message for load chain from repository
loadChainFromRepository=Görev zinciri yüklendi ve başlangıç durumuna getirildi.
#XMSG: Task log message started task
taskStarted=Görev {0} başlatıldı.
#XMSG: Task log message for finished task
taskFinished=Görev {0}, {1} durumu ile sonlandırıldı.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Görev zinciri yükleniyor ve bu zincirin parçası olan toplam {0} görevi çalıştırmaya hazırlanılıyor.
#XMSG: Task log message for starting a subtask
chainStartSubtask={0} görevini çalıştırma tetikleniyor. Görev tanıtıcısı = {1}.
#XMSG: Task log message for finishing a subtask
chainFinishSubtask=Görev {0}, {1} durumuyla tamamlandı.
#XMSG: Task log message for indicating chain success
chainCompleted={0} görevin tümü tamamlandı. Görev zinciri durumu tamamlandı olarak belirlendi.
#XMSG: Task log message for indicating chain failure
chainFailed=Toplam {0} görevden {1} görev tamamlanabildi ve {2} görev başarısız oldu. Görev zinciri durumu başarısız olarak belirlendi.
#XMSG: Task log message for indicating chain cancelation
chainCanceled=Görev zinciri çalıştırmasının iptali başlatıldı. İptal görevinin günlük tanıtıcısı {0}.
#XMSG: Task log message for indicating chain cancelation in the cancel task
chainCanceling={0} zinciri iptal ediliyor.
#XMSG: Task log message for general chain runtime error
chainError=Görev zinciri çalıştırılırken beklenmeyen hata oluştu.
#XMSG: Task log message for starting the overwrite check.
chainOverwriteStart={0} tanıtıcılı görev zincirinin tamamlanıp tamamlanmadığı kontrol ediliyor.
#XMSG: Task log message indicating the chain is too old.
chainOverwriteTooOld=Zincir {0} için çalıştırılacak görev bulunamadı. Çalıştırma yaşı {1} dakika. Görev zinciri durumu başarısız olarak belirlendi.
#XMSG: Task log message indicating the chain was created too recently.
chainOverwriteTooRecent=Zincir {0} için çalıştırılacak görev bulunamadı. Çalıştırmanın yaşı {1} dakika. Görev zinciri hâlâ çalışıyor.
#XMSG: Task log message indicating a recent heartbeat was found.
chainOverwriteHeartbeatAlive=Görev zinciri {0} hâlâ çalışıyor. Tamamlanan: {1}, çalışan: {2}, başarısız: {3}, tetiklenen: {4}, tetiklenmeyen: {5}.
#XMSG: Task log message for a chain that is not running anymore but not everything was triggered.
chainOverwriteNotAllTriggered=Görev zinciri {0} çalıştırılırken hata oluştu ve tüm görevler tetiklenemedi. Tamamlanan: {1}, çalışan: {2}, başarısız: {3}, tetiklenen: {4}, tetiklenmeyen: {5}. Bu görev zincirinin üzerine yazıldı.
#XMSG: Task log message for a chain that contains a failed subtask.
chainOverwriteAnySubtaskFailed=Görev zinciri {0} tamamlandı. Görev zincirinin bir börevi başarısız oldu, zincir başarısız olarak belirlenecek.
#XMSG: Task log message could not determine status.
chainOverwriteNotDetermined=Beklenmeyen bir hata, görevin durumuna erişmemizi engelliyor. Hata devam ederse SAP desteğine başvurun.
#XMSG: Task log message could not take over
failedTakeover=Mevcut görev devralınamadı.
#XMSG: Task log parallel check error
parallelCheckError=Bu görevi zaten kilitleyen başka bir görev çalışmakta olduğu için görev işlenemiyor.
#XMSG: Task log parallel task runnig error
parallelTaskRunning=Çakışan bir görev zaten çalışıyor.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Çalışma sırasında korelasyon tanıtıcısı {1} ile durum {0}.
#XMSG: Task log message successful takeover
successTakeover=Bırakılan kilidin açılması gerekiyordu. Bu görev için yeni kilit belirlendi.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Bu görevi kilitleme başka bir görev tarafından devralındı.
#XMSG: Schedule created alert message
createScheduleSuccess=Planlama oluşturuldu
#XMSG: Task log message for taskFinishedAt
taskFinishedAt=Görev {0}, {2} saatinde {1} durumuyla tamamlandı.
#XMSG: Placeholder symbol for empty columns
emptyCol=--
#XMSG: Paused Text
txtExpired=Süresi doldu
#XMSG: Update task schedule success Toast message
updateScheduleSuccess=Planlama güncellendi
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Planlama silindi.
#XMSG: Task chain might have children error text
taskChainNoChildErrorText=Plan oluşturulmadan görev başarısız olduğundan, görev zincirinde görüntülenmeyen alt öğeler olabilir.
#XMSG: Task chain repair recent failed run label
retryRunLabel=Son çalıştırmayı yeniden dene
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Görev zinciri yeniden deneme çalıştırması başlatıldı
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Görev zinciri yeniden deneme çalıştırması başarısız oldu
#XMSG: chain repair message
chainRetried=Görev zinciri yeniden denemesi {0} kullanıcısı tarafından tetiklendi
#XMSG: chain not found during run
chainNotFoundDuringRun=Görev zinciri {0} bulunamadı. Görev zincirinin mevcut olup olmadığını Veri oluşturucu aracılığıyla kontrol edin ve zincirin dağıtıldığından emin olun.
#XMSG: chain is not DAG
chainNotDag=Görev zinciri {0} başlatılamıyor. Zincir yapısı geçersiz. Görev zincirini veri oluşturucuda kontrol edin.
#XMSG: chain has not valid parameters
notValidParameters=Görev zinciri {0} başlatılamıyor. Zincirin bir veya daha fazla parametresi geçersiz. Görev zincirini veri oluşturucuda kontrol edin.
#XMSG: Configuration parameter value size exceeds the maximum allowed limit
configurationSizeLimitError=Görev zinciri {0} başlatılamıyor. Zincirde görevin konfigürasyonunun boyutu, izin verilen 100 kibibaytlık (KiB) azami boyutu aşıyor.
#XMSG: Task {0} has input parameters
taskHasInputParameters={0} görevinde girdi parametreleri var.
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Çakışan bir görev zaten çalışıyor
#XMSG: error message for reading data from backend
txtReadBackendError=Görünüşe göre arka uçtan okuma sırasında hata oluştu.
##XMSG: error message for admission control rejection
admissionControlError=Görev bir SAP HANA giriş kontrolü reddi nedeniyle başarısız oldu.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Planlamayı bana tayin et
#XBUT: Pause schedule menu label
pauseScheduleLabel=Planlamayı duraklat
#XBUT: Resume schedule menu label
resumeScheduleLabel=Planlamayı sürdür
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Planlamalar kaldırılırken hata oluştu.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Planlamalar tayin edilirken hata oluştu.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Planlamalar duraklatılırken hata oluştu.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Planlamalar sürdürülürken hata oluştu.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted={0} planlama siliniyor
#XMSG: Message for starting mass assign of schedules
massAssignStarted={0} planlamanın sahibi değiştiriliyor
#XMSG: Message for starting mass pausing of schedules
massPauseStarted={0} planlama duraklatılıyor
#XMSG: Message for starting mass resuming of schedules
massResumeStarted={0} planlama sürdürülüyor
#XBUT: Select Columns Button
selectColumnsBtn=Sütun seç
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Plan oluşturulmadan önce önceki görev çalıştırması başarısız olduğundan en son çalıştırmayı yeniden denemeye ilişkin hata oluşmuş gibi görünüyor.
#XFLD: Refresh tooltip
TEXT_REFRESH=Yenile
#XFLD: Select Columns tooltip
text_selectColumns=Sütun seç

#Messages for BW Process Chain task log of the Data Integration Monitor
#XMSG: Task log message for successfully starting a BW process chain
startBwProcessChainSuccess=BW süreç zinciri "{0}", SAP BW köprüsü kiracısında başarıyla başlatıldı.
#XMSG: Task log message for skipping a BW process chain execution
bwProcessChainSkipped=BW süreç zinciri "{0}", yeni verilerin kullanılabilir olmaması veya önceki yürütmenin başarısız olması nedeniyle atlandı.
#XMSG: Task log message for failure in starting a BW process chain
startBwProcessChainFailure=BW süreç zinciri "{0}", SAP BW köprüsü kiracısında başlatılamadı.
#XMSG: Task log message for failure in getting the status of a BW process chain
getBwProcessChainStatusFailure=SAP BW köprüsüyle bağlantı kurmada yaşanan bir sorun nedeniyle "{0}" BW süreç zincirinin durumunu alamadığımızdan görev başarısız oldu. "Bağlantılar" uygulamasını açın ve "{1}" alanında SAP BW köprüsü bağlantısını doğrulayın. "Bağlantılar" uygulamasına erişiminiz yoksa yöneticinizle irtibata geçin.
#XMSG: Task log message for failure in completing a BW process chain
completeBwProcessChainFailure=BW süreç zinciri "{0}", SAP BW köprüsü kiracısında tamamlanamadı.
#XMSG: Task log message for the clickable text of the SAP BW Bridge Monitor link
bwBridgeMonitorLinkText=SAP BW köprüsü izlemede ayrıntıları görüntüle
#XMSG warning message while retrieving the task log messages if the user does not have access to the SAP BW Bridge space or to the shared object's space
missingPrivilegeOnSpaceError=İletiler alınırken sorun oluştu veya iletileri görüntülemek için gerekli izne sahip değilsiniz.

#Messages for API task log of the Data Integration Monitor
#XMSG: Task log message for displaying the API response
viewResponse="{0}" için yanıt alındı.
#XMSG: Task log message for empty ID in the API response to retrieve the status
emptyJobId=JSON yolu için yanıt gövdesinde tanıtıcı döndürülmedi: "{0}".
#XMSG: Task log message for empty success value in the API response
emptySuccessValue=JSON yolu için yanıt gövdesinde başarı değeri döndürülmedi: "{0}".
#XMSG: Task log message for empty error value in the API response
emptyErrorValue=JSON yolu için yanıt gövdesinde hata değeri döndürülmedi: "{0}".
#XMSG: Task log message for none of the success/error conditions matching for the values in the API response
conditionsNotFulfilled=Belirtilen başarı veya hata göstergesi koşullarından hiçbiri yanıttaki değerlerle eşleşmiyor.
#XMSG: Task log message for the API response status being unsuccessful
statusNotOk="{1}" için yanıt, başarısız bir durum kodu döndürdü: {0}
#XMSG: Task log message for the API response body being empty
emptyResponseBody=Yanıt gövdesi boş.
#XMSG: Task log message for the API response location header being invalid
emptyLocationHeader=Yanıttaki konum başlığı boştu.
#XMSG: Task log message for failure in retrieving the status of the invoked API
getStatusFailure=Çağrılan API'nin durumu alınamadı.
#XMSG: Task log message for failure in completing the API task
completionFailure=API görevi "{0}" tamamlanamadı.
#XMSG: Task log message for a successful API task completion
apiCompleted=API görevi "{0}" başarıyla tamamlandı.
#XMSG: Task log message for an API task with an invalid configuration
invalidConfiguration="{0}" API görevinin konfigürasyonu geçersiz. Konfigürasyonu kontrol edip tekrar deneyin.
#XMSG: Task log message for the API task being canceled
cancelStart={1} logId içeren "{0}" API görevine ilişkin iptal işlemi talep edildi.
#XMSG: Task log message for the cancelation of the an API task which is no longer running and cannot be canceled
unableToCancel={1} logId içeren "{0}" API görevi artık çalışmıyor ve iptal edilemiyor.
#XMSG: Task log message when starting an API task. It should carry the name of the API task.
apiRunStart="{0}" API görevi başlatılıyor.
#XMSG: Task log message when the task preparation is taking too long and times out
preparationTimeout=Görev hazırlama çok uzun sürüyor ve zaman aşımına uğradı.
#XMSG: Task log message when a task failed because a cancel task was triggered
apiRunCancelled={0} logId içeren çalıştırma görevi {1} iptal görevi tarafından iptal edildi.
#XMSG: Task log message when a cancel task failed to cancel a run task
cancelFailure={1} logId içeren iptal görevi {0} logId içeren çalıştırma görevini iptal edemedi.
#XMSG: Task log message when configuration of the API task is too large
apiTaskconfigurationSizeLimitError=Görev konfigürasyonunun boyutu izin verilen 100 kibibaytlık (KiB) azami boyutu aştığından API görevi {0} başlatılamıyor.
#XMSG: Task log message for failure in completing the Notification task
notificationTaskCompletionFailure=Bildirim görevi "{0}" tamamlanamadı.
#XMSG: Task log message when starting a Notification task.
notificationTaskRunStart=Bildirim görevi "{0}" başlatılıyor.
#XMSG: Task log message for a successful Notification task completion
notificationTaskCompletionSuccess=Bildirim görevi "{0}" tamamlandı.
#XFLD: Label for frequency column
everyLabel=Her
#XFLD: Plural Recurrence text for Hour
hoursLabel=Saat
#XFLD: Plural Recurrence text for Day
daysLabel=Gün
#XFLD: Plural Recurrence text for Month
monthsLabel=Ay
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Dakika
