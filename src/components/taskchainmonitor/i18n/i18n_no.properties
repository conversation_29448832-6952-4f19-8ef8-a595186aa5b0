

#XFLD: Task Chain header
headerTxt=Oppgavekjeder ({0})
#XFLD: Task Chain header
headerTxtNew=Oppgavekjeder ({0})
#XTXT: Text for Schedule label
scheduleTxt=Tidsplan
#XFLD: Text for Create schedule button
createScheduleTxt=Opprett tidsplan
#XFLD: Text for edit schedule
editScheduleTxt=Rediger tidsplan
#XLFD: Text for delete schedule
deleteScheduleTxt=Slett tidsplan
#XTXT: text for refresh button label
refrestTxt=Oppdater
#XTXT: Text for Completed status
completedTxt=Fullført
#XTX: Text for Running status
runningTxt=Kjører
#XTX: Text for failed status
failedTxt=Mislykket
#XTX: Text for stopped status
stoppedTxt=Stoppet
#XTX: Text for stopping status
stoppingTxt=Stopper
#XFLD: Header for Chain name
chainNameLabel=Navn på oppgavekjede
#XFLD: Header for Chain name
chainNameLabelBus=Forretningsnavn
#XFLD: Header for Chain name
chainNameLabelBusNew=Objekt (forretningsnavn)
#XFLD: Header for Chain name
chainNameLabelTech=Teknisk navn
#XFLD: Header for Chain name
chainNameLabelTechNew=Objekt (teknisk navn)
#XFLD: Last Run Status label
lastRunStatuslabel=Status for forrige kjøring
#XFLD: Last Run Status label
lastRunStatuslabelNew=Status
#XFLD: Frequency Label
frequencyLabel=Frekvens
#XFLD: Frequency Label
frequencyLabelNew=Planlagt frekvens
#XFLD: Duration label
durationLabel=Varighet
#XFLD: Duration label
durationLabelNew=Varighet for siste kjøring
#XFLD: Run Start label
runStartLabel=Start forrige kjøring
#XFLD: Run end label
runEndLabel=Slutt forrige kjøring
#XFLD: Next Run label
nextRunlabel=Neste kjøring
#XFLD: Next Run label
nextRunlabelNew=Neste planlagte kjøring
#XTXT: Task chain logs link tooltip text
taskChainLogsTxt=Detaljer i oppgavekjedeprotokoll
#XTXT: Task chain logs link tooltip text
taskChainLogsTxtNew=Detaljer
#XTXT: Scheduled text
scheduledTxt=Planlagt
#XTXT: Paused text
pausedTxt=Satt på pause
#XTXT: Execute button label
runLabel=Kjør
#XTXT: Execute button label
runLabelNew=Start kjøring
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Kjør oppgavekjeden
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Kjøringen av oppgavekjeden er startet.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Kjøring av oppgavekjeden er startet for {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Kan ikke kjøre oppgavekjeden.
#XFLD: Label for schedule owner column
txtScheduleOwner=Eier av tidsplan
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Viser hvem som opprettet tidsplanen
#XMSG: Task log message for start chain
startChain=Starter kjøring av oppgavekjeden.
#XMSG: Task log message for load chain from repository
loadChainFromRepository=Oppgavekjeden er lastet og initialisert.
#XMSG: Task log message started task
taskStarted=Oppgave {0} er startet.
#XMSG: Task log message for finished task
taskFinished=Oppgave {0} ble avsluttet med status {1}.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Laster oppgavekjede og forbereder kjøring av totalt {0} oppgaver som er del av denne kjeden.
#XMSG: Task log message for starting a subtask
chainStartSubtask=Utløser oppgave {0} for kjøring. Oppgave-ID = {1}.
#XMSG: Task log message for finishing a subtask
chainFinishSubtask=Oppgave {0} utført med statusen {1}.
#XMSG: Task log message for indicating chain success
chainCompleted=Alle {0} oppgaver er utført. Statusen for oppgavekjeden er satt til utført.
#XMSG: Task log message for indicating chain failure
chainFailed=Av totalt {0} oppgaver ble {1} oppgaver utført og {2} oppgaver mislyktes. Statusen for oppgavekjeden er satt til mislykket.
#XMSG: Task log message for indicating chain cancelation
chainCanceled=Kjøringen av oppgavekjeden avbrytes. Protokoll-ID for avbrytingsoppgaven er {0}.
#XMSG: Task log message for indicating chain cancelation in the cancel task
chainCanceling=Avbryter kjeden {0}.
#XMSG: Task log message for general chain runtime error
chainError=Det oppstod en uventet feil under kjøring av oppgavekjeden.
#XMSG: Task log message for starting the overwrite check.
chainOverwriteStart=Kontrollerer om oppgavekjeden med ID {0} er utført.
#XMSG: Task log message indicating the chain is too old.
chainOverwriteTooOld=Finner ingen oppgaver å kjøre for kjeden {0}. Kjøringen er {1} minutter gammel. Statusen for oppgavekjeden er satt til mislykket.
#XMSG: Task log message indicating the chain was created too recently.
chainOverwriteTooRecent=Finner ingen oppgaver å kjøre for kjeden {0}. Kjøringen er {1} minutter gammel. Oppgavekjeden kjører fortsatt.
#XMSG: Task log message indicating a recent heartbeat was found.
chainOverwriteHeartbeatAlive=Oppgavekjeden {0} kjører fortsatt. Utført: {1}, kjører: {2}, mislykket: {3}, utløst: {4}, ikke utløst: {5}.
#XMSG: Task log message for a chain that is not running anymore but not everything was triggered.
chainOverwriteNotAllTriggered=Det oppstod en feil under kjøringen av oppgavekjeden {0}, og alle oppgavene ble ikke utløst. Utført: {1}, kjører: {2}, mislykket: {3}, utløst: {4}. ikke utløst: {5}. Denne oppgaven er overskrevet.
#XMSG: Task log message for a chain that contains a failed subtask.
chainOverwriteAnySubtaskFailed=Oppgavekjeden {0} er utført. Én oppgave i kjeden mislyktes, kjeden settes til mislykket.
#XMSG: Task log message could not determine status.
chainOverwriteNotDetermined=En uventet feil forhindrer oss i få tilgang til oppgavens status. Prøv på nytt, og ta kontakt med SAP-brukerstøtte hvis feilen vedvarer.
#XMSG: Task log message could not take over
failedTakeover=Kan ikke overta eksisterende oppgave.
#XMSG: Task log parallel check error
parallelCheckError=Oppgaven kan ikke behandles fordi en annen oppgave kjøres, og den sperrer allerede denne oppgaven.
#XMSG: Task log parallel task runnig error
parallelTaskRunning=En oppgave i konflikt kjøres allerede.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Status {0} under kjøring med korrelasjons-ID {1}.
#XMSG: Task log message successful takeover
successTakeover=Det var nødvendig å oppheve resterende sperre. Ny sperre er fastsatt for denne oppgaven.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=En annen oppgave har overtatt sperringen av denne oppgaven.
#XMSG: Schedule created alert message
createScheduleSuccess=Tidsplan opprettet
#XMSG: Task log message for taskFinishedAt
taskFinishedAt=Oppgave {0} utført kl. {2} med statusen {1}.
#XMSG: Placeholder symbol for empty columns
emptyCol=--
#XMSG: Paused Text
txtExpired=Utløpt
#XMSG: Update task schedule success Toast message
updateScheduleSuccess=Tidsplan oppdatert
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Tidsplanen er slettet.
#XMSG: Task chain might have children error text
taskChainNoChildErrorText=Oppgavekjeden kan ha underordnede elementer som ikke vises fordi oppgaven mislyktes før planen kunne genereres.
#XMSG: Task chain repair recent failed run label
retryRunLabel=Forsøk siste kjøring på nytt
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Nytt forsøk på kjøring av oppgavekjede er startet
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Nytt forsøk på kjøring av oppgavekjede mislyktes
#XMSG: chain repair message
chainRetried=Nytt forsøk for oppgavekjede er utløst av brukeren {0}
#XMSG: chain not found during run
chainNotFoundDuringRun=Finner ikke oppgavekjeden {0}. Se om den finnes via databyggeren, og pass på at kjeden er distribuert.
#XMSG: chain is not DAG
chainNotDag=Kan ikke starte oppgavekjede {0}. Strukturen er ugyldig. Sjekk oppgavekjeden i databyggeren.
#XMSG: chain has not valid parameters
notValidParameters=Kan ikke starte oppgavekjede {0}. Én eller flere parametere er ugyldig. Sjekk oppgavekjeden i databyggeren.
#XMSG: Configuration parameter value size exceeds the maximum allowed limit
configurationSizeLimitError=Kan ikke starte oppgavekjeden {0}. Størrelsen på konfigurasjonen til en oppgave i kjeden overskrider maksimal tillatt størrelse på 100 kibibytes (KiB).
#XMSG: Task {0} has input parameters
taskHasInputParameters=Oppgave ({0}) har inndataparametere.
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=En oppgave i konflikt kjøres allerede
#XMSG: error message for reading data from backend
txtReadBackendError=Det ser som det oppstod en feil under lesing fra backend.
##XMSG: error message for admission control rejection
admissionControlError=Oppgaven mislyktes på grunn av avvisning av SAP HANA-tilgangskontroll.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Tilordne tidsplan til meg
#XBUT: Pause schedule menu label
pauseScheduleLabel=Sett tidsplan på pause
#XBUT: Resume schedule menu label
resumeScheduleLabel=Fortsett tidsplan
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Det oppstod en feil ved fjerning av tidsplaner.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Det oppstod en feil ved tilordning av tidsplaner.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Det oppstod en feil ved pause av tidsplaner.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Det oppstod en feil ved fortsetting av tidsplaner.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Sletter {0} tidsplaner
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Endrer eieren av {0} tidsplaner
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Setter {0} tidsplaner på pause
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Fortsetter {0} tidsplaner
#XBUT: Select Columns Button
selectColumnsBtn=Velg kolonner
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Det ser ut til at det oppstod en feil da siste kjøring ble forsøkt på nytt. Årsaken er at den forrige oppgavekjøringen mislyktes før planen kunne generes.
#XFLD: Refresh tooltip
TEXT_REFRESH=Oppdater
#XFLD: Select Columns tooltip
text_selectColumns=Velg kolonner

#Messages for BW Process Chain task log of the Data Integration Monitor
#XMSG: Task log message for successfully starting a BW process chain
startBwProcessChainSuccess=BW-prosesskjeden "{0}" er startet i SAP BW-brotenanten.
#XMSG: Task log message for skipping a BW process chain execution
bwProcessChainSkipped=BW-prosesskjeden "{0}" ble hoppet over enten på grunn av utilgjengelige nye data eller at en tidligere utføring mislyktes.
#XMSG: Task log message for failure in starting a BW process chain
startBwProcessChainFailure=Starting av BW-prosesskjeden "{0}" i SAP BW-brotenanten mislyktes.
#XMSG: Task log message for failure in getting the status of a BW process chain
getBwProcessChainStatusFailure=Oppgaven mislyktes fordi vi ikke kunne hente status for BW-prosesskjeden "{0}" på grunn av et problem med å opprette forbindelse med SAP BW Bridge. Åpne appen "Forbindelser" og valider SAP BW Bridge-forbindelsen i området "{1}". Hvis du ikke har tilgang til appen "Forbindelser", må du kontakte administratoren.
#XMSG: Task log message for failure in completing a BW process chain
completeBwProcessChainFailure=Fullføring av BW-prosesskjeden "{0}" i SAP BW-brotenanten mislyktes.
#XMSG: Task log message for the clickable text of the SAP BW Bridge Monitor link
bwBridgeMonitorLinkText=Vis detaljer i SAP BW Monitor
#XMSG warning message while retrieving the task log messages if the user does not have access to the SAP BW Bridge space or to the shared object's space
missingPrivilegeOnSpaceError=Det oppstod et problem ved henting av meldinger, eller du mangler autorisasjonen som kreves for å vise meldingene.

#Messages for API task log of the Data Integration Monitor
#XMSG: Task log message for displaying the API response
viewResponse=Svar mottatt for "{0}".
#XMSG: Task log message for empty ID in the API response to retrieve the status
emptyJobId=Ingen ID ble returnert i svarteksten for JSON-banen "{0}".
#XMSG: Task log message for empty success value in the API response
emptySuccessValue=Ingen resultatverdi ble returnert i svarteksten for JSON-banen "{0}".
#XMSG: Task log message for empty error value in the API response
emptyErrorValue=Ingen feilverdi ble returnert i svarteksten for JSON-banen: "{0}".
#XMSG: Task log message for none of the success/error conditions matching for the values in the API response
conditionsNotFulfilled=Ingen av de oppgitte betingelsene for resultat- eller feilindikator stemmer overens med verdiene i svaret.
#XMSG: Task log message for the API response status being unsuccessful
statusNotOk=Svaret for "{1}" returnerte en mislykket statuskode: {0}.
#XMSG: Task log message for the API response body being empty
emptyResponseBody=Svarteksten er tom.
#XMSG: Task log message for the API response location header being invalid
emptyLocationHeader=Lokaliseringstoppteksten i svaret er tom.
#XMSG: Task log message for failure in retrieving the status of the invoked API
getStatusFailure=Statusen for den anropte API-en kan ikke hentes.
#XMSG: Task log message for failure in completing the API task
completionFailure=Fullføring av API-oppgave "{0}" mislyktes.
#XMSG: Task log message for a successful API task completion
apiCompleted=API-oppgaven "{0}" er fullført.
#XMSG: Task log message for an API task with an invalid configuration
invalidConfiguration=Konfigurasjonen av API-oppgaven "{0}" er ugyldig. Kontroller konfigurasjonen og prøv igjen.
#XMSG: Task log message for the API task being canceled
cancelStart=Bad om avbrytelse av API-oppgaven "{0}" med logId {1}.
#XMSG: Task log message for the cancelation of the an API task which is no longer running and cannot be canceled
unableToCancel=API-oppgaven "{0}" med logId {1} kjører ikke lenger, og det er ikke mulig å avbryte den.
#XMSG: Task log message when starting an API task. It should carry the name of the API task.
apiRunStart=Starer API-oppgaven "{0}".
#XMSG: Task log message when the task preparation is taking too long and times out
preparationTimeout=Klargjøringen av oppgaven tar for lang tid, og den er tidsavbrutt.
#XMSG: Task log message when a task failed because a cancel task was triggered
apiRunCancelled=Kjøreoppgaven med logId {0} ble avbrutt av den avbrutte oppgaven {1}.
#XMSG: Task log message when a cancel task failed to cancel a run task
cancelFailure=Den avbrutte oppgaven med logId {1} kunne ikke avbryte kjøreoppgaven med logId {0}.
#XMSG: Task log message when configuration of the API task is too large
apiTaskconfigurationSizeLimitError=Kan ikke starte API-oppgaven {0} fordi størrelsen på oppgavekonfigurasjonen overskrider maksimal tillatt størrelse på 100 kibibytes (KiB).
#XMSG: Task log message for failure in completing the Notification task
notificationTaskCompletionFailure=Fullføring av varslingsoppgaven "{0}" mislyktes.
#XMSG: Task log message when starting a Notification task.
notificationTaskRunStart=Starter varslingsoppgaven "{0}".
#XMSG: Task log message for a successful Notification task completion
notificationTaskCompletionSuccess=Varslingsoppgaven "{0}" er fullført.
#XFLD: Label for frequency column
everyLabel=Hver
#XFLD: Plural Recurrence text for Hour
hoursLabel=Timer
#XFLD: Plural Recurrence text for Day
daysLabel=Dager
#XFLD: Plural Recurrence text for Month
monthsLabel=Måneder
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minutter
