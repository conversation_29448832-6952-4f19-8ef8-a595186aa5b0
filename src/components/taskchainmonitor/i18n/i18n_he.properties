

#XFLD: Task Chain header
headerTxt=שרשראות משימות ({0})
#XFLD: Task Chain header
headerTxtNew=שרשראות משימות ({0})
#XTXT: Text for Schedule label
scheduleTxt=תזמון
#XFLD: Text for Create schedule button
createScheduleTxt=צור תזמון
#XFLD: Text for edit schedule
editScheduleTxt=ערוך תזמון
#XLFD: Text for delete schedule
deleteScheduleTxt=מחק תזמון
#XTXT: text for refresh button label
refrestTxt=רענן
#XTXT: Text for Completed status
completedTxt=הושלם
#XTX: Text for Running status
runningTxt=פועל
#XTX: Text for failed status
failedTxt=נכשל
#XTX: Text for stopped status
stoppedTxt=נעצר
#XTX: Text for stopping status
stoppingTxt=מתבצעת עצירה
#XFLD: Header for Chain name
chainNameLabel=שם שרשרת משימות
#XFLD: Header for Chain name
chainNameLabelBus=שם עסקי
#XFLD: Header for Chain name
chainNameLabelBusNew=אובייקט (שם עסקי)
#XFLD: Header for Chain name
chainNameLabelTech=שם טכני
#XFLD: Header for Chain name
chainNameLabelTechNew=אובייקט (שם טכני)
#XFLD: Last Run Status label
lastRunStatuslabel=סטאטוס הפעלה אחרונה
#XFLD: Last Run Status label
lastRunStatuslabelNew=סטאטוס
#XFLD: Frequency Label
frequencyLabel=תדירות
#XFLD: Frequency Label
frequencyLabelNew=תדירות מתוזמנת
#XFLD: Duration label
durationLabel=משך זמן
#XFLD: Duration label
durationLabelNew=משך זמן הפעלה אחרונה
#XFLD: Run Start label
runStartLabel=תחילת הפעלה אחרונה
#XFLD: Run end label
runEndLabel=סיום הפעלה אחרונה
#XFLD: Next Run label
nextRunlabel=ההפעלה הבאה
#XFLD: Next Run label
nextRunlabelNew=ההפעלה המתוזמנת הבאה
#XTXT: Task chain logs link tooltip text
taskChainLogsTxt=פרטי יומן של שרשרת משימות
#XTXT: Task chain logs link tooltip text
taskChainLogsTxtNew=פרטים
#XTXT: Scheduled text
scheduledTxt=מתוזמן
#XTXT: Paused text
pausedTxt=מושהה
#XTXT: Execute button label
runLabel=הפעלה
#XTXT: Execute button label
runLabelNew=התחל הפעלה
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=הפעל את שרשרת המשימות
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=הפעלת שרשרת המשימות החלה.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=הפעלת שרשרת המשימות החלה עבור {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=הפעלת שרשרת המשימות נכשלה.
#XFLD: Label for schedule owner column
txtScheduleOwner=בעלי לוח הזמנים
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=מציג את מי שיצר את לוח הזמנים
#XMSG: Task log message for start chain
startChain=מתחיל הפעלה של שרשרת משימות.
#XMSG: Task log message for load chain from repository
loadChainFromRepository=שרשרת משימות נטענה ואותחלה.
#XMSG: Task log message started task
taskStarted=משימה {0} התחילה.
#XMSG: Task log message for finished task
taskFinished=משימה {0} הסתיימה בסטאטוס {1}.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=טוען שרשרת משימה ומתכונן להפעלה כוללת של {0} משימות שהן חלק מהשרשרת.
#XMSG: Task log message for starting a subtask
chainStartSubtask=מפעיל משימה {0} להפעלה. זיהוי משימה = {1}.
#XMSG: Task log message for finishing a subtask
chainFinishSubtask=משימה {0} הסתיימה עם סטאטוס {1}.
#XMSG: Task log message for indicating chain success
chainCompleted=כל {0} המשימות הושלמו. סטאטוס של שרשרת משימות נקבע כ''הושלם''.
#XMSG: Task log message for indicating chain failure
chainFailed=בסך הכול של {0} משימות, {1} משימות ניתנות להשלמה ו-{2} משימות נכשלו. סטאטוס של שרשרת משימות נקבע כ''נכשל''.
#XMSG: Task log message for indicating chain cancelation
chainCanceled=ביטול הפעלת שרשרת המשימות החל. מזהה היומן של משימת הביטול הוא {0}.
#XMSG: Task log message for indicating chain cancelation in the cancel task
chainCanceling=שרשרת ביטול {0}.
#XMSG: Task log message for general chain runtime error
chainError=שגיאה בלתי צפויה אירעה בעת הפעלת שרשרת משימות.
#XMSG: Task log message for starting the overwrite check.
chainOverwriteStart=בודק אם שרשרת משימות עם זיהוי {0} הסתיימה.
#XMSG: Task log message indicating the chain is too old.
chainOverwriteTooOld=לא ניתן היה למצוא משימות להפעלה עבור שרשרת {0}. הפעלה בת {1} דקות. סטאטוס של שרשרת משימות נקבע כ''הושלם''.
#XMSG: Task log message indicating the chain was created too recently.
chainOverwriteTooRecent=לא ניתן היה למצוא משימות להפעלה עבור שרשרת {0}. הפעלה בת {1} דקות. שרשרת משימות עדיין עובדת.
#XMSG: Task log message indicating a recent heartbeat was found.
chainOverwriteHeartbeatAlive=שרשרת משימות {0} עדיין עובדת. הושלם: {1}, פועל: {2} נכשל: {3}, הופעל: {4}, לא הופעל: {5}.
#XMSG: Task log message for a chain that is not running anymore but not everything was triggered.
chainOverwriteNotAllTriggered=אירעה שגיאה בעת הפעלת שרשרת משימות {0}, ולא ניתן היה להפעיל את כל המשימות. הושלם: {1} פועל: {2}, נכשל: {3}, הופעל: {4}, לא הופעל: {5}. שרשרת המשימות שוכתבה.
#XMSG: Task log message for a chain that contains a failed subtask.
chainOverwriteAnySubtaskFailed=שרשרת משימות {0} הסתיימה. משימה אחת מהשרשרת נכשלה, שרשרת הוגדרה כ''נכשלה''.
#XMSG: Task log message could not determine status.
chainOverwriteNotDetermined=שגיאה בלתי צפויה מונעת גישה לסטאטוס המשימה. נסה שוב ופנה לתמיכה של SAP שלך אם השגיאה נמשכת.
#XMSG: Task log message could not take over
failedTakeover=השתלטות על המשימה הקיימת נכשלה.
#XMSG: Task log parallel check error
parallelCheckError=לא ניתן לעבד את המשימה כיוון שמשימה אחרת פועלת וחוסמת את המשימה הזו.
#XMSG: Task log parallel task runnig error
parallelTaskRunning=משימה מתנגשת כבר פועלת.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=סטאטוס {0} במהלך הפעלה עם זיהוי מתאם {1}.
#XMSG: Task log message successful takeover
successTakeover=היה צורך לשחרר נעילה שנשארה. הנעילה החדשה עבור משימה זו נקבעה.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=נעילה של משימה זו נשלטת בידי משימה אחרת.
#XMSG: Schedule created alert message
createScheduleSuccess=נוצר תזמון.
#XMSG: Task log message for taskFinishedAt
taskFinishedAt=משימה {0} הסתיימה  בשעה {2} עם סטאטוס {1}.
#XMSG: Placeholder symbol for empty columns
emptyCol=--
#XMSG: Paused Text
txtExpired=פג תוקף
#XMSG: Update task schedule success Toast message
updateScheduleSuccess=התזמון עודכן
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=התזמון נמחק.
#XMSG: Task chain might have children error text
taskChainNoChildErrorText=שרשרת משימות עשויה לכלול בנים שלא מוצגים בגלל שהמשימה נכשלה לפני יצירת התוכנית.
#XMSG: Task chain repair recent failed run label
retryRunLabel=נסה שוב הפעלה אחרונה
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=הפעלת ניסיון נוסף של שרשרת המשימות החלה
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=הפעלת ניסיון נוסף של שרשרת המשימות נכשלה
#XMSG: chain repair message
chainRetried=ניסיון נוסף של שרשרת משימות הופעל על ידי משתמש {0}
#XMSG: chain not found during run
chainNotFoundDuringRun=לא ניתן היה למצוא את שרשרת המשימות {0}. בדוק אם היא קיימת דרך בונה מודל נתונים וודא שהשרשרת פרוסה.
#XMSG: chain is not DAG
chainNotDag=לא ניתן להפעיל את שרשרת המשימות {0}. המבנה שלה לא חוקי. בדוק את שרשרת המשימות בבונה מודל נתונים.
#XMSG: chain has not valid parameters
notValidParameters=לא ניתן להפעיל את שרשרת המשימות {0}. פרמטר אחד או יותר בה אינם חוקיים. בדוק את שרשרת המשימות בבונה מודל נתונים.
#XMSG: Configuration parameter value size exceeds the maximum allowed limit
configurationSizeLimitError=לא ניתן להתחיל את שרשרת משימות {0}. הגודל של תצורת משימה בשרשרת חורג מהגודל המקסימלי המותר של 100 קילובייטים (KiB).
#XMSG: Task {0} has input parameters
taskHasInputParameters=משימה {0} מכילה פרמטרי קלט
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=משימה מתנגשת כבר פועלת.
#XMSG: error message for reading data from backend
txtReadBackendError=נראה שאירעה שגיאה במהלך קריאה מה-Back-End.
##XMSG: error message for admission control rejection
admissionControlError=המשימה נכשלה בגלל דחיית בקרת קבלה של SAP HANA.

#XBUT: Assign schedule menu button label
assignScheduleLabel=הקצה לי תזמון
#XBUT: Pause schedule menu label
pauseScheduleLabel=הפסק תזמון
#XBUT: Resume schedule menu label
resumeScheduleLabel=חדש תזמון
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=אירעה שגיאה בעת הסרת התזמונים.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=אירעה שגיאה בעת הקצאת התזמונים.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=אירעה שגיאה בעת הפסקת התזמונים.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=אירעה שגיאה בעת חידוש התזמונים.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=מוחק {0} תזמונים
#XMSG: Message for starting mass assign of schedules
massAssignStarted=שינוי הבעלים של {0} תזמונים
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=משהה {0} תזמונים
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=ממשיך {0} תזמונים
#XBUT: Select Columns Button
selectColumnsBtn=בחר עמודות
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=נראה שאירעה שגיאה בניסיון מחדש של ההפעלה האחרונה כיוון שהפעלת המשימה הקודמת נכשלה לפני יצירת התוכנית.
#XFLD: Refresh tooltip
TEXT_REFRESH=רענן
#XFLD: Select Columns tooltip
text_selectColumns=בחר עמודות

#Messages for BW Process Chain task log of the Data Integration Monitor
#XMSG: Task log message for successfully starting a BW process chain
startBwProcessChainSuccess=שרשרת תהליכי BW "{0}" התחילה בהצלחה בדייר SAP BW Bridge.
#XMSG: Task log message for skipping a BW process chain execution
bwProcessChainSkipped=שרשרת תהליכי BW "{0}" נדחתה בגלל חוסר זמינות של נתונים חדשים או כשל של ביצוע קודם.
#XMSG: Task log message for failure in starting a BW process chain
startBwProcessChainFailure=התחלת שרשרת תהליכי BW "{0}" נכשלה בדייר SAP BW Bridge.
#XMSG: Task log message for failure in getting the status of a BW process chain
getBwProcessChainStatusFailure=המשימה נכשלה מכיוון שלא הצלחנו לאחזר את הסטאטוס של שרשרת תהליכי BW "{0}" עקב בעיה ביצירת חיבור ל-SAP BW Bridge. פתח את יישום "חיבורים" ואמת את חיבור SAP BW Bridge במרחב "{1}". אם אין לך גישה ליישום "חיבורים", פנה למנהל המערכת שלך.
#XMSG: Task log message for failure in completing a BW process chain
completeBwProcessChainFailure=השלמת שרשרת תהליכי BW "{0}" נכשלה בדייר SAP BW Bridge.
#XMSG: Task log message for the clickable text of the SAP BW Bridge Monitor link
bwBridgeMonitorLinkText=הצג פרטים במעקב SAP BW Bridge
#XMSG warning message while retrieving the task log messages if the user does not have access to the SAP BW Bridge space or to the shared object's space
missingPrivilegeOnSpaceError=הייתה בעיה באחזור ההודעות, או שאין לך ההרשאה הנדרש לצפות בהן.

#Messages for API task log of the Data Integration Monitor
#XMSG: Task log message for displaying the API response
viewResponse=תגובה התקבלה עבור "{0}".
#XMSG: Task log message for empty ID in the API response to retrieve the status
emptyJobId=לא הוחזר זיהוי בגוף התגובה עבור נתיב JSON: "{0}".
#XMSG: Task log message for empty success value in the API response
emptySuccessValue=לא הוחזר ערך הצלחה בגוף התגובה עבור נתיב JSON: "{0}".
#XMSG: Task log message for empty error value in the API response
emptyErrorValue=לא הוחזר ערך שגיאה בגוף התגובה עבור נתיב JSON: "{0}".
#XMSG: Task log message for none of the success/error conditions matching for the values in the API response
conditionsNotFulfilled=אף אחד מתנאי סמן השגיאה או ההצלחה שצויינו לא תואם לערכים שבתגובה.
#XMSG: Task log message for the API response status being unsuccessful
statusNotOk=התגובה עבור "{1}" החזירה קוד סטאטוס שלא הצליח: {0}.
#XMSG: Task log message for the API response body being empty
emptyResponseBody=גוף התגובה היה ריק.
#XMSG: Task log message for the API response location header being invalid
emptyLocationHeader=כותרת המיקום בתגובה הייתה ריקה.
#XMSG: Task log message for failure in retrieving the status of the invoked API
getStatusFailure=לא ניתן לאחזר את הסטאטוס של ה-API שהופעל.
#XMSG: Task log message for failure in completing the API task
completionFailure=השלמת משימת ה-API "{0}" נכשלה.
#XMSG: Task log message for a successful API task completion
apiCompleted=משימת ה-API "{0}" הושלמה בהצלחה.
#XMSG: Task log message for an API task with an invalid configuration
invalidConfiguration=התצורה של משימת ה-API "{0}" אינה חוקית. בדוק את התצורה ונסה שוב.
#XMSG: Task log message for the API task being canceled
cancelStart=ביטול מבוקש של משימת ה-API "{0}" עם זיהוי כניסה {1}.
#XMSG: Task log message for the cancelation of the an API task which is no longer running and cannot be canceled
unableToCancel=משימת ה-API "{0}" עם זיהוי כניסה {1} כבר לא פועלת ולא ניתן לבטל אותה.
#XMSG: Task log message when starting an API task. It should carry the name of the API task.
apiRunStart=מתחיל את משימת API "{0}".
#XMSG: Task log message when the task preparation is taking too long and times out
preparationTimeout=הכנת המשימה נמשכת זמן רב מדי ונגמר הזמן הקצוב.
#XMSG: Task log message when a task failed because a cancel task was triggered
apiRunCancelled=משימת ההפעלה עם זיהוי כניסה {0} בוטלה על-ידי משימת ביטול {1}.
#XMSG: Task log message when a cancel task failed to cancel a run task
cancelFailure=משימת הביטול עם זיהוי כניסה {1} לא הצליחה לבטל את משימת ההפעלה עם זיהוי כניסה {0}.
#XMSG: Task log message when configuration of the API task is too large
apiTaskconfigurationSizeLimitError=לא ניתן להתחיל את משימת ה-API {0}. מכיוון שהגודל של תצורת המשימה שלה חורג מהגודל המקסימלי המותר של 100 קילובייטים (KiB).
#XMSG: Task log message for failure in completing the Notification task
notificationTaskCompletionFailure=השלמת משימת ההודעה "{0}" נכשלה.
#XMSG: Task log message when starting a Notification task.
notificationTaskRunStart=מתחילת את משימת ההודעה "{0}".
#XMSG: Task log message for a successful Notification task completion
notificationTaskCompletionSuccess=משימת ההודעה "{0}" הושלמה.
#XFLD: Label for frequency column
everyLabel=כל
#XFLD: Plural Recurrence text for Hour
hoursLabel=שעות
#XFLD: Plural Recurrence text for Day
daysLabel=ימים
#XFLD: Plural Recurrence text for Month
monthsLabel=חודשים
#XFLD: Plural Recurrence text for Minutes
minutesLabel=דקות
