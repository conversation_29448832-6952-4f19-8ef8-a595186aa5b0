

#XFLD: Task Chain header
headerTxt=タスクチェーン ({0})
#XFLD: Task Chain header
headerTxtNew=タスクチェーン ({0})
#XTXT: Text for Schedule label
scheduleTxt=スケジュール
#XFLD: Text for Create schedule button
createScheduleTxt=スケジュールを作成
#XFLD: Text for edit schedule
editScheduleTxt=スケジュールを編集
#XLFD: Text for delete schedule
deleteScheduleTxt=スケジュールを削除
#XTXT: text for refresh button label
refrestTxt=リフレッシュ
#XTXT: Text for Completed status
completedTxt=完了
#XTX: Text for Running status
runningTxt=実行中
#XTX: Text for failed status
failedTxt=失敗
#XTX: Text for stopped status
stoppedTxt=停止
#XTX: Text for stopping status
stoppingTxt=停止中
#XFLD: Header for Chain name
chainNameLabel=タスクチェーン名
#XFLD: Header for Chain name
chainNameLabelBus=ビジネス名
#XFLD: Header for Chain name
chainNameLabelBusNew=オブジェクト (ビジネス名)
#XFLD: Header for Chain name
chainNameLabelTech=技術名
#XFLD: Header for Chain name
chainNameLabelTechNew=オブジェクト (技術名)
#XFLD: Last Run Status label
lastRunStatuslabel=最終実行ステータス
#XFLD: Last Run Status label
lastRunStatuslabelNew=ステータス
#XFLD: Frequency Label
frequencyLabel=頻度
#XFLD: Frequency Label
frequencyLabelNew=スケジュールされた頻度
#XFLD: Duration label
durationLabel=期間
#XFLD: Duration label
durationLabelNew=前回実行の期間
#XFLD: Run Start label
runStartLabel=最終実行の開始
#XFLD: Run end label
runEndLabel=最終実行の終了
#XFLD: Next Run label
nextRunlabel=次回の実行
#XFLD: Next Run label
nextRunlabelNew=スケジュールされた次回の実行
#XTXT: Task chain logs link tooltip text
taskChainLogsTxt=タスクチェーンログの詳細
#XTXT: Task chain logs link tooltip text
taskChainLogsTxtNew=詳細
#XTXT: Scheduled text
scheduledTxt=スケジュール済み
#XTXT: Paused text
pausedTxt=一時停止
#XTXT: Execute button label
runLabel=実行
#XTXT: Execute button label
runLabelNew=実行を開始
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=タスクチェーンを実行
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=タスクチェーンの実行が開始されました。
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg={0} のタスクチェーンの実行が開始されました。
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=タスクチェーンを実行できませんでした。
#XFLD: Label for schedule owner column
txtScheduleOwner=スケジュール所有者
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=スケジュール作成者を表示
#XMSG: Task log message for start chain
startChain=タスクチェーンの実行を開始しています。
#XMSG: Task log message for load chain from repository
loadChainFromRepository=タスクチェーンがロードされて初期化されました。
#XMSG: Task log message started task
taskStarted=タスク {0} が開始されました。
#XMSG: Task log message for finished task
taskFinished=タスク {0} がステータス {1} で終了しました。
#XMSG: Task log message for chain preparation
chainLoadFromRepository=タスクチェーンをロードするとともに、そのタスクチェーンの一部である合計 {0} 個のタスクを実行する準備をしています。
#XMSG: Task log message for starting a subtask
chainStartSubtask=タスク {0} を実行するようにトリガしています。タスク ID = {1}。
#XMSG: Task log message for finishing a subtask
chainFinishSubtask=タスク {0} はステータス {1} で終了しました。
#XMSG: Task log message for indicating chain success
chainCompleted={0} 個のタスクがすべて完了しました。タスクチェーンのステータスは "完了" に設定されました。
#XMSG: Task log message for indicating chain failure
chainFailed=合計 {0} 個のタスクのうち、{1} 個のタスクを完了できず、{2} 個のタスクが失敗しました。タスクチェーンのステータスは "失敗" に設定されました。
#XMSG: Task log message for indicating chain cancelation
chainCanceled=タスクチェーン実行のキャンセルが開始されました。キャンセルタスクのログ ID は {0} です。
#XMSG: Task log message for indicating chain cancelation in the cancel task
chainCanceling=チェーン {0} をキャンセルしています。
#XMSG: Task log message for general chain runtime error
chainError=タスクチェーンを実行しているときに、予期しないエラーが発生しました。
#XMSG: Task log message for starting the overwrite check.
chainOverwriteStart=ID {0} のタスクチェーンが終了したかどうかをチェックしています。
#XMSG: Task log message indicating the chain is too old.
chainOverwriteTooOld=チェーン {0} で実行するタスクが見つかりませんでした。実行は {1} 分前に終了しました。タスクチェーンのステータスは "失敗" に設定されました。
#XMSG: Task log message indicating the chain was created too recently.
chainOverwriteTooRecent=チェーン {0} で実行するタスクが見つかりませんでした。実行は {1} 分前に終了しました。タスクチェーンはまだ実行されています。
#XMSG: Task log message indicating a recent heartbeat was found.
chainOverwriteHeartbeatAlive=タスクチェーン {0} はまだ実行されています。完了: {1}、実行中: {2}、失敗: {3}、トリガ済: {4}、トリガ未了: {5}。
#XMSG: Task log message for a chain that is not running anymore but not everything was triggered.
chainOverwriteNotAllTriggered=タスクチェーン {0} を実行しているときにエラーが発生したため、一部のタスクをトリガできませんでした。完了: {1}、実行中: {2}、失敗: {3}、トリガ済: {4}、トリガ未了: {5}。このタスクチェーンは上書きされました。
#XMSG: Task log message for a chain that contains a failed subtask.
chainOverwriteAnySubtaskFailed=タスクチェーン {0} が終了しました。タスクチェーン内の 1 つのタスクが失敗したため、タスクチェーンのステータスは "失敗" に設定されました。
#XMSG: Task log message could not determine status.
chainOverwriteNotDetermined=予期しないエラーが発生したため、タスクのステータスにアクセスできません。もう一度実行してください。エラーが再発する場合は、SAP サポートに連絡してください。
#XMSG: Task log message could not take over
failedTakeover=既存のタスクを引き継ぎできませんでした。
#XMSG: Task log parallel check error
parallelCheckError=別のタスクが実行されており、すでにこのタスクをロックしているため、このタスクは処理できません。
#XMSG: Task log parallel task runnig error
parallelTaskRunning=競合タスクがすでに実行されています。
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=相関 ID {1} で実行中のステータス {0}。
#XMSG: Task log message successful takeover
successTakeover=ロックを解除する必要がありました。このタスクに対する新しいロックが設定されています。
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=このタスクのロックは、別のタスクによって引き継がれました。
#XMSG: Schedule created alert message
createScheduleSuccess=スケジュールが作成されました
#XMSG: Task log message for taskFinishedAt
taskFinishedAt=タスク {0} は {2} にステータス {1} で終了しました。
#XMSG: Placeholder symbol for empty columns
emptyCol=--
#XMSG: Paused Text
txtExpired=期限切れ
#XMSG: Update task schedule success Toast message
updateScheduleSuccess=スケジュールが更新されました
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=スケジュールが削除されました。
#XMSG: Task chain might have children error text
taskChainNoChildErrorText=計画を生成する前にタスクが失敗したため、表示されていない下位タスクがタスクチェーンに含まれている可能性があります。
#XMSG: Task chain repair recent failed run label
retryRunLabel=最新実行を再試行
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=タスクチェーンの再試行実行が開始されました。
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=タスクチェーンの再試行実行が失敗しました。
#XMSG: chain repair message
chainRetried=タスクチェーンの再試行がユーザ {0} によってトリガされました
#XMSG: chain not found during run
chainNotFoundDuringRun=タスクチェーン {0} が見つかりませんでした。データビルダを介してタスクチェーンが存在しているかどうかを確認し、タスクチェーンがデプロイされていることを確認してください。
#XMSG: chain is not DAG
chainNotDag=タスクチェーン {0} を開始できません。構造が無効です。データビルダでタスクチェーンを確認してください。
#XMSG: chain has not valid parameters
notValidParameters=タスクチェーン {0} を開始できません。1 つ以上のパラメータが無効です。データビルダでタスクチェーンを確認してください。
#XMSG: Configuration parameter value size exceeds the maximum allowed limit
configurationSizeLimitError=タスクチェーン {0} を開始できません。チェーンでのタスクの設定のサイズが、最大許容サイズ 100 キロバイト (KiB) を超えています。
#XMSG: Task {0} has input parameters
taskHasInputParameters=タスク {0} に入力パラメータがあります。
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=競合タスクがすでに実行されています
#XMSG: error message for reading data from backend
txtReadBackendError=バックエンドからの読み込み中にエラーが発生したようです。
##XMSG: error message for admission control rejection
admissionControlError=SAP HANA の受付制御の却下により、タスクが失敗しました。

#XBUT: Assign schedule menu button label
assignScheduleLabel=スケジュールを自分に割り当て
#XBUT: Pause schedule menu label
pauseScheduleLabel=スケジュールを一時停止
#XBUT: Resume schedule menu label
resumeScheduleLabel=スケジュールを再開
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=スケジュールの削除中にエラーが発生しました。
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=スケジュールの割り当て中にエラーが発生しました。
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=スケジュールの一時停止中にエラーが発生しました。
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=スケジュールの再開中にエラーが発生しました。
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted={0} スケジュールを削除しています
#XMSG: Message for starting mass assign of schedules
massAssignStarted={0} スケジュールの所有者を変更しています
#XMSG: Message for starting mass pausing of schedules
massPauseStarted={0} スケジュールを一時停止しています
#XMSG: Message for starting mass resuming of schedules
massResumeStarted={0} スケジュールを再開しています
#XBUT: Select Columns Button
selectColumnsBtn=列を選択
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=計画の生成前に先行タスクの実行に失敗したため、最新実行の再試行でエラーが発生したようです。
#XFLD: Refresh tooltip
TEXT_REFRESH=リフレッシュ
#XFLD: Select Columns tooltip
text_selectColumns=列を選択

#Messages for BW Process Chain task log of the Data Integration Monitor
#XMSG: Task log message for successfully starting a BW process chain
startBwProcessChainSuccess=SAP BW ブリッジテナントで BW プロセスチェーン "{0}" が正常に開始されました。
#XMSG: Task log message for skipping a BW process chain execution
bwProcessChainSkipped=新しいデータを利用できないか、以前の実行の失敗により、BW プロセスチェーン "{0}" がスキップされました。
#XMSG: Task log message for failure in starting a BW process chain
startBwProcessChainFailure=SAP BW ブリッジテナントで BW プロセスチェーン "{0}" を開始できませんでした。
#XMSG: Task log message for failure in getting the status of a BW process chain
getBwProcessChainStatusFailure=SAP BW ブリッジへの接続確立時の問題が原因で BW プロセスチェーン "{0}" のステータスを取得できなかったため、タスクが失敗しました。"接続" アプリを開き、"{1}" スペースで SAP BW ブリッジへの接続をチェックしてください。"接続" アプリにアクセスできない場合は、管理者に連絡してください。
#XMSG: Task log message for failure in completing a BW process chain
completeBwProcessChainFailure=SAP BW ブリッジテナントで BW プロセスチェーン "{0}" を完了できませんでした。
#XMSG: Task log message for the clickable text of the SAP BW Bridge Monitor link
bwBridgeMonitorLinkText=View details in SAP BW ブリッジモニタで詳細を表示
#XMSG warning message while retrieving the task log messages if the user does not have access to the SAP BW Bridge space or to the shared object's space
missingPrivilegeOnSpaceError=メッセージの取得中に問題が発生したか、メッセージを表示するために必要な権限がありません。

#Messages for API task log of the Data Integration Monitor
#XMSG: Task log message for displaying the API response
viewResponse="{0}" の回答を受信しました。
#XMSG: Task log message for empty ID in the API response to retrieve the status
emptyJobId= JSON パスの応答本文で ID が返されていません: "{0}"。
#XMSG: Task log message for empty success value in the API response
emptySuccessValue= JSON パスの応答本文で成功値が返されていません: "{0}"。
#XMSG: Task log message for empty error value in the API response
emptyErrorValue= JSON パスの応答本文でエラー値が返されていません: "{0}"。
#XMSG: Task log message for none of the success/error conditions matching for the values in the API response
conditionsNotFulfilled=指定された成功区分条件またはエラー区分条件のいずれも、応答内の値に一致しません。
#XMSG: Task log message for the API response status being unsuccessful
statusNotOk="{1}" に対する応答から失敗ステータスコードが返されました: {0}。
#XMSG: Task log message for the API response body being empty
emptyResponseBody=応答本文が空でした。
#XMSG: Task log message for the API response location header being invalid
emptyLocationHeader=応答内のロケーションヘッダが空でした。
#XMSG: Task log message for failure in retrieving the status of the invoked API
getStatusFailure=呼び出された API のステータスを取得できませんでした。
#XMSG: Task log message for failure in completing the API task
completionFailure=API タスク "{0}" を完了できませんでした。
#XMSG: Task log message for a successful API task completion
apiCompleted=API タスク "{0}" が正常に完了しました。
#XMSG: Task log message for an API task with an invalid configuration
invalidConfiguration=API タスク "{0}" は無効です。設定を確認し、もう一度実行してください。
#XMSG: Task log message for the API task being canceled
cancelStart=logId {1} の API タスク "{0}" のキャンセルが要求されました。
#XMSG: Task log message for the cancelation of the an API task which is no longer running and cannot be canceled
unableToCancel=logId {1} の API タスク "{0}" は実行されていないため、キャンセルできません。
#XMSG: Task log message when starting an API task. It should carry the name of the API task.
apiRunStart=API タスク "{0}" を開始しています。
#XMSG: Task log message when the task preparation is taking too long and times out
preparationTimeout=タスクの準備に時間がかかっているため、タイムアウトしました。
#XMSG: Task log message when a task failed because a cancel task was triggered
apiRunCancelled=logId {0} の実行タスクがキャンセルタスク {1} によってキャンセルされました。
#XMSG: Task log message when a cancel task failed to cancel a run task
cancelFailure=logId {1} のキャンセルタスクが logId {0} の実行タスクのキャンセルに失敗しました。
#XMSG: Task log message when configuration of the API task is too large
apiTaskconfigurationSizeLimitError=タスクの設定のサイズが、最大許容サイズ 100 キロバイト (KiB) を超えているため、API タスク {0} を開始できません。
#XMSG: Task log message for failure in completing the Notification task
notificationTaskCompletionFailure=通知タスク "{0}" を完了できませんでした。
#XMSG: Task log message when starting a Notification task.
notificationTaskRunStart=通知タスク "{0}" を開始しています。
#XMSG: Task log message for a successful Notification task completion
notificationTaskCompletionSuccess=通知タスク "{0}" が完了しました。
#XFLD: Label for frequency column
everyLabel=間隔
#XFLD: Plural Recurrence text for Hour
hoursLabel=時間
#XFLD: Plural Recurrence text for Day
daysLabel=日
#XFLD: Plural Recurrence text for Month
monthsLabel=月
#XFLD: Plural Recurrence text for Minutes
minutesLabel=分
