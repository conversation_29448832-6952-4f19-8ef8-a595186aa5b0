

#XFLD: Task Chain header
headerTxt=Chuỗi tác vụ({0})
#XFLD: Task Chain header
headerTxtNew=Chuỗi tác vụ({0})
#XTXT: Text for Schedule label
scheduleTxt=Lập lịch
#XFLD: Text for Create schedule button
createScheduleTxt=Tạo lịch
#XFLD: Text for edit schedule
editScheduleTxt=Hiệu chỉnh lịch
#XLFD: Text for delete schedule
deleteScheduleTxt=Xóa lịch
#XTXT: text for refresh button label
refrestTxt=Làm mới
#XTXT: Text for Completed status
completedTxt=Đã hoàn tất
#XTX: Text for Running status
runningTxt=Chạy
#XTX: Text for failed status
failedTxt=Bị lỗi
#XTX: Text for stopped status
stoppedTxt=Đã dừng
#XTX: Text for stopping status
stoppingTxt=Đang dừng
#XFLD: Header for Chain name
chainNameLabel=Tên chuỗi tác vụ
#XFLD: Header for Chain name
chainNameLabelBus=Tên doanh nghiệp
#XFLD: Header for Chain name
chainNameLabelBusNew=Đối tượng (Tên doanh nghiệp)
#XFLD: Header for Chain name
chainNameLabelTech=Tên kỹ thuật
#XFLD: Header for Chain name
chainNameLabelTechNew=Đối tượng (Tên kỹ thuật)
#XFLD: Last Run Status label
lastRunStatuslabel=Trạng trạng chạy cuối cùng
#XFLD: Last Run Status label
lastRunStatuslabelNew=Trạng thái
#XFLD: Frequency Label
frequencyLabel=Tần suất
#XFLD: Frequency Label
frequencyLabelNew=Tần suất được lên lịch
#XFLD: Duration label
durationLabel=Khoảng thời gian
#XFLD: Duration label
durationLabelNew=Thời lượng lần chạy cuối
#XFLD: Run Start label
runStartLabel=Khởi động lần thực hiện cuối cùng
#XFLD: Run end label
runEndLabel=Kết thúc lần thực hiện cuối cùng
#XFLD: Next Run label
nextRunlabel=Lần thực hiện tiếp theo
#XFLD: Next Run label
nextRunlabelNew=Lần thực hiện tiếp theo được lên lịch
#XTXT: Task chain logs link tooltip text
taskChainLogsTxt=Chi tiết nhật ký chuỗi tác vụ
#XTXT: Task chain logs link tooltip text
taskChainLogsTxtNew=Chi tiết
#XTXT: Scheduled text
scheduledTxt=Được lập lịch
#XTXT: Paused text
pausedTxt=Đã tạm dừng
#XTXT: Execute button label
runLabel=Chạy
#XTXT: Execute button label
runLabelNew=Bắt đầu chạy
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Chạy chuỗi tác vụ
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Đã bắt đầu quá trình chạy chuỗi tác vụ.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Đã bắt đầu chạy chuỗi tác vụ cho {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Quá trình chạy chuỗi tác vụ không thành công.
#XFLD: Label for schedule owner column
txtScheduleOwner=Chủ sở hữu lịch
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Hiển thị ai đã tạo lịch
#XMSG: Task log message for start chain
startChain=Bắt đầu chạy chuỗi tác vụ.
#XMSG: Task log message for load chain from repository
loadChainFromRepository=Đã tải và khởi chạy chạy chuỗi tác vụ.
#XMSG: Task log message started task
taskStarted=Đã bắt đầu {0} tác vụ.
#XMSG: Task log message for finished task
taskFinished=Tác vụ {0} đã kết thúc với trạng thái {1}.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Đang tải chuỗi tác vụ và chuẩn bị chạy tổng cộng {0} tác vụ là một phần của chuỗi này.
#XMSG: Task log message for starting a subtask
chainStartSubtask=Đang kích hoạt tác vụ {0} để chạy. ID tác vụ = {1}.
#XMSG: Task log message for finishing a subtask
chainFinishSubtask=Tác vụ {0} đã hoàn tất với trạng thái {1}.
#XMSG: Task log message for indicating chain success
chainCompleted=Tất cả các tác vụ {0} đã hoàn tất. Trạng thái chuỗi tác vụ được đặt thành đã hoàn tất.
#XMSG: Task log message for indicating chain failure
chainFailed=Trong tổng {0} tác vụ, {1} tác vụ có thể hoàn tấ̃t và {2} tác vụ không thành công. Trạng thái chuỗi tác vụ được đặt thành không thành công.
#XMSG: Task log message for indicating chain cancelation
chainCanceled=Hủy thực hiện chuỗi tác vụ đã bắt đầu. Id nhật ký của hủy tác vụ là {0}.
#XMSG: Task log message for indicating chain cancelation in the cancel task
chainCanceling=Hủy chuỗi {0}.
#XMSG: Task log message for general chain runtime error
chainError=Đã xảy ra lỗi không mong muốn khi chạy chuỗi tác vụ.
#XMSG: Task log message for starting the overwrite check.
chainOverwriteStart=Kiểm tra xem chuỗi nhiệm vụ có ID {0} đã hoàn tất hay chưa.
#XMSG: Task log message indicating the chain is too old.
chainOverwriteTooOld=Không tìm thấy tác vụ để chạy chuỗi {0}. Quá trình chạy kéo dài trong {1} phút. Trạng thái chuỗi tác vụ được đặt thành không thành công.
#XMSG: Task log message indicating the chain was created too recently.
chainOverwriteTooRecent=Không tìm thấy tác vụ để chạy chuỗi {0}. Quá trình chạy kéo dài trong {1} phút. Trạng thái chuỗi tác vụ được đặt thành vẫn đang chạy.
#XMSG: Task log message indicating a recent heartbeat was found.
chainOverwriteHeartbeatAlive=Chuỗi tác vụ {0} vẫn đang chạy. Đã hoàn tất: {1}, Đang chạy: {2}, Không thành công: {3}, Đã kích hoạt: {4}, Chưa kích hoạt: {5}.
#XMSG: Task log message for a chain that is not running anymore but not everything was triggered.
chainOverwriteNotAllTriggered=Đã xảy ra lỗi khi chạy chuỗi tác vụ {0} và không phải tác vụ nào cũng có thể được kích hoạt. Đã hoàn tất: {1}, Đang chạy: {2}, Không thành công: {3}, Đã kích hoạt: {4}, Chưa kích hoạt: {5}. Chuỗi tác vụ này đã bị ghi đè.
#XMSG: Task log message for a chain that contains a failed subtask.
chainOverwriteAnySubtaskFailed=Đã hoàn tất chuỗi tác vụ {0}. Một tác vụ của chuỗi không thành công, thiết lập chuỗi không thành công.
#XMSG: Task log message could not determine status.
chainOverwriteNotDetermined=Một lỗi không mong muốn ngăn chúng tôi truy cập trạng thái của tác vụ. Hãy thử lại và liên hệ với bộ phận Hỗ trợ SAP của bạn nếu lỗi vẫn tiếp diễn.
#XMSG: Task log message could not take over
failedTakeover=Không thể tiếp tục tác vụ hiện có.
#XMSG: Task log parallel check error
parallelCheckError=Không thể xử lý tác vụ vì tác vụ khác đang chạy và đã chặn tác vụ này.
#XMSG: Task log parallel task runnig error
parallelTaskRunning=Công việc xung đột đã chạy.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Trạng thái {0} trong quá trình thực hiện với ID tương quan {1}.
#XMSG: Task log message successful takeover
successTakeover=Khóa còn lại phải được mở. Khóa mới cho tác vụ này được thiết lập.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Khóa tác vụ này được tiếp quản bởi tác vụ khác.
#XMSG: Schedule created alert message
createScheduleSuccess=Đã tạo lịch
#XMSG: Task log message for taskFinishedAt
taskFinishedAt=Tác vụ {0} đã hoàn tất lúc {2} với trạng thái {1}.
#XMSG: Placeholder symbol for empty columns
emptyCol=--
#XMSG: Paused Text
txtExpired=Đã hết hạn
#XMSG: Update task schedule success Toast message
updateScheduleSuccess=Đã cập nhật lịch
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Đã xóa lịch.
#XMSG: Task chain might have children error text
taskChainNoChildErrorText=Chuỗi tác vụ có thể có các phần tử con không được hiển thị vì tác vụ không thành công trước khi có thể tạo kế hoạch.
#XMSG: Task chain repair recent failed run label
retryRunLabel=Lần chạy thử lại gần nhất
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Lần chạy thử lại chuỗi tác vụ đã bắt đầu
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Lần chạy thử lại chuỗi tác vụ không thành công
#XMSG: chain repair message
chainRetried=Thử lại chuỗi tác vụ được kích hoạt bởi người dùng {0}
#XMSG: chain not found during run
chainNotFoundDuringRun=Không thể tìm thấy chuỗi tác vụ {0}. Kiểm tra xem chuỗi tác vụ này có tồn tại thông qua Trình tạo dữ liệu hay không và đảm bảo chuỗi được triển khai.
#XMSG: chain is not DAG
chainNotDag=Không thể bắt đầu chuỗi tác vụ {0}. Cấu trúc của nó không hợp lệ. Kiểm tra chuỗi tác vụ trong Trình tạo dữ liệu.
#XMSG: chain has not valid parameters
notValidParameters=Không thể bắt đầu chuỗi tác vụ {0}. Một hoặc nhiều tham số của nó không hợp lệ. Kiểm tra chuỗi tác vụ trong Trình tạo dữ liệu.
#XMSG: Configuration parameter value size exceeds the maximum allowed limit
configurationSizeLimitError=Không thể bắt đầu chuỗi tác vụ {0}. Kích thước cấu hình của tác vụ trong chuỗi vượt quá kích thước tối đa được phép là 100 kibibyte (KiB).
#XMSG: Task {0} has input parameters
taskHasInputParameters=Tác vụ {0} có tham số đầu vào.
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Tác vụ xung đột đang chạy
#XMSG: error message for reading data from backend
txtReadBackendError=Có vẻ như đã có lỗi khi đọc từ chương trình phụ trợ.
##XMSG: error message for admission control rejection
admissionControlError=Tác vụ không thành công do Từ chối kiểm soát thu nạp SAP HANA.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Gán lịch cho tôi
#XBUT: Pause schedule menu label
pauseScheduleLabel=Tạm dừng lịch
#XBUT: Resume schedule menu label
resumeScheduleLabel=Tiếp tục lịch
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Đã xảy ra lỗi khi loại bỏ lịch.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Đã xảy ra lỗi khi gán lịch.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Đã xảy ra lỗi khi tạm dừng bỏ lịch.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Đã xảy ra lỗi khi tiếp tục lịch.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Xóa {0} lịch
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Thay đổi chủ sở hữu {0} lịch
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Tạm dừng {0} lịch
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Tiếp tục {0} lịch
#XBUT: Select Columns Button
selectColumnsBtn=Lựa chọn cột
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Có vẻ như đã có lỗi trong lần thử chạy gần đây nhất do tác vụ trước đó không thành công trước khi có thể tạo kế hoạch.
#XFLD: Refresh tooltip
TEXT_REFRESH=Làm mới
#XFLD: Select Columns tooltip
text_selectColumns=Lựa chọn cột

#Messages for BW Process Chain task log of the Data Integration Monitor
#XMSG: Task log message for successfully starting a BW process chain
startBwProcessChainSuccess=Chuỗi quy trình BW "{0}" đã bắt đầu thành công trong đối tượng thuê SAP BW Bridge.
#XMSG: Task log message for skipping a BW process chain execution
bwProcessChainSkipped=Chuỗi quy trình BW "{0}" đã bị bỏ qua do không có sẵn dữ liệu mới hoặc do lỗi thực hiện trước đó.
#XMSG: Task log message for failure in starting a BW process chain
startBwProcessChainFailure=Chuỗi quy trình BW "{0}" đã bắt đầu không thành công trong đối tượng thuê SAP BW Bridge.
#XMSG: Task log message for failure in getting the status of a BW process chain
getBwProcessChainStatusFailure=Tác vụ không thành công vì chúng tôi không thể truy xuất trạng thái của chuỗi quy trình BW "{0}" do sự cố khi thiết lập kết nối với SAP BW Bridge. Mở ứng dụng "Kết nối" và xác thực kết nối SAP BW Bridge trong vùng dữ liệu "{1}". Nếu bạn không có quyền truy cập vào ứng dụng "Kết nối", vui lòng liên hệ người quản trị của bạn.
#XMSG: Task log message for failure in completing a BW process chain
completeBwProcessChainFailure=Chuỗi quy trình BW "{0}" đã hoàn tất không thành công trong đối tượng thuê SAP BW Bridge.
#XMSG: Task log message for the clickable text of the SAP BW Bridge Monitor link
bwBridgeMonitorLinkText=Xem chi tiết trong màn hình SAP BW Bridge
#XMSG warning message while retrieving the task log messages if the user does not have access to the SAP BW Bridge space or to the shared object's space
missingPrivilegeOnSpaceError=Đã xảy ra sự cố khi truy xuất thông báo hoặc bạn không có quyền cần có để xem thông báo.

#Messages for API task log of the Data Integration Monitor
#XMSG: Task log message for displaying the API response
viewResponse=Đã nhận được phản hồi cho "{0}".
#XMSG: Task log message for empty ID in the API response to retrieve the status
emptyJobId=Không có ID nào đã được trả về trong phần thân của phản hồi cho đường dẫn JSON: "{0}".
#XMSG: Task log message for empty success value in the API response
emptySuccessValue=Không có giá trị thành công nào đã được trả về trong phần thân của phản hồi cho đường dẫn JSON: "{0}".
#XMSG: Task log message for empty error value in the API response
emptyErrorValue=Không có giá trị lỗi nào đã được trả về trong phần thân của phản hồi cho đường dẫn JSON: "{0}".
#XMSG: Task log message for none of the success/error conditions matching for the values in the API response
conditionsNotFulfilled=Không có điều kiện chỉ báo thành công hoặc lỗi được định rõ nào khớp với các giá trị trong phản hồi.
#XMSG: Task log message for the API response status being unsuccessful
statusNotOk=Phản hồi cho "{1}" trả về mã trạng thái không thành công: {0}.
#XMSG: Task log message for the API response body being empty
emptyResponseBody=Phần thân của phản hồi để trống.
#XMSG: Task log message for the API response location header being invalid
emptyLocationHeader=Tiêu đề vị trí trong phản hồi để trống.
#XMSG: Task log message for failure in retrieving the status of the invoked API
getStatusFailure=Không thể truy xuất trạng thái của API được gọi.
#XMSG: Task log message for failure in completing the API task
completionFailure=Tác vụ API "{0}" không hoàn tất được.
#XMSG: Task log message for a successful API task completion
apiCompleted=Tác vụ API "{0}" đã hoàn tất thành công.
#XMSG: Task log message for an API task with an invalid configuration
invalidConfiguration=Cấu hình của tác vụ API "{0}" không hợp lệ. Vui lòng kiểm tra cấu hình và thử lại.
#XMSG: Task log message for the API task being canceled
cancelStart=Hủy được yêu cầu của tác vụ API "{0}" với logId {1}.
#XMSG: Task log message for the cancelation of the an API task which is no longer running and cannot be canceled
unableToCancel=Tác vụ API "{0}" có logId {1} không còn chạy nữa và không thể hủy được.
#XMSG: Task log message when starting an API task. It should carry the name of the API task.
apiRunStart=Đang bắt đầu tác vụ API "{0}".
#XMSG: Task log message when the task preparation is taking too long and times out
preparationTimeout=Việc chuẩn bị tác vụ mất quá nhiều thời gian và đã hết thời gian.
#XMSG: Task log message when a task failed because a cancel task was triggered
apiRunCancelled=Tác vụ chạy với logId {0} đã bị hủy bởi tác vụ hủy {1}.
#XMSG: Task log message when a cancel task failed to cancel a run task
cancelFailure=Tác vụ hủy với logId {1} không thể hủy tác vụ chạy với logId. {0}
#XMSG: Task log message when configuration of the API task is too large
apiTaskconfigurationSizeLimitError=Tác vụ API {0} không thể khởi động vì kích thước cấu hình tác vụ vượt quá kích thước tối đa cho phép là 100 kibibyte (KiB).
#XMSG: Task log message for failure in completing the Notification task
notificationTaskCompletionFailure=Tác vụ thông báo "{0}" không hoàn tất được.
#XMSG: Task log message when starting a Notification task.
notificationTaskRunStart=Bắt đầu tác vụ thông báo "{0}".
#XMSG: Task log message for a successful Notification task completion
notificationTaskCompletionSuccess=Tác vụ thông báo "{0}" đã hoàn tất.
#XFLD: Label for frequency column
everyLabel=Mỗi
#XFLD: Plural Recurrence text for Hour
hoursLabel=Giờ
#XFLD: Plural Recurrence text for Day
daysLabel=Ngày
#XFLD: Plural Recurrence text for Month
monthsLabel=Tháng
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Phút
