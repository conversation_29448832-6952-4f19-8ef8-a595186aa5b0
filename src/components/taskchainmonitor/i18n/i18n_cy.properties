

#XFLD: Task Chain header
headerTxt=Cadwyni Tasgau({0})
#XFLD: Task Chain header
headerTxtNew=Cadwyni Tasgau ({0})
#XTXT: Text for Schedule label
scheduleTxt=Amserlennu
#XFLD: Text for Create schedule button
createScheduleTxt=Creu Amserlen
#XFLD: Text for edit schedule
editScheduleTxt=Golygu Amserlen
#XLFD: Text for delete schedule
deleteScheduleTxt=Dileu Amserlen
#XTXT: text for refresh button label
refrestTxt=Adnewyddu
#XTXT: Text for Completed status
completedTxt=Wedi Cwblhau
#XTX: Text for Running status
runningTxt=Yn Rhedeg
#XTX: Text for failed status
failedTxt=Wedi Methu
#XTX: Text for stopped status
stoppedTxt=Wedi'i Stopio
#XTX: Text for stopping status
stoppingTxt=Wrthi'n Stopio
#XFLD: Header for Chain name
chainNameLabel=Enw'r Gadwyn Dasgau
#XFLD: Header for Chain name
chainNameLabelBus=Enw Busnes
#XFLD: Header for Chain name
chainNameLabelBusNew=Gwrthrych (Enw'r Busnes)
#XFLD: Header for Chain name
chainNameLabelTech=Enw Technegol
#XFLD: Header for Chain name
chainNameLabelTechNew=Gwrthrych (Enw Technegol)
#XFLD: Last Run Status label
lastRunStatuslabel=Statws Rhedeg Diwethaf
#XFLD: Last Run Status label
lastRunStatuslabelNew=Statws
#XFLD: Frequency Label
frequencyLabel=Amlder
#XFLD: Frequency Label
frequencyLabelNew=Amlder a Drefnwyd
#XFLD: Duration label
durationLabel=Hyd
#XFLD: Duration label
durationLabelNew=Hyd y Rhediad Diwethaf
#XFLD: Run Start label
runStartLabel=Dechrau Rhediad Diwethaf
#XFLD: Run end label
runEndLabel=Gorffen Rhediad Diwethaf
#XFLD: Next Run label
nextRunlabel=Rhediad Nesaf
#XFLD: Next Run label
nextRunlabelNew=Rhediad Nesaf sydd wedi’i Drefnu
#XTXT: Task chain logs link tooltip text
taskChainLogsTxt=Manylion Log y Gadwyn Dasgau
#XTXT: Task chain logs link tooltip text
taskChainLogsTxtNew=Manylion
#XTXT: Scheduled text
scheduledTxt=Wedi'i Amserlennu
#XTXT: Paused text
pausedTxt=Wedi'i Rewi
#XTXT: Execute button label
runLabel=Rhedeg
#XTXT: Execute button label
runLabelNew=Dechrau Rhedeg
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Rhedeg y gadwyn tasgau
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Rhedeg y gadwyn tasgau wedi dechrau.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Rhedeg y gadwyn tasgau wedi dechrau ar gyfer {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Wedi methu rhedeg y gadwyn tasgau.
#XFLD: Label for schedule owner column
txtScheduleOwner=Perchennog Amserlen
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Dangos pwy sydd wedi creu'r amserlen
#XMSG: Task log message for start chain
startChain=Dechrau'r broses rhedeg y gadwyn dasgau.
#XMSG: Task log message for load chain from repository
loadChainFromRepository=Cadwyn dasgau wedi'i llwytho a'i chychwyn.
#XMSG: Task log message started task
taskStarted=Mae tasg {0} wedi dechrau.
#XMSG: Task log message for finished task
taskFinished=Daeth y dasg {0} i ben gyda statws {1}.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Wrthi''n llwytho cadwyn tasgau a pharatoi i redeg {0} o dasgau sy''n rhan o''r gadwyn hon.
#XMSG: Task log message for starting a subtask
chainStartSubtask=Sbarduno tasg {0} i redeg. ID tasg = {1}.
#XMSG: Task log message for finishing a subtask
chainFinishSubtask=Tasg {0} wedi gorffen gyda statws {1}.
#XMSG: Task log message for indicating chain success
chainCompleted=Mae pob tasg {0} wedi''u cwblhau. Mae statws y gadwyn tasgau wedi''i osod ar wedi cwblhau.
#XMSG: Task log message for indicating chain failure
chainFailed=Ar gyfanswm o {0} tasg, roedd modd cwblhau {1} tasg ac mae {2} tasg wedi methu. Mae statws y gadwyn tasgau wedi''i osod ar wedi methu.
#XMSG: Task log message for indicating chain cancelation
chainCanceled=Proses ganslo rhedeg y gadwyn tasgau wedi dechrau. Id log y dasg ganslo yw {0}.
#XMSG: Task log message for indicating chain cancelation in the cancel task
chainCanceling=Wrthi''n canslo''r gadwyn {0}.
#XMSG: Task log message for general chain runtime error
chainError=Gwall annisgwyl wrth redeg y gadwyn tasgau.
#XMSG: Task log message for starting the overwrite check.
chainOverwriteStart=Gwirio i weld a yw''r gadwyn tasgau gydag id {0} wedi gorffen.
#XMSG: Task log message indicating the chain is too old.
chainOverwriteTooOld=Doedd dim modd dod o hyd i dasgau i''w rhedeg ar gyfer cadwyn{0}. Mae''r broses rhedeg yn {1} munud. Mae statws y gadwyn tasgau wedi''i osod ar wedi methu.
#XMSG: Task log message indicating the chain was created too recently.
chainOverwriteTooRecent=Doedd dim modd dod o hyd i dasgau i''w rhedeg ar gyfer cadwyn{0}. Mae''r broses rhedeg yn {1} munud. Mae''r gadwyn tasgau yn dal yn rhedeg.
#XMSG: Task log message indicating a recent heartbeat was found.
chainOverwriteHeartbeatAlive=Mae''r gadwyn tasgau {0} yn dal yn rhedeg. Wedi cwblhau: {1}, Rhedeg: {2}, Wedi methu: {3}, Wedi sbarduno: {4}, Heb ei sbarduno: {5}.
#XMSG: Task log message for a chain that is not running anymore but not everything was triggered.
chainOverwriteNotAllTriggered=Gwall wrth redeg gadwyn tasgau {0}, a doedd dim modd sbarduno pob tasg. Wedi cwblhau: {1}, Rhedeg: {2}, Wedi methu: {3}, Wedi sbarduno: {4}, Heb ei sbarduno: {5}. Mae''r gadwyn tasgau yma wedi cael ei throsysgrifo.
#XMSG: Task log message for a chain that contains a failed subtask.
chainOverwriteAnySubtaskFailed=Mae cadwyn tasgau {0} wedi gorffen. Mae un tasg yn y gadwyn wedi methu, gan osod y gadwyn tasgau ar wedi methu.
#XMSG: Task log message could not determine status.
chainOverwriteNotDetermined=Mae gwall annisgwyl yn ein hatal rhag cyrchu statws y dasg. Ceisiwch eto a chysylltwch â'ch Cefnogaeth SAP os bydd y gwall yn parhau.
#XMSG: Task log message could not take over
failedTakeover=Wedi methu cymryd drosodd tasg sy’n bodoli’n barod.
#XMSG: Task log parallel check error
parallelCheckError=Does dim modd prosesu’r dasg gan fod tasg arall yn rhedeg ac yn rhwystro’r dasg hon yn barod.
#XMSG: Task log parallel task runnig error
parallelTaskRunning=Mae tasg sy’n gwrthdaro eisoes yn rhedeg.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Statws {0} yn ystod proses rhedeg gyda ID Cydberthyniad {1}.
#XMSG: Task log message successful takeover
successTakeover=Roedd yn rhaid rhyddhau'r clo dros ben. Mae'r clo newydd ar gyfer y dasg yma wedi'i osod.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Tasg arall wedi cymryd drosodd clo y dasg yma.
#XMSG: Schedule created alert message
createScheduleSuccess=Amserlen wedi'i chreu
#XMSG: Task log message for taskFinishedAt
taskFinishedAt=Gorffennodd tasg {0} am {2} gyda statws {1}.
#XMSG: Placeholder symbol for empty columns
emptyCol=--
#XMSG: Paused Text
txtExpired=Wedi Dod i Ben
#XMSG: Update task schedule success Toast message
updateScheduleSuccess=Amserlen wedi'i diweddaru
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Amserlen wedi'i dileu.
#XMSG: Task chain might have children error text
taskChainNoChildErrorText=Efallai fod gan y gadwyn dasgau blant sydd ddim yn cael eu dangos oherwydd bod y dasg wedi methu cyn gallu cynhyrchu cynllun.
#XMSG: Task chain repair recent failed run label
retryRunLabel=Rhoi Ailgynnig ar y Rhediad Diweddaraf
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Rhedeg ailgynnig y gadwyn tasgau wedi dechrau
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Rhedeg ailgynnig y gadwyn tasgau wedi methu
#XMSG: chain repair message
chainRetried=Ailgynnig y gadwyn tasgau wedi''i sbarduno gan {0}
#XMSG: chain not found during run
chainNotFoundDuringRun=Methu dod o hyd i gadwyn tasgau {0}. Gwiriwch i weld a yw''n bodoli drwy''r Lluniwr Data a sicrhau bod y gadwyn wedi''i gosod.
#XMSG: chain is not DAG
chainNotDag=Methu cychwyn cadwyn tasgau {0}. Mae ei strwythur yn annilys. Gwiriwch fod y gadwyn tasgau yn y Lluniwr Data.
#XMSG: chain has not valid parameters
notValidParameters=Methu cychwyn cadwyn tasgau {0}. Mae un neu fwy o''i baramedrau yn annilys. Gwiriwch fod y gadwyn tasgau yn y Lluniwr Data.
#XMSG: Configuration parameter value size exceeds the maximum allowed limit
configurationSizeLimitError=Methu cychwyn cadwyn tasgau {0}. Mae maint ffurfweddiad y dasg yn y gadwyn yn fwy na''r maint mwyaf a ganiateir, sef 100 cibibeit (KiB).
#XMSG: Task {0} has input parameters
taskHasInputParameters=Mae gan y dasg {0} paramedr mewnbwn.
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Mae tasg sy’n gwrthdaro eisoes yn rhedeg
#XMSG: error message for reading data from backend
txtReadBackendError=Roedd gwall wrth ddarllen o'r cefn.
##XMSG: error message for admission control rejection
admissionControlError=Mae'r dasg wedi methu oherwydd Gwrthod Rheoli Mynediad SAP HANA.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Neilltuo Amserlen i Mi
#XBUT: Pause schedule menu label
pauseScheduleLabel=Rhewi Amserlen
#XBUT: Resume schedule menu label
resumeScheduleLabel=Ailgychwyn Amserlen
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Gwall wrth dynnu amserlenni.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Gwall wrth neilltuo amserlenni.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Gwall wrth rewi amserlenni.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Gwall wrth ailgychwyn amserlenni.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Dileu {0} amserlen
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Newid perchennog {0} amserlen
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Rhewi {0} amserlen
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Ailgychwyn {0} amserlen
#XBUT: Select Columns Button
selectColumnsBtn=Dewis Colofnau
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Mae'n ymddangos bod gwall wedi codi yn y broses ailgynnig diwethaf gan fod y broses rhedeg tasgau flaenorol wedi methu cyn bod modd creu'r cynllun.
#XFLD: Refresh tooltip
TEXT_REFRESH=Adnewyddu
#XFLD: Select Columns tooltip
text_selectColumns=Dewis Colofnau

#Messages for BW Process Chain task log of the Data Integration Monitor
#XMSG: Task log message for successfully starting a BW process chain
startBwProcessChainSuccess=Mae''r gadwyn broses BW "{0}" wedi dechrau''n llwyddiannus yn y tenant Pont SAP BW.
#XMSG: Task log message for skipping a BW process chain execution
bwProcessChainSkipped=Cafodd y gadwyn broses BW "{0}" ei hepgor naill ai oherwydd nad oedd data newydd ar gael neu oherwydd bod gweithrediad blaenorol wedi methu.
#XMSG: Task log message for failure in starting a BW process chain
startBwProcessChainFailure=Mae''r gadwyn broses BW "{0}" wedi methu cychwyn yn y tenant Pont SAP BW.
#XMSG: Task log message for failure in getting the status of a BW process chain
getBwProcessChainStatusFailure=Mae''r dasg wedi methu oherwydd nid oeddem yn gallu adennill statws y gadwyn broses BW "{0}" oherwydd problem sefydlu cysylltiad â SAP BW Bridge. Agorwch yr ap "Cysylltiadau" a dilysu''r cysylltiad SAP BW Bridge yn y gofod "{1}". Os nad oes gennych fynediad at yr ap "Cysylltiadau", cysylltwch â''ch gweinyddwr.
#XMSG: Task log message for failure in completing a BW process chain
completeBwProcessChainFailure=Mae''r gadwyn broses BW "{0}" wedi methu cwblhau yn y tenant Pont SAP BW.
#XMSG: Task log message for the clickable text of the SAP BW Bridge Monitor link
bwBridgeMonitorLinkText=Gweld y manylion yn Monitor SAP BW Bridge
#XMSG warning message while retrieving the task log messages if the user does not have access to the SAP BW Bridge space or to the shared object's space
missingPrivilegeOnSpaceError=Cafwyd problem wrth nôl y negeseuon, neu does gennych chi ddim y caniatâd sydd ei angen i'w gweld nhw.

#Messages for API task log of the Data Integration Monitor
#XMSG: Task log message for displaying the API response
viewResponse=Ymateb wedi''i dderbyn ar gyfer "{0}".
#XMSG: Task log message for empty ID in the API response to retrieve the status
emptyJobId=Doedd dim ID wedi''i ddychwelyd yn y corff ymateb ar gyfer y llwybr JSON: "{0}".
#XMSG: Task log message for empty success value in the API response
emptySuccessValue=Doedd dim gwerth llwyddiant wedi''i ddychwelyd yn y corff ymateb ar gyfer y llwybr JSON: "{0}".
#XMSG: Task log message for empty error value in the API response
emptyErrorValue=Doedd dim gwerth gwall wedi''i ddychwelyd yn y corff ymateb ar gyfer y llwybr JSON: "{0}".
#XMSG: Task log message for none of the success/error conditions matching for the values in the API response
conditionsNotFulfilled=Nid yw amodau'r dangosydd llwyddiant neu wall a nodwyd yn cyfateb i'r gwerthoedd yn yr ymateb.
#XMSG: Task log message for the API response status being unsuccessful
statusNotOk=Mae''r ymateb ar gyfer "{1}" wedi dychwelyd cod statws aflwyddiannus: {0}.
#XMSG: Task log message for the API response body being empty
emptyResponseBody=Roedd y corff ymateb yn wag.
#XMSG: Task log message for the API response location header being invalid
emptyLocationHeader=Roedd y penyn lleoliad yn yr ymateb yn wag.
#XMSG: Task log message for failure in retrieving the status of the invoked API
getStatusFailure=Wedi methu adfer statws yr API sydd wedi'i ysgogi.
#XMSG: Task log message for failure in completing the API task
completionFailure=Wedi methu cwblhau''r dasg API "{0}".
#XMSG: Task log message for a successful API task completion
apiCompleted=Wedi llwyddo i gwblhau tasg API "{0}".
#XMSG: Task log message for an API task with an invalid configuration
invalidConfiguration=Mae ffurfweddiad y dasg API "{0}" yn annilys. Edrychwch ar y ffurfweddiad a rhoi cynnig arall arni.
#XMSG: Task log message for the API task being canceled
cancelStart=Wedi gofyn am gael ganslo tasg API "{0}" gyda logId {1}.
#XMSG: Task log message for the cancelation of the an API task which is no longer running and cannot be canceled
unableToCancel=Nid yw''r dasg API "{0}" gyda logId {1} bellach yn rhedeg, ac nid oes modd ei chanslo.
#XMSG: Task log message when starting an API task. It should carry the name of the API task.
apiRunStart=Wrthi''n dechrau''r dasg API "{0}".
#XMSG: Task log message when the task preparation is taking too long and times out
preparationTimeout=Mae'r broses o baratoi'r dasg yn cymryd gormod o amser, ac mae wedi dod i ben.
#XMSG: Task log message when a task failed because a cancel task was triggered
apiRunCancelled=Cafodd y dasg rhedeg gyda logId {0} ei chanslo drwy ganslo''r dasg {1}.
#XMSG: Task log message when a cancel task failed to cancel a run task
cancelFailure=Mae''r dasg canslo gyda logId {1} wedi methu â chanslo''r dasg rhedeg gyda logId {0}.
#XMSG: Task log message when configuration of the API task is too large
apiTaskconfigurationSizeLimitError=Methu cychwyn tasg API {0} gan fod maint ffurfweddiad ei dasg yn fwy na''r maint mwyaf a ganiateir, sef 100 cibibeit (KiB).
#XMSG: Task log message for failure in completing the Notification task
notificationTaskCompletionFailure=Wedi methu cwblhau''r dasg Neges Hysbysu "{0}".
#XMSG: Task log message when starting a Notification task.
notificationTaskRunStart=Wrthi''n dechrau''r dasg Neges Hysbysu "{0}".
#XMSG: Task log message for a successful Notification task completion
notificationTaskCompletionSuccess=Wedi cwblhau''r dasg Neges Hysbysu "{0}".
#XFLD: Label for frequency column
everyLabel=Pob
#XFLD: Plural Recurrence text for Hour
hoursLabel=Oriau
#XFLD: Plural Recurrence text for Day
daysLabel=Diwrnod
#XFLD: Plural Recurrence text for Month
monthsLabel=Mis
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Munud
