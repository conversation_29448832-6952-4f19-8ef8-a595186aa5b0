

#XFLD: Task Chain header
headerTxt=Re<PERSON><PERSON><PERSON> ú<PERSON>({0})
#XFLD: Task Chain header
headerTxtNew=<PERSON><PERSON><PERSON><PERSON> ú<PERSON> ({0})
#XTXT: Text for Schedule label
scheduleTxt=Naplánovať
#XFLD: Text for Create schedule button
createScheduleTxt=Vytvoriť plán
#XFLD: Text for edit schedule
editScheduleTxt=Upraviť plán
#XLFD: Text for delete schedule
deleteScheduleTxt=Odstrániť plán
#XTXT: text for refresh button label
refrestTxt=Obnoviť
#XTXT: Text for Completed status
completedTxt=Dokončené
#XTX: Text for Running status
runningTxt=Spustené
#XTX: Text for failed status
failedTxt=Neúspešné
#XTX: Text for stopped status
stoppedTxt=Zastavené
#XTX: Text for stopping status
stoppingTxt=Zastavuje sa
#XFLD: Header for Chain name
chainNameLabel=Názov reťazca úloh
#XFLD: Header for Chain name
chainNameLabelBus=Podnikový názov
#XFLD: Header for Chain name
chainNameLabelBusNew=Objekt (podnikový názov)
#XFLD: Header for Chain name
chainNameLabelTech=Technický názov
#XFLD: Header for Chain name
chainNameLabelTechNew=Objekt (technický názov)
#XFLD: Last Run Status label
lastRunStatuslabel=Status posledného chodu
#XFLD: Last Run Status label
lastRunStatuslabelNew=Status
#XFLD: Frequency Label
frequencyLabel=Frekvencia
#XFLD: Frequency Label
frequencyLabelNew=Plánovaná frekvencia
#XFLD: Duration label
durationLabel=Trvanie
#XFLD: Duration label
durationLabelNew=Trvanie posledného chodu
#XFLD: Run Start label
runStartLabel=Začiatok posledného chodu
#XFLD: Run end label
runEndLabel=Koniec posledného chodu
#XFLD: Next Run label
nextRunlabel=Ďalší chod
#XFLD: Next Run label
nextRunlabelNew=Plánovaný nasledujúci chod
#XTXT: Task chain logs link tooltip text
taskChainLogsTxt=Detaily protokolu reťazca úloh
#XTXT: Task chain logs link tooltip text
taskChainLogsTxtNew=Detaily
#XTXT: Scheduled text
scheduledTxt=Naplánované
#XTXT: Paused text
pausedTxt=Pozastavené
#XTXT: Execute button label
runLabel=Spustiť
#XTXT: Execute button label
runLabelNew=Spustiť chod
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Spustite reťazec úloh
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Chod reťazca úloh sa spustil.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Chod reťazca úloh sa spustil pre {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Nepodarilo sa spustiť reťazec úloh.
#XFLD: Label for schedule owner column
txtScheduleOwner=Vlastník plánu
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Zobrazuje, kto vytvoril plán
#XMSG: Task log message for start chain
startChain=Spúšťa sa chod reťazca úloh.
#XMSG: Task log message for load chain from repository
loadChainFromRepository=Reťazec úloh načítaný a inicializovaný.
#XMSG: Task log message started task
taskStarted=Úloha {0} sa spustila.
#XMSG: Task log message for finished task
taskFinished=Úloha {0} skončila so statusom {1}.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Načítava sa reťazec úloh a pripravuje sa spustenie celkového počtu {0} úloh, ktoré sú súčasťou tohto reťazca.
#XMSG: Task log message for starting a subtask
chainStartSubtask=Spustenie úlohy {0}. ID úlohy = {1}.
#XMSG: Task log message for finishing a subtask
chainFinishSubtask=Úloha {0} dokončená so statusom {1}.
#XMSG: Task log message for indicating chain success
chainCompleted=Všetkých {0} úloh sa dokončilo. Status reťazca úloh je nastavený na dokončený.
#XMSG: Task log message for indicating chain failure
chainFailed=Z celkového počtu {0} úloh bolo možné {1} úloh dokončiť a {2} úlohy zlyhalo. Status reťazca úloh je nastavený na neúspešný.
#XMSG: Task log message for indicating chain cancelation
chainCanceled=Zrušenie spustenia reťazca úloh sa začalo. ID protokolu zrušenia úlohy je {0}.
#XMSG: Task log message for indicating chain cancelation in the cancel task
chainCanceling=Zrušenie reťazca {0}.
#XMSG: Task log message for general chain runtime error
chainError=Počas spúšťania reťazca úloh sa vyskytla neočakávaná chyba.
#XMSG: Task log message for starting the overwrite check.
chainOverwriteStart=Kontrola, či sa reťazec úloh s ID {0} dokončil.
#XMSG: Task log message indicating the chain is too old.
chainOverwriteTooOld=Nenašli sa žiadne úlohy na spustenie reťazca {0}. Chod je {1} minút starý. Status reťazca úloh je nastavený na neúspešný.
#XMSG: Task log message indicating the chain was created too recently.
chainOverwriteTooRecent=Nenašli sa žiadne úlohy na spustenie reťazca {0}. Chod je {1} minút starý. Reťazec úloh stále prebieha.
#XMSG: Task log message indicating a recent heartbeat was found.
chainOverwriteHeartbeatAlive=Reťazec úloh {0} stále prebieha. Dokončené: {1}, Prebieha: {2}, Neúspešné: {3}, Spustené: {4}, Nespustené: {5}.
#XMSG: Task log message for a chain that is not running anymore but not everything was triggered.
chainOverwriteNotAllTriggered=Pri spúšťaní reťazca úloh {0} sa vyskytla chyba a nebolo možné spustiť všetky úlohy. Dokončené: {1}, Prebieha: {2}, Neúspešné: {3}, Spustené: {4}, Nespustené: {5}. Tento reťazec úloh bol prepísaný.
#XMSG: Task log message for a chain that contains a failed subtask.
chainOverwriteAnySubtaskFailed=Reťazec úloh {0} dokončený. Jedna úloha reťazca zlyhala, nastavenie reťazca na Neúspešné.
#XMSG: Task log message could not determine status.
chainOverwriteNotDetermined=Neočakávaná chyba nám bráni v prístupe k statusu úlohy. Skúste to znova a ak chyba pretrváva, kontaktujte podporu SAP.
#XMSG: Task log message could not take over
failedTakeover=Existujúcu úlohu sa nepodarilo prevziať.
#XMSG: Task log parallel check error
parallelCheckError=Úloha sa nemôže spracovať, pretože prebieha iná úloha, ktorá už blokuje túto úlohu.
#XMSG: Task log parallel task runnig error
parallelTaskRunning=Konfliktná úloha ešte prebieha.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Status {0} počas chodu s ID korelácie {1}.
#XMSG: Task log message successful takeover
successTakeover=Zostávajúce blokovanie bolo potrebné uvoľniť. Je nastavené nové blokovanie pre túto úlohu.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Blokovanie tejto úlohy bolo prevzaté inou úlohou.
#XMSG: Schedule created alert message
createScheduleSuccess=Plán vytvorený
#XMSG: Task log message for taskFinishedAt
taskFinishedAt=Úloha {0} skončila o {2} so statusom {1}.
#XMSG: Placeholder symbol for empty columns
emptyCol=--
#XMSG: Paused Text
txtExpired=Exspirované
#XMSG: Update task schedule success Toast message
updateScheduleSuccess=Plán aktualizovaný
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Plán odstránený.
#XMSG: Task chain might have children error text
taskChainNoChildErrorText=Reťazec úloh môže mať podradené prvky, ktoré sa nezobrazujú, pretože úloha zlyhala pred vygenerovaním plánu.
#XMSG: Task chain repair recent failed run label
retryRunLabel=Zopakovať najnovší chod
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Chod opakovania reťazca úloh bol spustený
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Chod opakovania reťazca úloh zlyhal
#XMSG: chain repair message
chainRetried=Opakovanie reťazca úloh spustil používateľ {0}
#XMSG: chain not found during run
chainNotFoundDuringRun=Nebolo možné nájsť reťazec úloh {0}. Skontrolujte, či existuje pomocou Data Builder a uistite sa, že reťazec je nasadený.
#XMSG: chain is not DAG
chainNotDag=Nie je možné spustiť reťazec úloh {0}. Jeho štruktúra je neplatná. Skontrolujte reťazec úloh v Data Builder.
#XMSG: chain has not valid parameters
notValidParameters=Nie je možné spustiť reťazec úloh {0}. Jeden alebo viacero jeho parametrov je neplatných. Skontrolujte reťazec úloh v nástroji Data Builder.
#XMSG: Configuration parameter value size exceeds the maximum allowed limit
configurationSizeLimitError=Nie je možné spustiť reťazec úloh {0}. Veľkosť konfigurácie úlohy v reťazci presahuje maximálnu povolenú veľkosť 100 kibibajtov (KiB).
#XMSG: Task {0} has input parameters
taskHasInputParameters=Úloha {0} má vstupné parametre
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Konfliktná úloha už prebieha
#XMSG: error message for reading data from backend
txtReadBackendError=Zdá sa, že počas načítania z backendu sa vyskytla chyba.
##XMSG: error message for admission control rejection
admissionControlError=Úloha zlyhala z dôvodu zamietnutia kontroly prístupu SAP HANA.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Priradiť plán mne
#XBUT: Pause schedule menu label
pauseScheduleLabel=Pozastaviť plán
#XBUT: Resume schedule menu label
resumeScheduleLabel=Obnoviť plán
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Pri odstraňovaní plánov sa vyskytla chyba.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Pri priraďovaní plánov sa vyskytla chyba.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Pri pozastavení plánov sa vyskytla chyba.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Pri obnovení plánov sa vyskytla chyba.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Odstraňuje sa {0} plánov
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Zmena vlastníka {0} plánov
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Pozastavenie {0} plánov
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Obnovenie {0} plánov
#XBUT: Select Columns Button
selectColumnsBtn=Vybrať stĺpce
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Zdá sa, že pri zopakovaní posledného chodu sa vyskytla chyba, pretože predchádzajúce spustenie úlohy zlyhalo pred vygenerovaním plánu.
#XFLD: Refresh tooltip
TEXT_REFRESH=Obnoviť
#XFLD: Select Columns tooltip
text_selectColumns=Vybrať stĺpce

#Messages for BW Process Chain task log of the Data Integration Monitor
#XMSG: Task log message for successfully starting a BW process chain
startBwProcessChainSuccess=Reťazec procesov BW "{0}" bol úspešne spustený v kliente SAP BW Bridge.
#XMSG: Task log message for skipping a BW process chain execution
bwProcessChainSkipped=Reťazec procesov BW "{0}" bol preskočený buď z dôvodu nedostupnosti nových údajov alebo zlyhania predchádzajúceho vykonania.
#XMSG: Task log message for failure in starting a BW process chain
startBwProcessChainFailure=Reťazec procesov BW "{0}" sa nepodarilo spustiť v kliente SAP BW Bridge.
#XMSG: Task log message for failure in getting the status of a BW process chain
getBwProcessChainStatusFailure=Úloha zlyhala, pretože sa nám nepodarilo načítať status procesného reťazca BW "{0}" kvôli problému s nadviazaním pripojenia k SAP BW Bridge. Otvorte aplikáciu „Pripojenia“ a overte pripojenie SAP BW Bridge v priestore "{1}". Ak nemáte prístup k aplikácii „Pripojenia“, kontaktujte svojho správcu.
#XMSG: Task log message for failure in completing a BW process chain
completeBwProcessChainFailure=Reťazec procesov BW "{0}" sa nepodarilo dokončiť v kliente SAP BW Bridge.
#XMSG: Task log message for the clickable text of the SAP BW Bridge Monitor link
bwBridgeMonitorLinkText=Zobraziť podrobnosti v SAP BW Bridge Monitor
#XMSG warning message while retrieving the task log messages if the user does not have access to the SAP BW Bridge space or to the shared object's space
missingPrivilegeOnSpaceError=Pri načítavaní správ sa vyskytol problém alebo nemáte potrebné povolenie na ich zobrazenie.

#Messages for API task log of the Data Integration Monitor
#XMSG: Task log message for displaying the API response
viewResponse=Prijatá odpoveď pre "{0}".
#XMSG: Task log message for empty ID in the API response to retrieve the status
emptyJobId=V hlavnej časti odpovede pre cestu JSON nebolo vrátené žiadne ID: "{0}".
#XMSG: Task log message for empty success value in the API response
emptySuccessValue=V hlavnej časti odpovede pre cestu JSON nebola vrátená žiadna hodnota úspešného vykonania: "{0}".
#XMSG: Task log message for empty error value in the API response
emptyErrorValue=V hlavnej časti odpovede pre cestu JSON nebola vrátená žiadna hodnota chyby: "{0}".
#XMSG: Task log message for none of the success/error conditions matching for the values in the API response
conditionsNotFulfilled=Žiadna zo špecifikovaných podmienok indikátora úspešného vykonania alebo chyby nezodpovedá hodnotám v odpovedi.
#XMSG: Task log message for the API response status being unsuccessful
statusNotOk=Odpoveď "{1}" vrátila neúspešný stavový kód: {0}.
#XMSG: Task log message for the API response body being empty
emptyResponseBody=Hlavná časť odpovede bola prázdna.
#XMSG: Task log message for the API response location header being invalid
emptyLocationHeader=Hlavička miesta v odpovedi bola prázdna.
#XMSG: Task log message for failure in retrieving the status of the invoked API
getStatusFailure=Stav vyvolaného API sa nepodarilo získať.
#XMSG: Task log message for failure in completing the API task
completionFailure=Úlohu API "{0}" sa nepodarilo vykonať.
#XMSG: Task log message for a successful API task completion
apiCompleted=Úloha API "{0}" bola úspešne dokončená.
#XMSG: Task log message for an API task with an invalid configuration
invalidConfiguration=Konfigurácia úlohy API "{0}" je neplatná. Skontrolujte konfiguráciu a skúste to znova.
#XMSG: Task log message for the API task being canceled
cancelStart=Požadované zrušenie úlohy API "{0}" s logId {1}.
#XMSG: Task log message for the cancelation of the an API task which is no longer running and cannot be canceled
unableToCancel=Úloha API "{0}" s logId {1} už nie je spustená a nedá sa zrušiť.
#XMSG: Task log message when starting an API task. It should carry the name of the API task.
apiRunStart=Spúšťa sa úloha API "{0}".
#XMSG: Task log message when the task preparation is taking too long and times out
preparationTimeout=Príprava úlohy trvá príliš dlho a vypršal jej časový limit.
#XMSG: Task log message when a task failed because a cancel task was triggered
apiRunCancelled=Úloha spustenia s logId {0} bola zrušená úlohou zrušenia {1}.
#XMSG: Task log message when a cancel task failed to cancel a run task
cancelFailure=Úlohe uzrušenia s logId {1} sa nepodarilo zrušiť spustenie úlohy s logId {0}.
#XMSG: Task log message when configuration of the API task is too large
apiTaskconfigurationSizeLimitError=Úloha API {0} sa nedá spustiť, pretože veľkosť jej konfigurácie úlohy presahuje maximálnu povolenú veľkosť 100 kibibajtov (KiB).
#XMSG: Task log message for failure in completing the Notification task
notificationTaskCompletionFailure=Úlohu notifikácie "{0}" sa nepodarilo vykonať.
#XMSG: Task log message when starting a Notification task.
notificationTaskRunStart=Spustenie úlohy notifikácie "{0}".
#XMSG: Task log message for a successful Notification task completion
notificationTaskCompletionSuccess=Úloha notifikácie "{0}" bola dokončená.
#XFLD: Label for frequency column
everyLabel=Každých
#XFLD: Plural Recurrence text for Hour
hoursLabel=Hodiny
#XFLD: Plural Recurrence text for Day
daysLabel=Dni
#XFLD: Plural Recurrence text for Month
monthsLabel=Mesiace
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minúty
