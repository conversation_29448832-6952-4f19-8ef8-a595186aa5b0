

#XFLD: Task Chain header
headerTxt=Cadenes de tasques ({0})
#XFLD: Task Chain header
headerTxtNew=Cadenes de tasques ({0})
#XTXT: Text for Schedule label
scheduleTxt=Programar
#XFLD: Text for Create schedule button
createScheduleTxt=Crear programa
#XFLD: Text for edit schedule
editScheduleTxt=Editar programa
#XLFD: Text for delete schedule
deleteScheduleTxt=Suprimir programa
#XTXT: text for refresh button label
refrestTxt=Actualitzar
#XTXT: Text for Completed status
completedTxt=Conclòs
#XTX: Text for Running status
runningTxt=En execució
#XTX: Text for failed status
failedTxt=Erroni
#XTX: Text for stopped status
stoppedTxt=Aturat
#XTX: Text for stopping status
stoppingTxt=Aturant
#XFLD: Header for Chain name
chainNameLabel=Nom de la cadena de tasques
#XFLD: Header for Chain name
chainNameLabelBus=Nom empresarial
#XFLD: Header for Chain name
chainNameLabelBusNew=Objecte (nom empresarial)
#XFLD: Header for Chain name
chainNameLabelTech=Nom tècnic
#XFLD: Header for Chain name
chainNameLabelTechNew=Objecte (nom tècnic)
#XFLD: Last Run Status label
lastRunStatuslabel=Últim estat d’execució
#XFLD: Last Run Status label
lastRunStatuslabelNew=Estat
#XFLD: Frequency Label
frequencyLabel=Freqüència
#XFLD: Frequency Label
frequencyLabelNew=Freqüència programada
#XFLD: Duration label
durationLabel=Durada
#XFLD: Duration label
durationLabelNew=Durada de l'última execució
#XFLD: Run Start label
runStartLabel=Inici de l’última execució
#XFLD: Run end label
runEndLabel=Fi de l’última execució
#XFLD: Next Run label
nextRunlabel=Execució següent
#XFLD: Next Run label
nextRunlabelNew=Següent execució programada
#XTXT: Task chain logs link tooltip text
taskChainLogsTxt=Detalls del log de la cadena de tasques
#XTXT: Task chain logs link tooltip text
taskChainLogsTxtNew=Detalls
#XTXT: Scheduled text
scheduledTxt=Programat
#XTXT: Paused text
pausedTxt=En pausa
#XTXT: Execute button label
runLabel=Executar
#XTXT: Execute button label
runLabelNew=Iniciar execució
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Executar la cadena de tasques
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=S'ha iniciat l'execució de la cadena de tasques.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=S''ha iniciat l''execució de la cadena de tasques per a {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Error en executar la cadena de tasques.
#XFLD: Label for schedule owner column
txtScheduleOwner=Propietari del programa
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Mostra qui ha creat el programa
#XMSG: Task log message for start chain
startChain=S'ha iniciat l'execució de la cadena de tasques.
#XMSG: Task log message for load chain from repository
loadChainFromRepository=S'ha carregat i inicialitzat la cadena de tasques.
#XMSG: Task log message started task
taskStarted=S''ha iniciat la tasca {0}.
#XMSG: Task log message for finished task
taskFinished=La tasca {0} ha conclòs amb l''estat {1}.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=S''està carregant la cadena de tasques i preparant l''execució d''un total de {0} tasques que formen part d''aquesta cadena.
#XMSG: Task log message for starting a subtask
chainStartSubtask=Iniciant tasca {0} per executar. ID de tasca = {1}.
#XMSG: Task log message for finishing a subtask
chainFinishSubtask=La tasca {0} ha finalitzat amb l''estat {1}.
#XMSG: Task log message for indicating chain success
chainCompleted=Les {0} tasques estan concloses. L''estat de la cadena de tasques es fixa en conclòs.
#XMSG: Task log message for indicating chain failure
chainFailed=Sobre un total de {0} tasques, {1} s''han pogut concloure i {2} han donat error. L''estat de la cadena de tasques es fixa en error.
#XMSG: Task log message for indicating chain cancelation
chainCanceled=S''ha iniciat la cancel·lació de l''execució de la cadena de tasques. L''ID de log de la tasca de cancel·lació és {0}.
#XMSG: Task log message for indicating chain cancelation in the cancel task
chainCanceling=S''està cancel·lant la cadena {0}.
#XMSG: Task log message for general chain runtime error
chainError=S'ha produït un error inesperat en executar la cadena de tasques.
#XMSG: Task log message for starting the overwrite check.
chainOverwriteStart=S''està verificant si la cadena de tasques amb ID {0} ha conclòs.
#XMSG: Task log message indicating the chain is too old.
chainOverwriteTooOld=No existeixen tasques per executar per a la cadena {0}. L''execució té {1} minuts. L''estat de la cadena de tasques es fixa en error.
#XMSG: Task log message indicating the chain was created too recently.
chainOverwriteTooRecent=No existeixen tasques per executar per a la cadena {0}. L''execució té {1} minuts. La cadena de tasques encara s''està executant.
#XMSG: Task log message indicating a recent heartbeat was found.
chainOverwriteHeartbeatAlive=La cadena de tasques {0} encara s''està executant. Conclòs: {1}, En execució: {2}, Error: {3}, Desencadenat: {4}, No desencadenat: {5}.
#XMSG: Task log message for a chain that is not running anymore but not everything was triggered.
chainOverwriteNotAllTriggered=S''ha produït un error en executar la cadena de tasques {0} i no s''han pogut iniciar totes. Conclòs: {1}, En execució: {2}, Error: {3}, Desencadenat: {4}, No desencadenat: {5}. Aquesta cadena de tasques s''ha sobreescrit.
#XMSG: Task log message for a chain that contains a failed subtask.
chainOverwriteAnySubtaskFailed=S''ha finalitzat la cadena de tasques {0}. Una de les tasques ha estat fallida i la cadena es fixa en error.
#XMSG: Task log message could not determine status.
chainOverwriteNotDetermined=Un error inesperat ens impedeix accedir a l'estat de la tasca. Torneu a provar-ho i adreceu-vos al servei d'assistència de SAP si l'error persisteix.
#XMSG: Task log message could not take over
failedTakeover=No s'ha pogut assumir la tasca existent.
#XMSG: Task log parallel check error
parallelCheckError=La tasca no es pot processar perquè hi ha una altra tasca que s'està executant i que ja la bloqueja.
#XMSG: Task log parallel task runnig error
parallelTaskRunning=Ja s’està executant una tasca en conflicte.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Estat {0} durant l''execució amb l''ID de correlació {1}.
#XMSG: Task log message successful takeover
successTakeover=Calia alliberar el bloqueig sobrant. S'ha fixat el bloqueig nou per a aquesta tasca.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=El bloqueig d'aquesta tasca ha estat assumit per una altra tasca.
#XMSG: Schedule created alert message
createScheduleSuccess=Programació creada
#XMSG: Task log message for taskFinishedAt
taskFinishedAt=La tasca {0} ha conclòs a les {2} amb l''estat {1}.
#XMSG: Placeholder symbol for empty columns
emptyCol=--
#XMSG: Paused Text
txtExpired=Vençut
#XMSG: Update task schedule success Toast message
updateScheduleSuccess=Programació actualitzada
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Programació suprimida
#XMSG: Task chain might have children error text
taskChainNoChildErrorText=Pot ser que la cadena de tasques tingui elements inferiors que no es mostrin perquè s'ha produït un error a la tasca abans que es pogués generar el pla.
#XMSG: Task chain repair recent failed run label
retryRunLabel=Reintentar última execució
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=S'ha iniciat l'execució de reintent de la cadena de tasques
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=L'execució de reintent de la cadena de tasques ha estat fallida
#XMSG: chain repair message
chainRetried=L''usuari {0} ha iniciat el reintent de la cadena de tasques
#XMSG: chain not found during run
chainNotFoundDuringRun=La cadena de tasques {0} no existeix. Verifiqueu-ho amb el Generador de dades i comproveu que la cadena estigui desplegada.
#XMSG: chain is not DAG
chainNotDag=No es pot iniciar la cadena de tasques {0}. No té una estructura vàlida. Verifiqueu la cadena de tasques al Generador de dades.
#XMSG: chain has not valid parameters
notValidParameters=No es pot iniciar la cadena de tasques {0}. Un o més dels seus paràmetres no són vàlids. Verifiqueu la cadena de tasques al Generador de dades.
#XMSG: Configuration parameter value size exceeds the maximum allowed limit
configurationSizeLimitError=No es pot iniciar la cadena de tasques {0}. La mida de la configuració d''una tasca de la cadena supera la mida màxima permesa de 100 kibibytes (KiB).
#XMSG: Task {0} has input parameters
taskHasInputParameters=La tasca {0} té paràmetres d''entrada.
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Ja s'està executant una tasca en conflicte.
#XMSG: error message for reading data from backend
txtReadBackendError=Sembla que s’ha produït un error en llegir al back-end.
##XMSG: error message for admission control rejection
admissionControlError=S'ha produït un error a la tasca a causa d'un rebuig de control d'admissió a SAP HANA.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Assignar-me programa
#XBUT: Pause schedule menu label
pauseScheduleLabel=Pausar programa
#XBUT: Resume schedule menu label
resumeScheduleLabel=Reprendre programa
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=S'ha produït un error en eliminar els programes.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=S'ha produït un error en assignar els programes.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=S'ha produït un error en pausar els programes.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=S'ha produït un error en reprendre els programes.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Suprimir {0} programes
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Modificar el propietari de {0} programes
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Pausar {0} programes
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Reprendre {0} programes
#XBUT: Select Columns Button
selectColumnsBtn=Seleccionar columnes
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Sembla que s'ha produït un error en tornar a intentar l'última execució, ja que l'execució de la tasca anterior ha fallat abans que es pogués generar el pla.
#XFLD: Refresh tooltip
TEXT_REFRESH=Actualitzar
#XFLD: Select Columns tooltip
text_selectColumns=Seleccionar columnes

#Messages for BW Process Chain task log of the Data Integration Monitor
#XMSG: Task log message for successfully starting a BW process chain
startBwProcessChainSuccess=La cadena de processos BW "{0}" s''ha iniciat amb èxit a l''arrendatari SAP BW Bridge.
#XMSG: Task log message for skipping a BW process chain execution
bwProcessChainSkipped=La cadena de processos BW "{0}" s''ha omès perquè no hi havia dades noves disponibles o per un error en una execució anterior.
#XMSG: Task log message for failure in starting a BW process chain
startBwProcessChainFailure=La cadena de processos BW "{0}" no s''ha pogut iniciar a l''arrendatari SAP BW Bridge.
#XMSG: Task log message for failure in getting the status of a BW process chain
getBwProcessChainStatusFailure=La tasca ha estat fallida perquè no hem pogut recuperar l''estat de la cadena de processos BW "{0}" a causa d''un problema per establir una connexió amb el pont de SAP BW. Obriu l''aplicació "Connexions" i valideu la connexió del pont de SAP BW a l''espai "{1}". Si  no teniu accés a l''aplicació "Connexions", adreceu-vos a l''administrador.
#XMSG: Task log message for failure in completing a BW process chain
completeBwProcessChainFailure=La cadena de processos BW "{0}" no s''ha pogut finalitzar en l''arrendatari SAP BW Bridge.
#XMSG: Task log message for the clickable text of the SAP BW Bridge Monitor link
bwBridgeMonitorLinkText=Visualitza els detalls a SAP BW Bridge Monitor
#XMSG warning message while retrieving the task log messages if the user does not have access to the SAP BW Bridge space or to the shared object's space
missingPrivilegeOnSpaceError=Hi havia un problema en recuperar els missatges o no teniu el permís necessari per a veure'ls.

#Messages for API task log of the Data Integration Monitor
#XMSG: Task log message for displaying the API response
viewResponse=Resposta rebuda per a "{0}".
#XMSG: Task log message for empty ID in the API response to retrieve the status
emptyJobId=No s''ha retornat cap ID al cos de la resposta per a la via d''accés JSON: "{0}".
#XMSG: Task log message for empty success value in the API response
emptySuccessValue=No s''ha retornat cap valor d''èxit al cos de la resposta per a la via d''accés JSON: "{0}".
#XMSG: Task log message for empty error value in the API response
emptyErrorValue=No s''ha retornat cap valor d''error al cos de la resposta per a la via d''accés JSON: "{0}".
#XMSG: Task log message for none of the success/error conditions matching for the values in the API response
conditionsNotFulfilled=Cap de les condicions de l'indicador d'èxit o error indicades coincideix amb els valors de la resposta.
#XMSG: Task log message for the API response status being unsuccessful
statusNotOk=La resposta per a "{1}" ha donat un codi d''estat fallit: {0}.
#XMSG: Task log message for the API response body being empty
emptyResponseBody=El cos de la resposta estava buit.
#XMSG: Task log message for the API response location header being invalid
emptyLocationHeader=La capçalera d'ubicació de la resposta estava buida.
#XMSG: Task log message for failure in retrieving the status of the invoked API
getStatusFailure=L'estat de l'API cridada no s'ha pogut recuperar.
#XMSG: Task log message for failure in completing the API task
completionFailure=La tasca d''API "{0}" no ha conclòs.
#XMSG: Task log message for a successful API task completion
apiCompleted=La tasca d''API "{0}" ha conclòs correctament.
#XMSG: Task log message for an API task with an invalid configuration
invalidConfiguration=La configuració de la tasca d''API "{0}" no és vàlida. Verifiqueu la configuració i torneu a provar-ho.
#XMSG: Task log message for the API task being canceled
cancelStart=Cancel·lació sol·licitada de la tasca API "{0}" amb logId {1}.
#XMSG: Task log message for the cancelation of the an API task which is no longer running and cannot be canceled
unableToCancel=La tasca API "{0}" amb logId {1} ja no s''executa i no es pot cancel·lar.
#XMSG: Task log message when starting an API task. It should carry the name of the API task.
apiRunStart=Inici de la tasca API "{0}".
#XMSG: Task log message when the task preparation is taking too long and times out
preparationTimeout=La preparació de la tasca triga massa i s'ha aturat.
#XMSG: Task log message when a task failed because a cancel task was triggered
apiRunCancelled=La tasca d''execució amb logId {0} s''ha cancel·lat per la tasca de cancel·lació {1}.
#XMSG: Task log message when a cancel task failed to cancel a run task
cancelFailure=La tasca de cancel·lació amb logId {1} ha fallat a cancel·lar la tasca d''execució amb logId {0}.
#XMSG: Task log message when configuration of the API task is too large
apiTaskconfigurationSizeLimitError=No es pot iniciar la tasca d''API {0} perquè la mida de la seva configuració de tasca supera la mida màxima permesa de 100 kibibytes (KiB).
#XMSG: Task log message for failure in completing the Notification task
notificationTaskCompletionFailure=La tasca de notificació "{0}" no ha conclòs.
#XMSG: Task log message when starting a Notification task.
notificationTaskRunStart=S''inicia la tasca de notificació "{0}".
#XMSG: Task log message for a successful Notification task completion
notificationTaskCompletionSuccess=La tasca de notificació "{0}" ha conclòs.
#XFLD: Label for frequency column
everyLabel=Cada
#XFLD: Plural Recurrence text for Hour
hoursLabel=Hores
#XFLD: Plural Recurrence text for Day
daysLabel=Dies
#XFLD: Plural Recurrence text for Month
monthsLabel=Mesos
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minuts
