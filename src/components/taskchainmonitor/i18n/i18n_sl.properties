

#XFLD: Task Chain header
headerTxt=Verige nalog ({0})
#XFLD: Task Chain header
headerTxtNew=Verige nalog ({0})
#XTXT: Text for Schedule label
scheduleTxt=Časovno načrtujte
#XFLD: Text for Create schedule button
createScheduleTxt=Ustvari časovni načrt
#XFLD: Text for edit schedule
editScheduleTxt=Uredi časovni načrt
#XLFD: Text for delete schedule
deleteScheduleTxt=Izbriš<PERSON> časovni načrt
#XTXT: text for refresh button label
refrestTxt=Osveži
#XTXT: Text for Completed status
completedTxt=Dokončano
#XTX: Text for Running status
runningTxt=Se izvaja
#XTX: Text for failed status
failedTxt=Ni uspelo
#XTX: Text for stopped status
stoppedTxt=Zaustavljeno
#XTX: Text for stopping status
stoppingTxt=Se zaustavlja
#XFLD: Header for Chain name
chainNameLabel=Ime verige nalog
#XFLD: Header for Chain name
chainNameLabelBus=Poslovno ime
#XFLD: Header for Chain name
chainNameLabelBusNew=Objekt (poslovno ime)
#XFLD: Header for Chain name
chainNameLabelTech=Tehnično ime
#XFLD: Header for Chain name
chainNameLabelTechNew=Objekt (tehnično ime)
#XFLD: Last Run Status label
lastRunStatuslabel=Status zadnjega izvajanja
#XFLD: Last Run Status label
lastRunStatuslabelNew=Status
#XFLD: Frequency Label
frequencyLabel=Pogostost
#XFLD: Frequency Label
frequencyLabelNew=Načrtovana pogostost
#XFLD: Duration label
durationLabel=Trajanje
#XFLD: Duration label
durationLabelNew=Trajanje zadnjega izvajanja
#XFLD: Run Start label
runStartLabel=Začetek zadnjega izvajanja
#XFLD: Run end label
runEndLabel=Konec zadnjega izvajanja
#XFLD: Next Run label
nextRunlabel=Naslednje izvajanje
#XFLD: Next Run label
nextRunlabelNew=Načrtovano naslednje izvajanje
#XTXT: Task chain logs link tooltip text
taskChainLogsTxt=Podrobnosti dnevnika verige nalog
#XTXT: Task chain logs link tooltip text
taskChainLogsTxtNew=Podrobnosti
#XTXT: Scheduled text
scheduledTxt=Načrtovano
#XTXT: Paused text
pausedTxt=Začasno zaustavljeno
#XTXT: Execute button label
runLabel=Izvedi
#XTXT: Execute button label
runLabelNew=Začetek izvajanja
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Vnovična izvedba verige nalog
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Izvajanje verige nalog je začeto.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Izvajanje verige nalog je bilo zagnano za {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Izvajanje verige nalog ni uspelo.
#XFLD: Label for schedule owner column
txtScheduleOwner=Lastnik časovnega načrta
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Prikaže, kdo je ustvaril časovni načrt
#XMSG: Task log message for start chain
startChain=Začetek izvajanja verige nalog.
#XMSG: Task log message for load chain from repository
loadChainFromRepository=Veriga nalog prenesena in inicializirana.
#XMSG: Task log message started task
taskStarted=Naloga {0} se je začela.
#XMSG: Task log message for finished task
taskFinished=Naloga {0} se je končala s statusom {1}.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Prenos verige nalog in priprava za izvajanje skupno {0} nalog, ki so del te verige.
#XMSG: Task log message for starting a subtask
chainStartSubtask=Sprožitev izvajanja naloge {0}. ID naloge = {1}.
#XMSG: Task log message for finishing a subtask
chainFinishSubtask=Naloga {0} končana s statusom {1}.
#XMSG: Task log message for indicating chain success
chainCompleted=Vseh {0} nalog je dokončanih. Status verige nalog je nastavljen na Dokončano.
#XMSG: Task log message for indicating chain failure
chainFailed=Od skupno {0} nalog je bilo mogoče dokončati {1} nalog, {2} nalog pa ni uspelo. Status verige nalog je nastavljen na Ni uspelo.
#XMSG: Task log message for indicating chain cancelation
chainCanceled=Začel se je preklic izvajanja verige nalog. ID zapisnika za nalogo preklica je {0}.
#XMSG: Task log message for indicating chain cancelation in the cancel task
chainCanceling=Preklic verige {0}.
#XMSG: Task log message for general chain runtime error
chainError=Pri izvajanju verige nalog je prišlo do nepričakovane napake.
#XMSG: Task log message for starting the overwrite check.
chainOverwriteStart=Preverjanje, ali je veriga nalog z ID-jem {0} končana.
#XMSG: Task log message indicating the chain is too old.
chainOverwriteTooOld=Nalog za izvajanje za verigo {0} ni bilo mogoče najti. Izvajanje se je začelo pred {1} minutami. Status verige nalog je nastavljen na Ni uspelo.
#XMSG: Task log message indicating the chain was created too recently.
chainOverwriteTooRecent=Nalog za izvajanje za verigo {0} ni bilo mogoče najti. Izvajanje se je začelo pred {1} minutami. Veriga nalog se še vedno izvaja.
#XMSG: Task log message indicating a recent heartbeat was found.
chainOverwriteHeartbeatAlive=Veriga nalog {0} se še vedno izvaja. Dokončano: {1}, se izvaja: {2}, ni uspelo: {3}, sproženo: {4}, ni sproženo: {5}.
#XMSG: Task log message for a chain that is not running anymore but not everything was triggered.
chainOverwriteNotAllTriggered=Pri izvajanju verige naloge {0} je prišlo do napake in vseh nalog ni bilo mogoče sprožiti. Dokončano: {1}, se izvaja: {2}, ni uspelo: {3}, sproženo: {4}, ni sproženo: {5}. Ta veriga nalog je bila prepisana.
#XMSG: Task log message for a chain that contains a failed subtask.
chainOverwriteAnySubtaskFailed=Veriga nalog {0} končana. Ena naloga verige ni uspela, veriga je nastavljena na Ni uspelo.
#XMSG: Task log message could not determine status.
chainOverwriteNotDetermined=Nepričakovana napaka preprečuje dostop do statusa naloge. Poskusite znova, če napake ne morete odpraviti pa se obrnite na SAP-jevo podporo.
#XMSG: Task log message could not take over
failedTakeover=Prevzem obstoječe naloge ni uspel.
#XMSG: Task log parallel check error
parallelCheckError=Naloge ni mogoče obdelati, ker se izvaja druga naloga in že blokira to nalogo.
#XMSG: Task log parallel task runnig error
parallelTaskRunning=Naloga v sporu se že izvaja.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Status {0} pri izvajanju z ID-jem korelacije {1}.
#XMSG: Task log message successful takeover
successTakeover=Prejšnjo blokado je bilo treba sprostiti. Nova blokada za to nalogo je nastavljena.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Blokado te naloge je prevzela druga naloga.
#XMSG: Schedule created alert message
createScheduleSuccess=Časovni razpored ustvarjen
#XMSG: Task log message for taskFinishedAt
taskFinishedAt=Naloga {0} končana ob {2} s statusom {1}.
#XMSG: Placeholder symbol for empty columns
emptyCol=--
#XMSG: Paused Text
txtExpired=Poteklo
#XMSG: Update task schedule success Toast message
updateScheduleSuccess=Časovni razpored posodobljen
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Časovni razpored izbrisan.
#XMSG: Task chain might have children error text
taskChainNoChildErrorText=Veriga nalog ima morda podrejene elemente, ki niso prikazani, ker je bila naloga neuspešna, preden je bilo mogoče generirati načrt.
#XMSG: Task chain repair recent failed run label
retryRunLabel=Ponoven poskus zadnjega izvajanja
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Ponoven poskus verige nalog se je začel
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Ponoven poskus verige nalog ni uspel
#XMSG: chain repair message
chainRetried=Ponoven poskus verige nalog je sprožil uporabnik {0}
#XMSG: chain not found during run
chainNotFoundDuringRun=Verige nalog {0} ni mogoče najti. V graditelju podatkov preverite, ali obstaja in ali je postavljena.
#XMSG: chain is not DAG
chainNotDag=Verige nalog {0} ni mogoče zagnati. Njena struktura ni veljavna. Z graditeljem podatkov preverite verigo nalog.
#XMSG: chain has not valid parameters
notValidParameters=Verige nalog {0} ni mogoče zagnati. Eden ali več njenih parametrov je neveljavnih. Z graditeljem podatkov preverite verigo nalog.
#XMSG: Configuration parameter value size exceeds the maximum allowed limit
configurationSizeLimitError=Verige nalog {0} ni mogoče začeti. Velikost konfiguracije naloge v verigi presega največjo dovoljeno velikost 100 kibibajtov (KiB).
#XMSG: Task {0} has input parameters
taskHasInputParameters=Naloga {0} ima vhodne parametre.
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Naloga v sporu se že izvaja
#XMSG: error message for reading data from backend
txtReadBackendError=Videti je, da je prišlo do napake pri branju iz zaledja.
##XMSG: error message for admission control rejection
admissionControlError=Naloga ni uspela zaradi zavrnitve nadzora sprejema SAP HANA.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Dodeli časovni načrt meni
#XBUT: Pause schedule menu label
pauseScheduleLabel=Začasno prekini časovni načrt
#XBUT: Resume schedule menu label
resumeScheduleLabel=Nadaljuj časovni načrt
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Prišlo je do napake pri odstranjevanju časovnih načrtov.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Prišlo je do napake pri dodelitvi časovnih načrtov.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Prišlo je do napake pri začasni prekinitvi izvajanja časovnih načrtov.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Prišlo je do napake pri nadaljevanju izvajanja časovnih načrtov.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Izbris {0} časovnih načrtov je v teku
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Sprememba lastnika {0} časovnih načrtov je v teku
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Začasna prekinitev {0} časovnih načrtov
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Nadaljevanje {0} časovnih načrtov
#XBUT: Select Columns Button
selectColumnsBtn=Izbira stolpcev
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Očitno je prišlo do napake pri ponovnem poskusu zadnjega izvajanja, saj prejšnjo izvajanje naloge ni bilo uspešno, preden je bilo mogoče generirati plan.
#XFLD: Refresh tooltip
TEXT_REFRESH=Osveži
#XFLD: Select Columns tooltip
text_selectColumns=Izbira stolpcev

#Messages for BW Process Chain task log of the Data Integration Monitor
#XMSG: Task log message for successfully starting a BW process chain
startBwProcessChainSuccess=Procesna veriga BW "{0}" se je uspešno začela v najemniku SAP BW Bridge.
#XMSG: Task log message for skipping a BW process chain execution
bwProcessChainSkipped=Procesna veriga BW "{0}" je bila preskočena zaradi nerazpoložljivosti novih podatkov ali neizvedbe predhodnega izvajanja.
#XMSG: Task log message for failure in starting a BW process chain
startBwProcessChainFailure=Procesna veriga BW "{0}" se ni zagnala v najemniku SAP BW Bridge.
#XMSG: Task log message for failure in getting the status of a BW process chain
getBwProcessChainStatusFailure=Naloga ni bila uspešna, ker nismo mogli pridobiti stanja procesne verige BW "{0}" zaradi težave pri vzpostavljanju povezave s SAP BW Bridge. Odprite aplikacijo "Povezave" in potrdite povezavo s SAP BW Bridge v prostoru "{1}". Če nimate dostopa do aplikacije "Povezave", se obrnite na skrbnika.
#XMSG: Task log message for failure in completing a BW process chain
completeBwProcessChainFailure=Procesna veriga BW "{0}" ni zaključena v najemniku SAP BW Bridge.
#XMSG: Task log message for the clickable text of the SAP BW Bridge Monitor link
bwBridgeMonitorLinkText=Ogled podrobnosti v SAP BW Bridge Monitorju
#XMSG warning message while retrieving the task log messages if the user does not have access to the SAP BW Bridge space or to the shared object's space
missingPrivilegeOnSpaceError=Pri priklicu sporočil je prišlo do težave ali pa nimate potrebnega dovoljenja za njihov ogled.

#Messages for API task log of the Data Integration Monitor
#XMSG: Task log message for displaying the API response
viewResponse=Odziv prejet za "{0}".
#XMSG: Task log message for empty ID in the API response to retrieve the status
emptyJobId=ID ni bil vrnjen v telesu odziva za pot JSON: "{0}".
#XMSG: Task log message for empty success value in the API response
emptySuccessValue=Vrednost o uspehu ni bila vrnjena v telesu odziva za pot JSON: "{0}".
#XMSG: Task log message for empty error value in the API response
emptyErrorValue=Vrednost o napaki ni bila vrnjena v telesu odziva za pot JSON: "{0}".
#XMSG: Task log message for none of the success/error conditions matching for the values in the API response
conditionsNotFulfilled=Nobeden od navedenih pogojev indikatorja uspeha ali napake se ne ujema z vrednostmi v odzivu.
#XMSG: Task log message for the API response status being unsuccessful
statusNotOk=Odgovor za "{1}" je vrnil kodo neuspešnega statusa: {0}.
#XMSG: Task log message for the API response body being empty
emptyResponseBody=Telo odziva je prazno.
#XMSG: Task log message for the API response location header being invalid
emptyLocationHeader=Glava lokacije v odzivu je prazna.
#XMSG: Task log message for failure in retrieving the status of the invoked API
getStatusFailure=Statusa pozvanega vmesnika API ni mogoče priklicati.
#XMSG: Task log message for failure in completing the API task
completionFailure=Naloge API "{0}" ni mogoče dokončati.
#XMSG: Task log message for a successful API task completion
apiCompleted=Naloga API "{0}" se je uspešno dokončala.
#XMSG: Task log message for an API task with an invalid configuration
invalidConfiguration=Konfiguracija naloge API "{0}" ni veljavna. Preverite konfiguracijo in poskusite znova.
#XMSG: Task log message for the API task being canceled
cancelStart=Zahtevan je preklic naloge API "{0}" z logId {1}.
#XMSG: Task log message for the cancelation of the an API task which is no longer running and cannot be canceled
unableToCancel=Naloga "{0}" z logId {1} se več ne izvaja in je ni mogoče preklicati.
#XMSG: Task log message when starting an API task. It should carry the name of the API task.
apiRunStart=Zaganjanje naloge API "{0}".
#XMSG: Task log message when the task preparation is taking too long and times out
preparationTimeout=Priprava naloge traja predolgo, časovna omejitev je potekla.
#XMSG: Task log message when a task failed because a cancel task was triggered
apiRunCancelled=Nalogo izvajanja z logId {0} je preklicala naloga {1}.
#XMSG: Task log message when a cancel task failed to cancel a run task
cancelFailure=Naloga preklica z logId {1} ni preklicala naloge preklica z logId {0}.
#XMSG: Task log message when configuration of the API task is too large
apiTaskconfigurationSizeLimitError=Naloga API {0} se ne more začeti, ker velikost njene konfiguracije naloge presega največjo dovoljeno velikost 100 kibibajtov (KiB).
#XMSG: Task log message for failure in completing the Notification task
notificationTaskCompletionFailure=Naloga obvestila "{0}" se ni uspela dokončati.
#XMSG: Task log message when starting a Notification task.
notificationTaskRunStart=Naloga obvestila "{0}" se začenja.
#XMSG: Task log message for a successful Notification task completion
notificationTaskCompletionSuccess=Naloga obvestila "{0}" se je dokončala.
#XFLD: Label for frequency column
everyLabel=Vsakih
#XFLD: Plural Recurrence text for Hour
hoursLabel=ur
#XFLD: Plural Recurrence text for Day
daysLabel=dni
#XFLD: Plural Recurrence text for Month
monthsLabel=mesecev
#XFLD: Plural Recurrence text for Minutes
minutesLabel=minut
