

#XFLD: Task Chain header
headerTxt=Cadeias de tarefas ({0})
#XFLD: Task Chain header
headerTxtNew=Cadeias de tarefas ({0})
#XTXT: Text for Schedule label
scheduleTxt=Agendar
#XFLD: Text for Create schedule button
createScheduleTxt=Criar agenda
#XFLD: Text for edit schedule
editScheduleTxt=Editar agenda
#XLFD: Text for delete schedule
deleteScheduleTxt=Eliminar agenda
#XTXT: text for refresh button label
refrestTxt=Atualizar
#XTXT: Text for Completed status
completedTxt=Concluído
#XTX: Text for Running status
runningTxt=Em execução
#XTX: Text for failed status
failedTxt=Falhado
#XTX: Text for stopped status
stoppedTxt=Parado
#XTX: Text for stopping status
stoppingTxt=A parar
#XFLD: Header for Chain name
chainNameLabel=Nome de cadeia de tarefas
#XFLD: Header for Chain name
chainNameLabelBus=Nome comercial
#XFLD: Header for Chain name
chainNameLabelBusNew=Objeto (nome comercial)
#XFLD: Header for Chain name
chainNameLabelTech=Nome técnico
#XFLD: Header for Chain name
chainNameLabelTechNew=Objeto (nome técnico)
#XFLD: Last Run Status label
lastRunStatuslabel=Estado da última execução
#XFLD: Last Run Status label
lastRunStatuslabelNew=Estado
#XFLD: Frequency Label
frequencyLabel=Frequência
#XFLD: Frequency Label
frequencyLabelNew=Frequência agendada
#XFLD: Duration label
durationLabel=Duração
#XFLD: Duration label
durationLabelNew=Duração da última execução
#XFLD: Run Start label
runStartLabel=Início da última execução
#XFLD: Run end label
runEndLabel=Fim da última execução
#XFLD: Next Run label
nextRunlabel=Próxima execução
#XFLD: Next Run label
nextRunlabelNew=Execução seguinte agendada
#XTXT: Task chain logs link tooltip text
taskChainLogsTxt=Detalhes de registo de cadeia de tarefas
#XTXT: Task chain logs link tooltip text
taskChainLogsTxtNew=Detalhes
#XTXT: Scheduled text
scheduledTxt=Agendado
#XTXT: Paused text
pausedTxt=Interrompido
#XTXT: Execute button label
runLabel=Executar
#XTXT: Execute button label
runLabelNew=Iniciar execução
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Executar a cadeia de tarefas
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=A execução da cadeia de tarefas foi iniciada.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=A execução da cadeia de tarefas foi iniciada para {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Falha ao executar a cadeia de tarefas.
#XFLD: Label for schedule owner column
txtScheduleOwner=Proprietário do agendamento
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Mostra quem criou o agendamento
#XMSG: Task log message for start chain
startChain=A iniciar execução de cadeia de tarefas.
#XMSG: Task log message for load chain from repository
loadChainFromRepository=Cadeia de tarefas carregada e inicializada.
#XMSG: Task log message started task
taskStarted=A tarefa {0} foi iniciada.
#XMSG: Task log message for finished task
taskFinished=A tarefa {0} terminou com o estado {1}.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=A carregar a cadeia de tarefas e a preparar a execução de um total de {0} tarefas que fazem parte desta cadeia.
#XMSG: Task log message for starting a subtask
chainStartSubtask=A acionar a tarefa {0} para execução. ID da tarefa = {1}.
#XMSG: Task log message for finishing a subtask
chainFinishSubtask=A tarefa {0} foi concluída com o estado {1}.
#XMSG: Task log message for indicating chain success
chainCompleted=Todas as {0} tarefas foram concluídas. O estado da cadeia de tarefas está definido como concluído.
#XMSG: Task log message for indicating chain failure
chainFailed=Num total de {0} tarefas, {1} tarefas podem estar concluídas e {2} tarefas falhadas. O estado da cadeia de tarefas falhou.
#XMSG: Task log message for indicating chain cancelation
chainCanceled=O cancelamento da execução da cadeia de tarefas foi iniciado. O ID do registo da tarefa de cancelamento é {0}.
#XMSG: Task log message for indicating chain cancelation in the cancel task
chainCanceling=A cancelar a cadeia {0}.
#XMSG: Task log message for general chain runtime error
chainError=Ocorreu um erro inesperado durante a execução da cadeia de tarefas.
#XMSG: Task log message for starting the overwrite check.
chainOverwriteStart=A verificar se a cadeia de tarefas com ID {0} foi concluída.
#XMSG: Task log message indicating the chain is too old.
chainOverwriteTooOld=Não foi possível encontrar nenhuma tarefa a executar para a cadeia {0}. A execução tem {1} minutos. O estado da cadeia de tarefa está definida como falhado.
#XMSG: Task log message indicating the chain was created too recently.
chainOverwriteTooRecent=Não foi possível encontrar nenhuma tarefa a executar para a cadeia {0}. A execução tem {1} minutos. O estado da cadeia é Ainda está em execução.
#XMSG: Task log message indicating a recent heartbeat was found.
chainOverwriteHeartbeatAlive=A cadeia de tarefas {0} ainda está em execução. Concluído: {1}, Em execução: {2}, Falhado: {3}, Acionado: {4}, Não acionado: {5}.
#XMSG: Task log message for a chain that is not running anymore but not everything was triggered.
chainOverwriteNotAllTriggered=Ocorreu um erro ao executar a cadeia de tarefas {0} e não foi possível acionar todas as tarefas. Concluído: {1}, Em execução: {2}, Falhado: {3}, Acionado: {4}, Não acionado: {5}. Esta tarefa foi substituída.
#XMSG: Task log message for a chain that contains a failed subtask.
chainOverwriteAnySubtaskFailed=Cadeia de tarefa {0} concluída. Uma tarefa da cadeia falhou, a definir a cadeia como falhada.
#XMSG: Task log message could not determine status.
chainOverwriteNotDetermined=Um erro inesperado impede o acesso ao estado da tarefa. Tente novamente e contate o seu suporte SAP se o erro persistir.
#XMSG: Task log message could not take over
failedTakeover=Falha ao assumir tarefa existente.
#XMSG: Task log parallel check error
parallelCheckError=A tarefa não pode ser processada porque outra tarefa está em execução e está a bloquear esta tarefa.
#XMSG: Task log parallel task runnig error
parallelTaskRunning=Uma tarefa em conflito já está em execução.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Estado {0} durante execução com ID de correlação {1}.
#XMSG: Task log message successful takeover
successTakeover=Foi necessário libertar o bloqueio restante. O novo bloqueio para esta tarefa está definido.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=O bloqueio desta tarefa foi assumido por outra tarefa.
#XMSG: Schedule created alert message
createScheduleSuccess=Agenda criada
#XMSG: Task log message for taskFinishedAt
taskFinishedAt=A tarefa {0} foi concluída às {2} com o estado {1}.
#XMSG: Placeholder symbol for empty columns
emptyCol=--
#XMSG: Paused Text
txtExpired=Expirado
#XMSG: Update task schedule success Toast message
updateScheduleSuccess=Agenda atualizada
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Agenda eliminada.
#XMSG: Task chain might have children error text
taskChainNoChildErrorText=A cadeia de tarefas pode ter subordinados que não são exibidos, porque a tarefa falhou antes de ser possível gerar o plano.
#XMSG: Task chain repair recent failed run label
retryRunLabel=Repetir a última execução
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=A execução da repetição da cadeia de tarefas foi iniciada
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=A execução da repetição da cadeia de tarefas falhou
#XMSG: chain repair message
chainRetried=Repetição da cadeia de tarefas acionada pelo utilizador {0}
#XMSG: chain not found during run
chainNotFoundDuringRun=Não foi possível encontrar a cadeia de tarefas {0}. Verifique se ela existe através do gerador de dados e certifique-se de que a cadeia foi implementada.
#XMSG: chain is not DAG
chainNotDag=Não é possível inciar a cadeia de tarefas {0}. A estrutura é inválida. Verifique a cadeia de tarefas no gerador de dados.
#XMSG: chain has not valid parameters
notValidParameters=Não é possível inciar a cadeia de tarefas {0}. Um ou vários dos seus parâmetros são inválidos. Verifique a cadeia de tarefas no gerador de dados.
#XMSG: Configuration parameter value size exceeds the maximum allowed limit
configurationSizeLimitError=Impossível iniciar cadeia de tarefas {0}. O tamanho da configuração de uma tarefa na cadeia excede o tamanho máximo permitido de 100 kibibytes (KiB).
#XMSG: Task {0} has input parameters
taskHasInputParameters=A tarefa {0} tem parâmetros de entrada.
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Uma tarefa em conflito já está em execução
#XMSG: error message for reading data from backend
txtReadBackendError=Parece que ocorreu um erro ao ler do back-end.
##XMSG: error message for admission control rejection
admissionControlError=A tarefa falhou devido a uma rejeição do controlo de admissão SAP HANA.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Atribuir-me o agendamento
#XBUT: Pause schedule menu label
pauseScheduleLabel=Interromper o agendamento
#XBUT: Resume schedule menu label
resumeScheduleLabel=Retomar o agendamento
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Ocorreu um erro ao remover os agendamentos.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Ocorreu um erro ao atribuir os agendamentos.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Ocorreu um erro ao interromper os agendamentos.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Ocorreu um erro ao retomar os agendamentos.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=A eliminar {0} agendamentos
#XMSG: Message for starting mass assign of schedules
massAssignStarted=A alterar o proprietário de {0} agendamentos
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=A interromper {0} agendamentos
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=A retomar {0} agendamentos
#XBUT: Select Columns Button
selectColumnsBtn=Selecionar colunas
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Parece que ocorreu um erro com a nova tentativa da última execução, pois a execução da tarefa anterior falhou antes de o plano poder ser gerado.
#XFLD: Refresh tooltip
TEXT_REFRESH=Atualizar
#XFLD: Select Columns tooltip
text_selectColumns=Selecionar colunas

#Messages for BW Process Chain task log of the Data Integration Monitor
#XMSG: Task log message for successfully starting a BW process chain
startBwProcessChainSuccess=A cadeia de processos "{0}" foi iniciada com sucesso no inquilino SAP BW Bridge.
#XMSG: Task log message for skipping a BW process chain execution
bwProcessChainSkipped=A cadeia de processos BW "{0}" foi ignorada devido à indisponibilidade de novos dados ou à falha de uma execução anterior.
#XMSG: Task log message for failure in starting a BW process chain
startBwProcessChainFailure=Falha ao iniciar a cadeia de processos BW "{0}" no inquilino SAP BW Bridge.
#XMSG: Task log message for failure in getting the status of a BW process chain
getBwProcessChainStatusFailure=A tarefa falhou uma vez que não foi possível obter o estado da cadeia de processos BW "{0}" devido a um problema ao estabelecer uma ligação ao SAP BW Bridge. Abra a aplicação "Ligações" e valide a ligação do SAP BW Bridge no espaço "{1}". Se não tiver acesso à aplicação "Ligações", contacte o seu administrador.
#XMSG: Task log message for failure in completing a BW process chain
completeBwProcessChainFailure=Falha ao concluir a cadeia de processos BW "{0}" no inquilino SAP BW Bridge.
#XMSG: Task log message for the clickable text of the SAP BW Bridge Monitor link
bwBridgeMonitorLinkText=Ver detalhes no monitor SAP BW Bridge
#XMSG warning message while retrieving the task log messages if the user does not have access to the SAP BW Bridge space or to the shared object's space
missingPrivilegeOnSpaceError=Ocorreu um problema ao recuperar as mensagens ou não tem a permissão necessária para as ver.

#Messages for API task log of the Data Integration Monitor
#XMSG: Task log message for displaying the API response
viewResponse=Resposta recebida para "{0}".
#XMSG: Task log message for empty ID in the API response to retrieve the status
emptyJobId=Nenhum ID devolvido no corpo da resposta para o caminho JSON: "{0}".
#XMSG: Task log message for empty success value in the API response
emptySuccessValue=Não foi devolvido nenhum valor de êxito no corpo da resposta para o caminho JSON: "{0}".
#XMSG: Task log message for empty error value in the API response
emptyErrorValue=Não foi devolvido nenhum valor de erro no corpo da resposta para o caminho JSON: "{0}".
#XMSG: Task log message for none of the success/error conditions matching for the values in the API response
conditionsNotFulfilled=Nenhuma das condições de indicador de êxito ou erro corresponde aos valores na resposta.
#XMSG: Task log message for the API response status being unsuccessful
statusNotOk=A resposta para "{1}" devolveu um código de estado sem êxito: {0}.
#XMSG: Task log message for the API response body being empty
emptyResponseBody=O corpo da resposta estava vazio.
#XMSG: Task log message for the API response location header being invalid
emptyLocationHeader=O cabeçalho "location" na resposta estava vazio.
#XMSG: Task log message for failure in retrieving the status of the invoked API
getStatusFailure=Não foi possível recuperar o estado da API invocada.
#XMSG: Task log message for failure in completing the API task
completionFailure=Falha ao concluir a tarefa API "{0}".
#XMSG: Task log message for a successful API task completion
apiCompleted=A tarefa da API "{0}" foi concluída com êxito.
#XMSG: Task log message for an API task with an invalid configuration
invalidConfiguration=A configuração da tarefa da API "{0}" é inválida. Verifique a configuração e tente novamente.
#XMSG: Task log message for the API task being canceled
cancelStart=Pedido cancelamento de tarefa de API "{0}" com logId {1}.
#XMSG: Task log message for the cancelation of the an API task which is no longer running and cannot be canceled
unableToCancel=A tarefa de API "{0}" com logId {1} já não está em execução e não pode ser cancelada.
#XMSG: Task log message when starting an API task. It should carry the name of the API task.
apiRunStart=A iniciar tarefa de API "{0}".
#XMSG: Task log message when the task preparation is taking too long and times out
preparationTimeout=A preparação da tarefa está a demorar muito tempo e excedeu o tempo.
#XMSG: Task log message when a task failed because a cancel task was triggered
apiRunCancelled=A tarefa de execução com logId {0} foi cancelada pela tarefa de cancelamento {1}.
#XMSG: Task log message when a cancel task failed to cancel a run task
cancelFailure=A tarefa de cancelamento com logId {1} falhou no cancelamento da tarefa de execução com logId {0}.
#XMSG: Task log message when configuration of the API task is too large
apiTaskconfigurationSizeLimitError=A tarefa da API {0} não pode ser iniciada porque o tamanho da sua configuração de tarefa excede o tamanho máximo permitido de 100 kibibytes (KiB).
#XMSG: Task log message for failure in completing the Notification task
notificationTaskCompletionFailure=Falha ao concluir a tarefa de notificação "{0}".
#XMSG: Task log message when starting a Notification task.
notificationTaskRunStart=A iniciar a tarefa de notificação "{0}".
#XMSG: Task log message for a successful Notification task completion
notificationTaskCompletionSuccess=A tarefa de notificação "{0}" foi concluída.
#XFLD: Label for frequency column
everyLabel=A cada
#XFLD: Plural Recurrence text for Hour
hoursLabel=Horas
#XFLD: Plural Recurrence text for Day
daysLabel=Dias
#XFLD: Plural Recurrence text for Month
monthsLabel=Meses
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minutos
