

#XFLD: Task Chain header
headerTxt=Tegumiahelad ({0})
#XFLD: Task Chain header
headerTxtNew=Tegumiahelad ({0})
#XTXT: Text for Schedule label
scheduleTxt=Graafik
#XFLD: Text for Create schedule button
createScheduleTxt=Loo graafik
#XFLD: Text for edit schedule
editScheduleTxt=Redigeeri graafikut
#XLFD: Text for delete schedule
deleteScheduleTxt=Kustuta graafik
#XTXT: text for refresh button label
refrestTxt=Värskenda
#XTXT: Text for Completed status
completedTxt=Lõpetatud
#XTX: Text for Running status
runningTxt=Töötab
#XTX: Text for failed status
failedTxt=Nurjunud
#XTX: Text for stopped status
stoppedTxt=Peatatud
#XTX: Text for stopping status
stoppingTxt=Peatamine
#XFLD: Header for Chain name
chainNameLabel=Tegumiahela nimi
#XFLD: Header for Chain name
chainNameLabelBus=Ärinimi
#XFLD: Header for Chain name
chainNameLabelBusNew=Objekt (ärinimi)
#XFLD: Header for Chain name
chainNameLabelTech=Tehniline nimi
#XFLD: Header for Chain name
chainNameLabelTechNew=Objekt (tehniline nimi)
#XFLD: Last Run Status label
lastRunStatuslabel=Viimase käituse olek
#XFLD: Last Run Status label
lastRunStatuslabelNew=Olek
#XFLD: Frequency Label
frequencyLabel=Sagedus
#XFLD: Frequency Label
frequencyLabelNew=Ajastatud sagedus
#XFLD: Duration label
durationLabel=Kestus
#XFLD: Duration label
durationLabelNew=Viimase käituse kestus
#XFLD: Run Start label
runStartLabel=Viimase käituse algus
#XFLD: Run end label
runEndLabel=Viimase käituse lõpp
#XFLD: Next Run label
nextRunlabel=Järgmine käitus
#XFLD: Next Run label
nextRunlabelNew=Järgmine ajastatud käitus
#XTXT: Task chain logs link tooltip text
taskChainLogsTxt=Tegumiahela logi üksikasjad
#XTXT: Task chain logs link tooltip text
taskChainLogsTxtNew=Üksikasjad
#XTXT: Scheduled text
scheduledTxt=Ajastatud
#XTXT: Paused text
pausedTxt=Peatatud
#XTXT: Execute button label
runLabel=Käivita
#XTXT: Execute button label
runLabelNew=Käivita käitus
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Käivita tegumiahel
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Tegumiahela käitus on käivitatud.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Üksuse „{0}“ tegumiahela käitus on käivitatud
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Tegumiahela käivitamine nurjus.
#XFLD: Label for schedule owner column
txtScheduleOwner=Ajakava omanik
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Näitab ajakava loojat
#XMSG: Task log message for start chain
startChain=Tegumiahela käituse käivitamine.
#XMSG: Task log message for load chain from repository
loadChainFromRepository=Tegumiahel on laaditud ja käivitatud.
#XMSG: Task log message started task
taskStarted=Tegum {0} on käivitatud.
#XMSG: Task log message for finished task
taskFinished=Tegum {0} lõppes olekuga {1}.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Tegumiahela laadimine ja {0} tegumi käituse ettevalmistamine, mis on selle ahela osa.
#XMSG: Task log message for starting a subtask
chainStartSubtask=Tegumi {0} käivitamine. Tegumi ID = {1}.
#XMSG: Task log message for finishing a subtask
chainFinishSubtask=Tegum {0} jõudis lõpule olekuga {1}.
#XMSG: Task log message for indicating chain success
chainCompleted=Kõik {0} tegumit on lõpule jõudnud. Tegumiahela olekuks on määratud Lõpetatud.
#XMSG: Task log message for indicating chain failure
chainFailed={0} Tegumist peab {1} tegumit olema lõpetatud ja {2} tegumit nurjunud. Tegumiahela olekuks on määratud Nurjunud.
#XMSG: Task log message for indicating chain cancelation
chainCanceled=Tegumiahela käituse tühistamine on käivitatud. Tühistamistegumi logi ID on {0}.
#XMSG: Task log message for indicating chain cancelation in the cancel task
chainCanceling=Ahela {0} tühistamine.
#XMSG: Task log message for general chain runtime error
chainError=Tegumiahela käivitamisel ilmnes ootamatu tõrge.
#XMSG: Task log message for starting the overwrite check.
chainOverwriteStart=Kontrollimine, kas tegumiahel ID-ga {0} jõudis lõpule.
#XMSG: Task log message indicating the chain is too old.
chainOverwriteTooOld=Ahela {0} käivitatavaid tegumeid ei leitud. Käitus on kestnud {1} minutit. Tegumiahela olekuks on määratud Nurjunud.
#XMSG: Task log message indicating the chain was created too recently.
chainOverwriteTooRecent=Ahela {0} käivitatavaid tegumeid ei leitud. Käitus on kestnud {1} minutit. Tegumiahela käitus on veel pooleli.
#XMSG: Task log message indicating a recent heartbeat was found.
chainOverwriteHeartbeatAlive=Tegumiahelat {0} käitatakse endiselt. Lõpetatud: {1}, käituses: {2}, nurjunud: {3}, käivitatud: {4}, käivitamata: {5}.
#XMSG: Task log message for a chain that is not running anymore but not everything was triggered.
chainOverwriteNotAllTriggered=Tegumiahela {0} käivitamisel ilmnes tõrge ja kõiki tegumeid ei saanud käivitada. Lõpetatud: {1}, käituses: {2}, nurjunud: {3}, käivitatud: {4}, käivitamata: {5}. See tegumiahel on üle kirjutatud.
#XMSG: Task log message for a chain that contains a failed subtask.
chainOverwriteAnySubtaskFailed=Tegumiahel {0} jõudis lõpule. Üks ahela tegum nurjus, määrated ahela olekuks Nurjunud.
#XMSG: Task log message could not determine status.
chainOverwriteNotDetermined=Ootamatu tõrge takistab meil tegumi olekule juurdepääsemist. Proovige uuesti ja kui tõrge ei kao, pöörduge oma SAP-i toe poole.
#XMSG: Task log message could not take over
failedTakeover=Olemasoleva tegumi ülevõtmine nurjus.
#XMSG: Task log parallel check error
parallelCheckError=Tegumit ei saa töödelda, kuna teine tegum töötab ja blokeerib juba seda tegumit.
#XMSG: Task log parallel task runnig error
parallelTaskRunning=Vastuoluline tegum juba töötab.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Olek {0} korrelatsiooni Id-ga {1} käituse ajal.
#XMSG: Task log message successful takeover
successTakeover=Ülejäänud lukk tuli vabastada. Sellele tegumile on määratud uus lukk.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Selle tegumi luku võttis üle teine tegum.
#XMSG: Schedule created alert message
createScheduleSuccess=Graafik on loodud
#XMSG: Task log message for taskFinishedAt
taskFinishedAt=Tegum {0} jõudis lõpule {2} olekuga {1}.
#XMSG: Placeholder symbol for empty columns
emptyCol=--
#XMSG: Paused Text
txtExpired=Aegunud
#XMSG: Update task schedule success Toast message
updateScheduleSuccess=Graafik on uuendatud
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Graafik on kustutatud.
#XMSG: Task chain might have children error text
taskChainNoChildErrorText=Tegumiahelal võivad olla alamliikmed, mida ei kuvata, kuna ülesanne nurjus enne, kui plaani sai hakata genereerima.
#XMSG: Task chain repair recent failed run label
retryRunLabel=Proovi viimast käitust uuesti
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Tegumiahela käituse korduskatse on käivitatud
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Tegumiahela käituse korduskatse on nurjunud
#XMSG: chain repair message
chainRetried=Kasutaja {0} on käivitanud tegumiahela korduskatse
#XMSG: chain not found during run
chainNotFoundDuringRun=Tegumiahelat {0} ei leitud. Kontrollige andmemudelite haldamise rakendusest, kas see on olemas, ja veenduge, et ahel oleks juurutatud.
#XMSG: chain is not DAG
chainNotDag=Tegumiahelat {0} ei saa käivitada. Selle struktuur ei sobi. Kontrollige tegumiahelat andmemudelite haldamise rakenduses.
#XMSG: chain has not valid parameters
notValidParameters=Tegumiahelat {0} ei saa käivitada. Vähemalt üks selle parameetritest ei sobi. Kontrollige tegumiahelat andmemudelite haldamise rakenduses.
#XMSG: Configuration parameter value size exceeds the maximum allowed limit
configurationSizeLimitError=Tegumiahelat {0} ei saa käivitada. Tegumi konfiguratsiooni maht ahelas ületab mahu jaoks lubatud 100 kibibaidi (KiB) piirmäära.
#XMSG: Task {0} has input parameters
taskHasInputParameters=Ülesandel {0} on sisendparameetrid.
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Vastuoluline tegum juba töötab
#XMSG: error message for reading data from backend
txtReadBackendError=Näib, et tagasüsteemist lugemisel ilmnes tõrge.
##XMSG: error message for admission control rejection
admissionControlError=Tegum nurjus SAP HANA vastuvõtukontrolli tagasilükkamise tõttu.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Määra ajakava mulle
#XBUT: Pause schedule menu label
pauseScheduleLabel=Peata ajakava
#XBUT: Resume schedule menu label
resumeScheduleLabel=Jätka ajakava
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Ajakavade eemaldamisel ilmnes tõrge.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Ajakavade määramisel ilmnes tõrge.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Ajakavade peatamisel ilmnes tõrge.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Ajakavade jätkamisel ilmnes tõrge.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted={0} ajakava kustutamine
#XMSG: Message for starting mass assign of schedules
massAssignStarted={0} ajakava omaniku muutmine
#XMSG: Message for starting mass pausing of schedules
massPauseStarted={0} ajakava peatamine
#XMSG: Message for starting mass resuming of schedules
massResumeStarted={0} ajakava jätkamine
#XBUT: Select Columns Button
selectColumnsBtn=Vali veerud
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Näib, et viimase käituse korduskatsel ilmnes tõrge, kuna eelmine ülesandekäitud nurjus, enne kui plaani sai genereerida.
#XFLD: Refresh tooltip
TEXT_REFRESH=Värskenda
#XFLD: Select Columns tooltip
text_selectColumns=Vali veerud

#Messages for BW Process Chain task log of the Data Integration Monitor
#XMSG: Task log message for successfully starting a BW process chain
startBwProcessChainSuccess=BW protsessiahel „{0}“ on SAP BW Bridge’i rentnikus käivitatud.
#XMSG: Task log message for skipping a BW process chain execution
bwProcessChainSkipped=BW protsessiahel „{0}“ on uute andmete kättesaamatuse või mõne varasema käituse nurjumise tõttu vahele jäetud.
#XMSG: Task log message for failure in starting a BW process chain
startBwProcessChainFailure=BW protsessiahela „{0}“ käivitumine on SAP BW Bridge’i rentnikus nurjunud.
#XMSG: Task log message for failure in getting the status of a BW process chain
getBwProcessChainStatusFailure=Tegum nurjus, kuna me ei saanud BW protsessiahela „{0}“ olekut tuua: SAP BW Bridge’iga ühenduse loomisel tekkis probleem. Avage rakendus „Ühendused“ ja valideerige SAP BW Bridge’i ühendus ruumis „{1}“. Kui teil pole rakendusele „Ühendused“ juurdepääsu, pöörduge oma administraatori poole.
#XMSG: Task log message for failure in completing a BW process chain
completeBwProcessChainFailure=BW protsessiahela „{0}“ lõpuleviimine on SAP BW Bridge’i rentnikus nurjunud.
#XMSG: Task log message for the clickable text of the SAP BW Bridge Monitor link
bwBridgeMonitorLinkText=Kuva üksikasjad SAP BW silla jälgimisprogrammis
#XMSG warning message while retrieving the task log messages if the user does not have access to the SAP BW Bridge space or to the shared object's space
missingPrivilegeOnSpaceError=Sõnumite toomisel ilmnes probleem või teil pole nende vaatamiseks vajalikku õigust.

#Messages for API task log of the Data Integration Monitor
#XMSG: Task log message for displaying the API response
viewResponse=Vastus on vastu võetud: {0}.
#XMSG: Task log message for empty ID in the API response to retrieve the status
emptyJobId=Järgmise JSON-i tee vastuse sisuosas ei tagastatud ID-d: {0}.
#XMSG: Task log message for empty success value in the API response
emptySuccessValue=Järgmise JSON-i tee vastuse sisuosas ei tagastatud õnnestumisväärtust: {0}.
#XMSG: Task log message for empty error value in the API response
emptyErrorValue=Järgmise JSON-i tee vastuse sisuosas ei tagastatud tõrkeväärtust: {0}.
#XMSG: Task log message for none of the success/error conditions matching for the values in the API response
conditionsNotFulfilled=Ükski määratud õnnestumis- või tõrketunnuse tingimus ei vasta vastuse väärtustele.
#XMSG: Task log message for the API response status being unsuccessful
statusNotOk={1}: vastus tagastas nurjunud oleku koodi: {0}.
#XMSG: Task log message for the API response body being empty
emptyResponseBody=Vastuse sisuosa oli tühi.
#XMSG: Task log message for the API response location header being invalid
emptyLocationHeader=Vastuse asukohapäis oli tühi.
#XMSG: Task log message for failure in retrieving the status of the invoked API
getStatusFailure=Aktiveeritud API olekut ei saanud tuua.
#XMSG: Task log message for failure in completing the API task
completionFailure=API toimingut „{0}“ ei viidud lõpule.
#XMSG: Task log message for a successful API task completion
apiCompleted=API toiming „{0}“ on lõpule viidud.
#XMSG: Task log message for an API task with an invalid configuration
invalidConfiguration=API toimingu „{0}“ konfiguratsioon ei sobi. Kontrollige konfiguratsiooni ja proovige uuesti.
#XMSG: Task log message for the API task being canceled
cancelStart=Taotletud on API toimingu „{0}“ (logId {1}) tühistamist.
#XMSG: Task log message for the cancelation of the an API task which is no longer running and cannot be canceled
unableToCancel=API toiming „{0}“ (logId {1}) ei tööta enam ja seda ei saa tühistada.
#XMSG: Task log message when starting an API task. It should carry the name of the API task.
apiRunStart=API toiming „{0}“ käivitatakse.
#XMSG: Task log message when the task preparation is taking too long and times out
preparationTimeout=Toimingu ettevalmistamine võtab liiga palju aega ja on aegunud.
#XMSG: Task log message when a task failed because a cancel task was triggered
apiRunCancelled=Käitustoiming (logId {0}) on tühistatud tühistamistoiminguga {1}.
#XMSG: Task log message when a cancel task failed to cancel a run task
cancelFailure=Tühistamistoiming (logId {1}) ei saanud tühistada käitustoimingut (logId {0}).
#XMSG: Task log message when configuration of the API task is too large
apiTaskconfigurationSizeLimitError=API toimingut {0} ei saa käivitada, kuna selle tegumikonfiguratsiooni maht ahelas ületab lubatud 100 kibibaidi (KiB) piirmäära.
#XMSG: Task log message for failure in completing the Notification task
notificationTaskCompletionFailure=Teavitustoimingut „{0}“ ei viidud lõpule.
#XMSG: Task log message when starting a Notification task.
notificationTaskRunStart=Teavitustoiming „{0}“ käivitatakse.
#XMSG: Task log message for a successful Notification task completion
notificationTaskCompletionSuccess=Teavitustoiming „{0}“ on lõpule viidud.
#XFLD: Label for frequency column
everyLabel=Iga
#XFLD: Plural Recurrence text for Hour
hoursLabel=tunni järel
#XFLD: Plural Recurrence text for Day
daysLabel=päeva järel
#XFLD: Plural Recurrence text for Month
monthsLabel=kuu järel
#XFLD: Plural Recurrence text for Minutes
minutesLabel=minuti järel
