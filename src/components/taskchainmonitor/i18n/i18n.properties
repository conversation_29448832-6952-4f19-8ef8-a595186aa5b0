

#XFLD: Task Chain header
headerTxt=Task Chains({0})
#XFLD: Task Chain header
headerTxtNew=Task Chains ({0})
#XTXT: Text for Schedule label
scheduleTxt=Schedule
#XFLD: Text for Create schedule button
createScheduleTxt=Create Schedule
#XFLD: Text for edit schedule
editScheduleTxt=Edit Schedule
#XLFD: Text for delete schedule
deleteScheduleTxt=Delete Schedule
#XTXT: text for refresh button label
refrestTxt=Refresh
#XTXT: Text for Completed status
completedTxt=Completed
#XTX: Text for Running status
runningTxt=Running
#XTX: Text for failed status
failedTxt=Failed
#XTX: Text for stopped status
stoppedTxt=Stopped
#XTX: Text for stopping status
stoppingTxt=Stopping
#XFLD: Header for Chain name
chainNameLabel=Task Chain Name
#XFLD: Header for Chain name
chainNameLabelBus=Business Name
#XFLD: Header for Chain name
chainNameLabelBusNew=Object (Business Name)
#XFLD: Header for Chain name
chainNameLabelTech=Technical Name
#XFLD: Header for Chain name
chainNameLabelTechNew=Object (Technical Name)
#XFLD: Last Run Status label
lastRunStatuslabel=Last Run Status
#XFLD: Last Run Status label
lastRunStatuslabelNew=Status
#XFLD: Frequency Label
frequencyLabel=Frequency
#XFLD: Frequency Label
frequencyLabelNew=Scheduled Frequency
#XFLD: Duration label
durationLabel=Duration
#XFLD: Duration label
durationLabelNew=Last Run Duration
#XFLD: Run Start label
runStartLabel=Last Run Start
#XFLD: Run end label
runEndLabel=Last Run End
#XFLD: Next Run label
nextRunlabel=Next Run
#XFLD: Next Run label
nextRunlabelNew=Scheduled Next Run
#XTXT: Task chain logs link tooltip text
taskChainLogsTxt=Task Chain Log Details
#XTXT: Task chain logs link tooltip text
taskChainLogsTxtNew=Details
#XTXT: Scheduled text
scheduledTxt=Scheduled
#XTXT: Paused text
pausedTxt=Paused
#XTXT: Execute button label
runLabel=Run
#XTXT: Execute button label
runLabelNew=Start Run
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Run the task chain
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Task chain run has started.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Task chain run has started for {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Failed to run the task chain.
#XFLD: Label for schedule owner column
txtScheduleOwner=Schedule Owner
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Shows who created the schedule
#XMSG: Task log message for start chain
startChain=Starting task chain run.
#XMSG: Task log message for load chain from repository
loadChainFromRepository=Task chain loaded and initialized.
#XMSG: Task log message started task
taskStarted=Task {0} has started.
#XMSG: Task log message for finished task
taskFinished=The task {0} ended with status {1}.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Loading task chain and preparing to run a total of {0} tasks that are part of this chain.
#XMSG: Task log message for starting a subtask
chainStartSubtask=Triggering task {0} to run. Task id = {1}.
#XMSG: Task log message for finishing a subtask
chainFinishSubtask=Task {0} finished with status {1}.
#XMSG: Task log message for indicating chain success
chainCompleted=All {0} tasks are completed. The task chain status is set to completed.
#XMSG: Task log message for indicating chain failure
chainFailed=On a total of {0} tasks, {1} tasks could be completed and {2} tasks failed. The task chain status is set to failed.
#XMSG: Task log message for indicating chain cancelation
chainCanceled=Task chain run cancelation has started. Log id of cancel task is {0}.
#XMSG: Task log message for indicating chain cancelation in the cancel task
chainCanceling=Canceling chain {0}.
#XMSG: Task log message for general chain runtime error
chainError=An unexpected error occurred while running the task chain.
#XMSG: Task log message for starting the overwrite check.
chainOverwriteStart=Checking whether task chain with id {0} has finished.
#XMSG: Task log message indicating the chain is too old.
chainOverwriteTooOld=No tasks to run for chain {0} could be found. Run is {1} minutes old. The task chain status is set to failed.
#XMSG: Task log message indicating the chain was created too recently.
chainOverwriteTooRecent=No tasks to run for chain {0} could be found. Run is {1} minutes old. The task chain is still running.
#XMSG: Task log message indicating a recent heartbeat was found.
chainOverwriteHeartbeatAlive=Task chain {0} is still running. Completed: {1}, Running: {2}, Failed: {3}, Triggered: {4}, Not triggered: {5}.
#XMSG: Task log message for a chain that is not running anymore but not everything was triggered.
chainOverwriteNotAllTriggered=An error occurred while running task chain {0}, and not all tasks could be triggered. Completed: {1}, Running: {2}, Failed: {3}, Triggered: {4}, Not triggered: {5}. This task chain has been overwritten.
#XMSG: Task log message for a chain that contains a failed subtask.
chainOverwriteAnySubtaskFailed=Task chain {0} finished. One task of the chain failed, setting chain to failed.
#XMSG: Task log message could not determine status.
chainOverwriteNotDetermined=An unexpected error prevents us from accessing the status of the task. Try again and contact your SAP Support if the error persists.
#XMSG: Task log message could not take over
failedTakeover=Failed to take over existing task.
#XMSG: Task log parallel check error
parallelCheckError=The task can’t be processed because another task is running and already blocking this task.
#XMSG: Task log parallel task runnig error
parallelTaskRunning=A conflicting task is already running.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Status {0} during run with correlation ID {1}.
#XMSG: Task log message successful takeover
successTakeover=Left over lock had to be released. The new lock for this task is set.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Lock of this task was taken over by another task.
#XMSG: Schedule created alert message
createScheduleSuccess=Schedule created
#XMSG: Task log message for taskFinishedAt
taskFinishedAt=Task {0} finished at {2} with status {1}.
#XMSG: Placeholder symbol for empty columns
emptyCol=--
#XMSG: Paused Text
txtExpired=Expired
#XMSG: Update task schedule success Toast message
updateScheduleSuccess=Schedule updated
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Schedule deleted.
#XMSG: Task chain might have children error text
taskChainNoChildErrorText=The task chain might have children which are not shown because the task failed before the plan could be generated.
#XMSG: Task chain repair recent failed run label
retryRunLabel=Retry Latest Run
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Task chain retry run has started
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Task chain retry run has failed
#XMSG: chain repair message
chainRetried=Task chain retry triggered by user {0}
#XMSG: chain not found during run
chainNotFoundDuringRun=Could not find task chain {0}. Check if it exists via the Data Builder and ensure the chain is deployed.
#XMSG: chain is not DAG
chainNotDag=Cannot start task chain {0}. Its structure is invalid. Check the task chain in the Data Builder.
#XMSG: chain has not valid parameters
notValidParameters=Cannot start task chain {0}. One or more of its parameters are invalid. Check the task chain in the Data Builder.
#XMSG: Configuration parameter value size exceeds the maximum allowed limit
configurationSizeLimitError=Cannot start task chain {0}. The size of a task’s configuration in the chain exceeds the maximum allowed size of 100 kibibytes (KiB).
#XMSG: Task {0} has input parameters
taskHasInputParameters=Task {0} has input parameters.
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=A conflicting task is already running
#XMSG: error message for reading data from backend
txtReadBackendError=It looks like there was an error while reading from the back end.
##XMSG: error message for admission control rejection
admissionControlError=The task failed because of an SAP HANA Admission Control Rejection.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Assign Schedule to Me
#XBUT: Pause schedule menu label
pauseScheduleLabel=Pause Schedule
#XBUT: Resume schedule menu label
resumeScheduleLabel=Resume Schedule
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=An error occurred while removing schedules.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=An error occurred while assigning schedules.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=An error occurred while pausing schedules.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=An error occurred while resuming schedules.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Deleting {0} schedules
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Changing the owner of {0} schedules
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Pausing {0} schedules
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Resuming {0} schedules
#XBUT: Select Columns Button
selectColumnsBtn=Select Columns
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=It looks like there was an error with retry latest run as the previous task run failed before the plan could be generated.
#XFLD: Refresh tooltip
TEXT_REFRESH=Refresh
#XFLD: Select Columns tooltip
text_selectColumns=Select Columns

#Messages for BW Process Chain task log of the Data Integration Monitor
#XMSG: Task log message for successfully starting a BW process chain
startBwProcessChainSuccess=The BW process chain "{0}" has started successfully in the SAP BW Bridge tenant.
#XMSG: Task log message for skipping a BW process chain execution
bwProcessChainSkipped=The BW process chain "{0}" was skipped either due to the unavailability of new data or failure of a previous execution.
#XMSG: Task log message for failure in starting a BW process chain
startBwProcessChainFailure=The BW process chain "{0}" has failed to start in the SAP BW Bridge tenant.
#XMSG: Task log message for failure in getting the status of a BW process chain
getBwProcessChainStatusFailure=The task failed as we were unable to retrieve the status of the BW process chain "{0}" due to a problem establishing a connection to SAP BW Bridge. Open the "Connections" app and validate the SAP BW Bridge connection in the "{1}" space. If you do not have access to the "Connections" app, please contact your administrator.
#XMSG: Task log message for failure in completing a BW process chain
completeBwProcessChainFailure=The BW process chain "{0}" has failed to complete in the SAP BW Bridge tenant.
#XMSG: Task log message for the clickable text of the SAP BW Bridge Monitor link
bwBridgeMonitorLinkText=View details in SAP BW Bridge Monitor
#XMSG warning message while retrieving the task log messages if the user does not have access to the SAP BW Bridge space or to the shared object's space
missingPrivilegeOnSpaceError=There was a problem retrieving the messages, or you do not have necessary permission to view them.

#Messages for API task log of the Data Integration Monitor
#XMSG: Task log message for displaying the API response
viewResponse=Response received for "{0}".
#XMSG: Task log message for empty ID in the API response to retrieve the status
emptyJobId=No ID was returned in the response body for the JSON path: "{0}".
#XMSG: Task log message for empty success value in the API response
emptySuccessValue=No success value was returned in the response body for the JSON path: "{0}".
#XMSG: Task log message for empty error value in the API response
emptyErrorValue=No error value was returned in the response body for the JSON path: "{0}".
#XMSG: Task log message for none of the success/error conditions matching for the values in the API response
conditionsNotFulfilled=None of the specified success or error indicator conditions match the values in the response.
#XMSG: Task log message for the API response status being unsuccessful
statusNotOk=The response for "{1}" returned an unsuccessful status code: {0}.
#XMSG: Task log message for the API response body being empty
emptyResponseBody=The response body was empty.
#XMSG: Task log message for the API response location header being invalid
emptyLocationHeader=The location header in the response was empty.
#XMSG: Task log message for failure in retrieving the status of the invoked API
getStatusFailure=The status of the invoked API could not be retrieved.
#XMSG: Task log message for failure in completing the API task
completionFailure=The API task "{0}" has failed to complete.
#XMSG: Task log message for a successful API task completion
apiCompleted=The API task "{0}" has completed successfully.
#XMSG: Task log message for an API task with an invalid configuration
invalidConfiguration=The configuration of the API task "{0}" is invalid. Please check the configuration and try again.
#XMSG: Task log message for the API task being canceled
cancelStart=Requested cancelation of the API task "{0}" with logId {1}.
#XMSG: Task log message for the cancelation of the an API task which is no longer running and cannot be canceled
unableToCancel=The API task "{0}" with logId {1} is no longer running and cannot be canceled.
#XMSG: Task log message when starting an API task. It should carry the name of the API task.
apiRunStart=Starting the API task "{0}".
#XMSG: Task log message when the task preparation is taking too long and times out
preparationTimeout=The task preparation is taking too long and has timed out.
#XMSG: Task log message when a task failed because a cancel task was triggered
apiRunCancelled=The run task with logId {0} was canceled by cancel task {1}.
#XMSG: Task log message when a cancel task failed to cancel a run task
cancelFailure=The cancel task with logId {1} failed to cancel the run task with logId {0}.
#XMSG: Task log message when configuration of the API task is too large
apiTaskconfigurationSizeLimitError=The API task {0} can’t start because the size of its task configuration exceeds the maximum allowed size of 100 kibibytes (KiB).
#XMSG: Task log message for failure in completing the Notification task
notificationTaskCompletionFailure=The Notification task "{0}" has failed to complete.
#XMSG: Task log message when starting a Notification task.
notificationTaskRunStart=Starting the Notification task "{0}".
#XMSG: Task log message for a successful Notification task completion
notificationTaskCompletionSuccess=The Notification task "{0}" has completed.
#XMSG: Task log message when a cancel task failed to cancel a run task
notificationTaskCancelFailure=The cancel task with logId {1} failed to cancel the run task with logId {0}.
#XMSG: Task log message when a task failed because a cancel task was triggered
notificationTaskSendEmailCancelled=The run task with logId {0} was canceled by cancel task {1}.
#XMSG: Task log message when the task preparation is taking too long and times out
notificationTaskPreparationTimeout=The task preparation is taking too long and has timed out.
#XMSG: Task log message for the Notification task being canceled
notificationTaskCancelStart=Requested cancelation of the Notification task "{0}" with logId {1}.
#XMSG: Task log message for the cancelation of the an API task which is no longer running and cannot be canceled
notificationTaskUnableToCancel=The Notification task "{0}" with logId {1} is no longer running and cannot be canceled.
#XMSG: Task log message when the Notification task is not configured correctly
notificationTaskMissingConfiguration=The task "{0}" is missing necessary configuration and has been terminated.
#XFLD: Label for frequency column
everyLabel=Every
#XFLD: Plural Recurrence text for Hour
hoursLabel=Hours
#XFLD: Plural Recurrence text for Day
daysLabel=Days
#XFLD: Plural Recurrence text for Month
monthsLabel=Months
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minutes
