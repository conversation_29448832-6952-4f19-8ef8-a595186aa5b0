

#XFLD: Task Chain header
headerTxt=Chaînes de tâches ({0})
#XFLD: Task Chain header
headerTxtNew=Chaînes de tâches ({0})
#XTXT: Text for Schedule label
scheduleTxt=Planification
#XFLD: Text for Create schedule button
createScheduleTxt=Créer une planification
#XFLD: Text for edit schedule
editScheduleTxt=Modifier la planification
#XLFD: Text for delete schedule
deleteScheduleTxt=Supprimer la planification
#XTXT: text for refresh button label
refrestTxt=Actualiser
#XTXT: Text for Completed status
completedTxt=Terminé
#XTX: Text for Running status
runningTxt=En cours d'exécution
#XTX: Text for failed status
failedTxt=Échec
#XTX: Text for stopped status
stoppedTxt=Arrêté
#XTX: Text for stopping status
stoppingTxt=Arrêt en cours
#XFLD: Header for Chain name
chainNameLabel=Nom de la chaîne de tâches
#XFLD: Header for Chain name
chainNameLabelBus=Appellation
#XFLD: Header for Chain name
chainNameLabelBusNew=Objet (Appellation)
#XFLD: Header for Chain name
chainNameLabelTech=Nom technique
#XFLD: Header for Chain name
chainNameLabelTechNew=Objet (Nom technique)
#XFLD: Last Run Status label
lastRunStatuslabel=Statut de la dernière exécution
#XFLD: Last Run Status label
lastRunStatuslabelNew=Statut
#XFLD: Frequency Label
frequencyLabel=Fréquence
#XFLD: Frequency Label
frequencyLabelNew=Fréquence planifiée
#XFLD: Duration label
durationLabel=Durée
#XFLD: Duration label
durationLabelNew=Durée de la dernière exécution
#XFLD: Run Start label
runStartLabel=Début de la dernière exécution
#XFLD: Run end label
runEndLabel=Fin de la dernière exécution
#XFLD: Next Run label
nextRunlabel=Prochaine exécution
#XFLD: Next Run label
nextRunlabelNew=Prochaine exécution planifiée
#XTXT: Task chain logs link tooltip text
taskChainLogsTxt=Détails relatifs au journal de la chaîne de tâches
#XTXT: Task chain logs link tooltip text
taskChainLogsTxtNew=Détails
#XTXT: Scheduled text
scheduledTxt=Planifié
#XTXT: Paused text
pausedTxt=Suspendu
#XTXT: Execute button label
runLabel=Exécuter
#XTXT: Execute button label
runLabelNew=Lancer l'exécution
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Exécuter la chaîne de tâches
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Lancement de l'exécution de la chaîne de tâches
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Lancement de l''exécution de la chaîne de tâches pour {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Échec de l'exécution de la chaîne de tâches
#XFLD: Label for schedule owner column
txtScheduleOwner=Responsable des planifications
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Affiche la personne ayant créé la planification
#XMSG: Task log message for start chain
startChain=Lancement de l'exécution de la chaîne de tâches
#XMSG: Task log message for load chain from repository
loadChainFromRepository=Chaîne de tâches chargée et initialisée
#XMSG: Task log message started task
taskStarted=La tâche {0} a été lancée.
#XMSG: Task log message for finished task
taskFinished=La tâche {0} s''est terminée avec le statut {1}.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Chargement de la chaîne de tâches en cours et préparation de l''exécution de {0} tâches appartenant à cette chaîne.
#XMSG: Task log message for starting a subtask
chainStartSubtask=Déclenchement de l''exécution de la tâche {0}. ID de la tâche = {1}.
#XMSG: Task log message for finishing a subtask
chainFinishSubtask=Tâche {0} terminée au statut {1}.
#XMSG: Task log message for indicating chain success
chainCompleted=Les {0} tâches sont terminées. Le statut de la chaîne de tâches a été défini sur Terminé.
#XMSG: Task log message for indicating chain failure
chainFailed=Sur {0} tâches au total, {1} tâches se sont terminées et {2} tâches ont échoué. Le statut de la chaîne de tâches a été défini sur Échec.
#XMSG: Task log message for indicating chain cancelation
chainCanceled=L''annulation de l''exécution de la chaîne de tâche a commencé. L''ID de journal de l''annulation de la tâche est {0}.
#XMSG: Task log message for indicating chain cancelation in the cancel task
chainCanceling=Annulation de la chaîne {0}
#XMSG: Task log message for general chain runtime error
chainError=Une erreur inattendue s'est produite lors de l'exécution de la chaîne de tâches.
#XMSG: Task log message for starting the overwrite check.
chainOverwriteStart=Vérification de la progression de la chaîne de tâches dont l''ID est {0}.
#XMSG: Task log message indicating the chain is too old.
chainOverwriteTooOld=Aucune tâche à exécuter pour la chaîne {0}. L''exécution date de {1} minutes. Le statut de la chaîne de tâches a été défini sur Échec.
#XMSG: Task log message indicating the chain was created too recently.
chainOverwriteTooRecent=Aucune tâche à exécuter pour la chaîne {0}. L''exécution date de {1} minutes. La chaîne de tâches est toujours en cours d''exécution.
#XMSG: Task log message indicating a recent heartbeat was found.
chainOverwriteHeartbeatAlive=La chaîne de tâches {0} est toujours en cours d''exécution. Terminée(s) : {1}, En cours d''exécution : {2}, Échec : {3}, Déclenchée(s) : {4}, Non déclenchée(s) : {5}.
#XMSG: Task log message for a chain that is not running anymore but not everything was triggered.
chainOverwriteNotAllTriggered=Une erreur s''est produite lors de l''exécution de la chaîne de tâches {0} et toutes les tâches n''ont pas été déclenchées. Terminée(s) : {1}, En cours d''exécution : {2}, Échec : {3}, Déclenchée(s) : {4}, Non déclenchée(s) : {5}. Cette chaîne de tâches a été écrasée.
#XMSG: Task log message for a chain that contains a failed subtask.
chainOverwriteAnySubtaskFailed=Chaîne de tâches {0} terminée. Une des tâches de la chaîne a échoué, le statut de la chaîne est donc défini sur Échec.
#XMSG: Task log message could not determine status.
chainOverwriteNotDetermined=Une erreur inattendue nous empêche d'accéder au statut de la tâche. Réessayez et contactez le support SAP si l'erreur persiste.
#XMSG: Task log message could not take over
failedTakeover=Échec de la reprise de la tâche existante.
#XMSG: Task log parallel check error
parallelCheckError=La tâche n'a pas pu être traitée car une autre tâche est en cours d'exécution et bloque déjà cette tâche.
#XMSG: Task log parallel task runnig error
parallelTaskRunning=Une tâche en conflit est déjà en cours d'exécution.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Statut {0} pendant l''exécution avec l''ID de corrélation {1}.
#XMSG: Task log message successful takeover
successTakeover=Le blocage restant a dû être libéré. Le nouveau blocage pour cette tâche est défini.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Le blocage de cette tâche a été repris par une autre tâche.
#XMSG: Schedule created alert message
createScheduleSuccess=La planification a été créée.
#XMSG: Task log message for taskFinishedAt
taskFinishedAt=Tâche {0} terminée à {2} au statut {1}
#XMSG: Placeholder symbol for empty columns
emptyCol=--
#XMSG: Paused Text
txtExpired=Expiré
#XMSG: Update task schedule success Toast message
updateScheduleSuccess=La planification a été mise à jour
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Planification supprimée
#XMSG: Task chain might have children error text
taskChainNoChildErrorText=La chaîne de tâches a peut-être des enfants qui ne sont pas affichés car la tâche a échoué avant que le plan ne soit généré.
#XMSG: Task chain repair recent failed run label
retryRunLabel=Réessayez la dernière exécution.
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=La nouvelle tentative d'exécution de la chaîne de tâches a commencé.
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=La nouvelle tentative d'exécution de la chaîne de tâches a échoué.
#XMSG: chain repair message
chainRetried=Nouvelle tentative d''exécution de la chaîne de tâches déclenchée par l''utilisateur {0}
#XMSG: chain not found during run
chainNotFoundDuringRun=La chaîne de tâches {0} est introuvable. Vérifiez si elle existe via le Data Builder et assurez-vous que la chaîne est déployée.
#XMSG: chain is not DAG
chainNotDag=Impossible de lancer la chaîne de tâches {0}. Sa structure n''est pas valide. Contrôlez la chaîne de tâches dans le Data Builder.
#XMSG: chain has not valid parameters
notValidParameters=Impossible de lancer la chaîne de tâches {0}. Un ou plusieurs de ses paramètres ne sont pas valides. Contrôlez la chaîne de tâches dans le Data Builder.
#XMSG: Configuration parameter value size exceeds the maximum allowed limit
configurationSizeLimitError=Impossible de lancer la chaîne de tâches {0}. La taille de la configuration d''une tâche dans la chaîne dépasse la taille maximale autorisée de 100 Kio.
#XMSG: Task {0} has input parameters
taskHasInputParameters=La tâche {0} a des paramètres d''entrée.
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Une tâche en conflit est déjà en cours d'exécution.
#XMSG: error message for reading data from backend
txtReadBackendError=Il semblerait qu'une erreur se soit produite lors de la lecture à partir du backend.
##XMSG: error message for admission control rejection
admissionControlError=La tâche a échoué en raison d'un rejet de contrôle d'accès pour SAP HANA.

#XBUT: Assign schedule menu button label
assignScheduleLabel=M'affecter la planification
#XBUT: Pause schedule menu label
pauseScheduleLabel=Suspendre la planification
#XBUT: Resume schedule menu label
resumeScheduleLabel=Reprendre la planification
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Une erreur s'est produite lors du retrait des planifications.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Une erreur s'est produite lors de l'affectation des planifications.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Une erreur s'est produite lors de la suspension des planifications.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Une erreur s'est produite lors de la reprise des planifications.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Suppression de {0} planifications
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Modification du responsable de {0} planifications
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Suspension de {0} planifications
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Reprise de {0} planifications
#XBUT: Select Columns Button
selectColumnsBtn=Sélectionner des colonnes
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Il semblerait qu'une erreur se soit produite lors de la dernière tentative d’exécution. En effet, l'exécution de la tâche précédente a échoué avant la génération du plan.
#XFLD: Refresh tooltip
TEXT_REFRESH=Actualiser
#XFLD: Select Columns tooltip
text_selectColumns=Sélectionner des colonnes

#Messages for BW Process Chain task log of the Data Integration Monitor
#XMSG: Task log message for successfully starting a BW process chain
startBwProcessChainSuccess=L''exécution de la chaîne de processus BW "{0}" a été lancée avec succès dans le locataire du pont vers SAP BW.
#XMSG: Task log message for skipping a BW process chain execution
bwProcessChainSkipped=La chaîne de processus BW "{0}" a été ignorée en raison de la non-disponibilité des nouvelles données ou de l''échec d''une exécution précédente.
#XMSG: Task log message for failure in starting a BW process chain
startBwProcessChainFailure=Le lancement de la chaîne de processus BW "{0}" a échoué dans le locataire du pont vers SAP BW.
#XMSG: Task log message for failure in getting the status of a BW process chain
getBwProcessChainStatusFailure=La tâche a échoué car nous n''avons pas pu récupérer le statut de la chaîne de processus BW "{0}" en raison d''un problème d''établissement d''une connexion du pont vers SAP BW. Ouvrez l''application "Connexions" et validez la connexion du pont vers SAP BW dans l''espace "{1}". Si vous n''avez pas accès à l''application "Connexions", contactez votre administrateur.
#XMSG: Task log message for failure in completing a BW process chain
completeBwProcessChainFailure=L''exécution de la chaîne de processus BW "{0}" a échoué dans le locataire du pont vers SAP BW.
#XMSG: Task log message for the clickable text of the SAP BW Bridge Monitor link
bwBridgeMonitorLinkText=Afficher les détails dans le moniteur du pont vers SAP BW
#XMSG warning message while retrieving the task log messages if the user does not have access to the SAP BW Bridge space or to the shared object's space
missingPrivilegeOnSpaceError=Un problème est survenu lors de la récupération des messages ou bien vous ne disposez pas de l'autorisation nécessaire pour les afficher.

#Messages for API task log of the Data Integration Monitor
#XMSG: Task log message for displaying the API response
viewResponse=Réponse reçue pour "{0}".
#XMSG: Task log message for empty ID in the API response to retrieve the status
emptyJobId=Aucun ID n''a été renvoyé dans le corps de la réponse pour le chemin d''accès JSON : "{0}".
#XMSG: Task log message for empty success value in the API response
emptySuccessValue=Aucune valeur de réussite n''a été renvoyée dans le corps de la réponse pour le chemin d''accès JSON : "{0}".
#XMSG: Task log message for empty error value in the API response
emptyErrorValue=Aucune valeur d''erreur n''a été renvoyée dans le corps de la réponse pour le chemin d''accès JSON : "{0}".
#XMSG: Task log message for none of the success/error conditions matching for the values in the API response
conditionsNotFulfilled=Aucune des conditions indiquées du code de réussite ou d'erreur ne correspond aux valeurs dans la réponse.
#XMSG: Task log message for the API response status being unsuccessful
statusNotOk=La réponse pour "{1}" a renvoyé un code de statut d''échec : {0}.
#XMSG: Task log message for the API response body being empty
emptyResponseBody=Le corps de la réponse était vide.
#XMSG: Task log message for the API response location header being invalid
emptyLocationHeader=L'en-tête de l'emplacement dans la réponse était vide.
#XMSG: Task log message for failure in retrieving the status of the invoked API
getStatusFailure=Le statut de l'API appelée n'a pas pu être récupéré.
#XMSG: Task log message for failure in completing the API task
completionFailure=La tâche d''API "{0}" n''a pas abouti.
#XMSG: Task log message for a successful API task completion
apiCompleted=La tâche d''API "{0}" a abouti.
#XMSG: Task log message for an API task with an invalid configuration
invalidConfiguration=La configuration de la tâche d''API "{0}" n''est pas valide. Contrôlez la configuration et réessayez.
#XMSG: Task log message for the API task being canceled
cancelStart=Annulation demandée de la tâche d''API "{0}" avec l''ID de connexion {1}.
#XMSG: Task log message for the cancelation of the an API task which is no longer running and cannot be canceled
unableToCancel=La tâche d''API "{0}" avec l''ID de connexion {1} ne s''exécute plus et ne peut plus être annulée.
#XMSG: Task log message when starting an API task. It should carry the name of the API task.
apiRunStart=Lancement de la tâche API "{0}".
#XMSG: Task log message when the task preparation is taking too long and times out
preparationTimeout=La préparation de la tâche prend trop de temps et a expiré.
#XMSG: Task log message when a task failed because a cancel task was triggered
apiRunCancelled=La tâche d’exécution avec l''ID de connexion {0} a été annulée par la tâche d''annulation {1}.
#XMSG: Task log message when a cancel task failed to cancel a run task
cancelFailure=La tâche d’annulation avec l''ID de connexion {1} n''a pas réussi à annuler la tâche d''exécution avec l''ID de connexion {0}.
#XMSG: Task log message when configuration of the API task is too large
apiTaskconfigurationSizeLimitError=Impossible de lancer la tâche d''API {0} car la taille de la configuration de sa tâche dépasse la taille maximale autorisée de 100 Kio.
#XMSG: Task log message for failure in completing the Notification task
notificationTaskCompletionFailure=La tâche de notification "{0}" n''a pas abouti.
#XMSG: Task log message when starting a Notification task.
notificationTaskRunStart=Lancement en cours de la tâche de notification "{0}".
#XMSG: Task log message for a successful Notification task completion
notificationTaskCompletionSuccess=La tâche de notification "{0}" est terminée.
#XFLD: Label for frequency column
everyLabel=Tous les/Toutes les
#XFLD: Plural Recurrence text for Hour
hoursLabel=Heures
#XFLD: Plural Recurrence text for Day
daysLabel=Jours
#XFLD: Plural Recurrence text for Month
monthsLabel=Mois
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minutes
