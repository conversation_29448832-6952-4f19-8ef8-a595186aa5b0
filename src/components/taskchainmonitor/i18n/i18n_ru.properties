

#XFLD: Task Chain header
headerTxt=Цепочки задач ({0})
#XFLD: Task Chain header
headerTxtNew=Цепочки задач ({0})
#XTXT: Text for Schedule label
scheduleTxt=Планирование
#XFLD: Text for Create schedule button
createScheduleTxt=Создать планирование
#XFLD: Text for edit schedule
editScheduleTxt=Редактировать планирование
#XLFD: Text for delete schedule
deleteScheduleTxt=Удалить планирование
#XTXT: text for refresh button label
refrestTxt=Обновить
#XTXT: Text for Completed status
completedTxt=Завершено
#XTX: Text for Running status
runningTxt=Выполняется
#XTX: Text for failed status
failedTxt=Не выполнено
#XTX: Text for stopped status
stoppedTxt=Остановлено
#XTX: Text for stopping status
stoppingTxt=Останавливается
#XFLD: Header for Chain name
chainNameLabel=Имя цепочки задач
#XFLD: Header for Chain name
chainNameLabelBus=Бизнес-имя
#XFLD: Header for Chain name
chainNameLabelBusNew=Объект (бизнес-имя)
#XFLD: Header for Chain name
chainNameLabelTech=Техническое имя
#XFLD: Header for Chain name
chainNameLabelTechNew=Объект (техническое имя)
#XFLD: Last Run Status label
lastRunStatuslabel=Статус последнего прогона
#XFLD: Last Run Status label
lastRunStatuslabelNew=Статус
#XFLD: Frequency Label
frequencyLabel=Частота
#XFLD: Frequency Label
frequencyLabelNew=Плановая периодичность
#XFLD: Duration label
durationLabel=Продолжительность
#XFLD: Duration label
durationLabelNew=Продолжительность последнего прогона
#XFLD: Run Start label
runStartLabel=Начало последнего прогона
#XFLD: Run end label
runEndLabel=Конец последнего прогона
#XFLD: Next Run label
nextRunlabel=Следующий прогон
#XFLD: Next Run label
nextRunlabelNew=Запланированный следующий прогон
#XTXT: Task chain logs link tooltip text
taskChainLogsTxt=Сведения о журнале цепочек задач
#XTXT: Task chain logs link tooltip text
taskChainLogsTxtNew=Сведения
#XTXT: Scheduled text
scheduledTxt=Запланировано
#XTXT: Paused text
pausedTxt=Приостановлено
#XTXT: Execute button label
runLabel=Выполнить
#XTXT: Execute button label
runLabelNew=Запустить прогон
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Выполнить цепочку задач
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Прогон цепочки задач запущен.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Прогон цепочки задач запущен для {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Не удалось выполнить цепочку задач.
#XFLD: Label for schedule owner column
txtScheduleOwner=Владелец планирования
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Показывает, кто создал планирование
#XMSG: Task log message for start chain
startChain=Запускаем прогон цепочки задач.
#XMSG: Task log message for load chain from repository
loadChainFromRepository=Цепочка задач загружена и инициализирована.
#XMSG: Task log message started task
taskStarted=Задача {0} запущена.
#XMSG: Task log message for finished task
taskFinished=Задача {0} завершена со статусом {1}.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Загрузка цепочки задач и подготовка к выполнению {0} задач, входящих в эту цепочку.
#XMSG: Task log message for starting a subtask
chainStartSubtask=Инициация выполнения задачи {0}. Ид. задачи = {1}.
#XMSG: Task log message for finishing a subtask
chainFinishSubtask=Задача {0} завершена со статусом {1}.
#XMSG: Task log message for indicating chain success
chainCompleted=Все задачи ({0}) завершены. Установлен статус цепочки задач "завершено".
#XMSG: Task log message for indicating chain failure
chainFailed=Из {0} задач всего удалось завершить {1} задач, сбой в {2} задачах. Установлен статус цепочки задач "не выполнено".
#XMSG: Task log message for indicating chain cancelation
chainCanceled=Отмена выполнения цепочки задач запущена. Ид. журнала задачи отмены: {0}.
#XMSG: Task log message for indicating chain cancelation in the cancel task
chainCanceling=Отмена цепочки {0}.
#XMSG: Task log message for general chain runtime error
chainError=При выполнении цепочки задач обнаружена неожиданная ошибка.
#XMSG: Task log message for starting the overwrite check.
chainOverwriteStart=Проверка завершения цепочки задач с ид. {0}.
#XMSG: Task log message indicating the chain is too old.
chainOverwriteTooOld=Не удалось найти задачи цепочки {0} для выполнения. Прогон выполняется {1} мин. Установлен статус цепочки задач "не выполнено".
#XMSG: Task log message indicating the chain was created too recently.
chainOverwriteTooRecent=Не удалось найти задачи цепочки {0} для выполнения. Прогон выполняется {1} мин. Цепочка задач все еще выполняется.
#XMSG: Task log message indicating a recent heartbeat was found.
chainOverwriteHeartbeatAlive=Цепочка задач {0} все еще выполняется. Завершено: {1}, выполняется: {2}, не выполнено: {3}, инициировано: {4}, не инициировано: {5}.
#XMSG: Task log message for a chain that is not running anymore but not everything was triggered.
chainOverwriteNotAllTriggered=Ошибка при выполнении цепочки задач {0}, не все задачи инициированы. Завершено: {1}, выполняется: {2}, не выполнено: {3}, инициировано: {4}, не инициировано: {5}. Эта цепочка задач переписана.
#XMSG: Task log message for a chain that contains a failed subtask.
chainOverwriteAnySubtaskFailed=Цепочка задач {0} завершена. Одна из задач цепочки не выполнена, статус цепочки задач "не выполнено".
#XMSG: Task log message could not determine status.
chainOverwriteNotDetermined=Нежиданная ошибка препятствует получению доступа к статусу задачи. Повторите попытку и обратитесь в службу SAP по сопровождению, если ошибка сохранится.
#XMSG: Task log message could not take over
failedTakeover=Не удалось принять существующую задачу.
#XMSG: Task log parallel check error
parallelCheckError=Невозможно обработать задачу, так как выполняется другая задача, блокирующая ее.
#XMSG: Task log parallel task runnig error
parallelTaskRunning=Уже выполняется конфликтующая задача.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Статус {0} во время выполнения с ид. корреляции {1}.
#XMSG: Task log message successful takeover
successTakeover=Требовалось снять существующую блокировку. Новая блокировка для этой задачи установлена.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Блокировка этой задачи перенесена на другую задачу.
#XMSG: Schedule created alert message
createScheduleSuccess=Планирование создано
#XMSG: Task log message for taskFinishedAt
taskFinishedAt=Задача {0} завершена в {2} со статусом {1}.
#XMSG: Placeholder symbol for empty columns
emptyCol=--
#XMSG: Paused Text
txtExpired=Истекло
#XMSG: Update task schedule success Toast message
updateScheduleSuccess=Планирование обновлено
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Планирование удалено.
#XMSG: Task chain might have children error text
taskChainNoChildErrorText=У цепочки задач могут быть подчиненные объекты, которые не отображаются из-за ошибки задачи до генерации плана.
#XMSG: Task chain repair recent failed run label
retryRunLabel=Повторить последнее выполнение
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Повторный прогон цепочки задач запущен
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Повторный прогон цепочки задач не выполнен
#XMSG: chain repair message
chainRetried=Повторение цепочки задач инициировал пользователь {0}
#XMSG: chain not found during run
chainNotFoundDuringRun=Не найдена цепочка задач {0}. Проверьте существование через построитель данных и убедитесь, что цепочка развернута.
#XMSG: chain is not DAG
chainNotDag=Невозможно запустить цепочку задач {0}. Недействительная структура. Проверьте цепочку задач в построителе данных.
#XMSG: chain has not valid parameters
notValidParameters=Невозможно запустить цепочку задач {0}. Один или несколько параметров недействительны. Проверьте цепочку задач в построителе данных.
#XMSG: Configuration parameter value size exceeds the maximum allowed limit
configurationSizeLimitError=Невозможно запустить цепочку задач {0}. Размер конфигурации задачи в цепочке превышает разрешенный максимальный размер в 100 кибибайтов (КиБ).
#XMSG: Task {0} has input parameters
taskHasInputParameters=Задача {0} имеет параметры ввода.
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Уже выполняется конфликтующая задача
#XMSG: error message for reading data from backend
txtReadBackendError=Ошибка при чтении из бэкэнда.
##XMSG: error message for admission control rejection
admissionControlError=Задача не выполнена из-за отклонения в контроле допуска SAP HANA.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Присвоить планирование мне
#XBUT: Pause schedule menu label
pauseScheduleLabel=Приостановить планирование
#XBUT: Resume schedule menu label
resumeScheduleLabel=Возобновить планирование
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Произошла ошибка при удалении планирования.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Произошла ошибка при присвоении планирования.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Произошла ошибка при приостановке планирования.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Произошла ошибка при возобновлении планирования.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Удаляем планирования ({0})
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Изменяем владельца планирований {0}
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Приостанавливаем планирования {0}
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Возобновляем планирования {0}
#XBUT: Select Columns Button
selectColumnsBtn=Выбрать столбцы
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Вероятно, возникла ошибка при повторной попытке выполнения, поскольку выполнение предыдущей задачи закончилось ошибкой до генерации плана.
#XFLD: Refresh tooltip
TEXT_REFRESH=Обновить
#XFLD: Select Columns tooltip
text_selectColumns=Выбрать столбцы

#Messages for BW Process Chain task log of the Data Integration Monitor
#XMSG: Task log message for successfully starting a BW process chain
startBwProcessChainSuccess=Цепочка процессов BW "{0}" успешно запущена в арендаторе моста SAP BW.
#XMSG: Task log message for skipping a BW process chain execution
bwProcessChainSkipped=Цепочка процессов BW "{0}" пропущена, так как либо недоступны новые данные, либо имеется ошибка предыдущего выполнения.
#XMSG: Task log message for failure in starting a BW process chain
startBwProcessChainFailure=Не удалось запустить цепочку процессов BW "{0}" в арендаторе моста SAP BW.
#XMSG: Task log message for failure in getting the status of a BW process chain
getBwProcessChainStatusFailure=Задача не выполнена, так как не удалось вызвать статус цепочки процессов BW "{0}" из-за проблемы с установкой соединения с мостом SAP BW. Откройте приложение "Соединения" и проверьте соединение с мостом SAP BW в пространстве "{1}". Если у вас нет доступа к приложению "Соединения", обратитесь к администратору.
#XMSG: Task log message for failure in completing a BW process chain
completeBwProcessChainFailure=Не удалось завершить цепочку процессов BW "{0}" в арендаторе моста SAP BW.
#XMSG: Task log message for the clickable text of the SAP BW Bridge Monitor link
bwBridgeMonitorLinkText=Просмотреть сведения в мониторе моста SAP BW
#XMSG warning message while retrieving the task log messages if the user does not have access to the SAP BW Bridge space or to the shared object's space
missingPrivilegeOnSpaceError=Возникла проблема при вызове сообщений, или у вас нет разрешения на их просмотр.

#Messages for API task log of the Data Integration Monitor
#XMSG: Task log message for displaying the API response
viewResponse=Ответ получен для "{0}".
#XMSG: Task log message for empty ID in the API response to retrieve the status
emptyJobId=Не возвращен ид. в тексте ответа для пути JSON: "{0}".
#XMSG: Task log message for empty success value in the API response
emptySuccessValue=Не возвращено значение успеха в тексте ответа для пути JSON: "{0}".
#XMSG: Task log message for empty error value in the API response
emptyErrorValue=Не возвращено значение ошибки в тексте ответа для пути JSON: "{0}".
#XMSG: Task log message for none of the success/error conditions matching for the values in the API response
conditionsNotFulfilled=Ни одно из указанных условий индикатора успеха или ошибки не соответствует значениям в ответе.
#XMSG: Task log message for the API response status being unsuccessful
statusNotOk=Ответ для "{1}" вернул код статуса неудачи: {0}.
#XMSG: Task log message for the API response body being empty
emptyResponseBody=Пустой текст ответа.
#XMSG: Task log message for the API response location header being invalid
emptyLocationHeader=Заголовок местоположения в ответе был пустым.
#XMSG: Task log message for failure in retrieving the status of the invoked API
getStatusFailure=Не удалось получить статус вызванного API.
#XMSG: Task log message for failure in completing the API task
completionFailure=Не удалось завершить задачу API "{0}".
#XMSG: Task log message for a successful API task completion
apiCompleted=Задача API "{0}" успешно завершена.
#XMSG: Task log message for an API task with an invalid configuration
invalidConfiguration=Конфигурация задачи API "{0}" недействительна. Проверьте конфигурацию и повторите попытку.
#XMSG: Task log message for the API task being canceled
cancelStart=Запрошена отмена задачи API "{0}" с logId {1}.
#XMSG: Task log message for the cancelation of the an API task which is no longer running and cannot be canceled
unableToCancel=Задача API "{0}" с logId {1} больше не выполняется и не может быть отменена.
#XMSG: Task log message when starting an API task. It should carry the name of the API task.
apiRunStart=Запуск задачи API "{0}".
#XMSG: Task log message when the task preparation is taking too long and times out
preparationTimeout=Подготовка задачи длится слишком долго, и ее время истекло.
#XMSG: Task log message when a task failed because a cancel task was triggered
apiRunCancelled=Задача выполнения с logId {0} была отменена задачей отмены {1}.
#XMSG: Task log message when a cancel task failed to cancel a run task
cancelFailure=Задаче отмены с logId {1} не удалось отменить задачу выполнения с logId {0}.
#XMSG: Task log message when configuration of the API task is too large
apiTaskconfigurationSizeLimitError=Задача API {0} не может быть запущена, так как размер ее конфигурации превышает разрешенный максимальный размер в 100 кибибайтов (КиБ).
#XMSG: Task log message for failure in completing the Notification task
notificationTaskCompletionFailure=Не удалось завершить задачу уведомления "{0}".
#XMSG: Task log message when starting a Notification task.
notificationTaskRunStart=Запуск задачи уведомления "{0}".
#XMSG: Task log message for a successful Notification task completion
notificationTaskCompletionSuccess=Задача уведомления "{0}" завершена.
#XFLD: Label for frequency column
everyLabel=Кажд.
#XFLD: Plural Recurrence text for Hour
hoursLabel=ч
#XFLD: Plural Recurrence text for Day
daysLabel=дн.
#XFLD: Plural Recurrence text for Month
monthsLabel=мес.
#XFLD: Plural Recurrence text for Minutes
minutesLabel=мин
