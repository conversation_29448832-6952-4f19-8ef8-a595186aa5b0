

#XFLD: Task Chain header
headerTxt=Cadenas de tareas ({0})
#XFLD: Task Chain header
headerTxtNew=Cadenas de tareas ({0})
#XTXT: Text for Schedule label
scheduleTxt=Programar
#XFLD: Text for Create schedule button
createScheduleTxt=Crear programa
#XFLD: Text for edit schedule
editScheduleTxt=Editar programa
#XLFD: Text for delete schedule
deleteScheduleTxt=Eliminar programa
#XTXT: text for refresh button label
refrestTxt=Actualizar
#XTXT: Text for Completed status
completedTxt=Finalizado
#XTX: Text for Running status
runningTxt=En ejecución
#XTX: Text for failed status
failedTxt=Error
#XTX: Text for stopped status
stoppedTxt=Detenido
#XTX: Text for stopping status
stoppingTxt=Detención en curso
#XFLD: Header for Chain name
chainNameLabel=Nombre de cadena de tareas
#XFLD: Header for Chain name
chainNameLabelBus=Nombre empresarial
#XFLD: Header for Chain name
chainNameLabelBusNew=Objeto (nombre empresarial)
#XFLD: Header for Chain name
chainNameLabelTech=Nombre técnico
#XFLD: Header for Chain name
chainNameLabelTechNew=Objeto (nombre técnico)
#XFLD: Last Run Status label
lastRunStatuslabel=Estado de última ejecución
#XFLD: Last Run Status label
lastRunStatuslabelNew=Estado
#XFLD: Frequency Label
frequencyLabel=Frecuencia
#XFLD: Frequency Label
frequencyLabelNew=Frecuencia programada
#XFLD: Duration label
durationLabel=Duración
#XFLD: Duration label
durationLabelNew=Duración de última ejecución
#XFLD: Run Start label
runStartLabel=Inicio de última ejecución
#XFLD: Run end label
runEndLabel=Finalización de última ejecución
#XFLD: Next Run label
nextRunlabel=Siguiente ejecución
#XFLD: Next Run label
nextRunlabelNew=Próxima ejecución programada
#XTXT: Task chain logs link tooltip text
taskChainLogsTxt=Detalles de registro de la cadena de tareas
#XTXT: Task chain logs link tooltip text
taskChainLogsTxtNew=Detalles
#XTXT: Scheduled text
scheduledTxt=Programado
#XTXT: Paused text
pausedTxt=En pausa
#XTXT: Execute button label
runLabel=Ejecutar
#XTXT: Execute button label
runLabelNew=Iniciar ejecución
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Ejecutar la cadena de tareas
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Se inició la cadena de tareas.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Se inició la ejecución de la cadena de tareas para {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Se produjo un error en la ejecución de la cadena de tareas.
#XFLD: Label for schedule owner column
txtScheduleOwner=Propietario del programa
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Muestra quién creó el programa
#XMSG: Task log message for start chain
startChain=Iniciando la ejecución de la cadena de tareas.
#XMSG: Task log message for load chain from repository
loadChainFromRepository=La cadena de tareas se cargó e inicializó.
#XMSG: Task log message started task
taskStarted=Se inició la tarea {0}.
#XMSG: Task log message for finished task
taskFinished=La tarea {0} finalizó con el estado {1}.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=En proceso de carga de la cadena de tareas y en preparación para la ejecución de un total de {0} tareas que son parte de esta cadena.
#XMSG: Task log message for starting a subtask
chainStartSubtask=Activando tarea {0} para ejecución. ID de tarea = {1}.
#XMSG: Task log message for finishing a subtask
chainFinishSubtask=La tarea {0} finalizó con el estado {1}.
#XMSG: Task log message for indicating chain success
chainCompleted=Finalizaron todas las {0} tareas. El estado de la cadena de tareas se establece en Finalizado.
#XMSG: Task log message for indicating chain failure
chainFailed=De un total de {0} tareas, {1} tareas pudieron finalizarse y {2} no se completaron. El estado de la cadena de tareas se establece en Error.
#XMSG: Task log message for indicating chain cancelation
chainCanceled=Se inició la cancelación de la ejecución de la cadena de tareas. El ID de registro de la tarea de cancelación es {0}.
#XMSG: Task log message for indicating chain cancelation in the cancel task
chainCanceling=Cancelando cadena {0}.
#XMSG: Task log message for general chain runtime error
chainError=Se produjo un error inesperado durante la ejecución de la cadena de tareas.
#XMSG: Task log message for starting the overwrite check.
chainOverwriteStart=Verificando si finalizó la cadena de tareas con el ID {0}.
#XMSG: Task log message indicating the chain is too old.
chainOverwriteTooOld=No se encontraron tareas para ejecutar en la cadena {0}. La ejecución tiene {1} minutos. El estado se establece en Error.
#XMSG: Task log message indicating the chain was created too recently.
chainOverwriteTooRecent=No se encontraron tareas para ejecutar en la cadena {0}. La ejecución tiene {1} minutos. La cadena de tareas aún está en ejecución.
#XMSG: Task log message indicating a recent heartbeat was found.
chainOverwriteHeartbeatAlive=La cadena de tareas {0} aún está en ejecución. Finalizado: {1}, En ejecución: {2}, Error: {3}, Activado: {4}, No activado: {5}.
#XMSG: Task log message for a chain that is not running anymore but not everything was triggered.
chainOverwriteNotAllTriggered=Se produjo un error durante la ejecución de la cadena de tareas {0}; no todas las tareas pudieron activarse. Finalizado: {1}, En ejecución: {2}, Error: {3}, Activado: {4}, No activado: {5}. Se sobrescribió esta cadena de tareas.
#XMSG: Task log message for a chain that contains a failed subtask.
chainOverwriteAnySubtaskFailed=Finalizó la cadena de tareas {0}. Una de las tareas no se completó; el estado de la cadena se establece en Error.
#XMSG: Task log message could not determine status.
chainOverwriteNotDetermined=Un error inesperado no nos permite acceder al estado de la tarea. Vuelva a intentarlo y, si el error persiste, comuníquese con el soporte de SAP.
#XMSG: Task log message could not take over
failedTakeover=Se produjo un error que impidió retomar la tarea existente.
#XMSG: Task log parallel check error
parallelCheckError=La tarea no se puede procesar porque otra tarea está en ejecución y ya bloquea esta tarea.
#XMSG: Task log parallel task runnig error
parallelTaskRunning=Ya se está ejecutando una tarea con conflictos.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Estado {0} durante la ejecución con el ID de correlación {1}.
#XMSG: Task log message successful takeover
successTakeover=Se liberó el bloqueo anterior y se estableció un nuevo bloqueo para esta tarea.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=El bloqueo de esta tarea fue tomado por otra tarea.
#XMSG: Schedule created alert message
createScheduleSuccess=Programa creado
#XMSG: Task log message for taskFinishedAt
taskFinishedAt=La tarea {0} finalizó a las {2} con el estado {1}.
#XMSG: Placeholder symbol for empty columns
emptyCol=--
#XMSG: Paused Text
txtExpired=Vencido
#XMSG: Update task schedule success Toast message
updateScheduleSuccess=Programa actualizado
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Se eliminó el programa.
#XMSG: Task chain might have children error text
taskChainNoChildErrorText=La cadena de tareas podría tener tareas secundarias que no se muestran porque ocurrió un error en la tarea antes de que se pudiera generar el plan.
#XMSG: Task chain repair recent failed run label
retryRunLabel=Reintentar última ejecución
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Se inició el reintento de la cadena de tareas
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Falló el reintento de la cadena de tareas
#XMSG: chain repair message
chainRetried=El usuario {0} activó el reintento de la cadena de tareas
#XMSG: chain not found during run
chainNotFoundDuringRun=No se pudo encontrar la cadena de tareas {0}. Compruebe si existe a través del Generador de datos y asegúrese de que se implemente la cadena.
#XMSG: chain is not DAG
chainNotDag=No se puede iniciar la cadena de tareas {0}. Su estructura no es válida. Compruebe la cadena de tareas en el generador de datos.
#XMSG: chain has not valid parameters
notValidParameters=No se puede iniciar la cadena de tareas {0}. Uno o más de sus parámetros no son válidos. Compruebe la cadena de tareas en el generador de datos.
#XMSG: Configuration parameter value size exceeds the maximum allowed limit
configurationSizeLimitError=No se puede iniciar la cadena de tareas {0}. El tamaño de configuración de una tarea en la cadena excede el tamaño máximo permitido de 100 kibibytes (KiB).
#XMSG: Task {0} has input parameters
taskHasInputParameters=La tarea {0} tiene parámetros de entrada.
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Ya se está ejecutando una tarea con conflictos
#XMSG: error message for reading data from backend
txtReadBackendError=Parece que hubo un error durante la lectura desde el backend.
##XMSG: error message for admission control rejection
admissionControlError=La tarea falló debido a un rechazo del control de admisión de SAP HANA.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Asignarme programa
#XBUT: Pause schedule menu label
pauseScheduleLabel=Pausar programa
#XBUT: Resume schedule menu label
resumeScheduleLabel=Reanudar programa
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Ocurrió un error al eliminar programas.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Ocurrió un error al asignar programas.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Ocurrió un error al pausar programas.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Ocurrió un error al reanudar programas.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Eliminando {0} programas
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Cambiando el propietario de {0} programas
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Pausando {0} programas
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Reanudando {0} programas
#XBUT: Select Columns Button
selectColumnsBtn=Seleccionar columnas
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Parece que ocurrió un error al reintentar la última ejecución, ya que la ejecución de la tarea anterior falló antes de que se pudiera generar el plan.
#XFLD: Refresh tooltip
TEXT_REFRESH=Actualizar
#XFLD: Select Columns tooltip
text_selectColumns=Seleccionar columnas

#Messages for BW Process Chain task log of the Data Integration Monitor
#XMSG: Task log message for successfully starting a BW process chain
startBwProcessChainSuccess=La cadena de procesos de BW "{0}" se inició correctamente en el inquilino de puente de SAP BW.
#XMSG: Task log message for skipping a BW process chain execution
bwProcessChainSkipped=La cadena de proceso de BW "{0}" se omitió debido a la falta de disponibilidad de nuevos datos o a la falla de una ejecución previa.
#XMSG: Task log message for failure in starting a BW process chain
startBwProcessChainFailure=La cadena de procesos de BW "{0}" no se inició en el inquilino de puente de SAP BW.
#XMSG: Task log message for failure in getting the status of a BW process chain
getBwProcessChainStatusFailure=La tarea falló porque no pudimos recuperar el estado de la cadena de proceso de BW "{0}" debido a un problema en el establecimiento de una conexión al puente de SAP BW. Abra la aplicación "Conexiones" y valide la conexión de puente de SAP BW en el espacio "{1}". Si no tiene acceso a la aplicación "Conexiones", comuníquese con su administrador.
#XMSG: Task log message for failure in completing a BW process chain
completeBwProcessChainFailure=La cadena de procesos de BW "{0}" no se completó en el inquilino de puente de SAP BW.
#XMSG: Task log message for the clickable text of the SAP BW Bridge Monitor link
bwBridgeMonitorLinkText=Ver detalles en el Supervisor de puente de SAP BW
#XMSG warning message while retrieving the task log messages if the user does not have access to the SAP BW Bridge space or to the shared object's space
missingPrivilegeOnSpaceError=Ocurrió un problema al recuperar los mensajes, o no tiene la autorización necesaria para verlos.

#Messages for API task log of the Data Integration Monitor
#XMSG: Task log message for displaying the API response
viewResponse=Respuesta recibida para "{0}".
#XMSG: Task log message for empty ID in the API response to retrieve the status
emptyJobId=No se mostró ID en el cuerpo de la respuesta para la ruta JSON: "{0}".
#XMSG: Task log message for empty success value in the API response
emptySuccessValue=No se mostró valor de estado correcto en el cuerpo de la respuesta para la ruta JSON: "{0}".
#XMSG: Task log message for empty error value in the API response
emptyErrorValue=No se mostró valor de error en el cuerpo de la respuesta para la ruta JSON: "{0}".
#XMSG: Task log message for none of the success/error conditions matching for the values in the API response
conditionsNotFulfilled=Ninguna de las condiciones de indicador de estado correcto o error coincide con los valores en la respuesta.
#XMSG: Task log message for the API response status being unsuccessful
statusNotOk=La respuesta para "{1}" mostró un código de estado de error: "{0}".
#XMSG: Task log message for the API response body being empty
emptyResponseBody=El cuerpo de la respuesta estaba vacío.
#XMSG: Task log message for the API response location header being invalid
emptyLocationHeader=El encabezado de ubicación en la respuesta estaba vacío.
#XMSG: Task log message for failure in retrieving the status of the invoked API
getStatusFailure=El esto de la API invocada no se pudo recuperar.
#XMSG: Task log message for failure in completing the API task
completionFailure=La tarea de API "{0}" no se pudo completar.
#XMSG: Task log message for a successful API task completion
apiCompleted=La tarea de API "{0}" se completó correctamente.
#XMSG: Task log message for an API task with an invalid configuration
invalidConfiguration=La configuración de la tarea de API "{0}" no es válida. Revise la configuración y vuelva a intentarlo.
#XMSG: Task log message for the API task being canceled
cancelStart=Cancelación solicitada de la tarea de API "{0}" con el ID de registro {1}.
#XMSG: Task log message for the cancelation of the an API task which is no longer running and cannot be canceled
unableToCancel=La tarea de API "{0}" con el ID de registro {1} ya no se está ejecutando y no se puede cancelar.
#XMSG: Task log message when starting an API task. It should carry the name of the API task.
apiRunStart=Iniciando la tarea de API "{0}".
#XMSG: Task log message when the task preparation is taking too long and times out
preparationTimeout=La preparación de la tarea está demorando mucho y se agotó el tiempo.
#XMSG: Task log message when a task failed because a cancel task was triggered
apiRunCancelled=La tarea de ejecución con el ID de registro {0} se canceló con la tarea de cancelación {1}.
#XMSG: Task log message when a cancel task failed to cancel a run task
cancelFailure=La tarea de cancelación con el ID de registro {1} no pudo cancelar la tarea de cancelación con el ID de registro {0}.
#XMSG: Task log message when configuration of the API task is too large
apiTaskconfigurationSizeLimitError=No se puede iniciar la tarea de API {0} porque el tamaño de configuración de la tarea excede el tamaño máximo permitido de 100 kibibytes (KiB).
#XMSG: Task log message for failure in completing the Notification task
notificationTaskCompletionFailure=La tarea de notificación "{0}" no se pudo completar.
#XMSG: Task log message when starting a Notification task.
notificationTaskRunStart=Iniciando la tarea de notificación "{0}".
#XMSG: Task log message for a successful Notification task completion
notificationTaskCompletionSuccess=La tarea de notificación "{0}" se completó.
#XFLD: Label for frequency column
everyLabel=Cada
#XFLD: Plural Recurrence text for Hour
hoursLabel=Horas
#XFLD: Plural Recurrence text for Day
daysLabel=Días
#XFLD: Plural Recurrence text for Month
monthsLabel=Meses
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minutos
