

#XFLD: Task Chain header
headerTxt=Feladatláncok ({0})
#XFLD: Task Chain header
headerTxtNew=<PERSON>ladatláncok ({0})
#XTXT: Text for Schedule label
scheduleTxt=Ütemezés
#XFLD: Text for Create schedule button
createScheduleTxt=Ütemezés létrehozása
#XFLD: Text for edit schedule
editScheduleTxt=Ütemezés szerkesztése
#XLFD: Text for delete schedule
deleteScheduleTxt=Ütemezés törlése
#XTXT: text for refresh button label
refrestTxt=Frissítés
#XTXT: Text for Completed status
completedTxt=Befejeződött
#XTX: Text for Running status
runningTxt=Fut
#XTX: Text for failed status
failedTxt=Sikertelen
#XTX: Text for stopped status
stoppedTxt=Leállítva
#XTX: Text for stopping status
stoppingTxt=Leállítás
#XFLD: Header for Chain name
chainNameLabel=Feladatlánc neve
#XFLD: Header for Chain name
chainNameLabelBus=Üzleti név
#XFLD: Header for Chain name
chainNameLabelBusNew=Objektum (üzleti név)
#XFLD: Header for Chain name
chainNameLabelTech=Technikai név
#XFLD: Header for Chain name
chainNameLabelTechNew=Objektum (technikai név)
#XFLD: Last Run Status label
lastRunStatuslabel=Legutóbbi futás állapota
#XFLD: Last Run Status label
lastRunStatuslabelNew=Állapot
#XFLD: Frequency Label
frequencyLabel=Gyakoriság
#XFLD: Frequency Label
frequencyLabelNew=Ütemezés szerinti gyakoriság
#XFLD: Duration label
durationLabel=Időtartam
#XFLD: Duration label
durationLabelNew=Legutóbbi futás időtartama
#XFLD: Run Start label
runStartLabel=Legutóbbi futás kezdete
#XFLD: Run end label
runEndLabel=Legutóbbi futás vége
#XFLD: Next Run label
nextRunlabel=Következő futás
#XFLD: Next Run label
nextRunlabelNew=Legközelebbi beütemezett futás
#XTXT: Task chain logs link tooltip text
taskChainLogsTxt=Feladatláncnapló részletei
#XTXT: Task chain logs link tooltip text
taskChainLogsTxtNew=Részletek
#XTXT: Scheduled text
scheduledTxt=Beütemezve
#XTXT: Paused text
pausedTxt=Szünetel
#XTXT: Execute button label
runLabel=Futtatás
#XTXT: Execute button label
runLabelNew=Futás indítása
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Feladatlánc futtatása
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=A feladatlánc futtatása megkezdődött.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=A feladatlánc futtatása elindult: {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Nem sikerült futtatni a feladatláncot.
#XFLD: Label for schedule owner column
txtScheduleOwner=Ütemezés tulajdonosa
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Az ütemezés létrehozójának megjelenítése
#XMSG: Task log message for start chain
startChain=Feladatlánc-futás indítása
#XMSG: Task log message for load chain from repository
loadChainFromRepository=A feladatlánc betöltve és inicializálva.
#XMSG: Task log message started task
taskStarted=A(z) {0} feladat elindult.
#XMSG: Task log message for finished task
taskFinished=A(z) {0} feladat {1} állapottal fejeződött be.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Feladatlánc betöltése és a lánchoz tartozó összesen {0} feladat futtatásának előkészítése.
#XMSG: Task log message for starting a subtask
chainStartSubtask=A(z) {0} feladat futtatásának indítása. Feladatazonosító = {1}.
#XMSG: Task log message for finishing a subtask
chainFinishSubtask=A(z) {0} feladat {1} állapottal fejeződött be.
#XMSG: Task log message for indicating chain success
chainCompleted=Mind a(z) {0} feladat befejeződött. A feladatlánc Befejeződött állapotba került.
#XMSG: Task log message for indicating chain failure
chainFailed=Összesen {0} feladatból {1} feladatot nem sikerült befejezni, {2} pedig sikertelen volt. A feladatlánc Sikertelen állapotba került.
#XMSG: Task log message for indicating chain cancelation
chainCanceled=A feladatláncfutás megszakítása megkezdődött. A megszakítási feladat naplóazonosítója: {0}.
#XMSG: Task log message for indicating chain cancelation in the cancel task
chainCanceling=A(z) {0} lánc megszakítása.
#XMSG: Task log message for general chain runtime error
chainError=Váratlan hiba történt a feladatlánc futtatásakor.
#XMSG: Task log message for starting the overwrite check.
chainOverwriteStart=A rendszer ellenőrzi, hogy befejeződött-e a(z) {0} azonosítójú feladatlánc.
#XMSG: Task log message indicating the chain is too old.
chainOverwriteTooOld=Nem található futtatható feladat a(z) {0} láncnál. A futtatás {1} perce kezdődött. A feladatlánc Sikertelen állapotba került.
#XMSG: Task log message indicating the chain was created too recently.
chainOverwriteTooRecent=Nem található futtatható feladat a(z) {0} láncnál. A futtatás {1} perce kezdődött. A feladatlánc még fut.
#XMSG: Task log message indicating a recent heartbeat was found.
chainOverwriteHeartbeatAlive=A(z) {0} feladatlánc még fut. Befejeződött: {1}, fut: {2}, sikertelen: {3}, elindítva: {4}, nincs elindítva: {5}.
#XMSG: Task log message for a chain that is not running anymore but not everything was triggered.
chainOverwriteNotAllTriggered=Hiba történt a(z) {0} feladatlánc futtatásakor, és nem sikerült minden feladatot elindítani. Befejeződött: {1}, fut: {2}, sikertelen: {3}, elindítva: {4}, nincs elindítva: {5}. Ez a feladatlánc felülíródott.
#XMSG: Task log message for a chain that contains a failed subtask.
chainOverwriteAnySubtaskFailed=A(z) {0} feladatlánc befejeződött. A lánc egyik feladata sikertelen volt. A feladatlánc Sikertelen állapotba kerül.
#XMSG: Task log message could not determine status.
chainOverwriteNotDetermined=Egy váratlan hiba miatt nem lehet hozzáférni a feladat állapotához. Próbálkozzon újra. Ha a hiba továbbra is fennáll, forduljon az SAP támogatási csoportjához.
#XMSG: Task log message could not take over
failedTakeover=Nem sikerült átvenni a meglévő feladatot.
#XMSG: Task log parallel check error
parallelCheckError=A feladat nem dolgozható fel, mert egy másik folyamat fut, és már zárolja ezt a feladatot.
#XMSG: Task log parallel task runnig error
parallelTaskRunning=Már fut egy ezzel ütköző feladat.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus={0} állapot futtatás közben, korrelációazonosító: {1}.
#XMSG: Task log message successful takeover
successTakeover=A hátramaradt zárolást fel kellett oldani. Az új zárolás beállítva a feladatnál.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Egy másik feladat átvette ennek a feladatnak a zárolását.
#XMSG: Schedule created alert message
createScheduleSuccess=Az ütemezés létrejött
#XMSG: Task log message for taskFinishedAt
taskFinishedAt=A(z) {0} feladat {1} állapottal fejeződött be ekkor: {2}
#XMSG: Placeholder symbol for empty columns
emptyCol=--
#XMSG: Paused Text
txtExpired=Lejárt
#XMSG: Update task schedule success Toast message
updateScheduleSuccess=Az ütemezés frissítve
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Az ütemezés törölve.
#XMSG: Task chain might have children error text
taskChainNoChildErrorText=Lehet, hogy a feladatláncnak vannak olyan gyermekei, amelyek nem jelennek meg, a feladat már a terv generálása előtt meghiúsult.
#XMSG: Task chain repair recent failed run label
retryRunLabel=Legutóbbi futás újrapróbálása
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=A feladatlánc újrafuttatása elindult
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=A feladatlánc újrafuttatása sikertelen
#XMSG: chain repair message
chainRetried=A feladatlánc újbóli megkísérlése kiváltva {0} felhasználó által
#XMSG: chain not found during run
chainNotFoundDuringRun=A következő feladatlánc nem található: {0}. Az Adatmodell-szerkesztővel ellenőrizze, hogy létezik-e, és üzembe van-e helyezve a lánc.
#XMSG: chain is not DAG
chainNotDag=A következő feladatlánc nem indítható el: {0}. Érvénytelen a struktúrája. Ellenőrizze a feladatláncot az Adatmodell-szerkesztőben.
#XMSG: chain has not valid parameters
notValidParameters=A következő feladatlánc nem indítható el: {0}. Egy vagy több paramétere érvénytelen. Ellenőrizze a feladatláncot az Adatmodell-szerkesztőben.
#XMSG: Configuration parameter value size exceeds the maximum allowed limit
configurationSizeLimitError=A következő feladatlánc nem indítható el: {0}. A feladat konfigurációjának mérete a láncban túllépi az engedélyezett maximális, 100 kibibájtos (KiB) méretet.
#XMSG: Task {0} has input parameters
taskHasInputParameters=A(z) {0} feladat bemeneti paraméterekkel rendelkezik.
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Már fut egy ezzel ütköző feladat
#XMSG: error message for reading data from backend
txtReadBackendError=Hiba történt a backendből való olvasáskor.
##XMSG: error message for admission control rejection
admissionControlError=A feladat sikertelen, mert az SAP HANA bejutás-ellenőrzése elutasította.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Ütemezés magamhoz rendelése
#XBUT: Pause schedule menu label
pauseScheduleLabel=Ütemezés szüneteltetése
#XBUT: Resume schedule menu label
resumeScheduleLabel=Ütemezés folytatása
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Hiba történt az ütemezések eltávolításakor.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Hiba történt az ütemezések hozzárendelésekor.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Hiba történt az ütemezések szüneteltetésekor.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Hiba történt az ütemezések folytatásakor.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted={0} ütemezés törlése
#XMSG: Message for starting mass assign of schedules
massAssignStarted={0} ütemezés tulajdonosának módosítása
#XMSG: Message for starting mass pausing of schedules
massPauseStarted={0} ütemezés szüneteltetése
#XMSG: Message for starting mass resuming of schedules
massResumeStarted={0} ütemezés folytatása
#XBUT: Select Columns Button
selectColumnsBtn=Oszlopok kiválasztása
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Úgy tűnik, hiba történt a legutóbbi futás újbóli megkísérlésekor, mert az előző feladatfuttatás már a terv generálása előtt meghiúsult.
#XFLD: Refresh tooltip
TEXT_REFRESH=Frissítés
#XFLD: Select Columns tooltip
text_selectColumns=Oszlopok kiválasztása

#Messages for BW Process Chain task log of the Data Integration Monitor
#XMSG: Task log message for successfully starting a BW process chain
startBwProcessChainSuccess=A(z) {0} BW-folyamatlánc sikeresen elindult az SAP BW-híd-bérlőben.
#XMSG: Task log message for skipping a BW process chain execution
bwProcessChainSkipped=A(z) {0} BW-folyamatlánc ki lett hagyva, mert nem érhetők el új adatok, vagy nem sikerült egy korábbi végrehajtás.
#XMSG: Task log message for failure in starting a BW process chain
startBwProcessChainFailure=A(z) {0} BW-folyamatlánc elindítása nem sikerült az SAP BW-híd-bérlőben.
#XMSG: Task log message for failure in getting the status of a BW process chain
getBwProcessChainStatusFailure=A feladat nem sikerült, mert nem tudtuk lehívni a(z) {0} BW-folyamatlánc állapotát az SAP BW-hídhoz való kapcsolódási problémák miatt. Nyissa meg a Kapcsolatok alkalmazást, és érvényesítse az SAP BW-híd kapcsolatot a(z) {1} térben. Ha nincs hozzáférése a Kapcsolatok alkalmazáshoz, forduljon az adminisztrátorhoz.
#XMSG: Task log message for failure in completing a BW process chain
completeBwProcessChainFailure=A(z) {0} BW-folyamatláncot nem sikerült befejezni az SAP BW-híd-bérlőben.
#XMSG: Task log message for the clickable text of the SAP BW Bridge Monitor link
bwBridgeMonitorLinkText=Részletek megtekintése az SAP BW-híd monitorában
#XMSG warning message while retrieving the task log messages if the user does not have access to the SAP BW Bridge space or to the shared object's space
missingPrivilegeOnSpaceError=Probléma merült fel az üzenetek lehívásakor, vagy nincs megfelelő jogosultsága a megtekintésükhöz.

#Messages for API task log of the Data Integration Monitor
#XMSG: Task log message for displaying the API response
viewResponse=Válasz érkezett a következőhöz: {0}.
#XMSG: Task log message for empty ID in the API response to retrieve the status
emptyJobId=Nem érkezett azonosító a választörzsben a következő JSON-útvonalhoz: "{0}".
#XMSG: Task log message for empty success value in the API response
emptySuccessValue=Nem érkezett sikerérték a választörzsben a következő JSON-útvonalhoz: "{0}".
#XMSG: Task log message for empty error value in the API response
emptyErrorValue=Nem érkezett hibaérték a választörzsben a következő JSON-útvonalhoz: "{0}".
#XMSG: Task log message for none of the success/error conditions matching for the values in the API response
conditionsNotFulfilled=A megadott siker- és hibajelző-feltételek egyike sem felel meg a válaszban szereplő értékeknek.
#XMSG: Task log message for the API response status being unsuccessful
statusNotOk={1} esetében a válasz sikertelen állapotkódot adott vissza: {0}.
#XMSG: Task log message for the API response body being empty
emptyResponseBody=Üres volt a választörzs.
#XMSG: Task log message for the API response location header being invalid
emptyLocationHeader=Üres volt a helyfejléc a válaszban.
#XMSG: Task log message for failure in retrieving the status of the invoked API
getStatusFailure=Nem sikerült lehívni a meghívott API állapotát.
#XMSG: Task log message for failure in completing the API task
completionFailure=Nem sikerült elvégezni a(z) {0} API-feladatot.
#XMSG: Task log message for a successful API task completion
apiCompleted=A(z) {0} API-feladat sikeresen befejeződött.
#XMSG: Task log message for an API task with an invalid configuration
invalidConfiguration=A(z) {0} API-feladat konfigurációja érvénytelen. Ellenőrizze a konfigurációt, és próbálkozzon újra.
#XMSG: Task log message for the API task being canceled
cancelStart=A(z) {0} API-feladat megszakítása kérelmezve, naplóazonosító: {1}.
#XMSG: Task log message for the cancelation of the an API task which is no longer running and cannot be canceled
unableToCancel=A(z) {0} API-feladat (naplóazonosító: {1}) már nem fut, ezért nem szakítható meg.
#XMSG: Task log message when starting an API task. It should carry the name of the API task.
apiRunStart={0} API-feladat indítása
#XMSG: Task log message when the task preparation is taking too long and times out
preparationTimeout=A feladat előkészítése túl sokáig tart, és időtúllépés miatt megszakadt.
#XMSG: Task log message when a task failed because a cancel task was triggered
apiRunCancelled=A(z) {0} naplóazonosítójú futtatási feladatot megszakította {1} megszakítási feladat.
#XMSG: Task log message when a cancel task failed to cancel a run task
cancelFailure=A(z) {1} naplóazonosítójú megszakítási feladat nem tudta megszakítani a(z) {0} naplóazonosítójú futtatási feladatot.
#XMSG: Task log message when configuration of the API task is too large
apiTaskconfigurationSizeLimitError=A(z) {0} API-feladat nem indítható el, mert a feladat konfigurációjának mérete túllépi az engedélyezett maximális, 100 kibibájtos (KiB) méretet.
#XMSG: Task log message for failure in completing the Notification task
notificationTaskCompletionFailure=Nem sikerült elvégezni a(z) {0} értesítési feladatot.
#XMSG: Task log message when starting a Notification task.
notificationTaskRunStart=A(z) {0} értesítési feladat indítása.
#XMSG: Task log message for a successful Notification task completion
notificationTaskCompletionSuccess=A(z) {0} értesítési feladat befejeződött.
#XFLD: Label for frequency column
everyLabel=Gyakoriság
#XFLD: Plural Recurrence text for Hour
hoursLabel=óra
#XFLD: Plural Recurrence text for Day
daysLabel=nap
#XFLD: Plural Recurrence text for Month
monthsLabel=hónap
#XFLD: Plural Recurrence text for Minutes
minutesLabel=perc
