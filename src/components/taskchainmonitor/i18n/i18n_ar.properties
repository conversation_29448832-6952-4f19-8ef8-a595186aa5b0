

#XFLD: Task Chain header
headerTxt=سلاسل المهام({0})
#XFLD: Task Chain header
headerTxtNew=سلاسل المهام ({0})
#XTXT: Text for Schedule label
scheduleTxt=الجدول الزمني
#XFLD: Text for Create schedule button
createScheduleTxt=إنشاء الجدول الزمني
#XFLD: Text for edit schedule
editScheduleTxt=تحرير الجدول الزمني
#XLFD: Text for delete schedule
deleteScheduleTxt=حذف الجدول الزمني
#XTXT: text for refresh button label
refrestTxt=تحديث
#XTXT: Text for Completed status
completedTxt=مكتمل
#XTX: Text for Running status
runningTxt=قيد التشغيل
#XTX: Text for failed status
failedTxt=فشل
#XTX: Text for stopped status
stoppedTxt=موقوف
#XTX: Text for stopping status
stoppingTxt=جارٍ الإيقاف
#XFLD: Header for Chain name
chainNameLabel=اسم سلسلة المهام
#XFLD: Header for Chain name
chainNameLabelBus=الاسم التجاري
#XFLD: Header for Chain name
chainNameLabelBusNew=الكائن (الاسم التجاري)
#XFLD: Header for Chain name
chainNameLabelTech=الاسم التقني
#XFLD: Header for Chain name
chainNameLabelTechNew=الكائن (الاسم التقني)
#XFLD: Last Run Status label
lastRunStatuslabel=حالة آخر تشغيل
#XFLD: Last Run Status label
lastRunStatuslabelNew=الحالة
#XFLD: Frequency Label
frequencyLabel=التكرار
#XFLD: Frequency Label
frequencyLabelNew=التكرار المجدوَل
#XFLD: Duration label
durationLabel=المدة
#XFLD: Duration label
durationLabelNew=مدة آخر تشغيل
#XFLD: Run Start label
runStartLabel=بداية آخر تشغيل
#XFLD: Run end label
runEndLabel=نهاية آخر تشغيل
#XFLD: Next Run label
nextRunlabel=التشغيل التالي
#XFLD: Next Run label
nextRunlabelNew=التشغيل التالي المجدوَل
#XTXT: Task chain logs link tooltip text
taskChainLogsTxt=تفاصيل سجل سلسلة المهام
#XTXT: Task chain logs link tooltip text
taskChainLogsTxtNew=التفاصيل
#XTXT: Scheduled text
scheduledTxt=مجدوَل
#XTXT: Paused text
pausedTxt=إيقاف مؤقت
#XTXT: Execute button label
runLabel=تشغيل
#XTXT: Execute button label
runLabelNew=بدء التشغيل
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=تشغيل سلسلة المهام
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=بدأ تشغيل سلسلة المهام.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=بدأ تشغيل سلسلة المهام لـ {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=فشل تشغيل سلسلة المهام.
#XFLD: Label for schedule owner column
txtScheduleOwner=مالك الجدول الزمني
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=إظهار الشخص الذي أنشأ الجدول الزمني
#XMSG: Task log message for start chain
startChain=جارٍ بدء تشغيل سلسلة المهام.
#XMSG: Task log message for load chain from repository
loadChainFromRepository=تم تحميل سلسلة المهام وتهيئتها.
#XMSG: Task log message started task
taskStarted=تم بدء المهمة {0}.
#XMSG: Task log message for finished task
taskFinished=انتهت مهمة {0} بالحالة {1}.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=جارٍ تحميل سلسلة المهام والتحضير لتشغيل إجمالي {0} من المهام التي تشكل جزءًا من هذه السلسلة.
#XMSG: Task log message for starting a subtask
chainStartSubtask=جارٍ بدء تشغيل المهمة {0} للتشغيل. معرف المهمة = {1}.
#XMSG: Task log message for finishing a subtask
chainFinishSubtask=تم إنهاء المهمة {0} بالحالة {1}.
#XMSG: Task log message for indicating chain success
chainCompleted=تم إكمال كل {0} من المهام. تم تعيين حالة سلسلة المهام على مكتملة.
#XMSG: Task log message for indicating chain failure
chainFailed=في إجمالي {0} من المهام، تعذر إكمال {1} من المهام وفشل إكمال {2} من المهام. تم تعيين حالة سلسلة المهام على فاشل.
#XMSG: Task log message for indicating chain cancelation
chainCanceled=بدأ إلغاء تشغيل سلسلة المهام. معرف سجل مهمة الإلغاء هو {0}.
#XMSG: Task log message for indicating chain cancelation in the cancel task
chainCanceling=جارٍ إلغاء السلسلة {0}.
#XMSG: Task log message for general chain runtime error
chainError=حدث خطأ غير متوقع أثناء تشغيل سلسلة المهام.
#XMSG: Task log message for starting the overwrite check.
chainOverwriteStart=جارٍ فحص ما إذا كان تم إنهاء سلسلة المهام بالمعرف {0}.
#XMSG: Task log message indicating the chain is too old.
chainOverwriteTooOld=تعذر العثور على مهام لتشغيل سلسلة {0}. التشغيل عمره {1} من الدقائق. تم تعيين حالة سلسلة المهام على فاشل.
#XMSG: Task log message indicating the chain was created too recently.
chainOverwriteTooRecent=تعذر العثور على مهام لتشغيل سلسلة {0}. التشغيل عمره {1} من الدقائق. سلسلة المهام لا تزال قيد التشغيل.
#XMSG: Task log message indicating a recent heartbeat was found.
chainOverwriteHeartbeatAlive=سلسلة المهام {0} لا تزال قيد التشغيل. مكتمل: {1}، قيد التشغيل: {2}، فاشل: {3}، مبدوء التشغيل: {4}، غير مبدوء التشغيل: {5}.
#XMSG: Task log message for a chain that is not running anymore but not everything was triggered.
chainOverwriteNotAllTriggered=حدث خطأ أثناء تشغيل سلسلة المهام {0}، وتعذر بدء تشغيل جميع المهام. مكتمل: {1}، قيد التشغيل: {2}، فاشل: {3}، مبدوء التشغيل: {4}، غير مبدوء التشغيل: {5}. تم استبدال سلسلة المهام هذه.
#XMSG: Task log message for a chain that contains a failed subtask.
chainOverwriteAnySubtaskFailed=تم إنهاء سلسلة المهام {0}. فشلت إحدى مهام السلسلة، جارٍ تعيين السلسلة إلى فاشل.
#XMSG: Task log message could not determine status.
chainOverwriteNotDetermined=خطأ غير متوقع يمنعنا من الوصول إلى حالة المهمة. حاول مرة أخرى واتصل بدعم SAP إذا استمر الخطأ.
#XMSG: Task log message could not take over
failedTakeover=فشل تولي مهمة موجودة.
#XMSG: Task log parallel check error
parallelCheckError=لا يمكن معالجة المهمة نظرًا لوجود مهمة أخرى قيد التشغيل وتوقف هذه المهمة بالفعل.
#XMSG: Task log parallel task runnig error
parallelTaskRunning=توجد مهمة متعارضة قيد التشغيل بالفعل.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=الحالة {0} أثناء التشغيل بمعرف الارتباط {1}.
#XMSG: Task log message successful takeover
successTakeover=كان لابد من تحرير التأمين المتبقي. تم تعيين التأمين الجديد لهذه المهمة.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=تم تولي تأمين هذه المهمة من خلال مهمة أخرى.
#XMSG: Schedule created alert message
createScheduleSuccess=تم إنشاء الجدول الزمني
#XMSG: Task log message for taskFinishedAt
taskFinishedAt=تم إنهاء المهمة {0} في {2} بالحالة {1}.
#XMSG: Placeholder symbol for empty columns
emptyCol=--
#XMSG: Paused Text
txtExpired=منتهي الصلاحية
#XMSG: Update task schedule success Toast message
updateScheduleSuccess=تم تحديث الجدول الزمني
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=تم حذف الجدول الزمني.
#XMSG: Task chain might have children error text
taskChainNoChildErrorText=قد تحتوي سلسلة المهام على عناصر فرعية لا تظهر بسبب فشل المهمة قبل إنشاء الخطة.
#XMSG: Task chain repair recent failed run label
retryRunLabel=إعادة محاولة أحدث تشغيل
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=بدأ تشغيل إعادة محاولة سلسلة المهام
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=فشل تشغيل إعادة محاولة سلسلة المهام
#XMSG: chain repair message
chainRetried=تم بدء تشغيل إعادة محاولة سلسلة المهام بواسطة المستخدم {0}
#XMSG: chain not found during run
chainNotFoundDuringRun=تعذر العثور على سلسلة المهام {0}. تحقق مما إذا كانت موجودة عبر أداة إنشاء البيانات وتأكد من نشر السلسلة.
#XMSG: chain is not DAG
chainNotDag=لا يمكن بدء سلسلة المهام {0}. البنية الخاصة بها غير صالحة. تحقق من سلسلة المهام في أداة إنشاء البيانات.
#XMSG: chain has not valid parameters
notValidParameters=لا يمكن بدء سلسلة المهام {0}. واحدة أو أكثر من معامِلاتها غير صالحة. تحقق من سلسلة المهام في أداة إنشاء البيانات.
#XMSG: Configuration parameter value size exceeds the maximum allowed limit
configurationSizeLimitError=لا يمكن بدء سلسلة المهام {0}. يتجاوز حجم تكوين المهمة في السلسلة الحد الأقصى للحجم المسموح به البالغ 100 كيبيبايت (KiB).
#XMSG: Task {0} has input parameters
taskHasInputParameters=المهمة {0} بها معامِلات إدخال.
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=توجد مهمة متعارضة قيد التشغيل بالفعل
#XMSG: error message for reading data from backend
txtReadBackendError=يبدو أن هناك خطأ أثناء القراءة من النظام الخلفي.
##XMSG: error message for admission control rejection
admissionControlError=فشلت المهمة بسبب رفض التحكم بالدخول في SAP HANA.

#XBUT: Assign schedule menu button label
assignScheduleLabel=تعيين الجدول لي
#XBUT: Pause schedule menu label
pauseScheduleLabel=إيقاف مؤقت للجدول
#XBUT: Resume schedule menu label
resumeScheduleLabel=استئناف الجدول
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=حدث خطأ أثناء إزالة الجداول.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=حدث خطأ أثناء تعيين الجداول.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=حدث خطأ أثناء الإيقاف المؤقت للجداول.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=حدث خطأ أثناء استئناف الجداول.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=جارٍ حذف {0} من الجداول
#XMSG: Message for starting mass assign of schedules
massAssignStarted=جارٍ تغيير مالك {0} من الجداول
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=جارٍ الإيقاف المؤقت لعدد {0} من الجداول
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=جارٍ استئناف {0} من الجداول
#XBUT: Select Columns Button
selectColumnsBtn=تحديد الأعمدة
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=يبدو أنه كان هناك خطأ في إعادة محاولة التشغيل الأخير حيث فشل تشغيل المهمة السابقة قبل أن يتمكن من إنشاء الخطة.
#XFLD: Refresh tooltip
TEXT_REFRESH=تحديث
#XFLD: Select Columns tooltip
text_selectColumns=تحديد الأعمدة

#Messages for BW Process Chain task log of the Data Integration Monitor
#XMSG: Task log message for successfully starting a BW process chain
startBwProcessChainSuccess=تم بدء سلسلة عمليات BW "{0}" بنجاح في الوحدة المستضافة لوحدة توصيل SAP BW.
#XMSG: Task log message for skipping a BW process chain execution
bwProcessChainSkipped=تم تخطي سلسلة عمليات BW "{0}" إما بسبب عدم توفر بيانات جديدة أو فشل تنفيذ سابق.
#XMSG: Task log message for failure in starting a BW process chain
startBwProcessChainFailure=فشل بدء سلسلة عمليات BW "{0}" في الوحدة المستضافة لوحدة توصيل SAP BW.
#XMSG: Task log message for failure in getting the status of a BW process chain
getBwProcessChainStatusFailure=فشلت المهمة نظرًا لأننا لم نتمكن من استرجاع حالة سلسلة عمليات BW "{0}" بسبب مشكلة في إنشاء اتصال بوحدة توصيل SAP BW. افتح تطبيق "الاتصالات" وتحقق من صحة الاتصال بوحدة توصيل SAP BW في مساحة "{1}". إذا لم يكن لديك حق الوصول إلى تطبيق "الاتصالات"، يرجى الاتصال بالمسؤول لديك.
#XMSG: Task log message for failure in completing a BW process chain
completeBwProcessChainFailure=فشل إكمال سلسلة عمليات BW "{0}" في الوحدة المستضافة لوحدة توصيل SAP BW.
#XMSG: Task log message for the clickable text of the SAP BW Bridge Monitor link
bwBridgeMonitorLinkText=عرض التفاصيل في مراقب وحدة توصيل SAP BW
#XMSG warning message while retrieving the task log messages if the user does not have access to the SAP BW Bridge space or to the shared object's space
missingPrivilegeOnSpaceError=حدثت مشكلة أثناء استرجاع الرسائل، أو ليس لديك الإذن اللازم لعرضها.

#Messages for API task log of the Data Integration Monitor
#XMSG: Task log message for displaying the API response
viewResponse=تم تلقي استجابة لـ "{0}".
#XMSG: Task log message for empty ID in the API response to retrieve the status
emptyJobId=لم يتم إرجاع معرف في نص الاستجابة لمسار JSON: "{0}".
#XMSG: Task log message for empty success value in the API response
emptySuccessValue=لم يتم إرجاع أي قيمة نجاح في نص الاستجابة لمسار JSON: "{0}".
#XMSG: Task log message for empty error value in the API response
emptyErrorValue=لم يتم إرجاع أي قيمة خطأ في نص الاستجابة لمسار JSON: "{0}".
#XMSG: Task log message for none of the success/error conditions matching for the values in the API response
conditionsNotFulfilled=لا تتطابق أي من شروط مؤشر النجاح أو الخطأ المحددة مع القيم الموجودة في الاستجابة.
#XMSG: Task log message for the API response status being unsuccessful
statusNotOk=الاستجابة لـ "{1}" أرجعت رمز حالة غير ناجح: {0}.
#XMSG: Task log message for the API response body being empty
emptyResponseBody=النص الأساسي للاستجابة فارغ.
#XMSG: Task log message for the API response location header being invalid
emptyLocationHeader=كانت مقدمة الموقع في الاستجابة فارغة.
#XMSG: Task log message for failure in retrieving the status of the invoked API
getStatusFailure=تعذر استرجاع حالة واجهة برمجة التطبيقات المستدعاة.
#XMSG: Task log message for failure in completing the API task
completionFailure=فشل إكمال مهمة واجهة برمجة التطبيقات "{0}".
#XMSG: Task log message for a successful API task completion
apiCompleted=اكتملت مهمة واجهة برمجة التطبيقات "{0}" بنجاح.
#XMSG: Task log message for an API task with an invalid configuration
invalidConfiguration=تكوين مهمة واجهة برمجة التطبيقات "{0}" غير صالح. يرجى التحقق من التكوين والمحاولة مرة أخرى.
#XMSG: Task log message for the API task being canceled
cancelStart=تم طلب إلغاء مهمة واجهة برمجة التطبيقات "{0}" بمعرف logId {1}.
#XMSG: Task log message for the cancelation of the an API task which is no longer running and cannot be canceled
unableToCancel="مهمة واجهة برمجة التطبيقات "{0}" مع logId {1} لم تعد قيد التشغيل ولا يمكن إلغاؤها.
#XMSG: Task log message when starting an API task. It should carry the name of the API task.
apiRunStart=جارٍ بدء مهمة واجهة برمجة التطبيقات "{0}".
#XMSG: Task log message when the task preparation is taking too long and times out
preparationTimeout=تحضير المهمة يستغرق وقتًا طويلاً وقد انتهت المدة المحددة.
#XMSG: Task log message when a task failed because a cancel task was triggered
apiRunCancelled=تم إلغاء مهمة التشغيل بمعرف logId {0} بواسطة مهمة الإلغاء {1}.
#XMSG: Task log message when a cancel task failed to cancel a run task
cancelFailure=فشلت مهمة الإلغاء ذات logId {1} لإلغاء مهمة التشغيل بالمعرف logId {0}.
#XMSG: Task log message when configuration of the API task is too large
apiTaskconfigurationSizeLimitError=لا يمكن بدء مهمة واجهة برمجة التطبيقات {0} لأن حجم تكوين المهمة الخاص بها يتجاوز الحد الأقصى للحجم المسموح به البالغ 100 كيبيبايت (KiB).
#XMSG: Task log message for failure in completing the Notification task
notificationTaskCompletionFailure=فشل إكمال مهمة الإشعار "{0}".
#XMSG: Task log message when starting a Notification task.
notificationTaskRunStart=جارٍ بدء مهمة الإشعار "{0}".
#XMSG: Task log message for a successful Notification task completion
notificationTaskCompletionSuccess=اكتملت مهمة الإشعار "{0}".
#XFLD: Label for frequency column
everyLabel=كل
#XFLD: Plural Recurrence text for Hour
hoursLabel=ساعات
#XFLD: Plural Recurrence text for Day
daysLabel=أيام
#XFLD: Plural Recurrence text for Month
monthsLabel=شهور
#XFLD: Plural Recurrence text for Minutes
minutesLabel=دقائق
