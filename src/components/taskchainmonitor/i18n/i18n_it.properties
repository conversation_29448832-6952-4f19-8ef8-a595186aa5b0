

#XFLD: Task Chain header
headerTxt=Catene di task ({0})
#XFLD: Task Chain header
headerTxtNew=Catene di task ({0})
#XTXT: Text for Schedule label
scheduleTxt=Pianifica
#XFLD: Text for Create schedule button
createScheduleTxt=Crea pianificazione
#XFLD: Text for edit schedule
editScheduleTxt=Modifica pianificazione
#XLFD: Text for delete schedule
deleteScheduleTxt=Elimina pianificazione
#XTXT: text for refresh button label
refrestTxt=Aggiorna
#XTXT: Text for Completed status
completedTxt=Completato
#XTX: Text for Running status
runningTxt=In esecuzione
#XTX: Text for failed status
failedTxt=Non riuscito
#XTX: Text for stopped status
stoppedTxt=Interrotto
#XTX: Text for stopping status
stoppingTxt=Interruzione in corso
#XFLD: Header for Chain name
chainNameLabel=Nome catena di task
#XFLD: Header for Chain name
chainNameLabelBus=Nome aziendale
#XFLD: Header for Chain name
chainNameLabelBusNew=Oggetto (nome aziendale)
#XFLD: Header for Chain name
chainNameLabelTech=Nome tecnico
#XFLD: Header for Chain name
chainNameLabelTechNew=Oggetto (nome tecnico)
#XFLD: Last Run Status label
lastRunStatuslabel=Stato ultima esecuzione
#XFLD: Last Run Status label
lastRunStatuslabelNew=Stato
#XFLD: Frequency Label
frequencyLabel=Frequenza
#XFLD: Frequency Label
frequencyLabelNew=Frequenza pianificata
#XFLD: Duration label
durationLabel=Durata
#XFLD: Duration label
durationLabelNew=Durata ultima esecuzione
#XFLD: Run Start label
runStartLabel=Avvio ultima esecuzione
#XFLD: Run end label
runEndLabel=Fine ultima esecuzione
#XFLD: Next Run label
nextRunlabel=Prossima esecuzione
#XFLD: Next Run label
nextRunlabelNew=Prossima esecuzione pianificata
#XTXT: Task chain logs link tooltip text
taskChainLogsTxt=Dettagli registro catena di task
#XTXT: Task chain logs link tooltip text
taskChainLogsTxtNew=Dettagli
#XTXT: Scheduled text
scheduledTxt=Pianificato
#XTXT: Paused text
pausedTxt=Sospeso
#XTXT: Execute button label
runLabel=Esegui
#XTXT: Execute button label
runLabelNew=Avvia esecuzione
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Esegui la catena di task
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Esecuzione catena di task avviata.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Esecuzione catena di task avviata per {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Esecuzione catena di task non riuscita.
#XFLD: Label for schedule owner column
txtScheduleOwner=Proprietario pianificazione
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Mostra l'autore creazione della pianificazione
#XMSG: Task log message for start chain
startChain=Avvio dell'esecuzione catena di task.
#XMSG: Task log message for load chain from repository
loadChainFromRepository=Catena di task caricata e inizializzata.
#XMSG: Task log message started task
taskStarted=Task {0} avviato.
#XMSG: Task log message for finished task
taskFinished=Il task {0} è terminato con lo stato {1}.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Caricamento della catena di task e preparazione dell''esecuzione di un totale di {0} task parte di questa catena.
#XMSG: Task log message for starting a subtask
chainStartSubtask=Avvio dell''esecuzione del task {0}. ID task = {1}.
#XMSG: Task log message for finishing a subtask
chainFinishSubtask=Task {0} concluso con stato {1}.
#XMSG: Task log message for indicating chain success
chainCompleted=Tutti i {0} task completati. Lo stato catena di task è impostato su Completato.
#XMSG: Task log message for indicating chain failure
chainFailed=Su un totale di {0} task, è stato possibile completare {1} task, mentre {2} non sono riusciti. Lo stato catena di task è impostato su Non riuscito.
#XMSG: Task log message for indicating chain cancelation
chainCanceled=Annullamento della catena di task avviato. L''ID registro del task di annullamento è {0}.
#XMSG: Task log message for indicating chain cancelation in the cancel task
chainCanceling=Annullamento della catena {0}.
#XMSG: Task log message for general chain runtime error
chainError=Si è verificato un errore imprevisto durante l'esecuzione della catena di task.
#XMSG: Task log message for starting the overwrite check.
chainOverwriteStart=Controllo della conclusione della catena di task con ID {0}.
#XMSG: Task log message indicating the chain is too old.
chainOverwriteTooOld=Impossibile trovare task da eseguire per la catena {0}. L''esecuzione è iniziata {1} minuti fa. Lo stato catena di task è impostato su Non riuscito.
#XMSG: Task log message indicating the chain was created too recently.
chainOverwriteTooRecent=Impossibile trovare task da eseguire per la catena {0}. L''esecuzione è iniziata {1} minuti fa. La catena di task è ancora in esecuzione.
#XMSG: Task log message indicating a recent heartbeat was found.
chainOverwriteHeartbeatAlive=Catena di task {0} ancora in esecuzione. Completati: {1}, in esecuzione: {2}, non riusciti: {3}, avviati: {4}, non avviati: {5}.
#XMSG: Task log message for a chain that is not running anymore but not everything was triggered.
chainOverwriteNotAllTriggered=Si è verificato un errore durante l''esecuzione della catena di task {0}, quindi non è stato possibile avviare tutti i task. Completati: {1}, in esecuzione: {2}, non riusciti: {3}, avviati: {4}, non avviati: {5}. Questa catena di task è stata sovrascritta.
#XMSG: Task log message for a chain that contains a failed subtask.
chainOverwriteAnySubtaskFailed=Catena di task {0} conclusa. Un task della catena non è riuscito, la catena è impostata su Non riuscito.
#XMSG: Task log message could not determine status.
chainOverwriteNotDetermined=Un errore imprevisto impedisce l'accesso allo stato del task. Riprovare e contattare il supporto SAP se l'errore persiste.
#XMSG: Task log message could not take over
failedTakeover=Impossibile prendere in carico il task esistente.
#XMSG: Task log parallel check error
parallelCheckError=Impossibile elaborare il task perché un altro task è in esecuzione e lo sta già bloccando.
#XMSG: Task log parallel task runnig error
parallelTaskRunning=È già in esecuzione un task in conflitto.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Stato {0} durante l''esecuzione con ID correlazione {1}.
#XMSG: Task log message successful takeover
successTakeover=È stato necessario rilasciare il blocco rimanente; il nuovo blocco per questo task è impostato.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Il blocco di questo task è stato rilevato da un altro task.
#XMSG: Schedule created alert message
createScheduleSuccess=Pianificazione creata
#XMSG: Task log message for taskFinishedAt
taskFinishedAt=Task {0} concluso alle {2} con stato {1}.
#XMSG: Placeholder symbol for empty columns
emptyCol=--
#XMSG: Paused Text
txtExpired=Scaduto
#XMSG: Update task schedule success Toast message
updateScheduleSuccess=Pianificazione aggiornata
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Pianificazione eliminata.
#XMSG: Task chain might have children error text
taskChainNoChildErrorText=La catena di task potrebbe avere elementi subordinati non visualizzati perché il task non è riuscito prima che il piano potesse essere generato.
#XMSG: Task chain repair recent failed run label
retryRunLabel=Riprova ultima esecuzione
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Esecuzione nuovo tentativo catena di task avviata
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Esecuzione nuovo tentativo catena di task non riuscita
#XMSG: chain repair message
chainRetried=Esecuzione nuovo tentativo catena di task avviata dall''utente {0}
#XMSG: chain not found during run
chainNotFoundDuringRun=Impossibile trovare la catena di task {0}. Verificarne l''esistenza tramite il Generatore di dati e assicurarsi che la catena sia distribuita.
#XMSG: chain is not DAG
chainNotDag=Impossibile avviare la catena di task {0}. La sua struttura non è valida. Verificare la catena di task nel Generatore di dati.
#XMSG: chain has not valid parameters
notValidParameters=Impossibile avviare la catena di task {0}. Uno o più dei suoi parametri non sono validi. Verificare la catena di task nel Generatore di dati.
#XMSG: Configuration parameter value size exceeds the maximum allowed limit
configurationSizeLimitError=Impossibile avviare la catena di task {0}. Le dimensioni della configurazione di un task superano le dimensioni massime consentite di 100 kibibyte (KiB).
#XMSG: Task {0} has input parameters
taskHasInputParameters=Il task {0} ha parametri di input.
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=È già in esecuzione un task in conflitto
#XMSG: error message for reading data from backend
txtReadBackendError=Sembra che si sia verificato un errore durante la lettura dal backend.
##XMSG: error message for admission control rejection
admissionControlError=Il task non è riuscito a causa di un rifiuto del controllo accettazione SAP HANA.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Assegna pianificazione a me
#XBUT: Pause schedule menu label
pauseScheduleLabel=Metti pianificazione in pausa
#XBUT: Resume schedule menu label
resumeScheduleLabel=Riprendi pianificazione
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Si è verificato un errore durante la rimozione delle pianificazioni.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Si è verificato un errore durante l'assegnazione delle pianificazioni.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Si è verificato un errore durante la messa in pausa delle pianificazioni.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Si è verificato un errore durante la ripresa delle pianificazioni.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Eliminazione di {0} pianificazioni
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Modifica del proprietario di {0} pianificazioni
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Messa in pausa di {0} pianificazione
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Ripresa di {0} pianificazioni
#XBUT: Select Columns Button
selectColumnsBtn=Seleziona colonne
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Sembra che si sia verificato un errore nell'ultimo tentativo di esecuzione, poiché l'esecuzione del task precedente non è riuscita prima che il piano potesse essere generato.
#XFLD: Refresh tooltip
TEXT_REFRESH=Aggiorna
#XFLD: Select Columns tooltip
text_selectColumns=Seleziona colonne

#Messages for BW Process Chain task log of the Data Integration Monitor
#XMSG: Task log message for successfully starting a BW process chain
startBwProcessChainSuccess=La catena di processi BW "{0}" è stata avviata correttamente nel tenant del ponte SAP BW.
#XMSG: Task log message for skipping a BW process chain execution
bwProcessChainSkipped=La catena di processi BW "{0}" è stata saltata a causa dell''indisponibilità di nuovi dati o di un''esecuzione precedente non riuscita.
#XMSG: Task log message for failure in starting a BW process chain
startBwProcessChainFailure=L''avvio della catena di processi BW "{0}" non è riuscito nel tenant del ponte SAP BW.
#XMSG: Task log message for failure in getting the status of a BW process chain
getBwProcessChainStatusFailure=Il task non è riuscito perché non è stato possibile recuperare lo stato della catena di processi BW "{0}" a causa di un problema di connessione al ponte SAP BW. Aprire l''applicazione "Connessioni" e convalidare la connessione al ponte SAP BW nello spazio "{1}". Se non si ha accesso all''applicazione "Connessioni", contattare l''amministratore.
#XMSG: Task log message for failure in completing a BW process chain
completeBwProcessChainFailure=Il completamento della catena di processi BW "{0}" non è riuscito nel tenant del ponte SAP BW.
#XMSG: Task log message for the clickable text of the SAP BW Bridge Monitor link
bwBridgeMonitorLinkText=Visualizza i dettagli nel monitor del ponte SAP BW
#XMSG warning message while retrieving the task log messages if the user does not have access to the SAP BW Bridge space or to the shared object's space
missingPrivilegeOnSpaceError=Si è verificato un problema nel recupero dei messaggi o non si dispone dell'autorizzazione necessaria per visualizzarli.

#Messages for API task log of the Data Integration Monitor
#XMSG: Task log message for displaying the API response
viewResponse=Risposta ricevuta per "{0}".
#XMSG: Task log message for empty ID in the API response to retrieve the status
emptyJobId=Nessun ID restituito nel corpo della risposta per il percorso JSON: "{0}".
#XMSG: Task log message for empty success value in the API response
emptySuccessValue=Nessun valore di operazione riuscita restituito nel corpo della risposta per il percorso JSON: "{0}".
#XMSG: Task log message for empty error value in the API response
emptyErrorValue=Nessun valore di errore restituito nel corpo della risposta per il percorso JSON: "{0}".
#XMSG: Task log message for none of the success/error conditions matching for the values in the API response
conditionsNotFulfilled=Nessuna delle condizioni indicatore di operazione riuscita o errore specificate corrisponde ai valori nella risposta.
#XMSG: Task log message for the API response status being unsuccessful
statusNotOk=La risposta per "{1}" ha restituito un codice stato di operazione non riuscita: {0}.
#XMSG: Task log message for the API response body being empty
emptyResponseBody=Il corpo della risposta era vuoto.
#XMSG: Task log message for the API response location header being invalid
emptyLocationHeader=L'intestazione di ubicazione nella risposta era vuota.
#XMSG: Task log message for failure in retrieving the status of the invoked API
getStatusFailure=Impossibile recuperare lo stato dell'API chiamata.
#XMSG: Task log message for failure in completing the API task
completionFailure=Completamento del task API "{0}" non riuscito.
#XMSG: Task log message for a successful API task completion
apiCompleted=Task API "{0}" completato correttamente.
#XMSG: Task log message for an API task with an invalid configuration
invalidConfiguration=Configurazione del task API "{0}" non valida. Controllare la configurazione e riprovare.
#XMSG: Task log message for the API task being canceled
cancelStart=Richiesto l''annullamento del task API "{0}" con logId {1}.
#XMSG: Task log message for the cancelation of the an API task which is no longer running and cannot be canceled
unableToCancel=Il task API "{0}" con logId {1} non è più in esecuzione e non può essere annullato.
#XMSG: Task log message when starting an API task. It should carry the name of the API task.
apiRunStart=Avvio del task API "{0}".
#XMSG: Task log message when the task preparation is taking too long and times out
preparationTimeout=La preparazione del task richiede troppo tempo ed è scaduta.
#XMSG: Task log message when a task failed because a cancel task was triggered
apiRunCancelled=Il task di esecuzione con logId {0} è stato annullato dal task di annullamento {1}.
#XMSG: Task log message when a cancel task failed to cancel a run task
cancelFailure=Il task di annullamento con logId {1} non è riuscito ad annullare il task di esecuzione con logId {0}.
#XMSG: Task log message when configuration of the API task is too large
apiTaskconfigurationSizeLimitError=Il task API {0} non può essere avviato perché le dimensioni della relativa configurazione del task superano le dimensioni massime consentite di 100 kibibyte (KiB).
#XMSG: Task log message for failure in completing the Notification task
notificationTaskCompletionFailure=Completamento del task di notifica "{0}" non riuscito.
#XMSG: Task log message when starting a Notification task.
notificationTaskRunStart=Avvio del task di notifica "{0}".
#XMSG: Task log message for a successful Notification task completion
notificationTaskCompletionSuccess=Completamento del task di notifica "{0}" riuscito.
#XFLD: Label for frequency column
everyLabel=Ogni
#XFLD: Plural Recurrence text for Hour
hoursLabel=Ore
#XFLD: Plural Recurrence text for Day
daysLabel=Giorni
#XFLD: Plural Recurrence text for Month
monthsLabel=Mesi
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minuti
