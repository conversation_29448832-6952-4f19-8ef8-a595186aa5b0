

#XFLD: Task Chain header
headerTxt=<PERSON><PERSON><PERSON> zada<PERSON>({0})
#XFLD: Task Chain header
headerTxtNew=<PERSON><PERSON><PERSON> zada<PERSON> ({0})
#XTXT: Text for Schedule label
scheduleTxt=Raspored
#XFLD: Text for Create schedule button
createScheduleTxt=<PERSON><PERSON><PERSON> raspored
#XFLD: Text for edit schedule
editScheduleTxt=Uredi raspored
#XLFD: Text for delete schedule
deleteScheduleTxt=Izbriš<PERSON> raspored
#XTXT: text for refresh button label
refrestTxt=Osvježi
#XTXT: Text for Completed status
completedTxt=Dovršeno
#XTX: Text for Running status
runningTxt=Izvodi se
#XTX: Text for failed status
failedTxt=Nije uspjelo
#XTX: Text for stopped status
stoppedTxt=Zaustavljeno
#XTX: Text for stopping status
stoppingTxt=Zaustavljanje
#XFLD: Header for Chain name
chainNameLabel=Naziv lanca zadataka
#XFLD: Header for Chain name
chainNameLabelBus=Poslovni naziv
#XFLD: Header for Chain name
chainNameLabelBusNew=Objekt (poslovni naziv)
#XFLD: Header for Chain name
chainNameLabelTech=Tehnički naziv
#XFLD: Header for Chain name
chainNameLabelTechNew=Objekt (tehnički naziv)
#XFLD: Last Run Status label
lastRunStatuslabel=Status zadnjeg izvođenja
#XFLD: Last Run Status label
lastRunStatuslabelNew=Status
#XFLD: Frequency Label
frequencyLabel=Učestalost
#XFLD: Frequency Label
frequencyLabelNew=Raspoređena učestalost
#XFLD: Duration label
durationLabel=Trajanje
#XFLD: Duration label
durationLabelNew=Trajanje zadnjeg izvođenja
#XFLD: Run Start label
runStartLabel=Početak zadnjeg izvođenja
#XFLD: Run end label
runEndLabel=Završetak zadnjeg izvođenja
#XFLD: Next Run label
nextRunlabel=Sljedeće izvođenje
#XFLD: Next Run label
nextRunlabelNew=Raspoređeno sljedeće izvođenje
#XTXT: Task chain logs link tooltip text
taskChainLogsTxt=Pojedinosti zapisnika lanca zadataka
#XTXT: Task chain logs link tooltip text
taskChainLogsTxtNew=Pojedinosti
#XTXT: Scheduled text
scheduledTxt=Planirano
#XTXT: Paused text
pausedTxt=Pauzirano
#XTXT: Execute button label
runLabel=Izvedi
#XTXT: Execute button label
runLabelNew=Pokreni izvođenje
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Izvedite lanac zadataka
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Izvođenje lanca zadataka pokrenuto.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Izvođenje lanca zadataka pokrenuto za {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Izvođenje lanca zadataka nije uspjelo.
#XFLD: Label for schedule owner column
txtScheduleOwner=Vlasnik rasporeda
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Pokazuje tko je stvorio raspored
#XMSG: Task log message for start chain
startChain=Pokretanje izvođenja lanca zadataka.
#XMSG: Task log message for load chain from repository
loadChainFromRepository=Lanac zadataka učitan i inicijaliziran.
#XMSG: Task log message started task
taskStarted=Zadatak {0} pokrenut.
#XMSG: Task log message for finished task
taskFinished=Zadatak {0} završen sa statusom {1}.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Učitavanje lanca zadataka i priprema za izvođenje ukupno ovoliko zadataka {0} koji su dio ovog lanca.
#XMSG: Task log message for starting a subtask
chainStartSubtask=Pokretanje zadatka {0} za izvođenje. ID zadatka = {1}.
#XMSG: Task log message for finishing a subtask
chainFinishSubtask=Zadatak {0} završen sa statusom {1}.
#XMSG: Task log message for indicating chain success
chainCompleted=Dovršeno je svih ovoliko zadataka: {0}. Status lanca zadataka postavljen je na Dovršeno.
#XMSG: Task log message for indicating chain failure
chainFailed=Od ukupno {0} zadataka, {1} zadataka bilo je moguće dovršiti, a {2} zadataka nije uspjelo. Status lanca zadataka postavljen je na Neuspješno.
#XMSG: Task log message for indicating chain cancelation
chainCanceled=Pokrenuto je otkazivanje izvođenja lanca zadataka. ID zapisnika zadatka za otkazivanje jest {0}.
#XMSG: Task log message for indicating chain cancelation in the cancel task
chainCanceling=Otkazivanje lanca {0}.
#XMSG: Task log message for general chain runtime error
chainError=Došlo je do neočekivane pogreške tijekom izvođenja lanca zadataka.
#XMSG: Task log message for starting the overwrite check.
chainOverwriteStart=Provjera je li lanac zadataka s ID-om {0} završio.
#XMSG: Task log message indicating the chain is too old.
chainOverwriteTooOld=Nije moguće pronaći zadatke za izvođenje za lanac {0}. Izvođenje je bilo prije ovoliko minuta {1}. Status lanca zadataka postavljen je na Neuspješno.
#XMSG: Task log message indicating the chain was created too recently.
chainOverwriteTooRecent=Nije moguće pronaći zadatke za izvođenje za lanac {0}. Izvođenje je bilo prije ovoliko minuta {1}. Lanac zadataka još uvijek se izvodi.
#XMSG: Task log message indicating a recent heartbeat was found.
chainOverwriteHeartbeatAlive=Lanac zadataka {0} još uvijek se izvodi. Dovršeno: {1}, izvodi se: {2}, neuspješno: {3}, pokrenuto: {4}, nepokrenuto: {5}.
#XMSG: Task log message for a chain that is not running anymore but not everything was triggered.
chainOverwriteNotAllTriggered=Došlo je do pogreške tijekom izvođenja lanca zadataka {0} i svi zadaci nisu se mogli pokrenuti . Dovršeno: {1}, izvodi se: {2}, neuspješno: {3}, pokrenuto: {4}, nepokrenuto: {5}. Preko ovog se lanca zadataka pisalo.
#XMSG: Task log message for a chain that contains a failed subtask.
chainOverwriteAnySubtaskFailed=Lanac zadataka {0} završen. Jedan zadatak lanca nije uspio, postavljanje lanca na Neuspješno.
#XMSG: Task log message could not determine status.
chainOverwriteNotDetermined=Neočekivana pogreška sprječava nas u pristupu statusu zadatka. Pokušajte ponovo i obratite se SAP podršci ako pogreška potraje.
#XMSG: Task log message could not take over
failedTakeover=Preuzimanje postojećeg zadatka nije uspjelo.
#XMSG: Task log parallel check error
parallelCheckError=Zadatak nije moguće obraditi jer se izvodi drugi zadatak koji ga blokira.
#XMSG: Task log parallel task runnig error
parallelTaskRunning=Zadatak u sukobu već se izvodi.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Status {0} tijekom izvođenja s ID-om korelacije {1}.
#XMSG: Task log message successful takeover
successTakeover=Preostalo zaključavanje moralo se otpustiti. Novo je zaključavanje za ovaj zadatak postavljeno.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Zaključavanje ovog zadatka preuzeo je drugi zadatak.
#XMSG: Schedule created alert message
createScheduleSuccess=Raspored stvoren
#XMSG: Task log message for taskFinishedAt
taskFinishedAt=Zadatak {0} završen u {2} sa statusom {1}.
#XMSG: Placeholder symbol for empty columns
emptyCol=--
#XMSG: Paused Text
txtExpired=Isteklo
#XMSG: Update task schedule success Toast message
updateScheduleSuccess=Raspored ažuriran
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Raspored izbrisan.
#XMSG: Task chain might have children error text
taskChainNoChildErrorText=Lanac zadataka možda ima podređene lance koji nisu prikazani jer zadatak nije uspio prije nego što se plan mogao generirati.
#XMSG: Task chain repair recent failed run label
retryRunLabel=Ponovo pokušaj posljednje izvođenje
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Ponovni pokušaj izvođenja lanca zadataka pokrenut
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Ponovni pokušaj izvođenja lanca zadataka nije uspio
#XMSG: chain repair message
chainRetried=Ponovni pokušaj lanca zadataka pokrenuo je korisnik {0}
#XMSG: chain not found during run
chainNotFoundDuringRun=Nije moguće pronaći lanac zadataka {0}. Preko sastavljača podataka provjerite postoji li i provjerite je li lanac uveden.
#XMSG: chain is not DAG
chainNotDag=Nije moguće pokrenuti lanac zadataka {0}. Njegova je struktura nevaljana. Provjerite lanac zadataka u sastavljaču podataka.
#XMSG: chain has not valid parameters
notValidParameters=Nije moguće pokrenuti lanac zadataka {0}. Jedan ili više njegovih parametara nije valjano. Provjerite lanac zadataka u sastavljaču podataka.
#XMSG: Configuration parameter value size exceeds the maximum allowed limit
configurationSizeLimitError=Lanac zadataka {0} ne može se pokrenuti. Veličina konfiguracije zadatka u lancu prekoračuje maksimalnu dopuštenu veličinu od 100 kibibajta (KiB).
#XMSG: Task {0} has input parameters
taskHasInputParameters=Zadatak {0} ima ulazne parametre.
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Zadatak u sukobu već se izvodi
#XMSG: error message for reading data from backend
txtReadBackendError=Čini se da je došlo do pogreške pri čitanju iz pozadine.
##XMSG: error message for admission control rejection
admissionControlError=Zadatak nije uspio zbog odbijanja kontrole prijema SAP HANA.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Dodijeli mi raspored
#XBUT: Pause schedule menu label
pauseScheduleLabel=Pauziraj raspored
#XBUT: Resume schedule menu label
resumeScheduleLabel=Nastavi s rasporedom
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Došlo je do pogreške tijekom uklanjanja rasporeda.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Došlo je do pogreške tijekom dodjele rasporeda.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Došlo je do pogreške tijekom pauziranja rasporeda.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Došlo je do pogreške tijekom nastavka rasporeda.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Brisanje {0} rasporeda
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Promjena vlasnika {0} rasporeda
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Pauziranje {0} rasporeda
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Nastavak {0} rasporeda
#XBUT: Select Columns Button
selectColumnsBtn=Odabir stupaca
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Čini se da je došlo do pogreške pri ponovnom pokušaju posljednjeg izvođenja jer prethodno izvođenje zadatka nije uspjelo prije nego što se plan mogao generirati.
#XFLD: Refresh tooltip
TEXT_REFRESH=Osvježi
#XFLD: Select Columns tooltip
text_selectColumns=Odabir stupaca

#Messages for BW Process Chain task log of the Data Integration Monitor
#XMSG: Task log message for successfully starting a BW process chain
startBwProcessChainSuccess=Lanac procesa BW "{0}" uspješno se pokrenuo u zakupcu mosta za SAP BW.
#XMSG: Task log message for skipping a BW process chain execution
bwProcessChainSkipped=Lanac procesa BW "{0}" preskočen je zbog nedostupnosti novih podataka ili zbog neuspjeha prethodnog izvršenja.
#XMSG: Task log message for failure in starting a BW process chain
startBwProcessChainFailure=Lanac procesa BW "{0}" nije se uspio pokrenuti u zakupcu mosta za SAP BW.
#XMSG: Task log message for failure in getting the status of a BW process chain
getBwProcessChainStatusFailure=Zadatak nije uspio jer nismo mogli dohvatiti status lanca procesa BW "{0}" zbog problema s uspostavljanjem veze s mostom za SAP BW. Otvorite aplikaciju "Veze" i validirajte vezu s mostom za SAP BW u prostoru "{1}". Ako nemate pristup aplikaciji "Veze", obratite se svom administratoru.
#XMSG: Task log message for failure in completing a BW process chain
completeBwProcessChainFailure=Lanac procesa BW "{0}" nije se uspio dovršiti u zakupcu mosta za SAP BW.
#XMSG: Task log message for the clickable text of the SAP BW Bridge Monitor link
bwBridgeMonitorLinkText=Prikaži pojedinosti u nadzoru mosta za SAP BW.
#XMSG warning message while retrieving the task log messages if the user does not have access to the SAP BW Bridge space or to the shared object's space
missingPrivilegeOnSpaceError=Došlo je do problema pri dohvaćanju poruka ili nemate potrebno dopuštenje za njihov prikaz.

#Messages for API task log of the Data Integration Monitor
#XMSG: Task log message for displaying the API response
viewResponse=Odgovor primljen za "{0}".
#XMSG: Task log message for empty ID in the API response to retrieve the status
emptyJobId=U tijelu odgovora nije vraćen ID za stazu JSON -a: "{0}".
#XMSG: Task log message for empty success value in the API response
emptySuccessValue=Vrijednost uspjeha nije vraćena u tijelu odgovora za stazu JSON -a: "{0}".
#XMSG: Task log message for empty error value in the API response
emptyErrorValue=Vrijednost pogreške nije vraćena u tijelu odgovora za stazu JSON -a: "{0}".
#XMSG: Task log message for none of the success/error conditions matching for the values in the API response
conditionsNotFulfilled=Nijedan od navedenih uvjeta pokazatelja uspjeha ili pogreške ne podudara se s vrijednostima u odgovoru.
#XMSG: Task log message for the API response status being unsuccessful
statusNotOk=Odgovor za "{1}" vratio je neuspješnu šifru statusa: {0}.
#XMSG: Task log message for the API response body being empty
emptyResponseBody=Tijelo odgovora bilo je prazno.
#XMSG: Task log message for the API response location header being invalid
emptyLocationHeader=Zaglavlje lokacije u odgovoru bilo je prazno.
#XMSG: Task log message for failure in retrieving the status of the invoked API
getStatusFailure=Status pozvanog API-ja nije moguće dohvatiti.
#XMSG: Task log message for failure in completing the API task
completionFailure=Zadatak API-ja "{0}" nije se uspio dovršiti.
#XMSG: Task log message for a successful API task completion
apiCompleted=Zadatak API-ja "{0}" uspješno je dovršen.
#XMSG: Task log message for an API task with an invalid configuration
invalidConfiguration=Konfiguracija zadatka API-ja "{0}" nije valjana. Provjerite konfiguraciju i pokušajte ponovo.
#XMSG: Task log message for the API task being canceled
cancelStart=Zatraženo otkazivanje zadatka API-ja "{0}" s logId {1}.
#XMSG: Task log message for the cancelation of the an API task which is no longer running and cannot be canceled
unableToCancel=Zadatak API-ja "{0}" i logId {1} više se ne izvodi i nije ga moguće otkazati.
#XMSG: Task log message when starting an API task. It should carry the name of the API task.
apiRunStart=Pokretanje zadatka API-ja "{0}".
#XMSG: Task log message when the task preparation is taking too long and times out
preparationTimeout=Priprema zadatka traje predugo i vrijeme je isteklo.
#XMSG: Task log message when a task failed because a cancel task was triggered
apiRunCancelled=Zadatak izvođenja s logId {0} otkazan je zadatkom otkazivanja {1}.
#XMSG: Task log message when a cancel task failed to cancel a run task
cancelFailure=Zadatak izvođenja s logId {1} nije uspio otkazati zadatak izvođenja s logId {0}.
#XMSG: Task log message when configuration of the API task is too large
apiTaskconfigurationSizeLimitError=Zadatak API-ja {0} ne može se pokrenuti jer veličina konfiguracije zadatka prekoračuje maksimalnu dopuštenu veličinu od 100 kibibajta (KiB).
#XMSG: Task log message for failure in completing the Notification task
notificationTaskCompletionFailure=Zadatak obavijesti "{0}" nije se uspio dovršiti.
#XMSG: Task log message when starting a Notification task.
notificationTaskRunStart=Pokretanje zadatka obavijesti "{0}".
#XMSG: Task log message for a successful Notification task completion
notificationTaskCompletionSuccess=Zadatak obavijesti "{0}" dovršen je.
#XFLD: Label for frequency column
everyLabel=Svakih
#XFLD: Plural Recurrence text for Hour
hoursLabel=Sati
#XFLD: Plural Recurrence text for Day
daysLabel=Dani
#XFLD: Plural Recurrence text for Month
monthsLabel=Mjeseci
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minute
