

#XFLD: Task Chain header
headerTxt=Ланци задатака ({0})
#XFLD: Task Chain header
headerTxtNew=Ланци задатака ({0})
#XTXT: Text for Schedule label
scheduleTxt=План
#XFLD: Text for Create schedule button
createScheduleTxt=Креирај план
#XFLD: Text for edit schedule
editScheduleTxt=Уреди план
#XLFD: Text for delete schedule
deleteScheduleTxt=Избриши план
#XTXT: text for refresh button label
refrestTxt=Освежи
#XTXT: Text for Completed status
completedTxt=Завршено
#XTX: Text for Running status
runningTxt=Изводи се
#XTX: Text for failed status
failedTxt=Није успело
#XTX: Text for stopped status
stoppedTxt=Заустављено
#XTX: Text for stopping status
stoppingTxt=Зауставља се
#XFLD: Header for Chain name
chainNameLabel=Назив ланца задатака
#XFLD: Header for Chain name
chainNameLabelBus=Пословни назив
#XFLD: Header for Chain name
chainNameLabelBusNew=Објекат (пословни назив)
#XFLD: Header for Chain name
chainNameLabelTech=Технички назив
#XFLD: Header for Chain name
chainNameLabelTechNew=Објекат (технички назив)
#XFLD: Last Run Status label
lastRunStatuslabel=Статус последњег извођења
#XFLD: Last Run Status label
lastRunStatuslabelNew=Статус
#XFLD: Frequency Label
frequencyLabel=Учесталост
#XFLD: Frequency Label
frequencyLabelNew=Планирана учесталост
#XFLD: Duration label
durationLabel=Трајање
#XFLD: Duration label
durationLabelNew=Трајање последњег извођења
#XFLD: Run Start label
runStartLabel=Почетак последњег извођења
#XFLD: Run end label
runEndLabel=Завршетак последњег извођења
#XFLD: Next Run label
nextRunlabel=Следеће извођење
#XFLD: Next Run label
nextRunlabelNew=Планирано следеће извођење
#XTXT: Task chain logs link tooltip text
taskChainLogsTxt=Детаљи протокола ланца задатака
#XTXT: Task chain logs link tooltip text
taskChainLogsTxtNew=Детаљи
#XTXT: Scheduled text
scheduledTxt=Планирано
#XTXT: Paused text
pausedTxt=Паузирано
#XTXT: Execute button label
runLabel=Изведи
#XTXT: Execute button label
runLabelNew=Покрени извођење
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Изведи ланац задатака
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Извођење ланца задатака је покренуто.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Извођење ланца задатака је покренуто за {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Није успело извођење ланца задатака.
#XFLD: Label for schedule owner column
txtScheduleOwner=Одговорно лице за план
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Показује ко је креирао план
#XMSG: Task log message for start chain
startChain=Покретање извођења ланца задатака.
#XMSG: Task log message for load chain from repository
loadChainFromRepository=Ланац задатака учитан и покренут.
#XMSG: Task log message started task
taskStarted=Задатак {0} је покренут.
#XMSG: Task log message for finished task
taskFinished=Задатак {0} је завршен са статусом {1}.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Учитавање ланца задатака и припрема извођења за укупно {0} задатака који су део овог ланца.
#XMSG: Task log message for starting a subtask
chainStartSubtask=Покретање задатка {0} за извођење. ID задатка = {1}.
#XMSG: Task log message for finishing a subtask
chainFinishSubtask=Задатак {0} завршен са статусом {1}.
#XMSG: Task log message for indicating chain success
chainCompleted=Завршено је свих {0} задатака. Статус ланца задатака је постављен на Завршено.
#XMSG: Task log message for indicating chain failure
chainFailed=Од укупно {0} задатака, завршено је {1} задатака, a није успело {2} задатака. Статус ланца задатака је постављен на Није успело.
#XMSG: Task log message for indicating chain cancelation
chainCanceled=Отказивање извођења ланца задатака је покренуто. ID протокола задатка отказивања је {0}.
#XMSG: Task log message for indicating chain cancelation in the cancel task
chainCanceling=Отказивање ланца {0}.
#XMSG: Task log message for general chain runtime error
chainError=Неочекивана грешка при извођењу ланца задатака.
#XMSG: Task log message for starting the overwrite check.
chainOverwriteStart=Провера се да ли је ланац задатака са ID-ом {0} завршен.
#XMSG: Task log message indicating the chain is too old.
chainOverwriteTooOld=Нису нађени задаци за извођење за ланац {0}. Извођење траје {1} минута. Статус ланца задатака је постављен на Није успело.
#XMSG: Task log message indicating the chain was created too recently.
chainOverwriteTooRecent=Нису нађени задаци за извођење за ланац {0}. Извођење траје {1} минута. Ланац задатака се још увек изводи.
#XMSG: Task log message indicating a recent heartbeat was found.
chainOverwriteHeartbeatAlive=Ланац задатака {0} се још увек изводи. Завршено: {1}, Изводи се: {2}, Није успело: {3}, Покренуто: {4}, Није покренуто: {5}.
#XMSG: Task log message for a chain that is not running anymore but not everything was triggered.
chainOverwriteNotAllTriggered=Грешка при извођењу ланца задатака {0} и не могу се покренути сви задаци. Завршено: {1}, Изводи се: {2}, Није успело: {3}, Покренуто: {4}, Није покренуто: {5}. Овај ланац задатака је замењен.
#XMSG: Task log message for a chain that contains a failed subtask.
chainOverwriteAnySubtaskFailed=Ланац задатака {0} завршен. Један задатак ланца није успео, постављање ланца на Није успело.
#XMSG: Task log message could not determine status.
chainOverwriteNotDetermined=Неочекивана грешка нас спречава да приступимо статусу задатка. Покушајте поново и обратите се SAP подршци ако грешка потраје.
#XMSG: Task log message could not take over
failedTakeover=Није успело преузимање постојећег задатка.
#XMSG: Task log parallel check error
parallelCheckError=Задатак се не може обрадити јер се други задатак већ изводи и блокира овај задатак.
#XMSG: Task log parallel task runnig error
parallelTaskRunning=Задатак с конфликтом се већ изводи.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Статус {0} током извођења са ID-ом корелације {1}.
#XMSG: Task log message successful takeover
successTakeover=Преостала блокада се морала укинути. Нова блокада је постављена за овај задатак.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Блокаду овог задатка је преузео други задатак.
#XMSG: Schedule created alert message
createScheduleSuccess=План креиран
#XMSG: Task log message for taskFinishedAt
taskFinishedAt=Задатак {0} завршен у {2} са статусом {1}.
#XMSG: Placeholder symbol for empty columns
emptyCol=--
#XMSG: Paused Text
txtExpired=Истекло
#XMSG: Update task schedule success Toast message
updateScheduleSuccess=План ажуриран
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=План избрисан.
#XMSG: Task chain might have children error text
taskChainNoChildErrorText=Ланац задатака можда има подређене елементе који нису приказани јер задатак није успео пре генерисања плана.
#XMSG: Task chain repair recent failed run label
retryRunLabel=Поново покушај последње извођење
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Извођење поновног покушаја ланца задатака је покренуто
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Извођење поновног покушаја ланца задатака није успело
#XMSG: chain repair message
chainRetried=Поновни покушај ланца задатака покренуо корисник {0}
#XMSG: chain not found during run
chainNotFoundDuringRun=Није нађен ланац задатака {0}. Проверите да ли постоји помоћу генератора података и уверите се да је ланац имплементиран.
#XMSG: chain is not DAG
chainNotDag=Није могуће покренути ланац задатака {0}. Његова структура је неважећа. Проверите ланац задатака у генератору података.
#XMSG: chain has not valid parameters
notValidParameters=Није могуће покренути ланац задатака {0}. Има најмање један неважећи параметар. Проверите ланац задатака у генератору података.
#XMSG: Configuration parameter value size exceeds the maximum allowed limit
configurationSizeLimitError=Није могуће покренути ланац задатака {0}. Величина конфигурације задатка у ланцу прекорачује максималну дозвољену величину од 100 кибибајта (KiB).
#XMSG: Task {0} has input parameters
taskHasInputParameters=Задатак {0} има параметре уноса.
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Задатак с конфликтом се већ изводи
#XMSG: error message for reading data from backend
txtReadBackendError=Изгледа да је дошло до грешке при читању из back-end-а.
##XMSG: error message for admission control rejection
admissionControlError=Задатак није успео због одбијања контроле пријема SAP HANA.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Додели ми план
#XBUT: Pause schedule menu label
pauseScheduleLabel=Паузирај план
#XBUT: Resume schedule menu label
resumeScheduleLabel=Настави план
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Грешка при уклањању планова.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Грешка при додели планова.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Грешка при паузирању планова.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Грешка при настављању планова.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Брисање {0} планова
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Промена власника {0} планова
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Паузирање {0} планова
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Настављање {0} планова
#XBUT: Select Columns Button
selectColumnsBtn=Одабери колоне
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Изгледа да је дошло до грешке при последњем извођењу поновног покушаја јер претходно извођење задатка није успело пре генерисања плана.
#XFLD: Refresh tooltip
TEXT_REFRESH=Освежи
#XFLD: Select Columns tooltip
text_selectColumns=Одабери колоне

#Messages for BW Process Chain task log of the Data Integration Monitor
#XMSG: Task log message for successfully starting a BW process chain
startBwProcessChainSuccess=Ланац процеса BW "{0}" је успешно покренут за клијента система SAP BW Bridge.
#XMSG: Task log message for skipping a BW process chain execution
bwProcessChainSkipped=Ланац процеса BW "{0}" је прескочен због недоступности нових података или због грешке у претходном извршењу.
#XMSG: Task log message for failure in starting a BW process chain
startBwProcessChainFailure=Покретање ланца процеса BW "{0}" није успело у клијенту SAP BW Bridge-а.
#XMSG: Task log message for failure in getting the status of a BW process chain
getBwProcessChainStatusFailure=Задатак није успео јер нисмо могли да позовемо статус ланца процеса BW "{0}" због проблема са успостављањем везе са системом SAP BW Bridge. Отворите апликацију "Везе" и валидирајте везу са системом SAP BW Bridge у простору "{1}". Ако немате приступ апликацији "Везе", обратите се администратору.
#XMSG: Task log message for failure in completing a BW process chain
completeBwProcessChainFailure=Завршетак ланца процеса BW "{0}" није успео у клијенту SAP BW Bridge-а.
#XMSG: Task log message for the clickable text of the SAP BW Bridge Monitor link
bwBridgeMonitorLinkText=Погледајте детаље у надзору система SAP BW Bridge
#XMSG warning message while retrieving the task log messages if the user does not have access to the SAP BW Bridge space or to the shared object's space
missingPrivilegeOnSpaceError=Проблем при позивању порука или немате неопходну дозволу да их видите.

#Messages for API task log of the Data Integration Monitor
#XMSG: Task log message for displaying the API response
viewResponse=Одговор примљен за "{0}".
#XMSG: Task log message for empty ID in the API response to retrieve the status
emptyJobId=ID није враћен за главни део одговора за пут JSON: "{0}".
#XMSG: Task log message for empty success value in the API response
emptySuccessValue=Вредност успеха није враћена у главном делу одговора за пут JSON: "{0}".
#XMSG: Task log message for empty error value in the API response
emptyErrorValue=Вредност грешке није враћена у главном делу одговора за пут JSON: "{0}".
#XMSG: Task log message for none of the success/error conditions matching for the values in the API response
conditionsNotFulfilled=Ниједан од наведених услова показатеља успеха или грешке не подудара се с вредностима у одговору.
#XMSG: Task log message for the API response status being unsuccessful
statusNotOk=Одговор за "{1}" је приказао неуспешну шифру статуса: {0}.
#XMSG: Task log message for the API response body being empty
emptyResponseBody=Главни део одговора је празан.
#XMSG: Task log message for the API response location header being invalid
emptyLocationHeader=Заглавље локације у одговору је празно.
#XMSG: Task log message for failure in retrieving the status of the invoked API
getStatusFailure=Статус позваног API-ја није могуће позвати.
#XMSG: Task log message for failure in completing the API task
completionFailure=Задатак API-ја "{0}" није успешно завршен.
#XMSG: Task log message for a successful API task completion
apiCompleted=Задатак API-ја "{0}" је успешно завршен.
#XMSG: Task log message for an API task with an invalid configuration
invalidConfiguration=Конфигурација задатка API-ја "{0}" је неважећа. Проверите конфигурацију и покушајте поново.
#XMSG: Task log message for the API task being canceled
cancelStart=Захтевано отказивање API задатка „{0}” с logId-ом {1}.
#XMSG: Task log message for the cancelation of the an API task which is no longer running and cannot be canceled
unableToCancel=API задатак „{0}” с logId-ом {1} се више не изводи и не може се отказати.
#XMSG: Task log message when starting an API task. It should carry the name of the API task.
apiRunStart=Покретање API задатка „{0}”.
#XMSG: Task log message when the task preparation is taking too long and times out
preparationTimeout=Припрема задатка траје предуго и истекла је.
#XMSG: Task log message when a task failed because a cancel task was triggered
apiRunCancelled=Задатак извођења с logId-ом {0} је отказан задатком отказивања {1}.
#XMSG: Task log message when a cancel task failed to cancel a run task
cancelFailure=Задатак отказивања с logId-ом {1} није успео да откаже задатак извођења с logId-ом {0}.
#XMSG: Task log message when configuration of the API task is too large
apiTaskconfigurationSizeLimitError=Задатак API-ја {0} се не може покренути јер величина његове конфигурације задатка прекорачује максималну дозвољену величину од 100 кибибајта (KiB).
#XMSG: Task log message for failure in completing the Notification task
notificationTaskCompletionFailure=Задатак обавештења "{0}" није успешно завршен.
#XMSG: Task log message when starting a Notification task.
notificationTaskRunStart=Покретање задатка обавештењаа "{0}".
#XMSG: Task log message for a successful Notification task completion
notificationTaskCompletionSuccess=Задатак обавештења "{0}" је завршен.
#XFLD: Label for frequency column
everyLabel=Сваких
#XFLD: Plural Recurrence text for Hour
hoursLabel=сати
#XFLD: Plural Recurrence text for Day
daysLabel=дана
#XFLD: Plural Recurrence text for Month
monthsLabel=месеца/и
#XFLD: Plural Recurrence text for Minutes
minutesLabel=минута
