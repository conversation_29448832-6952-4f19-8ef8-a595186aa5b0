

#XFLD: Task Chain header
headerTxt=<PERSON><PERSON><PERSON> zada<PERSON> ({0})
#XFLD: Task Chain header
headerTxtNew=<PERSON><PERSON><PERSON> zada<PERSON> ({0})
#XTXT: Text for Schedule label
scheduleTxt=Plan
#XFLD: Text for Create schedule button
createScheduleTxt=Kreiraj plan
#XFLD: Text for edit schedule
editScheduleTxt=Uredi plan
#XLFD: Text for delete schedule
deleteScheduleTxt=Izbriši plan
#XTXT: text for refresh button label
refrestTxt=Osveži
#XTXT: Text for Completed status
completedTxt=Završeno
#XTX: Text for Running status
runningTxt=Izvodi se
#XTX: Text for failed status
failedTxt=Nije uspelo
#XTX: Text for stopped status
stoppedTxt=Zaustavljeno
#XTX: Text for stopping status
stoppingTxt=Zaustavlja se
#XFLD: Header for Chain name
chainNameLabel=Naziv lanca zadataka
#XFLD: Header for Chain name
chainNameLabelBus=Poslovni naziv
#XFLD: Header for Chain name
chainNameLabelBusNew=Objekat (poslovni naziv)
#XFLD: Header for Chain name
chainNameLabelTech=Tehnički naziv
#XFLD: Header for Chain name
chainNameLabelTechNew=Objekat (tehnički naziv)
#XFLD: Last Run Status label
lastRunStatuslabel=Status poslednjeg izvođenja
#XFLD: Last Run Status label
lastRunStatuslabelNew=Status
#XFLD: Frequency Label
frequencyLabel=Učestalost
#XFLD: Frequency Label
frequencyLabelNew=Planirana učestalost
#XFLD: Duration label
durationLabel=Trajanje
#XFLD: Duration label
durationLabelNew=Trajanje poslednjeg izvođenja
#XFLD: Run Start label
runStartLabel=Početak poslednjeg izvođenja
#XFLD: Run end label
runEndLabel=Završetak poslednjeg izvođenja
#XFLD: Next Run label
nextRunlabel=Sledeće izvođenje
#XFLD: Next Run label
nextRunlabelNew=Planirano sledeće izvođenje
#XTXT: Task chain logs link tooltip text
taskChainLogsTxt=Detalji protokola lanca zadataka
#XTXT: Task chain logs link tooltip text
taskChainLogsTxtNew=Detalji
#XTXT: Scheduled text
scheduledTxt=Planirano
#XTXT: Paused text
pausedTxt=Pauzirano
#XTXT: Execute button label
runLabel=Izvedi
#XTXT: Execute button label
runLabelNew=Pokreni izvođenje
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Izvedi lanac zadataka
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Izvođenje lanca zadataka je pokrenuto.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Izvođenje lanca zadataka je pokrenuto za {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Nije uspelo izvođenje lanca zadataka.
#XFLD: Label for schedule owner column
txtScheduleOwner=Odgovorno lice za plan
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Pokazuje ko je kreirao plan
#XMSG: Task log message for start chain
startChain=Pokretanje izvođenja lanca zadataka.
#XMSG: Task log message for load chain from repository
loadChainFromRepository=Lanac zadataka učitan i pokrenut.
#XMSG: Task log message started task
taskStarted=Zadatak {0} je pokrenut.
#XMSG: Task log message for finished task
taskFinished=Zadatak {0} je završen sa statusom {1}.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Učitavanje lanca zadataka i priprema izvođenja za ukupno {0} zadataka koji su deo ovog lanca.
#XMSG: Task log message for starting a subtask
chainStartSubtask=Pokretanje zadatka {0} za izvođenje. ID zadatka = {1}.
#XMSG: Task log message for finishing a subtask
chainFinishSubtask=Zadatak {0} završen sa statusom {1}.
#XMSG: Task log message for indicating chain success
chainCompleted=Završeno je svih {0} zadataka. Status lanca zadataka je postavljen na Završeno.
#XMSG: Task log message for indicating chain failure
chainFailed=Od ukupno {0} zadataka, završeno je {1} zadataka, a nije uspelo {2} zadataka. Status lanca zadataka je postavljen na Nije uspelo.
#XMSG: Task log message for indicating chain cancelation
chainCanceled=Otkazivanje izvođenja lanca zadataka je pokrenuto. ID protokola zadatka otkazivanja je {0}.
#XMSG: Task log message for indicating chain cancelation in the cancel task
chainCanceling=Otkazivanje lanca {0}.
#XMSG: Task log message for general chain runtime error
chainError=Neočekivana greška pri izvođenju lanca zadataka.
#XMSG: Task log message for starting the overwrite check.
chainOverwriteStart=Provera se da li je lanac zadataka sa ID-om {0} završen.
#XMSG: Task log message indicating the chain is too old.
chainOverwriteTooOld=Nisu nađeni zadaci za izvođenje za lanac {0}. Izvođenje traje {1} minuta. Status lanca zadataka je postavljen na Nije uspelo.
#XMSG: Task log message indicating the chain was created too recently.
chainOverwriteTooRecent=Nisu nađeni zadaci za izvođenje za lanac {0}. Izvođenje traje {1} minuta. Lanac zadataka se još uvek izvodi.
#XMSG: Task log message indicating a recent heartbeat was found.
chainOverwriteHeartbeatAlive=Lanac zadataka {0} se još uvek izvodi. Završeno: {1}, Izvodi se: {2}, Nije uspelo: {3}, Pokrenuto: {4}, Nije pokrenuto: {5}.
#XMSG: Task log message for a chain that is not running anymore but not everything was triggered.
chainOverwriteNotAllTriggered=Greška pri izvođenju lanca zadataka {0} i ne mogu se pokrenuti svi zadaci. Završeno: {1}, Izvodi se: {2}, Nije uspelo: {3}, Pokrenuto: {4}, Nije pokrenuto: {5}. Ovaj lanac zadataka je zamenjen.
#XMSG: Task log message for a chain that contains a failed subtask.
chainOverwriteAnySubtaskFailed=Lanac zadataka {0} završen. Jedan zadatak lanca nije uspeo, postavljanje lanca na Nije uspelo.
#XMSG: Task log message could not determine status.
chainOverwriteNotDetermined=Neočekivana greška nas sprečava da pristupimo statusu zadatka. Pokušajte ponovo i obratite se SAP podršci ako greška potraje.
#XMSG: Task log message could not take over
failedTakeover=Nije uspelo preuzimanje postojećeg zadatka.
#XMSG: Task log parallel check error
parallelCheckError=Zadatak se ne može obraditi jer se drugi zadatak već izvodi i blokira ovaj zadatak.
#XMSG: Task log parallel task runnig error
parallelTaskRunning=Zadatak s konfliktom se već izvodi.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Status {0} tokom izvođenja sa ID-om korelacije {1}.
#XMSG: Task log message successful takeover
successTakeover=Preostala blokada se morala ukinuti. Nova blokada je postavljena za ovaj zadatak.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Blokadu ovog zadatka je preuzeo drugi zadatak.
#XMSG: Schedule created alert message
createScheduleSuccess=Plan kreiran
#XMSG: Task log message for taskFinishedAt
taskFinishedAt=Zadatak {0} završen u {2} sa statusom {1}.
#XMSG: Placeholder symbol for empty columns
emptyCol=--
#XMSG: Paused Text
txtExpired=Isteklo
#XMSG: Update task schedule success Toast message
updateScheduleSuccess=Plan ažuriran
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Plan izbrisan.
#XMSG: Task chain might have children error text
taskChainNoChildErrorText=Lanac zadataka možda ima podređene elemente koji nisu prikazani jer zadatak nije uspeo pre generisanja plana.
#XMSG: Task chain repair recent failed run label
retryRunLabel=Ponovo pokušaj poslednje izvođenje
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Izvođenje ponovnog pokušaja lanca zadataka je pokrenuto
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Izvođenje ponovnog pokušaja lanca zadataka nije uspelo
#XMSG: chain repair message
chainRetried=Ponovni pokušaj lanca zadataka pokrenuo korisnik {0}
#XMSG: chain not found during run
chainNotFoundDuringRun=Nije nađen lanac zadataka {0}. Proverite da li postoji pomoću generatora podataka i uverite se da je lanac implementiran.
#XMSG: chain is not DAG
chainNotDag=Nije moguće pokrenuti lanac zadataka {0}. Njegova struktura je nevažeća. Proverite lanac zadataka u generatoru podataka.
#XMSG: chain has not valid parameters
notValidParameters=Nije moguće pokrenuti lanac zadataka {0}. Ima najmanje jedan nevažeći parametar. Proverite lanac zadataka u generatoru podataka.
#XMSG: Configuration parameter value size exceeds the maximum allowed limit
configurationSizeLimitError=Nije moguće pokrenuti lanac zadataka {0}. Veličina konfiguracije zadatka u lancu prekoračuje maksimalnu dozvoljenu veličinu od 100 kibibajta (KiB).
#XMSG: Task {0} has input parameters
taskHasInputParameters=Zadatak {0} ima parametre unosa.
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Zadatak s konfliktom se već izvodi
#XMSG: error message for reading data from backend
txtReadBackendError=Izgleda da je došlo do greške pri čitanju iz back-end-a.
##XMSG: error message for admission control rejection
admissionControlError=Zadatak nije uspeo zbog odbijanja kontrole prijema SAP HANA.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Dodeli mi plan
#XBUT: Pause schedule menu label
pauseScheduleLabel=Pauziraj plan
#XBUT: Resume schedule menu label
resumeScheduleLabel=Nastavi plan
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Greška pri uklanjanju planova.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Greška pri dodeli planova.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Greška pri pauziranju planova.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Greška pri nastavljanju planova.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Brisanje {0} planova
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Promena vlasnika {0} planova
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Pauziranje {0} planova
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Nastavljanje {0} planova
#XBUT: Select Columns Button
selectColumnsBtn=Odaberi kolone
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Izgleda da je došlo do greške pri poslednjem izvođenju ponovnog pokušaja jer prethodno izvođenje zadatka nije uspelo pre generisanja plana.
#XFLD: Refresh tooltip
TEXT_REFRESH=Osveži
#XFLD: Select Columns tooltip
text_selectColumns=Odaberi kolone

#Messages for BW Process Chain task log of the Data Integration Monitor
#XMSG: Task log message for successfully starting a BW process chain
startBwProcessChainSuccess=Lanac procesa BW "{0}" je uspešno pokrenut za klijenta sistema SAP BW Bridge.
#XMSG: Task log message for skipping a BW process chain execution
bwProcessChainSkipped=Lanac procesa BW "{0}" je preskočen zbog nedostupnosti novih podataka ili zbog greške u prethodnom izvršenju.
#XMSG: Task log message for failure in starting a BW process chain
startBwProcessChainFailure=Pokretanje lanca procesa BW "{0}" nije uspelo u klijentu SAP BW Bridge-a.
#XMSG: Task log message for failure in getting the status of a BW process chain
getBwProcessChainStatusFailure=Zadatak nije uspeo jer nismo mogli da pozovemo status lanca procesa BW "{0}" zbog problema sa uspostavljanjem veze sa sistemom SAP BW Bridge. Otvorite aplikaciju "Veze" i validirajte vezu sa sistemom SAP BW Bridge u prostoru "{1}". Ako nemate pristup aplikaciji "Veze", obratite se administratoru.
#XMSG: Task log message for failure in completing a BW process chain
completeBwProcessChainFailure=Završetak lanca procesa BW "{0}" nije uspeo u klijentu SAP BW Bridge-a.
#XMSG: Task log message for the clickable text of the SAP BW Bridge Monitor link
bwBridgeMonitorLinkText=Pogledajte detalje u nadzoru sistema SAP BW Bridge
#XMSG warning message while retrieving the task log messages if the user does not have access to the SAP BW Bridge space or to the shared object's space
missingPrivilegeOnSpaceError=Problem pri pozivanju poruka ili nemate neophodnu dozvolu da ih vidite.

#Messages for API task log of the Data Integration Monitor
#XMSG: Task log message for displaying the API response
viewResponse=Odgovor primljen za "{0}".
#XMSG: Task log message for empty ID in the API response to retrieve the status
emptyJobId=ID nije vraćen za glavni deo odgovora za put JSON: "{0}".
#XMSG: Task log message for empty success value in the API response
emptySuccessValue=Vrednost uspeha nije vraćena u glavnom delu odgovora za put JSON: "{0}".
#XMSG: Task log message for empty error value in the API response
emptyErrorValue=Vrednost greške nije vraćena u glavnom delu odgovora za put JSON: "{0}".
#XMSG: Task log message for none of the success/error conditions matching for the values in the API response
conditionsNotFulfilled=Nijedan od navedenih uslova pokazatelja uspeha ili greške ne podudara se s vrednostima u odgovoru.
#XMSG: Task log message for the API response status being unsuccessful
statusNotOk=Odgovor za "{1}" je prikazao neuspešnu šifru statusa: {0}.
#XMSG: Task log message for the API response body being empty
emptyResponseBody=Glavni deo odgovora je prazan.
#XMSG: Task log message for the API response location header being invalid
emptyLocationHeader=Zaglavlje lokacije u odgovoru je prazno.
#XMSG: Task log message for failure in retrieving the status of the invoked API
getStatusFailure=Status pozvanog API-ja nije moguće pozvati.
#XMSG: Task log message for failure in completing the API task
completionFailure=Zadatak API-ja "{0}" nije uspešno završen.
#XMSG: Task log message for a successful API task completion
apiCompleted=Zadatak API-ja "{0}" je uspešno završen.
#XMSG: Task log message for an API task with an invalid configuration
invalidConfiguration=Konfiguracija zadatka API-ja "{0}" je nevažeća. Proverite konfiguraciju i pokušajte ponovo.
#XMSG: Task log message for the API task being canceled
cancelStart=Zahtevano otkazivanje API zadatka „{0}” s logId-om {1}.
#XMSG: Task log message for the cancelation of the an API task which is no longer running and cannot be canceled
unableToCancel=API zadatak „{0}” s logId-om {1} se više ne izvodi i ne može se otkazati.
#XMSG: Task log message when starting an API task. It should carry the name of the API task.
apiRunStart=Pokretanje API zadatka „{0}”.
#XMSG: Task log message when the task preparation is taking too long and times out
preparationTimeout=Priprema zadatka traje predugo i istekla je.
#XMSG: Task log message when a task failed because a cancel task was triggered
apiRunCancelled=Zadatak izvođenja s logId-om {0} je otkazan zadatkom otkazivanja {1}.
#XMSG: Task log message when a cancel task failed to cancel a run task
cancelFailure=Zadatak otkazivanja s logId-om {1} nije uspeo da otkaže zadatak izvođenja s logId-om {0}.
#XMSG: Task log message when configuration of the API task is too large
apiTaskconfigurationSizeLimitError=Zadatak API-ja {0} se ne može pokrenuti jer veličina njegove konfiguracije zadatka prekoračuje maksimalnu dozvoljenu veličinu od 100 kibibajta (KiB).
#XMSG: Task log message for failure in completing the Notification task
notificationTaskCompletionFailure=Zadatak obaveštenja "{0}" nije uspešno završen.
#XMSG: Task log message when starting a Notification task.
notificationTaskRunStart=Pokretanje zadatka obaveštenjaa "{0}".
#XMSG: Task log message for a successful Notification task completion
notificationTaskCompletionSuccess=Zadatak obaveštenja "{0}" je završen.
#XFLD: Label for frequency column
everyLabel=Svakih
#XFLD: Plural Recurrence text for Hour
hoursLabel=sati
#XFLD: Plural Recurrence text for Day
daysLabel=dana
#XFLD: Plural Recurrence text for Month
monthsLabel=meseca/i
#XFLD: Plural Recurrence text for Minutes
minutesLabel=minuta
