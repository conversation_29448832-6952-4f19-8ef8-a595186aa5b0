/** @format */

import { isNotUndefined } from "@sap/deepsea-utils";
import { State } from "@sap/dwc-circuit-breaker";
import { isUndefined } from "lodash";
import { isDiMonitorImprovementsEnabled } from "../../abstractbuilder/commonservices/FeatureFlagCheck";
import {
  BaseController,
  BaseControllerClass,
  smartExtend,
} from "../../basecomponent/controller/BaseController.controller";
import { DataIntegrationComponentClass } from "../../dataintegration/Component";
import { TablePersonalizationClass } from "../../dataintegration/utility/TablePersonalization.controller";
import { MessageHandler } from "../../reuse/utility/MessageHandler";
import { ContentType, DataType, HttpMethod, ServiceCall } from "../../reuse/utility/ServiceCall";
import { UI5Require } from "../../reuse/utility/UIHelper";
import { isCircuitBreakerYellowStateEnabled } from "../../shell/featureState/CircuitBreakerYellowStateFeatureState";
import { ShellContainer } from "../../shell/utility/Container";
import { User } from "../../shell/utility/User";
import { setHelpScreenId } from "../../shell/utility/WebAssistantHelper";
import { Activity, ApplicationId } from "../../tasklog/utility/Constants";
import {
  ITTaskScheduleController,
  ITaskScheduleMassRequest,
  ITaskScheduleRequest,
  MassOps,
} from "../../taskscheduler/controller/TaskSchedule.controller";
import { getTaskScheduer, openSchedulePopover, recordAction } from "../../taskscheduler/utility/ScheduleUtil";
import { PreferenceChanges } from "../../userSettings/utility/Constants";
import { TaskChainMonitorFormatters } from "../utility/TaskChainMonitorFormatters";

export class TaskChainMonitorClass extends BaseControllerClass {
  dataIntegrationComponent: DataIntegrationComponentClass;
  router: sap.m.routing.Router;
  spaceId: string;
  private previousSpaceId: string;
  private sTableHeader: string;
  private searchField: sap.m.SearchField;
  private view: sap.ui.core.mvc.View;
  private privilegesRemoteConn: sap.ui.model.json.JSONModel;
  private privilegesSpaceFile: sap.ui.model.json.JSONModel;
  private replicationModel: sap.ui.model.json.JSONModel;
  tablePersoController: sap.ui.table.TablePersoController;
  private oBundle: any;
  private oBundleTask: any;
  private taskChainFormatter = TaskChainMonitorFormatters;
  private selectedChain: string;
  private sortObject: any;
  isReusableTaskScheduleFFEnabled: boolean;
  isAdoptionOfTaskSchedulerEnabled: boolean;
  selectedRowsSnapshot = [];
  // private remoteTableFormatter = RemoteTableMonitorFormatter;

  public onInit(): void {
    super.onInit();
    const i18nModel = new sap.ui.model.resource.ResourceModel({
      bundleName: require("../i18n/i18n.properties"),
    });
    const i18nModelTask = new sap.ui.model.resource.ResourceModel({
      bundleName: require("../../taskscheduler/i18n/i18n.properties"),
    });
    const isSDPEnabled = sap.ui.getCore().getModel("featureflags").getProperty("/DWC_DUMMY_SPACE_PERMISSIONS");
    this.isReusableTaskScheduleFFEnabled = sap.ui
      .getCore()
      .getModel("featureflags")
      .getProperty("/DWCO_REUSABLE_TASK_SCHEDULING_UI");
    this.isAdoptionOfTaskSchedulerEnabled = sap.ui
      .getCore()
      .getModel("featureflags")
      .getProperty("/DWCO_REUSABLE_TASK_SCHEDULER_ADOPTION");
    this.oBundle = i18nModel.getResourceBundle();
    this.oBundleTask = i18nModel.getResourceBundle();
    this.view = this.getView();
    this.router = sap.ui.core.UIComponent.getRouterFor(this) as sap.m.routing.Router;
    this.view.setModel(new sap.ui.model.json.JSONModel({}));
    this.view.setModel(i18nModel, "i18n");
    this.view.setModel(i18nModelTask, "i18n_task");

    // TODO: Remove privRemoteConnModel, privSpaceFile along with FF removal DWC_DUMMY_SPACE_PERMISSIONS
    this.privilegesRemoteConn = new sap.ui.model.json.JSONModel();
    this.view.setModel(this.privilegesRemoteConn, "privRemoteConnModel");
    this.privilegesSpaceFile = new sap.ui.model.json.JSONModel();
    this.view.setModel(this.privilegesSpaceFile, "privSpaceFile");

    this.replicationModel = new sap.ui.model.json.JSONModel();
    this.view.setModel(this.replicationModel, "replicationModel");

    const schedulingSettingModel = new sap.ui.model.json.JSONModel({
      newSchedule: false,
      editSchedule: false,
      deleteSchedule: false,
    });
    this.view.setModel(schedulingSettingModel, "schedulingModel");

    this.dataIntegrationComponent = this.getOwnerComponent() as DataIntegrationComponentClass;

    this.view.getModel().setProperty("/tableHeader", this.oBundle.getText("headerTxtNew", [0]));

    this.view.getModel().setProperty("/executeEnabled", false);
    this.view.getModel().setProperty("/retryEnabled", false);
    const oTable = this.getView().byId("taskChainsTable");
    if (isDiMonitorImprovementsEnabled()) {
      oTable["getColumns"]()[3].destroy();
      oTable["getColumns"]()[4].destroy();
    } else {
      oTable["getColumns"]()[2].destroy();
      oTable["getColumns"]()[6].destroy();
    }

    this.router.attachRouteMatched(null, async (event: sap.ui.base.Event) => {
      if (event.getParameter("name") === "taskChainMonitor") {
        setHelpScreenId("taskChainMonitor");
        this.clearSearchField();
        this.previousSpaceId = this.spaceId;
        this.spaceId = event.getParameter("arguments").spaceId;
        let scheduleDialog;
        if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
          await this.initScheduleDialog();
          scheduleDialog = this["newScheduleDialog"];
        } else {
          scheduleDialog = (
            this.getView().byId("taskChainSchedulingDialog") as sap.ui.core.mvc.View
          ).getController() as ITTaskScheduleController;
        }
        this.view.setBusyIndicatorDelay(0);
        this.view.setBusy(true);
        if (this.spaceId) {
          await this.onRefreshTable();
        }
        sap.ui
          .getCore()
          .getEventBus()
          .subscribe("userPreferences", "change", (_, __, preferenceChanges: PreferenceChanges) => {
            if (!preferenceChanges.changes.DWC_OBJECT_NAME_DISPLAY) {
              return;
            }
            this.onRefreshTable();
          });
        this.view.setBusy(false);
      }

      if (!isSDPEnabled) {
        const userPrivileges = ShellContainer.get().getPrivilegeService().getPrivilegesByType("DWC_DATAINTEGRATION");
        (this.view.getModel("privRemoteConnModel") as sap.ui.model.json.JSONModel).setProperty(
          "/read",
          userPrivileges.read
        );
        (this.view.getModel("privRemoteConnModel") as sap.ui.model.json.JSONModel).setProperty(
          "/update",
          userPrivileges.update
        );
        (this.view.getModel("privRemoteConnModel") as sap.ui.model.json.JSONModel).setProperty(
          "/execute",
          userPrivileges.execute
        );
        // have separate property to store privileges as update and execute properties in model are overwritten
        (this.view.getModel("privRemoteConnModel") as sap.ui.model.json.JSONModel).setProperty(
          "/canupdate",
          userPrivileges.update
        );
        (this.view.getModel("privRemoteConnModel") as sap.ui.model.json.JSONModel).setProperty(
          "/canexecute",
          userPrivileges.execute
        );
        /** Privileges for SpaceFile */
        const spacePriv = ShellContainer.get().getPrivilegeService().getPrivilegesByType("DWC_SPACEFILE");
        (this.view.getModel("privSpaceFile") as sap.ui.model.json.JSONModel).setProperty("/create", spacePriv.create);
        (this.view.getModel("privSpaceFile") as sap.ui.model.json.JSONModel).setProperty("/update", spacePriv.update);
        (this.view.getModel("privSpaceFile") as sap.ui.model.json.JSONModel).setProperty("/delete", spacePriv.delete);
      }

      if (this.previousSpaceId !== this.spaceId && this.previousSpaceId !== undefined) {
        this.clearFilter(this.getView().byId("taskChainsTable"));
      }
    });

    this.registerForColumnSettings();
    // create personalization controller
    UI5Require<typeof sap.ui.table.TablePersoController>("sap/ui/table/TablePersoController").then((TPC) => {
      this.tablePersoController = new TPC("tablePersonalizationDialogId", {
        table: this.getTable(),
        persoService: new TablePersonalizationClass("TPC"),
      });
    });

    // create Statement Editor
    UI5Require<typeof sap.ui.codeeditor.CodeEditor>("sap/ui/codeeditor/CodeEditor").then((Ce) => {
      this["codeEditor"] = new Ce("codeEditorIdTM", {
        type: "sql",
        maxLines: 15,
        height: "auto",
        lineNumbers: false,
        editable: false,
      } as any); // Sorry, the reuse sap.ui.codeeditor.CodeEditor doesn't have the correct constructor yet....
    });
  }

  protected registerForColumnSettings() {
    sap.ui.getCore().getEventBus().subscribe("personalizationsDone", "change", this.onAfterTablePersonalization, this);
  }

  public onAfterTablePersonalization() {
    this.clearSelection();
  }

  public onAfterRendering(): void {
    this.dataIntegrationComponent.registerHanaStateChangeHandler(
      () => {
        this.onRefreshTable();
      },
      () => {
        this.onRefreshTable();
      }
    );
  }

  private clearSearchField() {
    const searchField = this.view.byId("searchTablesInput") as sap.m.SearchField;
    searchField["clear"]();
  }

  private selectRow() {
    const taskChainList = this.view.getModel().getProperty("/taskChainsList");
    const taskChainTable = this.getView().byId("taskChainsTable") as sap.ui.core.mvc.View as any;
    const selectIndex = taskChainList.findIndex((task) => task.objectId === this.selectedChain);
    taskChainTable.setSelectedIndex(selectIndex);
  }

  public executeTaskChain() {
    const runPayload = {
      objectId: this.selectedChain,
      activity: "RUN_CHAIN",
      applicationId: "TASK_CHAINS",
      spaceId: this.spaceId,
    };
    this.view.setBusy(true);
    const url = `./tf/${this.spaceId}/taskchains/${this.selectedChain}/start`;
    ServiceCall.post(url, { contentType: ContentType.APPLICATION_JSON }, true, JSON.stringify(runPayload)).then(
      () => {
        const toastMessage = this.oBundle.getText("taskExecuteSuccessMsg", [this.selectedChain]);
        this.view.setBusy(false);
        this.onRefreshTable().then(() => {
          this.selectRow();
          this.clearSelection();
          sap.m.MessageToast.show(toastMessage);
        });
      },
      (error) => {
        const errorResp = error[0].responseJSON;
        this.view.setBusy(false);
        const toastMessage = errorResp
          ? this.oBundle.getText(errorResp.code)
          : this.oBundle.getText("msgTaskExecuteFail");
        MessageHandler.exception({ exception: error, message: toastMessage, id: "executeFailedErrorMsgbox" });
      }
    );
  }

  captureSelectedRowsSnapshot(oEvent: sap.ui.base.Event) {
    const oTable = this.byId("taskChainsTable") as sap.ui.table.Table;
    const isSelectAll = oEvent.getParameter("selectAll");
    const rowIndex = oEvent.getParameter("rowIndex");
    const rowIndices = oEvent.getParameter("rowIndices");
    const userInteraction = oEvent.getParameter("userInteraction");
    if (isSelectAll) {
      for (const i of rowIndices) {
        const path = oTable.getContextByIndex(i)?.getPath();
        const selectedTable = this.view.getModel().getProperty(path);
        this.selectedRowsSnapshot.push(selectedTable);
      }
    } else if (rowIndex === -1 && rowIndices.length > 0 && userInteraction) {
      // deselect all clicked
      for (const i of rowIndices) {
        const path = oTable.getContextByIndex(i)?.getPath();
        const selectedTable = this.view.getModel().getProperty(path);
        const existingTableIndex = this.selectedRowsSnapshot.findIndex(
          (table) => table.objectId === selectedTable.objectId
        );
        if (existingTableIndex > -1) {
          this.selectedRowsSnapshot.splice(existingTableIndex, 1);
        }
      }
    } else if (userInteraction) {
      const selectedPath = oEvent.getParameter("rowContext")?.getPath();
      if (!!selectedPath) {
        const selectedTable = this.view.getModel().getProperty(selectedPath);
        const existingTableIndex = this.selectedRowsSnapshot.findIndex(
          (table) => table.objectId === selectedTable.objectId
        );
        if (existingTableIndex > -1) {
          this.selectedRowsSnapshot.splice(existingTableIndex, 1);
        } else {
          this.selectedRowsSnapshot.push(selectedTable);
        }
      }
    }
  }

  restoreSelectedRows() {
    const oTable: any = this.byId("taskChainsTable") as sap.ui.table.Table;
    const oBinding: any = oTable.getBinding("rows");
    const filteredIndices = oBinding.aIndices;
    const tablesList = this.getView().getModel().getData();
    const selectedTables = this.selectedRowsSnapshot.map((table) => table.objectId);
    filteredIndices.forEach((filteredIndex, index) => {
      const tableName = tablesList.taskChainsList[filteredIndex]?.objectId;
      if (selectedTables.includes(tableName)) {
        oTable.addSelectionInterval(index, index);
      }
    });
  }

  public retryRecentRun() {
    const url = `./tf/${this.spaceId}/taskchains/${this.selectedChain}/retry`;
    this.view.setBusy(true);
    ServiceCall.request({
      url: url,
      type: HttpMethod.POST,
      dataType: DataType.TEXT,
    }).then(
      () => {
        const toastMessage = this.oBundle.getText("msgTaskRetryExecuteSuccess");
        this.view.setBusy(false);
        this.onRefreshTable();
        sap.m.MessageToast.show(toastMessage);
      },
      (error) => {
        const errorCode = error[0] && error[0].responseText ? JSON.parse(error[0].responseText) : null;
        const errorTxt =
          errorCode && errorCode.code === "initiateChainRetry.noFailedChildTask"
            ? "noFailedChildTaskErrorTxt"
            : "msgTaskRetryExecuteFail";
        this.view.setBusy(false);
        const toastMessage = this.oBundle.getText(errorTxt);
        MessageHandler.exception({ exception: error, message: toastMessage, id: "executeFailedErrorMsgbox" });
      }
    );
  }

  getTaskChainsList(): Promise<any> {
    const user = User.getInstance();
    const displayNamePreference = user.getObjectNameDisplay();
    const oTable = this.getView().byId("taskChainsTable");
    if (isDiMonitorImprovementsEnabled()) {
      if (displayNamePreference === "businessName") {
        this.view.byId("taskChainNameCol")["setLabel"](this.oBundle.getText("chainNameLabelBusNew"));
      } else {
        this.view.byId("taskChainNameCol")["setLabel"](this.oBundle.getText("chainNameLabelTechNew"));
      }
    } else {
      if (displayNamePreference === "businessName") {
        this.view.byId("taskChainNameCol")["setLabel"](this.oBundle.getText("chainNameLabelBus"));
      } else {
        this.view.byId("taskChainNameCol")["setLabel"](this.oBundle.getText("chainNameLabelTech"));
      }
    }

    const isBusinessNameEnabled = displayNamePreference === "businessName";
    const sUrl = `monitor/${this.spaceId}/taskchains${isBusinessNameEnabled ? "?includeBusinessNames=true" : ""}`;
    return ServiceCall.request<any>({
      url: sUrl,
      type: HttpMethod.GET,
      contentType: ContentType.APPLICATION_JSON,
    })
      .then(async (oResponse) => {
        let scheduleDialog;
        if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
          await this.initScheduleDialog();
          scheduleDialog = this["newScheduleDialog"];
        } else {
          scheduleDialog = (
            this.getView().byId("taskChainSchedulingDialog") as sap.ui.core.mvc.View
          ).getController() as ITTaskScheduleController;
        }
        const scheduleList = await scheduleDialog.refreshTaskScheduleList(this.spaceId, ApplicationId.TASK_CHAINS);
        oResponse.data.forEach((item) => {
          const schedule = scheduleList?.find((obj) => obj.objectId === item.objectId);
          item.formattedName = isBusinessNameEnabled ? item.businessName : item.objectId;
          item.formattedLastRunStatus = this.taskChainFormatter.statusTextFormatter(item.status);
          item.sortedLastRunStatus = this.taskChainFormatter.statusSortedTextFormatter(item.status);
          item.formattedRunTime = item.runTime ? this.formatTimespanHHMMSS(Math.floor(item.runTime / 1000)) : "";
          item.formattedLastStarted = this.formatDateTime(item.startTime);
          item.formattedLastStopped = this.formatDateTime(item.endTime);
          item.isScheduled = isNotUndefined(schedule);
          if (item.isScheduled && schedule.activationStatus === "DISABLED") {
            item.isPaused = true;
          } else if (item.isScheduled && schedule.activationStatus === "ENABLED") {
            item.isPaused = false;
          }
          if (item.isPaused) {
            item.formattedFrequencyText = this.oBundle.getText("pausedTxt");
          } else if (item.isPaused === false) {
            item.formattedFrequencyText = this.oBundle.getText("scheduledTxt");
          } else {
            item.formattedFrequencyText = "";
          }
          if (item?.isPaused) {
            item.formattedNextRun = "";
          } else {
            item.formattedNextRun = schedule
              ? this.taskChainFormatter.formatNextSchedule(schedule.nextRun, true, item.objectId, scheduleList)
              : "";
          }
          if (this.sortObject) {
            oTable["sort"](this.sortObject?.column, this.sortObject?.sortOrder, false);
          } else {
            oTable["sort"](
              this.getView().byId("lastRunStatusColumn") as sap.ui.table.Column,
              sap.ui.table.SortOrder.Descending,
              false
            );
          }
          item.scheduleOwner = "";
          if (schedule) {
            item.scheduleOwner = schedule?.owner;
          }
        });
        this.view.getModel().setProperty("/taskChainsList", oResponse.data);
        this.view.getModel().setProperty("/tableHeader", this.oBundle.getText("headerTxtNew", [oResponse.data.length]));
      })
      .catch((error) => {
        MessageHandler.exception({
          exception: error,
          message: this.oBundle.getText("txtReadBackendError"),
        });
      });
  }

  public loadTaskChainLogs(oEvent: IEvent<any, any>): void {
    const oItem = oEvent.getSource();
    const oBinding = oItem.getBindingContext();
    const sName = oBinding?.getObject("objectId");
    if (oEvent.getSource()) {
      this.router.navTo("taskChainMonitorLog", {
        spaceId: this.spaceId,
        objectId: encodeURIComponent(sName),
      });
    }
  }

  clearSelection() {
    const oTable = this.getView().byId("taskChainsTable") as sap.ui.core.mvc.View as any;
    this.selectedRowsSnapshot = [];
    oTable?.clearSelection();
    this.view.getModel().setProperty("/menuButtonsEnabled", false);
  }

  async onMassOperations(operation: MassOps) {
    const taskChainTable = this.getView().byId("taskChainsTable") as sap.ui.core.mvc.View as any;
    let errorTxt, successTxt;
    let operationMethod;
    const model = this.view.getModel("schedulingModel");
    let scheduleDialog;
    if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
      await this.initScheduleDialog();
      scheduleDialog = this["newScheduleDialog"];
    } else {
      scheduleDialog = (
        this.getView().byId("taskChainSchedulingDialog") as sap.ui.core.mvc.View
      ).getController() as ITTaskScheduleController;
    }
    switch (operation) {
      case MassOps.DELETE:
        operationMethod = (...args) => {
          const count = model.getProperty("/deleteCount");
          errorTxt = this.getText("errorMassRemoveScheduleTxt");
          successTxt = this.getText("massDeleteStarted", [count]);
          // eslint-disable-next-line prefer-spread
          scheduleDialog.deleteMultiSchedules.apply(scheduleDialog, args);
        };
        break;
      case MassOps.CHANGE_OWNER:
        operationMethod = (...args) => {
          const count = model.getProperty("/changeOwnerCount");
          errorTxt = this.getText("errorMassScheduleOwnerChangeTxt");
          successTxt = this.getText("massAssignStarted", [count]);
          // eslint-disable-next-line prefer-spread
          scheduleDialog.changeOwnerMultiSchedules.apply(scheduleDialog, args);
        };
        break;
      case MassOps.PAUSE:
        operationMethod = (...args) => {
          const count = model.getProperty("/pauseCount");
          errorTxt = this.getText("errorMassPauseScheduleTxt");
          successTxt = this.getText("massPauseStarted", [count]);
          // eslint-disable-next-line prefer-spread
          scheduleDialog.pauseMultiSchedules.apply(scheduleDialog, args);
        };
        break;
      case MassOps.RESUME:
        operationMethod = (...args) => {
          const count = model.getProperty("/resumeCount");
          errorTxt = this.getText("errorMassResumeScheduleTxt");
          successTxt = this.getText("massResumeStarted", [count]);
          // eslint-disable-next-line prefer-spread
          scheduleDialog.resumeMultiSchedules.apply(scheduleDialog, args);
        };
        break;
    }
    if (operationMethod) {
      const selectedTasks = this.getSelectedTasks(taskChainTable);
      const selectedTaskNames = selectedTasks.map((task) => task.objectId);
      const data: ITaskScheduleMassRequest = {
        objectIdList: selectedTaskNames,
      };
      this.view.setBusy(true);
      operationMethod(
        data,
        this.spaceId,
        ApplicationId.TASK_CHAINS,
        () => {
          sap.m.MessageToast.show(successTxt);
          this.onRefreshTable().then(() => {
            this.clearSelection();
            this.view.setBusy(false);
          });
        },
        (error) => {
          MessageHandler.exception({
            exception: error,
            message: errorTxt,
          });
          this.view.setBusy(false);
        },
        () => {
          this.view.setBusy(false);
        }
      );
    }
  }

  onMassDeleteOps() {
    this.onMassOperations(MassOps.DELETE);
  }

  onMassChangeOwner() {
    this.onMassOperations(MassOps.CHANGE_OWNER);
  }

  onMassSchedulesPause() {
    this.onMassOperations(MassOps.PAUSE);
  }

  onMassSchedulesResume() {
    this.onMassOperations(MassOps.RESUME);
  }

  refreshMenuItems() {
    const scheduleModel = this.view.getModel("schedulingModel") as sap.ui.model.json.JSONModel;
    const scheduleList = this.view.getModel("replicationModel").getProperty("/scheduleList");
    const schedule = scheduleList && scheduleList?.find((obj) => obj.objectId === this.selectedChain);
    const scheduleId = schedule && schedule.scheduleId;
    const taskChainTable = this.getView().byId("taskChainsTable") as sap.ui.core.mvc.View as any;
    const selectedTask = this.getSelectedTasks(taskChainTable);
    const status = selectedTask[0]?.status;
    const subStatus = selectedTask[0]?.subStatus;
    const executeEnabled =
      selectedTask.length > 0 && ((status && status.toLowerCase() !== "running") || isUndefined(status)) ? true : false;
    const retryEnabled =
      selectedTask.length > 0 &&
      status &&
      status.toLowerCase() === "failed" &&
      subStatus !== "FAIL_CONSENT_NOT_AVAILABLE"
        ? true
        : false;
    if (selectedTask.length === 0) {
      this.selectedChain = undefined;
      scheduleModel.setProperty("/newSchedule", false);
      scheduleModel.setProperty("/editSchedule", false);
      scheduleModel.setProperty("/deleteSchedule", false);
    } else if (scheduleId) {
      scheduleModel.setProperty("/newSchedule", false);
      scheduleModel.setProperty("/editSchedule", true);
      scheduleModel.setProperty("/deleteSchedule", true);
    } else {
      scheduleModel.setProperty("/newSchedule", true);
      scheduleModel.setProperty("/editSchedule", false);
      scheduleModel.setProperty("/deleteSchedule", false);
    }
    this.view.getModel().setProperty("/executeEnabled", executeEnabled);
    this.view.getModel().setProperty("/retryEnabled", retryEnabled);
  }

  private getSelectedTasks(taskChainTable: sap.ui.table.Table): any[] {
    return this.selectedRowsSnapshot;
  }

  private async handleMassOpsButtonStatus(taskChainTable: sap.ui.table.Table) {
    const selectedTasks = this.getSelectedTasks(taskChainTable);
    const selectedObjects = selectedTasks.map((task) => task.objectId);
    let scheduleDialog;
    if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
      await this.initScheduleDialog();
      scheduleDialog = this["newScheduleDialog"];
    } else {
      scheduleDialog = (
        this.getView().byId("taskChainSchedulingDialog") as sap.ui.core.mvc.View
      ).getController() as ITTaskScheduleController;
    }
    const massOpsButtonsStatus = await scheduleDialog.getMassOpsButtonEnablement(
      selectedObjects,
      this.spaceId,
      ApplicationId.TASK_CHAINS
    );
    const isCreateEnabled = selectedTasks.length === 1 && massOpsButtonsStatus.delete === false;
    const isEditEnabled = selectedTasks.length === 1 && massOpsButtonsStatus.delete;
    const model = this.view.getModel("schedulingModel");
    model.setProperty("/newSchedule", isCreateEnabled);
    model.setProperty("/editSchedule", isEditEnabled);
    model.setProperty("/deleteMassSchedules", massOpsButtonsStatus.delete);
    model.setProperty("/assignSchedule", massOpsButtonsStatus.changeOwner);
    model.setProperty("/pauseSchedule", massOpsButtonsStatus.pause);
    model.setProperty("/resumeSchedule", massOpsButtonsStatus.resume);
    model.setProperty("/deleteCount", massOpsButtonsStatus.deleteCount);
    model.setProperty("/changeOwnerCount", massOpsButtonsStatus.changeOwnerCount);
    model.setProperty("/pauseCount", massOpsButtonsStatus.pauseCount);
    model.setProperty("/resumeCount", massOpsButtonsStatus.resumeCount);
  }

  public async onRowSelect(event: sap.ui.base.Event) {
    const taskChainTable = this.getView().byId("taskChainsTable") as sap.ui.core.mvc.View as any;
    const selectedTasks = this.getSelectedTasks(taskChainTable);
    this.captureSelectedRowsSnapshot(event);
    if (selectedTasks.length > 1) {
      this.view.getModel().setProperty("/executeEnabled", false);
      this.view.getModel().setProperty("/retryEnabled", false);
    } else {
      this.selectedChain = selectedTasks[0]?.objectId;
      this.refreshMenuItems();
    }
    if (selectedTasks?.length === 0) {
      this.view.getModel().setProperty("/menuButtonsEnabled", false);
    } else {
      this.view.getModel().setProperty("/menuButtonsEnabled", true);
    }
    await this.handleMassOpsButtonStatus(taskChainTable);
  }

  public async onRefreshTable(): Promise<void> {
    const taskChainTable = this.getView().byId("taskChainsTable") as sap.ui.core.mvc.View as any;
    const circuitBreakerModel = sap.ui.getCore().getModel("circuitbreaker");
    const hanaState = circuitBreakerModel?.getProperty("/DataHANA");
    const isHanaDown: boolean =
      // On FF removal of isCircuitBreakerYellowStateEnabled be sure to keep boolean logic consistent
      hanaState === State.Red || (hanaState === State.Yellow && !isCircuitBreakerYellowStateEnabled());
    const dataHANAProvisioningState = circuitBreakerModel?.getProperty("/DataHANAProvisioningState");
    const isHanaUpgradeInProgress: boolean =
      dataHANAProvisioningState === State.Red ||
      // On FF removal of isCircuitBreakerYellowStateEnabled be sure to keep boolean logic consistent
      (dataHANAProvisioningState === State.Yellow && !isCircuitBreakerYellowStateEnabled());
    this.view.setBusyIndicatorDelay(0);
    this.view.setBusy(true);
    if (isHanaDown || isHanaUpgradeInProgress) {
      this.view.getModel().setProperty("/taskChainsList", []);
      this.view.getModel().setProperty("/tableHeader", "");
      this.view.setBusy(false);
    } else {
      await this.getTaskChainsList();
      let scheduleDialog;
      if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
        await this.initScheduleDialog();
        scheduleDialog = this["newScheduleDialog"];
      } else {
        scheduleDialog = (
          this.getView().byId("taskChainSchedulingDialog") as sap.ui.core.mvc.View
        ).getController() as ITTaskScheduleController;
      }
      const scheduleList = await scheduleDialog?.getTaskScheduleList(this.spaceId, ApplicationId.TASK_CHAINS);
      this.view.getModel("replicationModel").setProperty("/scheduleList", scheduleList);
      this.view.setBusy(false);
    }
    this.clearSelection();
    const oTable = this.getView().byId("taskChainsTable");

    const user = User.getInstance();
    if (isDiMonitorImprovementsEnabled()) {
      if (user.getObjectNameDisplay() === "businessName") {
        this.view.byId("taskChainNameCol")["setLabel"](this.oBundle.getText("chainNameLabelBusNew"));
      } else {
        this.view.byId("taskChainNameCol")["setLabel"](this.oBundle.getText("chainNameLabelTechNew"));
      }
    } else {
      if (user.getObjectNameDisplay() === "businessName") {
        this.view.byId("taskChainNameCol")["setLabel"](this.oBundle.getText("chainNameLabelBus"));
      } else {
        this.view.byId("taskChainNameCol")["setLabel"](this.oBundle.getText("chainNameLabelTech"));
      }
    }
  }

  async initScheduleDialog() {
    if (!this["newScheduleDialog"]) {
      this["newScheduleDialog"] = await getTaskScheduer("taskChainTaskScheduler");
    }
  }

  public async onCreateSchedule(): Promise<void> {
    const data: ITaskScheduleRequest = {
      objectId: this.selectedChain,
      applicationId: ApplicationId.TASK_CHAINS,
      activity: Activity.RUN_CHAIN,
      description: "Task Chain Monitoring",
      activationStatus: "ENABLED",
    };
    const taskChainTable = this.getView().byId("taskChainsTable") as sap.ui.core.mvc.View as any;
    const selectedIndex = taskChainTable.getSelectedIndex();
    let scheduleDialog;
    if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
      await this.initScheduleDialog();
      scheduleDialog = this["newScheduleDialog"];
      recordAction(`createTaskSchedule: ${data.applicationId}`, "taskSchedule", "onCreate");
    } else {
      scheduleDialog = (
        this.getView().byId("taskChainSchedulingDialog") as sap.ui.core.mvc.View
      ).getController() as ITTaskScheduleController;
    }
    this.view.setBusy(true);
    scheduleDialog.createTaskSchedule(
      data,
      this.spaceId,
      ApplicationId.TASK_CHAINS,
      () => {
        if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
          recordAction(`saveTaskSchedule: ${data.applicationId}`, "taskSchedule", "save");
        }
        const msg = this.oBundleTask.getText("createScheduleSuccess");
        this.onRefreshTable().then(() => {
          sap.m.MessageToast.show(msg);
        });
      },
      (error) => {
        if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
          recordAction(`saveTaskSchedule: ${data.applicationId}`, "taskSchedule", "save");
          MessageHandler.exception({
            exception: error.error,
            message: error.message,
          });
        }
        this.view.setBusy(false);
      },
      () => {
        this.view.setBusy(false);
      }
    );
  }

  public async onEditSchedule(): Promise<void> {
    const data: ITaskScheduleRequest = {
      objectId: this.selectedChain,
      applicationId: ApplicationId.TASK_CHAINS,
      activity: Activity.RUN_CHAIN,
      description: "Task Chain Monitoring",
      activationStatus: "ENABLED",
    };
    // const scheduleDialog = (this.getView().byId("taskChainSchedulingDialog") as sap.ui.core.mvc.View).getController() as ITTaskScheduleController;
    const taskChainTable = this.getView().byId("taskChainsTable") as sap.ui.core.mvc.View as any;
    const selectedIndex = taskChainTable.getSelectedIndex();
    let scheduleDialog;
    if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
      await this.initScheduleDialog();
      scheduleDialog = this["newScheduleDialog"];
      recordAction(`editTaskSchedule: ${data.applicationId}`, "taskSchedule", "onEdit");
    } else {
      scheduleDialog = (
        this.getView().byId("taskChainSchedulingDialog") as sap.ui.core.mvc.View
      ).getController() as ITTaskScheduleController;
    }
    this.view.setBusy(true);
    scheduleDialog.changeTaskSchedule(
      data,
      this.spaceId,
      ApplicationId.TASK_CHAINS,
      () => {
        if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
          recordAction(`saveTaskSchedule: ${data.applicationId}`, "taskSchedule", "save");
        }
        const msg = this.oBundleTask.getText("updateScheduleSuccess");
        this.onRefreshTable().then(() => {
          sap.m.MessageToast.show(msg);
        });
      },
      (error) => {
        if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
          recordAction(`saveTaskSchedule: ${data.applicationId}`, "taskSchedule", "save");
          MessageHandler.exception({
            exception: error.error,
            message: error.message,
          });
        }
        this.view.setBusy(false);
      },
      () => {
        this.view.setBusy(false);
      }
    );
  }

  public async onDeleteSchedule(): Promise<void> {
    this.onMassDeleteOps();
  }

  public async openSchedule(oEvent: any, name: string): Promise<void> {
    // const scheduleDialog = (this.getView().byId("taskChainSchedulingDialog") as sap.ui.core.mvc.View).getController() as ITTaskScheduleController;
    const link = oEvent.getSource();
    const data: ITaskScheduleRequest = {
      objectId: name,
      applicationId: ApplicationId.TASK_CHAINS,
      activity: Activity.RUN_CHAIN,
      description: "Task Chain Monitoring",
      activationStatus: "ENABLED",
    };
    const isSpaceLocked = this.getView().getModel("diPageModel")?.getProperty("/isSpaceLocked");
    const applicationId = ApplicationId.TASK_CHAINS;
    if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
      await this.initScheduleDialog();
      if (isDiMonitorImprovementsEnabled()) {
        await openSchedulePopover(
          link,
          name,
          applicationId,
          this.spaceId,
          this["newScheduleDialog"],
          data,
          this,
          this.onRefreshTable.bind(this),
          isSpaceLocked
        );
      } else {
        await openSchedulePopover(link, name, applicationId, this.spaceId, this["newScheduleDialog"]);
      }
    } else {
      const scheduleDialog = (
        this.getView().byId("taskChainSchedulingDialog") as sap.ui.core.mvc.View
      ).getController() as ITTaskScheduleController;
      scheduleDialog.openSchedulePopover(link, name, ApplicationId.TASK_CHAINS, this.spaceId);
    }
  }

  public onSearch(oEvent: IEvent<any, any>): void {
    // clear column filters on table search
    const oElement: any = this.view.byId("taskChainsTable");
    const columns: any[] = oElement.getColumns();
    columns.forEach((col) => {
      col.setFilterValue("");
      col.setFiltered(false);
    });

    // apply table search
    const data = oEvent.getSource().getValue();
    const oFilter = new sap.ui.model.Filter({
      filters: [
        new sap.ui.model.Filter("formattedName", sap.ui.model.FilterOperator.Contains, data),
        new sap.ui.model.Filter("status", sap.ui.model.FilterOperator.Contains, data),
      ],
    });
    const oBinding: any = oElement.getBinding("rows");
    oBinding.filter([oFilter]);
    // show dataflow count on search
    const filteredTableRecordLength = oBinding.getLength().toString();
    this.sTableHeader = this.oBundle.getText("headerTxtNew", [filteredTableRecordLength]);
    (this.view.getModel() as sap.ui.model.json.JSONModel).setProperty("/tableHeader", this.sTableHeader);
    this.restoreSelectedRows();
  }

  public fireOnFilterColumn(): void {
    const searchText = this.getView().byId("searchTablesInput");
    searchText["setValue"]("");
    setTimeout(() => {
      const oElement: any = this.view.byId("taskChainsTable");
      const oBinding: any = oElement.getBinding("rows");
      const RecordLength = oBinding.getLength().toString();
      this.sTableHeader = this.oBundle.getText("headerTxtNew", [RecordLength]);
      (this.view.getModel() as sap.ui.model.json.JSONModel).setProperty("/tableHeader", this.sTableHeader);
      this.restoreSelectedRows();
    }, 0);
  }

  public onPersoButtonPress() {
    this.tablePersoController.openDialog();
  }

  private getTable(): sap.m.Table {
    return this.byId("taskChainsTable") as sap.m.Table;
  }

  public fireOnSortColumn(event): void {
    if (!this.sortObject) {
      this.sortObject = {};
    }
    this.sortObject.column = event?.getParameter("column");
    this.sortObject.sortOrder = event?.getParameter("sortOrder");
    setTimeout(() => {
      this.restoreSelectedRows();
    });
  }

  public clearFilter(oTable) {
    const aColumns = oTable.getColumns();
    for (var i = 0; i < aColumns.length; i++) {
      oTable.filter(aColumns[i], null);
    }
  }

  public getScheduledText(taskChainName, formattedRefreshFrequency, isPaused, scheduleList) {
    if (isDiMonitorImprovementsEnabled() && !isPaused && scheduleList !== undefined) {
      const schedule = scheduleList?.find((obj) => obj.objectId === taskChainName);
      if (schedule) {
        return schedule?.cron !== undefined
          ? schedule?.cron
          : this.getText("everyLabel") +
              " " +
              schedule?.frequency?.interval +
              " " +
              this.getFrequencyText(schedule?.frequency?.type);
      } else {
        return formattedRefreshFrequency;
      }
    } else {
      return formattedRefreshFrequency;
    }
  }

  public getFrequencyText(type) {
    switch (type) {
      case "MINUTES":
        return this.getText("minutesLabel");
      case "HOURLY":
        return this.getText("hoursLabel");
      case "DAILY":
        return this.getText("daysLabel");
      case "WEEKLY":
        return this.getText("weeksLabel");
      case "MONTHLY":
        return this.getText("monthsLabel");
      default:
        return "";
    }
  }
}

export const TaskChainMonitor = smartExtend(
  BaseController,
  "sap.cdw.components.taskchainmonitor.controller.TaskChainMonitor",
  TaskChainMonitorClass
);

sap.ui.define("sap/cdw/components/taskchainmonitor/controller/TaskChainMonitor.controller", [], function () {
  return TaskChainMonitor;
});
