/** @format */

import { ISupportedFeaturesService } from "../../commonmodel/api/SupportedFeaturesService";
import {
  isAnalyticMeasureFormulaEnabled,
  isAnalyticModelDimensionHandlingEnabled,
  isBDCGAEnabled,
  isBDCRepositoryTransportDataEnabled,
  isBWBridgeForTaskChainEnabled,
  isBWPCEPushEnabled,
  isBrowserLasyLoadEnabled,
  isCSNDepsAnalyzerEnabled,
  isColumnValueHelpGVEnabled,
  isCompatibilityContractsEnabled,
  isCountDistinctEnabled,
  isDataActivationEnabled,
  isDataLayerLandingWithSearchCompositeEnabled,
  isDeltaReadAPIEnabled,
  isDiMonitorImprovementsEnabled,
  isDotSupportEnabled,
  isDotSupportEnabledFF,
  isExceptionAggregationEnabled,
  isFiscalTimeDimensionEnabled,
  isFolderSelectorEnabled,
  isGenAISemanticEnabled,
  isHDLFSpaceImportTransportEnabled,
  isHWDSupportCompounding,
  isHarmonizationObjectSelection,
  isHashPartitioningEnabled,
  isImproveOutputUpdateEnabled,
  isLoadingRepairedCsnEnabled,
  isModelValidationEnabled,
  isModelingAnnotatePartitionsEnabled,
  isModelingNamespaceEnabled,
  isORDInERMEnabled,
  isParameterValueHelpEnabled,
  isRepositoryExtensionsEnabled,
  isRepositryHiddenPerspectiveEnabled,
  isRestrictedMeasureEnabled,
  isSACPlanningIntegrationEnabled,
  isSQLCMEnabled,
  isSQLSaveWithErrorEnabled,
  isSQLScriptProcedureForTaskChainEnabled,
  isSkylineERModelerEnabled,
  isSpacePermissionsEnabled,
  isSparkSelectionVacuumEnabled,
  isTableDeleteDataEnabled,
  isTableRestoreEnabled,
  isTableTasksUseActiveRecords,
  isTechnicalVersionRestoreEnabled,
} from "./FeatureFlagCheck";

export class SupportedFeaturesImpl implements ISupportedFeaturesService {
  isTechnicalVersionRestoreEnabled(): boolean {
    return isTechnicalVersionRestoreEnabled();
  }
  isImproveOutputUpdateEnabled(): boolean {
    return isImproveOutputUpdateEnabled();
  }
  isFiscalTimeDimensionEnabled(): boolean {
    return isFiscalTimeDimensionEnabled();
  }
  isDataActivationEnabled(): boolean {
    return isDataActivationEnabled();
  }

  isAnalyticMeasureFormulaEnabled(): boolean {
    return isAnalyticMeasureFormulaEnabled();
  }
  isCountDistinctEnabled(): boolean {
    return isCountDistinctEnabled();
  }

  isExceptionAggregationEnabled(): boolean {
    return isExceptionAggregationEnabled();
  }

  isDotSupportEnabled(): boolean {
    return isDotSupportEnabled();
  }

  isDotSupportEnabledFF(): boolean {
    return isDotSupportEnabledFF();
  }

  isLoadingRepairedCsnEnabled(): boolean {
    return isLoadingRepairedCsnEnabled();
  }

  isRepositryHiddenPerspectiveEnabled(): boolean {
    return isRepositryHiddenPerspectiveEnabled();
  }

  isRepositoryExtensionsEnabled(): boolean {
    return isRepositoryExtensionsEnabled();
  }

  isSpacePermissionsEnabled(): boolean {
    return isSpacePermissionsEnabled();
  }

  isORDInERMEnabled(): boolean {
    return isORDInERMEnabled();
  }

  isBrowserLasyLoadEnabled(): boolean {
    return isBrowserLasyLoadEnabled();
  }

  isRestrictedMeasureEnabled(): boolean {
    return isRestrictedMeasureEnabled();
  }

  isCompatibilityContractsEnabled(): boolean {
    return isCompatibilityContractsEnabled();
  }
  isModelingNamespaceEnabled(): boolean {
    return isModelingNamespaceEnabled();
  }
  isModelingAnnotatePartitionsEnabled(): boolean {
    return isModelingAnnotatePartitionsEnabled();
  }

  isSQLCMEnabled(): boolean {
    return isSQLCMEnabled();
  }
  isColumnValueHelpGVEnabled(): boolean {
    return isColumnValueHelpGVEnabled();
  }
  isParameterValueHelpEnabled(): boolean {
    return isParameterValueHelpEnabled();
  }
  isFolderSelectorEnabled(): boolean {
    return isFolderSelectorEnabled();
  }
  isSQLSaveWithErrorEnabled(): boolean {
    return isSQLSaveWithErrorEnabled();
  }
  isDataLayerLandingWithSearchCompositeEnabled(): boolean {
    return isDataLayerLandingWithSearchCompositeEnabled();
  }
  isHWDSupportCompounding(): boolean {
    return isHWDSupportCompounding();
  }
  isSACPlanningIntegrationEnabled(): boolean {
    return isSACPlanningIntegrationEnabled();
  }
  isHarmonizationObjectSelection(): boolean {
    return isHarmonizationObjectSelection();
  }
  isCSNDepsAnalyzerEnabled(): boolean {
    return isCSNDepsAnalyzerEnabled();
  }
  isModelValidationEnabled(): boolean {
    return isModelValidationEnabled();
  }
  isBWBridgeForTaskChainEnabled(): boolean {
    return isBWBridgeForTaskChainEnabled();
  }
  isSQLScriptProcedureForTaskChainEnabled(): boolean {
    return isSQLScriptProcedureForTaskChainEnabled();
  }
  isSkylineERModelerEnabled(): boolean {
    return isSkylineERModelerEnabled();
  }
  isGenAISemanticEnabled(): boolean {
    return isGenAISemanticEnabled();
  }

  isTableRestoreEnabled(): boolean {
    return isTableRestoreEnabled();
  }

  isAnalyticModelDimensionHandlingEnabled(): boolean {
    return isAnalyticModelDimensionHandlingEnabled();
  }

  isDeltaReadAPIEnabled(): boolean {
    return isDeltaReadAPIEnabled();
  }

  isTableDeleteDataEnabled(): boolean {
    return isTableDeleteDataEnabled();
  }

  isSparkSelectionVacuumEnabled(): boolean {
    return isSparkSelectionVacuumEnabled();
  }

  isBWPCEPushEnabled(): boolean {
    return isBWPCEPushEnabled();
  }

  isDiMonitorImprovementsEnabled(): boolean {
    return isDiMonitorImprovementsEnabled();
  }

  isTableTasksUseActiveRecords() {
    return isTableTasksUseActiveRecords();
  }

  isHDLFSpaceImportTransportEnabled() {
    return isHDLFSpaceImportTransportEnabled();
  }

  isBDCRepositoryTransportDataEnabled() {
    return isBDCRepositoryTransportDataEnabled();
  }

  isBDCGAEnabled() {
    return isBDCGAEnabled();
  }

  isHashPartitioningEnabled() {
    return isHashPartitioningEnabled();
  }
}
