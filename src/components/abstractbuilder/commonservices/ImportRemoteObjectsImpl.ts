/** @format */

import {
  Comparison,
  Expression,
  IESSearchOptions,
  SearchQueryComparisonOperator,
  SearchQueryLogicalOperator,
} from "@sap/enterprise-search-objects";
import { deployCSN } from "../../../services/metadata";
import { getChildren } from "../../datasourcebrowser/utility/DatasourceUtility";
import {
  IChildInfoOptions,
  IConnectionInfoOptions,
  IDeployCSN,
  IImportRemoteService,
  IRepoOptions,
  ISearchOptions,
} from "../../reuse/importremotetable/api/ImportRemoteService";
import { getEndpointUrlOfRepoObjects } from "../../reuse/utility/RepoHelper";
import { ContentType, DataType, HttpMethod, ServiceCall } from "../../reuse/utility/ServiceCall";
import { ShellContainer } from "../../shell/utility/Container";
import { Crud } from "../../shell/utility/Crud";
import { IRemoteConnection, ModelType, Repo, SpaceType } from "../../shell/utility/Repo";

export class ImportRemoteObjectsImpl implements IImportRemoteService {
  public async getConnectionsOfSpace(connectionOptions: IConnectionInfoOptions) {
    // TODO: This list should be reviewed and unecessary/unused properties should be removed
    // Also, requesting one of the remote source runtime properties (here it is "adapter") would retrieve all runtime properties like location, agentName, etc.
    // If remote source runtime data is not required, please remove "adapter" from the list
    let connDetailsQuery: Array<keyof IRemoteConnection> = [
      "typeId",
      "name",
      "adapter",
      "credentialMode",
      "businessName",
      "description",
      "creator",
      "modifier",
      "creation_date",
      "modification_date",
      "space_name",
      "configuration",
      "remoteSourceName",
      "content",
      "disReplicationStatus",
      "capabilityHanaSdi",
      "capabilityModelTransfer",
      "capabilityPartnerSchema",
      "connectionMetaschema",
      "isPartnerIpAllowListValid",
      "partnerIpAllowList",
      "isConnProxyAssumedReady",
      "asyncRemoteSourceDeploymentStatus",
    ];

    const featureService = ShellContainer.get().getFeatureFlagService();
    const capabilitiesFF = featureService.getFeatureValue("INFRA_DWC_TWO_TENANT_MODE");
    if (capabilitiesFF) {
      const newCapabilities: Array<keyof IRemoteConnection> = [
        "capabilityDataflowSource",
        "capabilityDataflowTarget",
        "capabilityReplicationflowSource",
        "capabilityReplicationflowTarget",
      ];
      connDetailsQuery = connDetailsQuery.concat(newCapabilities);
    } else {
      connDetailsQuery.push("capabilityDisDataflow");
    }
    const result = await Repo.getRemoteConnectionList(connectionOptions.spaceName, connDetailsQuery);
    return result;
  }

  public async getChildren(childInfoOptions: IChildInfoOptions) {
    const path = childInfoOptions.path;
    const spaceName = childInfoOptions.spaceName;
    const browserOptions = childInfoOptions.browserOptions;
    const result = getChildren(path, spaceName, browserOptions.editorCapabilities.join(","));
    return result;
  }

  public async getRepositoryObjects(options: IRepoOptions) {
    const spaceName = options.spaceName;
    const details = options.details;
    const repositoryData = await Repo.getModelList(spaceName, details);
    return repositoryData;
  }

  public async searchChildren(options: ISearchOptions) {
    const result = await this.call(
      `searchchildren?space=${options.spaceName}&connection=${options.connection}&parentid=${options.parentid}&adapter=${options.adapter}` +
        (options.searchValue ? `&search=${options.searchValue}` : "")
    );
    const items = result["items"];
    items["_partial"] = result["partial"];
    return items;
  }

  public async deployCSN(options: IDeployCSN) {
    const response = await deployCSN(options);
    return response;
  }

  public call<T>(url: string) {
    return ServiceCall.request<T>({
      url: `datasources/${url}`,
      type: HttpMethod.GET,
      dataType: DataType.JSON,
    }).then((p) => p.data);
  }

  public async getRepositoryObjectsWithRemoteInfo(remoteObjectNames: string[], space: string) {
    const allObjects = await Repo.getModelList(space, []);
    const remoteEntities = await this.getRemoteObjectInfo(remoteObjectNames, space);
    if (remoteEntities?.length) {
      const remoteEntitiesMap = new Map(remoteEntities.map((entity) => [entity.name, entity]));
      allObjects.forEach((obj) => {
        const remoteEntity = remoteEntitiesMap.get(obj.name);
        if (remoteEntity?.csn) {
          obj.csn = JSON.parse(remoteEntity.csn);
        }
      });
    }
    return allObjects;
  }

  public async getRepositoryObjectCsnByName(names: string[], space: string) {
    const request = async (objectNames: string[]) => {
      const details = encodeURIComponent(["id", "name", "#repairedCsn", "#extensibleProperties"].join(","));
      const ajaxOptions: JQueryAjaxSettings = {
        url: `${getEndpointUrlOfRepoObjects()}?details=${details}&filters=name:${encodeURIComponent(
          objectNames.join("|")
        )}`,
        type: HttpMethod.GET,
        dataType: DataType.JSON,
        contentType: ContentType.APPLICATION_JSON,
      };
      const response = ServiceCall.request<any>(ajaxOptions);
      const results = (await response).data.results;
      results.forEach((result) => {
        result.csn = result["#repairedCsn"];
        result.extensibleProperties = result["#extensibleProperties"];
      });
      return results;
    };
    const chunkSize = 30; // to avoid long URL, we will send requests in chunks
    const reqs = [];
    for (let i = 0; i < names.length; i += chunkSize) {
      reqs.push(request(names.slice(i, i + chunkSize)));
    }
    const res = await Promise.all(reqs);
    return res.flat();
  }

  private async getRemoteObjectInfo(names: string[], space: string) {
    const chunkSize = 30; // to avoid long URL, we will send requests in chunks
    const request = async (objectNames: string[]) => {
      const searchOptions: IESSearchOptions = {
        $top: chunkSize,
        $skip: 0,
        searchQueryFilter: new Expression({
          operator: SearchQueryLogicalOperator.OR,
          items: objectNames.map((objectName) => {
            return new Comparison({
              property: "remote_entity",
              operator: SearchQueryComparisonOperator.EqualCaseSensitive,
              value: objectName,
            });
          }),
        }),
      };
      const remoteEntities = await Crud.get().search(
        [{ type: SpaceType, name: space }, { type: ModelType }],
        ["id", "name", "remote_entity", "csn"],
        searchOptions
      );
      return remoteEntities?.value;
    };
    const reqs = [];
    for (let i = 0; i < names.length; i += chunkSize) {
      reqs.push(request(names.slice(i, i + chunkSize)));
    }
    const results = await Promise.all(reqs);
    return results.flat();
  }
}
