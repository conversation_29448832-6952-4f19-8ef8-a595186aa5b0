/**
 * This file can be used for adding feature flag code till the feature flag is removed.
 *
 * @format
 */

import { CurrentContextService } from "../../commonmodel/api/CurrentContextService";

/**
 * Tells if the technical version restore function is enabled
 */
export function isTechnicalVersionRestoreEnabled() {
  let features;
  if (sap?.ui?.getCore instanceof Function) {
    features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
  }
  return !!(features && features.DWCO_MODELING_TECHNICAL_VERSIONS_RESTORE);
}

export function isModelingParameterLineageEnabled() {
  let features;
  if (sap?.ui?.getCore instanceof Function) {
    features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
  }
  return !!(features && features.DWCO_MODELING_PARAMETER_LINEAGE);
}

export function isSearchBasedSourceListEnabled() {
  if (
    window?.location?.href?.includes("?DwcoModelingSourceEntityListActive") ||
    window?.location?.href?.includes("&DwcoModelingSourceEntityListActive")
  ) {
    return true;
  }
  let features; // ToDo, activate later
  if (sap?.ui?.getCore instanceof Function) {
    features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
  }
  return !!(features && features.DWCO_MODELING_SOURCE_ENTITY_LIST && features.DWCO_MODELING_BROWSER_LAZY);
}

/**
 * Tells if data plane is enabled
 */
export function isDataActivationEnabled() {
  let features;
  if (sap?.ui?.getCore instanceof Function) {
    features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
  }
  return !!(features && features.DWCO_INFRA_DATA_PLANE);
}

/**
 * Tells if formula feature is enabled
 */
export function isAnalyticMeasureFormulaEnabled() {
  return isFeatureEnabledInCustomerSpace("INFRA_TENANT_PROVIDER_SAP");
}
/**
 * Tells if formula feature is enabled
 */
export function isRestrictedMeasureEnabled() {
  return isFeatureEnabledInCustomerSpace("INFRA_TENANT_PROVIDER_SAP");
}
/**
 * Tells if formula feature is enabled
 */
export function isAnalyticMeasureFormulaFeatureEnabled() {
  return isFeatureEnabledInCustomerSpace("INFRA_TENANT_PROVIDER_SAP");
}
/**
 * Tells if Exception Aggregation is enabled
 */
export function isExceptionAggregationEnabled() {
  return isFeatureEnabledInCustomerSpace("INFRA_TENANT_PROVIDER_SAP");
}

/**
 * Tells if Count Distinct is enabled
 */
export function isCountDistinctEnabled() {
  return isFeatureEnabledInCustomerSpace("INFRA_TENANT_PROVIDER_SAP");
}

/**
 * Tells if the FF of dot is enabled
 * Note: still need to check if there is namespace exist, search the usages of this function
 */
export function isDotSupportEnabledFF() {
  // return isFeatureEnabledInCustomerSpace("INFRA_TENANT_PROVIDER_SAP");
  let features;
  if (sap?.ui?.getCore instanceof Function) {
    features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
  }
  return !!(features && features.DWCO_MODELING_ALLOW_DOTS);
}

/**
 * Tells if dot is supported in all data layer editors
 * Note: this is an old function, please use isDotSupportEnabledFF instead when you enabled dot for your editors
 */
export function isDotSupportEnabled() {
  return isFeatureEnabledInCustomerSpace("INFRA_TENANT_PROVIDER_SAP");
}

/**
 * Tells if AnalyticalMeasure formula in formula supported
 */
export function isAnalyticMesureFormulaInformulaEnabled() {
  return isFeatureEnabledInCustomerSpace("INFRA_TENANT_PROVIDER_SAP");
}
/**
 * Tells if analytic measures in formula expression feature is enabled
 */
export function isAnalyticMeasuresInFormulaEnabled() {
  return isFeatureEnabledInCustomerSpace("INFRA_TENANT_PROVIDER_SAP");
}

/**
 * Tells if functions in formula expression feature is enabled
 */
export function isAMFunctionsInFormulaEnabled() {
  return isFeatureEnabledInCustomerSpace("INFRA_TENANT_PROVIDER_SAP");
}

/**
 * Tells if requesting #csn instead of csn in designObjects request is enabled
 */
export function isLoadingRepairedCsnEnabled() {
  let features;
  if (sap?.ui?.getCore instanceof Function) {
    features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
  }
  return true;
}

/*
 * Tells if hide the marked artefacts from searches/ hide them from modeling editors.
 */
export function isRepositryHiddenPerspectiveEnabled() {
  let features;
  if (sap?.ui?.getCore instanceof Function) {
    features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
  }
  return !!(features && features.DWCO_INFRA_REPOSITORY_HIDDEN_PERSPECTIVE);
}

/*
 * Tells if repository support CSN Extensions
 */
export function isRepositoryExtensionsEnabled() {
  let features;
  if (sap?.ui?.getCore instanceof Function) {
    features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
  }
  return !!(features && features.DWCO_INFRA_REPOSITORY_EXTENSION_CORE);
}

/*
 * Tells if ORD in ER model is supported
 */
export function isORDInERMEnabled() {
  let features;
  if (sap?.ui?.getCore instanceof Function) {
    features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
  }
  return !!(features && features.DWCO_INFRA_DATA_PLANE);
}

/*
 * Tells if support space permissions
 */
export function isSpacePermissionsEnabled() {
  let features;
  if (sap?.ui?.getCore instanceof Function) {
    features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
  }
  return !!(features && features.DWC_DUMMY_SPACE_PERMISSIONS); // DWC_DUMMY_SPACE_PERMISSIONS shall be dependent on DWCO_INFRA_SPACE_PERMISSIONS && INFRA_SCOPE_DEPENDENT_ROLES and 1T tenants should not (and cannot) be converted to SDP
}

/*
 * Tells if lasy mode of browser editor is enabled
 */
export function isBrowserLasyLoadEnabled() {
  let features;
  if (sap?.ui?.getCore instanceof Function) {
    features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
  }
  return !!(features && features.DWCO_MODELING_BROWSER_LAZY);
}

/*
 * Tells if customerspace enable
 */
function isFeatureEnabledInCustomerSpace(featureName: string, isModificationPermitted = true) {
  let features;
  if (sap?.ui?.getCore instanceof Function) {
    features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
  }
  const DP_FF = !!(features && features[featureName]);
  const spaceInfo = CurrentContextService.getInstance().getCurrentSpaceInfo();
  if (!spaceInfo || !spaceInfo.spaceId) {
    return false;
  } else {
    const spaceProvider = spaceInfo.spaceId.spaceProvider;
    const DWC_DUMMY_DATA_PLANE_DEV_TENANT = !!features?.DWC_DUMMY_DATA_PLANE_DEV_TENANT;
    if (isModificationPermitted === true) {
      return DP_FF && (DWC_DUMMY_DATA_PLANE_DEV_TENANT || spaceProvider === "SAP");
    } else {
      return DP_FF && DWC_DUMMY_DATA_PLANE_DEV_TENANT;
    }
  }
}

/*
 * Tells if modeling namespace feature is enabled
 */
export function isModelingNamespaceEnabled() {
  let features;
  if (sap?.ui?.getCore instanceof Function) {
    features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
  }
  return !!(features && features.DWCO_MODELING_NAMESPACE);
}

/*
 * Tells if modeling compatibility contracts feature is enabled
 */
export function isCompatibilityContractsEnabled() {
  let features;
  if (sap?.ui?.getCore instanceof Function) {
    features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
  }
  return !!(features && features.DWCO_MODELING_COMPATIBILITY_CONTRACTS);
  // return true;
}

/*
 * Tells if modeling fiscal time dimension feature is enabled
 */
export function isFiscalTimeDimensionEnabled() {
  let features;
  if (sap?.ui?.getCore instanceof Function) {
    features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
  }
  return !!(features && features.DWCO_MODELING_SUPPORT_FISCAL_TIME);
}

/* Tells if SKIP unnecessary output update enabled
 */
export function isImproveOutputUpdateEnabled() {
  let features;
  if (sap?.ui?.getCore instanceof Function) {
    features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
  }
  return !!(features && features.DWCO_MODELING_IMPROVE_OUTPUT_UPDATE);
}

/* Tells if Change management for SQL view enabled
 */
export function isSQLCMEnabled() {
  let features;
  if (sap?.ui?.getCore instanceof Function) {
    features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
  }
  return !!(features && features.DWCO_CHANGE_MANAGEMENT_SQL_VIEW);
}

/* Tells if column value help for Graphical View expression editors is supported
 */
export function isColumnValueHelpGVEnabled() {
  let features;
  if (sap?.ui?.getCore instanceof Function) {
    features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
  }
  return !!(features && features.DWCO_MODELING_COLUMN_VALUE_HELP);
}

/* Tells if input parameter value help is enabled
 */
export function isParameterValueHelpEnabled() {
  let features;
  if (sap?.ui?.getCore instanceof Function) {
    features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
  }
  return !!(features && features.DWCO_IP_VALUE_HELP);
}

/* Tells if Folder Selection Enabled
 */
export function isFolderSelectorEnabled(calledByDac: boolean = false, calledByGraphBuilder: boolean = false) {
  let features;
  if (sap?.ui?.getCore instanceof Function) {
    features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
  }
  if (calledByGraphBuilder) {
    return features && features.DWCO_GRAPH_REPO_EXPLORER;
  }
  return !!(features && ((features.DWCO_MODELING_SAVE_FOLDER_SUPPORT && !calledByDac) || calledByDac));
}

/* Tells if SQL Editor Save with error is enabled
 */
export function isSQLSaveWithErrorEnabled() {
  let features;
  if (sap?.ui?.getCore instanceof Function) {
    features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
  }
  return !!(features && features.DWCO_SAVE_WITH_ERROR_SQL_VIEW);
}

/**
 * Check if Transformation flows is enabled
 */
export function isTransformationFlowEnabled() {
  let features;
  if (sap?.ui?.getCore instanceof Function) {
    features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
  }
  return !!(features && features.INFRA_DWC_TWO_TENANT_MODE);
}

/**
 * Check if Shared Task Chains is enabled
 */
export function isSharedTaskChainEnabled() {
  let features: any;
  if (sap?.ui?.getCore instanceof Function) {
    features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
  }
  return !!(features && features.DWCO_TASK_FRAMEWORK_SHARE_TASK_CHAINS);
}

/**
 * Check if Replication flows is enabled
 */
export function isReplicationFlowEnabled() {
  let features;
  if (sap?.ui?.getCore instanceof Function) {
    features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
  }
  return !!(features && features.INFRA_DWC_TWO_TENANT_MODE);
}

/**
 * Check if Data Layer landing page with search composite is enabled
 */
export function isDataLayerLandingWithSearchCompositeEnabled() {
  let features;
  if (sap?.ui?.getCore instanceof Function) {
    features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
  }
  return !!(features && features.DWCO_MODELING_DATABUILDER_LANDING);
}

/**
 * Check if analytic model dimension handling is enabled
 */
export function isAnalyticModelDimensionHandlingEnabled() {
  let features;
  if (sap?.ui?.getCore instanceof Function) {
    features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
  }
  return !!(features && features.DWCO_MODELING_AM_NEW_DIM_HANDLING);
}

/* checks if SAC planning integration FF is enabled
 */
export function isSACPlanningIntegrationEnabled() {
  let features;
  if (sap?.ui?.getCore instanceof Function) {
    features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
  }
  return !!(features && features.DWCO_MODELING_SUPPORT_SAC_SEAMLESS_PLANNING_INTEGRATION);
}

/* Check if Harmonization of object selection dialog across data layer is enabled.
 */
export function isHarmonizationObjectSelection() {
  let features;
  if (sap?.ui?.getCore instanceof Function) {
    features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
  }
  return !!(features && features.DWCO_HARMONIZATION_OBJECT_SELECTION);
}

/**
 * Check if hierarchy with directory support compounding key
 */
export function isHWDSupportCompounding() {
  let features;
  if (sap?.ui?.getCore instanceof Function) {
    features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
  }
  return !!(features && features.DWCO_MODELING_EXTERNAL_HIERARCHY_SUPPORT_COMPOUNDING);
}

/**
 * Check if use processdocument endpoint to anaylze the CSN document
 */
export function isCSNDepsAnalyzerEnabled() {
  let features;
  if (sap?.ui?.getCore instanceof Function) {
    features = sap.ui.getCore().getModel("featureflags").getData();
  }
  return features?.DWCO_MODELING_CSN_DEPSANALYZER;
}

/**
 * check if the Model validation suppoed is enabled
 */
export function isModelValidationEnabled() {
  let features;
  if (sap?.ui?.getCore instanceof Function) {
    features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
  }
  return !!(features && features.DWCO_MODEL_VALIDATION);
}

/** Check if Task Chain support BW Bridge
 */
export function isBWBridgeForTaskChainEnabled() {
  let features;
  if (sap?.ui?.getCore instanceof Function) {
    features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
  }
  return !!(features && features.DWCO_INFRA_TASKS_BW_PROCESS_CHAIN);
}

/** Check is Task Chain support Procedures
 */
export function isSQLScriptProcedureForTaskChainEnabled() {
  let features;
  if (sap?.ui?.getCore instanceof Function) {
    features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
  }
  return !!(features && features.DWCO_INFRA_TASKS_PROCEDURES);
}

/**
 * check if the Skyline ERModeler is enabled
 */
export function isSkylineERModelerEnabled() {
  let features;
  if (sap?.ui?.getCore instanceof Function) {
    features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
  }
  return !!(features && features.DWCO_REUSABLE_ERMODELER);
}

/**
 * Check if the GenAI semantic is enabled
 */
export function isGenAISemanticEnabled(): boolean {
  let features;
  if (sap?.ui?.getCore instanceof Function) {
    features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
  }
  return !!(features && features.DWCO_MODELING_GEN_AI_SEMANTIC_ENRICHMENT);
}

export function isSharedTaskChainSupported() {
  let features: any;
  if (sap?.ui?.getCore instanceof Function) {
    features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
  }
  return !!(features && features.DWCO_TASK_FRAMEWORK_SHARE_TASK_CHAINS);
}

/**
 * Check if Replication Flows Import Export Enabled
 */

export function isTableRestoreEnabled() {
  let features: any;
  if (sap?.ui?.getCore instanceof Function) {
    features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
  }
  return !!(features && features.DWCO_TABLE_TECHNICAL_VERSIONS_RESTORE);
}

export function isAnalyticModelRestoreEnabled() {
  let features: any;
  if (sap?.ui?.getCore instanceof Function) {
    features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
  }
  return !!(features && features.DWCO_MODELING_AM_RESTORE_VERSION);
}

export function isDACRestoreEnabled() {
  let features: any;
  if (sap?.ui?.getCore instanceof Function) {
    features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
  }
  return !!(features && features.DWCO_MODELING_DAC_RESTORE_VERSION);
}

export function isDeltaReadAPIEnabled() {
  let features: any;
  if (sap?.ui?.getCore instanceof Function) {
    features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
  }
  return !!(features && features.DWCO_TABLE_DELTA_UPSERT_READ_API);
}

export function isTableDeleteDataEnabled() {
  let features: any;
  if (sap?.ui?.getCore instanceof Function) {
    features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
  }
  return !!(features && features.DWCO_DELETE_DATA_LOCAL_HANA_TABLE);
}

export function isBWPCEPushEnabled() {
  let features: any;
  if (sap?.ui?.getCore instanceof Function) {
    features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
  }
  return !!(features && features.DWCO_LOCAL_TABLE_FILES_BWPUSH_UI);
}

export function isDiMonitorImprovementsEnabled() {
  let features: any;
  if (sap?.ui?.getCore instanceof Function) {
    features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
  }
  return !!(features && features.DWCO_DI_MONITOR_UI_IMPROVEMENTS);
}
export function isTableTasksUseActiveRecords() {
  let features: any;
  if (sap?.ui?.getCore instanceof Function) {
    features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
  }
  return !!(features && features.DWCO_LOCAL_TABLE_TASKS_ACTIVE_RECORDS);
}

export function isHashPartitioningEnabled() {
  let features: any;
  if (sap?.ui?.getCore instanceof Function) {
    features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
  }
  return !!(features && features.DWCO_TABLE_PARTITIONING_HASH);
}

export function isHDLFSpaceImportTransportEnabled() {
  let features: any;
  if (sap?.ui?.getCore instanceof Function) {
    features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
  }
  return !!(features && features.DWCO_HDLF_SPACE_IMPORT_TRANSPORT);
}

export function isBDCRepositoryTransportDataEnabled() {
  let features: any;
  if (sap?.ui?.getCore instanceof Function) {
    features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
  }
  return !!(features && features.DWCO_BDC_REPOSITORY_TRANSPORT_DATA);
}

export function isBDCGAEnabled() {
  let features: any;
  if (sap?.ui?.getCore instanceof Function) {
    features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
  }
  return !!(features && features.DWCO_BDC_GA);
}

export function isSparkSelectionVacuumEnabled() {
  let features: any;
  if (sap?.ui?.getCore instanceof Function) {
    features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
  }
  return !!(features && features.DWCO_LOCAL_TABLE_FILES_VACUUM_SPARK_SELECTION);
}

export function isModelingAnnotatePartitionsEnabled() {
  let features: any;
  if (sap?.ui?.getCore instanceof Function) {
    features = (sap?.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel)?.getData();
  }
  return !!(features && features.DWCO_MODELING_ANNOTATE_PARTITIONS);
}
