/** @format */

import { hanaStateFormatter } from "../../dataintegration/utility/DIFormatters";
import { Format } from "../../reuse/utility/Format";
import { replicationState, taskState } from "../viewpersistencyservice/ServiceConsumption";

const bundleName = require("../i18n/i18n.properties");
const i18nModel = new sap.ui.model.resource.ResourceModel({ bundleName });
const oBundle = i18nModel.getResourceBundle();

export class ViewMonitorFormatter {
  public static replicationStatus = {
    AVAILABLE: "A",
    ERROR: "E",
    LOADING: "I",
    NODATA: "D",
    PAUSED: "P",
    DISCONNECTED: "X",
  };

  public static DataPersistency = {
    VIRTUAL: "Virtual",
    PERSISTED: "Persisted",
  };

  public static getTextRefreshFrequency(dataPersistency: string, scheduled: boolean): string {
    if (scheduled) {
      return oBundle.getText("scheduledTxt");
    }
    if (dataPersistency === this.DataPersistency.VIRTUAL) {
      return "";
    } else if (dataPersistency === this.DataPersistency.PERSISTED) {
      return "";
    }
  }

  public static getTextViewPersistenceStatus(
    sReplicationState: string,
    sReplicationType: string,
    sTaskState: string
  ): string {
    switch (sReplicationState) {
      case replicationState.None:
        return oBundle.getText("emptyCell");
      case replicationState.Initialize:
        if (sTaskState === taskState.FAILED) {
          return oBundle.getText("txtError");
        } else {
          return oBundle.getText("txtRunning");
        }
      case replicationState.Available:
        return oBundle.getText("txtAvailable");
      case replicationState.Error:
        return oBundle.getText("txtError");
    }
  }

  public static getDuration(startTime, endTime) {
    if (startTime && endTime) {
      const startDate = new Date(startTime);
      const endDate = new Date(endTime);
      let duration = endDate.getTime() - startDate.getTime();
      duration = Math.abs(duration);
      const hours = Math.floor(duration / 3600000);
      duration -= hours * 3600000;
      let minutes = Math.floor(duration / 60000);
      duration -= minutes * 60000;
      let seconds = Math.round(duration / 1000);
      if (seconds === 60) {
        minutes += 1;
        seconds = 0;
      }
      return `${hours}:${("0" + minutes).slice(-2)}:${("0" + seconds).slice(-2)}`;
    }
    return "";
  }

  public static getTextViewSortedStatus(sReplicationState: string, sReplicationType: string, sTaskState: string) {
    switch (sReplicationState) {
      case replicationState.None:
        return 2;
      case replicationState.Initialize:
        if (sTaskState === taskState.FAILED) {
          return 5;
        } else {
          return 4;
        }
      case replicationState.Available:
        return 3;
      case replicationState.Error:
        return 5;
    }
  }

  public static getViewPersistenceStatusTooltip(
    sReplicationState: string,
    sReplicationType: string,
    sTaskState: string,
    sReplicationError: string
  ): string {
    switch (sReplicationState) {
      case replicationState.None:
        return oBundle.getText("emptyCell");
      case replicationState.Initialize:
        if (sTaskState === taskState.FAILED) {
          return oBundle.getText("txtError");
        } else {
          return oBundle.getText("txtRunning");
        }
      case replicationState.Available:
        return oBundle.getText("txtAvailable");
      case replicationState.Error:
        if (ViewMonitorFormatter.persistencyErrorJson(sReplicationError)) {
          return JSON.parse(sReplicationError).message;
        } else {
          return sReplicationError;
        }
    }
  }

  /** This method validate that error message has valid JSON message or not  */
  public static persistencyErrorJson(replicationError): boolean {
    try {
      if (replicationError && replicationError.length > 0) {
        JSON.parse(replicationError);
      } else {
        return false;
      }
    } catch (e) {
      return false;
    }
    return true;
  }

  public static _getSemanticColorValue(sStatusBackend: string, sTaskState: string): string {
    switch (sStatusBackend) {
      case replicationState.Initialize:
        if (sTaskState === taskState.FAILED) {
          return "Error";
        } else {
          return "None";
        }
      case replicationState.Available:
        return "Success";
      case replicationState.Error:
        return "Error";
      default:
        return "None";
    }
  }

  public static getViewMsgBundle() {
    return oBundle;
  }

  /** This method formats the next run in remote table monitor */
  public static formatNextSchedule = function (
    nextSchedule: string,
    isScheduled: boolean,
    viewName?: string,
    scheduleList?: any
  ) {
    const schedule = scheduleList?.find((val) => val.objectId === viewName);
    if (schedule?.activationStatus === "DISABLED") {
      return "";
    } else {
      if (isScheduled && (!nextSchedule || nextSchedule.length === 0) && schedule?.activationStatus === "ENABLED") {
        return oBundle.getText("txtExpired");
      }
      return Format.toLocalDateTime(nextSchedule);
    }
  };

  public static hanaStateFormatter = hanaStateFormatter;
}
