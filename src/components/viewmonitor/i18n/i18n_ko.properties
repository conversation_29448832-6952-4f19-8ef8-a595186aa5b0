
#XBCB: Breadcrumb
viewMonitorBreadcrumb={spaceId}

#XFLD: Label for persisted view column
SOURCE=소스
#XFLD: Label for persisted view column
NAME=이름
#XFLD: Label for persisted view column
NAME_LABEL=업무 이름
#XFLD: Label for persisted view column
NAME_LABELNew=오브젝트(업무 이름)
#XFLD: Label for persisted view column
TECHINCAL_NAME=기술적 이름
#XFLD: Label for persisted view column
TECHINCAL_NAMENew=오브젝트(기술적 이름)
#XFLD: Label for persisted view column
DATA_PERSISTENCY=데이터 액세스
#XFLD: Label for persisted view column
STATUS=상태
#XFLD: Label for persisted view column
LAST_UPDATED=최종 업데이트
#XFLD: Label for persisted view column
IN_MEMORY_SIZE=저장에 사용된 메모리(MiB)
#XFLD: Label for persisted view column
DISK_SIZE=저장에 사용된 디스크(MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_NEWW=인메모리 크기(MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_HEADER=인메모리 크기
#XFLD: Label for persisted view column
DISK_SIZE_NEWW=디스크의 크기(MiB)
#XFLD: Label for persisted view column
DISK_SIZE_HEADER=디스크의 크기
#XFLD: Label for schedule owner column
txtScheduleOwner=일정 소유자
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=일정을 생성한 사람 표시
#XFLD: Label for persisted view column
PERSISTED=지속됨
#XFLD: Label for persisted view column
TYPE=유형
#XFLD: Label for View Selection Dialog column
changedOn=변경일
#XFLD: Label for View Selection Dialog column
createdBy=생성자
#XFLD: Label for log details column
txtViewPersistencyLogs=로그 보기
#XFLD: Label for log details column
txtViewPersistencyLogsNew=세부사항
#XFLD: text for values shown for Ascending sort order
SortInAsc=오름차순 정렬
#XFLD: text for values shown for Descending sort order
SortInDesc=내림차순 정렬
#XFLD: Label for persisted view monitor heading
ViewMonitor_Heading=뷰 모니터
#XFLD: Label for persisted view monitor sub-heading
ViewMonitor_SubHeading=뷰 데이터 지속성 모니터링 및 유지보수


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ DynamicPageHeader ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: text for values shown in column Persistence Status
txtLoading=로드하는 중
#XFLD: text for values shown in column Persistence Status
txtRunning=실행 중
#XFLD: text for values shown in column Persistence Status
txtAvailable=사용 가능
#XFLD: text for values shown in column Persistence Status
txtError=오류
#XMSG: Error message for unknown Persistence type that gets logged in Kibana
unknownReplicationTypeError=복제 유형 "{0}"은(는) 지원되지 않습니다.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Content ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=마지막 데이터 지속성 실행에 사용된 설정:
#XMSG: Message for input parameter name
inputParameterLabel=입력 매개변수
#XMSG: Message for input parameter value
inputParameterValueLabel=값
#XMSG: Message for persisted data
inputParameterPersistedLabel=지속된 시간
#XTIT: Count for View in ViewMonitor Toolbar
txtViewCount=뷰({0})
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=뷰 지속성
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=데이터 지속성
#XBUT: Button to clear the selected view persistance
ClearViewPersistance=지우기
#XBUT: Button to stop the selected view persistance
stopPersistance=지속성 중지
#XFLD: Placeholder for Search field
txtSearch=검색
#XBUT: Tooltip for refresh button
txtRefresh=새로 고침
#XBUT: Tooltip for add view button
txtDeleteView=지속성 삭제
#XBUT: Tooltip for load new peristence
loadNewPersistence=지속성 재시작
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=신규 스냅샷 로드
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=데이터 지속성 시작
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=지속 데이터 제거
#XMSG: success message for starting persistence
startPersistenceSuccess=뷰 "{0}"을(를) 지속하고 있습니다.
#XMSG: success message for stopping persistence
stopPersistenceSuccess=뷰 "{0}"의 지속 데이터를 제거하고 있습니다.
#XMSG: success message for stopping persistence
deletePersistedViewSuccess=모니터링 리스트에서 "{0}" 뷰를 제거하는 중입니다.
#XMSG: error message during start persistence of a view (backend error)
ERROR_STARTING_PERSISTENCE=뷰 "{0}"의 데이터 지속성을 시작하는 동안 오류가 발생했습니다.
#XMSG: error message during start persistence of a view (backend error)
PARAMERROR_STARTING_PERSISTENCE="{0}" 뷰에 입력 매개변수가 포함되어 있어 지속될 수 없습니다.
#XMSG: error message during start persistence of a view (backend error)
NUMBER_OF_PARAMETER_STARTING_PERSISTENCE="{0}" 뷰에 입력 매개변수가 두 개 이상 있으므로 지속할 수 없습니다.
#XMSG: error message during start persistence of a view (backend error)
PARAMETER_DEFAULT_VALUE_STARTING_PERSISTENCE=입력 매개변수에 기본값이 없으므로 "{0}" 뷰를 지속할 수 없습니다.
#XMSG: error message during start persistence of a view (backend error)
REDEPLOYMENT_DAC_REQUIRED=데이터 지속성을 지원하려면 데이터 액세스 제어(DAC) "{0}"을(를) 재배포해야 합니다.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_STARTING_PERSISTENCE="{0}" 뷰는 데이터 액세스 제어(DAC)가 있는 "{1}" 뷰를 사용하므로 지속될 수 없습니다.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_EXTERNAL_STARTING_PERSISTENCE="{0}" 뷰는 다른 공간에 속한 데이터 액세스 제어(DAC)가 있는 뷰를 사용하므로 지속할 수 없습니다.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_STRUCTURE_STARTING_PERSISTENCE="{0}" 뷰의 데이터 액세스 제어(DAC) 중 하나 이상의 구조가 데이터 지속성을 지원하지 않으므로 이 뷰를 지속할 수 없습니다.
#XMSG: error message during stop persistence of a view (backend error)
STOP_PERSISTENCE_ERROR=뷰 "{0}"의 지속성을 중지하는 동안 오류가 발생했습니다.
#XMSG: error message during stop persistence of a view (backend error)
DELETE_PERSISTED_VIEW_ERROR=지속 뷰 "{0}"을(를) 삭제하는 동안 오류가 발생했습니다.
#XMSG: Warning message for removing the persisted data
RemovePersistency_Confirm_Msg=지속 데이터를 삭제하고 뷰 "{0}"의 가상 액세스로 전환하시겠습니까?
#XMSG: Warning message for delete the view persistence
DeletePersistency_Confirm_Msg=모니터링 리스트에서 뷰를 제거하고 "{0}" 뷰의 지속 데이터를 삭제하시겠습니까?
#XMSG: error message for reading data from backend
txtReadBackendError=백엔드에서 읽는 동안 오류가 발생한 것 같습니다.
#XFLD: Label for No Data Error
NoDataError=오류
#XMSG: message for conflicting task
Task_Already_Running=뷰 "{0}"에 대한 충돌 태스크가 이미 실행 중입니다.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Show All Views~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Text for show all views button
allViewsText=모든 뷰({0})
#XBUT: Text for show scheduled views button
scheduledText=예정됨({0})
#XBUT: Text for show persisted views button
persistedText=지속됨({0})
#XBUT: Text for start analyzer button
startAnalyzer=View Analyzer 시작
#XFLD: Message if repository is unavailable
repositoryErrorMsg=저장소를 사용할 수 없으며 특정 기능이 비활성화됩니다.

#XFLD: Data Access - Virtual
Virtual=가상
#XFLD: Data Access - Persisted
Persisted=지속됨

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Non-Persisted View Selection Dialog~~~~~~~~~~~~~~~~~~~~~~~

#XTIT: Title for Non-Persisted View selection dialog
select_source=지속할 뷰 선택

#XTIT: Placeholder for Non-Persisted View selection dialog search
searchView=뷰 검색
#XTIT: No data in the list of non-persisted view
No_Data=데이터 없음
#XBUT: Button to select non-persisted view
ok=확인
#XBUT: Button to close the non-persisted views selection dialog
cancel=취소

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~View persistency log message key~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message for view persistency logs
START_STOP_PERSISTENCY="{1}"의 데이터 지속성 태스크 실행을 시작하는 중입니다.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY="{1}" 뷰의 데이터 지속 중
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY)
START_VIEW_PERSISTENCY_PROCESS="{0}" 뷰의 데이터를 지속하는 프로세스를 시작하는 중
#XFLD: Message for view persistency manual partition load
START_VIEW_PERSISTENCY_PROCESS_MANUAL_PARTITIONS="{0}" 뷰의 데이터를 지속하는 프로세스를 시작하는 중(선택한 파티션 ID: "{1}")
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY=뷰 "{1}"의 지속 데이터를 제거하는 중
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY)
REMOVE_VIEW_PERSISTENCY="{0}" 뷰의 지속된 데이터를 제거하는 프로세스를 시작하는 중
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_SUCCESS="{1}" 뷰의 데이터가 지속됩니다.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_SUCCESS)
START_VIEW_PERSISTENCY_PROCESS_SUCCESS="{0}" 뷰의 데이터가 지속됩니다.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY_SUCCESS="{1}" 뷰의 지속 데이터가 제거되고 가상 데이터 액세스가 복원되었습니다.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_SUCCESS)
REMOVE_VIEW_PERSISTENCY_SUCCESS="{0}" 뷰의 지속된 데이터를 제거하는 프로세스가 완료되었습니다.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_FAILED="{1}" 뷰의 데이터를 지속할 수 없습니다.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_FAILED)
START_VIEW_PERSISTENCY_PROCESS_FAILED="{0}" 뷰의 데이터를 지속할 수 없습니다.
#XFLD: Message for view persistency logs (for messages created before version 2022.05)
STOP_VIEW_PERSISTENCY_FAILED="{1}" 뷰의 지속 데이터를 제거할 수 없습니다.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_FAILED)
REMOVE_VIEW_PERSISTENCY_FAILED="{0}" 뷰의 지속 데이터를 제거할 수 없습니다.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_SUCCESS="{1}" 뷰의 "{3}"개의 레코드가 지속되었습니다.
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace START_PERSISTENCY_RECORD_COUNT_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS={0}개의 레코드가 "{1}" 뷰의 데이터 지속성 테이블에 삽입되었습니다.
#XFLD: Message used by SEAL repository for view persistency logs (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS_V2={0}개의 레코드가 뷰 "{1}"의 데이터 지속성 테이블에 삽입되었습니다. 사용된 메모리: {2}GiB.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_SUCCESS="{1}" 뷰의 "{3}"개의 지속 레코드가 제거되었습니다.
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace STOP_PERSISTENCY_RECORD_COUNT_SUCCESS)
REMOVE_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=지속 데이터가 제거되었습니다. "{0}"개의 지속 레코드가 삭제되었습니다.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_FAILED="{1}" 뷰의 recordCount를 가져오지 못했습니다.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_FAILED="{1}" 뷰의 recordCount를 가져오지 못했습니다.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS="{1}"에 대한 일정이 삭제되었습니다.
#XFLD: Message for view persistency logs (new message to replace STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS)
REMOVE_VIEW_PERSISTENCY_DELETE_SCHEDULE_SUCCESS="{0}" 뷰의 일정이 삭제되었습니다.
#XFLD: Message for view persistency logs
STOP_PERSISTENCY_DELETE_SCHEDULE_FAILED="{1}"에 대한 일정을 삭제하지 못했습니다.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_REDEPLOY=사용자가 뷰 "{0}" 지속을 시작한 후에 뷰가 변경되고 배포되어서 뷰를 지속할 수 없습니다. 뷰를 다시 지속해보거나 예정된 다음 실행까지 기다리십시오.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_DELETE=뷰 "{0}" 지속을 시작한 후에 삭제되어서 뷰를 지속할 수 없습니다.
#XFLD: Message for successful data persistency of a single partition (for messages created before version 2022.03)
START_PERSISTENCY_SINGLE_PARTITION_SUCCESS=값이 "{1}" <= {2} < "{3}"에 해당하는 {0}개의 레코드가 파티션에 지속되었습니다.
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (new message to replace START_PERSISTENCY_SINGLE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0}개의 레코드가 "{1}" <= "{2}" < "{3}" 값에 해당하는 파티션에 삽입되었습니다.
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS_V2={0}개의 레코드가 값 "{1}" <= "{2}" < "{3}"의 파티션에 삽입되었습니다. 사용된 메모리: {4}GiB.
#XFLD: Message for successful replication of a "others" partition (for messages created before version 2022.03)
START_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0}개의 레코드가 "기타" 파티션으로 지속되었습니다.
#XFLD: Message used by SEAL repository for successful replication of a "others" partition (new message to replace START_PERSISTENCY_OTHERS_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0}개의 레코드가 "기타" 파티션에 삽입되었습니다.
#XFLD: Message for view persistency record count of partitions (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={3}개의 레코드가 {4}개의 파티션에서 뷰 "{1}"에 대해 지속되었습니다.
#XFLD: Message used by SEAL repository for view persistency record count of partitions (new message to replace START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={0}개의 레코드가 {2}개의 파티션에서 뷰 "{1}"에 대해 데이터 지속성 테이블에 삽입되었습니다.
#XFLD: Message used by SEAL repository for view persistency for partition wise refresh
START_VIEW_PERSISTENCY_RECORD_COUNT_REFRESHED_PARTITIONS_SUCCESS=뷰 "{1}"의 지속성 테이블에 {0}개의 레코드를 삽입했습니다. 업데이트된 파티션: {2}, 잠긴 파티션: {3}, 총 파티션: {4}
#XFLD: Message used by SEAL repository for view persistency for manual partition load
START_VIEW_PERSISTENCY_RECORD_COUNT_MANUAL_PARTITIONS_SUCCESS=선택한 {2}개의 파티션에서 "{1}" 뷰의 데이터 지속성 테이블에 {0}개의 레코드를 삽입했습니다.
#XFLD: Message used by SEAL repository for view persistency for locked partition data load
START_VIEW_PERSISTENCY_RECORD_COUNT_LOCKED_PARTITIONS_SUCCESS=뷰 "{1}"의 데이터 지속성 테이블에 {0}개의 레코드를 삽입했습니다. 업데이트된 파티션: {2}, 잠겨서 변경되지 않은 파티션: {3}, 총 파티션: {4}
#XMSG: Message for unexpected error (for messages created before version 2022.03)
START_PERSISTENCY_UNEXPECTED_ERROR="{0}" 뷰에 대한 데이터를 지속하는 동안 예기치 않은 오류가 발생했습니다.
#XMSG: Message for unexpected error (new message to replace START_PERSISTENCY_UNEXPECTED_ERROR)
START_VIEW_PERSISTENCY_UNEXPECTED_ERROR="{0}" 뷰에 대한 데이터를 지속하는 동안 예기치 않은 오류가 발생했습니다.
#XMSG: Message for Locked space error
START_VIEW_PERSISTENCY_LOCKED_SPACE_ERROR=공간 "{1}"이(가) 잠겨있어서 뷰 "{0}"을(를) 지속할 수 없습니다.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
STOP_PERSISTENCY_UNEXPECTED_ERROR="{0}" 뷰의 지속 데이터를 제거하는 동안 예기치 않은 오류가 발생했습니다.
#XMSG: Message for unexpected error (new message to replace STOP_PERSISTENCY_UNEXPECTED_ERROR)
REMOVE_VIEW_PERSISTENCY_UNEXPECTED_ERROR="{0}" 뷰의 지속성을 제거하는 동안 예기치 않은 오류가 발생했습니다.
#XMSG: Message for ivalidated view
VIEW_PERSISTENCY_ABORTED_DEFINITION_INVALID="{0}" 뷰의 정의가 무효화되었습니다. 뷰에 직접 또는 간접적으로 사용되는 오브젝트에 발생한 변경 때문인 것 같습니다. 뷰 재배포를 시도하여 이슈를 해결하거나 근본 원인을 파악하십시오.
#XMSG: Message for persisted data removal (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT=지속 데이터는 뷰 "{0}"을(를) 배포하는 동안 제거됩니다.
#XMSG: Message for persisted data removal of a consuming view
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW=사용 뷰 "{0}"을(를) 배포하는 동안 지속 데이터가 제거되었습니다.
#XMSG: Message for persisted data removal of a consuming view due to a dac change
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW_DAC_CHANGED=데이터 액세스 제어가 변경되어서 사용 뷰 "{0}"을(를) 배포하는 동안 지속 데이터가 제거되었습니다.
#XMSG: Message for persisted data removal (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DEPLOYMENT="{0}" 뷰를 배포하는 동안 지속 데이터가 제거되었습니다.
#XMSG: Message for deleted view (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION="{0}" 뷰를 삭제하면 지속성이 제거됩니다.
#XMSG: Message for deleted view (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DELETION="{0}" 뷰를 삭제하면 지속성이 제거됩니다.
#XMSG Message for removal of persisted data, because prerequisites not fulfilled
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_PREREQUISITES_NOT_FULFILLED=데이터 지속성 선행조건이 더 이상 충족되지 않으므로 지속 데이터가 제거됩니다.
#XMSG: Message for inconsistent persistency
VIEW_PERSISTENCY_INCONSISTENT_PERSISTENCY="{0}" 뷰의 지속성에 일관성이 없습니다. 이러한 이슈를 해결하려면 지속된 데이터를 제거하십시오.
#XMSG: Message for prerequisites
START_PERSISTENCY_CHECK_PREREQUISITES="{0}" 뷰의 지속성에 필요한 선행조건을 점검하고 있습니다.
#XMSG: Message for DAC deployed in deprecated way using table function and join (for messages created before 2025.05)
START_PERSISTENCY_DEPRECIATED_DAC=비추천 상태인 데이터 액세스 제어(DAC)를 사용하여 "{0}" 뷰가 배포되었습니다. 성능을 향상시키려면 뷰를 다시 배포하십시오.
#XMSG: Message for DAC deployed in deprecated way using table function and join
START_PERSISTENCY_DEPRECATED_DAC=비추천 상태인 데이터 액세스 제어(DAC)를 사용하여 "{0}" 뷰가 배포되었습니다. 성능을 향상시키려면 뷰를 다시 배포하십시오.
#XMSG: Message for roll back (for messages created before version 2022.03)
ROLLBACK_VIEW_PERSISTENCY=오류가 발생했습니다. "{0}" 뷰에 대해 이전 지속성 상태가 복원되었습니다.
#XMSG: Message used by SEAL repository for rollback of running procedure to persist view (new message to replace ROLLBACK_VIEW_PERSISTENCY)
VIEW_PERSISTENCY_ROLLBACK=오류가 발생했습니다. "{0}" 뷰의 지속성 프로세스가 중단되고 변경사항이 롤백되었습니다.
#XMSG: Message used by SEAL repository for rollback of running procedure to remove persisted data of view
REMOVE_VIEW_PERSISTENCY_ROLLBACK=오류가 발생했습니다. "{0}" 뷰의 지속된 데이터를 제거하는 프로세스가 중단되고 변경사항이 롤백되었습니다.
#XMSG: Message used by SEAL repository for persistency preparation
START_PERSISTENCY_PREPARATION=데이터 지속을 준비하고 있습니다.
#XMSG: Message used by SEAL repository for data insertion
START_INSERTING_PERSISTENCY_DATA=지속성 테이블에 데이터를 삽입하고 있습니다.
#XMSG: Message for null values - partitioning (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS={0}개의 NULL 값 레코드가 "기타" 파티션에 삽입되었습니다.
#XMSG: Message used by SEAL repository for others partition values - partitioning (new message to replace START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS={0}개의 레코드가 값 "{2}" < "{1}" OR "{2}" >= "{3}"의 "기타" 파티션에 삽입되었습니다.
#XMSG: Message used by SEAL repository for others partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS_V2={0}개의 레코드가 값 "{2}" < "{1}" OR "{2}" >= "{3}"의 "기타" 파티션에 삽입되었습니다. 사용된 메모리: {4}GiB.
#XMSG: Message used by SEAL repository for null partition values - partitioning
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS={0}개의 레코드가 값 "{1}" IS NULL의 "기타" 파티션에 삽입되었습니다.
#XMSG: Message used by SEAL repository for null partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS_V2={0}개의 레코드가 값 "{1}" IS NULL의 "기타" 파티션에 삽입되었습니다. 사용된 메모리: {2}GiB.
##XFLD:Message used by SEAL repository for remote statistics
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS=관련된 데이터 로드: {0}개 원격 명령문. 가져온 총 레코드 수: {1}. 총 지속 시간: {2}초.
##XFLD:Message used by SEAL repository for remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS_WITH_PARTITIONS={1}개의 원격 명령문이 있는 {0}개의 파티션을 사용하여 관련된 데이터 로드. 가져온 총 레코드 수: {2}. 총 지속 시간: {3}초.
##XFLD:Message used by SEAL repository with UI ID for instructions of remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_INSTRUCTION=실행 중에 처리된 원격 명령문은 원격 쿼리 모니터를 열어 파티션별 메시지의 세부사항에 표시할 수 있습니다.
##XFLD:Message for start reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS=배포 후 {0} 뷰에 대해 기존 지속 데이터를 재사용하기 위한 프로세스를 시작하고 있습니다.
##XFLD:Message used by SEAL repository for reattach view persitency
START_VIEW_PERSISTENCY_REATTACHMENT=기존 지속 데이터 재사용을 시작합니다.
##XFLD:Message used by SEAL repository for reattach view persitency
START_FINILIZING_VIEW_PERSISTENCY_REATTACHMENT=기존 지속 데이터를 재사용하고 있습니다.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_COMPLETE={0} 뷰에 대해 기존 지속 데이터를 재사용하기 위한 프로세스가 완료되었습니다.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_FAILED=배포 후 {0} 뷰에 대해 기존 지속 데이터를 재사용하지 못했습니다.
#XMSG: Message used by SEAL repository for finalizing persistency
START_FINALIZING_PERSISTENCY=지속성을 종료하고 있습니다.
#XMSG: Message used by SEAL repository for restoring virtual access for view
REMOVE_VIEW_PERSISTENCY_VIRTUAL_ACCESS_RESTORED="{0}" 뷰에 대한 가상 데이터 액세스가 복원되었습니다.
#XMSG: Message used by SEAL repository for view that already has virtual access
REMOVE_VIEW_PERSISTENCY_ALREADY_VIRTUAL_ACCESS="{0}" 뷰에 이미 가상 데이터 액세스가 있습니다. 지속된 데이터가 제거되지 않습니다.
#XMSG: Message for removing view persistency
VIEW_PERSISTENCY_REMOVED_FROM_MONITOR="{0}" 뷰가 뷰 모니터에서 제거되었습니다.
#XMSG: Message for view doesnt exist.
VIEW_PERSISTENCY_VIEW_NOT_EXISTING="{0}" 뷰가 데이터베이스에 없거나 바르게 배포되지 않아서 지속될 수 없습니다. 뷰를 다시 배포하여 문제를 해결하거나 근본 원인을 확인해 보십시오.
#XMSG: Message for persistency not enabled.
VIEW_PERSISTENCY_NOT_ENABLED=데이터 지속성을 사용할 수 없습니다. 이 기능을 사용하려면 공간 "{0}"에 테이블/뷰를 재배포하십시오.
#XMSG: Message for unexpected error due to node crash
VIEW_PERSISTENCY_UNEXPECTED_ERROR_GENERAL=마지막 뷰 지속성 실행이 기술적 오류로 중단되었습니다.
#XFLD: Message for view persistency memory consumption
VIEW_PERSISTENCY_MEMORY_CONSUMPTION=뷰 지속성 런타임에 사용된 최대 메모리가 {0}GiB입니다.
#XFLD: Message for view persistency timeout
VIEW_PERSISTENCY_TIMEOUT=뷰 {0}의 지속성이 {1}시간의 시간 제한에 도달했습니다.
#XFLD: Message for not started asynchronous view persistency
VIEW_PERSISTENCY_ASYNC_NOT_STARTED=시스템 부하가 높아 뷰 지속성의 비동기 실행을 시작할 수 없습니다. 병렬 실행되는 태스크가 너무 많지 않은지 확인하십시오.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency (for messages created before version 2022.05)
START_VIEW_PERSISTENCY_EXECUTION_METHOD_DROP_AND_RENAME=기존의 지속 테이블이 삭제되고 새로운 지속 테이블로 바뀌었습니다.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_RECREATE_AND_DROP=기존의 지속 테이블이 삭제되고 새로운 지속 테이블로 바뀌었습니다.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_TRUNCATE_AND_COPY=기존의 지속 테이블이 새로운 데이터로 업데이트되었습니다.
##XFLD: Message for missing authorizations for view persistency
VIEW_PERSISTENCY_MISSING_AUTH=데이터 지속성에 대한 권한이 없습니다.
##XFLD: Message start of persistency cancellation
VIEW_PERSISTENCY_TRIGGER_CANCEL={0} 뷰를 지속하기 위한 프로세스 취소를 시작합니다.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_UNAVAILABLE_TASK_RUN={0} 뷰에 대해 실행 중인 데이터 지속성 태스크가 없으므로 뷰 지속을 위한 프로세스를 취소하지 못했습니다.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_NONDETERMINISTIC_TASK_RUN={0} 뷰에 대해 실행 중인 데이터 지속성 태스크가 없으므로 뷰 지속을 위한 프로세스를 취소하지 못했습니다.
##XFLD: Message for canceling a non running persitency task
VIEW_PERSISTENCY_CANCEL_NON_RUNNING_SELECTED_TASK=선택한 데이터 지속성 태스크 {1}이(가) 실행되고 있지 않기 때문에 뷰 {0}을(를) 지속하는 프로세스를 취소하지 못했습니다.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_UNREACHABLE_PERSISTENCY_RUN={0} 뷰의 데이터 지속성이 아직 시작되지 않았으므로 지속을 위한 프로세스를 취소하지 못했습니다.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_COMPLETED_PERSISTENCY_RUN={0} 뷰가 이미 완료되었으므로 뷰 지속을 위한 프로세스를 취소하지 못했습니다.
##XFLD: Message for failed cancellation
VIEW_PERSISTENCY_ABORTED_CANCEL_EXECUTION={0} 뷰를 지속하기 위한 프로세스를 취소하지 못했습니다.
##XFLD: Message for finalizing persistency cancellation
VIEW_PERSISTENCY_FINALIZE_CANCEL={0} 뷰의 데이터 지속성을 중단하기 위한 프로세스가 제출되었습니다.
##XFLD: Message used in persist task when cancellation is caught
VIEW_PERSISTENCY_CANCELED={0} 뷰 지속을 위한 프로세스가 중단되었습니다.
##XFLD: Message used in persist task when cancelation is caught and performed through a cancelation task
VIEW_PERSISTENCY_CANCELED_VIA_CANCEL_TASK={0} 뷰 지속을 위한 프로세스가 {1} 취소 태스크를 통해 중단되었습니다.
##XFLD: Message used in cancel task when the task is triggered by deployment
VIEW_PERSISTENCY_CANCEL_TRIGGERED_BY_DEPLOYMENT={0} 뷰를 배포하는 동안 데이터 지속을 위한 프로세스를 취소하는 중입니다.
##XFLD: Message used in case the cancelation is retriggered for a running task
VIEW_PERSISTENCY_CANCEL_ATTEMPT_RETRIGGER_CANCEL={0} 뷰 지속을 취소하는 태스크가 이전에 이미 제출되었습니다.
##XFLD: Message used to express the delay that may occur before the persistency task is stopped
VIEW_PERSISTENCY_CANCEL_DELAY_STOPPING_PERSISTENCY={0} 뷰의 데이터 지속성 태스크가 중단될 때까지 지연될 수 있습니다.
##XFLD: Message used to reflect the task log identifier of the current running persistency task
VIEW_PERSISTENCY_CANCEL_CURRENT_RUNNING_TASK=뷰 {0}에 대한 데이터는 {1} 태스크와 함께 유지됩니다.
##XFLD:Message used by SEAL repository for locked partitions and DACs
VIEW_PERSISTENCY_LOCKED_PARTITIONS_DACS=DAC에 의해 제공된 권한은 변경될 수 있으므로 잠긴 파티션에서 고려되지 않습니다. 파티션 잠금을 해제하고 새 스냅샷을 로드하여 변경사항을 적용하십시오.
##XFLD:Message used by SEAL repository for column structure change
VIEW_PERSISTENCY_COLUMN_STRUCTURE_CHANGE=열 구조가 변경되었으며 더 이상 기존 지속성 테이블과 일치하지 않습니다. 지속 데이터를 제거하고 새로운 데이터 지속성을 시작하여 지속성 테이블을 업데이트하십시오.
##XFLD: Message for out of memory
VIEW_PERSISTENCY_HANA_OUT_OF_MEMORY=SAP HANA 데이터베이스의 메모리 부족 오류로 인해 태스크에 실패했습니다.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_EXCEPTION_TRANSACTION_ROLLBACK=SAP HANA 데이터베이스의 내부 예외로 인해 태스크에 실패했습니다.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_SQL_EXECUTION_ISSUE=SAP HANA 데이터베이스의 내부 SQL 실행 이슈로 인해 태스크에 실패했습니다.
##XFLD: Message for HANA out of memory result
VIEW_PERSISTENCY_OUT_OF_MEMORY_RESULT_MESSAGE=HANA 메모리 부족 이벤트 사유: {0}
##XFLD: Message for admission control rejection
VIEW_PERSISTENCY_HANA_ADMISSION_CTRL_REJECTION=SAP HANA Admission Control 거부로 인해 태스크에 실패했습니다.
##XFLD: Message for HANA back pressure / rate limit of HANA client
VIEW_PERSISTENCY_HANA_CLIENT_RATE_LIMIT_REJECTION=활성 SAP HANA 연결이 너무 많아서 태스크에 실패했습니다.
#XMSG: Message for invalid persisted table name
VIEW_PERSISTENCY_PERSISTED_TABLE_INVALID=오류가 발생하여 지속 테이블이 유효하지 않게 되었습니다. 문제를 해결하려면 지속된 데이터를 제거하고 뷰를 다시 지속하십시오.
#XMSG: Message for usahe of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION=뷰가 지속 뷰가 될 수 없습니다. 사용자 전파가 활성화된 상태로 원격 소스를 기반으로 하는 원격 테이블이 사용됩니다. 뷰의 계보를 확인하십시오.
#XMSG: Message for dynamic usage of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION_DYNAMIC=뷰가 지속 뷰가 될 수 없습니다. 사용자 전파가 활성화된 상태로 원격 소스를 기반으로 하는 원격 테이블이 사용됩니다. SQLScript 뷰를 통해 원격 테이블이 동적으로 사용될 수 있습니다. 뷰의 계보에 원격 테이블이 표시되지 않을 수 있습니다.
#XMSG: Message for insufficient privileges or consumption of view with DAC via dynamic SQL
VIEW_PERSISTENCY_INSUF_PRIV_OR_DAC_DYN_SQL=권한이 충분하지 않을 수 있습니다. 데이터 미리보기를 열어서 필요한 권한이 있는지 확인하십시오. 권한이 있다면, 동적 SQL 스크립트를 통해 사용된 두 번째 뷰에 데이터 액세스 제어(DAC)가 적용된 것일 수 있습니다.
#XMSG: deployment of DAC is deprecated and view need to be redeployed to be able to persist data
VIEW_PERSISTENCY_DEPRECATED_DAC=비추천 상태인 데이터 액세스 제어(DAC)를 사용하여 "{0}" 뷰가 배포되었습니다. 뷰의 데이터가 지속될 수 있도록 뷰를 다시 배포하십시오.
##XFLD:Message used by SEAL repository for input parameter
START_VIEW_PERSISTENCY_INPUT_PARAMETER=입력 매개변수 "{1}"에 기본값 "{0}"을(를) 사용합니다.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Elastic compute node keys~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
##XFLD:Message used to reflect disablement of ecn replica
ECN_DISABLE_EXISTING_REPLICA=탄력적 계산 노드 복제가 비활성화됩니다.
##XFLD:Message used to reflect recreation of ecn replica
ECN_RECREATE_EXISTING_REPLICA=탄력적 계산 노드 복제가 재생성됩니다.
##XFLD:Message used to reflect re-enablement of ecn replica
ECN_ENABLE_EXISTING_REPLICA=탄력적 계산 노드 복제가 재활성화됩니다.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Task Schedule Menu key~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message to schedule task
scheduleText=일정
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=일정 생성
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=일정 편집
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=일정 삭제
#XFLD: Refresh frequency field
refreshFrequency=새로 고침 주기
#XFLD: Refresh frequency field
refreshFrequencyNew=주기
#XFLD: Refresh frequency field
refreshFrequencyNewNew=예정된 주기
#XBUT: label for None
none=없음
#XBUT: label for Real-Time replication state
realtime=실시간
#XFLD: Label for table column
txtNextSchedule=다음 실행
#XFLD: Label for table column
txtNextScheduleNew=예정된 다음 실행
#XFLD: Label for table column
txtNumOfRecords=레코드 수
#XFLD: Label for scheduled link
scheduledTxt=예약됨
#XFLD: LABEL for partially persisted link
partiallyPersisted=부분 지속됨
#XFLD: Text for paused text
paused=일시 중지됨

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ View Monitor Tasklog Details Action keys~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message for release lock actionn
releaseLockMsg=실행에 평소보다 오랜 시간이 소요된다면 이 실행이 실패했으며 상태가 적절히 업데이트되지 않았음을 나타낼 수 있습니다. \R\n 이슈를 해결하려면 잠금을 릴리스한 후 상태를 "실패"로 설정하십시오.
#XFLD: Label for release lock dialog
releaseLockText=잠금 릴리스

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Column header tooltip ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: tooltip for table column
txtViewTooltip=지속 뷰의 이름
#XFLD: tooltip for table column
txtViewDataAccessTooltip=뷰의 가용성을 나타냅니다.
#XFLD: tooltip for table column
txtViewRefreshFreqTooltip=뷰에 대한 일정이 정의되었는지 여부를 나타냅니다.
#XFLD: tooltip for table column
txtViewStatusTooltip=지속 뷰의 상태 가져오기
#XFLD: tooltip for table column
txtViewLatestUpdateTooltip=지속 뷰가 마지막으로 업데이트된 시기에 대한 정보를 제공합니다.
#XFLD: tooltip for table column
txtViewNextRunTooltip=뷰에 대한 일정이 설정된 경우 예약된 다음 실행의 시기를 확인하십시오.
#XFLD: tooltip for table column
txtViewNumOfRecordsTooltip=레코드 수 추적
#XFLD: tooltip for table column
txtViewInMemorySizeTooltip=메모리에서 사용 중인 뷰의 크기 추적
#XFLD: tooltip for table column
txtViewDiskSizeTooltip=디스크에서 차지하는 뷰의 크기 추적
#XMSG: Expired text
txtExpired=만료됨

#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=오브젝트 "{0}"을(를) 태스크 체인에 추가할 수 없습니다.

#~~~~~~~~~~~View Analyzer task log messages~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message indicating number of records for a view, and memory consumption in Mebibyte during simulation of view persistency
RECORDS_PEAK_MEMORY=뷰 "{0}"에 {1}개의 레코드가 있습니다. 이 뷰의 데이터 지속성 시뮬레이션에서 {2}MiB의 메모리를 사용했습니다.
#XMSG: View Analyzer execution failure
VIEW_ANALYZER_EXECUTION_FAILED=View Analyzer 실행에 실패했습니다.
#XMSG: Missing authorizations for View Analyzer
VIEW_ANALYZER_MISSING_AUTH=View Analyzer에 대한 권한이 없습니다.
#XMSG: Maximum memory in Gibibyte is reached, therefore persistency simulation is stopped
MAXIMUM_MEMORY_REACHED=뷰 "{1}"의 데이터 지속성 시뮬레이션 실행 중 {0}GiB의 최대 메모리 사용에 도달했습니다. 더 이상 데이터 지속성 시뮬레이션이 실행되지 않습니다.
#XMSG: Error during persistency simulation occurred
PERSISTENCY_SIMULATION_ERROR=뷰 "{0}"의 데이터 지속성 시뮬레이션 실행 중 오류가 발생했습니다.
#XMSG: Persistency simulation is not executed, because prerequisites are not fulfilled and the view cannot be persisted
PREREQUISITES_NOT_FULFILLED=선행조건이 충족되지 않았고 뷰를 지속할 수 없으므로 뷰 "{0}"의 데이터 지속성 시뮬레이션을 실행할 수 없습니다.
#XMSG: Persistency simulation is not executed, because the view does not exist in the database
VIEW_DOES_NOT_EXIST=데이터 지속성 시뮬레이션을 사용하려면 "{0}" 뷰를 배포해야 합니다.
#XMSG: Number of records cannot be determined, because the local table does not exist in the database
TABLE_DOES_NOT_EXIST=로컬 테이블 "{0}"이(가) 데이터베이스에 없어서 이 테이블의 레코드 수를 결정할 수 없습니다.
##XMSG: Message start of cancellation for View Analyzer task
CANCEL_VIEW_ANALYZER_TRIGGERED=뷰 "{1}"의 View Analyzer 태스크 {0}을(를) 중지할 프로세스가 제출되었습니다. 태스크가 중지될 때까지 지연이 발생할 수 있습니다.
##XMSG: Message start to indicate that task is not active
CANCEL_VIEW_ANALYZER_NO_ACTIVE_TASK=뷰 "{1}"의 View Analyzer 태스크 {0}이(가) 활성 상태가 아닙니다.
#XMSG: Cancel View Analyzer task failure
CANCEL_VIEW_ANALYZER_FAILED=View Analyzer 태스크를 취소하지 못했습니다.
##XMSG: Message used in View Analyzer execution task when task is canceled
VIEW_ANALYZER_CANCELED=뷰 "{0}"의 View Analyzer 실행이 취소 태스크를 통해 중지되었습니다.
##XMSG: Message start of cancelation for Model Validation task
CANCEL_MODEL_VALIDATION_TRIGGERED=뷰 "{1}"의 ''모델 유효성 확인'' 태스크 {0}을(를) 중지하는 프로세스가 제출되었습니다. 태스크가 중지될 때까지 지연이 발생할 수 있습니다.
##XMSG: Message to indicate that task is not active
CANCEL_MODEL_VALIDATION_NO_ACTIVE_TASK=뷰 "{1}"의 ''모델 유효성 확인'' 태스크 {0}이(가) 활성 상태가 아닙니다.
#XMSG: Cancel Model Validation task failure
CANCEL_MODEL_VALIDATION_FAILED= '모델 유효성 확인' 태스크를 취소하지 못했습니다.
##XMSG: Message used in Model Validation task when task is canceled
MODEL_VALIDATION_CANCELED=뷰 "{0}"의  ''모델 유효성 확인'' 실행이 취소 태스크를 통해 중지되었습니다.
#XMSG: Message for Locked space error, before starting Model Validation task
MODEL_VALIDATION_LOCKED_SPACE_ERROR=공간 "{1}"이(가) 잠겨 있어서 뷰 "{0}"에 대해 ''모델 유효성 확인''을 실행할 수 없습니다.
##XMSG: Message used in View Analyzer execution task when determination of number of rows fails
NUMBER_ROWS_DETERMINATION_FAILED=로컬 테이블 "{0}"의 행 수를 결정하는 동안 오류가 발생했습니다.
##XMSG: Message to indicate that SQL plan was created
EXECUTION_PLAN_CREATED=뷰 "{0}"의 SQL 분석기 계획 파일이 생성되어 다운로드할 수 있게 되었습니다.
#XFLD: Message to indicate starting of process to generate SQL Analyzer plan file
START_PLANVIZ_EXECUTION=뷰 "{0}"의 SQL 분석기 계획 파일을 생성할 프로세스를 시작하고 있습니다.
#XFLD: Message to indicate starting of View Analyzer process
VIEW_ANALYZER_EXECUTION_STARTED=View Analyzer 시작하는 중
##XMSG: Message to indicate that SQL plan file cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_PLANVIZ_NOT_FULFILLED=데이터 지속성 선행조건이 충족되지 않아 뷰 "{0}"의 SQL 분석기 계획 파일을 생성할 수 없습니다
##XMSG: Error message during  generation of SQL Analyzer plan file
PLANVIZ_ERROR=뷰 "{0}"의 SQL 분석기 계획 파일을 생성하는 동안 오류가 발생했습니다.
#XMSG: Message for Locked space error
VIEW_ANALYZER_LOCKED_SPACE_ERROR=공간 "{1}"이(가) 잠겨 있어 뷰 "{0}"에 대해 View Analyzer를 실행할 수 없습니다.
#XMSG: Message for persistency simulation without partitioning
PERSISTENCY_SIMULATION_WITHOUT_PARTITIONS=데이터 지속성 시뮬레이션 중 뷰 "{0}"의 파티션은 고려되지 않습니다
#XMSG: Message for generation of SQL Analyzer plan file without considering partitioning
PLANVIZ_WITHOUT_PARTITIONS=SQL 분석기 계획 파일 생성 중 뷰 "{0}"의 파티션은 고려되지 않습니다.
#XMSG: Confirmation message for mass removal of persisted data
MassRemovePersistency_Confirm_Msg=지속 데이터를 제거하고 데이터 액세스를 가상 액세스로 되돌리시겠습니까?
#XMSG: Confirmation message for partial mass removal of selected persisted data
PartialMassRemovePersistency_Confirm_Msg=선택된 뷰 {1}개 중 {0}개에 지속 데이터가 있습니다. \n 지속 데이터를 제거하고 데이터 액세스를 가상 액세스로 되돌리시겠습니까?
#XMSG: success message for stopping mass persistence
stopMassPersistenceSuccess=선택된 뷰에 대한 지속 데이터를 제거하고 있습니다.
#XMSG: error message during mass stop persistence of a view (backend error)
STOP_MASS_PERSISTENCE_ERROR=선택된 뷰의 지속성을 중지하는 동안 오류가 발생했습니다.
#XMSG: memory analysis is only performed in space {0}
NO_MEMORY_ANALYSIS=공간 "{0}"의 엔티티에 대해서만 메모리 분석이 수행됩니다. "{1}" "{2}"이(가) 스킵되었습니다.
##XMSG: Message to indicate that Explain Plan cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_EXPLAINPLAN_NOT_FULFILLED=지속성 선행조건이 충족되지 않아서 뷰 "{0}"의 Explain Plan 파일을 생성할 수 없습니다.
#XMSG: Message for Explain Plan generation without partitioning
EXPLAINPLAN_WITHOUT_PARTITIONS=Explain Plan 파일 생성 중 뷰 "{0}"의 파티션은 고려되지 않습니다.
#XFLD: Message to indicate starting of process to generate Explain Plan file
START_EXPLAINPLAN_EXECUTION=뷰 "{0}"의 Explain Plan 파일을 생성하는 프로세스를 시작하고 있습니다.
##XMSG: Message to indicate that Explain Plan was created
EXPLAINPLAN_CREATED=뷰 "{0}"의 Explain Plan 파일이 생성되었습니다. "세부사항 보기"를 클릭하여 조회하거나, 관련 권한을 보유한 경우 다운로드할 수 있습니다.
##XMSG: Error message during  generation of Explain Plan file
EXPLAINPLAN_ERROR=뷰 "{0}"의 Explain Plan 파일을 생성하는 동안 오류가 발생했습니다.
##XMSG: Explain Plan file cannot be generated for the view {0}, because too many views are stacked on each other
EXPLAINPLAN_MODEL_TOO_COMPLEX=뷰 "{0}"의 Explain Plan 파일을 생성할 수 없습니다. 너무 많은 뷰가 겹쳐 있습니다. 복잡한 모델은 메모리 부족 오류와 성능 저하의 원인이 될 수 있습니다. 하나의 뷰를 지속하는 것이 좋습니다.

##XMSG: Message to indicate that the space is locked
PERFORMANCE_ANALYSIS_LOCKED_SPACE_ERROR=공간 "{1}"이(가) 잠겨 있어서 뷰 "{0}"에 대해 성능 분석을 실행할 수 없습니다.
##XMSG: The performance analysis task was cancelled
PERFORMANCE_ANALYSIS_CANCELED=뷰 "{0}"에 대한 성능 분석이 취소되었습니다.
##XMSG: The performance analysis failed
PERFORMANCE_ANALYSIS_FAILED=성능 분석에 실패했습니다.
##XMSG: The performance analysis is finished
PERFORMANCE_ANALYSIS_FINISHED=뷰 "{0}"에 대한 성능 분석이 완료되었습니다. "뷰 세부사항"을 클릭하여 결과를 조회하십시오.
##XMSG: The view has input parameter with no default value
INPUT_PARAMETER_WITHOUT_DEFAULT_VALUE=기본값 없는 매개변수가 있으므로 이 뷰를 분석할 수 없습니다.
##XMSG: The view is not fully deployed
VIEW_NOT_FULLY_DEPLOYED=이 뷰는 완전히 배포되지 않았으므로 분석할 수 없습니다.
##XMSG: The view uses limited remote adapters
LIMITED_REMOTE_ADAPTER=이 뷰에는 '카운트' 지원 또는 필터 푸시다운 누락 같은 제한된 기능을 갖는 원격 어댑터가 하나 이상 사용됩니다. 오브젝트 지속 또는 복제 시 런타임 성능이 향상될 수 있습니다.
##XMSG: The view uses remote adapter that does not support 'Limit'
REMOTE_ADAPTER_NOT_SUPPORTING_LIMIT=이 뷰에 '한도'를 지원하지 않는 원격 어댑터가 하나 이상 사용됩니다. 1000개를 초과하는 레코드가 선택될 수 있습니다.
##XMSG: Performance analysis is executed by using default parameters of view'
INPUT_PARAMETER_WITH_DEFAULT_VALUE=뷰 매개변수의 기본값을 사용하여 성능 분석이 실행되었습니다.
##XMSG: Message to indicate that an error occurred
PERFORMANCE_ANALYSIS_ERROR=뷰 "{0}"에 대한 성능 분석 중 오류가 발생했습니다.
##XMSG: Message start of cancellation for Performance Analysis task
CANCEL_PERFORMANCE_ANALYSIS_TRIGGERED=뷰 "{1}"의 성능 분석 태스크 {0}을(를) 중지할 프로세스가 제출되었습니다. 태스크가 중지될 때까지 지연이 발생할 수 있습니다.
##XMSG: Message start to indicate that task is not active
CANCEL_PERFORMANCE_ANALYSIS_NO_ACTIVE_TASK=뷰 "{1}"의 성능 분석 태스크 {0}이(가) 활성 상태가 아닙니다.
#XMSG: Cancel Performance Analysis task failure
CANCEL_PERFORMANCE_ANALYSIS_FAILED=성능 분석 태스크를 취소하지 못했습니다.

#XBUT: Assign schedule menu button label
assignScheduleLabel=나에게 일정 지정
#XBUT: Pause schedule menu label
pauseScheduleLabel=일정 일시 중지
#XBUT: Resume schedule menu label
resumeScheduleLabel=일정 다시 시작
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=일정을 제거하는 동안 오류가 발생했습니다.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=일정을 지정하는 동안 오류가 발생했습니다.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=일정을 일시 중지하는 동안 오류가 발생했습니다.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=일정을 다시 시작하는 동안 오류가 발생했습니다.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted={0}개의 일정을 삭제하는 중입니다.
#XMSG: Message for starting mass assign of schedules
massAssignStarted={0}개 일정의 소유자를 변경하는 중입니다.
#XMSG: Message for starting mass pausing of schedules
massPauseStarted={0}개의 일정을 일시 중지하는 중입니다.
#XMSG: Message for starting mass resuming of schedules
massResumeStarted={0}개의 일정을 다시 시작하는 중입니다.
#XBUT: Select Columns Button
selectColumnsBtn=열 선택
#XFLD: Refresh tooltip
TEXT_REFRESH=새로 고침
#XFLD: Select Columns tooltip
text_selectColumns=열 선택


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Runtime Metrics ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Label for Runtime Metrics
RuntimeMetricsTitle=런타임 지표
#XFLD : Label for Run Button
runButton=실행
#XFLD : Label for Cancel Button
cancelButton=취소
#XFLD : Label for Close Button
closeButton=닫기
#XFLD : Label for Open View Analyzer Button
openViewAnalyzerButton=View Analyzer 열기
#XFLD : Label for Generate Explain Plan Button
generateExplainPlanButton=Explain Plan 생성
#XFLD : Label for Previous Run Column
previousRun=이전 실행
#XFLD : Label for Latest Run Column
latestRun=최근 실행
#XFLD : Label for time Column
time=시간
#XFLD : Label for Duration Column
duration=기간
#XFLD : Label for Peak Memory Column
peakMemory=최고 메모리
#XFLD : Label for Number of Rows
numberOfRows=행 수
#XFLD : Label for Overall Number of Sources
overallNumberOfSources=전체 소스 수
#XFLD : Label for Data Access Column
dataAccess=데이터 액세스
#XFLD : Label for Local Tables
localTables=로컬 테이블
#XFLD : Label for federatedRemoteTables
federatedRemoteTables=연결 원격 테이블(제한된 어댑터 기능 포함)
#XTXT Text for initial state of the runtime metrics
initialState=메트릭을 가져오려면 먼저 성능 분석을 실행해야 합니다. 이 작업에는 어느 정도 시간이 소요될 수 있지만, 필요할 경우 프로세스를 취소할 수 있습니다.
#XMSG: Cancel confirmation message
CancelRunConfirmationMessage=성능 분석의 현재 실행을 취소하시겠습니까?
#XTIT: Cancel dialog title
CancelRunTitle=실행 취소
#XFLD: Label for Number of Rows
NUMBER_ROWS=행 수
#XFLD: Label for Overall Number of Sources
OVERALL_NUMBER_SOURCES=전체 소스 수
#XFLD: Label for Data Access
DATA_ACCESS=데이터 액세스
#XFLD: Label for Remote tables
NUMBER_REMOTE_TABLES_LIMITED_ADAPTERS=연결 원격 테이블(제한된 어댑터 기능 포함)
#XFLD: Label for select statement
SELECT_STATEMENT='SELECT * FROM VIEW LIMIT 1000'
#XFLD: Label for duration
SELECT_RUNTIME=기간
#XFLD: Label for peak memory
SELECT_PEAK_MEMORY=최고 메모리
#XFLD: Label for count statement
SELECT_COUNT_STATEMENT='SELECT COUNT(*) FROM VIEW'
#XFLD: Label for count duration
SELECT_COUNT_RUNTIME=기간
#XFLD: Label for count peak memory
SELECT_COUNT_PEAK_MEMORY=최고 메모리
#XFLD: Label for count of number of local tables
NUMBER_LOCAL_TABLES_FILE=로컬 테이블(파일)
#XTXT: Text for running state of the runtime metrics
Running=실행 중...
#XFLD: Label for time
Time=시간
#XFLD: Label for virtual access
PA_VIRTUAL=가상
#XFLD: Label for persisted access
PA_PERSISTED=지속됨
PA_PARTIALLY_PERSISTED=부분 지속됨
#XTXT: Text for cancel
CancelRunSuccessMessage=성능 분석 실행을 취소하는 중입니다.
#XTXT: Text for cancel error
CancelRunErrorMessage=성능 분석 실행을 취소하는 동안 오류가 발생했습니다.
#XTXT: Text for explain plan generation
ExplainPlanStarted=뷰 "{0}"의 Explain Plan을 생성하는 중입니다. 
#XTXT: Text for performance analysis
PerformanceAnalysisStarted=뷰 "{0}"에 대한 성능 분석을 시작하고 있습니다.
#XTXT: Text for performance analysis error
ErrorFetchingPerformanceAnalysis=성능 분석 데이터를 가져오는 동안 오류가 발생했습니다.
#XTXT: Text for performance analysis error
conflictingTask=성능 분석 태스크가 이미 실행 중입니다.
#XFLD: Label for Errors
Errors=오류
#XFLD: Label for Warnings
Warnings=경고
#XFLD: Tooltip for missing privileges
openViewAnalyzerButtonTooltipNoPrivilege=View Analyzer를 열기 위해서는 DWC_DATAINTEGRATION(업데이트) 관리 권한이 필요합니다.
#XFLD: Tooltip for missing privileges
generateExplainPlanButtonTooltipNoPrivilege=Explain Plan을 생성하기 위해서는 DWC_RUNTIME(읽기) 관리 권한이 필요합니다.



#XFLD: Label for frequency column
everyLabel=매
#XFLD: Plural Recurrence text for Hour
hoursLabel=시간
#XFLD: Plural Recurrence text for Day
daysLabel=일
#XFLD: Plural Recurrence text for Month
monthsLabel=개월
#XFLD: Plural Recurrence text for Minutes
minutesLabel=분
