
#XBCB: Breadcrumb
viewMonitorBreadcrumb={spaceId}

#XFLD: Label for persisted view column
SOURCE=Zdroj
#XFLD: Label for persisted view column
NAME=Názov
#XFLD: Label for persisted view column
NAME_LABEL=Podnikový názov
#XFLD: Label for persisted view column
NAME_LABELNew=Objekt (podnikový názov)
#XFLD: Label for persisted view column
TECHINCAL_NAME=Technický názov
#XFLD: Label for persisted view column
TECHINCAL_NAMENew=Objekt (technický názov)
#XFLD: Label for persisted view column
DATA_PERSISTENCY=Prístup k údajom
#XFLD: Label for persisted view column
STATUS=Status
#XFLD: Label for persisted view column
LAST_UPDATED=Posledná aktualizácia
#XFLD: Label for persisted view column
IN_MEMORY_SIZE=Pamäť používaná na ukladanie (MiB)
#XFLD: Label for persisted view column
DISK_SIZE=Disk používaný na ukladanie (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_NEWW=Vnútorná pamäť (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_HEADER=Vnútorná pamäť
#XFLD: Label for persisted view column
DISK_SIZE_NEWW=Veľkosť na disku (MiB)
#XFLD: Label for persisted view column
DISK_SIZE_HEADER=Veľkosť na disku
#XFLD: Label for schedule owner column
txtScheduleOwner=Vlastník plánu
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Zobrazuje, kto vytvoril plán
#XFLD: Label for persisted view column
PERSISTED=Perzistentné
#XFLD: Label for persisted view column
TYPE=Typ
#XFLD: Label for View Selection Dialog column
changedOn=Zmenené dňa
#XFLD: Label for View Selection Dialog column
createdBy=Vytvoril
#XFLD: Label for log details column
txtViewPersistencyLogs=Upraviť protokoly
#XFLD: Label for log details column
txtViewPersistencyLogsNew=Detaily
#XFLD: text for values shown for Ascending sort order
SortInAsc=Triediť vzostupne
#XFLD: text for values shown for Descending sort order
SortInDesc=Triediť zostupne
#XFLD: Label for persisted view monitor heading
ViewMonitor_Heading=Monitor zobrazení
#XFLD: Label for persisted view monitor sub-heading
ViewMonitor_SubHeading=Monitorujte a udržujte perzistenciu údajov zobrazení


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ DynamicPageHeader ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: text for values shown in column Persistence Status
txtLoading=Načítava sa
#XFLD: text for values shown in column Persistence Status
txtRunning=Spustené
#XFLD: text for values shown in column Persistence Status
txtAvailable=K dispozícii
#XFLD: text for values shown in column Persistence Status
txtError=Chyba
#XMSG: Error message for unknown Persistence type that gets logged in Kibana
unknownReplicationTypeError=Typ replikácie „{0}“ nie je podporovaný.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Content ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Nastavenia použité pri poslednom spustení uchovávania údajov:
#XMSG: Message for input parameter name
inputParameterLabel=Vstupný parameter
#XMSG: Message for input parameter value
inputParameterValueLabel=Hodnota
#XMSG: Message for persisted data
inputParameterPersistedLabel=Vytvorená perzistencia o
#XTIT: Count for View in ViewMonitor Toolbar
txtViewCount=Zobrazenia ({0})
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Perzistencia zobrazenia
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Perzistencia údajov
#XBUT: Button to clear the selected view persistance
ClearViewPersistance=Vymazať
#XBUT: Button to stop the selected view persistance
stopPersistance=Zastaviť perzistenciu
#XFLD: Placeholder for Search field
txtSearch=Hľadať
#XBUT: Tooltip for refresh button
txtRefresh=Obnoviť
#XBUT: Tooltip for add view button
txtDeleteView=Odstrániť perzistenciu
#XBUT: Tooltip for load new peristence
loadNewPersistence=Znovu spustiť perzistenciu
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Načítať novú snímku
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Spustiť vytvorenie perzistencie údajov
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Odstrániť perzistentné údaje
#XMSG: success message for starting persistence
startPersistenceSuccess=Vykonávame perzistenciu zobrazenia „{0}“.
#XMSG: success message for stopping persistence
stopPersistenceSuccess=Odstraňujeme perzistentné údaje pre zobrazenie „{0}“.
#XMSG: success message for stopping persistence
deletePersistedViewSuccess=Odstraňujeme zobrazenie „{0}“ zo zoznamu kontrol.
#XMSG: error message during start persistence of a view (backend error)
ERROR_STARTING_PERSISTENCE=Pri spustení perzistencie údajov pre zobrazenie „{0}“ sa vyskytla chyba.
#XMSG: error message during start persistence of a view (backend error)
PARAMERROR_STARTING_PERSISTENCE=Zobrazenie „{0}“ nemôže byť zachované, pretože obsahuje vstupné parametre.
#XMSG: error message during start persistence of a view (backend error)
NUMBER_OF_PARAMETER_STARTING_PERSISTENCE=Zobrazenie „{0}“ nemôže byť zachované, pretože má viac ako jeden vstupný parameter.
#XMSG: error message during start persistence of a view (backend error)
PARAMETER_DEFAULT_VALUE_STARTING_PERSISTENCE=Zobrazenie „{0}“ nemôže byť zachované, pretože vstupný parameter nemá predvolenú hodnotu.
#XMSG: error message during start persistence of a view (backend error)
REDEPLOYMENT_DAC_REQUIRED=Musíte znovu nasadiť riadenie prístupu k údajom „{0}“ na podporu perzistencie údajov.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_STARTING_PERSISTENCE=Zobrazenie „{0}“ nie je možné trvalo uložiť, pretože používa zobrazenie „{1}“, ktoré obsahuje riadenie prístupu k údajom.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_EXTERNAL_STARTING_PERSISTENCE=Zobrazenie „{0}“ nie je možné zachovať, pretože používa zobrazenie s riadením prístupu k údajom (DAC), ktoré patrí do iného priestoru.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_STRUCTURE_STARTING_PERSISTENCE=Zobrazenie ''‘{0}'' nemožno uchovať, pretože štruktúra jedného alebo viacerých jeho riadení prístupu k údajom (DAC) nepodporuje perzistenciu údajov.
#XMSG: error message during stop persistence of a view (backend error)
STOP_PERSISTENCE_ERROR=Pri zastavení perzistencie pre zobrazenie „{0}“ sa vyskytla chyba.
#XMSG: error message during stop persistence of a view (backend error)
DELETE_PERSISTED_VIEW_ERROR=Pri odstraňovaní perzistentného zobrazenia „{0}“ sa vyskytla chyba.
#XMSG: Warning message for removing the persisted data
RemovePersistency_Confirm_Msg=Chcete odstrániť perzistentné údaje a prepnúť virtuálny prístup zobrazenia „{0}“?
#XMSG: Warning message for delete the view persistence
DeletePersistency_Confirm_Msg=Chcete odstrániť zobrazenie zo zoznamu kontrol a perzistentné údaje zobrazenia „{0}“?
#XMSG: error message for reading data from backend
txtReadBackendError=Zdá sa, že počas načítania z backendu sa vyskytla chyba.
#XFLD: Label for No Data Error
NoDataError=Chyba
#XMSG: message for conflicting task
Task_Already_Running=Konfliktná úloha už prebieha pre zobrazenie „{0}“.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Show All Views~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Text for show all views button
allViewsText=Všetky zobrazenia ({0})
#XBUT: Text for show scheduled views button
scheduledText=Naplánované ({0})
#XBUT: Text for show persisted views button
persistedText=Perzistentné ({0})
#XBUT: Text for start analyzer button
startAnalyzer=Spustiť analyzátor zobrazenia View Analyzer
#XFLD: Message if repository is unavailable
repositoryErrorMsg=Úložisko nie je k dispozícii a niektoré funkcie sú vypnuté.

#XFLD: Data Access - Virtual
Virtual=Virtuálne
#XFLD: Data Access - Persisted
Persisted=Perzistentné

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Non-Persisted View Selection Dialog~~~~~~~~~~~~~~~~~~~~~~~

#XTIT: Title for Non-Persisted View selection dialog
select_source=Vybrať zobrazenie na vykonanie perzistencie

#XTIT: Placeholder for Non-Persisted View selection dialog search
searchView=Vyhľadať zobrazenia
#XTIT: No data in the list of non-persisted view
No_Data=Žiadne údaje
#XBUT: Button to select non-persisted view
ok=OK
#XBUT: Button to close the non-persisted views selection dialog
cancel=Zrušiť

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~View persistency log message key~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message for view persistency logs
START_STOP_PERSISTENCY=Spúšťa sa vykonanie úlohy perzistencie zobrazenia pre „{1}“.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY=Údaje sa trvalo ukladajú pre zobrazenie „{1}“.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY)
START_VIEW_PERSISTENCY_PROCESS=Spúšťa sa proces perzistencie údajov pre zobrazenie „{0}“.
#XFLD: Message for view persistency manual partition load
START_VIEW_PERSISTENCY_PROCESS_MANUAL_PARTITIONS=Spúšťa sa proces perzistencie údajov pre zobrazenie ''‘{0}'' s vybratými ID segmentov: ''{1}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY=Odstraňujú sa perzistentné údaje pre zobrazenie „{1}“.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY)
REMOVE_VIEW_PERSISTENCY=Spúšťa sa proces na odstránenie perzistentných údajov pre zobrazenie „{0}“.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_SUCCESS=Údaje trvalo uložené pre zobrazenie „{1}“.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_SUCCESS)
START_VIEW_PERSISTENCY_PROCESS_SUCCESS=Údaje trvalo uložené pre zobrazenie „{0}“.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY_SUCCESS=Perzistentné údaje boli odstránené a virtuálny prístup k údajom bol obnovený pre zobrazenie „{1}“.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_SUCCESS)
REMOVE_VIEW_PERSISTENCY_SUCCESS=Bol dokončený proces odstránenia perzistentných údajov pre zobrazenie „{0}“.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_FAILED=Údaje nie je možné trvalo uložiť pre zobrazenie „{1}“.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_FAILED)
START_VIEW_PERSISTENCY_PROCESS_FAILED=Údaje nie je možné trvalo uložiť pre zobrazenie „{0}“.
#XFLD: Message for view persistency logs (for messages created before version 2022.05)
STOP_VIEW_PERSISTENCY_FAILED=Nie je možné odstrániť perzistentné údaje pre zobrazenie „{1}“.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_FAILED)
REMOVE_VIEW_PERSISTENCY_FAILED=Nie je možné odstrániť perzistentné údaje pre zobrazenie „{0}“.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_SUCCESS=„{3}“ záznamov trvalo uložených pre zobrazenie „{1}“.
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace START_PERSISTENCY_RECORD_COUNT_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=„{0}“ záznamov vložených do tabuľky perzistencie údajov pre zobrazenie „{1}“.
#XFLD: Message used by SEAL repository for view persistency logs (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS_V2={0} záznamov vložených do tabuľky perzistencie údajov pre zobrazenie ''{1}''. Využitá pamäť: {2} GiB.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_SUCCESS=„{3}“ perzistentných záznamov odstránených pre zobrazenie „{1}“.
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace STOP_PERSISTENCY_RECORD_COUNT_SUCCESS)
REMOVE_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=Perzistentné údaje odstránené, „{0}“ perzistentných záznamov odstránených.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_FAILED=Načítanie recordCount zlyhalo pre zobrazenie „{1}“.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_FAILED=Načítanie recordCount zlyhalo pre zobrazenie „{1}“.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Plán je odstránený pre „{1}“.
#XFLD: Message for view persistency logs (new message to replace STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS)
REMOVE_VIEW_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Plán je odstránený pre zobrazenie „{0}“.
#XFLD: Message for view persistency logs
STOP_PERSISTENCY_DELETE_SCHEDULE_FAILED=Odstránenie plánu zlyhalo pre „{1}“.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_REDEPLOY=Zobrazenie „{0}“ nemôžeme trvalo uložiť, pretože bolo zmenené a nasadené od spustenia trvalého uloženia. Skúste zobrazenie znovu trvalo uložiť alebo počkajte do ďalšieho plánovaného chodu.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_DELETE=Zobrazenie „{0}“ nemôžeme trvalo uložiť, pretože bolo odstránené od spustenia trvalého uloženia.
#XFLD: Message for successful data persistency of a single partition (for messages created before version 2022.03)
START_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} záznamov trvalo uložených do segmentu pre hodnoty „{1}“ <= {2} < „{3}“.
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (new message to replace START_PERSISTENCY_SINGLE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} záznamov vložených do segmentu pre hodnoty „{1}“ <= {2} < „{3}“.
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS_V2={0} záznamov vložených do segmentu pre hodnoty ''{1}'' <= ''{2}'' < ''{3}''. Využitá pamäť: {4} GiB.
#XFLD: Message for successful replication of a "others" partition (for messages created before version 2022.03)
START_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} záznamov trvalo uložených do segmentu „iné“.
#XFLD: Message used by SEAL repository for successful replication of a "others" partition (new message to replace START_PERSISTENCY_OTHERS_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} záznamov vložených do segmentu „iné“.
#XFLD: Message for view persistency record count of partitions (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={3} záznamov vložených pre zobrazenie „{1}“ v {4} segmentoch.
#XFLD: Message used by SEAL repository for view persistency record count of partitions (new message to replace START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={0} záznamov vložených do tabuľky perzistencie údajov pre zobrazenie "{1}“ v segmente {2}.
#XFLD: Message used by SEAL repository for view persistency for partition wise refresh
START_VIEW_PERSISTENCY_RECORD_COUNT_REFRESHED_PARTITIONS_SUCCESS={0} záznamov sa vložilo do tabuľky perzistencie údajov ''{1}''. Aktualizované segmenty:{2}; Blokované segmenty:{3}; Segmenty celkom:{4}
#XFLD: Message used by SEAL repository for view persistency for manual partition load
START_VIEW_PERSISTENCY_RECORD_COUNT_MANUAL_PARTITIONS_SUCCESS={0} záznamov sa vložilo do tabuľky perzistencie údajov na zobrazenie ''‘{1}'' v {2} vybratých segmentoch.
#XFLD: Message used by SEAL repository for view persistency for locked partition data load
START_VIEW_PERSISTENCY_RECORD_COUNT_LOCKED_PARTITIONS_SUCCESS={0} záznamov sa vložilo do tabuľky perzistencie údajov ''{1}''. Aktualizované segmenty:{2}; Blokované nezmenené segmenty:{3}; Segmenty celkom:{4}
#XMSG: Message for unexpected error (for messages created before version 2022.03)
START_PERSISTENCY_UNEXPECTED_ERROR=Počas trvalého ukladania údajov pre zobrazenie „{0}“ sa vyskytla neočakávaná chyba.
#XMSG: Message for unexpected error (new message to replace START_PERSISTENCY_UNEXPECTED_ERROR)
START_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Počas trvalého ukladania údajov pre zobrazenie „{0}“ sa vyskytla neočakávaná chyba.
#XMSG: Message for Locked space error
START_VIEW_PERSISTENCY_LOCKED_SPACE_ERROR=Zobrazenie ''‘{0}'' nie je možné zachovať, pretože priestor ''‘{1}'' je uzamknutý.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
STOP_PERSISTENCY_UNEXPECTED_ERROR=Počas odstraňovania trvalo uložených údajov pre zobrazenie „{0}“ sa vyskytla neočakávaná chyba.
#XMSG: Message for unexpected error (new message to replace STOP_PERSISTENCY_UNEXPECTED_ERROR)
REMOVE_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Počas odstraňovania perzistencie pre zobrazenie „{0}“ sa vyskytla neočakávaná chyba.
#XMSG: Message for ivalidated view
VIEW_PERSISTENCY_ABORTED_DEFINITION_INVALID=Definícia zobrazenia „{0}“ je neplatná, pravdepodobne z dôvodu z meny objektu, ktorý priamo alebo nepriamo používa zobrazenie. Skúste znovu nasadiť zobrazenie za účelom v vyriešenia problému alebo identifikácie hlavnej príčiny.
#XMSG: Message for persisted data removal (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT=Perzistentné údaje sú odstránené počas nasadenia zobrazenia „{0}“.
#XMSG: Message for persisted data removal of a consuming view
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW=Perzistentné údaje sú odstránené počas nasadenia spotrebovaného zobrazenia „{0}“.
#XMSG: Message for persisted data removal of a consuming view due to a dac change
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW_DAC_CHANGED=Perzistentné údaje sa odstránia počas nasadenia spotrebovaného zobrazenia ''{0}'', pretože sa zmenilo jeho riadenie prístupu k údajom.
#XMSG: Message for persisted data removal (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DEPLOYMENT=Perzistentné údaje sú odstránené počas nasadenia zobrazenia „{0}“.
#XMSG: Message for deleted view (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION=Perzistencia sa odstráni pri odstránení zobrazenia „{0}“.
#XMSG: Message for deleted view (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DELETION=Perzistencia sa odstráni pri odstránení zobrazenia „{0}“.
#XMSG Message for removal of persisted data, because prerequisites not fulfilled
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_PREREQUISITES_NOT_FULFILLED=Perzistentné údaje sa odstránia, pretože už nie sú splnené predpoklady perzistencie údajov.
#XMSG: Message for inconsistent persistency
VIEW_PERSISTENCY_INCONSISTENT_PERSISTENCY=Perzistencia zobrazenia „{0}“ je nekonzistentná. Problém vyriešite odstránením pretrvávajúcich údajov.
#XMSG: Message for prerequisites
START_PERSISTENCY_CHECK_PREREQUISITES=Kontrolujú sa predpoklady pre perzistenciu zobrazenia „{0}“.
#XMSG: Message for DAC deployed in deprecated way using table function and join (for messages created before 2025.05)
START_PERSISTENCY_DEPRECIATED_DAC=Zobrazenie "{0}" je nasadené pomocou riadenia prístupu k údajom (DAC), ktoré bude zastarané. Znova nasaďte zobrazenie, aby ste zlepšili výkon.
#XMSG: Message for DAC deployed in deprecated way using table function and join
START_PERSISTENCY_DEPRECATED_DAC=Zobrazenie "{0}" je nasadené pomocou riadenia prístupu k údajom (DAC), ktoré bude zastarané. Znova nasaďte zobrazenie, aby ste zlepšili výkon.
#XMSG: Message for roll back (for messages created before version 2022.03)
ROLLBACK_VIEW_PERSISTENCY=Vyskytla sa chyba. Bol obnovený predchádzajúci stav perzistencie pre zobrazenie „{0}“.
#XMSG: Message used by SEAL repository for rollback of running procedure to persist view (new message to replace ROLLBACK_VIEW_PERSISTENCY)
VIEW_PERSISTENCY_ROLLBACK=Vyskytla sa chyba. Proces perzistencie zobrazenia „{0}“'' bol zastavený a zmeny boli vrátené.
#XMSG: Message used by SEAL repository for rollback of running procedure to remove persisted data of view
REMOVE_VIEW_PERSISTENCY_ROLLBACK=Vyskytla sa chyba. Proces odstraňovania perzistentných údajov zobrazenia „{0}“'' bol zastavený a zmeny boli anulované.
#XMSG: Message used by SEAL repository for persistency preparation
START_PERSISTENCY_PREPARATION=Pripravuje sa perzistencia údajov.
#XMSG: Message used by SEAL repository for data insertion
START_INSERTING_PERSISTENCY_DATA=Vkladajú sa údaje do tabuľky perzistencie.
#XMSG: Message for null values - partitioning (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS={0} záznamov hodnôt null bolo vložených do segmentu „iné“.
#XMSG: Message used by SEAL repository for others partition values - partitioning (new message to replace START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS={0} záznamov vložených do segmentu „iné“ pre hodnoty ''{2}'' < ''{1}'' ALEBO ''{2}'' >= ''{3}''.
#XMSG: Message used by SEAL repository for others partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS_V2={0} záznamov vložených do segmentu ''iné''‘ pre hodnoty ''{2}'' < ''{1}'' ALEBO ''{2}'' >= ''{3}''. Využitá pamäť: {4} GiB.
#XMSG: Message used by SEAL repository for null partition values - partitioning
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS={0} záznamov vložených do segmentu „iné“ pre hodnoty ''{1}'' má hodnotu NULL.
#XMSG: Message used by SEAL repository for null partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS_V2={0} záznamov vložených do segmentu „iné“ pre hodnoty ''{1}'' má hodnotu NULL. Využitá pamäť: {2} GiB.
##XFLD:Message used by SEAL repository for remote statistics
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS=Načítavanie príslušných dát: {0} vzdialených príkazov: Celkový počet vyvolaných záznamov: {1}. Celková doba trvania: {2} sekúnd.
##XFLD:Message used by SEAL repository for remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS_WITH_PARTITIONS=Načítanie príslušných dát pomocou {0} segmentov so {1} vzdialenými príkazmi. Celkový počet vyvolaných záznamov: {2}. Celková doba trvania: {3} sekúnd.
##XFLD:Message used by SEAL repository with UI ID for instructions of remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_INSTRUCTION=Vzdialené príkazy spracované počas chodu je možné zobraziť otvorením monitoru vzdialeného dotazu, v detailoch správ špecifických pre segment.
##XFLD:Message for start reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS=Spustenie procesu opätovného použitia existujúcich perzistentných dát pre zobrazenie {0} po nasadení.
##XFLD:Message used by SEAL repository for reattach view persitency
START_VIEW_PERSISTENCY_REATTACHMENT=Začnite znova používať existujúce perzistentné dáta.
##XFLD:Message used by SEAL repository for reattach view persitency
START_FINILIZING_VIEW_PERSISTENCY_REATTACHMENT=Opätovné použitie existujúcich perzistentných dát.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_COMPLETE=Proces opätovného použitia existujúcich perzistentných dát je pre zobrazenie {0} dokončený.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_FAILED=Zlyhalo opätovné použitie existujúcich perzistentných dát pre zobrazenie {0} po nasadení.
#XMSG: Message used by SEAL repository for finalizing persistency
START_FINALIZING_PERSISTENCY=Dokončuje sa perzistencia.
#XMSG: Message used by SEAL repository for restoring virtual access for view
REMOVE_VIEW_PERSISTENCY_VIRTUAL_ACCESS_RESTORED=Prístup k virtuálnym údajom obnovený pre zobrazenie „{0}“.
#XMSG: Message used by SEAL repository for view that already has virtual access
REMOVE_VIEW_PERSISTENCY_ALREADY_VIRTUAL_ACCESS=Zobrazenie "{0}" už má virtuálny prístup k údajom. Žiadne perzistentné údaje sa neodstránia.
#XMSG: Message for removing view persistency
VIEW_PERSISTENCY_REMOVED_FROM_MONITOR=Zobrazenie „{0}“ bolo odstránené z monitora Zobrazenia.
#XMSG: Message for view doesnt exist.
VIEW_PERSISTENCY_VIEW_NOT_EXISTING=Zobrazenie "{0}" buď neexistuje v databáze, alebo nie je správne nasadené, a preto ho nemožno zachovať. Pokúste sa znova nasadiť zobrazenie, aby ste vyriešili problém alebo identifikovali hlavnú príčinu.
#XMSG: Message for persistency not enabled.
VIEW_PERSISTENCY_NOT_ENABLED=Perzistencia údajov nie je aktivovaná. Znovu nasaďte tabuľku/zobrazenie v priestore „{0}“, aby ste aktivovali funkčnosť.
#XMSG: Message for unexpected error due to node crash
VIEW_PERSISTENCY_UNEXPECTED_ERROR_GENERAL=Posledný chod perzistencie zobrazenia bol prerušený z dôvodu technických chýb.
#XFLD: Message for view persistency memory consumption
VIEW_PERSISTENCY_MEMORY_CONSUMPTION={0} GiB maximálne využitej pamäte v dobe chodu perzistencie zobrazenia.
#XFLD: Message for view persistency timeout
VIEW_PERSISTENCY_TIMEOUT=Perzistencia zobrazenia {0} dosiahla časový limit {1} hodín.
#XFLD: Message for not started asynchronous view persistency
VIEW_PERSISTENCY_ASYNC_NOT_STARTED=Vysoké zaťaženie systému zabránilo spusteniu asynchrónneho vykonávania perzistencie zobrazenia. Skontrolujte, či paralelne neprebieha príliš veľa úloh.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency (for messages created before version 2022.05)
START_VIEW_PERSISTENCY_EXECUTION_METHOD_DROP_AND_RENAME=Existujúca tabuľka perzistencie bola odstránená a nahradená novou tabuľkou perzistencie.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_RECREATE_AND_DROP=Existujúca tabuľka perzistencie bola odstránená a nahradená novou tabuľkou perzistencie.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_TRUNCATE_AND_COPY=Existujúca tabuľka perzistencie bola aktualizovaná o nové údaje.
##XFLD: Message for missing authorizations for view persistency
VIEW_PERSISTENCY_MISSING_AUTH=Chýbajú oprávnenia pre perzistenciu údajov.
##XFLD: Message start of persistency cancellation
VIEW_PERSISTENCY_TRIGGER_CANCEL=Proces sa začína rušiť, aby sa zachovalo zobrazenie {0}.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_UNAVAILABLE_TASK_RUN=Nepodarilo sa zrušiť proces perzistencie zobrazenia, pretože pre zobrazenie {0} nie je spustená žiadna úloha perzistencie.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_NONDETERMINISTIC_TASK_RUN=Nepodarilo sa zrušiť proces perzistencie zobrazenia, pretože pre zobrazenie {0} nie je spustená žiadna úloha perzistencie údajov.
##XFLD: Message for canceling a non running persitency task
VIEW_PERSISTENCY_CANCEL_NON_RUNNING_SELECTED_TASK=Nepodarilo sa zrušiť proces na zachovanie zobrazenia {0}, pretože vybratá úloha perzistencie {1} nie je spustená.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_UNREACHABLE_PERSISTENCY_RUN=Nepodarilo sa zrušiť proces perzistencie zobrazenia, pretože perzistencia údajov pre zobrazenie {0} ešte nezačala.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_COMPLETED_PERSISTENCY_RUN=Nepodarilo sa zrušiť proces perzistencie zobrazenia {0}, pretože už bol dokončený.
##XFLD: Message for failed cancellation
VIEW_PERSISTENCY_ABORTED_CANCEL_EXECUTION=Nepodarilo sa zrušiť proces perzistenicie zobrazenia {0}.
##XFLD: Message for finalizing persistency cancellation
VIEW_PERSISTENCY_FINALIZE_CANCEL=Proces na zastavenie perzistencie údajov zobrazenia {0} bol odovzdaný.
##XFLD: Message used in persist task when cancellation is caught
VIEW_PERSISTENCY_CANCELED=Proces perzistencie zobrazenia {0} bol zastavený.
##XFLD: Message used in persist task when cancelation is caught and performed through a cancelation task
VIEW_PERSISTENCY_CANCELED_VIA_CANCEL_TASK=Proces perzistencie zobrazenia {0} bol zastavený úlohou zrušenia {1}.
##XFLD: Message used in cancel task when the task is triggered by deployment
VIEW_PERSISTENCY_CANCEL_TRIGGERED_BY_DEPLOYMENT=Zrušenie procesu na perzistenciu údajov počas nasadenia zobrazenia {0}.
##XFLD: Message used in case the cancelation is retriggered for a running task
VIEW_PERSISTENCY_CANCEL_ATTEMPT_RETRIGGER_CANCEL=Predchádzajúca úloha zrušenia pre perzistenciu zobrazenia {0} už bola odoslaná.
##XFLD: Message used to express the delay that may occur before the persistency task is stopped
VIEW_PERSISTENCY_CANCEL_DELAY_STOPPING_PERSISTENCY=Pokým sa úloha perzistencie údajov pre zobrazenie {0} nezastaví, môže dôjsť k oneskoreniu.
##XFLD: Message used to reflect the task log identifier of the current running persistency task
VIEW_PERSISTENCY_CANCEL_CURRENT_RUNNING_TASK=Údaje na zobrazenie {0} sa trvalo ukladajú pomocou úlohy {1}.
##XFLD:Message used by SEAL repository for locked partitions and DACs
VIEW_PERSISTENCY_LOCKED_PARTITIONS_DACS=Oprávnenia poskytnuté DAC sa mohli zmeniť a blokované segmenty ich nezohľadňujú. Aby ste prevzali zmeny. zrušte blokovanie segmentov a zaveďte novú snímku.
##XFLD:Message used by SEAL repository for column structure change
VIEW_PERSISTENCY_COLUMN_STRUCTURE_CHANGE=Štruktúra stĺpcov sa zmenila a už nezodpovedá existujúcej tabuľke perzistencie. Odstráňte trvalé údaje a spustite nové uchovávanie údajov, aby ste aktualizovali svoju tabuľku perzistencie.
##XFLD: Message for out of memory
VIEW_PERSISTENCY_HANA_OUT_OF_MEMORY=Úloha zlyhala z dôvodu nedostatku pamäte v databáze SAP HANA.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_EXCEPTION_TRANSACTION_ROLLBACK=Úloha zlyhala z dôvodu internej výnimky v databáze SAP HANA.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_SQL_EXECUTION_ISSUE=Úloha zlyhala z dôvodu interného problému so spustením SQL v databáze SAP HANA.
##XFLD: Message for HANA out of memory result
VIEW_PERSISTENCY_OUT_OF_MEMORY_RESULT_MESSAGE=HANA - dôvod udalosti nedostatku pamäte: {0}
##XFLD: Message for admission control rejection
VIEW_PERSISTENCY_HANA_ADMISSION_CTRL_REJECTION=Úloha zlyhala z dôvodu zamietnutia kontroly prístupu SAP HANA.
##XFLD: Message for HANA back pressure / rate limit of HANA client
VIEW_PERSISTENCY_HANA_CLIENT_RATE_LIMIT_REJECTION=Úloha zlyhala z dôvodu príliš veľkého počtu aktívnych pripojení SAP HANA.
#XMSG: Message for invalid persisted table name
VIEW_PERSISTENCY_PERSISTED_TABLE_INVALID=Vyskytla sa chyba a perzistentná tabuľka sa stala neplatnou. Ak chcete problém vyriešiť, odstráňte perzistentné údaje a znova vykonajte perzistenciu zobrazenia.
#XMSG: Message for usahe of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION=Nie je možné vytvoriť perzistenciu zobrazenia. Používa vzdialenú tabuľku založenú na vzdialenom zdroji s povoleným šírením používateľov. Skontrolujte pôvod zobrazenia.
#XMSG: Message for dynamic usage of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION_DYNAMIC=Nie je možné vytvoriť perzistenciu zobrazenia. Používa vzdialenú tabuľku založenú na vzdialenom zdroji s povoleným šírením používateľov. Vzdialená tabuľka môže byť používaná dynamicky prostredníctvom zobrazenia skriptu SQL. Pôvod zobrazenia nemusí zobrazovať vzdialenú tabuľku.
#XMSG: Message for insufficient privileges or consumption of view with DAC via dynamic SQL
VIEW_PERSISTENCY_INSUF_PRIV_OR_DAC_DYN_SQL=Vaše oprávnenia môžu byť nedostatočné. Otvorte Náhľad údajov a zistite, či máte požadované oprávnenia. Ak áno, pre druhé zobrazenie použité prostredníctvom dynamického skriptu SQL sa môže použiť riadenie prístupu k údajom (DAC).
#XMSG: deployment of DAC is deprecated and view need to be redeployed to be able to persist data
VIEW_PERSISTENCY_DEPRECATED_DAC=Zobrazenie "{0}" je nasadené pomocou riadenia prístupu k údajom (DAC), ktoré bude zastarané. Nasaďte zobrazenie znova, aby ste mohli spracovať údaje pre zobrazenie.
##XFLD:Message used by SEAL repository for input parameter
START_VIEW_PERSISTENCY_INPUT_PARAMETER=Použitie predvolenej hodnoty "{0}" pre vstupný parameter "{1}".
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Elastic compute node keys~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
##XFLD:Message used to reflect disablement of ecn replica
ECN_DISABLE_EXISTING_REPLICA=Replika elastického výpočtového uzla je deaktivovaná.
##XFLD:Message used to reflect recreation of ecn replica
ECN_RECREATE_EXISTING_REPLICA=Replika elastického výpočtového uzla je znova vytvorená.
##XFLD:Message used to reflect re-enablement of ecn replica
ECN_ENABLE_EXISTING_REPLICA=Replika elastického výpočtového uzla je opäť aktivovaná.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Task Schedule Menu key~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message to schedule task
scheduleText=Naplánovať
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Vytvoriť plán
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Upraviť plán
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Odstrániť plán
#XFLD: Refresh frequency field
refreshFrequency=Frekvencia obnovenia
#XFLD: Refresh frequency field
refreshFrequencyNew=Frekvencia
#XFLD: Refresh frequency field
refreshFrequencyNewNew=Plánovaná frekvencia
#XBUT: label for None
none=Žiadne
#XBUT: label for Real-Time replication state
realtime=V reálnom čase
#XFLD: Label for table column
txtNextSchedule=Ďalší chod
#XFLD: Label for table column
txtNextScheduleNew=Plánovaný nasledujúci chod
#XFLD: Label for table column
txtNumOfRecords=Počet záznamov
#XFLD: Label for scheduled link
scheduledTxt=Naplánované
#XFLD: LABEL for partially persisted link
partiallyPersisted=Vytvorená čiastočná perzistencia
#XFLD: Text for paused text
paused=Pozastavené

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ View Monitor Tasklog Details Action keys~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message for release lock actionn
releaseLockMsg=Ak vykonanie chodu potrvá dlhšie ako obvykle, môže to znamenať, že zlyhalo a že status nebol primerane aktualizovaný. \r\n Ak chcete vyriešiť problm, uvoľnite blokovanie a nastavte status na Neúspešné.
#XFLD: Label for release lock dialog
releaseLockText=Uvoľniť blokovanie

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Column header tooltip ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: tooltip for table column
txtViewTooltip=Názov perzistentného zobrazenia
#XFLD: tooltip for table column
txtViewDataAccessTooltip=Označuje dostupnosť zobrazenia
#XFLD: tooltip for table column
txtViewRefreshFreqTooltip=Označuje, či je pre zobrazenie definovaný plán
#XFLD: tooltip for table column
txtViewStatusTooltip=Získať status perzistentného zobrazenia
#XFLD: tooltip for table column
txtViewLatestUpdateTooltip=Poskytuje informácie o čase poslednej aktualizácie perzistentného zobrazenia
#XFLD: tooltip for table column
txtViewNextRunTooltip=Ak je nastavený plán pre zobrazenie, pozrite sa, kedy je naplánovaný ďalší chod.
#XFLD: tooltip for table column
txtViewNumOfRecordsTooltip=Sledujte počet záznamov.
#XFLD: tooltip for table column
txtViewInMemorySizeTooltip=Sledovať, akú veľkosť v pamäti využíva zobrazenie
#XFLD: tooltip for table column
txtViewDiskSizeTooltip=Sledovať, akú veľkosť na disku zaberá zobrazenie
#XMSG: Expired text
txtExpired=Exspirované

#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Objekt „{0}“ nie je možné pridať do reťazca úloh.

#~~~~~~~~~~~View Analyzer task log messages~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message indicating number of records for a view, and memory consumption in Mebibyte during simulation of view persistency
RECORDS_PEAK_MEMORY=Zobrazenie ''{0}''‘ má {1} záznamov. Simulácia perzistencie údajov pre toto zobrazenie využila {2} MiB pamäte.
#XMSG: View Analyzer execution failure
VIEW_ANALYZER_EXECUTION_FAILED=Vykonanie analyzátora zobrazenia zlyhalo.
#XMSG: Missing authorizations for View Analyzer
VIEW_ANALYZER_MISSING_AUTH=Chýbajú oprávnenia pre analyzátor zobrazenia.
#XMSG: Maximum memory in Gibibyte is reached, therefore persistency simulation is stopped
MAXIMUM_MEMORY_REACHED=Bola dosiahnutá maximálna pamäť {0} GiB pri simulovaní perzistencie údajov pre zobrazenie ''{1}''. Preto nebudú spustené žiadne ďalšie simulácie perzistencie údajov.
#XMSG: Error during persistency simulation occurred
PERSISTENCY_SIMULATION_ERROR=Počas simulácie perzistencie údajov pre zobrazenie ''{0}'' sa vyskytla chyba.
#XMSG: Persistency simulation is not executed, because prerequisites are not fulfilled and the view cannot be persisted
PREREQUISITES_NOT_FULFILLED=Simulácia perzistencie údajov sa nevykoná pre zobrazenie ''{0}'', pretože nie sú splnené predpoklady a zobrazenie nie je možné zachovať.
#XMSG: Persistency simulation is not executed, because the view does not exist in the database
VIEW_DOES_NOT_EXIST=Ak chcete povoliť simuláciu perzistencie údajov, musíte nasadiť zobrazenie "{0}".
#XMSG: Number of records cannot be determined, because the local table does not exist in the database
TABLE_DOES_NOT_EXIST=Lokálna tabuľka "{0}" v databáze neexistuje, preto pre túto tabuľku nie je možné určiť počet záznamov.
##XMSG: Message start of cancellation for View Analyzer task
CANCEL_VIEW_ANALYZER_TRIGGERED=Proces pre zastavenie úlohy analyzátora zobrazenia {0} pre zobrazenie ''{1}'' bol odoslaný. Môže dôjsť k oneskoreniu kým sa úloha zastaví.
##XMSG: Message start to indicate that task is not active
CANCEL_VIEW_ANALYZER_NO_ACTIVE_TASK=Úloha analyzátora zobrazenia {0} pre zobrazenie ''{1}'' nie je aktívna.
#XMSG: Cancel View Analyzer task failure
CANCEL_VIEW_ANALYZER_FAILED=Nepodarilo sa zrušiť úlohu analyzátora zobrazenia.
##XMSG: Message used in View Analyzer execution task when task is canceled
VIEW_ANALYZER_CANCELED=Vykonanie analyzátora zobrazenia pre zobrazenie ''{0}'' bolo zastavené prostredníctvom úlohy zrušenia.
##XMSG: Message start of cancelation for Model Validation task
CANCEL_MODEL_VALIDATION_TRIGGERED=Proces pre zastavenie úlohy Overenie modelu {0} pre zobrazenie "{1}" bol odoslaný. Môže dôjsť k oneskoreniu kým sa úloha zastaví.
##XMSG: Message to indicate that task is not active
CANCEL_MODEL_VALIDATION_NO_ACTIVE_TASK=Úloha Overenie modelu {0} pre zobrazenie "{1}" nie je aktívna.
#XMSG: Cancel Model Validation task failure
CANCEL_MODEL_VALIDATION_FAILED=Nepodarilo sa zrušiť úlohu overenia modelu.
##XMSG: Message used in Model Validation task when task is canceled
MODEL_VALIDATION_CANCELED=Vykonanie overenia modelu pre zobrazenie "{0}" bolo zastavené prostredníctvom úlohy zrušenia.
#XMSG: Message for Locked space error, before starting Model Validation task
MODEL_VALIDATION_LOCKED_SPACE_ERROR=Nie je možné vykonať overenie modelu pre zobrazenie "{0}", pretože priestor "{1}" je blokovaný.
##XMSG: Message used in View Analyzer execution task when determination of number of rows fails
NUMBER_ROWS_DETERMINATION_FAILED=Vyskytla sa chyba pri určovaní počtu riadkov pre lokálnu tabuľku ''{0}''.
##XMSG: Message to indicate that SQL plan was created
EXECUTION_PLAN_CREATED=Súbor plánu SQL Analyzer pre zobrazenie ''{0}'' bol vytvorený a je možné ho prevziať.
#XFLD: Message to indicate starting of process to generate SQL Analyzer plan file
START_PLANVIZ_EXECUTION=Spúšťa sa proces generovania súboru plánu SQL Analyzer pre zobrazenie ''{0}''.
#XFLD: Message to indicate starting of View Analyzer process
VIEW_ANALYZER_EXECUTION_STARTED=Spúšťa sa vykonanie Analyzátora zobrazenia.
##XMSG: Message to indicate that SQL plan file cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_PLANVIZ_NOT_FULFILLED=Súbor plánu SQL Analyzer nemožno vygenerovať pre zobrazenie ''{0}'', pretože nie sú splnené predpoklady perzistencie údajov.
##XMSG: Error message during  generation of SQL Analyzer plan file
PLANVIZ_ERROR=Počas generovania súboru plánu SQL Analyzer pre zobrazenie ''{0}''‘ sa vyskytla chyba.
#XMSG: Message for Locked space error
VIEW_ANALYZER_LOCKED_SPACE_ERROR=Nie je možné spustiť analyzátor zobrazenia pre zobrazenie ''‘{0}'', pretože priestor ''‘{1}'' je blokovaný.
#XMSG: Message for persistency simulation without partitioning
PERSISTENCY_SIMULATION_WITHOUT_PARTITIONS=Segmenty zobrazenia ''{0}'' sa neberú do úvahy počas simulácie perzistencie zobrazenia údajov.
#XMSG: Message for generation of SQL Analyzer plan file without considering partitioning
PLANVIZ_WITHOUT_PARTITIONS=Segmenty zobrazenia ''{0}'' sa neberú do úvahy pri generovaní súboru plánu SQL Analyzer.
#XMSG: Confirmation message for mass removal of persisted data
MassRemovePersistency_Confirm_Msg=Chcete odstrániť perzistentné údaje a prepnúť prístup k údajom späť na virtuálny prístup?
#XMSG: Confirmation message for partial mass removal of selected persisted data
PartialMassRemovePersistency_Confirm_Msg={0} z {1} vybratých zobrazení má perzistentné údaje. \n Chcete odstrániť perzistentné údaje a prepnúť prístup k údajom späť na virtuálny prístup?
#XMSG: success message for stopping mass persistence
stopMassPersistenceSuccess=Odstraňujeme perzistentné údaje pre vybrané zobrazenia.
#XMSG: error message during mass stop persistence of a view (backend error)
STOP_MASS_PERSISTENCE_ERROR=Pri zastavení perzistencie pre vybrané zobrazenia sa vyskytla chyba.
#XMSG: memory analysis is only performed in space {0}
NO_MEMORY_ANALYSIS=Analýza pamäte sa vykonáva iba pre entity v priestore "{0}": "{1}" "{2}" bolo vynechané.
##XMSG: Message to indicate that Explain Plan cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_EXPLAINPLAN_NOT_FULFILLED=Súbor Explain Plan nie je možné vygenerovať pre zobrazenie "{0}", pretože nie sú splnené predpoklady perzistencie.
#XMSG: Message for Explain Plan generation without partitioning
EXPLAINPLAN_WITHOUT_PARTITIONS=Segmenty zobrazenia "{0}" sa neberú do úvahy pri generovaní súboru Explain Plan.
#XFLD: Message to indicate starting of process to generate Explain Plan file
START_EXPLAINPLAN_EXECUTION=Spúšťa sa proces generovania súboru Explain Plan file pre zobrazenie "{0}".
##XMSG: Message to indicate that Explain Plan was created
EXPLAINPLAN_CREATED=Súbor Explain Plan pre zobrazenie "{0}" bol vygenerovaný. Môžete si ho zobraziť kliknutím na „Zobraziť podrobnosti“ alebo ho prevziať, ak máte príslušné oprávnenie.
##XMSG: Error message during  generation of Explain Plan file
EXPLAINPLAN_ERROR=Počas generovania súboru Explain Plan pre zobrazenie "{0}" sa vyskytla chyba.
##XMSG: Explain Plan file cannot be generated for the view {0}, because too many views are stacked on each other
EXPLAINPLAN_MODEL_TOO_COMPLEX=Súbor Explain Plan nie je možné vygenerovať pre zobrazenie "{0}". Príliš veľa zobrazení je na sebe naukladaných. Komplexné modely môžu spôsobiť nedostatok pamäte a pomalý výkon. Odporúčame vytvoriť perzistenciu zobrazenia.

##XMSG: Message to indicate that the space is locked
PERFORMANCE_ANALYSIS_LOCKED_SPACE_ERROR=Nie je možné spustiť analýzu výkonu pre zobrazenie "{0}", pretože priestor "{1}" je blokovaný.
##XMSG: The performance analysis task was cancelled
PERFORMANCE_ANALYSIS_CANCELED=Analýza výkonu pre zobrazenie "{0}" bola zrušená.
##XMSG: The performance analysis failed
PERFORMANCE_ANALYSIS_FAILED=Analýza výkonu zlyhala.
##XMSG: The performance analysis is finished
PERFORMANCE_ANALYSIS_FINISHED=Analýza výkonu pre zobrazenie "{0}" bola dokončená. Výsledok zobrazíte kliknutím na „Zobraziť podrobnosti“.
##XMSG: The view has input parameter with no default value
INPUT_PARAMETER_WITHOUT_DEFAULT_VALUE=Toto zobrazenie nie je možné analyzovať, pretože obsahuje parameter bez predvolenej hodnoty.
##XMSG: The view is not fully deployed
VIEW_NOT_FULLY_DEPLOYED=Toto zobrazenie nie je možné analyzovať, pretože nie je plne nasadené.
##XMSG: The view uses limited remote adapters
LIMITED_REMOTE_ADAPTER=Toto zobrazenie používa aspoň jeden vzdialený adaptér s obmedzenými možnosťami, ako je chýbajúce posunutie filtra alebo podpora pre „Počet“. Perzistencia alebo replikovanie objektov môže zlepšiť výkon počas doby chodu.
##XMSG: The view uses remote adapter that does not support 'Limit'
REMOTE_ADAPTER_NOT_SUPPORTING_LIMIT=Toto zobrazenie používa aspoň jeden vzdialený adaptér, ktorý nepodporuje 'Limit'. Možno bolo vybratých viac ako 1000 záznamov.
##XMSG: Performance analysis is executed by using default parameters of view'
INPUT_PARAMETER_WITH_DEFAULT_VALUE=Analýza výkonu sa vykonáva pomocou predvolených hodnôt parametrov zobrazenia.
##XMSG: Message to indicate that an error occurred
PERFORMANCE_ANALYSIS_ERROR=Vyskytla sa chyba počas analýzy výkonnosti zobrazenia "{0}".
##XMSG: Message start of cancellation for Performance Analysis task
CANCEL_PERFORMANCE_ANALYSIS_TRIGGERED=Proces pre zastavenie úlohy Analýza výkonu {0} pre zobrazenie "{1}" bol odoslaný. Môže dôjsť k oneskoreniu kým sa úloha zastaví.
##XMSG: Message start to indicate that task is not active
CANCEL_PERFORMANCE_ANALYSIS_NO_ACTIVE_TASK=Úloha Analýza výkonu {0} pre zobrazenie "{1}" nie je aktívna.
#XMSG: Cancel Performance Analysis task failure
CANCEL_PERFORMANCE_ANALYSIS_FAILED=Úlohu analýzy výkonu sa nepodarilo zrušiť.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Priradiť plán mne
#XBUT: Pause schedule menu label
pauseScheduleLabel=Pozastaviť plán
#XBUT: Resume schedule menu label
resumeScheduleLabel=Obnoviť plán
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Pri odstraňovaní plánov sa vyskytla chyba.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Pri priraďovaní plánov sa vyskytla chyba.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Pri pozastavení plánov sa vyskytla chyba.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Pri obnovení plánov sa vyskytla chyba.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Odstraňuje sa {0} plánov
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Zmena vlastníka {0} plánov
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Pozastavenie {0} plánov
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Obnovenie {0} plánov
#XBUT: Select Columns Button
selectColumnsBtn=Vybrať stĺpce
#XFLD: Refresh tooltip
TEXT_REFRESH=Obnoviť
#XFLD: Select Columns tooltip
text_selectColumns=Vybrať stĺpce


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Runtime Metrics ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Label for Runtime Metrics
RuntimeMetricsTitle=Metrika doby chodu pre
#XFLD : Label for Run Button
runButton=Spustiť
#XFLD : Label for Cancel Button
cancelButton=Zrušiť
#XFLD : Label for Close Button
closeButton=Zavrieť
#XFLD : Label for Open View Analyzer Button
openViewAnalyzerButton=Otvoriť View Analyzer
#XFLD : Label for Generate Explain Plan Button
generateExplainPlanButton=Generovať plán Explain
#XFLD : Label for Previous Run Column
previousRun=Predchádzajúci chod
#XFLD : Label for Latest Run Column
latestRun=Najnovší chod
#XFLD : Label for time Column
time=Čas
#XFLD : Label for Duration Column
duration=Trvanie
#XFLD : Label for Peak Memory Column
peakMemory=Max.pamäť
#XFLD : Label for Number of Rows
numberOfRows=Počet riadkov
#XFLD : Label for Overall Number of Sources
overallNumberOfSources=Celkový počet zdrojov
#XFLD : Label for Data Access Column
dataAccess=Prístup k údajom
#XFLD : Label for Local Tables
localTables=Lokálne tabuľky
#XFLD : Label for federatedRemoteTables
federatedRemoteTables=Federované vzdialené tabuľky (s obmedzenými schopnosťami adaptéra)
#XTXT Text for initial state of the runtime metrics
initialState=Najprv musíte spustiť analýzu výkonu, aby ste vyvolali metriky. Môže to chvíľu trvať, ale v prípade potreby môžete proces zrušiť.
#XMSG: Cancel confirmation message
CancelRunConfirmationMessage=Naozaj chcete zrušiť aktulálny chod analýzy výkonu?
#XTIT: Cancel dialog title
CancelRunTitle=Zrušiť chod
#XFLD: Label for Number of Rows
NUMBER_ROWS=Počet riadkov
#XFLD: Label for Overall Number of Sources
OVERALL_NUMBER_SOURCES=Celkový počet zdrojov
#XFLD: Label for Data Access
DATA_ACCESS=Prístup k údajom
#XFLD: Label for Remote tables
NUMBER_REMOTE_TABLES_LIMITED_ADAPTERS=Federované vzdialené tabuľky (s obmedzenými schopnosťami adaptéra)
#XFLD: Label for select statement
SELECT_STATEMENT='SELECT * FROM VIEW LIMIT 1000'
#XFLD: Label for duration
SELECT_RUNTIME=Trvanie
#XFLD: Label for peak memory
SELECT_PEAK_MEMORY=Max.pamäť
#XFLD: Label for count statement
SELECT_COUNT_STATEMENT='SELECT COUNT(*) FROM VIEW'
#XFLD: Label for count duration
SELECT_COUNT_RUNTIME=Trvanie
#XFLD: Label for count peak memory
SELECT_COUNT_PEAK_MEMORY=Max.pamäť
#XFLD: Label for count of number of local tables
NUMBER_LOCAL_TABLES_FILE=Lokálne tabuľky (súbor)
#XTXT: Text for running state of the runtime metrics
Running=Spustené...
#XFLD: Label for time
Time=Čas
#XFLD: Label for virtual access
PA_VIRTUAL=Virtuálne
#XFLD: Label for persisted access
PA_PERSISTED=Perzistentné
PA_PARTIALLY_PERSISTED=Vytvorená čiastočná perzistencia
#XTXT: Text for cancel
CancelRunSuccessMessage=Zrušenie chodu analýzy výkonu.
#XTXT: Text for cancel error
CancelRunErrorMessage=Vyskytla sa chyba pri zrušení chodu analýzy výkonu.
#XTXT: Text for explain plan generation
ExplainPlanStarted=Generovanie Explain Plan pre zobrazenie "{0}".
#XTXT: Text for performance analysis
PerformanceAnalysisStarted=Spustenie analýzy výkonu pre zobrazenie "{0}".
#XTXT: Text for performance analysis error
ErrorFetchingPerformanceAnalysis=Vyskytla sa chyba pri vyvolaní dát analýzy výkonu.
#XTXT: Text for performance analysis error
conflictingTask=Úloha Analýza výkonu už prebieha
#XFLD: Label for Errors
Errors=Chyby
#XFLD: Label for Warnings
Warnings=Upozornenia
#XFLD: Tooltip for missing privileges
openViewAnalyzerButtonTooltipNoPrivilege=Na otvorenie nástroja View Analyzer potrebujete oprávnenie DWC_DATAINTEGRATION (aktualizácia).
#XFLD: Tooltip for missing privileges
generateExplainPlanButtonTooltipNoPrivilege=Na vygenerovanie plánu vysvetlenia potrebujete privilégium DWC_RUNTIME (čítanie).



#XFLD: Label for frequency column
everyLabel=Každých
#XFLD: Plural Recurrence text for Hour
hoursLabel=Hodiny
#XFLD: Plural Recurrence text for Day
daysLabel=Dni
#XFLD: Plural Recurrence text for Month
monthsLabel=Mesiace
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minúty
