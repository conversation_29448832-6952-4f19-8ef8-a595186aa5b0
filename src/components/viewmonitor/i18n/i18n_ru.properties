
#XBCB: Breadcrumb
viewMonitorBreadcrumb={spaceId}

#XFLD: Label for persisted view column
SOURCE=Источник
#XFLD: Label for persisted view column
NAME=Имя
#XFLD: Label for persisted view column
NAME_LABEL=Бизнес-имя
#XFLD: Label for persisted view column
NAME_LABELNew=Объект (бизнес-имя)
#XFLD: Label for persisted view column
TECHINCAL_NAME=Техническое имя
#XFLD: Label for persisted view column
TECHINCAL_NAMENew=Объект (техническое имя)
#XFLD: Label for persisted view column
DATA_PERSISTENCY=Доступ к данным
#XFLD: Label for persisted view column
STATUS=Статус
#XFLD: Label for persisted view column
LAST_UPDATED=Последнее обновление
#XFLD: Label for persisted view column
IN_MEMORY_SIZE=Использование памяти для данных (МиБ)
#XFLD: Label for persisted view column
DISK_SIZE=Использование диска для данных (МиБ)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_NEWW=Размер in-memory (МиБ)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_HEADER=Размер in-memory
#XFLD: Label for persisted view column
DISK_SIZE_NEWW=Размер на диске (МиБ)
#XFLD: Label for persisted view column
DISK_SIZE_HEADER=Размер на диске
#XFLD: Label for schedule owner column
txtScheduleOwner=Владелец планирования
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Показывает, кто создал планирование
#XFLD: Label for persisted view column
PERSISTED=Устойчиво сохранено
#XFLD: Label for persisted view column
TYPE=Тип
#XFLD: Label for View Selection Dialog column
changedOn=Дата изменения
#XFLD: Label for View Selection Dialog column
createdBy=Создал
#XFLD: Label for log details column
txtViewPersistencyLogs=Просмотреть журналы
#XFLD: Label for log details column
txtViewPersistencyLogsNew=Сведения
#XFLD: text for values shown for Ascending sort order
SortInAsc=Сортировать по восходящей
#XFLD: text for values shown for Descending sort order
SortInDesc=Сортировать по нисходящей
#XFLD: Label for persisted view monitor heading
ViewMonitor_Heading=Монитор ракурсов
#XFLD: Label for persisted view monitor sub-heading
ViewMonitor_SubHeading=Мониторинг и ведение устойчивости данных ракурсов


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ DynamicPageHeader ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: text for values shown in column Persistence Status
txtLoading=Загрузка
#XFLD: text for values shown in column Persistence Status
txtRunning=Выполняется
#XFLD: text for values shown in column Persistence Status
txtAvailable=Доступно
#XFLD: text for values shown in column Persistence Status
txtError=Ошибка
#XMSG: Error message for unknown Persistence type that gets logged in Kibana
unknownReplicationTypeError=Тип тиражирования "{0}" не поддерживается.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Content ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Настройки, использованные для последнего выполнения устойчивого сохранения данных:
#XMSG: Message for input parameter name
inputParameterLabel=Параметр ввода
#XMSG: Message for input parameter value
inputParameterValueLabel=Значение
#XMSG: Message for persisted data
inputParameterPersistedLabel=Устойчиво сохранено в
#XTIT: Count for View in ViewMonitor Toolbar
txtViewCount=Ракурсы ({0})
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Устойчивость ракурса
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Устойчивость данных
#XBUT: Button to clear the selected view persistance
ClearViewPersistance=Очистить
#XBUT: Button to stop the selected view persistance
stopPersistance=Остановить хранение
#XFLD: Placeholder for Search field
txtSearch=Поиск
#XBUT: Tooltip for refresh button
txtRefresh=Обновить
#XBUT: Tooltip for add view button
txtDeleteView=Удалить устойчивость
#XBUT: Tooltip for load new peristence
loadNewPersistence=Перезапустить устойчивость
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Загрузить новый мгновенный снимок
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Запустить устойчивое сохранение данных
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Удалить сохраненные данные
#XMSG: success message for starting persistence
startPersistenceSuccess=Устойчиво сохраняем ракурс "{0}".
#XMSG: success message for stopping persistence
stopPersistenceSuccess=Удаляем устойчивые данные ракурса "{0}".
#XMSG: success message for stopping persistence
deletePersistedViewSuccess=Удаляем ракурс "{0}" из списка мониторинга.
#XMSG: error message during start persistence of a view (backend error)
ERROR_STARTING_PERSISTENCE=Ошибка при запуске устойчивого сохранения данных ракурса "{0}".
#XMSG: error message during start persistence of a view (backend error)
PARAMERROR_STARTING_PERSISTENCE=Ракурс "{0}" недоступен для устойчивого сохранения, так как содержит параметры ввода.
#XMSG: error message during start persistence of a view (backend error)
NUMBER_OF_PARAMETER_STARTING_PERSISTENCE=Ракурс "{0}" невозможно сохранить, так как он имеет больше одного параметра ввода.
#XMSG: error message during start persistence of a view (backend error)
PARAMETER_DEFAULT_VALUE_STARTING_PERSISTENCE=Ракурс "{0}" невозможно сохранить, так как параметр ввода не имеет значения по умолчанию.
#XMSG: error message during start persistence of a view (backend error)
REDEPLOYMENT_DAC_REQUIRED=Для поддержки устойчивости данных необходимо повторно развернуть контроль доступа к данным (DAC) "{0}".
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_STARTING_PERSISTENCE=Ракурс "{0}" не может быть устойчивым, поскольку использует ракурс "{1}", который содержит контроль доступа к данным (DAC).
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_EXTERNAL_STARTING_PERSISTENCE=Невозможно устойчиво сохранить ракурс "{0}", поскольку он использует ракурс с контролем доступа к данным (DAC), который относится к другому пространству.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_STRUCTURE_STARTING_PERSISTENCE=Ракурс "{0}" невозможно устойчиво сохранить, так как структура одного или нескольких его элементов контроля доступа к данным (DAC) не поддерживает устойчивость данных.
#XMSG: error message during stop persistence of a view (backend error)
STOP_PERSISTENCE_ERROR=Произошла ошибка при остановке устойчивого сохранения ракурса "{0}".
#XMSG: error message during stop persistence of a view (backend error)
DELETE_PERSISTED_VIEW_ERROR=Произошла ошибка при удалении устойчиво сохраненного ракурса "{0}".
#XMSG: Warning message for removing the persisted data
RemovePersistency_Confirm_Msg=Удалить устойчивые данные и переключиться на виртуальный доступ к ракурсу "{0}"?
#XMSG: Warning message for delete the view persistence
DeletePersistency_Confirm_Msg=Удалить ракурс из списка мониторинга и удалить устойчивые данные ракурса "{0}"?
#XMSG: error message for reading data from backend
txtReadBackendError=Ошибка при чтении из бэкэнда.
#XFLD: Label for No Data Error
NoDataError=Ошибка
#XMSG: message for conflicting task
Task_Already_Running=Для ракурса "{0}" уже выполняется конфликтующая задача.

#XFLD: Error while accessing pertitioning for a view
missingPrivilegeInViewPersistency=Нет полномочий на выполнение разделения для ракурса "{0}"

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Show All Views~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Text for show all views button
allViewsText=Все ракурсы ({0})
#XBUT: Text for show scheduled views button
scheduledText=Запланировано ({0})
#XBUT: Text for show persisted views button
persistedText=Устойчиво ({0})
#XBUT: Text for start analyzer button
startAnalyzer=Запустить анализ ракурса
#XFLD: Message if repository is unavailable
repositoryErrorMsg=Репозитарий недоступен, и некоторые функции деактивированы.

#XFLD: Data Access - Virtual
Virtual=Виртуально
#XFLD: Data Access - Persisted
Persisted=Устойчиво сохранено

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Non-Persisted View Selection Dialog~~~~~~~~~~~~~~~~~~~~~~~

#XTIT: Title for Non-Persisted View selection dialog
select_source=Выбрать ракурс для сохранения

#XTIT: Placeholder for Non-Persisted View selection dialog search
searchView=Поиск ракурсов
#XTIT: No data in the list of non-persisted view
No_Data=Нет данных
#XBUT: Button to select non-persisted view
ok=ОК
#XBUT: Button to close the non-persisted views selection dialog
cancel=Отменить

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~View persistency log message key~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message for view persistency logs
START_STOP_PERSISTENCY=Запускаем задачу устойчивости данных ракурса для "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY=Сохраняем данные для ракурса ''{1}''.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY)
START_VIEW_PERSISTENCY_PROCESS=Запускаем процесс устойчивого сохранения данных для ракурса "{0}".
#XFLD: Message for view persistency manual partition load
START_VIEW_PERSISTENCY_PROCESS_MANUAL_PARTITIONS=Запускаем процесс устойчивого сохранения данных для ракурса "{0}" с выбранными ид. разделов: "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY=Удаляем устойчивые данные ракурса "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY)
REMOVE_VIEW_PERSISTENCY=Запускаем процесс удаления устойчивых данных для ракурса "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_SUCCESS=Данные для ракурса "{1}" устойчиво сохранены.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_SUCCESS)
START_VIEW_PERSISTENCY_PROCESS_SUCCESS=Данные для ракурса "{0}" устойчиво сохранены.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY_SUCCESS=Устойчивые данные удалены, и виртуальный доступ к данным восстановлен для ракурса "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_SUCCESS)
REMOVE_VIEW_PERSISTENCY_SUCCESS=Процесс удаления устойчивых данных для ракурса "{0}" завершен.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_FAILED=Не удалось устойчиво сохранить данные для ракурса "{1}".
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_FAILED)
START_VIEW_PERSISTENCY_PROCESS_FAILED=Не удалось устойчиво сохранить данные для ракурса "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.05)
STOP_VIEW_PERSISTENCY_FAILED=Невозможно удалить устойчивые данные для ракурса "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_FAILED)
REMOVE_VIEW_PERSISTENCY_FAILED=Невозможно удалить устойчивые данные для ракурса "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_SUCCESS="{3}" записи(ей) устойчиво сохранено для ракурса "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace START_PERSISTENCY_RECORD_COUNT_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS={0} записи(ей) вставлено в таблицу устойчивости данных для ракурса "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS_V2={0} записи(ей) вставлено в таблицу устойчивости данных для ракурса "{1}". Использовано памяти: {2} ГиБ.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_SUCCESS="{3}" устойчивых записи(ей) удалено для ракурса "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace STOP_PERSISTENCY_RECORD_COUNT_SUCCESS)
REMOVE_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=Устойчивые данные удалены, удалено устойчивых записей: "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_FAILED=Не удалось вызвать число записей для ракурса "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_FAILED=Не удалось вызвать число записей для ракурса "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Планирование успешно удалено для "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS)
REMOVE_VIEW_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Планирование удалено для ракурса "{0}".
#XFLD: Message for view persistency logs
STOP_PERSISTENCY_DELETE_SCHEDULE_FAILED=Не получилось удалить планирование для "{1}".
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_REDEPLOY=Невозможно устойчиво сохранить ракурс "{0}", поскольку он был изменен и развернут после запуска устойчивого сохранения. Повторите попытку или дождитесь следующего запланированного выполнения.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_DELETE=Невозможно устойчиво сохранить ракурс "{0}", поскольку он был удален после запуска устойчивого сохранения.
#XFLD: Message for successful data persistency of a single partition (for messages created before version 2022.03)
START_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} записи(ей) устойчиво сохранено в разделе для значений "{1}" <= {2} < "{3}".
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (new message to replace START_PERSISTENCY_SINGLE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} записи(ей) вставлено в раздел для значений "{1}" <= "{2}" < "{3}".
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS_V2={0} записи(ей) вставлено в раздел для значений "{1}" <= "{2}" < "{3}". Использованная память: {4} ГиБ.
#XFLD: Message for successful replication of a "others" partition (for messages created before version 2022.03)
START_PERSISTENCY_OTHERS_PARTITION_SUCCESS=В разделе "Прочее" устойчиво сохранено записей: {0}.
#XFLD: Message used by SEAL repository for successful replication of a "others" partition (new message to replace START_PERSISTENCY_OTHERS_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_OTHERS_PARTITION_SUCCESS=В раздел "Прочее" вставлено записей: {0}.
#XFLD: Message for view persistency record count of partitions (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={3} записи(ей) устойчиво сохранено для ракурса "{1}" в {4} разделах.
#XFLD: Message used by SEAL repository for view persistency record count of partitions (new message to replace START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={0} записи(ей) вставлено в таблицу устойчивости данных для ракурса "{1}" в {2} разделах.
#XFLD: Message used by SEAL repository for view persistency for partition wise refresh
START_VIEW_PERSISTENCY_RECORD_COUNT_REFRESHED_PARTITIONS_SUCCESS={0} записи(ей) вставлено в таблицу устойчивости данных для ракурса "{1}". Обновленные разделы: {2}; блокированные разделы: {3}; всего разделов: {4}
#XFLD: Message used by SEAL repository for view persistency for manual partition load
START_VIEW_PERSISTENCY_RECORD_COUNT_MANUAL_PARTITIONS_SUCCESS={0} записи(ей) вставлено в таблицу устойчивости данных для ракурса "{1}" в {2} выбранных разделах
#XFLD: Message used by SEAL repository for view persistency for locked partition data load
START_VIEW_PERSISTENCY_RECORD_COUNT_LOCKED_PARTITIONS_SUCCESS={0} записи(ей) вставлено в таблицу устойчивости данных для ракурса "{1}". Обновленные разделы: {2}; блокированные и не измененные разделы: {3}; всего разделов: {4}.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
START_PERSISTENCY_UNEXPECTED_ERROR=Произошла непредвиденная ошибка при устойчивом сохранении данных для ракурса "{0}".
#XMSG: Message for unexpected error (new message to replace START_PERSISTENCY_UNEXPECTED_ERROR)
START_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Произошла непредвиденная ошибка при устойчивом сохранении данных для ракурса "{0}".
#XMSG: Message for Locked space error
START_VIEW_PERSISTENCY_LOCKED_SPACE_ERROR=Невозможно устойчиво сохранить ракурс "{0}", так как пространство "{1}" блокировано.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
STOP_PERSISTENCY_UNEXPECTED_ERROR=Произошла непредвиденная ошибка при удалении устойчивых данных для ракурса "{0}".
#XMSG: Message for unexpected error (new message to replace STOP_PERSISTENCY_UNEXPECTED_ERROR)
REMOVE_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Произошла непредвиденная ошибка при удалении устойчивости ракурса "{0}".
#XMSG: Message for ivalidated view
VIEW_PERSISTENCY_ABORTED_DEFINITION_INVALID=Определение ракурса "{0}" стало недействительным, вероятно, в связи с изменением объекта, напрямую или косвенно потребляемого ракурсом. Попробуйте повторно развернуть ракурс для устранения проблемы или определить основную причину.
#XMSG: Message for persisted data removal (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT=Устойчивые данные удалены при развертывании ракурса "{0}".
#XMSG: Message for persisted data removal of a consuming view
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW=При развертывании потребляемого ракурса "{0}" устойчивые данные удалены.
#XMSG: Message for persisted data removal of a consuming view due to a dac change
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW_DAC_CHANGED=При развертывании потребляемого ракурса "{0}" устойчивые данные удалены, так как контроль доступа к данным изменен.
#XMSG: Message for persisted data removal (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DEPLOYMENT=При развертывании ракурса "{0}" устойчивые данные удалены.
#XMSG: Message for deleted view (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION=Устойчивость удалена при удалении ракурса "{0}".
#XMSG: Message for deleted view (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DELETION=Устойчивость удалена при удалении ракурса "{0}".
#XMSG Message for removal of persisted data, because prerequisites not fulfilled
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_PREREQUISITES_NOT_FULFILLED=Устойчивые данные удалены, так как предпосылки устойчивости данных больше не выполняются.
#XMSG: Message for inconsistent persistency
VIEW_PERSISTENCY_INCONSISTENT_PERSISTENCY=Устойчивость ракурса "{0}" стала противоречивой. Удалите сохраненные данные, чтобы решить проблему.
#XMSG: Message for prerequisites
START_PERSISTENCY_CHECK_PREREQUISITES=Проверка предпосылок для устойчивости ракурса "{0}".
#XMSG: Message for DAC deployed in deprecated way using table function and join (for messages created before 2025.05)
START_PERSISTENCY_DEPRECIATED_DAC=Ракурс "{0}" развернут с использованием устаревающего контроля доступа к данным (DAC). Разверните ракурс повторно для повышения производительности.
#XMSG: Message for DAC deployed in deprecated way using table function and join
START_PERSISTENCY_DEPRECATED_DAC=Ракурс "{0}" развернут с использованием устаревающего контроля доступа к данным (DAC). Разверните ракурс повторно для повышения производительности.
#XMSG: Message for roll back (for messages created before version 2022.03)
ROLLBACK_VIEW_PERSISTENCY=Произошла ошибка. Восстановлен прежний статус устойчивости для ракурса "{0}".
#XMSG: Message used by SEAL repository for rollback of running procedure to persist view (new message to replace ROLLBACK_VIEW_PERSISTENCY)
VIEW_PERSISTENCY_ROLLBACK=Произошла ошибка. Процесс устойчивого сохранения ракурса ''{0}'' остановлен, и изменения сброшены.
#XMSG: Message used by SEAL repository for rollback of running procedure to remove persisted data of view
REMOVE_VIEW_PERSISTENCY_ROLLBACK=Произошла ошибка. Процесс удаления устойчивых данных ракурса ''{0}'' остановлен, и изменения сброшены.
#XMSG: Message used by SEAL repository for persistency preparation
START_PERSISTENCY_PREPARATION=Подготовка к устойчивому сохранению данных.
#XMSG: Message used by SEAL repository for data insertion
START_INSERTING_PERSISTENCY_DATA=Вставка данных в таблицу устойчивости.
#XMSG: Message for null values - partitioning (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS={0} записи(ей) с нулевыми значениями вставлено в раздел "Прочее".
#XMSG: Message used by SEAL repository for others partition values - partitioning (new message to replace START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS={0} записи(ей) вставлено в раздел "Прочее" для значений "{2}" < "{1}" ИЛИ "{2}" >= "{3}".
#XMSG: Message used by SEAL repository for others partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS_V2={0} записи(ей) вставлено в раздел "Прочее" для значений ''{2}'' < ''{1}'' ИЛИ ''{2}'' >= ''{3}''. Использованная память: {4} ГиБ.
#XMSG: Message used by SEAL repository for null partition values - partitioning
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS={0} записи(ей) вставлено в раздел "Прочее" для нулевых значений "{1}".
#XMSG: Message used by SEAL repository for null partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS_V2={0} записи(ей) вставлено в раздел "Прочее" для нулевых значений "{1}". Использованная память: {2} ГиБ.
##XFLD:Message used by SEAL repository for remote statistics
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS=Загрузка данных включала дистанционных инструкций: {0}. Всего извлечено записей: {1}. Общая продолжительность: {2} сек.
##XFLD:Message used by SEAL repository for remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS_WITH_PARTITIONS=Загрузка данных включала разделов: {0}, дистанционных инструкций: {1}. Всего извлечено записей: {2}. Общая продолжительность: {3} сек.
##XFLD:Message used by SEAL repository with UI ID for instructions of remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_INSTRUCTION=Дистанционные инструкции, обработанные в прогоне, можно просмотреть, открыв монитор дистанционных запросов, в сведениях из сообщений по разделу.
##XFLD:Message for start reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS=Запускаем процесс для повторного использования существующих устойчивых данных ракурса {0} после развертывания.
##XFLD:Message used by SEAL repository for reattach view persitency
START_VIEW_PERSISTENCY_REATTACHMENT=Запуск для повторного использования существующих устойчивых данных.
##XFLD:Message used by SEAL repository for reattach view persitency
START_FINILIZING_VIEW_PERSISTENCY_REATTACHMENT=Повторное использование существующих устойчивых данных.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_COMPLETE=Процесс повторного использования существующих устойчивых данных завершен для ракурса {0}.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_FAILED=Не удалось повторно использовать существующие устойчивые данные ракурса {0} после развертывания.
#XMSG: Message used by SEAL repository for finalizing persistency
START_FINALIZING_PERSISTENCY=Завершение устойчивого сохранения.
#XMSG: Message used by SEAL repository for restoring virtual access for view
REMOVE_VIEW_PERSISTENCY_VIRTUAL_ACCESS_RESTORED=Для ракурса "{0}" восстановлен виртуальный доступ к данным.
#XMSG: Message used by SEAL repository for view that already has virtual access
REMOVE_VIEW_PERSISTENCY_ALREADY_VIRTUAL_ACCESS=Ракурс "{0}" уже имеет виртуальный доступ к данным. Устойчивые данные не удалены.
#XMSG: Message for removing view persistency
VIEW_PERSISTENCY_REMOVED_FROM_MONITOR=Из монитора ракурсов удален ракурс "{0}".
#XMSG: Message for view doesnt exist.
VIEW_PERSISTENCY_VIEW_NOT_EXISTING=Ракурс "{0}" не существует в базе данных или не развернут корректно и поэтому не может быть устойчиво сохранен. Попробуйте повторно развернуть ракурс, чтобы решить проблему, или определите причину.
#XMSG: Message for persistency not enabled.
VIEW_PERSISTENCY_NOT_ENABLED=Устойчивость данных не активирована. Чтобы активировать функцию, повторно выполните развертывание таблицы/ракурса в пространстве "{0}".
#XMSG: Message for unexpected error due to node crash
VIEW_PERSISTENCY_UNEXPECTED_ERROR_GENERAL=Последний прогон устойчивости ракурса был прерван из‑за технических ошибок.
#XFLD: Message for view persistency memory consumption
VIEW_PERSISTENCY_MEMORY_CONSUMPTION=Во время выполнения устойчивого сохранения ракурса используется {0} ГиБ пиковой нагрузки памяти.
#XFLD: Message for view persistency timeout
VIEW_PERSISTENCY_TIMEOUT=Для устойчивости ракурса {0} достигнут тайм-аут {1} ч.
#XFLD: Message for not started asynchronous view persistency
VIEW_PERSISTENCY_ASYNC_NOT_STARTED=Высокая системная нагрузка не позволила асинхронно выполнить устойчивость ракурса при запуске. Проверьте, выполняются ли параллельно несколько задач.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency (for messages created before version 2022.05)
START_VIEW_PERSISTENCY_EXECUTION_METHOD_DROP_AND_RENAME=Существующая устойчиво сохраненная таблица удалена и заменена новой устойчиво сохраненной таблицей.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_RECREATE_AND_DROP=Существующая устойчиво сохраненная таблица удалена и заменена новой устойчиво сохраненной таблицей.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_TRUNCATE_AND_COPY=В существующую устойчиво сохраненную таблицу добавлены новые данные.
##XFLD: Message for missing authorizations for view persistency
VIEW_PERSISTENCY_MISSING_AUTH=Нет полномочий на устойчивое сохранение данных.
##XFLD: Message start of persistency cancellation
VIEW_PERSISTENCY_TRIGGER_CANCEL=Начинаем отмену процесса устойчивого сохранения ракурса {0}.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_UNAVAILABLE_TASK_RUN=Не удалось отменить процесс устойчивого сохранения ракурса, так как нет текущей задачи устойчивости для ракурса {0}.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_NONDETERMINISTIC_TASK_RUN=Не удалось отменить процесс устойчивого сохранения ракурса, так как сейчас не выполняется задача устойчивости данных для ракурса {0}.
##XFLD: Message for canceling a non running persitency task
VIEW_PERSISTENCY_CANCEL_NON_RUNNING_SELECTED_TASK=Не удалось отменить процесс устойчивого сохранения ракурса {0}, так как выбранная задача устойчивости данных {1} не выполняется.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_UNREACHABLE_PERSISTENCY_RUN=Не удалось отменить процесс устойчивого сохранения ракурса, так как устойчивое сохранение данных для ракурса {0} еще не начато.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_COMPLETED_PERSISTENCY_RUN=Не удалось отменить процесс устойчивого сохранения ракурса {0}, так как он уже завершен.
##XFLD: Message for failed cancellation
VIEW_PERSISTENCY_ABORTED_CANCEL_EXECUTION=Не удалось отменить процесс устойчивого сохранения ракурса {0}.
##XFLD: Message for finalizing persistency cancellation
VIEW_PERSISTENCY_FINALIZE_CANCEL=Процесс остановки устойчивого сохранения данных ракурса {0} отправлен.
##XFLD: Message used in persist task when cancellation is caught
VIEW_PERSISTENCY_CANCELED=Процесс устойчивого сохранения ракурса {0} остановлен.
##XFLD: Message used in persist task when cancelation is caught and performed through a cancelation task
VIEW_PERSISTENCY_CANCELED_VIA_CANCEL_TASK=Процесс устойчивого сохранения ракурса {0} остановлен через задачу отмены {1}.
##XFLD: Message used in cancel task when the task is triggered by deployment
VIEW_PERSISTENCY_CANCEL_TRIGGERED_BY_DEPLOYMENT=Отмена процесса устойчивого сохранения ракурса при развертывании ракурса {0}.
##XFLD: Message used in case the cancelation is retriggered for a running task
VIEW_PERSISTENCY_CANCEL_ATTEMPT_RETRIGGER_CANCEL=Предыдущая задача отмены для устойчивого сохранения ракурса {0} уже отправлена.
##XFLD: Message used to express the delay that may occur before the persistency task is stopped
VIEW_PERSISTENCY_CANCEL_DELAY_STOPPING_PERSISTENCY=Возможна задержка до остановки задачи устойчивого сохранения данных для ракурса {0}.
##XFLD: Message used to reflect the task log identifier of the current running persistency task
VIEW_PERSISTENCY_CANCEL_CURRENT_RUNNING_TASK=Данные для ракурса {0} устойчиво сохраняются с задачей {1}.
##XFLD:Message used by SEAL repository for locked partitions and DACs
VIEW_PERSISTENCY_LOCKED_PARTITIONS_DACS=Предоставленные контролем доступа к данным полномочия могли измениться и не учитываются блокированными разделами. Разблокируйте разделы и загрузите новый мгновенный снимок для применения изменений.
##XFLD:Message used by SEAL repository for column structure change
VIEW_PERSISTENCY_COLUMN_STRUCTURE_CHANGE=Структура столбцов изменилась и больше не соответствует существующей таблице устойчивости. Удалите устойчивые данные и запустите новое устойчивое сохранение данных, чтобы обновить таблицу устойчивости.
##XFLD: Message for out of memory
VIEW_PERSISTENCY_HANA_OUT_OF_MEMORY=Задача не выполнена из-за нехватки памяти в базе данных SAP HANA.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_EXCEPTION_TRANSACTION_ROLLBACK=Задача не выполнена из-за внутренней особой ситуации в базе данных SAP HANA.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_SQL_EXECUTION_ISSUE=Задача не выполнена из-за внутренней проблемы с выполнением SQL в базе данных SAP HANA.
##XFLD: Message for HANA out of memory result
VIEW_PERSISTENCY_OUT_OF_MEMORY_RESULT_MESSAGE=Причина события нехватки памяти в HANA: {0}
##XFLD: Message for admission control rejection
VIEW_PERSISTENCY_HANA_ADMISSION_CTRL_REJECTION=Задача не выполнения из-за отклонения в контроле допуска SAP HANA.
##XFLD: Message for HANA back pressure / rate limit of HANA client
VIEW_PERSISTENCY_HANA_CLIENT_RATE_LIMIT_REJECTION=Задача не выполнена, так как слишком много активных соединений SAP HANA.
#XMSG: Message for invalid persisted table name
VIEW_PERSISTENCY_PERSISTED_TABLE_INVALID=Произошла ошибка, и устойчиво сохраненная таблица стала недействительной. Чтобы устранить проблему, удалите устойчиво сохраненные данные и снова устойчиво сохраните ракурс.
#XMSG: Message for usahe of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION=Ракурс недоступен для устойчивого сохранения. Он использует дистанционную таблицу на основе дистанционного источника с активированным распространением пользователей. Проверьте происхождение ракурса.
#XMSG: Message for dynamic usage of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION_DYNAMIC=Ракурс недоступен для устойчивого сохранения. Он использует дистанционную таблицу на основе дистанционного источника с активированным распространением пользователей. Эта дистанционная таблица может потребляться динамически через ракурс скрипта SQL. Происхождение ракурса может не показывать эту дистанционную таблицу.
#XMSG: Message for insufficient privileges or consumption of view with DAC via dynamic SQL
VIEW_PERSISTENCY_INSUF_PRIV_OR_DAC_DYN_SQL=Ваших привилегий может быть недостаточно. Откройте предварительный просмотр данных, чтобы узнать, есть ли у вас необходимые привилегии. Если это так, то ко второму ракурсу, используемому через динамический скрипт SQL, может быть применен контроль доступа к данным (DAC).
#XMSG: deployment of DAC is deprecated and view need to be redeployed to be able to persist data
VIEW_PERSISTENCY_DEPRECATED_DAC=Ракурс "{0}" развернут с использованием устаревающего контроля доступа к данным (DAC). Разверните ракурс повторно, чтобы можно было устойчиво сохранить данные для ракурса.
##XFLD:Message used by SEAL repository for input parameter
START_VIEW_PERSISTENCY_INPUT_PARAMETER=Использование значения по умолчанию "{0}" для параметра ввода "{1}".
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Elastic compute node keys~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
##XFLD:Message used to reflect disablement of ecn replica
ECN_DISABLE_EXISTING_REPLICA=Таблица тиражирования для эластичного узла вычислений деактивирована.
##XFLD:Message used to reflect recreation of ecn replica
ECN_RECREATE_EXISTING_REPLICA=Таблица тиражирования для эластичного узла вычислений создана повторно.
##XFLD:Message used to reflect re-enablement of ecn replica
ECN_ENABLE_EXISTING_REPLICA=Таблица тиражирования для эластичного узла вычислений снова активирована.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Task Schedule Menu key~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message to schedule task
scheduleText=Запланировать
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Создать планирование
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Редактировать планирование
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Удалить планирование
#XFLD: Refresh frequency field
refreshFrequency=Частота обновления
#XFLD: Refresh frequency field
refreshFrequencyNew=Частота
#XFLD: Refresh frequency field
refreshFrequencyNewNew=Плановая периодичность
#XBUT: label for None
none=Нет
#XBUT: label for Real-Time replication state
realtime=В реальном времени
#XFLD: Label for table column
txtNextSchedule=Следующий прогон
#XFLD: Label for table column
txtNextScheduleNew=Запланированный следующий прогон
#XFLD: Label for table column
txtNumOfRecords=Число записей
#XFLD: Label for scheduled link
scheduledTxt=Запланировано
#XFLD: LABEL for partially persisted link
partiallyPersisted=Частично устойчиво сохранено
#XFLD: Text for paused text
paused=Приостановлено

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ View Monitor Tasklog Details Action keys~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message for release lock actionn
releaseLockMsg=Если выполнение занимает больше времени, чем обычно, возможно, оно завершилось с ошибкой, поэтому статус не обновлен.\r\nДля решения этой проблемы нужно снять блокировку и установить статус "не выполнено" или "ошибка".
#XFLD: Label for release lock dialog
releaseLockText=Снять блокировку

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Column header tooltip ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: tooltip for table column
txtViewTooltip=Имя устойчивого ракурса
#XFLD: tooltip for table column
txtViewDataAccessTooltip=Указывает доступность ракурса
#XFLD: tooltip for table column
txtViewRefreshFreqTooltip=Указывает наличие расписания для ракурса
#XFLD: tooltip for table column
txtViewStatusTooltip=Вызвать статус устойчивого ракурса
#XFLD: tooltip for table column
txtViewLatestUpdateTooltip=Предоставляет информацию о последнем обновлении устойчивого ракурса
#XFLD: tooltip for table column
txtViewNextRunTooltip=Если для ракурса настроено планирование, посмотрите, когда запланирован следующий прогон.
#XFLD: tooltip for table column
txtViewNumOfRecordsTooltip=Отслеживать число записей.
#XFLD: tooltip for table column
txtViewInMemorySizeTooltip=Отслеживание использования памяти ракурсом
#XFLD: tooltip for table column
txtViewDiskSizeTooltip=Отслеживание использования места на диске ракурсом
#XMSG: Expired text
txtExpired=Истекло

#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Объект "{0}" невозможно добавить в цепочку задач.

#~~~~~~~~~~~View Analyzer task log messages~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message indicating number of records for a view, and memory consumption in Mebibyte during simulation of view persistency
RECORDS_PEAK_MEMORY=Ракурс "{0}" имеет записей: {1}. Моделирование устойчивого сохранения ракурса использовало {2} МиБ памяти.
#XMSG: View Analyzer execution failure
VIEW_ANALYZER_EXECUTION_FAILED=Ошибка выполнения анализа ракурса.
#XMSG: Missing authorizations for View Analyzer
VIEW_ANALYZER_MISSING_AUTH=Нет полномочий на анализ ракурса.
#XMSG: Maximum memory in Gibibyte is reached, therefore persistency simulation is stopped
MAXIMUM_MEMORY_REACHED=Максимум памяти в {0} ГиБ достигнут при моделировании устойчивого сохранения данных для ракурса "{1}". Поэтому другие моделирования устойчивого сохранения данных не будут выполнены.
#XMSG: Error during persistency simulation occurred
PERSISTENCY_SIMULATION_ERROR=Ошибка при моделировании устойчивого сохранения данных для ракурса "{0}".
#XMSG: Persistency simulation is not executed, because prerequisites are not fulfilled and the view cannot be persisted
PREREQUISITES_NOT_FULFILLED=Моделирование устойчивого сохранения данных не выполнено для ракурса "{0}", так как не выполнены предпосылки, и устойчивое сохранение ракурса невозможно.
#XMSG: Persistency simulation is not executed, because the view does not exist in the database
VIEW_DOES_NOT_EXIST=Чтобы активировать моделирование устойчивого сохранения данных, необходимо развернуть ракурс "{0}".
#XMSG: Number of records cannot be determined, because the local table does not exist in the database
TABLE_DOES_NOT_EXIST=Локальной таблицы "{0}" нет в базе данных, поэтому невозможно определить число записей для этой таблицы.
##XMSG: Message start of cancellation for View Analyzer task
CANCEL_VIEW_ANALYZER_TRIGGERED=Процесс для остановки задачи анализа ракурса {0} по ракурсу "{1}" отправлен. Задача может быть остановлена с задержкой.
##XMSG: Message start to indicate that task is not active
CANCEL_VIEW_ANALYZER_NO_ACTIVE_TASK=Задача анализа ракурса {0} по ракурсу "{1}" неактивна.
#XMSG: Cancel View Analyzer task failure
CANCEL_VIEW_ANALYZER_FAILED=Не удалось отменить задачу анализа ракурса.
##XMSG: Message used in View Analyzer execution task when task is canceled
VIEW_ANALYZER_CANCELED=Выполнение анализа ракурса для ракурса "{0}" остановлено через задачу отмены.
##XMSG: Message start of cancelation for Model Validation task
CANCEL_MODEL_VALIDATION_TRIGGERED=Процесс для остановки задачи проверки модели {0} по ракурсу "{1}" отправлен. Задача может быть остановлена с задержкой.
##XMSG: Message to indicate that task is not active
CANCEL_MODEL_VALIDATION_NO_ACTIVE_TASK=Задача проверки модели {0} для ракурса "{1}" неактивна.
#XMSG: Cancel Model Validation task failure
CANCEL_MODEL_VALIDATION_FAILED=Не удалось отменить задачу проверки модели.
##XMSG: Message used in Model Validation task when task is canceled
MODEL_VALIDATION_CANCELED=Выполнение проверки модели для ракурса "{0}" остановлено через задачу отмены.
#XMSG: Message for Locked space error, before starting Model Validation task
MODEL_VALIDATION_LOCKED_SPACE_ERROR=Невозможно выполнить проверку модели для ракурса "{0}", так как пространство "{1}" блокировано.
##XMSG: Message used in View Analyzer execution task when determination of number of rows fails
NUMBER_ROWS_DETERMINATION_FAILED=Ошибка при определении числа строк для локальной таблицы "{0}".
##XMSG: Message to indicate that SQL plan was created
EXECUTION_PLAN_CREATED=Файл плана анализа SQL для ракурса "{0}" создан и может быть выгружен.
#XFLD: Message to indicate starting of process to generate SQL Analyzer plan file
START_PLANVIZ_EXECUTION=Запускаем процесс генерации файла плана анализа SQL для ракурса "{0}".
#XFLD: Message to indicate starting of View Analyzer process
VIEW_ANALYZER_EXECUTION_STARTED=Запускаем выполнение анализа ракурса.
##XMSG: Message to indicate that SQL plan file cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_PLANVIZ_NOT_FULFILLED=Невозможно сгенерировать файл плана анализа SQL для ракурса "{0}", так как предпосылки для устойчивости данных не выполнены.
##XMSG: Error message during  generation of SQL Analyzer plan file
PLANVIZ_ERROR=Произошла ошибка при генерации файла плана анализа SQL для ракурса "{0}".
#XMSG: Message for Locked space error
VIEW_ANALYZER_LOCKED_SPACE_ERROR=Невозможно выполнить анализ ракурса для ракурса "{0}", так как пространство "{1}" блокировано.
#XMSG: Message for persistency simulation without partitioning
PERSISTENCY_SIMULATION_WITHOUT_PARTITIONS=Разделы ракурса "{0}" не учитываются при моделировании устойчивости ракурса.
#XMSG: Message for generation of SQL Analyzer plan file without considering partitioning
PLANVIZ_WITHOUT_PARTITIONS=Разделы ракурса "{0}" не учитываются при генерации файла плана анализа SQL.
#XMSG: Confirmation message for mass removal of persisted data
MassRemovePersistency_Confirm_Msg=Удалить устойчиво сохраненные данные и переключить доступ к данным обратно на виртуальный?
#XMSG: Confirmation message for partial mass removal of selected persisted data
PartialMassRemovePersistency_Confirm_Msg={0} из {1} выбранных ракурсов имеют устойчиво сохраненные данные. \n Удалить устойчиво сохраненные данные и переключить доступ к данным обратно на виртуальный?
#XMSG: success message for stopping mass persistence
stopMassPersistenceSuccess=Мы удаляем устойчиво сохраненные данные для выбранных ракурсов.
#XMSG: error message during mass stop persistence of a view (backend error)
STOP_MASS_PERSISTENCE_ERROR=Произошла ошибка при остановке устойчивого сохранения выбранных ракурсов.
#XMSG: memory analysis is only performed in space {0}
NO_MEMORY_ANALYSIS=Анализ памяти выполняется только для сущностей в пространстве "{0}": "{1}" "{2}" пропущено.
##XMSG: Message to indicate that Explain Plan cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_EXPLAINPLAN_NOT_FULFILLED=Невозможно сгенерировать файл Explain Plan для ракурса "{0}", так как предпосылки для устойчивости не выполнены.
#XMSG: Message for Explain Plan generation without partitioning
EXPLAINPLAN_WITHOUT_PARTITIONS=Разделы ракурса "{0}" не учитываются при генерации файла Explain Plan.
#XFLD: Message to indicate starting of process to generate Explain Plan file
START_EXPLAINPLAN_EXECUTION=Запуск процесса генерации файла Explain Plan для ракурса "{0}".
##XMSG: Message to indicate that Explain Plan was created
EXPLAINPLAN_CREATED=Файл Explain Plan для ракурса "{0}" сгенерирован. Его можно просмотреть, щелкнув "Просмотреть сведения", или выгрузить при наличии соответствующих полномочий.
##XMSG: Error message during  generation of Explain Plan file
EXPLAINPLAN_ERROR=При генерации файла Explain Plan для ракурса "{0}" произошла ошибка.
##XMSG: Explain Plan file cannot be generated for the view {0}, because too many views are stacked on each other
EXPLAINPLAN_MODEL_TOO_COMPLEX=Невозможно сгенерировать файл Explain Plan для ракурса "{0}". Наложение слишком большого числа ракурсов. Сложные модели могут вызывать ошибки нехватки памяти и снижать производительность. Рекомендуется устойчиво сохранить ракурс.

##XMSG: Message to indicate that the space is locked
PERFORMANCE_ANALYSIS_LOCKED_SPACE_ERROR=Невозможно выполнить анализ производительности для ракурса "{0}", так как пространство "{1}" заблокировано.
##XMSG: The performance analysis task was cancelled
PERFORMANCE_ANALYSIS_CANCELED=Анализ производительности для ракурса "{0}" отменен.
##XMSG: The performance analysis failed
PERFORMANCE_ANALYSIS_FAILED=Анализ производительности не удался.
##XMSG: The performance analysis is finished
PERFORMANCE_ANALYSIS_FINISHED=Анализ производительности для ракурса "{0}" завершен. Чтобы просмотреть результат, нажмите "Просмотреть сведения".
##XMSG: The view has input parameter with no default value
INPUT_PARAMETER_WITHOUT_DEFAULT_VALUE=Этот ракурс недоступен для анализа, так как имеет параметр без значения по умолчанию.
##XMSG: The view is not fully deployed
VIEW_NOT_FULLY_DEPLOYED=Этот ракурс недоступен для анализа, так как развернут не полностью.
##XMSG: The view uses limited remote adapters
LIMITED_REMOTE_ADAPTER=Этот ракурс использует минимум один дистанционный адаптер с ограниченными возможностями, такими как отсутствие передачи фильтров или поддержки "Числа". Устойчивое сохранение или тиражирование объектов может повысить производительность времени выполнения.
##XMSG: The view uses remote adapter that does not support 'Limit'
REMOTE_ADAPTER_NOT_SUPPORTING_LIMIT=Этот ракурс использует минимум один дистанционный адаптер, который не поддерживает "Лимит". Возможно, выбрано более 1000 записей.
##XMSG: Performance analysis is executed by using default parameters of view'
INPUT_PARAMETER_WITH_DEFAULT_VALUE=Анализ производительности выполнен с использованием значений по умолчанию параметров ракурса.
##XMSG: Message to indicate that an error occurred
PERFORMANCE_ANALYSIS_ERROR=Произошла ошибка при анализе производительности для ракурса "{0}".
##XMSG: Message start of cancellation for Performance Analysis task
CANCEL_PERFORMANCE_ANALYSIS_TRIGGERED=Процесс для остановки задачи анализа производительности {0} по ракурсу "{1}" отправлен. Задача может быть остановлена с задержкой.
##XMSG: Message start to indicate that task is not active
CANCEL_PERFORMANCE_ANALYSIS_NO_ACTIVE_TASK=Задача анализа производительности {0} по ракурсу "{1}" неактивна.
#XMSG: Cancel Performance Analysis task failure
CANCEL_PERFORMANCE_ANALYSIS_FAILED=Не удалось отменить задачу анализа производительности.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Присвоить планирование мне
#XBUT: Pause schedule menu label
pauseScheduleLabel=Приостановить планирование
#XBUT: Resume schedule menu label
resumeScheduleLabel=Возобновить планирование
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Произошла ошибка при удалении планирования.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Произошла ошибка при присвоении планирования.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Произошла ошибка при приостановке планирования.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Произошла ошибка при возобновлении планирования.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Удаляем планирования ({0})
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Изменяем владельца планирований {0}
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Приостанавливаем планирования {0}
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Возобновляем планирования {0}
#XBUT: Select Columns Button
selectColumnsBtn=Выбрать столбцы
#XFLD: Refresh tooltip
TEXT_REFRESH=Обновить
#XFLD: Select Columns tooltip
text_selectColumns=Выбрать столбцы


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Runtime Metrics ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Label for Runtime Metrics
RuntimeMetricsTitle=Метрики времени выполнения для
#XFLD : Label for Run Button
runButton=Выполнить
#XFLD : Label for Cancel Button
cancelButton=Отменить
#XFLD : Label for Close Button
closeButton=Закрыть
#XFLD : Label for Open View Analyzer Button
openViewAnalyzerButton=Открыть анализ ракурса
#XFLD : Label for Generate Explain Plan Button
generateExplainPlanButton=Сгенерировать план Explain
#XFLD : Label for Previous Run Column
previousRun=Предыдущий прогон
#XFLD : Label for Latest Run Column
latestRun=Последний прогон
#XFLD : Label for time Column
time=Время
#XFLD : Label for Duration Column
duration=Продолжительность
#XFLD : Label for Peak Memory Column
peakMemory=Пиковая память
#XFLD : Label for Number of Rows
numberOfRows=Число строк
#XFLD : Label for Overall Number of Sources
overallNumberOfSources=Общее число источников
#XFLD : Label for Data Access Column
dataAccess=Доступ к данным
#XFLD : Label for Local Tables
localTables=Локальные таблицы
#XFLD : Label for federatedRemoteTables
federatedRemoteTables=Федеративные дистанционные таблицы (с ограниченными возможностями адаптера)
#XTXT Text for initial state of the runtime metrics
initialState=Сначала необходимо выполнить анализ производительности для получения метрик. Это может занять некоторое время, но при необходимости процесс можно отменить.
#XMSG: Cancel confirmation message
CancelRunConfirmationMessage=Действительно отменить текущий прогон анализа производительности?
#XTIT: Cancel dialog title
CancelRunTitle=Отменить прогон
#XFLD: Label for Number of Rows
NUMBER_ROWS=Число строк
#XFLD: Label for Overall Number of Sources
OVERALL_NUMBER_SOURCES=Общее число источников
#XFLD: Label for Data Access
DATA_ACCESS=Доступ к данным
#XFLD: Label for Remote tables
NUMBER_REMOTE_TABLES_LIMITED_ADAPTERS=Федеративные дистанционные таблицы (с ограниченными возможностями адаптера)
#XFLD: Label for select statement
SELECT_STATEMENT='SELECT * FROM VIEW LIMIT 1000'
#XFLD: Label for duration
SELECT_RUNTIME=Продолжительность
#XFLD: Label for peak memory
SELECT_PEAK_MEMORY=Пиковая память
#XFLD: Label for count statement
SELECT_COUNT_STATEMENT='SELECT COUNT(*) FROM VIEW'
#XFLD: Label for count duration
SELECT_COUNT_RUNTIME=Продолжительность
#XFLD: Label for count peak memory
SELECT_COUNT_PEAK_MEMORY=Пиковая память
#XFLD: Label for count of number of local tables
NUMBER_LOCAL_TABLES_FILE=Локальные таблицы (файловые)
#XTXT: Text for running state of the runtime metrics
Running=Выполняется...
#XFLD: Label for time
Time=Время
#XFLD: Label for virtual access
PA_VIRTUAL=Виртуально
#XFLD: Label for persisted access
PA_PERSISTED=Устойчиво сохранено
PA_PARTIALLY_PERSISTED=Частично устойчиво сохранено
#XTXT: Text for cancel
CancelRunSuccessMessage=Отмена прогона анализа производительности.
#XTXT: Text for cancel error
CancelRunErrorMessage=Произошла ошибка при отмене прогона анализа производительности.
#XTXT: Text for explain plan generation
ExplainPlanStarted=Генерация плана Explain для ракурса "{0}".
#XTXT: Text for performance analysis
PerformanceAnalysisStarted=Запуск анализа производительности для ракурса "{0}".
#XTXT: Text for performance analysis error
ErrorFetchingPerformanceAnalysis=Произошла ошибка при вызове данных анализа производительности.
#XTXT: Text for performance analysis error
conflictingTask=Задача анализа производительности уже выполняется
#XFLD: Label for Errors
Errors=Ошибки
#XFLD: Label for Warnings
Warnings=Предупреждения
#XFLD: Tooltip for missing privileges
openViewAnalyzerButtonTooltipNoPrivilege=Вам необходима привилегия DWC_DATAINTEGRATION (обновление) для открытия анализа ракурса.
#XFLD: Tooltip for missing privileges
generateExplainPlanButtonTooltipNoPrivilege=Вам необходима привилегия DWC_RUNTIME (чтение) для генерации плана Explain.



#XFLD: Label for frequency column
everyLabel=Кажд.
#XFLD: Plural Recurrence text for Hour
hoursLabel=ч
#XFLD: Plural Recurrence text for Day
daysLabel=дн.
#XFLD: Plural Recurrence text for Month
monthsLabel=мес.
#XFLD: Plural Recurrence text for Minutes
minutesLabel=мин
