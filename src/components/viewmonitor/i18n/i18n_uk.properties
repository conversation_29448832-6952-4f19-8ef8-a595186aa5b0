
#XBCB: Breadcrumb
viewMonitorBreadcrumb={spaceId}

#XFLD: Label for persisted view column
SOURCE=Джерело
#XFLD: Label for persisted view column
NAME=Ім’я
#XFLD: Label for persisted view column
NAME_LABEL=Бізнес-ім'я
#XFLD: Label for persisted view column
NAME_LABELNew=Об'єкт (бізнес-ім'я)
#XFLD: Label for persisted view column
TECHINCAL_NAME=Технічне ім’я
#XFLD: Label for persisted view column
TECHINCAL_NAMENew=Об'єкт (технічне ім'я)
#XFLD: Label for persisted view column
DATA_PERSISTENCY=Доступ до даних
#XFLD: Label for persisted view column
STATUS=Статус
#XFLD: Label for persisted view column
LAST_UPDATED=Останнє оновлення
#XFLD: Label for persisted view column
IN_MEMORY_SIZE=Пам'ять, використана для сховища (МіБ)
#XFLD: Label for persisted view column
DISK_SIZE=Диск, використаний для сховища (МіБ)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_NEWW=Розмір у пам'яті (МіБ)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_HEADER=Розмір у пам'яті
#XFLD: Label for persisted view column
DISK_SIZE_NEWW=Розмір на диску (МіБ)
#XFLD: Label for persisted view column
DISK_SIZE_HEADER=Розмір на диску
#XFLD: Label for schedule owner column
txtScheduleOwner=Власник розкладу
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Показує, хто створив розклад
#XFLD: Label for persisted view column
PERSISTED=Постійно
#XFLD: Label for persisted view column
TYPE=Тип
#XFLD: Label for View Selection Dialog column
changedOn=Дата зміни
#XFLD: Label for View Selection Dialog column
createdBy=Автор
#XFLD: Label for log details column
txtViewPersistencyLogs=Журнали подань
#XFLD: Label for log details column
txtViewPersistencyLogsNew=Подробиці
#XFLD: text for values shown for Ascending sort order
SortInAsc=Сортувати за зростанням
#XFLD: text for values shown for Descending sort order
SortInDesc=Сортувати за спаданням
#XFLD: Label for persisted view monitor heading
ViewMonitor_Heading=Монітор подань
#XFLD: Label for persisted view monitor sub-heading
ViewMonitor_SubHeading=Контроль і ведення постійного відтворення даних подань


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ DynamicPageHeader ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: text for values shown in column Persistence Status
txtLoading=Завантаження
#XFLD: text for values shown in column Persistence Status
txtRunning=Виконується
#XFLD: text for values shown in column Persistence Status
txtAvailable=Доступно
#XFLD: text for values shown in column Persistence Status
txtError=Помилка
#XMSG: Error message for unknown Persistence type that gets logged in Kibana
unknownReplicationTypeError=Тип реплікації "{0}" не підтримується.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Content ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Настройки, що використовувалися для останнього прогону постійного відтворення даних:
#XMSG: Message for input parameter name
inputParameterLabel=Параметр введення
#XMSG: Message for input parameter value
inputParameterValueLabel=Значення
#XMSG: Message for persisted data
inputParameterPersistedLabel=Постійно відтворювалися
#XTIT: Count for View in ViewMonitor Toolbar
txtViewCount=Подання ({0})
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Переглянути постійне подання
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Постійне відтворення даних
#XBUT: Button to clear the selected view persistance
ClearViewPersistance=Очистити
#XBUT: Button to stop the selected view persistance
stopPersistance=Зупинити постійні
#XFLD: Placeholder for Search field
txtSearch=Пошук
#XBUT: Tooltip for refresh button
txtRefresh=Оновити
#XBUT: Tooltip for add view button
txtDeleteView=Видалити постійне відтворення
#XBUT: Tooltip for load new peristence
loadNewPersistence=Перезапустити постійне відтворення
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Завантажити новий знімок
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Почати постійне відтворення даних
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Вилучити постійні дані
#XMSG: success message for starting persistence
startPersistenceSuccess=Подання "{0}" постійно ввімкнене.
#XMSG: success message for stopping persistence
stopPersistenceSuccess=Постійні дані для подання "{0}" вилучаються.
#XMSG: success message for stopping persistence
deletePersistedViewSuccess=Подання "{0}" видаляється зі контрольного списку.
#XMSG: error message during start persistence of a view (backend error)
ERROR_STARTING_PERSISTENCE=Під час запуску постійного відтворення даних для подання "{0}" сталася помилка.
#XMSG: error message during start persistence of a view (backend error)
PARAMERROR_STARTING_PERSISTENCE=Подання "{0}" не може відтворюватися постійно, оскільки воно містить вхідні параметри.
#XMSG: error message during start persistence of a view (backend error)
NUMBER_OF_PARAMETER_STARTING_PERSISTENCE=Подання "{0}" не може відтворюватися постійно, оскільки воно має більше одного вхідного параметра.
#XMSG: error message during start persistence of a view (backend error)
PARAMETER_DEFAULT_VALUE_STARTING_PERSISTENCE=Подання "{0}" не може відтворюватися постійно, оскільки вхідний параметр не має усталеного значення.
#XMSG: error message during start persistence of a view (backend error)
REDEPLOYMENT_DAC_REQUIRED=Щоб забезпечити підтримку постійного відтворення даних, потрібно повторно розгорнути засіб контролю доступу до даних (DAC) "{0}".
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_STARTING_PERSISTENCE=Подання "{0}" не може бути постійним, оскільки воно використовує подання "{1}", що містить засіб контролю доступу до даних (DAC).
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_EXTERNAL_STARTING_PERSISTENCE=Подання "{0}" не може бути постійним, оскільки воно використовує подання із засобом контролю доступу до даних (DAC), яке належить до іншого простору.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_STRUCTURE_STARTING_PERSISTENCE=Подання "{0}" не може бути постійним, оскільки структура одного або кількох його засобів контролю доступу до даних (DAC) не підтримує постійне відтворення даних.
#XMSG: error message during stop persistence of a view (backend error)
STOP_PERSISTENCE_ERROR=Під час зупинки постійного відтворення для подання "{0}" сталася помилка.
#XMSG: error message during stop persistence of a view (backend error)
DELETE_PERSISTED_VIEW_ERROR=Під час видалення постійного подання "{0}" сталася помилка.
#XMSG: Warning message for removing the persisted data
RemovePersistency_Confirm_Msg=Справді видалити постійно відтворювані дані та перейти до віртуального доступу в поданні "{0}"?
#XMSG: Warning message for delete the view persistence
DeletePersistency_Confirm_Msg=Справді вилучити подання з контрольного списку та видалити постійно відтворювані дані подання "{0}"?
#XMSG: error message for reading data from backend
txtReadBackendError=Схоже, під час читання із бекенду сталася помилка.
#XFLD: Label for No Data Error
NoDataError=Помилка
#XMSG: message for conflicting task
Task_Already_Running=Конфліктне завдання вже виконується для подання "{0}".

#XFLD: Error while accessing pertitioning for a view
missingPrivilegeInViewPersistency=Недостатньо дозволів для виконання поділу подання "{0}"

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Show All Views~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Text for show all views button
allViewsText=Усі подання ({0})
#XBUT: Text for show scheduled views button
scheduledText=За розкладом ({0})
#XBUT: Text for show persisted views button
persistedText=Постійно відтворювані ({0})
#XBUT: Text for start analyzer button
startAnalyzer=Запустити Аналізатор подань
#XFLD: Message if repository is unavailable
repositoryErrorMsg=Репозиторій недоступний, і певні функції вимкнуто.

#XFLD: Data Access - Virtual
Virtual=Віртуально
#XFLD: Data Access - Persisted
Persisted=Постійно

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Non-Persisted View Selection Dialog~~~~~~~~~~~~~~~~~~~~~~~

#XTIT: Title for Non-Persisted View selection dialog
select_source=Виберіть подання для постійного відтворення

#XTIT: Placeholder for Non-Persisted View selection dialog search
searchView=Шукати подання
#XTIT: No data in the list of non-persisted view
No_Data=Немає даних
#XBUT: Button to select non-persisted view
ok=Ok
#XBUT: Button to close the non-persisted views selection dialog
cancel=Скасувати

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~View persistency log message key~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message for view persistency logs
START_STOP_PERSISTENCY=Розпочато виконання завдання постійного відтворення даних для "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY=Триває збереження даних для постійного подання "{1}".
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY)
START_VIEW_PERSISTENCY_PROCESS=Запущено процес постійного відтворення даних для подання "{0}".
#XFLD: Message for view persistency manual partition load
START_VIEW_PERSISTENCY_PROCESS_MANUAL_PARTITIONS=Розпочато процес постійного відтворення даних для постійного подання "{0}" з вибраними ідентифікаторами розділів: "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY=Вилучення постійно відтворюваних даних для подання "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY)
REMOVE_VIEW_PERSISTENCY=Запущено процес вилучення постійно відтворюваних даних для подання "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_SUCCESS=Дані постійно відтворюються для подання "{1}".
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_SUCCESS)
START_VIEW_PERSISTENCY_PROCESS_SUCCESS=Дані постійно відтворюються для подання "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY_SUCCESS=Постійно відтворювані дані вилучено, а віртуальний доступ до даних для подання "{1}" відновлено.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_SUCCESS)
REMOVE_VIEW_PERSISTENCY_SUCCESS=Процес вилучення постійно відтворюваних даних для подання "{0}" завершено.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_FAILED=Не вдалося постійно відтворювати дані для подання "{1}".
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_FAILED)
START_VIEW_PERSISTENCY_PROCESS_FAILED=Не вдалося постійно відтворювати дані для подання "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.05)
STOP_VIEW_PERSISTENCY_FAILED=Не вдалося вилучити постійно відтворювані дані для подання "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_FAILED)
REMOVE_VIEW_PERSISTENCY_FAILED=Не вдалося вилучити постійно відтворювані дані для подання "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_SUCCESS=Записи "{3}" постійно відтворюються для подання "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace START_PERSISTENCY_RECORD_COUNT_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=Записи "{0}" вставлено в таблицю постійного відтворення даних для подання "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS_V2=Записи "{0}" вставлено в таблицю постійного відтворення даних для подання "{1}". Використано пам''яті: {2} ГіБ.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_SUCCESS=Постійно відтворювані записи "{3}" вилучено для подання "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace STOP_PERSISTENCY_RECORD_COUNT_SUCCESS)
REMOVE_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=Постійно відтворювані дані вилучено, постійно відтворювані записи "{0}" видалено.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_FAILED=Не вдалось отримати recordCount для подання "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_FAILED=Не вдалось отримати recordCount для подання "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Розклад видалено для "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS)
REMOVE_VIEW_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Розклад видалено для подання "{0}".
#XFLD: Message for view persistency logs
STOP_PERSISTENCY_DELETE_SCHEDULE_FAILED=Не вдалося видалити розклад для "{1}".
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_REDEPLOY=Ми не можемо постійно відтворювати подання "{0}", оскільки його було змінено та розгорнуто після початку постійного відтворення. Спробуйте знову активувати постійне відтворення подання або зачекайте до наступного запланованого прогону.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_DELETE=Ми не можемо постійно відтворювати подання "{0}", оскільки його було видалено після початку постійного відтворення.
#XFLD: Message for successful data persistency of a single partition (for messages created before version 2022.03)
START_PERSISTENCY_SINGLE_PARTITION_SUCCESS=Записи ({0}) постійно відтворюються в розділі для значень "{1}" <= {2} < "{3}".
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (new message to replace START_PERSISTENCY_SINGLE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS=Записи ({0}) вставлено в розділі для значень "{1}" <= {2} < "{3}".
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS_V2=Записи ({0}) вставлено в розділі для значень "{1}" <= "{2}" < "{3}". Використано пам''яті: {4} ГіБ.
#XFLD: Message for successful replication of a "others" partition (for messages created before version 2022.03)
START_PERSISTENCY_OTHERS_PARTITION_SUCCESS=У розділі "Інше" постійно відтворюється стільки записів: {0}.
#XFLD: Message used by SEAL repository for successful replication of a "others" partition (new message to replace START_PERSISTENCY_OTHERS_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_OTHERS_PARTITION_SUCCESS=У розділі "Інше" вставлено стільки записів: {0}.
#XFLD: Message for view persistency record count of partitions (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS=Записи ({3}) постійно відтворюються для подання "{1}" в розділах ({4}).
#XFLD: Message used by SEAL repository for view persistency record count of partitions (new message to replace START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS=Записи ({0}) вставлено в таблицю постійного відтворення даних для подання "{1}" в розділах ({2}).
#XFLD: Message used by SEAL repository for view persistency for partition wise refresh
START_VIEW_PERSISTENCY_RECORD_COUNT_REFRESHED_PARTITIONS_SUCCESS=Записи ({0}) вставлено до таблиці постійного відтворення даних для подання "{1}". Оновлених розділів: {2}. Заблокованих розділів: {3}. Усього розділів: {4}.
#XFLD: Message used by SEAL repository for view persistency for manual partition load
START_VIEW_PERSISTENCY_RECORD_COUNT_MANUAL_PARTITIONS_SUCCESS=Записи ({0}) вставлено в таблицю постійного відтворення даних для подання "{1}" у вибраних розділах ({2}).
#XFLD: Message used by SEAL repository for view persistency for locked partition data load
START_VIEW_PERSISTENCY_RECORD_COUNT_LOCKED_PARTITIONS_SUCCESS=Записи ({0}) вставлено до таблиці постійного відтворення даних для подання "{1}". Оновлених розділів: {2}. Заблокованих і не змінених розділів: {3}. Усього розділів: {4}.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
START_PERSISTENCY_UNEXPECTED_ERROR=Під час постійного відтворення даних для подання "{0}" сталася неочікувана помилка.
#XMSG: Message for unexpected error (new message to replace START_PERSISTENCY_UNEXPECTED_ERROR)
START_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Під час постійного відтворення даних для подання "{0}" сталася неочікувана помилка.
#XMSG: Message for Locked space error
START_VIEW_PERSISTENCY_LOCKED_SPACE_ERROR=Подання "{0}" не може відтворюватися постійно, оскільки простір "{1}" заблоковано.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
STOP_PERSISTENCY_UNEXPECTED_ERROR=Під час вилучення постійно відтворюваних даних для подання "{0}" сталася неочікувана помилка.
#XMSG: Message for unexpected error (new message to replace STOP_PERSISTENCY_UNEXPECTED_ERROR)
REMOVE_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Під час вилучення постійного відтворення для подання "{0}" сталася неочікувана помилка.
#XMSG: Message for ivalidated view
VIEW_PERSISTENCY_ABORTED_DEFINITION_INVALID=Визначення подання "{0}" стало недійсним, швидше за все, через змінення об''єкта, спожитого прямо чи опосередковано цим поданням. Спробуйте повторно розгорнути подання, щоб вирішити проблему або визначити першопричину.
#XMSG: Message for persisted data removal (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT=Постійно відтворювані дані вилучено під час розгортання подання "{0}".
#XMSG: Message for persisted data removal of a consuming view
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW=Постійно відтворювані дані вилучено під час розгортання спожитого подання "{0}".
#XMSG: Message for persisted data removal of a consuming view due to a dac change
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW_DAC_CHANGED=Постійно відтворювані дані видаляються під час розгортання споживаного подання "{0}", оскільки його контроль доступу до даних змінено.
#XMSG: Message for persisted data removal (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DEPLOYMENT=Постійно відтворювані дані вилучено під час розгортання подання "{0}".
#XMSG: Message for deleted view (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION=Постійне відтворення вилучено під час видалення подання "{0}".
#XMSG: Message for deleted view (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DELETION=Постійне відтворення вилучено під час видалення подання "{0}".
#XMSG Message for removal of persisted data, because prerequisites not fulfilled
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_PREREQUISITES_NOT_FULFILLED=Постійно відтворювані дані вилучено, оскільки передумови для постійного відтворення даних більше не виконуються.
#XMSG: Message for inconsistent persistency
VIEW_PERSISTENCY_INCONSISTENT_PERSISTENCY=Постійне відтворення подання "{0}" стало суперечливим. Вилучіть постійно відтворювані дані, щоб вирішити проблему.
#XMSG: Message for prerequisites
START_PERSISTENCY_CHECK_PREREQUISITES=Перевірка передумов для постійного відтворення подання "{0}".
#XMSG: Message for DAC deployed in deprecated way using table function and join (for messages created before 2025.05)
START_PERSISTENCY_DEPRECIATED_DAC=Подання "{0}" розгорнуто з використанням виведеного з експлуатації засобу контролю доступу до даних (DAC). Розгорніть подання знову, щоб покращити продуктивність.
#XMSG: Message for DAC deployed in deprecated way using table function and join
START_PERSISTENCY_DEPRECATED_DAC=Подання "{0}" розгорнуто з використанням виведеного з експлуатації засобу контролю доступу до даних (DAC). Розгорніть подання знову, щоб покращити продуктивність.
#XMSG: Message for roll back (for messages created before version 2022.03)
ROLLBACK_VIEW_PERSISTENCY=Сталася помилка. Для подання "{0}" відновлено попередній стан постійного відтворення.
#XMSG: Message used by SEAL repository for rollback of running procedure to persist view (new message to replace ROLLBACK_VIEW_PERSISTENCY)
VIEW_PERSISTENCY_ROLLBACK=Сталася помилка. Процес постійного відтворення подання ''{0}'' зупинено, а всі зміни відкочено.
#XMSG: Message used by SEAL repository for rollback of running procedure to remove persisted data of view
REMOVE_VIEW_PERSISTENCY_ROLLBACK=Сталася помилка. Процес вилучення постійно відтворюваних даних подання ''''{0}'''' зупинено, а всі зміни відкочено.
#XMSG: Message used by SEAL repository for persistency preparation
START_PERSISTENCY_PREPARATION=Підготовка до збереження даних.
#XMSG: Message used by SEAL repository for data insertion
START_INSERTING_PERSISTENCY_DATA=Вставлення даних у таблицю постійного подання.
#XMSG: Message for null values - partitioning (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS=Записи зі значенням Null ({0}) вставлено в розділі "Інше".
#XMSG: Message used by SEAL repository for others partition values - partitioning (new message to replace START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS=Записи ({0}) вставлено в розділ "Інше" для значень "{2}" < "{1}" OR "{2}" >= "{3}".
#XMSG: Message used by SEAL repository for others partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS_V2=Записи ({0}) вставлено в розділ "Інше" для значень "{2}" < "{1}" OR "{2}" >= "{3}". Використано пам''яті: {4} ГіБ.
#XMSG: Message used by SEAL repository for null partition values - partitioning
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS=Записи ({0}) вставлено в розділі "Інше" для значень "{1}" IS NULL.
#XMSG: Message used by SEAL repository for null partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS_V2=Записи ({0}) вставлено в розділі "Інше" для значень "{1}" IS NULL. Використано пам''яті: {2} ГіБ.
##XFLD:Message used by SEAL repository for remote statistics
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS=Завантаження даних пов''язано з такою кількістю віддалених тверджень: {0}. Усього отримано записів: {1}. Загальна тривалість: {2} с.
##XFLD:Message used by SEAL repository for remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS_WITH_PARTITIONS=Завантаження даних пов''язано з використанням розділів ({0}) з такою кількістю віддалених тверджень: {1}. Усього отримано записів: {2}. Загальна тривалість: {3} с.
##XFLD:Message used by SEAL repository with UI ID for instructions of remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_INSTRUCTION=Щоб відобразити віддалені твердження, оброблені під час прогону, можна відкрити монітор віддалених запитів і переглянути подробиці в повідомленнях, що стосуються розділів.
##XFLD:Message for start reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS=Розпочинається процес повторного використання наявних постійних даних для подання ''{0}'' після розгортання.
##XFLD:Message used by SEAL repository for reattach view persitency
START_VIEW_PERSISTENCY_REATTACHMENT=Початок повторного використання наявних постійних даних.
##XFLD:Message used by SEAL repository for reattach view persitency
START_FINILIZING_VIEW_PERSISTENCY_REATTACHMENT=Повторне використання наявних постійних даних.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_COMPLETE=Процес повторного використання наявних постійних даних для подання ''{0}'' завершено.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_FAILED=Не вдалося повторно використати наявні постійні дані для подання ''{0}'' після розгортання.
#XMSG: Message used by SEAL repository for finalizing persistency
START_FINALIZING_PERSISTENCY=Завершення постійного відтворення.
#XMSG: Message used by SEAL repository for restoring virtual access for view
REMOVE_VIEW_PERSISTENCY_VIRTUAL_ACCESS_RESTORED=Віртуальний доступ до даних для подання "{0}" відновлено.
#XMSG: Message used by SEAL repository for view that already has virtual access
REMOVE_VIEW_PERSISTENCY_ALREADY_VIRTUAL_ACCESS=Подання "{0}" уже має віртуальний доступ до даних. Жодні постійно відтворювані дані не вилучено.
#XMSG: Message for removing view persistency
VIEW_PERSISTENCY_REMOVED_FROM_MONITOR=Подання ''{0}'' вилучено з монітора подань.
#XMSG: Message for view doesnt exist.
VIEW_PERSISTENCY_VIEW_NOT_EXISTING=Подання "{0}" не існує в базі даних або його неправильно розгорнуто, тому його не можна відтворювати постійно. Спробуйте повторно розгорнути подання, щоб усунути проблему або визначити її першопричину.
#XMSG: Message for persistency not enabled.
VIEW_PERSISTENCY_NOT_ENABLED=Постійне відтворення даних не активовано. Повторно розгорніть таблицю / подання в просторі "{0}", щоб активувати цю функцію.
#XMSG: Message for unexpected error due to node crash
VIEW_PERSISTENCY_UNEXPECTED_ERROR_GENERAL=Останній прогін постійного подання було перервано через технічні помилки.
#XFLD: Message for view persistency memory consumption
VIEW_PERSISTENCY_MEMORY_CONSUMPTION=Пікове використання пам’яті під час прогону постійного подання, ГіБ: {0}.
#XFLD: Message for view persistency timeout
VIEW_PERSISTENCY_TIMEOUT=Постійне відтворення подання {0} перевищує час очікування {1} год.
#XFLD: Message for not started asynchronous view persistency
VIEW_PERSISTENCY_ASYNC_NOT_STARTED=Високе навантаження на систему перешкоджало запуску асинхронного виконання постійного подання. Перевірте, чи не виконується забагато завдань паралельно.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency (for messages created before version 2022.05)
START_VIEW_PERSISTENCY_EXECUTION_METHOD_DROP_AND_RENAME=Існуючу таблицю постійного відтворення було видалено та замінено новою таблицею постійного відтворення.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_RECREATE_AND_DROP=Існуючу таблицю постійного відтворення було видалено та замінено новою таблицею постійного відтворення.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_TRUNCATE_AND_COPY=Наявну таблицю постійного відтворення оновлено новими даними.
##XFLD: Message for missing authorizations for view persistency
VIEW_PERSISTENCY_MISSING_AUTH=Бракує авторизації для постійного відтворення даних.
##XFLD: Message start of persistency cancellation
VIEW_PERSISTENCY_TRIGGER_CANCEL=Початок скасування процесу збереження подання {0}.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_UNAVAILABLE_TASK_RUN=Не вдалося скасувати процес постійного відтворення подання, оскільки для подання {0} немає виконуваного завдання постійного відтворення даних.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_NONDETERMINISTIC_TASK_RUN=Не вдалося скасувати процес постійного відтворення подання, оскільки для подання {0} не виконується завдання постійного відтворення даних.
##XFLD: Message for canceling a non running persitency task
VIEW_PERSISTENCY_CANCEL_NON_RUNNING_SELECTED_TASK=Не вдалося скасувати процес постійного відтворення подання {0}, оскільки не запущено вибране завдання постійного відтворення даних {1}.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_UNREACHABLE_PERSISTENCY_RUN=Не вдалося скасувати процес збереження подання, оскільки збереження даних для подання {0} ще не розпочато.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_COMPLETED_PERSISTENCY_RUN=Не вдалося скасувати процес збереження подання {0}, оскільки його уже завершено.
##XFLD: Message for failed cancellation
VIEW_PERSISTENCY_ABORTED_CANCEL_EXECUTION=Не вдалося скасувати процес, щоб зберегти подання {0}.
##XFLD: Message for finalizing persistency cancellation
VIEW_PERSISTENCY_FINALIZE_CANCEL=Процес постійного відтворення даних подання {0} зупинено.
##XFLD: Message used in persist task when cancellation is caught
VIEW_PERSISTENCY_CANCELED=Процес збереження подання {0} зупинено.
##XFLD: Message used in persist task when cancelation is caught and performed through a cancelation task
VIEW_PERSISTENCY_CANCELED_VIA_CANCEL_TASK=Процес постійного відтворення подання {0} зупинено за допомогою завдання скасування {1}.
##XFLD: Message used in cancel task when the task is triggered by deployment
VIEW_PERSISTENCY_CANCEL_TRIGGERED_BY_DEPLOYMENT=Скасування процесу, щоб зберегти дані під час розгортання подання {0}.
##XFLD: Message used in case the cancelation is retriggered for a running task
VIEW_PERSISTENCY_CANCEL_ATTEMPT_RETRIGGER_CANCEL=Попереднє завдання скасування для постійного відтворення подання {0} вже надіслано.
##XFLD: Message used to express the delay that may occur before the persistency task is stopped
VIEW_PERSISTENCY_CANCEL_DELAY_STOPPING_PERSISTENCY=Може виникнути затримка, поки завдання постійного відтворення даних для подання {0} не буде зупинено.
##XFLD: Message used to reflect the task log identifier of the current running persistency task
VIEW_PERSISTENCY_CANCEL_CURRENT_RUNNING_TASK=Дані для подання {0} зберігаються завданням {1}.
##XFLD:Message used by SEAL repository for locked partitions and DACs
VIEW_PERSISTENCY_LOCKED_PARTITIONS_DACS=Авторизації, надані керуванням доступом до даних (DAC), можна змінити і не їх враховуватимуть заблоковані розділи. Розблокуйте розділи та завантажте новий знімок, щоб застосувати зміни.
##XFLD:Message used by SEAL repository for column structure change
VIEW_PERSISTENCY_COLUMN_STRUCTURE_CHANGE=Структуру стовпчиків змінено, і тепер вона не відповідає наявній таблиці постійного відтворення. Щоб забезпечити оновлення таблиці постійного відтворення вилучіть постійно відтворювані дані та почніть нове постійне відтворення даних.
##XFLD: Message for out of memory
VIEW_PERSISTENCY_HANA_OUT_OF_MEMORY=Не вдалося виконати завдання через помилку нестачі пам'яті в базі даних SAP HANA.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_EXCEPTION_TRANSACTION_ROLLBACK=Не вдалося виконати завдання через внутрішній виняток у базі даних SAP HANA.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_SQL_EXECUTION_ISSUE=Не вдалося виконати завдання через внутрішню проблему виконання SQL у базі даних SAP HANA.
##XFLD: Message for HANA out of memory result
VIEW_PERSISTENCY_OUT_OF_MEMORY_RESULT_MESSAGE=Причина події нестачі пам''яті в HANA: {0}
##XFLD: Message for admission control rejection
VIEW_PERSISTENCY_HANA_ADMISSION_CTRL_REJECTION=Не вдалося виконати завдання через відхилення контролю допуску SAP HANA.
##XFLD: Message for HANA back pressure / rate limit of HANA client
VIEW_PERSISTENCY_HANA_CLIENT_RATE_LIMIT_REJECTION=Не вдалося виконати завдання через завелику кількість активних з'єднань SAP HANA.
#XMSG: Message for invalid persisted table name
VIEW_PERSISTENCY_PERSISTED_TABLE_INVALID=Сталася помилка, і постійно відтворювана таблиця стала недійсною. Щоб вирішити цю проблему, вилучіть постійно відтворювані дані й активуйте постійне відтворення подання знову.
#XMSG: Message for usahe of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION=Подання не можна відтворювати постійно. Воно використовує віддалену таблицю на основі віддаленого джерела з активованим розповсюдженням користувача. Перевірте походження подання.
#XMSG: Message for dynamic usage of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION_DYNAMIC=Подання не можна відтворювати постійно. Воно використовує віддалену таблицю на основі віддаленого джерела з активованим розповсюдженням користувача. Віддалена таблиці може споживатися динамічно за допомогою подання скрипта SQL. Віддалена таблиця може не відображатися в походженні.
#XMSG: Message for insufficient privileges or consumption of view with DAC via dynamic SQL
VIEW_PERSISTENCY_INSUF_PRIV_OR_DAC_DYN_SQL=Можливо, ваших привілеїв не достатньо. Відкрийте попередній перегляд даних, щоб побачити, чи маєте ви необхідні привілеї. Якщо так, можливо, до другого подання, що споживається за допомогою динамічного скрипта SQL застосовано засіб контролю доступу до даних (DAC).
#XMSG: deployment of DAC is deprecated and view need to be redeployed to be able to persist data
VIEW_PERSISTENCY_DEPRECATED_DAC=Подання "{0}" розгорнуто з використанням виведеного з експлуатації засобу контролю доступу до даних (DAC). Розгорніть подання знову, щоб мати змогу постійно відтворювати дані для подання.
##XFLD:Message used by SEAL repository for input parameter
START_VIEW_PERSISTENCY_INPUT_PARAMETER=Використовується усталене значення "{0}" для параметра введення "{1}".
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Elastic compute node keys~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
##XFLD:Message used to reflect disablement of ecn replica
ECN_DISABLE_EXISTING_REPLICA=Репліку обчислювального вузла Elastic вимкнуто.
##XFLD:Message used to reflect recreation of ecn replica
ECN_RECREATE_EXISTING_REPLICA=Репліку обчислювального вузла Elastic створено повторно.
##XFLD:Message used to reflect re-enablement of ecn replica
ECN_ENABLE_EXISTING_REPLICA=Репліку обчислювального вузла Elastic знову ввімкнуто.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Task Schedule Menu key~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message to schedule task
scheduleText=Розклад
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Створити розклад
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Змінити розклад
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Видалити розклад
#XFLD: Refresh frequency field
refreshFrequency=Частота оновлення
#XFLD: Refresh frequency field
refreshFrequencyNew=Частота
#XFLD: Refresh frequency field
refreshFrequencyNewNew=Запланована частота
#XBUT: label for None
none=Немає
#XBUT: label for Real-Time replication state
realtime=Реальний час
#XFLD: Label for table column
txtNextSchedule=Наступний запуск
#XFLD: Label for table column
txtNextScheduleNew=Наступний прогін за розкладом
#XFLD: Label for table column
txtNumOfRecords=Кількість записів
#XFLD: Label for scheduled link
scheduledTxt=Заплановано
#XFLD: LABEL for partially persisted link
partiallyPersisted=З частковим постійним відтворенням
#XFLD: Text for paused text
paused=Призупинено

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ View Monitor Tasklog Details Action keys~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message for release lock actionn
releaseLockMsg=Якщо запуск триває довше, ніж зазвичай, це може свідчити про те, що стався збій і його статус не оновлено відповідним чином. \r\n Щоб вирішити проблему, ви можете зняти блокування та змінити його статус на "Збій".
#XFLD: Label for release lock dialog
releaseLockText=Зняти блокування

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Column header tooltip ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: tooltip for table column
txtViewTooltip=Ім’я постійного подання
#XFLD: tooltip for table column
txtViewDataAccessTooltip=Вказує на доступність подання
#XFLD: tooltip for table column
txtViewRefreshFreqTooltip=Вказує, чи визначено розклад для подання
#XFLD: tooltip for table column
txtViewStatusTooltip=Отримує статус постійного подання
#XFLD: tooltip for table column
txtViewLatestUpdateTooltip=Надає інформацію про останнє оновлення постійного подання
#XFLD: tooltip for table column
txtViewNextRunTooltip=Якщо для подання встановлено розклад, подивіться, на коли заплановано наступний запуск.
#XFLD: tooltip for table column
txtViewNumOfRecordsTooltip=Номер відстеження записів.
#XFLD: tooltip for table column
txtViewInMemorySizeTooltip=Відстежує, яку кількість пам’яті споживає ваше подання
#XFLD: tooltip for table column
txtViewDiskSizeTooltip=Відстежує, яку кількість дискового простору споживає ваше подання
#XMSG: Expired text
txtExpired=Прострочено

#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Об''єкт "{0}" не можна додати до ланцюжка завдань.

#~~~~~~~~~~~View Analyzer task log messages~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message indicating number of records for a view, and memory consumption in Mebibyte during simulation of view persistency
RECORDS_PEAK_MEMORY=Подання "{0}" має записи ({1}). Для моделювання постійного відтворення даних для подання використано {2} МіБ пам''яті.
#XMSG: View Analyzer execution failure
VIEW_ANALYZER_EXECUTION_FAILED=Помилка виконання аналізатора подання.
#XMSG: Missing authorizations for View Analyzer
VIEW_ANALYZER_MISSING_AUTH=Відсутня авторизація для аналізатора подання.
#XMSG: Maximum memory in Gibibyte is reached, therefore persistency simulation is stopped
MAXIMUM_MEMORY_REACHED=Під час моделювання постійного відтворення даних для подання "{1}" досягнуто максимального споживання пам''яті {0} ГіБ. Тому інші моделювання постійного відтворення даних не проганятимуться.
#XMSG: Error during persistency simulation occurred
PERSISTENCY_SIMULATION_ERROR=Сталася помилка під час моделювання постійного відтворення даних для подання "{0}".
#XMSG: Persistency simulation is not executed, because prerequisites are not fulfilled and the view cannot be persisted
PREREQUISITES_NOT_FULFILLED=Моделювання постійного відтворення даних не виконується для подання "{0}", оскільки попередні умови не виконано, і подання постійно відтворювати не можна.
#XMSG: Persistency simulation is not executed, because the view does not exist in the database
VIEW_DOES_NOT_EXIST=Щоб активувати можливість моделювання постійного відтворення даних, слід розгорнути подання "{0}".
#XMSG: Number of records cannot be determined, because the local table does not exist in the database
TABLE_DOES_NOT_EXIST=Локальної таблиці "{0}" не існує в базі даних, тому для цієї таблиці не можна визначити кількість записів.
##XMSG: Message start of cancellation for View Analyzer task
CANCEL_VIEW_ANALYZER_TRIGGERED=Процес зупинки завдання Аналізатора подання {0} для подання "{1}" надіслано. Може виникнути певна затримка, поки завдання не буде зупинено.
##XMSG: Message start to indicate that task is not active
CANCEL_VIEW_ANALYZER_NO_ACTIVE_TASK=Завдання Аналізатора подання {0} для подання "{1}" неактивне.
#XMSG: Cancel View Analyzer task failure
CANCEL_VIEW_ANALYZER_FAILED=Не вдалося скасувати завдання Аналізатора подання.
##XMSG: Message used in View Analyzer execution task when task is canceled
VIEW_ANALYZER_CANCELED=Виконання Аналізатора подання для подання "{0}" зупинено за допомогою завдання скасування.
##XMSG: Message start of cancelation for Model Validation task
CANCEL_MODEL_VALIDATION_TRIGGERED=Процес зупинки завдання перевірки моделі {0} для подання "{1}" надіслано. Можливо, завдання буде зупинено з певною затримкою.
##XMSG: Message to indicate that task is not active
CANCEL_MODEL_VALIDATION_NO_ACTIVE_TASK=Завдання перевірки моделі "{0}" для подання "{1}" неактивне.
#XMSG: Cancel Model Validation task failure
CANCEL_MODEL_VALIDATION_FAILED=Не вдалося скасувати завдання перевірки моделі.
##XMSG: Message used in Model Validation task when task is canceled
MODEL_VALIDATION_CANCELED=Виконання перевірки моделі для подання "{0}" зупинено за допомогою завдання скасування.
#XMSG: Message for Locked space error, before starting Model Validation task
MODEL_VALIDATION_LOCKED_SPACE_ERROR=Не вдалося виконати перевірку моделі для подання "{0}", оскільки простір "{1}" заблоковано.
##XMSG: Message used in View Analyzer execution task when determination of number of rows fails
NUMBER_ROWS_DETERMINATION_FAILED=Під час визначення кількості рядків для локальної таблиці "{0}" сталася помилка.
##XMSG: Message to indicate that SQL plan was created
EXECUTION_PLAN_CREATED=Створено файл плану Аналізатора SQL для подання "{0}". Можете його завантажити.
#XFLD: Message to indicate starting of process to generate SQL Analyzer plan file
START_PLANVIZ_EXECUTION=Початок процесу генерування файлу плану Аналізатора SQL для подання "{0}".
#XFLD: Message to indicate starting of View Analyzer process
VIEW_ANALYZER_EXECUTION_STARTED=Запуск виконання Аналізатора подань.
##XMSG: Message to indicate that SQL plan file cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_PLANVIZ_NOT_FULFILLED=Не вдалося згенерувати файл плану Аналізатора SQL для подання "{0}", оскільки не виконано передумови для постійного відтворення даних.`
##XMSG: Error message during  generation of SQL Analyzer plan file
PLANVIZ_ERROR=Під час генерування файлу плану Аналізатора SQL для подання "{0}" сталася помилка.
#XMSG: Message for Locked space error
VIEW_ANALYZER_LOCKED_SPACE_ERROR=Не вдалося виконати Аналізатор подань "{0}", оскільки простір "{1}" заблоковано.
#XMSG: Message for persistency simulation without partitioning
PERSISTENCY_SIMULATION_WITHOUT_PARTITIONS=Під час моделювання постійного відтворення даних розділи подання "{0}" не враховуються.
#XMSG: Message for generation of SQL Analyzer plan file without considering partitioning
PLANVIZ_WITHOUT_PARTITIONS=Під час генерування файлу плану Аналізатора SQL розділи подання "{0}" не враховуються.
#XMSG: Confirmation message for mass removal of persisted data
MassRemovePersistency_Confirm_Msg=Справді вилучити постійні дані та повернутися до віртуального доступу до даних?
#XMSG: Confirmation message for partial mass removal of selected persisted data
PartialMassRemovePersistency_Confirm_Msg=Вибрані подання ({0} з {1}) отримали постійні дані. \nСправді вилучити постійні дані та повернутися до віртуального доступу до даних?
#XMSG: success message for stopping mass persistence
stopMassPersistenceSuccess=Триває вилучення постійних даних для вибраних подань.
#XMSG: error message during mass stop persistence of a view (backend error)
STOP_MASS_PERSISTENCE_ERROR=Під час зупинки постійного відтворення вибраних подань сталася помилка.
#XMSG: memory analysis is only performed in space {0}
NO_MEMORY_ANALYSIS=Аналіз пам''яті виконано тільки для сутностей у просторі "{0}": "{1}" "{2}" пропущено.
##XMSG: Message to indicate that Explain Plan cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_EXPLAINPLAN_NOT_FULFILLED=Не вдалося згенерувати файл плану пояснення для подання "{0}", оскільки не виконано передумови для постійного відтворення.
#XMSG: Message for Explain Plan generation without partitioning
EXPLAINPLAN_WITHOUT_PARTITIONS=Під час генерування файлу плану пояснення розділи подання "{0}" не враховуються.
#XFLD: Message to indicate starting of process to generate Explain Plan file
START_EXPLAINPLAN_EXECUTION=Початок процесу генерування файлу плану пояснення для подання "{0}".
##XMSG: Message to indicate that Explain Plan was created
EXPLAINPLAN_CREATED=Згенеровано файл плану пояснення для подання "{0}". Його можна відобразити, клацнувши "Переглянути подробиці", або завантажити за наявності релевантного дозволу.
##XMSG: Error message during  generation of Explain Plan file
EXPLAINPLAN_ERROR=Сталася помилка під час генерування файлу плану пояснення для подання "{0}".
##XMSG: Explain Plan file cannot be generated for the view {0}, because too many views are stacked on each other
EXPLAINPLAN_MODEL_TOO_COMPLEX=Для подання "{0}" не можна згенерувати файл плану пояснення. Забагато подань нагромаджено одне на одне. Складні моделі можуть спричинити помилки браку пам''яті та призвести до зменшення продуктивності. Радимо використовувати постійне відтворення подання.

##XMSG: Message to indicate that the space is locked
PERFORMANCE_ANALYSIS_LOCKED_SPACE_ERROR=Не вдалося виконати аналіз продуктивності для подання "{0}", оскільки простір "{1}" заблоковано.
##XMSG: The performance analysis task was cancelled
PERFORMANCE_ANALYSIS_CANCELED=Аналіз продуктивності для подання "{0}" скасовано.
##XMSG: The performance analysis failed
PERFORMANCE_ANALYSIS_FAILED=Не вдалося виконати аналіз продуктивності.
##XMSG: The performance analysis is finished
PERFORMANCE_ANALYSIS_FINISHED=Аналіз продуктивності для подання "{0}" завершено. Щоб відобразити результати, клацніть "Подробиці подання".
##XMSG: The view has input parameter with no default value
INPUT_PARAMETER_WITHOUT_DEFAULT_VALUE=Це подання не можна проаналізувати, оскільки воно має параметр без усталеного значення.
##XMSG: The view is not fully deployed
VIEW_NOT_FULLY_DEPLOYED=Це подання не можна проаналізувати, оскільки його не повністю розгорнуто.
##XMSG: The view uses limited remote adapters
LIMITED_REMOTE_ADAPTER=Це подання використовує принаймні оди віддалений адаптер з обмеженими можливостями, як от браком можливості примусового застосування фільтра або браком підтримки опції "Підрахунок". Постійне відтворення або реплікація об'єктів можуть покращити продуктивність часу виконання.
##XMSG: The view uses remote adapter that does not support 'Limit'
REMOTE_ADAPTER_NOT_SUPPORTING_LIMIT=Це подання використовує принаймні один віддалений адаптер, що не підтримує опцію "Ліміт". Можливо, вибрано понад 1000 записів.
##XMSG: Performance analysis is executed by using default parameters of view'
INPUT_PARAMETER_WITH_DEFAULT_VALUE=Аналіз продуктивності виконано з використанням усталених значень параметрів подання.
##XMSG: Message to indicate that an error occurred
PERFORMANCE_ANALYSIS_ERROR=Під час аналізу продуктивності для подання "{0}" сталася помилка.
##XMSG: Message start of cancellation for Performance Analysis task
CANCEL_PERFORMANCE_ANALYSIS_TRIGGERED=Надіслано запит на активацію процесу зупинки завдання аналізу продуктивності "{0}" для подання "{1}". Можливо, завдання буде зупинено з певною затримкою.
##XMSG: Message start to indicate that task is not active
CANCEL_PERFORMANCE_ANALYSIS_NO_ACTIVE_TASK=Завдання аналізу продуктивності "{0}" для подання "{1}" неактивне.
#XMSG: Cancel Performance Analysis task failure
CANCEL_PERFORMANCE_ANALYSIS_FAILED=Не вдалося скасувати завдання аналізу продуктивності.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Призначити мені розклад
#XBUT: Pause schedule menu label
pauseScheduleLabel=Призупинити розклад
#XBUT: Resume schedule menu label
resumeScheduleLabel=Відновити розклад
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Під час вилучення розкладів сталася помилка.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Під час призначення розкладів сталася помилка.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Під час призупинення розкладів сталася помилка.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Під час відновлення розкладів сталася помилка.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Видалення розкладів ({0})
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Змінення власника розкладів ({0})
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Призупинення розкладів ({0})
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Відновлення розкладів ({0})
#XBUT: Select Columns Button
selectColumnsBtn=Вибрати стовпчики
#XFLD: Refresh tooltip
TEXT_REFRESH=Оновити
#XFLD: Select Columns tooltip
text_selectColumns=Вибрати стовпчики


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Runtime Metrics ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Label for Runtime Metrics
RuntimeMetricsTitle=Метрики часу виконання для
#XFLD : Label for Run Button
runButton=Прогін
#XFLD : Label for Cancel Button
cancelButton=Скасувати
#XFLD : Label for Close Button
closeButton=Закрити
#XFLD : Label for Open View Analyzer Button
openViewAnalyzerButton=Відкрити Аналізатор подань
#XFLD : Label for Generate Explain Plan Button
generateExplainPlanButton=Згенерувати план пояснення
#XFLD : Label for Previous Run Column
previousRun=Попередній прогін
#XFLD : Label for Latest Run Column
latestRun=Останній прогін
#XFLD : Label for time Column
time=Час
#XFLD : Label for Duration Column
duration=Тривалість
#XFLD : Label for Peak Memory Column
peakMemory=Піковий обсяг пам'яті
#XFLD : Label for Number of Rows
numberOfRows=Кількість рядків
#XFLD : Label for Overall Number of Sources
overallNumberOfSources=Загальна кількість джерел
#XFLD : Label for Data Access Column
dataAccess=Доступ до даних
#XFLD : Label for Local Tables
localTables=Локальні таблиці
#XFLD : Label for federatedRemoteTables
federatedRemoteTables=Федеративні віддалені таблиці (з обмеженими можливостями адаптера)
#XTXT Text for initial state of the runtime metrics
initialState=Спочатку слід запустити аналіз продуктивності, щоб отримати метрики. На це може знадобитися якийсь час, але за необхідності процес можна скасувати.
#XMSG: Cancel confirmation message
CancelRunConfirmationMessage=Справді скасувати поточний прогін аналізу продуктивності?
#XTIT: Cancel dialog title
CancelRunTitle=Скасувати прогін
#XFLD: Label for Number of Rows
NUMBER_ROWS=Кількість рядків
#XFLD: Label for Overall Number of Sources
OVERALL_NUMBER_SOURCES=Загальна кількість джерел
#XFLD: Label for Data Access
DATA_ACCESS=Доступ до даних
#XFLD: Label for Remote tables
NUMBER_REMOTE_TABLES_LIMITED_ADAPTERS=Федеративні віддалені таблиці (з обмеженими можливостями адаптера)
#XFLD: Label for select statement
SELECT_STATEMENT='SELECT * FROM VIEW LIMIT 1000'
#XFLD: Label for duration
SELECT_RUNTIME=Тривалість
#XFLD: Label for peak memory
SELECT_PEAK_MEMORY=Піковий обсяг пам'яті
#XFLD: Label for count statement
SELECT_COUNT_STATEMENT='ВИБЕРІТЬ ПІДРАХУНОК(*) ІЗ ПОДАННЯ'
#XFLD: Label for count duration
SELECT_COUNT_RUNTIME=Тривалість
#XFLD: Label for count peak memory
SELECT_COUNT_PEAK_MEMORY=Піковий обсяг пам'яті
#XFLD: Label for count of number of local tables
NUMBER_LOCAL_TABLES_FILE=Локальні таблиці (файл)
#XTXT: Text for running state of the runtime metrics
Running=Виконується...
#XFLD: Label for time
Time=Час
#XFLD: Label for virtual access
PA_VIRTUAL=Віртуально
#XFLD: Label for persisted access
PA_PERSISTED=Постійно
PA_PARTIALLY_PERSISTED=З частковим постійним відтворенням
#XTXT: Text for cancel
CancelRunSuccessMessage=Скасування прогону аналізу продуктивності.
#XTXT: Text for cancel error
CancelRunErrorMessage=Сталася помилка під час скасування прогону аналізу продуктивності.
#XTXT: Text for explain plan generation
ExplainPlanStarted=Генерування плану пояснення для подання "{0}".
#XTXT: Text for performance analysis
PerformanceAnalysisStarted=Запуск аналізу продуктивності для подання "{0}".
#XTXT: Text for performance analysis error
ErrorFetchingPerformanceAnalysis=Сталася помилка під час отримання даних аналізу продуктивності.
#XTXT: Text for performance analysis error
conflictingTask=Завдання аналізу продуктивності вже виконується
#XFLD: Label for Errors
Errors=Помилки
#XFLD: Label for Warnings
Warnings=Застереження
#XFLD: Tooltip for missing privileges
openViewAnalyzerButtonTooltipNoPrivilege=Щоб відкрити аналізатор подань, потрібен привілей DWC_DATAINTEGRATION(update).
#XFLD: Tooltip for missing privileges
generateExplainPlanButtonTooltipNoPrivilege=Щоб згенерувати план пояснення, потрібен привілей DWC_RUNTIME(read).



#XFLD: Label for frequency column
everyLabel=Кожні
#XFLD: Plural Recurrence text for Hour
hoursLabel=Години
#XFLD: Plural Recurrence text for Day
daysLabel=Дні
#XFLD: Plural Recurrence text for Month
monthsLabel=Місяці
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Хвилини
