
#XBCB: Breadcrumb
viewMonitorBreadcrumb={spaceId}

#XFLD: Label for persisted view column
SOURCE=Źródło
#XFLD: Label for persisted view column
NAME=Nazwa
#XFLD: Label for persisted view column
NAME_LABEL=Nazwa biznesowa
#XFLD: Label for persisted view column
NAME_LABELNew=Obiekt (nazwa biznesowa)
#XFLD: Label for persisted view column
TECHINCAL_NAME=Nazwa techniczna
#XFLD: Label for persisted view column
TECHINCAL_NAMENew=Obiekt (nazwa techniczna)
#XFLD: Label for persisted view column
DATA_PERSISTENCY=Dost<PERSON>p do danych
#XFLD: Label for persisted view column
STATUS=Status
#XFLD: Label for persisted view column
LAST_UPDATED=Ostatnio zaktualizowane
#XFLD: Label for persisted view column
IN_MEMORY_SIZE=Pamięć wykorzystana do przechowywania (MiB)
#XFLD: Label for persisted view column
DISK_SIZE=Dysk wykorzystany do przechowywania (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_NEWW=Rozmiar pamięci in-memory (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_HEADER=Rozmiar pamięci in-memory
#XFLD: Label for persisted view column
DISK_SIZE_NEWW=Rozmiar dysku (MiB)
#XFLD: Label for persisted view column
DISK_SIZE_HEADER=Rozmiar dysku
#XFLD: Label for schedule owner column
txtScheduleOwner=Osoba odpowiedzialna za harmonogram
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Wyświetla, kto utworzył harmonogram
#XFLD: Label for persisted view column
PERSISTED=Utrwalone
#XFLD: Label for persisted view column
TYPE=Typ
#XFLD: Label for View Selection Dialog column
changedOn=Data zmiany
#XFLD: Label for View Selection Dialog column
createdBy=Autor
#XFLD: Label for log details column
txtViewPersistencyLogs=Wyświetl logi
#XFLD: Label for log details column
txtViewPersistencyLogsNew=Szczegóły
#XFLD: text for values shown for Ascending sort order
SortInAsc=Sortuj rosnąco
#XFLD: text for values shown for Descending sort order
SortInDesc=Sortuj malejąco
#XFLD: Label for persisted view monitor heading
ViewMonitor_Heading=Monitor widoków
#XFLD: Label for persisted view monitor sub-heading
ViewMonitor_SubHeading=Monitorowanie i opracowywanie utrwalania danych widoków


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ DynamicPageHeader ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: text for values shown in column Persistence Status
txtLoading=Wczytywanie
#XFLD: text for values shown in column Persistence Status
txtRunning=Aktywne
#XFLD: text for values shown in column Persistence Status
txtAvailable=Dostępne
#XFLD: text for values shown in column Persistence Status
txtError=Błąd
#XMSG: Error message for unknown Persistence type that gets logged in Kibana
unknownReplicationTypeError=Typ replikacji "{0}" nie jest obsługiwany.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Content ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Ustawienia używane dla ostatniego przebiegu utrwalania danych:
#XMSG: Message for input parameter name
inputParameterLabel=Parametr wejściowy
#XMSG: Message for input parameter value
inputParameterValueLabel=Wartość
#XMSG: Message for persisted data
inputParameterPersistedLabel=Czas utrwalenia
#XTIT: Count for View in ViewMonitor Toolbar
txtViewCount=Widoki ({0})
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Utrwalenie widoku
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Trwałość danych
#XBUT: Button to clear the selected view persistance
ClearViewPersistance=Wyczyść
#XBUT: Button to stop the selected view persistance
stopPersistance=Zatrzymaj utrwalenie
#XFLD: Placeholder for Search field
txtSearch=Wyszukaj
#XBUT: Tooltip for refresh button
txtRefresh=Odśwież
#XBUT: Tooltip for add view button
txtDeleteView=Usuń utrwalenie
#XBUT: Tooltip for load new peristence
loadNewPersistence=Ponownie uruchom utrwalanie
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Załaduj nową migawkę
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Rozpocznij utrwalanie danych
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Usuń utrwalone dane
#XMSG: success message for starting persistence
startPersistenceSuccess=Utrwalamy widok "{0}".
#XMSG: success message for stopping persistence
stopPersistenceSuccess=Usuwamy utrwalone dane dla widoku "{0}".
#XMSG: success message for stopping persistence
deletePersistedViewSuccess=Usuwamy widok "{0}" z listy kontroli.
#XMSG: error message during start persistence of a view (backend error)
ERROR_STARTING_PERSISTENCE=Wystąpił błąd podczas rozpoczynania utrwalania danych dla widoku "{0}".
#XMSG: error message during start persistence of a view (backend error)
PARAMERROR_STARTING_PERSISTENCE=Widoku "{0}" nie można utrwalić, ponieważ zawiera parametry wejściowe.
#XMSG: error message during start persistence of a view (backend error)
NUMBER_OF_PARAMETER_STARTING_PERSISTENCE=Widoku "{0}" nie można utrwalić, ponieważ ma więcej niż jeden parametr wejściowy.
#XMSG: error message during start persistence of a view (backend error)
PARAMETER_DEFAULT_VALUE_STARTING_PERSISTENCE=Widoku "{0}" nie można utrwalić, ponieważ parametr wejściowy nie ma wartości domyślnej.
#XMSG: error message during start persistence of a view (backend error)
REDEPLOYMENT_DAC_REQUIRED=Aby zapewnić obsługę utrwalania danych, należy ponownie wdrożyć kontrolę dostępu do danych (DAC) "{0}".
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_STARTING_PERSISTENCE=Nie można utrwalić widoku "{0}", ponieważ wykorzystuje on widok "{1}", który zawiera kontrolę dostępu do danych (DAC).
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_EXTERNAL_STARTING_PERSISTENCE=Nie można utrwalić widoku "{0}", ponieważ wykorzystuje widok z kontrolą dostępu do danych (DAC), który należy do innej przestrzeni.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_STRUCTURE_STARTING_PERSISTENCE=Nie można utrwalić widoku "{0}", ponieważ struktura co najmniej jednej kontroli dostępu do danych (DAC) nie obsługuje utrwalania danych.
#XMSG: error message during stop persistence of a view (backend error)
STOP_PERSISTENCE_ERROR=Wystąpił błąd podczas zatrzymania utrwalania dla widoku "{0}".
#XMSG: error message during stop persistence of a view (backend error)
DELETE_PERSISTED_VIEW_ERROR=Wystąpił błąd podczas usuwania utrwalonego widoku "{0}".
#XMSG: Warning message for removing the persisted data
RemovePersistency_Confirm_Msg=Czy chcesz usunąć utrwalone dane i przełączyć na dostęp wirtualny widoku "{0}"?
#XMSG: Warning message for delete the view persistence
DeletePersistency_Confirm_Msg=Czy chcesz usunąć widok z listy kontroli i usunąć utrwalone dane widoku "{0}"?
#XMSG: error message for reading data from backend
txtReadBackendError=Wygląda na to, że wystąpił błąd podczas odczytu z backend.
#XFLD: Label for No Data Error
NoDataError=Błąd
#XMSG: message for conflicting task
Task_Already_Running=Niezgodne zadanie jest już uruchomione dla widoku "{0}".

#XFLD: Error while accessing pertitioning for a view
missingPrivilegeInViewPersistency=Niewystarczające uprawnienie do wykonania partycjonowania widoku "{0}"

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Show All Views~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Text for show all views button
allViewsText=Wszystkie widoki ({0})
#XBUT: Text for show scheduled views button
scheduledText=Zaplanowane ('{{0}}')
#XBUT: Text for show persisted views button
persistedText=Utrwalone ('{{0}}')
#XBUT: Text for start analyzer button
startAnalyzer=Uruchom analizator widoków
#XFLD: Message if repository is unavailable
repositoryErrorMsg=To repozytorium nie jest dostępne i niektóre funkcje są wyłączone.

#XFLD: Data Access - Virtual
Virtual=Wirtualne
#XFLD: Data Access - Persisted
Persisted=Utrwalone

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Non-Persisted View Selection Dialog~~~~~~~~~~~~~~~~~~~~~~~

#XTIT: Title for Non-Persisted View selection dialog
select_source=Wybierz widok do utrwalenia

#XTIT: Placeholder for Non-Persisted View selection dialog search
searchView=Wyszukaj widok
#XTIT: No data in the list of non-persisted view
No_Data=Brak danych
#XBUT: Button to select non-persisted view
ok=Ok
#XBUT: Button to close the non-persisted views selection dialog
cancel=Anuluj

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~View persistency log message key~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message for view persistency logs
START_STOP_PERSISTENCY=Rozpoczęcie przebiegu zadania utrwalania danych dla "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY=Utrwalanie danych dla widoku "{1}".
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY)
START_VIEW_PERSISTENCY_PROCESS=Rozpoczynanie procesu utrwalania danych dla widoku "{0}".
#XFLD: Message for view persistency manual partition load
START_VIEW_PERSISTENCY_PROCESS_MANUAL_PARTITIONS=Rozpoczynanie procesu utrwalania danych dla widoku "{0}" z wybranymi ID partycji: "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY=Usuwanie utrwalonych danych dla widoku "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY)
REMOVE_VIEW_PERSISTENCY=Rozpoczynanie procesu usuwania utrwalonych danych dla widoku "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_SUCCESS=Utrwalono dane dla widoku "{1}".
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_SUCCESS)
START_VIEW_PERSISTENCY_PROCESS_SUCCESS=Utrwalono dane dla widoku "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY_SUCCESS=Usunięto utrwalone dane i przywrócono wirtualny dostęp do danych dla widoku "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_SUCCESS)
REMOVE_VIEW_PERSISTENCY_SUCCESS=Zakończono proces usuwania utrwalonych danych dla widoku "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_FAILED=Nie można utrwalić danych dla widoku "{1}".
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_FAILED)
START_VIEW_PERSISTENCY_PROCESS_FAILED=Nie można utrwalić danych dla widoku "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.05)
STOP_VIEW_PERSISTENCY_FAILED=Nie można usunąć utrwalonych danych dla widoku "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_FAILED)
REMOVE_VIEW_PERSISTENCY_FAILED=Nie można usunąć utrwalonych danych dla widoku "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_SUCCESS=Dokonano utrwalenia "{3}" rekordów dla widoku "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace START_PERSISTENCY_RECORD_COUNT_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=Do tabeli utrwalania danych dla widoku "{1}" wstawiono następującą liczbę rekordów: {0}.
#XFLD: Message used by SEAL repository for view persistency logs (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS_V2=Do tabeli utrwalenia danych dla widoku "{1}" wstawiono następującą liczbę rekordów: {0}. Wykorzystana pamięć: {2} GiB.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_SUCCESS=Dokonano usunięcia następującej liczby utrwalonych rekordów dla widoku "{1}": {3}.
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace STOP_PERSISTENCY_RECORD_COUNT_SUCCESS)
REMOVE_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=Usunięto utrwalone dane. Usunięto następującą liczbę utrwalonych rekordów: "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_FAILED=Pobranie recordCount dla widoku "{1}" nie powiodło się.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_FAILED=Pobranie recordCount dla widoku "{1}" nie powiodło się.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Pomyślnie usunięto harmonogram dla "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS)
REMOVE_VIEW_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Pomyślnie usunięto harmonogram dla widoku "{0}".
#XFLD: Message for view persistency logs
STOP_PERSISTENCY_DELETE_SCHEDULE_FAILED=Usuwanie harmonogramu dla "{1}" nie powiodło się.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_REDEPLOY=Nie można trwale zapamiętać widoku "{0}", ponieważ został zmieniony i wdrożony od momentu rozpoczęcia zapamiętywania. Spróbuj ponownie trwale zapamiętać widok lub poczekaj na kolejny zaplanowany przebieg.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_DELETE=Nie można trwale zapamiętać widoku "{0}", ponieważ został usunięty od momentu rozpoczęcia zapamiętywania.
#XFLD: Message for successful data persistency of a single partition (for messages created before version 2022.03)
START_PERSISTENCY_SINGLE_PARTITION_SUCCESS=Utrwalono {0} rek. w partycji dla wartości "{1}" <= {2} < "{3}".
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (new message to replace START_PERSISTENCY_SINGLE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS=Wstawiono {0} rek. w partycji dla wartości "{1}" <= "{2}" < "{3}".
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS_V2=Wstawiono {0} rek. w partycji dla wartości "{1}" <= "{2}" < "{3}". Wykorzystana pamięć: {4} GiB.
#XFLD: Message for successful replication of a "others" partition (for messages created before version 2022.03)
START_PERSISTENCY_OTHERS_PARTITION_SUCCESS=Utrwalono {0} rek. w partycji "inne".
#XFLD: Message used by SEAL repository for successful replication of a "others" partition (new message to replace START_PERSISTENCY_OTHERS_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_OTHERS_PARTITION_SUCCESS=Wstawiono {0} rek. w partycji "inne".
#XFLD: Message for view persistency record count of partitions (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS=Utrwalono {3} rek. dla widoku "{1}" w {4} partycjach.
#XFLD: Message used by SEAL repository for view persistency record count of partitions (new message to replace START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS=Wstawiono {0} rek. do tabeli utrwalania danych dla widoku "{1}" w {2} partycjach.
#XFLD: Message used by SEAL repository for view persistency for partition wise refresh
START_VIEW_PERSISTENCY_RECORD_COUNT_REFRESHED_PARTITIONS_SUCCESS=Wstawiono {0} rek. do tabeli utrwalania danych dla widoku "{1}". Zaktualizowane partycje: {2}. Zablokowane partycje: {3}. Suma partycji: {4}
#XFLD: Message used by SEAL repository for view persistency for manual partition load
START_VIEW_PERSISTENCY_RECORD_COUNT_MANUAL_PARTITIONS_SUCCESS=Wstawiono {0} rek. do tabeli utrwalania danych dla widoku "{1}" w {2} wybranych partycjach
#XFLD: Message used by SEAL repository for view persistency for locked partition data load
START_VIEW_PERSISTENCY_RECORD_COUNT_LOCKED_PARTITIONS_SUCCESS=Wstawiono {0} rek. do tabeli utrwalania danych dla widoku "{1}". Zaktualizowane partycje: {2}. Zablokowane, niezmienione partycje: {3}. Suma partycji: {4}
#XMSG: Message for unexpected error (for messages created before version 2022.03)
START_PERSISTENCY_UNEXPECTED_ERROR=Wystąpił nieoczekiwany błąd podczas utrwalania danych dla widoku "{0}".
#XMSG: Message for unexpected error (new message to replace START_PERSISTENCY_UNEXPECTED_ERROR)
START_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Wystąpił nieoczekiwany błąd podczas utrwalania danych dla widoku "{0}".
#XMSG: Message for Locked space error
START_VIEW_PERSISTENCY_LOCKED_SPACE_ERROR=Nie można utrwalić widoku "{0}", ponieważ przestrzeń "{1}" jest zablokowana.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
STOP_PERSISTENCY_UNEXPECTED_ERROR=Wystąpił nieoczekiwany błąd podczas usuwania utrwalonych danych dla widoku "{0}".
#XMSG: Message for unexpected error (new message to replace STOP_PERSISTENCY_UNEXPECTED_ERROR)
REMOVE_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Wystąpił nieoczekiwany błąd podczas usuwania utrwalenia dla widoku "{0}".
#XMSG: Message for ivalidated view
VIEW_PERSISTENCY_ABORTED_DEFINITION_INVALID=Definicja widoku "{0}" nie jest już prawidłowa najprawdopodobniej z powodu zmiany obiektu bezpośrednio lub pośrednio używanego przez widok. Spróbuj ponownie wdrożyć widok, aby rozwiązać problem lub zidentyfikować główną przyczynę.
#XMSG: Message for persisted data removal (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT=Utrwalone dane są usuwane podczas wdrażania widoku "{0}".
#XMSG: Message for persisted data removal of a consuming view
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW=Utrwalone dane są usuwane podczas wdrażania wykorzystanego widoku "{0}".
#XMSG: Message for persisted data removal of a consuming view due to a dac change
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW_DAC_CHANGED=Utrwalone dane są usuwane podczas wdrażania wykorzystanego widoku "{0}", ponieważ zmieniła się kontrola dostępu do danych.
#XMSG: Message for persisted data removal (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DEPLOYMENT=Utrwalone dane są usuwane podczas wdrażania widoku "{0}".
#XMSG: Message for deleted view (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION=Utrwalenie jest usuwane wraz z usunięciem widoku "{0}".
#XMSG: Message for deleted view (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DELETION=Utrwalenie jest usuwane wraz z usunięciem widoku "{0}".
#XMSG Message for removal of persisted data, because prerequisites not fulfilled
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_PREREQUISITES_NOT_FULFILLED=Utrwalone dane są usuwane, ponieważ nie są już spełnione warunki wstępne dot. utrwalania danych.
#XMSG: Message for inconsistent persistency
VIEW_PERSISTENCY_INCONSISTENT_PERSISTENCY=Utrwalenie widoku "{0}" stało się niespójne. Aby rozwiązać ten problem, usuń utrwalone dane.
#XMSG: Message for prerequisites
START_PERSISTENCY_CHECK_PREREQUISITES=Sprawdzanie warunków wstępnych utrwalenia widoku "{0}".
#XMSG: Message for DAC deployed in deprecated way using table function and join (for messages created before 2025.05)
START_PERSISTENCY_DEPRECIATED_DAC=Widok "{0}" wdrożono za pomocą kontroli dostępu do danych (DAC), która jest wycofywana. Wdróż widok ponownie, aby poprawić wydajność.
#XMSG: Message for DAC deployed in deprecated way using table function and join
START_PERSISTENCY_DEPRECATED_DAC=Widok "{0}" wdrożono za pomocą kontroli dostępu do danych (DAC), która jest wycofywana. Wdróż widok ponownie, aby poprawić wydajność.
#XMSG: Message for roll back (for messages created before version 2022.03)
ROLLBACK_VIEW_PERSISTENCY=Wystąpił błąd. Przywrócono wcześniejszy stan utrwalenia dla widoku "{0}".
#XMSG: Message used by SEAL repository for rollback of running procedure to persist view (new message to replace ROLLBACK_VIEW_PERSISTENCY)
VIEW_PERSISTENCY_ROLLBACK=Wystąpił błąd. Proces utrwalenia widoku "{0}" został zatrzymany i wycofano zmiany.
#XMSG: Message used by SEAL repository for rollback of running procedure to remove persisted data of view
REMOVE_VIEW_PERSISTENCY_ROLLBACK=Wystąpił błąd. Proces usuwania utrwalonych danych widoku "{0}" został zatrzymany i wycofano zmiany.
#XMSG: Message used by SEAL repository for persistency preparation
START_PERSISTENCY_PREPARATION=Przygotowanie do utrwalenia danych.
#XMSG: Message used by SEAL repository for data insertion
START_INSERTING_PERSISTENCY_DATA=Wstawianie danych w tabeli utrwalenia.
#XMSG: Message for null values - partitioning (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS={0} rek. wartości null wstawiono w partycji "inne".
#XMSG: Message used by SEAL repository for others partition values - partitioning (new message to replace START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS=Wstawiono {0} rek. w partycji "inne" dla wartości "{2}" < "{1}" OR "{2}" >= "{3}".
#XMSG: Message used by SEAL repository for others partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS_V2=Wstawiono {0} rek. w partycji "inne" dla wartości "{2}" < "{1}" OR "{2}" >= "{3}". Wykorzystana pamięć: {4} GiB.
#XMSG: Message used by SEAL repository for null partition values - partitioning
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS=Wstawiono {0} rek. w partycji "inne" dla wartości "{1}" IS NULL.
#XMSG: Message used by SEAL repository for null partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS_V2=Wstawiono {0} rek. w partycji "inne" dla wartości "{1}" IS NULL. Wykorzystana pamięć: {2} GiB.
##XFLD:Message used by SEAL repository for remote statistics
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS=Wczytywanie istotnych danych. Liczba instrukcji: {0}. Łączna liczba pobranych rekordów: {1}. Łączny czas trwania: {2} sek.
##XFLD:Message used by SEAL repository for remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS_WITH_PARTITIONS=Wczytywanie istotnych danych za pomocą {0} partycji z {1} zdalnymi instrukcjami. Łączna liczba pobranych rekordów: {2}. Łączny czas trwania: {3} sek.
##XFLD:Message used by SEAL repository with UI ID for instructions of remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_INSTRUCTION=Zdalne instrukcje przetworzone podczas przebiegu można wyświetlić poprzez otwarcie monitora zdalnego zapytania, w szczegółach komunikatów dotyczących partycji.
##XFLD:Message for start reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS=Rozpoczęcie procesu ponownego użycia istniejących utrwalonych danych dla widoku {0} po wdrożeniu.
##XFLD:Message used by SEAL repository for reattach view persitency
START_VIEW_PERSISTENCY_REATTACHMENT=Uruchom ponowne użycie istniejących utrwalonych danych.
##XFLD:Message used by SEAL repository for reattach view persitency
START_FINILIZING_VIEW_PERSISTENCY_REATTACHMENT=Ponowne użycie istniejących utrwalonych danych.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_COMPLETE=Proces ponownego użycia istniejących utrwalonych danych został zakończony dla widoku {0}.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_FAILED=Nie udało się ponownie użyć istniejących utrwalonych danych dla widoku {0} po wdrożeniu.
#XMSG: Message used by SEAL repository for finalizing persistency
START_FINALIZING_PERSISTENCY=Finalizowanie utrwalenia.
#XMSG: Message used by SEAL repository for restoring virtual access for view
REMOVE_VIEW_PERSISTENCY_VIRTUAL_ACCESS_RESTORED=Przywrócono wirtualny dostęp do danych dla widoku "{0}".
#XMSG: Message used by SEAL repository for view that already has virtual access
REMOVE_VIEW_PERSISTENCY_ALREADY_VIRTUAL_ACCESS=Widok "{0}" ma już dostęp do danych wirtualnych. Nie usunięto utrwalonych danych.
#XMSG: Message for removing view persistency
VIEW_PERSISTENCY_REMOVED_FROM_MONITOR=Widok "{0}" został usunięty z monitora widoków.
#XMSG: Message for view doesnt exist.
VIEW_PERSISTENCY_VIEW_NOT_EXISTING=Widok "{0}" nie istnieje w bazie danych lub nie został prawidłowo wdrożony i w związku z tym nie można go utrwalić. Spróbuj ponownie wdrożyć widok, aby rozwiązać problem lub ustalić główną przyczynę.
#XMSG: Message for persistency not enabled.
VIEW_PERSISTENCY_NOT_ENABLED=Utrwalanie danych nie zostało włączone. Wdróż tabelę/widok ponownie w przestrzeni "{0}", aby włączyć funkcjonalność.
#XMSG: Message for unexpected error due to node crash
VIEW_PERSISTENCY_UNEXPECTED_ERROR_GENERAL=Ostatni przebieg utrwalania widoku przerwano z powodu błędów technicznych.
#XFLD: Message for view persistency memory consumption
VIEW_PERSISTENCY_MEMORY_CONSUMPTION=Użyto {0} GiB pamięci maksymalnej w czasie wykonania utrwalania widoku.
#XFLD: Message for view persistency timeout
VIEW_PERSISTENCY_TIMEOUT=Przekroczono czas utrwalenia widoku {0} wynoszący {1} godz.
#XFLD: Message for not started asynchronous view persistency
VIEW_PERSISTENCY_ASYNC_NOT_STARTED=Duże obciążenie systemu uniemożliwiło rozpoczęcie asynchronicznego wykonania utrwalenia widoku. Sprawdź, czy nie uruchomiono równolegle zbyt wielu zadań.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency (for messages created before version 2022.05)
START_VIEW_PERSISTENCY_EXECUTION_METHOD_DROP_AND_RENAME=Istniejąca utrwalona tabela została usunięta i zastąpiona nową utrwaloną tabelą.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_RECREATE_AND_DROP=Istniejąca utrwalona tabela została usunięta i zastąpiona nową utrwaloną tabelą.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_TRUNCATE_AND_COPY=Istniejąca utrwalona tabela została zaktualizowana z uwzględnieniem nowych danych.
##XFLD: Message for missing authorizations for view persistency
VIEW_PERSISTENCY_MISSING_AUTH=Brak uprawnień do utrwalania danych.
##XFLD: Message start of persistency cancellation
VIEW_PERSISTENCY_TRIGGER_CANCEL=Rozpoczynanie anulowania procesu w celu utrwalenia widoku {0}.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_UNAVAILABLE_TASK_RUN=Nie udało się anulować procesu utrwalania widoku, ponieważ nie ma żadnego uruchomionego zadania utrwalania danych dla widoku {0}.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_NONDETERMINISTIC_TASK_RUN=Nie udało się anulować procesu utrwalania widoku, ponieważ nie ma żadnego uruchomionego zadania utrwalania danych dla widoku {0}.
##XFLD: Message for canceling a non running persitency task
VIEW_PERSISTENCY_CANCEL_NON_RUNNING_SELECTED_TASK=Nie udało się anulować procesu utrwalania widoku {0}, ponieważ wybrane zadanie utrwalania danych {1} nie jest wykonywane.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_UNREACHABLE_PERSISTENCY_RUN=Nie można było anulować procesu w celu utrwalenia widoku, ponieważ utrwalenie danych dla widoku {0} jeszcze nie zostało rozpoczęte.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_COMPLETED_PERSISTENCY_RUN=Nie można było anulować procesu w celu utrwalenia widoku {0}, ponieważ został już zakończony.
##XFLD: Message for failed cancellation
VIEW_PERSISTENCY_ABORTED_CANCEL_EXECUTION=Nie można było anulować procesu w celu utrwalenia widoku {0}.
##XFLD: Message for finalizing persistency cancellation
VIEW_PERSISTENCY_FINALIZE_CANCEL=Proces zatrzymania utrwalania widoku {0} został przesłany.
##XFLD: Message used in persist task when cancellation is caught
VIEW_PERSISTENCY_CANCELED=Proces mający na celu utrwalenie widoku {0} został zatrzymany.
##XFLD: Message used in persist task when cancelation is caught and performed through a cancelation task
VIEW_PERSISTENCY_CANCELED_VIA_CANCEL_TASK=Proces mający na celu utrwalenie widoku {0} został zatrzymany za pośrednictwem zadania anulowania {1}.
##XFLD: Message used in cancel task when the task is triggered by deployment
VIEW_PERSISTENCY_CANCEL_TRIGGERED_BY_DEPLOYMENT=Anulowanie procesu mającego na celu utrwalenie danych podczas wdrażania widoku {0}.
##XFLD: Message used in case the cancelation is retriggered for a running task
VIEW_PERSISTENCY_CANCEL_ATTEMPT_RETRIGGER_CANCEL=Poprzednie zadanie anulowania utrwalania widoku {0} zostało już przesłane.
##XFLD: Message used to express the delay that may occur before the persistency task is stopped
VIEW_PERSISTENCY_CANCEL_DELAY_STOPPING_PERSISTENCY=Może wystąpić opóźnienie do czasu zatrzymania zadania utrwalania dla widoku {0}.
##XFLD: Message used to reflect the task log identifier of the current running persistency task
VIEW_PERSISTENCY_CANCEL_CURRENT_RUNNING_TASK=Dane dla widoku {0} są utrwalane w ramach zadania {1}.
##XFLD:Message used by SEAL repository for locked partitions and DACs
VIEW_PERSISTENCY_LOCKED_PARTITIONS_DACS=Uprawnienia dostarczone przez kontrole dostępu do danych (DAC) mogły się zmienić i nie są uwzględniane przez zablokowane partycje. Odblokuj partycje i wczytaj nową migawkę w celu zastosowania zmian.
##XFLD:Message used by SEAL repository for column structure change
VIEW_PERSISTENCY_COLUMN_STRUCTURE_CHANGE=Struktura kolumny uległa zmianie i nie jest zgodna z istniejącą tabelą trwałości. Usuń utrwalone dane i rozpocznij nowe utrwalanie danych, aby zaktualizować swoją tabelę trwałości.
##XFLD: Message for out of memory
VIEW_PERSISTENCY_HANA_OUT_OF_MEMORY=Zadanie nie powiodło się z powodu wyczerpania pamięci bazy danych SAP HANA.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_EXCEPTION_TRANSACTION_ROLLBACK=Zadanie nie powiodło się z powodu wewnętrznego wyjątku bazy danych SAP HANA.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_SQL_EXECUTION_ISSUE=Zadanie nie powiodło się z powodu wewnętrznego problemu z wykonaniem kodu SQL bazy danych SAP HANA.
##XFLD: Message for HANA out of memory result
VIEW_PERSISTENCY_OUT_OF_MEMORY_RESULT_MESSAGE=Przyczyna zdarzenia braku pamięci w systemie HANA: {0}
##XFLD: Message for admission control rejection
VIEW_PERSISTENCY_HANA_ADMISSION_CTRL_REJECTION=Zadanie nie powiodło się z powodu odrzucenia kontroli przyjęcia SAP HANA.
##XFLD: Message for HANA back pressure / rate limit of HANA client
VIEW_PERSISTENCY_HANA_CLIENT_RATE_LIMIT_REJECTION=Zadanie nie powiodło się z powodu zbyt wielu aktywnych połączeń SAP HANA.
#XMSG: Message for invalid persisted table name
VIEW_PERSISTENCY_PERSISTED_TABLE_INVALID=Wystąpił błąd i utrwalona tabela utraciła ważność. Aby rozwiązać ten problem, usuń utrwalone dane i ponownie utrwal widok.
#XMSG: Message for usahe of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION=Nie można utrwalić widoku. Jest w nim używana tabela zdalna na podstawie źródła zdalnego z włączoną propagacją użytkownika. Sprawdź pochodzenie widoku.
#XMSG: Message for dynamic usage of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION_DYNAMIC=Nie można utrwalić widoku. Jest w nim używana tabela zdalna na podstawie źródła zdalnego z włączoną propagacją użytkownika. Tabela zdalna może być wykorzystywane dynamicznie za pomocą widoku skryptu SQL. Pochodzenie widoku może nie pokazywać tabeli zdalnej.
#XMSG: Message for insufficient privileges or consumption of view with DAC via dynamic SQL
VIEW_PERSISTENCY_INSUF_PRIV_OR_DAC_DYN_SQL=Możliwe, że masz niewystarczające uprawnienia. Otwórz Podgląd danych, aby sprawdzić, czy masz wymagane uprawnienia. Jeśli tak, oznacza to, że względem drugiego widoku użytkowanego za pośrednictwem dynamicznego skryptu SQL mogła zostać zastosowana kontrola dostępu do danych (DAC).
#XMSG: deployment of DAC is deprecated and view need to be redeployed to be able to persist data
VIEW_PERSISTENCY_DEPRECATED_DAC=Widok "{0}" wdrożono za pomocą kontroli dostępu do danych (DAC), która jest wycofywana. Wdróż widok ponownie, aby móc utrwalić dane dla widoku.
##XFLD:Message used by SEAL repository for input parameter
START_VIEW_PERSISTENCY_INPUT_PARAMETER=Stosowanie wartości domyślnej "{0}" dla parametru wejściowego "{1}".
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Elastic compute node keys~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
##XFLD:Message used to reflect disablement of ecn replica
ECN_DISABLE_EXISTING_REPLICA=Replika elastycznego węzła obliczeniowego została wyłączona.
##XFLD:Message used to reflect recreation of ecn replica
ECN_RECREATE_EXISTING_REPLICA=Replika elastycznego węzła obliczeniowego została ponownie utworzona.
##XFLD:Message used to reflect re-enablement of ecn replica
ECN_ENABLE_EXISTING_REPLICA=Replika elastycznego węzła obliczeniowego została ponownie włączona.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Task Schedule Menu key~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message to schedule task
scheduleText=Zaplanuj
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Utwórz harmonogram
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Edytuj harmonogram
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Usuń harmonogram
#XFLD: Refresh frequency field
refreshFrequency=Częstotliwość odświeżania
#XFLD: Refresh frequency field
refreshFrequencyNew=Częstotliwość
#XFLD: Refresh frequency field
refreshFrequencyNewNew=Zaplanowana częstotliwość
#XBUT: label for None
none=Brak
#XBUT: label for Real-Time replication state
realtime=W czasie rzeczywistym
#XFLD: Label for table column
txtNextSchedule=Następny przebieg
#XFLD: Label for table column
txtNextScheduleNew=Zaplanowany następny przebieg
#XFLD: Label for table column
txtNumOfRecords=Liczba rekordów
#XFLD: Label for scheduled link
scheduledTxt=Zaplanowane
#XFLD: LABEL for partially persisted link
partiallyPersisted=Utrwalono częściowo
#XFLD: Text for paused text
paused=Wstrzymane

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ View Monitor Tasklog Details Action keys~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message for release lock actionn
releaseLockMsg=Jeśli przebieg trwa dłużej niż zwykle, może to oznaczać, że się nie powiódł, a status nie został odpowiednio zaktualizowany. \r\n Aby rozwiązać ten problem, możesz zwolnić blokadę i ustawić status Niepowodzenie.
#XFLD: Label for release lock dialog
releaseLockText=Zwolnij blokadę

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Column header tooltip ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: tooltip for table column
txtViewTooltip=Nazwa utrwalonego widoku
#XFLD: tooltip for table column
txtViewDataAccessTooltip=Określa dostępność widoku
#XFLD: tooltip for table column
txtViewRefreshFreqTooltip=Określa, czy dla widoku jest zdefiniowany harmonogram
#XFLD: tooltip for table column
txtViewStatusTooltip=Pobierz status utrwalonego widoku
#XFLD: tooltip for table column
txtViewLatestUpdateTooltip=Informuje o dacie ostatniej aktualizacji utrwalonego widoku
#XFLD: tooltip for table column
txtViewNextRunTooltip=Jeśli dla widoku ustawiono harmonogram, sprawdź do kiedy zostało zaplanowane wykonanie kolejnego przebiegu
#XFLD: tooltip for table column
txtViewNumOfRecordsTooltip=Monitoruj liczbę rekordów.
#XFLD: tooltip for table column
txtViewInMemorySizeTooltip=Monitoruj jaki jest rozmiar widoku w Twojej pamięci
#XFLD: tooltip for table column
txtViewDiskSizeTooltip=Monitoruj jaki jest rozmiar widoku na Twoim dysku
#XMSG: Expired text
txtExpired=Wygasłe

#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Nie można dodać obiektu "{0}" do łańcucha zadań.

#~~~~~~~~~~~View Analyzer task log messages~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message indicating number of records for a view, and memory consumption in Mebibyte during simulation of view persistency
RECORDS_PEAK_MEMORY=Widok "{0}" ma następującą liczbę rekordów: {1}. Symulacja utrwalania danych dla widoku wykorzystała {2} MiB pamięci.
#XMSG: View Analyzer execution failure
VIEW_ANALYZER_EXECUTION_FAILED=Wykonanie analizatora widoków nie powiodło się.
#XMSG: Missing authorizations for View Analyzer
VIEW_ANALYZER_MISSING_AUTH=Brak uprawnień dla analizatora widoków.
#XMSG: Maximum memory in Gibibyte is reached, therefore persistency simulation is stopped
MAXIMUM_MEMORY_REACHED=Podczas symulacji utrwalania danych dla widoku "{1}" osiągnięto limit wykorzystania pamięci wynoszący {0} GiB. W związku z tym kolejne symulacje utrwalania danych nie zostaną wykonane.
#XMSG: Error during persistency simulation occurred
PERSISTENCY_SIMULATION_ERROR=Wystąpił błąd podczas symulacji utrwalania danych dla widoku "{0}".
#XMSG: Persistency simulation is not executed, because prerequisites are not fulfilled and the view cannot be persisted
PREREQUISITES_NOT_FULFILLED=Symulacja utrwalania danych nie została wykonana dla widoku "{0}", ponieważ nie spełniono warunków wstępnych i nie można utrwalić widoku.
#XMSG: Persistency simulation is not executed, because the view does not exist in the database
VIEW_DOES_NOT_EXIST=Aby aktywować symulację utrwalania danych, trzeba wdrożyć widok "{0}".
#XMSG: Number of records cannot be determined, because the local table does not exist in the database
TABLE_DOES_NOT_EXIST=Tabela lokalna "{0}" nie istnieje w bazie danych, dlatego nie można ustalić liczby rekordów dla tej tabeli.
##XMSG: Message start of cancellation for View Analyzer task
CANCEL_VIEW_ANALYZER_TRIGGERED=Przesłano proces w celu zatrzymania zadania analizatora widoków {0} dla widoku "{1}". Może upłynąć chwila, zanim zadanie zostanie zatrzymane.
##XMSG: Message start to indicate that task is not active
CANCEL_VIEW_ANALYZER_NO_ACTIVE_TASK=Zadanie analizatora widoków {0} dla widoku "{1}" nie jest aktywne.
#XMSG: Cancel View Analyzer task failure
CANCEL_VIEW_ANALYZER_FAILED=Błąd anulowania zadania analizatora widoków.
##XMSG: Message used in View Analyzer execution task when task is canceled
VIEW_ANALYZER_CANCELED=Wykonanie analizatora widoków dla widoku "{0}" zostało zatrzymane za pomocą zadania anulowania.
##XMSG: Message start of cancelation for Model Validation task
CANCEL_MODEL_VALIDATION_TRIGGERED=Przesłano proces w celu zatrzymania zadania walidacji modelu {0} dla widoku "{1}". Może upłynąć chwila, zanim zadanie zostanie zatrzymane.
##XMSG: Message to indicate that task is not active
CANCEL_MODEL_VALIDATION_NO_ACTIVE_TASK=Zadanie walidacji modelu {0} dla modelu "{1}" jest nieaktywne.
#XMSG: Cancel Model Validation task failure
CANCEL_MODEL_VALIDATION_FAILED=Nie udało się anulować zadania walidacji modelu.
##XMSG: Message used in Model Validation task when task is canceled
MODEL_VALIDATION_CANCELED=Wykonanie walidacji modelu dla widoku "{0}" zostało zatrzymane za pomocą zadania anulowania.
#XMSG: Message for Locked space error, before starting Model Validation task
MODEL_VALIDATION_LOCKED_SPACE_ERROR=Nie można wykonać walidacji modelu dla widoku "{0}", ponieważ przestrzeń "{1}" jest zablokowana.
##XMSG: Message used in View Analyzer execution task when determination of number of rows fails
NUMBER_ROWS_DETERMINATION_FAILED=Wystąpił błąd podczas ustalania liczby wierszy dla tabeli lokalnej "{0}".
##XMSG: Message to indicate that SQL plan was created
EXECUTION_PLAN_CREATED=Utworzono plik planu analizatora SQL dla widoku "{0}" i można go pobrać.
#XFLD: Message to indicate starting of process to generate SQL Analyzer plan file
START_PLANVIZ_EXECUTION=Rozpoczynanie procesu generowania pliku planu analizatora SQL dla widoku "{0}".
#XFLD: Message to indicate starting of View Analyzer process
VIEW_ANALYZER_EXECUTION_STARTED=Uruchamianie wykonania analizatora widoków.
##XMSG: Message to indicate that SQL plan file cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_PLANVIZ_NOT_FULFILLED=Nie można wygenerować pliku planu analizatora SQL dla widoku "{0}", ponieważ nie spełniono warunków wstępnych dot. utrwalania danych.
##XMSG: Error message during  generation of SQL Analyzer plan file
PLANVIZ_ERROR=Wystąpił błąd podczas generowania pliku planu analizatora SQL dla widoku "{0}".
#XMSG: Message for Locked space error
VIEW_ANALYZER_LOCKED_SPACE_ERROR=Nie można wykonać analizatora widoków dla widoku "{0}", ponieważ przestrzeń "{1}" jest zablokowana.
#XMSG: Message for persistency simulation without partitioning
PERSISTENCY_SIMULATION_WITHOUT_PARTITIONS=Partycje widoku "{0}" nie są uwzględniane podczas symulacji utrwalania danych.
#XMSG: Message for generation of SQL Analyzer plan file without considering partitioning
PLANVIZ_WITHOUT_PARTITIONS=Partycje widoku "{0}" nie są uwzględniane podczas generowania pliku planu analizatora SQL.
#XMSG: Confirmation message for mass removal of persisted data
MassRemovePersistency_Confirm_Msg=Czy chcesz usunąć utrwalone dane i ponownie przełączyć dostęp do danych na wirtualny?
#XMSG: Confirmation message for partial mass removal of selected persisted data
PartialMassRemovePersistency_Confirm_Msg=W {0} z {1} wybranych widoków znajdują się utrwalone dane. \n Czy chcesz usunąć utrwalone dane i ponownie przełączyć dostęp do danych na wirtualny?
#XMSG: success message for stopping mass persistence
stopMassPersistenceSuccess=Usuwamy zreplikowane dane dla wybranych widoków.
#XMSG: error message during mass stop persistence of a view (backend error)
STOP_MASS_PERSISTENCE_ERROR=Wystąpił błąd podczas zatrzymywania utrwalenia dla wybranych widoków.
#XMSG: memory analysis is only performed in space {0}
NO_MEMORY_ANALYSIS=Analiza pamięci została wykonana tylko dla encji w przestrzeni "{0}": "{1}" "{2}" została pominięta.
##XMSG: Message to indicate that Explain Plan cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_EXPLAINPLAN_NOT_FULFILLED=Nie można wygenerować pliku planu Explain dla widoku "{0}", ponieważ nie spełniono warunków wstępnych dot. utrwalania danych.
#XMSG: Message for Explain Plan generation without partitioning
EXPLAINPLAN_WITHOUT_PARTITIONS=Partycje widoku "{0}" nie są uwzględniane podczas generowania pliku planu Explain.
#XFLD: Message to indicate starting of process to generate Explain Plan file
START_EXPLAINPLAN_EXECUTION=Rozpoczynanie procesu generowania pliku planu Explain dla widoku "{0}".
##XMSG: Message to indicate that Explain Plan was created
EXPLAINPLAN_CREATED=Plik planu Explain dla widoku "{0}" został wygenerowany. Możesz wyświetlić go, klikając opcję "Wyświetl szczegóły", lub pobrać go, jeśli masz odpowiednie uprawnienie.
##XMSG: Error message during  generation of Explain Plan file
EXPLAINPLAN_ERROR=Wystąpił błąd podczas generowania pliku planu Explain dla widoku "{0}".
##XMSG: Explain Plan file cannot be generated for the view {0}, because too many views are stacked on each other
EXPLAINPLAN_MODEL_TOO_COMPLEX=Nie można było wygenerować pliku planu Explain dla widoku "{0}". Skumulowało się zbyt wiele widoków. Złożone modele mogą powodować błędy braku pamięci i wolniejsze działanie. Zalecamy utrwalenie widoku.

##XMSG: Message to indicate that the space is locked
PERFORMANCE_ANALYSIS_LOCKED_SPACE_ERROR=Nie można wykonać analizy wydajności dla widoku "{0}", ponieważ przestrzeń "{1}" jest zablokowana.
##XMSG: The performance analysis task was cancelled
PERFORMANCE_ANALYSIS_CANCELED=Analiza wydajności dla widoku "{0}" została anulowana.
##XMSG: The performance analysis failed
PERFORMANCE_ANALYSIS_FAILED=Analiza wydajności nie powiodła się.
##XMSG: The performance analysis is finished
PERFORMANCE_ANALYSIS_FINISHED=Analiza wydajności dla widoku "{0}" została zakończona. Aby zobaczyć wyniki, kliknij opcję "Wyświetl szczegóły".
##XMSG: The view has input parameter with no default value
INPUT_PARAMETER_WITHOUT_DEFAULT_VALUE=Tego widoku nie można przeanalizować, ponieważ ma parametr bez wartości domyślnej.
##XMSG: The view is not fully deployed
VIEW_NOT_FULLY_DEPLOYED=Tego widoku nie można przeanalizować, ponieważ nie jest w pełni wdrożony.
##XMSG: The view uses limited remote adapters
LIMITED_REMOTE_ADAPTER=Ten widok wykorzystuje co najmniej jeden zdalny adapter z ograniczonymi funkcjami, na przykład brak filtru push-down lub obsługi opcji 'Liczba'. Utrwalenie lub replikacja obiektów może poprawić wydajność w czasie wykonania.
##XMSG: The view uses remote adapter that does not support 'Limit'
REMOTE_ADAPTER_NOT_SUPPORTING_LIMIT=Ten widok korzysta z co najmniej jednego zdalnego adaptera, który nie obsługuje opcji 'Limit'. Mogło zostać wybrane ponad 1000 rekordów.
##XMSG: Performance analysis is executed by using default parameters of view'
INPUT_PARAMETER_WITH_DEFAULT_VALUE=Analiza wydajności jest wykonywana przy użyciu domyślnych wartości parametrów widoku.
##XMSG: Message to indicate that an error occurred
PERFORMANCE_ANALYSIS_ERROR=Wystąpił błąd podczas analizy wydajności dla widoku "{0}".
##XMSG: Message start of cancellation for Performance Analysis task
CANCEL_PERFORMANCE_ANALYSIS_TRIGGERED=Przesłano proces w celu zatrzymania zadania analizy wydajności {0} dla widoku "{1}". Może upłynąć chwila, zanim zadanie zostanie zatrzymane.
##XMSG: Message start to indicate that task is not active
CANCEL_PERFORMANCE_ANALYSIS_NO_ACTIVE_TASK=Zadanie analizy wydajności {0} dla widoku "{1}" nie jest aktywne.
#XMSG: Cancel Performance Analysis task failure
CANCEL_PERFORMANCE_ANALYSIS_FAILED=Nie udało się anulować zadania analizy wydajności.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Przypisz harmonogram do mnie
#XBUT: Pause schedule menu label
pauseScheduleLabel=Wstrzymaj harmonogram
#XBUT: Resume schedule menu label
resumeScheduleLabel=Wznów harmonogram
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Wystąpił błąd podczas usuwania harmonogramów.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Wystąpił błąd podczas przypisywania harmonogramów.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Wystąpił błąd podczas wstrzymywania harmonogramów.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Wystąpił błąd podczas wznawiania harmonogramów.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Usuwanie {0} harmonogramów
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Zmiana osoby odpowiedzialnej dla {0} harmonogramów
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Wstrzymywanie {0} harmonogramów
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Wznawianie {0} harmonogramów
#XBUT: Select Columns Button
selectColumnsBtn=Wybierz kolumny
#XFLD: Refresh tooltip
TEXT_REFRESH=Odśwież
#XFLD: Select Columns tooltip
text_selectColumns=Wybierz kolumny


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Runtime Metrics ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Label for Runtime Metrics
RuntimeMetricsTitle=Metryki czasu wykonania dla
#XFLD : Label for Run Button
runButton=Uruchom
#XFLD : Label for Cancel Button
cancelButton=Anuluj
#XFLD : Label for Close Button
closeButton=Zamknij
#XFLD : Label for Open View Analyzer Button
openViewAnalyzerButton=Otwórz analizator widoku
#XFLD : Label for Generate Explain Plan Button
generateExplainPlanButton=Generuj plan Explain
#XFLD : Label for Previous Run Column
previousRun=Poprzedni przebieg
#XFLD : Label for Latest Run Column
latestRun=Ostatni przebieg
#XFLD : Label for time Column
time=Czas
#XFLD : Label for Duration Column
duration=Czas trwania
#XFLD : Label for Peak Memory Column
peakMemory=Pamięć szczytowa
#XFLD : Label for Number of Rows
numberOfRows=Liczba wierszy
#XFLD : Label for Overall Number of Sources
overallNumberOfSources=Ogólna liczba źródeł
#XFLD : Label for Data Access Column
dataAccess=Dostęp do danych
#XFLD : Label for Local Tables
localTables=Tabele lokalne
#XFLD : Label for federatedRemoteTables
federatedRemoteTables=Sfederowane tabele zdalne (z ograniczonymi funkcjami adaptera)
#XTXT Text for initial state of the runtime metrics
initialState=Aby uzyskać metryki, musisz najpierw uruchomić analizę wydajności. Może to trochę potrwać, ale w razie potrzeby można anulować ten proces.
#XMSG: Cancel confirmation message
CancelRunConfirmationMessage=Czy na pewno chcesz anulować bieżący przebieg analizy wydajności?
#XTIT: Cancel dialog title
CancelRunTitle=Anuluj przebieg
#XFLD: Label for Number of Rows
NUMBER_ROWS=Liczba wierszy
#XFLD: Label for Overall Number of Sources
OVERALL_NUMBER_SOURCES=Ogólna liczba źródeł
#XFLD: Label for Data Access
DATA_ACCESS=Dostęp do danych
#XFLD: Label for Remote tables
NUMBER_REMOTE_TABLES_LIMITED_ADAPTERS=Sfederowane tabele zdalne (z ograniczonymi funkcjami adaptera)
#XFLD: Label for select statement
SELECT_STATEMENT='SELECT * FROM VIEW LIMIT 1000'
#XFLD: Label for duration
SELECT_RUNTIME=Czas trwania
#XFLD: Label for peak memory
SELECT_PEAK_MEMORY=Pamięć szczytowa
#XFLD: Label for count statement
SELECT_COUNT_STATEMENT='SELECT COUNT(*) FROM VIEW'
#XFLD: Label for count duration
SELECT_COUNT_RUNTIME=Czas trwania
#XFLD: Label for count peak memory
SELECT_COUNT_PEAK_MEMORY=Pamięć szczytowa
#XFLD: Label for count of number of local tables
NUMBER_LOCAL_TABLES_FILE=Tabele lokalne (plik)
#XTXT: Text for running state of the runtime metrics
Running=W toku...
#XFLD: Label for time
Time=Czas
#XFLD: Label for virtual access
PA_VIRTUAL=Wirtualny
#XFLD: Label for persisted access
PA_PERSISTED=Utrwalony
PA_PARTIALLY_PERSISTED=Utrwalony częściowo
#XTXT: Text for cancel
CancelRunSuccessMessage=Anulowanie przebiegu analizy wydajności.
#XTXT: Text for cancel error
CancelRunErrorMessage=Wystąpił błąd podczas anulowania przebiegu analizy wydajności.
#XTXT: Text for explain plan generation
ExplainPlanStarted=Generowanie planu Explain dla widoku "{0}".
#XTXT: Text for performance analysis
PerformanceAnalysisStarted=Rozpoczynanie analizy wydajności dla widoku "{0}".
#XTXT: Text for performance analysis error
ErrorFetchingPerformanceAnalysis=Wystąpił błąd podczas pobierania danych analizy wydajności.
#XTXT: Text for performance analysis error
conflictingTask=Zadanie analizy wydajności jest już uruchomione
#XFLD: Label for Errors
Errors=Błędy
#XFLD: Label for Warnings
Warnings=Ostrzeżenia
#XFLD: Tooltip for missing privileges
openViewAnalyzerButtonTooltipNoPrivilege=Aby otworzyć analizator widoku, trzeba mieć uprawnienie DWC_DATAINTEGRATION(aktualizacja).
#XFLD: Tooltip for missing privileges
generateExplainPlanButtonTooltipNoPrivilege=Aby wygenerować plan Explain, trzeba mieć uprawnienie DWC_RUNTIME(odczyt).



#XFLD: Label for frequency column
everyLabel=Co
#XFLD: Plural Recurrence text for Hour
hoursLabel=godz.
#XFLD: Plural Recurrence text for Day
daysLabel=dni
#XFLD: Plural Recurrence text for Month
monthsLabel=mies.
#XFLD: Plural Recurrence text for Minutes
minutesLabel=min
