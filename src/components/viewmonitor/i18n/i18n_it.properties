
#XBCB: Breadcrumb
viewMonitorBreadcrumb={spaceId}

#XFLD: Label for persisted view column
SOURCE=Origine
#XFLD: Label for persisted view column
NAME=Nome
#XFLD: Label for persisted view column
NAME_LABEL=Nome aziendale
#XFLD: Label for persisted view column
NAME_LABELNew=Oggetto (nome aziendale)
#XFLD: Label for persisted view column
TECHINCAL_NAME=Nome tecnico
#XFLD: Label for persisted view column
TECHINCAL_NAMENew=Oggetto (nome tecnico)
#XFLD: Label for persisted view column
DATA_PERSISTENCY=Accesso ai dati
#XFLD: Label for persisted view column
STATUS=Stato
#XFLD: Label for persisted view column
LAST_UPDATED=Ultimo aggiornamento
#XFLD: Label for persisted view column
IN_MEMORY_SIZE=Memoria utilizzata per archivio (MiB)
#XFLD: Label for persisted view column
DISK_SIZE=Disco utilizzato per archivio (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_NEWW=Dimensioni in memoria (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_HEADER=Dimensioni in memoria
#XFLD: Label for persisted view column
DISK_SIZE_NEWW=Dimensioni su disco (MiB)
#XFLD: Label for persisted view column
DISK_SIZE_HEADER=Dimensioni su disco
#XFLD: Label for schedule owner column
txtScheduleOwner=Proprietario pianificazione
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Mostra l'autore creazione della pianificazione
#XFLD: Label for persisted view column
PERSISTED=Persistente
#XFLD: Label for persisted view column
TYPE=Tipo
#XFLD: Label for View Selection Dialog column
changedOn=Data di modifica
#XFLD: Label for View Selection Dialog column
createdBy=Autore creazione
#XFLD: Label for log details column
txtViewPersistencyLogs=Visualizza registri
#XFLD: Label for log details column
txtViewPersistencyLogsNew=Dettagli
#XFLD: text for values shown for Ascending sort order
SortInAsc=Classifica in ordine crescente
#XFLD: text for values shown for Descending sort order
SortInDesc=Classifica in ordine decrescente
#XFLD: Label for persisted view monitor heading
ViewMonitor_Heading=Monitor viste
#XFLD: Label for persisted view monitor sub-heading
ViewMonitor_SubHeading=Monitora ed elabora la persistenza dei dati delle viste


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ DynamicPageHeader ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: text for values shown in column Persistence Status
txtLoading=Caricamento in corso
#XFLD: text for values shown in column Persistence Status
txtRunning=In esecuzione
#XFLD: text for values shown in column Persistence Status
txtAvailable=Disponibile
#XFLD: text for values shown in column Persistence Status
txtError=Errore
#XMSG: Error message for unknown Persistence type that gets logged in Kibana
unknownReplicationTypeError=Tipo di replicazione "{0}" non supportato.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Content ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Impostazioni utilizzate per l'ultima esecuzione di persistenza dei dati:
#XMSG: Message for input parameter name
inputParameterLabel=Parametro di input
#XMSG: Message for input parameter value
inputParameterValueLabel=Valore
#XMSG: Message for persisted data
inputParameterPersistedLabel=Ora di persistenza
#XTIT: Count for View in ViewMonitor Toolbar
txtViewCount=Viste ({0})
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Persistenza viste
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Persistenza dei dati
#XBUT: Button to clear the selected view persistance
ClearViewPersistance=Cancella
#XBUT: Button to stop the selected view persistance
stopPersistance=Interrompi persistenza
#XFLD: Placeholder for Search field
txtSearch=Cerca
#XBUT: Tooltip for refresh button
txtRefresh=Aggiorna
#XBUT: Tooltip for add view button
txtDeleteView=Elimina persistenza
#XBUT: Tooltip for load new peristence
loadNewPersistence=Riavvia persistenza
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Carica nuova istantanea
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Avvia persistenza dei dati
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Rimuovi dati persistenza
#XMSG: success message for starting persistence
startPersistenceSuccess=Si sta applicando la persistenza alla vista "{0}".
#XMSG: success message for stopping persistence
stopPersistenceSuccess=Si stanno rimuovendo i dati con persistenza per la vista "{0}".
#XMSG: success message for stopping persistence
deletePersistedViewSuccess=Si sta rimuovendo la vista "{0}" dall''elenco di monitoraggio.
#XMSG: error message during start persistence of a view (backend error)
ERROR_STARTING_PERSISTENCE=Si è verificato un errore durante l''avvio della persistenza dei dati per la vista "{0}".
#XMSG: error message during start persistence of a view (backend error)
PARAMERROR_STARTING_PERSISTENCE=Impossibile applicare la persistenza alla vista "{0}", perché contiene parametri di input.
#XMSG: error message during start persistence of a view (backend error)
NUMBER_OF_PARAMETER_STARTING_PERSISTENCE=Impossibile applicare la persistenza alla vista "{0}", perché presenta più di un parametro.
#XMSG: error message during start persistence of a view (backend error)
PARAMETER_DEFAULT_VALUE_STARTING_PERSISTENCE=Impossibile applicare la persistenza alla vista "{0}", perché il parametro di input non presenta il valore predefinito.
#XMSG: error message during start persistence of a view (backend error)
REDEPLOYMENT_DAC_REQUIRED=È necessario ridistribuire il controllo di accesso ai dati "{0}" per supportare la persistenza dei dati.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_STARTING_PERSISTENCE=Impossibile applicare la persistenza alla vista "{0}", perché utilizza la vista "{1}" che contiene il controllo di accesso ai dati.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_EXTERNAL_STARTING_PERSISTENCE=Impossibile applicare la persistenza alla vista "{0}", perché utilizza una vista con controllo di accesso ai dati che appartiene a uno spazio diverso.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_STRUCTURE_STARTING_PERSISTENCE=Impossibile applicare la persistenza alla vista "{0}" perché la struttura di uno o più dei relativi controlli di accesso ai dati non supporta la persistenza dei dati.
#XMSG: error message during stop persistence of a view (backend error)
STOP_PERSISTENCE_ERROR=Si è verificato un errore durante l''interruzione della persistenza per la vista "{0}".
#XMSG: error message during stop persistence of a view (backend error)
DELETE_PERSISTED_VIEW_ERROR=Si è verificato un errore durante l''eliminazione della vista persistente "{0}".
#XMSG: Warning message for removing the persisted data
RemovePersistency_Confirm_Msg=Eliminare i dati con persistenza e passare all''accesso virtuale della vista "{0}"?
#XMSG: Warning message for delete the view persistence
DeletePersistency_Confirm_Msg=Rimuovere la vista dall''elenco di monitoraggio ed eliminare i dati con persistenza della vista "{0}"?
#XMSG: error message for reading data from backend
txtReadBackendError=Sembra che si sia verificato un errore durante la lettura dal backend.
#XFLD: Label for No Data Error
NoDataError=Errore
#XMSG: message for conflicting task
Task_Already_Running=È già in esecuzione un task in conflitto per la vista "{0}".

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Show All Views~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Text for show all views button
allViewsText=Tutte le viste ({0})
#XBUT: Text for show scheduled views button
scheduledText=Pianificate ({0})
#XBUT: Text for show persisted views button
persistedText=Persistenti ({0})
#XBUT: Text for start analyzer button
startAnalyzer=Avvia analizzatore vista
#XFLD: Message if repository is unavailable
repositoryErrorMsg=Il repository non è disponibile e determinate funzionalità sono disabilitate.

#XFLD: Data Access - Virtual
Virtual=Virtuale
#XFLD: Data Access - Persisted
Persisted=Persistente

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Non-Persisted View Selection Dialog~~~~~~~~~~~~~~~~~~~~~~~

#XTIT: Title for Non-Persisted View selection dialog
select_source=Seleziona vista cui applicare la persistenza

#XTIT: Placeholder for Non-Persisted View selection dialog search
searchView=Cerca viste
#XTIT: No data in the list of non-persisted view
No_Data=Nessun dato
#XBUT: Button to select non-persisted view
ok=OK
#XBUT: Button to close the non-persisted views selection dialog
cancel=Annulla

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~View persistency log message key~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message for view persistency logs
START_STOP_PERSISTENCY=Avvio dell''esecuzione task di persistenza dei dati per "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY=Applicazione della persistenza ai dati della vista ''{1}''.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY)
START_VIEW_PERSISTENCY_PROCESS=Avvio del processo di applicazione della persistenza ai dati della vista "{0}".
#XFLD: Message for view persistency manual partition load
START_VIEW_PERSISTENCY_PROCESS_MANUAL_PARTITIONS=Avvio del processo per la persistenza dei dati della vista "{0}" con gli ID partizione selezionati: "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY=Rimozione dei dati con persistenza per la vista "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY)
REMOVE_VIEW_PERSISTENCY=Avvio del processo di rimozione dei dati con persistenza della vista "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_SUCCESS=Applicazione della persistenza riuscita ai dati della vista "{1}".
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_SUCCESS)
START_VIEW_PERSISTENCY_PROCESS_SUCCESS=Applicazione della persistenza riuscita ai dati della vista "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY_SUCCESS=Dati con persistenza rimossi e accesso ai dati virtuale ripristinato per la vista "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_SUCCESS)
REMOVE_VIEW_PERSISTENCY_SUCCESS=Completato il processo di rimozione dei dati con persistenza della vista "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_FAILED=Applicazione della persistenza ai dati della vista "{1}" non riuscita.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_FAILED)
START_VIEW_PERSISTENCY_PROCESS_FAILED=Applicazione della persistenza ai dati della vista "{0}" non riuscita.
#XFLD: Message for view persistency logs (for messages created before version 2022.05)
STOP_VIEW_PERSISTENCY_FAILED=Rimozione della persistenza dei dati della vista "{1}" non riuscita.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_FAILED)
REMOVE_VIEW_PERSISTENCY_FAILED=Rimozione della persistenza dei dati della vista "{0}" non riuscita.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_SUCCESS=Persistenza applicata ai record "{3}" della vista "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace START_PERSISTENCY_RECORD_COUNT_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS={0} record inseriti nella tabella di persistenza dei dati per la vista "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS_V2={0} record inseriti nella tabella di persistenza dei dati per la vista "{1}". Memoria utilizzata: {2} GiB.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_SUCCESS=Record con persistenza "{3}" rimossi per la vista "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace STOP_PERSISTENCY_RECORD_COUNT_SUCCESS)
REMOVE_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=Dati con persistenza rimossi, "{0}" record con persistenza eliminati.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_FAILED=Recupero di recordCount non riuscito per la vista "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_FAILED=Recupero di recordCount non riuscito per la vista "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Pianificazione eliminata per "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS)
REMOVE_VIEW_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Pianificazione eliminata per la vista "{0}".
#XFLD: Message for view persistency logs
STOP_PERSISTENCY_DELETE_SCHEDULE_FAILED=Eliminazione della pianificazione non riuscita per "{1}".
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_REDEPLOY=Impossibile applicare la persistenza alla vista "{0}" poiché è stata modificata e distribuita dal momento in cui è stata avviata la persistenza. Riprovare l''operazione o attendere fino alla prossima esecuzione pianificata.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_DELETE=Impossibile applicare la persistenza alla vista "{0}" poiché è stata eliminata dal momento in cui è stata avviata la persistenza.
#XFLD: Message for successful data persistency of a single partition (for messages created before version 2022.03)
START_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} record persistenti nella partizione per valori "{1}" <= {2} < "{3}".
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (new message to replace START_PERSISTENCY_SINGLE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} record inseriti nella partizione per valori "{1}" <= {2} < "{3}".
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS_V2={0} record inseriti nella partizione per valori "{1}" <= "{2}" < "{3}". Memoria utilizzata: {4} GiB.
#XFLD: Message for successful replication of a "others" partition (for messages created before version 2022.03)
START_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} record persistenti nella partizione "Altri".
#XFLD: Message used by SEAL repository for successful replication of a "others" partition (new message to replace START_PERSISTENCY_OTHERS_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} record inseriti nella partizione "Altri".
#XFLD: Message for view persistency record count of partitions (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={3} record persistenti per la vista "{1}" in {4} partizioni.
#XFLD: Message used by SEAL repository for view persistency record count of partitions (new message to replace START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={0} record inseriti nella tabella di persistenza dei dati per la vista "{1}" in {2} partizioni.
#XFLD: Message used by SEAL repository for view persistency for partition wise refresh
START_VIEW_PERSISTENCY_RECORD_COUNT_REFRESHED_PARTITIONS_SUCCESS={0} record inseriti nella tabella di persistenza dei dati per la vista "{1}". Partizioni aggiornate: {2}; partizioni bloccate: {3}; partizioni totali: {4}.
#XFLD: Message used by SEAL repository for view persistency for manual partition load
START_VIEW_PERSISTENCY_RECORD_COUNT_MANUAL_PARTITIONS_SUCCESS={0} record inseriti nella tabella di persistenza dei dati per la vista "{1}" in {2} partizioni selezionate.
#XFLD: Message used by SEAL repository for view persistency for locked partition data load
START_VIEW_PERSISTENCY_RECORD_COUNT_LOCKED_PARTITIONS_SUCCESS={0} record inseriti nella tabella di persistenza dei dati per la vista "{1}". Partizioni aggiornate: {2}; partizioni bloccate invariate: {3}; partizioni totali: {4}.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
START_PERSISTENCY_UNEXPECTED_ERROR=Si è verificato un errore imprevisto durante l''applicazione della persistenza ai dati della vista "{0}".
#XMSG: Message for unexpected error (new message to replace START_PERSISTENCY_UNEXPECTED_ERROR)
START_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Si è verificato un errore imprevisto durante l''applicazione della persistenza ai dati della vista "{0}".
#XMSG: Message for Locked space error
START_VIEW_PERSISTENCY_LOCKED_SPACE_ERROR=Impossibile rendere persistente la vista "{0}" perché lo spazio "{1}" è bloccato.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
STOP_PERSISTENCY_UNEXPECTED_ERROR=Si è verificato un errore imprevisto durante la rimozione dei dati persistenti della vista "{0}".
#XMSG: Message for unexpected error (new message to replace STOP_PERSISTENCY_UNEXPECTED_ERROR)
REMOVE_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Si è verificato un errore imprevisto durante la rimozione della persistenza dalla vista "{0}".
#XMSG: Message for ivalidated view
VIEW_PERSISTENCY_ABORTED_DEFINITION_INVALID=La definizione della vista "{0}" non è più valida, molto probabilmente a causa di una modifica a un oggetto utilizzato direttamente o indirettamente dalla vista. Provare a ridistribuire la vista per risolvere il problema oppure a identificare la causa radice.
#XMSG: Message for persisted data removal (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT=I dati persistenti vengono rimossi nella distribuzione della vista "{0}".
#XMSG: Message for persisted data removal of a consuming view
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW=I dati persistenti vengono rimossi nella distribuzione della vista di consumo "{0}".
#XMSG: Message for persisted data removal of a consuming view due to a dac change
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW_DAC_CHANGED=I dati persistenti vengono rimossi durante la distribuzione della vista di consumo "{0}" perché il relativo controllo di accesso ai dati è cambiato.
#XMSG: Message for persisted data removal (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DEPLOYMENT=I dati persistenti vengono rimossi nella distribuzione della vista "{0}".
#XMSG: Message for deleted view (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION=La persistenza viene rimossa con l''eliminazione della vista "{0}".
#XMSG: Message for deleted view (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DELETION=La persistenza viene rimossa con l''eliminazione della vista "{0}".
#XMSG Message for removal of persisted data, because prerequisites not fulfilled
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_PREREQUISITES_NOT_FULFILLED=I dati persistenti sono rimossi perché non sono più soddisfatti i prerequisiti di persistenza dei dati.
#XMSG: Message for inconsistent persistency
VIEW_PERSISTENCY_INCONSISTENT_PERSISTENCY=La persistenza della vista "{0}" è diventata incoerente. Rimuovere i dati persistenti per risolvere l''errore.
#XMSG: Message for prerequisites
START_PERSISTENCY_CHECK_PREREQUISITES=Verifica dei prerequisiti per la persistenza della vista "{0}".
#XMSG: Message for DAC deployed in deprecated way using table function and join (for messages created before 2025.05)
START_PERSISTENCY_DEPRECIATED_DAC=La vista "{0}" è distribuita utilizzando il controllo di accesso ai dati in obsolescenza. Distribuire nuovamente la vista per migliorare le prestazioni.
#XMSG: Message for DAC deployed in deprecated way using table function and join
START_PERSISTENCY_DEPRECATED_DAC=La vista "{0}" è distribuita utilizzando il controllo di accesso ai dati in obsolescenza. Distribuire nuovamente la vista per migliorare le prestazioni.
#XMSG: Message for roll back (for messages created before version 2022.03)
ROLLBACK_VIEW_PERSISTENCY=Si è verificato un errore. Ripristinato lo stato precedente della persistenza per la vista "{0}".
#XMSG: Message used by SEAL repository for rollback of running procedure to persist view (new message to replace ROLLBACK_VIEW_PERSISTENCY)
VIEW_PERSISTENCY_ROLLBACK=Si è verificato un errore. Il processo di applicazione della persistenza della vista ''{0}'' è stato interrotto ed è stato eseguito il rollback delle modifiche.
#XMSG: Message used by SEAL repository for rollback of running procedure to remove persisted data of view
REMOVE_VIEW_PERSISTENCY_ROLLBACK=Si è verificato un errore. Il processo di rimozione dei dati con persistenza della vista "{0}" è stato interrotto ed è stato eseguito il rollback delle modifiche.
#XMSG: Message used by SEAL repository for persistency preparation
START_PERSISTENCY_PREPARATION=Preparazione per l'applicazione della persistenza ai dati.
#XMSG: Message used by SEAL repository for data insertion
START_INSERTING_PERSISTENCY_DATA=Inserimento dati nella tabella di persistenza.
#XMSG: Message for null values - partitioning (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS={0} record di valori null inseriti nella partizione "Altri".
#XMSG: Message used by SEAL repository for others partition values - partitioning (new message to replace START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS={0} record inseriti nella partizione "Altri" per valori "{2}" < "{1}" OR "{2}" >= "{3}".
#XMSG: Message used by SEAL repository for others partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS_V2={0} record inseriti nella partizione "Altri" per valori "{2}" < "{1}" OR "{2}" >= "{3}". Memoria utilizzata: {4} GiB.
#XMSG: Message used by SEAL repository for null partition values - partitioning
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS={0} record inseriti nella partizione "Altri" per valori "{1}" IS NULL.
#XMSG: Message used by SEAL repository for null partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS_V2={0} record inseriti nella partizione "Altri" per valori "{1}" IS NULL. Memoria utilizzata: {2} GiB.
##XFLD:Message used by SEAL repository for remote statistics
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS=Caricamento dei dati coinvolti: {0} istruzioni remote. Record totali recuperati: {1}. Durata totale: {2} secondi.
##XFLD:Message used by SEAL repository for remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS_WITH_PARTITIONS=Caricamento dei dati coinvolti utilizzando {0} partizioni con {1} istruzioni remote. Record totali recuperati: {2}. Durata totale: {3} secondi.
##XFLD:Message used by SEAL repository with UI ID for instructions of remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_INSTRUCTION=L'istruzione remota elaborata durante l'esecuzione può essere visualizzata aprendo il monitor query remote, nei dettagli dei messaggi specifici della partizione.
##XFLD:Message for start reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS=Avvio del processo per riutilizzare i dati persistenti esistenti perla vista {0} dopo la distribuzione.
##XFLD:Message used by SEAL repository for reattach view persitency
START_VIEW_PERSISTENCY_REATTACHMENT=Avviare per riutilizzare i dati persistenti esistenti.
##XFLD:Message used by SEAL repository for reattach view persitency
START_FINILIZING_VIEW_PERSISTENCY_REATTACHMENT=Riutilizzo dei dati persistenti esistenti.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_COMPLETE=Il processo per riutilizzare i dati persistenti esistenti è completato per la vista {0}.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_FAILED=Impossibile riutilizzare i dati persistenti esistenti per la vista {0} dopo la distribuzione.
#XMSG: Message used by SEAL repository for finalizing persistency
START_FINALIZING_PERSISTENCY=Finalizzazione della persistenza.
#XMSG: Message used by SEAL repository for restoring virtual access for view
REMOVE_VIEW_PERSISTENCY_VIRTUAL_ACCESS_RESTORED=Accesso ai dati virtuale ripristinato per la vista "{0}".
#XMSG: Message used by SEAL repository for view that already has virtual access
REMOVE_VIEW_PERSISTENCY_ALREADY_VIRTUAL_ACCESS=La vista "{0}" presenta già l''accesso ai dati virtuali. Non è stato rimosso alcun dato con persistenza.
#XMSG: Message for removing view persistency
VIEW_PERSISTENCY_REMOVED_FROM_MONITOR=La vista "{0}" è stata rimossa dal Monitor viste.
#XMSG: Message for view doesnt exist.
VIEW_PERSISTENCY_VIEW_NOT_EXISTING=La vista "{0}" non esiste nel database o non è distribuita correttamente, pertanto non può esservi applicata la persistenza. Riprovare la distribuzione della vista per risolvere il problema o identificare la causa radice.
#XMSG: Message for persistency not enabled.
VIEW_PERSISTENCY_NOT_ENABLED=La persistenza dei dati non è abilitata. Ridistribuire una tabella/vista nello spazio "{0}" per abilitare la funzionalità.
#XMSG: Message for unexpected error due to node crash
VIEW_PERSISTENCY_UNEXPECTED_ERROR_GENERAL=L'ultima esecuzione di persistenza della vista è stata interrotta a causa di errori tecnici.
#XFLD: Message for view persistency memory consumption
VIEW_PERSISTENCY_MEMORY_CONSUMPTION=Valore massimo di memoria occupata nel runtime di persistenza della vista pari a {0} GiB.
#XFLD: Message for view persistency timeout
VIEW_PERSISTENCY_TIMEOUT=La persistenza della vista {0} ha raggiunto il timeout di {1} ore.
#XFLD: Message for not started asynchronous view persistency
VIEW_PERSISTENCY_ASYNC_NOT_STARTED=L'elevato carico del sistema ha impedito l'avvio dell'esecuzione asincrona della persistenza della vista. Verificare che non siano in esecuzione troppi task in parallelo.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency (for messages created before version 2022.05)
START_VIEW_PERSISTENCY_EXECUTION_METHOD_DROP_AND_RENAME=La tabella persistente esistente è stata eliminata e sostituita con una nuova.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_RECREATE_AND_DROP=La tabella persistente esistente è stata eliminata e sostituita con una nuova.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_TRUNCATE_AND_COPY=La tabella persistente esistente è stata aggiornata con i nuovi dati.
##XFLD: Message for missing authorizations for view persistency
VIEW_PERSISTENCY_MISSING_AUTH=Autorizzazioni mancanti per persistenza dei dati.
##XFLD: Message start of persistency cancellation
VIEW_PERSISTENCY_TRIGGER_CANCEL=Avvio dell''annullamento del processo per per l''applicazione della persistenza alla vista {0}.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_UNAVAILABLE_TASK_RUN=Annullamento del processo di applicazione della persistenza alla vista non riuscito perché non sono presenti task di persistenza dei dati in esecuzione per la vista {0}.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_NONDETERMINISTIC_TASK_RUN=Annullamento del processo di applicazione della persistenza alla vista non riuscito perché non sono presenti task di persistenza dei dati in esecuzione per la vista {0}.
##XFLD: Message for canceling a non running persitency task
VIEW_PERSISTENCY_CANCEL_NON_RUNNING_SELECTED_TASK=Impossibile annullare il processo di applicazione della persistenza alla vista {0} perché il task di persistenza dei dati selezionato {1} non è in esecuzione.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_UNREACHABLE_PERSISTENCY_RUN=Impossibile annullare il processo di applicazione della persistenza alla vista perché la persistenza dei dati per la vista {0} non è ancora stata avviata.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_COMPLETED_PERSISTENCY_RUN=Impossibile annullare il processo di applicazione della persistenza alla vista {0} perché è già stata completata.
##XFLD: Message for failed cancellation
VIEW_PERSISTENCY_ABORTED_CANCEL_EXECUTION=Annullamento del processo per per l''applicazione della persistenza alla vista {0} non riuscito.
##XFLD: Message for finalizing persistency cancellation
VIEW_PERSISTENCY_FINALIZE_CANCEL=Il processo per arrestare la persistenza dei dati della vista {0} è stato inviato.
##XFLD: Message used in persist task when cancellation is caught
VIEW_PERSISTENCY_CANCELED=Il processo per applicare la persistenza alla vista {0} è stato interrotto.
##XFLD: Message used in persist task when cancelation is caught and performed through a cancelation task
VIEW_PERSISTENCY_CANCELED_VIA_CANCEL_TASK=Il processo per applicare la persistenza alla vista {0} è stato interrotto tramite task di annullamento {1}.
##XFLD: Message used in cancel task when the task is triggered by deployment
VIEW_PERSISTENCY_CANCEL_TRIGGERED_BY_DEPLOYMENT=Annullamento del processo di applicazione della persistenza ai dati durante la distribuzione della vista {0}.
##XFLD: Message used in case the cancelation is retriggered for a running task
VIEW_PERSISTENCY_CANCEL_ATTEMPT_RETRIGGER_CANCEL=Un task di annullamento della persistenza per la vista {0} è già stato inviato.
##XFLD: Message used to express the delay that may occur before the persistency task is stopped
VIEW_PERSISTENCY_CANCEL_DELAY_STOPPING_PERSISTENCY=Potrebbe verificarsi un ritardo prima dell''arresto del task di persistenza dei dati per la vista {0}.
##XFLD: Message used to reflect the task log identifier of the current running persistency task
VIEW_PERSISTENCY_CANCEL_CURRENT_RUNNING_TASK=Persistenza applicata ai dati della vista {0} con il task {1}.
##XFLD:Message used by SEAL repository for locked partitions and DACs
VIEW_PERSISTENCY_LOCKED_PARTITIONS_DACS=Le autorizzazioni fornite dai controlli di accesso ai dati potrebbero essere state modificate e non vengono considerate dalle partizioni bloccate. Sbloccare le partizioni e caricare una nuova istantanea per applicare le modifiche.
##XFLD:Message used by SEAL repository for column structure change
VIEW_PERSISTENCY_COLUMN_STRUCTURE_CHANGE=La struttura colonna è cambiata e non corrisponde più alla tabella di persistenza esistente. Rimuovere i dati persistenti e avviare una nuova persistenza di dati per ottenere la tabella di persistenza aggiornata.
##XFLD: Message for out of memory
VIEW_PERSISTENCY_HANA_OUT_OF_MEMORY=Il task non è riuscito a causa di un errore di esaurimento della memoria nel database SAP HANA.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_EXCEPTION_TRANSACTION_ROLLBACK=Il task non è riuscito a causa di un'eccezione interna nel database SAP HANA.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_SQL_EXECUTION_ISSUE=Il task non è riuscito a causa di un problema di esecuzione SQL interno nel database SAP HANA.
##XFLD: Message for HANA out of memory result
VIEW_PERSISTENCY_OUT_OF_MEMORY_RESULT_MESSAGE=Motivo dell''evento di memoria HANA esaurita: {0}
##XFLD: Message for admission control rejection
VIEW_PERSISTENCY_HANA_ADMISSION_CTRL_REJECTION=Il task non è riuscito a causa di un rifiuto del controllo accettazione SAP HANA.
##XFLD: Message for HANA back pressure / rate limit of HANA client
VIEW_PERSISTENCY_HANA_CLIENT_RATE_LIMIT_REJECTION=Il task non è riuscito a causa di troppe connessioni SAP HANA attive.
#XMSG: Message for invalid persisted table name
VIEW_PERSISTENCY_PERSISTED_TABLE_INVALID=Si è verificato un errore e la tabella di persistenza è diventata non valida. Per risolvere il problema, rimuovere i dati con persistenza e applicare nuovamente la persistenza alla vista.
#XMSG: Message for usahe of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION=La vista non può essere resa persistente. Utilizza una tabella remota basata su un'origine remota con propagazione utente abilitata. Controllare la derivazione della vista.
#XMSG: Message for dynamic usage of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION_DYNAMIC=La vista non può essere resa persistente. Utilizza una tabella remota basata su un'origine remota con propagazione utente abilitata. La tabella remota può essere consumata dinamicamente tramite una vista script SQL. La derivazione della vista potrebbe non mostrare la tabella remota.
#XMSG: Message for insufficient privileges or consumption of view with DAC via dynamic SQL
VIEW_PERSISTENCY_INSUF_PRIV_OR_DAC_DYN_SQL=I privilegi potrebbero essere insufficienti. Aprire l'anteprima dati per verificare se si dispone dei privilegi richiesti. In caso affermativo, a una seconda vista utilizzata tramite script SQL dinamico potrebbe essere applicato un controllo di accesso ai dati.
#XMSG: deployment of DAC is deprecated and view need to be redeployed to be able to persist data
VIEW_PERSISTENCY_DEPRECATED_DAC=La vista "{0}" è distribuita utilizzando il controllo di accesso ai dati in obsolescenza. Distribuire nuovamente la vista per poter applicare la persistenza ai dati della vista.
##XFLD:Message used by SEAL repository for input parameter
START_VIEW_PERSISTENCY_INPUT_PARAMETER=Utilizzo del valore predefinito "{0}" per il parametro di input "{1}".
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Elastic compute node keys~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
##XFLD:Message used to reflect disablement of ecn replica
ECN_DISABLE_EXISTING_REPLICA=Replicazione del nodo di calcolo elastico disabilitata.
##XFLD:Message used to reflect recreation of ecn replica
ECN_RECREATE_EXISTING_REPLICA=Replicazione del nodo di calcolo elastico ricreata.
##XFLD:Message used to reflect re-enablement of ecn replica
ECN_ENABLE_EXISTING_REPLICA=Replicazione del nodo di calcolo elastico riabilitata.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Task Schedule Menu key~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message to schedule task
scheduleText=Pianifica
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Crea pianificazione
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Modifica pianificazione
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Elimina pianificazione
#XFLD: Refresh frequency field
refreshFrequency=Frequenza di aggiornamento
#XFLD: Refresh frequency field
refreshFrequencyNew=Frequenza
#XFLD: Refresh frequency field
refreshFrequencyNewNew=Frequenza pianificata
#XBUT: label for None
none=Nessuna
#XBUT: label for Real-Time replication state
realtime=Tempo reale
#XFLD: Label for table column
txtNextSchedule=Prossima esecuzione
#XFLD: Label for table column
txtNextScheduleNew=Prossima esecuzione pianificata
#XFLD: Label for table column
txtNumOfRecords=Numero di record
#XFLD: Label for scheduled link
scheduledTxt=Pianificato
#XFLD: LABEL for partially persisted link
partiallyPersisted=Persistenza parzialmente applicata
#XFLD: Text for paused text
paused=Sospeso

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ View Monitor Tasklog Details Action keys~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message for release lock actionn
releaseLockMsg=Se un'esecuzione impiega più tempo del normale, può indicare che non è riuscita e che lo stato non è stato aggiornato di conseguenza.\r\nPer risolvere il problema è possibile eliminare il blocco e impostare lo stato su Non riuscito.
#XFLD: Label for release lock dialog
releaseLockText=Elimina blocco

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Column header tooltip ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: tooltip for table column
txtViewTooltip=Nome della vista persistente
#XFLD: tooltip for table column
txtViewDataAccessTooltip=Indica la disponibilità della vista
#XFLD: tooltip for table column
txtViewRefreshFreqTooltip=Indica se è stata definita una pianificazione per la vista
#XFLD: tooltip for table column
txtViewStatusTooltip=Ottieni lo stato della vista persistente
#XFLD: tooltip for table column
txtViewLatestUpdateTooltip=Fornisce informazioni su quando è stata aggiornata l'ultima volta la vista persistente
#XFLD: tooltip for table column
txtViewNextRunTooltip=Se è impostata una pianificazione per la vista, è possibile vedere quando è prevista la prossima esecuzione.
#XFLD: tooltip for table column
txtViewNumOfRecordsTooltip=Numero di tracciamento dei record.
#XFLD: tooltip for table column
txtViewInMemorySizeTooltip=Tieni traccia dello spazio in memoria che la vista sta utilizzando
#XFLD: tooltip for table column
txtViewDiskSizeTooltip=Tieni traccia dello spazio su disco che la vista sta utilizzando
#XMSG: Expired text
txtExpired=Scaduto

#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Impossibile aggiungere l''oggetto "{0}" alla catena di task.

#~~~~~~~~~~~View Analyzer task log messages~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message indicating number of records for a view, and memory consumption in Mebibyte during simulation of view persistency
RECORDS_PEAK_MEMORY=La vista "{0}" ha {1} record. Una simulazione della persistenza dei dati di questa vista ha utilizzato {2} MiB di memoria.
#XMSG: View Analyzer execution failure
VIEW_ANALYZER_EXECUTION_FAILED=Esecuzione dell'analizzatore vista non riuscita.
#XMSG: Missing authorizations for View Analyzer
VIEW_ANALYZER_MISSING_AUTH=Autorizzazioni mancanti per l'analizzatore vista.
#XMSG: Maximum memory in Gibibyte is reached, therefore persistency simulation is stopped
MAXIMUM_MEMORY_REACHED=È stata raggiunta la memoria massima di {0} GiB durante la simulazione della persistenza dei dati per la vista "{1}". Pertanto, non verranno eseguite altre simulazioni di persistenza dei dati.
#XMSG: Error during persistency simulation occurred
PERSISTENCY_SIMULATION_ERROR=Si è verificato un errore durante la simulazione della persistenza dei dati della vista "{0}".
#XMSG: Persistency simulation is not executed, because prerequisites are not fulfilled and the view cannot be persisted
PREREQUISITES_NOT_FULFILLED=La simulazione della persistenza dei dati non è stata eseguita per la vista "{0}", perché i prerequisiti non sono stati soddisfatti e la vista non può essere resa persistente.
#XMSG: Persistency simulation is not executed, because the view does not exist in the database
VIEW_DOES_NOT_EXIST=È necessario distribuire la vista "{0}" per abilitare la simulazione della persistenza dei dati.
#XMSG: Number of records cannot be determined, because the local table does not exist in the database
TABLE_DOES_NOT_EXIST=La tabella locale "{0}" non esiste nel database, pertanto per essa non è possibile determinare il numero di record.
##XMSG: Message start of cancellation for View Analyzer task
CANCEL_VIEW_ANALYZER_TRIGGERED=Il processo di arresto del task dell''analizzatore vista {0} per la vista "{1}" è stato inviato. Potrebbe verificarsi un ritardo prima dell''arresto del task.
##XMSG: Message start to indicate that task is not active
CANCEL_VIEW_ANALYZER_NO_ACTIVE_TASK=Task dell''analizzatore vista {0} per la vista "{1}" non attivo.
#XMSG: Cancel View Analyzer task failure
CANCEL_VIEW_ANALYZER_FAILED=Impossibile annullare il task dell'analizzatore vista.
##XMSG: Message used in View Analyzer execution task when task is canceled
VIEW_ANALYZER_CANCELED=Esecuzione dell''analizzatore vista per la vista "{0}" interrotta tramite task di annullamento.
##XMSG: Message start of cancelation for Model Validation task
CANCEL_MODEL_VALIDATION_TRIGGERED=Il processo di arresto del task di convalida modello {0} per la vista "{1}" è stato inviato. Potrebbe verificarsi un ritardo prima dell''arresto del task.
##XMSG: Message to indicate that task is not active
CANCEL_MODEL_VALIDATION_NO_ACTIVE_TASK=Task di convalida modello {0} per vista "{1}" non attivo.
#XMSG: Cancel Model Validation task failure
CANCEL_MODEL_VALIDATION_FAILED=Annullamento del task di convalida modello non riuscito.
##XMSG: Message used in Model Validation task when task is canceled
MODEL_VALIDATION_CANCELED=Esecuzione della convalida modello per la vista "{0}" interrotta tramite task di annullamento.
#XMSG: Message for Locked space error, before starting Model Validation task
MODEL_VALIDATION_LOCKED_SPACE_ERROR=Impossibile eseguire la convalida modello per la vista "{0}", perché lo spazio "{1}" è bloccato.
##XMSG: Message used in View Analyzer execution task when determination of number of rows fails
NUMBER_ROWS_DETERMINATION_FAILED=Si è verificato un errore durante la determinazione del numero di righe per la tabella locale "{0}".
##XMSG: Message to indicate that SQL plan was created
EXECUTION_PLAN_CREATED=Il file del piano SQL Analyzer per la vista "{0}" è stato creato e può essere scaricato.
#XFLD: Message to indicate starting of process to generate SQL Analyzer plan file
START_PLANVIZ_EXECUTION=Avvio del processo per generare il file del piano SQL Analyzer per la vista "{0}".
#XFLD: Message to indicate starting of View Analyzer process
VIEW_ANALYZER_EXECUTION_STARTED=Avvio esecuzione dell'analizzatore vista.
##XMSG: Message to indicate that SQL plan file cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_PLANVIZ_NOT_FULFILLED=Impossibile generare il file del piano SQL Analyzer per la vista "{0}", poiché non sono soddisfatti i prerequisiti di persistenza dei dati.`
##XMSG: Error message during  generation of SQL Analyzer plan file
PLANVIZ_ERROR=Si è verificato un errore durante la generazione del file del piano SQL Analyzer per la vista "{0}".
#XMSG: Message for Locked space error
VIEW_ANALYZER_LOCKED_SPACE_ERROR=Impossibile eseguire l''analizzatore vista per la vista "{0}", perché lo spazio "{1}" è bloccato.
#XMSG: Message for persistency simulation without partitioning
PERSISTENCY_SIMULATION_WITHOUT_PARTITIONS=Le partizioni della vista "{0}" non vengono prese in considerazione durante la simulazione della persistenza dei dati.
#XMSG: Message for generation of SQL Analyzer plan file without considering partitioning
PLANVIZ_WITHOUT_PARTITIONS=Le partizioni della vista "{0}" non vengono prese in considerazione durante la generazione del file del piano SQL Analyzer.
#XMSG: Confirmation message for mass removal of persisted data
MassRemovePersistency_Confirm_Msg=Rimuovere i dati con persistenza e riportare l'accesso ai dati all'accesso virtuale?
#XMSG: Confirmation message for partial mass removal of selected persisted data
PartialMassRemovePersistency_Confirm_Msg={0} delle {1} viste selezionate presentano dati con persistenza. \n Rimuovere i dati con persistenza e riportare l''accesso ai dati all''accesso virtuale?
#XMSG: success message for stopping mass persistence
stopMassPersistenceSuccess=Si stanno rimuovendo i dati con persistenza per le viste selezionate.
#XMSG: error message during mass stop persistence of a view (backend error)
STOP_MASS_PERSISTENCE_ERROR=Si è verificato un errore durante l'interruzione della persistenza per le viste selezionate.
#XMSG: memory analysis is only performed in space {0}
NO_MEMORY_ANALYSIS=L''analisi della memoria viene eseguita soltanto per le entità dello spazio "{0}": "{1}" "{2}" è stato ignorato.
##XMSG: Message to indicate that Explain Plan cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_EXPLAINPLAN_NOT_FULFILLED=Impossibile generare il file del Piano Explain per la vista "{0}", poiché non sono soddisfatti i prerequisiti di persistenza.
#XMSG: Message for Explain Plan generation without partitioning
EXPLAINPLAN_WITHOUT_PARTITIONS=Le partizioni della vista "{0}" non vengono prese in considerazione durante la generazione del file Piano Explain.
#XFLD: Message to indicate starting of process to generate Explain Plan file
START_EXPLAINPLAN_EXECUTION=Avvio del processo per generare il file Piano Explain per la vista "{0}".
##XMSG: Message to indicate that Explain Plan was created
EXPLAINPLAN_CREATED=Il file Piano Explain è stato generato per la vista "{0}". È possibile visualizzarlo facendo clic su "Visualizza dettagli" o scaricarlo se si dispone delle relative autorizzazioni.
##XMSG: Error message during  generation of Explain Plan file
EXPLAINPLAN_ERROR=Si è verificato un errore durante la generazione del file Piano Explain per la vista "{0}".
##XMSG: Explain Plan file cannot be generated for the view {0}, because too many views are stacked on each other
EXPLAINPLAN_MODEL_TOO_COMPLEX=Impossibile generare il file Piano Explain per la vista "{0}". Troppe viste sono in pila una sull''altra. Modelli complessi possono causare errori di memoria esaurita e rallentare le prestazioni. Si consiglia di applicare la persistenza a una vista.

##XMSG: Message to indicate that the space is locked
PERFORMANCE_ANALYSIS_LOCKED_SPACE_ERROR=Impossibile eseguire l''analisi delle prestazioni per la vista "{0}", perché lo spazio "{1}" è bloccato.
##XMSG: The performance analysis task was cancelled
PERFORMANCE_ANALYSIS_CANCELED=Analisi delle prestazioni per la vista "{0}" annullata.
##XMSG: The performance analysis failed
PERFORMANCE_ANALYSIS_FAILED=Analisi delle prestazioni non riuscita.
##XMSG: The performance analysis is finished
PERFORMANCE_ANALYSIS_FINISHED=L''analisi delle prestazioni per la vista "{0}" è terminata. Visualizzare il risultato facendo clic su "Visualizza dettagli".
##XMSG: The view has input parameter with no default value
INPUT_PARAMETER_WITHOUT_DEFAULT_VALUE=Impossibile analizzare questa vista perché presenta un parametro senza valori predefiniti.
##XMSG: The view is not fully deployed
VIEW_NOT_FULLY_DEPLOYED=Questa vista non può essere analizzata perché non è completamente distribuita.
##XMSG: The view uses limited remote adapters
LIMITED_REMOTE_ADAPTER=Questa vista utilizza almeno un adattatore remoto con funzionalità limitate, come l'assenza di pushdown di filtro o il supporto di "Conteggio". La persistenza o la replicazione di oggetti può migliorare le prestazioni del runtime.
##XMSG: The view uses remote adapter that does not support 'Limit'
REMOTE_ADAPTER_NOT_SUPPORTING_LIMIT=Questa vista utilizza almeno un adattatore remoto che non supporta "Limit". Potrebbero essere stati selezionati più di 1000 record.
##XMSG: Performance analysis is executed by using default parameters of view'
INPUT_PARAMETER_WITH_DEFAULT_VALUE=L'analisi delle prestazioni viene eseguita utilizzando i valori predefiniti dei parametri della vista.
##XMSG: Message to indicate that an error occurred
PERFORMANCE_ANALYSIS_ERROR=Si è verificato un errore durante l''analisi delle prestazioni per la vista "{0}".
##XMSG: Message start of cancellation for Performance Analysis task
CANCEL_PERFORMANCE_ANALYSIS_TRIGGERED=Il processo di arresto del task di analisi delle prestazioni {0} per la vista "{1}" è stato inviato. Potrebbe verificarsi un ritardo prima dell''arresto del task.
##XMSG: Message start to indicate that task is not active
CANCEL_PERFORMANCE_ANALYSIS_NO_ACTIVE_TASK=Task di analisi delle prestazioni {0} per la vista "{1}" non attivo.
#XMSG: Cancel Performance Analysis task failure
CANCEL_PERFORMANCE_ANALYSIS_FAILED=Annullamento del task di analisi delle prestazioni non riuscito.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Assegna pianificazione a me
#XBUT: Pause schedule menu label
pauseScheduleLabel=Metti pianificazione in pausa
#XBUT: Resume schedule menu label
resumeScheduleLabel=Riprendi pianificazione
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Si è verificato un errore durante la rimozione delle pianificazioni.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Si è verificato un errore durante l'assegnazione delle pianificazioni.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Si è verificato un errore durante la messa in pausa delle pianificazioni.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Si è verificato un errore durante la ripresa delle pianificazioni.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Eliminazione di {0} pianificazioni
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Modifica del proprietario di {0} pianificazioni
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Messa in pausa di {0} pianificazione
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Ripresa di {0} pianificazioni
#XBUT: Select Columns Button
selectColumnsBtn=Seleziona colonne
#XFLD: Refresh tooltip
TEXT_REFRESH=Aggiorna
#XFLD: Select Columns tooltip
text_selectColumns=Seleziona colonne


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Runtime Metrics ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Label for Runtime Metrics
RuntimeMetricsTitle=Metriche di runtime per
#XFLD : Label for Run Button
runButton=Esegui
#XFLD : Label for Cancel Button
cancelButton=Annulla
#XFLD : Label for Close Button
closeButton=Chiudi
#XFLD : Label for Open View Analyzer Button
openViewAnalyzerButton=Apri Analizzatore vista
#XFLD : Label for Generate Explain Plan Button
generateExplainPlanButton=Genera Piano Explain
#XFLD : Label for Previous Run Column
previousRun=Esecuzione precedente
#XFLD : Label for Latest Run Column
latestRun=Ultima esecuzione
#XFLD : Label for time Column
time=Ora
#XFLD : Label for Duration Column
duration=Durata
#XFLD : Label for Peak Memory Column
peakMemory=Memoria massima
#XFLD : Label for Number of Rows
numberOfRows=Numero di righe
#XFLD : Label for Overall Number of Sources
overallNumberOfSources=Numero complessivo di origini
#XFLD : Label for Data Access Column
dataAccess=Accesso ai dati
#XFLD : Label for Local Tables
localTables=Tabella locale
#XFLD : Label for federatedRemoteTables
federatedRemoteTables=Tabelle remote federate (con capacità di adattamento limitate)
#XTXT Text for initial state of the runtime metrics
initialState=Per ottenere le metriche occorre prima eseguire l'analisi delle prestazioni. L'operazione potrebbe richiedere tempo, ma è possibile annullare il processo se necessario.
#XMSG: Cancel confirmation message
CancelRunConfirmationMessage=Annullare l'esecuzione corrente dell'analisi delle prestazioni?
#XTIT: Cancel dialog title
CancelRunTitle=Annulla esecuzione
#XFLD: Label for Number of Rows
NUMBER_ROWS=Numero di righe
#XFLD: Label for Overall Number of Sources
OVERALL_NUMBER_SOURCES=Numero complessivo di origini
#XFLD: Label for Data Access
DATA_ACCESS=Accesso ai dati
#XFLD: Label for Remote tables
NUMBER_REMOTE_TABLES_LIMITED_ADAPTERS=Tabelle remote federate (con capacità di adattamento limitate)
#XFLD: Label for select statement
SELECT_STATEMENT='SELECT * FROM VIEW LIMIT 1000'
#XFLD: Label for duration
SELECT_RUNTIME=Durata
#XFLD: Label for peak memory
SELECT_PEAK_MEMORY=Memoria massima
#XFLD: Label for count statement
SELECT_COUNT_STATEMENT='SELECT COUNT(*) FROM VIEW'
#XFLD: Label for count duration
SELECT_COUNT_RUNTIME=Durata
#XFLD: Label for count peak memory
SELECT_COUNT_PEAK_MEMORY=Memoria massima
#XFLD: Label for count of number of local tables
NUMBER_LOCAL_TABLES_FILE=Tabelle locali (file)
#XTXT: Text for running state of the runtime metrics
Running=In esecuzione...
#XFLD: Label for time
Time=Ora
#XFLD: Label for virtual access
PA_VIRTUAL=Virtuale
#XFLD: Label for persisted access
PA_PERSISTED=Persistente
PA_PARTIALLY_PERSISTED=Persistenza parzialmente applicata
#XTXT: Text for cancel
CancelRunSuccessMessage=Annullamento dell'esecuzione dell'analisi delle prestazioni.
#XTXT: Text for cancel error
CancelRunErrorMessage=Si è verificato un errore durante l'annullamento dell'esecuzione dell'analisi delle prestazioni.
#XTXT: Text for explain plan generation
ExplainPlanStarted=Generazione del Piano Explain per la vista "{0}".
#XTXT: Text for performance analysis
PerformanceAnalysisStarted=Avvio dell''analisi delle prestazioni per la vista "{0}".
#XTXT: Text for performance analysis error
ErrorFetchingPerformanceAnalysis=Si è verificato un errore durante il richiamo dei dati dell'analisi delle prestazioni.
#XTXT: Text for performance analysis error
conflictingTask=Il task di analisi delle prestazioni è già in esecuzione
#XFLD: Label for Errors
Errors=Errore/i
#XFLD: Label for Warnings
Warnings=Avviso/i
#XFLD: Tooltip for missing privileges
openViewAnalyzerButtonTooltipNoPrivilege=Per aprire l'analizzatore vista è necessario il privilegio DWC_DATAINTEGRATION(aggiornamento).
#XFLD: Tooltip for missing privileges
generateExplainPlanButtonTooltipNoPrivilege=Per generare il Piano Explain è necessario il privilegio DWC_RUNTIME(lettura).



#XFLD: Label for frequency column
everyLabel=Ogni
#XFLD: Plural Recurrence text for Hour
hoursLabel=Ore
#XFLD: Plural Recurrence text for Day
daysLabel=Giorni
#XFLD: Plural Recurrence text for Month
monthsLabel=Mesi
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minuti
