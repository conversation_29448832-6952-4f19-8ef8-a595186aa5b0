
#XBCB: Breadcrumb
viewMonitorBreadcrumb={spaceId}

#XFLD: Label for persisted view column
SOURCE=מקור
#XFLD: Label for persisted view column
NAME=שם
#XFLD: Label for persisted view column
NAME_LABEL=שם עסקי
#XFLD: Label for persisted view column
NAME_LABELNew=אובייקט (שם עסקי)
#XFLD: Label for persisted view column
TECHINCAL_NAME=שם טכני
#XFLD: Label for persisted view column
TECHINCAL_NAMENew=אובייקט (שם טכני)
#XFLD: Label for persisted view column
DATA_PERSISTENCY=גישה לנתונים
#XFLD: Label for persisted view column
STATUS=סטאטוס
#XFLD: Label for persisted view column
LAST_UPDATED=עודכן לאחרונה
#XFLD: Label for persisted view column
IN_MEMORY_SIZE=זיכרון בשימוש לאחסון (MiB)
#XFLD: Label for persisted view column
DISK_SIZE=דיסק בשימוש לאחסון (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_NEWW=שטח זיכרון פנימי (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_HEADER=שטח זיכרון פנימי
#XFLD: Label for persisted view column
DISK_SIZE_NEWW=שטח זיכרון בדיסק (MiB)
#XFLD: Label for persisted view column
DISK_SIZE_HEADER=שטח זיכרון בדיסק 
#XFLD: Label for schedule owner column
txtScheduleOwner=בעלי לוח הזמנים
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=מציג את מי שיצר את לוח הזמנים
#XFLD: Label for persisted view column
PERSISTED=מאוחסן באחסון קבוע
#XFLD: Label for persisted view column
TYPE=סוג
#XFLD: Label for View Selection Dialog column
changedOn=שונה בתאריך
#XFLD: Label for View Selection Dialog column
createdBy=נוצר על-ידי
#XFLD: Label for log details column
txtViewPersistencyLogs=הצג יומנים
#XFLD: Label for log details column
txtViewPersistencyLogsNew=פרטים
#XFLD: text for values shown for Ascending sort order
SortInAsc=מיין בסדר עולה
#XFLD: text for values shown for Descending sort order
SortInDesc=מיין בסדר יורד
#XFLD: Label for persisted view monitor heading
ViewMonitor_Heading=מעקב אחר תצוגות
#XFLD: Label for persisted view monitor sub-heading
ViewMonitor_SubHeading=נטר ותחזק נתונים באחסון קבוע של תצוגות


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ DynamicPageHeader ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: text for values shown in column Persistence Status
txtLoading=טוען
#XFLD: text for values shown in column Persistence Status
txtRunning=פועל
#XFLD: text for values shown in column Persistence Status
txtAvailable=זמין
#XFLD: text for values shown in column Persistence Status
txtError=שגיאה
#XMSG: Error message for unknown Persistence type that gets logged in Kibana
unknownReplicationTypeError=סוג שכפול "{0}" לא נתמך.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Content ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=הגדרות בשימוש עבור הפעלת אחסון נתונים קבוע אחרונה:
#XMSG: Message for input parameter name
inputParameterLabel=פרמטר קלט
#XMSG: Message for input parameter value
inputParameterValueLabel=ערך
#XMSG: Message for persisted data
inputParameterPersistedLabel=אוחסן באחסון קבוע בשעה
#XTIT: Count for View in ViewMonitor Toolbar
txtViewCount=תצוגות ({0})
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=הצג אחסון קבוע
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=נתונים באחסון קבוע
#XBUT: Button to clear the selected view persistance
ClearViewPersistance=התאם
#XBUT: Button to stop the selected view persistance
stopPersistance=עצור אחסון קבוע
#XFLD: Placeholder for Search field
txtSearch=חפש
#XBUT: Tooltip for refresh button
txtRefresh=רענן
#XBUT: Tooltip for add view button
txtDeleteView=מחק אחסון קבוע
#XBUT: Tooltip for load new peristence
loadNewPersistence=אפס אחסון קבוע
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=טען תמונת מצב חדשה
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=התחל נתונים באחסון קבוע
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=הסר נתונים עקביים
#XMSG: success message for starting persistence
startPersistenceSuccess=אנחנו מאחסנים קבוע את התצוגה "{0}".
#XMSG: success message for stopping persistence
stopPersistenceSuccess=אנחנו מסירים נתונים באחסון קבוע עבור תצוגה ''{0}''.
#XMSG: success message for stopping persistence
deletePersistedViewSuccess=אנחנו מסירים את התצוגה ''{0}'' מרשימת המעקב.
#XMSG: error message during start persistence of a view (backend error)
ERROR_STARTING_PERSISTENCE=אירעה שגיאה במהלך תחילת נתונים באחסון הקבוע עבור תצוגה ''{0}''.
#XMSG: error message during start persistence of a view (backend error)
PARAMERROR_STARTING_PERSISTENCE=תצוגה ''{0}'' אינה מתאימה עבור אחסון קבוע, מאחר שיש לה פרמטרי קלט.
#XMSG: error message during start persistence of a view (backend error)
NUMBER_OF_PARAMETER_STARTING_PERSISTENCE=תצוגה ''{0}'' אינה מתאימה עבור אחסון קבוע מכיוון שיש לה יותר מפרמטר קלט אחד.
#XMSG: error message during start persistence of a view (backend error)
PARAMETER_DEFAULT_VALUE_STARTING_PERSISTENCE=תצוגה ''{0}'' אינה מתאימה עבור אחסון קבוע מכיוון שלפרמטר הקלט אין ערך ברירת מחדל.
#XMSG: error message during start persistence of a view (backend error)
REDEPLOYMENT_DAC_REQUIRED=עליך לפרוס מחדש את בקרת גישה לנתונים (DAC) "{0}" לתמיכה נתונים באחסון קבוע.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_STARTING_PERSISTENCE=לא ניתן לאחסן את התצוגה "{0}" באופן קבוע כיוון שהיא משתמשת בתצוגה "{1}", שמכילה בקרת גישה לנתונים (DAC).
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_EXTERNAL_STARTING_PERSISTENCE=לא ניתן לאחסן את תצוגה ''{0}'' באחסון קבוע כיוון שהיא משתמשת בתצוגה  עם בקרת גישה לנתונים(DAC) ששייכת למרחב אחר.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_STRUCTURE_STARTING_PERSISTENCE=תצוגה ''{0}'' לא יכולה להיות מאוחסנת באחסון קבוע כיוון שהמבנה של בקרת גישה של נתונים (DAC) אחת או יותר לא תומך בנתונים באחסון קבוע.
#XMSG: error message during stop persistence of a view (backend error)
STOP_PERSISTENCE_ERROR=אירעה שגיאה במהלך עצירת האחסון הקבוע עבור תצוגה ''{0}''.
#XMSG: error message during stop persistence of a view (backend error)
DELETE_PERSISTED_VIEW_ERROR=אירעה שגיאה במהלך מחיקת תצוגה "{0}" באחסון קבוע.
#XMSG: Warning message for removing the persisted data
RemovePersistency_Confirm_Msg=האם ברצונך למחוק את הנתונים באחסון קבוע ולהחליף לגישה וירטואלית של תצוגה ''{0}''?
#XMSG: Warning message for delete the view persistence
DeletePersistency_Confirm_Msg=האם ברצונך להסיר את התצוגה מרשימת המעקב ולמחוק את הנתונים באחסון קבוע של התצוגה ''{0}''?
#XMSG: error message for reading data from backend
txtReadBackendError=נראה שאירעה שגיאה במהלך קריאה מה-Back-End.
#XFLD: Label for No Data Error
NoDataError=שגיאה
#XMSG: message for conflicting task
Task_Already_Running=משימה מתנגשת כבר פועלת עבור התצוגה "{0}".

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Show All Views~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Text for show all views button
allViewsText=כל התצוגות ({0})
#XBUT: Text for show scheduled views button
scheduledText=מתוזמן ({0})
#XBUT: Text for show persisted views button
persistedText=מאוחסן באחסון קבוע ({0})
#XBUT: Text for start analyzer button
startAnalyzer=הפעל מנתח תצוגות
#XFLD: Message if repository is unavailable
repositoryErrorMsg=המאגר אינו זמין ומאפיינים מסוימים מושבתים.

#XFLD: Data Access - Virtual
Virtual=וירטואלי
#XFLD: Data Access - Persisted
Persisted=מאוחסן באחסון קבוע

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Non-Persisted View Selection Dialog~~~~~~~~~~~~~~~~~~~~~~~

#XTIT: Title for Non-Persisted View selection dialog
select_source=בחר תצוגה לאחסון קבוע

#XTIT: Placeholder for Non-Persisted View selection dialog search
searchView=חפש תצוגות
#XTIT: No data in the list of non-persisted view
No_Data=אין נתונים
#XBUT: Button to select non-persisted view
ok=Ok
#XBUT: Button to close the non-persisted views selection dialog
cancel=בטל

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~View persistency log message key~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message for view persistency logs
START_STOP_PERSISTENCY=מתחיל בהפעלת משימה נתונים באחסון קבוע עבור ''{1}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY=מבצע אחסון קבוע של נתונים עבור תצוגה ''{1}''.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY)
START_VIEW_PERSISTENCY_PROCESS=מתחיל תהליך נתונים באחסון קבוע עבור התצוגה "{0}".
#XFLD: Message for view persistency manual partition load
START_VIEW_PERSISTENCY_PROCESS_MANUAL_PARTITIONS=מתחיל תהליך אחסון קבוע של נתונים עבור התצוגה ''{0}'' עם זיהויי המחיצה שנבחרו: ''{1}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY=מסיר נתונים באחסון קבוע עבור תצוגה "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY)
REMOVE_VIEW_PERSISTENCY=מתחיל תהליך הסרת נתונים באחסון קבוע עבור התצוגה "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_SUCCESS=הנתונים אוחסנו קבוע עבור התצוגה "{1}".
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_SUCCESS)
START_VIEW_PERSISTENCY_PROCESS_SUCCESS=הנתונים אוחסנו קבוע עבור התצוגה "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY_SUCCESS=נתונים באחסון קבוע הוסרו והגישה הוירטואלית לנתונים שוחזרה עבור התצוגה ''{1}''.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_SUCCESS)
REMOVE_VIEW_PERSISTENCY_SUCCESS=הושלם תהליך הסרת נתונים באחסון קבוע עבור התצוגה "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_FAILED=לא ניתן לאחסן קבוע נתונים עבור התצוגה ''{1}''.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_FAILED)
START_VIEW_PERSISTENCY_PROCESS_FAILED=לא ניתן לאחסן קבוע נתונים עבור התצוגה ''{0}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.05)
STOP_VIEW_PERSISTENCY_FAILED=לא ניתן להסיר נתונים באחסון קבוע עבור התצוגה "{1}''".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_FAILED)
REMOVE_VIEW_PERSISTENCY_FAILED=לא ניתן להסיר נתונים באחסון קבוע עבור התצוגה "{0}''".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_SUCCESS=''{3}'' רשומות אוחסנו קבוע עבור תצוגה ''{1}''.
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace START_PERSISTENCY_RECORD_COUNT_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS={0} רשומות נוספו לטבלת נתונים באחסון קבוע עבור התצוגה "{1}''".
#XFLD: Message used by SEAL repository for view persistency logs (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS_V2={0} רשומות נוספו לטבלת נתונים באחסון קבוע עבור התצוגה "{1}''". זיכרון בשימוש: {2} GiB.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_SUCCESS=''{3}'' רשומות באחסון קבוע הוסרו עבור תצוגה ''{1}''.
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace STOP_PERSISTENCY_RECORD_COUNT_SUCCESS)
REMOVE_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=נתונים באחסון קבוע הוסרו, רשומות אחסון קבוע "{0}" נמחקו.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_FAILED=הפעולה ''קבל ספירת רשומות'' נכשלה עבור תצוגה ''{1}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_FAILED=הפעולה ''קבל ספירת רשומות'' נכשלה עבור תצוגה ''{1}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=תזמון נמחק עבור ''{1}''.
#XFLD: Message for view persistency logs (new message to replace STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS)
REMOVE_VIEW_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=תזמון נמחק בהצלחה עבור התצוגה "{0}".
#XFLD: Message for view persistency logs
STOP_PERSISTENCY_DELETE_SCHEDULE_FAILED=מחיקת התזמון נכשלה עבור ''{1}''.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_REDEPLOY=לא ניתן לאחסן את התצוגה "{0}" באחסון קבוע כיוון שהיא שונתה ונפרסה מתחילת תהליך אחסונה באחסון קבוע. נסה שוב או המתן עד להפעלה המתוזמנת הבאה.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_DELETE=לא ניתן לאחסן את התצוגה "{0}" באחסון קבוע כיוון שהיא נמחקה מתחילת תהליך אחסונה באחסון קבוע.
#XFLD: Message for successful data persistency of a single partition (for messages created before version 2022.03)
START_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} רשומות באחסון קבוע במחיצה עבור ערכים "{1}" <= {2} < "{3}".
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (new message to replace START_PERSISTENCY_SINGLE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} רשומות נוספו למחיצה עבור ערכים ''{1}'' <= {2} < ''{3}''.
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS_V2={0} רשומות נוספו למחיצה עבור ערכים "{1}" <= "{2}" < "{3}". זיכרון בשימוש: {4} GiB.
#XFLD: Message for successful replication of a "others" partition (for messages created before version 2022.03)
START_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} רשומות באחסון קבוע במחיצה "אחרים".
#XFLD: Message used by SEAL repository for successful replication of a "others" partition (new message to replace START_PERSISTENCY_OTHERS_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} רשומות נוספו למחיצה "אחרים".
#XFLD: Message for view persistency record count of partitions (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={3} רשומות באחסון קבוע עבור תצוגה "{1}" ב-{4} מחיצות.
#XFLD: Message used by SEAL repository for view persistency record count of partitions (new message to replace START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={0} רשומות נוספו לטבלת נתונים באחסון קבוע עבור התצוגה "{1}" ב-{2} מחיצות.
#XFLD: Message used by SEAL repository for view persistency for partition wise refresh
START_VIEW_PERSISTENCY_RECORD_COUNT_REFRESHED_PARTITIONS_SUCCESS={0} רשומות נוספו לטבלת נתונים באחסון קבוע עבור התצוגה "{1}". מחיצות מעודכנות:{2} מחיצות נעולות:{3} סה"כ מחיצות: {4}
#XFLD: Message used by SEAL repository for view persistency for manual partition load
START_VIEW_PERSISTENCY_RECORD_COUNT_MANUAL_PARTITIONS_SUCCESS={0} רשומות התווספו לטבלת נתונים באחסון קבוע עבור תצוגה "{1}" ב-{2} מחיצות שנבחרו.
#XFLD: Message used by SEAL repository for view persistency for locked partition data load
START_VIEW_PERSISTENCY_RECORD_COUNT_LOCKED_PARTITIONS_SUCCESS={0} רשומות נוספו לטבלת נתונים באחסון קבוע עבור התצוגה "{1}". מחיצות מעודכנות:{2} מחיצות נעולות שלא השתנו:{3} סה"כ מחיצות:{4}.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
START_PERSISTENCY_UNEXPECTED_ERROR=שגיאה לא צפויה אירעה במהלך אחסון קבוע של נתונים עבור התצוגה "{0}".
#XMSG: Message for unexpected error (new message to replace START_PERSISTENCY_UNEXPECTED_ERROR)
START_VIEW_PERSISTENCY_UNEXPECTED_ERROR=שגיאה לא צפויה אירעה במהלך אחסון קבוע של נתונים עבור התצוגה "{0}".
#XMSG: Message for Locked space error
START_VIEW_PERSISTENCY_LOCKED_SPACE_ERROR=התצוגה "{0}" לא יכולה להיות מאוחסנת באחסון קבוע כיוון שמרחב "{1}" נעול.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
STOP_PERSISTENCY_UNEXPECTED_ERROR=שגיאה לא צפויה אירעה במהלך הסרת נתונים שאוחסנו באחסון קבוע עבור התצוגה "{0}".
#XMSG: Message for unexpected error (new message to replace STOP_PERSISTENCY_UNEXPECTED_ERROR)
REMOVE_VIEW_PERSISTENCY_UNEXPECTED_ERROR=שגיאה לא צפויה אירעה במהלך הסרת אחסון קבוע עבור התצוגה "{0}".
#XMSG: Message for ivalidated view
VIEW_PERSISTENCY_ABORTED_DEFINITION_INVALID=ההגדרה של תצוגה "{0}" הפכה לבלתי חוקית, עקב שינוי של אובייקט שנצרך באופן ישיר או עקיף על-ידי התצוגה, ככל הנראה. נסה לפרוס מחדש את התצוגה כדי לפתור את הבעיה או כדי לזהות את הגורם העיקרי.
#XMSG: Message for persisted data removal (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT=נתונים באחסון קבוע הוסרו בעת פריסת התצוגה "{0}".
#XMSG: Message for persisted data removal of a consuming view
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW=נתונים באחסון קבוע הוסרו בעת פריסת התצוגה שנצרכה "{0}".
#XMSG: Message for persisted data removal of a consuming view due to a dac change
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW_DAC_CHANGED=נתונים באחסון קבוע מוסרים בזמן פריסת התצוגה הנצרכת ''{0}'' מכיוון שבקרת הגישה לנתונים שלה השתנתה.
#XMSG: Message for persisted data removal (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DEPLOYMENT=נתונים באחסון קבוע הוסרו בעת פריסת התצוגה "{0}".
#XMSG: Message for deleted view (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION=אחסון קבוע הוסר עם מחיקת התצוגה "{0}".
#XMSG: Message for deleted view (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DELETION=אחסון קבוע הוסר עם מחיקת התצוגה "{0}".
#XMSG Message for removal of persisted data, because prerequisites not fulfilled
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_PREREQUISITES_NOT_FULFILLED=נתונים באחסון קבוע הוסרו מכיוון שהדרישות המקדימות של אחסון נתונים קבוע לא מולאו.
#XMSG: Message for inconsistent persistency
VIEW_PERSISTENCY_INCONSISTENT_PERSISTENCY=האחסון הקבוע של התצוגה "{0}" הפך ללא עקבי. הסר את הנתונים באחסון הקבוע כדי לפתור את הבעיה.
#XMSG: Message for prerequisites
START_PERSISTENCY_CHECK_PREREQUISITES=בדיקת הדרישות המוקדמות עבור אחסון קבוע של התצוגה "{0}".
#XMSG: Message for DAC deployed in deprecated way using table function and join (for messages created before 2025.05)
START_PERSISTENCY_DEPRECIATED_DAC=התצוגה "{0}" נפרסת באמצעות בקרת גישה לנתונים (DAC) שנמצאת בתהליך הוצאה משימוש. פרוס את התצוגה שוב כדי לשפר את הביצועים.
#XMSG: Message for DAC deployed in deprecated way using table function and join
START_PERSISTENCY_DEPRECATED_DAC=התצוגה "{0}" נפרסת באמצעות בקרת גישה לנתונים (DAC) שנמצאת בתהליך הוצאה משימוש. פרוס את התצוגה שוב כדי לשפר את הביצועים.
#XMSG: Message for roll back (for messages created before version 2022.03)
ROLLBACK_VIEW_PERSISTENCY=אירעה שגיאה. מצב קודם של אחסון קבוע אוחזר עבור התצוגה "{0}".
#XMSG: Message used by SEAL repository for rollback of running procedure to persist view (new message to replace ROLLBACK_VIEW_PERSISTENCY)
VIEW_PERSISTENCY_ROLLBACK=אירעה שגיאה. תהליך לאחסון קבוע של התצוגה ''{0}'' נעצר והשינויים הוחזרו למצב הקודם.
#XMSG: Message used by SEAL repository for rollback of running procedure to remove persisted data of view
REMOVE_VIEW_PERSISTENCY_ROLLBACK=אירעה שגיאה. התהליך להסרת נתונים באחסון קבוע של התצוגה ''{0}'' נעצר והשינויים הוחזרו למצב הקודם.
#XMSG: Message used by SEAL repository for persistency preparation
START_PERSISTENCY_PREPARATION=מתכונן לאחסן נתונים באחסון קבוע.
#XMSG: Message used by SEAL repository for data insertion
START_INSERTING_PERSISTENCY_DATA=הוספת נתונים בטבלת אחסון קבוע.
#XMSG: Message for null values - partitioning (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS={0} רשומות של ערך Null נוספו למחיצה "אחרים".
#XMSG: Message used by SEAL repository for others partition values - partitioning (new message to replace START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS={0} רשומות נוספו למחיצה ''אחרים'' עבור ערכים "{2}" < "{1}" או "{2}" >= "{3}".
#XMSG: Message used by SEAL repository for others partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS_V2={0} רשומות נוספו למחיצה "אחרים" עבור ערכים "{2}" < "{1}" או "{2}" >= "{3}". זיכרון בשימוש: {4} GiB.
#XMSG: Message used by SEAL repository for null partition values - partitioning
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS={0} רשומות נוספו למחיצה ''אחרים'' עבור ערכים "{1}" IS NULL.
#XMSG: Message used by SEAL repository for null partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS_V2={0} רשומות נוספו למחיצה "אחרים" עבור ערכים "{1}" IS NULL. זיכרון בשימוש: {2} GiB.
##XFLD:Message used by SEAL repository for remote statistics
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS=טעינת הנתונים המעורבים: {0} הצהרות מרוחקות. סך כל הרשומות שהובאו: {1}. זמן משך כולל: {2} שניות.
##XFLD:Message used by SEAL repository for remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS_WITH_PARTITIONS=טעינת נתונים כרוכה בשימוש ב-{0} מחיצות עם {1} הצהרות מרוחקות. סך כל הרשומות שהובאו: {2}. זמן משך כולל: {3} שניות.
##XFLD:Message used by SEAL repository with UI ID for instructions of remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_INSTRUCTION=ניתן להציג את ההצהרות המרוחקות שעובדו במהלך הריצה על-ידי פתיחת צג השאילתות המרוחק, בפרטי ההודעות הספציפיות למחיצות.
##XFLD:Message for start reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS=מתחיל את התהליך לשימוש חוזר בנתונים קיימים שאוחסנו באחסון קבוע עבור תצוגה {0} אחרי הפריסה.
##XFLD:Message used by SEAL repository for reattach view persitency
START_VIEW_PERSISTENCY_REATTACHMENT=התחל לעשות שימוש חוזר בנתונים קיימים שאוחסנו באחסון קבוע.
##XFLD:Message used by SEAL repository for reattach view persitency
START_FINILIZING_VIEW_PERSISTENCY_REATTACHMENT=שימוש חוזר בנתונים קיימים שאוחסנו באחסון קבוע.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_COMPLETE=התהליך לעשיית שימוש חוזר בנתונים קיימים שאוחסנו באחסון קבוע הושלם לתצוגה {0}.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_FAILED=שימוש חוזר בנתונים קיימים שאוחסנו באחסון קבוע עבור תצוגה {0} נכשל אחרי הפריסה.
#XMSG: Message used by SEAL repository for finalizing persistency
START_FINALIZING_PERSISTENCY=מסיים אחסון קבוע.
#XMSG: Message used by SEAL repository for restoring virtual access for view
REMOVE_VIEW_PERSISTENCY_VIRTUAL_ACCESS_RESTORED=גישה לנתונים וירטואליים שוחזרה עבור התצוגה "{0}".
#XMSG: Message used by SEAL repository for view that already has virtual access
REMOVE_VIEW_PERSISTENCY_ALREADY_VIRTUAL_ACCESS=לתצוגה "{0}" כבר יש גישה וירטואלית לנתונים. לא הוסרו נתונים באחסון קבוע.
#XMSG: Message for removing view persistency
VIEW_PERSISTENCY_REMOVED_FROM_MONITOR=תצוגה "{0}" הוסרה ממסך אחסון קבוע של תצוגה.
#XMSG: Message for view doesnt exist.
VIEW_PERSISTENCY_VIEW_NOT_EXISTING=התצוגה "{0}" אינה קיימת בבסיס הנתונים או שאינה פרוסה כראוי, ולכן לא ניתן לאחסן אותה באחסון קבוע. נסה לפרוס מחדש את התצוגה כדי לפתור את הבעיה, או לזהות את סיבת השורש.
#XMSG: Message for persistency not enabled.
VIEW_PERSISTENCY_NOT_ENABLED=נתונים באחסון קבוע לא הופעל. פרוס מחדש טבלה/תצוגה במרחב "{0}" כדי להפעיל את הפונקציונאליות.
#XMSG: Message for unexpected error due to node crash
VIEW_PERSISTENCY_UNEXPECTED_ERROR_GENERAL=הפעלת אחסון קבוע של תצוגה אחרונה הופרעה עקב שגיאות טכניות.
#XFLD: Message for view persistency memory consumption
VIEW_PERSISTENCY_MEMORY_CONSUMPTION={0}GiB של שימוש שיא בזיכרון נמצא בזמן ריצה באחסון קבוע של תצוגה.
#XFLD: Message for view persistency timeout
VIEW_PERSISTENCY_TIMEOUT=אחסון קבוע של תצוגה {0} הגיע ל-Timeout של {1} שעות.
#XFLD: Message for not started asynchronous view persistency
VIEW_PERSISTENCY_ASYNC_NOT_STARTED=עומס מערכת גבוה מנע את התחלת הביצוע הא-סנכרוני של אחסון קבוע של תצוגה. בדוק אם יותר מדי משימות פועלות במקביל.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency (for messages created before version 2022.05)
START_VIEW_PERSISTENCY_EXECUTION_METHOD_DROP_AND_RENAME=טבלת אחסון קבוע קיימת נמחקה והוחלפה בטבלת אחסון קבוע חדשה.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_RECREATE_AND_DROP=טבלת אחסון קבוע קיימת נמחקה והוחלפה בטבלת אחסון קבוע חדשה.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_TRUNCATE_AND_COPY=טבלת אחסון קבוע קיימת עודכנה בנתונים חדשים.
##XFLD: Message for missing authorizations for view persistency
VIEW_PERSISTENCY_MISSING_AUTH=הרשאות חסרות עבור נתונים באחסון קבוע.
##XFLD: Message start of persistency cancellation
VIEW_PERSISTENCY_TRIGGER_CANCEL=מתחיל לבטל את תהליך האחסון הקבוע עבור תצוגה {0}.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_UNAVAILABLE_TASK_RUN=ביטול תהליך האחסון הקבוע של התצוגה נכשל מכיוון שאין משימת אחסון קבוע פועלת עבור התצוגה {0}.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_NONDETERMINISTIC_TASK_RUN=ביטול תהליך האחסון הקבוע של התצוגה נכשל מכיוון שאין משימת נתונים באחסון קבוע פועלת עבור התצוגה {0}.
##XFLD: Message for canceling a non running persitency task
VIEW_PERSISTENCY_CANCEL_NON_RUNNING_SELECTED_TASK=ביטול תהליך לאחסון התצוגה {0} באחסון קבוע נכשל כיוון שמשימת נתונים בבאחסון קבוע {1} לא פועלת.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_UNREACHABLE_PERSISTENCY_RUN=ביטול תהליך האחסון הקבוע עבור התצוגה נכשל מכיוון שאחסון קבוע של נתונים עבור התצוגה {0} עדיין לא התחיל.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_COMPLETED_PERSISTENCY_RUN=ביטול תהליך האחסון הקבוע של התצוגה {0} נכשל מכיוון שהוא כבר הושלם.
##XFLD: Message for failed cancellation
VIEW_PERSISTENCY_ABORTED_CANCEL_EXECUTION=ביטול את תהליך האחסון הקבוע עבור תצוגה {0} נכשל.
##XFLD: Message for finalizing persistency cancellation
VIEW_PERSISTENCY_FINALIZE_CANCEL=תהליך להפסקת נתונים באחסון קבוע עבור התצוגה {0} נשלח.
##XFLD: Message used in persist task when cancellation is caught
VIEW_PERSISTENCY_CANCELED=תהליך אחסון קבוע עבור התצוגה {0} נעצר.
##XFLD: Message used in persist task when cancelation is caught and performed through a cancelation task
VIEW_PERSISTENCY_CANCELED_VIA_CANCEL_TASK=תהליך אחסון קבוע עבור התצוגה {0} נעצר באמצעות משימת הביטול {1}.
##XFLD: Message used in cancel task when the task is triggered by deployment
VIEW_PERSISTENCY_CANCEL_TRIGGERED_BY_DEPLOYMENT=מבטל את תהליך האחסון הקבוע של הנתונים בזמן פריסת התצוגה {0}.
##XFLD: Message used in case the cancelation is retriggered for a running task
VIEW_PERSISTENCY_CANCEL_ATTEMPT_RETRIGGER_CANCEL=משימת הביטול הקודמת עבור אחסון קבוע של התצוגה {0} כבר נשלחה.
##XFLD: Message used to express the delay that may occur before the persistency task is stopped
VIEW_PERSISTENCY_CANCEL_DELAY_STOPPING_PERSISTENCY=ייתכן שיהיה עיכוב עד שמשימת נתונים באחסון קבוע עבור התצוגה {0} תופסק.
##XFLD: Message used to reflect the task log identifier of the current running persistency task
VIEW_PERSISTENCY_CANCEL_CURRENT_RUNNING_TASK=נתונים עבור תצוגה {0} נשמרים באחסון קבוע עם המשימה {1}.
##XFLD:Message used by SEAL repository for locked partitions and DACs
VIEW_PERSISTENCY_LOCKED_PARTITIONS_DACS=ייתכן שההרשאות שסופקו על-ידי DACs השתנו ואינן נלקחות בחשבון על-ידי מחיצות נעולות. בטל את נעילת המחיצות וטען תמונת מצב חדשה כדי להחיל את השינויים.
##XFLD:Message used by SEAL repository for column structure change
VIEW_PERSISTENCY_COLUMN_STRUCTURE_CHANGE=מבנה העמודה השתנה וכבר לא תואם לטבלת האחסון הקבוע הקיימת. הסר את הנתונים המאוחסנים באחסון קבוע והתחל אחסון נתונים קבוע חדש כדי לעדכן את טבלת האחסון הקבוע.
##XFLD: Message for out of memory
VIEW_PERSISTENCY_HANA_OUT_OF_MEMORY=המשימה נכשלה בגלל שגיאת זיכרון חסר בבסיס הנתונים של SAP HANA.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_EXCEPTION_TRANSACTION_ROLLBACK=המשימה נכשלה בגלל חריגה פנימית בבסיס הנתונים של SAP HANA.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_SQL_EXECUTION_ISSUE=המשימה נכשלה בגלל חריגת SQL פנימית בבסיס הנתונים של SAP HANA.
##XFLD: Message for HANA out of memory result
VIEW_PERSISTENCY_OUT_OF_MEMORY_RESULT_MESSAGE=סיבה לאירוע חוסר בזיכרון של HANA: {0}
##XFLD: Message for admission control rejection
VIEW_PERSISTENCY_HANA_ADMISSION_CTRL_REJECTION=המשימה נכשלה בגלל דחיית בקרת קבלה של SAP HANA.
##XFLD: Message for HANA back pressure / rate limit of HANA client
VIEW_PERSISTENCY_HANA_CLIENT_RATE_LIMIT_REJECTION=המשימה נכשלה בגלל יותר מדי חיבורי SAP HANA פעילים.
#XMSG: Message for invalid persisted table name
VIEW_PERSISTENCY_PERSISTED_TABLE_INVALID=אירעה שגיאה כך שהטבלה באחסון קבוע אינה חוקית עוד. כדי לפתור בעיה זו הסר את הנתונים באחסון קבוע ואחסן את התצוגה באחסון קבוע שוב.
#XMSG: Message for usahe of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION=לא ניתן לאחזן באחסון קבוע את התצוגה. היא משתמשת בטבלה מרוחקת המבוססת על מקור מרוחק עם הפצת משתמשים מופעלת. בדוק את שושלת התצוגה.
#XMSG: Message for dynamic usage of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION_DYNAMIC=לא ניתן לאחסן באחסון קבוע את התצוגה. היא משתמשת בטבלה מרוחקת המבוססת על מקור מרוחק עם הפצת משתמשים מופעלת. ניתן לצרוך את הטבלה המרוחקת באופן דינמי באמצעות תצוגת script של SQL. ייתכן שקישור התצוגה לא יציג את הטבלה המרוחקת.
#XMSG: Message for insufficient privileges or consumption of view with DAC via dynamic SQL
VIEW_PERSISTENCY_INSUF_PRIV_OR_DAC_DYN_SQL=ייתכן שההרשאות שלך לא מספיקות. פתח את התצוגה המקדימה של הנתונים כדי לראות אם יש לך את ההרשאות הנדרשות. אם כן, תצוגה שנייה הנצרכת באמצעות SQL script דינמי עשויה להחיל עליה בקרת גישה לנתונים (DAC).
#XMSG: deployment of DAC is deprecated and view need to be redeployed to be able to persist data
VIEW_PERSISTENCY_DEPRECATED_DAC=התצוגה "{0}" נפרסת באמצעות בקרת גישה לנתונים (DAC) שנמצאת בתהליך הוצאה משימוש. פרוס את התצוגה שוב כדי שניתן יהיה לאחסן נתונים באחסון קבוע עבור התצוגה.
##XFLD:Message used by SEAL repository for input parameter
START_VIEW_PERSISTENCY_INPUT_PARAMETER=שימוש בערך ברירת מחדל ''{0}'' עבור פרמטר קלט ''{1}''.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Elastic compute node keys~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
##XFLD:Message used to reflect disablement of ecn replica
ECN_DISABLE_EXISTING_REPLICA=העתק צומת החישוב הגמיש מושבת.
##XFLD:Message used to reflect recreation of ecn replica
ECN_RECREATE_EXISTING_REPLICA=העתק צומת החישוב הגמיש נוצר מחדש.
##XFLD:Message used to reflect re-enablement of ecn replica
ECN_ENABLE_EXISTING_REPLICA=העתק צומת החישוב הגמיש מופעל מחדש.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Task Schedule Menu key~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message to schedule task
scheduleText=תזמן
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=צור תזמון
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=ערוך תזמון
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=מחק תזמון
#XFLD: Refresh frequency field
refreshFrequency=רענן תדירות
#XFLD: Refresh frequency field
refreshFrequencyNew=תדירות
#XFLD: Refresh frequency field
refreshFrequencyNewNew=תדירות מתוזמנת
#XBUT: label for None
none=ללא
#XBUT: label for Real-Time replication state
realtime=זמן אמת
#XFLD: Label for table column
txtNextSchedule=ההפעלה הבאה
#XFLD: Label for table column
txtNextScheduleNew=ההפעלה המתוזמנת הבאה
#XFLD: Label for table column
txtNumOfRecords=מספר רשומות
#XFLD: Label for scheduled link
scheduledTxt=מתוזמן
#XFLD: LABEL for partially persisted link
partiallyPersisted=אוחסן באחסון קבוע באופן חלקי
#XFLD: Text for paused text
paused=מושהה

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ View Monitor Tasklog Details Action keys~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message for release lock actionn
releaseLockMsg=אם הפעלה אורכת זמן רב מהרגיל, הדבר עשוי להצביע על כישלון ועל כך שהסטאטוס לא עודכן בהתאם. \r\n כדי לפתור את הבעיה באפשרותך לשחרר את הנעילה ולהגדיר את הסטאטוס כ'נכשל'.
#XFLD: Label for release lock dialog
releaseLockText=שחרר נעילה

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Column header tooltip ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: tooltip for table column
txtViewTooltip=שם התצוגה באחסון קבוע
#XFLD: tooltip for table column
txtViewDataAccessTooltip=זה מציין את זמינות התצוגה
#XFLD: tooltip for table column
txtViewRefreshFreqTooltip=זה מציין אם הוגדר תזמון עבור התצוגה
#XFLD: tooltip for table column
txtViewStatusTooltip=קבל את הסטאטוס של התצוגה באחסון קבוע
#XFLD: tooltip for table column
txtViewLatestUpdateTooltip=מספק מידע על המועד האחרון שבו התצוגה הייתה באחסון קבוע
#XFLD: tooltip for table column
txtViewNextRunTooltip=אם נקבע תזמון עבור התצוגה, ראה למתי תוזמנה ההפעלה הבאה.
#XFLD: tooltip for table column
txtViewNumOfRecordsTooltip=עקוב אחר מספר הרשומות.
#XFLD: tooltip for table column
txtViewInMemorySizeTooltip=נטר בכמה שטח בזיכרון שלך משתמשת התצוגה
#XFLD: tooltip for table column
txtViewDiskSizeTooltip=נטר בכמה שטח בזיכרון הדיסק שלך משתמשת התצוגה
#XMSG: Expired text
txtExpired=פג תוקף

#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=אובייקט "{0}" לא ניתן להוספה לשרשרת המשימות.

#~~~~~~~~~~~View Analyzer task log messages~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message indicating number of records for a view, and memory consumption in Mebibyte during simulation of view persistency
RECORDS_PEAK_MEMORY=תצוגה "{0}" מכילה {1} רשומות. הדמיה של נתונים באחסון קבוע של תצוגה זו משתמשת ב-{2} MiB של זיכרון.
#XMSG: View Analyzer execution failure
VIEW_ANALYZER_EXECUTION_FAILED=הביצוע של מנתח תצוגות נכשל.
#XMSG: Missing authorizations for View Analyzer
VIEW_ANALYZER_MISSING_AUTH=הרשאות חסרות עבור מנתח תצוגות.
#XMSG: Maximum memory in Gibibyte is reached, therefore persistency simulation is stopped
MAXIMUM_MEMORY_REACHED=הזיכרון המקסימלי של {0} GiB הושג בעת יצירת הדמיה של נתונים באחסון קבוע עבור תצוגה "{1}", לכן, לא יופעלו הדמיות נוספות של נתונים באחסון קבוע.
#XMSG: Error during persistency simulation occurred
PERSISTENCY_SIMULATION_ERROR=אירעה שגיאה במהלך הדמיית נתונים באחסון קבוע עבור תצוגה "{0}".
#XMSG: Persistency simulation is not executed, because prerequisites are not fulfilled and the view cannot be persisted
PREREQUISITES_NOT_FULFILLED=הדמיית נתונים באחסון קבוע לא מבוצעת עבור תצוגה "{0}" כיוון שהדרישות המקדימות לא מולאו והתצוגה לא יכולה להתאחסן באחסון קבוע.
#XMSG: Persistency simulation is not executed, because the view does not exist in the database
VIEW_DOES_NOT_EXIST=עליך לפרוס את התצוגה "{0}" כדי להפעיל את הדמיית אחסון הנתונים הקבוע.
#XMSG: Number of records cannot be determined, because the local table does not exist in the database
TABLE_DOES_NOT_EXIST=הטבלה המקומית "{0}" אינה קיימת בבסיס הנתונים, לכן לא ניתן לקבוע את מספר הרשומות עבור טבלה זו.
##XMSG: Message start of cancellation for View Analyzer task
CANCEL_VIEW_ANALYZER_TRIGGERED=נשלח תהליך להפסקת משימת מנתח תצוגות {0} עבור תצוגה "{1}". ייתכן שהפסקת המשימה תתעכב.
##XMSG: Message start to indicate that task is not active
CANCEL_VIEW_ANALYZER_NO_ACTIVE_TASK=משימת מנתח תצוגות {0} עבור תצוגה "{1}" לא פעילה.
#XMSG: Cancel View Analyzer task failure
CANCEL_VIEW_ANALYZER_FAILED=ביטול משימת מנתח תצוגות נכשל.
##XMSG: Message used in View Analyzer execution task when task is canceled
VIEW_ANALYZER_CANCELED=ביצוע מנתח תצוגות עבור התצוגה "{0}" הופסק באמצעות משימת ביטול.
##XMSG: Message start of cancelation for Model Validation task
CANCEL_MODEL_VALIDATION_TRIGGERED=נשלח תהליך להפסקת משימת אימות המודל {0} עבור תצוגה "{1}". ייתכן שהפסקת המשימה תתעכב.
##XMSG: Message to indicate that task is not active
CANCEL_MODEL_VALIDATION_NO_ACTIVE_TASK=משימת אימות מודל {0} עבור התצוגה ''{1}'' אינה פעילה.
#XMSG: Cancel Model Validation task failure
CANCEL_MODEL_VALIDATION_FAILED=ביטול משימת אימות מודל נכשל.
##XMSG: Message used in Model Validation task when task is canceled
MODEL_VALIDATION_CANCELED=ביצוע אימות מודל עבור התצוגה "{0}" הופסק באמצעות משימת ביטול.
#XMSG: Message for Locked space error, before starting Model Validation task
MODEL_VALIDATION_LOCKED_SPACE_ERROR=לא ניתן לבצע אימות מודל עבור תצוגה ''{0}'', כיוון שמרחב ''{1}'' נעול.
##XMSG: Message used in View Analyzer execution task when determination of number of rows fails
NUMBER_ROWS_DETERMINATION_FAILED=אירעה שגיאה בעת קביעת מספר השורות עבור טבלה מקומית "{0}".
##XMSG: Message to indicate that SQL plan was created
EXECUTION_PLAN_CREATED=קובץ תוכנית של מנתח SQL לתצוגה "{0}" נוצר וניתן להוריד אותו.
#XFLD: Message to indicate starting of process to generate SQL Analyzer plan file
START_PLANVIZ_EXECUTION=מתחיל תהליך כדי ליצור קובץ תוכנית של מנתח SQL'' עבור תצוגה "{0}".
#XFLD: Message to indicate starting of View Analyzer process
VIEW_ANALYZER_EXECUTION_STARTED=התחל בביצוע מנתח תצוגות.
##XMSG: Message to indicate that SQL plan file cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_PLANVIZ_NOT_FULFILLED=לא ניתן ליצור קובץ תוכנית של מנתח SQL עבור תצוגה "{0}" מכיוון שדרישות מקדימות של נתונים באחסון קבוע לא מולאו.
##XMSG: Error message during  generation of SQL Analyzer plan file
PLANVIZ_ERROR=אירעה שגיאה במהלך יצירה של קובץ תוכנית של מנתח SQL עבור תצוגה "{0}".
#XMSG: Message for Locked space error
VIEW_ANALYZER_LOCKED_SPACE_ERROR=לא ניתן לבצע מנתח תצוגות עבור תצוגה ''{0}'', כיוון שמרחב ''{1}'' נעול.
#XMSG: Message for persistency simulation without partitioning
PERSISTENCY_SIMULATION_WITHOUT_PARTITIONS=המחיצות של תצוגה "{0}" אינן מובאות בחשבון במהלך הדמיה של הדמיה של נתונים באחסון קבוע.
#XMSG: Message for generation of SQL Analyzer plan file without considering partitioning
PLANVIZ_WITHOUT_PARTITIONS=המחיצות של תצוגה "{0}" אינן מובאות בחשבון במהלך יצירה של קובץ תוכנית של מנתח SQL.
#XMSG: Confirmation message for mass removal of persisted data
MassRemovePersistency_Confirm_Msg=האם ברצונך להסיר את הנתונים באחסון קבוע ולהחליף את הגישה לנתונים חזרה לגישה וירטואלית?
#XMSG: Confirmation message for partial mass removal of selected persisted data
PartialMassRemovePersistency_Confirm_Msg=ל-{0} מתוך {1} התצוגות שנבחרו יש נתונים באחסון קבוע. \n האם ברצונך להסיר נתונים באחסון קבוע ולהחליף את הגישה לנתונים חזרה לגישה וירטואלית?
#XMSG: success message for stopping mass persistence
stopMassPersistenceSuccess=אנחנו מסירים את הנתונים באחסון קבוע עבור התצוגות שנבחרו.
#XMSG: error message during mass stop persistence of a view (backend error)
STOP_MASS_PERSISTENCE_ERROR=אירעה שגיאה במהלך עצירת האחסון הקבוע עבור התצוגות שנבחרו.
#XMSG: memory analysis is only performed in space {0}
NO_MEMORY_ANALYSIS=ניתוח הזיכרון מבוצע עבור ישויות במרחב "{0}" בלבד: "{1}""{2}" דולגו.
##XMSG: Message to indicate that Explain Plan cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_EXPLAINPLAN_NOT_FULFILLED=לא ניתן ליצור קובץ ''הסבר תוכנית'' עבור תצוגה "{0}" מכיוון שדרישות מקדימות של אחסון קבוע לא מולאו.
#XMSG: Message for Explain Plan generation without partitioning
EXPLAINPLAN_WITHOUT_PARTITIONS=המחיצות של תצוגה ''{0}'' אינן מובאות בחשבון במהלך יצירה של קובץ ''הסבר תוכנית''.
#XFLD: Message to indicate starting of process to generate Explain Plan file
START_EXPLAINPLAN_EXECUTION=מתחיל תהליך כדי ליצור קובץ ''הסבר תוכנית'' עבור תצוגה ''{0}''.
##XMSG: Message to indicate that Explain Plan was created
EXPLAINPLAN_CREATED=הקובץ ''הסבר תוכנית'' נוצר עבור התצוגה ''{0}''. ניתן להציגו בלחיצה על ''הצג פרטים'', או להוריד אותו אם ברשותך ההרשאה הרלוונטית.
##XMSG: Error message during  generation of Explain Plan file
EXPLAINPLAN_ERROR=אירעה שגיאה במהלך יצירה של קובץ ''הסבר תוכנית'' עבור תצוגה ''{0}''.
##XMSG: Explain Plan file cannot be generated for the view {0}, because too many views are stacked on each other
EXPLAINPLAN_MODEL_TOO_COMPLEX=לא ניתן ליצור את הקובץ ''הסבר תוכנית'' עבור התצוגה ''{0}''. יותר מדי תצוגות בערמה אחת על האחרת. מודלים מורכבים עשויים לגרום לשגיאות זיכרון וביצועים איטיים. מומלץ לאחסן את התצוגה באחסון קבוע.

##XMSG: Message to indicate that the space is locked
PERFORMANCE_ANALYSIS_LOCKED_SPACE_ERROR=לא ניתן לבצע ניתוח ביצועים עבור תצוגה ''{0}'', כיוון שמרחב ''{1}'' נעול.
##XMSG: The performance analysis task was cancelled
PERFORMANCE_ANALYSIS_CANCELED=ניתוח ביצועים עבור התצוגה "{0}" בוטל.
##XMSG: The performance analysis failed
PERFORMANCE_ANALYSIS_FAILED=ניתוח ביצועים נכשל.
##XMSG: The performance analysis is finished
PERFORMANCE_ANALYSIS_FINISHED=ניתוח ביצועים עבור התצוגה "{0}" הסתיים. הצג את התוצאה באמצעות לחיצה על"הצג פרטים".
##XMSG: The view has input parameter with no default value
INPUT_PARAMETER_WITHOUT_DEFAULT_VALUE=לא ניתן לנתחתצוגה זו באופן קבוע מכיוון שיש לה פרמטר חסר ערך ברירת מחדל.
##XMSG: The view is not fully deployed
VIEW_NOT_FULLY_DEPLOYED=לא ניתן לנתח תצוגה ו מכיוון שהיא לא נפרסה במלואה.
##XMSG: The view uses limited remote adapters
LIMITED_REMOTE_ADAPTER=תצוגה זו משתמשת במתאם מרוחק אחד לפחות עם יכולות מוגבלות כגון דחיפה של מסנן חסר או תמיכה ב'ספירה'. אחסון קבוע או שכפול אובייקטים יכול לשפר את ביצועי זמן הריצה.
##XMSG: The view uses remote adapter that does not support 'Limit'
REMOTE_ADAPTER_NOT_SUPPORTING_LIMIT=תצוגה זו משתמשת במתאם מרוחק אחד לפחות שאינו תומך ב'הגבל'. ייתכן שנבחרו מעל ל-1000 רשומות.
##XMSG: Performance analysis is executed by using default parameters of view'
INPUT_PARAMETER_WITH_DEFAULT_VALUE=ניתוח הביצועים מבוצע באמצעות ערכי ברירת המחדל של פרמטרי תצוגה.
##XMSG: Message to indicate that an error occurred
PERFORMANCE_ANALYSIS_ERROR=אירעה שגיאה במהלך ניתוח הביצועים של התצורה "{0}".
##XMSG: Message start of cancellation for Performance Analysis task
CANCEL_PERFORMANCE_ANALYSIS_TRIGGERED=נשלח תהליך להפסקת משימת ניתוח ביצועים {0} עבור תצוגה "{1}". ייתכן שהפסקת המשימה תתעכב.
##XMSG: Message start to indicate that task is not active
CANCEL_PERFORMANCE_ANALYSIS_NO_ACTIVE_TASK=משימת ניתוח ביצועים {0} עבור תצוגה "{1}" לא פעילה.
#XMSG: Cancel Performance Analysis task failure
CANCEL_PERFORMANCE_ANALYSIS_FAILED=ביטול משימת ניתוח ביצועים נכשל.

#XBUT: Assign schedule menu button label
assignScheduleLabel=הקצה לי תזמון
#XBUT: Pause schedule menu label
pauseScheduleLabel=הפסק תזמון
#XBUT: Resume schedule menu label
resumeScheduleLabel=חדש תזמון
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=אירעה שגיאה בעת הסרת התזמונים.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=אירעה שגיאה בעת הקצאת התזמונים.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=אירעה שגיאה בעת הפסקת התזמונים.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=אירעה שגיאה בעת חידוש התזמונים.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=מוחק {0} תזמונים
#XMSG: Message for starting mass assign of schedules
massAssignStarted=שינוי הבעלים של {0} תזמונים
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=משהה {0} תזמונים
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=ממשיך {0} תזמונים
#XBUT: Select Columns Button
selectColumnsBtn=בחר עמודות
#XFLD: Refresh tooltip
TEXT_REFRESH=רענן
#XFLD: Select Columns tooltip
text_selectColumns=בחר עמודות


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Runtime Metrics ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Label for Runtime Metrics
RuntimeMetricsTitle=מדדי זמן ריצה עבור
#XFLD : Label for Run Button
runButton=הפעל
#XFLD : Label for Cancel Button
cancelButton=בטל
#XFLD : Label for Close Button
closeButton=סגור
#XFLD : Label for Open View Analyzer Button
openViewAnalyzerButton=פתח את מנתח התצוגה
#XFLD : Label for Generate Explain Plan Button
generateExplainPlanButton=צור תוכנית הסבר
#XFLD : Label for Previous Run Column
previousRun=הפעלה קודמת
#XFLD : Label for Latest Run Column
latestRun=הפעלה אחרונה
#XFLD : Label for time Column
time=שעה
#XFLD : Label for Duration Column
duration=משך זמן
#XFLD : Label for Peak Memory Column
peakMemory=זיכרון שיא
#XFLD : Label for Number of Rows
numberOfRows=מספר שורות
#XFLD : Label for Overall Number of Sources
overallNumberOfSources=מספר המקורות הכולל
#XFLD : Label for Data Access Column
dataAccess=גישה לנתונים
#XFLD : Label for Local Tables
localTables=טבלות מקומיות
#XFLD : Label for federatedRemoteTables
federatedRemoteTables=טבלאות מרוחקות מאוחדות (עם יכולות מתאם מוגבלות)
#XTXT Text for initial state of the runtime metrics
initialState=עליך קודם להפעיל את ניתוח הביצועים כדי לקבל את המדדים. פעולה זו עשויה לארוך זמן מה, אבל ניתן לבטל את התהליך אם יש צורך.
#XMSG: Cancel confirmation message
CancelRunConfirmationMessage=בטוח שברצונך לבטל את ההפעלה הנוכחית של ניתוח הביצועים?
#XTIT: Cancel dialog title
CancelRunTitle=בטל את ההפעלה
#XFLD: Label for Number of Rows
NUMBER_ROWS=מספר שורות
#XFLD: Label for Overall Number of Sources
OVERALL_NUMBER_SOURCES=מספר המקורות הכולל
#XFLD: Label for Data Access
DATA_ACCESS=גישה לנתונים
#XFLD: Label for Remote tables
NUMBER_REMOTE_TABLES_LIMITED_ADAPTERS=טבלאות מרוחקות מאוחדות (עם יכולות מתאם מוגבלות)
#XFLD: Label for select statement
SELECT_STATEMENT='SELECT * FROM VIEW LIMIT 1000'
#XFLD: Label for duration
SELECT_RUNTIME=משך זמן
#XFLD: Label for peak memory
SELECT_PEAK_MEMORY=זיכרון שיא
#XFLD: Label for count statement
SELECT_COUNT_STATEMENT='SELECT COUNT(*) FROM VIEW'
#XFLD: Label for count duration
SELECT_COUNT_RUNTIME=משך זמן
#XFLD: Label for count peak memory
SELECT_COUNT_PEAK_MEMORY=זיכרון שיא
#XFLD: Label for count of number of local tables
NUMBER_LOCAL_TABLES_FILE=טבלאות מקומיות (קובץ)
#XTXT: Text for running state of the runtime metrics
Running=פועל...
#XFLD: Label for time
Time=שעה
#XFLD: Label for virtual access
PA_VIRTUAL=וירטואלי
#XFLD: Label for persisted access
PA_PERSISTED=מאוחסן באחסון קבוע
PA_PARTIALLY_PERSISTED=אוחסן באחסון קבוע באופן חלקי
#XTXT: Text for cancel
CancelRunSuccessMessage=מבטל את ההפעלה של ניתוח הביצועים.
#XTXT: Text for cancel error
CancelRunErrorMessage=אירעה שגיאה בעת ביטול ההפעלה של ניתוח הביצועים.
#XTXT: Text for explain plan generation
ExplainPlanStarted=יוצר ''הסבר תוכנית'' עבור תצוגה ''{0}''.
#XTXT: Text for performance analysis
PerformanceAnalysisStarted=מתחיל בניתוח הביצועים של התצוגה "{0}".
#XTXT: Text for performance analysis error
ErrorFetchingPerformanceAnalysis=אירעה שגיאה בעת הבאת נתוני ניתוח הביצועים.
#XTXT: Text for performance analysis error
conflictingTask=משימת ניתוח הביצועים כבר פועלת
#XFLD: Label for Errors
Errors=שגיאות
#XFLD: Label for Warnings
Warnings=אזהרות
#XFLD: Tooltip for missing privileges
openViewAnalyzerButtonTooltipNoPrivilege=אתה צריך הרשאת DWC_DATAINTEGRATION(עדכון) כדי לפתוח את תצוגת המנתח.
#XFLD: Tooltip for missing privileges
generateExplainPlanButtonTooltipNoPrivilege=אתה צריך הרשאת DWC_RUNTIME(קריאה) כדי ליצור 'הסבר תוכנית'.



#XFLD: Label for frequency column
everyLabel=כל
#XFLD: Plural Recurrence text for Hour
hoursLabel=שעות
#XFLD: Plural Recurrence text for Day
daysLabel=ימים
#XFLD: Plural Recurrence text for Month
monthsLabel=חודשים
#XFLD: Plural Recurrence text for Minutes
minutesLabel=דקות
