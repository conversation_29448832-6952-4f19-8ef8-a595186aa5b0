
#XBCB: Breadcrumb
viewMonitorBreadcrumb={spaceId}

#XFLD: Label for persisted view column
SOURCE=Izvor
#XFLD: Label for persisted view column
NAME=Ime
#XFLD: Label for persisted view column
NAME_LABEL=Poslovno ime
#XFLD: Label for persisted view column
NAME_LABELNew=Obje<PERSON> (poslovno ime)
#XFLD: Label for persisted view column
TECHINCAL_NAME=Tehnično ime
#XFLD: Label for persisted view column
TECHINCAL_NAMENew=Objekt (tehnično ime)
#XFLD: Label for persisted view column
DATA_PERSISTENCY=Dostop do podatkov
#XFLD: Label for persisted view column
STATUS=Status
#XFLD: Label for persisted view column
LAST_UPDATED=Nazadnje posodobljeno
#XFLD: Label for persisted view column
IN_MEMORY_SIZE=Zasedeni pomnilnik za shrambo (MiB)
#XFLD: Label for persisted view column
DISK_SIZE=Zasedeni disk za shrambo (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_NEWW=Velikost v pomnilniku (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_HEADER=Velikost v pomnilniku
#XFLD: Label for persisted view column
DISK_SIZE_NEWW=Velikost na disku (MiB)
#XFLD: Label for persisted view column
DISK_SIZE_HEADER=Velikost na disku
#XFLD: Label for schedule owner column
txtScheduleOwner=Lastnik časovnega načrta
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Prikaže, kdo je ustvaril časovni načrt
#XFLD: Label for persisted view column
PERSISTED=Trajno
#XFLD: Label for persisted view column
TYPE=Tip
#XFLD: Label for View Selection Dialog column
changedOn=Spremenjeno dne
#XFLD: Label for View Selection Dialog column
createdBy=Ustvaril
#XFLD: Label for log details column
txtViewPersistencyLogs=Prikaz dnevnikov
#XFLD: Label for log details column
txtViewPersistencyLogsNew=Podrobnosti
#XFLD: text for values shown for Ascending sort order
SortInAsc=Naraščajoče razvrščanje
#XFLD: text for values shown for Descending sort order
SortInDesc=Padajoče razvrščanje
#XFLD: Label for persisted view monitor heading
ViewMonitor_Heading=Nadzor pogledov
#XFLD: Label for persisted view monitor sub-heading
ViewMonitor_SubHeading=Nadzor in vzdrževanje trajnega shranjevanja podatkov pogledov


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ DynamicPageHeader ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: text for values shown in column Persistence Status
txtLoading=Nalaganje
#XFLD: text for values shown in column Persistence Status
txtRunning=Se izvaja
#XFLD: text for values shown in column Persistence Status
txtAvailable=Na voljo
#XFLD: text for values shown in column Persistence Status
txtError=Napaka
#XMSG: Error message for unknown Persistence type that gets logged in Kibana
unknownReplicationTypeError=Vrsta podvajanja ''{0}'' ni podprta.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Content ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Nastavitve, uporabljene za zadnje izvajanje trajnega shranjevanja podatkov:
#XMSG: Message for input parameter name
inputParameterLabel=Vhodni parameter
#XMSG: Message for input parameter value
inputParameterValueLabel=Vrednost
#XMSG: Message for persisted data
inputParameterPersistedLabel=Trajno shranjeno ob
#XTIT: Count for View in ViewMonitor Toolbar
txtViewCount=Pogledi ({0})
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Prikaz trajnosti
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Trajnost podatkov
#XBUT: Button to clear the selected view persistance
ClearViewPersistance=Počisti
#XBUT: Button to stop the selected view persistance
stopPersistance=Zaustavi trajnost
#XFLD: Placeholder for Search field
txtSearch=Išči
#XBUT: Tooltip for refresh button
txtRefresh=Osveži
#XBUT: Tooltip for add view button
txtDeleteView=Izbriši trajno shranjevanje
#XBUT: Tooltip for load new peristence
loadNewPersistence=Znova zaženi trajno shranjevanje
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Prenesi nov trenutni posnetek
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Začetek trajnosti podatkov
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Odstrani trajne podatke
#XMSG: success message for starting persistence
startPersistenceSuccess=Trajno shranjevanje pogleda "{0}" je v teku.
#XMSG: success message for stopping persistence
stopPersistenceSuccess=Odstranjevanje trajnih podatkov za pogled "{0}" je v teku.
#XMSG: success message for stopping persistence
deletePersistedViewSuccess=Odstranjevanje pogleda ''{0}'' s seznama nadzora je v teku.
#XMSG: error message during start persistence of a view (backend error)
ERROR_STARTING_PERSISTENCE=Pri zagonu trajnega shranjevanja podatkov za pogled "{0}" je prišlo do napake.
#XMSG: error message during start persistence of a view (backend error)
PARAMERROR_STARTING_PERSISTENCE=Pogleda ''{0}'' ni mogoče trajno shraniti, ker ima parametre vnosa.
#XMSG: error message during start persistence of a view (backend error)
NUMBER_OF_PARAMETER_STARTING_PERSISTENCE=Pogleda ''{0}'' ni mogoče trajno shraniti, ker ima več kot en parameter vnosa.
#XMSG: error message during start persistence of a view (backend error)
PARAMETER_DEFAULT_VALUE_STARTING_PERSISTENCE=Pogleda ''{0}'' ni mogoče trajno shraniti, ker parameter vnosa nima prvizete vrednosti.
#XMSG: error message during start persistence of a view (backend error)
REDEPLOYMENT_DAC_REQUIRED=Da podprete trajno shranjevanje podatkov, morate znova postaviti kontrolnik za dostop do podatkov ''{0}''.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_STARTING_PERSISTENCE=Pogleda ''{0}'' ni mogoče trajno shraniti, ker uporablja pogled ''{1}'' s kontrolnikom za dostop do podatkov.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_EXTERNAL_STARTING_PERSISTENCE=Pogleda ''{0}'' ni mogoče trajno shraniti, ker uporablja pogled s kontrolnikom za dostop do podatkov, ki pripada drugemu prostoru.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_STRUCTURE_STARTING_PERSISTENCE=Pogleda ''{0}'' ni mogoče trajno shraniti, ker struktura enega ali več njegovih kontrolnikov za dostop do podatkov ne podpira trajnega shranjevanja podatkov.
#XMSG: error message during stop persistence of a view (backend error)
STOP_PERSISTENCE_ERROR=Pri zaustavitvi trajnega shranjevanja za pogled "{0}" je prišlo do napake.
#XMSG: error message during stop persistence of a view (backend error)
DELETE_PERSISTED_VIEW_ERROR=Pri izbrisu trajno shranjenega pogleda ''{0}'' je prišlo do napake.
#XMSG: Warning message for removing the persisted data
RemovePersistency_Confirm_Msg=Želite izbrisati trajno shranjene podatke in preklopiti na navidezni dostop pogleda "{0}"?
#XMSG: Warning message for delete the view persistence
DeletePersistency_Confirm_Msg=Želite odstraniti pogled s seznama nadzora in izbrisati trajno shranjene podatke pogleda ''{0}''?
#XMSG: error message for reading data from backend
txtReadBackendError=Očitno je prišlo do napake pri branju podatkov iz zaledja.
#XFLD: Label for No Data Error
NoDataError=Napaka
#XMSG: message for conflicting task
Task_Already_Running=Naloga v sporu se že izvaja za pogled "{0}".

#XFLD: Error while accessing pertitioning for a view
missingPrivilegeInViewPersistency=Nezadostno dovoljenje za izvedbo particioniranja za pogled ''{0}''

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Show All Views~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Text for show all views button
allViewsText=Vsi pogledi ({0})
#XBUT: Text for show scheduled views button
scheduledText=Načrtovano ({0})
#XBUT: Text for show persisted views button
persistedText=Trajno ({0})
#XBUT: Text for start analyzer button
startAnalyzer=Zaženi analizator pogleda
#XFLD: Message if repository is unavailable
repositoryErrorMsg=Odložišče ni na voljo in določene funkcije so onemogočene.

#XFLD: Data Access - Virtual
Virtual=Navidezno
#XFLD: Data Access - Persisted
Persisted=Trajno

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Non-Persisted View Selection Dialog~~~~~~~~~~~~~~~~~~~~~~~

#XTIT: Title for Non-Persisted View selection dialog
select_source=Izberite pogled za trajnost

#XTIT: Placeholder for Non-Persisted View selection dialog search
searchView=Iskanje pogledov
#XTIT: No data in the list of non-persisted view
No_Data=Ni podatkov
#XBUT: Button to select non-persisted view
ok=V redu
#XBUT: Button to close the non-persisted views selection dialog
cancel=Prekliči

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~View persistency log message key~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message for view persistency logs
START_STOP_PERSISTENCY=Zagon izvajanja naloge trajnega shranjevanja podatkov za ''{1}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY=Trajno shranjevanje podatkov za pogled ''{1}''.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY)
START_VIEW_PERSISTENCY_PROCESS=Zagon postopka za trajno shranjevanje podatkov za pogled "{0}".
#XFLD: Message for view persistency manual partition load
START_VIEW_PERSISTENCY_PROCESS_MANUAL_PARTITIONS=Zagon postopka za trajno shranjevanje podatkov za pogled ''{0}''z ID-ji izbrane particije: ''{1}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY=Odstranjevanje trajno shranjenih podatkov za pogled "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY)
REMOVE_VIEW_PERSISTENCY=Zagon postopka za odstranjevanje trajno shranjenih podatkov za pogled "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_SUCCESS=Podatki so trajno shranjeni za pogled "{1}".
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_SUCCESS)
START_VIEW_PERSISTENCY_PROCESS_SUCCESS=Podatki so trajno shranjeni za pogled "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY_SUCCESS=Trajno shranjeni podatki so odstranjeni, navidezni dostop do podatkov pa je obnovljen za pogled "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_SUCCESS)
REMOVE_VIEW_PERSISTENCY_SUCCESS=Dokončanje postopka za odstranjevanje trajno shranjenih podatkov za pogled "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_FAILED=Podatkov za pogled "{1}" ni mogoče trajno shraniti.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_FAILED)
START_VIEW_PERSISTENCY_PROCESS_FAILED=Podatkov za pogled "{0}" ni mogoče trajno shraniti.
#XFLD: Message for view persistency logs (for messages created before version 2022.05)
STOP_VIEW_PERSISTENCY_FAILED=Trajno shranjenih podatkov ni mogoče odstraniti za pogled ''{1}''.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_FAILED)
REMOVE_VIEW_PERSISTENCY_FAILED=Trajno shranjenih podatkov ni mogoče odstraniti za pogled ''{0}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_SUCCESS="{3}" zapisov je trajno shranjenih za pogled ''{1}''.
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace START_PERSISTENCY_RECORD_COUNT_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS={0} zapisov je vstavljenih v tabelo trajnega shranjevanja podatkov za pogled ''{1}''.
#XFLD: Message used by SEAL repository for view persistency logs (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS_V2={0} zapisov je vstavljenih v tabelo trajnega shranjevanja podatkov za pogled ''{1}''. Uporabljeni pomnilnik: {2} GiB.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_SUCCESS="{3}" trajno shranjenih zapisov je odstranjenih za pogled ''{1}''.
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace STOP_PERSISTENCY_RECORD_COUNT_SUCCESS)
REMOVE_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=Trajno shranjeni podatki so odstranjeni, ''{0}'' trajno shranjenih zapisov je izbrisanih.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_FAILED=Priklic recordCount ni uspel za pogled ''{1}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_FAILED=Priklic recordCount ni uspel za pogled ''{1}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Časovni načrt je izbrisan za ''{1}''.
#XFLD: Message for view persistency logs (new message to replace STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS)
REMOVE_VIEW_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Časovni načrt je izbrisan za pogled ''{0}''.
#XFLD: Message for view persistency logs
STOP_PERSISTENCY_DELETE_SCHEDULE_FAILED=Izbris časovnega načrta za "{1}" ni uspel.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_REDEPLOY=Pogleda ''{0}'' ni mogoče trajno shraniti, ker je bil v času od začetka trajnega shranjevanja spremenjen in postavljen. Poskusite znova trajno shraniti pogled ali počakajte na naslednje načrtovano izvajanje.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_DELETE=Pogleda ''{0}'' ni mogoče trajno shraniti, ker je bil v času od začetka trajnega shranjevanja izbrisan.
#XFLD: Message for successful data persistency of a single partition (for messages created before version 2022.03)
START_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} zapisov je trajno shranjenih v particijo za vrednosti ''{1}'' <= {2} < ''{3}''.
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (new message to replace START_PERSISTENCY_SINGLE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} zapisov je vstavljenih v particijo za vrednosti ''{1}'' <= {2} < ''{3}''.
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS_V2={0} zapisov je vstavljenih v particijo za vrednosti ''{1}'' <= ''{2}'' < ''{3}''. Uporabljeni pomnilnik: {4} GiB.
#XFLD: Message for successful replication of a "others" partition (for messages created before version 2022.03)
START_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} zapisov je trajno shranjenih v particijo ''drugi''.
#XFLD: Message used by SEAL repository for successful replication of a "others" partition (new message to replace START_PERSISTENCY_OTHERS_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} zapisov je vstavljenih v particijo ''drugi''.
#XFLD: Message for view persistency record count of partitions (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={3} zapisov je trajno shranjenih za pogled ''{1}'' v {4} particijah.
#XFLD: Message used by SEAL repository for view persistency record count of partitions (new message to replace START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={0} zapisov je vstavljenih v tabelo trajnega shranjevanja podatkov za pogled ''{1}'' v {2} particijah.
#XFLD: Message used by SEAL repository for view persistency for partition wise refresh
START_VIEW_PERSISTENCY_RECORD_COUNT_REFRESHED_PARTITIONS_SUCCESS={0} zapisov je vstavljenih v tabelo trajnega shranjevanja podatkov za pogled ''{1}''. Posodobljene particije: {2}, zaklenjene particije: {3}, skupno particij: {4}
#XFLD: Message used by SEAL repository for view persistency for manual partition load
START_VIEW_PERSISTENCY_RECORD_COUNT_MANUAL_PARTITIONS_SUCCESS={0} zapisov je vstavljenih v tabelo trajnega shranjevanja podatkov za pogled ''{1}'' v {2} izbranih particijah
#XFLD: Message used by SEAL repository for view persistency for locked partition data load
START_VIEW_PERSISTENCY_RECORD_COUNT_LOCKED_PARTITIONS_SUCCESS={0} zapisov je vstavljenih v tabelo trajnega shranjevanja podatkov za pogled ''{1}''. Posodobljene particije: {2}, zaklenjene in nespremenjene particije: {3}, skupno particij: {4}
#XMSG: Message for unexpected error (for messages created before version 2022.03)
START_PERSISTENCY_UNEXPECTED_ERROR=Med trajnim shranjevanjem podatkov za pogled ''{0}'' je prišlo do nepričakovane napake.
#XMSG: Message for unexpected error (new message to replace START_PERSISTENCY_UNEXPECTED_ERROR)
START_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Med trajnim shranjevanjem podatkov za pogled ''{0}'' je prišlo do nepričakovane napake.
#XMSG: Message for Locked space error
START_VIEW_PERSISTENCY_LOCKED_SPACE_ERROR=Pogleda ''{0}''’ ni mogoče trajno shraniti, ker je prostor ''{1}'' zaklenjen.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
STOP_PERSISTENCY_UNEXPECTED_ERROR=Med odstranjevanjem trajno shranjenih podatkov za pogled ''{0}'' je prišlo do nepričakovane napake.
#XMSG: Message for unexpected error (new message to replace STOP_PERSISTENCY_UNEXPECTED_ERROR)
REMOVE_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Med odstranjevanjem trajnega shranjevanja za pogled ''{0}'' je prišlo do nepričakovane napake.
#XMSG: Message for ivalidated view
VIEW_PERSISTENCY_ABORTED_DEFINITION_INVALID=Opredelitev pogleda ''{0}'' je postala neveljavna, najverjetneje zaradi spremembe objekta, ki ga neposredno ali posredno porablja pogled. Da odpravite težavo, poskusite znova postaviti pogled ali ugotovite vzrok težave.
#XMSG: Message for persisted data removal (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT=Trajno shranjeni podatki so odstranjeni pri postavljanju pogleda ''{0}''.
#XMSG: Message for persisted data removal of a consuming view
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW=Trajno shranjeni podatki so odstranjeni pri postavljanju porabljenega pogleda ''{0}''.
#XMSG: Message for persisted data removal of a consuming view due to a dac change
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW_DAC_CHANGED=Trajno shranjeni podatki so odstranjeni pri postavljanju porabljenega pogleda ''{0}'', ker je bil kontrolnik za dostop do podatkov spremenjen.
#XMSG: Message for persisted data removal (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DEPLOYMENT=Trajno shranjeni podatki so odstranjeni pri postavljanju pogleda ''{0}''.
#XMSG: Message for deleted view (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION=Z izbrisom pogleda ''{0}'' je trajno shranjevanje odstranjeno.
#XMSG: Message for deleted view (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DELETION=Z izbrisom pogleda ''{0}'' je trajno shranjevanje odstranjeno.
#XMSG Message for removal of persisted data, because prerequisites not fulfilled
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_PREREQUISITES_NOT_FULFILLED=Trajni podatki so odstranjeni, ker predpogoji za trajnost podatkov niso več izpolnjeni.
#XMSG: Message for inconsistent persistency
VIEW_PERSISTENCY_INCONSISTENT_PERSISTENCY=Trajno shranjevanje pogleda ''{0}'' je postalo nekonsistentno. Da odpravite težavo, odstranite trajno shranjene podatke.
#XMSG: Message for prerequisites
START_PERSISTENCY_CHECK_PREREQUISITES=Preverjanje pogojev za trajno shranjevanje pogleda ''{0}''.
#XMSG: Message for DAC deployed in deprecated way using table function and join (for messages created before 2025.05)
START_PERSISTENCY_DEPRECIATED_DAC=Pogled "{0}" je postavljen s kontrolnikom za dostop do podatkov (DAC), ki bo kmalu zastarel. Znova postavite pogled, da izboljšate učinkovitost.
#XMSG: Message for DAC deployed in deprecated way using table function and join
START_PERSISTENCY_DEPRECATED_DAC=Pogled "{0}" je postavljen s kontrolnikom za dostop do podatkov (DAC), ki bo kmalu zastarel. Znova postavite pogled, da izboljšate učinkovitost.
#XMSG: Message for roll back (for messages created before version 2022.03)
ROLLBACK_VIEW_PERSISTENCY=Prišlo je do napake. Prejšnji status trajnega shranjevanje je obnovljen za pogled ''{0}''.
#XMSG: Message used by SEAL repository for rollback of running procedure to persist view (new message to replace ROLLBACK_VIEW_PERSISTENCY)
VIEW_PERSISTENCY_ROLLBACK=Prišlo je do napake. Postopek trajnosti za pogled ''{0}'' je bil zaustavljen in spremembe so bile povrnjene na prejšnje stanje.
#XMSG: Message used by SEAL repository for rollback of running procedure to remove persisted data of view
REMOVE_VIEW_PERSISTENCY_ROLLBACK=Prišlo je do napake. Postopek za odstranitev trajnih podatkov pogleda ''{0}'' je bil zaustavljen in spremembe so bile povrnjene na prejšnje stanje.
#XMSG: Message used by SEAL repository for persistency preparation
START_PERSISTENCY_PREPARATION=Priprava trajnega shranjevanja podatkov.
#XMSG: Message used by SEAL repository for data insertion
START_INSERTING_PERSISTENCY_DATA=Vstavljanje podatkov v tabelo trajnosti.
#XMSG: Message for null values - partitioning (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS={0} zapisov ničelne vrednosti je vstavljenih v particijo ''drugi''.
#XMSG: Message used by SEAL repository for others partition values - partitioning (new message to replace START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS={0} zapisov je vstavljenih v particijo ''drugi'' za vrednosti ''{2}'' < ''{1}'' ALI ''{2}'' >= ''{3}''.
#XMSG: Message used by SEAL repository for others partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS_V2={0} zapisov je vstavljenih v particijo ''drugi'' za vrednosti ''{2}'' < ''{1}'' ALI ''{2}'' >= ''{3}''. Uporabljeni pomnilnik: {4} GiB.
#XMSG: Message used by SEAL repository for null partition values - partitioning
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS={0} zapisov je vstavljenih v particijo ''drugi'' za vrednosti ''{1}'' JE NIČELNA VREDNOST.
#XMSG: Message used by SEAL repository for null partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS_V2={0} zapisov je vstavljenih v particijo ''drugi'' za vrednosti ''{1}'' JE NIČELNA VREDNOST. Uporabljeni pomnilnik: {2} GiB.
##XFLD:Message used by SEAL repository for remote statistics
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS=Prenos vpletenih podatkov: {0} oddaljene izjave. Skupno število priklicanih zapisov: {1}. Skupni čas trajanja: {2} sekund.
##XFLD:Message used by SEAL repository for remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS_WITH_PARTITIONS=Prenos vpletenih podatkov s {0} particijami z {1} oddaljenimi izjavami. Skupno število priklicanih zapisov: {2}. Skupni čas trajanja: {3} sekund.
##XFLD:Message used by SEAL repository with UI ID for instructions of remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_INSTRUCTION=Oddaljene izjave, obdelane pri izvajanju, lahko prikažete v podrobnostih sporočil, specifičnih za particijo, tako da odprete monitor oddaljenih poizvedb.
##XFLD:Message for start reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS=Zagon postopka za vnovično uporabo obstoječih trajno shranjenih podatkov za pogled {0} po postavitvi.
##XFLD:Message used by SEAL repository for reattach view persitency
START_VIEW_PERSISTENCY_REATTACHMENT=Zagon vnovične uporabe obstoječih trajno shranjenih podatkov.
##XFLD:Message used by SEAL repository for reattach view persitency
START_FINILIZING_VIEW_PERSISTENCY_REATTACHMENT=Vnovična uporaba obstoječih trajno shranjenih podatkov.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_COMPLETE=Postopek vnovične uporabe obstoječih trajno shranjenih podatkov za pogled {0} je dokončan.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_FAILED=Vnovična uporaba obstoječih trajno shranjenih podatkov za pogled {0} po postavitvi ni uspela.
#XMSG: Message used by SEAL repository for finalizing persistency
START_FINALIZING_PERSISTENCY=Dokončanje trajnega shranjevanja.
#XMSG: Message used by SEAL repository for restoring virtual access for view
REMOVE_VIEW_PERSISTENCY_VIRTUAL_ACCESS_RESTORED=Navidezni dostop do podatkov je obnovljen za pogled ''{0}''.
#XMSG: Message used by SEAL repository for view that already has virtual access
REMOVE_VIEW_PERSISTENCY_ALREADY_VIRTUAL_ACCESS=Pogled ''{0}'' že ima navidezni dostop do podatkov. Nobeni trajni podatki niso bili odstranjeni.
#XMSG: Message for removing view persistency
VIEW_PERSISTENCY_REMOVED_FROM_MONITOR=Pogled ''{0}'' je bil odstranjen iz nadzora pogledov.
#XMSG: Message for view doesnt exist.
VIEW_PERSISTENCY_VIEW_NOT_EXISTING=Pogled "{0}" ne obstaja v bazi podatkov ali pa ni pravilno postavljen, zato ga ni mogoče ohraniti. Poskusite znova postaviti pogled, da odpravite težavo, ali pa poiščite glavni vzrok težave.
#XMSG: Message for persistency not enabled.
VIEW_PERSISTENCY_NOT_ENABLED=Trajno shranjevanje podatkov ni omogočeno. Znova postavite tabelo/pogled v prostor ''{0}'', da omogočite funkcijo.
#XMSG: Message for unexpected error due to node crash
VIEW_PERSISTENCY_UNEXPECTED_ERROR_GENERAL=Zadnje izvajanje trajnosti pogleda je bilo prekinjeno zaradi tehničnih težav.
#XFLD: Message for view persistency memory consumption
VIEW_PERSISTENCY_MEMORY_CONSUMPTION={0} GiB največje količine pomnilnika, uporabljenega v času izvajanja trajnosti pogleda.
#XFLD: Message for view persistency timeout
VIEW_PERSISTENCY_TIMEOUT=Trajnost pogleda {0} je dosegla časovno omejitev {1} ur.
#XFLD: Message for not started asynchronous view persistency
VIEW_PERSISTENCY_ASYNC_NOT_STARTED=Visoka obremenjenost sistema je preprečila zagon asinhrone izvedbe trajnosti pogleda. Preverite, ali se vzporedno izvaja preveč nalog.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency (for messages created before version 2022.05)
START_VIEW_PERSISTENCY_EXECUTION_METHOD_DROP_AND_RENAME=Obstoječa trajno shranjena tabela je bila izbrisana in zamenjana z novo trajno shranjeno tabelo.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_RECREATE_AND_DROP=Obstoječa trajno shranjena tabela je bila izbrisana in zamenjana z novo trajno shranjeno tabelo.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_TRUNCATE_AND_COPY=Obstoječa trajno shranjena tabela je bila posodobljena z novimi podatki.
##XFLD: Message for missing authorizations for view persistency
VIEW_PERSISTENCY_MISSING_AUTH=Pooblastila za trajno shranjevanje podatkov manjkajo.
##XFLD: Message start of persistency cancellation
VIEW_PERSISTENCY_TRIGGER_CANCEL=Začetek preklica procesa trajnosti pogleda {0}.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_UNAVAILABLE_TASK_RUN=Postopka za trajno shranjevanje pogleda ni uspelo preklicati, ker za pogled {0} ni naloge trajnega shranjevanja podatkov v teku.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_NONDETERMINISTIC_TASK_RUN=Postopka za trajno shranjevanje pogleda ni uspelo preklicati, ker se naloga trajnega shranjevanja podatkov ne izvaja za pogled {0}.
##XFLD: Message for canceling a non running persitency task
VIEW_PERSISTENCY_CANCEL_NON_RUNNING_SELECTED_TASK=Postopka za trajno shranjevanje pogleda {0} ni uspelo preklicati, ker se izbrana naloga trajnega shranjevanja podatkov {1} ne izvaja.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_UNREACHABLE_PERSISTENCY_RUN=Preklic procesa trajnosti pogleda ni uspel, ker se trajnost podatkov za pogled {0} še ni začela.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_COMPLETED_PERSISTENCY_RUN=Preklic procesa trajnosti pogleda {0} ni uspel, ker je bil že dokončan.
##XFLD: Message for failed cancellation
VIEW_PERSISTENCY_ABORTED_CANCEL_EXECUTION=Preklic procesa trajnosti pogleda {0} ni uspel.
##XFLD: Message for finalizing persistency cancellation
VIEW_PERSISTENCY_FINALIZE_CANCEL=Postopek za zaustavitev trajnega shranjevanja podatkov pogleda {0} je bil oddan.
##XFLD: Message used in persist task when cancellation is caught
VIEW_PERSISTENCY_CANCELED=Proces za trajno shranjevanje pogleda {0} je bil zaustavljen.
##XFLD: Message used in persist task when cancelation is caught and performed through a cancelation task
VIEW_PERSISTENCY_CANCELED_VIA_CANCEL_TASK=Proces za trajno shranjevanje pogleda {0} je bil zaustavljen z nalogo preklica {1}.
##XFLD: Message used in cancel task when the task is triggered by deployment
VIEW_PERSISTENCY_CANCEL_TRIGGERED_BY_DEPLOYMENT=Preklic procesa trajnega shranjevanja podatkov med postavljanjem pogleda {0}.
##XFLD: Message used in case the cancelation is retriggered for a running task
VIEW_PERSISTENCY_CANCEL_ATTEMPT_RETRIGGER_CANCEL=Prejšnja naloga preklica za trajno shranjevanje pogleda {0} je že oddana.
##XFLD: Message used to express the delay that may occur before the persistency task is stopped
VIEW_PERSISTENCY_CANCEL_DELAY_STOPPING_PERSISTENCY=Naloga trajnega shranjevanja podatkov za pogled {0} se lahko zaustavi z zakasnitvijo.
##XFLD: Message used to reflect the task log identifier of the current running persistency task
VIEW_PERSISTENCY_CANCEL_CURRENT_RUNNING_TASK=Podatki za pogled {0} se trajno shranjujejo z nalogo {1}.
##XFLD:Message used by SEAL repository for locked partitions and DACs
VIEW_PERSISTENCY_LOCKED_PARTITIONS_DACS=Pooblastila, ki so jih zagotovili kontrolniki za dostop do podatkov, so se morda spremenila in jih zaklenjene particije ne upoštevajo. Odklenite particije in prenesite nov trenutni posnetek, da uvedete spremembe.
##XFLD:Message used by SEAL repository for column structure change
VIEW_PERSISTENCY_COLUMN_STRUCTURE_CHANGE=Struktura stolpca je spremenjena in se ne ujema več z obstoječo tabelo trajnosti. Odstranite trajne podatke in zaženite novo trajnost podatkov, da posodobite tabelo trajnosti.
##XFLD: Message for out of memory
VIEW_PERSISTENCY_HANA_OUT_OF_MEMORY=Naloga ni uspela zaradi napake pomanjkanja pomnilnika v zbirki podatkov SAP HANA.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_EXCEPTION_TRANSACTION_ROLLBACK=Naloga ni uspela zaradi notranje izjeme v bazi podatkov SAP HANA.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_SQL_EXECUTION_ISSUE=Naloga ni uspela zaradi notranje težave pri izvedbi SQL v bazi podatkov SAP HANA.
##XFLD: Message for HANA out of memory result
VIEW_PERSISTENCY_OUT_OF_MEMORY_RESULT_MESSAGE=Razlog za dogodek pomanjkanja pomnilnika HANA: {0}
##XFLD: Message for admission control rejection
VIEW_PERSISTENCY_HANA_ADMISSION_CTRL_REJECTION=Naloga ni uspela zaradi zavrnitve nadzora sprejema SAP HANA.
##XFLD: Message for HANA back pressure / rate limit of HANA client
VIEW_PERSISTENCY_HANA_CLIENT_RATE_LIMIT_REJECTION=Naloga ni uspela zaradi preveč aktivnih povezav SAP HANA.
#XMSG: Message for invalid persisted table name
VIEW_PERSISTENCY_PERSISTED_TABLE_INVALID=Prišlo je do napake in trajno shranjena tabela je postala neveljavna. Če želite rešiti težavo, odstranite trajno shranjene podatke in znova shranite pogled.
#XMSG: Message for usahe of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION=Pogleda ni mogoče ohraniti. Uporablja oddaljeno tabelo, ki temelji na oddaljenemu viru z omogočenim oddaljenim propagiranjem uporabnikov. Preverite poreklo pogleda.
#XMSG: Message for dynamic usage of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION_DYNAMIC=Pogleda ni mogoče ohraniti. Uporablja oddaljeno tabelo, ki temelji na oddaljenemu viru z omogočenim oddaljenim propagiranjem uporabnikov. Oddaljena tabela je morda uporabljena dinamično prek pogleda skripta SQL. Poreklo pogleda morda ni prikazano v oddaljeni tabeli.
#XMSG: Message for insufficient privileges or consumption of view with DAC via dynamic SQL
VIEW_PERSISTENCY_INSUF_PRIV_OR_DAC_DYN_SQL=Morda nimate ustreznih pravic. Odprite predogled podatkov, da prikažete, ali imate zahtevane pravice. Če jih imate, je za drugi pogled, prikazan prek dinamičnega skripta SQL, morda omogočen nadzor dostopa do podatkov (DAC).
#XMSG: deployment of DAC is deprecated and view need to be redeployed to be able to persist data
VIEW_PERSISTENCY_DEPRECATED_DAC=Pogled "{0}" je postavljen s kontrolnikom za dostop do podatkov (DAC), ki bo kmalu zastarel. Znova postavite pogled, da bo mogoče trajno shraniti podatke za pogled.
##XFLD:Message used by SEAL repository for input parameter
START_VIEW_PERSISTENCY_INPUT_PARAMETER=Uporaba privzete vrednosti "{0}" za vhodni parameter "{1}".
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Elastic compute node keys~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
##XFLD:Message used to reflect disablement of ecn replica
ECN_DISABLE_EXISTING_REPLICA=Replika vozlišča za elastično računanje je onemogočena.
##XFLD:Message used to reflect recreation of ecn replica
ECN_RECREATE_EXISTING_REPLICA=Replika vozlišča za elastično računanje je znova ustvarjena.
##XFLD:Message used to reflect re-enablement of ecn replica
ECN_ENABLE_EXISTING_REPLICA=Replika vozlišča za elastično računanje je znova omogočena.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Task Schedule Menu key~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message to schedule task
scheduleText=Časovni načrt
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Ustvari časovni načrt
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Uredi časovni načrt
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Izbriši časovni načrt
#XFLD: Refresh frequency field
refreshFrequency=Pogostost osvežitve
#XFLD: Refresh frequency field
refreshFrequencyNew=Pogostost
#XFLD: Refresh frequency field
refreshFrequencyNewNew=Načrtovana pogostost
#XBUT: label for None
none=Brez
#XBUT: label for Real-Time replication state
realtime=V dejanskem času
#XFLD: Label for table column
txtNextSchedule=Naslednje izvajanje
#XFLD: Label for table column
txtNextScheduleNew=Načrtovano naslednje izvajanje
#XFLD: Label for table column
txtNumOfRecords=Število zapisov
#XFLD: Label for scheduled link
scheduledTxt=Načrtovano
#XFLD: LABEL for partially persisted link
partiallyPersisted=Delno trajno shranjeno
#XFLD: Text for paused text
paused=Začasno zaustavljeno

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ View Monitor Tasklog Details Action keys~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message for release lock actionn
releaseLockMsg=Če traja izvajanje dlje kot običajno, lahko to pomeni, da ni uspelo in da status ni bil ustrezno posodobljen. \r\nDa odpravite težavo, lahko sprostite zaklepanje in status izvajanja nastavite na Neuspelo.
#XFLD: Label for release lock dialog
releaseLockText=Sprostitev zaklepanja

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Column header tooltip ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: tooltip for table column
txtViewTooltip=Ime obstoječega pogleda
#XFLD: tooltip for table column
txtViewDataAccessTooltip=To označuje razpoložljivost pogleda
#XFLD: tooltip for table column
txtViewRefreshFreqTooltip=To označuje, ali je za pogled opredeljen časovni razpored
#XFLD: tooltip for table column
txtViewStatusTooltip=Priklic statusa obstoječega pogleda
#XFLD: tooltip for table column
txtViewLatestUpdateTooltip=Prikaže podatek o tem, kdaj je bil obstoječi pogled nazadnje posodobljen
#XFLD: tooltip for table column
txtViewNextRunTooltip=Če je za pogled nastavljen časovni razpored, lahko vidite, do kdaj je naslednje izvajanje načrtovano.
#XFLD: tooltip for table column
txtViewNumOfRecordsTooltip=Sledite številu zapisov.
#XFLD: tooltip for table column
txtViewInMemorySizeTooltip=Sledenje, koliko prostora porabi pogled v pomnilniku
#XFLD: tooltip for table column
txtViewDiskSizeTooltip=Sledenje, koliko prostora porabi pogled na disku
#XMSG: Expired text
txtExpired=Poteklo

#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Objekta ''{0}'' ni mogoče dodati verigi naloge.

#~~~~~~~~~~~View Analyzer task log messages~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message indicating number of records for a view, and memory consumption in Mebibyte during simulation of view persistency
RECORDS_PEAK_MEMORY=Pogled "{0}" ima {1} zapisov. Za simulacijo trajnega shranjevanja podatkov za ta pogled je bilo porabljeno {2} MiB pomnilnika.
#XMSG: View Analyzer execution failure
VIEW_ANALYZER_EXECUTION_FAILED=Izvajanje analizatorja pogleda ni uspelo.
#XMSG: Missing authorizations for View Analyzer
VIEW_ANALYZER_MISSING_AUTH=Ni pooblastil za analizator pogleda.
#XMSG: Maximum memory in Gibibyte is reached, therefore persistency simulation is stopped
MAXIMUM_MEMORY_REACHED=Največja dovoljena poraba pomnilnika {0} GiB je bila dosežena pri simulaciji trajnega shranjevanja podatkov za pogled ''{1}''. Nadaljnje simulacije trajnega shranjevanja podatkov zato ne bodo izvedene.
#XMSG: Error during persistency simulation occurred
PERSISTENCY_SIMULATION_ERROR=Pri simulaciji trajnega shranjevanja podatkov za pogled ''{0}'' je prišlo do napake.
#XMSG: Persistency simulation is not executed, because prerequisites are not fulfilled and the view cannot be persisted
PREREQUISITES_NOT_FULFILLED=Simulacija trajnega shranjevanja podatkov ni izvedena za pogled ''{0}'', ker pogoji niso izpolnjeni in pogleda ni mogoče trajno shraniti.
#XMSG: Persistency simulation is not executed, because the view does not exist in the database
VIEW_DOES_NOT_EXIST=Če želite omogočiti simulacijo trajnosti podatkov, morate postaviti pogled "{0}".
#XMSG: Number of records cannot be determined, because the local table does not exist in the database
TABLE_DOES_NOT_EXIST=Lokalna tabela "{0}" ne obstaja v zbirki podatkov, zato za to tabelo ni mogoče določiti števila zapisov.
##XMSG: Message start of cancellation for View Analyzer task
CANCEL_VIEW_ANALYZER_TRIGGERED=Postopek za zaustavitev naloge Analizatorja pogleda {0} za pogled ''{1}'' je oddan. Do zaustavitve naloge lahko pride z zakasnitvijo.
##XMSG: Message start to indicate that task is not active
CANCEL_VIEW_ANALYZER_NO_ACTIVE_TASK=Naloga Analizatorja pogleda {0} za pogled ''{1}'' ni aktivna.
#XMSG: Cancel View Analyzer task failure
CANCEL_VIEW_ANALYZER_FAILED=Naloge Analizatorja pogleda ni uspelo preklicati.
##XMSG: Message used in View Analyzer execution task when task is canceled
VIEW_ANALYZER_CANCELED=Izvedba Analizatorja pogleda za pogled "{0}" je bila zaustavljena z nalogo preklica.
##XMSG: Message start of cancelation for Model Validation task
CANCEL_MODEL_VALIDATION_TRIGGERED=Postopek za zaustavitev naloge Preverjanje veljavnosti modela {0} za pogled "{1}" je oddan. Do zaustavitve naloge lahko pride z zakasnitvijo.
##XMSG: Message to indicate that task is not active
CANCEL_MODEL_VALIDATION_NO_ACTIVE_TASK=Naloga Preverjanje veljavnosti modela {0} za pogled "{1}" ni aktivna.
#XMSG: Cancel Model Validation task failure
CANCEL_MODEL_VALIDATION_FAILED=Naloge Preverjanje veljavnosti modela ni bilo mogoče preklicati.
##XMSG: Message used in Model Validation task when task is canceled
MODEL_VALIDATION_CANCELED=Izvedba preverjanja veljavnosti modela za pogled "{0}" je bila zaustavljena z nalogo preklica.
#XMSG: Message for Locked space error, before starting Model Validation task
MODEL_VALIDATION_LOCKED_SPACE_ERROR=Preverjanja veljavnosti modela ni mogoče izvesti za pogled "{0}", ker je prostor "{1}" zaklenjen.
##XMSG: Message used in View Analyzer execution task when determination of number of rows fails
NUMBER_ROWS_DETERMINATION_FAILED=Pri določanju števila vrstic za lokalno tabelo ''{0}'' je prišlo do napake.
##XMSG: Message to indicate that SQL plan was created
EXECUTION_PLAN_CREATED=Datoteka za načrtovanje SQL Analyzer za pogled ''{0}'' je ustvarjena in jo je mogoče prenesti iz strežnika.
#XFLD: Message to indicate starting of process to generate SQL Analyzer plan file
START_PLANVIZ_EXECUTION=Začetek postopka za generiranje datoteke za načrtovanje SQL Analyzer za pogled ''{0}''.
#XFLD: Message to indicate starting of View Analyzer process
VIEW_ANALYZER_EXECUTION_STARTED=Zagon izvedbe analizatorja pogleda.
##XMSG: Message to indicate that SQL plan file cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_PLANVIZ_NOT_FULFILLED=Datoteke za načrtovanje SQL Analyzer ni mogoče generirati za pogled ''{0}'', ker pogoji za trajno shranjevanje podatkov niso izpolnjeni.
##XMSG: Error message during  generation of SQL Analyzer plan file
PLANVIZ_ERROR=Pri generiranju datoteke za načrtovanje SQL Analyzer za pogled ''{0}'' je prišlo do napake.
#XMSG: Message for Locked space error
VIEW_ANALYZER_LOCKED_SPACE_ERROR=Analizatorja pogleda ni mogoče izvesti za pogled ''{0}'', ker je prostor ''{1}'' zaklenjen.
#XMSG: Message for persistency simulation without partitioning
PERSISTENCY_SIMULATION_WITHOUT_PARTITIONS=Particije pogleda ''{0}'' niso upoštevane pri simulaciji trajnega shranjevanja podatkov.
#XMSG: Message for generation of SQL Analyzer plan file without considering partitioning
PLANVIZ_WITHOUT_PARTITIONS=Particije pogleda ''{0}'' niso upoštevane pri generiranju datoteke za načrtovanje SQL Analyzer.
#XMSG: Confirmation message for mass removal of persisted data
MassRemovePersistency_Confirm_Msg=Želite odstraniti trajne podatke in preklopiti dostop do podatkov nazaj na navidezni dostop?
#XMSG: Confirmation message for partial mass removal of selected persisted data
PartialMassRemovePersistency_Confirm_Msg={0} od {1} izbranih pogledov ima trajne podatke. \n Želite odstraniti trajne podatke in preklopiti dostop do podatkov nazaj na navidezni dostop?
#XMSG: success message for stopping mass persistence
stopMassPersistenceSuccess=Poteka odstranjeva trajnih podatkov za izbrane poglede.
#XMSG: error message during mass stop persistence of a view (backend error)
STOP_MASS_PERSISTENCE_ERROR=Prišlo je do napake pri zaustavljanju trajnega shranjevanja za izbrane poglede.
#XMSG: memory analysis is only performed in space {0}
NO_MEMORY_ANALYSIS=Analiza pomnilnika se izvaja samo za entitete v prostoru "{0}": "{1}" "{2}" je preskočeno.
##XMSG: Message to indicate that Explain Plan cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_EXPLAINPLAN_NOT_FULFILLED=Datoteke Pojasnilo načrta ni mogoče ustvariti za pogled "{0}", ker niso izpolnjeni pogoji za trajno shranjevanje.
#XMSG: Message for Explain Plan generation without partitioning
EXPLAINPLAN_WITHOUT_PARTITIONS=Particije pogleda "{0}" niso upoštevane pri ustvarjanju datoteke Pojasnilo načrta.
#XFLD: Message to indicate starting of process to generate Explain Plan file
START_EXPLAINPLAN_EXECUTION=Postopek ustvarjanja datoteke Pojasnilo načrta za pogled "{0}" se začenja.
##XMSG: Message to indicate that Explain Plan was created
EXPLAINPLAN_CREATED=Datoteka Pojasnilo načrta za pogled "{0}" je bila ustvarjena. Prikažete jo lahko s klikom "Prikaz podrobnosti" ali jo prenesete iz strežnika, če imate ustrezno dovoljenje.
##XMSG: Error message during  generation of Explain Plan file
EXPLAINPLAN_ERROR=Pri ustvarjanju datoteke za Pojasnilo načrta za pogled ''''{0}" je prišlo do napake.
##XMSG: Explain Plan file cannot be generated for the view {0}, because too many views are stacked on each other
EXPLAINPLAN_MODEL_TOO_COMPLEX=Datoteke Pojasnilo načrta ni mogoče ustvariti za pogled {0}. Preveč pogledov je zloženih drug na drugega. Kompleksni modeli lahko povzročijo napake zaradi pomanjkanja pomnilnika in počasno delovanje. Priporočeno je trajno shranjevanje pogleda.

##XMSG: Message to indicate that the space is locked
PERFORMANCE_ANALYSIS_LOCKED_SPACE_ERROR=Analize delovanja ni mogoče izvesti za pogled "{0}", ker je prostor "{1}" zaklenjen.
##XMSG: The performance analysis task was cancelled
PERFORMANCE_ANALYSIS_CANCELED=Analiza delovanja za pogled "{0}" je bila preklicana.
##XMSG: The performance analysis failed
PERFORMANCE_ANALYSIS_FAILED=Analiza delovanja ni uspela.
##XMSG: The performance analysis is finished
PERFORMANCE_ANALYSIS_FINISHED=Analiza delovanja za pogled "{0}" je dokončana. Rezultate prikažite s klikom na "Ogled podrobnosti".
##XMSG: The view has input parameter with no default value
INPUT_PARAMETER_WITHOUT_DEFAULT_VALUE=Pogleda ni mogoče analizirati, ker ima parameter brez privzete vrednosti.
##XMSG: The view is not fully deployed
VIEW_NOT_FULLY_DEPLOYED=Tega pogleda ni mogoče analizirati, ker ni v celoti postavljen.
##XMSG: The view uses limited remote adapters
LIMITED_REMOTE_ADAPTER=Ta pogled uporablja vsaj en oddaljen adapter z omejenimi zmogljivostmi, kot je manjkajoči filter potiska navzdol ali podpora za 'Štetje'. Ohranjanje ali podvajanje objektov lahko izboljša delovanje v času izvajanja.
##XMSG: The view uses remote adapter that does not support 'Limit'
REMOTE_ADAPTER_NOT_SUPPORTING_LIMIT=Ta pogled uporablja vsaj en oddaljeni adapter, ki ne podpira 'Omejitve'. Morda je izbranih več kot 1000 zapisov.
##XMSG: Performance analysis is executed by using default parameters of view'
INPUT_PARAMETER_WITH_DEFAULT_VALUE=Analiza delovanje je izvedena z uporabo privzetih vrednosti parametrov pogleda.
##XMSG: Message to indicate that an error occurred
PERFORMANCE_ANALYSIS_ERROR=Med analizo zmogljivosti za pogled je prišlo do napake "{0}".
##XMSG: Message start of cancellation for Performance Analysis task
CANCEL_PERFORMANCE_ANALYSIS_TRIGGERED=Postopek za zaustavitev naloge Analiza delovanja {0} za pogled ''{1}'' je oddan. Do zaustavitve naloge lahko pride z zakasnitvijo.
##XMSG: Message start to indicate that task is not active
CANCEL_PERFORMANCE_ANALYSIS_NO_ACTIVE_TASK=Naloga Analiza delovanja {0} za pogled "{1}" ni aktivna.
#XMSG: Cancel Performance Analysis task failure
CANCEL_PERFORMANCE_ANALYSIS_FAILED=Preklic naloge Analiza delovanja ni uspel.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Dodeli časovni načrt meni
#XBUT: Pause schedule menu label
pauseScheduleLabel=Začasno prekini časovni načrt
#XBUT: Resume schedule menu label
resumeScheduleLabel=Nadaljuj časovni načrt
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Prišlo je do napake pri odstranjevanju časovnih načrtov.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Prišlo je do napake pri dodelitvi časovnih načrtov.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Prišlo je do napake pri začasni prekinitvi izvajanja časovnih načrtov.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Prišlo je do napake pri nadaljevanju izvajanja časovnih načrtov.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Izbris {0} časovnih načrtov je v teku
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Sprememba lastnika {0} časovnih načrtov je v teku
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Začasna prekinitev {0} časovnih načrtov
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Nadaljevanje {0} časovnih načrtov
#XBUT: Select Columns Button
selectColumnsBtn=Izbira stolpcev
#XFLD: Refresh tooltip
TEXT_REFRESH=Osveži
#XFLD: Select Columns tooltip
text_selectColumns=Izbira stolpcev


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Runtime Metrics ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Label for Runtime Metrics
RuntimeMetricsTitle=Metrika časa izvajanja za
#XFLD : Label for Run Button
runButton=Izvedi
#XFLD : Label for Cancel Button
cancelButton=Prekliči
#XFLD : Label for Close Button
closeButton=Zapri
#XFLD : Label for Open View Analyzer Button
openViewAnalyzerButton=Odpiranje analizatorja pogleda
#XFLD : Label for Generate Explain Plan Button
generateExplainPlanButton=Generiranje pojasnila načrta
#XFLD : Label for Previous Run Column
previousRun=Prejšnje izvajanje
#XFLD : Label for Latest Run Column
latestRun=Zadnje izvajanje
#XFLD : Label for time Column
time=Čas
#XFLD : Label for Duration Column
duration=Trajanje
#XFLD : Label for Peak Memory Column
peakMemory=Največja obremenitev pomnilnika
#XFLD : Label for Number of Rows
numberOfRows=Število vrstic
#XFLD : Label for Overall Number of Sources
overallNumberOfSources=Skupno število virov
#XFLD : Label for Data Access Column
dataAccess=Dostop do podatkov
#XFLD : Label for Local Tables
localTables=Lokalne tabele
#XFLD : Label for federatedRemoteTables
federatedRemoteTables=Združene oddaljene tabele (z omejenimi zmogljivostmi adapterja)
#XTXT Text for initial state of the runtime metrics
initialState=Za pridobitev metrike morate najprej zagnati analizo zmogljivosti. To lahko traja nekaj časa, vendar lahko postopek po potrebi prekličete.
#XMSG: Cancel confirmation message
CancelRunConfirmationMessage=Ali res želite preklicati trenutno izvajanje analize zmogljivosti?
#XTIT: Cancel dialog title
CancelRunTitle=Prekliči izvajanje
#XFLD: Label for Number of Rows
NUMBER_ROWS=Število vrstic
#XFLD: Label for Overall Number of Sources
OVERALL_NUMBER_SOURCES=Skupno število virov
#XFLD: Label for Data Access
DATA_ACCESS=Dostop do podatkov
#XFLD: Label for Remote tables
NUMBER_REMOTE_TABLES_LIMITED_ADAPTERS=Združene oddaljene tabele (z omejenimi zmogljivostmi adapterja)
#XFLD: Label for select statement
SELECT_STATEMENT='SELECT * FROM VIEW LIMIT 1000'
#XFLD: Label for duration
SELECT_RUNTIME=Trajanje
#XFLD: Label for peak memory
SELECT_PEAK_MEMORY=Največja obremenitev pomnilnika
#XFLD: Label for count statement
SELECT_COUNT_STATEMENT='SELECT COUNT(*) FROM VIEW'
#XFLD: Label for count duration
SELECT_COUNT_RUNTIME=Trajanje
#XFLD: Label for count peak memory
SELECT_COUNT_PEAK_MEMORY=Največja obremenitev pomnilnika
#XFLD: Label for count of number of local tables
NUMBER_LOCAL_TABLES_FILE=Lokalne tabele (datoteka)
#XTXT: Text for running state of the runtime metrics
Running=Izvajanje ...
#XFLD: Label for time
Time=Čas
#XFLD: Label for virtual access
PA_VIRTUAL=Navidezno
#XFLD: Label for persisted access
PA_PERSISTED=Trajno
PA_PARTIALLY_PERSISTED=Delno trajno shranjeno
#XTXT: Text for cancel
CancelRunSuccessMessage=Preklic izvajanja analize zmogljivosti.
#XTXT: Text for cancel error
CancelRunErrorMessage=Pri preklicu izvajanja analize zmogljivosti je prišlo do napake.
#XTXT: Text for explain plan generation
ExplainPlanStarted=Generiranje pojasnila načrta za pogled "{0}".
#XTXT: Text for performance analysis
PerformanceAnalysisStarted=Zagon analize zmogljivosti za pogled "{0}".
#XTXT: Text for performance analysis error
ErrorFetchingPerformanceAnalysis=Pri priklicu podatkov analize zmogljivosti je prišlo do napake.
#XTXT: Text for performance analysis error
conflictingTask=Naloga analize zmogljivosti se že izvaja
#XFLD: Label for Errors
Errors=Napake
#XFLD: Label for Warnings
Warnings=Opozorila
#XFLD: Tooltip for missing privileges
openViewAnalyzerButtonTooltipNoPrivilege=Za odpiranje Analizatorja pogleda potrebujete pravico za (posodobitev) DWC_DATAINTEGRATION.
#XFLD: Tooltip for missing privileges
generateExplainPlanButtonTooltipNoPrivilege=Za generiranje Pojasnila načrta potrebujete pravico za (branje) DWC_DATAINTEGRATION.



#XFLD: Label for frequency column
everyLabel=Vsakih
#XFLD: Plural Recurrence text for Hour
hoursLabel=ur
#XFLD: Plural Recurrence text for Day
daysLabel=dni
#XFLD: Plural Recurrence text for Month
monthsLabel=mesecev
#XFLD: Plural Recurrence text for Minutes
minutesLabel=minut
