
#XBCB: Breadcrumb
viewMonitorBreadcrumb={spaceId}

#XFLD: Label for persisted view column
SOURCE=Nguồn
#XFLD: Label for persisted view column
NAME=Tên
#XFLD: Label for persisted view column
NAME_LABEL=Tên doanh nghiệp
#XFLD: Label for persisted view column
NAME_LABELNew=<PERSON><PERSON><PERSON> (<PERSON>ê<PERSON> do<PERSON>h nghiệ<PERSON>)
#XFLD: Label for persisted view column
TECHINCAL_NAME=Tên kỹ thuật
#XFLD: Label for persisted view column
TECHINCAL_NAMENew=<PERSON><PERSON><PERSON>ư<PERSON> (Tê<PERSON> kỹ thuật)
#XFLD: Label for persisted view column
DATA_PERSISTENCY=Truy cập dữ liệu
#XFLD: Label for persisted view column
STATUS=Trạng thái
#XFLD: Label for persisted view column
LAST_UPDATED=Cập nhật lần cuối
#XFLD: Label for persisted view column
IN_MEMORY_SIZE=Bộ nhớ được sử dụng để lưu trữ (MiB)
#XFLD: Label for persisted view column
DISK_SIZE=Đ<PERSON><PERSON> được sử dụng để lưu trữ (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_NEWW=Kích thước bộ nhớ trong (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_HEADER=Kích thước bộ nhớ trong
#XFLD: Label for persisted view column
DISK_SIZE_NEWW=Kích thước trên ổ đĩa (MiB)
#XFLD: Label for persisted view column
DISK_SIZE_HEADER=Kích thước trên ổ đĩa
#XFLD: Label for schedule owner column
txtScheduleOwner=Chủ sở hữu lịch
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Hiển thị ai đã tạo lịch
#XFLD: Label for persisted view column
PERSISTED=Được duy trì
#XFLD: Label for persisted view column
TYPE=Kiểu
#XFLD: Label for View Selection Dialog column
changedOn=Thay đổi vào
#XFLD: Label for View Selection Dialog column
createdBy=Được tạo bởi
#XFLD: Label for log details column
txtViewPersistencyLogs=Xem nhật ký
#XFLD: Label for log details column
txtViewPersistencyLogsNew=Chi tiết
#XFLD: text for values shown for Ascending sort order
SortInAsc=Sắp xếp tăng
#XFLD: text for values shown for Descending sort order
SortInDesc=Sắp xếp giảm
#XFLD: Label for persisted view monitor heading
ViewMonitor_Heading=Giám sát màn hình
#XFLD: Label for persisted view monitor sub-heading
ViewMonitor_SubHeading=Giám sát và duy trì quá trình ổn định dữ liệu của màn hình


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ DynamicPageHeader ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: text for values shown in column Persistence Status
txtLoading=Đang tải
#XFLD: text for values shown in column Persistence Status
txtRunning=Đang chạy
#XFLD: text for values shown in column Persistence Status
txtAvailable=Có sẵn
#XFLD: text for values shown in column Persistence Status
txtError=Lỗi
#XMSG: Error message for unknown Persistence type that gets logged in Kibana
unknownReplicationTypeError=Không hỗ trợ kiểu sao chép ''{0}''.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Content ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Thiết lập được sử dụng để thực hiện ổn định dữ liệu lần cuối:
#XMSG: Message for input parameter name
inputParameterLabel=Tham số nhập
#XMSG: Message for input parameter value
inputParameterValueLabel=Giá trị
#XMSG: Message for persisted data
inputParameterPersistedLabel=Được ổn định lúc
#XTIT: Count for View in ViewMonitor Toolbar
txtViewCount=Màn hình ({0})
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Tính ổn định của màn hình
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Ổn định dữ liệu
#XBUT: Button to clear the selected view persistance
ClearViewPersistance=Xóa
#XBUT: Button to stop the selected view persistance
stopPersistance=Dừng tính ổn định
#XFLD: Placeholder for Search field
txtSearch=Tìm kiếm
#XBUT: Tooltip for refresh button
txtRefresh=Làm mới
#XBUT: Tooltip for add view button
txtDeleteView=Xóa quá trình ổn định
#XBUT: Tooltip for load new peristence
loadNewPersistence=Bắt đầu lại quá trình ổn định
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Tải ảnh chụp nhanh mới
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Bắt đầu ổn định dữ liệu
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Loại bỏ dữ liệu liên tục
#XMSG: success message for starting persistence
startPersistenceSuccess=Chúng tôi đang ổn định màn hình ''{0}''.
#XMSG: success message for stopping persistence
stopPersistenceSuccess=Chúng tôi đang gỡ bỏ dữ liệu đã ổn định cho màn hình ''{0}''.
#XMSG: success message for stopping persistence
deletePersistedViewSuccess=Chúng tôi đang gỡ bỏ màn hình ''{0}'' khỏi danh sách giám sát.
#XMSG: error message during start persistence of a view (backend error)
ERROR_STARTING_PERSISTENCE=Đã xảy ra lỗi trong khi bắt đầu quá trình ổn định dữ liệu cho màn hình ''’{0}''.
#XMSG: error message during start persistence of a view (backend error)
PARAMERROR_STARTING_PERSISTENCE=Màn hình “{0}” không thể được ổn định vì nó có các tham số đầu vào.
#XMSG: error message during start persistence of a view (backend error)
NUMBER_OF_PARAMETER_STARTING_PERSISTENCE=Không thể ổn định màn hình "{0}" vì nó có nhiều hơn một tham số đầu vào.
#XMSG: error message during start persistence of a view (backend error)
PARAMETER_DEFAULT_VALUE_STARTING_PERSISTENCE=Không thể ổn định màn hình "{0}" vì tham số đầu vào không có giá trị mặc định.
#XMSG: error message during start persistence of a view (backend error)
REDEPLOYMENT_DAC_REQUIRED=Triển khai lại Kiểm soát truy cập dữ liệu (DAC) "{0}" được yêu cầu để hỗ trợ ổn định dữ liệu.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_STARTING_PERSISTENCE=Màn hình ''{0}'' không thể ổn định được do nó sử dụng màn hình ''{1}''’ có phần Kiểm soát truy cập dữ liệu (DAC).
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_EXTERNAL_STARTING_PERSISTENCE=Không thể ổn định màn hình ''{0}''’'' vì nó sử dụng màn hình có phần Kiểm soát truy cập dữ liệu (DAC) thuộc vùng dữ liệu khác.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_STRUCTURE_STARTING_PERSISTENCE=Không thể ổn định màn hình ''’{0}'' vì cấu trúc của một hoặc nhiều phần Kiểm soát truy cập dữ liệu (DAC) của nó không hỗ trợ quá trình ổn định dữ liệu.
#XMSG: error message during stop persistence of a view (backend error)
STOP_PERSISTENCE_ERROR=Đã xảy ra lỗi trong khi dừng quá trình ổn định cho màn hình ''’{0}''.
#XMSG: error message during stop persistence of a view (backend error)
DELETE_PERSISTED_VIEW_ERROR=Đã xảy ra lỗi trong khi xóa màn hình đã ổn định ''{0}''.
#XMSG: Warning message for removing the persisted data
RemovePersistency_Confirm_Msg=Bạn có muốn xóa dữ liệu đã ổn định và chuyển sang chế độ truy cập ảo của màn hình ''{0}''’ không?
#XMSG: Warning message for delete the view persistence
DeletePersistency_Confirm_Msg=Bạn có muốn gỡ bỏ màn hình khỏi danh sách giám sát và xóa dữ liệu đã ổn định của màn hình ''{0}''’ không?
#XMSG: error message for reading data from backend
txtReadBackendError=Có vẻ như đã có lỗi khi đọc từ chương trình phụ trợ.
#XFLD: Label for No Data Error
NoDataError=Lỗi
#XMSG: message for conflicting task
Task_Already_Running=Tác vụ xung đột đang chạy cho màn hình ''{0}''.

#XFLD: Error while accessing pertitioning for a view
missingPrivilegeInViewPersistency=Không đủ quyền để thực thi phân vùng cho màn hình ''{0}''

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Show All Views~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Text for show all views button
allViewsText=Tất cả màn hình ({0})
#XBUT: Text for show scheduled views button
scheduledText=Được lập lịch ({0})
#XBUT: Text for show persisted views button
persistedText=Được duy trì ({0})
#XBUT: Text for start analyzer button
startAnalyzer=Khởi động trình phân tích màn hình
#XFLD: Message if repository is unavailable
repositoryErrorMsg=Kho lưu trữ không có sẵn và một số tính năng nhất định bị tắt.

#XFLD: Data Access - Virtual
Virtual=Ảo
#XFLD: Data Access - Persisted
Persisted=Được duy trì

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Non-Persisted View Selection Dialog~~~~~~~~~~~~~~~~~~~~~~~

#XTIT: Title for Non-Persisted View selection dialog
select_source=Chọn màn hình để tiếp tục

#XTIT: Placeholder for Non-Persisted View selection dialog search
searchView=Tìm kiếm màn hình
#XTIT: No data in the list of non-persisted view
No_Data=Không có dữ liệu
#XBUT: Button to select non-persisted view
ok=Ok
#XBUT: Button to close the non-persisted views selection dialog
cancel=Hủy

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~View persistency log message key~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message for view persistency logs
START_STOP_PERSISTENCY=Bắt đầu chạy tác vụ ổn định dữ liệu cho''{1}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY=Duy trì dữ liệu cho màn hình ''{1}''
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY)
START_VIEW_PERSISTENCY_PROCESS=Bắt đầu quy trình để ổn định dữ liệu cho màn hình ''{0}''.
#XFLD: Message for view persistency manual partition load
START_VIEW_PERSISTENCY_PROCESS_MANUAL_PARTITIONS=Bắt đầu quy trình để ổn định dữ liệu cho màn hình ''{0}'' có ID phân vùng được chọn: ''{1}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY=Đang gỡ bỏ dữ liệu đã ổn định cho màn hình ''{1}''.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY)
REMOVE_VIEW_PERSISTENCY=Đang bắt đầu quy trình để gỡ bỏ dữ liệu được duy trì ổn định cho màn hình "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_SUCCESS=Dữ liệu được ổn định cho màn hình ''{1}''.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_SUCCESS)
START_VIEW_PERSISTENCY_PROCESS_SUCCESS=Dữ liệu được ổn định cho màn hình ''{0}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY_SUCCESS=Dữ liệu đã ổn định bị gỡ bỏ và quyền truy cập dữ liệu ảo được khôi phục cho màn hình ''{1}''.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_SUCCESS)
REMOVE_VIEW_PERSISTENCY_SUCCESS=Đã hoàn tất quy trình để gỡ bỏ dữ liệu được duy trì ổn định cho màn hình "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_FAILED=Không thể ổn định dữ liệu cho màn hình ''{1}''.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_FAILED)
START_VIEW_PERSISTENCY_PROCESS_FAILED=Không thể ổn định dữ liệu cho màn hình ''{0}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.05)
STOP_VIEW_PERSISTENCY_FAILED=Không thể gỡ bỏ dữ liệu đã ổn định cho màn hình ''{1}''.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_FAILED)
REMOVE_VIEW_PERSISTENCY_FAILED=Không thể gỡ bỏ dữ liệu đã ổn định cho màn hình ''{0}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_SUCCESS=“{3}” bản ghi được ổn định cho màn hình ''{1}''.
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace START_PERSISTENCY_RECORD_COUNT_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS={0} bản ghi được chèn vào bảng ổn định dữ liệu cho màn hình ''{1}''.
#XFLD: Message used by SEAL repository for view persistency logs (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS_V2={0} bản ghi được chèn vào bảng ổn định dữ liệu cho màn hình ''{1}''. Bộ nhớ đã sử dụng: {2} GiB.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_SUCCESS=“{3}" bản ghi đã ổn định bị gỡ bỏ cho màn hình ''{1}''.
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace STOP_PERSISTENCY_RECORD_COUNT_SUCCESS)
REMOVE_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=Dữ liệu đã ổn định bị gỡ bỏ, ''{0}'' bản ghi đã ổn định bị xóa.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_FAILED=Không lấy được recordCount cho màn hình ''{1}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_FAILED=Không lấy được recordCount cho màn hình ''{1}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Lịch được xóa cho ''{1}''.
#XFLD: Message for view persistency logs (new message to replace STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS)
REMOVE_VIEW_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Lịch được xóa cho màn hình ''{0}''.
#XFLD: Message for view persistency logs
STOP_PERSISTENCY_DELETE_SCHEDULE_FAILED=Không xóa được lịch cho ''{1}''.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_REDEPLOY=Chúng tôi không thể ổn định màn hình ''’{0}'' vì nó đã được thay đổi và triển khai từ khi bạn bắt đầu ổn định nó. Hãy thử ổn định lại màn hình hoặc đợi cho đến lần chạy được lập lịch kế tiếp.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_DELETE=Chúng tôi không thể ổn định màn hình ''{0}'' vì nó đã bị xóa từ khi bạn bắt đầu ổn định nó.
#XFLD: Message for successful data persistency of a single partition (for messages created before version 2022.03)
START_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} bản ghi được ổn định vào phân vùng cho giá trị ''{1}'' <= {2} < ''{3}''.
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (new message to replace START_PERSISTENCY_SINGLE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} bản ghi được chèn vào phân vùng cho giá trị ''{1}'' <= “{2}” < ''{3}''.
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS_V2={0} bản ghi được chèn vào phân vùng cho giá trị ''{1}'' <= ''{2}'' < ''{3}''. Bộ nhớ đã sử dụng: {4} GiB.
#XFLD: Message for successful replication of a "others" partition (for messages created before version 2022.03)
START_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} bản ghi được ổn định vào phân vùng ''khác''.
#XFLD: Message used by SEAL repository for successful replication of a "others" partition (new message to replace START_PERSISTENCY_OTHERS_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} bản ghi được chèn vào phân vùng ''khác''.
#XFLD: Message for view persistency record count of partitions (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={3} bản ghi được ổn định cho màn hình ''{1}'' trong {4} phân vùng.
#XFLD: Message used by SEAL repository for view persistency record count of partitions (new message to replace START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={0} bản ghi được chèn vào bảng ổn định dữ liệu cho màn hình "{1}" trong {2} phân vùng.
#XFLD: Message used by SEAL repository for view persistency for partition wise refresh
START_VIEW_PERSISTENCY_RECORD_COUNT_REFRESHED_PARTITIONS_SUCCESS={0} bản ghi được chèn vào bảng ổn định dữ liệu cho màn hình ''{1}''. Phân vùng đã cập nhật: {2}; Phân vùng bị khóa: {3}; Tổng số phân vùng: {4}
#XFLD: Message used by SEAL repository for view persistency for manual partition load
START_VIEW_PERSISTENCY_RECORD_COUNT_MANUAL_PARTITIONS_SUCCESS={0} bản ghi được chèn vào bảng ổn định dữ liệu cho màn hình ''{1}'' trong {2} phân vùng được chọn
#XFLD: Message used by SEAL repository for view persistency for locked partition data load
START_VIEW_PERSISTENCY_RECORD_COUNT_LOCKED_PARTITIONS_SUCCESS={0} bản ghi được chèn vào bảng ổn định dữ liệu cho màn hình ''{1}''. Phân vùng đã cập nhật: {2}; Phân vùng bị khóa, không thay đổi: {3}; Tổng số phân vùng: {4}
#XMSG: Message for unexpected error (for messages created before version 2022.03)
START_PERSISTENCY_UNEXPECTED_ERROR=Đã xảy ra lỗi không mong muốn trong khi ổn định dữ liệu cho màn hình ''{0}''.
#XMSG: Message for unexpected error (new message to replace START_PERSISTENCY_UNEXPECTED_ERROR)
START_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Đã xảy ra lỗi không mong muốn trong khi ổn định dữ liệu cho màn hình ''{0}''.
#XMSG: Message for Locked space error
START_VIEW_PERSISTENCY_LOCKED_SPACE_ERROR=Không thể ổn định màn hình ''{0}''’ vì vùng dữ liệu ''{1}'' bị khóa.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
STOP_PERSISTENCY_UNEXPECTED_ERROR=Đã xảy ra lỗi không mong muốn trong khi gỡ bỏ dữ liệu đã ổn định cho màn hình ''{0}''.
#XMSG: Message for unexpected error (new message to replace STOP_PERSISTENCY_UNEXPECTED_ERROR)
REMOVE_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Đã xảy ra lỗi không mong muốn trong khi gỡ bỏ quá trình ổn định cho màn hình ''{0}''.
#XMSG: Message for ivalidated view
VIEW_PERSISTENCY_ABORTED_DEFINITION_INVALID=Định nghĩa của màn hình ''{0}'' đã trở nên không hợp lệ, phần lớn có thể là do sự thay đổi của đối tượng được màn hình sử dụng trực tiếp hoặc gián tiếp. Hãy thử triển khai lại màn hình để giải quyết sự cố hoặc nhận dạng nguyên nhân gốc rễ.
#XMSG: Message for persisted data removal (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT=Dữ liệu ổn định được gỡ bỏ trong khi triển khai màn hình ''{0}''.
#XMSG: Message for persisted data removal of a consuming view
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW=Dữ liệu ổn định được gỡ bỏ trong khi triển khai màn hình được sử dụng ''{0}''.
#XMSG: Message for persisted data removal of a consuming view due to a dac change
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW_DAC_CHANGED=Dữ liệu ổn định được gỡ bỏ trong khi triển khai màn hình được sử dụng ''’{0}'' vì phần kiểm soát truy cập dữ liệu của nó đã thay đổi.
#XMSG: Message for persisted data removal (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DEPLOYMENT=Dữ liệu ổn định được gỡ bỏ trong khi triển khai màn hình ''{0}''.
#XMSG: Message for deleted view (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION=Quá trình ổn định được gỡ bỏ với thao tác xóa màn hình ''{0}''.
#XMSG: Message for deleted view (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DELETION=Quá trình ổn định được gỡ bỏ với thao tác xóa màn hình ''{0}''.
#XMSG Message for removal of persisted data, because prerequisites not fulfilled
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_PREREQUISITES_NOT_FULFILLED=Dữ liệu ổn định đã bị loại bỏ vì các điều kiện tiên quyết về ổn định dữ liệu không còn được đáp ứng.
#XMSG: Message for inconsistent persistency
VIEW_PERSISTENCY_INCONSISTENT_PERSISTENCY=Quá trình ổn định màn hình "{0}" đã trở nên không nhất quán. Xóa dữ liệu ổn định để giải quyết vấn đề.
#XMSG: Message for prerequisites
START_PERSISTENCY_CHECK_PREREQUISITES=Kiểm tra điều kiện tiên quyết để ổn định màn hình ''{0}''.
#XMSG: Message for DAC deployed in deprecated way using table function and join (for messages created before 2025.05)
START_PERSISTENCY_DEPRECIATED_DAC=Màn hình "{0}" được triển khai bằng tính năng Kiểm soát quyền truy cập dữ liệu (DAC) mà đang dần được bỏ dùng. Vui lòng triển khai lại màn hình để cải thiện hiệu suất.
#XMSG: Message for DAC deployed in deprecated way using table function and join
START_PERSISTENCY_DEPRECATED_DAC=Màn hình "{0}" được triển khai bằng tính năng Kiểm soát quyền truy cập dữ liệu (DAC) mà đang dần được bỏ dùng. Vui lòng triển khai lại màn hình để cải thiện hiệu suất.
#XMSG: Message for roll back (for messages created before version 2022.03)
ROLLBACK_VIEW_PERSISTENCY=Đã xảy ra lỗi. Tình trạng ổn định trước đó được khôi phục cho màn hình ''{0}''.
#XMSG: Message used by SEAL repository for rollback of running procedure to persist view (new message to replace ROLLBACK_VIEW_PERSISTENCY)
VIEW_PERSISTENCY_ROLLBACK=Đã xảy ra lỗi. Quy trình ổn định màn hình ''{0}'' đã dừng và các thay đổi được khôi phục.
#XMSG: Message used by SEAL repository for rollback of running procedure to remove persisted data of view
REMOVE_VIEW_PERSISTENCY_ROLLBACK=Đã xảy ra lỗi. Quy trình để gỡ bỏ dữ liệu được duy trì ổn định của màn hình "{0}" đã bị dừng lại và các thay đổi đã được phục hồi.
#XMSG: Message used by SEAL repository for persistency preparation
START_PERSISTENCY_PREPARATION=Chuẩn bị ổn định dữ liệu.
#XMSG: Message used by SEAL repository for data insertion
START_INSERTING_PERSISTENCY_DATA=Chèn dữ liệu vào bảng ổn định.
#XMSG: Message for null values - partitioning (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS={0} bản ghi giá trị rỗng được chèn vào phân vùng “khác”.
#XMSG: Message used by SEAL repository for others partition values - partitioning (new message to replace START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS={0} bản ghi được chèn vào phân vùng “khác” cho giá trị ''{2}'' < ''{1}'' OR ''{2}'' >= ''{3}''.
#XMSG: Message used by SEAL repository for others partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS_V2={0} bản ghi được chèn vào phân vùng ''khác''’ cho giá trị ''{2}'' < ''’{1}” OR ''{2}'' >= ''{3}''’. Bộ nhớ đã sử dụng: {4} GiB.
#XMSG: Message used by SEAL repository for null partition values - partitioning
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS={0} bản ghi được chèn vào phân vùng “khác” cho giá trị ''{1}'' IS NULL.
#XMSG: Message used by SEAL repository for null partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS_V2={0} bản ghi được chèn vào phân vùng “khác” cho giá trị ''{1}'' IS NULL. Bộ nhớ đã sử dụng: {2} GiB.
##XFLD:Message used by SEAL repository for remote statistics
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS=Tải dữ liệu có liên quan: {0} các câu lệnh từ xa. Tổng bản ghi được tìm nạp: {1}. Tổng thời lượng: {2} giây.
##XFLD:Message used by SEAL repository for remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS_WITH_PARTITIONS=Tải dữ liệu có liên quan bằng cách sử dụng {0} phân vùng có {1} các câu lệnh từ xa. Tổng số bản ghi được tìm nạp: {2}. Tổng thời lượng: {3} giây.
##XFLD:Message used by SEAL repository with UI ID for instructions of remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_INSTRUCTION=Các câu lệnh từ xa được xử lý trong lúc thực hiện có thể được hiển thị bằng cách mở màn hình truy vấn từ xa, theo chi tiết của các thông báo theo phân vùng cụ thể.
##XFLD:Message for start reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS=Bắt đầu quá trình sử dụng lại dữ liệu đã ổn định hiện có cho màn hình {0} sau khi triển khai.
##XFLD:Message used by SEAL repository for reattach view persitency
START_VIEW_PERSISTENCY_REATTACHMENT=Bắt đầu sử dụng lại dữ liệu đã ổn định hiện có.
##XFLD:Message used by SEAL repository for reattach view persitency
START_FINILIZING_VIEW_PERSISTENCY_REATTACHMENT=Sử dụng lại dữ liệu đã ổn định hiện có.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_COMPLETE=Quá trình sử dụng lại dữ liệu đã ổn định hiện có đã hoàn tất cho màn hình {0}.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_FAILED=Không thể sử dụng lại dữ liệu đã ổn định hiện có cho màn hình {0} sau khi triển khai.
#XMSG: Message used by SEAL repository for finalizing persistency
START_FINALIZING_PERSISTENCY=Đang hoàn thiện quá trình ổn định.
#XMSG: Message used by SEAL repository for restoring virtual access for view
REMOVE_VIEW_PERSISTENCY_VIRTUAL_ACCESS_RESTORED=Quyền truy cập dữ liệu ảo được khôi phục cho màn hình ''{0}''.
#XMSG: Message used by SEAL repository for view that already has virtual access
REMOVE_VIEW_PERSISTENCY_ALREADY_VIRTUAL_ACCESS=Màn hình "{0}" đã có quyền truy cập dữ liệu ảo rồi. Không có dữ liệu được duy trì ổn định nào bị gỡ bỏ.
#XMSG: Message for removing view persistency
VIEW_PERSISTENCY_REMOVED_FROM_MONITOR=Màn hình ''{0}'' đã được gỡ bỏ khỏi phần giám sát Màn hình.
#XMSG: Message for view doesnt exist.
VIEW_PERSISTENCY_VIEW_NOT_EXISTING=Chế độ xem "{0}" không tồn tại trong cơ sở dữ liệu hoặc không được triển khai chính xác và do đó không thể duy trì ổn định được. Hãy thử triển khai lại chế độ xem để giải quyết sự cố hoặc xác định nguyên nhân gốc rễ.
#XMSG: Message for persistency not enabled.
VIEW_PERSISTENCY_NOT_ENABLED=Quá trình ổn định dữ liệu không được bật. Hãy triển khai lại bảng/màn hình trong vùng dữ liệu ''{0}'' để bật chức năng.
#XMSG: Message for unexpected error due to node crash
VIEW_PERSISTENCY_UNEXPECTED_ERROR_GENERAL=Lần chạy của ổn định màn hình sau cùng đã bị gián đoạn do lỗi kỹ thuật.
#XFLD: Message for view persistency memory consumption
VIEW_PERSISTENCY_MEMORY_CONSUMPTION={0} GiB của bộ nhớ cao nhất được sử dụng trong thời gian chạy ổn định của màn hình.
#XFLD: Message for view persistency timeout
VIEW_PERSISTENCY_TIMEOUT=Mức độ duy trì của màn hình {0} đã hết giờ trong {1} giờ.
#XFLD: Message for not started asynchronous view persistency
VIEW_PERSISTENCY_ASYNC_NOT_STARTED=Tải hệ thống cao đã ngăn không cho bắt đầu thực hiện không đồng bộ duy trì màn hình. Kiểm tra xem có quá nhiều tác vụ đang chạy song song không.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency (for messages created before version 2022.05)
START_VIEW_PERSISTENCY_EXECUTION_METHOD_DROP_AND_RENAME=Bảng duy trì hiện tại đã xóa và được thay thế bằng bảng duy trì mới.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_RECREATE_AND_DROP=Bảng duy trì hiện tại đã xóa và được thay thế bằng bảng duy trì mới.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_TRUNCATE_AND_COPY=Bảng duy trì hiện tại đã được cập nhật với dữ liệu mới.
##XFLD: Message for missing authorizations for view persistency
VIEW_PERSISTENCY_MISSING_AUTH=Thiếu phân quyền cho quá trình ổn định dữ liệu.
##XFLD: Message start of persistency cancellation
VIEW_PERSISTENCY_TRIGGER_CANCEL=Bắt đầu hủy quá trình để duy trì màn hình {0}.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_UNAVAILABLE_TASK_RUN=Không hủy được quá trình ổn định màn hình vì không có tác vụ ổn định dữ liệu nào đang chạy cho màn hình {0}.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_NONDETERMINISTIC_TASK_RUN=Không hủy được quá trình ổn định màn hình vì không có tác vụ ổn định dữ liệu nào đang chạy cho màn hình {0}.
##XFLD: Message for canceling a non running persitency task
VIEW_PERSISTENCY_CANCEL_NON_RUNNING_SELECTED_TASK=Không hủy được quá trình ổn định màn hình {0} vì tác vụ ổn định dữ liệu đã chọn {1} hiện không đang chạy.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_UNREACHABLE_PERSISTENCY_RUN=Không thể hủy quá trình để duy trì màn hình vì dữ liệu tồn tại cho màn hình {0} vẫn chưa bắt đầu.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_COMPLETED_PERSISTENCY_RUN=Không thể hủy quá trình để duy trì màn hình {0} bởi vì nó đã được hoàn thành.
##XFLD: Message for failed cancellation
VIEW_PERSISTENCY_ABORTED_CANCEL_EXECUTION=Không thể hủy quá trình để duy trì màn hình {0}.
##XFLD: Message for finalizing persistency cancellation
VIEW_PERSISTENCY_FINALIZE_CANCEL=Quy trình dừng ổn định dữ liệu của màn hình {0} đã được gửi.
##XFLD: Message used in persist task when cancellation is caught
VIEW_PERSISTENCY_CANCELED=Quy trình duy trì màn hình {0} đã dừng.
##XFLD: Message used in persist task when cancelation is caught and performed through a cancelation task
VIEW_PERSISTENCY_CANCELED_VIA_CANCEL_TASK=Quy trình duy trì màn hình {0} đã bị dừng thông qua tác vụ hủy {1}.
##XFLD: Message used in cancel task when the task is triggered by deployment
VIEW_PERSISTENCY_CANCEL_TRIGGERED_BY_DEPLOYMENT=Hủy quy trình duy trì dữ liệu trong khi triển khai màn hình {0}.
##XFLD: Message used in case the cancelation is retriggered for a running task
VIEW_PERSISTENCY_CANCEL_ATTEMPT_RETRIGGER_CANCEL=Tác vụ hủy trước đó để ổn định màn hình {0} đã được gửi.
##XFLD: Message used to express the delay that may occur before the persistency task is stopped
VIEW_PERSISTENCY_CANCEL_DELAY_STOPPING_PERSISTENCY=Có thể có độ trễ cho đến khi dừng tác vụ ổn định dữ liệu cho màn hình {0}.
##XFLD: Message used to reflect the task log identifier of the current running persistency task
VIEW_PERSISTENCY_CANCEL_CURRENT_RUNNING_TASK=Dữ liệu cho màn hình {0} được duy trì ổn định bằng tác vụ {1}.
##XFLD:Message used by SEAL repository for locked partitions and DACs
VIEW_PERSISTENCY_LOCKED_PARTITIONS_DACS=Phân quyền do DAC cung cấp có thể đã thay đổi và không được xem xét bởi các phân vùng bị khóa. Mở khóa các phân vùng và tải một ảnh chụp nhanh mới để áp dụng các thay đổi.
##XFLD:Message used by SEAL repository for column structure change
VIEW_PERSISTENCY_COLUMN_STRUCTURE_CHANGE=Cấu trúc cột đã thay đổi và không còn khớp với bảng lưu trữ hiện có. Xóa dữ liệu đã lưu trữ và bắt đầu lưu trữ dữ liệu mới để cập nhật bảng lưu trữ của bạn.
##XFLD: Message for out of memory
VIEW_PERSISTENCY_HANA_OUT_OF_MEMORY=Tác vụ không thành công do lỗi hết bộ nhớ trên cơ sở dữ liệu SAP HANA.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_EXCEPTION_TRANSACTION_ROLLBACK=Tác vụ không thành công do ngoại lệ nội bộ trên cơ sở dữ liệu SAP HANA.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_SQL_EXECUTION_ISSUE=Tác vụ không thành công do sự cố ngoại lệ SQL nội bộ trên cơ sở dữ liệu SAP HANA.
##XFLD: Message for HANA out of memory result
VIEW_PERSISTENCY_OUT_OF_MEMORY_RESULT_MESSAGE=Lý do sự kiện hết bộ nhớ HANA: {0}
##XFLD: Message for admission control rejection
VIEW_PERSISTENCY_HANA_ADMISSION_CTRL_REJECTION=Tác vụ không thành công do từ chối kiểm soát thu nạp SAP HANA.
##XFLD: Message for HANA back pressure / rate limit of HANA client
VIEW_PERSISTENCY_HANA_CLIENT_RATE_LIMIT_REJECTION=Tác vụ không thành công do có quá nhiều kết nối SAP HANA đang hoạt động.
#XMSG: Message for invalid persisted table name
VIEW_PERSISTENCY_PERSISTED_TABLE_INVALID=Đã xảy ra lỗi và bảng được ổn định đã trở nên không hợp lệ. Để giải quyết vấn đề, vui lòng loại bỏ dữ liệu đã đã ổn định và ổn định màn hình lại.
#XMSG: Message for usahe of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION=Không thể ổn định màn hình. Nó sử dụng bảng từ xa dựa trên nguồn từ xa có nhân rộng người dùng được kích hoạt. Kiểm tra nguồn gốc của màn hình.
#XMSG: Message for dynamic usage of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION_DYNAMIC=Không thể ổn định màn hình. Nó sử dụng bảng từ xa dựa trên nguồn từ xa có nhân rộng người dùng được kích hoạt. Bảng từ xa có thể được sử dụng động thông qua màn hình tập lệnh SQL. Nguồn gốc màn hình có thể không hiển thị bảng từ xa.
#XMSG: Message for insufficient privileges or consumption of view with DAC via dynamic SQL
VIEW_PERSISTENCY_INSUF_PRIV_OR_DAC_DYN_SQL=Đặc quyền của bạn có thể không đủ. Hãy mở Bản xem trước dữ liệu để biết xem bạn có đủ đặc quyền cần thiết không. Nếu có, màn hình thứ hai được sử dụng qua tập lệnh SQL động có thể đã áp dụng quyền kiểm soát truy cập dữ liệu (DAC) cho nó.
#XMSG: deployment of DAC is deprecated and view need to be redeployed to be able to persist data
VIEW_PERSISTENCY_DEPRECATED_DAC=Màn hình "{0}" được triển khai bằng tính năng Kiểm soát quyền truy cập dữ liệu (DAC) mà đang dần được bỏ dùng. Vui lòng triển khai lại màn hình để có thể duy trì ổn định dữ liệu cho màn hình.
##XFLD:Message used by SEAL repository for input parameter
START_VIEW_PERSISTENCY_INPUT_PARAMETER=Sử dụng giá trị mặc định "{0}" cho tham số nhập "{1}".
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Elastic compute node keys~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
##XFLD:Message used to reflect disablement of ecn replica
ECN_DISABLE_EXISTING_REPLICA=Bản sao nút tính toán linh hoạt đã vô hiệu hóa.
##XFLD:Message used to reflect recreation of ecn replica
ECN_RECREATE_EXISTING_REPLICA=Bản sao nút tính toán linh hoạt đã được tạo lại.
##XFLD:Message used to reflect re-enablement of ecn replica
ECN_ENABLE_EXISTING_REPLICA=Bản sao nút tính toán linh hoạt đã kích hoạt lại.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Task Schedule Menu key~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message to schedule task
scheduleText=Lập lịch
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Tạo lịch
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Hiệu chỉnh lịch
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Xóa lịch
#XFLD: Refresh frequency field
refreshFrequency=Tần suất làm mới
#XFLD: Refresh frequency field
refreshFrequencyNew=Tần suất
#XFLD: Refresh frequency field
refreshFrequencyNewNew=Tần suất được lên lịch
#XBUT: label for None
none=Không có
#XBUT: label for Real-Time replication state
realtime=Thời gian thực
#XFLD: Label for table column
txtNextSchedule=Thực hiện kế tiếp
#XFLD: Label for table column
txtNextScheduleNew=Lần thực hiện tiếp theo được lên lịch
#XFLD: Label for table column
txtNumOfRecords=Số bản ghi
#XFLD: Label for scheduled link
scheduledTxt=Được lập lịch
#XFLD: LABEL for partially persisted link
partiallyPersisted=Được ổn định một phần
#XFLD: Text for paused text
paused=Đã tạm dừng

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ View Monitor Tasklog Details Action keys~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message for release lock actionn
releaseLockMsg=Nếu việc thực hiện mất nhiều thời gian hơn bình thường, điều này có thể cho thấy rằng nó đã bị lỗi và trạng thái không được cập nhật tương ứng. \r\n Để giải quyết sự cố, bạn có thể mở khóa và thiết lập trạng thái của nó thành không thành công.
#XFLD: Label for release lock dialog
releaseLockText=Mở khóa

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Column header tooltip ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: tooltip for table column
txtViewTooltip=Màn hình được duy trì
#XFLD: tooltip for table column
txtViewDataAccessTooltip=Điều này cho biết tính khả dụng của màn hình
#XFLD: tooltip for table column
txtViewRefreshFreqTooltip=Điều này cho biết liệu lịch trình có được xác định cho màn hình hay không
#XFLD: tooltip for table column
txtViewStatusTooltip=Lấy trạng thái của màn hình được duy trì
#XFLD: tooltip for table column
txtViewLatestUpdateTooltip=Cung cấp thông tin về màn hình được duy trì được cập nhật lần cuối
#XFLD: tooltip for table column
txtViewNextRunTooltip=Nếu lịch trình được thiết lập cho màn hình, hãy xem đến thời điểm nào lần thực hiện kế tiếp được lập lịch
#XFLD: tooltip for table column
txtViewNumOfRecordsTooltip=Theo dõi số bản ghi.
#XFLD: tooltip for table column
txtViewInMemorySizeTooltip=Theo dõi màn hình đang sử dụng dung lượng bao nhiêu trong bộ nhớ của bạn
#XFLD: tooltip for table column
txtViewDiskSizeTooltip=Theo dõi màn hình chiếm dung lượng bao nhiêu trên đĩa của bạn
#XMSG: Expired text
txtExpired=Đã hết hạn

#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Đối tượng ''{0}'' không thể được thêm vào chuỗi tác vụ.

#~~~~~~~~~~~View Analyzer task log messages~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message indicating number of records for a view, and memory consumption in Mebibyte during simulation of view persistency
RECORDS_PEAK_MEMORY=Màn hình “{0}” có {1} bản ghi. Việc mô phỏng quá trình ổn định dữ liệu cho màn hình này đã sử dụng {2} MiB bộ nhớ.
#XMSG: View Analyzer execution failure
VIEW_ANALYZER_EXECUTION_FAILED=Thực hiện của Công cụ phân tích màn hình không thành công.
#XMSG: Missing authorizations for View Analyzer
VIEW_ANALYZER_MISSING_AUTH=Thiếu phân quyền cho Công cụ phân tích màn hình.
#XMSG: Maximum memory in Gibibyte is reached, therefore persistency simulation is stopped
MAXIMUM_MEMORY_REACHED=Đã đạt đến bộ nhớ tối đa {0} GiB trong khi mô phỏng quá trình ổn định dữ liệu cho màn hình ''’{1}''. Do đó, sẽ không có thêm lần chạy mô phỏng quá trình ổn định dữ liệu nào nữa.
#XMSG: Error during persistency simulation occurred
PERSISTENCY_SIMULATION_ERROR=Đã xảy ra lỗi trong khi mô phỏng quá trình ổn định dữ liệu cho màn hình ''{0}''.
#XMSG: Persistency simulation is not executed, because prerequisites are not fulfilled and the view cannot be persisted
PREREQUISITES_NOT_FULFILLED=Việc mô phỏng quá trình ổn định dữ liệu không được thực thi cho màn hình ''{0}'', bởi vì các điều kiện tiên quyết không được đáp ứng và màn hình không thể được ổn định.
#XMSG: Persistency simulation is not executed, because the view does not exist in the database
VIEW_DOES_NOT_EXIST=Bạn phải triển khai màn hình "{0}" để bật chức năng mô phỏng tính ổn định dữ liệu.
#XMSG: Number of records cannot be determined, because the local table does not exist in the database
TABLE_DOES_NOT_EXIST=Bảng cục bộ "{0}" không tồn tại trong cơ sở dữ liệu, vì thế không thể xác định số lượng bản ghi cho bảng này.
##XMSG: Message start of cancellation for View Analyzer task
CANCEL_VIEW_ANALYZER_TRIGGERED=Quy trình để dừng tác vụ Trình phân tích màn hình {0} cho màn hình ''{1}'' đã được gửi. Có thể có độ trễ cho đến khi tác vụ được dừng.
##XMSG: Message start to indicate that task is not active
CANCEL_VIEW_ANALYZER_NO_ACTIVE_TASK=Tác vụ Trình phân tích màn hình {0} cho màn hình ''{1}'' không hoạt động.
#XMSG: Cancel View Analyzer task failure
CANCEL_VIEW_ANALYZER_FAILED=Không thể hủy tác vụ Công cụ phân tích màn hình.
##XMSG: Message used in View Analyzer execution task when task is canceled
VIEW_ANALYZER_CANCELED=Thực hiện trình phân tích màn hình cho màn hình "{0}" đã bị dừng thông qua tác vụ hủy.
##XMSG: Message start of cancelation for Model Validation task
CANCEL_MODEL_VALIDATION_TRIGGERED=Quy trình để dừng tác vụ Xác thực mô hình {0} cho màn hình "{1}" đã được gửi. Có thể có độ trễ cho đến khi tác vụ được dừng.
##XMSG: Message to indicate that task is not active
CANCEL_MODEL_VALIDATION_NO_ACTIVE_TASK=Tác vụ xác thực mô hình {0} cho màn hình "{1}" không hoạt động.
#XMSG: Cancel Model Validation task failure
CANCEL_MODEL_VALIDATION_FAILED=Không thể hủy tác vụ xác thực mô hình.
##XMSG: Message used in Model Validation task when task is canceled
MODEL_VALIDATION_CANCELED=Thực hiện xác thực mô hình cho màn hình "{0}" đã bị dừng thông qua tác vụ hủy.
#XMSG: Message for Locked space error, before starting Model Validation task
MODEL_VALIDATION_LOCKED_SPACE_ERROR=Không thể thực hiện xác thực mô hình cho màn hình "{0}", vì vùng dữ liệu "{1}" bị khóa.
##XMSG: Message used in View Analyzer execution task when determination of number of rows fails
NUMBER_ROWS_DETERMINATION_FAILED=Đã xảy ra lỗi trong khi xác định số lượng hàng cho bảng cục bộ ''{0}''.
##XMSG: Message to indicate that SQL plan was created
EXECUTION_PLAN_CREATED=Tập tin kế hoạch Trình phân tích SQL cho màn hình ''{0}'' được tạo và có thể tải xuống.
#XFLD: Message to indicate starting of process to generate SQL Analyzer plan file
START_PLANVIZ_EXECUTION=Đang bắt đầu quy trình tạo tập tin kế hoạch Trình phân tích SQL cho màn hình ''{0}''.
#XFLD: Message to indicate starting of View Analyzer process
VIEW_ANALYZER_EXECUTION_STARTED=Đang khởi động thực hiện Trình phân tích màn hình.
##XMSG: Message to indicate that SQL plan file cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_PLANVIZ_NOT_FULFILLED=Tập tin kế hoạch Trình phân tích SQL không thể được tạo cho màn hình ''{0}’ vì điều kiện tiên quyết cho quá trình ổn định dữ liệu không được đáp ứng.
##XMSG: Error message during  generation of SQL Analyzer plan file
PLANVIZ_ERROR=Đã xảy ra lỗi trong quá trình tạo tập tin kế hoạch Trình phân tích SQL cho màn hình ''{0}''.
#XMSG: Message for Locked space error
VIEW_ANALYZER_LOCKED_SPACE_ERROR=Không thể thực thi Trình phân tích màn hình cho màn hình ''{0}'' vì vùng dữ liệu ''{1}'' bị khóa.
#XMSG: Message for persistency simulation without partitioning
PERSISTENCY_SIMULATION_WITHOUT_PARTITIONS=Các phân vùng của màn hình ''’{0}'' không được xem xét trong quá trình mô phỏng ổn định dữ liệu.
#XMSG: Message for generation of SQL Analyzer plan file without considering partitioning
PLANVIZ_WITHOUT_PARTITIONS=Các phân vùng của màn hình ''’{0}'' không được xem xét trong quá trình tạo tập tin kế hoạch Trình phân tích SQL.
#XMSG: Confirmation message for mass removal of persisted data
MassRemovePersistency_Confirm_Msg=Bạn có muốn loại bỏ dữ liệu được duy trì ổn định và chuyển quyền truy cập dữ liệu trở lại quyền truy cập ảo không?
#XMSG: Confirmation message for partial mass removal of selected persisted data
PartialMassRemovePersistency_Confirm_Msg={0} trong số {1} màn hình đã chọn có dữ liệu được duy trì ổn định. \n Bạn có muốn loại bỏ dữ liệu được duy trì ổn định và chuyển quyền truy cập dữ liệu trở lại quyền truy cập ảo không?
#XMSG: success message for stopping mass persistence
stopMassPersistenceSuccess=Chúng tôi đang loại bỏ dữ liệu được duy trì ổn định cho các bảng đã chọn.
#XMSG: error message during mass stop persistence of a view (backend error)
STOP_MASS_PERSISTENCE_ERROR=Đã xảy ra lỗi khi dừng ổn định các màn hình đã chọn.
#XMSG: memory analysis is only performed in space {0}
NO_MEMORY_ANALYSIS=Phân tích bộ nhớ chỉ được thực hiện cho thực thể trong vùng dữ liệu "{0}": "{1}" "{2}" bị bỏ qua.
##XMSG: Message to indicate that Explain Plan cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_EXPLAINPLAN_NOT_FULFILLED=Tập tin Giải thích kế hoạch không thể được tạo cho màn hình "{0}", vì điều kiện tiên quyết cho quá trình ổn định không được đáp ứng.
#XMSG: Message for Explain Plan generation without partitioning
EXPLAINPLAN_WITHOUT_PARTITIONS=Các phân vùng của màn hình "{0}" không được xem xét trong quá trình tạo tập tin Giải thích kế hoạch.
#XFLD: Message to indicate starting of process to generate Explain Plan file
START_EXPLAINPLAN_EXECUTION=Đang bắt đầu quy trình tạo tập tin Giải thích kế hoạch cho màn hình "{0}".
##XMSG: Message to indicate that Explain Plan was created
EXPLAINPLAN_CREATED=Tập tin Giải thích kế hoạch cho màn hình "{0}" đã được tạo ra. Bạn có thể hiển thị tập tin này bằng cách nhấp vào "Xem chi tiết" hoặc tải xuống tập tin nếu bạn có quyền phù hợp.
##XMSG: Error message during  generation of Explain Plan file
EXPLAINPLAN_ERROR=Đã xảy ra lỗi trong quá trình tạo tập tin Giải thích kế hoạch cho màn hình "{0}".
##XMSG: Explain Plan file cannot be generated for the view {0}, because too many views are stacked on each other
EXPLAINPLAN_MODEL_TOO_COMPLEX=Không thể tạo ra tập tin Giải thích kế hoạch cho màn hình "{0}". Có quá nhiều màn hình được xếp chồng lên nhau. Các mô hình phức tạp có thể gây ra lỗi hết bộ nhớ và hiệu suất chậm. Nên duy trì màn hình.

##XMSG: Message to indicate that the space is locked
PERFORMANCE_ANALYSIS_LOCKED_SPACE_ERROR=Không thể thực thi phân tích hiệu suất cho màn hình ''{0}'' vì vùng dữ liệu ''{1}'' bị khóa.
##XMSG: The performance analysis task was cancelled
PERFORMANCE_ANALYSIS_CANCELED=Phân tích hiệu suất cho màn hình "{0}" đã bị hủy.
##XMSG: The performance analysis failed
PERFORMANCE_ANALYSIS_FAILED=Phân tích hiệu suất không thành công.
##XMSG: The performance analysis is finished
PERFORMANCE_ANALYSIS_FINISHED=Phân tích hiệu suất cho màn hình "{0}" đã hoàn tất. Hiển thị kết quả bằng cách nhấp vào "Chi tiết màn hình".
##XMSG: The view has input parameter with no default value
INPUT_PARAMETER_WITHOUT_DEFAULT_VALUE=Không thể phân tích màn hình này vì nó có tham số mà không có giá trị mặc định nào.
##XMSG: The view is not fully deployed
VIEW_NOT_FULLY_DEPLOYED=Không thể phân tích màn hình này vì nó chưa được triển khai đầy đủ.
##XMSG: The view uses limited remote adapters
LIMITED_REMOTE_ADAPTER=Màn hình này sử dụng ít nhất một bộ điều hợp từ xa có khả năng hạn chế như thiếu chức năng đẩy bộ lọc xuống hoặc hỗ trợ cho tính 'Tổng số'. Việc duy trì hoặc sao chép các đối tượng có thể cải thiện hiệu suất thời gian chạy.
##XMSG: The view uses remote adapter that does not support 'Limit'
REMOTE_ADAPTER_NOT_SUPPORTING_LIMIT=Màn hình này sử dụng ít nhất một bộ điều hợp từ xa không hỗ trợ 'Giới hạn'. Có thể đã chọn hơn 1000 bản ghi.
##XMSG: Performance analysis is executed by using default parameters of view'
INPUT_PARAMETER_WITH_DEFAULT_VALUE=Phân tích hiệu suất được thực hiện bằng cách sử dụng các giá trị mặc định của tham số màn hình.
##XMSG: Message to indicate that an error occurred
PERFORMANCE_ANALYSIS_ERROR=Đã xảy ra lỗi trong quá trình phân tích hiệu suất cho màn hình "{0}".
##XMSG: Message start of cancellation for Performance Analysis task
CANCEL_PERFORMANCE_ANALYSIS_TRIGGERED=Quy trình để dừng tác vụ Phân tích hiệu suất {0} cho màn hình ''{1}'' đã được gửi. Có thể có độ trễ cho đến khi tác vụ được dừng.
##XMSG: Message start to indicate that task is not active
CANCEL_PERFORMANCE_ANALYSIS_NO_ACTIVE_TASK=Tác vụ Phân tích hiệu suất {0} cho màn hình "{1}" không hoạt động.
#XMSG: Cancel Performance Analysis task failure
CANCEL_PERFORMANCE_ANALYSIS_FAILED=Không thể hủy tác vụ Phân tích hiệu suất.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Gán lịch cho tôi
#XBUT: Pause schedule menu label
pauseScheduleLabel=Tạm dừng lịch
#XBUT: Resume schedule menu label
resumeScheduleLabel=Tiếp tục lịch
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Đã xảy ra lỗi khi loại bỏ lịch.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Đã xảy ra lỗi khi gán lịch.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Đã xảy ra lỗi khi tạm dừng bỏ lịch.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Đã xảy ra lỗi khi tiếp tục lịch.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Xóa {0} lịch
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Thay đổi chủ sở hữu {0} lịch
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Tạm dừng {0} lịch
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Tiếp tục {0} lịch
#XBUT: Select Columns Button
selectColumnsBtn=Lựa chọn cột
#XFLD: Refresh tooltip
TEXT_REFRESH=Làm mới
#XFLD: Select Columns tooltip
text_selectColumns=Lựa chọn cột


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Runtime Metrics ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Label for Runtime Metrics
RuntimeMetricsTitle=Số liệu thời gian chạy cho
#XFLD : Label for Run Button
runButton=Thực hiện
#XFLD : Label for Cancel Button
cancelButton=Hủy
#XFLD : Label for Close Button
closeButton=Đóng
#XFLD : Label for Open View Analyzer Button
openViewAnalyzerButton=Mở trình phân tích màn hình
#XFLD : Label for Generate Explain Plan Button
generateExplainPlanButton=Tạo ra Giải thích kế hoạch
#XFLD : Label for Previous Run Column
previousRun=Lần thực hiện trước đó
#XFLD : Label for Latest Run Column
latestRun=Lần thực hiện gần nhất
#XFLD : Label for time Column
time=Thời gian
#XFLD : Label for Duration Column
duration=Khoảng thời gian
#XFLD : Label for Peak Memory Column
peakMemory=Bộ nhớ cao nhất
#XFLD : Label for Number of Rows
numberOfRows=Số lượng hàng
#XFLD : Label for Overall Number of Sources
overallNumberOfSources=Số lượng nguồn tổng thể
#XFLD : Label for Data Access Column
dataAccess=Truy cập dữ liệu
#XFLD : Label for Local Tables
localTables=Bảng cục bộ
#XFLD : Label for federatedRemoteTables
federatedRemoteTables=Bảng từ xa liên kết (với khả năng bộ điều hợp bị hạn chế)
#XTXT Text for initial state of the runtime metrics
initialState=Trước tiên, bạn phải chạy Phân tích hiệu suất để lấy số liệu. Việc này có thể mất một thời gian, nhưng bạn có thể hủy quy trình nếu cần.
#XMSG: Cancel confirmation message
CancelRunConfirmationMessage=Bạn có chắc chắn muốn hủy lần thực hiện Phân tích hiệu suất làm việc hiện tại không?
#XTIT: Cancel dialog title
CancelRunTitle=Hủy thực hiện
#XFLD: Label for Number of Rows
NUMBER_ROWS=Số lượng hàng
#XFLD: Label for Overall Number of Sources
OVERALL_NUMBER_SOURCES=Số lượng nguồn tổng thể
#XFLD: Label for Data Access
DATA_ACCESS=Truy cập dữ liệu
#XFLD: Label for Remote tables
NUMBER_REMOTE_TABLES_LIMITED_ADAPTERS=Bảng từ xa liên kết (với khả năng bộ điều hợp bị hạn chế)
#XFLD: Label for select statement
SELECT_STATEMENT='CHỌN * TỪ GIỚI HẠN MÀN HÌNH 1000'
#XFLD: Label for duration
SELECT_RUNTIME=Khoảng thời gian
#XFLD: Label for peak memory
SELECT_PEAK_MEMORY=Bộ nhớ cao nhất
#XFLD: Label for count statement
SELECT_COUNT_STATEMENT='CHỌN TỔNG SỐ(*) TỪ MÀN HÌNH'
#XFLD: Label for count duration
SELECT_COUNT_RUNTIME=Khoảng thời gian
#XFLD: Label for count peak memory
SELECT_COUNT_PEAK_MEMORY=Bộ nhớ cao nhất
#XFLD: Label for count of number of local tables
NUMBER_LOCAL_TABLES_FILE=Bảng cục bộ (Tập tin)
#XTXT: Text for running state of the runtime metrics
Running=Đang chạy...
#XFLD: Label for time
Time=Thời gian
#XFLD: Label for virtual access
PA_VIRTUAL=Ảo
#XFLD: Label for persisted access
PA_PERSISTED=Được duy trì
PA_PARTIALLY_PERSISTED=Được ổn định một phần
#XTXT: Text for cancel
CancelRunSuccessMessage=Hủy thực hiện Phân tích hiệu suất làm việc.
#XTXT: Text for cancel error
CancelRunErrorMessage=Đã xảy ra lỗi khi hủy thực hiện Phân tích hiệu suất làm việc.
#XTXT: Text for explain plan generation
ExplainPlanStarted=Đang tạo Giải thích kế hoạch cho màn hình "{0}".
#XTXT: Text for performance analysis
PerformanceAnalysisStarted=Đang bắt đầu Phân tích hiệu suất làm việc cho màn hình "{0}".
#XTXT: Text for performance analysis error
ErrorFetchingPerformanceAnalysis=Đã xảy ra lỗi khi tải dữ liệu Phân tích hiệu suất làm việc.
#XTXT: Text for performance analysis error
conflictingTask=Tác vụ Phân tích hiệu suất làm việc đang chạy
#XFLD: Label for Errors
Errors=Lỗi
#XFLD: Label for Warnings
Warnings=Cảnh báo
#XFLD: Tooltip for missing privileges
openViewAnalyzerButtonTooltipNoPrivilege=Bạn cần đặc quyền DWC_DATAINTEGRATION(cập nhật) để mở Trình phân tích màn hình.
#XFLD: Tooltip for missing privileges
generateExplainPlanButtonTooltipNoPrivilege=Bạn cần đặc quyền DWC_RUNTIME(đọc) để tạo Giải thích kế hoạch.



#XFLD: Label for frequency column
everyLabel=Mỗi
#XFLD: Plural Recurrence text for Hour
hoursLabel=Giờ
#XFLD: Plural Recurrence text for Day
daysLabel=Ngày
#XFLD: Plural Recurrence text for Month
monthsLabel=Tháng
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Phút
