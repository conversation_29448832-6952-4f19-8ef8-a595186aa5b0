
#XBCB: Breadcrumb
viewMonitorBreadcrumb={spaceId}

#XFLD: Label for persisted view column
SOURCE=Källa
#XFLD: Label for persisted view column
NAME=Namn
#XFLD: Label for persisted view column
NAME_LABEL=Affärsnamn
#XFLD: Label for persisted view column
NAME_LABELNew=Objekt (affärsnamn)
#XFLD: Label for persisted view column
TECHINCAL_NAME=Tekniskt namn
#XFLD: Label for persisted view column
TECHINCAL_NAMENew=Objekt (tekniskt namn)
#XFLD: Label for persisted view column
DATA_PERSISTENCY=Dataåtkomst
#XFLD: Label for persisted view column
STATUS=Status
#XFLD: Label for persisted view column
LAST_UPDATED=Senast uppdaterad
#XFLD: Label for persisted view column
IN_MEMORY_SIZE=Minne använt för lagring (MiB)
#XFLD: Label for persisted view column
DISK_SIZE=Disk använd för lagring (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_NEWW=Storle<PERSON> för minnes<PERSON>rad (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_HEADER=Storlek för minnesbaserad
#XFLD: Label for persisted view column
DISK_SIZE_NEWW=Storlek på disk (MiB)
#XFLD: Label for persisted view column
DISK_SIZE_HEADER=Storlek på disk
#XFLD: Label for schedule owner column
txtScheduleOwner=Schemaägare
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Visar vem som skapade schemat
#XFLD: Label for persisted view column
PERSISTED=Persisterad
#XFLD: Label for persisted view column
TYPE=Typ
#XFLD: Label for View Selection Dialog column
changedOn=Ändring den
#XFLD: Label for View Selection Dialog column
createdBy=Uppläggning av
#XFLD: Label for log details column
txtViewPersistencyLogs=Visa protokoll
#XFLD: Label for log details column
txtViewPersistencyLogsNew=Detaljer
#XFLD: text for values shown for Ascending sort order
SortInAsc=Sortera stigande
#XFLD: text for values shown for Descending sort order
SortInDesc=Sortera fallande
#XFLD: Label for persisted view monitor heading
ViewMonitor_Heading=Vymonitor
#XFLD: Label for persisted view monitor sub-heading
ViewMonitor_SubHeading=Övervaka och underhåll datapersistens för vyer


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ DynamicPageHeader ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: text for values shown in column Persistence Status
txtLoading=Läser in
#XFLD: text for values shown in column Persistence Status
txtRunning=Körs
#XFLD: text for values shown in column Persistence Status
txtAvailable=Tillgänglig
#XFLD: text for values shown in column Persistence Status
txtError=Fel
#XMSG: Error message for unknown Persistence type that gets logged in Kibana
unknownReplicationTypeError=Replikeringstyp "{0}" medges ej.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Content ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Inställningar som användes för senaste datapersistenskörning:
#XMSG: Message for input parameter name
inputParameterLabel=Inparameter
#XMSG: Message for input parameter value
inputParameterValueLabel=Värde
#XMSG: Message for persisted data
inputParameterPersistedLabel=Persisterad kl.
#XTIT: Count for View in ViewMonitor Toolbar
txtViewCount=Vyer ({0})
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Vypersistens
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Datapersistens
#XBUT: Button to clear the selected view persistance
ClearViewPersistance=Rensa
#XBUT: Button to stop the selected view persistance
stopPersistance=Stoppa persistens
#XFLD: Placeholder for Search field
txtSearch=Sök
#XBUT: Tooltip for refresh button
txtRefresh=Uppdatera
#XBUT: Tooltip for add view button
txtDeleteView=Radera persistens
#XBUT: Tooltip for load new peristence
loadNewPersistence=Starta om persistens
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Läs in ny snapshot
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Starta datapersistens
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Ta bort persisterade data
#XMSG: success message for starting persistence
startPersistenceSuccess=Persistering utförs för vy "{0}".
#XMSG: success message for stopping persistence
stopPersistenceSuccess=Persisterade data tas bort för vy "{0}".
#XMSG: success message for stopping persistence
deletePersistedViewSuccess=Vy "{0}" tas bort från övervakningslistan.
#XMSG: error message during start persistence of a view (backend error)
ERROR_STARTING_PERSISTENCE=Ett fel inträffade vid start av datapersistens för vy "{0}".
#XMSG: error message during start persistence of a view (backend error)
PARAMERROR_STARTING_PERSISTENCE=Vy "{0}" kan inte persisteras eftersom den innehåller inparametrar.
#XMSG: error message during start persistence of a view (backend error)
NUMBER_OF_PARAMETER_STARTING_PERSISTENCE=Vy "{0}" kan inte persisteras eftersom den har mer än en inparameter.
#XMSG: error message during start persistence of a view (backend error)
PARAMETER_DEFAULT_VALUE_STARTING_PERSISTENCE=Vy "{0}" kan inte persisteras eftersom inparametern saknar standardvärde.
#XMSG: error message during start persistence of a view (backend error)
REDEPLOYMENT_DAC_REQUIRED=Dataåtkomstkontroll "{0}" måste distribueras på nytt för att medge datapersistens.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_STARTING_PERSISTENCE=Vy "{0}" kan inte persisteras eftersom den använder vy "{1}" som har dataåtkomstkontroll.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_EXTERNAL_STARTING_PERSISTENCE=Vy "{0}" kan inte persisteras eftersom den använder en vy med dataåtkomstkontroll som tillhör ett annat utrymme.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_STRUCTURE_STARTING_PERSISTENCE=Vy "{0}" kan inte persisteras eftersom strukturen för en eller flera av dess dataåtkomstkontroller inte medger datapersistens.
#XMSG: error message during stop persistence of a view (backend error)
STOP_PERSISTENCE_ERROR=Ett fel inträffade vid stopp av persistens för vy "{0}".
#XMSG: error message during stop persistence of a view (backend error)
DELETE_PERSISTED_VIEW_ERROR=Ett fel inträffade vid radering av persisterad vy "{0}".
#XMSG: Warning message for removing the persisted data
RemovePersistency_Confirm_Msg=Vill du radera persisterade data och växla till virtuell åtkomst till vy "{0}"?
#XMSG: Warning message for delete the view persistence
DeletePersistency_Confirm_Msg=Vill du ta bort vyn från övervakningslistan och radera persisterade data för vy "{0}"?
#XMSG: error message for reading data from backend
txtReadBackendError=Ett fel ser ut att ha inträffat vid läsning från backend.
#XFLD: Label for No Data Error
NoDataError=Fel
#XMSG: message for conflicting task
Task_Already_Running=Uppgift i konflikt körs redan för vy "{0}".

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Show All Views~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Text for show all views button
allViewsText=Alla vyer ({0})
#XBUT: Text for show scheduled views button
scheduledText=Inplanerade ({0})
#XBUT: Text for show persisted views button
persistedText=Persisterade ({0})
#XBUT: Text for start analyzer button
startAnalyzer=Starta vyanalys
#XFLD: Message if repository is unavailable
repositoryErrorMsg=Repository är inte tillgängligt och vissa funktioner har inaktiverats.

#XFLD: Data Access - Virtual
Virtual=Virtuell
#XFLD: Data Access - Persisted
Persisted=Persisterad

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Non-Persisted View Selection Dialog~~~~~~~~~~~~~~~~~~~~~~~

#XTIT: Title for Non-Persisted View selection dialog
select_source=Välj vy att persistera

#XTIT: Placeholder for Non-Persisted View selection dialog search
searchView=Sök vyer
#XTIT: No data in the list of non-persisted view
No_Data=Inga data
#XBUT: Button to select non-persisted view
ok=OK
#XBUT: Button to close the non-persisted views selection dialog
cancel=Avbryt

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~View persistency log message key~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message for view persistency logs
START_STOP_PERSISTENCY=Startar körning av datapersistensuppgift för "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY=Persisterar data för vy "{1}".
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY)
START_VIEW_PERSISTENCY_PROCESS=Startar processen för att persistera data för vy "{0}".
#XFLD: Message for view persistency manual partition load
START_VIEW_PERSISTENCY_PROCESS_MANUAL_PARTITIONS=Startar processen för att persistera data för vy "{0}" med valda partitions-ID: "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY=Persisterade data tas bort för vy "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY)
REMOVE_VIEW_PERSISTENCY=Startar processen för att ta bort persisterade data för vy "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_SUCCESS=Data har persisterats för vy "{1}".
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_SUCCESS)
START_VIEW_PERSISTENCY_PROCESS_SUCCESS=Data har persisterats för vy "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY_SUCCESS=Persisterade data har tagits bort och virtuell dataåtkomst har återställts för vy "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_SUCCESS)
REMOVE_VIEW_PERSISTENCY_SUCCESS=Processen för att ta bort persisterade data för vy "{0}" har slutförts.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_FAILED=Data kunde inte persisteras för vy "{1}".
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_FAILED)
START_VIEW_PERSISTENCY_PROCESS_FAILED=Data kunde inte persisteras för vy "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.05)
STOP_VIEW_PERSISTENCY_FAILED=Persisterade data kunde inte tas bort för vy "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_FAILED)
REMOVE_VIEW_PERSISTENCY_FAILED=Persisterade data kunde inte tas bort för vy "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_SUCCESS="{3}" poster har persisterats för vy "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace START_PERSISTENCY_RECORD_COUNT_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS={0} poster har infogats i datapersistenstabell för vy "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS_V2={0} poster har infogats i datapersistenstabell för vy "{1}". Använt minne: {2} GB.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_SUCCESS="{3}" persisterade poster har tagits bort för vy "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace STOP_PERSISTENCY_RECORD_COUNT_SUCCESS)
REMOVE_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=Persisterade data har tagits bort, "{0}" persisterade poster har raderats.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_FAILED=Antal poster kunde inte hämtas för vy "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_FAILED=Antal poster kunde inte hämtas för vy "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Schema har raderats för "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS)
REMOVE_VIEW_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Schema har raderats för vy "{0}".
#XFLD: Message for view persistency logs
STOP_PERSISTENCY_DELETE_SCHEDULE_FAILED=Schema kunde inte raderas för "{1}".
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_REDEPLOY=Vi kan inte persistera vy "{0}" eftersom den har ändrats och implementerats sedan du började persistera den. Försök att persistera vyn igen eller vänta till nästa planlagda körning.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_DELETE=Vi kan inte persistera vy "{0}" eftersom den har raderats sedan du började persistera den.
#XFLD: Message for successful data persistency of a single partition (for messages created before version 2022.03)
START_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} poster har persisterats till partition för värden "{1}" <= {2} < "{3}".
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (new message to replace START_PERSISTENCY_SINGLE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} poster har infogats i partition för värden "{1}" <= "{2}" < "{3}".
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS_V2={0} poster har infogats i partition för värden "{1}" <= "{2}" < "{3}". Använt minne: {4} GB.
#XFLD: Message for successful replication of a "others" partition (for messages created before version 2022.03)
START_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} poster har persisterats till partition "andra".
#XFLD: Message used by SEAL repository for successful replication of a "others" partition (new message to replace START_PERSISTENCY_OTHERS_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} poster har infogats i partition "andra".
#XFLD: Message for view persistency record count of partitions (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={3} poster har persisterats för vy "{1}" i {4} partitioner.
#XFLD: Message used by SEAL repository for view persistency record count of partitions (new message to replace START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={0} poster har infogats i datapersistenstabell för vy "{1}" i {2} partitioner.
#XFLD: Message used by SEAL repository for view persistency for partition wise refresh
START_VIEW_PERSISTENCY_RECORD_COUNT_REFRESHED_PARTITIONS_SUCCESS={0} poster har infogats i datapersistenstabell för vy "{1}". Uppdaterade partitioner: {2}; Spärrade partitioner: {3}; Partitioner totalt: {4}
#XFLD: Message used by SEAL repository for view persistency for manual partition load
START_VIEW_PERSISTENCY_RECORD_COUNT_MANUAL_PARTITIONS_SUCCESS={0} poster har infogats i datapersistenstabell för vy  "{1}" i {2} valda partitioner
#XFLD: Message used by SEAL repository for view persistency for locked partition data load
START_VIEW_PERSISTENCY_RECORD_COUNT_LOCKED_PARTITIONS_SUCCESS={0} poster har infogats i datapersistenstabell för vy "{1}". Uppdaterade partitioner: {2}; Spärrade oförändrade partitioner: {3}; Partitioner totalt: {4}.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
START_PERSISTENCY_UNEXPECTED_ERROR=Oväntat fel vid persistering av data för vy "{0}".
#XMSG: Message for unexpected error (new message to replace START_PERSISTENCY_UNEXPECTED_ERROR)
START_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Oväntat fel vid persistering av data för vy "{0}".
#XMSG: Message for Locked space error
START_VIEW_PERSISTENCY_LOCKED_SPACE_ERROR=Vyn "{0}" kan inte persisteras eftersom utrymme "{1}" är spärrat.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
STOP_PERSISTENCY_UNEXPECTED_ERROR=Oväntat fel när persisterade data för vy "{0}" togs bort.
#XMSG: Message for unexpected error (new message to replace STOP_PERSISTENCY_UNEXPECTED_ERROR)
REMOVE_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Oväntat fel när persistens för vy "{0}" togs bort.
#XMSG: Message for ivalidated view
VIEW_PERSISTENCY_ABORTED_DEFINITION_INVALID=Definition av vy "{0}" har blivit ogiltig, troligen på grund av en ändring av ett objekt som används direkt eller indirekt av vyn. Försök omdistribuera vyn för att lösa problemet eller identifiera grundorsaken.
#XMSG: Message for persisted data removal (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT=Persisterade data tas bort under distribution av vy "{0}".
#XMSG: Message for persisted data removal of a consuming view
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW=Persisterade data tas bort under distribution av förbrukningsvy "{0}".
#XMSG: Message for persisted data removal of a consuming view due to a dac change
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW_DAC_CHANGED=Persisterade data tas bort under distribution av förbrukningsvy "{0}" eftersom dess dataåtkomstkontroll har ändrats.
#XMSG: Message for persisted data removal (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DEPLOYMENT=Persisterade data tas bort under distribution av vy "{0}".
#XMSG: Message for deleted view (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION=Persistens tas bort vid radering av vy "{0}".
#XMSG: Message for deleted view (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DELETION=Persistens tas bort vid radering av vy "{0}".
#XMSG Message for removal of persisted data, because prerequisites not fulfilled
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_PREREQUISITES_NOT_FULFILLED=Persisterade data har tagits bort eftersom datapersistensförutsättningar inte längre uppfylls.
#XMSG: Message for inconsistent persistency
VIEW_PERSISTENCY_INCONSISTENT_PERSISTENCY=Persistens för vy "{0}" har blivit inkonsistent. Ta bort persisterade data för att lösa problemet.
#XMSG: Message for prerequisites
START_PERSISTENCY_CHECK_PREREQUISITES=Kontrollerar förutsättningarna för persistens för vy "{0}".
#XMSG: Message for DAC deployed in deprecated way using table function and join (for messages created before 2025.05)
START_PERSISTENCY_DEPRECIATED_DAC=Vy "{0}" är distribuerad med dataåtkomstkontroll som håller på att fasas ut. Distribuera vyn igen för att förbättra prestandan.
#XMSG: Message for DAC deployed in deprecated way using table function and join
START_PERSISTENCY_DEPRECATED_DAC=Vy "{0}" är distribuerad med dataåtkomstkontroll som håller på att fasas ut. Distribuera vyn igen för att förbättra prestandan.
#XMSG: Message for roll back (for messages created before version 2022.03)
ROLLBACK_VIEW_PERSISTENCY=Ett fel inträffade. Föregående status för persistens har återställts för vy "{0}".
#XMSG: Message used by SEAL repository for rollback of running procedure to persist view (new message to replace ROLLBACK_VIEW_PERSISTENCY)
VIEW_PERSISTENCY_ROLLBACK=Ett fel inträffade. Process för att persistera vy "{0}" har stoppats och ändringar har återställts.
#XMSG: Message used by SEAL repository for rollback of running procedure to remove persisted data of view
REMOVE_VIEW_PERSISTENCY_ROLLBACK=Ett fel inträffade. Processen för att ta bort persisterade data för vy "{0}" har stoppats och ändringar har återställts.
#XMSG: Message used by SEAL repository for persistency preparation
START_PERSISTENCY_PREPARATION=Förbereder persistering av data.
#XMSG: Message used by SEAL repository for data insertion
START_INSERTING_PERSISTENCY_DATA=Infogar data i persistenstabell.
#XMSG: Message for null values - partitioning (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS={0} nullvärdesposter har infogats i partition "andra".
#XMSG: Message used by SEAL repository for others partition values - partitioning (new message to replace START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS={0} poster har infogats i partition "andra" för värden "{2}" < "{1}" ELLER "{2}" >= "{3}".
#XMSG: Message used by SEAL repository for others partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS_V2={0} poster har infogats i partition "andra" för värden "{2}" < "{1}" ELLER "{2}" >= "{3}". Använt minne: {4} GB.
#XMSG: Message used by SEAL repository for null partition values - partitioning
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS={0} poster har infogats i partition "andra" för värden "{1}" ÄR NULL.
#XMSG: Message used by SEAL repository for null partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS_V2={0} poster har infogats i partition "andra" för värden "{1}" ÄR NULL. Använt minne: {2} GB.
##XFLD:Message used by SEAL repository for remote statistics
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS=Läser in berörda data: {0} fjärrinstruktioner. Totalt antal hämtade poster: {1}. Total tidslängd: {2} sekunder.
##XFLD:Message used by SEAL repository for remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS_WITH_PARTITIONS=Läser in berörda data med hjälp av {0} partitioner med {1} fjärrinstruktioner. Totalt antal hämtade poster: {2}. Total tidslängd: {3} sekunder.
##XFLD:Message used by SEAL repository with UI ID for instructions of remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_INSTRUCTION=Fjärrinstruktionerna som bearbetas under körningen kan visas genom att öppna fjärrfrågemonitorn i detaljerna för de partitionsspecifika meddelandena.
##XFLD:Message for start reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS=Startar processen för att återanvända befintliga persisterade data för vy {0} efter distribution.
##XFLD:Message used by SEAL repository for reattach view persitency
START_VIEW_PERSISTENCY_REATTACHMENT=Startar återanvändning av befintliga persisterade data.
##XFLD:Message used by SEAL repository for reattach view persitency
START_FINILIZING_VIEW_PERSISTENCY_REATTACHMENT=Återanvänder befintliga persisterade data.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_COMPLETE=Processen för att återanvända befintliga persisterade data har slutförts för vy {0}.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_FAILED=Befintliga persisterade data för vy {0} kunde inte återanvändas efter distribution.
#XMSG: Message used by SEAL repository for finalizing persistency
START_FINALIZING_PERSISTENCY=Slutför persistens.
#XMSG: Message used by SEAL repository for restoring virtual access for view
REMOVE_VIEW_PERSISTENCY_VIRTUAL_ACCESS_RESTORED=Virtuell dataåtkomst har återställts för vy "{0}".
#XMSG: Message used by SEAL repository for view that already has virtual access
REMOVE_VIEW_PERSISTENCY_ALREADY_VIRTUAL_ACCESS=Vy "{0}" har redan virtuell dataåtkomst. Inga persisterade data tas bort.
#XMSG: Message for removing view persistency
VIEW_PERSISTENCY_REMOVED_FROM_MONITOR=Vy ''{0}'' har tagits bort från vymonitorn.
#XMSG: Message for view doesnt exist.
VIEW_PERSISTENCY_VIEW_NOT_EXISTING=Vy "{0}" finns antingen inte i databasen eller så har den inte distribuerats korrekt och kan därför inte persisteras. Prova att distribuera vyn på nytt för att lösa problemet eller identifiera grundorsaken.
#XMSG: Message for persistency not enabled.
VIEW_PERSISTENCY_NOT_ENABLED=Datapersistens har inte aktiverats. Omdistribuera en tabell/vy i utrymme "{0}" för att aktivera funktionen.
#XMSG: Message for unexpected error due to node crash
VIEW_PERSISTENCY_UNEXPECTED_ERROR_GENERAL=Senaste vypersistenskörning avbröts på grund av tekniska fel.
#XFLD: Message for view persistency memory consumption
VIEW_PERSISTENCY_MEMORY_CONSUMPTION={0} GB av maximalt minne använt för vypersistenskörtid.
#XFLD: Message for view persistency timeout
VIEW_PERSISTENCY_TIMEOUT=Timeout för persistens för vy {0} efter {1} timmar.
#XFLD: Message for not started asynchronous view persistency
VIEW_PERSISTENCY_ASYNC_NOT_STARTED=Hög systembelastning hindrade start av asynkront utförande av vypersistens. Kontrollera så att inte för många uppgifter kör samtidigt.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency (for messages created before version 2022.05)
START_VIEW_PERSISTENCY_EXECUTION_METHOD_DROP_AND_RENAME=Befintlig persisterad tabell har raderats och ersatts med en ny persisterad tabell.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_RECREATE_AND_DROP=Befintlig persisterad tabell har raderats och ersatts med en ny persisterad tabell.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_TRUNCATE_AND_COPY=Befintlig persisterad tabell har uppdaterats med nya data.
##XFLD: Message for missing authorizations for view persistency
VIEW_PERSISTENCY_MISSING_AUTH=Behörigheter saknas för datapersistens.
##XFLD: Message start of persistency cancellation
VIEW_PERSISTENCY_TRIGGER_CANCEL=Startar avbrott av process för att persistera vy {0}.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_UNAVAILABLE_TASK_RUN=Process för att persistera vy kunde inte avbrytas eftersom ingen datapersistensuppgift körs för vy {0}.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_NONDETERMINISTIC_TASK_RUN=Process för att persistera vy kunde inte avbrytas eftersom ingen datapersistensuppgift körs för vy {0}.
##XFLD: Message for canceling a non running persitency task
VIEW_PERSISTENCY_CANCEL_NON_RUNNING_SELECTED_TASK=Process för att persistera vy {0} kunde inte avbrytas eftersom vald datapersistensuppgift {1} inte körs.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_UNREACHABLE_PERSISTENCY_RUN=Process för att persistera vy kunde inte avbrytas eftersom datapersistens för vy {0} inte har startat än.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_COMPLETED_PERSISTENCY_RUN=Process för att persistera vy {0} kunde inte avbrytas eftersom den redan har slutförts.
##XFLD: Message for failed cancellation
VIEW_PERSISTENCY_ABORTED_CANCEL_EXECUTION=Process för att persistera vy {0} kunde inte avbrytas.
##XFLD: Message for finalizing persistency cancellation
VIEW_PERSISTENCY_FINALIZE_CANCEL=Process för att stoppa datapersistens för vy {0} har skickats.
##XFLD: Message used in persist task when cancellation is caught
VIEW_PERSISTENCY_CANCELED=Process för att persistera vy {0} har stoppats.
##XFLD: Message used in persist task when cancelation is caught and performed through a cancelation task
VIEW_PERSISTENCY_CANCELED_VIA_CANCEL_TASK=Process för att persistera vy {0} stoppades via annulleringsuppgift {1}.
##XFLD: Message used in cancel task when the task is triggered by deployment
VIEW_PERSISTENCY_CANCEL_TRIGGERED_BY_DEPLOYMENT=Annullerar process för att persistera data under distribution av vy {0}.
##XFLD: Message used in case the cancelation is retriggered for a running task
VIEW_PERSISTENCY_CANCEL_ATTEMPT_RETRIGGER_CANCEL=En tidigare uppgift för annullering av vypersistens för vy {0} har redan skickats.
##XFLD: Message used to express the delay that may occur before the persistency task is stopped
VIEW_PERSISTENCY_CANCEL_DELAY_STOPPING_PERSISTENCY=Det kan ta en stund innan datapersistensuppgift för vy {0} stoppas.
##XFLD: Message used to reflect the task log identifier of the current running persistency task
VIEW_PERSISTENCY_CANCEL_CURRENT_RUNNING_TASK=Data för vy {0} persisteras med uppgift {1}.
##XFLD:Message used by SEAL repository for locked partitions and DACs
VIEW_PERSISTENCY_LOCKED_PARTITIONS_DACS=Behörigheter som hämtas från dataåtkomstkontroll kan ha ändrats och beaktas inte av spärrade partitioner. Häv spärr för partitioner och läs in en ny snapshot för att tillämpa ändringar.
##XFLD:Message used by SEAL repository for column structure change
VIEW_PERSISTENCY_COLUMN_STRUCTURE_CHANGE=Kolumnstrukturen har ändrats och matchar inte längre befintlig persistenstabell. Ta bort persisterade data och starta en ny datapersistens för att uppdatera din persistenstabell.
##XFLD: Message for out of memory
VIEW_PERSISTENCY_HANA_OUT_OF_MEMORY=Uppgiften misslyckades på grund av ett minnesbristfel i SAP HANA-databasen.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_EXCEPTION_TRANSACTION_ROLLBACK=Uppgiften misslyckades på grund av ett internt undantag i SAP HANA-databasen.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_SQL_EXECUTION_ISSUE=Uppgiften misslyckades på grund av ett internt SQL-körningsfel i SAP HANA-databasen.
##XFLD: Message for HANA out of memory result
VIEW_PERSISTENCY_OUT_OF_MEMORY_RESULT_MESSAGE=Orsak till händelse med för lite minne för HANA: {0}
##XFLD: Message for admission control rejection
VIEW_PERSISTENCY_HANA_ADMISSION_CTRL_REJECTION=Uppgiften misslyckades på grund av avvisning av åtkomstkontroll för SAP HANA.
##XFLD: Message for HANA back pressure / rate limit of HANA client
VIEW_PERSISTENCY_HANA_CLIENT_RATE_LIMIT_REJECTION=Uppgiften misslyckades på grund av för många aktiva SAP HANA-anslutningar.
#XMSG: Message for invalid persisted table name
VIEW_PERSISTENCY_PERSISTED_TABLE_INVALID=Ett fel inträffade och persisterad tabell har blivit ogiltig. Lös problemet genom att ta bort persisterade data och persistera vyn igen.
#XMSG: Message for usahe of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION=Vyn kan inte persisteras. Den använder en fjärrtabell som baseras på en fjärrkälla med aktiverad användarpropagering. Kontrollera vyns ursprung.
#XMSG: Message for dynamic usage of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION_DYNAMIC=Vyn kan inte persisteras. Den använder en fjärrtabell som baseras på en fjärrkälla med aktiverad användarpropagering. Fjärrtabellen kan användas dynamiskt via en SQL-skriptvy. Vyns ursprung visar kanske inte fjärrtabellen.
#XMSG: Message for insufficient privileges or consumption of view with DAC via dynamic SQL
VIEW_PERSISTENCY_INSUF_PRIV_OR_DAC_DYN_SQL=Behörigheterna kan vara otillräckliga. Öppna förhandsgranskning av data för att se om du har erforderliga behörigheter. Om så är fallet kan dataåtkomstkontroll (DAC) användas för en andra vy som konsumeras via dynamiskt SQL-skript.
#XMSG: deployment of DAC is deprecated and view need to be redeployed to be able to persist data
VIEW_PERSISTENCY_DEPRECATED_DAC=Vy "{0}" är distribuerad med dataåtkomstkontroll som håller på att fasas ut. Distribuera vyn igen för att kunna persistera data för vyn.
##XFLD:Message used by SEAL repository for input parameter
START_VIEW_PERSISTENCY_INPUT_PARAMETER=Standardvärde "{0}" för inparameter "{1}" används.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Elastic compute node keys~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
##XFLD:Message used to reflect disablement of ecn replica
ECN_DISABLE_EXISTING_REPLICA=Replikering av elastisk beräkningsnod har inaktiverats.
##XFLD:Message used to reflect recreation of ecn replica
ECN_RECREATE_EXISTING_REPLICA=Replikering av elastisk beräkningsnod har skapats på nytt.
##XFLD:Message used to reflect re-enablement of ecn replica
ECN_ENABLE_EXISTING_REPLICA=Replikering av elastisk beräkningsnod har aktiverats på nytt.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Task Schedule Menu key~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message to schedule task
scheduleText=Schema
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Skapa schema
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Redigera schema
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Radera schema
#XFLD: Refresh frequency field
refreshFrequency=Uppdateringsfrekvens
#XFLD: Refresh frequency field
refreshFrequencyNew=Frekvens
#XFLD: Refresh frequency field
refreshFrequencyNewNew=Inplanerad frekvens
#XBUT: label for None
none=Ingen
#XBUT: label for Real-Time replication state
realtime=Realtid
#XFLD: Label for table column
txtNextSchedule=Nästa körning
#XFLD: Label for table column
txtNextScheduleNew=Inplanerad nästa körning
#XFLD: Label for table column
txtNumOfRecords=Antal poster
#XFLD: Label for scheduled link
scheduledTxt=Inplanerad
#XFLD: LABEL for partially persisted link
partiallyPersisted=Delvis persisterade
#XFLD: Text for paused text
paused=Pausad

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ View Monitor Tasklog Details Action keys~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message for release lock actionn
releaseLockMsg=Om en körning tar längre tid än vanligt kan det indikera att den misslyckades men att status inte har uppdaterats. \r\n För att åtgärda problemet kan du frisläppa spärren och sätta status till misslyckad.
#XFLD: Label for release lock dialog
releaseLockText=Frisläpp spärr

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Column header tooltip ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: tooltip for table column
txtViewTooltip=Namn på persisterad vy
#XFLD: tooltip for table column
txtViewDataAccessTooltip=Detta indikerar tillgänglighet för vyn
#XFLD: tooltip for table column
txtViewRefreshFreqTooltip=Detta indikerar om ett schema är definierat för vyn
#XFLD: tooltip for table column
txtViewStatusTooltip=Hämta status på persisterad vy
#XFLD: tooltip for table column
txtViewLatestUpdateTooltip=Ger information om när persisterad vy uppdaterades senast
#XFLD: tooltip for table column
txtViewNextRunTooltip=Se när nästa körning är inplanerad om schema finns för vy.
#XFLD: tooltip for table column
txtViewNumOfRecordsTooltip=Spåra antal poster.
#XFLD: tooltip for table column
txtViewInMemorySizeTooltip=Spåra hur mycket utrymme vyn använder i minnet
#XFLD: tooltip for table column
txtViewDiskSizeTooltip=Spåra hur mycket utrymme vyn använder på disken
#XMSG: Expired text
txtExpired=Utgången

#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Objekt "{0}" kan inte läggas till i uppgiftskedja.

#~~~~~~~~~~~View Analyzer task log messages~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message indicating number of records for a view, and memory consumption in Mebibyte during simulation of view persistency
RECORDS_PEAK_MEMORY=Vy "{0}" har {1} poster. En simulering av datapersistens för denna vy använde {2} MiB minne.
#XMSG: View Analyzer execution failure
VIEW_ANALYZER_EXECUTION_FAILED=Vyanalys kunde inte utföras.
#XMSG: Missing authorizations for View Analyzer
VIEW_ANALYZER_MISSING_AUTH=Behörigheter saknas för vyanalys.
#XMSG: Maximum memory in Gibibyte is reached, therefore persistency simulation is stopped
MAXIMUM_MEMORY_REACHED=Maximalt minne på {0} GiB har nåtts vid simulering av datapersistens för vy "{1}". Inga ytterligare simuleringar av datapersistens kommer därmed att köras.
#XMSG: Error during persistency simulation occurred
PERSISTENCY_SIMULATION_ERROR=Ett fel inträffade under datapersistenssimulering för vy "{0}".
#XMSG: Persistency simulation is not executed, because prerequisites are not fulfilled and the view cannot be persisted
PREREQUISITES_NOT_FULFILLED=Datapersistenssimulering utförs inte för vy "{0}" eftersom förutsättningarna inte uppfylls och vyn kan inte persisteras.
#XMSG: Persistency simulation is not executed, because the view does not exist in the database
VIEW_DOES_NOT_EXIST=Vyn "{0}" måste distribueras för att datapersistenssimulering ska aktiveras.
#XMSG: Number of records cannot be determined, because the local table does not exist in the database
TABLE_DOES_NOT_EXIST=Lokal tabell "{0}" finns inte i databasen, därför kan inte antal poster bestämmas för denna tabell.
##XMSG: Message start of cancellation for View Analyzer task
CANCEL_VIEW_ANALYZER_TRIGGERED=Process för att stoppa vyanalysuppgift {0} för vy "{1}" har skickats. Det kan dröja lite innan uppgiften stoppas.
##XMSG: Message start to indicate that task is not active
CANCEL_VIEW_ANALYZER_NO_ACTIVE_TASK=Vyanalysuppgift {0} för vy "{1}" är inte aktiv.
#XMSG: Cancel View Analyzer task failure
CANCEL_VIEW_ANALYZER_FAILED=Kunde inte avbryta vyanalysuppgift.
##XMSG: Message used in View Analyzer execution task when task is canceled
VIEW_ANALYZER_CANCELED=Utförande av vyanalys för vy "{0}" stoppades via en annulleringsuppgift.
##XMSG: Message start of cancelation for Model Validation task
CANCEL_MODEL_VALIDATION_TRIGGERED=Process för att stoppa modellvalideringsuppgift {0} för vy "{1}" har skickats. Det kan dröja lite innan uppgiften stoppas.
##XMSG: Message to indicate that task is not active
CANCEL_MODEL_VALIDATION_NO_ACTIVE_TASK=Modellvalideringsuppgift {0} för vy "{1}" är inte aktiv.
#XMSG: Cancel Model Validation task failure
CANCEL_MODEL_VALIDATION_FAILED=Modellvalideringsuppgift kunde inte avbrytas.
##XMSG: Message used in Model Validation task when task is canceled
MODEL_VALIDATION_CANCELED=Utförande av modellvalidering för vy "{0}" stoppades via en annulleringsuppgift.
#XMSG: Message for Locked space error, before starting Model Validation task
MODEL_VALIDATION_LOCKED_SPACE_ERROR=Kan inte utföra modellvalidering för vy "{0}" eftersom utrymme {1} är spärrat.
##XMSG: Message used in View Analyzer execution task when determination of number of rows fails
NUMBER_ROWS_DETERMINATION_FAILED=Ett fel inträffade vid bestämning av antal rader för lokal tabell "{0}".
##XMSG: Message to indicate that SQL plan was created
EXECUTION_PLAN_CREATED=Planeringsfil för SQL-analys för vy "{0}" skapas och kan läsas ned.
#XFLD: Message to indicate starting of process to generate SQL Analyzer plan file
START_PLANVIZ_EXECUTION=Startar process för generering av planeringsfil för SQL-analys för vy "{0}".
#XFLD: Message to indicate starting of View Analyzer process
VIEW_ANALYZER_EXECUTION_STARTED=Startar utförande av vyanalys.
##XMSG: Message to indicate that SQL plan file cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_PLANVIZ_NOT_FULFILLED=Planeringsfil för SQL-analys kan inte genereras för vy "{0}" eftersom datapersistensförutsättningar inte uppfylls.`
##XMSG: Error message during  generation of SQL Analyzer plan file
PLANVIZ_ERROR=Ett fel inträffade vid generering av planeringsfil för SQL-analys för vy "{0}".
#XMSG: Message for Locked space error
VIEW_ANALYZER_LOCKED_SPACE_ERROR=Kan inte utföra vyanalys för vy "{0}" eftersom utrymme "{1}" är spärrat.
#XMSG: Message for persistency simulation without partitioning
PERSISTENCY_SIMULATION_WITHOUT_PARTITIONS=Partitioner för vy "{0}" beaktas inte under datapersistenssimulering.
#XMSG: Message for generation of SQL Analyzer plan file without considering partitioning
PLANVIZ_WITHOUT_PARTITIONS=Partitioner för vy "{0}" beaktas inte vid generering av planeringsfil för SQL-analys.
#XMSG: Confirmation message for mass removal of persisted data
MassRemovePersistency_Confirm_Msg=Vill du ta bort persisterade data och växla tillbaka dataåtkomsten till virtuell åtkomst?
#XMSG: Confirmation message for partial mass removal of selected persisted data
PartialMassRemovePersistency_Confirm_Msg={0} av {1} valda vyer har persisterade data. \n Vill du ta bort persisterade data och växla tillbaka dataåtkomsten till virtuell åtkomst?
#XMSG: success message for stopping mass persistence
stopMassPersistenceSuccess=Vi tar bort persisterade data för valda vyer.
#XMSG: error message during mass stop persistence of a view (backend error)
STOP_MASS_PERSISTENCE_ERROR=Ett fel inträffade vid stopp av persistens för valda vyer.
#XMSG: memory analysis is only performed in space {0}
NO_MEMORY_ANALYSIS=Minnesanalys utförs endast för entiteter i utrymme "{0}", "{1}" "{2}" har hoppats över.
##XMSG: Message to indicate that Explain Plan cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_EXPLAINPLAN_NOT_FULFILLED=Explain-planfil kan inte genereras för vy "{0}" eftersom datapersistensförutsättningar inte uppfylls.
#XMSG: Message for Explain Plan generation without partitioning
EXPLAINPLAN_WITHOUT_PARTITIONS=Partitioner för vy "{0}" beaktas inte vid generering av Explain-planfil.
#XFLD: Message to indicate starting of process to generate Explain Plan file
START_EXPLAINPLAN_EXECUTION=Startar process för generering av Explain-planfil för vy "{0}".
##XMSG: Message to indicate that Explain Plan was created
EXPLAINPLAN_CREATED=Explain-planfil för vy "{0}" har genererats. Du kan visa den genom att klicka på "Visa detaljer" eller läsa ner den om du har rätt behörighet.
##XMSG: Error message during  generation of Explain Plan file
EXPLAINPLAN_ERROR=Ett fel inträffade vid generering av Explain-planfil för vy "{0}".
##XMSG: Explain Plan file cannot be generated for the view {0}, because too many views are stacked on each other
EXPLAINPLAN_MODEL_TOO_COMPLEX=Explain-planfil kan inte genereras för vyn "{0}". För många vyer staplas på varandra. Komplexa modeller kan orsaka minnesbristfel och långsam prestanda. Vi rekommenderar att du persisterar en vy.

##XMSG: Message to indicate that the space is locked
PERFORMANCE_ANALYSIS_LOCKED_SPACE_ERROR=Kan inte utföra prestandaanalys för vy "{0}" eftersom utrymme "{1}" är spärrat.
##XMSG: The performance analysis task was cancelled
PERFORMANCE_ANALYSIS_CANCELED=Prestandaanalys för vy "{0}" avbröts.
##XMSG: The performance analysis failed
PERFORMANCE_ANALYSIS_FAILED=Prestandaanalys misslyckades.
##XMSG: The performance analysis is finished
PERFORMANCE_ANALYSIS_FINISHED=Prestandaanalys för vy "{0}" har avslutats. Visa resultat genom att klicka på "Visa detaljer".
##XMSG: The view has input parameter with no default value
INPUT_PARAMETER_WITHOUT_DEFAULT_VALUE=Vyn kan inte analyseras eftersom den har en parameter utan ett standardvärde.
##XMSG: The view is not fully deployed
VIEW_NOT_FULLY_DEPLOYED=Vyn kan inte analyseras eftersom den inte är helt distribuerad.
##XMSG: The view uses limited remote adapters
LIMITED_REMOTE_ADAPTER=Vyn använder minst en fjärradapter med begränsade funktioner, till exempel saknad filter-push-down eller stöd för "Antal". Persistering eller replikering av objekt kan förbättra körtidsprestanda.
##XMSG: The view uses remote adapter that does not support 'Limit'
REMOTE_ADAPTER_NOT_SUPPORTING_LIMIT=Vyn använder minst en fjärradapter som inte medger "Gräns". Fler än 1000 poster kan ha valts.
##XMSG: Performance analysis is executed by using default parameters of view'
INPUT_PARAMETER_WITH_DEFAULT_VALUE=Prestandaanalys utförs genom att använda standardvärden för vyparametrar.
##XMSG: Message to indicate that an error occurred
PERFORMANCE_ANALYSIS_ERROR=Ett fel inträffade vid prestandaanalys för vy "{0}".
##XMSG: Message start of cancellation for Performance Analysis task
CANCEL_PERFORMANCE_ANALYSIS_TRIGGERED=Process för att stoppa prestandaanalysuppgift {0} för vy "{1}" har skickats. Det kan dröja lite innan uppgiften stoppas.
##XMSG: Message start to indicate that task is not active
CANCEL_PERFORMANCE_ANALYSIS_NO_ACTIVE_TASK=Prestandaanalysuppgift {0} för vy "{1}" är inte aktiv.
#XMSG: Cancel Performance Analysis task failure
CANCEL_PERFORMANCE_ANALYSIS_FAILED=Prestandaanalysuppgift kunde inte avbrytas.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Allokera schema till mig
#XBUT: Pause schedule menu label
pauseScheduleLabel=Pausa schema
#XBUT: Resume schedule menu label
resumeScheduleLabel=Återuppta schema
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Ett fel inträffade när scheman skulle tas bort.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Ett fel inträffade när scheman skulle allokeras.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Ett fel inträffade när scheman skulle pausas.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Ett fel inträffade när scheman skulle återupptas.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Raderar {0} scheman
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Ändrar ägare för {0} scheman
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Pausar {0} scheman
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Återupptar {0} scheman
#XBUT: Select Columns Button
selectColumnsBtn=Välj kolumner
#XFLD: Refresh tooltip
TEXT_REFRESH=Uppdatera
#XFLD: Select Columns tooltip
text_selectColumns=Välj kolumner


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Runtime Metrics ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Label for Runtime Metrics
RuntimeMetricsTitle=Körtidsmått för
#XFLD : Label for Run Button
runButton=Kör
#XFLD : Label for Cancel Button
cancelButton=Avbryt
#XFLD : Label for Close Button
closeButton=Stäng
#XFLD : Label for Open View Analyzer Button
openViewAnalyzerButton=Öppna vyanalys
#XFLD : Label for Generate Explain Plan Button
generateExplainPlanButton=Generera Explain-plan
#XFLD : Label for Previous Run Column
previousRun=Föregående körning
#XFLD : Label for Latest Run Column
latestRun=Senaste körning
#XFLD : Label for time Column
time=Tid
#XFLD : Label for Duration Column
duration=Tidslängd
#XFLD : Label for Peak Memory Column
peakMemory=Maximalt minne
#XFLD : Label for Number of Rows
numberOfRows=Antal rader
#XFLD : Label for Overall Number of Sources
overallNumberOfSources=Källor totalt
#XFLD : Label for Data Access Column
dataAccess=Dataåtkomst
#XFLD : Label for Local Tables
localTables=Lokala tabeller
#XFLD : Label for federatedRemoteTables
federatedRemoteTables=Samordnade fjärrtabeller (med begränsade adapterfunktioner)
#XTXT Text for initial state of the runtime metrics
initialState=Du måste köra prestandaanalys först för att hämta måtten. Det kan ta en stund, men du kan avbryta processen vid behov.
#XMSG: Cancel confirmation message
CancelRunConfirmationMessage=Vill du avbryta aktuell körning av prestandaanalys?
#XTIT: Cancel dialog title
CancelRunTitle=Avbryt körning
#XFLD: Label for Number of Rows
NUMBER_ROWS=Antal rader
#XFLD: Label for Overall Number of Sources
OVERALL_NUMBER_SOURCES=Källor totalt
#XFLD: Label for Data Access
DATA_ACCESS=Dataåtkomst
#XFLD: Label for Remote tables
NUMBER_REMOTE_TABLES_LIMITED_ADAPTERS=Samordnade fjärrtabeller (med begränsade adapterfunktioner)
#XFLD: Label for select statement
SELECT_STATEMENT='SELECT * FROM VIEW LIMIT 1000'
#XFLD: Label for duration
SELECT_RUNTIME=Tidslängd
#XFLD: Label for peak memory
SELECT_PEAK_MEMORY=Maximalt minne
#XFLD: Label for count statement
SELECT_COUNT_STATEMENT='SELECT COUNT(*) FROM VIEW'
#XFLD: Label for count duration
SELECT_COUNT_RUNTIME=Tidslängd
#XFLD: Label for count peak memory
SELECT_COUNT_PEAK_MEMORY=Maximalt minne
#XFLD: Label for count of number of local tables
NUMBER_LOCAL_TABLES_FILE=Lokala tabeller (fil)
#XTXT: Text for running state of the runtime metrics
Running=Körs
#XFLD: Label for time
Time=Tid
#XFLD: Label for virtual access
PA_VIRTUAL=Virtuell
#XFLD: Label for persisted access
PA_PERSISTED=Persisterad
PA_PARTIALLY_PERSISTED=Delvis persisterad
#XTXT: Text for cancel
CancelRunSuccessMessage=Körning av prestandaanalys avbryts.
#XTXT: Text for cancel error
CancelRunErrorMessage=Ett fel inträffade när körning av prestandaanalys skulle avbrytas.
#XTXT: Text for explain plan generation
ExplainPlanStarted=Genererar Explain-plan för vy "{0}".
#XTXT: Text for performance analysis
PerformanceAnalysisStarted=Startar prestandaanalys för vy "{0}".
#XTXT: Text for performance analysis error
ErrorFetchingPerformanceAnalysis=Ett fel inträffade vid hämtning av prestandaanalysdata.
#XTXT: Text for performance analysis error
conflictingTask=Prestandaanalysuppgift körs redan
#XFLD: Label for Errors
Errors=Fel
#XFLD: Label for Warnings
Warnings=Varningar
#XFLD: Tooltip for missing privileges
openViewAnalyzerButtonTooltipNoPrivilege=Du behöver behörigheten DWC_DATAINTEGRATION(update) för att öppna Vyanalys.
#XFLD: Tooltip for missing privileges
generateExplainPlanButtonTooltipNoPrivilege=Du behöver behörigheten DWC_RUNTIME(read) för att generera Explain-plan.



#XFLD: Label for frequency column
everyLabel=Varje
#XFLD: Plural Recurrence text for Hour
hoursLabel=Timmar
#XFLD: Plural Recurrence text for Day
daysLabel=Dagar
#XFLD: Plural Recurrence text for Month
monthsLabel=Månader
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minuter
