
#XBCB: Breadcrumb
viewMonitorBreadcrumb={spaceId}

#XFLD: Label for persisted view column
SOURCE=Origen
#XFLD: Label for persisted view column
NAME=Nombre
#XFLD: Label for persisted view column
NAME_LABEL=Nombre empresarial
#XFLD: Label for persisted view column
NAME_LABELNew=Objeto (nombre empresarial)
#XFLD: Label for persisted view column
TECHINCAL_NAME=Nombre técnico
#XFLD: Label for persisted view column
TECHINCAL_NAMENew=Objet<PERSON> (nombre técnico)
#XFLD: Label for persisted view column
DATA_PERSISTENCY=Acceso a datos
#XFLD: Label for persisted view column
STATUS=Estado
#XFLD: Label for persisted view column
LAST_UPDATED=Última actualización
#XFLD: Label for persisted view column
IN_MEMORY_SIZE=Memoria usada para almacenamiento (MiB)
#XFLD: Label for persisted view column
DISK_SIZE=Disco usado para almacenamiento (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_NEWW=Tamaño en memoria (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_HEADER=Tamaño en memoria
#XFLD: Label for persisted view column
DISK_SIZE_NEWW=Tamaño en disco (MiB)
#XFLD: Label for persisted view column
DISK_SIZE_HEADER=Tamaño en disco
#XFLD: Label for schedule owner column
txtScheduleOwner=Propietario del programa
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Muestra quién creó el programa
#XFLD: Label for persisted view column
PERSISTED=Persistente
#XFLD: Label for persisted view column
TYPE=Tipo
#XFLD: Label for View Selection Dialog column
changedOn=Fecha de modificación
#XFLD: Label for View Selection Dialog column
createdBy=Creado por
#XFLD: Label for log details column
txtViewPersistencyLogs=Ver registros
#XFLD: Label for log details column
txtViewPersistencyLogsNew=Detalles
#XFLD: text for values shown for Ascending sort order
SortInAsc=Orden ascendente
#XFLD: text for values shown for Descending sort order
SortInDesc=Orden descendente
#XFLD: Label for persisted view monitor heading
ViewMonitor_Heading=Supervisor de vistas
#XFLD: Label for persisted view monitor sub-heading
ViewMonitor_SubHeading=Supervisar y mantener las vistas de persistencia de datos


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ DynamicPageHeader ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: text for values shown in column Persistence Status
txtLoading=Cargando
#XFLD: text for values shown in column Persistence Status
txtRunning=En ejecución
#XFLD: text for values shown in column Persistence Status
txtAvailable=Disponible
#XFLD: text for values shown in column Persistence Status
txtError=Error
#XMSG: Error message for unknown Persistence type that gets logged in Kibana
unknownReplicationTypeError=El tipo de replicación "{0}" no se admite.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Content ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Opciones utilizadas para la última ejecución de persistencia de datos:
#XMSG: Message for input parameter name
inputParameterLabel=Parámetro de entrada
#XMSG: Message for input parameter value
inputParameterValueLabel=Valor
#XMSG: Message for persisted data
inputParameterPersistedLabel=Persistente en
#XTIT: Count for View in ViewMonitor Toolbar
txtViewCount=Vistas ({0})
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Persistencia de vista
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Persistencia de datos
#XBUT: Button to clear the selected view persistance
ClearViewPersistance=Borrar
#XBUT: Button to stop the selected view persistance
stopPersistance=Detener persistencia
#XFLD: Placeholder for Search field
txtSearch=Buscar
#XBUT: Tooltip for refresh button
txtRefresh=Actualizar
#XBUT: Tooltip for add view button
txtDeleteView=Eliminar persistencia
#XBUT: Tooltip for load new peristence
loadNewPersistence=Reiniciar persistencia
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Cargar nueva instantánea
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Iniciar persistencia de datos
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Quitar datos persistentes
#XMSG: success message for starting persistence
startPersistenceSuccess=Estamos persistiendo la vista "{0}".
#XMSG: success message for stopping persistence
stopPersistenceSuccess=Estamos quitando datos persistentes de la vista "{0}".
#XMSG: success message for stopping persistence
deletePersistedViewSuccess=Estamos quitando la vista "{0}" de la lista de supervisión.
#XMSG: error message during start persistence of a view (backend error)
ERROR_STARTING_PERSISTENCE=Se produjo un error en el inicio de la persistencia de datos para la vista "{0}".
#XMSG: error message during start persistence of a view (backend error)
PARAMERROR_STARTING_PERSISTENCE=No se admite la persistencia de la vista "{0}" porque contiene parámetros de entrada.
#XMSG: error message during start persistence of a view (backend error)
NUMBER_OF_PARAMETER_STARTING_PERSISTENCE=No se admite la persistencia de la vista "{0}" porque tiene más de un parámetro de entrada.
#XMSG: error message during start persistence of a view (backend error)
PARAMETER_DEFAULT_VALUE_STARTING_PERSISTENCE=No se admite la persistencia de la vista "{0}" porque el parámetro de entrada no tiene un valor predeterminado.
#XMSG: error message during start persistence of a view (backend error)
REDEPLOYMENT_DAC_REQUIRED=Debe implementar nuevamente el Control de acceso de datos (DAC) "{0}" para admitir la persistencia de datos.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_STARTING_PERSISTENCE=La vista "{0}" no puede ser persistente porque utiliza la vista "{1}", que tiene Control de acceso de datos (DAC).
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_EXTERNAL_STARTING_PERSISTENCE=La vista "{0}" no puede ser persistente porque usa una vista con control de acceso a datos (DAC) que pertenece a un espacio diferente.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_STRUCTURE_STARTING_PERSISTENCE=La vista "{0}" no puede ser persistente porque la estructura de uno o más de sus controles de acceso de datos (DAC) no admite la persistencia de datos.
#XMSG: error message during stop persistence of a view (backend error)
STOP_PERSISTENCE_ERROR=Se produjo un error en la detención de la persistencia de la vista "{0}".
#XMSG: error message during stop persistence of a view (backend error)
DELETE_PERSISTED_VIEW_ERROR=Se produjo un error en la eliminación de la persistencia de la vista "{0}".
#XMSG: Warning message for removing the persisted data
RemovePersistency_Confirm_Msg=¿Desea eliminar los datos persistentes y cambiar al acceso virtual de la vista "{0}"?
#XMSG: Warning message for delete the view persistence
DeletePersistency_Confirm_Msg=¿Desea quitar la vista de la lista de supervisión y eliminar los datos persistentes de la vista "{0}"?
#XMSG: error message for reading data from backend
txtReadBackendError=Parece que hubo un error durante la lectura desde el backend.
#XFLD: Label for No Data Error
NoDataError=Error
#XMSG: message for conflicting task
Task_Already_Running=Ya se está ejecutando una tarea con conflictos para la vista "{0}".

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Show All Views~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Text for show all views button
allViewsText=Todas las vistas ({0})
#XBUT: Text for show scheduled views button
scheduledText=Programado ({0})
#XBUT: Text for show persisted views button
persistedText=Persistente ({0})
#XBUT: Text for start analyzer button
startAnalyzer=Iniciar analizador de vista
#XFLD: Message if repository is unavailable
repositoryErrorMsg=El repositorio no está disponible y se desactivaron ciertas funciones.

#XFLD: Data Access - Virtual
Virtual=Virtual
#XFLD: Data Access - Persisted
Persisted=Persistente

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Non-Persisted View Selection Dialog~~~~~~~~~~~~~~~~~~~~~~~

#XTIT: Title for Non-Persisted View selection dialog
select_source=Seleccionar vista para persistir

#XTIT: Placeholder for Non-Persisted View selection dialog search
searchView=Buscar vistas
#XTIT: No data in the list of non-persisted view
No_Data=Sin datos
#XBUT: Button to select non-persisted view
ok=Ok
#XBUT: Button to close the non-persisted views selection dialog
cancel=Cancelar

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~View persistency log message key~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message for view persistency logs
START_STOP_PERSISTENCY=Iniciando la ejecución de la tarea de persistencia para "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY=Persistencia de los datos para la vista "{1}".
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY)
START_VIEW_PERSISTENCY_PROCESS=Iniciando el proceso de persistencia de los datos para la vista "{0}".
#XFLD: Message for view persistency manual partition load
START_VIEW_PERSISTENCY_PROCESS_MANUAL_PARTITIONS=Iniciando el proceso de persistencia de los datos para la vista "{0}" con los ID de las particiones seleccionadas: "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY=Quitando datos persistentes de la vista "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY)
REMOVE_VIEW_PERSISTENCY=Iniciando el proceso de eliminación de datos persistentes para la vista "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_SUCCESS=Persistencia de los datos para la vista "{1}".
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_SUCCESS)
START_VIEW_PERSISTENCY_PROCESS_SUCCESS=Persistencia de los datos para la vista "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY_SUCCESS=Se quitaron los datos con persistencia y se recuperó el acceso a los datos virtuales para la vista "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_SUCCESS)
REMOVE_VIEW_PERSISTENCY_SUCCESS=Se completó el proceso de eliminación de datos persistentes para la vista "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_FAILED=No se puede iniciar la persistencia de los datos para la vista "{1}".
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_FAILED)
START_VIEW_PERSISTENCY_PROCESS_FAILED=No se puede iniciar la persistencia de los datos para la vista "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.05)
STOP_VIEW_PERSISTENCY_FAILED=No se puede quitar la persistencia de los datos para la vista "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_FAILED)
REMOVE_VIEW_PERSISTENCY_FAILED=No se puede quitar la persistencia de los datos para la vista "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_SUCCESS="{3}" registros con persistencia para vista "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace START_PERSISTENCY_RECORD_COUNT_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS={0} registros insertados en la tabla de persistencia de datos para la vista "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS_V2={0} registros insertados en la tabla de persistencia de datos para la vista "{1}". Memoria usada: {2} GiB.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_SUCCESS="{3}" registros con persistencia quitados para la vista "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace STOP_PERSISTENCY_RECORD_COUNT_SUCCESS)
REMOVE_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=Se quitaron los datos con persistencia: "{0}" registros con persistencia eliminados.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_FAILED=Se produjo un error al obtener recordCount para la vista "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_FAILED=Se produjo un error al obtener recordCount para la vista "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Programación eliminada para "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS)
REMOVE_VIEW_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=La programación se eliminó para la vista "{0}".
#XFLD: Message for view persistency logs
STOP_PERSISTENCY_DELETE_SCHEDULE_FAILED=Se produjo un error al eliminar la programación para "{1}".
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_REDEPLOY=La vista "{0}" no puede ser persistente porque se cambió e implementó desde que inició su persistencia. Vuelva a intentar hacer persistente la vista o espere hasta la próxima ejecución programada.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_DELETE=La vista "{0}" no puede ser persistente porque se eliminó desde que inició su persistencia.
#XFLD: Message for successful data persistency of a single partition (for messages created before version 2022.03)
START_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} registros con persistencia en la partición para los valores "{1}" <= {2} < "{3}".
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (new message to replace START_PERSISTENCY_SINGLE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} registros insertados en la partición para los valores "{1}" <= {2} < "{3}".
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS_V2={0} registros insertados en la partición para los valores "{1}" <= "{2}" < "{3}". Memoria usada: {4} GiB.
#XFLD: Message for successful replication of a "others" partition (for messages created before version 2022.03)
START_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} registros con persistencia en la partición "otros".
#XFLD: Message used by SEAL repository for successful replication of a "others" partition (new message to replace START_PERSISTENCY_OTHERS_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} registros insertados en la partición "otros".
#XFLD: Message for view persistency record count of partitions (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={3} registros con persistencia para la vista "{1}" en {4} particiones.
#XFLD: Message used by SEAL repository for view persistency record count of partitions (new message to replace START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={0} registros insertados en la tabla de persistencia de datos para la vista "{1}" en {2} particiones.
#XFLD: Message used by SEAL repository for view persistency for partition wise refresh
START_VIEW_PERSISTENCY_RECORD_COUNT_REFRESHED_PARTITIONS_SUCCESS={0} registros insertados en la tabla de persistencia de datos para la vista "{1}". Particiones utilizadas: {2} Particiones bloqueadas: {3} Particiones totales: {4}
#XFLD: Message used by SEAL repository for view persistency for manual partition load
START_VIEW_PERSISTENCY_RECORD_COUNT_MANUAL_PARTITIONS_SUCCESS={0} registros insertados en la tabla de persistencia de datos para la vista "{1}" en {2} particiones seleccionadas
#XFLD: Message used by SEAL repository for view persistency for locked partition data load
START_VIEW_PERSISTENCY_RECORD_COUNT_LOCKED_PARTITIONS_SUCCESS={0} registros insertados en la tabla de persistencia de datos para la vista "{1}". Particiones actualizadas: {2}; Particiones sin cambios y bloqueadas: {3}; Particiones totales: {4}.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
START_PERSISTENCY_UNEXPECTED_ERROR=Se produjo un error inesperado en la persistencia de los datos para la vista "{0}".
#XMSG: Message for unexpected error (new message to replace START_PERSISTENCY_UNEXPECTED_ERROR)
START_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Se produjo un error inesperado en la persistencia de los datos para la vista "{0}".
#XMSG: Message for Locked space error
START_VIEW_PERSISTENCY_LOCKED_SPACE_ERROR=No se puede realizar la persistencia de la vista "{0}" porque el espacio "{1}" está bloqueado.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
STOP_PERSISTENCY_UNEXPECTED_ERROR=Se produjo un error inesperado en la eliminación de los datos con persistencia para la vista "{0}".
#XMSG: Message for unexpected error (new message to replace STOP_PERSISTENCY_UNEXPECTED_ERROR)
REMOVE_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Se produjo un error inesperado durante la eliminación de la persistencia para la vista "{0}".
#XMSG: Message for ivalidated view
VIEW_PERSISTENCY_ABORTED_DEFINITION_INVALID=La definición de la vista "{0}" se volvió no válida, más probablemente debido a un cambio en un objeto consumido directa o indirectamente por la vista. Intente volver a implementar la vista para resolver el problema, o bien identificar la causa raíz.
#XMSG: Message for persisted data removal (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT=Los datos con persistencia se quitan al implementar la vista "{0}".
#XMSG: Message for persisted data removal of a consuming view
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW=Los datos con persistencia se quitan al implementar la vista consumida "{0}".
#XMSG: Message for persisted data removal of a consuming view due to a dac change
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW_DAC_CHANGED=Los datos persistentes se quitan cuando se implementa la vista consumida "{0}" porque cambió el control de acceso de los datos.
#XMSG: Message for persisted data removal (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DEPLOYMENT=Los datos con persistencia se quitan al implementar la vista "{0}".
#XMSG: Message for deleted view (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION=La persistencia se quita con la eliminación de la vista "{0}".
#XMSG: Message for deleted view (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DELETION=La persistencia se quita con la eliminación de la vista "{0}".
#XMSG Message for removal of persisted data, because prerequisites not fulfilled
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_PREREQUISITES_NOT_FULFILLED=Los datos con persistencia se quitan porque los requisitos previos de la persistencia de datos ya no se cumplen.
#XMSG: Message for inconsistent persistency
VIEW_PERSISTENCY_INCONSISTENT_PERSISTENCY=La persistencia de la vista "{0}" se ha vuelto inconsistente. Elimine los datos con persistencia para resolver el problema.
#XMSG: Message for prerequisites
START_PERSISTENCY_CHECK_PREREQUISITES=Verificando los requisitos para la persistencia de la vista "{0}".
#XMSG: Message for DAC deployed in deprecated way using table function and join (for messages created before 2025.05)
START_PERSISTENCY_DEPRECIATED_DAC=La vista "{0}" se implementa con un Control de acceso de datos (DAC) que se está tornando obsoleto. Vuelva a implementar la vista para mejorar el rendimiento.
#XMSG: Message for DAC deployed in deprecated way using table function and join
START_PERSISTENCY_DEPRECATED_DAC=La vista "{0}" se implementa con un Control de acceso de datos (DAC) que se está tornando obsoleto. Vuelva a implementar la vista para mejorar el rendimiento.
#XMSG: Message for roll back (for messages created before version 2022.03)
ROLLBACK_VIEW_PERSISTENCY=Ocurrió un error. Se restauró el estado previo de persistencia para la vista "{0}".
#XMSG: Message used by SEAL repository for rollback of running procedure to persist view (new message to replace ROLLBACK_VIEW_PERSISTENCY)
VIEW_PERSISTENCY_ROLLBACK=Ocurrió un error. Se detuvo el proceso para la persistencia de la vista ''{0}'' y se revirtieron los cambios.
#XMSG: Message used by SEAL repository for rollback of running procedure to remove persisted data of view
REMOVE_VIEW_PERSISTENCY_ROLLBACK=Ocurrió un error. Se detuvo el proceso para quitar los datos persistentes de la vista "{0}" y se revirtieron los cambios.
#XMSG: Message used by SEAL repository for persistency preparation
START_PERSISTENCY_PREPARATION=Preparando la persistencia de los datos.
#XMSG: Message used by SEAL repository for data insertion
START_INSERTING_PERSISTENCY_DATA=Insertando datos en la tabla de persistencia.
#XMSG: Message for null values - partitioning (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS={0} registros de valores nulos insertados en la partición "otros".
#XMSG: Message used by SEAL repository for others partition values - partitioning (new message to replace START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS={0} registros insertados en la partición "otros" para los valores "{2}" < "{1}" O "{2}" >= "{3}".
#XMSG: Message used by SEAL repository for others partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS_V2={0} registros insertados en la partición "otros" para los valores "{2}" < "{1}" O "{2}" >= "{3}". Memoria usada: {4} GiB.
#XMSG: Message used by SEAL repository for null partition values - partitioning
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS={0} registros insertados en la partición "otros" para los valores "{1}" ES NULO.
#XMSG: Message used by SEAL repository for null partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS_V2={0} registros insertados en la partición "otros" para los valores "{1}" ES NULO. Memoria usada: {2} GiB.
##XFLD:Message used by SEAL repository for remote statistics
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS=Cargando los datos involucrados: {0} instrucciones remotas. Total de registros recuperados: {1}. Tiempo de duración total: {2} segundos.
##XFLD:Message used by SEAL repository for remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS_WITH_PARTITIONS=Cargando los datos involucrados mediante {0} particiones con {1} instrucciones remotas. Total de registros recuperados: {2}. Tiempo de duración total: {3} segundos.
##XFLD:Message used by SEAL repository with UI ID for instructions of remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_INSTRUCTION=Para mostrar las instrucciones remotas procesadas durante la ejecución, abra el supervisor de consulta remota en los detalles de los mensajes específicos de la partición.
##XFLD:Message for start reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS=Iniciando el proceso para reutilizar los datos persistentes existentes para la vista {0} después de la implementación.
##XFLD:Message used by SEAL repository for reattach view persitency
START_VIEW_PERSISTENCY_REATTACHMENT=Comience a reutilizar los datos persistentes existentes.
##XFLD:Message used by SEAL repository for reattach view persitency
START_FINILIZING_VIEW_PERSISTENCY_REATTACHMENT=Reutilizando los datos persistentes existentes.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_COMPLETE=El proceso para reutilizar los datos persistentes existentes se completó para la vista {0}.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_FAILED=Ocurrió un error al reutilizar los datos persistentes existentes para la vista {0} después de la implementación.
#XMSG: Message used by SEAL repository for finalizing persistency
START_FINALIZING_PERSISTENCY=Finalizando persistencia.
#XMSG: Message used by SEAL repository for restoring virtual access for view
REMOVE_VIEW_PERSISTENCY_VIRTUAL_ACCESS_RESTORED=Se restauró el acceso de datos virtuales para la vista "{0}".
#XMSG: Message used by SEAL repository for view that already has virtual access
REMOVE_VIEW_PERSISTENCY_ALREADY_VIRTUAL_ACCESS=La vista "{0}" ya tiene acceso de datos virtual. No se quitan los datos persistentes.
#XMSG: Message for removing view persistency
VIEW_PERSISTENCY_REMOVED_FROM_MONITOR=La vista "{0}" se quitó del supervisor de vistas.
#XMSG: Message for view doesnt exist.
VIEW_PERSISTENCY_VIEW_NOT_EXISTING=La vista "{0}" no existe en la base de datos o no se implementó correctamente y, por lo tanto, no se puede realizar su persistencia. Intente volver a implementar la vista para resolver este problema o para identificar la causa raíz.
#XMSG: Message for persistency not enabled.
VIEW_PERSISTENCY_NOT_ENABLED=La persistencia de datos no está habilitada. Vuelva a implementar una tabla o vista en el espacio "{0}" para habilitar la funcionalidad.
#XMSG: Message for unexpected error due to node crash
VIEW_PERSISTENCY_UNEXPECTED_ERROR_GENERAL=Se interrumpió la ejecución de la persistencia de la vista debido a errores técnicos.
#XFLD: Message for view persistency memory consumption
VIEW_PERSISTENCY_MEMORY_CONSUMPTION={0} GiB de memoria máxima utilizada en el tiempo de ejecución de persistencia de la vista.
#XFLD: Message for view persistency timeout
VIEW_PERSISTENCY_TIMEOUT=La persistencia de la vista {0} alcanzó el tiempo de espera de {1} horas.
#XFLD: Message for not started asynchronous view persistency
VIEW_PERSISTENCY_ASYNC_NOT_STARTED=La alta carga del sistema evitó que se inicie la ejecución asincrónica de la persistencia de la vista. Verifique si hay varias tareas ejecutándose en paralelo.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency (for messages created before version 2022.05)
START_VIEW_PERSISTENCY_EXECUTION_METHOD_DROP_AND_RENAME=La tabla de persistencia existente se eliminó y reemplazó por una tabla de persistencia nueva.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_RECREATE_AND_DROP=La tabla de persistencia existente se eliminó y reemplazó por una tabla de persistencia nueva.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_TRUNCATE_AND_COPY=La tabla de persistencia existente se actualizó con datos nuevos.
##XFLD: Message for missing authorizations for view persistency
VIEW_PERSISTENCY_MISSING_AUTH=Faltan autorizaciones para la persistencia de datos.
##XFLD: Message start of persistency cancellation
VIEW_PERSISTENCY_TRIGGER_CANCEL=Iniciando la cancelación del proceso para la persistencia de la vista {0}.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_UNAVAILABLE_TASK_RUN=Falló la cancelación del proceso para la persistencia de la vista porque no hay una tarea de persistencia de datos en ejecución para la vista {0}.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_NONDETERMINISTIC_TASK_RUN=Falló la cancelación del proceso para la persistencia de la vista porque no hay una tarea de persistencia de datos en ejecución para la vista {0}.
##XFLD: Message for canceling a non running persitency task
VIEW_PERSISTENCY_CANCEL_NON_RUNNING_SELECTED_TASK=Ocurrió un error al cancelar el proceso de persistencia de la vista {0} porque la tarea de persistencia de datos seleccionada {1} no se está ejecutando.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_UNREACHABLE_PERSISTENCY_RUN=Falló la cancelación del proceso para la persistencia de la vista porque aún no se inició la persistencia de datos para la vista {0}.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_COMPLETED_PERSISTENCY_RUN=Falló la cancelación del proceso para la persistencia de la vista {0} porque ya se completó.
##XFLD: Message for failed cancellation
VIEW_PERSISTENCY_ABORTED_CANCEL_EXECUTION=Falló la cancelación del proceso para la persistencia de la vista {0}.
##XFLD: Message for finalizing persistency cancellation
VIEW_PERSISTENCY_FINALIZE_CANCEL=Se envió el proceso para detener la persistencia de datos de la vista {0}.
##XFLD: Message used in persist task when cancellation is caught
VIEW_PERSISTENCY_CANCELED=Se detuvo el proceso para la persistencia de la vista {0}.
##XFLD: Message used in persist task when cancelation is caught and performed through a cancelation task
VIEW_PERSISTENCY_CANCELED_VIA_CANCEL_TASK=Se detuvo el proceso para la persistencia de la vista {0} a través de la tarea de cancelación {1}.
##XFLD: Message used in cancel task when the task is triggered by deployment
VIEW_PERSISTENCY_CANCEL_TRIGGERED_BY_DEPLOYMENT=Cancelando el proceso para la persistencia de los datos a la vez que se implementa la vista {0}.
##XFLD: Message used in case the cancelation is retriggered for a running task
VIEW_PERSISTENCY_CANCEL_ATTEMPT_RETRIGGER_CANCEL=Ya se envió una tarea de cancelación previa para la persistencia de la vista {0}.
##XFLD: Message used to express the delay that may occur before the persistency task is stopped
VIEW_PERSISTENCY_CANCEL_DELAY_STOPPING_PERSISTENCY=Puede haber una demora hasta que se detenga la tarea de persistencia de datos para la vista {0}.
##XFLD: Message used to reflect the task log identifier of the current running persistency task
VIEW_PERSISTENCY_CANCEL_CURRENT_RUNNING_TASK=Los datos para la vista {0} son persistentes con la tarea {1}.
##XFLD:Message used by SEAL repository for locked partitions and DACs
VIEW_PERSISTENCY_LOCKED_PARTITIONS_DACS=Las autorizaciones provistas por los DAC pueden haber cambiado y no están consideradas en las particiones bloqueadas. Desbloquee las particiones y cargue una nueva instantánea para aplicar los cambios.
##XFLD:Message used by SEAL repository for column structure change
VIEW_PERSISTENCY_COLUMN_STRUCTURE_CHANGE=La estructura de columna cambió y ya no coincide con la tabla de persistencia existente. Quite los datos persistentes e inicie una nueva persistencia de datos para que se actualice su tabla de persistencia.
##XFLD: Message for out of memory
VIEW_PERSISTENCY_HANA_OUT_OF_MEMORY=La tarea falló debido a un error de falta de memoria en la base de datos de SAP HANA.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_EXCEPTION_TRANSACTION_ROLLBACK=La tarea falló debido a una excepción interna en la base de datos de SAP HANA.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_SQL_EXECUTION_ISSUE=La tarea falló debido a un problema de ejecución de SQL interna en la base de datos de SAP HANA.
##XFLD: Message for HANA out of memory result
VIEW_PERSISTENCY_OUT_OF_MEMORY_RESULT_MESSAGE=Motivo del evento de falta de memoria de HANA: {0}
##XFLD: Message for admission control rejection
VIEW_PERSISTENCY_HANA_ADMISSION_CTRL_REJECTION=La tarea falló debido a un rechazo del control de admisión de SAP HANA.
##XFLD: Message for HANA back pressure / rate limit of HANA client
VIEW_PERSISTENCY_HANA_CLIENT_RATE_LIMIT_REJECTION=La tarea falló debido a demasiadas conexiones de SAP HANA activas.
#XMSG: Message for invalid persisted table name
VIEW_PERSISTENCY_PERSISTED_TABLE_INVALID=Ocurrió un error y la tabla persistente se tornó no válida. Para resolver esto, quite los datos persistentes y vuelva a realizar la persistencia de la vista.
#XMSG: Message for usahe of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION=No se puede realizar la persistencia de la vista. Usa una tabla remota con base en un origen remoto con la propagación de usuario activada. Compruebe el linaje de la vista.
#XMSG: Message for dynamic usage of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION_DYNAMIC=No se puede realizar la persistencia de la vista. Usa una tabla remota con base en un origen remoto con la propagación de usuario activada. La tabla remota se puede consumir dinámicamente a través de una vista de script SQL. Es posible que el linaje de la vista no muestre la tabla remota.
#XMSG: Message for insufficient privileges or consumption of view with DAC via dynamic SQL
VIEW_PERSISTENCY_INSUF_PRIV_OR_DAC_DYN_SQL=Es posible que sus privilegios sean insuficientes. Abra la vista previa de datos para ver si tiene los privilegios requeridos. Si es así, una segunda vista consumida a través del script SQL dinámico puede tener control de acceso de datos (DAC) aplicado a este.
#XMSG: deployment of DAC is deprecated and view need to be redeployed to be able to persist data
VIEW_PERSISTENCY_DEPRECATED_DAC=La vista "{0}" se implementa con un Control de acceso de datos (DAC) que se está tornando obsoleto. Vuelva a implementar la vista para poder realizar la persistencia de los datos para la vista.
##XFLD:Message used by SEAL repository for input parameter
START_VIEW_PERSISTENCY_INPUT_PARAMETER=Uso del valor predeterminado "{0}" para el parámetro de entrada "{1}".
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Elastic compute node keys~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
##XFLD:Message used to reflect disablement of ecn replica
ECN_DISABLE_EXISTING_REPLICA=La réplica en el nodo de procesamiento elástico está deshabilitada.
##XFLD:Message used to reflect recreation of ecn replica
ECN_RECREATE_EXISTING_REPLICA=La réplica en el nodo de procesamiento elástico está recreada.
##XFLD:Message used to reflect re-enablement of ecn replica
ECN_ENABLE_EXISTING_REPLICA=La réplica en el nodo de procesamiento elástico está habilitada nuevamente.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Task Schedule Menu key~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message to schedule task
scheduleText=Programar
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Crear programa
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Editar programa
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Eliminar programa
#XFLD: Refresh frequency field
refreshFrequency=Frecuencia de actualización
#XFLD: Refresh frequency field
refreshFrequencyNew=Frecuencia
#XFLD: Refresh frequency field
refreshFrequencyNewNew=Frecuencia programada
#XBUT: label for None
none=Ninguno
#XBUT: label for Real-Time replication state
realtime=En tiempo real
#XFLD: Label for table column
txtNextSchedule=Siguiente ejecución
#XFLD: Label for table column
txtNextScheduleNew=Próxima ejecución programada
#XFLD: Label for table column
txtNumOfRecords=Número de registros
#XFLD: Label for scheduled link
scheduledTxt=Programado
#XFLD: LABEL for partially persisted link
partiallyPersisted=Persistencia parcial
#XFLD: Text for paused text
paused=En pausa

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ View Monitor Tasklog Details Action keys~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message for release lock actionn
releaseLockMsg=Si una ejecución tarda más tiempo del habitual, esto puede indicar que se produjo un error y que el estado no se actualizó correctamente. \r\n Para resolver el problema, puede liberar el bloqueo y establecer el estado en Error.
#XFLD: Label for release lock dialog
releaseLockText=Liberar bloqueo

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Column header tooltip ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: tooltip for table column
txtViewTooltip=Nombre de la vista persistente
#XFLD: tooltip for table column
txtViewDataAccessTooltip=Esto indica la disponibilidad de la vista
#XFLD: tooltip for table column
txtViewRefreshFreqTooltip=Esto indica si hay un programa definido para la vista
#XFLD: tooltip for table column
txtViewStatusTooltip=Obtener el estado de la vista persistente
#XFLD: tooltip for table column
txtViewLatestUpdateTooltip=Proporciona información sobre la última actualización de la vista persistente
#XFLD: tooltip for table column
txtViewNextRunTooltip=Si se estableció un programa para la vista, vea para cuándo está programada la próxima ejecución.
#XFLD: tooltip for table column
txtViewNumOfRecordsTooltip=Realice un seguimiento del número de registros.
#XFLD: tooltip for table column
txtViewInMemorySizeTooltip=Realice un seguimiento del espacio que utiliza la vista en la memoria
#XFLD: tooltip for table column
txtViewDiskSizeTooltip=Realice un seguimiento del espacio que ocupa la vista en el disco
#XMSG: Expired text
txtExpired=Vencido

#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=El objeto "{0}" no se puede agregar a la cadena de tareas.

#~~~~~~~~~~~View Analyzer task log messages~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message indicating number of records for a view, and memory consumption in Mebibyte during simulation of view persistency
RECORDS_PEAK_MEMORY=La vista "{0}" tiene {1} registros. Una simulación de la persistencia de datos para esta vista usó {2} MiB de memoria.
#XMSG: View Analyzer execution failure
VIEW_ANALYZER_EXECUTION_FAILED=Ocurrió un error durante la ejecución del analizador de vistas.
#XMSG: Missing authorizations for View Analyzer
VIEW_ANALYZER_MISSING_AUTH=Faltan autorizaciones para el analizador de vistas.
#XMSG: Maximum memory in Gibibyte is reached, therefore persistency simulation is stopped
MAXIMUM_MEMORY_REACHED=Se alcanzó la memoria máxima de {0} GiB durante la simulación de la persistencia de datos para la vista "{1}". Por lo tanto, no se ejecutarán más simulaciones de persistencia de datos.
#XMSG: Error during persistency simulation occurred
PERSISTENCY_SIMULATION_ERROR=Ocurrió un error durante la simulación de la persistencia de datos para la vista "{0}".
#XMSG: Persistency simulation is not executed, because prerequisites are not fulfilled and the view cannot be persisted
PREREQUISITES_NOT_FULFILLED=La simulación de la persistencia de datos no se ejecuta para la vista "{0}", porque los requisitos previos no se cumplen y no se puede realizar la persistencia de la vista.
#XMSG: Persistency simulation is not executed, because the view does not exist in the database
VIEW_DOES_NOT_EXIST=Debe desplegar la vista "{0}" para activar la simulación de la persistencia de datos.
#XMSG: Number of records cannot be determined, because the local table does not exist in the database
TABLE_DOES_NOT_EXIST=La tabla local "{0}" no existe en la base de datos y, por lo tanto, no se puede determinar el número de registros para esta tabla.
##XMSG: Message start of cancellation for View Analyzer task
CANCEL_VIEW_ANALYZER_TRIGGERED=Se envió el proceso para detener la tarea {0} del Analizador de vistas para la vista "{1}". Puede haber una demora hasta que se detenga la tarea.
##XMSG: Message start to indicate that task is not active
CANCEL_VIEW_ANALYZER_NO_ACTIVE_TASK=La tarea {0} del Analizador de vistas para la vista "{1}" no está activa.
#XMSG: Cancel View Analyzer task failure
CANCEL_VIEW_ANALYZER_FAILED=Ocurrió un error al cancelar la tarea del Analizador de vistas.
##XMSG: Message used in View Analyzer execution task when task is canceled
VIEW_ANALYZER_CANCELED=Se detuvo la ejecución del Analizador de vistas para la vista "{0}" a través de una tarea de cancelación.
##XMSG: Message start of cancelation for Model Validation task
CANCEL_MODEL_VALIDATION_TRIGGERED=Se envió el proceso para detener la tarea {0} de la Validación de modelo para la vista "{1}". Puede haber una demora hasta que se detenga la tarea.
##XMSG: Message to indicate that task is not active
CANCEL_MODEL_VALIDATION_NO_ACTIVE_TASK=La tarea {0} de la Validación de modelo para la vista "{1}" no está activa.
#XMSG: Cancel Model Validation task failure
CANCEL_MODEL_VALIDATION_FAILED=No se ha podido cancelar la tarea Validación de modelo.
##XMSG: Message used in Model Validation task when task is canceled
MODEL_VALIDATION_CANCELED=Se detuvo la ejecución de la Validación de modelo para la vista "{0}" a través de una tarea de cancelación.
#XMSG: Message for Locked space error, before starting Model Validation task
MODEL_VALIDATION_LOCKED_SPACE_ERROR=No se puede ejecutar la Validación de modelo para la vista "{0}" porque el espacio "{1}" está bloqueado.
##XMSG: Message used in View Analyzer execution task when determination of number of rows fails
NUMBER_ROWS_DETERMINATION_FAILED=Ocurrió un error al determinar el número de filas para la tabla local "{0}".
##XMSG: Message to indicate that SQL plan was created
EXECUTION_PLAN_CREATED=Se crea el archivo de plan de SQL Analyzer para la vista "{0}" y se puede descargar.
#XFLD: Message to indicate starting of process to generate SQL Analyzer plan file
START_PLANVIZ_EXECUTION=Iniciando el proceso para generar el archivo de plan de SQL Analyzer para la vista "{0}".
#XFLD: Message to indicate starting of View Analyzer process
VIEW_ANALYZER_EXECUTION_STARTED=Iniciando la ejecución del analizador de vistas.
##XMSG: Message to indicate that SQL plan file cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_PLANVIZ_NOT_FULFILLED=El archivo de plan de SQL Analyzer no se puede generar para la vista "{0}" porque no se cumplieron los requisitos de persistencia de datos.
##XMSG: Error message during  generation of SQL Analyzer plan file
PLANVIZ_ERROR=Ocurrió un error durante la generación del archivo de plan de SQL Analyzer para la vista "{0}".
#XMSG: Message for Locked space error
VIEW_ANALYZER_LOCKED_SPACE_ERROR=No se puede ejecutar el Analizador de datos para la vista "{0}" porque el espacio "{1}" está bloqueado.
#XMSG: Message for persistency simulation without partitioning
PERSISTENCY_SIMULATION_WITHOUT_PARTITIONS=Las particiones de la vista "{0}" no se consideran durante la simulación de la persistencia de datos.
#XMSG: Message for generation of SQL Analyzer plan file without considering partitioning
PLANVIZ_WITHOUT_PARTITIONS=Las particiones de la vista "{0}" no se consideran durante la generación del archivo de plan de SQL Analyzer.
#XMSG: Confirmation message for mass removal of persisted data
MassRemovePersistency_Confirm_Msg=¿Desea eliminar los datos persistentes y cambiar el acceso a los datos nuevamente al acceso virtual?
#XMSG: Confirmation message for partial mass removal of selected persisted data
PartialMassRemovePersistency_Confirm_Msg={0} de las {1} vistas seleccionadas tienen datos persistentes. \n ¿Desea eliminar los datos persistentes y cambiar el acceso a los datos nuevamente al acceso virtual?
#XMSG: success message for stopping mass persistence
stopMassPersistenceSuccess=Estamos eliminando los datos persistentes para las vistas seleccionadas.
#XMSG: error message during mass stop persistence of a view (backend error)
STOP_MASS_PERSISTENCE_ERROR=Se produjo un error en la detención de la persistencia de las vistas seleccionadas.
#XMSG: memory analysis is only performed in space {0}
NO_MEMORY_ANALYSIS=El análisis de memoria se realiza para entidades en el espacio "{0}" únicamente: se omitió "{1}" "{2}"
##XMSG: Message to indicate that Explain Plan cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_EXPLAINPLAN_NOT_FULFILLED=El archivo de plan de explicación no se puede generar para la vista "{0}" porque no se cumplen los requisitos de persistencia.
#XMSG: Message for Explain Plan generation without partitioning
EXPLAINPLAN_WITHOUT_PARTITIONS=Las particiones de la vista "{0}" no se consideran durante la generación del archivo de plan de explicación.
#XFLD: Message to indicate starting of process to generate Explain Plan file
START_EXPLAINPLAN_EXECUTION=Iniciando el proceso para generar el archivo de plan de explicación para la vista "{0}".
##XMSG: Message to indicate that Explain Plan was created
EXPLAINPLAN_CREATED=Se generó el archivo de plan de explicación para la vista "{0}". Para mostrarlo, haga clic en "Ver detalles", o bien descárguelo si tiene la autorización correspondiente.
##XMSG: Error message during  generation of Explain Plan file
EXPLAINPLAN_ERROR=Ocurrió un error durante la generación del archivo de plan de explicación para la vista "{0}".
##XMSG: Explain Plan file cannot be generated for the view {0}, because too many views are stacked on each other
EXPLAINPLAN_MODEL_TOO_COMPLEX=El archivo del Plan de explicación no se puede generar para la vista "{0}". Hay demasiadas vistas apiladas. Estos modelos complejos pueden causar errores de falta de memoria y ralentizar el rendimiento. Recomendamos realizar la persistencia de una vista.

##XMSG: Message to indicate that the space is locked
PERFORMANCE_ANALYSIS_LOCKED_SPACE_ERROR=No se puede ejecutar el análisis de rendimiento para la vista "{0}" porque el espacio "{1}" está bloqueado.
##XMSG: The performance analysis task was cancelled
PERFORMANCE_ANALYSIS_CANCELED=El análisis de rendimiento para la vista "{0}" se canceló.
##XMSG: The performance analysis failed
PERFORMANCE_ANALYSIS_FAILED=Falló el análisis de rendimiento.
##XMSG: The performance analysis is finished
PERFORMANCE_ANALYSIS_FINISHED=Finalizó el análisis de rendimiento para la vista "{0}". Para ver el resultado, haga clic en "Ver detalles".
##XMSG: The view has input parameter with no default value
INPUT_PARAMETER_WITHOUT_DEFAULT_VALUE=No se admite el análisis de esta vista porque tiene un parámetro sin valor predeterminado.
##XMSG: The view is not fully deployed
VIEW_NOT_FULLY_DEPLOYED=Esta vista no se puede analizar porque no está completamente implementada.
##XMSG: The view uses limited remote adapters
LIMITED_REMOTE_ADAPTER=Esta vista usa al menos un adaptador remoto con capacidades limitadas como delegación de filtro faltante o soporte para "Recuento". La persistencia o replicación de objetos puede mejorar el rendimiento del tiempo de ejecución.
##XMSG: The view uses remote adapter that does not support 'Limit'
REMOTE_ADAPTER_NOT_SUPPORTING_LIMIT=Esta vista usa al menos un adaptador remoto que no admite Límite. Es posible que se hayan seleccionado más de 1000 registros.
##XMSG: Performance analysis is executed by using default parameters of view'
INPUT_PARAMETER_WITH_DEFAULT_VALUE=El análisis de rendimiento se ejecutó con los valores predeterminados de los parámetros de la vista.
##XMSG: Message to indicate that an error occurred
PERFORMANCE_ANALYSIS_ERROR=Ocurrió un error durante el análisis de rendimiento para la vista "{0}".
##XMSG: Message start of cancellation for Performance Analysis task
CANCEL_PERFORMANCE_ANALYSIS_TRIGGERED=Se envió el proceso para detener la tarea {0} del análisis de rendimiento para la vista "{1}". Puede haber una demora hasta que se detenga la tarea.
##XMSG: Message start to indicate that task is not active
CANCEL_PERFORMANCE_ANALYSIS_NO_ACTIVE_TASK=La tarea {0} del análisis de rendimiento para la vista "{1}" no está activa.
#XMSG: Cancel Performance Analysis task failure
CANCEL_PERFORMANCE_ANALYSIS_FAILED=Falló la cancelación de la tarea de análisis de rendimiento.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Asignarme programa
#XBUT: Pause schedule menu label
pauseScheduleLabel=Pausar programa
#XBUT: Resume schedule menu label
resumeScheduleLabel=Reanudar programa
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Ocurrió un error al eliminar programas.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Ocurrió un error al asignar programas.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Ocurrió un error al pausar programas.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Ocurrió un error al reanudar programas.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Eliminando {0} programas
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Cambiando el propietario de {0} programas
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Pausando {0} programas
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Reanudando {0} programas
#XBUT: Select Columns Button
selectColumnsBtn=Seleccionar columnas
#XFLD: Refresh tooltip
TEXT_REFRESH=Actualizar
#XFLD: Select Columns tooltip
text_selectColumns=Seleccionar columnas


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Runtime Metrics ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Label for Runtime Metrics
RuntimeMetricsTitle=Métricas de tiempo de ejecución para
#XFLD : Label for Run Button
runButton=Ejecutar
#XFLD : Label for Cancel Button
cancelButton=Cancelar
#XFLD : Label for Close Button
closeButton=Cerrar
#XFLD : Label for Open View Analyzer Button
openViewAnalyzerButton=Abrir analizador de vistas
#XFLD : Label for Generate Explain Plan Button
generateExplainPlanButton=Generar plan de explicación
#XFLD : Label for Previous Run Column
previousRun=Ejecución anterior
#XFLD : Label for Latest Run Column
latestRun=Última ejecución
#XFLD : Label for time Column
time=Hora
#XFLD : Label for Duration Column
duration=Duración
#XFLD : Label for Peak Memory Column
peakMemory=Memoria máxima
#XFLD : Label for Number of Rows
numberOfRows=Número de filas
#XFLD : Label for Overall Number of Sources
overallNumberOfSources=Número total de orígenes
#XFLD : Label for Data Access Column
dataAccess=Acceso a datos
#XFLD : Label for Local Tables
localTables=Tablas locales
#XFLD : Label for federatedRemoteTables
federatedRemoteTables=Tablas remotas federadas (con capacidades de adaptador limitadas)
#XTXT Text for initial state of the runtime metrics
initialState=Primero debe ejecutar el análisis de rendimiento para obtener las métricas. Esto puede llevar tiempo, pero puede cancelar el proceso de ser necesario.
#XMSG: Cancel confirmation message
CancelRunConfirmationMessage=¿Seguro que desea cancelar la ejecución actual del análisis de rendimiento?
#XTIT: Cancel dialog title
CancelRunTitle=Cancelar ejecución
#XFLD: Label for Number of Rows
NUMBER_ROWS=Número de filas
#XFLD: Label for Overall Number of Sources
OVERALL_NUMBER_SOURCES=Número total de orígenes
#XFLD: Label for Data Access
DATA_ACCESS=Acceso a datos
#XFLD: Label for Remote tables
NUMBER_REMOTE_TABLES_LIMITED_ADAPTERS=Tablas remotas federadas (con capacidades de adaptador limitadas)
#XFLD: Label for select statement
SELECT_STATEMENT='SELECT * FROM VIEW LIMIT 1000'
#XFLD: Label for duration
SELECT_RUNTIME=Duración
#XFLD: Label for peak memory
SELECT_PEAK_MEMORY=Memoria máxima
#XFLD: Label for count statement
SELECT_COUNT_STATEMENT='SELECCIONAR RECUENTO(*) DESDE VISTA'
#XFLD: Label for count duration
SELECT_COUNT_RUNTIME=Duración
#XFLD: Label for count peak memory
SELECT_COUNT_PEAK_MEMORY=Memoria máxima
#XFLD: Label for count of number of local tables
NUMBER_LOCAL_TABLES_FILE=Tablas locales (Archivo)
#XTXT: Text for running state of the runtime metrics
Running=En ejecución…
#XFLD: Label for time
Time=Hora
#XFLD: Label for virtual access
PA_VIRTUAL=Virtual
#XFLD: Label for persisted access
PA_PERSISTED=Persistente
PA_PARTIALLY_PERSISTED=Persistencia parcial
#XTXT: Text for cancel
CancelRunSuccessMessage=Cancelando la ejecución del análisis de rendimiento.
#XTXT: Text for cancel error
CancelRunErrorMessage=Ocurrió un error al cancelar la ejecución del análisis de rendimiento.
#XTXT: Text for explain plan generation
ExplainPlanStarted=Generando el plan de explicación para la vista "{0}".
#XTXT: Text for performance analysis
PerformanceAnalysisStarted=Iniciando el análisis de rendimiento para la vista "{0}".
#XTXT: Text for performance analysis error
ErrorFetchingPerformanceAnalysis=Ocurrió un error al recuperar los datos del análisis de rendimiento.
#XTXT: Text for performance analysis error
conflictingTask=La tarea de análisis del rendimiento ya está en curso
#XFLD: Label for Errors
Errors=Error(es)
#XFLD: Label for Warnings
Warnings=Advertencia(s)
#XFLD: Tooltip for missing privileges
openViewAnalyzerButtonTooltipNoPrivilege=Necesita el privilegio DWC_DATAINTEGRATION(update) para abrir el analizador de vistas.
#XFLD: Tooltip for missing privileges
generateExplainPlanButtonTooltipNoPrivilege=Necesita el privilegio DWC_RUNTIME(read) para generar el plan de explicación.



#XFLD: Label for frequency column
everyLabel=Cada
#XFLD: Plural Recurrence text for Hour
hoursLabel=Horas
#XFLD: Plural Recurrence text for Day
daysLabel=Días
#XFLD: Plural Recurrence text for Month
monthsLabel=Meses
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minutos
