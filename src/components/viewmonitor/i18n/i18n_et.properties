
#XBCB: Breadcrumb
viewMonitorBreadcrumb={spaceId}

#XFLD: Label for persisted view column
SOURCE=Allikas
#XFLD: Label for persisted view column
NAME=Nimi
#XFLD: Label for persisted view column
NAME_LABEL=Ärinimi
#XFLD: Label for persisted view column
NAME_LABELNew=Objekt (ärinimi)
#XFLD: Label for persisted view column
TECHINCAL_NAME=Tehniline nimi
#XFLD: Label for persisted view column
TECHINCAL_NAMENew=Objekt (tehniline nimi)
#XFLD: Label for persisted view column
DATA_PERSISTENCY=Andmepääs
#XFLD: Label for persisted view column
STATUS=Olek
#XFLD: Label for persisted view column
LAST_UPDATED=Viimati uuendatud
#XFLD: Label for persisted view column
IN_MEMORY_SIZE=Salvestusruumi jaoks kasutatud mälumaht (MiB)
#XFLD: Label for persisted view column
DISK_SIZE=Salvestusruumi jaoks kasutatud kettaruum (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_NEWW=Suurus mälusiseses ruumis (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_HEADER=Suurus mälusiseses ruumis
#XFLD: Label for persisted view column
DISK_SIZE_NEWW=Suurus kettal (MiB)
#XFLD: Label for persisted view column
DISK_SIZE_HEADER=Suurus kettal
#XFLD: Label for schedule owner column
txtScheduleOwner=Ajakava omanik
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Näitab ajakava loojat
#XFLD: Label for persisted view column
PERSISTED=Püsisalvestatud
#XFLD: Label for persisted view column
TYPE=Tüüp
#XFLD: Label for View Selection Dialog column
changedOn=Muutmiskuupäev
#XFLD: Label for View Selection Dialog column
createdBy=Autor
#XFLD: Label for log details column
txtViewPersistencyLogs=Kuva logid
#XFLD: Label for log details column
txtViewPersistencyLogsNew=Üksikasjad
#XFLD: text for values shown for Ascending sort order
SortInAsc=Sordi kasvavalt
#XFLD: text for values shown for Descending sort order
SortInDesc=Sordi kahanevalt
#XFLD: Label for persisted view monitor heading
ViewMonitor_Heading=Vaadete jälgimisprogramm
#XFLD: Label for persisted view monitor sub-heading
ViewMonitor_SubHeading=Jälgi ja halda vaadete andmete püsisalvestamist


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ DynamicPageHeader ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: text for values shown in column Persistence Status
txtLoading=Laadimine
#XFLD: text for values shown in column Persistence Status
txtRunning=Töötab
#XFLD: text for values shown in column Persistence Status
txtAvailable=Saadaval
#XFLD: text for values shown in column Persistence Status
txtError=Tõrge
#XMSG: Error message for unknown Persistence type that gets logged in Kibana
unknownReplicationTypeError=Alliktabelite replikeerimise tüüp „{0}“ on toetuseta.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Content ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Viimase andmete püsisalvestuskäituse jaoks kasutatud sätted:
#XMSG: Message for input parameter name
inputParameterLabel=Sisendparameeter
#XMSG: Message for input parameter value
inputParameterValueLabel=Väärtus
#XMSG: Message for persisted data
inputParameterPersistedLabel=Püsisalvestatud
#XTIT: Count for View in ViewMonitor Toolbar
txtViewCount=Vaated ({0})
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Kuva püsisalvestus
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Andmete püsivus
#XBUT: Button to clear the selected view persistance
ClearViewPersistance=Tühjenda
#XBUT: Button to stop the selected view persistance
stopPersistance=Peata püsisalvestus
#XFLD: Placeholder for Search field
txtSearch=Otsing
#XBUT: Tooltip for refresh button
txtRefresh=Värskenda
#XBUT: Tooltip for add view button
txtDeleteView=Kustuta püsisalvestus
#XBUT: Tooltip for load new peristence
loadNewPersistence=Taaskäivita püsisalvestus
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Laadi uus hetktõmmis
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Käivita andmete püsisalvestus
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Eemalda püsisalvestatud andmed
#XMSG: success message for starting persistence
startPersistenceSuccess=Püsisalvestame vaadet „{0}“.
#XMSG: success message for stopping persistence
stopPersistenceSuccess=Eemaldame vaate „{0}“ püsisalvestatud andmed.
#XMSG: success message for stopping persistence
deletePersistedViewSuccess=Eemaldame vaate „{0}“ jälgimisloendist.
#XMSG: error message during start persistence of a view (backend error)
ERROR_STARTING_PERSISTENCE=Vaate „{0}“ andmete püsisalvestamise käivitamisel ilmnes tõrge.
#XMSG: error message during start persistence of a view (backend error)
PARAMERROR_STARTING_PERSISTENCE=Vaadet „{0}“ ei saa püsisalvestada, sest see sisaldab sisendparameetreid.
#XMSG: error message during start persistence of a view (backend error)
NUMBER_OF_PARAMETER_STARTING_PERSISTENCE=Vaadet „{0}“ ei saa püsisalvestada, sest see sisaldab rohkem kui ühte sisendparameetrit.
#XMSG: error message during start persistence of a view (backend error)
PARAMETER_DEFAULT_VALUE_STARTING_PERSISTENCE=Vaadet „{0}“ ei saa püsisalvestada, sest sisendparameetril pole vaikeväärtust.
#XMSG: error message during start persistence of a view (backend error)
REDEPLOYMENT_DAC_REQUIRED=Andmete püsisalvestamise toetamiseks on nõutav andmepääsukontrolli (DAC) „{0}“ uuesti juurutamine.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_STARTING_PERSISTENCE=Vaadet „{0}“ ei saa püsisalvestada, kuna see kasutab vaadet „{1}“, mis sisaldab andmepääsukontrolli (DAC).
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_EXTERNAL_STARTING_PERSISTENCE=Vaadet „{0}“ ei saa püsisalvestada, kuna see kasutab andmepääsukontrolliga (DAC) vaadet, mis kuulub teise ruumi.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_STRUCTURE_STARTING_PERSISTENCE=Vaadet „{0}“ ei saa püsisalvestada, sest üks või enam selle andmepääsukontrollidest (DAC) ei toeta andmete püsisalvestamist.
#XMSG: error message during stop persistence of a view (backend error)
STOP_PERSISTENCE_ERROR=Vaate „{0}“ püsisalvestamise peatamisel ilmnes tõrge.
#XMSG: error message during stop persistence of a view (backend error)
DELETE_PERSISTED_VIEW_ERROR=Püsisalvestatud vaate „{0}“ kustutamisel ilmnes tõrge.
#XMSG: Warning message for removing the persisted data
RemovePersistency_Confirm_Msg=Kas soovite püsisalvestatud andmed kustutada ja aktiveerida vaatele „{0}“ virtuaalse juurdepääsu?
#XMSG: Warning message for delete the view persistence
DeletePersistency_Confirm_Msg=Kas soovite eemaldada vaate jälgimisloendist ja kustutada vaate „{0}“ püsisalvestatud andmed?
#XMSG: error message for reading data from backend
txtReadBackendError=Näib, et tagasüsteemist lugemisel ilmnes tõrge.
#XFLD: Label for No Data Error
NoDataError=Tõrge
#XMSG: message for conflicting task
Task_Already_Running=Vaate „{0}“ jaoks käitatakse juba vastuolulist tegumit.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Show All Views~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Text for show all views button
allViewsText=Kõik vaated ({0})
#XBUT: Text for show scheduled views button
scheduledText=Ajastatud ({0})
#XBUT: Text for show persisted views button
persistedText=Püsisalvestatud ({0})
#XBUT: Text for start analyzer button
startAnalyzer=Käivita vaateanalüsaator
#XFLD: Message if repository is unavailable
repositoryErrorMsg=Hoidla pole saadaval ja teatud funktsioonid on keelatud.

#XFLD: Data Access - Virtual
Virtual=Virtuaalne
#XFLD: Data Access - Persisted
Persisted=Püsisalvestatud

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Non-Persisted View Selection Dialog~~~~~~~~~~~~~~~~~~~~~~~

#XTIT: Title for Non-Persisted View selection dialog
select_source=Vali püsisalvestatav vaade

#XTIT: Placeholder for Non-Persisted View selection dialog search
searchView=Otsi vaateid
#XTIT: No data in the list of non-persisted view
No_Data=Andmeid pole
#XBUT: Button to select non-persisted view
ok=OK
#XBUT: Button to close the non-persisted views selection dialog
cancel=Tühista

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~View persistency log message key~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message for view persistency logs
START_STOP_PERSISTENCY=Üksuse „{1}“ püsisalvestustegumi käituse käivitamine.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY=Vaate ''{1}''’ andmete püsisalvestamine.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY)
START_VIEW_PERSISTENCY_PROCESS=Vaate „{0}“ andmete püsisalvestusprotsessi käivitamine.
#XFLD: Message for view persistency manual partition load
START_VIEW_PERSISTENCY_PROCESS_MANUAL_PARTITIONS=Vaate „{0}“ andmete püsisalvestusprotsessi käivitamine valitud partitsiooni ID-dega: „{1}“.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY=Vaate „{1}“ püsisalvestatud andmete eemaldamine.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY)
REMOVE_VIEW_PERSISTENCY=Vaate „{0}“ püsisalvestatud andmete eemaldamise protsessi käivitamine.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_SUCCESS=Vaate „{1}“ andmed on püsisalvestatud.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_SUCCESS)
START_VIEW_PERSISTENCY_PROCESS_SUCCESS=Vaate „{0}“ andmed on püsisalvestatud.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY_SUCCESS=Vaate „{1}“ püsisalvestatud andmed on eemaldatud ja virtuaalne juurdepääs andmetele on taastatud.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_SUCCESS)
REMOVE_VIEW_PERSISTENCY_SUCCESS=Vaate „{0}“ püsisalvestatud andmete eemaldamise protsess on lõpule viidud.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_FAILED=Vaate „{1}“ andmeid ei saa püsisalvestada.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_FAILED)
START_VIEW_PERSISTENCY_PROCESS_FAILED=Vaate „{0}“ andmeid ei saa püsisalvestada.
#XFLD: Message for view persistency logs (for messages created before version 2022.05)
STOP_VIEW_PERSISTENCY_FAILED=Vaate „{1}“ püsisalvestatud andmeid ei saa eemaldada.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_FAILED)
REMOVE_VIEW_PERSISTENCY_FAILED=Vaate „{0}“ püsisalvestatud andmeid ei saa eemaldada.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_SUCCESS=Vaate „{1}“ jaoks on püsisalvestatud „{3}“ kirjet.
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace START_PERSISTENCY_RECORD_COUNT_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=Vaate „{1}“ jaoks andmete püsisalvestamise tabelisse lisatud {0} kirjet.
#XFLD: Message used by SEAL repository for view persistency logs (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS_V2=Vaate „{1}“ jaoks on andmete püsisalvestamise tabelisse lisatud {0} kirjet. Kasutatud mälu: {2} GiB.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_SUCCESS=Vaate „{1}“ „{3}“ püsisalvestatud kirjet on eemaldatud.
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace STOP_PERSISTENCY_RECORD_COUNT_SUCCESS)
REMOVE_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=Püsisalvestatud andmed on eemaldatud, „{0}“ püsisalvestatud kirjet on kustutatud.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_FAILED=Vaate „{1}“ kirjete arvu toomine on nurjunud.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_FAILED=Vaate „{1}“ kirjete arvu toomine on nurjunud.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Üksuse „{1}“ graafik on kustutatud.
#XFLD: Message for view persistency logs (new message to replace STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS)
REMOVE_VIEW_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Vaate „{0}“ graafik on kustutatud.
#XFLD: Message for view persistency logs
STOP_PERSISTENCY_DELETE_SCHEDULE_FAILED=Üksuse „{1}“ graafiku kustutamine nurjus.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_REDEPLOY=Me ei saa vaadet „{0}“ püsisalvestada, kuna seda on muudetud ja juurutatud pärast selle püsisalvestamise algust. Proovige uuesti vaadet püsisalvestada või oodake järgmise ajastatud käituseni.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_DELETE=Me ei saa vaadet „{0}“ püsisalvestada, kuna see on pärast selle püsisalvestamise algust kustutatud.
#XFLD: Message for successful data persistency of a single partition (for messages created before version 2022.03)
START_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} kirjet on püsisalvestatud partitsiooni väärtustele „{1}“ <= {2} < „{3}“.
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (new message to replace START_PERSISTENCY_SINGLE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} kirjet on lisatud partitsiooni väärtustele „{1}“ <= „{2}“ < „{3}“.
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS_V2={0} kirjet on lisatud partitsiooni väärtustele „{1}“ <= „{2}“ < „{3}“. Kasutatud mälu: {4} GiB.
#XFLD: Message for successful replication of a "others" partition (for messages created before version 2022.03)
START_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} kirjet on püsisalvestatud partitsiooni „muud“.
#XFLD: Message used by SEAL repository for successful replication of a "others" partition (new message to replace START_PERSISTENCY_OTHERS_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} kirjet on lisatud partitsiooni „muud“.
#XFLD: Message for view persistency record count of partitions (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={3} kirjet on vaate „{1}“ jaoks püsisalvestatud {4} partitsiooni.
#XFLD: Message used by SEAL repository for view persistency record count of partitions (new message to replace START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS=Vaate „{1}“ jaoks on {2} partitsioonis lisatud andmete püsisalvestamise tabelisse {0} kirjet.
#XFLD: Message used by SEAL repository for view persistency for partition wise refresh
START_VIEW_PERSISTENCY_RECORD_COUNT_REFRESHED_PARTITIONS_SUCCESS={0} kirjet on lisatud vaate „{1}“ andmete püsisalvestamise tabelisse. Uuendatud partitsioonid: {2}; lukustatud partitsioonid: {3}; partitsioone kokku: {4}
#XFLD: Message used by SEAL repository for view persistency for manual partition load
START_VIEW_PERSISTENCY_RECORD_COUNT_MANUAL_PARTITIONS_SUCCESS={0} kirjet lisati andmete püsisalvestamise tabelisse vaate „{1}“ jaoks {2} valitud partitsioonis
#XFLD: Message used by SEAL repository for view persistency for locked partition data load
START_VIEW_PERSISTENCY_RECORD_COUNT_LOCKED_PARTITIONS_SUCCESS={0} kirjet on lisatud vaate „{1}“ andmete püsisalvestamise tabelisse. Uuendatud partitsioonid: {2}; lukustatud muutmata partitsioonid: {3}; partitsioone kokku: {4}
#XMSG: Message for unexpected error (for messages created before version 2022.03)
START_PERSISTENCY_UNEXPECTED_ERROR=Vaate „{0}“ andmete püsisalvestamise ajal ilmnes ootamatu tõrge.
#XMSG: Message for unexpected error (new message to replace START_PERSISTENCY_UNEXPECTED_ERROR)
START_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Vaate „{0}“ andmete püsisalvestamise ajal ilmnes ootamatu tõrge.
#XMSG: Message for Locked space error
START_VIEW_PERSISTENCY_LOCKED_SPACE_ERROR=Vaadet „{0}“ ei saa püsisalvestada, kuna ruum „{1}“ on lukustatud.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
STOP_PERSISTENCY_UNEXPECTED_ERROR=Vaate „{0}“ püsisalvestatud andmete eemaldamise ajal ilmnes ootamatu tõrge.
#XMSG: Message for unexpected error (new message to replace STOP_PERSISTENCY_UNEXPECTED_ERROR)
REMOVE_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Vaate „{0}“ püsisalvestemise eemaldamise ajal ilmnes ootamatu tõrge.
#XMSG: Message for ivalidated view
VIEW_PERSISTENCY_ABORTED_DEFINITION_INVALID=Vaate „{0}“ määratlus on muutunud ebasobivaks, tõenäoliselt vaate otseselt või kaudselt kasutatava objekti muutmise tõttu. Proovige vaade uuesti juurutada, et lahendada probleem või tuvastada algpõhjus.
#XMSG: Message for persisted data removal (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT=Püsisalvestatud andmed eemaldatakse vaate „{0}“ juurutamise ajal.
#XMSG: Message for persisted data removal of a consuming view
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW=Püsisalvestatud andmed eemaldatakse kasutatud vaate „{0}“ juurutamise ajal.
#XMSG: Message for persisted data removal of a consuming view due to a dac change
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW_DAC_CHANGED=Püsisalvestatud andmed eemaldatakse kasutatud vaate „{0}“ juurutamisel, kuna selle andmepääsukontrolli on muudetud.
#XMSG: Message for persisted data removal (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DEPLOYMENT=Püsisalvestatud andmed eemaldatakse vaate „{0}“ juurutamise ajal.
#XMSG: Message for deleted view (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION=Püsisalvestamine eemaldatakse vaate „{0}“ kustutamisega.
#XMSG: Message for deleted view (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DELETION=Püsisalvestamine eemaldatakse vaate „{0}“ kustutamisega.
#XMSG Message for removal of persisted data, because prerequisites not fulfilled
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_PREREQUISITES_NOT_FULFILLED=Püsisalvestatud andmed eemaldatakse, kuna andmete püsisalvestamise eeltingimused pole enam täidetud.
#XMSG: Message for inconsistent persistency
VIEW_PERSISTENCY_INCONSISTENT_PERSISTENCY=Vaate „{0}“ püsisalvestamine on muutunud vastuoluliseks. Probleemi lahendamiseks eemaldage püsisalvestatud andmed.
#XMSG: Message for prerequisites
START_PERSISTENCY_CHECK_PREREQUISITES=Vaate „{0}“ püsisalvestamise eeltingimuste kontrollimine.
#XMSG: Message for DAC deployed in deprecated way using table function and join (for messages created before 2025.05)
START_PERSISTENCY_DEPRECIATED_DAC=Vaade „{0}“ on juurutatud andmepääsukontrolliga (DAC), mis kõrvaldatakse peagi kasutusest. Jõudluse parendamiseks juurutage vaade uuesti.
#XMSG: Message for DAC deployed in deprecated way using table function and join
START_PERSISTENCY_DEPRECATED_DAC=Vaade „{0}“ on juurutatud andmepääsukontrolliga (DAC), mis kõrvaldatakse peagi kasutusest. Jõudluse parendamiseks juurutage vaade uuesti.
#XMSG: Message for roll back (for messages created before version 2022.03)
ROLLBACK_VIEW_PERSISTENCY=Ilmnes tõrge. Vaate „{0}“ püsisalvestamise eelmine olek on taastatud.
#XMSG: Message used by SEAL repository for rollback of running procedure to persist view (new message to replace ROLLBACK_VIEW_PERSISTENCY)
VIEW_PERSISTENCY_ROLLBACK=Ilmnes tõrge. Vaate „{0}“ püsisalvestamisprotsess on peatatud ja muudatused on tagasi võetud.
#XMSG: Message used by SEAL repository for rollback of running procedure to remove persisted data of view
REMOVE_VIEW_PERSISTENCY_ROLLBACK=Ilmnes tõrge. Vaate „{0}“ püsisalvestud andmete eemaldamise protsess on peatatud ja muudatused on tagasi võetud.
#XMSG: Message used by SEAL repository for persistency preparation
START_PERSISTENCY_PREPARATION=Andmete püsisalvestamise ettevalmistamine.
#XMSG: Message used by SEAL repository for data insertion
START_INSERTING_PERSISTENCY_DATA=Andmete lisamine püsisalvestustabelisse.
#XMSG: Message for null values - partitioning (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS={0} tühiväärtusega kirjet lisati partitsiooni „muud“.
#XMSG: Message used by SEAL repository for others partition values - partitioning (new message to replace START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS={0} kirjet on lisatud partitsiooni „muud“ väärtustele „{2}“ < „{1}“ OR „{2}“ >= „{3}“.
#XMSG: Message used by SEAL repository for others partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS_V2={0} kirjet on lisatud partitsiooni „muud“ väärtustele „{2}“ < „{1}“ OR „{2}“ >= „{3}“. Kasutatud mälu: {4} GiB.
#XMSG: Message used by SEAL repository for null partition values - partitioning
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS={0} kirjet on lisatud partitsiooni „muud“ väärtustele „{1}“ IS NULL.
#XMSG: Message used by SEAL repository for null partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS_V2={0} kirjet on lisatud partitsiooni „muud“ väärtustele „{1}“ IS NULL. Kasutatud mälu: {2} GiB.
##XFLD:Message used by SEAL repository for remote statistics
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS=Seotud andmete laadimine: {0} kauglauset. Toodud kirjeid kokku: {1}. Kestus kokku: {2} sekundit.
##XFLD:Message used by SEAL repository for remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS_WITH_PARTITIONS=Seotud andmete laadimine, kasutades {0} partitsiooni {1} kauglausega. Toodud kirjeid kokku: {2}. Kestus kokku: {3} sekundit.
##XFLD:Message used by SEAL repository with UI ID for instructions of remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_INSTRUCTION=Käituse käigus töödeldud kauglausete kuvamiseks saab partitsioonikohaste teadete üksikasjades avada tööriista „Laadimiste monitoorimine“.
##XFLD:Message for start reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS=Protsessi alustamine püsisalvestatud andmete taaskasutamiseks vaatel {0} pärast juurutamist.
##XFLD:Message used by SEAL repository for reattach view persitency
START_VIEW_PERSISTENCY_REATTACHMENT=Alustage olemasolevate püsisalvestatud andmete taaskasutamiseks.
##XFLD:Message used by SEAL repository for reattach view persitency
START_FINILIZING_VIEW_PERSISTENCY_REATTACHMENT=Olemasolevate püsisalvestatud andmete taaskasutamine.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_COMPLETE=Vaatel {0} on lõpetatud olemasolevate püsisalvestatud andmete taaskasutamise protsess.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_FAILED=Olemasolevate püsisalvestatud andmete taaskasutamine nurjus vaatel {0} pärast juurutamist.
#XMSG: Message used by SEAL repository for finalizing persistency
START_FINALIZING_PERSISTENCY=Püsisalvestamise lõpuleviimine.
#XMSG: Message used by SEAL repository for restoring virtual access for view
REMOVE_VIEW_PERSISTENCY_VIRTUAL_ACCESS_RESTORED=Vaate „{0}“ virtuaalne andmepääs on taastatud.
#XMSG: Message used by SEAL repository for view that already has virtual access
REMOVE_VIEW_PERSISTENCY_ALREADY_VIRTUAL_ACCESS=Vaates „{0}“ on virtuaalne andmepääs juba olemas. Püsisalvestatud andmeid ei eemaldata.
#XMSG: Message for removing view persistency
VIEW_PERSISTENCY_REMOVED_FROM_MONITOR=Vaade „{0}“ on vaadete jälgimisprogrammist eemaldatud.
#XMSG: Message for view doesnt exist.
VIEW_PERSISTENCY_VIEW_NOT_EXISTING=Vaadet „{0}“ kas pole andmebaasis olemas või pole see õigesti juurutatud ja seetõttu ei saa seda püsisalvestada. Proovige probleemi lahendamiseks vaade uuesti juurutada või juurpõhjus tuvastada.
#XMSG: Message for persistency not enabled.
VIEW_PERSISTENCY_NOT_ENABLED=Andmete püsisalvestamine pole lubatud. Juurutage tabel/vaade funktsioonide lubamiseks ruumis „{0}“ uuesti.
#XMSG: Message for unexpected error due to node crash
VIEW_PERSISTENCY_UNEXPECTED_ERROR_GENERAL=Viimane vaate püsisalvestamise käitus katkestati tehniliste tõrgete tõttu.
#XFLD: Message for view persistency memory consumption
VIEW_PERSISTENCY_MEMORY_CONSUMPTION=Tippmälukasutus {0} GiB vaate püsisalvestamise käitusajal.
#XFLD: Message for view persistency timeout
VIEW_PERSISTENCY_TIMEOUT=Vaate {0} püsisalvestamine jõudis ajalõpuni {1} tundi.
#XFLD: Message for not started asynchronous view persistency
VIEW_PERSISTENCY_ASYNC_NOT_STARTED=Süsteemi suur koormus takistas vaate püsisalvestamise asünkroonse käivitamise algusest. Kontrollige, kas paralleelselt töötab liiga palju tegumeid.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency (for messages created before version 2022.05)
START_VIEW_PERSISTENCY_EXECUTION_METHOD_DROP_AND_RENAME=Olemasolev püsisalvestatud tabel on kustutatud ja asendatud uue püsisalvestustatud tabeliga.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_RECREATE_AND_DROP=Olemasolev püsisalvestatud tabel on kustutatud ja asendatud uue püsisalvestustatud tabeliga.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_TRUNCATE_AND_COPY=Olemasolev püsisalvestatud tabel on uuendatud uute andmetega.
##XFLD: Message for missing authorizations for view persistency
VIEW_PERSISTENCY_MISSING_AUTH=Andmete püsisalvestamise õigus puudub.
##XFLD: Message start of persistency cancellation
VIEW_PERSISTENCY_TRIGGER_CANCEL=Protsessi tühistamise alustamine {0} vaate säilitamiseks.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_UNAVAILABLE_TASK_RUN=Vaate püsisalvestamise protsessi tühistamine nurjus, kuna vaate {0} jaoks puudub käitatav andmete püsivsalvestustegum.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_NONDETERMINISTIC_TASK_RUN=Vaate püsisalvestamise protsessi tühistamine nurjus, kuna vaate {0} vaate jaoks ei käitata ühtki andmete püsisalvestustegumit.
##XFLD: Message for canceling a non running persitency task
VIEW_PERSISTENCY_CANCEL_NON_RUNNING_SELECTED_TASK=Vaate {0} püsisalvestusprotsessi tühistamine nurjus, kuna valitud andmete püsisalvestustegumit {1} ei käitata.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_UNREACHABLE_PERSISTENCY_RUN=Vaate säilitamise protsessi tühistamine nurjus, kuna {0} vaate andmete püsivus ei ole veel alanud.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_COMPLETED_PERSISTENCY_RUN={0} vaate säilitamise protsessi tühistamine nurjus, kuna see on juba lõpule viidud.
##XFLD: Message for failed cancellation
VIEW_PERSISTENCY_ABORTED_CANCEL_EXECUTION=Protsessi tühistamine {0} vaate säilitamiseks nurjus.
##XFLD: Message for finalizing persistency cancellation
VIEW_PERSISTENCY_FINALIZE_CANCEL=Vaate {0} andmete püsisalvestamise lõpetamisprotsess on edastatud.
##XFLD: Message used in persist task when cancellation is caught
VIEW_PERSISTENCY_CANCELED=Vaate {0} püsisalvestamisprotsess on lõpetatud.
##XFLD: Message used in persist task when cancelation is caught and performed through a cancelation task
VIEW_PERSISTENCY_CANCELED_VIA_CANCEL_TASK=Vaate {0} püsisalvestamisprotsess on lõpetatud tühistamistegumiga {1}.
##XFLD: Message used in cancel task when the task is triggered by deployment
VIEW_PERSISTENCY_CANCEL_TRIGGERED_BY_DEPLOYMENT=Vaate {0} juurutamisel andmete püsisalvestusprotsessi tühistamine.
##XFLD: Message used in case the cancelation is retriggered for a running task
VIEW_PERSISTENCY_CANCEL_ATTEMPT_RETRIGGER_CANCEL=Vaate {0} püsisalvestamise tühistamise eelmine tegum on juba esitatud.
##XFLD: Message used to express the delay that may occur before the persistency task is stopped
VIEW_PERSISTENCY_CANCEL_DELAY_STOPPING_PERSISTENCY=Vaate {0} andmete püsisalvestustegumi peatamine võib viibida.
##XFLD: Message used to reflect the task log identifier of the current running persistency task
VIEW_PERSISTENCY_CANCEL_CURRENT_RUNNING_TASK=Vaate {0} andmeid säilitatakse koos ülesandega {1}.
##XFLD:Message used by SEAL repository for locked partitions and DACs
VIEW_PERSISTENCY_LOCKED_PARTITIONS_DACS=DAC-de antud volitused võivad olla muutunud ja lukustatud partitsioonid neid ei arvesta. Avage partitsioonid ja laadige muudatuste rakendamiseks uus hetktõmmis.
##XFLD:Message used by SEAL repository for column structure change
VIEW_PERSISTENCY_COLUMN_STRUCTURE_CHANGE=Veeru struktuuri on muudetud ja see ei vasta enam olemasolevale püsisalvestustabelile. Eemaldage püsisalvestatud andmed ja käivitage uus andmete püsisalvestus, et püsisalvestustabelit värskendada.
##XFLD: Message for out of memory
VIEW_PERSISTENCY_HANA_OUT_OF_MEMORY=Tegum nurjus mälu puudumise tõrke tõttu SAP HANA andmebaasis.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_EXCEPTION_TRANSACTION_ROLLBACK=Tegum nurjus sisemise erandi tõttu SAP HANA andmebaasis.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_SQL_EXECUTION_ISSUE=Tegum nurjus sisemise SQL-i käitusprobleemi tõttu SAP HANA andmebaasis.
##XFLD: Message for HANA out of memory result
VIEW_PERSISTENCY_OUT_OF_MEMORY_RESULT_MESSAGE=HANA mälu puudumise sündmuse põhjus: {0}
##XFLD: Message for admission control rejection
VIEW_PERSISTENCY_HANA_ADMISSION_CTRL_REJECTION=Tegum nurjus SAP HANA vastuvõtukontrolli tagasilükkamise tõttu.
##XFLD: Message for HANA back pressure / rate limit of HANA client
VIEW_PERSISTENCY_HANA_CLIENT_RATE_LIMIT_REJECTION=Tegum nurjus, kuna on liiga palju aktiivseid SAP HANA ühendusi.
#XMSG: Message for invalid persisted table name
VIEW_PERSISTENCY_PERSISTED_TABLE_INVALID=Ilmnes tõrge ja püsisalvestatud tabel on muutunud kehtetuks. Probleemi lahendamiseks eemaldage püsisalvestatud andmed ja püsisalvestage vaade uuesti.
#XMSG: Message for usahe of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION=Vaadet ei saa püsisalvestada. See kasutab kaugtabelit, mis põhineb kaugallikal, kus on kasutajate levitamine lubatud. Kontrollige vaate järglust.
#XMSG: Message for dynamic usage of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION_DYNAMIC=Vaadet ei saa püsisalvestada. See kasutab kaugtabelit, mis põhineb kaugallikal, kus on kasutajate levitamine lubatud. On võimalik, et kaugtabelit tarbitakse dünaamiliselt SQL-i skripti vaate kaudu. Vaate järglus ei pruugi kaugtabelit kuvada.
#XMSG: Message for insufficient privileges or consumption of view with DAC via dynamic SQL
VIEW_PERSISTENCY_INSUF_PRIV_OR_DAC_DYN_SQL=Teie õigused ei pruugi olla piisavad. Avage andmete eelvaade, et näha, kas teil on vajalikud õigused olemas. Kui teil on õigused olemas, on võimalik, et dünaamilise SQL-i skripti kaudu kasutatavale teisele vaatele on rakendatud andmepääsu reguleerimine (DAC).
#XMSG: deployment of DAC is deprecated and view need to be redeployed to be able to persist data
VIEW_PERSISTENCY_DEPRECATED_DAC=Vaade „{0}“ on juurutatud andmepääsukontrolliga (DAC), mis kõrvaldatakse peagi kasutusest. Vaates andmete püsisalvestamiseks juurutage vaade uuesti.
##XFLD:Message used by SEAL repository for input parameter
START_VIEW_PERSISTENCY_INPUT_PARAMETER=Sisendparameetri „{1}“ jaoks kasutatakse vaikeväärtust „{0}“.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Elastic compute node keys~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
##XFLD:Message used to reflect disablement of ecn replica
ECN_DISABLE_EXISTING_REPLICA=Elastse andmetöötlussõlme koopia on keelatud.
##XFLD:Message used to reflect recreation of ecn replica
ECN_RECREATE_EXISTING_REPLICA=Elastse andmetöötlussõlme koopia on uuesti loodud.
##XFLD:Message used to reflect re-enablement of ecn replica
ECN_ENABLE_EXISTING_REPLICA=Elastse andmetöötlussõlme koopia on uuesti lubatud.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Task Schedule Menu key~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message to schedule task
scheduleText=Graafik
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Loo graafik
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Redigeeri graafikut
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Kustuta graafik
#XFLD: Refresh frequency field
refreshFrequency=Värskendamise sagedus
#XFLD: Refresh frequency field
refreshFrequencyNew=Sagedus
#XFLD: Refresh frequency field
refreshFrequencyNewNew=Ajastatud sagedus
#XBUT: label for None
none=Pole
#XBUT: label for Real-Time replication state
realtime=Reaalajas
#XFLD: Label for table column
txtNextSchedule=Järgmine käitus
#XFLD: Label for table column
txtNextScheduleNew=Järgmine ajastatud käitus
#XFLD: Label for table column
txtNumOfRecords=Kirjete arv
#XFLD: Label for scheduled link
scheduledTxt=Ajastatud
#XFLD: LABEL for partially persisted link
partiallyPersisted=Osaliselt püsisalvestatud
#XFLD: Text for paused text
paused=Peatatud

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ View Monitor Tasklog Details Action keys~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message for release lock actionn
releaseLockMsg=Kui käitus võtab tavapärasest kauem aega, võib see näidata, et see nurjus ja olekut pole vastavalt uuendatud. \r\n Probleemi lahendamiseks saate luku vabastada ja määrata selle olekuks Nurjunud.
#XFLD: Label for release lock dialog
releaseLockText=Vabasta lukk

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Column header tooltip ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: tooltip for table column
txtViewTooltip=Püsisalvestatud vaate nimi
#XFLD: tooltip for table column
txtViewDataAccessTooltip=See näitab vaate saadavust
#XFLD: tooltip for table column
txtViewRefreshFreqTooltip=See näitab, kas vaate jaoks on määratletud graafik
#XFLD: tooltip for table column
txtViewStatusTooltip=Too püsisalvestatud vaate olek
#XFLD: tooltip for table column
txtViewLatestUpdateTooltip=Pakub teavet püsisalvestatud vaate viimase uuendamise kohta
#XFLD: tooltip for table column
txtViewNextRunTooltip=Kui vaate jaoks on määratud graafik, vaadake, mis ajaks on ajastatud järgmine käitus.
#XFLD: tooltip for table column
txtViewNumOfRecordsTooltip=Jälgige kirjete arvu.
#XFLD: tooltip for table column
txtViewInMemorySizeTooltip=Jälgige, kui palju mahtu vaade teie mälus kasutab
#XFLD: tooltip for table column
txtViewDiskSizeTooltip=Jälgige, kui palju mahtu vaade teie kettal võtab
#XMSG: Expired text
txtExpired=Aegunud

#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Objekti „{0}“ ei saa tegumiahelasse lisada.

#~~~~~~~~~~~View Analyzer task log messages~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message indicating number of records for a view, and memory consumption in Mebibyte during simulation of view persistency
RECORDS_PEAK_MEMORY=Vaatel „{0}“ on {1} kirjet. Vaate andmete püsisalvestamise simulatsioon kasutas {2} MiB mälu.
#XMSG: View Analyzer execution failure
VIEW_ANALYZER_EXECUTION_FAILED=Vaate analüüsija käivitamine nurjus.
#XMSG: Missing authorizations for View Analyzer
VIEW_ANALYZER_MISSING_AUTH=Vaate analüüsija õigused puuduvad.
#XMSG: Maximum memory in Gibibyte is reached, therefore persistency simulation is stopped
MAXIMUM_MEMORY_REACHED=Vaate „{1}“ andmete püsisalvestamise simuleerimisel on täitunud maksimummälu {0} GiB. Seetõttu ei käivitata rohkem andmete püsisalvestamise simulatsioone.
#XMSG: Error during persistency simulation occurred
PERSISTENCY_SIMULATION_ERROR=Vaate „{0}“ andmete püsisalvestamise simuleerimisel ilmnes tõrge.
#XMSG: Persistency simulation is not executed, because prerequisites are not fulfilled and the view cannot be persisted
PREREQUISITES_NOT_FULFILLED=Vaate „{0}“ andmete püsisalvestamise simulatsiooni ei teostata, kuna eeltingimused ei ole täidetud ja vaadet ei saa püsisalvestada.
#XMSG: Persistency simulation is not executed, because the view does not exist in the database
VIEW_DOES_NOT_EXIST=Andmete püsisalvestamise simulatsiooni lubamiseks peate juurutama vaate „{0}“.
#XMSG: Number of records cannot be determined, because the local table does not exist in the database
TABLE_DOES_NOT_EXIST=Kohalikku tabelit „{0}“ pole andmebaasis olemas. Seetõttu ei saa kirjete arvu selle tabeli jaoks määratleda.
##XMSG: Message start of cancellation for View Analyzer task
CANCEL_VIEW_ANALYZER_TRIGGERED=Vaate „{1}“ vaateanalüsaatori tegumi {0} lõpetamise protsess on esitatud. Tegumi lõpetamine võib toimuda viivitusega.
##XMSG: Message start to indicate that task is not active
CANCEL_VIEW_ANALYZER_NO_ACTIVE_TASK=Vaate „{1}“ vaateanalüsaatori tegum {0} pole aktiivne.
#XMSG: Cancel View Analyzer task failure
CANCEL_VIEW_ANALYZER_FAILED=Vaateanalüsaatori tegumi tühistamine on nurjunud.
##XMSG: Message used in View Analyzer execution task when task is canceled
VIEW_ANALYZER_CANCELED=Vaate „{0}“ jaoks vaateanalüsaatori täitmine lõpetati tühistamistegumiga.
##XMSG: Message start of cancelation for Model Validation task
CANCEL_MODEL_VALIDATION_TRIGGERED=Vaate „{1}“ mudeli valideerimise tegumi {0} lõpetamise protsess on esitatud. Tegumi lõpetamine võib toimuda viivitusega.
##XMSG: Message to indicate that task is not active
CANCEL_MODEL_VALIDATION_NO_ACTIVE_TASK=Vaate „{1}“ mudeli valideerimise tegum {0} pole aktiivne.
#XMSG: Cancel Model Validation task failure
CANCEL_MODEL_VALIDATION_FAILED=Mudeli valideerimise tegumit ei saanud tühistada.
##XMSG: Message used in Model Validation task when task is canceled
MODEL_VALIDATION_CANCELED=Vaate „{0}“ jaoks mudeli valideerimise täitmine lõpetati tühistamistegumiga.
#XMSG: Message for Locked space error, before starting Model Validation task
MODEL_VALIDATION_LOCKED_SPACE_ERROR=Vaate „{0}“ jaoks ei saa mudeli valideerimist käivitada, kuna ruum „{1}“ on lukustatud.
##XMSG: Message used in View Analyzer execution task when determination of number of rows fails
NUMBER_ROWS_DETERMINATION_FAILED=Kohaliku tabeli „{0}“ ridade arvu määramisel ilmnes tõrge.
##XMSG: Message to indicate that SQL plan was created
EXECUTION_PLAN_CREATED=SQL-i analüsaatori plaanifail on vaate „{0}“ jaoks loodud ja selle saab alla laadida.
#XFLD: Message to indicate starting of process to generate SQL Analyzer plan file
START_PLANVIZ_EXECUTION=Vaate „{0}“ jaoks SQL-i analüsaatori plaanifaili genereerimise käivitamine.
#XFLD: Message to indicate starting of View Analyzer process
VIEW_ANALYZER_EXECUTION_STARTED=Vaateanalüsaatori täitmise käivitamine.
##XMSG: Message to indicate that SQL plan file cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_PLANVIZ_NOT_FULFILLED=Vaate „{0}“ jaoks ei saa SQL-i analüsaatori plaanifaili genereerida, kuna andmete püsisalvestamise eeldused pole täidetud.
##XMSG: Error message during  generation of SQL Analyzer plan file
PLANVIZ_ERROR=Vaate „{0}“ jaoks SQL-i analüsaatori plaanifaili genereerimisel ilmnes tõrge.
#XMSG: Message for Locked space error
VIEW_ANALYZER_LOCKED_SPACE_ERROR=Vaate „{0}“ jaoks ei saa vaateanalüsaatorit käivitada, kuna ruum „{1}“ on lukustatud.
#XMSG: Message for persistency simulation without partitioning
PERSISTENCY_SIMULATION_WITHOUT_PARTITIONS=Andmete püsisalvestamise simulatsioonis ei võeta arvesse vaate „{0}“ partitsioone.
#XMSG: Message for generation of SQL Analyzer plan file without considering partitioning
PLANVIZ_WITHOUT_PARTITIONS=SQL-i analüsaatori plaanifaili genereerimisel ei võeta arvesse vaate „{0}“ partitsioone.
#XMSG: Confirmation message for mass removal of persisted data
MassRemovePersistency_Confirm_Msg=Kas soovite püsisalvestatud andmed eemaldada ja lülitada andmepääsu tagasi virtuaalpääsule?
#XMSG: Confirmation message for partial mass removal of selected persisted data
PartialMassRemovePersistency_Confirm_Msg={0} valitud vaatel {1} vaatest on püsisalvestatud andmeid. \n Kas soovite püsisalvestatud andmed eemaldada ja lülitada andmepääsu tagasi virtuaalpääsule?
#XMSG: success message for stopping mass persistence
stopMassPersistenceSuccess=Eemaldame valitud vaadete püsisalvestatud andmeid.
#XMSG: error message during mass stop persistence of a view (backend error)
STOP_MASS_PERSISTENCE_ERROR=Valitud vaadete püsisalvestamise peatamisel ilmnes tõrge.
#XMSG: memory analysis is only performed in space {0}
NO_MEMORY_ANALYSIS=Mäluanalüüs tehakse ainult ruumis „{0}“ asuvate olemite jaoks: „{1}“ „{2}“ on vahele jäetud.
##XMSG: Message to indicate that Explain Plan cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_EXPLAINPLAN_NOT_FULFILLED=Vaate „{0}“ jaoks ei saa plaaniselgituse faili genereerida, kuna püsisalvestamise eeltingimused pole täidetud.
#XMSG: Message for Explain Plan generation without partitioning
EXPLAINPLAN_WITHOUT_PARTITIONS=Plaaniselgituse faili genereerimisel ei võeta arvesse vaate „{0}“ sektsioone.
#XFLD: Message to indicate starting of process to generate Explain Plan file
START_EXPLAINPLAN_EXECUTION=Vaate „{0}“ jaoks käivitatakse plaaniselgituse faili genereerimine.
##XMSG: Message to indicate that Explain Plan was created
EXPLAINPLAN_CREATED=Plaaniselgituse fail on vaate „{0}“ jaoks genereeritud. Saate selle kuvamiseks klõpsata nuppu „Kuva üksikasjad“ või laadida selle alla, kui teil on vastav õigus olemas.
##XMSG: Error message during  generation of Explain Plan file
EXPLAINPLAN_ERROR=Vaate „{0}“ jaoks plaaniselgituse faili genereerimisel ilmnes tõrge.
##XMSG: Explain Plan file cannot be generated for the view {0}, because too many views are stacked on each other
EXPLAINPLAN_MODEL_TOO_COMPLEX=Vaate „{0}“ jaoks ei saa plaaniselgituse faili genereerida. Üksteise peale on virnastatud liiga palju vaateid. Keerukad mudelid võivad põhjustada ebapiisavast mälust tingitud tõrkeid ja muuta töö aeglaseks. Soovitatav on vaade püsisalvestada.

##XMSG: Message to indicate that the space is locked
PERFORMANCE_ANALYSIS_LOCKED_SPACE_ERROR=Vaate „{0}“ jaoks ei saa jõudlusanalüüsi käivitada, kuna ruum „{1}“ on lukustatud.
##XMSG: The performance analysis task was cancelled
PERFORMANCE_ANALYSIS_CANCELED=Vaate „{0}“ jõudlusanalüüs on tühistatud.
##XMSG: The performance analysis failed
PERFORMANCE_ANALYSIS_FAILED=Jõudlusanalüüs nurjus.
##XMSG: The performance analysis is finished
PERFORMANCE_ANALYSIS_FINISHED=Vaate „{0}“ jõudlusanalüüs jõudis lõpule. Tulemuse kuvamiseks klõpsake nuppu „Kuva üksikasjad“.
##XMSG: The view has input parameter with no default value
INPUT_PARAMETER_WITHOUT_DEFAULT_VALUE=Vaadet ei saa analüüsida, sest see sisaldab ilma vaikeväärtuseta parameetrit.
##XMSG: The view is not fully deployed
VIEW_NOT_FULLY_DEPLOYED=Seda vaadet ei saa analüüsida, kuna see pole täielikult juurutatud.
##XMSG: The view uses limited remote adapters
LIMITED_REMOTE_ADAPTER=See vaade kasutab vähemalt ühte kaugadapterit, mille funktsionaalsus on piiratud (nt on puudu filtri pinustamine või funktsiooni „Loendus“ tugi). Objektide püsisalvestamine või tiražeerimine võib käitusaja jõudlust paremaks muuta.
##XMSG: The view uses remote adapter that does not support 'Limit'
REMOTE_ADAPTER_NOT_SUPPORTING_LIMIT=See vaade kasutab vähemalt ühte kaugadapterit, mis ei toeta limiiti. On võimalik, et valitud on rohkem kui 1000 kirjet.
##XMSG: Performance analysis is executed by using default parameters of view'
INPUT_PARAMETER_WITH_DEFAULT_VALUE=Jõudlusanalüüsi käivitamiseks kasutatakse vaateparameetrite vaikeväärtuseid.
##XMSG: Message to indicate that an error occurred
PERFORMANCE_ANALYSIS_ERROR=Vaate „{0}“ jõudlusanalüüsi käigus ilmnes tõrge.
##XMSG: Message start of cancellation for Performance Analysis task
CANCEL_PERFORMANCE_ANALYSIS_TRIGGERED=Vaate „{1}“ jõudlusanalüüsi tegumi {0} lõpetamise protsess on esitatud. Tegumi lõpetamine võib toimuda viivitusega.
##XMSG: Message start to indicate that task is not active
CANCEL_PERFORMANCE_ANALYSIS_NO_ACTIVE_TASK=Vaate „{1}“ jõudlusanalüüsi tegum {0} pole aktiivne.
#XMSG: Cancel Performance Analysis task failure
CANCEL_PERFORMANCE_ANALYSIS_FAILED=Jõudlusanalüüsi tegumit ei saanud tühistada.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Määra ajakava mulle
#XBUT: Pause schedule menu label
pauseScheduleLabel=Peata ajakava
#XBUT: Resume schedule menu label
resumeScheduleLabel=Jätka ajakava
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Ajakavade eemaldamisel ilmnes tõrge.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Ajakavade määramisel ilmnes tõrge.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Ajakavade peatamisel ilmnes tõrge.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Ajakavade jätkamisel ilmnes tõrge.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted={0} ajakava kustutamine
#XMSG: Message for starting mass assign of schedules
massAssignStarted={0} ajakava omaniku muutmine
#XMSG: Message for starting mass pausing of schedules
massPauseStarted={0} ajakava peatamine
#XMSG: Message for starting mass resuming of schedules
massResumeStarted={0} ajakava jätkamine
#XBUT: Select Columns Button
selectColumnsBtn=Vali veerud
#XFLD: Refresh tooltip
TEXT_REFRESH=Värskenda
#XFLD: Select Columns tooltip
text_selectColumns=Vali veerud


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Runtime Metrics ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Label for Runtime Metrics
RuntimeMetricsTitle=Käitusaja mõõdikud:
#XFLD : Label for Run Button
runButton=Käivita
#XFLD : Label for Cancel Button
cancelButton=Tühista
#XFLD : Label for Close Button
closeButton=Sule
#XFLD : Label for Open View Analyzer Button
openViewAnalyzerButton=Ava vaateanalüsaator
#XFLD : Label for Generate Explain Plan Button
generateExplainPlanButton=Genereeri plaaniselgitus
#XFLD : Label for Previous Run Column
previousRun=Eelmine käitus
#XFLD : Label for Latest Run Column
latestRun=Viimane käitus
#XFLD : Label for time Column
time=Kellaaeg
#XFLD : Label for Duration Column
duration=Kestus
#XFLD : Label for Peak Memory Column
peakMemory=Suurim mälukasutus
#XFLD : Label for Number of Rows
numberOfRows=Ridade arv
#XFLD : Label for Overall Number of Sources
overallNumberOfSources=Allikate koguarv
#XFLD : Label for Data Access Column
dataAccess=Andmepääs
#XFLD : Label for Local Tables
localTables=Kohalikud tabelid
#XFLD : Label for federatedRemoteTables
federatedRemoteTables=Koondatud kaugtabelid (piiratud adapterifunktsioonidega)
#XTXT Text for initial state of the runtime metrics
initialState=Mõõdikute toomiseks peate esmalt käivitama jõudlusanalüüsi. See võib võtta aega, kuid vajaduse korral saate protsessi tühistada.
#XMSG: Cancel confirmation message
CancelRunConfirmationMessage=Kas soovite kindlasti jõudlusanalüüsi praeguse käituse tühistada?
#XTIT: Cancel dialog title
CancelRunTitle=Käituse tühistamine
#XFLD: Label for Number of Rows
NUMBER_ROWS=Ridade arv
#XFLD: Label for Overall Number of Sources
OVERALL_NUMBER_SOURCES=Allikate koguarv
#XFLD: Label for Data Access
DATA_ACCESS=Andmepääs
#XFLD: Label for Remote tables
NUMBER_REMOTE_TABLES_LIMITED_ADAPTERS=Koondatud kaugtabelid (piiratud adapterifunktsioonidega)
#XFLD: Label for select statement
SELECT_STATEMENT='SELECT * FROM VIEW LIMIT 1000'
#XFLD: Label for duration
SELECT_RUNTIME=Kestus
#XFLD: Label for peak memory
SELECT_PEAK_MEMORY=Suurim mälukasutus
#XFLD: Label for count statement
SELECT_COUNT_STATEMENT='SELECT COUNT(*) FROM VIEW'
#XFLD: Label for count duration
SELECT_COUNT_RUNTIME=Kestus
#XFLD: Label for count peak memory
SELECT_COUNT_PEAK_MEMORY=Suurim mälukasutus
#XFLD: Label for count of number of local tables
NUMBER_LOCAL_TABLES_FILE=Kohalikud tabelid (fail)
#XTXT: Text for running state of the runtime metrics
Running=Töötab...
#XFLD: Label for time
Time=Kellaaeg
#XFLD: Label for virtual access
PA_VIRTUAL=Virtuaalne
#XFLD: Label for persisted access
PA_PERSISTED=Püsisalvestatud
PA_PARTIALLY_PERSISTED=Osaliselt püsisalvestatud
#XTXT: Text for cancel
CancelRunSuccessMessage=Jõudlusanalüüsi käituse tühistamine
#XTXT: Text for cancel error
CancelRunErrorMessage=Jõudlusanalüüsi käituse tühistamisel ilmnes tõrge.
#XTXT: Text for explain plan generation
ExplainPlanStarted=Vaate „{0}“ jaoks genereeritakse plaaniselgitust.
#XTXT: Text for performance analysis
PerformanceAnalysisStarted=Vaate „{0}“ jaoks käivitatakse jõudlusanalüüs.
#XTXT: Text for performance analysis error
ErrorFetchingPerformanceAnalysis=Jõudlusanalüüsi andmete toomisel ilmnes tõrge.
#XTXT: Text for performance analysis error
conflictingTask=Jõudlusanalüüsi tegum juba töötab
#XFLD: Label for Errors
Errors=Tõrked
#XFLD: Label for Warnings
Warnings=Hoiatused
#XFLD: Tooltip for missing privileges
openViewAnalyzerButtonTooltipNoPrivilege=Vaateanalüsaatori avamiseks vajate õigust DWC_DATAINTEGRATION(update).
#XFLD: Tooltip for missing privileges
generateExplainPlanButtonTooltipNoPrivilege=Selgitusplaani genereerimiseks vajate õigust DWC_RUNTIME(read).



#XFLD: Label for frequency column
everyLabel=Iga
#XFLD: Plural Recurrence text for Hour
hoursLabel=tunni järel
#XFLD: Plural Recurrence text for Day
daysLabel=päeva järel
#XFLD: Plural Recurrence text for Month
monthsLabel=kuu järel
#XFLD: Plural Recurrence text for Minutes
minutesLabel=minuti järel
