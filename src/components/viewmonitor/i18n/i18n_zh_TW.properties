
#XBCB: Breadcrumb
viewMonitorBreadcrumb={spaceId}

#XFLD: Label for persisted view column
SOURCE=來源
#XFLD: Label for persisted view column
NAME=名稱
#XFLD: Label for persisted view column
NAME_LABEL=業務名稱
#XFLD: Label for persisted view column
NAME_LABELNew=物件 (業務名稱)
#XFLD: Label for persisted view column
TECHINCAL_NAME=技術名稱
#XFLD: Label for persisted view column
TECHINCAL_NAMENew=物件 (技術名稱)
#XFLD: Label for persisted view column
DATA_PERSISTENCY=資料存取
#XFLD: Label for persisted view column
STATUS=狀態
#XFLD: Label for persisted view column
LAST_UPDATED=最後更新
#XFLD: Label for persisted view column
IN_MEMORY_SIZE=用於儲存的記憶體 (MiB)
#XFLD: Label for persisted view column
DISK_SIZE=用於儲存的硬碟 (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_NEWW=大小記憶體內 (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_HEADER=大小記憶體內
#XFLD: Label for persisted view column
DISK_SIZE_NEWW=硬碟大小 (MiB)
#XFLD: Label for persisted view column
DISK_SIZE_HEADER=硬碟大小
#XFLD: Label for schedule owner column
txtScheduleOwner=排程所有人
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=顯示建立排程的人員
#XFLD: Label for persisted view column
PERSISTED=持續
#XFLD: Label for persisted view column
TYPE=類型
#XFLD: Label for View Selection Dialog column
changedOn=更改日期
#XFLD: Label for View Selection Dialog column
createdBy=建立者
#XFLD: Label for log details column
txtViewPersistencyLogs=檢視日誌
#XFLD: Label for log details column
txtViewPersistencyLogsNew=明細
#XFLD: text for values shown for Ascending sort order
SortInAsc=升冪排序
#XFLD: text for values shown for Descending sort order
SortInDesc=降冪排序
#XFLD: Label for persisted view monitor heading
ViewMonitor_Heading=檢視監控器
#XFLD: Label for persisted view monitor sub-heading
ViewMonitor_SubHeading=監控和維護檢視的資料存續性


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ DynamicPageHeader ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: text for values shown in column Persistence Status
txtLoading=載入中
#XFLD: text for values shown in column Persistence Status
txtRunning=執行中
#XFLD: text for values shown in column Persistence Status
txtAvailable=可用
#XFLD: text for values shown in column Persistence Status
txtError=錯誤
#XMSG: Error message for unknown Persistence type that gets logged in Kibana
unknownReplicationTypeError=不支援複製類型 "{0}"。

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Content ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=設定已用於最後一次資料存續性執行：
#XMSG: Message for input parameter name
inputParameterLabel=輸入參數
#XMSG: Message for input parameter value
inputParameterValueLabel=值
#XMSG: Message for persisted data
inputParameterPersistedLabel=保存時間
#XTIT: Count for View in ViewMonitor Toolbar
txtViewCount=檢視 ({0})
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=檢視存續性
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=資料存續性
#XBUT: Button to clear the selected view persistance
ClearViewPersistance=清除
#XBUT: Button to stop the selected view persistance
stopPersistance=停止存續性
#XFLD: Placeholder for Search field
txtSearch=搜尋
#XBUT: Tooltip for refresh button
txtRefresh=重新整理
#XBUT: Tooltip for add view button
txtDeleteView=刪除存續性
#XBUT: Tooltip for load new peristence
loadNewPersistence=重新開始存續性
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=載入新概要
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=開始資料存續性
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=移除存續性資料
#XMSG: success message for starting persistence
startPersistenceSuccess=系統正保存檢視 "{0}"。
#XMSG: success message for stopping persistence
stopPersistenceSuccess=系統正移除檢視 "{0}" 的存續性資料。
#XMSG: success message for stopping persistence
deletePersistedViewSuccess=系統正從監控清單中移除檢視 "{0}"。
#XMSG: error message during start persistence of a view (backend error)
ERROR_STARTING_PERSISTENCE=開始檢視 "{0}" 的資料存續性時發生錯誤。
#XMSG: error message during start persistence of a view (backend error)
PARAMERROR_STARTING_PERSISTENCE=由於檢視 "{0}" 包含輸入參數，因此無法保存。
#XMSG: error message during start persistence of a view (backend error)
NUMBER_OF_PARAMETER_STARTING_PERSISTENCE=由於檢視 "{0}" 包含超過一個輸入參數，因此無法保存。
#XMSG: error message during start persistence of a view (backend error)
PARAMETER_DEFAULT_VALUE_STARTING_PERSISTENCE=由於輸入參數沒有預設值，因此無法保存檢視 "{0}"。
#XMSG: error message during start persistence of a view (backend error)
REDEPLOYMENT_DAC_REQUIRED=需要重新部署資料存取控制 (DAC) "{0}" 才能支援資料存續性。
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_STARTING_PERSISTENCE=由於檢視 "{0}" 使用包含資料存取控制 (DAC) 的檢視 "{1}"，因此無法保存。
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_EXTERNAL_STARTING_PERSISTENCE=由於檢視 "{0}" 使用具有屬於不同空間的資料存取控制 (DAC)，因此無法保存。
#XMSG: error message during start persistence of a view (backend error)
DACERROR_STRUCTURE_STARTING_PERSISTENCE=由於檢視 "{0}" 的一或多個資料存取控制 (DAC) 結構不支援資料存續性，因此無法保存該檢視。
#XMSG: error message during stop persistence of a view (backend error)
STOP_PERSISTENCE_ERROR=停止檢視 "{0}" 的存續性時發生錯誤。
#XMSG: error message during stop persistence of a view (backend error)
DELETE_PERSISTED_VIEW_ERROR=刪除存續性檢視 "{0}" 時發生錯誤。
#XMSG: Warning message for removing the persisted data
RemovePersistency_Confirm_Msg=您要刪除存續性資料，並切換至檢視 "{0}" 的虛擬存取嗎？
#XMSG: Warning message for delete the view persistence
DeletePersistency_Confirm_Msg=您要自監控清單移除檢視，並刪除檢視 "{0}" 的存續性資料嗎？
#XMSG: error message for reading data from backend
txtReadBackendError=自後端讀取資料時發生錯誤。
#XFLD: Label for No Data Error
NoDataError=錯誤
#XMSG: message for conflicting task
Task_Already_Running=檢視 "{0}" 的衝突工作細項執行中。

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Show All Views~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Text for show all views button
allViewsText=所有檢視 ({0})
#XBUT: Text for show scheduled views button
scheduledText=已排程 ({0})
#XBUT: Text for show persisted views button
persistedText=已保存 ({0})
#XBUT: Text for start analyzer button
startAnalyzer=開始檢視分析工具
#XFLD: Message if repository is unavailable
repositoryErrorMsg=儲藏庫無法使用，且特定功能已停用。

#XFLD: Data Access - Virtual
Virtual=虛擬
#XFLD: Data Access - Persisted
Persisted=已保存

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Non-Persisted View Selection Dialog~~~~~~~~~~~~~~~~~~~~~~~

#XTIT: Title for Non-Persisted View selection dialog
select_source=選擇要保存的檢視

#XTIT: Placeholder for Non-Persisted View selection dialog search
searchView=搜尋檢視
#XTIT: No data in the list of non-persisted view
No_Data=無資料
#XBUT: Button to select non-persisted view
ok=確定
#XBUT: Button to close the non-persisted views selection dialog
cancel=取消

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~View persistency log message key~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message for view persistency logs
START_STOP_PERSISTENCY=開始 "{1}" 的資料存續性工作細項執行。
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY=正在保存檢視「{1}」的資料...
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY)
START_VIEW_PERSISTENCY_PROCESS=開始程序以保存檢視 "{0}" 的資料。
#XFLD: Message for view persistency manual partition load
START_VIEW_PERSISTENCY_PROCESS_MANUAL_PARTITIONS=開始程序已保存所選分割 ID 為："{1}" 檢視 "{0}" 的資料。
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY=正在移除檢視 "{1}" 的存續性資料。
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY)
REMOVE_VIEW_PERSISTENCY=開始程序以移除檢視 "{0}" 的已保存資料。
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_SUCCESS=已保存檢視 "{1}" 的資料。
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_SUCCESS)
START_VIEW_PERSISTENCY_PROCESS_SUCCESS=已保存檢視 "{0}" 的資料。
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY_SUCCESS=檢視 "{1}" 的存續性資料已移除，且虛擬資料存取已還原。
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_SUCCESS)
REMOVE_VIEW_PERSISTENCY_SUCCESS=已完成程序以移除檢視 "{0}" 的已保存資料。
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_FAILED=無法保存檢視 "{1}" 的資料。
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_FAILED)
START_VIEW_PERSISTENCY_PROCESS_FAILED=無法保存檢視 "{0}" 的資料。
#XFLD: Message for view persistency logs (for messages created before version 2022.05)
STOP_VIEW_PERSISTENCY_FAILED=無法移除檢視 "{1}" 的存續性資料。
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_FAILED)
REMOVE_VIEW_PERSISTENCY_FAILED=無法移除檢視 "{0}" 的存續性資料。
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_SUCCESS=已保存檢視 "{1}" 的 "{3}" 個記錄。
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace START_PERSISTENCY_RECORD_COUNT_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=已將檢視 "{1}" 的 {0} 個記錄插入至資料存續性表格。
#XFLD: Message used by SEAL repository for view persistency logs (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS_V2=已將檢視 "{1}" 的 {0} 個記錄插入至資料存續性表格。已使用記憶體：{2} GiB。
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_SUCCESS=已移除檢視 "{1}" 的 "{3}" 個存續性記錄。
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace STOP_PERSISTENCY_RECORD_COUNT_SUCCESS)
REMOVE_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=已移除存續性資料，已刪除 "{0}" 個存續性記錄。
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_FAILED=檢視 "{1}" 的 recordCount 取得失敗。
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_FAILED=檢視 "{1}" 的 recordCount 取得失敗。
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=已刪除 "{1}" 的排程。
#XFLD: Message for view persistency logs (new message to replace STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS)
REMOVE_VIEW_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=已刪除檢視 "{0}" 的排程。
#XFLD: Message for view persistency logs
STOP_PERSISTENCY_DELETE_SCHEDULE_FAILED="{1}" 的排程刪除失敗。
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_REDEPLOY=由於檢視 "{0}" 已於您開始保存後更改且部署，因此無法保存。請重試保存檢視或稍後下一次排程執行。
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_DELETE=由於檢視 "{0}" 已於您開始保存後刪除，因此無法保存。
#XFLD: Message for successful data persistency of a single partition (for messages created before version 2022.03)
START_PERSISTENCY_SINGLE_PARTITION_SUCCESS=已將 {0} 個記錄保存至值 "{1}" <= {2} < "{3}" 的分割。
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (new message to replace START_PERSISTENCY_SINGLE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS=已將 {0} 個記錄插入至值 "{1}" <= "{2}" < "{3}" 的分割。
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS_V2=已將 {0} 個記錄插入至值 "{1}" <= "{2}" < "{3}" 的分割。使用的記憶體：{4} GiB。
#XFLD: Message for successful replication of a "others" partition (for messages created before version 2022.03)
START_PERSISTENCY_OTHERS_PARTITION_SUCCESS=已將 {0} 個記錄保存至「其他」分割。
#XFLD: Message used by SEAL repository for successful replication of a "others" partition (new message to replace START_PERSISTENCY_OTHERS_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_OTHERS_PARTITION_SUCCESS=已將 {0} 個記錄插入至「其他」分割。
#XFLD: Message for view persistency record count of partitions (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS=已將檢視 "{1}" 的 {3} 個記錄保存於 {4} 個分割。
#XFLD: Message used by SEAL repository for view persistency record count of partitions (new message to replace START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS=已將檢視 "{1}" 的 {0} 個記錄在 {2} 個分割中插入至資料存續性表格。
#XFLD: Message used by SEAL repository for view persistency for partition wise refresh
START_VIEW_PERSISTENCY_RECORD_COUNT_REFRESHED_PARTITIONS_SUCCESS=已將檢視 "{1}" 的 {0} 個記錄插入至資料存續性表格。更新的分割：{2}；鎖住的分割：{3}；總計分割：{4}
#XFLD: Message used by SEAL repository for view persistency for manual partition load
START_VIEW_PERSISTENCY_RECORD_COUNT_MANUAL_PARTITIONS_SUCCESS=已在 {2} 個所選分割中，將 {0} 個記錄插入至檢視 "{1}" 的資料存續性表格。
#XFLD: Message used by SEAL repository for view persistency for locked partition data load
START_VIEW_PERSISTENCY_RECORD_COUNT_LOCKED_PARTITIONS_SUCCESS=已將檢視 "{1}" 的 {0} 個記錄插入至資料存續性表格。更新的分割：{2}；鎖住、未更改的分割：{3}；總計分割：{4}
#XMSG: Message for unexpected error (for messages created before version 2022.03)
START_PERSISTENCY_UNEXPECTED_ERROR=保存檢視 "{0}" 的資料時發生未預期錯誤。
#XMSG: Message for unexpected error (new message to replace START_PERSISTENCY_UNEXPECTED_ERROR)
START_VIEW_PERSISTENCY_UNEXPECTED_ERROR=保存檢視 "{0}" 的資料時發生未預期錯誤。
#XMSG: Message for Locked space error
START_VIEW_PERSISTENCY_LOCKED_SPACE_ERROR=由於空間 "{1}" 已鎖住，因此無法保存檢視 "{0}"。
#XMSG: Message for unexpected error (for messages created before version 2022.03)
STOP_PERSISTENCY_UNEXPECTED_ERROR=移除檢視 "{0}" 的存續性資料時發生未預期錯誤。
#XMSG: Message for unexpected error (new message to replace STOP_PERSISTENCY_UNEXPECTED_ERROR)
REMOVE_VIEW_PERSISTENCY_UNEXPECTED_ERROR=移除檢視 "{0}" 的存續性時發生未預期錯誤。
#XMSG: Message for ivalidated view
VIEW_PERSISTENCY_ABORTED_DEFINITION_INVALID=檢視 "{0}" 定義已無效，可能因為檢視直接或間接使用的物件發生更改。請嘗試部署檢視以解決問題或識別根本原因。
#XMSG: Message for persisted data removal (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT=部署檢視 "{0}" 時已移除存續性資料。
#XMSG: Message for persisted data removal of a consuming view
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW=部署使用檢視 "{0}" 時已移除存續性資料。
#XMSG: Message for persisted data removal of a consuming view due to a dac change
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW_DAC_CHANGED=由於存續性資料的資料存取控制已更改，因此部署使用檢視 "{0}" 時已移除該存續性資料。
#XMSG: Message for persisted data removal (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DEPLOYMENT=部署檢視 "{0}" 時已移除存續性資料。
#XMSG: Message for deleted view (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION=已移除存續性並刪除檢視 "{0}"。
#XMSG: Message for deleted view (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DELETION=已移除存續性並刪除檢視 "{0}"。
#XMSG Message for removal of persisted data, because prerequisites not fulfilled
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_PREREQUISITES_NOT_FULFILLED=已移除保存的資料，因為不再滿足資料存續性的先決條件。
#XMSG: Message for inconsistent persistency
VIEW_PERSISTENCY_INCONSISTENT_PERSISTENCY=檢視 "{0}" 存續性不一致。請移除保存的資料以解決問題。
#XMSG: Message for prerequisites
START_PERSISTENCY_CHECK_PREREQUISITES=正在檢查保存檢視 "{0}" 的先決條件。
#XMSG: Message for DAC deployed in deprecated way using table function and join (for messages created before 2025.05)
START_PERSISTENCY_DEPRECIATED_DAC=已使用即將淘汰的資料存取控制 (DAC) 來部署檢視 "{0}"。請再次部署檢視以改善效能。
#XMSG: Message for DAC deployed in deprecated way using table function and join
START_PERSISTENCY_DEPRECATED_DAC=已使用即將淘汰的資料存取控制 (DAC) 來部署檢視 "{0}"。請再次部署檢視以改善效能。
#XMSG: Message for roll back (for messages created before version 2022.03)
ROLLBACK_VIEW_PERSISTENCY=發生錯誤。已還原檢視 "{0}" 存續性的先前狀態。
#XMSG: Message used by SEAL repository for rollback of running procedure to persist view (new message to replace ROLLBACK_VIEW_PERSISTENCY)
VIEW_PERSISTENCY_ROLLBACK=發生錯誤。已停止檢視「{0}」的保存程序，且已復原更改內容。
#XMSG: Message used by SEAL repository for rollback of running procedure to remove persisted data of view
REMOVE_VIEW_PERSISTENCY_ROLLBACK=發生錯誤。已停止移除檢視 "{0}" 已保存資料的程序，且已復原更改內容。
#XMSG: Message used by SEAL repository for persistency preparation
START_PERSISTENCY_PREPARATION=正在準備保存資料。
#XMSG: Message used by SEAL repository for data insertion
START_INSERTING_PERSISTENCY_DATA=正在存續性表格中插入資料。
#XMSG: Message for null values - partitioning (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS=已將 {0} 個 Null 值記錄插入至「其他」分割。
#XMSG: Message used by SEAL repository for others partition values - partitioning (new message to replace START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS=已將 {0} 個記錄插入至值 "{2}" < "{1}" OR "{2}" >= "{3}" 的「其他」分割。
#XMSG: Message used by SEAL repository for others partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS_V2=已將 {0} 個記錄插入至值 "{2}" < "{1}" OR "{2}" >= "{3}" 的「其他」分割。使用的記憶體：{4} GiB。
#XMSG: Message used by SEAL repository for null partition values - partitioning
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS=已將 {0} 個記錄插入至值 "{1}" IS NULL 的「其他」分割。
#XMSG: Message used by SEAL repository for null partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS_V2=已將 {0} 個記錄插入至值 "{1}" IS NULL 的「其他」分割。使用的記憶體：{2} GiB。
##XFLD:Message used by SEAL repository for remote statistics
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS=正在載入相關資料：{0} 個遠端陳述式。取得的總計記錄：{1}。總計持續期：{2} 秒。
##XFLD:Message used by SEAL repository for remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS_WITH_PARTITIONS=正在使用含 {1} 個遠端陳述式的 {0} 個分割載入相關資料。取得的總計記錄：{2}。總計持續期：{3} 秒。
##XFLD:Message used by SEAL repository with UI ID for instructions of remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_INSTRUCTION=可透過在分割特定訊息明細中開啟遠端查詢監控器，顯示執行期間處理的遠端陳述式。
##XFLD:Message for start reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS=部署後正在開始重複使用檢視 {0} 現有保存資料的程序。
##XFLD:Message used by SEAL repository for reattach view persitency
START_VIEW_PERSISTENCY_REATTACHMENT=開始重複使用現有保存資料。
##XFLD:Message used by SEAL repository for reattach view persitency
START_FINILIZING_VIEW_PERSISTENCY_REATTACHMENT=重複使用現有保存資料。
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_COMPLETE=已完成重複使用檢視 {0} 的現有保存資料程序。
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_FAILED=部署後無法重複使用檢視 {0} 的縣有保存資料。
#XMSG: Message used by SEAL repository for finalizing persistency
START_FINALIZING_PERSISTENCY=正在完成存續性。
#XMSG: Message used by SEAL repository for restoring virtual access for view
REMOVE_VIEW_PERSISTENCY_VIRTUAL_ACCESS_RESTORED=已還原檢視 ''{0}'' 的虛擬資料存取。
#XMSG: Message used by SEAL repository for view that already has virtual access
REMOVE_VIEW_PERSISTENCY_ALREADY_VIRTUAL_ACCESS=檢視 "{0}" 已有虛擬資料存取。已保存的資料未移除。
#XMSG: Message for removing view persistency
VIEW_PERSISTENCY_REMOVED_FROM_MONITOR=已從檢視監控器移除檢視 ''{0}''。
#XMSG: Message for view doesnt exist.
VIEW_PERSISTENCY_VIEW_NOT_EXISTING=資料庫中沒有檢視 "{0}" 或未正確部署，因此無法保存。請嘗試重新部署檢視以解決問題，或識別根本原因。
#XMSG: Message for persistency not enabled.
VIEW_PERSISTENCY_NOT_ENABLED=未啟用資料存續性。請重新部署空間 "{0}" 中的表格/檢視，以啟用該功能。
#XMSG: Message for unexpected error due to node crash
VIEW_PERSISTENCY_UNEXPECTED_ERROR_GENERAL=上次檢視存續性執行作業已因技術錯誤而中斷。
#XFLD: Message for view persistency memory consumption
VIEW_PERSISTENCY_MEMORY_CONSUMPTION=檢視存續性執行時期中已使用 {0} GiB 的尖峰記憶體。
#XFLD: Message for view persistency timeout
VIEW_PERSISTENCY_TIMEOUT=檢視 {0} 存續性已達到 {1} 小時的逾時。
#XFLD: Message for not started asynchronous view persistency
VIEW_PERSISTENCY_ASYNC_NOT_STARTED=高系統負載會阻止開始檢視存續性的非同步執行。請檢查是否過多工作細項同時執行。
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency (for messages created before version 2022.05)
START_VIEW_PERSISTENCY_EXECUTION_METHOD_DROP_AND_RENAME=已刪除現有保存表格，並以新保存表格取代。
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_RECREATE_AND_DROP=已刪除現有保存表格，並以新保存表格取代。
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_TRUNCATE_AND_COPY=已使用新資料更新現有保存表格。
##XFLD: Message for missing authorizations for view persistency
VIEW_PERSISTENCY_MISSING_AUTH=缺少資料存續性的權限。
##XFLD: Message start of persistency cancellation
VIEW_PERSISTENCY_TRIGGER_CANCEL=正在開始取消保存檢視 {0} 的程序。
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_UNAVAILABLE_TASK_RUN=由於檢視 {0} 沒有執行中的資料存續性工作細項，因此無法取消保存該檢視的程序。
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_NONDETERMINISTIC_TASK_RUN=由於檢視 {0} 沒有執行中的資料存續性工作細項，因此無法取消保存該檢視的程序。
##XFLD: Message for canceling a non running persitency task
VIEW_PERSISTENCY_CANCEL_NON_RUNNING_SELECTED_TASK=由於所選資料存續性工作細項 {1} 未執行，因此無法取消保存檢視 {0} 的程序。
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_UNREACHABLE_PERSISTENCY_RUN=由於檢視 {0} 的資料存續性尚未開始，因此無法取消保存該檢視的程序。
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_COMPLETED_PERSISTENCY_RUN=由於檢視 {0} 已完成，因此無法取消保存該檢視的程序。
##XFLD: Message for failed cancellation
VIEW_PERSISTENCY_ABORTED_CANCEL_EXECUTION=無法取消保存檢視 {0} 的程序。
##XFLD: Message for finalizing persistency cancellation
VIEW_PERSISTENCY_FINALIZE_CANCEL=已提交停止檢視 {0} 的資料存續性程序。
##XFLD: Message used in persist task when cancellation is caught
VIEW_PERSISTENCY_CANCELED=已停止保存檢視 {0} 的程序。
##XFLD: Message used in persist task when cancelation is caught and performed through a cancelation task
VIEW_PERSISTENCY_CANCELED_VIA_CANCEL_TASK=已透過取消工作細項 {1} 停止保存檢視 {0} 的程序。
##XFLD: Message used in cancel task when the task is triggered by deployment
VIEW_PERSISTENCY_CANCEL_TRIGGERED_BY_DEPLOYMENT=正在取消部署檢視 {0} 時保存資料的程序。
##XFLD: Message used in case the cancelation is retriggered for a running task
VIEW_PERSISTENCY_CANCEL_ATTEMPT_RETRIGGER_CANCEL=先前用以保存檢視 {0} 的取消工作細項已提交。
##XFLD: Message used to express the delay that may occur before the persistency task is stopped
VIEW_PERSISTENCY_CANCEL_DELAY_STOPPING_PERSISTENCY=停止檢視 {0} 的資料存續性工作細項前可能有延遲。
##XFLD: Message used to reflect the task log identifier of the current running persistency task
VIEW_PERSISTENCY_CANCEL_CURRENT_RUNNING_TASK=正在使用工作細項 {1} 保存檢視 {0} 的資料。
##XFLD:Message used by SEAL repository for locked partitions and DACs
VIEW_PERSISTENCY_LOCKED_PARTITIONS_DACS=DAC 提供的權限可能已更改，且鎖住的分割不會考量。請解除鎖定該分割並載入新概要以套用更改內容。
##XFLD:Message used by SEAL repository for column structure change
VIEW_PERSISTENCY_COLUMN_STRUCTURE_CHANGE=欄結構已更改且不再符合現有存續性表格。請移除存續性資料，並開始新的資料存續性以更新存續性表格。
##XFLD: Message for out of memory
VIEW_PERSISTENCY_HANA_OUT_OF_MEMORY=由於 SAP HANA 資料庫發生記憶體不足錯誤，因此工作細項失敗。
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_EXCEPTION_TRANSACTION_ROLLBACK=由於 SAP HANA 資料庫內部異常，因此工作細項失敗。
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_SQL_EXECUTION_ISSUE=由於 SAP HANA 資料庫發生內部 SQL 異常問題，因此工作細項失敗。
##XFLD: Message for HANA out of memory result
VIEW_PERSISTENCY_OUT_OF_MEMORY_RESULT_MESSAGE=HANA 記憶體不足事件原因：{0}
##XFLD: Message for admission control rejection
VIEW_PERSISTENCY_HANA_ADMISSION_CTRL_REJECTION=由於 SAP HANA 許可控制拒絕，因此工作細項失敗。
##XFLD: Message for HANA back pressure / rate limit of HANA client
VIEW_PERSISTENCY_HANA_CLIENT_RATE_LIMIT_REJECTION=由於啟用中 SAP HANA 連線過多，因此工作細項失敗。
#XMSG: Message for invalid persisted table name
VIEW_PERSISTENCY_PERSISTED_TABLE_INVALID=發生錯誤，保存的表格已無效。若要解決問題，請移除保存的資料並再次保存檢視。
#XMSG: Message for usahe of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION=無法保存檢視，因為其使用根據已啟用使用者傳播遠端來源的遠端表格。請查看檢視歷程。
#XMSG: Message for dynamic usage of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION_DYNAMIC=無法保存檢視，因為其使用根據已啟用使用者傳播遠端來源的遠端表格。遠端表格可能透過 SQL 指令碼檢視動態使用。檢視歷程可能不會顯示遠端表格。
#XMSG: Message for insufficient privileges or consumption of view with DAC via dynamic SQL
VIEW_PERSISTENCY_INSUF_PRIV_OR_DAC_DYN_SQL=您的許可不足。請開啟「資料預覽」查看是否具備所需許可。若有，則透過動態 SQL 指令碼使用的第二檢視將套用資料存取控制 (DAC)。
#XMSG: deployment of DAC is deprecated and view need to be redeployed to be able to persist data
VIEW_PERSISTENCY_DEPRECATED_DAC=已使用即將淘汰的資料存取控制 (DAC) 來部署檢視 "{0}"。請再次部署檢視，才能保存檢視的資料。
##XFLD:Message used by SEAL repository for input parameter
START_VIEW_PERSISTENCY_INPUT_PARAMETER=將預設值 "{0}" 用於輸入參數 "{1}"。
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Elastic compute node keys~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
##XFLD:Message used to reflect disablement of ecn replica
ECN_DISABLE_EXISTING_REPLICA=已停用彈性計算節點複寫。
##XFLD:Message used to reflect recreation of ecn replica
ECN_RECREATE_EXISTING_REPLICA=已重新建立彈性計算節點複寫。
##XFLD:Message used to reflect re-enablement of ecn replica
ECN_ENABLE_EXISTING_REPLICA=已重新啟用彈性計算節點複寫。
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Task Schedule Menu key~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message to schedule task
scheduleText=排程
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=建立排程
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=編輯排程
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=刪除排程
#XFLD: Refresh frequency field
refreshFrequency=重新整理頻率
#XFLD: Refresh frequency field
refreshFrequencyNew=頻率
#XFLD: Refresh frequency field
refreshFrequencyNewNew=排程頻率
#XBUT: label for None
none=無
#XBUT: label for Real-Time replication state
realtime=即時
#XFLD: Label for table column
txtNextSchedule=下一次執行
#XFLD: Label for table column
txtNextScheduleNew=排程下一次執行
#XFLD: Label for table column
txtNumOfRecords=記錄數量
#XFLD: Label for scheduled link
scheduledTxt=已排程
#XFLD: LABEL for partially persisted link
partiallyPersisted=已部份保存
#XFLD: Text for paused text
paused=已暫停

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ View Monitor Tasklog Details Action keys~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message for release lock actionn
releaseLockMsg=若執行費時過久，這可能表示執行失敗，但狀態未同步更新。\r\n若要解決問題，您可釋放加鎖並將狀態設為失敗。
#XFLD: Label for release lock dialog
releaseLockText=釋放加鎖

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Column header tooltip ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: tooltip for table column
txtViewTooltip=存續性檢視名稱
#XFLD: tooltip for table column
txtViewDataAccessTooltip=這表示檢視可用度
#XFLD: tooltip for table column
txtViewRefreshFreqTooltip=這表示是否針對檢視定義排程
#XFLD: tooltip for table column
txtViewStatusTooltip=取得存續性檢視的狀態
#XFLD: tooltip for table column
txtViewLatestUpdateTooltip=提供存續性檢視何時最後更新的資訊。
#XFLD: tooltip for table column
txtViewNextRunTooltip=若已為檢視設定排程，查看下次執行何時排程。
#XFLD: tooltip for table column
txtViewNumOfRecordsTooltip=追蹤記錄數量。
#XFLD: tooltip for table column
txtViewInMemorySizeTooltip=追蹤檢視在您的記憶體中使用多少大小
#XFLD: tooltip for table column
txtViewDiskSizeTooltip=追蹤檢視在您的硬碟佔用多少大小
#XMSG: Expired text
txtExpired=已到期

#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=無法將物件 "{0}" 新增至工作細項鏈。

#~~~~~~~~~~~View Analyzer task log messages~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message indicating number of records for a view, and memory consumption in Mebibyte during simulation of view persistency
RECORDS_PEAK_MEMORY=檢視 "{0}" 有 {1} 個記錄。此檢視的資料存續性模擬已使用 {2} MiB 記憶體。
#XMSG: View Analyzer execution failure
VIEW_ANALYZER_EXECUTION_FAILED=檢視分析工具執行失敗。
#XMSG: Missing authorizations for View Analyzer
VIEW_ANALYZER_MISSING_AUTH=缺少檢視分析工具的權限。
#XMSG: Maximum memory in Gibibyte is reached, therefore persistency simulation is stopped
MAXIMUM_MEMORY_REACHED=由於模擬檢視 "{1}" 的資料存續性時已達到最大 {0} GiB 記憶體，因此將不會執行更多資料存續性模擬。
#XMSG: Error during persistency simulation occurred
PERSISTENCY_SIMULATION_ERROR=檢視 "{0}" 的資料存續性模擬期間發生錯誤。
#XMSG: Persistency simulation is not executed, because prerequisites are not fulfilled and the view cannot be persisted
PREREQUISITES_NOT_FULFILLED=由於未滿足先決條件且未保存檢視，因此未執行檢視 "{0}" 的資料存續性模擬。
#XMSG: Persistency simulation is not executed, because the view does not exist in the database
VIEW_DOES_NOT_EXIST=您必須部署檢視 "{0}"，才能啟用資料存續性模擬。
#XMSG: Number of records cannot be determined, because the local table does not exist in the database
TABLE_DOES_NOT_EXIST=資料庫中沒有本端表格 "{0}"，因此無法識別此表格的記錄數量。
##XMSG: Message start of cancellation for View Analyzer task
CANCEL_VIEW_ANALYZER_TRIGGERED=已提交檢視 "{1}" 的停止檢視分析工具工作細項 {0} 程序。工作細項停止前可能會有延遲。
##XMSG: Message start to indicate that task is not active
CANCEL_VIEW_ANALYZER_NO_ACTIVE_TASK=檢視 "{1}" 的檢視分析工具工作細項 {0} 未啟用。
#XMSG: Cancel View Analyzer task failure
CANCEL_VIEW_ANALYZER_FAILED=無法取消檢視分析工具工作細項。
##XMSG: Message used in View Analyzer execution task when task is canceled
VIEW_ANALYZER_CANCELED=檢視 "{0}" 的檢視分析工具執行已透過取消工作細項停止。
##XMSG: Message start of cancelation for Model Validation task
CANCEL_MODEL_VALIDATION_TRIGGERED=已提交檢視 "{1}" 的停止模型驗證工作細項 {0} 程序。工作細項停止前可能會有延遲。
##XMSG: Message to indicate that task is not active
CANCEL_MODEL_VALIDATION_NO_ACTIVE_TASK=檢視 "{1}" 的模型驗證工作細項 {0} 未啟用。
#XMSG: Cancel Model Validation task failure
CANCEL_MODEL_VALIDATION_FAILED=無法取消模型驗證工作細項。
##XMSG: Message used in Model Validation task when task is canceled
MODEL_VALIDATION_CANCELED=檢視 "{0}" 的模型驗證執行已透過取消工作細項停止。
#XMSG: Message for Locked space error, before starting Model Validation task
MODEL_VALIDATION_LOCKED_SPACE_ERROR=由於空間 "{1}" 已鎖住，因此無法執行檢視 "{0}" 的模型驗證。
##XMSG: Message used in View Analyzer execution task when determination of number of rows fails
NUMBER_ROWS_DETERMINATION_FAILED=決定本端表格 "{0}" 的列數時發生錯誤。
##XMSG: Message to indicate that SQL plan was created
EXECUTION_PLAN_CREATED=已建立且可下載檢視 "{0}" 的 SQL 分析工具計劃檔案。
#XFLD: Message to indicate starting of process to generate SQL Analyzer plan file
START_PLANVIZ_EXECUTION=開始流程以產生檢視 "{0}" 的 SQL 分析工具計劃檔案。
#XFLD: Message to indicate starting of View Analyzer process
VIEW_ANALYZER_EXECUTION_STARTED=開始檢視分析工具執行。
##XMSG: Message to indicate that SQL plan file cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_PLANVIZ_NOT_FULFILLED=由於未滿足資料存續性先決條件，因此無法產生檢視 "{0}" 的 SQL 分析工具計劃檔案。
##XMSG: Error message during  generation of SQL Analyzer plan file
PLANVIZ_ERROR=產生檢視 "{0}" SQL 分析工具計劃檔案期間發生錯誤。
#XMSG: Message for Locked space error
VIEW_ANALYZER_LOCKED_SPACE_ERROR=由於空間 "{1}" 已鎖住，因此無法執行檢視 "{0}" 的檢視分析工具。
#XMSG: Message for persistency simulation without partitioning
PERSISTENCY_SIMULATION_WITHOUT_PARTITIONS=模擬檢視存續性期間未考量檢視 "{0}" 分割。
#XMSG: Message for generation of SQL Analyzer plan file without considering partitioning
PLANVIZ_WITHOUT_PARTITIONS=產生 SQL 分析工具計劃檔案期間未考量檢視 "{0}" 分割。
#XMSG: Confirmation message for mass removal of persisted data
MassRemovePersistency_Confirm_Msg=您要移除保存的資料並將資料存取切換回虛擬存取嗎？
#XMSG: Confirmation message for partial mass removal of selected persisted data
PartialMassRemovePersistency_Confirm_Msg={0} 個所選檢視 (共 {1} 個) 已有保存的資料。\n您要移除保存的資料並將資料存取切換回虛擬存取嗎？
#XMSG: success message for stopping mass persistence
stopMassPersistenceSuccess=我們正移除所選檢視的保存資料。
#XMSG: error message during mass stop persistence of a view (backend error)
STOP_MASS_PERSISTENCE_ERROR=停止保存所選檢視時發生錯誤。
#XMSG: memory analysis is only performed in space {0}
NO_MEMORY_ANALYSIS=僅執行空間 "{0}" 中實體的記憶體分析："{1}" "{2}" 已跳過。
##XMSG: Message to indicate that Explain Plan cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_EXPLAINPLAN_NOT_FULFILLED=由於未滿足存續性先決條件，因此無法產生檢視 "{0}" 的 Explain Plan 檔案。
#XMSG: Message for Explain Plan generation without partitioning
EXPLAINPLAN_WITHOUT_PARTITIONS=產生 Explain Plan 檔案期間未考量檢視 "{0}" 分割。
#XFLD: Message to indicate starting of process to generate Explain Plan file
START_EXPLAINPLAN_EXECUTION=開始流程以產生檢視 "{0}" 的 Explain Plan 檔案。
##XMSG: Message to indicate that Explain Plan was created
EXPLAINPLAN_CREATED=檢視 "{0}" 的 Explain Plan 檔案已產生。您可按一下「檢視明細 」以顯示，或若具備相關權限則可下載。
##XMSG: Error message during  generation of Explain Plan file
EXPLAINPLAN_ERROR=產生檢視 "{0}" Explain Plan 檔案期間發生錯誤。
##XMSG: Explain Plan file cannot be generated for the view {0}, because too many views are stacked on each other
EXPLAINPLAN_MODEL_TOO_COMPLEX=無法產生檢視 "{0}" 的 Explain Plan 檔案。相互堆疊的檢視過多。複雜模型可能造成記憶體不足的錯誤，並降低效能。建議保存檢視。

##XMSG: Message to indicate that the space is locked
PERFORMANCE_ANALYSIS_LOCKED_SPACE_ERROR=由於空間 "{1}" 已鎖住，因此無法執行檢視 "{0}" 的效能分析。
##XMSG: The performance analysis task was cancelled
PERFORMANCE_ANALYSIS_CANCELED=已取消檢視 "{0}" 的效能分析。
##XMSG: The performance analysis failed
PERFORMANCE_ANALYSIS_FAILED=效能分析失敗。
##XMSG: The performance analysis is finished
PERFORMANCE_ANALYSIS_FINISHED=檢視 "{0}" 的效能分析已結束。按一下「檢視明細」以顯示結果。
##XMSG: The view has input parameter with no default value
INPUT_PARAMETER_WITHOUT_DEFAULT_VALUE=由於此檢視具有無預設值的參數，因此無法分析。
##XMSG: The view is not fully deployed
VIEW_NOT_FULLY_DEPLOYED=由於此檢視未完全部署，因此無法分析。
##XMSG: The view uses limited remote adapters
LIMITED_REMOTE_ADAPTER=此檢視使用至少一個具有限功能的遠端轉接器，例如缺少篩選下推或「計數」支援。保存或複製物件可改善執行時期效能。
##XMSG: The view uses remote adapter that does not support 'Limit'
REMOTE_ADAPTER_NOT_SUPPORTING_LIMIT=此檢視使用至少一個不支援「限制」的遠端轉接器。可能已選擇超過 1000 個記錄。
##XMSG: Performance analysis is executed by using default parameters of view'
INPUT_PARAMETER_WITH_DEFAULT_VALUE=正在使用檢視參數的預設值執行效能分析。
##XMSG: Message to indicate that an error occurred
PERFORMANCE_ANALYSIS_ERROR=檢視 "{0}" 效能分析期間發生錯誤。
##XMSG: Message start of cancellation for Performance Analysis task
CANCEL_PERFORMANCE_ANALYSIS_TRIGGERED=已提交檢視 "{1}" 的停止效能分析工作細項 {0} 程序。工作細項停止前可能會有延遲。
##XMSG: Message start to indicate that task is not active
CANCEL_PERFORMANCE_ANALYSIS_NO_ACTIVE_TASK=檢視 "{1}" 的效能分析工作細項 {0} 停用中。
#XMSG: Cancel Performance Analysis task failure
CANCEL_PERFORMANCE_ANALYSIS_FAILED=無法取消效能分析工作細項。

#XBUT: Assign schedule menu button label
assignScheduleLabel=將排程指派給我
#XBUT: Pause schedule menu label
pauseScheduleLabel=暫停排程
#XBUT: Resume schedule menu label
resumeScheduleLabel=繼續排程
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=移除排程時發生錯誤。
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=指派排程時發生錯誤。
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=暫停排程時發生錯誤。
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=繼續排程時發生錯誤。
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=正在刪除 {0} 個排程
#XMSG: Message for starting mass assign of schedules
massAssignStarted=正在更改 {0} 個排程的所有人
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=正在暫停 {0} 個排程
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=正在繼續 {0} 個排程
#XBUT: Select Columns Button
selectColumnsBtn=選擇欄
#XFLD: Refresh tooltip
TEXT_REFRESH=重新整理
#XFLD: Select Columns tooltip
text_selectColumns=選擇欄


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Runtime Metrics ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Label for Runtime Metrics
RuntimeMetricsTitle=執行時期公制
#XFLD : Label for Run Button
runButton=執行
#XFLD : Label for Cancel Button
cancelButton=取消
#XFLD : Label for Close Button
closeButton=關閉
#XFLD : Label for Open View Analyzer Button
openViewAnalyzerButton=開啟檢視分析工具
#XFLD : Label for Generate Explain Plan Button
generateExplainPlanButton=產生 Explain Plan
#XFLD : Label for Previous Run Column
previousRun=先前執行
#XFLD : Label for Latest Run Column
latestRun=上次執行
#XFLD : Label for time Column
time=時間
#XFLD : Label for Duration Column
duration=持續期
#XFLD : Label for Peak Memory Column
peakMemory=尖峰記憶體
#XFLD : Label for Number of Rows
numberOfRows=列數
#XFLD : Label for Overall Number of Sources
overallNumberOfSources=整體來源數量
#XFLD : Label for Data Access Column
dataAccess=資料存取
#XFLD : Label for Local Tables
localTables=本端表格
#XFLD : Label for federatedRemoteTables
federatedRemoteTables=聯合的遠端表格 (含有限轉接器功能)
#XTXT Text for initial state of the runtime metrics
initialState=您必須先執行效能分析才能取得公制。這可能需要一些時間，但您可視需要取消程序。
#XMSG: Cancel confirmation message
CancelRunConfirmationMessage=您確定要取消效能分析的目前執行嗎？
#XTIT: Cancel dialog title
CancelRunTitle=取消執行
#XFLD: Label for Number of Rows
NUMBER_ROWS=列數
#XFLD: Label for Overall Number of Sources
OVERALL_NUMBER_SOURCES=整體來源數量
#XFLD: Label for Data Access
DATA_ACCESS=資料存取
#XFLD: Label for Remote tables
NUMBER_REMOTE_TABLES_LIMITED_ADAPTERS=聯合的遠端表格 (含有限轉接器功能)
#XFLD: Label for select statement
SELECT_STATEMENT='SELECT * FROM VIEW LIMIT 1000'
#XFLD: Label for duration
SELECT_RUNTIME=持續期
#XFLD: Label for peak memory
SELECT_PEAK_MEMORY=尖峰記憶體
#XFLD: Label for count statement
SELECT_COUNT_STATEMENT='SELECT COUNT(*) FROM VIEW'
#XFLD: Label for count duration
SELECT_COUNT_RUNTIME=持續期
#XFLD: Label for count peak memory
SELECT_COUNT_PEAK_MEMORY=尖峰記憶體
#XFLD: Label for count of number of local tables
NUMBER_LOCAL_TABLES_FILE=本端表格 (檔案)
#XTXT: Text for running state of the runtime metrics
Running=執行中...
#XFLD: Label for time
Time=時間
#XFLD: Label for virtual access
PA_VIRTUAL=虛擬
#XFLD: Label for persisted access
PA_PERSISTED=已保存
PA_PARTIALLY_PERSISTED=已部份保存
#XTXT: Text for cancel
CancelRunSuccessMessage=正在取消效能分析的執行。
#XTXT: Text for cancel error
CancelRunErrorMessage=取消效能分析的執行時發生錯誤。
#XTXT: Text for explain plan generation
ExplainPlanStarted=正在產生檢視 "{0}" 的 Explain Plan。
#XTXT: Text for performance analysis
PerformanceAnalysisStarted=正在開始檢視 "{0}" 的效能分析。
#XTXT: Text for performance analysis error
ErrorFetchingPerformanceAnalysis=取得效能分析資料時發生錯誤。
#XTXT: Text for performance analysis error
conflictingTask=效能分析工作細項已在執行中。
#XFLD: Label for Errors
Errors=錯誤
#XFLD: Label for Warnings
Warnings=警告
#XFLD: Tooltip for missing privileges
openViewAnalyzerButtonTooltipNoPrivilege=您需要 DWC_DATAINTEGRATION (更新) 許可才能開啟檢視分析工具。
#XFLD: Tooltip for missing privileges
generateExplainPlanButtonTooltipNoPrivilege=您需要 DWC_RUNTIME (讀取) 許可才能產生 Explain Plan。



#XFLD: Label for frequency column
everyLabel=每
#XFLD: Plural Recurrence text for Hour
hoursLabel=小時
#XFLD: Plural Recurrence text for Day
daysLabel=天
#XFLD: Plural Recurrence text for Month
monthsLabel=個月
#XFLD: Plural Recurrence text for Minutes
minutesLabel=分鐘
