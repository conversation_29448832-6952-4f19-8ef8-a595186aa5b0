
#XBCB: Breadcrumb
viewMonitorBreadcrumb={spaceId}

#XFLD: Label for persisted view column
SOURCE=Sumber
#XFLD: Label for persisted view column
NAME=Nama
#XFLD: Label for persisted view column
NAME_LABEL=Nama Bisnis
#XFLD: Label for persisted view column
NAME_LABELNew=<PERSON><PERSON><PERSON><PERSON> (Nama Bisnis)
#XFLD: Label for persisted view column
TECHINCAL_NAME=Nama Teknis
#XFLD: Label for persisted view column
TECHINCAL_NAMENew=<PERSON><PERSON><PERSON><PERSON> (<PERSON>a <PERSON>)
#XFLD: Label for persisted view column
DATA_PERSISTENCY=Akses Data
#XFLD: Label for persisted view column
STATUS=Status
#XFLD: Label for persisted view column
LAST_UPDATED=Terakhir Diperbarui
#XFLD: Label for persisted view column
IN_MEMORY_SIZE=Memori yang Digunakan untuk Penyimpanan (MiB)
#XFLD: Label for persisted view column
DISK_SIZE=Disk yang Digunakan untuk Penyimpanan (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_NEWW=Ukuran dalam Memori (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_HEADER=Ukuran dalam Memori
#XFLD: Label for persisted view column
DISK_SIZE_NEWW=Ukuran pada Disk (MiB)
#XFLD: Label for persisted view column
DISK_SIZE_HEADER=Ukuran pada Disk
#XFLD: Label for schedule owner column
txtScheduleOwner=Pemilik Jadwal
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Menampilkan orang yang membuat jadwal
#XFLD: Label for persisted view column
PERSISTED=Dipersistenkan
#XFLD: Label for persisted view column
TYPE=Tipe
#XFLD: Label for View Selection Dialog column
changedOn=Diubah Pada
#XFLD: Label for View Selection Dialog column
createdBy=Dibuat Oleh
#XFLD: Label for log details column
txtViewPersistencyLogs=Tampilkan Log
#XFLD: Label for log details column
txtViewPersistencyLogsNew=Rincian
#XFLD: text for values shown for Ascending sort order
SortInAsc=Urutkan Naik
#XFLD: text for values shown for Descending sort order
SortInDesc=Urutkan Menurun
#XFLD: Label for persisted view monitor heading
ViewMonitor_Heading=Pemantauan Tampilan
#XFLD: Label for persisted view monitor sub-heading
ViewMonitor_SubHeading=Pantau dan Pertahankan Persistensi Data Tampilan


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ DynamicPageHeader ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: text for values shown in column Persistence Status
txtLoading=Memuat
#XFLD: text for values shown in column Persistence Status
txtRunning=Sedang Dieksekusi
#XFLD: text for values shown in column Persistence Status
txtAvailable=Tersedia
#XFLD: text for values shown in column Persistence Status
txtError=Kesalahan
#XMSG: Error message for unknown Persistence type that gets logged in Kibana
unknownReplicationTypeError=Tipe replikasi ''{0}'' tidak didukung.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Content ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Pengaturan yang digunakan untuk mengeksekusi persistensi data terakhir:
#XMSG: Message for input parameter name
inputParameterLabel=Parameter Input
#XMSG: Message for input parameter value
inputParameterValueLabel=Nilai
#XMSG: Message for persisted data
inputParameterPersistedLabel=Persisten Pada
#XTIT: Count for View in ViewMonitor Toolbar
txtViewCount=Tampilan ({0})
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Persistensi Tampilan
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Persistensi Data
#XBUT: Button to clear the selected view persistance
ClearViewPersistance=Hapus
#XBUT: Button to stop the selected view persistance
stopPersistance=Hentikan Persistensi
#XFLD: Placeholder for Search field
txtSearch=Cari
#XBUT: Tooltip for refresh button
txtRefresh=Segarkan
#XBUT: Tooltip for add view button
txtDeleteView=Hapus Permanen Persistensi
#XBUT: Tooltip for load new peristence
loadNewPersistence=Mulai Ulang Persistensi
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Muat Snapshot Baru
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Mulai Persistensi Data
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Hapus Data Yang Persisten
#XMSG: success message for starting persistence
startPersistenceSuccess=Kami mempertahankan tampilan ''{0}''.
#XMSG: success message for stopping persistence
stopPersistenceSuccess=Kami menghapus data yang persisten untuk tampilan ''{0}''.
#XMSG: success message for stopping persistence
deletePersistedViewSuccess=Kami menghapus tampilan ''{0}'' dari daftar pemantauan.
#XMSG: error message during start persistence of a view (backend error)
ERROR_STARTING_PERSISTENCE=Terjadi kesalahan saat memulai persistensi data untuk tampilan ''{0}''.
#XMSG: error message during start persistence of a view (backend error)
PARAMERROR_STARTING_PERSISTENCE=Tampilan ''{0}'' dengan parameter input tidak didukung untuk persistensi.
#XMSG: error message during start persistence of a view (backend error)
NUMBER_OF_PARAMETER_STARTING_PERSISTENCE=Tampilan "{0}" tidak dapat dibuat persisten karena memiliki lebih dari satu parameter input.
#XMSG: error message during start persistence of a view (backend error)
PARAMETER_DEFAULT_VALUE_STARTING_PERSISTENCE=Tampilan "{0}" tidak dapat dibuat persisten karena parameter input tidak memiliki nilai default.
#XMSG: error message during start persistence of a view (backend error)
REDEPLOYMENT_DAC_REQUIRED=Anda perlu menyebarkan kembali Kontrol Akses Data (Data Access Control - "DAC") "{0}" untuk mendukung persistensi data.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_STARTING_PERSISTENCE=Tampilan ''{0}'' tidak dapat dibuat persisten karena menggunakan tampilan ''{1}'', yang memiliki Kontrol Akses Data (DAC).
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_EXTERNAL_STARTING_PERSISTENCE=Tampilan ''{0}''’ tidak dapat dibuat persisten karena menggunakan tampilan Kontrol Akses Data (DAC) yang dimiliki ruang yang berbeda.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_STRUCTURE_STARTING_PERSISTENCE=Tampilan ''{0}'' tidak dapat dibuat persisten karena struktur dari satu atau beberapa Kontrol Akses Data (DAC) tidak mendukung persistensi data.
#XMSG: error message during stop persistence of a view (backend error)
STOP_PERSISTENCE_ERROR=Terjadi kesalahan saat menghentikan persistensi untuk tampilan ''{0}''.
#XMSG: error message during stop persistence of a view (backend error)
DELETE_PERSISTED_VIEW_ERROR=Terjadi kesalahan saat menghapus tampilan persisten ''{0}''.
#XMSG: Warning message for removing the persisted data
RemovePersistency_Confirm_Msg=Apakah Anda ingin menghapus permanen data yang persisten dan beralih ke akses virtual tampilan ''{0}''?
#XMSG: Warning message for delete the view persistence
DeletePersistency_Confirm_Msg=Apakah Anda ingin menghapus tampilan dari daftar pemantauan dan menghapus permanen data yang persisten dari tampilan ''{0}''?
#XMSG: error message for reading data from backend
txtReadBackendError=Tampaknya ada kesalahan saat membaca dari back end.
#XFLD: Label for No Data Error
NoDataError=Kesalahan
#XMSG: message for conflicting task
Task_Already_Running=Tugas yang bertentangan sudah dieksekusi untuk tampilan ''{0}''.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Show All Views~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Text for show all views button
allViewsText=Semua Tampilan ({0})
#XBUT: Text for show scheduled views button
scheduledText=Terjadwal ({0})
#XBUT: Text for show persisted views button
persistedText=Aktif ({0})
#XBUT: Text for start analyzer button
startAnalyzer=Mulai Penganalisis Tampilan
#XFLD: Message if repository is unavailable
repositoryErrorMsg=Repositori tidak tersedia dan fitur tertentu dinonaktifkan.

#XFLD: Data Access - Virtual
Virtual=Virtual
#XFLD: Data Access - Persisted
Persisted=Dipersistenkan

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Non-Persisted View Selection Dialog~~~~~~~~~~~~~~~~~~~~~~~

#XTIT: Title for Non-Persisted View selection dialog
select_source=Pilih Tampilan untuk Diaktifkan

#XTIT: Placeholder for Non-Persisted View selection dialog search
searchView=Cari Tampilan
#XTIT: No data in the list of non-persisted view
No_Data=Tidak Ada Data
#XBUT: Button to select non-persisted view
ok=Oke
#XBUT: Button to close the non-persisted views selection dialog
cancel=Batalkan

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~View persistency log message key~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message for view persistency logs
START_STOP_PERSISTENCY=Memulai eksekusi tugas persistensi data untuk ''{1}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY=Mempertahankan data untuk tampilan ''{1}''.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY)
START_VIEW_PERSISTENCY_PROCESS=Memulai proses untuk mempertahankan data untuk tampilan ''{0}''.
#XFLD: Message for view persistency manual partition load
START_VIEW_PERSISTENCY_PROCESS_MANUAL_PARTITIONS=Memulai proses untuk mempertahankan data pada tampilan ''{0}'' dengan ID partisi yang dipilih: ''{1}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY=Menghapus data yang persisten untuk tampilan ''{1}''.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY)
REMOVE_VIEW_PERSISTENCY=Memulai proses untuk menghapus data yang persisten pada tampilan “{0}”.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_SUCCESS=Data dibuat persisten untuk tampilan ''{1}''.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_SUCCESS)
START_VIEW_PERSISTENCY_PROCESS_SUCCESS=Data dibuat persisten untuk tampilan ''{0}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY_SUCCESS=Data yang persisten dihapus dan akses data virtual dipulihkan untuk tampilan ''{1}''.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_SUCCESS)
REMOVE_VIEW_PERSISTENCY_SUCCESS=Proses penghapusan data yang persisten pada tampilan “{0}” telah selesai.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_FAILED=Tidak dapat melakukan persistensi data untuk tampilan ''{1}''.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_FAILED)
START_VIEW_PERSISTENCY_PROCESS_FAILED=Tidak dapat melakukan persistensi data untuk tampilan ''{0}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.05)
STOP_VIEW_PERSISTENCY_FAILED=Tidak dapat menghapus data yang persisten untuk tampilan ''{1}''.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_FAILED)
REMOVE_VIEW_PERSISTENCY_FAILED=Tidak dapat menghapus data yang persisten untuk tampilan ''{0}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_SUCCESS="{3}" catatan dibuat persisten untuk tampilan "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace START_PERSISTENCY_RECORD_COUNT_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS={0} catatan yang disisipkan ke dalam tabel persistensi data untuk tampilan ''{1}''.
#XFLD: Message used by SEAL repository for view persistency logs (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS_V2={0} catatan yang disisipkan ke dalam tabel persistensi data untuk tampilan ''{1}''. Memori yang digunakan: {2} GiB.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_SUCCESS="{3}" catatan yang persisten dihapus untuk tampilan "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace STOP_PERSISTENCY_RECORD_COUNT_SUCCESS)
REMOVE_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=Data yang persisten dihapus, ''{0}'' catatan yang persisten dihapus permanen.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_FAILED=Gagal mendapatkan recordCount untuk tampilan "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_FAILED=Gagal mendapatkan recordCount untuk tampilan "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Jadwal dihapus permanen untuk "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS)
REMOVE_VIEW_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Jadwal dihapus permanen untuk tampilan ''{0}''.
#XFLD: Message for view persistency logs
STOP_PERSISTENCY_DELETE_SCHEDULE_FAILED=Jadwal gagal dihapus permanen untuk "{1}".
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_REDEPLOY=Kami tidak dapat membuat persistensi tampilan ''{0}''’ karena telah diubah dan disebarkan sejak Anda mulai mempersistensi tampilan tersebut. Coba lagi untuk mempersistensi tampilan atau tunggu hingga jadwal selanjutnya dieksekusi.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_DELETE=Kami tidak dapat mempersistensi tampilan ''{0}''’ karena telah dihapus permanen sejak Anda mulai mempersistensi tampilan tersebut.
#XFLD: Message for successful data persistency of a single partition (for messages created before version 2022.03)
START_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} catatan yang dipersistensi ke dalam partisi untuk nilai ''''{1}'' <= {2} < ''{3}''.
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (new message to replace START_PERSISTENCY_SINGLE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} catatan yang disisipkan ke dalam partisi untuk nilai ''{1}'' <= {2} < ''{3}''.
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS_V2={0} catatan yang disisipkan ke dalam partisi untuk nilai ''{1}'' <= ''{2}'' < ''{3}''. Memori yang digunakan: {4} GiB.
#XFLD: Message for successful replication of a "others" partition (for messages created before version 2022.03)
START_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} catatan yang dipersistensi ke dalam partisi ''lainnya''.
#XFLD: Message used by SEAL repository for successful replication of a "others" partition (new message to replace START_PERSISTENCY_OTHERS_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} catatan yang disisipkan ke dalam partisi "lainnya".
#XFLD: Message for view persistency record count of partitions (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={3} catatan yang dipersistensi untuk tampilan ''{1}'' dalam {4} partisi.
#XFLD: Message used by SEAL repository for view persistency record count of partitions (new message to replace START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={0} catatan yang disisipkan ke dalam tabel persistensi data untuk tampilan "{1}" dalam {2} partisi.
#XFLD: Message used by SEAL repository for view persistency for partition wise refresh
START_VIEW_PERSISTENCY_RECORD_COUNT_REFRESHED_PARTITIONS_SUCCESS={0} catatan yang disisipkan ke dalam tabel persistensi data untuk tampilan ''{1}''. Partisi yang diperbarui: {2}; Partisi yang terkunci: {3}; Total partisi: {4}
#XFLD: Message used by SEAL repository for view persistency for manual partition load
START_VIEW_PERSISTENCY_RECORD_COUNT_MANUAL_PARTITIONS_SUCCESS={0} catatan disisipkan ke dalam tabel persistensi data untuk tampilan ''{1}'' di {2} partisi yang dipilih
#XFLD: Message used by SEAL repository for view persistency for locked partition data load
START_VIEW_PERSISTENCY_RECORD_COUNT_LOCKED_PARTITIONS_SUCCESS={0} catatan yang disisipkan ke dalam tabel persistensi data untuk tampilan ''{1}''. Partisi yang diperbarui:{2}; Partisi terkunci yang tidak diubah:{3}; Total partisi: {4}
#XMSG: Message for unexpected error (for messages created before version 2022.03)
START_PERSISTENCY_UNEXPECTED_ERROR=Terjadi kesalahan yang tidak terduga ketika mempersistensi data untuk tampilan ''{0}''.
#XMSG: Message for unexpected error (new message to replace START_PERSISTENCY_UNEXPECTED_ERROR)
START_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Terjadi kesalahan yang tidak terduga ketika mempersistensi data untuk tampilan ''{0}''.
#XMSG: Message for Locked space error
START_VIEW_PERSISTENCY_LOCKED_SPACE_ERROR=Tampilan ''{0}'' tidak dapat dibuat persisten karena ruang ''{1}'' dikunci.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
STOP_PERSISTENCY_UNEXPECTED_ERROR=Terjadi kesalahan yang tidak terduga ketika menghapus data yang persisten untuk tampilan ''{0}".
#XMSG: Message for unexpected error (new message to replace STOP_PERSISTENCY_UNEXPECTED_ERROR)
REMOVE_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Terjadi kesalahan yang tidak terduga ketika menghapus persistensi untuk tampilan ''{0}''.
#XMSG: Message for ivalidated view
VIEW_PERSISTENCY_ABORTED_DEFINITION_INVALID=Definisi tampilan ''{0}'' menjadi tidak valid, sebagian besar disebabkan perubahan objek yang dipakai secara langsung atau tidak langsung oleh tampilan. Coba sebarkan kembali tampilan untuk menyelesaikan masalah, atau mengidentifikasi penyebab utama.
#XMSG: Message for persisted data removal (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT=Data yang persisten dihapus saat menyebarkan tampilan ''{0}''.
#XMSG: Message for persisted data removal of a consuming view
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW=Data yang persisten dihapus saat menyebarkan tampilan ''{0}'' yang dipakai.
#XMSG: Message for persisted data removal of a consuming view due to a dac change
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW_DAC_CHANGED=Data yang persisten dihapus saat menyebarkan tampilan ''{0}'' yang dipakai karena kontrol akses data telah berubah.
#XMSG: Message for persisted data removal (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DEPLOYMENT=Data yang persisten dihapus saat menyebarkan tampilan ''{0}''.
#XMSG: Message for deleted view (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION=Persistensi dihapus dengan penghapusan permanen tampilan ''{0}''.
#XMSG: Message for deleted view (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DELETION=Persistensi dihapus dengan penghapusan permanen tampilan ''{0}''.
#XMSG Message for removal of persisted data, because prerequisites not fulfilled
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_PREREQUISITES_NOT_FULFILLED=Data yang persisten akan dihapus karena prasyarat persistensi data tidak lagi terpenuhi.
#XMSG: Message for inconsistent persistency
VIEW_PERSISTENCY_INCONSISTENT_PERSISTENCY=Persistensi tampilan "{0}" menjadi tidak konsisten. Hapus data persisten untuk menyelesaikan masalah tersebut.
#XMSG: Message for prerequisites
START_PERSISTENCY_CHECK_PREREQUISITES=Memeriksa prasyarat untuk persistensi tampilan ''{0}''.
#XMSG: Message for DAC deployed in deprecated way using table function and join (for messages created before 2025.05)
START_PERSISTENCY_DEPRECIATED_DAC=Tampilan "{0}" disebarkan menggunakan Kontrol Akses Data (Data Access Control - "DAC") yang sudah tidak digunakan lagi. Silakan sebarkan ulang tampilan tersebut untuk meningkatkan kinerjanya.
#XMSG: Message for DAC deployed in deprecated way using table function and join
START_PERSISTENCY_DEPRECATED_DAC=Tampilan "{0}" disebarkan menggunakan Kontrol Akses Data (Data Access Control - "DAC") yang sudah tidak digunakan lagi. Silakan sebarkan ulang tampilan tersebut untuk meningkatkan kinerjanya.
#XMSG: Message for roll back (for messages created before version 2022.03)
ROLLBACK_VIEW_PERSISTENCY=Terjadi kesalahan. Status persistensi sebelumnya dipulihkan untuk tampilan ''{0}''.
#XMSG: Message used by SEAL repository for rollback of running procedure to persist view (new message to replace ROLLBACK_VIEW_PERSISTENCY)
VIEW_PERSISTENCY_ROLLBACK=Terjadi kesalahan. Proses untuk mempertahankan tampilan ''{0}'' telah dihentikan dan perubahan telah ditarik kembali.
#XMSG: Message used by SEAL repository for rollback of running procedure to remove persisted data of view
REMOVE_VIEW_PERSISTENCY_ROLLBACK=Terjadi kesalahan. Proses penghapusan data yang persisten pada tampilan “{0}” telah dihentikan dan perubahan dikembalikan ke kondisi sebelumnya.
#XMSG: Message used by SEAL repository for persistency preparation
START_PERSISTENCY_PREPARATION=Mempersiapkan untuk mempertahankan data.
#XMSG: Message used by SEAL repository for data insertion
START_INSERTING_PERSISTENCY_DATA=Memasukkan data dalam tabel persistensi.
#XMSG: Message for null values - partitioning (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS={0} catatan nilai kosong yang disisipkan ke dalam partisi ''lainnya''.
#XMSG: Message used by SEAL repository for others partition values - partitioning (new message to replace START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS={0} catatan yang disisipkan ke partisi ''lainnya'' untuk nilai ''{2}'' < ''{1}'' ATAU ''{2}'' >= ''{3}''.
#XMSG: Message used by SEAL repository for others partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS_V2={0} catatan yang disisipkan ke dalam partisi “lainnya” untuk nilai ''{2}'' < “{1}” ATAU ''{2}'' >= ''{3}''. Memori yang digunakan: {4} GiB.
#XMSG: Message used by SEAL repository for null partition values - partitioning
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS={0} catatan yang disisipkan ke dalam partisi ''lainnya'' untuk nilai ''{1}'' ADALAH KOSONG.
#XMSG: Message used by SEAL repository for null partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS_V2={0} catatan yang disisipkan ke dalam partisi ''lainnya'' untuk nilai ''{1}'' ADALAH KOSONG. Memori yang digunakan: {2} GiB.
##XFLD:Message used by SEAL repository for remote statistics
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS=Memuat data terkait: {0} pernyataan jarak jauh. Total catatan yang diambil: {1}. Total waktu durasi: {2} detik.
##XFLD:Message used by SEAL repository for remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS_WITH_PARTITIONS=Memuat data terkait menggunakan {0} partisi dengan {1} pernyataan jarak jauh. Total catatan yang diambil: {2}. Total waktu durasi: {3} detik.
##XFLD:Message used by SEAL repository with UI ID for instructions of remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_INSTRUCTION=Pernyataan jarak jauh yang diproses selama eksekusi dapat ditampilkan dengan membuka pemantau kueri jarak jauh, di rincian pesan khusus partisi.
##XFLD:Message for start reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS=Memulai proses untuk menggunakan kembali data persisten yang sudah ada untuk tampilan {0} setelah penyebaran.
##XFLD:Message used by SEAL repository for reattach view persitency
START_VIEW_PERSISTENCY_REATTACHMENT=Mulai untuk menggunakan kembali data persisten yang sudah ada.
##XFLD:Message used by SEAL repository for reattach view persitency
START_FINILIZING_VIEW_PERSISTENCY_REATTACHMENT=Menggunakan kembali data persisten yang sudah ada.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_COMPLETE=Proses untuk menggunakan kembali data persisten yang sudah ada telah selesai untuk tampilan {0}.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_FAILED=Gagal untuk menggunakan kembali data persisten yang sudah ada untuk tampilan {0} setelah penyebaran.
#XMSG: Message used by SEAL repository for finalizing persistency
START_FINALIZING_PERSISTENCY=Menyelesaikan persistensi.
#XMSG: Message used by SEAL repository for restoring virtual access for view
REMOVE_VIEW_PERSISTENCY_VIRTUAL_ACCESS_RESTORED=Akses data virtual dipulihkan untuk tampilan ''{0}''.
#XMSG: Message used by SEAL repository for view that already has virtual access
REMOVE_VIEW_PERSISTENCY_ALREADY_VIRTUAL_ACCESS=Tampilan “{0}” telah memiliki akses data virtual. Tidak ada data persisten yang dihapus.
#XMSG: Message for removing view persistency
VIEW_PERSISTENCY_REMOVED_FROM_MONITOR=Tampilan ''{0}'' telah dihapus dari monitor Tampilan.
#XMSG: Message for view doesnt exist.
VIEW_PERSISTENCY_VIEW_NOT_EXISTING=Tampilan "{0}" tidak ada di dalam basis data atau tidak disebarkan dengan benar, sehingga persistensi tampilan tidak dapat dilaksanakan. Cobalah sebarkan ulang tampilan untuk menyelesaikan masalah ini, atau untuk mengidentifikasi penyebab utamanya.
#XMSG: Message for persistency not enabled.
VIEW_PERSISTENCY_NOT_ENABLED=Persistensi data tidak diaktifkan. Sebar ulang tabel/tampilan dalam ruang ''{0}'' untuk mengaktifkan fungsionalitas.
#XMSG: Message for unexpected error due to node crash
VIEW_PERSISTENCY_UNEXPECTED_ERROR_GENERAL=Eksekusi persistensi tampilan terakhir terganggu karena kesalahan teknis.
#XFLD: Message for view persistency memory consumption
VIEW_PERSISTENCY_MEMORY_CONSUMPTION={0} GiB dari memori puncak yang digunakan dalam runtime persistensi tampilan.
#XFLD: Message for view persistency timeout
VIEW_PERSISTENCY_TIMEOUT=Persistensi tampilan {0} telah mencapai batas waktu {1} jam.
#XFLD: Message for not started asynchronous view persistency
VIEW_PERSISTENCY_ASYNC_NOT_STARTED=Beban sistem yang tinggi mencegah pelaksanaan asinkron pada persistensi tampilan dari awal dimulai. Periksa apakah terlalu banyak tugas yang dieksekusi secara paralel.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency (for messages created before version 2022.05)
START_VIEW_PERSISTENCY_EXECUTION_METHOD_DROP_AND_RENAME=Tabel dipersistensi yang ada telah dihapus permanen dan diganti dengan tabel dipersistensi yang baru.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_RECREATE_AND_DROP=Tabel dipersistensi yang ada telah dihapus permanen dan diganti dengan tabel dipersistensi yang baru.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_TRUNCATE_AND_COPY=Tabel dipersistensi yang ada telah diperbarui dengan data yang baru.
##XFLD: Message for missing authorizations for view persistency
VIEW_PERSISTENCY_MISSING_AUTH=Otorisasi tidak ditemukan untuk persistensi data.
##XFLD: Message start of persistency cancellation
VIEW_PERSISTENCY_TRIGGER_CANCEL=Memulai membatalkan proses untuk mempertahankan tampilan {0}.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_UNAVAILABLE_TASK_RUN=Gagal membatalkan proses untuk mempertahankan tampilan karena tidak ada tugas persistensi data yang dieksekusi untuk tampilan {0}.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_NONDETERMINISTIC_TASK_RUN=Gagal membatalkan proses untuk mempertahankan tampilan karena tidak ada tugas persistensi data yang dieksekusi untuk tampilan {0}.
##XFLD: Message for canceling a non running persitency task
VIEW_PERSISTENCY_CANCEL_NON_RUNNING_SELECTED_TASK=Gagal membatalkan proses untuk mempertahankan tampilan {0} karena tugas persistensi data yang dipilih {1} tidak dieksekusi.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_UNREACHABLE_PERSISTENCY_RUN=Gagal membatalkan proses untuk mempertahankan tampilan karena persistensi data untuk tampilan {0} belum dimulai.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_COMPLETED_PERSISTENCY_RUN=Gagal membatalkan proses untuk mempertahankan tampilan {0} karena proses telah diselesaikan.
##XFLD: Message for failed cancellation
VIEW_PERSISTENCY_ABORTED_CANCEL_EXECUTION=Gagal membatalkan proses untuk mempertahankan tampilan {0}.
##XFLD: Message for finalizing persistency cancellation
VIEW_PERSISTENCY_FINALIZE_CANCEL=Proses untuk menghentikan persistensi data tampilan {0} telah dikirimkan.
##XFLD: Message used in persist task when cancellation is caught
VIEW_PERSISTENCY_CANCELED=Proses untuk mempertahankan tampilan {0} dihentikan.
##XFLD: Message used in persist task when cancelation is caught and performed through a cancelation task
VIEW_PERSISTENCY_CANCELED_VIA_CANCEL_TASK=Proses untuk mempertahankan tampilan {0} dihentikan melalui tugas pembatalan {1}.
##XFLD: Message used in cancel task when the task is triggered by deployment
VIEW_PERSISTENCY_CANCEL_TRIGGERED_BY_DEPLOYMENT=Membatalkan proses untuk mempertahankan data saat menyebarkan tampilan {0}.
##XFLD: Message used in case the cancelation is retriggered for a running task
VIEW_PERSISTENCY_CANCEL_ATTEMPT_RETRIGGER_CANCEL=Tugas pembatalan sebelumnya untuk mempertahankan tampilan {0} telah dikirimkan.
##XFLD: Message used to express the delay that may occur before the persistency task is stopped
VIEW_PERSISTENCY_CANCEL_DELAY_STOPPING_PERSISTENCY=Mungkin terdapat penundaan hingga tugas persistensi data untuk tampilan {0} dihentikan.
##XFLD: Message used to reflect the task log identifier of the current running persistency task
VIEW_PERSISTENCY_CANCEL_CURRENT_RUNNING_TASK=Data untuk tampilan {0} sedang dipersistensi dengan tugas {1}.
##XFLD:Message used by SEAL repository for locked partitions and DACs
VIEW_PERSISTENCY_LOCKED_PARTITIONS_DACS=Otorisasi yang diberikan oleh DAC mungkin telah berubah dan tidak dipertimbangkan oleh partisi yang dikunci. Buka kunci partisi dan muat snapshot baru untuk menerapkan perubahan.
##XFLD:Message used by SEAL repository for column structure change
VIEW_PERSISTENCY_COLUMN_STRUCTURE_CHANGE=Struktur kolom telah berubah dan tidak lagi cocok dengan tabel persistensi yang ada. Hapus data yang persisten dan mulai persistensi data baru untuk memperbarui tabel persistensi Anda.
##XFLD: Message for out of memory
VIEW_PERSISTENCY_HANA_OUT_OF_MEMORY=Tugas gagal karena kesalahan kehabisan memori pada basis data SAP HANA.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_EXCEPTION_TRANSACTION_ROLLBACK=Tugas gagal karena terjadi pengecualian internal pada basis data SAP HANA.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_SQL_EXECUTION_ISSUE=Tugas gagal karena terjadi masalah pelaksanaan SQL internal pada basis data SAP HANA.
##XFLD: Message for HANA out of memory result
VIEW_PERSISTENCY_OUT_OF_MEMORY_RESULT_MESSAGE=Alasan peristiwa kehabisan memori di HANA: {0}
##XFLD: Message for admission control rejection
VIEW_PERSISTENCY_HANA_ADMISSION_CTRL_REJECTION=Tugas gagal karena adanya Penolakan Kontrol Penerimaan SAP HANA.
##XFLD: Message for HANA back pressure / rate limit of HANA client
VIEW_PERSISTENCY_HANA_CLIENT_RATE_LIMIT_REJECTION=Tugas gagal karena terlalu banyak koneksi SAP HANA yang aktif.
#XMSG: Message for invalid persisted table name
VIEW_PERSISTENCY_PERSISTED_TABLE_INVALID=Terjadi kesalahan dan tabel yang persisten menjadi tidak valid. Untuk mengatasi masalah ini, silakan hapus data yang persisten dan pertahankan kembali tampilan tersebut.
#XMSG: Message for usahe of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION=Tampilan tidak memenuhi syarat untuk persistensi. Tampilan ini menggunakan tabel jarak jauh yang berasal dari sumber jarak jauh dengan propagasi pengguna yang diaktifkan. Periksa silsilah tampilan tersebut.
#XMSG: Message for dynamic usage of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION_DYNAMIC=Tampilan tidak memenuhi syarat untuk persistensi. Tampilan ini menggunakan tabel jarak jauh yang berasal dari sumber jarak jauh dengan propagasi pengguna yang diaktifkan. Tabel jarak jauh tersebut dapat digunakan secara dinamis melalui tampilan skrip SQL. Silsilah tampilan mungkin tidak menampilkan tabel jarak jauh.
#XMSG: Message for insufficient privileges or consumption of view with DAC via dynamic SQL
VIEW_PERSISTENCY_INSUF_PRIV_OR_DAC_DYN_SQL=Hak akses Anda sepertinya tidak mencukupi. Buka Pratinjau Data untuk melihat apakah Anda memiliki hak akses yang diperlukan. Jika ya, kontrol akses data (DAC) mungkin akan diterapkan di tampilan kedua yang diakses melalui skrip SQL dinamis.
#XMSG: deployment of DAC is deprecated and view need to be redeployed to be able to persist data
VIEW_PERSISTENCY_DEPRECATED_DAC=Tampilan "{0}" disebarkan menggunakan Kontrol Akses Data (Data Access Control - "DAC") yang sudah tidak digunakan lagi. Silakan sebarkan ulang tampilan tersebut agar dapat menyimpan data secara persisten.
##XFLD:Message used by SEAL repository for input parameter
START_VIEW_PERSISTENCY_INPUT_PARAMETER=Menggunakan nilai default "{0}" untuk parameter input "{1}".
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Elastic compute node keys~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
##XFLD:Message used to reflect disablement of ecn replica
ECN_DISABLE_EXISTING_REPLICA=Replika node komputasi elastis dinonaktifkan.
##XFLD:Message used to reflect recreation of ecn replica
ECN_RECREATE_EXISTING_REPLICA=Replika node komputasi elastis dibuat ulang.
##XFLD:Message used to reflect re-enablement of ecn replica
ECN_ENABLE_EXISTING_REPLICA=Replika node komputasi elastis diaktifkan ulang.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Task Schedule Menu key~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message to schedule task
scheduleText=Jadwalkan
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Buat Jadwal
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Edit Jadwal
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Hapus Permanen Jadwal
#XFLD: Refresh frequency field
refreshFrequency=Frekuensi Penyegaran
#XFLD: Refresh frequency field
refreshFrequencyNew=Frekuensi
#XFLD: Refresh frequency field
refreshFrequencyNewNew=Frekuensi yang Dijadwalkan
#XBUT: label for None
none=Tidak Ada
#XBUT: label for Real-Time replication state
realtime=Waktu Nyata
#XFLD: Label for table column
txtNextSchedule=Eksekusi Selanjutnya
#XFLD: Label for table column
txtNextScheduleNew=Eksekusi Selanjutnya yang Dijadwalkan
#XFLD: Label for table column
txtNumOfRecords=Jumlah Catatan
#XFLD: Label for scheduled link
scheduledTxt=Dijadwalkan
#XFLD: LABEL for partially persisted link
partiallyPersisted=Persisten Sebagian
#XFLD: Text for paused text
paused=Dijeda

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ View Monitor Tasklog Details Action keys~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message for release lock actionn
releaseLockMsg=Jika eksekusi berjalan lebih lama dari biasanya, ini mungkin mengindikasikan bahwa eksekusi tersebut telah gagal dan statusnya belum diperbarui. \r\n Untuk menyelesaikan masalah tersebut, Anda dapat merilis kunci dan mengatur statusnya menjadi gagal.
#XFLD: Label for release lock dialog
releaseLockText=Rilis Kunci

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Column header tooltip ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: tooltip for table column
txtViewTooltip=Nama tampilan yang persisten
#XFLD: tooltip for table column
txtViewDataAccessTooltip=Ini mengindikasikan ketersediaan tampilan
#XFLD: tooltip for table column
txtViewRefreshFreqTooltip=Ini mengindikasikan apakah jadwal ditentukan untuk tampilan
#XFLD: tooltip for table column
txtViewStatusTooltip=Dapatkan status dari tampilan yang persisten
#XFLD: tooltip for table column
txtViewLatestUpdateTooltip=Memberikan informasi mengenai kapan tampilan yang persisten terakhir diperbarui
#XFLD: tooltip for table column
txtViewNextRunTooltip=Jika jadwal ditetapkan untuk tampilan, tampilkan kapan eksekusi selanjutnya dijadwalkan.
#XFLD: tooltip for table column
txtViewNumOfRecordsTooltip=Lacak jumlah catatan.
#XFLD: tooltip for table column
txtViewInMemorySizeTooltip=Lacak seberapa besar ukuran yang digunakan tampilan dalam memori Anda
#XFLD: tooltip for table column
txtViewDiskSizeTooltip=Lacak seberapa besar ukuran yang digunakan tampilan pada disk Anda
#XMSG: Expired text
txtExpired=Kedaluwarsa

#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Objek ''{0}'' tidak dapat ditambahkan ke rantai tugas.

#~~~~~~~~~~~View Analyzer task log messages~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message indicating number of records for a view, and memory consumption in Mebibyte during simulation of view persistency
RECORDS_PEAK_MEMORY=Tampilan ''{0}'' memiliki {1} catatan. Simulasi persistensi data untuk tampilan ini menggunakan {2} MiB memori.
#XMSG: View Analyzer execution failure
VIEW_ANALYZER_EXECUTION_FAILED=Penganalisis Tampilan gagal dilaksanakan.
#XMSG: Missing authorizations for View Analyzer
VIEW_ANALYZER_MISSING_AUTH=Otorisasi tidak ditemukan untuk Penganalisis Tampilan.
#XMSG: Maximum memory in Gibibyte is reached, therefore persistency simulation is stopped
MAXIMUM_MEMORY_REACHED=Memori maksimum sebesar {0} GiB telah tercapai saat menyimulasikan persistensi data untuk tampilan ''{1}''. Anda akan melihat simulasi persistensi data selanjutnya tidak akan dieksekusi.
#XMSG: Error during persistency simulation occurred
PERSISTENCY_SIMULATION_ERROR=Terjadi kesalahan selama simulasi persistensi data untuk tampilan ''{0}''.
#XMSG: Persistency simulation is not executed, because prerequisites are not fulfilled and the view cannot be persisted
PREREQUISITES_NOT_FULFILLED=Simulasi persistensi data tidak dijalankan untuk tampilan ''{0}'', karena prasyarat tidak terpenuhi dan tampilan tidak dapat dipersistensi.
#XMSG: Persistency simulation is not executed, because the view does not exist in the database
VIEW_DOES_NOT_EXIST=Anda harus menyebarkan tampilan "{0}" untuk mengaktifkan simulasi persistensi data.
#XMSG: Number of records cannot be determined, because the local table does not exist in the database
TABLE_DOES_NOT_EXIST=Tabel lokal "{0}" tidak ada di basis data, sehingga jumlah catatan tidak dapat ditentukan untuk tabel ini.
##XMSG: Message start of cancellation for View Analyzer task
CANCEL_VIEW_ANALYZER_TRIGGERED=Proses untuk menghentikan tugas Penganalisis Tampilan {0} untuk tampilan ''{1}'' telah dikirimkan. Mungkin terdapat penundaan hingga tugas dihentikan.
##XMSG: Message start to indicate that task is not active
CANCEL_VIEW_ANALYZER_NO_ACTIVE_TASK=Tugas Penganalisis Tampilan {0} untuk tampilan ''{1}'' tidak aktif.
#XMSG: Cancel View Analyzer task failure
CANCEL_VIEW_ANALYZER_FAILED=Gagal membatalkan tugas Penganalisis Tampilan.
##XMSG: Message used in View Analyzer execution task when task is canceled
VIEW_ANALYZER_CANCELED=Pelaksanaan Penganalisis Tampilan untuk "{0}" dihentikan melalui tugas pembatalan.
##XMSG: Message start of cancelation for Model Validation task
CANCEL_MODEL_VALIDATION_TRIGGERED=Proses untuk menghentikan tugas Validasi Model {0} untuk tampilan "{1}" telah dikirimkan. Mungkin terdapat penundaan hingga tugas dihentikan.
##XMSG: Message to indicate that task is not active
CANCEL_MODEL_VALIDATION_NO_ACTIVE_TASK=Tugas Validasi Model {0} untuk tampilan "{1}" tidak aktif.
#XMSG: Cancel Model Validation task failure
CANCEL_MODEL_VALIDATION_FAILED=Gagal untuk membatalkan tugas Validasi Model.
##XMSG: Message used in Model Validation task when task is canceled
MODEL_VALIDATION_CANCELED=Pelaksanaan Validasi Model untuk tampilan "{0}" dihentikan melalui tugas pembatalan.
#XMSG: Message for Locked space error, before starting Model Validation task
MODEL_VALIDATION_LOCKED_SPACE_ERROR=Tidak dapat melaksanakan Validasi Model untuk tampilan "{0}", karena ruang "{1}" dikunci.
##XMSG: Message used in View Analyzer execution task when determination of number of rows fails
NUMBER_ROWS_DETERMINATION_FAILED=Terjadi kesalahan saat menentukan jumlah baris untuk tabel lokal ''{0}''.
##XMSG: Message to indicate that SQL plan was created
EXECUTION_PLAN_CREATED=File rencana Penganalisis SQL untuk tampilan ''{0}'' dibuat dan dapat diunduh.
#XFLD: Message to indicate starting of process to generate SQL Analyzer plan file
START_PLANVIZ_EXECUTION=Memulai proses untuk membuat file rencana Penganalisis SQL untuk tampilan ''{0}''.
#XFLD: Message to indicate starting of View Analyzer process
VIEW_ANALYZER_EXECUTION_STARTED=Memulai pelaksanaan Penganalisis Tampilan.
##XMSG: Message to indicate that SQL plan file cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_PLANVIZ_NOT_FULFILLED=File rencana Penganalisis SQL tidak dapat dibuat untuk tampilan ''{0}'', karena prasyarat persistensi tidak terpenuhi.`
##XMSG: Error message during  generation of SQL Analyzer plan file
PLANVIZ_ERROR=Terjadi kesalahan selama pembuatan file rencana Penganalisis SQL untuk tampilan ''{0}''.
#XMSG: Message for Locked space error
VIEW_ANALYZER_LOCKED_SPACE_ERROR=Tidak dapat melaksanakan Penganalisis Tampilan untuk tampilan ''{0}'', karena ruang ''{1}'' terkunci.
#XMSG: Message for persistency simulation without partitioning
PERSISTENCY_SIMULATION_WITHOUT_PARTITIONS=Partisi tampilan ''{0}'' tidak dipertimbangkan selama simulasi persistensi data.
#XMSG: Message for generation of SQL Analyzer plan file without considering partitioning
PLANVIZ_WITHOUT_PARTITIONS=Partisi tampilan ''{0}'' tidak dipertimbangkan selama pembuatan file rencana Penganalisis SQL.
#XMSG: Confirmation message for mass removal of persisted data
MassRemovePersistency_Confirm_Msg=Apakah Anda ingin menghapus data persisten dan mengalihkan akses data kembali ke akses virtual?
#XMSG: Confirmation message for partial mass removal of selected persisted data
PartialMassRemovePersistency_Confirm_Msg={0} dari {1} tampilan terpilih telah memiliki data persisten. \n Apakah Anda ingin menghapus data persisten dan mengalihkan akses data kembali ke akses virtual?
#XMSG: success message for stopping mass persistence
stopMassPersistenceSuccess=Kami menghapus data persisten untuk tampilan terpilih.
#XMSG: error message during mass stop persistence of a view (backend error)
STOP_MASS_PERSISTENCE_ERROR=Terjadi kesalahan saat menghentikan persistensi untuk tampilan terpilih.
#XMSG: memory analysis is only performed in space {0}
NO_MEMORY_ANALYSIS=Analisis memori dilakukan untuk entitas dalam ruang "{0}" saja: ruang "{1}" "{2}" dilewati.
##XMSG: Message to indicate that Explain Plan cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_EXPLAINPLAN_NOT_FULFILLED=File Explain Plan tidak dapat dibuat untuk tampilan "{0}", karena prasyarat persistensi tidak terpenuhi.
#XMSG: Message for Explain Plan generation without partitioning
EXPLAINPLAN_WITHOUT_PARTITIONS=Partisi tampilan "{0}" tidak dipertimbangkan selama pembuatan file Explain Plan.
#XFLD: Message to indicate starting of process to generate Explain Plan file
START_EXPLAINPLAN_EXECUTION=Memulai proses untuk membuat file Explain Plan untuk tampilan "{0}".
##XMSG: Message to indicate that Explain Plan was created
EXPLAINPLAN_CREATED=File Explain Plan untuk tampilan "{0}" telah dihasilkan. Anda dapat menampilkannya dengan mengeklik "Lihat Rincian", atau mengunduhnya jika Anda memiliki izin yang relevan.
##XMSG: Error message during  generation of Explain Plan file
EXPLAINPLAN_ERROR=Terjadi kesalahan selama pembuatan file Explain Plan untuk tampilan "{0}".
##XMSG: Explain Plan file cannot be generated for the view {0}, because too many views are stacked on each other
EXPLAINPLAN_MODEL_TOO_COMPLEX=File Explain Plan tidak dapat dihasilkan untuk tampilan "{0}". Terlalu banyak tampilan yang saling tumpang tindih. Model yang kompleks dapat menyebabkan kesalahan kehabisan memori dan memperlambat kinerja. Disarankan untuk mempersistensi tampilan tersebut.

##XMSG: Message to indicate that the space is locked
PERFORMANCE_ANALYSIS_LOCKED_SPACE_ERROR=Tidak dapat melaksanakan analisis kinerja untuk tampilan "{0}" karena ruang "{1}" terkunci.
##XMSG: The performance analysis task was cancelled
PERFORMANCE_ANALYSIS_CANCELED=Analisis kinerja untuk tampilan "{0}" telah dibatalkan.
##XMSG: The performance analysis failed
PERFORMANCE_ANALYSIS_FAILED=Analisis kinerja gagal.
##XMSG: The performance analysis is finished
PERFORMANCE_ANALYSIS_FINISHED=Analisis kinerja untuk tampilan "{0}" telah selesai. Tampilkan hasilnya dengan mengeklik "Tampilkan Rincian".
##XMSG: The view has input parameter with no default value
INPUT_PARAMETER_WITHOUT_DEFAULT_VALUE=Tampilan ini tidak memenuhi syarat untuk dianalisis karena memiliki parameter tanpa nilai default.
##XMSG: The view is not fully deployed
VIEW_NOT_FULLY_DEPLOYED=Tampilan ini tidak memenuhi syarat untuk dianalisis karena belum sepenuhnya disebarkan.
##XMSG: The view uses limited remote adapters
LIMITED_REMOTE_ADAPTER=Tampilan ini menggunakan setidaknya satu adaptor jarak jauh dengan keterbatasan, seperti tidak mendukung filter pushdown atau fungsi 'Count'. Menyimpan atau mereplikasi objek dapat meningkatkan kinerja runtime.
##XMSG: The view uses remote adapter that does not support 'Limit'
REMOTE_ADAPTER_NOT_SUPPORTING_LIMIT=Tampilan ini menggunakan setidaknya satu adaptor jarak jauh yang tidak mendukung 'Batas', sehingga kemungkinan lebih dari 1000 catatan telah dipilih.
##XMSG: Performance analysis is executed by using default parameters of view'
INPUT_PARAMETER_WITH_DEFAULT_VALUE=Analisis kinerja dijalankan menggunakan nilai default dari parameter tampilan.
##XMSG: Message to indicate that an error occurred
PERFORMANCE_ANALYSIS_ERROR=Terjadi kesalahan selama analisis kinerja untuk tampilan "{0}".
##XMSG: Message start of cancellation for Performance Analysis task
CANCEL_PERFORMANCE_ANALYSIS_TRIGGERED=Proses untuk menghentikan tugas Analisis Kinerja {0} untuk tampilan "{1}" telah dikirimkan. Mungkin terdapat penundaan hingga tugas dihentikan.
##XMSG: Message start to indicate that task is not active
CANCEL_PERFORMANCE_ANALYSIS_NO_ACTIVE_TASK=Tugas Analisis Kinerja {0} untuk tampilan "{1}" tidak aktif.
#XMSG: Cancel Performance Analysis task failure
CANCEL_PERFORMANCE_ANALYSIS_FAILED=Gagal membatalkan tugas Analisis Kinerja.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Tetapkan Jadwal untuk Saya
#XBUT: Pause schedule menu label
pauseScheduleLabel=Jeda Jadwal
#XBUT: Resume schedule menu label
resumeScheduleLabel=Lanjutkan Jadwal
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Terjadi kesalahan saat menghapus jadwal.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Terjadi kesalahan saat menetapkan jadwal.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Terjadi kesalahan saat menjeda jadwal.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Terjadi kesalahan saat melanjutkan jadwal.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Menghapus permanen {0} jadwal
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Mengubah pemilik {0} jadwal
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Menjeda {0} jadwal
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Melanjutkan {0} jadwal
#XBUT: Select Columns Button
selectColumnsBtn=Pilih Kolom
#XFLD: Refresh tooltip
TEXT_REFRESH=Segarkan
#XFLD: Select Columns tooltip
text_selectColumns=Pilih Kolom


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Runtime Metrics ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Label for Runtime Metrics
RuntimeMetricsTitle=Metrik Runtime untuk
#XFLD : Label for Run Button
runButton=Eksekusi
#XFLD : Label for Cancel Button
cancelButton=Batalkan
#XFLD : Label for Close Button
closeButton=Tutup
#XFLD : Label for Open View Analyzer Button
openViewAnalyzerButton=Buka Penganalisis Tampilan
#XFLD : Label for Generate Explain Plan Button
generateExplainPlanButton=Buat Explain Plan
#XFLD : Label for Previous Run Column
previousRun=Eksekusi Sebelumnya
#XFLD : Label for Latest Run Column
latestRun=Eksekusi Terbaru
#XFLD : Label for time Column
time=Waktu
#XFLD : Label for Duration Column
duration=Durasi
#XFLD : Label for Peak Memory Column
peakMemory=Memori Puncak
#XFLD : Label for Number of Rows
numberOfRows=Jumlah Baris
#XFLD : Label for Overall Number of Sources
overallNumberOfSources=Jumlah Keseluruhan Sumber
#XFLD : Label for Data Access Column
dataAccess=Akses Data
#XFLD : Label for Local Tables
localTables=Tabel Lokal
#XFLD : Label for federatedRemoteTables
federatedRemoteTables=Tabel Jarak Jauh Gabungan (dengan Kemampuan Adaptor Terbatas)
#XTXT Text for initial state of the runtime metrics
initialState=Anda harus mengeksekusi Analisis Kinerja terlebih dahulu untuk mendapatkan metrik. Proses ini mungkin memerlukan waktu, tetapi Anda dapat membatalkannya jika diperlukan.
#XMSG: Cancel confirmation message
CancelRunConfirmationMessage=Apakah Anda yakin ingin membatalkan eksekusi Analisis Kinerja saat ini?
#XTIT: Cancel dialog title
CancelRunTitle=Batalkan Eksekusi
#XFLD: Label for Number of Rows
NUMBER_ROWS=Jumlah Baris
#XFLD: Label for Overall Number of Sources
OVERALL_NUMBER_SOURCES=Jumlah Keseluruhan Sumber
#XFLD: Label for Data Access
DATA_ACCESS=Akses Data
#XFLD: Label for Remote tables
NUMBER_REMOTE_TABLES_LIMITED_ADAPTERS=Tabel Jarak Jauh Gabungan (dengan Kemampuan Adaptor Terbatas)
#XFLD: Label for select statement
SELECT_STATEMENT='SELECT * FROM VIEW LIMIT 1000'
#XFLD: Label for duration
SELECT_RUNTIME=Durasi
#XFLD: Label for peak memory
SELECT_PEAK_MEMORY=Memori Puncak
#XFLD: Label for count statement
SELECT_COUNT_STATEMENT='SELECT COUNT(*) FROM VIEW'
#XFLD: Label for count duration
SELECT_COUNT_RUNTIME=Durasi
#XFLD: Label for count peak memory
SELECT_COUNT_PEAK_MEMORY=Memori Puncak
#XFLD: Label for count of number of local tables
NUMBER_LOCAL_TABLES_FILE=Tabel Lokal (File)
#XTXT: Text for running state of the runtime metrics
Running=Sedang Dieksekusi...
#XFLD: Label for time
Time=Waktu
#XFLD: Label for virtual access
PA_VIRTUAL=Virtual
#XFLD: Label for persisted access
PA_PERSISTED=Dipersistenkan
PA_PARTIALLY_PERSISTED=Persisten Sebagian
#XTXT: Text for cancel
CancelRunSuccessMessage=Membatalkan eksekusi Analisis Kinerja.
#XTXT: Text for cancel error
CancelRunErrorMessage=Terjadi kesalahan saat membatalkan eksekusi Analisis Kinerja.
#XTXT: Text for explain plan generation
ExplainPlanStarted=Membuat Explain Plan untuk tampilan "{0}".
#XTXT: Text for performance analysis
PerformanceAnalysisStarted=Memulai Analisis Kinerja untuk tampilan "{0}".
#XTXT: Text for performance analysis error
ErrorFetchingPerformanceAnalysis=Terjadi kesalahan saat mengambil data Analisis Kinerja.
#XTXT: Text for performance analysis error
conflictingTask=Eksekusi tugas Analisis Kinerja sudah berjalan
#XFLD: Label for Errors
Errors=Kesalahan
#XFLD: Label for Warnings
Warnings=Peringatan
#XFLD: Tooltip for missing privileges
openViewAnalyzerButtonTooltipNoPrivilege=Anda memerlukan hak akses DWC_DATAINTEGRATION(update) untuk membuka Penganalisis Tampilan.
#XFLD: Tooltip for missing privileges
generateExplainPlanButtonTooltipNoPrivilege=Anda memerlukan hak akses DWC_RUNTIME(read) untuk membuat Explain Plan.



#XFLD: Label for frequency column
everyLabel=Setiap
#XFLD: Plural Recurrence text for Hour
hoursLabel=Jam
#XFLD: Plural Recurrence text for Day
daysLabel=Hari
#XFLD: Plural Recurrence text for Month
monthsLabel=Bulan
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Menit
