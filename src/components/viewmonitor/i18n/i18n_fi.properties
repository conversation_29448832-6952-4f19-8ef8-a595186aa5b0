
#XBCB: Breadcrumb
viewMonitorBreadcrumb={spaceId}

#XFLD: Label for persisted view column
SOURCE=Lähde
#XFLD: Label for persisted view column
NAME=Nimi
#XFLD: Label for persisted view column
NAME_LABEL=Liiketoiminnallinen nimi
#XFLD: Label for persisted view column
NAME_LABELNew=Objekti (liiketoiminnallinen nimi)
#XFLD: Label for persisted view column
TECHINCAL_NAME=Tekninen nimi
#XFLD: Label for persisted view column
TECHINCAL_NAMENew=Obje<PERSON>i (tekninen nimi)
#XFLD: Label for persisted view column
DATA_PERSISTENCY=Tietojen käyttö
#XFLD: Label for persisted view column
STATUS=Tila
#XFLD: Label for persisted view column
LAST_UPDATED=Viimeinen päivitys
#XFLD: Label for persisted view column
IN_MEMORY_SIZE=Tallennukseen käytetty muisti (MiB)
#XFLD: Label for persisted view column
DISK_SIZE=Tallen<PERSON>ks<PERSON> käytetty levy (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_NEWW=In-memoryn koko (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_HEADER=In-memoryn koko
#XFLD: Label for persisted view column
DISK_SIZE_NEWW=Levyn koko (MiB)
#XFLD: Label for persisted view column
DISK_SIZE_HEADER=Levyn koko
#XFLD: Label for schedule owner column
txtScheduleOwner=Aikataulun omistaja
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Näyttää, kuka loi aikataulun
#XFLD: Label for persisted view column
PERSISTED=Pysyvä
#XFLD: Label for persisted view column
TYPE=Tyyppi
#XFLD: Label for View Selection Dialog column
changedOn=Muutospäivämäärä
#XFLD: Label for View Selection Dialog column
createdBy=Tekijä
#XFLD: Label for log details column
txtViewPersistencyLogs=Näytä lokit
#XFLD: Label for log details column
txtViewPersistencyLogsNew=Lisätiedot
#XFLD: text for values shown for Ascending sort order
SortInAsc=Lajittele nousevasti
#XFLD: text for values shown for Descending sort order
SortInDesc=Lajittele laskevasti
#XFLD: Label for persisted view monitor heading
ViewMonitor_Heading=Näkymien valvonta
#XFLD: Label for persisted view monitor sub-heading
ViewMonitor_SubHeading=Valvo ja ylläpidä näkymien tietojen pysyvyyttä


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ DynamicPageHeader ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: text for values shown in column Persistence Status
txtLoading=Ladataan
#XFLD: text for values shown in column Persistence Status
txtRunning=Käynnissä
#XFLD: text for values shown in column Persistence Status
txtAvailable=Käytettävissä
#XFLD: text for values shown in column Persistence Status
txtError=Virhe
#XMSG: Error message for unknown Persistence type that gets logged in Kibana
unknownReplicationTypeError=Replikointityyppiä "{0}" ei tueta.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Content ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Viimeisessä tietojen pysyvyysajossa käytetyt asetukset:
#XMSG: Message for input parameter name
inputParameterLabel=Syöttöparametri
#XMSG: Message for input parameter value
inputParameterValueLabel=Arvo
#XMSG: Message for persisted data
inputParameterPersistedLabel=Pysyväksi muuttamisaika
#XTIT: Count for View in ViewMonitor Toolbar
txtViewCount=Näkymät ({0})
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Näkymän pysyvyys
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Tietojen pysyvyys
#XBUT: Button to clear the selected view persistance
ClearViewPersistance=Tyhjennä
#XBUT: Button to stop the selected view persistance
stopPersistance=Pysäytä pysyvyys
#XFLD: Placeholder for Search field
txtSearch=Hae
#XBUT: Tooltip for refresh button
txtRefresh=Päivitä
#XBUT: Tooltip for add view button
txtDeleteView=Poista pysyvyys
#XBUT: Tooltip for load new peristence
loadNewPersistence=Käynnistä pysyvyys uudelleen
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Lataa uusi tilannevedos
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Aloita tietojen pysyvyys
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Poista pysyvät tiedot
#XMSG: success message for starting persistence
startPersistenceSuccess=Muutamme näkymää "{0}" pysyväksi.
#XMSG: success message for stopping persistence
stopPersistenceSuccess=Poistamme näkymän "{0}" pysyviä tietoja.
#XMSG: success message for stopping persistence
deletePersistedViewSuccess=Poistamme näkymän "{0}" valvontaluettelosta.
#XMSG: error message during start persistence of a view (backend error)
ERROR_STARTING_PERSISTENCE=Näkymän "{0}" tietojen pysyvyyden käynnistyksessä tapahtui virhe.
#XMSG: error message during start persistence of a view (backend error)
PARAMERROR_STARTING_PERSISTENCE=Näkymää "{0}" ei voi muuttaa pysyväksi, koska se sisältää syöttöparametreja.
#XMSG: error message during start persistence of a view (backend error)
NUMBER_OF_PARAMETER_STARTING_PERSISTENCE=Näkymää "{0}" ei voi muuttaa pysyväksi, koska sillä on enemmän kuin yksi syöttöparametria.
#XMSG: error message during start persistence of a view (backend error)
PARAMETER_DEFAULT_VALUE_STARTING_PERSISTENCE=Näkymää "{0}" ei voi muuttaa pysyväksi, koska syöttöparametrilla ei ole oletusarvoa.
#XMSG: error message during start persistence of a view (backend error)
REDEPLOYMENT_DAC_REQUIRED=Sinun täytyy ottaa uudelleen käyttöön tietojen käytön valvonta (DAC) "{0}" tietojen pysyvyyden tukemista varten.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_STARTING_PERSISTENCE=Näkymää "{0}" ei voi muuttaa pysyväksi, koska se käyttää näkymää "{1}", jossa käytetään tietojen haun ohjausta (DAC).
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_EXTERNAL_STARTING_PERSISTENCE=Näkymää "{0}" ei voi muuttaa pysyväksi, koska se käyttää näkymää eri tilaan kuuluvan tietojen haun ohjauksen (DAC) kanssa.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_STRUCTURE_STARTING_PERSISTENCE=Näkymää "{0}" ei voi muuttaa pysyväksi, koska yhden tai usean tietojen haun ohjauksen (DAC) rakenne ei tue tietojen pysyvyyttä.
#XMSG: error message during stop persistence of a view (backend error)
STOP_PERSISTENCE_ERROR=Näkymän "{0}" pysyvyyden pysäyttämisessä tapahtui virhe.
#XMSG: error message during stop persistence of a view (backend error)
DELETE_PERSISTED_VIEW_ERROR=Näkymän "{0}" pysyvyyden poistamisessa tapahtui virhe.
#XMSG: Warning message for removing the persisted data
RemovePersistency_Confirm_Msg=Haluatko poistaa pysyvät tiedot ja vaihtaa näkymän "{0}" virtuaaliseen käyttöön?
#XMSG: Warning message for delete the view persistence
DeletePersistency_Confirm_Msg=Haluatko poistaa näkymän valvontaluettelosta ja poistaa näkymän "{0}" pysyvyystiedot?
#XMSG: error message for reading data from backend
txtReadBackendError=Vaikuttaa siltä, että taustasta luettaessa tapahtui virhe.
#XFLD: Label for No Data Error
NoDataError=Virhe
#XMSG: message for conflicting task
Task_Already_Running=Ristiriitainen tehtävä on jo käynnissä näkymälle "{0}".

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Show All Views~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Text for show all views button
allViewsText=Kaikki näkymät ({0})
#XBUT: Text for show scheduled views button
scheduledText=Ajoitettu ({0})
#XBUT: Text for show persisted views button
persistedText=Pysyvä ({0})
#XBUT: Text for start analyzer button
startAnalyzer=Käynnistä näkymän analysoija
#XFLD: Message if repository is unavailable
repositoryErrorMsg=Tietohakemisto ei ole käytettävissä, ja tietyt ominaisuudet on poistettu käytöstä.

#XFLD: Data Access - Virtual
Virtual=Virtuaalinen
#XFLD: Data Access - Persisted
Persisted=Pysyvä

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Non-Persisted View Selection Dialog~~~~~~~~~~~~~~~~~~~~~~~

#XTIT: Title for Non-Persisted View selection dialog
select_source=Valitse näkymä pysyvyyttä varten

#XTIT: Placeholder for Non-Persisted View selection dialog search
searchView=Hae näkymät
#XTIT: No data in the list of non-persisted view
No_Data=Ei tietoja
#XBUT: Button to select non-persisted view
ok=OK
#XBUT: Button to close the non-persisted views selection dialog
cancel=Peruuta

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~View persistency log message key~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message for view persistency logs
START_STOP_PERSISTENCY=Käynnistetään kohteen "{1}" tietojen pysyvyystehtävän suoritus.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY=Näkymän "{1}" tiedot muutetaan pysyviksi.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY)
START_VIEW_PERSISTENCY_PROCESS=Käynnistetään prosessi näkymän "{0}" tietojen muuttamiseksi pysyviksi.
#XFLD: Message for view persistency manual partition load
START_VIEW_PERSISTENCY_PROCESS_MANUAL_PARTITIONS=Käynnistetään prosessi näkymän "{0}" tietojen muuttamiseksi pysyviksi valituilla osiotunnuksilla: "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY=Poistetaan näkymän "{1}" pysyviä tietoja.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY)
REMOVE_VIEW_PERSISTENCY=Käynnistetään prosessi näkymän "{0}" pysyviksi muutettujen tietojen poistoa varten.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_SUCCESS=Näkymän "{1}" tiedot muutettu pysyviksi.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_SUCCESS)
START_VIEW_PERSISTENCY_PROCESS_SUCCESS=Näkymän "{0}" tiedot muutettu pysyviksi.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY_SUCCESS=Näkymän "{1}" pysyvät tiedot poistettu ja virtuaalinen tietojen käyttö palautettu.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_SUCCESS)
REMOVE_VIEW_PERSISTENCY_SUCCESS=Näkymän "{0}" pysyviksi muutettujen tietojen poistoprosessi päätetty.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_FAILED=Näkymän "{1}" tietoja ei voi muuttaa pysyviksi.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_FAILED)
START_VIEW_PERSISTENCY_PROCESS_FAILED=Näkymän "{0}" tietoja ei voi muuttaa pysyviksi.
#XFLD: Message for view persistency logs (for messages created before version 2022.05)
STOP_VIEW_PERSISTENCY_FAILED=Näkymän "{1}" pysyviä tietoja ei voi poistaa.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_FAILED)
REMOVE_VIEW_PERSISTENCY_FAILED=Näkymän "{0}" pysyviä tietoja ei voi poistaa.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_SUCCESS=Näkymän "{1}" "{3}" tietuetta muutettu pysyviksi.
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace START_PERSISTENCY_RECORD_COUNT_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS={0} tietuetta lisätty tietojen pysyvyystauluun näkymälle "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS_V2={0} tietuetta lisätty tietojen pysyvyystauluun näkymälle "{1}". Käytetty muisti: {2} GiB.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_SUCCESS=Näkymän "{1}" "{3}" pysyvää tietuetta poistettu.
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace STOP_PERSISTENCY_RECORD_COUNT_SUCCESS)
REMOVE_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=Pysyvät tiedot poistettu, "{0}" pysyvää tietuetta poistettu.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_FAILED=Näkymän "{1}" tietueiden lukumäärän haku epäonnistui.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_FAILED=Näkymän "{1}" tietueiden lukumäärän haku epäonnistui.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Kohteen "{1}" aikataulu poistettu.
#XFLD: Message for view persistency logs (new message to replace STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS)
REMOVE_VIEW_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Näkymän "{0}" aikataulu poistettu.
#XFLD: Message for view persistency logs
STOP_PERSISTENCY_DELETE_SCHEDULE_FAILED=Kohteen "{1}" aikataulun poisto epäonnistui.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_REDEPLOY=Emme voi muuttaa näkymää "{0}" pysyväksi, koska sitä on muutettu ja se on otettu käyttöön sen jälkeen, kun aloitit sen muuttamisen pysyväksi. Yritä uudelleen näkymän muuttamista pysyväksi tai odota seuraavaan ajoitettuun suoritukseen.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_DELETE=Emme voi muuttaa näkymää "{0}" pysyväksi, koska se on poistettu sen jälkeen, kun aloitit sen muuttamisen pysyväksi.
#XFLD: Message for successful data persistency of a single partition (for messages created before version 2022.03)
START_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} tietuetta muutettu pysyviksi osioon arvoille "{1}" <= {2} < "{3}".
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (new message to replace START_PERSISTENCY_SINGLE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} tietuetta lisätty osioon arvoille "{1}" <= {2} < "{3}".
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS_V2={0} tietuetta lisätty osioon arvoille "{1}" <= "{2}" < "{3}". Käytetty muisti: {4} GiB.
#XFLD: Message for successful replication of a "others" partition (for messages created before version 2022.03)
START_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} tietuetta muutettiin pysyviksi "muut"-osioon.
#XFLD: Message used by SEAL repository for successful replication of a "others" partition (new message to replace START_PERSISTENCY_OTHERS_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} tietuetta lisätty "muut"-osioon.
#XFLD: Message for view persistency record count of partitions (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={3} tietuetta muutettu pysyviksi näkymälle "{1}" {4} osiossa.
#XFLD: Message used by SEAL repository for view persistency record count of partitions (new message to replace START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={0} tietuetta lisätty tietojen pysyvyystauluun näkymälle "{1}" {2} osiossa.
#XFLD: Message used by SEAL repository for view persistency for partition wise refresh
START_VIEW_PERSISTENCY_RECORD_COUNT_REFRESHED_PARTITIONS_SUCCESS={0} tietuetta lisätty näkymän "{1}" pysyvyystauluun. Päivitetyt osiot: {2}; lukitut osiot: {3}; osiot yhteensä: {4}
#XFLD: Message used by SEAL repository for view persistency for manual partition load
START_VIEW_PERSISTENCY_RECORD_COUNT_MANUAL_PARTITIONS_SUCCESS={0} tietuetta lisätty tietojen pysyvyystauluun näkymälle "{1}" {2} valitussa osiossa.
#XFLD: Message used by SEAL repository for view persistency for locked partition data load
START_VIEW_PERSISTENCY_RECORD_COUNT_LOCKED_PARTITIONS_SUCCESS={0} tietuetta lisätty näkymän "{1}" tietojen pysyvyystauluun. Päivitetyt osiot: {2}; lukitut muuttumattomat osiot: {3}; osiot yhteensä: {4}
#XMSG: Message for unexpected error (for messages created before version 2022.03)
START_PERSISTENCY_UNEXPECTED_ERROR=Tapahtui odottamaton virhe muutettaessa näkymän "{0}" tietoja pysyviksi.
#XMSG: Message for unexpected error (new message to replace START_PERSISTENCY_UNEXPECTED_ERROR)
START_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Tapahtui odottamaton virhe muutettaessa näkymän "{0}" tietoja pysyviksi.
#XMSG: Message for Locked space error
START_VIEW_PERSISTENCY_LOCKED_SPACE_ERROR=Näkymä "{0}" ei voi muuttaa pysyväksi, sillä tila "{1}" on lukittu.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
STOP_PERSISTENCY_UNEXPECTED_ERROR=Tapahtui odottamaton virhe poistettaessa näkymän "{0}" pysyviä tietoja.
#XMSG: Message for unexpected error (new message to replace STOP_PERSISTENCY_UNEXPECTED_ERROR)
REMOVE_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Tapahtui odottamaton virhe poistettaessa pysyvyyttä näkymältä "{0}".
#XMSG: Message for ivalidated view
VIEW_PERSISTENCY_ABORTED_DEFINITION_INVALID=Näkymän "{0}" määritelmä on virheellinen. Tämä johtuu todennäköisimmin siitä, että objekti, jota näkymä käyttää suoraan tai epäsuorasti, on muuttunut. Yritä ottaa näkymä uudelleen käyttöön ongelman ratkaisemiseksi tai yritä tunnistaa perussyy.
#XMSG: Message for persisted data removal (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT=Pysyvät tiedot poistetaan otettaessa käyttöön näkymää "{0}".
#XMSG: Message for persisted data removal of a consuming view
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW=Pysyvät tiedot poistetaan otettaessa käyttöön käytettyä näkymää "{0}".
#XMSG: Message for persisted data removal of a consuming view due to a dac change
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW_DAC_CHANGED=Pysyvät tiedot poistetaan otettaessa käyttöön käytettyä näkymää "{0}", sillä sen tietojen käytön valvonta on muuttunut.
#XMSG: Message for persisted data removal (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DEPLOYMENT=Pysyvät tiedot poistetaan otettaessa käyttöön näkymää "{0}".
#XMSG: Message for deleted view (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION=Pysyvyys poistetaan, kun näkymä "{0}" poistetaan.
#XMSG: Message for deleted view (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DELETION=Pysyvyys poistetaan, kun näkymä "{0}" poistetaan.
#XMSG Message for removal of persisted data, because prerequisites not fulfilled
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_PREREQUISITES_NOT_FULFILLED=Pysyvät tiedot poistetaan, koska tietojen pysyvyyden edellytykset eivät enää täyty.
#XMSG: Message for inconsistent persistency
VIEW_PERSISTENCY_INCONSISTENT_PERSISTENCY=Näkymän "{0}" pysyvyys on ristiriitainen. Poista pysyvät tiedot ongelman ratkaisemiseksi.
#XMSG: Message for prerequisites
START_PERSISTENCY_CHECK_PREREQUISITES=Edellytysten tarkistus näkymän "{0}" pysyvyyttä varten.
#XMSG: Message for DAC deployed in deprecated way using table function and join (for messages created before 2025.05)
START_PERSISTENCY_DEPRECIATED_DAC=Näkymä "{0}" on otettu käyttöön käyttämällä tietojen haun ohjausta (DAC), joka on vanhenemassa. Ota näkymä käyttöön uudelleen suorituskyvyn parantamista varten.
#XMSG: Message for DAC deployed in deprecated way using table function and join
START_PERSISTENCY_DEPRECATED_DAC=Näkymä "{0}" on otettu käyttöön käyttämällä tietojen haun ohjausta (DAC), joka on vanhenemassa. Ota näkymä käyttöön uudelleen suorituskyvyn parantamista varten.
#XMSG: Message for roll back (for messages created before version 2022.03)
ROLLBACK_VIEW_PERSISTENCY=Tapahtui virhe. Pysyvyyden edellinen tila palautettu näkymälle "{0}".
#XMSG: Message used by SEAL repository for rollback of running procedure to persist view (new message to replace ROLLBACK_VIEW_PERSISTENCY)
VIEW_PERSISTENCY_ROLLBACK=Tapahtui virhe. Näkymän ''{0}'' pysyväksi muuttamisen prosessi on pysäytetty ja muutokset on hylätty.
#XMSG: Message used by SEAL repository for rollback of running procedure to remove persisted data of view
REMOVE_VIEW_PERSISTENCY_ROLLBACK=On tapahtunut virhe. Näkymän ''{0}'' pysyviksi muutettujen tietojen poistoprosessi on pysäytetty ja muutokset on hylätty.
#XMSG: Message used by SEAL repository for persistency preparation
START_PERSISTENCY_PREPARATION=Valmistellaan tietojen muuttamista pysyviksi.
#XMSG: Message used by SEAL repository for data insertion
START_INSERTING_PERSISTENCY_DATA=Tietojen lisääminen pysyvyystauluun.
#XMSG: Message for null values - partitioning (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS={0} nolla-arvotietuetta lisätty "muut"-osioon.
#XMSG: Message used by SEAL repository for others partition values - partitioning (new message to replace START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS={0} tietuetta lisätty "muut"-osioon arvoille "{2}" < "{1}" TAI "{2}" >= "{3}".
#XMSG: Message used by SEAL repository for others partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS_V2={0} tietuetta lisätty "muut"-osioon arvoille "{2}" < "{1}" TAI "{2}" >= "{3}". Käytetty muisti: {4} GiB.
#XMSG: Message used by SEAL repository for null partition values - partitioning
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS={0} tietuetta lisätty "muut"-osioon arvoille "{1}" ON TYHJÄ.
#XMSG: Message used by SEAL repository for null partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS_V2={0} tietuetta lisätty "muut"-osioon arvoille "{1}" ON TYHJÄ. Käytetty muisti: {2} GiB.
##XFLD:Message used by SEAL repository for remote statistics
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS=Ladataan liittyviä tietoja: {0} etälausetta. Tietueita noudettu yhteensä: {1}. Kokonaiskesto: {2} sekuntia.
##XFLD:Message used by SEAL repository for remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS_WITH_PARTITIONS=Ladataan liittyviä tietoja käyttäen {0} osiota {1} etälauseen kanssa. Tietueita noudettu yhteensä: {2}. Kokonaiskesto: {3} sekuntia.
##XFLD:Message used by SEAL repository with UI ID for instructions of remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_INSTRUCTION=Ajon aikana käsitellyt etälauseet voi näyttää osaspesifien viestien lisätiedoissa etäkyselynvalvonnan avaamalla.
##XFLD:Message for start reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS=Aloitetaan prosessi näkymän {0} olemassa olevien pysyvien tietojen käyttämiseksi uudelleen käyttöönoton jälkeen.
##XFLD:Message used by SEAL repository for reattach view persitency
START_VIEW_PERSISTENCY_REATTACHMENT=Aloita olemassa olevien pysyvien tietojen uudelleenkäyttö.
##XFLD:Message used by SEAL repository for reattach view persitency
START_FINILIZING_VIEW_PERSISTENCY_REATTACHMENT=Käytetään uudelleen olemassa olevia pysyviä tietoja.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_COMPLETE=Prosessi näkymän {0} olemassa olevien pysyvien tietojen käyttämiseksi uudelleen on päätetty.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_FAILED=Näkymän {0} olemassa olevien pysyvien tietojen uudelleenkäyttö käyttöönoton jälkeen epäonnistui.
#XMSG: Message used by SEAL repository for finalizing persistency
START_FINALIZING_PERSISTENCY=Pysyvyyden päättäminen.
#XMSG: Message used by SEAL repository for restoring virtual access for view
REMOVE_VIEW_PERSISTENCY_VIRTUAL_ACCESS_RESTORED=Virtuaalinen tietojen käyttö palautettu näkymälle "{0}".
#XMSG: Message used by SEAL repository for view that already has virtual access
REMOVE_VIEW_PERSISTENCY_ALREADY_VIRTUAL_ACCESS=Näkymässä "{0}" on jo virtuaalinen tietojen käyttö. Pysyviksi muutettuja tietoja ei poisteta.
#XMSG: Message for removing view persistency
VIEW_PERSISTENCY_REMOVED_FROM_MONITOR=Näkymä "{0}" on poistettu näkymien valvonnasta.
#XMSG: Message for view doesnt exist.
VIEW_PERSISTENCY_VIEW_NOT_EXISTING=Näkymää "{0}" ei ole tietokannassa tai sitä ei ole otettu käyttöön oikein, joten sitä ei voida säilyttää. Kokeile ottaa näkymä uudelleen käyttöön ongelman ratkaisemiseksi tai tunnista sen juurisyy.
#XMSG: Message for persistency not enabled.
VIEW_PERSISTENCY_NOT_ENABLED=Näkymän pysyvyyttä ei ole aktivoitu. Ota taulu/näkymä uudelleen käyttöön tilassa {0}" toiminnon aktivoimiseksi.
#XMSG: Message for unexpected error due to node crash
VIEW_PERSISTENCY_UNEXPECTED_ERROR_GENERAL=Viimeisen näkymän pysyvyysajo keskeytettiin teknisten virheiden vuoksi.
#XFLD: Message for view persistency memory consumption
VIEW_PERSISTENCY_MEMORY_CONSUMPTION={0} GiB:tä huippumuistia käytetty näkymän pysyvyyden ajoajassa.
#XFLD: Message for view persistency timeout
VIEW_PERSISTENCY_TIMEOUT=Näkymän {0} pysyvyys saavutti {1} tunnin aikakatkaisun.
#XFLD: Message for not started asynchronous view persistency
VIEW_PERSISTENCY_ASYNC_NOT_STARTED=Suuri järjestelmän kuormitus esti näkymän pysyvyyden asynkronisen suorituksen käynnistyksen. Tarkista, onko liian monta tehtävää käynnissä rinnakkain.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency (for messages created before version 2022.05)
START_VIEW_PERSISTENCY_EXECUTION_METHOD_DROP_AND_RENAME=Olemassa ollut pysyvä taulu poistettiin ja korvattiin uudella pysyvällä taululla.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_RECREATE_AND_DROP=Olemassa ollut pysyvä taulu poistettiin ja korvattiin uudella pysyvällä taululla.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_TRUNCATE_AND_COPY=Olemassa oleva pysyvä taulu on päivitetty uusilla tiedoilla.
##XFLD: Message for missing authorizations for view persistency
VIEW_PERSISTENCY_MISSING_AUTH=Näkymän pysyvyyden oikeudet puuttuvat.
##XFLD: Message start of persistency cancellation
VIEW_PERSISTENCY_TRIGGER_CANCEL=Aloitetaan näkymän {0} pysyväksi muuttamisen prosessin peruuttaminen.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_UNAVAILABLE_TASK_RUN=Näkymän pysyväksi muuttamisen prosessin peruutus epäonnistui, koska näkymälle {0} ei ole käynnissä tietojen pysyvyystehtävää.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_NONDETERMINISTIC_TASK_RUN=Näkymän pysyväksi muuttamisen prosessin peruutus epäonnistui, koska näkymälle {0} ei ole käynnissä tietojen pysyvyystehtävää.
##XFLD: Message for canceling a non running persitency task
VIEW_PERSISTENCY_CANCEL_NON_RUNNING_SELECTED_TASK=Näkymän {0} pysyväksi muuttamisen prosessin peruutus epäonnistui, koska valittu tietojen pysyvyystehtävä {1} ei ole käynnissä.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_UNREACHABLE_PERSISTENCY_RUN=Näkymän pysyväksi muuttamisen prosessin peruutus epäonnistui, koska näkymän {0} tietojen pysyvyys ei ole vielä alkanut.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_COMPLETED_PERSISTENCY_RUN=Näkymän {0} pysyväksi muuttamisen prosessin peruutus epäonnistui, koska se on jo päätetty.
##XFLD: Message for failed cancellation
VIEW_PERSISTENCY_ABORTED_CANCEL_EXECUTION=Näkymän {0} pysyväksi muuttamisen prosessin peruutus epäonnistui.
##XFLD: Message for finalizing persistency cancellation
VIEW_PERSISTENCY_FINALIZE_CANCEL=Prosessi näkymän {0} tietojen pysyväksi muuttamisen pysäyttämiseksi on lähetetty.
##XFLD: Message used in persist task when cancellation is caught
VIEW_PERSISTENCY_CANCELED=Näkymän {0} pysyväksi muuttamisen prosessi on pysähtynyt.
##XFLD: Message used in persist task when cancelation is caught and performed through a cancelation task
VIEW_PERSISTENCY_CANCELED_VIA_CANCEL_TASK=Näkymän {0} pysyväksi muuttamisen prosessi pysäytettiin peruutustehtävän {1} kautta.
##XFLD: Message used in cancel task when the task is triggered by deployment
VIEW_PERSISTENCY_CANCEL_TRIGGERED_BY_DEPLOYMENT=Tietojen pysyväksi muuttamisen prosessi peruutetaan otettaessa käyttöön näkymää {0}.
##XFLD: Message used in case the cancelation is retriggered for a running task
VIEW_PERSISTENCY_CANCEL_ATTEMPT_RETRIGGER_CANCEL=Näkymän {0} pysyvyyden edellinen peruutustehtävä on jo lähetetty.
##XFLD: Message used to express the delay that may occur before the persistency task is stopped
VIEW_PERSISTENCY_CANCEL_DELAY_STOPPING_PERSISTENCY=Saattaa esiintyä viive, ennen kuin näkymän {0} tietojen pysyvyystehtävä on pysäytetty.
##XFLD: Message used to reflect the task log identifier of the current running persistency task
VIEW_PERSISTENCY_CANCEL_CURRENT_RUNNING_TASK=Näkymän {0} tiedot muutetaan pysyviksi tehtävällä {1}.
##XFLD:Message used by SEAL repository for locked partitions and DACs
VIEW_PERSISTENCY_LOCKED_PARTITIONS_DACS=Tiedonkäyttöluokkien antamat käyttöoikeudet ovat saattaneet muuttua, eivätkä lukitut osiot ota niitä huomioon. Avaa osioiden lukitus ja lataa uusi tilannevedos muutosten ottamiseksi käyttöön.
##XFLD:Message used by SEAL repository for column structure change
VIEW_PERSISTENCY_COLUMN_STRUCTURE_CHANGE=Sarakerakenne on muuttunut eikä se enää vastaa olemassa olevaa pysyvyystaulua. Päivitä oma pysyvyystaulu poistamalla pysyvät tiedot ja käynnistämällä uusi tietojen pysyvyys.
##XFLD: Message for out of memory
VIEW_PERSISTENCY_HANA_OUT_OF_MEMORY=Tehtävä epäonnistui SAP HANA -tietokannan muisti ei riitä -virheen vuoksi.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_EXCEPTION_TRANSACTION_ROLLBACK=Tehtävä epäonnistui SAP HANA -tietokannan sisäisen poikkeuksen vuoksi.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_SQL_EXECUTION_ISSUE=Tehtävä epäonnistui SAP HANA -tietokannan sisäisen SQL-suoritusongelman vuoksi.
##XFLD: Message for HANA out of memory result
VIEW_PERSISTENCY_OUT_OF_MEMORY_RESULT_MESSAGE=HANA-muisti ei riitä. Tapahtuman syy: {0}
##XFLD: Message for admission control rejection
VIEW_PERSISTENCY_HANA_ADMISSION_CTRL_REJECTION=Tehtävä epäonnistui SAP HANA -pääsyvalvonnan hylkäyksen vuoksi.
##XFLD: Message for HANA back pressure / rate limit of HANA client
VIEW_PERSISTENCY_HANA_CLIENT_RATE_LIMIT_REJECTION=Tehtävä epäonnistui, koska aktiivisia SAP HANA -yhteyksiä on liian monta.
#XMSG: Message for invalid persisted table name
VIEW_PERSISTENCY_PERSISTED_TABLE_INVALID=On tapahtunut virhe, ja pysyvä taulu on virheellinen. Voit ratkaista ongelman poistamalla pysyvät tiedot ja muuttamalla näkymän uudelleen pysyväksi.
#XMSG: Message for usahe of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION=Näkymää ei voi muuttaa pysyväksi. Se käyttää etätaulua, jonka perustana on etälähde aktivoidun käyttäjän välityksen kanssa. Tarkista näkymän alkuperä.
#XMSG: Message for dynamic usage of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION_DYNAMIC=Näkymää ei voi muuttaa pysäväksi. Se käyttää etätaulua, jonka perustana on etälähde aktivoidun käyttäjän välityksen kanssa. Etätaulua saatetaan käyttää dynaamisesti SQL-skriptinäkymän kautta. Näkymän alkuperä ei ehkä näytä etätaulua.
#XMSG: Message for insufficient privileges or consumption of view with DAC via dynamic SQL
VIEW_PERSISTENCY_INSUF_PRIV_OR_DAC_DYN_SQL=Käyttöoikeutesi eivät välttämättä riitä. Tarkista avaamalla tietojen esikatselu, onko sinulla tarvittavat käyttöoikeudet. Jos kyllä, dynaamisen SQL-skriptin kautta käytettävään toiseen näkymään on saatettu soveltaa tietojen käytön valvontaa (DAC).
#XMSG: deployment of DAC is deprecated and view need to be redeployed to be able to persist data
VIEW_PERSISTENCY_DEPRECATED_DAC=Näkymä "{0}" on otettu käyttöön käyttämällä tietojen haun ohjausta (DAC), joka on vanhenemassa. Ota näkymä käyttöön uudelleen, jotta näkymän tiedot voidaan muuttaa pysyviksi.
##XFLD:Message used by SEAL repository for input parameter
START_VIEW_PERSISTENCY_INPUT_PARAMETER=Oletusarvoa "{0}" käytetään syöttöparametrina "{1}".
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Elastic compute node keys~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
##XFLD:Message used to reflect disablement of ecn replica
ECN_DISABLE_EXISTING_REPLICA=Joustavan laskentasolmun replika on poistettu käytöstä.
##XFLD:Message used to reflect recreation of ecn replica
ECN_RECREATE_EXISTING_REPLICA=Joustavan laskentasolmun replika on luotu uudelleen.
##XFLD:Message used to reflect re-enablement of ecn replica
ECN_ENABLE_EXISTING_REPLICA=Joustavan laskentasolmun replika on aktivoitu uudelleen.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Task Schedule Menu key~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message to schedule task
scheduleText=Aikataulu
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Luo aikataulu
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Muokkaa aikataulua
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Poista aikataulu
#XFLD: Refresh frequency field
refreshFrequency=Päivitystaajuus
#XFLD: Refresh frequency field
refreshFrequencyNew=Tiheys
#XFLD: Refresh frequency field
refreshFrequencyNewNew=Suunniteltu tiheys
#XBUT: label for None
none=Ei mitään
#XBUT: label for Real-Time replication state
realtime=Reaaliaikainen
#XFLD: Label for table column
txtNextSchedule=Seuraava ajo
#XFLD: Label for table column
txtNextScheduleNew=Seuraava suunniteltu ajo
#XFLD: Label for table column
txtNumOfRecords=Tietueiden lukumäärä
#XFLD: Label for scheduled link
scheduledTxt=Ajoitettu
#XFLD: LABEL for partially persisted link
partiallyPersisted=Osittain pysyvä
#XFLD: Text for paused text
paused=Keskeytetty

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ View Monitor Tasklog Details Action keys~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message for release lock actionn
releaseLockMsg=Jos ajo kestää tavallista kauemmin, se saattaa tarkoittaa, että se on epäonnistunut ja että tilaa ei ole päivitetty vastaavasti. \r\n Voit ratkaista ongelman vapauttamalla lukituksen ja asettamalla sen tilan epäonnistuneeksi.
#XFLD: Label for release lock dialog
releaseLockText=Vapauta lukitus

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Column header tooltip ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: tooltip for table column
txtViewTooltip=Pysyvän näkymän nimi
#XFLD: tooltip for table column
txtViewDataAccessTooltip=Tämä ilmaisee näkymän käytettävyyden
#XFLD: tooltip for table column
txtViewRefreshFreqTooltip=Tämä ilmaisee, onko näkymälle määritetty aikataulu
#XFLD: tooltip for table column
txtViewStatusTooltip=Hae pysyvän näkymän tila
#XFLD: tooltip for table column
txtViewLatestUpdateTooltip=Tarjoaa tietoja siitä, milloin jatkuva näkymä viimeksi päivitettiin
#XFLD: tooltip for table column
txtViewNextRunTooltip=Jos näkymälle on määritetty aikataulu, katso milloin seuraava ajo on suunniteltu suoritettavaksi.
#XFLD: tooltip for table column
txtViewNumOfRecordsTooltip=Seuraa tietueiden lukumäärää.
#XFLD: tooltip for table column
txtViewInMemorySizeTooltip=Seuraa, kuinka paljon näkymä käyttää muistiasi
#XFLD: tooltip for table column
txtViewDiskSizeTooltip=Seuraa, kuinka paljon näkymä vie levytilaasi
#XMSG: Expired text
txtExpired=Vanhentunut

#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Objektia "{0}" ei voi lisätä tehtäväketjuun.

#~~~~~~~~~~~View Analyzer task log messages~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message indicating number of records for a view, and memory consumption in Mebibyte during simulation of view persistency
RECORDS_PEAK_MEMORY=Näkymässä "{0}" on {1} tietuetta. Näkymän tietojen pysyvyyden simulointi käytti {2} mebitavua muistia.
#XMSG: View Analyzer execution failure
VIEW_ANALYZER_EXECUTION_FAILED=Näkymän analysoijan suoritus epäonnistui.
#XMSG: Missing authorizations for View Analyzer
VIEW_ANALYZER_MISSING_AUTH=Näkymän analysoijan käyttöoikeudet puuttuvat.
#XMSG: Maximum memory in Gibibyte is reached, therefore persistency simulation is stopped
MAXIMUM_MEMORY_REACHED=Näkymän "{1}" tietojen pysyvyyttä simuloitaessa on saavutettu {0} GiB:n enimmäismuisti. Tästä syystä muita tietojen pysyvyyssimulointeja ei suoriteta.
#XMSG: Error during persistency simulation occurred
PERSISTENCY_SIMULATION_ERROR=Näkymän "{0}" tietojen pysyvyyden simuloinnissa tapahtui virhe.
#XMSG: Persistency simulation is not executed, because prerequisites are not fulfilled and the view cannot be persisted
PREREQUISITES_NOT_FULFILLED=Tietojen pysyvyyden simulointia ei suoriteta näkymälle "{0}", koska edellytykset eivät täyty eikä näkymää voi muuttaa pysyväksi.
#XMSG: Persistency simulation is not executed, because the view does not exist in the database
VIEW_DOES_NOT_EXIST=Sinun on otettava näkymä "{0}" käyttöön, jotta voit aktivoida tietojen pysyvyyden simuloinnin.
#XMSG: Number of records cannot be determined, because the local table does not exist in the database
TABLE_DOES_NOT_EXIST=Tietokannassa ei ole paikallista taulua "{0}", joten taulun tietueiden lukumäärää ei voi määrittää.
##XMSG: Message start of cancellation for View Analyzer task
CANCEL_VIEW_ANALYZER_TRIGGERED=Pysäytysprosessi näkymän analysoijan tehtävän {0} näkymää "{1}" varten lähetetty. Viive on mahdollinen, ennen kuin tehtävä on pysäytetty.
##XMSG: Message start to indicate that task is not active
CANCEL_VIEW_ANALYZER_NO_ACTIVE_TASK=Näkymän analysoijan tehtävä {0} näkymää "{1}" varten ei ole aktiivinen.
#XMSG: Cancel View Analyzer task failure
CANCEL_VIEW_ANALYZER_FAILED=Näkymän analysoijan tehtävän peruutus epäonnistui.
##XMSG: Message used in View Analyzer execution task when task is canceled
VIEW_ANALYZER_CANCELED=Näkymän "{0}" näkymän analysoijan suoritus pysäytettiin peruutustehtävän avulla.
##XMSG: Message start of cancelation for Model Validation task
CANCEL_MODEL_VALIDATION_TRIGGERED=Näkymän "{1}" mallin validointitehtävän {0} pysäytysprosessi lähetetty. Tehtävän pysäytyksessä saattaa esiintyä viive.
##XMSG: Message to indicate that task is not active
CANCEL_MODEL_VALIDATION_NO_ACTIVE_TASK=Näkymän "{1}" mallin validointitehtävä {0} ei ole aktiivinen.
#XMSG: Cancel Model Validation task failure
CANCEL_MODEL_VALIDATION_FAILED=Mallin validointitehtävän peruutus epäonnistui.
##XMSG: Message used in Model Validation task when task is canceled
MODEL_VALIDATION_CANCELED=Näkymän "{0}" mallin validoinnin suoritus pysäytettiin peruutustehtävän avulla.
#XMSG: Message for Locked space error, before starting Model Validation task
MODEL_VALIDATION_LOCKED_SPACE_ERROR=Näkymän "{0}" mallin validointia ei voi suorittaa , koska tila "{1}" on lukittu.
##XMSG: Message used in View Analyzer execution task when determination of number of rows fails
NUMBER_ROWS_DETERMINATION_FAILED=Paikallisen taulukon "{0}" rivien lukumäärää määrittäessä tapahtui virhe.
##XMSG: Message to indicate that SQL plan was created
EXECUTION_PLAN_CREATED=Näkymän "{0}" SQL-analysoijan suunnitelmatiedosto on luotu, ja se voidaan ladata paikallisesti.
#XFLD: Message to indicate starting of process to generate SQL Analyzer plan file
START_PLANVIZ_EXECUTION=Käynnistetään SQL-analysoijan suunnitelmatiedoston generointiprosessi näkymälle "{0}".
#XFLD: Message to indicate starting of View Analyzer process
VIEW_ANALYZER_EXECUTION_STARTED=Käynnistetään näkymän analysoijan suoritus.
##XMSG: Message to indicate that SQL plan file cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_PLANVIZ_NOT_FULFILLED=SQL-analysoijan suunnitelmatiedostoa ei voi generoida näkymälle "{0}", koska tietojen pysyvyyden edellytykset eivät täyty.
##XMSG: Error message during  generation of SQL Analyzer plan file
PLANVIZ_ERROR=Virhe, kun SQL-analysoijan suunnitelmatiedostoa generoitiin näkymälle "{0}".
#XMSG: Message for Locked space error
VIEW_ANALYZER_LOCKED_SPACE_ERROR=Näkymän analysoijaa ei voi suorittaa näkymää "{0}" varten, koska tila "{1}" on lukittu.
#XMSG: Message for persistency simulation without partitioning
PERSISTENCY_SIMULATION_WITHOUT_PARTITIONS=Näkymän "{0}" osioita ei oteta huomioon tietojen pysyvyyden simuloinnin aikana.
#XMSG: Message for generation of SQL Analyzer plan file without considering partitioning
PLANVIZ_WITHOUT_PARTITIONS=Näkymän "{0}" osioita ei oteta huomioon SQL-analysoijan suunnitelmatiedoston generoinnin aikana.
#XMSG: Confirmation message for mass removal of persisted data
MassRemovePersistency_Confirm_Msg=Haluatko poistaa pysyvät tiedot ja vaihtaa tietojen käytön takaisin virtuaaliseen käyttöön?
#XMSG: Confirmation message for partial mass removal of selected persisted data
PartialMassRemovePersistency_Confirm_Msg={0}/{1} valitusta näkymästä sisältää pysyviä tietoja. \n Haluatko poistaa pysyvät tiedot ja vaihtaa tietojen käytön takaisin virtuaaliseen käyttöön?
#XMSG: success message for stopping mass persistence
stopMassPersistenceSuccess=Poistamme pysyvät tiedot valituista näkymistä.
#XMSG: error message during mass stop persistence of a view (backend error)
STOP_MASS_PERSISTENCE_ERROR=Valittujen näkymien pysyvyyden pysäyttämisessä tapahtui virhe.
#XMSG: memory analysis is only performed in space {0}
NO_MEMORY_ANALYSIS=Muistin analyysi suoritetaan vain tilassa "{0}" oleville entiteeteille: "{1}" "{2}" on ohitettu.
##XMSG: Message to indicate that Explain Plan cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_EXPLAINPLAN_NOT_FULFILLED=Explain-suunnitelmatiedostoa ei voida luoda näkymälle "{0}", koska pysyvyyden edellytykset eivät täyty.
#XMSG: Message for Explain Plan generation without partitioning
EXPLAINPLAN_WITHOUT_PARTITIONS=Näkymän "{0}" osioita ei oteta huomioon Explain-suunnitelmatiedoston luonnin aikana.
#XFLD: Message to indicate starting of process to generate Explain Plan file
START_EXPLAINPLAN_EXECUTION=Käynnistetään Explain-suunnitelmatiedoston luontiprosessi näkymälle "{0}".
##XMSG: Message to indicate that Explain Plan was created
EXPLAINPLAN_CREATED=Explain-suunnitelmatiedosto näkymälle "{0}" on luotu. Voit näyttää sen napsauttamalla "Näytä lisätiedot" tai lataamalla sen, jos sinulla on relevantti käyttöoikeus.
##XMSG: Error message during  generation of Explain Plan file
EXPLAINPLAN_ERROR=Virhe Explain-suunnitelmatiedoston luonnissa näkymälle "{0}".
##XMSG: Explain Plan file cannot be generated for the view {0}, because too many views are stacked on each other
EXPLAINPLAN_MODEL_TOO_COMPLEX=Explain-suunnitelmatiedostoa ei voi luoda näkymälle "{0}". Päällekkäisiä näkymiä on liikaa. Monimutkaiset mallit voivat aiheuttaa muistin loppumiseen liittyviä virheitä ja hidastaa suorituskykyä. On suositeltavaa säilyttää näkymä.

##XMSG: Message to indicate that the space is locked
PERFORMANCE_ANALYSIS_LOCKED_SPACE_ERROR=Suorituskykyanalyysia ei voi suorittaa näkymää "{0}" varten, koska tila "{1}" on lukittu.
##XMSG: The performance analysis task was cancelled
PERFORMANCE_ANALYSIS_CANCELED=Suorituskykyanalyysi näkymää "{0}" varten on peruutettu.
##XMSG: The performance analysis failed
PERFORMANCE_ANALYSIS_FAILED=Suorituskykyanalyysi epäonnistui.
##XMSG: The performance analysis is finished
PERFORMANCE_ANALYSIS_FINISHED=Suorituskykyanalyysi näkymää "{0}" varten on päättynyt. Näytä tulos napsauttamalla "Näytä lisätiedot".
##XMSG: The view has input parameter with no default value
INPUT_PARAMETER_WITHOUT_DEFAULT_VALUE=Tätä näkymää ei voi analysoida, koska se sisältää parametrin, jolla ei ole oletusarvoa.
##XMSG: The view is not fully deployed
VIEW_NOT_FULLY_DEPLOYED=Tätä näkymää ei voi analysoida, koska sitä ei ole otettu käyttöön kokonaan.
##XMSG: The view uses limited remote adapters
LIMITED_REMOTE_ADAPTER=Tässä näkymässä käytetään ainakin yhtä etäsovitinta, jonka toiminnot ovat rajoitettuja. Siltä saattaa esimerkiksi puuttua suodattimen pushdown-toiminto tai "Laske"-toiminnon tuki. Objektien säilyttäminen tai replikointi voi parantaa ajonaikaista suorituskykyä. 
##XMSG: The view uses remote adapter that does not support 'Limit'
REMOTE_ADAPTER_NOT_SUPPORTING_LIMIT=Tässä näkymässä käytetään ainakin yhtä etäsovitinta, joka ei tue 'Raja'-toimintoa. Yli 1 000 tietuetta on mahdollisesti valittu.
##XMSG: Performance analysis is executed by using default parameters of view'
INPUT_PARAMETER_WITH_DEFAULT_VALUE=Suorituskykyanalyysi suoritetaan käyttämällä näkymäparametrien oletusarvoja.
##XMSG: Message to indicate that an error occurred
PERFORMANCE_ANALYSIS_ERROR=Virhe näkymän "{0}" suorituskykyanalyysin aikana.
##XMSG: Message start of cancellation for Performance Analysis task
CANCEL_PERFORMANCE_ANALYSIS_TRIGGERED=Pysäytysprosessi suorituskykyanalyysin tehtävän {0} näkymää "{1}" varten lähetetty. Viive on mahdollinen, ennen kuin tehtävä on pysäytetty.
##XMSG: Message start to indicate that task is not active
CANCEL_PERFORMANCE_ANALYSIS_NO_ACTIVE_TASK=Suorituskykyanalyysin tehtävä {0} näkymää "{1}" varten ei ole aktiivinen.
#XMSG: Cancel Performance Analysis task failure
CANCEL_PERFORMANCE_ANALYSIS_FAILED=Suorituskykyanalyysin tehtävän peruutus epäonnistui.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Kohdista aikataulu minulle
#XBUT: Pause schedule menu label
pauseScheduleLabel=Keskeytä aikataulu
#XBUT: Resume schedule menu label
resumeScheduleLabel=Palaa aikatauluun
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Aikatauluja poistaessa tapahtui virhe.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Aikatauluja kohdistaessa tapahtui virhe.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Aikatauluja keskeyttäessä tapahtui virhe.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Aikatauluihin palaamisessa tapahtui virhe.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Poistetaan {0} aikataulua
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Muutetaan {0} aikataulun omistaja
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Keskeytetään {0} aikataulua
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Jatketaan {0} aikataulua
#XBUT: Select Columns Button
selectColumnsBtn=Valitse sarakkeet
#XFLD: Refresh tooltip
TEXT_REFRESH=Päivitä
#XFLD: Select Columns tooltip
text_selectColumns=Valitse sarakkeet


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Runtime Metrics ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Label for Runtime Metrics
RuntimeMetricsTitle=Ajoajan metriikka:
#XFLD : Label for Run Button
runButton=Aja
#XFLD : Label for Cancel Button
cancelButton=Peruuta
#XFLD : Label for Close Button
closeButton=Sulje
#XFLD : Label for Open View Analyzer Button
openViewAnalyzerButton=Avaa näkymän analysoija
#XFLD : Label for Generate Explain Plan Button
generateExplainPlanButton=Generoi Explain-suunnitelma
#XFLD : Label for Previous Run Column
previousRun=Edellinen ajo
#XFLD : Label for Latest Run Column
latestRun=Viimeinen ajo
#XFLD : Label for time Column
time=Aika
#XFLD : Label for Duration Column
duration=Kesto
#XFLD : Label for Peak Memory Column
peakMemory=Huippumuisti
#XFLD : Label for Number of Rows
numberOfRows=Rivien lukumäärä
#XFLD : Label for Overall Number of Sources
overallNumberOfSources=Lähteiden kokonaislukumäärä
#XFLD : Label for Data Access Column
dataAccess=Tietojen käyttö
#XFLD : Label for Local Tables
localTables=Paikalliset taulut
#XFLD : Label for federatedRemoteTables
federatedRemoteTables=Yhdistetyt etätaulut (rajoitettujen sovitinominaisuuksien kanssa)
#XTXT Text for initial state of the runtime metrics
initialState=Suorituskykyanalyysi suoritettava ennen metriikan käyttöä. Se voi kestää jonkin aikaa, mutta prosessin voi peruuttaa tarvittaessa.
#XMSG: Cancel confirmation message
CancelRunConfirmationMessage=Haluatko varmasti peruuttaa suorituskykyanalyysin nykyisen ajon?
#XTIT: Cancel dialog title
CancelRunTitle=Peruuta ajo
#XFLD: Label for Number of Rows
NUMBER_ROWS=Rivien lukumäärä
#XFLD: Label for Overall Number of Sources
OVERALL_NUMBER_SOURCES=Lähteiden kokonaislukumäärä
#XFLD: Label for Data Access
DATA_ACCESS=Tietojen käyttö
#XFLD: Label for Remote tables
NUMBER_REMOTE_TABLES_LIMITED_ADAPTERS=Yhdistetyt etätaulut (rajoitettujen sovitinominaisuuksien kanssa)
#XFLD: Label for select statement
SELECT_STATEMENT='SELECT * FROM VIEW LIMIT 1000'
#XFLD: Label for duration
SELECT_RUNTIME=Kesto
#XFLD: Label for peak memory
SELECT_PEAK_MEMORY=Huippumuisti
#XFLD: Label for count statement
SELECT_COUNT_STATEMENT='SELECT COUNT(*) FROM VIEW'
#XFLD: Label for count duration
SELECT_COUNT_RUNTIME=Kesto
#XFLD: Label for count peak memory
SELECT_COUNT_PEAK_MEMORY=Huippumuisti
#XFLD: Label for count of number of local tables
NUMBER_LOCAL_TABLES_FILE=Paikalliset taulut (tiedosto)
#XTXT: Text for running state of the runtime metrics
Running=Käynnissä...
#XFLD: Label for time
Time=Aika
#XFLD: Label for virtual access
PA_VIRTUAL=Virtuaalinen
#XFLD: Label for persisted access
PA_PERSISTED=Pysyvä
PA_PARTIALLY_PERSISTED=Osittain pysyvä
#XTXT: Text for cancel
CancelRunSuccessMessage=Peruutetaan suorituskykyanalyysin ajo.
#XTXT: Text for cancel error
CancelRunErrorMessage=Virhe suorituskykyanalyysin ajoa peruutettaessa.
#XTXT: Text for explain plan generation
ExplainPlanStarted=Generoidaan Explain-suunnitelma näkymälle "{0}".
#XTXT: Text for performance analysis
PerformanceAnalysisStarted=Käynnistetään näkymän "{0}" suorituskykyanalyysi.
#XTXT: Text for performance analysis error
ErrorFetchingPerformanceAnalysis=Virhe suorituskykyanalyysin tietoja haettaessa.
#XTXT: Text for performance analysis error
conflictingTask=Suorituskykyanalyysitehtävä on jo käynnissä
#XFLD: Label for Errors
Errors=Virhe(itä)
#XFLD: Label for Warnings
Warnings=Varoituksia
#XFLD: Tooltip for missing privileges
openViewAnalyzerButtonTooltipNoPrivilege=Näkymän analysoijan avaamiseen tarvitaan DWC_DATAINTEGRATION(update)-käyttöoikeus.
#XFLD: Tooltip for missing privileges
generateExplainPlanButtonTooltipNoPrivilege=Explain-suunnitelman generointiin tarvitaan DWC_RUNTIME(read)-käyttöoikeus.



#XFLD: Label for frequency column
everyLabel=Aina
#XFLD: Plural Recurrence text for Hour
hoursLabel=Tunnit
#XFLD: Plural Recurrence text for Day
daysLabel=Pv
#XFLD: Plural Recurrence text for Month
monthsLabel=kuukauden välein
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minuutit
