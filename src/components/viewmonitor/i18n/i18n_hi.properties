
#XBCB: Breadcrumb
viewMonitorBreadcrumb={spaceId}

#XFLD: Label for persisted view column
SOURCE=स्रोत
#XFLD: Label for persisted view column
NAME=नाम
#XFLD: Label for persisted view column
NAME_LABEL=व्यवसाय नाम
#XFLD: Label for persisted view column
NAME_LABELNew=ऑब्जेक्ट (व्यवसाय का नाम)
#XFLD: Label for persisted view column
TECHINCAL_NAME=तकनीकी नाम
#XFLD: Label for persisted view column
TECHINCAL_NAMENew=ऑब्जेक्ट (तकनीकी नाम)
#XFLD: Label for persisted view column
DATA_PERSISTENCY=डेटा तक पहुंचना
#XFLD: Label for persisted view column
STATUS=स्थिति
#XFLD: Label for persisted view column
LAST_UPDATED=अंतिम बार अपडेट किया गया
#XFLD: Label for persisted view column
IN_MEMORY_SIZE=(MiB) स्टोरेज के लिए उपयोग की जाने वाली मेमोरी
#XFLD: Label for persisted view column
DISK_SIZE=(MiB) संग्रहण के लिए उपयोग की जाने वाली डिस्क
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_NEWW=आकार इन-मेमोरी (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_HEADER=आकार इन-मेमोरी
#XFLD: Label for persisted view column
DISK_SIZE_NEWW=डिस्क पर आकार (MiB)
#XFLD: Label for persisted view column
DISK_SIZE_HEADER=डिस्क पर आकार
#XFLD: Label for schedule owner column
txtScheduleOwner=शेड्यूल स्वामी
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=दिखाता है कि शेड्यूल किसने बनाया है
#XFLD: Label for persisted view column
PERSISTED=जारी
#XFLD: Label for persisted view column
TYPE=प्रकार
#XFLD: Label for View Selection Dialog column
changedOn=परिवर्तन दिनांक
#XFLD: Label for View Selection Dialog column
createdBy=निर्माता
#XFLD: Label for log details column
txtViewPersistencyLogs=लॉग देखें
#XFLD: Label for log details column
txtViewPersistencyLogsNew=विवरण
#XFLD: text for values shown for Ascending sort order
SortInAsc=आरोही क्रमित करें
#XFLD: text for values shown for Descending sort order
SortInDesc=अवरोही क्रम में क्रमित करें
#XFLD: Label for persisted view monitor heading
ViewMonitor_Heading=दृश्य मॉनिटर
#XFLD: Label for persisted view monitor sub-heading
ViewMonitor_SubHeading=दृश्य के डेटा  निर्बाध को मॉनिटर करें और रखरखाव करें


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ DynamicPageHeader ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: text for values shown in column Persistence Status
txtLoading=लोड किया जा रहा है
#XFLD: text for values shown in column Persistence Status
txtRunning=रन हो रहा है
#XFLD: text for values shown in column Persistence Status
txtAvailable=उपलब्ध
#XFLD: text for values shown in column Persistence Status
txtError=त्रुटि
#XMSG: Error message for unknown Persistence type that gets logged in Kibana
unknownReplicationTypeError=प्रतिकृति प्रकार ''{0}'' समर्थित नहीं है.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Content ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=अंतिम डेटा निर्बाध रन के लिए उपयोग की गई सेटिंग:
#XMSG: Message for input parameter name
inputParameterLabel=इनपुट पैरामीटर
#XMSG: Message for input parameter value
inputParameterValueLabel=मान
#XMSG: Message for persisted data
inputParameterPersistedLabel=इस पर निर्बाधित
#XTIT: Count for View in ViewMonitor Toolbar
txtViewCount=दृश्य ({0})
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=स्थिरता देखें
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=डेटा निर्बाध
#XBUT: Button to clear the selected view persistance
ClearViewPersistance=साफ़ करें
#XBUT: Button to stop the selected view persistance
stopPersistance=स्थिरता रोकें
#XFLD: Placeholder for Search field
txtSearch=खोजें
#XBUT: Tooltip for refresh button
txtRefresh=रीफ़्रेश करें
#XBUT: Tooltip for add view button
txtDeleteView=निर्बाध हटाएं
#XBUT: Tooltip for load new peristence
loadNewPersistence=निर्बाध पुनः प्रारंभ करें
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=नया स्नैपशॉट लोड करें
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=डेटा निर्बाध प्रारंभ करें
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=स्थायी डेटा निकालें
#XMSG: success message for starting persistence
startPersistenceSuccess=हम दृश्य ''{0}” जारी रख रहे हैं.
#XMSG: success message for stopping persistence
stopPersistenceSuccess=हम दृश्य ''{0}''’ के लिए निर्बाध डेटा हटा रहे हैं.
#XMSG: success message for stopping persistence
deletePersistedViewSuccess=हम मॉनिटरिंग सूची से ''{0}'' दृश्य को निकाल रहें हैं.
#XMSG: error message during start persistence of a view (backend error)
ERROR_STARTING_PERSISTENCE=दृश्य "{0}" के लिए डेटा निर्बाध प्रारंभ करते समय एक त्रुटि उत्पन्न हुई.
#XMSG: error message during start persistence of a view (backend error)
PARAMERROR_STARTING_PERSISTENCE=दृश्य ''{0}''’ निर्बाध के लिए योग्य नहीं है क्योंकि इसमें इनपुट पैरामीटर हैं.
#XMSG: error message during start persistence of a view (backend error)
NUMBER_OF_PARAMETER_STARTING_PERSISTENCE=दृश्य "{0}" निर्बाध के लिए योग्य नहीं है क्योंकि इसमें एक से अधिक इनपुट पैरामीटर हैं.
#XMSG: error message during start persistence of a view (backend error)
PARAMETER_DEFAULT_VALUE_STARTING_PERSISTENCE=दृश्य "{0}" निर्बाध के लिए योग्य नहीं है क्योंकि इनपुट पैरामीटर में डिफ़ॉल्ट मान नहीं है.
#XMSG: error message during start persistence of a view (backend error)
REDEPLOYMENT_DAC_REQUIRED=दृश्य दृढ़ता का समर्थन करने के लिए आपको डेटा पहुंच नियंत्रण (DAC) ''{0}'' का पुन: परिनियोजन करने की आवश्यकता है.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_STARTING_PERSISTENCE=दृश्य ''{0}'' को जारी नहीं रखा जा सकता है, क्योंकि यह ''{1}'' दृश्य का उपयोग करता है, जिसमें डेटा एक्सेस कंट्रोल (DAC) है.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_EXTERNAL_STARTING_PERSISTENCE=दृश्य ''{0}''’ का निर्बांध नहीं किया जा सकता, क्योंकि यह डेटा पहुंच नियंत्रण (DAC) के साथ दृश्य का उपयोग करता है जो एक अलग स्थान से संबंधित है.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_STRUCTURE_STARTING_PERSISTENCE=दृश्य ''{0}'' का निर्बांध नहीं किया जा सकता, क्योंकि इसके एक या अधिक डेटा पहुंच नियंत्रण (DAC) की संरचना दृश्य निर्बाध का समर्थन नहीं करती है.
#XMSG: error message during stop persistence of a view (backend error)
STOP_PERSISTENCE_ERROR=दृश्य "{0}" के लिए निर्बाध को रोकते समय एक त्रुटि उत्पन्न हुई.
#XMSG: error message during stop persistence of a view (backend error)
DELETE_PERSISTED_VIEW_ERROR=निर्बाध दृश्य ''{0}''’ हटाते समय एक त्रुटि हुई.
#XMSG: Warning message for removing the persisted data
RemovePersistency_Confirm_Msg=क्या आप निर्बाध डेटा को हटाना चाहते हैं और दृश्य ''{0}''’ के वर्चुअल पहुंच में स्विच करना चाहते हैं?
#XMSG: Warning message for delete the view persistence
DeletePersistency_Confirm_Msg=क्या आप दृश्य को मॉनिटरिंग सूची से निकालना चाहते हैं और दृश्य ''{0}''’ के निर्बाध डेटा को हटाना चाहते हैं?
#XMSG: error message for reading data from backend
txtReadBackendError=ऐसा लगता है कि वापस पीछे से पढ़ते समय कोई त्रुटि हुई है.
#XFLD: Label for No Data Error
NoDataError=त्रुटि
#XMSG: message for conflicting task
Task_Already_Running=दृश्य ''{0}'' के लिए विरोधी कार्य पहले से ही रन हो रहा है.

#XFLD: Error while accessing pertitioning for a view
missingPrivilegeInViewPersistency=दृश्य ''{0}'' के लिए विभाजन निष्पादित करने के लिए अपर्याप्त अनुमति

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Show All Views~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Text for show all views button
allViewsText=सभी दृश्य ({0})
#XBUT: Text for show scheduled views button
scheduledText=शेड्यूल किया गया ({0})
#XBUT: Text for show persisted views button
persistedText=जारी {0}
#XBUT: Text for start analyzer button
startAnalyzer=दृश्य विश्लेषक प्रारंभ करें
#XFLD: Message if repository is unavailable
repositoryErrorMsg=कोष उपलब्ध नहीं है और कुछ सुविधाएं अक्षम हैं.

#XFLD: Data Access - Virtual
Virtual=वर्चूअल
#XFLD: Data Access - Persisted
Persisted=जारी

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Non-Persisted View Selection Dialog~~~~~~~~~~~~~~~~~~~~~~~

#XTIT: Title for Non-Persisted View selection dialog
select_source=जारी रखने के लिए दृश्य चुनें

#XTIT: Placeholder for Non-Persisted View selection dialog search
searchView=दृश्यों को खोजें
#XTIT: No data in the list of non-persisted view
No_Data=कोई डेटा नहीं
#XBUT: Button to select non-persisted view
ok=ठीक है
#XBUT: Button to close the non-persisted views selection dialog
cancel=रद्द करें

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~View persistency log message key~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message for view persistency logs
START_STOP_PERSISTENCY="{1}" के लिए डेटा निर्बाध कार्य प्रारंभ करना.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY=दृश्य ''{1}'' के लिए डेटा स्थिरता शुरू हो रही है.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY)
START_VIEW_PERSISTENCY_PROCESS=दृश्य ''{0}'' के लिए डेटा स्थायी करने हेतु प्रक्रिया प्रारंभिक हो गई है.
#XFLD: Message for view persistency manual partition load
START_VIEW_PERSISTENCY_PROCESS_MANUAL_PARTITIONS=चयनित विभाजन ID: के साथ दृश्य "{0}" के लिए निर्बांध डेटा की प्रक्रिया प्रारंभ करना: "{1}”.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY=हम दृश्य ''{1}''’ के लिए निर्बाध डेटा निकाल रहे हैं.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY)
REMOVE_VIEW_PERSISTENCY=दृश्य ''{0}'' के लिए स्थायी डेटा को निकालने की प्रक्रिया प्रारंभ की जा रही है.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_SUCCESS=दृश्य ''{1}'' के लिए डेटा निर्बाध है
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_SUCCESS)
START_VIEW_PERSISTENCY_PROCESS_SUCCESS=दृश्य ''{0}'' के लिए डेटा निर्बाध है
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY_SUCCESS=दृश्य ''{1}''’ के लिए स्थिरता वाला डेटा निकाला गया और वर्चुअल डेटा पहुंच को पुर्नप्राप्त किया गया.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_SUCCESS)
REMOVE_VIEW_PERSISTENCY_SUCCESS=दृश्य ''{0}'' के लिए स्थायी डेटा को निकालने की प्रक्रिया पूर्ण की गई है.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_FAILED=दृश्य ''{1}'' के लिए निर्बांध डेटा में असमर्थ.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_FAILED)
START_VIEW_PERSISTENCY_PROCESS_FAILED=दृश्य ''{0}'' के लिए निर्बांध डेटा में असमर्थ.
#XFLD: Message for view persistency logs (for messages created before version 2022.05)
STOP_VIEW_PERSISTENCY_FAILED=दृश्य ''{1}'' के लिए निर्बाध डेटा निकालने में असमर्थ.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_FAILED)
REMOVE_VIEW_PERSISTENCY_FAILED=दृश्य ''{0}'' के लिए निर्बाध डेटा निकालने में असमर्थ.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_SUCCESS=\ ''{3}'' रिकॉर्ड दृश्य ''{1}''’ के लिए निर्बाध किए गए.
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace START_PERSISTENCY_RECORD_COUNT_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS={0} रिकॉर्ड दृश्य ''{1}''’ के लिए डेटा निर्बाध तालिका में सम्मिलित किया गया.
#XFLD: Message used by SEAL repository for view persistency logs (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS_V2={0} रिकॉर्ड दृश्य "{1}" के लिए डेटा निर्बाध तालिका में सम्मिलित किए गए. प्रयुक्त मेमोरी: {2} GiB.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_SUCCESS="{3}" के निर्बाध रिकॉर्ड दृश्य "{1}" के लिए निकाले गए.
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace STOP_PERSISTENCY_RECORD_COUNT_SUCCESS)
REMOVE_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=स्थिरता डेटा निकाला गया, ''{0}''’ स्थिरता रिकॉर्ड हटाएं.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_FAILED=दृश्य ''{1}''’ के लिए रिकॉर्ड गणना प्राप्त करना विफल.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_FAILED=दृश्य ''{1}''’ के लिए रिकॉर्ड गणना प्राप्त करना विफल.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=''{1}''’ के लिए शेड्यूल हटाया गया.
#XFLD: Message for view persistency logs (new message to replace STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS)
REMOVE_VIEW_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=दृश्य ''{0}''’ के लिए शेड्यूल हटाया गया.
#XFLD: Message for view persistency logs
STOP_PERSISTENCY_DELETE_SCHEDULE_FAILED=‘''{1}''’ के लिए हटाने का शेड्यूल विफल रहा.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_REDEPLOY=हम दृश्य ''{0}'' को जारी नहीं रख सकते क्योंकि जब से आपने इसे जारी रखना शुरू किया है तब से इसे बदल दिया गया है और परिनियोजित किया गया है. दृश्य जारी रखने के लिए पुन: प्रयास करें या अगले शेड्यूल किए गए रन तक प्रतीक्षा करें.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_DELETE=हम ''{0}'' दृश्य को जारी नहीं रख सकते क्योंकि जब से आपने इसे जारी रखना शुरू किया है तब से इसे हटा दिया गया है .
#XFLD: Message for successful data persistency of a single partition (for messages created before version 2022.03)
START_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} रिकॉर्ड ''{1}'' <= {2} < ''{3}'' मानों के लिए विभाजन में बने रहे.
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (new message to replace START_PERSISTENCY_SINGLE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} रिकॉर्ड ''{1}'' <= {2} < ''{3}'' मानों के लिए विभाजन में बने रहे.
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS_V2={0} रिकॉर्ड मानों ''{1}'' <= ''{2}'' < ''{3}''.के लिए विभाजन में सम्मिलित किया गया मेमरी का उपयोग किया गया: {4} GiB.
#XFLD: Message for successful replication of a "others" partition (for messages created before version 2022.03)
START_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} रिकॉर्ड ''अन्य'' विभाजन में निर्बाध रहे.
#XFLD: Message used by SEAL repository for successful replication of a "others" partition (new message to replace START_PERSISTENCY_OTHERS_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} रिकॉर्ड ''अन्य'' विभाजन में निर्बंध किया गया है.
#XFLD: Message for view persistency record count of partitions (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={3} रिकॉर्ड दृश्य ''{1}''’ के लिए {4} विभाजनों में निर्बाध किया गया है.
#XFLD: Message used by SEAL repository for view persistency record count of partitions (new message to replace START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={0} रिकॉर्ड {2} विभाजन में दृश्य ''’{1}''’ के लिए डेटा निर्बाध तालिका में सम्मिलित किया गया.
#XFLD: Message used by SEAL repository for view persistency for partition wise refresh
START_VIEW_PERSISTENCY_RECORD_COUNT_REFRESHED_PARTITIONS_SUCCESS={0} रिकॉर्ड को ''{1}'' देखने के लिए निर्बांध तालिका में सम्मिलित किया गया है. अपडेट किए गए विभाजन: {2} लॉक किए गए विभाजन: {3} कुल विभाजन: {4}
#XFLD: Message used by SEAL repository for view persistency for manual partition load
START_VIEW_PERSISTENCY_RECORD_COUNT_MANUAL_PARTITIONS_SUCCESS={0} रिकॉर्ड {2} चयनित विभाजनों में दृश्य ''{1}'' के लिए निर्बाध तालिका में सम्मिलित किए गए.
#XFLD: Message used by SEAL repository for view persistency for locked partition data load
START_VIEW_PERSISTENCY_RECORD_COUNT_LOCKED_PARTITIONS_SUCCESS={0} रिकॉर्ड को दृश्य ''{1}'' के लिए निर्बंध तालिका में सम्मिलित किया गया है. अपडेट किए गए विभाजन: {2} लॉक किए गए, अपरिवर्तित विभाजन: {3} कुल विभाजन: {4}
#XMSG: Message for unexpected error (for messages created before version 2022.03)
START_PERSISTENCY_UNEXPECTED_ERROR=दृश्य ''{0}'' के लिए डेटा बनाते समय एक अनपेक्षित त्रुटि हुई.
#XMSG: Message for unexpected error (new message to replace START_PERSISTENCY_UNEXPECTED_ERROR)
START_VIEW_PERSISTENCY_UNEXPECTED_ERROR=दृश्य ''{0}'' के लिए डेटा बनाते समय एक अनपेक्षित त्रुटि हुई.
#XMSG: Message for Locked space error
START_VIEW_PERSISTENCY_LOCKED_SPACE_ERROR=दृश्य ''’{0}'' निर्बाध नहीं किया जा सकता, क्योंकि स्थान ''{1}'' लॉक है.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
STOP_PERSISTENCY_UNEXPECTED_ERROR=दृश्य ''{0}'' के लिए निर्बाध डेटा निकालते समय अनपेक्षित त्रुटि उत्पन्न हुई.
#XMSG: Message for unexpected error (new message to replace STOP_PERSISTENCY_UNEXPECTED_ERROR)
REMOVE_VIEW_PERSISTENCY_UNEXPECTED_ERROR=दृश्य ''{0}'' के लिए निर्बाध डेटा निकालते समय अनपेक्षित त्रुटि उत्पन्न हुई.
#XMSG: Message for ivalidated view
VIEW_PERSISTENCY_ABORTED_DEFINITION_INVALID=दृश्य ''{0}''’ की परिभाषा अमान्य हो गई है, संभवतः दृश्य द्वारा प्रत्यक्ष या अप्रत्यक्ष रूप से उपभोग की गई वस्तु के परिवर्तन के कारण. समस्या को हल करने के लिए, या मूल कारण की पहचान करने के लिए दृश्य को पुन: नियोजित करने का प्रयास करें.
#XMSG: Message for persisted data removal (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT=दृश्य ''{0}''’ नियोजित करते समय निर्बांध डेटा निकाला गया.
#XMSG: Message for persisted data removal of a consuming view
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW=निर्बाध डेटा की उपभोग की गई दृश्य ''{0}''’ परिनियोजित करने के दौरान निकाला गया.
#XMSG: Message for persisted data removal of a consuming view due to a dac change
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW_DAC_CHANGED=उपभोग किए गए दृश्य ''{0}'' को परिनियोजित करते समय निर्बाध डेटा निकाल दिया जाता है क्योंकि इसका डेटा पहुंच नियंत्रण बदल गया है.
#XMSG: Message for persisted data removal (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DEPLOYMENT=दृश्य ''{0}''’ परीनियोजित करते समय निर्बांध डेटा निकाला गया.
#XMSG: Message for deleted view (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION=दृश्य ''{0}''’ के हटाने के साथ निर्बाध निकाल दिया गया है.
#XMSG: Message for deleted view (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DELETION=दृश्य ''{0}''’ के हटाने के साथ निर्बाध निकाल दिया गया है.
#XMSG Message for removal of persisted data, because prerequisites not fulfilled
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_PREREQUISITES_NOT_FULFILLED=निर्बांध डेटा निकाला गया है क्योंकि डेटा दृढ़ता पूर्वापेक्षाएँ अब पूरी नहीं हुई हैं.
#XMSG: Message for inconsistent persistency
VIEW_PERSISTENCY_INCONSISTENT_PERSISTENCY=दृश्य ''{0}''’ का निर्बाध असंगत है. समस्या का निवारण करने के लिए मौजूदा डेटा हटाएँ.
#XMSG: Message for prerequisites
START_PERSISTENCY_CHECK_PREREQUISITES=दृश्य ''{0}'' की पूर्वापेक्षा के लिए आवश्कताएं जांचना.
#XMSG: Message for DAC deployed in deprecated way using table function and join (for messages created before 2025.05)
START_PERSISTENCY_DEPRECIATED_DAC=दृश्य "{0}" को डेटा पहुंच नियंत्रण (DAC) का उपयोग करके परिनियोजित किया गया है जिसे रोका गया है. प्रदर्शन को बेहतर बनाने के लिए कृपया दृश्य को फिर से परिनियोजित करें.
#XMSG: Message for DAC deployed in deprecated way using table function and join
START_PERSISTENCY_DEPRECATED_DAC=दृश्य "{0}" को डेटा पहुंच नियंत्रण (DAC) का उपयोग करके परिनियोजित किया गया है जिसे रोका गया है. प्रदर्शन को बेहतर बनाने के लिए कृपया दृश्य को फिर से परिनियोजित करें.
#XMSG: Message for roll back (for messages created before version 2022.03)
ROLLBACK_VIEW_PERSISTENCY=त्रुटि हुई. दृश्य ''{0}'' के लिए निर्बाध की पिछली स्थिति पुनर्स्थापित की गई.
#XMSG: Message used by SEAL repository for rollback of running procedure to persist view (new message to replace ROLLBACK_VIEW_PERSISTENCY)
VIEW_PERSISTENCY_ROLLBACK=त्रुटि हुई. दृश्य ''{0}'' को जारी रखने की प्रक्रिया बंद की गई और परिवर्तनों को रोल बैक किया गया.
#XMSG: Message used by SEAL repository for rollback of running procedure to remove persisted data of view
REMOVE_VIEW_PERSISTENCY_ROLLBACK=त्रुटि हुई. दृश्य ''{0}'' की निर्बांध डेटा को निकालने की प्रक्रिया बंद की गई और परिवर्तनों को रोल बैक किया गया.
#XMSG: Message used by SEAL repository for persistency preparation
START_PERSISTENCY_PREPARATION=डेटा जारी रखने की तैयारी करना.
#XMSG: Message used by SEAL repository for data insertion
START_INSERTING_PERSISTENCY_DATA=निरंतरता तालिका में डेटा डालना.
#XMSG: Message for null values - partitioning (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS={0} शून्य मान रिकॉर्ड "अन्य" विभाजन में सम्मिलित किए गए.
#XMSG: Message used by SEAL repository for others partition values - partitioning (new message to replace START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS={0} रिकॉर्ड ‘''{2}'' < ''{1}'' या ''{2}'' >= ''{3}''’मानों के लिए ''अन्य''’ विभाजन में सम्मिलित किया गया है.
#XMSG: Message used by SEAL repository for others partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS_V2={0} रिकॉर्ड मानों ''{2}'' < ''{1}'' या ''{2}'' >= ''{3}''’ हेतु ''अन्य''’ विभाजन में सम्मिलित किया गया. मेमरी का उपयोग किया गया: {4} GiB.
#XMSG: Message used by SEAL repository for null partition values - partitioning
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS={0} रिकॉर्ड ‘{1}'' IS NULL मानों के लिए ''अन्य''’ विभाजन में सम्मिलित किया गया है.
#XMSG: Message used by SEAL repository for null partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS_V2={0} रिकॉर्ड मान ''{1}'' IS NULL हेतु ''अन्य''’ विभाजन में सम्मिलित किया गया. मेमरी का उपयोग किया गया: {2} GiB.
##XFLD:Message used by SEAL repository for remote statistics
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS=शामिल डेटा लोड हो रहा है: {0} दूरस्थ विवरण. कुल रिकॉर्ड फ़ैच किए गए: {1}. कुल अवधि समय: {2} सेकंड.
##XFLD:Message used by SEAL repository for remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS_WITH_PARTITIONS={1} दूरस्थ विवरण के साथ {0} विभाजन का उपयोग करके, लोडिंग डेटा शामिल किया गया. कुल रिकॉर्ड फ़ैच किए गए: {2}. कुल अवधि समय: {3} सेकंड.
##XFLD:Message used by SEAL repository with UI ID for instructions of remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_INSTRUCTION=रन के दौरान प्रोसेस किए गए रिमोट दूरस्थ विवरण को विभाजन-विशिष्ट संदेशों के विवरण में दूरस्थ क्वेरी मॉनिटर खोलकर प्रदर्शित किया जा सकता है.
##XFLD:Message for start reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS=परिनियोजन के बाद दृश्य {0} के लिए मौजूदा स्थायी डेटा का पुन: उपयोग करने की प्रक्रिया प्रारंभ करना.
##XFLD:Message used by SEAL repository for reattach view persitency
START_VIEW_PERSISTENCY_REATTACHMENT=मौजूदा स्थायी डेटा का पुन: उपयोग करना प्रारंभ करें.
##XFLD:Message used by SEAL repository for reattach view persitency
START_FINILIZING_VIEW_PERSISTENCY_REATTACHMENT=मौजूदा स्थायी डेटा का पुन: उपयोग करना.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_COMPLETE=दृश्य {0} के लिए मौजूदा स्थायी डेटा का पुन: उपयोग करने की प्रक्रिया पूर्ण हो गई है.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_FAILED=परिनियोजन के बाद दृश्य {0} के लिए मौजूदा स्थायी डेटा का पुन: उपयोग करने में विफल.
#XMSG: Message used by SEAL repository for finalizing persistency
START_FINALIZING_PERSISTENCY=निर्बाध को अंतिम रूप देना.
#XMSG: Message used by SEAL repository for restoring virtual access for view
REMOVE_VIEW_PERSISTENCY_VIRTUAL_ACCESS_RESTORED=दृश्य‘''{0}''’ के लिए वर्चुअल डेटा पहुंच पुनर्स्थापित की गई.
#XMSG: Message used by SEAL repository for view that already has virtual access
REMOVE_VIEW_PERSISTENCY_ALREADY_VIRTUAL_ACCESS=दृश्य "{0}" में पहले से ही वर्चुअल डेटा पहुंच है. कोई भी निर्बांध डेटा निकाला नहीं गया है.
#XMSG: Message for removing view persistency
VIEW_PERSISTENCY_REMOVED_FROM_MONITOR=दृश्य मॉनिटर से दृश्य ''{0}''’ से निकाला गया.
#XMSG: Message for view doesnt exist.
VIEW_PERSISTENCY_VIEW_NOT_EXISTING=दृश्य "{0}" या तो डेटाबेस में मौजूद नहीं है या सही ढंग से परिनियोजित नहीं है और इसलिए इसे बरकरार नहीं रखा जा सकता है. समस्या का समाधान करने के लिए, या मूल कारण की पहचान करने के लिए दृश्य को फिर से परिनियोजित करने का प्रयास करें.
#XMSG: Message for persistency not enabled.
VIEW_PERSISTENCY_NOT_ENABLED=दृश्य निर्बाध सक्षम नहीं किया गया है. कार्यक्षमता को सक्षम करने के लिए स्थान ''{0}'' में तालिका/दृश्य को पुन: नियोजित करें.
#XMSG: Message for unexpected error due to node crash
VIEW_PERSISTENCY_UNEXPECTED_ERROR_GENERAL=अंतिम दृश्य स्थिरता रन तकनीकी त्रुटियों के कारण बाधित किया गया.
#XFLD: Message for view persistency memory consumption
VIEW_PERSISTENCY_MEMORY_CONSUMPTION=दृश्य स्थिरता रनटाइम में पीक मेमरी का {0} GiB उपयोग किया गया.
#XFLD: Message for view persistency timeout
VIEW_PERSISTENCY_TIMEOUT=दृश्य {0} के निर्बाध {1}घंटों के समयआउट तक पहुंच गई.
#XFLD: Message for not started asynchronous view persistency
VIEW_PERSISTENCY_ASYNC_NOT_STARTED=उच्च सिस्टम लोड ने दृश्य दृढ़ता के अतुल्यकालिक निष्पादन को प्रारंभ होने से रोका गया जांच करें कि क्या बहुत सारे कार्य समानांतर में चल रहे हैं.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency (for messages created before version 2022.05)
START_VIEW_PERSISTENCY_EXECUTION_METHOD_DROP_AND_RENAME=मौजूदा निर्बाध तालिका हटा दी गई है और एक नई निर्बाध तालिका के साथ प्रतिस्थापित किया गया है.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_RECREATE_AND_DROP=मौजूदा निर्बाध तालिका हटा दी गई है और एक नई निर्बाध तालिका के साथ प्रतिस्थापित किया गया है.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_TRUNCATE_AND_COPY=मौजूदा निर्बाध तालिका को नए डेटा के साथ अपडेट किया गया है.
##XFLD: Message for missing authorizations for view persistency
VIEW_PERSISTENCY_MISSING_AUTH=दृश्य निर्बाध के लिए प्राधिकार गुम है.
##XFLD: Message start of persistency cancellation
VIEW_PERSISTENCY_TRIGGER_CANCEL=दृश्य निर्बाध के लिए प्रक्रिया को रद्द करने हेतु प्रारंभ करें {0}.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_UNAVAILABLE_TASK_RUN=दृश्य निर्बाध के लिए प्रक्रिया को रद्द करने हेतु में विफल रहा क्योंकि दृश्य {0} के लिए कोई निर्बाध कार्य रन नहीं कर रहा.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_NONDETERMINISTIC_TASK_RUN=दृश्य निर्बाध के लिए प्रक्रिया को रद्द करने हेतु में विफल रहा क्योंकि दृश्य {0} के लिए कोई निर्बाध कार्य नहीं रन कर रहा है.
##XFLD: Message for canceling a non running persitency task
VIEW_PERSISTENCY_CANCEL_NON_RUNNING_SELECTED_TASK=दृश्य {0} को बनाए रखने की प्रक्रिया को रद्द करने में विफल क्योंकि चयनित निर्बाध कार्य {1} रनिंग नहीं है.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_UNREACHABLE_PERSISTENCY_RUN=दृश्य निर्बाध के लिए प्रक्रिया को रद्द करने हेतु में विफल रहा क्योंकि दृश्य {0} के लिए डेटा निर्बाध अभी तक शुरू नहीं हुई है.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_COMPLETED_PERSISTENCY_RUN=दृश्य निर्बाध के लिए प्रक्रिया को रद्द करने में विफल हुआ {0} क्योंकि यह पहले ही पूरा हो हो गया है.
##XFLD: Message for failed cancellation
VIEW_PERSISTENCY_ABORTED_CANCEL_EXECUTION=दृश्य निर्बाध के लिए प्रक्रिया को रद्द करने में विफल हुआ {0}.
##XFLD: Message for finalizing persistency cancellation
VIEW_PERSISTENCY_FINALIZE_CANCEL=दृश्य {0} की डेटा निर्बाध को रोकने की प्रक्रिया सबमिट की गई है.
##XFLD: Message used in persist task when cancellation is caught
VIEW_PERSISTENCY_CANCELED=दृश्य {0} निर्बाध हेतु प्रक्रिया बंद किया गया.
##XFLD: Message used in persist task when cancelation is caught and performed through a cancelation task
VIEW_PERSISTENCY_CANCELED_VIA_CANCEL_TASK=दृश्य {0}को जारी रखने की प्रक्रिया रद्दीकरण कार्य {1}के माध्यम से रोक दी गई थी.
##XFLD: Message used in cancel task when the task is triggered by deployment
VIEW_PERSISTENCY_CANCEL_TRIGGERED_BY_DEPLOYMENT=दृश्य {0} परिनियोजन के दौरान निर्बाध डेटा हेतु प्रक्रिया रद्द करें.
##XFLD: Message used in case the cancelation is retriggered for a running task
VIEW_PERSISTENCY_CANCEL_ATTEMPT_RETRIGGER_CANCEL=दृश्य {0} को जारी रखने के लिए पिछला रद्दीकरण कार्य पहले ही सबमिट किया जा चुका है.
##XFLD: Message used to express the delay that may occur before the persistency task is stopped
VIEW_PERSISTENCY_CANCEL_DELAY_STOPPING_PERSISTENCY=दृश्य {0} के लिए स्थिरता कार्य रुकने तक विलंब हो सकता है.
##XFLD: Message used to reflect the task log identifier of the current running persistency task
VIEW_PERSISTENCY_CANCEL_CURRENT_RUNNING_TASK=दृश्य {0} के लिए डेटा कार्य {1} के साथ बरकरार रखा जा रहा है.
##XFLD:Message used by SEAL repository for locked partitions and DACs
VIEW_PERSISTENCY_LOCKED_PARTITIONS_DACS=DAC द्वारा प्रदान प्राधिकार परिवर्तित हो सकता है और लॉक किए गए विभाजनों द्वारा उसे विचार में नहीं लिया जा सकता. परिवर्तनों को लागू करने के लिए विभाजनों को अनलॉक करें और नया स्नैपशॉट लोड करें.
##XFLD:Message used by SEAL repository for column structure change
VIEW_PERSISTENCY_COLUMN_STRUCTURE_CHANGE=स्तंभ संरचना बदल गई है और अब मौजूदा दृढ़ता तालिका से मिलान नहीं करती है. अपनी दृढ़ता तालिका को अपडेट करने के लिए स्थायी डेटा हटाएं और एक नया डेटा दृढ़ता प्रारंभ करें.
##XFLD: Message for out of memory
VIEW_PERSISTENCY_HANA_OUT_OF_MEMORY=SAP HANA database पर मेमोरी समाप्त होने की त्रुटि के कारण कार्य विफल हो गया.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_EXCEPTION_TRANSACTION_ROLLBACK=SAP HANA डेटाबेस पर आंतरिक अपवाद के कारण कार्य विफल हो गया.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_SQL_EXECUTION_ISSUE=SAP HANA डेटाबेस पर आंतरिक SQL निष्पादन समस्या के कारण कार्य विफल हुआ.
##XFLD: Message for HANA out of memory result
VIEW_PERSISTENCY_OUT_OF_MEMORY_RESULT_MESSAGE=HANA मेमोरी समाप्त होने की घटना का कारण:{0}
##XFLD: Message for admission control rejection
VIEW_PERSISTENCY_HANA_ADMISSION_CTRL_REJECTION=SAP HANA प्रवेश नियंत्रण अस्वीकृति के कारण कार्य विफल हो गया.
##XFLD: Message for HANA back pressure / rate limit of HANA client
VIEW_PERSISTENCY_HANA_CLIENT_RATE_LIMIT_REJECTION=बहुत अधिक सक्रिय SAP HANA कनेक्शन के कारण कार्य विफल हो गया.
#XMSG: Message for invalid persisted table name
VIEW_PERSISTENCY_PERSISTED_TABLE_INVALID=कोई त्रुटि उत्पन्न हुई और निर्बाध तालिका अमान्य हो गई है. समस्या को हल करने के लिए, कृपया निर्बाध डेटा को हटा दें और दृश्य को फिर से निर्बाध करें.
#XMSG: Message for usahe of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION=दृश्य को बनाए नहीं रखा जा सकता. यह सक्षम उपयोगकर्ता प्रसार के साथ दूरस्थ स्रोत पर आधारित दूरस्थ तालिका का उपयोग करता है. दृश्य की वंशावली की जाँच करें.
#XMSG: Message for dynamic usage of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION_DYNAMIC=दृश्य को बनाए नहीं रखा जा सकता. यह सक्षम उपयोगकर्ता प्रसार के साथ दूरस्थ स्रोत पर आधारित दूरस्थ तालिका का उपयोग करता है. दूरस्थ तालिका को SQL स्क्रिप्ट दृश्य के माध्यम से गतिशील रूप से उपयोग किया जा सकता है. दृश्य की वंशावली दूरस्थ तालिका को नहीं दिखा सकती है.
#XMSG: Message for insufficient privileges or consumption of view with DAC via dynamic SQL
VIEW_PERSISTENCY_INSUF_PRIV_OR_DAC_DYN_SQL=आपके विशेषाधिकार अपर्याप्त हो सकते हैं. यह देखने के लिए डेटा पूर्वावलोकन खोलें कि क्या आपके पास आवश्यक विशेषाधिकार हैं. यदि हाँ, तो डायनेमिक SQL स्क्रिप्ट के माध्यम से उपभोग किए गए दूसरे दृश्य पर डेटा पहुंच नियंत्रण (DAC) लागू हो सकता है.
#XMSG: deployment of DAC is deprecated and view need to be redeployed to be able to persist data
VIEW_PERSISTENCY_DEPRECATED_DAC=दृश्य "{0}" को डेटा पहुंच नियंत्रण (DAC) का उपयोग करके परिनियोजित किया गया है जिसे रोका गया है. दृश्य के लिए डेटा निर्बांध करने में सक्षम होने के लिए कृपया दृश्य को फिर से परिनियोजित करें.
##XFLD:Message used by SEAL repository for input parameter
START_VIEW_PERSISTENCY_INPUT_PARAMETER=इनपुट पैरामीटर "{1}" के लिए डिफॉल्ट मान "{0}" का उपयोग करना.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Elastic compute node keys~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
##XFLD:Message used to reflect disablement of ecn replica
ECN_DISABLE_EXISTING_REPLICA=इलास्टिक कंप्यूट नोड प्रतिकृति अक्षम है.
##XFLD:Message used to reflect recreation of ecn replica
ECN_RECREATE_EXISTING_REPLICA=इलास्टिक कंप्यूट नोड प्रतिकृति को पुननिर्मित किया गया है.
##XFLD:Message used to reflect re-enablement of ecn replica
ECN_ENABLE_EXISTING_REPLICA=इलास्टिक कंप्यूट नोड प्रतिकृति पुन: सक्षम है.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Task Schedule Menu key~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message to schedule task
scheduleText=शेड्यूल
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=शेड्यूल बनाएं
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=शेड्यूल संपादित करें
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=शेड्यूल हटाएं
#XFLD: Refresh frequency field
refreshFrequency=आवृति रिफ़्रेश करें
#XFLD: Refresh frequency field
refreshFrequencyNew=आवृत्ति
#XFLD: Refresh frequency field
refreshFrequencyNewNew=शेड्यूल की गई आवृत्ति
#XBUT: label for None
none=कोई नहीं
#XBUT: label for Real-Time replication state
realtime=वास्तविक-समय
#XFLD: Label for table column
txtNextSchedule=अगला रन
#XFLD: Label for table column
txtNextScheduleNew=अगला रन शेड्यूल किया गया
#XFLD: Label for table column
txtNumOfRecords=रिकॉर्ड की संख्या
#XFLD: Label for scheduled link
scheduledTxt=शेड्यूल किया गया
#XFLD: LABEL for partially persisted link
partiallyPersisted=आंशिक रूप से निर्बाधित
#XFLD: Text for paused text
paused=रोका गया

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ View Monitor Tasklog Details Action keys~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message for release lock actionn
releaseLockMsg=यदि रन में सामान्य से अधिक समय लगता है तो यह संकेत देता है कि वह विफल हो गया है या स्थिति को तदानुसार अपडेट नहीं किया गया है. \r\n समस्या का निवारण करने के लिए आप लॉक रिलीज़ कर सकते हैं और उसकी स्थिति को विफल में सेट कर सकते हैं.
#XFLD: Label for release lock dialog
releaseLockText=लॉक रिलीज़ करें

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Column header tooltip ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: tooltip for table column
txtViewTooltip=स्थायी दृश्य का नाम
#XFLD: tooltip for table column
txtViewDataAccessTooltip=यह दृश्य की उपलब्धता को इंगित करता है
#XFLD: tooltip for table column
txtViewRefreshFreqTooltip=यह इंगित करता है कि क्या दृश्य के लिए कोई शेड्यूल परिभाषित किया गया है
#XFLD: tooltip for table column
txtViewStatusTooltip=स्थायी दृश्य की स्थिति प्राप्त करें
#XFLD: tooltip for table column
txtViewLatestUpdateTooltip=अंतिम बार अपडेट किए जाने के बारे में जानकारी प्रदान करता है
#XFLD: tooltip for table column
txtViewNextRunTooltip=यदि दृश्य के लिए कोई शेड्यूल सेट किया गया है, तो देखें कि अगला रन कब शेड्यूल किया गया है.
#XFLD: tooltip for table column
txtViewNumOfRecordsTooltip=रिकॉर्ड की संख्या ट्रैक करें
#XFLD: tooltip for table column
txtViewInMemorySizeTooltip=ट्रैक करें कि आपकी मेमोरी में दृश्य कितना आकार उपयोग कर रहा है
#XFLD: tooltip for table column
txtViewDiskSizeTooltip=ट्रैक करें कि आपकी डिस्क पर दृश्य कितना आकार ले रहा है
#XMSG: Expired text
txtExpired=समाप्त हुआ

#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=ऑब्जेक्ट ''{0}'' को कार्य श्रृंखला में जोड़ा नहीं जा सकता.

#~~~~~~~~~~~View Analyzer task log messages~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message indicating number of records for a view, and memory consumption in Mebibyte during simulation of view persistency
RECORDS_PEAK_MEMORY=दृश्य ''{0}'' में {1} रिकॉर्ड हैं. दृश्य सततता के एक अनुरूपण ने स्मृति के {2} MiB का उपयोग किया.
#XMSG: View Analyzer execution failure
VIEW_ANALYZER_EXECUTION_FAILED=दृश्य विश्लेषक निष्पादन विफल हुआ.
#XMSG: Missing authorizations for View Analyzer
VIEW_ANALYZER_MISSING_AUTH=दृश्य निर्बाध के लिए प्राधिकार गुम है.
#XMSG: Maximum memory in Gibibyte is reached, therefore persistency simulation is stopped
MAXIMUM_MEMORY_REACHED=दृश्य ''{1}'' के लिए निर्बाध के अनुरूपण के दौरान {0} GiB की अधिकतम स्मृति तक पहुंच गई है. इसलिए, कोई और दृश्य निर्बाध अनुरूपण रन नहीं किया जाएगा.
#XMSG: Error during persistency simulation occurred
PERSISTENCY_SIMULATION_ERROR=दृश्य ''{0}''’ के लिए सततता अनुरूपण के दौरान एक त्रुटि उत्पन्न हुई.
#XMSG: Persistency simulation is not executed, because prerequisites are not fulfilled and the view cannot be persisted
PREREQUISITES_NOT_FULFILLED=दृश्य ''{0}''’ के लिए सततता अनुरूपण निष्पादित नहीं किया गया है, क्योंकि पूर्वापेक्षाओं की पूर्ति नहीं की गई है और दृश्य को निर्बाध नहीं रखा जा सकता.
#XMSG: Persistency simulation is not executed, because the view does not exist in the database
VIEW_DOES_NOT_EXIST=आपको डेटा निर्बांध अनुरूपण को सक्षम करने के लिए दृश्य "{0}" को परिनियोजित करना होगा.
#XMSG: Number of records cannot be determined, because the local table does not exist in the database
TABLE_DOES_NOT_EXIST=स्थानीय तालिका "{0}" डेटाबेस में मौजूद नहीं है, इसलिए इस तालिका के लिए रिकॉर्ड की संख्या निर्धारित नहीं की जा सकती.
##XMSG: Message start of cancellation for View Analyzer task
CANCEL_VIEW_ANALYZER_TRIGGERED=दृश्य ''{1}'' के लिए दृश्य विश्लेषक कार्य {0} को रोकने की प्रक्रिया सबमिट कर दी गई है. कार्य रुकने तक विलंब हो सकता है.
##XMSG: Message start to indicate that task is not active
CANCEL_VIEW_ANALYZER_NO_ACTIVE_TASK=दृश्य ''{1}''’ के लिए दृश्य विश्लेषक कार्य {0} सक्रिय नहीं है.
#XMSG: Cancel View Analyzer task failure
CANCEL_VIEW_ANALYZER_FAILED=दृश्य विश्लेषक कार्य रद्द करने में विफल हुआ.
##XMSG: Message used in View Analyzer execution task when task is canceled
VIEW_ANALYZER_CANCELED=दृश्य "{0}" के लिए दृश्य विश्लेषक निष्पादन को रद्दीकरण कार्य के माध्यम से रोक दिया गया था.
##XMSG: Message start of cancelation for Model Validation task
CANCEL_MODEL_VALIDATION_TRIGGERED=दृश्य ''{1}'' के लिए मॉडल सत्यापन कार्य {0} को रोकने की प्रक्रिया सबमिट कर दी गई है. कार्य रुकने तक विलंब हो सकता है.
##XMSG: Message to indicate that task is not active
CANCEL_MODEL_VALIDATION_NO_ACTIVE_TASK=दृश्य "{1}" के लिए मॉडल सत्यापन कार्य {0} सक्रिय नहीं है.
#XMSG: Cancel Model Validation task failure
CANCEL_MODEL_VALIDATION_FAILED=मॉडल सत्यापन कार्य रद्द करने में विफल.
##XMSG: Message used in Model Validation task when task is canceled
MODEL_VALIDATION_CANCELED=दृश्य "{0}" के लिए मॉडल सत्यापन निष्पादन को रद्दीकरण कार्य के माध्यम से रोक दिया गया था.
#XMSG: Message for Locked space error, before starting Model Validation task
MODEL_VALIDATION_LOCKED_SPACE_ERROR=दृश्य ''{0}''’ के लिए मॉडल सत्यापन निष्पादित नहीं किया जा सकता, क्योंकि स्थान "{1}" लॉक है.
##XMSG: Message used in View Analyzer execution task when determination of number of rows fails
NUMBER_ROWS_DETERMINATION_FAILED=स्थानीय तालिका ''{0}''.के लिए पंक्तियों की संख्या निर्धारित करते समय त्रुटि हुई.
##XMSG: Message to indicate that SQL plan was created
EXECUTION_PLAN_CREATED=दृश्य ''’{0}'' के लिए SQL विश्लेषक योजना फ़ाइल बनाई गई है और डाउनलोड की जा सकती है.
#XFLD: Message to indicate starting of process to generate SQL Analyzer plan file
START_PLANVIZ_EXECUTION=दृश्य ''’{0}'' के लिए SQL विश्लेषक योजना फ़ाइल जनरेट करने की प्रक्रिया प्रारंभ हो रही है.
#XFLD: Message to indicate starting of View Analyzer process
VIEW_ANALYZER_EXECUTION_STARTED=आरंभिक दृश्य विश्लेषक निष्पादन.
##XMSG: Message to indicate that SQL plan file cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_PLANVIZ_NOT_FULFILLED=दृश्य ''’{0}''’ के लिए SQL विश्लेषक योजना फ़ाइल जनरेट नहीं की जा सकती, क्योंकि दृढ़ता संबंधी पूर्वापेक्षाएँ पूरी नहीं हुई हैं.
##XMSG: Error message during  generation of SQL Analyzer plan file
PLANVIZ_ERROR=दृश्य ''’{0}'' के लिए SQL विश्लेषक योजना फ़ाइल के जनरेशन के दौरान एक त्रुटि उत्पन्न हुई.
#XMSG: Message for Locked space error
VIEW_ANALYZER_LOCKED_SPACE_ERROR=दृश्य ''{0}''’ के लिए दृश्य विश्लेषक निष्पादित नहीं किया जा सकता, क्योंकि स्थान ''{1}'' लॉक है.
#XMSG: Message for persistency simulation without partitioning
PERSISTENCY_SIMULATION_WITHOUT_PARTITIONS=दृश्य निर्बाध के अनुकरण के दौरान दृश्य ''{0}'' के विभाजन पर विचार नहीं किया जाता है.
#XMSG: Message for generation of SQL Analyzer plan file without considering partitioning
PLANVIZ_WITHOUT_PARTITIONS=SQL विश्लेषक योजना फ़ाइल के जनरेशन के दौरान ''’{0}'' दृश्य के विभाजन पर विचार नहीं किया जाता है.
#XMSG: Confirmation message for mass removal of persisted data
MassRemovePersistency_Confirm_Msg=क्या आप स्थायी डेटा को हटाना चाहते हैं और डेटा एक्सेस को वापस वर्चुअल एक्सेस पर स्विच करना चाहते हैं?
#XMSG: Confirmation message for partial mass removal of selected persisted data
PartialMassRemovePersistency_Confirm_Msg={0} का {1}चयनित दृश्यों में से स्थायी डेटा प्राप्त हुआ है। \n क्या आप स्थायी डेटा को हटाना चाहते हैं और डेटा एक्सेस को वापस वर्चुअल एक्सेस पर स्विच करना चाहते हैं?
#XMSG: success message for stopping mass persistence
stopMassPersistenceSuccess=हम चयनित दृश्यों के लिए स्थायी डेटा निकाल रहे हैं.
#XMSG: error message during mass stop persistence of a view (backend error)
STOP_MASS_PERSISTENCE_ERROR=चयनित दृश्यों के लिए निर्बाध को रोकते समय त्रुटि उत्पन्न हुई.
#XMSG: memory analysis is only performed in space {0}
NO_MEMORY_ANALYSIS=स्मृति विश्लेषण केवल स्थान "{0}" में निकाय के लिए निष्पादित किया जाता है: "{1}" "{2}" छोड़ दिया गया है.
##XMSG: Message to indicate that Explain Plan cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_EXPLAINPLAN_NOT_FULFILLED=दृश्य ''’{0}''’ के लिए स्पष्टीकरण योजना फ़ाइल जनरेट नहीं की जा सकती, क्योंकि दृढ़ता संबंधी पूर्वापेक्षाएँ पूरी नहीं हुई हैं.
#XMSG: Message for Explain Plan generation without partitioning
EXPLAINPLAN_WITHOUT_PARTITIONS=स्पष्टीकरण योजना फ़ाइल के जनरेशन के दौरान दृश्य "{0}" के विभाजन पर विचार नहीं किया जाता है.
#XFLD: Message to indicate starting of process to generate Explain Plan file
START_EXPLAINPLAN_EXECUTION=दृश्य ''’{0}'' के लिए स्पष्टीकरण योजना फ़ाइल जनरेट करने की प्रक्रिया प्रारंभ हो रही है.
##XMSG: Message to indicate that Explain Plan was created
EXPLAINPLAN_CREATED=दृश्य "{0}" के लिए योजना स्पष्ट करें फ़ाइल जनरेट कर दी गई है. आप इसे "विवरण देखें" पर क्लिक करके प्रदर्शित कर सकते हैं या यदि आपके पास प्रासंगिक अनुमति है, तो इसे डाउनलोड कर सकते हैं.
##XMSG: Error message during  generation of Explain Plan file
EXPLAINPLAN_ERROR=दृश्य ''’{0}'' के लिए स्पष्टीकरण योजना फ़ाइल के जनरेशन के दौरान एक त्रुटि उत्पन्न हुई.
##XMSG: Explain Plan file cannot be generated for the view {0}, because too many views are stacked on each other
EXPLAINPLAN_MODEL_TOO_COMPLEX=योजना स्पष्ट करें फ़ाइल, दृश्य "{0}" के लिए जनरेट नहीं की जा सकती. बहुत सारे दृश्य एक-दूसरे पर क्रमबद्ध हैं. जटिल मॉडल मेमोरी की कमी वाली त्रुटियां और धीमे प्रदर्शन का कारण बन सकते हैं. हम दृढ़ता से एक दृश्य का निर्बाध करने की अनुशंसा करते हैं.

##XMSG: Message to indicate that the space is locked
PERFORMANCE_ANALYSIS_LOCKED_SPACE_ERROR=दृश्य ''{0}''’ के लिए दृश्य विश्लेषक निष्पादित नहीं किया जा सकता, क्योंकि स्थान ''{1}'' लॉक है.
##XMSG: The performance analysis task was cancelled
PERFORMANCE_ANALYSIS_CANCELED=दृश्य "{0}" के लिए निष्पादन विश्लेषण रद्द कर दिया गया.
##XMSG: The performance analysis failed
PERFORMANCE_ANALYSIS_FAILED=निष्पादन विश्लेषण विफल हुआ.
##XMSG: The performance analysis is finished
PERFORMANCE_ANALYSIS_FINISHED=दृश्य "{0}" के लिए निष्पादन विश्लेषण समाप्त हो गया है। "विवरण देखें" पर क्लिक करके परिणाम प्रदर्शित करें।
##XMSG: The view has input parameter with no default value
INPUT_PARAMETER_WITHOUT_DEFAULT_VALUE=इस दृश्य का विश्लेषण नहीं किया जा सकता क्योंकि इसमें एक पैरामीटर है जिसका कोई डिफ़ॉल्ट मान नहीं है.
##XMSG: The view is not fully deployed
VIEW_NOT_FULLY_DEPLOYED=इस दृश्य का विश्लेषण नहीं किया जा सकता क्योंकि यह पूरी तरह से परिनियोजित नहीं है.
##XMSG: The view uses limited remote adapters
LIMITED_REMOTE_ADAPTER=यह दृश्य सीमित क्षमताओं वाले कम से कम एक रिमोट एडाप्टर का उपयोग करता है जैसे कि फ़िल्टर पुशडाउन का अभाव या 'गणना' के लिए समर्थन. ऑब्जेक्ट को बनाए रखना या उसकी प्रतिकृति बनाना रनटाइम प्रदर्शन को बेहतर बना सकता है.
##XMSG: The view uses remote adapter that does not support 'Limit'
REMOTE_ADAPTER_NOT_SUPPORTING_LIMIT=यह दृश्य कम से कम एक दूरस्थ एडॉप्टर का उपयोग करता है जो 'लिमिट' का समर्थन नहीं करता है। 1000 से अधिक रिकॉर्ड चयनित हो सकते हैं.
##XMSG: Performance analysis is executed by using default parameters of view'
INPUT_PARAMETER_WITH_DEFAULT_VALUE=निष्पादन विश्लेषण को दृश्य मापदंडों के डिफ़ॉल्ट मानों का उपयोग करके निष्पादित किया जाता है।
##XMSG: Message to indicate that an error occurred
PERFORMANCE_ANALYSIS_ERROR=दृश्य "{0}" के लिए प्रदर्शन विश्लेषण के दौरान एक त्रुटि उत्पन्न हुई.
##XMSG: Message start of cancellation for Performance Analysis task
CANCEL_PERFORMANCE_ANALYSIS_TRIGGERED=दृश्य ''{1}'' के लिए दृश्य विश्लेषक कार्य {0} को रोकने की प्रक्रिया सबमिट कर दी गई है. कार्य रुकने तक विलंब हो सकता है.
##XMSG: Message start to indicate that task is not active
CANCEL_PERFORMANCE_ANALYSIS_NO_ACTIVE_TASK=दृश्य ''{1}''’ के लिए निष्पादन विश्लेषक कार्य {0} सक्रिय नहीं है.
#XMSG: Cancel Performance Analysis task failure
CANCEL_PERFORMANCE_ANALYSIS_FAILED=निष्पादन विश्लेषण कार्य रद्द करने में विफल.

#XBUT: Assign schedule menu button label
assignScheduleLabel=मुझे शेड्यूल असाइन करें
#XBUT: Pause schedule menu label
pauseScheduleLabel=शेड्यूल रोकें
#XBUT: Resume schedule menu label
resumeScheduleLabel=शेड्यूल फिर से शुरू करें
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=शेड्यूल निकालते समय त्रुटि उत्पन्न हुई.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=शेड्यूल असाइन करते समय त्रुटि उत्पन्न हुई.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=शेड्यूल रोकते समय त्रुटि उत्पन्न हुई.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=शेड्यूल फिर से शुरू करते समय त्रुटि उत्पन्न हुई.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted={0} शेड्यूल हटाना
#XMSG: Message for starting mass assign of schedules
massAssignStarted={0} शेड्यूल के स्वामी में परिवर्तन करना
#XMSG: Message for starting mass pausing of schedules
massPauseStarted={0} शेड्यूल रोकना
#XMSG: Message for starting mass resuming of schedules
massResumeStarted={0} शेड्यूल फिर से शुरू करना
#XBUT: Select Columns Button
selectColumnsBtn=स्तंभों का चयन करें
#XFLD: Refresh tooltip
TEXT_REFRESH=रीफ़्रेश करें
#XFLD: Select Columns tooltip
text_selectColumns=स्तंभों का चयन करें


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Runtime Metrics ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Label for Runtime Metrics
RuntimeMetricsTitle=के लिए रनटाइम मेट्रिक्स
#XFLD : Label for Run Button
runButton=रन
#XFLD : Label for Cancel Button
cancelButton=रद्द करें
#XFLD : Label for Close Button
closeButton=बंद करें
#XFLD : Label for Open View Analyzer Button
openViewAnalyzerButton=दृश्य विश्लेषक खोलें
#XFLD : Label for Generate Explain Plan Button
generateExplainPlanButton=स्पष्टीकरण योजना जनरेट करें
#XFLD : Label for Previous Run Column
previousRun=पिछला रन
#XFLD : Label for Latest Run Column
latestRun=नविनतम रन
#XFLD : Label for time Column
time=समय
#XFLD : Label for Duration Column
duration=अवधि
#XFLD : Label for Peak Memory Column
peakMemory=अधिकतम स्मृति
#XFLD : Label for Number of Rows
numberOfRows=पंक्तियों की संख्या
#XFLD : Label for Overall Number of Sources
overallNumberOfSources=स्रोतों की कुल संख्या
#XFLD : Label for Data Access Column
dataAccess=डेटा पहुंच
#XFLD : Label for Local Tables
localTables=स्थानीय तालिका
#XFLD : Label for federatedRemoteTables
federatedRemoteTables=फ़ेडरेटेड दूरस्थ तालिकाएं (सीमित एडाप्टर क्षमताओं के साथ)
#XTXT Text for initial state of the runtime metrics
initialState=मेट्रिक्स प्राप्त करने के लिए आपको पहले प्रदर्शन विश्लेषण रन करना होगा. इसमें कुछ समय लग सकता है, लेकिन यदि आवश्यक हो तो आप प्रक्रिया को रद्द कर सकते हैं.
#XMSG: Cancel confirmation message
CancelRunConfirmationMessage=क्या आप वाकई प्रदर्शन विश्लेषण के वर्तमान रन को रद्द करना चाहते हैं?
#XTIT: Cancel dialog title
CancelRunTitle=रन रद्द करें
#XFLD: Label for Number of Rows
NUMBER_ROWS=पंक्तियों की संख्या
#XFLD: Label for Overall Number of Sources
OVERALL_NUMBER_SOURCES=स्रोतों की कुल संख्या
#XFLD: Label for Data Access
DATA_ACCESS=डेटा पहुंच
#XFLD: Label for Remote tables
NUMBER_REMOTE_TABLES_LIMITED_ADAPTERS=फ़ेडरेटेड दूरस्थ तालिकाएं (सीमित एडाप्टर क्षमताओं के साथ)
#XFLD: Label for select statement
SELECT_STATEMENT=दृश्य सीमा 1000' से * का चयन करें
#XFLD: Label for duration
SELECT_RUNTIME=अवधि
#XFLD: Label for peak memory
SELECT_PEAK_MEMORY=अधिकतम स्मृति
#XFLD: Label for count statement
SELECT_COUNT_STATEMENT='दृश्य से गणना (*) चुनें'
#XFLD: Label for count duration
SELECT_COUNT_RUNTIME=अवधि
#XFLD: Label for count peak memory
SELECT_COUNT_PEAK_MEMORY=अधिकतम स्मृति
#XFLD: Label for count of number of local tables
NUMBER_LOCAL_TABLES_FILE=स्थानीय तालिकाएँ (फ़ाइल) 
#XTXT: Text for running state of the runtime metrics
Running=रन हो रहा है
#XFLD: Label for time
Time=समय
#XFLD: Label for virtual access
PA_VIRTUAL=वर्चूअल
#XFLD: Label for persisted access
PA_PERSISTED=जारी
PA_PARTIALLY_PERSISTED=आंशिक रूप से निर्बाधित
#XTXT: Text for cancel
CancelRunSuccessMessage=निष्पादन विश्लेषण का संचालन रद्द करना.
#XTXT: Text for cancel error
CancelRunErrorMessage=प्रदर्शन विश्लेषण रन रद्द करते समय एक त्रुटि उत्पन्न हुई.
#XTXT: Text for explain plan generation
ExplainPlanStarted=दृश्य "{0}" के लिए व्याख्या योजना जेनरेट की जा रही है. 
#XTXT: Text for performance analysis
PerformanceAnalysisStarted=दृश्य "{0}" के लिए प्रदर्शन विश्लेषण प्रारंभ किया जा रहा है.
#XTXT: Text for performance analysis error
ErrorFetchingPerformanceAnalysis=प्रदर्शन विश्लेषण डेटा प्राप्त करते समय एक त्रुटि हुई.
#XTXT: Text for performance analysis error
conflictingTask=प्रदर्शन विश्लेषण कार्य पहले से चल रहा है
#XFLD: Label for Errors
Errors=त्रुटि(यां)
#XFLD: Label for Warnings
Warnings=चेतावनी(नियां)
#XFLD: Tooltip for missing privileges
openViewAnalyzerButtonTooltipNoPrivilege=दृश्य विश्लेषक को खोलने के लिए आपको DWC_DATAINTEGRATION(update) विशेषाधिकार की आवश्यकता है.
#XFLD: Tooltip for missing privileges
generateExplainPlanButtonTooltipNoPrivilege=व्याख्या योजना जेनरेट करने के लिए आपको DWC_RUNTIME(read) विशेषाधिकार की आवश्यकता है.



#XFLD: Label for frequency column
everyLabel=प्रत्येक
#XFLD: Plural Recurrence text for Hour
hoursLabel=घंटे
#XFLD: Plural Recurrence text for Day
daysLabel=दिन
#XFLD: Plural Recurrence text for Month
monthsLabel=महीने
#XFLD: Plural Recurrence text for Minutes
minutesLabel=मिनट
