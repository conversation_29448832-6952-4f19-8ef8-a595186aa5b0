
#XBCB: Breadcrumb
viewMonitorBreadcrumb={spaceId}

#XFLD: Label for persisted view column
SOURCE=ต้นทาง
#XFLD: Label for persisted view column
NAME=ชื่อ
#XFLD: Label for persisted view column
NAME_LABEL=ชื่อทางธุรกิจ
#XFLD: Label for persisted view column
NAME_LABELNew=ออบเจค (ชื่อทางธุรกิจ)
#XFLD: Label for persisted view column
TECHINCAL_NAME=ชื่อทางเทคนิค
#XFLD: Label for persisted view column
TECHINCAL_NAMENew=ออบเจค (ชื่อทางเทคนิค)
#XFLD: Label for persisted view column
DATA_PERSISTENCY=การเข้าถึงข้อมูล
#XFLD: Label for persisted view column
STATUS=สถานะ
#XFLD: Label for persisted view column
LAST_UPDATED=อัพเดทครั้งล่าสุด
#XFLD: Label for persisted view column
IN_MEMORY_SIZE=หน่วยความจำที่ใช้สำหรับพื้นที่จัดเก็บ (MiB)
#XFLD: Label for persisted view column
DISK_SIZE=ดิสก์ที่ใช้สำหรับพื้นที่จัดเก็บ (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_NEWW=ขนาดในหน่วยความจำ (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_HEADER=ขนาดในหน่วยความจำ
#XFLD: Label for persisted view column
DISK_SIZE_NEWW=ขนาดในดิสก์ (MiB)
#XFLD: Label for persisted view column
DISK_SIZE_HEADER=ขนาดในดิสก์
#XFLD: Label for schedule owner column
txtScheduleOwner=เจ้าของกำหนดการ
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=แสดงผู้ที่สร้างกำหนดการ
#XFLD: Label for persisted view column
PERSISTED=ถูกคงไว้
#XFLD: Label for persisted view column
TYPE=ประเภท
#XFLD: Label for View Selection Dialog column
changedOn=เปลี่ยนแปลงเมื่อ
#XFLD: Label for View Selection Dialog column
createdBy=สร้างโดย
#XFLD: Label for log details column
txtViewPersistencyLogs=ดูล็อก
#XFLD: Label for log details column
txtViewPersistencyLogsNew=รายละเอียด
#XFLD: text for values shown for Ascending sort order
SortInAsc=จัดเรียงจากน้อยไปหามาก
#XFLD: text for values shown for Descending sort order
SortInDesc=จัดเรียงจากมากไปหาน้อย
#XFLD: Label for persisted view monitor heading
ViewMonitor_Heading=ตัวติดตามตรวจสอบมุมมอง
#XFLD: Label for persisted view monitor sub-heading
ViewMonitor_SubHeading=ติดตามตรวจสอบและปรับปรุงการคงอยู่ของข้อมูลสำหรับมุมมอง


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ DynamicPageHeader ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: text for values shown in column Persistence Status
txtLoading=กำลังโหลด
#XFLD: text for values shown in column Persistence Status
txtRunning=กำลังดำเนินการ
#XFLD: text for values shown in column Persistence Status
txtAvailable=พร้อมใช้งาน
#XFLD: text for values shown in column Persistence Status
txtError=ข้อผิดพลาด
#XMSG: Error message for unknown Persistence type that gets logged in Kibana
unknownReplicationTypeError=ประเภทการทำสำเนา "{0}" ไม่ได้รับการสนับสนุน

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Content ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=การกำหนดค่าที่ใช้สำหรับการดำเนินการคงอยู่ของข้อมูลครั้งล่าสุด:
#XMSG: Message for input parameter name
inputParameterLabel=พารามิเตอร์ป้อนข้อมูล
#XMSG: Message for input parameter value
inputParameterValueLabel=ค่า
#XMSG: Message for persisted data
inputParameterPersistedLabel=คงไว้เมื่อ
#XTIT: Count for View in ViewMonitor Toolbar
txtViewCount=มุมมอง ({0})
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=การคงอยู่ของมุมมอง
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=การคงอยู่ของข้อมูล
#XBUT: Button to clear the selected view persistance
ClearViewPersistance=ล้าง
#XBUT: Button to stop the selected view persistance
stopPersistance=หยุดการคงอยู่
#XFLD: Placeholder for Search field
txtSearch=ค้นหา
#XBUT: Tooltip for refresh button
txtRefresh=รีเฟรช
#XBUT: Tooltip for add view button
txtDeleteView=ลบการคงอยู่
#XBUT: Tooltip for load new peristence
loadNewPersistence=เริ่มต้นการคงอยู่ใหม่
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=โหลด Snapshot ใหม่
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=เริ่มต้นการคงอยู่ของข้อมูล
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=ย้ายข้อมูลที่คงไว้ออก
#XMSG: success message for starting persistence
startPersistenceSuccess=เรากำลังคงมุมมอง "{0}" ไว้
#XMSG: success message for stopping persistence
stopPersistenceSuccess=เรากำลังย้ายข้อมูลที่คงไว้สำหรับมุมมอง "{0}" ออก
#XMSG: success message for stopping persistence
deletePersistedViewSuccess=เรากำลังย้ายมุมมอง "{0}" ออกจากรายการติดตามตรวจสอบ
#XMSG: error message during start persistence of a view (backend error)
ERROR_STARTING_PERSISTENCE=มีข้อผิดพลาดเกิดขึ้นขณะเริ่มต้นการคงอยู่ของข้อมูลสำหรับมุมมอง "{0}"
#XMSG: error message during start persistence of a view (backend error)
PARAMERROR_STARTING_PERSISTENCE=ไม่สามารถคงมุมมอง "{0}" ไว้ได้เนื่องจากมีพารามิเตอร์ป้อนข้อมูล
#XMSG: error message during start persistence of a view (backend error)
NUMBER_OF_PARAMETER_STARTING_PERSISTENCE=ไม่สามารถคงมุมมอง "{0}" ไว้ได้เนื่องจากมีพารามิเตอร์ป้อนข้อมูลมากกว่าหนึ่งรายการ
#XMSG: error message during start persistence of a view (backend error)
PARAMETER_DEFAULT_VALUE_STARTING_PERSISTENCE=ไม่สามารถคงมุมมอง "{0}" ไว้ได้เนื่องจากพารามิเตอร์ป้อนข้อมูลไม่มีค่าตั้งต้น
#XMSG: error message during start persistence of a view (backend error)
REDEPLOYMENT_DAC_REQUIRED=จำเป็นต้องมีการปรับใช้การควบคุมการเข้าถึงข้อมูล (DAC) "{0}" อีกครั้งเพื่อสนับสนุนการคงอยู่ของข้อมูล
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_STARTING_PERSISTENCE=ไม่สามารถคงมุมมอง "{0}" ไว้ได้เนื่องจากใช้มุมมอง "{1}" ซึ่งมีการควบคุมการเข้าถึงข้อมูล (DAC)
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_EXTERNAL_STARTING_PERSISTENCE=ไม่สามารถคงมุมมอง "{0}" ไว้ได้เนื่องจากมุมมองนี้จะใช้มุมมองที่มีการควบคุมการเข้าถึงข้อมูล (DAC) ซึ่งเป็นของพื้นที่อื่น
#XMSG: error message during start persistence of a view (backend error)
DACERROR_STRUCTURE_STARTING_PERSISTENCE=ไม่สามารถคงมุมมอง "{0}" ไว้ได้เนื่องจากโครงสร้างของการควบคุมการเข้าถึงข้อมูล (DAC) อย่างน้อยหนึ่งรายการไม่รองรับการคงอยู่ของข้อมูล
#XMSG: error message during stop persistence of a view (backend error)
STOP_PERSISTENCE_ERROR=มีข้อผิดพลาดเกิดขึ้นขณะหยุดการคงอยู่สำหรับมุมมอง "{0}"
#XMSG: error message during stop persistence of a view (backend error)
DELETE_PERSISTED_VIEW_ERROR=มีข้อผิดพลาดเกิดขึ้นขณะลบมุมมองที่ถูกคงไว้ "{0}"
#XMSG: Warning message for removing the persisted data
RemovePersistency_Confirm_Msg=คุณต้องการลบข้อมูลที่คงไว้และสลับไปยังการเข้าถึงมุมมอง "{0}" แบบเสมือนหรือไม่?
#XMSG: Warning message for delete the view persistence
DeletePersistency_Confirm_Msg=คุณต้องการย้ายมุมมองออกจากรายการติดตามตรวจสอบและลบข้อมูลที่คงไว้สำหรับมุมมอง "{0}" ออกหรือไม่?
#XMSG: error message for reading data from backend
txtReadBackendError=ดูเหมือนว่ามีข้อผิดพลาดขณะอ่านจากแบคเอนด์
#XFLD: Label for No Data Error
NoDataError=ข้อผิดพลาด
#XMSG: message for conflicting task
Task_Already_Running=งานที่ขัดแย้งกันกำลังดำเนินการอยู่สำหรับมุมมอง "{0}"

#XFLD: Error while accessing pertitioning for a view
missingPrivilegeInViewPersistency=สิทธิไม่เพียงพอในการดำเนินการพาร์ทิชันสำหรับมุมมอง "{0}"

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Show All Views~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Text for show all views button
allViewsText=มุมมองทั้งหมด ({0})
#XBUT: Text for show scheduled views button
scheduledText=จัดกำหนดการแล้ว ({0})
#XBUT: Text for show persisted views button
persistedText=ถูกคงไว้ ({0})
#XBUT: Text for start analyzer button
startAnalyzer=เริ่มต้น View Analyzer
#XFLD: Message if repository is unavailable
repositoryErrorMsg=พื้นที่เก็บข้อมูลไม่พร้อมใช้งานและฟีเจอร์บางอย่างถูกปิดใช้งาน

#XFLD: Data Access - Virtual
Virtual=แบบเสมือน
#XFLD: Data Access - Persisted
Persisted=ถูกคงไว้

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Non-Persisted View Selection Dialog~~~~~~~~~~~~~~~~~~~~~~~

#XTIT: Title for Non-Persisted View selection dialog
select_source=เลือกมุมมองที่จะคงไว้

#XTIT: Placeholder for Non-Persisted View selection dialog search
searchView=ค้นหามุมมอง
#XTIT: No data in the list of non-persisted view
No_Data=ไม่มีข้อมูล
#XBUT: Button to select non-persisted view
ok=ตกลง
#XBUT: Button to close the non-persisted views selection dialog
cancel=ยกเลิก

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~View persistency log message key~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message for view persistency logs
START_STOP_PERSISTENCY=กำลังเริ่มต้นการดำเนินงานการคงอยู่ของข้อมูลสำหรับ "{1}"
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY=กำลังคงข้อมูลไว้สำหรับมุมมอง ''{1}''
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY)
START_VIEW_PERSISTENCY_PROCESS=กำลังเริ่มต้นกระบวนการเพื่อคงข้อมูลไว้สำหรับมุมมอง "{0}"
#XFLD: Message for view persistency manual partition load
START_VIEW_PERSISTENCY_PROCESS_MANUAL_PARTITIONS=กำลังเริ่มต้นกระบวนการเพื่อคงข้อมูลไว้สำหรับมุมมอง "{0}" ที่มี ID พาร์ทิชันที่เลือก: "{1}"
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY=กำลังย้ายข้อมูลที่ถูกคงไว้สำหรับมุมมอง "{1}" ออก
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY)
REMOVE_VIEW_PERSISTENCY=กำลังเริ่มต้นกระบวนการเพื่อย้ายข้อมูลที่คงไว้สำหรับมุมมอง "{0}" ออก
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_SUCCESS=ข้อมูลถูกคงไว้สำหรับมุมมอง "{1}"
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_SUCCESS)
START_VIEW_PERSISTENCY_PROCESS_SUCCESS=ข้อมูลถูกคงไว้สำหรับมุมมอง "{0}"
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY_SUCCESS=ย้ายข้อมูลที่คงไว้ออกและคืนค่าการเข้าถึงข้อมูลแบบเสมือนแล้วสำหรับมุมมอง "{1}"
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_SUCCESS)
REMOVE_VIEW_PERSISTENCY_SUCCESS=ดำเนินกระบวนการให้เสร็จสมบูรณ์เพื่อย้ายข้อมูลที่คงไว้สำหรับมุมมอง "{0}" ออก
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_FAILED=ไม่สามารถคงข้อมูลไว้สำหรับมุมมอง "{1}"
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_FAILED)
START_VIEW_PERSISTENCY_PROCESS_FAILED=ไม่สามารถคงข้อมูลไว้สำหรับมุมมอง "{0}"
#XFLD: Message for view persistency logs (for messages created before version 2022.05)
STOP_VIEW_PERSISTENCY_FAILED=ไม่สามารถย้ายข้อมูลที่คงไว้สำหรับมุมมอง "{1}" ออกได้
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_FAILED)
REMOVE_VIEW_PERSISTENCY_FAILED=ไม่สามารถย้ายข้อมูลที่คงไว้สำหรับมุมมอง "{0}" ออกได้
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_SUCCESS="{3}" เรคคอร์ดถูกคงไว้สำหรับมุมมอง "{1}"
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace START_PERSISTENCY_RECORD_COUNT_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS={0} เรคคอร์ดถูกแทรกในตารางการคงอยู่ของข้อมูลสำหรับมุมมอง "{1}"
#XFLD: Message used by SEAL repository for view persistency logs (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS_V2={0} เรคคอร์ดถูกแทรกในตารางการคงอยู่ของข้อมูลสำหรับมุมมอง "{1}" หน่วยความจำที่ใช้: {2} GiB
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_SUCCESS="{3}" เรคคอร์ดที่คงไว้ถูกย้ายออกแล้วสำหรับมุมมอง "{1}"
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace STOP_PERSISTENCY_RECORD_COUNT_SUCCESS)
REMOVE_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=ย้ายข้อมูลที่คงไว้ออกแล้ว, "{0}" เรคคอร์ดที่คงไว้ถูกลบ
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_FAILED=การดึงข้อมูล recordCount ล้มเหลวสำหรับมุมมอง "{1}"
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_FAILED=การดึงข้อมูล recordCount ล้มเหลวสำหรับมุมมอง "{1}"
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=ลบกำหนดการแล้วสำหรับ "{1}"
#XFLD: Message for view persistency logs (new message to replace STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS)
REMOVE_VIEW_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=ลบกำหนดการแล้วสำหรับมุมมอง "{0}"
#XFLD: Message for view persistency logs
STOP_PERSISTENCY_DELETE_SCHEDULE_FAILED=การลบกำหนดการล้มเหลวสำหรับ "{1}"
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_REDEPLOY=เราไม่สามารถคงมุมมอง "{0}" ไว้เนื่องจากมีการเปลี่ยนแปลงและปรับใช้ตั้งแต่คุณเริ่มคงไว้ กรุณาลองอีกครั้งเพื่อคงมุมมองไว้หรือรอจนถึงการดำเนินการตามกำหนดการครั้งถัดไป
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_DELETE=เราไม่สามารถคงมุมมอง "{0}" ไว้เนื่องจากถูกลบตั้งแต่คุณเริ่มคงไว้
#XFLD: Message for successful data persistency of a single partition (for messages created before version 2022.03)
START_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} เรคคอร์ดถูกคงไว้ในพาร์ทิชันสำหรับค่า "{1}" <= {2} < "{3}"
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (new message to replace START_PERSISTENCY_SINGLE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} เรคคอร์ดถูกแทรกในพาร์ทิชันสำหรับค่า "{1}" <= "{2}" < "{3}"
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS_V2={0} เรคคอร์ดถูกแทรกในพาร์ทิชันสำหรับค่า "{1}" <= "{2}" < "{3}" หน่วยความจำที่ใช้: {4} GiB
#XFLD: Message for successful replication of a "others" partition (for messages created before version 2022.03)
START_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} เรคคอร์ดถูกคงไว้ในพาร์ทิชัน "อื่นๆ"
#XFLD: Message used by SEAL repository for successful replication of a "others" partition (new message to replace START_PERSISTENCY_OTHERS_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} เรคคอร์ดถูกแทรกในพาร์ทิชัน "อื่นๆ"
#XFLD: Message for view persistency record count of partitions (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={3} เรคคอร์ดถูกคงไว้สำหรับมุมมอง "{1}" ใน {4} พาร์ทิชัน
#XFLD: Message used by SEAL repository for view persistency record count of partitions (new message to replace START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={0} เรคคอร์ดถูกแทรกในตารางการคงอยู่ของข้อมูลสำหรับมุมมอง "{1}" ในพาร์ทิชัน "{2}"
#XFLD: Message used by SEAL repository for view persistency for partition wise refresh
START_VIEW_PERSISTENCY_RECORD_COUNT_REFRESHED_PARTITIONS_SUCCESS={0} เรคคอร์ดถูกแทรกในตารางการคงอยู่ของข้อมูลสำหรับมุมมอง "{1}" พาร์ทิชันที่อัพเดท: {2} พาร์ทิชันที่ถูกล็อค: {3} พาร์ทิชันทั้งหมด: {4}
#XFLD: Message used by SEAL repository for view persistency for manual partition load
START_VIEW_PERSISTENCY_RECORD_COUNT_MANUAL_PARTITIONS_SUCCESS={0} เรคคอร์ดถูกแทรกในตารางการคงอยู่ของข้อมูลสำหรับมุมมอง "{1}" ใน {2} พาร์ทิชันที่เลือก
#XFLD: Message used by SEAL repository for view persistency for locked partition data load
START_VIEW_PERSISTENCY_RECORD_COUNT_LOCKED_PARTITIONS_SUCCESS={0} เรคคอร์ดถูกแทรกในตารางการคงอยู่ของข้อมูลสำหรับมุมมอง "{1}" พาร์ทิชันที่อัพเดท: {2} พาร์ทิชันที่ถูกล็อคและไม่มีการเปลี่ยนแปลง: {3} พาร์ทิชันทั้งหมด: {4}
#XMSG: Message for unexpected error (for messages created before version 2022.03)
START_PERSISTENCY_UNEXPECTED_ERROR=มีข้อผิดพลาดที่ไม่คาดคิดเกิดขึ้นขณะคงข้อมูลไว้สำหรับมุมมอง "{0}"
#XMSG: Message for unexpected error (new message to replace START_PERSISTENCY_UNEXPECTED_ERROR)
START_VIEW_PERSISTENCY_UNEXPECTED_ERROR=มีข้อผิดพลาดที่ไม่คาดคิดเกิดขึ้นขณะคงข้อมูลไว้สำหรับมุมมอง "{0}"
#XMSG: Message for Locked space error
START_VIEW_PERSISTENCY_LOCKED_SPACE_ERROR=ไม่สามารถคงตารางระยะไกล "{0}" ไว้ได้เนื่องจากพื้นที่ "{1}" ถูกล็อค
#XMSG: Message for unexpected error (for messages created before version 2022.03)
STOP_PERSISTENCY_UNEXPECTED_ERROR=มีข้อผิดพลาดที่ไม่คาดคิดเกิดขึ้นขณะย้ายข้อมูลที่คงไว้สำหรับมุมมอง "{0}" ออก
#XMSG: Message for unexpected error (new message to replace STOP_PERSISTENCY_UNEXPECTED_ERROR)
REMOVE_VIEW_PERSISTENCY_UNEXPECTED_ERROR=มีข้อผิดพลาดที่ไม่คาดคิดเกิดขึ้นขณะย้ายการคงอยู่สำหรับมุมมอง "{0}" ออก
#XMSG: Message for ivalidated view
VIEW_PERSISTENCY_ABORTED_DEFINITION_INVALID=ข้อกำหนดของมุมมอง "{0}" กลายเป็นไม่ถูกต้อง อาจเนื่องมาจากการเปลี่ยนแปลงของออบเจคที่ใช้ในมุมมองโดยตรงและโดยอ้อม กรุณาลองปรับใช้มุมมองใหม่เพื่อแก้ปัญหา หรือเพื่อระบุสาเหตุหลัก
#XMSG: Message for persisted data removal (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT=ข้อมูลที่คงไว้ถูกย้ายออกขณะปรับใช้มุมมอง ''{0}''
#XMSG: Message for persisted data removal of a consuming view
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW=ข้อมูลที่คงไว้ถูกย้ายออกขณะปรับใช้มุมมองที่ใช้ "{0}"
#XMSG: Message for persisted data removal of a consuming view due to a dac change
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW_DAC_CHANGED=ข้อมูลที่คงไว้ถูกย้ายออกขณะปรับใช้มุมมองที่ใช้ "{0}" เนื่องจากการควบคุมการเข้าถึงข้อมูลมีการเปลี่ยนแปลง
#XMSG: Message for persisted data removal (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DEPLOYMENT=ข้อมูลที่คงไว้ถูกย้ายออกขณะปรับใช้มุมมอง "{0}"
#XMSG: Message for deleted view (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION=การคงอยู่ถูกย้ายออกด้วยการลบมุมมอง "{0}"
#XMSG: Message for deleted view (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DELETION=การคงอยู่ถูกย้ายออกด้วยการลบมุมมอง "{0}"
#XMSG Message for removal of persisted data, because prerequisites not fulfilled
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_PREREQUISITES_NOT_FULFILLED=ข้อมูลที่คงไว้ถูกย้ายออกเนื่องจากไม่เป็นไปตามข้อกำหนดเบื้องต้นในการคงอยู่ของข้อมูล
#XMSG: Message for inconsistent persistency
VIEW_PERSISTENCY_INCONSISTENT_PERSISTENCY=การคงอยู่ของมุมมอง "{0}" ไม่สอดคล้องกัน ย้ายข้อมูลที่คงไว้ออกเพื่อแก้ไขปัญหา
#XMSG: Message for prerequisites
START_PERSISTENCY_CHECK_PREREQUISITES=กำลังตรวจสอบข้อกำหนดเบื้องต้นสำหรับการคงมุมมอง "{0}" ไว้
#XMSG: Message for DAC deployed in deprecated way using table function and join (for messages created before 2025.05)
START_PERSISTENCY_DEPRECIATED_DAC=มุมมอง "{0}" ถูกปรับใช้โดยใช้การควบคุมการเข้าถึงข้อมูล (DAC) ซึ่งกำลังจะเลิกใช้แล้ว กรุณาปรับใช้มุมมองอีกครั้งเพื่อปรับปรุงประสิทธิภาพ
#XMSG: Message for DAC deployed in deprecated way using table function and join
START_PERSISTENCY_DEPRECATED_DAC=มุมมอง "{0}" ถูกปรับใช้โดยใช้การควบคุมการเข้าถึงข้อมูล (DAC) ซึ่งกำลังจะเลิกใช้แล้ว กรุณาปรับใช้มุมมองอีกครั้งเพื่อปรับปรุงประสิทธิภาพ
#XMSG: Message for roll back (for messages created before version 2022.03)
ROLLBACK_VIEW_PERSISTENCY=มีข้อผิดพลาดเกิดขึ้น มีการคืนค่าสถานะการคงอยู่ก่อนหน้าสำหรับมุมมอง "{0}"
#XMSG: Message used by SEAL repository for rollback of running procedure to persist view (new message to replace ROLLBACK_VIEW_PERSISTENCY)
VIEW_PERSISTENCY_ROLLBACK=มีข้อผิดพลาดเกิดขึ้น กระบวนการในการคงมุมมอง ''{0}'' หยุดลงและการเปลี่ยนแปลงถูกย้อนกลับ
#XMSG: Message used by SEAL repository for rollback of running procedure to remove persisted data of view
REMOVE_VIEW_PERSISTENCY_ROLLBACK=มีข้อผิดพลาดเกิดขึ้น กระบวนการในการย้ายข้อมูลที่คงไว้สำหรับมุมมอง ''{0}'' ออกถูกหยุดและการเปลี่ยนแปลงถูกย้อนกลับ
#XMSG: Message used by SEAL repository for persistency preparation
START_PERSISTENCY_PREPARATION=กำลังจัดเตรียมการคงข้อมูลไว้
#XMSG: Message used by SEAL repository for data insertion
START_INSERTING_PERSISTENCY_DATA=กำลังแทรกข้อมูลในตารางการคงอยู่
#XMSG: Message for null values - partitioning (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS=เรคคอร์ดค่า Null "{0}" ถูกแทรกในพาร์ทิชัน "อื่นๆ"
#XMSG: Message used by SEAL repository for others partition values - partitioning (new message to replace START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS={0} เรคคอร์ดถูกแทรกในพาร์ทิชัน ''อื่นๆ'' สำหรับค่า "{2}" < "{1}" หรือ "{2}" >= "{3}"
#XMSG: Message used by SEAL repository for others partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS_V2={0} เรคคอร์ดถูกแทรกในพาร์ทิชัน "อื่นๆ" สำหรับค่า  "{2}" < "{1}" หรือ "{2}" >= "{3}" หน่วยความจำที่ใช้: {4} GiB
#XMSG: Message used by SEAL repository for null partition values - partitioning
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS={0} เรคคอร์ดถูกแทรกในพาร์ทิชัน "อื่นๆ" สำหรับค่า "{1}" เป็นค่า Null
#XMSG: Message used by SEAL repository for null partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS_V2={0} เรคคอร์ดถูกแทรกในพาร์ทิชัน "อื่นๆ" สำหรับค่า "{1}" เป็นค่า Null หน่วยความจำที่ใช้: {2} GiB
##XFLD:Message used by SEAL repository for remote statistics
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS=กำลังโหลดข้อมูลที่เกี่ยวข้อง: คำสั่งระยะไกล {0} รายการ ยอดรวมเรคคอร์ดที่ดึงข้อมูล: {1} ระยะเวลารวม: {2} วินาที
##XFLD:Message used by SEAL repository for remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS_WITH_PARTITIONS=กำลังโหลดข้อมูลที่เกี่ยวข้องโดยใช้ {0} พาร์ทิชันที่มีคำสั่งระยะไกล {1} รายการ ยอดรวมเรคคอร์ดที่ดึงข้อมูล: {2} ระยะเวลารวม: {3} วินาที
##XFLD:Message used by SEAL repository with UI ID for instructions of remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_INSTRUCTION=คำสั่งระยะไกลที่ทำระหว่างการดำเนินการสามารถแสดงได้โดยการเปิดตัวติดตามตรวจสอบคิวรีระยะไกลในรายละเอียดของข้อความเฉพาะพาร์ทิชัน
##XFLD:Message for start reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS=กำลังเริ่มต้นกระบวนการเพื่อนำข้อมูลที่คงไว้ที่มีอยู่สำหรับมุมมอง {0} กลับมาใช้ใหม่หลังจากการปรับใช้
##XFLD:Message used by SEAL repository for reattach view persitency
START_VIEW_PERSISTENCY_REATTACHMENT=เริ่มนำข้อมูลที่คงไว้ที่มีอยู่กลับมาใช้ใหม่
##XFLD:Message used by SEAL repository for reattach view persitency
START_FINILIZING_VIEW_PERSISTENCY_REATTACHMENT=กำลังนำข้อมูลที่คงไว้ที่มีอยู่กลับมาใช้ใหม่
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_COMPLETE=กระบวนการเพื่อนำข้อมูลที่คงไว้ที่มีอยู่กลับมาใช้ใหม่เสร็จสมบูรณ์สำหรับมุมมอง {0}
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_FAILED=ไม่สามารถนำข้อมูลที่คงไว้ที่มีอยู่สำหรับมุมมอง {0} กลับมาใช้ใหม่หลังจากการปรับใช้
#XMSG: Message used by SEAL repository for finalizing persistency
START_FINALIZING_PERSISTENCY=กำลังดำเนินการการคงอยู่ขั้นสุดท้าย
#XMSG: Message used by SEAL repository for restoring virtual access for view
REMOVE_VIEW_PERSISTENCY_VIRTUAL_ACCESS_RESTORED=การเข้าถึงข้อมูลแบบเสมือนถูกคืนค่าแล้วสำหรับมุมมอง ''{0}''
#XMSG: Message used by SEAL repository for view that already has virtual access
REMOVE_VIEW_PERSISTENCY_ALREADY_VIRTUAL_ACCESS=มุมมอง "{0}" มีการเข้าถึงข้อมูลแบบเสมือนแล้ว ไม่มีการย้ายข้อมูลที่คงไว้ออก
#XMSG: Message for removing view persistency
VIEW_PERSISTENCY_REMOVED_FROM_MONITOR=มุมมอง ''{0}'' ถูกย้ายออกจากตัวติดตามตรวจสอบมุมมอง
#XMSG: Message for view doesnt exist.
VIEW_PERSISTENCY_VIEW_NOT_EXISTING=มุมมอง "{0}" ไม่มีอยู่ในฐานข้อมูลหรือไม่ได้ปรับใช้อย่างถูกต้อง ดังนั้นจึงไม่สามารถคงอยู่ได้ ลองปรับใช้มุมมองใหม่เพื่อแก้ไขปัญหาหรือเพื่อระบุสาเหตุที่แท้จริง
#XMSG: Message for persistency not enabled.
VIEW_PERSISTENCY_NOT_ENABLED=การคงอยู่ของข้อมูลสำหรับมุมมองไม่ถูกเปิดใช้งาน กรุณาปรับใช้ตาราง/มุมมองในพื้นที่ "{0}" อีกครั้งเพื่อเปิดใช้งานฟังก์ชัน
#XMSG: Message for unexpected error due to node crash
VIEW_PERSISTENCY_UNEXPECTED_ERROR_GENERAL=การดำเนินการการคงอยู่ของมุมมองล่าสุดถูกขัดจังหวะเนื่องจากเกิดข้อผิดพลาดทางเทคนิค
#XFLD: Message for view persistency memory consumption
VIEW_PERSISTENCY_MEMORY_CONSUMPTION=หน่วยความจำสูงสุด {0} GiB ถูกใช้ในรันไทม์การคงอยู่ของมุมมอง
#XFLD: Message for view persistency timeout
VIEW_PERSISTENCY_TIMEOUT=หมดเวลาการคงอยู่ของมุมมอง {0} {1} ชั่วโมงแล้ว
#XFLD: Message for not started asynchronous view persistency
VIEW_PERSISTENCY_ASYNC_NOT_STARTED=การโหลดระบบจำนวนมากขัดขวางไม่ให้การดำเนินการแบบอะซิงโครนัสของการคงอยู่ของมุมมองเริ่มต้น ตรวจสอบว่ามีงานจำนวนมากเกินไปกำลังดำเนินการพร้อมกันหรือไม่
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency (for messages created before version 2022.05)
START_VIEW_PERSISTENCY_EXECUTION_METHOD_DROP_AND_RENAME=ตารางที่คงไว้ที่มีอยู่ถูกลบและแทนที่ด้วยตารางที่คงไว้ใหม่แล้ว
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_RECREATE_AND_DROP=ตารางที่คงไว้ที่มีอยู่ถูกลบและแทนที่ด้วยตารางที่คงไว้ใหม่แล้ว
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_TRUNCATE_AND_COPY=ตารางที่คงไว้ที่มีอยู่ได้รับการอัพเดทด้วยข้อมูลใหม่แล้ว
##XFLD: Message for missing authorizations for view persistency
VIEW_PERSISTENCY_MISSING_AUTH=ไม่มีสิทธิสำหรับการคงอยู่ของข้อมูล
##XFLD: Message start of persistency cancellation
VIEW_PERSISTENCY_TRIGGER_CANCEL=การเริ่มต้นยกเลิกกระบวนการเพื่อคงมุมมอง {0} ไว้
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_UNAVAILABLE_TASK_RUN=ไม่สามารถยกเลิกกระบวนการในการคงมุมมองไว้เนื่องจากไม่มีงานการคงอยู่ของข้อมูลที่กำลังดำเนินการสำหรับมุมมอง {0}
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_NONDETERMINISTIC_TASK_RUN=ไม่สามารถยกเลิกกระบวนการเพื่อคงมุมมองไว้เนื่องจากไม่มีงานการคงอยู่ของข้อมูลกำลังดำเนินการสำหรับมุมมอง {0}
##XFLD: Message for canceling a non running persitency task
VIEW_PERSISTENCY_CANCEL_NON_RUNNING_SELECTED_TASK=ไม่สามารถยกเลิกกระบวนการในการคงมุมมอง {0} ไว้เนื่องจากงานการคงอยู่ของข้อมูลที่เลือก {1} ไม่ได้ดำเนินการอยู่
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_UNREACHABLE_PERSISTENCY_RUN=ไม่สามารถยกเลิกกระบวนการเพื่อคงมุมมองไว้เนื่องจากยังไม่ได้เริ่มต้นการคงอยู่ของข้อมูลสำหรับมุมมอง {0}
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_COMPLETED_PERSISTENCY_RUN=ไม่สามารถยกเลิกกระบวนการเพื่อคงมุมมอง {0} ไว้เนื่องจากเนื่องจากเสร็จสมบูรณ์แล้ว
##XFLD: Message for failed cancellation
VIEW_PERSISTENCY_ABORTED_CANCEL_EXECUTION=ไม่สามารถยกเลิกกระบวนการเพื่อคงมุมมอง {0} ไว้
##XFLD: Message for finalizing persistency cancellation
VIEW_PERSISTENCY_FINALIZE_CANCEL=กระบวนการในการหยุดการคงอยู่ของข้อมูลสำหรับมุมมอง {0} ถูกส่งแล้ว
##XFLD: Message used in persist task when cancellation is caught
VIEW_PERSISTENCY_CANCELED=กระบวนการเพื่อคงมุมมอง {0} ไว้ถูกหยุด
##XFLD: Message used in persist task when cancelation is caught and performed through a cancelation task
VIEW_PERSISTENCY_CANCELED_VIA_CANCEL_TASK=กระบวนการเพื่อคงมุมมอง {0} ไว้ถูกหยุดผ่านงานการยกเลิก {1}
##XFLD: Message used in cancel task when the task is triggered by deployment
VIEW_PERSISTENCY_CANCEL_TRIGGERED_BY_DEPLOYMENT=กำลังยกเลิกกระบวนการเพื่อคงข้อมูลไว้ขณะปรับใช้มุมมอง {0}
##XFLD: Message used in case the cancelation is retriggered for a running task
VIEW_PERSISTENCY_CANCEL_ATTEMPT_RETRIGGER_CANCEL=งานการยกเลิกก่อนหน้าเพื่อคงมุมมอง {0} ไว้ถูกส่งแล้ว
##XFLD: Message used to express the delay that may occur before the persistency task is stopped
VIEW_PERSISTENCY_CANCEL_DELAY_STOPPING_PERSISTENCY=อาจมีความล่าช้าจนกว่างานการคงอยู่ของข้อมูลสำหรับมุมมอง {0} หยุดลง
##XFLD: Message used to reflect the task log identifier of the current running persistency task
VIEW_PERSISTENCY_CANCEL_CURRENT_RUNNING_TASK=กำลังคงข้อมูลสำหรับมุมมอง {0} ไว้ด้วยงาน {1}
##XFLD:Message used by SEAL repository for locked partitions and DACs
VIEW_PERSISTENCY_LOCKED_PARTITIONS_DACS=สิทธิที่มีให้โดย DAC อาจมีการเปลี่ยนแปลงและไม่ได้รับการพิจารณาโดยพาร์ทิชันที่ถูกล็อค กรุณาปลดล็อคพาร์ทิชันและโหลด Snapshot ใหม่เพื่อนำการเปลี่ยนแปลงไปใช้
##XFLD:Message used by SEAL repository for column structure change
VIEW_PERSISTENCY_COLUMN_STRUCTURE_CHANGE=โครงสร้างคอลัมน์มีการเปลี่ยนแปลงและไม่ตรงกับตารางการคงอยู่ที่มีอยู่อีกต่อไป ย้ายข้อมูลที่มีอยู่ออกและเริ่มต้นการคงอยู่ของข้อมูลใหม่เพื่ออัพเดทตารางการคงอยู่ของคุณ
##XFLD: Message for out of memory
VIEW_PERSISTENCY_HANA_OUT_OF_MEMORY=งานล้มเหลวเนื่องจากเกิดข้อผิดพลาดหน่วยความจำไม่เพียงพอบนฐานข้อมูล SAP HANA
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_EXCEPTION_TRANSACTION_ROLLBACK=งานล้มเหลวเนื่องจากเกิดข้อยกเว้นภายในบนฐานข้อมูล SAP HANA
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_SQL_EXECUTION_ISSUE=งานล้มเหลวเนื่องจากเกิดปัญหาการดำเนินการ SQL ภายในบนฐานข้อมูล SAP HANA
##XFLD: Message for HANA out of memory result
VIEW_PERSISTENCY_OUT_OF_MEMORY_RESULT_MESSAGE=เหตุผลสำหรับเหตุการณ์หน่วยความจำไม่เพียงพอใน HANA: {0}
##XFLD: Message for admission control rejection
VIEW_PERSISTENCY_HANA_ADMISSION_CTRL_REJECTION=งานล้มเหลวเนื่องจากการปฏิเสธการควบคุม SAP HANA Admission
##XFLD: Message for HANA back pressure / rate limit of HANA client
VIEW_PERSISTENCY_HANA_CLIENT_RATE_LIMIT_REJECTION=งานล้มเหลวเนื่องจากมีการเชื่อมต่อ SAP HANA ที่ใช้งานอยู่มากเกินไป
#XMSG: Message for invalid persisted table name
VIEW_PERSISTENCY_PERSISTED_TABLE_INVALID=มีข้อผิดพลาดเกิดขึ้นและตารางที่คงไว้ไม่สามารถใช้ได้ เมื่อต้องการแก้ปัญหานี้ กรุณาลบข้อมูลที่คงไว้ออกและคงมุมมองไว้อีกครั้ง
#XMSG: Message for usahe of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION=ไม่สามารถคงมุมมองไว้ได้เนื่องจากมีการใช้ตารางระยะไกลจากแหล่งข้อมูลระยะไกลที่มีการเปิดใช้งานการเผยแพร่ผู้ใช้ กรุณาตรวจสอบสายข้อมูลของมุมมอง
#XMSG: Message for dynamic usage of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION_DYNAMIC=ไม่สามารถคงมุมมองไว้ได้เนื่องจากมีการใช้ตารางระยะไกลจากแหล่งข้อมูลระยะไกลที่มีการเปิดใช้งานการเผยแพร่ผู้ใช้ ตารางระยะไกลอาจถูกใช้แบบไดนามิกผ่านมุมมองสคริปต์ SQL สายข้อมูลของมุมมองอาจไม่แสดงตารางระยะไกล
#XMSG: Message for insufficient privileges or consumption of view with DAC via dynamic SQL
VIEW_PERSISTENCY_INSUF_PRIV_OR_DAC_DYN_SQL=สิทธิพิเศษของคุณอาจไม่เพียงพอ เปิดการแสดงตัวอย่างข้อมูลเพื่อดูว่าคุณมีสิทธิพิเศษที่จำเป็นหรือไม่ หากใช่ มุมมองที่สองที่ใช้ผ่านสคริปต์ SQL แบบไดนามิกอาจมีการนำการควบคุมการเข้าถึงข้อมูล (DAC) มาใช้
#XMSG: deployment of DAC is deprecated and view need to be redeployed to be able to persist data
VIEW_PERSISTENCY_DEPRECATED_DAC=มุมมอง "{0}" ถูกปรับใช้โดยใช้การควบคุมการเข้าถึงข้อมูล (DAC) ซึ่งกำลังจะเลิกใช้แล้ว กรุณาปรับใช้มุมมองอีกครั้งเพื่อให้สามารถคงข้อมูลสำหรับมุมมอง
##XFLD:Message used by SEAL repository for input parameter
START_VIEW_PERSISTENCY_INPUT_PARAMETER=การใช้ค่าตั้งต้น "{0}" สำหรับพารามิเตอร์ป้อนข้อมูล "{1}"
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Elastic compute node keys~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
##XFLD:Message used to reflect disablement of ecn replica
ECN_DISABLE_EXISTING_REPLICA=แบบจำลองโหนดการคำนวณแบบยืดหยุ่นถูกปิดใช้งาน
##XFLD:Message used to reflect recreation of ecn replica
ECN_RECREATE_EXISTING_REPLICA=แบบจำลองโหนดการคำนวณแบบยืดหยุ่นถูกสร้างขึ้นใหม่
##XFLD:Message used to reflect re-enablement of ecn replica
ECN_ENABLE_EXISTING_REPLICA=แบบจำลองโหนดการคำนวณแบบยืดหยุ่นถูกเปิดใช้งานอีกครั้ง
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Task Schedule Menu key~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message to schedule task
scheduleText=กำหนดการ
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=สร้างกำหนดการ
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=แก้ไขกำหนดการ
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=ลบกำหนดการ
#XFLD: Refresh frequency field
refreshFrequency=ความถี่ในการรีเฟรช
#XFLD: Refresh frequency field
refreshFrequencyNew=ความถี่
#XFLD: Refresh frequency field
refreshFrequencyNewNew=ความถี่ตามกำหนดการ
#XBUT: label for None
none=ไม่มี
#XBUT: label for Real-Time replication state
realtime=เวลาจริง
#XFLD: Label for table column
txtNextSchedule=การดำเนินการครั้งถัดไป
#XFLD: Label for table column
txtNextScheduleNew=การดำเนินการตามกำหนดการครั้งถัดไป
#XFLD: Label for table column
txtNumOfRecords=จำนวนเรคคอร์ด
#XFLD: Label for scheduled link
scheduledTxt=จัดกำหนดการแล้ว
#XFLD: LABEL for partially persisted link
partiallyPersisted=คงไว้บางส่วน
#XFLD: Text for paused text
paused=ถูกหยุดชั่วคราว

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ View Monitor Tasklog Details Action keys~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message for release lock actionn
releaseLockMsg=หากการดำเนินการใช้เวลานานกว่าปกติ อาจบ่งชี้ว่าการดำเนินการนี้ล้มเหลวและไม่ได้อัพเดทสถานะให้สอดคล้องกัน \r\n เมื่อต้องการแก้ปัญหานี้ คุณสามารถปลดล็อคแล้วกำหนดสถานะเป็นล้มเหลวได้
#XFLD: Label for release lock dialog
releaseLockText=ปลดล็อค

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Column header tooltip ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: tooltip for table column
txtViewTooltip=ชื่อมุมมองที่ถูกคงไว้
#XFLD: tooltip for table column
txtViewDataAccessTooltip=แสดงความพร้อมใช้งานของมุมมอง
#XFLD: tooltip for table column
txtViewRefreshFreqTooltip=ซึ่งบ่งชี้ว่ามีการระบุกำหนดการสำหรับมุมมองหรือไม่
#XFLD: tooltip for table column
txtViewStatusTooltip=รับสถานะของมุมมองที่ถูกคงไว้
#XFLD: tooltip for table column
txtViewLatestUpdateTooltip=ให้ข้อมูลเกี่ยวกับเวลาที่อัพเดทครั้งล่าสุดของมุมมองที่ถูกคงไว้
#XFLD: tooltip for table column
txtViewNextRunTooltip=หากมีการตั้งค่ากำหนดการสำหรับมุมมอง กรุณาดูว่าการดำเนินการครั้งถัดไปถูกจัดกำหนดเวลาไว้เมื่อใด
#XFLD: tooltip for table column
txtViewNumOfRecordsTooltip=ติดตามจำนวนเรคคอร์ด
#XFLD: tooltip for table column
txtViewInMemorySizeTooltip=ติดตามขนาดของมุมมองที่ใช้ในหน่วยความจำของคุณ
#XFLD: tooltip for table column
txtViewDiskSizeTooltip=ติดตามขนาดของมุมมองที่ใช้บนดิสก์ของคุณ
#XMSG: Expired text
txtExpired=หมดอายุแล้ว

#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=ไม่สามารถเพิ่มออบเจค "{0}" ในเชนงาน

#~~~~~~~~~~~View Analyzer task log messages~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message indicating number of records for a view, and memory consumption in Mebibyte during simulation of view persistency
RECORDS_PEAK_MEMORY=มุมมอง "{0}" มีเรคคอร์ด {1} รายการ การจำลองการคงอยู่ของข้อมูลสำหรับมุมมองใช้หน่วยความจำ {2} MiB
#XMSG: View Analyzer execution failure
VIEW_ANALYZER_EXECUTION_FAILED=การดำเนินการ View Analyzer ล้มเหลว
#XMSG: Missing authorizations for View Analyzer
VIEW_ANALYZER_MISSING_AUTH=ไม่มีสิทธิสำหรับ View Analyzer
#XMSG: Maximum memory in Gibibyte is reached, therefore persistency simulation is stopped
MAXIMUM_MEMORY_REACHED=ใช้หน่วยความจำถึงจำนวนสูงสุด {0} GiB แล้วขณะจำลองการคงอยู่สำหรับมุมมอง "{1}" ดังนั้นจึงไม่มีการจำลองการคงอยู่เพิ่มเติมสำหรับมุมมอง
#XMSG: Error during persistency simulation occurred
PERSISTENCY_SIMULATION_ERROR=มีข้อผิดพลาดเกิดขึ้นระหว่างการจำลองการคงอยู่ของข้อมูลสำหรับมุมมอง "{0}"
#XMSG: Persistency simulation is not executed, because prerequisites are not fulfilled and the view cannot be persisted
PREREQUISITES_NOT_FULFILLED=ไม่ได้ดำเนินการจำลองการคงอยู่ของข้อมูลสำหรับมุมมอง "{0}" เนื่องจากไม่ตรงตามข้อกำหนดเบื้องต้นและไม่สามารถคงมุมมองไว้
#XMSG: Persistency simulation is not executed, because the view does not exist in the database
VIEW_DOES_NOT_EXIST=คุณต้องปรับใช้มุมมอง "{0}" เพื่อเปิดใช้งานการจำลองการคงอยู่ของข้อมูล
#XMSG: Number of records cannot be determined, because the local table does not exist in the database
TABLE_DOES_NOT_EXIST=ไม่มีตารางภายใน "{0}" ในฐานข้อมูล ดังนั้นจึงไม่สามารถกำหนดจำนวนเรคคอร์ดสำหรับตารางนี้
##XMSG: Message start of cancellation for View Analyzer task
CANCEL_VIEW_ANALYZER_TRIGGERED=กระบวนการในการหยุดงาน View Analyzer {0} สำหรับมุมมอง ''{1}'' ถูกส่งแล้ว ซึ่งอาจมีความล่าช้าจนกว่างานจะหยุดลง
##XMSG: Message start to indicate that task is not active
CANCEL_VIEW_ANALYZER_NO_ACTIVE_TASK=งาน View Analyzer {0} สำหรับมุมมอง ''{1}'' ใช้งานไม่ได้
#XMSG: Cancel View Analyzer task failure
CANCEL_VIEW_ANALYZER_FAILED=ไม่สามารถยกเลิกงาน View Analyzer
##XMSG: Message used in View Analyzer execution task when task is canceled
VIEW_ANALYZER_CANCELED=การดำเนินการ View Analyzer สำหรับมุมมอง "{0}" ถูกหยุดผ่านงานการยกเลิก
##XMSG: Message start of cancelation for Model Validation task
CANCEL_MODEL_VALIDATION_TRIGGERED=กระบวนการในการหยุดงานการตรวจสอบความถูกต้องของโมเดล {0} สำหรับมุมมอง "{1}" ถูกส่งแล้ว ซึ่งอาจมีความล่าช้าจนกว่างานจะหยุดลง
##XMSG: Message to indicate that task is not active
CANCEL_MODEL_VALIDATION_NO_ACTIVE_TASK=งานการตรวจสอบความถูกต้องของโมเดล {0} สำหรับมุมมอง "{1}" ใช้งานไม่ได้
#XMSG: Cancel Model Validation task failure
CANCEL_MODEL_VALIDATION_FAILED=ไม่สามารถยกเลิกงานการตรวจสอบความถูกต้องของโมเดล
##XMSG: Message used in Model Validation task when task is canceled
MODEL_VALIDATION_CANCELED=การดำเนินการตรวจสอบความถูกต้องของโมเดลสำหรับมุมมอง "{0}" ถูกหยุดผ่านงานการยกเลิก
#XMSG: Message for Locked space error, before starting Model Validation task
MODEL_VALIDATION_LOCKED_SPACE_ERROR=ไม่สามารถดำเนินการตรวจสอบความถูกต้องของโมเดลสำหรับมุมมอง "{0}" เนื่องจากพื้นที่ "{1}" ถูกล็อค
##XMSG: Message used in View Analyzer execution task when determination of number of rows fails
NUMBER_ROWS_DETERMINATION_FAILED=มีข้อผิดพลาดเกิดขึ้นขณะกำหนดจำนวนแถวสำหรับตารางภายใน "{0}"
##XMSG: Message to indicate that SQL plan was created
EXECUTION_PLAN_CREATED=ไฟล์แผน SQL Analyzer สำหรับมุมมอง ''{0}'' ถูกสร้างขึ้นและสามารถดาวน์โหลดได้
#XFLD: Message to indicate starting of process to generate SQL Analyzer plan file
START_PLANVIZ_EXECUTION=กำลังเริ่มต้นกระบวนการในการสร้างไฟล์แผน SQL Analyzer สำหรับมุมมอง "{0}"
#XFLD: Message to indicate starting of View Analyzer process
VIEW_ANALYZER_EXECUTION_STARTED=กำลังเริ่มต้นการดำเนินการ View Analyzer
##XMSG: Message to indicate that SQL plan file cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_PLANVIZ_NOT_FULFILLED=ไม่สามารถสร้างไฟล์แผน SQL Analyzer สำหรับมุมมอง ''{0}'' เนื่องจากไม่ตรงตามข้อกำหนดเบื้องต้นของการคงอยู่ของข้อมูล
##XMSG: Error message during  generation of SQL Analyzer plan file
PLANVIZ_ERROR=มีข้อผิดพลาดเกิดขึ้นระหว่างการสร้างไฟล์แผน SQL Analyzer สำหรับมุมมอง ''{0}''
#XMSG: Message for Locked space error
VIEW_ANALYZER_LOCKED_SPACE_ERROR=ไม่สามารถดำเนินการ View Analyzer สำหรับมุมมอง ''{0}'' เนื่องจากพื้นที่ ''{1}'' ถูกล็อค
#XMSG: Message for persistency simulation without partitioning
PERSISTENCY_SIMULATION_WITHOUT_PARTITIONS=พาร์ทิชันของมุมมอง ''{0}'' ไม่ถูกพิจารณาระหว่างการจำลองการคงอยู่ของข้อมูล
#XMSG: Message for generation of SQL Analyzer plan file without considering partitioning
PLANVIZ_WITHOUT_PARTITIONS=พาร์ทิชันของมุมมอง ''{0}'' ไม่ถูกพิจารณาระหว่างการสร้างไฟล์แผน SQL Analyzer
#XMSG: Confirmation message for mass removal of persisted data
MassRemovePersistency_Confirm_Msg=คุณต้องการย้ายข้อมูลที่คงไว้ออกและสลับการเข้าถึงข้อมูลกลับเป็นการเข้าถึงแบบเสมือนหรือไม่?
#XMSG: Confirmation message for partial mass removal of selected persisted data
PartialMassRemovePersistency_Confirm_Msg=มุมมองที่เลือก {0} จาก {1} มุมมองมีข้อมูลที่คงไว้ \n คุณต้องการย้ายข้อมูลที่คงไว้ออกและสลับการเข้าถึงข้อมูลกลับเป็นการเข้าถึงแบบเสมือนหรือไม่?
#XMSG: success message for stopping mass persistence
stopMassPersistenceSuccess=เรากำลังย้ายข้อมูลที่คงไว้ออกสำหรับมุมมองที่เลือก
#XMSG: error message during mass stop persistence of a view (backend error)
STOP_MASS_PERSISTENCE_ERROR=มีข้อผิดพลาดเกิดขึ้นขณะหยุดการคงอยู่ของมุมมองที่เลือก
#XMSG: memory analysis is only performed in space {0}
NO_MEMORY_ANALYSIS=จะมีการดำเนินการวิเคราะห์หน่วยความจำสำหรับเอนทิตี้ในพื้นที่ "{0}" เท่านั้น: "{1}" "{2}" จะถูกข้ามไป
##XMSG: Message to indicate that Explain Plan cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_EXPLAINPLAN_NOT_FULFILLED=ไม่สามารถสร้างไฟล์แผนการอธิบายสำหรับมุมมอง "{0}" ได้เนื่องจากไม่เป็นไปตามข้อกำหนดเบื้องต้นของการคงอยู่
#XMSG: Message for Explain Plan generation without partitioning
EXPLAINPLAN_WITHOUT_PARTITIONS=พาร์ทิชันของมุมมอง "{0}" จะไม่ได้รับการพิจารณาระหว่างการสร้างไฟล์แผนการอธิบาย
#XFLD: Message to indicate starting of process to generate Explain Plan file
START_EXPLAINPLAN_EXECUTION=กำลังเริ่มต้นกระบวนการในการสร้างไฟล์แผนการอธิบายสำหรับมุมมอง "{0}"
##XMSG: Message to indicate that Explain Plan was created
EXPLAINPLAN_CREATED=สร้างไฟล์แผนการอธิบายสำหรับมุมมอง "{0}" แล้ว คุณสามารถแสดงไฟล์ดังกล่าวได้โดยการคลิก "ดูรายละเอียด" หรือดาวน์โหลดหากคุณมีสิทธิที่เกี่ยวข้อง
##XMSG: Error message during  generation of Explain Plan file
EXPLAINPLAN_ERROR=มีข้อผิดพลาดเกิดขึ้นระหว่างการสร้างไฟล์แผนการอธิบายสำหรับมุมมอง "{0}"
##XMSG: Explain Plan file cannot be generated for the view {0}, because too many views are stacked on each other
EXPLAINPLAN_MODEL_TOO_COMPLEX=ไม่สามารถสร้างไฟล์แผนการอธิบายสำหรับมุมมอง "{0}" ได้ มีมุมมองเรียงซ้อนกันมากเกินไป โมเดลที่ซับซ้อนดังกล่าวอาจทำให้เกิดข้อผิดพลาดหน่วยความจำไม่เพียงพอและประสิทธิภาพการทำงานช้าลง เราขอแนะนำให้คงมุมมองไว้

##XMSG: Message to indicate that the space is locked
PERFORMANCE_ANALYSIS_LOCKED_SPACE_ERROR=ไม่สามารถดำเนินการวิเคราะห์ประสิทธิภาพสำหรับมุมมอง "{0}" เนื่องจากพื้นที่ "{1}" ถูกล็อค
##XMSG: The performance analysis task was cancelled
PERFORMANCE_ANALYSIS_CANCELED=การวิเคราะห์ประสิทธิภาพสำหรับมุมมอง "{0}" ถูกยกเลิก
##XMSG: The performance analysis failed
PERFORMANCE_ANALYSIS_FAILED=การวิเคราะห์ประสิทธิภาพล้มเหลว
##XMSG: The performance analysis is finished
PERFORMANCE_ANALYSIS_FINISHED=การวิเคราะห์ประสิทธิภาพสำหรับมุมมอง "{0}" เสร็จสิ้นแล้ว แสดงผลลัพธ์โดยการคลิก "ดูรายละเอียด"
##XMSG: The view has input parameter with no default value
INPUT_PARAMETER_WITHOUT_DEFAULT_VALUE=ไม่สามารถวิเคราะห์มุมมองนี้ได้เนื่องจากมีพารามิเตอร์ที่ไม่มีค่าตั้งต้น
##XMSG: The view is not fully deployed
VIEW_NOT_FULLY_DEPLOYED=ไม่สามารถวิเคราะห์มุมมองนี้ได้เนื่องจากไม่ได้ถูกปรับใช้อย่างสมบูรณ์
##XMSG: The view uses limited remote adapters
LIMITED_REMOTE_ADAPTER=มุมมองนี้ใช้อะแดปเตอร์ระยะไกลอย่างน้อยหนึ่งตัวที่มีความสามารถจำกัด เช่น ไม่มี Pushdown ของฟิลเตอร์หรือไม่รองรับ 'จำนวน' การคงไว้หรือการทำสำเนาออบเจคสามารถปรับปรุงประสิทธิภาพของรันไทม์ได้
##XMSG: The view uses remote adapter that does not support 'Limit'
REMOTE_ADAPTER_NOT_SUPPORTING_LIMIT=มุมมองนี้ใช้อะแดปเตอร์ระยะไกลอย่างน้อยหนึ่งตัวที่ไม่รองรับ 'ขีดจำกัด' อาจมีการเลือกเรคคอร์ดมากกว่า 1,000 รายการ
##XMSG: Performance analysis is executed by using default parameters of view'
INPUT_PARAMETER_WITH_DEFAULT_VALUE=การวิเคราะห์ประสิทธิภาพจะดำเนินการโดยใช้ค่าตั้งต้นของพารามิเตอร์มุมมอง
##XMSG: Message to indicate that an error occurred
PERFORMANCE_ANALYSIS_ERROR=มีข้อผิดพลาดเกิดขึ้นระหว่างการวิเคราะห์ประสิทธิภาพสำหรับมุมมอง "{0}"
##XMSG: Message start of cancellation for Performance Analysis task
CANCEL_PERFORMANCE_ANALYSIS_TRIGGERED=กระบวนการในการหยุดงานการวิเคราะห์ประสิทธิภาพ {0} สำหรับมุมมอง "{1}" ถูกส่งแล้ว ซึ่งอาจมีความล่าช้าจนกว่างานจะหยุดลง
##XMSG: Message start to indicate that task is not active
CANCEL_PERFORMANCE_ANALYSIS_NO_ACTIVE_TASK=งานการวิเคราะห์ประสิทธิภาพ {0} สำหรับมุมมอง "{1}" ใช้งานไม่ได้
#XMSG: Cancel Performance Analysis task failure
CANCEL_PERFORMANCE_ANALYSIS_FAILED=ไม่สามารถยกเลิกงานการวิเคราะห์ประสิทธิภาพ

#XBUT: Assign schedule menu button label
assignScheduleLabel=กำหนดกำหนดการให้กับฉัน
#XBUT: Pause schedule menu label
pauseScheduleLabel=หยุดกำหนดการชั่วคราว
#XBUT: Resume schedule menu label
resumeScheduleLabel=ดำเนินการกำหนดการต่อ
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=มีข้อผิดพลาดเกิดขึ้นขณะย้ายกำหนดการออก
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=มีข้อผิดพลาดเกิดขึ้นขณะกำหนดกำหนดการ
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=มีข้อผิดพลาดเกิดขึ้นขณะหยุดกำหนดการชั่วคราว
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=มีข้อผิดพลาดเกิดขึ้นขณะดำเนินการกำหนดการต่อ
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=กำลังลบ {0} กำหนดการ
#XMSG: Message for starting mass assign of schedules
massAssignStarted=กำลังเปลี่ยนแปลงเจ้าของ {0} กำหนดการ
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=กำลังหยุด {0} กำหนดการชั่วคราว
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=กำลังดำเนินการ {0} กำหนดการต่อ
#XBUT: Select Columns Button
selectColumnsBtn=เลือกคอลัมน์
#XFLD: Refresh tooltip
TEXT_REFRESH=รีเฟรช
#XFLD: Select Columns tooltip
text_selectColumns=เลือกคอลัมน์


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Runtime Metrics ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Label for Runtime Metrics
RuntimeMetricsTitle=เมตริกขณะรันไทม์สำหรับ
#XFLD : Label for Run Button
runButton=ดำเนินการ
#XFLD : Label for Cancel Button
cancelButton=ยกเลิก
#XFLD : Label for Close Button
closeButton=ปิด
#XFLD : Label for Open View Analyzer Button
openViewAnalyzerButton=เปิดตัววิเคราะห์มุมมอง
#XFLD : Label for Generate Explain Plan Button
generateExplainPlanButton=สร้างแผนการอธิบาย
#XFLD : Label for Previous Run Column
previousRun=การดำเนินการก่อนหน้า
#XFLD : Label for Latest Run Column
latestRun=การดำเนินการล่าสุด
#XFLD : Label for time Column
time=เวลา
#XFLD : Label for Duration Column
duration=ระยะเวลา
#XFLD : Label for Peak Memory Column
peakMemory=หน่วยความจำสูงสุด
#XFLD : Label for Number of Rows
numberOfRows=จำนวนแถว
#XFLD : Label for Overall Number of Sources
overallNumberOfSources=จำนวนแหล่งข้อมูลทั้งหมด
#XFLD : Label for Data Access Column
dataAccess=การเข้าถึงข้อมูล
#XFLD : Label for Local Tables
localTables=ตารางภายใน
#XFLD : Label for federatedRemoteTables
federatedRemoteTables=ตารางระยะไกลแบบรวม (ที่มีความสามารถของอะแดปเตอร์แบบจำกัด)
#XTXT Text for initial state of the runtime metrics
initialState=คุณต้องดำเนินการวิเคราะห์ประสิทธิภาพก่อนจึงจะดึงข้อมูลเมตริกได้ การดำเนินการนี้อาจใช้เวลาสักครู่ แต่คุณสามารถยกเลิกกระบวนการได้หากจำเป็น
#XMSG: Cancel confirmation message
CancelRunConfirmationMessage=คุณแน่ใจหรือไม่ว่าต้องการยกเลิกการดำเนินการวิเคราะห์ประสิทธิภาพในปัจจุบัน?
#XTIT: Cancel dialog title
CancelRunTitle=ยกเลิกการดำเนินการ
#XFLD: Label for Number of Rows
NUMBER_ROWS=จำนวนแถว
#XFLD: Label for Overall Number of Sources
OVERALL_NUMBER_SOURCES=จำนวนแหล่งข้อมูลทั้งหมด
#XFLD: Label for Data Access
DATA_ACCESS=การเข้าถึงข้อมูล
#XFLD: Label for Remote tables
NUMBER_REMOTE_TABLES_LIMITED_ADAPTERS=ตารางระยะไกลแบบรวม (ที่มีความสามารถของอะแดปเตอร์แบบจำกัด)
#XFLD: Label for select statement
SELECT_STATEMENT='SELECT * FROM VIEW LIMIT 1000'
#XFLD: Label for duration
SELECT_RUNTIME=ระยะเวลา
#XFLD: Label for peak memory
SELECT_PEAK_MEMORY=หน่วยความจำสูงสุด
#XFLD: Label for count statement
SELECT_COUNT_STATEMENT='SELECT COUNT(*) FROM VIEW'
#XFLD: Label for count duration
SELECT_COUNT_RUNTIME=ระยะเวลา
#XFLD: Label for count peak memory
SELECT_COUNT_PEAK_MEMORY=หน่วยความจำสูงสุด
#XFLD: Label for count of number of local tables
NUMBER_LOCAL_TABLES_FILE=ตารางภายใน (ไฟล์)
#XTXT: Text for running state of the runtime metrics
Running=กำลังดำเนินการ...
#XFLD: Label for time
Time=เวลา
#XFLD: Label for virtual access
PA_VIRTUAL=แบบเสมือน
#XFLD: Label for persisted access
PA_PERSISTED=ถูกคงไว้
PA_PARTIALLY_PERSISTED=คงไว้บางส่วน
#XTXT: Text for cancel
CancelRunSuccessMessage=กำลังยกเลิกการดำเนินการวิเคราะห์ประสิทธิภาพ
#XTXT: Text for cancel error
CancelRunErrorMessage=มีข้อผิดพลาดเกิดขึ้นขณะยกเลิกการดำเนินการวิเคราะห์ประสิทธิภาพ
#XTXT: Text for explain plan generation
ExplainPlanStarted=กำลังสร้างแผนการอธิบายสำหรับมุมมอง "{0}"
#XTXT: Text for performance analysis
PerformanceAnalysisStarted=กำลังเริ่มต้นการวิเคราะห์ประสิทธิภาพสำหรับมุมมอง "{0}"
#XTXT: Text for performance analysis error
ErrorFetchingPerformanceAnalysis=มีข้อผิดพลาดเกิดขึ้นขณะดึงข้อมูลการวิเคราะห์ประสิทธิภาพ
#XTXT: Text for performance analysis error
conflictingTask=งานการวิเคราะห์ประสิทธิภาพกำลังดำเนินการอยู่
#XFLD: Label for Errors
Errors=ข้อผิดพลาด
#XFLD: Label for Warnings
Warnings=คำเตือน
#XFLD: Tooltip for missing privileges
openViewAnalyzerButtonTooltipNoPrivilege=คุณต้องมีสิทธิพิเศษ DWC_DATAINTEGRATION(update) ในการเปิด View Analyzer
#XFLD: Tooltip for missing privileges
generateExplainPlanButtonTooltipNoPrivilege=คุณต้องมีสิทธิพิเศษ DWC_RUNTIME(read) ในการสร้างแผนการอธิบาย



#XFLD: Label for frequency column
everyLabel=ทุก
#XFLD: Plural Recurrence text for Hour
hoursLabel=ชั่วโมง
#XFLD: Plural Recurrence text for Day
daysLabel=วัน
#XFLD: Plural Recurrence text for Month
monthsLabel=เดือน
#XFLD: Plural Recurrence text for Minutes
minutesLabel=นาที
