
#XBCB: Breadcrumb
viewMonitorBreadcrumb={spaceId}

#XFLD: Label for persisted view column
SOURCE=Πηγή
#XFLD: Label for persisted view column
NAME=Ονομα
#XFLD: Label for persisted view column
NAME_LABEL=Επωνυμία Επιχείρησης
#XFLD: Label for persisted view column
NAME_LABELNew=Αντικείμενο (Επωνυμία Επιχείρησης)
#XFLD: Label for persisted view column
TECHINCAL_NAME=Τεχνικό Ονομα
#XFLD: Label for persisted view column
TECHINCAL_NAMENew=Αντικείμενο (Τεχνικό Ονομα)
#XFLD: Label for persisted view column
DATA_PERSISTENCY=Πρόσβαση Δεδομένων
#XFLD: Label for persisted view column
STATUS=Κατάσταση
#XFLD: Label for persisted view column
LAST_UPDATED=Τελευταία Ενημέρωση
#XFLD: Label for persisted view column
IN_MEMORY_SIZE=Mνήμη που χρησιμοποιείται για Αποθήκευση (MiB)
#XFLD: Label for persisted view column
DISK_SIZE=Δίσκος που χρησιμοποιείται για Αποθήκευση (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_NEWW=Μέγεθος στη Μνήμη (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_HEADER=Μέγεθος στην Μνήμη 
#XFLD: Label for persisted view column
DISK_SIZE_NEWW=Μέγεθος στον Δίσκο (MiB)
#XFLD: Label for persisted view column
DISK_SIZE_HEADER=Μέγεθος στον Δίσκο 
#XFLD: Label for schedule owner column
txtScheduleOwner=Ιδιοκτήτης Προγράμματος
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Εμφάνιση δημιουργού προγράμματος
#XFLD: Label for persisted view column
PERSISTED=Διατηρημένο
#XFLD: Label for persisted view column
TYPE=Τύπος
#XFLD: Label for View Selection Dialog column
changedOn=Αλλαγή Στις
#XFLD: Label for View Selection Dialog column
createdBy=Δημιουργημένο Από
#XFLD: Label for log details column
txtViewPersistencyLogs=ΠροβΗμερλ
#XFLD: Label for log details column
txtViewPersistencyLogsNew=Λεπτομέρειες
#XFLD: text for values shown for Ascending sort order
SortInAsc=Ταξινόμηση σε Αύξουσα Σειρά
#XFLD: text for values shown for Descending sort order
SortInDesc=Ταξινόμηση σε Φθίνουσα Σειρά
#XFLD: Label for persisted view monitor heading
ViewMonitor_Heading=Παρακολούθηση Προβολών
#XFLD: Label for persisted view monitor sub-heading
ViewMonitor_SubHeading=Παρακολούθηση και Συντήρηση Διατήρησης Προβολών


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ DynamicPageHeader ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: text for values shown in column Persistence Status
txtLoading=Φόρτωση
#XFLD: text for values shown in column Persistence Status
txtRunning=Εκτελείται
#XFLD: text for values shown in column Persistence Status
txtAvailable=Διαθέσιμο
#XFLD: text for values shown in column Persistence Status
txtError=Σφάλμα
#XMSG: Error message for unknown Persistence type that gets logged in Kibana
unknownReplicationTypeError=Τύπος αντιγραφής ''{0}'' δεν υποστηρίζεται.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Content ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Οι ρυθμίσεις που χρησιμοποιούνται για την τελευταία εκτέλεση διατήησης δεδομένων:
#XMSG: Message for input parameter name
inputParameterLabel=Παράμετρος Εισόδου
#XMSG: Message for input parameter value
inputParameterValueLabel=Αξία
#XMSG: Message for persisted data
inputParameterPersistedLabel=Διατηρημένο ΑΙ
#XTIT: Count for View in ViewMonitor Toolbar
txtViewCount=Προβολές ({0})
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Διατήρηση Προβολών
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Διατήρηση Δεδομένων
#XBUT: Button to clear the selected view persistance
ClearViewPersistance=Εκκαθάριση
#XBUT: Button to stop the selected view persistance
stopPersistance=Διακοπή Διατήρησης
#XFLD: Placeholder for Search field
txtSearch=Αναζήτηση
#XBUT: Tooltip for refresh button
txtRefresh=Ανανέωση
#XBUT: Tooltip for add view button
txtDeleteView=Διαγραφή Διατήρησης
#XBUT: Tooltip for load new peristence
loadNewPersistence=Επανέναρξη Διατήρησης
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Φόρτωση Νέου Στιγμιοτύπου
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Εναρξη Διατήρησης Δεδομένων
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Αφαίρεση Διατηρημένων Δεδομένων
#XMSG: success message for starting persistence
startPersistenceSuccess=Διατηρούμε την προβολή ''{0}''.
#XMSG: success message for stopping persistence
stopPersistenceSuccess=Αφαιρούμε τα διατηρημένα δεδομένα για προβολή ''{0}''.
#XMSG: success message for stopping persistence
deletePersistedViewSuccess=Θα διαγράψουμε την προβολή ''{0}'' από την λίστα παρακολούθησης.
#XMSG: error message during start persistence of a view (backend error)
ERROR_STARTING_PERSISTENCE=Σφάλμα κατά την έναρξη διατήρησης δεδομένων για προβολή ''{0}''.
#XMSG: error message during start persistence of a view (backend error)
PARAMERROR_STARTING_PERSISTENCE=Η προβολή ''{0}'' δεν μπορεί να διατηρηθεί γιατί περιέχει παραμέτρους εισόδου.
#XMSG: error message during start persistence of a view (backend error)
NUMBER_OF_PARAMETER_STARTING_PERSISTENCE=Η προβολή "{0}" δεν μπορεί να διατηρηθεί γιατί έχει περισσότερες από μία παραμέτρους εισόδου.
#XMSG: error message during start persistence of a view (backend error)
PARAMETER_DEFAULT_VALUE_STARTING_PERSISTENCE=Η προβολή "{0}" δεν μπορεί να διατηρηθεί γιατί η παράμετρος εισόδου δεν έχει προεπιλεγμένη τιμή.
#XMSG: error message during start persistence of a view (backend error)
REDEPLOYMENT_DAC_REQUIRED=Επανανάπτυξη Ελέγχου Προσβάσεων Δεδομένων (DAC) "{0}" είναι υποχρεωτική για την υποστήριξη διατήρησης δεδομένων.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_STARTING_PERSISTENCE=Η προβολή ''{0}'' δεν διατηρείται γιατί χρησιμοποιεί την προβολή ''{1}'', που έχει Ελεγχο Πρόσβασης Δεδομένων (DAC).
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_EXTERNAL_STARTING_PERSISTENCE=Η προβολή ''{0}''’ δεν μπορεί να διατηρηθεί γιατί χρησιμοποιεί μία προβολή με τον Ελεγχο Πρόσβασης Δεδομένων(DAC) που ανήκει σε έναν διαφορετικό χώρο.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_STRUCTURE_STARTING_PERSISTENCE=Η προβολή ''{0}''’ δεν μπορεί να διατηρηθεί γιατί η δομή ενός ή περισσοτέρων από τους Ελέγχους Πρόσβασης Δεδομένων (DAC) δεν υποστηρίζει διατήρηση προβολής.
#XMSG: error message during stop persistence of a view (backend error)
STOP_PERSISTENCE_ERROR=Σφάλμα κατά την διακοπή διατήρησης για προβολή ''{0}''.
#XMSG: error message during stop persistence of a view (backend error)
DELETE_PERSISTED_VIEW_ERROR=Σφάλμα συνέβη κατά τη διαγραφή διατηρημένης προβολής ''{0}''.
#XMSG: Warning message for removing the persisted data
RemovePersistency_Confirm_Msg=Θέλετε να διαγράψετε τα διατηρημένα δεδομένα και να επιλέξετε εικονική πρόσβαση της προβολής ''{0}'';
#XMSG: Warning message for delete the view persistence
DeletePersistency_Confirm_Msg=Θέλετε να διαγράψετε την προβολή από τη λίστα παρακολούθησης και να διαγράψετε τα διατηρημένα δεδομένα της προβολής ''{0}'';
#XMSG: error message for reading data from backend
txtReadBackendError=Μάλλον υπήρχε σφάλμα κατά την ανάγνωση από το back end.
#XFLD: Label for No Data Error
NoDataError=Σφάλμα
#XMSG: message for conflicting task
Task_Already_Running=Μία αντικρουόμενη εργασία εκτελείται για το αντικείμενο ''{0}''.

#XFLD: Error while accessing pertitioning for a view
missingPrivilegeInViewPersistency=Ανεπαρκής άδεια για εκτέλεση κατανομής για Προβολή ''{0}''

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Show All Views~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Text for show all views button
allViewsText=Όλες οι Προβολές ({0})
#XBUT: Text for show scheduled views button
scheduledText=Σχεδιασμένο ({0})
#XBUT: Text for show persisted views button
persistedText=Διατηρημένο ({0})
#XBUT: Text for start analyzer button
startAnalyzer=Έναρξη Αναλυτή Προβολής
#XFLD: Message if repository is unavailable
repositoryErrorMsg=Η αποθήκη δεν είναι διαθέσιμη και ορισμένα χαρακτηριστικά είναι ανενεργά.

#XFLD: Data Access - Virtual
Virtual=Εικονικό
#XFLD: Data Access - Persisted
Persisted=Διατηρημένο

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Non-Persisted View Selection Dialog~~~~~~~~~~~~~~~~~~~~~~~

#XTIT: Title for Non-Persisted View selection dialog
select_source=Επιλογή Προβολής για Διατήρηση

#XTIT: Placeholder for Non-Persisted View selection dialog search
searchView=Προβολές Αναζήτησης
#XTIT: No data in the list of non-persisted view
No_Data=Χωρίς Δεδομένα
#XBUT: Button to select non-persisted view
ok=Ok
#XBUT: Button to close the non-persisted views selection dialog
cancel=Ακύρωση

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~View persistency log message key~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message for view persistency logs
START_STOP_PERSISTENCY=Εναρξη εκτέλεσης εργασίας διατήρησης δεδομένων για "{1}"..
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY=Διατήρηση δεδομένων για προβολή "{1}".
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY)
START_VIEW_PERSISTENCY_PROCESS=Εναρξη επεξεργασίας για διατήρηση δεδομένων για προβολή "{0}".
#XFLD: Message for view persistency manual partition load
START_VIEW_PERSISTENCY_PROCESS_MANUAL_PARTITIONS=Εναρξη διαδικασίας διατήρησης δεδομένων για την προβολή "{0}"με τα επιλεγμένα IDs κατανομής: ''{1}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY=Διαγραφή διατηρημένων δεδομένων για προβολή "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY)
REMOVE_VIEW_PERSISTENCY=Εναρξη επεξεργασίας για διαγραφή διατηρημένων δεδομένων για προβολή "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_SUCCESS=Τα δεδομένα διατηρήθηκαν για προβολή "{1}".
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_SUCCESS)
START_VIEW_PERSISTENCY_PROCESS_SUCCESS=Τα δεδομένα διατηρήθηκαν για προβολή "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY_SUCCESS=Τα διατηρημένα δεδομένα διαγράφηκαν και η πρόσβαση σε εικονικά δεδομένα επανήλθε για προβολή ''{1}''.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_SUCCESS)
REMOVE_VIEW_PERSISTENCY_SUCCESS=Ολοκληρωμένη διαδικασία διαγραφής διατηρημένων δεδομένων για προβολή "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_FAILED=Αδύνατη διατήρηση δεδομένων για προβολή ''{1}''.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_FAILED)
START_VIEW_PERSISTENCY_PROCESS_FAILED=Αδύνατη διατήρηση δεδομένων για προβολή ''{0}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.05)
STOP_VIEW_PERSISTENCY_FAILED=Αδύνατη διαγραφή διατηρημένων δεδομένων για προβολή ''{1}''.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_FAILED)
REMOVE_VIEW_PERSISTENCY_FAILED=Αδύνατη διαγραφή διατηρημένων δεδομένων για προβολή ''{0}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_SUCCESS=«{3}» αρχεία διατηρήθηκαν για προβολή ''{1}''.
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace START_PERSISTENCY_RECORD_COUNT_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS={0} εγγραφές προστέθηκαν σε πίνακα διατήρησης για την προβολή ''{1}''.
#XFLD: Message used by SEAL repository for view persistency logs (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS_V2={0} εγγραφές εισήχθησαν στον πίνακα διατήρησης για την  προβολή ''{1}''. Μνήμη που χρησιμοποιείται: {2} GiB.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_SUCCESS=''{3}'' διατηρημένα αρχεία διαγράφηκαν για προβολή ''{1}''.
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace STOP_PERSISTENCY_RECORD_COUNT_SUCCESS)
REMOVE_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=Διατηρημένα δεδομένα διαγράφηκαν, ''{0}'' διατηρημένες εγραφές διαγράφηκαν.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_FAILED=Η εμφάνιση recordCount απέτυχε για προβολή ''{1}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_FAILED=Η εμφάνιση recordCount απέτυχε για προβολή ''{1}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Πρόγραμμα διαγράφηκε για ''{1}''.
#XFLD: Message for view persistency logs (new message to replace STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS)
REMOVE_VIEW_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Το πρόγραμμα διαγράφηκε για την προβολή ''{0}''.
#XFLD: Message for view persistency logs
STOP_PERSISTENCY_DELETE_SCHEDULE_FAILED=Διαγραφή προγράμματος απέτυχε για ''{1}''.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_REDEPLOY=Δεν μπορούμε να διατηρήσουμε την προβολή ''{0}'' γιατί άλλαξε και αναπτύχθηκε εφόσον αρχίσατε να την διατηρείτε. Δοκιμάστε πάλι να διατηρήσετε την προβολή ή περιμένετε την επόμενη προγραμματισμένη εκτέλεση.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_DELETE=Δεν μπορούμε να διατηρήσουμε την προβολή ''{0}'' γιατί διαγράφηκε αφού ξεκινήσατε να την διατηρείτε.
#XFLD: Message for successful data persistency of a single partition (for messages created before version 2022.03)
START_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} εγγραφές διατηρήθηκαν σε κατανομή για τιμές ''{1}'' <= {2} < ''{3}'' .
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (new message to replace START_PERSISTENCY_SINGLE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} εγγραφές προστέθηκαν σε κατανομή για τιμές ''{1}'' <= {2} < ''{3}'' .
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS_V2={0} εγγραφές εισήχθησαν στην κατανομή για τιμές ''{1}'' <= ''{2}'' < ''{3}''. Μνήμη που χρησιμοποιείται: {4} GiB.
#XFLD: Message for successful replication of a "others" partition (for messages created before version 2022.03)
START_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} εγγραφές διατηρήθηκαν σε κατανομή «άλλων».
#XFLD: Message used by SEAL repository for successful replication of a "others" partition (new message to replace START_PERSISTENCY_OTHERS_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} εγγραφές διατηρήθηκαν σε κατανομή «άλλων».
#XFLD: Message for view persistency record count of partitions (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={3} Εγγραφές διατηρήθηκαν για την προβολή ''{1}'' σε {4} κατανομές.
#XFLD: Message used by SEAL repository for view persistency record count of partitions (new message to replace START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={0} εγγραφές προστέθηκαν σε πίνακα διατήρησης για την προβολή «{1}» σε {2} κατανομές.
#XFLD: Message used by SEAL repository for view persistency for partition wise refresh
START_VIEW_PERSISTENCY_RECORD_COUNT_REFRESHED_PARTITIONS_SUCCESS={0} εγγραφές καταχωρίστηκαν σε πίνακα διατήρησης για την προβολή ''{1}''. Ενημερωμένες κατανομές: {2}. Κλειδωμένες κατανομές: {3} Συνολικές κατανομές: {4}
#XFLD: Message used by SEAL repository for view persistency for manual partition load
START_VIEW_PERSISTENCY_RECORD_COUNT_MANUAL_PARTITIONS_SUCCESS={0} εγγραφές καταχωρίστηκαν στον πίνακα διατήρησης για προβολή ''{1}'' σε {2} επιλεγμένες κατανομές.
#XFLD: Message used by SEAL repository for view persistency for locked partition data load
START_VIEW_PERSISTENCY_RECORD_COUNT_LOCKED_PARTITIONS_SUCCESS={0} εγγραφές καταχωρίστηκαν σε πίνακα διατήρησης για την προβολή ''{1}''. Ενημερωμένες κατανομές: {2} Κλειδωμένες αμετάβλητες κατανομές: {3} Συνολικές κατανομές: {4}
#XMSG: Message for unexpected error (for messages created before version 2022.03)
START_PERSISTENCY_UNEXPECTED_ERROR=Απρόβλεπτο σφάλμα κατά την διατήρηση δεδομένων για την προβολή ''{0}''.
#XMSG: Message for unexpected error (new message to replace START_PERSISTENCY_UNEXPECTED_ERROR)
START_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Απρόβλεπτο σφάλμα κατά την διατήρηση δεδομένων για την προβολή ''{0}''.
#XMSG: Message for Locked space error
START_VIEW_PERSISTENCY_LOCKED_SPACE_ERROR=Η προβολή ''{0}''’ δεν μπορεί να διατηρηθεί γιατί ο χώρος ''{1}'' είναι κλειδωμένος.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
STOP_PERSISTENCY_UNEXPECTED_ERROR=Απρόβλεπτο σφάλμα κατά την διαγραφή διατηρημένων δεδομένων για την προβολή ''{0}''.
#XMSG: Message for unexpected error (new message to replace STOP_PERSISTENCY_UNEXPECTED_ERROR)
REMOVE_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Απρόβλεπτο σφάλμα κατά την διαγραφή διατηρημένων δεδομένων για την προβολή ''{0}''.
#XMSG: Message for ivalidated view
VIEW_PERSISTENCY_ABORTED_DEFINITION_INVALID=Ο ορισμός της προβολής ''{0}'' κατέστη άκυρος, μάλλον λόγω αλλαγής ενός αντικειμένου που αναλώθηκε άμεσα ή έμμεσα από την προβολή. Προσπαθήστε να αναπτύξετε πάλι την προβολή για να επιλύσετε το ζήτημα ή να εντοπίσετε την αρχική αιτία.
#XMSG: Message for persisted data removal (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT=Τα διατηρημένα δεδομένα διαγράφονται κατά την ανάπτυξη προβολής ''{0}''.
#XMSG: Message for persisted data removal of a consuming view
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW=Τα διατηρημένα δεδομένα διαγράφονται κατά την ανάπτυξη της χρησιμοποιημένης προβολής ''{0}''.
#XMSG: Message for persisted data removal of a consuming view due to a dac change
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW_DAC_CHANGED=Τα διατηρημένα δεδομένα διαγράφονται όταν αναπτύσσετε την χρησιμοποιημένη προβολή ''{0}'' γιατί ο έλεγχος πρόσβασης δεδομένων άλλαξε.
#XMSG: Message for persisted data removal (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DEPLOYMENT=Τα διατηρημένα δεδομένα διαγράφονται κατά την ανάπτυξη προβολής ''{0}''.
#XMSG: Message for deleted view (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION=Η διατήρηση διαγράφεται με τη διαγραφή προβολής ''{0}''.
#XMSG: Message for deleted view (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DELETION=Η διατήρηση διαγράφεται με τη διαγραφή προβολής ''{0}''.
#XMSG Message for removal of persisted data, because prerequisites not fulfilled
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_PREREQUISITES_NOT_FULFILLED=Τα διατηρημένα δεδομένα καταργήθηκαν γιατί δεν πληρούνται οι προϋποθέσεις διατήρησης δεδομένων.
#XMSG: Message for inconsistent persistency
VIEW_PERSISTENCY_INCONSISTENT_PERSISTENCY=Η διατήρηση προβολής "{0}" είναι λανθασμένη. Διαγράψτε τα διατηρημένα δεδομένα για να επιλύσετε το ζήτημα.
#XMSG: Message for prerequisites
START_PERSISTENCY_CHECK_PREREQUISITES=Ελεγχος προϋποθέσεων για διατήρηση προβολής ''{0}''.
#XMSG: Message for DAC deployed in deprecated way using table function and join (for messages created before 2025.05)
START_PERSISTENCY_DEPRECIATED_DAC=Η προβολή "{0}" αναπτύχθηκε με χρήση του Ελέγχου Πρόσβασης Δεδομένων (DAC) που θα καταργηθεί. Αναπτύξτε την προβολή ξανά για να βελτιώσετε την απόδοση.
#XMSG: Message for DAC deployed in deprecated way using table function and join
START_PERSISTENCY_DEPRECATED_DAC=Η προβολή "{0}" αναπτύχθηκε με χρήση του Ελέγχου Πρόσβασης Δεδομένων (DAC) που θα καταργηθεί. Αναπτύξτε την προβολή ξανά για να βελτιώσετε την απόδοση.
#XMSG: Message for roll back (for messages created before version 2022.03)
ROLLBACK_VIEW_PERSISTENCY=Σφάλμα εντοπίστηκε. Η προηγούμενη κατάσταση διατήρησης επανήλθε για προβολή ''{0}''.
#XMSG: Message used by SEAL repository for rollback of running procedure to persist view (new message to replace ROLLBACK_VIEW_PERSISTENCY)
VIEW_PERSISTENCY_ROLLBACK=Σφάλμα. Η επεξεργασία διατήρησης προβολής ''{0}'' διακόπηκε και οι αλλαγές ακυρώθηκαν.
#XMSG: Message used by SEAL repository for rollback of running procedure to remove persisted data of view
REMOVE_VIEW_PERSISTENCY_ROLLBACK=Σφάλμα. Η επεξεργασία διαγραφής των διατηρημένων δεδομένων της προβολής ''{0}'' διακόπηκε και οι αλλαγές ακυρώθηκαν.
#XMSG: Message used by SEAL repository for persistency preparation
START_PERSISTENCY_PREPARATION=Προετοιμασία διατήρησης δεδομένων.
#XMSG: Message used by SEAL repository for data insertion
START_INSERTING_PERSISTENCY_DATA=Εισαγωγή δεδομένων σε πίνακα διατήρησης.
#XMSG: Message for null values - partitioning (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS={0} εγγραφές άκυρης τιμής διατηρήθηκαν σε «άλλες» κατανομές.
#XMSG: Message used by SEAL repository for others partition values - partitioning (new message to replace START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS={0} εγγραφές προστέθηκαν σε κατανομή «άλλοι» για τιμές ''{2}'' < ''{1}'' OR ''{2}'' >= ''{3}''.
#XMSG: Message used by SEAL repository for others partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS_V2={0} εγγραφές εισήχθησαν στην κατανομή «διάφορα» για τιμές ''{2}'' < ''{1}'' OR ''{2}'' >= ''{3}''. Μνήμη που χρησιμοποιείται: {4} GiB.
#XMSG: Message used by SEAL repository for null partition values - partitioning
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS={0} εγγραφές προστέθηκαν σε κατανομή «άλλοι» για τιμές ''{1}'' IS NULL.
#XMSG: Message used by SEAL repository for null partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS_V2={0} εγγραφές που εισήχθησαν στην κατανομή «διάφορα» για τιμές ''{1}'' ΕΙΝΑΙ ΑΚΥΡΟ. Μνήμη που χρησιμοποιείται: {2} GiB.
##XFLD:Message used by SEAL repository for remote statistics
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS=Φόρτωση σχετικών δεδομένων: {0} απομακρυσμένες δηλώσεις. Συνολικές εγγραφές λήφθηκαν: {1}. Συνολική διάρκεια: {2} δευτερόλεπτα.
##XFLD:Message used by SEAL repository for remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS_WITH_PARTITIONS=Φόρτωση σχετικών δεδομένων χρησιμοποιώντας {0} κατανομές με {1} απομακρυσμένα ερωτήματα. Συνολικές εγγραφές λήφθηκαν: {2}. Συνολική διάρκεια: {3} δευτερόλεπτα.
##XFLD:Message used by SEAL repository with UI ID for instructions of remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_INSTRUCTION=Οι απομακρυσμένες δηλώσεις που είναι επεξεργασμένες κατά την εκτέλεση μπορούν να εμφανιστούν ανοίγοντας την παρακολούθηση του απομακρυσμένου ερωτήματος, στις λεπτομέρειες των μηνυμάτων κατανομής.
##XFLD:Message for start reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS=Εναρξη της διαδικασίας επαναχρησιμοποίησης υφιστάμενων διατηρημένων δεδομένων για προβολή {0} μετά την ανάπτυξη.
##XFLD:Message used by SEAL repository for reattach view persitency
START_VIEW_PERSISTENCY_REATTACHMENT=Εναρξη επαναχρησιμοποίησης υφιστάμενων δεδομένων διατήρησης.
##XFLD:Message used by SEAL repository for reattach view persitency
START_FINILIZING_VIEW_PERSISTENCY_REATTACHMENT=Επαναχρησιμοποίηση υφιστάμενων δεδομένων διατήρησης.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_COMPLETE=Η διαδικασία επαναχρησιμοποίησης υφιστάμενων διατηρημένων δεδομένων ολοκληρώθηκε για την προβολή {0}.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_FAILED=Αδύνατη επαναχρησιμοποίηση υφιστάμενων διατηρημένων δεδομένων για προβολή {0} μετά την ανάπτυξη.
#XMSG: Message used by SEAL repository for finalizing persistency
START_FINALIZING_PERSISTENCY=Ολοκλήρωση διατήρησης.
#XMSG: Message used by SEAL repository for restoring virtual access for view
REMOVE_VIEW_PERSISTENCY_VIRTUAL_ACCESS_RESTORED=Εικονική πρόσβαση δεδομένων ισχύει για την προβολή ''{0}''.
#XMSG: Message used by SEAL repository for view that already has virtual access
REMOVE_VIEW_PERSISTENCY_ALREADY_VIRTUAL_ACCESS=Η προβολή "{0}" έχει ήδη εικονική πρόσβαση δεδομένων. Δεν διαγράφηκαν διατηρημένα δεδομένα.
#XMSG: Message for removing view persistency
VIEW_PERSISTENCY_REMOVED_FROM_MONITOR=Προβολή "{0}" διαγράφηκε από την οθόνη Προβολές.
#XMSG: Message for view doesnt exist.
VIEW_PERSISTENCY_VIEW_NOT_EXISTING=Η προβολή "{0}" είτε δεν υπάρχει στη βάση δεδομένων ή δεν αναπτύχθηκε σωστά και επομένως δεν μπορεί να διατηρηθεί. Δοκιμάστε να αναπτύξετε πάλι την προβολή για να επιλύσετε το πρόβλημα ή να εντοπίσετε την αρχική αιτία.
#XMSG: Message for persistency not enabled.
VIEW_PERSISTENCY_NOT_ENABLED=Η διατήρηση προβολής δεν είναι ενεργή. Νέα ανάπτυξη πίνακα/προβολή στον χώρο ''{0}'' για ενεργοποίηση λειτουργικότητας.
#XMSG: Message for unexpected error due to node crash
VIEW_PERSISTENCY_UNEXPECTED_ERROR_GENERAL=Τελευταία εκτέλεση διατήρησης προβολής διακόπηκε λόγω τεχνικών σφαλμάτων.
#XFLD: Message for view persistency memory consumption
VIEW_PERSISTENCY_MEMORY_CONSUMPTION={0} GiB.μέγιστης μνήμης χρησιμοποιείται στον χρόνο εκτέλεσης διατήρησης προβολής.
#XFLD: Message for view persistency timeout
VIEW_PERSISTENCY_TIMEOUT=Διατήρηση προβολής {0} έφθασε στην λήξη {1} ωρών.
#XFLD: Message for not started asynchronous view persistency
VIEW_PERSISTENCY_ASYNC_NOT_STARTED=Το υψηλό φορτίο συστήματος δεν επέτρεψε την έναρξη της ασύγχρονης εκτέλεσης της διατήρησης προβολής. Ελέγξτε αν εκτελούνται παράλληλα πολλές εργασίες.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency (for messages created before version 2022.05)
START_VIEW_PERSISTENCY_EXECUTION_METHOD_DROP_AND_RENAME=Ο υπάρχων πίνακας διατήρησης διαγράφηκε και αντικαταστάθηκε με έναν νέο πίνακα διατήρησης.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_RECREATE_AND_DROP=Ο υπάρχων πίνακας διατήρησης διαγράφηκε και αντικαταστάθηκε με έναν νέο πίνακα διατήρησης.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_TRUNCATE_AND_COPY=Ο υπάρχων πίνακας διατήρησης ενημερώθηκε με νέα δεδομένα.
##XFLD: Message for missing authorizations for view persistency
VIEW_PERSISTENCY_MISSING_AUTH=Απουσία εξουσιοδοτήσεων για διατήρηση δεδομένων.
##XFLD: Message start of persistency cancellation
VIEW_PERSISTENCY_TRIGGER_CANCEL=Εναρξη ακύρωσης διαδικασίας διατήρησης προβολής {0}.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_UNAVAILABLE_TASK_RUN=Αδύνατη ακύρωση διατήρησης προβολής γιατί δεν υπάρχει εκτελούμενη εργασία διατήρησης για την προβολή {0}.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_NONDETERMINISTIC_TASK_RUN=Αδύνατη ακύρωση διατήρησης προβολής γιατί δεν υπάρχει εκτελούμενη εργασία διατήρησης για την προβολή {0}.
##XFLD: Message for canceling a non running persitency task
VIEW_PERSISTENCY_CANCEL_NON_RUNNING_SELECTED_TASK=Αδύνατη ακύρωση της διαδικασίας διατήρησης της προβολής {0} γιατί η επιλεγμένη εργασία διατήρησης {1} δεν εκτελείται.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_UNREACHABLE_PERSISTENCY_RUN=Αδύνατη ακύρωση διαδικασίας διατήρησης προβολής γιατί η διατήρηση προβολής για την προβολή {0} δεν άρχισε ακόμα.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_COMPLETED_PERSISTENCY_RUN=Αδύνατη ακύρωση διαδικασίας διατήρησης προβολής {0} γιατί ολοκληρώθηκε ήδη.
##XFLD: Message for failed cancellation
VIEW_PERSISTENCY_ABORTED_CANCEL_EXECUTION=Αδύνατη ακύρωσης διαδικασίας διατήρησης προβολής {0}.
##XFLD: Message for finalizing persistency cancellation
VIEW_PERSISTENCY_FINALIZE_CANCEL=Η διαδικασία διατήρησης δεδομένων προβολής {0} υποβλήθηκε.
##XFLD: Message used in persist task when cancellation is caught
VIEW_PERSISTENCY_CANCELED=Η διαδικασία διατήρησης προβολής {0} διακόπηκε.
##XFLD: Message used in persist task when cancelation is caught and performed through a cancelation task
VIEW_PERSISTENCY_CANCELED_VIA_CANCEL_TASK=Διαδικασία διατήρησης προβολής {0} διακόπηκε μέσω εργασίας ακύρωσης {1}.
##XFLD: Message used in cancel task when the task is triggered by deployment
VIEW_PERSISTENCY_CANCEL_TRIGGERED_BY_DEPLOYMENT=Ακύρωση της διαδικασίας διατήρησης δεδομένων κατά την ανάπτυξη της προβολής {0}.
##XFLD: Message used in case the cancelation is retriggered for a running task
VIEW_PERSISTENCY_CANCEL_ATTEMPT_RETRIGGER_CANCEL=Μία προηγούμενη εργασία ακύρωσης για διατήρηση προβολής {0} υποβλήθηκε ήδη.
##XFLD: Message used to express the delay that may occur before the persistency task is stopped
VIEW_PERSISTENCY_CANCEL_DELAY_STOPPING_PERSISTENCY=Ενδέχεται να υπάρξει καθυστέρηση μέχρι να διακοπεί η εργασία διατήρησης για την προβολή {0} .
##XFLD: Message used to reflect the task log identifier of the current running persistency task
VIEW_PERSISTENCY_CANCEL_CURRENT_RUNNING_TASK=Δεδομένα για προβολή {0} διατηρήθηκαν με την εργασία {1}.
##XFLD:Message used by SEAL repository for locked partitions and DACs
VIEW_PERSISTENCY_LOCKED_PARTITIONS_DACS=Οι εξουσιοδοτήσεις που παρέχονται από τα DACs ενδέχεται να άλλαξαν και δεν περιλαμβάνονται στις κλειδωμένες κατανομές. Ξεκλειδώστε τις κατανομές και φορτώστε ένα νέο στιγμιότυπο για να εφαρμοστούν οι αλλαγές.
##XFLD:Message used by SEAL repository for column structure change
VIEW_PERSISTENCY_COLUMN_STRUCTURE_CHANGE=Η δομή της στήλης άλλαξε και δεν ταιριάζει πλέον στον υπάρχοντα πίνακα διατήρησης. Διαγράψτε τα διατηρημένα δεδομένα και ενεργοποιήστε μια νέα διατήρηση δεδομένων για να ενημερώσετε τον πίνακα διατήρησής σας.
##XFLD: Message for out of memory
VIEW_PERSISTENCY_HANA_OUT_OF_MEMORY=Η εργασία απέτυχε λόγω σφάλματος εξάντλησης μνήμης στη βάση δεδομένων SAP HANA.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_EXCEPTION_TRANSACTION_ROLLBACK=Η εργασία απέτυχε λόγω εσωτερικής εξαίρεσης στην βάση δεδομένων SAP HANA.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_SQL_EXECUTION_ISSUE=Η εργασία απέτυχε λόγω εσωτερικού προβλήματος εκτέλεσης SQL στην βάση δεδομένων SAP HANA.
##XFLD: Message for HANA out of memory result
VIEW_PERSISTENCY_OUT_OF_MEMORY_RESULT_MESSAGE=Αιτία γεγονότος εξάντλησης μνήμης ΗΑΝΑ: {0}
##XFLD: Message for admission control rejection
VIEW_PERSISTENCY_HANA_ADMISSION_CTRL_REJECTION=Η εργασία απέτυχε λόγω Απόρριψης Ελέγχου Εισαγωγής SAP HANA.
##XFLD: Message for HANA back pressure / rate limit of HANA client
VIEW_PERSISTENCY_HANA_CLIENT_RATE_LIMIT_REJECTION=Η εργασία απέτυχε λόγω πολλών ενεργών συνδέσεων SAP HANA.
#XMSG: Message for invalid persisted table name
VIEW_PERSISTENCY_PERSISTED_TABLE_INVALID=Σφάλμα προέκυψε και ο διατηρημένος πίνακας είναι άκυρος. Για να επιλύσετε το πρόβλημα διαγράψτε τα διατηρημένα δεδομένα και διατηρήστε την προβολή ξανά.
#XMSG: Message for usahe of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION=Η προβολή δεν μπορεί να διατηρηθεί. Χρησιμοποιεί έναν απομακρυσμένο πίνακα βάσει μίας απομακρυσμένης πηγής με ενεργή συμπλήρωση χρήστη.
#XMSG: Message for dynamic usage of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION_DYNAMIC=Η προβολή δεν μπορεί να διατηρηθεί. Χρησιμοποιεί έναν απομακρυσμένο πίνακα βάσει μίας απομακρυσμένης πηγής με ενεργή συμπλήρωση χρήστη. Ο απομακρυσμένος πίνακας μπορεί να χρησιμοποιηθεί δυναμικά μέσω μίας προβολής σεναρίου SQL. Η προέλευση της προβολής ενδέχεται να μην εμφανίσει τον απομακρυσμένο πίνακα.
#XMSG: Message for insufficient privileges or consumption of view with DAC via dynamic SQL
VIEW_PERSISTENCY_INSUF_PRIV_OR_DAC_DYN_SQL=Τα δικαιώματά σας ίσως δεν επαρκούν. Ανοίξτε την Προεπισκόπηση Δεδομένων για να δείτε αν έχετε τα δικαιώματα που απαιτούνται. Αν ναι, μία δεύτερη προβολή που χρησιμοποιήθηκε μέσω του δυναμικού σεναρίου SQL μπορεί να έχει εφαρμόσει έλεγχο προσβάσεων δεδομένων.
#XMSG: deployment of DAC is deprecated and view need to be redeployed to be able to persist data
VIEW_PERSISTENCY_DEPRECATED_DAC=Η προβολή "{0}" αναπτύχθηκε με χρήση του Ελέγχου Προσβάσεων Δεδομένων που  μειώνεται. Αναπτύξτε την προβολή ξανά για να διατηρήσετε τα δεδομένα για την προβολή.
##XFLD:Message used by SEAL repository for input parameter
START_VIEW_PERSISTENCY_INPUT_PARAMETER=Χρήση προεπιλεγμένης τιμής "{0}" για παράμετρο εισόδου "{1}".
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Elastic compute node keys~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
##XFLD:Message used to reflect disablement of ecn replica
ECN_DISABLE_EXISTING_REPLICA=Το αντίγραφο ελαστικού κόμβου υπολογισμού απενεργοποιήθηκε.
##XFLD:Message used to reflect recreation of ecn replica
ECN_RECREATE_EXISTING_REPLICA=Το αντίγραφο ελαστικού κόμβου υπολογισμού αναδημιουργήθηκε.
##XFLD:Message used to reflect re-enablement of ecn replica
ECN_ENABLE_EXISTING_REPLICA=Το αντίγραφο ελαστικού κόμβου υπολογισμού επανενεργοποιήθηκε.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Task Schedule Menu key~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message to schedule task
scheduleText=Χρονοδ/μα
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Δημιουργία Προγράμματος
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Επεξεργασία Προγράμματος
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Διαγραφή Προγράμματος
#XFLD: Refresh frequency field
refreshFrequency=Συχνότητα Ανανέωσης
#XFLD: Refresh frequency field
refreshFrequencyNew=Συχνότητα
#XFLD: Refresh frequency field
refreshFrequencyNewNew=Προγραμματισμένη Συχνότητα
#XBUT: label for None
none=Κανένα
#XBUT: label for Real-Time replication state
realtime=Πραγματικός Χρόνος
#XFLD: Label for table column
txtNextSchedule=Επόμενη Εκτέλεση
#XFLD: Label for table column
txtNextScheduleNew=Επόμενη Προγραμματισμένη Εκτέλεση
#XFLD: Label for table column
txtNumOfRecords=Αριθμός Εγγραφών
#XFLD: Label for scheduled link
scheduledTxt=Προγρ/μένο
#XFLD: LABEL for partially persisted link
partiallyPersisted=Μερικώς Διατηρημένο
#XFLD: Text for paused text
paused=Διακ.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ View Monitor Tasklog Details Action keys~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message for release lock actionn
releaseLockMsg=Εάν μία εκτέλεση διαρκέσει πολύ, ίσως είναι ένδειξη αποτυχίας και η κατάσταση δεν ενημερώθηκε. \r\n Για να επιλύσετε το ζήτημα, αφαιρέστε το κλείδωμα και καθορίστε την κατάσταση σε αποτυχημένη.
#XFLD: Label for release lock dialog
releaseLockText=Αφαίρεση Κλειδώματος

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Column header tooltip ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: tooltip for table column
txtViewTooltip=Ονομα διατηρημένης προβολής
#XFLD: tooltip for table column
txtViewDataAccessTooltip=Δείχνει την διαθεσιμότητα της προβολής
#XFLD: tooltip for table column
txtViewRefreshFreqTooltip=Δείχνει εάν ένα πρόγραμμα έχει καθοριστεί για την προβολή
#XFLD: tooltip for table column
txtViewStatusTooltip=Εμφανίζει την κατάσταση της διατηρημένης προβολής
#XFLD: tooltip for table column
txtViewLatestUpdateTooltip=Παρέχει πληροφορίες σχετικά με τον χρόνο τελευταίας ενημέρωσης της διατηρημένης προβολής
#XFLD: tooltip for table column
txtViewNextRunTooltip=Εάν καθορίστηκε ένα πρόγραμμα για τον απομακρυσμένο πίνακα, δείτε πότε θα προγραμματιστεί η επόμενη εκτέλεση
#XFLD: tooltip for table column
txtViewNumOfRecordsTooltip=Παρακολούθηση αριθμού εγγραφών.
#XFLD: tooltip for table column
txtViewInMemorySizeTooltip=Εντοπίστε το μέγεθος χώρου στη μνήμη που χρησιμοποιεί η προβολή
#XFLD: tooltip for table column
txtViewDiskSizeTooltip=Εντοπίστε το μέγεθος χώρου στον δίσκο σας που χρησιμοποιεί η πρόβολή
#XMSG: Expired text
txtExpired=Λήξη

#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Το αντικείμενο ''{0}''’ δεν μπορεί να προστεθεί στην αλυσίδα εργασιών.

#~~~~~~~~~~~View Analyzer task log messages~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message indicating number of records for a view, and memory consumption in Mebibyte during simulation of view persistency
RECORDS_PEAK_MEMORY=Η προβολή ''{0}'' έχει {1} εγγραφές. Μία προσομοίωση της προβολής χρησιμοποίησε {2} MiB μνήμης.
#XMSG: View Analyzer execution failure
VIEW_ANALYZER_EXECUTION_FAILED=Εκτέλεση Αναλυτής Προβολής απέτυχε.
#XMSG: Missing authorizations for View Analyzer
VIEW_ANALYZER_MISSING_AUTH=Απουσία εξουσιοδοτήσεων για Αναλυτή Προβολής.
#XMSG: Maximum memory in Gibibyte is reached, therefore persistency simulation is stopped
MAXIMUM_MEMORY_REACHED=Η μέγιστη μνήμη {0} GiB καλύφθηκε κατά την προσομοίωση διατήρησης για προβολή ''{1}''. Επομένως, δεν θα εκτελεστεί άλλη προσομοίωση διατήρησης προβολών.
#XMSG: Error during persistency simulation occurred
PERSISTENCY_SIMULATION_ERROR=Σφάλμα κατά την προσομοίωση διατήρησης για προβολή ''{0}''.
#XMSG: Persistency simulation is not executed, because prerequisites are not fulfilled and the view cannot be persisted
PREREQUISITES_NOT_FULFILLED=Προσομοίωση διατήρησης δεν εκτελέστηκε για προβολή ''{0}'', γιατί δεν πληρούνται οι προϋποθέσεις και η προβολή δεν μπορεί να διατηρηθεί.
#XMSG: Persistency simulation is not executed, because the view does not exist in the database
VIEW_DOES_NOT_EXIST=Πρέπει να αναπτύξετε την προβολή "{0}" για να ενεργοποιήσετε την προσομοίωση διατήρησης δεδομένων.
#XMSG: Number of records cannot be determined, because the local table does not exist in the database
TABLE_DOES_NOT_EXIST=Ο τοπικός πίνακας "{0}" δεν υπάρχει στη βάση δεδομένων, επομένως ο αριθμός εγγραφών δεν μπορεί να καθοριστεί για αυτό τον πίνακα.
##XMSG: Message start of cancellation for View Analyzer task
CANCEL_VIEW_ANALYZER_TRIGGERED=Διαδικασία για διακοπή της εργασίας View Analyzer {0} για την προβολή ''{1}'' υποβλήθηκε. Ενδέχεται να υπάρχει μία καθυστέρηση μέχρι να διακοπεί η εργασία.
##XMSG: Message start to indicate that task is not active
CANCEL_VIEW_ANALYZER_NO_ACTIVE_TASK=Η εργασία View Analyzer {0} για προβολή ''{1}'' δεν είναι ενεργή.
#XMSG: Cancel View Analyzer task failure
CANCEL_VIEW_ANALYZER_FAILED=Αδύνατη ακύρωση εργασίας View Analyzer.
##XMSG: Message used in View Analyzer execution task when task is canceled
VIEW_ANALYZER_CANCELED=Εκτέλεση Αναλυτή Προβολών για την προβολή "{0}" διακόπηκε μέσω εργασίας ακύρωσης.
##XMSG: Message start of cancelation for Model Validation task
CANCEL_MODEL_VALIDATION_TRIGGERED=Διαδικασία για διακοπή εργασίας Επαλήθευσης Μοντέλου {0} για προβολή "{1}" υποβλήθηκε. Μπορεί να υπάρχει καθυστέρηση μέχρι να διακοπεί η εργασία.
##XMSG: Message to indicate that task is not active
CANCEL_MODEL_VALIDATION_NO_ACTIVE_TASK=Εργασία Επαλήθευσης Μοντέλου {0} για προβολή "{1}" δεν είναι ενεργή.
#XMSG: Cancel Model Validation task failure
CANCEL_MODEL_VALIDATION_FAILED=Αδύνατη ακύρωση εργασίας Επαλήθευσης Μοντέλου.
##XMSG: Message used in Model Validation task when task is canceled
MODEL_VALIDATION_CANCELED=Η εκτέλεση Επαλήθευσης Μοντέλου για την προβολή "{0}" διακόπηκε μέσω εργασίας ακύρωσης.
#XMSG: Message for Locked space error, before starting Model Validation task
MODEL_VALIDATION_LOCKED_SPACE_ERROR=Αδύνατη εκτέλεση Επαλήθευσης Μοντέλου για την προβολή "{0}", γιατί ο χώρος "{1}" είναι κλειδωμένος.
##XMSG: Message used in View Analyzer execution task when determination of number of rows fails
NUMBER_ROWS_DETERMINATION_FAILED=Σφάλμα κατά τον καθορισμό του αριθμού σειρών για τον τοπικό πίνακα ''{0}''.
##XMSG: Message to indicate that SQL plan was created
EXECUTION_PLAN_CREATED=Αρχείο προγράμματος Αναλυτή SQL για προβολή ''{0}'' δημιουργήθηκε και μπορεί να ληφθεί.
#XFLD: Message to indicate starting of process to generate SQL Analyzer plan file
START_PLANVIZ_EXECUTION=Εναρξη επεξεργασίας για δημιουργία αρχείου προγράμματος Αναλυτή SQL για προβολή ''{0}''.
#XFLD: Message to indicate starting of View Analyzer process
VIEW_ANALYZER_EXECUTION_STARTED=Εναρξη εκτέλεσης Αναλυτή Προβολών.
##XMSG: Message to indicate that SQL plan file cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_PLANVIZ_NOT_FULFILLED=Αδύνατη δημιουργία αρχείου προγράμματος Αναλυτή SQL για προβολή ''{0}'', γιατί δεν πληρούνται οι προϋποθέσεις διατήρησης.`
##XMSG: Error message during  generation of SQL Analyzer plan file
PLANVIZ_ERROR=Σφάλμα κατά την δημιουργία αρχείου προγράμματος Αναλυτή SQL για προβολή ''{0}''.
#XMSG: Message for Locked space error
VIEW_ANALYZER_LOCKED_SPACE_ERROR=Αδύνατη εκτέλεση του View Analyzer για προβολή ''{0}'', γιατί ο χώρος ''{1}'' είναι κλειδωμένος.
#XMSG: Message for persistency simulation without partitioning
PERSISTENCY_SIMULATION_WITHOUT_PARTITIONS=Οι κατανομές προβολής ''{0}'' δεν περιλαμβάνονται κατά την προσομοίωση διατήρησης προβολής.
#XMSG: Message for generation of SQL Analyzer plan file without considering partitioning
PLANVIZ_WITHOUT_PARTITIONS=Οι κατανομές προβολής ''{0}'' δεν περιλαμβάνονται κατά την δημιουργία αρχείου προγράμματος Αναλυτή SQL.
#XMSG: Confirmation message for mass removal of persisted data
MassRemovePersistency_Confirm_Msg=Θέλετε να διαγράψετε τα διατηρημένα δεδομένα και να επιστρέψετε την πρόσβαση δεδομένων σε εικονική πρόσβαση;
#XMSG: Confirmation message for partial mass removal of selected persisted data
PartialMassRemovePersistency_Confirm_Msg={0} από {1} τις επιλεγμένες προβολές έχουν διατηρημένα δεδομένα. \n Θέλετε να διαγράψετε τα διατηρημένα δεδομένα και να αλλάξετε την πρόσβαση δεδομένων σε εικονική πρόσβαση;
#XMSG: success message for stopping mass persistence
stopMassPersistenceSuccess=Διαγράφουμε τα διατηρημένα δεδομένα για τις επιλεγμένες προβολές.
#XMSG: error message during mass stop persistence of a view (backend error)
STOP_MASS_PERSISTENCE_ERROR=Σφάλμα κατά την διακοπή διατήρησης για τις επιλεγμένες προβολές.
#XMSG: memory analysis is only performed in space {0}
NO_MEMORY_ANALYSIS=Η ανάλυση μνήμης εκτελέστηκε για οντότητες στον χώρο "{0}" μόνο: "{1}" "{2}" παραβλέφθηκε.
##XMSG: Message to indicate that Explain Plan cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_EXPLAINPLAN_NOT_FULFILLED=Το αρχείο Επεξήγησης Σχεδίου δεν μπορεί να δημιουργηθεί για την προβολή "{0}", επειδή οι προϋποθέσεις διατήρησης δεν πληρούνται.
#XMSG: Message for Explain Plan generation without partitioning
EXPLAINPLAN_WITHOUT_PARTITIONS=Τα τμήματα της προβολής "{0}" δεν λαμβάνονται υπόψη κατά τη δημιουργία του αρχείου Επεξήγηση Σχεδίου.
#XFLD: Message to indicate starting of process to generate Explain Plan file
START_EXPLAINPLAN_EXECUTION=Εκκίνηση της διαδικασίας δημιουργίας του αρχείου Επεξήγησης Σχεδίου για την προβολή "{0}".
##XMSG: Message to indicate that Explain Plan was created
EXPLAINPLAN_CREATED=Το αρχείο Επεξήγηση Προγράμματος για την προβολή "{0}" δημιουργήθηκε. Μπορείτε να το εμφανίσετε πατώντας "Προβολή Λεπτομερειών", ή να το κατεβάσετε αν έχετε την σχετική άδεια.
##XMSG: Error message during  generation of Explain Plan file
EXPLAINPLAN_ERROR=Προέκυψε σφάλμα κατά τη δημιουργία του αρχείου Επεξήγηση Σχεδίου για την προβολή "{0}".
##XMSG: Explain Plan file cannot be generated for the view {0}, because too many views are stacked on each other
EXPLAINPLAN_MODEL_TOO_COMPLEX=Το αρχείο Επεξήγηση Προγράμματος δεν μπορεί να δημιουργηθεί για την προβολή "{0}". Πολλές προβολές συγκεντρώνονται η μία πάνω στην άλλη. Σύνθετα μοντέλα μπορεί να προκαλέσουν σφάλματα μνήμης και επιβραδύνουν την απόδοση. Σας προτείνουμε να διατηρήσετε μία προβολή.

##XMSG: Message to indicate that the space is locked
PERFORMANCE_ANALYSIS_LOCKED_SPACE_ERROR=Αδύνατη εκτέλεση ανάλυσης απόδοσης για προβολή ''{0}'', γιατί ο χώρος ''{1}'' είναι κλειδωμένος.
##XMSG: The performance analysis task was cancelled
PERFORMANCE_ANALYSIS_CANCELED=Ανάλυση απόδοσης για προβολή "{0}" ακυρώθηκε.
##XMSG: The performance analysis failed
PERFORMANCE_ANALYSIS_FAILED=Ανάλυση απόδοσης απέτυχε.
##XMSG: The performance analysis is finished
PERFORMANCE_ANALYSIS_FINISHED=Ανάλυση απόδοσης για προβολή "{0}" ολοκληρώθηκε. Εμφανίστε το αποτέλεσμα πατώντας "Προβολή λεπτομερειών".
##XMSG: The view has input parameter with no default value
INPUT_PARAMETER_WITHOUT_DEFAULT_VALUE=Αυτή η προβολή δεν μπορεί να αναλυθεί γιατί έχει μία παράμετρο χωρίς προεπιλεγμένη τιμή.
##XMSG: The view is not fully deployed
VIEW_NOT_FULLY_DEPLOYED=Αυτή η προβολή δεν μπορεί να αναλυθεί γιατί δεν αναπτύχθηκε πλήρως.
##XMSG: The view uses limited remote adapters
LIMITED_REMOTE_ADAPTER=Αυτή η προβολή χρησιμοποιεί έναν απομακρυσμένο προσαρμογέα με περιορισμένες δυνατότητες όπως απουσία προώθησης φίλτρου ή υποστήριξη για 'Αριθμός'. Η διατήρηση ή αντιγραφή αντικειμένων μπορεί να βελτιώσει την απόδοση χρόνου εκτέλεσης.
##XMSG: The view uses remote adapter that does not support 'Limit'
REMOTE_ADAPTER_NOT_SUPPORTING_LIMIT=Αυτή η προβολή χρησιμοποιεί τουλάχιστον έναν απομακρυσμένο προσαρμογέα που δεν υποστηρίζει 'Όριο'. Μπορεί να επιλέχθηκαν πάνω από 1000 εγγραφές.
##XMSG: Performance analysis is executed by using default parameters of view'
INPUT_PARAMETER_WITH_DEFAULT_VALUE=Η ανάλυση απόδοσης εκτελέστηκε με τις προεπιλεγμένες τιμές παραμέτρων προβολής.
##XMSG: Message to indicate that an error occurred
PERFORMANCE_ANALYSIS_ERROR=Σφάλμα κατά την ανάλυση απόδοσης για προβολή "{0}".
##XMSG: Message start of cancellation for Performance Analysis task
CANCEL_PERFORMANCE_ANALYSIS_TRIGGERED=Διαδικασία για διακοπή της εργασίας Ανάλυσης Απόδοσης {0} για την προβολή ''{1}'' υποβλήθηκε. Ενδέχεται να υπάρχει μία καθυστέρηση μέχρι να διακοπεί η εργασία.
##XMSG: Message start to indicate that task is not active
CANCEL_PERFORMANCE_ANALYSIS_NO_ACTIVE_TASK=Εργασία Ανάλυσης Απόδοσης {0} για την προβολή ''{1}'' ανενεργή.
#XMSG: Cancel Performance Analysis task failure
CANCEL_PERFORMANCE_ANALYSIS_FAILED=Αδύνατη ακύρωση εργασίας Ανάλυσης Απόδοσης.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Αντιστοίχιση Προγράμματος σε Εμένα
#XBUT: Pause schedule menu label
pauseScheduleLabel=Διακοπή προγράμματος
#XBUT: Resume schedule menu label
resumeScheduleLabel=Επανέναρξη Προγράμματος
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Σφάλμα κατά την διαγραφή προγραμμάτων.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Σφάλμα κατά την αντιστοίχιση προγραμμάτων.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Σφάλμα κατά την διακοπή προγραμμάτων.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Σφάλμα κατά την επανέναρξη προγραμμάτων.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Διαγραφή {0} προγραμμάτων
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Αλλαγή ιδιοκτήτη {0} προγραμμάτων
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Διακοπή {0} προγραμμάτων
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Επανέναρξη {0} προγραμμάτων
#XBUT: Select Columns Button
selectColumnsBtn=Επιλογή Στηλών
#XFLD: Refresh tooltip
TEXT_REFRESH=Ανανέωση
#XFLD: Select Columns tooltip
text_selectColumns=Επιλογή Στηλών


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Runtime Metrics ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Label for Runtime Metrics
RuntimeMetricsTitle=Μετρήσεις χρόνου εκτέλεσης
#XFLD : Label for Run Button
runButton=Εκτέλεση
#XFLD : Label for Cancel Button
cancelButton=Ακύρωση
#XFLD : Label for Close Button
closeButton=Κλείσιμο
#XFLD : Label for Open View Analyzer Button
openViewAnalyzerButton=Άνοιγμα Αναλυτή Προβολής
#XFLD : Label for Generate Explain Plan Button
generateExplainPlanButton=Δημιουργία Προγράμματος Επεξήγησης
#XFLD : Label for Previous Run Column
previousRun=Προηγούμενη Εκτέλεση
#XFLD : Label for Latest Run Column
latestRun=Τελευταία Εκτέλεση
#XFLD : Label for time Column
time=Χρόνος
#XFLD : Label for Duration Column
duration=Διάρκεια
#XFLD : Label for Peak Memory Column
peakMemory=Ασθενής Μνήμη
#XFLD : Label for Number of Rows
numberOfRows=Αριθμός Σειρών
#XFLD : Label for Overall Number of Sources
overallNumberOfSources=Συνολικός αριθμός πηγών
#XFLD : Label for Data Access Column
dataAccess=Πρόσβαση Δεδομένων
#XFLD : Label for Local Tables
localTables=Τοπικοί Πίνακες
#XFLD : Label for federatedRemoteTables
federatedRemoteTables=Ομαδοποιημένοι Απομακρυσμένοι Πίνακες (με Περιορισμένες Ιδιότητες Προσαρμογέα)
#XTXT Text for initial state of the runtime metrics
initialState=Πρέπει να εκτελέσετε πρώτα τα την Ανάλυση Απόδοσης για να λάβετε μετρήσεις. Αυτό ίσως είναι χρονοβόρο, αλλά μπορείτε να ακυρώσετε τη διαδικασία αν χρειαστεί.
#XMSG: Cancel confirmation message
CancelRunConfirmationMessage=Θέλετε να ακυρώσετε την τρέχουσα εκτέλεση Ανάλυσης Απόδοσης;
#XTIT: Cancel dialog title
CancelRunTitle=Ακύρωση Εκτέλεσης
#XFLD: Label for Number of Rows
NUMBER_ROWS=Αριθμός Σειρών
#XFLD: Label for Overall Number of Sources
OVERALL_NUMBER_SOURCES=Συνολικός αριθμός πηγών
#XFLD: Label for Data Access
DATA_ACCESS=Πρόσβαση Δεδομένων
#XFLD: Label for Remote tables
NUMBER_REMOTE_TABLES_LIMITED_ADAPTERS=Ομαδοποιημένοι Απομακρυσμένοι Πίνακες (με Περιορισμένες Ιδιότητες Προσαρμογέα)
#XFLD: Label for select statement
SELECT_STATEMENT='SELECT * FROM VIEW LIMIT 1000'
#XFLD: Label for duration
SELECT_RUNTIME=Διάρκεια
#XFLD: Label for peak memory
SELECT_PEAK_MEMORY=Ασθενής Μνήμη
#XFLD: Label for count statement
SELECT_COUNT_STATEMENT='SELECT COUNT(*) FROM VIEW'
#XFLD: Label for count duration
SELECT_COUNT_RUNTIME=Διάρκεια
#XFLD: Label for count peak memory
SELECT_COUNT_PEAK_MEMORY=Ασθενής Μνήμη
#XFLD: Label for count of number of local tables
NUMBER_LOCAL_TABLES_FILE=Τοπικοί Πίνακες (Αρχείο)
#XTXT: Text for running state of the runtime metrics
Running=Εκτελείται
#XFLD: Label for time
Time=Χρόνος
#XFLD: Label for virtual access
PA_VIRTUAL=Εικονικό
#XFLD: Label for persisted access
PA_PERSISTED=Διατηρημένο
PA_PARTIALLY_PERSISTED=Μερικώς Διατηρημένο
#XTXT: Text for cancel
CancelRunSuccessMessage=Ακύρωση εκτέλεσης Ανάλυσης Απόδοσης.
#XTXT: Text for cancel error
CancelRunErrorMessage=Σφάλμα κατά την ακύρωση εκτέλεσης Ανάλυσης Απόδοσης.
#XTXT: Text for explain plan generation
ExplainPlanStarted=Δημιουργία Προγράμματος Επεξήγησης για την προβολή "{0}".
#XTXT: Text for performance analysis
PerformanceAnalysisStarted=Έναρξη Ανάλυσης Απόδοσης για προβολή "{0}".
#XTXT: Text for performance analysis error
ErrorFetchingPerformanceAnalysis=Σφάλμα κατά την λήψη δεδομένων Ανάλυσης Απόδοσης.
#XTXT: Text for performance analysis error
conflictingTask=Εργασία Ανάλυσης Απόδοσης εκτελείται ήδη
#XFLD: Label for Errors
Errors=Σφάλμα(τα)
#XFLD: Label for Warnings
Warnings=Προειδοποίηση/εις
#XFLD: Tooltip for missing privileges
openViewAnalyzerButtonTooltipNoPrivilege=Χρειάζεστε το δικαίωμα DWC_DATAINTEGRATION(update) για να ανοίξετε το View Analyzer.
#XFLD: Tooltip for missing privileges
generateExplainPlanButtonTooltipNoPrivilege=Χρειάζεστε το δικαίωμα DWC_RUNTIME(read) για να δημιιουργήσετε το Πρόγραμμα Επεξήγησης.



#XFLD: Label for frequency column
everyLabel=Κάθε
#XFLD: Plural Recurrence text for Hour
hoursLabel=Ωρες
#XFLD: Plural Recurrence text for Day
daysLabel=Ημέρες
#XFLD: Plural Recurrence text for Month
monthsLabel=Μήνες
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Λεπτά
