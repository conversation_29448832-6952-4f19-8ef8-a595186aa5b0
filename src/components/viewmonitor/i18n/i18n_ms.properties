
#XBCB: Breadcrumb
viewMonitorBreadcrumb={spaceId}

#XFLD: Label for persisted view column
SOURCE=Sumber
#XFLD: Label for persisted view column
NAME=Nama
#XFLD: Label for persisted view column
NAME_LABEL=Nama <PERSON>an
#XFLD: Label for persisted view column
NAME_LABELNew=<PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON>)
#XFLD: Label for persisted view column
TECHINCAL_NAME=Nama Teknikal
#XFLD: Label for persisted view column
TECHINCAL_NAMENew=<PERSON><PERSON><PERSON><PERSON> (Nama Teknikal)
#XFLD: Label for persisted view column
DATA_PERSISTENCY=Capaian Data
#XFLD: Label for persisted view column
STATUS=Status
#XFLD: Label for persisted view column
LAST_UPDATED=Terakhir Dikemas Kini
#XFLD: Label for persisted view column
IN_MEMORY_SIZE=Ingatan Digunakan untuk Storan (MiB)
#XFLD: Label for persisted view column
DISK_SIZE=Cakera Digunakan untuk Storan (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_NEWW=Saiz In-Memory (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_HEADER=Saiz In-Memory
#XFLD: Label for persisted view column
DISK_SIZE_NEWW=Saiz pada Cakera (MiB)
#XFLD: Label for persisted view column
DISK_SIZE_HEADER=Saiz pada Cakera
#XFLD: Label for schedule owner column
txtScheduleOwner=Pemilik Jadual
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Menunjukkan orang yang mencipta jadual
#XFLD: Label for persisted view column
PERSISTED=Berterusan
#XFLD: Label for persisted view column
TYPE=Jenis
#XFLD: Label for View Selection Dialog column
changedOn=Diubah pada
#XFLD: Label for View Selection Dialog column
createdBy=Dicipta oleh
#XFLD: Label for log details column
txtViewPersistencyLogs=Paparkan Log
#XFLD: Label for log details column
txtViewPersistencyLogsNew=Butiran
#XFLD: text for values shown for Ascending sort order
SortInAsc=Isih Menaik
#XFLD: text for values shown for Descending sort order
SortInDesc=Isih Menurun
#XFLD: Label for persisted view monitor heading
ViewMonitor_Heading=Pemantau Paparan
#XFLD: Label for persisted view monitor sub-heading
ViewMonitor_SubHeading=Pantau dan Selenggarakan Keterusan Data Paparan


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ DynamicPageHeader ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: text for values shown in column Persistence Status
txtLoading=Memuat
#XFLD: text for values shown in column Persistence Status
txtRunning=Sedang Berjalan
#XFLD: text for values shown in column Persistence Status
txtAvailable=Tersedia
#XFLD: text for values shown in column Persistence Status
txtError=Ralat
#XMSG: Error message for unknown Persistence type that gets logged in Kibana
unknownReplicationTypeError=Jenis replikasi "{0}" tidak disokong.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Content ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Tetapan digunakan untuk jalanan keterusan data terakhir:
#XMSG: Message for input parameter name
inputParameterLabel=Parameter Input
#XMSG: Message for input parameter value
inputParameterValueLabel=Nilai
#XMSG: Message for persisted data
inputParameterPersistedLabel=Berterusan pada
#XTIT: Count for View in ViewMonitor Toolbar
txtViewCount=Paparan ({0})
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Paparkan Keterusan
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Keterusan Data
#XBUT: Button to clear the selected view persistance
ClearViewPersistance=Kosongkan
#XBUT: Button to stop the selected view persistance
stopPersistance=Hentikan Keterusan
#XFLD: Placeholder for Search field
txtSearch=Cari
#XBUT: Tooltip for refresh button
txtRefresh=Segar Semula
#XBUT: Tooltip for add view button
txtDeleteView=Padam Keterusan
#XBUT: Tooltip for load new peristence
loadNewPersistence=Mulakan Semula Keterusan
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Muat Snapshot Baharu
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Mulakan Keterusan Data
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Keluarkan Data Berterusan
#XMSG: success message for starting persistence
startPersistenceSuccess=Kami meneruskan paparan "{0}".
#XMSG: success message for stopping persistence
stopPersistenceSuccess=Kami mengeluarkan data berterusan untuk paparan "{0}".
#XMSG: success message for stopping persistence
deletePersistedViewSuccess=Kami mengeluarkan paparan "{0}" daripada senarai pemantauan.
#XMSG: error message during start persistence of a view (backend error)
ERROR_STARTING_PERSISTENCE=Ralat berlaku semasa memulakan keterusan data untuk paparan "{0}".
#XMSG: error message during start persistence of a view (backend error)
PARAMERROR_STARTING_PERSISTENCE=Paparan "{0}" tidak boleh diteruskan kerana ia mengandungi parameter input.
#XMSG: error message during start persistence of a view (backend error)
NUMBER_OF_PARAMETER_STARTING_PERSISTENCE=Paparan "{0}" tidak boleh diteruskan kerana ia mempunyai lebih daripada satu parameter input.
#XMSG: error message during start persistence of a view (backend error)
PARAMETER_DEFAULT_VALUE_STARTING_PERSISTENCE=Paparan "{0}" tidak boleh diteruskan kerana parameter input tidak mempunyai nilai lalai.
#XMSG: error message during start persistence of a view (backend error)
REDEPLOYMENT_DAC_REQUIRED=Pengerahan semula Kawalan Capaian Data (DAC) "{0}" diperlukan untuk menyokong keterusan paparan.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_STARTING_PERSISTENCE=Paparan "{0}" tidak berjaya diteruskan kerana ia menggunakan paparan "{1}" yang mengandungi Kawalan Capaian Data (DAC).
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_EXTERNAL_STARTING_PERSISTENCE=Cuba semula paparan berterusan "{0}", kerana ia menggunakan paparan dengan Kawalan Capaian Data (DAC) kepunyaan ruang yang berbeza.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_STRUCTURE_STARTING_PERSISTENCE=Cuba semula keterusan paparan "{0}" kerana struktur satu atau beberapa Kawalan Capaian Data (DAC) tidak menyokong keterusan data.
#XMSG: error message during stop persistence of a view (backend error)
STOP_PERSISTENCE_ERROR=Ralat berlaku semasa menghentikan keterusan untuk paparan "{0}".
#XMSG: error message during stop persistence of a view (backend error)
DELETE_PERSISTED_VIEW_ERROR=Ralat berlaku semasa memadam paparan berterusan "{0}".
#XMSG: Warning message for removing the persisted data
RemovePersistency_Confirm_Msg=Adakah anda ingin memadam data berterusan dan menukarnya kepada capaian maya bagi paparan "{0}"?
#XMSG: Warning message for delete the view persistence
DeletePersistency_Confirm_Msg=Adakah anda ingin mengeluarkan paparan daripada senarai dan memadam data berterusan bagi paparan "{0}"?
#XMSG: error message for reading data from backend
txtReadBackendError=Terdapat ralat semasa bacaan dari bahagian belakang.
#XFLD: Label for No Data Error
NoDataError=Ralat
#XMSG: message for conflicting task
Task_Already_Running=Percanggahan tugas telah berjalan untuk paparan "{0}".

#XFLD: Error while accessing pertitioning for a view
missingPrivilegeInViewPersistency=Anda memerlukan kebenaran yang mencukupi untuk melaksanakan pembahagian bagi Paparan "{0}"

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Show All Views~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Text for show all views button
allViewsText=Semua Paparan ({0})
#XBUT: Text for show scheduled views button
scheduledText=Berjadual ({0})
#XBUT: Text for show persisted views button
persistedText=Berterusan ({0})
#XBUT: Text for start analyzer button
startAnalyzer=Mulakan Penganalisis Paparan
#XFLD: Message if repository is unavailable
repositoryErrorMsg=Repositori tidak tersedia dan beberapa ciri dinyahdayakan.

#XFLD: Data Access - Virtual
Virtual=Maya
#XFLD: Data Access - Persisted
Persisted=Berterusan

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Non-Persisted View Selection Dialog~~~~~~~~~~~~~~~~~~~~~~~

#XTIT: Title for Non-Persisted View selection dialog
select_source=Pilih Paparan untuk Berterusan

#XTIT: Placeholder for Non-Persisted View selection dialog search
searchView=Cari Paparan
#XTIT: No data in the list of non-persisted view
No_Data=Tiada Data
#XBUT: Button to select non-persisted view
ok=Ok
#XBUT: Button to close the non-persisted views selection dialog
cancel=Batalkan

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~View persistency log message key~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message for view persistency logs
START_STOP_PERSISTENCY=Memulakan tugas keterusan data dijalankan untuk "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY=Keterusan data untuk paparan ''{1}''.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY)
START_VIEW_PERSISTENCY_PROCESS=Memulakan proses untuk data berterusan bagi paparan "{0}".
#XFLD: Message for view persistency manual partition load
START_VIEW_PERSISTENCY_PROCESS_MANUAL_PARTITIONS=Memulakan proses untuk data berterusan bagi paparan "{0}" dengan ID bahagian yang anda pilih: "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY=Mengeluarkan data berterusan untuk paparan "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY)
REMOVE_VIEW_PERSISTENCY=Memulakan proses untuk mengeluarkan data berterusan bagi paparan "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_SUCCESS=Data berterusan untuk paparan "{1}".
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_SUCCESS)
START_VIEW_PERSISTENCY_PROCESS_SUCCESS=Data berterusan untuk paparan "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY_SUCCESS=Data berterusan dikeluarkan dan capaian data maya dipulihkan untuk paparan "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_SUCCESS)
REMOVE_VIEW_PERSISTENCY_SUCCESS=Selesaikan proses untuk mengeluarkan data berterusan bagi paparan "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_FAILED=Keterusan data untuk paparan "{1}" tidak berjaya.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_FAILED)
START_VIEW_PERSISTENCY_PROCESS_FAILED=Keterusan data untuk paparan "{0}" tidak berjaya.
#XFLD: Message for view persistency logs (for messages created before version 2022.05)
STOP_VIEW_PERSISTENCY_FAILED=Data berterusan untuk paparan "{1}" tidak berjaya dikeluarkan.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_FAILED)
REMOVE_VIEW_PERSISTENCY_FAILED=Data berterusan untuk paparan "{0}" tidak berjaya dikeluarkan.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_SUCCESS="{3}" rekod berterusan untuk paparan "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace START_PERSISTENCY_RECORD_COUNT_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS={0} rekod dimasukkan ke dalam jadual keterusan data untuk paparan "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS_V2=Anda telah masukkan {0} rekod ke dalam jadual keterusan data untuk paparan "{1}". Penggunaan ingatan: {2} GiB.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_SUCCESS="{3}" rekod berterusan dikeluarkan untuk paparan "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace STOP_PERSISTENCY_RECORD_COUNT_SUCCESS)
REMOVE_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=Data berterusan dikeluarkan, rekod berterusan "{0}" dipadam.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_FAILED=Dapatkan recordCount gagal untuk paparan "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_FAILED=Dapatkan recordCount gagal untuk paparan "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Anda berjaya memadam jadual untuk "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS)
REMOVE_VIEW_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Anda berjaya memadam jadual untuk paparan "{0}".
#XFLD: Message for view persistency logs
STOP_PERSISTENCY_DELETE_SCHEDULE_FAILED=Pemadaman jadual untuk "{1}" tidak berjaya.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_REDEPLOY=Kami tidak boleh teruskan dengan paparan "{0}" kerana ia telah diubah dan dikerahkan sejak anda mula teruskannya. Cuba lagi teruskan dengan paparan atau tunggu sehingga jalanan berjadual seterusnya.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_DELETE=Kami tidak boleh teruskan dengan paparan "{0}" kerana ia telah dipadam sejak anda mula teruskannya.
#XFLD: Message for successful data persistency of a single partition (for messages created before version 2022.03)
START_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} rekod berterusan masuk ke dalam bahagian untuk nilai "{1}" <= {2} < "{3}".
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (new message to replace START_PERSISTENCY_SINGLE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} rekod dimasukkan ke dalam bahagian untuk nilai "{1}" <= "{2}" < "{3}".
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS_V2=Anda telah masukkan {0} rekod ke dalam bahagian untuk nilai "{1}" <= "{2}" < "{3}". Penggunaan ingatan: {4} GiB.
#XFLD: Message for successful replication of a "others" partition (for messages created before version 2022.03)
START_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} rekod berterusan masuk ke dalam bahagian "lain".
#XFLD: Message used by SEAL repository for successful replication of a "others" partition (new message to replace START_PERSISTENCY_OTHERS_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} rekod dimasukkan ke dalam bahagian "lain".
#XFLD: Message for view persistency record count of partitions (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={3} rekod berterusan untuk paparan "{1}" dalam {4} bahagian.
#XFLD: Message used by SEAL repository for view persistency record count of partitions (new message to replace START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={0} rekod dimasukkan ke dalam jadual keterusan data untuk paparan "{1}" dalam {2} bahagian.
#XFLD: Message used by SEAL repository for view persistency for partition wise refresh
START_VIEW_PERSISTENCY_RECORD_COUNT_REFRESHED_PARTITIONS_SUCCESS={0} rekod dimasukkan dalam jadual keterusan data untuk paparan "{1}". Bahagian dikemas kini: {2}; Bahagian dikunci: {3}; Jumlah bahagian: {4}
#XFLD: Message used by SEAL repository for view persistency for manual partition load
START_VIEW_PERSISTENCY_RECORD_COUNT_MANUAL_PARTITIONS_SUCCESS=Anda telah memasukkan {0} rekod ke dalam jadual keterusan data untuk paparan "{1}" dalam {2} bahagian yang anda pilih
#XFLD: Message used by SEAL repository for view persistency for locked partition data load
START_VIEW_PERSISTENCY_RECORD_COUNT_LOCKED_PARTITIONS_SUCCESS={0} rekod dimasukkan dalam jadual keterusan data untuk paparan "{1}". Bahagian dikemas kini: {2}; Bahagian dikunci tidak berubah: {3}; Jumlah bahagian: {4}
#XMSG: Message for unexpected error (for messages created before version 2022.03)
START_PERSISTENCY_UNEXPECTED_ERROR=Ralat tidak dijangka berlaku semasa data keterusan untuk paparan "{0}".
#XMSG: Message for unexpected error (new message to replace START_PERSISTENCY_UNEXPECTED_ERROR)
START_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Ralat tidak dijangka berlaku semasa data keterusan untuk paparan "{0}".
#XMSG: Message for Locked space error
START_VIEW_PERSISTENCY_LOCKED_SPACE_ERROR=Paparan "{0}” tidak boleh direplikakan kerana ruang "{1}" dikunci.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
STOP_PERSISTENCY_UNEXPECTED_ERROR=Ralat tidak dijangka berlaku semasa mengeluarkan data keterusan untuk paparan "{0}".
#XMSG: Message for unexpected error (new message to replace STOP_PERSISTENCY_UNEXPECTED_ERROR)
REMOVE_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Ralat tidak dijangka berlaku semasa mengeluarkan keterusan untuk paparan "{0}".
#XMSG: Message for ivalidated view
VIEW_PERSISTENCY_ABORTED_DEFINITION_INVALID=Takrifan paparan "{0}" telah menjadi tidak sah, kemungkinan besar disebabkan oleh perubahan objek yang digunakan secara langsung atau tidak langsung oleh paparan. Cuba atur duduk paparan untuk menyelesaikan isu, atau untuk mengenal pasti punca.
#XMSG: Message for persisted data removal (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT=Data berterusan dikeluarkan ketika mengatur duduk paparan "{0}".
#XMSG: Message for persisted data removal of a consuming view
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW=Data berterusan dikeluarkan ketika mengatur duduk paparan digunakan "{0}".
#XMSG: Message for persisted data removal of a consuming view due to a dac change
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW_DAC_CHANGED=Data berterusan dikeluarkan ketika mengatur duduk paparan digunakan "{0}" kerana kawalan capaian datanya telah diubah.
#XMSG: Message for persisted data removal (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DEPLOYMENT=Data berterusan dikeluarkan ketika mengatur duduk paparan "{0}".
#XMSG: Message for deleted view (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION=Keterusan dikeluarkan dengan pemadaman paparan "{0}".
#XMSG: Message for deleted view (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DELETION=Keterusan dikeluarkan dengan pemadaman paparan "{0}".
#XMSG Message for removal of persisted data, because prerequisites not fulfilled
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_PREREQUISITES_NOT_FULFILLED=Data berterusan dikeluarkan kerana prasyarat keterusan data tidak lagi dipenuhi.
#XMSG: Message for inconsistent persistency
VIEW_PERSISTENCY_INCONSISTENT_PERSISTENCY=Keterusan paparan "{0}" menjadi tidak konsisten. Keluarkan data berterusan untuk menyelesaikan masalah.
#XMSG: Message for prerequisites
START_PERSISTENCY_CHECK_PREREQUISITES=Menyemak prasyarat untuk keterusan paparan "{0}".
#XMSG: Message for DAC deployed in deprecated way using table function and join (for messages created before 2025.05)
START_PERSISTENCY_DEPRECIATED_DAC=Paparan "{0}" diatur duduk menggunakan Kawalan Capaian Data (DAC) yang sedang dikecam. Atur duduk paparan sekali lagi untuk meningkatkan prestasi.
#XMSG: Message for DAC deployed in deprecated way using table function and join
START_PERSISTENCY_DEPRECATED_DAC=Paparan "{0}" diatur duduk menggunakan Kawalan Capaian Data (DAC) yang sedang dikecam. Atur duduk paparan sekali lagi untuk meningkatkan prestasi.
#XMSG: Message for roll back (for messages created before version 2022.03)
ROLLBACK_VIEW_PERSISTENCY=Ralat berlaku. Status keterusan sebelumnya dipulihkan untuk paparan "{0}".
#XMSG: Message used by SEAL repository for rollback of running procedure to persist view (new message to replace ROLLBACK_VIEW_PERSISTENCY)
VIEW_PERSISTENCY_ROLLBACK=Ralat berlaku. Proses untuk meneruskan paparan ''{0}'' dihentikan dan pertukaran dipulihkan semula.
#XMSG: Message used by SEAL repository for rollback of running procedure to remove persisted data of view
REMOVE_VIEW_PERSISTENCY_ROLLBACK=Ralat berlaku. Proses untuk mengeluarkan data berterusan bagi paparan "{0}" telah dihentikan dan pertukaran dipulihkan semula.
#XMSG: Message used by SEAL repository for persistency preparation
START_PERSISTENCY_PREPARATION=Penyediaan untuk meneruskan data.
#XMSG: Message used by SEAL repository for data insertion
START_INSERTING_PERSISTENCY_DATA=Memasukkan data dalam jadual keterusan.
#XMSG: Message for null values - partitioning (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS={0} rekod nilai sifar dimasukkan ke dalam bahagian "lain-lain".
#XMSG: Message used by SEAL repository for others partition values - partitioning (new message to replace START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS={0} rekod dimasukkan ke dalam bahagian "lain" untuk nilai "{2}" < "{1}" ATAU "{2}" >= "{3}".
#XMSG: Message used by SEAL repository for others partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS_V2=Anda telah masukkan {0} rekod ke dalam bahagian "lain" untuk nilai "{2}" < "{1}" ATAU "{2}" >= "{3}". Penggunaan ingatan: {4} GiB.
#XMSG: Message used by SEAL repository for null partition values - partitioning
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS={0} rekod dimasukkan ke dalam bahagian "lain" untuk nilai "{1}" ADALAH SIFAR.
#XMSG: Message used by SEAL repository for null partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS_V2=Anda telah masukkan {0} rekod ke dalam bahagian "lain" untuk nilai "{1}" ADALAH SIFAR. Penggunaan ingatan: {2} GiB.
##XFLD:Message used by SEAL repository for remote statistics
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS=Memuatkan data terlibat: {0} penyata jauh. Jumlah rekod yang anda peroleh: {1}. Jumlah masa jangka masa: {2} saat.
##XFLD:Message used by SEAL repository for remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS_WITH_PARTITIONS=Memuatkan data terlibat menggunakan {0} bahagian dengan {1} penyata jauh. Jumlah rekod yang anda peroleh: {2}. Jumlah masa jangka masa: {3} saat.
##XFLD:Message used by SEAL repository with UI ID for instructions of remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_INSTRUCTION=Anda boleh paparkan penyata jauh yang anda proses semasa jalanan dengan membuka pemantau pertanyaan jauh, dalam butiran mesej tertentu bahagian.
##XFLD:Message for start reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS=Memulakan proses untuk menggunakan semula keterusan data sedia ada untuk paparan {0} selepas pengerahan.
##XFLD:Message used by SEAL repository for reattach view persitency
START_VIEW_PERSISTENCY_REATTACHMENT=Mula menggunakan semula keterusan data sedia ada.
##XFLD:Message used by SEAL repository for reattach view persitency
START_FINILIZING_VIEW_PERSISTENCY_REATTACHMENT=Menggunakan semula keterusan data sedia ada.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_COMPLETE=Proses untuk menggunakan semula keterusan data sedia ada selesai untuk paparan {0}.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_FAILED=Cuba gunakan semula keterusan data sedia ada untuk paparan {0} selepas pengerahan.
#XMSG: Message used by SEAL repository for finalizing persistency
START_FINALIZING_PERSISTENCY=Memuktamadkan keterusan.
#XMSG: Message used by SEAL repository for restoring virtual access for view
REMOVE_VIEW_PERSISTENCY_VIRTUAL_ACCESS_RESTORED=Anda memulihkan semula capaian data maya untuk paparan ''{0}''.
#XMSG: Message used by SEAL repository for view that already has virtual access
REMOVE_VIEW_PERSISTENCY_ALREADY_VIRTUAL_ACCESS=Paparan "{0}" telah mempunyai capaian data maya. Tiada data berterusan dikeluarkan.
#XMSG: Message for removing view persistency
VIEW_PERSISTENCY_REMOVED_FROM_MONITOR=Paparan ''{0}'' dikeluarkan dari pemantau Paparan.
#XMSG: Message for view doesnt exist.
VIEW_PERSISTENCY_VIEW_NOT_EXISTING=Paparan "{0}" tidak wujud dalam pangkalan data atau tidak diatur duduk dengan betul, dan oleh itu tidak boleh diteruskan. Cuba atur duduk semula paparan untuk menyelesaikan isu, atau untuk mengenal pasti punca.
#XMSG: Message for persistency not enabled.
VIEW_PERSISTENCY_NOT_ENABLED=Keterusan data tidak berjaya didayakan. Atur duduk semula jadual/paparan dalam ruang "{0}" untuk dayakan kefungsian.
#XMSG: Message for unexpected error due to node crash
VIEW_PERSISTENCY_UNEXPECTED_ERROR_GENERAL=Jalanan keterusan paparan terakhir tidak berjaya dijalankan kerana masalah teknikal.
#XFLD: Message for view persistency memory consumption
VIEW_PERSISTENCY_MEMORY_CONSUMPTION=Anda telah gunakan {0} GiB ingatan puncak dalam masa jalanan keterusan paparan.
#XFLD: Message for view persistency timeout
VIEW_PERSISTENCY_TIMEOUT=Keterusan paparan {0} mencapai tamat masa {1} jam.
#XFLD: Message for not started asynchronous view persistency
VIEW_PERSISTENCY_ASYNC_NOT_STARTED=Muatan sistem tinggi menghalang pelaksanaan tak segerak bagi keterusan paparan daripada bermula. Semak jika terlalu banyak tugas berjalan secara selari.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency (for messages created before version 2022.05)
START_VIEW_PERSISTENCY_EXECUTION_METHOD_DROP_AND_RENAME=Jadual keterusan sedia ada telah dipadam dan digantikan dengan jadual keterusan baharu.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_RECREATE_AND_DROP=Jadual keterusan sedia ada telah dipadam dan digantikan dengan jadual keterusan baharu.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_TRUNCATE_AND_COPY=Jadual keterusan sedia ada telah dikemas kini dengan data baharu.
##XFLD: Message for missing authorizations for view persistency
VIEW_PERSISTENCY_MISSING_AUTH=Tiada sah kuasa untuk keterusan data.
##XFLD: Message start of persistency cancellation
VIEW_PERSISTENCY_TRIGGER_CANCEL=Mula membatalkan proses untuk meneruskan paparan {0}.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_UNAVAILABLE_TASK_RUN=Cuba semula batalkan proses untuk meneruskan paparan kerana tiada tugas keterusan data berjalan untuk paparan {0}.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_NONDETERMINISTIC_TASK_RUN=Cuba semula batalkan proses untuk meneruskan paparan kerana tiada tugas keterusan data sedang berjalan untuk paparan {0}.
##XFLD: Message for canceling a non running persitency task
VIEW_PERSISTENCY_CANCEL_NON_RUNNING_SELECTED_TASK=Cuba semula batalkan proses untuk meneruskan paparan {0} kerana tugas keterusan data dipilih {1} tidak berjalan.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_UNREACHABLE_PERSISTENCY_RUN=Cuba semula batalkan proses untuk meneruskan paparan kerana keterusan data untuk paparan {0} masih belum bermula.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_COMPLETED_PERSISTENCY_RUN=Cuba semula batalkan proses untuk meneruskan paparan {0} kerana ia telah selesai.
##XFLD: Message for failed cancellation
VIEW_PERSISTENCY_ABORTED_CANCEL_EXECUTION=Cuba semula batalkan proses untuk meneruskan paparan {0}.
##XFLD: Message for finalizing persistency cancellation
VIEW_PERSISTENCY_FINALIZE_CANCEL=Anda telah serahkan proses untuk menghentikan keterusan data paparan {0}.
##XFLD: Message used in persist task when cancellation is caught
VIEW_PERSISTENCY_CANCELED=Anda telah hentikan proses untuk meneruskan paparan {0}.
##XFLD: Message used in persist task when cancelation is caught and performed through a cancelation task
VIEW_PERSISTENCY_CANCELED_VIA_CANCEL_TASK=Anda telah hentikan proses untuk meneruskan paparan {0} melalui tugas pembatalan {1}.
##XFLD: Message used in cancel task when the task is triggered by deployment
VIEW_PERSISTENCY_CANCEL_TRIGGERED_BY_DEPLOYMENT=Membatalkan proses untuk meneruskan data semasa mengatur duduk paparan {0}.
##XFLD: Message used in case the cancelation is retriggered for a running task
VIEW_PERSISTENCY_CANCEL_ATTEMPT_RETRIGGER_CANCEL=Anda telah serahkan tugas pembatalan sebelumnya untuk meneruskan paparan {0}.
##XFLD: Message used to express the delay that may occur before the persistency task is stopped
VIEW_PERSISTENCY_CANCEL_DELAY_STOPPING_PERSISTENCY=Mungkin terdapat kelewatan sehingga tugas keterusan data untuk paparan {0} terhenti.
##XFLD: Message used to reflect the task log identifier of the current running persistency task
VIEW_PERSISTENCY_CANCEL_CURRENT_RUNNING_TASK=Anda sedang meneruskan data untuk paparan {0} dengan tugas {1}.
##XFLD:Message used by SEAL repository for locked partitions and DACs
VIEW_PERSISTENCY_LOCKED_PARTITIONS_DACS=Sah kuasa yang DAC sediakan telah berubah dan bahagian terkunci tidak mempertimbangkannya. Buka kunci bahagian dan muatkan snapshot baharu untuk menggunakan perubahan.
##XFLD:Message used by SEAL repository for column structure change
VIEW_PERSISTENCY_COLUMN_STRUCTURE_CHANGE=Struktur lajur telah berubah dan tidak lagi sepadan dengan jadual keterusan sedia ada. Keluarkan data berterusan dan mulakan keterusan data baharu untuk mengemas kini jadual keterusan anda.
##XFLD: Message for out of memory
VIEW_PERSISTENCY_HANA_OUT_OF_MEMORY=Tugas gagal kerana ralat kehabisan ingatan pada pangkalan data SAP HANA.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_EXCEPTION_TRANSACTION_ROLLBACK=Tugas tidak berjaya kerana pengecualian dalaman pada pangkalan data SAP HANA.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_SQL_EXECUTION_ISSUE=Tugas tidak berjaya kerana isu pelaksanaan SQL dalaman pada pangkalan data SAP HANA.
##XFLD: Message for HANA out of memory result
VIEW_PERSISTENCY_OUT_OF_MEMORY_RESULT_MESSAGE=Sebab acara HANA kehabisan ingatan: {0}
##XFLD: Message for admission control rejection
VIEW_PERSISTENCY_HANA_ADMISSION_CTRL_REJECTION=Tugas gagal kerana Penolakan Kawalan Kemasukan SAP HANA.
##XFLD: Message for HANA back pressure / rate limit of HANA client
VIEW_PERSISTENCY_HANA_CLIENT_RATE_LIMIT_REJECTION=Tugas gagal kerana terlalu banyak sambungan SAP HANA yang aktif.
#XMSG: Message for invalid persisted table name
VIEW_PERSISTENCY_PERSISTED_TABLE_INVALID=Ralat telah berlaku dan jadual keterusan menjadi tidak sah. Untuk menyelesaikan isu, keluarkan data keterusan dan teruskan paparan semula.
#XMSG: Message for usahe of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION=Tidak boleh teruskan paparan ini. Ia menggunakan jadual jauh berdasarkan sumber jauh dengan rambatan pengguna yang didayakan. Semak asal-usul paparan.
#XMSG: Message for dynamic usage of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION_DYNAMIC=Tidak boleh teruskan paparan ini. Ia menggunakan jadual jauh berdasarkan sumber jauh dengan rambatan pengguna yang didayakan. Jadual jauh mungkin digunakan secara dinamik melalui paparan skrip SQL. Asal-usul paparan mungkin tidak menunjukkan jadual jauh.
#XMSG: Message for insufficient privileges or consumption of view with DAC via dynamic SQL
VIEW_PERSISTENCY_INSUF_PRIV_OR_DAC_DYN_SQL=Keistimewaan anda mungkin tidak mencukupi. Buka Pratonton Data untuk melihat sama ada anda mempunyai keistimewaan yang diperlukan. Jika ya, paparan kedua yang digunakan melalui skrip SQL dinamik mungkin mempunyai kawalan capaian data (DAC) yang digunakan padanya.
#XMSG: deployment of DAC is deprecated and view need to be redeployed to be able to persist data
VIEW_PERSISTENCY_DEPRECATED_DAC=Paparan "{0}" diatur duduk menggunakan Kawalan Capaian Data (DAC) yang sedang dikecam. Atur duduk paparan sekali lagi untuk meneruskan data bagi paparan.
##XFLD:Message used by SEAL repository for input parameter
START_VIEW_PERSISTENCY_INPUT_PARAMETER=Menggunakan nilai lalai "{0}" untuk parameter input "{1}".
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Elastic compute node keys~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
##XFLD:Message used to reflect disablement of ecn replica
ECN_DISABLE_EXISTING_REPLICA=Replika nod kira elastik dinyahdayakan.
##XFLD:Message used to reflect recreation of ecn replica
ECN_RECREATE_EXISTING_REPLICA=Replika nod kira elastik dicipta semula.
##XFLD:Message used to reflect re-enablement of ecn replica
ECN_ENABLE_EXISTING_REPLICA=Replika nod kira elastik didayakan semula.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Task Schedule Menu key~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message to schedule task
scheduleText=Jadual
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Cipta Jadual
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Edit Jadual
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Padam Jadual
#XFLD: Refresh frequency field
refreshFrequency=Kekerapan Segar Semula
#XFLD: Refresh frequency field
refreshFrequencyNew=Kekerapan
#XFLD: Refresh frequency field
refreshFrequencyNewNew=Kekerapan Berjadual
#XBUT: label for None
none=Tiada
#XBUT: label for Real-Time replication state
realtime=Masa Nyata
#XFLD: Label for table column
txtNextSchedule=Jalanan Seterusnya
#XFLD: Label for table column
txtNextScheduleNew=Jalanan Seterusnya Dijadualkan
#XFLD: Label for table column
txtNumOfRecords=Bilangan Rekod
#XFLD: Label for scheduled link
scheduledTxt=Dijadualkan
#XFLD: LABEL for partially persisted link
partiallyPersisted=Berterusan Sebahagian
#XFLD: Text for paused text
paused=Dihentikan Sementara

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ View Monitor Tasklog Details Action keys~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message for release lock actionn
releaseLockMsg=Jika jalanan lebih lama daripada biasa, berkemungkinan jalanan perlu dilakukan semula, dan statusnya belum dikemas kini. \r\n Untuk menyelesaikannya, anda boleh buka kunci dan tetapkan status kepada gagal.
#XFLD: Label for release lock dialog
releaseLockText=Buka Kunci

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Column header tooltip ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: tooltip for table column
txtViewTooltip=Nama paparan berterusan
#XFLD: tooltip for table column
txtViewDataAccessTooltip=Berikut menunjukkan ketersediaan paparan
#XFLD: tooltip for table column
txtViewRefreshFreqTooltip=Berikut menunjukkan sama ada jadual ditakrifkan untuk paparan atau tidak
#XFLD: tooltip for table column
txtViewStatusTooltip=Dapatkan status paparan berterusan
#XFLD: tooltip for table column
txtViewLatestUpdateTooltip=Berikan maklumat mengenai masa paparan berterusan terakhir dikemas kini
#XFLD: tooltip for table column
txtViewNextRunTooltip=Jika jadual ditetapkan untuk paparan, lihat masa jalanan seterusnya dijadualkan.
#XFLD: tooltip for table column
txtViewNumOfRecordsTooltip=Jejaki bilangan rekod.
#XFLD: tooltip for table column
txtViewInMemorySizeTooltip=Jejaki jumlah saiz yang paparan gunakan dalam ingatan anda
#XFLD: tooltip for table column
txtViewDiskSizeTooltip=Jejaki jumlah saiz yang paparan ambil pada cakera anda
#XMSG: Expired text
txtExpired=Telah Tamat Tempoh

#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Objek "{0}" tidak boleh ditambah ke rantaian tugas.

#~~~~~~~~~~~View Analyzer task log messages~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message indicating number of records for a view, and memory consumption in Mebibyte during simulation of view persistency
RECORDS_PEAK_MEMORY=Paparan "{0}" mempunyai {1} rekod. Simulasi keterusan data untuk paparan ini menggunakan {2} memori MiB.
#XMSG: View Analyzer execution failure
VIEW_ANALYZER_EXECUTION_FAILED=Tidak berjaya melaksanakan Paparan Penganalisis.
#XMSG: Missing authorizations for View Analyzer
VIEW_ANALYZER_MISSING_AUTH=Sah kuasa diperlukan untuk Penganalisis Paparan.
#XMSG: Maximum memory in Gibibyte is reached, therefore persistency simulation is stopped
MAXIMUM_MEMORY_REACHED=Anda telah mencapai memori maksimum {0} GiB semasa simulasi keterusan data untuk paparan "{1}" Oleh itu, tiada lagi jalanan simulasi keterusan data.
#XMSG: Error during persistency simulation occurred
PERSISTENCY_SIMULATION_ERROR=Ralat berlaku semasa simulasi keterusan data untuk paparan "{0}".
#XMSG: Persistency simulation is not executed, because prerequisites are not fulfilled and the view cannot be persisted
PREREQUISITES_NOT_FULFILLED=Simulasi keterusan data tidak dilaksanakan untuk paparan "{0}", kerana prasyarat tidak dipenuhi dan paparan tidak boleh diteruskan.
#XMSG: Persistency simulation is not executed, because the view does not exist in the database
VIEW_DOES_NOT_EXIST=Anda mesti atur duduk paparan "{0}" untuk mendayakan simulasi keterusan data.
#XMSG: Number of records cannot be determined, because the local table does not exist in the database
TABLE_DOES_NOT_EXIST=Masukkan jadual tempatan "{0}" dalam pangkalan data untuk menentukan bilangan rekod bagi jadual ini.
##XMSG: Message start of cancellation for View Analyzer task
CANCEL_VIEW_ANALYZER_TRIGGERED=Anda telah serahkan proses untuk menghentikan tugas Penganalisis Paparan {0} untuk paparan "{1}". Mungkin terdapat kelewatan sehingga tugas terhenti.
##XMSG: Message start to indicate that task is not active
CANCEL_VIEW_ANALYZER_NO_ACTIVE_TASK=Tugas Penganalisis Paparan {0} untuk paparan "{1}" tidak aktif.
#XMSG: Cancel View Analyzer task failure
CANCEL_VIEW_ANALYZER_FAILED=Cuba batalkan tugas Penganalisis Paparan semula.
##XMSG: Message used in View Analyzer execution task when task is canceled
VIEW_ANALYZER_CANCELED=Pelaksanaan Penganalisis Paparan untuk paparan "{0}" terhenti melalui tugas pembatalan.
##XMSG: Message start of cancelation for Model Validation task
CANCEL_MODEL_VALIDATION_TRIGGERED=Anda telah serahkan proses untuk menghentikan tugas Pengesahan Model {0} untuk paparan "{1}". Mungkin terdapat kelewatan sehingga tugas terhenti.
##XMSG: Message to indicate that task is not active
CANCEL_MODEL_VALIDATION_NO_ACTIVE_TASK=Tugas Pengesahan Model {0} untuk paparan "{1}" tidak aktif.
#XMSG: Cancel Model Validation task failure
CANCEL_MODEL_VALIDATION_FAILED=Cuba batalkan semula tugas Pengesahan Model.
##XMSG: Message used in Model Validation task when task is canceled
MODEL_VALIDATION_CANCELED=Pelaksanaan Pengesahan Model untuk paparan "{0}" terhenti melalui tugas pembatalan.
#XMSG: Message for Locked space error, before starting Model Validation task
MODEL_VALIDATION_LOCKED_SPACE_ERROR=Buka kunci untuk melaksanakan Pengesahan Model untuk paparan "{0}", kerana ruang "{1}" dikunci.
##XMSG: Message used in View Analyzer execution task when determination of number of rows fails
NUMBER_ROWS_DETERMINATION_FAILED=Tentukan semula bilangan baris untuk jadual tempatan "{0}".
##XMSG: Message to indicate that SQL plan was created
EXECUTION_PLAN_CREATED=Fail rancangan Penganalisis SQL untuk paparan "{0}" telah dicipta dan boleh dimuat turun.
#XFLD: Message to indicate starting of process to generate SQL Analyzer plan file
START_PLANVIZ_EXECUTION=Memulakan proses untuk menjana fail rancangan Penganalisis SQL untuk paparan "{0}".
#XFLD: Message to indicate starting of View Analyzer process
VIEW_ANALYZER_EXECUTION_STARTED=Memulakan pelaksanaan Penganalisis Paparan.
##XMSG: Message to indicate that SQL plan file cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_PLANVIZ_NOT_FULFILLED=Tidak boleh menjana fail rancangan Penganalisis SQL untuk paparan "{0}", kerana anda perlu penuhi prasyarat keterusan data.`
##XMSG: Error message during  generation of SQL Analyzer plan file
PLANVIZ_ERROR=Cuba semula penjanaan fail rancangan Penganalisis SQL untuk paparan "{0}".
#XMSG: Message for Locked space error
VIEW_ANALYZER_LOCKED_SPACE_ERROR=Cuba semula laksanakan Penganalisis Paparan untuk paparan "{0}" kerana ruang "{1}" terkunci.
#XMSG: Message for persistency simulation without partitioning
PERSISTENCY_SIMULATION_WITHOUT_PARTITIONS=Bahagian paparan "{0}" tidak dipertimbangkan semasa simulasi keterusan data.
#XMSG: Message for generation of SQL Analyzer plan file without considering partitioning
PLANVIZ_WITHOUT_PARTITIONS=Bahagian paparan "{0}" tidak dipertimbangkan semasa penjanaan fail rancangan Penganalisis SQL.
#XMSG: Confirmation message for mass removal of persisted data
MassRemovePersistency_Confirm_Msg=Anda ingin mengeluarkan keterusan data dan menukar capaian data kembali kepada capaian maya?
#XMSG: Confirmation message for partial mass removal of selected persisted data
PartialMassRemovePersistency_Confirm_Msg={0} daripada {1} paparan yang anda pilih mempunyai keterusan data. \n Anda ingin mengeluarkan keterusan data dan menukar capaian data kembali ke capaian maya?
#XMSG: success message for stopping mass persistence
stopMassPersistenceSuccess=Kami mengeluarkan keterusan data untuk paparan yang anda pilih.
#XMSG: error message during mass stop persistence of a view (backend error)
STOP_MASS_PERSISTENCE_ERROR=Ralat berlaku semasa menghentikan keterusan untuk paparan yang anda pilih.
#XMSG: memory analysis is only performed in space {0}
NO_MEMORY_ANALYSIS=Analisis ingatan dijalankan untuk entiti dalam ruang "{0}" sahaja: "{1}" "{2}" telah dilangkau.
##XMSG: Message to indicate that Explain Plan cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_EXPLAINPLAN_NOT_FULFILLED=Tidak boleh menjana fail Rancangan Penerangan untuk paparan "{0}", kerana anda perlu penuhi prasyarat keterusan data.
#XMSG: Message for Explain Plan generation without partitioning
EXPLAINPLAN_WITHOUT_PARTITIONS=Bahagian paparan "{0}" tidak dipertimbangkan semasa penjanaan fail Rancangan Penerangan.
#XFLD: Message to indicate starting of process to generate Explain Plan file
START_EXPLAINPLAN_EXECUTION=Memulakan proses untuk menjana fail Rancangan Penerangan untuk paparan "{0}".
##XMSG: Message to indicate that Explain Plan was created
EXPLAINPLAN_CREATED=Fail Rancangan Penerangan untuk paparan "{0}" telah dijanakan. Anda boleh memaparkannya dengan mengklik "Paparkan Butiran", atau memuat turunnya jika anda mempunyai kebenaran yang berkaitan.
##XMSG: Error message during  generation of Explain Plan file
EXPLAINPLAN_ERROR=Cuba semula penjanaan fail Rancangan Penerangan untuk paparan "{0}".
##XMSG: Explain Plan file cannot be generated for the view {0}, because too many views are stacked on each other
EXPLAINPLAN_MODEL_TOO_COMPLEX=Fail Rancangan Penerangan tidak boleh dijanakan untuk paparan "{0}". Terlalu banyak paparan bertindan dengan satu sama lain. Model kompleks boleh menyebabkan ralat ingatan dan prestasi perlahan. Tindakan meneruskan paparan adalah dicadangkan.

##XMSG: Message to indicate that the space is locked
PERFORMANCE_ANALYSIS_LOCKED_SPACE_ERROR=Cuba semula laksanakan analisis prestasi untuk paparan "{0}" kerana ruang "{1}" terkunci.
##XMSG: The performance analysis task was cancelled
PERFORMANCE_ANALYSIS_CANCELED=Analisis prestasi untuk paparan "{0}" telah dibatalkan.
##XMSG: The performance analysis failed
PERFORMANCE_ANALYSIS_FAILED=Analisis prestasi tidak berjaya.
##XMSG: The performance analysis is finished
PERFORMANCE_ANALYSIS_FINISHED=Analisis prestasi untuk paparan "{0}" telah selesai. Paparkan hasil dengan mengklik "Paparkan Butiran".
##XMSG: The view has input parameter with no default value
INPUT_PARAMETER_WITHOUT_DEFAULT_VALUE=Paparan ini tidak boleh dianalisis kerana ia mempunyai parameter tanpa nilai lalai.
##XMSG: The view is not fully deployed
VIEW_NOT_FULLY_DEPLOYED=Paparan ini tidak boleh dianalisis kerana ia tidak diatur duduk sepenuhnya.
##XMSG: The view uses limited remote adapters
LIMITED_REMOTE_ADAPTER=Paparan ini menggunakan sekurang-kurangnya satu penyesuai jauh dengan keupayaan terhad seperti tiada penapis tolak ke bawah atau sokongan untuk 'Kiraan'. Objek yang berterusan atau mereplikakan boleh meningkatkan prestasi masa jalanan.
##XMSG: The view uses remote adapter that does not support 'Limit'
REMOTE_ADAPTER_NOT_SUPPORTING_LIMIT=Paparan ini menggunakan sekurang-kurangnya satu penyesuai jauh yang tidak menyokong 'Had'. Lebih daripada 1000 rekod mungkin telah dipilih.
##XMSG: Performance analysis is executed by using default parameters of view'
INPUT_PARAMETER_WITH_DEFAULT_VALUE=Analisis prestasi dilaksanakan dengan menggunakan nilai lalai parameter paparan.
##XMSG: Message to indicate that an error occurred
PERFORMANCE_ANALYSIS_ERROR=Ralat berlaku semasa analisis prestasi untuk paparan "{0}".
##XMSG: Message start of cancellation for Performance Analysis task
CANCEL_PERFORMANCE_ANALYSIS_TRIGGERED=Anda telah serahkan proses untuk menghentikan tugas Analisis Prestasi {0} untuk paparan "{1}". Mungkin terdapat kelewatan sehingga tugas terhenti.
##XMSG: Message start to indicate that task is not active
CANCEL_PERFORMANCE_ANALYSIS_NO_ACTIVE_TASK=Tugas Analisis Prestasi {0} untuk paparan "{1}" tidak aktif.
#XMSG: Cancel Performance Analysis task failure
CANCEL_PERFORMANCE_ANALYSIS_FAILED=Pembatalan tugas Analisis Prestasi tidak berjaya.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Umpukkan Jadual kepada Saya
#XBUT: Pause schedule menu label
pauseScheduleLabel=Hentikan Seketika Jadual
#XBUT: Resume schedule menu label
resumeScheduleLabel=Sambung Semula Jadual
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Ralat berlaku semasa mengeluarkan jadual.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Ralat berlaku semasa mengumpukkan jadual.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Ralat berlaku semasa menghentikan seketika jadual.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Ralat berlaku semasa menyambung semula jadual.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Memadam {0} jadual
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Mengubah pemilik bagi {0} jadual
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Menghentikan seketika {0} jadual
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Menyambung semula {0} jadual
#XBUT: Select Columns Button
selectColumnsBtn=Pilih Lajur
#XFLD: Refresh tooltip
TEXT_REFRESH=Segar Semula
#XFLD: Select Columns tooltip
text_selectColumns=Pilih Lajur


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Runtime Metrics ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Label for Runtime Metrics
RuntimeMetricsTitle=Metrik Masa Jalanan untuk
#XFLD : Label for Run Button
runButton=Jalankan
#XFLD : Label for Cancel Button
cancelButton=Batalkan
#XFLD : Label for Close Button
closeButton=Tutup
#XFLD : Label for Open View Analyzer Button
openViewAnalyzerButton=Buka Penganalisis Paparan
#XFLD : Label for Generate Explain Plan Button
generateExplainPlanButton=Janakan Rancangan Penerangan
#XFLD : Label for Previous Run Column
previousRun=Jalanan Sebelumnya
#XFLD : Label for Latest Run Column
latestRun=Jalanan Terkini
#XFLD : Label for time Column
time=Masa
#XFLD : Label for Duration Column
duration=Jangka Masa
#XFLD : Label for Peak Memory Column
peakMemory=Ingatan Puncak
#XFLD : Label for Number of Rows
numberOfRows=Bilangan Baris
#XFLD : Label for Overall Number of Sources
overallNumberOfSources=Bilangan Keseluruhan Sumber
#XFLD : Label for Data Access Column
dataAccess=Capaian Data
#XFLD : Label for Local Tables
localTables=Jadual Tempatan
#XFLD : Label for federatedRemoteTables
federatedRemoteTables=Jadual Jauh Bersekutu (dengan Keupayaan Penyesuai Terhad)
#XTXT Text for initial state of the runtime metrics
initialState=Anda mesti menjalankan Analisis Prestasi terlebih dahulu untuk mendapatkan metrik. Ini mungkin mengambil sedikit masa, tetapi anda boleh membatalkan proses jika perlu.
#XMSG: Cancel confirmation message
CancelRunConfirmationMessage=Adakah anda pasti ingin batalkan jalanan Analisis Prestasi semasa?
#XTIT: Cancel dialog title
CancelRunTitle=Batalkan Jalanan
#XFLD: Label for Number of Rows
NUMBER_ROWS=Bilangan Baris
#XFLD: Label for Overall Number of Sources
OVERALL_NUMBER_SOURCES=Bilangan Keseluruhan Sumber
#XFLD: Label for Data Access
DATA_ACCESS=Capaian Data
#XFLD: Label for Remote tables
NUMBER_REMOTE_TABLES_LIMITED_ADAPTERS=Jadual Jauh Bersekutu (dengan Keupayaan Penyesuai Terhad)
#XFLD: Label for select statement
SELECT_STATEMENT='SELECT * FROM VIEW LIMIT 1000'
#XFLD: Label for duration
SELECT_RUNTIME=Jangka Masa
#XFLD: Label for peak memory
SELECT_PEAK_MEMORY=Ingatan Puncak
#XFLD: Label for count statement
SELECT_COUNT_STATEMENT='PILIH KIRAAN(*) DARIPADA PAPARAN'
#XFLD: Label for count duration
SELECT_COUNT_RUNTIME=Jangka Masa
#XFLD: Label for count peak memory
SELECT_COUNT_PEAK_MEMORY=Ingatan Puncak
#XFLD: Label for count of number of local tables
NUMBER_LOCAL_TABLES_FILE=Jadual Tempatan (Fail)
#XTXT: Text for running state of the runtime metrics
Running=Sedang Berjalan...
#XFLD: Label for time
Time=Masa
#XFLD: Label for virtual access
PA_VIRTUAL=Maya
#XFLD: Label for persisted access
PA_PERSISTED=Berterusan
PA_PARTIALLY_PERSISTED=Berterusan Sebahagian
#XTXT: Text for cancel
CancelRunSuccessMessage=Membatalkan jalanan Analisis Prestasi.
#XTXT: Text for cancel error
CancelRunErrorMessage=Ralat berlaku semasa membatalkan jalanan Analisis Prestasi.
#XTXT: Text for explain plan generation
ExplainPlanStarted=Menjana Rancangan Penerangan untuk paparan "{0}".
#XTXT: Text for performance analysis
PerformanceAnalysisStarted=Memulakan Analisis Prestasi untuk paparan "{0}".
#XTXT: Text for performance analysis error
ErrorFetchingPerformanceAnalysis=Ralat berlaku semasa mengambil data Analisis Prestasi.
#XTXT: Text for performance analysis error
conflictingTask=Tugas Analisis Prestasi telah berjalan
#XFLD: Label for Errors
Errors=Ralat
#XFLD: Label for Warnings
Warnings=Amaran
#XFLD: Tooltip for missing privileges
openViewAnalyzerButtonTooltipNoPrivilege=Anda memerlukan keistimewaan DWC_DATAINTEGRATION(kemas kini) untuk membuka Penganalisis Paparan.
#XFLD: Tooltip for missing privileges
generateExplainPlanButtonTooltipNoPrivilege=Anda memerlukan keistimewaan DWC_RUNTIME(baca) untuk menjana Rancangan Penerangan.



#XFLD: Label for frequency column
everyLabel=Setiap
#XFLD: Plural Recurrence text for Hour
hoursLabel=Jam
#XFLD: Plural Recurrence text for Day
daysLabel=Hari
#XFLD: Plural Recurrence text for Month
monthsLabel=Bulan
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minit
