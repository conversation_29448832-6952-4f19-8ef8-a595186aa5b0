
#XBCB: Breadcrumb
viewMonitorBreadcrumb={spaceId}

#XFLD: Label for persisted view column
SOURCE=Kaynak
#XFLD: Label for persisted view column
NAME=Ad
#XFLD: Label for persisted view column
NAME_LABEL=İş adı
#XFLD: Label for persisted view column
NAME_LABELNew=Nesne (iş adı)
#XFLD: Label for persisted view column
TECHINCAL_NAME=Teknik ad
#XFLD: Label for persisted view column
TECHINCAL_NAMENew=Nesne (teknik ad)
#XFLD: Label for persisted view column
DATA_PERSISTENCY=Veri erişimi
#XFLD: Label for persisted view column
STATUS=Durum
#XFLD: Label for persisted view column
LAST_UPDATED=Son güncelleme
#XFLD: Label for persisted view column
IN_MEMORY_SIZE=Depolama için kullan<PERSON>lan bellek (MiB)
#XFLD: Label for persisted view column
DISK_SIZE=Depolama için kullan<PERSON>lan disk (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_NEWW=Bellek içi boyut (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_HEADER=Bellek içi boyut
#XFLD: Label for persisted view column
DISK_SIZE_NEWW=Diskteki boyut (MiB)
#XFLD: Label for persisted view column
DISK_SIZE_HEADER=Diskteki boyut
#XFLD: Label for schedule owner column
txtScheduleOwner=Planlama sorumlusu
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Planlamayı oluşturan kişiyi gösterir
#XFLD: Label for persisted view column
PERSISTED=Kalıcı
#XFLD: Label for persisted view column
TYPE=Tür
#XFLD: Label for View Selection Dialog column
changedOn=Değişiklik zamanı
#XFLD: Label for View Selection Dialog column
createdBy=Oluşturan
#XFLD: Label for log details column
txtViewPersistencyLogs=Günlükleri görüntüle
#XFLD: Label for log details column
txtViewPersistencyLogsNew=Ayrıntılar
#XFLD: text for values shown for Ascending sort order
SortInAsc=Artan düzende sırala
#XFLD: text for values shown for Descending sort order
SortInDesc=Azalan düzende sırala
#XFLD: Label for persisted view monitor heading
ViewMonitor_Heading=Görünüm izleme
#XFLD: Label for persisted view monitor sub-heading
ViewMonitor_SubHeading=Görünüm veri kalıcılığını izleme ve ilgili bakım işlemlerini yapma


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ DynamicPageHeader ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: text for values shown in column Persistence Status
txtLoading=Yükleniyor
#XFLD: text for values shown in column Persistence Status
txtRunning=Çalışıyor
#XFLD: text for values shown in column Persistence Status
txtAvailable=Kullanılabilir
#XFLD: text for values shown in column Persistence Status
txtError=Hata
#XMSG: Error message for unknown Persistence type that gets logged in Kibana
unknownReplicationTypeError=Çoğaltma türü "{0}" desteklenmiyor.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Content ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Son veri kalıcılığı çalıştırması için kullanılan ayarlar:
#XMSG: Message for input parameter name
inputParameterLabel=Girdi parametresi
#XMSG: Message for input parameter value
inputParameterValueLabel=Değer
#XMSG: Message for persisted data
inputParameterPersistedLabel=Kalıcı hale getirilme zamanı
#XTIT: Count for View in ViewMonitor Toolbar
txtViewCount=Görünümler ({0})
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Görünüm kalıcılığı
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Veri kalıcılığı
#XBUT: Button to clear the selected view persistance
ClearViewPersistance=Temizle
#XBUT: Button to stop the selected view persistance
stopPersistance=Kalıcılığı durdur
#XFLD: Placeholder for Search field
txtSearch=Ara
#XBUT: Tooltip for refresh button
txtRefresh=Yenile
#XBUT: Tooltip for add view button
txtDeleteView=Kalıcılığı sil
#XBUT: Tooltip for load new peristence
loadNewPersistence=Kalıcılığı yeniden başlat
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Yeni anlık görüntü yükle
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Veri kalıcılığını başlat
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Kalıcı verileri kaldır
#XMSG: success message for starting persistence
startPersistenceSuccess="{0}" görünümünü kalıcı hale getiriyoruz.
#XMSG: success message for stopping persistence
stopPersistenceSuccess="{0}" görünümü için kalıcı verileri kaldırıyoruz.
#XMSG: success message for stopping persistence
deletePersistedViewSuccess="{0}" görünümünü, izleme listesinden kaldırıyoruz.
#XMSG: error message during start persistence of a view (backend error)
ERROR_STARTING_PERSISTENCE="{0}" görünümü için veri kalıcılığı başlatılırken hata oluştu.
#XMSG: error message during start persistence of a view (backend error)
PARAMERROR_STARTING_PERSISTENCE=Görünüm "{0}" giriş parametreleri içerdiğinden kalıcı hale getirilemiyor.
#XMSG: error message during start persistence of a view (backend error)
NUMBER_OF_PARAMETER_STARTING_PERSISTENCE=Birden fazla giriş parametresi içerdiğinden görünüm "{0}" kalıcı hale getirilemiyor.
#XMSG: error message during start persistence of a view (backend error)
PARAMETER_DEFAULT_VALUE_STARTING_PERSISTENCE=Giriş parametresi varsayılan değer içermediğinden görünüm "{0}" kalıcı hale getirilemiyor.
#XMSG: error message during start persistence of a view (backend error)
REDEPLOYMENT_DAC_REQUIRED=Veri kalıcılığını desteklemek için "{0}" veri erişimi denetiminin (DAC) yeniden dağıtılması gerekir.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_STARTING_PERSISTENCE=Görünüm "{0}", veri erişimi denetimi (DAC) içeren "{1}" görünümünü kullandığından kalıcı hale getirilemiyor.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_EXTERNAL_STARTING_PERSISTENCE=Görünüm "{0}", farklı bir alana ait olan veri erişimi denetiminin (DAC) bulunduğu bir görünümü kullandığından kalıcı hale getirilemiyor.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_STRUCTURE_STARTING_PERSISTENCE=Görünüm "{0}", veri erişimi denetimlerinden (DAC) birinin veya daha fazlasının yapısı veri kalıcılığını desteklemediğinden kalıcı hale getirilemiyor.
#XMSG: error message during stop persistence of a view (backend error)
STOP_PERSISTENCE_ERROR="{0}" görünümü için kalıcılık durdurulurken hata oluştu.
#XMSG: error message during stop persistence of a view (backend error)
DELETE_PERSISTED_VIEW_ERROR="{0}" kalıcı görünümü silinirken hata oluştu.
#XMSG: Warning message for removing the persisted data
RemovePersistency_Confirm_Msg=Kalıcı verileri silmek ve "{0}" görünümünün sanal erişimine geçiş yapmak istiyor musunuz?
#XMSG: Warning message for delete the view persistence
DeletePersistency_Confirm_Msg=Görünümü izleme listesinden kaldırmak ve "{0}" görünümünün kalıcı verilerini silmek istiyor musunuz?
#XMSG: error message for reading data from backend
txtReadBackendError=Görünüşe göre arka uçtan okuma sırasında hata oluştu.
#XFLD: Label for No Data Error
NoDataError=Hata
#XMSG: message for conflicting task
Task_Already_Running=Görünüm "{0}" için çakışan bir görev zaten çalışıyor.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Show All Views~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Text for show all views button
allViewsText=Tüm görünümler ({0})
#XBUT: Text for show scheduled views button
scheduledText=Planlandı ({0})
#XBUT: Text for show persisted views button
persistedText=Kalıcı ({0})
#XBUT: Text for start analyzer button
startAnalyzer=Görünüm analistini başlat
#XFLD: Message if repository is unavailable
repositoryErrorMsg=Havuz kullanılamıyor ve belirli özellikler devre dışı.

#XFLD: Data Access - Virtual
Virtual=Sanal
#XFLD: Data Access - Persisted
Persisted=Kalıcı

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Non-Persisted View Selection Dialog~~~~~~~~~~~~~~~~~~~~~~~

#XTIT: Title for Non-Persisted View selection dialog
select_source=Kalıcı hale getirilecek görünüm seçin

#XTIT: Placeholder for Non-Persisted View selection dialog search
searchView=Görünüm arayın
#XTIT: No data in the list of non-persisted view
No_Data=Veri yok
#XBUT: Button to select non-persisted view
ok=Tamam
#XBUT: Button to close the non-persisted views selection dialog
cancel=İptal

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~View persistency log message key~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message for view persistency logs
START_STOP_PERSISTENCY="{1}" için veri kalıcılığı görev çalıştırması başlatılıyor.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY=Görünüm "{1}" için veriler kalıcı hale getiriliyor.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY)
START_VIEW_PERSISTENCY_PROCESS=Görünüm "{0}" için verileri kalıcı hale getirme süreci başlatılıyor.
#XFLD: Message for view persistency manual partition load
START_VIEW_PERSISTENCY_PROCESS_MANUAL_PARTITIONS=Seçili bölümleme tanıtıcıları ile görünüm "{0}" için verileri kalıcı hale getirme süreci başlatılıyor: "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY=Görünüm "{1}" için kalıcı veriler kaldırılıyor.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY)
REMOVE_VIEW_PERSISTENCY=Görünüm "{0}" için kalıcı verileri kaldırma süreci başlatılıyor.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_SUCCESS=Görünüm "{1}" için veriler kalıcı hale getirildi.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_SUCCESS)
START_VIEW_PERSISTENCY_PROCESS_SUCCESS=Görünüm "{0}" için veriler kalıcı hale getirildi.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY_SUCCESS=Görünüm "{1}" için kalıcı veriler kaldırıldı ve sanal veri erişimi geri yüklendi.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_SUCCESS)
REMOVE_VIEW_PERSISTENCY_SUCCESS=Görünüm "{0}" için kalıcı verileri kaldırma süreci tamamlandı.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_FAILED=Görünüm "{1}" için veriler kalıcı hale getirilemiyor.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_FAILED)
START_VIEW_PERSISTENCY_PROCESS_FAILED=Görünüm "{0}" için veriler kalıcı hale getirilemiyor.
#XFLD: Message for view persistency logs (for messages created before version 2022.05)
STOP_VIEW_PERSISTENCY_FAILED=Görünüm "{1}" için kalıcı veriler kaldırılamıyor.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_FAILED)
REMOVE_VIEW_PERSISTENCY_FAILED=Görünüm "{0}" için kalıcı veriler kaldırılamıyor.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_SUCCESS=Görünüm "{1}" için "{3}" kayıt kalıcı hale getirildi.
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace START_PERSISTENCY_RECORD_COUNT_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=Görünüm "{1}" için veri kalıcılığı tablosuna {0} kayıt eklendi.
#XFLD: Message used by SEAL repository for view persistency logs (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS_V2=Görünüm "{1}" için veri kalıcılığı tablosuna {0} kayıt eklendi. Kullanılan bellek: {2} GiB.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_SUCCESS=Görünüm "{1}" için "{3}" kalıcı kayıt kaldırıldı.
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace STOP_PERSISTENCY_RECORD_COUNT_SUCCESS)
REMOVE_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=Kalıcı veriler kaldırıldı, "{0}" kalıcı kayıt silindi.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_FAILED=Görünüm "{1}" için recordCount alınamadı.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_FAILED=Görünüm "{1}" için recordCount alınamadı.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS="{1}" için planlama silindi.
#XFLD: Message for view persistency logs (new message to replace STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS)
REMOVE_VIEW_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Görünüm "{0}" için planlama silindi.
#XFLD: Message for view persistency logs
STOP_PERSISTENCY_DELETE_SCHEDULE_FAILED="{1}" için planlama silinemedi.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_REDEPLOY=Siz görünümü kalıcı olarak kaydetmeye başladıktan sonra görünüm değiştirildiğinden ve dağıtıldığından, "{0}" görünümünü kalıcı hale getiremiyoruz. Görünümü kalıcı hale getirmeyi tekrar deneyin veya planlanan sonraki çalıştırmaya kadar bekleyin.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_DELETE=Siz görünümü kalıcı olarak kaydetmeye başladıktan sonra görünüm silindiğinden, ''{0}'' görünümünü kalıcı hale getiremiyoruz.
#XFLD: Message for successful data persistency of a single partition (for messages created before version 2022.03)
START_PERSISTENCY_SINGLE_PARTITION_SUCCESS=Değerler ("{1}" <= {2} < "{3}") için bölümlemede {0} kayıt kalıcı hale getirildi.
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (new message to replace START_PERSISTENCY_SINGLE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS=Değerler ("{1}" <= "{2}" < "{3}") için bölümlemeye {0} kayıt eklendi.
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS_V2=Değerler ("{1}" <= "{2}" < "{3}") için bölümlemeye {0} kayıt eklendi. Kullanılan bellek {4} GiB.
#XFLD: Message for successful replication of a "others" partition (for messages created before version 2022.03)
START_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} kayıt, "diğer" bölümlemesinde kalıcı hale getirildi.
#XFLD: Message used by SEAL repository for successful replication of a "others" partition (new message to replace START_PERSISTENCY_OTHERS_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} kayıt "diğer" bölümlemesine eklendi.
#XFLD: Message for view persistency record count of partitions (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={4} bölümlemede görünüm "{1}" için {3} kayıt kalıcı hale getirildi.
#XFLD: Message used by SEAL repository for view persistency record count of partitions (new message to replace START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={2} bölümlemede görünüm "{1}" için veri kalıcılığı tablosuna {0} kayıt eklendi.
#XFLD: Message used by SEAL repository for view persistency for partition wise refresh
START_VIEW_PERSISTENCY_RECORD_COUNT_REFRESHED_PARTITIONS_SUCCESS=Görünüm "{1}" için veri kalıcılığı tablosuna {0} kayıt eklendi. Güncellenen bölümlemeler: {2}; kilitli bölümlemeler: {3}; toplam bölümleme: {4}
#XFLD: Message used by SEAL repository for view persistency for manual partition load
START_VIEW_PERSISTENCY_RECORD_COUNT_MANUAL_PARTITIONS_SUCCESS=Seçili {2} bölümlemede görünüm "{1}" için veri kalıcılığı tablosuna {0} kayıt eklendi.
#XFLD: Message used by SEAL repository for view persistency for locked partition data load
START_VIEW_PERSISTENCY_RECORD_COUNT_LOCKED_PARTITIONS_SUCCESS=Görünüm "{1}" için veri kalıcılığı tablosuna {0} kayıt eklendi. Güncellenen bölümlemeler: {2}; kilitli ve değiştirilmemiş bölümlemeler: {3}; toplam bölümleme: {4}.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
START_PERSISTENCY_UNEXPECTED_ERROR=Görünüm "{0}" için veriler kalıcı hale getirilirken hata oluştu.
#XMSG: Message for unexpected error (new message to replace START_PERSISTENCY_UNEXPECTED_ERROR)
START_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Görünüm "{0}" için veriler kalıcı hale getirilirken hata oluştu.
#XMSG: Message for Locked space error
START_VIEW_PERSISTENCY_LOCKED_SPACE_ERROR=Alan "{1}" kilitlendiğinden görünüm "{0}" kalıcı hale getirilemiyor.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
STOP_PERSISTENCY_UNEXPECTED_ERROR=Görünüm "{0}" için kalıcı veriler kaldırılırken hata oluştu.
#XMSG: Message for unexpected error (new message to replace STOP_PERSISTENCY_UNEXPECTED_ERROR)
REMOVE_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Görünüm "{0}" için kalıcılık kaldırılırken beklenmeyen bir hata oluştu.
#XMSG: Message for ivalidated view
VIEW_PERSISTENCY_ABORTED_DEFINITION_INVALID="{0}" görünümünün tanımı geçersiz hale geldi. Bunun en olası nedeni, görev tarafından doğrudan veya dolaylı olarak kullanılan bir nesnenin değiştirilmesidir. Sorunu çözmek veya temel nedeni belirlemek için görünümü yeniden dağıtın.
#XMSG: Message for persisted data removal (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT="{0}" görünümü dağıtılırken kalıcı veriler kaldırıldı.
#XMSG: Message for persisted data removal of a consuming view
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW=Kullanılan "{0}" görünümü dağıtılırken kalıcı veriler kaldırılır.
#XMSG: Message for persisted data removal of a consuming view due to a dac change
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW_DAC_CHANGED=Veri erişimi denetimi değiştirildiğinden, kullanılan "{0}" görünümü dağıtılırken kalıcı veriler kaldırıldı.
#XMSG: Message for persisted data removal (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DEPLOYMENT=''{0}'' görünümü dağıtılırken kalıcı veriler kaldırıldı.
#XMSG: Message for deleted view (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION="{0}" görünümünün silinmesiyle birlikte kalıcılık kaldırıldı.
#XMSG: Message for deleted view (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DELETION="{0}" görünümünün silinmesiyle birlikte kalıcılık kaldırıldı.
#XMSG Message for removal of persisted data, because prerequisites not fulfilled
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_PREREQUISITES_NOT_FULFILLED=Veri kalıcılığı önkoşulları artık karşılanmadığından kalıcı veriler kaldırıldı.
#XMSG: Message for inconsistent persistency
VIEW_PERSISTENCY_INCONSISTENT_PERSISTENCY="{0}" görünümünün kalıcılığı tutarsız hale geldi. Sorunu çözmek için kalıcı hale getirilen verileri kaldırın.
#XMSG: Message for prerequisites
START_PERSISTENCY_CHECK_PREREQUISITES="{0}" görünümünü kalıcı hale getirmek için önkoşullar kontrol ediliyor.
#XMSG: Message for DAC deployed in deprecated way using table function and join (for messages created before 2025.05)
START_PERSISTENCY_DEPRECIATED_DAC=Görünüm "{0}", kullanım dışı bırakılan veri erişimi denetimi (DAC) kullanılarak dağıtıldı. Performansı iyileştirmek için görünümü tekrar dağıtın.
#XMSG: Message for DAC deployed in deprecated way using table function and join
START_PERSISTENCY_DEPRECATED_DAC=Görünüm "{0}", kullanım dışı bırakılan veri erişimi denetimi (DAC) kullanılarak dağıtıldı. Performansı iyileştirmek için görünümü tekrar dağıtın.
#XMSG: Message for roll back (for messages created before version 2022.03)
ROLLBACK_VIEW_PERSISTENCY=Hata oluştu. "{0}" görünümü için önceki kalıcılık durumu geri yüklendi.
#XMSG: Message used by SEAL repository for rollback of running procedure to persist view (new message to replace ROLLBACK_VIEW_PERSISTENCY)
VIEW_PERSISTENCY_ROLLBACK=Hata oluştu. ''{0}'' görünümünü kalıcı hale getirme işlemi durduruldu ve değişiklikler geri alındı.
#XMSG: Message used by SEAL repository for rollback of running procedure to remove persisted data of view
REMOVE_VIEW_PERSISTENCY_ROLLBACK=Hata oluştu. "{0}" görünümünün kalıcı verilerini kaldırma işlemi durduruldu ve değişiklikler geri alındı.
#XMSG: Message used by SEAL repository for persistency preparation
START_PERSISTENCY_PREPARATION=Verilerin kalıcı hale getirilmesi için hazırlık yapılıyor.
#XMSG: Message used by SEAL repository for data insertion
START_INSERTING_PERSISTENCY_DATA=Veriler kalıcılık tablosuna ekleniyor.
#XMSG: Message for null values - partitioning (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS={0} null değer kaydı "diğer" bölümlemesine eklendi.
#XMSG: Message used by SEAL repository for others partition values - partitioning (new message to replace START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS=Değerler ("{2}" < "{1}" VEYA "{2}" >= "{3}") için {0} kayıt "diğer" bölümlemesine eklendi.
#XMSG: Message used by SEAL repository for others partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS_V2=Değerler ("{2}" < "{1}" VEYA "{2}" >= "{3}") için {0} kayıt "diğer" bölümlemesine eklendi. Kullanılan bellek: {4} GiB.
#XMSG: Message used by SEAL repository for null partition values - partitioning
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS="{1}" değerleri için "diğer" bölümlemesine eklenen {0} kayıt NULL.
#XMSG: Message used by SEAL repository for null partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS_V2="{1}" değerleri için "diğer" bölümlemesine eklenen {0} kayıt NULL. Kullanılan bellek: {2} GiB.
##XFLD:Message used by SEAL repository for remote statistics
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS=Dahil olan veriler yükleniyor: {0} uzak deyim. Getirilen toplam kayıt: {1}. Toplam süre: {2} saniye.
##XFLD:Message used by SEAL repository for remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS_WITH_PARTITIONS={1} uzak deyim içeren {0} bölümleme kullanılarak dahil olan veriler yükleniyor. Getirilen toplam kayıt: {2}. Toplam süre: {3} saniye.
##XFLD:Message used by SEAL repository with UI ID for instructions of remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_INSTRUCTION=Çalıştırma sırasında işlenen uzak deyimler, bölümlemeye özgü iletilerin ayrıntılarında uzak sorgu izleme açılarak görüntülenebilir.
##XFLD:Message for start reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS=Dağıtım sonrasında görünüm {0} için mevcut kalıcı verileri yeniden kullanma işlemi başlatılıyor.
##XFLD:Message used by SEAL repository for reattach view persitency
START_VIEW_PERSISTENCY_REATTACHMENT=Mevcut kalıcı verilerin yeniden kullanılmasını başlatın.
##XFLD:Message used by SEAL repository for reattach view persitency
START_FINILIZING_VIEW_PERSISTENCY_REATTACHMENT=Mevcut kalıcı veriler yeniden kullanılıyor.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_COMPLETE=Görünüm {0} için mevcut kalıcı verileri yeniden kullanma işlemi tamamlandı.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_FAILED=Dağıtım sonrasında görünüm {0} için mevcut kalıcı veriler yeniden kullanılamadı.
#XMSG: Message used by SEAL repository for finalizing persistency
START_FINALIZING_PERSISTENCY=Kalıcılık sonlandırılıyor.
#XMSG: Message used by SEAL repository for restoring virtual access for view
REMOVE_VIEW_PERSISTENCY_VIRTUAL_ACCESS_RESTORED="{0}" görünümü için sanal veri erişimi geri yüklendi.
#XMSG: Message used by SEAL repository for view that already has virtual access
REMOVE_VIEW_PERSISTENCY_ALREADY_VIRTUAL_ACCESS=Görünüm "{0}", sanal veri erişimine zaten sahip. Kalıcı veri kaldırılmadı.
#XMSG: Message for removing view persistency
VIEW_PERSISTENCY_REMOVED_FROM_MONITOR="{0}" görünümü, görünüm izlemeden kaldırıldı.
#XMSG: Message for view doesnt exist.
VIEW_PERSISTENCY_VIEW_NOT_EXISTING="{0}" görünümü veri tabanında mevcut değil veya doğru şekilde dağıtılmamış ve bu nedenle kalıcılığı sağlanamıyor. Sorunu çözmek için görünümü yeniden dağıtmayı veya kök nedeni tanımlamayı deneyin.
#XMSG: Message for persistency not enabled.
VIEW_PERSISTENCY_NOT_ENABLED=Veri kalıcılığı etkinleştirilmedi. İşlevi etkinleştirmek için "{0}" alanında bir tabloyu/görünümü yeniden dağıtın.
#XMSG: Message for unexpected error due to node crash
VIEW_PERSISTENCY_UNEXPECTED_ERROR_GENERAL=Son görünüm kalıcılık çalıştırması teknik hatalar nedeniyle durduruldu.
#XFLD: Message for view persistency memory consumption
VIEW_PERSISTENCY_MEMORY_CONSUMPTION=Görünüm kalıcılığı çalıştırma süresinde {0} GiB azami bellek kullanımı gerçekleşti.
#XFLD: Message for view persistency timeout
VIEW_PERSISTENCY_TIMEOUT={0} görünümü kalıcılığı {1} saatlik zaman aşımına ulaştı.
#XFLD: Message for not started asynchronous view persistency
VIEW_PERSISTENCY_ASYNC_NOT_STARTED=Yüksek sistem yükü, görünüm kalıcılığına ilişkin asenkron yürütmenin başlatılmasını engelledi. Paralel olarak çok fazla sayıda görev çalıştırılıp çalıştırılmadığını kontrol edin.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency (for messages created before version 2022.05)
START_VIEW_PERSISTENCY_EXECUTION_METHOD_DROP_AND_RENAME=Kalıcı hale getirilmiş mevcut tablo silindi ve kalıcı hale getirilmiş yeni bir tabloyla değiştirildi.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_RECREATE_AND_DROP=Kalıcı hale getirilmiş mevcut tablo silindi ve kalıcı hale getirilmiş yeni bir tabloyla değiştirildi.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_TRUNCATE_AND_COPY=Kalıcı hale getirilmiş mevcut tablo yeni verilerle güncellendi.
##XFLD: Message for missing authorizations for view persistency
VIEW_PERSISTENCY_MISSING_AUTH=Veri kalıcılığı için yetkiler eksik.
##XFLD: Message start of persistency cancellation
VIEW_PERSISTENCY_TRIGGER_CANCEL={0} görünümünü kalıcı hale getirmek için süreci iptal etme başlatılıyor.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_UNAVAILABLE_TASK_RUN=Görünüm {0} için çalıştırılmakta olan hiç veri kalıcılığı görevi bulunmadığından, görünümü kalıcı hale getirme süreci iptal edilemedi.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_NONDETERMINISTIC_TASK_RUN=Görünüm {0} için çalıştırılmakta olan hiç veri kalıcılığı görevi bulunmadığından, görünümü kalıcı hale getirme süreci iptal edilemedi.
##XFLD: Message for canceling a non running persitency task
VIEW_PERSISTENCY_CANCEL_NON_RUNNING_SELECTED_TASK=Seçilen kalıcılık görevi {1} çalıştırılmadığından, {0} görünümünü kalıcı hale getirme süreci iptal edilemedi.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_UNREACHABLE_PERSISTENCY_RUN=Görünüm {0} için veri kalıcılığı henüz başlatılmadığından görünümü kalıcı hale getirme süreci iptal edilemedi.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_COMPLETED_PERSISTENCY_RUN=Zaten tamamlandığı için {0} görünümünü kalıcı hale getirme süreci iptal edilemedi.
##XFLD: Message for failed cancellation
VIEW_PERSISTENCY_ABORTED_CANCEL_EXECUTION={0} görünümünü kalıcı hale getirmek için süreç iptal edilemedi.
##XFLD: Message for finalizing persistency cancellation
VIEW_PERSISTENCY_FINALIZE_CANCEL={0} görünümünün veri kalıcılığını durdurma süreci gönderildi.
##XFLD: Message used in persist task when cancellation is caught
VIEW_PERSISTENCY_CANCELED={0} görünümünü kalıcı hale getirme süreci durduruldu.
##XFLD: Message used in persist task when cancelation is caught and performed through a cancelation task
VIEW_PERSISTENCY_CANCELED_VIA_CANCEL_TASK={0} görünümünü kalıcı hale getirme süreci, {1} iptal görevi aracılığıyla durduruldu.
##XFLD: Message used in cancel task when the task is triggered by deployment
VIEW_PERSISTENCY_CANCEL_TRIGGERED_BY_DEPLOYMENT=Görünüm {0} dağıtılırken verileri kalıcı hale getirme süreci iptal ediliyor.
##XFLD: Message used in case the cancelation is retriggered for a running task
VIEW_PERSISTENCY_CANCEL_ATTEMPT_RETRIGGER_CANCEL={0} görünümünü kalıcı hale getirmeye ilişkin önceki bir iptal görevi zaten gönderildi.
##XFLD: Message used to express the delay that may occur before the persistency task is stopped
VIEW_PERSISTENCY_CANCEL_DELAY_STOPPING_PERSISTENCY=Görünüm {0} için veri kalıcılığı görevi durdurulana kadar gecikme olabilir.
##XFLD: Message used to reflect the task log identifier of the current running persistency task
VIEW_PERSISTENCY_CANCEL_CURRENT_RUNNING_TASK=Görünüm {0} için veriler {1} göreviyle kalıcı hale getirildi.
##XFLD:Message used by SEAL repository for locked partitions and DACs
VIEW_PERSISTENCY_LOCKED_PARTITIONS_DACS=DAC'ler tarafından sağlanan yetkiler değişmiş ve kilitli bölümlemeler tarafından dikkate alınmıyor olabilir. Değişiklikleri uygulamak için bölümlemelerin kilidini açın ve yeni anlık görüntü yükleyin.
##XFLD:Message used by SEAL repository for column structure change
VIEW_PERSISTENCY_COLUMN_STRUCTURE_CHANGE=Sütun yapısı değiştirildi ve artık mevcut kalıcılık tablosuyla eşleşmiyor. Kalıcılık tablonuzun güncellenmesini sağlamak için kalıcı verileri kaldırın ve yeni bir veri kalıcılığı başlatın.
##XFLD: Message for out of memory
VIEW_PERSISTENCY_HANA_OUT_OF_MEMORY=Görev, SAP HANA veri tabanındaki bir yetersiz bellek hatası nedeniyle başarısız oldu.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_EXCEPTION_TRANSACTION_ROLLBACK=Görev, SAP HANA veri tabanındaki bir dahili istisna nedeniyle başarısız oldu.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_SQL_EXECUTION_ISSUE=Görev, SAP HANA veri tabanındaki bir dahili SQL istisna sorunu nedeniyle başarısız oldu.
##XFLD: Message for HANA out of memory result
VIEW_PERSISTENCY_OUT_OF_MEMORY_RESULT_MESSAGE=HANA bellek dışı olay nedeni: {0}
##XFLD: Message for admission control rejection
VIEW_PERSISTENCY_HANA_ADMISSION_CTRL_REJECTION=Görev bir SAP HANA giriş kontrolü reddi nedeniyle başarısız oldu.
##XFLD: Message for HANA back pressure / rate limit of HANA client
VIEW_PERSISTENCY_HANA_CLIENT_RATE_LIMIT_REJECTION=Görev çok fazla sayıda etkin SAP HANA bağlantısı nedeniyle başarısız oldu.
#XMSG: Message for invalid persisted table name
VIEW_PERSISTENCY_PERSISTED_TABLE_INVALID=Hata oluştu ve kalıcı tablo geçersiz hale geldi. Sorunu çözmek için kalıcı verileri kaldırın ve görünümü tekrar kalıcı hale getirin.
#XMSG: Message for usahe of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION=Görünüm kalıcı hale getirilemiyor. Görünümde, kullanıcı yaymanın etkin olduğu bir uzak kaynağı temel alan bir uzak tablo kullanılıyor. Görünümün kaynağını kontrol edin.
#XMSG: Message for dynamic usage of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION_DYNAMIC=Görünüm kalıcı hale getirilemiyor. Görünümde, kullanıcı yaymanın etkin olduğu bir uzak kaynağı temel alan bir uzak tablo kullanılıyor. Uzak tablo, SQL komut dosyası görünümü aracılığıyla dinamik olarak kullanılabilir. Görünümün kaynağı, uzak tabloyu göstermeyebilir.
#XMSG: Message for insufficient privileges or consumption of view with DAC via dynamic SQL
VIEW_PERSISTENCY_INSUF_PRIV_OR_DAC_DYN_SQL=Ayrıcalıklarınız yetersiz olabilir. Gerekli ayrıcalıklara sahip olup olmadığınızı görmek için Veri önizleme'yi açın. Sahipseniz dinamik SQL komut dosyası aracılığıyla kullanılan ikinci bir görünüme veri erişimi denetimi (DAC) uygulanabilir.
#XMSG: deployment of DAC is deprecated and view need to be redeployed to be able to persist data
VIEW_PERSISTENCY_DEPRECATED_DAC=Görünüm "{0}", kullanım dışı bırakılan veri erişimi denetimi (DAC) kullanılarak dağıtıldı. Görünüm için verileri kalıcı hale getirebilmek üzere görünümü tekrar dağıtın.
##XFLD:Message used by SEAL repository for input parameter
START_VIEW_PERSISTENCY_INPUT_PARAMETER=Girdi parametresi "{1}" için varsayılan değer "{0}" kullanılıyor.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Elastic compute node keys~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
##XFLD:Message used to reflect disablement of ecn replica
ECN_DISABLE_EXISTING_REPLICA=Esnek işlem düğümünü çoğaltma devre dışı bırakıldı.
##XFLD:Message used to reflect recreation of ecn replica
ECN_RECREATE_EXISTING_REPLICA=Esnek işlem düğümünü çoğaltma yeniden oluşturuldu.
##XFLD:Message used to reflect re-enablement of ecn replica
ECN_ENABLE_EXISTING_REPLICA=Esnek işlem düğümünü çoğaltma yeniden etkileştirildi.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Task Schedule Menu key~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message to schedule task
scheduleText=Planla
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Planlama oluştur
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Planlamayı düzenle
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Planlamayı sil
#XFLD: Refresh frequency field
refreshFrequency=Yenileme sıklığı
#XFLD: Refresh frequency field
refreshFrequencyNew=Sıklık
#XFLD: Refresh frequency field
refreshFrequencyNewNew=Planlanan sıklık
#XBUT: label for None
none=Hiçbiri
#XBUT: label for Real-Time replication state
realtime=Gerçek zamanlı
#XFLD: Label for table column
txtNextSchedule=Sonraki çalıştırma
#XFLD: Label for table column
txtNextScheduleNew=Sonraki planlı çalıştırma
#XFLD: Label for table column
txtNumOfRecords=Kayıt sayısı
#XFLD: Label for scheduled link
scheduledTxt=Planlandı
#XFLD: LABEL for partially persisted link
partiallyPersisted=Kısmen kalıcı
#XFLD: Text for paused text
paused=Duraklatıldı

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ View Monitor Tasklog Details Action keys~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message for release lock actionn
releaseLockMsg=Çalıştırma normalden uzun sürerse, çalıştırma başarısız ve durum buna uygun olarak güncellenmemiş anlamına gelir. \r\n Bu sorunu çözmek için kilidi kaldırabilir ve durumunu başarısız oldu olarak belirleyebilirsiniz.
#XFLD: Label for release lock dialog
releaseLockText=Kilidi kaldır

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Column header tooltip ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: tooltip for table column
txtViewTooltip=Kalıcı görünümün adı
#XFLD: tooltip for table column
txtViewDataAccessTooltip=Bu görünümün kullanılabilirliğini gösterir
#XFLD: tooltip for table column
txtViewRefreshFreqTooltip=Bu görünüm için planlamanın tanımlanıp tanımlanmadığını gösterir
#XFLD: tooltip for table column
txtViewStatusTooltip=Kalıcı görünüm durumunu al
#XFLD: tooltip for table column
txtViewLatestUpdateTooltip=Kalıcı görünümün en son ne zaman güncellendiğine ilişkin bilgi sağlar
#XFLD: tooltip for table column
txtViewNextRunTooltip=Görünüm için bir planlama ayarlanmışsa, sonraki çalıştırmanın ne zaman planlandığına bakın.
#XFLD: tooltip for table column
txtViewNumOfRecordsTooltip=Kayıt sayısını izleyin.
#XFLD: tooltip for table column
txtViewInMemorySizeTooltip=Görünümün belleğinizde ne kadar boyut kullandığını izleyin
#XFLD: tooltip for table column
txtViewDiskSizeTooltip=Görünümün diskinizde ne kadar boyut kapladığını izleyin
#XMSG: Expired text
txtExpired=Süresi doldu

#XMSG: Cannot be Added to the chain
NOT_CHAINABLE="{0}" nesnesi, görev zincirine eklenemiyor.

#~~~~~~~~~~~View Analyzer task log messages~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message indicating number of records for a view, and memory consumption in Mebibyte during simulation of view persistency
RECORDS_PEAK_MEMORY=Görünüm ''{0}'', {1} kayıt içeriyor. Bu görünüme ilişkin bir veri kalıcılığı simülasyonu için {2} MiB bellek kullanıldı.
#XMSG: View Analyzer execution failure
VIEW_ANALYZER_EXECUTION_FAILED=Görünüm analistini yürütme başarısız oldu.
#XMSG: Missing authorizations for View Analyzer
VIEW_ANALYZER_MISSING_AUTH=Görünüm analisti için yetkiler eksik.
#XMSG: Maximum memory in Gibibyte is reached, therefore persistency simulation is stopped
MAXIMUM_MEMORY_REACHED=Görünüm "{1}" için veri kalıcılığı simülasyonu yapılırken {0} GiB''lik azami bellek kullanımına ulaşıldığından başka veri kalıcılığı simülasyonu çalıştırılmayacak.
#XMSG: Error during persistency simulation occurred
PERSISTENCY_SIMULATION_ERROR=Görünüm "{0}" için veri kalıcılığı simülasyonu sırasında hata oluştu.
#XMSG: Persistency simulation is not executed, because prerequisites are not fulfilled and the view cannot be persisted
PREREQUISITES_NOT_FULFILLED=Önkoşullar karşılanmadığından ve görünüm kalıcı hale getirilemediğinden, görünüm "{0}" için veri kalıcılığı simülasyonu yürütülmedi.
#XMSG: Persistency simulation is not executed, because the view does not exist in the database
VIEW_DOES_NOT_EXIST=Veri kalıcılığı simülasyonunu etkinleştirmek için "{0}" görünümünü dağıtmanız gerekir.
#XMSG: Number of records cannot be determined, because the local table does not exist in the database
TABLE_DOES_NOT_EXIST=Yerel tablo "{0}" veri tabanında mevcut değil. Dolayısıyla bu tablo için kayıt sayısı belirlenemiyor.
##XMSG: Message start of cancellation for View Analyzer task
CANCEL_VIEW_ANALYZER_TRIGGERED=Görünüm "{1}" için görünüm analisti görevi {0} gönderildi. Görev durdurulana kadar gecikme olabilir.
##XMSG: Message start to indicate that task is not active
CANCEL_VIEW_ANALYZER_NO_ACTIVE_TASK=Görünüm "{1}" için görünüm analisti görevi {0} etkin değil.
#XMSG: Cancel View Analyzer task failure
CANCEL_VIEW_ANALYZER_FAILED=Görünüm analisti görevi iptal edilemedi.
##XMSG: Message used in View Analyzer execution task when task is canceled
VIEW_ANALYZER_CANCELED=Görünüm "{0}" için görünüm analisti yürütmesi bir iptal görevi aracılığıyla durduruldu.
##XMSG: Message start of cancelation for Model Validation task
CANCEL_MODEL_VALIDATION_TRIGGERED=Görünüm "{1}" için model doğrulama görevi {0} gönderildi. Görev durdurulana kadar gecikme olabilir.
##XMSG: Message to indicate that task is not active
CANCEL_MODEL_VALIDATION_NO_ACTIVE_TASK=Görünüm "{1}" için model doğrulama görevi {0} etkin değil.
#XMSG: Cancel Model Validation task failure
CANCEL_MODEL_VALIDATION_FAILED=Model doğrulama görevi iptal edilemedi.
##XMSG: Message used in Model Validation task when task is canceled
MODEL_VALIDATION_CANCELED=Görünüm "{0}" için model doğrulama yürütmesi bir iptal görevi aracılığıyla durduruldu.
#XMSG: Message for Locked space error, before starting Model Validation task
MODEL_VALIDATION_LOCKED_SPACE_ERROR="{1}" alanı kilitli olduğundan, "{0}" görünümü için model doğrulama yürütülemiyor.
##XMSG: Message used in View Analyzer execution task when determination of number of rows fails
NUMBER_ROWS_DETERMINATION_FAILED=Yerel tablo "{0}" için satır sayısı belirlenirken hata oluştu.
##XMSG: Message to indicate that SQL plan was created
EXECUTION_PLAN_CREATED=Görünüm "{0}" için SQL analisti plan dosyası oluşturuldu ve indirilebilir.
#XFLD: Message to indicate starting of process to generate SQL Analyzer plan file
START_PLANVIZ_EXECUTION=Görünüm "{0}" için SQL analisti plan dosyası üretme işlemi başlatılıyor.
#XFLD: Message to indicate starting of View Analyzer process
VIEW_ANALYZER_EXECUTION_STARTED=Görünüm analisti yürütmesi başlatılıyor.
##XMSG: Message to indicate that SQL plan file cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_PLANVIZ_NOT_FULFILLED=Veri kalıcılığı önkoşulları karşılanmadığından, görünüm "{0}" için SQL analisti plan dosyası üretilemiyor.
##XMSG: Error message during  generation of SQL Analyzer plan file
PLANVIZ_ERROR=Görünüm "{0}" için SQL analisti plan dosyası üretilmesi sırasında hata oluştu.
#XMSG: Message for Locked space error
VIEW_ANALYZER_LOCKED_SPACE_ERROR="{1}" alanı kilitli olduğundan, "{0}" görünümü için Görünüm analisti yürütülemiyor.
#XMSG: Message for persistency simulation without partitioning
PERSISTENCY_SIMULATION_WITHOUT_PARTITIONS="{0}" görünümüne ilişkin bölümlemeler veri kalıcılığı simülasyonu sırasında dikkate alınmaz.
#XMSG: Message for generation of SQL Analyzer plan file without considering partitioning
PLANVIZ_WITHOUT_PARTITIONS="{0}" görünümüne ilişkin bölümlemeler, SQL analisti plan dosyası üretilmesi sırasında dikkate alınmaz.
#XMSG: Confirmation message for mass removal of persisted data
MassRemovePersistency_Confirm_Msg=Kalıcı verileri kaldırmak ve veri erişimini tekrar sanal erişim olarak değiştirmek istiyor musunuz?
#XMSG: Confirmation message for partial mass removal of selected persisted data
PartialMassRemovePersistency_Confirm_Msg=Seçilen {1} görünümden {0} adedi kalıcı veriler içeriyor. \n Kalıcı verileri kaldırmak ve veri erişimini tekrar sanal erişim olarak değiştirmek istiyor musunuz?
#XMSG: success message for stopping mass persistence
stopMassPersistenceSuccess=Seçilen görünümler için kalıcı verileri kaldırıyoruz.
#XMSG: error message during mass stop persistence of a view (backend error)
STOP_MASS_PERSISTENCE_ERROR=Seçilen görünümler için kalıcılık durdurulurken hata oluştu.
#XMSG: memory analysis is only performed in space {0}
NO_MEMORY_ANALYSIS=Birimler için bellek analizi yalnızca "{0}" alanında gerçekleştirilir: "{1}" "{2}" atlandı.
##XMSG: Message to indicate that Explain Plan cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_EXPLAINPLAN_NOT_FULFILLED=Kalıcılık önkoşulları karşılanmadığından, görünüm "{0}" için Explain Plan dosyası üretilemiyor.
#XMSG: Message for Explain Plan generation without partitioning
EXPLAINPLAN_WITHOUT_PARTITIONS="{0}" görünümüne ilişkin bölümlemeler, Explain Plan dosyası üretilmesi sırasında dikkate alınmaz.
#XFLD: Message to indicate starting of process to generate Explain Plan file
START_EXPLAINPLAN_EXECUTION=Görünüm "{0}" için Explain Plan dosyası üretme işlemi başlatılıyor.
##XMSG: Message to indicate that Explain Plan was created
EXPLAINPLAN_CREATED="{0}" görünümü için Explain plan dosyası üretildi. Dosyayı "Ayrıntıları görüntüle"ye tıklayarak görüntüleyebilirsiniz veya ilgili izinlere sahipseniz indirebilirsiniz.
##XMSG: Error message during  generation of Explain Plan file
EXPLAINPLAN_ERROR=Görünüm "{0}" için Explain Plan dosyası üretilmesi sırasında hata oluştu.
##XMSG: Explain Plan file cannot be generated for the view {0}, because too many views are stacked on each other
EXPLAINPLAN_MODEL_TOO_COMPLEX=Görünüm "{0}" için Explain planı dosyası oluşturulamıyor. Birbiri üzerine yığılmış çok fazla sayıda görünüm var. Karmaşık modeller, bellek yetersizliği hatalarına ve yavaş performansa neden olabilir: Bir görünümün kalıcı hale getirilmesi önerilir.

##XMSG: Message to indicate that the space is locked
PERFORMANCE_ANALYSIS_LOCKED_SPACE_ERROR="{1}" alanı kilitli olduğundan "{0}" görünümü için performans analizi yürütülemiyor.
##XMSG: The performance analysis task was cancelled
PERFORMANCE_ANALYSIS_CANCELED=Görünüm "{0}" için performans analizi iptal edildi.
##XMSG: The performance analysis failed
PERFORMANCE_ANALYSIS_FAILED=Performans analizi başarısız oldu.
##XMSG: The performance analysis is finished
PERFORMANCE_ANALYSIS_FINISHED=Görünüm "{0}" için performans analizi tamamlandı. "Ayrıntıları görüntüle"ye tıklayarak sonucu görüntüleyin.
##XMSG: The view has input parameter with no default value
INPUT_PARAMETER_WITHOUT_DEFAULT_VALUE=Bu görünüm, varsayılan değeri olmayan parametre içerdiğinden analiz edilemiyor.
##XMSG: The view is not fully deployed
VIEW_NOT_FULLY_DEPLOYED=Bu görünüm tam olarak dağıtılmadığından analiz edilemiyor.
##XMSG: The view uses limited remote adapters
LIMITED_REMOTE_ADAPTER=Bu görünümde, filtreleme pushdown veya 'Sayı' desteği gibi özellikleri içermeyen, sınırlı özelliklere sahip en az bir uzak bağdaştırıcı kullanılır. Nesnelerin kalıcı hale getirilmesi veya çoğaltılması çalıştırma süresi performansını iyileştirebilir.
##XMSG: The view uses remote adapter that does not support 'Limit'
REMOTE_ADAPTER_NOT_SUPPORTING_LIMIT=Bu görünümde, 'Sınır'ın desteklenmediği en az bir uzak bağdaştırıcı kullanılıyor. 1.000'den fazla kayıt seçilmiş olabilir.
##XMSG: Performance analysis is executed by using default parameters of view'
INPUT_PARAMETER_WITH_DEFAULT_VALUE=Performans analizi, görünüm parametrelerinin varsayılan değerleri kullanılarak yürütülür.
##XMSG: Message to indicate that an error occurred
PERFORMANCE_ANALYSIS_ERROR="{0}" görünümü için performans analizi sırasında hata meydana geldi.
##XMSG: Message start of cancellation for Performance Analysis task
CANCEL_PERFORMANCE_ANALYSIS_TRIGGERED="{1}" görünümü için {0} performans analizi görevini durdurma işlemi gönderildi. Görev durdurulana kadar gecikme olabilir.
##XMSG: Message start to indicate that task is not active
CANCEL_PERFORMANCE_ANALYSIS_NO_ACTIVE_TASK=Görünüm "{1}" için performans analizi görevi {0} etkin değil.
#XMSG: Cancel Performance Analysis task failure
CANCEL_PERFORMANCE_ANALYSIS_FAILED=Performans analizi görevi iptal edilemedi.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Planlamayı bana tayin et
#XBUT: Pause schedule menu label
pauseScheduleLabel=Planlamayı duraklat
#XBUT: Resume schedule menu label
resumeScheduleLabel=Planlamayı sürdür
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Planlamalar kaldırılırken hata oluştu.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Planlamalar tayin edilirken hata oluştu.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Planlamalar duraklatılırken hata oluştu.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Planlamalar sürdürülürken hata oluştu.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted={0} planlama siliniyor
#XMSG: Message for starting mass assign of schedules
massAssignStarted={0} planlamanın sahibi değiştiriliyor
#XMSG: Message for starting mass pausing of schedules
massPauseStarted={0} planlama duraklatılıyor
#XMSG: Message for starting mass resuming of schedules
massResumeStarted={0} planlama sürdürülüyor
#XBUT: Select Columns Button
selectColumnsBtn=Sütun seç
#XFLD: Refresh tooltip
TEXT_REFRESH=Yenile
#XFLD: Select Columns tooltip
text_selectColumns=Sütun seç


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Runtime Metrics ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Label for Runtime Metrics
RuntimeMetricsTitle=Şunun için çalıştırma zamanı ölçümleri
#XFLD : Label for Run Button
runButton=Çalıştır
#XFLD : Label for Cancel Button
cancelButton=İptal et
#XFLD : Label for Close Button
closeButton=Kapat
#XFLD : Label for Open View Analyzer Button
openViewAnalyzerButton=Görünüm analistini aç
#XFLD : Label for Generate Explain Plan Button
generateExplainPlanButton=Explain plan üret
#XFLD : Label for Previous Run Column
previousRun=Önceki çalıştırma
#XFLD : Label for Latest Run Column
latestRun=Son çalıştırma
#XFLD : Label for time Column
time=Zaman
#XFLD : Label for Duration Column
duration=Süre
#XFLD : Label for Peak Memory Column
peakMemory=Azami bellek
#XFLD : Label for Number of Rows
numberOfRows=Satır sayısı
#XFLD : Label for Overall Number of Sources
overallNumberOfSources=Toplam kaynak sayısı
#XFLD : Label for Data Access Column
dataAccess=Veri erişimi
#XFLD : Label for Local Tables
localTables=Yerel tablolar
#XFLD : Label for federatedRemoteTables
federatedRemoteTables=Birleşik uzak tablolar (sınırlı bağdaştırıcı özellikleri ile)
#XTXT Text for initial state of the runtime metrics
initialState=Metrikleri almak için öncelikle Performans analizlerini çalıştırmanız gerekir. Bu, biraz zaman alabilir ancak gerekirse süreci iptal edebilirsiniz.
#XMSG: Cancel confirmation message
CancelRunConfirmationMessage=Performans analizinin geçerli çalıştırmasını iptal etmek istediğinizden emin misiniz?
#XTIT: Cancel dialog title
CancelRunTitle=Çalıştırmayı iptal et
#XFLD: Label for Number of Rows
NUMBER_ROWS=Satır sayısı
#XFLD: Label for Overall Number of Sources
OVERALL_NUMBER_SOURCES=Toplam kaynak sayısı
#XFLD: Label for Data Access
DATA_ACCESS=Veri erişimi
#XFLD: Label for Remote tables
NUMBER_REMOTE_TABLES_LIMITED_ADAPTERS=Birleşik uzak tablolar (sınırlı bağdaştırıcı özellikleri ile)
#XFLD: Label for select statement
SELECT_STATEMENT='SELECT * FROM VIEW LIMIT 1000'
#XFLD: Label for duration
SELECT_RUNTIME=Süre
#XFLD: Label for peak memory
SELECT_PEAK_MEMORY=Azami bellek
#XFLD: Label for count statement
SELECT_COUNT_STATEMENT='GÖRÜNÜMDEN SAYI(*) SEÇ'
#XFLD: Label for count duration
SELECT_COUNT_RUNTIME=Süre
#XFLD: Label for count peak memory
SELECT_COUNT_PEAK_MEMORY=Azami bellek
#XFLD: Label for count of number of local tables
NUMBER_LOCAL_TABLES_FILE=Yerel tablolar (dosya)
#XTXT: Text for running state of the runtime metrics
Running=Çalışıyor...
#XFLD: Label for time
Time=Zaman
#XFLD: Label for virtual access
PA_VIRTUAL=Sanal
#XFLD: Label for persisted access
PA_PERSISTED=Kalıcı
PA_PARTIALLY_PERSISTED=Kısmen kalıcı
#XTXT: Text for cancel
CancelRunSuccessMessage=Performans analizi çalıştırması iptal ediliyor.
#XTXT: Text for cancel error
CancelRunErrorMessage=Performans analizi çalıştırması iptal edilirken hata meydana geldi.
#XTXT: Text for explain plan generation
ExplainPlanStarted=Görünüm "{0}" için Explain plan oluşturuluyor.
#XTXT: Text for performance analysis
PerformanceAnalysisStarted="{0}" görünümü için performans analizi başlatılıyor.
#XTXT: Text for performance analysis error
ErrorFetchingPerformanceAnalysis=Performans analizi verileri getirilirken hata meydana geldi.
#XTXT: Text for performance analysis error
conflictingTask=Performans analizi görevi zaten çalışıyor
#XFLD: Label for Errors
Errors=Hatalar
#XFLD: Label for Warnings
Warnings=Uyarılar
#XFLD: Tooltip for missing privileges
openViewAnalyzerButtonTooltipNoPrivilege=Görünüm analistini açmak için DWC_DATAINTEGRATION(güncelleme) ayrıcalığınız olmalıdır.
#XFLD: Tooltip for missing privileges
generateExplainPlanButtonTooltipNoPrivilege=Explain plan oluşturmak için DWC_RUNTIME(okuma) ayrıcalığınız olmalıdır.



#XFLD: Label for frequency column
everyLabel=Her
#XFLD: Plural Recurrence text for Hour
hoursLabel=Saat
#XFLD: Plural Recurrence text for Day
daysLabel=Gün
#XFLD: Plural Recurrence text for Month
monthsLabel=Ay
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Dakika
