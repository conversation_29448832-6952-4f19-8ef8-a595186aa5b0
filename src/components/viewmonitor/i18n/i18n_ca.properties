
#XBCB: Breadcrumb
viewMonitorBreadcrumb={spaceId}

#XFLD: Label for persisted view column
SOURCE=Origen
#XFLD: Label for persisted view column
NAME=Nom
#XFLD: Label for persisted view column
NAME_LABEL=Nom empresarial
#XFLD: Label for persisted view column
NAME_LABELNew=Objecte (nom empresarial)
#XFLD: Label for persisted view column
TECHINCAL_NAME=Nom tècnic
#XFLD: Label for persisted view column
TECHINCAL_NAMENew=Objecte (nom tècnic)
#XFLD: Label for persisted view column
DATA_PERSISTENCY=Accés a dades
#XFLD: Label for persisted view column
STATUS=Estat
#XFLD: Label for persisted view column
LAST_UPDATED=Última actualització
#XFLD: Label for persisted view column
IN_MEMORY_SIZE=Memòria utilitzada per a l'emmagatzematge (MiB)
#XFLD: Label for persisted view column
DISK_SIZE=Disc utilitzat per a l'emmagatzematge (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_NEWW=Mida d'En memòria (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_HEADER=Mida d'En memòria
#XFLD: Label for persisted view column
DISK_SIZE_NEWW=Mida en disc (MiB)
#XFLD: Label for persisted view column
DISK_SIZE_HEADER=Mida en disc
#XFLD: Label for schedule owner column
txtScheduleOwner=Propietari del programa
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Mostra qui ha creat el programa
#XFLD: Label for persisted view column
PERSISTED=Persistit
#XFLD: Label for persisted view column
TYPE=Tipus
#XFLD: Label for View Selection Dialog column
changedOn=Data de modificació
#XFLD: Label for View Selection Dialog column
createdBy=Autor
#XFLD: Label for log details column
txtViewPersistencyLogs=Veure logs
#XFLD: Label for log details column
txtViewPersistencyLogsNew=Detalls
#XFLD: text for values shown for Ascending sort order
SortInAsc=Classificar en sentit ascendent
#XFLD: text for values shown for Descending sort order
SortInDesc=Classificar en sentit descendent
#XFLD: Label for persisted view monitor heading
ViewMonitor_Heading=Monitor de vistes
#XFLD: Label for persisted view monitor sub-heading
ViewMonitor_SubHeading=Supervisar i actualitzar persistència de dades de vistes


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ DynamicPageHeader ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: text for values shown in column Persistence Status
txtLoading=Carregant
#XFLD: text for values shown in column Persistence Status
txtRunning=En execució
#XFLD: text for values shown in column Persistence Status
txtAvailable=Disponible
#XFLD: text for values shown in column Persistence Status
txtError=Error
#XMSG: Error message for unknown Persistence type that gets logged in Kibana
unknownReplicationTypeError=El tipus de replicació ''{0}'' no està permès.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Content ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Opcions utilitzades per a l'última execució de persistència de dades: 
#XMSG: Message for input parameter name
inputParameterLabel=Paràmetre d’entrada
#XMSG: Message for input parameter value
inputParameterValueLabel=Valor
#XMSG: Message for persisted data
inputParameterPersistedLabel=Persistit a
#XTIT: Count for View in ViewMonitor Toolbar
txtViewCount=Vistes ({0})
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Persistència de vistes
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Persistència de dades
#XBUT: Button to clear the selected view persistance
ClearViewPersistance=Esborrar
#XBUT: Button to stop the selected view persistance
stopPersistance=Aturar persistència
#XFLD: Placeholder for Search field
txtSearch=Cercar
#XBUT: Tooltip for refresh button
txtRefresh=Actualitzar
#XBUT: Tooltip for add view button
txtDeleteView=Suprimir persistència
#XBUT: Tooltip for load new peristence
loadNewPersistence=Reiniciar persistència
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Carregar captura nova
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Iniciar persistència de dades
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Eliminar dades persistides
#XMSG: success message for starting persistence
startPersistenceSuccess=Estem persistint la vista "{0}".
#XMSG: success message for stopping persistence
stopPersistenceSuccess=Estem eliminant les dades persistides per a la vista "{0}".
#XMSG: success message for stopping persistence
deletePersistedViewSuccess=Estem eliminant la vista ''{0}'' de la llista de supervisió.
#XMSG: error message during start persistence of a view (backend error)
ERROR_STARTING_PERSISTENCE=S''ha produït un error en iniciar la persistència de dades per la vista "{0}".
#XMSG: error message during start persistence of a view (backend error)
PARAMERROR_STARTING_PERSISTENCE=La vista ''{0}'' no és apta per a la persistència perquè conté paràmetres d''entrada.
#XMSG: error message during start persistence of a view (backend error)
NUMBER_OF_PARAMETER_STARTING_PERSISTENCE=La vista "{0}" no és apta per a la persistència perquè conté més d''un paràmetre d''entrada.
#XMSG: error message during start persistence of a view (backend error)
PARAMETER_DEFAULT_VALUE_STARTING_PERSISTENCE=La vista "{0}" no és apta per a la persistència perquè el paràmetre d''entrada no té el valor predeterminat.
#XMSG: error message during start persistence of a view (backend error)
REDEPLOYMENT_DAC_REQUIRED=Heu de tornar a desplegar el control d''accés a dades (DAC) "{0}" per permetre la persistència de dades.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_STARTING_PERSISTENCE=La vista ''{0}'' no es pot desar de forma persistent perquè utilitza la vista ''{1}'', que conté control d''accés a dades (CAD).
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_EXTERNAL_STARTING_PERSISTENCE=No es pot persistir la vista "{0}" perquè utilitza una vista amb control d''accés a dades (DAC) que pertany a un altre espai.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_STRUCTURE_STARTING_PERSISTENCE=La vista ''{0}'' no es pot desar de forma persistent perquè l''estructura d''un o més dels seus controls d''accés a dades (DAC) no admet la persistència de dades.
#XMSG: error message during stop persistence of a view (backend error)
STOP_PERSISTENCE_ERROR=S''ha produït un error en aturar la persistència per la vista "{0}".
#XMSG: error message during stop persistence of a view (backend error)
DELETE_PERSISTED_VIEW_ERROR=S''ha produït un error en suprimir la vista persistida "{0}".
#XMSG: Warning message for removing the persisted data
RemovePersistency_Confirm_Msg=Voleu suprimir les dades persistides i canviar a l’accés virtual de la vista "{0}"?
#XMSG: Warning message for delete the view persistence
DeletePersistency_Confirm_Msg=Voleu eliminar la vista de la llista de supervisió i suprimir les dades persistides de la vista "{0}"?
#XMSG: error message for reading data from backend
txtReadBackendError=Sembla que s’ha produït un error en llegir al back-end.
#XFLD: Label for No Data Error
NoDataError=Error
#XMSG: message for conflicting task
Task_Already_Running=Ja s''està executant una tasca en conflicte per a la vista "{0}".

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Show All Views~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Text for show all views button
allViewsText=Totes les vistes ({0})
#XBUT: Text for show scheduled views button
scheduledText=Programat ({0})
#XBUT: Text for show persisted views button
persistedText=Persistit ({0})
#XBUT: Text for start analyzer button
startAnalyzer=Iniciar analitzador de vistes
#XFLD: Message if repository is unavailable
repositoryErrorMsg=El dipòsit no està disponible i algunes funcions estan desactivades.

#XFLD: Data Access - Virtual
Virtual=Virtual
#XFLD: Data Access - Persisted
Persisted=Persistit

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Non-Persisted View Selection Dialog~~~~~~~~~~~~~~~~~~~~~~~

#XTIT: Title for Non-Persisted View selection dialog
select_source=Seleccioneu vista per persistir

#XTIT: Placeholder for Non-Persisted View selection dialog search
searchView=Cercar vistes
#XTIT: No data in the list of non-persisted view
No_Data=Sense dades
#XBUT: Button to select non-persisted view
ok=D'acord
#XBUT: Button to close the non-persisted views selection dialog
cancel=Cancel·lar

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~View persistency log message key~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message for view persistency logs
START_STOP_PERSISTENCY=S''està iniciant l''execució de la tasca de persistència de dades per a "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY=S’estan desant de manera persistent les dades de la vista "{1}".
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY)
START_VIEW_PERSISTENCY_PROCESS=S''està iniciant el procés de persistència de dades per a la vista "{0}".
#XFLD: Message for view persistency manual partition load
START_VIEW_PERSISTENCY_PROCESS_MANUAL_PARTITIONS=S''està iniciant el procés de persistència de dades per a la vista ''{0}'' amb els ID de partició seleccionats: ''{1}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY=Eliminant les dades persistides per a la vista "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY)
REMOVE_VIEW_PERSISTENCY=S''inicia el procés d''eliminar les dades desades de forma persistent per a la vista "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_SUCCESS=Les dades s’han desat de manera persistent per a la vista "{1}".
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_SUCCESS)
START_VIEW_PERSISTENCY_PROCESS_SUCCESS=Les dades s’han desat de manera persistent per a la vista "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY_SUCCESS=S’han eliminat les dades desades de manera persistent i s’ha restablert l’accés virtual a les dades per a la vista "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_SUCCESS)
REMOVE_VIEW_PERSISTENCY_SUCCESS=Ha conclòs el procés d''eliminar les dades desades de forma persistent per a la vista "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_FAILED=No s''han pogut desar de manera persistent les dades de la vista "{1}".
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_FAILED)
START_VIEW_PERSISTENCY_PROCESS_FAILED=No s''han pogut desar de manera persistent les dades de la vista "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.05)
STOP_VIEW_PERSISTENCY_FAILED=No s''han pogut eliminar les dades desades de manera persistent de la vista "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_FAILED)
REMOVE_VIEW_PERSISTENCY_FAILED=No s''han pogut eliminar les dades desades de manera persistent de la vista "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_SUCCESS="{3}" registres desats de manera persistent per a la vista "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace START_PERSISTENCY_RECORD_COUNT_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=S''han inserit {0} registres a la taula de persistència de dades per a la vista "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS_V2={0} registres inserits a la taula de persistència de dades de la vista ''{1}''. Memòria utilitzada: {2} GiB.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_SUCCESS="{3}" registres desats de manera persistents eliminats per a la vista "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace STOP_PERSISTENCY_RECORD_COUNT_SUCCESS)
REMOVE_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=S''han eliminat les dades desades de manera persistent i s''han suprimit {0} registres desats de manera persistent.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_FAILED=Error en obtenir recordCount per a la vista "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_FAILED=Error en obtenir recordCount per a la vista "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=S’ha suprimit el programa per a "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS)
REMOVE_VIEW_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=La programació s''ha suprimit per a la vista "{0}".
#XFLD: Message for view persistency logs
STOP_PERSISTENCY_DELETE_SCHEDULE_FAILED=Error en suprimir el programa de "{1}".
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_REDEPLOY=No es pot desar de manera persistent la vista "{0}" perquè s''ha modificat i desplegat des que vau començar a desar-la de manera persistent. Proveu de tornar a desar-la o espereu fins a la propera execució programada.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_DELETE=No es pot desar de manera persistent la vista "{0}" perquè s''ha suprimit des que vau començar a desar-la de manera persistent.
#XFLD: Message for successful data persistency of a single partition (for messages created before version 2022.03)
START_PERSISTENCY_SINGLE_PARTITION_SUCCESS=S''han desat de manera persistent {0} registres a la partició per als valors ''{1}'' <= {2} < ''{3}''.
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (new message to replace START_PERSISTENCY_SINGLE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS=S''han inserit {0} registres a la partició per als valors ''{1}'' <= {2} < ''{3}''.
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS_V2={0} registres inserits a la partició per als valors ''{1}'' <= ''{2}'' < ''{3}''. Memòria utilitzada: {4} GiB.
#XFLD: Message for successful replication of a "others" partition (for messages created before version 2022.03)
START_PERSISTENCY_OTHERS_PARTITION_SUCCESS=S''han desat de manera persistent {0} registres a la partició ''altres''.
#XFLD: Message used by SEAL repository for successful replication of a "others" partition (new message to replace START_PERSISTENCY_OTHERS_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_OTHERS_PARTITION_SUCCESS=S''han inserit {0} registres a la partició ''altres''.
#XFLD: Message for view persistency record count of partitions (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS=S''han desat de manera persistent {3} registres per a la vista ''{1}'' en {4} particions.
#XFLD: Message used by SEAL repository for view persistency record count of partitions (new message to replace START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS=S''han inserit {0} registres a la taula de persistència de dades per a la vista "{1}" en {2} particions.
#XFLD: Message used by SEAL repository for view persistency for partition wise refresh
START_VIEW_PERSISTENCY_RECORD_COUNT_REFRESHED_PARTITIONS_SUCCESS=S''han inserit {0} registres a la taula de persistència de dades per a la vista "{1}". Particions actualitzades: {2}. Particions bloquejades: {3}. Particions totals: {4}
#XFLD: Message used by SEAL repository for view persistency for manual partition load
START_VIEW_PERSISTENCY_RECORD_COUNT_MANUAL_PARTITIONS_SUCCESS={0} registres inserits a la taula de persistència de dades per a la vista ''{1}'' en {2} particions seleccionades.
#XFLD: Message used by SEAL repository for view persistency for locked partition data load
START_VIEW_PERSISTENCY_RECORD_COUNT_LOCKED_PARTITIONS_SUCCESS=S''han inserit {0} registres a la taula de persistència de dades per a la vista "{1}". Particions actualitzades: {2}. Particions sense modificar bloquejades: {3}. Particions totals: {4}
#XMSG: Message for unexpected error (for messages created before version 2022.03)
START_PERSISTENCY_UNEXPECTED_ERROR=S''ha produït un error inesperat en persistir les dades de la vista "{0}".
#XMSG: Message for unexpected error (new message to replace START_PERSISTENCY_UNEXPECTED_ERROR)
START_VIEW_PERSISTENCY_UNEXPECTED_ERROR=S''ha produït un error inesperat en persistir les dades de la vista "{0}".
#XMSG: Message for Locked space error
START_VIEW_PERSISTENCY_LOCKED_SPACE_ERROR=La vista "{0}" no es pot persistir perquè l''espai "{1}" està bloquejat.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
STOP_PERSISTENCY_UNEXPECTED_ERROR=S''ha produït un error inesperat en eliminar les dades persistides de la vista "{0}".
#XMSG: Message for unexpected error (new message to replace STOP_PERSISTENCY_UNEXPECTED_ERROR)
REMOVE_VIEW_PERSISTENCY_UNEXPECTED_ERROR=S''ha produït un error inesperat en eliminar la persistència de la vista "{0}".
#XMSG: Message for ivalidated view
VIEW_PERSISTENCY_ABORTED_DEFINITION_INVALID=La definició de la vista "{0}" ha quedat invalidada, probablement a causa d''una modificació d''un objecte consumit de manera directa o indirecta per la vista. Proveu de tornar a desplegar la vista per resoldre el problema o per identificar-ne la causa arrel.
#XMSG: Message for persisted data removal (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT=Les dades persistides s''han eliminat en desplegar la vista ''{0}''.
#XMSG: Message for persisted data removal of a consuming view
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW=Les dades persistides s''han eliminat en desplegar la vista consumida "{0}".
#XMSG: Message for persisted data removal of a consuming view due to a dac change
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW_DAC_CHANGED=Les dades persistides s''han eliminat en desplegar la vista consumida "{0}" perquè el seu control d''accés a dades ha canviat.
#XMSG: Message for persisted data removal (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DEPLOYMENT=Les dades persistides s''han eliminat en desplegar la vista "{0}".
#XMSG: Message for deleted view (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION=La persistència s''ha eliminat amb la supressió de la vista ''{0}''.
#XMSG: Message for deleted view (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DELETION=La persistència s''ha eliminat amb la supressió de la vista ''{0}''.
#XMSG Message for removal of persisted data, because prerequisites not fulfilled
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_PREREQUISITES_NOT_FULFILLED=Les dades desades de manera persistent s'eliminen, perquè els requisits previs per a la persistència de dades ja no es compleixen.
#XMSG: Message for inconsistent persistency
VIEW_PERSISTENCY_INCONSISTENT_PERSISTENCY=La persistència de la vista "{0}" ha esdevingut inconsistent. Elimineu les dades desades de forma persistent per resoldre el problema.
#XMSG: Message for prerequisites
START_PERSISTENCY_CHECK_PREREQUISITES=S''estan verificant els requisits previs de la persistència de la vista "{0}".
#XMSG: Message for DAC deployed in deprecated way using table function and join (for messages created before 2025.05)
START_PERSISTENCY_DEPRECIATED_DAC=La vista "{0}" es desplega mitjançant el control d''accés a dades (DAC) que està quedant obsolet. Desplegueu la vista un altre cop per millorar-ne el rendiment.
#XMSG: Message for DAC deployed in deprecated way using table function and join
START_PERSISTENCY_DEPRECATED_DAC=La vista "{0}" es desplega mitjançant el control d''accés a dades (DAC) que està quedant obsolet. Desplegueu la vista un altre cop per millorar-ne el rendiment.
#XMSG: Message for roll back (for messages created before version 2022.03)
ROLLBACK_VIEW_PERSISTENCY=S''ha produït un error. S''ha restaurat l''estat anterior de la persistència per a la vista "{0}".
#XMSG: Message used by SEAL repository for rollback of running procedure to persist view (new message to replace ROLLBACK_VIEW_PERSISTENCY)
VIEW_PERSISTENCY_ROLLBACK=S''ha produït un error. El procés per persistir la vista "{0}" s''ha aturat i s''han revertit les modificacions.
#XMSG: Message used by SEAL repository for rollback of running procedure to remove persisted data of view
REMOVE_VIEW_PERSISTENCY_ROLLBACK=S''ha produït un error. El procés d''eliminar les dades desades de forma persistent per a la vista "{0}" s''ha aturat i s''han revertit les modificacions.
#XMSG: Message used by SEAL repository for persistency preparation
START_PERSISTENCY_PREPARATION=S'està preparant la persistència de les dades.
#XMSG: Message used by SEAL repository for data insertion
START_INSERTING_PERSISTENCY_DATA=S'estan inserint les dades a la taula de persistència.
#XMSG: Message for null values - partitioning (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS=S''han inserit {0} registres de valors nuls a la partició "altres".
#XMSG: Message used by SEAL repository for others partition values - partitioning (new message to replace START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS=S''han inserit {0} registres a la partició "Altres" per als valors "{2}" < "{1}" O "{2}" >= "{3}".
#XMSG: Message used by SEAL repository for others partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS_V2={0} registres inserits a la partició "altres" per als valors "{2}" < "{1}" O "{2}" >= "{3}". Memòria utilitzada: {4} GiB.
#XMSG: Message used by SEAL repository for null partition values - partitioning
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS=S''han inserit {0} registres a la partició "Altres" per als valors "{1}" ÉS NUL.
#XMSG: Message used by SEAL repository for null partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS_V2={0} registres inserits a la partició "altres" per als valors "{1}" ÉS NUL. Memòria utilitzada: {2} GiB.
##XFLD:Message used by SEAL repository for remote statistics
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS=Carregar les dades implicades : {0} sentències remotes. Total de registres obtinguts: {1}. Duració total: {2} segons.
##XFLD:Message used by SEAL repository for remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS_WITH_PARTITIONS=Carregar les dades implicades aplicant {0} particions amb {1} sentències remotes. Total de registres obtinguts: {2}. Duració total: {3} segons.
##XFLD:Message used by SEAL repository with UI ID for instructions of remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_INSTRUCTION=Les declaracions remotes que s'han processat durant l'execució es poden veure obrint el monitor de consultes remotes, en els detalls dels missatges específics de partició.
##XFLD:Message for start reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS=S''inicia el procés per reutilitzar les dades existents desades de forma persistent per a la vista {0} després de la implementació.
##XFLD:Message used by SEAL repository for reattach view persitency
START_VIEW_PERSISTENCY_REATTACHMENT=Es comencen a reutilitzar les dades existents desades de forma persistent.
##XFLD:Message used by SEAL repository for reattach view persitency
START_FINILIZING_VIEW_PERSISTENCY_REATTACHMENT=Es reutilitzen les dades existents desades de forma persistent.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_COMPLETE=El procés per reutilitzar les dades existents desades deforma persistent ha conclòs per a la vista {0}.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_FAILED=Error en reutilitzar les dades existents desades de forma persistent per a la vista {0} després de la implementació.
#XMSG: Message used by SEAL repository for finalizing persistency
START_FINALIZING_PERSISTENCY=S'està finalitzant la persistència.
#XMSG: Message used by SEAL repository for restoring virtual access for view
REMOVE_VIEW_PERSISTENCY_VIRTUAL_ACCESS_RESTORED=S''ha restaurat l''accés a dades virtuals per a la vista "{0}".
#XMSG: Message used by SEAL repository for view that already has virtual access
REMOVE_VIEW_PERSISTENCY_ALREADY_VIRTUAL_ACCESS=La vista "{0}" ja té un accés a dades virtual. No s''eliminen dades desades de forma persistent.
#XMSG: Message for removing view persistency
VIEW_PERSISTENCY_REMOVED_FROM_MONITOR=La vista "{0}" s''ha eliminat del monitor de persistència de vistes.
#XMSG: Message for view doesnt exist.
VIEW_PERSISTENCY_VIEW_NOT_EXISTING=La vista "{0}" no existeix a la base de dades o no s''ha desplegat correctament i, per tant, no es pot persistir. Proveu de tornar a desplegar la vista per resoldre el problema o per identificar-ne la causa arrel.
#XMSG: Message for persistency not enabled.
VIEW_PERSISTENCY_NOT_ENABLED=La persistència de dades no està activada. Torneu a desplegar una taula o una vista a l''espai "{0}" per activar la funcionalitat.
#XMSG: Message for unexpected error due to node crash
VIEW_PERSISTENCY_UNEXPECTED_ERROR_GENERAL=L'execució de persistència de l'última vista s'ha interromput per errors tècnics.
#XFLD: Message for view persistency memory consumption
VIEW_PERSISTENCY_MEMORY_CONSUMPTION={0} GiB de memòria màxima utilitzats en temps d''execució de persistència de vista.
#XFLD: Message for view persistency timeout
VIEW_PERSISTENCY_TIMEOUT=La persistència de la vista {0} ha assolit el temps d''espera de {1} hores.
#XFLD: Message for not started asynchronous view persistency
VIEW_PERSISTENCY_ASYNC_NOT_STARTED=Una càrrega elevada del sistema ha impedit que s'iniciés l'execució asincrònica de la persistència de vista. Verifiqueu si hi ha massa tasques executant-se en paral·lel.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency (for messages created before version 2022.05)
START_VIEW_PERSISTENCY_EXECUTION_METHOD_DROP_AND_RENAME=S'ha suprimit la taula existent desada de forma persistent i s'ha substituït per una de nova.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_RECREATE_AND_DROP=S'ha suprimit la taula existent desada de forma persistent i s'ha substituït per una de nova.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_TRUNCATE_AND_COPY=La taula existent desada de forma persistent s'ha actualitzat amb dades noves.
##XFLD: Message for missing authorizations for view persistency
VIEW_PERSISTENCY_MISSING_AUTH=Falten autoritzacions per a la persistència de dades.
##XFLD: Message start of persistency cancellation
VIEW_PERSISTENCY_TRIGGER_CANCEL=S''està començant a cancel·lar el procés per desar la vista {0} de manera persistent.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_UNAVAILABLE_TASK_RUN=No s''ha pogut cancel·lar el procés per desar la vista de manera persistent perquè no hi ha cap tasca de persistència de dades en execució per a la vista {0}.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_NONDETERMINISTIC_TASK_RUN=No s''ha pogut cancel·lar el procés per desar la vista de manera persistent perquè no s''està executant cap tasca de persistència de dades per a la vista {0}.
##XFLD: Message for canceling a non running persitency task
VIEW_PERSISTENCY_CANCEL_NON_RUNNING_SELECTED_TASK=Error en cancel·lar el procés per persistir la vista {0} perquè la tasca de persistència de dades seleccionada {1} no s''està executant.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_UNREACHABLE_PERSISTENCY_RUN=No s''ha pogut cancel·lar el procés per desar la vista de manera persistent perquè encara no s''ha iniciat la persistència de dades per a la vista {0}.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_COMPLETED_PERSISTENCY_RUN=No s''ha pogut cancel·lar el procés per desar la vista {0} de manera persistent perquè ja ha conclòs.
##XFLD: Message for failed cancellation
VIEW_PERSISTENCY_ABORTED_CANCEL_EXECUTION=No s''ha pogut cancel·lar el procés per desar la vista {0} de manera persistent.
##XFLD: Message for finalizing persistency cancellation
VIEW_PERSISTENCY_FINALIZE_CANCEL=S''ha enviat el procés per aturar la persistència de dades per a la vista {0}.
##XFLD: Message used in persist task when cancellation is caught
VIEW_PERSISTENCY_CANCELED=S''ha aturat el procés per desar la vista {0} de manera persistent.
##XFLD: Message used in persist task when cancelation is caught and performed through a cancelation task
VIEW_PERSISTENCY_CANCELED_VIA_CANCEL_TASK=S''ha aturat el procés per desar la vista {0} de manera persistent mitjançant la tasca de cancel·lació {1}.
##XFLD: Message used in cancel task when the task is triggered by deployment
VIEW_PERSISTENCY_CANCEL_TRIGGERED_BY_DEPLOYMENT=S''està cancel·lant el procés per desar les dades de manera persistent mentre es desplega la vista {0}.
##XFLD: Message used in case the cancelation is retriggered for a running task
VIEW_PERSISTENCY_CANCEL_ATTEMPT_RETRIGGER_CANCEL=Ja s''ha enviat una tasca de cancel·lació anterior per desar la vista {0} de manera persistent.
##XFLD: Message used to express the delay that may occur before the persistency task is stopped
VIEW_PERSISTENCY_CANCEL_DELAY_STOPPING_PERSISTENCY=Es pot produir un retard fins que s''aturi la tasca de persistència de dades per a la vista {0}.
##XFLD: Message used to reflect the task log identifier of the current running persistency task
VIEW_PERSISTENCY_CANCEL_CURRENT_RUNNING_TASK=Les dades de la vista {0} s''estan desant de forma persistent amb la tasca {1}.
##XFLD:Message used by SEAL repository for locked partitions and DACs
VIEW_PERSISTENCY_LOCKED_PARTITIONS_DACS=Les autoritzacions que proporcionen els DAC poden haver canviat i les particions bloquejades no les tenen en compte. Desbloquegeu les particions i carregueu una captura de pantalla nova per aplicar les modificacions.
##XFLD:Message used by SEAL repository for column structure change
VIEW_PERSISTENCY_COLUMN_STRUCTURE_CHANGE=L'estructura de la columna ha canviat i ja no coincideix amb la taula de persistència existent. Elimineu les dades desades de forma persistent i inicieu una nova persistència de dades per actualitzar la taula de persistència.
##XFLD: Message for out of memory
VIEW_PERSISTENCY_HANA_OUT_OF_MEMORY=La tasca ha estat errònia perquè s'ha produït un error de falta de memòria a la base de dades de SAP HANA.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_EXCEPTION_TRANSACTION_ROLLBACK=La tasca ha fallat a causa d'una excepció interna a la base de dades de SAP HANA.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_SQL_EXECUTION_ISSUE=La tasca ha fallat a causa d'un probelma d'execució SQL intern a la base de dades de SAP HANA.
##XFLD: Message for HANA out of memory result
VIEW_PERSISTENCY_OUT_OF_MEMORY_RESULT_MESSAGE=Motiu d''esdeveniment de HANA sense memòria: {0}
##XFLD: Message for admission control rejection
VIEW_PERSISTENCY_HANA_ADMISSION_CTRL_REJECTION=La tasca ha estat fallida perquè s'ha produït un rebuig de control d'admissió a SAP HANA.
##XFLD: Message for HANA back pressure / rate limit of HANA client
VIEW_PERSISTENCY_HANA_CLIENT_RATE_LIMIT_REJECTION=La tasca ha estat fallida perquè hi ha massa connexions actives de SAP HANA.
#XMSG: Message for invalid persisted table name
VIEW_PERSISTENCY_PERSISTED_TABLE_INVALID=S'ha produït un error i la taula desada de forma persistent ha perdut validesa. Per resoldre aquest problema, elimineu les dades desades de forma persistent i torneu a desar la vista d'aquesta manera.
#XMSG: Message for usahe of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION=La vista no es pot desar de forma persistent. Utilitza una taula remota en una font remota amb propagació d'usuaris activada. Verifiqueu el llinatge de la vista. 
#XMSG: Message for dynamic usage of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION_DYNAMIC=La vista no es pot desar de forma persistent. Utilitza una taula remota en una font remota amb propagació d'usuaris activada. La taula remota es pot consumir de forma dinàmica amb una vista de script SQL. El llinatge de la vista no pot mostrar la taula remota. 
#XMSG: Message for insufficient privileges or consumption of view with DAC via dynamic SQL
VIEW_PERSISTENCY_INSUF_PRIV_OR_DAC_DYN_SQL=És possible que no tingueu privilegis suficients. Obriu la Previsualització de dades per veure si teniu els privilegis requerits. En cas afirmatiu, una segona vista consumida mitjançant un script SQL dinàmic hi pot aplicar el control d'accés a dades (DAC).
#XMSG: deployment of DAC is deprecated and view need to be redeployed to be able to persist data
VIEW_PERSISTENCY_DEPRECATED_DAC=La vista "{0}" es desplega mitjançant el control d''accés a dades (DAC) que està quedant obsolet. Torneu a desplegar-la per poder desar les dades de forma persistent per a la vista.
##XFLD:Message used by SEAL repository for input parameter
START_VIEW_PERSISTENCY_INPUT_PARAMETER=S''utilitza el valor per defecte "{0}" per al paràmetre d’entrada "{1}".
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Elastic compute node keys~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
##XFLD:Message used to reflect disablement of ecn replica
ECN_DISABLE_EXISTING_REPLICA=La rèplica del node de computació elàstic està desactivada.
##XFLD:Message used to reflect recreation of ecn replica
ECN_RECREATE_EXISTING_REPLICA=La rèplica del node de computació elàstic s'ha recreat.
##XFLD:Message used to reflect re-enablement of ecn replica
ECN_ENABLE_EXISTING_REPLICA=La rèplica del node de computació elàstic s'ha reactivat.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Task Schedule Menu key~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message to schedule task
scheduleText=Programa
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Crear programa
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Editar programa
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Suprimir programa
#XFLD: Refresh frequency field
refreshFrequency=Actualitzar freqüència
#XFLD: Refresh frequency field
refreshFrequencyNew=Freqüència
#XFLD: Refresh frequency field
refreshFrequencyNewNew=Freqüència programada
#XBUT: label for None
none=Cap
#XBUT: label for Real-Time replication state
realtime=Temps real
#XFLD: Label for table column
txtNextSchedule=Execució següent
#XFLD: Label for table column
txtNextScheduleNew=Següent execució programada
#XFLD: Label for table column
txtNumOfRecords=Nombre de registres
#XFLD: Label for scheduled link
scheduledTxt=Programat
#XFLD: LABEL for partially persisted link
partiallyPersisted=Desat de forma persistent i parcial
#XFLD: Text for paused text
paused=En pausa

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ View Monitor Tasklog Details Action keys~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message for release lock actionn
releaseLockMsg=Si una execució triga més de l'habitual, pot indicar que ha fallat i que l'estat no s'ha actualitzat en conseqüència. \r\n Per resoldre el problema, podeu alliberar el bloqueig i fixar-ne l'estat en "Error".
#XFLD: Label for release lock dialog
releaseLockText=Alliberar bloqueig

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Column header tooltip ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: tooltip for table column
txtViewTooltip=Nom de la vista desada de manera persistent
#XFLD: tooltip for table column
txtViewDataAccessTooltip=Això indica la disponibilitat de la vista
#XFLD: tooltip for table column
txtViewRefreshFreqTooltip=Això indica si es defineix un programa per a la vista
#XFLD: tooltip for table column
txtViewStatusTooltip=Obtenir l’estat de la vista desada de manera persistent
#XFLD: tooltip for table column
txtViewLatestUpdateTooltip=Proporciona informació sobre l’última actualització de la vista desada de manera persistent
#XFLD: tooltip for table column
txtViewNextRunTooltip=Si es fixa un programa per a la vista, consulteu quan es programa la propera execució.
#XFLD: tooltip for table column
txtViewNumOfRecordsTooltip=Fer un seguiment del nombre de registres.
#XFLD: tooltip for table column
txtViewInMemorySizeTooltip=Feu un seguiment de la mida que fa servir la vista a la memòria
#XFLD: tooltip for table column
txtViewDiskSizeTooltip=Feu un seguiment de la mida que ocupa la vista al disc
#XMSG: Expired text
txtExpired=Vençut

#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=L''objecte "{0}" no es pot afegir a la cadena de tasques.

#~~~~~~~~~~~View Analyzer task log messages~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message indicating number of records for a view, and memory consumption in Mebibyte during simulation of view persistency
RECORDS_PEAK_MEMORY=La vista "{0}" té {1} registres. Una simulació de la persistència de dades per a aquesta vista ha utilitzat {2} MiB de memòria.
#XMSG: View Analyzer execution failure
VIEW_ANALYZER_EXECUTION_FAILED=No s'ha pogut executar l'analitzador de vistes.
#XMSG: Missing authorizations for View Analyzer
VIEW_ANALYZER_MISSING_AUTH=Falten autoritzacions per a l’analitzador de visualització.
#XMSG: Maximum memory in Gibibyte is reached, therefore persistency simulation is stopped
MAXIMUM_MEMORY_REACHED=La simulació de persistència de dades per a la vista "{1}" ha arribat a consumir el màxim de memòria de {0} GiB. Per tant, no s''executaran més simulacions de persistència de dades.
#XMSG: Error during persistency simulation occurred
PERSISTENCY_SIMULATION_ERROR=S''ha produït un error durant la simulació de persistència de dades per a la vista ''{0}''.
#XMSG: Persistency simulation is not executed, because prerequisites are not fulfilled and the view cannot be persisted
PREREQUISITES_NOT_FULFILLED=La simulació de persistència de dades no s''executa per a la vista ''{0}'', perquè els requisits previs no es compleixen i no es pot persistir la vista.
#XMSG: Persistency simulation is not executed, because the view does not exist in the database
VIEW_DOES_NOT_EXIST=Heu de desplegar la vista "{0}" per activar la simulació de persistència de dades.
#XMSG: Number of records cannot be determined, because the local table does not exist in the database
TABLE_DOES_NOT_EXIST=La taula local "{0}" no existeix a la base de dades i, per tant, no es pot determinar el nombre de registres per a aquesta taula.
##XMSG: Message start of cancellation for View Analyzer task
CANCEL_VIEW_ANALYZER_TRIGGERED=S''ha enviat un procés per aturar la tasca "Analitzador de vistes" {0} per a la vista "{1}". Hi pot haver un retard fins que s''aturi la tasca.
##XMSG: Message start to indicate that task is not active
CANCEL_VIEW_ANALYZER_NO_ACTIVE_TASK=La tasca "Analitzador de vistes" {0} per a la vista "{1}" no està activa.
#XMSG: Cancel View Analyzer task failure
CANCEL_VIEW_ANALYZER_FAILED=No s'ha pogut cancel·lar la tasca "Analitzador de vistes".
##XMSG: Message used in View Analyzer execution task when task is canceled
VIEW_ANALYZER_CANCELED=L''execució de l''Analitzador de vistes per a la vista "{0}" s''ha aturat mitjançant una tasca de cancel·lació.
##XMSG: Message start of cancelation for Model Validation task
CANCEL_MODEL_VALIDATION_TRIGGERED=S''ha enviat un procés per aturar la tasca "Validació de model" {0} per a la vista "{1}". Hi pot haver un retard fins que s''aturi la tasca.
##XMSG: Message to indicate that task is not active
CANCEL_MODEL_VALIDATION_NO_ACTIVE_TASK=La tasca "Validació de model" {0} de la vista "{1}" no està activa.
#XMSG: Cancel Model Validation task failure
CANCEL_MODEL_VALIDATION_FAILED=No s'ha pogut cancel·lar la tasca "Validació de model".
##XMSG: Message used in Model Validation task when task is canceled
MODEL_VALIDATION_CANCELED=L''execució de "Validació de model" per a la vista "{0}" s''ha aturat mitjançant una tasca de cancel·lació.
#XMSG: Message for Locked space error, before starting Model Validation task
MODEL_VALIDATION_LOCKED_SPACE_ERROR=No es pot executar "Validació de model" per a la vista "{0}", perquè l''espai "{1}" està bloquejat.
##XMSG: Message used in View Analyzer execution task when determination of number of rows fails
NUMBER_ROWS_DETERMINATION_FAILED=S''ha produït un error en determinar el nombre de files per a la taula local ''{0}''.
##XMSG: Message to indicate that SQL plan was created
EXECUTION_PLAN_CREATED=El fitxer de pla de l''analitzador SQL per a la vista "{0}" s''ha creat i es pot baixar.
#XFLD: Message to indicate starting of process to generate SQL Analyzer plan file
START_PLANVIZ_EXECUTION=S''inicia el procés per generar el fitxer de pla de l''analitzador SQL per a la vista "{0}".
#XFLD: Message to indicate starting of View Analyzer process
VIEW_ANALYZER_EXECUTION_STARTED=S'està iniciant l'execució de l'analitzador de vistes.
##XMSG: Message to indicate that SQL plan file cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_PLANVIZ_NOT_FULFILLED=El fitxer de pla de l''analitzador SQL no es pot generar per a la vista "{0}" perquè no es compleixen els requisits previs de persistència de dades.
##XMSG: Error message during  generation of SQL Analyzer plan file
PLANVIZ_ERROR=S''ha produït un error en generar el fitxer de pla de l''analitzador SQL per a la vista "{0}".
#XMSG: Message for Locked space error
VIEW_ANALYZER_LOCKED_SPACE_ERROR=No es pot executar l''Analitzador de vistes per a la vista ''{0}'', perquè l''espai ''{1}'' està bloquejat.
#XMSG: Message for persistency simulation without partitioning
PERSISTENCY_SIMULATION_WITHOUT_PARTITIONS=Les particions de la vista ''{0}'' no es tenen en compte durant la simulació de la persistència de dades.
#XMSG: Message for generation of SQL Analyzer plan file without considering partitioning
PLANVIZ_WITHOUT_PARTITIONS=Les particions de la vista ''{0}'' no es tenen en compte durant la generació del fitxer de pla de l''analitzador SQL.
#XMSG: Confirmation message for mass removal of persisted data
MassRemovePersistency_Confirm_Msg=Voleu eliminar les dades desades de forma persistent i tornar a canviar l'accés a dades a accés virtual?
#XMSG: Confirmation message for partial mass removal of selected persisted data
PartialMassRemovePersistency_Confirm_Msg={0} de les {1} vistes seleccionades tenen dades persistides. \n Voleu eliminar les dades desades de forma persistent i tornar a canviar l''accés a dades a accés virtual?
#XMSG: success message for stopping mass persistence
stopMassPersistenceSuccess=Estem eliminant les dades desades de forma persistent de les vistes seleccionades.
#XMSG: error message during mass stop persistence of a view (backend error)
STOP_MASS_PERSISTENCE_ERROR=S'ha produït un error en aturar la persistència de les vistes seleccionades.
#XMSG: memory analysis is only performed in space {0}
NO_MEMORY_ANALYSIS=Anàlisi de memòria realitzada només per a entitats de l''espai "{0}": "{1}" "{2}" s''ha omès.
##XMSG: Message to indicate that Explain Plan cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_EXPLAINPLAN_NOT_FULFILLED=No es pot generar l''arxiu de pla d''explicació per a la vista "{0}", perquè no es compleixen els requisits previs de la persistència.
#XMSG: Message for Explain Plan generation without partitioning
EXPLAINPLAN_WITHOUT_PARTITIONS=No es tenen en compte les particions de la vista "{0}" durant la generació de l''arxiu de pla d''explicació.
#XFLD: Message to indicate starting of process to generate Explain Plan file
START_EXPLAINPLAN_EXECUTION=S''inicia el procés per a generar l''arxiu de pla d''explicació per a la vista "{0}".
##XMSG: Message to indicate that Explain Plan was created
EXPLAINPLAN_CREATED=S''ha generat el fitxer Explica el pla per a la vista "{0}". Podeu visualitzar-lo fent clic a "Veure detalls" o descarregar-lo si teniu els permisos rellevants.
##XMSG: Error message during  generation of Explain Plan file
EXPLAINPLAN_ERROR=S''ha produït un error durant la generació de l''arxiu de pla d''explicació per a la vista "{0}".
##XMSG: Explain Plan file cannot be generated for the view {0}, because too many views are stacked on each other
EXPLAINPLAN_MODEL_TOO_COMPLEX=No es pot generar el fitxer Explica el pla per a la vista "{0}". S''apilen masses vistes una sobre l''altra. Pot ser que els models complexos causin errors fora de la memòria i un rendiment més lent. Es recomana persistir una vista.

##XMSG: Message to indicate that the space is locked
PERFORMANCE_ANALYSIS_LOCKED_SPACE_ERROR=No es pot executar l''anàlisi de rendiment per a la vista "{0}", perquè l''espai "{1}" està bloquejat.
##XMSG: The performance analysis task was cancelled
PERFORMANCE_ANALYSIS_CANCELED=S''ha cancel·lat l''anàlisi de rendiment per a la vista "{0}".
##XMSG: The performance analysis failed
PERFORMANCE_ANALYSIS_FAILED=L'anàlisi de rendiment ha estat fallit.
##XMSG: The performance analysis is finished
PERFORMANCE_ANALYSIS_FINISHED=L''anàlisi de rendiment per a la vista "{0}" ha conclòs. Visualitzeu-ne el resultat fent clic a "Veure detalls".
##XMSG: The view has input parameter with no default value
INPUT_PARAMETER_WITHOUT_DEFAULT_VALUE=Aquesta vista no es pot analitzar perquè té un paràmetre sense valor predeterminat.
##XMSG: The view is not fully deployed
VIEW_NOT_FULLY_DEPLOYED=Aquesta vista no es pot analitzar perquè no s'ha desplegat completament.
##XMSG: The view uses limited remote adapters
LIMITED_REMOTE_ADAPTER=Aquesta vista utilitza com a mínim un adaptador remot amb capacitats limitades com a polsador del filtre que falta o suport per "Recompte". Desar els objectes de forma persistent o replicar-los pot millorar el rendiment en temps d'execució.
##XMSG: The view uses remote adapter that does not support 'Limit'
REMOTE_ADAPTER_NOT_SUPPORTING_LIMIT=Aquesta vista utilitza, com a mínim, un adaptador remot que no admet "Límit". Es possible que s'hagin seleccionat més de 1000 registres. 
##XMSG: Performance analysis is executed by using default parameters of view'
INPUT_PARAMETER_WITH_DEFAULT_VALUE=L'anàlisi de rendiment s'executa utilitzant els valors predeterminats dels paràmetres de vista.
##XMSG: Message to indicate that an error occurred
PERFORMANCE_ANALYSIS_ERROR=S''ha produït un error durant l''anàlisi de rendiment per a la vista "{0}".
##XMSG: Message start of cancellation for Performance Analysis task
CANCEL_PERFORMANCE_ANALYSIS_TRIGGERED=S''ha enviat un procés per aturar la tasca Analitzador de rendiment {0} per a la vista "{1}". Hi pot haver un retard fins que s''aturi la tasca.
##XMSG: Message start to indicate that task is not active
CANCEL_PERFORMANCE_ANALYSIS_NO_ACTIVE_TASK=La tasca Anàlisi de rendiment {0} per a la vista "{1}" no està activa.
#XMSG: Cancel Performance Analysis task failure
CANCEL_PERFORMANCE_ANALYSIS_FAILED=Error en cancel·lar la tasca Anàlisi de rendiment.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Assignar-me programa
#XBUT: Pause schedule menu label
pauseScheduleLabel=Pausar programa
#XBUT: Resume schedule menu label
resumeScheduleLabel=Reprendre programa
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=S'ha produït un error en eliminar els programes.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=S'ha produït un error en assignar els programes.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=S'ha produït un error en pausar els programes.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=S'ha produït un error en reprendre els programes.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Suprimir {0} programes
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Modificar el propietari de {0} programes
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Pausar {0} programes
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Reprendre {0} programes
#XBUT: Select Columns Button
selectColumnsBtn=Seleccionar columnes
#XFLD: Refresh tooltip
TEXT_REFRESH=Actualitzar
#XFLD: Select Columns tooltip
text_selectColumns=Seleccionar columnes


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Runtime Metrics ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Label for Runtime Metrics
RuntimeMetricsTitle=Mètrica de temps d'execució per a
#XFLD : Label for Run Button
runButton=Executar
#XFLD : Label for Cancel Button
cancelButton=Cancel·lar
#XFLD : Label for Close Button
closeButton=Tancar
#XFLD : Label for Open View Analyzer Button
openViewAnalyzerButton=Obrir l'analitzador de vistes
#XFLD : Label for Generate Explain Plan Button
generateExplainPlanButton=Generar Explicar el pla 
#XFLD : Label for Previous Run Column
previousRun=Execució anterior
#XFLD : Label for Latest Run Column
latestRun=Última execució
#XFLD : Label for time Column
time=Hora
#XFLD : Label for Duration Column
duration=Durada
#XFLD : Label for Peak Memory Column
peakMemory=Memòria límit
#XFLD : Label for Number of Rows
numberOfRows=Nombre de files
#XFLD : Label for Overall Number of Sources
overallNumberOfSources=Nombre general de fonts
#XFLD : Label for Data Access Column
dataAccess=Accés a dades
#XFLD : Label for Local Tables
localTables=Taules locals
#XFLD : Label for federatedRemoteTables
federatedRemoteTables=Taules remotes federades (amb capacitats d'adaptador limitades)
#XTXT Text for initial state of the runtime metrics
initialState=Primer executeu l'Anàlisi de rendiment per obtenir la mètrica. Aquesta operació pot trigar una estona, però , si cal, podeu cancel·lar el procés.
#XMSG: Cancel confirmation message
CancelRunConfirmationMessage=Segur que voleu cancel·lar l’execució actual de l'anàlisi de rendiment?
#XTIT: Cancel dialog title
CancelRunTitle=Cancel·lar execució
#XFLD: Label for Number of Rows
NUMBER_ROWS=Nombre de files
#XFLD: Label for Overall Number of Sources
OVERALL_NUMBER_SOURCES=Nombre general de fonts
#XFLD: Label for Data Access
DATA_ACCESS=Accés a dades
#XFLD: Label for Remote tables
NUMBER_REMOTE_TABLES_LIMITED_ADAPTERS=Taules remotes federades (amb capacitats d'adaptador limitades)
#XFLD: Label for select statement
SELECT_STATEMENT='SELECT * FROM VIEW LIMIT 1000'
#XFLD: Label for duration
SELECT_RUNTIME=Durada
#XFLD: Label for peak memory
SELECT_PEAK_MEMORY=Memòria límit
#XFLD: Label for count statement
SELECT_COUNT_STATEMENT="SELECCIONAR RECOMPTE (*) DE LA VISTA"
#XFLD: Label for count duration
SELECT_COUNT_RUNTIME=Durada
#XFLD: Label for count peak memory
SELECT_COUNT_PEAK_MEMORY=Memòria límit
#XFLD: Label for count of number of local tables
NUMBER_LOCAL_TABLES_FILE=Taules locals (fitxer)
#XTXT: Text for running state of the runtime metrics
Running=En execució...
#XFLD: Label for time
Time=Hora
#XFLD: Label for virtual access
PA_VIRTUAL=Virtual
#XFLD: Label for persisted access
PA_PERSISTED=Persistit
PA_PARTIALLY_PERSISTED=Desat de forma persistent i parcial
#XTXT: Text for cancel
CancelRunSuccessMessage=S'està cancel·lant l'execució de l'anàlisi de rendiment.
#XTXT: Text for cancel error
CancelRunErrorMessage=S'ha produït un error en cancel·lar l'execució de l'anàlisi de rendiment.
#XTXT: Text for explain plan generation
ExplainPlanStarted=Es genera Explicar pla per a la vista "{0}".
#XTXT: Text for performance analysis
PerformanceAnalysisStarted=S''inicia l''anàlisi de rendiment per a la vista "{0}".
#XTXT: Text for performance analysis error
ErrorFetchingPerformanceAnalysis=S'ha produït un error en obtenir dades de l'anàlisi de rendiment.
#XTXT: Text for performance analysis error
conflictingTask=Ja hi ha una tasca d'anàlisi de rendiment en curs
#XFLD: Label for Errors
Errors=Error/s
#XFLD: Label for Warnings
Warnings=Advertència/es
#XFLD: Tooltip for missing privileges
openViewAnalyzerButtonTooltipNoPrivilege=Necessiteu el permís DWC_DATAINTEGRATION(actualitzar) per obrir l'analitzador de vistes.
#XFLD: Tooltip for missing privileges
generateExplainPlanButtonTooltipNoPrivilege=Necessiteu el permís DWC_RUNTIME(llegir) per generar Explicar pla.



#XFLD: Label for frequency column
everyLabel=Cada
#XFLD: Plural Recurrence text for Hour
hoursLabel=Hores
#XFLD: Plural Recurrence text for Day
daysLabel=Dies
#XFLD: Plural Recurrence text for Month
monthsLabel=Mesos
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minuts
