
#XBCB: Breadcrumb
viewMonitorBreadcrumb={spaceId}

#XFLD: Label for persisted view column
SOURCE=ソース
#XFLD: Label for persisted view column
NAME=名前
#XFLD: Label for persisted view column
NAME_LABEL=ビジネス名
#XFLD: Label for persisted view column
NAME_LABELNew=オブジェクト (ビジネス名)
#XFLD: Label for persisted view column
TECHINCAL_NAME=技術名
#XFLD: Label for persisted view column
TECHINCAL_NAMENew=オブジェクト (技術名)
#XFLD: Label for persisted view column
DATA_PERSISTENCY=データアクセス
#XFLD: Label for persisted view column
STATUS=ステータス
#XFLD: Label for persisted view column
LAST_UPDATED=最終更新
#XFLD: Label for persisted view column
IN_MEMORY_SIZE=ストレージに使用されているメモリ (MiB)
#XFLD: Label for persisted view column
DISK_SIZE=ストレージに使用されているディスク (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_NEWW=インメモリサイズ (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_HEADER=インメモリサイズ
#XFLD: Label for persisted view column
DISK_SIZE_NEWW=ディスク上のサイズ (MiB)
#XFLD: Label for persisted view column
DISK_SIZE_HEADER=ディスク上のサイズ
#XFLD: Label for schedule owner column
txtScheduleOwner=スケジュール所有者
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=スケジュール作成者を表示
#XFLD: Label for persisted view column
PERSISTED=永続化
#XFLD: Label for persisted view column
TYPE=タイプ
#XFLD: Label for View Selection Dialog column
changedOn=変更日付
#XFLD: Label for View Selection Dialog column
createdBy=作成者
#XFLD: Label for log details column
txtViewPersistencyLogs=ログの表示
#XFLD: Label for log details column
txtViewPersistencyLogsNew=詳細
#XFLD: text for values shown for Ascending sort order
SortInAsc=昇順ソート
#XFLD: text for values shown for Descending sort order
SortInDesc=降順ソート
#XFLD: Label for persisted view monitor heading
ViewMonitor_Heading=ビューモニタ
#XFLD: Label for persisted view monitor sub-heading
ViewMonitor_SubHeading=ビューのデータ永続性の監視および更新


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ DynamicPageHeader ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: text for values shown in column Persistence Status
txtLoading=ロード中
#XFLD: text for values shown in column Persistence Status
txtRunning=実行中
#XFLD: text for values shown in column Persistence Status
txtAvailable=利用可能
#XFLD: text for values shown in column Persistence Status
txtError=エラー
#XMSG: Error message for unknown Persistence type that gets logged in Kibana
unknownReplicationTypeError=複製タイプ "{0}" はサポートされていません。

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Content ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=最後のデータ永続性実行に使用された設定
#XMSG: Message for input parameter name
inputParameterLabel=入力パラメータ
#XMSG: Message for input parameter value
inputParameterValueLabel=値
#XMSG: Message for persisted data
inputParameterPersistedLabel=永続化時刻
#XTIT: Count for View in ViewMonitor Toolbar
txtViewCount=ビュー ({0})
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=ビュー永続性
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=データ永続性
#XBUT: Button to clear the selected view persistance
ClearViewPersistance=クリア
#XBUT: Button to stop the selected view persistance
stopPersistance=永続性を停止
#XFLD: Placeholder for Search field
txtSearch=検索
#XBUT: Tooltip for refresh button
txtRefresh=リフレッシュ
#XBUT: Tooltip for add view button
txtDeleteView=永続性を削除
#XBUT: Tooltip for load new peristence
loadNewPersistence=永続性を再開
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=新規スナップショットをロード
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=データ永続性を開始
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=永続化されたデータを削除
#XMSG: success message for starting persistence
startPersistenceSuccess=ビュー "{0}" を永続化しています。
#XMSG: success message for stopping persistence
stopPersistenceSuccess=ビュー "{0}" の永続化されたデータを削除しています。
#XMSG: success message for stopping persistence
deletePersistedViewSuccess=モニタ一覧からビュー "{0}" を削除しています。
#XMSG: error message during start persistence of a view (backend error)
ERROR_STARTING_PERSISTENCE=ビュー "{0}" のデータ永続性の開始中にエラーが発生しました。
#XMSG: error message during start persistence of a view (backend error)
PARAMERROR_STARTING_PERSISTENCE=ビュー "{0}" には入力パラメータが含まれているため永続化できません。
#XMSG: error message during start persistence of a view (backend error)
NUMBER_OF_PARAMETER_STARTING_PERSISTENCE=ビュー "{0}" には入力パラメータが 2 つ以上あるため永続化できません。
#XMSG: error message during start persistence of a view (backend error)
PARAMETER_DEFAULT_VALUE_STARTING_PERSISTENCE=ビュー "{0}" には入力パラメータにデフォルト値がないため永続化できません。
#XMSG: error message during start persistence of a view (backend error)
REDEPLOYMENT_DAC_REQUIRED=データ永続性をサポートするには、データアクセス制御 (DAC) "{0}" を再デプロイする必要があります。
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_STARTING_PERSISTENCE=データアクセス制御 (DAC) を含むビュー "{1}" が使用されているため、ビュー "{0}" を永続化することはできません。
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_EXTERNAL_STARTING_PERSISTENCE=別のスペースに属するデータアクセス制御 (DAC) が適用されたビューがビュー "{0}" で使用されているため、このビューは永続化することができません。
#XMSG: error message during start persistence of a view (backend error)
DACERROR_STRUCTURE_STARTING_PERSISTENCE=1 つ以上のデータアクセス制御 (DAC) の構造でデータ永続性がサポートされていないため、ビュー "{0}"を永続化できません。
#XMSG: error message during stop persistence of a view (backend error)
STOP_PERSISTENCE_ERROR=ビュー "{0}" の永続性の停止中にエラーが発生しました。
#XMSG: error message during stop persistence of a view (backend error)
DELETE_PERSISTED_VIEW_ERROR=永続化されたビュー "{0}" の削除中にエラーが発生しました。
#XMSG: Warning message for removing the persisted data
RemovePersistency_Confirm_Msg=永続化されたデータを削除して、ビュー "{0}" の仮想アクセスに切り替えますか?
#XMSG: Warning message for delete the view persistence
DeletePersistency_Confirm_Msg=モニタ一覧からビューを削除して、ビュー "{0}" の永続化されたデータを削除しますか?
#XMSG: error message for reading data from backend
txtReadBackendError=バックエンドからの読み込み中にエラーが発生したようです。
#XFLD: Label for No Data Error
NoDataError=エラー
#XMSG: message for conflicting task
Task_Already_Running=競合タスクがビュー "{0}" に対してすでに実行されています。

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Show All Views~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Text for show all views button
allViewsText=すべてのビュー ({0})
#XBUT: Text for show scheduled views button
scheduledText=スケジュール済み ({0})
#XBUT: Text for show persisted views button
persistedText=永続化 ({0})
#XBUT: Text for start analyzer button
startAnalyzer=ビューアナライザを開始
#XFLD: Message if repository is unavailable
repositoryErrorMsg=リポジトリを利用できないため、特定の機能が無効になっています。

#XFLD: Data Access - Virtual
Virtual=仮想
#XFLD: Data Access - Persisted
Persisted=永続化

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Non-Persisted View Selection Dialog~~~~~~~~~~~~~~~~~~~~~~~

#XTIT: Title for Non-Persisted View selection dialog
select_source=永続化対象のビューを選択

#XTIT: Placeholder for Non-Persisted View selection dialog search
searchView=ビューの検索
#XTIT: No data in the list of non-persisted view
No_Data=データなし
#XBUT: Button to select non-persisted view
ok=OK
#XBUT: Button to close the non-persisted views selection dialog
cancel=キャンセル

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~View persistency log message key~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message for view persistency logs
START_STOP_PERSISTENCY="{1}" に対するデータ永続性タスク実行を開始しています。
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY=ビュー "{1}" のデータを永続化しています。
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY)
START_VIEW_PERSISTENCY_PROCESS=ビュー "{0}" のデータを永続化する処理を開始しています。
#XFLD: Message for view persistency manual partition load
START_VIEW_PERSISTENCY_PROCESS_MANUAL_PARTITIONS=選択したパーティション ID: "{1}" のビュー "{0}" に対してデータを永続化するためにプロセスを起動しています。
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY=ビュー "{1}" の永続化されたデータを削除しています。
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY)
REMOVE_VIEW_PERSISTENCY=ビュー "{0}" の永続化されたデータを削除する処理を開始しています。
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_SUCCESS=ビュー "{1}"のデータが永続化されました。
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_SUCCESS)
START_VIEW_PERSISTENCY_PROCESS_SUCCESS=ビュー "{0}"のデータが永続化されました。
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY_SUCCESS=永続化されたデータが削除され、ビュー "{1}" に対する仮想データアクセスが復元されました。
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_SUCCESS)
REMOVE_VIEW_PERSISTENCY_SUCCESS=ビュー "{0}" の永続化されたデータを削除する処理をが完了しました。
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_FAILED=ビュー "{1}" のデータを永続化できません。
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_FAILED)
START_VIEW_PERSISTENCY_PROCESS_FAILED=ビュー "{0}" のデータを永続化できません。
#XFLD: Message for view persistency logs (for messages created before version 2022.05)
STOP_VIEW_PERSISTENCY_FAILED=ビュー "{1}" の永続化されたデータを削除できません。
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_FAILED)
REMOVE_VIEW_PERSISTENCY_FAILED=ビュー "{0}" の永続化されたデータを削除できません。
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_SUCCESS=ビュー "{1}" に対して "{3}" 件のレコードが永続化されました。
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace START_PERSISTENCY_RECORD_COUNT_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=ビュー "{1}" のデータ永続化テーブルに対して {0} 件のレコードが挿入されました。
#XFLD: Message used by SEAL repository for view persistency logs (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS_V2=ビュー "{1}".のデータ永続化テーブルに対して {0} 件のレコードが挿入されました。使用済みメモリ量: {2} GiB。
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_SUCCESS=ビュー "{1}" に対して永続化された "{3}" 件のレコードが削除されました。
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace STOP_PERSISTENCY_RECORD_COUNT_SUCCESS)
REMOVE_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=永続化されたデータが削除されました。"{0}" 件の永続化レコードが削除されました。
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_FAILED=ビュー "{1}" に対するレコード検数が失敗しました。
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_FAILED=ビュー "{1}" に対するレコード検数が失敗しました。
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS="{1}" に対するスケジュールが削除されました。
#XFLD: Message for view persistency logs (new message to replace STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS)
REMOVE_VIEW_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=ビュー "{0}" に対するスケジュールが削除されました。
#XFLD: Message for view persistency logs
STOP_PERSISTENCY_DELETE_SCHEDULE_FAILED="{1}" に対するスケジュール削除が失敗しました。
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_REDEPLOY=ビュー "{0}" は永続化の開始以降に変更されてデプロイされたため、永続化することができません。ビューの永続化をもう一度実行するか、次回のスケジュール済み実行まで待ってください。
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_DELETE=ビュー "{0}" は永続化の開始以降に削除されたため、永続化することができません。
#XFLD: Message for successful data persistency of a single partition (for messages created before version 2022.03)
START_PERSISTENCY_SINGLE_PARTITION_SUCCESS=値 "{1}" <= {2} < "{3}" のパーティションに対して {0} 件のレコードが永続化されました。
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (new message to replace START_PERSISTENCY_SINGLE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS=値 "{1}" <= {2} < "{3}" のパーティションに対して {0} 件のレコードが挿入されました。
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS_V2=値 "{1}" <= "{2}" < "{3}" のパーティションに対して {0} 件のレコードが挿入されました。使用済みメモリ量: {4} GiB。
#XFLD: Message for successful replication of a "others" partition (for messages created before version 2022.03)
START_PERSISTENCY_OTHERS_PARTITION_SUCCESS="その他" パーティションに対して {0} 件のレコードが永続化されました。
#XFLD: Message used by SEAL repository for successful replication of a "others" partition (new message to replace START_PERSISTENCY_OTHERS_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_OTHERS_PARTITION_SUCCESS="その他" パーティションに対して {0} 件のレコードが挿入されました。
#XFLD: Message for view persistency record count of partitions (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS=ビュー "{1}" に対して {4} 個のパーティションで {3} 件のレコードが永続化されました。
#XFLD: Message used by SEAL repository for view persistency record count of partitions (new message to replace START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={2} 個のパーティションでビュー "{1}" のデータ永続化テーブルに対して {0} 件のレコードが挿入されました。
#XFLD: Message used by SEAL repository for view persistency for partition wise refresh
START_VIEW_PERSISTENCY_RECORD_COUNT_REFRESHED_PARTITIONS_SUCCESS={0} 件のレコードがビュー "{1}" のデータ永続化テーブルに挿入されました。更新されたパーティション数: {2}、ロックされたパーティション数: {3}、合計パーティション数: {4}
#XFLD: Message used by SEAL repository for view persistency for manual partition load
START_VIEW_PERSISTENCY_RECORD_COUNT_MANUAL_PARTITIONS_SUCCESS={2} 個の選択されたパーティションでビュー "{1}" のデータ永続化テーブルに対して {0} 件のレコードが挿入されました。
#XFLD: Message used by SEAL repository for view persistency for locked partition data load
START_VIEW_PERSISTENCY_RECORD_COUNT_LOCKED_PARTITIONS_SUCCESS={0} 件のレコードがビュー "{1}" のデータ永続化テーブルに挿入されました。更新されたパーティション数: {2}、ロックされた変更されていないパーティション数: {3}、合計パーティション数: {4}
#XMSG: Message for unexpected error (for messages created before version 2022.03)
START_PERSISTENCY_UNEXPECTED_ERROR=ビュー "{0}" のデータを永続化するときに、予期しないエラーが発生しました。
#XMSG: Message for unexpected error (new message to replace START_PERSISTENCY_UNEXPECTED_ERROR)
START_VIEW_PERSISTENCY_UNEXPECTED_ERROR=ビュー "{0}" のデータを永続化するときに、予期しないエラーが発生しました。
#XMSG: Message for Locked space error
START_VIEW_PERSISTENCY_LOCKED_SPACE_ERROR=スペース "{1}" はロックされているため、ビュー "{0}" は永続化できません。
#XMSG: Message for unexpected error (for messages created before version 2022.03)
STOP_PERSISTENCY_UNEXPECTED_ERROR=ビュー "{0}" のデータを削除するときに、予期しないエラーが発生しました。
#XMSG: Message for unexpected error (new message to replace STOP_PERSISTENCY_UNEXPECTED_ERROR)
REMOVE_VIEW_PERSISTENCY_UNEXPECTED_ERROR=ビュー "{0}" の永続性を解除するときに、予期しないエラーが発生しました。
#XMSG: Message for ivalidated view
VIEW_PERSISTENCY_ABORTED_DEFINITION_INVALID=ビューによって間接的または直接利用されるオブジェクトが変更されたことを最も可能性の高い原因として、ビュー "{0}" の定義が無効になりました。問題を解決するか、根本的原因を特定するには、ビューを再デプロイしてみてください。
#XMSG: Message for persisted data removal (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT=ビュー "{0}" をデプロイするときに、永続化データが削除されます。
#XMSG: Message for persisted data removal of a consuming view
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW=利用ビュー "{0}" をデプロイするときに、永続化データが削除されます。
#XMSG: Message for persisted data removal of a consuming view due to a dac change
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW_DAC_CHANGED=永続化データのデータアクセス制御が変更されたため、利用ビュー "{0}" をデプロイするときに、永続化データは削除されます。
#XMSG: Message for persisted data removal (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DEPLOYMENT=ビュー "{0}" をデプロイするときに、永続化データが削除されます。
#XMSG: Message for deleted view (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION=ビュー "{0}" を削除すると、永続性が解除されます。
#XMSG: Message for deleted view (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DELETION=ビュー "{0}" を削除すると、永続性が解除されます。
#XMSG Message for removal of persisted data, because prerequisites not fulfilled
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_PREREQUISITES_NOT_FULFILLED=データ永続性の前提条件が満たされなくなったため、永続化データが削除されます。
#XMSG: Message for inconsistent persistency
VIEW_PERSISTENCY_INCONSISTENT_PERSISTENCY=ビュー "{0}" の永続性が不整合になりました。この問題を解決するには、永続化されたデータを削除してください。
#XMSG: Message for prerequisites
START_PERSISTENCY_CHECK_PREREQUISITES=ビュー "{0}" の永続化に関する前提条件をチェックしています。
#XMSG: Message for DAC deployed in deprecated way using table function and join (for messages created before 2025.05)
START_PERSISTENCY_DEPRECIATED_DAC=非推奨となっているデータアクセス制御 (DAC) を使用してビュー "{0}" がデプロイされました。パフォーマンスを改善するために、ビューを再度デプロイしてください。
#XMSG: Message for DAC deployed in deprecated way using table function and join
START_PERSISTENCY_DEPRECATED_DAC=非推奨となっているデータアクセス制御 (DAC) を使用してビュー "{0}" がデプロイされました。パフォーマンスを改善するために、ビューを再度デプロイしてください。
#XMSG: Message for roll back (for messages created before version 2022.03)
ROLLBACK_VIEW_PERSISTENCY=エラーが発生しました。ビュー "{0}" に対して以前のステータスの永続化が復元されました。
#XMSG: Message used by SEAL repository for rollback of running procedure to persist view (new message to replace ROLLBACK_VIEW_PERSISTENCY)
VIEW_PERSISTENCY_ROLLBACK=エラーが発生しました。ビュー "{0}" を永続化するプロセスが停止され、変更はロールバックされました。
#XMSG: Message used by SEAL repository for rollback of running procedure to remove persisted data of view
REMOVE_VIEW_PERSISTENCY_ROLLBACK=エラーが発生しました。ビュー "{0}" の永続化されたデータを削除するプロセスが停止され、変更はロールバックされました。
#XMSG: Message used by SEAL repository for persistency preparation
START_PERSISTENCY_PREPARATION=データを永続化する準備をしています。
#XMSG: Message used by SEAL repository for data insertion
START_INSERTING_PERSISTENCY_DATA=永続化テーブルにデータを挿入しています。
#XMSG: Message for null values - partitioning (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS="その他" パーティションに対して {0} 件の Null 値レコードが挿入されました。
#XMSG: Message used by SEAL repository for others partition values - partitioning (new message to replace START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS=値 "{2}" < "{1}" OR "{2}" >= "{3}" の "その他" パーティションに対して {0} 件のレコードが挿入されました。
#XMSG: Message used by SEAL repository for others partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS_V2=値 "{2}" < "{1}" OR "{2}" >= "{3}" の "その他" パーティションに対して {0} 件のレコードが挿入されました。使用済みメモリ量: {4} GiB。
#XMSG: Message used by SEAL repository for null partition values - partitioning
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS=値 "{1}" IS NULL の "その他" パーティションに対して、{0} 件のレコードが挿入されました。
#XMSG: Message used by SEAL repository for null partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS_V2=値 "{1}" IS NULL の "その他" パーティションに対して、{0} 件のレコードが挿入されました。使用済みメモリ量: {2} GiB。
##XFLD:Message used by SEAL repository for remote statistics
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS=関連データのロード: {0} リモート文。フェッチされた合計レコード: {1}。合計期間: {2} 秒。
##XFLD:Message used by SEAL repository for remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS_WITH_PARTITIONS={1} リモート文の {0} パーティションを使用した関連データのロード。フェッチされた合計レコード: {2}。合計期間: {3} 秒。
##XFLD:Message used by SEAL repository with UI ID for instructions of remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_INSTRUCTION=実行中に処理されるリモート文は、リモートクエリモニタを開くことで、パーティション固有メッセージの詳細に表示することができます。
##XFLD:Message for start reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS=デプロイメント後にビュー {0} の既存の永続化されたデータを再利用する処理を開始しています。
##XFLD:Message used by SEAL repository for reattach view persitency
START_VIEW_PERSISTENCY_REATTACHMENT=既存の永続化されたデータの再利用を開始します。
##XFLD:Message used by SEAL repository for reattach view persitency
START_FINILIZING_VIEW_PERSISTENCY_REATTACHMENT=既存の永続化されたデータを再利用しています。
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_COMPLETE=ビュー "{0}" の既存の永続化されたデータを再利用する処理が完了しました。
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_FAILED=デプロイメント後にビュー {0} の既存の永続化されたデータを再利用できませんでした。
#XMSG: Message used by SEAL repository for finalizing persistency
START_FINALIZING_PERSISTENCY=永続性を終了しています。
#XMSG: Message used by SEAL repository for restoring virtual access for view
REMOVE_VIEW_PERSISTENCY_VIRTUAL_ACCESS_RESTORED=ビュー "{0}" に対する仮想データアクセスが復元されました。.
#XMSG: Message used by SEAL repository for view that already has virtual access
REMOVE_VIEW_PERSISTENCY_ALREADY_VIRTUAL_ACCESS=ビュー "{0}" はすでに仮想データアクセスを行うことができます。永続化されたデータは削除されません。
#XMSG: Message for removing view persistency
VIEW_PERSISTENCY_REMOVED_FROM_MONITOR=ビュー "{0}" はビューモニタから削除されました。
#XMSG: Message for view doesnt exist.
VIEW_PERSISTENCY_VIEW_NOT_EXISTING=ビュー "{0}" がデータベースに存在しないか正しくデプロイされていないため、永続化できません。この問題を解決したり根本的な原因を特定したりするには、ビューを再デプロイしてください。
#XMSG: Message for persistency not enabled.
VIEW_PERSISTENCY_NOT_ENABLED=データ永続性が有効化されていません。この機能を有効化するには、テーブル/ビューをスペース "{0}" に再デプロイしてください。
#XMSG: Message for unexpected error due to node crash
VIEW_PERSISTENCY_UNEXPECTED_ERROR_GENERAL=技術的なエラーのために、最後のビュー永続化実行が中断されました。
#XFLD: Message for view persistency memory consumption
VIEW_PERSISTENCY_MEMORY_CONSUMPTION=ピーク時メモリ (= {0} GiB) がビュー永続化実行時に使用されました。
#XFLD: Message for view persistency timeout
VIEW_PERSISTENCY_TIMEOUT=ビュー {0} の永続性がタイムアウト (= {1} 時間) に達しました。
#XFLD: Message for not started asynchronous view persistency
VIEW_PERSISTENCY_ASYNC_NOT_STARTED=システム負荷が高かったため、ビュー永続化の非同期実行を開始できませんでした。並列実行されているタスクの数が多すぎないかどうかを確認してください。
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency (for messages created before version 2022.05)
START_VIEW_PERSISTENCY_EXECUTION_METHOD_DROP_AND_RENAME=既存の永続化テーブルが削除され、新しい永続化テーブルで置き換えられました。
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_RECREATE_AND_DROP=既存の永続化テーブルが削除され、新しい永続化テーブルで置き換えられました。
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_TRUNCATE_AND_COPY=既存の永続化テーブルが新しいデータで更新されました。
##XFLD: Message for missing authorizations for view persistency
VIEW_PERSISTENCY_MISSING_AUTH=データ永続性に対する権限がありません。
##XFLD: Message start of persistency cancellation
VIEW_PERSISTENCY_TRIGGER_CANCEL=ビュー {0}. を永続化する処理の取り消しを開始しています。
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_UNAVAILABLE_TASK_RUN=ビュー {0} に対して実行中のデータ永続性タスクが存在しないため、ビューを永続化する処理をキャンセルできませんでした。
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_NONDETERMINISTIC_TASK_RUN=ビュー {0} に対してデータ永続性タスクが実行されていないため、ビューを永続化する処理をキャンセルできませんでした。
##XFLD: Message for canceling a non running persitency task
VIEW_PERSISTENCY_CANCEL_NON_RUNNING_SELECTED_TASK=選択したデータ永続性タスク {1} が実行されていないため、ビュー {0} を永続化する処理をキャンセルできませんでした。
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_UNREACHABLE_PERSISTENCY_RUN=ビュー {0} に対するデータ永続化がまだ開始されていないため、ビューを永続化する処理を取り消しできませんでした。
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_COMPLETED_PERSISTENCY_RUN=ビュー {0} はすでに完了しているため、このビューを永続化する処理を取り消しできませんでした。
##XFLD: Message for failed cancellation
VIEW_PERSISTENCY_ABORTED_CANCEL_EXECUTION=ビュー {0}. を永続化する処理を取り消しできませんでした。
##XFLD: Message for finalizing persistency cancellation
VIEW_PERSISTENCY_FINALIZE_CANCEL=ビュー {0} のデータ永続性を停止する処理が送信されました。
##XFLD: Message used in persist task when cancellation is caught
VIEW_PERSISTENCY_CANCELED=ビュー {0} を永続化する処理が停止されました。
##XFLD: Message used in persist task when cancelation is caught and performed through a cancelation task
VIEW_PERSISTENCY_CANCELED_VIA_CANCEL_TASK=ビュー {0} を永続化する処理がキャンセルタスク {1} によって停止されました。
##XFLD: Message used in cancel task when the task is triggered by deployment
VIEW_PERSISTENCY_CANCEL_TRIGGERED_BY_DEPLOYMENT=ビュー {0} のデプロイ中に、データを永続化する処理をキャンセルしています。
##XFLD: Message used in case the cancelation is retriggered for a running task
VIEW_PERSISTENCY_CANCEL_ATTEMPT_RETRIGGER_CANCEL=ビュー {0} の永続化をキャンセルするためのタスクはすでに送信されています。
##XFLD: Message used to express the delay that may occur before the persistency task is stopped
VIEW_PERSISTENCY_CANCEL_DELAY_STOPPING_PERSISTENCY=ビュー {0} に対するデータ永続性タスクが停止されるまでに、待ち時間が発生する可能性があります。
##XFLD: Message used to reflect the task log identifier of the current running persistency task
VIEW_PERSISTENCY_CANCEL_CURRENT_RUNNING_TASK=ビュー {0} のデータは、タスク {1} で永続化されています。
##XFLD:Message used by SEAL repository for locked partitions and DACs
VIEW_PERSISTENCY_LOCKED_PARTITIONS_DACS=データアクセス制御 (DAC) による権限によってロック済みパーティションが変更された可能性があり、ロック済みパーティションで DAC 権限が考慮されていません。変更を適用するには、パーティションのロックを解除して新しいスナップショットをロードしてください。
##XFLD:Message used by SEAL repository for column structure change
VIEW_PERSISTENCY_COLUMN_STRUCTURE_CHANGE=列構造が変更され、既存の永続性テーブルに一致しなくなりました。永続化されたデータを削除し、永続性テーブルが更新されるように新しいデータ永続性を開始してください。
##XFLD: Message for out of memory
VIEW_PERSISTENCY_HANA_OUT_OF_MEMORY=SAP HANA データベースのメモリ不足エラーにより、タスクが失敗しました。
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_EXCEPTION_TRANSACTION_ROLLBACK=SAP HANA データベースの内部例外により、タスクが失敗しました。
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_SQL_EXECUTION_ISSUE=SAP HANA データベースの SQL 例外の問題により、タスクが失敗しました。
##XFLD: Message for HANA out of memory result
VIEW_PERSISTENCY_OUT_OF_MEMORY_RESULT_MESSAGE=HANA メモリ不足イベント理由: {0}
##XFLD: Message for admission control rejection
VIEW_PERSISTENCY_HANA_ADMISSION_CTRL_REJECTION=SAP HANA の受付制御の却下により、タスクが失敗しました。
##XFLD: Message for HANA back pressure / rate limit of HANA client
VIEW_PERSISTENCY_HANA_CLIENT_RATE_LIMIT_REJECTION=有効な SAP HANA 接続が多すぎるため、タスクが失敗しました。
#XMSG: Message for invalid persisted table name
VIEW_PERSISTENCY_PERSISTED_TABLE_INVALID=エラーが発生し、永続化テーブルが無効になりました。この問題を解決するには、永続化データを削除し、もう一度ビューを永続化してください。
#XMSG: Message for usahe of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION=ビューを永続化できません。ビューで、ユーザ継承が有効化されているリモートソースに基づくリモートテーブルが使用されています。ビューのリネージを確認してください。
#XMSG: Message for dynamic usage of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION_DYNAMIC=ビューを永続化できません。ビューで、ユーザ継承が有効化されているリモートソースに基づくリモートテーブルが使用されています。リモートテーブルは SQL スクリプトビューを介して動的に利用できます。ビューのリネージはリモートテーブルに表示されない場合があります。
#XMSG: Message for insufficient privileges or consumption of view with DAC via dynamic SQL
VIEW_PERSISTENCY_INSUF_PRIV_OR_DAC_DYN_SQL=権限が不十分な可能性があります。データプレビューを開いて、必要な権限があるかどうかを確認してください。権限がある場合は、動的な SQL スクリプトを介して利用される 2 番目のビューに、データアクセス制御 (DAC) が適用されている可能性があります。
#XMSG: deployment of DAC is deprecated and view need to be redeployed to be able to persist data
VIEW_PERSISTENCY_DEPRECATED_DAC=非推奨となっているデータアクセス制御 (DAC) を使用してビュー "{0}" がデプロイされました。ビューのデータを永続化できるように、ビューを再度デプロイしてください。
##XFLD:Message used by SEAL repository for input parameter
START_VIEW_PERSISTENCY_INPUT_PARAMETER=入力パラメータ "{1}" のデフォルト値 "{0}" を使用しています。
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Elastic compute node keys~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
##XFLD:Message used to reflect disablement of ecn replica
ECN_DISABLE_EXISTING_REPLICA=Elastic Compute ノードのレプリカが無効化されます。
##XFLD:Message used to reflect recreation of ecn replica
ECN_RECREATE_EXISTING_REPLICA=Elastic Compute ノードのレプリカが再作成されます。
##XFLD:Message used to reflect re-enablement of ecn replica
ECN_ENABLE_EXISTING_REPLICA=Elastic Compute ノードのレプリカが再有効化されます。
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Task Schedule Menu key~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message to schedule task
scheduleText=スケジュール
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=スケジュールを作成
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=スケジュールを編集
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=スケジュールを削除
#XFLD: Refresh frequency field
refreshFrequency=リフレッシュ頻度
#XFLD: Refresh frequency field
refreshFrequencyNew=頻度
#XFLD: Refresh frequency field
refreshFrequencyNewNew=スケジュールされた頻度
#XBUT: label for None
none=なし
#XBUT: label for Real-Time replication state
realtime=リアルタイム
#XFLD: Label for table column
txtNextSchedule=次回の実行
#XFLD: Label for table column
txtNextScheduleNew=スケジュールされた次回の実行
#XFLD: Label for table column
txtNumOfRecords=レコード数
#XFLD: Label for scheduled link
scheduledTxt=スケジュール済み
#XFLD: LABEL for partially persisted link
partiallyPersisted=部分的に永続化済み
#XFLD: Text for paused text
paused=一時停止

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ View Monitor Tasklog Details Action keys~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message for release lock actionn
releaseLockMsg=実行時間が通常よりも長くなっている場合は、実行に失敗してステータスが適切に更新されていない可能性があります。\r\n問題を解決するには、ロックを解除してステータスを "失敗" に設定することができます。
#XFLD: Label for release lock dialog
releaseLockText=ロックの解除

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Column header tooltip ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: tooltip for table column
txtViewTooltip=永続化ビューの名前
#XFLD: tooltip for table column
txtViewDataAccessTooltip=これはビューの可用性を示します
#XFLD: tooltip for table column
txtViewRefreshFreqTooltip=これはビューに対してスケジュールが定義されているかどうかを示します
#XFLD: tooltip for table column
txtViewStatusTooltip=永続化ビューのステータスを取得
#XFLD: tooltip for table column
txtViewLatestUpdateTooltip=永続化ビューが最後に更新された時間に関する情報を示します
#XFLD: tooltip for table column
txtViewNextRunTooltip=ビューに対してスケジュールが設定されている場合は、次の実行が予定されている期限を確認してください。
#XFLD: tooltip for table column
txtViewNumOfRecordsTooltip=レコード数を追跡します。
#XFLD: tooltip for table column
txtViewInMemorySizeTooltip=ビューによって使用されているメモリのサイズを追跡します
#XFLD: tooltip for table column
txtViewDiskSizeTooltip=ビューによって使用されているディスクのサイズを追跡します
#XMSG: Expired text
txtExpired=期限切れ

#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=オブジェクト "{0}" をタスクチェーンに追加できません。

#~~~~~~~~~~~View Analyzer task log messages~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message indicating number of records for a view, and memory consumption in Mebibyte during simulation of view persistency
RECORDS_PEAK_MEMORY=ビュー "{0}"には {1} 件のレコードがあります。ビューのデータ永続化シミュレーションでは {2} MB のメモリが使用されました。
#XMSG: View Analyzer execution failure
VIEW_ANALYZER_EXECUTION_FAILED=ビューアナライザの実行に失敗しました。
#XMSG: Missing authorizations for View Analyzer
VIEW_ANALYZER_MISSING_AUTH=ビューアナライザに対する権限がありません。
#XMSG: Maximum memory in Gibibyte is reached, therefore persistency simulation is stopped
MAXIMUM_MEMORY_REACHED=ビュー "{1}" のデータ永続化シミュレーション中に最大メモリ ({0} GiB) に達したため、以降のデータ永続化シミュレーションは実行されません。
#XMSG: Error during persistency simulation occurred
PERSISTENCY_SIMULATION_ERROR=ビュー "{0}" のデータ永続化シミュレーション中にエラーが発生しました。
#XMSG: Persistency simulation is not executed, because prerequisites are not fulfilled and the view cannot be persisted
PREREQUISITES_NOT_FULFILLED=前提条件が満たされず、ビューを永続化できないため、ビュー "{0}" のデータ永続化シミュレーションは実行されません。
#XMSG: Persistency simulation is not executed, because the view does not exist in the database
VIEW_DOES_NOT_EXIST=データ永続化シミュレーションを有効化するには、ビュー "{0}" をデプロイする必要があります。
#XMSG: Number of records cannot be determined, because the local table does not exist in the database
TABLE_DOES_NOT_EXIST=データベースにローカルテーブル "{0}" が存在しないため、このテーブルのレコード数を決定できません。
##XMSG: Message start of cancellation for View Analyzer task
CANCEL_VIEW_ANALYZER_TRIGGERED=ビュー "{1}" のビューアナライザタスク {0} を停止するプロセスが送信されました。タスクが停止されるまで、遅延が発生する可能性があります。
##XMSG: Message start to indicate that task is not active
CANCEL_VIEW_ANALYZER_NO_ACTIVE_TASK=ビュー "{1}" のビューアナライザタスク {0} が無効です。
#XMSG: Cancel View Analyzer task failure
CANCEL_VIEW_ANALYZER_FAILED=ビューアナライザタスクをキャンセルできませんでした。
##XMSG: Message used in View Analyzer execution task when task is canceled
VIEW_ANALYZER_CANCELED=ビュー "{0}" のビューアナライザ実行がタスクのキャンセルによって停止されました。
##XMSG: Message start of cancelation for Model Validation task
CANCEL_MODEL_VALIDATION_TRIGGERED=ビュー "{1}" のモデルチェックタスク {0} を停止するプロセスが送信されました。タスクが停止されるまで、遅延が発生する可能性があります。
##XMSG: Message to indicate that task is not active
CANCEL_MODEL_VALIDATION_NO_ACTIVE_TASK=ビュー "{1}" のモデルチェックタスク {0} が有効ではありません。
#XMSG: Cancel Model Validation task failure
CANCEL_MODEL_VALIDATION_FAILED=モデルチェックタスクをキャンセルできませんでした。
##XMSG: Message used in Model Validation task when task is canceled
MODEL_VALIDATION_CANCELED=ビュー "{0}" のモデルチェック実行がタスクのキャンセルによって停止されました。
#XMSG: Message for Locked space error, before starting Model Validation task
MODEL_VALIDATION_LOCKED_SPACE_ERROR=スペース "{1}" がロックされているため、ビュー "{0}" に対してモデルチェックを実行できません。
##XMSG: Message used in View Analyzer execution task when determination of number of rows fails
NUMBER_ROWS_DETERMINATION_FAILED=ローカルテーブル "{0}" の行数の決定中にエラーが発生しました。
##XMSG: Message to indicate that SQL plan was created
EXECUTION_PLAN_CREATED=ビュー "{0}" の SQL アナライザ計画ファイルが作成され、ダウンロードできます。
#XFLD: Message to indicate starting of process to generate SQL Analyzer plan file
START_PLANVIZ_EXECUTION=ビュー "{0}" の SQL アナライザ計画ファイルを生成する処理を開始しています。
#XFLD: Message to indicate starting of View Analyzer process
VIEW_ANALYZER_EXECUTION_STARTED=ビューアナライザの実行を開始します。
##XMSG: Message to indicate that SQL plan file cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_PLANVIZ_NOT_FULFILLED=データ永続性の前提条件が満たされていないため、ビュー "{0}" の SQL アナライザ計画ファイルを生成できません。
##XMSG: Error message during  generation of SQL Analyzer plan file
PLANVIZ_ERROR=ビュー "{0}" の SQL アナライザ計画ファイルの生成中にエラーが発生しました。
#XMSG: Message for Locked space error
VIEW_ANALYZER_LOCKED_SPACE_ERROR=スペース "{1}" がロックされているため、ビュー "{0}" に対してビューアナライザを実行できません。
#XMSG: Message for persistency simulation without partitioning
PERSISTENCY_SIMULATION_WITHOUT_PARTITIONS=データ永続性のシミュレーション中にビュー "{0}" のパーティションは考慮されません。
#XMSG: Message for generation of SQL Analyzer plan file without considering partitioning
PLANVIZ_WITHOUT_PARTITIONS=SQL アナライザ計画ファイルの生成中にビュー "{0}" のパーティションは考慮されません。
#XMSG: Confirmation message for mass removal of persisted data
MassRemovePersistency_Confirm_Msg=永続化されたデータを削除して、データアクセスを仮想アクセスに戻しますか?
#XMSG: Confirmation message for partial mass removal of selected persisted data
PartialMassRemovePersistency_Confirm_Msg=選択した {1} 個のビューのうちの {0} 個が永続化されたデータを取得しました。\n永続化されたデータを削除して、データアクセスを仮想アクセスに戻しますか?
#XMSG: success message for stopping mass persistence
stopMassPersistenceSuccess=選択したビューの永続化されたデータを削除しています。
#XMSG: error message during mass stop persistence of a view (backend error)
STOP_MASS_PERSISTENCE_ERROR=選択したビューの永続性の停止中にエラーが発生しました。
#XMSG: memory analysis is only performed in space {0}
NO_MEMORY_ANALYSIS=メモリ分析はスペース "{0}" のエンティティに対してのみ実行されます: "{1}" "{2}" はスキップされました。
##XMSG: Message to indicate that Explain Plan cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_EXPLAINPLAN_NOT_FULFILLED=永続性の前提条件が満たされていないため、ビュー "{0}" の実行プランファイルを生成できません。
#XMSG: Message for Explain Plan generation without partitioning
EXPLAINPLAN_WITHOUT_PARTITIONS=実行プランファイルの生成中にビュー "{0}" のパーティションは考慮されません。
#XFLD: Message to indicate starting of process to generate Explain Plan file
START_EXPLAINPLAN_EXECUTION=ビュー "{0}" の実行プランファイルを生成する処理を開始しています。
##XMSG: Message to indicate that Explain Plan was created
EXPLAINPLAN_CREATED=ビュー "{0}" の実行プランファイルが生成されました。"詳細表示" をクリックして表示することや、該当する権限がある場合はダウンロードすることができます。
##XMSG: Error message during  generation of Explain Plan file
EXPLAINPLAN_ERROR=ビュー "{0}" の実行プランファイルの生成中にエラーが発生しました。
##XMSG: Explain Plan file cannot be generated for the view {0}, because too many views are stacked on each other
EXPLAINPLAN_MODEL_TOO_COMPLEX=ビュー "{0}" に対して実行プランファイルを生成できません。積み重ねられているビューが多すぎます。複合モデルではメモリエラーが発生し、パフォーマンスが低下する可能性があります。ビューを永続化することをお奨めします。

##XMSG: Message to indicate that the space is locked
PERFORMANCE_ANALYSIS_LOCKED_SPACE_ERROR=スペース "{1}" がロックされているため、ビュー "{0}" のパフォーマンス分析を実行できません。
##XMSG: The performance analysis task was cancelled
PERFORMANCE_ANALYSIS_CANCELED=ビュー "{0}" のパフォーマンス分析がキャンセルされました。
##XMSG: The performance analysis failed
PERFORMANCE_ANALYSIS_FAILED=パフォーマンス分析が失敗しました。
##XMSG: The performance analysis is finished
PERFORMANCE_ANALYSIS_FINISHED=ビュー "{0}" のパフォーマンス分析が失敗しました。"詳細表示" をクリックして結果を表示してください。
##XMSG: The view has input parameter with no default value
INPUT_PARAMETER_WITHOUT_DEFAULT_VALUE=このビューは、デフォルト値のないパラメータがあるため、分析できません。
##XMSG: The view is not fully deployed
VIEW_NOT_FULLY_DEPLOYED=このビューは、完全にデプロイされていないため、分析できません。
##XMSG: The view uses limited remote adapters
LIMITED_REMOTE_ADAPTER=このビューでは、フィルタのプッシュダウンや 'Count' のサポートがないなど、機能が制限された状態で少なくとも 1 つのリモートアダプタが使用されています。オブジェクトを永続化または複製すると、実行時のパフォーマンスを向上させることができます。
##XMSG: The view uses remote adapter that does not support 'Limit'
REMOTE_ADAPTER_NOT_SUPPORTING_LIMIT=このビューでは、'Limit' がサポートされないリモートアダプタが少なくとも 1 つ使用されています。1,000 件を超えるレコードが選択されている可能性があります。
##XMSG: Performance analysis is executed by using default parameters of view'
INPUT_PARAMETER_WITH_DEFAULT_VALUE=パフォーマンス分析はビューパラメータのデフォルト値を使用して実行されます。
##XMSG: Message to indicate that an error occurred
PERFORMANCE_ANALYSIS_ERROR=ビュー "{0}" のパフォーマンス分析中にエラーが発生しました。
##XMSG: Message start of cancellation for Performance Analysis task
CANCEL_PERFORMANCE_ANALYSIS_TRIGGERED=ビュー "{1}" のパフォーマンス分析タスク{0}を停止するプロセスが送信されました。タスクが停止されるまで、遅延が発生する可能性があります。
##XMSG: Message start to indicate that task is not active
CANCEL_PERFORMANCE_ANALYSIS_NO_ACTIVE_TASK=ビュー "{1}" のパフォーマンス分析タスク {0} が無効です。
#XMSG: Cancel Performance Analysis task failure
CANCEL_PERFORMANCE_ANALYSIS_FAILED=パフォーマンス分析タスクをキャンセルできませんでした。

#XBUT: Assign schedule menu button label
assignScheduleLabel=スケジュールを自分に割り当て
#XBUT: Pause schedule menu label
pauseScheduleLabel=スケジュールを一時停止
#XBUT: Resume schedule menu label
resumeScheduleLabel=スケジュールを再開
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=スケジュールの削除中にエラーが発生しました。
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=スケジュールの割り当て中にエラーが発生しました。
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=スケジュールの一時停止中にエラーが発生しました。
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=スケジュールの再開中にエラーが発生しました。
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted={0} スケジュールを削除しています
#XMSG: Message for starting mass assign of schedules
massAssignStarted={0} スケジュールの所有者を変更しています
#XMSG: Message for starting mass pausing of schedules
massPauseStarted={0} スケジュールを一時停止しています
#XMSG: Message for starting mass resuming of schedules
massResumeStarted={0} スケジュールを再開しています
#XBUT: Select Columns Button
selectColumnsBtn=列を選択
#XFLD: Refresh tooltip
TEXT_REFRESH=リフレッシュ
#XFLD: Select Columns tooltip
text_selectColumns=列を選択


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Runtime Metrics ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Label for Runtime Metrics
RuntimeMetricsTitle=次のランタイムメトリクス
#XFLD : Label for Run Button
runButton=実行
#XFLD : Label for Cancel Button
cancelButton=キャンセル
#XFLD : Label for Close Button
closeButton=閉じる
#XFLD : Label for Open View Analyzer Button
openViewAnalyzerButton=ビューアナライザを開く
#XFLD : Label for Generate Explain Plan Button
generateExplainPlanButton=実行プランを生成
#XFLD : Label for Previous Run Column
previousRun=以前の実行
#XFLD : Label for Latest Run Column
latestRun=最終実行
#XFLD : Label for time Column
time=時刻
#XFLD : Label for Duration Column
duration=期間
#XFLD : Label for Peak Memory Column
peakMemory=ピーク時メモリ
#XFLD : Label for Number of Rows
numberOfRows=行数
#XFLD : Label for Overall Number of Sources
overallNumberOfSources=ソースの総数
#XFLD : Label for Data Access Column
dataAccess=データアクセス
#XFLD : Label for Local Tables
localTables=ローカルテーブル
#XFLD : Label for federatedRemoteTables
federatedRemoteTables=連携リモートテーブル (機能が制限されたアダプタを使用)
#XTXT Text for initial state of the runtime metrics
initialState=メトリクスを取得するには、最初にパフォーマンス分析を実行する必要があります。これには時間がかかる可能性がありますが、必要に応じてプロセスをキャンセルしてもかまいません。
#XMSG: Cancel confirmation message
CancelRunConfirmationMessage=パフォーマンス分析の現在の実行をキャンセルしてもよろしいですか?
#XTIT: Cancel dialog title
CancelRunTitle=実行をキャンセル
#XFLD: Label for Number of Rows
NUMBER_ROWS=行数
#XFLD: Label for Overall Number of Sources
OVERALL_NUMBER_SOURCES=ソースの総数
#XFLD: Label for Data Access
DATA_ACCESS=データアクセス
#XFLD: Label for Remote tables
NUMBER_REMOTE_TABLES_LIMITED_ADAPTERS=連携リモートテーブル (機能が制限されたアダプタを使用)
#XFLD: Label for select statement
SELECT_STATEMENT='SELECT * FROM VIEW LIMIT 1000'
#XFLD: Label for duration
SELECT_RUNTIME=期間
#XFLD: Label for peak memory
SELECT_PEAK_MEMORY=ピーク時メモリ
#XFLD: Label for count statement
SELECT_COUNT_STATEMENT='SELECT COUNT(*) FROM VIEW'
#XFLD: Label for count duration
SELECT_COUNT_RUNTIME=期間
#XFLD: Label for count peak memory
SELECT_COUNT_PEAK_MEMORY=ピーク時メモリ
#XFLD: Label for count of number of local tables
NUMBER_LOCAL_TABLES_FILE=ローカルテーブル (ファイル)
#XTXT: Text for running state of the runtime metrics
Running=実行しています...
#XFLD: Label for time
Time=時刻
#XFLD: Label for virtual access
PA_VIRTUAL=仮想
#XFLD: Label for persisted access
PA_PERSISTED=永続化済み
PA_PARTIALLY_PERSISTED=部分的に永続化済み
#XTXT: Text for cancel
CancelRunSuccessMessage=パフォーマンス分析の実行をキャンセルしています。
#XTXT: Text for cancel error
CancelRunErrorMessage=パフォーマンス分析実行のキャンセル中にエラーが発生しました。
#XTXT: Text for explain plan generation
ExplainPlanStarted=ビュー "{0}" の実行プランファイルを生成しています。
#XTXT: Text for performance analysis
PerformanceAnalysisStarted=ビュー "{0}" のパフォーマンス分析を開始しています。
#XTXT: Text for performance analysis error
ErrorFetchingPerformanceAnalysis=パフォーマンス分析データのフェッチ中にエラーが発生しました。
#XTXT: Text for performance analysis error
conflictingTask=パフォーマンス分析タスクはすでに実行されています
#XFLD: Label for Errors
Errors=エラー
#XFLD: Label for Warnings
Warnings=警告
#XFLD: Tooltip for missing privileges
openViewAnalyzerButtonTooltipNoPrivilege=ビューアナライザを開くには、DWC_DATAINTEGRATION(update) 権限が必要です。
#XFLD: Tooltip for missing privileges
generateExplainPlanButtonTooltipNoPrivilege=実行プランを生成するには、DWC_RUNTIME(read) 権限が必要です。



#XFLD: Label for frequency column
everyLabel=間隔
#XFLD: Plural Recurrence text for Hour
hoursLabel=時間
#XFLD: Plural Recurrence text for Day
daysLabel=日
#XFLD: Plural Recurrence text for Month
monthsLabel=月
#XFLD: Plural Recurrence text for Minutes
minutesLabel=分
