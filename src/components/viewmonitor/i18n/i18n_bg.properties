
#XBCB: Breadcrumb
viewMonitorBreadcrumb={spaceId}

#XFLD: Label for persisted view column
SOURCE=Източник
#XFLD: Label for persisted view column
NAME=Име
#XFLD: Label for persisted view column
NAME_LABEL=Бизнес наименование
#XFLD: Label for persisted view column
NAME_LABELNew=Обект (бизнес наименованиe)
#XFLD: Label for persisted view column
TECHINCAL_NAME=Техническо име
#XFLD: Label for persisted view column
TECHINCAL_NAMENew=Обект (техническо име)
#XFLD: Label for persisted view column
DATA_PERSISTENCY=Достъп до данните
#XFLD: Label for persisted view column
STATUS=Статус
#XFLD: Label for persisted view column
LAST_UPDATED=Последна актуализация
#XFLD: Label for persisted view column
IN_MEMORY_SIZE=Използвана памет за съхранение (MiB)
#XFLD: Label for persisted view column
DISK_SIZE=Използван диск за съхранение (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_NEWW=Размер на оперативната памет (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_HEADER=Размер на оперативната памет
#XFLD: Label for persisted view column
DISK_SIZE_NEWW=Размер на диска (MiB)
#XFLD: Label for persisted view column
DISK_SIZE_HEADER=Размер на диска
#XFLD: Label for schedule owner column
txtScheduleOwner=Собственик на график
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Показва създателя на графика
#XFLD: Label for persisted view column
PERSISTED=Трайно съхранено
#XFLD: Label for persisted view column
TYPE=Вид
#XFLD: Label for View Selection Dialog column
changedOn=Променено на
#XFLD: Label for View Selection Dialog column
createdBy=Създадено от
#XFLD: Label for log details column
txtViewPersistencyLogs=Преглед на журналите
#XFLD: Label for log details column
txtViewPersistencyLogsNew=Подробни данни
#XFLD: text for values shown for Ascending sort order
SortInAsc=Сортиране във възходящ ред
#XFLD: text for values shown for Descending sort order
SortInDesc=Сортиране в низходящ ред
#XFLD: Label for persisted view monitor heading
ViewMonitor_Heading=Монитор на изгледи
#XFLD: Label for persisted view monitor sub-heading
ViewMonitor_SubHeading=Наблюдение и поддръжка на трайното съхранение на изгледи


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ DynamicPageHeader ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: text for values shown in column Persistence Status
txtLoading=Зареждане
#XFLD: text for values shown in column Persistence Status
txtRunning=Изпълнява се
#XFLD: text for values shown in column Persistence Status
txtAvailable=Наличен
#XFLD: text for values shown in column Persistence Status
txtError=Грешка
#XMSG: Error message for unknown Persistence type that gets logged in Kibana
unknownReplicationTypeError=Вид репликиране „{0}“ не се поддържа.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Content ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Настройки, използвани при последното изпълнение на съхранението на данни:
#XMSG: Message for input parameter name
inputParameterLabel=Входен параметър
#XMSG: Message for input parameter value
inputParameterValueLabel=Стойност
#XMSG: Message for persisted data
inputParameterPersistedLabel=Час на трайно съхранение
#XTIT: Count for View in ViewMonitor Toolbar
txtViewCount=Изгледи ({0})
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Преглед на трайно съхранение
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Съхранение на данни
#XBUT: Button to clear the selected view persistance
ClearViewPersistance=Изчистване
#XBUT: Button to stop the selected view persistance
stopPersistance=Прекратяване на трайно съхранение
#XFLD: Placeholder for Search field
txtSearch=Търсене
#XBUT: Tooltip for refresh button
txtRefresh=Опресняване
#XBUT: Tooltip for add view button
txtDeleteView=Изтриване на трайно съхранение
#XBUT: Tooltip for load new peristence
loadNewPersistence=Рестартиране на запазването за трайно съхранение
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Зареждане на нова моментна снимка
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Стартиране на съхранението на данни
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Премахване на трайно съхранените данни
#XMSG: success message for starting persistence
startPersistenceSuccess=Запазваме изгледа „{0}“ за трайно съхранение.
#XMSG: success message for stopping persistence
stopPersistenceSuccess=Премахване трайно съхранените данни за изглед „{0}“.
#XMSG: success message for stopping persistence
deletePersistedViewSuccess=Премахваме изгледа „{0}“ от списъка за наблюдение.
#XMSG: error message during start persistence of a view (backend error)
ERROR_STARTING_PERSISTENCE=Възникна грешка при стартирането на запазване на изглед „{0}“ за трайно съхранение.
#XMSG: error message during start persistence of a view (backend error)
PARAMERROR_STARTING_PERSISTENCE=Изглед „{0}“ не може да бъде трайно съхранен, защото съдържа входни параметри.
#XMSG: error message during start persistence of a view (backend error)
NUMBER_OF_PARAMETER_STARTING_PERSISTENCE=Изглед „{0}“ не може да бъде трайно съхранен, тъй като съдържа няколко входни параметъра.
#XMSG: error message during start persistence of a view (backend error)
PARAMETER_DEFAULT_VALUE_STARTING_PERSISTENCE=Изглед „{0}“ не може да бъде трайно съхранен, тъй като входният параметър няма стойност по подразбиране.
#XMSG: error message during start persistence of a view (backend error)
REDEPLOYMENT_DAC_REQUIRED=За да осигурите поддръжка за трайното съхранение на данни, трябва да разгърнете повторно контрола на достъпа до данни (DAC) „{0}“.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_STARTING_PERSISTENCE=Изгледът „{0}“ не може да бъде трайно съхранен, защото използва изглед „{1}“, който съдържа контрол на достъпа до данни (DAC).
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_EXTERNAL_STARTING_PERSISTENCE=Изгледът „{0}“ не може да бъде трайно съхранен, защото използва друг изглед с контрол на достъпа до данните (DAC), който принадлежи към друго пространство.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_STRUCTURE_STARTING_PERSISTENCE=Изгледът „{0}“ не може да бъде трайно съхранен, защото структурата на един или повече от неговите контроли за достъп до данни (DAC) не поддържа запазване на данни.
#XMSG: error message during stop persistence of a view (backend error)
STOP_PERSISTENCE_ERROR=Възникна грешка при прекратяването на запазването на изглед „{0}“ за трайно съхранение.
#XMSG: error message during stop persistence of a view (backend error)
DELETE_PERSISTED_VIEW_ERROR=Възникна грешка при изтриването на трайно съхранения изглед „{0}“.
#XMSG: Warning message for removing the persisted data
RemovePersistency_Confirm_Msg=Искате ли да изтриете трайно съхранените данни и да преминете към виртуален достъп до изглед „{0}“?
#XMSG: Warning message for delete the view persistence
DeletePersistency_Confirm_Msg=Искате ли да премахнете изгледа от списъка на наблюдение и да изтриете трайно съхранените данни за изглед „{0}“?
#XMSG: error message for reading data from backend
txtReadBackendError=Изглежда се получи грешка при четене от бекенда.
#XFLD: Label for No Data Error
NoDataError=Грешка
#XMSG: message for conflicting task
Task_Already_Running=За изглед „{0}“ вече се изпълнява конфликтна задача.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Show All Views~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Text for show all views button
allViewsText=Всички изгледи ({0})
#XBUT: Text for show scheduled views button
scheduledText=Насрочено ({0})
#XBUT: Text for show persisted views button
persistedText=Трайно съхранено ({0})
#XBUT: Text for start analyzer button
startAnalyzer=Стартиране на анализатора на изгледи
#XFLD: Message if repository is unavailable
repositoryErrorMsg=Хранилището не е налично и някои функции са дезактивирани.

#XFLD: Data Access - Virtual
Virtual=Виртуално
#XFLD: Data Access - Persisted
Persisted=Трайно съхранено

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Non-Persisted View Selection Dialog~~~~~~~~~~~~~~~~~~~~~~~

#XTIT: Title for Non-Persisted View selection dialog
select_source=Избор на изглед за трайно съхранение

#XTIT: Placeholder for Non-Persisted View selection dialog search
searchView=Търсене на изгледи
#XTIT: No data in the list of non-persisted view
No_Data=Няма данни
#XBUT: Button to select non-persisted view
ok=ОК
#XBUT: Button to close the non-persisted views selection dialog
cancel=Отмяна

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~View persistency log message key~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message for view persistency logs
START_STOP_PERSISTENCY=Стартиране на изпълнение на трайно съхранение на данни за „{1}“.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY=Трайно съхраняване на данни за изглед "{1}".
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY)
START_VIEW_PERSISTENCY_PROCESS=Стартиране на процеса за трайно съхраняване на данни за изгледа „{0}“.
#XFLD: Message for view persistency manual partition load
START_VIEW_PERSISTENCY_PROCESS_MANUAL_PARTITIONS=Стартиране на процеса за трайно съхраняване на данни за изгледа „{0}“ с избрани ИД на дялове: „{1}“.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY=Премахване трайно съхранените данни за изгледа „{1}“.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY)
REMOVE_VIEW_PERSISTENCY=Стартиране на процеса по премахване на трайно съхранените данни за изгледа „{0}“.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_SUCCESS=Данните са съхранени трайно за изглед „{1}“.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_SUCCESS)
START_VIEW_PERSISTENCY_PROCESS_SUCCESS=Данните са съхранени трайно за изглед „{0}“.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY_SUCCESS=Трайно съхранените данни са премахнати и виртуалният достъп до данните за изглед „{1}“ е възстановен.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_SUCCESS)
REMOVE_VIEW_PERSISTENCY_SUCCESS=Завърши процесът по премахване на трайно съхранените данни за изгледа „{0}“.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_FAILED=Трайното съхраняване на данни за изглед „{1}“ е неуспешно.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_FAILED)
START_VIEW_PERSISTENCY_PROCESS_FAILED=Трайното съхраняване на данни за изглед „{0}“ е неуспешно.
#XFLD: Message for view persistency logs (for messages created before version 2022.05)
STOP_VIEW_PERSISTENCY_FAILED=Премахването на трайно съхранените данни за изглед „{1}“ е неуспешно.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_FAILED)
REMOVE_VIEW_PERSISTENCY_FAILED=Премахването на трайно съхранените данни за изглед „{0}“ е неуспешно.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_SUCCESS=Трайно съхранени записи за изглед „{1}“: {3}.
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace START_PERSISTENCY_RECORD_COUNT_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS={0} записа са вмъкнати в таблица на трайно съхранение за изглед „{1}“.
#XFLD: Message used by SEAL repository for view persistency logs (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS_V2={0} записа са вмъкнати в таблица на трайно съхранение за изглед „{1}“. Използвана памет: {2} GiB.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_SUCCESS=Трайно съхранени записа, премахнати за изглед „{1}“: {3}.
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace STOP_PERSISTENCY_RECORD_COUNT_SUCCESS)
REMOVE_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=Премахнати са трайно съхранени данни, изтрити трайно съхранени записи: „{0}“.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_FAILED=Извличането на recordCount за изглед „{1}“ е неуспешно.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_FAILED=Извличането на recordCount за изглед „{1}“ е неуспешно.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Графикът за „{1}“ е изтрит.
#XFLD: Message for view persistency logs (new message to replace STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS)
REMOVE_VIEW_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Графикът за изглед „{0}“ е изтрит.
#XFLD: Message for view persistency logs
STOP_PERSISTENCY_DELETE_SCHEDULE_FAILED=Изтриването на графика за „{1}“ е неуспешно.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_REDEPLOY=Изгледът „{0}“ не може да бъде трайно съхранен, защото е бил променен и разгърнат, след като сте стартирали трайното съхраняване. Опитайте отново или изчакайте следващото планирано изпълнение.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_DELETE=Изгледът „{0}“ не може да бъде трайно съхранен, защото е бил изтрит, след като сте стартирали трайното съхраняване.
#XFLD: Message for successful data persistency of a single partition (for messages created before version 2022.03)
START_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} записа са трайно съхранени в дял за стойности „{1}“ <= {2} < „{3}“.
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (new message to replace START_PERSISTENCY_SINGLE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} записа са вмъкнати в дял за стойности „{1}“ <= {2} < „{3}“.
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS_V2={0} записа са вмъкнати в дял за стойности „{1}“ <= „{2}“ < „{3}“. Използвана памет: {4} GiB.
#XFLD: Message for successful replication of a "others" partition (for messages created before version 2022.03)
START_PERSISTENCY_OTHERS_PARTITION_SUCCESS=Записи, трайно съхранени в дял „други“: {0}.
#XFLD: Message used by SEAL repository for successful replication of a "others" partition (new message to replace START_PERSISTENCY_OTHERS_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_OTHERS_PARTITION_SUCCESS=Записи, вмъкнати в дял „други“: {0}.
#XFLD: Message for view persistency record count of partitions (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={3} записа са трайно съхранени за изглед „{1}“ в {4} дяла.
#XFLD: Message used by SEAL repository for view persistency record count of partitions (new message to replace START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={0} записа са вмъкнати в таблица на трайно съхранение за изглед „{1}“ в {2} дяла.
#XFLD: Message used by SEAL repository for view persistency for partition wise refresh
START_VIEW_PERSISTENCY_RECORD_COUNT_REFRESHED_PARTITIONS_SUCCESS={0} записа са вмъкнати в таблица на трайно съхранение на данни за изглед „{1}“. Актуализирани дялове: {2}. Заключени дялове: {3}. Дялове общо: {4}
#XFLD: Message used by SEAL repository for view persistency for manual partition load
START_VIEW_PERSISTENCY_RECORD_COUNT_MANUAL_PARTITIONS_SUCCESS={0} записа са вмъкнати в таблицата на трайно съхранение на данни за изглед „{1}“ в {2} избрани дяла.
#XFLD: Message used by SEAL repository for view persistency for locked partition data load
START_VIEW_PERSISTENCY_RECORD_COUNT_LOCKED_PARTITIONS_SUCCESS={0} записа са вмъкнати в таблица на трайно съхранение на данни за изгледа „{1}“. Актуализирани дялове: {2}. Заключени непроменени дялове: {3}. Дялове общо: {4}.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
START_PERSISTENCY_UNEXPECTED_ERROR=Възникна неочаквана грешка при трайното съхранение на данни за изглед „{0}“.
#XMSG: Message for unexpected error (new message to replace START_PERSISTENCY_UNEXPECTED_ERROR)
START_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Възникна неочаквана грешка при трайното съхранение на данни за изглед „{0}“.
#XMSG: Message for Locked space error
START_VIEW_PERSISTENCY_LOCKED_SPACE_ERROR=Изгледът „{0}“ не може да бъде трайно съхранен, защото пространството „{1}“ е заключено.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
STOP_PERSISTENCY_UNEXPECTED_ERROR=Възникна неочаквана грешка при премахване на трайно съхранени данни за изглед „{0}“.
#XMSG: Message for unexpected error (new message to replace STOP_PERSISTENCY_UNEXPECTED_ERROR)
REMOVE_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Възникна неочаквана грешка при премахване на трайно съхранение за изглед „{0}“.
#XMSG: Message for ivalidated view
VIEW_PERSISTENCY_ABORTED_DEFINITION_INVALID=Дефиницията за изглед „{0}“ вече е невалидна, вероятно поради промяна на обект, използван директно или косвено от изгледа. Опитайте да разгърнете изгледа отново, за да разрешите проблема или за да откриете основната причина.
#XMSG: Message for persisted data removal (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT=При разгръщането на изглед „{0}“ са премахнати трайно съхранени данни.
#XMSG: Message for persisted data removal of a consuming view
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW=При разгръщането на използвания изглед „{0}“ са премахнати трайно съхранени данни.
#XMSG: Message for persisted data removal of a consuming view due to a dac change
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW_DAC_CHANGED=Премахнати са трайно съхранени данни при разгръщането на използвания изглед „{0}“, защото контролът му за достъп до данни е променен.
#XMSG: Message for persisted data removal (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DEPLOYMENT=При разгръщането на изглед „{0}“ са премахнати трайно съхранени данни.
#XMSG: Message for deleted view (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION=При изтриването на изглед „{0}“ е премахнато трайно съхранение.
#XMSG: Message for deleted view (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DELETION=При изтриването на изглед „{0}“ е премахнато трайно съхранение.
#XMSG Message for removal of persisted data, because prerequisites not fulfilled
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_PREREQUISITES_NOT_FULFILLED=Трайно съхранените данни са премахнати, тъй като вече не са изпълнени предварителните изисквания за съхранение.
#XMSG: Message for inconsistent persistency
VIEW_PERSISTENCY_INCONSISTENT_PERSISTENCY=В трайното съхранение на изглед „{0}“ възникнаха несъответствия. За да отстраните проблема, премахнете трайно съхранените данни.
#XMSG: Message for prerequisites
START_PERSISTENCY_CHECK_PREREQUISITES=Проверка на предпоставките за трайно съхранение на изглед „{0}“.
#XMSG: Message for DAC deployed in deprecated way using table function and join (for messages created before 2025.05)
START_PERSISTENCY_DEPRECIATED_DAC=Изгледът "{0}" е разгърнат с помощта на непрепоръчителен контрол на достъпа до данни (DAC). Разгърнете го отново, за да подобрите представянето.
#XMSG: Message for DAC deployed in deprecated way using table function and join
START_PERSISTENCY_DEPRECATED_DAC=Изгледът "{0}" е разгърнат с помощта на непрепоръчителен контрол на достъпа до данни (DAC). Разгърнете го отново, за да подобрите представянето.
#XMSG: Message for roll back (for messages created before version 2022.03)
ROLLBACK_VIEW_PERSISTENCY=Възникна грешка. Предходното състояние на трайно съхранение е възстановено за изглед „{0}“.
#XMSG: Message used by SEAL repository for rollback of running procedure to persist view (new message to replace ROLLBACK_VIEW_PERSISTENCY)
VIEW_PERSISTENCY_ROLLBACK=Възникна грешка. Процесът за трайно съхранение на изгледа „{0}” е спрян и промените са анулирани.
#XMSG: Message used by SEAL repository for rollback of running procedure to remove persisted data of view
REMOVE_VIEW_PERSISTENCY_ROLLBACK=Възникна грешка. Процесът по премахване на трайно съхранените данни на изгледа „{0}” е спрян и промените са анулирани.
#XMSG: Message used by SEAL repository for persistency preparation
START_PERSISTENCY_PREPARATION=Подготовка за трайно съхранение на данни.
#XMSG: Message used by SEAL repository for data insertion
START_INSERTING_PERSISTENCY_DATA=Вмъкване на данни в таблица за трайно съхранение.
#XMSG: Message for null values - partitioning (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS=Записи с нулева стойност, вмъкнати в дял „други“: {0}.
#XMSG: Message used by SEAL repository for others partition values - partitioning (new message to replace START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS={0} записа са вмъкнати в дял „други“ за стойности „{2}“ < „{1}“ OR „{2}“ >= „{3}“.
#XMSG: Message used by SEAL repository for others partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS_V2={0} записа са вмъкнати в дял за стойности „други“ „{2}“ < „{1}“ OR „{2}“ >= „{3}“. Използвана памет: {4} GiB.
#XMSG: Message used by SEAL repository for null partition values - partitioning
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS={0} записа са вмъкнати в дял „други“ за стойности „{1}“ IS NULL.
#XMSG: Message used by SEAL repository for null partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS_V2={0} записа са вмъкнати в дял „други“ за стойности „{1}“ IS NULL. Използвана памет: {2} GiB.
##XFLD:Message used by SEAL repository for remote statistics
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS=Зареждане на релевантните данни: {0} отдалечени инструкции. Общо извлечени записи: {1}. Обща продължителност: {2} секунди.
##XFLD:Message used by SEAL repository for remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS_WITH_PARTITIONS=Зареждане на релевантните данни чрез {0} дяла с {1} отдалечени инструкции. Общо извлечени записи: {2}. Обща продължителност: {3} секунди.
##XFLD:Message used by SEAL repository with UI ID for instructions of remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_INSTRUCTION=Отдалечените инструкции, обработени по време на изпълнението, се показват в подробните данни на съобщенията за дялове в Монитора на отдалечените заявки.
##XFLD:Message for start reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS=Стартиране на процеса за повторно използване на съществуващите устойчиви данни за изглед {0} след разгръщане.
##XFLD:Message used by SEAL repository for reattach view persitency
START_VIEW_PERSISTENCY_REATTACHMENT=Начало на повторно използване на съществуващите устойчиви данни.
##XFLD:Message used by SEAL repository for reattach view persitency
START_FINILIZING_VIEW_PERSISTENCY_REATTACHMENT=Повторно използване на съществуващите устойчиви данни.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_COMPLETE=Процесът за преизползване на съществуващите устойчиви данни е завършен за изгледа {0}.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_FAILED=Неуспешно повторно използване на съществуващите устойчиви данни за изгледа {0} след разгръщане.
#XMSG: Message used by SEAL repository for finalizing persistency
START_FINALIZING_PERSISTENCY=Финализиране на трайно съхранение.
#XMSG: Message used by SEAL repository for restoring virtual access for view
REMOVE_VIEW_PERSISTENCY_VIRTUAL_ACCESS_RESTORED=Достъпът до виртуални данни е възстановен за изглед „{0}“.
#XMSG: Message used by SEAL repository for view that already has virtual access
REMOVE_VIEW_PERSISTENCY_ALREADY_VIRTUAL_ACCESS=Изгледът „{0}” вече има виртуален достъп до данни. Няма премахнати трайно съхранени данни.
#XMSG: Message for removing view persistency
VIEW_PERSISTENCY_REMOVED_FROM_MONITOR=Изглед „{0}“ е премахнат от монитора „Изгледи“.
#XMSG: Message for view doesnt exist.
VIEW_PERSISTENCY_VIEW_NOT_EXISTING=Изглед „{0}“ не съществува в базата данни, или не е правилно разгърнат, затова не може да бъде запазен. Опитайте да го разгърнете повторно, за да разрешите проблема, или да идентифицирате основната причина.
#XMSG: Message for persistency not enabled.
VIEW_PERSISTENCY_NOT_ENABLED=Трайно съхранение на данни не е активирано. Разгърнете повторно таблица/изглед в пространство „{0}“, за да активирате функционалността.
#XMSG: Message for unexpected error due to node crash
VIEW_PERSISTENCY_UNEXPECTED_ERROR_GENERAL=Последното изпълнение на трайно съхранение на изгледи е прекъснато поради технически грешки.
#XFLD: Message for view persistency memory consumption
VIEW_PERSISTENCY_MEMORY_CONSUMPTION=Във време на изпълнение на трайно съхранение на изгледи са използвани {0} GiB от пиковата памет.
#XFLD: Message for view persistency timeout
VIEW_PERSISTENCY_TIMEOUT=Трайното съхранение на изглед {0} достигна таймаут от {1} часа.
#XFLD: Message for not started asynchronous view persistency
VIEW_PERSISTENCY_ASYNC_NOT_STARTED=Силнонатоварената система блокира стартирането на асинхронно изпълнение на трайното съхранение на изгледи. Проверете дали не се изпълняват едновременно прекалено много задачи.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency (for messages created before version 2022.05)
START_VIEW_PERSISTENCY_EXECUTION_METHOD_DROP_AND_RENAME=Съществуващата таблица на трайно съхранение е изтрита и заменена от нова.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_RECREATE_AND_DROP=Съществуващата таблица на трайно съхранение е изтрита и заменена от нова.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_TRUNCATE_AND_COPY=Съществуващата таблица на трайно съхранение е актуализирана с нови данни.
##XFLD: Message for missing authorizations for view persistency
VIEW_PERSISTENCY_MISSING_AUTH=Липсват оторизации за трайно съхранение на данни.
##XFLD: Message start of persistency cancellation
VIEW_PERSISTENCY_TRIGGER_CANCEL=Начало на отмяна на процеса за трайно съхранение на изгледа {0}.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_UNAVAILABLE_TASK_RUN=Процесът за трайно съхранение на изгледа не беше отменен, защото няма текуща задача за трайно съхранение на данни за изгледа {0}.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_NONDETERMINISTIC_TASK_RUN=Процесът за трайно съхранение на изгледа не беше отменен, защото няма текуща задача за трайно съхранение на данни за изгледа {0}.
##XFLD: Message for canceling a non running persitency task
VIEW_PERSISTENCY_CANCEL_NON_RUNNING_SELECTED_TASK=Неуспешна отмяна на процеса за трайно съхраняване на изгледа {0}, защото избраната задача за трайно съхранение на данни {1} не се изпълнява.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_UNREACHABLE_PERSISTENCY_RUN=Процесът за трайно съхранение на изгледа не беше отменен успешно, защото трайното съхранение за изгледа {0} все още не е започнало.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_COMPLETED_PERSISTENCY_RUN=Процесът за трайно съхранение на изгледа {0} не беше отменен успешно, защото вече е завършен.
##XFLD: Message for failed cancellation
VIEW_PERSISTENCY_ABORTED_CANCEL_EXECUTION=Процесът за трайно съхранение на изгледа {0} не беше отменен успешно.
##XFLD: Message for finalizing persistency cancellation
VIEW_PERSISTENCY_FINALIZE_CANCEL=Процесът за спиране на трайното съхранение на изгледа {0} е изпратен.
##XFLD: Message used in persist task when cancellation is caught
VIEW_PERSISTENCY_CANCELED=Процесът за трайно съхранение на изгледа {0} е спрян.
##XFLD: Message used in persist task when cancelation is caught and performed through a cancelation task
VIEW_PERSISTENCY_CANCELED_VIA_CANCEL_TASK=Процесът за трайно съхранение на изгледа {0} е спрян чрез задача за анулиране {1}.
##XFLD: Message used in cancel task when the task is triggered by deployment
VIEW_PERSISTENCY_CANCEL_TRIGGERED_BY_DEPLOYMENT=Отмяна на процеса за трайно съхранение на данни при разгръщане на изгледа {0}.
##XFLD: Message used in case the cancelation is retriggered for a running task
VIEW_PERSISTENCY_CANCEL_ATTEMPT_RETRIGGER_CANCEL=Вече е изпратена предходна задача за анулиране на трайното съхранение на изглед {0}.
##XFLD: Message used to express the delay that may occur before the persistency task is stopped
VIEW_PERSISTENCY_CANCEL_DELAY_STOPPING_PERSISTENCY=Възможно е да има забавяне преди спирането на задачата за трайно съхранение за изглед {0}.
##XFLD: Message used to reflect the task log identifier of the current running persistency task
VIEW_PERSISTENCY_CANCEL_CURRENT_RUNNING_TASK=В задача {1} данните за изгледа {0} са трайно съхранени.
##XFLD:Message used by SEAL repository for locked partitions and DACs
VIEW_PERSISTENCY_LOCKED_PARTITIONS_DACS=Оторизациите, предоставени от DAC, може да са се променили и да не се вземат предвид от заключените дялове. Отключете дяловете и заредете нова моментна снимка, за да приложите промените.
##XFLD:Message used by SEAL repository for column structure change
VIEW_PERSISTENCY_COLUMN_STRUCTURE_CHANGE=Структурата на колоните е променена и вече не съответства на съществуващата таблица за трайно съхранение. Премахнете трайно съхранените данни и стартирайте ново трайно съхранение, за да се актуализира таблицата ви.
##XFLD: Message for out of memory
VIEW_PERSISTENCY_HANA_OUT_OF_MEMORY=Задачата е неуспешна поради грешка за липса на памет в базата данни на SAP HANA.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_EXCEPTION_TRANSACTION_ROLLBACK=Задачата е неуспешна поради вътрешно изключение в базата данни на SAP HANA.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_SQL_EXECUTION_ISSUE=Задачата е неуспешна поради вътрешен проблем при изпълнението на SQL в базата данни на SAP HANA.
##XFLD: Message for HANA out of memory result
VIEW_PERSISTENCY_OUT_OF_MEMORY_RESULT_MESSAGE=Причина за събитието за недостиг на памет в HANA: {0}
##XFLD: Message for admission control rejection
VIEW_PERSISTENCY_HANA_ADMISSION_CTRL_REJECTION=Задачата е неуспешна заради отхвърляне от контрола на приемане на SAP HANA.
##XFLD: Message for HANA back pressure / rate limit of HANA client
VIEW_PERSISTENCY_HANA_CLIENT_RATE_LIMIT_REJECTION=Задачата е неуспешна, защото има прекалено много активни връзки SAP HANA.
#XMSG: Message for invalid persisted table name
VIEW_PERSISTENCY_PERSISTED_TABLE_INVALID=Възникна грешка и таблицата на трайно съхранение е невалидна. За да разрешите проблема, премахнете трайно съхранените данни и съхранете трайно изгледа отново.
#XMSG: Message for usahe of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION=Изгледът не може да бъде запазен. Той използва отдалечена таблица, базирана на отдалечен източник с активирано попълване на потребители. Проверете произхода на изгледа.
#XMSG: Message for dynamic usage of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION_DYNAMIC=Изгледът не може да бъде запазен. Той използва отдалечена таблица, базирана на отдалечен източник с активирано попълване на потребители. Отдалечената таблица може да се използва динамично чрез изглед на SQL скрипт. Произходът на изгледа може да не показва отдалечената таблица.
#XMSG: Message for insufficient privileges or consumption of view with DAC via dynamic SQL
VIEW_PERSISTENCY_INSUF_PRIV_OR_DAC_DYN_SQL=Вашите привилегии може да са недостатъчни. Отворете прегледа на данните, за да видите дали имате необходимите привилегии. Ако е така, към втори изглед, използван чрез динамичен SQL скрипт, може да има приложен контрол на достъпа до данните.
#XMSG: deployment of DAC is deprecated and view need to be redeployed to be able to persist data
VIEW_PERSISTENCY_DEPRECATED_DAC=Изгледът "{0}" е разгърнат с помощта на непрепоръчителен контрол на достъпа до данни (DAC). Разгърнете го отново, за да може да съхраните трайно данните за изгледа.
##XFLD:Message used by SEAL repository for input parameter
START_VIEW_PERSISTENCY_INPUT_PARAMETER=Използване на стойността по подразбиране „{0}“ за входен параметър „{1}“.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Elastic compute node keys~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
##XFLD:Message used to reflect disablement of ecn replica
ECN_DISABLE_EXISTING_REPLICA=Репликата в еластичния изчислителен възел е дезактивирана.
##XFLD:Message used to reflect recreation of ecn replica
ECN_RECREATE_EXISTING_REPLICA=Репликата в еластичния изчислителен възел е създадена повторно.
##XFLD:Message used to reflect re-enablement of ecn replica
ECN_ENABLE_EXISTING_REPLICA=Репликата в еластичния изчислителен възел е активирана повторно.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Task Schedule Menu key~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message to schedule task
scheduleText=График
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Създаване на график
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Редактиране на графика
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Изтриване на графика
#XFLD: Refresh frequency field
refreshFrequency=Честота на опресняване
#XFLD: Refresh frequency field
refreshFrequencyNew=Честота
#XFLD: Refresh frequency field
refreshFrequencyNewNew=Планирана честота
#XBUT: label for None
none=Няма
#XBUT: label for Real-Time replication state
realtime=В реално време
#XFLD: Label for table column
txtNextSchedule=Следващо изпълнение
#XFLD: Label for table column
txtNextScheduleNew=Планирано следващо изпълнение
#XFLD: Label for table column
txtNumOfRecords=Брой записи
#XFLD: Label for scheduled link
scheduledTxt=Планирано
#XFLD: LABEL for partially persisted link
partiallyPersisted=Частично съхранено
#XFLD: Text for paused text
paused=Пауза

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ View Monitor Tasklog Details Action keys~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message for release lock actionn
releaseLockMsg=Ако едно изпълнение продължи по-дълго от обичайното, това може да означава, че е неуспешно и че статусът не е актуализиран правилно. \r\n За да разрешите проблема, може да деблокирате заключването и да зададете статуса му на "неуспешно".
#XFLD: Label for release lock dialog
releaseLockText=Деблокиране на заключване

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Column header tooltip ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: tooltip for table column
txtViewTooltip=Име на изгледа на трайно съхранение
#XFLD: tooltip for table column
txtViewDataAccessTooltip=Това посочва наличността на изгледа
#XFLD: tooltip for table column
txtViewRefreshFreqTooltip=Това посочва дали за изгледа има дефиниран график
#XFLD: tooltip for table column
txtViewStatusTooltip=Вземане статуса на изгледа на трайно съхранение
#XFLD: tooltip for table column
txtViewLatestUpdateTooltip=Предоставя информация относно кога последно е актуализиран изгледът на трайно съхранение.
#XFLD: tooltip for table column
txtViewNextRunTooltip=Ако е зададен график за изгледа, може да видите за кога е насрочено следващото изпълнение
#XFLD: tooltip for table column
txtViewNumOfRecordsTooltip=Проследяване на броя на записите.
#XFLD: tooltip for table column
txtViewInMemorySizeTooltip=Проследявайте каква част от вашата памет използва изгледът
#XFLD: tooltip for table column
txtViewDiskSizeTooltip=Проследявайте каква част от вашия диск заема изгледът
#XMSG: Expired text
txtExpired=С изтекъл срок

#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Обект „{0}“ не може да бъде добавен към веригата задачи.

#~~~~~~~~~~~View Analyzer task log messages~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message indicating number of records for a view, and memory consumption in Mebibyte during simulation of view persistency
RECORDS_PEAK_MEMORY=Изглед „{0}“ има {1} записа. Симулация на трайно съхранение на този изглед е използвала {2} MiB от паметта.
#XMSG: View Analyzer execution failure
VIEW_ANALYZER_EXECUTION_FAILED=Неуспешно изпълнение на анализатор на изгледа
#XMSG: Missing authorizations for View Analyzer
VIEW_ANALYZER_MISSING_AUTH=Липсват оторизации за анализатор на изгледи.
#XMSG: Maximum memory in Gibibyte is reached, therefore persistency simulation is stopped
MAXIMUM_MEMORY_REACHED=Максималната памет от {0} GiB е достигната при симулирането на трайно съхранение на изглед „{1}“. По тази причина няма да се изпълняват повече симулации на трайно съхранение.
#XMSG: Error during persistency simulation occurred
PERSISTENCY_SIMULATION_ERROR=Възникна грешка при симулация на трайно съхранение за изглед „{0}“.
#XMSG: Persistency simulation is not executed, because prerequisites are not fulfilled and the view cannot be persisted
PREREQUISITES_NOT_FULFILLED=Симулацията на трайно съхранение не е изпълнена за изглед „{0}“, защото предварителните изисквания не са изпълнени и изгледът не може да бъде запазен.
#XMSG: Persistency simulation is not executed, because the view does not exist in the database
VIEW_DOES_NOT_EXIST=Необходимо е да разгърнете изгледа „{0}“, за да активирате симулацията на трайно съхранение на данните.
#XMSG: Number of records cannot be determined, because the local table does not exist in the database
TABLE_DOES_NOT_EXIST=Локалната таблица „{0}“ не съществува в базата данни. Следователно броят на записите не може да бъде определен за тази таблица.
##XMSG: Message start of cancellation for View Analyzer task
CANCEL_VIEW_ANALYZER_TRIGGERED=Процесът по спиране на задачата на анализатора на изгледи {0} за изглед „{1}“ е стартиран. Възможно е да има забавяне докато задачата спре.
##XMSG: Message start to indicate that task is not active
CANCEL_VIEW_ANALYZER_NO_ACTIVE_TASK=Задачата на анализатора на изгледи {0} за изглед „{1}“ не е активна.
#XMSG: Cancel View Analyzer task failure
CANCEL_VIEW_ANALYZER_FAILED=Неуспешна отмяна на задачата на анализатора на изгледи.
##XMSG: Message used in View Analyzer execution task when task is canceled
VIEW_ANALYZER_CANCELED=Изпълнението на анализатора на изгледи за изглед „{0}“ е спряно чрез задача за анулиране.
##XMSG: Message start of cancelation for Model Validation task
CANCEL_MODEL_VALIDATION_TRIGGERED=Процесът по спиране на задачата по валидиране на модела на изгледи {0} за изглед „{1}“ е подаден. Възможно е да има забавяне докато задачата спре.
##XMSG: Message to indicate that task is not active
CANCEL_MODEL_VALIDATION_NO_ACTIVE_TASK=Задачата по валидиране на модела {0} за изгледа „{1}“ не е активна.
#XMSG: Cancel Model Validation task failure
CANCEL_MODEL_VALIDATION_FAILED=Анулирането на задачата по валидиране на модела е неуспешно.
##XMSG: Message used in Model Validation task when task is canceled
MODEL_VALIDATION_CANCELED=Изпълнението валидиране на модела за изгледа „{0}“ е спряно чрез задача за анулиране.
#XMSG: Message for Locked space error, before starting Model Validation task
MODEL_VALIDATION_LOCKED_SPACE_ERROR=Невъзможно изпълнение на валидиране на модела за изгледа „{0}“, защото пространството „{1}“ е заключено.
##XMSG: Message used in View Analyzer execution task when determination of number of rows fails
NUMBER_ROWS_DETERMINATION_FAILED=Възникна грешка при определяне на броя редове за локалната таблица „{0}“.
##XMSG: Message to indicate that SQL plan was created
EXECUTION_PLAN_CREATED=Създаден е файл на план за SQL анализатора за изглед „{0}“ и може да се изтегли.
#XFLD: Message to indicate starting of process to generate SQL Analyzer plan file
START_PLANVIZ_EXECUTION=Стартиране на процеса за генериране на файл на план за SQL анализатора за изглед „{0}“.
#XFLD: Message to indicate starting of View Analyzer process
VIEW_ANALYZER_EXECUTION_STARTED=Стартиране на изпълнението на анализатора на изгледи:
##XMSG: Message to indicate that SQL plan file cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_PLANVIZ_NOT_FULFILLED=Не може да се генерира файл на план за SQL анализатора за изглед „{0}“, защото не са спазени предварителните изисквания за трайно съхранение на данни.
##XMSG: Error message during  generation of SQL Analyzer plan file
PLANVIZ_ERROR=Възникна грешка при генерирането на файл на план за SQL анализатора за изглед „{0}“.
#XMSG: Message for Locked space error
VIEW_ANALYZER_LOCKED_SPACE_ERROR=Невъзможно изпълнение на анализатор на изгледи за изглед „{0}“, защото пространство „{1}“ е заключено.
#XMSG: Message for persistency simulation without partitioning
PERSISTENCY_SIMULATION_WITHOUT_PARTITIONS=Дяловете на изгледа „{0}“ не се вземат под внимание при симулирането на трайното съхранение на данни.
#XMSG: Message for generation of SQL Analyzer plan file without considering partitioning
PLANVIZ_WITHOUT_PARTITIONS=Дяловете на изгледа „{0}“ не се вземат под внимание при генериране на файл на план за SQL анализатора.
#XMSG: Confirmation message for mass removal of persisted data
MassRemovePersistency_Confirm_Msg=Желаете ли да премахнете трайното съхранение на данни и да върнете виртуалния достъп до данните?
#XMSG: Confirmation message for partial mass removal of selected persisted data
PartialMassRemovePersistency_Confirm_Msg={0} от {1} избрани изгледи получават трайно съхранени данни. \n Желаете ли да премахнете трайното съхранение на данни и да върнете виртуалния достъп до данните?
#XMSG: success message for stopping mass persistence
stopMassPersistenceSuccess=Премахваме трайното съхранение на данни за избраните изгледи.
#XMSG: error message during mass stop persistence of a view (backend error)
STOP_MASS_PERSISTENCE_ERROR=Възникна грешка при прекратяването на запазването на избраните изгледи.
#XMSG: memory analysis is only performed in space {0}
NO_MEMORY_ANALYSIS=Изпълнен е анализ на паметта за единиците само в пространство „{0}“: „{1}“ „{2}“ са пропуснати.
##XMSG: Message to indicate that Explain Plan cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_EXPLAINPLAN_NOT_FULFILLED=Не може да се генерира файл с план за изпълнение на заявката за изглед „{0}“, защото не са спазени предварителните изисквания за трайно съхранение.
#XMSG: Message for Explain Plan generation without partitioning
EXPLAINPLAN_WITHOUT_PARTITIONS=Дяловете на изгледа „{0}“ не се вземат под внимание при генериране на файл с план за изпълнение на заявка.
#XFLD: Message to indicate starting of process to generate Explain Plan file
START_EXPLAINPLAN_EXECUTION=Стартиране на процеса за генериране на файл с план за изпълнение на заявка за изгледа „{0}“.
##XMSG: Message to indicate that Explain Plan was created
EXPLAINPLAN_CREATED=Файлът с план за изпълнение на заявката за изгледа „{0}“ е генериран. Може да го видите от „Преглед на подробни данни“ или да го изтеглите, ако имате нужните права.
##XMSG: Error message during  generation of Explain Plan file
EXPLAINPLAN_ERROR=Възникна грешка при генерирането на файла с плана за изпълнение на заявката за изгледа „{0}”.
##XMSG: Explain Plan file cannot be generated for the view {0}, because too many views are stacked on each other
EXPLAINPLAN_MODEL_TOO_COMPLEX=Не може да бъде генериран план за изпълнение на заявката за изгледа „{0}“, защото има твърде много изгледи един върху друг. Сложните модели могат да доведат до грешки, свързани с липса на памет, както и с бавна производителност. Препоръчваме да съхраните изглед трайно.

##XMSG: Message to indicate that the space is locked
PERFORMANCE_ANALYSIS_LOCKED_SPACE_ERROR=Невъзможно изпълнение на анализ на производителността за изгледа „{0}“, защото пространството „{1}“ е заключено.
##XMSG: The performance analysis task was cancelled
PERFORMANCE_ANALYSIS_CANCELED=Анализът на производителността за изгледа „{0}“ е отказан.
##XMSG: The performance analysis failed
PERFORMANCE_ANALYSIS_FAILED=Неуспешен анализ на производителността.
##XMSG: The performance analysis is finished
PERFORMANCE_ANALYSIS_FINISHED=Анализът на производителността за изгледа „{0}“ завърши. Кликнете върху „Преглед на данните“, за да видите резултата.
##XMSG: The view has input parameter with no default value
INPUT_PARAMETER_WITHOUT_DEFAULT_VALUE=Този изглед не може да бъде анализиран, тъй като съдържа параметър без стойност по подразбиране.
##XMSG: The view is not fully deployed
VIEW_NOT_FULLY_DEPLOYED=Този изглед не може да бъде анализиран, защото не е разгърнат изцяло.
##XMSG: The view uses limited remote adapters
LIMITED_REMOTE_ADAPTER=Този изглед използва поне един отдалечен адаптер с ограничени възможности, например липсващо притискане на филтри или поддръжка на „Брой“. Трайното съхранение или репликацията на обектите може да подобри времето на изпълнение.
##XMSG: The view uses remote adapter that does not support 'Limit'
REMOTE_ADAPTER_NOT_SUPPORTING_LIMIT=Този изглед използва поне един отдалечен адаптер, който не поддържа „Лимит“. Възможно е да са избрани повече от 1000 записа.
##XMSG: Performance analysis is executed by using default parameters of view'
INPUT_PARAMETER_WITH_DEFAULT_VALUE=Анализът на производителността се изпълнява посредством стойностите по подразбиране за параметрите на изглед.
##XMSG: Message to indicate that an error occurred
PERFORMANCE_ANALYSIS_ERROR=Възникна грешка по време на анализа на производителността за изгледа „{0}“.
##XMSG: Message start of cancellation for Performance Analysis task
CANCEL_PERFORMANCE_ANALYSIS_TRIGGERED=Процесът по спиране на задачата за анализ на производителността {0} за изгледа „{1}“ е стартиран. Възможно е да има забавяне, докато задачата спре.
##XMSG: Message start to indicate that task is not active
CANCEL_PERFORMANCE_ANALYSIS_NO_ACTIVE_TASK=Задачата за анализ на производителността {0} за изгледа „{1}“ не е активна.
#XMSG: Cancel Performance Analysis task failure
CANCEL_PERFORMANCE_ANALYSIS_FAILED=Неуспешен отказ на задачата за анализ на производителността.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Присъединяване на график към мен
#XBUT: Pause schedule menu label
pauseScheduleLabel=Поставяне на график на пауза
#XBUT: Resume schedule menu label
resumeScheduleLabel=График за възобновяване
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Възникна грешка при премахването на графиците.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Възникна грешка при присъединяването на графиците.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Възникна грешка при поставянето на пауза на графиците.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Възникна грешка при възобновяването на графиците.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Изтриване на графици: {0}
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Промяна на собственика на графици: {0}
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Графици за поставяне на пауза: {0}
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Възобновяване на графици: {0}
#XBUT: Select Columns Button
selectColumnsBtn=Избор на колони
#XFLD: Refresh tooltip
TEXT_REFRESH=Опресняване
#XFLD: Select Columns tooltip
text_selectColumns=Избор на колони


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Runtime Metrics ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Label for Runtime Metrics
RuntimeMetricsTitle=Метрики на време на изпълнение за
#XFLD : Label for Run Button
runButton=Изпълнение
#XFLD : Label for Cancel Button
cancelButton=Отмяна
#XFLD : Label for Close Button
closeButton=Затваряне
#XFLD : Label for Open View Analyzer Button
openViewAnalyzerButton=Отваряне на анализатора на изгледи
#XFLD : Label for Generate Explain Plan Button
generateExplainPlanButton=Генериране на план за изпълнение на заявката
#XFLD : Label for Previous Run Column
previousRun=Предишно изпълнение
#XFLD : Label for Latest Run Column
latestRun=Последно изпълнение
#XFLD : Label for time Column
time=Час
#XFLD : Label for Duration Column
duration=Продължителност
#XFLD : Label for Peak Memory Column
peakMemory=Пиково използване на паметта (MiB)
#XFLD : Label for Number of Rows
numberOfRows=Брой редове
#XFLD : Label for Overall Number of Sources
overallNumberOfSources=Общ брой източници
#XFLD : Label for Data Access Column
dataAccess=Достъп до данните
#XFLD : Label for Local Tables
localTables=Локални таблици
#XFLD : Label for federatedRemoteTables
federatedRemoteTables=Обединени отдалечени таблици (с ограничени възможности на адаптер)
#XTXT Text for initial state of the runtime metrics
initialState=Първо трябва да изпълните анализ на производителността, за да получите метриките. Това може да отнеме известно време, но може да откажете процеса, ако е необходимо.
#XMSG: Cancel confirmation message
CancelRunConfirmationMessage=Сигурни ли сте, че желаете да откажете текущото изпълнение на анализа на производителността?
#XTIT: Cancel dialog title
CancelRunTitle=Отмяна на изпълнение
#XFLD: Label for Number of Rows
NUMBER_ROWS=Брой редове
#XFLD: Label for Overall Number of Sources
OVERALL_NUMBER_SOURCES=Общ брой източници
#XFLD: Label for Data Access
DATA_ACCESS=Достъп до данните
#XFLD: Label for Remote tables
NUMBER_REMOTE_TABLES_LIMITED_ADAPTERS=Обединени отдалечени таблици (с ограничени възможности на адаптер)
#XFLD: Label for select statement
SELECT_STATEMENT='SELECT * FROM VIEW LIMIT 1000'
#XFLD: Label for duration
SELECT_RUNTIME=Продължителност
#XFLD: Label for peak memory
SELECT_PEAK_MEMORY=Пиково използване на паметта (MiB)
#XFLD: Label for count statement
SELECT_COUNT_STATEMENT=„ИЗБЕРЕТЕ БРОЙ(*) ОТ ИЗГЛЕДА“
#XFLD: Label for count duration
SELECT_COUNT_RUNTIME=Продължителност
#XFLD: Label for count peak memory
SELECT_COUNT_PEAK_MEMORY=Пиково използване на паметта (MiB)
#XFLD: Label for count of number of local tables
NUMBER_LOCAL_TABLES_FILE=Локални таблици (файл)
#XTXT: Text for running state of the runtime metrics
Running=Изпълнява се...
#XFLD: Label for time
Time=Час
#XFLD: Label for virtual access
PA_VIRTUAL=Виртуално
#XFLD: Label for persisted access
PA_PERSISTED=Трайно съхранено
PA_PARTIALLY_PERSISTED=Частично съхранено
#XTXT: Text for cancel
CancelRunSuccessMessage=Изпълнението на анализа на производителността се прекратява.
#XTXT: Text for cancel error
CancelRunErrorMessage=Възникна грешка при прекратяването на анализа на производителността.
#XTXT: Text for explain plan generation
ExplainPlanStarted=Генерира се план за изпълнение на заявка за изгледа „{0}“.
#XTXT: Text for performance analysis
PerformanceAnalysisStarted=Стартира се анализ на производителността за изгледа „{0}“.
#XTXT: Text for performance analysis error
ErrorFetchingPerformanceAnalysis=Възникна грешка при извличането на данни за анализа на производителността.
#XTXT: Text for performance analysis error
conflictingTask=Задачата за анализ на производителността вече се изпълянва
#XFLD: Label for Errors
Errors=Грешки
#XFLD: Label for Warnings
Warnings=Предупреждения
#XFLD: Tooltip for missing privileges
openViewAnalyzerButtonTooltipNoPrivilege=Необходима ви е привилегията DWC_DATAINTEGRATION(актуализация), за да отворите анализатора на изгледи.
#XFLD: Tooltip for missing privileges
generateExplainPlanButtonTooltipNoPrivilege=Необходима ви е привилегията DWC_RUNTIME(четене), за да генерирате плана за изпълнение на заявката.



#XFLD: Label for frequency column
everyLabel=на всеки
#XFLD: Plural Recurrence text for Hour
hoursLabel=часа
#XFLD: Plural Recurrence text for Day
daysLabel=дни
#XFLD: Plural Recurrence text for Month
monthsLabel=месеца
#XFLD: Plural Recurrence text for Minutes
minutesLabel=минути
