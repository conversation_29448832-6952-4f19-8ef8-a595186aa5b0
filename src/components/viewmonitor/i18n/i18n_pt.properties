
#XBCB: Breadcrumb
viewMonitorBreadcrumb={spaceId}

#XFLD: Label for persisted view column
SOURCE=Fonte
#XFLD: Label for persisted view column
NAME=Nome
#XFLD: Label for persisted view column
NAME_LABEL=Nome comercial
#XFLD: Label for persisted view column
NAME_LABELNew=Objeto (nome comercial)
#XFLD: Label for persisted view column
TECHINCAL_NAME=Nome técnico
#XFLD: Label for persisted view column
TECHINCAL_NAMENew=Objeto (nome técnico)
#XFLD: Label for persisted view column
DATA_PERSISTENCY=Acesso aos dados
#XFLD: Label for persisted view column
STATUS=Status
#XFLD: Label for persisted view column
LAST_UPDATED=Última atualização
#XFLD: Label for persisted view column
IN_MEMORY_SIZE=Memória usada para armazenamento (MiB)
#XFLD: Label for persisted view column
DISK_SIZE=Disco usado para armazenamento (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_NEWW=Tamanho in-memory (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_HEADER=Tamanho in-memory
#XFLD: Label for persisted view column
DISK_SIZE_NEWW=Tamanho no disco (MiB)
#XFLD: Label for persisted view column
DISK_SIZE_HEADER=Tamanho no disco
#XFLD: Label for schedule owner column
txtScheduleOwner=Proprietário da programação
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Mostra quem criou a programação
#XFLD: Label for persisted view column
PERSISTED=Modo persistente
#XFLD: Label for persisted view column
TYPE=Tipo
#XFLD: Label for View Selection Dialog column
changedOn=Alterado em
#XFLD: Label for View Selection Dialog column
createdBy=Criado por
#XFLD: Label for log details column
txtViewPersistencyLogs=Visualizar logs
#XFLD: Label for log details column
txtViewPersistencyLogsNew=Detalhes
#XFLD: text for values shown for Ascending sort order
SortInAsc=Organizar em ordem crescente
#XFLD: text for values shown for Descending sort order
SortInDesc=Organizar em ordem decrescente
#XFLD: Label for persisted view monitor heading
ViewMonitor_Heading=Monitor de visões
#XFLD: Label for persisted view monitor sub-heading
ViewMonitor_SubHeading=Monitore e atualize persistência de dados das visões


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ DynamicPageHeader ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: text for values shown in column Persistence Status
txtLoading=Carregando
#XFLD: text for values shown in column Persistence Status
txtRunning=Em execução
#XFLD: text for values shown in column Persistence Status
txtAvailable=Disponível
#XFLD: text for values shown in column Persistence Status
txtError=Erro
#XMSG: Error message for unknown Persistence type that gets logged in Kibana
unknownReplicationTypeError=Não há suporte ao tipo de replicação "{0}".

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Content ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Configurações usadas para última execução de persistência de dados:
#XMSG: Message for input parameter name
inputParameterLabel=Parâmetro de entrada
#XMSG: Message for input parameter value
inputParameterValueLabel=Valor
#XMSG: Message for persisted data
inputParameterPersistedLabel=Persistido às
#XTIT: Count for View in ViewMonitor Toolbar
txtViewCount=Visões ({0})
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Persistência de visão
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Persistência de dados
#XBUT: Button to clear the selected view persistance
ClearViewPersistance=Limpar
#XBUT: Button to stop the selected view persistance
stopPersistance=Interromper persistência
#XFLD: Placeholder for Search field
txtSearch=Procurar
#XBUT: Tooltip for refresh button
txtRefresh=Atualizar
#XBUT: Tooltip for add view button
txtDeleteView=Excluir persistência
#XBUT: Tooltip for load new peristence
loadNewPersistence=Reiniciar persistência
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Carregar novo instantâneo
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Iniciar persistência de dados
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Remover dados persistidos
#XMSG: success message for starting persistence
startPersistenceSuccess=Estamos persistindo a visão "{0}".
#XMSG: success message for stopping persistence
stopPersistenceSuccess=Estamos removendo os dados persistidos da visão "{0}".
#XMSG: success message for stopping persistence
deletePersistedViewSuccess=Estamos removendo a visão "{0}" da lista de monitoramento.
#XMSG: error message during start persistence of a view (backend error)
ERROR_STARTING_PERSISTENCE=Erro ao iniciar a persistência de dados da visão "{0}".
#XMSG: error message during start persistence of a view (backend error)
PARAMERROR_STARTING_PERSISTENCE=Não é possível persistir a visão "{0}", ela contém parâmetros de entrada.
#XMSG: error message during start persistence of a view (backend error)
NUMBER_OF_PARAMETER_STARTING_PERSISTENCE=Não é possível persistir a visão "{0}", ela tem mais de um parâmetro de entrada.
#XMSG: error message during start persistence of a view (backend error)
PARAMETER_DEFAULT_VALUE_STARTING_PERSISTENCE=Não é possível persistir a visão "{0}", o parâmetro de entrada não tem valor padrão.
#XMSG: error message during start persistence of a view (backend error)
REDEPLOYMENT_DAC_REQUIRED=Você precisa reimplementar o controle de acesso aos dados (DAC) "{0}" para dar suporte à persistência de dados.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_STARTING_PERSISTENCE=Não é possível persistir a visão "{0}", ela usa a visão "{1}" que tem controle de acesso aos dados (DAC).
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_EXTERNAL_STARTING_PERSISTENCE=Não é possível persistir a visão "{0}" porque ela usa uma visão com controle de acesso aos dados (DAC) que pertence a uma área diferente.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_STRUCTURE_STARTING_PERSISTENCE=Não é possível persistir a visão "{0}", a estrutura de um ou mais controles de acesso aos dados (DAC) não suporta persistência de dados.
#XMSG: error message during stop persistence of a view (backend error)
STOP_PERSISTENCE_ERROR=Erro ao interromper a persistência da visão "{0}".
#XMSG: error message during stop persistence of a view (backend error)
DELETE_PERSISTED_VIEW_ERROR=Erro ao excluir a visão em modo persistente "{0}".
#XMSG: Warning message for removing the persisted data
RemovePersistency_Confirm_Msg=Deseja excluir os dados persistidos e trocar para o acesso virtual da visão "{0}"?
#XMSG: Warning message for delete the view persistence
DeletePersistency_Confirm_Msg=Deseja remover a visão da lista de monitoramento e excluir os dados persistidos da visão "{0}"?
#XMSG: error message for reading data from backend
txtReadBackendError=Parece que houve um erro na leitura do back-end.
#XFLD: Label for No Data Error
NoDataError=Erro
#XMSG: message for conflicting task
Task_Already_Running=Uma tarefa em conflito já está em execução para a visão "{0}".

#XFLD: Error while accessing pertitioning for a view
missingPrivilegeInViewPersistency=Permissão insuficiente para executar o particionamento da visão "{0}"

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Show All Views~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Text for show all views button
allViewsText=Todas as visões ({0})
#XBUT: Text for show scheduled views button
scheduledText=Programadas ({0})
#XBUT: Text for show persisted views button
persistedText=Persistidas ({0})
#XBUT: Text for start analyzer button
startAnalyzer=Iniciar Analisador de visões
#XFLD: Message if repository is unavailable
repositoryErrorMsg=O repositório não está disponível e alguns recursos estão desativados.

#XFLD: Data Access - Virtual
Virtual=Virtual
#XFLD: Data Access - Persisted
Persisted=Persistido

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Non-Persisted View Selection Dialog~~~~~~~~~~~~~~~~~~~~~~~

#XTIT: Title for Non-Persisted View selection dialog
select_source=Selecionar visão para modo persistente

#XTIT: Placeholder for Non-Persisted View selection dialog search
searchView=Procurar visões
#XTIT: No data in the list of non-persisted view
No_Data=Sem dados
#XBUT: Button to select non-persisted view
ok=OK
#XBUT: Button to close the non-persisted views selection dialog
cancel=Cancelar

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~View persistency log message key~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message for view persistency logs
START_STOP_PERSISTENCY=Iniciando execução da tarefa de persistência de dados para "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY=Realizando a persistência dos dados da visão ''{1}''.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY)
START_VIEW_PERSISTENCY_PROCESS=Iniciando processo para persistir dados da visão "{0}".
#XFLD: Message for view persistency manual partition load
START_VIEW_PERSISTENCY_PROCESS_MANUAL_PARTITIONS=Iniciando processo para persistir dados da visão "{0}" com IDs de partição selecionados: "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY=Removendo dados persistidos da visão "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY)
REMOVE_VIEW_PERSISTENCY=Iniciando processo para remover dados persistidos da visão "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_SUCCESS=Persistência de dados da visão "{1}" realizada.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_SUCCESS)
START_VIEW_PERSISTENCY_PROCESS_SUCCESS=Persistência de dados da visão "{0}" realizada.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY_SUCCESS=Dados persistidos removidos e acesso aos dados virtuais restaurado para a visão "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_SUCCESS)
REMOVE_VIEW_PERSISTENCY_SUCCESS=Processo para remover dados persistidos da visão "{0}" concluído.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_FAILED=Não é possível persistir os dados da visão "{1}".
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_FAILED)
START_VIEW_PERSISTENCY_PROCESS_FAILED=Não é possível persistir os dados da visão "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.05)
STOP_VIEW_PERSISTENCY_FAILED=Não é possível remover dados persistidos da visão "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_FAILED)
REMOVE_VIEW_PERSISTENCY_FAILED=Não é possível remover dados persistidos da visão "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_SUCCESS="{3}" registros persistidos para a visão "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace START_PERSISTENCY_RECORD_COUNT_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS={0} registros inseridos na tabela de persistência de dados da visão "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS_V2={0} registros inseridos na tabela de persistência de dados da visão "{1}". Memória usada: {2} GiB.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_SUCCESS="{3}" registros persistidos removidos para a visão "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace STOP_PERSISTENCY_RECORD_COUNT_SUCCESS)
REMOVE_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=Dados persistidos removidos, "{0}" registros persistidos excluídos.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_FAILED=Falha ao obter o recordCount para a visão "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_FAILED=Falha ao obter o recordCount para a visão "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Programação excluída para "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS)
REMOVE_VIEW_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Programação excluída para a visão "{0}".
#XFLD: Message for view persistency logs
STOP_PERSISTENCY_DELETE_SCHEDULE_FAILED=Falha na exclusão da programação para "{1}".
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_REDEPLOY=A visão "{0}" não pode ser persistida porque ela foi alterada e implementada depois de você ter começado a persisti-la. Tente persisti-la novamente ou aguarde até a execução seguinte programada.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_DELETE=A visão "{0}" não pode ser persistida, ela foi excluída depois de você ter começado a persisti-la.
#XFLD: Message for successful data persistency of a single partition (for messages created before version 2022.03)
START_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} registros persistidos na partição para valores "{1}" <= {2} < "{3}".
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (new message to replace START_PERSISTENCY_SINGLE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} registros inseridos na partição para valores "{1}" <= {2} < "{3}".
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS_V2={0} registros inseridos na partição para valores "{1}" <= "{2}" < "{3}". Memória usada: {4} GiB.
#XFLD: Message for successful replication of a "others" partition (for messages created before version 2022.03)
START_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} registros persistidos na partição "outros".
#XFLD: Message used by SEAL repository for successful replication of a "others" partition (new message to replace START_PERSISTENCY_OTHERS_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} registros inseridos na partição "outros".
#XFLD: Message for view persistency record count of partitions (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={3} registros persistidos para a visão "{1}" em {4} partições.
#XFLD: Message used by SEAL repository for view persistency record count of partitions (new message to replace START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={0} registros inseridos na tabela de persistência de dados para a visão "{1}" em {2} partições.
#XFLD: Message used by SEAL repository for view persistency for partition wise refresh
START_VIEW_PERSISTENCY_RECORD_COUNT_REFRESHED_PARTITIONS_SUCCESS={0} registros inseridos na tabela de persistência de dados para a visão "{1}". Partições atualizadas: {2}; Partições bloqueadas: {3}; Total de partições: {4}
#XFLD: Message used by SEAL repository for view persistency for manual partition load
START_VIEW_PERSISTENCY_RECORD_COUNT_MANUAL_PARTITIONS_SUCCESS={0} registros inseridos na tabela de persistência de dados da visão "{1}" nas {2} partições selecionadas
#XFLD: Message used by SEAL repository for view persistency for locked partition data load
START_VIEW_PERSISTENCY_RECORD_COUNT_LOCKED_PARTITIONS_SUCCESS={0} registros inseridos na tabela de persistência de dados para a visão "{1}". Partições atualizadas: {2}; Partições bloqueadas não alteradas: {3}; Total de partições: {4}
#XMSG: Message for unexpected error (for messages created before version 2022.03)
START_PERSISTENCY_UNEXPECTED_ERROR=Ocorreu um erro inesperado ao persistir dados para a visão "{0}".
#XMSG: Message for unexpected error (new message to replace START_PERSISTENCY_UNEXPECTED_ERROR)
START_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Ocorreu um erro inesperado ao persistir dados para a visão "{0}".
#XMSG: Message for Locked space error
START_VIEW_PERSISTENCY_LOCKED_SPACE_ERROR=A visão "{0}" não pode ser persistida porque a área "{1}" está bloqueada.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
STOP_PERSISTENCY_UNEXPECTED_ERROR=Ocorreu um erro inesperado ao remover dados persistidos para a visão "{0}".
#XMSG: Message for unexpected error (new message to replace STOP_PERSISTENCY_UNEXPECTED_ERROR)
REMOVE_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Ocorreu um erro inesperado ao remover a persistência da visão "{0}".
#XMSG: Message for ivalidated view
VIEW_PERSISTENCY_ABORTED_DEFINITION_INVALID=A definição da visão "{0}" tornou-se inválida, provavelmente devido a uma alteração de um objeto consumido direta ou indiretamente pela visão. Tente reimplementar a visão para resolver o problema ou identificar a causa raiz.
#XMSG: Message for persisted data removal (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT=Os dados persistidos são removidos durante a implementação da visão "{0}".
#XMSG: Message for persisted data removal of a consuming view
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW=Os dados persistidos são removidos durante a implementação da visão consumida "{0}".
#XMSG: Message for persisted data removal of a consuming view due to a dac change
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW_DAC_CHANGED=Os dados persistidos foram removidos durante a implementação da visão consumida "{0}" porque o controle de acesso aos dados foi alterado.
#XMSG: Message for persisted data removal (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DEPLOYMENT=Os dados persistidos são removidos durante a implementação da visão "{0}".
#XMSG: Message for deleted view (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION=A persistência é removida com a exclusão da visão "{0}".
#XMSG: Message for deleted view (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DELETION=A persistência é removida com a exclusão da visão "{0}".
#XMSG Message for removal of persisted data, because prerequisites not fulfilled
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_PREREQUISITES_NOT_FULFILLED=Os dados persistidos serão removidos porque os pré-requisitos para persistência de dados não estão sendo mais atendidos.
#XMSG: Message for inconsistent persistency
VIEW_PERSISTENCY_INCONSISTENT_PERSISTENCY=A persistência da visão "{0}" se tornou inconsistente. Remova os dados persistidos para solucionar o problema.
#XMSG: Message for prerequisites
START_PERSISTENCY_CHECK_PREREQUISITES=Verificando os pré-requisitos para persistência da visão "{0}".
#XMSG: Message for DAC deployed in deprecated way using table function and join (for messages created before 2025.05)
START_PERSISTENCY_DEPRECIATED_DAC=A visão "{0}" foi implementada usando o controle de acesso aos dados (DAC) que está sendo descontinuado. Implemente a visão novamente para melhorar o desempenho.
#XMSG: Message for DAC deployed in deprecated way using table function and join
START_PERSISTENCY_DEPRECATED_DAC=A visão "{0}" foi implementada usando o controle de acesso aos dados (DAC) que está sendo descontinuado. Implemente a visão novamente para melhorar o desempenho.
#XMSG: Message for roll back (for messages created before version 2022.03)
ROLLBACK_VIEW_PERSISTENCY=Ocorreu um erro. Estado anterior da persistência restaurado para a visão "{0}".
#XMSG: Message used by SEAL repository for rollback of running procedure to persist view (new message to replace ROLLBACK_VIEW_PERSISTENCY)
VIEW_PERSISTENCY_ROLLBACK=Ocorreu um erro. O processo de persistência da visão ''{0}'' foi interrompido e as alterações foram revertidas.
#XMSG: Message used by SEAL repository for rollback of running procedure to remove persisted data of view
REMOVE_VIEW_PERSISTENCY_ROLLBACK=Ocorreu um erro. O processo de remoção de dados persistidos da visão ''{0}'' foi interrompido e as alterações foram revertidas.
#XMSG: Message used by SEAL repository for persistency preparation
START_PERSISTENCY_PREPARATION=Preparando para persistir dados.
#XMSG: Message used by SEAL repository for data insertion
START_INSERTING_PERSISTENCY_DATA=Inserindo dados na tabela de persistência.
#XMSG: Message for null values - partitioning (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS={0} registros de valor nulo inseridos na partição "outros".
#XMSG: Message used by SEAL repository for others partition values - partitioning (new message to replace START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS={0} registros inseridos na partição "outros" para valores "{2}" < "{1}" OR "{2}" >= "{3}".
#XMSG: Message used by SEAL repository for others partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS_V2={0} registros inseridos na partição "outros" para valores "{2}" < "{1}" OR "{2}" >= "{3}". Memória usada: {4} GiB.
#XMSG: Message used by SEAL repository for null partition values - partitioning
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS={0} registros inseridos na partição "outros" para valores "{1}" IS NULL.
#XMSG: Message used by SEAL repository for null partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS_V2={0} registros inseridos na partição "outros" para valores "{1}" IS NULL. Memória usada: {2} GiB.
##XFLD:Message used by SEAL repository for remote statistics
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS=Carregando os dados envolvidos: {0} instruções remotas. Total de registros carregados: {1}. Tempo total de duração: {2} segundos.
##XFLD:Message used by SEAL repository for remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS_WITH_PARTITIONS=Carregando os dados envolvidos usando {0} partições com {1} instruções remotas. Total de registros carregados: {2}. Tempo total de duração: {3} segundos.
##XFLD:Message used by SEAL repository with UI ID for instructions of remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_INSTRUCTION=Para exibir as instruções remotas processadas durante a execução, abra o monitor de consulta remota nos detalhes das mensagens específicas da partição.
##XFLD:Message for start reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS=Iniciando processo para reutilizar dados persistidos existentes da visão {0} após implementação.
##XFLD:Message used by SEAL repository for reattach view persitency
START_VIEW_PERSISTENCY_REATTACHMENT=Comece a reutilizar os dados persistidos existentes.
##XFLD:Message used by SEAL repository for reattach view persitency
START_FINILIZING_VIEW_PERSISTENCY_REATTACHMENT=Reutilizando os dados persistidos existentes.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_COMPLETE=O processo de reutilização de dados persistidos existentes foi concluído para a visão {0}.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_FAILED=Falha ao reutilizar os dados persistidos existentes para a visão {0} após implementação.
#XMSG: Message used by SEAL repository for finalizing persistency
START_FINALIZING_PERSISTENCY=Finalizando persistência.
#XMSG: Message used by SEAL repository for restoring virtual access for view
REMOVE_VIEW_PERSISTENCY_VIRTUAL_ACCESS_RESTORED=Acesso a dados virtuais restaurado para a visão "{0}".
#XMSG: Message used by SEAL repository for view that already has virtual access
REMOVE_VIEW_PERSISTENCY_ALREADY_VIRTUAL_ACCESS=A visão "{0}" já tem acesso aos dados virtual. Nenhum dado persistido foi removido.
#XMSG: Message for removing view persistency
VIEW_PERSISTENCY_REMOVED_FROM_MONITOR=A visão "{0}" foi removida do Monitor de visões.
#XMSG: Message for view doesnt exist.
VIEW_PERSISTENCY_VIEW_NOT_EXISTING=A visão "{0}" não existe no banco de dados ou não está implementada corretamente, e isso significa que não pode ser persistida. Tente reimplementar a visão para resolver o problema ou identifique a causa raiz.
#XMSG: Message for persistency not enabled.
VIEW_PERSISTENCY_NOT_ENABLED=A persistência de dados não está ativada. Reimplemente uma tabela/visão na área "{0}" para ativar a funcionalidade.
#XMSG: Message for unexpected error due to node crash
VIEW_PERSISTENCY_UNEXPECTED_ERROR_GENERAL=A última execução de persistência da visão foi interrompida devido a erros técnicos.
#XFLD: Message for view persistency memory consumption
VIEW_PERSISTENCY_MEMORY_CONSUMPTION={0} GiB de memória máxima usados no tempo de execução de persistência da visão.
#XFLD: Message for view persistency timeout
VIEW_PERSISTENCY_TIMEOUT=A persistência da visão {0} atingiu o tempo-limite de {1} horas.
#XFLD: Message for not started asynchronous view persistency
VIEW_PERSISTENCY_ASYNC_NOT_STARTED=Uma alta carga do sistema impediu o início da execução assíncrona da persistência da visão. Verifique se muitas tarefas estão sendo executadas simultaneamente.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency (for messages created before version 2022.05)
START_VIEW_PERSISTENCY_EXECUTION_METHOD_DROP_AND_RENAME=A tabela persistida existente foi excluída e substituída por uma nova tabela persistida.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_RECREATE_AND_DROP=A tabela persistida existente foi excluída e substituída por uma nova tabela persistida.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_TRUNCATE_AND_COPY=A tabela persistida existente foi atualizada com novos dados.
##XFLD: Message for missing authorizations for view persistency
VIEW_PERSISTENCY_MISSING_AUTH=Sem autorizações para persistência de dados.
##XFLD: Message start of persistency cancellation
VIEW_PERSISTENCY_TRIGGER_CANCEL=Iniciando cancelamento do processo de persistência da visão {0}.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_UNAVAILABLE_TASK_RUN=Falha ao cancelar o processo de persistência da visão; não há nenhuma tarefa de persistência de dados em execução para a visão {0}.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_NONDETERMINISTIC_TASK_RUN=Falha ao cancelar o processo de persistência da visão; nenhuma tarefa de persistência de dados está em execução para a visão {0}.
##XFLD: Message for canceling a non running persitency task
VIEW_PERSISTENCY_CANCEL_NON_RUNNING_SELECTED_TASK=Falha ao cancelar o processo de persistência da visão {0}, a tarefa de persistência de dados selecionada {1} não está em execução.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_UNREACHABLE_PERSISTENCY_RUN=Falha ao cancelar o processo de persistência da visão; a persistência de dados para a visão {0} ainda não foi iniciada.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_COMPLETED_PERSISTENCY_RUN=Falha ao cancelar o processo de persistência da visão {0}; o processo já foi concluído.
##XFLD: Message for failed cancellation
VIEW_PERSISTENCY_ABORTED_CANCEL_EXECUTION=Falha ao cancelar o processo de persistência da visão {0}.
##XFLD: Message for finalizing persistency cancellation
VIEW_PERSISTENCY_FINALIZE_CANCEL=O processo para interromper a persistência de dados da visão {0} foi enviado.
##XFLD: Message used in persist task when cancellation is caught
VIEW_PERSISTENCY_CANCELED=Processo de persistência da visão {0} interrompido.
##XFLD: Message used in persist task when cancelation is caught and performed through a cancelation task
VIEW_PERSISTENCY_CANCELED_VIA_CANCEL_TASK=Processo de persistência da visão {0} interrompido por meio de uma tarefa de cancelamento {1}.
##XFLD: Message used in cancel task when the task is triggered by deployment
VIEW_PERSISTENCY_CANCEL_TRIGGERED_BY_DEPLOYMENT=Cancelando o processo de persistência de dados durante a implementação da visão {0}.
##XFLD: Message used in case the cancelation is retriggered for a running task
VIEW_PERSISTENCY_CANCEL_ATTEMPT_RETRIGGER_CANCEL=Uma tarefa de cancelamento anterior para persistência da visão {0} já foi enviada.
##XFLD: Message used to express the delay that may occur before the persistency task is stopped
VIEW_PERSISTENCY_CANCEL_DELAY_STOPPING_PERSISTENCY=Pode ocorrer um atraso até que a tarefa de persistência de dados para a visão {0} seja interrompida.
##XFLD: Message used to reflect the task log identifier of the current running persistency task
VIEW_PERSISTENCY_CANCEL_CURRENT_RUNNING_TASK=Os dados para a visão {0} estão sendo persistidos com a tarefa {1}.
##XFLD:Message used by SEAL repository for locked partitions and DACs
VIEW_PERSISTENCY_LOCKED_PARTITIONS_DACS=As autorizações fornecidas pelos DACs podem ter sido alteradas e não são consideradas pelas partições bloqueadas. Desbloqueie as partições e carregue um novo instantâneo para aplicar as alterações.
##XFLD:Message used by SEAL repository for column structure change
VIEW_PERSISTENCY_COLUMN_STRUCTURE_CHANGE=A estrutura da coluna foi alterada e não corresponde mais à tabela de persistência existente. Remova os dados persistidos e inicie uma nova persistência para obter a tabela de persistência atualizada.
##XFLD: Message for out of memory
VIEW_PERSISTENCY_HANA_OUT_OF_MEMORY=Falha na tarefa, erro de falta de memória no banco de dados SAP HANA.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_EXCEPTION_TRANSACTION_ROLLBACK=Falha na tarefa, exceção interna no banco de dados SAP HANA.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_SQL_EXECUTION_ISSUE=Falha na tarefa, problema de execução interna de SQL no banco de dados SAP HANA.
##XFLD: Message for HANA out of memory result
VIEW_PERSISTENCY_OUT_OF_MEMORY_RESULT_MESSAGE=Motivo do evento de falta de memória do HANA: {0}
##XFLD: Message for admission control rejection
VIEW_PERSISTENCY_HANA_ADMISSION_CTRL_REJECTION=Falha na tarefa, rejeição do controle de admissão do SAP HANA.
##XFLD: Message for HANA back pressure / rate limit of HANA client
VIEW_PERSISTENCY_HANA_CLIENT_RATE_LIMIT_REJECTION=Falha na tarefa, há muitas conexões do SAP HANA ativas.
#XMSG: Message for invalid persisted table name
VIEW_PERSISTENCY_PERSISTED_TABLE_INVALID=Ocorreu um erro e a tabela persistida se tornou inválida. Para resolver o problema, remova os dados persistidos e persista a visão novamente.
#XMSG: Message for usahe of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION=Não é possível persistir a visão. Ela usa uma tabela remota baseada em uma fonte remota com propagação de usuários ativada. Verifique a linhagem da visão.
#XMSG: Message for dynamic usage of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION_DYNAMIC=Não é possível persistir a visão. Ela usa uma tabela remota baseada em uma fonte remota com propagação de usuários ativada. A tabela remota pode ser consumida dinamicamente por meio de uma visão de script SQL. A linhagem da visão pode não mostrar a tabela remota.
#XMSG: Message for insufficient privileges or consumption of view with DAC via dynamic SQL
VIEW_PERSISTENCY_INSUF_PRIV_OR_DAC_DYN_SQL=Seus privilégios podem ser insuficientes. Abra a Visualização de dados para ver se você tem os privilégios necessários. Caso positivo, uma segunda visão consumida via script SQL dinâmico pode ter o controle de acesso aos dados (DAC) aplicado a ela.
#XMSG: deployment of DAC is deprecated and view need to be redeployed to be able to persist data
VIEW_PERSISTENCY_DEPRECATED_DAC=A visão "{0}" foi implementada usando o controle de acesso aos dados (DAC) que está sendo descontinuado. Implemente a visão novamente para que seja possível persistir os dados dessa visão.
##XFLD:Message used by SEAL repository for input parameter
START_VIEW_PERSISTENCY_INPUT_PARAMETER=Usando valor padrão "{0}" para parâmetro de entrada "{1}".
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Elastic compute node keys~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
##XFLD:Message used to reflect disablement of ecn replica
ECN_DISABLE_EXISTING_REPLICA=A réplica do nó de computação elástica está desativada.
##XFLD:Message used to reflect recreation of ecn replica
ECN_RECREATE_EXISTING_REPLICA=A réplica do nó de computação elástica foi recriada.
##XFLD:Message used to reflect re-enablement of ecn replica
ECN_ENABLE_EXISTING_REPLICA=A réplica do nó de computação elástica foi reativada.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Task Schedule Menu key~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message to schedule task
scheduleText=Programar
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Criar programação
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Editar programação
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Excluir programação
#XFLD: Refresh frequency field
refreshFrequency=Frequência de atualização
#XFLD: Refresh frequency field
refreshFrequencyNew=Frequência
#XFLD: Refresh frequency field
refreshFrequencyNewNew=Frequência agendada
#XBUT: label for None
none=Nenhuma
#XBUT: label for Real-Time replication state
realtime=Em tempo real
#XFLD: Label for table column
txtNextSchedule=Execução seguinte
#XFLD: Label for table column
txtNextScheduleNew=Próxima execução programada
#XFLD: Label for table column
txtNumOfRecords=Número de registros
#XFLD: Label for scheduled link
scheduledTxt=Programado
#XFLD: LABEL for partially persisted link
partiallyPersisted=Persistido parcialmente
#XFLD: Text for paused text
paused=Pausado

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ View Monitor Tasklog Details Action keys~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message for release lock actionn
releaseLockMsg=Se uma execução levar mais tempo do que o normal, isso pode indicar que ocorreu uma falha nessa execução e que o status não foi atualizado adequadamente. \r\nPara resolver o problema, você pode liberar o bloqueio e definir o status como Com falha.
#XFLD: Label for release lock dialog
releaseLockText=Liberar bloqueio

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Column header tooltip ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: tooltip for table column
txtViewTooltip=Nome da visão persistida
#XFLD: tooltip for table column
txtViewDataAccessTooltip=Indica a disponibilidade da visão
#XFLD: tooltip for table column
txtViewRefreshFreqTooltip=Indica se uma programação está definida para a visão
#XFLD: tooltip for table column
txtViewStatusTooltip=Obter o status da visão persistida
#XFLD: tooltip for table column
txtViewLatestUpdateTooltip=Fornece informações sobre quando a visão persistida foi atualizada pela última vez
#XFLD: tooltip for table column
txtViewNextRunTooltip=Se uma programação está definida para a visão, veja para quando a execução seguinte está programada.
#XFLD: tooltip for table column
txtViewNumOfRecordsTooltip=Controlar o número de registros.
#XFLD: tooltip for table column
txtViewInMemorySizeTooltip=Controla o volume que a visão está usando da sua memória
#XFLD: tooltip for table column
txtViewDiskSizeTooltip=Controla o volume que a visão está ocupando no seu disco
#XMSG: Expired text
txtExpired=Expirado

#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=O objeto "{0}" não pode ser adicionado à cadeia de tarefas.

#~~~~~~~~~~~View Analyzer task log messages~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message indicating number of records for a view, and memory consumption in Mebibyte during simulation of view persistency
RECORDS_PEAK_MEMORY=A visão "{0}" tem {1} registros. Uma simulação da persistência de dados da visão usou {2} MiB de memória.
#XMSG: View Analyzer execution failure
VIEW_ANALYZER_EXECUTION_FAILED=Falha na execução do View Analyzer.
#XMSG: Missing authorizations for View Analyzer
VIEW_ANALYZER_MISSING_AUTH=Sem autorizações para o View Analyzer.
#XMSG: Maximum memory in Gibibyte is reached, therefore persistency simulation is stopped
MAXIMUM_MEMORY_REACHED=A memória máxima de {0} GiB foi alcançada ao simular a persistência de dados para a visão "{1}". Portanto, nenhuma simulação adicional de persistência de visão será executada.
#XMSG: Error during persistency simulation occurred
PERSISTENCY_SIMULATION_ERROR=Ocorreu um erro durante a simulação de persistência de dados da visão "{0}".
#XMSG: Persistency simulation is not executed, because prerequisites are not fulfilled and the view cannot be persisted
PREREQUISITES_NOT_FULFILLED=A simulação de persistência de dados não foi executada para a visão "{0}" porque os pré-requisitos não foram atendidos e a visão não pode ser persistida.
#XMSG: Persistency simulation is not executed, because the view does not exist in the database
VIEW_DOES_NOT_EXIST=Você deve implementar a visão "{0}" para ativar a simulação de persistência de dados.
#XMSG: Number of records cannot be determined, because the local table does not exist in the database
TABLE_DOES_NOT_EXIST=A tabela local "{0}" não existe no banco de dados, ou seja, não é possível determinar o número de registros para essa tabela.
##XMSG: Message start of cancellation for View Analyzer task
CANCEL_VIEW_ANALYZER_TRIGGERED=O processo para interromper a tarefa do Analisador de visões {0} para a visão "{1}" foi enviado. Pode haver um atraso até que a tarefa seja interrompida.
##XMSG: Message start to indicate that task is not active
CANCEL_VIEW_ANALYZER_NO_ACTIVE_TASK=A tarefa do Analisador de visões {0} para a visão "{1}" não está ativa.
#XMSG: Cancel View Analyzer task failure
CANCEL_VIEW_ANALYZER_FAILED=Falha ao cancelar a tarefa do Analisador de visões.
##XMSG: Message used in View Analyzer execution task when task is canceled
VIEW_ANALYZER_CANCELED=A execução do Analisador de visões para a visão "{0}" foi interrompida por meio de uma tarefa de cancelamento.
##XMSG: Message start of cancelation for Model Validation task
CANCEL_MODEL_VALIDATION_TRIGGERED=O processo para interromper a tarefa Validação de modelo {0} para a visão "{1}" foi enviado. Pode haver um atraso até que a tarefa seja interrompida.
##XMSG: Message to indicate that task is not active
CANCEL_MODEL_VALIDATION_NO_ACTIVE_TASK=A tarefa Validação de modelo {0} para visão "{1}" não está ativa.
#XMSG: Cancel Model Validation task failure
CANCEL_MODEL_VALIDATION_FAILED=Falha ao cancelar a tarefa Validação de modelo.
##XMSG: Message used in Model Validation task when task is canceled
MODEL_VALIDATION_CANCELED=A execução da Validação de modelo para a visão "{0}" foi interrompida por meio de uma tarefa de cancelamento.
#XMSG: Message for Locked space error, before starting Model Validation task
MODEL_VALIDATION_LOCKED_SPACE_ERROR=Não é possível executar a Validação de modelo para a visão "{0}", a área "{1}" está bloqueada.
##XMSG: Message used in View Analyzer execution task when determination of number of rows fails
NUMBER_ROWS_DETERMINATION_FAILED=Erro ao determinar o número de linhas da tabela local "{0}".
##XMSG: Message to indicate that SQL plan was created
EXECUTION_PLAN_CREATED=O arquivo de plano do Analisador de SQL para a visão "{0}" foi criado e pode ser baixado.
#XFLD: Message to indicate starting of process to generate SQL Analyzer plan file
START_PLANVIZ_EXECUTION=Iniciando processo para gerar arquivo de plano do Analisador de SQL para a visão "{0}".
#XFLD: Message to indicate starting of View Analyzer process
VIEW_ANALYZER_EXECUTION_STARTED=Iniciando execução do Analisador de visões.
##XMSG: Message to indicate that SQL plan file cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_PLANVIZ_NOT_FULFILLED=Não é possível gerar o arquivo de plano do Analisador de SQL para a visão "{0}", os pré-requisitos de persistência de dados não foram atendidos.
##XMSG: Error message during  generation of SQL Analyzer plan file
PLANVIZ_ERROR=Erro durante a geração do arquivo de plano do Analisador de SQL para a visão "{0}".
#XMSG: Message for Locked space error
VIEW_ANALYZER_LOCKED_SPACE_ERROR=Não é possível executar o Analisador de visões para a visão "{0}", a área "{1}" está bloqueada.
#XMSG: Message for persistency simulation without partitioning
PERSISTENCY_SIMULATION_WITHOUT_PARTITIONS=As partições da visão "{0}" não são consideradas durante a simulação da persistência de dados.
#XMSG: Message for generation of SQL Analyzer plan file without considering partitioning
PLANVIZ_WITHOUT_PARTITIONS=Partições da visão "{0}" não são consideradas durante a geração do arquivo de plano do Analisador de SQL.
#XMSG: Confirmation message for mass removal of persisted data
MassRemovePersistency_Confirm_Msg=Deseja remover os dados persistidos e voltar o acesso aos dados para acesso virtual?
#XMSG: Confirmation message for partial mass removal of selected persisted data
PartialMassRemovePersistency_Confirm_Msg={0} das {1} visões selecionadas têm dados persistidos. \n Deseja remover os dados persistidos e voltar o acesso aos dados para acesso virtual?
#XMSG: success message for stopping mass persistence
stopMassPersistenceSuccess=Estamos removendo os dados persistidos das tabelas selecionadas.
#XMSG: error message during mass stop persistence of a view (backend error)
STOP_MASS_PERSISTENCE_ERROR=Erro ao interromper a persistência das visões selecionadas.
#XMSG: memory analysis is only performed in space {0}
NO_MEMORY_ANALYSIS=Análise da memória só é realizada para entidades na área "{0}": "{1}" "{2}" foram ignorados.
##XMSG: Message to indicate that Explain Plan cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_EXPLAINPLAN_NOT_FULFILLED=O arquivo de plano explicativo não pode ser gerado para a visão "{0}" porque os pré-requisitos de persistência não foram atendidos.
#XMSG: Message for Explain Plan generation without partitioning
EXPLAINPLAN_WITHOUT_PARTITIONS=As partições da visão "{0}" não são consideradas durante a geração do arquivo de plano explicativo.
#XFLD: Message to indicate starting of process to generate Explain Plan file
START_EXPLAINPLAN_EXECUTION=Iniciando processo para gerar o arquivo de plano explicativo para a visão "{0}".
##XMSG: Message to indicate that Explain Plan was created
EXPLAINPLAN_CREATED=O arquivo de plano explicativo para a visão "{0}" foi gerado. Você pode exibi-lo clicando em "Exibir detalhes" ou o baixá-lo caso tenha a permissão relevante.
##XMSG: Error message during  generation of Explain Plan file
EXPLAINPLAN_ERROR=Erro durante a geração do arquivo de plano explicativo da visão "{0}".
##XMSG: Explain Plan file cannot be generated for the view {0}, because too many views are stacked on each other
EXPLAINPLAN_MODEL_TOO_COMPLEX=Não é possível gerar o arquivo de plano explicativo da visão "{0}". Há muitas visões empilhadas entre si. Modelos complexos podem causar erros de falta de memória e reduzir o desempenho. É recomendável persistir uma visão.

##XMSG: Message to indicate that the space is locked
PERFORMANCE_ANALYSIS_LOCKED_SPACE_ERROR=Não é possível executar a análise de desempenho da visão "{0}", a área "{1}" está bloqueada.
##XMSG: The performance analysis task was cancelled
PERFORMANCE_ANALYSIS_CANCELED=A análise de desempenho da visão "{0}" foi cancelada.
##XMSG: The performance analysis failed
PERFORMANCE_ANALYSIS_FAILED=Falha na análise de desempenho.
##XMSG: The performance analysis is finished
PERFORMANCE_ANALYSIS_FINISHED=Análise de desempenho da visão "{0}" concluída. Para exibir o resultado, clique em "Exibir detalhes".
##XMSG: The view has input parameter with no default value
INPUT_PARAMETER_WITHOUT_DEFAULT_VALUE=Não é possível analisar esta visão, ela tem um parâmetro sem valor padrão.
##XMSG: The view is not fully deployed
VIEW_NOT_FULLY_DEPLOYED=Não é possível analisar esta visão, ela não foi completamente implementada.
##XMSG: The view uses limited remote adapters
LIMITED_REMOTE_ADAPTER=Esta visão usa pelo menos um adaptador remoto com recursos limitados como condições de filtro ausentes ou suporte a 'Count'. A persistência ou replicação de objetos pode melhorar o desempenho de tempo de execução.
##XMSG: The view uses remote adapter that does not support 'Limit'
REMOTE_ADAPTER_NOT_SUPPORTING_LIMIT=Esta visão usa pelo menos um adaptador remoto que não suporta 'Limite'. Mais de 1000 registros podem ter sido selecionados.
##XMSG: Performance analysis is executed by using default parameters of view'
INPUT_PARAMETER_WITH_DEFAULT_VALUE=A análise de desempenho é executada usando valores padrão dos parâmetros da visão.
##XMSG: Message to indicate that an error occurred
PERFORMANCE_ANALYSIS_ERROR=Ocorreu um erro durante a análise de desempenho para a visão "{0}".
##XMSG: Message start of cancellation for Performance Analysis task
CANCEL_PERFORMANCE_ANALYSIS_TRIGGERED=O processo para interromper a tarefa de análise de desempenho {0} para a visão "{1}" foi enviado. Pode haver um atraso até que a tarefa seja interrompida.
##XMSG: Message start to indicate that task is not active
CANCEL_PERFORMANCE_ANALYSIS_NO_ACTIVE_TASK=A tarefa de análise de desempenho {0} para a visão "{1}" não está ativa.
#XMSG: Cancel Performance Analysis task failure
CANCEL_PERFORMANCE_ANALYSIS_FAILED=Falha ao cancelar a tarefa de análise de desempenho.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Atribuir programação a mim
#XBUT: Pause schedule menu label
pauseScheduleLabel=Pausar programação
#XBUT: Resume schedule menu label
resumeScheduleLabel=Retomar programação
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Ocorreu um erro ao remover as programações.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Ocorreu um erro ao atribuir as programações.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Ocorreu um erro ao pausar as programações.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Ocorreu um erro ao retomar as programações.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Excluindo {0} programações
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Alteração do proprietário de {0} programações
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Pausando {0} programações
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Retomando {0} programações
#XBUT: Select Columns Button
selectColumnsBtn=Selecionar colunas
#XFLD: Refresh tooltip
TEXT_REFRESH=Atualizar
#XFLD: Select Columns tooltip
text_selectColumns=Selecionar colunas


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Runtime Metrics ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Label for Runtime Metrics
RuntimeMetricsTitle=Métricas de tempo de execução para
#XFLD : Label for Run Button
runButton=Executar
#XFLD : Label for Cancel Button
cancelButton=Cancelar
#XFLD : Label for Close Button
closeButton=Fechar
#XFLD : Label for Open View Analyzer Button
openViewAnalyzerButton=Abrir Analisador de visões
#XFLD : Label for Generate Explain Plan Button
generateExplainPlanButton=Gerar plano explicativo
#XFLD : Label for Previous Run Column
previousRun=Execução anterior
#XFLD : Label for Latest Run Column
latestRun=Última execução
#XFLD : Label for time Column
time=Hora
#XFLD : Label for Duration Column
duration=Duração
#XFLD : Label for Peak Memory Column
peakMemory=Pico de memória
#XFLD : Label for Number of Rows
numberOfRows=Número de linhas
#XFLD : Label for Overall Number of Sources
overallNumberOfSources=Número total de fontes
#XFLD : Label for Data Access Column
dataAccess=Acesso aos dados
#XFLD : Label for Local Tables
localTables=Tabelas locais
#XFLD : Label for federatedRemoteTables
federatedRemoteTables=Tabelas remotas federadas (com recursos de adaptador limitados)
#XTXT Text for initial state of the runtime metrics
initialState=Você deve primeiro executar a análise de desempenho para obter as métricas. Isso pode levar algum tempo, mas você pode cancelar o processo se necessário.
#XMSG: Cancel confirmation message
CancelRunConfirmationMessage=Deseja realmente cancelar a execução atual da análise de desempenho?
#XTIT: Cancel dialog title
CancelRunTitle=Cancelar execução
#XFLD: Label for Number of Rows
NUMBER_ROWS=Número de linhas
#XFLD: Label for Overall Number of Sources
OVERALL_NUMBER_SOURCES=Número total de fontes
#XFLD: Label for Data Access
DATA_ACCESS=Acesso aos dados
#XFLD: Label for Remote tables
NUMBER_REMOTE_TABLES_LIMITED_ADAPTERS=Tabelas remotas federadas (com recursos de adaptador limitados)
#XFLD: Label for select statement
SELECT_STATEMENT='SELECT * FROM VIEW LIMIT 1000'
#XFLD: Label for duration
SELECT_RUNTIME=Duração
#XFLD: Label for peak memory
SELECT_PEAK_MEMORY=Pico de memória
#XFLD: Label for count statement
SELECT_COUNT_STATEMENT='SELECT COUNT(*) FROM VIEW'
#XFLD: Label for count duration
SELECT_COUNT_RUNTIME=Duração
#XFLD: Label for count peak memory
SELECT_COUNT_PEAK_MEMORY=Pico de memória
#XFLD: Label for count of number of local tables
NUMBER_LOCAL_TABLES_FILE=Tabelas locais (arquivo)
#XTXT: Text for running state of the runtime metrics
Running=Executando...
#XFLD: Label for time
Time=Hora
#XFLD: Label for virtual access
PA_VIRTUAL=Virtual
#XFLD: Label for persisted access
PA_PERSISTED=Persistido
PA_PARTIALLY_PERSISTED=Persistido parcialmente
#XTXT: Text for cancel
CancelRunSuccessMessage=Cancelando a execução da análise de desempenho.
#XTXT: Text for cancel error
CancelRunErrorMessage=Ocorreu um erro ao cancelar a execução da análise de desempenho.
#XTXT: Text for explain plan generation
ExplainPlanStarted=Gerando o plano explicativo para a visão "{0}".
#XTXT: Text for performance analysis
PerformanceAnalysisStarted=Iniciando a análise de desempenho para a visão "{0}".
#XTXT: Text for performance analysis error
ErrorFetchingPerformanceAnalysis=Ocorreu um erro ao buscar os dados da análise de desempenho.
#XTXT: Text for performance analysis error
conflictingTask=A tarefa de análise de desempenho já está em execução
#XFLD: Label for Errors
Errors=Erro(s)
#XFLD: Label for Warnings
Warnings=Aviso(s)
#XFLD: Tooltip for missing privileges
openViewAnalyzerButtonTooltipNoPrivilege=Você precisa do privilégio DWC_DATAINTEGRATION(atualizar) para abrir o Analisador de visões.
#XFLD: Tooltip for missing privileges
generateExplainPlanButtonTooltipNoPrivilege=Você precisa do privilégio DWC_RUNTIME(ler) para gerar o plano explicativo.



#XFLD: Label for frequency column
everyLabel=A cada
#XFLD: Plural Recurrence text for Hour
hoursLabel=Horas
#XFLD: Plural Recurrence text for Day
daysLabel=Dias
#XFLD: Plural Recurrence text for Month
monthsLabel=Meses
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minutos
