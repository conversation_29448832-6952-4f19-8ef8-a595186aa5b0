
#XBCB: Breadcrumb
viewMonitorBreadcrumb={spaceId}

#XFLD: Label for persisted view column
SOURCE=Zdroj
#XFLD: Label for persisted view column
NAME=Název
#XFLD: Label for persisted view column
NAME_LABEL=Business název
#XFLD: Label for persisted view column
NAME_LABELNew=Objekt (business název)
#XFLD: Label for persisted view column
TECHINCAL_NAME=Technický název
#XFLD: Label for persisted view column
TECHINCAL_NAMENew=<PERSON><PERSON><PERSON><PERSON> (technický název)
#XFLD: Label for persisted view column
DATA_PERSISTENCY=Přístup k datům
#XFLD: Label for persisted view column
STATUS=Status
#XFLD: Label for persisted view column
LAST_UPDATED=Poslední aktualizace
#XFLD: Label for persisted view column
IN_MEMORY_SIZE=Paměť využitá pro úložiště (MiB)
#XFLD: Label for persisted view column
DISK_SIZE=Disk využitý pro úlo<PERSON>tě (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_NEWW=Velikost vnitřn<PERSON> (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_HEADER=Velikost vnitřní paměti
#XFLD: Label for persisted view column
DISK_SIZE_NEWW=Prostor na disku (MiB)
#XFLD: Label for persisted view column
DISK_SIZE_HEADER=Prostor na disku
#XFLD: Label for schedule owner column
txtScheduleOwner=Vlastník plánu
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Zobrazuje, kdo vytvořil plán
#XFLD: Label for persisted view column
PERSISTED=Trvale uloženo
#XFLD: Label for persisted view column
TYPE=Typ
#XFLD: Label for View Selection Dialog column
changedOn=Změněno dne
#XFLD: Label for View Selection Dialog column
createdBy=Vytvořil
#XFLD: Label for log details column
txtViewPersistencyLogs=Zobrazení protokolů
#XFLD: Label for log details column
txtViewPersistencyLogsNew=Detaily
#XFLD: text for values shown for Ascending sort order
SortInAsc=Třídit vzestupně
#XFLD: text for values shown for Descending sort order
SortInDesc=Třídit sestupně
#XFLD: Label for persisted view monitor heading
ViewMonitor_Heading=Zobrazuje monitor
#XFLD: Label for persisted view monitor sub-heading
ViewMonitor_SubHeading=Monitorovat a upravit data perzistence pohledů


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ DynamicPageHeader ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: text for values shown in column Persistence Status
txtLoading=Načítání
#XFLD: text for values shown in column Persistence Status
txtRunning=Probíhá
#XFLD: text for values shown in column Persistence Status
txtAvailable=Dostupné
#XFLD: text for values shown in column Persistence Status
txtError=Chyba
#XMSG: Error message for unknown Persistence type that gets logged in Kibana
unknownReplicationTypeError=Typ replikace ''{0}'' není podporován.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Content ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Nastavení použitá pro poslední běh perzistence dat:
#XMSG: Message for input parameter name
inputParameterLabel=Vstupní parametr
#XMSG: Message for input parameter value
inputParameterValueLabel=Hodnota
#XMSG: Message for persisted data
inputParameterPersistedLabel=Trvale uloženo v
#XTIT: Count for View in ViewMonitor Toolbar
txtViewCount=Pohledy ({0})
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Perzistence pohledu
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Perzistence dat
#XBUT: Button to clear the selected view persistance
ClearViewPersistance=Vymazat
#XBUT: Button to stop the selected view persistance
stopPersistance=Zastavit perzistenci
#XFLD: Placeholder for Search field
txtSearch=Hledat
#XBUT: Tooltip for refresh button
txtRefresh=Aktualizovat
#XBUT: Tooltip for add view button
txtDeleteView=Odstranit perzistenci
#XBUT: Tooltip for load new peristence
loadNewPersistence=Restartovat perzistenci
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Načíst nový snímek
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Spustit perzistenci dat
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Odebrat perzistentní data
#XMSG: success message for starting persistence
startPersistenceSuccess=Činíme pohled ''{0}'' perzistentním.
#XMSG: success message for stopping persistence
stopPersistenceSuccess=Odebíráme perzistentní data pro pohled ''{0}''.
#XMSG: success message for stopping persistence
deletePersistedViewSuccess=Odebíráme pohled ''{0}'' ze seznamu monitorování.
#XMSG: error message during start persistence of a view (backend error)
ERROR_STARTING_PERSISTENCE=Při zahajování perzistence dat pro pohled ''{0}'' došlo k chybě.
#XMSG: error message during start persistence of a view (backend error)
PARAMERROR_STARTING_PERSISTENCE=Pohled "{0}" není pro perzistenci podporován, protože obsahuje vstupní parametry.
#XMSG: error message during start persistence of a view (backend error)
NUMBER_OF_PARAMETER_STARTING_PERSISTENCE=Pohled "{0}" není pro perzistenci podporován, protože má více než jeden vstupní parametr.
#XMSG: error message during start persistence of a view (backend error)
PARAMETER_DEFAULT_VALUE_STARTING_PERSISTENCE=Pohled "{0}" není pro perzistenci podporován, protože vstupní parametr nemá standardní hodnotu.
#XMSG: error message during start persistence of a view (backend error)
REDEPLOYMENT_DAC_REQUIRED=Je vyžadováno opětovné nasazení řízení přístupu k datům "{0}" pro podporu perzistence dat.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_STARTING_PERSISTENCE=Pohled ''{0}'' nemůže být perzistentní, protože používá pohled ''{1}'', který obsahuje řízení přístupu k datům.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_EXTERNAL_STARTING_PERSISTENCE=Pohled ''{0}'' nemůže být perzistentní, protože používá pohled s řízením přístupu k datům, jež patří k jinému prostoru.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_STRUCTURE_STARTING_PERSISTENCE=Pohled ''{0}'' nemůže být perzistentní, protože struktura jednoho nebo více jeho řízení přístupu k datům nepodporuje perzistenci dat.
#XMSG: error message during stop persistence of a view (backend error)
STOP_PERSISTENCE_ERROR=Při zastavování perzistence pro pohled ''{0}'' došlo k chybě.
#XMSG: error message during stop persistence of a view (backend error)
DELETE_PERSISTED_VIEW_ERROR=Při odstraňování perzistentního pohledu ''{0}'' došlo k chybě.
#XMSG: Warning message for removing the persisted data
RemovePersistency_Confirm_Msg=Chcete odstranit perzistentní data a přepnout na virtuální přístup pohledu ''{0}''?
#XMSG: Warning message for delete the view persistence
DeletePersistency_Confirm_Msg=Chcete odebrat pohled ze seznamu monitorování a odstranit perzistentní data pohledu ''{0}''?
#XMSG: error message for reading data from backend
txtReadBackendError=Vypadá to, že při čtení z backendu došlo k chybě.
#XFLD: Label for No Data Error
NoDataError=Chyba
#XMSG: message for conflicting task
Task_Already_Running=Konfliktní úloha je již spuštěna pro pohled ''{0}''.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Show All Views~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Text for show all views button
allViewsText=Všechny pohledy ({0})
#XBUT: Text for show scheduled views button
scheduledText=Naplánováno ({0})
#XBUT: Text for show persisted views button
persistedText=Trvale uloženo ({0})
#XBUT: Text for start analyzer button
startAnalyzer=Spustit analyzátor pohledu
#XFLD: Message if repository is unavailable
repositoryErrorMsg=Úložiště není dostupné a určité funkce jsou deaktivovány.

#XFLD: Data Access - Virtual
Virtual=Virtuální
#XFLD: Data Access - Persisted
Persisted=Trvale uloženo

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Non-Persisted View Selection Dialog~~~~~~~~~~~~~~~~~~~~~~~

#XTIT: Title for Non-Persisted View selection dialog
select_source=Vyberte pohled pro perzistenci

#XTIT: Placeholder for Non-Persisted View selection dialog search
searchView=Hledat pohledy
#XTIT: No data in the list of non-persisted view
No_Data=Žádná data
#XBUT: Button to select non-persisted view
ok=OK
#XBUT: Button to close the non-persisted views selection dialog
cancel=Zrušit

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~View persistency log message key~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message for view persistency logs
START_STOP_PERSISTENCY=Zahájení běhu úlohy perzistence dat pro ''{1}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY=Perzistence dat pro pohled ''{1}''.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY)
START_VIEW_PERSISTENCY_PROCESS=Začíná proces perzistence dat pro pohled ''{0}''.
#XFLD: Message for view persistency manual partition load
START_VIEW_PERSISTENCY_PROCESS_MANUAL_PARTITIONS=Začíná proces perzistence dat pro pohled ''{0}'' s vybranými ID oddílů: ''{1}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY=Odebírání perzistentních dat pro pohled ''{1}''.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY)
REMOVE_VIEW_PERSISTENCY=Začíná proces odebrání perzistentních dat pro pohled "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_SUCCESS=Perzistence dat realizována pro pohled ''{1}''‘.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_SUCCESS)
START_VIEW_PERSISTENCY_PROCESS_SUCCESS=Perzistence dat pro pohled ''{0}''‘ úspěšně provedena.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY_SUCCESS=Perzistentní data byla odebrána a byl obnoven virtuální přístup k datům pro pohled ''{1}''.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_SUCCESS)
REMOVE_VIEW_PERSISTENCY_SUCCESS=Proces odebrání perzistentních dat pro pohled "{0}" je dokončen.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_FAILED=Nelze provést perzistenci dat pro pohled ''{1}''.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_FAILED)
START_VIEW_PERSISTENCY_PROCESS_FAILED=Nelze provést perzistenci dat pro pohled ''{0}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.05)
STOP_VIEW_PERSISTENCY_FAILED=Nelze odebrat perzistentní data pro pohled ''{1}''.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_FAILED)
REMOVE_VIEW_PERSISTENCY_FAILED=Nelze odebrat perzistentní data pro pohled ''{0}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_SUCCESS=Provedena perzistence ''{3}'' záznamů pro pohled ''{1}''.
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace START_PERSISTENCY_RECORD_COUNT_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS={0} záznamy vloženy do tabulky perzistence dat pro pohled "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS_V2={0} Záznamy vloženy do tabulky perzistence dat pro pohled ''{1}''. Využitá paměť: {2} GiB.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_SUCCESS=''{3}'' perzistentní záznamy odebrány pro pohled ''{1}''.
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace STOP_PERSISTENCY_RECORD_COUNT_SUCCESS)
REMOVE_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=Perzistentní data odebrána, ''{0}'' perzistentní záznamy odstraněny.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_FAILED=Načtení recordCount se nezdařilo pro pohled ''{1}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_FAILED=Načtení recordCount se nezdařilo pro pohled ''{1}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Časový plán byl odstraněn pro ''{1}''.
#XFLD: Message for view persistency logs (new message to replace STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS)
REMOVE_VIEW_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Časový plán byl odstraněn pro pohled ''{0}''.
#XFLD: Message for view persistency logs
STOP_PERSISTENCY_DELETE_SCHEDULE_FAILED=Odstranění časového pánu se nezdařilo pro ''{1}''.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_REDEPLOY=Nemůžeme učinit pohled ''{0}'' perzistentním, protože od chvíle, kdy jste s tím začali, byl změněn a nasazen. Pokuste se o to znovu nebo počkejte na následující naplánovaný běh.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_DELETE=Nemůžeme učinit pohled ''{0}'' perzistentním, protože od chvíle, kdy jste s tím začali, byl odstraněn.
#XFLD: Message for successful data persistency of a single partition (for messages created before version 2022.03)
START_PERSISTENCY_SINGLE_PARTITION_SUCCESS=Provedena perzistence {0} záznamů do oddílu pro hodnoty ''{1}'' <= {2} < ''{3}''.
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (new message to replace START_PERSISTENCY_SINGLE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} záznamy vloženy do oddílu pro hodnoty ''{1}'' <= ''{2}'' < ''{3}'' .
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS_V2={0} záznamy vloženy do oddílu pro hodnoty ''{1}'' <= ''{2}'' < ''{3}''. Využitá paměť: {4} GiB.
#XFLD: Message for successful replication of a "others" partition (for messages created before version 2022.03)
START_PERSISTENCY_OTHERS_PARTITION_SUCCESS=Provedena perzistence {0} záznamů do oddílu ''jiné''.
#XFLD: Message used by SEAL repository for successful replication of a "others" partition (new message to replace START_PERSISTENCY_OTHERS_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} záznamy vloženy do oddílu ''jiné''.
#XFLD: Message for view persistency record count of partitions (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS=Provedena perzistence {3} záznamů pro pohled ''{1}'' v {4} oddílech.
#XFLD: Message used by SEAL repository for view persistency record count of partitions (new message to replace START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={0} záznamy vloženy do tabulky perzistence dat pro pohled ''{1}''‘ v {2} oddílech.
#XFLD: Message used by SEAL repository for view persistency for partition wise refresh
START_VIEW_PERSISTENCY_RECORD_COUNT_REFRESHED_PARTITIONS_SUCCESS={0} záznamy vloženy do tabulky perzistence dat pro pohled ''{1}''. Aktualizované oddíly: {2}; Blokované oddíly: {3}; Oddíly celkem: {4}
#XFLD: Message used by SEAL repository for view persistency for manual partition load
START_VIEW_PERSISTENCY_RECORD_COUNT_MANUAL_PARTITIONS_SUCCESS={0} záznamy vloženy tabulky perzistence dat pro pohled ''{1}'' v {2} vybraných oddílech.
#XFLD: Message used by SEAL repository for view persistency for locked partition data load
START_VIEW_PERSISTENCY_RECORD_COUNT_LOCKED_PARTITIONS_SUCCESS={0} záznamy vloženy do tabulky perzistence dat pro pohled ''{1}''. Aktualizované oddíly: {2}; Blokované nezměněné oddíly: {3}; Oddíly celkem: {4}
#XMSG: Message for unexpected error (for messages created before version 2022.03)
START_PERSISTENCY_UNEXPECTED_ERROR=Při zřizování perzistence dat pro pohled ''{0}''‘ došlo k neočekávané chybě.
#XMSG: Message for unexpected error (new message to replace START_PERSISTENCY_UNEXPECTED_ERROR)
START_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Při zřizování perzistence dat pro pohled ''{0}''‘ došlo k neočekávané chybě.
#XMSG: Message for Locked space error
START_VIEW_PERSISTENCY_LOCKED_SPACE_ERROR=Pohled ''{0}''’ nelze učinit perzistentním, protože prostor ''{1}'' je blokován.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
STOP_PERSISTENCY_UNEXPECTED_ERROR=Při odebírání perzistentních dat pro pohled ''{0}''‘ došlo k neočekávané chybě.
#XMSG: Message for unexpected error (new message to replace STOP_PERSISTENCY_UNEXPECTED_ERROR)
REMOVE_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Při odebírání perzistence pro pohled ''{0}''‘ došlo k neočekávané chybě.
#XMSG: Message for ivalidated view
VIEW_PERSISTENCY_ABORTED_DEFINITION_INVALID=Definice pohledu ''{0}'' se stala neplatnou, pravděpodobně kvůli změně objektu spotřebovávaného tímto pohledem přímo nebo nepřímo. Abyste tento problém vyřešili, zkuste pohled znovu nasadit nebo identifikovat hlavní příčinu.
#XMSG: Message for persisted data removal (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT=Při nasazování pohledu ''{0}''‘ se odeberou perzistentní data.
#XMSG: Message for persisted data removal of a consuming view
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW=Při nasazování spotřebovaného pohledu ''{0}''‘ se odeberou perzistentní data.
#XMSG: Message for persisted data removal of a consuming view due to a dac change
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW_DAC_CHANGED=Perzistentní data jsou odebrána při nasazení použitého pohledu ''{0}'', protože jeho řízení přístupu k datům se změnilo.
#XMSG: Message for persisted data removal (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DEPLOYMENT=Při nasazování pohledu ''{0}''‘ se odeberou perzistentní data.
#XMSG: Message for deleted view (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION=Perzistence je odebrána s odstraněním z pohledu ''{0}''.
#XMSG: Message for deleted view (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DELETION=Perzistence je odebrána s odstraněním z pohledu ''{0}''.
#XMSG Message for removal of persisted data, because prerequisites not fulfilled
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_PREREQUISITES_NOT_FULFILLED=Perzistentní data jsou odebrána, protože předpoklady perzistence dat již nejsou splněny.
#XMSG: Message for inconsistent persistency
VIEW_PERSISTENCY_INCONSISTENT_PERSISTENCY=Perzistence pohledu "{0}" se stala nekonzistentní. Pro vyřešení problému odeberte perzistentní data.
#XMSG: Message for prerequisites
START_PERSISTENCY_CHECK_PREREQUISITES=Kontrola předpokladů pro perzistenci pohledu ''{0}''.
#XMSG: Message for DAC deployed in deprecated way using table function and join (for messages created before 2025.05)
START_PERSISTENCY_DEPRECIATED_DAC=Pohled "{0}" je nasazen pomocí řízení přístupu k datům(DAC), které již zastarává. Nasaďte pohled znovu, abyste zlepšili výkon.
#XMSG: Message for DAC deployed in deprecated way using table function and join
START_PERSISTENCY_DEPRECATED_DAC=Pohled "{0}" je nasazen pomocí řízení přístupu k datům(DAC), které již zastarává. Nasaďte pohled znovu, abyste zlepšili výkon.
#XMSG: Message for roll back (for messages created before version 2022.03)
ROLLBACK_VIEW_PERSISTENCY=Došlo k chybě. Předchozí stav perzistence byl obnoven pro pohled ''{0}''.
#XMSG: Message used by SEAL repository for rollback of running procedure to persist view (new message to replace ROLLBACK_VIEW_PERSISTENCY)
VIEW_PERSISTENCY_ROLLBACK=Došlo k chybě. Proces pro perzistenci pohledu ''{0}''‘ byl zastaven a změny byly vráceny.
#XMSG: Message used by SEAL repository for rollback of running procedure to remove persisted data of view
REMOVE_VIEW_PERSISTENCY_ROLLBACK=Došlo k chybě. Proces odebrání perzistentních dat pohledu ''{0}''‘ byl zastaven a změny byly vráceny.
#XMSG: Message used by SEAL repository for persistency preparation
START_PERSISTENCY_PREPARATION=Příprava na perzistenci dat.
#XMSG: Message used by SEAL repository for data insertion
START_INSERTING_PERSISTENCY_DATA=Vkládání dat do tabulky perzistence.
#XMSG: Message for null values - partitioning (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS={0} záznamy nulových hodnot vloženy do oddílu ''jiné''.
#XMSG: Message used by SEAL repository for others partition values - partitioning (new message to replace START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS={0} záznamy vloženy do oddílu ''jiné'' pro hodnoty ''{2}'' < ''{1}'' NEBO ''{2}'' >= ''{3}''.
#XMSG: Message used by SEAL repository for others partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS_V2={0} záznamy vloženy do oddílu ''jiné''‘ pro hodnoty ''{2}'' < ''{1}'' NEBO ''{2}'' >= ''{3}''. Využitá paměť: {4} GiB.
#XMSG: Message used by SEAL repository for null partition values - partitioning
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS={0} záznamy vloženy do oddílu ''jiné'' pro hodnoty ''{1}'' JE NULL.
#XMSG: Message used by SEAL repository for null partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS_V2={0} záznamy vloženy do oddílu ''jiné'' pro hodnoty ''{1}'' JE NULL. Využitá paměť: {2} GiB.
##XFLD:Message used by SEAL repository for remote statistics
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS=Načítání příslušných dat: {0} vzdálené příkazy. Celkový počet načtených záznamů: {1}. Celková doba trvání: {2} sekund.
##XFLD:Message used by SEAL repository for remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS_WITH_PARTITIONS=Načítání dat zahrnuje použití {0} oddílů s {1} vzdálenými příkazy. Celkový počet načtených záznamů: {2}. Celková doba trvání: {3} sekund.
##XFLD:Message used by SEAL repository with UI ID for instructions of remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_INSTRUCTION=Vzdálené příkazy zpracované při běhu lze zobrazit otevřením monitoru vzdálených dotazů v detailech zpráv specifických pro oddíl.
##XFLD:Message for start reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS=Spouštění procesu pro opětovné použití existujících perzistentních dat pro pohled {0} po nasazení.
##XFLD:Message used by SEAL repository for reattach view persitency
START_VIEW_PERSISTENCY_REATTACHMENT=Spustit opětovné použití existujících perzistentních dat.
##XFLD:Message used by SEAL repository for reattach view persitency
START_FINILIZING_VIEW_PERSISTENCY_REATTACHMENT=Opětovné použití existujících perzistentních dat.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_COMPLETE=Proces opětovného použití existujících perzistentních dat je dokončen pro pohled {0}.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_FAILED=Nezdařilo se opakovaně použít existující perzistentní data pro pohled {0} po nasazení.
#XMSG: Message used by SEAL repository for finalizing persistency
START_FINALIZING_PERSISTENCY=Finalizace perzistence.
#XMSG: Message used by SEAL repository for restoring virtual access for view
REMOVE_VIEW_PERSISTENCY_VIRTUAL_ACCESS_RESTORED=Virtuální přístup k datům byl obnoven pro pohled ''{0}''.
#XMSG: Message used by SEAL repository for view that already has virtual access
REMOVE_VIEW_PERSISTENCY_ALREADY_VIRTUAL_ACCESS=Pohled "{0}" má již virtuální přístup k datům. Žádná perzistentní data se neodeberou.
#XMSG: Message for removing view persistency
VIEW_PERSISTENCY_REMOVED_FROM_MONITOR=Pohled ''{0}'' byl odebrán z monitoru pohledu.
#XMSG: Message for view doesnt exist.
VIEW_PERSISTENCY_VIEW_NOT_EXISTING=Pohled "{0}" buď v databázi neexistuje, nebo není správně nasazen a proto nemůže být perzistentní. Abyste problém vyřešili, zkuste pohled znovu nasadit nebo identifikujte klíčovou příčinu.
#XMSG: Message for persistency not enabled.
VIEW_PERSISTENCY_NOT_ENABLED=Perzistence dat není aktivována. Pro aktivaci této funkčnosti znovu nasaďte tabulku/pohled v prostoru ''{0}''.
#XMSG: Message for unexpected error due to node crash
VIEW_PERSISTENCY_UNEXPECTED_ERROR_GENERAL=Poslední běh perzistence pohledu byl přerušen kvůli technickým chybám.
#XFLD: Message for view persistency memory consumption
VIEW_PERSISTENCY_MEMORY_CONSUMPTION={0} GiB maximální využité paměti v době běhu perzistence pohledu.
#XFLD: Message for view persistency timeout
VIEW_PERSISTENCY_TIMEOUT=Perzistence pohledu {0} dosáhla časového limitu {1} hodin(y).
#XFLD: Message for not started asynchronous view persistency
VIEW_PERSISTENCY_ASYNC_NOT_STARTED=Vysoké zatížení systému zabránilo spuštění asynchronního provádění persistence zobrazení. Zkontrolujte, zda paralelně neběží příliš mnoho úloh.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency (for messages created before version 2022.05)
START_VIEW_PERSISTENCY_EXECUTION_METHOD_DROP_AND_RENAME=Existující perzistentní tabulka byla odstraněna a nahrazena novou perzistentní tabulkou.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_RECREATE_AND_DROP=Existující perzistentní tabulka byla odstraněna a nahrazena novou perzistentní tabulkou.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_TRUNCATE_AND_COPY=Existující perzistentní tabulka byla aktualizována novými daty.
##XFLD: Message for missing authorizations for view persistency
VIEW_PERSISTENCY_MISSING_AUTH=Chybí oprávnění pro perzistenci dat.
##XFLD: Message start of persistency cancellation
VIEW_PERSISTENCY_TRIGGER_CANCEL=Zahájení rušení procesu pro perzistenci pohledu {0}.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_UNAVAILABLE_TASK_RUN=Proces pro perzistenci pohledu se nezdařilo zrušit, protože pro pohled {0} není spuštěna žádná úloha perzistence.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_NONDETERMINISTIC_TASK_RUN=Proces pro perzistenci pohledu se nezdařilo zrušit, protože pro pohled {0} není spuštěna žádná úloha perzistence dat.
##XFLD: Message for canceling a non running persitency task
VIEW_PERSISTENCY_CANCEL_NON_RUNNING_SELECTED_TASK=Nezdařilo se zrušit proces pro perzistenci pohledu {0}, protože vybraná úloha perzistence dat {1} není spuštěna.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_UNREACHABLE_PERSISTENCY_RUN=Proces pro perzistenci pohledu se nezdařilo zrušit, protože data perzistence pro pohled {0} ještě nebyla spuštěna.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_COMPLETED_PERSISTENCY_RUN=Proces pro perzistenci pohledu {0} se nezdařilo zrušit, protože již byl dokončen.
##XFLD: Message for failed cancellation
VIEW_PERSISTENCY_ABORTED_CANCEL_EXECUTION=Nezdařilo se zrušit proces pro perzistenci pohledu {0}.
##XFLD: Message for finalizing persistency cancellation
VIEW_PERSISTENCY_FINALIZE_CANCEL=Proces pro zastavení perzistence dat pohledu {0} byl odeslán.
##XFLD: Message used in persist task when cancellation is caught
VIEW_PERSISTENCY_CANCELED=Proces pro perzistenci pohledu {0} byl zastaven.
##XFLD: Message used in persist task when cancelation is caught and performed through a cancelation task
VIEW_PERSISTENCY_CANCELED_VIA_CANCEL_TASK=Proces pro perzistenci pohledu {0} byl zastaven úlohou zrušení {1}.
##XFLD: Message used in cancel task when the task is triggered by deployment
VIEW_PERSISTENCY_CANCEL_TRIGGERED_BY_DEPLOYMENT=Zrušení procesu pro perzistenci dat při nasazování pohledu {0}.
##XFLD: Message used in case the cancelation is retriggered for a running task
VIEW_PERSISTENCY_CANCEL_ATTEMPT_RETRIGGER_CANCEL=Předchozí úloha pro zrušení perzistence pohledu {0} již byla odeslána.
##XFLD: Message used to express the delay that may occur before the persistency task is stopped
VIEW_PERSISTENCY_CANCEL_DELAY_STOPPING_PERSISTENCY=Může být zpoždění, než se úloha perzistence dat pro pohled {0} zastaví.
##XFLD: Message used to reflect the task log identifier of the current running persistency task
VIEW_PERSISTENCY_CANCEL_CURRENT_RUNNING_TASK=Data pro pohled {0} jsou uchována s úlohou {1}.
##XFLD:Message used by SEAL repository for locked partitions and DACs
VIEW_PERSISTENCY_LOCKED_PARTITIONS_DACS=Oprávnění zadaná skrze DAC se mohla změnit a uzamčené oddíly je neberou v úvahu. Odblokujte oddíly a načtěte nový snímek pro použití změn.
##XFLD:Message used by SEAL repository for column structure change
VIEW_PERSISTENCY_COLUMN_STRUCTURE_CHANGE=Struktura sloupců se změnila a již neodpovídá existující tabulce perzistence. Odeberte perzistentní data a spusťte novou perzistenci dat, aby byla tabulka perzistence aktualizována.
##XFLD: Message for out of memory
VIEW_PERSISTENCY_HANA_OUT_OF_MEMORY=Úloha se nezdařila kvůli chybě nedostatku paměti v databázi SAP HANA.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_EXCEPTION_TRANSACTION_ROLLBACK=Úloha se nezdařila kvůli interní výjimce v databázi SAP HANA.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_SQL_EXECUTION_ISSUE=Úloha se nezdařila kvůli internímu problému provedení v databázi SAP HANA.
##XFLD: Message for HANA out of memory result
VIEW_PERSISTENCY_OUT_OF_MEMORY_RESULT_MESSAGE=Důvod chybějící paměti HANA: {0}
##XFLD: Message for admission control rejection
VIEW_PERSISTENCY_HANA_ADMISSION_CTRL_REJECTION=Úloha se nezdařila z důvodu odmítnutí řízení přístupu SAP HANA.
##XFLD: Message for HANA back pressure / rate limit of HANA client
VIEW_PERSISTENCY_HANA_CLIENT_RATE_LIMIT_REJECTION=Úloha se nezdařila z důvodu příliš mnoha aktivních připojení SAP HANA.
#XMSG: Message for invalid persisted table name
VIEW_PERSISTENCY_PERSISTED_TABLE_INVALID=Došlo k chybě a perzistentní tabulka se stala neplatnou. Chcete-li problém vyřešit, odeberte perzistentní data a znovu ponechejte zobrazení.
#XMSG: Message for usahe of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION=Tento pohled nemůže být perzistentní. Používá vzdálené tabulky s aktivovaným uživatelským rozšířením. Zkontrolujte rodokmen pohledu.
#XMSG: Message for dynamic usage of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION_DYNAMIC=Tento pohled nemůže být perzistentní. Používá vzdálené tabulky s aktivovaným uživatelským rozšířením. Vzdálená tabulka může být spotřebovávána dynamicky zobrazením skriptu SQL. Rodokmen pohledu nemusí zobrazovat vzdálenou tabulku.
#XMSG: Message for insufficient privileges or consumption of view with DAC via dynamic SQL
VIEW_PERSISTENCY_INSUF_PRIV_OR_DAC_DYN_SQL=Vaše oprávnění mohou být nedostatečná. Otevřete Náhled dat a zjistěte, zda máte požadovaná oprávnění. Pokud ano, druhý pohled spotřebovaný prostřednictvím dynamického skriptu SQL může mít použito řízení přístupu k datům (DAC).
#XMSG: deployment of DAC is deprecated and view need to be redeployed to be able to persist data
VIEW_PERSISTENCY_DEPRECATED_DAC=Pohled "{0}" je nasazen pomocí řízení přístupu k datům(DAC), které již zastarává. Nasaďte pohled znovu, abyste mohli uchovat data pro pohled.
##XFLD:Message used by SEAL repository for input parameter
START_VIEW_PERSISTENCY_INPUT_PARAMETER=Použití standardní hodnoty "{0}" pro vstupní parametr "{1}".
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Elastic compute node keys~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
##XFLD:Message used to reflect disablement of ecn replica
ECN_DISABLE_EXISTING_REPLICA=Replika elastického výpočetního uzlu je deaktivována.
##XFLD:Message used to reflect recreation of ecn replica
ECN_RECREATE_EXISTING_REPLICA=Replika elastického výpočetního uzlu je znovu vytvořena.
##XFLD:Message used to reflect re-enablement of ecn replica
ECN_ENABLE_EXISTING_REPLICA=Replika elastického výpočetního uzlu je znovu aktivována.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Task Schedule Menu key~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message to schedule task
scheduleText=Naplánovat
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Vytvořit časový plán
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Upravit časový plán
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Odstranit plán
#XFLD: Refresh frequency field
refreshFrequency=Frekvence aktualizací
#XFLD: Refresh frequency field
refreshFrequencyNew=Frekvence
#XFLD: Refresh frequency field
refreshFrequencyNewNew=Naplánovaná frekvence
#XBUT: label for None
none=Žádné
#XBUT: label for Real-Time replication state
realtime=V reálném čase
#XFLD: Label for table column
txtNextSchedule=Příští běh
#XFLD: Label for table column
txtNextScheduleNew=Naplánovaný příští běh
#XFLD: Label for table column
txtNumOfRecords=Počet záznamů
#XFLD: Label for scheduled link
scheduledTxt=Naplánováno
#XFLD: LABEL for partially persisted link
partiallyPersisted=Částečně trvale uloženo
#XFLD: Text for paused text
paused=Pozastaveno

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ View Monitor Tasklog Details Action keys~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message for release lock actionn
releaseLockMsg=Když trvá běh déle než obvykle, může to ukazovat, že byl neúspěšný a že status nebyl odpovídajícím způsobem aktualizován. \r\n Pro vyřešení tohoto problému můžete uvolnit blokování a nastavit jeho status na Neúspěšné.
#XFLD: Label for release lock dialog
releaseLockText=Uvolnit blokování

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Column header tooltip ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: tooltip for table column
txtViewTooltip=Název perzistentního pohledu
#XFLD: tooltip for table column
txtViewDataAccessTooltip=Toto ukazuje dostupnost pohledu
#XFLD: tooltip for table column
txtViewRefreshFreqTooltip=Toto ukazuje, zda je pro pohled definován časový plán
#XFLD: tooltip for table column
txtViewStatusTooltip=Načíst status perzistentního pohledu
#XFLD: tooltip for table column
txtViewLatestUpdateTooltip=Poskytuje informace o tom, kdy byl perzistentní pohled naposledy aktualizován
#XFLD: tooltip for table column
txtViewNextRunTooltip=Je-li pro pohled nastaven časový plán, podívejte se, na kdy je naplánován následující běh.
#XFLD: tooltip for table column
txtViewNumOfRecordsTooltip=Sledovat počet záznamů.
#XFLD: tooltip for table column
txtViewInMemorySizeTooltip=Sledovat, jakou velikost vaší paměti pohled využívá
#XFLD: tooltip for table column
txtViewDiskSizeTooltip=Sledovat, jakou velikost na vašem disku pohled využívá
#XMSG: Expired text
txtExpired=Platnost vypršela

#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Objekt ''{0}'' nelze přidat k řetězci úloh.

#~~~~~~~~~~~View Analyzer task log messages~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message indicating number of records for a view, and memory consumption in Mebibyte during simulation of view persistency
RECORDS_PEAK_MEMORY=Pohled ''{0}'' má {1} záznamy. Simulace perzistence dat pro tento pohled použila {2} MiB paměti.
#XMSG: View Analyzer execution failure
VIEW_ANALYZER_EXECUTION_FAILED=Provedení analyzátoru pohledu se nezdařilo.
#XMSG: Missing authorizations for View Analyzer
VIEW_ANALYZER_MISSING_AUTH=Chybí oprávnění pro analyzátor pohledu.
#XMSG: Maximum memory in Gibibyte is reached, therefore persistency simulation is stopped
MAXIMUM_MEMORY_REACHED=Bylo dosaženo maximální spotřeby paměti {0} GiB při simulaci perzistence dat pro pohled ''{1}''. Žádné další simulace perzistence pohledu proto nebudou spuštěny.
#XMSG: Error during persistency simulation occurred
PERSISTENCY_SIMULATION_ERROR=Při simulaci perzistence dat pro pohled ''{0}''‘ došlo k chybě.
#XMSG: Persistency simulation is not executed, because prerequisites are not fulfilled and the view cannot be persisted
PREREQUISITES_NOT_FULFILLED=Simulace perzistence dat není pro pohled ''{0}''‘ provedena, protože předpoklady nejsou splněny a pohled nelze učinit perzistentním.
#XMSG: Persistency simulation is not executed, because the view does not exist in the database
VIEW_DOES_NOT_EXIST=Abyste aktivovali simulaci perzistence dat, musíte nasadit pohled "{0}".
#XMSG: Number of records cannot be determined, because the local table does not exist in the database
TABLE_DOES_NOT_EXIST=Lokální tabulka "{0}" v databázi neexistuje, proto nelze pro tuto tabulku určit počet záznamů.
##XMSG: Message start of cancellation for View Analyzer task
CANCEL_VIEW_ANALYZER_TRIGGERED=Proces pro zastavení úlohy analyzátoru pohledu {0} pro pohled ''{1}'' byl odeslán. Do zastavení úlohy může dojít ke zpoždění.
##XMSG: Message start to indicate that task is not active
CANCEL_VIEW_ANALYZER_NO_ACTIVE_TASK=Úloha analyzátoru pohledu {0} pro pohled ''{1}'' není aktivní.
#XMSG: Cancel View Analyzer task failure
CANCEL_VIEW_ANALYZER_FAILED=Nezdařilo se zrušit úlohu analyzátoru pohledu.
##XMSG: Message used in View Analyzer execution task when task is canceled
VIEW_ANALYZER_CANCELED=Provedení analyzátoru pohledu ''{0}''‘ bylo zastaveno úlohou zrušení.
##XMSG: Message start of cancelation for Model Validation task
CANCEL_MODEL_VALIDATION_TRIGGERED=Proces pro zastavení úlohy ověření modelu {0} pro pohled "{1}" byl odeslán. Do zastavení úlohy může dojít ke zpoždění.
##XMSG: Message to indicate that task is not active
CANCEL_MODEL_VALIDATION_NO_ACTIVE_TASK=Úloha ověření modelu {0} pro pohled "{1}" není aktivní.
#XMSG: Cancel Model Validation task failure
CANCEL_MODEL_VALIDATION_FAILED=Nezdařilo se zrušit úlohu ověření modelu.
##XMSG: Message used in Model Validation task when task is canceled
MODEL_VALIDATION_CANCELED=Provedení ověření modelu pro pohled "{0}''‘ bylo zastaveno úlohou zrušení.
#XMSG: Message for Locked space error, before starting Model Validation task
MODEL_VALIDATION_LOCKED_SPACE_ERROR=Nelze provést ověření modelu pro pohled "{0}", protože prostor "{1}" je blokován.
##XMSG: Message used in View Analyzer execution task when determination of number of rows fails
NUMBER_ROWS_DETERMINATION_FAILED=Došlo k chybě při stanovení počtu řádek pro lokální tabulku ''{0}''.
##XMSG: Message to indicate that SQL plan was created
EXECUTION_PLAN_CREATED=Soubor plánu analyzátoru SQL pro pohled ''{0}'' je vytvořen a lze ho stáhnout.
#XFLD: Message to indicate starting of process to generate SQL Analyzer plan file
START_PLANVIZ_EXECUTION=Zahájení procesu pro generování souboru plánu analyzátoru SQL pro pohled ''{0}''.
#XFLD: Message to indicate starting of View Analyzer process
VIEW_ANALYZER_EXECUTION_STARTED=Spustit provedení analyzátor pohledu.
##XMSG: Message to indicate that SQL plan file cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_PLANVIZ_NOT_FULFILLED=Soubor plánu analyzátoru SQL nelze vygenerovat pro pohled ''{0}'', protože nejsou splněny předpoklady perzistence dat.`
##XMSG: Error message during  generation of SQL Analyzer plan file
PLANVIZ_ERROR=Při generování souboru plánu analyzátoru SQL pro pohled ''{0}''‘ došlo k chybě.
#XMSG: Message for Locked space error
VIEW_ANALYZER_LOCKED_SPACE_ERROR=Nelze provést analyzátor pohledu pro pohled ''{0}'', protože prostor ''{1}'' je blokován.
#XMSG: Message for persistency simulation without partitioning
PERSISTENCY_SIMULATION_WITHOUT_PARTITIONS=Oddíly pohledu ''{0}'' nejsou při simulaci perzistence pohledu zohledněny.
#XMSG: Message for generation of SQL Analyzer plan file without considering partitioning
PLANVIZ_WITHOUT_PARTITIONS=Oddíly pohledu ''{0}'' nejsou při generování souboru plánu analyzátoru SQL zohledněny.
#XMSG: Confirmation message for mass removal of persisted data
MassRemovePersistency_Confirm_Msg=Chcete odebrat perzistentní data a přepnout přístup k datům zpět na virtuální přístup?
#XMSG: Confirmation message for partial mass removal of selected persisted data
PartialMassRemovePersistency_Confirm_Msg={0} z {1} vybraných pohledů mají perzistentní data. \n Chcete odebrat perzistentní data a přepnout přístup k datům zpět na virtuální přístup?
#XMSG: success message for stopping mass persistence
stopMassPersistenceSuccess=Odebíráme perzistentní data pro vybrané pohledy.
#XMSG: error message during mass stop persistence of a view (backend error)
STOP_MASS_PERSISTENCE_ERROR=Při zastavování perzistence pro vybrané pohledy došlo k chybě.
#XMSG: memory analysis is only performed in space {0}
NO_MEMORY_ANALYSIS=Analýza paměti se provádí jen pro entity v prostoru "{0}": "{1}" "{2}" byly přeskočeny.
##XMSG: Message to indicate that Explain Plan cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_EXPLAINPLAN_NOT_FULFILLED=Soubor vysvětlení plánu nelze vygenerovat pro pohled "{0}", protože nejsou splněny předpoklady perzistence.
#XMSG: Message for Explain Plan generation without partitioning
EXPLAINPLAN_WITHOUT_PARTITIONS=Oddíly pohledu "{0}" nejsou při generování souboru vysvětlení plánu zohledněny.
#XFLD: Message to indicate starting of process to generate Explain Plan file
START_EXPLAINPLAN_EXECUTION=Zahájení procesu pro generování souboru vysvětlení plánu pro pohled "{0}".
##XMSG: Message to indicate that Explain Plan was created
EXPLAINPLAN_CREATED=Soubor vysvětlení plánu pro pohled "{0}" byl vygenerován. Můžete ho vygenerovat kliknutím na "Zobrazit detaily" nebo ho stáhnout, pokud máte relevantní oprávnění.
##XMSG: Error message during  generation of Explain Plan file
EXPLAINPLAN_ERROR=Při generování souboru vysvětlení plánu pro pohled ''{0}''‘ došlo k chybě.
##XMSG: Explain Plan file cannot be generated for the view {0}, because too many views are stacked on each other
EXPLAINPLAN_MODEL_TOO_COMPLEX=Soubor vysvětlení plánu nelze vygenerovat pro pohled "{0}". Je příliš mnoho na sobě navrstvených pohledů. Komplexní modely mohou způsobit chyby z nedostatku paměti a pomalý výkon. Doporučujeme zachovat pohled.

##XMSG: Message to indicate that the space is locked
PERFORMANCE_ANALYSIS_LOCKED_SPACE_ERROR=Nelze provést analyzátor pohledu pro pohled "{0}", protože prostor "{1}" je blokován.
##XMSG: The performance analysis task was cancelled
PERFORMANCE_ANALYSIS_CANCELED=Analýza výkonu pro pohled "{0}" byla zrušena.
##XMSG: The performance analysis failed
PERFORMANCE_ANALYSIS_FAILED=Analýza výkonu byla neúspěšná.
##XMSG: The performance analysis is finished
PERFORMANCE_ANALYSIS_FINISHED=Analýza výkonu pro pohled "{0}" byla dokončena. Kliknutím na "Zobrazit detaily" zobrazíte výsledek.
##XMSG: The view has input parameter with no default value
INPUT_PARAMETER_WITHOUT_DEFAULT_VALUE=Tento pohled nelze analyzovat, protože má parametr bez standardní hodnoty.
##XMSG: The view is not fully deployed
VIEW_NOT_FULLY_DEPLOYED=Tento pohled nelze analyzovat, protože není kompletně nasazen.
##XMSG: The view uses limited remote adapters
LIMITED_REMOTE_ADAPTER=Tento pohled používá alespoň jeden vzdálený adaptér s limitovanými funkčnostmi, jako například chybějící filtr pushdown nebo podpora pro 'Počet'. Perzistence nebo replikace objektů může zlepšit výkon doby běhu.
##XMSG: The view uses remote adapter that does not support 'Limit'
REMOTE_ADAPTER_NOT_SUPPORTING_LIMIT=Tento pohled používá alespoň jeden vzdálený adaptér, který nepodporuje 'Limit'. Mohlo by být vybráno více než 1000 záznamů. 
##XMSG: Performance analysis is executed by using default parameters of view'
INPUT_PARAMETER_WITH_DEFAULT_VALUE=Analýza výkonu je provedena pomocí standardních hodnot parametrů pohledu.
##XMSG: Message to indicate that an error occurred
PERFORMANCE_ANALYSIS_ERROR=Při analýze výkonu pro pohled "{0}" došlo k chybě.
##XMSG: Message start of cancellation for Performance Analysis task
CANCEL_PERFORMANCE_ANALYSIS_TRIGGERED=Proces pro zastavení úlohy analyzátoru pohledu {0} pro pohled ''{1}'' byl odeslán. Do zastavení úlohy může dojít ke zpoždění.
##XMSG: Message start to indicate that task is not active
CANCEL_PERFORMANCE_ANALYSIS_NO_ACTIVE_TASK=Úloha analyzátoru pohledu {0} pro pohled "{1}" není aktivní.
#XMSG: Cancel Performance Analysis task failure
CANCEL_PERFORMANCE_ANALYSIS_FAILED=Nezdařilo se zrušit úlohu Analýza výkonu.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Přiřadit plán mně
#XBUT: Pause schedule menu label
pauseScheduleLabel=Pozastavit plán
#XBUT: Resume schedule menu label
resumeScheduleLabel=Obnovit plán
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Při odebírání plánů došlo k chybě.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Při přiřazování plánů došlo k chybě.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Při pozastavování plánů došlo k chybě.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Při obnovování plánů došlo k chybě.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Odstraňování {0} plánů
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Změna vlastníka {0} plánů
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Pozastavování {0} plánů
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Obnovování {0} plánů
#XBUT: Select Columns Button
selectColumnsBtn=Vybrat sloupce
#XFLD: Refresh tooltip
TEXT_REFRESH=Aktualizovat
#XFLD: Select Columns tooltip
text_selectColumns=Vybrat sloupce


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Runtime Metrics ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Label for Runtime Metrics
RuntimeMetricsTitle=Metriky doby běhu pro
#XFLD : Label for Run Button
runButton=Spustit
#XFLD : Label for Cancel Button
cancelButton=Zrušit
#XFLD : Label for Close Button
closeButton=Zavřít
#XFLD : Label for Open View Analyzer Button
openViewAnalyzerButton=Otevřít analyzátor pohledu
#XFLD : Label for Generate Explain Plan Button
generateExplainPlanButton=Generovat vysvětlení plánu
#XFLD : Label for Previous Run Column
previousRun=Předchozí běh
#XFLD : Label for Latest Run Column
latestRun=Nejnovější běh
#XFLD : Label for time Column
time=Čas
#XFLD : Label for Duration Column
duration=Trvání
#XFLD : Label for Peak Memory Column
peakMemory=Paměťová špička
#XFLD : Label for Number of Rows
numberOfRows=Počet řádek
#XFLD : Label for Overall Number of Sources
overallNumberOfSources=Celkový počet zdrojů
#XFLD : Label for Data Access Column
dataAccess=Přístup k datům
#XFLD : Label for Local Tables
localTables=Lokální tabulky
#XFLD : Label for federatedRemoteTables
federatedRemoteTables=Federované vzdálené tabulky (s omezenými funkčnostmi adaptéru)
#XTXT Text for initial state of the runtime metrics
initialState=Pro načtení metrik musíte nejprve spustit analýzu výkonu. To může trvat nějakou dobu, ale v případě potřeby můžete tento proces zrušit.
#XMSG: Cancel confirmation message
CancelRunConfirmationMessage=Opravdu chcete zrušit aktuální běh analýzy výkonu?
#XTIT: Cancel dialog title
CancelRunTitle=Zrušit běh
#XFLD: Label for Number of Rows
NUMBER_ROWS=Počet řádek
#XFLD: Label for Overall Number of Sources
OVERALL_NUMBER_SOURCES=Celkový počet zdrojů
#XFLD: Label for Data Access
DATA_ACCESS=Přístup k datům
#XFLD: Label for Remote tables
NUMBER_REMOTE_TABLES_LIMITED_ADAPTERS=Federované vzdálené tabulky (s omezenými funkčnostmi adaptéru)
#XFLD: Label for select statement
SELECT_STATEMENT='SELECT * FROM VIEW LIMIT 1000'
#XFLD: Label for duration
SELECT_RUNTIME=Trvání
#XFLD: Label for peak memory
SELECT_PEAK_MEMORY=Paměťová špička
#XFLD: Label for count statement
SELECT_COUNT_STATEMENT='SELECT COUNT(*) FROM VIEW'
#XFLD: Label for count duration
SELECT_COUNT_RUNTIME=Trvání
#XFLD: Label for count peak memory
SELECT_COUNT_PEAK_MEMORY=Paměťová špička
#XFLD: Label for count of number of local tables
NUMBER_LOCAL_TABLES_FILE=Lokální tabulky (soubor)
#XTXT: Text for running state of the runtime metrics
Running=Probíhá...
#XFLD: Label for time
Time=Čas
#XFLD: Label for virtual access
PA_VIRTUAL=Virtuální
#XFLD: Label for persisted access
PA_PERSISTED=Trvale uloženo
PA_PARTIALLY_PERSISTED=Částečně trvale uloženo
#XTXT: Text for cancel
CancelRunSuccessMessage=Zrušení běhu analýzy výkonu.
#XTXT: Text for cancel error
CancelRunErrorMessage=Při zrušení běhu analýzy výkonu došlo k chybě.
#XTXT: Text for explain plan generation
ExplainPlanStarted=Generování vysvětlení plánu pro pohled "{0}".
#XTXT: Text for performance analysis
PerformanceAnalysisStarted=Zahájení analýzy výkonu pro pohled "{0}".
#XTXT: Text for performance analysis error
ErrorFetchingPerformanceAnalysis=Při načítání dat analýzy výkonu došlo k chybě.
#XTXT: Text for performance analysis error
conflictingTask=Úloha analýzy výkonu již probíhá
#XFLD: Label for Errors
Errors=Chyba(y)
#XFLD: Label for Warnings
Warnings=Upozornění
#XFLD: Tooltip for missing privileges
openViewAnalyzerButtonTooltipNoPrivilege=Pro otevření analyzátoru pohledu potřebujete oprávnění DWC_DATAINTEGRATION (aktualizace).
#XFLD: Tooltip for missing privileges
generateExplainPlanButtonTooltipNoPrivilege=Pro generování vysvětlení plánu potřebujete oprávnění DWC_RUNTIME (čtení).



#XFLD: Label for frequency column
everyLabel=Každé
#XFLD: Plural Recurrence text for Hour
hoursLabel=Hodiny
#XFLD: Plural Recurrence text for Day
daysLabel=Dny
#XFLD: Plural Recurrence text for Month
monthsLabel=Měsíce
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minuty
