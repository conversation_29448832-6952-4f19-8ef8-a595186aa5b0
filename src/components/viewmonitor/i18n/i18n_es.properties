
#XBCB: Breadcrumb
viewMonitorBreadcrumb={spaceId}

#XFLD: Label for persisted view column
SOURCE=Fuente
#XFLD: Label for persisted view column
NAME=Nombre
#XFLD: Label for persisted view column
NAME_LABEL=Nombre empresarial
#XFLD: Label for persisted view column
NAME_LABELNew=Objeto (nombre empresarial)
#XFLD: Label for persisted view column
TECHINCAL_NAME=Nombre técnico
#XFLD: Label for persisted view column
TECHINCAL_NAMENew=Objeto (nombre técnico)
#XFLD: Label for persisted view column
DATA_PERSISTENCY=Acceso a datos
#XFLD: Label for persisted view column
STATUS=Estado
#XFLD: Label for persisted view column
LAST_UPDATED=Última actualización
#XFLD: Label for persisted view column
IN_MEMORY_SIZE=Memoria utilizada para el almacenamiento (MiB)
#XFLD: Label for persisted view column
DISK_SIZE=Disco utilizado para el almacenamiento (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_NEWW=Tamaño en memoria (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_HEADER=Tamaño en memoria
#XFLD: Label for persisted view column
DISK_SIZE_NEWW=Tamaño en disco (MiB)
#XFLD: Label for persisted view column
DISK_SIZE_HEADER=Tamaño en disco
#XFLD: Label for schedule owner column
txtScheduleOwner=Propietario de la programación
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Muestra quién ha creado la programación
#XFLD: Label for persisted view column
PERSISTED=Guardado de forma persistente
#XFLD: Label for persisted view column
TYPE=Tipo
#XFLD: Label for View Selection Dialog column
changedOn=Modificado el
#XFLD: Label for View Selection Dialog column
createdBy=Creado por
#XFLD: Label for log details column
txtViewPersistencyLogs=Ver logs
#XFLD: Label for log details column
txtViewPersistencyLogsNew=Detalles
#XFLD: text for values shown for Ascending sort order
SortInAsc=Orden ascendente
#XFLD: text for values shown for Descending sort order
SortInDesc=Orden descendente
#XFLD: Label for persisted view monitor heading
ViewMonitor_Heading=Monitor de vistas
#XFLD: Label for persisted view monitor sub-heading
ViewMonitor_SubHeading=Supervisar y actualizar persistencia de datos de las vistas


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ DynamicPageHeader ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: text for values shown in column Persistence Status
txtLoading=Cargando
#XFLD: text for values shown in column Persistence Status
txtRunning=En ejecución
#XFLD: text for values shown in column Persistence Status
txtAvailable=Disponible
#XFLD: text for values shown in column Persistence Status
txtError=Error
#XMSG: Error message for unknown Persistence type that gets logged in Kibana
unknownReplicationTypeError=El tipo de replicación ''{0}'' no es compatible.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Content ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Opciones utilizadas para la última ejecución de persistencia de datos:
#XMSG: Message for input parameter name
inputParameterLabel=Parámetro de entrada
#XMSG: Message for input parameter value
inputParameterValueLabel=Valor
#XMSG: Message for persisted data
inputParameterPersistedLabel=Hora de persistencia
#XTIT: Count for View in ViewMonitor Toolbar
txtViewCount=Vistas ({0})
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Persistencia de vista
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Persistencia de datos
#XBUT: Button to clear the selected view persistance
ClearViewPersistance=Borrar
#XBUT: Button to stop the selected view persistance
stopPersistance=Detener persistencia
#XFLD: Placeholder for Search field
txtSearch=Buscar
#XBUT: Tooltip for refresh button
txtRefresh=Actualizar
#XBUT: Tooltip for add view button
txtDeleteView=Eliminar persistencia
#XBUT: Tooltip for load new peristence
loadNewPersistence=Reiniciar persistencia
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Cargar nueva instantánea
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Iniciar persistencia de datos
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Quitar datos guardados de forma persistente
#XMSG: success message for starting persistence
startPersistenceSuccess=Estamos guardando la vista ''{0}'' de forma persistente.
#XMSG: success message for stopping persistence
stopPersistenceSuccess=Estamos quitando datos guardados de forma persistente de la vista ''{0}''.
#XMSG: success message for stopping persistence
deletePersistedViewSuccess=Estamos quitando la vista ''{0}'' de la lista de supervisión.
#XMSG: error message during start persistence of a view (backend error)
ERROR_STARTING_PERSISTENCE=Se ha producido un error al iniciar la persistencia de datos de la vista ''{0}''.
#XMSG: error message during start persistence of a view (backend error)
PARAMERROR_STARTING_PERSISTENCE=La vista "{0}" no se puede guardar de forma persistente porque contiene parámetros de entrada.
#XMSG: error message during start persistence of a view (backend error)
NUMBER_OF_PARAMETER_STARTING_PERSISTENCE=La vista "{0}" no se puede guardar de forma persistente porque contiene más de un parámetro de entrada.
#XMSG: error message during start persistence of a view (backend error)
PARAMETER_DEFAULT_VALUE_STARTING_PERSISTENCE=La vista "{0}" no se puede guardar de forma persistente porque no contiene un valor predeterminado.
#XMSG: error message during start persistence of a view (backend error)
REDEPLOYMENT_DAC_REQUIRED=Debe volver a desplegar el control de acceso a datos (DAC) "{0}" para admitir la persistencia de datos.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_STARTING_PERSISTENCE=La vista ''{0}'' no se puede guardar de forma persistente porque utiliza la vista ''{1}'', que tiene control de acceso a datos (DAC).
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_EXTERNAL_STARTING_PERSISTENCE=La vista ''{0}'' no se puede guardar de forma persistente porque utiliza una vista con control de acceso a datos (DAC) que pertenece a un espacio diferente.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_STRUCTURE_STARTING_PERSISTENCE=La vista ''{0}'' no se puede guardar de forma persistente porque la estructura de uno o más de sus controles de acceso a datos (DAC) no admite guardar los datos de forma persistente.
#XMSG: error message during stop persistence of a view (backend error)
STOP_PERSISTENCE_ERROR=Se ha producido un error al detener la persistencia de la vista ''{0}''.
#XMSG: error message during stop persistence of a view (backend error)
DELETE_PERSISTED_VIEW_ERROR=Se ha producido un error al eliminar la vista ''{0}'' guardada de forma persistente.
#XMSG: Warning message for removing the persisted data
RemovePersistency_Confirm_Msg=¿Desea eliminar los datos guardados de forma persistente y cambiar al acceso virtual de la vista ''{0}''?
#XMSG: Warning message for delete the view persistence
DeletePersistency_Confirm_Msg=¿Desea quitar la vista de la lista de supervisión y eliminar los datos guardados de forma persistente de la vista ''{0}''?
#XMSG: error message for reading data from backend
txtReadBackendError=Parece que se ha producido un error al leer desde el back end.
#XFLD: Label for No Data Error
NoDataError=Error
#XMSG: message for conflicting task
Task_Already_Running=Ya hay en ejecución una tarea en conflicto para la vista "{0}".

#XFLD: Error while accessing pertitioning for a view
missingPrivilegeInViewPersistency=No tiene suficientes permisos para ejecutar la partición para la vista ''{0}''

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Show All Views~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Text for show all views button
allViewsText=Todas las vistas ({0})
#XBUT: Text for show scheduled views button
scheduledText=Programadas ({0})
#XBUT: Text for show persisted views button
persistedText=Guardadas de forma persistente ({0})
#XBUT: Text for start analyzer button
startAnalyzer=Iniciar el Analizador de vistas
#XFLD: Message if repository is unavailable
repositoryErrorMsg=El repositorio no está disponible y determinadas funciones se han desactivado.

#XFLD: Data Access - Virtual
Virtual=Virtual
#XFLD: Data Access - Persisted
Persisted=Guardada de forma persistente

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Non-Persisted View Selection Dialog~~~~~~~~~~~~~~~~~~~~~~~

#XTIT: Title for Non-Persisted View selection dialog
select_source=Seleccione la vista para guardar de forma persistente

#XTIT: Placeholder for Non-Persisted View selection dialog search
searchView=Buscar vistas
#XTIT: No data in the list of non-persisted view
No_Data=Sin datos
#XBUT: Button to select non-persisted view
ok=OK
#XBUT: Button to close the non-persisted views selection dialog
cancel=Cancelar

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~View persistency log message key~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message for view persistency logs
START_STOP_PERSISTENCY=Iniciando ejecución de tarea de persistencia de datos para "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY=Guardando los datos de forma persistente para la vista "{1}"...
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY)
START_VIEW_PERSISTENCY_PROCESS=Iniciando el proceso para guardar los datos de forma persistente para la vista "{0}".
#XFLD: Message for view persistency manual partition load
START_VIEW_PERSISTENCY_PROCESS_MANUAL_PARTITIONS=Iniciando el proceso para guardar los datos de forma persistente para la vista ''{0}'' con los ID de partición seleccionados: ''{1}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY=Quitando datos guardados de forma persistente para la vista ''{1}''.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY)
REMOVE_VIEW_PERSISTENCY=Iniciando el proceso para quitar los datos guardados de forma persistente para la vista "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_SUCCESS=Se han guardado los datos de forma persistente para la vista "{1}".
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_SUCCESS)
START_VIEW_PERSISTENCY_PROCESS_SUCCESS=Se han guardado los datos de forma persistente para la vista "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY_SUCCESS=Se han quitado los datos guardados de forma persistente y se ha restaurado el acceso virtual a los datos para la vista "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_SUCCESS)
REMOVE_VIEW_PERSISTENCY_SUCCESS=Se ha completado el proceso para quitar los datos guardados de forma persistente para la vista "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_FAILED=No se han podido guardar los datos de forma persistente de para vista "{1}".
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_FAILED)
START_VIEW_PERSISTENCY_PROCESS_FAILED=No se han podido guardar los datos de forma persistente de para vista "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.05)
STOP_VIEW_PERSISTENCY_FAILED=No se han podido quitar los datos guardados de forma persistente de la vista ''{1}''.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_FAILED)
REMOVE_VIEW_PERSISTENCY_FAILED=No se han podido quitar los datos guardados de forma persistente de la vista ''{0}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_SUCCESS=Se han guardado de forma persistente {3} registros para la vista "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace START_PERSISTENCY_RECORD_COUNT_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=Se han insertado {0} registros en la tabla de persistencia de datos para la vista "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS_V2=Se han insertado {0} registros en la tabla de persistencia de datos para la vista "{1}". Memoria utilizada: {2} GiB.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_SUCCESS=Se han eliminado {3} registros guardados de forma persistente para la vista "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace STOP_PERSISTENCY_RECORD_COUNT_SUCCESS)
REMOVE_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=Se han quitado los datos guardados de forma persistente y se han eliminado los registros ''{0}'' guardados de forma persistente.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_FAILED=No se ha podido obtener recordCount para la vista ''{1}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_FAILED=No se ha podido obtener recordCount para la vista ''{1}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Se ha eliminado la programación de "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS)
REMOVE_VIEW_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Se ha eliminado la programación para la vista ''{0}''.
#XFLD: Message for view persistency logs
STOP_PERSISTENCY_DELETE_SCHEDULE_FAILED=No se ha podido eliminar la programación de "{1}".
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_REDEPLOY=No se puede guardar de forma persistente la vista "{0}" porque se ha sido modificado y desplegado desde que comenzó a guardarla de forma persistente. Intente guardarla de nuevo o espere hasta la próxima ejecución programada.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_DELETE=Las vista "{0}" no se puede guardar de forma persistente porque se ha eliminado desde que comenzó a guardarla de forma persistente.
#XFLD: Message for successful data persistency of a single partition (for messages created before version 2022.03)
START_PERSISTENCY_SINGLE_PARTITION_SUCCESS=Se han guardado {0} registros de forma persistente en la partición para valores ''{1}'' <= {2} < ''{3}'' .
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (new message to replace START_PERSISTENCY_SINGLE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS=Se han insertado {0} registros en la partición para valores ''{1}'' <= {2} < ''{3}''.
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS_V2=Se han insertado {0} registros en la partición para los valores "{1}" <= "{2}" < "{3}". Memoria utilizada: {4} GiB.
#XFLD: Message for successful replication of a "others" partition (for messages created before version 2022.03)
START_PERSISTENCY_OTHERS_PARTITION_SUCCESS=Se han guardado de forma persistente {0} registros en la partición "Otros".
#XFLD: Message used by SEAL repository for successful replication of a "others" partition (new message to replace START_PERSISTENCY_OTHERS_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_OTHERS_PARTITION_SUCCESS=Se han insertado {0} registros en la partición "''Otros".
#XFLD: Message for view persistency record count of partitions (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS=Se han guardado {3} registros de forma persistente para la vista ''{1}'' in {4} particiones.
#XFLD: Message used by SEAL repository for view persistency record count of partitions (new message to replace START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS=Se han insertado {0} registros en la tabla de persistencia de datos para la vista "{1}" en "{2}" particiones.
#XFLD: Message used by SEAL repository for view persistency for partition wise refresh
START_VIEW_PERSISTENCY_RECORD_COUNT_REFRESHED_PARTITIONS_SUCCESS=Se han insertado {0} registros en la tabla de persistencia de datos para la vista ''{1}''. Particiones actualizadas:{2} Particiones bloqueadas:{3} Particiones en total:{4}
#XFLD: Message used by SEAL repository for view persistency for manual partition load
START_VIEW_PERSISTENCY_RECORD_COUNT_MANUAL_PARTITIONS_SUCCESS=Se han insertado {0} registros en la tabla de persistencia de datos para la vista "{1}" en "{2}" particiones seleccionadas.
#XFLD: Message used by SEAL repository for view persistency for locked partition data load
START_VIEW_PERSISTENCY_RECORD_COUNT_LOCKED_PARTITIONS_SUCCESS=Se han insertado {0} registros en la tabla de persistencia de datos para la vista ''{1}''. Particiones actualizadas:{2}; bloqueadas, particiones no modificadas: {3}; Particiones en total:{4}
#XMSG: Message for unexpected error (for messages created before version 2022.03)
START_PERSISTENCY_UNEXPECTED_ERROR=Se ha producido un error inesperado al guardar datos de forma persistente para la vista "{0}".
#XMSG: Message for unexpected error (new message to replace START_PERSISTENCY_UNEXPECTED_ERROR)
START_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Se ha producido un error inesperado al guardar datos de forma persistente para la vista "{0}".
#XMSG: Message for Locked space error
START_VIEW_PERSISTENCY_LOCKED_SPACE_ERROR=La vista "{0}" no se puede guardar de forma persistente porque el espacio "{1}" está bloqueado.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
STOP_PERSISTENCY_UNEXPECTED_ERROR=Se ha producido un error inesperado al quitar datos guardados de forma persistente para la vista "{0}".
#XMSG: Message for unexpected error (new message to replace STOP_PERSISTENCY_UNEXPECTED_ERROR)
REMOVE_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Se ha producido un error inesperado al quitar la persistencia para la vista "{0}".
#XMSG: Message for ivalidated view
VIEW_PERSISTENCY_ABORTED_DEFINITION_INVALID=La definición de la vista "{0}" ha dejado de ser válida, probablemente por una modificación de un objeto consumido directa o indirectamente por la vista. Pruebe a volver a desplegar la vista para solucionar el problema o identifique la causa principal.
#XMSG: Message for persisted data removal (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT=Los datos guardados de forma persistente se han quitado al desplegar la vista "{0}".
#XMSG: Message for persisted data removal of a consuming view
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW=Los datos guardados de forma persistente se han quitado al desplegar la vista utilizada "{0}".
#XMSG: Message for persisted data removal of a consuming view due to a dac change
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW_DAC_CHANGED=Se han quitado los datos guardados de forma persistente mientras se desplegaba la vista consumida "{0}" porque se ha modificado su control de acceso a datos.
#XMSG: Message for persisted data removal (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DEPLOYMENT=Los datos guardados de forma persistente se han quitado al desplegar la vista "{0}".
#XMSG: Message for deleted view (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION=Se ha quitado la persistencia con la eliminación de la vista "{0}".
#XMSG: Message for deleted view (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DELETION=Se ha quitado la persistencia con la eliminación de la vista "{0}".
#XMSG Message for removal of persisted data, because prerequisites not fulfilled
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_PREREQUISITES_NOT_FULFILLED=Se van a eliminar los datos persistidos porque ya no se cumplen los requisitos previos de persistencia de datos.
#XMSG: Message for inconsistent persistency
VIEW_PERSISTENCY_INCONSISTENT_PERSISTENCY=La persistencia de la vista "{0}" se ha vuelto inconsistente. Quite los datos persistidos para resolver el problema.
#XMSG: Message for prerequisites
START_PERSISTENCY_CHECK_PREREQUISITES=Comprobando los requisitos previos para la persistencia de la vista ''{0}''.
#XMSG: Message for DAC deployed in deprecated way using table function and join (for messages created before 2025.05)
START_PERSISTENCY_DEPRECIATED_DAC=La vista "{0}" se ha desplegado mediante el control de acceso a datos (CAD) que va a quedar obsoleto. Vuelva a desplegar la vista para mejorar el rendimiento.
#XMSG: Message for DAC deployed in deprecated way using table function and join
START_PERSISTENCY_DEPRECATED_DAC=La vista "{0}" se ha desplegado mediante el control de acceso a datos (CAD) que va a quedar obsoleto. Vuelva a desplegar la vista para mejorar el rendimiento.
#XMSG: Message for roll back (for messages created before version 2022.03)
ROLLBACK_VIEW_PERSISTENCY=Se ha producido un error. El estado anterior de la persistencia restaurada para la vista es ''{0}''.
#XMSG: Message used by SEAL repository for rollback of running procedure to persist view (new message to replace ROLLBACK_VIEW_PERSISTENCY)
VIEW_PERSISTENCY_ROLLBACK=Se ha producido un error. Se ha detenido el proceso para guardar la vista ''{0}'' de forma persistente y se han revertido las modificaciones.
#XMSG: Message used by SEAL repository for rollback of running procedure to remove persisted data of view
REMOVE_VIEW_PERSISTENCY_ROLLBACK=Se ha producido un error. Se ha detenido el proceso para quitar los datos guardados de forma persistente de la vista ''{0}'' y se han revertido las modificaciones.
#XMSG: Message used by SEAL repository for persistency preparation
START_PERSISTENCY_PREPARATION=En preparación para guardar los datos de forma persistente.
#XMSG: Message used by SEAL repository for data insertion
START_INSERTING_PERSISTENCY_DATA=Insertando datos en la tabla de persistencia.
#XMSG: Message for null values - partitioning (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS=Se han insertado {0} registros de valor nulo en la partición "Otros".
#XMSG: Message used by SEAL repository for others partition values - partitioning (new message to replace START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS=Se han insertado {0} registros en la partición ''otros'' para valores ''{2}'' < ''{1}'' OR ''{2}'' >= ''{3}''.
#XMSG: Message used by SEAL repository for others partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS_V2=Se han insertado {0} registros en la partición "otros" para los valores "{2}" < "{1}" OR "{2}" >= "{3}". Memoria utilizada: {4} GiB.
#XMSG: Message used by SEAL repository for null partition values - partitioning
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS=Los {0} registros insertados en la partición ''otros'' para valores ''{1}'' son NULL.
#XMSG: Message used by SEAL repository for null partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS_V2=Se han insertado {0} registros en la partición "otros" para los valores "{1}" IS NULL. Memoria utilizada: {2} GiB.
##XFLD:Message used by SEAL repository for remote statistics
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS=Cargando los datos implicados: {0} sentencias remotas. Registros totales obtenidos: {1}. Tiempo de duración total: {2} segundos.
##XFLD:Message used by SEAL repository for remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS_WITH_PARTITIONS=Cargando los datos implicados con {0} particiones y {1} sentencias remotas. Registros totales obtenidos: {2}. Tiempo de duración total: {3} segundos.
##XFLD:Message used by SEAL repository with UI ID for instructions of remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_INSTRUCTION=Las sentencias remotas procesadas durante la ejecución se pueden mostrar abriendo el monitor de consultas remotas, en los detalles de los mensajes específicos de la partición.
##XFLD:Message for start reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS=Iniciando el proceso para reutilizar los datos existentes guardados de forma persistente para ver {0} después del despliegue.
##XFLD:Message used by SEAL repository for reattach view persitency
START_VIEW_PERSISTENCY_REATTACHMENT=Comience a reutilizar los datos existentes guardados de forma persistente.
##XFLD:Message used by SEAL repository for reattach view persitency
START_FINILIZING_VIEW_PERSISTENCY_REATTACHMENT=Reutilizando los datos existentes guardados de forma persistente.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_COMPLETE=El proceso para reutilizar datos existentes guardados de forma persistente ha finalizado para la vista {0}.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_FAILED=No se han podido reutilizar los datos existentes guardados de forma persistente para la vista {0} después del despliegue.
#XMSG: Message used by SEAL repository for finalizing persistency
START_FINALIZING_PERSISTENCY=Finalizando la persistencia.
#XMSG: Message used by SEAL repository for restoring virtual access for view
REMOVE_VIEW_PERSISTENCY_VIRTUAL_ACCESS_RESTORED=Se ha restaurado el acceso a datos virtual para la vista "{0}".
#XMSG: Message used by SEAL repository for view that already has virtual access
REMOVE_VIEW_PERSISTENCY_ALREADY_VIRTUAL_ACCESS=La vista "{0}" ya tiene acceso virtual a los datos. No se ha quitado ningún dato guardado de forma persistente.
#XMSG: Message for removing view persistency
VIEW_PERSISTENCY_REMOVED_FROM_MONITOR=Se ha quitado la vista ''{0}'' del monitor de persistencia de vistas.
#XMSG: Message for view doesnt exist.
VIEW_PERSISTENCY_VIEW_NOT_EXISTING=La vista "{0}" no existe en la base de datos o no se ha desplegado correctamente, por lo tanto, no se puede guardar de forma persistente. Vuelva a desplegar la vista para intentar solucionar el problema o averigüe cuál es la causa principal.
#XMSG: Message for persistency not enabled.
VIEW_PERSISTENCY_NOT_ENABLED=La persistencia de vistas no está activada. Vuelva a desplegar una tabla/vista en el espacio ''{0}'' para activar la funcionalidad.
#XMSG: Message for unexpected error due to node crash
VIEW_PERSISTENCY_UNEXPECTED_ERROR_GENERAL=Se ha interrumpido la última ejecución de persistencia de vista debido a errores técnicos.
#XFLD: Message for view persistency memory consumption
VIEW_PERSISTENCY_MEMORY_CONSUMPTION={0} GiB de memoria máxima utilizada en el tiempo de ejecución de persistencia de vista.
#XFLD: Message for view persistency timeout
VIEW_PERSISTENCY_TIMEOUT=La persistencia de la vista {0} ha agotado el tiempo de espera de {1} horas.
#XFLD: Message for not started asynchronous view persistency
VIEW_PERSISTENCY_ASYNC_NOT_STARTED=La carga del sistema alta ha impedido que la ejecución asincrónica de la persistencia de vistas se inicie. Compruebe si se están ejecutando en paralelo demasiadas tareas.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency (for messages created before version 2022.05)
START_VIEW_PERSISTENCY_EXECUTION_METHOD_DROP_AND_RENAME=Se ha eliminado la tabla de persistencia existente y se ha sustituido por una tabla de persistencia nueva.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_RECREATE_AND_DROP=Se ha eliminado la tabla de persistencia existente y se ha sustituido por una tabla de persistencia nueva.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_TRUNCATE_AND_COPY=Se ha actualizado la tabla de persistencia existente con nuevos datos.
##XFLD: Message for missing authorizations for view persistency
VIEW_PERSISTENCY_MISSING_AUTH=Faltan autorizaciones para la persistencia de vistas.
##XFLD: Message start of persistency cancellation
VIEW_PERSISTENCY_TRIGGER_CANCEL=Se está iniciando la cancelación del proceso para guardar de forma persistente la vista {0}.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_UNAVAILABLE_TASK_RUN=No se ha podido cancelar el proceso para guardar de forma persistente la vista porque no hay ninguna tarea de persistencia en ejecución para la vista {0}.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_NONDETERMINISTIC_TASK_RUN=No se ha podido cancelar el proceso para guardar de forma persistente la vista porque no se está ejecutando ninguna tarea de persistencia para la vista {0}.
##XFLD: Message for canceling a non running persitency task
VIEW_PERSISTENCY_CANCEL_NON_RUNNING_SELECTED_TASK=No se ha podido cancelar el proceso para guardar la vista {0} de forma persistente porque la tarea de persistencia seleccionada {1} no se está ejecutando.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_UNREACHABLE_PERSISTENCY_RUN=No se ha podido cancelar el proceso para guardar de forma persistente la vista porque aún no ha empezado la persistencia de datos para la vista {0}.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_COMPLETED_PERSISTENCY_RUN=No se ha podido cancelar el proceso para guardar de forma persistente la vista {0} porque ya se ha completado.
##XFLD: Message for failed cancellation
VIEW_PERSISTENCY_ABORTED_CANCEL_EXECUTION=No se ha podido cancelar el proceso para guardar de forma persistente la vista {0}.
##XFLD: Message for finalizing persistency cancellation
VIEW_PERSISTENCY_FINALIZE_CANCEL=Se ha enviado el proceso para detener la persistencia de la vista {0}.
##XFLD: Message used in persist task when cancellation is caught
VIEW_PERSISTENCY_CANCELED=Se ha parado el proceso para guardar de forma persistente la vista {0}.
##XFLD: Message used in persist task when cancelation is caught and performed through a cancelation task
VIEW_PERSISTENCY_CANCELED_VIA_CANCEL_TASK=Se ha parado el proceso para guardar de forma persistente la vista {0} mediante la tarea de cancelación {1}.
##XFLD: Message used in cancel task when the task is triggered by deployment
VIEW_PERSISTENCY_CANCEL_TRIGGERED_BY_DEPLOYMENT=Se está cancelando el proceso para guardar de forma persistente los datos mientras se despliega la vista {0}.
##XFLD: Message used in case the cancelation is retriggered for a running task
VIEW_PERSISTENCY_CANCEL_ATTEMPT_RETRIGGER_CANCEL=Ya se ha enviado una tarea de cancelación previa para la persistencia de la vista {0}.
##XFLD: Message used to express the delay that may occur before the persistency task is stopped
VIEW_PERSISTENCY_CANCEL_DELAY_STOPPING_PERSISTENCY=Es posible que se produzca un retraso hasta que se detenga la tarea de persistencia para la vista {0}.
##XFLD: Message used to reflect the task log identifier of the current running persistency task
VIEW_PERSISTENCY_CANCEL_CURRENT_RUNNING_TASK=Los datos para la vista {0} se están guardando de forma persistente con la tarea {1}.
##XFLD:Message used by SEAL repository for locked partitions and DACs
VIEW_PERSISTENCY_LOCKED_PARTITIONS_DACS=Es posible que se hayan modificado las autorizaciones proporcionadas por los DAC y no se tienen en cuenta en las particiones bloqueadas. Desbloquéelas y cargue una nueva instantánea para aplicar las modificaciones.
##XFLD:Message used by SEAL repository for column structure change
VIEW_PERSISTENCY_COLUMN_STRUCTURE_CHANGE=La estructura de columnas se ha modificado y ya no coincide con la tabla de persistencia existente. Quite los datos guardados de forma persistente e inicie una nueva persistencia de datos para que se actualice dicha tabla.
##XFLD: Message for out of memory
VIEW_PERSISTENCY_HANA_OUT_OF_MEMORY=La tarea ha fallado debido a un error de falta de memoria en la base de datos de SAP HANA.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_EXCEPTION_TRANSACTION_ROLLBACK=No se ha podido realizar la tarea debido a una excepción interna en la base de datos de SAP HANA.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_SQL_EXECUTION_ISSUE=No se ha podido realizar la tarea debido a un problema de ejecución SQL interno en la base de datos de SAP HANA.
##XFLD: Message for HANA out of memory result
VIEW_PERSISTENCY_OUT_OF_MEMORY_RESULT_MESSAGE=Motivo del evento de falta de memoria de HANA: {0}
##XFLD: Message for admission control rejection
VIEW_PERSISTENCY_HANA_ADMISSION_CTRL_REJECTION=La tarea ha fallado debido a un rechazo del control de admisión de SAP HANA.
##XFLD: Message for HANA back pressure / rate limit of HANA client
VIEW_PERSISTENCY_HANA_CLIENT_RATE_LIMIT_REJECTION=La tarea ha fallado debido a demasiadas conexiones activas de SAP HANA.
#XMSG: Message for invalid persisted table name
VIEW_PERSISTENCY_PERSISTED_TABLE_INVALID=Se ha producido un error y la tabla persistida ha dejado de ser válida. Para resolver el problema, elimine los datos persistidos y vuelva a persistir la vista.
#XMSG: Message for usahe of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION=La vista no se puede guardar de forma persistente porque utiliza una tabla remota basada en una fuente remota con la propagación de usuarios activada. Compruebe el linaje de la vista.
#XMSG: Message for dynamic usage of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION_DYNAMIC=La vista no se puede guardar de forma persistente porque utiliza una tabla remota basada en una fuente remota con la propagación de usuarios activada. La tabla remota puede consumirse de forma dinámica mediante una vista de script SQL. El linaje de la vista podría no mostrar la tabla remota.
#XMSG: Message for insufficient privileges or consumption of view with DAC via dynamic SQL
VIEW_PERSISTENCY_INSUF_PRIV_OR_DAC_DYN_SQL=Es posible que sus autorizaciones no sean suficientes. Abra la vista previa de datos para ver si tiene las autorizaciones necesarias. En caso afirmativo, es posible que se aplique el control de acceso a datos (CAD) a una segunda vista consumida mediante un script SQL dinámico.
#XMSG: deployment of DAC is deprecated and view need to be redeployed to be able to persist data
VIEW_PERSISTENCY_DEPRECATED_DAC=La vista "{0}" se ha desplegado mediante el control de acceso a datos (CAD) que va a quedar obsoleto. Vuelva a desplegar la vista para guardar los datos de la vista de forma persistente.
##XFLD:Message used by SEAL repository for input parameter
START_VIEW_PERSISTENCY_INPUT_PARAMETER=Se utiliza el valor predeterminado "{0}" para el parámetro de entrada "{1}".
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Elastic compute node keys~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
##XFLD:Message used to reflect disablement of ecn replica
ECN_DISABLE_EXISTING_REPLICA=Se ha desactivado la réplica del nodo de computación elástico.
##XFLD:Message used to reflect recreation of ecn replica
ECN_RECREATE_EXISTING_REPLICA=Se ha vuelto a crear la réplica del nodo de computación elástico.
##XFLD:Message used to reflect re-enablement of ecn replica
ECN_ENABLE_EXISTING_REPLICA=Se ha vuelto a activar la réplica del nodo de computación elástico.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Task Schedule Menu key~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message to schedule task
scheduleText=Programación
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Crear programación
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Editar programación
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Eliminar programación
#XFLD: Refresh frequency field
refreshFrequency=Frecuencia de actualización
#XFLD: Refresh frequency field
refreshFrequencyNew=Frecuencia
#XFLD: Refresh frequency field
refreshFrequencyNewNew=Frecuencia programada
#XBUT: label for None
none=Ninguno
#XBUT: label for Real-Time replication state
realtime=En tiempo real
#XFLD: Label for table column
txtNextSchedule=Siguiente ejecución
#XFLD: Label for table column
txtNextScheduleNew=Próxima ejecución programada
#XFLD: Label for table column
txtNumOfRecords=Número de registros
#XFLD: Label for scheduled link
scheduledTxt=Programado
#XFLD: LABEL for partially persisted link
partiallyPersisted=Persistido parcialmente
#XFLD: Text for paused text
paused=Interrumpida

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ View Monitor Tasklog Details Action keys~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message for release lock actionn
releaseLockMsg=Si una ejecución tarda más tiempo de lo normal, esto puede indicar que no se ha realizado correctamente y que el estado no se ha actualizado a la información correspondiente. \r\n Para solucionar el problema, puede liberar el bloqueo y definir su estado como Fallido.
#XFLD: Label for release lock dialog
releaseLockText=Liberar bloqueo

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Column header tooltip ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: tooltip for table column
txtViewTooltip=Nombre de la vista guardada de forma persistente
#XFLD: tooltip for table column
txtViewDataAccessTooltip=Esto indica la disponibilidad de la vista
#XFLD: tooltip for table column
txtViewRefreshFreqTooltip=Esto indica si hay una programación definida para la vista
#XFLD: tooltip for table column
txtViewStatusTooltip=Obtenga el estado de la vista guardada de forma persistente
#XFLD: tooltip for table column
txtViewLatestUpdateTooltip=Proporciona información sobre la última actualización de la vista guardada de forma persistente
#XFLD: tooltip for table column
txtViewNextRunTooltip=Si hay una programación definida para la vista, consulte para cuándo está programada la siguiente ejecución.
#XFLD: tooltip for table column
txtViewNumOfRecordsTooltip=Hace un seguimiento del número de registros.
#XFLD: tooltip for table column
txtViewInMemorySizeTooltip=Realice un seguimiento de la cantidad de memoria que utiliza la vista.
#XFLD: tooltip for table column
txtViewDiskSizeTooltip=Realice un seguimiento de la cantidad de espacio en disco que utiliza la vista.
#XMSG: Expired text
txtExpired=Vencido

#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=El objeto ''{0}'' no puede añadirse a la cadena de tareas.

#~~~~~~~~~~~View Analyzer task log messages~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message indicating number of records for a view, and memory consumption in Mebibyte during simulation of view persistency
RECORDS_PEAK_MEMORY=La vista "{0}" tiene {1} registros. Una simulación de la persistencia de la vista ha utilizado {2} MiB de memoria.
#XMSG: View Analyzer execution failure
VIEW_ANALYZER_EXECUTION_FAILED=Error al ejecutar el analizador de vistas.
#XMSG: Missing authorizations for View Analyzer
VIEW_ANALYZER_MISSING_AUTH=Faltan autorizaciones para el analizador de vistas.
#XMSG: Maximum memory in Gibibyte is reached, therefore persistency simulation is stopped
MAXIMUM_MEMORY_REACHED=Se ha alcanzado el consumo máximo de memoria de {0} GiB al simular la persistencia para la vista "{1}". Por lo tanto, no se ejecutarán más simulaciones de persistencia de vista.
#XMSG: Error during persistency simulation occurred
PERSISTENCY_SIMULATION_ERROR=Se ha producido un error durante la simulación de persistencia de la vista "{0}".
#XMSG: Persistency simulation is not executed, because prerequisites are not fulfilled and the view cannot be persisted
PREREQUISITES_NOT_FULFILLED=La simulación de persistencia no se ejecuta para la vista "{0}" porque no se cumplen los requisitos previos y la vista no se puede persistir.
#XMSG: Persistency simulation is not executed, because the view does not exist in the database
VIEW_DOES_NOT_EXIST=Debe desplegar la vista "{0}" para activar la simulación de persistencia de datos.
#XMSG: Number of records cannot be determined, because the local table does not exist in the database
TABLE_DOES_NOT_EXIST=La tabla local "{0}" no existe en la base de datos, por lo tanto no se puede determinar el número de registros para esta tabla.
##XMSG: Message start of cancellation for View Analyzer task
CANCEL_VIEW_ANALYZER_TRIGGERED=Se ha enviado el proceso para parar la tarea del Analizador de vistas {0} para la vista "{1}". Es posible que haya un retraso hasta que se pare la tarea.
##XMSG: Message start to indicate that task is not active
CANCEL_VIEW_ANALYZER_NO_ACTIVE_TASK=La tarea del Analizador de vistas {0} para la vista "{1}" no está activa.
#XMSG: Cancel View Analyzer task failure
CANCEL_VIEW_ANALYZER_FAILED=No se ha podido cancelar la tarea del Analizador de vistas.
##XMSG: Message used in View Analyzer execution task when task is canceled
VIEW_ANALYZER_CANCELED=Se ha parado la ejecución del Analizador de datos para la vista "{0}" mediante una tarea de cancelación.
##XMSG: Message start of cancelation for Model Validation task
CANCEL_MODEL_VALIDATION_TRIGGERED=Se ha enviado el proceso para parar la tarea Validación de modelo {0} para la vista "{1}". Es posible que haya un retraso hasta que se pare la tarea.
##XMSG: Message to indicate that task is not active
CANCEL_MODEL_VALIDATION_NO_ACTIVE_TASK=La tarea Validación de modelo {0} para la vista "{1}" no está activa.
#XMSG: Cancel Model Validation task failure
CANCEL_MODEL_VALIDATION_FAILED=No se ha podido cancelar la tarea Validación de modelo.
##XMSG: Message used in Model Validation task when task is canceled
MODEL_VALIDATION_CANCELED=Se ha parado la ejecución de Validación de modelo para la vista "{0}" mediante una tarea de cancelación.
#XMSG: Message for Locked space error, before starting Model Validation task
MODEL_VALIDATION_LOCKED_SPACE_ERROR=No se puede ejecutar Validación de modelo para la vista "{0}" porque el espacio "{1}" está bloqueado.
##XMSG: Message used in View Analyzer execution task when determination of number of rows fails
NUMBER_ROWS_DETERMINATION_FAILED=Se ha producido un error mientras se determinaba el número de filas para la tabla local ''{0}''.
##XMSG: Message to indicate that SQL plan was created
EXECUTION_PLAN_CREATED=Se ha creado un archivo de planificación del analizador SQL para la vista "{0}" que se puede descargar.
#XFLD: Message to indicate starting of process to generate SQL Analyzer plan file
START_PLANVIZ_EXECUTION=Se está iniciando el proceso para generar un archivo de planificación del analizador SQL para la vista "{0}".
#XFLD: Message to indicate starting of View Analyzer process
VIEW_ANALYZER_EXECUTION_STARTED=Se está iniciando la ejecución del Analizador de vistas.
##XMSG: Message to indicate that SQL plan file cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_PLANVIZ_NOT_FULFILLED=No se puede generar el archivo de planificación del analizador SQL para la vista "{0}" porque no se cumplen los requisitos previos de persistencia de datos.
##XMSG: Error message during  generation of SQL Analyzer plan file
PLANVIZ_ERROR=Se ha producido un error al generar el archivo de planificación del analizador SQL para la vista "{0}".
#XMSG: Message for Locked space error
VIEW_ANALYZER_LOCKED_SPACE_ERROR=No puede ejecutar el Analizador de vistas para la vista "{0}" porque el espacio "{1}" está bloqueado.
#XMSG: Message for persistency simulation without partitioning
PERSISTENCY_SIMULATION_WITHOUT_PARTITIONS=Las particiones de la vista "{0}" no se tienen en cuenta durante la simulación de la persistencia de vistas.
#XMSG: Message for generation of SQL Analyzer plan file without considering partitioning
PLANVIZ_WITHOUT_PARTITIONS=Las particiones de la vista "{0}" no se tienen en cuenta durante la generación del archivo de planificación del analizador SQL.
#XMSG: Confirmation message for mass removal of persisted data
MassRemovePersistency_Confirm_Msg=¿Desea quitar los datos guardados de forma persistente y volver al acceso virtual a los datos?
#XMSG: Confirmation message for partial mass removal of selected persisted data
PartialMassRemovePersistency_Confirm_Msg={0} de las {1} vistas seleccionadas tienen datos guardados de forma persistente. \n ¿Desea quitar estos datos y volver al acceso virtual a los datos?
#XMSG: success message for stopping mass persistence
stopMassPersistenceSuccess=Estamos quitando datos guardados de forma persistente para las vistas seleccionadas.
#XMSG: error message during mass stop persistence of a view (backend error)
STOP_MASS_PERSISTENCE_ERROR=Se ha producido un error al detener la persistencia de las vistas seleccionadas.
#XMSG: memory analysis is only performed in space {0}
NO_MEMORY_ANALYSIS=El análisis de memoria se efectúa únicamente para las entidades del espacio "{0}": Se ha omitido "{1}" "{2}".
##XMSG: Message to indicate that Explain Plan cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_EXPLAINPLAN_NOT_FULFILLED=No se puede generar el archivo de planificación explicativo para la vista "{0}" porque no se cumplen los requisitos previos de persistencia.
#XMSG: Message for Explain Plan generation without partitioning
EXPLAINPLAN_WITHOUT_PARTITIONS=Las particiones de la vista "{0}" no se tienen en cuenta durante la generación del archivo de planificación explicativo.
#XFLD: Message to indicate starting of process to generate Explain Plan file
START_EXPLAINPLAN_EXECUTION=Se está iniciando el proceso para generar un archivo de planificación explicativo "{0}".
##XMSG: Message to indicate that Explain Plan was created
EXPLAINPLAN_CREATED=Se ha generado el archivo del plan de explicación para la vista "{0}". Puede visualizarlo haciendo clic en "Ver detalles" o descargarlo si tiene el permiso correspondiente.
##XMSG: Error message during  generation of Explain Plan file
EXPLAINPLAN_ERROR=Se ha producido un error al generar el archivo de planificación explicativo para la vista "{0}".
##XMSG: Explain Plan file cannot be generated for the view {0}, because too many views are stacked on each other
EXPLAINPLAN_MODEL_TOO_COMPLEX=El archivo Explicar plan no puede para la vista "{0}". Hay demasiadas vistas apiladas unas sobre otras. Los modelos tan complejos podrían provocar errores de falta de memoria y un rendimiento lento. Se recomienda guardar una vista de forma permanente.

##XMSG: Message to indicate that the space is locked
PERFORMANCE_ANALYSIS_LOCKED_SPACE_ERROR=No se puede ejecutar el análisis de rendimiento para la vista "{0}", porque el espacio "{1}" está bloqueado.
##XMSG: The performance analysis task was cancelled
PERFORMANCE_ANALYSIS_CANCELED=Se ha cancelado el análisis de rendimiento de la vista "{0}".
##XMSG: The performance analysis failed
PERFORMANCE_ANALYSIS_FAILED=Se ha producido un error en el análisis de rendimiento.
##XMSG: The performance analysis is finished
PERFORMANCE_ANALYSIS_FINISHED=El análisis de rendimiento de la vista "{0}" ha finalizado. Visualice el resultado haciendo clic en "Ver detalles".
##XMSG: The view has input parameter with no default value
INPUT_PARAMETER_WITHOUT_DEFAULT_VALUE=La vista no se puede analizar porque contiene más de un parámetro de entrada.
##XMSG: The view is not fully deployed
VIEW_NOT_FULLY_DEPLOYED=Esta vista no se puede analizar porque no está completamente desplegada.
##XMSG: The view uses limited remote adapters
LIMITED_REMOTE_ADAPTER=Esta vista utiliza al menos un adaptador remoto con capacidades limitadas, como la falta de inserción de filtros o compatibilidad con "Recuento". Guardar de forma persistente o replicar objetos puede mejorar el rendimiento del tiempo de ejecución.
##XMSG: The view uses remote adapter that does not support 'Limit'
REMOTE_ADAPTER_NOT_SUPPORTING_LIMIT=Esta vista utiliza al menos un adaptador remoto que no admite la opción "Límite". Es posible que se hayan seleccionado más de 1000 registros.
##XMSG: Performance analysis is executed by using default parameters of view'
INPUT_PARAMETER_WITH_DEFAULT_VALUE=El análisis de rendimiento se ejecuta utilizando los valores predeterminados de los parámetros de vista.
##XMSG: Message to indicate that an error occurred
PERFORMANCE_ANALYSIS_ERROR=Se ha producido un error durante el Análisis de rendimiento para la vista "{0}".
##XMSG: Message start of cancellation for Performance Analysis task
CANCEL_PERFORMANCE_ANALYSIS_TRIGGERED=Se ha enviado el proceso para detener la tarea de análisis de rendimiento {0} de la vista "{1}". Es posible que se produzca una demora hasta que se detenga la tarea.
##XMSG: Message start to indicate that task is not active
CANCEL_PERFORMANCE_ANALYSIS_NO_ACTIVE_TASK=La tarea de análisis de rendimiento {0} de la vista "{1}" no está activa.
#XMSG: Cancel Performance Analysis task failure
CANCEL_PERFORMANCE_ANALYSIS_FAILED=No se ha podido cancelar la tarea de análisis de rendimiento.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Asignarme la programación
#XBUT: Pause schedule menu label
pauseScheduleLabel=Interrumpir programación
#XBUT: Resume schedule menu label
resumeScheduleLabel=Reanudar programación
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Se ha producido un error al quitar las programaciones.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Se ha producido un error al asignar las programaciones.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Se ha producido un error al interrumpir las programaciones.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Se ha producido un error al reanudar las programaciones.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Se están eliminando {0} programaciones
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Se está modificando el propietario de {0} programaciones
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Se están interrumpiendo {0} programaciones
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Se están reanudando {0} programaciones
#XBUT: Select Columns Button
selectColumnsBtn=Seleccionar columnas
#XFLD: Refresh tooltip
TEXT_REFRESH=Actualizar
#XFLD: Select Columns tooltip
text_selectColumns=Seleccionar columnas


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Runtime Metrics ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Label for Runtime Metrics
RuntimeMetricsTitle=Métricas de tiempo de ejecución para
#XFLD : Label for Run Button
runButton=Ejecutar
#XFLD : Label for Cancel Button
cancelButton=Cancelar
#XFLD : Label for Close Button
closeButton=Cerrar
#XFLD : Label for Open View Analyzer Button
openViewAnalyzerButton=Abrir Analizador de vistas
#XFLD : Label for Generate Explain Plan Button
generateExplainPlanButton=Generar plan de explicación
#XFLD : Label for Previous Run Column
previousRun=Ejecución anterior
#XFLD : Label for Latest Run Column
latestRun=Última ejecución
#XFLD : Label for time Column
time=Hora
#XFLD : Label for Duration Column
duration=Duración
#XFLD : Label for Peak Memory Column
peakMemory=Uso máximo de memoria
#XFLD : Label for Number of Rows
numberOfRows=Número de filas
#XFLD : Label for Overall Number of Sources
overallNumberOfSources=Número global de fuentes
#XFLD : Label for Data Access Column
dataAccess=Acceso a datos
#XFLD : Label for Local Tables
localTables=Tablas locales
#XFLD : Label for federatedRemoteTables
federatedRemoteTables=Tablas remotas federadas (con capacidades limitadas de adaptador)
#XTXT Text for initial state of the runtime metrics
initialState=Debe ejecutar primero el Análisis de rendimiento para obtener las métricas. Esto puede tardar un tiempo, pero puede cancelar el proceso si es necesario.
#XMSG: Cancel confirmation message
CancelRunConfirmationMessage=¿Seguro que desea cancelar la ejecución actual del Análisis de rendimiento?
#XTIT: Cancel dialog title
CancelRunTitle=Cancelar ejecución
#XFLD: Label for Number of Rows
NUMBER_ROWS=Número de filas
#XFLD: Label for Overall Number of Sources
OVERALL_NUMBER_SOURCES=Número global de fuentes
#XFLD: Label for Data Access
DATA_ACCESS=Acceso a datos
#XFLD: Label for Remote tables
NUMBER_REMOTE_TABLES_LIMITED_ADAPTERS=Tablas remotas federadas (con capacidades limitadas de adaptador)
#XFLD: Label for select statement
SELECT_STATEMENT='SELECCIONAR * DEL LÍMITE DE VISTA 1000'
#XFLD: Label for duration
SELECT_RUNTIME=Duración
#XFLD: Label for peak memory
SELECT_PEAK_MEMORY=Uso máximo de memoria
#XFLD: Label for count statement
SELECT_COUNT_STATEMENT='SELECCIONAR RECUENTO (*) DE LA VISTA'
#XFLD: Label for count duration
SELECT_COUNT_RUNTIME=Duración
#XFLD: Label for count peak memory
SELECT_COUNT_PEAK_MEMORY=Uso máximo de memoria
#XFLD: Label for count of number of local tables
NUMBER_LOCAL_TABLES_FILE=Tablas locales (archivo)
#XTXT: Text for running state of the runtime metrics
Running=En ejecución...
#XFLD: Label for time
Time=Hora
#XFLD: Label for virtual access
PA_VIRTUAL=Virtual
#XFLD: Label for persisted access
PA_PERSISTED=Guardado de forma persistente
PA_PARTIALLY_PERSISTED=Guardado de forma persistente parcialmente
#XTXT: Text for cancel
CancelRunSuccessMessage=Se está cancelando la ejecución del Análisis de rendimiento.
#XTXT: Text for cancel error
CancelRunErrorMessage=Se ha producido un error al cancelar la ejecución del Análisis de rendimiento.
#XTXT: Text for explain plan generation
ExplainPlanStarted=Se está generando el plan de explicación para la vista "{0}".
#XTXT: Text for performance analysis
PerformanceAnalysisStarted=Se está iniciando el Análisis de rendimiento para la vista "{0}".
#XTXT: Text for performance analysis error
ErrorFetchingPerformanceAnalysis=Se ha producido un error al obtener la ejecución del Análisis de rendimiento.
#XTXT: Text for performance analysis error
conflictingTask=Ya se está ejecutando la tarea del Análisis de rendimiento
#XFLD: Label for Errors
Errors=Errores
#XFLD: Label for Warnings
Warnings=Advertencias
#XFLD: Tooltip for missing privileges
openViewAnalyzerButtonTooltipNoPrivilege=Necesita la autorización DWC_DATAINTEGRATION(update) para abrir el Analizador de vistas.
#XFLD: Tooltip for missing privileges
generateExplainPlanButtonTooltipNoPrivilege=Necesita la autorización DWC_RUNTIME(read) para generar el plan de explicación.



#XFLD: Label for frequency column
everyLabel=Cada
#XFLD: Plural Recurrence text for Hour
hoursLabel=horas
#XFLD: Plural Recurrence text for Day
daysLabel=días
#XFLD: Plural Recurrence text for Month
monthsLabel=meses
#XFLD: Plural Recurrence text for Minutes
minutesLabel=minutos
