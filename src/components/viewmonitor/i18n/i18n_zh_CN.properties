
#XBCB: Breadcrumb
viewMonitorBreadcrumb={spaceId}

#XFLD: Label for persisted view column
SOURCE=源
#XFLD: Label for persisted view column
NAME=名称
#XFLD: Label for persisted view column
NAME_LABEL=业务名称
#XFLD: Label for persisted view column
NAME_LABELNew=对象（业务名称）
#XFLD: Label for persisted view column
TECHINCAL_NAME=技术名称
#XFLD: Label for persisted view column
TECHINCAL_NAMENew=对象（技术名称）
#XFLD: Label for persisted view column
DATA_PERSISTENCY=数据访问
#XFLD: Label for persisted view column
STATUS=状态
#XFLD: Label for persisted view column
LAST_UPDATED=上次更新
#XFLD: Label for persisted view column
IN_MEMORY_SIZE=已用内存存储 (MiB)
#XFLD: Label for persisted view column
DISK_SIZE=已用磁盘存储 (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_NEWW=内存大小 (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_HEADER=内存大小
#XFLD: Label for persisted view column
DISK_SIZE_NEWW=磁盘存储大小 (MiB)
#XFLD: Label for persisted view column
DISK_SIZE_HEADER=磁盘存储大小
#XFLD: Label for schedule owner column
txtScheduleOwner=计划所有者
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=显示计划创建者
#XFLD: Label for persisted view column
PERSISTED=已持久化
#XFLD: Label for persisted view column
TYPE=类型
#XFLD: Label for View Selection Dialog column
changedOn=更改日期
#XFLD: Label for View Selection Dialog column
createdBy=创建者
#XFLD: Label for log details column
txtViewPersistencyLogs=查看日志
#XFLD: Label for log details column
txtViewPersistencyLogsNew=详细信息
#XFLD: text for values shown for Ascending sort order
SortInAsc=升序排序
#XFLD: text for values shown for Descending sort order
SortInDesc=降序排序
#XFLD: Label for persisted view monitor heading
ViewMonitor_Heading=视图监控器
#XFLD: Label for persisted view monitor sub-heading
ViewMonitor_SubHeading=监控和维护视图的数据持久化


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ DynamicPageHeader ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: text for values shown in column Persistence Status
txtLoading=正在加载
#XFLD: text for values shown in column Persistence Status
txtRunning=运行中
#XFLD: text for values shown in column Persistence Status
txtAvailable=可用
#XFLD: text for values shown in column Persistence Status
txtError=错误
#XMSG: Error message for unknown Persistence type that gets logged in Kibana
unknownReplicationTypeError=复制类型 "{0}" 不受支持。

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Content ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=上次数据持久化运行使用的设置：
#XMSG: Message for input parameter name
inputParameterLabel=输入参数
#XMSG: Message for input parameter value
inputParameterValueLabel=值
#XMSG: Message for persisted data
inputParameterPersistedLabel=已持久化
#XTIT: Count for View in ViewMonitor Toolbar
txtViewCount=视图 ({0})
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=视图持久性
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=数据持久化
#XBUT: Button to clear the selected view persistance
ClearViewPersistance=清除
#XBUT: Button to stop the selected view persistance
stopPersistance=停止持久化
#XFLD: Placeholder for Search field
txtSearch=搜索
#XBUT: Tooltip for refresh button
txtRefresh=刷新
#XBUT: Tooltip for add view button
txtDeleteView=删除持久性
#XBUT: Tooltip for load new peristence
loadNewPersistence=重新开始持久化
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=加载新快照
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=开始数据持久化
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=移除持久数据
#XMSG: success message for starting persistence
startPersistenceSuccess=正在持久化视图 "{0}"。
#XMSG: success message for stopping persistence
stopPersistenceSuccess=正在移除视图 "{0}" 的持久数据。
#XMSG: success message for stopping persistence
deletePersistedViewSuccess=正在从监控列表移除视图 "{0}"。
#XMSG: error message during start persistence of a view (backend error)
ERROR_STARTING_PERSISTENCE=开始视图 "{0}" 的数据持久化时出错。
#XMSG: error message during start persistence of a view (backend error)
PARAMERROR_STARTING_PERSISTENCE=没能持久化视图 "{0}"，因为它包含输入参数。
#XMSG: error message during start persistence of a view (backend error)
NUMBER_OF_PARAMETER_STARTING_PERSISTENCE=没能持久化视图 "{0}"，因为它包含多个输入参数。
#XMSG: error message during start persistence of a view (backend error)
PARAMETER_DEFAULT_VALUE_STARTING_PERSISTENCE=没能持久化视图 "{0}"，因为输入参数没有默认值。
#XMSG: error message during start persistence of a view (backend error)
REDEPLOYMENT_DAC_REQUIRED=需要重新部署数据访问控制（DAC）"{0}"，才能支持数据持久化。
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_STARTING_PERSISTENCE=没能持久化视图 "{0}"，因为它使用的视图 "{1}" 包含数据控制访问（DAC）。
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_EXTERNAL_STARTING_PERSISTENCE=没能持久化视图 "{0}"，因为它使用的视图具有属于其他空间的数据访问控制（DAC）。
#XMSG: error message during start persistence of a view (backend error)
DACERROR_STRUCTURE_STARTING_PERSISTENCE=没能持久化视图 "{0}"，因为它有一个或多个数据访问控制（DAC）的结构不支持数据持久化。
#XMSG: error message during stop persistence of a view (backend error)
STOP_PERSISTENCE_ERROR=停止视图 "{0}" 的持久化时出错。
#XMSG: error message during stop persistence of a view (backend error)
DELETE_PERSISTED_VIEW_ERROR=删除持久视图 "{0}" 时出错。
#XMSG: Warning message for removing the persisted data
RemovePersistency_Confirm_Msg=是否要删除持久数据并切换到视图 "{0}" 的虚拟访问？
#XMSG: Warning message for delete the view persistence
DeletePersistency_Confirm_Msg=是否要从监控列表移除视图并删除视图 "{0}" 的持久数据？
#XMSG: error message for reading data from backend
txtReadBackendError=从后端读取时似乎出现错误。
#XFLD: Label for No Data Error
NoDataError=错误
#XMSG: message for conflicting task
Task_Already_Running=视图 "{0}" 已经有一个冲突任务在运行。

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Show All Views~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Text for show all views button
allViewsText=所有视图（{0}）
#XBUT: Text for show scheduled views button
scheduledText=已计划（{0}）
#XBUT: Text for show persisted views button
persistedText=已持久化（{0}）
#XBUT: Text for start analyzer button
startAnalyzer=启动视图分析器
#XFLD: Message if repository is unavailable
repositoryErrorMsg=这个资源库不可用，某些功能被禁用。

#XFLD: Data Access - Virtual
Virtual=虚拟
#XFLD: Data Access - Persisted
Persisted=已持久化

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Non-Persisted View Selection Dialog~~~~~~~~~~~~~~~~~~~~~~~

#XTIT: Title for Non-Persisted View selection dialog
select_source=选择要持久化的视图

#XTIT: Placeholder for Non-Persisted View selection dialog search
searchView=搜索视图
#XTIT: No data in the list of non-persisted view
No_Data=没有数据
#XBUT: Button to select non-persisted view
ok=确定
#XBUT: Button to close the non-persisted views selection dialog
cancel=取消

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~View persistency log message key~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message for view persistency logs
START_STOP_PERSISTENCY=正在开始 "{1}" 的数据持久化任务运行。
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY=正在持久化视图“{1}”的数据。
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY)
START_VIEW_PERSISTENCY_PROCESS=正在开始视图 "{0}" 的数据持久化流程。
#XFLD: Message for view persistency manual partition load
START_VIEW_PERSISTENCY_PROCESS_MANUAL_PARTITIONS=正在开始视图 "{0}" 的数据持久化流程，选定的分区 ID 为："{1}"。
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY=正在移除视图 "{1}" 的持久数据。
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY)
REMOVE_VIEW_PERSISTENCY=正在启动视图 "{0}" 的持久数据移除流程。
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_SUCCESS=已经持久化视图 "{1}" 的数据。
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_SUCCESS)
START_VIEW_PERSISTENCY_PROCESS_SUCCESS=已经持久化视图 "{0}" 的数据。
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY_SUCCESS=已经移除持久数据，并且已还原视图 "{1}" 的虚拟数据访问。
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_SUCCESS)
REMOVE_VIEW_PERSISTENCY_SUCCESS=已完成视图 "{0}" 的持久数据移除流程。
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_FAILED=没能持久化视图 "{1}" 的数据。
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_FAILED)
START_VIEW_PERSISTENCY_PROCESS_FAILED=没能持久化视图 "{0}" 的数据。
#XFLD: Message for view persistency logs (for messages created before version 2022.05)
STOP_VIEW_PERSISTENCY_FAILED=没能移除视图 "{1}" 的持久数据。
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_FAILED)
REMOVE_VIEW_PERSISTENCY_FAILED=没能移除视图 "{0}" 的持久数据。
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_SUCCESS=已经为视图 "{1}" 持久化 "{3}" 项记录。
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace START_PERSISTENCY_RECORD_COUNT_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=已经在视图"{1}" 的数据持久化表中插入 {0} 项记录。
#XFLD: Message used by SEAL repository for view persistency logs (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS_V2=已经在视图 "{1}" 的数据持久化表中插入 {0} 项记录。已用内存：{2} GiB。
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_SUCCESS=已经为视图 "{1}" 移除 "{3}" 项持久记录。
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace STOP_PERSISTENCY_RECORD_COUNT_SUCCESS)
REMOVE_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=已经移除持久数据，并且已删除 "{0}" 项持久记录。
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_FAILED=获取视图 "{1}" 的记录计数失败。
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_FAILED=获取视图 "{1}" 的记录计数失败。
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=已经为 "{1}" 删除计划。
#XFLD: Message for view persistency logs (new message to replace STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS)
REMOVE_VIEW_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=已经为视图 "{0}" 删除计划。
#XFLD: Message for view persistency logs
STOP_PERSISTENCY_DELETE_SCHEDULE_FAILED="{1}" 的计划删除失败。
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_REDEPLOY=没能持久化视图 "{0}"，因为从开始持久化以来，它已经更改和部署。请再次尝试持久化这个视图或者等到下次计划运行。
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_DELETE=没能持久化视图 "{0}"，因为从开始持久化以来，它已经被删除。
#XFLD: Message for successful data persistency of a single partition (for messages created before version 2022.03)
START_PERSISTENCY_SINGLE_PARTITION_SUCCESS=已经将 {0} 项记录持久保存到值 "{1}" <= {2} < "{3}" 的分区。
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (new message to replace START_PERSISTENCY_SINGLE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS=已经将 {0} 项记录插入值 "{1}" <= "{2}" < "{3}" 的分区。
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS_V2=已经将 {0} 项记录插入值 "{1}" <= "{2}" < "{3}" 的分区。已用内存：{4} GiB。
#XFLD: Message for successful replication of a "others" partition (for messages created before version 2022.03)
START_PERSISTENCY_OTHERS_PARTITION_SUCCESS=已经将 {0} 项记录持久保存到 "其他" 分区。
#XFLD: Message used by SEAL repository for successful replication of a "others" partition (new message to replace START_PERSISTENCY_OTHERS_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_OTHERS_PARTITION_SUCCESS=已经将 {0} 项记录插入 "其他" 分区。
#XFLD: Message for view persistency record count of partitions (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS=已经在 {4} 个分区中为视图 "{1}" 持久化 {3} 项记录。
#XFLD: Message used by SEAL repository for view persistency record count of partitions (new message to replace START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS=在 {2} 个分区中，已经为视图 "{1}" 在数据持久化表中插入 {0} 项记录。
#XFLD: Message used by SEAL repository for view persistency for partition wise refresh
START_VIEW_PERSISTENCY_RECORD_COUNT_REFRESHED_PARTITIONS_SUCCESS=已经为视图 "{1}" 在数据持久化表中插入 {0} 项记录。已更新分区数：{2}；已锁定分区数：{3}；分区总数：{4}
#XFLD: Message used by SEAL repository for view persistency for manual partition load
START_VIEW_PERSISTENCY_RECORD_COUNT_MANUAL_PARTITIONS_SUCCESS=在 {2} 个选定分区中，已经为视图 "{1}” 在持久化表中插入 {0} 项记录。
#XFLD: Message used by SEAL repository for view persistency for locked partition data load
START_VIEW_PERSISTENCY_RECORD_COUNT_LOCKED_PARTITIONS_SUCCESS=已经为视图 "{1}" 在持久化表中插入 {0} 项记录。已更新分区数：{2}；已锁定的未更改分区数：{3}；分区总数：{4}
#XMSG: Message for unexpected error (for messages created before version 2022.03)
START_PERSISTENCY_UNEXPECTED_ERROR=持久化视图 "{0}" 的数据时出现意外错误。
#XMSG: Message for unexpected error (new message to replace START_PERSISTENCY_UNEXPECTED_ERROR)
START_VIEW_PERSISTENCY_UNEXPECTED_ERROR=持久化视图 "{0}" 的数据时出现意外错误。
#XMSG: Message for Locked space error
START_VIEW_PERSISTENCY_LOCKED_SPACE_ERROR=没能持久化视图 "{0}"，因为空间 "{1}" 已经锁定。
#XMSG: Message for unexpected error (for messages created before version 2022.03)
STOP_PERSISTENCY_UNEXPECTED_ERROR=移除视图 "{0}" 的持久数据时出现意外错误。
#XMSG: Message for unexpected error (new message to replace STOP_PERSISTENCY_UNEXPECTED_ERROR)
REMOVE_VIEW_PERSISTENCY_UNEXPECTED_ERROR=移除视图 "{0}" 的持久性时出现意外错误。
#XMSG: Message for ivalidated view
VIEW_PERSISTENCY_ABORTED_DEFINITION_INVALID=视图 "{0}" 的定义已经失效，最可能的原因是视图直接或间接使用的对象已经更改。请尝试重新部署视图，解决问题，或找出根本原因。
#XMSG: Message for persisted data removal (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT=部署视图 "{0}" 时已经移除持久数据。
#XMSG: Message for persisted data removal of a consuming view
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW=部署使用的视图 "{0}" 时已经移除持久数据。
#XMSG: Message for persisted data removal of a consuming view due to a dac change
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW_DAC_CHANGED=部署使用的视图 "{0}" 时已经移除持久数据，因为它的数据访问控制已经更改。
#XMSG: Message for persisted data removal (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DEPLOYMENT=部署视图 "{0}" 时已经移除持久数据。
#XMSG: Message for deleted view (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION=删除视图 "{0}" 时已经移除持久性。
#XMSG: Message for deleted view (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DELETION=删除视图 "{0}" 时已经移除持久性。
#XMSG Message for removal of persisted data, because prerequisites not fulfilled
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_PREREQUISITES_NOT_FULFILLED=由于不再满足数据持久化先决条件，持久数据已被移除。
#XMSG: Message for inconsistent persistency
VIEW_PERSISTENCY_INCONSISTENT_PERSISTENCY=视图 "{0}" 的持久性已经变得不一致。请移除持久数据，解决这个问题。
#XMSG: Message for prerequisites
START_PERSISTENCY_CHECK_PREREQUISITES=正在检查持久化视图 "{0}" 的先决条件。
#XMSG: Message for DAC deployed in deprecated way using table function and join (for messages created before 2025.05)
START_PERSISTENCY_DEPRECIATED_DAC=视图 "{0}" 是使用即将弃用的数据访问控制（DAC）进行部署的。请重新部署视图，以便提升性能。
#XMSG: Message for DAC deployed in deprecated way using table function and join
START_PERSISTENCY_DEPRECATED_DAC=视图 "{0}" 是使用即将弃用的数据访问控制（DAC）进行部署的。请重新部署视图，以便提升性能。
#XMSG: Message for roll back (for messages created before version 2022.03)
ROLLBACK_VIEW_PERSISTENCY=出现错误。已经还原视图 "{0}" 的先前持久性状态。
#XMSG: Message used by SEAL repository for rollback of running procedure to persist view (new message to replace ROLLBACK_VIEW_PERSISTENCY)
VIEW_PERSISTENCY_ROLLBACK=出现错误。持久化视图“{0}”的流程已停止，并且已回滚更改。
#XMSG: Message used by SEAL repository for rollback of running procedure to remove persisted data of view
REMOVE_VIEW_PERSISTENCY_ROLLBACK=出现错误。移除视图“{0}”的持久数据的流程已停止，并且已回滚更改。
#XMSG: Message used by SEAL repository for persistency preparation
START_PERSISTENCY_PREPARATION=正在准备持久化数据。
#XMSG: Message used by SEAL repository for data insertion
START_INSERTING_PERSISTENCY_DATA=正在向持久化表中插入数据。
#XMSG: Message for null values - partitioning (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS=已经将 {0} 个 Null 值记录插入 "其他" 分区。
#XMSG: Message used by SEAL repository for others partition values - partitioning (new message to replace START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS=已经将 {0} 项记录插入值 "{2}" < "{1}" 或 "{2}" >= "{3}" 的 "其他" 分区。
#XMSG: Message used by SEAL repository for others partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS_V2=已经将 {0} 项记录插入值 "{2}" < "{1}" 或 "{2}" >= "{3}" 的 "其他" 分区。已用内存：{4} GiB。
#XMSG: Message used by SEAL repository for null partition values - partitioning
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS=已经将 {0} 项记录插入值 "{1}" 为 NULL 的 "其他" 分区。
#XMSG: Message used by SEAL repository for null partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS_V2=已经将 {0} 项记录插入值 "{1}" 为 NULL 的 "其他" 分区。已用内存：{2} GiB。
##XFLD:Message used by SEAL repository for remote statistics
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS=正在加载数据，涉及 {0} 个远程语句。获取的总记录数：{1}。总持续时间：{2} 秒。
##XFLD:Message used by SEAL repository for remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS_WITH_PARTITIONS=正在加载数据，涉及使用 {0} 个分区和 {1} 个远程语句。获取的总记录数：{2}。总持续时间：{3} 秒。
##XFLD:Message used by SEAL repository with UI ID for instructions of remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_INSTRUCTION=打开远程查询监控器，可以在分区特定消息的详细信息中显示运行期间处理的远程语句。
##XFLD:Message for start reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS=正在对视图 {0} 启动部署后重新使用现有持久数据的流程。
##XFLD:Message used by SEAL repository for reattach view persitency
START_VIEW_PERSISTENCY_REATTACHMENT=开始重新使用现有持久数据。
##XFLD:Message used by SEAL repository for reattach view persitency
START_FINILIZING_VIEW_PERSISTENCY_REATTACHMENT=正在重新使用现有持久数据。
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_COMPLETE=已完成对视图 {0} 重新使用现有持久数据的流程。
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_FAILED=部署后没能对视图 {0} 重新使用现有持久数据。
#XMSG: Message used by SEAL repository for finalizing persistency
START_FINALIZING_PERSISTENCY=正在完成持久化。
#XMSG: Message used by SEAL repository for restoring virtual access for view
REMOVE_VIEW_PERSISTENCY_VIRTUAL_ACCESS_RESTORED=已经还原视图 "{0}" 的虚拟数据访问。
#XMSG: Message used by SEAL repository for view that already has virtual access
REMOVE_VIEW_PERSISTENCY_ALREADY_VIRTUAL_ACCESS=视图 "{0}" 已具有虚拟数据访问权限。没有移除任何持久数据。
#XMSG: Message for removing view persistency
VIEW_PERSISTENCY_REMOVED_FROM_MONITOR=已经从视图监控器中移除视图 "{0}"。
#XMSG: Message for view doesnt exist.
VIEW_PERSISTENCY_VIEW_NOT_EXISTING=视图 "{0}" 在数据库中不存在或部署不正确，因此没能持久化。请尝试重新部署视图来解决问题，或者查找根本原因。
#XMSG: Message for persistency not enabled.
VIEW_PERSISTENCY_NOT_ENABLED=没有启用数据持久化。请在空间 "{0}" 中重新部署表/视图，启用该功能。
#XMSG: Message for unexpected error due to node crash
VIEW_PERSISTENCY_UNEXPECTED_ERROR_GENERAL=由于技术错误，上次视图持久化运行已中断。
#XFLD: Message for view persistency memory consumption
VIEW_PERSISTENCY_MEMORY_CONSUMPTION=在视图持久化运行时已使用 {0} GiB 峰值内存。
#XFLD: Message for view persistency timeout
VIEW_PERSISTENCY_TIMEOUT=视图 {0} 的持久化超时时间已达到 {1} 小时。
#XFLD: Message for not started asynchronous view persistency
VIEW_PERSISTENCY_ASYNC_NOT_STARTED=高系统负载阻止开始视图持久化的异步执行。检查是否有太多的任务正在并行运行。
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency (for messages created before version 2022.05)
START_VIEW_PERSISTENCY_EXECUTION_METHOD_DROP_AND_RENAME=现有持久化表已删除，并替换为新的持久化表。
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_RECREATE_AND_DROP=现有持久化表已删除，并替换为新的持久化表。
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_TRUNCATE_AND_COPY=已在现有持久化表中更新新数据。
##XFLD: Message for missing authorizations for view persistency
VIEW_PERSISTENCY_MISSING_AUTH=缺少数据持久化权限。
##XFLD: Message start of persistency cancellation
VIEW_PERSISTENCY_TRIGGER_CANCEL=正在开始取消视图 {0} 的持久化流程。
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_UNAVAILABLE_TASK_RUN=没能取消视图持久化流程，因为视图 {0} 没有正在运行的数据持久化任务。
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_NONDETERMINISTIC_TASK_RUN=没能取消视图持久化流程，因为视图 {0} 没有数据持久化任务正在运行。
##XFLD: Message for canceling a non running persitency task
VIEW_PERSISTENCY_CANCEL_NON_RUNNING_SELECTED_TASK=没能取消持久化视图 {0} 的流程，因为选定的数据持久化任务 {1} 还没有运行。
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_UNREACHABLE_PERSISTENCY_RUN=没能取消视图的持久化流程，因为视图 {0} 的数据持久化还没有开始。
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_COMPLETED_PERSISTENCY_RUN=没能取消视图 {0} 的持久化流程，因为这个视图已经完成。
##XFLD: Message for failed cancellation
VIEW_PERSISTENCY_ABORTED_CANCEL_EXECUTION=没能取消视图 {0} 的持久化流程。
##XFLD: Message for finalizing persistency cancellation
VIEW_PERSISTENCY_FINALIZE_CANCEL=已经提交停止视图 {0} 的数据持久化的流程。
##XFLD: Message used in persist task when cancellation is caught
VIEW_PERSISTENCY_CANCELED=视图 {0} 的持久化流程已停止。
##XFLD: Message used in persist task when cancelation is caught and performed through a cancelation task
VIEW_PERSISTENCY_CANCELED_VIA_CANCEL_TASK=已通过取消任务 {1} 停止视图 {0} 的持久化流程。
##XFLD: Message used in cancel task when the task is triggered by deployment
VIEW_PERSISTENCY_CANCEL_TRIGGERED_BY_DEPLOYMENT=部署视图 {0} 时，正在取消数据持久化流程。
##XFLD: Message used in case the cancelation is retriggered for a running task
VIEW_PERSISTENCY_CANCEL_ATTEMPT_RETRIGGER_CANCEL=之前已提交过取消持久化视图 {0} 的任务。
##XFLD: Message used to express the delay that may occur before the persistency task is stopped
VIEW_PERSISTENCY_CANCEL_DELAY_STOPPING_PERSISTENCY=停止视图 {0} 的数据持久化任务之前可能存在延迟。
##XFLD: Message used to reflect the task log identifier of the current running persistency task
VIEW_PERSISTENCY_CANCEL_CURRENT_RUNNING_TASK=视图 {0} 的数据正在通过任务 {1} 进行持久化。
##XFLD:Message used by SEAL repository for locked partitions and DACs
VIEW_PERSISTENCY_LOCKED_PARTITIONS_DACS=通过数据访问控制（DAC）提供的权限可能已更改，不会被锁定的分区考虑。解除分区锁定并加载新快照，以应用更改。
##XFLD:Message used by SEAL repository for column structure change
VIEW_PERSISTENCY_COLUMN_STRUCTURE_CHANGE=列结构已经更改，与现有持久化表不再匹配。 请移除持久化数据并启动新的数据持久化，更新持久化表。
##XFLD: Message for out of memory
VIEW_PERSISTENCY_HANA_OUT_OF_MEMORY=SAP HANA 数据库内存不足，任务失败。
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_EXCEPTION_TRANSACTION_ROLLBACK=SAP HANA 数据库内部出现异常，任务失败。
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_SQL_EXECUTION_ISSUE=SAP HANA 数据库内部的 SQL 执行出现问题，任务失败。
##XFLD: Message for HANA out of memory result
VIEW_PERSISTENCY_OUT_OF_MEMORY_RESULT_MESSAGE=SAP HANA 出现内存不足事件的原因： {0}
##XFLD: Message for admission control rejection
VIEW_PERSISTENCY_HANA_ADMISSION_CTRL_REJECTION=SAP HANA 许可控制被拒，任务失败。
##XFLD: Message for HANA back pressure / rate limit of HANA client
VIEW_PERSISTENCY_HANA_CLIENT_RATE_LIMIT_REJECTION=活动的 SAP HANA 连接过多，任务失败。
#XMSG: Message for invalid persisted table name
VIEW_PERSISTENCY_PERSISTED_TABLE_INVALID=发生错误，持久化表已无效。要解决这个问题，请移除持久数据并重新持久化视图。
#XMSG: Message for usahe of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION=没能持久化视图。该视图使用的远程表基于已启用用户传播的远程源。请检查视图的沿袭。
#XMSG: Message for dynamic usage of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION_DYNAMIC=没能持久化视图。该视图使用的远程表基于已启用用户传播的远程源。远程表可能通过 SQL 脚本视图动态使用。视图的沿袭可能不显示远程表。
#XMSG: Message for insufficient privileges or consumption of view with DAC via dynamic SQL
VIEW_PERSISTENCY_INSUF_PRIV_OR_DAC_DYN_SQL=你可能没有足够的权限。请打开数据预览，查看是否拥有所需的权限。如果是这样，通过动态 SQL 脚本使用的第二个视图可能应用了数据访问控制（DAC）。
#XMSG: deployment of DAC is deprecated and view need to be redeployed to be able to persist data
VIEW_PERSISTENCY_DEPRECATED_DAC=视图 "{0}" 是使用即将弃用的数据访问控制（DAC）进行部署的。请重新部署视图，以便能够持久化视图的数据。
##XFLD:Message used by SEAL repository for input parameter
START_VIEW_PERSISTENCY_INPUT_PARAMETER=对输入参数 "{1}" 使用默认值 "{0}"。
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Elastic compute node keys~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
##XFLD:Message used to reflect disablement of ecn replica
ECN_DISABLE_EXISTING_REPLICA=已禁用弹性计算节点副本。
##XFLD:Message used to reflect recreation of ecn replica
ECN_RECREATE_EXISTING_REPLICA=已重新创建弹性计算节点副本。
##XFLD:Message used to reflect re-enablement of ecn replica
ECN_ENABLE_EXISTING_REPLICA=已重新启用弹性计算节点副本。
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Task Schedule Menu key~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message to schedule task
scheduleText=计划
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=创建计划
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=编辑计划
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=删除计划
#XFLD: Refresh frequency field
refreshFrequency=刷新频率
#XFLD: Refresh frequency field
refreshFrequencyNew=频率
#XFLD: Refresh frequency field
refreshFrequencyNewNew=计划频率
#XBUT: label for None
none=无
#XBUT: label for Real-Time replication state
realtime=实时
#XFLD: Label for table column
txtNextSchedule=下次运行
#XFLD: Label for table column
txtNextScheduleNew=计划下次运行
#XFLD: Label for table column
txtNumOfRecords=记录数
#XFLD: Label for scheduled link
scheduledTxt=已计划
#XFLD: LABEL for partially persisted link
partiallyPersisted=部分持久化
#XFLD: Text for paused text
paused=已暂停

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ View Monitor Tasklog Details Action keys~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message for release lock actionn
releaseLockMsg=如果运行时间比平时长，可能表明运行失败，而状态未相应更新。\r\n 要解决此问题，可以解除锁定，然后将状态设置为失败。
#XFLD: Label for release lock dialog
releaseLockText=解除锁定

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Column header tooltip ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: tooltip for table column
txtViewTooltip=持久视图的名称
#XFLD: tooltip for table column
txtViewDataAccessTooltip=这表示视图的可用性
#XFLD: tooltip for table column
txtViewRefreshFreqTooltip=这表示是否已为视图定义计划
#XFLD: tooltip for table column
txtViewStatusTooltip=获取持久视图的状态
#XFLD: tooltip for table column
txtViewLatestUpdateTooltip=提供持久视图上次更新时间的信息
#XFLD: tooltip for table column
txtViewNextRunTooltip=如果已为视图设置计划，则查看计划执行下次运行的时间。
#XFLD: tooltip for table column
txtViewNumOfRecordsTooltip=跟踪记录数。
#XFLD: tooltip for table column
txtViewInMemorySizeTooltip=跟踪视图在你的内存中占用的大小
#XFLD: tooltip for table column
txtViewDiskSizeTooltip=跟踪视图在你的磁盘中占用的大小
#XMSG: Expired text
txtExpired=已过期

#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=没能将对象 "{0}" 添加到任务链。

#~~~~~~~~~~~View Analyzer task log messages~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message indicating number of records for a view, and memory consumption in Mebibyte during simulation of view persistency
RECORDS_PEAK_MEMORY=视图 "{0}" 包含 {1} 项记录。这个视图的数据持久化模拟已经使用 {2} MiB 内存。
#XMSG: View Analyzer execution failure
VIEW_ANALYZER_EXECUTION_FAILED=视图分析器执行失败。
#XMSG: Missing authorizations for View Analyzer
VIEW_ANALYZER_MISSING_AUTH=缺少视图分析器的权限。
#XMSG: Maximum memory in Gibibyte is reached, therefore persistency simulation is stopped
MAXIMUM_MEMORY_REACHED=模拟视图 "{1}" 的数据持久化时已经达到最大内存 {0} GiB。因此，不会再运行其他数据持久化模拟。
#XMSG: Error during persistency simulation occurred
PERSISTENCY_SIMULATION_ERROR=视图 "{0}" 的数据持久化模拟期间出错。
#XMSG: Persistency simulation is not executed, because prerequisites are not fulfilled and the view cannot be persisted
PREREQUISITES_NOT_FULFILLED=没有为视图 "{0}" 执行数据持久化模拟，因为没有满足先决条件并且视图不能持久化。
#XMSG: Persistency simulation is not executed, because the view does not exist in the database
VIEW_DOES_NOT_EXIST=需要部署视图 "{0}" 才能启用数据持久化模拟。
#XMSG: Number of records cannot be determined, because the local table does not exist in the database
TABLE_DOES_NOT_EXIST=本地表 "{0}" 在数据库中不存在，所以不能确定这个表的记录数。
##XMSG: Message start of cancellation for View Analyzer task
CANCEL_VIEW_ANALYZER_TRIGGERED=已经提交停止视图 "{1}" 的视图分析器任务 {0} 的流程。在任务停止之前可能存在延迟。
##XMSG: Message start to indicate that task is not active
CANCEL_VIEW_ANALYZER_NO_ACTIVE_TASK=视图 "{1}" 的视图分析器任务 {0} 处于非活动状态。
#XMSG: Cancel View Analyzer task failure
CANCEL_VIEW_ANALYZER_FAILED=没能取消视图分析器任务。
##XMSG: Message used in View Analyzer execution task when task is canceled
VIEW_ANALYZER_CANCELED=已通过取消任务停止视图 "{0}" 的视图分析器执行。
##XMSG: Message start of cancelation for Model Validation task
CANCEL_MODEL_VALIDATION_TRIGGERED=已提交停止视图 "{1}" 的模型验证任务 {0} 的流程。在任务停止之前可能存在延迟。
##XMSG: Message to indicate that task is not active
CANCEL_MODEL_VALIDATION_NO_ACTIVE_TASK=视图 "{1}" 的模型验证任务 {0} 没有激活。
#XMSG: Cancel Model Validation task failure
CANCEL_MODEL_VALIDATION_FAILED=没能取消模型验证任务。
##XMSG: Message used in Model Validation task when task is canceled
MODEL_VALIDATION_CANCELED=已通过取消任务停止视图 "{0}" 的模型验证执行。
#XMSG: Message for Locked space error, before starting Model Validation task
MODEL_VALIDATION_LOCKED_SPACE_ERROR=没能为视图 "{0}" 执行模型验证，因为空间 "{1}" 被锁定。
##XMSG: Message used in View Analyzer execution task when determination of number of rows fails
NUMBER_ROWS_DETERMINATION_FAILED=确定本地表 "{0}" 的行数时出错。
##XMSG: Message to indicate that SQL plan was created
EXECUTION_PLAN_CREATED=已经为视图 "{0}" 创建 SQL 分析器计划文件，可以下载。
#XFLD: Message to indicate starting of process to generate SQL Analyzer plan file
START_PLANVIZ_EXECUTION=开始执行为视图 "{0}" 生成 SQL 分析器计划文件的流程。
#XFLD: Message to indicate starting of View Analyzer process
VIEW_ANALYZER_EXECUTION_STARTED=开始执行视图分析器任务。
##XMSG: Message to indicate that SQL plan file cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_PLANVIZ_NOT_FULFILLED=没能为视图 "{0}" 生成 SQL 分析器计划文件，因为没有满足数据持久化先决条件。`
##XMSG: Error message during  generation of SQL Analyzer plan file
PLANVIZ_ERROR=为视图 "{0}" 生成 SQL 分析器计划文件时出错。
#XMSG: Message for Locked space error
VIEW_ANALYZER_LOCKED_SPACE_ERROR=没能为视图 "{0}" 执行视图分析器，因为空间 "{1}" 被锁定。
#XMSG: Message for persistency simulation without partitioning
PERSISTENCY_SIMULATION_WITHOUT_PARTITIONS=模拟数据持久化时没有考虑视图 "{0}" 的分区。
#XMSG: Message for generation of SQL Analyzer plan file without considering partitioning
PLANVIZ_WITHOUT_PARTITIONS=生成 SQL 分析器计划文件时没有考虑视图 "{0}" 的分区。
#XMSG: Confirmation message for mass removal of persisted data
MassRemovePersistency_Confirm_Msg=是否要移除持久数据，并将数据访问切换回虚拟访问？
#XMSG: Confirmation message for partial mass removal of selected persisted data
PartialMassRemovePersistency_Confirm_Msg={1} 个选定对象中有 {0} 个已获得持久数据。\n是否要移除持久数据，并将数据访问切换回虚拟访问？
#XMSG: success message for stopping mass persistence
stopMassPersistenceSuccess=正在移除选定视图的持久数据。
#XMSG: error message during mass stop persistence of a view (backend error)
STOP_MASS_PERSISTENCE_ERROR=停止选定视图的持久化时出错。
#XMSG: memory analysis is only performed in space {0}
NO_MEMORY_ANALYSIS=只对空间 "{0}" 中的实体执行内存分析：已跳过 "{1}" "{2}"。
##XMSG: Message to indicate that Explain Plan cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_EXPLAINPLAN_NOT_FULFILLED=没能为视图 "{0}" 生成解释计划文件，因为没有满足持久化先决条件。
#XMSG: Message for Explain Plan generation without partitioning
EXPLAINPLAN_WITHOUT_PARTITIONS=生成解释计划文件时没有考虑视图 "{0}" 的分区。
#XFLD: Message to indicate starting of process to generate Explain Plan file
START_EXPLAINPLAN_EXECUTION=开始执行为视图 "{0}" 生成解释计划文件的流程。
##XMSG: Message to indicate that Explain Plan was created
EXPLAINPLAN_CREATED=视图 "{0}" 的解释计划文件已生成。点击 "查看详细信息" 可以显示该文件，如果有相关权限，也可以下载。
##XMSG: Error message during  generation of Explain Plan file
EXPLAINPLAN_ERROR=为视图 "{0}" 生成解释计划文件时出错。
##XMSG: Explain Plan file cannot be generated for the view {0}, because too many views are stacked on each other
EXPLAINPLAN_MODEL_TOO_COMPLEX=没能为视图 "{0}" 生成解释计划文件。太多视图相互堆叠。复杂的模型可能会导致内存溢出错误和性能下降。建议持久化视图。

##XMSG: Message to indicate that the space is locked
PERFORMANCE_ANALYSIS_LOCKED_SPACE_ERROR=没能为视图 "{0}" 执行性能分析，因为空间 "{1}" 被锁定。
##XMSG: The performance analysis task was cancelled
PERFORMANCE_ANALYSIS_CANCELED=视图 "{0}" 的性能分析已取消。
##XMSG: The performance analysis failed
PERFORMANCE_ANALYSIS_FAILED=性能分析失败。
##XMSG: The performance analysis is finished
PERFORMANCE_ANALYSIS_FINISHED=视图 "{0}" 的性能分析已完成。请点击 "查看详细信息"，显示分析结果。
##XMSG: The view has input parameter with no default value
INPUT_PARAMETER_WITHOUT_DEFAULT_VALUE=没能分析这个视图，它的某个参数缺少默认值。
##XMSG: The view is not fully deployed
VIEW_NOT_FULLY_DEPLOYED=没能分析这个视图，它还没有完全部署。
##XMSG: The view uses limited remote adapters
LIMITED_REMOTE_ADAPTER=这个视图使用了至少一个功能受限的远程适配器，比如缺少筛选器下推或 '计数' 支持。持久化或复制对象可以提高运行时性能。
##XMSG: The view uses remote adapter that does not support 'Limit'
REMOTE_ADAPTER_NOT_SUPPORTING_LIMIT=这个视图使用了至少一个不支持 '限制' 的远程适配器。可能已选择超过 1000 条记录。
##XMSG: Performance analysis is executed by using default parameters of view'
INPUT_PARAMETER_WITH_DEFAULT_VALUE=性能分析是通过使用视图参数的默认值来执行的。
##XMSG: Message to indicate that an error occurred
PERFORMANCE_ANALYSIS_ERROR=对视图 "{0}" 执行性能分析时出错。
##XMSG: Message start of cancellation for Performance Analysis task
CANCEL_PERFORMANCE_ANALYSIS_TRIGGERED=已经提交停止视图 "{1}" 的性能分析任务 {0} 的请求。任务停止可能会有一定的延迟。
##XMSG: Message start to indicate that task is not active
CANCEL_PERFORMANCE_ANALYSIS_NO_ACTIVE_TASK=视图 "{1}" 的性能分析任务 {0} 处于非活动状态。
#XMSG: Cancel Performance Analysis task failure
CANCEL_PERFORMANCE_ANALYSIS_FAILED=没能取消性能分析任务。

#XBUT: Assign schedule menu button label
assignScheduleLabel=将计划分配给我
#XBUT: Pause schedule menu label
pauseScheduleLabel=暂停计划
#XBUT: Resume schedule menu label
resumeScheduleLabel=恢复计划
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=移除计划时出错。
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=分配计划时出错。
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=暂停计划时出错。
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=恢复计划时出错。
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=正在删除 {0} 个计划
#XMSG: Message for starting mass assign of schedules
massAssignStarted=正在更改 {0} 个计划的所有者
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=正在暂停 {0} 个计划
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=正在恢复 {0} 个计划
#XBUT: Select Columns Button
selectColumnsBtn=选择列
#XFLD: Refresh tooltip
TEXT_REFRESH=刷新
#XFLD: Select Columns tooltip
text_selectColumns=选择列


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Runtime Metrics ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Label for Runtime Metrics
RuntimeMetricsTitle=运行时指标
#XFLD : Label for Run Button
runButton=运行
#XFLD : Label for Cancel Button
cancelButton=取消
#XFLD : Label for Close Button
closeButton=关闭
#XFLD : Label for Open View Analyzer Button
openViewAnalyzerButton=打开视图分析器
#XFLD : Label for Generate Explain Plan Button
generateExplainPlanButton=生成解释计划
#XFLD : Label for Previous Run Column
previousRun=先前运行
#XFLD : Label for Latest Run Column
latestRun=最新运行
#XFLD : Label for time Column
time=时间
#XFLD : Label for Duration Column
duration=持续时间
#XFLD : Label for Peak Memory Column
peakMemory=峰值内存
#XFLD : Label for Number of Rows
numberOfRows=行数
#XFLD : Label for Overall Number of Sources
overallNumberOfSources=源总数
#XFLD : Label for Data Access Column
dataAccess=数据访问
#XFLD : Label for Local Tables
localTables=本地表
#XFLD : Label for federatedRemoteTables
federatedRemoteTables=联合远程表（具备有限的适配器功能）
#XTXT Text for initial state of the runtime metrics
initialState=需先运行性能分析，才能获取指标。此过程可能需要一些时间，但可以随时取消。
#XMSG: Cancel confirmation message
CancelRunConfirmationMessage=是否确定要取消当前运行的性能分析？
#XTIT: Cancel dialog title
CancelRunTitle=取消运行
#XFLD: Label for Number of Rows
NUMBER_ROWS=行数
#XFLD: Label for Overall Number of Sources
OVERALL_NUMBER_SOURCES=源总数
#XFLD: Label for Data Access
DATA_ACCESS=数据访问
#XFLD: Label for Remote tables
NUMBER_REMOTE_TABLES_LIMITED_ADAPTERS=联合远程表（具备有限的适配器功能）
#XFLD: Label for select statement
SELECT_STATEMENT='SELECT * FROM VIEW LIMIT 1000'
#XFLD: Label for duration
SELECT_RUNTIME=持续时间
#XFLD: Label for peak memory
SELECT_PEAK_MEMORY=峰值内存
#XFLD: Label for count statement
SELECT_COUNT_STATEMENT='SELECT COUNT(*) FROM VIEW'
#XFLD: Label for count duration
SELECT_COUNT_RUNTIME=持续时间
#XFLD: Label for count peak memory
SELECT_COUNT_PEAK_MEMORY=峰值内存
#XFLD: Label for count of number of local tables
NUMBER_LOCAL_TABLES_FILE=本地表（文件）
#XTXT: Text for running state of the runtime metrics
Running=正在运行...
#XFLD: Label for time
Time=时间
#XFLD: Label for virtual access
PA_VIRTUAL=虚拟
#XFLD: Label for persisted access
PA_PERSISTED=已持久化
PA_PARTIALLY_PERSISTED=部分持久化
#XTXT: Text for cancel
CancelRunSuccessMessage=正在取消运行性能分析。
#XTXT: Text for cancel error
CancelRunErrorMessage=取消运行性能分析时出错。
#XTXT: Text for explain plan generation
ExplainPlanStarted=正在为视图 "{0}" 生成解释计划。
#XTXT: Text for performance analysis
PerformanceAnalysisStarted=正在开始对视图 "{0}" 执行性能分析。
#XTXT: Text for performance analysis error
ErrorFetchingPerformanceAnalysis=获取性能分析数据时出错。
#XTXT: Text for performance analysis error
conflictingTask=性能分析任务已在运行中
#XFLD: Label for Errors
Errors=错误
#XFLD: Label for Warnings
Warnings=警告
#XFLD: Tooltip for missing privileges
openViewAnalyzerButtonTooltipNoPrivilege=需要 DWC_DATAINTEGRATION(update) 权限才能打开视图分析器。
#XFLD: Tooltip for missing privileges
generateExplainPlanButtonTooltipNoPrivilege=需要 DWC_RUNTIME(read) 权限才能生成解释计划。



#XFLD: Label for frequency column
everyLabel=每
#XFLD: Plural Recurrence text for Hour
hoursLabel=小时
#XFLD: Plural Recurrence text for Day
daysLabel=天
#XFLD: Plural Recurrence text for Month
monthsLabel=个月
#XFLD: Plural Recurrence text for Minutes
minutesLabel=分钟
