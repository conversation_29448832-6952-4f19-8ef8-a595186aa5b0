
#XBCB: Breadcrumb
viewMonitorBreadcrumb={spaceId}

#XFLD: Label for persisted view column
SOURCE=Ffynhonnell
#XFLD: Label for persisted view column
NAME=Enw
#XFLD: Label for persisted view column
NAME_LABEL=Enw Busnes
#XFLD: Label for persisted view column
NAME_LABELNew=<PERSON><PERSON><PERSON><PERSON><PERSON> (Enw'r Busnes)
#XFLD: Label for persisted view column
TECHINCAL_NAME=Enw Technegol
#XFLD: Label for persisted view column
TECHINCAL_NAMENew=Gwrthry<PERSON> (Enw Technegol)
#XFLD: Label for persisted view column
DATA_PERSISTENCY=Mynediad Data
#XFLD: Label for persisted view column
STATUS=Statws
#XFLD: Label for persisted view column
LAST_UPDATED=Diweddarwyd Ddiwethaf
#XFLD: Label for persisted view column
IN_MEMORY_SIZE=Cof wedi'i ddefnyddio ar gyfer Storio (MiB)
#XFLD: Label for persisted view column
DISK_SIZE=Disg wedi'i defnyddio ar gyfer <PERSON>orio (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_NEWW=Maint yn y Cof (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_HEADER=Maint yn y Cof
#XFLD: Label for persisted view column
DISK_SIZE_NEWW=Maint ar y Disg (MiB)
#XFLD: Label for persisted view column
DISK_SIZE_HEADER=Maint ar y Disg
#XFLD: Label for schedule owner column
txtScheduleOwner=Perchennog Amserlen
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Dangos pwy sydd wedi creu'r amserlen
#XFLD: Label for persisted view column
PERSISTED=Parhaol
#XFLD: Label for persisted view column
TYPE=Math
#XFLD: Label for View Selection Dialog column
changedOn=Wedi'i Newid Ar
#XFLD: Label for View Selection Dialog column
createdBy=Wedi'i Greu Gan
#XFLD: Label for log details column
txtViewPersistencyLogs=Gweld Logiau
#XFLD: Label for log details column
txtViewPersistencyLogsNew=Manylion
#XFLD: text for values shown for Ascending sort order
SortInAsc=Trefnu o'r Dechrau i'r Diwedd
#XFLD: text for values shown for Descending sort order
SortInDesc=Trefnu o'r Diwedd i'r Dechrau
#XFLD: Label for persisted view monitor heading
ViewMonitor_Heading=Monitor Gweddau
#XFLD: Label for persisted view monitor sub-heading
ViewMonitor_SubHeading=Monitro a Chynnal Parhad Data Gweddau


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ DynamicPageHeader ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: text for values shown in column Persistence Status
txtLoading=Wrthi'n Llwytho
#XFLD: text for values shown in column Persistence Status
txtRunning=Yn Rhedeg
#XFLD: text for values shown in column Persistence Status
txtAvailable=Ar Gael
#XFLD: text for values shown in column Persistence Status
txtError=Gwall
#XMSG: Error message for unknown Persistence type that gets logged in Kibana
unknownReplicationTypeError=Dim modd delio â math dyblygu "{0}".

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Content ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Gosodiadau a ddefnyddiwyd ar gyfer y broses rhedeg parhau data ddiwethaf:
#XMSG: Message for input parameter name
inputParameterLabel=Paramedr Mewnbwn
#XMSG: Message for input parameter value
inputParameterValueLabel=Gwerth
#XMSG: Message for persisted data
inputParameterPersistedLabel=Wedi Parhau Yn
#XTIT: Count for View in ViewMonitor Toolbar
txtViewCount=Gweddau ({0})
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Parhad Gwedd
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Parhad Data
#XBUT: Button to clear the selected view persistance
ClearViewPersistance=Clir
#XBUT: Button to stop the selected view persistance
stopPersistance=Stopio Parhad
#XFLD: Placeholder for Search field
txtSearch=Chwilio
#XBUT: Tooltip for refresh button
txtRefresh=Adnewyddu
#XBUT: Tooltip for add view button
txtDeleteView=Dileu Parhad
#XBUT: Tooltip for load new peristence
loadNewPersistence=Ail-ddechrau Parhad
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Llwytho Ciplun Newydd
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Dechrau Parhad Data
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Tynnu Data Parhad
#XMSG: success message for starting persistence
startPersistenceSuccess=Rydyn ni''n parhau â’r wedd "{0}".
#XMSG: success message for stopping persistence
stopPersistenceSuccess=Rydyn ni''n tynnu data sydd wedi''i barhau ar gyfer gwedd "{0}".
#XMSG: success message for stopping persistence
deletePersistedViewSuccess=Rydyn ni''n tynnu gwedd "{0}" o''r rhestr fonitro.
#XMSG: error message during start persistence of a view (backend error)
ERROR_STARTING_PERSISTENCE=Roedd gwall wrth ddechrau parhad data ar gyfer gwedd "{0}".
#XMSG: error message during start persistence of a view (backend error)
PARAMERROR_STARTING_PERSISTENCE=Dydy gwedd "{0}" ddim yn gymwys i''w pharhau oherwydd bod ganddi baramedrau mewnbwn.
#XMSG: error message during start persistence of a view (backend error)
NUMBER_OF_PARAMETER_STARTING_PERSISTENCE=Dydy gwedd "{0}" ddim yn gymwys i''w pharhau oherwydd bod ganddi fwy nag un paramedr mewnbwn.
#XMSG: error message during start persistence of a view (backend error)
PARAMETER_DEFAULT_VALUE_STARTING_PERSISTENCE=Dydy gwedd "{0}" ddim yn gymwys i''w pharhau oherwydd nad oes gan y paramedr mewnbwn werth diofyn.
#XMSG: error message during start persistence of a view (backend error)
REDEPLOYMENT_DAC_REQUIRED=Mae angen ailosod y Rheolydd Mynediad Data (DAC) "{0}" er mwyn delio â pharhad data.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_STARTING_PERSISTENCE=Dim modd parhau â’r wedd "{0}" am ei bod yn defnyddio''r wedd "{1}", sydd â Rheolydd Mynediad Data (DAC).
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_EXTERNAL_STARTING_PERSISTENCE=Dim modd parhau â''r wedd "{0}" am ei bod yn defnyddio gwedd sydd â Rheolydd Mynediad Data (DAC) sy''n perthyn i ofod gwahanol.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_STRUCTURE_STARTING_PERSISTENCE=Does dim modd parhau â''r wedd "{0}" oherwydd bod strwythur un neu ragor o’i Rheolyddion Mynediad Data (DAC) ddim yn gallu delio â pharhad data.
#XMSG: error message during stop persistence of a view (backend error)
STOP_PERSISTENCE_ERROR=Roedd gwall wrth stopio parhad ar gyfer gwedd "{0}".
#XMSG: error message during stop persistence of a view (backend error)
DELETE_PERSISTED_VIEW_ERROR=Roedd gwall wrth ddileu''r wedd wedi''i pharhau "{0}".
#XMSG: Warning message for removing the persisted data
RemovePersistency_Confirm_Msg=Ydych chi am ddileu''r data parhad a newid i fynediad rhithwir o wedd "{0}"?
#XMSG: Warning message for delete the view persistence
DeletePersistency_Confirm_Msg=Ydych chi am dynnu''r wedd o''r rhestr fonitro a dileu''r data parhad o wedd "{0}"?
#XMSG: error message for reading data from backend
txtReadBackendError=Roedd gwall wrth ddarllen o'r cefn.
#XFLD: Label for No Data Error
NoDataError=Gwall
#XMSG: message for conflicting task
Task_Already_Running=Mae tasg sy’n gwrthdaro yn rhedeg yn barod ar gyfer y wedd "{0}".

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Show All Views~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Text for show all views button
allViewsText=Pob Gwedd ({0})
#XBUT: Text for show scheduled views button
scheduledText=Wedi Amserlennu ({0})
#XBUT: Text for show persisted views button
persistedText=Wedi Parhau ({0})
#XBUT: Text for start analyzer button
startAnalyzer=Dechrau Dadansoddwr Gweddau
#XFLD: Message if repository is unavailable
repositoryErrorMsg=Dydy'r ystorfa ddim ar gael ac mae nodweddion penodol wedi'u hanalluogi.

#XFLD: Data Access - Virtual
Virtual=Rhithiol
#XFLD: Data Access - Persisted
Persisted=Parhaol

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Non-Persisted View Selection Dialog~~~~~~~~~~~~~~~~~~~~~~~

#XTIT: Title for Non-Persisted View selection dialog
select_source=Dewiswch Wedd i'w Barhau

#XTIT: Placeholder for Non-Persisted View selection dialog search
searchView=Chwilio Gweddau
#XTIT: No data in the list of non-persisted view
No_Data=Dim Data
#XBUT: Button to select non-persisted view
ok=Iawn
#XBUT: Button to close the non-persisted views selection dialog
cancel=Canslo

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~View persistency log message key~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message for view persistency logs
START_STOP_PERSISTENCY=Dechrau tasg parhad data ar gyfer "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY=Wrthi''n parhau data ar gyfer y wedd ''{1}''.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY)
START_VIEW_PERSISTENCY_PROCESS=Dechrau''r broses i barhau data ar gyfer y wedd "{0}".
#XFLD: Message for view persistency manual partition load
START_VIEW_PERSISTENCY_PROCESS_MANUAL_PARTITIONS=Dechrau''r broses i barhau data ar gyfer y wedd "{0}" gydag IDs rhaniad dan sylw: "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY=Tynnu data parhad ar gyfer y wedd "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY)
REMOVE_VIEW_PERSISTENCY=Dechrau''r broses i ddileu parhad data ar gyfer y wedd "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_SUCCESS=Data wedi''i barhau ar gyfer gwedd "{1}".
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_SUCCESS)
START_VIEW_PERSISTENCY_PROCESS_SUCCESS=Data wedi''i barhau ar gyfer gwedd "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY_SUCCESS=Data parhad wedi''i dynnu a mynediad data rhithwir wedi''i adfer ar gyfer gwedd "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_SUCCESS)
REMOVE_VIEW_PERSISTENCY_SUCCESS=Wedi cwblhau''r broses o ddileu parhad data ar gyfer y wedd "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_FAILED=Dim modd parhau''r data ar gyfer y wedd "{1}".
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_FAILED)
START_VIEW_PERSISTENCY_PROCESS_FAILED=Dim modd parhau''r data ar gyfer y wedd "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.05)
STOP_VIEW_PERSISTENCY_FAILED=Dim modd tynnu data parhad ar gyfer y wedd "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_FAILED)
REMOVE_VIEW_PERSISTENCY_FAILED=Dim modd tynnu data parhad ar gyfer y wedd "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_SUCCESS="{3}" cofnod wedi''i barhau ar gyfer gwedd "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace START_PERSISTENCY_RECORD_COUNT_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS={0} cofnod wedi’i roi yn nhabl parhad data ar gyfer gwedd "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS_V2={0} cofnod wedi''i fewnosod yn nhabl parhad data ar gyfer y wedd "{1}". Cof a ddefnyddiwyd: {2} GiB.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_SUCCESS="{3}" cofnod wedi''i barhau wedi''i dynnu ar gyfer gwedd "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace STOP_PERSISTENCY_RECORD_COUNT_SUCCESS)
REMOVE_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=Data parhad wedi’i dynnu, "{0}" cofnod parhad wedi’i ddileu.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_FAILED=Cael recordCount wedi methu ar gyfer y wedd ''{1}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_FAILED=Cael recordCount wedi methu ar gyfer y wedd ''{1}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Amserlen wedi''i dileu ar gyfer "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS)
REMOVE_VIEW_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Amserlen wedi''i dileu ar gyfer gwedd "{0}".
#XFLD: Message for view persistency logs
STOP_PERSISTENCY_DELETE_SCHEDULE_FAILED=Proses dileu amserlen wedi methu ar gyfer "{1}".
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_REDEPLOY=Does dim modd i ni barhau gwedd "{0}" oherwydd mae wedi cael ei newid a’i osod ers i chi ddechrau ei pharhau. Rhowch gynnig arall ar barhau’r wedd neu arhoswch nes y rhediad nesaf sydd wedi’i drefnu.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_DELETE=Does dim modd i ni barhau gwedd "{0}" oherwydd mae wedi cael ei dileu ers i chi ddechrau ei pharhau.
#XFLD: Message for successful data persistency of a single partition (for messages created before version 2022.03)
START_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} cofnod wedi''i barhau mewn rhaniad ar gyfer gwerthoedd "{1}" <= {2} < "{3}".
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (new message to replace START_PERSISTENCY_SINGLE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} cofnod wedi''i roi mewn rhaniad ar gyfer gwerthoedd "{1}" <= {2} < "{3}".
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS_V2={0} cofnod wedi''i fewnosod mewn rhaniad ar gyfer gwerthoedd "{1}" <= "{2}" < "{3}". Cof a ddefnyddiwyd: {4} GiB.
#XFLD: Message for successful replication of a "others" partition (for messages created before version 2022.03)
START_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} cofnod wedi''i barhau i raniad "eraill".
#XFLD: Message used by SEAL repository for successful replication of a "others" partition (new message to replace START_PERSISTENCY_OTHERS_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} cofnod wedi’i roi mewn rhaniad "eraill".
#XFLD: Message for view persistency record count of partitions (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={3} cofnod wedi''i barhau ar gyfer y wedd "{1}" mewn {4} rhaniad.
#XFLD: Message used by SEAL repository for view persistency record count of partitions (new message to replace START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={0} cofnod wedi’i roi yn nhabl parhad data ar gyfer gwedd "{1}" mewn {2} rhaniad.
#XFLD: Message used by SEAL repository for view persistency for partition wise refresh
START_VIEW_PERSISTENCY_RECORD_COUNT_REFRESHED_PARTITIONS_SUCCESS=Mae {0} cofnod wedi''i roi yn nhabl parhad data ar gyfer y wedd "{1}". Rhaniadau wedi''u diweddaru: {2}; Rhaniadau wedi''u cloi: {3}; Cyfanswm rhaniadau: {4}
#XFLD: Message used by SEAL repository for view persistency for manual partition load
START_VIEW_PERSISTENCY_RECORD_COUNT_MANUAL_PARTITIONS_SUCCESS={0} cofnod wedi''i osod mewn tabl parhad data ar gyfer gwedd "{1}" mewn {2} rhaniad wedi''i ddewis.
#XFLD: Message used by SEAL repository for view persistency for locked partition data load
START_VIEW_PERSISTENCY_RECORD_COUNT_LOCKED_PARTITIONS_SUCCESS=Mae {0} cofnod wedi''i mewn tabl parhad data ar gyfer y wedd "{1}". Rhaniadau wedi''u diweddaru: {2}; Rhaniadau heb eu newid sydd wedi''u cloi: {3}; Cyfanswm rhaniadau: {4}
#XMSG: Message for unexpected error (for messages created before version 2022.03)
START_PERSISTENCY_UNEXPECTED_ERROR=Gwall annisgwyl wrth barhau â’r data ar gyfer gwedd "{0}".
#XMSG: Message for unexpected error (new message to replace START_PERSISTENCY_UNEXPECTED_ERROR)
START_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Gwall annisgwyl wrth barhau â’r data ar gyfer gwedd "{0}".
#XMSG: Message for Locked space error
START_VIEW_PERSISTENCY_LOCKED_SPACE_ERROR=Does dim modd parhau gwedd "{0}" oherwydd bod y gofod "{1}" wedi’i gloi.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
STOP_PERSISTENCY_UNEXPECTED_ERROR=Gwall annisgwyl wrth dynnu data sydd wedi parhau ar gyfer gwedd "{0}".
#XMSG: Message for unexpected error (new message to replace STOP_PERSISTENCY_UNEXPECTED_ERROR)
REMOVE_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Gwall annisgwyl wrth dynnu parhad ar gyfer gwedd "{0}".
#XMSG: Message for ivalidated view
VIEW_PERSISTENCY_ABORTED_DEFINITION_INVALID=Mae ddiffiniad o wedd "{0}" yn annilys erbyn hyn oherwydd, fwyaf tebyg, newid gwrthrych sy’n cael ei ddefnyddio’n uniongyrchol neu’n anuniongyrchol gan y wedd. Ceisiwch ail-osod y wedd i ddatrys y broblem, neu i weld beth sy’n achosi hyn.
#XMSG: Message for persisted data removal (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT=Data parhad wedi''i dynnu wrth osod gwedd "{0}".
#XMSG: Message for persisted data removal of a consuming view
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW=Data parhad wedi''i dynnu wrth osod y wedd a ddefnyddiwyd "{0}".
#XMSG: Message for persisted data removal of a consuming view due to a dac change
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW_DAC_CHANGED=Mae data parhad yn cael ei dynnu wrth osod y wedd a ddefnyddiwyd "{0}" oherwydd bod ei reolydd mynediad data wedi newid.
#XMSG: Message for persisted data removal (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DEPLOYMENT=Data parhad wedi''i dynnu wrth osod gwedd "{0}".
#XMSG: Message for deleted view (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION=Parhad wedi''i dynnu wrth ddileu gwedd "{0}".
#XMSG: Message for deleted view (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DELETION=Parhad wedi''i dynnu wrth ddileu gwedd "{0}".
#XMSG Message for removal of persisted data, because prerequisites not fulfilled
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_PREREQUISITES_NOT_FULFILLED=Mae'r data sydd wedi'i barhau wedi'i dynnu, oherwydd nad yw rhagofynion parhau data yn cael eu bodloni mwyach.
#XMSG: Message for inconsistent persistency
VIEW_PERSISTENCY_INCONSISTENT_PERSISTENCY=Mae parhad gwedd "{0}" yn anghyson bellach. Tynnwch data sydd wedi''i barhau i ddatrys y broblem.
#XMSG: Message for prerequisites
START_PERSISTENCY_CHECK_PREREQUISITES=Gwirio’r rhagofynion ar gyfer parhad y wedd "{0}".
#XMSG: Message for DAC deployed in deprecated way using table function and join (for messages created before 2025.05)
START_PERSISTENCY_DEPRECIATED_DAC=Mae''r wedd "{0}" yn cael ei defnyddio drwy Reolaeth Mynediad Data (DAC) sy''n cael ei ddatgymeradwyo. Defnyddiwch y wedd eto i wella''r perfformiad.
#XMSG: Message for DAC deployed in deprecated way using table function and join
START_PERSISTENCY_DEPRECATED_DAC=Mae''r wedd "{0}" yn cael ei defnyddio drwy Reolaeth Mynediad Data (DAC) sy''n cael ei ddatgymeradwyo. Defnyddiwch y wedd eto i wella''r perfformiad.
#XMSG: Message for roll back (for messages created before version 2022.03)
ROLLBACK_VIEW_PERSISTENCY=Gwall. Cyflwr blaenorol y broses barhad wedi’i hadfer ar gyfer y wedd "{0}".
#XMSG: Message used by SEAL repository for rollback of running procedure to persist view (new message to replace ROLLBACK_VIEW_PERSISTENCY)
VIEW_PERSISTENCY_ROLLBACK=Gwall. Proses flaenorol y wedd ''{0}'' wedi''i stopio ac mae''r newidiadau wedi cael eu dadwneud.
#XMSG: Message used by SEAL repository for rollback of running procedure to remove persisted data of view
REMOVE_VIEW_PERSISTENCY_ROLLBACK=Gwall. Mae''r broses i gael gwared ar barhad data o''r wedd "{0}" wedi''i stopio, ac mae''r newidiadau wedi cael eu dadwneud.
#XMSG: Message used by SEAL repository for persistency preparation
START_PERSISTENCY_PREPARATION=Paratoi i barhau â’r data.
#XMSG: Message used by SEAL repository for data insertion
START_INSERTING_PERSISTENCY_DATA=Gosod data mewn tabl parhad.
#XMSG: Message for null values - partitioning (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS={0} cofnod gwerth nwl wedi’i roi mewn rhaniad "eraill".
#XMSG: Message used by SEAL repository for others partition values - partitioning (new message to replace START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS={0} cofnod wedi''i roi mewn rhaniad "eraill" ar gyfer gwerthoedd "{2}" < "{1}" NEU "{2}" >= "{3}".
#XMSG: Message used by SEAL repository for others partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS_V2={0} cofnod wedi''i fewnosod mewn rhaniad "eraill" ar gyfer gwerthoedd "{2}" < "{1}" NEU "{2}" >= "{3}". Cof a ddefnyddiwyd: {4} GiB.
#XMSG: Message used by SEAL repository for null partition values - partitioning
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS={0} cofnod wedi''i roi mewn rhaniad "eraill" ar gyfer gwerthoedd "{1}" YN NWL.
#XMSG: Message used by SEAL repository for null partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS_V2={0} cofnod wedi''i roi mewn rhaniad "eraill" ar gyfer gwerthoedd "{1}" YN NWL. Cof a ddefnyddiwyd: {2} GiB.
##XFLD:Message used by SEAL repository for remote statistics
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS=Wrthi’n llwytho’r data cysylltiedig: {0} datganiad pell. Cyfanswm y cofnodion wedi’i nôl: {1}. Cyfanswm amser: {2} eiliad.
##XFLD:Message used by SEAL repository for remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS_WITH_PARTITIONS=Wrthi’n llwytho’r data cysylltiedig gan ddefnyddio {0} rhaniad gyda {1} datganiad pell. Cyfanswm y cofnodion wedi’i nôl: {2}. Cyfanswm amser: {3} eiliad.
##XFLD:Message used by SEAL repository with UI ID for instructions of remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_INSTRUCTION=Mae modd dangos y datganiadau pell a gafodd eu prosesu yn ystod y rhediad drwy agor y monitor ymholiadau pell, ym manylion y negeseuon penodol i raniad.
##XFLD:Message for start reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS=Dechrau’r broses i ailddefnyddio data wedi’i barhau cyfredol ar gyfer gwedd {0} ar ol ei gosod.
##XFLD:Message used by SEAL repository for reattach view persitency
START_VIEW_PERSISTENCY_REATTACHMENT=Dechrau ailddefnyddio’r data wedi’i barhau cyfredol.
##XFLD:Message used by SEAL repository for reattach view persitency
START_FINILIZING_VIEW_PERSISTENCY_REATTACHMENT=Ailddefnyddio’r data wedi’i barhau cyfredol.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_COMPLETE=Mae’r broses o ailddefnyddio’r data wedi’i barhau cyfredol wedi’i gwblhau ar gyfer y wedd {0}.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_FAILED=Wedi methu ailddefnyddio’r data wedi’i barhau cyfredol ar gyfer gwedd {0} ar ôl ei gosod.
#XMSG: Message used by SEAL repository for finalizing persistency
START_FINALIZING_PERSISTENCY=Wrthi’n gorffen proses parhad.
#XMSG: Message used by SEAL repository for restoring virtual access for view
REMOVE_VIEW_PERSISTENCY_VIRTUAL_ACCESS_RESTORED=Mynediad at ddata rhithwir wedi''i adfer ar gyfer gwedd "{0}".
#XMSG: Message used by SEAL repository for view that already has virtual access
REMOVE_VIEW_PERSISTENCY_ALREADY_VIRTUAL_ACCESS=Mae gan y wedd "{0}" fynediad data rhithwir yn barod. Nid oes unrhyw parhad data yn cael ei ddileu.
#XMSG: Message for removing view persistency
VIEW_PERSISTENCY_REMOVED_FROM_MONITOR=Mae gwedd "{0}" wedi cael ei thynnu o''r Monitor Gweddau.
#XMSG: Message for view doesnt exist.
VIEW_PERSISTENCY_VIEW_NOT_EXISTING=Dydy''r wedd "{0}" naill ai ddim yn bodoli yn y gronfa ddata neu dydy hi ddim wedi''i gosod yn gywir, ac felly does dim modd ei pharhau. Ceisiwch ailosod y wedd i ddatrys y broblem, neu i weld beth sy''n achosi''r broblem.
#XMSG: Message for persistency not enabled.
VIEW_PERSISTENCY_NOT_ENABLED=Parhad data heb ei roi ar waith. Dylech ailosod tabl/gwedd yn y gofod "{0}" i alluogi''r swyddogaeth.
#XMSG: Message for unexpected error due to node crash
VIEW_PERSISTENCY_UNEXPECTED_ERROR_GENERAL=Gwallau technegol wedi amharu ar redeg y broses parhad gweddau ddiwethaf.
#XFLD: Message for view persistency memory consumption
VIEW_PERSISTENCY_MEMORY_CONSUMPTION={0} GiB o uchafswm cof sydd wedi''i ddefnyddio yn amser rhedeg parhad gweddau.
#XFLD: Message for view persistency timeout
VIEW_PERSISTENCY_TIMEOUT=Parhad gwedd {0} wedi cyrraedd y terfyn amser sef {1} awr.
#XFLD: Message for not started asynchronous view persistency
VIEW_PERSISTENCY_ASYNC_NOT_STARTED=Mae llwyth mawr yn y system wedi atal proses ansyncronaidd parhad gweddau rhag cychwyn. Gwiriwch i weld a oes gormod dasgau ar waith ar yr un pryd.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency (for messages created before version 2022.05)
START_VIEW_PERSISTENCY_EXECUTION_METHOD_DROP_AND_RENAME=Mae tabl parhad a oedd yn bodoli wedi cael ei ddileu ac wedi'i ddisodli gan dabl parhad newydd.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_RECREATE_AND_DROP=Mae tabl parhad a oedd yn bodoli wedi cael ei ddileu ac wedi'i ddisodli gan dabl parhad newydd.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_TRUNCATE_AND_COPY=Mae tabl parhad a oedd yn bodoli wedi cael ei ddiweddaru â data newydd.
##XFLD: Message for missing authorizations for view persistency
VIEW_PERSISTENCY_MISSING_AUTH=Awdurdodiadau coll ar gyfer parhad data.
##XFLD: Message start of persistency cancellation
VIEW_PERSISTENCY_TRIGGER_CANCEL=Dechrau i ganslo’r broses o barhau gwedd {0}.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_UNAVAILABLE_TASK_RUN=Wedi methu canslo’r broses i barhau â’r wedd gan nad oes tasg barhau data yn rhedeg ar gyfer y wedd {0}.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_NONDETERMINISTIC_TASK_RUN=Wedi methu canslo’r broses i barhau â’r wedd gan nad oes tasg barhau data yn rhedeg ar gyfer y wedd {0}.
##XFLD: Message for canceling a non running persitency task
VIEW_PERSISTENCY_CANCEL_NON_RUNNING_SELECTED_TASK=Wedi methu canslo’r broses i barhau â’r wedd {0} oherwydd nad ydy''r dasg parhau data {1} dan sylw yn rhedeg.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_UNREACHABLE_PERSISTENCY_RUN=Wedi methu canslo’r broses i barhau â’r wedd gan nad yw parhad data ar gyfer y wedd {0} wedi dechrau eto.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_COMPLETED_PERSISTENCY_RUN=Wedi methu canslo’r broses i barhau â’r wedd {0} oherwydd ei fod wedi cael ei gwblhau’n barod.
##XFLD: Message for failed cancellation
VIEW_PERSISTENCY_ABORTED_CANCEL_EXECUTION=Wedi methu canslo’r broses o barhau gwedd {0}.
##XFLD: Message for finalizing persistency cancellation
VIEW_PERSISTENCY_FINALIZE_CANCEL=Mae’r broses i atal parhad data {0} wedi cael ei chyflwyno.
##XFLD: Message used in persist task when cancellation is caught
VIEW_PERSISTENCY_CANCELED=Mae''r broses i barhau gwedd {0} wedi''i hatal.
##XFLD: Message used in persist task when cancelation is caught and performed through a cancelation task
VIEW_PERSISTENCY_CANCELED_VIA_CANCEL_TASK=Mae''r broses i barhau gwedd {0} wedi''i hatal drwy''r dasg ganslo {1}.
##XFLD: Message used in cancel task when the task is triggered by deployment
VIEW_PERSISTENCY_CANCEL_TRIGGERED_BY_DEPLOYMENT=Canslo''r broses parhau data wrth osod y wedd {0}.
##XFLD: Message used in case the cancelation is retriggered for a running task
VIEW_PERSISTENCY_CANCEL_ATTEMPT_RETRIGGER_CANCEL=Mae tasg ganslo flaenorol ar gyfer parhau â''r wedd {0} eisoes wedi cael ei chyflwyno.
##XFLD: Message used to express the delay that may occur before the persistency task is stopped
VIEW_PERSISTENCY_CANCEL_DELAY_STOPPING_PERSISTENCY=Efallai y bydd oedi nes bod y dasg barhau data ar gyfer gwedd {0} yn cael ei hatal.
##XFLD: Message used to reflect the task log identifier of the current running persistency task
VIEW_PERSISTENCY_CANCEL_CURRENT_RUNNING_TASK=Mae data ar gyfer gwedd {0} yn cael ei wneud yn barhaol gyda’r dasg {1}.
##XFLD:Message used by SEAL repository for locked partitions and DACs
VIEW_PERSISTENCY_LOCKED_PARTITIONS_DACS=Efallai fod awdurdodiadau gan DACs wedi newid ac nad ydyn nhw’n cael eu hystyried gan raniadau wedi’u cloi. Ewch ati i ddatgloi’r rhaniadau a llwytho ciplun newydd i osod y newidiadau.
##XFLD:Message used by SEAL repository for column structure change
VIEW_PERSISTENCY_COLUMN_STRUCTURE_CHANGE=Mae strwythur y golofn wedi newid ac nid yw'n cyfateb mwyach i'r tabl parhad sy'n bodoli. Tynnwch y data parhad a chychwyn proses parhau data newydd i ddiweddaru eich tabl parhad.
##XFLD: Message for out of memory
VIEW_PERSISTENCY_HANA_OUT_OF_MEMORY=Mae'r dasg wedi methu oherwydd gwall dim cof ar ôl yng nghronfa ddata SAP HANA.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_EXCEPTION_TRANSACTION_ROLLBACK=Mae'r dasg wedi methu oherwydd eithriad mewnol yng nghronfa ddata SAP HANA.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_SQL_EXECUTION_ISSUE=Mae'r dasg wedi methu oherwydd problem fewnol wrth weithredu SQL yng nghronfa ddata SAP HANA.
##XFLD: Message for HANA out of memory result
VIEW_PERSISTENCY_OUT_OF_MEMORY_RESULT_MESSAGE=Rheswm dros ddigwyddiad dim cof HANA: {0}
##XFLD: Message for admission control rejection
VIEW_PERSISTENCY_HANA_ADMISSION_CTRL_REJECTION=Mae'r dasg wedi methu oherwydd Gwrthod Rheoli Mynediad SAP HANA.
##XFLD: Message for HANA back pressure / rate limit of HANA client
VIEW_PERSISTENCY_HANA_CLIENT_RATE_LIMIT_REJECTION=Mae'r dasg wedi methu oherwydd bod gormod o gysylltiadau gweithredol SAP HANA.
#XMSG: Message for invalid persisted table name
VIEW_PERSISTENCY_PERSISTED_TABLE_INVALID=Gwall wedi codi ac mae'r tabl sydd wedi'i barhau wedi dod yn annilys. I ddatrys y broblem, tynnwch y data sydd wedi'i barhau a pharhau â’r wedd eto.
#XMSG: Message for usahe of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION=Does dim modd parhau'r wedd. Mae'n defnyddio tabl pell yn seiliedig ar ffynhonnell pell gydag ymlediad defnyddiwr wedi'i alluogi. Edrychwch ar linach y wedd.
#XMSG: Message for dynamic usage of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION_DYNAMIC=Does dim modd parhau'r wedd. Mae'n defnyddio tabl pell yn seiliedig ar ffynhonnell pell gydag ymlediad defnyddiwr wedi'i alluogi. Mae modd i'r tabl pell gael ei ddefnyddio yn ddeinamig drwy wedd sgript SQL.
#XMSG: Message for insufficient privileges or consumption of view with DAC via dynamic SQL
VIEW_PERSISTENCY_INSUF_PRIV_OR_DAC_DYN_SQL=Gall eich hawliau fod yn annigonol. Agorwch y Rhagolwg Data i weld os oes gennych yr hawliau gofynnol. Os oes, mae'n bosibl y bydd rheolaeth mynediad data (DAC) yn berthnasol i ail wedd a ddefnyddir trwy sgript SQL ddeinamig.
#XMSG: deployment of DAC is deprecated and view need to be redeployed to be able to persist data
VIEW_PERSISTENCY_DEPRECATED_DAC=Mae''r wedd "{0}" yn cael ei defnyddio drwy Reolaeth Mynediad Data (DAC) sy''n cael ei ddatgymeradwyo. Defnyddiwch y wedd eto i allu parhau â data ar gyfer y wedd.
##XFLD:Message used by SEAL repository for input parameter
START_VIEW_PERSISTENCY_INPUT_PARAMETER=Defnyddio gwerth diofyn "{0}" ar gyfer paramedr mewnbwn "{1}".
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Elastic compute node keys~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
##XFLD:Message used to reflect disablement of ecn replica
ECN_DISABLE_EXISTING_REPLICA=Mae replica cwgn cyfrifyddu elastig wedi'i analluogi.
##XFLD:Message used to reflect recreation of ecn replica
ECN_RECREATE_EXISTING_REPLICA=Mae replica cwgn cyfrifyddu elastig wedi'i ail-greu.
##XFLD:Message used to reflect re-enablement of ecn replica
ECN_ENABLE_EXISTING_REPLICA=Mae replica cwgn cyfrifyddu elastig wedi'i alluogi eto.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Task Schedule Menu key~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message to schedule task
scheduleText=Amserlennu
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Creu Amserlen
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Golygu Amserlen
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Dileu Amserlen
#XFLD: Refresh frequency field
refreshFrequency=Amlder Adnewyddu
#XFLD: Refresh frequency field
refreshFrequencyNew=Amlder
#XFLD: Refresh frequency field
refreshFrequencyNewNew=Amlder a Drefnwyd
#XBUT: label for None
none=Dim
#XBUT: label for Real-Time replication state
realtime=Amser Real
#XFLD: Label for table column
txtNextSchedule=Rhediad Nesaf
#XFLD: Label for table column
txtNextScheduleNew=Rhediad Nesaf sydd wedi’i Drefnu
#XFLD: Label for table column
txtNumOfRecords=Nifer y Cofnodion
#XFLD: Label for scheduled link
scheduledTxt=Wedi'i Amserlennu
#XFLD: LABEL for partially persisted link
partiallyPersisted=Wedi Parhau'n Rhannol
#XFLD: Text for paused text
paused=Wedi'i Rewi

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ View Monitor Tasklog Details Action keys~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message for release lock actionn
releaseLockMsg=Os yw’r broses rhedeg wedi bod ar waith am gyfnod hirach na’r arfer, gallai hyn fod yn arwydd ei bod wedi methu ac nad yw’r statws wedi’i ddiweddaru’n unol â hynny. \r\nI ddatrys y broblem, gallwch ryddhau’r clo a gosod ei statws i wedi methu.
#XFLD: Label for release lock dialog
releaseLockText=Rhyddhau Clo

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Column header tooltip ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: tooltip for table column
txtViewTooltip=Enw'r wedd parhad
#XFLD: tooltip for table column
txtViewDataAccessTooltip=Mae hyn yn dynodi a yw'r wedd ar gael
#XFLD: tooltip for table column
txtViewRefreshFreqTooltip=Mae hyn yn dynodi a yw amserlen wedi'i diffinio ar gyfer y wedd
#XFLD: tooltip for table column
txtViewStatusTooltip=Cael statws y wedd parhad
#XFLD: tooltip for table column
txtViewLatestUpdateTooltip=Mae'n rhoi gwybodaeth ynglŷn â pha bryd gafodd y wedd parhad ei diweddaru ddiwethaf
#XFLD: tooltip for table column
txtViewNextRunTooltip=Os yw amserlen wedi'i gosod ar gyfer y wedd, gwelwch erbyn pryd mae'r rhediad nesaf wedi'i drefnu.
#XFLD: tooltip for table column
txtViewNumOfRecordsTooltip=Olthain nifer y cofnodion.
#XFLD: tooltip for table column
txtViewInMemorySizeTooltip=Olrhain faint o'ch cof mae'r wedd yn ei ddefnyddio
#XFLD: tooltip for table column
txtViewDiskSizeTooltip=Olrhain faint o'ch disg mae'r wedd yn ei ddefnyddio
#XMSG: Expired text
txtExpired=Wedi Dod i Ben

#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Dim modd ychwanegu gwrthrych "{0}" at y gadwyn tasgau.

#~~~~~~~~~~~View Analyzer task log messages~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message indicating number of records for a view, and memory consumption in Mebibyte during simulation of view persistency
RECORDS_PEAK_MEMORY=Mae gan wedd "{0}" {1} cofnod. Mae efelychiad o barhad data ar gyfer y wedd hon wedi defnyddio {2} MiB o gof.
#XMSG: View Analyzer execution failure
VIEW_ANALYZER_EXECUTION_FAILED=Gweithredu Dadansoddwr Gweddau wedi methu.
#XMSG: Missing authorizations for View Analyzer
VIEW_ANALYZER_MISSING_AUTH=Awdurdodiadau coll ar gyfer Dadansoddwr Gweddau.
#XMSG: Maximum memory in Gibibyte is reached, therefore persistency simulation is stopped
MAXIMUM_MEMORY_REACHED=Wedi cyrraedd uchafswm cof o {0} GiB wrth efelychu parhad data ar gyfer gwedd "{1}". Felly, ni fydd rhagor o efelychiadau parhad data yn cael eu rhedeg.
#XMSG: Error during persistency simulation occurred
PERSISTENCY_SIMULATION_ERROR=Gwall wedi codi wrth efelychu parhad data ar gyfer gwedd "{0}".
#XMSG: Persistency simulation is not executed, because prerequisites are not fulfilled and the view cannot be persisted
PREREQUISITES_NOT_FULFILLED=Dydy efelychiad parhad data ar gyfer gwedd "{0}" heb ei weithredu oherwydd nid yw rhagofynion wedi''u cyflawni a does dim modd parhau â''r wedd.
#XMSG: Persistency simulation is not executed, because the view does not exist in the database
VIEW_DOES_NOT_EXIST=Mae''n rhaid i chi osod y wedd "{0}" er mwyn galluogi efelychiad parhad data.
#XMSG: Number of records cannot be determined, because the local table does not exist in the database
TABLE_DOES_NOT_EXIST=Dydy''r tabl lleol "{0}" ddim yn bodoli yn y gronfa ddata, felly does dim modd pennu nifer y cofnodion ar gyfer y tabl hwn.
##XMSG: Message start of cancellation for View Analyzer task
CANCEL_VIEW_ANALYZER_TRIGGERED=Proses i stopio tasg Dadansoddwr Gweddau {0} ar gyfer gwedd "{1}" wedi''i chyflwyno. Efallai y bydd oedi nes bydd y dasg wedi''i hatal.
##XMSG: Message start to indicate that task is not active
CANCEL_VIEW_ANALYZER_NO_ACTIVE_TASK=Dydy''r dasg Dadansoddwr Gweddau {0} ar gyfer gwedd "{1}" ddim ar waith.
#XMSG: Cancel View Analyzer task failure
CANCEL_VIEW_ANALYZER_FAILED=Wedi methu canslo'r dasg Dadansoddwr Gweddau.
##XMSG: Message used in View Analyzer execution task when task is canceled
VIEW_ANALYZER_CANCELED=Cafodd proses gweithredu Dadansoddwr Gweddau ar gyfer "{0}" ei hatal drwy dasg canslo.
##XMSG: Message start of cancelation for Model Validation task
CANCEL_MODEL_VALIDATION_TRIGGERED=Mae proses i stopio tasg Dilysu Model {0} ar gyfer gwedd "{1}" wedi''i chyflwyno. Efallai y bydd oedi nes bydd y dasg wedi''i hatal.
##XMSG: Message to indicate that task is not active
CANCEL_MODEL_VALIDATION_NO_ACTIVE_TASK=Dydy tasg Dilysu Model {0} ar gyfer y wedd "{1}" ddim ar waith.
#XMSG: Cancel Model Validation task failure
CANCEL_MODEL_VALIDATION_FAILED=Wedi methu canslo tasg Dilysu Model.
##XMSG: Message used in Model Validation task when task is canceled
MODEL_VALIDATION_CANCELED=Cafodd proses gweithredu Dilysu Model ar gyfer "{0}" ei hatal drwy dasg canslo.
#XMSG: Message for Locked space error, before starting Model Validation task
MODEL_VALIDATION_LOCKED_SPACE_ERROR=Dim modd defnyddio Dilysu Model ar gyfer gwedd "{0}", am fod y gofod "{1}" wedi''i gloi.
##XMSG: Message used in View Analyzer execution task when determination of number of rows fails
NUMBER_ROWS_DETERMINATION_FAILED=Gwall wrth bennu nifer y rhesi ar gyfer tabl lleol "{0}".
##XMSG: Message to indicate that SQL plan was created
EXECUTION_PLAN_CREATED=Mae ffeil cynllun Dadansoddwr SQL ar gyfer gwedd "{0}" wedi''i chreu ac mae modd ei llwytho i lawr.
#XFLD: Message to indicate starting of process to generate SQL Analyzer plan file
START_PLANVIZ_EXECUTION=Dechrau proses i greu ffeil cynllun Dadansoddwr SQL ar gyfer gwedd "{0}".
#XFLD: Message to indicate starting of View Analyzer process
VIEW_ANALYZER_EXECUTION_STARTED=Wrthi'n dechrau rhoi Dadansoddwr Gwedd ar waith.
##XMSG: Message to indicate that SQL plan file cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_PLANVIZ_NOT_FULFILLED=Does dim modd creu ffeil cynllun Dadansoddwr SQL ar gyfer gwedd "{0}", gan nad yw rhagofynion parhad data wedi''u bodloni.`
##XMSG: Error message during  generation of SQL Analyzer plan file
PLANVIZ_ERROR=Gwall wrth greu ffeil cynllun Dadansoddwr SQL ar gyfer gwedd "{0}".
#XMSG: Message for Locked space error
VIEW_ANALYZER_LOCKED_SPACE_ERROR=Dim modd defnyddio Dadansoddwr Gweddau ar gyfer gwedd "{0}", am fod y gofod "{1}" wedi''i gloi.
#XMSG: Message for persistency simulation without partitioning
PERSISTENCY_SIMULATION_WITHOUT_PARTITIONS=Dydy''r rhaniadau gwedd "{0}" ddim yn cael eu hystyried wrth efelychu parhad data.
#XMSG: Message for generation of SQL Analyzer plan file without considering partitioning
PLANVIZ_WITHOUT_PARTITIONS=Dydy''r rhaniadau gwedd "{0}" ddim yn cael eu hystyried wrth greu ffeil cynllun Dadansoddwr SQL.
#XMSG: Confirmation message for mass removal of persisted data
MassRemovePersistency_Confirm_Msg=Ydych chi am dynnu'r data parhaus a newid y mynediad data yn ôl i fynediad rhithwir?
#XMSG: Confirmation message for partial mass removal of selected persisted data
PartialMassRemovePersistency_Confirm_Msg=Mae gan {0} o''r {1} wedd dan sylw ddata parhaus. \nYdych chi am dynnu''r data parhaus a newid y mynediad data yn ôl i fynediad rhithwir?
#XMSG: success message for stopping mass persistence
stopMassPersistenceSuccess=Rydym yn tynnu data parhaus ar gyfer y gweddau dan sylw.
#XMSG: error message during mass stop persistence of a view (backend error)
STOP_MASS_PERSISTENCE_ERROR=Roedd gwall wrth stopio parhau â’r gweddau dan sylw.
#XMSG: memory analysis is only performed in space {0}
NO_MEMORY_ANALYSIS=Mae dadansoddiad o''r cof yn cael ei gynnal ar gyfer endidau yn y gofod "{0}" yn unig. Mae "{1}" "{2}" wedi''u hanwybyddu.
##XMSG: Message to indicate that Explain Plan cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_EXPLAINPLAN_NOT_FULFILLED=Does dim modd creu ffeil Cynllun Eglurhad ar gyfer gwedd "{0}", gan nad yw rhagofynion parhad wedi''u bodloni.
#XMSG: Message for Explain Plan generation without partitioning
EXPLAINPLAN_WITHOUT_PARTITIONS=Dydy''r rhaniadau gwedd "{0}" ddim yn cael eu hystyried wrth greu ffeil Cynllun Eglurhad.
#XFLD: Message to indicate starting of process to generate Explain Plan file
START_EXPLAINPLAN_EXECUTION=Dechrau proses i greu ffeil Cynllun Eglurhad ar gyfer gwedd "{0}".
##XMSG: Message to indicate that Explain Plan was created
EXPLAINPLAN_CREATED=Mae ffeil Cynllun Egluro wedi''i chreu ar gyfer gwedd "{0}". Gallwch ddangos y ffeil drwy glicio "Gweld Manylion", neu ei llwytho i lawr os oes gennych y caniatâd perthnasol.
##XMSG: Error message during  generation of Explain Plan file
EXPLAINPLAN_ERROR=Gwall wrth greu ffeil Cynllun Eglurhad ar gyfer gwedd "{0}".
##XMSG: Explain Plan file cannot be generated for the view {0}, because too many views are stacked on each other
EXPLAINPLAN_MODEL_TOO_COMPLEX=Does dim modd creu ffeil Cynllun Eglurhad ar gyfer gwedd "{0}. Mae gormod o weddau wedi''u pentyrru ar ei gilydd. Gall modelau cymhleth achosi perfformiad araf a gwallau o ran y cof. Rydyn ni''n eich argymell i barhau â gwedd.

##XMSG: Message to indicate that the space is locked
PERFORMANCE_ANALYSIS_LOCKED_SPACE_ERROR=Dim modd defnyddio''r nodwedd dadansoddiad o berfformiad ar gyfer gwedd "{0}", am fod y gofod "{1}" wedi''i gloi.
##XMSG: The performance analysis task was cancelled
PERFORMANCE_ANALYSIS_CANCELED=Mae''r broses o ddadansoddi perfformiad ar gyfer gwedd "{0}" wedi''i chanslo.
##XMSG: The performance analysis failed
PERFORMANCE_ANALYSIS_FAILED=Proses dadansoddi perfformiad wedi methu.
##XMSG: The performance analysis is finished
PERFORMANCE_ANALYSIS_FINISHED=Mae''r broses o ddadansoddi perfformiad ar gyfer gwedd "{0}" wedi dod i ben. Gallwch weld y canlyniad drwy glicio "Gweld Manylion".
##XMSG: The view has input parameter with no default value
INPUT_PARAMETER_WITHOUT_DEFAULT_VALUE=Dydy'r wedd hon ddim yn gymwys i'w dadansoddi oherwydd bod ganddi baramedr sydd heb werth diofyn.
##XMSG: The view is not fully deployed
VIEW_NOT_FULLY_DEPLOYED=Does dim modd dadansoddi'r wedd hon gan nad ydy hi wedi'i gosod yn llawn.
##XMSG: The view uses limited remote adapters
LIMITED_REMOTE_ADAPTER=Mae'r wedd hon yn defnyddio o leiaf un addasydd o bell sydd â galluoedd cyfyngedig, fel hidlydd gwthio i lawr ar goll neu gymorth ar gyfer 'Cyfrif'. Os byddwch yn parhau neu'n dyblygu gwrthrychau, gall hyn wella perfformiad amser rhedeg.
##XMSG: The view uses remote adapter that does not support 'Limit'
REMOTE_ADAPTER_NOT_SUPPORTING_LIMIT=Mae'r wedd hon yn defnyddio o leiaf un addasydd o bell nad yw'n cefnogi 'Cyfyngiad'. Mae'n bosibl bod mwy na 1000 o gofnodion wedi'u dewis.
##XMSG: Performance analysis is executed by using default parameters of view'
INPUT_PARAMETER_WITH_DEFAULT_VALUE=Proses dadansoddi perfformiad wedi'i chwblhau trwy ddefnyddio gwerthoedd rhagosodedig paramedrau gwedd.
##XMSG: Message to indicate that an error occurred
PERFORMANCE_ANALYSIS_ERROR=Gwall yn ystod dadansoddiad perfformiad ar gyfer y wedd "{0}".
##XMSG: Message start of cancellation for Performance Analysis task
CANCEL_PERFORMANCE_ANALYSIS_TRIGGERED=Proses i stopio tasg Dadansoddi Perfformiad {0} ar gyfer gwedd "{1}" wedi''i chyflwyno. Efallai y bydd oedi nes bydd y dasg wedi''i hatal.
##XMSG: Message start to indicate that task is not active
CANCEL_PERFORMANCE_ANALYSIS_NO_ACTIVE_TASK=Dydy''r dasg Dadansoddi Perfformiad {0} ar gyfer gwedd "{1}" ddim ar waith.
#XMSG: Cancel Performance Analysis task failure
CANCEL_PERFORMANCE_ANALYSIS_FAILED=Wedi methu canslo tasg Dadansoddi Perfformiad.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Neilltuo Amserlen i Mi
#XBUT: Pause schedule menu label
pauseScheduleLabel=Rhewi Amserlen
#XBUT: Resume schedule menu label
resumeScheduleLabel=Ailgychwyn Amserlen
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Gwall wrth dynnu amserlenni.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Gwall wrth neilltuo amserlenni.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Gwall wrth rewi amserlenni.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Gwall wrth ailgychwyn amserlenni.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Dileu {0} amserlen
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Newid perchennog {0} amserlen
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Rhewi {0} amserlen
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Ailgychwyn {0} amserlen
#XBUT: Select Columns Button
selectColumnsBtn=Dewis Colofnau
#XFLD: Refresh tooltip
TEXT_REFRESH=Adnewyddu
#XFLD: Select Columns tooltip
text_selectColumns=Dewis Colofnau


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Runtime Metrics ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Label for Runtime Metrics
RuntimeMetricsTitle=Metrigau Amser Rhedeg ar gyfer 
#XFLD : Label for Run Button
runButton=Rhedeg
#XFLD : Label for Cancel Button
cancelButton=Canslo
#XFLD : Label for Close Button
closeButton=Cau
#XFLD : Label for Open View Analyzer Button
openViewAnalyzerButton=Agor Dadansoddwr Gwedd
#XFLD : Label for Generate Explain Plan Button
generateExplainPlanButton=Creu cynllun egluro
#XFLD : Label for Previous Run Column
previousRun=Rhediad Blaenorol
#XFLD : Label for Latest Run Column
latestRun=Wedi Rhedeg Ddiwethaf
#XFLD : Label for time Column
time=Amser
#XFLD : Label for Duration Column
duration=Hyd
#XFLD : Label for Peak Memory Column
peakMemory=Cof Mwyaf
#XFLD : Label for Number of Rows
numberOfRows=Nifer y Rhesi
#XFLD : Label for Overall Number of Sources
overallNumberOfSources=Cwfanswm Nifer y Ffynonellau
#XFLD : Label for Data Access Column
dataAccess=Mynediad Data
#XFLD : Label for Local Tables
localTables=Tablau Lleol
#XFLD : Label for federatedRemoteTables
federatedRemoteTables=Tablau Ffederal Pell (gyda Galluoedd Addasydd Cyfyngedig)
#XTXT Text for initial state of the runtime metrics
initialState=Yn gyntaf, rhaid i chi gynnal Dadansoddiad Perfformiad i gael y metrigau. Gall hyn gymryd amser, ond gallwch ganslo'r broses os bydd angen gwneud hynny.
#XMSG: Cancel confirmation message
CancelRunConfirmationMessage=Ydych chi'n siŵr eich bod am ganslo'r broses rhedeg bresennol o Ddadansoddiad Perfformiad?
#XTIT: Cancel dialog title
CancelRunTitle=Canslo’r broses Rhedeg
#XFLD: Label for Number of Rows
NUMBER_ROWS=Nifer y Rhesi
#XFLD: Label for Overall Number of Sources
OVERALL_NUMBER_SOURCES=Cwfanswm Nifer y Ffynonellau
#XFLD: Label for Data Access
DATA_ACCESS=Mynediad Data
#XFLD: Label for Remote tables
NUMBER_REMOTE_TABLES_LIMITED_ADAPTERS=Tablau Ffederal Pell (gyda Galluoedd Addasydd Cyfyngedig)
#XFLD: Label for select statement
SELECT_STATEMENT='DEWIS * O'R CYFYNGIAD GWEDD 1000'
#XFLD: Label for duration
SELECT_RUNTIME=Hyd
#XFLD: Label for peak memory
SELECT_PEAK_MEMORY=Cof Mwyaf
#XFLD: Label for count statement
SELECT_COUNT_STATEMENT='DEWIS CYFRIF(*) O'R WEDD'
#XFLD: Label for count duration
SELECT_COUNT_RUNTIME=Hyd
#XFLD: Label for count peak memory
SELECT_COUNT_PEAK_MEMORY=Cof Mwyaf
#XFLD: Label for count of number of local tables
NUMBER_LOCAL_TABLES_FILE=Tablau Lleol (Ffeil)
#XTXT: Text for running state of the runtime metrics
Running=Wrthi'n Rhedeg...
#XFLD: Label for time
Time=Amser
#XFLD: Label for virtual access
PA_VIRTUAL=Rhithiol
#XFLD: Label for persisted access
PA_PERSISTED=Parhaol
PA_PARTIALLY_PERSISTED=Wedi Parhau'n Rhannol
#XTXT: Text for cancel
CancelRunSuccessMessage=Wrthi'n canslo proses redeg Dadansoddiad Perfformiad.
#XTXT: Text for cancel error
CancelRunErrorMessage=Gwall wrth geisio canslo proses redeg Dadansoddiad Perfformiad.
#XTXT: Text for explain plan generation
ExplainPlanStarted=Wrthi''n creu Cynllun Eglurhad ar gyfer gwedd "{0}".
#XTXT: Text for performance analysis
PerformanceAnalysisStarted=Wrthi''n dechrau Dadansoddiad Perfformiad ar gyfer y wedd "{0}".
#XTXT: Text for performance analysis error
ErrorFetchingPerformanceAnalysis=Gwall wrth geisio nôl data Dadansoddiad Perfformiad.
#XTXT: Text for performance analysis error
conflictingTask=Mae'r dasg Dadansoddiad Perfformiad eisoes yn rhedeg
#XFLD: Label for Errors
Errors=Gwall(au)
#XFLD: Label for Warnings
Warnings=Rhybudd(ion)
#XFLD: Tooltip for missing privileges
openViewAnalyzerButtonTooltipNoPrivilege=Mae arnoch angen hawl DWC_DATAINTEGRATION(diweddariad) i agor o Dadansoddwr Gwedd.
#XFLD: Tooltip for missing privileges
generateExplainPlanButtonTooltipNoPrivilege=Mae arnoch angen hawl DWC_RUNTIME(darllen) i greu'r Cynllun Eglurhad.



#XFLD: Label for frequency column
everyLabel=Pob
#XFLD: Plural Recurrence text for Hour
hoursLabel=Oriau
#XFLD: Plural Recurrence text for Day
daysLabel=Diwrnod
#XFLD: Plural Recurrence text for Month
monthsLabel=Mis
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Munud
