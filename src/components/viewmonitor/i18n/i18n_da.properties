
#XBCB: Breadcrumb
viewMonitorBreadcrumb={spaceId}

#XFLD: Label for persisted view column
SOURCE=Kilde
#XFLD: Label for persisted view column
NAME=Navn
#XFLD: Label for persisted view column
NAME_LABEL=Virksomhedsnavn
#XFLD: Label for persisted view column
NAME_LABELNew=Objekt (virksomhedsnavn)
#XFLD: Label for persisted view column
TECHINCAL_NAME=Teknisk navn
#XFLD: Label for persisted view column
TECHINCAL_NAMENew=Objekt (teknisk navn)
#XFLD: Label for persisted view column
DATA_PERSISTENCY=Dataadgang
#XFLD: Label for persisted view column
STATUS=Status
#XFLD: Label for persisted view column
LAST_UPDATED=Senest opdateret
#XFLD: Label for persisted view column
IN_MEMORY_SIZE=Hukommelse brugt til lager (MiB)
#XFLD: Label for persisted view column
DISK_SIZE=Disk brugt til lager (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_NEWW=Størrelse in-memory (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_HEADER=Størrelse in-memory
#XFLD: Label for persisted view column
DISK_SIZE_NEWW=Størrelse på disk (MiB)
#XFLD: Label for persisted view column
DISK_SIZE_HEADER=Størrelse på disk
#XFLD: Label for schedule owner column
txtScheduleOwner=Ejer af tidsplan
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Viser, hvem der har oprettet tidsplanen
#XFLD: Label for persisted view column
PERSISTED=Persisteret
#XFLD: Label for persisted view column
TYPE=Type
#XFLD: Label for View Selection Dialog column
changedOn=Ændret den
#XFLD: Label for View Selection Dialog column
createdBy=Oprettet af
#XFLD: Label for log details column
txtViewPersistencyLogs=Vis logge
#XFLD: Label for log details column
txtViewPersistencyLogsNew=Detaljer
#XFLD: text for values shown for Ascending sort order
SortInAsc=Sorter stigende
#XFLD: text for values shown for Descending sort order
SortInDesc=Sorter faldende
#XFLD: Label for persisted view monitor heading
ViewMonitor_Heading=Visningsmonitor
#XFLD: Label for persisted view monitor sub-heading
ViewMonitor_SubHeading=Overvåg og vedligehold datapersistens for visninger


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ DynamicPageHeader ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: text for values shown in column Persistence Status
txtLoading=Indlæser
#XFLD: text for values shown in column Persistence Status
txtRunning=Kører
#XFLD: text for values shown in column Persistence Status
txtAvailable=Tilgængelig
#XFLD: text for values shown in column Persistence Status
txtError=Fejl
#XMSG: Error message for unknown Persistence type that gets logged in Kibana
unknownReplicationTypeError=Replikeringstype "{0}" understøttes ikke.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Content ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Indstillinger anvendt ved seneste persistenskørsel:
#XMSG: Message for input parameter name
inputParameterLabel=Inputparameter
#XMSG: Message for input parameter value
inputParameterValueLabel=Værdi
#XMSG: Message for persisted data
inputParameterPersistedLabel=Persisteret kl.
#XTIT: Count for View in ViewMonitor Toolbar
txtViewCount=Visninger ({0})
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Visningspersistens
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Datapersistens
#XBUT: Button to clear the selected view persistance
ClearViewPersistance=Ryd
#XBUT: Button to stop the selected view persistance
stopPersistance=Stop persistens
#XFLD: Placeholder for Search field
txtSearch=Søg
#XBUT: Tooltip for refresh button
txtRefresh=Opdater
#XBUT: Tooltip for add view button
txtDeleteView=Slet persistens
#XBUT: Tooltip for load new peristence
loadNewPersistence=Genstart persistens
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Indlæs nyt snapshot
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Start datapersistens
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Fjern persisterede data
#XMSG: success message for starting persistence
startPersistenceSuccess=Vi persisterer visningen "{0}".
#XMSG: success message for stopping persistence
stopPersistenceSuccess=Vi fjerner persisterede data for visning "{0}".
#XMSG: success message for stopping persistence
deletePersistedViewSuccess=Vi fjerner visningen "{0}" fra overvågningslisten.
#XMSG: error message during start persistence of a view (backend error)
ERROR_STARTING_PERSISTENCE=Der opstod en fejl under start af persistensen for visning "{0}".
#XMSG: error message during start persistence of a view (backend error)
PARAMERROR_STARTING_PERSISTENCE=Visningen "{0}" kan ikke persisteres, fordi den indeholder inputparametre.
#XMSG: error message during start persistence of a view (backend error)
NUMBER_OF_PARAMETER_STARTING_PERSISTENCE=Visningen "{0}" kan ikke persisteres, fordi den har mere end en inputparameter.
#XMSG: error message during start persistence of a view (backend error)
PARAMETER_DEFAULT_VALUE_STARTING_PERSISTENCE=Visningen "{0}" kan ikke persisteres, fordi inputparameteren ikke har standardværdi.
#XMSG: error message during start persistence of a view (backend error)
REDEPLOYMENT_DAC_REQUIRED=Du skal genimplementere dataadgangskontrol "{0}" for at understøtte datapersistens.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_STARTING_PERSISTENCE=Visningen "{0}" kan ikke persisteres, fordi den bruger visningen "{1}", som indeholder dataadgangskontrol.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_EXTERNAL_STARTING_PERSISTENCE=Visningen "{0}" kan ikke persisteres, fordi den bruger en visning med dataadgangskontrol, der hører til et andet space.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_STRUCTURE_STARTING_PERSISTENCE=Visningen "{0}" kan ikke persisteres, fordi strukturen i en eller flere af dens dataadgangskontroller ikke understøtter datapersistens.
#XMSG: error message during stop persistence of a view (backend error)
STOP_PERSISTENCE_ERROR=Der opstod en fejl under standsningen af persistensen for visning "{0}".
#XMSG: error message during stop persistence of a view (backend error)
DELETE_PERSISTED_VIEW_ERROR=Der opstod en fejl under sletning af den persisterede visning "{0}".
#XMSG: Warning message for removing the persisted data
RemovePersistency_Confirm_Msg=Vil du slette de persisterede data og skifte til virtuel adgang for visning "{0}"?
#XMSG: Warning message for delete the view persistence
DeletePersistency_Confirm_Msg=Vil du fjerne visningen fra overvågningslisten og slette de persisterede data for visning "{0}"?
#XMSG: error message for reading data from backend
txtReadBackendError=Det ser ud til, at der opstod en fejl under læsning fra backend.
#XFLD: Label for No Data Error
NoDataError=Fejl
#XMSG: message for conflicting task
Task_Already_Running=En opgave med konflikt kører allerede for visningen "{0}".

#XFLD: Error while accessing pertitioning for a view
missingPrivilegeInViewPersistency=Utilstrækkelige rettigheder til at udføre partitionering for visningen "{0}"

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Show All Views~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Text for show all views button
allViewsText=Alle visninger ({0})
#XBUT: Text for show scheduled views button
scheduledText=Planlagt ({0})
#XBUT: Text for show persisted views button
persistedText=Persisteret ({0})
#XBUT: Text for start analyzer button
startAnalyzer=Start visningsanalyse
#XFLD: Message if repository is unavailable
repositoryErrorMsg=Lageret er ikke tilgængeligt, og visse funktioner er deaktiveret.

#XFLD: Data Access - Virtual
Virtual=Virtuel
#XFLD: Data Access - Persisted
Persisted=Persisteret

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Non-Persisted View Selection Dialog~~~~~~~~~~~~~~~~~~~~~~~

#XTIT: Title for Non-Persisted View selection dialog
select_source=Vælg Visning for at persistere

#XTIT: Placeholder for Non-Persisted View selection dialog search
searchView=Vælg Visninger
#XTIT: No data in the list of non-persisted view
No_Data=Ingen data
#XBUT: Button to select non-persisted view
ok=OK
#XBUT: Button to close the non-persisted views selection dialog
cancel=Annuller

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~View persistency log message key~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message for view persistency logs
START_STOP_PERSISTENCY=Starter opgaveudførelse af datapersistens for "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY=Persisterer data for visningen "{1}".
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY)
START_VIEW_PERSISTENCY_PROCESS=Starter processen til at persistere data for visningen "{0}".
#XFLD: Message for view persistency manual partition load
START_VIEW_PERSISTENCY_PROCESS_MANUAL_PARTITIONS=Starter processen til at persistere data for visningen "{0}" med valgte partitions-id''er: "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY=Fjerner persisterede data for visningen "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY)
REMOVE_VIEW_PERSISTENCY=Starter processen til at fjerne persisterede data for visningen "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_SUCCESS=Data er persisteret for visningen "{1}".
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_SUCCESS)
START_VIEW_PERSISTENCY_PROCESS_SUCCESS=Data er persisteret for visningen "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY_SUCCESS=Persisterede data er fjernet, og virtuel dataadgang er gendannet for visningen "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_SUCCESS)
REMOVE_VIEW_PERSISTENCY_SUCCESS=Afsluttede processen til at fjerne persisterede data for visningen "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_FAILED=Kan ikke persistere data for visningen "{1}".
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_FAILED)
START_VIEW_PERSISTENCY_PROCESS_FAILED=Kan ikke persistere data for visningen "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.05)
STOP_VIEW_PERSISTENCY_FAILED=Kan ikke fjerne persisterede data for visningen "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_FAILED)
REMOVE_VIEW_PERSISTENCY_FAILED=Kan ikke fjerne persisterede data for visningen "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_SUCCESS="{3}" poster er persisteret for visning "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace START_PERSISTENCY_RECORD_COUNT_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS={0} poster blev indsat i datapersistenstabellen for visningen "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS_V2={0} poster blev indsat i datapersistenstabellen for visningen "{1}". Anvendt hukommelse: {2} GiB.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_SUCCESS="{3}" persisterede poster blev fjernet for visning "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace STOP_PERSISTENCY_RECORD_COUNT_SUCCESS)
REMOVE_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=Persisterede data er fjernet, "{0}" persisterede poster er slettet.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_FAILED=Hentning af recordCount kunne ikke udføres for tabellen "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_FAILED=Hentning af recordCount kunne ikke udføres for tabellen "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Tidsplanen er slettet for "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS)
REMOVE_VIEW_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Tidsplan er slettet for visningen "{0}".
#XFLD: Message for view persistency logs
STOP_PERSISTENCY_DELETE_SCHEDULE_FAILED=Sletning af tidsplanen kunne ikke udføres for "{1}".
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_REDEPLOY=Vi kan ikke persistere visningen "{0}", fordi den er ændret og implementeret, siden du begyndte at persistere den. Prøv at persistere visningen igen, eller vent til næste planlagte kørsel.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_DELETE=Vi kan ikke persistere visningen "{0}", fordi den er slettet, siden du begyndte at persistere den.
#XFLD: Message for successful data persistency of a single partition (for messages created before version 2022.03)
START_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} poster blev persisteret til partition for værdierne "{1}" <= {2} < "{3}".
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (new message to replace START_PERSISTENCY_SINGLE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} poster blev indsat i partition for værdierne "{1}" <= "{2}" < "{3}".
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS_V2={0} poster blev indsat i partition for værdier "{1}" <= "{2}" < "{3}". Anvendt hukommelse: {4} GiB.
#XFLD: Message for successful replication of a "others" partition (for messages created before version 2022.03)
START_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} poster blev persisteret til partitionen "andre".
#XFLD: Message used by SEAL repository for successful replication of a "others" partition (new message to replace START_PERSISTENCY_OTHERS_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} poster blev indsat i partitionen "andre".
#XFLD: Message for view persistency record count of partitions (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={3} blev persisteret for visningen "{1}" i {4} partitioner.
#XFLD: Message used by SEAL repository for view persistency record count of partitions (new message to replace START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={0} poster blev indsat i datapersistenstabellen for visningen "{1}" i {2} partitioner.
#XFLD: Message used by SEAL repository for view persistency for partition wise refresh
START_VIEW_PERSISTENCY_RECORD_COUNT_REFRESHED_PARTITIONS_SUCCESS={0} poster blev indsat i datapersistenstabellen for visningen "{1}". Opdaterede partitioner:{2}; Låste partitioner:{3}; Partitioner i alt:{4}
#XFLD: Message used by SEAL repository for view persistency for manual partition load
START_VIEW_PERSISTENCY_RECORD_COUNT_MANUAL_PARTITIONS_SUCCESS={0} poster blev indsat i datapersistenstabel for visningen "{1}" i {2} valgte partitioner
#XFLD: Message used by SEAL repository for view persistency for locked partition data load
START_VIEW_PERSISTENCY_RECORD_COUNT_LOCKED_PARTITIONS_SUCCESS={0} poster blev indsat i datapersistenstabellen for visningen "{1}". Opdaterede partitioner:{2}; Låste, uændrede partitioner:{3}; Partitioner i alt:{4}
#XMSG: Message for unexpected error (for messages created before version 2022.03)
START_PERSISTENCY_UNEXPECTED_ERROR=Der opstod en uventet fejl under persistering af data til visningen "{0}".
#XMSG: Message for unexpected error (new message to replace START_PERSISTENCY_UNEXPECTED_ERROR)
START_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Der opstod en uventet fejl under persistering af data til visningen "{0}".
#XMSG: Message for Locked space error
START_VIEW_PERSISTENCY_LOCKED_SPACE_ERROR=Visningen "{0}” kan ikke persisteres, fordi spacet "{1}" er låst.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
STOP_PERSISTENCY_UNEXPECTED_ERROR=Der opstod en uventet fejl under fjernelse af persisterede data til visningen "{0}".
#XMSG: Message for unexpected error (new message to replace STOP_PERSISTENCY_UNEXPECTED_ERROR)
REMOVE_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Der opstod en uventet fejl under fjernelse af persistens for visningen "{0}".
#XMSG: Message for ivalidated view
VIEW_PERSISTENCY_ABORTED_DEFINITION_INVALID=Definitionen af visningen "{0}" er blevet ugyldig, sandsynligvis pga. en ændring af et objekt, der er forbrugt direkte eller indirekte af visningen. Prøv at implementere visningen påny for at løse problemet eller at identificere grundårsagen.
#XMSG: Message for persisted data removal (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT=Persisterede data fjernes, mens visningen "{0}" implementeres.
#XMSG: Message for persisted data removal of a consuming view
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW=Persisterede data fjernes, mens den forbrugte visning "{0}" implementeres.
#XMSG: Message for persisted data removal of a consuming view due to a dac change
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW_DAC_CHANGED=Persisterede data fjernes, mens den forbrugte visning "{0}" implementeres, fordi dens dataadgangskontrol er ændret.
#XMSG: Message for persisted data removal (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DEPLOYMENT=Persisterede data fjernes, mens visningen "{0}" implementeres.
#XMSG: Message for deleted view (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION=Persistens fjernes med sletning af visningen "{0}".
#XMSG: Message for deleted view (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DELETION=Persistens fjernes med sletning af visningen "{0}".
#XMSG Message for removal of persisted data, because prerequisites not fulfilled
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_PREREQUISITES_NOT_FULFILLED=Persisterede data blev fjernet, fordi forudsætninger for datapersistens ikke længere opfyldes.
#XMSG: Message for inconsistent persistency
VIEW_PERSISTENCY_INCONSISTENT_PERSISTENCY=Persistens af visningen "{0}" er blevet inkonsistent. Fjern persisterede data for at løse problemet.
#XMSG: Message for prerequisites
START_PERSISTENCY_CHECK_PREREQUISITES=Kontrollerer forudsætningerne for persistens af visningen "{0}".
#XMSG: Message for DAC deployed in deprecated way using table function and join (for messages created before 2025.05)
START_PERSISTENCY_DEPRECIATED_DAC=Visningen "{0}" er implementeret ved hjælp af Data Access Control (DAC), der er ved at blive forældet. Implementer venligst visningen igen for at forbedre ydeevnen.
#XMSG: Message for DAC deployed in deprecated way using table function and join
START_PERSISTENCY_DEPRECATED_DAC=Visningen "{0}" er implementeret ved hjælp af Data Access Control (DAC), der er ved at blive forældet. Implementer venligst visningen igen for at forbedre ydeevnen.
#XMSG: Message for roll back (for messages created before version 2022.03)
ROLLBACK_VIEW_PERSISTENCY=Der opstod en fejl. Tidligere tilstand af persistens er genoprettet for viewet "{0}".
#XMSG: Message used by SEAL repository for rollback of running procedure to persist view (new message to replace ROLLBACK_VIEW_PERSISTENCY)
VIEW_PERSISTENCY_ROLLBACK=Der opstod en fejl. Processen til at persistere visningen ''{0}'' er stoppet, og ændringer er annulleret.
#XMSG: Message used by SEAL repository for rollback of running procedure to remove persisted data of view
REMOVE_VIEW_PERSISTENCY_ROLLBACK=Der opstod en fejl. Processen til at fjerne persisterede data for visningen "{0}" er stoppet, og ændringer er annulleret.
#XMSG: Message used by SEAL repository for persistency preparation
START_PERSISTENCY_PREPARATION=Forbereder persistens af data.
#XMSG: Message used by SEAL repository for data insertion
START_INSERTING_PERSISTENCY_DATA=Indsætter data i persistenstabel.
#XMSG: Message for null values - partitioning (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS={0} null-værdiposter indsat i partitionen "andre".
#XMSG: Message used by SEAL repository for others partition values - partitioning (new message to replace START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS={0} poster blev indsat i partitionen "andre" for værdierne "{2}" < "{1}" OR "{2}" >= "{3}".
#XMSG: Message used by SEAL repository for others partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS_V2={0} poster blev indsat i partitionen "andre" for værdierne "{2}" < "{1}" OR "{2}" >= "{3}". Anvendt hukommelse: {4} GiB.
#XMSG: Message used by SEAL repository for null partition values - partitioning
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS={0} poster blev indsat i partitionen "andre" for værdierne "{1}" IS NULL.
#XMSG: Message used by SEAL repository for null partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS_V2={0} poster blev indsat i partitionen "andre" for værdierne "{1}" IS NULL. Anvendt hukommelse: {2} GiB.
##XFLD:Message used by SEAL repository for remote statistics
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS=Indlæser involverede data: {0} eksterne sætninger. Poster hentet i alt: {1}. Varighed i alt: {2} sekunder.
##XFLD:Message used by SEAL repository for remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS_WITH_PARTITIONS=Indlæser involverede data vha. {0} partitioner med {1} eksterne sætninger. Poster hentet i alt: {2}. Varighed i alt: {3} sekunder.
##XFLD:Message used by SEAL repository with UI ID for instructions of remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_INSTRUCTION=De under kørslen behandlede eksterne sætninger kan vises ved at åbne monitoren for eksterne forespørgsler i detaljerne for de partitionsspecifikke meddelelser.
##XFLD:Message for start reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS=Start af processen med at genbruge eksisterende persisterede data til visning {0} efter implementering.
##XFLD:Message used by SEAL repository for reattach view persitency
START_VIEW_PERSISTENCY_REATTACHMENT=Begynd at genbruge de eksisterende persisterede data.
##XFLD:Message used by SEAL repository for reattach view persitency
START_FINILIZING_VIEW_PERSISTENCY_REATTACHMENT=Genbrug af de eksisterende persisterede data.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_COMPLETE=Processen med at genbruge eksisterende persisterede data er fuldført for visningen {0}.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_FAILED=Kunne ikke genbruge de eksisterende persisterede data til visningen {0} efter implementering.
#XMSG: Message used by SEAL repository for finalizing persistency
START_FINALIZING_PERSISTENCY=Afslutter persistens.
#XMSG: Message used by SEAL repository for restoring virtual access for view
REMOVE_VIEW_PERSISTENCY_VIRTUAL_ACCESS_RESTORED=Den virtuellr dataadgang er gendannet for visningen ''{0}''.
#XMSG: Message used by SEAL repository for view that already has virtual access
REMOVE_VIEW_PERSISTENCY_ALREADY_VIRTUAL_ACCESS=Visningen "{0}" har allerede adgang til virtuelle data. Ingen persisterede data fjernes.
#XMSG: Message for removing view persistency
VIEW_PERSISTENCY_REMOVED_FROM_MONITOR=Visningen ''{0}'' er fjernet fra visningsmonitoren.
#XMSG: Message for view doesnt exist.
VIEW_PERSISTENCY_VIEW_NOT_EXISTING=Visningen "{0}" findes enten ikke i databasen, eller også er den ikke korrekt implementeret, og den kan derfor ikke persisteres. Prøv at implementere visningen igen for at løse problemet, eller prøv at identificere grundårsagen.
#XMSG: Message for persistency not enabled.
VIEW_PERSISTENCY_NOT_ENABLED=Datapersistens er ikke aktiveret. Implementer en tabel/visning på ny i spacet "{0}" for at aktivere funktionaliteten.
#XMSG: Message for unexpected error due to node crash
VIEW_PERSISTENCY_UNEXPECTED_ERROR_GENERAL=Seneste kørsel af visningspersistens blev afbrudt pga. tekniske fejl.
#XFLD: Message for view persistency memory consumption
VIEW_PERSISTENCY_MEMORY_CONSUMPTION={0} GiB maksimal hukommelse brugt i kørselstid for visningspersistens.
#XFLD: Message for view persistency timeout
VIEW_PERSISTENCY_TIMEOUT=Persistens for visningen {0} nåede timeout på {1} timer.
#XFLD: Message for not started asynchronous view persistency
VIEW_PERSISTENCY_ASYNC_NOT_STARTED=Høj systembelastning forhindrede asynkron udførelse af visningspersistensen fra start. Kontroller, om der kører for mange parallelle opgaver.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency (for messages created before version 2022.05)
START_VIEW_PERSISTENCY_EXECUTION_METHOD_DROP_AND_RENAME=Den eksisterende persisterede tabel er slettet og erstattet med en ny persisteret tabel.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_RECREATE_AND_DROP=Den eksisterende persisterede tabel er slettet og erstattet med en ny persisteret tabel.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_TRUNCATE_AND_COPY=Den eksisterende persisterede tabel er opdateret med nye data.
##XFLD: Message for missing authorizations for view persistency
VIEW_PERSISTENCY_MISSING_AUTH=Manglende autorisationer til datapersistens.
##XFLD: Message start of persistency cancellation
VIEW_PERSISTENCY_TRIGGER_CANCEL=Starter med at annullere processen til at persistere visningen {0}.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_UNAVAILABLE_TASK_RUN=Kunne ikke annullere processen til at persistere visningen, fordi der ikke er nogen aktuel datapersistensopgave for visningen {0}.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_NONDETERMINISTIC_TASK_RUN=Kunne ikke annullere processen til at persistere visningen, fordi der ikke kører nogen datapersistensopgave for visningen {0}.
##XFLD: Message for canceling a non running persitency task
VIEW_PERSISTENCY_CANCEL_NON_RUNNING_SELECTED_TASK=Kunne ikke annullere processen til at persistere visningen {0}, da den valgte datapersistensopgave {1} ikke kører.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_UNREACHABLE_PERSISTENCY_RUN=Kunne ikke annullere processen til at persistere visningen, fordi datapersistens for visningen {0} endnu ikke er startet.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_COMPLETED_PERSISTENCY_RUN=Kunne ikke annullere processen til at persistere visningen {0}, fordi den allerede er afsluttet.
##XFLD: Message for failed cancellation
VIEW_PERSISTENCY_ABORTED_CANCEL_EXECUTION=Kunne ikke annullere processen til at persistere visningen {0}.
##XFLD: Message for finalizing persistency cancellation
VIEW_PERSISTENCY_FINALIZE_CANCEL=Processen til at stoppe datapersistensen af visningen {0} er sendt.
##XFLD: Message used in persist task when cancellation is caught
VIEW_PERSISTENCY_CANCELED=Processen til at persistere visningen {0} blev stoppet.
##XFLD: Message used in persist task when cancelation is caught and performed through a cancelation task
VIEW_PERSISTENCY_CANCELED_VIA_CANCEL_TASK=Processen til at persistere visningen {0} blev stoppet via annulleringsopgaven {1}.
##XFLD: Message used in cancel task when the task is triggered by deployment
VIEW_PERSISTENCY_CANCEL_TRIGGERED_BY_DEPLOYMENT=Annullerer processen til persistering af data under implementering af visningen {0}.
##XFLD: Message used in case the cancelation is retriggered for a running task
VIEW_PERSISTENCY_CANCEL_ATTEMPT_RETRIGGER_CANCEL=En tidligere annulleringsopgave til at persistere visningen {0} er allerede sendt.
##XFLD: Message used to express the delay that may occur before the persistency task is stopped
VIEW_PERSISTENCY_CANCEL_DELAY_STOPPING_PERSISTENCY=Der kan være en forsinkelse, indtil datapersistensopgaven for visningen {0} er stoppet.
##XFLD: Message used to reflect the task log identifier of the current running persistency task
VIEW_PERSISTENCY_CANCEL_CURRENT_RUNNING_TASK=Data for visning {0} persisteres p.t. med opgaven {1}.
##XFLD:Message used by SEAL repository for locked partitions and DACs
VIEW_PERSISTENCY_LOCKED_PARTITIONS_DACS=Autorisationer angivet af dataadgangskontroller er muligvis ændret eller medtages ikke af låste partitioner. Lås partitionerne op, og indlæs et nyt snapshot for at anvende ændringerne.
##XFLD:Message used by SEAL repository for column structure change
VIEW_PERSISTENCY_COLUMN_STRUCTURE_CHANGE=Kolonnestrukturen er blevet ændret og stemmer ikke længere overens med den eksisterende persistenstabel. Fjern persisterede data, og start en ny datapersistens for at opdatere din persistenstabel.
##XFLD: Message for out of memory
VIEW_PERSISTENCY_HANA_OUT_OF_MEMORY=Opgaven blev ikke udført pga. fejlen Hukommelse opbrugt i SAP HANA-databasen.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_EXCEPTION_TRANSACTION_ROLLBACK=Opgaven blev ikke udført på grund af en intern undtagelse i SAP HANA-databasen.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_SQL_EXECUTION_ISSUE=Opgaven blev ikke udført på grund af et internt SQL-udførelsesproblem i SAP HANA-databasen.
##XFLD: Message for HANA out of memory result
VIEW_PERSISTENCY_OUT_OF_MEMORY_RESULT_MESSAGE=Årsag til HANA-out-of-memory-hændelse: {0}
##XFLD: Message for admission control rejection
VIEW_PERSISTENCY_HANA_ADMISSION_CTRL_REJECTION=Opgaven blev ikke udført pga. en afvisning af SAP HANA-adgangskontrol.
##XFLD: Message for HANA back pressure / rate limit of HANA client
VIEW_PERSISTENCY_HANA_CLIENT_RATE_LIMIT_REJECTION=Opgaven blev ikke udført pga. for mange aktive SAP HANA-forbindelser.
#XMSG: Message for invalid persisted table name
VIEW_PERSISTENCY_PERSISTED_TABLE_INVALID=Der opstod en fejl, og den persisterede tabel er blevet ugyldig. For at løse problemet skal du fjerne de persisterede data og persistere visningen igen.
#XMSG: Message for usahe of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION=Visningen kan ikke persisteres. Den bruger en ekstern tabel baseret på en ekstern kilde med brugeroverførsel aktiveret. Kontroller oprindelsen for visningen.
#XMSG: Message for dynamic usage of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION_DYNAMIC=Visningen kan ikke persisteres. Den bruger en ekstern tabel baseret på en ekstern kilde med brugeroverførsel aktiveret. Den eksterne tabel kan forbruges dynamisk via en SQL-script-visning. Oprindelsen for visningen viser muligvis ikke den eksterne tabel.
#XMSG: Message for insufficient privileges or consumption of view with DAC via dynamic SQL
VIEW_PERSISTENCY_INSUF_PRIV_OR_DAC_DYN_SQL=Dine rettigheder er muligvis ikke tilstrækkelige. Åbn eksempelvisning af data for at se, om du har de nødvendige rettigheder. Hvis ja, kan en anden visning, der forbruges via et dynamisk SQL-script, have dataadgangskontrol (DAC).
#XMSG: deployment of DAC is deprecated and view need to be redeployed to be able to persist data
VIEW_PERSISTENCY_DEPRECATED_DAC=Visningen "{0}" er implementeret ved hjælp af Data Access Control (DAC), der er ved at blive forældet. Implementer venligst visningen igen for at kunne persistere data for visningen.
##XFLD:Message used by SEAL repository for input parameter
START_VIEW_PERSISTENCY_INPUT_PARAMETER=Bruger standardværdi "{0}" for inputparameter "{1}".
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Elastic compute node keys~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
##XFLD:Message used to reflect disablement of ecn replica
ECN_DISABLE_EXISTING_REPLICA=Den elastiske beregningsnodereplika blev deaktiveret.
##XFLD:Message used to reflect recreation of ecn replica
ECN_RECREATE_EXISTING_REPLICA=Den elastiske beregningsnodereplika blev genoprettet.
##XFLD:Message used to reflect re-enablement of ecn replica
ECN_ENABLE_EXISTING_REPLICA=Den elastiske beregningsnodereplika blev genaktiveret.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Task Schedule Menu key~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message to schedule task
scheduleText=Tidsplan
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Opret tidsplan
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Rediger tidsplan
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Slet tidsplan
#XFLD: Refresh frequency field
refreshFrequency=Opdateringsfrekvens
#XFLD: Refresh frequency field
refreshFrequencyNew=Frekvens
#XFLD: Refresh frequency field
refreshFrequencyNewNew=Planlagt frekvens
#XBUT: label for None
none=Ingen
#XBUT: label for Real-Time replication state
realtime=Realtid
#XFLD: Label for table column
txtNextSchedule=Næste kørsel
#XFLD: Label for table column
txtNextScheduleNew=Planlagt næste kørsel
#XFLD: Label for table column
txtNumOfRecords=Antal poster
#XFLD: Label for scheduled link
scheduledTxt=Planlagt
#XFLD: LABEL for partially persisted link
partiallyPersisted=Delvist persisteret
#XFLD: Text for paused text
paused=Sat på pause

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ View Monitor Tasklog Details Action keys~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message for release lock actionn
releaseLockMsg=Hvis en kørsel tager længere tid end sædvanligt, kan dette være en indikation på, at den ikke er gennemført, og at status ikke er opdateret tilsvarende. \r\n Du kan løse problemet ved at frigøre låsen og angive dens status til mislykkedes.
#XFLD: Label for release lock dialog
releaseLockText=Frigør lås

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Column header tooltip ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: tooltip for table column
txtViewTooltip=Navn på den persisterede visning
#XFLD: tooltip for table column
txtViewDataAccessTooltip=Dette angiver visningens tilgængelighed
#XFLD: tooltip for table column
txtViewRefreshFreqTooltip=Dette angiver, om der er defineret en tidsplan for visningen
#XFLD: tooltip for table column
txtViewStatusTooltip=Hent status på den persisterede visning
#XFLD: tooltip for table column
txtViewLatestUpdateTooltip=Giver oplysninger om, hvornår den persisterede visning sidst blev opdateret
#XFLD: tooltip for table column
txtViewNextRunTooltip=Hvis der er defineret en tidsplan for visningen, så kontroller, hvornår næste kørsel er planlagt.
#XFLD: tooltip for table column
txtViewNumOfRecordsTooltip=Undersøg antal poster.
#XFLD: tooltip for table column
txtViewInMemorySizeTooltip=Undersøg, hvor meget visningen fylder i din hukommelse
#XFLD: tooltip for table column
txtViewDiskSizeTooltip=Undersøg, hvor meget plads visningen optager på din disk
#XMSG: Expired text
txtExpired=Udløbet

#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Objektet "{0}" kan ikke føjes til opgavekæden.

#~~~~~~~~~~~View Analyzer task log messages~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message indicating number of records for a view, and memory consumption in Mebibyte during simulation of view persistency
RECORDS_PEAK_MEMORY=Visningen "{0}" har {1} poster. En simulering af datapersistens for denne visning brugte {2} MiB hukommelse.
#XMSG: View Analyzer execution failure
VIEW_ANALYZER_EXECUTION_FAILED=Kunne ikke udføre visningsanalyse.
#XMSG: Missing authorizations for View Analyzer
VIEW_ANALYZER_MISSING_AUTH=Manglende autorisationer til visningsanalyse.
#XMSG: Maximum memory in Gibibyte is reached, therefore persistency simulation is stopped
MAXIMUM_MEMORY_REACHED=Maks. hukommelse på {0} GiB er nået under simulering af datapersistens for visning "{1}". Derfor køres der ingen yderligere simuleringer af datapersistens.
#XMSG: Error during persistency simulation occurred
PERSISTENCY_SIMULATION_ERROR=Der opstod en fejl under simulering af datapersistens for visningen "{0}".
#XMSG: Persistency simulation is not executed, because prerequisites are not fulfilled and the view cannot be persisted
PREREQUISITES_NOT_FULFILLED=Simuleringen af datapersitensen er ikke udført for visningen "{0}", fordi forudsætninger ikke er opfyldt, og visningen kan ikke persisteres.
#XMSG: Persistency simulation is not executed, because the view does not exist in the database
VIEW_DOES_NOT_EXIST=Du skal implementere visningen "{0}" for at aktivere datapersistenssimuleringen.
#XMSG: Number of records cannot be determined, because the local table does not exist in the database
TABLE_DOES_NOT_EXIST=Den lokale tabel "{0}" findes ikke i databasen, hvorfor antallet af poster ikke kan bestemmes for denne tabel.
##XMSG: Message start of cancellation for View Analyzer task
CANCEL_VIEW_ANALYZER_TRIGGERED=Proces, der stopper visningsanalyseopgave {0} for visning "{1}" er indsendt. Der kan være en forsinkelse, indtil opgaven stoppes.
##XMSG: Message start to indicate that task is not active
CANCEL_VIEW_ANALYZER_NO_ACTIVE_TASK=Visningsanalyseopgave {0} til visningen "{1}" er ikke aktiv.
#XMSG: Cancel View Analyzer task failure
CANCEL_VIEW_ANALYZER_FAILED=Kunne ikke annullere visningsanalyseopgave.
##XMSG: Message used in View Analyzer execution task when task is canceled
VIEW_ANALYZER_CANCELED=Udførelse af visningsanalyse for visningen "{0}" blev stoppet via en annulleringsopgave.
##XMSG: Message start of cancelation for Model Validation task
CANCEL_MODEL_VALIDATION_TRIGGERED=Proces, der stopper modelvalideringsopgaven {0} for visningen "{1}," er indsendt. Der kan være en forsinkelse, indtil opgaven stoppes.
##XMSG: Message to indicate that task is not active
CANCEL_MODEL_VALIDATION_NO_ACTIVE_TASK=Modelvalideringsopgaven {0} for visningen "{1}" er ikke aktiv.
#XMSG: Cancel Model Validation task failure
CANCEL_MODEL_VALIDATION_FAILED=Kunne ikke annullere modelvalideringsopgave.
##XMSG: Message used in Model Validation task when task is canceled
MODEL_VALIDATION_CANCELED=Udførelse af modelvalidering for visningen "{0}" blev stoppet via en annulleringsopgave.
#XMSG: Message for Locked space error, before starting Model Validation task
MODEL_VALIDATION_LOCKED_SPACE_ERROR=Kan ikke udføre modelvalidering for visningen "{0}", da spacet "{1}" er låst.
##XMSG: Message used in View Analyzer execution task when determination of number of rows fails
NUMBER_ROWS_DETERMINATION_FAILED=Der opstod en fejl under fastsættelse af antal rækker for den lokale tabel, "{0}".
##XMSG: Message to indicate that SQL plan was created
EXECUTION_PLAN_CREATED=SQL Analyzer-planlægningsfil for visningen "{0}" er oprettet og kan downloades.
#XFLD: Message to indicate starting of process to generate SQL Analyzer plan file
START_PLANVIZ_EXECUTION=Starter proces med at generere SQL Analyzer-planlægningsfil for visningen "{0}".
#XFLD: Message to indicate starting of View Analyzer process
VIEW_ANALYZER_EXECUTION_STARTED=Start udførelse af visningsanalyse:
##XMSG: Message to indicate that SQL plan file cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_PLANVIZ_NOT_FULFILLED=SQL Analyzer-planlægningsfil kan ikke genereres for visningen "{0}", da datapersistensforudsætninger ikke er opfyldt.
##XMSG: Error message during  generation of SQL Analyzer plan file
PLANVIZ_ERROR=Der opstod en fejl under generering af SQL Analyzer-planlægningsfil for visningen "{0}".
#XMSG: Message for Locked space error
VIEW_ANALYZER_LOCKED_SPACE_ERROR=Kan ikke udføre visningsanalyse for visningen "{0}", da spacet "{1}" er låst.
#XMSG: Message for persistency simulation without partitioning
PERSISTENCY_SIMULATION_WITHOUT_PARTITIONS=Partitionerne for visningen "{0}" medtages ikke under simulering af datapersistens.
#XMSG: Message for generation of SQL Analyzer plan file without considering partitioning
PLANVIZ_WITHOUT_PARTITIONS=Partitionerne for visningen "{0}" medtages ikke under generering af SQL Analyzer-planlægningsfil.
#XMSG: Confirmation message for mass removal of persisted data
MassRemovePersistency_Confirm_Msg=Vil du fjerne de persisterede data og skifte dataadgangen tilbage til virtuel adgang?
#XMSG: Confirmation message for partial mass removal of selected persisted data
PartialMassRemovePersistency_Confirm_Msg={0} af de {1} valgte visninger har persisterede data. \n Vil du fjerne de persisterede data og skifte dataadgangen tilbage til virtuel adgang?
#XMSG: success message for stopping mass persistence
stopMassPersistenceSuccess=Vi fjerner persisterede data for de valgte visninger.
#XMSG: error message during mass stop persistence of a view (backend error)
STOP_MASS_PERSISTENCE_ERROR=Der opstod en fejl under standsning af persistensen for de valgte visninger.
#XMSG: memory analysis is only performed in space {0}
NO_MEMORY_ANALYSIS=Hukommelsesanalyse udføres kun for entiteter i space "{0}": "{1}" "{2}" blev sprunget over.
##XMSG: Message to indicate that Explain Plan cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_EXPLAINPLAN_NOT_FULFILLED=Explain-plan-fil kan ikke genereres for visningen "{0}", da datapersistensforudsætninger ikke er opfyldt.
#XMSG: Message for Explain Plan generation without partitioning
EXPLAINPLAN_WITHOUT_PARTITIONS=Partitioner for visningen "{0}" medtages ikke under generering af Explain-plan-fil.
#XFLD: Message to indicate starting of process to generate Explain Plan file
START_EXPLAINPLAN_EXECUTION=Starter proces med at generere Explain-plan-fil for visningen "{0}".
##XMSG: Message to indicate that Explain Plan was created
EXPLAINPLAN_CREATED=Explain-plan-fil for visningen "{0}" blev genereret. Du kan se den ved at klikke på "Vis detaljer", eller du kan downloade den, hvis du har den relevante tilladelse.
##XMSG: Error message during  generation of Explain Plan file
EXPLAINPLAN_ERROR=Der opstod en fejl under generering af Explain-plan-fil for visningen "{0}".
##XMSG: Explain Plan file cannot be generated for the view {0}, because too many views are stacked on each other
EXPLAINPLAN_MODEL_TOO_COMPLEX=Explain-plan-fil kan ikke genereres for visningen "{0}". For mange visninger er stablet. Komplekse modeller kan forårsage problemer med manglende hukommelse og langsom ydeevne. Vi anbefaler, at du persisterer en visning.

##XMSG: Message to indicate that the space is locked
PERFORMANCE_ANALYSIS_LOCKED_SPACE_ERROR=Kan ikke udføre ydeevneanalyse for visning "{0}", da space "{1}" er låst.
##XMSG: The performance analysis task was cancelled
PERFORMANCE_ANALYSIS_CANCELED=Ydeevneanalyse for visningen "{0}" blev annulleret.
##XMSG: The performance analysis failed
PERFORMANCE_ANALYSIS_FAILED=Ydeevneanalyse mislykkedes.
##XMSG: The performance analysis is finished
PERFORMANCE_ANALYSIS_FINISHED=Ydeevneanalyse for visningen "{0}" er afsluttet. Vis resultatet ved at klikke på "Vis detaljer".
##XMSG: The view has input parameter with no default value
INPUT_PARAMETER_WITHOUT_DEFAULT_VALUE=Denne visning kan ikke analyseres, da den har en parameter uden standardværdi.
##XMSG: The view is not fully deployed
VIEW_NOT_FULLY_DEPLOYED=Denne visning kan ikke analyseres, da den ikke er fuldstændigt implementeret.
##XMSG: The view uses limited remote adapters
LIMITED_REMOTE_ADAPTER=Denne visning bruger mindst én ekstern adapter med begrænsede funktioner, såsom manglende filter-push-down eller support for 'Antal'. Persistering eller replikering af objekter kan forbedre kørselstidens ydeevne.
##XMSG: The view uses remote adapter that does not support 'Limit'
REMOTE_ADAPTER_NOT_SUPPORTING_LIMIT=Denne visning bruger mindst én ekstern adapter, der ikke understøtter 'Grænse'. Mere end 1.000 poster kan være valgt.
##XMSG: Performance analysis is executed by using default parameters of view'
INPUT_PARAMETER_WITH_DEFAULT_VALUE=Ydeevneanalysen udføres ved at bruge standardværdierne for visningsparametre.
##XMSG: Message to indicate that an error occurred
PERFORMANCE_ANALYSIS_ERROR=Der opstod en fejl under ydeevneanalysen for visningen "{0}".
##XMSG: Message start of cancellation for Performance Analysis task
CANCEL_PERFORMANCE_ANALYSIS_TRIGGERED=Proces, der stopper ydeevneanalyseopgave {0} for visning "{1}", er indsendt. Der kan være en forsinkelse, indtil opgaven stoppes.
##XMSG: Message start to indicate that task is not active
CANCEL_PERFORMANCE_ANALYSIS_NO_ACTIVE_TASK=Ydeevneanalyseopgave {0} for visningen "{1}" er ikke aktiv.
#XMSG: Cancel Performance Analysis task failure
CANCEL_PERFORMANCE_ANALYSIS_FAILED=Ydeevneanalyseopgave blev ikke annulleret.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Tildel tidsplan til mig
#XBUT: Pause schedule menu label
pauseScheduleLabel=Sæt tidsplan på pause
#XBUT: Resume schedule menu label
resumeScheduleLabel=Genoptag tidsplan
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Der opstod en fejl ved fjernelse af tidsplaner.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Der opstod en fejl ved tildeling af tidsplaner.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Der opstod en fejl, da tidsplaner blev sat på pause.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Der opstod en fejl ved genoptagelse af tidsplaner.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Sletter {0} tidsplaner
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Ændrer ejer af {0} tidsplaner
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Sætter {0} tidsplaner på pause
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Genoptager {0} tidsplaner
#XBUT: Select Columns Button
selectColumnsBtn=Vælg kolonner
#XFLD: Refresh tooltip
TEXT_REFRESH=Opdater
#XFLD: Select Columns tooltip
text_selectColumns=Vælg kolonner


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Runtime Metrics ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Label for Runtime Metrics
RuntimeMetricsTitle=Kørselstidsmetrikker for
#XFLD : Label for Run Button
runButton=Kør
#XFLD : Label for Cancel Button
cancelButton=Annuller
#XFLD : Label for Close Button
closeButton=Luk
#XFLD : Label for Open View Analyzer Button
openViewAnalyzerButton=Åbn visningsanalyse
#XFLD : Label for Generate Explain Plan Button
generateExplainPlanButton=Generer Explain-plan
#XFLD : Label for Previous Run Column
previousRun=Foregående kørsel
#XFLD : Label for Latest Run Column
latestRun=Seneste kørsel
#XFLD : Label for time Column
time=Tid
#XFLD : Label for Duration Column
duration=Varighed
#XFLD : Label for Peak Memory Column
peakMemory=Maksimal hukommelse
#XFLD : Label for Number of Rows
numberOfRows=Antal rækker
#XFLD : Label for Overall Number of Sources
overallNumberOfSources=Samlet antal kilder
#XFLD : Label for Data Access Column
dataAccess=Dataadgang
#XFLD : Label for Local Tables
localTables=Lokale tabeller
#XFLD : Label for federatedRemoteTables
federatedRemoteTables=Sammenkædede eksterne tabeller (med begrænsede adapterfunktioner)
#XTXT Text for initial state of the runtime metrics
initialState=Du skal først køre ydeevneanalysen for at få metrikkerne. Dette kan tage noget tid, men du kan om nødvendigt annullere processen.
#XMSG: Cancel confirmation message
CancelRunConfirmationMessage=Er du sikker på, at vil annullere den aktuelle kørsel af ydeevneanalysen?
#XTIT: Cancel dialog title
CancelRunTitle=Annuller kørsel
#XFLD: Label for Number of Rows
NUMBER_ROWS=Antal rækker
#XFLD: Label for Overall Number of Sources
OVERALL_NUMBER_SOURCES=Samlet antal kilder
#XFLD: Label for Data Access
DATA_ACCESS=Dataadgang
#XFLD: Label for Remote tables
NUMBER_REMOTE_TABLES_LIMITED_ADAPTERS=Sammenkædede eksterne tabeller (med begrænsede adapterfunktioner)
#XFLD: Label for select statement
SELECT_STATEMENT='SELECT * FROM VIEW LIMIT 1000'
#XFLD: Label for duration
SELECT_RUNTIME=Varighed
#XFLD: Label for peak memory
SELECT_PEAK_MEMORY=Maksimal hukommelse
#XFLD: Label for count statement
SELECT_COUNT_STATEMENT='SELECT COUNT(*) FROM VIEW'
#XFLD: Label for count duration
SELECT_COUNT_RUNTIME=Varighed
#XFLD: Label for count peak memory
SELECT_COUNT_PEAK_MEMORY=Maksimal hukommelse
#XFLD: Label for count of number of local tables
NUMBER_LOCAL_TABLES_FILE=Lokale tabeller (fil)
#XTXT: Text for running state of the runtime metrics
Running=Kører...
#XFLD: Label for time
Time=Tid
#XFLD: Label for virtual access
PA_VIRTUAL=Virtuel
#XFLD: Label for persisted access
PA_PERSISTED=Persisteret
PA_PARTIALLY_PERSISTED=Delvist persisteret
#XTXT: Text for cancel
CancelRunSuccessMessage=Annullerer kørslen af ydeevneanalysen.
#XTXT: Text for cancel error
CancelRunErrorMessage=Der opstod en fejl under annulleringen af ydeevneanalysen.
#XTXT: Text for explain plan generation
ExplainPlanStarted=Genererer Explain-plan for visningen "{0}".
#XTXT: Text for performance analysis
PerformanceAnalysisStarted=Starter ydeevneanalysen for visningen "{0}".
#XTXT: Text for performance analysis error
ErrorFetchingPerformanceAnalysis=Der opstod en fejl under hentningen af data for ydeevneanalysen.
#XTXT: Text for performance analysis error
conflictingTask=Ydeevneanalyseopgaven kører allerede
#XFLD: Label for Errors
Errors=Fejl
#XFLD: Label for Warnings
Warnings=Advarsel/advarsler
#XFLD: Tooltip for missing privileges
openViewAnalyzerButtonTooltipNoPrivilege=Du skal have (opdaterings)rettigheden DWC_DATAINTEGRATION for at åbne visningsanalysen.
#XFLD: Tooltip for missing privileges
generateExplainPlanButtonTooltipNoPrivilege=Du skal have (læse)rettigheden DWC_RUNTIME for at generere Explain-planen.



#XFLD: Label for frequency column
everyLabel=Hver
#XFLD: Plural Recurrence text for Hour
hoursLabel=Timer
#XFLD: Plural Recurrence text for Day
daysLabel=Dage
#XFLD: Plural Recurrence text for Month
monthsLabel=Måneder
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minutter
