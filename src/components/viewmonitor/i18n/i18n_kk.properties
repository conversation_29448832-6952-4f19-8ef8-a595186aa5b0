
#XBCB: Breadcrumb
viewMonitorBreadcrumb={spaceId}

#XFLD: Label for persisted view column
SOURCE=Бастапқы
#XFLD: Label for persisted view column
NAME=Атауы
#XFLD: Label for persisted view column
NAME_LABEL=Бизнес атау
#XFLD: Label for persisted view column
NAME_LABELNew=Нысан (бизнес атау)
#XFLD: Label for persisted view column
TECHINCAL_NAME=Техникалық атау
#XFLD: Label for persisted view column
TECHINCAL_NAMENew=Нысан (техникалық атау)
#XFLD: Label for persisted view column
DATA_PERSISTENCY=Дерекке қол жеткізу
#XFLD: Label for persisted view column
STATUS=Күйі
#XFLD: Label for persisted view column
LAST_UPDATED=Соңғы жаңартылған
#XFLD: Label for persisted view column
IN_MEMORY_SIZE=Сақтау үшін пайдаланылған жад (МиБ)
#XFLD: Label for persisted view column
DISK_SIZE=Сақтау үшін пайдаланылған диск (МиБ)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_NEWW=Жадтағы өлшем (МиБ)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_HEADER=Жадтағы өлшем
#XFLD: Label for persisted view column
DISK_SIZE_NEWW=Дискідегі өлшем (МиБ)
#XFLD: Label for persisted view column
DISK_SIZE_HEADER=Дискідегі өлшем
#XFLD: Label for schedule owner column
txtScheduleOwner=Кестеге жауапты тұлға
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Кестені жасаған адамды көрсетеді
#XFLD: Label for persisted view column
PERSISTED=Сақталған
#XFLD: Label for persisted view column
TYPE=Түрі
#XFLD: Label for View Selection Dialog column
changedOn=Өзгертілген күні
#XFLD: Label for View Selection Dialog column
createdBy=Жасаған
#XFLD: Label for log details column
txtViewPersistencyLogs=Журналдарды қарау
#XFLD: Label for log details column
txtViewPersistencyLogsNew=Мәліметтер
#XFLD: text for values shown for Ascending sort order
SortInAsc=Арту ретімен сұрыптау
#XFLD: text for values shown for Descending sort order
SortInDesc=Кему ретімен сұрыптау
#XFLD: Label for persisted view monitor heading
ViewMonitor_Heading=Көріністер мониторы
#XFLD: Label for persisted view monitor sub-heading
ViewMonitor_SubHeading=Көріністердің деректерін сақтауды бақылау және жүргізу


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ DynamicPageHeader ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: text for values shown in column Persistence Status
txtLoading=Жүктелуде
#XFLD: text for values shown in column Persistence Status
txtRunning=Орындалуда
#XFLD: text for values shown in column Persistence Status
txtAvailable=Қолжетімді
#XFLD: text for values shown in column Persistence Status
txtError=Қате
#XMSG: Error message for unknown Persistence type that gets logged in Kibana
unknownReplicationTypeError="{0}" тираждау түріне қолдау көрсетілмейді.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Content ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Соңғы деректерді сақтау сеансы үшін пайдаланылған параметрлер:
#XMSG: Message for input parameter name
inputParameterLabel=Кіріс параметрі
#XMSG: Message for input parameter value
inputParameterValueLabel=Мәні
#XMSG: Message for persisted data
inputParameterPersistedLabel=Сақталған уақыты:
#XTIT: Count for View in ViewMonitor Toolbar
txtViewCount=Көріністер ({0})
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Көріністі сақтау
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Деректерді сақтау
#XBUT: Button to clear the selected view persistance
ClearViewPersistance=Тазарту
#XBUT: Button to stop the selected view persistance
stopPersistance=Сақтауды тоқтату
#XFLD: Placeholder for Search field
txtSearch=Іздеу
#XBUT: Tooltip for refresh button
txtRefresh=Жаңарту
#XBUT: Tooltip for add view button
txtDeleteView=Сақтау мүмкіндігін жою
#XBUT: Tooltip for load new peristence
loadNewPersistence=Cақтауды қайта бастау
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Жаңа лездік суретті жүктеу
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Деректерді сақтауды бастау
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Сақталған деректерді жою
#XMSG: success message for starting persistence
startPersistenceSuccess=Біз "{0}" көрінісін сақтап жатырмыз.
#XMSG: success message for stopping persistence
stopPersistenceSuccess="{0}" көрінісі үшін сақталған деректерді жойып жатырмыз.
#XMSG: success message for stopping persistence
deletePersistedViewSuccess=Бақылау тізімінен "{0}" көрінісін жойып жатырмыз.
#XMSG: error message during start persistence of a view (backend error)
ERROR_STARTING_PERSISTENCE="{0}" көрінісі үшін деректерді сақтауды бастау кезінде қате орын алды.
#XMSG: error message during start persistence of a view (backend error)
PARAMERROR_STARTING_PERSISTENCE="{0}" көрінісін сақтау мүмкін емес, себебі ол кіріс параметрлерін қамтиды.
#XMSG: error message during start persistence of a view (backend error)
NUMBER_OF_PARAMETER_STARTING_PERSISTENCE="{0}" көрінісін сақтау мүмкін емес, себебі ол бірнеше кіріс параметрін қамтиды.
#XMSG: error message during start persistence of a view (backend error)
PARAMETER_DEFAULT_VALUE_STARTING_PERSISTENCE="{0}" көрінісін сақтау мүмкін емес, себебі кіріс параметрінде әдепкі мән жоқ.
#XMSG: error message during start persistence of a view (backend error)
REDEPLOYMENT_DAC_REQUIRED=Деректерді сақтауға қолдау көрсету үшін "{0}" дерекке қол жеткізуді басқару элементін (DAC) қолданысқа қайта енгізу талап етіледі.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_STARTING_PERSISTENCE="{0}" көрінісін сақтау мүмкін емес, себебі ол дерекке қол жеткізуді басқару элементін (DAC) қамтитын "{1}" көрінісін пайдаланады.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_EXTERNAL_STARTING_PERSISTENCE="{0}" көрінісін сақтау мүмкін емес, себебі ол басқа кеңістікке тиесілі дерекке қол жеткізуді басқару элементін (DAC) пайдаланады.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_STRUCTURE_STARTING_PERSISTENCE="{0}" көрінісін сақтау мүмкін емес, себебі дерекке қол жеткізуді басқару элементтерінің (DAC) бір немесе бірнеше құрылымы деректерді сақтау мүмкіндігіне қолдау көрсетпейді.
#XMSG: error message during stop persistence of a view (backend error)
STOP_PERSISTENCE_ERROR="{0}" көрінісі үшін сақтауды тоқтату кезінде қате орын алды.
#XMSG: error message during stop persistence of a view (backend error)
DELETE_PERSISTED_VIEW_ERROR=Сақталған "{0}" көрінісін жою кезінде қате орын алды.
#XMSG: Warning message for removing the persisted data
RemovePersistency_Confirm_Msg=Сақталған деректерді жою және "{0}" көрінісінің виртуалды қол жеткізу мүмкіндігіне ауыстыру қажет пе?
#XMSG: Warning message for delete the view persistence
DeletePersistency_Confirm_Msg=Көріністі бақылау тізімінен жою және "{0}" көрінісінің сақталған деректерін жою қажет пе?
#XMSG: error message for reading data from backend
txtReadBackendError=Бэкэндтен оқу кезінде қате орын алған сияқты.
#XFLD: Label for No Data Error
NoDataError=Қате
#XMSG: message for conflicting task
Task_Already_Running="{0}" көрінісі үшін қайшылықты тапсырма әлдеқашан орындалуда.

#XFLD: Error while accessing pertitioning for a view
missingPrivilegeInViewPersistency="{0}" көрінісі үшін бөлу әрекетін орындау үшін рұқсат жеткіліксіз.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Show All Views~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Text for show all views button
allViewsText=Барлық көріністер ({0})
#XBUT: Text for show scheduled views button
scheduledText=Жоспарланған ({0})
#XBUT: Text for show persisted views button
persistedText=Сақталған ({0})
#XBUT: Text for start analyzer button
startAnalyzer=Көріністерді талдау құралын іске қосу
#XFLD: Message if repository is unavailable
repositoryErrorMsg=Репозитарий қолжетімді емес және белгілі бір функциялар өшірілген.

#XFLD: Data Access - Virtual
Virtual=Виртуалды
#XFLD: Data Access - Persisted
Persisted=Сақталған

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Non-Persisted View Selection Dialog~~~~~~~~~~~~~~~~~~~~~~~

#XTIT: Title for Non-Persisted View selection dialog
select_source=Сақтау үшін көріністі таңдаңыз

#XTIT: Placeholder for Non-Persisted View selection dialog search
searchView=Көріністерді іздеу
#XTIT: No data in the list of non-persisted view
No_Data=Дерек жоқ
#XBUT: Button to select non-persisted view
ok=OK
#XBUT: Button to close the non-persisted views selection dialog
cancel=Бас тарту

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~View persistency log message key~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message for view persistency logs
START_STOP_PERSISTENCY="{1}" үшін деректерді сақтау тапсырмасы сеансы басталуда.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY="{1}" көрінісі үшін деректер сақталуда.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY)
START_VIEW_PERSISTENCY_PROCESS="{0}" көрінісі үшін деректерді сақтау процесі басталуда.
#XFLD: Message for view persistency manual partition load
START_VIEW_PERSISTENCY_PROCESS_MANUAL_PARTITIONS=Таңдалған бөлік идентификаторларына ("{1}") ие "{0}" көрінісі үшін деректерді сақтау процесі басталуда.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY="{1}" көрінісі үшін сақталған деректер жойылуда.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY)
REMOVE_VIEW_PERSISTENCY="{0}" көрінісі үшін сақталған деректерді жою процесі басталуда.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_SUCCESS=Деректер "{1}" көрінісі үшін сақталды.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_SUCCESS)
START_VIEW_PERSISTENCY_PROCESS_SUCCESS=Деректер "{0}" көрінісі үшін сақталды.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY_SUCCESS="{1}" көрінісі үшін сақталған деректер жойылды және вируталды деректерге қол жеткізу рұқсаты қалпына келтірілді.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_SUCCESS)
REMOVE_VIEW_PERSISTENCY_SUCCESS="{0}" көрінісі үшін сақталған деректерді жою процесі аяқталды.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_FAILED="{1}" көрінісі үшін деректерді сақтау мүмкін емес.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_FAILED)
START_VIEW_PERSISTENCY_PROCESS_FAILED="{0}" көрінісі үшін деректерді сақтау мүмкін емес.
#XFLD: Message for view persistency logs (for messages created before version 2022.05)
STOP_VIEW_PERSISTENCY_FAILED="{1}" көрінісі үшін сақталған деректерді жою мүмкін емес.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_FAILED)
REMOVE_VIEW_PERSISTENCY_FAILED="{0}" көрінісі үшін сақталған деректерді жою мүмкін емес.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_SUCCESS="{1}" көрінісі үшін "{3}" жазбалары сақталды.
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace START_PERSISTENCY_RECORD_COUNT_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS="{1}" көрінісі үшін деректерді сақтау кестесіне {0} жазба мұраланды.
#XFLD: Message used by SEAL repository for view persistency logs (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS_V2="{1}" көрінісі үшін деректерді сақтау кестесіне {0} жазба мұраланды. Пайдаланылған жад:  {2} ГиБ.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_SUCCESS="{1}" көрінісі үшін "{3}" сақталған жазбалары жойылды.
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace STOP_PERSISTENCY_RECORD_COUNT_SUCCESS)
REMOVE_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=Сақталған деректер жойылды, "{0}" сақталған жазбалары жойылды.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_FAILED="{1}" көрінісі үшін жазбалар санын алу сәтсіз аяқталды.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_FAILED="{1}" көрінісі үшін жазбалар санын алу сәтсіз аяқталды.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS="{1}" үшін кесте жойылды.
#XFLD: Message for view persistency logs (new message to replace STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS)
REMOVE_VIEW_PERSISTENCY_DELETE_SCHEDULE_SUCCESS="{0}" көрінісі үшін кесте жойылды.
#XFLD: Message for view persistency logs
STOP_PERSISTENCY_DELETE_SCHEDULE_FAILED="{1}" үшін кестені жою сәтсіз аяқталды.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_REDEPLOY="{0}" көрінісін сақтау мүмкін емес, себебі сіз оны сақтай бастағаннан бері ол өзгертілді және қолданысқа енгізілді.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_DELETE="{0}" көрінісін сақтау мүмкін емес, себебі сіз оны сақтай бастағаннан бері ол жойылды.
#XFLD: Message for successful data persistency of a single partition (for messages created before version 2022.03)
START_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} жазба "{1}" <= {2} < "{3}" мәндеріне арналған бөлікке сақталды.
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (new message to replace START_PERSISTENCY_SINGLE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} жазба "{1}" <= {2} < "{3}" мәндеріне арналған бөлікке енгізілді.
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS_V2={0} жазба "{1}" <= {2} < "{3}" мәндеріне арналған бөлікке енгізілді. Пайдаланылған жад: {4} ГиБ.
#XFLD: Message for successful replication of a "others" partition (for messages created before version 2022.03)
START_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} жазба "басқалар" бөлігіне сақталды.
#XFLD: Message used by SEAL repository for successful replication of a "others" partition (new message to replace START_PERSISTENCY_OTHERS_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} жазба "басқалар" бөлігіне енгізілді.
#XFLD: Message for view persistency record count of partitions (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS="{1}" көрінісі үшін {3} жазба {4} бөлікке сақталды.
#XFLD: Message used by SEAL repository for view persistency record count of partitions (new message to replace START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={2} бөліктегі "{1}" көрінісі үшін деректерді сақтау кестесіне {0} жазба енгізілді.
#XFLD: Message used by SEAL repository for view persistency for partition wise refresh
START_VIEW_PERSISTENCY_RECORD_COUNT_REFRESHED_PARTITIONS_SUCCESS="{1}" көрінісі үшін деректерді сақтау кестесіне {0} жазба енгізілді. Жаңартылған бөлік: {2}; Құлыпталған бөлік: {3}; Жалпы бөлік: {4}
#XFLD: Message used by SEAL repository for view persistency for manual partition load
START_VIEW_PERSISTENCY_RECORD_COUNT_MANUAL_PARTITIONS_SUCCESS={2} таңдалған бөліктегі "{1}" көрінісі үшін деректерді сақтау кестесіне {0} жазба енгізілді.
#XFLD: Message used by SEAL repository for view persistency for locked partition data load
START_VIEW_PERSISTENCY_RECORD_COUNT_LOCKED_PARTITIONS_SUCCESS="{1}" көрінісі үшін деректерді сақтау кестесіне {0} жазба енгізілді. Жаңартылған бөлік: {2}; Құлыпталған, өзгертілмеген бөлік: {3}; Жалпы бөлік: {4}
#XMSG: Message for unexpected error (for messages created before version 2022.03)
START_PERSISTENCY_UNEXPECTED_ERROR="{0}" көрінісі үшін деректерді сақтау кезінде күтілмеген қате орын алды.
#XMSG: Message for unexpected error (new message to replace START_PERSISTENCY_UNEXPECTED_ERROR)
START_VIEW_PERSISTENCY_UNEXPECTED_ERROR="{0}" көрінісі үшін деректерді сақтау кезінде күтілмеген қате орын алды.
#XMSG: Message for Locked space error
START_VIEW_PERSISTENCY_LOCKED_SPACE_ERROR="{0}" көрінісін сақтау мүмкін емес, себебі "{1}" кеңістігі құлыпталды.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
STOP_PERSISTENCY_UNEXPECTED_ERROR="{0}" көрінісі үшін сақталған деректерді жою кезінде күтілмеген қате орын алды.
#XMSG: Message for unexpected error (new message to replace STOP_PERSISTENCY_UNEXPECTED_ERROR)
REMOVE_VIEW_PERSISTENCY_UNEXPECTED_ERROR="{0}" көрінісі үшін сақтау мүмкіндігін жою кезінде күтілмеген қате орын алды.
#XMSG: Message for ivalidated view
VIEW_PERSISTENCY_ABORTED_DEFINITION_INVALID="{0}" көрінісінің анықтамасы жарамсыз болды, бұл, ең алдымен, көрініс тікелей немесе жанама түрде қабылдаған нысанның өзгеруіне байланысты. Мәселені шешу немесе негізгі себебін анықтау үшін көріністі қолданысқа қайта енгізіп көріңіз.
#XMSG: Message for persisted data removal (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT="{0}" көрінісін қолданысқа енгізу кезінде сақталған деректер жойылды.
#XMSG: Message for persisted data removal of a consuming view
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW=Тұтынылатын "{0}" көрінісін қолданысқа енгізу кезінде сақталған деректер жойылды.
#XMSG: Message for persisted data removal of a consuming view due to a dac change
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW_DAC_CHANGED=Тұтынылатын "{0}" көрінісін қолданысқа енгізу кезінде сақталған деректер жойылды, себебі оның деректеріне қол жеткізуді басқару элементі өзгертілді.
#XMSG: Message for persisted data removal (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DEPLOYMENT="{0}" көрінісін қолданысқа енгізу кезінде сақталған деректер жойылды.
#XMSG: Message for deleted view (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION=Деректерді сақтау мүмкіндігі "{0}" көрінісімен бірге жойылды.
#XMSG: Message for deleted view (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DELETION=Деректерді сақтау мүмкіндігі "{0}" көрінісімен бірге жойылды.
#XMSG Message for removal of persisted data, because prerequisites not fulfilled
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_PREREQUISITES_NOT_FULFILLED=Сақталған деректер жойылды, себебі деректерді сақтаудың алғышарттары бұдан былай орындалмайды.
#XMSG: Message for inconsistent persistency
VIEW_PERSISTENCY_INCONSISTENT_PERSISTENCY="{0}" көрінісінің деректерін сақтау мүмкіндігі сәйкес келмейді. Мәселені шешу үшін сақталған деректерді жойыңыз.
#XMSG: Message for prerequisites
START_PERSISTENCY_CHECK_PREREQUISITES="{0}" көрінісін сақтаудың алғышарттары тексерілуде.
#XMSG: Message for DAC deployed in deprecated way using table function and join (for messages created before 2025.05)
START_PERSISTENCY_DEPRECIATED_DAC=Қолданыстан шыққалы жатқан дерекке қол жеткізуді басқару арқылы "{0}" көрінісі қолданысқа енгізілді. Өнімділікті жақсарту үшін көріністі қайта қолданысқа енгізіңіз.
#XMSG: Message for DAC deployed in deprecated way using table function and join
START_PERSISTENCY_DEPRECATED_DAC=Қолданыстан шыққалы жатқан дерекке қол жеткізуді басқару арқылы "{0}" көрінісі қолданысқа енгізілді. Өнімділікті жақсарту үшін көріністі қайта қолданысқа енгізіңіз.
#XMSG: Message for roll back (for messages created before version 2022.03)
ROLLBACK_VIEW_PERSISTENCY=Қате орын алды. "{0}" көрінісі үшін бұрынғы сақтау күйі қалпына келтірілді.
#XMSG: Message used by SEAL repository for rollback of running procedure to persist view (new message to replace ROLLBACK_VIEW_PERSISTENCY)
VIEW_PERSISTENCY_ROLLBACK=Қате орын алды. "{0}" көрінісін сақтау процесі тоқтатылды және өзгерістерден бас тартылды.
#XMSG: Message used by SEAL repository for rollback of running procedure to remove persisted data of view
REMOVE_VIEW_PERSISTENCY_ROLLBACK=Қате орын алды. "{0}" көрінісінің сақталған деректерін жою процесі тоқтатылды және өзгерістерден бас тартылды.
#XMSG: Message used by SEAL repository for persistency preparation
START_PERSISTENCY_PREPARATION=Деректерді сақтауға дайындалуда.
#XMSG: Message used by SEAL repository for data insertion
START_INSERTING_PERSISTENCY_DATA=Деректерді сақтау кестесіне деректер кірістірілуде.
#XMSG: Message for null values - partitioning (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS=NULL мәніне ие {0} жазба "басқалар" бөлігіне енгізілді.
#XMSG: Message used by SEAL repository for others partition values - partitioning (new message to replace START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS={0} жазба "{2}" < "{1}" НЕМЕСЕ "{2}" >= "{3}" мәндеріне арналған "басқалар" бөлігіне енгізілді.
#XMSG: Message used by SEAL repository for others partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS_V2={0} жазба "{2}" < "{1}" НЕМЕСЕ "{2}" >= "{3}" мәндеріне арналған "басқалар" бөлігіне енгізілді. Пайдаланылған жад: {4} ГиБ.
#XMSG: Message used by SEAL repository for null partition values - partitioning
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS={0} жазба "{1}" IS NULL мәндеріне арналған "басқалар" бөлігіне енгізілді.
#XMSG: Message used by SEAL repository for null partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS_V2={0} жазба "{1}" IS NULL мәндеріне арналған "басқалар" бөлігіне енгізілді. Пайдаланылған жад: {2} ГиБ.
##XFLD:Message used by SEAL repository for remote statistics
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS=Қатысты деректер жүктелуде: {0} қашықтағы оператор. Жалпы алынған жазбалар: {1}. Жалпы ұзақтығы: {2} секунд.
##XFLD:Message used by SEAL repository for remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS_WITH_PARTITIONS={1} қашықтағы операторға ие {0} бөлік арқылы қатысты деректер жүктелуде. Жалпы алынған жазбалар: {2}. Жалпы ұзақтығы: {3} секунд.
##XFLD:Message used by SEAL repository with UI ID for instructions of remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_INSTRUCTION=Сеанс кезінде өңделген қашықтағы операторларды бөлікке тән хабарлардың мәліметтерінде қашықтағы сұрау мониторын ашу арқылы көрсетуге болады.
##XFLD:Message for start reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS=Қолданысқа енгізгеннен кейін {0} көрінісі үшін бұрыннан бар сақталған деректерді қайта пайдалану процесін бастау.
##XFLD:Message used by SEAL repository for reattach view persitency
START_VIEW_PERSISTENCY_REATTACHMENT=Бұрыннан бар сақталған деректерді қайта пайдалану үшін бастаңыз.
##XFLD:Message used by SEAL repository for reattach view persitency
START_FINILIZING_VIEW_PERSISTENCY_REATTACHMENT=Бұрыннан бар сақталған деректер қайта пайдаланылуда.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_COMPLETE={0} көрінісі үшін бұрыннан бар сақталған деректерді қайта пайдалану процесі аяқталды.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_FAILED=Қолданысқа енгізгеннен кейін {0} көрінісі үшін бұрыннан бар сақталған деректерді қайта пайдалану сәтсіз аяқталды.
#XMSG: Message used by SEAL repository for finalizing persistency
START_FINALIZING_PERSISTENCY=Сақтау аяқталуда.
#XMSG: Message used by SEAL repository for restoring virtual access for view
REMOVE_VIEW_PERSISTENCY_VIRTUAL_ACCESS_RESTORED="{0}" көрінісі үшін вируталды деректерге қол жеткізу рұқсаты қалпына келтірілді.
#XMSG: Message used by SEAL repository for view that already has virtual access
REMOVE_VIEW_PERSISTENCY_ALREADY_VIRTUAL_ACCESS="{0}" көрінісінде виртуалды дерекке қол жеткізу рұқсаты бұрыннан бар. Сақталған деректер жойылмады.
#XMSG: Message for removing view persistency
VIEW_PERSISTENCY_REMOVED_FROM_MONITOR="{0}" көрінісі "Көріністер" мониторынан жойылды.
#XMSG: Message for view doesnt exist.
VIEW_PERSISTENCY_VIEW_NOT_EXISTING="{0}" көрінісі дерекқорда жоқ немесе қолданысқа дұрыс енгізілмеген, сондықтан оны сақтау мүмкін емес. Мәселені шешу немесе негізгі себебін анықтау үшін, көріністі қайта қолданысқа енгізіп көріңіз.
#XMSG: Message for persistency not enabled.
VIEW_PERSISTENCY_NOT_ENABLED=Деректерді сақтау қосылмаған. Функцияны іске қосу үшін, "{0}" кеңістігінде кестені/көріністі қайта қолданысқа енгізіңіз.
#XMSG: Message for unexpected error due to node crash
VIEW_PERSISTENCY_UNEXPECTED_ERROR_GENERAL=Соңғы көріністі сақтау сеансы техникалық қателерге байланысты үзілді.
#XFLD: Message for view persistency memory consumption
VIEW_PERSISTENCY_MEMORY_CONSUMPTION=Көріністі сақтауды орындау уақытында {0} ГиБ максималды жад пайдаланылды.
#XFLD: Message for view persistency timeout
VIEW_PERSISTENCY_TIMEOUT={0} көрінісі дерегін сақтау {1} сағатқа созылды.
#XFLD: Message for not started asynchronous view persistency
VIEW_PERSISTENCY_ASYNC_NOT_STARTED=Жүйенің жоғары жүктемесі көріністі сақтаудың бейсинхронды орындалуына жол бермеді. Тым көп тапсырмалар параллельді түрде орындалып жатқанын тексеріңіз.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency (for messages created before version 2022.05)
START_VIEW_PERSISTENCY_EXECUTION_METHOD_DROP_AND_RENAME=Бұрыннан бар сақталған кесте жойылды және жаңа сақталған кестемен ауыстырылды.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_RECREATE_AND_DROP=Бұрыннан бар сақталған кесте жойылды және жаңа сақталған кестемен ауыстырылды.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_TRUNCATE_AND_COPY=Бұрыннан бар сақталған кесте жаңа деректермен жаңартылды.
##XFLD: Message for missing authorizations for view persistency
VIEW_PERSISTENCY_MISSING_AUTH=Деректерді сақтау үшін өкілеттіктер жоқ.
##XFLD: Message start of persistency cancellation
VIEW_PERSISTENCY_TRIGGER_CANCEL="{0}" көрінісін сақтау процесінен бас тарту басталуда.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_UNAVAILABLE_TASK_RUN=Көріністі сақтау процесінен бас тарту мүмкін болмады, себебі {0} көрінісі үшін орындалып жатқан деректі сақтау тапсырмасы жоқ.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_NONDETERMINISTIC_TASK_RUN=Көріністі сақтау процесінен бас тарту мүмкін болмады, себебі {0} көрінісі үшін ешқандай деректі сақтау тапсырмасы орындалып жатқан жоқ.
##XFLD: Message for canceling a non running persitency task
VIEW_PERSISTENCY_CANCEL_NON_RUNNING_SELECTED_TASK={0} көрінісін сақтау процесінен бас тарту мүмкін болмады, себебі {1} таңдалған деректерді сақтау тапсырмасы орындалып жатқан жоқ.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_UNREACHABLE_PERSISTENCY_RUN=Көріністі сақтау процесінен бас тарту мүмкін болмады, себебі {0} көрінісі үшін деректі сақтау әлі басталған жоқ.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_COMPLETED_PERSISTENCY_RUN="{0}" көрінісін сақтау процесінен бас тарту сәтсіз аяқталды, себебі ол әлдеқашан орындалып қойған.
##XFLD: Message for failed cancellation
VIEW_PERSISTENCY_ABORTED_CANCEL_EXECUTION="{0}" көрінісін сақтау процесінен бас тарту сәтсіз аяқталды.
##XFLD: Message for finalizing persistency cancellation
VIEW_PERSISTENCY_FINALIZE_CANCEL={0} көрінісінің деректерін сақтауды тоқтату процесі жіберілді.
##XFLD: Message used in persist task when cancellation is caught
VIEW_PERSISTENCY_CANCELED={0} көрінісін сақтау процесі тоқтатылды.
##XFLD: Message used in persist task when cancelation is caught and performed through a cancelation task
VIEW_PERSISTENCY_CANCELED_VIA_CANCEL_TASK={0} көрінісін сақтау процесі {1} бас тарту тапсырмасы арқылы тоқтатылды.
##XFLD: Message used in cancel task when the task is triggered by deployment
VIEW_PERSISTENCY_CANCEL_TRIGGERED_BY_DEPLOYMENT={0} көрінісін қолданысқа енгізу кезінде деректерді сақтау процесінен бас тартылуда.
##XFLD: Message used in case the cancelation is retriggered for a running task
VIEW_PERSISTENCY_CANCEL_ATTEMPT_RETRIGGER_CANCEL={0} көрінісін сақтау үшін алдыңғы бас тарту тапсырмасы әлдеқашан жіберілген.
##XFLD: Message used to express the delay that may occur before the persistency task is stopped
VIEW_PERSISTENCY_CANCEL_DELAY_STOPPING_PERSISTENCY={0} көрінісіне арналған деректерді сақтау тапсырмасы тоқтатылғанша кідіріс болуы мүмкін.
##XFLD: Message used to reflect the task log identifier of the current running persistency task
VIEW_PERSISTENCY_CANCEL_CURRENT_RUNNING_TASK={0} көрінісіне арналған деректер {1} тапсырмасымен сақталуда.
##XFLD:Message used by SEAL repository for locked partitions and DACs
VIEW_PERSISTENCY_LOCKED_PARTITIONS_DACS=DAC берген өкілеттіктер өзгерген болуы мүмкін және оларды құлыпталған бөліктер қарастырмайды. Бөліктердің құлпын ашыңыз және өзгертулерді қолдану үшін жаңа суретті жүктеңіз.
##XFLD:Message used by SEAL repository for column structure change
VIEW_PERSISTENCY_COLUMN_STRUCTURE_CHANGE=Баған құрылымы өзгертілді және бұдан былай бұрыннан бар деректерді сақтау кестесіне сәйкес келмейді. Сақталған деректерді жойыңыз және деректерді сақтау кестесін жаңарту үшін жаңа деректерді сақтау тапсырмасын бастаңыз.
##XFLD: Message for out of memory
VIEW_PERSISTENCY_HANA_OUT_OF_MEMORY=SAP HANA дерекқорындағы жадтың таусылу қатесіне байланысты тапсырма орындалмады.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_EXCEPTION_TRANSACTION_ROLLBACK=SAP HANA дерекқорындағы ішкі ерекшелікке байланысты тапсырма орындалмады.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_SQL_EXECUTION_ISSUE=SAP HANA дерекқорындағы ішкі SQL орындау мәселесіне байланысты тапсырма орындалмады.
##XFLD: Message for HANA out of memory result
VIEW_PERSISTENCY_OUT_OF_MEMORY_RESULT_MESSAGE=HANA жадының таусылуы оқиғасының себебі: {0}
##XFLD: Message for admission control rejection
VIEW_PERSISTENCY_HANA_ADMISSION_CTRL_REJECTION=SAP HANA рұқсаттарды бақылаудан бас тартуына байланысты тапсырманы орындау сәтсіз аяқталды.
##XFLD: Message for HANA back pressure / rate limit of HANA client
VIEW_PERSISTENCY_HANA_CLIENT_RATE_LIMIT_REJECTION=Тапсырма орындалмады, себебі белсенді SAP HANA қосылымдары тым көп.
#XMSG: Message for invalid persisted table name
VIEW_PERSISTENCY_PERSISTED_TABLE_INVALID=Қате орын алды және сақталған кесте жарамсыз болды. Мәселені шешу үшін, сақталған деректерді жойып, көріністі қайта сақтаңыз.
#XMSG: Message for usahe of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION=Көріністі сақтау мүмкін емес. Ол пайдаланушы деректерін тарату мүмкіндігі қосылған қашықтағы дереккөзге негізделген қашықтағы кестені пайдаланады. Көріністің шығу тегін тексеріңіз.
#XMSG: Message for dynamic usage of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION_DYNAMIC=Көріністі сақтау мүмкін емес. Ол пайдаланушы деректерін тарату мүмкіндігі қосылған қашықтағы дереккөзге негізделген қашықтағы кестені пайдаланады. Қашықтағы кестені SQL сценарий көрінісі арқылы динамикалық түрде пайдалануға болады. Көріністің шығу тегі қашықтағы кестені көрсетпеуі мүмкін.
#XMSG: Message for insufficient privileges or consumption of view with DAC via dynamic SQL
VIEW_PERSISTENCY_INSUF_PRIV_OR_DAC_DYN_SQL=Артықшылықтарыңыз жеткіліксіз болуы мүмкін. Қажетті артықшылықтардың бар-жоғын көру үшін Деректі алдын ала қарауды ашыңыз. Иә болса, динамикалық SQL сценарийі арқылы пайдаланылатын екінші көріністе оған қолданылған дерекке қол жеткізуді басқару элементі (DAC) болуы мүмкін.
#XMSG: deployment of DAC is deprecated and view need to be redeployed to be able to persist data
VIEW_PERSISTENCY_DEPRECATED_DAC=Қолданыстан шыққалы жатқан дерекке қол жеткізуді басқару арқылы "{0}" көрінісі қолданысқа енгізілді. Көрініске арналған деректі сақтау үшін көріністі қайта қолданысқа енгізіңіз.
##XFLD:Message used by SEAL repository for input parameter
START_VIEW_PERSISTENCY_INPUT_PARAMETER="{1}" кіріс параметрі үшін "{0}" әдепкі мәні пайдаланылуда.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Elastic compute node keys~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
##XFLD:Message used to reflect disablement of ecn replica
ECN_DISABLE_EXISTING_REPLICA=Серпімді есептеу түйінінің көшірмесі өшірілген.
##XFLD:Message used to reflect recreation of ecn replica
ECN_RECREATE_EXISTING_REPLICA=Серпімді есептеу түйінінің көшірмесі қайта жасалды.
##XFLD:Message used to reflect re-enablement of ecn replica
ECN_ENABLE_EXISTING_REPLICA=Серпімді есептеу түйінінің көшірмесі қайта қосылды.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Task Schedule Menu key~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message to schedule task
scheduleText=Жоспарлау
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Кесте жасау
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Кестені өңдеу
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Кестені жою
#XFLD: Refresh frequency field
refreshFrequency=Жаңарту жиілігі
#XFLD: Refresh frequency field
refreshFrequencyNew=Жиілік
#XFLD: Refresh frequency field
refreshFrequencyNewNew=Жоспарланған жиілік
#XBUT: label for None
none=Ешқайсысы
#XBUT: label for Real-Time replication state
realtime=Нақты уақыт
#XFLD: Label for table column
txtNextSchedule=Келесі сеанс
#XFLD: Label for table column
txtNextScheduleNew=Келесі жоспарланған сеанс
#XFLD: Label for table column
txtNumOfRecords=Жазбалар саны
#XFLD: Label for scheduled link
scheduledTxt=Жоспарланды
#XFLD: LABEL for partially persisted link
partiallyPersisted=Ішінара сақталды
#XFLD: Text for paused text
paused=Кідіртілді

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ View Monitor Tasklog Details Action keys~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message for release lock actionn
releaseLockMsg=Егер сеанс әдеттегіден ұзағырақ болса, бұл оның орындалмағанын және күйдің сәйкесінше жаңартылмағанын көрсетуі мүмкін. \r\n Мәселені шешу үшін, құлыптан босатып, оның күйін сәтсіз деп орнатуға болады.
#XFLD: Label for release lock dialog
releaseLockText=Құлыптан босату

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Column header tooltip ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: tooltip for table column
txtViewTooltip=Сақталған көріністің атауы
#XFLD: tooltip for table column
txtViewDataAccessTooltip=Бұл көріністің қолжетімділігін көрсетеді.
#XFLD: tooltip for table column
txtViewRefreshFreqTooltip=Бұл кестенің көрініс үшін анықталғанын не анықталмағанын көрсетеді
#XFLD: tooltip for table column
txtViewStatusTooltip=Сақталған көріністің күйін алыңыз
#XFLD: tooltip for table column
txtViewLatestUpdateTooltip=Сақталған көріністің соңғы рет жаңартылған уақыты туралы ақпаратты көрсетеді
#XFLD: tooltip for table column
txtViewNextRunTooltip=Егер көрініс үшін кесте орнатылса, келесі сеанстың қай уақытқа жоспарланғанын қараңыз
#XFLD: tooltip for table column
txtViewNumOfRecordsTooltip=Жазбалар санын қадағалаңыз.
#XFLD: tooltip for table column
txtViewInMemorySizeTooltip=Көрініс жадыңызда қанша көлемді пайдаланатынын қадағалаңыз
#XFLD: tooltip for table column
txtViewDiskSizeTooltip=Көріністің дискіңіздегі өлшемін қадағалаңыз
#XMSG: Expired text
txtExpired=Мерзімі өткен

#XMSG: Cannot be Added to the chain
NOT_CHAINABLE="{0}" нысанын тапсырмалар тізбегіне қосу мүмкін емес.

#~~~~~~~~~~~View Analyzer task log messages~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message indicating number of records for a view, and memory consumption in Mebibyte during simulation of view persistency
RECORDS_PEAK_MEMORY="{0}" көрінісінде {1} жазба бар. Осы көрініс үшін деректерді сақтауды үлгілеу {2} МиБ жадты пайдаланды.
#XMSG: View Analyzer execution failure
VIEW_ANALYZER_EXECUTION_FAILED=Көрініс талдағыш тапсырмасын орындау сәтсіз аяқталды.
#XMSG: Missing authorizations for View Analyzer
VIEW_ANALYZER_MISSING_AUTH=Көрініс талдағыш үшін өкілеттіктер жоқ.
#XMSG: Maximum memory in Gibibyte is reached, therefore persistency simulation is stopped
MAXIMUM_MEMORY_REACHED="{1}" көрінісі үшін деректерді сақтауды үлгілеу кезінде {0} ГиБ көлеміндегі максималды жад пайдаланылды. Сондықтан басқа деректерді сақтауды үлгілеу сеанстары іске қосылмайды.
#XMSG: Error during persistency simulation occurred
PERSISTENCY_SIMULATION_ERROR="{0}" көрінісі үшін деректерді сақтауды үлгілеу кезінде қате орын алды.
#XMSG: Persistency simulation is not executed, because prerequisites are not fulfilled and the view cannot be persisted
PREREQUISITES_NOT_FULFILLED="{0}" көрінісі үшін деректерді сақтауды үлгілеу орындалмады, себебі алғыгарттары орындалмады және көріністі сақтау мүмкін емес.
#XMSG: Persistency simulation is not executed, because the view does not exist in the database
VIEW_DOES_NOT_EXIST=Деректерді сақтауды үлгілеуді іске қосу үшін "{0}" көрінісін қолданысқа енгізуіңіз керек.
#XMSG: Number of records cannot be determined, because the local table does not exist in the database
TABLE_DOES_NOT_EXIST="{0}" жергілікті кестесі дерекқорда жоқ, сондықтан осы кесте үшін жазбалар санын анықтау мүмкін емес.
##XMSG: Message start of cancellation for View Analyzer task
CANCEL_VIEW_ANALYZER_TRIGGERED="{1}" көрінісі үшін көрініс талдағыштың {0} тапсырмасын тоқтату процесі жіберілді. Тапсырма тоқтатылғанға дейін кідіріс болуы мүмкін.
##XMSG: Message start to indicate that task is not active
CANCEL_VIEW_ANALYZER_NO_ACTIVE_TASK="{1}" көрінісі үшін көрініс талдағыштың {0} тапсырмасы белсенді емес.
#XMSG: Cancel View Analyzer task failure
CANCEL_VIEW_ANALYZER_FAILED=Көрініс талдағыш тапсырмасынан бас тарту сәтсіз аяқталды.
##XMSG: Message used in View Analyzer execution task when task is canceled
VIEW_ANALYZER_CANCELED="{0}" көрінісі үшін көрініс талдағыш тапсырмасын орындау процесі тапсырмадан бас тарту мүмкіндігі арқылы тоқтатылды.
##XMSG: Message start of cancelation for Model Validation task
CANCEL_MODEL_VALIDATION_TRIGGERED="{1}" көрінісі үшін үлгіні тексерудің {0} тапсырмасын тоқтату процесі жіберілді. Тапсырма тоқтатылғанға дейін кідіріс болуы мүмкін.
##XMSG: Message to indicate that task is not active
CANCEL_MODEL_VALIDATION_NO_ACTIVE_TASK="{1}" көрінісі үшін үлгіні тексерудің {0} тапсырмасы белсенді емес.
#XMSG: Cancel Model Validation task failure
CANCEL_MODEL_VALIDATION_FAILED=Үлгіні тексеру тапсырмасынан бас тарту сәтсіз аяқталды.
##XMSG: Message used in Model Validation task when task is canceled
MODEL_VALIDATION_CANCELED="{0}" көрінісі үшін үлгіні тексеру тапсырмасын орындау процесі тапсырмадан бас тарту мүмкіндігі арқылы тоқтатылды.
#XMSG: Message for Locked space error, before starting Model Validation task
MODEL_VALIDATION_LOCKED_SPACE_ERROR="{0}" көрінісі үшін үлгіні тексеру тапсырмасын орындау мүмкін емес, себебі "{1}" кеңістігі құлыпталды.
##XMSG: Message used in View Analyzer execution task when determination of number of rows fails
NUMBER_ROWS_DETERMINATION_FAILED="{0}" жергілікті кестесі үшін жолдар санын анықтау кезінде қате орын алды.
##XMSG: Message to indicate that SQL plan was created
EXECUTION_PLAN_CREATED="{0}" көрінісі үшін SQL Analyzer жоспарының файлы жасалды және оны жүктеп алуға болады.
#XFLD: Message to indicate starting of process to generate SQL Analyzer plan file
START_PLANVIZ_EXECUTION="{0}" көрінісі үшін SQL Analyzer жоспарының файлын құру процесі басталуда.
#XFLD: Message to indicate starting of View Analyzer process
VIEW_ANALYZER_EXECUTION_STARTED=Көрініс талдағыш тапсырмасын орындау басталуда.
##XMSG: Message to indicate that SQL plan file cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_PLANVIZ_NOT_FULFILLED="{0}" көрінісі үшін SQL Analyzer жоспарының файлын жасау мүмкін емес, себебі деректерді сақтау алғышарттары орындалмады.
##XMSG: Error message during  generation of SQL Analyzer plan file
PLANVIZ_ERROR="{0}" көрінісі үшін SQL Analyzer жоспарының файлын жасау кезінде қате орын алды.
#XMSG: Message for Locked space error
VIEW_ANALYZER_LOCKED_SPACE_ERROR="{0}" көрінісі үшін көрініс талдағыш тапсырмасын орындау мүмкін емес, себебі "{1}" кеңістігі құлыпталды.
#XMSG: Message for persistency simulation without partitioning
PERSISTENCY_SIMULATION_WITHOUT_PARTITIONS=Деректерді сақтауды үлгілеу кезінде "{0}" көрінісінің бөліктері қарастырылмайды.
#XMSG: Message for generation of SQL Analyzer plan file without considering partitioning
PLANVIZ_WITHOUT_PARTITIONS=SQL Analyzer жоспарының файлын жасау кезінде "{0}" көрінісінің бөліктері қарастырылмайды.
#XMSG: Confirmation message for mass removal of persisted data
MassRemovePersistency_Confirm_Msg=Сақталған деректерді жою және деректерге қол жеткізу мүмкіндігін виртуалды деректерге қол жеткізу мүмкіндігіне ауыстыру қажет пе?
#XMSG: Confirmation message for partial mass removal of selected persisted data
PartialMassRemovePersistency_Confirm_Msg={0}/{1} таңдалған көрінісінде сақталған деректер бар. \n Сақталған деректерді жою және деректерге қол жеткізу мүмкіндігін виртуалды деректерге қол жеткізу мүмкіндігіне ауыстыру қажет пе?
#XMSG: success message for stopping mass persistence
stopMassPersistenceSuccess=Таңдалған көріністер үшін сақталған деректерді жойып жатырмыз.
#XMSG: error message during mass stop persistence of a view (backend error)
STOP_MASS_PERSISTENCE_ERROR=Таңдалған көріністер үшін сақтауды тоқтату кезінде қате орын алды.
#XMSG: memory analysis is only performed in space {0}
NO_MEMORY_ANALYSIS=Жадты талдау тек "{0}" кеңістігіндегі мәндер үшін орындалады: "{1}" "{2}" өткізіп жіберілді.
##XMSG: Message to indicate that Explain Plan cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_EXPLAINPLAN_NOT_FULFILLED="{0}" көрінісі үшін Explain Plan файлын жасау мүмкін емес, себебі сақтау алғышарттары орындалмады.
#XMSG: Message for Explain Plan generation without partitioning
EXPLAINPLAN_WITHOUT_PARTITIONS=Explain Plan файлын жасау кезінде "{0}" көрінісінің бөліктері қарастырылмайды.
#XFLD: Message to indicate starting of process to generate Explain Plan file
START_EXPLAINPLAN_EXECUTION="{0}" көрінісі үшін Explain Plan файлын құру процесі басталуда.
##XMSG: Message to indicate that Explain Plan was created
EXPLAINPLAN_CREATED="{0}" көрінісі үшін Explain Plan файлы құрылды. Оны "Мәліметтерді қарау" опциясын басу арқылы көрсетуіңізге немесе оған тиісті рұқсатыңыз болса, оны жүктеп алуыңызға болады.
##XMSG: Error message during  generation of Explain Plan file
EXPLAINPLAN_ERROR="{0}" көрінісі үшін Explain Plan файлын жасау кезінде қате орын алды.
##XMSG: Explain Plan file cannot be generated for the view {0}, because too many views are stacked on each other
EXPLAINPLAN_MODEL_TOO_COMPLEX="{0}" көрінісі үшін Explain Plan файлын құру мүмкін емес. Тым көп көріністер бір-біріне жинақталған. Күрделі үлгілер жадтың жетіспеушілігін тудыруы және өнімділікті төмендетуі мүмкін. Көріністі сақтауға кеңес береміз.

##XMSG: Message to indicate that the space is locked
PERFORMANCE_ANALYSIS_LOCKED_SPACE_ERROR="{0}" көрінісі үшін өнімділік талдауын орындау мүмкін емес, себебі "{1}" кеңістігі құлыпталды.
##XMSG: The performance analysis task was cancelled
PERFORMANCE_ANALYSIS_CANCELED="{0}" көрінісі үшін өнімділік талдауы болдырылмады.
##XMSG: The performance analysis failed
PERFORMANCE_ANALYSIS_FAILED=Өнімділік талдауы сәтсіз аяқталды.
##XMSG: The performance analysis is finished
PERFORMANCE_ANALYSIS_FINISHED="{0}" көрінісі үшін өнімділік талдауы аяқталды. "Мәліметтерді көру" түймесін басу арқылы нәтижені көріңіз.
##XMSG: The view has input parameter with no default value
INPUT_PARAMETER_WITHOUT_DEFAULT_VALUE=Осы көріністі талдау мүмкін емес, себебі оның әдепкі мәні жоқ параметрі бар.
##XMSG: The view is not fully deployed
VIEW_NOT_FULLY_DEPLOYED=Осы көріністі талдау мүмкін емес, себебі ол толық қолданысқа енгізілмеген.
##XMSG: The view uses limited remote adapters
LIMITED_REMOTE_ADAPTER=Бұл көріністе кем дегенде бір мүмкіндіктері шектеулі (мысалы, сүзгіні басу түймесінің болмауы немесе "Санау" үшін қолдау көрсетпеуі) қашықтағы адаптер пайдаланылады. Тұрақты немесе тираждалатын нысандар орындау уақытының өнімділігін жақсарта алады.
##XMSG: The view uses remote adapter that does not support 'Limit'
REMOTE_ADAPTER_NOT_SUPPORTING_LIMIT=Бұл көрініс 'Шек' функциясына қолдау көрсетпейтін кемінде бір қашықтағы адаптерді пайдаланады. 1000-нан астам жазба таңдалған болуы мүмкін.
##XMSG: Performance analysis is executed by using default parameters of view'
INPUT_PARAMETER_WITH_DEFAULT_VALUE=Өнімділік талдауы көрініс параметрлерінің әдепкі мәндерін пайдалану арқылы орындалады.
##XMSG: Message to indicate that an error occurred
PERFORMANCE_ANALYSIS_ERROR="{0}" көрінісі үшін өнімділік талдауы кезінде қате орын алды.
##XMSG: Message start of cancellation for Performance Analysis task
CANCEL_PERFORMANCE_ANALYSIS_TRIGGERED="{1}" көрінісі үшін өнімділік талдауының {0} тапсырмасын тоқтату процесі жіберілді. Тапсырма тоқтатылғанға дейін кідіріс болуы мүмкін.
##XMSG: Message start to indicate that task is not active
CANCEL_PERFORMANCE_ANALYSIS_NO_ACTIVE_TASK="{1}" көрінісі үшін өнімділік талдауының {0} тапсырмасы белсенді емес.
#XMSG: Cancel Performance Analysis task failure
CANCEL_PERFORMANCE_ANALYSIS_FAILED=Өнімділік талдауының тапсырмасын болдырмау сәтсіз аяқталды.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Кестені маған тағайындау
#XBUT: Pause schedule menu label
pauseScheduleLabel=Кестені кідірту
#XBUT: Resume schedule menu label
resumeScheduleLabel=Кестені жалғастыру
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Кестелерді жою кезінде қате орын алды.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Кестелерді тағайындау кезінде қате орын алды.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Кестелерді кідірту кезінде қате орын алды.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Кестелерді жалғастыру кезінде қате орын алды.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted={0} кесте жойылуда
#XMSG: Message for starting mass assign of schedules
massAssignStarted={0} кесте иесі жойылуда
#XMSG: Message for starting mass pausing of schedules
massPauseStarted={0} кесте кідіртілуде
#XMSG: Message for starting mass resuming of schedules
massResumeStarted={0} кесте жалғастырылуда
#XBUT: Select Columns Button
selectColumnsBtn=Бағандарды таңдау
#XFLD: Refresh tooltip
TEXT_REFRESH=Жаңарту
#XFLD: Select Columns tooltip
text_selectColumns=Бағандарды таңдау


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Runtime Metrics ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Label for Runtime Metrics
RuntimeMetricsTitle=Келесі үшін орындау уақытының көрсеткіштері
#XFLD : Label for Run Button
runButton=Орындау
#XFLD : Label for Cancel Button
cancelButton=Болдырмау
#XFLD : Label for Close Button
closeButton=Жабу
#XFLD : Label for Open View Analyzer Button
openViewAnalyzerButton=Көрініс талдағышты ашу
#XFLD : Label for Generate Explain Plan Button
generateExplainPlanButton=Explain Plan құру
#XFLD : Label for Previous Run Column
previousRun=Алдыңғы сеанс
#XFLD : Label for Latest Run Column
latestRun=Соңғы сеанс
#XFLD : Label for time Column
time=Уақыт
#XFLD : Label for Duration Column
duration=Ұзақтық
#XFLD : Label for Peak Memory Column
peakMemory=Максималды жад
#XFLD : Label for Number of Rows
numberOfRows=Жолдар саны
#XFLD : Label for Overall Number of Sources
overallNumberOfSources=Бастапқы көздердің жалпы саны
#XFLD : Label for Data Access Column
dataAccess=Дерекке қол жеткізу
#XFLD : Label for Local Tables
localTables=Жергілікті кестелер
#XFLD : Label for federatedRemoteTables
federatedRemoteTables=Федерацияланған қашықтағы кестелер (шектеулі адаптер мүмкіндіктері бар)
#XTXT Text for initial state of the runtime metrics
initialState=Көрсеткіштерді алу үшін алдымен өнімділікті талдау сеансын жүргізу қажет. Бұл біраз уақыт алуы мүмкін, бірақ қажет болса, процесті болдырмауға болады.
#XMSG: Cancel confirmation message
CancelRunConfirmationMessage=Ағымдағы өнімділікті талдау сеансын болдырмауға сенімдісіз бе?
#XTIT: Cancel dialog title
CancelRunTitle=Сеансты болдырмау
#XFLD: Label for Number of Rows
NUMBER_ROWS=Жолдар саны
#XFLD: Label for Overall Number of Sources
OVERALL_NUMBER_SOURCES=Бастапқы көздердің жалпы саны
#XFLD: Label for Data Access
DATA_ACCESS=Дерекке қол жеткізу
#XFLD: Label for Remote tables
NUMBER_REMOTE_TABLES_LIMITED_ADAPTERS=Федерацияланған қашықтағы кестелер (шектеулі адаптер мүмкіндіктері бар)
#XFLD: Label for select statement
SELECT_STATEMENT='КӨРІНІС ШЕГІНЕН 1000 * ТАҢДАУ'
#XFLD: Label for duration
SELECT_RUNTIME=Ұзақтық
#XFLD: Label for peak memory
SELECT_PEAK_MEMORY=Максималды жад
#XFLD: Label for count statement
SELECT_COUNT_STATEMENT='КӨРІНІСТЕН САНЫН (*) ТАҢДАУ'
#XFLD: Label for count duration
SELECT_COUNT_RUNTIME=Ұзақтық
#XFLD: Label for count peak memory
SELECT_COUNT_PEAK_MEMORY=Максималды жад
#XFLD: Label for count of number of local tables
NUMBER_LOCAL_TABLES_FILE=Жергілікті кестелер (Файл)
#XTXT: Text for running state of the runtime metrics
Running=Орындалуда...
#XFLD: Label for time
Time=Уақыт
#XFLD: Label for virtual access
PA_VIRTUAL=Виртуалды
#XFLD: Label for persisted access
PA_PERSISTED=Сақталған
PA_PARTIALLY_PERSISTED=Ішінара сақталған
#XTXT: Text for cancel
CancelRunSuccessMessage=Өнімділікті талдау сеансы болдырылмауда.
#XTXT: Text for cancel error
CancelRunErrorMessage=Өнімділікті талдау сеансын болдырмау кезінде қате орын алды.
#XTXT: Text for explain plan generation
ExplainPlanStarted="{0}" көрінісі үшін Explain Plan файлы құрылуда.
#XTXT: Text for performance analysis
PerformanceAnalysisStarted="{0}" көрінісі үшін өнімділік талдауы басталуда.
#XTXT: Text for performance analysis error
ErrorFetchingPerformanceAnalysis=Өнімділікті талдау дерегін іріктеу кезінде қате орын алды.
#XTXT: Text for performance analysis error
conflictingTask=Өнімділікті талдау тапсырмасы бұрыннан орындалуда
#XFLD: Label for Errors
Errors=Қате/қателер
#XFLD: Label for Warnings
Warnings=Ескерту/ескертулер
#XFLD: Tooltip for missing privileges
openViewAnalyzerButtonTooltipNoPrivilege=Көрініс талдағышты ашу үшін DWC_DATAINTEGRATION(жаңарту) артықшылығы қажет.
#XFLD: Tooltip for missing privileges
generateExplainPlanButtonTooltipNoPrivilege=Explain Plan құру үшін WC_RUNTIME(оқу) артықшылығы қажет.



#XFLD: Label for frequency column
everyLabel=Әр
#XFLD: Plural Recurrence text for Hour
hoursLabel=Сағат
#XFLD: Plural Recurrence text for Day
daysLabel=Күн
#XFLD: Plural Recurrence text for Month
monthsLabel=Ай
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Минут
