
#XBCB: Breadcrumb
viewMonitorBreadcrumb={spaceId}

#XFLD: Label for persisted view column
SOURCE=Izvor
#XFLD: Label for persisted view column
NAME=Naziv
#XFLD: Label for persisted view column
NAME_LABEL=Poslovni naziv
#XFLD: Label for persisted view column
NAME_LABELNew=Obje<PERSON> (poslovni naziv)
#XFLD: Label for persisted view column
TECHINCAL_NAME=Tehnički naziv
#XFLD: Label for persisted view column
TECHINCAL_NAMENew=Obje<PERSON> (tehnički naziv)
#XFLD: Label for persisted view column
DATA_PERSISTENCY=Pristup podacima
#XFLD: Label for persisted view column
STATUS=Status
#XFLD: Label for persisted view column
LAST_UPDATED=Zadnje ažurirano
#XFLD: Label for persisted view column
IN_MEMORY_SIZE=Memorija iskorištena za pohranu (MiB)
#XFLD: Label for persisted view column
DISK_SIZE=Disk iskorišten za pohranu (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_NEWW=Veličina in-Memory (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_HEADER=Veličina in-Memory 
#XFLD: Label for persisted view column
DISK_SIZE_NEWW=Veličina na disku (MiB)
#XFLD: Label for persisted view column
DISK_SIZE_HEADER=Veličina na disku
#XFLD: Label for schedule owner column
txtScheduleOwner=Vlasnik rasporeda
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Pokazuje tko je stvorio raspored
#XFLD: Label for persisted view column
PERSISTED=Trajno
#XFLD: Label for persisted view column
TYPE=Tip
#XFLD: Label for View Selection Dialog column
changedOn=Promijenjeno
#XFLD: Label for View Selection Dialog column
createdBy=Stvorio
#XFLD: Label for log details column
txtViewPersistencyLogs=Prikaži zapisnike
#XFLD: Label for log details column
txtViewPersistencyLogsNew=Pojedinosti
#XFLD: text for values shown for Ascending sort order
SortInAsc=Sortiraj uzlazno
#XFLD: text for values shown for Descending sort order
SortInDesc=Sortiraj silazno
#XFLD: Label for persisted view monitor heading
ViewMonitor_Heading=Nadzor trajnosti prikaza
#XFLD: Label for persisted view monitor sub-heading
ViewMonitor_SubHeading=Nadzor i održavanje trajnosti prikaza


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ DynamicPageHeader ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: text for values shown in column Persistence Status
txtLoading=Učitavanje
#XFLD: text for values shown in column Persistence Status
txtRunning=Izvodi se
#XFLD: text for values shown in column Persistence Status
txtAvailable=Dostupno
#XFLD: text for values shown in column Persistence Status
txtError=Pogreška
#XMSG: Error message for unknown Persistence type that gets logged in Kibana
unknownReplicationTypeError=Tip replikacije "{0}" nije podržan.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Content ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Postavke upotrijebljene za zadnje izvođenje trajnosti podataka:
#XMSG: Message for input parameter name
inputParameterLabel=Ulazni parametar
#XMSG: Message for input parameter value
inputParameterValueLabel=Vrijednost
#XMSG: Message for persisted data
inputParameterPersistedLabel=Učinjeno trajnim
#XTIT: Count for View in ViewMonitor Toolbar
txtViewCount=Prikazi ({0})
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Trajnost prikaza
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Trajnost podataka
#XBUT: Button to clear the selected view persistance
ClearViewPersistance=Očisti
#XBUT: Button to stop the selected view persistance
stopPersistance=Zaustavi trajnost
#XFLD: Placeholder for Search field
txtSearch=Pretraži
#XBUT: Tooltip for refresh button
txtRefresh=Osvježi
#XBUT: Tooltip for add view button
txtDeleteView=Izbriši trajnosti
#XBUT: Tooltip for load new peristence
loadNewPersistence=Ponovo pokreni trajnost
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Učitaj novu snimku stanja
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Pokreni trajnost podataka
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Ukloni trajne podatke
#XMSG: success message for starting persistence
startPersistenceSuccess=Prikaz "{0}" činimo trajnim.
#XMSG: success message for stopping persistence
stopPersistenceSuccess=Uklanjamo trajne podatke za prikaz "{0}".
#XMSG: success message for stopping persistence
deletePersistedViewSuccess=Uklanjamo prikaz "{0}" iz popisa za nadzor.
#XMSG: error message during start persistence of a view (backend error)
ERROR_STARTING_PERSISTENCE=Došlo je do pogreške pri pokretanju trajnosti za prikaz "{0}".
#XMSG: error message during start persistence of a view (backend error)
PARAMERROR_STARTING_PERSISTENCE=Trajnost se ne podržava u prikazu "{0}" s ulaznim parametrima.
#XMSG: error message during start persistence of a view (backend error)
NUMBER_OF_PARAMETER_STARTING_PERSISTENCE=Trajnost se ne podržava u prikazu "{0}" jer ima više od jednog ulaznog parametra.
#XMSG: error message during start persistence of a view (backend error)
PARAMETER_DEFAULT_VALUE_STARTING_PERSISTENCE=Trajnost se ne podržava u prikazu "{0}" jer ulazni parametar nema zadanu vrijednost.
#XMSG: error message during start persistence of a view (backend error)
REDEPLOYMENT_DAC_REQUIRED=Trebate ponovo uvesti kontrolu pristupa podacima (DAC) "{0}" radi podrške trajnosti podataka.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_STARTING_PERSISTENCE=Prikaz "{0}" ne može biti trajan jer upotrebljava prikaz "{1}", koji ima kontrolu pristupa podacima (DAC).
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_EXTERNAL_STARTING_PERSISTENCE=Prikaz ''{0}'' ne može biti trajan jer upotrebljava prikaz s kontrolom prikaza podacima (DAC) koji pripada drugom prostoru.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_STRUCTURE_STARTING_PERSISTENCE=Prikaz ''{0}'' ne može biti trajan jer struktura jedne ili više njegovih kontrola pristupa podacima (DAC) ne podržava trajnost prikaza.
#XMSG: error message during stop persistence of a view (backend error)
STOP_PERSISTENCE_ERROR=Došlo je do pogreške pri zaustavljanju trajnosti za prikaz "{0}".
#XMSG: error message during stop persistence of a view (backend error)
DELETE_PERSISTED_VIEW_ERROR=Došlo je do pogreške pri brisanju trajnog prikaza "{0}".
#XMSG: Warning message for removing the persisted data
RemovePersistency_Confirm_Msg=Želite li izbrisati trajne podatke i prebaciti na virtualni pristup za prikaz "{0}"?
#XMSG: Warning message for delete the view persistence
DeletePersistency_Confirm_Msg=Želite li ukloniti prikaz iz popisa za nadzor i izbrisati trajne podatke prikaza "{0}"?
#XMSG: error message for reading data from backend
txtReadBackendError=Čini se da je došlo do pogreške pri čitanju iz pozadine.
#XFLD: Label for No Data Error
NoDataError=Pogreška
#XMSG: message for conflicting task
Task_Already_Running=Zadatak u sukobu već se izvodi za prikaz "{0}".

#XFLD: Error while accessing pertitioning for a view
missingPrivilegeInViewPersistency=Nedovoljno dopuštenje za izvođenje particioniranja za prikaz ''{0}''

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Show All Views~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Text for show all views button
allViewsText=Svi prikazi ({0})
#XBUT: Text for show scheduled views button
scheduledText=Raspoređeno ({0})
#XBUT: Text for show persisted views button
persistedText=Trajno ({0})
#XBUT: Text for start analyzer button
startAnalyzer=Pokreni Analizator prikaza
#XFLD: Message if repository is unavailable
repositoryErrorMsg=Spremište nije dostupno i određene su značajke onemogućene.

#XFLD: Data Access - Virtual
Virtual=Virtualno
#XFLD: Data Access - Persisted
Persisted=Trajno

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Non-Persisted View Selection Dialog~~~~~~~~~~~~~~~~~~~~~~~

#XTIT: Title for Non-Persisted View selection dialog
select_source=Odabir prikaza za trajnost

#XTIT: Placeholder for Non-Persisted View selection dialog search
searchView=Pretraži prikaze
#XTIT: No data in the list of non-persisted view
No_Data=Nema podataka
#XBUT: Button to select non-persisted view
ok=OK
#XBUT: Button to close the non-persisted views selection dialog
cancel=Odustani

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~View persistency log message key~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message for view persistency logs
START_STOP_PERSISTENCY=Pokretanje izvršenja zadatka trajnosti prikaza za "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY=Pokretanje trajnosti za prikaz "{1}".
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY)
START_VIEW_PERSISTENCY_PROCESS=Pokretanje procesa trajnosti podataka za prikaz "{0}".
#XFLD: Message for view persistency manual partition load
START_VIEW_PERSISTENCY_PROCESS_MANUAL_PARTITIONS=Pokretanje procesa trajnosti podataka za prikaz ''{0}'' s odabranim ID-ovima particije: ''{1}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY=Uklanjanje trajnosti podataka za prikaz "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY)
REMOVE_VIEW_PERSISTENCY=Pokretanje procesa za uklanjanje trajnosti podataka za prikaz "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_SUCCESS=Trajnost podataka za prikaz "{1}" uspješno pokrenuta.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_SUCCESS)
START_VIEW_PERSISTENCY_PROCESS_SUCCESS=Trajnost podataka za prikaz "{0}" uspješno pokrenuta.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY_SUCCESS=Trajni podaci uklonjeni, a virtualni pristup podacima vraćen za prikaz "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_SUCCESS)
REMOVE_VIEW_PERSISTENCY_SUCCESS=Dovršen proces za uklanjanje trajnosti podataka za prikaz "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_FAILED=Nije moguće pokrenuti trajnost podataka za prikaz "{1}".
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_FAILED)
START_VIEW_PERSISTENCY_PROCESS_FAILED=Nije moguće pokrenuti trajnost podataka za prikaz "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.05)
STOP_VIEW_PERSISTENCY_FAILED=Nije moguće ukloniti trajnost podataka za prikaz "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_FAILED)
REMOVE_VIEW_PERSISTENCY_FAILED=Nije moguće ukloniti trajnost podataka za prikaz "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_SUCCESS="{3}" zapisa trajno za prikaz "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace START_PERSISTENCY_RECORD_COUNT_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS={0} zapisa umetnuto u tablicu trajnosti za prikaz ''{1}''.
#XFLD: Message used by SEAL repository for view persistency logs (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS_V2={0} zapisa umetnuto u tablicu trajnosti za prikaz ''{1}''. Iskorištena memorija: {2} GiB.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_SUCCESS=Trajni zapisi uklonjeni za prikaz "{1}": "{3}".
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace STOP_PERSISTENCY_RECORD_COUNT_SUCCESS)
REMOVE_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=Trajni podaci uklonjeni, ovoliko trajnih zapisa izbrisano: ''{0}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_FAILED=Dohvaćanje recordCount nije uspjelo za prikaz "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_FAILED=Dohvaćanje recordCount nije uspjelo za prikaz "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Raspored uspješno izbrisan za "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS)
REMOVE_VIEW_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Raspored uspješno izbrisan za prikaz ''{0}''.
#XFLD: Message for view persistency logs
STOP_PERSISTENCY_DELETE_SCHEDULE_FAILED=Brisanje rasporeda nije uspjelo za "{1}".
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_REDEPLOY=Ne možemo zadržati prikaz "{0} jer je promijenjen i uveden prije početka zadržavanja. Pokušajte ponovo zadržati prikaz ili pričekajte do sljedećeg planiranog izvođenja..
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_DELETE=Ne možemo zadržati prikaz "{0}" jer je izbrisan prije početka zadržavanja.
#XFLD: Message for successful data persistency of a single partition (for messages created before version 2022.03)
START_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} zapisa zadržano u particiji za vrijednosti "{1}" <= {2} < "{3}".
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (new message to replace START_PERSISTENCY_SINGLE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} zapisa umetnuto u particiju za vrijednosti "{1}" <= ''{2}''’ < "{3}" .
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS_V2={0} zapisa umetnuto u particiju za vrijednosti ''{1}'' <= ''{2}'' < ''{3}''. Iskorištena memorija: {4} GiB.
#XFLD: Message for successful replication of a "others" partition (for messages created before version 2022.03)
START_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} zapisa zadržano u particiji "ostalo".
#XFLD: Message used by SEAL repository for successful replication of a "others" partition (new message to replace START_PERSISTENCY_OTHERS_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} zapisa umetnuto u particiju "ostalo".
#XFLD: Message for view persistency record count of partitions (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={3} zapisa zadržano za prikaz ''{1}'' u ovoliko particija: {4}.
#XFLD: Message used by SEAL repository for view persistency record count of partitions (new message to replace START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={0} zapisa umetnuto u tablicu trajnosti za prikaz ''{1}'' u ovoliko particija: {2}.
#XFLD: Message used by SEAL repository for view persistency for partition wise refresh
START_VIEW_PERSISTENCY_RECORD_COUNT_REFRESHED_PARTITIONS_SUCCESS={0} zapisa umetnuto u tablicu trajnosti za prikaz ''{1}''. Ažurirane particije: {2}; zaključane particije: {3}; ukupno particija: {4}
#XFLD: Message used by SEAL repository for view persistency for manual partition load
START_VIEW_PERSISTENCY_RECORD_COUNT_MANUAL_PARTITIONS_SUCCESS={0} zapisa umetnuto u tablicu trajnosti za prikaz ''{1}'' u {2} odabranih particija.
#XFLD: Message used by SEAL repository for view persistency for locked partition data load
START_VIEW_PERSISTENCY_RECORD_COUNT_LOCKED_PARTITIONS_SUCCESS={0} zapisa umetnuto u tablicu trajnosti za prikaz ''{1}''. Ažurirane particije: {2}; zaključane nepromijenjene particije: {3}; ukupno particija: {4}.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
START_PERSISTENCY_UNEXPECTED_ERROR=Došlo je do neočekivane pogreške tijekom zadržavanja podataka za prikaz ''{0}''.
#XMSG: Message for unexpected error (new message to replace START_PERSISTENCY_UNEXPECTED_ERROR)
START_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Došlo je do neočekivane pogreške tijekom zadržavanja podataka za prikaz ''{0}''.
#XMSG: Message for Locked space error
START_VIEW_PERSISTENCY_LOCKED_SPACE_ERROR=Prikaz ''{0}''’ ne može biti trajan jer je prostor ''{1}'' zaključan.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
STOP_PERSISTENCY_UNEXPECTED_ERROR=Došlo je do neočekivane pogreške tijekom uklanjanja trajnih podataka za prikaz ''{0}''.
#XMSG: Message for unexpected error (new message to replace STOP_PERSISTENCY_UNEXPECTED_ERROR)
REMOVE_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Došlo je do neočekivane pogreške tijekom uklanjanja trajnosti za prikaz ''{0}''.
#XMSG: Message for ivalidated view
VIEW_PERSISTENCY_ABORTED_DEFINITION_INVALID=Definicija prikaza ''{0}'' postala je nevaljana, najvjerojatnije zbog promjene objekta koji je prikaz izravno ili neizravno potrošio. Pokušajte ponovo uvesti prikaz da biste riješili problem ili identificirali osnovni uzrok.
#XMSG: Message for persisted data removal (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT=Trajni podaci uklanjaju se tijekom uvođenja prikaza ''{0}''.
#XMSG: Message for persisted data removal of a consuming view
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW=Trajni podaci uklanjaju se tijekom uvođenja potrošenog prikaza ''{0}''.
#XMSG: Message for persisted data removal of a consuming view due to a dac change
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW_DAC_CHANGED=Trajni podaci uklanjaju se tijekom uvođenja potrošenog prikaza ''{0}'' jer se promijenila kontrola pristupa podacima.
#XMSG: Message for persisted data removal (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DEPLOYMENT=Trajni podaci uklanjaju se tijekom uvođenja prikaza ''{0}''.
#XMSG: Message for deleted view (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION=Trajnost se uklanja brisanjem prikaza ''{0}''.
#XMSG: Message for deleted view (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DELETION=Trajnost se uklanja brisanjem prikaza ''{0}''.
#XMSG Message for removal of persisted data, because prerequisites not fulfilled
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_PREREQUISITES_NOT_FULFILLED=Trajni podaci uklonjeni su jer preduvjeti za trajnost podataka više nisu ispunjeni.
#XMSG: Message for inconsistent persistency
VIEW_PERSISTENCY_INCONSISTENT_PERSISTENCY=Trajnost prikaza "{0}" postala je nedosljedna. Da biste riješili problem, uklonite trajne podatke.
#XMSG: Message for prerequisites
START_PERSISTENCY_CHECK_PREREQUISITES=Provjera preduvjeta za trajnost prikaza ''{0}''.
#XMSG: Message for DAC deployed in deprecated way using table function and join (for messages created before 2025.05)
START_PERSISTENCY_DEPRECIATED_DAC=Prikaz "{0}" uveden je s pomoću kontrole pristupa podacima (DAC) koja se ukida. Ponovo uvedite prikaz kako biste poboljšali izvedbu.
#XMSG: Message for DAC deployed in deprecated way using table function and join
START_PERSISTENCY_DEPRECATED_DAC=Prikaz "{0}" uveden je s pomoću kontrole pristupa podacima (DAC) koja se ukida. Ponovo uvedite prikaz kako biste poboljšali izvedbu.
#XMSG: Message for roll back (for messages created before version 2022.03)
ROLLBACK_VIEW_PERSISTENCY=Došlo je do pogreške. Prethodno stanje trajnosti vraćeno je za prikaz ''{0}''.
#XMSG: Message used by SEAL repository for rollback of running procedure to persist view (new message to replace ROLLBACK_VIEW_PERSISTENCY)
VIEW_PERSISTENCY_ROLLBACK=Došlo je do pogreške. Proces za trajnost prikaza ''{0}''’ zaustavljen je, a promjene su poništene.
#XMSG: Message used by SEAL repository for rollback of running procedure to remove persisted data of view
REMOVE_VIEW_PERSISTENCY_ROLLBACK=Došlo je do pogreške. Proces za uklanjanje trajnih podataka prikaza ''{0}''’ zaustavljen je, a promjene su vraćene.
#XMSG: Message used by SEAL repository for persistency preparation
START_PERSISTENCY_PREPARATION=Priprema za trajnost podataka.
#XMSG: Message used by SEAL repository for data insertion
START_INSERTING_PERSISTENCY_DATA=Umetanje podataka u tablicu trajnosti.
#XMSG: Message for null values - partitioning (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS={0} zapisa nulte vrijednosti umetnuto u particiju "ostalo".
#XMSG: Message used by SEAL repository for others partition values - partitioning (new message to replace START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS={0} zapisa umetnuto u particiju ''ostalo''’ za vrijednosti "{2}" <= ''{1}''’ ILI ''{2}'' >= ''{3}''.
#XMSG: Message used by SEAL repository for others partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS_V2={0} zapisa umetnuto u particiju ''ostalo''’ za vrijednosti ''{2}'' < ''{1}'' ILI ''{2}'' >= ''{3}''. Iskorištena memorija: {4} GiB.
#XMSG: Message used by SEAL repository for null partition values - partitioning
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS={0} zapisa umetnuto u particiju ''ostalo''’ za vrijednosti "{1}" JE NULL.
#XMSG: Message used by SEAL repository for null partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS_V2={0} zapisa umetnuto u particiju ''ostalo''’ za vrijednosti "{1}" JE NULL. Iskorištena memorija: {2} GiB.
##XFLD:Message used by SEAL repository for remote statistics
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS=Učitavanje uključenih podataka: {0} udaljene naredbe. Ukupno dohvaćenih zapisa: {1}. Ukupno vrijeme trajanja: sekundi {2}.
##XFLD:Message used by SEAL repository for remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS_WITH_PARTITIONS=Učitavanje uključenih podataka s pomoću ovoliko particija:{0} s ovoliko udaljenih naredbi: {1}. Ukupno dohvaćenih zapisa: {2}. Ukupno vrijeme trajanja: sekundi {3}.
##XFLD:Message used by SEAL repository with UI ID for instructions of remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_INSTRUCTION=Udaljeni izrazi obrađeni tijekom izvođenja mogu se prikazati otvaranjem nadzora udaljenih upita u pojedinostima poruka specifičnih za particiju.
##XFLD:Message for start reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS=Pokretanje procesa ponovne upotrebe postojećih trajnih podataka za prikaz {0} nakon uvođenja.
##XFLD:Message used by SEAL repository for reattach view persitency
START_VIEW_PERSISTENCY_REATTACHMENT=Pokrenite ponovno upotrebljavati postojeće trajne podatke.
##XFLD:Message used by SEAL repository for reattach view persitency
START_FINILIZING_VIEW_PERSISTENCY_REATTACHMENT=Ponovna upotreba postojećih trajnih podataka.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_COMPLETE=Proces ponovne upotrebe postojećih trajnih podataka dovršen je za prikaz {0}.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_FAILED=Ponovna upotreba postojećih trajnih podataka za prikaz {0} nakon uvođenja.
#XMSG: Message used by SEAL repository for finalizing persistency
START_FINALIZING_PERSISTENCY=Dovršenje trajnosti.
#XMSG: Message used by SEAL repository for restoring virtual access for view
REMOVE_VIEW_PERSISTENCY_VIRTUAL_ACCESS_RESTORED=Virtualni pristup podacima vraćen za prikaz ''{0}''.
#XMSG: Message used by SEAL repository for view that already has virtual access
REMOVE_VIEW_PERSISTENCY_ALREADY_VIRTUAL_ACCESS=Prikaz "{0}" već ima virtualni pristup podacima. Trajni podaci nisu uklonjeni.
#XMSG: Message for removing view persistency
VIEW_PERSISTENCY_REMOVED_FROM_MONITOR=Prikaz ''{0}'' uklonjen je iz nadzora trajnosti prikaza.
#XMSG: Message for view doesnt exist.
VIEW_PERSISTENCY_VIEW_NOT_EXISTING=Prikaz "{0}" ne postoji u bazi podataka ili nije ispravno uveden, pa ne može biti trajan. Kako biste riješili taj problem, pokušajte ponovo uvesti taj prikaz ili identificirati korijenski uzrok.
#XMSG: Message for persistency not enabled.
VIEW_PERSISTENCY_NOT_ENABLED=Trajnost prikaza nije omogućena. Ponovo uvedite tablicu/prikaz u prostor ''{0}'' kako biste omogućili funkcionalnost.
#XMSG: Message for unexpected error due to node crash
VIEW_PERSISTENCY_UNEXPECTED_ERROR_GENERAL=Zadnje izvođenje trajnosti prikaza prekinuto zbog tehničkih pogrešaka.
#XFLD: Message for view persistency memory consumption
VIEW_PERSISTENCY_MEMORY_CONSUMPTION={0} GiB vršne memorije iskorišteno u vremenu izvođenja trajnosti prikaza.
#XFLD: Message for view persistency timeout
VIEW_PERSISTENCY_TIMEOUT=Trajnost prikaza {0} dosegla prekoračenje vremena od {1} sati.
#XFLD: Message for not started asynchronous view persistency
VIEW_PERSISTENCY_ASYNC_NOT_STARTED=Veliko opterećenje sustava spriječilo je pokretanje asinkronog izvršenja dosljednosti prikaza. Provjerite izvodi li se paralelno previše zadataka.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency (for messages created before version 2022.05)
START_VIEW_PERSISTENCY_EXECUTION_METHOD_DROP_AND_RENAME=Postojeća trajna tablica izbrisana je i zamijenjena novom trajnom tablicom.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_RECREATE_AND_DROP=Postojeća trajna tablica izbrisana je i zamijenjena novom trajnom tablicom.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_TRUNCATE_AND_COPY=Postojeća trajna tablica ažurirana je novim podacima.
##XFLD: Message for missing authorizations for view persistency
VIEW_PERSISTENCY_MISSING_AUTH=Nedostaju ovlaštenja za trajnost prikaza.
##XFLD: Message start of persistency cancellation
VIEW_PERSISTENCY_TRIGGER_CANCEL=Pokretanje otkazivanja procesa za trajnost prikaza {0}.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_UNAVAILABLE_TASK_RUN=Otkazivanje procesa za trajnost prikaza nije uspjelo jer se za prikaz {0} ne izvodi zadatak trajnosti.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_NONDETERMINISTIC_TASK_RUN=Otkazivanje procesa za trajnost prikaza nije uspjelo jer se za prikaz {0} ne izvodi zadatak trajnosti.
##XFLD: Message for canceling a non running persitency task
VIEW_PERSISTENCY_CANCEL_NON_RUNNING_SELECTED_TASK=Otkazivanje procesa za trajnost prikaza {0} nije uspjelo jer se ne izvodi odabrani zadatak trajnosti {1}.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_UNREACHABLE_PERSISTENCY_RUN=Otkazivanje procesa za trajnost prikaza nije uspjelo jer za prikaz {0} trajnost podataka još nije pokrenuta.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_COMPLETED_PERSISTENCY_RUN=Otkazivanje procesa za trajnost prikaza {0} nije uspjelo jer je već dovršeno.
##XFLD: Message for failed cancellation
VIEW_PERSISTENCY_ABORTED_CANCEL_EXECUTION=Otkazivanje procesa za trajnost prikaza {0} nije uspjelo.
##XFLD: Message for finalizing persistency cancellation
VIEW_PERSISTENCY_FINALIZE_CANCEL=Proces za zaustavljanje trajnosti prikaza {0} poslan.
##XFLD: Message used in persist task when cancellation is caught
VIEW_PERSISTENCY_CANCELED=Proces za trajnost prikaza {0} zaustavljen.
##XFLD: Message used in persist task when cancelation is caught and performed through a cancelation task
VIEW_PERSISTENCY_CANCELED_VIA_CANCEL_TASK=Proces za trajnost prikaza {0} zaustavljen preko zadatka otkazivanja {1}.
##XFLD: Message used in cancel task when the task is triggered by deployment
VIEW_PERSISTENCY_CANCEL_TRIGGERED_BY_DEPLOYMENT=Otkazivanje procesa trajnosti podataka tijekom uvođenja prikaza {0}.
##XFLD: Message used in case the cancelation is retriggered for a running task
VIEW_PERSISTENCY_CANCEL_ATTEMPT_RETRIGGER_CANCEL=Prethodni zadatak za otkazivanje trajnosti prikaza {0} već je poslan.
##XFLD: Message used to express the delay that may occur before the persistency task is stopped
VIEW_PERSISTENCY_CANCEL_DELAY_STOPPING_PERSISTENCY=Možda dođe do odgode zaustavljanja zadatka trajnosti za prikaz {0}.
##XFLD: Message used to reflect the task log identifier of the current running persistency task
VIEW_PERSISTENCY_CANCEL_CURRENT_RUNNING_TASK=Podaci za prikaz {0} trajni su sa zadatkom {1}.
##XFLD:Message used by SEAL repository for locked partitions and DACs
VIEW_PERSISTENCY_LOCKED_PARTITIONS_DACS=Autorizacije koje osiguravaju kontrole pristupa podacima možda su se promijenile i zaključane particije ih ne uzimaju u obzir. Otključajte particije i učitajte novu snimku stanja kako biste primijenili promjene.
##XFLD:Message used by SEAL repository for column structure change
VIEW_PERSISTENCY_COLUMN_STRUCTURE_CHANGE=Struktura stupca promijenjena je i više se ne podudara s postojećom trajnom tablicom. Uklonite trajne podatke i pokrenite novu trajnost podataka kako biste ažurirali trajnu tablicu.
##XFLD: Message for out of memory
VIEW_PERSISTENCY_HANA_OUT_OF_MEMORY=Zadatak nije uspio zbog pogreške nedostatka memorije u bazi podataka SAP HANA.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_EXCEPTION_TRANSACTION_ROLLBACK=Zadatak nije uspio zbog interne iznimke u bazi podataka SAP HANA.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_SQL_EXECUTION_ISSUE=Zadatak nije uspio zbog internog problema izvršenja SQL-a u bazi podataka SAP HANA.
##XFLD: Message for HANA out of memory result
VIEW_PERSISTENCY_OUT_OF_MEMORY_RESULT_MESSAGE=Razlog događaja nedostatka memorije za HANA: {0}
##XFLD: Message for admission control rejection
VIEW_PERSISTENCY_HANA_ADMISSION_CTRL_REJECTION=Zadatak nije uspio zbog odbijanja kontrole prijema SAP HANA.
##XFLD: Message for HANA back pressure / rate limit of HANA client
VIEW_PERSISTENCY_HANA_CLIENT_RATE_LIMIT_REJECTION=Zadatak nije uspio zbog previše aktivnih veza SAP HANA.
#XMSG: Message for invalid persisted table name
VIEW_PERSISTENCY_PERSISTED_TABLE_INVALID=Došlo je do pogreške i trajna je tablica postala nevaljana. Da biste riješili problem, uklonite trajne podatke i ponovo učinite prikaz trajnim.
#XMSG: Message for usahe of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION=Prikaz se ne može trajno zadržati. Koristi se udaljenom tablicom na osnovi udaljenog izvora s omogućenim propagiranjem korisnika. Provjerite porijeklo prikaza
#XMSG: Message for dynamic usage of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION_DYNAMIC=Prikaz se ne može trajno zadržati. Koristi se udaljenom tablicom na osnovi udaljenog izvora s omogućenim propagiranjem korisnika. Udaljena tablica može se dinamično upotrebljavati preko prikaza skripte SQL. Porijeklo prikaza možda neće prikazati udaljenu tablicu.
#XMSG: Message for insufficient privileges or consumption of view with DAC via dynamic SQL
VIEW_PERSISTENCY_INSUF_PRIV_OR_DAC_DYN_SQL=Vaše ovlasti možda nisu dovoljne. Otvorite Pretpregled podataka da vidite imate li potrebne ovlasti. Ako imate, drugi prikaz koji se koristi preko dinamične SQL skripte može imati primijenjenu kontrolu pristupa podacima (DAC).
#XMSG: deployment of DAC is deprecated and view need to be redeployed to be able to persist data
VIEW_PERSISTENCY_DEPRECATED_DAC=Prikaz "{0}" uveden je s pomoću kontrole pristupa podacima (DAC) koja se ukida. Ponovo uvedite prikaz kako biste mogli zadržati podatke za prikaz.
##XFLD:Message used by SEAL repository for input parameter
START_VIEW_PERSISTENCY_INPUT_PARAMETER=Upotreba zadane vrijednosti "{0}" za ulazni parametar "{1}".
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Elastic compute node keys~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
##XFLD:Message used to reflect disablement of ecn replica
ECN_DISABLE_EXISTING_REPLICA=Onemogućena je replika elastičnog računskoga čvora.
##XFLD:Message used to reflect recreation of ecn replica
ECN_RECREATE_EXISTING_REPLICA=Ponovo je stvorena replika čvora elastičnog izračuna.
##XFLD:Message used to reflect re-enablement of ecn replica
ECN_ENABLE_EXISTING_REPLICA=Ponovo je omogućena replika elastičnog računskoga čvora.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Task Schedule Menu key~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message to schedule task
scheduleText=Raspored
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Stvori raspored
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Uredi raspored
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Izbriši raspored
#XFLD: Refresh frequency field
refreshFrequency=Učestalost osvježavanja
#XFLD: Refresh frequency field
refreshFrequencyNew=Učestalost
#XFLD: Refresh frequency field
refreshFrequencyNewNew=Raspoređena učestalost
#XBUT: label for None
none=Nijedno
#XBUT: label for Real-Time replication state
realtime=U stvarnom vremenu
#XFLD: Label for table column
txtNextSchedule=Sljedeće izvođenje
#XFLD: Label for table column
txtNextScheduleNew=Raspoređeno sljedeće izvođenje
#XFLD: Label for table column
txtNumOfRecords=Broj zapisa
#XFLD: Label for scheduled link
scheduledTxt=Raspoređeno
#XFLD: LABEL for partially persisted link
partiallyPersisted=Djelomično trajno
#XFLD: Text for paused text
paused=Pauzirano

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ View Monitor Tasklog Details Action keys~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message for release lock actionn
releaseLockMsg=Ako izvođenje traje duže nego inače, to može značiti da nije uspjelo i da status nije sukladno s time ažuriran. \r\n Da biste riješili problem, možete otpustiti blokadu i postaviti njegov status na Neuspješno.
#XFLD: Label for release lock dialog
releaseLockText=Otpuštanje blokade

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Column header tooltip ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: tooltip for table column
txtViewTooltip=Naziv trajnog prikaza
#XFLD: tooltip for table column
txtViewDataAccessTooltip=Ovo pokazuje dostupnost prikaza
#XFLD: tooltip for table column
txtViewRefreshFreqTooltip=Ovo pokazuje je li raspored definiran za prikaz
#XFLD: tooltip for table column
txtViewStatusTooltip=Dohvaćanje statusa trajnog prikaza
#XFLD: tooltip for table column
txtViewLatestUpdateTooltip=Navodi informaciju tome kada je trajni prikaz posljednji put ažuriran
#XFLD: tooltip for table column
txtViewNextRunTooltip=Ako je za prikaz postavljen raspored, pogledajte za kada je planirano sljedeće izvođenje.
#XFLD: tooltip for table column
txtViewNumOfRecordsTooltip=Pratite broj zapisa.
#XFLD: tooltip for table column
txtViewInMemorySizeTooltip=Pratite koliko vam prikaz zauzima memorije
#XFLD: tooltip for table column
txtViewDiskSizeTooltip=Pratite koliko vam prikaz zauzima prostora na disku
#XMSG: Expired text
txtExpired=Isteklo

#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Objekt "{0}" nije moguće dodati lancu zadataka.

#~~~~~~~~~~~View Analyzer task log messages~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message indicating number of records for a view, and memory consumption in Mebibyte during simulation of view persistency
RECORDS_PEAK_MEMORY=Prikaz "{0}" ima {1} zapisa. Simulacija trajnosti prikaza upotrijebila je {2} MiB memorije.
#XMSG: View Analyzer execution failure
VIEW_ANALYZER_EXECUTION_FAILED=Izvršenje Analizatora prikaza nije uspjelo.
#XMSG: Missing authorizations for View Analyzer
VIEW_ANALYZER_MISSING_AUTH=Nedostaju autorizacije za Analizator prikaza.
#XMSG: Maximum memory in Gibibyte is reached, therefore persistency simulation is stopped
MAXIMUM_MEMORY_REACHED=Dosegnuta je maksimalna memorija od {0} tijekom simulacije trajnosti za prikaz ''{1}''. Stoga se neće izvoditi daljnje simulacije trajnosti prikaza.
#XMSG: Error during persistency simulation occurred
PERSISTENCY_SIMULATION_ERROR=Došlo je do pogreške tijekom simulacije trajnosti za prikaz ''{0}''.
#XMSG: Persistency simulation is not executed, because prerequisites are not fulfilled and the view cannot be persisted
PREREQUISITES_NOT_FULFILLED=Simulacija trajnosti nije izvršena za prikaz ''{0}'' jer preduvjeti nisu ispunjeni i prikaz ne može biti trajan.
#XMSG: Persistency simulation is not executed, because the view does not exist in the database
VIEW_DOES_NOT_EXIST=Morate uvesti prikaz "{0}" kako biste omogućili simulaciju trajnosti podataka.
#XMSG: Number of records cannot be determined, because the local table does not exist in the database
TABLE_DOES_NOT_EXIST=Lokalna tablica "{0}" ne postoji u bazi podataka, stoga se ne može odrediti broj zapisa za tu tablicu.
##XMSG: Message start of cancellation for View Analyzer task
CANCEL_VIEW_ANALYZER_TRIGGERED=Proces za zaustavljanje zadatka Analizatora prikaza {0} za prikaz ''{1}'' poslan je. Možda dođe do odgode zaustavljanja zadatka.
##XMSG: Message start to indicate that task is not active
CANCEL_VIEW_ANALYZER_NO_ACTIVE_TASK=Zadatak Analizatora prikaza {0} za prikaz ''{1}'' nije aktivan.
#XMSG: Cancel View Analyzer task failure
CANCEL_VIEW_ANALYZER_FAILED=Otkazivanje zadatka Analizatora prikaza nije uspjelo.
##XMSG: Message used in View Analyzer execution task when task is canceled
VIEW_ANALYZER_CANCELED=Izvršenje Analizatora prikaza za prikaz ''{0}'' zaustavljeno je preko zadatka otkazivanja.
##XMSG: Message start of cancelation for Model Validation task
CANCEL_MODEL_VALIDATION_TRIGGERED=Proces za zaustavljanje zadatka validacije modela {0} za prikaz ''{1}'' poslan je. Možda dođe do odgode dok se zadatak ne zaustavi.
##XMSG: Message to indicate that task is not active
CANCEL_MODEL_VALIDATION_NO_ACTIVE_TASK=Zadatak validacije modela {0} za prikaz "{1}" nije aktivan.
#XMSG: Cancel Model Validation task failure
CANCEL_MODEL_VALIDATION_FAILED=Otkazivanje zadatka validacije modela nije uspjelo.
##XMSG: Message used in Model Validation task when task is canceled
MODEL_VALIDATION_CANCELED=Izvršenje validacije modela za prikaz ''{0}'' zaustavljeno je preko zadatka otkazivanja.
#XMSG: Message for Locked space error, before starting Model Validation task
MODEL_VALIDATION_LOCKED_SPACE_ERROR=Nije moguće izvršiti validaciju modela za prikaz "{0}" jer je prostor "{1}" zaključan.
##XMSG: Message used in View Analyzer execution task when determination of number of rows fails
NUMBER_ROWS_DETERMINATION_FAILED=Došlo je do pogreške pri određivanju broja redaka za lokalnu tablicu ''{0}''.
##XMSG: Message to indicate that SQL plan was created
EXECUTION_PLAN_CREATED=Datoteka plana analizatora SQL za prikaz ''{0}'' stvorena je i može se preuzeti.
#XFLD: Message to indicate starting of process to generate SQL Analyzer plan file
START_PLANVIZ_EXECUTION=Generiranje datoteke plana analizatora SQL za prikaz ''{0}''.
#XFLD: Message to indicate starting of View Analyzer process
VIEW_ANALYZER_EXECUTION_STARTED=Pokretanje izvršenja Analizatora prikaza.
##XMSG: Message to indicate that SQL plan file cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_PLANVIZ_NOT_FULFILLED=Datoteku plana analizatora SQL nije moguće generirati za prikaz ''{0}'' jer nisu ispunjeni preduvjeti za trajnost.
##XMSG: Error message during  generation of SQL Analyzer plan file
PLANVIZ_ERROR=Došlo je do pogreške tijekom generiranja datoteke plana analizatora SQL za prikaz ''{0}''.
#XMSG: Message for Locked space error
VIEW_ANALYZER_LOCKED_SPACE_ERROR=Nije moguće izvršiti Analizator prikaza za prikaz "{0}" jer je prostor "{1}" zaključan.
#XMSG: Message for persistency simulation without partitioning
PERSISTENCY_SIMULATION_WITHOUT_PARTITIONS=Particije prikaza ''{0}'' ne uzimaju se u obzir tijekom simulacije trajnosti prikaza.
#XMSG: Message for generation of SQL Analyzer plan file without considering partitioning
PLANVIZ_WITHOUT_PARTITIONS=Particije prikaza ''{0}''  ne uzimaju se u obzir tijekom generiranja datoteke plana analizatora SQL.
#XMSG: Confirmation message for mass removal of persisted data
MassRemovePersistency_Confirm_Msg=Želite li ukloniti trajne podatke i prebaciti pristup podacima natrag u virtualni pristup?
#XMSG: Confirmation message for partial mass removal of selected persisted data
PartialMassRemovePersistency_Confirm_Msg=Ukupno {0} od sljedećeg broja odabranih prikaza: {1} sadrži trajne podatke. \n Želite li ukloniti trajne podatke i prebaciti pristup podacima natrag u virtualni pristup?
#XMSG: success message for stopping mass persistence
stopMassPersistenceSuccess=Uklanjamo trajne podatke za odabrane prikaze.
#XMSG: error message during mass stop persistence of a view (backend error)
STOP_MASS_PERSISTENCE_ERROR=Došlo je do pogreške pri zaustavljanju trajnosti za odabrane prikaze.
#XMSG: memory analysis is only performed in space {0}
NO_MEMORY_ANALYSIS=Analiza memorije izvodi se za entitete u prostoru "{0}" preskočeno je samo: "{1}" "{2}".
##XMSG: Message to indicate that Explain Plan cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_EXPLAINPLAN_NOT_FULFILLED=Datoteku objašnjenja plana nije moguće generirati za prikaz "{0}" jer nisu ispunjeni preduvjeti za trajnost.
#XMSG: Message for Explain Plan generation without partitioning
EXPLAINPLAN_WITHOUT_PARTITIONS=Particije prikaza "{0}" ne uzimaju se u obzir tijekom generiranja datoteke objašnjenja plana.
#XFLD: Message to indicate starting of process to generate Explain Plan file
START_EXPLAINPLAN_EXECUTION=Pokretanje procesa generiranja datoteke objašnjenja plana za prikaz "{0}".
##XMSG: Message to indicate that Explain Plan was created
EXPLAINPLAN_CREATED=Generirana je datoteka objašnjenja plana za prikaz "{0}". Možete je prikazati klikom na "Prikaži pojedinosti" ili je preuzeti ako imate relevantno dopuštenje.
##XMSG: Error message during  generation of Explain Plan file
EXPLAINPLAN_ERROR=Došlo je do pogreške tijekom generiranja datoteke objašnjenja plana za prikaz "{0}".
##XMSG: Explain Plan file cannot be generated for the view {0}, because too many views are stacked on each other
EXPLAINPLAN_MODEL_TOO_COMPLEX=Datoteka objašnjenja plana ne može se generirati za prikaz "{0}". Previše je prikaza naslagano jedan na drugi. Složeni modeli mogu uzrokovati pogreške nedostatka memorije i sporu izvedbu. Zadržavanje prikaza preporučeno.

##XMSG: Message to indicate that the space is locked
PERFORMANCE_ANALYSIS_LOCKED_SPACE_ERROR=Nije moguće izvršiti analizu izvedbe za prikaz "{0}" jer je prostor "{1}" zaključan.
##XMSG: The performance analysis task was cancelled
PERFORMANCE_ANALYSIS_CANCELED=Analiza izvedbe za prikaz "{0}" otkazana.
##XMSG: The performance analysis failed
PERFORMANCE_ANALYSIS_FAILED=Analiza izvedbe nije uspjela.
##XMSG: The performance analysis is finished
PERFORMANCE_ANALYSIS_FINISHED=Analiza izvedbe za prikaz "{0}" završena. Prikažite rezultat klikom na "Prikaži pojedinosti".
##XMSG: The view has input parameter with no default value
INPUT_PARAMETER_WITHOUT_DEFAULT_VALUE=Ovaj prikaz nije moguće analizirati jer ima parametar bez zadane vrijednosti.
##XMSG: The view is not fully deployed
VIEW_NOT_FULLY_DEPLOYED=Ovaj prikaz nije moguće analizirati jer nije potpuno uveden.
##XMSG: The view uses limited remote adapters
LIMITED_REMOTE_ADAPTER=Ovaj prikaz upotrebljava najmanje jedan udaljeni adapter s ograničenim sposobnostima, kao što su nedostajuće potiskivanje filtra ili podrška za 'Brojanje'. Trajni ili replicirani objekti mogu poboljšati izvedbu vremena izvođenja.
##XMSG: The view uses remote adapter that does not support 'Limit'
REMOTE_ADAPTER_NOT_SUPPORTING_LIMIT=Ovaj prikaz upotrebljava najmanje jedan udaljeni adapter koji ne podržava 'Ograničenje'. Možda je odabrano više od 1000 zapisa.
##XMSG: Performance analysis is executed by using default parameters of view'
INPUT_PARAMETER_WITH_DEFAULT_VALUE=Analiza izvedbe izvršena je upotrebom zadanih vrijednosti parametara prikaza.
##XMSG: Message to indicate that an error occurred
PERFORMANCE_ANALYSIS_ERROR=Došlo je do pogreške tijekom analize izvedbe za prikaz "{0}".
##XMSG: Message start of cancellation for Performance Analysis task
CANCEL_PERFORMANCE_ANALYSIS_TRIGGERED=Proces za zaustavljanje zadatka analize izvedbe {0} za prikaz ''{1}'' poslan je. Možda dođe do odgode zaustavljanja zadatka.
##XMSG: Message start to indicate that task is not active
CANCEL_PERFORMANCE_ANALYSIS_NO_ACTIVE_TASK=Zadatak analize izvedbe {0} za prikaz ''{1}'' nije aktivan.
#XMSG: Cancel Performance Analysis task failure
CANCEL_PERFORMANCE_ANALYSIS_FAILED=Otkazivanje zadatka analize izvedbe nije uspjelo.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Dodijeli mi raspored
#XBUT: Pause schedule menu label
pauseScheduleLabel=Pauziraj raspored
#XBUT: Resume schedule menu label
resumeScheduleLabel=Nastavi s rasporedom
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Došlo je do pogreške tijekom uklanjanja rasporeda.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Došlo je do pogreške tijekom dodjele rasporeda.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Došlo je do pogreške tijekom pauziranja rasporeda.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Došlo je do pogreške tijekom nastavka rasporeda.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Brisanje {0} rasporeda
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Promjena vlasnika {0} rasporeda
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Pauziranje {0} rasporeda
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Nastavak {0} rasporeda
#XBUT: Select Columns Button
selectColumnsBtn=Odabir stupaca
#XFLD: Refresh tooltip
TEXT_REFRESH=Osvježi
#XFLD: Select Columns tooltip
text_selectColumns=Odabir stupaca


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Runtime Metrics ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Label for Runtime Metrics
RuntimeMetricsTitle=Metrike vremena izvođenja za
#XFLD : Label for Run Button
runButton=Izvedi
#XFLD : Label for Cancel Button
cancelButton=Odustani
#XFLD : Label for Close Button
closeButton=Zatvori
#XFLD : Label for Open View Analyzer Button
openViewAnalyzerButton=Otvori Analizator prikaza
#XFLD : Label for Generate Explain Plan Button
generateExplainPlanButton=Generiraj objašnjenje plana
#XFLD : Label for Previous Run Column
previousRun=Prethodno izvođenje
#XFLD : Label for Latest Run Column
latestRun=Najnovije izvođenje
#XFLD : Label for time Column
time=Vrijeme
#XFLD : Label for Duration Column
duration=Trajanje
#XFLD : Label for Peak Memory Column
peakMemory=Vršna memorija
#XFLD : Label for Number of Rows
numberOfRows=Broj redaka
#XFLD : Label for Overall Number of Sources
overallNumberOfSources=Ukupan broj izvora
#XFLD : Label for Data Access Column
dataAccess=Pristup podacima
#XFLD : Label for Local Tables
localTables=Lokalne tablice
#XFLD : Label for federatedRemoteTables
federatedRemoteTables=Federirane udaljene tablice (s ograničenim mogućnostima adaptera)
#XTXT Text for initial state of the runtime metrics
initialState=Morate prvo izvesti analizu izvedbe kako biste dobili metrike. To može potrajati neko vrijeme, ali možete otkazati postupak ako je potrebno.
#XMSG: Cancel confirmation message
CancelRunConfirmationMessage=Želite li zaista otkazati trenutačno izvođenje analize izvedbe?
#XTIT: Cancel dialog title
CancelRunTitle=Otkaži izvođenje
#XFLD: Label for Number of Rows
NUMBER_ROWS=Broj redaka
#XFLD: Label for Overall Number of Sources
OVERALL_NUMBER_SOURCES=Ukupan broj izvora
#XFLD: Label for Data Access
DATA_ACCESS=Pristup podacima
#XFLD: Label for Remote tables
NUMBER_REMOTE_TABLES_LIMITED_ADAPTERS=Federirane udaljene tablice (s ograničenim mogućnostima adaptera)
#XFLD: Label for select statement
SELECT_STATEMENT='SELECT * FROM VIEW LIMIT 1000'
#XFLD: Label for duration
SELECT_RUNTIME=Trajanje
#XFLD: Label for peak memory
SELECT_PEAK_MEMORY=Vršna memorija
#XFLD: Label for count statement
SELECT_COUNT_STATEMENT='ODABERI BROJ (*) IZ PRIKAZA'
#XFLD: Label for count duration
SELECT_COUNT_RUNTIME=Trajanje
#XFLD: Label for count peak memory
SELECT_COUNT_PEAK_MEMORY=Vršna memorija
#XFLD: Label for count of number of local tables
NUMBER_LOCAL_TABLES_FILE=Lokalne tablice (datoteka)
#XTXT: Text for running state of the runtime metrics
Running=Izvodi se...
#XFLD: Label for time
Time=Vrijeme
#XFLD: Label for virtual access
PA_VIRTUAL=Virtualno
#XFLD: Label for persisted access
PA_PERSISTED=Trajno
PA_PARTIALLY_PERSISTED=Djelomično trajno
#XTXT: Text for cancel
CancelRunSuccessMessage=Otkazivanje izvođenja analize izvedbe.
#XTXT: Text for cancel error
CancelRunErrorMessage=Došlo je do pogreške tijekom otkazivanja izvođenja analize izvedbe.
#XTXT: Text for explain plan generation
ExplainPlanStarted=Generiranje objašnjenja plana za prikaz "{0}".
#XTXT: Text for performance analysis
PerformanceAnalysisStarted=Pokretanje analize izvedbe za prikaz "{0}".
#XTXT: Text for performance analysis error
ErrorFetchingPerformanceAnalysis=Došlo je do pogreške tijekom dohvaćanja podataka analize izvedbe.
#XTXT: Text for performance analysis error
conflictingTask=Zadatak analize izvedbe već se izvodi
#XFLD: Label for Errors
Errors=Pogreške
#XFLD: Label for Warnings
Warnings=Upozorenja
#XFLD: Tooltip for missing privileges
openViewAnalyzerButtonTooltipNoPrivilege=Treba vam ovlaštenje DWC_DATAINTEGRATION(ažuriranje) za otvaranje Analizatora prikaza.
#XFLD: Tooltip for missing privileges
generateExplainPlanButtonTooltipNoPrivilege=Treba vam ovlaštenje DWC_RUNTIME(čitanje) za generiranje objašnjenja plana.



#XFLD: Label for frequency column
everyLabel=Svakih
#XFLD: Plural Recurrence text for Hour
hoursLabel=Sati
#XFLD: Plural Recurrence text for Day
daysLabel=Dani
#XFLD: Plural Recurrence text for Month
monthsLabel=Mjeseci
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minute
