
#XBCB: Breadcrumb
viewMonitorBreadcrumb={spaceId}

#XFLD: Label for persisted view column
SOURCE=Forrás
#XFLD: Label for persisted view column
NAME=Név
#XFLD: Label for persisted view column
NAME_LABEL=Üzleti név
#XFLD: Label for persisted view column
NAME_LABELNew=Objektum (üzleti név)
#XFLD: Label for persisted view column
TECHINCAL_NAME=Technikai név
#XFLD: Label for persisted view column
TECHINCAL_NAMENew=Objektum (technikai név)
#XFLD: Label for persisted view column
DATA_PERSISTENCY=Adathozzáférés
#XFLD: Label for persisted view column
STATUS=Állapot
#XFLD: Label for persisted view column
LAST_UPDATED=Utolsó frissítés
#XFLD: Label for persisted view column
IN_MEMORY_SIZE=Tárolóként használt memória (MiB)
#XFLD: Label for persisted view column
DISK_SIZE=Tárolóként használt lemez (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_NEWW=Méret a memóriában (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_HEADER=Méret a memóriában
#XFLD: Label for persisted view column
DISK_SIZE_NEWW=Méret a lemezen (MiB)
#XFLD: Label for persisted view column
DISK_SIZE_HEADER=Méret a lemezen
#XFLD: Label for schedule owner column
txtScheduleOwner=Ütemezés tulajdonosa
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Az ütemezés létrehozójának megjelenítése
#XFLD: Label for persisted view column
PERSISTED=Véglegesített
#XFLD: Label for persisted view column
TYPE=Típus
#XFLD: Label for View Selection Dialog column
changedOn=Módosítás dátuma
#XFLD: Label for View Selection Dialog column
createdBy=Létrehozta
#XFLD: Label for log details column
txtViewPersistencyLogs=Naplók megjelenítése
#XFLD: Label for log details column
txtViewPersistencyLogsNew=Részletek
#XFLD: text for values shown for Ascending sort order
SortInAsc=Rendezés növekvő sorrendben
#XFLD: text for values shown for Descending sort order
SortInDesc=Rendezés csökkenő sorrendben
#XFLD: Label for persisted view monitor heading
ViewMonitor_Heading=Nézetfigyelő
#XFLD: Label for persisted view monitor sub-heading
ViewMonitor_SubHeading=Nézetek adatvéglegesítésének figyelése és karbantartása


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ DynamicPageHeader ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: text for values shown in column Persistence Status
txtLoading=Betöltés
#XFLD: text for values shown in column Persistence Status
txtRunning=Fut
#XFLD: text for values shown in column Persistence Status
txtAvailable=Elérhető
#XFLD: text for values shown in column Persistence Status
txtError=Hiba
#XMSG: Error message for unknown Persistence type that gets logged in Kibana
unknownReplicationTypeError=A(z) {0} replikációtípus nem támogatott.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Content ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=A legutóbbi adatvéglegesítési futáshoz használt beállítások:
#XMSG: Message for input parameter name
inputParameterLabel=Bemeneti paraméter
#XMSG: Message for input parameter value
inputParameterValueLabel=Érték
#XMSG: Message for persisted data
inputParameterPersistedLabel=Véglegesítés időpontja
#XTIT: Count for View in ViewMonitor Toolbar
txtViewCount=Nézetek ({0})
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Nézetvéglegesítés
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Adatvéglegesítés
#XBUT: Button to clear the selected view persistance
ClearViewPersistance=Törlés
#XBUT: Button to stop the selected view persistance
stopPersistance=Véglegesítés leállítása
#XFLD: Placeholder for Search field
txtSearch=Keresés
#XBUT: Tooltip for refresh button
txtRefresh=Frissítés
#XBUT: Tooltip for add view button
txtDeleteView=Véglegesítés törlése
#XBUT: Tooltip for load new peristence
loadNewPersistence=Véglegesítés újraindítása
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Új pillanatkép betöltése
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Adatvéglegesítés indítása
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Véglegesített adatok eltávolítása
#XMSG: success message for starting persistence
startPersistenceSuccess=Véglegesítjük a(z) {0} nézetet.
#XMSG: success message for stopping persistence
stopPersistenceSuccess=Eltávolítjuk a(z) {0} nézethez tartozó véglegesített adatokat.
#XMSG: success message for stopping persistence
deletePersistedViewSuccess=Eltávolítjuk a(z) {0} nézetet a figyelési listából.
#XMSG: error message during start persistence of a view (backend error)
ERROR_STARTING_PERSISTENCE=Hiba történt a(z) {0} nézet adatvéglegesítésének elindításakor.
#XMSG: error message during start persistence of a view (backend error)
PARAMERROR_STARTING_PERSISTENCE=A(z) {0} nézet nem véglegesíthető, mert bemeneti paramétereket tartalmaz.
#XMSG: error message during start persistence of a view (backend error)
NUMBER_OF_PARAMETER_STARTING_PERSISTENCE=A(z) {0} nézet nem véglegesíthető, mert egynél több bemeneti paramétere van.
#XMSG: error message during start persistence of a view (backend error)
PARAMETER_DEFAULT_VALUE_STARTING_PERSISTENCE=A(z) {0} nézet nem véglegesíthető, mert a bemeneti paraméternek nincs alapértelmezett értéke.
#XMSG: error message during start persistence of a view (backend error)
REDEPLOYMENT_DAC_REQUIRED=A(z) {0} adathozzáférés-vezérlőt újra üzembe kell helyezni az adatvéglegesítés támogatásához.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_STARTING_PERSISTENCE=A(z) {0} nézet nem véglegesíthető, mert a(z) {1} nézetet használja, ami adathozzáférés-vezérlőt (DAC) tartalmaz.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_EXTERNAL_STARTING_PERSISTENCE=A(z) {0} nézet nem véglegesíthető, mert olyan adathozzáférés-vezérlővel (DAC) rendelkező nézetet használ, ami egy másik térhez tartozik.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_STRUCTURE_STARTING_PERSISTENCE=A(z) {0} nézet nem véglegesíthető, mert egy vagy több adathozzáférés-vezérlőjének (DAC) struktúrája nem támogatja az adatvéglegesítést.
#XMSG: error message during stop persistence of a view (backend error)
STOP_PERSISTENCE_ERROR=Hiba történt a(z) {0} nézet véglegesítésének leállításakor.
#XMSG: error message during stop persistence of a view (backend error)
DELETE_PERSISTED_VIEW_ERROR=Hiba történt a(z) {0} véglegesített nézet törlésekor.
#XMSG: Warning message for removing the persisted data
RemovePersistency_Confirm_Msg=Eltávolítja a véglegesített adatokat, és átvált a(z) {0} nézet virtuális elérésére?
#XMSG: Warning message for delete the view persistence
DeletePersistency_Confirm_Msg=Eltávolítja a nézetet a figyelési listából, és törli a(z) {0} nézet véglegesített adatait?
#XMSG: error message for reading data from backend
txtReadBackendError=Hiba történt a backendből való olvasáskor.
#XFLD: Label for No Data Error
NoDataError=Hiba
#XMSG: message for conflicting task
Task_Already_Running=Már fut egy ezzel ütköző feladat a(z) {0} nézetnél.

#XFLD: Error while accessing pertitioning for a view
missingPrivilegeInViewPersistency=Nincs jogosultsága a particionálás végrehajtásához a(z) {0} nézetnél

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Show All Views~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Text for show all views button
allViewsText=Minden nézet ({0})
#XBUT: Text for show scheduled views button
scheduledText=Beütemezett ({0})
#XBUT: Text for show persisted views button
persistedText=Véglegesített ({0})
#XBUT: Text for start analyzer button
startAnalyzer=Nézetelemző indítása
#XFLD: Message if repository is unavailable
repositoryErrorMsg=A tárház nem érhető el, és bizonyos funkciók le vannak tiltva.

#XFLD: Data Access - Virtual
Virtual=Virtuális
#XFLD: Data Access - Persisted
Persisted=Véglegesített

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Non-Persisted View Selection Dialog~~~~~~~~~~~~~~~~~~~~~~~

#XTIT: Title for Non-Persisted View selection dialog
select_source=Válassza ki a véglegesítendő nézetet

#XTIT: Placeholder for Non-Persisted View selection dialog search
searchView=Nézetek keresése
#XTIT: No data in the list of non-persisted view
No_Data=Nincs adat
#XBUT: Button to select non-persisted view
ok=OK
#XBUT: Button to close the non-persisted views selection dialog
cancel=Mégse

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~View persistency log message key~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message for view persistency logs
START_STOP_PERSISTENCY=Adatvéglegesítési feladat futtatásának indítása: {1}.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY=Adatok véglegesítése a(z) {1} nézetnél.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY)
START_VIEW_PERSISTENCY_PROCESS=Adatok véglegesítési folyamatának indítása a(z) {0} nézetnél.
#XFLD: Message for view persistency manual partition load
START_VIEW_PERSISTENCY_PROCESS_MANUAL_PARTITIONS=Folyamat indítása a(z) {0} nézet adatainak véglegesítéséhez, kiválasztott partícióazonosítók: {1}.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY=Véglegesített adatok eltávolítása a(z) {1} nézetnél.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY)
REMOVE_VIEW_PERSISTENCY=Véglegesített adatok eltávolítási folyamatának indítása a(z) {0} nézetnél.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_SUCCESS=Az adatok véglegesítve a(z) {1} nézetnél.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_SUCCESS)
START_VIEW_PERSISTENCY_PROCESS_SUCCESS=Az adatok véglegesítve a(z) {0} nézetnél.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY_SUCCESS=A véglegesített adatok eltávolítva és a virtuális adathozzáférés helyreállítva a(z) {1} nézetnél.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_SUCCESS)
REMOVE_VIEW_PERSISTENCY_SUCCESS=A véglegesített adatok eltávolítási folyamata befejeződött a(z) {0} nézetnél.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_FAILED=Nem lehet véglegesíteni az adatokat a(z) {1} nézetnél.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_FAILED)
START_VIEW_PERSISTENCY_PROCESS_FAILED=Nem lehet véglegesíteni az adatokat a(z) {0} nézetnél.
#XFLD: Message for view persistency logs (for messages created before version 2022.05)
STOP_VIEW_PERSISTENCY_FAILED=Nem lehet eltávolítani a véglegesített adatokat a(z) {1} nézetnél.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_FAILED)
REMOVE_VIEW_PERSISTENCY_FAILED=Nem lehet eltávolítani a véglegesített adatokat a(z) {0} nézetnél.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_SUCCESS={3} rekord véglegesítve a(z) {1} nézetnél.
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace START_PERSISTENCY_RECORD_COUNT_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS={0} rekord beszúrva az adatvéglegesítési táblába a(z) {1} nézetnél.
#XFLD: Message used by SEAL repository for view persistency logs (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS_V2={0} rekord beszúrva az adatvéglegesítési táblába a(z) {1} nézetnél. Felhasznált memória: {2} GiB.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_SUCCESS={3} véglegesített rekord eltávolítva a(z) {1} nézetnél.
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace STOP_PERSISTENCY_RECORD_COUNT_SUCCESS)
REMOVE_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=A véglegesített adatok eltávolítva, {0} véglegesített rekord törölve.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_FAILED=Nem sikerült lehívni a rekordok számát a(z) {1} nézetnél.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_FAILED=Nem sikerült lehívni a rekordok számát a(z) {1} nézetnél.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Az ütemezés törölve: {1}.
#XFLD: Message for view persistency logs (new message to replace STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS)
REMOVE_VIEW_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Az ütemezés törölve a(z) {0} nézetnél.
#XFLD: Message for view persistency logs
STOP_PERSISTENCY_DELETE_SCHEDULE_FAILED=Nem sikerült törölni az ütemezést: {1}.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_REDEPLOY=Nem sikerült véglegesíteni a(z) {0} nézetet, mert módosították és üzembe helyezték azóta, hogy megkezdte a véglegesítését. Próbálja újra véglegesíteni a nézetet, vagy várja meg a legközelebbi beütemezett futást.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_DELETE=Nem sikerült véglegesíteni a(z) {0} nézetet, mert törölték azóta, hogy megkezdte a véglegesítését.
#XFLD: Message for successful data persistency of a single partition (for messages created before version 2022.03)
START_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} rekord véglegesítve az értékek partíciójában: {1} <= {2} < {3}.
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (new message to replace START_PERSISTENCY_SINGLE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} rekord beszúrva az értékek partíciójába: {1} <= {2} < {3}.
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS_V2={0} rekord beszúrva az értékek partíciójába: {1} <= ''{2}'' < {3}. Felhasznált memória: {4} GiB.
#XFLD: Message for successful replication of a "others" partition (for messages created before version 2022.03)
START_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} rekord véglegesítve a "többi" partícióban.
#XFLD: Message used by SEAL repository for successful replication of a "others" partition (new message to replace START_PERSISTENCY_OTHERS_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} rekord beszúrva a "többi" partícióba.
#XFLD: Message for view persistency record count of partitions (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={3} rekord véglegesítve a(z) {1} nézetnél {4} partícióban.
#XFLD: Message used by SEAL repository for view persistency record count of partitions (new message to replace START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={0} rekord beszúrva az adatvéglegesítési táblába a(z) {1} nézetnél {2} partícióban.
#XFLD: Message used by SEAL repository for view persistency for partition wise refresh
START_VIEW_PERSISTENCY_RECORD_COUNT_REFRESHED_PARTITIONS_SUCCESS={0} rekord beszúrva a véglegesítési táblába a(z) {1} nézetnél. Frissített partíciók: {2}; zárolt partíciók: {3}; partíciók száma összesen: {4}
#XFLD: Message used by SEAL repository for view persistency for manual partition load
START_VIEW_PERSISTENCY_RECORD_COUNT_MANUAL_PARTITIONS_SUCCESS={0} rekord beszúrva az adatvéglegesítési táblába a(z) {1} nézetnél {2} kiválasztott partícióban.
#XFLD: Message used by SEAL repository for view persistency for locked partition data load
START_VIEW_PERSISTENCY_RECORD_COUNT_LOCKED_PARTITIONS_SUCCESS={0} rekord beszúrva az adatvéglegesítési táblába a(z) {1} nézetnél. Frissített partíciók: {2}; zárolt változatlan partíciók: {3}; partíciók száma összesen: {4}.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
START_PERSISTENCY_UNEXPECTED_ERROR=Váratlan hiba történt az adatok véglegesítésekor a(z) {0} nézetnél.
#XMSG: Message for unexpected error (new message to replace START_PERSISTENCY_UNEXPECTED_ERROR)
START_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Váratlan hiba történt az adatok véglegesítésekor a(z) {0} nézetnél.
#XMSG: Message for Locked space error
START_VIEW_PERSISTENCY_LOCKED_SPACE_ERROR=A(z) {0} nézet nem véglegesíthető, mert a(z) {1} tér zárolva van.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
STOP_PERSISTENCY_UNEXPECTED_ERROR=Váratlan hiba történt a véglegesített adatok eltávolításakor a(z) {0} nézetnél.
#XMSG: Message for unexpected error (new message to replace STOP_PERSISTENCY_UNEXPECTED_ERROR)
REMOVE_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Váratlan hiba történt a véglegesítés eltávolításakor a(z) {0} nézetnél.
#XMSG: Message for ivalidated view
VIEW_PERSISTENCY_ABORTED_DEFINITION_INVALID=A(z) {0} nézet definíciója érvénytelenné vált, valószínűleg a nézet által közvetlenül vagy közvetve felhasznált objektum módosulása miatt. A probléma megoldásához, illetve a mögöttes ok kiderítéséhez helyezze üzembe újra a nézetet.
#XMSG: Message for persisted data removal (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT=A véglegesített adatok el lesznek távolítva a(z) {0} nézet üzembe helyezésekor.
#XMSG: Message for persisted data removal of a consuming view
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW=A véglegesített adatok el lesznek távolítva a(z) {0} felhasznált nézet üzembe helyezésekor.
#XMSG: Message for persisted data removal of a consuming view due to a dac change
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW_DAC_CHANGED=A véglegesített adatok el lesznek távolítva a(z) {0} felhasznált nézet üzembe helyezésekor, mert módosult az adathozzáférés-vezérlője.
#XMSG: Message for persisted data removal (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DEPLOYMENT=A véglegesített adatok el lesznek távolítva a(z) {0} nézet üzembe helyezésekor.
#XMSG: Message for deleted view (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION=A véglegesítés el lesz távolítva a(z) {0} nézet törlésekor.
#XMSG: Message for deleted view (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DELETION=A véglegesítés el lesz távolítva a(z) {0} nézet törlésekor.
#XMSG Message for removal of persisted data, because prerequisites not fulfilled
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_PREREQUISITES_NOT_FULFILLED=A véglegesített adatok eltávolítva, mert már nem teljesülnek az adatvéglegesítés előfeltételei.
#XMSG: Message for inconsistent persistency
VIEW_PERSISTENCY_INCONSISTENT_PERSISTENCY=A(z) {0} nézet véglegesítése inkonzisztenssé vált. A probléma megoldásához távolítsa el a véglegesített adatokat.
#XMSG: Message for prerequisites
START_PERSISTENCY_CHECK_PREREQUISITES=A(z) {0} nézet véglegesítési előfeltételeinek ellenőrzése.
#XMSG: Message for DAC deployed in deprecated way using table function and join (for messages created before 2025.05)
START_PERSISTENCY_DEPRECIATED_DAC=A(z) {0} nézet egy kivezetendő adathozzáférés-vezérlő (DAC) használatával van üzembe helyezve. Helyezze újra üzembe a nézetet a teljesítmény javításához.
#XMSG: Message for DAC deployed in deprecated way using table function and join
START_PERSISTENCY_DEPRECATED_DAC=A(z) {0} nézet egy kivezetendő adathozzáférés-vezérlő (DAC) használatával van üzembe helyezve. Helyezze újra üzembe a nézetet a teljesítmény javításához.
#XMSG: Message for roll back (for messages created before version 2022.03)
ROLLBACK_VIEW_PERSISTENCY=Hiba történt. A korábbi véglegesítési állapot visszaállítva a(z) {0} nézetnél.
#XMSG: Message used by SEAL repository for rollback of running procedure to persist view (new message to replace ROLLBACK_VIEW_PERSISTENCY)
VIEW_PERSISTENCY_ROLLBACK=Hiba történt. A(z) {0} nézet véglegesítési folyamata leállt, és a módosítások vissza lettek vonva.
#XMSG: Message used by SEAL repository for rollback of running procedure to remove persisted data of view
REMOVE_VIEW_PERSISTENCY_ROLLBACK=Hiba történt. A(z) {0} nézet véglegesített adatainak eltávolítási folyamata leállt, és a módosítások vissza lettek vonva.
#XMSG: Message used by SEAL repository for persistency preparation
START_PERSISTENCY_PREPARATION=Adatok véglegesítésének előkészítése.
#XMSG: Message used by SEAL repository for data insertion
START_INSERTING_PERSISTENCY_DATA=Adatok beszúrása a véglegesítési táblába.
#XMSG: Message for null values - partitioning (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS={0} null értékű rekord beszúrva a ''többi" partícióba.
#XMSG: Message used by SEAL repository for others partition values - partitioning (new message to replace START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS={0} rekord beszúrva a "többi" partícióba a következő értékeknél: {2} < {1} OR {2} >= {3}.
#XMSG: Message used by SEAL repository for others partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS_V2={0} rekord beszúrva a "többi" partícióba a következő értékeknél: {2}'' < {1} OR {2} >= {3}. Felhasznált memória: {4} GiB.
#XMSG: Message used by SEAL repository for null partition values - partitioning
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS={0} rekord beszúrva a "többi" partícióba a következő értékeknél: {1} IS NULL.
#XMSG: Message used by SEAL repository for null partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS_V2={0} rekord beszúrva a "többi" partícióba a következő értékeknél: {1} IS NULL. Felhasznált memória: {2} GiB
##XFLD:Message used by SEAL repository for remote statistics
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS=Az érintett adatok betöltése: {0} távoli utasítás. Összes behívott rekord: {1}. Teljes időtartam: {2} másodperc.
##XFLD:Message used by SEAL repository for remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS_WITH_PARTITIONS=Az érintett adatok betöltése {0} partíció használatával, {1} távoli utasítással. Összes behívott rekord: {2}. Teljes időtartam: {3} másodperc.
##XFLD:Message used by SEAL repository with UI ID for instructions of remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_INSTRUCTION=A futás alatt feldolgozott távoli utasítások megjelenítéséhez nyissa meg a partícióspecifikus üzenetek részleteiben a távolilekérdezés-figyelőt.
##XFLD:Message for start reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS=A(z) {0} nézet meglévő véglegesített adatainak újrahasználatát végző folyamat indítása üzembe helyezés után.
##XFLD:Message used by SEAL repository for reattach view persitency
START_VIEW_PERSISTENCY_REATTACHMENT=A meglévő véglegesített adatok újrahasználatának indítása.
##XFLD:Message used by SEAL repository for reattach view persitency
START_FINILIZING_VIEW_PERSISTENCY_REATTACHMENT=A meglévő véglegesített adatok újrahasználata.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_COMPLETE=A meglévő véglegesített adatok újrahasználati folyamata befejeződött a következő nézetnél: {0}.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_FAILED=Nem sikerült újra felhasználni a meglévő véglegesített adatokat üzembe helyezés után a következő nézetnél: {0}.
#XMSG: Message used by SEAL repository for finalizing persistency
START_FINALIZING_PERSISTENCY=Véglegesítés befejezése.
#XMSG: Message used by SEAL repository for restoring virtual access for view
REMOVE_VIEW_PERSISTENCY_VIRTUAL_ACCESS_RESTORED=A virtuális adathozzáférés visszaállítva a(z) {0} nézetnél.
#XMSG: Message used by SEAL repository for view that already has virtual access
REMOVE_VIEW_PERSISTENCY_ALREADY_VIRTUAL_ACCESS=A(z) {0} nézetnek már van virtuális adathozzáférése. Nem lesznek eltávolítva véglegesített adatok.
#XMSG: Message for removing view persistency
VIEW_PERSISTENCY_REMOVED_FROM_MONITOR=A(z) {0} nézet eltávolítva a Nézetfigyelőből.
#XMSG: Message for view doesnt exist.
VIEW_PERSISTENCY_VIEW_NOT_EXISTING=A(z) {0} nézet nem létezik az adatbázisban, vagy nincs megfelelően üzembe helyezve, ezért nem véglegesíthető. A probléma megoldásához vagy az eredendő ok megállapításához helyezze újra üzembe a nézetet.
#XMSG: Message for persistency not enabled.
VIEW_PERSISTENCY_NOT_ENABLED=Az adatvéglegesítés nincs engedélyezve. A funkció engedélyezéséhez helyezze újra üzembe a táblát/nézetet a(z) {0} térben.
#XMSG: Message for unexpected error due to node crash
VIEW_PERSISTENCY_UNEXPECTED_ERROR_GENERAL=A legutóbbi nézetvéglegesítési futás technikai okok miatt megszakadt.
#XFLD: Message for view persistency memory consumption
VIEW_PERSISTENCY_MEMORY_CONSUMPTION={0} GiB memória-csúcsérték felhasználva a nézetvéglegesítés futásidejében.
#XFLD: Message for view persistency timeout
VIEW_PERSISTENCY_TIMEOUT={0} nézet véglegesítése elérte a(z) {1} órás időkorlátot.
#XFLD: Message for not started asynchronous view persistency
VIEW_PERSISTENCY_ASYNC_NOT_STARTED=Nagy rendszerterhelés miatt nem indult el a nézetvéglegesítés aszinkron végrehajtása. Ellenőrizze, hogy nem fut-e túl sok feladat egyszerre.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency (for messages created before version 2022.05)
START_VIEW_PERSISTENCY_EXECUTION_METHOD_DROP_AND_RENAME=A meglévő véglegesített tábla törlődött és új véglegesített táblára cserélődött.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_RECREATE_AND_DROP=A meglévő véglegesített tábla törlődött és új véglegesített táblára cserélődött.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_TRUNCATE_AND_COPY=A meglévő véglegesített tábla új adatokkal frissült.
##XFLD: Message for missing authorizations for view persistency
VIEW_PERSISTENCY_MISSING_AUTH=Hiányoznak jogosultságok az adatvéglegesítéshez.
##XFLD: Message start of persistency cancellation
VIEW_PERSISTENCY_TRIGGER_CANCEL=A(z) {0} nézetet véglegesítő folyamat megszakításának indítása.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_UNAVAILABLE_TASK_RUN=Nem sikerült megszakítani a nézetvéglegesítési folyamatot, mert nincs futó adatvéglegesítési folyamat a(z) {0} nézetnél.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_NONDETERMINISTIC_TASK_RUN=Nem sikerült megszakítani a nézetvéglegesítési folyamatot, mert nem fut adatvéglegesítési folyamat a(z) {0} nézetnél.
##XFLD: Message for canceling a non running persitency task
VIEW_PERSISTENCY_CANCEL_NON_RUNNING_SELECTED_TASK=Nem sikerült megszakítani a(z) {0} nézet véglegesítési folyamatát, mert nem fut a választott adatvéglegesítési feladat ({1}).
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_UNREACHABLE_PERSISTENCY_RUN=Nem sikerült megszakítani a nézetvéglegesítési folyamatot, mert még nem indult el az adatvéglegesítés a(z) {0} nézetnél.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_COMPLETED_PERSISTENCY_RUN=Nem sikerült megszakítani a(z) {0} nézet véglegesítési folyamatát, mert már befejeződött.
##XFLD: Message for failed cancellation
VIEW_PERSISTENCY_ABORTED_CANCEL_EXECUTION=Nem sikerült megszakítani a(z) {0} nézet véglegesítési folyamatát.
##XFLD: Message for finalizing persistency cancellation
VIEW_PERSISTENCY_FINALIZE_CANCEL=A(z) {0} nézet adatvéglegesítésének leállítását végző folyamat beküldve.
##XFLD: Message used in persist task when cancellation is caught
VIEW_PERSISTENCY_CANCELED=A(z) {0} nézetet véglegesítő folyamat leállt.
##XFLD: Message used in persist task when cancelation is caught and performed through a cancelation task
VIEW_PERSISTENCY_CANCELED_VIA_CANCEL_TASK=A(z) {0} nézetet véglegesítő folyamat a következő megszakítási feladat által leállítva: {1}.
##XFLD: Message used in cancel task when the task is triggered by deployment
VIEW_PERSISTENCY_CANCEL_TRIGGERED_BY_DEPLOYMENT=Az adatvéglegesítési folyamat megszakítása a(z) {0} nézet üzembe helyezése közben.
##XFLD: Message used in case the cancelation is retriggered for a running task
VIEW_PERSISTENCY_CANCEL_ATTEMPT_RETRIGGER_CANCEL=A(z) {0} nézet véglegesítésére vonatkozóan már be van küldve egy korábbi megszakítási feladat.
##XFLD: Message used to express the delay that may occur before the persistency task is stopped
VIEW_PERSISTENCY_CANCEL_DELAY_STOPPING_PERSISTENCY=Előfordulhat, hogy a(z) {0} nézet adatvéglegesítési feladata csak valamivel később áll le.
##XFLD: Message used to reflect the task log identifier of the current running persistency task
VIEW_PERSISTENCY_CANCEL_CURRENT_RUNNING_TASK=A(z) {0} nézethez tartozó adatok a(z) {1} feladattal véglegesítődnek.
##XFLD:Message used by SEAL repository for locked partitions and DACs
VIEW_PERSISTENCY_LOCKED_PARTITIONS_DACS=Lehet, hogy módosultak a DAC-k által biztosított jogosultságok, és a zárolt partíciók nem veszik figyelembe őket. A módosítások alkalmazásához oldja fel a partíciók zárolását, és töltsön be új pillanatképet.
##XFLD:Message used by SEAL repository for column structure change
VIEW_PERSISTENCY_COLUMN_STRUCTURE_CHANGE=Megváltozott az oszlopstruktúra, és már nem egyezik a meglévő véglegesítési táblábal. Távolítsa el a véglegesített adatokat, vagy indítson új adatvéglegesítést a véglegesítési tábla frissítéséhez.
##XFLD: Message for out of memory
VIEW_PERSISTENCY_HANA_OUT_OF_MEMORY=A feladat sikertelen, mert a hibaüzenet szerint elfogyott a memória az SAP HANA-adatbázisban.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_EXCEPTION_TRANSACTION_ROLLBACK=A feladat sikertelen, mert belső kivétel történt az SAP HANA-adatbázisban.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_SQL_EXECUTION_ISSUE=A feladat sikertelen, mert belső SQL-végrehajtási hiba történt az SAP HANA-adatbázisban.
##XFLD: Message for HANA out of memory result
VIEW_PERSISTENCY_OUT_OF_MEMORY_RESULT_MESSAGE=HANA memóriaelfogyási esemény oka: {0}
##XFLD: Message for admission control rejection
VIEW_PERSISTENCY_HANA_ADMISSION_CTRL_REJECTION=A feladat sikertelen, mert az SAP HANA bejutás-ellenőrzése elutasította.
##XFLD: Message for HANA back pressure / rate limit of HANA client
VIEW_PERSISTENCY_HANA_CLIENT_RATE_LIMIT_REJECTION=A feladat túl sok aktív SAP HANA-kapcsolat miatt sikertelen.
#XMSG: Message for invalid persisted table name
VIEW_PERSISTENCY_PERSISTED_TABLE_INVALID=Hiba történt, és érvénytelenné vált a véglegesített tábla. A probléma megoldásához távolítsa el a véglegesített adatokat, és véglegesítse újra a nézetet.
#XMSG: Message for usahe of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION=A nézet nem véglegesíthető. Olyan távoli táblát használ, ami engedélyezett felhasználópropagálással rendelkező távoli forráson alapul. Ellenőrizze a nézet származását.
#XMSG: Message for dynamic usage of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION_DYNAMIC=A nézet nem véglegesíthető. Olyan távoli táblát használ, ami engedélyezett felhasználópropagálással rendelkező távoli forráson alapul. A távoli tábla dinamikusan használható fel egy SQL-szkript-nézet által. Lehet, hogy a távoli tábla nem jelenik meg nézet származásában.
#XMSG: Message for insufficient privileges or consumption of view with DAC via dynamic SQL
VIEW_PERSISTENCY_INSUF_PRIV_OR_DAC_DYN_SQL=Lehet, hogy nincs elegendő jogosultsága. Nyissa meg az adatelőnézetet, hogy megtudja, rendelkezik-e a szükséges jogosultságokkal. Ha igen, akkor lehet, hogy a dinamikus SQL-szkripttel felhasznált második nézetnél adathozzáférés-vezérlő (DAC) van érvényben.
#XMSG: deployment of DAC is deprecated and view need to be redeployed to be able to persist data
VIEW_PERSISTENCY_DEPRECATED_DAC=A(z) {0} nézet egy kivezetendő adathozzáférés-vezérlő (DAC) használatával van üzembe helyezve. Helyezze újra üzembe a nézetet, hogy véglegesíthesse az adatait.
##XFLD:Message used by SEAL repository for input parameter
START_VIEW_PERSISTENCY_INPUT_PARAMETER=Az alapértelmezett érték ({0}) használata a következő bemeneti paraméterhez: {1}.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Elastic compute node keys~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
##XFLD:Message used to reflect disablement of ecn replica
ECN_DISABLE_EXISTING_REPLICA=Az alkalmazkodó számítási csomópont replikája le van tiltva.
##XFLD:Message used to reflect recreation of ecn replica
ECN_RECREATE_EXISTING_REPLICA=Az alkalmazkodó számítási csomópont replikája újra létrejött.
##XFLD:Message used to reflect re-enablement of ecn replica
ECN_ENABLE_EXISTING_REPLICA=Az alkalmazkodó számítási csomópont replikája újraengedélyezve.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Task Schedule Menu key~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message to schedule task
scheduleText=Ütemezés
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Ütemezés létrehozása
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Ütemezés szerkesztése
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Ütemezés törlése
#XFLD: Refresh frequency field
refreshFrequency=Frissítés gyakorisága
#XFLD: Refresh frequency field
refreshFrequencyNew=Gyakoriság
#XFLD: Refresh frequency field
refreshFrequencyNewNew=Ütemezés szerinti gyakoriság
#XBUT: label for None
none=Nincs
#XBUT: label for Real-Time replication state
realtime=Valós idejű
#XFLD: Label for table column
txtNextSchedule=Következő futás
#XFLD: Label for table column
txtNextScheduleNew=Legközelebbi beütemezett futás
#XFLD: Label for table column
txtNumOfRecords=Rekordok száma
#XFLD: Label for scheduled link
scheduledTxt=Beütemezve
#XFLD: LABEL for partially persisted link
partiallyPersisted=Részben véglegesítve
#XFLD: Text for paused text
paused=Szünetel

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ View Monitor Tasklog Details Action keys~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message for release lock actionn
releaseLockMsg=Ha a futás tovább tart a megszokottnál, előfordulhat, hogy sikertelen volt, és nem frissült ennek megfelelően az állapota. \r\n A probléma megoldásához oldja fel a zárolást, és állítsa be a Sikertelen állapotot.
#XFLD: Label for release lock dialog
releaseLockText=Zárolás feloldása

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Column header tooltip ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: tooltip for table column
txtViewTooltip=Véglegesített nézet neve
#XFLD: tooltip for table column
txtViewDataAccessTooltip=Ez a nézet elérhetőségét mutatja
#XFLD: tooltip for table column
txtViewRefreshFreqTooltip=Azt mutatja, hogy van-e megadva ütemezés a nézethez
#XFLD: tooltip for table column
txtViewStatusTooltip=A véglegesített nézet állapotának lehívása
#XFLD: tooltip for table column
txtViewLatestUpdateTooltip=Arról szolgál információval, hogy utoljára mikor frissítették a véglegesített nézetet
#XFLD: tooltip for table column
txtViewNextRunTooltip=Ha ütemezés van beállítva a nézethez, láthatja, hogy mikorra van beütemezve a következő futás.
#XFLD: tooltip for table column
txtViewNumOfRecordsTooltip=A rekordok számának nyomon követése.
#XFLD: tooltip for table column
txtViewInMemorySizeTooltip=Ennyi memóriát használ fel a nézet
#XFLD: tooltip for table column
txtViewDiskSizeTooltip=Ekkora lemezterületet használ fel a nézet
#XMSG: Expired text
txtExpired=Lejárt

#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=A(z) {0} objektum nem adható hozzá a feladatlánchoz.

#~~~~~~~~~~~View Analyzer task log messages~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message indicating number of records for a view, and memory consumption in Mebibyte during simulation of view persistency
RECORDS_PEAK_MEMORY=A(z) {0} nézetnek {1} rekordja van. A nézet adatvéglegesítés-szimulációja {2} MiB memóriát használt.
#XMSG: View Analyzer execution failure
VIEW_ANALYZER_EXECUTION_FAILED=Nem sikerült futtatni a nézetelemzőt.
#XMSG: Missing authorizations for View Analyzer
VIEW_ANALYZER_MISSING_AUTH=Hiányzó jogosultságok a nézetelemzőhöz.
#XMSG: Maximum memory in Gibibyte is reached, therefore persistency simulation is stopped
MAXIMUM_MEMORY_REACHED=Elérte a {0} GiB-os maximális memóriafelhasználást a(z) {1} nézet adatvéglegesítésének szimulációja során. Ezért nem fut több adatvéglegesítés-szimuláció.
#XMSG: Error during persistency simulation occurred
PERSISTENCY_SIMULATION_ERROR=Hiba történt az adatvéglegesítés szimulációja során a(z) {0} nézetnél.
#XMSG: Persistency simulation is not executed, because prerequisites are not fulfilled and the view cannot be persisted
PREREQUISITES_NOT_FULFILLED=Az adatvéglegesítés szimulációja nincs végrehajtva a(z) {0} nézetnél, mert nem teljesültek az előfeltételek, és nem lehet véglegesíteni a nézetet.
#XMSG: Persistency simulation is not executed, because the view does not exist in the database
VIEW_DOES_NOT_EXIST=Az adatvéglegesítés szimulációjának engedélyezéséhez üzembe kell helyeznie a(z) {0} nézetet.
#XMSG: Number of records cannot be determined, because the local table does not exist in the database
TABLE_DOES_NOT_EXIST=A(z) {0} helyi tábla nem létezik az adatbázisban, ezért nem lehet megállapítani a rekordok számát ennél a táblánál.
##XMSG: Message start of cancellation for View Analyzer task
CANCEL_VIEW_ANALYZER_TRIGGERED=Folyamat beküldve a(z) {0} Nézetelemző-feladat ({1} nézet) leállítására. Előfordulhat, hogy a feladat leállítása valamivel később történik meg.
##XMSG: Message start to indicate that task is not active
CANCEL_VIEW_ANALYZER_NO_ACTIVE_TASK=Nem aktív a(z) {0} Nézetelemző-feladat a(z) {1} nézethez.
#XMSG: Cancel View Analyzer task failure
CANCEL_VIEW_ANALYZER_FAILED=Nem sikerült megszakítani a Nézetelemző-feladatot.
##XMSG: Message used in View Analyzer execution task when task is canceled
VIEW_ANALYZER_CANCELED=A Nézetelemző futtatása a(z) {0} nézethez megszakítási feladat által leállítva.
##XMSG: Message start of cancelation for Model Validation task
CANCEL_MODEL_VALIDATION_TRIGGERED=Folyamat beküldve a(z) {0} modellvalidálási feladat ({1} nézet) leállítására. Előfordulhat, hogy a feladat leállítása valamivel később történik meg.
##XMSG: Message to indicate that task is not active
CANCEL_MODEL_VALIDATION_NO_ACTIVE_TASK=Nem aktív a(z) {0} modellvalidálási feladat a(z) {1} nézethez.
#XMSG: Cancel Model Validation task failure
CANCEL_MODEL_VALIDATION_FAILED=Nem sikerült megszakítani a modellvalidálási feladatot.
##XMSG: Message used in Model Validation task when task is canceled
MODEL_VALIDATION_CANCELED=A modellvalidálás futtatása a(z) {0} nézethez megszakítási feladat által leállítva.
#XMSG: Message for Locked space error, before starting Model Validation task
MODEL_VALIDATION_LOCKED_SPACE_ERROR=Nem futtatható modellvalidálás a(z) {0} nézethez, mert a(z) {1} tér zárolva van.
##XMSG: Message used in View Analyzer execution task when determination of number of rows fails
NUMBER_ROWS_DETERMINATION_FAILED=Hiba történt a sorok számának meghatározásakor a(z) {0} helyi táblánál.
##XMSG: Message to indicate that SQL plan was created
EXECUTION_PLAN_CREATED=Létrejön és letölthető az SQL-elemző tervfájlja a(z) {0} nézethez.
#XFLD: Message to indicate starting of process to generate SQL Analyzer plan file
START_PLANVIZ_EXECUTION=SQL-elemző tervfájljának generálása a(z) {0} nézethez.
#XFLD: Message to indicate starting of View Analyzer process
VIEW_ANALYZER_EXECUTION_STARTED=Nézetelemző futtatásának indítása.
##XMSG: Message to indicate that SQL plan file cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_PLANVIZ_NOT_FULFILLED=Nem generálható az SQL-elemző tervfájlja a(z) {0} nézethez, mert nem teljesülnek az adatvéglegesítés előfeltételei.
##XMSG: Error message during  generation of SQL Analyzer plan file
PLANVIZ_ERROR=Hiba történt az SQL-elemző tervfájljának generálásakor a(z) {0} nézethez.
#XMSG: Message for Locked space error
VIEW_ANALYZER_LOCKED_SPACE_ERROR=Nem futtatható a Nézetelemző a(z) {0} nézethez, mert a(z) {1} tér zárolva van.
#XMSG: Message for persistency simulation without partitioning
PERSISTENCY_SIMULATION_WITHOUT_PARTITIONS=A(z) {0} nézet partíciói figyelmen kívül maradnak az adatvéglegesítés szimulációja során.
#XMSG: Message for generation of SQL Analyzer plan file without considering partitioning
PLANVIZ_WITHOUT_PARTITIONS=A(z) {0} nézet partíciói figyelmen kívül maradnak az SQL-elemző tervfájljának generálása során.
#XMSG: Confirmation message for mass removal of persisted data
MassRemovePersistency_Confirm_Msg=Eltávolítja a véglegesített adatokat, és visszavált virtuális adathozzáférésre?
#XMSG: Confirmation message for partial mass removal of selected persisted data
PartialMassRemovePersistency_Confirm_Msg=A kiválasztott {1} nézetből {0} tartalmaz véglegesített adatokat. \n Eltávolítja a véglegesített adatokat, és visszavált virtuális adathozzáférésre?
#XMSG: success message for stopping mass persistence
stopMassPersistenceSuccess=Eltávolítjuk a véglegesített adatokat a kiválasztott nézeteknél.
#XMSG: error message during mass stop persistence of a view (backend error)
STOP_MASS_PERSISTENCE_ERROR=Hiba történt a kiválasztott nézetek véglegesítésének leállításakor.
#XMSG: memory analysis is only performed in space {0}
NO_MEMORY_ANALYSIS=Csak a(z) {0} térben lévő entitásoknál történik memóriaelemzés: {1} {2} kihagyva.
##XMSG: Message to indicate that Explain Plan cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_EXPLAINPLAN_NOT_FULFILLED=A tervmagyarázati fájl nem hozható létre a(z) {0} nézethez, mert a perzisztenciai előfeltételek nem teljesülnek.
#XMSG: Message for Explain Plan generation without partitioning
EXPLAINPLAN_WITHOUT_PARTITIONS=A(z) {0} nézet partíciói nem lesznek figyelembe véve a tervmagyarázati fájl létrehozásakor.
#XFLD: Message to indicate starting of process to generate Explain Plan file
START_EXPLAINPLAN_EXECUTION=A(z) {0} nézethez tartozó tervmagyarázati fájl létrehozási folyamatának indítása.
##XMSG: Message to indicate that Explain Plan was created
EXPLAINPLAN_CREATED=A(z) {0} nézethez tartozó tervmagyarázati fájl létrejtt. A Részletek megtekintése gombra kattintva megjelenítheti, illetve megfelelő jogosultság birtokában le is töltheti.
##XMSG: Error message during  generation of Explain Plan file
EXPLAINPLAN_ERROR=Hiba történt a(z) {0} nézet tervmagyarázati fájljának létrehozásakor.
##XMSG: Explain Plan file cannot be generated for the view {0}, because too many views are stacked on each other
EXPLAINPLAN_MODEL_TOO_COMPLEX=Nem generálható tervmagyarázati fájl a(z) {0} nézethez. Túl sok nézet van egymásra halmozva. Az összetett modellek túl kevés memória miatti hibát és a rendszer lelassulását okozhatják. Ajánlott egy nézet véglegesítése.

##XMSG: Message to indicate that the space is locked
PERFORMANCE_ANALYSIS_LOCKED_SPACE_ERROR=Nem végezhető teljesítményelemzés a(z) {0} nézethez, mert a(z) {1} tér zárolva van.
##XMSG: The performance analysis task was cancelled
PERFORMANCE_ANALYSIS_CANCELED=A teljesítményelemzés megszakadt a(z) {0} nézet esetében.
##XMSG: The performance analysis failed
PERFORMANCE_ANALYSIS_FAILED=A teljesítményelemzés nem sikerült.
##XMSG: The performance analysis is finished
PERFORMANCE_ANALYSIS_FINISHED=Befejeződött a teljesítményelemzés a(z) {0} nézetnél. Az eredmény megjelenítéséhez kattintson a Részletek megtekintése lehetőségre.
##XMSG: The view has input parameter with no default value
INPUT_PARAMETER_WITHOUT_DEFAULT_VALUE=Ez a nézet nem elemezhető, mert alapértelmezett érték nélküli paramétere van.
##XMSG: The view is not fully deployed
VIEW_NOT_FULLY_DEPLOYED=Ez a nézet nem elemezhető, mert nincs teljesen üzembe helyezve.
##XMSG: The view uses limited remote adapters
LIMITED_REMOTE_ADAPTER=Ez a nézet legalább egy olyan távoli adaptert használ, amelynek korlátozottak a képességei, például hiányzik az előszűrés vagy a Darabszám függvény támogatása. Az objektumok véglegesítésével vagy replikálásával javíthatja a futásidejű teljesítményt.
##XMSG: The view uses remote adapter that does not support 'Limit'
REMOTE_ADAPTER_NOT_SUPPORTING_LIMIT=Ez a nézet legalább egy olyan távoli adaptert használ, amely nem támogatja a Korlát beállítást. Előfordulhat, hogy 1000-nél több rekord van kiválasztva.
##XMSG: Performance analysis is executed by using default parameters of view'
INPUT_PARAMETER_WITH_DEFAULT_VALUE=A teljesítményelemzés a nézetparaméterek alapértelmezett értékeivel történik.
##XMSG: Message to indicate that an error occurred
PERFORMANCE_ANALYSIS_ERROR=Hiba történt a(z) {0} nézet teljesítményelemzésekor.
##XMSG: Message start of cancellation for Performance Analysis task
CANCEL_PERFORMANCE_ANALYSIS_TRIGGERED=Folyamat beküldve a(z) {0} teljesítményelemzési feladat ({1} nézet) leállítására. Előfordulhat, hogy a feladat leállítása valamivel később történik meg.
##XMSG: Message start to indicate that task is not active
CANCEL_PERFORMANCE_ANALYSIS_NO_ACTIVE_TASK=Nem aktív a(z) {0} teljesítményelemzési feladat a(z) {1} nézethez.
#XMSG: Cancel Performance Analysis task failure
CANCEL_PERFORMANCE_ANALYSIS_FAILED=Nem sikerült megszakítani a teljesítményelemzési feladatot.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Ütemezés magamhoz rendelése
#XBUT: Pause schedule menu label
pauseScheduleLabel=Ütemezés szüneteltetése
#XBUT: Resume schedule menu label
resumeScheduleLabel=Ütemezés folytatása
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Hiba történt az ütemezések eltávolításakor.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Hiba történt az ütemezések hozzárendelésekor.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Hiba történt az ütemezések szüneteltetésekor.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Hiba történt az ütemezések folytatásakor.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted={0} ütemezés törlése
#XMSG: Message for starting mass assign of schedules
massAssignStarted={0} ütemezés tulajdonosának módosítása
#XMSG: Message for starting mass pausing of schedules
massPauseStarted={0} ütemezés szüneteltetése
#XMSG: Message for starting mass resuming of schedules
massResumeStarted={0} ütemezés folytatása
#XBUT: Select Columns Button
selectColumnsBtn=Oszlopok kiválasztása
#XFLD: Refresh tooltip
TEXT_REFRESH=Frissítés
#XFLD: Select Columns tooltip
text_selectColumns=Oszlopok kiválasztása


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Runtime Metrics ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Label for Runtime Metrics
RuntimeMetricsTitle=Futásidejű mérőszámok:
#XFLD : Label for Run Button
runButton=Futtatás
#XFLD : Label for Cancel Button
cancelButton=Mégse
#XFLD : Label for Close Button
closeButton=Bezárás
#XFLD : Label for Open View Analyzer Button
openViewAnalyzerButton=Nézetelemző megnyitása
#XFLD : Label for Generate Explain Plan Button
generateExplainPlanButton=Tervmagyarázat generálása
#XFLD : Label for Previous Run Column
previousRun=Előző futás
#XFLD : Label for Latest Run Column
latestRun=Legutóbbi futás
#XFLD : Label for time Column
time=Idő
#XFLD : Label for Duration Column
duration=Időtartam
#XFLD : Label for Peak Memory Column
peakMemory=Memória-csúcsérték
#XFLD : Label for Number of Rows
numberOfRows=Sorok száma
#XFLD : Label for Overall Number of Sources
overallNumberOfSources=Források száma összesen
#XFLD : Label for Data Access Column
dataAccess=Adathozzáférés
#XFLD : Label for Local Tables
localTables=Helyi táblák
#XFLD : Label for federatedRemoteTables
federatedRemoteTables=Összevont távoli táblák (korlátozott adapterképességekkel)
#XTXT Text for initial state of the runtime metrics
initialState=Előbb teljesítményelemzést kell futtatnia, hogy megkapja a mérőszámokat. Ez sokáig tarthat, de szükség esetén megszakíthatja a folyamatot.
#XMSG: Cancel confirmation message
CancelRunConfirmationMessage=Biztosan megszakítja a teljesítményelemzés jelenlegi futását?
#XTIT: Cancel dialog title
CancelRunTitle=Futás megszakítása
#XFLD: Label for Number of Rows
NUMBER_ROWS=Sorok száma
#XFLD: Label for Overall Number of Sources
OVERALL_NUMBER_SOURCES=Források száma összesen
#XFLD: Label for Data Access
DATA_ACCESS=Adathozzáférés
#XFLD: Label for Remote tables
NUMBER_REMOTE_TABLES_LIMITED_ADAPTERS=Összevont távoli táblák (korlátozott adapterképességekkel)
#XFLD: Label for select statement
SELECT_STATEMENT='SELECT * FROM VIEW LIMIT 1000'
#XFLD: Label for duration
SELECT_RUNTIME=Időtartam
#XFLD: Label for peak memory
SELECT_PEAK_MEMORY=Memória-csúcsérték
#XFLD: Label for count statement
SELECT_COUNT_STATEMENT='SELECT COUNT(*) FROM VIEW'
#XFLD: Label for count duration
SELECT_COUNT_RUNTIME=Időtartam
#XFLD: Label for count peak memory
SELECT_COUNT_PEAK_MEMORY=Memória-csúcsérték
#XFLD: Label for count of number of local tables
NUMBER_LOCAL_TABLES_FILE=Helyi táblák (fájl)
#XTXT: Text for running state of the runtime metrics
Running=Fut...
#XFLD: Label for time
Time=Idő
#XFLD: Label for virtual access
PA_VIRTUAL=Virtuális
#XFLD: Label for persisted access
PA_PERSISTED=Véglegesítve
PA_PARTIALLY_PERSISTED=Részben véglegesítve
#XTXT: Text for cancel
CancelRunSuccessMessage=Teljesítményelemzés futásának megszakítása
#XTXT: Text for cancel error
CancelRunErrorMessage=Hiba történt a teljesítményelemzés futásának megszakításakor
#XTXT: Text for explain plan generation
ExplainPlanStarted=Tervmagyarázat generálása a(z) {0} nézethez
#XTXT: Text for performance analysis
PerformanceAnalysisStarted=Teljesítményelemzés indítása a(z) {0} nézethez.
#XTXT: Text for performance analysis error
ErrorFetchingPerformanceAnalysis=Hiba történt a teljesítményelemzési adatok lehívásakor.
#XTXT: Text for performance analysis error
conflictingTask=Már fut a teljesítményelemzési feladat
#XFLD: Label for Errors
Errors=hiba
#XFLD: Label for Warnings
Warnings=figyelmeztetés
#XFLD: Tooltip for missing privileges
openViewAnalyzerButtonTooltipNoPrivilege=A Nézetelemző megnyitásához DWC_DATAINTEGRATION(update) jogosultság szükséges.
#XFLD: Tooltip for missing privileges
generateExplainPlanButtonTooltipNoPrivilege=Tervmagyarázat generálásához DWC_RUNTIME(read) jogosultság szükséges.



#XFLD: Label for frequency column
everyLabel=Gyakoriság
#XFLD: Plural Recurrence text for Hour
hoursLabel=óra
#XFLD: Plural Recurrence text for Day
daysLabel=nap
#XFLD: Plural Recurrence text for Month
monthsLabel=hónap
#XFLD: Plural Recurrence text for Minutes
minutesLabel=perc
