
#XBCB: Breadcrumb
viewMonitorBreadcrumb={spaceId}

#XFLD: Label for persisted view column
SOURCE=Sursă
#XFLD: Label for persisted view column
NAME=Nume
#XFLD: Label for persisted view column
NAME_LABEL=Nume comercial
#XFLD: Label for persisted view column
NAME_LABELNew=Obiect (nume de afaceri)
#XFLD: Label for persisted view column
TECHINCAL_NAME=Nume tehnic
#XFLD: Label for persisted view column
TECHINCAL_NAMENew=Obiect (nume tehnic)
#XFLD: Label for persisted view column
DATA_PERSISTENCY=Acces la date
#XFLD: Label for persisted view column
STATUS=Stare
#XFLD: Label for persisted view column
LAST_UPDATED=Ultima actualizare
#XFLD: Label for persisted view column
IN_MEMORY_SIZE=Memorie utilizată pentru stocare (MiB)
#XFLD: Label for persisted view column
DISK_SIZE=Disc utilizat pentru stocare (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_NEWW=Spațiu în memorie internă (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_HEADER=Spațiu în memorie internă
#XFLD: Label for persisted view column
DISK_SIZE_NEWW=Spațiu pe disc (MiB)
#XFLD: Label for persisted view column
DISK_SIZE_HEADER=Spațiu pe disc
#XFLD: Label for schedule owner column
txtScheduleOwner=Proprietar programare
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Afișează cine a creat programarea
#XFLD: Label for persisted view column
PERSISTED=Salvate persistent
#XFLD: Label for persisted view column
TYPE=Tip
#XFLD: Label for View Selection Dialog column
changedOn=Modificat pe
#XFLD: Label for View Selection Dialog column
createdBy=Creat de
#XFLD: Label for log details column
txtViewPersistencyLogs=Vizualizare jurnale
#XFLD: Label for log details column
txtViewPersistencyLogsNew=Detalii
#XFLD: text for values shown for Ascending sort order
SortInAsc=Sortare în ordine ascendentă
#XFLD: text for values shown for Descending sort order
SortInDesc=Sortare în ordine descendentă
#XFLD: Label for persisted view monitor heading
ViewMonitor_Heading=Monitorizare imagini
#XFLD: Label for persisted view monitor sub-heading
ViewMonitor_SubHeading=Monitorizare și întreținere persistență date imagini


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ DynamicPageHeader ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: text for values shown in column Persistence Status
txtLoading=Încărcare
#XFLD: text for values shown in column Persistence Status
txtRunning=În execuție
#XFLD: text for values shown in column Persistence Status
txtAvailable=Disponibil
#XFLD: text for values shown in column Persistence Status
txtError=Eroare
#XMSG: Error message for unknown Persistence type that gets logged in Kibana
unknownReplicationTypeError=Tipul de replicare "{0}" nu este suportat.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Content ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Setări utilizate pentru ultima execuție de persistență date:
#XMSG: Message for input parameter name
inputParameterLabel=Parametru de intrare
#XMSG: Message for input parameter value
inputParameterValueLabel=Valoare
#XMSG: Message for persisted data
inputParameterPersistedLabel=Salvat persistent la
#XTIT: Count for View in ViewMonitor Toolbar
txtViewCount=Imagini ({0})
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Persistență imagine
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Persistență date
#XBUT: Button to clear the selected view persistance
ClearViewPersistance=Resetare
#XBUT: Button to stop the selected view persistance
stopPersistance=Oprire persistență
#XFLD: Placeholder for Search field
txtSearch=Căutare
#XBUT: Tooltip for refresh button
txtRefresh=Împrospătare
#XBUT: Tooltip for add view button
txtDeleteView=Ștergere persistență
#XBUT: Tooltip for load new peristence
loadNewPersistence=Relansare persistență
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Încărcare instantaneu nou
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Lansare persistență date
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Eliminare date salvate persistent
#XMSG: success message for starting persistence
startPersistenceSuccess=Salvăm persistent imaginea "{0}".
#XMSG: success message for stopping persistence
stopPersistenceSuccess=Eliminăm datele salvate persistent pentru imaginea "{0}".
#XMSG: success message for stopping persistence
deletePersistedViewSuccess=Eliminăm imaginea "{0}" din lista de monitorizare.
#XMSG: error message during start persistence of a view (backend error)
ERROR_STARTING_PERSISTENCE=A apărut o eroare la lansarea persistenței de date pentru imaginea "{0}".
#XMSG: error message during start persistence of a view (backend error)
PARAMERROR_STARTING_PERSISTENCE=Imaginea "{0}" nu poate fi salvată persistent deoarece conține parametri de intrare.
#XMSG: error message during start persistence of a view (backend error)
NUMBER_OF_PARAMETER_STARTING_PERSISTENCE=Imaginea "{0}" nu poate fi salvată persistent deoarece conține mai mult de un parametru de intrare.
#XMSG: error message during start persistence of a view (backend error)
PARAMETER_DEFAULT_VALUE_STARTING_PERSISTENCE=Imaginea "{0}" nu poate fi salvată persistent deoarece parametrul de intrare nu are valoare implicită.
#XMSG: error message during start persistence of a view (backend error)
REDEPLOYMENT_DAC_REQUIRED=Trebuie să implementați din nou Controlul de acces la date (DAC) "{0}" pentru a suporta persistența de date.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_STARTING_PERSISTENCE=Imaginea "{0}" nu poate fi salvată persistent deoarece utilizează imaginea"{1}", care are control de acces la date (DAC).
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_EXTERNAL_STARTING_PERSISTENCE=Imaginea "{0}" nu poate fi salvată persistent deoarece utilizează o imagine cu control de acces la date (DAC) care aparține unui spațiu diferit.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_STRUCTURE_STARTING_PERSISTENCE=Imaginea "{0}" nu poate fi salvată deoarece structura uneia sau a mai multor controale de acces la date (DAC) nu suportă persistența de date.
#XMSG: error message during stop persistence of a view (backend error)
STOP_PERSISTENCE_ERROR=A apărut o eroare la oprirea persistenței pentru imaginea "{0}".
#XMSG: error message during stop persistence of a view (backend error)
DELETE_PERSISTED_VIEW_ERROR=A apărut o eroare la ștergerea imaginii salvate persistent "{0}".
#XMSG: Warning message for removing the persisted data
RemovePersistency_Confirm_Msg=Doriți să ștergeți datele salvate persistent și să comutați la accesul virtual al imaginii "{0}"?
#XMSG: Warning message for delete the view persistence
DeletePersistency_Confirm_Msg=Doriți să eliminați imaginea din lista de monitorizare și să ștergeți datele salvate persistent ale imaginii "{0}"?
#XMSG: error message for reading data from backend
txtReadBackendError=Se pare că a apărut o eroare la citire din backend.
#XFLD: Label for No Data Error
NoDataError=Eroare
#XMSG: message for conflicting task
Task_Already_Running=O sarcină în conflict este deja în execuție pentru imaginea "{0}".

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Show All Views~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Text for show all views button
allViewsText=Toate imaginile ({0})
#XBUT: Text for show scheduled views button
scheduledText=Programate ({0})
#XBUT: Text for show persisted views button
persistedText=Salvate persistent ({0})
#XBUT: Text for start analyzer button
startAnalyzer=Lansare analizor de imagini
#XFLD: Message if repository is unavailable
repositoryErrorMsg=Registrul nu este disponibil și anumite caracteristici sunt dezactivate.

#XFLD: Data Access - Virtual
Virtual=Virtual
#XFLD: Data Access - Persisted
Persisted=Salvate persistent

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Non-Persisted View Selection Dialog~~~~~~~~~~~~~~~~~~~~~~~

#XTIT: Title for Non-Persisted View selection dialog
select_source=Selectare imagine de salvat persistent

#XTIT: Placeholder for Non-Persisted View selection dialog search
searchView=Imagini căutare
#XTIT: No data in the list of non-persisted view
No_Data=Fără date
#XBUT: Button to select non-persisted view
ok=Ok
#XBUT: Button to close the non-persisted views selection dialog
cancel=Anulare

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~View persistency log message key~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message for view persistency logs
START_STOP_PERSISTENCY=Lansare execuție sarcină de persistență date pentru "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY=Salvare persistentă date pentru imaginea "{1}".
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY)
START_VIEW_PERSISTENCY_PROCESS=Lansare proces de salvare persistentă date pentru imaginea "{0}".
#XFLD: Message for view persistency manual partition load
START_VIEW_PERSISTENCY_PROCESS_MANUAL_PARTITIONS=Lansare proces de salvare persistentă date pentru imaginea "{0}" cu ID-urile de partiție selectate: "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY=Se elimină datele salvate persistent pentru imaginea "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY)
REMOVE_VIEW_PERSISTENCY=Se lansează procesul de eliminare date salvate persistent pentru imaginea "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_SUCCESS=Date salvate persistent pentru imaginea "{1}".
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_SUCCESS)
START_VIEW_PERSISTENCY_PROCESS_SUCCESS=Date salvate persistent pentru imaginea "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY_SUCCESS=Datele salvate persistent au fost eliminate și accesul la date a fost restaurat pentru imaginea "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_SUCCESS)
REMOVE_VIEW_PERSISTENCY_SUCCESS=Proces de eliminare date salvate persistent a fost terminat pentru imaginea "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_FAILED=Imposibil de salvat persistent date pentru imaginea "{1}".
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_FAILED)
START_VIEW_PERSISTENCY_PROCESS_FAILED=Imposibil de salvat persistent date pentru imaginea "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.05)
STOP_VIEW_PERSISTENCY_FAILED=Imposibil de eliminat datele salvate persistent pentru imaginea "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_FAILED)
REMOVE_VIEW_PERSISTENCY_FAILED=Imposibil de eliminat datele salvate persistent pentru imaginea "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_SUCCESS="{3}" înregistrări au fost salvate persistent pentru imaginea "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace START_PERSISTENCY_RECORD_COUNT_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS={0} înregistrări inserate în tabelul de persistență de date pentru imaginea "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS_V2={0} înregistrări inserate în tabelul de persistență de date pentru imaginea "{1}". Memorie utilizată: {2} GiB.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_SUCCESS="{3}" înregistrări salvate persistent au fost eliminate pentru imaginea "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace STOP_PERSISTENCY_RECORD_COUNT_SUCCESS)
REMOVE_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=Date salvate persistent au fost eliminate, "{0}" înregistrări salvate persistent au fost șterse.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_FAILED=Obținere număr de înregistrări a eșuat pentru imaginea "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_FAILED=Obținere număr de înregistrări a eșuat pentru imaginea "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Programare ștearsă pentru "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS)
REMOVE_VIEW_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Programarea ștearsă pentru imaginea "{0}".
#XFLD: Message for view persistency logs
STOP_PERSISTENCY_DELETE_SCHEDULE_FAILED=Ștergere programare nereușită pentru "{1}".
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_REDEPLOY=Nu putem salva persistent imaginea "{0}" deoarece a fost modificată și implementată de când ați început să o salvați persistent. Încercați din nou să o salvați persistent sau așteptați până la următoarea execuție programată.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_DELETE=Nu putem salva persistent imaginea "{0}" deoarece a fost ștearsă de când ați început să o salvați persistent.
#XFLD: Message for successful data persistency of a single partition (for messages created before version 2022.03)
START_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} înregistrări salvate persistent în partiția pentru valori "{1}" <= {2} < "{3}".
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (new message to replace START_PERSISTENCY_SINGLE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} înregistrări inserate în partiția pentru valori "{1}" <= {2} < "{3}".
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS_V2={0} înregistrări inserate în partiția pentru valori "{1}" <= "{2}" < "{3}".. Memorie utilizată: {4} GiB.
#XFLD: Message for successful replication of a "others" partition (for messages created before version 2022.03)
START_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} înregistrări salvate persistent în partiția "altele".
#XFLD: Message used by SEAL repository for successful replication of a "others" partition (new message to replace START_PERSISTENCY_OTHERS_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} înregistrări inserate în partiția "altele".
#XFLD: Message for view persistency record count of partitions (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={3} înregistrări salvate persistent pentru imaginea "{1}" în {4} partiții.
#XFLD: Message used by SEAL repository for view persistency record count of partitions (new message to replace START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={0} înregistrări inserate în tabelul de persistență de date pentru imaginea "{1}" din {2} partiții.
#XFLD: Message used by SEAL repository for view persistency for partition wise refresh
START_VIEW_PERSISTENCY_RECORD_COUNT_REFRESHED_PARTITIONS_SUCCESS={0} înregistrări au fost inserate în tabelul de persistență de date pentru imaginea "{1}". Partiții actualizate: {2}; Partiții blocate: {3}; Total partiții: {4}
#XFLD: Message used by SEAL repository for view persistency for manual partition load
START_VIEW_PERSISTENCY_RECORD_COUNT_MANUAL_PARTITIONS_SUCCESS={0} înregistrări inserate în tabelul de persistență de date pentru imaginea "{1}" din {2} partiții selectate
#XFLD: Message used by SEAL repository for view persistency for locked partition data load
START_VIEW_PERSISTENCY_RECORD_COUNT_LOCKED_PARTITIONS_SUCCESS={0} înregistrări au fost inserate în tabelul de persistență de date pentru imaginea "{1}". Partiții actualizate: {2}; Partiții blocate. nemodificate: {3}; Total partiții: {4}
#XMSG: Message for unexpected error (for messages created before version 2022.03)
START_PERSISTENCY_UNEXPECTED_ERROR=A apărut o eroare neprevăzută la salvarea persistentă a datelor pentru imaginea "{0}".
#XMSG: Message for unexpected error (new message to replace START_PERSISTENCY_UNEXPECTED_ERROR)
START_VIEW_PERSISTENCY_UNEXPECTED_ERROR=A apărut o eroare neprevăzută la salvarea persistentă a datelor pentru imaginea "{0}".
#XMSG: Message for Locked space error
START_VIEW_PERSISTENCY_LOCKED_SPACE_ERROR=Imaginea "{0}” nu poate fi salvată persistent deoarece spațiul "{1}" este blocat.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
STOP_PERSISTENCY_UNEXPECTED_ERROR=A apărut o eroare neprevăzută la eliminarea datelor persistente pentru imaginea "{0}".
#XMSG: Message for unexpected error (new message to replace STOP_PERSISTENCY_UNEXPECTED_ERROR)
REMOVE_VIEW_PERSISTENCY_UNEXPECTED_ERROR=A apărut o eroare neprevăzută la eliminarea persistenței pentru imaginea "{0}".
#XMSG: Message for ivalidated view
VIEW_PERSISTENCY_ABORTED_DEFINITION_INVALID=Definiția pentru imaginea "{0}" a devenit nevalabilă, cel mai probabil din cauza unei modificări a unui obiect consumat direct sau indirect de către imagine. Încercați să reimplementați imaginea pentru a rezolva problema sau încercați să identificați cauza rădăcină.
#XMSG: Message for persisted data removal (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT=Datele salvate persistent sunt eliminate la implementarea imaginii "{0}".
#XMSG: Message for persisted data removal of a consuming view
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW=Datele salvate persistent sunt eliminate la implementarea imaginii consumate "{0}".
#XMSG: Message for persisted data removal of a consuming view due to a dac change
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW_DAC_CHANGED=Datele salvate persistent sunt eliminate la implementarea imaginii consumate "{0}", deoarece controlul de acces la date a fost modificat.
#XMSG: Message for persisted data removal (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DEPLOYMENT=Datele salvate persistent sunt eliminate la implementarea imaginii "{0}".
#XMSG: Message for deleted view (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION=Persistența este eliminată cu ștergerea imaginii "{0}".
#XMSG: Message for deleted view (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DELETION=Persistența este eliminată cu ștergerea imaginii "{0}".
#XMSG Message for removal of persisted data, because prerequisites not fulfilled
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_PREREQUISITES_NOT_FULFILLED=Datele salvate persistent sunt eliminate deoarece condițiile preliminare de persistență a datelor nu mai sunt îndeplinite.
#XMSG: Message for inconsistent persistency
VIEW_PERSISTENCY_INCONSISTENT_PERSISTENCY=Persistența imaginii "{0}" a devenit inconsistentă. Eliminați datele salvate persistent pentru a rezolva problema.
#XMSG: Message for prerequisites
START_PERSISTENCY_CHECK_PREREQUISITES=Verificare condiții preliminare pentru persistența imaginii "{0}".
#XMSG: Message for DAC deployed in deprecated way using table function and join (for messages created before 2025.05)
START_PERSISTENCY_DEPRECIATED_DAC=Imaginea "{0}" este implementată utilizând controlul accesului la date (DAC), care este în curs de perimare. Implementați imaginea din nou pentru a îmbunătăți performanța.
#XMSG: Message for DAC deployed in deprecated way using table function and join
START_PERSISTENCY_DEPRECATED_DAC=Imaginea "{0}" este implementată utilizând controlul accesului la date (DAC), care este în curs de perimare. Implementați imaginea din nou pentru a îmbunătăți performanța.
#XMSG: Message for roll back (for messages created before version 2022.03)
ROLLBACK_VIEW_PERSISTENCY=A apărut o eroare. Starea anterioară a persistenței a fost restaurată pentru imaginea "{0}".
#XMSG: Message used by SEAL repository for rollback of running procedure to persist view (new message to replace ROLLBACK_VIEW_PERSISTENCY)
VIEW_PERSISTENCY_ROLLBACK=A apărut o eroare. Procesul de persistență al imaginii ''{0}'' a fost oprit și modificările au fost aduse la starea anterioară.
#XMSG: Message used by SEAL repository for rollback of running procedure to remove persisted data of view
REMOVE_VIEW_PERSISTENCY_ROLLBACK=A apărut o eroare. Procesul de eliminare a datelor salvate persistent pentru imaginea ''{0}'' a fost oprit și modificările au fost aduse la starea anterioară.
#XMSG: Message used by SEAL repository for persistency preparation
START_PERSISTENCY_PREPARATION=Pregătire de salvare persistentă date.
#XMSG: Message used by SEAL repository for data insertion
START_INSERTING_PERSISTENCY_DATA=Inserare date în tabel de persistență.
#XMSG: Message for null values - partitioning (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS={0} înregistrări cu valoare nulă inserate în partiția "altele".
#XMSG: Message used by SEAL repository for others partition values - partitioning (new message to replace START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS={0} înregistrări inserate în partiția "altele" pentru valorile "{2}" < "{1}" OR "{2}" >= "{3}".
#XMSG: Message used by SEAL repository for others partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS_V2={0} înregistrări inserate în partiția "altele" pentru valorile "{2}" < "{1}" OR "{2}" >= "{3}". Memorie utilizată: {4} GiB.
#XMSG: Message used by SEAL repository for null partition values - partitioning
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS={0} înregistrări inserate în partiția "altele" pentru valorile "{1}" IS NULL.
#XMSG: Message used by SEAL repository for null partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS_V2={0} înregistrări inserate în partiția "altele" pentru valorile "{1}" IS NULL. Memorie utilizată: {2} GiB.
##XFLD:Message used by SEAL repository for remote statistics
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS=Se încarcă datele implicate: {0} instrucțiuni la distanță. Total înregistrări obținute: {1}. Durată totală: {2} secunde.
##XFLD:Message used by SEAL repository for remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS_WITH_PARTITIONS=Se încarcă datele implicate utilizând {0} partiții cu {1} instrucțiuni la distanță. Total înregistrări obținute: {2}. Durată totală: {3} secunde.
##XFLD:Message used by SEAL repository with UI ID for instructions of remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_INSTRUCTION=Instrucțiunile la distanță prelucrate la execuție pot fi afișate deschizând monitorul de query-uri la distanță în detaliile mesajelor specifice pentru partiție.
##XFLD:Message for start reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS=Se începe procesul de reutilizare a datelor salvate existente pentru imaginea {0} după implementare.
##XFLD:Message used by SEAL repository for reattach view persitency
START_VIEW_PERSISTENCY_REATTACHMENT=Începeți să reutilizați datele salvate existente.
##XFLD:Message used by SEAL repository for reattach view persitency
START_FINILIZING_VIEW_PERSISTENCY_REATTACHMENT=Reutilizarea datelor salvae existente.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_COMPLETE=Procesul de reutilizare a datelor salvate existente este finalizat pentru imaginea {0}.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_FAILED=Reutilizare nereușită a datelor salvate existente pentru imaginea {0} după implementare.
#XMSG: Message used by SEAL repository for finalizing persistency
START_FINALIZING_PERSISTENCY=Finalizare persistență.
#XMSG: Message used by SEAL repository for restoring virtual access for view
REMOVE_VIEW_PERSISTENCY_VIRTUAL_ACCESS_RESTORED=Acces la date virtuale este restaurat pentru imaginea „{0}”.
#XMSG: Message used by SEAL repository for view that already has virtual access
REMOVE_VIEW_PERSISTENCY_ALREADY_VIRTUAL_ACCESS=Imaginea "{0}" are deja acces virtual la date. Nu sunt eliminate date salvate persistent.
#XMSG: Message for removing view persistency
VIEW_PERSISTENCY_REMOVED_FROM_MONITOR=Imaginea "{0}" a fost eliminată din Monitorul de imagini.
#XMSG: Message for view doesnt exist.
VIEW_PERSISTENCY_VIEW_NOT_EXISTING=Imaginea "{0}" nu există în baza de date sau nu este implementată corect și, în consecință, nu poate fi salvată persistent. Încercați să reimplementați imaginea pentru a rezolva problema sau pentru a identifica cauza principală.
#XMSG: Message for persistency not enabled.
VIEW_PERSISTENCY_NOT_ENABLED=Persistența datelor nu este activată. Reimplementați un tabel/o imagine în spațiul "{0}" pentru a activa funcționalitatea.
#XMSG: Message for unexpected error due to node crash
VIEW_PERSISTENCY_UNEXPECTED_ERROR_GENERAL=Ultima execuție de persistență a imaginii a fost întreruptă din cauza erorilor tehnice.
#XFLD: Message for view persistency memory consumption
VIEW_PERSISTENCY_MEMORY_CONSUMPTION={0} GiB de memorie maximă utilizată în timpul de execuție al persistenței imaginii.
#XFLD: Message for view persistency timeout
VIEW_PERSISTENCY_TIMEOUT=Persistență imagine {0} a atins depășirea de timp de {1} (de) ore.
#XFLD: Message for not started asynchronous view persistency
VIEW_PERSISTENCY_ASYNC_NOT_STARTED=Încărcarea ridicată a sistemului a împiedicat începerea execuției asincrone a persistenței imaginii. Verificați dacă prea multe sarcini sunt executate în paralel.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency (for messages created before version 2022.05)
START_VIEW_PERSISTENCY_EXECUTION_METHOD_DROP_AND_RENAME=Tabelul existent salvat persistent a fost șters și înlocuit cu un tabel salvat persistent nou.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_RECREATE_AND_DROP=Tabelul existent salvat persistent a fost șters și înlocuit cu un tabel salvat persistent nou.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_TRUNCATE_AND_COPY=Tabelul existent salvat persistent a fost actualizat cu noile date.
##XFLD: Message for missing authorizations for view persistency
VIEW_PERSISTENCY_MISSING_AUTH=Autorizații lipsă pentru persistență date.
##XFLD: Message start of persistency cancellation
VIEW_PERSISTENCY_TRIGGER_CANCEL=Lansare anulare proces pentru salvare persistentă imagine {0}.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_UNAVAILABLE_TASK_RUN=Eroare la anularea procesului de salvare persistentă a imaginii deoarece nu există nicio sarcină de salvare persistentă date în execuție pentru imaginea {0}.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_NONDETERMINISTIC_TASK_RUN=Eroare la anularea procesului de salvare persistentă a imaginii deoarece nicio sarcină de salvare persistentă date nu este în execuție pentru imaginea {0}.
##XFLD: Message for canceling a non running persitency task
VIEW_PERSISTENCY_CANCEL_NON_RUNNING_SELECTED_TASK=Eroare la anularea procesului de salvare persistentă a imaginii {0} deoarece sarcina de persistență date selectată {1} nu este în execuție.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_UNREACHABLE_PERSISTENCY_RUN=Eroare la anularea procesului de salvare persistentă a imaginii deoarece salvarea persistentă a datelor pentru imaginea {0} nu a fost încă lansată.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_COMPLETED_PERSISTENCY_RUN=Eroare la anularea procesului de salvare persistentă a imaginii {0} deoarece a fost deja terminat.
##XFLD: Message for failed cancellation
VIEW_PERSISTENCY_ABORTED_CANCEL_EXECUTION=Eroare la anularea procesului pentru salvarea persistentă a imaginii {0}.
##XFLD: Message for finalizing persistency cancellation
VIEW_PERSISTENCY_FINALIZE_CANCEL=Procesul de oprire a salvării persistente a datelor imaginii {0} a fost transmis.
##XFLD: Message used in persist task when cancellation is caught
VIEW_PERSISTENCY_CANCELED=Procesul de salvare persistentă a imaginii {0} a fost oprit.
##XFLD: Message used in persist task when cancelation is caught and performed through a cancelation task
VIEW_PERSISTENCY_CANCELED_VIA_CANCEL_TASK=Procesul de salvare persistentă a imaginii {0} a fost oprit prin sarcina de anulare {1}.
##XFLD: Message used in cancel task when the task is triggered by deployment
VIEW_PERSISTENCY_CANCEL_TRIGGERED_BY_DEPLOYMENT=Se anulează procesul de salvare persistentă a datelor la implementarea imaginii {0}.
##XFLD: Message used in case the cancelation is retriggered for a running task
VIEW_PERSISTENCY_CANCEL_ATTEMPT_RETRIGGER_CANCEL=O sarcină anterioară de anulare a salvării persistente pentru imaginea {0} a fost deja transmisă.
##XFLD: Message used to express the delay that may occur before the persistency task is stopped
VIEW_PERSISTENCY_CANCEL_DELAY_STOPPING_PERSISTENCY=Poate exista o întârziere până când sarcina de persistență date pentru imaginea {0} este oprită.
##XFLD: Message used to reflect the task log identifier of the current running persistency task
VIEW_PERSISTENCY_CANCEL_CURRENT_RUNNING_TASK=Datele pentru imaginea {0} sunt salvate persistent cu sarcina {1}.
##XFLD:Message used by SEAL repository for locked partitions and DACs
VIEW_PERSISTENCY_LOCKED_PARTITIONS_DACS=Este posibil ca autorizațiile furnizate de DAC-uri să fi fost modificate și să nu fie luate în considerare de partițiile blocate. Deblocați partițiile și încărcați un instantaneu nou pentru a aplica modificările.
##XFLD:Message used by SEAL repository for column structure change
VIEW_PERSISTENCY_COLUMN_STRUCTURE_CHANGE=Structura coloanei a fost modificată și nu mai corespunde cu tabelul de persistență existent. Eliminați datele salvate persistent și lansați o salvare persistentă de date nouă pentru a actualiza tabelul dvs. de persistență.
##XFLD: Message for out of memory
VIEW_PERSISTENCY_HANA_OUT_OF_MEMORY=Sarcina a eșuat din cauza unei erori de memorie insuficientă în baza de date SAP HANA.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_EXCEPTION_TRANSACTION_ROLLBACK=Sarcina a eșuat din cauza unei excepții interne în baza de date SAP HANA.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_SQL_EXECUTION_ISSUE=Sarcina a eșuat din cauza unei probleme interne de execuție SQL în baza de date SAP HANA.
##XFLD: Message for HANA out of memory result
VIEW_PERSISTENCY_OUT_OF_MEMORY_RESULT_MESSAGE=Motiv eveniment de memorie insuficientă HANA: {0}
##XFLD: Message for admission control rejection
VIEW_PERSISTENCY_HANA_ADMISSION_CTRL_REJECTION=Sarcina a eșuat din cauza unei respingeri control de admitere SAP HANA.
##XFLD: Message for HANA back pressure / rate limit of HANA client
VIEW_PERSISTENCY_HANA_CLIENT_RATE_LIMIT_REJECTION=Sarcina a eșuat din cauza unui număr prea mare de conexiuni SAP HANA active.
#XMSG: Message for invalid persisted table name
VIEW_PERSISTENCY_PERSISTED_TABLE_INVALID=A apărut o eroare și tabelul salvat persistent a devenit nevalabil. Pentru a rezolva problema, eliminați datele salvate persistent și salvați persistent imaginea din nou.
#XMSG: Message for usahe of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION=Această imagine nu poate fi salvată persistent. Utilizează un tabel la distanță bazat pe o sursă la distanță cu propagare de utilizator activată. Verificați originea imaginii.
#XMSG: Message for dynamic usage of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION_DYNAMIC=Această imagine nu poate fi salvată persistent. Utilizează un tabel la distanță bazat pe o sursă la distanță cu propagare de utilizator activată. Tabelul la distanță poate fi consumat dinamic printr-o imagine script SQL. Este posibil ca originea imaginii să nu afișeze tabelul la distanță.
#XMSG: Message for insufficient privileges or consumption of view with DAC via dynamic SQL
VIEW_PERSISTENCY_INSUF_PRIV_OR_DAC_DYN_SQL=Este posibil ca privilegiile dvs. să fie insuficiente. Deschideți previzualizarea datelor pentru a vedea dacă aveți privilegiile necesare. Dacă da, este posibil ca o imagine secundară consumată prin script SQL dinamic să aibă aplicat control de acces la date (DAC).
#XMSG: deployment of DAC is deprecated and view need to be redeployed to be able to persist data
VIEW_PERSISTENCY_DEPRECATED_DAC=Imaginea "{0}" este implementată utilizând controlul accesului la date (DAC), care este în curs de perimare. Implementați imaginea din nou pentru a putea salva persistent datele pentru imagine.
##XFLD:Message used by SEAL repository for input parameter
START_VIEW_PERSISTENCY_INPUT_PARAMETER=Se utilizează valoarea implicită "{0}" pentru parametrul de intrare "{1}".
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Elastic compute node keys~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
##XFLD:Message used to reflect disablement of ecn replica
ECN_DISABLE_EXISTING_REPLICA=Replica nodului de calcul elastic este dezactivată.
##XFLD:Message used to reflect recreation of ecn replica
ECN_RECREATE_EXISTING_REPLICA=Replica nodului de calcul elastic este recreată.
##XFLD:Message used to reflect re-enablement of ecn replica
ECN_ENABLE_EXISTING_REPLICA=Replica nodului de calcul elastic este reactivată.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Task Schedule Menu key~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message to schedule task
scheduleText=Programare
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Creare programare
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Editare programare
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Ștergere programare
#XFLD: Refresh frequency field
refreshFrequency=Frecvență de împrospătare
#XFLD: Refresh frequency field
refreshFrequencyNew=Frecvență
#XFLD: Refresh frequency field
refreshFrequencyNewNew=Frecvență programată
#XBUT: label for None
none=Niciunul
#XBUT: label for Real-Time replication state
realtime=În timp real
#XFLD: Label for table column
txtNextSchedule=Execuția următoare
#XFLD: Label for table column
txtNextScheduleNew=Următoarea execuție programată
#XFLD: Label for table column
txtNumOfRecords=Număr de înregistrări
#XFLD: Label for scheduled link
scheduledTxt=Programat
#XFLD: LABEL for partially persisted link
partiallyPersisted=Parțial salvat persistent
#XFLD: Text for paused text
paused=Întrerupt

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ View Monitor Tasklog Details Action keys~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message for release lock actionn
releaseLockMsg=Dacă o execuție durează mai mult decât de obicei, acest lucru poate indica o eroare și că starea nu a fost actualizată în consecință. \r\n Pentru a rezolva problema, puteți elibera blocarea și seta starea la nereușită.
#XFLD: Label for release lock dialog
releaseLockText=Eliberare blocare

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Column header tooltip ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: tooltip for table column
txtViewTooltip=Numele imaginii salvate persistent
#XFLD: tooltip for table column
txtViewDataAccessTooltip=Aceasta indică disponibilitatea imaginii
#XFLD: tooltip for table column
txtViewRefreshFreqTooltip=Aceasta indică dacă o programare este definită pentru imagine
#XFLD: tooltip for table column
txtViewStatusTooltip=Obțineți stare imagine salvată persistent
#XFLD: tooltip for table column
txtViewLatestUpdateTooltip=Oferă informații despre ultima dată de actualizare a imaginii salvate persistent
#XFLD: tooltip for table column
txtViewNextRunTooltip=Dacă pentru imagine este setat o programare, vedeți când este programată următoarea execuție.
#XFLD: tooltip for table column
txtViewNumOfRecordsTooltip=Urmăriți numărul de înregistrări.
#XFLD: tooltip for table column
txtViewInMemorySizeTooltip=Urmăriți dimensiunea din memoria dvs. pe care o utilizează imaginea
#XFLD: tooltip for table column
txtViewDiskSizeTooltip=Urmăriți dimensiunea de pe discul dvs. pe care o ocupă imaginea
#XMSG: Expired text
txtExpired=Expirat

#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Obiectul "{0}" nu poate fi adăugat la lanț de sarcini.

#~~~~~~~~~~~View Analyzer task log messages~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message indicating number of records for a view, and memory consumption in Mebibyte during simulation of view persistency
RECORDS_PEAK_MEMORY=Imaginea "{0}" are {1} înregistrări. O simulare a persistenței imaginii a utilizat {2} MiB de memorie.
#XMSG: View Analyzer execution failure
VIEW_ANALYZER_EXECUTION_FAILED=Execuție analizor de imagini nereușită.
#XMSG: Missing authorizations for View Analyzer
VIEW_ANALYZER_MISSING_AUTH=Autorizații lipsă pentru analizor de imagini.
#XMSG: Maximum memory in Gibibyte is reached, therefore persistency simulation is stopped
MAXIMUM_MEMORY_REACHED=Memoria maximă de {0} GiB a fost atinsă la simularea persistenței de date pentru imaginea "{1}". Prin urmare, nu vor mai fi executate simulări de persistență de date.
#XMSG: Error during persistency simulation occurred
PERSISTENCY_SIMULATION_ERROR=A apărut o eroare la simularea persistenței de date pentru imaginea "{0}".
#XMSG: Persistency simulation is not executed, because prerequisites are not fulfilled and the view cannot be persisted
PREREQUISITES_NOT_FULFILLED=Simularea persistenței de date nu este executată pentru imaginea "{0}", deoarece condițiile preliminare nu sunt îndeplinite și imaginea nu poate fi salvată persistent.
#XMSG: Persistency simulation is not executed, because the view does not exist in the database
VIEW_DOES_NOT_EXIST=Trebuie să implementați imaginea "{0}" pentru a activa simularea persistenței de date.
#XMSG: Number of records cannot be determined, because the local table does not exist in the database
TABLE_DOES_NOT_EXIST=Tabelul local "{0}"  nu există în baza de date, în consecință numărul de înregistrări nu poate fi determinat pentru acest tabel.
##XMSG: Message start of cancellation for View Analyzer task
CANCEL_VIEW_ANALYZER_TRIGGERED=Procesul de oprire a sarcinii analizorului de imagini {0} pentru imaginea "{1}" a fost transmis. Poate exista o întârziere până la oprirea sarcinii.
##XMSG: Message start to indicate that task is not active
CANCEL_VIEW_ANALYZER_NO_ACTIVE_TASK=Sarcina analizorului de imagini {0} pentru imaginea "{1}" nu este activă.
#XMSG: Cancel View Analyzer task failure
CANCEL_VIEW_ANALYZER_FAILED=Eroare la anularea sarcinii analizorului de imagini.
##XMSG: Message used in View Analyzer execution task when task is canceled
VIEW_ANALYZER_CANCELED=Execuția analizorului de imagini pentru imaginea "{0}" a fost oprită printr-o sarcină de anulare.
##XMSG: Message start of cancelation for Model Validation task
CANCEL_MODEL_VALIDATION_TRIGGERED=Procesul de oprire a sarcinii de validare a modelului {0} pentru imaginea "{1}" a fost transmis. Poate exista o întârziere până la oprirea sarcinii.
##XMSG: Message to indicate that task is not active
CANCEL_MODEL_VALIDATION_NO_ACTIVE_TASK=Sarcina de validare model {0} pentru imaginea "{1}" nu este activă.
#XMSG: Cancel Model Validation task failure
CANCEL_MODEL_VALIDATION_FAILED=Eroare la anulare sarcină de validare model.
##XMSG: Message used in Model Validation task when task is canceled
MODEL_VALIDATION_CANCELED=Execuția validării modelului pentru imaginea "{0}" a fost oprită printr-o sarcină de anulare.
#XMSG: Message for Locked space error, before starting Model Validation task
MODEL_VALIDATION_LOCKED_SPACE_ERROR=Imposibil de executat validare model pentru imaginea "{0}", deoarece spațiul "{1}" este blocat.
##XMSG: Message used in View Analyzer execution task when determination of number of rows fails
NUMBER_ROWS_DETERMINATION_FAILED=A apărut o eroare la determinarea numărului de linii pentru tabelul local "{0}".
##XMSG: Message to indicate that SQL plan was created
EXECUTION_PLAN_CREATED=Fișierul de planificare a analizorului SQL pentru imaginea "{0}" a fost creat și poate fi descărcat.
#XFLD: Message to indicate starting of process to generate SQL Analyzer plan file
START_PLANVIZ_EXECUTION=Se lansează procesul de generare a fișierului de planificare analizorului SQL pentru imaginea "{0}".
#XFLD: Message to indicate starting of View Analyzer process
VIEW_ANALYZER_EXECUTION_STARTED=Lansarea execuției Analizor de imagini.
##XMSG: Message to indicate that SQL plan file cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_PLANVIZ_NOT_FULFILLED=Fișierul de planificare a analizorului SQL nu poate fi generat pentru imaginea "{0}", deoarece condițiile preliminare ale persistenței de date nu sunt îndeplinite.
##XMSG: Error message during  generation of SQL Analyzer plan file
PLANVIZ_ERROR=A apărut o eroare la generarea fișierului de planificare analizorului SQL pntru imaginea "{0}".
#XMSG: Message for Locked space error
VIEW_ANALYZER_LOCKED_SPACE_ERROR=Imposibil de executat analizor de imagini pentru imaginea "{0}", deoarece spațiul "{1}" este blocat.
#XMSG: Message for persistency simulation without partitioning
PERSISTENCY_SIMULATION_WITHOUT_PARTITIONS=Partițiile imaginii "{0}" nu sunt luate în considerare la simularea persistenței de date.
#XMSG: Message for generation of SQL Analyzer plan file without considering partitioning
PLANVIZ_WITHOUT_PARTITIONS=Partițiile imaginii "{0}" nu sunt luate în considerare la generarea fișierului de planificare analizorului SQL.
#XMSG: Confirmation message for mass removal of persisted data
MassRemovePersistency_Confirm_Msg=Doriți să eliminați datele salvate persistent și să comutați accesul la date înapoi la acces virtual?
#XMSG: Confirmation message for partial mass removal of selected persisted data
PartialMassRemovePersistency_Confirm_Msg={0} din cele {1} imagini selectate au date salvate persistent. \n Doriți să eliminați datele salvate persistent și să comutați accesul la date înapoi la acces virtual?
#XMSG: success message for stopping mass persistence
stopMassPersistenceSuccess=Eliminăm datele înregistrate persistent pentru imaginile selectate.
#XMSG: error message during mass stop persistence of a view (backend error)
STOP_MASS_PERSISTENCE_ERROR=A apărut o eroare la oprirea persistenței pentru imaginile selectate.
#XMSG: memory analysis is only performed in space {0}
NO_MEMORY_ANALYSIS=Analiza memoriei este efectuată doar pentru entitățile din spațiul "{0}": "{1}" "{2}" a fost omis.
##XMSG: Message to indicate that Explain Plan cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_EXPLAINPLAN_NOT_FULFILLED=Fișierul planului de explicare nu poate fi generat pentru imaginea "{0}", deoarece condițiile preliminare ale persistenței nu sunt îndeplinite.
#XMSG: Message for Explain Plan generation without partitioning
EXPLAINPLAN_WITHOUT_PARTITIONS=Partițiile imaginii "{0}" nu sunt luate în considerare la generarea fișierului de plan de explicare.
#XFLD: Message to indicate starting of process to generate Explain Plan file
START_EXPLAINPLAN_EXECUTION=Se lansează procesul de generare a fișierului de plan de explicare pentru imaginea "{0}".
##XMSG: Message to indicate that Explain Plan was created
EXPLAINPLAN_CREATED=A fost generat fișierul planului de explicare pentru imaginea "{0}". Îl puteți afișa efectuând click pe "Vizualizare detalii" sau descărcându-l dacă aveți permisiunea relevantă.
##XMSG: Error message during  generation of Explain Plan file
EXPLAINPLAN_ERROR=A apărut o eroare la generarea fișierului de plan de explicare pentru imaginea "{0}".
##XMSG: Explain Plan file cannot be generated for the view {0}, because too many views are stacked on each other
EXPLAINPLAN_MODEL_TOO_COMPLEX=Fișierul planului de explicare nu poate fi generat pentru imaginea „{0}”. Sunt stivuite prea multe imagini. Modelele complexe pot cauza erori de memorie insuficientă și performanță lentă. Este recomandată salvarea persistentă a unei imagini.

##XMSG: Message to indicate that the space is locked
PERFORMANCE_ANALYSIS_LOCKED_SPACE_ERROR=Imposibil de executat analiză de performanță pentru imaginea „{0}”, deoarece spațiul „{1}” este blocat.
##XMSG: The performance analysis task was cancelled
PERFORMANCE_ANALYSIS_CANCELED=Analiza de performanță pentru imaginea „{0}” a fost anulată.
##XMSG: The performance analysis failed
PERFORMANCE_ANALYSIS_FAILED=Analiză de performanță nereușită.
##XMSG: The performance analysis is finished
PERFORMANCE_ANALYSIS_FINISHED=Analiza de performanță pentru imaginea „{0}” a fost finalizată. Afișați rezultatul efectuând click pe „Detalii imagine”.
##XMSG: The view has input parameter with no default value
INPUT_PARAMETER_WITHOUT_DEFAULT_VALUE=Această imagine nu poate fi analizată deoarece are un parametru fără valoare implicită.
##XMSG: The view is not fully deployed
VIEW_NOT_FULLY_DEPLOYED=Această imagine nu poate fi analizată deoarece nu este complet implementată.
##XMSG: The view uses limited remote adapters
LIMITED_REMOTE_ADAPTER=Această imagine utilizează cel puțin un adaptor la distanță cu capacități limitate precum pushdown de filtru lipsă sau suport pentru „Numărare”. Salvarea persistentă sau replicarea obiectelor poate îmbunătăți performanța timpului de execuție.
##XMSG: The view uses remote adapter that does not support 'Limit'
REMOTE_ADAPTER_NOT_SUPPORTING_LIMIT=Această imagine utilizează cel puțin un adaptor la distanță care nu suportă „Limitare”. Este posibil să fi fost selectate mai mult de 1000 de înregistrări.
##XMSG: Performance analysis is executed by using default parameters of view'
INPUT_PARAMETER_WITH_DEFAULT_VALUE=Analiza de performanță este executată utilizând valorile implicite ale parametrilor de imagine.
##XMSG: Message to indicate that an error occurred
PERFORMANCE_ANALYSIS_ERROR=A apărut o eroare la efectuarea analizei de performanță pentru imaginea „{0}”.
##XMSG: Message start of cancellation for Performance Analysis task
CANCEL_PERFORMANCE_ANALYSIS_TRIGGERED=Procesul de oprire a sarcinii analizei de performanță {0} pentru imaginea „{1}” a fost transmis. Poate exista o întârziere până la oprirea sarcinii.
##XMSG: Message start to indicate that task is not active
CANCEL_PERFORMANCE_ANALYSIS_NO_ACTIVE_TASK=Sarcina analizei de performanță {0} pentru imaginea „{1}” nu este activă.
#XMSG: Cancel Performance Analysis task failure
CANCEL_PERFORMANCE_ANALYSIS_FAILED=Eroare la anularea sarcinii analizei de performanță.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Alocare programare la mine
#XBUT: Pause schedule menu label
pauseScheduleLabel=Suspendare programare
#XBUT: Resume schedule menu label
resumeScheduleLabel=Reluare programare
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=A apărut o eroare la eliminarea programărilor.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=A apărut o eroare la alocarea programărilor.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=A apărut o eroare la suspendarea programărilor.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=A apărut o eroare la reluarea programărilor.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Ștergeți {0} programări
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Modificați proprietarul pentru {0} programări
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Se suspendă {0} programări
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Se reiau {0} programări
#XBUT: Select Columns Button
selectColumnsBtn=Selectare coloane
#XFLD: Refresh tooltip
TEXT_REFRESH=Împrospătare
#XFLD: Select Columns tooltip
text_selectColumns=Selectare coloane


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Runtime Metrics ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Label for Runtime Metrics
RuntimeMetricsTitle=Metrică pentru timp de execuție pentru
#XFLD : Label for Run Button
runButton=Executare
#XFLD : Label for Cancel Button
cancelButton=Anulare
#XFLD : Label for Close Button
closeButton=Închidere
#XFLD : Label for Open View Analyzer Button
openViewAnalyzerButton=Deschidere analizor de imagini
#XFLD : Label for Generate Explain Plan Button
generateExplainPlanButton=Generare plan explicativ
#XFLD : Label for Previous Run Column
previousRun=Execuție anterioară
#XFLD : Label for Latest Run Column
latestRun=Ultima execuție
#XFLD : Label for time Column
time=Timp
#XFLD : Label for Duration Column
duration=Durată
#XFLD : Label for Peak Memory Column
peakMemory=Memorie de vârf
#XFLD : Label for Number of Rows
numberOfRows=Număr de linii
#XFLD : Label for Overall Number of Sources
overallNumberOfSources=Număr global de surse
#XFLD : Label for Data Access Column
dataAccess=Acces la date
#XFLD : Label for Local Tables
localTables=Tabele locale
#XFLD : Label for federatedRemoteTables
federatedRemoteTables=Tabele la distanță federalizate (cu capabilități de adaptor limitate)
#XTXT Text for initial state of the runtime metrics
initialState=Întâi trebuie să executați analiza de performanță pentru a obține metrica. Acest lucru poate fi de durată, dar puteți anula procesul, dacă este necesar.
#XMSG: Cancel confirmation message
CancelRunConfirmationMessage=Sigur doriți să anulați execuția curentă a analizei de performanță?
#XTIT: Cancel dialog title
CancelRunTitle=Anulare execuție
#XFLD: Label for Number of Rows
NUMBER_ROWS=Număr de linii
#XFLD: Label for Overall Number of Sources
OVERALL_NUMBER_SOURCES=Număr global de surse
#XFLD: Label for Data Access
DATA_ACCESS=Acces la date
#XFLD: Label for Remote tables
NUMBER_REMOTE_TABLES_LIMITED_ADAPTERS=Tabele la distanță federalizate (cu capabilități de adaptor limitate)
#XFLD: Label for select statement
SELECT_STATEMENT='SELECT * FROM VIEW LIMIT 1000'
#XFLD: Label for duration
SELECT_RUNTIME=Durată
#XFLD: Label for peak memory
SELECT_PEAK_MEMORY=Memorie de vârf
#XFLD: Label for count statement
SELECT_COUNT_STATEMENT='SELECT COUNT(*) FROM VIEW'
#XFLD: Label for count duration
SELECT_COUNT_RUNTIME=Durată
#XFLD: Label for count peak memory
SELECT_COUNT_PEAK_MEMORY=Memorie de vârf
#XFLD: Label for count of number of local tables
NUMBER_LOCAL_TABLES_FILE=Tabele locale (fișier)
#XTXT: Text for running state of the runtime metrics
Running=În execuție...
#XFLD: Label for time
Time=Timp
#XFLD: Label for virtual access
PA_VIRTUAL=Virtual
#XFLD: Label for persisted access
PA_PERSISTED=Salvat persistent
PA_PARTIALLY_PERSISTED=Parțial salvat persistent
#XTXT: Text for cancel
CancelRunSuccessMessage=Se anulează execuția analizei de performanță.
#XTXT: Text for cancel error
CancelRunErrorMessage=A apărut o eroare la anularea execuției analizei de performanță.
#XTXT: Text for explain plan generation
ExplainPlanStarted=Se generează planul explicativ pentru imaginea „{0}”.
#XTXT: Text for performance analysis
PerformanceAnalysisStarted=Începe analiza de performanță pentru imaginea „{0}”.
#XTXT: Text for performance analysis error
ErrorFetchingPerformanceAnalysis=A apărut o eroare la obținerea datelor analizei de performanță.
#XTXT: Text for performance analysis error
conflictingTask=Sarcina analizei de performanță este deja în execuție
#XFLD: Label for Errors
Errors=Eroare/erori
#XFLD: Label for Warnings
Warnings=Avertizare/avertizări
#XFLD: Tooltip for missing privileges
openViewAnalyzerButtonTooltipNoPrivilege=Aveți nevoie de privilegiul DWC_DATAINTEGRATION(update) pentru a deschide analizorul de imagini.
#XFLD: Tooltip for missing privileges
generateExplainPlanButtonTooltipNoPrivilege=Aveți nevoie de privilegiul DWC_RUNTIME(read) pentru a genera planul explicativ.



#XFLD: Label for frequency column
everyLabel=La fiecare
#XFLD: Plural Recurrence text for Hour
hoursLabel=Ore
#XFLD: Plural Recurrence text for Day
daysLabel=Zile
#XFLD: Plural Recurrence text for Month
monthsLabel=Luni
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minute
