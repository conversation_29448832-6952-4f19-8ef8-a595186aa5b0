
#XBCB: Breadcrumb
viewMonitorBreadcrumb={spaceId}

#XFLD: Label for persisted view column
SOURCE=Quelle
#XFLD: Label for persisted view column
NAME=Name
#XFLD: Label for persisted view column
NAME_LABEL=Betriebswirtschaftlicher Name
#XFLD: Label for persisted view column
NAME_LABELNew=Objekt (betriebswirtschaftlicher Name)
#XFLD: Label for persisted view column
TECHINCAL_NAME=Technischer Name
#XFLD: Label for persisted view column
TECHINCAL_NAMENew=Objekt (technischer Name)
#XFLD: Label for persisted view column
DATA_PERSISTENCY=Datenzugriff
#XFLD: Label for persisted view column
STATUS=Status
#XFLD: Label for persisted view column
LAST_UPDATED=Zuletzt aktualisiert
#XFLD: Label for persisted view column
IN_MEMORY_SIZE=Für Speicherung verwendeter Arbeitsspeicher (MiB)
#XFLD: Label for persisted view column
DISK_SIZE=Für Speicherung verwendeter Festplattenspeicher (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_NEWW=Größe des In-Memory-Speichers (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_HEADER=Größe des In-Memory-Speichers
#XFLD: Label for persisted view column
DISK_SIZE_NEWW=Größe auf Datenträger (MiB)
#XFLD: Label for persisted view column
DISK_SIZE_HEADER=Größe auf Datenträger
#XFLD: Label for schedule owner column
txtScheduleOwner=Eigentümer des Zeitplans
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Zeigt an, wer den Zeitplan angelegt hat.
#XFLD: Label for persisted view column
PERSISTED=Persistiert
#XFLD: Label for persisted view column
TYPE=Typ
#XFLD: Label for View Selection Dialog column
changedOn=Geändert am
#XFLD: Label for View Selection Dialog column
createdBy=Angelegt von
#XFLD: Label for log details column
txtViewPersistencyLogs=Protokolle anzeigen
#XFLD: Label for log details column
txtViewPersistencyLogsNew=Details
#XFLD: text for values shown for Ascending sort order
SortInAsc=Aufsteigend sortieren
#XFLD: text for values shown for Descending sort order
SortInDesc=Absteigend sortieren
#XFLD: Label for persisted view monitor heading
ViewMonitor_Heading=View-Monitor
#XFLD: Label for persisted view monitor sub-heading
ViewMonitor_SubHeading=Überwachung und Verwaltung der Datenpersistenz von Views


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ DynamicPageHeader ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: text for values shown in column Persistence Status
txtLoading=Wird geladen
#XFLD: text for values shown in column Persistence Status
txtRunning=Wird ausgeführt
#XFLD: text for values shown in column Persistence Status
txtAvailable=Verfügbar
#XFLD: text for values shown in column Persistence Status
txtError=Fehler
#XMSG: Error message for unknown Persistence type that gets logged in Kibana
unknownReplicationTypeError=Replikationstyp "{0}" wird nicht unterstützt.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Content ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Verwendete Einstellungen für den letzten Datenpersistenzlauf:
#XMSG: Message for input parameter name
inputParameterLabel=Eingabeparameter
#XMSG: Message for input parameter value
inputParameterValueLabel=Wert
#XMSG: Message for persisted data
inputParameterPersistedLabel=Persistiert um
#XTIT: Count for View in ViewMonitor Toolbar
txtViewCount=Views ({0})
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=View-Persistenz
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Datenpersistenz
#XBUT: Button to clear the selected view persistance
ClearViewPersistance=Löschen
#XBUT: Button to stop the selected view persistance
stopPersistance=Persistenz beenden
#XFLD: Placeholder for Search field
txtSearch=Suchen
#XBUT: Tooltip for refresh button
txtRefresh=Aktualisieren
#XBUT: Tooltip for add view button
txtDeleteView=Persistenz löschen
#XBUT: Tooltip for load new peristence
loadNewPersistence=Persistenz erneut starten
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Neuen Snapshot laden
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Datenpersistenz starten
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Persistierte Daten entfernen
#XMSG: success message for starting persistence
startPersistenceSuccess=Persistenz von View "{0}" wird durchgeführt.
#XMSG: success message for stopping persistence
stopPersistenceSuccess=Persistierte Daten für View "{0}" werden entfernt.
#XMSG: success message for stopping persistence
deletePersistedViewSuccess=View "{0}" wird aus der Überwachungsliste entfernt.
#XMSG: error message during start persistence of a view (backend error)
ERROR_STARTING_PERSISTENCE=Beim Starten der Datenpersistenz für View "{0}" ist ein Fehler aufgetreten.
#XMSG: error message during start persistence of a view (backend error)
PARAMERROR_STARTING_PERSISTENCE=Der View "{0}" kann nicht persistiert werden, da er Eingabeparameter aufweist.
#XMSG: error message during start persistence of a view (backend error)
NUMBER_OF_PARAMETER_STARTING_PERSISTENCE=Der View "{0}" kann nicht persistiert werden, da er mehr als einen Eingabeparameter aufweist.
#XMSG: error message during start persistence of a view (backend error)
PARAMETER_DEFAULT_VALUE_STARTING_PERSISTENCE=Der View "{0}" kann nicht persistiert werden, da der Eingabeparameter keinen Standardwert aufweist.
#XMSG: error message during start persistence of a view (backend error)
REDEPLOYMENT_DAC_REQUIRED=Sie müssen die Datenzugriffskontrolle "{0}" erneut aktivieren, um die Datenpersistenz zu unterstützen.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_STARTING_PERSISTENCE=Der View "{0}" kann nicht persistiert werden, da er den View "{1}" verwendet, der Datenzugriffskontrolle aufweist.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_EXTERNAL_STARTING_PERSISTENCE=Der View "{0}" kann nicht persistiert werden, da er einen View mit Datenzugriffskontrolle verwendet, der zu einem anderen Space gehört.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_STRUCTURE_STARTING_PERSISTENCE=Der View "{0}" kann nicht persistiert werden, da die Struktur von mindestens einer der zugehörigen Datenzugriffskontrollen keine Datenpersistenz unterstützt.
#XMSG: error message during stop persistence of a view (backend error)
STOP_PERSISTENCE_ERROR=Beim Beenden der Persistenz für View "{0}" ist ein Fehler aufgetreten.
#XMSG: error message during stop persistence of a view (backend error)
DELETE_PERSISTED_VIEW_ERROR=Beim Löschen des persistierten View "{0}" ist ein Fehler aufgetreten.
#XMSG: Warning message for removing the persisted data
RemovePersistency_Confirm_Msg=Möchten Sie die persistierten Daten löschen und zum virtuellen Zugriff von View "{0}" wechseln?
#XMSG: Warning message for delete the view persistence
DeletePersistency_Confirm_Msg=Möchten Sie den View aus der Überwachungsliste entfernen und die persistierten Daten von View "{0}" löschen?
#XMSG: error message for reading data from backend
txtReadBackendError=Offensichtlich ist beim Lesen des Backend ein Fehler aufgetreten.
#XFLD: Label for No Data Error
NoDataError=Fehler
#XMSG: message for conflicting task
Task_Already_Running=Für den View "{0}" wird bereits eine in Konflikt stehende Aufgabe ausgeführt.

#XFLD: Error while accessing pertitioning for a view
missingPrivilegeInViewPersistency=Nicht ausreichende Berechtigung zur Ausführung der Partitionierung für View "{0}"

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Show All Views~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Text for show all views button
allViewsText=Alle Views ({0})
#XBUT: Text for show scheduled views button
scheduledText=Eingeplant ({0})
#XBUT: Text for show persisted views button
persistedText=Persistiert ({0})
#XBUT: Text for start analyzer button
startAnalyzer=View Analyzer starten
#XFLD: Message if repository is unavailable
repositoryErrorMsg=Das Repository ist nicht verfügbar, und bestimmte Funktionen sind deaktiviert.

#XFLD: Data Access - Virtual
Virtual=Virtuell
#XFLD: Data Access - Persisted
Persisted=Persistiert

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Non-Persisted View Selection Dialog~~~~~~~~~~~~~~~~~~~~~~~

#XTIT: Title for Non-Persisted View selection dialog
select_source=Zu persistierenden View auswählen

#XTIT: Placeholder for Non-Persisted View selection dialog search
searchView=Views suchen
#XTIT: No data in the list of non-persisted view
No_Data=Keine Daten
#XBUT: Button to select non-persisted view
ok=OK
#XBUT: Button to close the non-persisted views selection dialog
cancel=Abbrechen

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~View persistency log message key~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message for view persistency logs
START_STOP_PERSISTENCY=Ausführung der Datenpersistenzaufgabe für "{1}" wird gestartet.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY=Daten für den View "{1}" werden persistiert.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY)
START_VIEW_PERSISTENCY_PROCESS=Der Prozess zum Persistieren von Daten für den View "{0}" wird gestartet.
#XFLD: Message for view persistency manual partition load
START_VIEW_PERSISTENCY_PROCESS_MANUAL_PARTITIONS=Der Prozess zum Persistieren von Daten für den View "{0}" mit ausgewählten Partitions-IDs wird gestartet: "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY=Persistierte Daten für den View "{1}" werden entfernt.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY)
REMOVE_VIEW_PERSISTENCY=Der Prozess zum Entfernen von persistierten Daten für View "{0}" wird gestartet.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_SUCCESS=Daten für den View "{1}" wurden persistiert.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_SUCCESS)
START_VIEW_PERSISTENCY_PROCESS_SUCCESS=Daten für den View "{0}" wurden persistiert.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY_SUCCESS=Persistierte Daten wurden entfernt, und virtueller Datenzugriff für den View "{1}" wurde wiederhergestellt.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_SUCCESS)
REMOVE_VIEW_PERSISTENCY_SUCCESS=Der Prozess zum Entfernen von persistierten Daten für View "{0}" ist abgeschlossen.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_FAILED=Daten für den View "{1}" konnten nicht persistiert werden.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_FAILED)
START_VIEW_PERSISTENCY_PROCESS_FAILED=Daten für den View "{0}" konnten nicht persistiert werden.
#XFLD: Message for view persistency logs (for messages created before version 2022.05)
STOP_VIEW_PERSISTENCY_FAILED=Persistierte Daten für den View "{1}" konnten nicht entfernt werden.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_FAILED)
REMOVE_VIEW_PERSISTENCY_FAILED=Persistierte Daten für den View "{0}" konnten nicht entfernt werden.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_SUCCESS="{3}" Datensätze für den View "{1}" persistiert.
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace START_PERSISTENCY_RECORD_COUNT_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS={0} Datensätze in Datenpersistenztabelle für den View "{1}" eingefügt.
#XFLD: Message used by SEAL repository for view persistency logs (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS_V2={0} Datensätze in Datenpersistenztabelle für den View "{1}" eingefügt. Verwendeter Speicher: {2} GiB.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_SUCCESS="{3}" persistierte Datensätze für den View "{1}" entfernt.
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace STOP_PERSISTENCY_RECORD_COUNT_SUCCESS)
REMOVE_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=Persistierte Daten entfernt, "{0}" persistierte Datensätze gelöscht.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_FAILED=Abrufen von recordCount fehlgeschlagen für den View "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_FAILED=Abrufen von recordCount fehlgeschlagen für den View "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Zeitplan gelöscht für "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS)
REMOVE_VIEW_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Zeitplan gelöscht für den View "{0}.
#XFLD: Message for view persistency logs
STOP_PERSISTENCY_DELETE_SCHEDULE_FAILED=Löschen des Zeitplans für "{1}" fehlgeschlagen.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_REDEPLOY=Der View "{0}" kann nicht persistiert werden, da er geändert und aktiviert wurde, seit Sie mit dem Persistieren begonnen haben. Versuchen Sie, den View erneut zu persistieren, oder warten Sie auf den nächsten eingeplanten Lauf.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_DELETE=Der View "{0}" kann nicht persistiert werden, da er gelöscht wurde, seit Sie mit dem Persistieren begonnen haben.
#XFLD: Message for successful data persistency of a single partition (for messages created before version 2022.03)
START_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} Datensätze in Partition für Werte "{1}" <= {2} < "{3}" persistiert.
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (new message to replace START_PERSISTENCY_SINGLE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} Datensätze in Partition für Werte "{1}" <= {2} < "{3}" eingefügt.
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS_V2={0} Datensätze in Partition für Werte "{1}" <= "{2}" < "{3}" eingefügt. Verwendeter Speicher: {4} GiB.
#XFLD: Message for successful replication of a "others" partition (for messages created before version 2022.03)
START_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} Datensätze in Partition "Sonstige" persistiert.
#XFLD: Message used by SEAL repository for successful replication of a "others" partition (new message to replace START_PERSISTENCY_OTHERS_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} Datensätze in Partition "Sonstige" eingefügt.
#XFLD: Message for view persistency record count of partitions (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={3} Datensätze für den View "{1}" in {4} Partitionen persistiert.
#XFLD: Message used by SEAL repository for view persistency record count of partitions (new message to replace START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={0} Datensätze in Datenpersistenztabelle für den View "{1}" in {2} Partitionen eingefügt.
#XFLD: Message used by SEAL repository for view persistency for partition wise refresh
START_VIEW_PERSISTENCY_RECORD_COUNT_REFRESHED_PARTITIONS_SUCCESS={0} Datensätze in Datenpersistenztabelle für den View "{1}" eingefügt. Aktualisierte Partitionen: {2}; gesperrte Partitionen: {3}; Partitionen insgesamt: {4}
#XFLD: Message used by SEAL repository for view persistency for manual partition load
START_VIEW_PERSISTENCY_RECORD_COUNT_MANUAL_PARTITIONS_SUCCESS={0} Datensätze in Datenpersistenztabelle für den View "{1}" in {2} ausgewählten Partitionen eingefügt.
#XFLD: Message used by SEAL repository for view persistency for locked partition data load
START_VIEW_PERSISTENCY_RECORD_COUNT_LOCKED_PARTITIONS_SUCCESS={0} Datensätze in Datenpersistenztabelle für den View "{1}" eingefügt. Aktualisierte Partitionen: {2}; unveränderte Partitionen: {3}; Partitionen insgesamt: {4}
#XMSG: Message for unexpected error (for messages created before version 2022.03)
START_PERSISTENCY_UNEXPECTED_ERROR=Beim Persistieren von Daten für den View "{0}" ist ein unerwarteter Fehler aufgetreten.
#XMSG: Message for unexpected error (new message to replace START_PERSISTENCY_UNEXPECTED_ERROR)
START_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Beim Persistieren von Daten für den View "{0}" ist ein unerwarteter Fehler aufgetreten.
#XMSG: Message for Locked space error
START_VIEW_PERSISTENCY_LOCKED_SPACE_ERROR=Der View "{0}" kann nicht persistiert werden, da der Space "{1}" gesperrt ist.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
STOP_PERSISTENCY_UNEXPECTED_ERROR=Beim Entfernen von persistierten Daten für den View "{0}" ist ein unerwarteter Fehler aufgetreten.
#XMSG: Message for unexpected error (new message to replace STOP_PERSISTENCY_UNEXPECTED_ERROR)
REMOVE_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Beim Entfernen der Persistenz für den View "{0}" ist ein unerwarteter Fehler aufgetreten.
#XMSG: Message for ivalidated view
VIEW_PERSISTENCY_ABORTED_DEFINITION_INVALID=Die Definition des View "{0}" ist ungültig, höchstwahrscheinlich aufgrund von Änderungen an einem Objekt, das direkt oder indirekt durch den View verwendet wird. Versuchen Sie, den View erneut zu aktivieren, um das Problem zu beheben, oder die Ursache zu finden.
#XMSG: Message for persisted data removal (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT=Persistierte Daten werden entfernt, während der View "{0}" aktiviert wird.
#XMSG: Message for persisted data removal of a consuming view
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW=Persistierte Daten werden entfernt, während der verwendete View "{0}" aktiviert wird.
#XMSG: Message for persisted data removal of a consuming view due to a dac change
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW_DAC_CHANGED=Persistierte Daten werden entfernt, während der verwendete View "{0}" aktiviert wird, da sich die zugehörige Datenzugriffskontrolle geändert hat.
#XMSG: Message for persisted data removal (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DEPLOYMENT=Persistierte Daten werden entfernt, während der View "{0}" aktiviert wird.
#XMSG: Message for deleted view (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION=Persistenz wird beim Löschen von View "{0}" entfernt.
#XMSG: Message for deleted view (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DELETION=Persistenz wird beim Löschen von View "{0}" entfernt.
#XMSG Message for removal of persisted data, because prerequisites not fulfilled
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_PREREQUISITES_NOT_FULFILLED=Persistierte Daten werden entfernt, da die Voraussetzungen für die Datenpersistenz nicht mehr erfüllt sind.
#XMSG: Message for inconsistent persistency
VIEW_PERSISTENCY_INCONSISTENT_PERSISTENCY=Die Persistenz des View "{0}" ist nicht mehr konsistent. Entfernen Sie die persistierten Daten, um das Problem zu beheben.
#XMSG: Message for prerequisites
START_PERSISTENCY_CHECK_PREREQUISITES=Die Voraussetzungen zum Persistieren des View "{0}" werden geprüft.
#XMSG: Message for DAC deployed in deprecated way using table function and join (for messages created before 2025.05)
START_PERSISTENCY_DEPRECIATED_DAC=Der View "{0}" wurde mit Datenzugriffskontrolle aktiviert, die abgekündigt ist. Aktivieren Sie den View erneut, um die Performance zu verbessern.
#XMSG: Message for DAC deployed in deprecated way using table function and join
START_PERSISTENCY_DEPRECATED_DAC=Der View "{0}" wurde mit Datenzugriffskontrolle aktiviert, die abgekündigt ist. Aktivieren Sie den View erneut, um die Performance zu verbessern.
#XMSG: Message for roll back (for messages created before version 2022.03)
ROLLBACK_VIEW_PERSISTENCY=Es ist ein Fehler aufgetreten. Der vorherige Zustand der Persistenz für View "{0}" wird wiederhergestellt.
#XMSG: Message used by SEAL repository for rollback of running procedure to persist view (new message to replace ROLLBACK_VIEW_PERSISTENCY)
VIEW_PERSISTENCY_ROLLBACK=Es ist ein Fehler aufgetreten. Der Prozess zum Persistieren von View "{0}" wurde beendet, und die Änderungen wurden zurückgenommen.
#XMSG: Message used by SEAL repository for rollback of running procedure to remove persisted data of view
REMOVE_VIEW_PERSISTENCY_ROLLBACK=Es ist ein Fehler aufgetreten. Der Prozess zum Entfernen von persistierten Daten für View "{0}" wurde beendet, und die Änderungen wurden zurückgenommen.
#XMSG: Message used by SEAL repository for persistency preparation
START_PERSISTENCY_PREPARATION=Das Persistieren der Daten wird vorbereitet.
#XMSG: Message used by SEAL repository for data insertion
START_INSERTING_PERSISTENCY_DATA=Daten werden in der Persistenztabelle eingefügt.
#XMSG: Message for null values - partitioning (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS=Es wurden {0} Datensätze mit Nullwerten in der Partition "Sonstige" eingefügt.
#XMSG: Message used by SEAL repository for others partition values - partitioning (new message to replace START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS={0} Datensätze in der Partition "Sonstige" für Werte "{2}" < "{1}" ODER "{2}" >= "{3}" eingefügt.
#XMSG: Message used by SEAL repository for others partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS_V2={0} Datensätze in Partition "Sonstige" für Werte "{2}" < "{1}" ODER "{2}" >= "{3}" eingefügt. Verwendeter Speicher: {4} GiB.
#XMSG: Message used by SEAL repository for null partition values - partitioning
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS={0} Datensätze in der Partition "Sonstige" für Werte "{1}" IST NULL eingefügt.
#XMSG: Message used by SEAL repository for null partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS_V2={0} Datensätze in der Partition "Sonstige" für Werte "{1}" IST NULL eingefügt. Verwendeter Speicher: {2} GiB.
##XFLD:Message used by SEAL repository for remote statistics
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS=Betroffene Daten werden geladen: {0} Remote-Anweisungen. Abgerufene Datensätze insgesamt: {1}. Dauer insgesamt: {2} Sekunden.
##XFLD:Message used by SEAL repository for remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS_WITH_PARTITIONS=Betroffene Daten werden aus {0} Partitionen mit {1} Remote-Anweisungen geladen. Abgerufene Datensätze insgesamt: {2}. Dauer insgesamt: {3} Sekunden.
##XFLD:Message used by SEAL repository with UI ID for instructions of remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_INSTRUCTION=Die während des Laufs verarbeiteten Remote-Anweisungen können über den Remote-Abfrage-Monitor in den Details der partitionsspezifischen Meldungen angezeigt werden.
##XFLD:Message for start reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS=Prozess wird gestartet, um persistierte Daten für View {0} nach der Aktivierung erneut zu verwenden.
##XFLD:Message used by SEAL repository for reattach view persitency
START_VIEW_PERSISTENCY_REATTACHMENT=Starten, um die bereits vorhandenen persistierten Daten erneut zu verwenden.
##XFLD:Message used by SEAL repository for reattach view persitency
START_FINILIZING_VIEW_PERSISTENCY_REATTACHMENT=Die bereits vorhandenen persistierten Daten werden erneut verwendet.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_COMPLETE=Der Prozess zum erneuen Verwenden bereits vorhandener persistierter Daten für View {0} ist abgeschlossen.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_FAILED=Die bereits vorhandenen persistierten Daten für View {0} konnten nach der Aktivierung nicht erneut verwendet werden.
#XMSG: Message used by SEAL repository for finalizing persistency
START_FINALIZING_PERSISTENCY=Persistenz wird finalisiert.
#XMSG: Message used by SEAL repository for restoring virtual access for view
REMOVE_VIEW_PERSISTENCY_VIRTUAL_ACCESS_RESTORED=Der virtuelle Datenzugriff für den View "{0}" wurde wiederhergestellt.
#XMSG: Message used by SEAL repository for view that already has virtual access
REMOVE_VIEW_PERSISTENCY_ALREADY_VIRTUAL_ACCESS=Der View "{0}" besitzt bereits virtuellen Datenzugriff. Es werden keine persistierten Daten entfernt.
#XMSG: Message for removing view persistency
VIEW_PERSISTENCY_REMOVED_FROM_MONITOR=Der View "{0}" wurde aus dem View-Monitor entfernt.
#XMSG: Message for view doesnt exist.
VIEW_PERSISTENCY_VIEW_NOT_EXISTING=Der View "{0}" ist in der Datenbank nicht vorhanden oder wurde nicht ordnungsgemäß aktiviert und kann daher nicht persistiert werden. Versuchen Sie, den View erneut zu aktivieren, um das Problem zu beheben oder die Ursache zu ermitteln.
#XMSG: Message for persistency not enabled.
VIEW_PERSISTENCY_NOT_ENABLED=Datenpersistenz ist nicht aktiviert. Aktivieren Sie erneut eine Tabelle/einen View im Space "{0}", um die Funktion zu aktivieren.
#XMSG: Message for unexpected error due to node crash
VIEW_PERSISTENCY_UNEXPECTED_ERROR_GENERAL=Letzter View-Persistenzlauf wurde aufgrund von technischen Fehlern unterbrochen.
#XFLD: Message for view persistency memory consumption
VIEW_PERSISTENCY_MEMORY_CONSUMPTION={0} GiB maximale Speicherauslastung während der View-Persistenzlaufzeit verwendet.
#XFLD: Message for view persistency timeout
VIEW_PERSISTENCY_TIMEOUT=Persistenz von View {0} hat den Tmeout von {1} Stunden erreicht.
#XFLD: Message for not started asynchronous view persistency
VIEW_PERSISTENCY_ASYNC_NOT_STARTED=Asynchrone Ausführung der View-Persistenz konnte aufgrund hoher Systemauslastung nicht gestartet werden. Prüfen Sie, ob zu viele Aufgaben parallel ausgeführt werden.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency (for messages created before version 2022.05)
START_VIEW_PERSISTENCY_EXECUTION_METHOD_DROP_AND_RENAME=Vorhandene persistierte Tabelle wurde gelöscht und durch eine neue persistierte Tabelle ersetzt.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_RECREATE_AND_DROP=Vorhandene persistierte Tabelle wurde gelöscht und durch eine neue persistierte Tabelle ersetzt.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_TRUNCATE_AND_COPY=Vorhandene persistierte Tabelle wurde mit neuen Daten aktualisiert.
##XFLD: Message for missing authorizations for view persistency
VIEW_PERSISTENCY_MISSING_AUTH=Fehlende Berechtigungen für Datenpersistenz.
##XFLD: Message start of persistency cancellation
VIEW_PERSISTENCY_TRIGGER_CANCEL=Prozess zum Persistieren von View {0} wird abgebrochen.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_UNAVAILABLE_TASK_RUN=Abbrechen des Prozesses zum Persistieren des View ist fehlgeschlagen, da es keine laufende Datenpersistenzaufgabe für den View {0} gibt.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_NONDETERMINISTIC_TASK_RUN=Abbrechen des Prozesses zum Persistieren des View ist fehlgeschlagen, da keine Datenpersistenzaufgabe für den View {0} ausgeführt wird.
##XFLD: Message for canceling a non running persitency task
VIEW_PERSISTENCY_CANCEL_NON_RUNNING_SELECTED_TASK=Abbrechen des Prozesses zum Persistieren des View {0} ist fehlgeschlagen, da die ausgewählte Datenpersistenzaufgabe {1} nicht ausgeführt wird.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_UNREACHABLE_PERSISTENCY_RUN=Abbrechen des Prozesses zum Persistieren des View ist fehlgeschlagen, da die Datenpersistenz für den View {0} noch nicht begonnen hat.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_COMPLETED_PERSISTENCY_RUN=Abbrechen des Prozesses zum Persistieren von View {0} ist fehlgeschlagen, da er bereits abgeschlossen wurde.
##XFLD: Message for failed cancellation
VIEW_PERSISTENCY_ABORTED_CANCEL_EXECUTION=Abbrechen des Prozesses zum Persistieren von View {0} ist fehlgeschlagen.
##XFLD: Message for finalizing persistency cancellation
VIEW_PERSISTENCY_FINALIZE_CANCEL=Prozess zum Beenden der Datenpersistenz von View {0} wurde übergeben.
##XFLD: Message used in persist task when cancellation is caught
VIEW_PERSISTENCY_CANCELED=Prozess zum Persistieren von View {0} wurde beendet.
##XFLD: Message used in persist task when cancelation is caught and performed through a cancelation task
VIEW_PERSISTENCY_CANCELED_VIA_CANCEL_TASK=Prozess zum Persistieren von View {0} wurde über die Aufgabe zum Abbrechen {1} beendet.
##XFLD: Message used in cancel task when the task is triggered by deployment
VIEW_PERSISTENCY_CANCEL_TRIGGERED_BY_DEPLOYMENT=Prozess zum Persistieren von Daten beim Aktivieren von View {0} wird abgebrochen.
##XFLD: Message used in case the cancelation is retriggered for a running task
VIEW_PERSISTENCY_CANCEL_ATTEMPT_RETRIGGER_CANCEL=Eine vorherige Aufgabe zum Abbrechen der Persistenz von View {0} wurde bereits übergeben.
##XFLD: Message used to express the delay that may occur before the persistency task is stopped
VIEW_PERSISTENCY_CANCEL_DELAY_STOPPING_PERSISTENCY=Es kann zu einer Verzögerung kommen, bis die Datenpersistenzaufgabe für den View {0} beendet wird.
##XFLD: Message used to reflect the task log identifier of the current running persistency task
VIEW_PERSISTENCY_CANCEL_CURRENT_RUNNING_TASK=Daten für View {0} werden mit der Aufgabe {1} persistiert.
##XFLD:Message used by SEAL repository for locked partitions and DACs
VIEW_PERSISTENCY_LOCKED_PARTITIONS_DACS=Durch Datenzugriffskontrollen bereitgestellte Berechtigungen wurden eventuell geändert und werden nicht durch gesperrte Partitionen berücksichtigt. Entsperren Sie die Partitionen und laden Sie einen neuen Snapshot, um die Änderungen zu übernehmen.
##XFLD:Message used by SEAL repository for column structure change
VIEW_PERSISTENCY_COLUMN_STRUCTURE_CHANGE=Die Struktur der Spalte hat sich geändert und stimmt nicht mehr mit der bestehenden Persistenztabelle überein. Entfernen Sie die persistierten Daten und starten Sie eine neue Datenpersistenz, um Ihre Persistenztabelle zu aktualisieren.
##XFLD: Message for out of memory
VIEW_PERSISTENCY_HANA_OUT_OF_MEMORY=Die Aufgabe ist aufgrund unzureichenden Speichers in der SAP-HANA-Datenbank fehlgeschlagen.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_EXCEPTION_TRANSACTION_ROLLBACK=Die Aufgabe ist aufgrund einer internen Ausnahme in der SAP-HANA-Datenbank fehlgeschlagen.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_SQL_EXECUTION_ISSUE=Die Aufgabe ist aufgrund eines internen SQL-Ausführungsproblems in der SAP-HANA-Datenbank fehlgeschlagen.
##XFLD: Message for HANA out of memory result
VIEW_PERSISTENCY_OUT_OF_MEMORY_RESULT_MESSAGE=Ursache für unzureichenden Speicher in SAP HANA: {0}
##XFLD: Message for admission control rejection
VIEW_PERSISTENCY_HANA_ADMISSION_CTRL_REJECTION=Die Aufgabe ist aufgrund einer Ablehnung der SAP-HANA-Zugangssteuerung fehlgeschlagen.
##XFLD: Message for HANA back pressure / rate limit of HANA client
VIEW_PERSISTENCY_HANA_CLIENT_RATE_LIMIT_REJECTION=Die Aufgabe ist aufgrund zu vieler aktiver SAP-HANA-Verbindungen fehlgeschlagen.
#XMSG: Message for invalid persisted table name
VIEW_PERSISTENCY_PERSISTED_TABLE_INVALID=Es ist ein Fehler aufgetreten und die persistierte Tabelle ist ungültig geworden. Um das Problem zu lösen, entfernen Sie die persistierten Daten und persistieren Sie den View erneut.
#XMSG: Message for usahe of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION=Der View kann nicht persistiert werden. Er verwendet eine Remote-Tabelle, die auf einer Remote-Quelle mit aktivierter Benutzerpropagierung basiert. Prüfen Sie die Herkunft des View.
#XMSG: Message for dynamic usage of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION_DYNAMIC=Der View kann nicht persistiert werden. Er verwendet eine Remote-Tabelle, die auf einer Remote-Quelle mit aktivierter Benutzerpropagierung basiert. Die Remote-Tabelle kann dynamisch über einen SQL-Skript-View verwendet werden. Der View zeigt die Remote-Tabelle möglicherweise nicht an.
#XMSG: Message for insufficient privileges or consumption of view with DAC via dynamic SQL
VIEW_PERSISTENCY_INSUF_PRIV_OR_DAC_DYN_SQL=Ihre Berechtigungen sind möglicherweise unzureichend. Öffnen Sie die Datenvorschau, um zu sehen, ob Sie die erforderlichen Berechtigungen haben. Wenn dies der Fall ist, wurde auf einen zweiten View, der über ein dynamisches SQL-Skript verwendet wird, möglicherweise eine Datenzugriffskontrolle angewendet.
#XMSG: deployment of DAC is deprecated and view need to be redeployed to be able to persist data
VIEW_PERSISTENCY_DEPRECATED_DAC=Der View "{0}" wurde mit Datenzugriffskontrolle aktiviert, die abgekündigt ist. Aktivieren Sie den View erneut, um Daten für den View persistieren zu können.
##XFLD:Message used by SEAL repository for input parameter
START_VIEW_PERSISTENCY_INPUT_PARAMETER=Standardwert "{0}" wird für Eingabeparameter "{1}" verwendet.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Elastic compute node keys~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
##XFLD:Message used to reflect disablement of ecn replica
ECN_DISABLE_EXISTING_REPLICA=Das Replikat des elastischen Rechenleistungsknotens ist deaktiviert.
##XFLD:Message used to reflect recreation of ecn replica
ECN_RECREATE_EXISTING_REPLICA=Das Replikat des elastischen Rechenleistungsknotens wird wiederhergestellt.
##XFLD:Message used to reflect re-enablement of ecn replica
ECN_ENABLE_EXISTING_REPLICA=Das Replikat des elastischen Rechenleistungsknotens wird erneut aktiviert.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Task Schedule Menu key~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message to schedule task
scheduleText=Zeitplan
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Zeitplan anlegen
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Zeitplan bearbeiten
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Zeitplan löschen
#XFLD: Refresh frequency field
refreshFrequency=Aktualisierungshäufigkeit
#XFLD: Refresh frequency field
refreshFrequencyNew=Häufigkeit
#XFLD: Refresh frequency field
refreshFrequencyNewNew=Eingeplante Häufigkeit
#XBUT: label for None
none=Keine
#XBUT: label for Real-Time replication state
realtime=Echtzeit
#XFLD: Label for table column
txtNextSchedule=Nächster Lauf
#XFLD: Label for table column
txtNextScheduleNew=Nächster eingeplanter Lauf
#XFLD: Label for table column
txtNumOfRecords=Anzahl der Datensätze
#XFLD: Label for scheduled link
scheduledTxt=Eingeplant
#XFLD: LABEL for partially persisted link
partiallyPersisted=Teilweise persistiert
#XFLD: Text for paused text
paused=Pausiert

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ View Monitor Tasklog Details Action keys~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message for release lock actionn
releaseLockMsg=Wenn ein Lauf länger als üblich dauert, kann dies darauf hindeuten, dass er fehlgeschlagen ist und dass der Status nicht entsprechend aktualisiert wurde. \r\n Um das Problem zu beheben, können Sie die Sperre aufheben und den Status auf "Fehlgeschlagen" setzen.
#XFLD: Label for release lock dialog
releaseLockText=Sperre aufheben

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Column header tooltip ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: tooltip for table column
txtViewTooltip=Name des persistierten View
#XFLD: tooltip for table column
txtViewDataAccessTooltip=Gibt die Verfügbarkeit des View an
#XFLD: tooltip for table column
txtViewRefreshFreqTooltip=Gibt an, ob für den View ein Zeitplan definiert ist.
#XFLD: tooltip for table column
txtViewStatusTooltip=Ruft den Status des persistierten View ab
#XFLD: tooltip for table column
txtViewLatestUpdateTooltip=Enthält Informationen zur letzten persistierten Aktualisierung des View
#XFLD: tooltip for table column
txtViewNextRunTooltip=Wenn für den View ein Zeitplan festgelegt ist, sehen Sie hier den Zeitpunkt des nächsten Laufs.
#XFLD: tooltip for table column
txtViewNumOfRecordsTooltip=Zeigt die Anzahl der Datensätze an.
#XFLD: tooltip for table column
txtViewInMemorySizeTooltip=Zeigt an, wie viel Speicherplatz der View in Ihrem Speicher belegt.
#XFLD: tooltip for table column
txtViewDiskSizeTooltip=Zeigt an, wie viel Speicherplatz der View auf Ihrer Festplatte belegt.
#XMSG: Expired text
txtExpired=Abgelaufen

#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Das Objekt "{0}" kann nicht zur Aufgabenkette hinzugefügt werden.

#~~~~~~~~~~~View Analyzer task log messages~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message indicating number of records for a view, and memory consumption in Mebibyte during simulation of view persistency
RECORDS_PEAK_MEMORY=Der View "{0}" weist {1} Datensätze auf. Eine Simulation der Datenpersistenz hat {2} MiB Arbeitsspeicher verwendet.
#XMSG: View Analyzer execution failure
VIEW_ANALYZER_EXECUTION_FAILED=Ausführung von View Analyzer ist fehlgeschlagen.
#XMSG: Missing authorizations for View Analyzer
VIEW_ANALYZER_MISSING_AUTH=Fehlende Berechtigungen für View Analyzer.
#XMSG: Maximum memory in Gibibyte is reached, therefore persistency simulation is stopped
MAXIMUM_MEMORY_REACHED=Der maximale Speicherverbrauch von {0} GiB wurde bei der Simulation der Datenpersistenz für View "{1}" erreicht. Daher werden keine weiteren Datenpersistenzsimulationen ausgeführt.
#XMSG: Error during persistency simulation occurred
PERSISTENCY_SIMULATION_ERROR=Während der Datenpersistenzsimulation für den View "{0}" ist ein Fehler aufgetreten.
#XMSG: Persistency simulation is not executed, because prerequisites are not fulfilled and the view cannot be persisted
PREREQUISITES_NOT_FULFILLED=Die Datenpersistenzsimulation für den View "{0}" wird nicht ausgeführt, da Voraussetzungen nicht erfüllt sind und der View nicht persistiert werden kann.
#XMSG: Persistency simulation is not executed, because the view does not exist in the database
VIEW_DOES_NOT_EXIST=Sie müssen den View "{0}" aktivieren, um die Datenpersistenzsimulation zu aktivieren.
#XMSG: Number of records cannot be determined, because the local table does not exist in the database
TABLE_DOES_NOT_EXIST=Die lokale Tabelle "{0}" ist in der Datenbank nicht vorhanden, daher kann die Anzahl der Datensätze für diese Tabelle nicht ermittelt werden.
##XMSG: Message start of cancellation for View Analyzer task
CANCEL_VIEW_ANALYZER_TRIGGERED=Prozess zum Beenden von View-Analyzer-Aufgabe {0} für View "{1}" wurde übergeben. Es kann zu einer Verzögerung kommen, bis die Aufgabe beendet wird.
##XMSG: Message start to indicate that task is not active
CANCEL_VIEW_ANALYZER_NO_ACTIVE_TASK=View-Analyzer-Aufgabe {0} für den View "{1}" ist nicht aktiv.
#XMSG: Cancel View Analyzer task failure
CANCEL_VIEW_ANALYZER_FAILED=View-Analyzer-Aufgabe konnte nicht abgebrochen werden.
##XMSG: Message used in View Analyzer execution task when task is canceled
VIEW_ANALYZER_CANCELED=View-Analyzer-Ausführung für den View "{0}" wurde über eine Aufgabe zum Abbrechen beendet.
##XMSG: Message start of cancelation for Model Validation task
CANCEL_MODEL_VALIDATION_TRIGGERED=Prozess zum Beenden von Modellvalidierungsaufgabe {0} für View "{1}" wurde übergeben. Es kann zu einer Verzögerung kommen, bis die Aufgabe beendet wird.
##XMSG: Message to indicate that task is not active
CANCEL_MODEL_VALIDATION_NO_ACTIVE_TASK=Modellvalidierungsaufgabe {0} für den View "{1}" ist nicht aktiv.
#XMSG: Cancel Model Validation task failure
CANCEL_MODEL_VALIDATION_FAILED=Modellvalidierungsaufgabe konnte nicht abgebrochen werden.
##XMSG: Message used in Model Validation task when task is canceled
MODEL_VALIDATION_CANCELED=Modellvalidierungsausführung für den View "{0}" wurde über eine Aufgabe zum Abbrechen beendet.
#XMSG: Message for Locked space error, before starting Model Validation task
MODEL_VALIDATION_LOCKED_SPACE_ERROR=Modellvalidierung kann für den View "{0}" nicht ausgeführt werden, da der Space "{1}" gesperrt ist.
##XMSG: Message used in View Analyzer execution task when determination of number of rows fails
NUMBER_ROWS_DETERMINATION_FAILED=Beim Ermitteln der Anzahl der Zeilen für die lokale Tabelle "{0}" ist ein Fehler aufgetreten.
##XMSG: Message to indicate that SQL plan was created
EXECUTION_PLAN_CREATED=Plandatei für SQL Analyzer für den View "{0}" wurde angelegt und kann heruntergeladen werden.
#XFLD: Message to indicate starting of process to generate SQL Analyzer plan file
START_PLANVIZ_EXECUTION=Prozess zum Generieren der Plandatei für SQL Analyzer für den View "{0}" wird gestartet.
#XFLD: Message to indicate starting of View Analyzer process
VIEW_ANALYZER_EXECUTION_STARTED=Ausführung von View Analyzer wird gestartet.
##XMSG: Message to indicate that SQL plan file cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_PLANVIZ_NOT_FULFILLED=Plandatei für SQL Analyzer kann nicht für den View "{0}" generiert werden, da Voraussetzungen für die Datenpersistenz nicht erfüllt sind.
##XMSG: Error message during  generation of SQL Analyzer plan file
PLANVIZ_ERROR=Beim Generieren der Plandatei für SQL Analyzer für den View "{0}" ist ein Fehler aufgetreten.
#XMSG: Message for Locked space error
VIEW_ANALYZER_LOCKED_SPACE_ERROR=View Analyzer kann für den View "{0}" nicht ausgeführt werden, da der Space "{1}" gesperrt ist.
#XMSG: Message for persistency simulation without partitioning
PERSISTENCY_SIMULATION_WITHOUT_PARTITIONS=Partitionen des View "{0}" werden bei der Simulation der Datenpersistenz nicht berücksichtigt.
#XMSG: Message for generation of SQL Analyzer plan file without considering partitioning
PLANVIZ_WITHOUT_PARTITIONS=Partitionen des View "{0}" werden beim Generieren der Plandatei für SQL Analyzer nicht berücksichtigt.
#XMSG: Confirmation message for mass removal of persisted data
MassRemovePersistency_Confirm_Msg=Möchten Sie die persistierten Daten entfernen und den Datenzugriff wieder auf virtuellen Zugriff umstellen?
#XMSG: Confirmation message for partial mass removal of selected persisted data
PartialMassRemovePersistency_Confirm_Msg={0} der {1} ausgewählten Views weisen persistierte Daten auf. \n Möchten Sie die persistierten Daten entfernen und wieder auf virtuellen Zugriff umstellen?
#XMSG: success message for stopping mass persistence
stopMassPersistenceSuccess=Persistierte Daten für die ausgewählten Views werden entfernt.
#XMSG: error message during mass stop persistence of a view (backend error)
STOP_MASS_PERSISTENCE_ERROR=Beim Beenden der Persistenz für die ausgewählten Views ist ein Fehler aufgetreten.
#XMSG: memory analysis is only performed in space {0}
NO_MEMORY_ANALYSIS=Speicheranalyse wird nur für Entitäten in Space "{0}" durchgeführt: "{1}" "{2}" wurde übersprungen.
##XMSG: Message to indicate that Explain Plan cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_EXPLAINPLAN_NOT_FULFILLED=Explain-Plan-Datei kann nicht für den View "{0}" generiert werden, da Voraussetzungen für die Persistenz nicht erfüllt sind.
#XMSG: Message for Explain Plan generation without partitioning
EXPLAINPLAN_WITHOUT_PARTITIONS=Partitionen des View "{0}" werden beim Generieren der Explain-Plan-Datei nicht berücksichtigt.
#XFLD: Message to indicate starting of process to generate Explain Plan file
START_EXPLAINPLAN_EXECUTION=Prozess zum Generieren der Explain-Plan-Datei für den View "{0}" wird gestartet.
##XMSG: Message to indicate that Explain Plan was created
EXPLAINPLAN_CREATED=Explain-Plan-Datei für den View "{0}" wurde generiert. Sie können ihn anzeigen, indem Sie auf "Details anzeigen" klicken, oder ihn herunterladen, sofern Sie über die entsprechende Berechtigung verfügen.
##XMSG: Error message during  generation of Explain Plan file
EXPLAINPLAN_ERROR=Beim Generieren der Explain-Plan-Datei für den View "{0}" ist ein Fehler aufgetreten.
##XMSG: Explain Plan file cannot be generated for the view {0}, because too many views are stacked on each other
EXPLAINPLAN_MODEL_TOO_COMPLEX=Explain-Plan-Datei für View "{0}" kann nicht generiert werden. Zu viele Views sind übereinander gestapelt. Komplexe Modelle führen zu Fehlern wegen unzureichenden Speichers und beeinträchtigen die Performance. Es wird empfohlen, Views zu persistieren.

##XMSG: Message to indicate that the space is locked
PERFORMANCE_ANALYSIS_LOCKED_SPACE_ERROR=Leistungsanalyse für View "{0}" kann nicht ausgeführt werden, da Space "{1}" gesperrt ist.
##XMSG: The performance analysis task was cancelled
PERFORMANCE_ANALYSIS_CANCELED=Leistungsanalyse für View "{0}" wurde abgebrochen.
##XMSG: The performance analysis failed
PERFORMANCE_ANALYSIS_FAILED=Leistungsanalyse ist fehlgeschlagen.
##XMSG: The performance analysis is finished
PERFORMANCE_ANALYSIS_FINISHED=Leistungsanalyse für View "{0}" wurde abgeschlossen. Zeigen Sie das Ergebnis an, indem Sie "Details anzeigen" wählen.
##XMSG: The view has input parameter with no default value
INPUT_PARAMETER_WITHOUT_DEFAULT_VALUE=Dieser View kann nicht angezeigt werden, da er einen Parameter ohne Standardwert aufweist.
##XMSG: The view is not fully deployed
VIEW_NOT_FULLY_DEPLOYED=Dieser View kann nicht analysiert werden, da er nicht vollständig aktiviert ist.
##XMSG: The view uses limited remote adapters
LIMITED_REMOTE_ADAPTER=Dieser View verwendet mindestens einen Remote-Adapter mit eingeschränkter Funktionalität wie z. B. fehlendem Filter-Pushdown oder Unterstützung für "Count". Das Persistieren oder Replizieren von Objekten kann die Performance zur Laufzeit verbessern.
##XMSG: The view uses remote adapter that does not support 'Limit'
REMOTE_ADAPTER_NOT_SUPPORTING_LIMIT=Dieser View verwendet mindestens einen Remote-Adapter, der "Limit" nicht unterstützt. Gegebenenfalls wurden mehr als 1.000 Datensätze ausgewählt.
##XMSG: Performance analysis is executed by using default parameters of view'
INPUT_PARAMETER_WITH_DEFAULT_VALUE=Die Leistungsanalyse wird unter Anwendung der Standardwerte der View-Parameter durchgeführt.
##XMSG: Message to indicate that an error occurred
PERFORMANCE_ANALYSIS_ERROR=Bei der Leistungsanalyse für View "{0}" ist ein Fehler aufgetreten.
##XMSG: Message start of cancellation for Performance Analysis task
CANCEL_PERFORMANCE_ANALYSIS_TRIGGERED=Prozess zum Beenden von Leistungsanalyse-Aufgabe {0} für View "{1}" wurde übergeben. Es kann zu einer Verzögerung kommen, bis die Aufgabe beendet wird.
##XMSG: Message start to indicate that task is not active
CANCEL_PERFORMANCE_ANALYSIS_NO_ACTIVE_TASK=Leistungsanalyse-Aufgabe {0} für View "{1}" ist nicht aktiv.
#XMSG: Cancel Performance Analysis task failure
CANCEL_PERFORMANCE_ANALYSIS_FAILED=Abbrechen von Leistungsanalyse-Aufgabe ist fehlgeschlagen.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Zeitplan mir zuweisen
#XBUT: Pause schedule menu label
pauseScheduleLabel=Zeitplan pausieren
#XBUT: Resume schedule menu label
resumeScheduleLabel=Zeitplan fortsetzen
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Beim Entfernen von Zeitplänen ist ein Fehler aufgetreten.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Beim Zuweisen von Zeitplänen ist ein Fehler aufgetreten.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Beim Pausieren von Zeitplänen ist ein Fehler aufgetreten.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Beim Fortsetzten von Zeitplänen ist ein Fehler aufgetreten.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted={0} Zeitpläne werden gelöscht
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Eigentümer von {0} Zeitplänen wird geändert
#XMSG: Message for starting mass pausing of schedules
massPauseStarted={0} Zeitpläne werden pausiert
#XMSG: Message for starting mass resuming of schedules
massResumeStarted={0} Zeitpläne werden fortgesetzt
#XBUT: Select Columns Button
selectColumnsBtn=Spalten auswählen
#XFLD: Refresh tooltip
TEXT_REFRESH=Aktualisieren
#XFLD: Select Columns tooltip
text_selectColumns=Spalten auswählen


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Runtime Metrics ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Label for Runtime Metrics
RuntimeMetricsTitle=Laufzeitkennzahlen für
#XFLD : Label for Run Button
runButton=Ausführen
#XFLD : Label for Cancel Button
cancelButton=Abbrechen
#XFLD : Label for Close Button
closeButton=Schließen
#XFLD : Label for Open View Analyzer Button
openViewAnalyzerButton=View Analyzer öffnen
#XFLD : Label for Generate Explain Plan Button
generateExplainPlanButton=Explain-Plan generieren
#XFLD : Label for Previous Run Column
previousRun=Vorheriger Lauf
#XFLD : Label for Latest Run Column
latestRun=Letzter Lauf
#XFLD : Label for time Column
time=Uhrzeit
#XFLD : Label for Duration Column
duration=Dauer
#XFLD : Label for Peak Memory Column
peakMemory=Maximale Speicherauslastung
#XFLD : Label for Number of Rows
numberOfRows=Anzahl der Zeilen
#XFLD : Label for Overall Number of Sources
overallNumberOfSources=Gesamtzahl der Quellen
#XFLD : Label for Data Access Column
dataAccess=Datenzugriff
#XFLD : Label for Local Tables
localTables=Lokale Tabellen
#XFLD : Label for federatedRemoteTables
federatedRemoteTables=Föderierte Remote-Tabellen (mit eingeschränkter Adapterfunktionalität)
#XTXT Text for initial state of the runtime metrics
initialState=Sie müssen zunächst eine Leistungsanalyse durchführen, um die Metriken zu erhalten. Dies kann einige Zeit in Anspruch nehmen. Sie können den Vorgang jedoch bei Bedarf abbrechen.
#XMSG: Cancel confirmation message
CancelRunConfirmationMessage=Möchten Sie den aktuellen Lauf der Leistungsanalyse wirklich abbrechen?
#XTIT: Cancel dialog title
CancelRunTitle=Lauf abbrechen
#XFLD: Label for Number of Rows
NUMBER_ROWS=Anzahl der Zeilen
#XFLD: Label for Overall Number of Sources
OVERALL_NUMBER_SOURCES=Gesamtzahl der Quellen
#XFLD: Label for Data Access
DATA_ACCESS=Datenzugriff
#XFLD: Label for Remote tables
NUMBER_REMOTE_TABLES_LIMITED_ADAPTERS=Föderierte Remote-Tabellen (mit eingeschränkter Adapterfunktionalität)
#XFLD: Label for select statement
SELECT_STATEMENT='SELECT * FROM VIEW LIMIT 1000'
#XFLD: Label for duration
SELECT_RUNTIME=Dauer
#XFLD: Label for peak memory
SELECT_PEAK_MEMORY=Maximale Speicherauslastung
#XFLD: Label for count statement
SELECT_COUNT_STATEMENT='SELECT COUNT(*) FROM VIEW'
#XFLD: Label for count duration
SELECT_COUNT_RUNTIME=Dauer
#XFLD: Label for count peak memory
SELECT_COUNT_PEAK_MEMORY=Maximale Speicherauslastung
#XFLD: Label for count of number of local tables
NUMBER_LOCAL_TABLES_FILE=Lokale Tabellen (Datei)
#XTXT: Text for running state of the runtime metrics
Running=Wird ausgeführt ...
#XFLD: Label for time
Time=Uhrzeit
#XFLD: Label for virtual access
PA_VIRTUAL=Virtuell
#XFLD: Label for persisted access
PA_PERSISTED=Persistiert
PA_PARTIALLY_PERSISTED=Teilweise persistiert
#XTXT: Text for cancel
CancelRunSuccessMessage=Lauf der Leistungsanalyse wird abgebrochen.
#XTXT: Text for cancel error
CancelRunErrorMessage=Beim Abbrechen des Laufs der Leistungsanalyse ist ein Fehler aufgetreten.
#XTXT: Text for explain plan generation
ExplainPlanStarted=Explain-Plan für View "{0}" wird generiert.
#XTXT: Text for performance analysis
PerformanceAnalysisStarted=Leistungsanalyse für View "{0}" wird gestartet.
#XTXT: Text for performance analysis error
ErrorFetchingPerformanceAnalysis=Beim Abrufen der Leistungsanalysedaten ist ein Fehler aufgetreten.
#XTXT: Text for performance analysis error
conflictingTask=Leistungsanalyse-Aufgabe wird bereits ausgeführt
#XFLD: Label for Errors
Errors=Fehler
#XFLD: Label for Warnings
Warnings=Warnungen
#XFLD: Tooltip for missing privileges
openViewAnalyzerButtonTooltipNoPrivilege=Sie benötigen die Berechtigung DWC_DATAINTEGRATION(update), um den View Analyzer zu öffnen.
#XFLD: Tooltip for missing privileges
generateExplainPlanButtonTooltipNoPrivilege=Sie benötigen die Berechtigung DWC_RUNTIME(read), um den Explain-Plan zu generieren.



#XFLD: Label for frequency column
everyLabel=Alle
#XFLD: Plural Recurrence text for Hour
hoursLabel=Stunden
#XFLD: Plural Recurrence text for Day
daysLabel=Tage
#XFLD: Plural Recurrence text for Month
monthsLabel=Monate
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minuten
