
#XBCB: Breadcrumb
viewMonitorBreadcrumb={spaceId}

#XFLD: Label for persisted view column
SOURCE=Source
#XFLD: Label for persisted view column
NAME=Name
#XFLD: Label for persisted view column
NAME_LABEL=Business Name
#XFLD: Label for persisted view column
NAME_LABELNew=Object (Business Name)
#XFLD: Label for persisted view column
TECHINCAL_NAME=Technical Name
#XFLD: Label for persisted view column
TECHINCAL_NAMENew=Object (Technical Name)
#XFLD: Label for persisted view column
DATA_PERSISTENCY=Data Access
#XFLD: Label for persisted view column
STATUS=Status
#XFLD: Label for persisted view column
LAST_UPDATED=Last Updated
#XFLD: Label for persisted view column
IN_MEMORY_SIZE=Memory Used for Storage (MiB)
#XFLD: Label for persisted view column
DISK_SIZE=Disk Used for Storage (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_NEWW=Size in-Memory (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_HEADER=Size in-Memory
#XFLD: Label for persisted view column
DISK_SIZE_NEWW=Size on Disk (MiB)
#XFLD: Label for persisted view column
DISK_SIZE_HEADER=Size on Disk
#XFLD: Label for schedule owner column
txtScheduleOwner=Schedule Owner
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Shows who created the schedule
#XFLD: Label for persisted view column
PERSISTED=Persisted
#XFLD: Label for persisted view column
TYPE=Type
#XFLD: Label for View Selection Dialog column
changedOn=Changed On
#XFLD: Label for View Selection Dialog column
createdBy=Created By
#XFLD: Label for log details column
txtViewPersistencyLogs=View Logs
#XFLD: Label for log details column
txtViewPersistencyLogsNew=Details
#XFLD: text for values shown for Ascending sort order
SortInAsc=Sort Ascending
#XFLD: text for values shown for Descending sort order
SortInDesc=Sort Descending
#XFLD: Label for persisted view monitor heading
ViewMonitor_Heading=Views Monitor
#XFLD: Label for persisted view monitor sub-heading
ViewMonitor_SubHeading=Monitor and Maintain Data Persistence of Views


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ DynamicPageHeader ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: text for values shown in column Persistence Status
txtLoading=Loading
#XFLD: text for values shown in column Persistence Status
txtRunning=Running
#XFLD: text for values shown in column Persistence Status
txtAvailable=Available
#XFLD: text for values shown in column Persistence Status
txtError=Error
#XMSG: Error message for unknown Persistence type that gets logged in Kibana
unknownReplicationTypeError=Replication type "{0}" is not supported.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Content ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Settings used for last data persistence run:
#XMSG: Message for input parameter name
inputParameterLabel=Input Parameter
#XMSG: Message for input parameter value
inputParameterValueLabel=Value
#XMSG: Message for persisted data
inputParameterPersistedLabel=Persisted At
#XTIT: Count for View in ViewMonitor Toolbar
txtViewCount=Views ({0})
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=View Persistency
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Data Persistence
#XBUT: Button to clear the selected view persistance
ClearViewPersistance=Clear
#XBUT: Button to stop the selected view persistance
stopPersistance=Stop Persistence
#XFLD: Placeholder for Search field
txtSearch=Search
#XBUT: Tooltip for refresh button
txtRefresh=Refresh
#XBUT: Tooltip for add view button
txtDeleteView=Delete Persistence
#XBUT: Tooltip for load new peristence
loadNewPersistence=Restart Persistence
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Load New Snapshot
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Start Data Persistence
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Remove Persisted Data
#XMSG: success message for starting persistence
startPersistenceSuccess=We’re persisting the view "{0}".
#XMSG: success message for stopping persistence
stopPersistenceSuccess=We’re removing persisted data for view "{0}".
#XMSG: success message for stopping persistence
deletePersistedViewSuccess=We’re removing the view "{0}" from the monitoring list.
#XMSG: error message during start persistence of a view (backend error)
ERROR_STARTING_PERSISTENCE=An error occurred while starting the data persistence for view "{0}".
#XMSG: error message during start persistence of a view (backend error)
PARAMERROR_STARTING_PERSISTENCE=The view "{0}" cannot be persisted because it contains input parameters.
#XMSG: error message during start persistence of a view (backend error)
NUMBER_OF_PARAMETER_STARTING_PERSISTENCE=The view "{0}" cannot be persisted because it has more than one input parameter.
#XMSG: error message during start persistence of a view (backend error)
PARAMETER_DEFAULT_VALUE_STARTING_PERSISTENCE=The view "{0}" cannot be persisted because the input parameter does not have default value.
#XMSG: error message during start persistence of a view (backend error)
REDEPLOYMENT_DAC_REQUIRED=Redeployment of the Data Access Control (DAC) "{0}" is required to support data persistence.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_STARTING_PERSISTENCE=The view "{0}" cannot be persisted, because it uses the view "{1}", which contains Data Access Control (DAC).
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_EXTERNAL_STARTING_PERSISTENCE=The view "{0}" cannot be persisted, because it uses a view with Data Access Control (DAC) that belongs to a different space.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_STRUCTURE_STARTING_PERSISTENCE=The view "{0}" cannot be persisted because the structure of one or more of its Data Access Controls (DAC) does not support data persistence.
#XMSG: error message during stop persistence of a view (backend error)
STOP_PERSISTENCE_ERROR=An error occurred while stopping the persistence for view "{0}".
#XMSG: error message during stop persistence of a view (backend error)
DELETE_PERSISTED_VIEW_ERROR=An error occurred while deleting the persisted view "{0}".
#XMSG: Warning message for removing the persisted data
RemovePersistency_Confirm_Msg=Do you want to delete the persisted data and switch to virtual access of view "{0}"?
#XMSG: Warning message for delete the view persistence
DeletePersistency_Confirm_Msg=Do you want to remove the view from monitoring list and delete the persisted data of view "{0}"?
#XMSG: error message for reading data from backend
txtReadBackendError=It looks like there was an error while reading from the back end.
#XFLD: Label for No Data Error
NoDataError=Error
#XMSG: message for conflicting task
Task_Already_Running=A conflicting task is already running for the view "{0}".

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Show All Views~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Text for show all views button
allViewsText = All Views ({0})
#XBUT: Text for show scheduled views button
scheduledText = Scheduled ({0})
#XBUT: Text for show persisted views button
persistedText = Persisted ({0})
#XBUT: Text for start analyzer button
startAnalyzer=Start View Analyzer
#XFLD: Message if repository is unavailable
repositoryErrorMsg=The repository isn’t available and certain features are disabled.

#XFLD: Data Access - Virtual
Virtual=Virtual
#XFLD: Data Access - Persisted
Persisted=Persisted

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Non-Persisted View Selection Dialog~~~~~~~~~~~~~~~~~~~~~~~

#XTIT: Title for Non-Persisted View selection dialog
select_source=Select View to Persist

#XTIT: Placeholder for Non-Persisted View selection dialog search
searchView=Search Views
#XTIT: No data in the list of non-persisted view
No_Data=No Data
#XBUT: Button to select non-persisted view
ok=Ok
#XBUT: Button to close the non-persisted views selection dialog
cancel=Cancel

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~View persistency log message key~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message for view persistency logs
START_STOP_PERSISTENCY=Starting data persistence task run for "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY=Persisting data for the view "{1}".
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY)
START_VIEW_PERSISTENCY_PROCESS=Starting the process to persist data for the view "{0}".
#XFLD: Message for view persistency manual partition load
START_VIEW_PERSISTENCY_PROCESS_MANUAL_PARTITIONS=Starting the process to persist data for the view "{0}" with selected partition IDs: "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY=Removing persisted data for the view "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY)
REMOVE_VIEW_PERSISTENCY=Starting the process to remove persisted data for the view "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_SUCCESS=Data is persisted for the view "{1}".
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_SUCCESS)
START_VIEW_PERSISTENCY_PROCESS_SUCCESS=Data is persisted for the view "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY_SUCCESS=Persisted data removed and virtual data access restored for the view "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_SUCCESS)
REMOVE_VIEW_PERSISTENCY_SUCCESS=Completed the process to remove persisted data for the view "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_FAILED=Unable to persist data for the view "{1}".
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_FAILED)
START_VIEW_PERSISTENCY_PROCESS_FAILED=Unable to persist data for the view "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.05)
STOP_VIEW_PERSISTENCY_FAILED=Unable to remove persisted data for the view "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_FAILED)
REMOVE_VIEW_PERSISTENCY_FAILED=Unable to remove persisted data for the view "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_SUCCESS="{3}" records persisted for the view "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace START_PERSISTENCY_RECORD_COUNT_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS={0} records inserted into the data persistence table for the view "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS_V2={0} records inserted into the data persistence table for the view "{1}". Memory used: {2} GiB.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_SUCCESS="{3}" persisted records removed for the view "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace STOP_PERSISTENCY_RECORD_COUNT_SUCCESS)
REMOVE_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=Persisted data removed, "{0}" persisted records deleted.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_FAILED=Get recordCount failed for the view "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_FAILED=Get recordCount failed for the view "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Schedule is deleted for "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS)
REMOVE_VIEW_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Schedule is deleted for the view "{0}".
#XFLD: Message for view persistency logs
STOP_PERSISTENCY_DELETE_SCHEDULE_FAILED=Schedule deletion failed for "{1}".
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_REDEPLOY=We can’t persist the view "{0}" because it has been changed and deployed since you started to persist it. Try again to persist the view or wait until the next scheduled run.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_DELETE=We can’t persist the view "{0}" because it has been deleted since you started to persist it.
#XFLD: Message for successful data persistency of a single partition (for messages created before version 2022.03)
START_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} records persisted into partition for values "{1}" <= {2} < "{3}".
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (new message to replace START_PERSISTENCY_SINGLE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} records inserted into partition for values "{1}" <= "{2}" < "{3}".
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS_V2={0} records inserted into partition for values "{1}" <= "{2}" < "{3}". Memory used: {4} GiB.
#XFLD: Message for successful replication of a "others" partition (for messages created before version 2022.03)
START_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} records persisted into "others" partition.
#XFLD: Message used by SEAL repository for successful replication of a "others" partition (new message to replace START_PERSISTENCY_OTHERS_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} records inserted into "others" partition.
#XFLD: Message for view persistency record count of partitions (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={3} records persisted for the view "{1}" in {4} partitions.
#XFLD: Message used by SEAL repository for view persistency record count of partitions (new message to replace START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={0} records inserted into the data persistence table for the view "{1}" in {2} partitions.
#XFLD: Message used by SEAL repository for view persistency for partition wise refresh
START_VIEW_PERSISTENCY_RECORD_COUNT_REFRESHED_PARTITIONS_SUCCESS={0} records inserted into the data persistence table for the view "{1}". Updated partitions: {2}; Locked partitions: {3}; Total partitions: {4}
#XFLD: Message used by SEAL repository for view persistency for manual partition load
START_VIEW_PERSISTENCY_RECORD_COUNT_MANUAL_PARTITIONS_SUCCESS={0} records inserted into data persistence table for the view "{1}" in {2} selected partitions
#XFLD: Message used by SEAL repository for view persistency for locked partition data load
START_VIEW_PERSISTENCY_RECORD_COUNT_LOCKED_PARTITIONS_SUCCESS={0} records inserted into the data persistence table for the view "{1}". Updated partitions: {2}; Locked, unchanged partitions: {3}; Total partitions: {4}
#XMSG: Message for unexpected error (for messages created before version 2022.03)
START_PERSISTENCY_UNEXPECTED_ERROR=An unexpected error occurred while persisting data for the view "{0}".
#XMSG: Message for unexpected error (new message to replace START_PERSISTENCY_UNEXPECTED_ERROR)
START_VIEW_PERSISTENCY_UNEXPECTED_ERROR=An unexpected error occurred while persisting data for the view "{0}".
#XMSG: Message for Locked space error
START_VIEW_PERSISTENCY_LOCKED_SPACE_ERROR=The view "{0}" can’t be persisted because the space "{1}" is locked.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
STOP_PERSISTENCY_UNEXPECTED_ERROR=An unexpected error occurred while removing persisted data for the view "{0}".
#XMSG: Message for unexpected error (new message to replace STOP_PERSISTENCY_UNEXPECTED_ERROR)
REMOVE_VIEW_PERSISTENCY_UNEXPECTED_ERROR=An unexpected error occurred while removing persistence for the view "{0}".
#XMSG: Message for ivalidated view
VIEW_PERSISTENCY_ABORTED_DEFINITION_INVALID=Definition of the view "{0}" has become invalid, most probably due to a change of an object consumed directly or indirectly by the view. Try to redeploy the view to solve the issue, or to identify the root cause.
#XMSG: Message for persisted data removal (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT=Persisted data is removed while deploying view "{0}".
#XMSG: Message for persisted data removal of a consuming view
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW=Persisted data is removed while deploying the consumed view "{0}".
#XMSG: Message for persisted data removal of a consuming view due to a dac change
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW_DAC_CHANGED=Persisted data is removed while deploying the consumed view "{0}" because its data access control has changed.
#XMSG: Message for persisted data removal (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DEPLOYMENT=Persisted data is removed while deploying the view "{0}".
#XMSG: Message for deleted view (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION=Persistence is removed with deletion of the view "{0}".
#XMSG: Message for deleted view (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DELETION=Persistence is removed with deletion of the view "{0}".
#XMSG Message for removal of persisted data, because prerequisites not fulfilled
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_PREREQUISITES_NOT_FULFILLED=Persisted data is removed, because data persistence prerequisites are no longer fulfilled.
#XMSG: Message for inconsistent persistency
VIEW_PERSISTENCY_INCONSISTENT_PERSISTENCY=The persistence of the view "{0}" has become inconsistent. Remove persisted data to solve the issue.
#XMSG: Message for prerequisites
START_PERSISTENCY_CHECK_PREREQUISITES=Checking the prerequisites to persist the view "{0}".
#XMSG: Message for DAC deployed in deprecated way using table function and join (for messages created before 2025.05)
START_PERSISTENCY_DEPRECIATED_DAC=The view "{0}" is deployed using Data Access Control (DAC) that is being deprecated. Please deploy the view again to improve the performance.
#XMSG: Message for DAC deployed in deprecated way using table function and join
START_PERSISTENCY_DEPRECATED_DAC=The view "{0}" is deployed using Data Access Control (DAC) that is being deprecated. Please deploy the view again to improve the performance.
#XMSG: Message for roll back (for messages created before version 2022.03)
ROLLBACK_VIEW_PERSISTENCY=An error occurred. Previous state of persistence restored for view "{0}".
#XMSG: Message used by SEAL repository for rollback of running procedure to persist view (new message to replace ROLLBACK_VIEW_PERSISTENCY)
VIEW_PERSISTENCY_ROLLBACK=An error occurred. The process to persist the view "{0}" has been stopped and changes have been rolled back.
#XMSG: Message used by SEAL repository for rollback of running procedure to remove persisted data of view
REMOVE_VIEW_PERSISTENCY_ROLLBACK=An error occurred. The process to remove persisted data of the view "{0}" has been stopped and changes have been rolled back.
#XMSG: Message used by SEAL repository for persistency preparation
START_PERSISTENCY_PREPARATION=Preparing to persist data.
#XMSG: Message used by SEAL repository for data insertion
START_INSERTING_PERSISTENCY_DATA=Inserting data in persistency table.
#XMSG: Message for null values - partitioning (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS={0} null value records inserted into "others" partition.
#XMSG: Message used by SEAL repository for others partition values - partitioning (new message to replace START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS={0} records inserted into "others" partition for values "{2}" < "{1}" OR "{2}" >= "{3}".
#XMSG: Message used by SEAL repository for others partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS_V2={0} records inserted into "others" partition for values "{2}" < "{1}" OR "{2}" >= "{3}". Memory used: {4} GiB.
#XMSG: Message used by SEAL repository for null partition values - partitioning
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS={0} records inserted into "others" partition for values "{1}" IS NULL.
#XMSG: Message used by SEAL repository for null partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS_V2={0} records inserted into "others" partition for values "{1}" IS NULL. Memory used: {2} GiB.
##XFLD:Message used by SEAL repository for remote statistics
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS=Loading the data involved: {0} remote statements. Total records fetched: {1}. Total duration time: {2} seconds.
##XFLD:Message used by SEAL repository for remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS_WITH_PARTITIONS=Loading data involved using {0} partitions with {1} remote statements. Total records fetched: {2}. Total duration time: {3} seconds.
##XFLD:Message used by SEAL repository with UI ID for instructions of remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_INSTRUCTION=The remote statements processed during the run can be displayed by opening the remote query monitor, in the details of the partition-specific messages.
##XFLD:Message for start reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS=Starting the process to reuse existing persisted data for view {0} after deployment.
##XFLD:Message used by SEAL repository for reattach view persitency
START_VIEW_PERSISTENCY_REATTACHMENT=Start to reuse the existing persisted data.
##XFLD:Message used by SEAL repository for reattach view persitency
START_FINILIZING_VIEW_PERSISTENCY_REATTACHMENT=Reusing the existing persisted data.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_COMPLETE=The process to reuse existing persisted data is completed for the view {0}.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_FAILED=Failed to reuse the existing persisted data for view {0} after deployment.
#XMSG: Message used by SEAL repository for finalizing persistency
START_FINALIZING_PERSISTENCY=Finalizing persistence.
#XMSG: Message used by SEAL repository for restoring virtual access for view
REMOVE_VIEW_PERSISTENCY_VIRTUAL_ACCESS_RESTORED=The virtual data access is restored for the view "{0}".
#XMSG: Message used by SEAL repository for view that already has virtual access
REMOVE_VIEW_PERSISTENCY_ALREADY_VIRTUAL_ACCESS=The view "{0}" already has virtual data access. No persisted data is removed.
#XMSG: Message for removing view persistency
VIEW_PERSISTENCY_REMOVED_FROM_MONITOR=The view "{0}" has been removed from the Views monitor.
#XMSG: Message for view doesnt exist.
VIEW_PERSISTENCY_VIEW_NOT_EXISTING=The view "{0}" either doesn’t exist in the database or is not correctly deployed, and therefore cannot be persisted. Try to redeploy the view to solve the issue, or to identify the root cause.
#XMSG: Message for persistency not enabled.
VIEW_PERSISTENCY_NOT_ENABLED=Data persistence is not enabled. Redeploy a table/view in the space "{0}" to enable the functionality.
#XMSG: Message for unexpected error due to node crash
VIEW_PERSISTENCY_UNEXPECTED_ERROR_GENERAL=Last view persistency run was interrupted due to technical errors.
#XFLD: Message for view persistency memory consumption
VIEW_PERSISTENCY_MEMORY_CONSUMPTION={0} GiB of peak memory used in the view persistency runtime.
#XFLD: Message for view persistency timeout
VIEW_PERSISTENCY_TIMEOUT=Persistency of view {0} reached timeout of {1} hours.
#XFLD: Message for not started asynchronous view persistency
VIEW_PERSISTENCY_ASYNC_NOT_STARTED=High system load prevented the asynchronous execution of the view persistency from starting. Check if too many tasks are running in parallel.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency (for messages created before version 2022.05)
START_VIEW_PERSISTENCY_EXECUTION_METHOD_DROP_AND_RENAME=Existing persisted table has been deleted and replaced with a new persisted table.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_RECREATE_AND_DROP=Existing persisted table has been deleted and replaced with a new persisted table.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_TRUNCATE_AND_COPY=Existing persisted table has been updated with new data.
##XFLD: Message for missing authorizations for view persistency
VIEW_PERSISTENCY_MISSING_AUTH=Missing authorizations for data persistence.
##XFLD: Message start of persistency cancellation
VIEW_PERSISTENCY_TRIGGER_CANCEL=Starting to cancel the process to persist the view {0}.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_UNAVAILABLE_TASK_RUN=Failed to cancel the process to persist the view because there is no running data persistence task for the view {0}.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_NONDETERMINISTIC_TASK_RUN=Failed to cancel the process to persist the view because no data persistence task is running for the view {0}.
##XFLD: Message for canceling a non running persitency task
VIEW_PERSISTENCY_CANCEL_NON_RUNNING_SELECTED_TASK=Failed to cancel the process to persist the view {0} because the selected data persistence task {1} is not running.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_UNREACHABLE_PERSISTENCY_RUN=Failed to cancel the process to persist the view because data persistence for the view {0} has not started yet.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_COMPLETED_PERSISTENCY_RUN=Failed to cancel the process to persist the view {0} because it has already been completed.
##XFLD: Message for failed cancellation
VIEW_PERSISTENCY_ABORTED_CANCEL_EXECUTION=Failed to cancel the process to persist the view {0}.
##XFLD: Message for finalizing persistency cancellation
VIEW_PERSISTENCY_FINALIZE_CANCEL=Process to stop the data persistence of the view {0} has been submitted.
##XFLD: Message used in persist task when cancellation is caught
VIEW_PERSISTENCY_CANCELED=Process to persist the view {0} was stopped.
##XFLD: Message used in persist task when cancelation is caught and performed through a cancelation task
VIEW_PERSISTENCY_CANCELED_VIA_CANCEL_TASK=Process to persist the view {0} was stopped via the cancelation task {1}.
##XFLD: Message used in cancel task when the task is triggered by deployment
VIEW_PERSISTENCY_CANCEL_TRIGGERED_BY_DEPLOYMENT=Canceling the process to persist data while deploying the view {0}.
##XFLD: Message used in case the cancelation is retriggered for a running task
VIEW_PERSISTENCY_CANCEL_ATTEMPT_RETRIGGER_CANCEL=A previous cancelation task to persist the view {0} has already been submitted.
##XFLD: Message used to express the delay that may occur before the persistency task is stopped
VIEW_PERSISTENCY_CANCEL_DELAY_STOPPING_PERSISTENCY=There may be a delay until the data persistence task for the view {0} is stopped.
##XFLD: Message used to reflect the task log identifier of the current running persistency task
VIEW_PERSISTENCY_CANCEL_CURRENT_RUNNING_TASK=Data for view {0} is being persisted with the task {1}.
##XFLD:Message used by SEAL repository for locked partitions and DACs
VIEW_PERSISTENCY_LOCKED_PARTITIONS_DACS=Authorizations provided by DACs may have changed and are not considered by locked partitions. Unlock the partitions and load a new snapshot to apply the changes.
##XFLD:Message used by SEAL repository for column structure change
VIEW_PERSISTENCY_COLUMN_STRUCTURE_CHANGE=The column structure has changed and no longer matches the existing persistence table. Remove persisted data and start a new data persistence to get your persistence table updated.
##XFLD: Message for out of memory
VIEW_PERSISTENCY_HANA_OUT_OF_MEMORY=The task failed because of an out of memory error on the SAP HANA database.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_EXCEPTION_TRANSACTION_ROLLBACK=The task failed because of an internal exception on the SAP HANA database.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_SQL_EXECUTION_ISSUE=The task failed because of an internal SQL execution issue on the SAP HANA database.
##XFLD: Message for HANA out of memory result
VIEW_PERSISTENCY_OUT_OF_MEMORY_RESULT_MESSAGE=HANA out of memory event reason: {0}
##XFLD: Message for admission control rejection
VIEW_PERSISTENCY_HANA_ADMISSION_CTRL_REJECTION=The task failed because of an SAP HANA Admission Control Rejection.
##XFLD: Message for HANA back pressure / rate limit of HANA client
VIEW_PERSISTENCY_HANA_CLIENT_RATE_LIMIT_REJECTION=The task failed because of too many active SAP HANA connections.
#XMSG: Message for invalid persisted table name
VIEW_PERSISTENCY_PERSISTED_TABLE_INVALID=An error occurred and the persisted table has become invalid. To solve the issue, please remove the persisted data and persist the view again.
#XMSG: Message for usahe of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION=The view cannot be persisted. It uses a remote table based on a remote source with enabled user propagation. Check the lineage of the view.
#XMSG: Message for dynamic usage of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION_DYNAMIC=The view cannot be persisted. It uses a remote table based on a remote source with enabled user propagation. The remote table may be consumed dynamically via a SQL script view. The lineage of the view may not show the remote table.
#XMSG: Message for insufficient privileges or consumption of view with DAC via dynamic SQL
VIEW_PERSISTENCY_INSUF_PRIV_OR_DAC_DYN_SQL=Your privileges may be insufficient. Open the Data Preview to see if you have the privileges required. If yes, a second view consumed via dynamic SQL script may have data access control (DAC) applied to it.
#XMSG: deployment of DAC is deprecated and view need to be redeployed to be able to persist data
VIEW_PERSISTENCY_DEPRECATED_DAC=The view "{0}" is deployed using Data Access Control (DAC) that is being deprecated. Please deploy the view again to be able to persist data for the view.
##XFLD:Message used by SEAL repository for input parameter
START_VIEW_PERSISTENCY_INPUT_PARAMETER=Using default value "{0}" for input parameter "{1}".
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Elastic compute node keys~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
##XFLD:Message used to reflect disablement of ecn replica
ECN_DISABLE_EXISTING_REPLICA=The elastic compute node replica is disabled.
##XFLD:Message used to reflect recreation of ecn replica
ECN_RECREATE_EXISTING_REPLICA=The elastic compute node replica is recreated.
##XFLD:Message used to reflect re-enablement of ecn replica
ECN_ENABLE_EXISTING_REPLICA=The elastic compute node replica is re-enabled.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Task Schedule Menu key~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message to schedule task
scheduleText=Schedule
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Create Schedule
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Edit Schedule
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Delete Schedule
#XFLD: Refresh frequency field
refreshFrequency=Refresh Frequency
#XFLD: Refresh frequency field
refreshFrequencyNew=Frequency
#XFLD: Refresh frequency field
refreshFrequencyNewNew=Scheduled Frequency
#XBUT: label for None
none=None
#XBUT: label for Real-Time replication state
realtime=Real-Time
#XFLD: Label for table column
txtNextSchedule=Next Run
#XFLD: Label for table column
txtNextScheduleNew=Scheduled Next Run
#XFLD: Label for table column
txtNumOfRecords=Number of Records
#XFLD: Label for scheduled link
scheduledTxt=Scheduled
#XFLD: LABEL for partially persisted link
partiallyPersisted=Partially Persisted
#XFLD: Text for paused text
paused=Paused

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ View Monitor Tasklog Details Action keys~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message for release lock actionn
releaseLockMsg=If a run takes longer than usual, this might indicate that it has failed and that the status has not been updated accordingly. \r\n To solve the issue, you can release the lock and set its status to failed.
#XFLD: Label for release lock dialog
releaseLockText=Release Lock

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Column header tooltip ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: tooltip for table column
txtViewTooltip=Name of the persisted view
#XFLD: tooltip for table column
txtViewDataAccessTooltip=This indicates the availability of the view
#XFLD: tooltip for table column
txtViewRefreshFreqTooltip=This indicates whether a schedule is defined for the view
#XFLD: tooltip for table column
txtViewStatusTooltip=Get the status of the persisted view
#XFLD: tooltip for table column
txtViewLatestUpdateTooltip=Provides information about when the persisted view was last updated
#XFLD: tooltip for table column
txtViewNextRunTooltip=If a schedule is set for the view, see by when the next run is scheduled.
#XFLD: tooltip for table column
txtViewNumOfRecordsTooltip=Track number of records.
#XFLD: tooltip for table column
txtViewInMemorySizeTooltip=Track how much size the view is using in your memory
#XFLD: tooltip for table column
txtViewDiskSizeTooltip=Track how much size the view is taking on your disk
#XMSG: Expired text
txtExpired=Expired

#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=The object "{0}" cannot be added to the task chain.

#~~~~~~~~~~~View Analyzer task log messages~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message indicating number of records for a view, and memory consumption in Mebibyte during simulation of view persistency
RECORDS_PEAK_MEMORY=The view "{0}" has {1} records. A data persistence simulation for this view used {2} MiB of memory.
#XMSG: View Analyzer execution failure
VIEW_ANALYZER_EXECUTION_FAILED=View Analyzer execution failed.
#XMSG: Missing authorizations for View Analyzer
VIEW_ANALYZER_MISSING_AUTH=Missing authorizations for View Analyzer.
#XMSG: Maximum memory in Gibibyte is reached, therefore persistency simulation is stopped
MAXIMUM_MEMORY_REACHED=The maximum memory of {0} GiB has been reached while simulating data persistence for view "{1}". Therefore, no further data persistence simulations will be run.
#XMSG: Error during persistency simulation occurred
PERSISTENCY_SIMULATION_ERROR=An error occurred during data persistence simulation for the view "{0}".
#XMSG: Persistency simulation is not executed, because prerequisites are not fulfilled and the view cannot be persisted
PREREQUISITES_NOT_FULFILLED=The data persistence simulation is not executed for the view "{0}", because prerequisites are not fulfilled and the view cannot be persisted.
#XMSG: Persistency simulation is not executed, because the view does not exist in the database
VIEW_DOES_NOT_EXIST=You must deploy the view "{0}" to enable the data persistence simulation.
#XMSG: Number of records cannot be determined, because the local table does not exist in the database
TABLE_DOES_NOT_EXIST=The local table "{0}" does not exist in the database, therefore the number of records cannot be determined for this table.
##XMSG: Message start of cancellation for View Analyzer task
CANCEL_VIEW_ANALYZER_TRIGGERED=Process to stop View Analyzer task {0} for view "{1}" has been submitted. There may be a delay until the task is stopped.
##XMSG: Message start to indicate that task is not active
CANCEL_VIEW_ANALYZER_NO_ACTIVE_TASK=View Analyzer task {0} for the view "{1}" is not active.
#XMSG: Cancel View Analyzer task failure
CANCEL_VIEW_ANALYZER_FAILED=Failed to cancel View Analyzer task.
##XMSG: Message used in View Analyzer execution task when task is canceled
VIEW_ANALYZER_CANCELED=View Analyzer execution for the view "{0}" was stopped via a cancelation task.
##XMSG: Message start of cancelation for Model Validation task
CANCEL_MODEL_VALIDATION_TRIGGERED=Process to stop Model Validation task {0} for view "{1}" has been submitted. There may be a delay until the task is stopped.
##XMSG: Message to indicate that task is not active
CANCEL_MODEL_VALIDATION_NO_ACTIVE_TASK=Model Validation task {0} for the view "{1}" is not active.
#XMSG: Cancel Model Validation task failure
CANCEL_MODEL_VALIDATION_FAILED=Failed to cancel Model Validation task.
##XMSG: Message used in Model Validation task when task is canceled
MODEL_VALIDATION_CANCELED=Model Validation execution for the view "{0}" was stopped via a cancelation task.
#XMSG: Message for Locked space error, before starting Model Validation task
MODEL_VALIDATION_LOCKED_SPACE_ERROR=Cannot execute Model Validation for the view "{0}", because the space "{1}" is locked.
##XMSG: Message used in View Analyzer execution task when determination of number of rows fails
NUMBER_ROWS_DETERMINATION_FAILED=An error occurred while determining the number of rows for the local table "{0}".
##XMSG: Message to indicate that SQL plan was created
EXECUTION_PLAN_CREATED=SQL Analyzer plan file for the view "{0}" is created and can be downloaded.
#XFLD: Message to indicate starting of process to generate SQL Analyzer plan file
START_PLANVIZ_EXECUTION=Starting process to generate SQL Analyzer plan file for the view "{0}".
#XFLD: Message to indicate starting of View Analyzer process
VIEW_ANALYZER_EXECUTION_STARTED=Starting View Analyzer execution.
##XMSG: Message to indicate that SQL plan file cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_PLANVIZ_NOT_FULFILLED=SQL Analyzer plan file cannot be generated for the view "{0}", because data persistence prerequisites are not fulfilled.`
##XMSG: Error message during  generation of SQL Analyzer plan file
PLANVIZ_ERROR=An error occurred during generation of SQL Analyzer plan file for the view "{0}".
#XMSG: Message for Locked space error
VIEW_ANALYZER_LOCKED_SPACE_ERROR=Cannot execute View Analyzer for the view "{0}", because the space "{1}" is locked.
#XMSG: Message for persistency simulation without partitioning
PERSISTENCY_SIMULATION_WITHOUT_PARTITIONS=Partitions of the view "{0}" are not considered during data persistence simulation.
#XMSG: Message for generation of SQL Analyzer plan file without considering partitioning
PLANVIZ_WITHOUT_PARTITIONS=Partitions of the view "{0}" are not considered during generation of SQL Analyzer plan file.
#XMSG: Confirmation message for mass removal of persisted data
MassRemovePersistency_Confirm_Msg=Do you want to remove the persisted data and switch the data access back to virtual access?
#XMSG: Confirmation message for partial mass removal of selected persisted data
PartialMassRemovePersistency_Confirm_Msg={0} of the {1} selected views have got persisted data. \n Do you want to remove the persisted data and switch the data access back to virtual access?
#XMSG: success message for stopping mass persistence
stopMassPersistenceSuccess=We’re removing persisted data for the selected views.
#XMSG: error message during mass stop persistence of a view (backend error)
STOP_MASS_PERSISTENCE_ERROR=An error occurred while stopping the persistence for the selected views.
#XMSG: memory analysis is only performed in space {0}
NO_MEMORY_ANALYSIS=Memory analysis is performed for entities in space "{0}" only: "{1}" "{2}" has been skipped.
##XMSG: Message to indicate that Explain Plan cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_EXPLAINPLAN_NOT_FULFILLED=Explain Plan file cannot be generated for the view "{0}", because persistency prerequisites are not fulfilled.
#XMSG: Message for Explain Plan generation without partitioning
EXPLAINPLAN_WITHOUT_PARTITIONS=Partitions of the view "{0}" are not considered during generation of Explain Plan file.
#XFLD: Message to indicate starting of process to generate Explain Plan file
START_EXPLAINPLAN_EXECUTION=Starting process to generate Explain Plan file for the view "{0}".
##XMSG: Message to indicate that Explain Plan was created
EXPLAINPLAN_CREATED=Explain Plan file for the view "{0}" has been generated. You can display it clicking "View Details", or download it if you have the relevant permission.
##XMSG: Error message during  generation of Explain Plan file
EXPLAINPLAN_ERROR=An error occurred during generation of Explain Plan file for the view "{0}".
##XMSG: Explain Plan file cannot be generated for the view {0}, because too many views are stacked on each other
EXPLAINPLAN_MODEL_TOO_COMPLEX=Explain Plan file cannot be generated for the view "{0}". Too many views are stacked on each other. Complex models may cause out of memory errors and slow performance. Persisting a view is recommended.

##XMSG: Message to indicate that the space is locked
PERFORMANCE_ANALYSIS_LOCKED_SPACE_ERROR=Cannot execute performance analysis for view "{0}", because space "{1}" is locked.
##XMSG: The performance analysis task was cancelled
PERFORMANCE_ANALYSIS_CANCELED=Performance analysis for the view "{0}" was canceled.
##XMSG: The performance analysis failed
PERFORMANCE_ANALYSIS_FAILED=Performance analysis failed.
##XMSG: The performance analysis is finished
PERFORMANCE_ANALYSIS_FINISHED=Performance analysis for the view "{0}" has finished. Display the result by clicking "View Details".
##XMSG: The view has input parameter with no default value
INPUT_PARAMETER_WITHOUT_DEFAULT_VALUE=This view cannot be analyzed because it has a parameter with no default value.
##XMSG: The view is not fully deployed
VIEW_NOT_FULLY_DEPLOYED=This view cannot be analyzed because it is not fully deployed.
##XMSG: The view uses limited remote adapters
LIMITED_REMOTE_ADAPTER=This view uses at least one remote adapter with limited capabilities such as missing filter pushdown or support for 'Count'. Persisting or replicating objects can improve runtime performance.
##XMSG: The view uses remote adapter that does not support 'Limit'
REMOTE_ADAPTER_NOT_SUPPORTING_LIMIT=This view uses at least one remote adapter that does not support 'Limit'. More than 1000 records may have been selected.
##XMSG: Performance analysis is executed by using default parameters of view'
INPUT_PARAMETER_WITH_DEFAULT_VALUE=The performance analysis is executed by using the default values of view parameters.
##XMSG: Message to indicate that an error occurred
PERFORMANCE_ANALYSIS_ERROR=An error occurred during performance analysis for the view "{0}".
##XMSG: Message start of cancellation for Performance Analysis task
CANCEL_PERFORMANCE_ANALYSIS_TRIGGERED=Process to stop Performance Analysis task {0} for view "{1}" has been submitted. There may be a delay until the task is stopped.
##XMSG: Message start to indicate that task is not active
CANCEL_PERFORMANCE_ANALYSIS_NO_ACTIVE_TASK=Performance Analysis task {0} for the view "{1}" is not active.
#XMSG: Cancel Performance Analysis task failure
CANCEL_PERFORMANCE_ANALYSIS_FAILED=Failed to cancel Performance Analysis task.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Assign Schedule to Me
#XBUT: Pause schedule menu label
pauseScheduleLabel=Pause Schedule
#XBUT: Resume schedule menu label
resumeScheduleLabel=Resume Schedule
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=An error occurred while removing schedules.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=An error occurred while assigning schedules.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=An error occurred while pausing schedules.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=An error occurred while resuming schedules.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Deleting {0} schedules
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Changing the owner of {0} schedules
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Pausing {0} schedules
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Resuming {0} schedules
#XBUT: Select Columns Button
selectColumnsBtn=Select Columns
#XFLD: Refresh tooltip
TEXT_REFRESH=Refresh
#XFLD: Select Columns tooltip
text_selectColumns=Select Columns


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Runtime Metrics ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Label for Runtime Metrics
RuntimeMetricsTitle=Runtime Metrics for
#XFLD : Label for Run Button
runButton=Run
#XFLD : Label for Cancel Button
cancelButton=Cancel
#XFLD : Label for Close Button
closeButton=Close
#XFLD : Label for Open View Analyzer Button
openViewAnalyzerButton=Open View Analyzer
#XFLD : Label for Generate Explain Plan Button
generateExplainPlanButton=Generate Explain Plan
#XFLD : Label for Previous Run Column
previousRun=Previous Run
#XFLD : Label for Latest Run Column
latestRun=Latest Run
#XFLD : Label for time Column
time=Time
#XFLD : Label for Duration Column
duration=Duration
#XFLD : Label for Peak Memory Column
peakMemory=Peak Memory
#XFLD : Label for Number of Rows
numberOfRows=Number of Rows
#XFLD : Label for Overall Number of Sources
overallNumberOfSources=Overall Number of Sources
#XFLD : Label for Data Access Column
dataAccess=Data Access
#XFLD : Label for Local Tables
localTables=Local Tables
#XFLD : Label for federatedRemoteTables
federatedRemoteTables=Federated Remote Tables (with Limited Adapter Capabilities)
#XTXT Text for initial state of the runtime metrics
initialState=You must first run Performance Analysis to get the metrics. This may take some time, but you can cancel the process if necessary.
#XMSG: Cancel confirmation message
CancelRunConfirmationMessage=Are you sure that you want to cancel the current run of Performance Analysis?
#XTIT: Cancel dialog title
CancelRunTitle=Cancel Run
#XFLD: Label for Number of Rows
NUMBER_ROWS=Number of Rows
#XFLD: Label for Overall Number of Sources
OVERALL_NUMBER_SOURCES=Overall Number of Sources
#XFLD: Label for Data Access
DATA_ACCESS=Data Access
#XFLD: Label for Remote tables
NUMBER_REMOTE_TABLES_LIMITED_ADAPTERS=Federated Remote Tables (with Limited Adapter Capabilities)
#XFLD: Label for select statement
SELECT_STATEMENT='SELECT * FROM VIEW LIMIT 1000'
#XFLD: Label for duration
SELECT_RUNTIME=Duration
#XFLD: Label for peak memory
SELECT_PEAK_MEMORY=Peak Memory
#XFLD: Label for count statement
SELECT_COUNT_STATEMENT='SELECT COUNT(*) FROM VIEW'
#XFLD: Label for count duration
SELECT_COUNT_RUNTIME=Duration
#XFLD: Label for count peak memory
SELECT_COUNT_PEAK_MEMORY=Peak Memory
#XFLD: Label for count of number of local tables
NUMBER_LOCAL_TABLES_FILE=Local Tables (File)
#XTXT: Text for running state of the runtime metrics
Running=Running...
#XFLD: Label for time
Time=Time
#XFLD: Label for virtual access
PA_VIRTUAL=Virtual
#XFLD: Label for persisted access
PA_PERSISTED=Persisted
PA_PARTIALLY_PERSISTED=Partially Persisted
#XTXT: Text for cancel
CancelRunSuccessMessage=Cancelling the run of Performance Analysis.
#XTXT: Text for cancel error
CancelRunErrorMessage=An error occurred while canceling the run of Performance Analysis.
#XTXT: Text for explain plan generation
ExplainPlanStarted=Generating Explain Plan for the view "{0}".
#XTXT: Text for performance analysis
PerformanceAnalysisStarted=Starting Performance Analysis for the view "{0}".
#XTXT: Text for performance analysis error
ErrorFetchingPerformanceAnalysis=An error occurred while fetching Performance Analysis data.
#XTXT: Text for performance analysis error
conflictingTask=Performance Analysis task is already running
#XFLD: Label for Errors
Errors=Error(s)
#XFLD: Label for Warnings
Warnings=Warning(s)
#XFLD: Tooltip for missing privileges
openViewAnalyzerButtonTooltipNoPrivilege=You need DWC_DATAINTEGRATION(update) privilege to open the View Analyzer.
#XFLD: Tooltip for missing privileges
generateExplainPlanButtonTooltipNoPrivilege=You need DWC_RUNTIME(read) privilege to generate the Explain Plan.



#XFLD: Label for frequency column
everyLabel=Every
#XFLD: Plural Recurrence text for Hour
hoursLabel=Hours
#XFLD: Plural Recurrence text for Day
daysLabel=Days
#XFLD: Plural Recurrence text for Month
monthsLabel=Months
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minutes
