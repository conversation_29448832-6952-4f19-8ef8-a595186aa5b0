
#XBCB: Breadcrumb
viewMonitorBreadcrumb={spaceId}

#XFLD: Label for persisted view column
SOURCE=Извор
#XFLD: Label for persisted view column
NAME=Назив
#XFLD: Label for persisted view column
NAME_LABEL=Деловен назив
#XFLD: Label for persisted view column
NAME_LABELNew=Објект (деловен назив)
#XFLD: Label for persisted view column
TECHINCAL_NAME=Технички назив
#XFLD: Label for persisted view column
TECHINCAL_NAMENew=Објект (технички назив)
#XFLD: Label for persisted view column
DATA_PERSISTENCY=Пристап до податоци
#XFLD: Label for persisted view column
STATUS=Статус
#XFLD: Label for persisted view column
LAST_UPDATED=Последно ажурирано
#XFLD: Label for persisted view column
IN_MEMORY_SIZE=Меморија искористена за складирање (MiB)
#XFLD: Label for persisted view column
DISK_SIZE=Диск искористен за складирање (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_NEWW=Големина во меморијата (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_HEADER=Големина во меморијата
#XFLD: Label for persisted view column
DISK_SIZE_NEWW=Големина на дискот (MiB)
#XFLD: Label for persisted view column
DISK_SIZE_HEADER=Големина на дискот
#XFLD: Label for schedule owner column
txtScheduleOwner=Сопственик на распоредот
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Покажува кој го создал распоредот
#XFLD: Label for persisted view column
PERSISTED=Трајно зачувано
#XFLD: Label for persisted view column
TYPE=Тип
#XFLD: Label for View Selection Dialog column
changedOn=Променето на
#XFLD: Label for View Selection Dialog column
createdBy=Создадено од
#XFLD: Label for log details column
txtViewPersistencyLogs=Прикажи ги дневниците
#XFLD: Label for log details column
txtViewPersistencyLogsNew=Детали
#XFLD: text for values shown for Ascending sort order
SortInAsc=Подреди по растечки редослед
#XFLD: text for values shown for Descending sort order
SortInDesc=Подреди по опаѓачки редослед
#XFLD: Label for persisted view monitor heading
ViewMonitor_Heading=Алатка за следење прикази
#XFLD: Label for persisted view monitor sub-heading
ViewMonitor_SubHeading=Следење и одржување на трајноста на податоците од приказите


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ DynamicPageHeader ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: text for values shown in column Persistence Status
txtLoading=Се вчитува
#XFLD: text for values shown in column Persistence Status
txtRunning=Се извршува
#XFLD: text for values shown in column Persistence Status
txtAvailable=Достапно
#XFLD: text for values shown in column Persistence Status
txtError=Грешка
#XMSG: Error message for unknown Persistence type that gets logged in Kibana
unknownReplicationTypeError=Типот репликација „{0}“ не е поддржан.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Content ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Поставки што се користат за последно извршување трајно зачувување на податоците:
#XMSG: Message for input parameter name
inputParameterLabel=Влезен параметар
#XMSG: Message for input parameter value
inputParameterValueLabel=Вредност
#XMSG: Message for persisted data
inputParameterPersistedLabel=Трајно зачувано во
#XTIT: Count for View in ViewMonitor Toolbar
txtViewCount=Прикази ({0})
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Трајност на приказот
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Трајност на податоци
#XBUT: Button to clear the selected view persistance
ClearViewPersistance=Исчисти
#XBUT: Button to stop the selected view persistance
stopPersistance=Запри го трајното зачувување
#XFLD: Placeholder for Search field
txtSearch=Пребарај
#XBUT: Tooltip for refresh button
txtRefresh=Освежи
#XBUT: Tooltip for add view button
txtDeleteView=Избриши го трајното зачувување
#XBUT: Tooltip for load new peristence
loadNewPersistence=Престартувај го трајното зачувување на податоците
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Вчитај нова слика на состојба
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Започни со трајно зачувување на податоците
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Отстрани трајно зачувани податоци
#XMSG: success message for starting persistence
startPersistenceSuccess=Трајно го зачувуваме приказот „{0}“.
#XMSG: success message for stopping persistence
stopPersistenceSuccess=Ги отстрануваме трајно зачуваните податоци за приказот „{0}“.
#XMSG: success message for stopping persistence
deletePersistedViewSuccess=Го отстрануваме приказот „{0}“ од списокот за следење.
#XMSG: error message during start persistence of a view (backend error)
ERROR_STARTING_PERSISTENCE=Настана грешка при трајното зачувување на податоците за приказот „{0}“.
#XMSG: error message during start persistence of a view (backend error)
PARAMERROR_STARTING_PERSISTENCE=Приказот „{0}“ не може да се зачува трајно бидејќи содржи влезни параметри.
#XMSG: error message during start persistence of a view (backend error)
NUMBER_OF_PARAMETER_STARTING_PERSISTENCE=Приказот „{0}“ не може да се зачува трајно бидејќи има повеќе влезни параметри.
#XMSG: error message during start persistence of a view (backend error)
PARAMETER_DEFAULT_VALUE_STARTING_PERSISTENCE=Приказот „{0}“ не може да се зачува трајно бидејќи влезниот параметар нема стандардна вредност.
#XMSG: error message during start persistence of a view (backend error)
REDEPLOYMENT_DAC_REQUIRED=Треба повторно да се примени контролата за пристап до податоците (DAC) „{0}“ за да се овозможи трајно зачувување на податоците.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_STARTING_PERSISTENCE=Приказот „{0}“ не може да се зачува трајно бидејќи го користи приказот „{1}“, кој содржи контрола за пристап до податоците (DAC).
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_EXTERNAL_STARTING_PERSISTENCE=Приказот „{0}“ не може да се зачува трајно бидејќи користи приказ со контрола за пристап до податоците (DAC) што припаѓа во друг простор.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_STRUCTURE_STARTING_PERSISTENCE=Приказот „{0}“ не може да се зачува трајно бидејќи структурата на една или повеќе негови контроли за пристап до податоците (DAC) не поддржува трајно зачувување на податоците.
#XMSG: error message during stop persistence of a view (backend error)
STOP_PERSISTENCE_ERROR=Настана грешка при запирање на трајното зачувување за приказот „{0}“.
#XMSG: error message during stop persistence of a view (backend error)
DELETE_PERSISTED_VIEW_ERROR=Настана грешка при бришење на трајно зачуваниот приказ „{0}“.
#XMSG: Warning message for removing the persisted data
RemovePersistency_Confirm_Msg=Дали сакате да ги избришете трајно зачуваните податоци и да се префрлите на виртуелен пристап за приказот „{0}“?
#XMSG: Warning message for delete the view persistence
DeletePersistency_Confirm_Msg=Дали сакате да го отстраните приказот од списокот за следење и да ги избришете трајно зачуваните податоци за приказот „{0}“?
#XMSG: error message for reading data from backend
txtReadBackendError=Изгледа дека настана грешка при читањето од серверскиот дел.
#XFLD: Label for No Data Error
NoDataError=Грешка
#XMSG: message for conflicting task
Task_Already_Running=Конфликтна задача веќе се извршува за приказот „{0}“.

#XFLD: Error while accessing pertitioning for a view
missingPrivilegeInViewPersistency=Недоволна дозвола за извршување на поделбата на партиции за приказот „{0}“

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Show All Views~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Text for show all views button
allViewsText=Сите прикази ({0})
#XBUT: Text for show scheduled views button
scheduledText=Закажани ({0})
#XBUT: Text for show persisted views button
persistedText=Трајни ({0})
#XBUT: Text for start analyzer button
startAnalyzer=Активирај го анализаторот на прикази
#XFLD: Message if repository is unavailable
repositoryErrorMsg=Репозиториумот не е достапен и некои карактеристики се оневозможени

#XFLD: Data Access - Virtual
Virtual=Виртуелно
#XFLD: Data Access - Persisted
Persisted=Трајно зачувано

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Non-Persisted View Selection Dialog~~~~~~~~~~~~~~~~~~~~~~~

#XTIT: Title for Non-Persisted View selection dialog
select_source=Изберете приказ за трајно зачувување

#XTIT: Placeholder for Non-Persisted View selection dialog search
searchView=Пребарај прикази
#XTIT: No data in the list of non-persisted view
No_Data=Нема податоци
#XBUT: Button to select non-persisted view
ok=Во ред
#XBUT: Button to close the non-persisted views selection dialog
cancel=Откажи

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~View persistency log message key~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message for view persistency logs
START_STOP_PERSISTENCY=Започнува извршувањето на задачата за трајно зачувување на податоците за „{1}“.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY=Трајно се зачувуваат податоците за приказот „{1}“.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY)
START_VIEW_PERSISTENCY_PROCESS=Започнува процесот на трајно зачувување на податоците за приказот „{0}“.
#XFLD: Message for view persistency manual partition load
START_VIEW_PERSISTENCY_PROCESS_MANUAL_PARTITIONS=Започнува процесот на трајно зачувување на податоците за приказот „{0}“ со избраните ИД-броеви на партиции „{1}“.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY=Ги отстрануваме трајно зачуваните податоци за приказот „{1}“.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY)
REMOVE_VIEW_PERSISTENCY=Започнува процесот за отстранување на трајно зачуваните податоци за приказот „{0}“.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_SUCCESS=Податоците се зачувани трајно за приказот „{1}“.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_SUCCESS)
START_VIEW_PERSISTENCY_PROCESS_SUCCESS=Податоците се зачувани трајно за приказот „{0}“.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY_SUCCESS=Трајно зачуваните податоци се отстранети и виртуелниот пристап до податоците е обновен за приказот „{1}“.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_SUCCESS)
REMOVE_VIEW_PERSISTENCY_SUCCESS=Заврши процесот за отстранување на трајно зачуваните податоци за приказот „{0}“.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_FAILED=Податоците не може да се зачуваат трајно за приказот „{1}“.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_FAILED)
START_VIEW_PERSISTENCY_PROCESS_FAILED=Податоците не може да се зачуваат трајно за приказот „{0}“.
#XFLD: Message for view persistency logs (for messages created before version 2022.05)
STOP_VIEW_PERSISTENCY_FAILED=Трајно зачуваните податоците не може да се отстранат за приказот „{1}“.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_FAILED)
REMOVE_VIEW_PERSISTENCY_FAILED=Трајно зачуваните податоците не може да се отстранат за приказот „{0}“.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_SUCCESS=„{3}“ записи се зачувани трајно за приказот „{1}“.
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace START_PERSISTENCY_RECORD_COUNT_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS={0} записи се вметнати во табелата со трајно зачувани податоци за приказот „{1}“.
#XFLD: Message used by SEAL repository for view persistency logs (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS_V2={0} записи се вметнати во табелата со трајно зачувани податоци за приказот „{1}“. Искористена меморија: {2} GiB.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_SUCCESS=„{3}“ трајно зачувани записи се отстранети за приказот „{1}“.
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace STOP_PERSISTENCY_RECORD_COUNT_SUCCESS)
REMOVE_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=Трајно зачуваните податоци се отстранети, „{0}“ трајно зачувани записи се избришани.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_FAILED=Повикувањето на recordCount не успеа за приказот „{1}“.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_FAILED=Повикувањето на recordCount не успеа за приказот „{1}“.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Распоредот е избришан за „{1}“.
#XFLD: Message for view persistency logs (new message to replace STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS)
REMOVE_VIEW_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Распоредот е избришан за приказот „{0}“.
#XFLD: Message for view persistency logs
STOP_PERSISTENCY_DELETE_SCHEDULE_FAILED=Бришењето на распоредот не успеа за „{1}“.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_REDEPLOY=Не можеме да го зачуваме приказот „{0}“ трајно бидејќи е променет и применет откако почнавте да го зачувувате. Обидете се повторно да го зачувате приказот трајно или почекајте до следното закажано извршување.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_DELETE=Не можеме да го зачуваме приказот „{0}“ трајно бидејќи е избришан откако почнавте да го зачувувате.
#XFLD: Message for successful data persistency of a single partition (for messages created before version 2022.03)
START_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} записи се зачувани трајно во партиција за вредностите „{1}“ <= {2} < „{3}“.
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (new message to replace START_PERSISTENCY_SINGLE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} записи се вметнати во партиција за вредностите „{1}“ <= „{2}“ < „{3}“.
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS_V2={0} записи се вметнати во партиција за вредностите „{1}“ <= „{2}“ < „{3}“. Искористена меморија: {4} GiB.
#XFLD: Message for successful replication of a "others" partition (for messages created before version 2022.03)
START_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} записи се зачувани трајно во партицијата „други“.
#XFLD: Message used by SEAL repository for successful replication of a "others" partition (new message to replace START_PERSISTENCY_OTHERS_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} записи се вметнати во партицијата „други“.
#XFLD: Message for view persistency record count of partitions (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={3} записи се зачувани трајно за приказот „{1}“ во {4} партиции
#XFLD: Message used by SEAL repository for view persistency record count of partitions (new message to replace START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={0} записи се вметнати во табелата со трајно зачувани податоци за приказот „{1}“ во {2} партиции.
#XFLD: Message used by SEAL repository for view persistency for partition wise refresh
START_VIEW_PERSISTENCY_RECORD_COUNT_REFRESHED_PARTITIONS_SUCCESS={0} записи се вметнати во табелата со трајно зачувани податоци за приказот „{1}“. Ажурирани партиции {2}; Заклучени партиции: {3}; Вкупно партиции: {4}.
#XFLD: Message used by SEAL repository for view persistency for manual partition load
START_VIEW_PERSISTENCY_RECORD_COUNT_MANUAL_PARTITIONS_SUCCESS={0} записи се вметнати во табелата со трајно зачувани податоци за приказот „{1}“ во {2} избрани партиции.
#XFLD: Message used by SEAL repository for view persistency for locked partition data load
START_VIEW_PERSISTENCY_RECORD_COUNT_LOCKED_PARTITIONS_SUCCESS={0} записи се вметнати во табелата со трајно зачувани податоци за приказот „{1}“. Ажурирани партиции {2}; Заклучени и непроменети партиции: {3}; Вкупно партиции: {4}.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
START_PERSISTENCY_UNEXPECTED_ERROR=Настана неочекувана грешка при трајно зачувување на податоците за приказот „{0}“.
#XMSG: Message for unexpected error (new message to replace START_PERSISTENCY_UNEXPECTED_ERROR)
START_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Настана неочекувана грешка при трајно зачувување на податоците за приказот „{0}“.
#XMSG: Message for Locked space error
START_VIEW_PERSISTENCY_LOCKED_SPACE_ERROR=Приказот „{0}“ не може да се зачува трајно бидејќи просторот „{1}“ е заклучен.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
STOP_PERSISTENCY_UNEXPECTED_ERROR=Настана неочекувана грешка при отстранување на трајно зачуваните податоците за приказот „{0}“.
#XMSG: Message for unexpected error (new message to replace STOP_PERSISTENCY_UNEXPECTED_ERROR)
REMOVE_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Настана неочекувана грешка при отстранување на трајното зачувување за приказот „{0}“.
#XMSG: Message for ivalidated view
VIEW_PERSISTENCY_ABORTED_DEFINITION_INVALID=Дефиницијата на приказот „{0}“ стана неважечка поради промена на објект којшто го користи приказот директно или индиректно. Обидете се повторно да го примените приказот за да го решите проблемот или да ја идентификувате основната причина.
#XMSG: Message for persisted data removal (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT=Трајно зачуваните податоци се отстранети при примена на приказот „{0}“.
#XMSG: Message for persisted data removal of a consuming view
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW=Трајно зачуваните податоци се отстранети при примена на искористениот приказ „{0}“.
#XMSG: Message for persisted data removal of a consuming view due to a dac change
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW_DAC_CHANGED=Трајно зачуваните податоци се отстранети при примена на искористениот приказ „{0}“ бидејќи неговата контрола за пристап до податоци е променета.
#XMSG: Message for persisted data removal (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DEPLOYMENT=Трајно зачуваните податоци се отстранети при примена на приказот „{0}“.
#XMSG: Message for deleted view (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION=Ако го избришете приказот „{0}“, ќе се отстрани трајното зачувување.
#XMSG: Message for deleted view (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DELETION=Ако го избришете приказот „{0}“, ќе се отстрани трајното зачувување.
#XMSG Message for removal of persisted data, because prerequisites not fulfilled
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_PREREQUISITES_NOT_FULFILLED=Трајно зачуваните податоци се отстранети бидејќи веќе не се исполнети предусловите за трајно зачувување на податоците.
#XMSG: Message for inconsistent persistency
VIEW_PERSISTENCY_INCONSISTENT_PERSISTENCY=Трајноста на приказот „{0}“ стана недоследна. Отстранете ги трајно зачуваните податоци за да го решите проблемот.
#XMSG: Message for prerequisites
START_PERSISTENCY_CHECK_PREREQUISITES=Ги проверуваме предусловите за трајно зачувување на приказот „{0}“.
#XMSG: Message for DAC deployed in deprecated way using table function and join (for messages created before 2025.05)
START_PERSISTENCY_DEPRECIATED_DAC=Приказот „{0}“ е применет со користење на контролата за пристап до податоци (DAC) што е застарена. Повторно применете го приказот за да ја подобрите ефикасноста.
#XMSG: Message for DAC deployed in deprecated way using table function and join
START_PERSISTENCY_DEPRECATED_DAC=Приказот „{0}“ е применет со користење на контролата за пристап до податоци (DAC) што е застарена. Повторно применете го приказот за да ја подобрите ефикасноста.
#XMSG: Message for roll back (for messages created before version 2022.03)
ROLLBACK_VIEW_PERSISTENCY=Настана грешка. Претходната состојба на трајност е обновена за приказот „{0}“.
#XMSG: Message used by SEAL repository for rollback of running procedure to persist view (new message to replace ROLLBACK_VIEW_PERSISTENCY)
VIEW_PERSISTENCY_ROLLBACK=Настана грешка. Процесот на трајно зачувување на приказот „{0}“ е запрен и промените се отфрлени.
#XMSG: Message used by SEAL repository for rollback of running procedure to remove persisted data of view
REMOVE_VIEW_PERSISTENCY_ROLLBACK=Настана грешка. Процесот за отстранување на трајно зачуваните податоци за приказот „{0}“ е запрен и промените се отфрлени.
#XMSG: Message used by SEAL repository for persistency preparation
START_PERSISTENCY_PREPARATION=Се подготвуваме за трајно зачувување на податоците.
#XMSG: Message used by SEAL repository for data insertion
START_INSERTING_PERSISTENCY_DATA=Се вметнуваат податоци во табелата за трајност.
#XMSG: Message for null values - partitioning (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS={0} записи со нулта вредност се вметнати во партицијата „други“.
#XMSG: Message used by SEAL repository for others partition values - partitioning (new message to replace START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS={0} записи се вметнати во партицијата „други“ за вредностите „{2}“ < „{1}“ ИЛИ „{2}“ >= „{3}“.
#XMSG: Message used by SEAL repository for others partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS_V2={0} записи се вметнати во партицијата „други“ за вредностите „{2}“ < „{1}“ ИЛИ „{2}“ >= „{3}“. Искористена меморија: {4} GiB.
#XMSG: Message used by SEAL repository for null partition values - partitioning
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS={0} записи се вметнати во партицијата „други“ за вредностите „{1}“ Е NULL.
#XMSG: Message used by SEAL repository for null partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS_V2={0} записи се вметнати во партицијата „други“ за вредностите „{1}“ Е NULL. Искористена меморија: {2} GiB.
##XFLD:Message used by SEAL repository for remote statistics
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS=Се вчитуваат вклучените податоци: {0} наредби на далечина. Вкупно преземени записи: {1}. Вкупно времетраење: {2} секунди.
##XFLD:Message used by SEAL repository for remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS_WITH_PARTITIONS=Се вчитуваат вклучените податоци со користење на {0} партиции со {1} наредби на далечина. Вкупно преземени записи: {2}. Вкупно времетраење: {3} секунди.
##XFLD:Message used by SEAL repository with UI ID for instructions of remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_INSTRUCTION=Наредбите на далечина обработени за време на извршувањето може да се прикажат со отворање на алатката за следење прашалник на далечина во деталите за пораките специфични за партицијата.
##XFLD:Message for start reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS=Започнува процесот на повторно користење на постојните трајно зачувани податоци за приказот {0} по примената.
##XFLD:Message used by SEAL repository for reattach view persitency
START_VIEW_PERSISTENCY_REATTACHMENT=Започни со повторно користење на постојните трајно зачувани податоци.
##XFLD:Message used by SEAL repository for reattach view persitency
START_FINILIZING_VIEW_PERSISTENCY_REATTACHMENT=Постојните трајно зачувани податоци се користат повторно.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_COMPLETE=Процесот на повторно користење на постојните трајно зачувани податоци заврши за приказот {0}.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_FAILED=Повторното користење на постојните трајно зачувани податоци не успеа за приказот {0} по примената.
#XMSG: Message used by SEAL repository for finalizing persistency
START_FINALIZING_PERSISTENCY=Се завршува трајното зачувување.
#XMSG: Message used by SEAL repository for restoring virtual access for view
REMOVE_VIEW_PERSISTENCY_VIRTUAL_ACCESS_RESTORED=Виртуелниот пристап до податоците е обновен за приказот „{0}“.
#XMSG: Message used by SEAL repository for view that already has virtual access
REMOVE_VIEW_PERSISTENCY_ALREADY_VIRTUAL_ACCESS=Приказот „{0}“ веќе има виртуелен пристап до податоците. Трајно зачуваните податоци не се отстранети.
#XMSG: Message for removing view persistency
VIEW_PERSISTENCY_REMOVED_FROM_MONITOR=Приказот „{0}“ е отстранет од алатката за следење прикази.
#XMSG: Message for view doesnt exist.
VIEW_PERSISTENCY_VIEW_NOT_EXISTING=Приказот „{0}“ или не постои во базата на податоци или не е правилно применет и не може да се зачува трајно. Обидете се повторно да го примените приказот за да го решите проблемот или да ја идентификувате основната причина.
#XMSG: Message for persistency not enabled.
VIEW_PERSISTENCY_NOT_ENABLED=Трајното зачувување на податоците не е овозможено. Повторно применете ја табелата/приказот во просторот „{0}“ за да ја овозможите функционалноста.
#XMSG: Message for unexpected error due to node crash
VIEW_PERSISTENCY_UNEXPECTED_ERROR_GENERAL=Последното трајно зачувување на приказот се прекина поради технички грешки.
#XFLD: Message for view persistency memory consumption
VIEW_PERSISTENCY_MEMORY_CONSUMPTION=Искористена е максимална меморија од {0} GiB во текот на трајното зачувување на приказот.
#XFLD: Message for view persistency timeout
VIEW_PERSISTENCY_TIMEOUT=Истече времето за трајното зачувување на приказот {0} од {1} часа.
#XFLD: Message for not started asynchronous view persistency
VIEW_PERSISTENCY_ASYNC_NOT_STARTED=Големото оптоварување на системот спречи асинхроно започнување на процесот за трајно зачувување на приказот. Проверете дали премногу задачи се извршуваат паралелно.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency (for messages created before version 2022.05)
START_VIEW_PERSISTENCY_EXECUTION_METHOD_DROP_AND_RENAME=Постојната трајно зачувана табела е избришана и заменета со нова трајно зачувана табела.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_RECREATE_AND_DROP=Постојната трајно зачувана табела е избришана и заменета со нова трајно зачувана табела.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_TRUNCATE_AND_COPY=Постојната трајно зачувана табела е ажурирана со нови податоци.
##XFLD: Message for missing authorizations for view persistency
VIEW_PERSISTENCY_MISSING_AUTH=Недостигаат овластувања за трајно зачувување на податоците.
##XFLD: Message start of persistency cancellation
VIEW_PERSISTENCY_TRIGGER_CANCEL=Започнува откажувањето на процесот за трајно зачувување на приказот {0}.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_UNAVAILABLE_TASK_RUN=Откажувањето на процесот за трајно зачувување на приказот {0} не успеа бидејќи не се извршува задача за негово трајно зачувување.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_NONDETERMINISTIC_TASK_RUN=Откажувањето на процесот за трајно зачувување на приказот {0} не успеа бидејќи не се извршува задача за негово трајно зачувување.
##XFLD: Message for canceling a non running persitency task
VIEW_PERSISTENCY_CANCEL_NON_RUNNING_SELECTED_TASK=Откажувањето на процесот за трајно зачувување на приказот {0} не успеа бидејќи не се извршува избраната задача за трајно зачувување на податоците {1}.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_UNREACHABLE_PERSISTENCY_RUN=Откажувањето на процесот за трајно зачувување на приказот {0} не успеа бидејќи сѐ уште не е почнато неговото трајно зачувување.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_COMPLETED_PERSISTENCY_RUN=Откажувањето на процесот за трајно зачувување на приказот „{0}“ не успеа бидејќи е веќе завршен.
##XFLD: Message for failed cancellation
VIEW_PERSISTENCY_ABORTED_CANCEL_EXECUTION=Откажувањето на процесот за трајно зачувување на приказот {0} не успеа.
##XFLD: Message for finalizing persistency cancellation
VIEW_PERSISTENCY_FINALIZE_CANCEL=Процесот за запирање на трајното зачувување на податоците за приказот {0} е поднесен.
##XFLD: Message used in persist task when cancellation is caught
VIEW_PERSISTENCY_CANCELED=Процесот на трајно зачувување на приказот {0} е запрен.
##XFLD: Message used in persist task when cancelation is caught and performed through a cancelation task
VIEW_PERSISTENCY_CANCELED_VIA_CANCEL_TASK=Процесот на трајно зачувување на приказот {0} е запрен преку задачата за откажување {1}.
##XFLD: Message used in cancel task when the task is triggered by deployment
VIEW_PERSISTENCY_CANCEL_TRIGGERED_BY_DEPLOYMENT=Се откажува процесот за трајно зачувување на податоците при примена на приказот {0}.
##XFLD: Message used in case the cancelation is retriggered for a running task
VIEW_PERSISTENCY_CANCEL_ATTEMPT_RETRIGGER_CANCEL=Веќе е поднесена претходна задача за откажување на трајното зачувување на приказот {0}.
##XFLD: Message used to express the delay that may occur before the persistency task is stopped
VIEW_PERSISTENCY_CANCEL_DELAY_STOPPING_PERSISTENCY=Може да има доцнење додека да запре задачата за трајно зачувување на податоците за приказот {0}.
##XFLD: Message used to reflect the task log identifier of the current running persistency task
VIEW_PERSISTENCY_CANCEL_CURRENT_RUNNING_TASK=Податоците за приказот {0} се зачувуваат трајно со помош на задачата {1}.
##XFLD:Message used by SEAL repository for locked partitions and DACs
VIEW_PERSISTENCY_LOCKED_PARTITIONS_DACS=Овластувањата дадени од DAC може да се променети и заклучените партиции не ги земаат предвид. Отклучете ги партициите и вчитајте нова слика на состојбата за да ги примените промените.
##XFLD:Message used by SEAL repository for column structure change
VIEW_PERSISTENCY_COLUMN_STRUCTURE_CHANGE=Структурата на колоната е променета и веќе не одговара на постојната табела со трајно зачувани податоци. Отстранете ги трајно зачуваните податоци и започнете со ново трајно зачувување на податоците за да ја ажурирате табелата.
##XFLD: Message for out of memory
VIEW_PERSISTENCY_HANA_OUT_OF_MEMORY=Задачата не успеа поради грешка во недостаток на меморија во базата со податоци SAP HANA.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_EXCEPTION_TRANSACTION_ROLLBACK=Задачата не успеа поради интерен исклучок во базата на податоци од SAP HANA.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_SQL_EXECUTION_ISSUE=Задачата не успеа поради интерен исклучок поврзан со проблем со извршувањето на SQL во базата на податоци од SAP HANA.
##XFLD: Message for HANA out of memory result
VIEW_PERSISTENCY_OUT_OF_MEMORY_RESULT_MESSAGE=Причина за настан со немање меморија во HANA: {0}
##XFLD: Message for admission control rejection
VIEW_PERSISTENCY_HANA_ADMISSION_CTRL_REJECTION=Задачата не успеа поради одбивање на контролите кон SAP HANA.
##XFLD: Message for HANA back pressure / rate limit of HANA client
VIEW_PERSISTENCY_HANA_CLIENT_RATE_LIMIT_REJECTION=Задачата не успеа поради премногу активни врски на SAP HANA.
#XMSG: Message for invalid persisted table name
VIEW_PERSISTENCY_PERSISTED_TABLE_INVALID=Настана грешка и трајно зачуваната табела стана неважечка. За да го решите проблемот, отстранете ги трајно зачуваните податоци и трајно зачувајте го приказот повторно.
#XMSG: Message for usahe of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION=Приказот не може трајно да се зачува. Користи табела на далечина што е заснована на далечински извор со овозможено пропагирање корисници. Проверете го потеклото на приказот.
#XMSG: Message for dynamic usage of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION_DYNAMIC=Приказот не може трајно да се зачува. Користи табела на далечина што е заснована на далечински извор со овозможено пропагирање корисници. Табелата на далечина може да се употребува динамички преку приказ на SQL-скрипта. Потеклото на овој приказ може да не ја покаже табелата на далечина.
#XMSG: Message for insufficient privileges or consumption of view with DAC via dynamic SQL
VIEW_PERSISTENCY_INSUF_PRIV_OR_DAC_DYN_SQL=Привилегиите може да не се доволни. Отворете го Прегледот на податоци за да видите дали ги имате потребните привилегии. Ако да, можеби е применета контрола за пристап до податоци (DAC) на вториот приказ што се користи преку динамична SQL-скрипта.
#XMSG: deployment of DAC is deprecated and view need to be redeployed to be able to persist data
VIEW_PERSISTENCY_DEPRECATED_DAC=Приказот „{0}“ е применет со користење на контролата за пристап до податоци (DAC) што е застарена. Повторно применете го приказот за да може трајно да зачувувате податоци за него.
##XFLD:Message used by SEAL repository for input parameter
START_VIEW_PERSISTENCY_INPUT_PARAMETER=Се користи стандардната вредност „{0}“ за влезниот параметар „{1}“.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Elastic compute node keys~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
##XFLD:Message used to reflect disablement of ecn replica
ECN_DISABLE_EXISTING_REPLICA=Репликата на јазолот за еластично пресметување е оневозможена.
##XFLD:Message used to reflect recreation of ecn replica
ECN_RECREATE_EXISTING_REPLICA=Репликата на јазолот за еластично пресметување е повторно создадена.
##XFLD:Message used to reflect re-enablement of ecn replica
ECN_ENABLE_EXISTING_REPLICA=Репликата на јазолот за еластично пресметување е повторно овозможена.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Task Schedule Menu key~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message to schedule task
scheduleText=Распоред
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Создај распоред
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Уреди го распоредот
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Избриши го распоредот
#XFLD: Refresh frequency field
refreshFrequency=Освежи ја зачестеноста
#XFLD: Refresh frequency field
refreshFrequencyNew=Зачестеност
#XFLD: Refresh frequency field
refreshFrequencyNewNew=Распоредена зачестеност
#XBUT: label for None
none=Ништо
#XBUT: label for Real-Time replication state
realtime=Реално време
#XFLD: Label for table column
txtNextSchedule=Следно извршување
#XFLD: Label for table column
txtNextScheduleNew=Закажано следно извршување
#XFLD: Label for table column
txtNumOfRecords=Број записи
#XFLD: Label for scheduled link
scheduledTxt=Закажано
#XFLD: LABEL for partially persisted link
partiallyPersisted=Делумно трајно зачувано
#XFLD: Text for paused text
paused=Паузирано

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ View Monitor Tasklog Details Action keys~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message for release lock actionn
releaseLockMsg=Ако извршувањето трае подолго од вообичаеното, тоа може да значи дека не успеало и дека статусот не е соодветно ажуриран. \r\n За да го решите проблемот, може да ја отстраните блокадата и да го поставите статусот на Неуспешно.
#XFLD: Label for release lock dialog
releaseLockText=Отстрани ја блокадата

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Column header tooltip ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: tooltip for table column
txtViewTooltip=Назив на трајно зачуваниот приказ
#XFLD: tooltip for table column
txtViewDataAccessTooltip=Укажува на достапноста на приказот
#XFLD: tooltip for table column
txtViewRefreshFreqTooltip=Укажува дали е дефиниран распоред за приказот
#XFLD: tooltip for table column
txtViewStatusTooltip=Добиј го статусот на трајно зачуваниот приказ
#XFLD: tooltip for table column
txtViewLatestUpdateTooltip=Дава информации за последното ажурирање на трајно зачуваниот приказ
#XFLD: tooltip for table column
txtViewNextRunTooltip=Ако распоредот е поставен за приказот, погледнете до кога е планирано следното извршување
#XFLD: tooltip for table column
txtViewNumOfRecordsTooltip=Следете го бројот на записи.
#XFLD: tooltip for table column
txtViewInMemorySizeTooltip=Следете колку простор во меморијата користи приказот
#XFLD: tooltip for table column
txtViewDiskSizeTooltip=Следете колку простор на дискот зафаќа приказот
#XMSG: Expired text
txtExpired=Истечено

#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Објектот „{0}“ не може да се додаде во синџирот од задачи.

#~~~~~~~~~~~View Analyzer task log messages~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message indicating number of records for a view, and memory consumption in Mebibyte during simulation of view persistency
RECORDS_PEAK_MEMORY=Приказот „{0}“ има {1} записи. Симулацијата на трајноста на податоците за приказот искористи {2} MiB од меморијата.
#XMSG: View Analyzer execution failure
VIEW_ANALYZER_EXECUTION_FAILED=Анализаторот на прикази не успеа да се изврши.
#XMSG: Missing authorizations for View Analyzer
VIEW_ANALYZER_MISSING_AUTH=Недостигаат овластувања за анализаторот на прикази.
#XMSG: Maximum memory in Gibibyte is reached, therefore persistency simulation is stopped
MAXIMUM_MEMORY_REACHED=Искористена е максималната меморија од {0} GiB при симулирање на трајноста на податоците за приказот „{1}“. Затоа нема да се извршуваат понатамошни симулации на трајноста на податоците.
#XMSG: Error during persistency simulation occurred
PERSISTENCY_SIMULATION_ERROR=Настана грешка при симулирање на трајноста на податоците за приказот „{0}“.
#XMSG: Persistency simulation is not executed, because prerequisites are not fulfilled and the view cannot be persisted
PREREQUISITES_NOT_FULFILLED=Симулирањето на трајноста на податоците не е извршено за приказот „{0}“ бидејќи предусловите не се исполнети и приказот не може трајно да се зачува.
#XMSG: Persistency simulation is not executed, because the view does not exist in the database
VIEW_DOES_NOT_EXIST=Морате да го примените приказот „{0}“ за да овозможите симулација на трајното зачувување на податоците.
#XMSG: Number of records cannot be determined, because the local table does not exist in the database
TABLE_DOES_NOT_EXIST=Локалната табела „{0}“ не постои во базата на податоци, па затоа не може да се одреди бројот на записи за неа.
##XMSG: Message start of cancellation for View Analyzer task
CANCEL_VIEW_ANALYZER_TRIGGERED=Процесот за запирање на задачата на анализаторот на прикази {0} за приказот „{1}“ е поднесен. Може да има доцнење додека да запре задачата.
##XMSG: Message start to indicate that task is not active
CANCEL_VIEW_ANALYZER_NO_ACTIVE_TASK=Задачата на анализаторот на прикази {0} за приказот „{1}“ не е активна.
#XMSG: Cancel View Analyzer task failure
CANCEL_VIEW_ANALYZER_FAILED=Откажувањето на задачата на анализаторот на прикази не успеа.
##XMSG: Message used in View Analyzer execution task when task is canceled
VIEW_ANALYZER_CANCELED=Извршувањето на анализаторот на приказот „{0}“ е запрен со задача за откажување.
##XMSG: Message start of cancelation for Model Validation task
CANCEL_MODEL_VALIDATION_TRIGGERED=Процесот за запирање на задачата за потврдување на моделот {0} за приказот „{1}“ е поднесен. Може да има доцнење додека да запре задачата.
##XMSG: Message to indicate that task is not active
CANCEL_MODEL_VALIDATION_NO_ACTIVE_TASK=Задачата за потврдување на моделот {0} за приказот „{1}“ не е активна.
#XMSG: Cancel Model Validation task failure
CANCEL_MODEL_VALIDATION_FAILED=Задачата за потврдување на моделот не успеа да се откаже.
##XMSG: Message used in Model Validation task when task is canceled
MODEL_VALIDATION_CANCELED=Извршувањето на потврдувањето на моделот за приказот „{0}“ е запрено со задача за откажување.
#XMSG: Message for Locked space error, before starting Model Validation task
MODEL_VALIDATION_LOCKED_SPACE_ERROR=Потврдувањето на моделот не може да се изврши за приказот „{0}“ бидејќи просторот „{1}“ заклучен.
##XMSG: Message used in View Analyzer execution task when determination of number of rows fails
NUMBER_ROWS_DETERMINATION_FAILED=Настана грешка при определување на бројот на редови за локалната табела „{0}“.
##XMSG: Message to indicate that SQL plan was created
EXECUTION_PLAN_CREATED=Датотеката за планот на SQL-анализаторот за приказот „{0}“ е создадена и може да се преземе.
#XFLD: Message to indicate starting of process to generate SQL Analyzer plan file
START_PLANVIZ_EXECUTION=Започнува процесот на генерирање на датотеката за планот на SQL-анализаторот за приказот „{0}“.
#XFLD: Message to indicate starting of View Analyzer process
VIEW_ANALYZER_EXECUTION_STARTED=Започнува извршувањето на анализаторот на прикази.
##XMSG: Message to indicate that SQL plan file cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_PLANVIZ_NOT_FULFILLED=Датотеката за планот на SQL-анализаторот не може да се генерира за приказот „{0}“, бидејќи предусловите за трајност на податоците не се исполнети.
##XMSG: Error message during  generation of SQL Analyzer plan file
PLANVIZ_ERROR=Настана грешка при генерирање на датотеката за планот на SQL-анализаторот за приказот „{0}“.
#XMSG: Message for Locked space error
VIEW_ANALYZER_LOCKED_SPACE_ERROR=Анализаторот на прикази не може да се изврши за приказот „{0}“ бидејќи просторот е „{1}“ заклучен.
#XMSG: Message for persistency simulation without partitioning
PERSISTENCY_SIMULATION_WITHOUT_PARTITIONS=Партициите на приказот „{0}“ не се земаат предвид при симулирање на трајноста на податоците.
#XMSG: Message for generation of SQL Analyzer plan file without considering partitioning
PLANVIZ_WITHOUT_PARTITIONS=Партициите на приказот „{0}“ не се земаат предвид при генерирање датотека за планот на SQL-анализаторот.
#XMSG: Confirmation message for mass removal of persisted data
MassRemovePersistency_Confirm_Msg=Дали сакате да ги отстраните трајно зачуваните податоци и да го префрлите пристапот до податоци на виртуелен?
#XMSG: Confirmation message for partial mass removal of selected persisted data
PartialMassRemovePersistency_Confirm_Msg={0} од избраните {1} прикази имаат трајно зачувани податоци. \n Дали сакате да ги отстраните трајно зачуваните податоци и да го префрлите пристапот до податоци на виртуелен?
#XMSG: success message for stopping mass persistence
stopMassPersistenceSuccess=Ги отстрануваме трајно зачуваните податоци за избраните прикази.
#XMSG: error message during mass stop persistence of a view (backend error)
STOP_MASS_PERSISTENCE_ERROR=Настана грешка при запирање на трајното зачувување за избраните прикази.
#XMSG: memory analysis is only performed in space {0}
NO_MEMORY_ANALYSIS=Анализирањето на меморијата се врши само за ентитетите во просторот „{0}“: „{1}“ и „{2}“ се прескокнати.
##XMSG: Message to indicate that Explain Plan cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_EXPLAINPLAN_NOT_FULFILLED=Датотеката за Објасни го планот не може да се генерира за приказот „{0}“, бидејќи предусловите за трајност се исполнети.
#XMSG: Message for Explain Plan generation without partitioning
EXPLAINPLAN_WITHOUT_PARTITIONS=Партициите на приказот „{0}“ не се земаат предвид при генерирање датотека за Објасни го планот.
#XFLD: Message to indicate starting of process to generate Explain Plan file
START_EXPLAINPLAN_EXECUTION=Започнува процесот на генерирање на датотеката за Објасни го планот за приказот „{0}“.
##XMSG: Message to indicate that Explain Plan was created
EXPLAINPLAN_CREATED=Датотеката Објасни го планот за приказот „{0}“ е генерирана. Можете да ја прикажете со кликнување на „Прикажи ги деталите“ или да ја преземете ако ја имате соодветната дозвола.
##XMSG: Error message during  generation of Explain Plan file
EXPLAINPLAN_ERROR=Настана грешка при генерирање на датотеката за Објасни го планот за приказот „{0}“.
##XMSG: Explain Plan file cannot be generated for the view {0}, because too many views are stacked on each other
EXPLAINPLAN_MODEL_TOO_COMPLEX=Датотеката Објасни го планот не може да се генерира за приказот „{0}“. Премногу прикази се наредени еден врз друг. Сложените модели можат да предизвикаат грешки во меморијата и бавно работење. Се препорачува трајно да го зачувате приказот.

##XMSG: Message to indicate that the space is locked
PERFORMANCE_ANALYSIS_LOCKED_SPACE_ERROR=Не може да се изврши анализа на перформансот за приказот „{0}“ бидејќи просторот „{1}“ е заклучен.
##XMSG: The performance analysis task was cancelled
PERFORMANCE_ANALYSIS_CANCELED=Анализата на перформансот за приказот „{0}“ е откажана.
##XMSG: The performance analysis failed
PERFORMANCE_ANALYSIS_FAILED=Анализата на перформансот не успеа.
##XMSG: The performance analysis is finished
PERFORMANCE_ANALYSIS_FINISHED=Анализата на перформансот за приказот „{0}“ е завршена. Прикажете го резултатот така што ќе кликнете на „Прикажи детали“.
##XMSG: The view has input parameter with no default value
INPUT_PARAMETER_WITHOUT_DEFAULT_VALUE=Приказов не може да се анализира бидејќи има еден параметар без стандардна вредност.
##XMSG: The view is not fully deployed
VIEW_NOT_FULLY_DEPLOYED=Приказов не може да се анализира бидејќи не е целосно применет.
##XMSG: The view uses limited remote adapters
LIMITED_REMOTE_ADAPTER=Овој приказ користи најмалку еден далечински адаптер со ограничени способности, како што е немањето филтер за pushdown или поддршка за „Броење“. Трајното зачувување или репликација на објекти може да го подобри учинокот на времето за извршување.
##XMSG: The view uses remote adapter that does not support 'Limit'
REMOTE_ADAPTER_NOT_SUPPORTING_LIMIT=Приказов користи најмалку еден адаптер на далечина што не поддржува „Ограничување“. Можеби се избрани повеќе од 1000 записи.
##XMSG: Performance analysis is executed by using default parameters of view'
INPUT_PARAMETER_WITH_DEFAULT_VALUE=Анализата на перформансот се извршува со користење на стандардните вредности на параметрите за приказ.
##XMSG: Message to indicate that an error occurred
PERFORMANCE_ANALYSIS_ERROR=Настана грешка при анализата на учинокот за приказот „{0}“.
##XMSG: Message start of cancellation for Performance Analysis task
CANCEL_PERFORMANCE_ANALYSIS_TRIGGERED=Процесот за запирање на задачата за анализа на перформансот {0} за приказот „{1}“ е поднесен. Може да има доцнење додека да запре задачата.
##XMSG: Message start to indicate that task is not active
CANCEL_PERFORMANCE_ANALYSIS_NO_ACTIVE_TASK=Задачата на анализа на перформансот {0} за приказот „{1}“ не е активна.
#XMSG: Cancel Performance Analysis task failure
CANCEL_PERFORMANCE_ANALYSIS_FAILED=Откажувањето на задачата за анализа на перформансот не успеа.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Додели ми го распоредот
#XBUT: Pause schedule menu label
pauseScheduleLabel=Паузирај го распоредот
#XBUT: Resume schedule menu label
resumeScheduleLabel=Продолжи го распоредот
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Настана грешка при отстранување на распоредите.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Настана грешка при доделување на распоредите.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Настана грешка при паузирање на распоредите.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Настана грешка при продолжување на распоредите.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Се бришат {0} распореди
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Се менува сопственикот на {0} распореди
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Се паузираат {0} распореди
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Се продолжуваат {0} распореди
#XBUT: Select Columns Button
selectColumnsBtn=Избери колони
#XFLD: Refresh tooltip
TEXT_REFRESH=Освежи
#XFLD: Select Columns tooltip
text_selectColumns=Избери колони


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Runtime Metrics ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Label for Runtime Metrics
RuntimeMetricsTitle=Метрика на времето за извршување за
#XFLD : Label for Run Button
runButton=Изврши
#XFLD : Label for Cancel Button
cancelButton=Откажи
#XFLD : Label for Close Button
closeButton=Затвори
#XFLD : Label for Open View Analyzer Button
openViewAnalyzerButton=Отвори го анализаторот на прикази
#XFLD : Label for Generate Explain Plan Button
generateExplainPlanButton=Генерирај ја датотеката Објасни го планот
#XFLD : Label for Previous Run Column
previousRun=Претходно извршување
#XFLD : Label for Latest Run Column
latestRun=Последно извршување
#XFLD : Label for time Column
time=Време
#XFLD : Label for Duration Column
duration=Времетраење
#XFLD : Label for Peak Memory Column
peakMemory=Максимална меморија
#XFLD : Label for Number of Rows
numberOfRows=Број pедови
#XFLD : Label for Overall Number of Sources
overallNumberOfSources=Вкупен број извори
#XFLD : Label for Data Access Column
dataAccess=Пристап до податоци
#XFLD : Label for Local Tables
localTables=Локални табели
#XFLD : Label for federatedRemoteTables
federatedRemoteTables=Заеднички табели на далечина (со ограничени можности на адаптер)
#XTXT Text for initial state of the runtime metrics
initialState=Мора прво да ја извршите анализата на учинокот за да ја добиете метриката. Ова може да потрае, но може да го откажете процесот по потреба.
#XMSG: Cancel confirmation message
CancelRunConfirmationMessage=Дали сигурно сакате да го откажете тековното извршување на анализата на учинокот?
#XTIT: Cancel dialog title
CancelRunTitle=Откажи го извршувањето
#XFLD: Label for Number of Rows
NUMBER_ROWS=Број pедови
#XFLD: Label for Overall Number of Sources
OVERALL_NUMBER_SOURCES=Вкупен број извори
#XFLD: Label for Data Access
DATA_ACCESS=Пристап до податоци
#XFLD: Label for Remote tables
NUMBER_REMOTE_TABLES_LIMITED_ADAPTERS=Заеднички табели на далечина (со ограничени можности на адаптер)
#XFLD: Label for select statement
SELECT_STATEMENT=„ИЗБЕРИ * ОД ОГРАНИЧУВАЊЕТО ЗА ПРИКАЗ 1000“
#XFLD: Label for duration
SELECT_RUNTIME=Времетраење
#XFLD: Label for peak memory
SELECT_PEAK_MEMORY=Максимална меморија
#XFLD: Label for count statement
SELECT_COUNT_STATEMENT=„ИЗБЕРИ БРОЕЊЕ(*) ОД ПРИКАЗОТ“
#XFLD: Label for count duration
SELECT_COUNT_RUNTIME=Времетраење
#XFLD: Label for count peak memory
SELECT_COUNT_PEAK_MEMORY=Максимална меморија
#XFLD: Label for count of number of local tables
NUMBER_LOCAL_TABLES_FILE=Локални табели (датотека)
#XTXT: Text for running state of the runtime metrics
Running=Се извршува...
#XFLD: Label for time
Time=Време
#XFLD: Label for virtual access
PA_VIRTUAL=Виртуелно
#XFLD: Label for persisted access
PA_PERSISTED=Трајно зачувано
PA_PARTIALLY_PERSISTED=Делумно трајно зачувано
#XTXT: Text for cancel
CancelRunSuccessMessage=Се откажува извршувањето на анализата на учинокот.
#XTXT: Text for cancel error
CancelRunErrorMessage=Настана грешка при откажување на извршувањето на анализата на учинокот.
#XTXT: Text for explain plan generation
ExplainPlanStarted=Се генерира датотеката Објасни го планот за приказот „{0}“.
#XTXT: Text for performance analysis
PerformanceAnalysisStarted=Започнува анализа на учинокот за приказот „{0}“.
#XTXT: Text for performance analysis error
ErrorFetchingPerformanceAnalysis=Настана грешка при земање на деталите за анализата на учинокот.
#XTXT: Text for performance analysis error
conflictingTask=Задачата за анализа на учинокот веќе се извршува
#XFLD: Label for Errors
Errors=Грешка(и)
#XFLD: Label for Warnings
Warnings=Предупредување(и)
#XFLD: Tooltip for missing privileges
openViewAnalyzerButtonTooltipNoPrivilege=Ви треба привилегијата DWC_DATAINTEGRATION(update) за да го отворите анализаторот на прикази.
#XFLD: Tooltip for missing privileges
generateExplainPlanButtonTooltipNoPrivilege=Ви треба привилегијата DWC_RUNTIME(read) за да ја генерерирате датотеката Објасни го планот



#XFLD: Label for frequency column
everyLabel=Секој
#XFLD: Plural Recurrence text for Hour
hoursLabel=Часови
#XFLD: Plural Recurrence text for Day
daysLabel=Денови
#XFLD: Plural Recurrence text for Month
monthsLabel=Месеци
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Минути
