
#XBCB: Breadcrumb
viewMonitorBreadcrumb={spaceId}

#XFLD: Label for persisted view column
SOURCE=Bron
#XFLD: Label for persisted view column
NAME=Naam
#XFLD: Label for persisted view column
NAME_LABEL=Objectnaam
#XFLD: Label for persisted view column
NAME_LABELNew=Object (bedrijfsnaam)
#XFLD: Label for persisted view column
TECHINCAL_NAME=Technische naam
#XFLD: Label for persisted view column
TECHINCAL_NAMENew=Object (technische naam)
#XFLD: Label for persisted view column
DATA_PERSISTENCY=Toegang tot gegevens
#XFLD: Label for persisted view column
STATUS=Status
#XFLD: Label for persisted view column
LAST_UPDATED=Laatst bijgewerkt
#XFLD: Label for persisted view column
IN_MEMORY_SIZE=Geheugen gebruikt voor opslag (MiB)
#XFLD: Label for persisted view column
DISK_SIZE=Schijfruimte gebruikt voor opslag (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_NEWW=Grootte in-memory (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_HEADER=Grootte in-memory
#XFLD: Label for persisted view column
DISK_SIZE_NEWW=Grootte op schijf (MiB)
#XFLD: Label for persisted view column
DISK_SIZE_HEADER=Grootte op schijf
#XFLD: Label for schedule owner column
txtScheduleOwner=Planningseigenaar
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Geeft weer wie planning heeft gecreëerd
#XFLD: Label for persisted view column
PERSISTED=Persistent
#XFLD: Label for persisted view column
TYPE=Type
#XFLD: Label for View Selection Dialog column
changedOn=Gewijzigd op
#XFLD: Label for View Selection Dialog column
createdBy=Gemaakt door
#XFLD: Label for log details column
txtViewPersistencyLogs=Logs weergeven
#XFLD: Label for log details column
txtViewPersistencyLogsNew=Details
#XFLD: text for values shown for Ascending sort order
SortInAsc=Oplopend sorteren
#XFLD: text for values shown for Descending sort order
SortInDesc=Aflopend sorteren
#XFLD: Label for persisted view monitor heading
ViewMonitor_Heading=Monitor views
#XFLD: Label for persisted view monitor sub-heading
ViewMonitor_SubHeading=Gegevenspersistentie van views bewaken en onderhouden


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ DynamicPageHeader ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: text for values shown in column Persistence Status
txtLoading=Bezig met laden
#XFLD: text for values shown in column Persistence Status
txtRunning=Wordt uitgevoerd
#XFLD: text for values shown in column Persistence Status
txtAvailable=Beschikbaar
#XFLD: text for values shown in column Persistence Status
txtError=Fout
#XMSG: Error message for unknown Persistence type that gets logged in Kibana
unknownReplicationTypeError=Replicatietype ''{0}'' wordt niet ondersteund.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Content ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Instellingen die zijn gebruikt voor laatste gegevenspersistentierun:
#XMSG: Message for input parameter name
inputParameterLabel=Invoerparameter
#XMSG: Message for input parameter value
inputParameterValueLabel=Waarde
#XMSG: Message for persisted data
inputParameterPersistedLabel=Persistent gemaakt op
#XTIT: Count for View in ViewMonitor Toolbar
txtViewCount=Views ({0})
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Viewpersistentie
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Gegevenspersistentie
#XBUT: Button to clear the selected view persistance
ClearViewPersistance=Wissen
#XBUT: Button to stop the selected view persistance
stopPersistance=Persistentie stoppen
#XFLD: Placeholder for Search field
txtSearch=Zoeken
#XBUT: Tooltip for refresh button
txtRefresh=Vernieuwen
#XBUT: Tooltip for add view button
txtDeleteView=Persistentie verwijderen
#XBUT: Tooltip for load new peristence
loadNewPersistence=Persistentie opnieuw starten
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Nieuwe snapshot laden
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Gegevenspersistentie starten
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Persistente gegevens verwijderen
#XMSG: success message for starting persistence
startPersistenceSuccess=Wij zijn bezig met persistent maken van view ''{0}''.
#XMSG: success message for stopping persistence
stopPersistenceSuccess=Wij verwijderen persistente gegevens voor view ''{0}''.
#XMSG: success message for stopping persistence
deletePersistedViewSuccess=Wij verwijderen de view "{0}" van de bewakingslijst.
#XMSG: error message during start persistence of a view (backend error)
ERROR_STARTING_PERSISTENCE=Er is een fout opgetreden bij het starten van de gegevenspersistentie voor view ''{0}''.
#XMSG: error message during start persistence of a view (backend error)
PARAMERROR_STARTING_PERSISTENCE=View ''{0}" komt niet in aanmerking voor persistentie; bevat invoerparameters.
#XMSG: error message during start persistence of a view (backend error)
NUMBER_OF_PARAMETER_STARTING_PERSISTENCE=De view "{0}" kan niet worden gepersisteerd omdat deze meer dan een invoerparameter heeft.
#XMSG: error message during start persistence of a view (backend error)
PARAMETER_DEFAULT_VALUE_STARTING_PERSISTENCE=De view "{0}" kan niet worden gepersisteerd omdat de invoerparameter geen standaardwaarde heeft.
#XMSG: error message during start persistence of a view (backend error)
REDEPLOYMENT_DAC_REQUIRED=Nieuwe implementatie van Gegevenstoegangscontrole (DAC) "{0}" is vereist om gegevenspersistentie te ondersteunen.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_STARTING_PERSISTENCE=View ''{0}'' kan niet persistent worden gemaakt omdat deze view ''{1}'' gebruikt die gegevenstoegangscontrole bevat.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_EXTERNAL_STARTING_PERSISTENCE=View "{0}" kan niet persistent worden gemaakt omdat deze een view met gegevenstoegangscontrole gebruikt die bij een andere ruimte behoort.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_STRUCTURE_STARTING_PERSISTENCE=View ''{0}'' kan niet worden gepersisteerd omdat de structuur van een of meerdere van de gegevenstoegangscontroles (DAC) gegevenspersistentie niet ondersteunen.
#XMSG: error message during stop persistence of a view (backend error)
STOP_PERSISTENCE_ERROR=Er is een fout opgetreden bij het stoppen van de persistentie voor view ''{0}''.
#XMSG: error message during stop persistence of a view (backend error)
DELETE_PERSISTED_VIEW_ERROR=Er is een fout opgetreden bij het verwijderen van de persistent gemaakte view "{0}".
#XMSG: Warning message for removing the persisted data
RemovePersistency_Confirm_Msg=Wilt u de persistente gegevens verwijderen en omschakelen naar de virtuele toegang van view ''{0}''?
#XMSG: Warning message for delete the view persistence
DeletePersistency_Confirm_Msg=Wilt u de view verwijderen van de bewakingslijst en de persistente gegevens verwijderen van view "{0}"?
#XMSG: error message for reading data from backend
txtReadBackendError=Er is een fout opgetreden tijdens het lezen van gegevens uit de backend.
#XFLD: Label for No Data Error
NoDataError=Fout
#XMSG: message for conflicting task
Task_Already_Running=Er wordt al een conflicterende taak uitgevoerd voor view "{0}".

#XFLD: Error while accessing pertitioning for a view
missingPrivilegeInViewPersistency=Onvoldoende toestemming om partitionering uit te voeren voor view ''{0}''

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Show All Views~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Text for show all views button
allViewsText=Alle views ({0})
#XBUT: Text for show scheduled views button
scheduledText=Gepland ({0})
#XBUT: Text for show persisted views button
persistedText=Persistent ({0})
#XBUT: Text for start analyzer button
startAnalyzer=Viewanalyse starten
#XFLD: Message if repository is unavailable
repositoryErrorMsg=De repository is niet beschikbaar en sommige functies zijn uitgeschakeld.

#XFLD: Data Access - Virtual
Virtual=Virtueel
#XFLD: Data Access - Persisted
Persisted=Persistent

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Non-Persisted View Selection Dialog~~~~~~~~~~~~~~~~~~~~~~~

#XTIT: Title for Non-Persisted View selection dialog
select_source=Selecteer view om persistent te maken

#XTIT: Placeholder for Non-Persisted View selection dialog search
searchView=Views zoeken
#XTIT: No data in the list of non-persisted view
No_Data=Geen gegevens
#XBUT: Button to select non-persisted view
ok=OK
#XBUT: Button to close the non-persisted views selection dialog
cancel=Annuleren

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~View persistency log message key~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message for view persistency logs
START_STOP_PERSISTENCY=Uitvoering van taak gegevenspersistentie wordt gestart voor "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY=Gegevens worden persistent gemaakt voor view ''{1}''.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY)
START_VIEW_PERSISTENCY_PROCESS=Proces om gegevens persistent te maken voor view "{0}" wordt gestart.
#XFLD: Message for view persistency manual partition load
START_VIEW_PERSISTENCY_PROCESS_MANUAL_PARTITIONS=Proces om gegevens persistent te maken voor view "{0}" met geselecteerde partitie-ID''s: "{1}" wordt gestart.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY=Bezig met verwijderen van persistent gemaakte gegevens voor view ''{1}''.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY)
REMOVE_VIEW_PERSISTENCY=Proces om persistent gemaakte gegevens voor view "{0}" te verwijderen, wordt gestart.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_SUCCESS=Gegevens zijn persistent gemaakt voor view "{1}".
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_SUCCESS)
START_VIEW_PERSISTENCY_PROCESS_SUCCESS=Gegevens zijn persistent gemaakt voor view "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY_SUCCESS=Persistent gemaakte gegevens zijn verwijderd en virtuele toegang tot gegevens is hersteld voor view "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_SUCCESS)
REMOVE_VIEW_PERSISTENCY_SUCCESS=Proces om persistent gemaakte gegevens voor view "{0}" te verwijderen, is voltooid.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_FAILED=Gegevens persistent maken voor view "{1}" is niet mogelijk.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_FAILED)
START_VIEW_PERSISTENCY_PROCESS_FAILED=Gegevens persistent maken voor view "{0}" is niet mogelijk.
#XFLD: Message for view persistency logs (for messages created before version 2022.05)
STOP_VIEW_PERSISTENCY_FAILED=Verwijderen van persistent gemaakte gegevens voor view "{1}" niet mogelijk.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_FAILED)
REMOVE_VIEW_PERSISTENCY_FAILED=Verwijderen van persistent gemaakte gegevens voor view "{0}" niet mogelijk.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_SUCCESS=''{3}'' records persistent gemaakt voor view "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace START_PERSISTENCY_RECORD_COUNT_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS={0} records ingevoegd in persistentietabel voor view "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS_V2={0} records ingevoegd in gegevenspersistentietabel voor view "{1}". Gebruikt geheugen: {2} GiB.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_SUCCESS=''{3}'' persistent gemaakte records verwijderd voor view "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace STOP_PERSISTENCY_RECORD_COUNT_SUCCESS)
REMOVE_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=Persistent gemaakte gegevens verwijderd, "{0}" persistent gemaakte records verwijderd.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_FAILED=Ophalen aantal records mislukt voor view "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_FAILED=Ophalen aantal records mislukt voor view "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Planning is verwijderd voor "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS)
REMOVE_VIEW_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Planning is verwijderd voor view "{0}".
#XFLD: Message for view persistency logs
STOP_PERSISTENCY_DELETE_SCHEDULE_FAILED=Planning is niet verwijderd voor "{1}".
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_REDEPLOY=We kunnen view ''{0}'' niet persistent maken omdat deze is gewijzigd en geïmplementeerd sinds u deze persistent wilde maken. Probeer de view opnieuw persistent te maken of wacht tot de volgende geplande run.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_DELETE=We kunnen view ''{0}'' niet persistent maken omdat deze is verwijderd sinds u deze persistent wilde maken.
#XFLD: Message for successful data persistency of a single partition (for messages created before version 2022.03)
START_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} records persistent gemaakt in partitie voor waarden ''{1}'' <= {2} < ''{3}''.
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (new message to replace START_PERSISTENCY_SINGLE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} records ingevoegd in partitie voor waarden ''{1}'' <= {2} < ''{3}''.
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS_V2={0} records ingevoegd in partitie voor waarden ''{1}'' <= ''{2}'' < ''{3}''. Gebruikt geheugen: {4} GiB.
#XFLD: Message for successful replication of a "others" partition (for messages created before version 2022.03)
START_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} records persistent gemaakt in "andere" partitie.
#XFLD: Message used by SEAL repository for successful replication of a "others" partition (new message to replace START_PERSISTENCY_OTHERS_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} records ingevoegd in "andere" partitie.
#XFLD: Message for view persistency record count of partitions (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={3} records persistent gemaakt voor view ''{1}'' in {4} partities.
#XFLD: Message used by SEAL repository for view persistency record count of partitions (new message to replace START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={0} records ingevoegd in gegevenspersistentietabel voor view "{1}" in {2} partities.
#XFLD: Message used by SEAL repository for view persistency for partition wise refresh
START_VIEW_PERSISTENCY_RECORD_COUNT_REFRESHED_PARTITIONS_SUCCESS={0} records in gegevenspersistentietabel voor view ''{1}'' ingevoegd. Bijgewerkte partities: {2}; geblokkeerde partities: {3}; totaal aantal partities: {4}
#XFLD: Message used by SEAL repository for view persistency for manual partition load
START_VIEW_PERSISTENCY_RECORD_COUNT_MANUAL_PARTITIONS_SUCCESS={0} records ingevoegd in gegevenspersistentietabel voor view ''{1}'' in {2} geselecteerde partities.
#XFLD: Message used by SEAL repository for view persistency for locked partition data load
START_VIEW_PERSISTENCY_RECORD_COUNT_LOCKED_PARTITIONS_SUCCESS={0} records in gegevenspersistentietabel voor view ''{1}'' ingevoegd. Bijgewerkte partities: {2}; geblokkeerde ongewijzigde partities: {3}; totaal aantal partities: {4}
#XMSG: Message for unexpected error (for messages created before version 2022.03)
START_PERSISTENCY_UNEXPECTED_ERROR=Er is een onverwachte fout opgetreden tijdens persistent maken van gegevens voor view ''{0}''.
#XMSG: Message for unexpected error (new message to replace START_PERSISTENCY_UNEXPECTED_ERROR)
START_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Er is een onverwachte fout opgetreden tijdens persistent maken van gegevens voor view ''{0}''.
#XMSG: Message for Locked space error
START_VIEW_PERSISTENCY_LOCKED_SPACE_ERROR=View ''{0}''’ kan niet persistent worden gemaakt omdat ruimte ''{1}'' geblokkeerd is.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
STOP_PERSISTENCY_UNEXPECTED_ERROR=Er is een onverwachte fout opgetreden tijdens verwijderen van persistent gemaakte gegevens voor view ''{0}''.
#XMSG: Message for unexpected error (new message to replace STOP_PERSISTENCY_UNEXPECTED_ERROR)
REMOVE_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Er is een onverwachte fout opgetreden tijdens verwijderen van persistentie voor view ''{0}''.
#XMSG: Message for ivalidated view
VIEW_PERSISTENCY_ABORTED_DEFINITION_INVALID=Definitie van view ''{0}'' is ongeldig geworden, hoogstwaarschijnlijk vanwege een wijziging in een object dat direct of indirect in de view wordt gebruikt. Probeer de view opnieuw te implementeren om het probleem op te lossen of probeer te achterhalen wat de hoofdoorzaak van het probleem is.
#XMSG: Message for persisted data removal (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT=Persistent gemaakte gegevens zijn verwijderd tijdens implementatie van view ''{0}''.
#XMSG: Message for persisted data removal of a consuming view
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW=Persistent gemaakte gegevens zijn verwijderd tijdens implementatie van de gebruikte view ''{0}''.
#XMSG: Message for persisted data removal of a consuming view due to a dac change
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW_DAC_CHANGED=Persistent gemaakte gegevens zijn verwijderd tijdens implementatie van de gebruikte view ''{0}'' omdat de gegevenstoegangscontrole ervan is gewijzigd.
#XMSG: Message for persisted data removal (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DEPLOYMENT=Persistent gemaakte gegevens zijn verwijderd tijdens implementatie van view ''{0}''.
#XMSG: Message for deleted view (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION=Persistentie is verwijderd bij verwijdering van view ''{0}''.
#XMSG: Message for deleted view (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DELETION=Persistentie is verwijderd bij verwijdering van view ''{0}''.
#XMSG Message for removal of persisted data, because prerequisites not fulfilled
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_PREREQUISITES_NOT_FULFILLED=Persistent gemaakte gegevens verwijderd omdat niet langer aan gegevenspersistentievoorwaarden wordt voldaan.
#XMSG: Message for inconsistent persistency
VIEW_PERSISTENCY_INCONSISTENT_PERSISTENCY=De persistentie van de view "{0}" is inconsistent geworden. Verwijder gepersisteerde gegevens om probleem op te lossen.
#XMSG: Message for prerequisites
START_PERSISTENCY_CHECK_PREREQUISITES=De vereisten voor persistentie van view "{0}" worden gecontroleerd.
#XMSG: Message for DAC deployed in deprecated way using table function and join (for messages created before 2025.05)
START_PERSISTENCY_DEPRECIATED_DAC=De view "{0}" wordt geïmplementeerd via gegevenstoegangscontrole (DAC), die wordt uitgefaseerd. Implementeer de view opnieuw om de prestaties te verbeteren.
#XMSG: Message for DAC deployed in deprecated way using table function and join
START_PERSISTENCY_DEPRECATED_DAC=De view "{0}" wordt geïmplementeerd via gegevenstoegangscontrole (DAC), die wordt uitgefaseerd. Implementeer de view opnieuw om de prestaties te verbeteren.
#XMSG: Message for roll back (for messages created before version 2022.03)
ROLLBACK_VIEW_PERSISTENCY=Er is een fout opgetreden. De vorige status van persistentie is hersteld voor de view ''{0}''.
#XMSG: Message used by SEAL repository for rollback of running procedure to persist view (new message to replace ROLLBACK_VIEW_PERSISTENCY)
VIEW_PERSISTENCY_ROLLBACK=Er is een fout opgetreden. Het proces om view "{0}" persistent te maken is gestopt en wijzigingen zijn teruggedraaid.
#XMSG: Message used by SEAL repository for rollback of running procedure to remove persisted data of view
REMOVE_VIEW_PERSISTENCY_ROLLBACK=Er is een fout opgetreden. Het proces om de persistent gemaakte view "{0}" te verwijderen is gestopt en wijzigingen zijn teruggedraaid.
#XMSG: Message used by SEAL repository for persistency preparation
START_PERSISTENCY_PREPARATION=Er worden voorbereidingen getroffen om de gegevens persistent te maken.
#XMSG: Message used by SEAL repository for data insertion
START_INSERTING_PERSISTENCY_DATA=Gegevens worden in persistentietabel ingevoegd.
#XMSG: Message for null values - partitioning (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS={0} nullwaarderecords ingevoegd in "andere" partitie.
#XMSG: Message used by SEAL repository for others partition values - partitioning (new message to replace START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS={0} records ingevoegd in "andere" partitie voor waarden "{2}" < "{1}" OR "{2}" >= "{3}".
#XMSG: Message used by SEAL repository for others partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS_V2={0} records ingevoegd in partitie "andere" voor waarden "{2}" < "{1}" OR "{2}" >= "{3}" Gebruikt geheugen: {4} GiB.
#XMSG: Message used by SEAL repository for null partition values - partitioning
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS={0} records ingevoegd in "andere" partitie voor waarden "{1}" IS NULL.
#XMSG: Message used by SEAL repository for null partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS_V2={0} records ingevoegd in partitie "andere" voor waarden "{1}" IS NULL. Gebruikt geheugen: {2} GiB.
##XFLD:Message used by SEAL repository for remote statistics
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS=Laden van betrokken gegevens: {0} remote statements. Total aantal records opgehaald: {1}. Totale duur: {2} seconden.
##XFLD:Message used by SEAL repository for remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS_WITH_PARTITIONS=Laden van betrokken gegevens met gebruik van {0} partities met {1} remote statements. Total aantal records opgehaald: {2}. Totale duur: {3} seconden.
##XFLD:Message used by SEAL repository with UI ID for instructions of remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_INSTRUCTION=De remote statements die tijdens de run zijn verwerkt, kunnen worden weergegeven door de remote query-monitor te openen in de details van de partitiespecifieke berichten
##XFLD:Message for start reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS=Starten met het proces om bestaande gepersisteerde gegevens voor view {0} te hergebruiken na implementatie.
##XFLD:Message used by SEAL repository for reattach view persitency
START_VIEW_PERSISTENCY_REATTACHMENT=Starten met hergebruik van bestaande gepersisteerde gegevens.
##XFLD:Message used by SEAL repository for reattach view persitency
START_FINILIZING_VIEW_PERSISTENCY_REATTACHMENT=Hergebruik bestaande gepersisteerde gegevens.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_COMPLETE=Het proces om bestaande gepersisteerde gegevens te hergebruiken is voltooid voor view {0}.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_FAILED=Kon bestaande gepersisteerde gegevens voor view {0} niet hergebruiken na implementeren.
#XMSG: Message used by SEAL repository for finalizing persistency
START_FINALIZING_PERSISTENCY=Persistentie wordt voltooid.
#XMSG: Message used by SEAL repository for restoring virtual access for view
REMOVE_VIEW_PERSISTENCY_VIRTUAL_ACCESS_RESTORED=Virtuele toegang tot gegevens is hersteld voor view ''{0}''.
#XMSG: Message used by SEAL repository for view that already has virtual access
REMOVE_VIEW_PERSISTENCY_ALREADY_VIRTUAL_ACCESS=View "{0}" heeft al virtuele gegevenstoegang. Geen persistent gemaakte gegevens verwijderd.
#XMSG: Message for removing view persistency
VIEW_PERSISTENCY_REMOVED_FROM_MONITOR=View "{0}" is verwijderd uit Monitor views.
#XMSG: Message for view doesnt exist.
VIEW_PERSISTENCY_VIEW_NOT_EXISTING=View "{0}" bestaat niet in database of is niet correct geïmplementeerd en kan daarom niet persistent worden gemaakt. Probeer om view opnieuw te implementeren om probleem op te lossen, of om hoofdoorzaak te identificeren.
#XMSG: Message for persistency not enabled.
VIEW_PERSISTENCY_NOT_ENABLED=Gegevenspersistentie is niet ingeschakeld. Implementeer een tabel/view opnieuw in de ruimte "{0}" om de functionaliteit in te schakelen.
#XMSG: Message for unexpected error due to node crash
VIEW_PERSISTENCY_UNEXPECTED_ERROR_GENERAL=Laatste viewpersistentierun is onderbroken wegens technische fouten.
#XFLD: Message for view persistency memory consumption
VIEW_PERSISTENCY_MEMORY_CONSUMPTION={0} GiB aan piekgeheugen gebruikt in runtime van viewpersistentierun.
#XFLD: Message for view persistency timeout
VIEW_PERSISTENCY_TIMEOUT=Persistentie van view {0} heeft time-out van {1} uur bereikt.
#XFLD: Message for not started asynchronous view persistency
VIEW_PERSISTENCY_ASYNC_NOT_STARTED=Door een hoge systeembelasting is de asynchrone uitvoering van de viewpersistentie niet gestart. Controleer of er te veel taken parallel worden uitgevoerd.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency (for messages created before version 2022.05)
START_VIEW_PERSISTENCY_EXECUTION_METHOD_DROP_AND_RENAME=Bestaande gepersisteerde tabel is verwijderd en vervangen door een nieuwe gepersisteerde tabel.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_RECREATE_AND_DROP=Bestaande gepersisteerde tabel is verwijderd en vervangen door een nieuwe gepersisteerde tabel.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_TRUNCATE_AND_COPY=Bestaande gepersisteerde tabel is bijgewerkt met nieuwe gegevens.
##XFLD: Message for missing authorizations for view persistency
VIEW_PERSISTENCY_MISSING_AUTH=Ontbrekende bevoegdheden voor gegevenspersistentie.
##XFLD: Message start of persistency cancellation
VIEW_PERSISTENCY_TRIGGER_CANCEL=Er wordt gestart met het annuleren van het proces om de view {0} persistent te maken.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_UNAVAILABLE_TASK_RUN=Het annuleren van het proces om de view persistent te maken is mislukt omdat er geen gegevenspersistentietaak voor de view {0} wordt uitgevoerd.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_NONDETERMINISTIC_TASK_RUN=Het annuleren van het proces om de view persistent te maken is mislukt omdat er geen gegevenspersistentietaak voor de view {0} wordt uitgevoerd.
##XFLD: Message for canceling a non running persitency task
VIEW_PERSISTENCY_CANCEL_NON_RUNNING_SELECTED_TASK=Het annuleren van het proces om de view {0} persistent te maken is mislukt omdat de geselecteerde gegevenspersistentietaak {1} niet wordt uitgevoerd.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_UNREACHABLE_PERSISTENCY_RUN=Het annuleren van het proces om de view persistent te maken is mislukt omdat de gegevenspersistentie voor de view {0} nog niet is gestart.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_COMPLETED_PERSISTENCY_RUN=Het annuleren van het proces om de view {0} persistent te maken is mislukt omdat het proces al is voltooid.
##XFLD: Message for failed cancellation
VIEW_PERSISTENCY_ABORTED_CANCEL_EXECUTION=Het annuleren van het proces om de view {0} persistent te maken is mislukt.
##XFLD: Message for finalizing persistency cancellation
VIEW_PERSISTENCY_FINALIZE_CANCEL=Proces om de gegevenspersistentie van de view {0} te stoppen is verzonden.
##XFLD: Message used in persist task when cancellation is caught
VIEW_PERSISTENCY_CANCELED=Proces om de view {0} persistent te maken is gestopt.
##XFLD: Message used in persist task when cancelation is caught and performed through a cancelation task
VIEW_PERSISTENCY_CANCELED_VIA_CANCEL_TASK=Proces om de view {0} persistent te maken is gestopt via annuleringstaak {1}.
##XFLD: Message used in cancel task when the task is triggered by deployment
VIEW_PERSISTENCY_CANCEL_TRIGGERED_BY_DEPLOYMENT=Bezig met annuleren van proces om gegevens persistent te maken terwijl de view {0} wordt geïmplementeerd.
##XFLD: Message used in case the cancelation is retriggered for a running task
VIEW_PERSISTENCY_CANCEL_ATTEMPT_RETRIGGER_CANCEL=Een vorige taak voor annuleren van viewpersistentie van view {0} is al verzonden.
##XFLD: Message used to express the delay that may occur before the persistency task is stopped
VIEW_PERSISTENCY_CANCEL_DELAY_STOPPING_PERSISTENCY=Er is mogelijk een vertraging totdat de gegevenspersistentietaak voor de view {0} is gestopt.
##XFLD: Message used to reflect the task log identifier of the current running persistency task
VIEW_PERSISTENCY_CANCEL_CURRENT_RUNNING_TASK=Gegevens voor view {0} worden persistent gemaakt met taak {1}.
##XFLD:Message used by SEAL repository for locked partitions and DACs
VIEW_PERSISTENCY_LOCKED_PARTITIONS_DACS=Door DAC's verstrekte bevoegdheden zijn mogelijk gewijzigd en worden niet in aanmerking genomen door geblokkeerde partities. Deblokkeer de partities en laad een nieuwe snapshot om de wijzigingen toe te passen.
##XFLD:Message used by SEAL repository for column structure change
VIEW_PERSISTENCY_COLUMN_STRUCTURE_CHANGE=De kolomstructuur is gewijzigd en komt niet langer overeen met de bestaande persistentietabel. Verwijder persistente gegevens en begin een nieuwe gegevenspersistentie om uw persistentietabel bij te werken.
##XFLD: Message for out of memory
VIEW_PERSISTENCY_HANA_OUT_OF_MEMORY=De taak is mislukt vanwege de fout Onvoldoende geheugen van de SAP HANA-database.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_EXCEPTION_TRANSACTION_ROLLBACK=De taak is mislukt vanwege een interne uitzondering binnen de SAP HANA-database.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_SQL_EXECUTION_ISSUE=De taak is mislukt vanwege een intern SQL-uitzonderingsprobleem binnen de SAP HANA-database.
##XFLD: Message for HANA out of memory result
VIEW_PERSISTENCY_OUT_OF_MEMORY_RESULT_MESSAGE=Reden geheugentekort HANA: {0}
##XFLD: Message for admission control rejection
VIEW_PERSISTENCY_HANA_ADMISSION_CTRL_REJECTION=De taak is mislukt vanwege een SAP HANA-toegangscontroleafwijzing.
##XFLD: Message for HANA back pressure / rate limit of HANA client
VIEW_PERSISTENCY_HANA_CLIENT_RATE_LIMIT_REJECTION=De taak is mislukt door te veel actieve SAP HANA-verbindingen.
#XMSG: Message for invalid persisted table name
VIEW_PERSISTENCY_PERSISTED_TABLE_INVALID=Fout opgetreden en persistent gemaakte tabel is ongeldig geworden. Om het probleem op te lossen verwijdert u de persistent gemaakte gegevens en maakt u de view opnieuw persistent.
#XMSG: Message for usahe of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION=De view kan niet persistent worden gemaakt. Deze maakt gebruik van een remote tabel die op een remote bron met ingeschakelde gebruikersdoorgifte is gebaseerd. Controleer de herkomst van de view.
#XMSG: Message for dynamic usage of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION_DYNAMIC=De view kan niet persistent worden gemaakt. Deze maakt gebruik van een remote tabel die op een remote bron met ingeschakelde gebruikersdoorgifte is gebaseerd. De remote tabel kan dynamisch via een SQL-scriptview worden verbruikt. De herkomst van de view geeft de remote tabel mogelijk niet weer.
#XMSG: Message for insufficient privileges or consumption of view with DAC via dynamic SQL
VIEW_PERSISTENCY_INSUF_PRIV_OR_DAC_DYN_SQL=Uw privileges zijn mogelijk onvoldoende. Open de preview van gegevens om te zien of u de vereiste privileges hebt. Indien ja, worden mogelijk aan een tweede view, gebruikt via een dynamisch SQL-script, toegang tot gegevenstoegangscontrole (DAC) toegewezen.
#XMSG: deployment of DAC is deprecated and view need to be redeployed to be able to persist data
VIEW_PERSISTENCY_DEPRECATED_DAC=View "{0}" is geïmplementeerd via gegevenstoegangscontrole (DAC), die wordt uitgefaseerd. Implementeer de view opnieuw om gegevens voor de view persistent te maken.
##XFLD:Message used by SEAL repository for input parameter
START_VIEW_PERSISTENCY_INPUT_PARAMETER=Standaardwaarde "{0}" wordt gebruikt voor invoerparameter "{1}".
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Elastic compute node keys~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
##XFLD:Message used to reflect disablement of ecn replica
ECN_DISABLE_EXISTING_REPLICA=Knooppuntreplica elastisch berekenen is uitgeschakeld.
##XFLD:Message used to reflect recreation of ecn replica
ECN_RECREATE_EXISTING_REPLICA=Knooppuntreplica elastisch berekenen is opnieuw gecreëerd.
##XFLD:Message used to reflect re-enablement of ecn replica
ECN_ENABLE_EXISTING_REPLICA=Knooppuntreplica elastisch berekenen is opnieuw ingeschakeld.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Task Schedule Menu key~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message to schedule task
scheduleText=Planning
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Planning maken
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Planning bewerken
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Planning verwijderen
#XFLD: Refresh frequency field
refreshFrequency=Vernieuwingsfrequentie
#XFLD: Refresh frequency field
refreshFrequencyNew=Frequentie
#XFLD: Refresh frequency field
refreshFrequencyNewNew=Geplande frequentie
#XBUT: label for None
none=Geen
#XBUT: label for Real-Time replication state
realtime=Realtime
#XFLD: Label for table column
txtNextSchedule=Volgende run
#XFLD: Label for table column
txtNextScheduleNew=Geplande volgende run
#XFLD: Label for table column
txtNumOfRecords=Aantal records
#XFLD: Label for scheduled link
scheduledTxt=Gepland
#XFLD: LABEL for partially persisted link
partiallyPersisted=Gedeeltelijk persistent gemaakt
#XFLD: Text for paused text
paused=Gepauzeerd

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ View Monitor Tasklog Details Action keys~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message for release lock actionn
releaseLockMsg=Als een run langer duurt dan gebruikelijk, kan dit betekenen dat de run is mislukt en dat de status niet dienovereenkomstig is bijgewerkt. \r\n Om het probleem op te lossen, kunt u de blokkering vrijgeven en de status op mislukt zetten.
#XFLD: Label for release lock dialog
releaseLockText=Blokkering vrijgeven

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Column header tooltip ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: tooltip for table column
txtViewTooltip=Naam van persistent gemaakte view
#XFLD: tooltip for table column
txtViewDataAccessTooltip=Dit geeft de beschikbaarheid van de view aan
#XFLD: tooltip for table column
txtViewRefreshFreqTooltip=Dit geeft aan of een planning is gedefinieerd voor de view
#XFLD: tooltip for table column
txtViewStatusTooltip=De status van de persistent gemaakte view ophalen
#XFLD: tooltip for table column
txtViewLatestUpdateTooltip=Geeft informatie over wanneer de persistent gemaakte view voor het laatst is bijgewerkt
#XFLD: tooltip for table column
txtViewNextRunTooltip=Als voor de view een planning is ingesteld, kunt u zien wanneer de volgende run is gepland.
#XFLD: tooltip for table column
txtViewNumOfRecordsTooltip=Aantal records traceren
#XFLD: tooltip for table column
txtViewInMemorySizeTooltip=Controleer hoeveel ruimte de view van het geheugen gebruikt
#XFLD: tooltip for table column
txtViewDiskSizeTooltip=Controleer hoeveel ruimte de view van de schijf gebruikt
#XMSG: Expired text
txtExpired=Verlopen

#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Object "{0}" kan niet worden toegevoegd aan taakketen.

#~~~~~~~~~~~View Analyzer task log messages~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message indicating number of records for a view, and memory consumption in Mebibyte during simulation of view persistency
RECORDS_PEAK_MEMORY=View ''{0}'' heeft {1} records. Een simulatie van viewpersistentie gebruikt {2} MiB van geheugen.
#XMSG: View Analyzer execution failure
VIEW_ANALYZER_EXECUTION_FAILED=Uitvoering viewanalyse mislukt.
#XMSG: Missing authorizations for View Analyzer
VIEW_ANALYZER_MISSING_AUTH=Ontbrekende bevoegdheden voor viewanalyse
#XMSG: Maximum memory in Gibibyte is reached, therefore persistency simulation is stopped
MAXIMUM_MEMORY_REACHED=Maximumgeheugenverbruik van {0} GiB is bereikt tijdens simuleren van gegevenspersistentie voor view ''{1}". Daarom worden er geen andere gegevenspersistentiesimulaties meer uitgevoerd.
#XMSG: Error during persistency simulation occurred
PERSISTENCY_SIMULATION_ERROR=Er is een fout opgetreden tijdens gegevebspersistentiesimulatie voor view ''{0}''.
#XMSG: Persistency simulation is not executed, because prerequisites are not fulfilled and the view cannot be persisted
PREREQUISITES_NOT_FULFILLED=Gegevenspersistentiesimulatie is niet uitgevoerd voor view ''{0}''’ omdat niet aan vereisten is voldaan en view kan niet persistent worden gemaakt.
#XMSG: Persistency simulation is not executed, because the view does not exist in the database
VIEW_DOES_NOT_EXIST=Implementeer view "{0}" om gegevenspersistentiesimulatie in te schakelen.
#XMSG: Number of records cannot be determined, because the local table does not exist in the database
TABLE_DOES_NOT_EXIST=De lokale tabel "{0}" bestaat niet in de database, het aantal records kan daarom niet worden bepaald voor deze tabel.
##XMSG: Message start of cancellation for View Analyzer task
CANCEL_VIEW_ANALYZER_TRIGGERED=Proces om viewanalysetaak {0} voor view ''{1}'' te stoppen, is verzonden. Er is mogelijk een vertraging totdat de taak is gestopt.
##XMSG: Message start to indicate that task is not active
CANCEL_VIEW_ANALYZER_NO_ACTIVE_TASK=Viewanalysetaak {0} voor view ''{1}'' is niet actief.
#XMSG: Cancel View Analyzer task failure
CANCEL_VIEW_ANALYZER_FAILED=Annuleren van viewanalysetaak is mislukt.
##XMSG: Message used in View Analyzer execution task when task is canceled
VIEW_ANALYZER_CANCELED=Uitvoering van viewanalyse voor view ''{0}'' is gestopt via annuleringstaak.
##XMSG: Message start of cancelation for Model Validation task
CANCEL_MODEL_VALIDATION_TRIGGERED=Proces om modelvalidatietaak {0} voor view ''{1}'' te stoppen is verzonden. Er is mogelijk een vertraging totdat de taak is gestopt.
##XMSG: Message to indicate that task is not active
CANCEL_MODEL_VALIDATION_NO_ACTIVE_TASK=Modelvalidatietaak {0} voor view ''{1}'' is niet actief.
#XMSG: Cancel Model Validation task failure
CANCEL_MODEL_VALIDATION_FAILED=Annulering van modelvalidatietaak is mislukt.
##XMSG: Message used in Model Validation task when task is canceled
MODEL_VALIDATION_CANCELED=Uitvoering van modelvalidatie voor view ''{0}'' is gestopt via annuleringstaak.
#XMSG: Message for Locked space error, before starting Model Validation task
MODEL_VALIDATION_LOCKED_SPACE_ERROR=Kan geen modelvalidatie uitvoeren voor view ''{0}'', omdat ruimte ''{1}'' is geblokkeerd.
##XMSG: Message used in View Analyzer execution task when determination of number of rows fails
NUMBER_ROWS_DETERMINATION_FAILED=Er is een fout opgetreden tijdens het bepalen van het aantal rijen voor lokale tabel ''{0}''.
##XMSG: Message to indicate that SQL plan was created
EXECUTION_PLAN_CREATED=Planbestand SQL Analyzer voor view ''{0}'' is gecreëerd en kan worden gedownload.
#XFLD: Message to indicate starting of process to generate SQL Analyzer plan file
START_PLANVIZ_EXECUTION=Beginnen met verwerking om planbestand van SQL Analyzer te genereren voor view ''{0}''.
#XFLD: Message to indicate starting of View Analyzer process
VIEW_ANALYZER_EXECUTION_STARTED=Uitvoering View Analyzer starten
##XMSG: Message to indicate that SQL plan file cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_PLANVIZ_NOT_FULFILLED=Planbestand SQL Analyzer kan niet worden gegenereerd voor view ''{0}'' omdat niet aan gegevenspersistentievoorwaarden is voldaan.
##XMSG: Error message during  generation of SQL Analyzer plan file
PLANVIZ_ERROR=Er is een fout opgetreden tijdens de generering van het planbestand van de SQL Analyzer voor view ''{0}''.
#XMSG: Message for Locked space error
VIEW_ANALYZER_LOCKED_SPACE_ERROR=Kan geen viewanalyse uitvoeren voor view ''{0}'', omdat ruimte ''{1}'' is geblokkeerd.
#XMSG: Message for persistency simulation without partitioning
PERSISTENCY_SIMULATION_WITHOUT_PARTITIONS=De partities van view ''{0}'' worden niet ingecalculeerd tijdens de simulatie van gegevensviewpersistentie.
#XMSG: Message for generation of SQL Analyzer plan file without considering partitioning
PLANVIZ_WITHOUT_PARTITIONS=De partities van view ''{0}'' worden niet ingecalculeerd tijdens de generering van het planbestand van SQL Analyzer.
#XMSG: Confirmation message for mass removal of persisted data
MassRemovePersistency_Confirm_Msg=Wilt u de persistente gegevens verwijderen en de gegevenstoegang terugschakelen naar virtuele toegang?
#XMSG: Confirmation message for partial mass removal of selected persisted data
PartialMassRemovePersistency_Confirm_Msg={0} van de {1} geselecteerde views hebben gepersisteerde gegevens. \n Wilt u de gepersisteerde gegevens verwijderen en de gegevenstoegang terugschakelen naar virtuele toegang?
#XMSG: success message for stopping mass persistence
stopMassPersistenceSuccess=Wij zijn bezig met het verwijderen van gepersisteerde gegevens voor geselecteerde views.
#XMSG: error message during mass stop persistence of a view (backend error)
STOP_MASS_PERSISTENCE_ERROR=Fout opgetreden bij het stoppen van de persistentie voor geselecteerde views.
#XMSG: memory analysis is only performed in space {0}
NO_MEMORY_ANALYSIS=Geheugenanalyse wordt alleen uitgevoerd voor entiteiten in ruimte "{0}": "{1}" "{2}" zijn overgeslagen.
##XMSG: Message to indicate that Explain Plan cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_EXPLAINPLAN_NOT_FULFILLED=Bestand Explainplan kan niet worden gegenereerd voor view ''{0}'' omdat niet aan gegevenspersistentievoorwaarden is voldaan.
#XMSG: Message for Explain Plan generation without partitioning
EXPLAINPLAN_WITHOUT_PARTITIONS=De partities van view ''{0}'' worden niet ingecalculeerd tijdens de generering van het bestand Explainplan.
#XFLD: Message to indicate starting of process to generate Explain Plan file
START_EXPLAINPLAN_EXECUTION=Beginnen met verwerking om bestand Explainplan te genereren voor view ''{0}''.
##XMSG: Message to indicate that Explain Plan was created
EXPLAINPLAN_CREATED=Bestand Explainplan voor de view "{0}" gegenereerd. U kunt dit weergeven door op "Details weergeven" te klikken of het te downloaden als u de juiste machtiging hebt.
##XMSG: Error message during  generation of Explain Plan file
EXPLAINPLAN_ERROR=Er is een fout opgetreden tijdens de generering van het bestand Explainplan voor view ''{0}''.
##XMSG: Explain Plan file cannot be generated for the view {0}, because too many views are stacked on each other
EXPLAINPLAN_MODEL_TOO_COMPLEX=Bestand Explainplan kan niet worden gegenereerd voor view "{0}". Er zijn te veel views op elkaar gestapeld. Complexe modellen kunnen fouten als gevolg van onvoldoende geheugen veroorzaken en tot tragere prestaties leiden. Wij raden aan om een view persistent te maken.

##XMSG: Message to indicate that the space is locked
PERFORMANCE_ANALYSIS_LOCKED_SPACE_ERROR=Kan geen prestatieanalyse uitvoeren voor view ''{0}'', omdat ruimte ''{1}'' is geblokkeerd.
##XMSG: The performance analysis task was cancelled
PERFORMANCE_ANALYSIS_CANCELED=Prestatieanalyse voor view "{0}" is geannuleerd.
##XMSG: The performance analysis failed
PERFORMANCE_ANALYSIS_FAILED=Prestatieanalyse mislukt.
##XMSG: The performance analysis is finished
PERFORMANCE_ANALYSIS_FINISHED=Prestatieanalyse voor view "{0}" is voltooid. Geef resultaat weer door op "Details weergeven" te klikken.
##XMSG: The view has input parameter with no default value
INPUT_PARAMETER_WITHOUT_DEFAULT_VALUE=Deze view kan niet worden geanalyseerd omdat deze een parameter zonder standaardwaarde heeft.
##XMSG: The view is not fully deployed
VIEW_NOT_FULLY_DEPLOYED=View kan niet worden geanalyseerd omdat deze niet volledig is geïmplementeerd.
##XMSG: The view uses limited remote adapters
LIMITED_REMOTE_ADAPTER=De view gebruikt minstens één externe adapter met beperkte mogelijkheden, zoals ontbrekende filterpushdown of ondersteuning voor 'Aantal'. Persistentie- of replicatieobject kunnen de runtimeprestatie verbeteren.
##XMSG: The view uses remote adapter that does not support 'Limit'
REMOTE_ADAPTER_NOT_SUPPORTING_LIMIT=Deze view gebruikt minstens één externe adapter die 'Limiet' niet ondersteunt. Meer dan 1000 records zijn mogelijk geselecteerd.
##XMSG: Performance analysis is executed by using default parameters of view'
INPUT_PARAMETER_WITH_DEFAULT_VALUE=De prestatieanalyse wordt uitgevoerd door de standaardwaarden van viewparameters te gebruiken.
##XMSG: Message to indicate that an error occurred
PERFORMANCE_ANALYSIS_ERROR=Fout opgetreden bij prestatieanalyses voor view "{0}".
##XMSG: Message start of cancellation for Performance Analysis task
CANCEL_PERFORMANCE_ANALYSIS_TRIGGERED=Proces om prestatieanalysetaak {0} voor view "{1}" te stoppen, is verzonden. Er is mogelijk een vertraging totdat de taak is gestopt.
##XMSG: Message start to indicate that task is not active
CANCEL_PERFORMANCE_ANALYSIS_NO_ACTIVE_TASK=Prestatieanalysetaak {0} voor view "{1}" is niet actief.
#XMSG: Cancel Performance Analysis task failure
CANCEL_PERFORMANCE_ANALYSIS_FAILED=Kan prestatieanalysetaak niet annuleren.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Planning aan mij toewijzen
#XBUT: Pause schedule menu label
pauseScheduleLabel=Planning pauzeren
#XBUT: Resume schedule menu label
resumeScheduleLabel=Planning hervatten
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Fout opgetreden bij verwijderen planningen.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Fout opgetreden bij toewijzen planningen.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Fout opgetreden bij pauzeren planningen.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Fout opgetreden bij hervatten planningen.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted={0} planningen worden verwijderd
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Eigenaar van {0} planningen wordt gewijzigd
#XMSG: Message for starting mass pausing of schedules
massPauseStarted={0} planningen worden gepauzeerd
#XMSG: Message for starting mass resuming of schedules
massResumeStarted={0} planningen worden hervat
#XBUT: Select Columns Button
selectColumnsBtn=Kolommen selecteren
#XFLD: Refresh tooltip
TEXT_REFRESH=Vernieuwen
#XFLD: Select Columns tooltip
text_selectColumns=Kolommen selecteren


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Runtime Metrics ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Label for Runtime Metrics
RuntimeMetricsTitle=Runtimemetrieken voor
#XFLD : Label for Run Button
runButton=Uitvoeren
#XFLD : Label for Cancel Button
cancelButton=Annuleren
#XFLD : Label for Close Button
closeButton=Sluiten
#XFLD : Label for Open View Analyzer Button
openViewAnalyzerButton=Openviewanalyse
#XFLD : Label for Generate Explain Plan Button
generateExplainPlanButton=Explainplan genereren
#XFLD : Label for Previous Run Column
previousRun=Vorige run
#XFLD : Label for Latest Run Column
latestRun=Laatste run
#XFLD : Label for time Column
time=Tijd
#XFLD : Label for Duration Column
duration=Duur
#XFLD : Label for Peak Memory Column
peakMemory=Piekgeheugen
#XFLD : Label for Number of Rows
numberOfRows=Aantal rijen
#XFLD : Label for Overall Number of Sources
overallNumberOfSources=Totaalaantal bronnen
#XFLD : Label for Data Access Column
dataAccess=Gegevenstoegang
#XFLD : Label for Local Tables
localTables=Lokale tabellen
#XFLD : Label for federatedRemoteTables
federatedRemoteTables=Federatieve remote tabellen (met beperkte adaptercapaciteiten)
#XTXT Text for initial state of the runtime metrics
initialState=Voer prestatieanalyse uit om de metrieken te krijgen. Dit kan even duren, maar indien nodig kunt u het proces annuleren.
#XMSG: Cancel confirmation message
CancelRunConfirmationMessage=Weet u zeker dat u de huidige run van prestatieanalyses wilt annuleren?
#XTIT: Cancel dialog title
CancelRunTitle=Run annuleren
#XFLD: Label for Number of Rows
NUMBER_ROWS=Aantal rijen
#XFLD: Label for Overall Number of Sources
OVERALL_NUMBER_SOURCES=Totaalaantal bronnen
#XFLD: Label for Data Access
DATA_ACCESS=Gegevenstoegang
#XFLD: Label for Remote tables
NUMBER_REMOTE_TABLES_LIMITED_ADAPTERS=Federatieve remote tabellen (met beperkte adaptercapaciteiten)
#XFLD: Label for select statement
SELECT_STATEMENT='SELECT * FROM VIEW LIMIT 1000'
#XFLD: Label for duration
SELECT_RUNTIME=Duur
#XFLD: Label for peak memory
SELECT_PEAK_MEMORY=Piekgeheugen
#XFLD: Label for count statement
SELECT_COUNT_STATEMENT='SELECT COUNT(*) FROM VIEW'
#XFLD: Label for count duration
SELECT_COUNT_RUNTIME=Duur
#XFLD: Label for count peak memory
SELECT_COUNT_PEAK_MEMORY=Piekgeheugen
#XFLD: Label for count of number of local tables
NUMBER_LOCAL_TABLES_FILE=Lokale tabellen (bestand)
#XTXT: Text for running state of the runtime metrics
Running=Wordt uitgevoerd...
#XFLD: Label for time
Time=Tijd
#XFLD: Label for virtual access
PA_VIRTUAL=Virtueel
#XFLD: Label for persisted access
PA_PERSISTED=Persistent gemaakt
PA_PARTIALLY_PERSISTED=Gedeeltelijk persistent gemaakt
#XTXT: Text for cancel
CancelRunSuccessMessage=Prestatieanalyserun annuleren
#XTXT: Text for cancel error
CancelRunErrorMessage=Fout opgetreden tijdens annuleren van prestatieanalyserun
#XTXT: Text for explain plan generation
ExplainPlanStarted=Explainplan wordt gegenereerd voor view "{0}".
#XTXT: Text for performance analysis
PerformanceAnalysisStarted=Prestatieanalyse wordt gestart voor view "{0}".
#XTXT: Text for performance analysis error
ErrorFetchingPerformanceAnalysis=Fout opgetreden tijdens ophalen van prestatieanalysegegevens.
#XTXT: Text for performance analysis error
conflictingTask=Prestatieanalysetaak wordt al uitgevoerd
#XFLD: Label for Errors
Errors=Fout(en)
#XFLD: Label for Warnings
Warnings=Waarschuwing(en)
#XFLD: Tooltip for missing privileges
openViewAnalyzerButtonTooltipNoPrivilege=U hebt de bevoegdheden nodig voor DWC_DATAINTEGRATION(update) om de Viewanalyse te openen.
#XFLD: Tooltip for missing privileges
generateExplainPlanButtonTooltipNoPrivilege=U hebt de bevoegdheden nodig voor DWC_RUNTIME(read) om het Explainplan te genereren.



#XFLD: Label for frequency column
everyLabel=Elke
#XFLD: Plural Recurrence text for Hour
hoursLabel=Uur
#XFLD: Plural Recurrence text for Day
daysLabel=Dagen
#XFLD: Plural Recurrence text for Month
monthsLabel=Maanden
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minuten
