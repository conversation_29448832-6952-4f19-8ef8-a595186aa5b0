
#XBCB: Breadcrumb
viewMonitorBreadcrumb=[[[{şρąċēĬƌ}∙∙∙∙∙]]]

#XFLD: Label for persisted view column
SOURCE=[[[Ŝŏűŗċē∙∙∙∙∙∙∙∙]]]
#XFLD: Label for persisted view column
NAME=[[[Ńąɱē]]]
#XFLD: Label for persisted view column
NAME_LABEL=[[[Ɓűşįŋēşş Ńąɱē∙∙∙∙∙∙]]]
#XFLD: Label for persisted view column
NAME_LABELNew=[[[Ŏƃĵēċţ (Ɓűşįŋēşş Ńąɱē)∙∙∙∙∙]]]
#XFLD: Label for persisted view column
TECHINCAL_NAME=[[[Ţēċĥŋįċąĺ Ńąɱē∙∙∙∙∙]]]
#XFLD: Label for persisted view column
TECHINCAL_NAMENew=[[[Ŏƃĵēċţ (Ţēċĥŋįċąĺ Ńąɱē)∙∙∙∙∙∙]]]
#XFLD: Label for persisted view column
DATA_PERSISTENCY=[[[Ďąţą Āċċēşş∙∙∙∙∙∙∙∙]]]
#XFLD: Label for persisted view column
STATUS=[[[Ŝţąţűş∙∙∙∙∙∙∙∙]]]
#XFLD: Label for persisted view column
LAST_UPDATED=[[[Ļąşţ Ůρƌąţēƌ∙∙∙∙∙∙∙]]]
#XFLD: Label for persisted view column
IN_MEMORY_SIZE=[[[Μēɱŏŗŷ Ůşēƌ ƒŏŗ Ŝţŏŗąğē (ΜįƁ)∙∙∙∙∙∙∙∙∙]]]
#XFLD: Label for persisted view column
DISK_SIZE=[[[Ďįşķ Ůşēƌ ƒŏŗ Ŝţŏŗąğē (ΜįƁ)∙∙∙∙∙∙∙∙]]]
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_NEWW=[[[Ŝįžē įŋ-Μēɱŏŗŷ (ΜįƁ)∙∙∙∙]]]
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_HEADER=[[[Ŝįžē įŋ-Μēɱŏŗŷ∙∙∙∙∙]]]
#XFLD: Label for persisted view column
DISK_SIZE_NEWW=[[[Ŝįžē ŏŋ Ďįşķ (ΜįƁ)∙∙∙∙∙∙]]]
#XFLD: Label for persisted view column
DISK_SIZE_HEADER=[[[Ŝįžē ŏŋ Ďįşķ∙∙∙∙∙∙∙]]]
#XFLD: Label for schedule owner column
txtScheduleOwner=[[[Ŝċĥēƌűĺē Ŏŵŋēŗ∙∙∙∙∙]]]
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=[[[Ŝĥŏŵş ŵĥŏ ċŗēąţēƌ ţĥē şċĥēƌűĺē∙∙∙∙∙∙∙∙∙]]]
#XFLD: Label for persisted view column
PERSISTED=[[[Ƥēŗşįşţēƌ∙∙∙∙∙]]]
#XFLD: Label for persisted view column
TYPE=[[[Ţŷρē]]]
#XFLD: Label for View Selection Dialog column
changedOn=[[[Ĉĥąŋğēƌ Ŏŋ∙∙∙∙]]]
#XFLD: Label for View Selection Dialog column
createdBy=[[[Ĉŗēąţēƌ Ɓŷ∙∙∙∙]]]
#XFLD: Label for log details column
txtViewPersistencyLogs=[[[Ʋįēŵ Ļŏğş∙∙∙∙∙]]]
#XFLD: Label for log details column
txtViewPersistencyLogsNew=[[[Ďēţąįĺş∙∙∙∙∙∙∙]]]
#XFLD: text for values shown for Ascending sort order
SortInAsc=[[[Ŝŏŗţ Āşċēŋƌįŋğ∙∙∙∙∙]]]
#XFLD: text for values shown for Descending sort order
SortInDesc=[[[Ŝŏŗţ Ďēşċēŋƌįŋğ∙∙∙∙]]]
#XFLD: Label for persisted view monitor heading
ViewMonitor_Heading=[[[Ʋįēŵş Μŏŋįţŏŗ∙∙∙∙∙∙]]]
#XFLD: Label for persisted view monitor sub-heading
ViewMonitor_SubHeading=[[[Μŏŋįţŏŗ ąŋƌ Μąįŋţąįŋ Ďąţą Ƥēŗşįşţēŋċē ŏƒ Ʋįēŵş∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ DynamicPageHeader ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XFLD: placeholder for empty cell
emptyCell=[[[---∙]]]
#XFLD: text for values shown in column Persistence Status
txtLoading=[[[Ļŏąƌįŋğ∙∙∙∙∙∙∙]]]
#XFLD: text for values shown in column Persistence Status
txtRunning=[[[Řűŋŋįŋğ∙∙∙∙∙∙∙]]]
#XFLD: text for values shown in column Persistence Status
txtAvailable=[[[Āʋąįĺąƃĺē∙∙∙∙∙]]]
#XFLD: text for values shown in column Persistence Status
txtError=[[[Ĕŗŗŏŗ∙∙∙∙∙∙∙∙∙]]]
#XMSG: Error message for unknown Persistence type that gets logged in Kibana
unknownReplicationTypeError=[[[Řēρĺįċąţįŏŋ ţŷρē "{0}" įş ŋŏţ şűρρŏŗţēƌ.]]]

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Content ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=[[[Ŝēţţįŋğş űşēƌ ƒŏŗ ĺąşţ ƌąţą ρēŗşįşţēŋċē ŗűŋ:∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Message for input parameter name
inputParameterLabel=[[[Ĭŋρűţ Ƥąŗąɱēţēŗ∙∙∙∙]]]
#XMSG: Message for input parameter value
inputParameterValueLabel=[[[Ʋąĺűē∙∙∙∙∙∙∙∙∙]]]
#XMSG: Message for persisted data
inputParameterPersistedLabel=[[[Ƥēŗşįşţēƌ Āţ∙∙∙∙∙∙∙]]]
#XTIT: Count for View in ViewMonitor Toolbar
txtViewCount=[[[Ʋįēŵş ({0})]]]
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=[[[Ʋįēŵ Ƥēŗşįşţēŋċŷ∙∙∙∙∙∙∙∙]]]
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=[[[Ďąţą Ƥēŗşįşţēŋċē∙∙∙∙∙∙∙∙]]]
#XBUT: Button to clear the selected view persistance
ClearViewPersistance=[[[Ĉĺēąŗ∙∙∙∙∙∙∙∙∙]]]
#XBUT: Button to stop the selected view persistance
stopPersistance=[[[Ŝţŏρ Ƥēŗşįşţēŋċē∙∙∙∙∙∙∙∙]]]
#XFLD: Placeholder for Search field
txtSearch=[[[Ŝēąŗċĥ∙∙∙∙∙∙∙∙]]]
#XBUT: Tooltip for refresh button
txtRefresh=[[[Řēƒŗēşĥ∙∙∙∙∙∙∙]]]
#XBUT: Tooltip for add view button
txtDeleteView=[[[Ďēĺēţē Ƥēŗşįşţēŋċē∙∙∙∙∙∙]]]
#XBUT: Tooltip for load new peristence
loadNewPersistence=[[[Řēşţąŗţ Ƥēŗşįşţēŋċē∙∙∙∙∙]]]
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=[[[Ļŏąƌ Ńēŵ Ŝŋąρşĥŏţ∙∙∙∙∙∙∙]]]
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=[[[Ŝţąŗţ Ďąţą Ƥēŗşįşţēŋċē∙∙∙∙∙]]]
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=[[[Řēɱŏʋē Ƥēŗşįşţēƌ Ďąţą∙∙∙∙∙]]]
#XMSG: success message for starting persistence
startPersistenceSuccess=[[[Ŵē’ŗē ρēŗşįşţįŋğ ţĥē ʋįēŵ "{0}".]]]
#XMSG: success message for stopping persistence
stopPersistenceSuccess=[[[Ŵē’ŗē ŗēɱŏʋįŋğ ρēŗşįşţēƌ ƌąţą ƒŏŗ ʋįēŵ "{0}".]]]
#XMSG: success message for stopping persistence
deletePersistedViewSuccess=[[[Ŵē’ŗē ŗēɱŏʋįŋğ ţĥē ʋįēŵ "{0}" ƒŗŏɱ ţĥē ɱŏŋįţŏŗįŋğ ĺįşţ.]]]
#XMSG: error message during start persistence of a view (backend error)
ERROR_STARTING_PERSISTENCE=[[[Āŋ ēŗŗŏŗ ŏċċűŗŗēƌ ŵĥįĺē şţąŗţįŋğ ţĥē ƌąţą ρēŗşįşţēŋċē ƒŏŗ ʋįēŵ "{0}".]]]
#XMSG: error message during start persistence of a view (backend error)
PARAMERROR_STARTING_PERSISTENCE=[[[Ţĥē ʋįēŵ "{0}" ċąŋŋŏţ ƃē ρēŗşįşţēƌ ƃēċąűşē įţ ċŏŋţąįŋş įŋρűţ ρąŗąɱēţēŗş.]]]
#XMSG: error message during start persistence of a view (backend error)
NUMBER_OF_PARAMETER_STARTING_PERSISTENCE=[[[Ţĥē ʋįēŵ "{0}" ċąŋŋŏţ ƃē ρēŗşįşţēƌ ƃēċąűşē įţ ĥąş ɱŏŗē ţĥąŋ ŏŋē įŋρűţ ρąŗąɱēţēŗ.]]]
#XMSG: error message during start persistence of a view (backend error)
PARAMETER_DEFAULT_VALUE_STARTING_PERSISTENCE=[[[Ţĥē ʋįēŵ "{0}" ċąŋŋŏţ ƃē ρēŗşįşţēƌ ƃēċąűşē ţĥē įŋρűţ ρąŗąɱēţēŗ ƌŏēş ŋŏţ ĥąʋē ƌēƒąűĺţ ʋąĺűē.]]]
#XMSG: error message during start persistence of a view (backend error)
REDEPLOYMENT_DAC_REQUIRED=[[[Řēƌēρĺŏŷɱēŋţ ŏƒ ţĥē Ďąţą Āċċēşş Ĉŏŋţŗŏĺ (ĎĀĈ) "{0}" įş ŗēƣűįŗēƌ ţŏ şűρρŏŗţ ƌąţą ρēŗşįşţēŋċē.]]]
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_STARTING_PERSISTENCE=[[[Ţĥē ʋįēŵ "{0}" ċąŋŋŏţ ƃē ρēŗşįşţēƌ, ƃēċąűşē įţ űşēş ţĥē ʋįēŵ "{1}", ŵĥįċĥ ċŏŋţąįŋş Ďąţą Āċċēşş Ĉŏŋţŗŏĺ (ĎĀĈ).]]]
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_EXTERNAL_STARTING_PERSISTENCE=[[[Ţĥē ʋįēŵ "{0}" ċąŋŋŏţ ƃē ρēŗşįşţēƌ, ƃēċąűşē įţ űşēş ą ʋįēŵ ŵįţĥ Ďąţą Āċċēşş Ĉŏŋţŗŏĺ (ĎĀĈ) ţĥąţ ƃēĺŏŋğş ţŏ ą ƌįƒƒēŗēŋţ şρąċē.]]]
#XMSG: error message during start persistence of a view (backend error)
DACERROR_STRUCTURE_STARTING_PERSISTENCE=[[[Ţĥē ʋįēŵ "{0}" ċąŋŋŏţ ƃē ρēŗşįşţēƌ ƃēċąűşē ţĥē şţŗűċţűŗē ŏƒ ŏŋē ŏŗ ɱŏŗē ŏƒ įţş Ďąţą Āċċēşş Ĉŏŋţŗŏĺş (ĎĀĈ) ƌŏēş ŋŏţ şűρρŏŗţ ƌąţą ρēŗşįşţēŋċē.]]]
#XMSG: error message during stop persistence of a view (backend error)
STOP_PERSISTENCE_ERROR=[[[Āŋ ēŗŗŏŗ ŏċċűŗŗēƌ ŵĥįĺē şţŏρρįŋğ ţĥē ρēŗşįşţēŋċē ƒŏŗ ʋįēŵ "{0}".]]]
#XMSG: error message during stop persistence of a view (backend error)
DELETE_PERSISTED_VIEW_ERROR=[[[Āŋ ēŗŗŏŗ ŏċċűŗŗēƌ ŵĥįĺē ƌēĺēţįŋğ ţĥē ρēŗşįşţēƌ ʋįēŵ "{0}".]]]
#XMSG: Warning message for removing the persisted data
RemovePersistency_Confirm_Msg=[[[Ďŏ ŷŏű ŵąŋţ ţŏ ƌēĺēţē ţĥē ρēŗşįşţēƌ ƌąţą ąŋƌ şŵįţċĥ ţŏ ʋįŗţűąĺ ąċċēşş ŏƒ ʋįēŵ "{0}"?]]]
#XMSG: Warning message for delete the view persistence
DeletePersistency_Confirm_Msg=[[[Ďŏ ŷŏű ŵąŋţ ţŏ ŗēɱŏʋē ţĥē ʋįēŵ ƒŗŏɱ ɱŏŋįţŏŗįŋğ ĺįşţ ąŋƌ ƌēĺēţē ţĥē ρēŗşįşţēƌ ƌąţą ŏƒ ʋįēŵ "{0}"?]]]
#XMSG: error message for reading data from backend
txtReadBackendError=[[[Ĭţ ĺŏŏķş ĺįķē ţĥēŗē ŵąş ąŋ ēŗŗŏŗ ŵĥįĺē ŗēąƌįŋğ ƒŗŏɱ ţĥē ƃąċķ ēŋƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Label for No Data Error
NoDataError=[[[Ĕŗŗŏŗ∙∙∙∙∙∙∙∙∙]]]
#XMSG: message for conflicting task
Task_Already_Running=[[[Ā ċŏŋƒĺįċţįŋğ ţąşķ įş ąĺŗēąƌŷ ŗűŋŋįŋğ ƒŏŗ ţĥē ʋįēŵ "{0}".]]]

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Show All Views~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Text for show all views button
allViewsText=[[[Āĺĺ Ʋįēŵş ({0})]]]
#XBUT: Text for show scheduled views button
scheduledText=[[[Ŝċĥēƌűĺēƌ ({0})]]]
#XBUT: Text for show persisted views button
persistedText=[[[Ƥēŗşįşţēƌ ({0})]]]
#XBUT: Text for start analyzer button
startAnalyzer=[[[Ŝţąŗţ Ʋįēŵ Āŋąĺŷžēŗ∙∙∙∙∙]]]
#XFLD: Message if repository is unavailable
repositoryErrorMsg=[[[Ţĥē ŗēρŏşįţŏŗŷ įşŋ’ţ ąʋąįĺąƃĺē ąŋƌ ċēŗţąįŋ ƒēąţűŗēş ąŗē ƌįşąƃĺēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]

#XFLD: Data Access - Virtual
Virtual=[[[Ʋįŗţűąĺ∙∙∙∙∙∙∙]]]
#XFLD: Data Access - Persisted
Persisted=[[[Ƥēŗşįşţēƌ∙∙∙∙∙]]]

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Non-Persisted View Selection Dialog~~~~~~~~~~~~~~~~~~~~~~~

#XTIT: Title for Non-Persisted View selection dialog
select_source=[[[Ŝēĺēċţ Ʋįēŵ ţŏ Ƥēŗşįşţ∙∙∙∙∙]]]

#XTIT: Placeholder for Non-Persisted View selection dialog search
searchView=[[[Ŝēąŗċĥ Ʋįēŵş∙∙∙∙∙∙∙]]]
#XTIT: No data in the list of non-persisted view
No_Data=[[[Ńŏ Ďąţą∙∙∙∙∙∙∙]]]
#XBUT: Button to select non-persisted view
ok=[[[Ŏķ∙∙]]]
#XBUT: Button to close the non-persisted views selection dialog
cancel=[[[Ĉąŋċēĺ∙∙∙∙∙∙∙∙]]]

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~View persistency log message key~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message for view persistency logs
START_STOP_PERSISTENCY=[[[Ŝţąŗţįŋğ ƌąţą ρēŗşįşţēŋċē ţąşķ ŗűŋ ƒŏŗ "{1}".]]]
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY=[[[Ƥēŗşįşţįŋğ ƌąţą ƒŏŗ ţĥē ʋįēŵ "{1}".]]]
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY)
START_VIEW_PERSISTENCY_PROCESS=[[[Ŝţąŗţįŋğ ţĥē ρŗŏċēşş ţŏ ρēŗşįşţ ƌąţą ƒŏŗ ţĥē ʋįēŵ "{0}".]]]
#XFLD: Message for view persistency manual partition load
START_VIEW_PERSISTENCY_PROCESS_MANUAL_PARTITIONS=[[[Ŝţąŗţįŋğ ţĥē ρŗŏċēşş ţŏ ρēŗşįşţ ƌąţą ƒŏŗ ţĥē ʋįēŵ "{0}" ŵįţĥ şēĺēċţēƌ ρąŗţįţįŏŋ ĬĎş: "{1}".]]]
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY=[[[Řēɱŏʋįŋğ ρēŗşįşţēƌ ƌąţą ƒŏŗ ţĥē ʋįēŵ "{1}".]]]
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY)
REMOVE_VIEW_PERSISTENCY=[[[Ŝţąŗţįŋğ ţĥē ρŗŏċēşş ţŏ ŗēɱŏʋē ρēŗşįşţēƌ ƌąţą ƒŏŗ ţĥē ʋįēŵ "{0}".]]]
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_SUCCESS=[[[Ďąţą įş ρēŗşįşţēƌ ƒŏŗ ţĥē ʋįēŵ "{1}".]]]
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_SUCCESS)
START_VIEW_PERSISTENCY_PROCESS_SUCCESS=[[[Ďąţą įş ρēŗşįşţēƌ ƒŏŗ ţĥē ʋįēŵ "{0}".]]]
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY_SUCCESS=[[[Ƥēŗşįşţēƌ ƌąţą ŗēɱŏʋēƌ ąŋƌ ʋįŗţűąĺ ƌąţą ąċċēşş ŗēşţŏŗēƌ ƒŏŗ ţĥē ʋįēŵ "{1}".]]]
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_SUCCESS)
REMOVE_VIEW_PERSISTENCY_SUCCESS=[[[Ĉŏɱρĺēţēƌ ţĥē ρŗŏċēşş ţŏ ŗēɱŏʋē ρēŗşįşţēƌ ƌąţą ƒŏŗ ţĥē ʋįēŵ "{0}".]]]
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_FAILED=[[[Ůŋąƃĺē ţŏ ρēŗşįşţ ƌąţą ƒŏŗ ţĥē ʋįēŵ "{1}".]]]
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_FAILED)
START_VIEW_PERSISTENCY_PROCESS_FAILED=[[[Ůŋąƃĺē ţŏ ρēŗşįşţ ƌąţą ƒŏŗ ţĥē ʋįēŵ "{0}".]]]
#XFLD: Message for view persistency logs (for messages created before version 2022.05)
STOP_VIEW_PERSISTENCY_FAILED=[[[Ůŋąƃĺē ţŏ ŗēɱŏʋē ρēŗşįşţēƌ ƌąţą ƒŏŗ ţĥē ʋįēŵ "{1}".]]]
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_FAILED)
REMOVE_VIEW_PERSISTENCY_FAILED=[[[Ůŋąƃĺē ţŏ ŗēɱŏʋē ρēŗşįşţēƌ ƌąţą ƒŏŗ ţĥē ʋįēŵ "{0}".]]]
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_SUCCESS=[[["{3}" ŗēċŏŗƌş ρēŗşįşţēƌ ƒŏŗ ţĥē ʋįēŵ "{1}".]]]
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace START_PERSISTENCY_RECORD_COUNT_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=[[[{0} ŗēċŏŗƌş įŋşēŗţēƌ įŋţŏ ţĥē ƌąţą ρēŗşįşţēŋċē ţąƃĺē ƒŏŗ ţĥē ʋįēŵ "{1}".]]]
#XFLD: Message used by SEAL repository for view persistency logs (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS_V2=[[[{0} ŗēċŏŗƌş įŋşēŗţēƌ įŋţŏ ţĥē ƌąţą ρēŗşįşţēŋċē ţąƃĺē ƒŏŗ ţĥē ʋįēŵ "{1}". Μēɱŏŗŷ űşēƌ: {2} ĢįƁ.]]]
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_SUCCESS=[[["{3}" ρēŗşįşţēƌ ŗēċŏŗƌş ŗēɱŏʋēƌ ƒŏŗ ţĥē ʋįēŵ "{1}".]]]
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace STOP_PERSISTENCY_RECORD_COUNT_SUCCESS)
REMOVE_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=[[[Ƥēŗşįşţēƌ ƌąţą ŗēɱŏʋēƌ, "{0}" ρēŗşįşţēƌ ŗēċŏŗƌş ƌēĺēţēƌ.]]]
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_FAILED=[[[Ģēţ ŗēċŏŗƌĈŏűŋţ ƒąįĺēƌ ƒŏŗ ţĥē ʋįēŵ "{1}".]]]
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_FAILED=[[[Ģēţ ŗēċŏŗƌĈŏűŋţ ƒąįĺēƌ ƒŏŗ ţĥē ʋįēŵ "{1}".]]]
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=[[[Ŝċĥēƌűĺē įş ƌēĺēţēƌ ƒŏŗ "{1}".]]]
#XFLD: Message for view persistency logs (new message to replace STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS)
REMOVE_VIEW_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=[[[Ŝċĥēƌűĺē įş ƌēĺēţēƌ ƒŏŗ ţĥē ʋįēŵ "{0}".]]]
#XFLD: Message for view persistency logs
STOP_PERSISTENCY_DELETE_SCHEDULE_FAILED=[[[Ŝċĥēƌűĺē ƌēĺēţįŏŋ ƒąįĺēƌ ƒŏŗ "{1}".]]]
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_REDEPLOY=[[[Ŵē ċąŋ’ţ ρēŗşįşţ ţĥē ʋįēŵ "{0}" ƃēċąűşē įţ ĥąş ƃēēŋ ċĥąŋğēƌ ąŋƌ ƌēρĺŏŷēƌ şįŋċē ŷŏű şţąŗţēƌ ţŏ ρēŗşįşţ įţ. Ţŗŷ ąğąįŋ ţŏ ρēŗşįşţ ţĥē ʋįēŵ ŏŗ ŵąįţ űŋţįĺ ţĥē ŋēχţ şċĥēƌűĺēƌ ŗűŋ.]]]
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_DELETE=[[[Ŵē ċąŋ’ţ ρēŗşįşţ ţĥē ʋįēŵ "{0}" ƃēċąűşē įţ ĥąş ƃēēŋ ƌēĺēţēƌ şįŋċē ŷŏű şţąŗţēƌ ţŏ ρēŗşįşţ įţ.]]]
#XFLD: Message for successful data persistency of a single partition (for messages created before version 2022.03)
START_PERSISTENCY_SINGLE_PARTITION_SUCCESS=[[[{0} ŗēċŏŗƌş ρēŗşįşţēƌ įŋţŏ ρąŗţįţįŏŋ ƒŏŗ ʋąĺűēş "{1}" <= {2} < "{3}".]]]
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (new message to replace START_PERSISTENCY_SINGLE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS=[[[{0} ŗēċŏŗƌş įŋşēŗţēƌ įŋţŏ ρąŗţįţįŏŋ ƒŏŗ ʋąĺűēş "{1}" <= "{2}" < "{3}".]]]
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS_V2=[[[{0} ŗēċŏŗƌş įŋşēŗţēƌ įŋţŏ ρąŗţįţįŏŋ ƒŏŗ ʋąĺűēş "{1}" <= "{2}" < "{3}". Μēɱŏŗŷ űşēƌ: {4} ĢįƁ.]]]
#XFLD: Message for successful replication of a "others" partition (for messages created before version 2022.03)
START_PERSISTENCY_OTHERS_PARTITION_SUCCESS=[[[{0} ŗēċŏŗƌş ρēŗşįşţēƌ įŋţŏ "ŏţĥēŗş" ρąŗţįţįŏŋ.]]]
#XFLD: Message used by SEAL repository for successful replication of a "others" partition (new message to replace START_PERSISTENCY_OTHERS_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_OTHERS_PARTITION_SUCCESS=[[[{0} ŗēċŏŗƌş įŋşēŗţēƌ įŋţŏ "ŏţĥēŗş" ρąŗţįţįŏŋ.]]]
#XFLD: Message for view persistency record count of partitions (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS=[[[{3} ŗēċŏŗƌş ρēŗşįşţēƌ ƒŏŗ ţĥē ʋįēŵ "{1}" įŋ {4} ρąŗţįţįŏŋş.]]]
#XFLD: Message used by SEAL repository for view persistency record count of partitions (new message to replace START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS=[[[{0} ŗēċŏŗƌş įŋşēŗţēƌ įŋţŏ ţĥē ƌąţą ρēŗşįşţēŋċē ţąƃĺē ƒŏŗ ţĥē ʋįēŵ "{1}" įŋ {2} ρąŗţįţįŏŋş.]]]
#XFLD: Message used by SEAL repository for view persistency for partition wise refresh
START_VIEW_PERSISTENCY_RECORD_COUNT_REFRESHED_PARTITIONS_SUCCESS=[[[{0} ŗēċŏŗƌş įŋşēŗţēƌ įŋţŏ ţĥē ƌąţą ρēŗşįşţēŋċē ţąƃĺē ƒŏŗ ţĥē ʋįēŵ "{1}". Ůρƌąţēƌ ρąŗţįţįŏŋş: {2}; Ļŏċķēƌ ρąŗţįţįŏŋş: {3}; Ţŏţąĺ ρąŗţįţįŏŋş: {4}]]]
#XFLD: Message used by SEAL repository for view persistency for manual partition load
START_VIEW_PERSISTENCY_RECORD_COUNT_MANUAL_PARTITIONS_SUCCESS=[[[{0} ŗēċŏŗƌş įŋşēŗţēƌ įŋţŏ ƌąţą ρēŗşįşţēŋċē ţąƃĺē ƒŏŗ ţĥē ʋįēŵ "{1}" įŋ {2} şēĺēċţēƌ ρąŗţįţįŏŋş]]]
#XFLD: Message used by SEAL repository for view persistency for locked partition data load
START_VIEW_PERSISTENCY_RECORD_COUNT_LOCKED_PARTITIONS_SUCCESS=[[[{0} ŗēċŏŗƌş įŋşēŗţēƌ įŋţŏ ţĥē ƌąţą ρēŗşįşţēŋċē ţąƃĺē ƒŏŗ ţĥē ʋįēŵ "{1}". Ůρƌąţēƌ ρąŗţįţįŏŋş: {2}; Ļŏċķēƌ, űŋċĥąŋğēƌ ρąŗţįţįŏŋş: {3}; Ţŏţąĺ ρąŗţįţįŏŋş: {4}]]]
#XMSG: Message for unexpected error (for messages created before version 2022.03)
START_PERSISTENCY_UNEXPECTED_ERROR=[[[Āŋ űŋēχρēċţēƌ ēŗŗŏŗ ŏċċűŗŗēƌ ŵĥįĺē ρēŗşįşţįŋğ ƌąţą ƒŏŗ ţĥē ʋįēŵ "{0}".]]]
#XMSG: Message for unexpected error (new message to replace START_PERSISTENCY_UNEXPECTED_ERROR)
START_VIEW_PERSISTENCY_UNEXPECTED_ERROR=[[[Āŋ űŋēχρēċţēƌ ēŗŗŏŗ ŏċċűŗŗēƌ ŵĥįĺē ρēŗşįşţįŋğ ƌąţą ƒŏŗ ţĥē ʋįēŵ "{0}".]]]
#XMSG: Message for Locked space error
START_VIEW_PERSISTENCY_LOCKED_SPACE_ERROR=[[[Ţĥē ʋįēŵ "{0}" ċąŋ’ţ ƃē ρēŗşįşţēƌ ƃēċąűşē ţĥē şρąċē "{1}" įş ĺŏċķēƌ.]]]
#XMSG: Message for unexpected error (for messages created before version 2022.03)
STOP_PERSISTENCY_UNEXPECTED_ERROR=[[[Āŋ űŋēχρēċţēƌ ēŗŗŏŗ ŏċċűŗŗēƌ ŵĥįĺē ŗēɱŏʋįŋğ ρēŗşįşţēƌ ƌąţą ƒŏŗ ţĥē ʋįēŵ "{0}".]]]
#XMSG: Message for unexpected error (new message to replace STOP_PERSISTENCY_UNEXPECTED_ERROR)
REMOVE_VIEW_PERSISTENCY_UNEXPECTED_ERROR=[[[Āŋ űŋēχρēċţēƌ ēŗŗŏŗ ŏċċűŗŗēƌ ŵĥįĺē ŗēɱŏʋįŋğ ρēŗşįşţēŋċē ƒŏŗ ţĥē ʋįēŵ "{0}".]]]
#XMSG: Message for ivalidated view
VIEW_PERSISTENCY_ABORTED_DEFINITION_INVALID=[[[Ďēƒįŋįţįŏŋ ŏƒ ţĥē ʋįēŵ "{0}" ĥąş ƃēċŏɱē įŋʋąĺįƌ, ɱŏşţ ρŗŏƃąƃĺŷ ƌűē ţŏ ą ċĥąŋğē ŏƒ ąŋ ŏƃĵēċţ ċŏŋşűɱēƌ ƌįŗēċţĺŷ ŏŗ įŋƌįŗēċţĺŷ ƃŷ ţĥē ʋįēŵ. Ţŗŷ ţŏ ŗēƌēρĺŏŷ ţĥē ʋįēŵ ţŏ şŏĺʋē ţĥē įşşűē, ŏŗ ţŏ įƌēŋţįƒŷ ţĥē ŗŏŏţ ċąűşē.]]]
#XMSG: Message for persisted data removal (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT=[[[Ƥēŗşįşţēƌ ƌąţą įş ŗēɱŏʋēƌ ŵĥįĺē ƌēρĺŏŷįŋğ ʋįēŵ "{0}".]]]
#XMSG: Message for persisted data removal of a consuming view
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW=[[[Ƥēŗşįşţēƌ ƌąţą įş ŗēɱŏʋēƌ ŵĥįĺē ƌēρĺŏŷįŋğ ţĥē ċŏŋşűɱēƌ ʋįēŵ "{0}".]]]
#XMSG: Message for persisted data removal of a consuming view due to a dac change
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW_DAC_CHANGED=[[[Ƥēŗşįşţēƌ ƌąţą įş ŗēɱŏʋēƌ ŵĥįĺē ƌēρĺŏŷįŋğ ţĥē ċŏŋşűɱēƌ ʋįēŵ "{0}" ƃēċąűşē įţş ƌąţą ąċċēşş ċŏŋţŗŏĺ ĥąş ċĥąŋğēƌ.]]]
#XMSG: Message for persisted data removal (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DEPLOYMENT=[[[Ƥēŗşįşţēƌ ƌąţą įş ŗēɱŏʋēƌ ŵĥįĺē ƌēρĺŏŷįŋğ ţĥē ʋįēŵ "{0}".]]]
#XMSG: Message for deleted view (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION=[[[Ƥēŗşįşţēŋċē įş ŗēɱŏʋēƌ ŵįţĥ ƌēĺēţįŏŋ ŏƒ ţĥē ʋįēŵ "{0}".]]]
#XMSG: Message for deleted view (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DELETION=[[[Ƥēŗşįşţēŋċē įş ŗēɱŏʋēƌ ŵįţĥ ƌēĺēţįŏŋ ŏƒ ţĥē ʋįēŵ "{0}".]]]
#XMSG Message for removal of persisted data, because prerequisites not fulfilled
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_PREREQUISITES_NOT_FULFILLED=[[[Ƥēŗşįşţēƌ ƌąţą įş ŗēɱŏʋēƌ, ƃēċąűşē ƌąţą ρēŗşįşţēŋċē ρŗēŗēƣűįşįţēş ąŗē ŋŏ ĺŏŋğēŗ ƒűĺƒįĺĺēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Message for inconsistent persistency
VIEW_PERSISTENCY_INCONSISTENT_PERSISTENCY=[[[Ţĥē ρēŗşįşţēŋċē ŏƒ ţĥē ʋįēŵ "{0}" ĥąş ƃēċŏɱē įŋċŏŋşįşţēŋţ. Řēɱŏʋē ρēŗşįşţēƌ ƌąţą ţŏ şŏĺʋē ţĥē įşşűē.]]]
#XMSG: Message for prerequisites
START_PERSISTENCY_CHECK_PREREQUISITES=[[[Ĉĥēċķįŋğ ţĥē ρŗēŗēƣűįşįţēş ţŏ ρēŗşįşţ ţĥē ʋįēŵ "{0}".]]]
#XMSG: Message for DAC deployed in deprecated way using table function and join (for messages created before 2025.05)
START_PERSISTENCY_DEPRECIATED_DAC=[[[Ţĥē ʋįēŵ "{0}" įş ƌēρĺŏŷēƌ űşįŋğ Ďąţą Āċċēşş Ĉŏŋţŗŏĺ (ĎĀĈ) ţĥąţ įş ƃēįŋğ ƌēρŗēċąţēƌ. Ƥĺēąşē ƌēρĺŏŷ ţĥē ʋįēŵ ąğąįŋ ţŏ įɱρŗŏʋē ţĥē ρēŗƒŏŗɱąŋċē.]]]
#XMSG: Message for DAC deployed in deprecated way using table function and join
START_PERSISTENCY_DEPRECATED_DAC=[[[Ţĥē ʋįēŵ "{0}" įş ƌēρĺŏŷēƌ űşįŋğ Ďąţą Āċċēşş Ĉŏŋţŗŏĺ (ĎĀĈ) ţĥąţ įş ƃēįŋğ ƌēρŗēċąţēƌ. Ƥĺēąşē ƌēρĺŏŷ ţĥē ʋįēŵ ąğąįŋ ţŏ įɱρŗŏʋē ţĥē ρēŗƒŏŗɱąŋċē.]]]
#XMSG: Message for roll back (for messages created before version 2022.03)
ROLLBACK_VIEW_PERSISTENCY=[[[Āŋ ēŗŗŏŗ ŏċċűŗŗēƌ. Ƥŗēʋįŏűş şţąţē ŏƒ ρēŗşįşţēŋċē ŗēşţŏŗēƌ ƒŏŗ ʋįēŵ "{0}".]]]
#XMSG: Message used by SEAL repository for rollback of running procedure to persist view (new message to replace ROLLBACK_VIEW_PERSISTENCY)
VIEW_PERSISTENCY_ROLLBACK=[[[Āŋ ēŗŗŏŗ ŏċċűŗŗēƌ. Ţĥē ρŗŏċēşş ţŏ ρēŗşįşţ ţĥē ʋįēŵ "{0}" ĥąş ƃēēŋ şţŏρρēƌ ąŋƌ ċĥąŋğēş ĥąʋē ƃēēŋ ŗŏĺĺēƌ ƃąċķ.]]]
#XMSG: Message used by SEAL repository for rollback of running procedure to remove persisted data of view
REMOVE_VIEW_PERSISTENCY_ROLLBACK=[[[Āŋ ēŗŗŏŗ ŏċċűŗŗēƌ. Ţĥē ρŗŏċēşş ţŏ ŗēɱŏʋē ρēŗşįşţēƌ ƌąţą ŏƒ ţĥē ʋįēŵ "{0}" ĥąş ƃēēŋ şţŏρρēƌ ąŋƌ ċĥąŋğēş ĥąʋē ƃēēŋ ŗŏĺĺēƌ ƃąċķ.]]]
#XMSG: Message used by SEAL repository for persistency preparation
START_PERSISTENCY_PREPARATION=[[[Ƥŗēρąŗįŋğ ţŏ ρēŗşįşţ ƌąţą.∙∙∙∙∙∙∙]]]
#XMSG: Message used by SEAL repository for data insertion
START_INSERTING_PERSISTENCY_DATA=[[[Ĭŋşēŗţįŋğ ƌąţą įŋ ρēŗşįşţēŋċŷ ţąƃĺē.∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Message for null values - partitioning (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS=[[[{0} ŋűĺĺ ʋąĺűē ŗēċŏŗƌş įŋşēŗţēƌ įŋţŏ "ŏţĥēŗş" ρąŗţįţįŏŋ.]]]
#XMSG: Message used by SEAL repository for others partition values - partitioning (new message to replace START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS=[[[{0} ŗēċŏŗƌş įŋşēŗţēƌ įŋţŏ "ŏţĥēŗş" ρąŗţįţįŏŋ ƒŏŗ ʋąĺűēş "{2}" < "{1}" ŎŘ "{2}" >= "{3}".]]]
#XMSG: Message used by SEAL repository for others partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS_V2=[[[{0} ŗēċŏŗƌş įŋşēŗţēƌ įŋţŏ "ŏţĥēŗş" ρąŗţįţįŏŋ ƒŏŗ ʋąĺűēş "{2}" < "{1}" ŎŘ "{2}" >= "{3}". Μēɱŏŗŷ űşēƌ: {4} ĢįƁ.]]]
#XMSG: Message used by SEAL repository for null partition values - partitioning
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS=[[[{0} ŗēċŏŗƌş įŋşēŗţēƌ įŋţŏ "ŏţĥēŗş" ρąŗţįţįŏŋ ƒŏŗ ʋąĺűēş "{1}" ĬŜ ŃŮĻĻ.]]]
#XMSG: Message used by SEAL repository for null partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS_V2=[[[{0} ŗēċŏŗƌş įŋşēŗţēƌ įŋţŏ "ŏţĥēŗş" ρąŗţįţįŏŋ ƒŏŗ ʋąĺűēş "{1}" ĬŜ ŃŮĻĻ. Μēɱŏŗŷ űşēƌ: {2} ĢįƁ.]]]
##XFLD:Message used by SEAL repository for remote statistics
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS=[[[Ļŏąƌįŋğ ţĥē ƌąţą įŋʋŏĺʋēƌ: {0} ŗēɱŏţē şţąţēɱēŋţş. Ţŏţąĺ ŗēċŏŗƌş ƒēţċĥēƌ: {1}. Ţŏţąĺ ƌűŗąţįŏŋ ţįɱē: {2} şēċŏŋƌş.]]]
##XFLD:Message used by SEAL repository for remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS_WITH_PARTITIONS=[[[Ļŏąƌįŋğ ƌąţą įŋʋŏĺʋēƌ űşįŋğ {0} ρąŗţįţįŏŋş ŵįţĥ {1} ŗēɱŏţē şţąţēɱēŋţş. Ţŏţąĺ ŗēċŏŗƌş ƒēţċĥēƌ: {2}. Ţŏţąĺ ƌűŗąţįŏŋ ţįɱē: {3} şēċŏŋƌş.]]]
##XFLD:Message used by SEAL repository with UI ID for instructions of remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_INSTRUCTION=[[[Ţĥē ŗēɱŏţē şţąţēɱēŋţş ρŗŏċēşşēƌ ƌűŗįŋğ ţĥē ŗűŋ ċąŋ ƃē ƌįşρĺąŷēƌ ƃŷ ŏρēŋįŋğ ţĥē ŗēɱŏţē ƣűēŗŷ ɱŏŋįţŏŗ, įŋ ţĥē ƌēţąįĺş ŏƒ ţĥē ρąŗţįţįŏŋ-şρēċįƒįċ ɱēşşąğēş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
##XFLD:Message for start reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS=[[[Ŝţąŗţįŋğ ţĥē ρŗŏċēşş ţŏ ŗēűşē ēχįşţįŋğ ρēŗşįşţēƌ ƌąţą ƒŏŗ ʋįēŵ {0} ąƒţēŗ ƌēρĺŏŷɱēŋţ.]]]
##XFLD:Message used by SEAL repository for reattach view persitency
START_VIEW_PERSISTENCY_REATTACHMENT=[[[Ŝţąŗţ ţŏ ŗēűşē ţĥē ēχįşţįŋğ ρēŗşįşţēƌ ƌąţą.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
##XFLD:Message used by SEAL repository for reattach view persitency
START_FINILIZING_VIEW_PERSISTENCY_REATTACHMENT=[[[Řēűşįŋğ ţĥē ēχįşţįŋğ ρēŗşįşţēƌ ƌąţą.∙∙∙∙∙∙∙∙∙∙∙∙]]]
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_COMPLETE=[[[Ţĥē ρŗŏċēşş ţŏ ŗēűşē ēχįşţįŋğ ρēŗşįşţēƌ ƌąţą įş ċŏɱρĺēţēƌ ƒŏŗ ţĥē ʋįēŵ {0}.]]]
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_FAILED=[[[Ƒąįĺēƌ ţŏ ŗēűşē ţĥē ēχįşţįŋğ ρēŗşįşţēƌ ƌąţą ƒŏŗ ʋįēŵ {0} ąƒţēŗ ƌēρĺŏŷɱēŋţ.]]]
#XMSG: Message used by SEAL repository for finalizing persistency
START_FINALIZING_PERSISTENCY=[[[Ƒįŋąĺįžįŋğ ρēŗşįşţēŋċē.∙∙∙∙∙∙]]]
#XMSG: Message used by SEAL repository for restoring virtual access for view
REMOVE_VIEW_PERSISTENCY_VIRTUAL_ACCESS_RESTORED=[[[Ţĥē ʋįŗţűąĺ ƌąţą ąċċēşş įş ŗēşţŏŗēƌ ƒŏŗ ţĥē ʋįēŵ "{0}".]]]
#XMSG: Message used by SEAL repository for view that already has virtual access
REMOVE_VIEW_PERSISTENCY_ALREADY_VIRTUAL_ACCESS=[[[Ţĥē ʋįēŵ "{0}" ąĺŗēąƌŷ ĥąş ʋįŗţűąĺ ƌąţą ąċċēşş. Ńŏ ρēŗşįşţēƌ ƌąţą įş ŗēɱŏʋēƌ.]]]
#XMSG: Message for removing view persistency
VIEW_PERSISTENCY_REMOVED_FROM_MONITOR=[[[Ţĥē ʋįēŵ "{0}" ĥąş ƃēēŋ ŗēɱŏʋēƌ ƒŗŏɱ ţĥē Ʋįēŵş ɱŏŋįţŏŗ.]]]
#XMSG: Message for view doesnt exist.
VIEW_PERSISTENCY_VIEW_NOT_EXISTING=[[[Ţĥē ʋįēŵ "{0}" ēįţĥēŗ ƌŏēşŋ’ţ ēχįşţ įŋ ţĥē ƌąţąƃąşē ŏŗ įş ŋŏţ ċŏŗŗēċţĺŷ ƌēρĺŏŷēƌ, ąŋƌ ţĥēŗēƒŏŗē ċąŋŋŏţ ƃē ρēŗşįşţēƌ. Ţŗŷ ţŏ ŗēƌēρĺŏŷ ţĥē ʋįēŵ ţŏ şŏĺʋē ţĥē įşşűē, ŏŗ ţŏ įƌēŋţįƒŷ ţĥē ŗŏŏţ ċąűşē.]]]
#XMSG: Message for persistency not enabled.
VIEW_PERSISTENCY_NOT_ENABLED=[[[Ďąţą ρēŗşįşţēŋċē įş ŋŏţ ēŋąƃĺēƌ. Řēƌēρĺŏŷ ą ţąƃĺē/ʋįēŵ įŋ ţĥē şρąċē "{0}" ţŏ ēŋąƃĺē ţĥē ƒűŋċţįŏŋąĺįţŷ.]]]
#XMSG: Message for unexpected error due to node crash
VIEW_PERSISTENCY_UNEXPECTED_ERROR_GENERAL=[[[Ļąşţ ʋįēŵ ρēŗşįşţēŋċŷ ŗűŋ ŵąş įŋţēŗŗűρţēƌ ƌűē ţŏ ţēċĥŋįċąĺ ēŗŗŏŗş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Message for view persistency memory consumption
VIEW_PERSISTENCY_MEMORY_CONSUMPTION=[[[{0} ĢįƁ ŏƒ ρēąķ ɱēɱŏŗŷ űşēƌ įŋ ţĥē ʋįēŵ ρēŗşįşţēŋċŷ ŗűŋţįɱē.]]]
#XFLD: Message for view persistency timeout
VIEW_PERSISTENCY_TIMEOUT=[[[Ƥēŗşįşţēŋċŷ ŏƒ ʋįēŵ {0} ŗēąċĥēƌ ţįɱēŏűţ ŏƒ {1} ĥŏűŗş.]]]
#XFLD: Message for not started asynchronous view persistency
VIEW_PERSISTENCY_ASYNC_NOT_STARTED=[[[Ĥįğĥ şŷşţēɱ ĺŏąƌ ρŗēʋēŋţēƌ ţĥē ąşŷŋċĥŗŏŋŏűş ēχēċűţįŏŋ ŏƒ ţĥē ʋįēŵ ρēŗşįşţēŋċŷ ƒŗŏɱ şţąŗţįŋğ. Ĉĥēċķ įƒ ţŏŏ ɱąŋŷ ţąşķş ąŗē ŗűŋŋįŋğ įŋ ρąŗąĺĺēĺ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency (for messages created before version 2022.05)
START_VIEW_PERSISTENCY_EXECUTION_METHOD_DROP_AND_RENAME=[[[Ĕχįşţįŋğ ρēŗşįşţēƌ ţąƃĺē ĥąş ƃēēŋ ƌēĺēţēƌ ąŋƌ ŗēρĺąċēƌ ŵįţĥ ą ŋēŵ ρēŗşįşţēƌ ţąƃĺē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_RECREATE_AND_DROP=[[[Ĕχįşţįŋğ ρēŗşįşţēƌ ţąƃĺē ĥąş ƃēēŋ ƌēĺēţēƌ ąŋƌ ŗēρĺąċēƌ ŵįţĥ ą ŋēŵ ρēŗşįşţēƌ ţąƃĺē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_TRUNCATE_AND_COPY=[[[Ĕχįşţįŋğ ρēŗşįşţēƌ ţąƃĺē ĥąş ƃēēŋ űρƌąţēƌ ŵįţĥ ŋēŵ ƌąţą.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
##XFLD: Message for missing authorizations for view persistency
VIEW_PERSISTENCY_MISSING_AUTH=[[[Μįşşįŋğ ąűţĥŏŗįžąţįŏŋş ƒŏŗ ƌąţą ρēŗşįşţēŋċē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
##XFLD: Message start of persistency cancellation
VIEW_PERSISTENCY_TRIGGER_CANCEL=[[[Ŝţąŗţįŋğ ţŏ ċąŋċēĺ ţĥē ρŗŏċēşş ţŏ ρēŗşįşţ ţĥē ʋįēŵ {0}.]]]
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_UNAVAILABLE_TASK_RUN=[[[Ƒąįĺēƌ ţŏ ċąŋċēĺ ţĥē ρŗŏċēşş ţŏ ρēŗşįşţ ţĥē ʋįēŵ ƃēċąűşē ţĥēŗē įş ŋŏ ŗűŋŋįŋğ ƌąţą ρēŗşįşţēŋċē ţąşķ ƒŏŗ ţĥē ʋįēŵ {0}.]]]
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_NONDETERMINISTIC_TASK_RUN=[[[Ƒąįĺēƌ ţŏ ċąŋċēĺ ţĥē ρŗŏċēşş ţŏ ρēŗşįşţ ţĥē ʋįēŵ ƃēċąűşē ŋŏ ƌąţą ρēŗşįşţēŋċē ţąşķ įş ŗűŋŋįŋğ ƒŏŗ ţĥē ʋįēŵ {0}.]]]
##XFLD: Message for canceling a non running persitency task
VIEW_PERSISTENCY_CANCEL_NON_RUNNING_SELECTED_TASK=[[[Ƒąįĺēƌ ţŏ ċąŋċēĺ ţĥē ρŗŏċēşş ţŏ ρēŗşįşţ ţĥē ʋįēŵ {0} ƃēċąűşē ţĥē şēĺēċţēƌ ƌąţą ρēŗşįşţēŋċē ţąşķ {1} įş ŋŏţ ŗűŋŋįŋğ.]]]
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_UNREACHABLE_PERSISTENCY_RUN=[[[Ƒąįĺēƌ ţŏ ċąŋċēĺ ţĥē ρŗŏċēşş ţŏ ρēŗşįşţ ţĥē ʋįēŵ ƃēċąűşē ƌąţą ρēŗşįşţēŋċē ƒŏŗ ţĥē ʋįēŵ {0} ĥąş ŋŏţ şţąŗţēƌ ŷēţ.]]]
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_COMPLETED_PERSISTENCY_RUN=[[[Ƒąįĺēƌ ţŏ ċąŋċēĺ ţĥē ρŗŏċēşş ţŏ ρēŗşįşţ ţĥē ʋįēŵ {0} ƃēċąűşē įţ ĥąş ąĺŗēąƌŷ ƃēēŋ ċŏɱρĺēţēƌ.]]]
##XFLD: Message for failed cancellation
VIEW_PERSISTENCY_ABORTED_CANCEL_EXECUTION=[[[Ƒąįĺēƌ ţŏ ċąŋċēĺ ţĥē ρŗŏċēşş ţŏ ρēŗşįşţ ţĥē ʋįēŵ {0}.]]]
##XFLD: Message for finalizing persistency cancellation
VIEW_PERSISTENCY_FINALIZE_CANCEL=[[[Ƥŗŏċēşş ţŏ şţŏρ ţĥē ƌąţą ρēŗşįşţēŋċē ŏƒ ţĥē ʋįēŵ {0} ĥąş ƃēēŋ şűƃɱįţţēƌ.]]]
##XFLD: Message used in persist task when cancellation is caught
VIEW_PERSISTENCY_CANCELED=[[[Ƥŗŏċēşş ţŏ ρēŗşįşţ ţĥē ʋįēŵ {0} ŵąş şţŏρρēƌ.]]]
##XFLD: Message used in persist task when cancelation is caught and performed through a cancelation task
VIEW_PERSISTENCY_CANCELED_VIA_CANCEL_TASK=[[[Ƥŗŏċēşş ţŏ ρēŗşįşţ ţĥē ʋįēŵ {0} ŵąş şţŏρρēƌ ʋįą ţĥē ċąŋċēĺąţįŏŋ ţąşķ {1}.]]]
##XFLD: Message used in cancel task when the task is triggered by deployment
VIEW_PERSISTENCY_CANCEL_TRIGGERED_BY_DEPLOYMENT=[[[Ĉąŋċēĺįŋğ ţĥē ρŗŏċēşş ţŏ ρēŗşįşţ ƌąţą ŵĥįĺē ƌēρĺŏŷįŋğ ţĥē ʋįēŵ {0}.]]]
##XFLD: Message used in case the cancelation is retriggered for a running task
VIEW_PERSISTENCY_CANCEL_ATTEMPT_RETRIGGER_CANCEL=[[[Ā ρŗēʋįŏűş ċąŋċēĺąţįŏŋ ţąşķ ţŏ ρēŗşįşţ ţĥē ʋįēŵ {0} ĥąş ąĺŗēąƌŷ ƃēēŋ şűƃɱįţţēƌ.]]]
##XFLD: Message used to express the delay that may occur before the persistency task is stopped
VIEW_PERSISTENCY_CANCEL_DELAY_STOPPING_PERSISTENCY=[[[Ţĥēŗē ɱąŷ ƃē ą ƌēĺąŷ űŋţįĺ ţĥē ƌąţą ρēŗşįşţēŋċē ţąşķ ƒŏŗ ţĥē ʋįēŵ {0} įş şţŏρρēƌ.]]]
##XFLD: Message used to reflect the task log identifier of the current running persistency task
VIEW_PERSISTENCY_CANCEL_CURRENT_RUNNING_TASK=[[[Ďąţą ƒŏŗ ʋįēŵ {0} įş ƃēįŋğ ρēŗşįşţēƌ ŵįţĥ ţĥē ţąşķ {1}.]]]
##XFLD:Message used by SEAL repository for locked partitions and DACs
VIEW_PERSISTENCY_LOCKED_PARTITIONS_DACS=[[[Āűţĥŏŗįžąţįŏŋş ρŗŏʋįƌēƌ ƃŷ ĎĀĈş ɱąŷ ĥąʋē ċĥąŋğēƌ ąŋƌ ąŗē ŋŏţ ċŏŋşįƌēŗēƌ ƃŷ ĺŏċķēƌ ρąŗţįţįŏŋş. Ůŋĺŏċķ ţĥē ρąŗţįţįŏŋş ąŋƌ ĺŏąƌ ą ŋēŵ şŋąρşĥŏţ ţŏ ąρρĺŷ ţĥē ċĥąŋğēş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
##XFLD:Message used by SEAL repository for column structure change
VIEW_PERSISTENCY_COLUMN_STRUCTURE_CHANGE=[[[Ţĥē ċŏĺűɱŋ şţŗűċţűŗē ĥąş ċĥąŋğēƌ ąŋƌ ŋŏ ĺŏŋğēŗ ɱąţċĥēş ţĥē ēχįşţįŋğ ρēŗşįşţēŋċē ţąƃĺē. Řēɱŏʋē ρēŗşįşţēƌ ƌąţą ąŋƌ şţąŗţ ą ŋēŵ ƌąţą ρēŗşįşţēŋċē ţŏ ğēţ ŷŏűŗ ρēŗşįşţēŋċē ţąƃĺē űρƌąţēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
##XFLD: Message for out of memory
VIEW_PERSISTENCY_HANA_OUT_OF_MEMORY=[[[Ţĥē ţąşķ ƒąįĺēƌ ƃēċąűşē ŏƒ ąŋ ŏűţ ŏƒ ɱēɱŏŗŷ ēŗŗŏŗ ŏŋ ţĥē ŜĀƤ ĤĀŃĀ ƌąţąƃąşē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_EXCEPTION_TRANSACTION_ROLLBACK=[[[Ţĥē ţąşķ ƒąįĺēƌ ƃēċąűşē ŏƒ ąŋ įŋţēŗŋąĺ ēχċēρţįŏŋ ŏŋ ţĥē ŜĀƤ ĤĀŃĀ ƌąţąƃąşē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_SQL_EXECUTION_ISSUE=[[[Ţĥē ţąşķ ƒąįĺēƌ ƃēċąűşē ŏƒ ąŋ įŋţēŗŋąĺ ŜǬĻ ēχēċűţįŏŋ įşşűē ŏŋ ţĥē ŜĀƤ ĤĀŃĀ ƌąţąƃąşē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
##XFLD: Message for HANA out of memory result
VIEW_PERSISTENCY_OUT_OF_MEMORY_RESULT_MESSAGE=[[[ĤĀŃĀ ŏűţ ŏƒ ɱēɱŏŗŷ ēʋēŋţ ŗēąşŏŋ: {0}]]]
##XFLD: Message for admission control rejection
VIEW_PERSISTENCY_HANA_ADMISSION_CTRL_REJECTION=[[[Ţĥē ţąşķ ƒąįĺēƌ ƃēċąűşē ŏƒ ąŋ ŜĀƤ ĤĀŃĀ Āƌɱįşşįŏŋ Ĉŏŋţŗŏĺ Řēĵēċţįŏŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
##XFLD: Message for HANA back pressure / rate limit of HANA client
VIEW_PERSISTENCY_HANA_CLIENT_RATE_LIMIT_REJECTION=[[[Ţĥē ţąşķ ƒąįĺēƌ ƃēċąűşē ŏƒ ţŏŏ ɱąŋŷ ąċţįʋē ŜĀƤ ĤĀŃĀ ċŏŋŋēċţįŏŋş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Message for invalid persisted table name
VIEW_PERSISTENCY_PERSISTED_TABLE_INVALID=[[[Āŋ ēŗŗŏŗ ŏċċűŗŗēƌ ąŋƌ ţĥē ρēŗşįşţēƌ ţąƃĺē ĥąş ƃēċŏɱē įŋʋąĺįƌ. Ţŏ şŏĺʋē ţĥē įşşűē, ρĺēąşē ŗēɱŏʋē ţĥē ρēŗşįşţēƌ ƌąţą ąŋƌ ρēŗşįşţ ţĥē ʋįēŵ ąğąįŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Message for usahe of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION=[[[Ţĥē ʋįēŵ ċąŋŋŏţ ƃē ρēŗşįşţēƌ. Ĭţ űşēş ą ŗēɱŏţē ţąƃĺē ƃąşēƌ ŏŋ ą ŗēɱŏţē şŏűŗċē ŵįţĥ ēŋąƃĺēƌ űşēŗ ρŗŏρąğąţįŏŋ. Ĉĥēċķ ţĥē ĺįŋēąğē ŏƒ ţĥē ʋįēŵ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Message for dynamic usage of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION_DYNAMIC=[[[Ţĥē ʋįēŵ ċąŋŋŏţ ƃē ρēŗşįşţēƌ. Ĭţ űşēş ą ŗēɱŏţē ţąƃĺē ƃąşēƌ ŏŋ ą ŗēɱŏţē şŏűŗċē ŵįţĥ ēŋąƃĺēƌ űşēŗ ρŗŏρąğąţįŏŋ. Ţĥē ŗēɱŏţē ţąƃĺē ɱąŷ ƃē ċŏŋşűɱēƌ ƌŷŋąɱįċąĺĺŷ ʋįą ą ŜǬĻ şċŗįρţ ʋįēŵ. Ţĥē ĺįŋēąğē ŏƒ ţĥē ʋįēŵ ɱąŷ ŋŏţ şĥŏŵ ţĥē ŗēɱŏţē ţąƃĺē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Message for insufficient privileges or consumption of view with DAC via dynamic SQL
VIEW_PERSISTENCY_INSUF_PRIV_OR_DAC_DYN_SQL=[[[Ŷŏűŗ ρŗįʋįĺēğēş ɱąŷ ƃē įŋşűƒƒįċįēŋţ. Ŏρēŋ ţĥē Ďąţą Ƥŗēʋįēŵ ţŏ şēē įƒ ŷŏű ĥąʋē ţĥē ρŗįʋįĺēğēş ŗēƣűįŗēƌ. Ĭƒ ŷēş, ą şēċŏŋƌ ʋįēŵ ċŏŋşűɱēƌ ʋįą ƌŷŋąɱįċ ŜǬĻ şċŗįρţ ɱąŷ ĥąʋē ƌąţą ąċċēşş ċŏŋţŗŏĺ (ĎĀĈ) ąρρĺįēƌ ţŏ įţ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: deployment of DAC is deprecated and view need to be redeployed to be able to persist data
VIEW_PERSISTENCY_DEPRECATED_DAC=[[[Ţĥē ʋįēŵ "{0}" įş ƌēρĺŏŷēƌ űşįŋğ Ďąţą Āċċēşş Ĉŏŋţŗŏĺ (ĎĀĈ) ţĥąţ įş ƃēįŋğ ƌēρŗēċąţēƌ. Ƥĺēąşē ƌēρĺŏŷ ţĥē ʋįēŵ ąğąįŋ ţŏ ƃē ąƃĺē ţŏ ρēŗşįşţ ƌąţą ƒŏŗ ţĥē ʋįēŵ.]]]
##XFLD:Message used by SEAL repository for input parameter
START_VIEW_PERSISTENCY_INPUT_PARAMETER=[[[Ůşįŋğ ƌēƒąűĺţ ʋąĺűē "{0}" ƒŏŗ įŋρűţ ρąŗąɱēţēŗ "{1}".]]]
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Elastic compute node keys~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
##XFLD:Message used to reflect disablement of ecn replica
ECN_DISABLE_EXISTING_REPLICA=[[[Ţĥē ēĺąşţįċ ċŏɱρűţē ŋŏƌē ŗēρĺįċą įş ƌįşąƃĺēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
##XFLD:Message used to reflect recreation of ecn replica
ECN_RECREATE_EXISTING_REPLICA=[[[Ţĥē ēĺąşţįċ ċŏɱρűţē ŋŏƌē ŗēρĺįċą įş ŗēċŗēąţēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
##XFLD:Message used to reflect re-enablement of ecn replica
ECN_ENABLE_EXISTING_REPLICA=[[[Ţĥē ēĺąşţįċ ċŏɱρűţē ŋŏƌē ŗēρĺįċą įş ŗē-ēŋąƃĺēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Task Schedule Menu key~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message to schedule task
scheduleText=[[[Ŝċĥēƌűĺē∙∙∙∙∙∙]]]
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=[[[Ĉŗēąţē Ŝċĥēƌűĺē∙∙∙∙]]]
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=[[[Ĕƌįţ Ŝċĥēƌűĺē∙∙∙∙∙∙]]]
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=[[[Ďēĺēţē Ŝċĥēƌűĺē∙∙∙∙]]]
#XFLD: Refresh frequency field
refreshFrequency=[[[Řēƒŗēşĥ Ƒŗēƣűēŋċŷ∙∙∙∙∙∙∙]]]
#XFLD: Refresh frequency field
refreshFrequencyNew=[[[Ƒŗēƣűēŋċŷ∙∙∙∙∙]]]
#XFLD: Refresh frequency field
refreshFrequencyNewNew=[[[Ŝċĥēƌűĺēƌ Ƒŗēƣűēŋċŷ∙∙∙∙∙]]]
#XBUT: label for None
none=[[[Ńŏŋē]]]
#XBUT: label for Real-Time replication state
realtime=[[[Řēąĺ-Ţįɱē∙∙∙∙∙]]]
#XFLD: Label for table column
txtNextSchedule=[[[Ńēχţ Řűŋ∙∙∙∙∙∙]]]
#XFLD: Label for table column
txtNextScheduleNew=[[[Ŝċĥēƌűĺēƌ Ńēχţ Řűŋ∙∙∙∙∙∙]]]
#XFLD: Label for table column
txtNumOfRecords=[[[Ńűɱƃēŗ ŏƒ Řēċŏŗƌş∙∙∙∙∙∙∙]]]
#XFLD: Label for scheduled link
scheduledTxt=[[[Ŝċĥēƌűĺēƌ∙∙∙∙∙]]]
#XFLD: LABEL for partially persisted link
partiallyPersisted=[[[Ƥąŗţįąĺĺŷ Ƥēŗşįşţēƌ∙∙∙∙∙]]]
#XFLD: Text for paused text
paused=[[[Ƥąűşēƌ∙∙∙∙∙∙∙∙]]]

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ View Monitor Tasklog Details Action keys~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message for release lock actionn
releaseLockMsg=[[[Ĭƒ ą ŗűŋ ţąķēş ĺŏŋğēŗ ţĥąŋ űşűąĺ, ţĥįş ɱįğĥţ įŋƌįċąţē ţĥąţ įţ ĥąş ƒąįĺēƌ ąŋƌ ţĥąţ ţĥē şţąţűş ĥąş ŋŏţ ƃēēŋ űρƌąţēƌ ąċċŏŗƌįŋğĺŷ. \\u0157\\u014B Ţŏ şŏĺʋē ţĥē įşşűē, ŷŏű ċąŋ ŗēĺēąşē ţĥē ĺŏċķ ąŋƌ şēţ įţş şţąţűş ţŏ ƒąįĺēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Label for release lock dialog
releaseLockText=[[[Řēĺēąşē Ļŏċķ∙∙∙∙∙∙∙]]]

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Column header tooltip ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: tooltip for table column
txtViewTooltip=[[[Ńąɱē ŏƒ ţĥē ρēŗşįşţēƌ ʋįēŵ∙∙∙∙∙∙∙]]]
#XFLD: tooltip for table column
txtViewDataAccessTooltip=[[[Ţĥįş įŋƌįċąţēş ţĥē ąʋąįĺąƃįĺįţŷ ŏƒ ţĥē ʋįēŵ∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: tooltip for table column
txtViewRefreshFreqTooltip=[[[Ţĥįş įŋƌįċąţēş ŵĥēţĥēŗ ą şċĥēƌűĺē įş ƌēƒįŋēƌ ƒŏŗ ţĥē ʋįēŵ∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: tooltip for table column
txtViewStatusTooltip=[[[Ģēţ ţĥē şţąţűş ŏƒ ţĥē ρēŗşįşţēƌ ʋįēŵ∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: tooltip for table column
txtViewLatestUpdateTooltip=[[[Ƥŗŏʋįƌēş įŋƒŏŗɱąţįŏŋ ąƃŏűţ ŵĥēŋ ţĥē ρēŗşįşţēƌ ʋįēŵ ŵąş ĺąşţ űρƌąţēƌ∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: tooltip for table column
txtViewNextRunTooltip=[[[Ĭƒ ą şċĥēƌűĺē įş şēţ ƒŏŗ ţĥē ʋįēŵ, şēē ƃŷ ŵĥēŋ ţĥē ŋēχţ ŗűŋ įş şċĥēƌűĺēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: tooltip for table column
txtViewNumOfRecordsTooltip=[[[Ţŗąċķ ŋűɱƃēŗ ŏƒ ŗēċŏŗƌş.∙∙∙∙∙∙]]]
#XFLD: tooltip for table column
txtViewInMemorySizeTooltip=[[[Ţŗąċķ ĥŏŵ ɱűċĥ şįžē ţĥē ʋįēŵ įş űşįŋğ įŋ ŷŏűŗ ɱēɱŏŗŷ∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: tooltip for table column
txtViewDiskSizeTooltip=[[[Ţŗąċķ ĥŏŵ ɱűċĥ şįžē ţĥē ʋįēŵ įş ţąķįŋğ ŏŋ ŷŏűŗ ƌįşķ∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Expired text
txtExpired=[[[Ĕχρįŗēƌ∙∙∙∙∙∙∙]]]

#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=[[[Ţĥē ŏƃĵēċţ "{0}" ċąŋŋŏţ ƃē ąƌƌēƌ ţŏ ţĥē ţąşķ ċĥąįŋ.]]]

#~~~~~~~~~~~View Analyzer task log messages~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message indicating number of records for a view, and memory consumption in Mebibyte during simulation of view persistency
RECORDS_PEAK_MEMORY=[[[Ţĥē ʋįēŵ "{0}" ĥąş {1} ŗēċŏŗƌş. Ā ƌąţą ρēŗşįşţēŋċē şįɱűĺąţįŏŋ ƒŏŗ ţĥįş ʋįēŵ űşēƌ {2} ΜįƁ ŏƒ ɱēɱŏŗŷ.]]]
#XMSG: View Analyzer execution failure
VIEW_ANALYZER_EXECUTION_FAILED=[[[Ʋįēŵ Āŋąĺŷžēŗ ēχēċűţįŏŋ ƒąįĺēƌ.∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Missing authorizations for View Analyzer
VIEW_ANALYZER_MISSING_AUTH=[[[Μįşşįŋğ ąűţĥŏŗįžąţįŏŋş ƒŏŗ Ʋįēŵ Āŋąĺŷžēŗ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Maximum memory in Gibibyte is reached, therefore persistency simulation is stopped
MAXIMUM_MEMORY_REACHED=[[[Ţĥē ɱąχįɱűɱ ɱēɱŏŗŷ ŏƒ {0} ĢįƁ ĥąş ƃēēŋ ŗēąċĥēƌ ŵĥįĺē şįɱűĺąţįŋğ ƌąţą ρēŗşįşţēŋċē ƒŏŗ ʋįēŵ "{1}". Ţĥēŗēƒŏŗē, ŋŏ ƒűŗţĥēŗ ƌąţą ρēŗşįşţēŋċē şįɱűĺąţįŏŋş ŵįĺĺ ƃē ŗűŋ.]]]
#XMSG: Error during persistency simulation occurred
PERSISTENCY_SIMULATION_ERROR=[[[Āŋ ēŗŗŏŗ ŏċċűŗŗēƌ ƌűŗįŋğ ƌąţą ρēŗşįşţēŋċē şįɱűĺąţįŏŋ ƒŏŗ ţĥē ʋįēŵ "{0}".]]]
#XMSG: Persistency simulation is not executed, because prerequisites are not fulfilled and the view cannot be persisted
PREREQUISITES_NOT_FULFILLED=[[[Ţĥē ƌąţą ρēŗşįşţēŋċē şįɱűĺąţįŏŋ įş ŋŏţ ēχēċűţēƌ ƒŏŗ ţĥē ʋįēŵ "{0}", ƃēċąűşē ρŗēŗēƣűįşįţēş ąŗē ŋŏţ ƒűĺƒįĺĺēƌ ąŋƌ ţĥē ʋįēŵ ċąŋŋŏţ ƃē ρēŗşįşţēƌ.]]]
#XMSG: Persistency simulation is not executed, because the view does not exist in the database
VIEW_DOES_NOT_EXIST=[[[Ŷŏű ɱűşţ ƌēρĺŏŷ ţĥē ʋįēŵ "{0}" ţŏ ēŋąƃĺē ţĥē ƌąţą ρēŗşįşţēŋċē şįɱűĺąţįŏŋ.]]]
#XMSG: Number of records cannot be determined, because the local table does not exist in the database
TABLE_DOES_NOT_EXIST=[[[Ţĥē ĺŏċąĺ ţąƃĺē "{0}" ƌŏēş ŋŏţ ēχįşţ įŋ ţĥē ƌąţąƃąşē, ţĥēŗēƒŏŗē ţĥē ŋűɱƃēŗ ŏƒ ŗēċŏŗƌş ċąŋŋŏţ ƃē ƌēţēŗɱįŋēƌ ƒŏŗ ţĥįş ţąƃĺē.]]]
##XMSG: Message start of cancellation for View Analyzer task
CANCEL_VIEW_ANALYZER_TRIGGERED=[[[Ƥŗŏċēşş ţŏ şţŏρ Ʋįēŵ Āŋąĺŷžēŗ ţąşķ {0} ƒŏŗ ʋįēŵ "{1}" ĥąş ƃēēŋ şűƃɱįţţēƌ. Ţĥēŗē ɱąŷ ƃē ą ƌēĺąŷ űŋţįĺ ţĥē ţąşķ įş şţŏρρēƌ.]]]
##XMSG: Message start to indicate that task is not active
CANCEL_VIEW_ANALYZER_NO_ACTIVE_TASK=[[[Ʋįēŵ Āŋąĺŷžēŗ ţąşķ {0} ƒŏŗ ţĥē ʋįēŵ "{1}" įş ŋŏţ ąċţįʋē.]]]
#XMSG: Cancel View Analyzer task failure
CANCEL_VIEW_ANALYZER_FAILED=[[[Ƒąįĺēƌ ţŏ ċąŋċēĺ Ʋįēŵ Āŋąĺŷžēŗ ţąşķ.∙∙∙∙∙∙∙∙∙∙∙∙]]]
##XMSG: Message used in View Analyzer execution task when task is canceled
VIEW_ANALYZER_CANCELED=[[[Ʋįēŵ Āŋąĺŷžēŗ ēχēċűţįŏŋ ƒŏŗ ţĥē ʋįēŵ "{0}" ŵąş şţŏρρēƌ ʋįą ą ċąŋċēĺąţįŏŋ ţąşķ.]]]
##XMSG: Message start of cancelation for Model Validation task
CANCEL_MODEL_VALIDATION_TRIGGERED=[[[Ƥŗŏċēşş ţŏ şţŏρ Μŏƌēĺ Ʋąĺįƌąţįŏŋ ţąşķ {0} ƒŏŗ ʋįēŵ "{1}" ĥąş ƃēēŋ şűƃɱįţţēƌ. Ţĥēŗē ɱąŷ ƃē ą ƌēĺąŷ űŋţįĺ ţĥē ţąşķ įş şţŏρρēƌ.]]]
##XMSG: Message to indicate that task is not active
CANCEL_MODEL_VALIDATION_NO_ACTIVE_TASK=[[[Μŏƌēĺ Ʋąĺįƌąţįŏŋ ţąşķ {0} ƒŏŗ ţĥē ʋįēŵ "{1}" įş ŋŏţ ąċţįʋē.]]]
#XMSG: Cancel Model Validation task failure
CANCEL_MODEL_VALIDATION_FAILED=[[[Ƒąįĺēƌ ţŏ ċąŋċēĺ Μŏƌēĺ Ʋąĺįƌąţįŏŋ ţąşķ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
##XMSG: Message used in Model Validation task when task is canceled
MODEL_VALIDATION_CANCELED=[[[Μŏƌēĺ Ʋąĺįƌąţįŏŋ ēχēċűţįŏŋ ƒŏŗ ţĥē ʋįēŵ "{0}" ŵąş şţŏρρēƌ ʋįą ą ċąŋċēĺąţįŏŋ ţąşķ.]]]
#XMSG: Message for Locked space error, before starting Model Validation task
MODEL_VALIDATION_LOCKED_SPACE_ERROR=[[[Ĉąŋŋŏţ ēχēċűţē Μŏƌēĺ Ʋąĺįƌąţįŏŋ ƒŏŗ ţĥē ʋįēŵ "{0}", ƃēċąűşē ţĥē şρąċē "{1}" įş ĺŏċķēƌ.]]]
##XMSG: Message used in View Analyzer execution task when determination of number of rows fails
NUMBER_ROWS_DETERMINATION_FAILED=[[[Āŋ ēŗŗŏŗ ŏċċűŗŗēƌ ŵĥįĺē ƌēţēŗɱįŋįŋğ ţĥē ŋűɱƃēŗ ŏƒ ŗŏŵş ƒŏŗ ţĥē ĺŏċąĺ ţąƃĺē "{0}".]]]
##XMSG: Message to indicate that SQL plan was created
EXECUTION_PLAN_CREATED=[[[ŜǬĻ Āŋąĺŷžēŗ ρĺąŋ ƒįĺē ƒŏŗ ţĥē ʋįēŵ "{0}" įş ċŗēąţēƌ ąŋƌ ċąŋ ƃē ƌŏŵŋĺŏąƌēƌ.]]]
#XFLD: Message to indicate starting of process to generate SQL Analyzer plan file
START_PLANVIZ_EXECUTION=[[[Ŝţąŗţįŋğ ρŗŏċēşş ţŏ ğēŋēŗąţē ŜǬĻ Āŋąĺŷžēŗ ρĺąŋ ƒįĺē ƒŏŗ ţĥē ʋįēŵ "{0}".]]]
#XFLD: Message to indicate starting of View Analyzer process
VIEW_ANALYZER_EXECUTION_STARTED=[[[Ŝţąŗţįŋğ Ʋįēŵ Āŋąĺŷžēŗ ēχēċűţįŏŋ.∙∙∙∙∙∙∙∙∙∙∙]]]
##XMSG: Message to indicate that SQL plan file cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_PLANVIZ_NOT_FULFILLED=[[[ŜǬĻ Āŋąĺŷžēŗ ρĺąŋ ƒįĺē ċąŋŋŏţ ƃē ğēŋēŗąţēƌ ƒŏŗ ţĥē ʋįēŵ "{0}", ƃēċąűşē ƌąţą ρēŗşįşţēŋċē ρŗēŗēƣűįşįţēş ąŗē ŋŏţ ƒűĺƒįĺĺēƌ.`]]]
##XMSG: Error message during  generation of SQL Analyzer plan file
PLANVIZ_ERROR=[[[Āŋ ēŗŗŏŗ ŏċċűŗŗēƌ ƌűŗįŋğ ğēŋēŗąţįŏŋ ŏƒ ŜǬĻ Āŋąĺŷžēŗ ρĺąŋ ƒįĺē ƒŏŗ ţĥē ʋįēŵ "{0}".]]]
#XMSG: Message for Locked space error
VIEW_ANALYZER_LOCKED_SPACE_ERROR=[[[Ĉąŋŋŏţ ēχēċűţē Ʋįēŵ Āŋąĺŷžēŗ ƒŏŗ ţĥē ʋįēŵ "{0}", ƃēċąűşē ţĥē şρąċē "{1}" įş ĺŏċķēƌ.]]]
#XMSG: Message for persistency simulation without partitioning
PERSISTENCY_SIMULATION_WITHOUT_PARTITIONS=[[[Ƥąŗţįţįŏŋş ŏƒ ţĥē ʋįēŵ "{0}" ąŗē ŋŏţ ċŏŋşįƌēŗēƌ ƌűŗįŋğ ƌąţą ρēŗşįşţēŋċē şįɱűĺąţįŏŋ.]]]
#XMSG: Message for generation of SQL Analyzer plan file without considering partitioning
PLANVIZ_WITHOUT_PARTITIONS=[[[Ƥąŗţįţįŏŋş ŏƒ ţĥē ʋįēŵ "{0}" ąŗē ŋŏţ ċŏŋşįƌēŗēƌ ƌűŗįŋğ ğēŋēŗąţįŏŋ ŏƒ ŜǬĻ Āŋąĺŷžēŗ ρĺąŋ ƒįĺē.]]]
#XMSG: Confirmation message for mass removal of persisted data
MassRemovePersistency_Confirm_Msg=[[[Ďŏ ŷŏű ŵąŋţ ţŏ ŗēɱŏʋē ţĥē ρēŗşįşţēƌ ƌąţą ąŋƌ şŵįţċĥ ţĥē ƌąţą ąċċēşş ƃąċķ ţŏ ʋįŗţűąĺ ąċċēşş?∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Confirmation message for partial mass removal of selected persisted data
PartialMassRemovePersistency_Confirm_Msg=[[[{0} ŏƒ ţĥē {1} şēĺēċţēƌ ʋįēŵş ĥąʋē ğŏţ ρēŗşįşţēƌ ƌąţą. \\u014B Ďŏ ŷŏű ŵąŋţ ţŏ ŗēɱŏʋē ţĥē ρēŗşįşţēƌ ƌąţą ąŋƌ şŵįţċĥ ţĥē ƌąţą ąċċēşş ƃąċķ ţŏ ʋįŗţűąĺ ąċċēşş?]]]
#XMSG: success message for stopping mass persistence
stopMassPersistenceSuccess=[[[Ŵē’ŗē ŗēɱŏʋįŋğ ρēŗşįşţēƌ ƌąţą ƒŏŗ ţĥē şēĺēċţēƌ ʋįēŵş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: error message during mass stop persistence of a view (backend error)
STOP_MASS_PERSISTENCE_ERROR=[[[Āŋ ēŗŗŏŗ ŏċċűŗŗēƌ ŵĥįĺē şţŏρρįŋğ ţĥē ρēŗşįşţēŋċē ƒŏŗ ţĥē şēĺēċţēƌ ʋįēŵş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: memory analysis is only performed in space {0}
NO_MEMORY_ANALYSIS=[[[Μēɱŏŗŷ ąŋąĺŷşįş įş ρēŗƒŏŗɱēƌ ƒŏŗ ēŋţįţįēş įŋ şρąċē "{0}" ŏŋĺŷ: "{1}" "{2}" ĥąş ƃēēŋ şķįρρēƌ.]]]
##XMSG: Message to indicate that Explain Plan cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_EXPLAINPLAN_NOT_FULFILLED=[[[Ĕχρĺąįŋ Ƥĺąŋ ƒįĺē ċąŋŋŏţ ƃē ğēŋēŗąţēƌ ƒŏŗ ţĥē ʋįēŵ "{0}", ƃēċąűşē ρēŗşįşţēŋċŷ ρŗēŗēƣűįşįţēş ąŗē ŋŏţ ƒűĺƒįĺĺēƌ.]]]
#XMSG: Message for Explain Plan generation without partitioning
EXPLAINPLAN_WITHOUT_PARTITIONS=[[[Ƥąŗţįţįŏŋş ŏƒ ţĥē ʋįēŵ "{0}" ąŗē ŋŏţ ċŏŋşįƌēŗēƌ ƌűŗįŋğ ğēŋēŗąţįŏŋ ŏƒ Ĕχρĺąįŋ Ƥĺąŋ ƒįĺē.]]]
#XFLD: Message to indicate starting of process to generate Explain Plan file
START_EXPLAINPLAN_EXECUTION=[[[Ŝţąŗţįŋğ ρŗŏċēşş ţŏ ğēŋēŗąţē Ĕχρĺąįŋ Ƥĺąŋ ƒįĺē ƒŏŗ ţĥē ʋįēŵ "{0}".]]]
##XMSG: Message to indicate that Explain Plan was created
EXPLAINPLAN_CREATED=[[[Ĕχρĺąįŋ Ƥĺąŋ ƒįĺē ƒŏŗ ţĥē ʋįēŵ "{0}" ĥąş ƃēēŋ ğēŋēŗąţēƌ. Ŷŏű ċąŋ ƌįşρĺąŷ įţ ċĺįċķįŋğ "Ʋįēŵ Ďēţąįĺş", ŏŗ ƌŏŵŋĺŏąƌ įţ įƒ ŷŏű ĥąʋē ţĥē ŗēĺēʋąŋţ ρēŗɱįşşįŏŋ.]]]
##XMSG: Error message during  generation of Explain Plan file
EXPLAINPLAN_ERROR=[[[Āŋ ēŗŗŏŗ ŏċċűŗŗēƌ ƌűŗįŋğ ğēŋēŗąţįŏŋ ŏƒ Ĕχρĺąįŋ Ƥĺąŋ ƒįĺē ƒŏŗ ţĥē ʋįēŵ "{0}".]]]
##XMSG: Explain Plan file cannot be generated for the view {0}, because too many views are stacked on each other
EXPLAINPLAN_MODEL_TOO_COMPLEX=[[[Ĕχρĺąįŋ Ƥĺąŋ ƒįĺē ċąŋŋŏţ ƃē ğēŋēŗąţēƌ ƒŏŗ ţĥē ʋįēŵ "{0}". Ţŏŏ ɱąŋŷ ʋįēŵş ąŗē şţąċķēƌ ŏŋ ēąċĥ ŏţĥēŗ. Ĉŏɱρĺēχ ɱŏƌēĺş ɱąŷ ċąűşē ŏűţ ŏƒ ɱēɱŏŗŷ ēŗŗŏŗş ąŋƌ şĺŏŵ ρēŗƒŏŗɱąŋċē. Ƥēŗşįşţįŋğ ą ʋįēŵ įş ŗēċŏɱɱēŋƌēƌ.]]]

##XMSG: Message to indicate that the space is locked
PERFORMANCE_ANALYSIS_LOCKED_SPACE_ERROR=[[[Ĉąŋŋŏţ ēχēċűţē ρēŗƒŏŗɱąŋċē ąŋąĺŷşįş ƒŏŗ ʋįēŵ "{0}", ƃēċąűşē şρąċē "{1}" įş ĺŏċķēƌ.]]]
##XMSG: The performance analysis task was cancelled
PERFORMANCE_ANALYSIS_CANCELED=[[[Ƥēŗƒŏŗɱąŋċē ąŋąĺŷşįş ƒŏŗ ţĥē ʋįēŵ "{0}" ŵąş ċąŋċēĺēƌ.]]]
##XMSG: The performance analysis failed
PERFORMANCE_ANALYSIS_FAILED=[[[Ƥēŗƒŏŗɱąŋċē ąŋąĺŷşįş ƒąįĺēƌ.∙∙∙∙∙∙∙∙]]]
##XMSG: The performance analysis is finished
PERFORMANCE_ANALYSIS_FINISHED=[[[Ƥēŗƒŏŗɱąŋċē ąŋąĺŷşįş ƒŏŗ ţĥē ʋįēŵ "{0}" ĥąş ƒįŋįşĥēƌ. Ďįşρĺąŷ ţĥē ŗēşűĺţ ƃŷ ċĺįċķįŋğ "Ʋįēŵ Ďēţąįĺş".]]]
##XMSG: The view has input parameter with no default value
INPUT_PARAMETER_WITHOUT_DEFAULT_VALUE=[[[Ţĥįş ʋįēŵ ċąŋŋŏţ ƃē ąŋąĺŷžēƌ ƃēċąűşē įţ ĥąş ą ρąŗąɱēţēŗ ŵįţĥ ŋŏ ƌēƒąűĺţ ʋąĺűē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
##XMSG: The view is not fully deployed
VIEW_NOT_FULLY_DEPLOYED=[[[Ţĥįş ʋįēŵ ċąŋŋŏţ ƃē ąŋąĺŷžēƌ ƃēċąűşē įţ įş ŋŏţ ƒűĺĺŷ ƌēρĺŏŷēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
##XMSG: The view uses limited remote adapters
LIMITED_REMOTE_ADAPTER=[[[Ţĥįş ʋįēŵ űşēş ąţ ĺēąşţ ŏŋē ŗēɱŏţē ąƌąρţēŗ ŵįţĥ ĺįɱįţēƌ ċąρąƃįĺįţįēş şűċĥ ąş ɱįşşįŋğ ƒįĺţēŗ ρűşĥƌŏŵŋ ŏŗ şűρρŏŗţ ƒŏŗ 'Ĉŏűŋţ'. Ƥēŗşįşţįŋğ ŏŗ ŗēρĺįċąţįŋğ ŏƃĵēċţş ċąŋ įɱρŗŏʋē ŗűŋţįɱē ρēŗƒŏŗɱąŋċē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
##XMSG: The view uses remote adapter that does not support 'Limit'
REMOTE_ADAPTER_NOT_SUPPORTING_LIMIT=[[[Ţĥįş ʋįēŵ űşēş ąţ ĺēąşţ ŏŋē ŗēɱŏţē ąƌąρţēŗ ţĥąţ ƌŏēş ŋŏţ şűρρŏŗţ 'Ļįɱįţ'. Μŏŗē ţĥąŋ 1000 ŗēċŏŗƌş ɱąŷ ĥąʋē ƃēēŋ şēĺēċţēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
##XMSG: Performance analysis is executed by using default parameters of view'
INPUT_PARAMETER_WITH_DEFAULT_VALUE=[[[Ţĥē ρēŗƒŏŗɱąŋċē ąŋąĺŷşįş įş ēχēċűţēƌ ƃŷ űşįŋğ ţĥē ƌēƒąűĺţ ʋąĺűēş ŏƒ ʋįēŵ ρąŗąɱēţēŗş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
##XMSG: Message to indicate that an error occurred
PERFORMANCE_ANALYSIS_ERROR=[[[Āŋ ēŗŗŏŗ ŏċċűŗŗēƌ ƌűŗįŋğ ρēŗƒŏŗɱąŋċē ąŋąĺŷşįş ƒŏŗ ţĥē ʋįēŵ "{0}".]]]
##XMSG: Message start of cancellation for Performance Analysis task
CANCEL_PERFORMANCE_ANALYSIS_TRIGGERED=[[[Ƥŗŏċēşş ţŏ şţŏρ Ƥēŗƒŏŗɱąŋċē Āŋąĺŷşįş ţąşķ {0} ƒŏŗ ʋįēŵ "{1}" ĥąş ƃēēŋ şűƃɱįţţēƌ. Ţĥēŗē ɱąŷ ƃē ą ƌēĺąŷ űŋţįĺ ţĥē ţąşķ įş şţŏρρēƌ.]]]
##XMSG: Message start to indicate that task is not active
CANCEL_PERFORMANCE_ANALYSIS_NO_ACTIVE_TASK=[[[Ƥēŗƒŏŗɱąŋċē Āŋąĺŷşįş ţąşķ {0} ƒŏŗ ţĥē ʋįēŵ "{1}" įş ŋŏţ ąċţįʋē.]]]
#XMSG: Cancel Performance Analysis task failure
CANCEL_PERFORMANCE_ANALYSIS_FAILED=[[[Ƒąįĺēƌ ţŏ ċąŋċēĺ Ƥēŗƒŏŗɱąŋċē Āŋąĺŷşįş ţąşķ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]

#XBUT: Assign schedule menu button label
assignScheduleLabel=[[[Āşşįğŋ Ŝċĥēƌűĺē ţŏ Μē∙∙∙∙∙]]]
#XBUT: Pause schedule menu label
pauseScheduleLabel=[[[Ƥąűşē Ŝċĥēƌűĺē∙∙∙∙∙]]]
#XBUT: Resume schedule menu label
resumeScheduleLabel=[[[Řēşűɱē Ŝċĥēƌűĺē∙∙∙∙]]]
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=[[[Āŋ ēŗŗŏŗ ŏċċűŗŗēƌ ŵĥįĺē ŗēɱŏʋįŋğ şċĥēƌűĺēş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=[[[Āŋ ēŗŗŏŗ ŏċċűŗŗēƌ ŵĥįĺē ąşşįğŋįŋğ şċĥēƌűĺēş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=[[[Āŋ ēŗŗŏŗ ŏċċűŗŗēƌ ŵĥįĺē ρąűşįŋğ şċĥēƌűĺēş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=[[[Āŋ ēŗŗŏŗ ŏċċűŗŗēƌ ŵĥįĺē ŗēşűɱįŋğ şċĥēƌűĺēş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=[[[Ďēĺēţįŋğ {0} şċĥēƌűĺēş]]]
#XMSG: Message for starting mass assign of schedules
massAssignStarted=[[[Ĉĥąŋğįŋğ ţĥē ŏŵŋēŗ ŏƒ {0} şċĥēƌűĺēş]]]
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=[[[Ƥąűşįŋğ {0} şċĥēƌűĺēş]]]
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=[[[Řēşűɱįŋğ {0} şċĥēƌűĺēş]]]
#XBUT: Select Columns Button
selectColumnsBtn=[[[Ŝēĺēċţ Ĉŏĺűɱŋş∙∙∙∙∙]]]
#XFLD: Refresh tooltip
TEXT_REFRESH=[[[Řēƒŗēşĥ∙∙∙∙∙∙∙]]]
#XFLD: Select Columns tooltip
text_selectColumns=[[[Ŝēĺēċţ Ĉŏĺűɱŋş∙∙∙∙∙]]]


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Runtime Metrics ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Label for Runtime Metrics
RuntimeMetricsTitle=[[[Řűŋţįɱē Μēţŗįċş ƒŏŗ∙∙∙∙∙]]]
#XFLD : Label for Run Button
runButton=[[[Řűŋ∙]]]
#XFLD : Label for Cancel Button
cancelButton=[[[Ĉąŋċēĺ∙∙∙∙∙∙∙∙]]]
#XFLD : Label for Close Button
closeButton=[[[Ĉĺŏşē∙∙∙∙∙∙∙∙∙]]]
#XFLD : Label for Open View Analyzer Button
openViewAnalyzerButton=[[[Ŏρēŋ Ʋįēŵ Āŋąĺŷžēŗ∙∙∙∙∙∙]]]
#XFLD : Label for Generate Explain Plan Button
generateExplainPlanButton=[[[Ģēŋēŗąţē Ĕχρĺąįŋ Ƥĺąŋ∙∙∙∙∙]]]
#XFLD : Label for Previous Run Column
previousRun=[[[Ƥŗēʋįŏűş Řűŋ∙∙∙∙∙∙∙]]]
#XFLD : Label for Latest Run Column
latestRun=[[[Ļąţēşţ Řűŋ∙∙∙∙]]]
#XFLD : Label for time Column
time=[[[Ţįɱē]]]
#XFLD : Label for Duration Column
duration=[[[Ďűŗąţįŏŋ∙∙∙∙∙∙]]]
#XFLD : Label for Peak Memory Column
peakMemory=[[[Ƥēąķ Μēɱŏŗŷ∙∙∙∙∙∙∙∙]]]
#XFLD : Label for Number of Rows
numberOfRows=[[[Ńűɱƃēŗ ŏƒ Řŏŵş∙∙∙∙∙]]]
#XFLD : Label for Overall Number of Sources
overallNumberOfSources=[[[Ŏʋēŗąĺĺ Ńűɱƃēŗ ŏƒ Ŝŏűŗċēş∙∙∙∙∙∙∙]]]
#XFLD : Label for Data Access Column
dataAccess=[[[Ďąţą Āċċēşş∙∙∙∙∙∙∙∙]]]
#XFLD : Label for Local Tables
localTables=[[[Ļŏċąĺ Ţąƃĺēş∙∙∙∙∙∙∙]]]
#XFLD : Label for federatedRemoteTables
federatedRemoteTables=[[[Ƒēƌēŗąţēƌ Řēɱŏţē Ţąƃĺēş (ŵįţĥ Ļįɱįţēƌ Āƌąρţēŗ Ĉąρąƃįĺįţįēş)∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XTXT Text for initial state of the runtime metrics
initialState=[[[Ŷŏű ɱűşţ ƒįŗşţ ŗűŋ Ƥēŗƒŏŗɱąŋċē Āŋąĺŷşįş ţŏ ğēţ ţĥē ɱēţŗįċş. Ţĥįş ɱąŷ ţąķē şŏɱē ţįɱē, ƃűţ ŷŏű ċąŋ ċąŋċēĺ ţĥē ρŗŏċēşş įƒ ŋēċēşşąŗŷ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Cancel confirmation message
CancelRunConfirmationMessage=[[[Āŗē ŷŏű şűŗē ţĥąţ ŷŏű ŵąŋţ ţŏ ċąŋċēĺ ţĥē ċűŗŗēŋţ ŗűŋ ŏƒ Ƥēŗƒŏŗɱąŋċē Āŋąĺŷşįş?∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XTIT: Cancel dialog title
CancelRunTitle=[[[Ĉąŋċēĺ Řűŋ∙∙∙∙]]]
#XFLD: Label for Number of Rows
NUMBER_ROWS=[[[Ńűɱƃēŗ ŏƒ Řŏŵş∙∙∙∙∙]]]
#XFLD: Label for Overall Number of Sources
OVERALL_NUMBER_SOURCES=[[[Ŏʋēŗąĺĺ Ńűɱƃēŗ ŏƒ Ŝŏűŗċēş∙∙∙∙∙∙∙]]]
#XFLD: Label for Data Access
DATA_ACCESS=[[[Ďąţą Āċċēşş∙∙∙∙∙∙∙∙]]]
#XFLD: Label for Remote tables
NUMBER_REMOTE_TABLES_LIMITED_ADAPTERS=[[[Ƒēƌēŗąţēƌ Řēɱŏţē Ţąƃĺēş (ŵįţĥ Ļįɱįţēƌ Āƌąρţēŗ Ĉąρąƃįĺįţįēş)∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Label for select statement
SELECT_STATEMENT=[[['ŜĔĻĔĈŢ * ƑŘŎΜ ƲĬĔŴ ĻĬΜĬŢ 1000'∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Label for duration
SELECT_RUNTIME=[[[Ďűŗąţįŏŋ∙∙∙∙∙∙]]]
#XFLD: Label for peak memory
SELECT_PEAK_MEMORY=[[[Ƥēąķ Μēɱŏŗŷ∙∙∙∙∙∙∙∙]]]
#XFLD: Label for count statement
SELECT_COUNT_STATEMENT=[[['ŜĔĻĔĈŢ ĈŎŮŃŢ(*) ƑŘŎΜ ƲĬĔŴ'∙∙∙∙∙∙∙∙]]]
#XFLD: Label for count duration
SELECT_COUNT_RUNTIME=[[[Ďűŗąţįŏŋ∙∙∙∙∙∙]]]
#XFLD: Label for count peak memory
SELECT_COUNT_PEAK_MEMORY=[[[Ƥēąķ Μēɱŏŗŷ∙∙∙∙∙∙∙∙]]]
#XFLD: Label for count of number of local tables
NUMBER_LOCAL_TABLES_FILE=[[[Ļŏċąĺ Ţąƃĺēş (Ƒįĺē)∙∙∙∙∙]]]
#XTXT: Text for running state of the runtime metrics
Running=[[[Řűŋŋįŋğ...∙∙∙∙]]]
#XFLD: Label for time
Time=[[[Ţįɱē]]]
#XFLD: Label for virtual access
PA_VIRTUAL=[[[Ʋįŗţűąĺ∙∙∙∙∙∙∙]]]
#XFLD: Label for persisted access
PA_PERSISTED=[[[Ƥēŗşįşţēƌ∙∙∙∙∙]]]
PA_PARTIALLY_PERSISTED=[[[Ƥąŗţįąĺĺŷ Ƥēŗşįşţēƌ∙∙∙∙∙]]]
#XTXT: Text for cancel
CancelRunSuccessMessage=[[[Ĉąŋċēĺĺįŋğ ţĥē ŗűŋ ŏƒ Ƥēŗƒŏŗɱąŋċē Āŋąĺŷşįş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XTXT: Text for cancel error
CancelRunErrorMessage=[[[Āŋ ēŗŗŏŗ ŏċċűŗŗēƌ ŵĥįĺē ċąŋċēĺįŋğ ţĥē ŗűŋ ŏƒ Ƥēŗƒŏŗɱąŋċē Āŋąĺŷşįş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XTXT: Text for explain plan generation
ExplainPlanStarted=[[[Ģēŋēŗąţįŋğ Ĕχρĺąįŋ Ƥĺąŋ ƒŏŗ ţĥē ʋįēŵ "{0}".]]]
#XTXT: Text for performance analysis
PerformanceAnalysisStarted=[[[Ŝţąŗţįŋğ Ƥēŗƒŏŗɱąŋċē Āŋąĺŷşįş ƒŏŗ ţĥē ʋįēŵ "{0}".]]]
#XTXT: Text for performance analysis error
ErrorFetchingPerformanceAnalysis=[[[Āŋ ēŗŗŏŗ ŏċċűŗŗēƌ ŵĥįĺē ƒēţċĥįŋğ Ƥēŗƒŏŗɱąŋċē Āŋąĺŷşįş ƌąţą.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XTXT: Text for performance analysis error
conflictingTask=[[[Ƥēŗƒŏŗɱąŋċē Āŋąĺŷşįş ţąşķ įş ąĺŗēąƌŷ ŗűŋŋįŋğ∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Label for Errors
Errors=[[[Ĕŗŗŏŗ(ş)∙∙∙∙∙∙]]]
#XFLD: Label for Warnings
Warnings=[[[Ŵąŗŋįŋğ(ş)∙∙∙∙]]]
#XFLD: Tooltip for missing privileges
openViewAnalyzerButtonTooltipNoPrivilege=[[[Ŷŏű ŋēēƌ ĎŴĈ_ĎĀŢĀĬŃŢĔĢŘĀŢĬŎŃ(űρƌąţē) ρŗįʋįĺēğē ţŏ ŏρēŋ ţĥē Ʋįēŵ Āŋąĺŷžēŗ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Tooltip for missing privileges
generateExplainPlanButtonTooltipNoPrivilege=[[[Ŷŏű ŋēēƌ ĎŴĈ_ŘŮŃŢĬΜĔ(ŗēąƌ) ρŗįʋįĺēğē ţŏ ğēŋēŗąţē ţĥē Ĕχρĺąįŋ Ƥĺąŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]



#XFLD: Label for frequency column
everyLabel=[[[Ĕʋēŗŷ∙∙∙∙∙∙∙∙∙]]]
#XFLD: Plural Recurrence text for Hour
hoursLabel=[[[Ĥŏűŗş∙∙∙∙∙∙∙∙∙]]]
#XFLD: Plural Recurrence text for Day
daysLabel=[[[Ďąŷş]]]
#XFLD: Plural Recurrence text for Month
monthsLabel=[[[Μŏŋţĥş∙∙∙∙∙∙∙∙]]]
#XFLD: Plural Recurrence text for Minutes
minutesLabel=[[[Μįŋűţēş∙∙∙∙∙∙∙]]]
