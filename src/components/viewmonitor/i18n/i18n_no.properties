
#XBCB: Breadcrumb
viewMonitorBreadcrumb={spaceId}

#XFLD: Label for persisted view column
SOURCE=Kilde
#XFLD: Label for persisted view column
NAME=Navn
#XFLD: Label for persisted view column
NAME_LABEL=Forretningsnavn
#XFLD: Label for persisted view column
NAME_LABELNew=Objekt (forretningsnavn)
#XFLD: Label for persisted view column
TECHINCAL_NAME=Teknisk navn
#XFLD: Label for persisted view column
TECHINCAL_NAMENew=Objekt (teknisk navn)
#XFLD: Label for persisted view column
DATA_PERSISTENCY=Datatilgang
#XFLD: Label for persisted view column
STATUS=Status
#XFLD: Label for persisted view column
LAST_UPDATED=Sist oppdatert
#XFLD: Label for persisted view column
IN_MEMORY_SIZE=Minne brukt for lagring (MiB)
#XFLD: Label for persisted view column
DISK_SIZE=Disk brukt for lagring (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_NEWW=Størrelse på in-memory (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_HEADER=Størrelse på in-memory
#XFLD: Label for persisted view column
DISK_SIZE_NEWW=Størrelse på disk (MiB)
#XFLD: Label for persisted view column
DISK_SIZE_HEADER=Størrelse på disk
#XFLD: Label for schedule owner column
txtScheduleOwner=Eier av tidsplan
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Viser hvem som opprettet tidsplanen
#XFLD: Label for persisted view column
PERSISTED=Persistert
#XFLD: Label for persisted view column
TYPE=Type
#XFLD: Label for View Selection Dialog column
changedOn=Endret
#XFLD: Label for View Selection Dialog column
createdBy=Opprettet av
#XFLD: Label for log details column
txtViewPersistencyLogs=Vis protokoller
#XFLD: Label for log details column
txtViewPersistencyLogsNew=Detaljer
#XFLD: text for values shown for Ascending sort order
SortInAsc=Stigende sortering
#XFLD: text for values shown for Descending sort order
SortInDesc=Synkende sortering
#XFLD: Label for persisted view monitor heading
ViewMonitor_Heading=Visningsmonitor
#XFLD: Label for persisted view monitor sub-heading
ViewMonitor_SubHeading=Overvåk og vedlikehold datapersistens for visninger


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ DynamicPageHeader ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: text for values shown in column Persistence Status
txtLoading=Laster
#XFLD: text for values shown in column Persistence Status
txtRunning=Kjører
#XFLD: text for values shown in column Persistence Status
txtAvailable=Tilgjengelig
#XFLD: text for values shown in column Persistence Status
txtError=Feil
#XMSG: Error message for unknown Persistence type that gets logged in Kibana
unknownReplicationTypeError=Replikeringstypen "{0}" støttes ikke.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Content ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Innstillinger brukt for siste datapersistenskjøring:
#XMSG: Message for input parameter name
inputParameterLabel=Inndataparameter
#XMSG: Message for input parameter value
inputParameterValueLabel=Verdi
#XMSG: Message for persisted data
inputParameterPersistedLabel=Persistert kl.
#XTIT: Count for View in ViewMonitor Toolbar
txtViewCount=Visninger ({0})
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Visningspersistens
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Datapersistens
#XBUT: Button to clear the selected view persistance
ClearViewPersistance=Fjern
#XBUT: Button to stop the selected view persistance
stopPersistance=Stopp persistens
#XFLD: Placeholder for Search field
txtSearch=Søk
#XBUT: Tooltip for refresh button
txtRefresh=Oppdater
#XBUT: Tooltip for add view button
txtDeleteView=Slett persistens
#XBUT: Tooltip for load new peristence
loadNewPersistence=Start persistens på nytt
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Last nytt øyeblikksbilde
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Start datapersistens
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Fjern persisterte data
#XMSG: success message for starting persistence
startPersistenceSuccess=Vi persisterer visningen ''{0}''.
#XMSG: success message for stopping persistence
stopPersistenceSuccess=Vi fjerner persisterte data for visningen ''{0}''.
#XMSG: success message for stopping persistence
deletePersistedViewSuccess=Vi fjerner visningen "{0}" fra overvåkningslisten.
#XMSG: error message during start persistence of a view (backend error)
ERROR_STARTING_PERSISTENCE=Det oppstod en feil ved start av datapersistens for visningen "{0}".
#XMSG: error message during start persistence of a view (backend error)
PARAMERROR_STARTING_PERSISTENCE=Visningen "{0}" kan ikke persisteres fordi den inneholder inndataparametere.
#XMSG: error message during start persistence of a view (backend error)
NUMBER_OF_PARAMETER_STARTING_PERSISTENCE=Visningen "{0}" kan ikke persisteres fordi den har mer enn én inndataparameter.
#XMSG: error message during start persistence of a view (backend error)
PARAMETER_DEFAULT_VALUE_STARTING_PERSISTENCE=Visningen "{0}" kan ikke persisteres fordi inndataparameteren ikke har standardverdi.
#XMSG: error message during start persistence of a view (backend error)
REDEPLOYMENT_DAC_REQUIRED=Du må distribuere datatilgangskontrollen (DTK) "{0}" for å støtte datapersistens.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_STARTING_PERSISTENCE=Visningen {0}" kan ikke persisteres fordi den bruker visningen "{1}", som inneholder datatilgangskontroll (DTK).
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_EXTERNAL_STARTING_PERSISTENCE=Visningen "{0}" kan ikke persisteres fordi den bruker en visning med datatilgangskontroll (DTK) som tilhører et annet rom.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_STRUCTURE_STARTING_PERSISTENCE=Visningen ''{0}'' kan ikke persisteres fordi strukturen i en eller flere datatilgangskontroller (DTK) ikke støtter datapersistens.
#XMSG: error message during stop persistence of a view (backend error)
STOP_PERSISTENCE_ERROR=Det oppstod en feil ved stopp av persistensen for visningen ''{0}''.
#XMSG: error message during stop persistence of a view (backend error)
DELETE_PERSISTED_VIEW_ERROR=Det oppstod en feil ved sletting av den persisterte visningen "{0}".
#XMSG: Warning message for removing the persisted data
RemovePersistency_Confirm_Msg=Vil du slette de persisterte dataene og bytte til virtuell tilgang for visningen ''{0}''?
#XMSG: Warning message for delete the view persistence
DeletePersistency_Confirm_Msg=Vil du fjerne visningen fra overvåkningslisten og slette de persisterte dataene for visningen "{0}"?
#XMSG: error message for reading data from backend
txtReadBackendError=Det ser ut som det oppstod en feil under lesing fra backend.
#XFLD: Label for No Data Error
NoDataError=Feil
#XMSG: message for conflicting task
Task_Already_Running=En oppgave i konflikt kjøres allerede for visningen "{0}".

#XFLD: Error while accessing pertitioning for a view
missingPrivilegeInViewPersistency=Utilstrekkelig autorisasjon for utføring av partisjonering for visningen ''{0}''

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Show All Views~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Text for show all views button
allViewsText=Alle visninger ({0})
#XBUT: Text for show scheduled views button
scheduledText=Planlagt ({0})
#XBUT: Text for show persisted views button
persistedText=Persistert ({0})
#XBUT: Text for start analyzer button
startAnalyzer=Start visningsanalyse
#XFLD: Message if repository is unavailable
repositoryErrorMsg=Repository er ikke tilgjengelig, og visse funksjoner er deaktivert.

#XFLD: Data Access - Virtual
Virtual=Virtuell
#XFLD: Data Access - Persisted
Persisted=Persistert

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Non-Persisted View Selection Dialog~~~~~~~~~~~~~~~~~~~~~~~

#XTIT: Title for Non-Persisted View selection dialog
select_source=Velg visning for persistens

#XTIT: Placeholder for Non-Persisted View selection dialog search
searchView=Søk visninger
#XTIT: No data in the list of non-persisted view
No_Data=Ingen data
#XBUT: Button to select non-persisted view
ok=OK
#XBUT: Button to close the non-persisted views selection dialog
cancel=Avbryt

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~View persistency log message key~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message for view persistency logs
START_STOP_PERSISTENCY=Starter utføring av datapersistensoppgave for ''{1}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY=Persisterer data for visningen ''{1}''.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY)
START_VIEW_PERSISTENCY_PROCESS=Starter prosessen for å persistere data for visningen ''{0}''.
#XFLD: Message for view persistency manual partition load
START_VIEW_PERSISTENCY_PROCESS_MANUAL_PARTITIONS=Starter prosessen for å persistere data for visningen ''{0}'' med valgte partisjons-ID-er: "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY=Vi fjerner persisterte data for visningen ''{1}''.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY)
REMOVE_VIEW_PERSISTENCY=Starter prosessen for å fjerne persisterte data for visningen "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_SUCCESS=Data er persistert for visningen ''{1}''.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_SUCCESS)
START_VIEW_PERSISTENCY_PROCESS_SUCCESS=Data er persistert for visningen ''{0}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY_SUCCESS=Persisterte data er fjernet, og tilgang til virtuelle data er gjenopprettet for visningen ''{1}''.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_SUCCESS)
REMOVE_VIEW_PERSISTENCY_SUCCESS=Starter prosessen for å fjerne persisterte data for visningen "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_FAILED=Kan ikke persistere data for visningen ''{1}''.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_FAILED)
START_VIEW_PERSISTENCY_PROCESS_FAILED=Kan ikke persistere data for visningen ''{0}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.05)
STOP_VIEW_PERSISTENCY_FAILED=Kan ikke fjerne persisterte data for visningen ''{1}''.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_FAILED)
REMOVE_VIEW_PERSISTENCY_FAILED=Kan ikke fjerne persisterte data for visningen ''{0}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_SUCCESS="{3}" poster er persistert for visningen ''{1}''.
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace START_PERSISTENCY_RECORD_COUNT_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS={0} poster er satt inn i datapersistenstabellen for visningen "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS_V2={0} poster er satt inn i datapersistenstabellen for visningen "{1}". Minnebruk: {2} GiB.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_SUCCESS="{3}" persisterte poster er fjernet for visningen ''{1}''.
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace STOP_PERSISTENCY_RECORD_COUNT_SUCCESS)
REMOVE_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=Persisterte data er fjernet, "{0}" persisterte poster er slettet.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_FAILED=Henting av recordCount mislyktes for visningen ''{1}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_FAILED=Henting av recordCount mislyktes for visningen ''{1}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Tidsplan er slettet for ''{1}''.
#XFLD: Message for view persistency logs (new message to replace STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS)
REMOVE_VIEW_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Tidsplan er slettet for visningen "{0}".
#XFLD: Message for view persistency logs
STOP_PERSISTENCY_DELETE_SCHEDULE_FAILED=Sletting av tidsplan mislyktes for ''{1}''.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_REDEPLOY=Vi kan ikke persistere visningen ''{0}'' fordi den er endret og distribuert etter at du begynte å persistere den. Prøv å persistere visningen på nytt eller vent til den neste planlagte kjøringen.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_DELETE=Vi kan ikke persistere visningen ''{0}'' fordi den er slettet etter at du begynte å persistere den.
#XFLD: Message for successful data persistency of a single partition (for messages created before version 2022.03)
START_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} poster er persistert til partisjon for verdiene ''{1}'' <= {2} < ''{3}''.
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (new message to replace START_PERSISTENCY_SINGLE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} poster er satt inn i partisjon for verdiene ''{1}'' <= {2} < ''{3}''.
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS_V2={0} poster er satt inn i partisjon for verdiene "{1}" <= ''{2}'' < ''{3}''. Minnebruk: {4} GiB.
#XFLD: Message for successful replication of a "others" partition (for messages created before version 2022.03)
START_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} poster er persistert til partisjonen "andre".
#XFLD: Message used by SEAL repository for successful replication of a "others" partition (new message to replace START_PERSISTENCY_OTHERS_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} poster er satt inn i partisjonen "andre".
#XFLD: Message for view persistency record count of partitions (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={3} poster er persistert for visningen "{1}" i {4} partisjoner.
#XFLD: Message used by SEAL repository for view persistency record count of partitions (new message to replace START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={0} poster er satt inn i datapersistenstabellen for visningen "{1}" i {2} partisjoner.
#XFLD: Message used by SEAL repository for view persistency for partition wise refresh
START_VIEW_PERSISTENCY_RECORD_COUNT_REFRESHED_PARTITIONS_SUCCESS={0} poster er satt inn i datapersistenstabellen for visningen "{1}". Oppdaterte partisjoner: {2}, sperrede partisjoner: {3}, partisjoner totalt: {4}
#XFLD: Message used by SEAL repository for view persistency for manual partition load
START_VIEW_PERSISTENCY_RECORD_COUNT_MANUAL_PARTITIONS_SUCCESS={0} poster er satt inn i datapersistenstabellen for visningen "{1}" i {2} valgte partisjoner.
#XFLD: Message used by SEAL repository for view persistency for locked partition data load
START_VIEW_PERSISTENCY_RECORD_COUNT_LOCKED_PARTITIONS_SUCCESS={0} poster er satt inn i datapersistenstabellen for visningen "{1}". Oppdaterte partisjoner: {2}, sperrede uendrede partisjoner: {3}, partisjoner totalt: {4}
#XMSG: Message for unexpected error (for messages created before version 2022.03)
START_PERSISTENCY_UNEXPECTED_ERROR=Det oppstod en uventet feil ved persistering av data for visningen "{0}".
#XMSG: Message for unexpected error (new message to replace START_PERSISTENCY_UNEXPECTED_ERROR)
START_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Det oppstod en uventet feil ved persistering av data for visningen "{0}".
#XMSG: Message for Locked space error
START_VIEW_PERSISTENCY_LOCKED_SPACE_ERROR=Visningen ''{0}''’ kan ikke persisteres fordi rommet ''{1}'' er sperret.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
STOP_PERSISTENCY_UNEXPECTED_ERROR=Det oppstod en uventet feil ved fjerning av persisterte data for visningen "{0}".
#XMSG: Message for unexpected error (new message to replace STOP_PERSISTENCY_UNEXPECTED_ERROR)
REMOVE_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Det oppstod en uventet feil ved fjerning av persistens for visningen "{0}".
#XMSG: Message for ivalidated view
VIEW_PERSISTENCY_ABORTED_DEFINITION_INVALID=Definisjonen av visningen "{0}" har blitt ugyldig, mest sannsynlig på grunn av en endring av et objekt som er forbrukt direkte eller indirekte av visningen. Prøv å distribuere visningen på nytt for å løse problemet eller finne hovedårsaken.
#XMSG: Message for persisted data removal (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT=Persisterte data fjernes ved distribusjon av visningen ''{0}''.
#XMSG: Message for persisted data removal of a consuming view
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW=Persisterte data fjernes ved distribusjon av den forbrukte visningen ''{0}''.
#XMSG: Message for persisted data removal of a consuming view due to a dac change
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW_DAC_CHANGED=Persisterte data fjernes ved distribusjon av den forbrukte visningen ''{0}'' fordi datatilgangskontrollen er endret.
#XMSG: Message for persisted data removal (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DEPLOYMENT=Persisterte data fjernes ved distribusjon av visningen ''{0}''.
#XMSG: Message for deleted view (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION=Persistens fjernes ved sletting av visningen ''{0}''.
#XMSG: Message for deleted view (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DELETION=Persistens fjernes ved sletting av visningen ''{0}''.
#XMSG Message for removal of persisted data, because prerequisites not fulfilled
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_PREREQUISITES_NOT_FULFILLED=Persisterte data fjernes fordi forutsetningene for datapersistens ikke lenger er oppfylt.
#XMSG: Message for inconsistent persistency
VIEW_PERSISTENCY_INCONSISTENT_PERSISTENCY=Persistens for visningen "{0}" har blitt inkonsistent. Fjern persisterte data for å rette opp feilen.
#XMSG: Message for prerequisites
START_PERSISTENCY_CHECK_PREREQUISITES=Kontrollerer forutsetningene for persistens av visningen ''{0}''.
#XMSG: Message for DAC deployed in deprecated way using table function and join (for messages created before 2025.05)
START_PERSISTENCY_DEPRECIATED_DAC=Visningen "{0}" distribueres med datatilgangskontroll som avvikles. Distribuer visningen på nytt for å forbedre ytelsen.
#XMSG: Message for DAC deployed in deprecated way using table function and join
START_PERSISTENCY_DEPRECATED_DAC=Visningen "{0}" distribueres med datatilgangskontroll som avvikles. Distribuer visningen på nytt for å forbedre ytelsen.
#XMSG: Message for roll back (for messages created before version 2022.03)
ROLLBACK_VIEW_PERSISTENCY=Det oppsto en feil. Tidligere status for persistens er gjenopprettet for visningen ''{0}''.
#XMSG: Message used by SEAL repository for rollback of running procedure to persist view (new message to replace ROLLBACK_VIEW_PERSISTENCY)
VIEW_PERSISTENCY_ROLLBACK=Det oppsto en feil. Prosessen for å persistere visningen "{0}'' er stoppet og endringer er tilbakestilt.
#XMSG: Message used by SEAL repository for rollback of running procedure to remove persisted data of view
REMOVE_VIEW_PERSISTENCY_ROLLBACK=Det oppsto en feil. Prosessen for å fjerne persisterte data for visningen "{0}" er stoppet, og endringer er tilbakestilt.
#XMSG: Message used by SEAL repository for persistency preparation
START_PERSISTENCY_PREPARATION=Forbereder persistering av data.
#XMSG: Message used by SEAL repository for data insertion
START_INSERTING_PERSISTENCY_DATA=Setter inn data i persistenstabell.
#XMSG: Message for null values - partitioning (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS={0} nullverdiposter er satt inn i partisjonen "andre".
#XMSG: Message used by SEAL repository for others partition values - partitioning (new message to replace START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS={0} poster er satt inn i partisjonen "andre" for verdiene ''{2}'' < ''{1}'' ELLER ''{2}'' >= ''{3}''.
#XMSG: Message used by SEAL repository for others partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS_V2={0} poster er satt inn i partisjonen "andre" for verdiene ''{2}'' < ''{1}'' ELLER ''{2}'' >= ''{3}''. Minnebruk: {4} GiB.
#XMSG: Message used by SEAL repository for null partition values - partitioning
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS={0} poster er satt inn i partisjonen "andre" for verdiene ''{1}'' ER NULL.
#XMSG: Message used by SEAL repository for null partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS_V2={0} poster er satt inn i partisjonen "andre" for verdiene ''{1}'' ER NULL. Minnebruk: {2} GiB.
##XFLD:Message used by SEAL repository for remote statistics
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS=Laster involverte data: {0} fjernspørringer. Totalt antall poster hentet: {1}. Samlet varighet: {2} sekunder.
##XFLD:Message used by SEAL repository for remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS_WITH_PARTITIONS=Laster involverte data ved å bruke {0} partisjoner med {1} fjernspørringer. Totalt antall poster hentet: {2}. Samlet varighet: {3} sekunder.
##XFLD:Message used by SEAL repository with UI ID for instructions of remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_INSTRUCTION=Du kan se hvilke eksterne setninger som ble behandlet under kjøringen, ved å åpne fjernspørringsmonitoren og vise detaljene for partisjonsspesifikke meldinger.
##XFLD:Message for start reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS=Starter prosessen for gjenbruk av eksisterende persisterte data for visning {0} etter distribusjon.
##XFLD:Message used by SEAL repository for reattach view persitency
START_VIEW_PERSISTENCY_REATTACHMENT=Start gjenbruk av eksisterende persisterte data.
##XFLD:Message used by SEAL repository for reattach view persitency
START_FINILIZING_VIEW_PERSISTENCY_REATTACHMENT=Bruker eksisterende persisterte data på nytt.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_COMPLETE=Prosessen for gjenbruk av eksisterende persisterte data er fullført for visningen {0}.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_FAILED=Kan ikke bruke eksisterende persisterte data på nytt for visningen {0} etter distribusjon.
#XMSG: Message used by SEAL repository for finalizing persistency
START_FINALIZING_PERSISTENCY=Avslutter persistens.
#XMSG: Message used by SEAL repository for restoring virtual access for view
REMOVE_VIEW_PERSISTENCY_VIRTUAL_ACCESS_RESTORED=Virtuell datatilgang er gjenopprettet for visningen "{0}".
#XMSG: Message used by SEAL repository for view that already has virtual access
REMOVE_VIEW_PERSISTENCY_ALREADY_VIRTUAL_ACCESS=Visningen "{0}" har allerede virtuell datatilgang. Ingen persisterte data fjernes.
#XMSG: Message for removing view persistency
VIEW_PERSISTENCY_REMOVED_FROM_MONITOR=Visningen ''{0}'' er fjernet fra visningsmonitoren.
#XMSG: Message for view doesnt exist.
VIEW_PERSISTENCY_VIEW_NOT_EXISTING=Visningen "{0}" finnes enten ikke i databasen eller er ikke distribuert korrekt og kan derfor ikke persistere. Prøv å distribuere visningen på nytt for å løse problemet eller identifisere rotårsaken.
#XMSG: Message for persistency not enabled.
VIEW_PERSISTENCY_NOT_ENABLED=Datapersistens er ikke aktivert. Distribuer en tabell/visning på nytt i rommet ''{0}'' for å aktivere funksjonaliteten.
#XMSG: Message for unexpected error due to node crash
VIEW_PERSISTENCY_UNEXPECTED_ERROR_GENERAL=Siste kjøring av visningspersistens ble avbrutt på grunn av tekniske feil.
#XFLD: Message for view persistency memory consumption
VIEW_PERSISTENCY_MEMORY_CONSUMPTION=Høyeste minnebruk på {0} GiB er brukt i kjøringstiden for visningspersistens.
#XFLD: Message for view persistency timeout
VIEW_PERSISTENCY_TIMEOUT=Persistens for visningen {0} nådde grensen for tidsavbrudd på {1} timer.
#XFLD: Message for not started asynchronous view persistency
VIEW_PERSISTENCY_ASYNC_NOT_STARTED=Høy systembelastning hindret start av asynkron utføring for visningspersistens. Kontroller om det finnes for mange oppgaver som kjøres parallelt.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency (for messages created before version 2022.05)
START_VIEW_PERSISTENCY_EXECUTION_METHOD_DROP_AND_RENAME=Eksisterende persistert tabell er slettet og erstattet med en ny persistert tabell.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_RECREATE_AND_DROP=Eksisterende persistert tabell er slettet og erstattet med en ny persistert tabell.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_TRUNCATE_AND_COPY=Den eksisterende persisterte tabellen er oppdatert med nye data.
##XFLD: Message for missing authorizations for view persistency
VIEW_PERSISTENCY_MISSING_AUTH=Autorisasjoner mangler for datapersistens.
##XFLD: Message start of persistency cancellation
VIEW_PERSISTENCY_TRIGGER_CANCEL=Starter avbruddsprosess for å persistere visningen {0}.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_UNAVAILABLE_TASK_RUN=Kan ikke avbryte prosessen for å persistere visningen fordi det ikke finnes en løpende datapersistensoppgave for visningen {0}.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_NONDETERMINISTIC_TASK_RUN=Kan ikke avbryte prosessen for å persistere visningen fordi det ikke finnes en datapersistensoppgave som kjøres for visningen {0}.
##XFLD: Message for canceling a non running persitency task
VIEW_PERSISTENCY_CANCEL_NON_RUNNING_SELECTED_TASK=Kan ikke avbryte prosessen for å persistere visningen {0} fordi den valgte datapersistensoppgaven {1} ikke kjører.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_UNREACHABLE_PERSISTENCY_RUN=Kan ikke avbryte prosessen for å persistere visningen fordi datapersistens for visningen {0} ikke er startet ennå.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_COMPLETED_PERSISTENCY_RUN=Kan ikke avbryte prosessen for å persistere visningen {0} fordi den allerede er fullført.
##XFLD: Message for failed cancellation
VIEW_PERSISTENCY_ABORTED_CANCEL_EXECUTION=Kan ikke avbryte prosessen for å persistere visningen {0}.
##XFLD: Message for finalizing persistency cancellation
VIEW_PERSISTENCY_FINALIZE_CANCEL=Prosessen for å stoppe datapersistensen for visningen {0} er sendt.
##XFLD: Message used in persist task when cancellation is caught
VIEW_PERSISTENCY_CANCELED=Prosessen for å persistere visningen {0} er stoppet.
##XFLD: Message used in persist task when cancelation is caught and performed through a cancelation task
VIEW_PERSISTENCY_CANCELED_VIA_CANCEL_TASK=Prosessen for å persistere visningen {0} ble stoppet via en annulleringsoppgave {1}.
##XFLD: Message used in cancel task when the task is triggered by deployment
VIEW_PERSISTENCY_CANCEL_TRIGGERED_BY_DEPLOYMENT=Avbryter prosessen for å persistere data mens visningen {0} distribueres.
##XFLD: Message used in case the cancelation is retriggered for a running task
VIEW_PERSISTENCY_CANCEL_ATTEMPT_RETRIGGER_CANCEL=En tidligere oppgave for å annullere persistering av visningen {0} er allerede sendt.
##XFLD: Message used to express the delay that may occur before the persistency task is stopped
VIEW_PERSISTENCY_CANCEL_DELAY_STOPPING_PERSISTENCY=Det kan være en forsinkelse før datapersistensoppgaven for visningen {0} stoppes.
##XFLD: Message used to reflect the task log identifier of the current running persistency task
VIEW_PERSISTENCY_CANCEL_CURRENT_RUNNING_TASK=Data for visningen {0} persisteres med oppgaven {1}.
##XFLD:Message used by SEAL repository for locked partitions and DACs
VIEW_PERSISTENCY_LOCKED_PARTITIONS_DACS=Autorisasjoner som gis gjennom datatilgangskontroll (DTK), kan ha blitt endret og blir ikke tatt hensyn til av sperrede partisjoner. Opphev sperring av partisjonene og last inn et nytt øyeblikksbilde for å aktivere endringene.
##XFLD:Message used by SEAL repository for column structure change
VIEW_PERSISTENCY_COLUMN_STRUCTURE_CHANGE=Kolonnestrukturen er endret og stemmer ikke lenger overens med den eksisterende persistenstabellen. Fjern persisterte data og start en ny datapersistens, slik at persistenstabellen oppdateres.
##XFLD: Message for out of memory
VIEW_PERSISTENCY_HANA_OUT_OF_MEMORY=Oppgaven mislyktes fordi det er for lite minne i SAP HANA-databasen.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_EXCEPTION_TRANSACTION_ROLLBACK=Oppgaven mislyktes på grunn av et internt unntak i SAP HANA-databasen.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_SQL_EXECUTION_ISSUE=Oppgaven mislyktes på grunn av et internt problem med SQL-utføringen i SAP HANA-databasen.
##XFLD: Message for HANA out of memory result
VIEW_PERSISTENCY_OUT_OF_MEMORY_RESULT_MESSAGE=Hendelse med for lite minne for HANA, årsak: {0}
##XFLD: Message for admission control rejection
VIEW_PERSISTENCY_HANA_ADMISSION_CTRL_REJECTION=Oppgaven mislyktes på grunn av avvisning av SAP HANA-tilgangskontroll.
##XFLD: Message for HANA back pressure / rate limit of HANA client
VIEW_PERSISTENCY_HANA_CLIENT_RATE_LIMIT_REJECTION=Oppgaven mislyktes på grunn av for mange aktive SAP HANA-forbindelser.
#XMSG: Message for invalid persisted table name
VIEW_PERSISTENCY_PERSISTED_TABLE_INVALID=Det oppsto en feil og den persisterte tabellen har blitt ugyldig. For å løse problemet må du fjerne de persisterte dataene og persistere visningen på nytt.
#XMSG: Message for usahe of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION=Denne visningen kan ikke gjøres persistent. Den bruker en fjerntabell basert på en fjernkilde med aktivert brukeroverføring. Kontroller avstamningen til visningen.
#XMSG: Message for dynamic usage of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION_DYNAMIC=Denne visningen kan ikke gjøres persistent. Den bruker en fjerntabell basert på en fjernkilde med aktivert brukeroverføring. Fjerntabellen kan forbrukes dynamisk via en SQL-skriptvisning. Det kan hende at avstamningen til visningen ikke vises i fjerntabellen.
#XMSG: Message for insufficient privileges or consumption of view with DAC via dynamic SQL
VIEW_PERSISTENCY_INSUF_PRIV_OR_DAC_DYN_SQL=Det kan hende at autorisasjonene er utilstrekkelige. Åpne dataforhåndsvisningen for å se om du har de nøvendige autorisasjonene. Hvis ja, kan datatilgangskontroll (DAC) brukes for en andre visning som forbrukes via dynamisk SQL-skript.
#XMSG: deployment of DAC is deprecated and view need to be redeployed to be able to persist data
VIEW_PERSISTENCY_DEPRECATED_DAC=Visningen "{0}" distribueres med datatilgangskontroll som avvikles. Distribuer visningen på nytt for å persistere data for visningen.
##XFLD:Message used by SEAL repository for input parameter
START_VIEW_PERSISTENCY_INPUT_PARAMETER=Bruk av standardverdi "{0}" for inndataparameter "{1}".
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Elastic compute node keys~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
##XFLD:Message used to reflect disablement of ecn replica
ECN_DISABLE_EXISTING_REPLICA=Replika for elastisk beregningsnode er deaktivert.
##XFLD:Message used to reflect recreation of ecn replica
ECN_RECREATE_EXISTING_REPLICA=Replika for elastisk beregningsnode er opprettet på nytt.
##XFLD:Message used to reflect re-enablement of ecn replica
ECN_ENABLE_EXISTING_REPLICA=Replika for elastisk beregningsnode er aktivert på nytt.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Task Schedule Menu key~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message to schedule task
scheduleText=Tidsplan
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Opprett tidsplan
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Rediger tidsplan
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Slett tidsplan
#XFLD: Refresh frequency field
refreshFrequency=Oppdateringsfrekvens
#XFLD: Refresh frequency field
refreshFrequencyNew=Frekvens
#XFLD: Refresh frequency field
refreshFrequencyNewNew=Planlagt frekvens
#XBUT: label for None
none=Ingen
#XBUT: label for Real-Time replication state
realtime=Sanntid
#XFLD: Label for table column
txtNextSchedule=Neste kjøring
#XFLD: Label for table column
txtNextScheduleNew=Neste planlagte kjøring
#XFLD: Label for table column
txtNumOfRecords=Antall poster
#XFLD: Label for scheduled link
scheduledTxt=Planlagt
#XFLD: LABEL for partially persisted link
partiallyPersisted=Delvis persistert
#XFLD: Text for paused text
paused=Satt på pause

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ View Monitor Tasklog Details Action keys~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message for release lock actionn
releaseLockMsg=Hvis en kjøring tar lengre tid enn normalt, kan det bety at den har mislyktes og at statusen ikke er oppdatert. \r\n For å løse problemet kan du oppheve sperren og sette statusen til "mislyktes".
#XFLD: Label for release lock dialog
releaseLockText=Opphev sperre

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Column header tooltip ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: tooltip for table column
txtViewTooltip=Navn på persistert visning
#XFLD: tooltip for table column
txtViewDataAccessTooltip=Dette viser om visningen er tilgjengelig
#XFLD: tooltip for table column
txtViewRefreshFreqTooltip=Dette viser om en tidsplan er definert for visningen
#XFLD: tooltip for table column
txtViewStatusTooltip=Hent status for persistert visning
#XFLD: tooltip for table column
txtViewLatestUpdateTooltip=Gir informasjon om når den persisterte visningen ble oppdatert sist
#XFLD: tooltip for table column
txtViewNextRunTooltip=Hvis det er satt en tidsplan for visningen, kan du se tidspunktet for neste planlagte kjøring.
#XFLD: tooltip for table column
txtViewNumOfRecordsTooltip=Spor antall poster.
#XFLD: tooltip for table column
txtViewInMemorySizeTooltip=Spor hvor mye plass visningen bruker i minnet ditt
#XFLD: tooltip for table column
txtViewDiskSizeTooltip=Spor hvor mye plass visningen bruker på disken din
#XMSG: Expired text
txtExpired=Utløpt

#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Objektet "{0}" kan ikke legges til i oppgavekjeden.

#~~~~~~~~~~~View Analyzer task log messages~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message indicating number of records for a view, and memory consumption in Mebibyte during simulation of view persistency
RECORDS_PEAK_MEMORY=Visningen "{0}" har {1} poster. En simulering av visningspersistens brukte {2} MiB minne.
#XMSG: View Analyzer execution failure
VIEW_ANALYZER_EXECUTION_FAILED=Utføring av visningsanalyse mislyktes.
#XMSG: Missing authorizations for View Analyzer
VIEW_ANALYZER_MISSING_AUTH=Autorisasjoner mangler for visningsanalyse.
#XMSG: Maximum memory in Gibibyte is reached, therefore persistency simulation is stopped
MAXIMUM_MEMORY_REACHED=Maksimumsminnet på {0} GiB er nådd under simulering av datapersistens for visning "{1}". Derfor vil ingen ytterligere datapersistenssimulering bli kjørt.
#XMSG: Error during persistency simulation occurred
PERSISTENCY_SIMULATION_ERROR=Det oppstod en feil ved simulering av persistens for visningen "{0}".
#XMSG: Persistency simulation is not executed, because prerequisites are not fulfilled and the view cannot be persisted
PREREQUISITES_NOT_FULFILLED=Simulering av datapersistens er ikke utført for visningen "{0}" fordi forutsetningene ikke er oppfylt og visningen kan ikke persisteres.
#XMSG: Persistency simulation is not executed, because the view does not exist in the database
VIEW_DOES_NOT_EXIST=Du må distribuere visningen "{0}" for å aktivere to datapersistenssimuleringen.
#XMSG: Number of records cannot be determined, because the local table does not exist in the database
TABLE_DOES_NOT_EXIST=Lokal tabell "{0}" finnes ikke i databasen, så antall poster kan ikke fastsettes for denne tabellen.
##XMSG: Message start of cancellation for View Analyzer task
CANCEL_VIEW_ANALYZER_TRIGGERED=Prosessen for å stoppe visningsanalyseoppgave {0} for visningen "{1}" er sendt. Det kan oppstå en forsinkelse til oppgaven er stoppet.
##XMSG: Message start to indicate that task is not active
CANCEL_VIEW_ANALYZER_NO_ACTIVE_TASK=Visningsanalyseoppgave {0} for visningen "{1}" er ikke aktiv.
#XMSG: Cancel View Analyzer task failure
CANCEL_VIEW_ANALYZER_FAILED=Kan ikke annullere visningsanalyseoppgaven.
##XMSG: Message used in View Analyzer execution task when task is canceled
VIEW_ANALYZER_CANCELED=Utføring av visningsanalyse for visningen "{0}" ble stoppet via en annulleringsoppgave.
##XMSG: Message start of cancelation for Model Validation task
CANCEL_MODEL_VALIDATION_TRIGGERED=Prosessen for å stoppe modellvalideringsoppgave {0} for visningen "{1}" er sendt. Det kan oppstå en forsinkelse til oppgaven stoppes.
##XMSG: Message to indicate that task is not active
CANCEL_MODEL_VALIDATION_NO_ACTIVE_TASK=Modellvalideringsoppgave {0} for visningen "{1}" er ikke aktiv.
#XMSG: Cancel Model Validation task failure
CANCEL_MODEL_VALIDATION_FAILED=Kan ikke annullere modellvalideringsoppgaven.
##XMSG: Message used in Model Validation task when task is canceled
MODEL_VALIDATION_CANCELED=Utføring av modellvalidering for visningen "{0}" ble stoppet via en annulleringsoppgave.
#XMSG: Message for Locked space error, before starting Model Validation task
MODEL_VALIDATION_LOCKED_SPACE_ERROR=Kan ikke utføre modellvalidering for visningen "{0}" fordi rommet "{1}" er sperret.
##XMSG: Message used in View Analyzer execution task when determination of number of rows fails
NUMBER_ROWS_DETERMINATION_FAILED=Det oppstod en feil ved fastsetting av antallet rader for den lokale tabellen "{0}".
##XMSG: Message to indicate that SQL plan was created
EXECUTION_PLAN_CREATED=Planfil for SQL-analyse for visningen ''{0}'' er opprettet og kan lastes ned.
#XFLD: Message to indicate starting of process to generate SQL Analyzer plan file
START_PLANVIZ_EXECUTION=Starter prosessen med å generere planfil for SQL-analyse for visningen ''{0}''.
#XFLD: Message to indicate starting of View Analyzer process
VIEW_ANALYZER_EXECUTION_STARTED=Starter utføring av visningsanalyse.
##XMSG: Message to indicate that SQL plan file cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_PLANVIZ_NOT_FULFILLED=Planfil for SQL-analyse kan ikke genereres for visningen ''{0}'' siden forutsetningene for datapersistens ikke er oppfylt.
##XMSG: Error message during  generation of SQL Analyzer plan file
PLANVIZ_ERROR=Det oppstod en feil ved generering av planfil for SQL-analyse for visningen ''{0}''.
#XMSG: Message for Locked space error
VIEW_ANALYZER_LOCKED_SPACE_ERROR=Kan ikke utføre visningsanalyse for visningen ''{0}'' fordi rommet ''{1}'' er sperret.
#XMSG: Message for persistency simulation without partitioning
PERSISTENCY_SIMULATION_WITHOUT_PARTITIONS=Partisjonene for visningen ''{0}'' inkluderes ikke ved simulering av datapersistens.
#XMSG: Message for generation of SQL Analyzer plan file without considering partitioning
PLANVIZ_WITHOUT_PARTITIONS=Partisjonene for visningen ''{0}'' inkluderes ikke ved generering av planfil for SQL-analyse.
#XMSG: Confirmation message for mass removal of persisted data
MassRemovePersistency_Confirm_Msg=Vil du fjerne de persisterte dataene og sette datatilgangen tilbake til virtuell tilgang?
#XMSG: Confirmation message for partial mass removal of selected persisted data
PartialMassRemovePersistency_Confirm_Msg={0} av de {1} valgte visningene har persisterte data. \n Vil du fjerne de persisterte dataene og sette datatilgangen tilbake til virtuell tilgang?
#XMSG: success message for stopping mass persistence
stopMassPersistenceSuccess=Vi fjerner persisterte data for de valgte visningene.
#XMSG: error message during mass stop persistence of a view (backend error)
STOP_MASS_PERSISTENCE_ERROR=Det oppstod en feil ved stopp av persistensen for de valgte visningene.
#XMSG: memory analysis is only performed in space {0}
NO_MEMORY_ANALYSIS=Minneanalyse utføres bare for entiteter i rom "{0}": "{1}" "{2}" ble ignorert.
##XMSG: Message to indicate that Explain Plan cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_EXPLAINPLAN_NOT_FULFILLED=Explain-planfil kan ikke genereres for visningen "{0}" fordi persistensforutsetningene ikke er oppfylt.
#XMSG: Message for Explain Plan generation without partitioning
EXPLAINPLAN_WITHOUT_PARTITIONS=Partisjoner for visningen "{0}" inkluderes ikke under generering av Explain-planfil.
#XFLD: Message to indicate starting of process to generate Explain Plan file
START_EXPLAINPLAN_EXECUTION=Starter prosessen med å generere Explain-planfil for visningen "{0}".
##XMSG: Message to indicate that Explain Plan was created
EXPLAINPLAN_CREATED=Explain-planfil er generert for visningen "{0}". Du kan vise den ved å klikke på "Vis detaljer", eller du kan laste den ned hvis du har autorisasjonen som kreves.
##XMSG: Error message during  generation of Explain Plan file
EXPLAINPLAN_ERROR=Det oppstod en feil ved generering av Explain-planfil for visningen "{0}".
##XMSG: Explain Plan file cannot be generated for the view {0}, because too many views are stacked on each other
EXPLAINPLAN_MODEL_TOO_COMPLEX=Kan ikke generere filen Explain-plan for visningen "{0}". Det er stablet for mange visninger oppå hverandre. Komplekse modeller kan føre til for lite minne og lav ytelse. Vi anbefaler at du persisterer en visning.

##XMSG: Message to indicate that the space is locked
PERFORMANCE_ANALYSIS_LOCKED_SPACE_ERROR=Kan ikke utføre ytelsesanalyse for visningen "{0}" fordi rommet "{1}" er sperret.
##XMSG: The performance analysis task was cancelled
PERFORMANCE_ANALYSIS_CANCELED=Ytelsesanalyse for visningen "{0}" ble avbrutt.
##XMSG: The performance analysis failed
PERFORMANCE_ANALYSIS_FAILED=Ytelsesanalysen mislyktes.
##XMSG: The performance analysis is finished
PERFORMANCE_ANALYSIS_FINISHED=Ytelsesanalysen for visningen "{0}" er ferdig. Vis resultatet ved å klikke på "Vis detaljer".
##XMSG: The view has input parameter with no default value
INPUT_PARAMETER_WITHOUT_DEFAULT_VALUE=Denne visningen kan ikke analyseres fordi den har en parameter uten standardverdi.
##XMSG: The view is not fully deployed
VIEW_NOT_FULLY_DEPLOYED=Denne visningen kan ikke analyseres fordi den ikke er fullstendig distribuert.
##XMSG: The view uses limited remote adapters
LIMITED_REMOTE_ADAPTER=Denne visningen bruker minst én fjernadapter med begrenset funksjonalitet, for eksempel filter-pushdown eller støtte for 'Antall'. Ytelsen kan forbedres ved å persistere eller replikere objekter.
##XMSG: The view uses remote adapter that does not support 'Limit'
REMOTE_ADAPTER_NOT_SUPPORTING_LIMIT=Denne visningen bruker minst én fjernadapter som ikke støtter "Grense". Mer enn 1000 poster kan ha blitt valgt.
##XMSG: Performance analysis is executed by using default parameters of view'
INPUT_PARAMETER_WITH_DEFAULT_VALUE=Ytelsesanalysen utføres med standardverdier for visningsparametere.
##XMSG: Message to indicate that an error occurred
PERFORMANCE_ANALYSIS_ERROR=Det oppstod en feil under ytelsesanalyse for visning "{0}".
##XMSG: Message start of cancellation for Performance Analysis task
CANCEL_PERFORMANCE_ANALYSIS_TRIGGERED=Prosessen for å stoppe ytelsesanalyseoppgaven {0} for visningen "{1}" er sendt. Det kan oppstå en forsinkelse til oppgaven er stoppet.
##XMSG: Message start to indicate that task is not active
CANCEL_PERFORMANCE_ANALYSIS_NO_ACTIVE_TASK=Ytelsesanalyseoppgaven {0} for visningen "{1}" er ikke aktiv.
#XMSG: Cancel Performance Analysis task failure
CANCEL_PERFORMANCE_ANALYSIS_FAILED=Kan ikke avbryte ytelsesanalyseoppgaven.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Tilordne tidsplan til meg
#XBUT: Pause schedule menu label
pauseScheduleLabel=Sett tidsplan på pause
#XBUT: Resume schedule menu label
resumeScheduleLabel=Fortsett tidsplan
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Det oppstod en feil ved fjerning av tidsplaner.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Det oppstod en feil ved tilordning av tidsplaner.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Det oppstod en feil ved pause av tidsplaner.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Det oppstod en feil ved fortsetting av tidsplaner.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Sletter {0} tidsplaner
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Endrer eieren av {0} tidsplaner
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Setter {0} tidsplaner på pause
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Fortsetter {0} tidsplaner
#XBUT: Select Columns Button
selectColumnsBtn=Velg kolonner
#XFLD: Refresh tooltip
TEXT_REFRESH=Oppdater
#XFLD: Select Columns tooltip
text_selectColumns=Velg kolonner


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Runtime Metrics ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Label for Runtime Metrics
RuntimeMetricsTitle=Kjøretidsmålinger for
#XFLD : Label for Run Button
runButton=Kjør
#XFLD : Label for Cancel Button
cancelButton=Avbryt
#XFLD : Label for Close Button
closeButton=Lukk
#XFLD : Label for Open View Analyzer Button
openViewAnalyzerButton=Åpne visningsanalyse
#XFLD : Label for Generate Explain Plan Button
generateExplainPlanButton=Generer Explain-plan
#XFLD : Label for Previous Run Column
previousRun=Foregående kjøring
#XFLD : Label for Latest Run Column
latestRun=Nyeste kjøring
#XFLD : Label for time Column
time=Tid
#XFLD : Label for Duration Column
duration=Varighet
#XFLD : Label for Peak Memory Column
peakMemory=Høyeste minnebruk
#XFLD : Label for Number of Rows
numberOfRows=Antall rader
#XFLD : Label for Overall Number of Sources
overallNumberOfSources=Samlet antall kilder
#XFLD : Label for Data Access Column
dataAccess=Datatilgang
#XFLD : Label for Local Tables
localTables=Lokale tabeller
#XFLD : Label for federatedRemoteTables
federatedRemoteTables=Fødererte fjerntabeller (med begrenset adapterfunksjonalitet)
#XTXT Text for initial state of the runtime metrics
initialState=Du må først kjøre ytelsesanalysen for å hente målingene. Dette kan ta litt tid, men du kan avbryte prosessen hvis det er nødvendig.
#XMSG: Cancel confirmation message
CancelRunConfirmationMessage=Er du sikker på at du vil avbryte denne kjøringen av ytelsesanalysen?
#XTIT: Cancel dialog title
CancelRunTitle=Avbryt kjøring
#XFLD: Label for Number of Rows
NUMBER_ROWS=Antall rader
#XFLD: Label for Overall Number of Sources
OVERALL_NUMBER_SOURCES=Samlet antall kilder
#XFLD: Label for Data Access
DATA_ACCESS=Datatilgang
#XFLD: Label for Remote tables
NUMBER_REMOTE_TABLES_LIMITED_ADAPTERS=Fødererte fjerntabeller (med begrenset adapterfunksjonalitet)
#XFLD: Label for select statement
SELECT_STATEMENT='SELECT * FROM VIEW LIMIT 1000'
#XFLD: Label for duration
SELECT_RUNTIME=Varighet
#XFLD: Label for peak memory
SELECT_PEAK_MEMORY=Høyeste minnebruk
#XFLD: Label for count statement
SELECT_COUNT_STATEMENT='SELECT COUNT(*) FROM VIEW'
#XFLD: Label for count duration
SELECT_COUNT_RUNTIME=Varighet
#XFLD: Label for count peak memory
SELECT_COUNT_PEAK_MEMORY=Høyeste minnebruk
#XFLD: Label for count of number of local tables
NUMBER_LOCAL_TABLES_FILE=Lokale tabeller (fil)
#XTXT: Text for running state of the runtime metrics
Running=Kjører ...
#XFLD: Label for time
Time=Tid
#XFLD: Label for virtual access
PA_VIRTUAL=Virtuell
#XFLD: Label for persisted access
PA_PERSISTED=Persistert
PA_PARTIALLY_PERSISTED=Delvis persistert
#XTXT: Text for cancel
CancelRunSuccessMessage=Avbryter kjøring av ytelsesanalysen.
#XTXT: Text for cancel error
CancelRunErrorMessage=Det oppstod en feil ved avbrudd av ytelsesanalysekjøring.
#XTXT: Text for explain plan generation
ExplainPlanStarted=Genererer Explain-planfil for visning "{0}".
#XTXT: Text for performance analysis
PerformanceAnalysisStarted=Starter ytelsesanalyse for visning "{0}".
#XTXT: Text for performance analysis error
ErrorFetchingPerformanceAnalysis=Det oppstod en feil ved henting av ytelsesanalysedata.
#XTXT: Text for performance analysis error
conflictingTask=Ytelsesanalyseoppgave kjører allerede
#XFLD: Label for Errors
Errors=Feil
#XFLD: Label for Warnings
Warnings=Advarsel
#XFLD: Tooltip for missing privileges
openViewAnalyzerButtonTooltipNoPrivilege=Du må ha autorisasjonen DWC_DATAINTEGRATION(update) for å åpne visningsanalysen.
#XFLD: Tooltip for missing privileges
generateExplainPlanButtonTooltipNoPrivilege=Du må ha autorisasjonen DWC_RUNTIME(read) for å generere Explain-planen. 



#XFLD: Label for frequency column
everyLabel=Hver
#XFLD: Plural Recurrence text for Hour
hoursLabel=Timer
#XFLD: Plural Recurrence text for Day
daysLabel=Dager
#XFLD: Plural Recurrence text for Month
monthsLabel=Måneder
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minutter
