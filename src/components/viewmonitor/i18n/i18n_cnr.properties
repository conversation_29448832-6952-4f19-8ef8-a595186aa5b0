
#XBCB: Breadcrumb
viewMonitorBreadcrumb={spaceId}

#XFLD: Label for persisted view column
SOURCE=Izvor
#XFLD: Label for persisted view column
NAME=Naziv
#XFLD: Label for persisted view column
NAME_LABEL=Poslovni naziv
#XFLD: Label for persisted view column
NAME_LABELNew=<PERSON><PERSON><PERSON><PERSON> (poslovni naziv)
#XFLD: Label for persisted view column
TECHINCAL_NAME=Tehnički naziv
#XFLD: Label for persisted view column
TECHINCAL_NAMENew=<PERSON><PERSON><PERSON><PERSON> (tehnički naziv)
#XFLD: Label for persisted view column
DATA_PERSISTENCY=Pristup podacima
#XFLD: Label for persisted view column
STATUS=Status
#XFLD: Label for persisted view column
LAST_UPDATED=Posljednji put ažurirano
#XFLD: Label for persisted view column
IN_MEMORY_SIZE=Memorija korišćena za arhiviranje (MiB)
#XFLD: Label for persisted view column
DISK_SIZE=Disk korišćen za arhiviranje (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_NEWW=Veličina u memoriji (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_HEADER=Veličina u memoriji
#XFLD: Label for persisted view column
DISK_SIZE_NEWW=Veličina na disku (MiB)
#XFLD: Label for persisted view column
DISK_SIZE_HEADER=Veličina na disku
#XFLD: Label for schedule owner column
txtScheduleOwner=Odgovorno lice za plan
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Pokazuje ko je kreirao plan
#XFLD: Label for persisted view column
PERSISTED=Trajno sačuvano
#XFLD: Label for persisted view column
TYPE=Tip
#XFLD: Label for View Selection Dialog column
changedOn=Promijenjeno
#XFLD: Label for View Selection Dialog column
createdBy=Kreirao
#XFLD: Label for log details column
txtViewPersistencyLogs=Prikaži protokole
#XFLD: Label for log details column
txtViewPersistencyLogsNew=Detalji
#XFLD: text for values shown for Ascending sort order
SortInAsc=Poređaj po rastućem redosljedu
#XFLD: text for values shown for Descending sort order
SortInDesc=Poređaj po opadajućem redosljedu
#XFLD: Label for persisted view monitor heading
ViewMonitor_Heading=Monitor pogleda
#XFLD: Label for persisted view monitor sub-heading
ViewMonitor_SubHeading=Nadgledaj i održavaj trajnost podataka pogleda


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ DynamicPageHeader ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: text for values shown in column Persistence Status
txtLoading=Učitavanje
#XFLD: text for values shown in column Persistence Status
txtRunning=Izvodi se
#XFLD: text for values shown in column Persistence Status
txtAvailable=Dostupno
#XFLD: text for values shown in column Persistence Status
txtError=Greška
#XMSG: Error message for unknown Persistence type that gets logged in Kibana
unknownReplicationTypeError=Tip replikacije "{0}" nije podržan.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Content ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Podešavanja korišćena za posljednje izvođenje trajnosti podataka:
#XMSG: Message for input parameter name
inputParameterLabel=Parametar unosa
#XMSG: Message for input parameter value
inputParameterValueLabel=Vrijednost
#XMSG: Message for persisted data
inputParameterPersistedLabel=Trajno sačuvano u
#XTIT: Count for View in ViewMonitor Toolbar
txtViewCount=Pogledi ({0})
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Trajnost pogleda
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Trajnost podataka
#XBUT: Button to clear the selected view persistance
ClearViewPersistance=Poništi
#XBUT: Button to stop the selected view persistance
stopPersistance=Zaustavi trajnost
#XFLD: Placeholder for Search field
txtSearch=Traži
#XBUT: Tooltip for refresh button
txtRefresh=Osvježi
#XBUT: Tooltip for add view button
txtDeleteView=Izbriši trajnost
#XBUT: Tooltip for load new peristence
loadNewPersistence=Ponovo pokreni trajnost
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Učitaj novi trenutni snimak
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Pokreni trajnost podataka
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Ukloni trajno sačuvane podatke
#XMSG: success message for starting persistence
startPersistenceSuccess=Trajno snimamo pogled "{0}".
#XMSG: success message for stopping persistence
stopPersistenceSuccess=Uklanjamo trajno sačuvane podatke za pogled "{0}".
#XMSG: success message for stopping persistence
deletePersistedViewSuccess=Uklanjamo pogled "{0}" sa liste nadzora.
#XMSG: error message during start persistence of a view (backend error)
ERROR_STARTING_PERSISTENCE=Greška pri pokretanju trajnosti podataka za pogled "{0}".
#XMSG: error message during start persistence of a view (backend error)
PARAMERROR_STARTING_PERSISTENCE=Pogled "{0}" se ne može trajno sačuvati jer sadrži parametre unosa.
#XMSG: error message during start persistence of a view (backend error)
NUMBER_OF_PARAMETER_STARTING_PERSISTENCE=Pogled "{0}" nije moguće zadržati jer ima više od jednog parametra unosa.
#XMSG: error message during start persistence of a view (backend error)
PARAMETER_DEFAULT_VALUE_STARTING_PERSISTENCE=Pogled "{0}" nije moguće zadržati jer parametar unosa nema standardnu vrijednost.
#XMSG: error message during start persistence of a view (backend error)
REDEPLOYMENT_DAC_REQUIRED=Preraspodjela kontrole pristupa podacima (DAC) "{0}" je obavezna za podršku trajnosti podataka.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_STARTING_PERSISTENCE=Pogled "{0}" se ne može trajno sačuvati jer koristi pogled "{1}", koji sadrži kontrolu pristupa podacima (DAC).
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_EXTERNAL_STARTING_PERSISTENCE=Pogled "{0}" se ne može trajno sačuvati jer koristi pogled s kontrolom pristupa podacima (DAC) koji pripada drugom prostoru.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_STRUCTURE_STARTING_PERSISTENCE=Pogled "{0}" se ne može trajno sačuvati jer struktura jedne ili više kontrola pristupa podacima (DAC) ne podržava trajnost podataka.
#XMSG: error message during stop persistence of a view (backend error)
STOP_PERSISTENCE_ERROR=Greška pri zaustavljanju trajnosti za pogled "{0}".
#XMSG: error message during stop persistence of a view (backend error)
DELETE_PERSISTED_VIEW_ERROR=Greška pri brisanju trajno sačuvanog pogleda "{0}".
#XMSG: Warning message for removing the persisted data
RemovePersistency_Confirm_Msg=Da li želite da izbrišete trajno sačuvane podatke i prebacite na virtuelni pristup pogleda "{0}"?
#XMSG: Warning message for delete the view persistence
DeletePersistency_Confirm_Msg=Da li želite da uklonite pogled sa liste nadzora i izbrišete trajno sačuvane podatke pogleda "{0}"?
#XMSG: error message for reading data from backend
txtReadBackendError=Izgleda da je došlo do greške pri čitanju iz back-end-a.
#XFLD: Label for No Data Error
NoDataError=Greška
#XMSG: message for conflicting task
Task_Already_Running=Zadatak s konfliktom se već izvodi za pogled "{0}".

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Show All Views~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Text for show all views button
allViewsText=Svi pogledi ({0})
#XBUT: Text for show scheduled views button
scheduledText=Planirano ({0})
#XBUT: Text for show persisted views button
persistedText=Trajno sačuvano ({0})
#XBUT: Text for start analyzer button
startAnalyzer=Pokreni analizator pogleda
#XFLD: Message if repository is unavailable
repositoryErrorMsg=Repozitorijum nije dostupan i neka svojstva su deaktivirana.

#XFLD: Data Access - Virtual
Virtual=Virtuelno
#XFLD: Data Access - Persisted
Persisted=Trajno sačuvano

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Non-Persisted View Selection Dialog~~~~~~~~~~~~~~~~~~~~~~~

#XTIT: Title for Non-Persisted View selection dialog
select_source=Odaberi pogled za trajno snimanje

#XTIT: Placeholder for Non-Persisted View selection dialog search
searchView=Traži poglede
#XTIT: No data in the list of non-persisted view
No_Data=Nema podataka
#XBUT: Button to select non-persisted view
ok=OK
#XBUT: Button to close the non-persisted views selection dialog
cancel=Odustani

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~View persistency log message key~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message for view persistency logs
START_STOP_PERSISTENCY=Pokretanje izvršenja zadatka trajnosti podataka za "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY=Trajno snimanje podataka za pogled "{1}".
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY)
START_VIEW_PERSISTENCY_PROCESS=Pokretanje procesa za trajno snimanje podataka za pogled "{0}".
#XFLD: Message for view persistency manual partition load
START_VIEW_PERSISTENCY_PROCESS_MANUAL_PARTITIONS=Pokretanje procesa za trajno snimanje podataka za pogled "{0}" sa ID-ovima odabrane particije "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY=Uklanjanje trajno sačuvanih podataka za pogled "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY)
REMOVE_VIEW_PERSISTENCY=Pokretanje procesa za uklanjanje trajno sačuvanih podataka za pogled "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_SUCCESS=Podaci trajno sačuvani za pogled "{1}".
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_SUCCESS)
START_VIEW_PERSISTENCY_PROCESS_SUCCESS=Podaci trajno sačuvani za pogled "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY_SUCCESS=Trajno sačuvani podaci uklonjeni i virtuelni pristup podacima obnovljen za pogled "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_SUCCESS)
REMOVE_VIEW_PERSISTENCY_SUCCESS=Proces za uklanjanje trajno sačuvanih podataka za pogled "{0}" je završen.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_FAILED=Nije uspjelo trajno snimanje podataka za pogled "{1}".
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_FAILED)
START_VIEW_PERSISTENCY_PROCESS_FAILED=Nije uspjelo trajno snimanje podataka za pogled "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.05)
STOP_VIEW_PERSISTENCY_FAILED=Nije uspjelo uklanjanje trajno sačuvanih podataka za pogled "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_FAILED)
REMOVE_VIEW_PERSISTENCY_FAILED=Nije uspjelo uklanjanje trajno sačuvanih podataka za pogled "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_SUCCESS="{3}" zapisa trajno sačuvano za pogled "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace START_PERSISTENCY_RECORD_COUNT_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS={0} zapisa unijeto u tabelu trajnosti podataka za pogled "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS_V2={0} zapisa unijeto u tabelu trajnosti podataka za pogled "{1}". Korišćena memorija: {2} GiB.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_SUCCESS="{3}" trajno sačuvanih zapisa uklonjeno za pogled "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace STOP_PERSISTENCY_RECORD_COUNT_SUCCESS)
REMOVE_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=Trajno sačuvani podaci uklonjeni, izbrisano "{0}" trajno sačuvanih zapisa.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_FAILED=Pozivanje recordCount nije uspjelo za pogled "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_FAILED=Pozivanje recordCount nije uspjelo za pogled "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Plan je izbrisan za "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS)
REMOVE_VIEW_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Plan je izbrisan za pogled "{0}".
#XFLD: Message for view persistency logs
STOP_PERSISTENCY_DELETE_SCHEDULE_FAILED=Brisanje plana nije uspjelo za "{1}".
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_REDEPLOY=Ne možemo trajno sačuvati pogled "{0}" jer je promijenjen i implementiran od kad ste započeli da ga trajno snimate. Pokušajte ponovo da trajno sačuvate pogled ili sačekajte do sljedećeg planiranog izvođenja.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_DELETE=Ne možemo trajno sačuvati pogled "{0}" jer je izbrisan od kad ste započeli da ga trajno snimate.
#XFLD: Message for successful data persistency of a single partition (for messages created before version 2022.03)
START_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} zapisa trajno sačuvano u particiju za vrijednosti "{1}" <= {2} < "{3}".
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (new message to replace START_PERSISTENCY_SINGLE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} zapisa unijeto u particiju za vrijednosti "{1}" <= "{2}" < "{3}".
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS_V2={0} zapisa unijeto u particiju za vrijednosti "{1}" <= "{2}" < "{3}". Korišćena memorija: {4} GiB.
#XFLD: Message for successful replication of a "others" partition (for messages created before version 2022.03)
START_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} zapisa trajno sačuvano u particiju "drugi".
#XFLD: Message used by SEAL repository for successful replication of a "others" partition (new message to replace START_PERSISTENCY_OTHERS_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} zapisa unijeto u particiju "drugi".
#XFLD: Message for view persistency record count of partitions (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={3} zapisa trajno sačuvano za pogled "{1}" u {4} particija.
#XFLD: Message used by SEAL repository for view persistency record count of partitions (new message to replace START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={0} zapisa unijeto u tabelu trajnosti podataka za pogled "{1}" u {2} particija.
#XFLD: Message used by SEAL repository for view persistency for partition wise refresh
START_VIEW_PERSISTENCY_RECORD_COUNT_REFRESHED_PARTITIONS_SUCCESS={0} zapisa unijeto u tabelu trajnosti podataka za pogled "{1}". Ažurirane particije: {2} Zaključane particije:{3} Ukupni broj particija:{4}
#XFLD: Message used by SEAL repository for view persistency for manual partition load
START_VIEW_PERSISTENCY_RECORD_COUNT_MANUAL_PARTITIONS_SUCCESS={0} zapisa unijeto u tabelu trajnosti podataka za pogled "{1}" u {2} odabranih particija.
#XFLD: Message used by SEAL repository for view persistency for locked partition data load
START_VIEW_PERSISTENCY_RECORD_COUNT_LOCKED_PARTITIONS_SUCCESS={0} zapisa unijeto u tabelu trajnosti podataka za pogled "{1}". Ažurirane particije: {2} Zaključane nepromijenjene particije:{3} Ukupni broj particija:{4}
#XMSG: Message for unexpected error (for messages created before version 2022.03)
START_PERSISTENCY_UNEXPECTED_ERROR=Neočekivana greška pri trajnom snimanju podataka za pogled "{0}".
#XMSG: Message for unexpected error (new message to replace START_PERSISTENCY_UNEXPECTED_ERROR)
START_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Neočekivana greška pri trajnom snimanju podataka za pogled "{0}".
#XMSG: Message for Locked space error
START_VIEW_PERSISTENCY_LOCKED_SPACE_ERROR=Pogled "{0}” se ne može trajno sačuvati jer je prostor "{1}" zaključan.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
STOP_PERSISTENCY_UNEXPECTED_ERROR=Neočekivana greška pri uklanjanju trajno sačuvanih podataka za pogled "{0}".
#XMSG: Message for unexpected error (new message to replace STOP_PERSISTENCY_UNEXPECTED_ERROR)
REMOVE_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Neočekivana greška pri uklanjanju trajnosti za pogled "{0}".
#XMSG: Message for ivalidated view
VIEW_PERSISTENCY_ABORTED_DEFINITION_INVALID=Definicija pogleda "{0}" je postala nevažeća; najvjerovatnije zbog promjene objekta koji pogled direktno ili indirektno koristi. Pokušajte da ponovo implementirate pogled da biste otklonili ovaj problem ili pronađite osnovni uzrok.
#XMSG: Message for persisted data removal (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT=Trajno sačuvani podaci su uklonjeni dok se implementira pogled "{0}".
#XMSG: Message for persisted data removal of a consuming view
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW=Trajno sačuvani podaci su uklonjeni dok se implementira korišćeni pogled "{0}".
#XMSG: Message for persisted data removal of a consuming view due to a dac change
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW_DAC_CHANGED=Trajno sačuvani podaci su uklonjeni dok se implementira korišćeni pogled "{0}" jer je promijenjena kontrola pristupa podacima za njih.
#XMSG: Message for persisted data removal (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DEPLOYMENT=Trajno sačuvani podaci su uklonjeni dok se implementira pogled "{0}".
#XMSG: Message for deleted view (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION=Trajnost se uklanja brisanjem pogleda "{0}".
#XMSG: Message for deleted view (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DELETION=Trajnost se uklanja brisanjem pogleda "{0}".
#XMSG Message for removal of persisted data, because prerequisites not fulfilled
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_PREREQUISITES_NOT_FULFILLED=Trajno sačuvani podaci se uklanjaju jer preduslovi trajnosti podataka više nijesu ispunjeni.
#XMSG: Message for inconsistent persistency
VIEW_PERSISTENCY_INCONSISTENT_PERSISTENCY=Trajnost pogleda "{0}" je postala nedosljedna. Uklonite trajne podatke da biste riješili problem.
#XMSG: Message for prerequisites
START_PERSISTENCY_CHECK_PREREQUISITES=Provjera preduslova za trajno snimanje pogleda "{0}".
#XMSG: Message for DAC deployed in deprecated way using table function and join (for messages created before 2025.05)
START_PERSISTENCY_DEPRECIATED_DAC=Pogled "{0}" je implementiran pomoću kontrole pristupa podacima (DAC) koja je u procesu zastarijevanja. Implementirajte pogled ponovo da biste unaprijedili učinak.
#XMSG: Message for DAC deployed in deprecated way using table function and join
START_PERSISTENCY_DEPRECATED_DAC=Pogled "{0}" je implementiran pomoću kontrole pristupa podacima (DAC) koja je u procesu zastarijevanja. Implementirajte pogled ponovo da biste unaprijedili učinak.
#XMSG: Message for roll back (for messages created before version 2022.03)
ROLLBACK_VIEW_PERSISTENCY=Došlo je do greške. Prethodni status trajnosti obnovljen za pogled "{0}".
#XMSG: Message used by SEAL repository for rollback of running procedure to persist view (new message to replace ROLLBACK_VIEW_PERSISTENCY)
VIEW_PERSISTENCY_ROLLBACK=Došlo je do greške. Proces za trajno snimanje pogleda "{0}" je prekinut i promjene su odbačene.
#XMSG: Message used by SEAL repository for rollback of running procedure to remove persisted data of view
REMOVE_VIEW_PERSISTENCY_ROLLBACK=Došlo je do greške. Proces za uklanjanje trajno sačuvanih podataka pogleda "{0}" je prekinut i promjene su odbačene.
#XMSG: Message used by SEAL repository for persistency preparation
START_PERSISTENCY_PREPARATION=Priprema trajnog snimanja podataka.
#XMSG: Message used by SEAL repository for data insertion
START_INSERTING_PERSISTENCY_DATA=Unos podataka u tabelu trajnosti.
#XMSG: Message for null values - partitioning (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS={0} zapisa nulte vrijednosti unijeto u particiju "drugi".
#XMSG: Message used by SEAL repository for others partition values - partitioning (new message to replace START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS={0} zapisa unijeto u particiju "drugi" za vrijednosti "{2}" < "{1}" ILI "{2}" >= "{3}".
#XMSG: Message used by SEAL repository for others partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS_V2={0} zapisa unijeto u particiju "drugi" za vrijednosti "{2}" < "{1}" ILI "{2}" >= "{3}". Korišćena memorija: {4} GiB.
#XMSG: Message used by SEAL repository for null partition values - partitioning
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS={0} zapisa unijeto u particiju "drugi" za vrijednosti "{1}" IS NULL.
#XMSG: Message used by SEAL repository for null partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS_V2={0} zapisa unijeto u particiju "drugi" za vrijednosti "{1}" IS NULL. Korišćena memorija: {2} GiB.
##XFLD:Message used by SEAL repository for remote statistics
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS=Učitavanje uključenih podataka: {0} udaljenih naredbi. Ukupni pozvani zapisi: {1}. Ukupno vrijeme trajanja: {2} sekundi.
##XFLD:Message used by SEAL repository for remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS_WITH_PARTITIONS=Učitavanje uključenih podataka pomoću {0} particija sa {1} udaljenih naredbi. Ukupni pozvani zapisi: {2}. Ukupno vrijeme trajanja: {3} sekundi.
##XFLD:Message used by SEAL repository with UI ID for instructions of remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_INSTRUCTION=Udaljene naredbe obrađene tokom izvođenja mogu se prikazati otvaranjem monitora udaljenih upita u detaljima poruka specifičnih za particije.
##XFLD:Message for start reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS=Pokretanje procesa za ponovnu upotrebu postojećih trajno sačuvanih podataka za pogled {0} nakon implementacije.
##XFLD:Message used by SEAL repository for reattach view persitency
START_VIEW_PERSISTENCY_REATTACHMENT=Započnite ponovnu upotrebu postojećih trajno sačuvanih podataka.
##XFLD:Message used by SEAL repository for reattach view persitency
START_FINILIZING_VIEW_PERSISTENCY_REATTACHMENT=Ponovna upotreba postojećih trajno sačuvanih podataka.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_COMPLETE=Proces za ponovnu upotrebu postojećih trajno sačuvanih podataka je završen za pogled {0}.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_FAILED=Nije uspjela ponovna upotreba postojećih trajno sačuvanih podataka za pogled {0} nakon implementacije.
#XMSG: Message used by SEAL repository for finalizing persistency
START_FINALIZING_PERSISTENCY=Završavanje trajnosti.
#XMSG: Message used by SEAL repository for restoring virtual access for view
REMOVE_VIEW_PERSISTENCY_VIRTUAL_ACCESS_RESTORED=Virtuelni pristup podacima obnovljen za pogled "{0}".
#XMSG: Message used by SEAL repository for view that already has virtual access
REMOVE_VIEW_PERSISTENCY_ALREADY_VIRTUAL_ACCESS=Pogled "{0}" već ima virtuelni pristup podacima. Trajno sačuvani podaci nijesu uklonjeni.
#XMSG: Message for removing view persistency
VIEW_PERSISTENCY_REMOVED_FROM_MONITOR=Pogled "{0}" je uklonjen iz monitora pogleda.
#XMSG: Message for view doesnt exist.
VIEW_PERSISTENCY_VIEW_NOT_EXISTING=Pogled "{0}" ne postoji u bazi podataka ili nije ispravno implementiran, pa se stoga ne može trajno sačuvati. Pokušajte da ponovo implementirate pogled da biste riješili problem ili identifikovali osnovni uzrok.
#XMSG: Message for persistency not enabled.
VIEW_PERSISTENCY_NOT_ENABLED=Trajnost podataka nije aktivirana. Ponovo implementirajte tabelu/pogled u prostoru "{0}" da biste aktivirali funkcionalnost.
#XMSG: Message for unexpected error due to node crash
VIEW_PERSISTENCY_UNEXPECTED_ERROR_GENERAL=Posljednje izvođenje trajnosti pogleda je prekinuto zbog tehničkih grešaka.
#XFLD: Message for view persistency memory consumption
VIEW_PERSISTENCY_MEMORY_CONSUMPTION={0} GiB maksimuma memorije korišćeno u vremenu izvođenja trajnosti pogleda.
#XFLD: Message for view persistency timeout
VIEW_PERSISTENCY_TIMEOUT=Trajnost pogleda {0} je dostigla istek vremena od {1} sati.
#XFLD: Message for not started asynchronous view persistency
VIEW_PERSISTENCY_ASYNC_NOT_STARTED=Visoko opterećenje sistema je spriječilo pokretanje asinhronog izvršenja trajnosti pogleda. Provjerite da li se izvodi previše paralelnih zadataka.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency (for messages created before version 2022.05)
START_VIEW_PERSISTENCY_EXECUTION_METHOD_DROP_AND_RENAME=Postojeća trajno sačuvana tabela je izbrisana i zamijenjena novom trajno sačuvanom tabelom.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_RECREATE_AND_DROP=Postojeća trajno sačuvana tabela je izbrisana i zamijenjena novom trajno sačuvanom tabelom.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_TRUNCATE_AND_COPY=Postojeća trajno sačuvana tabela je ažurirana novim podacima.
##XFLD: Message for missing authorizations for view persistency
VIEW_PERSISTENCY_MISSING_AUTH=Nedostaju ovlašćenja za trajnost podataka.
##XFLD: Message start of persistency cancellation
VIEW_PERSISTENCY_TRIGGER_CANCEL=Pokretanje otkazivanja procesa za trajno snimanje pogleda {0}.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_UNAVAILABLE_TASK_RUN=Nije uspjelo otkazivanje procesa za trajno snimanje pogleda jer za pogled {0} ne postoji zadatak trajnosti podataka koji se izvodi.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_NONDETERMINISTIC_TASK_RUN=Nije uspjelo otkazivanje procesa za trajno snimanje pogleda jer se zadatak trajnosti podataka ne izvodi za pogled {0}.
##XFLD: Message for canceling a non running persitency task
VIEW_PERSISTENCY_CANCEL_NON_RUNNING_SELECTED_TASK=Nije uspjelo otkazivanje procesa za trajno snimanje pogleda {0} jer se odabrani zadatak trajnosti podataka {1} ne izvodi.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_UNREACHABLE_PERSISTENCY_RUN=Nije uspjelo otkazivanje procesa za trajno snimanje pogleda jer trajnost podataka za pogled {0} još nije pokrenuta.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_COMPLETED_PERSISTENCY_RUN=Nije uspjelo otkazivanje procesa za trajno snimanje pogleda {0} zato što je proces već završen.
##XFLD: Message for failed cancellation
VIEW_PERSISTENCY_ABORTED_CANCEL_EXECUTION=Nije uspjelo otkazivanja procesa za trajno snimanje pogleda {0}.
##XFLD: Message for finalizing persistency cancellation
VIEW_PERSISTENCY_FINALIZE_CANCEL=Proces za zaustavljanje trajnosti podataka pogleda {0} je podnijet.
##XFLD: Message used in persist task when cancellation is caught
VIEW_PERSISTENCY_CANCELED=Proces za trajno snimanje pogleda {0} je zaustavljen.
##XFLD: Message used in persist task when cancelation is caught and performed through a cancelation task
VIEW_PERSISTENCY_CANCELED_VIA_CANCEL_TASK=Proces za trajno snimanje pogleda {0} je zaustavljen pomoću zadatka otkazivanja {1}.
##XFLD: Message used in cancel task when the task is triggered by deployment
VIEW_PERSISTENCY_CANCEL_TRIGGERED_BY_DEPLOYMENT=Otkazivanje procesa za trajno snimanje podataka pri implementaciji pogleda {0}.
##XFLD: Message used in case the cancelation is retriggered for a running task
VIEW_PERSISTENCY_CANCEL_ATTEMPT_RETRIGGER_CANCEL=Prethodni zadatak otkazivanja za trajno snimanje pogleda {0} je već podnijet.
##XFLD: Message used to express the delay that may occur before the persistency task is stopped
VIEW_PERSISTENCY_CANCEL_DELAY_STOPPING_PERSISTENCY=Moguće je kašnjenje dok se ne zaustavi zadatak trajnog snimanja podataka za pogled {0}.
##XFLD: Message used to reflect the task log identifier of the current running persistency task
VIEW_PERSISTENCY_CANCEL_CURRENT_RUNNING_TASK=Podaci za pogled {0} se trajno snimaju sa zadatkom {1}.
##XFLD:Message used by SEAL repository for locked partitions and DACs
VIEW_PERSISTENCY_LOCKED_PARTITIONS_DACS=Ovlašćenja koja pružaju kontrole pristupa podacima mogu biti promijenjena i zaključane particije ih ne uzimaju u obzir. Otključajte particije i učitajte novi trenutni snimak da biste primijenili promjene.
##XFLD:Message used by SEAL repository for column structure change
VIEW_PERSISTENCY_COLUMN_STRUCTURE_CHANGE=Struktura kolone je promijenjena i više se ne podudara s postojećom trajno sačuvanom tabelom. Uklonite trajno sačuvane podatke i pokrenite novo trajno snimanje podataka da biste ažurirali trajno sačuvanu tabelu.
##XFLD: Message for out of memory
VIEW_PERSISTENCY_HANA_OUT_OF_MEMORY=Zadatak nije uspio zbog greške nedostatka memorije u bazi podataka SAP HANA.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_EXCEPTION_TRANSACTION_ROLLBACK=Zadatak nije uspio zbog internog izuzetka u bazi podataka SAP HANA.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_SQL_EXECUTION_ISSUE=Zadatak nije uspio zbog internog problema izvršenja SQL u bazi podataka SAP HANA.
##XFLD: Message for HANA out of memory result
VIEW_PERSISTENCY_OUT_OF_MEMORY_RESULT_MESSAGE=Razlog događaja nedostatka memorije HANA: {0}
##XFLD: Message for admission control rejection
VIEW_PERSISTENCY_HANA_ADMISSION_CTRL_REJECTION=Zadatak nije uspio zbog odbijanja kontrole prema SAP HANA.
##XFLD: Message for HANA back pressure / rate limit of HANA client
VIEW_PERSISTENCY_HANA_CLIENT_RATE_LIMIT_REJECTION=Zadatak nije uspio zbog previše aktivnih SAP HANA veza.
#XMSG: Message for invalid persisted table name
VIEW_PERSISTENCY_PERSISTED_TABLE_INVALID=Došlo je do greške i trajno sačuvana tabela je postala nevažeća. Da biste riješili ovaj problem, uklonite trajno sačuvane podatke i ponovo trajno sačuvajte pogled.
#XMSG: Message for usahe of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION=Pogled se ne može trajno sačuvati. Koristi udaljenu tabelu zasnovanu na udaljenom izvoru sa aktiviranim propagiranjem korisnika. Provjerite porijeklo pogleda.
#XMSG: Message for dynamic usage of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION_DYNAMIC=Pogled se ne može trajno sačuvati. Koristi udaljenu tabelu zasnovanu na udaljenom izvoru sa aktiviranim propagiranjem korisnika. Udaljena tabela se može dinamički koristiti preko pogleda SQL Script-a. Porijeklo pogleda možda neće prikazati udaljenu tabelu.
#XMSG: Message for insufficient privileges or consumption of view with DAC via dynamic SQL
VIEW_PERSISTENCY_INSUF_PRIV_OR_DAC_DYN_SQL=Vaša ovlašćenja možda nijesu dovoljna. Otvorite prethodni prikaz podataka da vidite da li imate potrebna ovlašćenja. Ako imate, možda drugi pogled koji se koristi pomoću dinamičkog SQL script-a ima primijenjenu kontrolu pristupa podacima (DAC).
#XMSG: deployment of DAC is deprecated and view need to be redeployed to be able to persist data
VIEW_PERSISTENCY_DEPRECATED_DAC=Pogled "{0}" je implementiran pomoću kontrole pristupa podacima (DAC) koja je u procesu zastarijevanja. Implementirajte pogled ponovo da biste mogli da zadržite podatke za pogled.
##XFLD:Message used by SEAL repository for input parameter
START_VIEW_PERSISTENCY_INPUT_PARAMETER=Upotreba standardne vrijednosti "{0}" za parametar unosa "{1}".
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Elastic compute node keys~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
##XFLD:Message used to reflect disablement of ecn replica
ECN_DISABLE_EXISTING_REPLICA=Replika elastičnog čvora izračunavanja je deaktivirana.
##XFLD:Message used to reflect recreation of ecn replica
ECN_RECREATE_EXISTING_REPLICA=Replika elastičnog čvora izračunavanja je ponovo kreirana.
##XFLD:Message used to reflect re-enablement of ecn replica
ECN_ENABLE_EXISTING_REPLICA=Replika elastičnog čvora izračunavanja je ponovo aktivirana.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Task Schedule Menu key~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message to schedule task
scheduleText=Plan
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Kreiraj plan
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Uredi plan
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Izbriši plan
#XFLD: Refresh frequency field
refreshFrequency=Učestalost osvježavanja
#XFLD: Refresh frequency field
refreshFrequencyNew=Učestalost
#XFLD: Refresh frequency field
refreshFrequencyNewNew=Planirana učestalost
#XBUT: label for None
none=Ništa
#XBUT: label for Real-Time replication state
realtime=U realnom vremenu
#XFLD: Label for table column
txtNextSchedule=Sljedeće izvođenje
#XFLD: Label for table column
txtNextScheduleNew=Planirano sljedeće izvođenje
#XFLD: Label for table column
txtNumOfRecords=Broj zapisa
#XFLD: Label for scheduled link
scheduledTxt=Planirano
#XFLD: LABEL for partially persisted link
partiallyPersisted=Djelimično trajno sačuvano
#XFLD: Text for paused text
paused=Pauzirano

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ View Monitor Tasklog Details Action keys~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message for release lock actionn
releaseLockMsg=Ako izvođenje potraje duže nego obično, to može značiti da nije uspjelo i da status nije ažuriran u skladu s tim. \r\n Da biste otklonili problem, možete ukloniti blokadu i postaviti njen status na Nije uspjelo.
#XFLD: Label for release lock dialog
releaseLockText=Ukloni blokadu

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Column header tooltip ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: tooltip for table column
txtViewTooltip=Naziv trajno sačuvanog pogleda
#XFLD: tooltip for table column
txtViewDataAccessTooltip=Ovo pokazuje dostupnost pogleda
#XFLD: tooltip for table column
txtViewRefreshFreqTooltip=Ovo pokazuje da li je plan definisan za pogled
#XFLD: tooltip for table column
txtViewStatusTooltip=Pozovite status trajno sačuvanog pogleda
#XFLD: tooltip for table column
txtViewLatestUpdateTooltip=Pruža informacije o vremenu posljednjeg ažuriranja trajno sačuvanog pogleda
#XFLD: tooltip for table column
txtViewNextRunTooltip=Ako je plan postavljen za pogled, pogledajte do kada je planirano sljedeće izvođenje
#XFLD: tooltip for table column
txtViewNumOfRecordsTooltip=Pratite broj zapisa.
#XFLD: tooltip for table column
txtViewInMemorySizeTooltip=Pratite koliko prostora pogled koristi u vašoj memoriji
#XFLD: tooltip for table column
txtViewDiskSizeTooltip=Pratite koliko prostora pogled zauzima na vašem disku
#XMSG: Expired text
txtExpired=Isteklo

#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Objekat "{0}" se ne može dodati u lanac zadataka.

#~~~~~~~~~~~View Analyzer task log messages~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message indicating number of records for a view, and memory consumption in Mebibyte during simulation of view persistency
RECORDS_PEAK_MEMORY=Pogled "{0}" ima {1} zapisa. Simulacija trajnosti podataka za ovaj pogled iskoristila je {2} MiB memorije.
#XMSG: View Analyzer execution failure
VIEW_ANALYZER_EXECUTION_FAILED=Izvršenje analizatora pogleda nije uspjelo.
#XMSG: Missing authorizations for View Analyzer
VIEW_ANALYZER_MISSING_AUTH=Nedostaju ovlašćenja za analizator pogleda.
#XMSG: Maximum memory in Gibibyte is reached, therefore persistency simulation is stopped
MAXIMUM_MEMORY_REACHED=Maksimalna memorija od {0} GiB je dostignuta pri simuliranju trajnosti podataka za pogled "{1}". Stoga se dodatne simulacije trajnosti podataka neće izvijesti.
#XMSG: Error during persistency simulation occurred
PERSISTENCY_SIMULATION_ERROR=Greška pri simulaciji trajnosti podataka za pogled "{0}".
#XMSG: Persistency simulation is not executed, because prerequisites are not fulfilled and the view cannot be persisted
PREREQUISITES_NOT_FULFILLED=Simulacija trajnosti podataka nije izvršena za pogled "{0}" jer nijesu ispunjeni preduslovi i pogled se ne može trajno sačuvati.
#XMSG: Persistency simulation is not executed, because the view does not exist in the database
VIEW_DOES_NOT_EXIST=Morate implementirati pogled "{0}" da biste omogućili simulaciju trajnosti podataka.
#XMSG: Number of records cannot be determined, because the local table does not exist in the database
TABLE_DOES_NOT_EXIST=Lokalna tabela "{0}" ne postoji u bazi podataka, stoga se broj zapisa ne može odrediti za ovu tabelu.
##XMSG: Message start of cancellation for View Analyzer task
CANCEL_VIEW_ANALYZER_TRIGGERED=Podnijet je proces za zaustavljanje zadatka analizatora pogleda {0} za pogled "{1}". Moguće je kašnjenje dok se zadatak ne zaustavi.
##XMSG: Message start to indicate that task is not active
CANCEL_VIEW_ANALYZER_NO_ACTIVE_TASK=Zadatak analizatora pogleda {0} za pogled "{1}" nije aktivan.
#XMSG: Cancel View Analyzer task failure
CANCEL_VIEW_ANALYZER_FAILED=Nije uspjelo otkazivanje zadatka analizatora pogleda.
##XMSG: Message used in View Analyzer execution task when task is canceled
VIEW_ANALYZER_CANCELED=Izvršenje analizatora pogleda za pogled "{0}" je zaustavljeno preko zadatka otkazivanja.
##XMSG: Message start of cancelation for Model Validation task
CANCEL_MODEL_VALIDATION_TRIGGERED=Proces za zaustavljanje zadatka vrednovanja modela {0} za pogled "{1}" je podnijet. Moguće je kašnjenje pri zaustavljanju zadatka.
##XMSG: Message to indicate that task is not active
CANCEL_MODEL_VALIDATION_NO_ACTIVE_TASK=Zadatak vrednovanja modela {0} za pogled "{1}" nije aktivan.
#XMSG: Cancel Model Validation task failure
CANCEL_MODEL_VALIDATION_FAILED=Nije uspjelo otkazivanje zadatka vrednovanja modela.
##XMSG: Message used in Model Validation task when task is canceled
MODEL_VALIDATION_CANCELED=Izvršenje vrednovanja modela za pogled "{0}" je zaustavljeno pomoću zadatka otkazivanja.
#XMSG: Message for Locked space error, before starting Model Validation task
MODEL_VALIDATION_LOCKED_SPACE_ERROR=Nije moguće izvršiti vrednovanje modela za pogled "{0}" jer je prostor "{1}" zaključan.
##XMSG: Message used in View Analyzer execution task when determination of number of rows fails
NUMBER_ROWS_DETERMINATION_FAILED=Greška pri određivanju broja redova za lokalnu tabelu "{0}".
##XMSG: Message to indicate that SQL plan was created
EXECUTION_PLAN_CREATED=Kreiran je fajl plana SQL analizatora za pogled "{0}"  i može se prenijeti sa servera.
#XFLD: Message to indicate starting of process to generate SQL Analyzer plan file
START_PLANVIZ_EXECUTION=Pokretanje procesa za generisanje fajla plana SQL analizatora za pogled "{0}".
#XFLD: Message to indicate starting of View Analyzer process
VIEW_ANALYZER_EXECUTION_STARTED=Pokretanje izvršenja analizatora pogleda.
##XMSG: Message to indicate that SQL plan file cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_PLANVIZ_NOT_FULFILLED=Fajl plana SQL analizatora ne može se generisati za pogled "{0}", jer preduslovi trajnosti podataka nijesu ispunjeni.`
##XMSG: Error message during  generation of SQL Analyzer plan file
PLANVIZ_ERROR=Greška pri generisanju fajla plana SQL analizatora za pogled "{0}".
#XMSG: Message for Locked space error
VIEW_ANALYZER_LOCKED_SPACE_ERROR=Nije moguće izvršiti analizator pogleda za pogled "{0}", pošto je prostor "{1}" zaključan.
#XMSG: Message for persistency simulation without partitioning
PERSISTENCY_SIMULATION_WITHOUT_PARTITIONS=Particije pogleda "{0}" ne uzimaju se u obzir tokom simulacije trajnosti podataka.
#XMSG: Message for generation of SQL Analyzer plan file without considering partitioning
PLANVIZ_WITHOUT_PARTITIONS=Particije pogleda "{0}" ne uzimaju se u obzir tokom generisanja fajla plana SQL analizatora.
#XMSG: Confirmation message for mass removal of persisted data
MassRemovePersistency_Confirm_Msg=Da li želite da uklonite trajno sačuvane podatke i vratite pristup podacima nazad na virtuelni pristup?
#XMSG: Confirmation message for partial mass removal of selected persisted data
PartialMassRemovePersistency_Confirm_Msg={0} od {1} odabranih pogleda imaju trajno sačuvane podatke. \n Da li želite da izbrišete trajno sačuvane podatke i da vratite pristup podacima nazad na virtuelni pristup?
#XMSG: success message for stopping mass persistence
stopMassPersistenceSuccess=Uklanjamo trajno sačuvane podatke za odabrane poglede.
#XMSG: error message during mass stop persistence of a view (backend error)
STOP_MASS_PERSISTENCE_ERROR=Došlo je do greške tokom zaustavljanja trajnosti odabranih pogleda.
#XMSG: memory analysis is only performed in space {0}
NO_MEMORY_ANALYSIS=Analiza memorije se vrši samo za entitete u prostoru „{0}“: „{1}“ „{2}“ su preskočeni.
##XMSG: Message to indicate that Explain Plan cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_EXPLAINPLAN_NOT_FULFILLED=Fajl Objasni plan ne može se generisati za pogled "{0}" jer preduslovi trajnosti nijesu ispunjeni.
#XMSG: Message for Explain Plan generation without partitioning
EXPLAINPLAN_WITHOUT_PARTITIONS=Particije pogleda "{0}" ne uzimaju se u obzir tokom generisanja fajla Objasni plan.
#XFLD: Message to indicate starting of process to generate Explain Plan file
START_EXPLAINPLAN_EXECUTION=Pokretanje procesa generisanja fajla Objasni plan za pogled "{0}".
##XMSG: Message to indicate that Explain Plan was created
EXPLAINPLAN_CREATED=Fajl Plan objašnjenja za pogled „{0}” je generisan. Možete ga prikazati klikom na „Prikaži detalje” ili ga možete prenijeti sa servera ako imate relevantno odobrenje.
##XMSG: Error message during  generation of Explain Plan file
EXPLAINPLAN_ERROR=Greška pri generisanju fajla Objasni plan za pogled "{0}".
##XMSG: Explain Plan file cannot be generated for the view {0}, because too many views are stacked on each other
EXPLAINPLAN_MODEL_TOO_COMPLEX=Fajl Plan objašnjenja se ne može generisati za pogled "{0}". Previše pogleda je naslagano jedan na drugi. Složeni modeli mogu dovesti do grešaka nedostatka memorije i usporiti izvođenje. Preporučuje se da zadržite pogled.

##XMSG: Message to indicate that the space is locked
PERFORMANCE_ANALYSIS_LOCKED_SPACE_ERROR=Nije moguće izvršiti analizu izvođenja za pogled "{0}" jer je prostor "{1}" zaključan.
##XMSG: The performance analysis task was cancelled
PERFORMANCE_ANALYSIS_CANCELED=Analiza izvođenja za pogled "{0}" je otkazana.
##XMSG: The performance analysis failed
PERFORMANCE_ANALYSIS_FAILED=Analiza izvođenja nije uspjela.
##XMSG: The performance analysis is finished
PERFORMANCE_ANALYSIS_FINISHED=Analiza izvođenja za pogled "{0}" je završena. Prikažite rezultat tako što ćete kliknuti na "Prikaži detalje".
##XMSG: The view has input parameter with no default value
INPUT_PARAMETER_WITHOUT_DEFAULT_VALUE=Ovaj pogled se ne može analizirati jer ima parametar bez standardne vrijednosti.
##XMSG: The view is not fully deployed
VIEW_NOT_FULLY_DEPLOYED=Ovaj pogled se ne može analizirati jer nije u potpunosti implementiran.
##XMSG: The view uses limited remote adapters
LIMITED_REMOTE_ADAPTER=Ovaj pogled koristi najmanje jedan udaljeni adapter sa ograničenim sposobnostima kao što je primjena filtera ili podrška za "Brojanje" koji nedostaju. Zadržavanje ili replikacija objekata može skratiti vrijeme izvođenja.
##XMSG: The view uses remote adapter that does not support 'Limit'
REMOTE_ADAPTER_NOT_SUPPORTING_LIMIT=Ovaj pogled koristi najmanje jedan udaljeni adapter koji ne podržava "Ograničenje". Možda je odabrano više od 1000 zapisa.
##XMSG: Performance analysis is executed by using default parameters of view'
INPUT_PARAMETER_WITH_DEFAULT_VALUE=Analiza izvođenja se vrši korišćenjem standardnih vrijednosti parametara pogleda.
##XMSG: Message to indicate that an error occurred
PERFORMANCE_ANALYSIS_ERROR=Greška u toku analize učinka za pogled "{0}".
##XMSG: Message start of cancellation for Performance Analysis task
CANCEL_PERFORMANCE_ANALYSIS_TRIGGERED=Podnijet je proces za zaustavljanje zadatka analize izvođenja {0} za pogled "{1}". Moguće je kašnjenje dok se zadatak ne zaustavi.
##XMSG: Message start to indicate that task is not active
CANCEL_PERFORMANCE_ANALYSIS_NO_ACTIVE_TASK=Zadatak analize izvođenja {0} za pogled "{1}" nije aktivan.
#XMSG: Cancel Performance Analysis task failure
CANCEL_PERFORMANCE_ANALYSIS_FAILED=Otkazivanje zadatka analize izvođenja nije uspjelo.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Dodijeli mi plan
#XBUT: Pause schedule menu label
pauseScheduleLabel=Pauziraj plan
#XBUT: Resume schedule menu label
resumeScheduleLabel=Nastavi plan
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Greška pri uklanjanju planova.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Greška pri dodijeli planova.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Greška pri pauziranju planova.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Greška pri nastavljanju planova.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Brisanje {0} planova
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Promjena vlasnika {0} planova
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Pauziranje {0} planova
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Nastavljanje {0} planova
#XBUT: Select Columns Button
selectColumnsBtn=Odaberi kolone
#XFLD: Refresh tooltip
TEXT_REFRESH=Osvježi
#XFLD: Select Columns tooltip
text_selectColumns=Odaberi kolone


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Runtime Metrics ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Label for Runtime Metrics
RuntimeMetricsTitle=Metrika vremena izvođenja za
#XFLD : Label for Run Button
runButton=Izvedi
#XFLD : Label for Cancel Button
cancelButton=Odustani
#XFLD : Label for Close Button
closeButton=Zatvori
#XFLD : Label for Open View Analyzer Button
openViewAnalyzerButton=Otvori analizator pogleda
#XFLD : Label for Generate Explain Plan Button
generateExplainPlanButton=Generiši plan objašnjenja
#XFLD : Label for Previous Run Column
previousRun=Prethodno izvođenje
#XFLD : Label for Latest Run Column
latestRun=Najnovije izvođenje
#XFLD : Label for time Column
time=Vrijeme
#XFLD : Label for Duration Column
duration=Trajanje
#XFLD : Label for Peak Memory Column
peakMemory=Maksimum memorije
#XFLD : Label for Number of Rows
numberOfRows=Broj redova
#XFLD : Label for Overall Number of Sources
overallNumberOfSources=Ukupni broj izvora
#XFLD : Label for Data Access Column
dataAccess=Pristup podacima
#XFLD : Label for Local Tables
localTables=Lokalne tabele
#XFLD : Label for federatedRemoteTables
federatedRemoteTables=Povezane udaljene tabele (sa ograničenim mogućnostima adaptera)
#XTXT Text for initial state of the runtime metrics
initialState=Prvo morate da izvedete analizu učinka da bi se pozvala metrika. To može potrajati, ali po potrebi možete otkazati ovaj proces.
#XMSG: Cancel confirmation message
CancelRunConfirmationMessage=Da li zaista želite da otkažete trenutno izvođenje analize učinka?
#XTIT: Cancel dialog title
CancelRunTitle=Otkaži izvođenje
#XFLD: Label for Number of Rows
NUMBER_ROWS=Broj redova
#XFLD: Label for Overall Number of Sources
OVERALL_NUMBER_SOURCES=Ukupni broj izvora
#XFLD: Label for Data Access
DATA_ACCESS=Pristup podacima
#XFLD: Label for Remote tables
NUMBER_REMOTE_TABLES_LIMITED_ADAPTERS=Povezane udaljene tabele (sa ograničenim mogućnostima adaptera)
#XFLD: Label for select statement
SELECT_STATEMENT="SELECT * FROM VIEW LIMIT 1000"
#XFLD: Label for duration
SELECT_RUNTIME=Trajanje
#XFLD: Label for peak memory
SELECT_PEAK_MEMORY=Maksimum memorije
#XFLD: Label for count statement
SELECT_COUNT_STATEMENT="SELECT COUNT(*) FROM VIEW"
#XFLD: Label for count duration
SELECT_COUNT_RUNTIME=Trajanje
#XFLD: Label for count peak memory
SELECT_COUNT_PEAK_MEMORY=Maksimum memorije
#XFLD: Label for count of number of local tables
NUMBER_LOCAL_TABLES_FILE=Lokalne tabele (fajl)
#XTXT: Text for running state of the runtime metrics
Running=Izvodi se...
#XFLD: Label for time
Time=Vrijeme
#XFLD: Label for virtual access
PA_VIRTUAL=Virtuelno
#XFLD: Label for persisted access
PA_PERSISTED=Trajno sačuvano
PA_PARTIALLY_PERSISTED=Djelimično trajno sačuvano
#XTXT: Text for cancel
CancelRunSuccessMessage=Otkazivanje izvođenja analize učinka.
#XTXT: Text for cancel error
CancelRunErrorMessage=Greška pri otkazivanju izvođenja analize učinka.
#XTXT: Text for explain plan generation
ExplainPlanStarted=Generiše se Objasni plan za pogled "{0}".
#XTXT: Text for performance analysis
PerformanceAnalysisStarted=Početak analize učinka za pogled "{0}".
#XTXT: Text for performance analysis error
ErrorFetchingPerformanceAnalysis=Greška pri pozivanju podataka analize učinka.
#XTXT: Text for performance analysis error
conflictingTask=Zadatak analize učinka se već izvodi
#XFLD: Label for Errors
Errors=Greške
#XFLD: Label for Warnings
Warnings=Upozorenje/a
#XFLD: Tooltip for missing privileges
openViewAnalyzerButtonTooltipNoPrivilege=Potrebno vam je ovlašćenje DWC_DATAINTEGRATION(ažuriranje) za otvaranje analizatora pogleda.
#XFLD: Tooltip for missing privileges
generateExplainPlanButtonTooltipNoPrivilege=Potrebno vam je ovlašćenje DWC_RUNTIME(čitanje) za generisanje Objasni plan.



#XFLD: Label for frequency column
everyLabel=Svakih
#XFLD: Plural Recurrence text for Hour
hoursLabel=Sati
#XFLD: Plural Recurrence text for Day
daysLabel=Dani
#XFLD: Plural Recurrence text for Month
monthsLabel=Mjeseci
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minuti
