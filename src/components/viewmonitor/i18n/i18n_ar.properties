
#XBCB: Breadcrumb
viewMonitorBreadcrumb={spaceId}

#XFLD: Label for persisted view column
SOURCE=المصدر
#XFLD: Label for persisted view column
NAME=الاسم
#XFLD: Label for persisted view column
NAME_LABEL=الاسم التجاري
#XFLD: Label for persisted view column
NAME_LABELNew=الكائن (الاسم التجاري)
#XFLD: Label for persisted view column
TECHINCAL_NAME=الاسم التقني
#XFLD: Label for persisted view column
TECHINCAL_NAMENew=الكائن (الاسم التقني)
#XFLD: Label for persisted view column
DATA_PERSISTENCY=الوصول إلى البيانات
#XFLD: Label for persisted view column
STATUS=الحالة
#XFLD: Label for persisted view column
LAST_UPDATED=آخر تحديث
#XFLD: Label for persisted view column
IN_MEMORY_SIZE=الذاكرة المستخدمة للتخزين (ميبي بايت)
#XFLD: Label for persisted view column
DISK_SIZE=القرص المستخدم للتخزين (ميبي بايت)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_NEWW=الحجم في الذاكرة (ميبي بايت)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_HEADER=الحجم في الذاكرة
#XFLD: Label for persisted view column
DISK_SIZE_NEWW=الحجم على القرص (ميبي بايت)
#XFLD: Label for persisted view column
DISK_SIZE_HEADER=الحجم على القرص
#XFLD: Label for schedule owner column
txtScheduleOwner=مالك الجدول الزمني
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=إظهار الشخص الذي أنشأ الجدول الزمني
#XFLD: Label for persisted view column
PERSISTED=مخزَّن بصفة دائمة
#XFLD: Label for persisted view column
TYPE=النوع
#XFLD: Label for View Selection Dialog column
changedOn=تاريخ التغيير
#XFLD: Label for View Selection Dialog column
createdBy=المنشئ
#XFLD: Label for log details column
txtViewPersistencyLogs=عرض السجلات
#XFLD: Label for log details column
txtViewPersistencyLogsNew=التفاصيل
#XFLD: text for values shown for Ascending sort order
SortInAsc=ترتيب تصاعدي
#XFLD: text for values shown for Descending sort order
SortInDesc=ترتيب تنازلي
#XFLD: Label for persisted view monitor heading
ViewMonitor_Heading=مراقب طريقة العرض
#XFLD: Label for persisted view monitor sub-heading
ViewMonitor_SubHeading=مراقبة ومعالجة التخزين الدائم للبيانات لطرق العرض


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ DynamicPageHeader ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: text for values shown in column Persistence Status
txtLoading=تحميل
#XFLD: text for values shown in column Persistence Status
txtRunning=قيد التشغيل
#XFLD: text for values shown in column Persistence Status
txtAvailable=متوفر
#XFLD: text for values shown in column Persistence Status
txtError=خطأ
#XMSG: Error message for unknown Persistence type that gets logged in Kibana
unknownReplicationTypeError=نوع النسخ المتماثل "{0}" غير مدعوم.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Content ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=الإعدادات المستخدمة لآخر تشغيل للتخزين الدائم للبيانات:
#XMSG: Message for input parameter name
inputParameterLabel=معامل الإدخال
#XMSG: Message for input parameter value
inputParameterValueLabel=القيمة
#XMSG: Message for persisted data
inputParameterPersistedLabel=تم التخزين الدائم للبيانات في
#XTIT: Count for View in ViewMonitor Toolbar
txtViewCount=طرق العرض ({0})
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=التخزين الدائم للعرض
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=التخزين الدائم للبيانات
#XBUT: Button to clear the selected view persistance
ClearViewPersistance=مسح
#XBUT: Button to stop the selected view persistance
stopPersistance=إيقاف التخزين الدائم
#XFLD: Placeholder for Search field
txtSearch=بحث
#XBUT: Tooltip for refresh button
txtRefresh=تحديث
#XBUT: Tooltip for add view button
txtDeleteView=حذف التخزين الدائم للبيانات
#XBUT: Tooltip for load new peristence
loadNewPersistence=إعادة تشغيل التخزين الدائم للبيانات
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=تحميل لقطة جديدة
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=بدء التخزين الدائم للبيانات
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=إزالة البيانات المخزنة بصفة دائمة
#XMSG: success message for starting persistence
startPersistenceSuccess=نقوم بتخزين طريقة العرض "{0}" بصفة دائمة.
#XMSG: success message for stopping persistence
stopPersistenceSuccess=إننا نزيل البيانات المخزنة دائمًا لطريقة العرض "{0}".
#XMSG: success message for stopping persistence
deletePersistedViewSuccess=نقوم بإزالة طريقة العرض "{0}" من قائمة المراقبة.
#XMSG: error message during start persistence of a view (backend error)
ERROR_STARTING_PERSISTENCE=حدث خطأ أثناء بدء التخزين الدائم للبيانات لطريقة العرض "{0}".
#XMSG: error message during start persistence of a view (backend error)
PARAMERROR_STARTING_PERSISTENCE=لا يمكن التخزين الدائم لبيانات طريقة العرض "{0}" لأنها تحتوي على معامِلات إدخال.
#XMSG: error message during start persistence of a view (backend error)
NUMBER_OF_PARAMETER_STARTING_PERSISTENCE=لا يمكن التخزين الدائم لبيانات طريقة العرض "{0}" لأنها تحتوي على أكثر من معامِل إدخال.
#XMSG: error message during start persistence of a view (backend error)
PARAMETER_DEFAULT_VALUE_STARTING_PERSISTENCE=لا يمكن التخزين الدائم لبيانات طريقة العرض "{0}" لأن معامل الإدخال لا يحتوي على قيمة افتراضية.
#XMSG: error message during start persistence of a view (backend error)
REDEPLOYMENT_DAC_REQUIRED=مطلوب إعادة نشر عنصر التحكم في الوصول إلى البيانات (DAC) "{0}" لدعم التخزين الدائم للبيانات.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_STARTING_PERSISTENCE=لا يمكن التخزين الدائم لطريقة العرض "{0}" نظرًا لأنه يستخدم طريقة العرض "{1}" والتي بها تحكم في الوصول إلى البيانات (DAC).
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_EXTERNAL_STARTING_PERSISTENCE=لا يمكن التخزين الدائم لطريقة العرض "{0}" نظرًا لأنه يستخدم طريقة عرض بها تحكم في الوصول إلى البيانات (DAC) ينتمي إلى مساحة مختلفة.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_STRUCTURE_STARTING_PERSISTENCE=لا يمكن التخزين الدائم لطريقة العرض "{0}" لأن بنية واحدة أو أكثر من عناصر التحكم في الوصول إلى البيانات (DAC) لا تدعم التخزين الدائم للبيانات.
#XMSG: error message during stop persistence of a view (backend error)
STOP_PERSISTENCE_ERROR=حدث خطأ أثناء إيقاف التخزين الدائم لطريقة العرض "{0}".
#XMSG: error message during stop persistence of a view (backend error)
DELETE_PERSISTED_VIEW_ERROR=حدث خطأ أثناء حذف طريقة العرض المخزنة بصفة دائمة "{0}".
#XMSG: Warning message for removing the persisted data
RemovePersistency_Confirm_Msg=هل تريد حذف البيانات المخزنة بصفة دائمة والتبديل إلى الوصول الافتراضي إلى طريقة العرض "{0}"؟
#XMSG: Warning message for delete the view persistence
DeletePersistency_Confirm_Msg=هل تريد إزالة طريقة العرض من قائمة المراقبة وحذف البيانات المخزنة بصفة دائمة للعرض "{0}"؟
#XMSG: error message for reading data from backend
txtReadBackendError=يبدو أن هناك خطأ أثناء القراءة من النظام الخلفي.
#XFLD: Label for No Data Error
NoDataError=خطأ
#XMSG: message for conflicting task
Task_Already_Running=توجد مهمة متعارضة قيد التشغيل بالفعل لطريقة العرض"{0}".

#XFLD: Error while accessing pertitioning for a view
missingPrivilegeInViewPersistency=إذن غير كافٍ لتنفيذ التقسيم لطريقة العرض "{0}"

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Show All Views~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Text for show all views button
allViewsText=جميع طرق العرض ({0})
#XBUT: Text for show scheduled views button
scheduledText=مجدوَل  ({0})
#XBUT: Text for show persisted views button
persistedText=مُخزَّن بصفة دائمة ({0})
#XBUT: Text for start analyzer button
startAnalyzer=بدء محلل العرض
#XFLD: Message if repository is unavailable
repositoryErrorMsg=المستودع غير متوفر وتم تعطيل ميزات معينة.

#XFLD: Data Access - Virtual
Virtual=افتراضي
#XFLD: Data Access - Persisted
Persisted=مخزَّن بصفة دائمة

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Non-Persisted View Selection Dialog~~~~~~~~~~~~~~~~~~~~~~~

#XTIT: Title for Non-Persisted View selection dialog
select_source=تحديد عرض لتخزينه بصفة دائمة

#XTIT: Placeholder for Non-Persisted View selection dialog search
searchView=بحث عن طرق العرض
#XTIT: No data in the list of non-persisted view
No_Data=لا توجد بيانات
#XBUT: Button to select non-persisted view
ok=موافق
#XBUT: Button to close the non-persisted views selection dialog
cancel=إلغاء

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~View persistency log message key~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message for view persistency logs
START_STOP_PERSISTENCY=جارٍ بدء تشغيل مهمة التخزين الدائم للبيانات "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY=جارٍ التخزين الدائم لبيانات طريقة العرض "{1}".
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY)
START_VIEW_PERSISTENCY_PROCESS=جارٍ بدء عملية التخزين الدائم لبيانات طريقة العرض "{0}".
#XFLD: Message for view persistency manual partition load
START_VIEW_PERSISTENCY_PROCESS_MANUAL_PARTITIONS=جارٍ بدء عملية التخزين الدائم لبيانات طريقة العرض "{0}" بمعرفات تقسيم محددة: "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY=جارٍ إزالة البيانات المخزنة بصفة دائمة لطريقة العرض "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY)
REMOVE_VIEW_PERSISTENCY=جارٍ بدء عملية إزالة البيانات المخزنة بصفة دائمة لطريقة العرض "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_SUCCESS=تم تخزين بيانات طريقة العرض "{1}" بصفة دائمة.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_SUCCESS)
START_VIEW_PERSISTENCY_PROCESS_SUCCESS=تم تخزين بيانات طريقة العرض "{0}" بصفة دائمة.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY_SUCCESS=تمت إزالة البيانات المخزنة بصفة دائمة وتمت استعادة الوصول الافتراضي إلى البيانات لطريقة العرض "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_SUCCESS)
REMOVE_VIEW_PERSISTENCY_SUCCESS=اكتملت عملية إزالة البيانات المخزنة بصفة دائمة لطريقة العرض "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_FAILED=يتعذر تخزين بيانات طريقة العرض "{1}" بصفة دائمة.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_FAILED)
START_VIEW_PERSISTENCY_PROCESS_FAILED=يتعذر تخزين بيانات طريقة العرض "{0}" بصفة دائمة.
#XFLD: Message for view persistency logs (for messages created before version 2022.05)
STOP_VIEW_PERSISTENCY_FAILED=تتعذر إزالة البيانات المخزنة بصفة دائمة لطريقة العرض "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_FAILED)
REMOVE_VIEW_PERSISTENCY_FAILED=تتعذر إزالة البيانات المخزنة بصفة دائمة لطريقة العرض "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_SUCCESS=تم التخزين الدائم لـ "{3}" من السجلات لطريقة العرض "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace START_PERSISTENCY_RECORD_COUNT_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=تم إدراج {0} من السجلات في جدول التخزين الدائم للبيانات لطريقة العرض "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS_V2=تم إدراج {0} من السجلات في جدول التخزين الدائم لطريقة العرض "{1}". الذاكرة المستخدمة: {2} جيبي بايت.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_SUCCESS=تمت إزالة "{3}" من السجلات المخزنة بصفة دائمة لطريقة العرض "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace STOP_PERSISTENCY_RECORD_COUNT_SUCCESS)
REMOVE_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=تمت إزالة البيانات بصفة دائمة، تم حذف "{0}" من السجلات التي تم تخزينها بصفة دائمة.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_FAILED=فشل استدعاء recordCount لطريقة العرض "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_FAILED=فشل استدعاء recordCount لطريقة العرض "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=تم حذف الجدول لـ "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS)
REMOVE_VIEW_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=تم حذف الجدول لطريقة العرض "{0}".
#XFLD: Message for view persistency logs
STOP_PERSISTENCY_DELETE_SCHEDULE_FAILED=فشل حذف الجدول لـ "{1}".
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_REDEPLOY=لا يمكننا تخزين طريقة العرض "{0}" تخزينًا دائمًا لأنه تم تغييره ونشره منذ إن بدأت في تخزينه تخزينًا دائمًا. حاول مرة أخرى تخزين العرض تخزينًا دائمًا أو الانتظار حتى التشغيل المجدول التالي.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_DELETE=لا يمكننا تخزين طريقة العرض "{0}" تخزينًا دائمًا لأنه تم حذفه منذ إن بدأت في تخزينه تخزينًا دائمًا.
#XFLD: Message for successful data persistency of a single partition (for messages created before version 2022.03)
START_PERSISTENCY_SINGLE_PARTITION_SUCCESS=تم تخزين {0} من السجلات بشكل دائم إلى القسم للقيم "{1}" <= {2} < "{3}".
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (new message to replace START_PERSISTENCY_SINGLE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS=تم إدراج {0} من السجلات بشكل دائم إلى القسم للقيم "{1}" <= "{2}" < "{3}".
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS_V2=تم إدراج {0} من السجلات في القسم للقيم "{1}" <= "{2}" < "{3}". الذاكرة المستخدمة: {4} جيبي بايت.
#XFLD: Message for successful replication of a "others" partition (for messages created before version 2022.03)
START_PERSISTENCY_OTHERS_PARTITION_SUCCESS=تم تخزين {0} من السجلات بشكل دائم إلى قسم "أخرى".
#XFLD: Message used by SEAL repository for successful replication of a "others" partition (new message to replace START_PERSISTENCY_OTHERS_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_OTHERS_PARTITION_SUCCESS=تم إدارج {0} من السجلات إلى قسم "أخرى".
#XFLD: Message for view persistency record count of partitions (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS=تم تخزين {3} من السجلات بشكل دائم لطريقة العرض "{1}" في {4} من الأقسام.
#XFLD: Message used by SEAL repository for view persistency record count of partitions (new message to replace START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS=تم إدراج {0} من السجلات في جدول التخزين الدائم للبيانات لطريقة العرض "{1}" في أقسام {2}.
#XFLD: Message used by SEAL repository for view persistency for partition wise refresh
START_VIEW_PERSISTENCY_RECORD_COUNT_REFRESHED_PARTITIONS_SUCCESS=تم إدراج {0} من السجلات في جدول التخزين الدائم للبيانات لطريقة العرض "{1}". الأقسام المحدَّثة: {2}، الأقسام المؤمَّنة: {3}؛ إجمالي الأقسام: {4}
#XFLD: Message used by SEAL repository for view persistency for manual partition load
START_VIEW_PERSISTENCY_RECORD_COUNT_MANUAL_PARTITIONS_SUCCESS=تم إدراج {0} من السجلات في جدول التخزين الدائم للبيانات لطريقة العرض "{1}" في {2} من الأقسام المحددة
#XFLD: Message used by SEAL repository for view persistency for locked partition data load
START_VIEW_PERSISTENCY_RECORD_COUNT_LOCKED_PARTITIONS_SUCCESS=تم إدراج {0} من السجلات في جدول التخزين الدائم للبيانات لطريقة العرض "{1}". الأقسام المحدَّثة: {2}، الأقسام غير المتغيرة: {3}، إجمالي الأقسام: {4}
#XMSG: Message for unexpected error (for messages created before version 2022.03)
START_PERSISTENCY_UNEXPECTED_ERROR=حدث خطأ غير متوقع أثناء تخزين بيانات طريقة العرض "{0}" بصفة دائمة.
#XMSG: Message for unexpected error (new message to replace START_PERSISTENCY_UNEXPECTED_ERROR)
START_VIEW_PERSISTENCY_UNEXPECTED_ERROR=حدث خطأ غير متوقع أثناء تخزين بيانات طريقة العرض "{0}" بصفة دائمة.
#XMSG: Message for Locked space error
START_VIEW_PERSISTENCY_LOCKED_SPACE_ERROR=طريقة العرض "{0}” لا يمكن تخزينها بشكل دائم لأن المساحة "{1}" مؤمَّنة.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
STOP_PERSISTENCY_UNEXPECTED_ERROR=حدث خطأ غير متوقع أثناء إزالة بيانات طريقة العرض "{0}" المخزنة بصفة دائمة.
#XMSG: Message for unexpected error (new message to replace STOP_PERSISTENCY_UNEXPECTED_ERROR)
REMOVE_VIEW_PERSISTENCY_UNEXPECTED_ERROR=حدث خطأ غير متوقع أثناء إزالة التخزين الدائم للبيانات لطريقة العرض "{0}".
#XMSG: Message for ivalidated view
VIEW_PERSISTENCY_ABORTED_DEFINITION_INVALID=أصبح تعريف طريقة العرض "{0}" غير صالح، ويرجع ذلك على الأرجح إلى تغيير كائن تم استهلاكه بشكل مباشر أو غير مباشر بواسطة طريقة العرض. حاول إعادة نشر طريقة العرض لحل المشكلة أو لتحديد السبب الجذري.
#XMSG: Message for persisted data removal (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT=تمت إزالة البيانات الدائمة أثناء نشر طريقة العرض "{0}".
#XMSG: Message for persisted data removal of a consuming view
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW=تمت إزالة البيانات المخزنة بشكل دائم أثناء نشر طريقة العرض المستهلكة "{0}".
#XMSG: Message for persisted data removal of a consuming view due to a dac change
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW_DAC_CHANGED=تمت إزالة البيانات المخزنة بشكل دائم أثناء نشر طريقة عرض المستهلكة "{0}" لأنه تم تغيير التحكم في الوصول إلى البيانات.
#XMSG: Message for persisted data removal (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DEPLOYMENT=تمت إزالة البيانات المخزنة بشكل دائم أثناء نشر طريقة العرض "{0}".
#XMSG: Message for deleted view (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION=تمت إزالة التخزين الدائم للبيانات مع حذف طريقة العرض "{0}".
#XMSG: Message for deleted view (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DELETION=تمت إزالة التخزين الدائم للبيانات مع حذف طريقة العرض "{0}".
#XMSG Message for removal of persisted data, because prerequisites not fulfilled
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_PREREQUISITES_NOT_FULFILLED=تمت إزالة البيانات المخزنة بشكل دائم لأن المتطلبات الأساسية للتخزين الدائم للبيانات لم تعد مستوفاة.
#XMSG: Message for inconsistent persistency
VIEW_PERSISTENCY_INCONSISTENT_PERSISTENCY=أصبح التخزين الدائم لطريقة العرض "{0}" غير متسق. قم بإزالة البيانات المخزنة بشكل دائم لحل المشكلة.
#XMSG: Message for prerequisites
START_PERSISTENCY_CHECK_PREREQUISITES=التحقق من المتطلبات الأساسية للتخزين الدائم لطريقة العرض "{0}".
#XMSG: Message for DAC deployed in deprecated way using table function and join (for messages created before 2025.05)
START_PERSISTENCY_DEPRECIATED_DAC=تم نشر طريقة العرض "{0}" باستخدام التحكم في الوصول إلى البيانات (DAC) الذي يتم إهلاكه. يُرجى نشر طريقة العرض مرة أخرى لتحسين الأداء.
#XMSG: Message for DAC deployed in deprecated way using table function and join
START_PERSISTENCY_DEPRECATED_DAC=تم نشر طريقة العرض "{0}" باستخدام التحكم في الوصول إلى البيانات (DAC) الذي يتم إهلاكه. يُرجى نشر طريقة العرض مرة أخرى لتحسين الأداء.
#XMSG: Message for roll back (for messages created before version 2022.03)
ROLLBACK_VIEW_PERSISTENCY=حدث خطأ. تمت استعادة حالة التخزين الدائم السابقة لطريقة العرض "{0}".
#XMSG: Message used by SEAL repository for rollback of running procedure to persist view (new message to replace ROLLBACK_VIEW_PERSISTENCY)
VIEW_PERSISTENCY_ROLLBACK=حدث خطأ. تم إيقاف عملية التخزين الدائم لبيانات طريقة العرض "{0}" وتم استرجاع التغييرات.
#XMSG: Message used by SEAL repository for rollback of running procedure to remove persisted data of view
REMOVE_VIEW_PERSISTENCY_ROLLBACK=حدث خطأ. تم إيقاف عملية إزالة البيانات المخزنة بصفة دائمة لطريقة العرض "{0}" وتم استرجاع التغييرات.
#XMSG: Message used by SEAL repository for persistency preparation
START_PERSISTENCY_PREPARATION=جارٍ تحضير التخزين الدائم للبيانات.
#XMSG: Message used by SEAL repository for data insertion
START_INSERTING_PERSISTENCY_DATA=جارٍ إدراج البيانات في جدول التخزين الدائم.
#XMSG: Message for null values - partitioning (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS=تم إدراج {0} من سجلات القيم الفارغة في القسم "أخرى".
#XMSG: Message used by SEAL repository for others partition values - partitioning (new message to replace START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS=تم إدراج {0} من السجلات في القسم "أخرى" للقيم "{2}" < "{1}" أو "{2}" >= "{3}".
#XMSG: Message used by SEAL repository for others partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS_V2=تم إدراج {0} من السجلات في القسم "أخرى" للقيم "{2}" < "{1}" OR "{2}" >= "{3}". الذاكرة المستخدمة: {4} جيبي بايت.
#XMSG: Message used by SEAL repository for null partition values - partitioning
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS=تم إدراج {0} من السجلات في القسم "أخرى" للقيم من السجلات في القسم "أخرى" للقيم "{1}" IS NULL.
#XMSG: Message used by SEAL repository for null partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS_V2=تم إدراج {0} من السجلات إلى القسم "أخرى" للقيم "{1}" IS NULL. الذاكرة المستخدمة: {2} جيبي بايت.
##XFLD:Message used by SEAL repository for remote statistics
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS=تحميل البيانات المتضمنة: {0} من العبارات البعيدة. إجمالي السجلات التي تم استدعائها: {1}. إجمالي وقت المدة: {2} من الثواني.
##XFLD:Message used by SEAL repository for remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS_WITH_PARTITIONS=تحميل البيانات المتضمنة باستخدام {0} من التقسيمات بـ {1} من العبارات البعيدة. إجمالي السجلات التي تم استدعائها: {2}. إجمالي وقت المدة: {3} من الثواني.
##XFLD:Message used by SEAL repository with UI ID for instructions of remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_INSTRUCTION=يمكن عرض العبارات البعيدة التي تمت معالجتها أثناء التشغيل بفتح مراقب الاستعلامات البعيدة، في تفاصيل الرسائل الخاصة بالتقسيم.
##XFLD:Message for start reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS=جارٍ بدء عملية إعادة استخدام البيانات المُخزنة بشكل دائم الموجودة لطريقة العرض {0} بعد النشر.
##XFLD:Message used by SEAL repository for reattach view persitency
START_VIEW_PERSISTENCY_REATTACHMENT=ابدأ في إعادة استخدام البيانات المُخزنة بشكل دائم الموجودة.
##XFLD:Message used by SEAL repository for reattach view persitency
START_FINILIZING_VIEW_PERSISTENCY_REATTACHMENT=جارٍ إعادة استخدام البيانات المُخزنة بشكل دائم الموجودة.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_COMPLETE=اكتملت عملية إعادة استخدام البيانات المُخزنة بشكل دائم الموجودة لطريقة العرض {0}.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_FAILED=فشلت إعادة استخدام البيانات المُخزنة بشكل دائم الموجودة لطريقة العرض {0} بعد النشر.
#XMSG: Message used by SEAL repository for finalizing persistency
START_FINALIZING_PERSISTENCY=جارٍ إنهاء التخزين الدائم للبيانات.
#XMSG: Message used by SEAL repository for restoring virtual access for view
REMOVE_VIEW_PERSISTENCY_VIRTUAL_ACCESS_RESTORED=تمت استعادة الوصول إلى البيانات الافتراضية لطريقة العرض "{0}".
#XMSG: Message used by SEAL repository for view that already has virtual access
REMOVE_VIEW_PERSISTENCY_ALREADY_VIRTUAL_ACCESS=طريقة العرض "{0}" لديها بالفعل وصول افتراضي إلى البيانات. لم تتم إزالة أي بيانات مخزنة بصفة دائمة.
#XMSG: Message for removing view persistency
VIEW_PERSISTENCY_REMOVED_FROM_MONITOR=تمت إزالة طريقة العرض "{0}" من مراقب طرق العرض.
#XMSG: Message for view doesnt exist.
VIEW_PERSISTENCY_VIEW_NOT_EXISTING=طريقة العرض "{0}" غير موجودة في قاعدة البيانات أو لم يتم نشرها بشكل صحيح، وبالتالي لا يمكن تخزينها تخزينًا دائمًا. حاول إعادة نشر طريقة العرض لحل المشكلة أو التعرف على السبب الجذري.
#XMSG: Message for persistency not enabled.
VIEW_PERSISTENCY_NOT_ENABLED=التخزين الدائم للبيانات غير ممكّن. أعد نشر جدول/طريقة عرض في المساحة "{0}" لتمكين الوظيفة.
#XMSG: Message for unexpected error due to node crash
VIEW_PERSISTENCY_UNEXPECTED_ERROR_GENERAL=تم قطع تشغيل التخزين الدائم للعرض الأخير بسبب أخطاء تقنية.
#XFLD: Message for view persistency memory consumption
VIEW_PERSISTENCY_MEMORY_CONSUMPTION={0} جيبي بايت لأقصى ذاكرة مستخدمة في وقت تشغيل التخزين الدائم للعرض.
#XFLD: Message for view persistency timeout
VIEW_PERSISTENCY_TIMEOUT=التخزين الدائم لطريقة العرض {0} وصل لانتهاء مدة {1} من الساعات.
#XFLD: Message for not started asynchronous view persistency
VIEW_PERSISTENCY_ASYNC_NOT_STARTED=أدى تحميل النظام العالي إلى منع بدء التنفيذ غير المتزامن للتخزين الدائم لطرق العرض. تحقق مما إذا كانت هناك مهام كثيرة قيد التشغيل بالتوازي.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency (for messages created before version 2022.05)
START_VIEW_PERSISTENCY_EXECUTION_METHOD_DROP_AND_RENAME=تم حذف الجدول المخزَّن دائمًا الحالي واستبداله بجدول جديد مخزَّن دائمًا.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_RECREATE_AND_DROP=تم حذف الجدول المخزَّن دائمًا الحالي واستبداله بجدول جديد مخزَّن دائمًا.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_TRUNCATE_AND_COPY=تم تحديث الجدول المخزَّن دائمًا الموجود ببيانات جديدة
##XFLD: Message for missing authorizations for view persistency
VIEW_PERSISTENCY_MISSING_AUTH=التفويضات المفقودة للتخزين الدائم للبيانات.
##XFLD: Message start of persistency cancellation
VIEW_PERSISTENCY_TRIGGER_CANCEL=البدء في إلغاء عملية التخزين الدائم للعرض {0}.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_UNAVAILABLE_TASK_RUN=فشل إلغاء العملية للتخزين الدائم لطريقة العرض لأنه لا توجد مهمة تخزين دائم للبيانات جارية لطريقة العرض {0}.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_NONDETERMINISTIC_TASK_RUN=فشل إلغاء العملية للتخزين الدائم لطريقة العرض لأنه لا توجد مهمة تخزين دائم للبيانات قيد التشغيل لطريقة العرض {0}.
##XFLD: Message for canceling a non running persitency task
VIEW_PERSISTENCY_CANCEL_NON_RUNNING_SELECTED_TASK=فشل إلغاء العملية للتخزين الدائم لطريقة العرض {0} لأنه لا توجد مهمة تخزين دائم للبيانات المحددة قيد التشغيل {1}.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_UNREACHABLE_PERSISTENCY_RUN=فشل إلغاء العملية للتخزين الدائم لطريقة العرض لأنه لم يتم بدء التخزين الدائم للبيانات لطريقة العرض {0} حتى الآن.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_COMPLETED_PERSISTENCY_RUN=فشل إلغاء العملية للتخزين الدائم لطريقة العرض {0} لأنه تم إكمالها بالفعل.
##XFLD: Message for failed cancellation
VIEW_PERSISTENCY_ABORTED_CANCEL_EXECUTION=فشل إلغاء العملية للتخزين الدائم لطريقة العرض {0}.
##XFLD: Message for finalizing persistency cancellation
VIEW_PERSISTENCY_FINALIZE_CANCEL=تم تقديم عملية إيقاف التخزين الدائم للبيانات لطريقة العرض {0}.
##XFLD: Message used in persist task when cancellation is caught
VIEW_PERSISTENCY_CANCELED=تم إيقاف عملية التخزين الدائم لطريقة العرض {0}.
##XFLD: Message used in persist task when cancelation is caught and performed through a cancelation task
VIEW_PERSISTENCY_CANCELED_VIA_CANCEL_TASK=تم إيقاف عملية التخزين الدائم لطريقة العرض {0} عبر مهمة الإلغاء {1}.
##XFLD: Message used in cancel task when the task is triggered by deployment
VIEW_PERSISTENCY_CANCEL_TRIGGERED_BY_DEPLOYMENT=إلغاء عملية التخزين الدائم للبيانات أثناء نشر العرض {0}.
##XFLD: Message used in case the cancelation is retriggered for a running task
VIEW_PERSISTENCY_CANCEL_ATTEMPT_RETRIGGER_CANCEL=تم تقديم مهمة إلغاء سابقة للتخزين الدائم لطريقة العرض {0} بالفعل.
##XFLD: Message used to express the delay that may occur before the persistency task is stopped
VIEW_PERSISTENCY_CANCEL_DELAY_STOPPING_PERSISTENCY=قد يكون هناك تأخير حتى يتم إيقاف مهمة التخزين الدائم للبيانات لطريقة العرض {0}.
##XFLD: Message used to reflect the task log identifier of the current running persistency task
VIEW_PERSISTENCY_CANCEL_CURRENT_RUNNING_TASK=بيانات العرض {0} يتم تخرينها بشكل دائم بالمهمة {1}.
##XFLD:Message used by SEAL repository for locked partitions and DACs
VIEW_PERSISTENCY_LOCKED_PARTITIONS_DACS=قد تكون التفويضات المقدمة من عمليات التحكم في الوصول إلى البيانات (DAC) قد تغيرت ولا يتم أخذها في الاعتبار بواسطة التقسيمات المؤمَّنة. قم بإلغاء تأمين التقسيمات وتحميل لقطة جديدة لتطبيق التغييرات.
##XFLD:Message used by SEAL repository for column structure change
VIEW_PERSISTENCY_COLUMN_STRUCTURE_CHANGE=لقد تغيرت بنية العمود ولم تعد مطابقة للجدول الموجود للتخزين بصفة دائمة. قم بإزالة البيانات المُخزنة بصفة دائمة وابدأ في تخزين جديد بصفة دائمة للبيانات لتحديث جدول التخزين بصفة دائمة لديك.
##XFLD: Message for out of memory
VIEW_PERSISTENCY_HANA_OUT_OF_MEMORY=فشلت المهمة بسبب خطأ نفاد الذاكرة في قاعدة بيانات SAP HANA.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_EXCEPTION_TRANSACTION_ROLLBACK=فشلت المهمة بسبب استثناء داخلي في قاعدة بيانات SAP HANA.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_SQL_EXECUTION_ISSUE=فشلت المهمة بسبب حدوث مشكلة في تنفيذ SQL داخلي في قاعدة بيانات SAP HANA.
##XFLD: Message for HANA out of memory result
VIEW_PERSISTENCY_OUT_OF_MEMORY_RESULT_MESSAGE=سبب حدث نفاد الذاكرة في HANA: {0}
##XFLD: Message for admission control rejection
VIEW_PERSISTENCY_HANA_ADMISSION_CTRL_REJECTION=فشلت المهمة بسبب رفض التحكم بالدخول في SAP HANA.
##XFLD: Message for HANA back pressure / rate limit of HANA client
VIEW_PERSISTENCY_HANA_CLIENT_RATE_LIMIT_REJECTION=فشلت المهمة بسبب وجود اتصالات SAP HANA نشطة كثيرة جدًا.
#XMSG: Message for invalid persisted table name
VIEW_PERSISTENCY_PERSISTED_TABLE_INVALID=حدث خطأ وأصبح الجدول المخزَّن دائمًا غير صالح. لحل المشكلة، يُرجى إزالة البيانات المخزنة دائمًا وقم بتخزين طريقة العرض بصفة دائمة مرة أخرى.
#XMSG: Message for usahe of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION=لا يمكن التخزين الدائم لطريقة العرض؛ نظرًا لأنها تستخدم جدولاً بعيدًا على أساس مصدر بعيد مع تمكين نشر المستخدم. تحقق من أصل بيانات طريقة العرض.
#XMSG: Message for dynamic usage of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION_DYNAMIC=لا يمكن التخزين الدائم لطريقة العرض؛ نظرًا لأنها تستخدم جدولاً بعيدًا على أساس مصدر بعيد مع تمكين نشر المستخدم. قد يتم استهلاك الجدول البعيد ديناميكيًا عبر طريقة عرض برنامج SQL النصي. قد لا يُظهر أصل بيانات العرض الجدول البعيد.
#XMSG: Message for insufficient privileges or consumption of view with DAC via dynamic SQL
VIEW_PERSISTENCY_INSUF_PRIV_OR_DAC_DYN_SQL=قد تكون امتيازاتك غير كافية. افتح معاينة البيانات لمعرفة ما إذا كان لديك الامتيازات المطلوبة. إذا كانت الإجابة بنعم، فإن العرض الثاني المُستهلك عبر برنامج SQL النصي الديناميكي قد يحتوي على عنصر تحكم في الوصول إلى البيانات (DAC) مطبَّق عليه.
#XMSG: deployment of DAC is deprecated and view need to be redeployed to be able to persist data
VIEW_PERSISTENCY_DEPRECATED_DAC=تم نشر طريقة العرض "{0}" باستخدام التحكم في الوصول إلى البيانات (DAC) الذي يتم إهلاكه. يُرجى نشر طريقة العرض مرة أخرى لتكون قادرًا على تخزين بيانات طريقة العرض تخزينًا دائمًا.
##XFLD:Message used by SEAL repository for input parameter
START_VIEW_PERSISTENCY_INPUT_PARAMETER=تم استخدام القيمة الافتراضية "{0}" لمعامل الإدخال "{1}".
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Elastic compute node keys~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
##XFLD:Message used to reflect disablement of ecn replica
ECN_DISABLE_EXISTING_REPLICA=تم تعطيل النسخة المتماثلة لعقدة الحوسبة المرنة.
##XFLD:Message used to reflect recreation of ecn replica
ECN_RECREATE_EXISTING_REPLICA=تمت إعادة إنشاء النسخة المتماثلة لعقدة الحوسبة المرنة.
##XFLD:Message used to reflect re-enablement of ecn replica
ECN_ENABLE_EXISTING_REPLICA=تمت إعادة تمكين النسخة المتماثلة لعقدة الحوسبة المرنة.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Task Schedule Menu key~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message to schedule task
scheduleText=جدولة
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=إنشاء الجدول الزمني
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=تحرير الجدول الزمني
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=حذف الجدول الزمني
#XFLD: Refresh frequency field
refreshFrequency=تكرار التحديث
#XFLD: Refresh frequency field
refreshFrequencyNew=التكرار
#XFLD: Refresh frequency field
refreshFrequencyNewNew=التكرار المجدوَل
#XBUT: label for None
none=لا شيء
#XBUT: label for Real-Time replication state
realtime=الوقت الفعلي
#XFLD: Label for table column
txtNextSchedule=التشغيل التالي
#XFLD: Label for table column
txtNextScheduleNew=التشغيل التالي المجدوَل
#XFLD: Label for table column
txtNumOfRecords=عدد السجلات
#XFLD: Label for scheduled link
scheduledTxt=مجدوَل
#XFLD: LABEL for partially persisted link
partiallyPersisted=تم التخزين الدائم للبيانات جزئيًا
#XFLD: Text for paused text
paused=إيقاف مؤقت

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ View Monitor Tasklog Details Action keys~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message for release lock actionn
releaseLockMsg=إذا استغرق التشغيل وقتًا أطول من المعتاد، فقد يشير ذلك إلى أنه قد فشل وأن الحالة لم يتم تحديثها وفقًا لذلك. \r\n لحل المشكلة، يمكنك تحرير التأمين وتعيين حالته إلى "فشل".
#XFLD: Label for release lock dialog
releaseLockText=تحرير التأمين

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Column header tooltip ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: tooltip for table column
txtViewTooltip=اسم العرض المخزن بصفة دائمة
#XFLD: tooltip for table column
txtViewDataAccessTooltip=هذا يشير إلى توفر طريقة العرض
#XFLD: tooltip for table column
txtViewRefreshFreqTooltip=هذا يشير إلى إذا ما تم تحديد جدول زمني لطريقة العرض
#XFLD: tooltip for table column
txtViewStatusTooltip=استدعاء حالة طريقة العرض المخزنة بصفة دائمة
#XFLD: tooltip for table column
txtViewLatestUpdateTooltip=تقديم معلومات عن موعد آخر تحديث لطريقة العرض المخزنة بصفة دائمة
#XFLD: tooltip for table column
txtViewNextRunTooltip=في حالة تعيين جدول زمني لطريقة العرض، راجِع موعد الجدولة للتشغيل التالي.
#XFLD: tooltip for table column
txtViewNumOfRecordsTooltip=قم بتتبع عدد السجلات.
#XFLD: tooltip for table column
txtViewInMemorySizeTooltip=تتبع مقدار الحجم الذي تستخدمه طريقة العرض في الذاكرة لديك
#XFLD: tooltip for table column
txtViewDiskSizeTooltip=تتبع مقدار الحجم الذي تشغله طريقة العرض في القرص لديك
#XMSG: Expired text
txtExpired=منتهي الصلاحية

#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=لا يمكن إضافة الكائن "{0}" إلى سلسلة المهام.

#~~~~~~~~~~~View Analyzer task log messages~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message indicating number of records for a view, and memory consumption in Mebibyte during simulation of view persistency
RECORDS_PEAK_MEMORY=طريقة العرض {0} تحتوي على {1} من السجلات. محاكاة التخزين الدائم للبيانات لطريقة العرض المستخدمة {2} ميبي بايت للذاكرة.
#XMSG: View Analyzer execution failure
VIEW_ANALYZER_EXECUTION_FAILED=فشل عرض تنفيذ المحلل.
#XMSG: Missing authorizations for View Analyzer
VIEW_ANALYZER_MISSING_AUTH=التفويضات المفقودة لعرض المحلل.
#XMSG: Maximum memory in Gibibyte is reached, therefore persistency simulation is stopped
MAXIMUM_MEMORY_REACHED=تم الوصول إلى الحد الأقصى لاستهلاك الذاكرة البالغ {0} جيبي بايت أثناء محاكاة التخزين الدائم للبيانات لطريقة العرض "{1}". وبالتالي، لن يتم تشغيل أي عمليات محاكاة للتخزين الدائم للبيانات لطريقة العرض.
#XMSG: Error during persistency simulation occurred
PERSISTENCY_SIMULATION_ERROR=حدث خطأ أثناء محاكاة التخزين الدائم للبيانات لطريقة العرض "{0}".
#XMSG: Persistency simulation is not executed, because prerequisites are not fulfilled and the view cannot be persisted
PREREQUISITES_NOT_FULFILLED=لم يتم تنفيذ محاكاة التخزين الدائم للبيانات لطريقة العرض "{0}"، لأنه لم يتم استيفاء المتطلبات الأساسية ولا يمكن الاستمرار في العرض بشكل دائم.
#XMSG: Persistency simulation is not executed, because the view does not exist in the database
VIEW_DOES_NOT_EXIST=يجب عليك نشر طريقة العرض "{0}" لتمكين محاكاة التخزين الدائم للبيانات.
#XMSG: Number of records cannot be determined, because the local table does not exist in the database
TABLE_DOES_NOT_EXIST=الجدول المحلي "{0}" غير موجود في قاعدة البيانات، وبالتالي لا يمكن تحديد عدد السجلات لهذا الجدول.
##XMSG: Message start of cancellation for View Analyzer task
CANCEL_VIEW_ANALYZER_TRIGGERED=تم تقديم عملية لإيقاف عرض مهمة المحلل {0} لطريقة العرض "{1}". قد يكون هناك تأخير حتى يتم إيقاف المهمة.
##XMSG: Message start to indicate that task is not active
CANCEL_VIEW_ANALYZER_NO_ACTIVE_TASK=عرض مهمة المحلل {0} لطريقة العرض "{1}" غير نشط.
#XMSG: Cancel View Analyzer task failure
CANCEL_VIEW_ANALYZER_FAILED=فشل إلغاء عرض مهمة المحلل.
##XMSG: Message used in View Analyzer execution task when task is canceled
VIEW_ANALYZER_CANCELED=تم إيقاف عرض تنفيذ المحلل لطريقة العرض "{0}" من خلال مهمة الإلغاء.
##XMSG: Message start of cancelation for Model Validation task
CANCEL_MODEL_VALIDATION_TRIGGERED=تم تقديم عملية إيقاف مهمة التحقق من صحة النموذج {0} لطريقة العرض "{1}". قد يكون هناك تأخير حتى يتم إيقاف المهمة.
##XMSG: Message to indicate that task is not active
CANCEL_MODEL_VALIDATION_NO_ACTIVE_TASK=مهمة التحقق من صحة النموذج {0} لطريقة العرض "{1}" غير نشطة.
#XMSG: Cancel Model Validation task failure
CANCEL_MODEL_VALIDATION_FAILED=فشل إلغاء مهمة التحقق من صحة النموذج.
##XMSG: Message used in Model Validation task when task is canceled
MODEL_VALIDATION_CANCELED=تم إيقاف تنفيذ التحقق من صحة النموذج لطريقة العرض "{0}" من خلال مهمة الإلغاء.
#XMSG: Message for Locked space error, before starting Model Validation task
MODEL_VALIDATION_LOCKED_SPACE_ERROR=لا يمكن تنفيذ التحقق من صحة النموذج لطريقة العرض "{0}"، لأن المساحة "{1}" مؤمَّنة.
##XMSG: Message used in View Analyzer execution task when determination of number of rows fails
NUMBER_ROWS_DETERMINATION_FAILED=حدث خطأ أثناء تحديد عدد الصفوف للجدول المحلي "{0}".
##XMSG: Message to indicate that SQL plan was created
EXECUTION_PLAN_CREATED=يتم إنشاء ملف خطة محلل SQL لطريقة العرض "{0}" ويمكن تنزيله.
#XFLD: Message to indicate starting of process to generate SQL Analyzer plan file
START_PLANVIZ_EXECUTION=جارٍ بدء عملية إنشاء ملف خطة محلل SQL لطريقة العرض "{0}".
#XFLD: Message to indicate starting of View Analyzer process
VIEW_ANALYZER_EXECUTION_STARTED=جار بدء تنفيذ محلل العرض.
##XMSG: Message to indicate that SQL plan file cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_PLANVIZ_NOT_FULFILLED=لا يمكن إنشاء ملف خطة محلل SQL لطريقة العرض "{0}"، لأنه لم يتم استيفاء المتطلبات الأساسية للتخزين الدائم للبيانات.
##XMSG: Error message during  generation of SQL Analyzer plan file
PLANVIZ_ERROR=حدث خطأ أثناء إنشاء ملف خطة محلل SQL لطريقة العرض "{0}".
#XMSG: Message for Locked space error
VIEW_ANALYZER_LOCKED_SPACE_ERROR=لا يمكن تنفيذ محلل العرض لطريقة العرض "{0}"، لأن المساحة "{1}" مؤمَّنة.
#XMSG: Message for persistency simulation without partitioning
PERSISTENCY_SIMULATION_WITHOUT_PARTITIONS=لم تتم مراعاة أقسام طريقة العرض "{0}" أثناء محاكاة التخزين الدائم للبيانات لطريقة العرض.
#XMSG: Message for generation of SQL Analyzer plan file without considering partitioning
PLANVIZ_WITHOUT_PARTITIONS=لم تتم مراعاة أقسام طريقة العرض "{0}" أثناء إنشاء ملف خطة محلل SQL.
#XMSG: Confirmation message for mass removal of persisted data
MassRemovePersistency_Confirm_Msg=هل تريد إزالة البيانات المُخزنة بشكل دائم وتبديل الوصول إلى البيانات مرة أخرى إلى الوصول الافتراضي؟
#XMSG: Confirmation message for partial mass removal of selected persisted data
PartialMassRemovePersistency_Confirm_Msg={0} من أصل {1} من طرق العرض المحددة تحتوي على بيانات مُخزنة بشكل دائم. \n هل تريد إزالة البيانات المُخزنة بشكل دائم وتبديل الوصول إلى البيانات مرة أخرى إلى الوصول الافتراضي؟
#XMSG: success message for stopping mass persistence
stopMassPersistenceSuccess=سنزيل البيانات المُخزنة بشكل دائم لطرق العرض المحددة.
#XMSG: error message during mass stop persistence of a view (backend error)
STOP_MASS_PERSISTENCE_ERROR=حدث خطأ أثناء إيقاف التخزين الدائم للبيانات لطرق العرض المحددة.
#XMSG: memory analysis is only performed in space {0}
NO_MEMORY_ANALYSIS=تم إجراء تحليل الذاكرة للكيانات في المساحة "{0}" فقط: تم تخطي "{1}" "{2}".
##XMSG: Message to indicate that Explain Plan cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_EXPLAINPLAN_NOT_FULFILLED=لا يمكن إنشاء ملف شرح الخطة لطريقة العرض ''{0}''، لأنه لم يتم استيفاء المتطلبات الأساسية للتخزين الدائم.
#XMSG: Message for Explain Plan generation without partitioning
EXPLAINPLAN_WITHOUT_PARTITIONS=لا تتم مراعاة أقسام طريقة العرض ''{0}'' أثناء إنشاء ملف شرح الخطة.
#XFLD: Message to indicate starting of process to generate Explain Plan file
START_EXPLAINPLAN_EXECUTION=جارٍ بدء عملية إنشاء ملف ''شرح الخطة'' للعرض ''{0}''.
##XMSG: Message to indicate that Explain Plan was created
EXPLAINPLAN_CREATED=تم إنشاء شرح ملف الخطة لطريقة العرض "{0}". يمكنك عرضه بالنقر فوق "عرض التفاصيل"، أو تنزيله إذا كان لديك الإذن ذا الصلة.
##XMSG: Error message during  generation of Explain Plan file
EXPLAINPLAN_ERROR=حدث خطأ أثناء إنشاء ملف شرح الخطة لطريقة العرض ''{0}''.
##XMSG: Explain Plan file cannot be generated for the view {0}, because too many views are stacked on each other
EXPLAINPLAN_MODEL_TOO_COMPLEX=لا يمكن إنشاء ملف شرح الخطة لطريقة العرض "{0}". العديد من وجهات النظر مكدسة على بعضها البعض. النماذج المعقدة قد تسبب أخطاء نفاد الذاكرة وبطء الأداء. يوصى بالتخزين الدائم لطريقة العرض.

##XMSG: Message to indicate that the space is locked
PERFORMANCE_ANALYSIS_LOCKED_SPACE_ERROR=لا يمكن تنفيذ تحليل الأداء لطريقة العرض "{0}"، لأن المساحة "{1}" مؤمَّنة.
##XMSG: The performance analysis task was cancelled
PERFORMANCE_ANALYSIS_CANCELED=تم إلغاء تحليل الأداء لطريقة العرض "{0}".
##XMSG: The performance analysis failed
PERFORMANCE_ANALYSIS_FAILED=فشل تحليل الأداء.
##XMSG: The performance analysis is finished
PERFORMANCE_ANALYSIS_FINISHED=انتهى تحليل الأداء لطريقة العرض "{0}". اعرض النتيجة بالنقر فوق "عرض التفاصيل".
##XMSG: The view has input parameter with no default value
INPUT_PARAMETER_WITHOUT_DEFAULT_VALUE=لا يمكن تحليل هذا العرض لأنه يحتوي على معامل بدون قيمة افتراضية.
##XMSG: The view is not fully deployed
VIEW_NOT_FULLY_DEPLOYED=لا يمكن تحليل هذا العرض لأنه لم يتم نشره بالكامل.
##XMSG: The view uses limited remote adapters
LIMITED_REMOTE_ADAPTER=يستخدم هذا العرض محوِّلاً بعيدًا واحدًا على الأقل بإمكانات محدودة مثل الضغط لأسفل على عامل تصفية مفقود أو دعم 'العدّ'. ويمكن أن يؤدي التخزين الدائم للكائنات أو نسخها نسخًا متماثلاً إلى تحسين أداء وقت التشغيل.
##XMSG: The view uses remote adapter that does not support 'Limit'
REMOTE_ADAPTER_NOT_SUPPORTING_LIMIT=يستخدم هذا العرض محوِّل بعيد واحد على الأقل لا يدعم "الحد". ربما تم تحديد أكثر من 1000 سجل.
##XMSG: Performance analysis is executed by using default parameters of view'
INPUT_PARAMETER_WITH_DEFAULT_VALUE=يتم تنفيذ تحليل الأداء باستخدام القيم الافتراضية لمعامِلات العرض.
##XMSG: Message to indicate that an error occurred
PERFORMANCE_ANALYSIS_ERROR=حدث خطأ أثناء تحليل الأداء للعرض "{0}".
##XMSG: Message start of cancellation for Performance Analysis task
CANCEL_PERFORMANCE_ANALYSIS_TRIGGERED=تم تقديم عملية لإيقاف مهمة تحليل الأداء {0} للعرض "{1}". قد يكون هناك تأخير حتى يتم إيقاف المهمة.
##XMSG: Message start to indicate that task is not active
CANCEL_PERFORMANCE_ANALYSIS_NO_ACTIVE_TASK=مهمة تحليل الأداء {0} لطريقة العرض "{1}" غير نشطة.
#XMSG: Cancel Performance Analysis task failure
CANCEL_PERFORMANCE_ANALYSIS_FAILED=فشل إلغاء مهمة تحليل الأداء.

#XBUT: Assign schedule menu button label
assignScheduleLabel=تعيين الجدول لي
#XBUT: Pause schedule menu label
pauseScheduleLabel=إيقاف مؤقت للجدول
#XBUT: Resume schedule menu label
resumeScheduleLabel=استئناف الجدول
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=حدث خطأ أثناء إزالة الجداول.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=حدث خطأ أثناء تعيين الجداول.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=حدث خطأ أثناء الإيقاف المؤقت للجداول.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=حدث خطأ أثناء استئناف الجداول.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=جارٍ حذف {0} من الجداول
#XMSG: Message for starting mass assign of schedules
massAssignStarted=جارٍ تغيير مالك {0} من الجداول
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=جارٍ الإيقاف المؤقت لعدد {0} من الجداول
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=جارٍ استئناف {0} من الجداول
#XBUT: Select Columns Button
selectColumnsBtn=تحديد الأعمدة
#XFLD: Refresh tooltip
TEXT_REFRESH=تحديث
#XFLD: Select Columns tooltip
text_selectColumns=تحديد الأعمدة


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Runtime Metrics ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Label for Runtime Metrics
RuntimeMetricsTitle=قياسات وقت تشغيل
#XFLD : Label for Run Button
runButton=تشغيل
#XFLD : Label for Cancel Button
cancelButton=إلغاء
#XFLD : Label for Close Button
closeButton=إغلاق
#XFLD : Label for Open View Analyzer Button
openViewAnalyzerButton=فتح محلل العرض
#XFLD : Label for Generate Explain Plan Button
generateExplainPlanButton=إنشاء "خطة توضيح"
#XFLD : Label for Previous Run Column
previousRun=التشغيل السابق
#XFLD : Label for Latest Run Column
latestRun=آخر تشغيل
#XFLD : Label for time Column
time=الوقت
#XFLD : Label for Duration Column
duration=المدة
#XFLD : Label for Peak Memory Column
peakMemory=أقصى ذاكرة
#XFLD : Label for Number of Rows
numberOfRows=عدد الصفوف
#XFLD : Label for Overall Number of Sources
overallNumberOfSources=العدد الكلي للمصادر
#XFLD : Label for Data Access Column
dataAccess=الوصول إلى البيانات
#XFLD : Label for Local Tables
localTables=الجداول المحلية
#XFLD : Label for federatedRemoteTables
federatedRemoteTables=الجداول البعيدة الموحدة (بإمكانات محدودة للمحوِّل)
#XTXT Text for initial state of the runtime metrics
initialState=يجب أولاً تشغيل "تحليل الأداء" للحصول على القياسات. وقد يستغرق هذا بعض الوقت، ولكن يمكنك إلغاء العملية إذا لزم الأمر.
#XMSG: Cancel confirmation message
CancelRunConfirmationMessage=هل تريد بالتأكيد إلغاء التشغيل الحالي "لتحليل الأداء"؟
#XTIT: Cancel dialog title
CancelRunTitle=إلغاء التشغيل
#XFLD: Label for Number of Rows
NUMBER_ROWS=عدد الصفوف
#XFLD: Label for Overall Number of Sources
OVERALL_NUMBER_SOURCES=العدد الكلي للمصادر
#XFLD: Label for Data Access
DATA_ACCESS=الوصول إلى البيانات
#XFLD: Label for Remote tables
NUMBER_REMOTE_TABLES_LIMITED_ADAPTERS=الجداول البعيدة الموحدة (بإمكانات محدودة للمحوِّل)
#XFLD: Label for select statement
SELECT_STATEMENT='تحديد * من حد العرض 1000'
#XFLD: Label for duration
SELECT_RUNTIME=المدة
#XFLD: Label for peak memory
SELECT_PEAK_MEMORY=أقصى ذاكرة
#XFLD: Label for count statement
SELECT_COUNT_STATEMENT='تحديد العدد (*) من العرض'
#XFLD: Label for count duration
SELECT_COUNT_RUNTIME=المدة
#XFLD: Label for count peak memory
SELECT_COUNT_PEAK_MEMORY=أقصى ذاكرة
#XFLD: Label for count of number of local tables
NUMBER_LOCAL_TABLES_FILE=الجداول المحلية (الملف)
#XTXT: Text for running state of the runtime metrics
Running=قيد التشغيل...
#XFLD: Label for time
Time=الوقت
#XFLD: Label for virtual access
PA_VIRTUAL=افتراضي
#XFLD: Label for persisted access
PA_PERSISTED=مخزَّن دائمًا
PA_PARTIALLY_PERSISTED=مخزَّن جزئيًا
#XTXT: Text for cancel
CancelRunSuccessMessage=إلغاء تشغيل "تحليل الأداء".
#XTXT: Text for cancel error
CancelRunErrorMessage=حدث خطأ أثناء إلغاء تشغيل "تحليل الأداء".
#XTXT: Text for explain plan generation
ExplainPlanStarted=جارٍ إنشاء "خطة توضيح" للعرض "{0}".
#XTXT: Text for performance analysis
PerformanceAnalysisStarted=جارٍ بدء "تحليل الأداء" للعرض "{0}".
#XTXT: Text for performance analysis error
ErrorFetchingPerformanceAnalysis=حدث خطأ أثناء استدعاء بيانات "تحليل الأداء".
#XTXT: Text for performance analysis error
conflictingTask=مهمة "تحليل الأداء" قيد التشغيل بالفعل
#XFLD: Label for Errors
Errors=خطأ (أخطاء)
#XFLD: Label for Warnings
Warnings=تحذير (تحذيرات)
#XFLD: Tooltip for missing privileges
openViewAnalyzerButtonTooltipNoPrivilege=تحتاج إلى ميزة DWC_DATAINTEGRATION(update) لفتح "محلل العرض".
#XFLD: Tooltip for missing privileges
generateExplainPlanButtonTooltipNoPrivilege=تحتاج إلى ميزة DWC_RUNTIME(read) لإنشاء "خطة التوضيح".



#XFLD: Label for frequency column
everyLabel=كل
#XFLD: Plural Recurrence text for Hour
hoursLabel=ساعات
#XFLD: Plural Recurrence text for Day
daysLabel=أيام
#XFLD: Plural Recurrence text for Month
monthsLabel=شهور
#XFLD: Plural Recurrence text for Minutes
minutesLabel=دقائق
