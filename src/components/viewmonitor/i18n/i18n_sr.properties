
#XBCB: Breadcrumb
viewMonitorBreadcrumb={spaceId}

#XFLD: Label for persisted view column
SOURCE=Извор
#XFLD: Label for persisted view column
NAME=Назив
#XFLD: Label for persisted view column
NAME_LABEL=Пословни назив
#XFLD: Label for persisted view column
NAME_LABELNew=Објекат (пословни назив)
#XFLD: Label for persisted view column
TECHINCAL_NAME=Технички назив
#XFLD: Label for persisted view column
TECHINCAL_NAMENew=Објекат (технички назив)
#XFLD: Label for persisted view column
DATA_PERSISTENCY=Приступ подацима
#XFLD: Label for persisted view column
STATUS=Статус
#XFLD: Label for persisted view column
LAST_UPDATED=Последњи пут ажурирано
#XFLD: Label for persisted view column
IN_MEMORY_SIZE=Меморија коришћена за архивирање (MiB)
#XFLD: Label for persisted view column
DISK_SIZE=Диск коришћен за архивирање (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_NEWW=Величина у меморији (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_HEADER=Величина у меморији
#XFLD: Label for persisted view column
DISK_SIZE_NEWW=Величина на диску (MiB)
#XFLD: Label for persisted view column
DISK_SIZE_HEADER=Величина на диску
#XFLD: Label for schedule owner column
txtScheduleOwner=Одговорно лице за план
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Показује ко је креирао план
#XFLD: Label for persisted view column
PERSISTED=Трајно сачувано
#XFLD: Label for persisted view column
TYPE=Тип
#XFLD: Label for View Selection Dialog column
changedOn=Промењено
#XFLD: Label for View Selection Dialog column
createdBy=Креирао
#XFLD: Label for log details column
txtViewPersistencyLogs=Прикажи протоколе
#XFLD: Label for log details column
txtViewPersistencyLogsNew=Детаљи
#XFLD: text for values shown for Ascending sort order
SortInAsc=Поређај по растућем редоследу
#XFLD: text for values shown for Descending sort order
SortInDesc=Поређај по опадајућем редоследу
#XFLD: Label for persisted view monitor heading
ViewMonitor_Heading=Монитор погледа
#XFLD: Label for persisted view monitor sub-heading
ViewMonitor_SubHeading=Надгледај и одржавај трајност података погледа


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ DynamicPageHeader ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: text for values shown in column Persistence Status
txtLoading=Учитавање
#XFLD: text for values shown in column Persistence Status
txtRunning=Изводи се
#XFLD: text for values shown in column Persistence Status
txtAvailable=Доступно
#XFLD: text for values shown in column Persistence Status
txtError=Грешка
#XMSG: Error message for unknown Persistence type that gets logged in Kibana
unknownReplicationTypeError=Тип репликације "{0}" није подржан.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Content ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Подешавања коришћена за последње извођење трајности података:
#XMSG: Message for input parameter name
inputParameterLabel=Параметар уноса
#XMSG: Message for input parameter value
inputParameterValueLabel=Вредност
#XMSG: Message for persisted data
inputParameterPersistedLabel=Трајно сачувано у
#XTIT: Count for View in ViewMonitor Toolbar
txtViewCount=Погледи ({0})
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Трајност погледа
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Трајност података
#XBUT: Button to clear the selected view persistance
ClearViewPersistance=Поништи
#XBUT: Button to stop the selected view persistance
stopPersistance=Заустави трајност
#XFLD: Placeholder for Search field
txtSearch=Тражи
#XBUT: Tooltip for refresh button
txtRefresh=Освежи
#XBUT: Tooltip for add view button
txtDeleteView=Избриши трајност
#XBUT: Tooltip for load new peristence
loadNewPersistence=Поново покрени трајност
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Учитај нови тренутни снимак
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Покрени трајност података
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Уклони трајно сачуване податке
#XMSG: success message for starting persistence
startPersistenceSuccess=Трајно снимамо поглед "{0}".
#XMSG: success message for stopping persistence
stopPersistenceSuccess=Уклањамо трајно сачуване податке за поглед "{0}".
#XMSG: success message for stopping persistence
deletePersistedViewSuccess=Уклањамо поглед "{0}" са листе надзора.
#XMSG: error message during start persistence of a view (backend error)
ERROR_STARTING_PERSISTENCE=Грешка при покретању трајности података за поглед "{0}".
#XMSG: error message during start persistence of a view (backend error)
PARAMERROR_STARTING_PERSISTENCE=Поглед "{0}" се не може трајно сачувати јер садржи параметре уноса.
#XMSG: error message during start persistence of a view (backend error)
NUMBER_OF_PARAMETER_STARTING_PERSISTENCE=Поглед "{0}" није могуће задржати јер има више од једног параметра уноса.
#XMSG: error message during start persistence of a view (backend error)
PARAMETER_DEFAULT_VALUE_STARTING_PERSISTENCE=Поглед "{0}" није могуће задржати јер параметар уноса нема стандардну вредност.
#XMSG: error message during start persistence of a view (backend error)
REDEPLOYMENT_DAC_REQUIRED=Прерасподела контроле приступа подацима (DAC) "{0}" је обавезна за подршку трајности података.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_STARTING_PERSISTENCE=Поглед "{0}" се не може трајно сачувати јер користи поглед "{1}", који садржи контролу приступа подацима (DAC).
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_EXTERNAL_STARTING_PERSISTENCE=Поглед "{0}" се не може трајно сачувати јер користи поглед с контролом приступа подацима (DAC) који припада другом простору.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_STRUCTURE_STARTING_PERSISTENCE=Поглед "{0}" се не може трајно сачувати јер структура једне или више контрола приступа подацима (DAC) не подржава трајност података.
#XMSG: error message during stop persistence of a view (backend error)
STOP_PERSISTENCE_ERROR=Грешка при заустављању трајности за поглед "{0}".
#XMSG: error message during stop persistence of a view (backend error)
DELETE_PERSISTED_VIEW_ERROR=Грешка при брисању трајно сачуваног погледа "{0}".
#XMSG: Warning message for removing the persisted data
RemovePersistency_Confirm_Msg=Да ли желите да избришете трајно сачуване податке и пребаците на виртуелни приступ погледа "{0}"?
#XMSG: Warning message for delete the view persistence
DeletePersistency_Confirm_Msg=Да ли желите да уклоните поглед са листе надзора и избришете трајно сачуване податке погледа "{0}"?
#XMSG: error message for reading data from backend
txtReadBackendError=Изгледа да је дошло до грешке при читању из back-end-а.
#XFLD: Label for No Data Error
NoDataError=Грешка
#XMSG: message for conflicting task
Task_Already_Running=Задатак с конфликтом се већ изводи за поглед "{0}".

#XFLD: Error while accessing pertitioning for a view
missingPrivilegeInViewPersistency=Недовољна дозвола за извршење партиционисања за поглед "{0}"

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Show All Views~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Text for show all views button
allViewsText=Сви погледи ({0})
#XBUT: Text for show scheduled views button
scheduledText=Планирано ({0})
#XBUT: Text for show persisted views button
persistedText=Трајно сачувано ({0})
#XBUT: Text for start analyzer button
startAnalyzer=Покрени анализатор погледа
#XFLD: Message if repository is unavailable
repositoryErrorMsg=Репозиторијум није доступан и нека својства су деактивирана.

#XFLD: Data Access - Virtual
Virtual=Виртуелно
#XFLD: Data Access - Persisted
Persisted=Трајно сачувано

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Non-Persisted View Selection Dialog~~~~~~~~~~~~~~~~~~~~~~~

#XTIT: Title for Non-Persisted View selection dialog
select_source=Одабери поглед за трајно снимање

#XTIT: Placeholder for Non-Persisted View selection dialog search
searchView=Тражи погледе
#XTIT: No data in the list of non-persisted view
No_Data=Нема података
#XBUT: Button to select non-persisted view
ok=ОК
#XBUT: Button to close the non-persisted views selection dialog
cancel=Одустани

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~View persistency log message key~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message for view persistency logs
START_STOP_PERSISTENCY=Покретање извршења задатка трајности података за "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY=Трајно снимање података за поглед "{1}".
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY)
START_VIEW_PERSISTENCY_PROCESS=Покретање процеса за трајно снимање података за поглед "{0}".
#XFLD: Message for view persistency manual partition load
START_VIEW_PERSISTENCY_PROCESS_MANUAL_PARTITIONS=Покретање процеса за трајно снимање података за поглед "{0}" са ID-овима одабране партиције "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY=Уклањање трајно сачуваних података за поглед "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY)
REMOVE_VIEW_PERSISTENCY=Покретање процеса за уклањање трајно сачуваних података за поглед "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_SUCCESS=Подаци трајно сачувани за поглед "{1}".
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_SUCCESS)
START_VIEW_PERSISTENCY_PROCESS_SUCCESS=Подаци трајно сачувани за поглед "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY_SUCCESS=Трајно сачувани подаци уклоњени и виртуелни приступ подацима обновљен за поглед "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_SUCCESS)
REMOVE_VIEW_PERSISTENCY_SUCCESS=Процес за уклањање трајно сачуваних података за поглед "{0}" је завршен.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_FAILED=Није успело трајно снимање података за поглед "{1}".
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_FAILED)
START_VIEW_PERSISTENCY_PROCESS_FAILED=Није успело трајно снимање података за поглед "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.05)
STOP_VIEW_PERSISTENCY_FAILED=Није успело уклањање трајно сачуваних података за поглед "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_FAILED)
REMOVE_VIEW_PERSISTENCY_FAILED=Није успело уклањање трајно сачуваних података за поглед "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_SUCCESS="{3}" записа трајно сачувано за поглед "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace START_PERSISTENCY_RECORD_COUNT_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS={0} записа унето у табелу трајности података за поглед "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS_V2={0} записа унето у табелу трајности података за поглед "{1}". Коришћена меморија: {2} GiB.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_SUCCESS="{3}" трајно сачуваних записа уклоњено за поглед "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace STOP_PERSISTENCY_RECORD_COUNT_SUCCESS)
REMOVE_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=Трајно сачувани подаци уклоњени, избрисано "{0}" трајно сачуваних записа.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_FAILED=Позивање recordCount није успело за поглед "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_FAILED=Позивање recordCount није успело за поглед "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=План је избрисан за "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS)
REMOVE_VIEW_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=План је избрисан за поглед "{0}".
#XFLD: Message for view persistency logs
STOP_PERSISTENCY_DELETE_SCHEDULE_FAILED=Брисање плана није успело за "{1}".
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_REDEPLOY=Не можемо трајно сачувати поглед "{0}" јер је промењен и имплементиран од кад сте започели да га трајно снимате. Покушајте поново да трајно сачувате поглед или сачекајте до следећег планираног извођења.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_DELETE=Не можемо трајно сачувати поглед "{0}" јер је избрисан од кад сте започели да га трајно снимате.
#XFLD: Message for successful data persistency of a single partition (for messages created before version 2022.03)
START_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} записа трајно сачувано у партицију за вредности "{1}" <= {2} < "{3}".
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (new message to replace START_PERSISTENCY_SINGLE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} записа унето у партицију за вредности "{1}" <= "{2}" < "{3}".
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS_V2={0} записа унето у партицију за вредности "{1}" <= "{2}" < "{3}". Коришћена меморија: {4} GiB.
#XFLD: Message for successful replication of a "others" partition (for messages created before version 2022.03)
START_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} записа трајно сачувано у партицију "други".
#XFLD: Message used by SEAL repository for successful replication of a "others" partition (new message to replace START_PERSISTENCY_OTHERS_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} записа унето у партицију "други".
#XFLD: Message for view persistency record count of partitions (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={3} записа трајно сачувано за поглед "{1}" у {4} партиција.
#XFLD: Message used by SEAL repository for view persistency record count of partitions (new message to replace START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={0} записа унето у табелу трајности података за поглед "{1}" у {2} партиција.
#XFLD: Message used by SEAL repository for view persistency for partition wise refresh
START_VIEW_PERSISTENCY_RECORD_COUNT_REFRESHED_PARTITIONS_SUCCESS={0} записа унето у табелу трајности података за поглед "{1}". Ажуриране партиције: {2} Закључане партиције:{3} Укупни број партиција:{4}
#XFLD: Message used by SEAL repository for view persistency for manual partition load
START_VIEW_PERSISTENCY_RECORD_COUNT_MANUAL_PARTITIONS_SUCCESS={0} записа унето у табелу трајности података за поглед "{1}" у {2} одабраних партиција.
#XFLD: Message used by SEAL repository for view persistency for locked partition data load
START_VIEW_PERSISTENCY_RECORD_COUNT_LOCKED_PARTITIONS_SUCCESS={0} записа унето у табелу трајности података за поглед "{1}". Ажуриране партиције: {2} Закључане непромењене партиције:{3} Укупни број партиција:{4}
#XMSG: Message for unexpected error (for messages created before version 2022.03)
START_PERSISTENCY_UNEXPECTED_ERROR=Неочекивана грешка при трајном снимању података за поглед "{0}".
#XMSG: Message for unexpected error (new message to replace START_PERSISTENCY_UNEXPECTED_ERROR)
START_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Неочекивана грешка при трајном снимању података за поглед "{0}".
#XMSG: Message for Locked space error
START_VIEW_PERSISTENCY_LOCKED_SPACE_ERROR=Поглед "{0}” се не може трајно сачувати јер је простор "{1}" закључан.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
STOP_PERSISTENCY_UNEXPECTED_ERROR=Неочекивана грешка при уклањању трајно сачуваних података за поглед "{0}".
#XMSG: Message for unexpected error (new message to replace STOP_PERSISTENCY_UNEXPECTED_ERROR)
REMOVE_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Неочекивана грешка при уклањању трајности за поглед "{0}".
#XMSG: Message for ivalidated view
VIEW_PERSISTENCY_ABORTED_DEFINITION_INVALID=Дефиниција погледа "{0}" је постала неважећа; највероватније због промене објекта који поглед директно или индиректно користи. Покушајте да поново имплементирате поглед да бисте отклонили овај проблем или пронађите основни узрок.
#XMSG: Message for persisted data removal (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT=Трајно сачувани подаци су уклоњени док се имплементира поглед "{0}".
#XMSG: Message for persisted data removal of a consuming view
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW=Трајно сачувани подаци су уклоњени док се имплементира коришћени поглед "{0}".
#XMSG: Message for persisted data removal of a consuming view due to a dac change
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW_DAC_CHANGED=Трајно сачувани подаци су уклоњени док се имплементира коришћени поглед "{0}" јер је промењена контрола приступа подацима за њих.
#XMSG: Message for persisted data removal (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DEPLOYMENT=Трајно сачувани подаци су уклоњени док се имплементира поглед "{0}".
#XMSG: Message for deleted view (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION=Трајност се уклања брисањем погледа "{0}".
#XMSG: Message for deleted view (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DELETION=Трајност се уклања брисањем погледа "{0}".
#XMSG Message for removal of persisted data, because prerequisites not fulfilled
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_PREREQUISITES_NOT_FULFILLED=Трајно сачувани подаци се уклањају јер предуслови трајности података више нису испуњени.
#XMSG: Message for inconsistent persistency
VIEW_PERSISTENCY_INCONSISTENT_PERSISTENCY=Трајност погледа "{0}" је постала недоследна. Уклоните трајне податке да бисте решили проблем.
#XMSG: Message for prerequisites
START_PERSISTENCY_CHECK_PREREQUISITES=Провера предуслова за трајно снимање погледа "{0}".
#XMSG: Message for DAC deployed in deprecated way using table function and join (for messages created before 2025.05)
START_PERSISTENCY_DEPRECIATED_DAC=Поглед "{0}" је имплементиран помоћу контроле приступа подацима (DAC) која је у процесу застаревања. Имплементирајте поглед поново да бисте унапредили учинак.
#XMSG: Message for DAC deployed in deprecated way using table function and join
START_PERSISTENCY_DEPRECATED_DAC=Поглед "{0}" је имплементиран помоћу контроле приступа подацима (DAC) која је у процесу застаревања. Имплементирајте поглед поново да бисте унапредили учинак.
#XMSG: Message for roll back (for messages created before version 2022.03)
ROLLBACK_VIEW_PERSISTENCY=Дошло је до грешке. Претходни статус трајности обновљен за поглед "{0}".
#XMSG: Message used by SEAL repository for rollback of running procedure to persist view (new message to replace ROLLBACK_VIEW_PERSISTENCY)
VIEW_PERSISTENCY_ROLLBACK=Дошло је до грешке. Процес за трајно снимање погледа "{0}" је прекинут и промене су одбачене.
#XMSG: Message used by SEAL repository for rollback of running procedure to remove persisted data of view
REMOVE_VIEW_PERSISTENCY_ROLLBACK=Дошло је до грешке. Процес за уклањање трајно сачуваних података погледа "{0}" је прекинут и промене су одбачене.
#XMSG: Message used by SEAL repository for persistency preparation
START_PERSISTENCY_PREPARATION=Припрема трајног снимања података.
#XMSG: Message used by SEAL repository for data insertion
START_INSERTING_PERSISTENCY_DATA=Унос података у табелу трајности.
#XMSG: Message for null values - partitioning (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS={0} записа нулте вредности унето у партицију "други".
#XMSG: Message used by SEAL repository for others partition values - partitioning (new message to replace START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS={0} записа унето у партицију "други" за вредности "{2}" < "{1}" ИЛИ "{2}" >= "{3}".
#XMSG: Message used by SEAL repository for others partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS_V2={0} записа унето у партицију "други" за вредности "{2}" < "{1}" ИЛИ "{2}" >= "{3}". Коришћена меморија: {4} GiB.
#XMSG: Message used by SEAL repository for null partition values - partitioning
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS={0} записа унето у партицију "други" за вредности "{1}" IS NULL.
#XMSG: Message used by SEAL repository for null partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS_V2={0} записа унето у партицију "други" за вредности "{1}" IS NULL. Коришћена меморија: {2} GiB.
##XFLD:Message used by SEAL repository for remote statistics
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS=Учитавање укључених података: {0} удаљених наредби. Укупни позвани записи: {1}. Укупно време трајања: {2} секунди.
##XFLD:Message used by SEAL repository for remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS_WITH_PARTITIONS=Учитавање укључених података помоћу {0} партиција са {1} удаљених наредби. Укупни позвани записи: {2}. Укупно време трајања: {3} секунди.
##XFLD:Message used by SEAL repository with UI ID for instructions of remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_INSTRUCTION=Удаљене наредбе обрађене током извођења могу се приказати отварањем монитора удаљених упита у детаљима порука специфичних за партиције.
##XFLD:Message for start reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS=Покретање процеса за поновну употребу постојећих трајно сачуваних података за поглед {0} након имплементације.
##XFLD:Message used by SEAL repository for reattach view persitency
START_VIEW_PERSISTENCY_REATTACHMENT=Започните поновну употребу постојећих трајно сачуваних података.
##XFLD:Message used by SEAL repository for reattach view persitency
START_FINILIZING_VIEW_PERSISTENCY_REATTACHMENT=Поновна употреба постојећих трајно сачуваних података.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_COMPLETE=Процес за поновну употребу постојећих трајно сачуваних података је завршен за поглед {0}.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_FAILED=Није успела поновна употреба постојећих трајно сачуваних података за поглед {0} након имплементације.
#XMSG: Message used by SEAL repository for finalizing persistency
START_FINALIZING_PERSISTENCY=Завршавање трајности.
#XMSG: Message used by SEAL repository for restoring virtual access for view
REMOVE_VIEW_PERSISTENCY_VIRTUAL_ACCESS_RESTORED=Виртуелни приступ подацима обновљен за поглед "{0}".
#XMSG: Message used by SEAL repository for view that already has virtual access
REMOVE_VIEW_PERSISTENCY_ALREADY_VIRTUAL_ACCESS=Поглед "{0}" већ има виртуелни приступ подацима. Трајно сачувани подаци нису уклоњени.
#XMSG: Message for removing view persistency
VIEW_PERSISTENCY_REMOVED_FROM_MONITOR=Поглед "{0}" је уклоњен из монитора погледа.
#XMSG: Message for view doesnt exist.
VIEW_PERSISTENCY_VIEW_NOT_EXISTING=Поглед "{0}" не постоји у бази података или није исправно имплементиран, па се стога не може трајно сачувати. Покушајте да поново имплементирате поглед да бисте решили проблем или идентификовали основни узрок.
#XMSG: Message for persistency not enabled.
VIEW_PERSISTENCY_NOT_ENABLED=Трајност података није активирана. Поново имплементирајте табелу/поглед у простору "{0}" да бисте активирали функционалност.
#XMSG: Message for unexpected error due to node crash
VIEW_PERSISTENCY_UNEXPECTED_ERROR_GENERAL=Последње извођење трајности погледа је прекинуто због техничких грешака.
#XFLD: Message for view persistency memory consumption
VIEW_PERSISTENCY_MEMORY_CONSUMPTION={0} GiB максимума меморије коришћено у времену извођења трајности погледа.
#XFLD: Message for view persistency timeout
VIEW_PERSISTENCY_TIMEOUT=Трајност погледа {0} је достигла истек времена од {1} сати.
#XFLD: Message for not started asynchronous view persistency
VIEW_PERSISTENCY_ASYNC_NOT_STARTED=Високо оптерећење система је спречило покретање асинхроног извршења трајности погледа. Проверите да ли се изводи превише паралелних задатака.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency (for messages created before version 2022.05)
START_VIEW_PERSISTENCY_EXECUTION_METHOD_DROP_AND_RENAME=Постојећа трајно сачувана табела је избрисана и замењена новом трајно сачуваном табелом.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_RECREATE_AND_DROP=Постојећа трајно сачувана табела је избрисана и замењена новом трајно сачуваном табелом.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_TRUNCATE_AND_COPY=Постојећа трајно сачувана табела је ажурирана новим подацима.
##XFLD: Message for missing authorizations for view persistency
VIEW_PERSISTENCY_MISSING_AUTH=Недостају овлашћења за трајност података.
##XFLD: Message start of persistency cancellation
VIEW_PERSISTENCY_TRIGGER_CANCEL=Покретање отказивања процеса за трајно снимање погледа {0}.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_UNAVAILABLE_TASK_RUN=Није успело отказивање процеса за трајно снимање погледа јер за поглед {0} не постоји задатак трајности података који се изводи.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_NONDETERMINISTIC_TASK_RUN=Није успело отказивање процеса за трајно снимање погледа јер се задатак трајности података не изводи за поглед {0}.
##XFLD: Message for canceling a non running persitency task
VIEW_PERSISTENCY_CANCEL_NON_RUNNING_SELECTED_TASK=Није успело отказивање процеса за трајно снимање погледа {0} јер се одабрани задатак трајности података {1} не изводи.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_UNREACHABLE_PERSISTENCY_RUN=Није успело отказивање процеса за трајно снимање погледа јер трајност података за поглед {0} још није покренута.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_COMPLETED_PERSISTENCY_RUN=Није успело отказивање процеса за трајно снимање погледа {0} зато што је процес већ завршен.
##XFLD: Message for failed cancellation
VIEW_PERSISTENCY_ABORTED_CANCEL_EXECUTION=Није успело отказивања процеса за трајно снимање погледа {0}.
##XFLD: Message for finalizing persistency cancellation
VIEW_PERSISTENCY_FINALIZE_CANCEL=Процес за заустављање трајности података погледа {0} је поднет.
##XFLD: Message used in persist task when cancellation is caught
VIEW_PERSISTENCY_CANCELED=Процес за трајно снимање погледа {0} је заустављен.
##XFLD: Message used in persist task when cancelation is caught and performed through a cancelation task
VIEW_PERSISTENCY_CANCELED_VIA_CANCEL_TASK=Процес за трајно снимање погледа {0} је заустављен помоћу задатка отказивања {1}.
##XFLD: Message used in cancel task when the task is triggered by deployment
VIEW_PERSISTENCY_CANCEL_TRIGGERED_BY_DEPLOYMENT=Отказивање процеса за трајно снимање података при имплементацији погледа {0}.
##XFLD: Message used in case the cancelation is retriggered for a running task
VIEW_PERSISTENCY_CANCEL_ATTEMPT_RETRIGGER_CANCEL=Претходни задатак отказивања за трајно снимање погледа {0} је већ поднет.
##XFLD: Message used to express the delay that may occur before the persistency task is stopped
VIEW_PERSISTENCY_CANCEL_DELAY_STOPPING_PERSISTENCY=Могуће је кашњење док се не заустави задатак трајног снимања података за поглед {0}.
##XFLD: Message used to reflect the task log identifier of the current running persistency task
VIEW_PERSISTENCY_CANCEL_CURRENT_RUNNING_TASK=Подаци за поглед {0} се трајно снимају са задатком {1}.
##XFLD:Message used by SEAL repository for locked partitions and DACs
VIEW_PERSISTENCY_LOCKED_PARTITIONS_DACS=Овлашћења која пружају контроле приступа подацима могу бити промењена и закључане партиције их не узимају у обзир. Откључајте партиције и учитајте нови тренутни снимак да бисте применили промене.
##XFLD:Message used by SEAL repository for column structure change
VIEW_PERSISTENCY_COLUMN_STRUCTURE_CHANGE=Структура колоне је промењена и више се не подудара с постојећом трајно сачуваном табелом. Уклоните трајно сачуване податке и покрените ново трајно снимање података да бисте ажурирали трајно сачувану табелу.
##XFLD: Message for out of memory
VIEW_PERSISTENCY_HANA_OUT_OF_MEMORY=Задатак није успео због грешке недостатка меморије у бази података SAP HANA.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_EXCEPTION_TRANSACTION_ROLLBACK=Задатак није успео због интерног изузетка у бази података SAP HANA.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_SQL_EXECUTION_ISSUE=Задатак није успео због интерног проблема извршења SQL у бази података SAP HANA.
##XFLD: Message for HANA out of memory result
VIEW_PERSISTENCY_OUT_OF_MEMORY_RESULT_MESSAGE=Разлог догађаја недостатка меморије HANA: {0}
##XFLD: Message for admission control rejection
VIEW_PERSISTENCY_HANA_ADMISSION_CTRL_REJECTION=Задатак није успео због одбијања контроле пријема SAP HANA.
##XFLD: Message for HANA back pressure / rate limit of HANA client
VIEW_PERSISTENCY_HANA_CLIENT_RATE_LIMIT_REJECTION=Задатак није успео због превише активних SAP HANA веза.
#XMSG: Message for invalid persisted table name
VIEW_PERSISTENCY_PERSISTED_TABLE_INVALID=Дошло је до грешке и трајно сачувана табела је постала неважећа. Да бисте решили овај проблем, уклоните трајно сачуване податке и поново трајно сачувајте поглед.
#XMSG: Message for usahe of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION=Поглед се не може трајно сачувати. Користи удаљену табелу засновану на удаљеном извору са активираним пропагирањем корисника. Проверите порекло погледа.
#XMSG: Message for dynamic usage of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION_DYNAMIC=Поглед се не може трајно сачувати. Користи удаљену табелу засновану на удаљеном извору са активираним пропагирањем корисника. Удаљена табела се може динамички користити преко погледа SQL Script-а. Порекло погледа можда неће приказати удаљену табелу.
#XMSG: Message for insufficient privileges or consumption of view with DAC via dynamic SQL
VIEW_PERSISTENCY_INSUF_PRIV_OR_DAC_DYN_SQL=Ваша овлашћења можда нису довољна. Отворите претходни приказ података да видите да ли имате потребна овлашћења. Ако имате, можда други поглед који се користи помоћу динамичког SQL script-а има примењену контролу приступа подацима (DAC).
#XMSG: deployment of DAC is deprecated and view need to be redeployed to be able to persist data
VIEW_PERSISTENCY_DEPRECATED_DAC=Поглед "{0}" је имплементиран помоћу контроле приступа подацима (DAC) која је у процесу застаревања. Имплементирајте поглед поново да бисте могли да задржите податке за поглед.
##XFLD:Message used by SEAL repository for input parameter
START_VIEW_PERSISTENCY_INPUT_PARAMETER=Употреба стандардне вредности "{0}" за параметар уноса "{1}".
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Elastic compute node keys~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
##XFLD:Message used to reflect disablement of ecn replica
ECN_DISABLE_EXISTING_REPLICA=Реплика еластичног чвора израчунавања је деактивирана.
##XFLD:Message used to reflect recreation of ecn replica
ECN_RECREATE_EXISTING_REPLICA=Реплика еластичног чвора израчунавања је поново креирана.
##XFLD:Message used to reflect re-enablement of ecn replica
ECN_ENABLE_EXISTING_REPLICA=Реплика еластичног чвора израчунавања је поново активирана.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Task Schedule Menu key~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message to schedule task
scheduleText=План
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Креирај план
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Уреди план
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Избриши план
#XFLD: Refresh frequency field
refreshFrequency=Учесталост освежавања
#XFLD: Refresh frequency field
refreshFrequencyNew=Учесталост
#XFLD: Refresh frequency field
refreshFrequencyNewNew=Планирана учесталост
#XBUT: label for None
none=Ништа
#XBUT: label for Real-Time replication state
realtime=У реалном времену
#XFLD: Label for table column
txtNextSchedule=Следеће извођење
#XFLD: Label for table column
txtNextScheduleNew=Планирано следеће извођење
#XFLD: Label for table column
txtNumOfRecords=Број записа
#XFLD: Label for scheduled link
scheduledTxt=Планирано
#XFLD: LABEL for partially persisted link
partiallyPersisted=Делимично трајно сачувано
#XFLD: Text for paused text
paused=Паузирано

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ View Monitor Tasklog Details Action keys~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message for release lock actionn
releaseLockMsg=Ако извођење потраје дуже него обично, то може значити да није успело и да статус није ажуриран у складу с тим. \r\n Да бисте отклонили проблем, можете уклонити блокаду и поставити њен статус на Није успело.
#XFLD: Label for release lock dialog
releaseLockText=Уклони блокаду

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Column header tooltip ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: tooltip for table column
txtViewTooltip=Назив трајно сачуваног погледа
#XFLD: tooltip for table column
txtViewDataAccessTooltip=Ово показује доступност погледа
#XFLD: tooltip for table column
txtViewRefreshFreqTooltip=Ово показује да ли је план дефинисан за поглед
#XFLD: tooltip for table column
txtViewStatusTooltip=Позовите статус трајно сачуваног погледа
#XFLD: tooltip for table column
txtViewLatestUpdateTooltip=Пружа информације о времену последњег ажурирања трајно сачуваног погледа
#XFLD: tooltip for table column
txtViewNextRunTooltip=Ако је план постављен за поглед, погледајте до када је планирано следеће извођење
#XFLD: tooltip for table column
txtViewNumOfRecordsTooltip=Пратите број записа.
#XFLD: tooltip for table column
txtViewInMemorySizeTooltip=Пратите колико простора поглед користи у вашој меморији
#XFLD: tooltip for table column
txtViewDiskSizeTooltip=Пратите колико простора поглед заузима на вашем диску
#XMSG: Expired text
txtExpired=Истекло

#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Објекат "{0}" се не може додати у ланац задатака.

#~~~~~~~~~~~View Analyzer task log messages~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message indicating number of records for a view, and memory consumption in Mebibyte during simulation of view persistency
RECORDS_PEAK_MEMORY=Поглед "{0}" има {1} записа. Симулација трајности података за овај поглед искористила је {2} MiB меморије.
#XMSG: View Analyzer execution failure
VIEW_ANALYZER_EXECUTION_FAILED=Извршење анализатора погледа није успело.
#XMSG: Missing authorizations for View Analyzer
VIEW_ANALYZER_MISSING_AUTH=Недостају овлашћења за анализатор погледа.
#XMSG: Maximum memory in Gibibyte is reached, therefore persistency simulation is stopped
MAXIMUM_MEMORY_REACHED=Максимална меморија од {0} GiB је достигнута при симулирању трајнoсти података за поглед "{1}". Стога се додатне симулације трајности података неће извести.
#XMSG: Error during persistency simulation occurred
PERSISTENCY_SIMULATION_ERROR=Грешка при симулацији трајности података за поглед "{0}".
#XMSG: Persistency simulation is not executed, because prerequisites are not fulfilled and the view cannot be persisted
PREREQUISITES_NOT_FULFILLED=Симулација трајности података није извршена за поглед "{0}" јер нису испуњени предуслови и поглед се не може трајно сачувати.
#XMSG: Persistency simulation is not executed, because the view does not exist in the database
VIEW_DOES_NOT_EXIST=Морате имплементирати поглед "{0}" да бисте омогућили симулацију трајности података.
#XMSG: Number of records cannot be determined, because the local table does not exist in the database
TABLE_DOES_NOT_EXIST=Локална табела "{0}" не постоји у бази података, стога се број записа не може одредити за ову табелу.
##XMSG: Message start of cancellation for View Analyzer task
CANCEL_VIEW_ANALYZER_TRIGGERED=Поднет је процес за заустављање задатка анализатора погледа {0} за поглед "{1}". Могуће је кашњење док се задатак не заустави.
##XMSG: Message start to indicate that task is not active
CANCEL_VIEW_ANALYZER_NO_ACTIVE_TASK=Задатак анализатора погледа {0} за поглед "{1}" није активан.
#XMSG: Cancel View Analyzer task failure
CANCEL_VIEW_ANALYZER_FAILED=Није успело отказивање задатка анализатора погледа.
##XMSG: Message used in View Analyzer execution task when task is canceled
VIEW_ANALYZER_CANCELED=Извршење анализатора погледа за поглед "{0}" је заустављено преко задатка отказивања.
##XMSG: Message start of cancelation for Model Validation task
CANCEL_MODEL_VALIDATION_TRIGGERED=Процес за заустављање задатка вредновања модела {0} за поглед "{1}" је поднет. Могуће је кашњење при заустављању задатка.
##XMSG: Message to indicate that task is not active
CANCEL_MODEL_VALIDATION_NO_ACTIVE_TASK=Задатак вредновања модела {0} за поглед "{1}" није активан.
#XMSG: Cancel Model Validation task failure
CANCEL_MODEL_VALIDATION_FAILED=Није успело отказивање задатка вредновања модела.
##XMSG: Message used in Model Validation task when task is canceled
MODEL_VALIDATION_CANCELED=Извршење вредновања модела за поглед "{0}" је заустављено помоћу задатка отказивања.
#XMSG: Message for Locked space error, before starting Model Validation task
MODEL_VALIDATION_LOCKED_SPACE_ERROR=Није могуће извршити вредновање модела за поглед "{0}" јер је простор "{1}" закључан.
##XMSG: Message used in View Analyzer execution task when determination of number of rows fails
NUMBER_ROWS_DETERMINATION_FAILED=Грешка при одређивању броја редова за локалну табелу "{0}".
##XMSG: Message to indicate that SQL plan was created
EXECUTION_PLAN_CREATED=Креиран је фајл плана SQL анализатора за поглед "{0}"  и може се пренети са сервера.
#XFLD: Message to indicate starting of process to generate SQL Analyzer plan file
START_PLANVIZ_EXECUTION=Покретање процеса за генерисање фајла плана SQL анализатора за поглед "{0}".
#XFLD: Message to indicate starting of View Analyzer process
VIEW_ANALYZER_EXECUTION_STARTED=Покретање извршења анализатора погледа.
##XMSG: Message to indicate that SQL plan file cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_PLANVIZ_NOT_FULFILLED=Фајл плана SQL анализатора не може се генерисати за поглед "{0}", јер предуслови трајности података нису испуњени.`
##XMSG: Error message during  generation of SQL Analyzer plan file
PLANVIZ_ERROR=Грешка при генерисању фајла плана SQL анализатора за поглед "{0}".
#XMSG: Message for Locked space error
VIEW_ANALYZER_LOCKED_SPACE_ERROR=Није могуће извршити анализатор погледа за поглед "{0}", пошто је простор "{1}" закључан.
#XMSG: Message for persistency simulation without partitioning
PERSISTENCY_SIMULATION_WITHOUT_PARTITIONS=Партиције погледа "{0}" не узимају се у обзир током симулације трајности података.
#XMSG: Message for generation of SQL Analyzer plan file without considering partitioning
PLANVIZ_WITHOUT_PARTITIONS=Партиције погледа "{0}" не узимају се у обзир током генерисања фајла плана SQL анализатора.
#XMSG: Confirmation message for mass removal of persisted data
MassRemovePersistency_Confirm_Msg=Да ли желите да уклоните трајно сачуване податке и вратите приступ подацима назад на виртуелни приступ?
#XMSG: Confirmation message for partial mass removal of selected persisted data
PartialMassRemovePersistency_Confirm_Msg={0} од {1} одабраних погледа имају трајно сачуване податке. \n Да ли желите да избришете трајно сачуване податке и да вратите приступ подацима назад на виртуелни приступ?
#XMSG: success message for stopping mass persistence
stopMassPersistenceSuccess=Уклањамо трајно сачуване податке за одабране погледе.
#XMSG: error message during mass stop persistence of a view (backend error)
STOP_MASS_PERSISTENCE_ERROR=Дошло је до грешке током заустављања трајности одабраних погледа.
#XMSG: memory analysis is only performed in space {0}
NO_MEMORY_ANALYSIS=Анализа меморије се врши само за ентитете у простору "{0}": "{1}" "{2}" су прескочени.
##XMSG: Message to indicate that Explain Plan cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_EXPLAINPLAN_NOT_FULFILLED=Фајл Објасни план не може се генерисати за поглед "{0}" јер предуслови трајности нису испуњени.
#XMSG: Message for Explain Plan generation without partitioning
EXPLAINPLAN_WITHOUT_PARTITIONS=Партиције погледа "{0}" не узимају се у обзир током генерисања фајла Објасни план.
#XFLD: Message to indicate starting of process to generate Explain Plan file
START_EXPLAINPLAN_EXECUTION=Покретање процеса генерисања фајла Објасни план за поглед "{0}".
##XMSG: Message to indicate that Explain Plan was created
EXPLAINPLAN_CREATED=Фајл План објашњења за поглед „{0}” је генерисан. Можете га приказати кликом на „Прикажи детаље” или га пренети са сервера ако имате релевантно одобрење.
##XMSG: Error message during  generation of Explain Plan file
EXPLAINPLAN_ERROR=Грешка при генерисању фајла Објасни план за поглед "{0}".
##XMSG: Explain Plan file cannot be generated for the view {0}, because too many views are stacked on each other
EXPLAINPLAN_MODEL_TOO_COMPLEX=Фајл План објашњења се не може генерисати за поглед "{0}". Превише погледа је наслагано један на други. Сложени модели могу довести до грешака недостатка меморије и успорити извођење. Препоручује се да задржите поглед.

##XMSG: Message to indicate that the space is locked
PERFORMANCE_ANALYSIS_LOCKED_SPACE_ERROR=Није могуће извршити анализу извођења за поглед "{0}" јер је простор "{1}" закључан.
##XMSG: The performance analysis task was cancelled
PERFORMANCE_ANALYSIS_CANCELED=Анализа извођења за поглед "{0}" је отказана.
##XMSG: The performance analysis failed
PERFORMANCE_ANALYSIS_FAILED=Анализа извођења није успела.
##XMSG: The performance analysis is finished
PERFORMANCE_ANALYSIS_FINISHED=Анализа извођења за поглед "{0}" је завршена. Прикажите резултат тако што ћете кликнути на "Прикажи детаље".
##XMSG: The view has input parameter with no default value
INPUT_PARAMETER_WITHOUT_DEFAULT_VALUE=Овај поглед се не може анализирати јер има параметар без стандардне вредности.
##XMSG: The view is not fully deployed
VIEW_NOT_FULLY_DEPLOYED=Овај поглед се не може анализирати јер није у потпуности имплементиран.
##XMSG: The view uses limited remote adapters
LIMITED_REMOTE_ADAPTER=Овај поглед користи најмање један удаљени адаптер са ограниченим способностима као што је примена филтера или подршка за "Бројање" који недостају. Задржавање или репликација објеката може скратити време извођења.
##XMSG: The view uses remote adapter that does not support 'Limit'
REMOTE_ADAPTER_NOT_SUPPORTING_LIMIT=Овај поглед користи најмање један удаљени адаптер који не подржава "Ограничење". Можда је одабрано више од 1000 записа.
##XMSG: Performance analysis is executed by using default parameters of view'
INPUT_PARAMETER_WITH_DEFAULT_VALUE=Анализа извођења се врши коришћењем стандардних вредности параметара погледа.
##XMSG: Message to indicate that an error occurred
PERFORMANCE_ANALYSIS_ERROR=Грешка у току анализе учинка за поглед "{0}".
##XMSG: Message start of cancellation for Performance Analysis task
CANCEL_PERFORMANCE_ANALYSIS_TRIGGERED=Поднет је процес за заустављање задатка анализе извођења {0} за поглед "{1}". Могуће је кашњење док се задатак не заустави.
##XMSG: Message start to indicate that task is not active
CANCEL_PERFORMANCE_ANALYSIS_NO_ACTIVE_TASK=Задатак анализе извођења {0} за поглед "{1}" није активан.
#XMSG: Cancel Performance Analysis task failure
CANCEL_PERFORMANCE_ANALYSIS_FAILED=Отказивање задатка анализе извођења није успело.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Додели ми план
#XBUT: Pause schedule menu label
pauseScheduleLabel=Паузирај план
#XBUT: Resume schedule menu label
resumeScheduleLabel=Настави план
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Грешка при уклањању планова.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Грешка при додели планова.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Грешка при паузирању планова.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Грешка при настављању планова.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Брисање {0} планова
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Промена власника {0} планова
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Паузирање {0} планова
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Настављање {0} планова
#XBUT: Select Columns Button
selectColumnsBtn=Одабери колоне
#XFLD: Refresh tooltip
TEXT_REFRESH=Освежи
#XFLD: Select Columns tooltip
text_selectColumns=Одабери колоне


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Runtime Metrics ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Label for Runtime Metrics
RuntimeMetricsTitle=Метрика времена извођења за
#XFLD : Label for Run Button
runButton=Изведи
#XFLD : Label for Cancel Button
cancelButton=Одустани
#XFLD : Label for Close Button
closeButton=Затвори
#XFLD : Label for Open View Analyzer Button
openViewAnalyzerButton=Отвори анализатор погледа
#XFLD : Label for Generate Explain Plan Button
generateExplainPlanButton=Генериши план објашњења
#XFLD : Label for Previous Run Column
previousRun=Претходно извођење
#XFLD : Label for Latest Run Column
latestRun=Најновије извођење
#XFLD : Label for time Column
time=Време
#XFLD : Label for Duration Column
duration=Трајање
#XFLD : Label for Peak Memory Column
peakMemory=Максимум меморије
#XFLD : Label for Number of Rows
numberOfRows=Број редова
#XFLD : Label for Overall Number of Sources
overallNumberOfSources=Укупни број извора
#XFLD : Label for Data Access Column
dataAccess=Приступ подацима
#XFLD : Label for Local Tables
localTables=Локалне табеле
#XFLD : Label for federatedRemoteTables
federatedRemoteTables=Повезане удаљене табеле (са ограниченим могућностима адаптера)
#XTXT Text for initial state of the runtime metrics
initialState=Прво морате да изведете анализу учинка да би се позвала метрика. То може потрајати, али по потреби можете отказати овај процес.
#XMSG: Cancel confirmation message
CancelRunConfirmationMessage=Да ли заиста желите да откажете тренутно извођење анализе учинка?
#XTIT: Cancel dialog title
CancelRunTitle=Откажи извођење
#XFLD: Label for Number of Rows
NUMBER_ROWS=Број редова
#XFLD: Label for Overall Number of Sources
OVERALL_NUMBER_SOURCES=Укупни број извора
#XFLD: Label for Data Access
DATA_ACCESS=Приступ подацима
#XFLD: Label for Remote tables
NUMBER_REMOTE_TABLES_LIMITED_ADAPTERS=Повезане удаљене табеле (са ограниченим могућностима адаптера)
#XFLD: Label for select statement
SELECT_STATEMENT="SELECT * FROM VIEW LIMIT 1000"
#XFLD: Label for duration
SELECT_RUNTIME=Трајање
#XFLD: Label for peak memory
SELECT_PEAK_MEMORY=Максимум меморије
#XFLD: Label for count statement
SELECT_COUNT_STATEMENT="SELECT COUNT(*) FROM VIEW"
#XFLD: Label for count duration
SELECT_COUNT_RUNTIME=Трајање
#XFLD: Label for count peak memory
SELECT_COUNT_PEAK_MEMORY=Максимум меморије
#XFLD: Label for count of number of local tables
NUMBER_LOCAL_TABLES_FILE=Локалне табеле (фајл)
#XTXT: Text for running state of the runtime metrics
Running=Изводи се...
#XFLD: Label for time
Time=Време
#XFLD: Label for virtual access
PA_VIRTUAL=Виртуелно
#XFLD: Label for persisted access
PA_PERSISTED=Трајно сачувано
PA_PARTIALLY_PERSISTED=Делимично трајно сачувано
#XTXT: Text for cancel
CancelRunSuccessMessage=Отказивање извођења анализе учинка.
#XTXT: Text for cancel error
CancelRunErrorMessage=Грешка при отказивању извођења анализе учинка.
#XTXT: Text for explain plan generation
ExplainPlanStarted=Генерише се Објасни план за поглед "{0}".
#XTXT: Text for performance analysis
PerformanceAnalysisStarted=Почетак анализе учинка за поглед "{0}".
#XTXT: Text for performance analysis error
ErrorFetchingPerformanceAnalysis=Грешка при позивању података анализе учинка.
#XTXT: Text for performance analysis error
conflictingTask=Задатак анализе учинка се већ изводи
#XFLD: Label for Errors
Errors=Грешке
#XFLD: Label for Warnings
Warnings=Упозорење/а
#XFLD: Tooltip for missing privileges
openViewAnalyzerButtonTooltipNoPrivilege=Потребно вам је овлашћење DWC_DATAINTEGRATION(ажурирање) за отварање анализатора погледа.
#XFLD: Tooltip for missing privileges
generateExplainPlanButtonTooltipNoPrivilege=Потребно вам је овлашћење DWC_RUNTIME(читање) за генерисање Објасни план.



#XFLD: Label for frequency column
everyLabel=Сваких
#XFLD: Plural Recurrence text for Hour
hoursLabel=Сати
#XFLD: Plural Recurrence text for Day
daysLabel=Дани
#XFLD: Plural Recurrence text for Month
monthsLabel=Месеци
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Минути
