
#XBCB: Breadcrumb
viewMonitorBreadcrumb={spaceId}

#XFLD: Label for persisted view column
SOURCE=Source
#XFLD: Label for persisted view column
NAME=Nom
#XFLD: Label for persisted view column
NAME_LABEL=Appellation
#XFLD: Label for persisted view column
NAME_LABELNew=Objet (Appellation)
#XFLD: Label for persisted view column
TECHINCAL_NAME=Nom technique
#XFLD: Label for persisted view column
TECHINCAL_NAMENew=Objet (Nom technique)
#XFLD: Label for persisted view column
DATA_PERSISTENCY=Accès aux données
#XFLD: Label for persisted view column
STATUS=Statut
#XFLD: Label for persisted view column
LAST_UPDATED=Dernière mise à jour
#XFLD: Label for persisted view column
IN_MEMORY_SIZE=Mémoire utilisée pour le stockage (Mio)
#XFLD: Label for persisted view column
DISK_SIZE=Disque utilisé pour le stockage (Mio)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_NEWW=Espace en mémoire (Mio) 
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_HEADER=Espace en mémoire
#XFLD: Label for persisted view column
DISK_SIZE_NEWW=Espace sur disque (Mio)
#XFLD: Label for persisted view column
DISK_SIZE_HEADER=Espace sur disque
#XFLD: Label for schedule owner column
txtScheduleOwner=Responsable des planifications
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Affiche la personne ayant créé la planification
#XFLD: Label for persisted view column
PERSISTED=Rendu(e) persistant(e)
#XFLD: Label for persisted view column
TYPE=Type
#XFLD: Label for View Selection Dialog column
changedOn=Date de modification
#XFLD: Label for View Selection Dialog column
createdBy=Auteur de la création
#XFLD: Label for log details column
txtViewPersistencyLogs=Afficher les journaux
#XFLD: Label for log details column
txtViewPersistencyLogsNew=Détails
#XFLD: text for values shown for Ascending sort order
SortInAsc=Trier par ordre croissant
#XFLD: text for values shown for Descending sort order
SortInDesc=Trier par ordre décroissant
#XFLD: Label for persisted view monitor heading
ViewMonitor_Heading=Moniteur de vues
#XFLD: Label for persisted view monitor sub-heading
ViewMonitor_SubHeading=Suivre et gérer la persistance des données des vues


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ DynamicPageHeader ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: text for values shown in column Persistence Status
txtLoading=Chargement en cours
#XFLD: text for values shown in column Persistence Status
txtRunning=En cours d'exécution
#XFLD: text for values shown in column Persistence Status
txtAvailable=Disponible
#XFLD: text for values shown in column Persistence Status
txtError=Erreur
#XMSG: Error message for unknown Persistence type that gets logged in Kibana
unknownReplicationTypeError=Le type de réplication "{0}" n''est pas pris en charge.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Content ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Paramètres utilisés pour la dernière exécution d'une persistance de données :
#XMSG: Message for input parameter name
inputParameterLabel=Paramètre d'entrée
#XMSG: Message for input parameter value
inputParameterValueLabel=Valeur
#XMSG: Message for persisted data
inputParameterPersistedLabel=Date/Heure de la persistance
#XTIT: Count for View in ViewMonitor Toolbar
txtViewCount=Vues ({0})
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Persistance des vues
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Persistance des données
#XBUT: Button to clear the selected view persistance
ClearViewPersistance=Réinitialiser
#XBUT: Button to stop the selected view persistance
stopPersistance=Arrêter la persistance
#XFLD: Placeholder for Search field
txtSearch=Rechercher
#XBUT: Tooltip for refresh button
txtRefresh=Actualiser
#XBUT: Tooltip for add view button
txtDeleteView=Supprimer la persistance
#XBUT: Tooltip for load new peristence
loadNewPersistence=Relancer la persistance
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Charger un nouvel instantané
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Lancer la persistance des données
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Retirer les données rendues persistantes
#XMSG: success message for starting persistence
startPersistenceSuccess=La vue ''{0}'' est actuellement rendue persistante.
#XMSG: success message for stopping persistence
stopPersistenceSuccess=Les données rendues persistantes pour la vue ''{0}'' sont en cours de retrait.
#XMSG: success message for stopping persistence
deletePersistedViewSuccess=La vue "{0}'' est en cours de retrait de la liste de suivi.
#XMSG: error message during start persistence of a view (backend error)
ERROR_STARTING_PERSISTENCE=Une erreur s''est produite lors du lancement de la persistance des données pour la vue ''{0}''.
#XMSG: error message during start persistence of a view (backend error)
PARAMERROR_STARTING_PERSISTENCE=La vue ''{0}'' ne peut pas être rendue persistante, car elle comporte des paramètres d''entrée.
#XMSG: error message during start persistence of a view (backend error)
NUMBER_OF_PARAMETER_STARTING_PERSISTENCE=La vue "{0}" ne peut pas être rendue persistante, car elle comporte plus d''un paramètre d''entrée.
#XMSG: error message during start persistence of a view (backend error)
PARAMETER_DEFAULT_VALUE_STARTING_PERSISTENCE=La vue "{0}" ne peut pas être rendue persistante, car le paramètre d''entrée n''a pas de valeur par défaut.
#XMSG: error message during start persistence of a view (backend error)
REDEPLOYMENT_DAC_REQUIRED=Le redéploiement du contrôle d''accès aux données (DAC) "{0}" est nécessaire pour prendre en charge la persistance des données.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_STARTING_PERSISTENCE=La vue ''{0}'' ne peut pas être rendue persistante, car elle utilise la vue ''{1}'', qui contient un contrôle d''accès aux données (DAC).
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_EXTERNAL_STARTING_PERSISTENCE=La vue ''{0}'' ne peut pas être rendue persistante, car elle utilise une vue qui possède un contrôle d''accès aux données (DAC) appartenant à un autre espace.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_STRUCTURE_STARTING_PERSISTENCE=La vue "{0}" ne peut pas être rendue persistante parce que la structure d''un ou de plusieurs de ses contrôles d''accès aux données ne prend pas en charge la persistance des données.
#XMSG: error message during stop persistence of a view (backend error)
STOP_PERSISTENCE_ERROR=Une erreur s''est produite lors de l''arrêt de la persistance pour la vue ''{0}''.
#XMSG: error message during stop persistence of a view (backend error)
DELETE_PERSISTED_VIEW_ERROR=Une erreur s''est produite lors de la suppression de la vue rendue persistante "{0}".
#XMSG: Warning message for removing the persisted data
RemovePersistency_Confirm_Msg=Souhaitez-vous supprimer les données rendues persistantes et basculer vers l''accès virtuel de la vue ''{0}'' ?
#XMSG: Warning message for delete the view persistence
DeletePersistency_Confirm_Msg=Souhaitez-vous retirer la vue de la liste de suivi et supprimer les données rendues persistantes de la vue "{0}" ?
#XMSG: error message for reading data from backend
txtReadBackendError=Il semblerait qu'une erreur se soit produite lors de la lecture à partir du backend.
#XFLD: Label for No Data Error
NoDataError=Erreur
#XMSG: message for conflicting task
Task_Already_Running=Une tâche en conflit est déjà en cours d''exécution pour la vue ''{0}''.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Show All Views~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Text for show all views button
allViewsText=Toutes les vues ({0})
#XBUT: Text for show scheduled views button
scheduledText=Planifiée(s) ({0})
#XBUT: Text for show persisted views button
persistedText=Rendue(s) persistante(s) ({0})
#XBUT: Text for start analyzer button
startAnalyzer=Lancer l'analyseur de vues
#XFLD: Message if repository is unavailable
repositoryErrorMsg=Le référentiel n'est pas disponible et certaines fonctionnalités sont désactivées.

#XFLD: Data Access - Virtual
Virtual=Virtuel
#XFLD: Data Access - Persisted
Persisted=Rendu(e) persistant(e)

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Non-Persisted View Selection Dialog~~~~~~~~~~~~~~~~~~~~~~~

#XTIT: Title for Non-Persisted View selection dialog
select_source=Sélectionner la vue à rendre persistante

#XTIT: Placeholder for Non-Persisted View selection dialog search
searchView=Rechercher les vues
#XTIT: No data in the list of non-persisted view
No_Data=Aucune donnée
#XBUT: Button to select non-persisted view
ok=OK
#XBUT: Button to close the non-persisted views selection dialog
cancel=Annuler

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~View persistency log message key~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message for view persistency logs
START_STOP_PERSISTENCY=Lancement de l''exécution de la tâche de persistance des données pour ''{1}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY=Persistance des données en cours pour la vue ''{1}''.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY)
START_VIEW_PERSISTENCY_PROCESS=Lancement du processus pour rendre les données persistantes pour la vue ''{0}''.
#XFLD: Message for view persistency manual partition load
START_VIEW_PERSISTENCY_PROCESS_MANUAL_PARTITIONS=Lancement du processus pour rendre les données persistantes pour la vue ''{0}'' avec les ID de partition sélectionnés : "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY=Les données rendues persistantes pour la vue ''{1}'' sont en cours de retrait.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY)
REMOVE_VIEW_PERSISTENCY=Lancement du processus pour retirer les données rendues persistantes pour la vue "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_SUCCESS=Données rendues persistantes pour la vue ''{1}''.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_SUCCESS)
START_VIEW_PERSISTENCY_PROCESS_SUCCESS=Données rendues persistantes pour la vue ''{0}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY_SUCCESS=Données rendues persistantes retirées et accès virtuel aux données restauré pour la vue ''{1}''.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_SUCCESS)
REMOVE_VIEW_PERSISTENCY_SUCCESS=Fin du processus pour retirer les données rendues persistantes pour la vue "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_FAILED=Impossible de rendre les données persistantes pour la vue ''{1}''.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_FAILED)
START_VIEW_PERSISTENCY_PROCESS_FAILED=Impossible de rendre les données persistantes pour la vue ''{0}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.05)
STOP_VIEW_PERSISTENCY_FAILED=Impossible de retirer les données rendues persistantes pour la vue ''{1}''.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_FAILED)
REMOVE_VIEW_PERSISTENCY_FAILED=Impossible de retirer les données rendues persistantes pour la vue ''{0}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_SUCCESS="{3}" enregistrements rendus persistants pour la vue ''{1}''.
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace START_PERSISTENCY_RECORD_COUNT_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS={0} enregistrements insérés dans la table de persistance des données pour la vue "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS_V2={0} enregistrements insérés dans la table de persistance des données pour la vue "{1}". Mémoire utilisée : {2} Gio.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_SUCCESS="{3}" enregistrements rendus persistants retirés pour la vue ''{1}''.
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace STOP_PERSISTENCY_RECORD_COUNT_SUCCESS)
REMOVE_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=Données rendues persistantes retirées, "{0}" enregistrements rendus persistants supprimés.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_FAILED=Échec de l''accès au nombre d''enregistrements (recordCount) pour la vue ''{1}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_FAILED=Échec de l''accès au nombre d''enregistrements (recordCount) pour la vue ''{1}''.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Planification supprimée pour ''{1}''.
#XFLD: Message for view persistency logs (new message to replace STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS)
REMOVE_VIEW_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Planification supprimée pour la vue "{0}".
#XFLD: Message for view persistency logs
STOP_PERSISTENCY_DELETE_SCHEDULE_FAILED=Échec de la suppression de la planification pour ''{1}''.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_REDEPLOY=Nous ne pouvons pas rendre la vue ''{0}'' persistante car elle a été modifiée et déployée depuis que vous avez commencé à la rendre persistante. Essayez à nouveau de rendre la vue persistante ou attendez la prochaine exécution planifiée.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_DELETE=Nous ne pouvons pas rendre la vue ''{0}'' persistante car elle a été supprimée depuis que vous avez commencé à la rendre persistante.
#XFLD: Message for successful data persistency of a single partition (for messages created before version 2022.03)
START_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} enregistrements rendus persistants dans la partition pour les valeurs "{1}" <= {2} < "{3}".
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (new message to replace START_PERSISTENCY_SINGLE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} enregistrements insérés dans la partition pour les valeurs "{1}" <= "{2}" < "{3}".
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS_V2={0} enregistrements insérés dans la partition pour les valeurs "{1}" <= "{2}" < "{3}". Mémoire utilisée : {4} Gio.
#XFLD: Message for successful replication of a "others" partition (for messages created before version 2022.03)
START_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} enregistrements rendus persistants dans la partition ''Autres''.
#XFLD: Message used by SEAL repository for successful replication of a "others" partition (new message to replace START_PERSISTENCY_OTHERS_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} enregistrements insérés dans la partition ''Autres''.
#XFLD: Message for view persistency record count of partitions (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={3} enregistrements rendus persistants pour la vue ''{1}'' dans {4} partitions.
#XFLD: Message used by SEAL repository for view persistency record count of partitions (new message to replace START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={0} enregistrements insérés dans la table de persistance des données pour la vue "{1}" dans {2} partitions.
#XFLD: Message used by SEAL repository for view persistency for partition wise refresh
START_VIEW_PERSISTENCY_RECORD_COUNT_REFRESHED_PARTITIONS_SUCCESS={0} enregistrements insérés dans la table de persistance des données pour la vue ''{1}''. Partitions mises à jour : {2} ; partitions bloquées : {3} ; nombre total de partitions : {4}.
#XFLD: Message used by SEAL repository for view persistency for manual partition load
START_VIEW_PERSISTENCY_RECORD_COUNT_MANUAL_PARTITIONS_SUCCESS={0} enregistrements insérés dans la table de persistance des données pour la vue ''{1}'' dans {2} partitions sélectionnées.
#XFLD: Message used by SEAL repository for view persistency for locked partition data load
START_VIEW_PERSISTENCY_RECORD_COUNT_LOCKED_PARTITIONS_SUCCESS={0} enregistrements insérés dans la table de persistance des données pour la vue ''{1}''. Partitions mises à jour : {2} ; partitions bloquées non modifiées : {3} ; nombre total de partitions : {4}.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
START_PERSISTENCY_UNEXPECTED_ERROR=Une erreur inattendue est survenue lorsque les données ont été rendues persistantes pour la vue ''{0}''.
#XMSG: Message for unexpected error (new message to replace START_PERSISTENCY_UNEXPECTED_ERROR)
START_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Une erreur inattendue est survenue lorsque les données ont été rendues persistantes pour la vue ''{0}''.
#XMSG: Message for Locked space error
START_VIEW_PERSISTENCY_LOCKED_SPACE_ERROR=Impossible de rendre la vue ''{0}''’ persistante car l''espace ''{1}'' est bloqué.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
STOP_PERSISTENCY_UNEXPECTED_ERROR=Une erreur inattendue est survenue lors du retrait des données rendues persistantes pour la vue ''{0}''.
#XMSG: Message for unexpected error (new message to replace STOP_PERSISTENCY_UNEXPECTED_ERROR)
REMOVE_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Une erreur inattendue est survenue lors du retrait de la persistance pour la vue ''{0}''.
#XMSG: Message for ivalidated view
VIEW_PERSISTENCY_ABORTED_DEFINITION_INVALID=La définition de la vue ''{0}'' n''est plus valide, très probablement en raison d''une modification apportée à un objet consommé directement ou indirectement par la vue. Tentez de redéployer la vue pour résoudre le problème ou pour identifier la cause profonde.
#XMSG: Message for persisted data removal (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT=Les données persistantes ont été retirées pendant le déploiement de la vue ''{0}''.
#XMSG: Message for persisted data removal of a consuming view
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW=Les données persistantes ont été retirées pendant le déploiement de la vue consommée ''{0}''.
#XMSG: Message for persisted data removal of a consuming view due to a dac change
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW_DAC_CHANGED=Les données persistantes ont été retirées pendant le déploiement de la vue consommée ''{0}'' car son contrôle d''accès aux données a été modifié.
#XMSG: Message for persisted data removal (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DEPLOYMENT=Les données persistantes ont été retirées pendant le déploiement de la vue ''{0}''.
#XMSG: Message for deleted view (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION=La persistance est retirée avec la suppression de la vue "{0}".
#XMSG: Message for deleted view (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DELETION=La persistance est retirée avec la suppression de la vue "{0}".
#XMSG Message for removal of persisted data, because prerequisites not fulfilled
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_PREREQUISITES_NOT_FULFILLED=Les données rendues persistantes ont été retirées, car les conditions préalables à la persistance des données ne sont plus satisfaites.
#XMSG: Message for inconsistent persistency
VIEW_PERSISTENCY_INCONSISTENT_PERSISTENCY=La persistance de la vue "{0}" est devenue incohérente. Retirez les données rendues persistantes pour résoudre le problème.
#XMSG: Message for prerequisites
START_PERSISTENCY_CHECK_PREREQUISITES=Vérification des conditions préalables à la persistance de la vue ''{0}''.
#XMSG: Message for DAC deployed in deprecated way using table function and join (for messages created before 2025.05)
START_PERSISTENCY_DEPRECIATED_DAC=La vue "{0}" est déployée à l''aide du contrôle d''accès aux données en cours d''obsolescence. Redéployez la vue pour améliorer les performances.
#XMSG: Message for DAC deployed in deprecated way using table function and join
START_PERSISTENCY_DEPRECATED_DAC=La vue "{0}" est déployée à l''aide du contrôle d''accès aux données en cours d''obsolescence. Redéployez la vue pour améliorer les performances.
#XMSG: Message for roll back (for messages created before version 2022.03)
ROLLBACK_VIEW_PERSISTENCY=Une erreur s''est produite. L''état précédent de persistance a été restauré pour la vue ''{0}''.
#XMSG: Message used by SEAL repository for rollback of running procedure to persist view (new message to replace ROLLBACK_VIEW_PERSISTENCY)
VIEW_PERSISTENCY_ROLLBACK=Une erreur s''est produite. Le processus permettant de rendre la vue ''{0}'' persistante a été arrêté et les modifications ont été rejetées.
#XMSG: Message used by SEAL repository for rollback of running procedure to remove persisted data of view
REMOVE_VIEW_PERSISTENCY_ROLLBACK=Une erreur s''est produite. Le processus permettant de retirer les données rendues persistantes de la vue "{0}" a été arrêté et les modifications ont été rejetées.
#XMSG: Message used by SEAL repository for persistency preparation
START_PERSISTENCY_PREPARATION=Préparation de la persistance des données.
#XMSG: Message used by SEAL repository for data insertion
START_INSERTING_PERSISTENCY_DATA=Insertion des données dans la table rendue persistante.
#XMSG: Message for null values - partitioning (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS={0} enregistrements de valeur nulle ont été insérés dans la partition "Autres".
#XMSG: Message used by SEAL repository for others partition values - partitioning (new message to replace START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS={0} enregistrements insérés dans la partition "Autres" pour les valeurs ''{2}'' < ''{1}'' OU ''{2}'' >= ''{3}''.
#XMSG: Message used by SEAL repository for others partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS_V2={0} enregistrements insérés dans la partition "Autres" pour les valeurs ''{2}'' < ''{1}'' OU ''{2}'' >= ''{3}''. Mémoire utilisée : {4} Gio.
#XMSG: Message used by SEAL repository for null partition values - partitioning
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS={0} enregistrements insérés dans la partition "Autres" pour les valeurs ''{1}'' IS NULL (nulles).
#XMSG: Message used by SEAL repository for null partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS_V2={0} enregistrements insérés dans la partition "Autres" pour les valeurs ''{1}'' IS NULL (nulles). Mémoire utilisée : {2} Gio.
##XFLD:Message used by SEAL repository for remote statistics
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS=Le chargement des données a impliqué {0} instructions à distance. Nombre total d''enregistrements extraits : {1}. Durée totale : {2} secondes.
##XFLD:Message used by SEAL repository for remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS_WITH_PARTITIONS=Le chargement des données a impliqué l''utilisation de {0} partitions avec {1} instructions à distance. Nombre total d''enregistrements extraits : {2}. Durée totale : {3} secondes.
##XFLD:Message used by SEAL repository with UI ID for instructions of remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_INSTRUCTION=Il est possible d'afficher les instructions à distance traitées lors de l'exécution en ouvrant le moniteur de requêtes à distance dans les détails des messages propres à la partition.
##XFLD:Message for start reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS=Lancement du processus de réutilisation des données rendues persistantes existantes pour la vue {0} après le déploiement
##XFLD:Message used by SEAL repository for reattach view persitency
START_VIEW_PERSISTENCY_REATTACHMENT=Lancer la réutilisation des données rendues persistantes existantes
##XFLD:Message used by SEAL repository for reattach view persitency
START_FINILIZING_VIEW_PERSISTENCY_REATTACHMENT=Réutilisation des données rendues persistantes existantes
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_COMPLETE=Le processus de réutilisation des données rendues persistantes existantes est terminé pour la vue {0}.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_FAILED=Échec de la réutilisation des données rendues persistantes existantes pour la vue {0} après le déploiement
#XMSG: Message used by SEAL repository for finalizing persistency
START_FINALIZING_PERSISTENCY=Finalisation de la persistance.
#XMSG: Message used by SEAL repository for restoring virtual access for view
REMOVE_VIEW_PERSISTENCY_VIRTUAL_ACCESS_RESTORED=L''accès aux données virtuelles est restauré pour la vue "{0}".
#XMSG: Message used by SEAL repository for view that already has virtual access
REMOVE_VIEW_PERSISTENCY_ALREADY_VIRTUAL_ACCESS=La vue "{0}" comporte déjà un accès aux données virtuelles. Aucune donnée rendue persistante n''est retirée.
#XMSG: Message for removing view persistency
VIEW_PERSISTENCY_REMOVED_FROM_MONITOR=La vue ''{0}'' a été retirée du Moniteur de vues.
#XMSG: Message for view doesnt exist.
VIEW_PERSISTENCY_VIEW_NOT_EXISTING=La vue "{0}" n''existe pas dans la base de données ou n''est pas correctement déployée. Le système ne peut donc pas la rendre persistante. Essayez de redéployer la vue pour résoudre ce problème ou d''identifier la cause profonde.
#XMSG: Message for persistency not enabled.
VIEW_PERSISTENCY_NOT_ENABLED=La persistance des données n''est pas activée. Redéployez une table/vue dans l''espace ''{0}'' pour activer la fonctionnalité.
#XMSG: Message for unexpected error due to node crash
VIEW_PERSISTENCY_UNEXPECTED_ERROR_GENERAL=La dernière exécution de la persistance des vues a été interrompue en raison d'erreurs techniques.
#XFLD: Message for view persistency memory consumption
VIEW_PERSISTENCY_MEMORY_CONSUMPTION={0} Gio de pic de mémoire utilisée dans l''exécution de la persistance des vues.
#XFLD: Message for view persistency timeout
VIEW_PERSISTENCY_TIMEOUT=La persistance de la vue {0} a atteint le délai d''expiration de {1} heures.
#XFLD: Message for not started asynchronous view persistency
VIEW_PERSISTENCY_ASYNC_NOT_STARTED=Une charge système élevée a empêché l'exécution asynchrone de la persistance des vues au démarrage. Vérifiez si un trop grand nombre de tâches s'exécutent en parallèle.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency (for messages created before version 2022.05)
START_VIEW_PERSISTENCY_EXECUTION_METHOD_DROP_AND_RENAME=La table rendue persistante existante a été supprimée et remplacée par une nouvelle table rendue persistante.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_RECREATE_AND_DROP=La table rendue persistante existante a été supprimée et remplacée par une nouvelle table rendue persistante.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_TRUNCATE_AND_COPY=La table rendue persistante existante a été mise à jour avec de nouvelles données.
##XFLD: Message for missing authorizations for view persistency
VIEW_PERSISTENCY_MISSING_AUTH=Autorisations manquantes pour la persistance des données
##XFLD: Message start of persistency cancellation
VIEW_PERSISTENCY_TRIGGER_CANCEL=Démarrage de l''annulation du processus visant à rendre la vue {0} persistante
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_UNAVAILABLE_TASK_RUN=L''annulation du processus visant à rendre la vue persistante a échoué car aucune tâche de persistance des données n''est en cours d''exécution pour la vue {0}.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_NONDETERMINISTIC_TASK_RUN=L''annulation du processus visant à rendre la vue persistante a échoué car aucune tâche de persistance des donnes n''est en cours d''exécution pour la vue {0}.
##XFLD: Message for canceling a non running persitency task
VIEW_PERSISTENCY_CANCEL_NON_RUNNING_SELECTED_TASK=L''annulation du processus visant à rendre la vue {0} persistante a échoué car la tâche de persistance des données {1} sélectionnée n''est pas en cours d''exécution.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_UNREACHABLE_PERSISTENCY_RUN=L''annulation du processus visant à rendre la vue persistante a échoué car la persistance des données pour la vue {0} n''a pas encore démarré.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_COMPLETED_PERSISTENCY_RUN=L''annulation du processus visant à rendre la vue {0} persistante a échoué car il est déjà terminé.
##XFLD: Message for failed cancellation
VIEW_PERSISTENCY_ABORTED_CANCEL_EXECUTION=L''annulation du processus visant à rendre la vue {0} persistante a échoué.
##XFLD: Message for finalizing persistency cancellation
VIEW_PERSISTENCY_FINALIZE_CANCEL=Le processus visant à arrêter la persistance des données de la vue {0} a été soumis.
##XFLD: Message used in persist task when cancellation is caught
VIEW_PERSISTENCY_CANCELED=Le processus visant à rendre la vue {0} persistante a été arrêté.
##XFLD: Message used in persist task when cancelation is caught and performed through a cancelation task
VIEW_PERSISTENCY_CANCELED_VIA_CANCEL_TASK=Le processus visant à rendre la vue {0} persistante a été arrêté via la tâche d''annulation {1}.
##XFLD: Message used in cancel task when the task is triggered by deployment
VIEW_PERSISTENCY_CANCEL_TRIGGERED_BY_DEPLOYMENT=Annulation du processus visant à rendre les données persistantes pendant le déploiement de la vue {0}
##XFLD: Message used in case the cancelation is retriggered for a running task
VIEW_PERSISTENCY_CANCEL_ATTEMPT_RETRIGGER_CANCEL=Une tâche d''annulation précédente pour la persistance de la vue {0} a déjà été soumise.
##XFLD: Message used to express the delay that may occur before the persistency task is stopped
VIEW_PERSISTENCY_CANCEL_DELAY_STOPPING_PERSISTENCY=Il peut y avoir un délai jusqu''à ce que la tâche de persistance des données pour la vue {0} soit arrêtée.
##XFLD: Message used to reflect the task log identifier of the current running persistency task
VIEW_PERSISTENCY_CANCEL_CURRENT_RUNNING_TASK=Les données de la vue {0} sont rendues persistantes avec la tâche {1}.
##XFLD:Message used by SEAL repository for locked partitions and DACs
VIEW_PERSISTENCY_LOCKED_PARTITIONS_DACS=Les autorisations fournies par les contrôles d'accès aux données (DAC) ont peut-être été modifiées et ne sont plus prises en compte par les partitions verrouillées. Déverrouillez les partitions et chargez un nouvel instantané pour appliquer les modifications.
##XFLD:Message used by SEAL repository for column structure change
VIEW_PERSISTENCY_COLUMN_STRUCTURE_CHANGE=La structure de colonne a été modifiée et ne correspond plus à la table de persistance existante. Retirez les données rendues persistantes et lancez une nouvelle persistance pour les données afin de mettre à jour votre table de persistance.
##XFLD: Message for out of memory
VIEW_PERSISTENCY_HANA_OUT_OF_MEMORY=La tâche a échoué en raison d'une erreur de capacité mémoire saturée dans la base de données SAP HANA.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_EXCEPTION_TRANSACTION_ROLLBACK=La tâche a échoué en raison d'une exception interne dans la base de données SAP HANA.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_SQL_EXECUTION_ISSUE=La tâche a échoué en raison d'un problème d'exécution SQL interne dans la base de données SAP HANA.
##XFLD: Message for HANA out of memory result
VIEW_PERSISTENCY_OUT_OF_MEMORY_RESULT_MESSAGE=Motif de l''événement de mémoire insuffisante HANA : {0}
##XFLD: Message for admission control rejection
VIEW_PERSISTENCY_HANA_ADMISSION_CTRL_REJECTION=La tâche a échoué en raison rejet de contrôle d'accès pour SAP HANA.
##XFLD: Message for HANA back pressure / rate limit of HANA client
VIEW_PERSISTENCY_HANA_CLIENT_RATE_LIMIT_REJECTION=La tâche a échoué en raison d'un nombre trop élevé de connexions SAP HANA actives.
#XMSG: Message for invalid persisted table name
VIEW_PERSISTENCY_PERSISTED_TABLE_INVALID=Une erreur est survenue et la table rendue persistante est devenue non valide. Pour résoudre ce problème, retirez les données rendues persistantes et rendez à nouveau la vue persistante.
#XMSG: Message for usahe of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION=La vue ne peut pas être rendue persistante. Elle utilise une table distante basée sur une source distante dont la propagation utilisateur est activée. Vérifiez le lignage de la vue.
#XMSG: Message for dynamic usage of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION_DYNAMIC=La vue ne peut pas être rendue persistante. Elle utilise une table distante basée sur une source distante dont la propagation utilisateur est activée. La table distante peut être utilisée de façon dynamique via une vue de script SQL. Le lignage de la vue peut ne pas afficher la table distante.
#XMSG: Message for insufficient privileges or consumption of view with DAC via dynamic SQL
VIEW_PERSISTENCY_INSUF_PRIV_OR_DAC_DYN_SQL=Vos droits sont peut-être insuffisants. Ouvrez l'aperçu des données pour voir si vous avez les droits nécessaires. Si oui, il se peut que le contrôle d'accès aux données soit appliqué à une deuxième vue utilisée via une procédure SQLScript dynamique.
#XMSG: deployment of DAC is deprecated and view need to be redeployed to be able to persist data
VIEW_PERSISTENCY_DEPRECATED_DAC=La vue "{0}" est déployée à l''aide du contrôle d''accès aux données en cours d''obsolescence. Redéployez la vue pour pouvoir rendre les données persistantes pour la vue.
##XFLD:Message used by SEAL repository for input parameter
START_VIEW_PERSISTENCY_INPUT_PARAMETER=Utilisateur de la valeur par défaut "{0}" pour le paramètre d''entrée "{1}"
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Elastic compute node keys~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
##XFLD:Message used to reflect disablement of ecn replica
ECN_DISABLE_EXISTING_REPLICA=Réplique du nœud de calcul flexible désactivée
##XFLD:Message used to reflect recreation of ecn replica
ECN_RECREATE_EXISTING_REPLICA=Réplique du nœud de calcul flexible recréée
##XFLD:Message used to reflect re-enablement of ecn replica
ECN_ENABLE_EXISTING_REPLICA=Réplique du nœud de calcul flexible réactivée
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Task Schedule Menu key~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message to schedule task
scheduleText=Planifier
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Créer une planification
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Modifier la planification
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Supprimer la planification
#XFLD: Refresh frequency field
refreshFrequency=Fréquence d'actualisation
#XFLD: Refresh frequency field
refreshFrequencyNew=Fréquence
#XFLD: Refresh frequency field
refreshFrequencyNewNew=Fréquence planifiée
#XBUT: label for None
none=Néant
#XBUT: label for Real-Time replication state
realtime=En temps réel
#XFLD: Label for table column
txtNextSchedule=Prochaine exécution
#XFLD: Label for table column
txtNextScheduleNew=Prochaine exécution planifiée
#XFLD: Label for table column
txtNumOfRecords=Nombre d'enregistrements
#XFLD: Label for scheduled link
scheduledTxt=Planifiée
#XFLD: LABEL for partially persisted link
partiallyPersisted=Partiellement rendu(e) persistant(e)
#XFLD: Text for paused text
paused=Suspendu

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ View Monitor Tasklog Details Action keys~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message for release lock actionn
releaseLockMsg=Si une exécution prend plus de temps que prévu, cela peut signifier qu'elle a échoué et que le statut n'a pas été mis à jour en conséquence. \r\n Pour résoudre l'erreur, vous pouvez débloquer et définir le statut sur Échec.
#XFLD: Label for release lock dialog
releaseLockText=Débloquer

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Column header tooltip ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: tooltip for table column
txtViewTooltip=Nom de la vue rendue persistante
#XFLD: tooltip for table column
txtViewDataAccessTooltip=Indique la disponibilité de la vue.
#XFLD: tooltip for table column
txtViewRefreshFreqTooltip=Indique si une planification est définie pour la vue.
#XFLD: tooltip for table column
txtViewStatusTooltip=Affiche le statut de la vue rendue persistante.
#XFLD: tooltip for table column
txtViewLatestUpdateTooltip=Fournit des informations sur la date/heure de la dernière mise à jour de la vue rendue persistante.
#XFLD: tooltip for table column
txtViewNextRunTooltip=Si une planification est définie pour la vue, examinez à quel moment la prochaine exécution est planifiée.
#XFLD: tooltip for table column
txtViewNumOfRecordsTooltip=Contrôlez le nombre d'enregistrements.
#XFLD: tooltip for table column
txtViewInMemorySizeTooltip=Contrôlez l'espace que la vue utilise dans votre mémoire.
#XFLD: tooltip for table column
txtViewDiskSizeTooltip=Contrôlez l'espace que la vue utilise sur votre disque.
#XMSG: Expired text
txtExpired=Expiré

#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Impossible d''ajouter l''objet ''{0}'' à la chaîne de tâches.

#~~~~~~~~~~~View Analyzer task log messages~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message indicating number of records for a view, and memory consumption in Mebibyte during simulation of view persistency
RECORDS_PEAK_MEMORY=La vue "{0}" contient {1} enregistrements. Une simulation de persistance des données pour cette vue a utilisé {2} Mio de mémoire.
#XMSG: View Analyzer execution failure
VIEW_ANALYZER_EXECUTION_FAILED=L'exécution de l'analyseur de vues a échoué.
#XMSG: Missing authorizations for View Analyzer
VIEW_ANALYZER_MISSING_AUTH=Autorisations manquantes pour l'analyseur de vues
#XMSG: Maximum memory in Gibibyte is reached, therefore persistency simulation is stopped
MAXIMUM_MEMORY_REACHED=La limite maximale de mémoire utilisée ({0} Gio) a été atteinte lors de la simulation de la persistance des données pour la vue ''{1}''. Par conséquent, aucune autre simulation de persistance des données ne sera exécutée.
#XMSG: Error during persistency simulation occurred
PERSISTENCY_SIMULATION_ERROR=Une erreur s''est produite lors de la simulation de persistance des données pour la vue ''{0}''.
#XMSG: Persistency simulation is not executed, because prerequisites are not fulfilled and the view cannot be persisted
PREREQUISITES_NOT_FULFILLED=La simulation de persistance des données n''est pas exécutée pour la vue ''{0}'', car les conditions préalables nécessaires ne sont pas remplies et la vue ne peut pas être rendue persistante.
#XMSG: Persistency simulation is not executed, because the view does not exist in the database
VIEW_DOES_NOT_EXIST=Pour permettre la simulation de la persistance des données, vous devez déployer la vue "{0}".
#XMSG: Number of records cannot be determined, because the local table does not exist in the database
TABLE_DOES_NOT_EXIST=La table locale "{0}" n''existe pas dans la base de données. Le système ne peut par conséquent pas déterminer le nombre d''enregistrements pour cette table.
##XMSG: Message start of cancellation for View Analyzer task
CANCEL_VIEW_ANALYZER_TRIGGERED=Le processus visant à arrêter la tâche {0} de l''analyseur de vues pour la vue ''{1}'' a été soumis. Il peut y avoir un délai jusqu''à ce que la tâche soit arrêtée.
##XMSG: Message start to indicate that task is not active
CANCEL_VIEW_ANALYZER_NO_ACTIVE_TASK=La tâche {0} de l''analyseur de vues pour la vue ''{1}'' n''est pas active.
#XMSG: Cancel View Analyzer task failure
CANCEL_VIEW_ANALYZER_FAILED=L'annulation de la tâche de l'analyseur de vues a échoué.
##XMSG: Message used in View Analyzer execution task when task is canceled
VIEW_ANALYZER_CANCELED=L''exécution de l''analyseur de vues pour la vue ''{0}'' a été arrêtée via une tâche d''annulation.
##XMSG: Message start of cancelation for Model Validation task
CANCEL_MODEL_VALIDATION_TRIGGERED=Le processus visant à arrêter la tâche {0} de la validation de modèle pour la vue ''{1}'' a été soumis. Il peut y avoir un délai avant l''arrêt de la tâche.
##XMSG: Message to indicate that task is not active
CANCEL_MODEL_VALIDATION_NO_ACTIVE_TASK=La tâche de validation de modèle {0} n''est pas active pour la vue ''{1}''.
#XMSG: Cancel Model Validation task failure
CANCEL_MODEL_VALIDATION_FAILED=Impossible d'annuler la tâche de validation de modèle.
##XMSG: Message used in Model Validation task when task is canceled
MODEL_VALIDATION_CANCELED=L''exécution de la validation de modèle pour la vue ''{0}'' a été arrêtée via une tâche d''annulation.
#XMSG: Message for Locked space error, before starting Model Validation task
MODEL_VALIDATION_LOCKED_SPACE_ERROR=Impossible d''exécuter la validation de modèle pour la vue ''{0}'' car l''espace ''{1}'' est bloqué.
##XMSG: Message used in View Analyzer execution task when determination of number of rows fails
NUMBER_ROWS_DETERMINATION_FAILED=Une erreur s''est produite lors de la détermination du nombre de lignes pour la table locale ''{0}''.
##XMSG: Message to indicate that SQL plan was created
EXECUTION_PLAN_CREATED=Le fichier de plan de l''analyseur SQL pour la vue ''{0}'' est créé et peut être téléchargé.
#XFLD: Message to indicate starting of process to generate SQL Analyzer plan file
START_PLANVIZ_EXECUTION=Lancement du processus visant à générer le fichier de plan de l''analyseur SQL pour la vue ''{0}''.
#XFLD: Message to indicate starting of View Analyzer process
VIEW_ANALYZER_EXECUTION_STARTED=Lancement de l'exécution de l'analyseur de vues en cours
##XMSG: Message to indicate that SQL plan file cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_PLANVIZ_NOT_FULFILLED=Le fichier de plan de l''analyseur SQL ne peut pas être généré pour la vue ''{0}'', car les conditions préalables à la persistance des données ne sont pas remplies.
##XMSG: Error message during  generation of SQL Analyzer plan file
PLANVIZ_ERROR=Une erreur s''est produite lors de la génération du fichier de plan de l''analyseur SQL pour la vue ''{0}''.
#XMSG: Message for Locked space error
VIEW_ANALYZER_LOCKED_SPACE_ERROR=Impossible d''exécuter l''analyseur de vues pour la vue "{0}", car l''espace "{1}" est bloqué.
#XMSG: Message for persistency simulation without partitioning
PERSISTENCY_SIMULATION_WITHOUT_PARTITIONS=Les partitions de la vue ''{0}'' ne sont pas prises en compte lors de la simulation de persistance des données.
#XMSG: Message for generation of SQL Analyzer plan file without considering partitioning
PLANVIZ_WITHOUT_PARTITIONS=Les partitions de la vue ''{0}'' ne sont pas prises en compte lors de la génération du fichier de plan de l''analyseur SQL.
#XMSG: Confirmation message for mass removal of persisted data
MassRemovePersistency_Confirm_Msg=Voulez-vous retirer les données rendues persistantes et revenir à l'accès virtuel pour l'accès aux données ?
#XMSG: Confirmation message for partial mass removal of selected persisted data
PartialMassRemovePersistency_Confirm_Msg={0} des {1} vues sélectionnées comporte(nt) des données rendues persistantes. \n Voulez-vous retirer les données rendues persistantes et revenir à l''accès virtuel pour l''accès aux données ?
#XMSG: success message for stopping mass persistence
stopMassPersistenceSuccess=Les données rendues persistantes des vues sélectionnées sont en cours de retrait.
#XMSG: error message during mass stop persistence of a view (backend error)
STOP_MASS_PERSISTENCE_ERROR=Une erreur s'est produite lors de l'arrêt de la persistance pour les vues sélectionnées.
#XMSG: memory analysis is only performed in space {0}
NO_MEMORY_ANALYSIS=L''analyse de la mémoire a été effectuée pour les entités dans l''espace "{0}" uniquement : " {1}" "{2}" a été ignoré.
##XMSG: Message to indicate that Explain Plan cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_EXPLAINPLAN_NOT_FULFILLED=Le fichier Explain plan ne peut pas être généré pour la vue "{0}", car les conditions préalables à la persistance ne sont pas remplies.
#XMSG: Message for Explain Plan generation without partitioning
EXPLAINPLAN_WITHOUT_PARTITIONS=Les partitions de la vue "{0}" ne sont pas prises en compte lors de la génération du fichier Explain plan.
#XFLD: Message to indicate starting of process to generate Explain Plan file
START_EXPLAINPLAN_EXECUTION=Lancement du processus visant à générer le fichier Explain plan pour la vue "{0}".
##XMSG: Message to indicate that Explain Plan was created
EXPLAINPLAN_CREATED=Le fichier Explain Plan pour la vue "{0}" a été généré. Vous pouvez l''afficher en cliquant sur "Afficher les détails" ou le télécharger si vous disposez de l''autorisation pertinente.
##XMSG: Error message during  generation of Explain Plan file
EXPLAINPLAN_ERROR=Une erreur s''est produite lors de la génération du fichier Explain plan pour la vue "{0}".
##XMSG: Explain Plan file cannot be generated for the view {0}, because too many views are stacked on each other
EXPLAINPLAN_MODEL_TOO_COMPLEX=Le fichier Explain Plan ne peut pas être généré pour la vue "{0}". Trop de vues sont empilées les unes sur les autres. Les modèles complexes peuvent entraîner des erreurs de mémoire et un ralentissement des performances. Il est recommandé de rendre une vue persistante.

##XMSG: Message to indicate that the space is locked
PERFORMANCE_ANALYSIS_LOCKED_SPACE_ERROR=Impossible d''exécuter l''analyse de performance pour la vue "{0}", car l''espace "{1}" est bloqué.
##XMSG: The performance analysis task was cancelled
PERFORMANCE_ANALYSIS_CANCELED=L''analyse de performance pour la vue "{0}" a été annulée.
##XMSG: The performance analysis failed
PERFORMANCE_ANALYSIS_FAILED=L'analyse de performance a échoué.
##XMSG: The performance analysis is finished
PERFORMANCE_ANALYSIS_FINISHED=L''analyse de performance pour la vue "{0}" est terminée. Affichez le résultat en cliquant sur "Afficher les détails".
##XMSG: The view has input parameter with no default value
INPUT_PARAMETER_WITHOUT_DEFAULT_VALUE=Cette vue ne peut pas être analysée, car elle comporte un paramètre qui n'a pas de valeur par défaut.
##XMSG: The view is not fully deployed
VIEW_NOT_FULLY_DEPLOYED=Cette vue ne peut pas être analysée car elle n'est pas entièrement déployée.
##XMSG: The view uses limited remote adapters
LIMITED_REMOTE_ADAPTER=Cette vue utilise au moins un adaptateur distant avec des fonctionnalités limitées comme l'absence d'application de filtre ou de prise en charge de "COUNT". Le fait de répliquer ou de rendre des objets persistants peut améliorer la performance de la durée d'exécution.
##XMSG: The view uses remote adapter that does not support 'Limit'
REMOTE_ADAPTER_NOT_SUPPORTING_LIMIT=Cette vue utilise au moins un adaptateur distant qui ne prend pas en charge la "Limite". Plus de 1 000 enregistrements ont potentiellement été sélectionnés.
##XMSG: Performance analysis is executed by using default parameters of view'
INPUT_PARAMETER_WITH_DEFAULT_VALUE=L'analyse de performance est exécutée à l'aide des valeurs par défaut des paramètres d'affichage.
##XMSG: Message to indicate that an error occurred
PERFORMANCE_ANALYSIS_ERROR=Une erreur s''est produite lors de l''analyse de la performance pour la vue "{0}".
##XMSG: Message start of cancellation for Performance Analysis task
CANCEL_PERFORMANCE_ANALYSIS_TRIGGERED=Le processus visant à arrêter la tâche d''analyse de la performance {0} pour la vue "{1}" a été soumis. Il peut y avoir un délai jusqu''à ce que la tâche soit arrêtée.
##XMSG: Message start to indicate that task is not active
CANCEL_PERFORMANCE_ANALYSIS_NO_ACTIVE_TASK=La tâche d''analyse de la performance {0} pour la vue "{1}" n''est pas active.
#XMSG: Cancel Performance Analysis task failure
CANCEL_PERFORMANCE_ANALYSIS_FAILED=Échec de l'annulation de la tâche d'analyse de la performance.

#XBUT: Assign schedule menu button label
assignScheduleLabel=M'affecter la planification
#XBUT: Pause schedule menu label
pauseScheduleLabel=Suspendre la planification
#XBUT: Resume schedule menu label
resumeScheduleLabel=Reprendre la planification
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Une erreur s'est produite lors du retrait des planifications.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Une erreur s'est produite lors de l'affectation des planifications.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Une erreur s'est produite lors de la suspension des planifications.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Une erreur s'est produite lors de la reprise des planifications.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Suppression de {0} planifications
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Modification du responsable de {0} planifications
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Suspension de {0} planifications
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Reprise de {0} planifications
#XBUT: Select Columns Button
selectColumnsBtn=Sélectionner des colonnes
#XFLD: Refresh tooltip
TEXT_REFRESH=Actualiser
#XFLD: Select Columns tooltip
text_selectColumns=Sélectionner des colonnes


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Runtime Metrics ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Label for Runtime Metrics
RuntimeMetricsTitle=Indicateurs d'exécution pour
#XFLD : Label for Run Button
runButton=Exécuter
#XFLD : Label for Cancel Button
cancelButton=Annuler
#XFLD : Label for Close Button
closeButton=Fermer
#XFLD : Label for Open View Analyzer Button
openViewAnalyzerButton=Ouvrir l'analyseur de vues
#XFLD : Label for Generate Explain Plan Button
generateExplainPlanButton=Générer le fichier Explain plan
#XFLD : Label for Previous Run Column
previousRun=Exécution précédente
#XFLD : Label for Latest Run Column
latestRun=Dernière exécution
#XFLD : Label for time Column
time=Heure
#XFLD : Label for Duration Column
duration=Durée
#XFLD : Label for Peak Memory Column
peakMemory=Pic de mémoire
#XFLD : Label for Number of Rows
numberOfRows=Nombre de lignes
#XFLD : Label for Overall Number of Sources
overallNumberOfSources=Nombre total de sources
#XFLD : Label for Data Access Column
dataAccess=Accès aux données
#XFLD : Label for Local Tables
localTables=Tables locales
#XFLD : Label for federatedRemoteTables
federatedRemoteTables=Tables distantes fédérées (avec fonctionnalités d'adaptateur limitées)
#XTXT Text for initial state of the runtime metrics
initialState=Vous devez commencer par exécuter l'analyse de la performance pour accéder aux mesures. Cela peut prendre un certain temps mais vous pouvez annuler le processus si nécessaire.
#XMSG: Cancel confirmation message
CancelRunConfirmationMessage=Voulez-vous vraiment annuler l'exécution en cours de l'analyse de la performance ?
#XTIT: Cancel dialog title
CancelRunTitle=Annuler l'exécution
#XFLD: Label for Number of Rows
NUMBER_ROWS=Nombre de lignes
#XFLD: Label for Overall Number of Sources
OVERALL_NUMBER_SOURCES=Nombre total de sources
#XFLD: Label for Data Access
DATA_ACCESS=Accès aux données
#XFLD: Label for Remote tables
NUMBER_REMOTE_TABLES_LIMITED_ADAPTERS=Tables distantes fédérées (avec fonctionnalités d'adaptateur limitées)
#XFLD: Label for select statement
SELECT_STATEMENT='SELECT * FROM VIEW LIMIT 1000'
#XFLD: Label for duration
SELECT_RUNTIME=Durée
#XFLD: Label for peak memory
SELECT_PEAK_MEMORY=Pic de mémoire
#XFLD: Label for count statement
SELECT_COUNT_STATEMENT='SELECT COUNT(*) FROM VIEW'
#XFLD: Label for count duration
SELECT_COUNT_RUNTIME=Durée
#XFLD: Label for count peak memory
SELECT_COUNT_PEAK_MEMORY=Pic de mémoire
#XFLD: Label for count of number of local tables
NUMBER_LOCAL_TABLES_FILE=Tables locales (fichier)
#XTXT: Text for running state of the runtime metrics
Running=En cours d'exécution...
#XFLD: Label for time
Time=Heure
#XFLD: Label for virtual access
PA_VIRTUAL=Virtuel
#XFLD: Label for persisted access
PA_PERSISTED=Rendu(e) persistant(e)
PA_PARTIALLY_PERSISTED=Partiellement rendu(e) persistant(e)
#XTXT: Text for cancel
CancelRunSuccessMessage=Annulation en cours de l'exécution de l'analyse de la performance
#XTXT: Text for cancel error
CancelRunErrorMessage=Une erreur s'est produite lors de l'annulation de l'exécution de l'analyse de la performance.
#XTXT: Text for explain plan generation
ExplainPlanStarted=Génération en cours du fichier Explain plan pour la vue "{0}".
#XTXT: Text for performance analysis
PerformanceAnalysisStarted=Démarrage de l''analyse de la performance pour la vue "{0}".
#XTXT: Text for performance analysis error
ErrorFetchingPerformanceAnalysis=Une erreur s'est produite lors de l'extraction des données de l'analyse de la performance.
#XTXT: Text for performance analysis error
conflictingTask=La tâche d'analyse de la performance est déjà en cours d'exécution.
#XFLD: Label for Errors
Errors=Erreur(s)
#XFLD: Label for Warnings
Warnings=Avertissement(s)
#XFLD: Tooltip for missing privileges
openViewAnalyzerButtonTooltipNoPrivilege=Vous devez disposer du droit DWC_DATAINTEGRATION(update) pour ouvrir l'analyseur de vues.
#XFLD: Tooltip for missing privileges
generateExplainPlanButtonTooltipNoPrivilege=Vous devez disposer du droit DWC_RUNTIME(read) pour générer le fichier Explain plan.



#XFLD: Label for frequency column
everyLabel=Tous les/Toutes les
#XFLD: Plural Recurrence text for Hour
hoursLabel=Heures
#XFLD: Plural Recurrence text for Day
daysLabel=Jours
#XFLD: Plural Recurrence text for Month
monthsLabel=Mois
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minutes
