
#XBCB: Breadcrumb
viewMonitorBreadcrumb={spaceId}

#XFLD: Label for persisted view column
SOURCE=Origem
#XFLD: Label for persisted view column
NAME=Nome
#XFLD: Label for persisted view column
NAME_LABEL=Nome comercial
#XFLD: Label for persisted view column
NAME_LABELNew=Objeto (nome comercial)
#XFLD: Label for persisted view column
TECHINCAL_NAME=Nome técnico
#XFLD: Label for persisted view column
TECHINCAL_NAMENew=Objeto (nome técnico)
#XFLD: Label for persisted view column
DATA_PERSISTENCY=Acesso a dados
#XFLD: Label for persisted view column
STATUS=Estado
#XFLD: Label for persisted view column
LAST_UPDATED=Última atualização
#XFLD: Label for persisted view column
IN_MEMORY_SIZE=Memória utilizada para armazenamento (MiB)
#XFLD: Label for persisted view column
DISK_SIZE=Disco utilizado para armazenamento (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_NEWW=Tamanho na memória interna (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_HEADER=Tamanho na memória interna
#XFLD: Label for persisted view column
DISK_SIZE_NEWW=Tamanho no disco (MiB)
#XFLD: Label for persisted view column
DISK_SIZE_HEADER=Tamanho no disco
#XFLD: Label for schedule owner column
txtScheduleOwner=Proprietário do agendamento
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Mostra quem criou o agendamento
#XFLD: Label for persisted view column
PERSISTED=Persistido
#XFLD: Label for persisted view column
TYPE=Tipo
#XFLD: Label for View Selection Dialog column
changedOn=Alterado a
#XFLD: Label for View Selection Dialog column
createdBy=Criado por
#XFLD: Label for log details column
txtViewPersistencyLogs=Visualizar registos
#XFLD: Label for log details column
txtViewPersistencyLogsNew=Detalhes
#XFLD: text for values shown for Ascending sort order
SortInAsc=Ordenação ascendente
#XFLD: text for values shown for Descending sort order
SortInDesc=Ordenação descendente
#XFLD: Label for persisted view monitor heading
ViewMonitor_Heading=Monitor de vistas
#XFLD: Label for persisted view monitor sub-heading
ViewMonitor_SubHeading=Monitorizar e manter persistência de dados de vistas


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ DynamicPageHeader ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: text for values shown in column Persistence Status
txtLoading=Carregamento
#XFLD: text for values shown in column Persistence Status
txtRunning=Em execução
#XFLD: text for values shown in column Persistence Status
txtAvailable=Disponível
#XFLD: text for values shown in column Persistence Status
txtError=Erro
#XMSG: Error message for unknown Persistence type that gets logged in Kibana
unknownReplicationTypeError=O tipo de replicação "{0}" não é suportado.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Content ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Definições utilizadas para a última execução de persistência de dados:
#XMSG: Message for input parameter name
inputParameterLabel=Parâmetro de entrada
#XMSG: Message for input parameter value
inputParameterValueLabel=Valor
#XMSG: Message for persisted data
inputParameterPersistedLabel=Persistido às
#XTIT: Count for View in ViewMonitor Toolbar
txtViewCount=Vistas ({0})
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Persistência de vista
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Persistência de dados
#XBUT: Button to clear the selected view persistance
ClearViewPersistance=Limpar
#XBUT: Button to stop the selected view persistance
stopPersistance=Parar persistência
#XFLD: Placeholder for Search field
txtSearch=Procurar
#XBUT: Tooltip for refresh button
txtRefresh=Atualizar
#XBUT: Tooltip for add view button
txtDeleteView=Eliminar persistência
#XBUT: Tooltip for load new peristence
loadNewPersistence=Reiniciar persistência
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Carregar novo instantâneo
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Iniciar persistência de dados
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Remover dados persistentes
#XMSG: success message for starting persistence
startPersistenceSuccess=Estamos a persistir a vista "{0}".
#XMSG: success message for stopping persistence
stopPersistenceSuccess=Estamos a remover dados persistentes para a vista "{0}".
#XMSG: success message for stopping persistence
deletePersistedViewSuccess=Estamos a remover a vista "{0}" da lista de monitorização.
#XMSG: error message during start persistence of a view (backend error)
ERROR_STARTING_PERSISTENCE=Ocorreu um erro ao iniciar a persistência de dados para a vista "{0}".
#XMSG: error message during start persistence of a view (backend error)
PARAMERROR_STARTING_PERSISTENCE=Não é possível persistir a vista "{0}" pois contém parâmetros de entrada.
#XMSG: error message during start persistence of a view (backend error)
NUMBER_OF_PARAMETER_STARTING_PERSISTENCE=Não é possível persistir a vista "{0}" porque tem mais do que um parâmetro de entrada.
#XMSG: error message during start persistence of a view (backend error)
PARAMETER_DEFAULT_VALUE_STARTING_PERSISTENCE=Não é possível persistir a vista "{0}" porque o parâmetro de entrada não tem um valor predefinido.
#XMSG: error message during start persistence of a view (backend error)
REDEPLOYMENT_DAC_REQUIRED=A reimplementação do controlo de acesso a dados "{0}" é necessária para suportar a persistência de dados.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_STARTING_PERSISTENCE=Não é possível efetuar a persistência da vista "{0}" porque ela utiliza a vista "{1}" que tem controlo de acesso a dados.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_EXTERNAL_STARTING_PERSISTENCE=Não é possível efetuar a persistência da vista "{0}" porque ela utiliza uma vista com controlo de acesso a dados que pertence a um espaço diferente.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_STRUCTURE_STARTING_PERSISTENCE=Não é possível efetuar a persistência da vista "{0}" porque a estrutura de um ou mais do seus controlos de acesso a dados não suporta a persistência de dedados.
#XMSG: error message during stop persistence of a view (backend error)
STOP_PERSISTENCE_ERROR=Ocorreu um erro ao parar a persistência para a vista "{0}".
#XMSG: error message during stop persistence of a view (backend error)
DELETE_PERSISTED_VIEW_ERROR=Ocorreu um erro ao eliminar a vista persistida "{0}".
#XMSG: Warning message for removing the persisted data
RemovePersistency_Confirm_Msg=Pretende eliminar os dados persistentes e mudar para o acesso virtual da vista "{0}"?
#XMSG: Warning message for delete the view persistence
DeletePersistency_Confirm_Msg=Pretende remover a vista da lista de monitorização e eliminar os dados persistentes da vista {0}"?
#XMSG: error message for reading data from backend
txtReadBackendError=Parece que ocorreu um erro ao ler do back-end.
#XFLD: Label for No Data Error
NoDataError=Erro
#XMSG: message for conflicting task
Task_Already_Running=Uma tarefa em conflito já está em execução para a vista "{0}".

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Show All Views~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Text for show all views button
allViewsText=Todas as vistas ({0})
#XBUT: Text for show scheduled views button
scheduledText=Agendado ({0})
#XBUT: Text for show persisted views button
persistedText=Persistido ({0})
#XBUT: Text for start analyzer button
startAnalyzer=Iniciar analisador de vistas
#XFLD: Message if repository is unavailable
repositoryErrorMsg=O repositório não está disponível e algumas funcionalidades estão desativadas.

#XFLD: Data Access - Virtual
Virtual=Virtual
#XFLD: Data Access - Persisted
Persisted=Persistido

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Non-Persisted View Selection Dialog~~~~~~~~~~~~~~~~~~~~~~~

#XTIT: Title for Non-Persisted View selection dialog
select_source=Selecionar vista para persistir

#XTIT: Placeholder for Non-Persisted View selection dialog search
searchView=Procurar vistas
#XTIT: No data in the list of non-persisted view
No_Data=Sem dados
#XBUT: Button to select non-persisted view
ok=Ok
#XBUT: Button to close the non-persisted views selection dialog
cancel=Cancelar

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~View persistency log message key~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message for view persistency logs
START_STOP_PERSISTENCY=A iniciar a execução da tarefa de persistência de dados para "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY=A executar persistência de dados para a vista ''{1}''.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY)
START_VIEW_PERSISTENCY_PROCESS=A iniciar processo de persistência de dados para a vista "{0}".
#XFLD: Message for view persistency manual partition load
START_VIEW_PERSISTENCY_PROCESS_MANUAL_PARTITIONS=A iniciar processo de persistência de dados para a vista "{0}" com IDs de partição selecionados: "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY=A remover dados persistentes para a vista "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY)
REMOVE_VIEW_PERSISTENCY=A iniciar o processo para remover dados persistentes para a vista "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_SUCCESS=É efetuada a persistência dos dados para a vista "{1}".
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_SUCCESS)
START_VIEW_PERSISTENCY_PROCESS_SUCCESS=É efetuada a persistência dos dados para a vista "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY_SUCCESS=Dados persistentes removidos e acesso virtual aos dados restaurado para a vista "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_SUCCESS)
REMOVE_VIEW_PERSISTENCY_SUCCESS=Concluiu o processo para remover dados persistentes para a vista "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_FAILED=Impossível executar a persistência dos dados para a vista "{1}".
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_FAILED)
START_VIEW_PERSISTENCY_PROCESS_FAILED=Impossível executar a persistência dos dados para a vista "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.05)
STOP_VIEW_PERSISTENCY_FAILED=Impossível remover dados persistentes para a vista "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_FAILED)
REMOVE_VIEW_PERSISTENCY_FAILED=Impossível remover dados persistentes para a vista "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_SUCCESS="{3}" registos persistentes para a vista "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace START_PERSISTENCY_RECORD_COUNT_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=''{0} registos inseridos na tabela de persistência de dados para a vista "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS_V2={0} registos inseridos na tabela de persistência de dados para a vista "{1}" Memória utilizada: {2} GB.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_SUCCESS="{3}" registos persistentes removidos para a vista "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace STOP_PERSISTENCY_RECORD_COUNT_SUCCESS)
REMOVE_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=Dados persistentes removidos,"{0}" registos persistentes eliminados.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_FAILED=Falha ao obter a contagem de registos para a vista "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_FAILED=Falha ao obter a contagem de registos para a vista "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=A agenda é eliminada para "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS)
REMOVE_VIEW_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=A agenda é eliminada para a vista "{0}".
#XFLD: Message for view persistency logs
STOP_PERSISTENCY_DELETE_SCHEDULE_FAILED=Falha ao eliminar agenda para "{1}".
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_REDEPLOY=Não conseguimos efetuar a persistência da vista "{0}" porque foi alterada e implementada desde que iniciou a persistência. Tente novamente efetuar a persistência da vista ou aguarde até à execução seguinte agendada.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_DELETE=Não conseguimos efetuar a persistência da vista "{0}" porque foi eliminada desde que iniciou a persistência.
#XFLD: Message for successful data persistency of a single partition (for messages created before version 2022.03)
START_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} registos persistentes em partição para valores "{1}" <= {2} < "{3}".
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (new message to replace START_PERSISTENCY_SINGLE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} registos inseridos na partição para valores "{1}" <= "{2}" < "{3}".
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS_V2={0} registos introduzidos na partição para valores "{1}" <= "{2}" < "{3}". Memória utilizada: {4} GB.
#XFLD: Message for successful replication of a "others" partition (for messages created before version 2022.03)
START_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} registos persistidos para a partição ''outros''.
#XFLD: Message used by SEAL repository for successful replication of a "others" partition (new message to replace START_PERSISTENCY_OTHERS_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} registos inseridos na partição "outros".
#XFLD: Message for view persistency record count of partitions (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={3} registos persistentes para a vista "{1}" em {4} partições.
#XFLD: Message used by SEAL repository for view persistency record count of partitions (new message to replace START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={0} registos inseridos na tabela de persistência de dados para a vista "{1}" em {2} partições.
#XFLD: Message used by SEAL repository for view persistency for partition wise refresh
START_VIEW_PERSISTENCY_RECORD_COUNT_REFRESHED_PARTITIONS_SUCCESS={0} registos inseridos na tabela de persistência de dados para a vista "{1}". Partições atualizadas: {2}; Partições bloqueadas:{3}; Total de partições: {4}
#XFLD: Message used by SEAL repository for view persistency for manual partition load
START_VIEW_PERSISTENCY_RECORD_COUNT_MANUAL_PARTITIONS_SUCCESS={0} registos inseridos na tabela de persistência de dados para a vista "{1}" em {2} partições selecionadas.
#XFLD: Message used by SEAL repository for view persistency for locked partition data load
START_VIEW_PERSISTENCY_RECORD_COUNT_LOCKED_PARTITIONS_SUCCESS={0} registos inseridos na tabela de persistência de dados para a vista "{1}". Partições atualizadas: {2}; Partições não alteradas bloqueadas: {3}; Total de partições: {4}.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
START_PERSISTENCY_UNEXPECTED_ERROR=Ocorreu um erro inesperado ao efetuar a persistência de dados para a vista "{0}".
#XMSG: Message for unexpected error (new message to replace START_PERSISTENCY_UNEXPECTED_ERROR)
START_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Ocorreu um erro inesperado ao efetuar a persistência de dados para a vista "{0}".
#XMSG: Message for Locked space error
START_VIEW_PERSISTENCY_LOCKED_SPACE_ERROR=Não é possível efetuar a persistência da vista "{0}” porque o espaço "{1}" está bloqueado.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
STOP_PERSISTENCY_UNEXPECTED_ERROR=Ocorreu um erro inesperado ao remover os dados persistentes para a vista "{0}".
#XMSG: Message for unexpected error (new message to replace STOP_PERSISTENCY_UNEXPECTED_ERROR)
REMOVE_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Ocorreu um erro inesperado ao remover a persistência para a vista "{0}".
#XMSG: Message for ivalidated view
VIEW_PERSISTENCY_ABORTED_DEFINITION_INVALID=A definição da vista "{0}" tornou-se inválida, muito provavelmente devido a uma alteração de um objeto consumido direta ou indiretamente pela vista. Tente reimplementar a vista para resolver o problema ou identificar a causa raiz.
#XMSG: Message for persisted data removal (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT=Os dados persistentes são removidos ao implementar a vista "{0}".
#XMSG: Message for persisted data removal of a consuming view
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW=Os dados persistentes são removidos ao implementar a vista consumida "{0}".
#XMSG: Message for persisted data removal of a consuming view due to a dac change
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW_DAC_CHANGED=Os dados persistentes são removidos ao implementar a vista consumida "{0}" porque o controlo de acesso a dados foi alterado.
#XMSG: Message for persisted data removal (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DEPLOYMENT=Os dados persistentes são removidos ao implementar a vista "{0}".
#XMSG: Message for deleted view (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION=A persistência é removida com a eliminação da vista "{0}".
#XMSG: Message for deleted view (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DELETION=A persistência é removida com a eliminação da vista "{0}".
#XMSG Message for removal of persisted data, because prerequisites not fulfilled
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_PREREQUISITES_NOT_FULFILLED=Os dados persistentes são removidos porque os pré-requisitos de persistência de dados já não são cumpridos.
#XMSG: Message for inconsistent persistency
VIEW_PERSISTENCY_INCONSISTENT_PERSISTENCY=A persistência da vista "{0}" tornou-se inconsistente. Remova os dados persistidos para resolver o problema.
#XMSG: Message for prerequisites
START_PERSISTENCY_CHECK_PREREQUISITES=A verificar os pré-requisitos de persistência da vista "{0}".
#XMSG: Message for DAC deployed in deprecated way using table function and join (for messages created before 2025.05)
START_PERSISTENCY_DEPRECIATED_DAC=A vista "{0}" é implementada com o controlo de acesso a dados que está a ser preterido. Implemente a vista novamente para melhorar o desempenho.
#XMSG: Message for DAC deployed in deprecated way using table function and join
START_PERSISTENCY_DEPRECATED_DAC=A vista "{0}" é implementada com o controlo de acesso a dados que está a ser preterido. Implemente a vista novamente para melhorar o desempenho.
#XMSG: Message for roll back (for messages created before version 2022.03)
ROLLBACK_VIEW_PERSISTENCY=Ocorreu um erro. Estado anterior da persistência restaurado para a vista "{0}".
#XMSG: Message used by SEAL repository for rollback of running procedure to persist view (new message to replace ROLLBACK_VIEW_PERSISTENCY)
VIEW_PERSISTENCY_ROLLBACK=Ocorreu um erro. O processo para persistir a vista ''{0}'' foi parado e as alterações foram anuladas.
#XMSG: Message used by SEAL repository for rollback of running procedure to remove persisted data of view
REMOVE_VIEW_PERSISTENCY_ROLLBACK=Ocorreu um erro. O processo para remover os dadospersistidos da vista "{0}" foi parado e as alterações foram anuladas.
#XMSG: Message used by SEAL repository for persistency preparation
START_PERSISTENCY_PREPARATION=A preparar a persistência dos dados.
#XMSG: Message used by SEAL repository for data insertion
START_INSERTING_PERSISTENCY_DATA=A inserir dados na tabela de persistência.
#XMSG: Message for null values - partitioning (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS={0} registos de valor nulo inseridos na partição "outros".
#XMSG: Message used by SEAL repository for others partition values - partitioning (new message to replace START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS={0} registos inseridos na partição ''outros'' para valores"{2}" < "{1}" OR "{2}" >= "{3}".
#XMSG: Message used by SEAL repository for others partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS_V2={0} registos inseridos na partição "outros" para valores "{2}" < "{1}" OR "{2}" >= "{3}". Memória utilizada: {4} GiB.
#XMSG: Message used by SEAL repository for null partition values - partitioning
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS={0} registos inseridos na partição "outros" para valores "{1}" IS NULL.
#XMSG: Message used by SEAL repository for null partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS_V2={0} registos inseridos na partição "outros" para valores ''{1}'' IS NULL. Memória utilizada: {2} GB.
##XFLD:Message used by SEAL repository for remote statistics
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS=A carregar os dados envolvidos: {0} instruções remotas. Total de registos obtidos: {1}. Tempo de duração total: {2} segundos.
##XFLD:Message used by SEAL repository for remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS_WITH_PARTITIONS=A carregar os dados envolvidos, utilizando {0} partições com {1} instruções remotas. Total de registos obtidos: {2}. Tempo de duração total: {3} segundos.
##XFLD:Message used by SEAL repository with UI ID for instructions of remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_INSTRUCTION=As instruções remotas processadas durante a execução podem ser visualizadas abrindo o monitor de consulta remota nos detalhes das mensagens específicas de partição.
##XFLD:Message for start reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS=A iniciar processo para reutilizar dados persistentes existentes para a vista {0} após a implementação.
##XFLD:Message used by SEAL repository for reattach view persitency
START_VIEW_PERSISTENCY_REATTACHMENT=Comece a reutilizar os dados persistentes existentes.
##XFLD:Message used by SEAL repository for reattach view persitency
START_FINILIZING_VIEW_PERSISTENCY_REATTACHMENT=A reutilização os dados persistentes existentes.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_COMPLETE=O processo para reutilizar dados persistentes está concluído para a vista {0}.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_FAILED=Falha ao reutilizar os dados persistentes existentes para a vista {0} após a implementação.
#XMSG: Message used by SEAL repository for finalizing persistency
START_FINALIZING_PERSISTENCY=A finalizar persistência.
#XMSG: Message used by SEAL repository for restoring virtual access for view
REMOVE_VIEW_PERSISTENCY_VIRTUAL_ACCESS_RESTORED=O acesso virtual aos dados é restaurado para a vista ''{0}''.
#XMSG: Message used by SEAL repository for view that already has virtual access
REMOVE_VIEW_PERSISTENCY_ALREADY_VIRTUAL_ACCESS=A vista "{0}" já tem acesso virtual aos dados. Não são removidos dados persistidos.
#XMSG: Message for removing view persistency
VIEW_PERSISTENCY_REMOVED_FROM_MONITOR=A vista ''{0}'' foi removida do monitor Vistas.
#XMSG: Message for view doesnt exist.
VIEW_PERSISTENCY_VIEW_NOT_EXISTING=A vista "{0}"não existe na base de dados ou não está implementada corretamente e, por isso, não pode ser persistida. Tente reimplementar a vista para resolver o problema ou identificar a causa raiz.
#XMSG: Message for persistency not enabled.
VIEW_PERSISTENCY_NOT_ENABLED=A persistência de dados não está ativada. Reimplemente uma tabela/vista no espaço "{0}" para ativar a funcionalidade.
#XMSG: Message for unexpected error due to node crash
VIEW_PERSISTENCY_UNEXPECTED_ERROR_GENERAL=A última execução da persistência da vista foi interrompida devido a erros técnicos.
#XFLD: Message for view persistency memory consumption
VIEW_PERSISTENCY_MEMORY_CONSUMPTION={0} GB de memória de pico utilizados no tempo de execução da persistência da vista.
#XFLD: Message for view persistency timeout
VIEW_PERSISTENCY_TIMEOUT=A persistência de vista {0} atingiu o tempo limite de {1} horas
#XFLD: Message for not started asynchronous view persistency
VIEW_PERSISTENCY_ASYNC_NOT_STARTED=Uma carga elevada do sistema impediu que a execução assíncrona da persistência da vista fosse iniciada. Verifique se estão a ser executadas demasiadas tarefas em paralelo.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency (for messages created before version 2022.05)
START_VIEW_PERSISTENCY_EXECUTION_METHOD_DROP_AND_RENAME=A tabela persistida existente foi eliminada e substituída por uma nova tabela persistida.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_RECREATE_AND_DROP=A tabela persistida existente foi eliminada e substituída por uma nova tabela persistida.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_TRUNCATE_AND_COPY=A tabela persistida existente foi atualizada com novos dados.
##XFLD: Message for missing authorizations for view persistency
VIEW_PERSISTENCY_MISSING_AUTH=Faltam autorizações para a persistência de dados.
##XFLD: Message start of persistency cancellation
VIEW_PERSISTENCY_TRIGGER_CANCEL=A iniciar o cancelamento do processo para persistir a vista {0}.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_UNAVAILABLE_TASK_RUN=Falha ao cancelar o processo de persistência da vista porque não existe uma tarefa de persistência de dados em execução para a vista {0}.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_NONDETERMINISTIC_TASK_RUN=Falha ao cancelar o processo de persistência da vista porque não existe uma tarefa de persistência de dados em execução para a vista {0}.
##XFLD: Message for canceling a non running persitency task
VIEW_PERSISTENCY_CANCEL_NON_RUNNING_SELECTED_TASK=Falha ao cancelar o processo de persistência da vista {0}, porque a tarefa de persistência de dados selecionada {1} não está em execução.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_UNREACHABLE_PERSISTENCY_RUN=Falha ao cancelar o processo para persistir a vista porque a persistência de dados para a vista {0} ainda não foi iniciada.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_COMPLETED_PERSISTENCY_RUN=Falha ao cancelar o processo para persistir a vista {0} porque já foi concluída.
##XFLD: Message for failed cancellation
VIEW_PERSISTENCY_ABORTED_CANCEL_EXECUTION=Falha ao cancelar o processo para persistir a vista {0}.
##XFLD: Message for finalizing persistency cancellation
VIEW_PERSISTENCY_FINALIZE_CANCEL=O processo para parar a persistência de dados da vista {0} foi submetido.
##XFLD: Message used in persist task when cancellation is caught
VIEW_PERSISTENCY_CANCELED=O processo para persistir a vista {0} foi parado.
##XFLD: Message used in persist task when cancelation is caught and performed through a cancelation task
VIEW_PERSISTENCY_CANCELED_VIA_CANCEL_TASK=O processo para persistir a vista {0} foi parado através da tarefa de cancelamento {1}.
##XFLD: Message used in cancel task when the task is triggered by deployment
VIEW_PERSISTENCY_CANCEL_TRIGGERED_BY_DEPLOYMENT=A cancelar o processo para persistir dados ao implementar a vista {0}.
##XFLD: Message used in case the cancelation is retriggered for a running task
VIEW_PERSISTENCY_CANCEL_ATTEMPT_RETRIGGER_CANCEL=Já foi submetida uma tarefa de cancelamento anterior para a persistência da vista {0}.
##XFLD: Message used to express the delay that may occur before the persistency task is stopped
VIEW_PERSISTENCY_CANCEL_DELAY_STOPPING_PERSISTENCY=Poderá existir um atraso até que a tarefa de persistência de dados para a vista {0} seja parada.
##XFLD: Message used to reflect the task log identifier of the current running persistency task
VIEW_PERSISTENCY_CANCEL_CURRENT_RUNNING_TASK=Os dados para a vista {0} estão a ser persistidos com a tarefa {1}.
##XFLD:Message used by SEAL repository for locked partitions and DACs
VIEW_PERSISTENCY_LOCKED_PARTITIONS_DACS=As autorizações fornecidas pelos controlos de acesso a dados podem ser ter sido alteradas e não são consideradas por partições bloqueadas. Desbloqueie as partições e carregue um novo instantâneo para aplicar as alterações.
##XFLD:Message used by SEAL repository for column structure change
VIEW_PERSISTENCY_COLUMN_STRUCTURE_CHANGE=A estrutura da coluna foi alterada e já não corresponde à tabela de persistência existente. Remova os dados persistentes e comece uma nova persistência de dados para que a sua tabela de persistência seja atualizada.
##XFLD: Message for out of memory
VIEW_PERSISTENCY_HANA_OUT_OF_MEMORY=A tarefa falhou devido a um erro de memória esgotada na base de dados SAP HANA.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_EXCEPTION_TRANSACTION_ROLLBACK=A tarefa falhou devido a uma exceção interna na base de dados SAP HANA.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_SQL_EXECUTION_ISSUE=A tarefa falhou devido a um problema de execução de SQL interno na base de dados SAP HANA.
##XFLD: Message for HANA out of memory result
VIEW_PERSISTENCY_OUT_OF_MEMORY_RESULT_MESSAGE=Motivo de evento de memória esgotada HANA: {0}
##XFLD: Message for admission control rejection
VIEW_PERSISTENCY_HANA_ADMISSION_CTRL_REJECTION=A tarefa falhou devido a uma rejeição do controlo de admissão SAP HANA.
##XFLD: Message for HANA back pressure / rate limit of HANA client
VIEW_PERSISTENCY_HANA_CLIENT_RATE_LIMIT_REJECTION=A tarefa falhou por haver demasiadas ligações SAP HANA ativas.
#XMSG: Message for invalid persisted table name
VIEW_PERSISTENCY_PERSISTED_TABLE_INVALID=Ocorreu um erro e a tabela persistida tornou-se inválida. Para resolver o problema, remova os dados persistidos e guarde a vista de forma persistente outra vez.
#XMSG: Message for usahe of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION=A vista não pode ser persistida. Utiliza uma tabela remota baseada numa origem remota com propagação de utilizadores ativada. Verifique a linhagem da vista.
#XMSG: Message for dynamic usage of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION_DYNAMIC=A vista não pode ser persistida. Utiliza uma tabela remota baseada numa origem remota com propagação de utilizadores ativada. A tabela remota pode ser consumida de forma dinâmica por meio de uma vista de script SQL. A linhagem da vista não pode ser mostrada na tabela remota.
#XMSG: Message for insufficient privileges or consumption of view with DAC via dynamic SQL
VIEW_PERSISTENCY_INSUF_PRIV_OR_DAC_DYN_SQL=Os seus privilégios podem ser insuficientes. Abra a pré-visualização de dados para ver se tem os privilégios necessários. Em caso afirmativo, uma segunda vista consumida através do script  SQL dinâmico pode ter o controlo de acesso a dados aplicado.
#XMSG: deployment of DAC is deprecated and view need to be redeployed to be able to persist data
VIEW_PERSISTENCY_DEPRECATED_DAC=A vista "{0}" é implementada com o controlo de acesso a dados que está a ser preterido. Implemente a vista novamente para poder executar a persistência de dados para a vista.
##XFLD:Message used by SEAL repository for input parameter
START_VIEW_PERSISTENCY_INPUT_PARAMETER=Utilizar valor predefinido "{0}" para parâmetro de entrada "{1}".
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Elastic compute node keys~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
##XFLD:Message used to reflect disablement of ecn replica
ECN_DISABLE_EXISTING_REPLICA=A réplica do nó de computação elástico está desativada.
##XFLD:Message used to reflect recreation of ecn replica
ECN_RECREATE_EXISTING_REPLICA=A réplica do nó de computação elástico está recriada.
##XFLD:Message used to reflect re-enablement of ecn replica
ECN_ENABLE_EXISTING_REPLICA=A réplica do nó de computação elástico está reativada.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Task Schedule Menu key~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message to schedule task
scheduleText=Agenda
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Criar agenda
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Editar agenda
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Eliminar agenda
#XFLD: Refresh frequency field
refreshFrequency=Frequência de atualização
#XFLD: Refresh frequency field
refreshFrequencyNew=Frequência
#XFLD: Refresh frequency field
refreshFrequencyNewNew=Frequência agendada
#XBUT: label for None
none=Nenhum
#XBUT: label for Real-Time replication state
realtime=Tempo real
#XFLD: Label for table column
txtNextSchedule=Execução seguinte
#XFLD: Label for table column
txtNextScheduleNew=Execução seguinte agendada
#XFLD: Label for table column
txtNumOfRecords=Número de registos
#XFLD: Label for scheduled link
scheduledTxt=Agendado
#XFLD: LABEL for partially persisted link
partiallyPersisted=Persistido parcialmente
#XFLD: Text for paused text
paused=Interrompido

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ View Monitor Tasklog Details Action keys~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message for release lock actionn
releaseLockMsg=Se uma execução demorar mais tempo que o habitual, isso pode indicar que ela falhou e que o estado não foi atualizado em conformidade. \r\n Para resolver o problema, pode libertar o bloqueio e definir o estado como falhado.
#XFLD: Label for release lock dialog
releaseLockText=Libertar bloqueio

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Column header tooltip ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: tooltip for table column
txtViewTooltip=Nome da vista persistida
#XFLD: tooltip for table column
txtViewDataAccessTooltip=Isto indica a disponibilidade da vista
#XFLD: tooltip for table column
txtViewRefreshFreqTooltip=Isto indica se foi definida uma agenda para a vista
#XFLD: tooltip for table column
txtViewStatusTooltip=Obter o estado da vista persistida
#XFLD: tooltip for table column
txtViewLatestUpdateTooltip=Fornece informações sobre quando a vista persistida foi atualizada pela última vez
#XFLD: tooltip for table column
txtViewNextRunTooltip=Se estiver definida uma agenda para a vista, veja quando está agendada a execução seguinte
#XFLD: tooltip for table column
txtViewNumOfRecordsTooltip=Controlar número de registos.
#XFLD: tooltip for table column
txtViewInMemorySizeTooltip=Controle o espaço usado pela vista na memória
#XFLD: tooltip for table column
txtViewDiskSizeTooltip=Controle o espaço usado pela vista no disco
#XMSG: Expired text
txtExpired=Expirado

#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Não é possível adicionar o objeto "{0}" à cadeia de tarefas.

#~~~~~~~~~~~View Analyzer task log messages~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message indicating number of records for a view, and memory consumption in Mebibyte during simulation of view persistency
RECORDS_PEAK_MEMORY=A vista "{0}" tem {1} registos. Uma simulação de persistência de dados para esta vista utilizou {2} MiB de memória.
#XMSG: View Analyzer execution failure
VIEW_ANALYZER_EXECUTION_FAILED=Falha ao executar o analisador de vistas.
#XMSG: Missing authorizations for View Analyzer
VIEW_ANALYZER_MISSING_AUTH=Faltam autorizações para o analisador de vistas.
#XMSG: Maximum memory in Gibibyte is reached, therefore persistency simulation is stopped
MAXIMUM_MEMORY_REACHED=A memória máxima de {0} GiB foi atingida ao simular a persistência de dados para a vista "{1}". Por isso, não serão executadas mais simulações de persistência de dados.
#XMSG: Error during persistency simulation occurred
PERSISTENCY_SIMULATION_ERROR=Ocorreu um erro durante a simulação da persistência de dados para a vista "{0}".
#XMSG: Persistency simulation is not executed, because prerequisites are not fulfilled and the view cannot be persisted
PREREQUISITES_NOT_FULFILLED=A simulação da persistência de dados não é executada para a vista "{0}" porque os pré-requisitos não estão cumpridos e a vista não pode ser persistida.
#XMSG: Persistency simulation is not executed, because the view does not exist in the database
VIEW_DOES_NOT_EXIST=Deve implementar a vista "{0}" para ativar a simulação da persistência dos dados.
#XMSG: Number of records cannot be determined, because the local table does not exist in the database
TABLE_DOES_NOT_EXIST=A tabela local "{0}" não existe na base de dados, pelo que não é possível determinar o número de registos para esta tabela.
##XMSG: Message start of cancellation for View Analyzer task
CANCEL_VIEW_ANALYZER_TRIGGERED=O processo para parar a tarefa do analisador de vistas {0} para a vista"{1}" foi submetido. Poderá haver um atraso até que a tarefa seja parada.
##XMSG: Message start to indicate that task is not active
CANCEL_VIEW_ANALYZER_NO_ACTIVE_TASK=A tarefa do analisador de vistas {0} para a vista "{1}" não está ativa.
#XMSG: Cancel View Analyzer task failure
CANCEL_VIEW_ANALYZER_FAILED=Falha ao cancelar a tarefa do analisador de vistas.
##XMSG: Message used in View Analyzer execution task when task is canceled
VIEW_ANALYZER_CANCELED=A execução do analisador de vistas para a vista "{0}" foi parada através de uma tarefa de cancelamento.
##XMSG: Message start of cancelation for Model Validation task
CANCEL_MODEL_VALIDATION_TRIGGERED=O processo para parar a tarefa de validação de modelo {0} para a vista "{1}" foi submetido. Poderá haver um atraso até que a tarefa seja parada.
##XMSG: Message to indicate that task is not active
CANCEL_MODEL_VALIDATION_NO_ACTIVE_TASK=A tarefa de validação de modelo {0} para a vista "{1}" não está ativa.
#XMSG: Cancel Model Validation task failure
CANCEL_MODEL_VALIDATION_FAILED=Falha ao cancelar a tarefa de validação de modelo.
##XMSG: Message used in Model Validation task when task is canceled
MODEL_VALIDATION_CANCELED=A execução da validação de modelo para a vista "{0}" foi parada através de uma tarefa de cancelamento.
#XMSG: Message for Locked space error, before starting Model Validation task
MODEL_VALIDATION_LOCKED_SPACE_ERROR=Impossível executar a validação de modelo para a vista "{0}" porque o espaço "{1}" está bloqueado.
##XMSG: Message used in View Analyzer execution task when determination of number of rows fails
NUMBER_ROWS_DETERMINATION_FAILED=Ocorreu um erro ao determinar o número de linhas para a tabela local "{0}".
##XMSG: Message to indicate that SQL plan was created
EXECUTION_PLAN_CREATED=O ficheiro do plano do analisador SQL para a vista "{0}" foi criado e pode ser transferido.
#XFLD: Message to indicate starting of process to generate SQL Analyzer plan file
START_PLANVIZ_EXECUTION=A iniciar o processo para gerar o ficheiro do plano do analisador SQL para a vista "{0}".
#XFLD: Message to indicate starting of View Analyzer process
VIEW_ANALYZER_EXECUTION_STARTED=A iniciar execução de analisador de vistas.
##XMSG: Message to indicate that SQL plan file cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_PLANVIZ_NOT_FULFILLED=O ficheiro do plano do analisador SQL não pode ser gerado para a vista ''"{0}" porque os pré-requisitos de persistência de dados não foram cumpridos.
##XMSG: Error message during  generation of SQL Analyzer plan file
PLANVIZ_ERROR=Ocorreu um erro durante a geração do ficheiro do plano do analisador SQL para a vista "{0}".
#XMSG: Message for Locked space error
VIEW_ANALYZER_LOCKED_SPACE_ERROR=Impossível executar o analisador de vistas para a vista "{0}" porque o espaço "{1}" está bloqueado.
#XMSG: Message for persistency simulation without partitioning
PERSISTENCY_SIMULATION_WITHOUT_PARTITIONS=As partições da vista "{0}" não são consideradas durante a simulação da persistência de dados.
#XMSG: Message for generation of SQL Analyzer plan file without considering partitioning
PLANVIZ_WITHOUT_PARTITIONS=As partições da vista "{0}" não são consideradas durante a geração do ficheiro do plano do analisador SQL.
#XMSG: Confirmation message for mass removal of persisted data
MassRemovePersistency_Confirm_Msg=Pretende remover os dados persistentes e mudar o acesso aos dados de volta para o acesso virtual?
#XMSG: Confirmation message for partial mass removal of selected persisted data
PartialMassRemovePersistency_Confirm_Msg={0} das {1} vistas selecionadas têm dados persistentes. \n Pretende remover os dados persistentes e mudar o acesso aos dados de volta para o acesso virtual?
#XMSG: success message for stopping mass persistence
stopMassPersistenceSuccess=Estamos a remover os dados persistentes para as vistas selecionadas.
#XMSG: error message during mass stop persistence of a view (backend error)
STOP_MASS_PERSISTENCE_ERROR=Ocorreu um erro ao parar a persistência para as vistas selecionadas.
#XMSG: memory analysis is only performed in space {0}
NO_MEMORY_ANALYSIS=A análise de memória só é efetuada para entidades no espaço "{0}": "{1}" "{2}" foi ignorada.
##XMSG: Message to indicate that Explain Plan cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_EXPLAINPLAN_NOT_FULFILLED=O ficheiro do plano de explicação não pode ser gerado para a vista ''"{0}", porque os pré-requisitos de persistência não foram cumpridos.
#XMSG: Message for Explain Plan generation without partitioning
EXPLAINPLAN_WITHOUT_PARTITIONS=Partições da vista "{0}" não são consideradas durante a geração do ficheiro de plano de explicação.
#XFLD: Message to indicate starting of process to generate Explain Plan file
START_EXPLAINPLAN_EXECUTION=A iniciar o processo para gerar o ficheiro do plano de explicação para a vista "{0}".
##XMSG: Message to indicate that Explain Plan was created
EXPLAINPLAN_CREATED=O ficheiro do plano de explicação para a vista "{0}" foi gerado. Pode apresentá-lo, clicando em "Ver detalhes" ou transferi-lo se tiver a permissão relevante.
##XMSG: Error message during  generation of Explain Plan file
EXPLAINPLAN_ERROR=Ocorreu um erro durante a geração do ficheiro do plano de explicação para a vista "{0}".
##XMSG: Explain Plan file cannot be generated for the view {0}, because too many views are stacked on each other
EXPLAINPLAN_MODEL_TOO_COMPLEX=O ficheiro do plano de explicação não pode ser gerado para a vista "{0}". Estão sobrepostas demasiadas vistas umas sobre as outras. Os modelos complexos podem causar erros de falta de memória e um desempenho lento. Recomendamos a persistência de uma vista.

##XMSG: Message to indicate that the space is locked
PERFORMANCE_ANALYSIS_LOCKED_SPACE_ERROR=Não é possível executar a análise de desempenho para a vista "{0}" porque o espaço "{1}" está bloqueado.
##XMSG: The performance analysis task was cancelled
PERFORMANCE_ANALYSIS_CANCELED=A análise de desempenho para a vista "{0}" foi cancelada.
##XMSG: The performance analysis failed
PERFORMANCE_ANALYSIS_FAILED=A análise de desempenho falhou.
##XMSG: The performance analysis is finished
PERFORMANCE_ANALYSIS_FINISHED=A análise de desempenho para a vista "{0}" foi concluída. Apresente o resultado, clicando em "Ver detalhes".
##XMSG: The view has input parameter with no default value
INPUT_PARAMETER_WITHOUT_DEFAULT_VALUE=Esta vista não pode ser analisada porque tem um parâmetro sem um valor predefinido.
##XMSG: The view is not fully deployed
VIEW_NOT_FULLY_DEPLOYED=Esta vista não pode ser analisada porque não está totalmente implementada.
##XMSG: The view uses limited remote adapters
LIMITED_REMOTE_ADAPTER=Esta vista utiliza pelo menos um adaptador remoto com funcionalidades limitadas, como propagação de filtros ou suporte em falta para 'Contagem'.
##XMSG: The view uses remote adapter that does not support 'Limit'
REMOTE_ADAPTER_NOT_SUPPORTING_LIMIT=Esta vista utiliza pelo menos um adaptador remoto que não suporta o 'Limite'. Podem ter sido selecionados mais de 1000 registos.
##XMSG: Performance analysis is executed by using default parameters of view'
INPUT_PARAMETER_WITH_DEFAULT_VALUE=A análise de desempenho é executada utilizando os valores predefinidos de parâmetros de vista.
##XMSG: Message to indicate that an error occurred
PERFORMANCE_ANALYSIS_ERROR=Ocorreu um erro durante a análise de desempenho para a vista "{0}".
##XMSG: Message start of cancellation for Performance Analysis task
CANCEL_PERFORMANCE_ANALYSIS_TRIGGERED=O processo para parar a tarefa de análise de desempenho {0} para a vista"{1}" foi submetido. Poderá haver um atraso até que a tarefa seja parada.
##XMSG: Message start to indicate that task is not active
CANCEL_PERFORMANCE_ANALYSIS_NO_ACTIVE_TASK=A tarefa de análise de desempenho {0} para a vista "{1}" não está ativa.
#XMSG: Cancel Performance Analysis task failure
CANCEL_PERFORMANCE_ANALYSIS_FAILED=Falha ao cancelar a tarefa de análise de desempenho.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Atribuir-me o agendamento
#XBUT: Pause schedule menu label
pauseScheduleLabel=Interromper o agendamento
#XBUT: Resume schedule menu label
resumeScheduleLabel=Retomar o agendamento
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Ocorreu um erro ao remover os agendamentos.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Ocorreu um erro ao atribuir os agendamentos.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Ocorreu um erro ao interromper os agendamentos.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Ocorreu um erro ao retomar os agendamentos.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=A eliminar {0} agendamentos
#XMSG: Message for starting mass assign of schedules
massAssignStarted=A alterar o proprietário de {0} agendamentos
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=A interromper {0} agendamentos
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=A retomar {0} agendamentos
#XBUT: Select Columns Button
selectColumnsBtn=Selecionar colunas
#XFLD: Refresh tooltip
TEXT_REFRESH=Atualizar
#XFLD: Select Columns tooltip
text_selectColumns=Selecionar colunas


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Runtime Metrics ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Label for Runtime Metrics
RuntimeMetricsTitle=Métricas de tempo de execução para
#XFLD : Label for Run Button
runButton=Executar
#XFLD : Label for Cancel Button
cancelButton=Cancelar
#XFLD : Label for Close Button
closeButton=Fechar
#XFLD : Label for Open View Analyzer Button
openViewAnalyzerButton=Abrir analisador de vistas
#XFLD : Label for Generate Explain Plan Button
generateExplainPlanButton=Gerar plano de explicação
#XFLD : Label for Previous Run Column
previousRun=Execução anterior
#XFLD : Label for Latest Run Column
latestRun=Última execução
#XFLD : Label for time Column
time=Hora
#XFLD : Label for Duration Column
duration=Duração
#XFLD : Label for Peak Memory Column
peakMemory=Memória de pico
#XFLD : Label for Number of Rows
numberOfRows=Número de linhas
#XFLD : Label for Overall Number of Sources
overallNumberOfSources=Número global de origens
#XFLD : Label for Data Access Column
dataAccess=Acesso a dados
#XFLD : Label for Local Tables
localTables=Tabelas locais
#XFLD : Label for federatedRemoteTables
federatedRemoteTables=Tabelas remotas federadas (com funcionalidades de adaptador limitadas)
#XTXT Text for initial state of the runtime metrics
initialState=Primeiro, tem de executar a análise do desempenho para obter as métricas. Isto pode demorar algum tempo, mas, se necessário, pode cancelar o processo.
#XMSG: Cancel confirmation message
CancelRunConfirmationMessage=Tem a certeza de que pretende cancelar a execução atual da análise de desempenho?
#XTIT: Cancel dialog title
CancelRunTitle=Cancelar execução
#XFLD: Label for Number of Rows
NUMBER_ROWS=Número de linhas
#XFLD: Label for Overall Number of Sources
OVERALL_NUMBER_SOURCES=Número global de origens
#XFLD: Label for Data Access
DATA_ACCESS=Acesso a dados
#XFLD: Label for Remote tables
NUMBER_REMOTE_TABLES_LIMITED_ADAPTERS=Tabelas remotas federadas (com funcionalidades de adaptador limitadas)
#XFLD: Label for select statement
SELECT_STATEMENT='SELECT * FROM VIEW LIMIT 1000'
#XFLD: Label for duration
SELECT_RUNTIME=Duração
#XFLD: Label for peak memory
SELECT_PEAK_MEMORY=Memória de pico
#XFLD: Label for count statement
SELECT_COUNT_STATEMENT='SELECT COUNT(*) FROM VIEW'
#XFLD: Label for count duration
SELECT_COUNT_RUNTIME=Duração
#XFLD: Label for count peak memory
SELECT_COUNT_PEAK_MEMORY=Memória de pico
#XFLD: Label for count of number of local tables
NUMBER_LOCAL_TABLES_FILE=Tabelas locais (ficheiro)
#XTXT: Text for running state of the runtime metrics
Running=Em execução...
#XFLD: Label for time
Time=Hora
#XFLD: Label for virtual access
PA_VIRTUAL=Virtual
#XFLD: Label for persisted access
PA_PERSISTED=Persistido
PA_PARTIALLY_PERSISTED=Persistido parcialmente
#XTXT: Text for cancel
CancelRunSuccessMessage=A cancelar a execução da análise de desempenho.
#XTXT: Text for cancel error
CancelRunErrorMessage=Ocorreu um erro ao cancelar a execução da análise de desempenho.
#XTXT: Text for explain plan generation
ExplainPlanStarted=A gerar o plano de explicação para a vista "{0}".
#XTXT: Text for performance analysis
PerformanceAnalysisStarted=A iniciar a análise de desempenho para a vista "{0}".
#XTXT: Text for performance analysis error
ErrorFetchingPerformanceAnalysis=Ocorreu um erro ao obter os dados da análise de desempenho.
#XTXT: Text for performance analysis error
conflictingTask=A tarefa da análise de desempenho já está em execução
#XFLD: Label for Errors
Errors=Erro(s)
#XFLD: Label for Warnings
Warnings=Aviso(s)
#XFLD: Tooltip for missing privileges
openViewAnalyzerButtonTooltipNoPrivilege=Precisa do privilégio DWC_DATAINTEGRATION(update) para abrir o analisador de vistas.
#XFLD: Tooltip for missing privileges
generateExplainPlanButtonTooltipNoPrivilege=Precisa do privilégio DWC_RUNTIME(read) para gerar o plano de explicação.



#XFLD: Label for frequency column
everyLabel=A cada
#XFLD: Plural Recurrence text for Hour
hoursLabel=Horas
#XFLD: Plural Recurrence text for Day
daysLabel=Dias
#XFLD: Plural Recurrence text for Month
monthsLabel=Meses
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minutos
