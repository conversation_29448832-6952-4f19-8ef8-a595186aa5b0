
#XBCB: Breadcrumb
viewMonitorBreadcrumb={spaceId}

#XFLD: Label for persisted view column
SOURCE=Avots
#XFLD: Label for persisted view column
NAME=Nosaukums
#XFLD: Label for persisted view column
NAME_LABEL=Biznesa nosaukums
#XFLD: Label for persisted view column
NAME_LABELNew=Objekts (Biznesa nosaukums)
#XFLD: Label for persisted view column
TECHINCAL_NAME=Tehniskais nosaukums
#XFLD: Label for persisted view column
TECHINCAL_NAMENew=Objekts (Tehniskais nosaukums)
#XFLD: Label for persisted view column
DATA_PERSISTENCY=Datu piekļuve
#XFLD: Label for persisted view column
STATUS=Statuss
#XFLD: Label for persisted view column
LAST_UPDATED=Pēdējā atjaunināšana
#XFLD: Label for persisted view column
IN_MEMORY_SIZE=Krātuvei izmantotā atmiņa (MiB)
#XFLD: Label for persisted view column
DISK_SIZE=Krātuvei izmantotais disks (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_NEWW=Lielums atmiņā (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_HEADER=Lielums atmiņā
#XFLD: Label for persisted view column
DISK_SIZE_NEWW=Lielums uz diska (MiB)
#XFLD: Label for persisted view column
DISK_SIZE_HEADER=Lielums uz diska
#XFLD: Label for schedule owner column
txtScheduleOwner=Grafika īpašnieks
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Rāda, kurš izveidoja grafiku
#XFLD: Label for persisted view column
PERSISTED=Pastāvošs
#XFLD: Label for persisted view column
TYPE=Tips
#XFLD: Label for View Selection Dialog column
changedOn=Mainīšanas datums
#XFLD: Label for View Selection Dialog column
createdBy=Izveidoja
#XFLD: Label for log details column
txtViewPersistencyLogs=Skatīt žurnālus
#XFLD: Label for log details column
txtViewPersistencyLogsNew=Detalizēta informācija
#XFLD: text for values shown for Ascending sort order
SortInAsc=Kārtot augošā secībā
#XFLD: text for values shown for Descending sort order
SortInDesc=Kārtot dilstošā secībā
#XFLD: Label for persisted view monitor heading
ViewMonitor_Heading=Skatu pārraugs
#XFLD: Label for persisted view monitor sub-heading
ViewMonitor_SubHeading=Pārraudzīt un uzturēt skatu datu pastāvīgumu


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ DynamicPageHeader ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: text for values shown in column Persistence Status
txtLoading=Ielāde
#XFLD: text for values shown in column Persistence Status
txtRunning=Tiek izpildīts
#XFLD: text for values shown in column Persistence Status
txtAvailable=Pieejams
#XFLD: text for values shown in column Persistence Status
txtError=Kļūda
#XMSG: Error message for unknown Persistence type that gets logged in Kibana
unknownReplicationTypeError=Replicēšanas tips ''{0}'' netiek atbalstīts.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Content ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Pēdējai datu pastāvīguma izpildei izmantotie iestatījumi:
#XMSG: Message for input parameter name
inputParameterLabel=Ievades parametrs
#XMSG: Message for input parameter value
inputParameterValueLabel=Vērtība
#XMSG: Message for persisted data
inputParameterPersistedLabel=Padarīts pastāvīgs
#XTIT: Count for View in ViewMonitor Toolbar
txtViewCount=Skati ({0})
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Skatīt pastāvīgumu
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Datu ilgtspēja
#XBUT: Button to clear the selected view persistance
ClearViewPersistance=Notīrīt
#XBUT: Button to stop the selected view persistance
stopPersistance=Apturēt pastāvīgumu
#XFLD: Placeholder for Search field
txtSearch=Meklēt
#XBUT: Tooltip for refresh button
txtRefresh=Atsvaidzināt
#XBUT: Tooltip for add view button
txtDeleteView=Dzēst pastāvīgumu
#XBUT: Tooltip for load new peristence
loadNewPersistence=Restartēt pastāvīgumu
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Ielādēt jaunu momentuzņēmumu
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Sākt datu ilgtspēju
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Noņemt esošos datus
#XMSG: success message for starting persistence
startPersistenceSuccess=Mēs padarām pastāvīgu skatu "{0}".
#XMSG: success message for stopping persistence
stopPersistenceSuccess=Mēs noņemam pastāvīgos datus skatam "{0}".
#XMSG: success message for stopping persistence
deletePersistedViewSuccess=Mēs noņemam skatu "{0}" no pārraudzības saraksta.
#XMSG: error message during start persistence of a view (backend error)
ERROR_STARTING_PERSISTENCE=Sākot skata "{0}" datu pastāvīgumu, radās kļūda.
#XMSG: error message during start persistence of a view (backend error)
PARAMERROR_STARTING_PERSISTENCE=Skats "{0}" nevar būt pastāvīgs, jo tas satur ievades parametrus.
#XMSG: error message during start persistence of a view (backend error)
NUMBER_OF_PARAMETER_STARTING_PERSISTENCE=Skats "{0}" nevar būt pastāvīgs, jo tam ir vairāki ievades parametri.
#XMSG: error message during start persistence of a view (backend error)
PARAMETER_DEFAULT_VALUE_STARTING_PERSISTENCE=Skats "{0}" nevar būt pastāvīgs, jo ievades parametram nav noklusējuma vērtības.
#XMSG: error message during start persistence of a view (backend error)
REDEPLOYMENT_DAC_REQUIRED=Ir atkārtoti jāizvieto datu piekļuves vadība (DAC) "{0}", lai atbalstītu datu pastāvīgumu.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_STARTING_PERSISTENCE=Skats "{0}" nevar būt pastāvīgs, jo tas izmanto skatu "{1}", kas satur datu piekļuves vadīklu (DAC).
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_EXTERNAL_STARTING_PERSISTENCE=Skats "{0}"nevar būt pastāvīgs, jo tas izmanto skatu ar datu piekļuves vadīklu (DAC), kas pieder citai vietai.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_STRUCTURE_STARTING_PERSISTENCE=Skats ''{0}'' nevar būt pastāvīgs, jo vienas vai vairāku tās datu piekļuves vadīklu (DAC) struktūra neatbalsta datu pastāvīgumu.
#XMSG: error message during stop persistence of a view (backend error)
STOP_PERSISTENCE_ERROR=Apturot skata "{0}" pastāvīgumu, radās kļūda.
#XMSG: error message during stop persistence of a view (backend error)
DELETE_PERSISTED_VIEW_ERROR=Dzēšot pastāvīgo skatu "{0}", radās kļūda.
#XMSG: Warning message for removing the persisted data
RemovePersistency_Confirm_Msg=Vai vēlaties dzēst pastāvīgos datus un pārslēgties uz virtuālo piekļuvi skatam "{0}"?
#XMSG: Warning message for delete the view persistence
DeletePersistency_Confirm_Msg=Vai vēlaties izņemt šo skatu no pārraudzības saraksta un dzēst skata "{0}" pastāvīgos datus?
#XMSG: error message for reading data from backend
txtReadBackendError=Šķiet, ka, nolasot no aizmugursistēmas, radās kļūda.
#XFLD: Label for No Data Error
NoDataError=Kļūda
#XMSG: message for conflicting task
Task_Already_Running=Konfliktējošais uzdevums jau tiek izpildīts skatam "{0}".

#XFLD: Error while accessing pertitioning for a view
missingPrivilegeInViewPersistency=Nepietiekama atļauja skata "{0}" nodalīšanas izpildei

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Show All Views~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Text for show all views button
allViewsText=Visi skati ({0})
#XBUT: Text for show scheduled views button
scheduledText=Ieplānots ({0})
#XBUT: Text for show persisted views button
persistedText=Pastāvošs ({0})
#XBUT: Text for start analyzer button
startAnalyzer=Startēt skata analizatoru
#XFLD: Message if repository is unavailable
repositoryErrorMsg=Repozitorijs nav pieejams, un noteikti līdzekļi ir atspējoti.

#XFLD: Data Access - Virtual
Virtual=Virtuāls
#XFLD: Data Access - Persisted
Persisted=Pastāvošs

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Non-Persisted View Selection Dialog~~~~~~~~~~~~~~~~~~~~~~~

#XTIT: Title for Non-Persisted View selection dialog
select_source=Atlasīt sarakstu, ko padarīt par pastāvīgu

#XTIT: Placeholder for Non-Persisted View selection dialog search
searchView=Meklēt skatos
#XTIT: No data in the list of non-persisted view
No_Data=Nav datu
#XBUT: Button to select non-persisted view
ok=Labi
#XBUT: Button to close the non-persisted views selection dialog
cancel=Atcelt

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~View persistency log message key~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message for view persistency logs
START_STOP_PERSISTENCY=Tiek sākta skata pastāvīguma uzdevuma izpilde šim: "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY=Notiek datu padarīšana par pastāvīgiem skatam “{1}”.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY)
START_VIEW_PERSISTENCY_PROCESS=Tiek sākts process, lai padarītu datus par pastāvīgiem, skatam "{0}".
#XFLD: Message for view persistency manual partition load
START_VIEW_PERSISTENCY_PROCESS_MANUAL_PARTITIONS=Tiek sākts process, lai padarītu datus par pastāvīgiem, skatam "{0}" ar atlasītiem nodalījuma ID: "{1}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY=Pastāvīgo datu noņemšana skatam "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY)
REMOVE_VIEW_PERSISTENCY=Tiek sākts process, lai noņemtu pastāvīgotos datus skatam "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_SUCCESS=Dati ir padarīti par pastāvīgiem skatam "{1}".
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_SUCCESS)
START_VIEW_PERSISTENCY_PROCESS_SUCCESS=Dati ir padarīti par pastāvīgiem skatam "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY_SUCCESS=Pastāvīgie dati ir noņemti un virtuālā datu piekļuve ir atjaunota skatam "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_SUCCESS)
REMOVE_VIEW_PERSISTENCY_SUCCESS=Ir pabeigts process, lai noņemtu pastāvīgotos datus skatam "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_FAILED=Nevar datus padarīt par pastāvīgiem skatam "{1}".
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_FAILED)
START_VIEW_PERSISTENCY_PROCESS_FAILED=Nevar datus padarīt par pastāvīgiem skatam "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.05)
STOP_VIEW_PERSISTENCY_FAILED=Nevar noņemt pastāvīgos datus skatam "{1}".
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_FAILED)
REMOVE_VIEW_PERSISTENCY_FAILED=Nevar noņemt pastāvīgos datus skatam "{0}".
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_SUCCESS="{3}" ieraksti ir pastāvīgi skatam "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace START_PERSISTENCY_RECORD_COUNT_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS={0} ieraksti ir ievietoti datu pastāvīguma tabulā skatam "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS_V2={0} ieraksti ir ievietoti datu pastāvīguma tabulā skatam "{1}". Izmantotā atmiņa: {2} GiB.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_SUCCESS="{3}" pastāvīgie ieraksti noņemti skatā "{1}".
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace STOP_PERSISTENCY_RECORD_COUNT_SUCCESS)
REMOVE_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=Pastāvīgie dati ir noņemti, "{0}" pastāvīgie ieraksti ir izdzēsti.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_FAILED=Ierakstu skaita iegūšana skatam "{1}" neizdevās.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_FAILED=Ierakstu skaita iegūšana skatam "{1}" neizdevās.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS="{1}" grafiks ir izdzēsts.
#XFLD: Message for view persistency logs (new message to replace STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS)
REMOVE_VIEW_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Grafiks ir izdzēsts skatam "{0}".
#XFLD: Message for view persistency logs
STOP_PERSISTENCY_DELETE_SCHEDULE_FAILED="{1}" grafika dzēšana neizdevās.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_REDEPLOY=Mēs nevaram padarīt par pastāvīgu skatu "{0}", jo pēc tam, kad sākāt to padarīt par pastāvīgu, tas tika mainīts un izvietots. Mēģiniet vēlreiz padarīt šo skatu par pastāvīgu vai uzgaidiet līdz nākamajai ieplānotajai izpildei.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_DELETE=Mēs nevaram padarīt par pastāvīgu skatu "{0}", jo pēc tam, kad sākāt to padarīt par pastāvīgu, tas tika izdzēsts.
#XFLD: Message for successful data persistency of a single partition (for messages created before version 2022.03)
START_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} ieraksti ir padarīti par pastāvīgiem nodalījumā vērtībām "{1}" <= {2} < "{3}".
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (new message to replace START_PERSISTENCY_SINGLE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS={0} ieraksti ir ievietoti nodalījumā vērtībām "{1}" <= "{2}" < "{3}".
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS_V2={0} ieraksti ir ievietoti nodalījumā vērtībām "{1}" <= "{2}" < "{3}". Izmantotā atmiņa: {4} GiB.
#XFLD: Message for successful replication of a "others" partition (for messages created before version 2022.03)
START_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} ieraksti ir padarīti par pastāvīgiem nodalījumā "citi".
#XFLD: Message used by SEAL repository for successful replication of a "others" partition (new message to replace START_PERSISTENCY_OTHERS_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_OTHERS_PARTITION_SUCCESS={0} ieraksti ir ievietoti nodalījumā "citi".
#XFLD: Message for view persistency record count of partitions (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={3} ieraksti ir padarīti par pastāvīgiem skatā "{1}" {4} nodalījumos.
#XFLD: Message used by SEAL repository for view persistency record count of partitions (new message to replace START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={0} ieraksti ir ievietoti datu pastāvīguma tabulā skatam "{1}" {2}nodalījumos.
#XFLD: Message used by SEAL repository for view persistency for partition wise refresh
START_VIEW_PERSISTENCY_RECORD_COUNT_REFRESHED_PARTITIONS_SUCCESS={0} ieraksti ir ievietoti datu pastāvīguma tabulā skatam "{1}". Atjauninātie nodalījumi: {2}; Slēgtie nodalījumi: {3}; Nodalījumi kopā: {4}
#XFLD: Message used by SEAL repository for view persistency for manual partition load
START_VIEW_PERSISTENCY_RECORD_COUNT_MANUAL_PARTITIONS_SUCCESS={0} ieraksti ir ievietoti datu pastāvības tabulā skatam "{1}" {2} atlasītajos nodalījumos.
#XFLD: Message used by SEAL repository for view persistency for locked partition data load
START_VIEW_PERSISTENCY_RECORD_COUNT_LOCKED_PARTITIONS_SUCCESS={0} ieraksti ir ievietoti datu pastāvīguma tabulā skatam "{1}". Atjauninātie nodalījumi: {2}; Slēgtie, nemainītie nodalījumi: {3}; Nodalījumi kopā: {4}
#XMSG: Message for unexpected error (for messages created before version 2022.03)
START_PERSISTENCY_UNEXPECTED_ERROR=Datus padarot par pastāvīgiem skatam "{0}", radās neparedzēta kļūda.
#XMSG: Message for unexpected error (new message to replace START_PERSISTENCY_UNEXPECTED_ERROR)
START_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Datus padarot par pastāvīgiem skatam "{0}", radās neparedzēta kļūda.
#XMSG: Message for Locked space error
START_VIEW_PERSISTENCY_LOCKED_SPACE_ERROR=Skatu "{0}" nevar padarīt par pastāvīgu, jo vieta "{1}" ir bloķēta.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
STOP_PERSISTENCY_UNEXPECTED_ERROR=Noņemot pastāvīgos datus skatam "{0}", radās neparedzēta kļūda.
#XMSG: Message for unexpected error (new message to replace STOP_PERSISTENCY_UNEXPECTED_ERROR)
REMOVE_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Noņemot pastāvīgumu skatam "{0}", radās neparedzēta kļūda.
#XMSG: Message for ivalidated view
VIEW_PERSISTENCY_ABORTED_DEFINITION_INVALID=Skata "{0}" definīcija ir kļuvusi nederīga visdrīzāk tāpēc, ka šīs skats tieši vai netieši patērēja objekta izmaiņas. Lai atrisinātu šo problēmu vai noteiktu saknes cēloni, mēģiniet skatu izvietot atkārtoti.
#XMSG: Message for persisted data removal (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT=Pastāvīgie dati tika noņemti, kamēr tika izvietots skats "{0}".
#XMSG: Message for persisted data removal of a consuming view
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW=Pastāvīgie dati tika noņemti, kamēr tika izvietots patērētais skats "{0}".
#XMSG: Message for persisted data removal of a consuming view due to a dac change
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW_DAC_CHANGED=Pastāvīgie dati tika noņemti, kamēr tika izvietots patērētais skats ''{0}'', jo tā piekļuve datiem tika mainīta.
#XMSG: Message for persisted data removal (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DEPLOYMENT=Pastāvīgie dati tika noņemti, kamēr tika izvietots skats "{0}".
#XMSG: Message for deleted view (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION=Pastāvīgums tika noņemts līdz ar skata "{0}" dzēšanu.
#XMSG: Message for deleted view (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DELETION=Pastāvīgums tika noņemts līdz ar skata "{0}" dzēšanu.
#XMSG Message for removal of persisted data, because prerequisites not fulfilled
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_PREREQUISITES_NOT_FULFILLED=Pastāvīgie dati ir noņemti, jo datu pastāvīguma priekšnosacījumi vairs netiek izpildīti.
#XMSG: Message for inconsistent persistency
VIEW_PERSISTENCY_INCONSISTENT_PERSISTENCY=Skata "{0}" pastāvīgums ir kļuvis pretrunīgs. Noņemiet pastāvīgos datus, lai novērstu šo problēmu.
#XMSG: Message for prerequisites
START_PERSISTENCY_CHECK_PREREQUISITES=Notiek priekšnoteikumu pārbaude skata "{0}" pastāvīgumam.
#XMSG: Message for DAC deployed in deprecated way using table function and join (for messages created before 2025.05)
START_PERSISTENCY_DEPRECIATED_DAC=Skats "{0}" ir izvietots, izmantojot datu piekļuves vadību (DAC), kura tiek atmesta. Lūdzu, izvietojiet skatu vēlreiz, lai uzlabotu veiktspēju.
#XMSG: Message for DAC deployed in deprecated way using table function and join
START_PERSISTENCY_DEPRECATED_DAC=Skats "{0}" ir izvietots, izmantojot datu piekļuves vadību (DAC), kura tiek atmesta. Lūdzu, izvietojiet skatu vēlreiz, lai uzlabotu veiktspēju.
#XMSG: Message for roll back (for messages created before version 2022.03)
ROLLBACK_VIEW_PERSISTENCY=Radās kļūda. Skatam "{0}" atjaunots pastāvīguma iepriekšējais stāvoklis.
#XMSG: Message used by SEAL repository for rollback of running procedure to persist view (new message to replace ROLLBACK_VIEW_PERSISTENCY)
VIEW_PERSISTENCY_ROLLBACK=Radās kļūda. Ir apturēts skata “{0}” ilgtspējas process, un izmaiņām tika veikta atrite.
#XMSG: Message used by SEAL repository for rollback of running procedure to remove persisted data of view
REMOVE_VIEW_PERSISTENCY_ROLLBACK=Radās kļūda. Process skata "{0}" pastāvīgoto datu noņemšanai ir apturēts, un izmaiņas ir atceltas.
#XMSG: Message used by SEAL repository for persistency preparation
START_PERSISTENCY_PREPARATION=Notiek datu padarīšana par pastāvīgiem.
#XMSG: Message used by SEAL repository for data insertion
START_INSERTING_PERSISTENCY_DATA=Notiek datu ievietošana pastāvīguma tabulā.
#XMSG: Message for null values - partitioning (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS={0} Null vērtības ieraksti ir ievietoti nodalījumā "citi".
#XMSG: Message used by SEAL repository for others partition values - partitioning (new message to replace START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS={0} ieraksti ir ievietoti nodalījumā "citi" vērtībām "{2}" < "{1}" OR "{2}" >= "{3}".
#XMSG: Message used by SEAL repository for others partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS_V2={0} ieraksti ir ievietoti nodalījumā "citi" vērtībām "{2}" < "{1}" OR "{2}" >= "{3}". Izmantotā atmiņa: {4} GiB.
#XMSG: Message used by SEAL repository for null partition values - partitioning
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS={0} ieraksti ir ievietoti nodalījumā "citi" vērtībām "{1}" IS NULL.
#XMSG: Message used by SEAL repository for null partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS_V2={0} ieraksti ir ievietoti nodalījumā "citi" vērtībām "{1}" IS NULL. Izmantotā atmiņa: {2} GiB.
##XFLD:Message used by SEAL repository for remote statistics
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS=Iesaistīto datu ielāde: {0} attālie priekšraksti. Ienesto ierakstu kopskaits: {1}. Kopējais ilgums: {2} sekundes.
##XFLD:Message used by SEAL repository for remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS_WITH_PARTITIONS=Iesaistīto datu ielāde, izmantojot {0} nodalījumus ar {1} attāliem priekšrakstiem. Ienesto ierakstu kopskaits: {2}. Kopējais ilgums: {3} sekundes.
##XFLD:Message used by SEAL repository with UI ID for instructions of remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_INSTRUCTION=Izpildes laikā apstrādātos attālos priekšrakstus var attēlot, atverot attālo vaicājumu pārraudzību nodalījumiem paredzēto ziņojumu detalizētajā informācijā.
##XFLD:Message for start reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS=Notiek skata {0} esošo pastāvīgo datu atkārtotas izmantošanas apstrāde pēc izvietošanas.
##XFLD:Message used by SEAL repository for reattach view persitency
START_VIEW_PERSISTENCY_REATTACHMENT=Sākt esošo pastāvīgo datu atkārtotu izmantošanu.
##XFLD:Message used by SEAL repository for reattach view persitency
START_FINILIZING_VIEW_PERSISTENCY_REATTACHMENT=Notiek esošo pastāvīgo datu atkārtota izmantošana.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_COMPLETE=Esošo pastāvīgo datu atkārtotas izmantošanas process skatam {0} ir pabeigts.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_FAILED=Neizdevās atkārtoti izmantot esošos pastāvīgos datus skatam {0} pēc izvietošanas.
#XMSG: Message used by SEAL repository for finalizing persistency
START_FINALIZING_PERSISTENCY=Notiek pastāvīguma pabeigšana.
#XMSG: Message used by SEAL repository for restoring virtual access for view
REMOVE_VIEW_PERSISTENCY_VIRTUAL_ACCESS_RESTORED=Skatam "{0}" atjaunota virtuālā datu piekļuve.
#XMSG: Message used by SEAL repository for view that already has virtual access
REMOVE_VIEW_PERSISTENCY_ALREADY_VIRTUAL_ACCESS=Skatam "{0}" jau ir virtuāla datu piekļuve. Nekādi pastāvīgotie dati nav noņemti.
#XMSG: Message for removing view persistency
VIEW_PERSISTENCY_REMOVED_FROM_MONITOR=Skats "{0}" ir noņemts skatu pārraugā.
#XMSG: Message for view doesnt exist.
VIEW_PERSISTENCY_VIEW_NOT_EXISTING=Skats "{0}" vai nu nepastāv datu bāzē, vai nav pareizi izvietots, tādēļ to nevar paturēt. Mēģiniet skatu izvietot vēlreiz, lai šo problēmu novērstu, vai identificēt saknes cēloni.
#XMSG: Message for persistency not enabled.
VIEW_PERSISTENCY_NOT_ENABLED=Skata pastāvīgums nav iespējots. Lai iespējotu šo funkcionalitāti, izvietojiet atkārtoti tabulu/skatu vietā "{0}".
#XMSG: Message for unexpected error due to node crash
VIEW_PERSISTENCY_UNEXPECTED_ERROR_GENERAL=Pēdējā skata pastāvīguma izpilde tika pārtraukta tehnisku kļūdu dēļ.
#XFLD: Message for view persistency memory consumption
VIEW_PERSISTENCY_MEMORY_CONSUMPTION=Skata pastāvīguma izpildlaikā izmantoti {0} GiB maksimālās atmiņas.
#XFLD: Message for view persistency timeout
VIEW_PERSISTENCY_TIMEOUT=Skata {0} pastāvīgums sasniedza {1} stundu taimautu.
#XFLD: Message for not started asynchronous view persistency
VIEW_PERSISTENCY_ASYNC_NOT_STARTED=Liela sistēmas slodze novērsa skata pastāvīguma asinhronās izpildes sākšanu. Pārbaudiet, vai paralēli nedarbojas pārāk daudz uzdevumu.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency (for messages created before version 2022.05)
START_VIEW_PERSISTENCY_EXECUTION_METHOD_DROP_AND_RENAME=Esošā pastāvīgā tabula tika izdzēsta un aizstāta ar jaunu pastāvīgo tabulu.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_RECREATE_AND_DROP=Esošā pastāvīgā tabula tika izdzēsta un aizstāta ar jaunu pastāvīgo tabulu.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_TRUNCATE_AND_COPY=Esošā pastāvīgā tabula tika atjaunināta ar jauniem datiem.
##XFLD: Message for missing authorizations for view persistency
VIEW_PERSISTENCY_MISSING_AUTH=Trūkst autorizācijas datu pastāvībai.
##XFLD: Message start of persistency cancellation
VIEW_PERSISTENCY_TRIGGER_CANCEL=Sāk atcelt procesu, lai pastāvētu skats {0}.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_UNAVAILABLE_TASK_RUN=Neizdevās atcelt procesu, lai pastāvētu skats, jo skatam {0} nav neviena datu pastāvīguma uzdevuma, kas tiktu izpildīts.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_NONDETERMINISTIC_TASK_RUN=Neizdevās atcelt procesu, lai pastāvētu skats, jo skatam {0} netiek izpildīts neviens datu pastāvīguma uzdevums.
##XFLD: Message for canceling a non running persitency task
VIEW_PERSISTENCY_CANCEL_NON_RUNNING_SELECTED_TASK=Neizdevās atcelt skata {0} pastāvības noteikšanas procesu, jo atlasītais datu pastāvības uzdevums {1} nedarbojas.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_UNREACHABLE_PERSISTENCY_RUN=Neizdevās atcelt procesu, lai pastāvētu skats, jo datu pastāvīgums skatam {0} vēl nav sākts.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_COMPLETED_PERSISTENCY_RUN=Neizdevās atcelt procesu, lai pastāvētu skats {0}, jo tas jau ir pabeigts.
##XFLD: Message for failed cancellation
VIEW_PERSISTENCY_ABORTED_CANCEL_EXECUTION=Neizdevās atcelt procesu, lai pastāvētu skats {0}.
##XFLD: Message for finalizing persistency cancellation
VIEW_PERSISTENCY_FINALIZE_CANCEL=Tika iesniegts process, lai apturētu skata {0} datu pastāvīgumu.
##XFLD: Message used in persist task when cancellation is caught
VIEW_PERSISTENCY_CANCELED=Process, lai pastāvētu skats {0}, tika pārtraukts.
##XFLD: Message used in persist task when cancelation is caught and performed through a cancelation task
VIEW_PERSISTENCY_CANCELED_VIA_CANCEL_TASK=Process skata {0} pastāvīgumam tika apturēts, izmantojot atcelšanas uzdevumu {1}.
##XFLD: Message used in cancel task when the task is triggered by deployment
VIEW_PERSISTENCY_CANCEL_TRIGGERED_BY_DEPLOYMENT=Notiek procesa atcelšana, lai pastāvētu dati, kamēr notiek skata {0} izvietošana.
##XFLD: Message used in case the cancelation is retriggered for a running task
VIEW_PERSISTENCY_CANCEL_ATTEMPT_RETRIGGER_CANCEL=Iepriekšējais atcelšanas uzdevums skata {0} pastāvīgumam jau ir iesniegts.
##XFLD: Message used to express the delay that may occur before the persistency task is stopped
VIEW_PERSISTENCY_CANCEL_DELAY_STOPPING_PERSISTENCY=Skata {0} pastāvīguma uzdevums var tikt apturēts ar aizkavi.
##XFLD: Message used to reflect the task log identifier of the current running persistency task
VIEW_PERSISTENCY_CANCEL_CURRENT_RUNNING_TASK=Dati skatam {0} pastāv ar uzdevumu {1}.
##XFLD:Message used by SEAL repository for locked partitions and DACs
VIEW_PERSISTENCY_LOCKED_PARTITIONS_DACS=DAC nodrošinātās autorizācijas, iespējams, ir mainītas un bloķētajos nodalījumos netiek apsvērtas. Lai lietotu šīs izmaiņas, atbloķējiet nodalījumus un ielādējiet jaunu momentuzņēmumu.
##XFLD:Message used by SEAL repository for column structure change
VIEW_PERSISTENCY_COLUMN_STRUCTURE_CHANGE=Kolonnas struktūra ir mainīta un vairs neatbilst esošajai pastāvības tabulai. Noņemiet pastāvošos datus un sāciet jaunu datu pastāvību, lai jūsu pastāvības tabula tiktu atjaunināta.
##XFLD: Message for out of memory
VIEW_PERSISTENCY_HANA_OUT_OF_MEMORY=Uzdevums neizdevās, jo SAP HANA datu bāzē radās vietas atmiņā trūkuma kļūda.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_EXCEPTION_TRANSACTION_ROLLBACK=Uzdevums neizdevās, jo SAP HANA datu bāzē radās iekšējs izņēmums.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_SQL_EXECUTION_ISSUE=Uzdevums neizdevās, jo SAP HANA datu bāzē radās iekšēja SQL izpildes problēma.
##XFLD: Message for HANA out of memory result
VIEW_PERSISTENCY_OUT_OF_MEMORY_RESULT_MESSAGE=HANA nepietiekamas atmiņas notikuma iemesls: {0}
##XFLD: Message for admission control rejection
VIEW_PERSISTENCY_HANA_ADMISSION_CTRL_REJECTION=Uzdevums neizdevās SAP HANA piekļuves vadības noraidījuma dēļ.
##XFLD: Message for HANA back pressure / rate limit of HANA client
VIEW_PERSISTENCY_HANA_CLIENT_RATE_LIMIT_REJECTION=Uzdevums neizdevās, jo bija pārāk daudz aktīvu SAP HANA savienojumu.
#XMSG: Message for invalid persisted table name
VIEW_PERSISTENCY_PERSISTED_TABLE_INVALID=Radās kļūda, un pastāvīgā tabula ir kļuvusi nederīga. Lai šo problēmu novērstu, lūdzu, noņemiet pastāvīgos datus un padariet skatu par pastāvīgu vēlreiz.
#XMSG: Message for usahe of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION=Skatu nevar pastāvīgot. Tas izmanto attālu tabulu, balstoties uz attālu avotu ar iespējotu lietotāju izplatīšanu. Pārbaudiet skata pārmantojamību.
#XMSG: Message for dynamic usage of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION_DYNAMIC=Skatu nevar pastāvīgot. Tas izmanto attālu tabulu, balstoties uz attālu avotu ar iespējotu lietotāju izplatīšanu. Attālo tabulu var patērēt dinamiski, izmantojot SQL skriptu skatu. Skata pārmantojamība var nerādīt attālo tabulu.
#XMSG: Message for insufficient privileges or consumption of view with DAC via dynamic SQL
VIEW_PERSISTENCY_INSUF_PRIV_OR_DAC_DYN_SQL=Jūsu privilēģijas var būt nepietiekamas. Atveriet sadaļu “Datu priekšskatījums”, lai apskatītu, vai jums ir nepieciešamās privilēģijas. Ja ir, tad otram ar dinamisko SQL skriptu izmantotajam skatam var tikt lietota datu piekļuves vadība (DAC).
#XMSG: deployment of DAC is deprecated and view need to be redeployed to be able to persist data
VIEW_PERSISTENCY_DEPRECATED_DAC=Skats "{0}" ir izvietots, izmantojot datu piekļuves vadību (DAC), kura tiek atmesta. Lūdzu, izvietojiet skatu vēlreiz, lai varētu pastāvīgot datus šim skatam.
##XFLD:Message used by SEAL repository for input parameter
START_VIEW_PERSISTENCY_INPUT_PARAMETER=Tiek izmantota noklusējuma vērtība "{0}" ievades parametram "{1}".
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Elastic compute node keys~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
##XFLD:Message used to reflect disablement of ecn replica
ECN_DISABLE_EXISTING_REPLICA=Elastīgais aprēķināšanas mezgls ir atspējots.
##XFLD:Message used to reflect recreation of ecn replica
ECN_RECREATE_EXISTING_REPLICA=Elastīgā aprēķināšanas mezgla dublikāts ir izveidots no jauna.
##XFLD:Message used to reflect re-enablement of ecn replica
ECN_ENABLE_EXISTING_REPLICA=Elastīgā aprēķināšanas mezgla dublikāts ir iespējots no jauna.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Task Schedule Menu key~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message to schedule task
scheduleText=Grafiks
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Izveidot grafiku
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Rediģēt grafiku
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Dzēst grafiku
#XFLD: Refresh frequency field
refreshFrequency=Atsvaidzināšanas biežums
#XFLD: Refresh frequency field
refreshFrequencyNew=Biežums
#XFLD: Refresh frequency field
refreshFrequencyNewNew=Ieplānotais biežums
#XBUT: label for None
none=Nav
#XBUT: label for Real-Time replication state
realtime=Reāllaika
#XFLD: Label for table column
txtNextSchedule=Nākamā izpilde
#XFLD: Label for table column
txtNextScheduleNew=Ieplānotā nākamā izpilde
#XFLD: Label for table column
txtNumOfRecords=Ierakstu skaits
#XFLD: Label for scheduled link
scheduledTxt=Ieplānots
#XFLD: LABEL for partially persisted link
partiallyPersisted=Daļēji pastāvīgs
#XFLD: Text for paused text
paused=Apturēts

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ View Monitor Tasklog Details Action keys~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message for release lock actionn
releaseLockMsg=Ja izpilde aizņem vairāk laika, nekā parasti, tas var norādīt, ka darbība nav izdevusies un statuss nav attiecīgi atjaunināts. \r\n Lai atrisinātu šo problēmu, varat atbrīvot bloķēšanu un iestatīt tai statusu Neizdevās.
#XFLD: Label for release lock dialog
releaseLockText=Atbrīvot bloķēšanu

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Column header tooltip ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: tooltip for table column
txtViewTooltip=Pastāvīgā skata nosaukums
#XFLD: tooltip for table column
txtViewDataAccessTooltip=Tas norāda skata pieejamību
#XFLD: tooltip for table column
txtViewRefreshFreqTooltip=Tas norāda, vai skatam ir definēts grafiks
#XFLD: tooltip for table column
txtViewStatusTooltip=Iegūt pastāvīgā skata statusu
#XFLD: tooltip for table column
txtViewLatestUpdateTooltip=Nodrošina informāciju par to, kad pastāvīgais skats tika pēdējoreiz atjaunināts
#XFLD: tooltip for table column
txtViewNextRunTooltip=Ja skatam ir iestatīts grafiks, skatiet, kad ir ieplānota nākamā izpilde.
#XFLD: tooltip for table column
txtViewNumOfRecordsTooltip=Izsekojiet ierakstu skaitu.
#XFLD: tooltip for table column
txtViewInMemorySizeTooltip=Izsekojiet, cik daudz vietas šis skats izmanto jūsu atmiņā
#XFLD: tooltip for table column
txtViewDiskSizeTooltip=Izsekojiet, cik daudz vietas šis skats aizņem jūsu diskā
#XMSG: Expired text
txtExpired=Beidzies derīgums

#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Objektu "{0}" nevar pievienot uzdevumu ķēdei.

#~~~~~~~~~~~View Analyzer task log messages~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message indicating number of records for a view, and memory consumption in Mebibyte during simulation of view persistency
RECORDS_PEAK_MEMORY=Skatam "{0}" ir {1} ieraksti. Skata pastāvīguma simulācija šim skatam izmantoja {2} MiB atmiņas.
#XMSG: View Analyzer execution failure
VIEW_ANALYZER_EXECUTION_FAILED=Skata analizatora izpilde neizdevās.
#XMSG: Missing authorizations for View Analyzer
VIEW_ANALYZER_MISSING_AUTH=Skata analizatoram trūkst autorizāciju.
#XMSG: Maximum memory in Gibibyte is reached, therefore persistency simulation is stopped
MAXIMUM_MEMORY_REACHED=Simulējot datu pastāvīgumu skatam "{1}", tika sasniegta maksimālā atmiņa {0} GiB. Tāpēc tālākas skata datu pastāvīguma simulācijas netiks izpildītas.
#XMSG: Error during persistency simulation occurred
PERSISTENCY_SIMULATION_ERROR=Skata "{0}" datu pastāvīguma simulācijas laikā radās kļūda.
#XMSG: Persistency simulation is not executed, because prerequisites are not fulfilled and the view cannot be persisted
PREREQUISITES_NOT_FULFILLED=Skatam "{0}" nav izpildīta datu pastāvīguma simulācija, jo nav izpildīti priekšnosacījumi un skatam nevar izveidot pastāvību.
#XMSG: Persistency simulation is not executed, because the view does not exist in the database
VIEW_DOES_NOT_EXIST=Lai iespējotu datu pastāvīguma simulāciju, jums ir jāizvieto skats "{0}".
#XMSG: Number of records cannot be determined, because the local table does not exist in the database
TABLE_DOES_NOT_EXIST=Lokālā tabula "{0}" nepastāv datu bāzē, tāpēc šai tabulai ierakstu skaitu noteikt nevar.
##XMSG: Message start of cancellation for View Analyzer task
CANCEL_VIEW_ANALYZER_TRIGGERED=Ir iesniegts process apturēt skatu analizētāja uzdevumu {0} skatam "{1}". Līdz uzdevuma apturēšanai var būt vērojama aizkave.
##XMSG: Message start to indicate that task is not active
CANCEL_VIEW_ANALYZER_NO_ACTIVE_TASK=Skatu analizētāja uzdevums {0} skatam "{1}" nav aktīvs.
#XMSG: Cancel View Analyzer task failure
CANCEL_VIEW_ANALYZER_FAILED=Neizdevās atcelt skatu analizētāja uzdevumu.
##XMSG: Message used in View Analyzer execution task when task is canceled
VIEW_ANALYZER_CANCELED=Skatu analizētāja izpilde skatam "{0}" tika apturēta, izmantojot atcelšanas uzdevumu.
##XMSG: Message start of cancelation for Model Validation task
CANCEL_MODEL_VALIDATION_TRIGGERED=Process, lai apturētu modeļa validēšanas uzdevumu {0} skatam "{1}", ir iesniegts. Līdz uzdevuma apturēšanai var būt aizkave.
##XMSG: Message to indicate that task is not active
CANCEL_MODEL_VALIDATION_NO_ACTIVE_TASK=Modeļa derīguma pārbaudes uzdevums {0} skatam "{1}" nav aktīvs.
#XMSG: Cancel Model Validation task failure
CANCEL_MODEL_VALIDATION_FAILED=Neizdevās atcelt modeļa derīguma pārbaudes uzdevumu.
##XMSG: Message used in Model Validation task when task is canceled
MODEL_VALIDATION_CANCELED=Modeļa derīguma pābaudes izpilde skatam "{0}" tika apturēta, izmantojot atcelšanas uzdevumu.
#XMSG: Message for Locked space error, before starting Model Validation task
MODEL_VALIDATION_LOCKED_SPACE_ERROR=Nevar izpildīt modeļa derīguma pārbaudi skatam "{0}", jo vieta "{1}" ir bloķēta.
##XMSG: Message used in View Analyzer execution task when determination of number of rows fails
NUMBER_ROWS_DETERMINATION_FAILED=Kļūda, vietējai tabulai "{0}" nosakot rindu skaitu.
##XMSG: Message to indicate that SQL plan was created
EXECUTION_PLAN_CREATED=SQL analizētāja plāna fails skatam "{0}" ir izveidots, un to var lejupielādēt.
#XFLD: Message to indicate starting of process to generate SQL Analyzer plan file
START_PLANVIZ_EXECUTION=Procesa sākšana, lai skatam "{0}" ģenerētu SQL analizētāja plāna failu.
#XFLD: Message to indicate starting of View Analyzer process
VIEW_ANALYZER_EXECUTION_STARTED=Tiek startēta skatu analizētāja izpilde.
##XMSG: Message to indicate that SQL plan file cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_PLANVIZ_NOT_FULFILLED=SQL analizētāja plāna failu nevar ģenerēt skatam "{0}", jo nav izpildīti datu pastāvīguma priekšnosacījumi.
##XMSG: Error message during  generation of SQL Analyzer plan file
PLANVIZ_ERROR=Skatam "{0}" ģenerējot SQL analizētāja plāna failu, radās kļūda.
#XMSG: Message for Locked space error
VIEW_ANALYZER_LOCKED_SPACE_ERROR=Nevar izpildīt funkciju "Skatu analizētājs" skatam "{0}", jo vieta "{1}" ir bloķēta.
#XMSG: Message for persistency simulation without partitioning
PERSISTENCY_SIMULATION_WITHOUT_PARTITIONS=Datu pastāvīguma simulācijas laikā netiek apsvērti skata "{0}" nodalījumi.
#XMSG: Message for generation of SQL Analyzer plan file without considering partitioning
PLANVIZ_WITHOUT_PARTITIONS=SQL analizētāja ģenerēšanas laikā netiek apsvērti skata "{0}" nodalījumi.
#XMSG: Confirmation message for mass removal of persisted data
MassRemovePersistency_Confirm_Msg=Vai vēlaties noņemt pastāvīgos datus un pārslēgt datu piekļuvi atpakaļ uz virtuālo piekļuvi?
#XMSG: Confirmation message for partial mass removal of selected persisted data
PartialMassRemovePersistency_Confirm_Msg={0} no {1} atlasītajiem skatiem ir pastāvīgi dati. \n Vai vēlaties noņemt pastāvīgos datus un pārslēgt datu piekļuvi atpakaļ uz virtuālo piekļuvi?
#XMSG: success message for stopping mass persistence
stopMassPersistenceSuccess=Mēs noņemam pastāvīgos datus atlasītajos skatos.
#XMSG: error message during mass stop persistence of a view (backend error)
STOP_MASS_PERSISTENCE_ERROR=Apturot atlasīto skatu pastāvīgumu, radās kļūda.
#XMSG: memory analysis is only performed in space {0}
NO_MEMORY_ANALYSIS=Atmiņas analīze ir izpildīta entītijām tikai vietā "{0}": "{1}" "{2}" ir izlaistas.
##XMSG: Message to indicate that Explain Plan cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_EXPLAINPLAN_NOT_FULFILLED=Failu “Skaidrot plānu” nevar ģenerēt skatam "{0}", jo pastāvīguma priekšnosacījumi nav izpildīti.
#XMSG: Message for Explain Plan generation without partitioning
EXPLAINPLAN_WITHOUT_PARTITIONS=Skata "{0}" nodalījumi netiek ņemti vērā faila “Skaidrot plānu” ģenerēšanas laikā.
#XFLD: Message to indicate starting of process to generate Explain Plan file
START_EXPLAINPLAN_EXECUTION=Tiek sākts process faila “Skaidrot plānu” ģenerēšanai skatam "{0}".
##XMSG: Message to indicate that Explain Plan was created
EXPLAINPLAN_CREATED=Fails “Skaidrot plānu” skatam "{0}" ir ģenerēts. Varat to parādīt, noklikšķinot uz "Skatīt detalizēto informāciju", vai to lejupielādēt, ja jums ir attiecīgā atļauja.
##XMSG: Error message during  generation of Explain Plan file
EXPLAINPLAN_ERROR=Radās kļūda, ģenerējot failu “Skaidrot plānu” skatam "{0}".
##XMSG: Explain Plan file cannot be generated for the view {0}, because too many views are stacked on each other
EXPLAINPLAN_MODEL_TOO_COMPLEX=Plāna skaidrojuma failu nevar ģenerēt skatam "{0}". Pārāk daudzi skati ir sakrauti cits virs cita. Kompleksi modeļi var izraisīt atmiņas nepietiekamības kļūdas un lēnu veiktspēju. Ieteicams skatu padarīt par pastāvīgu.

##XMSG: Message to indicate that the space is locked
PERFORMANCE_ANALYSIS_LOCKED_SPACE_ERROR=Nevar izpildīt veiktspējas analīzi skatam "{0}", jo vieta "{1}" ir bloķēta.
##XMSG: The performance analysis task was cancelled
PERFORMANCE_ANALYSIS_CANCELED=Veiktspējas analīze skatam "{0}" tika atcelta.
##XMSG: The performance analysis failed
PERFORMANCE_ANALYSIS_FAILED=Veiktspējas analīze neizdevās.
##XMSG: The performance analysis is finished
PERFORMANCE_ANALYSIS_FINISHED=Veiktspējas analīze skatam "{0}" ir pabeigta. Attēlojiet rezultātu, noklikšķinot uz "Skatīt detalizēto informāciju".
##XMSG: The view has input parameter with no default value
INPUT_PARAMETER_WITHOUT_DEFAULT_VALUE=Šo skatu nevar analizēt, jo tam ir parametrs bez noklusējuma vērtības.
##XMSG: The view is not fully deployed
VIEW_NOT_FULLY_DEPLOYED=Šo skatu nevar analizēt, jo tas nav pilnīgi izvietots.
##XMSG: The view uses limited remote adapters
LIMITED_REMOTE_ADAPTER=Šis skats izmanto vismaz vienu attālo adapteri ar ierobežotām spējām, piemēram, trūkstošu filtra pushdown vai atbalstu darbam ar “Uzskaite”. Objektu pastāvīgošana vai replicēšana var uzlabot izpildlaika veiktspēju.
##XMSG: The view uses remote adapter that does not support 'Limit'
REMOTE_ADAPTER_NOT_SUPPORTING_LIMIT=Šis skats izmanto vismaz vienu attālo adapteri, kurš neatbalsta parametru “Limits”. Iespējams, bija atlasīts vairāk par 1000 ierakstiem.
##XMSG: Performance analysis is executed by using default parameters of view'
INPUT_PARAMETER_WITH_DEFAULT_VALUE=Veiktspējas analīze ir izpildīta, izmantojot skata parametru noklusējuma vērtības.
##XMSG: Message to indicate that an error occurred
PERFORMANCE_ANALYSIS_ERROR=Radās kļūda, izpildot veiktspējas analīzi skatam "{0}".
##XMSG: Message start of cancellation for Performance Analysis task
CANCEL_PERFORMANCE_ANALYSIS_TRIGGERED=Process apturēt veiktspējas analīzes uzdevumu {0} skatam "{1}" ir iesniegts. Līdz uzdevuma apturēšanai var būt aizkave.
##XMSG: Message start to indicate that task is not active
CANCEL_PERFORMANCE_ANALYSIS_NO_ACTIVE_TASK=Veiktspējas analīzes uzdevums {0} skatam "{1}" nav aktīvs.
#XMSG: Cancel Performance Analysis task failure
CANCEL_PERFORMANCE_ANALYSIS_FAILED=Neizdevās atcelt veiktspējas analīzes uzdevumu.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Piešķirt grafiku man
#XBUT: Pause schedule menu label
pauseScheduleLabel=Aizturēt grafiku
#XBUT: Resume schedule menu label
resumeScheduleLabel=Turpināt grafiku
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Noņemot grafikus, radās kļūda.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Piešķirot grafikus, radās kļūda.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Aizturot grafikus, radās kļūda.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Turpinot grafikus, radās kļūda.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted={0} grafiku dzēšana
#XMSG: Message for starting mass assign of schedules
massAssignStarted={0} grafiku īpašnieka maiņa
#XMSG: Message for starting mass pausing of schedules
massPauseStarted={0} grafiku aizturēšana
#XMSG: Message for starting mass resuming of schedules
massResumeStarted={0} grafiku turpināšana
#XBUT: Select Columns Button
selectColumnsBtn=Atlasīt kolonnas
#XFLD: Refresh tooltip
TEXT_REFRESH=Atsvaidzināt
#XFLD: Select Columns tooltip
text_selectColumns=Atlasīt kolonnas


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Runtime Metrics ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Label for Runtime Metrics
RuntimeMetricsTitle=Izpildlaika metrika, kas paredzēta
#XFLD : Label for Run Button
runButton=Izpildīt
#XFLD : Label for Cancel Button
cancelButton=Atcelt
#XFLD : Label for Close Button
closeButton=Aizvērt
#XFLD : Label for Open View Analyzer Button
openViewAnalyzerButton=Atvērt skatu analizētāju
#XFLD : Label for Generate Explain Plan Button
generateExplainPlanButton=Ģenerēt plāna skaidrojumu
#XFLD : Label for Previous Run Column
previousRun=Iepriekšējā izpilde
#XFLD : Label for Latest Run Column
latestRun=Pēdējā izpilde
#XFLD : Label for time Column
time=Laiks
#XFLD : Label for Duration Column
duration=Ilgums
#XFLD : Label for Peak Memory Column
peakMemory=Maksimālā atmiņa
#XFLD : Label for Number of Rows
numberOfRows=Rindu skaits
#XFLD : Label for Overall Number of Sources
overallNumberOfSources=Avotu vispārējais skaits
#XFLD : Label for Data Access Column
dataAccess=Datu piekļuve
#XFLD : Label for Local Tables
localTables=Lokālās tabulas
#XFLD : Label for federatedRemoteTables
federatedRemoteTables=Federatīvās attālās tabulas (ar ierobežotām adapteru iespējām)
#XTXT Text for initial state of the runtime metrics
initialState=Vispirms jums ir jāizpilda “Veiktspējas analīze”, lai iegūtu metriku. Tas var aizņemt kādu laiku, taču, ja nepieciešams, šo procesu varat atcelt.
#XMSG: Cancel confirmation message
CancelRunConfirmationMessage=Vai tiešām vēlaties atcelt veiktspējas analīzes pašreizējo izpildi?
#XTIT: Cancel dialog title
CancelRunTitle=Atcelt izpildi
#XFLD: Label for Number of Rows
NUMBER_ROWS=Rindu skaits
#XFLD: Label for Overall Number of Sources
OVERALL_NUMBER_SOURCES=Avotu vispārējais skaits
#XFLD: Label for Data Access
DATA_ACCESS=Datu piekļuve
#XFLD: Label for Remote tables
NUMBER_REMOTE_TABLES_LIMITED_ADAPTERS=Federatīvās attālās tabulas (ar ierobežotām adapteru iespējām)
#XFLD: Label for select statement
SELECT_STATEMENT=“SELECT * FROM VIEW LIMIT 1000”
#XFLD: Label for duration
SELECT_RUNTIME=Ilgums
#XFLD: Label for peak memory
SELECT_PEAK_MEMORY=Maksimālā atmiņa
#XFLD: Label for count statement
SELECT_COUNT_STATEMENT=“SELECT COUNT(*) FROM VIEW”
#XFLD: Label for count duration
SELECT_COUNT_RUNTIME=Ilgums
#XFLD: Label for count peak memory
SELECT_COUNT_PEAK_MEMORY=Maksimālā atmiņa
#XFLD: Label for count of number of local tables
NUMBER_LOCAL_TABLES_FILE=Lokālās tabulas (fails)
#XTXT: Text for running state of the runtime metrics
Running=Tiek izpildīts...
#XFLD: Label for time
Time=Laiks
#XFLD: Label for virtual access
PA_VIRTUAL=Virtuāls
#XFLD: Label for persisted access
PA_PERSISTED=Pastāvīgs
PA_PARTIALLY_PERSISTED=Daļēji pastāvīgs
#XTXT: Text for cancel
CancelRunSuccessMessage=Veiktspējas analīzes izpildes atcelšana.
#XTXT: Text for cancel error
CancelRunErrorMessage=Radās kļūda, atceļot veiktspējas analīzes izpildi.
#XTXT: Text for explain plan generation
ExplainPlanStarted=Plāna skaidrojuma ģenerēšana skatam "{0}".
#XTXT: Text for performance analysis
PerformanceAnalysisStarted=Veiktspējas analīzes sākšana skatam "{0}".
#XTXT: Text for performance analysis error
ErrorFetchingPerformanceAnalysis=Radās kļūda, ienesot veiktspējas analīzes datus.
#XTXT: Text for performance analysis error
conflictingTask=Veiktspējas analīzes uzdevums jau darbojas
#XFLD: Label for Errors
Errors=Kļūda(s)
#XFLD: Label for Warnings
Warnings=Brīdinājums(-i)
#XFLD: Tooltip for missing privileges
openViewAnalyzerButtonTooltipNoPrivilege=Jums ir nepieciešama privilēģija DWC_DATAINTEGRATION(update), lai atvērtu rīku “Skatu analizētājs”.
#XFLD: Tooltip for missing privileges
generateExplainPlanButtonTooltipNoPrivilege=Jums ir nepieciešama privilēģija DWC_RUNTIME(read), lai ģenerētu plāna skaidrojumu.



#XFLD: Label for frequency column
everyLabel=Ik pēc
#XFLD: Plural Recurrence text for Hour
hoursLabel=Stundām
#XFLD: Plural Recurrence text for Day
daysLabel=Dienām
#XFLD: Plural Recurrence text for Month
monthsLabel=Mēnešiem
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minūtēm
