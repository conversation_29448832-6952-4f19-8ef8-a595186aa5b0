
#XBCB: Breadcrumb
viewMonitorBreadcrumb={spaceId}

#XFLD: Label for persisted view column
SOURCE=Šaltinis
#XFLD: Label for persisted view column
NAME=Pavadinimas
#XFLD: Label for persisted view column
NAME_LABEL=Verslo pavadinimas
#XFLD: Label for persisted view column
NAME_LABELNew=Obje<PERSON><PERSON> (verslo pavadinimas)
#XFLD: Label for persisted view column
TECHINCAL_NAME=Techninis pavadinimas
#XFLD: Label for persisted view column
TECHINCAL_NAMENew=Objekt<PERSON> (techninis pavadinimas)
#XFLD: Label for persisted view column
DATA_PERSISTENCY=Duomenų prieiga
#XFLD: Label for persisted view column
STATUS=Būsena
#XFLD: Label for persisted view column
LAST_UPDATED=Paskutinį kartą atnaujinta
#XFLD: Label for persisted view column
IN_MEMORY_SIZE=Saugyklai naudojama atmintis (MB)
#XFLD: Label for persisted view column
DISK_SIZE=Saugyklai naudojamas diskas (MB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_NEWW=Dydis atmintyje (MiB)
#XFLD: Label for persisted view column
IN_MEMORY_SIZE_HEADER=Dydis atmintyje
#XFLD: Label for persisted view column
DISK_SIZE_NEWW=Dydis diske (MiB)
#XFLD: Label for persisted view column
DISK_SIZE_HEADER=Dydis diske
#XFLD: Label for schedule owner column
txtScheduleOwner=Planuoti savininką
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Rodo tvarkaraščio autorių
#XFLD: Label for persisted view column
PERSISTED=Išlaikyta
#XFLD: Label for persisted view column
TYPE=Tipas
#XFLD: Label for View Selection Dialog column
changedOn=Keitimo data
#XFLD: Label for View Selection Dialog column
createdBy=Autorius
#XFLD: Label for log details column
txtViewPersistencyLogs=Peržiūrėti žurnalus
#XFLD: Label for log details column
txtViewPersistencyLogsNew=Išsami informacija
#XFLD: text for values shown for Ascending sort order
SortInAsc=Rūšiuoti didėjimo tvarka
#XFLD: text for values shown for Descending sort order
SortInDesc=Rūšiuoti mažėjimo tvarka
#XFLD: Label for persisted view monitor heading
ViewMonitor_Heading=Rakursų stebėjimas
#XFLD: Label for persisted view monitor sub-heading
ViewMonitor_SubHeading=Stebėti ir tvarkyti rakursų išlaikymą


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ DynamicPageHeader ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: text for values shown in column Persistence Status
txtLoading=Įkeliama
#XFLD: text for values shown in column Persistence Status
txtRunning=Vykdoma
#XFLD: text for values shown in column Persistence Status
txtAvailable=Prieinama
#XFLD: text for values shown in column Persistence Status
txtError=Klaida
#XMSG: Error message for unknown Persistence type that gets logged in Kibana
unknownReplicationTypeError=Replikavimo tipas „{0}“ nepalaikomas.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Content ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Parametrai, naudoti paskutiniam duomenų pastovumui vykdyti:
#XMSG: Message for input parameter name
inputParameterLabel=Įvesties parametras
#XMSG: Message for input parameter value
inputParameterValueLabel=Reikšmė
#XMSG: Message for persisted data
inputParameterPersistedLabel=Išlaikyta
#XTIT: Count for View in ViewMonitor Toolbar
txtViewCount=Rakursai ({0})
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Rakurso išlaikymas
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Duomenų išlaikymas
#XBUT: Button to clear the selected view persistance
ClearViewPersistance=Sudengti
#XBUT: Button to stop the selected view persistance
stopPersistance=Stabdyti išlaikymą
#XFLD: Placeholder for Search field
txtSearch=Ieškoti
#XBUT: Tooltip for refresh button
txtRefresh=Atnaujinti
#XBUT: Tooltip for add view button
txtDeleteView=Naikinti išlaikymą
#XBUT: Tooltip for load new peristence
loadNewPersistence=Iš naujo paleisti išlaikymą
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Įkelti naują momentinę kopiją
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Pradėti duomenų išlaikymą
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Pašalinti išlaikomus duomenis
#XMSG: success message for starting persistence
startPersistenceSuccess=Mes išlaikome rakursą „{0}“.
#XMSG: success message for stopping persistence
stopPersistenceSuccess=Mes šaliname pastovius rakurso „{0}“ duomenis.
#XMSG: success message for stopping persistence
deletePersistedViewSuccess=Pašaliname rakursą „{0}“ iš stebėjimo sąrašo.
#XMSG: error message during start persistence of a view (backend error)
ERROR_STARTING_PERSISTENCE=Pradedant rakurso „{0}“ duomenų pastovumą įvyko klaida.
#XMSG: error message during start persistence of a view (backend error)
PARAMERROR_STARTING_PERSISTENCE=Rakurso „{0}“ pastovumas nepalaikomas, nes yra įvesties parametrų.
#XMSG: error message during start persistence of a view (backend error)
NUMBER_OF_PARAMETER_STARTING_PERSISTENCE=Rakurso „{0}“ pastovumas nepalaikomas, nes jis turi daugiau nei vieną įvesties parametrą.
#XMSG: error message during start persistence of a view (backend error)
PARAMETER_DEFAULT_VALUE_STARTING_PERSISTENCE=Rakurso „{0}“ pastovumas nepalaikomas, nes įvesties parametras neturi numatytosios reikšmės.
#XMSG: error message during start persistence of a view (backend error)
REDEPLOYMENT_DAC_REQUIRED=Norėdami palaikyti duomenų pastovumą, turite iš naujo įdiegti duomenų prieigos kontrolę (DAC) „{0}“.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_STARTING_PERSISTENCE=Rakursas „{0}“ negali būti pastovus, nes jis naudoja rakursą „{1}“, kuris turi duomenų prieigos kontrolę (angl. Data Access Control - DAC).
#XMSG: error message during start persistence of a view (backend error)
DACERROR_CONSUMED_EXTERNAL_STARTING_PERSISTENCE=Rakursas „{0}“ negali būti pastovus, nes jis naudoja rakursą su duomenų prieigos kontrole (DAC), kuri priklauso kitai sričiai.
#XMSG: error message during start persistence of a view (backend error)
DACERROR_STRUCTURE_STARTING_PERSISTENCE=Rakursas „''{0}“ negali būti pastovus, nes vieno ar kelių jo duomenų prieigos valdiklių (DAC) struktūra nepalaiko duomenų pastovumo.
#XMSG: error message during stop persistence of a view (backend error)
STOP_PERSISTENCE_ERROR=Įvyko klaida stabdant rakurso „{0}“ pastovumą.
#XMSG: error message during stop persistence of a view (backend error)
DELETE_PERSISTED_VIEW_ERROR=Įvyko klaida naikinant pastovų rakursą „{0}“.
#XMSG: Warning message for removing the persisted data
RemovePersistency_Confirm_Msg=Ar norite panaikinti pastovius duomenis ir perjungti į rakurso „{0}“ virtualiąją prieigą?
#XMSG: Warning message for delete the view persistence
DeletePersistency_Confirm_Msg=Ar norite pašalinti rakursą iš stebėjimo sąrašo ir panaikinti rakurso „{0}“ išlaikomus duomenis?
#XMSG: error message for reading data from backend
txtReadBackendError=Regis, įvyko klaida nuskaitant iš galutinio apdorojimo sistemos.
#XFLD: Label for No Data Error
NoDataError=Klaida
#XMSG: message for conflicting task
Task_Already_Running=Rakursui „{0}“ jau vykdoma prieštaringa užduotis.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Show All Views~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Text for show all views button
allViewsText=Visi rodiniai ({0})
#XBUT: Text for show scheduled views button
scheduledText=Suplanuota ({0})
#XBUT: Text for show persisted views button
persistedText=Išlaikyta ({0})
#XBUT: Text for start analyzer button
startAnalyzer=Paleisti peržiūros analizės priemonę
#XFLD: Message if repository is unavailable
repositoryErrorMsg=Saugykla nepasiekiama ir tam tikros funkcijos yra išjungtos.

#XFLD: Data Access - Virtual
Virtual=Virtuali
#XFLD: Data Access - Persisted
Persisted=Išlaikyta

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Non-Persisted View Selection Dialog~~~~~~~~~~~~~~~~~~~~~~~

#XTIT: Title for Non-Persisted View selection dialog
select_source=Pasirinkti išlaikytiną rakursą

#XTIT: Placeholder for Non-Persisted View selection dialog search
searchView=Ieškoti rakursų
#XTIT: No data in the list of non-persisted view
No_Data=Duomenų nėra
#XBUT: Button to select non-persisted view
ok=Gerai
#XBUT: Button to close the non-persisted views selection dialog
cancel=Atšaukti

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~View persistency log message key~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message for view persistency logs
START_STOP_PERSISTENCY=Pradedamas „{1}“ duomenų pastovumo užduoties vykdymas.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY=Išlaikomi rakurso „{1}“ duomenys.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY)
START_VIEW_PERSISTENCY_PROCESS=Pradedamas rakurso „{0}“ duomenų išlaikymo procesas.
#XFLD: Message for view persistency manual partition load
START_VIEW_PERSISTENCY_PROCESS_MANUAL_PARTITIONS=Pradedamas rakurso „{0}“ duomenų išlaikymo procesas su pasirinktais skaidinio ID: „{1}“.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY=Šaliname pastovius rakurso „{1}“ duomenis.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY)
REMOVE_VIEW_PERSISTENCY=Pradedamas rakurso „{0}“ pastovių duomenų šalinimo procesas.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_SUCCESS=Rakurso „{1}“ duomenys išlaikyti.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_SUCCESS)
START_VIEW_PERSISTENCY_PROCESS_SUCCESS=Rakurso „{0}“ duomenys išlaikyti.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_VIEW_PERSISTENCY_SUCCESS=Pastovūs duomenys pašalinti. Prieiga prie rakurso „{1}“ duomenų atkurta.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_SUCCESS)
REMOVE_VIEW_PERSISTENCY_SUCCESS=Rakurso „{0}“ pastovių duomenų šalinimo procesas baigtas.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_FAILED=Rakurso „{1}“ duomenų išlaikyti nepavyko.
#XFLD: Message for view persistency logs (new message to replace START_VIEW_PERSISTENCY_FAILED)
START_VIEW_PERSISTENCY_PROCESS_FAILED=Rakurso „{0}“ duomenų išlaikyti nepavyko.
#XFLD: Message for view persistency logs (for messages created before version 2022.05)
STOP_VIEW_PERSISTENCY_FAILED=Rakurso „{1}“ pastovių duomenų pašalinti nepavyko.
#XFLD: Message for view persistency logs (new message to replace STOP_VIEW_PERSISTENCY_FAILED)
REMOVE_VIEW_PERSISTENCY_FAILED=Rakurso „{0}“ pastovių duomenų pašalinti nepavyko.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_SUCCESS=Išlaikyta rakurso „{1}“ įrašų: {3}.
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace START_PERSISTENCY_RECORD_COUNT_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS={0} įrašai įterpti į rakurso „{1}''“ pastovumo lentelę.
#XFLD: Message used by SEAL repository for view persistency logs (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS_V2={0} įrašai įterpti į rakurso „{1}“ duomenų pastovumo lentelę. Sunaudota atminties: {2} GiB.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_SUCCESS=Pašalinta rakurso „{1}“ pastovių duomenų: {3}.
#XFLD: Message used by SEAL repository for view persistency logs (new message to replace STOP_PERSISTENCY_RECORD_COUNT_SUCCESS)
REMOVE_VIEW_PERSISTENCY_RECORD_COUNT_SUCCESS=Išlaikyti duomenys pašalinti, „{0}“ išlaikyti įrašai panaikinti.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_FAILED=Gauti rakurso „{1}“ recordCount nepavyko.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_RECORD_COUNT_FAILED=Gauti rakurso „{1}“ recordCount nepavyko.
#XFLD: Message for view persistency logs (for messages created before version 2022.03)
STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=„{1}“ tvarkaraštis panaikintas.
#XFLD: Message for view persistency logs (new message to replace STOP_PERSISTENCY_DELETE_SCHEDULE_SUCCESS)
REMOVE_VIEW_PERSISTENCY_DELETE_SCHEDULE_SUCCESS=Rakurso „{0}“ tvarkaraštis panaikintas.
#XFLD: Message for view persistency logs
STOP_PERSISTENCY_DELETE_SCHEDULE_FAILED=„{1}“ tvarkaraščio panaikinti nepavyko.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_REDEPLOY=Negalime išlaikyti rakurso „{0}“, nes nuo tada, kai pradėjote palaikyti jo pastovumą, jis buvo pakeistas ir įdiegtas. Pabandykite išlaikyti rakursą dar kartą arba palaukite kito suplanuoto vykdymo.
#XFLD: Message for view persistency logs
VIEW_PERSISTENCY_ABORTED_DELETE=Negalime išlaikyti rakurso „{0}“, nes nuo tada, kai pradėjote palaikyti jo pastovumą, jis buvo panaikintas.
#XFLD: Message for successful data persistency of a single partition (for messages created before version 2022.03)
START_PERSISTENCY_SINGLE_PARTITION_SUCCESS=Reikšmių „{1}“ <= {2} < „{3}“ skaidinyje yra {0} įrašų.
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (new message to replace START_PERSISTENCY_SINGLE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS=Į reikšmių „{1}“ <= {2} < „{3}“ skaidinį įterpta {0} įrašų.
#XFLD: Message used by SEAL repository for successful data persistency of a single partition (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_SINGLE_PARTITION_SUCCESS_V2=Į reikšmių „{1}“ <= „{2}“ < „{3}“ skaidinį įterpta {0} įrašų. Sunaudota atminties: {4} GiB.
#XFLD: Message for successful replication of a "others" partition (for messages created before version 2022.03)
START_PERSISTENCY_OTHERS_PARTITION_SUCCESS=Skaidinyje „Kiti“ yra {0} įrašų.
#XFLD: Message used by SEAL repository for successful replication of a "others" partition (new message to replace START_PERSISTENCY_OTHERS_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_OTHERS_PARTITION_SUCCESS=Skaidinyje „Kiti“ yra įterpta {0} įrašų.
#XFLD: Message for view persistency record count of partitions (for messages created before version 2022.03)
START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS=Rakurso „{1}“ {3} įrašai išlaikyti {4} skaidiniuose.
#XFLD: Message used by SEAL repository for view persistency record count of partitions (new message to replace START_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS)
START_VIEW_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS={0} įrašai įterpti į rakurso „{1}''“ pastovumo lentelę {2} skaidiniuose.
#XFLD: Message used by SEAL repository for view persistency for partition wise refresh
START_VIEW_PERSISTENCY_RECORD_COUNT_REFRESHED_PARTITIONS_SUCCESS={0} įrašai įterpti į rakurso „{1}“ duomenų pastovumo lentelę. Atnaujinta skaidinių: {2}; užrakinta skaidinių: {3}; iš viso skaidinių: {4}
#XFLD: Message used by SEAL repository for view persistency for manual partition load
START_VIEW_PERSISTENCY_RECORD_COUNT_MANUAL_PARTITIONS_SUCCESS={0} įrašai įterpti į rakurso „{1}''“ pastovumo lentelę {2} pasirinktuose skaidiniuose.
#XFLD: Message used by SEAL repository for view persistency for locked partition data load
START_VIEW_PERSISTENCY_RECORD_COUNT_LOCKED_PARTITIONS_SUCCESS={0} įrašai įterpti į rakurso „{1}“ duomenų pastovumo lentelę. Atnaujinta skaidinių: {2}; užrakinta nepakeistų skaidinių: {3}; iš viso skaidinių: {4}
#XMSG: Message for unexpected error (for messages created before version 2022.03)
START_PERSISTENCY_UNEXPECTED_ERROR=Įvyko nenumatyta klaida išlaikant rakurso „{0}“ duomenis.
#XMSG: Message for unexpected error (new message to replace START_PERSISTENCY_UNEXPECTED_ERROR)
START_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Įvyko nenumatyta klaida išlaikant rakurso „{0}“ duomenis.
#XMSG: Message for Locked space error
START_VIEW_PERSISTENCY_LOCKED_SPACE_ERROR=Rakurso „{0}“ pastovumo išlaikyti negalima, nes sritis „{1}“ užrakinta.
#XMSG: Message for unexpected error (for messages created before version 2022.03)
STOP_PERSISTENCY_UNEXPECTED_ERROR=Įvyko nenumatyta klaida šalinant išlaikytus rakurso „{0}“ duomenis.
#XMSG: Message for unexpected error (new message to replace STOP_PERSISTENCY_UNEXPECTED_ERROR)
REMOVE_VIEW_PERSISTENCY_UNEXPECTED_ERROR=Įvyko nenumatyta klaida šalinant rakurso „{0}“ pastovumą.
#XMSG: Message for ivalidated view
VIEW_PERSISTENCY_ABORTED_DEFINITION_INVALID=Rakurso „{0}“ apibrėžimas tapo negaliojantis tikriausiai dėl pasikeitusio objekto, kurį tiesiogiai arba netiesiogiai naudoja rakursas. Norėdami išspręsti problemą, pabandykite įdiegti rakursą iš naujo arba identifikuokite pagrindinę priežastį.
#XMSG: Message for persisted data removal (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT=Pastovūs duomenys pašalinti diegiant rakursą „{0}“.
#XMSG: Message for persisted data removal of a consuming view
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW=Pastovūs duomenys pašalinti diegiant sunaudotą rakursą „{0}“.
#XMSG: Message for persisted data removal of a consuming view due to a dac change
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT_CONSUMED_VIEW_DAC_CHANGED=Pastovūs duomenys pašalinti diegiant sunaudotą rakursą „{0}“, nes jo duomenų prieigos kontrolė pakeista.
#XMSG: Message for persisted data removal (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DEPLOYMENT)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DEPLOYMENT=Pastovūs duomenys pašalinti diegiant rakursą „{0}“.
#XMSG: Message for deleted view (for messages created before version 2022.03)
VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION=Pastovumas pašalintas naikinant rakursą „{0}“.
#XMSG: Message for deleted view (new message to replace VIEW_PERSISTENCY_REMOVAL_TRIGGERED_BY_DELETION)
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_DELETION=Pastovumas pašalintas naikinant rakursą „{0}“.
#XMSG Message for removal of persisted data, because prerequisites not fulfilled
REMOVE_VIEW_PERSISTENCY_TRIGGERED_BY_PREREQUISITES_NOT_FULFILLED=Nuolatiniai duomenys pašalinti, nes nebeatitinka duomenų pastovumo būtinosios sąlygos.
#XMSG: Message for inconsistent persistency
VIEW_PERSISTENCY_INCONSISTENT_PERSISTENCY=Rakurso „{0}“ pastovumas tapo nenuoseklus. Kad išspręstumėte problemą, pašalinkite pastovius duomenis.
#XMSG: Message for prerequisites
START_PERSISTENCY_CHECK_PREREQUISITES=Tikrinamos rakurso „{0}“ pastovumo prielaidos.
#XMSG: Message for DAC deployed in deprecated way using table function and join (for messages created before 2025.05)
START_PERSISTENCY_DEPRECIATED_DAC=Rakursas „{0}“ įdiegtas naudojant mažinamą duomenų prieigos kontrolę (DAC). Norėdami pagerinti našumą, įdiekite rakursą iš naujo.
#XMSG: Message for DAC deployed in deprecated way using table function and join
START_PERSISTENCY_DEPRECATED_DAC=Rakursas „{0}“ įdiegtas naudojant mažinamą duomenų prieigos kontrolę (DAC). Norėdami pagerinti našumą, įdiekite rakursą iš naujo.
#XMSG: Message for roll back (for messages created before version 2022.03)
ROLLBACK_VIEW_PERSISTENCY=Įvyko klaida. Rakursui „{0}“ atkurta ankstesnė pastovumo būsena.
#XMSG: Message used by SEAL repository for rollback of running procedure to persist view (new message to replace ROLLBACK_VIEW_PERSISTENCY)
VIEW_PERSISTENCY_ROLLBACK=Įvyko klaida. Rakurso „{0}“ išlaikymo procesas sustabdytas ir keitimai atšaukti.
#XMSG: Message used by SEAL repository for rollback of running procedure to remove persisted data of view
REMOVE_VIEW_PERSISTENCY_ROLLBACK=Įvyko klaida. Rakurso „{0}“ pastovių duomenų šalinimo procesas sustabdytas ir keitimai atšaukti.
#XMSG: Message used by SEAL repository for persistency preparation
START_PERSISTENCY_PREPARATION=Ruošiamasi duomenų pastovumui.
#XMSG: Message used by SEAL repository for data insertion
START_INSERTING_PERSISTENCY_DATA=Įterpiami duomenys į pastovumo lentelę.
#XMSG: Message for null values - partitioning (for messages created before version 2022.03)
START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS={0} neapibrėžtos reikšmės įrašai įterpti į „kitą“ skaidinį.
#XMSG: Message used by SEAL repository for others partition values - partitioning (new message to replace START_VIEW_PERSISTENCY_NULL_VALUE_PARTITION_SUCCESS)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS=Į reikšmių „{2}“ < „{1}“ ARBA „{2}“ >= „{3}“ „kiti“ skaidinį įterpta {0} įrašų.
#XMSG: Message used by SEAL repository for others partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NOT_NULL_OTHERS_PARTITION_SUCCESS_V2=Į reikšmių „{2}‘“ < „{1}“ARBA „{2}“ >= „{3}“ „kiti“ skaidinį įterpta {0} įrašų. Sunaudota atminties: {4} GiB.
#XMSG: Message used by SEAL repository for null partition values - partitioning
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS=Į reikšmių „{1}“ YRA NEAPIBRĖŽTA „kiti“ skaidinį įterpta {0} įrašų.
#XMSG: Message used by SEAL repository for null partition values - partitioning (added memory usage parameter version 2022.07)
START_VIEW_PERSISTENCY_NULL_OTHERS_PARTITION_SUCCESS_V2=Į reikšmių „{1}“ YRA NEAPIBRĖŽTA „kiti“ skaidinį įterpta {0} įrašų. Sunaudota atminties: {2} GiB.
##XFLD:Message used by SEAL repository for remote statistics
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS=Duomenų įkėlimas apima: {0} nuotolinius sakinius. Bendras gautų įrašų skaičius: {1}. Bendra trukmė: {2} sek.
##XFLD:Message used by SEAL repository for remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_STATISTICS_WITH_PARTITIONS=Duomenų įkėlimas apima {0} skaidinių naudojimą su {1} nuotoliniais sakiniais. Bendras gautų įrašų skaičius: {2}. Bendra trukmė: {3} sek.
##XFLD:Message used by SEAL repository with UI ID for instructions of remote statistics with partitions
START_VIEW_PERSISTENCY_REMOTE_STATEMENTS_INSTRUCTION=Nuotoliniai teiginiai, apdoroti vykdymo metu, gali būti rodomi atidarant nuotolinių užklausų stebėjimo priemonę skaidinio pranešimų išsamioje informacijoje.
##XFLD:Message for start reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS=Pradedamas procesas, kas būtų galima po diegimo pakartotinai naudoti esamus pastovius rakursui {0}.
##XFLD:Message used by SEAL repository for reattach view persitency
START_VIEW_PERSISTENCY_REATTACHMENT=Pradėti pakartotinai naudoti esamus pastovius duomenis.
##XFLD:Message used by SEAL repository for reattach view persitency
START_FINILIZING_VIEW_PERSISTENCY_REATTACHMENT=Pakartotinai naudojami esami pastovūs duomenys.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_COMPLETE=Esamų pastovių duomenų pakartotinio naudojimo procesas rakursui {0} baigtas.
##XFLD:Message for reattach view persitency
START_PERSISTENCY_REATTACH_PROCESS_FAILED=Nepavyko pakartotinai panaudoti esamų pastovių duomenų rakursui {0} po diegimo.
#XMSG: Message used by SEAL repository for finalizing persistency
START_FINALIZING_PERSISTENCY=Baigiamas pastovumas.
#XMSG: Message used by SEAL repository for restoring virtual access for view
REMOVE_VIEW_PERSISTENCY_VIRTUAL_ACCESS_RESTORED=Rakurso „{0}“ virtualioji duomenų prieiga atkurta.
#XMSG: Message used by SEAL repository for view that already has virtual access
REMOVE_VIEW_PERSISTENCY_ALREADY_VIRTUAL_ACCESS=Rakursui „{0}“ jau suteikta virtualioji duomenų prieiga. Pastovių duomenų nepašalinta.
#XMSG: Message for removing view persistency
VIEW_PERSISTENCY_REMOVED_FROM_MONITOR=Rakursas „{0}“ pašalintas iš rakursų stebėjimo priemonės.
#XMSG: Message for view doesnt exist.
VIEW_PERSISTENCY_VIEW_NOT_EXISTING=Rodinys „{0}“ nėra duomenų bazėje arba jis neteisingai įdiegtas, todėl negali likti visam laikui. Pabandykite iš naujo įdiegti rodinį, norėdami išspręsti problemą arba nustatykite šakninę priežastį.
#XMSG: Message for persistency not enabled.
VIEW_PERSISTENCY_NOT_ENABLED=Duomenų pastovumas neįgalintas. Paleiskite lentelę / rakursą iš naujo „{0}“ vietoje, kad įgalintumėte funkciją.
#XMSG: Message for unexpected error due to node crash
VIEW_PERSISTENCY_UNEXPECTED_ERROR_GENERAL=Paskutinis rakurso pastovumo vykdymas nutrauktas dėl techninių klaidų.
#XFLD: Message for view persistency memory consumption
VIEW_PERSISTENCY_MEMORY_CONSUMPTION=Rakurso pastovumo vykdymo laikui daugiausia sunaudota {0} GiB atminties.
#XFLD: Message for view persistency timeout
VIEW_PERSISTENCY_TIMEOUT=Rakurso {0} pastovumas pasiekė {1} val. skirtojo laiko pabaigą.
#XFLD: Message for not started asynchronous view persistency
VIEW_PERSISTENCY_ASYNC_NOT_STARTED=Didelė sistemos apkrova neleido pradėti asinchroniškai vykdyti rakurso pastovumą. Patikrinkite, ar lygiagrečiai nevyksta per daug užduočių.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency (for messages created before version 2022.05)
START_VIEW_PERSISTENCY_EXECUTION_METHOD_DROP_AND_RENAME=Esama pastovumo lentelė panaikinta ir pakeista nauja pastovumo lentele.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_RECREATE_AND_DROP=Esama pastovumo lentelė panaikinta ir pakeista nauja pastovumo lentele.
##XFLD:Message used by SEAL repository for avoid revalidation for view persistency
START_VIEW_PERSISTENCY_EXECUTION_METHOD_TRUNCATE_AND_COPY=Esama pastovumo lentelė atnaujinta naujais duomenimis.
##XFLD: Message for missing authorizations for view persistency
VIEW_PERSISTENCY_MISSING_AUTH=Duomenų pastovumui peržiūrėti trūksta įgaliojimų.
##XFLD: Message start of persistency cancellation
VIEW_PERSISTENCY_TRIGGER_CANCEL=Pradedama atšaukti procesą, kad būtų išsaugotas rodinys {0}.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_UNAVAILABLE_TASK_RUN=Nepavyko atšaukti rodinio išsaugojimo proceso, nes nėra vykdomos rodinio {0} duomenų pastovumo užduoties.
##XFLD: Message for canceling an invalid persistency task run
VIEW_PERSISTENCY_CANCEL_NONDETERMINISTIC_TASK_RUN=Nepavyko atšaukti rodinio išsaugojimo proceso, nes nėra vykdomos rodinio {0} duomenų pastovumo užduoties.
##XFLD: Message for canceling a non running persitency task
VIEW_PERSISTENCY_CANCEL_NON_RUNNING_SELECTED_TASK=Nepavyko atšaukti rodinio {0} išsaugojimo proceso, nes pasirinkta duomenų pastovumo užduotis {1} nevykdoma.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_UNREACHABLE_PERSISTENCY_RUN=Nepavyko atšaukti rodinio išsaugojimo proceso, nes rodinio {0} duomenų išsaugojimas dar nepradėtas.
##XFLD: Message for canceling an invalid persistency run
VIEW_PERSISTENCY_CANCEL_COMPLETED_PERSISTENCY_RUN=Nepavyko atšaukti rodinio {0} išsaugojimo proceso, nes jis jau yra baigtas.
##XFLD: Message for failed cancellation
VIEW_PERSISTENCY_ABORTED_CANCEL_EXECUTION=Nepavyko atšaukti proceso išsaugoti rodinį {0}.
##XFLD: Message for finalizing persistency cancellation
VIEW_PERSISTENCY_FINALIZE_CANCEL=Procesas sustabdyti rakurso {0} duomenų pastovumą pateiktas.
##XFLD: Message used in persist task when cancellation is caught
VIEW_PERSISTENCY_CANCELED=Procesas išsaugoti rodinį {0} sustabdytas.
##XFLD: Message used in persist task when cancelation is caught and performed through a cancelation task
VIEW_PERSISTENCY_CANCELED_VIA_CANCEL_TASK=Procesas išsaugoti rodinį {0} sustabdytas atliekant atšaukimo užduotį {1}.
##XFLD: Message used in cancel task when the task is triggered by deployment
VIEW_PERSISTENCY_CANCEL_TRIGGERED_BY_DEPLOYMENT=Atšaukiamas procesas, kad duomenys būtų išsaugoti diegiant rodinį {0}.
##XFLD: Message used in case the cancelation is retriggered for a running task
VIEW_PERSISTENCY_CANCEL_ATTEMPT_RETRIGGER_CANCEL=Ankstesnė rodinio išsaugojimo {0} atšaukimo užduotis jau buvo pateikta.
##XFLD: Message used to express the delay that may occur before the persistency task is stopped
VIEW_PERSISTENCY_CANCEL_DELAY_STOPPING_PERSISTENCY=Gali užtrukti, kol bus sustabdyta rodinio {0} išsaugojimo užduotis.
##XFLD: Message used to reflect the task log identifier of the current running persistency task
VIEW_PERSISTENCY_CANCEL_CURRENT_RUNNING_TASK=Rodino {0} duomenys išsaugomi su užduotimi {1}.
##XFLD:Message used by SEAL repository for locked partitions and DACs
VIEW_PERSISTENCY_LOCKED_PARTITIONS_DACS=DAC suteiktos prieigos gali būti pasikeitusios ir į jas neatsižvelgiama užrakintose skaidiniuose. Atrakinkite skaidinius ir įkelkite naują momentinę nuotrauką, kad pritaikytumėte pakeitimus.
##XFLD:Message used by SEAL repository for column structure change
VIEW_PERSISTENCY_COLUMN_STRUCTURE_CHANGE=Stulpelio struktūra buvo pakeista ir nebeatitinka esamos pastovumo lentelės. Pašalinkite pastovius duomenis ir pradėkite naują duomenų pastovumą, kad pastovumo lentelė būtų atnaujinta.
##XFLD: Message for out of memory
VIEW_PERSISTENCY_HANA_OUT_OF_MEMORY=Užduotis nepavyko dėl atminties trūkumo SAP HANA duomenų bazėje klaidos.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_EXCEPTION_TRANSACTION_ROLLBACK=Užduotis nepavyko dėl vidinės išimties SAP HANA duomenų bazėje.
##XFLD: Message for HANA error
VIEW_PERSISTENCY_INTERNAL_SQL_EXECUTION_ISSUE=Užduotis nepavyko dėl vidinės SQL vykdymo problemos SAP HANA duomenų bazėje.
##XFLD: Message for HANA out of memory result
VIEW_PERSISTENCY_OUT_OF_MEMORY_RESULT_MESSAGE=HANA atminties trūkumo įvykio priežastis: {0}
##XFLD: Message for admission control rejection
VIEW_PERSISTENCY_HANA_ADMISSION_CTRL_REJECTION=Užduotis nepavyko dėl SAP HANA leidimų kontrolės atmetimo.
##XFLD: Message for HANA back pressure / rate limit of HANA client
VIEW_PERSISTENCY_HANA_CLIENT_RATE_LIMIT_REJECTION=Užduotis nepavyko dėl per didelio skaičiaus aktyvių SAP HANA jungčių.
#XMSG: Message for invalid persisted table name
VIEW_PERSISTENCY_PERSISTED_TABLE_INVALID=Įvyko klaida ir išlaikyta lentelė tapo nepasiekiama. Norėdami išspręsti problemą, pašalinkite išlaikytus duomenis ir rakursą vėl paverskite pastoviu.
#XMSG: Message for usahe of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION=Rakurso išlaikyti negalima. Jame naudojama nuotolinė lentelė, pagrįsta nuotoliniu šaltiniu su įjungtu naudotojo perdavimu. Patikrinkite rakurso kilmę.
#XMSG: Message for dynamic usage of remote source with user propagation
VIEW_PERSISTENCY_USER_PROPAGATION_DYNAMIC=Rakurso išlaikyti negalima. Jame naudojama nuotolinė lentelė, pagrįsta nuotoliniu šaltiniu su įjungtu naudotojo perdavimu. Ši nuotolinė lentelė gali būti dinamiškai naudojama per SQL scenarijaus rakursą. Rakurso paveldėjimas gali būti nerodomas lentelėje.
#XMSG: Message for insufficient privileges or consumption of view with DAC via dynamic SQL
VIEW_PERSISTENCY_INSUF_PRIV_OR_DAC_DYN_SQL=Jūsų įgaliojimai gali būti nepakankami. Atidarykite duomenų peržiūrą, kad patikrintumėte, ar turite būtinus įgaliojimus. Jeigu turite, antram rakursui, kurį naudoja dinaminis SQL scenarijus, gali būti taikoma duomenų prieigos kontrolė (DAC).
#XMSG: deployment of DAC is deprecated and view need to be redeployed to be able to persist data
VIEW_PERSISTENCY_DEPRECATED_DAC=Rakursas „{0}“ įdiegtas naudojant mažinamą duomenų prieigos kontrolę (DAC). Norėdami išlaikyti rakurso duomenis, įdiekite rakursą iš naujo.
##XFLD:Message used by SEAL repository for input parameter
START_VIEW_PERSISTENCY_INPUT_PARAMETER=Naudojama numatytoji įvesties parametro „{1}“ reikšmė „{0}“.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Elastic compute node keys~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
##XFLD:Message used to reflect disablement of ecn replica
ECN_DISABLE_EXISTING_REPLICA=Elastinio skaičiavimo mazgo dublikatas išjungtas.
##XFLD:Message used to reflect recreation of ecn replica
ECN_RECREATE_EXISTING_REPLICA=Elastinio skaičiavimo mazgo dublikatas atkurtas.
##XFLD:Message used to reflect re-enablement of ecn replica
ECN_ENABLE_EXISTING_REPLICA=Elastinio skaičiavimo mazgo dublikatas įgalintas iš naujo.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~Task Schedule Menu key~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Message to schedule task
scheduleText=Tvarkaraštis
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Kurti tvarkaraštį
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Redaguoti tvarkaraštį
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Naikinti tvarkaraštį
#XFLD: Refresh frequency field
refreshFrequency=Atnaujinimo dažnumas
#XFLD: Refresh frequency field
refreshFrequencyNew=Dažnumas
#XFLD: Refresh frequency field
refreshFrequencyNewNew=Suplanuotas dažnumas
#XBUT: label for None
none=Nėra
#XBUT: label for Real-Time replication state
realtime=Realiojo laiko
#XFLD: Label for table column
txtNextSchedule=Kitas vykdymas
#XFLD: Label for table column
txtNextScheduleNew=Suplanuotas kitas vykdymas
#XFLD: Label for table column
txtNumOfRecords=Įrašų skaičius
#XFLD: Label for scheduled link
scheduledTxt=Suplanuota
#XFLD: LABEL for partially persisted link
partiallyPersisted=Iš dalies išlaikyta
#XFLD: Text for paused text
paused=Pristabdyta

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ View Monitor Tasklog Details Action keys~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message for release lock actionn
releaseLockMsg=Jei vykdymas užtrunka ilgiau nei įprastai, gali reikšti, kad vykdymas nepavyko ir būsena nebuvo atnaujinta. \r\n Norėdami išspręsti problemą, galite atšaukti užrakinimą ir nustatyti jo būseną kaip nepavykusią.
#XFLD: Label for release lock dialog
releaseLockText=Atšaukti užrakinimą

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Column header tooltip ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: tooltip for table column
txtViewTooltip=Pastovaus rakurso pavadinimas
#XFLD: tooltip for table column
txtViewDataAccessTooltip=Tai nurodo rakurso prieinamumą
#XFLD: tooltip for table column
txtViewRefreshFreqTooltip=Tai nurodo, ar rakurso tvarkaraštis apibrėžtas
#XFLD: tooltip for table column
txtViewStatusTooltip=Gauti pastovaus rakurso būseną
#XFLD: tooltip for table column
txtViewLatestUpdateTooltip=Pateikia informaciją, kada pastovus rakursas buvo paskutinį kartą atnaujintas
#XFLD: tooltip for table column
txtViewNextRunTooltip=Jei nustatytas rakurso tvarkaraštis, peržiūrėkite, kada suplanuotas kitas vykdymas
#XFLD: tooltip for table column
txtViewNumOfRecordsTooltip=Sekti įrašų skaičių.
#XFLD: tooltip for table column
txtViewInMemorySizeTooltip=Sekite, kiek vietos rakursas naudoja atmintyje
#XFLD: tooltip for table column
txtViewDiskSizeTooltip=Sekite, kiek vietos rakursas naudoja diske
#XMSG: Expired text
txtExpired=Nebegalioja

#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Objekto „{0}“ negalima pridėti prie užduočių grandinės.

#~~~~~~~~~~~View Analyzer task log messages~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message indicating number of records for a view, and memory consumption in Mebibyte during simulation of view persistency
RECORDS_PEAK_MEMORY=Rakurse „{0}“ yra {1} įrašai (-ų). Rakurso duomenų pastovumo modeliavimas išnaudojo {2} MiB atminties.
#XMSG: View Analyzer execution failure
VIEW_ANALYZER_EXECUTION_FAILED=Rakurso analizės priemonės vykdymas nepavyko.
#XMSG: Missing authorizations for View Analyzer
VIEW_ANALYZER_MISSING_AUTH=Trūksta įgaliojimų rakurso analizės priemonei.
#XMSG: Maximum memory in Gibibyte is reached, therefore persistency simulation is stopped
MAXIMUM_MEMORY_REACHED=Modeliuojant rakurso „{1}“ duomenų pastovumą pasiektas didžiausias atminties naudojimas ({0} GiB). Todėl tolesni rakursų duomenų pastovumo modeliavimai nebus vykdomi.
#XMSG: Error during persistency simulation occurred
PERSISTENCY_SIMULATION_ERROR=Vykdant rakurso „{0}“ duomenų pastovumo modeliavimą įvyko klaida.
#XMSG: Persistency simulation is not executed, because prerequisites are not fulfilled and the view cannot be persisted
PREREQUISITES_NOT_FULFILLED=Rakurso „{0}“ duomenų pastovumo modeliavimas nevykdomas, nes neįvykdytos sąlygos ir rakursas negali būti išlaikomas pastovus.
#XMSG: Persistency simulation is not executed, because the view does not exist in the database
VIEW_DOES_NOT_EXIST=Turite įdiegti rakursą „{0}“, kad aktyvintumėte duomenų pastovumo modeliavimą.
#XMSG: Number of records cannot be determined, because the local table does not exist in the database
TABLE_DOES_NOT_EXIST=Vietinės lentelės „{0}“ duomenų bazėje nėra, todėl negalima nustatyti šios lentelės įrašų skaičiaus.
##XMSG: Message start of cancellation for View Analyzer task
CANCEL_VIEW_ANALYZER_TRIGGERED=Pateiktas procesas, skirtas rakurso „{1}“ analizės priemonės užduočiai „{0}“ sustabdyti. Gali būti atidėjimas, kol užduotis bus sustabdyta.
##XMSG: Message start to indicate that task is not active
CANCEL_VIEW_ANALYZER_NO_ACTIVE_TASK=Rakurso „{1}“ analizės priemonės užduotis {0} neaktyvi.
#XMSG: Cancel View Analyzer task failure
CANCEL_VIEW_ANALYZER_FAILED=Nepavyko atšaukti rakurso analizės priemonės užduoties.
##XMSG: Message used in View Analyzer execution task when task is canceled
VIEW_ANALYZER_CANCELED=Rakurso „{0}“ analizės priemonės užduotis sustabdyta per atšaukimo užduotį.
##XMSG: Message start of cancelation for Model Validation task
CANCEL_MODEL_VALIDATION_TRIGGERED=Pateiktas procesas, skirtas rakurso „{1}“ modelio tikrinimo užduočiai „{0}“ sustabdyti. Gali būti delsa, kol užduotis bus sustabdyta.
##XMSG: Message to indicate that task is not active
CANCEL_MODEL_VALIDATION_NO_ACTIVE_TASK=Rakurso „{1}“ modelio tikrinimo užduotis {0} neaktyvi.
#XMSG: Cancel Model Validation task failure
CANCEL_MODEL_VALIDATION_FAILED=Nepavyko atšaukti modelio tikrinimo užduoties.
##XMSG: Message used in Model Validation task when task is canceled
MODEL_VALIDATION_CANCELED=Rakurso „{0}“ modelio tikrinimo užduotis sustabdyta per atšaukimo užduotį.
#XMSG: Message for Locked space error, before starting Model Validation task
MODEL_VALIDATION_LOCKED_SPACE_ERROR=Negalima vykdyti rakurso „{0}“ modelio tikrinimo, nes sritis „{1}“ užrakinta.
##XMSG: Message used in View Analyzer execution task when determination of number of rows fails
NUMBER_ROWS_DETERMINATION_FAILED=Nustatant vietinės lentelės „{0}“ eilučių skaičių, įvyko klaida.
##XMSG: Message to indicate that SQL plan was created
EXECUTION_PLAN_CREATED=Rakurso „{0}“ SQL analizatoriaus plano failas sukurtas ir jį galima atsisiųsti.
#XFLD: Message to indicate starting of process to generate SQL Analyzer plan file
START_PLANVIZ_EXECUTION=Pradedamas rakurso „{0}“ SQL analizatoriaus plano failo generavimo procesas.
#XFLD: Message to indicate starting of View Analyzer process
VIEW_ANALYZER_EXECUTION_STARTED=Pradedamas rakursų analizatoriaus vykdymas.
##XMSG: Message to indicate that SQL plan file cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_PLANVIZ_NOT_FULFILLED=Rakurso „{0}“ SQL analizatoriaus failo plano negalima generuoti, nes neišpildytos duomenų pastovumo prielaidos.
##XMSG: Error message during  generation of SQL Analyzer plan file
PLANVIZ_ERROR=Generuojant rakurso „{0}“ SQL analizatoriaus plano failą įvyko klaida.
#XMSG: Message for Locked space error
VIEW_ANALYZER_LOCKED_SPACE_ERROR=Negalima vykdyti rakurso „{0}“ analizės priemonės, nes sritis „{1}“ užrakinta.
#XMSG: Message for persistency simulation without partitioning
PERSISTENCY_SIMULATION_WITHOUT_PARTITIONS=Modeliuojant duomenų pastovumą į rakurso „{0}“ skaidinius neatsižvelgiama.
#XMSG: Message for generation of SQL Analyzer plan file without considering partitioning
PLANVIZ_WITHOUT_PARTITIONS=Generuojant SQL analizatoriaus failo planą į rakurso „{0}“ skaidinį neatsižvelgiama.
#XMSG: Confirmation message for mass removal of persisted data
MassRemovePersistency_Confirm_Msg=Norite pašalinti pastovius duomenis ir perjungti duomenų prieigą atgal į virtualią prieigą?
#XMSG: Confirmation message for partial mass removal of selected persisted data
PartialMassRemovePersistency_Confirm_Msg={0} iš {1} pastovių rakursų gavo pastovių duomenų. \n Norite pašalinti pastovius duomenis ir perjungti duomenų prieigą atgal į virtualią prieigą?
#XMSG: success message for stopping mass persistence
stopMassPersistenceSuccess=Šaliname pasirinktų rakursų pastovius duomenis.
#XMSG: error message during mass stop persistence of a view (backend error)
STOP_MASS_PERSISTENCE_ERROR=Įvyko klaida stabdant rakurso pasirinktų rakursų pastovumą.
#XMSG: memory analysis is only performed in space {0}
NO_MEMORY_ANALYSIS=Atminties analizė apima tik srityje „{0}“ esančius objektus; „{1}“ „{2}“ praleistas.
##XMSG: Message to indicate that Explain Plan cannot be generated, because persistency prerequisites are not fulfilled.`
PREREQUISITES_EXPLAINPLAN_NOT_FULFILLED=Rodiniui „{0}“ aiškinamojo plano failo sugeneruoti negalima, nes neįvykdytos būtinosios pastovumo sąlygos.
#XMSG: Message for Explain Plan generation without partitioning
EXPLAINPLAN_WITHOUT_PARTITIONS=Generuojant aiškinamojo plano failą į rakurso „{0}“ skaidinį neatsižvelgiama.
#XFLD: Message to indicate starting of process to generate Explain Plan file
START_EXPLAINPLAN_EXECUTION=Pradedamas rakurso „{0}“ aiškinamojo plano failo generavimo procesas.
##XMSG: Message to indicate that Explain Plan was created
EXPLAINPLAN_CREATED=Rakurso „{0}“ aiškinamasis planas sugeneruotas. Galite jį rodyti spustelėdami „Rodyti išsamią informaciją“ arba atsisiųsti, jei turite reikalingas teises.
##XMSG: Error message during  generation of Explain Plan file
EXPLAINPLAN_ERROR=Generuojant rakurso „{0}“ aiškinamojo plano failą įvyko klaida.
##XMSG: Explain Plan file cannot be generated for the view {0}, because too many views are stacked on each other
EXPLAINPLAN_MODEL_TOO_COMPLEX=Sugeneruoti rakurso „{0}“ aiškinamojo plano failo negalima. Yra per daug vienas ant kito sudėtų rakursų. Sudėtingi modeliai gali sukelti atminties trūkumo klaidas ir lėtą veikimą. Rekomenduojama išlaikyti rakursą.

##XMSG: Message to indicate that the space is locked
PERFORMANCE_ANALYSIS_LOCKED_SPACE_ERROR=Negalima vykdyti rakurso „{0}“ našumo analizės, nes sritis „{1}“ užrakinta.
##XMSG: The performance analysis task was cancelled
PERFORMANCE_ANALYSIS_CANCELED=Rakurso „{0}“ našumo analizė atšaukta.
##XMSG: The performance analysis failed
PERFORMANCE_ANALYSIS_FAILED=Našumo analizė nepavyko.
##XMSG: The performance analysis is finished
PERFORMANCE_ANALYSIS_FINISHED=Rakurso „{0}“ našumo analizė baigta. Rodykite rezultatą spustelėję „Peržiūrėti išsamią informaciją“.
##XMSG: The view has input parameter with no default value
INPUT_PARAMETER_WITHOUT_DEFAULT_VALUE=Šio rakurso negalima analizuoti, nes jis turi parametrą be numatytosios reikšmės.
##XMSG: The view is not fully deployed
VIEW_NOT_FULLY_DEPLOYED=Šio rakurso negalima analizuoti, nes jis nevisiškai įdiegtas.
##XMSG: The view uses limited remote adapters
LIMITED_REMOTE_ADAPTER=Šis rakursas naudoja bent vieną nuotolinį adapterį su ribotomis galimybėmis, pvz., trūkstamu filtro nukreipimu arba „Skaičius“ palaikymu. Pastovūs arba replikuojami objektai gali pagerinti vykdymo laiko našumą.
##XMSG: The view uses remote adapter that does not support 'Limit'
REMOTE_ADAPTER_NOT_SUPPORTING_LIMIT=Šis rakursas naudoja bent vieną nuotolinį adapterį, kuris nepalaiko „Riba“. Galimai pasirinkta daugiau nei 1000 įrašų.
##XMSG: Performance analysis is executed by using default parameters of view'
INPUT_PARAMETER_WITH_DEFAULT_VALUE=Našumo analizė įvykdyta naudojant numatytąsias rakurso parametrų reikšmes.
##XMSG: Message to indicate that an error occurred
PERFORMANCE_ANALYSIS_ERROR=Atliekant rakurso „{0}“ našumo analizę įvyko klaida.
##XMSG: Message start of cancellation for Performance Analysis task
CANCEL_PERFORMANCE_ANALYSIS_TRIGGERED=Pateiktas procesas, skirtas rakurso „{1}“ našumo analizės užduočiai {0} sustabdyti. Gali būti atidėjimas, kol užduotis bus sustabdyta.
##XMSG: Message start to indicate that task is not active
CANCEL_PERFORMANCE_ANALYSIS_NO_ACTIVE_TASK=Rakurso „{1}“ našumo analizės užduotis {0} neaktyvi.
#XMSG: Cancel Performance Analysis task failure
CANCEL_PERFORMANCE_ANALYSIS_FAILED=Nepavyko atšaukti našumo analizės užduoties.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Priskirti tvarkaraštį man
#XBUT: Pause schedule menu label
pauseScheduleLabel=Pristabdyti tvarkaraštį
#XBUT: Resume schedule menu label
resumeScheduleLabel=Atkurti tvarkaraštį
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Šalinant tvarkaraščius įvyko klaida.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Priskiriant tvarkaraščius įvyko klaida.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Pristabdant tvarkaraščius įvyko klaida.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Atkuriant tvarkaraščius įvyko klaida.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Naikinti {0} tvarkaraščius
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Keičiamas {0} tvarkaraščių savininkas
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Pristabdomi {0} tvarkaraščiai
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Atkuriami {0} tvarkaraščiai
#XBUT: Select Columns Button
selectColumnsBtn=Pasirinkti stulpelius
#XFLD: Refresh tooltip
TEXT_REFRESH=Atnaujinti
#XFLD: Select Columns tooltip
text_selectColumns=Pasirinkti stulpelius


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Runtime Metrics ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Label for Runtime Metrics
RuntimeMetricsTitle=Vykdymo laiko metrika, skirta
#XFLD : Label for Run Button
runButton=Vykdyti
#XFLD : Label for Cancel Button
cancelButton=Atšaukti
#XFLD : Label for Close Button
closeButton=Uždaryti
#XFLD : Label for Open View Analyzer Button
openViewAnalyzerButton=Atidaryti rakursų analizatorių
#XFLD : Label for Generate Explain Plan Button
generateExplainPlanButton=Generuoti aiškinamąjį planą
#XFLD : Label for Previous Run Column
previousRun=Ankstesnis vykdymas
#XFLD : Label for Latest Run Column
latestRun=Paskutinis vykdymas
#XFLD : Label for time Column
time=Laikas
#XFLD : Label for Duration Column
duration=Trukmė
#XFLD : Label for Peak Memory Column
peakMemory=Atminties pikas
#XFLD : Label for Number of Rows
numberOfRows=Eilučių skaičius
#XFLD : Label for Overall Number of Sources
overallNumberOfSources=Bendras šaltinių skaičius
#XFLD : Label for Data Access Column
dataAccess=Duomenų prieiga
#XFLD : Label for Local Tables
localTables=Vietinės lentelės
#XFLD : Label for federatedRemoteTables
federatedRemoteTables=Sujungtos nuotolinės lentelės (su ribotomis adapterio galimybėmis)
#XTXT Text for initial state of the runtime metrics
initialState=Norėdami gauti metrikas, pirmiausia turite atlikti našumo analizę. Tai gali šiek tiek užtrukti, bet prireikus šį procesą galima nutraukti.
#XMSG: Cancel confirmation message
CancelRunConfirmationMessage=Ar tikrai norite atšaukti dabartinį našumo analizės vykdymą?
#XTIT: Cancel dialog title
CancelRunTitle=Atšaukti vykdymą
#XFLD: Label for Number of Rows
NUMBER_ROWS=Eilučių skaičius
#XFLD: Label for Overall Number of Sources
OVERALL_NUMBER_SOURCES=Bendras šaltinių skaičius
#XFLD: Label for Data Access
DATA_ACCESS=Duomenų prieiga
#XFLD: Label for Remote tables
NUMBER_REMOTE_TABLES_LIMITED_ADAPTERS=Sujungtos nuotolinės lentelės (su ribotomis adapterio galimybėmis)
#XFLD: Label for select statement
SELECT_STATEMENT=„SELECT * FROM VIEW LIMIT 1000“
#XFLD: Label for duration
SELECT_RUNTIME=Trukmė
#XFLD: Label for peak memory
SELECT_PEAK_MEMORY=Atminties pikas
#XFLD: Label for count statement
SELECT_COUNT_STATEMENT=„SELECT COUNT(*) FROM VIEW“
#XFLD: Label for count duration
SELECT_COUNT_RUNTIME=Trukmė
#XFLD: Label for count peak memory
SELECT_COUNT_PEAK_MEMORY=Atminties pikas
#XFLD: Label for count of number of local tables
NUMBER_LOCAL_TABLES_FILE=Vietinės lentelės (failas)
#XTXT: Text for running state of the runtime metrics
Running=Vykdoma...
#XFLD: Label for time
Time=Laikas
#XFLD: Label for virtual access
PA_VIRTUAL=Virtuali
#XFLD: Label for persisted access
PA_PERSISTED=Išlaikyta
PA_PARTIALLY_PERSISTED=Iš dalies išlaikyta
#XTXT: Text for cancel
CancelRunSuccessMessage=Našumo analizės nutraukimas.
#XTXT: Text for cancel error
CancelRunErrorMessage=Nutraukiant našumo analizės vykdymą įvyko klaida.
#XTXT: Text for explain plan generation
ExplainPlanStarted=Generuojamas rakurso „{0}“ aiškinamasis planas.
#XTXT: Text for performance analysis
PerformanceAnalysisStarted=Pradedama rakurso „{0}“ našumo analizė.
#XTXT: Text for performance analysis error
ErrorFetchingPerformanceAnalysis=Gaunant našumo analizės duomenis įvyko klaida.
#XTXT: Text for performance analysis error
conflictingTask=Našumo analizės užduotis jau vykdoma.
#XFLD: Label for Errors
Errors=Klaida (-os)
#XFLD: Label for Warnings
Warnings=Įspėjimas (-ai)
#XFLD: Tooltip for missing privileges
openViewAnalyzerButtonTooltipNoPrivilege=Norint atidaryti rakursų analizatorių reikalinga teisė „DWC_DATAINTEGRATION(update)“.
#XFLD: Tooltip for missing privileges
generateExplainPlanButtonTooltipNoPrivilege=Norint generuoti aiškinamąjį planą reikalinga teisė DWC_RUNTIME(read).



#XFLD: Label for frequency column
everyLabel=Kas
#XFLD: Plural Recurrence text for Hour
hoursLabel=Valandos
#XFLD: Plural Recurrence text for Day
daysLabel=Dienos
#XFLD: Plural Recurrence text for Month
monthsLabel=Mėnesiai
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minutės
