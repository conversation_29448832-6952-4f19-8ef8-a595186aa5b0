/** @format */

import { MessageHandler } from "../../reuse/utility/MessageHandler";
import { ContentType, HttpMethod, ServiceCall } from "../../reuse/utility/ServiceCall";
import { Activity, ApplicationId } from "../../tasklog/utility/Constants";

const bundleName = require("../i18n/i18n.properties");
const i18nModel = new sap.ui.model.resource.ResourceModel({ bundleName });
const oBundle = i18nModel.getResourceBundle();

export enum replicationState {
  Initialize = "I",
  None = "D",
  Available = "A",
  Error = "E",
}

export enum taskState {
  FAILED = "FAILED",
  COMPLETED = "COMPLETED",
}

export enum SERVICENAMES {
  persistedViews = "persistedViews",
  startPersistence = "startPersistence",
  stopPersistence = "stopPersistence",
  stopMassPersistence = "stopMassPersistence",
  viewPersistenceInfo = "viewPersistenceInfo",
  persistedViewsWithBusinessName = "persistedViewsWithBusinessName",
  allViews = "allViews",
  viewsWithRuns = "viewsWithRuns",
}

export function isOutputNodePersisted(oObject, oModel) {
  if (oModel && oModel.output) {
    const oPropertiesView = sap.ui
      .getCore()
      .byId(
        "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-EntityProperties--EntityPropertiesView"
      ) as sap.ui.core.mvc.View;
    const outputNodeName = oModel.output.technicalName;
    const objectName = oObject.technicalName;
    const oPersistencyData =
      oPropertiesView &&
      oPropertiesView.getModel("persistencePreviewModel") &&
      (oPropertiesView.getModel("persistencePreviewModel") as sap.ui.model.json.JSONModel).getData();
    if (
      !oObject.successorNode &&
      oPersistencyData &&
      oPersistencyData.dataPersistency &&
      oPersistencyData.dataPersistency === "Persisted" &&
      outputNodeName === objectName &&
      outputNodeName === oPersistencyData.viewName
    ) {
      return true;
    }
  }
  return false;
}

/**
 * Makes a Service call based on the service name and parameters
 *
 * @export
 * @param {string} spaceName
 * @param {string} serviceName
 * @param {*} parameters
 * serviceName              parameters
 * -----------              --------------------
 * persistedViews   -  spaceName required,
 * startPersistence -  spaceName, viewName required,
 * stopPersistence  - spaceName, viewName required,
 * viewPersistenceInfo - spaceName, viewName required,
 * @returns {Promise<any>}
 */
export function fetchViewPersistenceService(spaceName: string, serviceName: SERVICENAMES, ...parameters): Promise<any> {
  let sUrl = null,
    sViewName,
    typeMethod,
    data;
  switch (serviceName) {
    case SERVICENAMES.allViews:
    case SERVICENAMES.viewsWithRuns:
      const mode = serviceName === SERVICENAMES.allViews ? "all" : "withRuns";
      sUrl = `monitor/${spaceName}/persistedViews?mode=${mode}`;
      if (parameters?.length) {
        const [includeBusinessNames, ignoreThreshold] = parameters[0];
        if (includeBusinessNames === "businessName") {
          sUrl += "&includeBusinessNames=true";
        }
        if (ignoreThreshold) {
          sUrl += "&ignoreThresholdForBusinessNames=true";
        }
      }
      typeMethod = HttpMethod.GET;
      break;
    case SERVICENAMES.persistedViews:
      sUrl = "monitor/" + spaceName + "/persistedViews";
      typeMethod = HttpMethod.GET;
      break;
    case SERVICENAMES.persistedViewsWithBusinessName:
      sUrl = "monitor/" + spaceName + "/persistedViews" + "?includeBusinessNames=true";
      typeMethod = HttpMethod.GET;
      break;
    case SERVICENAMES.startPersistence:
      [sViewName] = parameters;
      sUrl = "tf/directexecute";
      typeMethod = HttpMethod.POST;
      const reqBody = {
        applicationId: ApplicationId.VIEWS,
        spaceId: spaceName,
        objectId: sViewName,
        activity: Activity.PERSIST,
      };
      data = JSON.stringify(reqBody);
      break;
    case SERVICENAMES.stopPersistence:
      [sViewName] = parameters;
      sUrl = "tf/directexecute";
      const requestBody = {
        applicationId: ApplicationId.VIEWS,
        spaceId: spaceName,
        objectId: sViewName,
        activity: Activity.REMOVE_PERSISTED_DATA,
      };
      data = JSON.stringify(requestBody);
      typeMethod = HttpMethod.POST;
      break;
    case SERVICENAMES.stopMassPersistence:
      sUrl = `persistence/${spaceName}/mass/persistedViews/stop`;
      typeMethod = HttpMethod.PUT;
      const [viewsList] = parameters;
      const jsonData = { views: [] };
      for (const view of viewsList) {
        jsonData.views.push({ viewName: view.viewName });
      }
      data = JSON.stringify(jsonData);
      break;
    case SERVICENAMES.viewPersistenceInfo:
      [sViewName] = parameters;
      sUrl = "monitor/" + spaceName + "/persistedViews/" + encodeURIComponent(sViewName);
      typeMethod = HttpMethod.GET;
      break;
    default:
      sUrl = "";
      break;
  }

  return new Promise((resolve, reject) => {
    ServiceCall.request<any>({
      url: sUrl,
      type: typeMethod,
      contentType: ContentType.APPLICATION_JSON,
      data: data ? data : null,
      success: function (data: any) {
        resolve(data);
      },
      error: function (err: any) {
        if (err.status >= 400) {
          reject(err);
        }
        resolve(undefined);
      },
    });
  });
}

export function getTaskScheduleListForView(spaceId, applicationId = ApplicationId.VIEWS) {
  return new Promise((resolve, reject) => {
    ServiceCall.request<any>({
      url: `tf/${spaceId}/schedules?applicationId=${applicationId}`,
      type: HttpMethod.GET,
      contentType: ContentType.APPLICATION_JSON,
      success: function (data: any) {
        resolve(data);
      },
      error: function (err: any) {
        if (err.status >= 400) {
          reject(err);
        }
        resolve(undefined);
      },
    });
  });
}

export function handleViewPersistenceResponse(oResponse, sView, spaceId) {
  if (oResponse && oResponse.responseJSON && oResponse.responseJSON.code === "taskAlreadyRunning") {
    MessageHandler.exception({ exception: oResponse, message: oBundle.getText("Task_Already_Running", [sView]) });
  } else if (oResponse && oResponse.responseJSON && oResponse.responseJSON.code === "viewPersistencyNotEnabled ") {
    MessageHandler.exception({
      exception: oResponse,
      message: oBundle.getText("VIEW_PERSISTENCY_NOT_ENABLED", [spaceId]),
    });
  } else if (oResponse && oResponse.responseJSON && oResponse.responseJSON.details) {
    MessageHandler.exception({ exception: oResponse, message: oBundle.getText("ERROR_STARTING_PERSISTENCE", [sView]) });
  } else {
    MessageHandler.error(oBundle.getText("ERROR_STARTING_PERSISTENCE", [sView]));
  }
}

export function openPartiallyPersistedInfo(source: any, inputParamsDetails: any, latestUpdate: any, view: any) {
  const popOverFragment = require("../view/partiallyPersistedPopover.fragment.xml");
  const inputParamDetailsParsed = JSON.parse(inputParamsDetails);
  (view.getView().getModel("partiallyPersistedModel") as sap.ui.model.json.JSONModel).setProperty(
    "/paramName",
    inputParamDetailsParsed.inputParametersDetails[0].inputParameterName
  );
  (view.getView().getModel("partiallyPersistedModel") as sap.ui.model.json.JSONModel).setProperty(
    "/defaultValue",
    inputParamDetailsParsed.inputParametersDetails[0].inputParameterDefaultValue
  );
  (view.getView().getModel("partiallyPersistedModel") as sap.ui.model.json.JSONModel).setProperty(
    "/persistedAt",
    view.formatDateTime(latestUpdate)
  );
  if (!view.partiallyPersistedPopover) {
    view.partiallyPersistedPopover = sap.ui.xmlfragment(
      view.getView().getId() + "--partiallyPersistedPopover",
      popOverFragment,
      view
    ) as sap.m.Popover;
    view.getView().addDependent(view.partiallyPersistedPopover);
    view.partiallyPersistedPopover.openBy(source, true);
  } else {
    view.partiallyPersistedPopover.openBy(source, true);
  }
}
