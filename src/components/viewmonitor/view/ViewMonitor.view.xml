<mvc:View
  height="100%"
  controllerName="sap.cdw.components.viewmonitor.controller.ViewMonitor"
  xmlns="sap.m"
  xmlns:mvc="sap.ui.core.mvc"
  xmlns:layout="sap.ui.layout"
  xmlns:core="sap.ui.core"
  xmlns:t="sap.ui.table"
  xmlns:f="sap.f"
  xmlns:unified="sap.ui.unified"
  xmlns:chart="sap.suite.ui.microchart"
  xmlns:ac="sap.cdw.components.reuse.control.actionchecker"
  xmlns:cd="http://schemas.sap.com/sapui5/extension/sap.ui.core.CustomData/1"
>
  <ac:ActionChecker
  id="viewTableMonitorAc"
  class="sapUiContentPadding"
  hanaState="{path:'circuitbreaker>/DataHANA', formatter:'.formatter.hanaStateFormatter'}"
  hanaProvisioningState="{path:'circuitbreaker>/DataHANAProvisioningState', formatter:'.formatter.hanaStateFormatter'}"
  spaceLocked="{= ${diPageModel>/isSpaceLocked} === true }"
  actionControlIds="refeshTableButton,viewPersistencyActionMenuBtn,scheduleMenu,viewScheduleAuth"
  hiddenMode="true"
  >
  </ac:ActionChecker>
  <f:DynamicPage
    id="viewMonitoringPage"
    preserveHeaderStateOnScroll="false"
    headerExpanded="false"
  >
    <f:title>
      <f:DynamicPageTitle
        class="viewMonitorDynPageTitle"
        >
        <!-- <f:heading>
          <Title
            id="dyntitle"
            text="{i18n>ViewMonitor_Heading}"
            class="sapUiTinyMarginBottom"
          />
        </f:heading>
        <f:expandedContent>
        </f:expandedContent>
        <f:snappedContent>
          <Label text="{i18n>ViewMonitor_SubHeading}" />
        </f:snappedContent> -->
        <f:content>

          <GenericTag
            text="{= ${featureflags>/DWCO_DI_MONITOR_UI_IMPROVEMENTS} ? ${i18n>IN_MEMORY_SIZE_HEADER} : ${i18n>IN_MEMORY_SIZE}}"
            design="StatusIconHidden"
            id="viewMonitorGenericTagInMemory"
          >
            <ObjectNumber
              number="{path: '/totalMemorySize', formatter: '.formatNumber'}"
              unit="MiB"
              emphasized="true"
              id="objectNumPersistencyInMemory"
            />
          </GenericTag>
          <GenericTag
            text="{= ${featureflags>/DWCO_DI_MONITOR_UI_IMPROVEMENTS} ? ${i18n>DISK_SIZE_HEADER} : ${i18n>DISK_SIZE}}"
            design="StatusIconHidden"
            class="sapUiMediumMarginBegin sapUiTinyMarginEnd"
            id="viewMonitorgenericTagDiskSize"
          >
            <ObjectNumber
              number="{path: '/totalDiskSize', formatter: '.formatNumber'}"
              unit="MiB"
              emphasized="true"
              id="objectNumPersistencyDiskSize"
            />
          </GenericTag>
          <!--
            To-Do: Kept this code to implement bullet micro chart
            <VBox>
            <Text
              id="viewMonitorgenericTagDiskSize"
              class="sapUiSmallMarginTop"
              text="{i18n>DISK_SIZE}"
            />
            <chart:BulletMicroChart targetValue="{/totalDiskSize}" minValue="0" maxValue="{/totalDiskSize}"
									  size="Responsive" height="18px" >
						<chart:actual>
							<chart:BulletMicroChartData value="{/totalMemorySize}" color="Good"/>
						</chart:actual>
					</chart:BulletMicroChart>
          </VBox>  -->
        </f:content>
      </f:DynamicPageTitle>
    </f:title>
    <f:content>
      <t:Table
        id="viewMonitorTable"
        minAutoRowCount="10"
        visibleRowCountMode="Auto"
        enableBusyIndicator="true"
        selectionMode="MultiToggle"
        selectionBehavior="Row"
        filter=".fireOnFilterColumn"
        rows="{path:'/tables', templateShareable:false}"
        rowActionCount="1"
        rowSelectionChange="onRowSelectionChange"
        showNoData="true"
        sort=".fireOnSort"
        noData="{i18n>No_Data}"
        class="sapFDynamicPageAlignContent noColumnBorder"
        width="auto"
      >
        <t:extension>
          <VBox
            width="100%"
            class="sapUiTinyMarginBottom"
          >
            <mvc:XMLView
              id="viewScheduleAuth"
              viewName="sap.cdw.components.taskscheduler.view.TaskScheduleAuth"
              visible="{= ${featureflags>/DWC_DUMMY_SPACE_PERMISSIONS} ? ${privilege>/DWC_DATAINTEGRATION/execute} === true : ${privPersistedView>/execute} === true }"
              cd:actionId="monitoring/editor/authFlow"
            />
            <mvc:XMLView
              id="viewScheduleDialog"
              viewName="sap.cdw.components.taskscheduler.view.TaskSchedule"
              visible="{= ${featureflags>/DWC_DUMMY_SPACE_PERMISSIONS} ? ${privilege>/DWC_DATAINTEGRATION/execute} === true : ${privPersistedView>/execute} === true }"

              />
          </VBox>
          <VBox>
            <MessageStrip
            id="repositoryErrorMsg"
            text="{i18n>repositoryErrorMsg}"
            type="Warning"
            showIcon="true"
            class="sapUiTinyMarginBottom"
            visible="{= !${/isFullViewList}}"
          />
          </VBox>
          <OverflowToolbar height="40px">
            <SegmentedButton
            selectedKey="scheduled"
            selectionChange="onViewChange"
            id="ViewsBtn"
          >
            <items>
              <SegmentedButtonItem
                tooltip="{/allViewsText}"
                text="{/allViewsText}"
                key="allviews"
                id="allViewsbtn"
              />
              <SegmentedButtonItem
                tooltip="{/scheduledText}"
                text="{/scheduledText}"
                key="scheduled"
                id="scheduledViewBtn"
              />
              <SegmentedButtonItem
                tooltip="{/persistedText}"
                text="{/persistedText}"
                key="persisted"
                id="persistedViewBtn"
              />
            </items>
          </SegmentedButton>
            <ToolbarSpacer></ToolbarSpacer>
            <SearchField
              id="searchTablesInput"
              placeholder="{i18n>txtSearch}"
              liveChange="onSearchField"
              width="15rem"
              class="sapUiTinyMarginBottom"
            />
            <MenuButton
              id="viewPersistencyActionMenuBtn"
              text="{i18n>ViewPersistenceMenuNew}"
              type="Transparent"
              visible="{= ${featureflags>/DWC_DUMMY_SPACE_PERMISSIONS} ? ${privilege>/DWC_DATAINTEGRATION/update} === true : ${privPersistedView>/update} === true}"
              enabled="{= ${persistencyActionModel>/isCustomerDPTenantAndSAPSpace} === false &amp;&amp; (${persistencyActionModel>/menuButtonsEnabled} === true)}"
              class="sapUiTinyMarginBottom"
              cd:actionId="monitoring/editor/dataReplication"
            >
              <Menu>
                <MenuItem
                  id="loadNewSnapShotMenuItem"
                  text="{i18n>loadNewSnapShotLabelNew}"
                  press="onLoadNewPersistence"
                  enabled="{= ${persistencyActionModel>/loadNewSnapShot} === true &amp;&amp; ${diPageModel>/isSpaceLocked} === false}"
                  visible="{= ${featureflags>/DWC_DUMMY_SPACE_PERMISSIONS} ? ${privilege>/DWC_DATAINTEGRATION/update} === true : ${privPersistedView>/update} === true}"
                />
                <MenuItem
                  id="removePersistedDataMenuItem"
                  text="{i18n>removePersistedDataLabel}"
                  press="onStopPersistence"
                  enabled="{= ${persistencyActionModel>/removePersistencyData} === true}"
                  visible="{= ${featureflags>/DWC_DUMMY_SPACE_PERMISSIONS} ? ${privilege>/DWC_DATAINTEGRATION/update} === true : ${privPersistedView>/update} === true}"
                />
                <MenuItem
                  id="startViewAnalyzerOverview"
                  text="{i18n>startAnalyzer}"
                  visible="{= ${privilege>/DWC_DATABUILDER/read} === true}"
                  enabled="{= ${persistencyActionModel>/enableViewAnalyzer} === true &amp;&amp; ${diPageModel>/isSpaceLocked} === false}"
                  press="startViewAnalyzer"
                />
              </Menu>
            </MenuButton>
            <MenuButton
              class="sapUiTinyMarginBottom"
              text="{i18n>scheduleText}"
              type="Transparent"
              visible="{= ${featureflags>/DWC_DUMMY_SPACE_PERMISSIONS} ? ${privilege>/DWC_DATAINTEGRATION/execute} === true : ${privPersistedView>/execute} === true }"
              enabled="{= ${persistencyActionModel>/isCustomerDPTenantAndSAPSpace} === false &amp;&amp; (${persistencyActionModel>/menuButtonsEnabled})}"
              id="scheduleMenu"
              cd:actionId="monitoring/editor/schedule"
            >
              <Menu>
                <MenuItem
                  id="createScheduleId"
                  text="{i18n>createScheduleLabel}"
                  press="onCreateSchedule"
                  enabled="{= ${persistencyActionModel>/newSchedule} === true}"
                />
                <MenuItem
                  id="editScheduleId"
                  text="{i18n>changeScheduleLabel}"
                  press="onChangeSchedule"
                  enabled="{= ${persistencyActionModel>/editSchedule} === true}"
                />
                <MenuItem
                  id="deleteScheduleId"
                  text="{i18n>deleteScheduleLabel}"
                  press="onDeleteSchedule"
                  enabled="{= ${persistencyActionModel>/deleteMassSchedules} === true}"
                />
                <MenuItem
                  text="{i18n>assignScheduleLabel}"
                  press="onMassChangeOwner"
                  enabled="{= ${persistencyActionModel>/assignSchedule} === true}"
                />
                <MenuItem
                  text="{i18n>pauseScheduleLabel}"
                  press="onMassSchedulesPause"
                  enabled="{= ${persistencyActionModel>/pauseSchedule} === true}"
                />
                <MenuItem
                  text="{i18n>resumeScheduleLabel}"
                  press="onMassSchedulesResume"
                  enabled="{= ${persistencyActionModel>/resumeSchedule} === true}"
                />
              </Menu>
            </MenuButton>
            <ToolbarSeparator class="sapUiTinyMarginBottom"/>
            <OverflowToolbarButton
              id="refeshTableButton"
              type="Transparent"
              text="{i18n>TEXT_REFRESH}"
              icon="sap-icon://refresh"
              tooltip="{i18n>txtRefresh}"
              press="onRefreshTable"
              enabled="{= ${persistencyActionModel>/isCustomerDPTenantAndSAPSpace} === false}"
              class="sapUiTinyMarginBottom"
              cd:actionId="monitoring/editor/refresh"
            />
            <OverflowToolbarButton
              id="personalizeTableButton"
              text="{i18n>text_selectColumns}"
              tooltip="{i18n>selectColumnsBtn}"
              type="Transparent"
              icon="sap-icon://action-settings"
              class="sapUiTinyMarginEnd, sapUiTinyMarginBottom"
              press="onPersoButtonPress"
              cd:actionId="monitoring/editor/refresh"
            />
          </OverflowToolbar>
        </t:extension>
        <t:columns>
          <t:Column
            sortProperty="displayName"
            filterProperty="displayName"
            id="viewNameColumn"
          >
            <Label
              text="{= ${featureflags>/DWCO_DI_MONITOR_UI_IMPROVEMENTS} ? ${i18n>NAME_LABELNew} :  ${i18n>NAME_LABEL}}"
              tooltip="{i18n>txtViewTooltip}"
            />
            <t:template>
              <Text
                text="{displayName}"
                tooltip="{displayName}"
                wrapping="false"
              />
            </t:template>
          </t:Column>
          <t:Column
            id="persistedViewStatusColumnNew"
            sortProperty="sortedStatus"
            filterProperty="formattedStatus"
          >

            <Label
              text="{i18n>STATUS}"
              tooltip="{i18n>txtViewStatusTooltip}"
            />
            <t:template>
              <HBox
                alignItems="Center"
                justifyContent="SpaceBetween"
              >
                <HBox justifyContent="Start">
                  <ObjectStatus
                    text="{formattedStatus}"
                    tooltip="{formattedStatusTooltip}"
                    state="{
                parts:['replicationState', 'taskState'],
                formatter: '.formatter._getSemanticColorValue'
              }"
                  />
                </HBox>

              </HBox>
            </t:template>
          </t:Column>
          <t:Column
            sortProperty="dataPersistency"
            filterProperty="dataPersistency"
            id="dataPersistencyColumn"
          >
            <Label
              text="{i18n>DATA_PERSISTENCY}"
              tooltip="{i18n>txtViewDataAccessTooltip}"
            />
            <t:template>
              <HBox>
                <HBox>
                  <Text
                    text="{formattedDataPersistency}"
                    tooltip="{formattedDataPersistency}"
                    visible="{= ${partiallyPersisted} !== true}"
                  ></Text>
                </HBox>
                <HBox>
                  <ObjectAttribute
                    text="{i18n>partiallyPersisted}"
                    tooltip="{i18n>partiallyPersisted}"
                    visible="{= ${partiallyPersisted} === true}"
                    active="{= ${partiallyPersisted} === true}"
                    press=".openPartiallyPersistedLink($event, ${inputParametersDetails}, ${latestUpdate})"
                  />
                </HBox>
              </HBox>
            </t:template>
          </t:Column>
          <t:Column
            id="refreshFrequencyColumn"
            filterProperty="formattedRefreshFrequency"
            sortProperty="formattedRefreshFrequency"
            sorted="true"
            sortOrder="Descending"
            visible="{= ${featureflags>/DWCO_DI_MONITOR_UI_IMPROVEMENTS} === false}"
          >

            <Label
              text="{i18n>refreshFrequencyNew}"
              tooltip="{i18n>txtViewRefreshFreqTooltip}"
            />
            <t:template>
              <HBox
                alignItems="Center"
                justifyContent="SpaceBetween"
              >
                <HBox
                  justifyContent="Start"
                  visible="{= ((${scheduled} !== undefined &amp;&amp; ${scheduled} === false) || ${scheduled} === undefined) || ((${scheduled} !== undefined) &amp;&amp;(${scheduled} === true &amp;&amp; ${isPaused} === true))}"
                >
                  <Text
                    text="{formattedRefreshFrequency}"
                    tooltip="{formattedRefreshFrequency}"
                    wrapping="false"
                  />
                </HBox>
                <HBox
                  justifyContent="Start"
                  visible="{= (${scheduled} !== undefined &amp;&amp; ${scheduled} === true &amp;&amp; ${isPaused} === false)}"
                >
                  <Link
                    text="{formattedRefreshFrequency}"
                    press=".openSchedule($event, ${viewName})"
                    class="schedulelink"
                  />
                </HBox>
              </HBox>
            </t:template>
          </t:Column>
          <t:Column
            filterProperty="formattedlatestUpdate"
            sortProperty="latestUpdate"
            id="latestPersistedColumn"
            hAlign="End"
          >
            <Label
              text="{i18n>LAST_UPDATED}"
              tooltip="{i18n>txtViewLatestUpdateTooltip}"
            />
            <t:template>
              <Text
                text="{formattedlatestUpdate}"
                tooltip="{formattedlatestUpdate}"
                wrapping="false"
              ></Text>
            </t:template>
          </t:Column>
          <t:Column
            sortProperty="formattedNextSchedule"
            id="nextRunColumn"
            filterProperty="formattedNextSchedule"
            hAlign="End"
            visible="{= ${featureflags>/DWCO_DI_MONITOR_UI_IMPROVEMENTS} === false}"
          >
            <Label
              text="{i18n>txtNextSchedule}"
              tooltip="{i18n>txtViewNextRunTooltip}"
            />
            <t:template>
              <Text
                text="{formattedNextSchedule}"
                tooltip="{formattedNextSchedule}"
                wrapping="false"
              />
            </t:template>
          </t:Column>
          <t:Column
            sortProperty="numberOfRecords"
            id="numOfRecordsCol"
            filterProperty="formattedNumberOfRecords"
            hAlign="End"
          >
            <Label
              text="{i18n>txtNumOfRecords}"
              tooltip="{i18n>txtViewNumOfRecordsTooltip}"
            />
            <t:template>
              <Text
                text="{formattedNumberOfRecords}"
                tooltip="{formattedNumberOfRecords}"
                wrapping="false"
              />
            </t:template>
          </t:Column>
          <t:Column
            sortProperty="inMemorySizeReplicaTableMB"
            id="memorySizePersistenceColumn"
            filterProperty="formattedinMemorySizeReplicaTableMB"
            hAlign="End"
            visible="false"
          >
            <Label
              text="{= ${featureflags>/DWCO_DI_MONITOR_UI_IMPROVEMENTS} ? ${i18n>IN_MEMORY_SIZE_NEWW} : ${i18n>IN_MEMORY_SIZE}}"
              tooltip="{i18n>txtViewInMemorySizeTooltip}"
            />
            <t:template>
              <Text text="{formattedinMemorySizeReplicaTableMB}"></Text>
            </t:template>
          </t:Column>
          <t:Column
            id="diskSizePersistenceColumn"
            sortProperty="diskSizeReplicaTableInMB"
            filterProperty="formatteddiskSizeReplicaTableInMB"
            hAlign="End"
            visible="false"
          >
            <Label
              text="{= ${featureflags>/DWCO_DI_MONITOR_UI_IMPROVEMENTS} ? ${i18n>DISK_SIZE_NEWW} : ${i18n>DISK_SIZE}}"
              tooltip="{i18n>txtViewDiskSizeTooltip}"
            />
            <t:template>
              <Text
                text="{formatteddiskSizeReplicaTableInMB}"
                wrapping="false"
              />
            </t:template>
          </t:Column>
          <t:Column
            id="refreshFrequencyColumnNew"
            filterProperty="formattedRefreshFrequency"
            sortProperty="formattedRefreshFrequency"
            sorted="true"
            sortOrder="Descending"
            visible="{featureflags>/DWCO_DI_MONITOR_UI_IMPROVEMENTS}"
          >

            <Label
              text="{i18n>refreshFrequencyNewNew}"
              tooltip="{i18n>txtViewRefreshFreqTooltip}"
            />
            <t:template>
              <HBox
                alignItems="Center"
                justifyContent="SpaceBetween"
              >
                <HBox
                  justifyContent="Start"
                  visible="{= ((${scheduled} !== undefined &amp;&amp; ${scheduled} === false) || ${scheduled} === undefined) || ((${scheduled} !== undefined) &amp;&amp;(${scheduled} === true &amp;&amp; ${isPaused} === true))}"
                >
                  <Text
                    text="{formattedRefreshFrequency}"
                    tooltip="{formattedRefreshFrequency}"
                    wrapping="false"
                  />
                </HBox>
                <HBox
                  justifyContent="Start"
                  visible="{= (${scheduled} !== undefined &amp;&amp; ${scheduled} === true &amp;&amp; ${isPaused} === false)}"
                >
                  <Link
                    text="{parts:['viewName', 'formattedRefreshFrequency', 'isPaused', 'persistencyActionModel>/scheduleList'], formatter:'.getScheduledText'}"
                    press=".openSchedule($event, ${viewName})"
                    class="schedulelink"
                  />
                </HBox>
              </HBox>
            </t:template>
          </t:Column>
          <t:Column
            sortProperty="formattedNextSchedule"
            id="nextRunColumnNew"
            filterProperty="formattedNextSchedule"
            hAlign="End"
            visible="{featureflags>/DWCO_DI_MONITOR_UI_IMPROVEMENTS}"
          >
            <Label
              text="{i18n>txtNextScheduleNew}"
              tooltip="{i18n>txtViewNextRunTooltip}"
            />
            <t:template>
              <Text
                text="{formattedNextSchedule}"
                tooltip="{formattedNextSchedule}"
                wrapping="false"
              />
            </t:template>
          </t:Column>
          <t:Column width="5rem">
          </t:Column>
          <t:Column
            sortProperty="scheduleOwner"
            filterProperty="scheduleOwner"
            id="scheduleOwnerId"
            visible="false"
          >
            <Label
              text="{i18n>txtScheduleOwner}"
              tooltip="{i18n>txtScheduleOwnerToolTip}"
            />
            <t:template>
              <Text
                text="{scheduleOwner}"
                wrapping="false"
              />
            </t:template>
          </t:Column>
        </t:columns>
        <t:rowActionTemplate>
          <t:RowAction>
            <t:RowActionItem
              press="LoadViewPersistencyLogs"
              type="Navigation"
              icon="sap-icon://feeder-arrow"
              tooltip="{i18n>txtViewPersistencyLogsNew}"
            />
          </t:RowAction>
        </t:rowActionTemplate>
      </t:Table>
    </f:content>
  </f:DynamicPage>
</mvc:View>
