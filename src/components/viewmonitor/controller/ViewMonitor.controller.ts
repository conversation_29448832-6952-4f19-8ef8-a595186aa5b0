/**
 * Copyright 2020 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { State } from "@sap/dwc-circuit-breaker";
import { isUndefined } from "lodash";
import { isDiMonitorImprovementsEnabled } from "../../abstractbuilder/commonservices/FeatureFlagCheck";
import {
  BaseController,
  BaseControllerClass,
  smartExtend,
} from "../../basecomponent/controller/BaseController.controller";
import { DataIntegrationComponentClass } from "../../dataintegration/Component";
import { TablePersonalizationClass } from "../../dataintegration/utility/TablePersonalization.controller";
import { checkForCustomerDPTenantAndSAPSpace } from "../../reuse/utility/DataPlaneConstraintsUtil";
import { MessageHandler } from "../../reuse/utility/MessageHandler";
import { UI5Require } from "../../reuse/utility/UIHelper";
import { isCircuitBreakerYellowStateEnabled } from "../../shell/featureState/CircuitBreakerYellowStateFeatureState";
import { ShellContainer } from "../../shell/utility/Container";
import { EventType } from "../../shell/utility/ShellUsageCollectionService";
import { User } from "../../shell/utility/User";
import { setHelpScreenId } from "../../shell/utility/WebAssistantHelper";
import { Activity, ApplicationId } from "../../tasklog/utility/Constants";
import {
  ITTaskScheduleController,
  ITaskScheduleMassRequest,
  ITaskScheduleRequest,
  MassOps,
} from "../../taskscheduler/controller/TaskSchedule.controller";
import { getTaskScheduer, openSchedulePopover, recordAction } from "../../taskscheduler/utility/ScheduleUtil";
import { PreferenceChanges } from "../../userSettings/utility/Constants";
import { StartAnalyzerDialog } from "../../viewanalyzer/controller/StartAnalyzerDialog.controller";
import { ViewMonitorFormatter } from "../utility/ViewMonitorFormatter";
import {
  SERVICENAMES,
  fetchViewPersistenceService,
  handleViewPersistenceResponse,
  openPartiallyPersistedInfo,
  replicationState,
} from "../viewpersistencyservice/ServiceConsumption";

export class ViewMonitorClass extends BaseControllerClass {
  private ViewFilter = {
    SCHEDULED: "scheduled",
    PERSISTED: "persisted",
    ALLVIEWLBL: "allViewsText",
    SCHDULEDLBL: "scheduledText",
    PERSISTEDLBL: "persistedText",
  };

  private view: sap.ui.core.mvc.View;
  private sTableHeader: string;
  private spaceId: string;
  private previousSpaceId: string;
  private searchValue: string = "";
  private oGlobalFilter: sap.ui.model.Filter;
  private persistencyActionModel: sap.ui.model.json.JSONModel;
  public partiallyPersistedModel: sap.ui.model.json.JSONModel;
  private selectedView: string;
  private formatter = ViewMonitorFormatter;
  tablePersoController: sap.ui.table.TablePersoController;
  private oTable: sap.ui.table.Table;
  private oSortOrder: sap.ui.table.SortOrder;
  dataIntegrationComponent: DataIntegrationComponentClass;
  router: sap.m.routing.Router;
  private filtersList: {};
  private sortObject: any;
  selectedRowIndex: any;
  user: User;
  isReusableTaskScheduleFFEnabled: boolean;
  isAdoptionOfTaskSchedulerEnabled: boolean;
  displayName: string;
  selectedRowsSnapshot: any[];
  private responseList = [];
  private currentViewList = "scheduledText";
  private currentTab = null;
  private responseListAllViews: any;
  private responseListViewWithRuns: any;

  public onInit(): void {
    require("../css/style.css");
    super.onInit();
    this.view = this.getView();
    const i18nModel = new sap.ui.model.resource.ResourceModel({
      bundleName: require("../i18n/i18n.properties"),
    });

    const i18nModelTask = new sap.ui.model.resource.ResourceModel({
      bundleName: require("../../taskscheduler/i18n/i18n.properties"),
    });
    this.view.setModel(i18nModel, "i18n");
    this.view.setModel(i18nModelTask, "i18n_task");
    this.persistencyActionModel = new sap.ui.model.json.JSONModel();
    this.partiallyPersistedModel = new sap.ui.model.json.JSONModel();
    this.view.setModel(this.persistencyActionModel, "persistencyActionModel");
    this.view.setModel(this.partiallyPersistedModel, "partiallyPersistedModel");
    (this.view.getModel("persistencyActionModel") as sap.ui.model.json.JSONModel).setProperty(
      "/loadNewSnapShot",
      false
    );
    (this.view.getModel("persistencyActionModel") as sap.ui.model.json.JSONModel).setProperty(
      "/removePersistencyData",
      false
    );
    (this.view.getModel("persistencyActionModel") as sap.ui.model.json.JSONModel).setProperty(
      "/deletePersistency",
      false
    );
    (this.view.getModel("persistencyActionModel") as sap.ui.model.json.JSONModel).setProperty("/newSchedule", false);
    (this.view.getModel("persistencyActionModel") as sap.ui.model.json.JSONModel).setProperty("/editSchedule", false);
    (this.view.getModel("persistencyActionModel") as sap.ui.model.json.JSONModel).setProperty("/deleteSchedule", false);

    // TODO: Will discuss and then put right authentication key, As of now i kept the same Authentication key which was mentioned in the endpoint of persisted views
    const isSDPEnabled = sap.ui.getCore().getModel("featureflags").getProperty("/DWC_DUMMY_SPACE_PERMISSIONS");
    this.isReusableTaskScheduleFFEnabled = sap.ui
      .getCore()
      .getModel("featureflags")
      .getProperty("/DWCO_REUSABLE_TASK_SCHEDULING_UI");
    this.isAdoptionOfTaskSchedulerEnabled = sap.ui
      .getCore()
      .getModel("featureflags")
      .getProperty("/DWCO_REUSABLE_TASK_SCHEDULER_ADOPTION");

    // TODO: Remove privPersistedView, privSpaceFile with FF DWC_DUMMY_SPACE_PERMISSIONS removal user story
    this.view.setModel(new sap.ui.model.json.JSONModel(), "privPersistedView");
    this.view.setModel(new sap.ui.model.json.JSONModel(), "privSpaceFile");

    this.router = sap.ui.core.UIComponent.getRouterFor(this) as sap.m.routing.Router;
    this.user = User.getInstance();

    // default model which contains data regarding persisted view infos
    this.view.setModel(new sap.ui.model.json.JSONModel({}));

    this.dataIntegrationComponent = this.getOwnerComponent() as DataIntegrationComponentClass;
    sap.ui
      .getCore()
      .getEventBus()
      .subscribe("userPreferences", "change", (_, __, preferenceChanges: PreferenceChanges) => {
        if (!preferenceChanges.changes.DWC_OBJECT_NAME_DISPLAY) {
          return;
        }
        const objectNameDisplay = this.displayName;
        if (objectNameDisplay !== preferenceChanges.changes.DWC_OBJECT_NAME_DISPLAY.newValue) {
          this.displayName = preferenceChanges.changes.DWC_OBJECT_NAME_DISPLAY.newValue;
          this.onRefreshTable();
        }
      });
    this.router.attachRouteMatched(null, async (event: sap.ui.base.Event) => {
      this.clearCache();
      this.selectDefaultTab();
      if (event.getParameter("name") === "viewMonitor") {
        setHelpScreenId("viewMonitor");
        this.view
          .getModel()
          .setProperty("/" + this.ViewFilter.ALLVIEWLBL, this.getText(this.ViewFilter.ALLVIEWLBL, [`0`]));
        this.view
          .getModel()
          .setProperty("/" + this.ViewFilter.SCHDULEDLBL, this.getText(this.ViewFilter.SCHDULEDLBL, [`0`]));
        this.view
          .getModel()
          .setProperty("/" + this.ViewFilter.PERSISTEDLBL, this.getText(this.ViewFilter.PERSISTEDLBL, [`0`]));
        this.clearSearchField();
        if (!isSDPEnabled) {
          (this.view.getModel("privPersistedView") as sap.ui.model.json.JSONModel).setProperty(
            "/read",
            ShellContainer.get().getPrivilegeService().getPrivilegesByType("DWC_DATAINTEGRATION").read
          );
          (this.view.getModel("privPersistedView") as sap.ui.model.json.JSONModel).setProperty(
            "/update",
            ShellContainer.get().getPrivilegeService().getPrivilegesByType("DWC_DATAINTEGRATION").update
          );
          (this.view.getModel("privPersistedView") as sap.ui.model.json.JSONModel).setProperty(
            "/execute",
            ShellContainer.get().getPrivilegeService().getPrivilegesByType("DWC_DATAINTEGRATION").execute
          );

          /** Privileges for SpaceFile */
          const spacePriv = ShellContainer.get().getPrivilegeService().getPrivilegesByType("DWC_SPACEFILE");
          (this.view.getModel("privSpaceFile") as sap.ui.model.json.JSONModel).setProperty("/create", spacePriv.create);
          (this.view.getModel("privSpaceFile") as sap.ui.model.json.JSONModel).setProperty("/update", spacePriv.update);
          (this.view.getModel("privSpaceFile") as sap.ui.model.json.JSONModel).setProperty("/delete", spacePriv.delete);

          const hasDatabuilderReadPrivilege = ShellContainer.get()
            .getPrivilegeService()
            .hasPrivilege("DWC_DATABUILDER", "read");
          (this.view.getModel("privPersistedView") as sap.ui.model.json.JSONModel).setProperty(
            "/hasDatabuilderReadPrivilege",
            hasDatabuilderReadPrivilege
          );
        }

        this.view.setBusyIndicatorDelay(0);
        this.view.setBusy(true);
        this.previousSpaceId = this.spaceId;
        this.spaceId = event.getParameter("arguments").spaceId;
        this.displayName = this.user.getObjectNameDisplay();
        if (isDiMonitorImprovementsEnabled()) {
          if (this.displayName === "businessName") {
            this.view.byId("viewNameColumn")["setLabel"](this.getText("NAME_LABELNew"));
          } else {
            this.view.byId("viewNameColumn")["setLabel"](this.getText("TECHINCAL_NAMENew"));
          }
        } else {
          if (this.displayName === "businessName") {
            this.view.byId("viewNameColumn")["setLabel"](this.getText("NAME_LABEL"));
          } else {
            this.view.byId("viewNameColumn")["setLabel"](this.getText("TECHINCAL_NAME"));
          }
        }

        if (this.spaceId) {
          const isCustomerDPTenantAndSAPSpace = await checkForCustomerDPTenantAndSAPSpace(this.spaceId);
          (this.view.getModel("persistencyActionModel") as sap.ui.model.json.JSONModel).setProperty(
            "/isCustomerDPTenantAndSAPSpace",
            isCustomerDPTenantAndSAPSpace
          );
          this.onRefreshTable();
        }

        if (this.previousSpaceId !== this.spaceId && this.previousSpaceId !== undefined) {
          this.clearFilter(this.view.byId("viewMonitorTable"));
        }
      }
    });

    const oTable = this.view.byId("viewMonitorTable") as sap.ui.table.Table;
    if (isDiMonitorImprovementsEnabled()) {
      oTable.getColumns()[3].destroy();
      oTable.getColumns()[4].destroy();
    }
    if (!isDiMonitorImprovementsEnabled()) {
      this.removeColumns(oTable, ["refreshFrequencyColumnNew", "nextRunColumnNew"]);
    }
    if (!sap.ui.getCore().getModel("featureflags").getProperty("/DWCO_VIEW_PERSISTENCY_NO_REPOSITORY")) {
      this.removeColumns(oTable, [
        "lastRunStartColumn",
        "lastRunEndColumn",
        "lastRunDuration",
        "lastRunStatus",
        "lastRunActivity",
      ]);
    }

    //oTable.getColumns()[9].destroy();

    this.registerForColumnSettings();
    // create personalization controller
    UI5Require<typeof sap.ui.table.TablePersoController>("sap/ui/table/TablePersoController").then((TPC) => {
      this.tablePersoController = new TPC("tablePersonalizationDialogId", {
        table: this.getTable(),
        persoService: new TablePersonalizationClass("TPC"),
      });
    });

    // create Statement Editor
    UI5Require<typeof sap.ui.codeeditor.CodeEditor>("sap/ui/codeeditor/CodeEditor").then((Ce) => {
      this["codeEditor"] = new Ce("codeEditorIdVM", {
        type: "sql",
        maxLines: 15,
        height: "auto",
        lineNumbers: false,
        editable: false,
      } as any); // Sorry, the reuse sap.ui.codeeditor.CodeEditor doesn't have the correct constructor yet....
    });
  }

  removeColumns(oTable, columnsToRemove: string[]) {
    columnsToRemove.forEach((columnId) => {
      const column = oTable.getColumns().find((col) => col.getId().endsWith(columnId));
      if (column) {
        column.destroy();
      }
    });
  }

  clearCache() {
    this.responseListAllViews = null;
    this.responseListViewWithRuns = null;
  }

  selectDefaultTab() {
    const segmentedButton = this.view.byId("ViewDisplaySegmentedButton") as sap.m.SegmentedButton;
    if (segmentedButton) {
      this.currentTab = "viewsWithRuns";
      segmentedButton.setSelectedKey(this.currentTab);
    }
  }

  protected registerForColumnSettings() {
    sap.ui.getCore().getEventBus().subscribe("personalizationsDone", "change", this.onAfterTablePersonalization, this);
  }

  public onAfterTablePersonalization() {
    const selectedRows = this.getSelectedViews();
    if (selectedRows.length > 0) {
      this.clearSelection();
    }
    (this.view.getModel("persistencyActionModel") as sap.ui.model.json.JSONModel).setProperty(
      "/deletePersistency",
      false
    );
  }

  public onAfterRendering(): void {
    this.dataIntegrationComponent.registerHanaStateChangeHandler(
      () => {
        this.onRefreshTable();
      },
      () => {
        this.onRefreshTable();
      }
    );
  }

  private clearSearchField() {
    const searchField = this.view.byId("searchTablesInput") as sap.m.SearchField;
    searchField["clear"]();
    this.searchValue = "";
  }

  private clearFilter(oTable) {
    const aColumns = oTable.getColumns();
    for (var i = 0; i < aColumns.length; i++) {
      oTable.filter(aColumns[i], null);
    }
  }
  captureSelectedRowsSnapshot(oEvent: sap.ui.base.Event) {
    const oTable = this.byId("viewMonitorTable") as sap.ui.table.Table;
    const isSelectAll = oEvent.getParameter("selectAll");
    const rowIndex = oEvent.getParameter("rowIndex");
    const rowIndices = oEvent.getParameter("rowIndices");
    const userInteraction = oEvent.getParameter("userInteraction");
    if (isSelectAll) {
      for (const i of rowIndices) {
        const path = oTable.getContextByIndex(i)?.getPath();
        const selectedView = this.view.getModel().getProperty(path);
        this.selectedRowsSnapshot.push(selectedView);
      }
    } else if (rowIndex === -1 && rowIndices.length > 0 && userInteraction) {
      // deselect all clicked
      for (const i of rowIndices) {
        const path = oTable.getContextByIndex(i)?.getPath();
        const selectedView = this.view.getModel().getProperty(path);
        const existingViewIndex = this.selectedRowsSnapshot.findIndex(
          (view) => view.viewName === selectedView.viewName
        );
        if (existingViewIndex > -1) {
          this.selectedRowsSnapshot.splice(existingViewIndex, 1);
        }
      }
    } else if (userInteraction) {
      const selectedPath = oEvent.getParameter("rowContext")?.getPath();
      if (!!selectedPath) {
        const selectedView = this.view.getModel().getProperty(selectedPath);
        const existingViewIndex = this.selectedRowsSnapshot.findIndex(
          (table) => table.viewName === selectedView.viewName
        );
        if (existingViewIndex > -1) {
          this.selectedRowsSnapshot.splice(existingViewIndex, 1);
        } else {
          this.selectedRowsSnapshot.push(selectedView);
        }
      }
    }
  }

  restoreSelectedRows() {
    const oTable: any = this.byId("viewMonitorTable") as sap.ui.table.Table;
    const oBinding: any = oTable.getBinding("rows");
    const filteredIndices = oBinding.aIndices;
    const viewsList = this.getView().getModel().getData();
    const selectedViews = this.selectedRowsSnapshot.map((view) => view.viewName);
    filteredIndices.forEach((filteredIndex, index) => {
      const viewName = viewsList.tables[filteredIndex]?.viewName;
      if (selectedViews.includes(viewName)) {
        oTable.addSelectionInterval(index, index);
      }
    });
  }

  public async onRefreshTable(): Promise<void> {
    const circuitBreakerModel = sap.ui.getCore().getModel("circuitbreaker");
    const hanaState = circuitBreakerModel?.getProperty("/DataHANA");
    const isHanaDown: boolean =
      hanaState === State.Red || (hanaState === State.Yellow && !isCircuitBreakerYellowStateEnabled());
    const dataHANAProvisioningState = circuitBreakerModel?.getProperty("/DataHANAProvisioningState");
    const isHanaUpgradeInProgress: boolean =
      dataHANAProvisioningState === State.Red ||
      (dataHANAProvisioningState === State.Yellow && !isCircuitBreakerYellowStateEnabled());

    if (sap.ui.getCore().getModel("featureflags").getProperty("/DWCO_VIEW_PERSISTENCY_NO_REPOSITORY")) {
      const segmentedButton = this.view.byId("ViewDisplaySegmentedButton") as sap.m.SegmentedButton;
      this.currentTab = segmentedButton?.getSelectedKey() ? segmentedButton.getSelectedKey() : "viewsWithRuns"; // reads the selected tab
    }
    if (isHanaDown || isHanaUpgradeInProgress) {
      const sTableHeader = this.getText("txtViewCount", ["0"]);
      this.view.getModel().setProperty("/tableHeader", sTableHeader);
      this.getView().getModel().setProperty("/totalDiskSize", "--");
      this.getView().getModel().setProperty("/totalMemorySize", "--");
      this.view.getModel().setProperty("/", []);
      this.view
        .getModel()
        .setProperty("/" + this.ViewFilter.ALLVIEWLBL, this.getText(this.ViewFilter.ALLVIEWLBL, [`0`]));
      this.view
        .getModel()
        .setProperty("/" + this.ViewFilter.SCHDULEDLBL, this.getText(this.ViewFilter.SCHDULEDLBL, [`0`]));
      this.view
        .getModel()
        .setProperty("/" + this.ViewFilter.PERSISTEDLBL, this.getText(this.ViewFilter.PERSISTEDLBL, [`0`]));
      this.view.setBusy(false);
    } else {
      let scheduleDialog;
      if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
        await this.initScheduleDialog();
        scheduleDialog = this["newScheduleDialog"];
      } else {
        scheduleDialog = (
          this.getView().byId("viewScheduleDialog") as sap.ui.core.mvc.View
        ).getController() as ITTaskScheduleController;
      }
      this.view.setBusyIndicatorDelay(0);
      this.view.setBusy(true);
      //this.clearSearchField();
      // read data from backend into default model this.view.getModel()
      await scheduleDialog?.refreshTaskScheduleList(this.spaceId, ApplicationId.VIEWS);
      if (sap.ui.getCore().getModel("featureflags").getProperty("/DWCO_VIEW_PERSISTENCY_NO_REPOSITORY")) {
        await this.loadData(this.currentTab);
      } else {
        await this.loadData();
      }
      this.clearSelection();
    }

    if (isDiMonitorImprovementsEnabled()) {
      if (this.displayName === "businessName") {
        this.view.byId("viewNameColumn")["setLabel"](this.getText("NAME_LABELNew"));
      } else {
        this.view.byId("viewNameColumn")["setLabel"](this.getText("TECHINCAL_NAMENew"));
      }
    } else {
      if (this.displayName === "businessName") {
        this.view.byId("viewNameColumn")["setLabel"](this.getText("NAME_LABEL"));
      } else {
        this.view.byId("viewNameColumn")["setLabel"](this.getText("TECHINCAL_NAME"));
      }
    }

    // set column to technical Name if FF is enabled & thresholdForBusinessNamesExceeded is true
    if (
      sap.ui.getCore().getModel("featureflags").getProperty("/DWCO_VIEW_PERSISTENCY_NO_REPOSITORY") &&
      this.view.getModel().getProperty("/thresholdForBusinessNamesExceeded")
    ) {
      this.view.byId("viewNameColumn")["setLabel"](this.getText("TECHINCAL_NAMENew"));
    }
  }

  public clearSelection() {
    const oTable = this.byId("viewMonitorTable") as sap.ui.table.Table;
    this.selectedRowsSnapshot = [];

    (this.view.getModel("persistencyActionModel") as sap.ui.model.json.JSONModel).setProperty(
      "/menuButtonsEnabled",
      false
    );

    oTable?.clearSelection();
  }

  public async onLoadNewPersistence(): Promise<void> {
    let selectedView;
    selectedView = this.getSelectedViews()[0];
    const viewName = selectedView.viewName;
    const sView = viewName;
    await this.startPersistence(sView);
  }

  async initScheduleDialog() {
    if (!this["newScheduleDialog"]) {
      this["newScheduleDialog"] = await getTaskScheduer("viewTaskScheduler");
    }
  }

  async onCreateSchedule(): Promise<void> {
    const data: ITaskScheduleRequest = {
      objectId: this.selectedView,
      applicationId: ApplicationId.VIEWS,
      activity: Activity.PERSIST,
      description: "View Persistency Scheduling",
      activationStatus: "ENABLED",
    };
    let scheduleDialog;
    if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
      await this.initScheduleDialog();
      scheduleDialog = this["newScheduleDialog"];
      recordAction(`createTaskSchedule: ${data.applicationId}`, "taskSchedule", "onCreate");
    } else {
      scheduleDialog = (
        this.getView().byId("viewScheduleDialog") as sap.ui.core.mvc.View
      ).getController() as ITTaskScheduleController;
    }
    const viewTable = this.getView().byId("viewMonitorTable") as sap.ui.core.mvc.View as any;
    const selectedIndex = viewTable.getSelectedIndex();
    const oRessourceBundle = (
      this.view.getModel("i18n_task") as sap.ui.model.resource.ResourceModel
    ).getResourceBundle();
    this.view.setBusy(true);
    scheduleDialog.createTaskSchedule(
      data,
      this.spaceId,
      ApplicationId.VIEWS,
      () => {
        if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
          recordAction(`saveTaskSchedule: ${data.applicationId}`, "taskSchedule", "save");
        }
        const msg = oRessourceBundle.getText("createScheduleSuccess");
        this.onRefreshTable().then(() => {
          sap.m.MessageToast.show(msg);
        });
      },
      (error) => {
        if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
          recordAction(`saveTaskSchedule: ${data.applicationId}`, "taskSchedule", "save");
          MessageHandler.exception({
            exception: error.error,
            message: error.message,
          });
        }
        this.view.setBusy(false);
      },
      () => {
        this.view.setBusy(false);
      }
    );
  }

  async onChangeSchedule(): Promise<void> {
    const data: ITaskScheduleRequest = {
      objectId: this.selectedView,
      applicationId: ApplicationId.VIEWS,
      activity: Activity.PERSIST,
      description: "View Persistency Scheduling",
      activationStatus: "ENABLED",
    };
    let scheduleDialog;
    if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
      await this.initScheduleDialog();
      scheduleDialog = this["newScheduleDialog"];
      recordAction(`editTaskSchedule: ${data.applicationId}`, "taskSchedule", "onEdit");
    } else {
      scheduleDialog = (
        this.getView().byId("viewScheduleDialog") as sap.ui.core.mvc.View
      ).getController() as ITTaskScheduleController;
    }
    const viewTable = this.getView().byId("viewMonitorTable") as sap.ui.core.mvc.View as any;
    const selectedIndex = viewTable.getSelectedIndex();
    const oRessourceBundle = (
      this.view.getModel("i18n_task") as sap.ui.model.resource.ResourceModel
    ).getResourceBundle();

    this.view.setBusy(true);
    scheduleDialog.changeTaskSchedule(
      data,
      this.spaceId,
      ApplicationId.VIEWS,
      () => {
        if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
          recordAction(`saveTaskSchedule: ${data.applicationId}`, "taskSchedule", "save");
        }
        const msg = oRessourceBundle.getText("updateScheduleSuccess");
        this.onRefreshTable().then(() => {
          sap.m.MessageToast.show(msg);
        });
      },
      (error) => {
        if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
          recordAction(`saveTaskSchedule: ${data.applicationId}`, "taskSchedule", "save");
          MessageHandler.exception({
            exception: error.error,
            message: error.message,
          });
        }
        this.view.setBusy(false);
      },
      () => {
        this.view.setBusy(false);
      }
    );
  }

  async onDeleteSchedule(): Promise<void> {
    this.onMassDeleteOps();
  }

  async onMassOperations(operation: MassOps) {
    let scheduleDialog;
    if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
      await this.initScheduleDialog();
      scheduleDialog = this["newScheduleDialog"];
    } else {
      scheduleDialog = (
        this.getView().byId("viewScheduleDialog") as sap.ui.core.mvc.View
      ).getController() as ITTaskScheduleController;
    }
    let errorTxt, successTxt;
    let operationMethod;
    const model = this.view.getModel("persistencyActionModel");
    switch (operation) {
      case MassOps.DELETE:
        operationMethod = (...args) => {
          const count = model.getProperty("/deleteCount");
          errorTxt = this.getText("errorMassRemoveScheduleTxt");
          successTxt = this.getText("massDeleteStarted", [count]);
          // eslint-disable-next-line prefer-spread
          scheduleDialog.deleteMultiSchedules.apply(scheduleDialog, args);
        };
        break;
      case MassOps.CHANGE_OWNER:
        operationMethod = (...args) => {
          const count = model.getProperty("/changeOwnerCount");
          errorTxt = this.getText("errorMassScheduleOwnerChangeTxt");
          successTxt = this.getText("massAssignStarted", [count]);
          // eslint-disable-next-line prefer-spread
          scheduleDialog.changeOwnerMultiSchedules.apply(scheduleDialog, args);
        };
        break;
      case MassOps.PAUSE:
        operationMethod = (...args) => {
          const count = model.getProperty("/pauseCount");
          errorTxt = this.getText("errorMassPauseScheduleTxt");
          successTxt = this.getText("massPauseStarted", [count]);
          // eslint-disable-next-line prefer-spread
          scheduleDialog.pauseMultiSchedules.apply(scheduleDialog, args);
        };
        break;
      case MassOps.RESUME:
        operationMethod = (...args) => {
          const count = model.getProperty("/resumeCount");
          errorTxt = this.getText("errorMassResumeScheduleTxt");
          successTxt = this.getText("massResumeStarted", [count]);
          // eslint-disable-next-line prefer-spread
          scheduleDialog.resumeMultiSchedules.apply(scheduleDialog, args);
        };
        break;
    }
    if (operationMethod) {
      const selectedViews = this.getSelectedViews();
      const selectedViewNames = selectedViews.map((table) => table.viewName);
      const data: ITaskScheduleMassRequest = {
        objectIdList: selectedViewNames,
      };
      this.view.setBusy(true);
      operationMethod(
        data,
        this.spaceId,
        ApplicationId.VIEWS,
        () => {
          sap.m.MessageToast.show(successTxt);
          this.onRefreshTable().then(() => {
            this.clearSelection();
            // sap.m.MessageToast.show(msg);
            this.view.setBusy(false);
          });
        },
        (error) => {
          MessageHandler.exception({
            exception: error,
            message: errorTxt,
          });
          this.view.setBusy(false);
        },
        () => {
          this.view.setBusy(false);
        }
      );
    }
  }

  onMassDeleteOps() {
    this.onMassOperations(MassOps.DELETE);
  }

  onMassChangeOwner() {
    this.onMassOperations(MassOps.CHANGE_OWNER);
  }

  onMassSchedulesPause() {
    this.onMassOperations(MassOps.PAUSE);
  }

  onMassSchedulesResume() {
    this.onMassOperations(MassOps.RESUME);
  }

  private async handleMassOpsButtonStatus() {
    const selectedTables = this.getSelectedViews();
    let scheduleDialog;
    if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
      await this.initScheduleDialog();
      scheduleDialog = this["newScheduleDialog"];
    } else {
      scheduleDialog = (
        this.getView().byId("viewScheduleDialog") as sap.ui.core.mvc.View
      ).getController() as ITTaskScheduleController;
    }
    const selectedObjects = selectedTables.map((view) => view.viewName);
    const massOpsButtonsStatus = await scheduleDialog.getMassOpsButtonEnablement(
      selectedObjects,
      this.spaceId,
      ApplicationId.VIEWS
    );
    const isCreateEnabled = selectedTables.length === 1 && massOpsButtonsStatus.delete === false;
    const isEditEnabled = selectedTables.length === 1 && massOpsButtonsStatus.delete;
    const model = this.view.getModel("persistencyActionModel");
    model.setProperty("/newSchedule", isCreateEnabled);
    model.setProperty("/editSchedule", isEditEnabled);
    model.setProperty("/deleteMassSchedules", massOpsButtonsStatus.delete);
    model.setProperty("/assignSchedule", massOpsButtonsStatus.changeOwner);
    model.setProperty("/pauseSchedule", massOpsButtonsStatus.pause);
    model.setProperty("/resumeSchedule", massOpsButtonsStatus.resume);
    model.setProperty("/deleteCount", massOpsButtonsStatus.deleteCount);
    model.setProperty("/changeOwnerCount", massOpsButtonsStatus.changeOwnerCount);
    model.setProperty("/pauseCount", massOpsButtonsStatus.pauseCount);
    model.setProperty("/resumeCount", massOpsButtonsStatus.resumeCount);
  }

  public async onRowSelectionChange(event: sap.ui.base.Event) {
    this.captureSelectedRowsSnapshot(event);
    const selectedIndices = this.getSelectedViews();
    if (selectedIndices?.length === 0) {
      (this.view.getModel("persistencyActionModel") as sap.ui.model.json.JSONModel).setProperty(
        "/menuButtonsEnabled",
        false
      );
    } else if (selectedIndices?.length > 0) {
      (this.view.getModel("persistencyActionModel") as sap.ui.model.json.JSONModel).setProperty(
        "/menuButtonsEnabled",
        true
      );
    }
    if (selectedIndices.length > 1) {
      this.changeMultiSelectButtonStates();
    } else {
      this.changeButtonStates(event);
      this.changeScheduleMenuStates();
    }
    await this.handleMassOpsButtonStatus();

    const selectedViews = this.getSelectedViews();
    const status = selectedViews[0]?.formattedStatus;
    const executeEnabled =
      selectedViews.length === 1 && ((status && status.toLowerCase() !== "running") || isUndefined(status))
        ? true
        : false;
    (this.view.getModel("persistencyActionModel") as sap.ui.model.json.JSONModel).setProperty(
      "/loadNewSnapShot",
      executeEnabled
    );
  }

  private getSelectedViews(): any[] {
    return this.selectedRowsSnapshot;
  }

  private hasPersistedData(viewData: any): boolean {
    switch (viewData.replicationState) {
      case replicationState.Available:
      case replicationState.Error:
        if (viewData.dataPersistency === "Persisted") {
          return true;
        }
    }
    return false;
  }

  private changeMultiSelectButtonStates() {
    const selectedViews = this.getSelectedViews();
    const viewsWithPersistedData = selectedViews.filter((view) => this.hasPersistedData(view));
    const isRemoveEnabled = viewsWithPersistedData.length > 0;
    this.view.getModel("persistencyActionModel").setProperty("/loadNewSnapShot", false);
    this.view.getModel("persistencyActionModel").setProperty("/removePersistencyData", isRemoveEnabled);
    this.view.getModel("persistencyActionModel").setProperty("/deletePersistency", false);
    this.view.getModel("persistencyActionModel").setProperty("/enableViewAnalyzer", selectedViews?.length === 1);
  }

  async changeScheduleMenuStates() {
    const selectedViews = this.getSelectedViews();
    let scheduleDialog;
    if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
      await this.initScheduleDialog();
      scheduleDialog = this["newScheduleDialog"];
    } else {
      scheduleDialog = (
        this.getView().byId("viewScheduleDialog") as sap.ui.core.mvc.View
      ).getController() as ITTaskScheduleController;
    }
    const isScheduled = selectedViews[0]?.scheduled;
    this.selectedView = selectedViews[0]?.viewName;
    const scheduleList = await scheduleDialog?.getTaskScheduleList(this.spaceId, ApplicationId.VIEWS);
    const selectedTableSchedule = scheduleList?.find((obj) => obj.objectId === this.selectedView);
    /** Below are task schecdule model peroperties */
    if (selectedViews.length === 0) {
      (this.view.getModel("persistencyActionModel") as sap.ui.model.json.JSONModel).setProperty("/newSchedule", false);
      (this.view.getModel("persistencyActionModel") as sap.ui.model.json.JSONModel).setProperty("/editSchedule", false);
      (this.view.getModel("persistencyActionModel") as sap.ui.model.json.JSONModel).setProperty(
        "/deleteSchedule",
        false
      );
    } else {
      if (selectedTableSchedule !== undefined && isScheduled) {
        (this.view.getModel("persistencyActionModel") as sap.ui.model.json.JSONModel).setProperty(
          "/newSchedule",
          false
        );
        (this.view.getModel("persistencyActionModel") as sap.ui.model.json.JSONModel).setProperty(
          "/editSchedule",
          true
        );
        (this.view.getModel("persistencyActionModel") as sap.ui.model.json.JSONModel).setProperty(
          "/deleteSchedule",
          true
        );
      } else {
        (this.view.getModel("persistencyActionModel") as sap.ui.model.json.JSONModel).setProperty("/newSchedule", true);
        (this.view.getModel("persistencyActionModel") as sap.ui.model.json.JSONModel).setProperty(
          "/editSchedule",
          false
        );
        (this.view.getModel("persistencyActionModel") as sap.ui.model.json.JSONModel).setProperty(
          "/deleteSchedule",
          false
        );
      }
    }
  }

  public changeButtonStates(event: sap.ui.base.Event) {
    const selectedViews = this.getSelectedViews();

    if (selectedViews.length === 1) {
      // one row was selected
      if (event.getParameter("rowContext") && event.getParameter("rowContext").getModel()) {
        this.view.getModel("persistencyActionModel").setProperty("/enableViewAnalyzer", true);
        (this.view.getModel("persistencyActionModel") as sap.ui.model.json.JSONModel).setProperty(
          "/loadNewSnapShot",
          true
        );
        (this.view.getModel("persistencyActionModel") as sap.ui.model.json.JSONModel).setProperty(
          "/deletePersistency",
          true
        );
        const selectedRowData = event
          .getParameter("rowContext")
          .getModel()
          .getProperty(event.getParameter("rowContext").getPath());
        switch (selectedRowData.replicationState) {
          case replicationState.Initialize:
          case replicationState.None:
            (this.view.getModel("persistencyActionModel") as sap.ui.model.json.JSONModel).setProperty(
              "/removePersistencyData",
              false
            );
            break;
          case replicationState.Available:
          case replicationState.Error:
            if (selectedRowData.dataPersistency === "Persisted") {
              (this.view.getModel("persistencyActionModel") as sap.ui.model.json.JSONModel).setProperty(
                "/removePersistencyData",
                true
              );
            } else {
              (this.view.getModel("persistencyActionModel") as sap.ui.model.json.JSONModel).setProperty(
                "/removePersistencyData",
                false
              );
            }
            break;
        }
      }
    } else {
      // a row was de-selected
      (this.view.getModel("persistencyActionModel") as sap.ui.model.json.JSONModel).setProperty(
        "/loadNewSnapShot",
        false
      );
      (this.view.getModel("persistencyActionModel") as sap.ui.model.json.JSONModel).setProperty(
        "/removePersistencyData",
        false
      );
      (this.view.getModel("persistencyActionModel") as sap.ui.model.json.JSONModel).setProperty(
        "/deletePersistency",
        false
      );
      this.view.getModel("persistencyActionModel").setProperty("/enableViewAnalyzer", false);
    }
  }

  async openSchedule(oEvent: any, viewName: string): Promise<void> {
    let scheduleDialog;
    const link = oEvent.getSource();
    const applicationId = ApplicationId.VIEWS;
    const data: ITaskScheduleRequest = {
      objectId: viewName,
      applicationId: ApplicationId.VIEWS,
      activity: Activity.PERSIST,
      description: "View Persistency Scheduling",
      activationStatus: "ENABLED",
    };
    const isSpaceLocked = this.getView().getModel("diPageModel")?.getProperty("/isSpaceLocked");
    if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
      await this.initScheduleDialog();
      if (isDiMonitorImprovementsEnabled()) {
        await openSchedulePopover(
          link,
          viewName,
          applicationId,
          this.spaceId,
          this["newScheduleDialog"],
          data,
          this,
          this.onRefreshTable.bind(this),
          isSpaceLocked
        );
      } else {
        await openSchedulePopover(link, viewName, applicationId, this.spaceId, this["newScheduleDialog"]);
      }
    } else {
      scheduleDialog = (
        this.getView().byId("viewScheduleDialog") as sap.ui.core.mvc.View
      ).getController() as ITTaskScheduleController;
      scheduleDialog.openSchedulePopover(link, viewName, ApplicationId.VIEWS, this.spaceId);
    }
  }

  openPartiallyPersistedLink(oEvent: any, inputParametersDetails: any, latestUpdate: any): void {
    const link = oEvent.getSource();
    openPartiallyPersistedInfo(link, inputParametersDetails, latestUpdate, this);
  }

  /**
   * Determines if cached data should be used based on feature flag
   */
  private shouldUseCachedData(): boolean {
    return sap.ui.getCore().getModel("featureflags").getProperty("/DWCO_VIEW_PERSISTENCY_NO_REPOSITORY");
  }

  /**
   * Gets cached data based on current tab selection
   */
  private getCachedDataForCurrentTab(): any {
    const segmentedButton = this.getView().byId(`ViewDisplaySegmentedButton`) as sap.m.SegmentedButton;
    const selectedKey = segmentedButton?.getSelectedKey() || this.currentTab;

    switch (selectedKey) {
      case "allViews":
        return this.responseListAllViews;
      case "viewsWithRuns":
        return this.responseListViewWithRuns;
      default:
        // Fallback to current tab property
        if (this.currentTab === "allViews" && this.responseListAllViews) {
          return this.responseListAllViews;
        } else if (this.currentTab === "viewsWithRuns" && this.responseListViewWithRuns) {
          return this.responseListViewWithRuns;
        }
        return null;
    }
  }

  // Load views with business names ignoring the threshold
  onLoadBusinessNames() {
    let selectedTab = this.getView().byId("ViewDisplaySegmentedButton") as sap.m.SegmentedButton;
    this.loadData(selectedTab.getSelectedKey(), true);

    // change column label to business name
    this.view.byId("viewNameColumn")["setLabel"](this.getText("NAME_LABELNew"));
  }
  // auxiliary method to be used instead of method loadData on default model so that ServiceCall API is used for http GET
  private async loadData(viewlist?: string, ignoreThresholdforBusinessName = false): Promise<any> {
    let service = SERVICENAMES.persistedViews;
    let scheduleList;
    let scheduleDialog;
    if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
      await this.initScheduleDialog();
      scheduleDialog = this["newScheduleDialog"];
    } else {
      scheduleDialog = (
        this.getView().byId("viewScheduleDialog") as sap.ui.core.mvc.View
      ).getController() as ITTaskScheduleController;
    }
    // eslint-disable-next-line prefer-const
    scheduleList = await scheduleDialog?.getTaskScheduleList(this.spaceId, ApplicationId.VIEWS);
    (this.view.getModel("persistencyActionModel") as sap.ui.model.json.JSONModel).setProperty(
      "/scheduleList",
      scheduleList
    );
    if (this.displayName === "businessName") {
      service = SERVICENAMES.persistedViewsWithBusinessName;
    }

    if (sap.ui.getCore().getModel("featureflags").getProperty("/DWCO_VIEW_PERSISTENCY_NO_REPOSITORY") && viewlist) {
      service = SERVICENAMES[viewlist];
    }
    // Check cache availability and segmented button selection
    const shouldUseCache = this.shouldUseCachedData();
    const cachedData = this.getCachedDataForCurrentTab();

    if (shouldUseCache && cachedData && !ignoreThresholdforBusinessName) {
      // Use cached data
      this.processAndSetData(cachedData, scheduleList);
      this.view.setBusy(false);
      return;
    }

    this.view.setBusy(true);
    try {
      const oResponse = await fetchViewPersistenceService(this.spaceId, service, [
        this.displayName,
        ignoreThresholdforBusinessName,
      ]);
      this.view.setBusy(false);
      this.cacheResponse(oResponse);
      this.processAndSetData(oResponse, scheduleList);
      this.view.setBusy(false);
    } catch (error) {
      this.view.setBusy(false);
      this.handleLoadError(error);
    }
  }

  /**
   * Caches the response based on current tab selection
   */
  private cacheResponse(oResponse: any): void {
    const segmentedButton = this.getView().byId(`ViewDisplaySegmentedButton`) as sap.m.SegmentedButton;
    const selectedKey = segmentedButton?.getSelectedKey() || this.currentTab;

    switch (selectedKey) {
      case "allViews":
        this.responseListAllViews = Object.assign({}, oResponse);
        break;
      case "viewsWithRuns":
        this.responseListViewWithRuns = Object.assign({}, oResponse);
        break;
      default:
        // Fallback to current tab property
        if (this.currentTab === "allViews") {
          this.responseListAllViews = Object.assign({}, oResponse);
        } else if (this.currentTab === "viewsWithRuns") {
          this.responseListViewWithRuns = Object.assign({}, oResponse);
        }
        break;
    }
  }

  /**
   * Processes response data and updates the UI
   */
  private processAndSetData(oResponse: any, scheduleList: any[]): void {
    // Process each table item
    oResponse?.tables?.forEach((item) => {
      this.processTableItem(item, scheduleList, oResponse.thresholdForBusinessNamesExceeded);
    });

    // Set data to model
    (this.view.getModel() as sap.ui.model.json.JSONModel).setData(oResponse);
    this.responseList = Object.assign(oResponse.tables);

    // Calculate totals and update UI
    this.updateTableUI(oResponse);

    // Apply filters and sorting
    this.applyFiltersAndSorting();

    // Handle search
    this.handleSearch();
  }

  /**
   * Processes individual table item with formatting and schedule information
   */
  private processTableItem(item: any, scheduleList: any[], thresholdForBusinessNamesExceeded): void {
    item.displayName = item.viewName;

    if (this.displayName === "businessName" && item.viewBusinessName && item.viewBusinessName !== "") {
      item.displayName = item.viewBusinessName;
    }

    // set technical name if business name is not available or threshold exceeded
    if (thresholdForBusinessNamesExceeded) {
      item.displayName = item.viewName;
    }

    const bundleName = require("../i18n/i18n.properties");
    const i18nModel = new sap.ui.model.resource.ResourceModel({ bundleName });
    const oBundle = i18nModel.getResourceBundle();

    // Format basic properties
    item.formattedDataPersistency = this.getText(item.dataPersistency);
    item.formattedlatestUpdate =
      item.latestUpdate && item.latestUpdate !== "" ? this.formatDateTime(item.latestUpdate) : "";

    // Handle scheduling
    const schedule = scheduleList?.find((obj) => obj.objectId === item.viewName);
    item.scheduled = !!schedule;

    if (schedule) {
      item.nextSchedule = schedule.nextRun;
      item.isPaused = schedule.activationStatus === "DISABLED";
      item.scheduleOwner = schedule.owner || "";
    } else {
      item.isPaused = false;
      item.scheduleOwner = "";
    }

    // Format schedule-related properties
    if (item.isPaused) {
      item.formattedRefreshFrequency = oBundle.getText("paused");
    } else {
      item.formattedRefreshFrequency = this.formatter.getTextRefreshFrequency(item.dataPersistency, item.scheduled);
    }

    item.formattedNextSchedule = this.formatter.formatNextSchedule(
      item.nextSchedule,
      item.scheduled,
      item.viewName,
      scheduleList
    );

    item.formattedStatus = this.formatter.getTextViewPersistenceStatus(
      item.replicationState,
      item.replicationType,
      item.taskState
    );

    item.sortedStatus = this.formatter.getTextViewSortedStatus(
      item.replicationState,
      item.replicationType,
      item.taskState
    );

    item.formattedStatusTooltip = this.formatter.getViewPersistenceStatusTooltip(
      item.replicationState,
      item.replicationType,
      item.taskState,
      item.replicationError
    );

    item.formattedNumberOfRecords = this.formatNumber(item.numberOfRecords);
    item.formatteddiskSizeReplicaTableInMB = this.formatNumber(item.diskSizeReplicaTableInMB);
    item.formattedinMemorySizeReplicaTableMB = this.formatNumber(item.inMemorySizeReplicaTableMB);

    if (sap.ui.getCore().getModel("featureflags").getProperty("/DWCO_VIEW_PERSISTENCY_NO_REPOSITORY")) {
      item.formattedLastRunStart = this.formatDateTime(item.lastRunStart);
      item.formattedLastRunEnd = this.formatDateTime(item.lastRunEnd);
      item.formattedLastRunDuration = this.formatter.getDuration(item.lastRunStart, item.lastRunEnd);
      item.formattedLastRunStatus = this.getText(item.lastRunStatus);
      item.formattedActivity = this.getText(item.lastRunActivity);
    }
  }

  /**
   * Updates table UI elements and properties
   */
  private updateTableUI(oResponse: any): void {
    const oTotal = this.calculateTotalDiskSize(oResponse.tables);
    const oBinding = this.byId("viewMonitorTable").getBinding("rows") as sap.ui.model.json.JSONListBinding;
    const count = oBinding.getLength().toString();

    this.sTableHeader = this.getText("txtViewCount", [count]);
    this.oTable = this.byId("viewMonitorTable") as sap.ui.table.Table;
    this.oSortOrder = sap.ui.table.SortOrder.Ascending;

    // Update model properties
    const model = this.view.getModel() as sap.ui.model.json.JSONModel;
    model.setProperty("/tableHeader", this.sTableHeader);
    model.setProperty(
      "/isFullViewList",
      Object.prototype.hasOwnProperty.call(oResponse, "isFullViewList") ? oResponse.isFullViewList : true
    );
    model.setProperty("/totalDiskSize", oTotal.totalDiskSize.toFixed(2));
    model.setProperty("/totalMemorySize", oTotal.totalMemorySize.toFixed(2));
    model.setProperty("/", oResponse);

    if (!sap.ui.getCore().getModel("featureflags").getProperty("/DWCO_VIEW_PERSISTENCY_NO_REPOSITORY")) {
      // Update segmented button selection
      const segmentedButton = this.getView().byId(`ViewsBtn`) as sap.m.SegmentedButton;
      const scheduledView = this.view.byId("scheduledViewBtn") as sap.m.SegmentedButtonItem;
      segmentedButton.fireSelectionChange({ item: scheduledView });
      segmentedButton.setSelectedItem(scheduledView);
    }
  }

  /**
   * Applies existing filters and sorting to the table
   */
  private applyFiltersAndSorting(): void {
    if (this.filtersList) {
      const table = this.oTable;
      const filterList = this.filtersList;
      Object.keys(this.filtersList).forEach(function (column) {
        const col = table.getColumns().filter((e) => e.hasOwnProperty("sId") && e.getId() === column);
        table.filter(col[0], filterList[column]);
      });
    }

    if (this.sortObject) {
      this.oTable.sort(this.sortObject?.column, this.sortObject?.sortOrder, false);
    } else {
      this.oTable.sort(this.oTable.getColumns()[1], sap.ui.table.SortOrder.Descending, false);
    }
  }

  /**
   * Handles search functionality
   */
  private handleSearch(): void {
    if (this.searchValue && this.searchValue.length > 0) {
      this.getFilters(this.searchValue);
      const searchField = this.view.byId("searchTablesInput") as sap.m.SearchField;
      searchField.setValue(this.searchValue);
    }
  }

  /**
   * Handles errors during data loading
   */
  private handleLoadError(error: any): void {
    (this.view.getModel() as sap.ui.model.json.JSONModel).setProperty("/", {});

    if (error && error.status === 500) {
      this.logError(error);
    } else if (error && error.responseJSON && error.responseJSON.details) {
      MessageHandler.error(error.responseJSON.details.message);
    } else {
      this.logError(
        (this.view.getModel("i18n") as sap.ui.model.resource.ResourceModel)
          .getResourceBundle()
          .getText("txtReadBackendError")
      );
    }
  }

  onViewDisplayChange(event: sap.ui.base.Event) {
    const selectedItem = event.getParameter("item");
    const selectedKey = selectedItem.getKey();
    // check for selected key and update the cache if required
    this.view.setBusy(true);
    this.loadData(selectedKey);
  }

  // schedule the view persistence
  public async startPersistence(sView: string): Promise<void> {
    this.view.setBusy(true);
    // record action for usage tracking
    ShellContainer.get().getUsageCollectionService().recordAction({
      action: "startPersistence",
      feature: "viewMonitor",
      eventtype: "click",
    });

    fetchViewPersistenceService(this.spaceId, SERVICENAMES.startPersistence, sView)
      .then(() => {
        this.view.setBusy(false);
        // start was successful, so that the view can be refreshed

        MessageHandler.success(
          (this.view.getModel("i18n") as sap.ui.model.resource.ResourceModel)
            .getResourceBundle()
            .getText("startPersistenceSuccess", [sView])
        );

        (this.view.getModel("persistencyActionModel") as sap.ui.model.json.JSONModel).setProperty(
          "/loadNewSnapShot",
          false
        );
        (this.view.getModel("persistencyActionModel") as sap.ui.model.json.JSONModel).setProperty(
          "/removePersistencyData",
          false
        );
        (this.view.getModel("persistencyActionModel") as sap.ui.model.json.JSONModel).setProperty(
          "/deletePersistency",
          false
        );

        this.onRefreshTable();
      })
      .catch((oResponse) => {
        this.view.setBusy(false);
        handleViewPersistenceResponse(oResponse, sView, this.spaceId);
        this.loadData();
      });
  }

  public async onDeletePersistence(): Promise<void> {
    let sView;
    sView = this.getSelectedViews()[0];
    const viewName = sView.viewName;
    sap.m.MessageBox.warning(this.getText("DeletePersistency_Confirm_Msg", [viewName]), {
      styleClass: "sapUiSizeCompact",
      actions: [sap.m.MessageBox.Action.OK, sap.m.MessageBox.Action.CANCEL],
      initialFocus: sap.m.MessageBox.Action.CANCEL,
      onClose: (action: sap.m.MessageBox.Action) => {
        if (action === sap.m.MessageBox.Action.OK) {
          this["deletePersistence"](viewName);
        }
      },
    });
  }

  private onMassStopPersistence() {
    const selectedViews = this.getSelectedViews();
    const filterViewsList = selectedViews.filter((view) => this.hasPersistedData(view));
    const warnText =
      filterViewsList.length < selectedViews.length
        ? this.getText("PartialMassRemovePersistency_Confirm_Msg", [
            filterViewsList.length.toString(),
            selectedViews.length.toString(),
          ])
        : this.getText("MassRemovePersistency_Confirm_Msg");
    sap.m.MessageBox.warning(warnText, {
      styleClass: "sapUiSizeCompact",
      actions: [sap.m.MessageBox.Action.OK, sap.m.MessageBox.Action.CANCEL],
      initialFocus: sap.m.MessageBox.Action.CANCEL,
      onClose: (action: sap.m.MessageBox.Action) => {
        if (action === sap.m.MessageBox.Action.OK) {
          this.stopMassPersistence(filterViewsList);
        }
      },
    });
  }

  public async onStopPersistence(): Promise<void> {
    const viewTable = this.byId("viewMonitorTable") as sap.ui.table.Table;
    const selectedViews = this.getSelectedViews();
    if (selectedViews.length > 1) {
      this.onMassStopPersistence();
    } else {
      let sView;
      sView = selectedViews[0].viewName;
      sap.m.MessageBox.warning(this.getText("RemovePersistency_Confirm_Msg", [sView]), {
        styleClass: "sapUiSizeCompact",
        actions: [sap.m.MessageBox.Action.OK, sap.m.MessageBox.Action.CANCEL],
        initialFocus: sap.m.MessageBox.Action.CANCEL,
        onClose: (action: sap.m.MessageBox.Action) => {
          if (action === sap.m.MessageBox.Action.OK) {
            this.stopPersistence(sView);
          }
        },
      });
    }
  }

  public stopMassPersistence(viewsList: any[]) {
    this.view.setBusy(true);
    fetchViewPersistenceService(this.spaceId, SERVICENAMES.stopMassPersistence, viewsList)
      .then(() => {
        this.view.setBusy(false);
        MessageHandler.success(this.view.getModel("i18n").getResourceBundle().getText("stopMassPersistenceSuccess"));
        this.onRefreshTable();
      })
      .catch((oResponse) => {
        this.view.setBusy(false);
        MessageHandler.exception({
          exception: oResponse,
          message: this.view.getModel("i18n").getResourceBundle().getText("STOP_MASS_PERSISTENCE_ERROR"),
        });
        this.loadData();
      });
  }

  // Remove the persisted data of selected view
  public async stopPersistence(sView: string): Promise<any> {
    this.view.setBusy(true);
    (this.view.getModel("persistencyActionModel") as sap.ui.model.json.JSONModel).setProperty(
      "/removePersistencyData",
      false
    );

    // record action for usage tracking
    ShellContainer.get().getUsageCollectionService().recordAction({
      action: "stopPersistence",
      feature: "viewMonitor",
      eventtype: "click",
    });

    fetchViewPersistenceService(this.spaceId, SERVICENAMES.stopPersistence, sView)
      .then(() => {
        this.view.setBusy(false);
        // start was successful, so that the table can be refreshed
        /*   if (oResponse.status === "success") { */
        MessageHandler.success(
          this.view.getModel("i18n").getResourceBundle().getText("stopPersistenceSuccess", [sView])
        );

        (this.view.getModel("persistencyActionModel") as sap.ui.model.json.JSONModel).setProperty(
          "/loadNewSnapShot",
          false
        );
        (this.view.getModel("persistencyActionModel") as sap.ui.model.json.JSONModel).setProperty(
          "/removePersistencyData",
          false
        );
        (this.view.getModel("persistencyActionModel") as sap.ui.model.json.JSONModel).setProperty(
          "/deletePersistency",
          false
        );
        // read data from backend into default model this.view.getModel()
        this.onRefreshTable();
      })
      .catch((oResponse) => {
        this.view.setBusy(false);
        (this.view.getModel("persistencyActionModel") as sap.ui.model.json.JSONModel).setProperty(
          "/removePersistencyData",
          true
        );
        if (oResponse && oResponse.responseJSON && oResponse.responseJSON.details) {
          MessageHandler.error(oResponse.responseJSON.details.message);
        } else {
          MessageHandler.error(
            this.view.getModel("i18n").getResourceBundle().getText("STOP_PERSISTENCE_ERROR", [sView])
          );
        }
        // read data from backend into default model this.view.getModel()
        this.loadData();
        return false;
      });
  }

  public onSearchField(oEvent: sap.ui.base.Event): void {
    const table = this.view.byId("viewMonitorTable");
    const columns: any[] = table?.["getColumns"]();
    columns?.forEach((col) => {
      col.setFilterValue("");
      col.setFiltered(false);
    });
    if (oEvent !== undefined) {
      this.searchValue = oEvent.getParameters().newValue;
    }
    this.getFilters(this.searchValue);
    this.restoreSelectedRows();
  }

  private getFilters(searchValue: string): void {
    const self = this;
    const oBinding = this.byId("viewMonitorTable").getBinding("rows") as sap.ui.model.json.JSONListBinding;
    this.oGlobalFilter = new sap.ui.model.Filter({
      filters: [],
      and: true,
    });
    if (this.searchValue && this.searchValue.length >= 1) {
      this.oGlobalFilter = new sap.ui.model.Filter({
        filters: [
          new sap.ui.model.Filter("displayName", sap.ui.model.FilterOperator.Contains, searchValue),
          new sap.ui.model.Filter("dataPersistency", sap.ui.model.FilterOperator.Contains, searchValue),
          new sap.ui.model.Filter({
            path: "replicationState",
            operator: sap.ui.model.FilterOperator.Contains,
            test: function (oValue) {
              const convertedValue = self.formatter.getTextViewPersistenceStatus(oValue, "", "");
              return convertedValue.toLowerCase().includes(self.searchValue.toLowerCase());
            },
          }),
        ],
        and: false,
      });
    }

    oBinding.filter([this.oGlobalFilter], null);
  }

  public fireOnFilterColumn(event): void {
    const searchText = this.getView().byId("searchTablesInput");
    searchText["setValue"]("");
    this.searchValue = "";
    if (!this.filtersList) {
      this.filtersList = {};
    }
    const filterColumn = event?.getParameter("column")?.getId();
    const filterValue = event?.getParameter("value");
    this.filtersList[filterColumn] = filterValue;
    setTimeout(() => {
      this.restoreSelectedRows();
    }, 0);
  }

  public fireOnSort(event) {
    if (!this.sortObject) {
      this.sortObject = {};
    }
    this.sortObject.column = event?.getParameter("column");
    this.sortObject.sortOrder = event?.getParameter("sortOrder");
    setTimeout(() => {
      this.restoreSelectedRows();
    });
  }

  private calculateTotalDiskSize(tables): any {
    let totalDiskSize = 0;
    let totalMemorySize = 0;
    const obj = {
      totalDiskSize: totalDiskSize,
      totalMemorySize: totalMemorySize,
    };

    tables.forEach((table) => {
      obj.totalDiskSize = totalDiskSize += Number(table.diskSizeReplicaTableInMB);
      obj.totalMemorySize = totalMemorySize += Number(table.inMemorySizeReplicaTableMB);
    });

    return obj;
  }

  public LoadViewPersistencyLogs(oEvent: IEvent<any, any>): void {
    const oItem = oEvent.getSource();
    const oBinding = oItem.getBindingContext();
    this.selectedRowIndex = oEvent?.getParameter("row")?.getIndex();
    const sName = oBinding?.getObject("viewName");
    if (oEvent.getSource()) {
      this.router.navTo("viewMonitorTaskLog", {
        spaceId: this.spaceId,
        objectId: encodeURIComponent(sName),
      });
    }
  }

  public onPersoButtonPress() {
    this.tablePersoController.openDialog();
  }

  public onViewChange(oEvent) {
    const src = oEvent.getSource();
    const selection = src?.getSelectedKey();
    // clear search when changing views
    // const searchText = this.getView().byId("searchTablesInput");
    // searchText.setValue("");

    this.filterViews(selection);
    if (this.searchValue && this.searchValue.length > 0) {
      this.onSearchField(undefined);
    } else {
      const searchText = this.getView().byId("searchTablesInput");
      searchText["setValue"]("");
      this.searchValue = "";
    }
  }

  public filterViews(selection) {
    let list = this.responseList;
    this.view
      .getModel()
      .setProperty(
        "/" + this.ViewFilter.ALLVIEWLBL,
        this.getText(this.ViewFilter.ALLVIEWLBL, [`${this.responseList?.length}`])
      );
    const schedulelist = this.responseList?.filter((view) => {
      return view.scheduled;
    });
    this.view
      .getModel()
      .setProperty(
        "/" + this.ViewFilter.SCHDULEDLBL,
        this.getText(this.ViewFilter.SCHDULEDLBL, [`${schedulelist?.length}`])
      );
    const persistedList = this.responseList?.filter((view) => {
      return view.dataPersistency === "Persisted";
    });
    this.view
      .getModel()
      .setProperty(
        "/" + this.ViewFilter.PERSISTEDLBL,
        this.getText(this.ViewFilter.PERSISTEDLBL, [`${persistedList?.length}`])
      );
    switch (selection) {
      case this.ViewFilter.SCHEDULED:
        this.view.getModel().setProperty("/tables", schedulelist);
        this.currentViewList = this.ViewFilter.SCHDULEDLBL;
        break;
      case this.ViewFilter.PERSISTED:
        this.view.getModel().setProperty("/tables", persistedList);
        this.currentViewList = this.ViewFilter.PERSISTEDLBL;
        break;

      default:
        this.view.getModel().setProperty("/tables", list);
        this.currentViewList = this.ViewFilter.ALLVIEWLBL;
        break;
    }
  }

  recordViewAnalyzerAction() {
    ShellContainer.get().getUsageCollectionService().recordAction({
      action: "startViewAnalyzer",
      feature: "viewMonitorTaskLog",
      eventtype: EventType.CLICK,
    });
  }

  private getStartAnalyzerDialog() {
    const dialog = require("../../viewanalyzer/view/StartAnalyzerDialog.view.xml");
    const analyzerView = sap.ui.view({
      id: this.getView().getId() + "--startAnalyzerrDialog",
      type: sap.ui.core.mvc.ViewType.XML,
      viewName: dialog,
      viewData: {
        spaceId: this.spaceId,
      },
    });
    return analyzerView.getController() as StartAnalyzerDialog;
  }

  public startViewAnalyzer() {
    this.recordViewAnalyzerAction();
    const startDialog = this.getStartAnalyzerDialog();
    const viewId = this.getSelectedViews()[0]?.viewName;
    startDialog.openDialog(true, viewId, this.spaceId);
  }

  private getTable(): sap.m.Table {
    return this.byId("viewMonitorTable") as sap.m.Table;
  }

  public getScheduledText(viewName, formattedRefreshFrequency, isPaused, scheduleList) {
    if (isDiMonitorImprovementsEnabled() && !isPaused && scheduleList !== undefined) {
      const schedule = scheduleList?.find((obj) => obj.objectId === viewName);
      if (schedule) {
        return schedule?.cron !== undefined
          ? schedule?.cron
          : this.getText("everyLabel") +
              " " +
              schedule?.frequency?.interval +
              " " +
              this.getFrequencyText(schedule?.frequency?.type);
      } else {
        return formattedRefreshFrequency;
      }
    } else {
      return formattedRefreshFrequency;
    }
  }

  public getFrequencyText(type) {
    switch (type) {
      case "MINUTES":
        return this.getText("minutesLabel");
      case "HOURLY":
        return this.getText("hoursLabel");
      case "DAILY":
        return this.getText("daysLabel");
      case "WEEKLY":
        return this.getText("weeksLabel");
      case "MONTHLY":
        return this.getText("monthsLabel");
      default:
        return "";
    }
  }
}

export const ViewMonitor = smartExtend(
  BaseController,
  "sap.cdw.components.viewmonitor.controller.ViewMonitor",
  ViewMonitorClass
);

sap.ui.define("sap/cdw/components/viewmonitor/controller/ViewMonitor.controller", [], function () {
  return ViewMonitor;
});
