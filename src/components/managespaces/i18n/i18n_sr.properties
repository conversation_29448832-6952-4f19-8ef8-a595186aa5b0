#DO NOT REMOVE________________________
#XFLD: Breadcrumb for space monitoring page
spaceMonitorBreadcrumb=Надзор
#XTXT: Type name for spaces in browser tab page title
space=Простор
#_____________________________________
#XFLD: Spaces label in
spaces=Простори
#XFLD: Manage plan button text
manageQuotaButtonText=Управљај планом
#XBUT: Manage resources button
manageResourcesButton=Управљај ресурсима
#XFLD: Create space button tooltip
createSpace=Креирај простор
#XFLD: Create
create=Креирај
#XFLD: Deploy
deploy=Имплементирај
#XFLD: Page
page=Страница
#XFLD: Cancel
cancel=Одустани
#XFLD: Update
update=Ажурирај
#XFLD: Save
save=Сачувај
#XFLD: OK
ok=OK
#XFLD: days
days=Дани
#XFLD: Space tile edit button label
edit=Уреди
#XFLD: Auto Assign all objects to space
autoAssign=Аутоматски додели
#XFLD: Space tile open monitoring button label
openMonitoring=Надгледај
#XFLD: Delete
delete=Избриши
#XFLD: Copy Space
copy=Копирај
#XFLD: Close
close=Затвори
#XCOL: Space table-view column status
status=Статус
#XFLD: Space status active
activeLabel=Активно
#XFLD: Space status locked
lockedLabel=Закључано
#XFLD: Space status critical
criticalLabel=Критично
#XFLD: Space status cold
coldLabel=Неактивно
#XFLD: Space status deleted
deletedLabel=Избрисано
#XFLD: Space status unknown
unknownLabel=Непознато
#XFLD: Space status ok
okLabel=У добром стању
#XFLD: Database user expired
expired=Истекло
#XFLD: deployed
deployed=Имплементирано
#XFLD: not deployed
notDeployed=Није имплементирано
#XFLD: changes to deploy
changesToDeploy=Промене за имплементацију
#XFLD: pending
pending=Имплементација
#XFLD: designtime error
designtimeError=Грешка времена дизајна
#XFLD: runtime error
runtimeError=Грешка времена извођења
#XFLD: Space created by label
createdBy=Креирао
#XFLD: Space created on label
createdOn=Креирано
#XFLD: Space deployed on label
deployedOn=Имплементирано
#XFLD: Space ID label
spaceID=ID простора
#XFLD: Priority label
priority=Приоритет
#XFLD: Space Priority label
spacePriority=Приоритет простора
#XFLD: Space Configuration label
spaceConfiguration=Конфигурација простора
#XFLD: Not available
notAvailable=Није доступно
#XFLD: WorkloadType default
default=Стандардно
#XFLD: WorkloadType custom
custom=Кориснички дефинисано
#XFLD: Data Lake option label; refers to component SAP HANA Cloud, Data Lake
dataLakeAccess=Приступ језеру података
#XFLD: Translation label
translationLabel=Превод
#XFLD: Source language label
sourceLanguageLabel=Изворни језик
#XFLD: Translation CheckBox label
translationCheckBox=Активирај превод
#XTXT: Popover hint that space has to be deployed to show user details
openDatabaseUserHintPopoverText=Имплементирајте простор да бисте приступили детаљима корисника.
#XTXT: Popover hint that space has to be deployed to open database explorer
openDatabaseExplorerHintPopoverText=Имплементирајте простор да бисте отворили претраживач базе података.
#XTXT: Text for when the Data Lake check box is disabled
dataLakeOptionDisabled=Не можете користити овај простор за приступ језеру података јер га већ користи други простор.
#XFLD: Data Lake check box text; refers to component SAP HANA Cloud, Data Lake
dataLakeCheckBox=Користите овај простор за приступ језеру података.
#XFLD: Space Priority minimum label extension
low=Ниско
#XFLD: Space Priority maximum label extension
high=Високо
#XFLD: Space name label
spaceName=Назив простора
#XFLD: Enable deploy objects checkbox
enableDeployObjects=Имплементирај објекте
#XTIT: Copy spaces dialog title
copySpaceDialogTitle=Копирај {0}
#XTXT Human readable text for unselected source language
unSelectedSourceLanguage=(Није одабрано)
#XTXT Human readable text for language code "af"
af=Африканс
#XTXT Human readable text for language code "ar"
ar=Арапски
#XTXT Human readable text for language code "bg"
bg=Бугарски
#XTXT Human readable text for language code "ca"
ca=Каталонски
#XTXT Human readable text for language code "zh"
zh=Поједностављени кинески
#XTXT Human readable text for language code "zf"
zf=Кинески
#XTXT Human readable text for language code "hr"
hr=Хрватски
#XTXT Human readable text for language code "cs"
cs=Чешки
#XTXT Human readable text for language code "cy"
cy=Велшки
#XTXT Human readable text for language code "da"
da=Дански
#XTXT Human readable text for language code "nl"
nl=Холандски
#XTXT Human readable text for language code "en-UK"
en-UK=Енглески (Уједињено Краљевство)
#XTXT Human readable text for language code "en"
en=Енглески (Сједињене Америчке Државе)
#XTXT Human readable text for language code "et"
et=Естонски
#XTXT Human readable text for language code "fa"
fa=Персијски
#XTXT Human readable text for language code "fi"
fi=Фински
#XTXT Human readable text for language code "fr-CA"
fr-CA=Француски (Канада)
#XTXT Human readable text for language code "fr"
fr=Француски
#XTXT Human readable text for language code "de"
de=Немачки
#XTXT Human readable text for language code "el"
el=Грчки
#XTXT Human readable text for language code "he"
he=Хебрејски
#XTXT Human readable text for language code "hi"
hi=Хинди
#XTXT Human readable text for language code "hu"
hu=Мађарски
#XTXT Human readable text for language code "is"
is=Исландски
#XTXT Human readable text for language code "id"
id=Индонежански
#XTXT Human readable text for language code "it"
it=Италијански
#XTXT Human readable text for language code "ja"
ja=Јапански
#XTXT Human readable text for language code "kk"
kk=Казашки
#XTXT Human readable text for language code "ko"
ko=Корејски
#XTXT Human readable text for language code "lv"
lv=Летонски
#XTXT Human readable text for language code "lt"
lt=Литвански
#XTXT Human readable text for language code "ms"
ms=Малајски
#XTXT Human readable text for language code "no"
no=Норвешки
#XTXT Human readable text for language code "pl"
pl=Пољски
#XTXT Human readable text for language code "pt"
pt=Португалски (Бразил)
#XTXT Human readable text for language code "pt-PT"
pt-PT=Португалски (Португал)
#XTXT Human readable text for language code "ro"
ro=Румунски
#XTXT Human readable text for language code "ru"
ru=Руски
#XTXT Human readable text for language code "sr"
sr=Српски
#XTXT Human readable text for language code "sh"
sh=Српскохрватски
#XTXT Human readable text for language code "sk"
sk=Словачки
#XTXT Human readable text for language code "sl"
sl=Словеначки
#XTXT Human readable text for language code "es"
es=Шпански
#XTXT Human readable text for language code "es-MX"
es-MX=Шпански (Мексико)
#XTXT Human readable text for language code "sv"
sv=Шведски
#XTXT Human readable text for language code "th"
th=Тајландски
#XTXT Human readable text for language code "tr"
tr=Турски
#XTXT Human readable text for language code "uk"
uk=Украјински
#XTXT Human readable text for language code "vi"
vi=Вијетнамски
#XTIT: Delete spaces confirmation title
deleteConfirmationHeaderText=Избриши просторе
#XTXT: Logically delete space delete confirmation text with space name
logicallyDeleteConfirmationBodyTextWithSpaceName=Да ли сигурно желите да померите простор "{0}" у корпу за отпатке?
#XTXT: Logically dpace delete confirmation text for multiple spaces
logicallyDeleteConfirmationBodyTextMultiple=Да ли сигурно желите да померите {0} одабраних простора у корпу за отпатке?
#XTXT: Delete space delete confirmation text with space name
deleteConfirmationBodyTextWithSpaceName=Да ли заиста желите да избришете простор "{0}"? Ова радња се не може поништити.
#XTXT: Delete space delete confirmation text for multiple spaces
deleteConfirmationBodyTextMultiple=Да ли заиста желите да избришете {0} одабраних простора? Ова радња се не може поништити. Следећи садржај ће бити {1} избрисан:
#XTXT: permanently
permanently=трајно
#XTXT: {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDetail=Следећи садржај ће бити {0} избрисан и не може се обновити:
#XTXT: Hint Delete space confirmation text
deleteConfirmationHint=Упишите {0} да бисте потврдили брисање.
#XTXT: Delete space confirmation error check spelling
deleteConfirmationErrorCheckSpelling=Проверите правопис и покушајте поново.
#XTXT: All Spaces
allSpaces=Сви простори
#XTXT: All data
allData=Сви објекти и подаци садржани у простору
#XTXT: All connections
allConnections=Све везе дефинисане у простору
#XFLD: Space tile selection box tooltip
clickToSelect=Кликните да одаберете
#XTXT: All database users
allDatabaseUsers=Сви објекти и подаци садржани у свим Open SQL шемама повезаним с простором
#XFLD: remove members button tooltip
deleteUsers=Уклони чланове
#XTXT: Space long description text
description=Опис (максимално 4000 знакова)
#XFLD: Add Members button tooltip
addUsers=Додај чланове
#XFLD: Add Users button tooltip
addUsersTooltip=Додај кориснике
#XFLD: Edit Users button tooltip
editUsersTooltip=Уреди кориснике
#XFLD: Remove Users button tooltip
removeUsersTooltip=Уклони кориснике
#XFLD: Searchfield placeholder
filter=Тражи
#XCOL: Users table-view column health
health=Исправност
#XCOL: Users table-view column access
access=Приступ
#XFLD: No user found nodatatext
noDataText=Корисник није нађен
#XTIT: Members dialog title
selectUserDialogTitle=Додај чланове
#XTIT: User dialog title
addUserDialogTitle=Додај кориснике
#XTIT: Delete connections confirmation title
deleteConnectionConfirmationHeaderText=Избриши везе
#XTIT: Delete single connection confirmation title
deleteConnectionConfirmationHeaderTextSingle=Избриши везу
#XTXT: Delete connection delete confirmation text
deleteConnectionConfirmationBodyText=Да ли заиста желите да избришете одабране везе? Биће трајно уклоњене.
#XTIT: Remote source dialog title
selectRemoteConnectionDialogTitle=Одабери везе
#XTIT: Share connection dialog title
connectionSharingDialogTitle=Дели везу
#XTIT: Connections shared from other spaces table title
sharedConnectionsFromOtherSpaces=Дељене везе
#XFLD: Add remote source button tooltip
addRemoteConnections=Додај везе
#XFLD: Delete remote source button tooltip
deleteRemoteConnections=Уклони везе
#XFLD: Share remote source button tooltip
shareConnections=Дели везе
#XFLD: Tile-layout tooltip
tileLayout=Изглед подекрана
#XFLD: Table-layout tooltip
tableLayout=Изглед табеле
#XMSG: Success message after creating space
createSpaceSuccessMessage=Простор креиран
#XMSG: Success message after copying space
copySpaceSuccessMessage=Копирање простора "{0}" у простор "{1}"
#XMSG: Success message after deploying space
deploymentSuccessMessage=Имплементација простора је покренута
#XMSG: Success message after Apache Spark update started
sparkUpdateStartedSuccessMessage=Ажурирање механизма Apache Spark покренуто
#YMSE: Error while starting Apache Spark update
sparkUpdateStartedErrorMessage=Није успело ажурирање механизма Apache Spark
#XMSG: Success message after updating space
updateSettingsSuccessMessage=Детаљи простора ажурирани
#XMSG: Success message after unlocking space
unlockSpaceSuccessMessage=Простор привремено откључан
#XMSG: Success message after deleting a single space
deleteSpaceSuccessMessage=Простор избрисан
#XMSG: Success message after deleting multiple spaces
deleteSpacesSuccessMessage=Простори избрисани
#XMSG: Success message after restoring a single space
restoreSpaceSuccessMessage=Простор обновљен
#XMSG: Success message after restoring multiple spaces
restoreSpacesSuccessMessage=Простори обновљени
#YMSE: Error while updating settings
updateSettingsFailureMessage=Подешавања простора се не могу ажурирати.
#YMSE: Error while updating data lake option
dataLakeEnabledInOtherSpace=Језеро података је већ додељено другом простору. Истовремени приступ више простора језеру података није могућ.
#YMSE: Error while updating data lake option
virtualTablesExists=Не можете поништити доделу језера података за овај простор јер још постоје зависности у односу на виртуелне табеле*. Избришите виртуелне табеле да бисте поништили доделу језера података за овај простор.
#YMSE: Error while unlocking space
unlockSpaceFailureMessage=Простор се не може откључати.
#YMSE: Error while creating space
createSpaceError=Простор се не може креирати.
#YMSE: Error while creating duplicate space
createDuplicateSpaceError=Простор с називом {0} већ постоји.
#YMSE: Error while deleting a single space
deleteSpaceError=Простор се не може избрисати.
#YMSE: Error details while deleting a single space
deleteSpaceDetailedError=Ваш простор “{0}” више не функционише исправно. Покушајте поново да га избришете. Ако и даље не буде функционисао, замолите администратора да избрише ваш простор или креирајте тикет.
#YMSE: Error while deleting a single space in files
deleteSpaceInFilesError=Подаци простора у фајловима се не могу избрисати.
#YMSE: Error while deleting a single space users
deleteSpaceUsersError=Корисници се не могу уклонити.
#YMSE: Error while deleting a single space schema
deleteSpaceSchemasError=Шеме се не могу уклонити.
#YMSE: Error while deleting a single space connections
deleteSpaceConnectionsError=Везе се не могу уклонити.
#YMSE: Error while deleting a single space data
deleteSpaceDataError=Подаци простора се не могу избрисати.
#YMSE: Error while deleting multiple spaces
deleteSpacesError=Простори се не могу избрисати.
#YMSE: Error while restoring a single space
restoreSpaceError=Простор се не може обновити.
#YMSE: Error while restoring multiple spaces
restoreSpacesError=Простори се не могу обновити.
#YMSE: Error while creating users
createUsersError=Корисници се не могу додати.
#YMSE: Error while removing users
removeUsersError=Не можемо уклонити кориснике.
#YMSE: Error while removing user
removeUserError=Не можемо уклонити корисника.
#YMSE: Error assigning roles to a user
roleAssignUnAssignError=Не можемо додати корисника одабраној улози у опсегу. \n\n Не можете додати себе улози у опсегу. Можете тражити од администратора да вас дода улози у опсегу.
#YMSE: Error assigning user to the space
userAssignError=Нисмо могли да доделимо корисника простору. \n\n Корисник је већ додељен максималном дозвољеном броју (100) простора у свим улогама у опсегу.
#YMSE: Error assigning users to the space
usersAssignError=Нисмо могли да доделимо кориснике простору. \n\n Корисник је већ додељен максималном дозвољеном броју (100) простора у свим улогама у опсегу.
#YMSE: Error while fetching users
fetchUserWithScopedRolesError=Не можемо позвати кориснике. Покушајте поново касније.
#YMSE: Error while fetching scoped roles
fetchScopedRolesError=Не можемо позвати улоге у опсегу.
#YMSE: Error while fetching members
fetchUserError=Чланови се не могу позвати. Покушајте поново касније.
#YMSE: Error while loading run-time database
loadRuntimeError=Не можемо учитати информације из базе података времена извођења.
#YMSE: Error while loading spaces
loadSpacesError=Нажалост, дошло је до проблема при покушају позивања ваших простора.
#YMSE: Error while loading haas resources
loadStorageError=Нажалост, дошло је до проблема при покушају позивања података складишта.
#YMSE: Error no data could be loaded
loadDataError=Нажалост, дошло је до проблема при покушају позивања ваших података.
#XFLD: Click to refresh storage data
clickToRefresh=Кликните овде да покушате поново.
#XTIT: Delete space popup title
deleteSpacePopupTitle=Избриши простор
#XCOL: Spaces table-view column name
name=Назив
#XCOL: Spaces table-view deployment status
deploymentStatus=Статус имплементације
#XFLD: Disk label in space details
storageLabel=Простор на диску (GB)
#XFLD: In-Memory label in space details
ramLabel=Меморија (GB)
#XFLD: Memory label on space card
memory=Меморија за складиштење
#XFLD: Storage Assignment label in space details
storrageAssignmentLabel=Складиште простора
#XFLD: Storage Type label in space details
storageTypeLabel=Тип складишта
#XFLD: Enable Space Quota
enableSpaceQuota=Активирај квоту простора
#XFLD: No Space Quota
noSpaceQuota=Нема квоте простора
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
diskAndInMemoryText=База података SAP HANA (на диску и у меморији)
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
dataLakeFilesText=Фајлови SAP HANA Data Lake
#XFLD: Available scoped roles label
availableRoles=Доступне улоге у опсегу
#XFLD: Selected scoped roles label
selectedRoles=Одабране улоге у опсегу
#XCOL: Spaces table-view column models
models=Модели
#XCOL: Spaces table-view column users
users=Корисници
#XCOL: Spaces table-view column connections
connections=Везе
#XFLD: Section header overview in space detail
overview=Преглед
#XFLD: Section header Apache Spark in space detail
sparkManagementSection=Apache Spark
#XFLD: Section header Apache Spark in space detail
sparkManagementSubSection=Апликације
#XFLD: Section header Apache Spark in space detail
taskAssignmentSubSection=Додела задатка
#XFLD: vCPU label in Apache Spark section
vCPULabel=vCPU
#XFLD: Memory label in Apache Spark section
memoryLabel=Меморија (GB)
#XFLD: space configuration label in Apache Spark section
spaceConfigurationLabel=Конфигурација простора
#XFLD: Space Source label
sparkApplicationLabel=Апликација
#XFLD: Cluster Size label
clusterSizeLabel=Величина скупа
#XFLD: Driver label
driverLabel=Покретач
#XFLD: Executor label
executorLabel=Извршилац
#XFLD: max label
maxLabel=Максимално искоришћено
#XFLD: TrF Default label
trFDefaultLabel=Стандардни ток трансформације
#XFLD: Merge Default label
mergeDefaultLabel=Стандардно спајање
#XFLD: Optimize Default label
optimizeDefaultLabel=Стандардно оптимизирање
#XFLD: Deployment Default label
deploymentDefaultLabel=Имплементација локалне табеле (фајл)
#XFLD: max spark source label
driverExecutorMaxUsedSparkSource={0} CPU / {1} GB
#XFLD: Object type label
taskObjectTypeLabel=Тип објекта
#XFLD: Task activity label
taskActivityLabel=Активност
#XFLD: Task Application ID label
taskApplicationIDLabel=Стнадардна апликација
#XFLD: Section header in space detail
generalSettings=Општа подешавања
#XMSG: Message strip message when a space is locked
spaceLockedMessageStrip=Систем је тренутно закључао овај простор.
#XMSG: Message strip message deploy warning
deployWarningMessageStrip=Промене у овом одељку ће бити одмах имплементиране.
#XMSG: Message strip message modify space configuration warning
modifySpaceConfigurationMessageStrip=Имајте у виду да промена ових вредности може проузроковати проблеме извођења.
#XFLD: Button text to unlock the space again
unlockSpace=Откључај простор
#XFLD: Info text for audit log formatted message
auditLogText=Омогућите да протоколи ревизије снимају, читају или мењају радње (политике ревизије). Администратори затим могу анализирати ко је извршио коју радњу у ком тренутку.
#XMSG: Message strip  audit warning
auditLogWarningMessageStrip=Протоколи ревизије користе велику количину меморије диска у вашем клијенту. Ако активирате политику ревизије (радње читања или промене), треба редовно да пратите коришћење меморије диска (путем картице Искоришћена меморија диска у Монитору система) како бисте избегли потпуне прекиде рада диска који могу довести до прекида у пружању услуга. Ако деактивирате политику ревизије, сви уноси протокола ревизије биће избрисани. Ако желите да задржите уносе протокола ревизије, размотрите њихов извоз пре него што деактивирате политику ревизије.
#XFLD: Message strip link text to open in app help to guide user what to do to unlock his space permanently
unlockSpaceHelp=Покажи помоћ
#XMSG: Message strip message when a space will be locked again
spaceTemporarilyUnlockedMessageStrip=Овај простор прекорачује меморију простора и биће закључан у {0} {1}.
#XMSG: Unit for remaining time until space is locked again
hours=сати
#XMSG: Unit for remaining time until space is locked again
minutes=минути
#XFLD: Section header in space detail for auditing
detailsSectionAuditing=Ревизија
#XFLD: Subsection header in space detail for auditing
auditing=Подешавања ревизије простора
#XFLD: Hot space tooltip
hotSpaceCountTooltip=Критични простори: Искоришћени простор за складиштење је већи од 90%.
#XFLD: Green space tooltip
greenSpaceCountTooltip=Простори у добром стању: Искоришћени простор за складиштење је између 6% и 90%.
#XFLD: Cold space tooltip
coldSpaceCountTooltip=Неактивни простори: Искоришћени простор за складиштење је 5% или мање.
#XFLD: Critical space tooltip
criticalSpaceCountTooltip=Критични простори: Искоришћени простор за складиштење је већи од 90%.
#XFLD: Green space tooltip
okSpaceCountTooltip=Простори у добром стању: Искоришћени простор за складиштење је између 6% и 90%.
#XFLD: Locked space tooltip
lockedSpaceCountTooltipNew=Закључани простори: Блокирано због недовољно меморије.
#XFLD: Locked space tooltip
lockedSpaceCountTooltip=Закључани простори
#YMSE: Error while deleting remote source
deleteRemoteError=Везе се не могу уклонити.
#XFLD: Space ID input tooltip
spaceIDInfoTooltip=ID простора се не може променити касније.\nВажећи знакови A - Z, 0 - 9 и _
#YMSE: Error when space name is empty
spaceNameEmptyValidationError=Унесите назив простора.
#YMSE: Error when ECN Business name is empty
ecnNameEmptyValidationError=Унесите пословни назив.
#YMSE: Error when space id is empty
spaceIDInputEmptyValidationError=Унесите ID простора.
#YMSE: Error when space id contains invalid characters
spaceIDInputInValidError=Неважећи знакови. Користите само A - Z, 0 - 9 и _.
#YMSE: Error when space id exists while creation
spaceIDInputDuplicateError=ID простора већ постоји.
#XFLD: Space searchfield placeholder
search=Тражи
#XMSG: Success message after creating users
createUsersSuccess=Корисници додати
#XMSG: Success message after creating user
createUserSuccess=Корисник додат
#XMSG: Success message after updating users
updateUsersSuccess={0} корисника ажурирано
#XMSG: Success message after updating user
updateUserSuccess=Корисник ажуриран
#XMSG: Success message after removing users
removeUsersSuccess={0} корисника уклоњено
#XMSG: Success message after removing user
removeUserSuccess=Корисник уклоњен
#XFLD: Schema name
schemaName=Назив шеме
#XFLD: used of total
ofTemplate={0} од {1}
#XFLD: Assigned disk label in space overview
assignedStorageTemplate=Додељени простор на диску ({0} од {1})
#XFLD: Assigned Memory label in space overview
assignedRAMTemplate=Додељена меморија ({0} од {1})
#XFLD: Storage ratio on space
accelearationRAM=Убрзање меморије
#XFLD: No Storage Consumption
noStorageConsumptionText=Квота складишта није додељена.
#XFLD: Used disk label in space overview
usedStorageTemplate=Диск коришћен за складиштење ({0} од {1})
#XFLD: Used Memory label in space overview
usedRAMTemplate=Меморија коришћена за складиштење ({0} од {1})
#XFLD: Used Disk tooltip
usedStorageTooltipTemplate={0} од {1} диска искоришћено за складиштење
#XFLD: Used Memory tooltip
usedRAMTooltipTemplate=Искоришћено {0} од {1} меморије
#XFLD: Assigned Disk tooltip
assignedStorageTooltipTemplate=Додељено {0} од {1} простора на диску
#XFLD: Assigned Memory tooltip
assignedRAMTooltipTemplate=Додељено {0} од {1} меморије
#XFLD: Space related used storage tooltip
spaceRelatedUsedStorageTooltipTemplate=Подаци простора: {0}
#XFLD: Not space related used storage tooltip
otherDataUsedStorageTooltipTemplate=Други подаци: {0}
#XFLD: Storage used exceeded popover and tooltip
storageUsedExceededText=Размотрите проширивање вашег плана или контактирајте SAP Support.
#XCOL: Space table-view column used Disk
usedStorage=Диск коришћен за складиштење
#XCOL: Space monitor column used Memory
usedRAM=Меморија коришћена за складиштење
#XCOL: Space monitor column Schema
tableSchema=Шема
#XCOL: Space monitor column Storage Type
tableStorageType=Тип складишта
#XCOL: Space monitor column Table Type
tableType=Тип табеле
#XCOL: Space monitor column Record Count
tableRecordCount=Број записа
#XFLD: Assigned Disk
assignedStorage=Диск додељен за складиштење
#XFLD: Assigned Memory
assignedRAM=Меморија додељена за складиштење
#XCOL: Space table-view column storage utilization
tableStorageUtilization=Искоришћени простор
#XFLD: space status
spaceStatus=Статус простора
#XFLD: space type
spaceType=Тип простора
#XFLD: space type label for space with space type "SAP BW bridge" ("SAP BW bridge" is a product name, do not translate it)
abapbridgeSpaceType=Премошћавање апликације SAP BW
#XFLD: space type label for space with space type "Data Marketplace"
marketplaceSpaceType=Производ даваоца података
#XMSG: Error message for when selected space cannot be deleted due to the space type
deleteTypedSpaceError=Не можете избрисати простор {0} јер је његов тип простора {1}.
#XMSG: Error message for when selected spaces cannot be deleted due to the space type
deleteTypedSpacesError=Не можете избрисати {0} одабраних простора. Простори са следећим типовима простора се не могу избрисати: {1}.
#XFLD: space type label for space without space type
defaultSpaceType=SAP Datasphere
#XFLD: Tooltip for monitor space button
monitorSpace=Надгледај
#XFLD: Tooltip for edit space button
editSpace=Уреди простор
#XMSG: Deletion warning in messagebox
deleteConfirmation=Да ли заиста желите да избришете овај простор?
#XFLD: Tooltip for delete space button
deleteSpace=Избриши простор
#XFLD: storage
storage=Диск за складиштење 
#XFLD: username
userName=Корисничко име
#XFLD: port
port=Порт
#XFLD: hostname
hostName=Назив главног компјутера
#XFLD: password
password=Лозинка
#XBUT: Request new password button
requestPassword=Захтевај нову лозинку
#YEXP: Usage explanation in time data section
timeDataSectionHint=Креирајте распореде и димензије за употребу у моделима и причама.
#YEXP: Usage explanation in Space schema section
spaceSchemaHint=Да ли желите да подаци у вашем простору буду доступни за употребу у другим алатима или апликацијама? Ако желите, креирајте једног или више корисника који могу приступити подацима у вашем простору и одаберите да ли желите да сви будући подаци простора стандардно буду доступни за употребу.
#XTIT: Create schema popup title
createSchemaDialogTitle=Креирај Open SQL шему
#XTIT: Generate Time Data dialog title
generateTimeDataDialogTitle=Креирај распореде и димензије
#XTIT: Edit Time Data dialog title
editTimeDataDialogTitle=Уреди распореде и димензије
#XTIT: Time Data token title
timeDataTokenTitle=Подаци о времену
#XTIT: Time Data token title
timeDataUpdateViews=Ажурирај погледе података о времену
#XFLD: Time Data token creation in progress label
timeDataCreationInProgress=Креирање у току...
#XFLD: Time Data token creation error label
timeDataCreationError=Креирање није успело. Покушајте поново.
#XTIT: Time Data dialog time settings title
timeDataDialogTimeSettingsTitle=Подешавања распореда
#XTIT: Time Data dialog time data table title
timeDataDialogTranslationTablesTitle=Табеле прерачунавања
#XTIT: Time Data dialog dimensions title
timeDataDialogTimeViewsTitle=Димензије времена
#XFLD: Time Data dialog time range label
timeRangeHint=Дефинишите временски распон.
#XFLD: Time Data dialog time data table label
timeDataHint=Дајте назив вашој табели.
#XFLD: Time Data dialog time data dimensions label
timeDimensionsHint=Дајте назив вашим димензијама.
#XFLD: Time Data Time range description label
timerangeLabel=Временски распон
#XFLD: Time Data dialog from year label
fromYearLabel=Година почетка
#XFLD: Time Data dialog to year label
toYearLabel=Година завршетка
#XFLD: Time Data dialog calendar type label
calendarTypeLabel=Тип календара
#XFLD: Time Data dialog granularity label
granularityLabel=Гранулација
#XFLD: Time Data dialog technical name label
technicalNameLabel=Технички назив
#XFLD: Time Data dialog quarters text table lable
quarterTextsTableLabel=Табела прерачунавања за квартале
#XFLD: Time Data dialog months text table lable
monthsTextsTableLabel=Табела прерачунавања за месеце
#XFLD: Time Data dialog days text table lable
daysTextsTableLabel=Табела прерачунавања за дане
#XFLD: Time Data dialog year label
yearLabel=Димензија године
#XFLD: Time Data dialog quarter label
quarterLabel=Димензија квартала
#XFLD: Time Data dialog month label
monthLabel=Димензија месеца
#XFLD: Time Data dialog day label
dayLabel=Димензија дана
#XFLD: Time Data dialog gregorian calendar type label
gregorian=Грегоријански
#XFLD: Time Data dialog time granularity day label
day=Дан
#XFLD: Time Data dialog business name maxlength reached
timeRangeValueStateTextMaxLengthBusinessName=Максимална дужина од 1000 знакова достигнута.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextMaxRangeExceeded=Максимални временски распон је 150 година.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeLower=“Година почетка” мора да буде пре “Године завршетка”
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeHigher="Година почетка" мора да буде 1900. или каснија.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeHigher=“Година завршетка” мора да буде после “Године почетка”
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeLower=“Година завршетка” мора да буде пре текуће године плус 100
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningFromYear=Каснија "Година почетка" може довести до губитка података
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningToYear=Ранија "Година почетка" може довести до губитка података
#XMSG: Time Data creation validation error message
timeDataValidationError=Изгледа да су нека поља неважећа. Проверите обавезна поља да бисте наставили.
#XMSG: Time Data deletion popup text
deleteTimeDataConfirmation=Да ли заиста желите да избришете податке?
#XMSG: Time Data creation success message
createTimeDataSuccess=Подаци о времену креирани
#XMSG: Time Data update success message
updateTimeDataSuccess=Подаци о времену ажурирани
#XMSG: Time Data delete success message
deleteTimeDataSuccess=Подаци о времену избрисани
#XMSG: Time Data creation error message
createTimeDataError=Дошло је до грешке при покушају креирања података о времену.
#XMSG: Time Data update error message
updateTimeDataError=Дошло је до грешке при покушају ажурирања података о времену.
#XMSG: Time Data creation error message
deleteTimeDataError=Дошло је до грешке при покушају брисања података о времену.
#XMSG: Error message when time data could not be retrieved
retrievingTimeDataError=Подаци о времену се не могу учитати.
#XTIT: Time Data deletion popup title
deleteTimeDataConfirmationTitle=Упозорење
#XTXT: Time Data deletion dependency popup text
deleteTimeDataDependencyText=Не можемо избрисати ваше податке о времену јер се користе у другим моделима.
#XTXT: Time Data deletion dependency popup text singular
deleteTimeDataDependencyTextSingular=Не можемо избрисати ваше податке о времену јер се користе у другом моделу.
#XMSG: Validation error when required time data form field is empty
timeDataRequiredField=Ово поље је обавезно и не може се оставити празно.
#XFLD: Tooltip for Open in Database Explorer button
openInDBX=Отворите у претраживачу базе података
#YMSE: Dimension Year
dimensionYearView=Димензија "Година"
#YMSE: Dimension Year
dimensionQuarterView=Димензија "Квартал"
#YMSE: Dimension Year
dimensionMonthView=Димензија "Месец"
#YMSE: Dimension Year
dimensionDayView=Димензија "Дан"
#XFLD: Time Data deletion object title
timeDataUsedIn=(користи се у {0} модела)
#XFLD: Time Data deletion object title singular
timeDataUsedInSingular=(користи се у 1 моделу)
#XFLD: Time Data deletion table column provider
provider=Давалац
#XTIT: Time Data dependency dialog title
timeDataDependencyDialogTitle=Зависности
#XTIT: Create schema popup title
createSpaceSchemaUserDialogTitle=Креирај корисника за шему простора
#XFLD: Create schema button
createSchemaButton=Креирај Open SQL шему
#XFLD: Generate TimeData button
generateTimeDataButton=Креирај распореде и димензије
#XFLD: Show dependencies button
showDependenciesButton=Покажи зависности
#XFLD: tooltip for disabled buttons when user is not member of space
mustBeMemberOfSpaceForOperation=Да би се извршила ова операција, ваш корисник мора бити члан простора.
#XFLD: Create space schema user button
createSpaceSchemaUserButton=Креирај корисник шеме простора
#YMSE: API Schema users load error
loadSchemaUsersError=Листа корисника се не може учитати.
#XTIT: API Schema-config popup title
schemaUserDetailsDialogTitle=Детаљи корисника шеме простора
#XMSG: API Schema user deletion popup text
deleteSpaceSchemaUserConfirmation=Да ли заиста желите да избришете одабрани корисник?
#XMSG: API Schema user deletion confirmation text
userDeleteSuccess=Корисник избрисан.
#YMSE: API Schema user deletion error
userDeleteError=Корисник се не може избрисати.
#XFLD: User deleted
userDeleted=Корисник је избрисан.
#XTIT: Remove user popup title
removeUserConfirmationTitle=Упозорење
#XMSG: Remove user popup text
removeUserConfirmation=Да ли заиста желите да уклоните корисник? Корисник и његове додељене улоге у опсегу ће бити уклоњени из простора.
#XMSG: Remove users popup text
removeUsersConfirmation=Да ли заиста желите да уклоните кориснике? Корисници и њихове додељене улоге у опсегу ће бити уклоњени из простора.
#XBUT: Confirmation button for remove user dialog
removeUserConfirmationBtn=Уклони
#YMSE: No data text for available roles
noDataAvailableRoles=Простор није додат ниједној улози у опсегу. \n Да бисте могли да додајете кориснике у простор, он прво мора бити додат најмање једној улози у опсегу.
#YMSE: No data text for selected roles
noDataSelectedRoles=Нису одабране улоге у опсегу
#XTIT: Schema-config popup title
schemaDetailsDialogTitle=Детаљи конфигурације Open SQL шеме
#XFLD: Label for Read Audit Log
auditLogRead=Активирај протокол ревизије за операције читања
#XFLD: Label for Change Audit Log
auditLogChange=Активирај протокол ревизије за операције промене
#XFLD: Label Audit Log Retention
auditLogRetention=Задржи протоколе за
#XFLD: Label Audit Log Retention Unit
retentionUnit=дана
#YMSE: Error when log retention time is set incorrectly
incorrectRetentionTime=Унесите цео број између {0} и {1}
#XFLD: Label for Cross Space Schema Access
spaceSchemaAccess=Користи податке шеме простора
#XTIT: Confirm deactivation of space schema consumption dialog title
disableSpaceSchemaConsumptionDialogTitle=Заустави коришћење података шеме простора
#XTXT: Confirm deactivation of space schema consumption dialog text
disableSpaceSchemaConsumptionDialogText=Ова Open SQL шема можда користи податке ваше шеме простора. Ако зауставите коришћење, модели засновани на подацима шеме простора можда више неће функционисати.
#XBUT: Confirmation button for the stop consuming space schema data dialog
stopConsuming=Заустави коришћење
#XTXT: Tooltip for data lake icon
dataLakeIconTooltip=Овај простор се користи за приступ језеру података
#XFLD: Tooltip for data lake table entry
dataLakeTableEntryTooltip=Језеро података активирано
#XFLD: used In-Memory linit reached tag
usedRamLimitReachedLabel=Ограничење меморије достигнуто
#XFLD: Space storage Limit reached tag
storageLimitReachedLabel=Ограничење простора за складиштење достигнуто
#XFLD: Minimal Storage Limit Reached tag
minStorageLimitReachedLabel=Минимално ограничење простора за складиштење достигнуто
#XFLD: Space ram tag
ramLimitReachedLabel=Ограничење меморије достигнуто
#XFLD: Minimal In-Memory Limit Reached tag
minRamLimitReachedLabel=Минимално ограничење меморије достигнуто
#XFLD: Popover text after click on space storage limit warning
storageLimitReachedText=Достигли сте ограничење додељеног простора за складиштење од {0}. Доделите још простора за складиштење простору.
#XFLD: System storage tag
systemStorageLimitReachedLabel=Достигнуто ограничење простора за складиштење система
#XMSG: Popover text after click on storage limit warning
systemStorageLimitReachedText=Достигли сте ограничење простора за складиштење система од {0}. Тренутно простору не можете доделити још простора за складиштење.
#XMSG: Schema deletion warning
deleteSchemaConfirmation=Брисање ове Open SQL шеме такође ће трајно избрисати све складиштене објекте и одржаване асоцијације у шеми. Наставити?
#XMSG: Success message after deleting a schema
schemaDeleteSuccess=Шема избрисана
#YMSE: Error while deleting schema.
schemaDeleteError=Шема се не може избрисати.
#XMSG: Success message after update a schema
schemaUpdateSuccess=Шема ажурирана
#YMSE: Error while updating schema.
schemaUpdateError=Шема се не може ажурирати.
#XMSG: Hint when no password is shown in the dialog.
newPasswordHint=Обезбедили смо лозинку за ову шему. Ако сте заборавили своју лозинку или сте је изгубили, можете затражити нову. Не заборавите да копирате или сачувате нову лозинку.
#XMSG: Hint when showing the password in the dialog.
savePasswordHint=Копирајте своју лозинку. Биће вам потребна да успоставите везу са овом шемом. Ако сте заборавили лозинку, можете отворити овај дијалог да бисте је поново поставили.
#XFLD,50: Hint on entering schema name in creation dialog
schemaNameHint=Овај назив се не може пром.након креирања шеме.
#XFLD: Open SQL Schemas section sub headline
schemasSQL=Open SQL
#XFLD: Space schema section sub headline
schemasSpace=Простор
#XFLD: HDI Container section header
HDIContainers=Спремници HDI
#XTXT: Add HDI Containers button tooltip
addHDIContainers=Додај спремнике HDI
#XTXT: Remove HDI Containers button tooltip
removeHDIContainers=Уклони спремнике HDI
#XFLD: Enable HDI Containers feature button
enableHDIContainersButton=Омогући приступ
#YMSE: No data text for HDI Containers table
noDataHDIContainers=Спремници HDI нису додати.
#YMSE: No data text for Timedata section
noDataTimedata=Табеле времена и димензије нису креиране.
#YMSE: No data text during runtime not available for Timedata section
runtimeNotAvailableNoDataTimedata=Није могуће учитати табеле времена и димензије јер база података времена извођења није доступна.
#YMSE: No data text during runtime not available for HDI section
runtimeNotAvailableNoDataHDI=Није могуће учитати спремнике HDI јер база података времена извођења није доступна.
#YMSE: Error while fetching HDI Containers
fetchHDIContainersError=Спремници HDI нису обезбеђени. Покушајте поново касније.
#XFLD Table column header for HDI Container names
HDIContainerName=Назив спремника HDI
#XFLD: Enable HDI Containers feature dialog title
enableHDIContainersDialogTitle=Омогући приступ
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText1New=Можете активирати SAP SQL Data Warehousing у вашем клијенту решења SAP Datasphere за размену података између ваших спремника HDI и ваших простора решења SAP Datasphere без потребе за померањем података.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText2=Да бисте то урадили, креирајте тикет тако што ћете кликнути на дугме у наставку.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText3New=Када се обради тикет, морате креирати један нови спремник HDI или више њих у бази података времена извођења решења SAP Datasphere. Затим дугме + мења дугме Омогући приступ у одељку Спремници HDI за све ваше просторе решења SAP Datasphere и можете додати своје спремнике простору.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText41=Потребно вам је више информација? Идите на %%0. За детаљне информације о томе шта да укључите у пријаву проблема, погледајте %%1.
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkText=SAP Help
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkTextSapNote3057059=SAP белешка 3057059
#XBUT: Open Ticket Button Text
openTicket=Отвори тикет
#XBUT: Add Button Text
add=Додај
#XBUT: Next Button Text
next=Следеће
#XBUT: Edit Button Text
editUsers=Уреди
#XBUT: create user Button Text
createUser=Креирај
#XBUT: Update user Button Text
updateUser=Одабери
#XTIT: Add HDI Containers Dialog Title
addHDIContainersDialogTitle=Додај недодељене спремнике HDI
#XFLD: No data text for add HDI container dialog
addHDIContainersDialogNoDataText=Нисмо нашли недодељене спремнике. \n Спремник који тражите можда је већ додељен простору.
#YMSE: Error while loading assigned HDI Containers
loadAssignedHDIContainersError=Додељени спремници HDI се не могу учитати.
#YMSE: Error while loading assigned HDI Containers
loadUnassignedHDIContainersError=Спремници HDI се не могу учитати.
#XMSG: Success message
succeededToAddHDIContainer=Спремник HDI додат
#XMSG: Success message
succeededToAddHDIContainerPlural=Спремници HDI додати
#XMSG: Success message
succeededToDeleteHDIContainer=Спремник HDI уклоњен
#XMSG: Success message
succeededToDeleteHDIContainerPlural=Спремници HDI уклоњени
#XFLD: Time data section sub headline
timeDataSection=Распореди и димензије
#XFLD: Schemas list entry read audit log enabled indicator
readIndicator=Читај
#XFLD: Schemas list entry change audit log enabled indicator
changeIndicator=Промени
#XFLD: Remote sources section sub headline
allconnections=Додела везе
#XFLD: Remote sources section sub headline
localconnections=Локалне везе
#XFLD: Remote sources section sub headline for open Connectors
openConnectors=SAP Open Connectors
#XFLD: User section sub headline
memberassignment=Додела члана
#XFLD: User assignment section sub headline
userAssignment=Додела корисника
#XFLD: User section Access dropdown Member
member=Члан
#XFLD: User assignment section column name
user=Корисничко име
#XTXT: Selected role count
selectedRoleToolbarText=Одабрано: {0}
#XTIT: Space detail section connections title
detailsSectionConnections=Везе
#XTIT: Space detail section data access title
detailsSectionDataAccess=Приступ шеми
#XTIT: Space detail section time data title
detailsSectionGenerateData=Подаци о времену
#XTIT: Space detail section members title
detailsSectionUsers=Чланови
#XTIT: Space detail section Users title
detailsSectionUsersTitle=Корисници
#XTIT: Out of Storage
insufficientStoragePopoverTitle=Нема простора за складиштење
#XTIT: Storage distribution
storageDistributionPopoverTitle=Искоришћена меморија диска
#XTXT: Out of Storage popover text
insufficientStorageText=Да бисте креирали нови простор, смањите додељени простор за складиштење другог простора или избришите простор који вам више није потребан. Можете повећати свој укупни простор за складиштење система позивањем функције Управљај планом.
#XMSG: Space id length warning
spaceIdLengthWarning=Максимум од {0} знакова прекорачен.
#XMSG: Space name length warning
spaceNameLengthWarning=Максимум од {0} знакова прекорачен.
#XMSG: Space id reserved prefix warning
spaceIdnWarningReservedPrefix=Немојте користити префикс {0} да бисте избегли могуће конфликте.
#YMSE: Error while loading open SQL schemas
loadLocalSchemasError=Open SQL шеме се не могу учитати.
#YMSE: Error while creating open SQL schema
createLocalSchemaError=Open SQL шема се не може креирати.
#YMSE: Error while loading all remote connections
loadRemoteConnectionsError=Није могуће учитати све удаљене везе.
#YMSE: Error while loading space details
loadSpaceDetailsError=Детаљи простора се не могу учитати.
#YMSE: Error while deploying space details
deploySpaceDetailsError=Простор се не може имплементирати.
#YMSE: Error while copying space details
copySpaceDetailsError=Простор се не може копирати.
#YMSE: Error while loading storage data
loadStorageDataError=Подаци складишта се не могу учитати.
#YMSE: Error while loading all users
loadAllUsersError=Није могуће учитати све кориснике.
#YMSE: Failed to reset password
resetPasswordError=Лозинка се не може поново поставити.
#XMSG: Success message after updating space
resetPasswordSuccessMessage=Нова лозинка постављена за шему
#YMSE: DP Agent-name too long
DBAgentNameError=Назив агента дистрибуције података је предуг.
#YMSE: Schema-name not valid.
schemaNameError=Назив шеме је неважећи.
#YMSE: User name not valid.
UserNameError=Корисничко име је неважеће.
#XTIT: Consumption by Storage Type
consumptionStorageTypeText=Коришћење по типу складишта
#XTIT: Consumption by Schema
consumptionSchemaText=Коришћење по шеми
#XTIT: Overall Consumption by schema
overallConsumptionSchemaText=Укупно коришћење табеле по шеми
#XTIT: Overall Consumption by Table Type
overallConsumptionTableTypeText=Укупно коришћење по типу табеле
#XTIT: Tables
tableDetailsText=Детаљи табеле
#XTIT: Table Storage Consumption
tableStorageConsumptionText=Коришћење складишта табеле
#XFLD: Table Type label
tableTypeLabel=Тип табеле
#XFLD: Schema label
schemaLabel=Шема
#XFLD: reset table tooltip
resetTable=Поново постави табелу
#XFLD: In-Memory label in space monitor
inMemoryLabel=Меморија
#XFLD: Disk label in space monitor
diskLabel=Диск
#XFLD: Yes
yesLabel=Да
#XFLD: No
noLabel=Не
#XFLD: switch label: Want the data in this space to be consumable by default?
allowConsumptionByDefault=Да ли желите да се подаци у овом простору стандардно користе?
#XFLD: Business Name
businessNameLabel=Пословни назив
#XFLD: Refresh
refresh=Освежи
#XMSG: No filter results title
noFilterResultsTitle=Изгледа да ваша подешавања филтера не приказују податке.
#XMSG: No filter results message
noFilterResultsMsg=Покушајте да детаљно одредите подешавања филтера, а ако и даље не будете видели податке, креирајте табеле у генератору података. Након што почну да користе складиште, овде ћете моћи да их надгледате.
#XMSG: Space monitoring Hana state red title
spaceMonitorHanaRedTitle=База података времена извођења није доступна.
#XMSG: Space monitoring Hana state red message
spaceMonitorHanaRedMsg=Пошто база података времена извођења није доступна, одређене функције су деактивиране и не можемо да прикажемо информације на овој страници.
#YMSE: Error while creating space schema user
createSpaceSchemaUserError=Корисник шеме простора се не може креирати.
#YMSE: Error User name already exists
userAlreadyExistsError=Корисничко име већ постоји.
#YMSE: Error Authentication failed
authenticationFailedError=Потврда идентитета није успела.
#XMSG: Message strip message when a schema user is locked
userLockedMessageStrip=Корисник је закључан због превише неуспешних пријава. Затражите нову лозинку да бисте откључали корисника.
#XMSG: Success message after reset and unlock schema user
resetUnlockPasswordSuccessMessage=Нова лозинка постављена и корисник откључан
#XMSG: user is locked message
userLockedMessage=Корисник је закључан.
#XCOL: Users table-view column Role
spaceRole=Улога
#XCOL: Users table-view column Scoped Role
spaceScopedRole=Улога у опсегу
#XCOL: Users table-view column Space Admin
spaceAdmin=Администратор простора
#XFLD: User section dropdown value Viewer
viewer=Прегледач
#XFLD: User section dropdown value Modeler
modeler=Моделатор
#XFLD: User section dropdown value Data Integrator
dataIntegrator=Интегратор података
#XFLD: User section dropdown value Space Administrator
spaceAdministrator=Администратор простора
#XMSG: Success message -  Space Role updated successfully.
updateUserSpaceRoleSuccess=Улога простора ажурирана
#XMSG: Error message - Failed to update Space Role.
updateUserSpaceRoleError=Улога простора није успешно ажурирана.
#XFLD:
databaseUserNameSuffix=Суфикс корисничког имена базе података
#XTXT: Space Schema password text
spaceSchemaPasswordText=Да бисте успоставили везу са овом шемом, копирајте своју лозинку. Ако сте заборавили своју лозинку, увек можете затражити нову.
#XFLD: Cloud Platform label in space schema dialog
cloudPlatformLabel=Cloud Platform
#XTXT: Cloud Platform text in space schema dialog
cloudPlatformText=Да бисте конфигурисали приступ преко овог корисничког налога, активирајте коришћење и копирајте податке приступа. Ако само можете да копирате податке приступа без лозинке, лозинку обавезно додајте накнадно.
#XFLD: enable consumption in CloudPlatform label in space schema dialog
enableConsumptionInCloudPlatformLabel=Активирај коришћење на платформи Cloud Platform
#XFLD: Credentials for User-Provided Service label in space schema dialog
cloudPlatformCredentialsLabel=Подаци приступа за услугу коју обезбеђује корисник:
#XFLD: Credentials for User-Provided Service (Without Password) label in space schema dialog
cloudPlatformCredentialsWithoutPasswordLabel=Подаци приступа за услугу коју обезбеђује корисник (без лозинке):
#XFLD: Copy Credentials label in space schema dialog
copyCredentialsLabel=Копирај податке приступа без лозинке
#XFLD: Copy Credentials label in space schema dialog
copyFullCredentialsLabel=Копирај потпуне податке приступа
#XFLD: Copy Password label in space schema dialog
copyPasswordLabel=Копирај лозинку
#XMSG: Credentials copied to clipboard
credentialsCopiedMessage=Подаци приступа копирани у прелазну меморију
#XMSG: Password copied to clipboard
passwordCopiedMessage=Лозинка копирана у прелазну меморију
#XMSG: create database user dialog title
createDatabaseUserDialogTitle=Креирај корисник базе података
#XMSG: Database Users section title
databaseUsers=Корисници базе података
#XMSG: Database User Details dialog title
databaseUserDetailsDialogTitle=Детаљи корисника базе података
#XFLD: database user read audit log
databaseUserAuditLogRead=Активирај протоколе ревизије за операције читања и задржи протоколе за
#XFLD: database user change audit log
databaseUserAuditLogChange=Активирај протоколе ревизије за операције промене и задржи протоколе за
#XMSG: Cloud Platform Access
cloudPlatformAccess=Приступ у платформу Cloud Platform
#XFLD: Enable Consumption in Cloud Platform hint
enableConsumptionCloudPlatformHint=Конфигуришите приступ спремнику HANA Deployment Infrastructure (HDI) преко овог корисника базе података. Да бисте се повезали са спремником HDI, SQL моделирање мора бити укључено
#XFLD: Enable HDI Consumption
enableConsumptionCloudPlatform=Активирај употребу HDI
#XFLD: Enable Consumption hint
enableConsumptionHint=Да ли желите да податке у вашем простору могу да користе други алати и апликације?
#XFLD: Enable Consumption
enableConsumption=Активирај коришћење SQL
#XFLD: Enable Modeling
enableModeling=Активирај моделирање SQL
#XMSG: Privileges for Data Modeling
privilegesModeling=Увоз велике количине података
#XMSG: Privileges for Data Consumption
privilegesConsumption=Коришћење података за екстерне алате
#XFLD: SQL Modeling
sqlModeling=Моделирање SQL
#XFLD: SQL Consumption
sqlConsumption=Коришћење SQL
#XFLD: enabled
enabled=Активирано
#XFLD: disabled
disabled=Деактивирано
#XFLD: Edit Privileges
editPrivileges=Овлашћења за уређивање
#XFLD: Open Database Explorer
openDBX=Отвори претраживач базе података
#XFLD: create database user hint
databaseCreateHint=Имајте у виду да након снимања неће бити могуће поново променити корисничко име.
#XFLD: Internal Schema Name
internalSchemaName=Назив интерне шеме
#YMSE: Failed to load database users
loadDatabaseUserError=Није успело учитавање корисника базе података
#YMSE: Failed to delete database user
deleteDatabaseUsersError=Није успело брисање корисника базе података
#XMSG: Success message after deleting database user
deleteDatabaseUserSuccess=Корисник базе података избрисан
#XMSG: Success message after deleting database users
deleteDatabaseUsersSuccess=Корисници базе података избрисани
#XMSG: Success message after creating database user
createDatabaseUserSuccessMessage=Корисник базе података креиран
#YMSE: Failed to create database user
createDatabaseUserError=Није успело креирање корисника базе података
#XMSG: Success message after updating database user
updateDatabaseUserSuccessMessage=Корисник базе података ажуриран
#YMSE: Failed to update database user
updateDatabaseUserError=Није успело ажурирање корисника базе података
#XFLD: HDI Consumption
hdiConsumption=Коришћење HDI
#XTIT: Space detail section Database Access title
detailsSectionDatabaseAccess=Приступ бази података
#XTXT: Database users consumption hint
databaseUsersConsumptionHint=Поставите своје податке простора да се могу стандардно користити. Модели у генераторима ће аутоматски омогућити коришћење података.
#XFLD: Default Consumption of Space Data:
defaultConsumptionOfSpaceData=Стандардно коришћење података простора:
#XFLD: Database User Name
databaseUserName=Име корисника базе података
#XMSG: Database User creation validation error message
databaseUserValidationError=Изгледа да су нека поља неважећа. Проверите обавезна поља да бисте наставили.
#XMSG: SQL modeling can’t be enabled since this user has been migrated.
migratedDatabaseUserHint=Увоз велике количине података се не може активирати јер је овај корисник мигриран.
#XBUT: Remove Button Text
remove=Уклони
#XBUT: Remove Spaces Button Text
removeSpaces=Уклони просторе
#XBUT: Remove Objects Button Text
removeObjects=Уклони објекте
#XMSG: No members have been added yet.
noMembersAssigned=Чланови још нису додати.
#XMSG: No users have been added yet.
noUsersAssigned=Корисници још нису додати.
#XMSG: No database users have been created or your filter is not showing any data.
noDatabaseUsers=Корисници базе података нису креирани или ваш филтер не приказује податке.
#XMSG: Please enter a user name.
noDatabaseUsername=Унесите корисничко име.
#XMSG: The user name is too long. Please use a shorter one.
databaseUsernameTooLong=Корисничко име је предуго. Користите краће корисничко име.
#XMSG: No privileges have been enabled and this database user will have limited functionality. Do you still want to continue?
noPrivilegesConfirmationMessage=Овлашћења нису активирана и овај корисник базе података ће имати ограничену функционалност. Да ли ипак желите да наставите?
#XMSG: To enable audit logs for change operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogChangeMessage=Да бисте активирали протоколе ревизије за операције промене, потребно је да активирате и увоз велике количине података. Да ли желите да извршите ово?
#XMSG: To enable audit logs for read operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogReadMessage=Да бисте активирали протоколе ревизије за операције читања, потребно је да активирате и увоз велике количине података. Да ли желите да извршите ово?
#XMSG: To enable HDI consumption data ingestion and data consumption needs to be enabled. Do you want to enable all?
enableHDIConsumptionMessage=Да бисте активирали коришћење HDI, потребно је да активирате и увоз велике количине података и коришћење података. Да ли желите да извршите ово?
#XMSG:
databaseUserPasswordText=Да бисте успоставили везу са овим корисником базе података, копирајте своју лозинку. Ако сте заборавили своју лозинку, увек можете затражити нову.
#XTIT: Space detail section members title
detailsSectionMembers=Чланови
#XMSG: New password set
newPasswordSet=Нова лозинка постављена
#XFLD: Data Ingestion
dataIngestion=Увоз велике количине података
#XFLD: Data Consumption
dataConsumption=Коришћење података
#XFLD: Privileges
privileges=Овлашћења
#XFLD: Enable Data ingestion
enableDataIngestion=Активирај увоз велике количине података
#XMSG: Log the read and change operations for data ingestion.
databaseUserAuditHint=Протоколирајте операције читања и промене за увоз велике количине података.
#XMSG: Make your space data available in your HDI containers.
databaseUserHDIHint=Учините податке простора доступним у вашим спремницима HDI.
#XFLD: Enable Data consumption
enableDataConsumption=Активирај коришћење података
#XMSG: Allow other apps or tools to consume your space data.
enableDataConsumptionHint=Дозволите другим апликацијама или алатима да користе ваше податке простора.
#XMSG: To set up access via this schema user copy the credentials to your user-provided service. In case you can only copy the credentials without a password make sure to add the password later.
databaseUserHDIConsumptionHint=Да бисте конфигурисали приступ преко овог корисника базе података, копирајте податке приступа у вашу услугу коју обезбеђује корисник. Ако само можете да копирате податке приступа без лозинке, лозинку обавезно додајте накнадно.
#XFLD: Data Flow Runtime Capacity label in space overview
dataFlowRuntimeCapacity=Капацитет времена извођења тока података ({0}:{1} сати од {2} сати)
#XFLD: Could not load Data Flow Runtime Capacity
dataFlowRuntimeCapacityError=Није могуће учитати капацитет времена извођења тока података
#XMSG: User can grant data consumption to other users.
databaseUserConsumptionWithGrantOptionHint=Корисник може одобрити коришћење података другим корисницима.
#XFLD: Enable data consumption with grant option
enableConsumptionWithGrantOption=Активирај коришћење података помоћу опције одобрења
#XMSG: To enable data consumption with grant option data consumption needs to be enabled. Do you want to enable both?
enableConsumptionWithGrantMessage=Да бисте активирали коришћење података помоћу опције одобрења, потребно је активирати коришћење података. Да ли желите да активирате и једно и друго?
#XFLD: Enable Automated Predictive Library (APL) and Predictive Analysis Library (PAL)
allowUsingScriptServerFunctionality=Активирај Automated Predictive Library (APL) и Predictive Analysis Library (PAL)
#XMSG: User can use SAP HANA Cloud embedded machine learning functions.
allowUsingScriptServerFunctionalityHint=Корисник може да користи функције машинског учења уграђене у SAP HANA Cloud.
#XFLD: Password Policy
passwordPolicy=Смернице за лозинку
#XMSG: Password Policy
passwordPolicyHint=Овде активирајте или деактивирајте конфигурисане смернице за лозинку.
#XFLD: Enable Password Policy
enablePasswordPolicy=Активирај смернице за лозинку
#XMSG: Read Access to the Space Schema
readAccessTitle=Приступ за читање шеме простора
#XMSG: read access hint
readAccessHint=Омогућите кориснику базе података да повеже екстерне алате са шемом простора и чита погледе који су изложени за коришћење.
#XFLD: Space Schema
spaceSchema=Шема простора
#XFLD: Enable Read Access (SQL)
enableReadAccess=Активирај приступ за читање (SQL)
#XMSG: Allow the user to grant read access to other users.
withGrantOptionHint=Омогућите кориснику да другим корисницима одобри приступ за читање.
#XFLD: With Grant Option
withGrantOption=Помоћу опције одобрења
#XMSG: Make your space data available in your HDI containers.
enableHDIConsumptionHint=Учините податке простора доступним у вашим спремницима HDI.
#XFLD: Enable HDI Consumption
enableHDIConsumption=Активирај употребу HDI
#XMSG: Write Access to the User’s Open SQL Schema
writeAccessTitle=Приступ за писање у Open SQL шему корисника
#XMSG: write access hint
writeAccessHint=Омогућите кориснику базе података да повеже екстерне алате са Open SQL шемом корисника ради креирања ентитета података и увоза велике количине података за коришћење у простору.
#XFLD: Open SQL Schema
openSQLSchema=Open SQL шема
#XFLD: Enable Write Access (SQL DDL & DML)
enableWriteAccess=Активирај приступ за писање (SQL, DDL и DML)
#XMSG: audit hint
auditHint=Протоколирајте операције читања и промене у Open SQL шеми.
#XMSG: data consumption hint
dataConsumptionHint=Стандардно изложите све нове погледе у простору за коришћење. Моделатори могу да замене ово подешавање за појединачне погледе помоћу прекидача “Изложи за коришћење” на бочном панелу издавања погледа. Такође можете изабрати формате у којима се излажу погледи.
#XFLD: Expose for Consumption by Default
exposeDataConsumption=Стандардно изложи за коришћење
#XMSG: database users hint consumption hint
databaseUsersHint2New=Креирајте кориснике базе података за повезивање екстерних алата с решењем SAP Datasphere. Поставите овлашћења да бисте омогућили корисницима да читају податке простора и да креирају ентитете података (DDL) и увозе велике количине података (DML) за употребу у простору.
#XFLD: Read
read=Читај
#XFLD: Read (HDI)
readHDI=Читај (HDI)
#XFLD: Write
write=Пиши
#XMSG: HDI Containers Hint
HDIContainersHint2=Омогућите приступ у ваше спремнике SAP HANA Deployment Infrastructure (HDI) у вашем простору. Моделатори могу да користе артефакте HDI као изворе за погледе, а клијенти HDI могу да приступе вашим подацима простора.
#XMSG: Open info dialog
openDatabaseUserInfoDialog=Отвори дијалог са информацијама
#XMSG: Database user is locked. Open info dialog to unlock
openLockedDatabaseUserInfoDialog=Корисник базе података је закључан. Отворите дијалог да бисте га откључали
#XFLD: Table
table=Табела
#XFLD: Space section partner configuration title
partnerConnectionConfigurationSectionTitle=Веза партнера
#XFLD: Space section partner configuration subtitle
partnerConnectionConfigurationSectionSubTitle=Конфигурација везе партнера
#XFLD: Space section partner configuration hint
partnerConnectionConfigurationSectionHint=Дефинишите подекран везе партнера тако што ћете додати iFrame URL и икону. Ова конфигурација је доступна само за овај клијент.
#XFLD: Table Name Field
partnerConnectionConfigurationName=Назив подекрана
#XFLD: Table iFrame URL field
partnerConnectionConfigurationiFrameURL=iFrame URL
#XFLD: Table iFrame Post Message Origin field
partnerConnectionConfigurationiFramePostMessageOrigin=Порекло поруке објављивања iFrame-а
#XFLD: Table icon/image name
partnerConnectionConfigurationIcon=Икона
#XMSG: Table no result found
partnerConnectionConfigurationNotfound=Конфигурације везе партнера нису нађене.
#XMSG: Partner Connection Confgurations are not available
partnerConnectionConfigurationNotAvailable=Конфигурације везе партнера се не могу приказати када је база података времена извођења недоступна.
#XFLD: Dialog Create Partner Connection Configuration
partnerConnectionConfigurationDialogTitleAdd=Креирај конфигурацију везе партнера
#XFLD: Dialog Create Partner Connection Configuration Upload Icon
partnerConnectionConfigurationButtonUloadPartnerIcon=Икона преноса на сервер
#XFLD: Dialog Create Partner Connection Configuration Select
partnerConnectionConfigurationSelectIcon=Одабери (максимална величина 200 KB)
#XFLD: Dialog Create Partner Connection Configuration Enter a name
partnerConnectionConfigurationNamePlaceholder=Пример подекрана партнера
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL
partnerConnectionConfigurationiFrameURLPlaceholder=http://www.primer.com
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL Check
partnerConnectionConfigurationiFramePostMessageOriginPlaceholder=.primer.com
#XFLD: Dialog Create Partner Connection Configuration Browse
partnerConnectionConfigurationBrowseButton=Претражи
#XFLD: Create Partner Connection Configuration Msg
partnerConnectionConfigurationUploadMsg=Конфигурација везе партнера је успешно креирана.
#XMSG: Error while deleting Partner Connection configuration(s)
partnerConnectionConfigurationDeleteError=Грешка при брисању конфигурација везе партнера.
#XMSG: Partner Connection Configuration has been successfully deleted
partnerConnectionConfigurationDeleteSuccess=Конфигурација везе партнера је успешно избрисана.
#XMSG: Error while retrieving Partner Connection Configurations
partnerConnectionConfigurationLoadError=Грешка при позивању конфигурација везе партнера.
#XMSG: Error fileSizeLimitExceeded
partnerConnectionConfigurationfileSizeLimitExceeded=Фајл се не може пренети на сервер јер прекорачује максималну величину од 200 KB.
#XBUT: Create Partner Connection Configuration
partnerConnectionConfigurationButtonCreate=Креирај конфигурацију везе партнера
#XBUT: Delete Partner Connection Configuration
partnerConnectionConfigurationButtonDelete=Избриши конфигурацију везе партнера.
#XMSG: Partner Tile Creation Failure
upsertPartnerTileFailed=Подекран партнера се не може креирати.
#XMSG: Partner Tile Deletion Failure
deletePartnerTileFailed=Подекран партнера се не може избрисати.
#XMSG: HANA cloud connector settings reset failure
resetHANASCCSettingsFailed=Поновно постављање подешавања за SAP HANA Cloud Connector није успело
#XFLD: Workload Class
workloadClass=Класа радног оптерећења
#XFLD: Workload Management
workloadManagement=Управљање радним оптерећењем
#XFLD: Priority
workloadClassPriority=Приоритет
#XMSG:
workloadManagementPriorityHint=Можете поставити одређивање приоритета овог простора при постављању упита за базу података. Унесите вредност од 1 (најнижи приоритет) до 8 (највиши приоритет). У ситуацији када се простори такмиче за доступне нити, простори с вишим приоритетима изводе се пре простора с нижим приоритетима.
#XMSG:
workloadClassPriorityHint=Можете поставити приоритет простора од 0 (најнижи) до 8 (највиши). Наредбе простора с високим приоритетом се извршавају пре наредби осталих простора с нижим приоритетом. Стандардни приоритет је 5. Пошто је вредност 9 резервисана за системске операције, она није доступна за простор.
#XFLD: Statement Limits
workloadclassStatementLimits=Ограничења наредбе
#XFLD: Workload Configuration
workloadConfiguration=Конфигурација радног оптерећења
#XMSG:
workloadClassStatementLimitsHint=Можете одредити максимални број (или проценат) нити и GB меморије које могу трошити наредбе које се истовремено изводе у простору. Можете унети било коју вредност или проценат између 0 (без ограничења) и укупне меморије и броја нити доступних у клијенту. \n\n Ако одредите ограничење нити, имајте у виду да то може негативно утицати на извођење. \n\n Ако одредите ограничење меморије, наредбе које достигну ограничење меморије неће бити извршене.
#XMSG:
workloadClassStatementLimitsDescription=Стандардна конфигурација обезбеђује издашна ограничења ресурса, истовремено спречавајући да било који појединачни простор преоптерети систем.
#XMSG:
workloadClassStatementLimitCustomDescription=Можете поставити ограничење максималног укупног броја нити и меморије које могу трошити наредбе које се истовремено изводе.
#XMSG:
totalStatementThreadLimitHelpText=Постављање ограничења нити на прениску вредност може утицати на перформансе наредбе, док превисоке вредности или 0 могу дозволити да простор искористи све доступне нити система.
#XMSG:
totalStatementMemoryLimitHelpText=Постављање ограничења меморије на прениску вредност може проузроковати проблеме недостатка меморије, док превисоке вредности или 0 могу дозволити да простор искористи сву доступну меморију система.
#XMSG:
totalStatementThreadLimitHelpTextRestricted=Унесите проценат између 1% и 70% (или еквивалентан број) од укупног броја нити доступних у вашем клијенту. Постављање ограничења нити на прениску вредност може утицати на перформансе наредби, док превисоке вредности могу утицати на перформансе наредби у другим просторима.
#XMSG:
totalStatementThreadLimitHelpTextDynamic=Унесите проценат између 1% и {0}% (или еквивалентан број) од укупног броја нити доступних у вашем клијенту. Постављање ограничења нити на прениску вредност може утицати на перформансе наредби, док превисоке вредности могу утицати на перформансе наредби у другим просторима.
#XMSG:
totalStatementMemoryLimitHelpTextNew=Унесите вредност или проценат између 0 (без ограничења) и укупног износа доступне меморије у вашем клијенту. Постављање ограничења меморије на прениску вредност може утицати на перформансе наредби, док превисоке вредности могу утицати на перформансе наредби у другим просторима.
#XFLD: Total Statement Thread Limit
totalStatementThreadLimit=Укупно ограничење нити наредбе
#XFLD: Segmented button label
workloadclassPercentage=%
#XFLD: Segmented button label
workloadclassGigaByte=GB
#XFLD, 80: Segmented button label
workloadclassThreads=Нити
#XFLD: Total Statement Memory Limit
totalStatementMemoryLimit=Укупно ограничење меморије наредбе
#YMSE: Error while loading customer hana info
loadCustomerHanaInfoError=Није успело учитавање корисничких информација базе података SAP HANA.
#XMSG:
minimumLimitReached=Минимално ограничење достигнуто.
#XMSG:
maximumLimitReached=Максимално ограничење достигнуто.
#XMSG: Name Taken for Technical Name
technical-name-taken=Веза с техничким називом који сте унели већ постоји. Унесите други назив.
#XMSG: Name Too long for Technical Name
technical-name-too-long=Технички назив који сте унели прекорачује 40 знакова. Унесите назив с мање знакова.
#XMSG: Technical name field empty
technical-name-field-empty=Унесите технички назив.
#XMSG: Generic error when user enters invalid characters for a field
field-invalid-characters=Овде можете користити само слова (a-z), бројеве (0-9) и доње црте (_) за назив.
#XMSG: Generic error when user enters invalid initial characters for a field
field-underscore-trim=Назив који унесете не сме почињати или се завршавати доњом цртом (_).
#XFLD: Checkbox label for Enable Statement Limits
enableStatementLimits=Активирај ограничења наредбе
#XFLD: Connection SubSection Settings Title in space details
connectionSettings=Подешавања
#XMSG: Connections tool hint in Space details section
connectionsToolHint=Да бисте креирали или уредили везе, отворите апликацију Везе у бочном усмеравању или кликните овде:
#XBUT: Connection list Open Connection Tool Button
button-connectiontool=Иди на Везе
#XFLD: Not deployed label on space tile
notDeployedLabel=Простор још није имплементиран.
#XFLD: Not deployed additional text on space tile
notDeployedText=Имплементирајте простор.
#XFLD: Corrupt space label on space tile
corruptSpace=Дошло је до грешке.
#XFLD: Corrupt space additional text on space tile
corruptSpaceHint=Покушајте да поново имплементирате или се обратите подршци
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Подаци протокола ревизије
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Административни подаци
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Други подаци
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Подаци у просторима
#XMSG: Unlock Space confirmation text
unlockSpaceConfirmation=Да ли заиста желите да откључате простор?
#XMSG: Lock Space confirmation text
lockSpaceConfirmation=Да ли заиста желите да закључате простор?
#XFLD: Lock
lock=Закључај
#XFLD: Unlock
unlock=Откључај
#XFLD: Locking
locking=Закључавање
#XMSG: Success message after locking space
lockSpaceSuccessMsg=Простор закључан
#XMSG: Success message after unlocking space
unlockSpaceSuccessMsg=Простор откључан
#XMSG: Success message after locking spaces
lockSpacesSuccessMsg=Простори закључани
#XMSG: Success message after unlocking spaces
unlockSpacesSuccessMsg=Простори откључани
#YMSE: Error while locking a space
lockSpaceError=Простор се не може закључати.
#YMSE: Error while unlocking a space
unlockSpaceError=Простор се не може откључати.
#XTIT: popup title Warning
confirmationWarningTitle=Упозорење
#XMSG: Message strip message when a space was locked manually
manualLockedMessage=Простор је ручно закључан.
#XMSG: Message strip message when a space was locked due to the amount of generated audit logs
auditLogLockedMessage=Систем је закључао простор јер протоколи ревизије користе велику количину GB на диску.
#XMSG: Message strip message when a space was locked because the assigned quota was exceeded.
quotaLockedMessage=Систем је закључао простор јер прекорачује своје алокације простора у меморији или меморије диска.
#XMSG: Unlock Spaces confirmation text
unlockSpacesConfirmation=Да ли заиста желите да откључате одабране просторе?
#XMSG: Lock Spaces confirmation text
lockSpacesConfirmation=Да ли заиста желите да закључате одабране просторе?
#XTIT: Scoped role editor dialog title
scopedRoleEditorDialogTitle=Уређивач улога у опсегу
#XTIT: ECN Management title
ecnManagementTitle=Управљање просторима и еластичним чворовима израчунавања
#XFLD: ECNs
ecns=Еластични чворови израчунавања
#XFLD: ECN phase Ready
ecnReady=Спремно
#XFLD: ECN phase Running
ecnRunning=Изводи се
#XFLD: ECN phase Initial
ecnInitial=Није спремно
#XFLD: ECN phase Starting
ecnStarting=Покретање
#XFLD: ECN phase Starting Failed
ecnStartingFailed=Покретање није успело
#XFLD: ECN phase Stopping
ecnStopping=Заустављање
#XFLD: ECN phase Stopping Failed
ecnStoppingFailed=Заустављање није успело
#XBTN: Assign Button
assign=Додај просторе
#XBTN: Start Header-Button
start=Покрени
#XBTN: Update Header-Button
repair=Ажурирај
#XBTN: Stop Header-Button
stop=Заустави
#XFLD: ECN hours remaining
ecnHoursRemaining=Преостало 1000 сати
#XFLD: ECN block-hours remaining
ecnBlockHoursRemaining=Преостаје {0} сати блокаде
#XFLD: ECN block-hour remaining
ecnBlockHourRemaining=Преостаје {0} сат блокаде
#XTIT: ECN creation dialog title
ecnCreationDialogTitle=Креирај еластични чвор израчунавања
#XTIT: ECN edit dialog title
ecnEditDialogTitle=Уреди еластични чвор израчунавања
#XTIT: ECN deletion dialog title
ecnDeletionDialogTitle=Избриши еластични чвор израчунавања
#XTIT: ECN Space assignment dialog title
ecnSpaceAssignmentDialogTitle=Додај просторе
#XFLD: ECN ID
ECNIDLabel=Еластични чвор израчунавања
#XTXT: Selected toolbar text
selectedToolbarText=Одабрано: {0}
#XTIT: Elastic Compute Nodes
ECNslong=Еластични чворови израчунавања
#XTIT: Object assignment - Table header: Objects available
objectsAvailable=Број објеката
#XTIT: Object assignment - Dialog header text
selectObjects=Одаберите просторе и објекте које желите да доделите свом еластичном чвору израчунавања:
#XTIT: Object assignment - Table header title: Objects
objects=Објекти
#XTIT: Object assignment - Table header: Type
type=Тип
#XMSG: Message strip delete database user audit warning
deleteDatabaseUserWarningMessageStrip=Имајте у виду да ће брисање корисника базе података довести до брисања свих генерисаних уноса протокола ревизије. Ако желите да задржите протоколе ревизије, размотрите да их извезете пре него што избришете корисника базе података.
#XMSG: Message strip delete HDI container audit logs impact
deleteHDIContainerWarningMessageStrip=Имајте у виду да поништавање доделе спремника HDI из простора ће довести до брисања свих генерисаних уноса протокола ревизије. Ако желите да задржите протоколе ревизије, размотрите да их извезете пре него што поништите доделу спремника HDI.
#XTXT: All audit logs
allAuditLogs=Сви уноси протокола ревизије генерисани за простор
#XMSG: Message strip disable audit warning
disableAuditWarningMessageStrip=Имајте у виду да деактивирање смерница ревизије (операције читања или промене) довешће до брисања свих њихових уноса протокола ревизије. Ако желите да задржите уносе протокола ревизије, размотрите да их извезете пре него што деактивирате смернице ревизије.
#XTIT: No Spaces assigned to ECN Illustration title
noSpacesECNTitle=Простори или објекти још нису додељени
#XTIT: No Spaces assigned to ECN Illustration description
noSpacesECNDescription=Да бисте започели рад са еластичним чвором израчунавања, доделите му простор или објекте.
#XTIT: No Spaces Illustration title
noSpacesTitle=Простор још није креиран
#XTIT: No Spaces Illustration description
noSpacesDescription=Да бисте започели прикупљање података, креирајте простор.
#XTIT: No Spaces Illustration title
noDeletedSpacesTitle=Корпа за отпатке је празна
#XTIT: No Spaces Illustration description
noDeletedSpacesDescription=Одавде можете да вратите избрисане просторе.
#XTXT:  {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDatabaseUsers=Када се простор имплементира, следећи корисници базе података биће {0} избрисани и не могу се обновити:
#XTIT: Delete database users
deleteDatabaseUsersTitle=Избриши кориснике базе података
#YMSE: Error if ecn id already exists
ecnIDInputDuplicateError=ID већ постоји.
#YMSE: Error if ecn id contains invalid characters
ecnIDInputInvalidError=Користите само мала слова a – z и бројеве 0 – 9
#YMSE: Error if ecn id is too short
ecnIDInputTooShortError=ID мора да садржи најмање {0} знакова.
#XMSG: ecn id length warning
ecnIdLengthWarning=Максимум од {0} знакова прекорачен.
#XFLD: open System Monitor
systemMonitor=Монитор система
#XFLD: open ECN schedule dialog menu entry
schedule=Распоред
#XFLD: open create ECN schedule dialog
createSchedule=Креирај распоред
#XFLD: open change ECN schedule dialog
changeSchedule=Уреди распоред
#XFLD: open delete ECN schedule dialog
deleteSchedule=Избриши распоред
#XFLD: open self assign ECN schedule dialog
selfAssignSchedule=Додели ми распоред
#XFLD: open pause ECN schedule dialog
pauseSchedule=Паузирај распоред
#XFLD: open resume ECN schedule dialog
resumeSchedule=Настави распоред
#XFLD: View Logs
viewLogs=Прикажи протоколе
#XFLD: Compute Blocks
computeBlocks=Блокови рачунања
#XFLD: Memory label in ECN creation dialog
ecnMemory=Меморија (GB)
#XFLD: Storage label in ECN creation dialog
ecnStorage=Простор за складиштење (GB)
#XFLD: Number of CPU label in ECN creation dialog
ecnCPUCount=Број CPU
#XFLD: ECN updated by label
changedBy=Променио
#XFLD: ECN updated on label
changedOn=Промењено
#XMSG: Success message after creating a Elastic Compute Node
createEcnSuccessMessage=Еластични чвор израчунавања креиран
#YMSE: Error while creating a Elastic Compute Node
createEcnError=Није могуће креирати еластични чвор израчунавања
#XMSG: Success message after updating a Elastic Compute Node
updateEcnSuccessMessage=Еластични чвор израчунавања ажуриран
#XMSG: Error while updating a Elastic Compute Node
udpateEcnError=Није могуће ажурирати еластични чвор израчунавања
#XMSG: Success message after deleting a Elastic Compute Node
deleteEcnSuccessMessage=Еластични чвор израчунавања избрисан
#YMSE: Error while deleting a Elastic Compute Node
deleteEcnError=Није могуће избрисати еластични чвор израчунавања
#XMSG: Success message after starting a Elastic Compute Node
startingEcnSuccessMessage=Покретање еластичног чвора израчунавања
#XMSG: Success message after stopping a Elastic Compute Node
stoppingEcnSuccessMessage=Заустављање еластичног чвора израчунавања
#YMSE: Error while starting a Elastic Compute Node
startingEcnError=Није могуће покренути еластични чвор израчунавања
#YMSE: Error while stopping a Elastic Compute Node
stoppingEcnError=Није могуће зауставити еластични чвор израчунавања
#XBUT: Add Object button for an ECN
assignObjects=Додај објекте
#XFLD: Enable auto object assignment label
enableAutoObjAssignment=Додај све објекте аутоматски
#XFLD: object type label to be assigned
objectTypeLabel=Тип (семантичка употреба)
#XFLD: assigned object type label
assignedObjectTypeLabel=Тип
#XFLD: technical name label
TechnicalNameLabel=Технички назив
#XFLD: Object selecttion ECN label
objectSelectionECNLabel=Одаберите објекте које желите да додате еластичном чвору израчунавања
#XTIT: Add objects dialog title
assignObjectsTitle=Додели објекте
#XFLD: object label with object count
objectLabel=Објекат
#XMSG: No objects available to add message.
noObjectsToAssign=Нема објеката расположивих за додавање.
#XMSG: No objects assigned message.
noAssignedObjects=Објекти нису додати.
#XTIT: Remove objects popup title
removeObjectsOrSpacesConfirmationTitle=Упозорење
#XTIT: Delete ECN popup button and title
deleteECNConfirmationButtonAndTitle=Избриши
#XMSG: Remove objects popup text
removeObjectsConfirmation=Да ли заиста желите да уклоните одабране објекте?
#XMSG: Remove spaces popup text
removeSpacesConfirmation=Да ли заиста желите да уклоните одабране просторе?
#XBUT: Confirmation button for objects dialog
removeObjectsOrSpacesConfirmationBtn=Уклони просторе
#XMSG: Success message after removing objects
removeObjectsSuccessMessage=Изложени објекти су уклоњени
#XMSG: Success message after assigning objects
assignObjectsSuccessMessage=Изложени објекти су додељени
#XFLD: auto object assignment label for space tile
autoObjAssignmentLabel=Сви изложени објекти
#XFLD: Spaces tab label
spacesTabLabel=Простори
#XFLD: Exposed objects tab label
consumptionObjectsTabLabel=Изложени објекти
#XMSG: Success message after unassigning spaces
spacesUnassignedSuccessMessage=Простори су уклоњени
#XMSG: Success message after unassigning space
spaceUnassignedSuccessMessage=Простор је уклоњен
#YMSE: Error while assigning / unassigning spaces
spacesAssignUnassignError=Простори се не могу доделити ни уклонити.
#YMSE: Error while removing objects
removeObjectsError=Није могуће доделити нити уклонити објекте.
#YMSE: Error while removing object
removeObjectError=Није могуће доделити нити уклонити објекат.
#YMSE: Error when saving the ecn with invalid value
invalidSizingPlanError=Претходно одабрани број више не важи. Одаберите важећи број.
#YMSE: Error when saving the ecn with invalid value
invalidPerformanceClassError=Одаберите важећу класу учинка.
#YMSE: Error when saving the ecn with invalid value
performanceClassNotAvailableError=Претходно одабрана класа учинка "{0}" тренутно није важећа. Одаберите важећу класу учинка.
#XTXT: Delete ECN confirmation text
deleteConfirmationTxtECN=Да ли заиста желите да избришете еластични чвор израчунавања?
#XFLD: tooltip for ? button
help=Помоћ
#XFLD: ECN edit button label
editECN=Конфигуриши
#XFLD: Technical type label for ERModel
DWC_ERMODEL=Модел односа ентитета
#XFLD: Technical type label for Local Table
DWC_LOCAL_TABLE=Локална табела
#XFLD: Technical type label for Remote Table
DWC_REMOTE_TABLE=Удаљена табела
#XFLD: Technical type label for Analytic Model
DWC_ANALYTIC_MODEL=Аналитички модел
#XFLD: Technical type label for Task Chain
DWC_TASKCHAIN=Ланац задатака
#XFLD: Technical type label for Data Flow
DWC_DATAFLOW=Ток података
#XFLD: Technical type label for Replication Flow
DWC_REPLICATIONFLOW=Ток репликације
#XFLD: Technical type label for Transformation Flow
DWC_TRANSFORMATIONFLOW=Ток трансформације
#XFLD: Technical type label for Intelligent Lookup
DWC_IDT=Паметно тражење
#XFLD: Technical type label for Repository
DWC_REPOSITORY_PACKAGE=Репозиторијум
#XFLD: Technical type label for Enterprise Search
DWC_ESMODEL=Enterprise Search
#XFLD: Technical type label for View
DWC_VIEW=Прикажи
#XFLD: Technical type label for Data Product
DWC_DATAPRODUCT=Производ података
#XFLD: Technical type label for Data Access Control
DWC_DAC=Контрола приступа подацима
#XFLD: Technical type label for Folder
DWC_FOLDER=Фолдер
#XFLD: Technical type label for Data Entity
DWC_BUSINESS_ENTITY=Пословни ентитет
#XFLD: Technical type label for Business Entity Variant
DWC_BUSINESS_ENTITY_VARIANT=Варијанта пословног ентитета
#XFLD: Technical type label for Responsibility Scenario
DWC_AUTH_SCENARIO=Сценарио одговорности
#XFLD: Technical type label for Fact Model
DWC_FACT_MODEL=Модел чињеница
#XFLD: Technical type label for Transformation Flow
DWC_PERSPECTIVE=Перспектива
#XFLD: Technical type label for Consumption Model
DWC_CONSUMPTION_MODEL=Модел потрошње
#XFLD: Technical type label for Remote Connection
DWC_REMOTECONNECTION=Удаљена веза
#XFLD: Technical type label for Fact Model Variant
DWC_FACT_MODEL_VARIANT=Варијанта модела чињеница
#XMSG: Schedule created alert message
createScheduleSuccess=План креиран
#XMSG: Schedule updated alert message
updateScheduleSuccess=План ажуриран
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=План избрисан
#XMSG: Schedule takeover success message
ownerChangeScheduleSuccess=Распоред додељен вама
#XMSG: Message for starting pausing of schedule
pauseScheduleStarted=Паузирање 1 плана
#XMSG: Message for starting resuming of schedule
resumeScheduleStarted=Наставак 1 плана
#XFLD: Segmented button label
availableSpacesButton=Доступно
#XFLD: Segmented button label
selectedSpacesButton=Одабрано
#XFLD: Visit website button text
visitWebsite=Посети веб-сајт
#XMSG: Warning message on unselecting source language
sourceLanguageRemovalWarning=Претходно одабрани изворни језик биће уклоњен.
#XBUT: enable translation confirmation button
enableTranslationConfirmationBtn=Активирај
#XFLD: ECN performance class label
performanceClassLabel=Класа учинка
#XTXT performance class memory text
memoryText=Меморија
#XTXT performance class compute text
computeText=Израчунај
#XTXT performance class high-compute text
highComputeText=Израчунавање високог нивоа
#XBUT: Recycle Bin Button Text
recycleBin=Корпа за отпатке
#XBUT: Restore Button Text
restore=Обнови
#XMSG: Warning message for new Workload Management UI
priorityWarning=Ова област је само за читање. Можете користити приоритет простора у области Систем / Конфигурација / Управљање радним оптерећењем.
#XMSG: Warning message for new Workload Management UI
workloadWarning=Ова област је само за читање. Можете користити конфигурацију радног оптерећења простора у области Систем / Конфигурација / Управљање радним оптерећењем.
#XCOL: Space table-view column: Apache Spark vCPUs
sparkManagementVCPUs=Apache Spark vCPU
#XCOL: Space table-view column: Apache Spark Memory (GB)
sparkManagementMemory=Меморија за Apache Spark (GB)
#XCOL: Space type (from Space Capability) for Data Ingestion Spaces
ingestionSpaceType=Увоз велике количине података производа
#XFLD: Deployment in progress nodatatext for spark table
sparkTableDeploymentInProgress=Подаци нису доступни јер се простор тренутно имплементира
#XFLD: Runtime data being loaded nodatatext for spark table
sparkTableDeploymentLoading=Подаци нису доступни јер се простор тренутно учитава
#XCOL: Space type (from Space Capability) for BW Private Cloud Edition
BWPCESpaceType=SAP BW
#XBUT: Go to Instance Mappings Button
navToMappingsButton=Уреди пресликавања инстанце
#XFLD: SAP HANA Data Lake Storage label if less than 1GB
dataLakeStorageUsed=< 1 GB
