#DO NOT REMOVE________________________
#XFLD: Breadcrumb for space monitoring page
spaceMonitorBreadcrumb=Бақылау
#XTXT: Type name for spaces in browser tab page title
space=Кеңістік
#_____________________________________
#XFLD: Spaces label in
spaces=Кеңістіктер
#XFLD: Manage plan button text
manageQuotaButtonText=Жоспарды басқару
#XBUT: Manage resources button
manageResourcesButton=Ресурстарды басқару
#XFLD: Create space button tooltip
createSpace=Кеңістікті жасау
#XFLD: Create
create=Жасау
#XFLD: Deploy
deploy=Қолданысқа енгізу
#XFLD: Page
page=Бет
#XFLD: Cancel
cancel=Бас тарту
#XFLD: Update
update=Жаңарту
#XFLD: Save
save=Сақтау
#XFLD: OK
ok=OK
#XFLD: days
days=Күн
#XFLD: Space tile edit button label
edit=Өңдеу
#XFLD: Auto Assign all objects to space
autoAssign=Автоматты түрде тағайындау
#XFLD: Space tile open monitoring button label
openMonitoring=Бақылау
#XFLD: Delete
delete=Жою
#XFLD: Copy Space
copy=Көшіру
#XFLD: Close
close=Жабу
#XCOL: Space table-view column status
status=Күйі
#XFLD: Space status active
activeLabel=Белсенді
#XFLD: Space status locked
lockedLabel=Құлыпталған
#XFLD: Space status critical
criticalLabel=Критикалық
#XFLD: Space status cold
coldLabel=Суық
#XFLD: Space status deleted
deletedLabel=Жойылды
#XFLD: Space status unknown
unknownLabel=Белгісіз
#XFLD: Space status ok
okLabel=Жұмысқа қабілетті
#XFLD: Database user expired
expired=Мерзімі өткен
#XFLD: deployed
deployed=Қолданысқа енгізілген
#XFLD: not deployed
notDeployed=Қолданысқа енгізілмеген
#XFLD: changes to deploy
changesToDeploy=Қолданысқа енгізілетін өзгерістер
#XFLD: pending
pending=Қолданысқа енгізілуде
#XFLD: designtime error
designtimeError=Құрастыру уақытының қатесі
#XFLD: runtime error
runtimeError=Орындау уақыты қатесі
#XFLD: Space created by label
createdBy=Жасаған
#XFLD: Space created on label
createdOn=Жасалған күні
#XFLD: Space deployed on label
deployedOn=Қолданысқа енгізілген күні
#XFLD: Space ID label
spaceID=Кеңістік ид.
#XFLD: Priority label
priority=Басымдық
#XFLD: Space Priority label
spacePriority=Кеңістік басымдығы
#XFLD: Space Configuration label
spaceConfiguration=Кеңістік конфигурациясы
#XFLD: Not available
notAvailable=Қолжетімді емес
#XFLD: WorkloadType default
default=Әдепкі
#XFLD: WorkloadType custom
custom=Теңшелмелі
#XFLD: Data Lake option label; refers to component SAP HANA Cloud, Data Lake
dataLakeAccess=Data Lake әдісіне қол жеткізу
#XFLD: Translation label
translationLabel=Аударма
#XFLD: Source language label
sourceLanguageLabel=Бастапқы тіл
#XFLD: Translation CheckBox label
translationCheckBox=Аударуды қосу
#XTXT: Popover hint that space has to be deployed to show user details
openDatabaseUserHintPopoverText=Пайдаланушы мәліметтеріне қол жеткізу үшін кеңістікті қолданысқа енгізіңіз.
#XTXT: Popover hint that space has to be deployed to open database explorer
openDatabaseExplorerHintPopoverText=Дерекқор жетектеушісін ашу үшін кеңістікті қолданысқа енгізіңіз.
#XTXT: Text for when the Data Lake check box is disabled
dataLakeOptionDisabled=Осы кеңістікті Data Lake қызметіне қол жеткізу үшін пайдалана алмайсыз, себебі оны басқа кеңістік пайдаланып жатыр.
#XFLD: Data Lake check box text; refers to component SAP HANA Cloud, Data Lake
dataLakeCheckBox=Data Lake қызметіне қол жеткізу үшін осы кеңістікті пайдаланыңыз.
#XFLD: Space Priority minimum label extension
low=Төмен
#XFLD: Space Priority maximum label extension
high=Жоғары
#XFLD: Space name label
spaceName=Кеңістік аты
#XFLD: Enable deploy objects checkbox
enableDeployObjects=Нысандарды қолданысқа енгізу
#XTIT: Copy spaces dialog title
copySpaceDialogTitle={0} көшіру
#XTXT Human readable text for unselected source language
unSelectedSourceLanguage=(Таңдалмаған)
#XTXT Human readable text for language code "af"
af=Африкаанс
#XTXT Human readable text for language code "ar"
ar=Араб
#XTXT Human readable text for language code "bg"
bg=Болгар
#XTXT Human readable text for language code "ca"
ca=Каталан
#XTXT Human readable text for language code "zh"
zh=Жеңілдетілген қытай 
#XTXT Human readable text for language code "zf"
zf=Қытай
#XTXT Human readable text for language code "hr"
hr=Хорват
#XTXT Human readable text for language code "cs"
cs=Чех
#XTXT Human readable text for language code "cy"
cy=Валлий
#XTXT Human readable text for language code "da"
da=Дат
#XTXT Human readable text for language code "nl"
nl=Нидерланд
#XTXT Human readable text for language code "en-UK"
en-UK=Ағылшын (Біріккен Корольдік)
#XTXT Human readable text for language code "en"
en=Ағылшын (Америка Құрама Штаттары)
#XTXT Human readable text for language code "et"
et=Эстон
#XTXT Human readable text for language code "fa"
fa=Парсы
#XTXT Human readable text for language code "fi"
fi=Фин
#XTXT Human readable text for language code "fr-CA"
fr-CA=Француз (Канада)
#XTXT Human readable text for language code "fr"
fr=Француз
#XTXT Human readable text for language code "de"
de=Неміс
#XTXT Human readable text for language code "el"
el=Грек
#XTXT Human readable text for language code "he"
he=Иврит
#XTXT Human readable text for language code "hi"
hi=Хинди
#XTXT Human readable text for language code "hu"
hu=Венгр
#XTXT Human readable text for language code "is"
is=Исланд
#XTXT Human readable text for language code "id"
id=Индонезия тілі
#XTXT Human readable text for language code "it"
it=Итальян
#XTXT Human readable text for language code "ja"
ja=Жапон
#XTXT Human readable text for language code "kk"
kk=Қазақ
#XTXT Human readable text for language code "ko"
ko=Корей
#XTXT Human readable text for language code "lv"
lv=Латыш
#XTXT Human readable text for language code "lt"
lt=Литва
#XTXT Human readable text for language code "ms"
ms=Малай тілі
#XTXT Human readable text for language code "no"
no=Норвег
#XTXT Human readable text for language code "pl"
pl=Поляк
#XTXT Human readable text for language code "pt"
pt=Португал (Бразилия)
#XTXT Human readable text for language code "pt-PT"
pt-PT=Португал (Португалия)
#XTXT Human readable text for language code "ro"
ro=Румын
#XTXT Human readable text for language code "ru"
ru=Орыс
#XTXT Human readable text for language code "sr"
sr=Серб
#XTXT Human readable text for language code "sh"
sh=Серб-Хорват
#XTXT Human readable text for language code "sk"
sk=Словак
#XTXT Human readable text for language code "sl"
sl=Словен
#XTXT Human readable text for language code "es"
es=Испан
#XTXT Human readable text for language code "es-MX"
es-MX=Испан (Мексика)
#XTXT Human readable text for language code "sv"
sv=Швед
#XTXT Human readable text for language code "th"
th=Тай
#XTXT Human readable text for language code "tr"
tr=Түрік
#XTXT Human readable text for language code "uk"
uk=Украин
#XTXT Human readable text for language code "vi"
vi=Вьетнам
#XTIT: Delete spaces confirmation title
deleteConfirmationHeaderText=Кеңістіктерді жою
#XTXT: Logically delete space delete confirmation text with space name
logicallyDeleteConfirmationBodyTextWithSpaceName="{0}" кеңістігін себетке жылжыту қажеттігіне сенімдісіз бе?
#XTXT: Logically dpace delete confirmation text for multiple spaces
logicallyDeleteConfirmationBodyTextMultiple=Таңдалған {0} кеңістікті себетке жылжыту қажеттігіне сенімдісіз бе?
#XTXT: Delete space delete confirmation text with space name
deleteConfirmationBodyTextWithSpaceName="{0}" кеңістігін жою қажеттігіне сенімдісіз бе? Бұл операцияны қайтару мүмкін емес.
#XTXT: Delete space delete confirmation text for multiple spaces
deleteConfirmationBodyTextMultiple=Таңдалған {0} кеңістікті жою қажеттігіне сенімдісіз бе? Бұл операцияны қайтару мүмкін емес. Келесі контент {1} жойылады:
#XTXT: permanently
permanently=біржолата
#XTXT: {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDetail=Келесі контент {0} жойылады және қалпына келтірілмейді:
#XTXT: Hint Delete space confirmation text
deleteConfirmationHint=Жоюды растау үшін {0} деп теріңіз.
#XTXT: Delete space confirmation error check spelling
deleteConfirmationErrorCheckSpelling=Дұрыс жазылғанын тексеріп, қайталап көріңіз.
#XTXT: All Spaces
allSpaces=Барлық кеңістіктер
#XTXT: All data
allData=Кеңістіктегі барлық нысандар мен деректер
#XTXT: All connections
allConnections=Кеңістікте анықталған барлық қосылымдар
#XFLD: Space tile selection box tooltip
clickToSelect=Таңдау үшін басыңыз
#XTXT: All database users
allDatabaseUsers=Кеңістікпен байланыстырылған кез келген Open SQL схемасындағы барлық нысандар мен деректер
#XFLD: remove members button tooltip
deleteUsers=Мүшелерді алып тастау
#XTXT: Space long description text
description=Сипаттама (ең көбі 4000 таңба)
#XFLD: Add Members button tooltip
addUsers=Мүшелер қосу
#XFLD: Add Users button tooltip
addUsersTooltip=Пайдаланушыларды қосу
#XFLD: Edit Users button tooltip
editUsersTooltip=Пайдаланушыларды өңдеу
#XFLD: Remove Users button tooltip
removeUsersTooltip=Пайдаланушыларды жою
#XFLD: Searchfield placeholder
filter=Іздеу
#XCOL: Users table-view column health
health=Жұмысқа қабілетті
#XCOL: Users table-view column access
access=Қол жеткізу
#XFLD: No user found nodatatext
noDataText=Пайдаланушы табылмады
#XTIT: Members dialog title
selectUserDialogTitle=Мүшелер қосу
#XTIT: User dialog title
addUserDialogTitle=Пайдаланушыларды қосу
#XTIT: Delete connections confirmation title
deleteConnectionConfirmationHeaderText=Қосылымдарды жою
#XTIT: Delete single connection confirmation title
deleteConnectionConfirmationHeaderTextSingle=Қосылымды жою
#XTXT: Delete connection delete confirmation text
deleteConnectionConfirmationBodyText=Таңдалған қосылымдарды жою қажеттігіне сенімдісіз бе? Олар біржолата жойылады.
#XTIT: Remote source dialog title
selectRemoteConnectionDialogTitle=Қосылымдарды таңдау
#XTIT: Share connection dialog title
connectionSharingDialogTitle=Қосылымды бөлісу
#XTIT: Connections shared from other spaces table title
sharedConnectionsFromOtherSpaces=Ортақ қосылымдар
#XFLD: Add remote source button tooltip
addRemoteConnections=Қосылымдар қосу
#XFLD: Delete remote source button tooltip
deleteRemoteConnections=Қосылымдарды жою
#XFLD: Share remote source button tooltip
shareConnections=Қосылымдарды бөлісу
#XFLD: Tile-layout tooltip
tileLayout=Плитка пішімі
#XFLD: Table-layout tooltip
tableLayout=Кесте пішімі
#XMSG: Success message after creating space
createSpaceSuccessMessage=Кеңістік жасалды
#XMSG: Success message after copying space
copySpaceSuccessMessage="{0}" кеңістігі "{1}" кеңістігіне көшірілуде
#XMSG: Success message after deploying space
deploymentSuccessMessage=Кеңістікті қолданысқа енгізу басталды
#XMSG: Success message after Apache Spark update started
sparkUpdateStartedSuccessMessage=Apache Spark қызметін жаңарту басталды.
#YMSE: Error while starting Apache Spark update
sparkUpdateStartedErrorMessage=Apache Spark қызметін жаңарту сәтсіз аяқталды.
#XMSG: Success message after updating space
updateSettingsSuccessMessage=Кеңістік мәліметтері жаңартылды
#XMSG: Success message after unlocking space
unlockSpaceSuccessMessage=Кеңістік құлпы уақытша ашылды
#XMSG: Success message after deleting a single space
deleteSpaceSuccessMessage=Кеңістік жойылды
#XMSG: Success message after deleting multiple spaces
deleteSpacesSuccessMessage=Кеңістіктер жойылды
#XMSG: Success message after restoring a single space
restoreSpaceSuccessMessage=Кеңістік қалпына келтірілді
#XMSG: Success message after restoring multiple spaces
restoreSpacesSuccessMessage=Кеңістіктер қалпына келтірілді
#YMSE: Error while updating settings
updateSettingsFailureMessage=Кеңістік параметрлерін жаңарту мүмкін болмады.
#YMSE: Error while updating data lake option
dataLakeEnabledInOtherSpace=Деректер көлі басқа кеңістікке тағайындалды. Тек бір кеңістік деректер көліне бір уақытта қол жеткізе алады.
#YMSE: Error while updating data lake option
virtualTablesExists=Бұл кеңістіктен деректер көлінің тағайындалуын алып тастай алмайсыз, себебі виртуалды кестелерге әлі де тәуелділіктер әлі бар*. Деректер көлінің тағайындалуын осы кеңістіктен алып тастау үшін виртуалды кестелерді жойыңыз.
#YMSE: Error while unlocking space
unlockSpaceFailureMessage=Кеңістік құлпы ашылмады.
#YMSE: Error while creating space
createSpaceError=Кеңістік құлпы жасалмады.
#YMSE: Error while creating duplicate space
createDuplicateSpaceError={0} атауына ие кеңістік бұрыннан бар.
#YMSE: Error while deleting a single space
deleteSpaceError=Кеңістік жойылмады.
#YMSE: Error details while deleting a single space
deleteSpaceDetailedError=“{0}” кеңістігі бұдан былай дұрыс жұмыс істемейді. Оны қайта жойып көріңіз. Егер ол әлі де жұмыс істемесе, әкімшіңізден кеңістікті жоюын немесе билетті ашуын сұраңыз.
#YMSE: Error while deleting a single space in files
deleteSpaceInFilesError=Файлдардағы кеңістік деректері жойылмады.
#YMSE: Error while deleting a single space users
deleteSpaceUsersError=Пайдаланушылар жойылмады.
#YMSE: Error while deleting a single space schema
deleteSpaceSchemasError=Схема жойылмады.
#YMSE: Error while deleting a single space connections
deleteSpaceConnectionsError=Қосылымдарды жою мүмкін болмады.
#YMSE: Error while deleting a single space data
deleteSpaceDataError=Кеңістік деректері жойылмады.
#YMSE: Error while deleting multiple spaces
deleteSpacesError=Кеңістіктер жойылмады.
#YMSE: Error while restoring a single space
restoreSpaceError=Кеңістік қалпына келтірілмеді.
#YMSE: Error while restoring multiple spaces
restoreSpacesError=Кеңістіктер қалпына келтірілмеді.
#YMSE: Error while creating users
createUsersError=Пайдаланушылар қосылмады.
#YMSE: Error while removing users
removeUsersError=Пайдаланушыларды жою мүмкін болмады.
#YMSE: Error while removing user
removeUserError=пайдаланушыны жою мүмкін болмады.
#YMSE: Error assigning roles to a user
roleAssignUnAssignError=Таңдалған ауқымды рөлге пайдаланушыны қоса алмадық. \n\n Өзіңізді ауқымды рөлге қоса алмайсыз. Әкімшіден сізді ауқымды рөлге қосуды сұрауға болады.
#YMSE: Error assigning user to the space
userAssignError=Пайдаланушыны кеңістікке тағайындай алмадық. \n\n Пайдаланушыға ауқымды рөлдердегі кеңістіктердің ең көп рұқсат етілген саны (100) әлдеқашан тағайындалған.
#YMSE: Error assigning users to the space
usersAssignError=Пайдаланушыларды кеңістікке тағайындай алмадық. \n\n Пайдаланушыға ауқымды рөлдердегі кеңістіктердің ең көп рұқсат етілген саны (100) әлдеқашан тағайындалған.
#YMSE: Error while fetching users
fetchUserWithScopedRolesError=Пайдаланушыларды шығарып алу мүмкін болмады. Кейінірек қайталап көріңіз.
#YMSE: Error while fetching scoped roles
fetchScopedRolesError=Ауқымды рөлдерді шығарып алу мүмкін болмады.
#YMSE: Error while fetching members
fetchUserError=Қатысушыларды алу мүмкін болмады. Кейінірек қайталап көріңіз.
#YMSE: Error while loading run-time database
loadRuntimeError=Орындау уақыты дерекқорынан ақпаратты жүктеу мүмкін болмады.
#YMSE: Error while loading spaces
loadSpacesError=Өкінішке орай, кеңістіктерді шығарып алу кезінде белгісіз қате пайда болды.
#YMSE: Error while loading haas resources
loadStorageError=Өкінішке орай, сақтау орны деректерін шығарып алу кезінде белгісіз қате пайда болды.
#YMSE: Error no data could be loaded
loadDataError=Өкінішке орай, деректерді шығарып алу кезінде белгісіз қате пайда болды.
#XFLD: Click to refresh storage data
clickToRefresh=Қайталап көру үшін осы жерді басыңыз.
#XTIT: Delete space popup title
deleteSpacePopupTitle=Кеңістікті жою
#XCOL: Spaces table-view column name
name=Атауы
#XCOL: Spaces table-view deployment status
deploymentStatus=Қолданысқа енгізу күйі
#XFLD: Disk label in space details
storageLabel=Диск (ГБ)
#XFLD: In-Memory label in space details
ramLabel=Жад (ГБ)
#XFLD: Memory label on space card
memory=Сақтау үшін пайдаланылатын жад
#XFLD: Storage Assignment label in space details
storrageAssignmentLabel=Кеңістіктің сақтау орны
#XFLD: Storage Type label in space details
storageTypeLabel=Сақтау орнының түрі
#XFLD: Enable Space Quota
enableSpaceQuota=Кеңістік квотасын іске қосу
#XFLD: No Space Quota
noSpaceQuota=Кеңістік квотасы жоқ
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
diskAndInMemoryText=SAP HANA дерекқоры (диск және жадтағы)
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
dataLakeFilesText=SAP HANA Data Lake файлдары
#XFLD: Available scoped roles label
availableRoles=Қолжетімді ауқымды рөлдер
#XFLD: Selected scoped roles label
selectedRoles=Таңдалған ауқымды рөлдер
#XCOL: Spaces table-view column models
models=Үлгілер
#XCOL: Spaces table-view column users
users=Пайдаланушылар
#XCOL: Spaces table-view column connections
connections=Қосылымдар
#XFLD: Section header overview in space detail
overview=Шолу
#XFLD: Section header Apache Spark in space detail
sparkManagementSection=Apache Spark
#XFLD: Section header Apache Spark in space detail
sparkManagementSubSection=Қолданбалар
#XFLD: Section header Apache Spark in space detail
taskAssignmentSubSection=Тапсырманы тағайындау
#XFLD: vCPU label in Apache Spark section
vCPULabel=Виртуалды орталық процессорлар
#XFLD: Memory label in Apache Spark section
memoryLabel=Жад (ГБ)
#XFLD: space configuration label in Apache Spark section
spaceConfigurationLabel=Кеңістік конфигурациясы
#XFLD: Space Source label
sparkApplicationLabel=Қолданба
#XFLD: Cluster Size label
clusterSizeLabel=Кластер өлшемі
#XFLD: Driver label
driverLabel=Драйвер
#XFLD: Executor label
executorLabel=Орындаушы
#XFLD: max label
maxLabel=Макс. пайдаланылған
#XFLD: TrF Default label
trFDefaultLabel=Түрлендіру ағыны (әдепкі)
#XFLD: Merge Default label
mergeDefaultLabel=Біріктіру (әдепкі)
#XFLD: Optimize Default label
optimizeDefaultLabel=Оңтайландыру (әдепкі)
#XFLD: Deployment Default label
deploymentDefaultLabel=Жергілікті кестені (файл) қолданысқа енгізу
#XFLD: max spark source label
driverExecutorMaxUsedSparkSource={0} ОП / {1} ГБ
#XFLD: Object type label
taskObjectTypeLabel=Нысан түрі
#XFLD: Task activity label
taskActivityLabel=Әрекет
#XFLD: Task Application ID label
taskApplicationIDLabel=Әдепкі қолданба
#XFLD: Section header in space detail
generalSettings=Жалпы параметрлер
#XMSG: Message strip message when a space is locked
spaceLockedMessageStrip=Осы кеңістік жүйе тарапынан құлыпталды.
#XMSG: Message strip message deploy warning
deployWarningMessageStrip=Осы бөлімдегі өзгерістер дереу қолданысқа енгізіледі.
#XMSG: Message strip message modify space configuration warning
modifySpaceConfigurationMessageStrip=Осы мәндерді өзгертсеңіз, бұл өнімділік мәселелерінің туындауына әкелуі мүмкін екенін ескеріңіз.
#XFLD: Button text to unlock the space again
unlockSpace=Кеңістік құлпын ашу
#XFLD: Info text for audit log formatted message
auditLogText=Оқу немесе өзгерту операцияларын (аудит саясаттары) жазу үшін аудит журналдарын қосыңыз. Содан кейін әкімшілер кімнің қай операцияны қай уақытта орындағанын талдай алады.
#XMSG: Message strip  audit warning
auditLogWarningMessageStrip=Аудит журналдары тенанттағы диск жадының үлкен көлемін пайдалануы мүмкін. Аудит саясатын (оқу немесе өзгерту әрекеттері) қоссаңыз, қызмет көрсетудің үзілуіне әкеліп соғуы мүмкін дискідегі толық үзілістерді болдырмау үшін дискіні пайдалануды (жүйе мониторындағы пайдаланылған дискінің жад картасы арқылы) жүйелі түрде бақылап отыруыңыз керек. Аудит саясатын өшірсеңіз, оның барлық аудит журналының енгізілімдері жойылады. Аудит журналының енгізілімдерін сақтағыңыз келсе, аудит саясатын өшірмес бұрын оларды экспорттауды қарастырыңыз.
#XFLD: Message strip link text to open in app help to guide user what to do to unlock his space permanently
unlockSpaceHelp=Анықтаманы көрсету
#XMSG: Message strip message when a space will be locked again
spaceTemporarilyUnlockedMessageStrip=Бұл кеңістік оның жадынан асып кетті және {0} {1} уақыттан кейін құлыпталады.
#XMSG: Unit for remaining time until space is locked again
hours=сағат
#XMSG: Unit for remaining time until space is locked again
minutes=минут
#XFLD: Section header in space detail for auditing
detailsSectionAuditing=Аудит
#XFLD: Subsection header in space detail for auditing
auditing=Кеңістіктің аудит параметрлері
#XFLD: Hot space tooltip
hotSpaceCountTooltip=Критикалық кеңістіктер: сақтау орнының 90%-дан астам бөлігі пайдаланылды.
#XFLD: Green space tooltip
greenSpaceCountTooltip=Жұмысқа қабілетті кеңістіктер: сақтау орнының 6%-90% арасындағы бөлігі пайдаланылды.
#XFLD: Cold space tooltip
coldSpaceCountTooltip=Суық кеңістіктер: сақтау орнының 5% немесе одан аз бөлігі пайдаланылды.
#XFLD: Critical space tooltip
criticalSpaceCountTooltip=Критикалық кеңістіктер: сақтау орнының 90%-дан астам бөлігі пайдаланылды.
#XFLD: Green space tooltip
okSpaceCountTooltip=Жұмысқа қабілетті кеңістіктер: сақтау орнының 6%-90% арасындағы бөлігі пайдаланылды.
#XFLD: Locked space tooltip
lockedSpaceCountTooltipNew=Құлыпталған кеңістіктер: жадтың жеткіліксіз болуына байланысты блокталды.
#XFLD: Locked space tooltip
lockedSpaceCountTooltip=Құлыпталған кеңістіктер
#YMSE: Error while deleting remote source
deleteRemoteError=Қосылымдарды жою мүмкін болмады.
#XFLD: Space ID input tooltip
spaceIDInfoTooltip=Кеңістік идентификаторын кейінірек өзгерту мүмкін емес.\nЖарамды таңбалар: А - Я, 0 - 9, және _
#YMSE: Error when space name is empty
spaceNameEmptyValidationError=Кеңістік атауын енгізіңіз.
#YMSE: Error when ECN Business name is empty
ecnNameEmptyValidationError=Бизнес атауын енгізіңіз.
#YMSE: Error when space id is empty
spaceIDInputEmptyValidationError=Кеңістік идентификаторын енгізіңіз.
#YMSE: Error when space id contains invalid characters
spaceIDInputInValidError=Таңбалар жарамсыз. Тек А - Я, 0 - 9 және _ пайдаланыңыз. 
#YMSE: Error when space id exists while creation
spaceIDInputDuplicateError=Кеңістік идентификаторы бұрыннан бар.
#XFLD: Space searchfield placeholder
search=Іздеу
#XMSG: Success message after creating users
createUsersSuccess=Пайдаланушылар қосылды
#XMSG: Success message after creating user
createUserSuccess=Пайдаланушы қосылды
#XMSG: Success message after updating users
updateUsersSuccess={0} пайдаланушы жаңартылды
#XMSG: Success message after updating user
updateUserSuccess=Пайдаланушы жаңартылды
#XMSG: Success message after removing users
removeUsersSuccess={0} пайдаланушы жойылды
#XMSG: Success message after removing user
removeUserSuccess=Пайдаланушы жойылды
#XFLD: Schema name
schemaName=Схема атауы
#XFLD: used of total
ofTemplate={0}/{1}
#XFLD: Assigned disk label in space overview
assignedStorageTemplate=Тағайындалған диск ({0}/{1})
#XFLD: Assigned Memory label in space overview
assignedRAMTemplate=Тағайындалған жад ({0}/{1})
#XFLD: Storage ratio on space
accelearationRAM=Жадты жылдамдату
#XFLD: No Storage Consumption
noStorageConsumptionText=Ешқандай сақтау орны квотасы тағайындалмады.
#XFLD: Used disk label in space overview
usedStorageTemplate=Сақтау үшін пайдаланылған диск ({0}/{1})
#XFLD: Used Memory label in space overview
usedRAMTemplate=Сақтау үшін пайдаланылған жад ({0}/{1})
#XFLD: Used Disk tooltip
usedStorageTooltipTemplate=Сақтау үшін пайдаланылған диск ({0}/{1})
#XFLD: Used Memory tooltip
usedRAMTooltipTemplate=Пайдаланылған жад ({0}/{1})
#XFLD: Assigned Disk tooltip
assignedStorageTooltipTemplate=Тағайындалған диск ({0}/{1})
#XFLD: Assigned Memory tooltip
assignedRAMTooltipTemplate=Тағайындалған жад ({0}/{1})
#XFLD: Space related used storage tooltip
spaceRelatedUsedStorageTooltipTemplate=Кеңістік деректері: {0}
#XFLD: Not space related used storage tooltip
otherDataUsedStorageTooltipTemplate=Басқа деректер: {0}
#XFLD: Storage used exceeded popover and tooltip
storageUsedExceededText=Жоспарыңызды ұзартып көріңіз немесе SAP қолдау көрсету қызметіне хабарласыңыз.
#XCOL: Space table-view column used Disk
usedStorage=Сақтау үшін пайдаланылған диск
#XCOL: Space monitor column used Memory
usedRAM=Сақтау үшін пайдаланылған жад
#XCOL: Space monitor column Schema
tableSchema=Схема
#XCOL: Space monitor column Storage Type
tableStorageType=Сақтау орнының түрі
#XCOL: Space monitor column Table Type
tableType=Кесте түрі
#XCOL: Space monitor column Record Count
tableRecordCount=Жазба саны
#XFLD: Assigned Disk
assignedStorage=Сақтау үшін тағайындалған диск
#XFLD: Assigned Memory
assignedRAM=Сақтау үшін тағайындалған жад
#XCOL: Space table-view column storage utilization
tableStorageUtilization=Пайдаланылған сақтау орны
#XFLD: space status
spaceStatus=Кеңістік күйі
#XFLD: space type
spaceType=Кеңістік түрі
#XFLD: space type label for space with space type "SAP BW bridge" ("SAP BW bridge" is a product name, do not translate it)
abapbridgeSpaceType=SAP BW Bridge
#XFLD: space type label for space with space type "Data Marketplace"
marketplaceSpaceType=Дерек провайдері өнімі
#XMSG: Error message for when selected space cannot be deleted due to the space type
deleteTypedSpaceError={0} кеңістігін жою мүмкін емес, себебі оның кеңістік түрі — {1}.
#XMSG: Error message for when selected spaces cannot be deleted due to the space type
deleteTypedSpacesError=Таңдалған {0} кеңістікті жоя алмайсыз. Келесідей кеңістік түрлеріне ие кеңістіктерді жою мүмкін емес: {1}.
#XFLD: space type label for space without space type
defaultSpaceType=SAP Datasphere
#XFLD: Tooltip for monitor space button
monitorSpace=Бақылау
#XFLD: Tooltip for edit space button
editSpace=Кеңістікті өңдеу
#XMSG: Deletion warning in messagebox
deleteConfirmation=Осы кеңістікті жою қажеттігін растайсыз ба?
#XFLD: Tooltip for delete space button
deleteSpace=Кеңістікті жою
#XFLD: storage
storage=Сақтауға арналған диск
#XFLD: username
userName=Пайдаланушы аты
#XFLD: port
port=Порт
#XFLD: hostname
hostName=Хост атауы
#XFLD: password
password=Құпиясөз
#XBUT: Request new password button
requestPassword=Жаңа құпиясөзді сұрау
#YEXP: Usage explanation in time data section
timeDataSectionHint=Үлгілерде және журналдарда пайдалану үшін уақыт кестелері мен өлшемдерін жасаңыз.
#YEXP: Usage explanation in Space schema section
spaceSchemaHint=Кеңістігіңіздегі деректер басқа құралдармен немесе қолданбалармен пайдаланылғанын қалайсыз ба? Олай болса, кеңістігіңіздегі деректерге қол жеткізе алатын бір немесе бірнеше пайдаланушыны жасаңыз және барлық болашақ кеңістік деректерінің әдепкі бойынша пайдаланылғанын қалайтыныңызды таңдаңыз.
#XTIT: Create schema popup title
createSchemaDialogTitle=Open SQL схемасын жасау
#XTIT: Generate Time Data dialog title
generateTimeDataDialogTitle=Уақыт кестелері мен өлшемдерін жасау
#XTIT: Edit Time Data dialog title
editTimeDataDialogTitle=Уақыт кестелері мен өлшемдерін өңдеу
#XTIT: Time Data token title
timeDataTokenTitle=Уақыт деректері
#XTIT: Time Data token title
timeDataUpdateViews=Уақыт деректері көріністерін жаңарту
#XFLD: Time Data token creation in progress label
timeDataCreationInProgress=Жасау орындалуда...
#XFLD: Time Data token creation error label
timeDataCreationError=Жасау сәтсіз аяқталды. Қайталап көріңіз.
#XTIT: Time Data dialog time settings title
timeDataDialogTimeSettingsTitle=Уақыт кестесі параметрлері
#XTIT: Time Data dialog time data table title
timeDataDialogTranslationTablesTitle=Аудару кестелері
#XTIT: Time Data dialog dimensions title
timeDataDialogTimeViewsTitle=Уақыт өлшемдері
#XFLD: Time Data dialog time range label
timeRangeHint=Уақыт ауқымын анықтаңыз.
#XFLD: Time Data dialog time data table label
timeDataHint=Кестеңізге атау беріңіз.
#XFLD: Time Data dialog time data dimensions label
timeDimensionsHint=Өлшемдерге атау беріңіз.
#XFLD: Time Data Time range description label
timerangeLabel=Уақыт ауқымы
#XFLD: Time Data dialog from year label
fromYearLabel=Басталу жылы
#XFLD: Time Data dialog to year label
toYearLabel=Аяқталу жыл
#XFLD: Time Data dialog calendar type label
calendarTypeLabel=Күнтізбе түрі
#XFLD: Time Data dialog granularity label
granularityLabel=Бөлінгіштік
#XFLD: Time Data dialog technical name label
technicalNameLabel=Техникалық атау
#XFLD: Time Data dialog quarters text table lable
quarterTextsTableLabel=Тоқсандарға арналған аудару кестесі
#XFLD: Time Data dialog months text table lable
monthsTextsTableLabel=Айларға арналған аудару кестесі
#XFLD: Time Data dialog days text table lable
daysTextsTableLabel=Күндерге арналған аудару кестесі
#XFLD: Time Data dialog year label
yearLabel=Жыл өлшемі
#XFLD: Time Data dialog quarter label
quarterLabel=Тоқсан өлшемі
#XFLD: Time Data dialog month label
monthLabel=Ай өлшемі
#XFLD: Time Data dialog day label
dayLabel=Күн өлшемі
#XFLD: Time Data dialog gregorian calendar type label
gregorian=Григориан
#XFLD: Time Data dialog time granularity day label
day=Күн
#XFLD: Time Data dialog business name maxlength reached
timeRangeValueStateTextMaxLengthBusinessName=Таңбалардың максималды ұзындығына (1000) жетті.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextMaxRangeExceeded=Максималды уақыт ауқымы — 150 жыл.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeLower="Басталу жылы" "Аяқталу жылынан" ерте болуы керек
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeHigher="Басталу жылы" 1900 немесе одан кейінгі жыл болуы керек.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeHigher="Аяқталу жылы" "Басталу жылынан" кеш болуы керек
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeLower="Аяқталу жылы" ағымдағы жылға 100 жыл қосылған уақыттан ерте болуы керек
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningFromYear="Басталу жылы" көрсеткішін арттыру деректердің жоғалуына әкелуі мүмкін
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningToYear="Аяқталу жылы" көрсеткішін төмендету деректердің жоғалуына әкелуі мүмкін
#XMSG: Time Data creation validation error message
timeDataValidationError=Кейбір өрістер жарамсыз сияқты. Жалғастыру үшін міндетті өрістерді тексеріңіз.
#XMSG: Time Data deletion popup text
deleteTimeDataConfirmation=Деректі шынымен жою керек пе?
#XMSG: Time Data creation success message
createTimeDataSuccess=Уақыт деректері жасалды
#XMSG: Time Data update success message
updateTimeDataSuccess=Уақыт деректері жаңартылды
#XMSG: Time Data delete success message
deleteTimeDataSuccess=Уақыт деректері жойылды
#XMSG: Time Data creation error message
createTimeDataError=Уақыт деректерін жасау кезінде белгісіз қате орын алды.
#XMSG: Time Data update error message
updateTimeDataError=Уақыт деректерін жаңарту кезінде белгісіз қате орын алды.
#XMSG: Time Data creation error message
deleteTimeDataError=Уақыт деректерін жою кезінде белгісіз қате орын алды.
#XMSG: Error message when time data could not be retrieved
retrievingTimeDataError=Уақыт деректерін жүктеу мүмкін болмады.
#XTIT: Time Data deletion popup title
deleteTimeDataConfirmationTitle=Ескерту
#XTXT: Time Data deletion dependency popup text
deleteTimeDataDependencyText=Уақыт деректерін жою мүмкін болмады, себебі ол басқа үлгілерде пайдаланылуда.
#XTXT: Time Data deletion dependency popup text singular
deleteTimeDataDependencyTextSingular=Уақыт деректерін жою мүмкін болмады, себебі ол басқа үлгіде пайдаланылуда.
#XMSG: Validation error when required time data form field is empty
timeDataRequiredField=Бұл өрісті толтыру міндетті және оны бос қалдыруға болмайды.
#XFLD: Tooltip for Open in Database Explorer button
openInDBX=Дерекқор жетектеушісінде ашу
#YMSE: Dimension Year
dimensionYearView="Жыл" өлшемі
#YMSE: Dimension Year
dimensionQuarterView="Тоқсан" өлшемі
#YMSE: Dimension Year
dimensionMonthView="Ай" өлшемі
#YMSE: Dimension Year
dimensionDayView="Күн" өлшемі
#XFLD: Time Data deletion object title
timeDataUsedIn=({0} үлгіде пайдаланылған)
#XFLD: Time Data deletion object title singular
timeDataUsedInSingular=(1 үлгіде пайдаланылған)
#XFLD: Time Data deletion table column provider
provider=Провайдер
#XTIT: Time Data dependency dialog title
timeDataDependencyDialogTitle=Тәуелділіктер
#XTIT: Create schema popup title
createSpaceSchemaUserDialogTitle=Кеңістік схемасына арналған пайдаланушыны жасау
#XFLD: Create schema button
createSchemaButton=Open SQL схемасын жасау
#XFLD: Generate TimeData button
generateTimeDataButton=Уақыт кестелері және өлшемдер жасау
#XFLD: Show dependencies button
showDependenciesButton=Тәуелділіктерді көрсету
#XFLD: tooltip for disabled buttons when user is not member of space
mustBeMemberOfSpaceForOperation=Осы операцияны орындау үшін, пайдаланушы кеңістіктің қатысушысы болуы керек.
#XFLD: Create space schema user button
createSpaceSchemaUserButton=Кеңістік схемасы пайдаланушысын жасау
#YMSE: API Schema users load error
loadSchemaUsersError=Пайдаланушылар тізімін жүктеу мүмкін болмады.
#XTIT: API Schema-config popup title
schemaUserDetailsDialogTitle=Кеңістік схемасы пайдаланушысы мәліметтері
#XMSG: API Schema user deletion popup text
deleteSpaceSchemaUserConfirmation=Таңдалған пайдаланушыны шынымен жою керек пе?
#XMSG: API Schema user deletion confirmation text
userDeleteSuccess=Пайдаланушы жойылды.
#YMSE: API Schema user deletion error
userDeleteError=Пайдаланушы жойылмады.
#XFLD: User deleted
userDeleted=Пайдаланушы жойылды.
#XTIT: Remove user popup title
removeUserConfirmationTitle=Ескерту
#XMSG: Remove user popup text
removeUserConfirmation=Пайдаланушыны жою қажеттігін растайсыз ба? Пайдаланушы мен оның тағайындалған ауқымды рөлдері кеңістіктен жойылады.
#XMSG: Remove users popup text
removeUsersConfirmation=Пайдаланушыларды жою қажеттігін растайсыз ба? Пайдаланушылар мен олардың тағайындалған ауқымды рөлдері кеңістіктен жойылады.
#XBUT: Confirmation button for remove user dialog
removeUserConfirmationBtn=Жою
#YMSE: No data text for available roles
noDataAvailableRoles=Кеңістік ауқымды рөлге қосылмайды. \n Пайдаланушыларды кеңістікке қосу үшін, алдымен оны бір немесе бірнеше ауқымды рөлдерге қосу керек.
#YMSE: No data text for selected roles
noDataSelectedRoles=Таңдалған ауқымды рөлдер жоқ
#XTIT: Schema-config popup title
schemaDetailsDialogTitle=Open SQL схемасы конфигурациясы мәліметтері
#XFLD: Label for Read Audit Log
auditLogRead=Оқу операцияларына арналған аудит журналын іске қосу
#XFLD: Label for Change Audit Log
auditLogChange=Өзгерту операцияларына арналған аудит журналын іске қосу
#XFLD: Label Audit Log Retention
auditLogRetention=Журналдарды мынанша уақытқа сақтау:
#XFLD: Label Audit Log Retention Unit
retentionUnit=Күн
#YMSE: Error when log retention time is set incorrectly
incorrectRetentionTime={0} және {1} арасындағы бүтін санды енгізіңіз
#XFLD: Label for Cross Space Schema Access
spaceSchemaAccess=Кеңістік схемасы деректерін пайдалану
#XTIT: Confirm deactivation of space schema consumption dialog title
disableSpaceSchemaConsumptionDialogTitle=Кеңістік схемасы деректерін пайдалануды тоқтату
#XTXT: Confirm deactivation of space schema consumption dialog text
disableSpaceSchemaConsumptionDialogText=Бұл Open SQL схемасы кеңістік схемасының деректерін тұтынуы мүмкін. Егер тұтынуды тоқтатсаңыз, кеңістік схемасы деректеріне негізделген үлгілер бұдан былай жұмыс істемеуі мүмкін.
#XBUT: Confirmation button for the stop consuming space schema data dialog
stopConsuming=Пайдалануды тоқтату
#XTXT: Tooltip for data lake icon
dataLakeIconTooltip=Data Lake қызметіне қол жеткізу үшін осы кеңістік пайдаланылады.
#XFLD: Tooltip for data lake table entry
dataLakeTableEntryTooltip=Data Lake қосулы
#XFLD: used In-Memory linit reached tag
usedRamLimitReachedLabel=Жад шегіне жетті
#XFLD: Space storage Limit reached tag
storageLimitReachedLabel=Сақтау орны шегіне жетті
#XFLD: Minimal Storage Limit Reached tag
minStorageLimitReachedLabel=Минималды сақтау орны шегіне жетті
#XFLD: Space ram tag
ramLimitReachedLabel=Жад шегіне жетті
#XFLD: Minimal In-Memory Limit Reached tag
minRamLimitReachedLabel=Минималды жад шегіне жетті
#XFLD: Popover text after click on space storage limit warning
storageLimitReachedText=Тағайындалған кеңістіктің {0} сақтау орны шегіне жеттіңіз. Кеңістікке қосымша сақтау орнын тағайындаңыз.
#XFLD: System storage tag
systemStorageLimitReachedLabel=Жүйелік сақтау орны шегіне жетті
#XMSG: Popover text after click on storage limit warning
systemStorageLimitReachedText=Жүйенің {0} сақтау орны шегіне жеттіңіз. Кеңістікке қосымша сақтау орнын тағайындай алмайсыз.
#XMSG: Schema deletion warning
deleteSchemaConfirmation=Осы Open SQL схемасын жою әрекеті сонымен қатар схемадағы барлық сақталған нысандар мен сақталған байланыстарды біржолата жояды. Жалғастыру керек пе?
#XMSG: Success message after deleting a schema
schemaDeleteSuccess=Схема жойылды
#YMSE: Error while deleting schema.
schemaDeleteError=Схема жойылмады.
#XMSG: Success message after update a schema
schemaUpdateSuccess=Схема жаңартылды
#YMSE: Error while updating schema.
schemaUpdateError=Схема жаңартылмады.
#XMSG: Hint when no password is shown in the dialog.
newPasswordHint=Біз бұл схемаға құпиясөз бердік. Құпиясөзіңізді ұмытып қалсаңыз немесе оны жоғалтып алсаңыз, жаңасын сұрай аласыз. Жаңа құпиясөзді көшіруді немесе сақтауды ұмытпаңыз.
#XMSG: Hint when showing the password in the dialog.
savePasswordHint=Құпиясөзіңізді көшіріп алыңыз. Бұл схемаға қосылымды орнату үшін қажет болады. Құпиясөзіңізді ұмытып қалсаңыз, оны қалпына келтіру үшін осы диалогтік терезені ашуға болады.
#XFLD,50: Hint on entering schema name in creation dialog
schemaNameHint=Схема жасалғаннан кейін бұл атау өзгертілмейді.
#XFLD: Open SQL Schemas section sub headline
schemasSQL=Open SQL
#XFLD: Space schema section sub headline
schemasSpace=Кеңістік
#XFLD: HDI Container section header
HDIContainers=HDI контейнерлері
#XTXT: Add HDI Containers button tooltip
addHDIContainers=HDI контейнерлерін қосу
#XTXT: Remove HDI Containers button tooltip
removeHDIContainers=HDI контейнерлерін жою
#XFLD: Enable HDI Containers feature button
enableHDIContainersButton=Қол жеткізу рұқсатын іске қосу
#YMSE: No data text for HDI Containers table
noDataHDIContainers=HDI контейнерлері қосылмады.
#YMSE: No data text for Timedata section
noDataTimedata=Уақыт кестелері мен өлшемдері жасалмады.
#YMSE: No data text during runtime not available for Timedata section
runtimeNotAvailableNoDataTimedata=Уақыт кестелері мен өлшемдерін жүктеу мүмкін емес, себебі орындау уақытының дерекқоры қолжетімді емес.
#YMSE: No data text during runtime not available for HDI section
runtimeNotAvailableNoDataHDI=HDI контейнерлерін жүктеу мүмкін емес, себебі орындау уақытының дерекқоры қолжетімді емес.
#YMSE: Error while fetching HDI Containers
fetchHDIContainersError=HDI контейнерлерін алу мүмкін болмады. Кейінірек қайталап көріңіз.
#XFLD Table column header for HDI Container names
HDIContainerName=HDI контейнері атауы
#XFLD: Enable HDI Containers feature dialog title
enableHDIContainersDialogTitle=Қол жеткізу рұқсатын іске қосу
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText1New=HDI контейнерлері мен SAP Datasphere кеңістіктері арасында деректерді жылжыту қажеттілігінсіз деректер алмасу үшін SAP Datasphere тенантында SAP SQL Data Warehousing қызметін іске қосуға болады.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText2=Ол үшін төмендегі түймені басу арқылы қолдау көрсету қызметіне арналған билет ашыңыз.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText3New=Билет өңделгеннен кейін, SAP Datasphere орындалу уақыты дерекқорында бір немесе бірнеше жаңа HDI контейнерлерін жасау керек. Содан кейін, "Қол жеткізу рұқсатын іске қосу" түймесі барлық SAP Datasphere кеңістіктері үшін HDI контейнерлері бөліміндегі + түймешігімен ауыстырылады және контейнерлерді кеңістікке қосуға болады.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText41=Толық ақпарат керек пе? %%0 бөліміне өтіңіз. Билетке не қосу керектігі туралы толық ақпарат алу үшін, %%1 бөлімін қараңыз.
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkText=SAP анықтамасы
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkTextSapNote3057059=SAP 3057059 ескертпесі
#XBUT: Open Ticket Button Text
openTicket=Билет ашу
#XBUT: Add Button Text
add=Қосу
#XBUT: Next Button Text
next=Келесі
#XBUT: Edit Button Text
editUsers=Өңдеу
#XBUT: create user Button Text
createUser=Жасау
#XBUT: Update user Button Text
updateUser=Таңдау
#XTIT: Add HDI Containers Dialog Title
addHDIContainersDialogTitle=Тағайындалмаған HDI контейнерлерін қосу
#XFLD: No data text for add HDI container dialog
addHDIContainersDialogNoDataText=Тағайындалмаған контейнерлерді табу мүмкін болмады. \n Сіз іздеп жатқан контейнер кеңістікке тағайындалып қойған болуы мүмкін.
#YMSE: Error while loading assigned HDI Containers
loadAssignedHDIContainersError=Тағайындалған HDI контейнерлерін жүктеу мүмкін болмады.
#YMSE: Error while loading assigned HDI Containers
loadUnassignedHDIContainersError=HDI контейнерлерін жүктеу мүмкін болмады.
#XMSG: Success message
succeededToAddHDIContainer=HDI контейнері қосылды
#XMSG: Success message
succeededToAddHDIContainerPlural=HDI контейнерлері қосылды
#XMSG: Success message
succeededToDeleteHDIContainer=HDI контейнері жойылды
#XMSG: Success message
succeededToDeleteHDIContainerPlural=HDI контейнерлері жойылды
#XFLD: Time data section sub headline
timeDataSection=Уақыт кестелері мен өлшемдері
#XFLD: Schemas list entry read audit log enabled indicator
readIndicator=Оқу
#XFLD: Schemas list entry change audit log enabled indicator
changeIndicator=Өзгерту
#XFLD: Remote sources section sub headline
allconnections=Қосылымды тағайындау
#XFLD: Remote sources section sub headline
localconnections=Жергілікті қосылымдар
#XFLD: Remote sources section sub headline for open Connectors
openConnectors=SAP Open Connectors
#XFLD: User section sub headline
memberassignment=Қатысушыны тағайындау
#XFLD: User assignment section sub headline
userAssignment=Пайдаланушыны тағайындау
#XFLD: User section Access dropdown Member
member=Мүше
#XFLD: User assignment section column name
user=Пайдаланушы аты
#XTXT: Selected role count
selectedRoleToolbarText=Таңдалды: {0}
#XTIT: Space detail section connections title
detailsSectionConnections=Байланыстар
#XTIT: Space detail section data access title
detailsSectionDataAccess=Схеманы пайдалану рұқсаты
#XTIT: Space detail section time data title
detailsSectionGenerateData=Уақыт деректері
#XTIT: Space detail section members title
detailsSectionUsers=Мүшелер
#XTIT: Space detail section Users title
detailsSectionUsersTitle=Пайдаланушылар
#XTIT: Out of Storage
insufficientStoragePopoverTitle=Сақтау орны бітті
#XTIT: Storage distribution
storageDistributionPopoverTitle=Дисктегі сақтау орны пайдаланылды
#XTXT: Out of Storage popover text
insufficientStorageText=Жаңа кеңістік жасау үшін басқа кеңістіктің тағайындалған жадын азайтыңыз немесе енді қажет емес кеңістікті жойыңыз. Жоспарды басқару функциясы арқылы жалпы жүйелік сақтау орнын көбейтуге болады.
#XMSG: Space id length warning
spaceIdLengthWarning=Максималды таңбалар санынан ({0}) асып кетті.
#XMSG: Space name length warning
spaceNameLengthWarning=Максималды таңбалар санынан ({0}) асып кетті.
#XMSG: Space id reserved prefix warning
spaceIdnWarningReservedPrefix=Ықтимал қайшылықтарды болдырмау үшін {0} префиксін пайдаланбаңыз.
#YMSE: Error while loading open SQL schemas
loadLocalSchemasError=Open SQL схемаларын жүктеу мүмкін болмады.
#YMSE: Error while creating open SQL schema
createLocalSchemaError=Open SQL схемасын жасау мүмкін болмады.
#YMSE: Error while loading all remote connections
loadRemoteConnectionsError=Барлық қашықтағы байланыстарды жүктеу мүмкін болмады.
#YMSE: Error while loading space details
loadSpaceDetailsError=Кеңістік мәліметтерін жүктеу мүмкін болмады.
#YMSE: Error while deploying space details
deploySpaceDetailsError=Кеңістікті қолданысқа енгізу мүмкін болмады.
#YMSE: Error while copying space details
copySpaceDetailsError=Кеңістікті көшіру мүмкін болмады.
#YMSE: Error while loading storage data
loadStorageDataError=Сақтау орны дерегін жүктеу мүмкін болмады.
#YMSE: Error while loading all users
loadAllUsersError=Барлық пайдаланушыны жүктеу мүмкін болмады.
#YMSE: Failed to reset password
resetPasswordError=Құпиясөзді қайта орнату мүмкін болмады.
#XMSG: Success message after updating space
resetPasswordSuccessMessage=Схема үшін жаңа құпиясөз орнатылды
#YMSE: DP Agent-name too long
DBAgentNameError=Дерекпен қамтамасыз ету агентінің аты тым ұзын.
#YMSE: Schema-name not valid.
schemaNameError=Схема атауы жарамсыз.
#YMSE: User name not valid.
UserNameError=Пайдаланушы аты жарамсыз.
#XTIT: Consumption by Storage Type
consumptionStorageTypeText=Сақтау орнының түрі бойынша тұтыну
#XTIT: Consumption by Schema
consumptionSchemaText=Схема бойынша тұтыну
#XTIT: Overall Consumption by schema
overallConsumptionSchemaText=Схема бойынша жалпы кестені тұтыну
#XTIT: Overall Consumption by Table Type
overallConsumptionTableTypeText=Кесте түрі бойынша жалпы тұтыну
#XTIT: Tables
tableDetailsText=Кестенің толық мәліметі
#XTIT: Table Storage Consumption
tableStorageConsumptionText=Кестені сақтау орнының пайдаланылуы
#XFLD: Table Type label
tableTypeLabel=Кесте түрі
#XFLD: Schema label
schemaLabel=Схема
#XFLD: reset table tooltip
resetTable=Кестені қайта орнату
#XFLD: In-Memory label in space monitor
inMemoryLabel=Жад
#XFLD: Disk label in space monitor
diskLabel=Диск
#XFLD: Yes
yesLabel=Иә
#XFLD: No
noLabel=Жоқ
#XFLD: switch label: Want the data in this space to be consumable by default?
allowConsumptionByDefault=Бұл кеңістіктегі деректер әдепкі бойынша тұтынылғанын қалайсыз ба?
#XFLD: Business Name
businessNameLabel=Бизнес атау
#XFLD: Refresh
refresh=Жаңарту
#XMSG: No filter results title
noFilterResultsTitle=Сүзгі параметрлері ешқандай деректерді көрсетпей тұрған сияқты.
#XMSG: No filter results message
noFilterResultsMsg=Сүзгі параметрлерін нақтылап көріңіз және әлі де ешқандай деректерді көрмесеңіз, дерек құрастырғышта кейбір кестелерді жасаңыз. Олар сақтау орнын пайдаланғаннан кейін, оларды осы жерден бақылай аласыз.
#XMSG: Space monitoring Hana state red title
spaceMonitorHanaRedTitle=Орындау уақыты дерекқоры қолжетімсіз.
#XMSG: Space monitoring Hana state red message
spaceMonitorHanaRedMsg=Орындау уақыты дерекқоры қолжетімсіз болғандықтан, белгілі бір функциялар өшірілді және осы бетте ешқандай ақпаратты көрсете алмаймыз.
#YMSE: Error while creating space schema user
createSpaceSchemaUserError=Кеңістік схемасының пайдаланушысы жасалмады.
#YMSE: Error User name already exists
userAlreadyExistsError=Пайдаланушы аты бұрыннан бар.
#YMSE: Error Authentication failed
authenticationFailedError=Аутентификация іске аспады.
#XMSG: Message strip message when a schema user is locked
userLockedMessageStrip=Тым көп сәтсіз кіруге байланысты пайдаланушы құлыпталады. Пайдаланушы құлпын ашу үшін жаңа құпиясөзді сұраңыз.
#XMSG: Success message after reset and unlock schema user
resetUnlockPasswordSuccessMessage=Жаңа құпиясөз орнатылды және пайдаланушы құлпы ашылды
#XMSG: user is locked message
userLockedMessage=Пайдаланушы құлыпталды.
#XCOL: Users table-view column Role
spaceRole=Рөл
#XCOL: Users table-view column Scoped Role
spaceScopedRole=Ауқымды рөл
#XCOL: Users table-view column Space Admin
spaceAdmin=Кеңістік әкімшісі
#XFLD: User section dropdown value Viewer
viewer=Көру құралы
#XFLD: User section dropdown value Modeler
modeler=Үлгілеуші
#XFLD: User section dropdown value Data Integrator
dataIntegrator=Деректерді біріктіру мониторы
#XFLD: User section dropdown value Space Administrator
spaceAdministrator=Кеңістік әкімшісі
#XMSG: Success message -  Space Role updated successfully.
updateUserSpaceRoleSuccess=Кеңістік рөлі жаңартылды
#XMSG: Error message - Failed to update Space Role.
updateUserSpaceRoleError=Кеңістік рөлі сәтті жаңартылмады.
#XFLD:
databaseUserNameSuffix=Дерекқор пайдаланушысы атының суффиксі
#XTXT: Space Schema password text
spaceSchemaPasswordText=Осы схемамен байланысты орнату үшін, құпиясөзіңізді көшіріңіз. Құпиясөзіңізді ұмытып қалсаңыз, жаңасына сұрау жолдай аласыз.
#XFLD: Cloud Platform label in space schema dialog
cloudPlatformLabel=Бұлттық платформа
#XTXT: Cloud Platform text in space schema dialog
cloudPlatformText=Осы пайдаланушы арқылы кіру рұқсатын орнату үшін тұтынуды қосыңыз және тіркелгі деректерін көшіріңіз. Тіркелгі деректерін құпиясөзсіз ғана көшіре алатын болсаңыз, құпиясөзді кейінірек қосқаныңызға көз жеткізіңіз.
#XFLD: enable consumption in CloudPlatform label in space schema dialog
enableConsumptionInCloudPlatformLabel=Бұлттық платформада тұтынуды іске қосу
#XFLD: Credentials for User-Provided Service label in space schema dialog
cloudPlatformCredentialsLabel= Пайдаланушы ұсынған қызметке арналған тіркелгі деректері:
#XFLD: Credentials for User-Provided Service (Without Password) label in space schema dialog
cloudPlatformCredentialsWithoutPasswordLabel= Пайдаланушы ұсынған қызметке арналған тіркелгі деректері (құпиясөзсіз):
#XFLD: Copy Credentials label in space schema dialog
copyCredentialsLabel=Тіркелгі деректерін құпиясөзсіз көшіру
#XFLD: Copy Credentials label in space schema dialog
copyFullCredentialsLabel=Тіркелгі деректерін толық көшіру
#XFLD: Copy Password label in space schema dialog
copyPasswordLabel=Құпиясөзді көшіру
#XMSG: Credentials copied to clipboard
credentialsCopiedMessage=Тіркелгі деректері аралық сақтағышқа көшірілді
#XMSG: Password copied to clipboard
passwordCopiedMessage=Құпиясөз аралық сақтағышқа көшірілді
#XMSG: create database user dialog title
createDatabaseUserDialogTitle=Дерекқор пайдаланушысын жасау
#XMSG: Database Users section title
databaseUsers=Дерекқор пайдаланушылары
#XMSG: Database User Details dialog title
databaseUserDetailsDialogTitle=Дерекқор пайдаланушысы туралы мәліметтер
#XFLD: database user read audit log
databaseUserAuditLogRead=Оқу операцияларына арналған аудит журналдарын іске қосу және журналдарды сақтау:
#XFLD: database user change audit log
databaseUserAuditLogChange=Өзгерту операцияларына арналған аудит журналдарын іске қосу және журналдарды сақтау:
#XMSG: Cloud Platform Access
cloudPlatformAccess=Бұлттық платформаға кіру рұқсаты
#XFLD: Enable Consumption in Cloud Platform hint
enableConsumptionCloudPlatformHint=Осы дерекқор пайдаланушысы арқылы HANA Deployment Infrastructure (HDI) контейнеріне кіру рұқсатын орнатыңыз. HDI контейнеріне қосылу үшін SQL үлгілеу мүмкіндігі қосулы болуы керек
#XFLD: Enable HDI Consumption
enableConsumptionCloudPlatform=HDI тұтыну мүмкіндігін іске қосу
#XFLD: Enable Consumption hint
enableConsumptionHint=Кеңістігіңіздегі деректер басқа құралдармен немесе қолданбалармен тұтынылуын қалайсыз ба?
#XFLD: Enable Consumption
enableConsumption=SQL тұтыну мүмкіндігін іске қосу
#XFLD: Enable Modeling
enableModeling=SQL үлгілеу мүмкіндігін іске қосу
#XMSG: Privileges for Data Modeling
privilegesModeling=Деректі қабылдау
#XMSG: Privileges for Data Consumption
privilegesConsumption=Сыртқы құралдарға арналған деректерді тұтыну
#XFLD: SQL Modeling
sqlModeling=SQL үлгілеу
#XFLD: SQL Consumption
sqlConsumption=SQL тұтыну
#XFLD: enabled
enabled=Қосылған
#XFLD: disabled
disabled=Өшірілген
#XFLD: Edit Privileges
editPrivileges=Артықшылықтарды өңдеу
#XFLD: Open Database Explorer
openDBX=Дерекқор жетектеушісін ашу
#XFLD: create database user hint
databaseCreateHint=Сақтағаннан кейін пайдаланушы атын қайта өзгерту мүмкін болмайтынын ескеріңіз.
#XFLD: Internal Schema Name
internalSchemaName=Ішкі схема атауы
#YMSE: Failed to load database users
loadDatabaseUserError=Дерекқор пайдаланушыларын жүктеу сәтсіз аяқталды
#YMSE: Failed to delete database user
deleteDatabaseUsersError=Дерекқор пайдаланушыларын жою мүмкін болмады
#XMSG: Success message after deleting database user
deleteDatabaseUserSuccess=Дерекқор пайдаланушысы жойылды
#XMSG: Success message after deleting database users
deleteDatabaseUsersSuccess=Дерекқор пайдаланушылары жойылды
#XMSG: Success message after creating database user
createDatabaseUserSuccessMessage=Дерекқор пайдаланушысы жасалды
#YMSE: Failed to create database user
createDatabaseUserError=Дерекқор пайдаланушысын жасау мүмкін болмады
#XMSG: Success message after updating database user
updateDatabaseUserSuccessMessage=Дерекқор пайдаланушысы жаңартылды
#YMSE: Failed to update database user
updateDatabaseUserError=Дерекқор пайдаланушысын жаңарту мүмкін болмады
#XFLD: HDI Consumption
hdiConsumption=HDI тұтыну
#XTIT: Space detail section Database Access title
detailsSectionDatabaseAccess=Дерекқорға кіру рұқсаты
#XTXT: Database users consumption hint
databaseUsersConsumptionHint=Кеңістік деректерін әдепкі бойынша тұтынылатын етіп жасаңыз. Құрастырғыштардағы үлгілер деректерді тұтынуға автоматты түрде мүмкіндік береді.
#XFLD: Default Consumption of Space Data:
defaultConsumptionOfSpaceData=Кеңістік деректерін әдепкі бойынша тұтыну:
#XFLD: Database User Name
databaseUserName=Дерекқор пайдаланушысының аты
#XMSG: Database User creation validation error message
databaseUserValidationError=Кейбір өрістер жарамсыз сияқты. Жалғастыру үшін міндетті өрістерді тексеріңіз.
#XMSG: SQL modeling can’t be enabled since this user has been migrated.
migratedDatabaseUserHint=Осы пайдаланушы тасымалданғандықтан, деректі қабылдау мүмкіндігін іске қосу мүмкін емес.
#XBUT: Remove Button Text
remove=Жою
#XBUT: Remove Spaces Button Text
removeSpaces=Кеңістіктерді жою
#XBUT: Remove Objects Button Text
removeObjects=Нысандарды жою
#XMSG: No members have been added yet.
noMembersAssigned=Мүшелер әлі қосылмады.
#XMSG: No users have been added yet.
noUsersAssigned=Пайдаланушылар әлі қосылмады.
#XMSG: No database users have been created or your filter is not showing any data.
noDatabaseUsers=Дерекқор пайдаланушылары жасалмады немесе сүзгі ешқандай деректерді көрсетпей тұр.
#XMSG: Please enter a user name.
noDatabaseUsername=Пайдаланушы атын енгізіңіз.
#XMSG: The user name is too long. Please use a shorter one.
databaseUsernameTooLong=Пайдаланушы аты тым ұзын. Қысқарақ атты пайдаланыңыз.
#XMSG: No privileges have been enabled and this database user will have limited functionality. Do you still want to continue?
noPrivilegesConfirmationMessage=Артықшылықтар іске қосылмады және осы дерекқор пайдаланушысының функцияларды пайдалану мүмкіндігі шектеулі болады. Әлі де жалғастырғыңыз келе ме?
#XMSG: To enable audit logs for change operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogChangeMessage=Өзгерту операцияларына арналған аудит журналдарын іске қосу үшін, деректі қабылдау мүмкіндігін де іске қосу керек. Осы функцияны іске қосу қажет пе?
#XMSG: To enable audit logs for read operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogReadMessage=Оқу операцияларына арналған аудит журналдарын іске қосу үшін, деректі қабылдау мүмкіндігін де іске қосу керек. Осы функцияны іске қосу қажет пе?
#XMSG: To enable HDI consumption data ingestion and data consumption needs to be enabled. Do you want to enable all?
enableHDIConsumptionMessage=HDI тұтыну мүмкіндігін іске қосу үшін, деректі қабылдау және деректі тұтыну мүмкіндігін де іске қосу қажет. Осы функцияны іске қосу қажет пе?
#XMSG:
databaseUserPasswordText=Осы дерекқор пайдаланушысымен байланысты орнату үшін, құпиясөзіңізді көшіріңіз. Құпиясөзіңізді ұмытып қалсаңыз, жаңасына сұрау жолдай аласыз.
#XTIT: Space detail section members title
detailsSectionMembers=Мүшелер
#XMSG: New password set
newPasswordSet=Жаңа құпиясөз орнатылды
#XFLD: Data Ingestion
dataIngestion=Деректі қабылдау
#XFLD: Data Consumption
dataConsumption=Деректі тұтыну
#XFLD: Privileges
privileges=Артықшылықтар
#XFLD: Enable Data ingestion
enableDataIngestion=Деректі қабылдауды іске қосу
#XMSG: Log the read and change operations for data ingestion.
databaseUserAuditHint=Деректі қабылдау үшін оқу және өзгерту операцияларын тіркеңіз.
#XMSG: Make your space data available in your HDI containers.
databaseUserHDIHint=Кеңістік деректерін HDI контейнерлерінде қолжетімді етіңіз.
#XFLD: Enable Data consumption
enableDataConsumption=Деректі тұтыну мүмкіндігін іске қосу
#XMSG: Allow other apps or tools to consume your space data.
enableDataConsumptionHint=Басқа қолданбаларға немесе құралдарға кеңістік деректеріңізді тұтынуға рұқсат беріңіз.
#XMSG: To set up access via this schema user copy the credentials to your user-provided service. In case you can only copy the credentials without a password make sure to add the password later.
databaseUserHDIConsumptionHint=Осы дерекқор пайдаланушысы арқылы кіру рұқсатын орнату үшін, тіркелгі деректерін пайдаланушы ұсынатын қызметке көшіріңіз. Тіркелгі деректерін құпиясөзсіз ғана көшіре алатын болсаңыз, құпиясөзді кейінірек қосқаныңызға көз жеткізіңіз.
#XFLD: Data Flow Runtime Capacity label in space overview
dataFlowRuntimeCapacity=Деректер ағынының орындалу уақытының сыйымдылығы ({0}:{1} сағат/{2} сағат)
#XFLD: Could not load Data Flow Runtime Capacity
dataFlowRuntimeCapacityError=Деректер ағынының орындалу уақытының сыйымдылығын жүктеу мүмкін болмады
#XMSG: User can grant data consumption to other users.
databaseUserConsumptionWithGrantOptionHint=Пайдаланушы басқа пайдаланушыларға деректерді тұтыну рұқсатын бере алады.
#XFLD: Enable data consumption with grant option
enableConsumptionWithGrantOption=Деректі тұтыну мүмкіндігін рұқсат беру опциясымен іске қосу
#XMSG: To enable data consumption with grant option data consumption needs to be enabled. Do you want to enable both?
enableConsumptionWithGrantMessage=Деректі тұтыну мүмкіндігін рұқсат беру опциясымен іске қосу үшін, деректі тұтыну мүмкіндігін іске қосу қажет. Екеуін де іске қосу қажет пе?
#XFLD: Enable Automated Predictive Library (APL) and Predictive Analysis Library (PAL)
allowUsingScriptServerFunctionality=Enable Automated Predictive Library (APL) және Predictive Analysis Library (PAL) функциясын іске қосу
#XMSG: User can use SAP HANA Cloud embedded machine learning functions.
allowUsingScriptServerFunctionalityHint=Пайдаланушы SAP HANA Cloud ендірілген машиналық оқыту функцияларын пайдалана алады.
#XFLD: Password Policy
passwordPolicy=Құпиясөз саясаты
#XMSG: Password Policy
passwordPolicyHint=Теңшелген құпиясөз саясатын осы жерде іске қосыңыз немесе өшіріңіз.
#XFLD: Enable Password Policy
enablePasswordPolicy=Құпиясөз саясатын іске қосу
#XMSG: Read Access to the Space Schema
readAccessTitle=Кеңістік схемасын оқу рұқсаты
#XMSG: read access hint
readAccessHint=Дерекқор пайдаланушысына сыртқы құралдарды кеңістік схемасына қосуға және тұтынуға арналған көріністерді оқуға рұқсат беріңіз.
#XFLD: Space Schema
spaceSchema=Кеңістік схемасы
#XFLD: Enable Read Access (SQL)
enableReadAccess=Оқу рұқсатын іске қосу (SQL)
#XMSG: Allow the user to grant read access to other users.
withGrantOptionHint=Пайдаланушыға басқа пайдаланушыларға оқу рұқсатын беруге рұқсат беріңіз.
#XFLD: With Grant Option
withGrantOption=Рұқсат беру опциясымен
#XMSG: Make your space data available in your HDI containers.
enableHDIConsumptionHint=Кеңістік деректерін HDI контейнерлерінде қолжетімді етіңіз.
#XFLD: Enable HDI Consumption
enableHDIConsumption=HDI тұтыну мүмкіндігін іске қосу
#XMSG: Write Access to the User’s Open SQL Schema
writeAccessTitle=Пайдаланушының Open SQL схемасына жазу рұқсаты
#XMSG: write access hint
writeAccessHint=Дерек объектілерін жасау және кеңістікте пайдалануға арналған деректерді қабылдау үшін дерекқор пайдаланушысына сыртқы құралдарды пайдаланушының Open SQL схемасына қосуға рұқсат беріңіз.
#XFLD: Open SQL Schema
openSQLSchema=Open SQL схемасы
#XFLD: Enable Write Access (SQL DDL & DML)
enableWriteAccess=Жазу рұқсатын іске қосу (SQL, DDL, және DML)
#XMSG: audit hint
auditHint=Open SQL схемасында оқу және өзгерту операцияларын тіркеңіз.
#XMSG: data consumption hint
dataConsumptionHint=Тұтыну үшін әдепкі кеңістіктегі барлық жаңа көріністерді көрсетіңіз. Үлгілеушілер бұл параметрді жеке көріністер үшін көрініс шығысының бүйірлік панеліндегі "Тұтыну үшін көрсету" қосқышы арқылы қайта анықтай алады. Сондай-ақ, көріністер көрсетілетін пішімдерді таңдауға болады.
#XFLD: Expose for Consumption by Default
exposeDataConsumption=Әдепкі бойынша тұтыну үшін көрсету
#XMSG: database users hint consumption hint
databaseUsersHint2New=Сыртқы құралдарды SAP Datasphere қызметіне қосу үшін дерекқор пайдаланушыларын жасаңыз. Пайдаланушыларға кеңістік деректерін оқуға және кеңістікте пайдалану үшін дерек объектілерін (DDL) жасауға және деректерді қабылдауға (DML) мүмкіндік беру үшін артықшылықтарды орнатыңыз.
#XFLD: Read
read=Оқу
#XFLD: Read (HDI)
readHDI=Оқу (HDI)
#XFLD: Write
write=Жазу
#XMSG: HDI Containers Hint
HDIContainersHint2=Кеңістігіңіздегі SAP HANA Deployment Infrastructure (HDI) контейнерлеріне кіру рұқсатын беріңіз. Үлгілеушілер HDI артефактілерін көріністер үшін дереккөздері ретінде пайдалана алады және HDI клиенттері кеңістік деректеріне қол жеткізе алады.
#XMSG: Open info dialog
openDatabaseUserInfoDialog=Ақпарат диалогтік терезесін ашыңыз
#XMSG: Database user is locked. Open info dialog to unlock
openLockedDatabaseUserInfoDialog=Дерекқор пайдаланушысы құлыптаулы. Құлпын ашу үшін диалогтік терезені ашыңыз
#XFLD: Table
table=Кесте
#XFLD: Space section partner configuration title
partnerConnectionConfigurationSectionTitle=Серіктес қосылымы
#XFLD: Space section partner configuration subtitle
partnerConnectionConfigurationSectionSubTitle=Серіктес қосылымы конфигурациясы
#XFLD: Space section partner configuration hint
partnerConnectionConfigurationSectionHint=iFrame URL мекенжайын және белгішесін қосу арқылы серіктес қосылымы плиткасын анықтаңыз. Бұл конфигурация тек осы тенант үшін қолжетімді.
#XFLD: Table Name Field
partnerConnectionConfigurationName=Плитка атауы
#XFLD: Table iFrame URL field
partnerConnectionConfigurationiFrameURL=iFrame URL мекенжайы
#XFLD: Table iFrame Post Message Origin field
partnerConnectionConfigurationiFramePostMessageOrigin=iFrame жарияланым хабарының шығу тегі
#XFLD: Table icon/image name
partnerConnectionConfigurationIcon=Белгіше
#XMSG: Table no result found
partnerConnectionConfigurationNotfound=Серіктес қосылымы конфигурация(лар)ын табу мүмкін болмады.
#XMSG: Partner Connection Confgurations are not available
partnerConnectionConfigurationNotAvailable=Орындау уақыты дерекқоры қолжетімсіз болғанда, серіктес қосылымы конфигурацияларын көрсету мүмкін емес.
#XFLD: Dialog Create Partner Connection Configuration
partnerConnectionConfigurationDialogTitleAdd=Серіктес қосылымы конфигурациясын жасау
#XFLD: Dialog Create Partner Connection Configuration Upload Icon
partnerConnectionConfigurationButtonUloadPartnerIcon=Жүктеп салу белгішесі
#XFLD: Dialog Create Partner Connection Configuration Select
partnerConnectionConfigurationSelectIcon=Таңдау (максималды өлшемі 200 КБ)
#XFLD: Dialog Create Partner Connection Configuration Enter a name
partnerConnectionConfigurationNamePlaceholder=Серіктес плиткасы мысалы
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL
partnerConnectionConfigurationiFrameURLPlaceholder=http://www.example.com
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL Check
partnerConnectionConfigurationiFramePostMessageOriginPlaceholder=.example.com
#XFLD: Dialog Create Partner Connection Configuration Browse
partnerConnectionConfigurationBrowseButton=Шолу
#XFLD: Create Partner Connection Configuration Msg
partnerConnectionConfigurationUploadMsg=Серіктес қосылымы конфигурациясы сәтті жасалды.
#XMSG: Error while deleting Partner Connection configuration(s)
partnerConnectionConfigurationDeleteError=Серіктес қосылымы конфигурация(лар)ын жою кезінде қате орын алды.
#XMSG: Partner Connection Configuration has been successfully deleted
partnerConnectionConfigurationDeleteSuccess=Серіктес қосылымы конфигурациясы сәтті жойылды.
#XMSG: Error while retrieving Partner Connection Configurations
partnerConnectionConfigurationLoadError=Серіктес қосылымы конфигурация(лар)ын шығарып алу кезінде қате орын алды.
#XMSG: Error fileSizeLimitExceeded
partnerConnectionConfigurationfileSizeLimitExceeded=Файлды жүктеп салу мүмкін болмады, себебі оның көлемі максималды көлемнен (200 КБ) асып кетті.
#XBUT: Create Partner Connection Configuration
partnerConnectionConfigurationButtonCreate=Серіктес қосылымы конфигурациясын жасау
#XBUT: Delete Partner Connection Configuration
partnerConnectionConfigurationButtonDelete=Серіктес қосылымы конфигурациясын жойыңыз.
#XMSG: Partner Tile Creation Failure
upsertPartnerTileFailed=Серіктес плиткасын жасау мүмкін болмады.
#XMSG: Partner Tile Deletion Failure
deletePartnerTileFailed=Серіктес плиткасын жою мүмкін болмады.
#XMSG: HANA cloud connector settings reset failure
resetHANASCCSettingsFailed=Тұтынушыға арналған SAP HANA бұлт коннекторы параметрлерін бастапқы қалпына келтіру мүмкін болмады
#XFLD: Workload Class
workloadClass=Жұмыс жүктемесі класы
#XFLD: Workload Management
workloadManagement=Жұмыс жүктемесін басқару
#XFLD: Priority
workloadClassPriority=Басымдық
#XMSG:
workloadManagementPriorityHint=Дерекқорды сұрау кезінде осы кеңістіктің басымдылығын көрсетуге болады. 1-ден (ең төменгі басымдылық) 8-ге (ең жоғары басымдылық) дейінгі мәнді енгізіңіз. Кеңістіктер қолжетімді тредтер үшін бәсекелесетін жағдайда, басымдықтары жоғарырақ кеңістіктер басымдықтары төмен кеңістіктерден бұрын іске қосылады.
#XMSG:
workloadClassPriorityHint=Кеңістіктің басымдығын 0-ден (ең төменгі) 8-ге (ең жоғары) дейін көрсетуге болады. Басымдылығы жоғары кеңістік операторлары басымдылығы төмен басқа кеңістіктердің операторларының алдында орындалады. Әдепкі басымдық — 5. 9 мәні жүйе операцияларына сақталғандықтан, ол кеңістік үшін қолжетімді емес.
#XFLD: Statement Limits
workloadclassStatementLimits=Оператор шектері
#XFLD: Workload Configuration
workloadConfiguration=Жұмыс жүктемесі конфигурациясы
#XMSG:
workloadClassStatementLimitsHint=Кеңістікте бір уақытта орындалатын операторлар тұтына алатын тредтердің және ГБ жадтың максималды санын (немесе пайызын) көрсетуге болады. 0 (шектеусіз) мен тенантта қолжетімді жалпы жад пен тредтер арасында кез келген мәнді немесе пайызды енгізуге болады. \n\n Егер тред шегін көрсетсеңіз, оның өнімділікті төмендетуі мүмкін екенін ескеріңіз. \n\n Жад шегін көрсетсеңіз, жад шегіне жеткен операторлар іске қосылмайды.
#XMSG:
workloadClassStatementLimitsDescription=Әдепкі конфигурация кез келген жалғыз кеңістіктің жүйеге шамадан тыс жүктелуіне жол бермей, кең ресурстар шектеулерін қамтамасыз етеді.
#XMSG:
workloadClassStatementLimitCustomDescription=Кеңістікте бір уақытта орындалатын мәлімдемелер пайдалана алатын ең үлкен жалпы тредті және жад шектеулерін орнатуға болады.
#XMSG:
totalStatementThreadLimitHelpText=Тред шегін тым төмен орнату оператордың өнімділігіне әсер етуі мүмкін, ал шамадан тыс жоғары мәндер немесе 0 кеңістіктің барлық қолжетімді жүйе тредтерін тұтынуына мүмкіндік береді.
#XMSG:
totalStatementMemoryLimitHelpText=Жад шегін тым төмен орнату жадта ақаулықтарды тудыруы мүмкін, ал шамадан тыс жоғары мәндер немесе 0 кеңістіктің барлық қолжетімді жүйелік жадты тұтынуына мүмкіндік береді.
#XMSG:
totalStatementThreadLimitHelpTextRestricted=Тенантта қолжетімді тредтердің жалпы санының 1%-ы және 70%-ы (немесе балама сан) арасындағы пайызды енгізіңіз. Тред шегін тым төмен орнату мәлімдеме өнімділігіне әсер етуі мүмкін, ал шамадан тыс жоғары мәндер басқа кеңістіктердегі мәлімдемелердің өнімділігіне әсер етуі мүмкін.
#XMSG:
totalStatementThreadLimitHelpTextDynamic=Тенантта қолжетімді тредтердің жалпы санының 1%-ы және {0}%-ы (немесе балама сан) арасындағы пайызды енгізіңіз. Тред шегін тым төмен орнату мәлімдеме өнімділігіне әсер етуі мүмкін, ал шамадан тыс жоғары мәндер басқа кеңістіктердегі мәлімдемелердің өнімділігіне әсер етуі мүмкін.
#XMSG:
totalStatementMemoryLimitHelpTextNew=0 (шектеусіз) мен тенантта қолжетімді жадтың жалпы көлемі арасындағы мәнді немесе пайызды енгізіңіз. Жад шегін тым төмен орнату мәлімдеме өнімділігіне әсер етуі мүмкін, ал шамадан тыс жоғары мәндер басқа кеңістіктердегі мәлімдемелердің өнімділігіне әсер етуі мүмкін.
#XFLD: Total Statement Thread Limit
totalStatementThreadLimit=Жалпы оператор тредінің шегі
#XFLD: Segmented button label
workloadclassPercentage=%
#XFLD: Segmented button label
workloadclassGigaByte=ГБ
#XFLD, 80: Segmented button label
workloadclassThreads=Тредтер
#XFLD: Total Statement Memory Limit
totalStatementMemoryLimit=Жалпы оператор жадының шегі
#YMSE: Error while loading customer hana info
loadCustomerHanaInfoError=Тұтынушыға арналған SAP HANA ақпаратын жүктеу мүмкін болмады.
#XMSG:
minimumLimitReached=Минималды шекке жетті.
#XMSG:
maximumLimitReached=Максималды шекке жетті.
#XMSG: Name Taken for Technical Name
technical-name-taken=Сіз енгізген техникалық атауға ие қосылым бұрыннан бар. Басқа атауды енгізіңіз.
#XMSG: Name Too long for Technical Name
technical-name-too-long=Сіз енгізген техникалық атау 40 таңбадан асады. Таңбалары аз атау енгізіңіз.
#XMSG: Technical name field empty
technical-name-field-empty=Техникалық атауын енгізіңіз.
#XMSG: Generic error when user enters invalid characters for a field
field-invalid-characters=Атау үшін тек әріптерді (а-я), сандарды (0-9) және астыңғы сызықшаны (_) пайдалана аласыз.
#XMSG: Generic error when user enters invalid initial characters for a field
field-underscore-trim=Сіз енгізетін атауды астыңғы сызықшамен (_) бастауға немесе аяқтауға болмайды.
#XFLD: Checkbox label for Enable Statement Limits
enableStatementLimits=Оператор шектерін іске қосу
#XFLD: Connection SubSection Settings Title in space details
connectionSettings=Параметрлер
#XMSG: Connections tool hint in Space details section
connectionsToolHint=Қосылымдарды жасау немесе өңдеу үшін, бүйірлік шарлау панелінен "Қосылымдар" қолданбасын ашыңыз немесе осы жерді басыңыз:
#XBUT: Connection list Open Connection Tool Button
button-connectiontool=Қосылымдарға өту
#XFLD: Not deployed label on space tile
notDeployedLabel=Кеңістік әлі қолданысқа енгізілмеді.
#XFLD: Not deployed additional text on space tile
notDeployedText=Кеңістікті қолданысқа енгізіңіз.
#XFLD: Corrupt space label on space tile
corruptSpace=Бірдеңе дұрыс болмады.
#XFLD: Corrupt space additional text on space tile
corruptSpaceHint=Қолданысқа қайта енгізіп көріңіз немесе қолдау көрсету қызметіне хабарласыңыз
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Аудит журналы деректері
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Әкімшілік дерек
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Басқа деректер
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Кеңістіктердегі деректер
#XMSG: Unlock Space confirmation text
unlockSpaceConfirmation=Кеңістікті құлыптан босату қажеттігіне сенімдісіз бе?
#XMSG: Lock Space confirmation text
lockSpaceConfirmation=Кеңістікті құлыптау қажеттігіне сенімдісіз бе?
#XFLD: Lock
lock=Құлыптау
#XFLD: Unlock
unlock=Құлыптан босату
#XFLD: Locking
locking=Құлыпталуда
#XMSG: Success message after locking space
lockSpaceSuccessMsg=Кеңістік құлыпталды
#XMSG: Success message after unlocking space
unlockSpaceSuccessMsg=Кеңістік құлыптан босатылды
#XMSG: Success message after locking spaces
lockSpacesSuccessMsg=Кеңістіктер құлыпталды
#XMSG: Success message after unlocking spaces
unlockSpacesSuccessMsg=Кеңістіктер құлыптан босатылды
#YMSE: Error while locking a space
lockSpaceError=Кеңістікті құлыптау мүмкін емес.
#YMSE: Error while unlocking a space
unlockSpaceError=Кеңістікті құлыптан босату мүмкін емес.
#XTIT: popup title Warning
confirmationWarningTitle=Ескерту
#XMSG: Message strip message when a space was locked manually
manualLockedMessage=Кеңістік қолмен құлыпталды.
#XMSG: Message strip message when a space was locked due to the amount of generated audit logs
auditLogLockedMessage=Кеңістік жүйе тарапынан құлыпталды, себебі аудит журналдары дискінің ГБ-мен өлшенетін үлкен көлемін тұтынады.
#XMSG: Message strip message when a space was locked because the assigned quota was exceeded.
quotaLockedMessage=Кеңістік жүйе тарапынан құлыпталған, себебі оның көлемі жадтағы немесе дискідегі бөлінген сақтау орны көлемінен асып кетті.
#XMSG: Unlock Spaces confirmation text
unlockSpacesConfirmation=Таңдалған кеңістіктерді құлыптан босату қажеттігіне сенімдісіз бе?
#XMSG: Lock Spaces confirmation text
lockSpacesConfirmation=Таңдалған кеңістіктерді құлыптау қажеттігіне сенімдісіз бе?
#XTIT: Scoped role editor dialog title
scopedRoleEditorDialogTitle=Ауқымды рөл редакторы
#XTIT: ECN Management title
ecnManagementTitle=Кеңістік және серпімді есептеу түйінін басқару
#XFLD: ECNs
ecns=Серпімді есептеу түйіндері
#XFLD: ECN phase Ready
ecnReady=Дайын
#XFLD: ECN phase Running
ecnRunning=Орындалуда
#XFLD: ECN phase Initial
ecnInitial=Дайын емес
#XFLD: ECN phase Starting
ecnStarting=Басталуда
#XFLD: ECN phase Starting Failed
ecnStartingFailed=Бастау сәтсіз аяқталды
#XFLD: ECN phase Stopping
ecnStopping=Тоқтатылуда
#XFLD: ECN phase Stopping Failed
ecnStoppingFailed=Тоқтату сәтсіз аяқталды
#XBTN: Assign Button
assign=Кеңістіктер қосу
#XBTN: Start Header-Button
start=Басталуы
#XBTN: Update Header-Button
repair=Жаңарту
#XBTN: Stop Header-Button
stop=Тоқтату
#XFLD: ECN hours remaining
ecnHoursRemaining=1000 сағат қалды
#XFLD: ECN block-hours remaining
ecnBlockHoursRemaining={0} блок-сағат қалды
#XFLD: ECN block-hour remaining
ecnBlockHourRemaining={0} блок-сағат қалды
#XTIT: ECN creation dialog title
ecnCreationDialogTitle=Серпімді есептеу түйінін жасау
#XTIT: ECN edit dialog title
ecnEditDialogTitle=Серпімді есептеу түйінін өңдеу
#XTIT: ECN deletion dialog title
ecnDeletionDialogTitle=Серпімді есептеу түйінін жою
#XTIT: ECN Space assignment dialog title
ecnSpaceAssignmentDialogTitle=Кеңістіктер қосу
#XFLD: ECN ID
ECNIDLabel=Серпімді есептеу түйіні
#XTXT: Selected toolbar text
selectedToolbarText=Таңдалды: {0}
#XTIT: Elastic Compute Nodes
ECNslong=Серпімді есептеу түйіндері
#XTIT: Object assignment - Table header: Objects available
objectsAvailable=Нысандар саны
#XTIT: Object assignment - Dialog header text
selectObjects=Серпімді есептеу түйініне қосу қажет кеңістіктер мен нысандарды таңдаңыз:
#XTIT: Object assignment - Table header title: Objects
objects=Нысандар
#XTIT: Object assignment - Table header: Type
type=Түрі
#XMSG: Message strip delete database user audit warning
deleteDatabaseUserWarningMessageStrip=Дерекқор пайдаланушысын жою барлық жасалған аудит журналының енгізілімдерінің жойылуына әкелетінін ескеріңіз. Аудит журналдарын сақтағыңыз келсе, дерекқор пайдаланушысын жоймас бұрын оларды экспорттауды қарастырыңыз.
#XMSG: Message strip delete HDI container audit logs impact
deleteHDIContainerWarningMessageStrip=Кеңістіктен тағайындалған HDI контейнерін алып тастау барлық жасалған аудит журналының енгізілімдерінің жойылуына әкелетінін ескеріңіз. Аудит журналдарын сақтағыңыз келсе, HDI контейнерін тағайындаудан бас тартпас бұрын оларды экспорттауды қарастырыңыз.
#XTXT: All audit logs
allAuditLogs=Кеңістік үшін құрылған барлық аудит енгізілімдері
#XMSG: Message strip disable audit warning
disableAuditWarningMessageStrip=Аудит саясатын өшіру (оқу немесе өзгерту операциялары) оның барлық аудит журналының енгізілімдерінің жойылуына әкелетінін ескеріңіз. Аудит журналының енгізілімдерін сақтағыңыз келсе, аудит саясатын өшірмес бұрын оларды экспорттаңыз.
#XTIT: No Spaces assigned to ECN Illustration title
noSpacesECNTitle=Әлі ешқандай кеңістіктер немесе нысандар қосылмады
#XTIT: No Spaces assigned to ECN Illustration description
noSpacesECNDescription=Серпімді есептеу түйінімен жұмыс істеуді бастау үшін, оған кеңістік немесе нысандар қосыңыз,
#XTIT: No Spaces Illustration title
noSpacesTitle=Әлі ешқандай кеңістік жасалмады
#XTIT: No Spaces Illustration description
noSpacesDescription=Деректерді алуды бастау үшін кеңістік жасаңыз.
#XTIT: No Spaces Illustration title
noDeletedSpacesTitle=Себет бос
#XTIT: No Spaces Illustration description
noDeletedSpacesDescription=Жойылған кеңістіктерді осы жерден қалпына келтіре аласыз.
#XTXT:  {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDatabaseUsers=Кеңістік қолданысқа енгізілген кезде, келесі дерекқор пайдаланушылары {0} жойылады және оларды қалпына келтіру мүмкін болмайды:
#XTIT: Delete database users
deleteDatabaseUsersTitle=Дерекқор пайдаланушыларын жою
#YMSE: Error if ecn id already exists
ecnIDInputDuplicateError=Ид. бұрыннан бар.
#YMSE: Error if ecn id contains invalid characters
ecnIDInputInvalidError=Кіші әріптерді (а - я) және сандарды (0 - 9) ғана пайдаланыңыз
#YMSE: Error if ecn id is too short
ecnIDInputTooShortError=Ид. ұзындығы кемінде {0} таңбадан тұруы керек.
#XMSG: ecn id length warning
ecnIdLengthWarning=Максималды таңбалар санынан ({0}) асып кетті.
#XFLD: open System Monitor
systemMonitor=Жүйе мониторы
#XFLD: open ECN schedule dialog menu entry
schedule=Жоспарлау
#XFLD: open create ECN schedule dialog
createSchedule=Кесте жасау
#XFLD: open change ECN schedule dialog
changeSchedule=Кестені өңдеу
#XFLD: open delete ECN schedule dialog
deleteSchedule=Кестені жою
#XFLD: open self assign ECN schedule dialog
selfAssignSchedule=Кестені маған тағайындау
#XFLD: open pause ECN schedule dialog
pauseSchedule=Кестені кідірту
#XFLD: open resume ECN schedule dialog
resumeSchedule=Кестені жалғастыру
#XFLD: View Logs
viewLogs=Журналдарды қарау
#XFLD: Compute Blocks
computeBlocks=Есептеу блоктары
#XFLD: Memory label in ECN creation dialog
ecnMemory=Жад (ГБ)
#XFLD: Storage label in ECN creation dialog
ecnStorage=Сақтау орны (ГБ)
#XFLD: Number of CPU label in ECN creation dialog
ecnCPUCount=Орталық процессорлар саны
#XFLD: ECN updated by label
changedBy=Өзгерткен
#XFLD: ECN updated on label
changedOn=Өзгертілген күні
#XMSG: Success message after creating a Elastic Compute Node
createEcnSuccessMessage=Серпімді есептеу түйіні жасалды
#YMSE: Error while creating a Elastic Compute Node
createEcnError=Серпімді есептеу түйіні жасалмады
#XMSG: Success message after updating a Elastic Compute Node
updateEcnSuccessMessage=Серпімді есептеу түйіні жаңартылды
#XMSG: Error while updating a Elastic Compute Node
udpateEcnError=Серпімді есептеу түйіні жаңартылмады
#XMSG: Success message after deleting a Elastic Compute Node
deleteEcnSuccessMessage=Серпімді есептеу түйіні жойылды
#YMSE: Error while deleting a Elastic Compute Node
deleteEcnError=Серпімді есептеу түйіні жойылмады
#XMSG: Success message after starting a Elastic Compute Node
startingEcnSuccessMessage=Серпімді есептеу түйіні іске қосылуда
#XMSG: Success message after stopping a Elastic Compute Node
stoppingEcnSuccessMessage=Серпімді есептеу түйіні тоқтатылуда
#YMSE: Error while starting a Elastic Compute Node
startingEcnError=Серпімді есептеу түйінін іске қосу мүмкін болмады
#YMSE: Error while stopping a Elastic Compute Node
stoppingEcnError=Серпімді есептеу түйінін тоқтату мүмкін болмады
#XBUT: Add Object button for an ECN
assignObjects=Нысандар қосу
#XFLD: Enable auto object assignment label
enableAutoObjAssignment=Барлық нысандарды автоматты түрде қосу
#XFLD: object type label to be assigned
objectTypeLabel=Түрі (Семантикалық қолданыс)
#XFLD: assigned object type label
assignedObjectTypeLabel=Түрі
#XFLD: technical name label
TechnicalNameLabel=Техникалық атау
#XFLD: Object selecttion ECN label
objectSelectionECNLabel=Серпімді есептеу түйініне қосу қажет нысандарды таңдаңыз
#XTIT: Add objects dialog title
assignObjectsTitle=Нысандар қосу:
#XFLD: object label with object count
objectLabel=Нысан
#XMSG: No objects available to add message.
noObjectsToAssign=Қосу үшін нысандар жоқ.
#XMSG: No objects assigned message.
noAssignedObjects=Нысандар қосылмады.
#XTIT: Remove objects popup title
removeObjectsOrSpacesConfirmationTitle=Ескерту
#XTIT: Delete ECN popup button and title
deleteECNConfirmationButtonAndTitle=Жою
#XMSG: Remove objects popup text
removeObjectsConfirmation=Таңдалған нысандарды жою қажеттігіне сенімдісіз бе?
#XMSG: Remove spaces popup text
removeSpacesConfirmation=Таңдалған кеңістіктерді жою қажеттігіне сенімдісіз бе?
#XBUT: Confirmation button for objects dialog
removeObjectsOrSpacesConfirmationBtn=Кеңістіктерді жою
#XMSG: Success message after removing objects
removeObjectsSuccessMessage=Ашық нысандар жойылды
#XMSG: Success message after assigning objects
assignObjectsSuccessMessage=Ашық нысандар тағайындалды
#XFLD: auto object assignment label for space tile
autoObjAssignmentLabel=Барлық ашық нысандар
#XFLD: Spaces tab label
spacesTabLabel=Кеңістіктер
#XFLD: Exposed objects tab label
consumptionObjectsTabLabel=Ашық нысандар
#XMSG: Success message after unassigning spaces
spacesUnassignedSuccessMessage=Кеңістіктер жойылды
#XMSG: Success message after unassigning space
spaceUnassignedSuccessMessage=Кеңістік жойылды
#YMSE: Error while assigning / unassigning spaces
spacesAssignUnassignError=Кеңістіктер тағайындалмады немесе жойылмады.
#YMSE: Error while removing objects
removeObjectsError=Нысандарды тағайындау немесе жою мүмкін болмады.
#YMSE: Error while removing object
removeObjectError=Нысанды тағайындау немесе жою мүмкін болмады.
#YMSE: Error when saving the ecn with invalid value
invalidSizingPlanError=Бұрын таңдалған нөмір енді жарамсыз. Жарамды нөмірді таңдаңыз.
#YMSE: Error when saving the ecn with invalid value
invalidPerformanceClassError=Жарамды өнімділік класын таңдаңыз.
#YMSE: Error when saving the ecn with invalid value
performanceClassNotAvailableError=Бұрын таңдалған "{0}" өнімділік класы қазір жарамсыз. Жарамды өнімділік класын таңдаңыз.
#XTXT: Delete ECN confirmation text
deleteConfirmationTxtECN=Серпімді есептеу түйінін шынымен жою керек пе?
#XFLD: tooltip for ? button
help=Анықтама
#XFLD: ECN edit button label
editECN=Теңшеу
#XFLD: Technical type label for ERModel
DWC_ERMODEL=Объектілер арақатынасының үлгісі
#XFLD: Technical type label for Local Table
DWC_LOCAL_TABLE=Жергілікті кесте
#XFLD: Technical type label for Remote Table
DWC_REMOTE_TABLE=Қашықтағы кесте
#XFLD: Technical type label for Analytic Model
DWC_ANALYTIC_MODEL=Аналитикалық үлгі
#XFLD: Technical type label for Task Chain
DWC_TASKCHAIN=Тапсырмалар тізбегі
#XFLD: Technical type label for Data Flow
DWC_DATAFLOW=Деректер ағыны
#XFLD: Technical type label for Replication Flow
DWC_REPLICATIONFLOW=Тираждау ағыны
#XFLD: Technical type label for Transformation Flow
DWC_TRANSFORMATIONFLOW=Түрлендіру ағыны
#XFLD: Technical type label for Intelligent Lookup
DWC_IDT=Интеллектуалды іздеу
#XFLD: Technical type label for Repository
DWC_REPOSITORY_PACKAGE=Репозитарий
#XFLD: Technical type label for Enterprise Search
DWC_ESMODEL=Корпоративтік іздеу
#XFLD: Technical type label for View
DWC_VIEW=Көрініс
#XFLD: Technical type label for Data Product
DWC_DATAPRODUCT=Дерек өнімі
#XFLD: Technical type label for Data Access Control
DWC_DAC=Дерекке қол жеткізуді басқару
#XFLD: Technical type label for Folder
DWC_FOLDER=Қалта
#XFLD: Technical type label for Data Entity
DWC_BUSINESS_ENTITY=Бизнес объект
#XFLD: Technical type label for Business Entity Variant
DWC_BUSINESS_ENTITY_VARIANT=Бизнес объект нұсқасы
#XFLD: Technical type label for Responsibility Scenario
DWC_AUTH_SCENARIO=Жауапкершілік сценарийі
#XFLD: Technical type label for Fact Model
DWC_FACT_MODEL=Факт үлгісі
#XFLD: Technical type label for Transformation Flow
DWC_PERSPECTIVE=Перспектива
#XFLD: Technical type label for Consumption Model
DWC_CONSUMPTION_MODEL=Тұтыну үлгісі
#XFLD: Technical type label for Remote Connection
DWC_REMOTECONNECTION=Қашықтан қосылу
#XFLD: Technical type label for Fact Model Variant
DWC_FACT_MODEL_VARIANT=Факт үлгісі нұсқасы
#XMSG: Schedule created alert message
createScheduleSuccess=Кесте жасалды
#XMSG: Schedule updated alert message
updateScheduleSuccess=Кесте жаңартылды
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Кесте жойылды
#XMSG: Schedule takeover success message
ownerChangeScheduleSuccess=Сізге тағайындалған кесте
#XMSG: Message for starting pausing of schedule
pauseScheduleStarted=1 кесте кідіртілуде
#XMSG: Message for starting resuming of schedule
resumeScheduleStarted=1 кесте жалғастырылуда
#XFLD: Segmented button label
availableSpacesButton=Қолжетімді
#XFLD: Segmented button label
selectedSpacesButton=Таңдалған
#XFLD: Visit website button text
visitWebsite=Веб-сайтқа кіру
#XMSG: Warning message on unselecting source language
sourceLanguageRemovalWarning=Бұрын таңдалған түпнұсқа тіл жойылды.
#XBUT: enable translation confirmation button
enableTranslationConfirmationBtn=Қосу
#XFLD: ECN performance class label
performanceClassLabel=Өнімділік класы
#XTXT performance class memory text
memoryText=Жад
#XTXT performance class compute text
computeText=Есептеу
#XTXT performance class high-compute text
highComputeText=Жоғары есептеу
#XBUT: Recycle Bin Button Text
recycleBin=Себет
#XBUT: Restore Button Text
restore=Қалпына келтіру
#XMSG: Warning message for new Workload Management UI
priorityWarning=Бұл аймақ тек оқуға арналған. Жүйе / Конфигурация / Жұмыс жүктемесін басқару аймағындағы кеңістік басымдылығын өзгертуге болады.
#XMSG: Warning message for new Workload Management UI
workloadWarning=Бұл аймақ тек оқуға арналған. Жүйе / Конфигурация / Жұмыс жүктемесін басқару аймағындағы кеңістіктің жұмыс жүктемесі конфигурациясын өзгертуге болады.
#XCOL: Space table-view column: Apache Spark vCPUs
sparkManagementVCPUs=Apache Spark вируталды орталық процессорлары
#XCOL: Space table-view column: Apache Spark Memory (GB)
sparkManagementMemory=Apache Spark жады (ГБ)
#XCOL: Space type (from Space Capability) for Data Ingestion Spaces
ingestionSpaceType=Дерек өнімін қабылдау
#XFLD: Deployment in progress nodatatext for spark table
sparkTableDeploymentInProgress=Кеңістік қазір қолданылуда, дерек қолжетімді емес
#XFLD: Runtime data being loaded nodatatext for spark table
sparkTableDeploymentLoading=Кеңістік қазір жүктелуде, дерек қолжетімді емес
#XCOL: Space type (from Space Capability) for BW Private Cloud Edition
BWPCESpaceType=SAP BW
#XBUT: Go to Instance Mappings Button
navToMappingsButton=Инстанция мэппингтерін өңдеу
#XFLD: SAP HANA Data Lake Storage label if less than 1GB
dataLakeStorageUsed=< 1 ГБ
