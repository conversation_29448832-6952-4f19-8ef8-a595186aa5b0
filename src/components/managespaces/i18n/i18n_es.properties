#DO NOT REMOVE________________________
#XFLD: Breadcrumb for space monitoring page
spaceMonitorBreadcrumb=Supervisión
#XTXT: Type name for spaces in browser tab page title
space=Espacio
#_____________________________________
#XFLD: Spaces label in
spaces=Espacios
#XFLD: Manage plan button text
manageQuotaButtonText=Gestionar plan
#XBUT: Manage resources button
manageResourcesButton=Gestionar recursos
#XFLD: Create space button tooltip
createSpace=Crear espacio
#XFLD: Create
create=Crear
#XFLD: Deploy
deploy=Desplegar
#XFLD: Page
page=Página
#XFLD: Cancel
cancel=Cancelar
#XFLD: Update
update=Actualizar
#XFLD: Save
save=Guardar
#XFLD: OK
ok=OK
#XFLD: days
days=Días
#XFLD: Space tile edit button label
edit=Editar
#XFLD: Auto Assign all objects to space
autoAssign=Asignar automáticamente
#XFLD: Space tile open monitoring button label
openMonitoring=Supervisar
#XFLD: Delete
delete=Eliminar
#XFLD: Copy Space
copy=Copiar
#XFLD: Close
close=Cerrar
#XCOL: Space table-view column status
status=Estado
#XFLD: Space status active
activeLabel=Activo
#XFLD: Space status locked
lockedLabel=Bloqueado
#XFLD: Space status critical
criticalLabel=Crítico
#XFLD: Space status cold
coldLabel=Frío
#XFLD: Space status deleted
deletedLabel=Eliminado
#XFLD: Space status unknown
unknownLabel=Desconocido
#XFLD: Space status ok
okLabel=Correcto
#XFLD: Database user expired
expired=Vencido
#XFLD: deployed
deployed=Desplegado
#XFLD: not deployed
notDeployed=No desplegado
#XFLD: changes to deploy
changesToDeploy=Modificaciones para desplegar
#XFLD: pending
pending=Desplegando
#XFLD: designtime error
designtimeError=Error en tiempo de diseño
#XFLD: runtime error
runtimeError=Error en tiempo de ejecución
#XFLD: Space created by label
createdBy=Creado por
#XFLD: Space created on label
createdOn=Creado el
#XFLD: Space deployed on label
deployedOn=Fecha de despliegue
#XFLD: Space ID label
spaceID=ID de espacio
#XFLD: Priority label
priority=Prioridad
#XFLD: Space Priority label
spacePriority=Prioridad del espacio
#XFLD: Space Configuration label
spaceConfiguration=Configuración del espacio
#XFLD: Not available
notAvailable=No disponible
#XFLD: WorkloadType default
default=Predeterminado
#XFLD: WorkloadType custom
custom=Personalizado
#XFLD: Data Lake option label; refers to component SAP HANA Cloud, Data Lake
dataLakeAccess=Acceso a lago de datos
#XFLD: Translation label
translationLabel=Traducción
#XFLD: Source language label
sourceLanguageLabel=Idioma de origen
#XFLD: Translation CheckBox label
translationCheckBox=Activar traducción
#XTXT: Popover hint that space has to be deployed to show user details
openDatabaseUserHintPopoverText=Despliegue el espacio para acceder a los detalles del usuario.
#XTXT: Popover hint that space has to be deployed to open database explorer
openDatabaseExplorerHintPopoverText=Despliegue el espacio para abrir el explorador de bases de datos.
#XTXT: Text for when the Data Lake check box is disabled
dataLakeOptionDisabled=No puede utilizar este espacio para acceder al data lake porque otro espacio ya lo está utilizando.
#XFLD: Data Lake check box text; refers to component SAP HANA Cloud, Data Lake
dataLakeCheckBox=Utilice este espacio para acceder al data lake.
#XFLD: Space Priority minimum label extension
low=Baja
#XFLD: Space Priority maximum label extension
high=Alta
#XFLD: Space name label
spaceName=Nombre de espacio
#XFLD: Enable deploy objects checkbox
enableDeployObjects=Desplegar objetos
#XTIT: Copy spaces dialog title
copySpaceDialogTitle=Copiar {0}
#XTXT Human readable text for unselected source language
unSelectedSourceLanguage=(No seleccionado)
#XTXT Human readable text for language code "af"
af=Afrikáans
#XTXT Human readable text for language code "ar"
ar=Árabe
#XTXT Human readable text for language code "bg"
bg=búlgaro
#XTXT Human readable text for language code "ca"
ca=catalán
#XTXT Human readable text for language code "zh"
zh=Chino simplificado
#XTXT Human readable text for language code "zf"
zf=Chino
#XTXT Human readable text for language code "hr"
hr=croata
#XTXT Human readable text for language code "cs"
cs=checo
#XTXT Human readable text for language code "cy"
cy=galés
#XTXT Human readable text for language code "da"
da=danés
#XTXT Human readable text for language code "nl"
nl=neerlandés
#XTXT Human readable text for language code "en-UK"
en-UK=Inglés (Reino Unido)
#XTXT Human readable text for language code "en"
en=Inglés (Estados Unidos)
#XTXT Human readable text for language code "et"
et=Estonio
#XTXT Human readable text for language code "fa"
fa=Persa
#XTXT Human readable text for language code "fi"
fi=finés
#XTXT Human readable text for language code "fr-CA"
fr-CA=francés (Canadá)
#XTXT Human readable text for language code "fr"
fr=francés
#XTXT Human readable text for language code "de"
de=Alemán
#XTXT Human readable text for language code "el"
el=griego
#XTXT Human readable text for language code "he"
he=Hebreo
#XTXT Human readable text for language code "hi"
hi=hindi
#XTXT Human readable text for language code "hu"
hu=húngaro
#XTXT Human readable text for language code "is"
is=Islandés
#XTXT Human readable text for language code "id"
id=bahasa Indonesia
#XTXT Human readable text for language code "it"
it=italiano
#XTXT Human readable text for language code "ja"
ja=japonés
#XTXT Human readable text for language code "kk"
kk=kazajo
#XTXT Human readable text for language code "ko"
ko=coreano
#XTXT Human readable text for language code "lv"
lv=Letón
#XTXT Human readable text for language code "lt"
lt=lituano
#XTXT Human readable text for language code "ms"
ms=Bahasa Melayu
#XTXT Human readable text for language code "no"
no=noruego
#XTXT Human readable text for language code "pl"
pl=polaco
#XTXT Human readable text for language code "pt"
pt=Portugués (Brasil)
#XTXT Human readable text for language code "pt-PT"
pt-PT=portugués (Portugal)
#XTXT Human readable text for language code "ro"
ro=rumano
#XTXT Human readable text for language code "ru"
ru=ruso
#XTXT Human readable text for language code "sr"
sr=Serbio
#XTXT Human readable text for language code "sh"
sh=Serbocroata
#XTXT Human readable text for language code "sk"
sk=eslovaco
#XTXT Human readable text for language code "sl"
sl=esloveno
#XTXT Human readable text for language code "es"
es=español
#XTXT Human readable text for language code "es-MX"
es-MX=español (México)
#XTXT Human readable text for language code "sv"
sv=sueco
#XTXT Human readable text for language code "th"
th=tailandés
#XTXT Human readable text for language code "tr"
tr=turco
#XTXT Human readable text for language code "uk"
uk=ucraniano
#XTXT Human readable text for language code "vi"
vi=vietnamita
#XTIT: Delete spaces confirmation title
deleteConfirmationHeaderText=Eliminar espacios
#XTXT: Logically delete space delete confirmation text with space name
logicallyDeleteConfirmationBodyTextWithSpaceName=¿Seguro que desea mover el espacio "{0}" a la papelera de reciclaje?
#XTXT: Logically dpace delete confirmation text for multiple spaces
logicallyDeleteConfirmationBodyTextMultiple=¿Seguro que desea mover los {0} espacios seleccionados a la papelera de reciclaje?
#XTXT: Delete space delete confirmation text with space name
deleteConfirmationBodyTextWithSpaceName=¿Seguro que desea eliminar el espacio "{0}"? Esta acción no se puede deshacer.
#XTXT: Delete space delete confirmation text for multiple spaces
deleteConfirmationBodyTextMultiple=¿Seguro que desea eliminar los {0} espacios seleccionados? Esta acción no se puede deshacer. El siguiente contenido se eliminará {1}:
#XTXT: permanently
permanently=de forma permanente
#XTXT: {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDetail=El siguiente contenido se eliminará {0} y no se podrá recuperar:
#XTXT: Hint Delete space confirmation text
deleteConfirmationHint=Escriba {0} para confirmar la eliminación.
#XTXT: Delete space confirmation error check spelling
deleteConfirmationErrorCheckSpelling=Compruebe la ortografía e inténtelo de nuevo.
#XTXT: All Spaces
allSpaces=Todos los espacios
#XTXT: All data
allData=Todos los objetos y datos incluidos en el espacio
#XTXT: All connections
allConnections=Todas las conexiones definidas en el espacio
#XFLD: Space tile selection box tooltip
clickToSelect=Haga clic para seleccionar
#XTXT: All database users
allDatabaseUsers=Todos los objetos y datos incluidos en cualquier esquema Open SQL asociado al espacio
#XFLD: remove members button tooltip
deleteUsers=Quitar miembros
#XTXT: Space long description text
description=Descripción (4000 caracteres máximo)
#XFLD: Add Members button tooltip
addUsers=Añadir miembros
#XFLD: Add Users button tooltip
addUsersTooltip=Añadir usuarios
#XFLD: Edit Users button tooltip
editUsersTooltip=Editar usuarios
#XFLD: Remove Users button tooltip
removeUsersTooltip=Quitar usuarios
#XFLD: Searchfield placeholder
filter=Buscar
#XCOL: Users table-view column health
health=Salud
#XCOL: Users table-view column access
access=Acceso
#XFLD: No user found nodatatext
noDataText=No se ha encontrado ningún usuario
#XTIT: Members dialog title
selectUserDialogTitle=Añadir miembros
#XTIT: User dialog title
addUserDialogTitle=Añadir usuarios
#XTIT: Delete connections confirmation title
deleteConnectionConfirmationHeaderText=Eliminar conexiones
#XTIT: Delete single connection confirmation title
deleteConnectionConfirmationHeaderTextSingle=Eliminar conexión
#XTXT: Delete connection delete confirmation text
deleteConnectionConfirmationBodyText=¿Seguro que desea eliminar las conexiones seleccionadas? Se quitarán de forma permanente.
#XTIT: Remote source dialog title
selectRemoteConnectionDialogTitle=Seleccionar conexiones
#XTIT: Share connection dialog title
connectionSharingDialogTitle=Compartir conexión
#XTIT: Connections shared from other spaces table title
sharedConnectionsFromOtherSpaces=Conexiones compartidas
#XFLD: Add remote source button tooltip
addRemoteConnections=Añadir conexiones
#XFLD: Delete remote source button tooltip
deleteRemoteConnections=Quitar conexiones
#XFLD: Share remote source button tooltip
shareConnections=Compartir conexiones
#XFLD: Tile-layout tooltip
tileLayout=Disposición de mosaicos
#XFLD: Table-layout tooltip
tableLayout=Disposición de tablas
#XMSG: Success message after creating space
createSpaceSuccessMessage=Se ha creado el espacio
#XMSG: Success message after copying space
copySpaceSuccessMessage=Se está copiando el espacio "{0}" en el espacio "{1}"
#XMSG: Success message after deploying space
deploymentSuccessMessage=Se ha iniciado el despliegue del espacio
#XMSG: Success message after Apache Spark update started
sparkUpdateStartedSuccessMessage=La actualización de Apache Spark ha comenzado
#YMSE: Error while starting Apache Spark update
sparkUpdateStartedErrorMessage=Error al actualizar Apache Spark
#XMSG: Success message after updating space
updateSettingsSuccessMessage=Se han actualizado los detalles del espacio
#XMSG: Success message after unlocking space
unlockSpaceSuccessMessage=Espacio no bloqueado temporalmente
#XMSG: Success message after deleting a single space
deleteSpaceSuccessMessage=Se ha eliminado el espacio
#XMSG: Success message after deleting multiple spaces
deleteSpacesSuccessMessage=Se han eliminado los espacios
#XMSG: Success message after restoring a single space
restoreSpaceSuccessMessage=Se ha restaurado el espacio
#XMSG: Success message after restoring multiple spaces
restoreSpacesSuccessMessage=Se han restaurado los espacios
#YMSE: Error while updating settings
updateSettingsFailureMessage=No se han podido actualizar las opciones del espacio.
#YMSE: Error while updating data lake option
dataLakeEnabledInOtherSpace=El data lake ya se ha asignado a otro espacio. Solo un espacio puede acceder al data lake a la vez.
#YMSE: Error while updating data lake option
virtualTablesExists=No puede anular la asignación del data lake de este espacio porque aún hay dependencias en las tablas virtuales*. Elimine las tablas virtuales para anular la asignación del data lake de este espacio.
#YMSE: Error while unlocking space
unlockSpaceFailureMessage=No se ha podido desbloquear el espacio.
#YMSE: Error while creating space
createSpaceError=No se ha podido crear el espacio.
#YMSE: Error while creating duplicate space
createDuplicateSpaceError=Ya existe un espacio con el nombre {0}.
#YMSE: Error while deleting a single space
deleteSpaceError=No se ha podido eliminar el espacio.
#YMSE: Error details while deleting a single space
deleteSpaceDetailedError=Su espacio "{0}" ha dejado de funcionar correctamente. Intente eliminarlo de nuevo. Si sigue sin funcionar, pida al administrador que elimine su espacio o abra un ticket.
#YMSE: Error while deleting a single space in files
deleteSpaceInFilesError=No se han podido eliminar datos de espacio en Archivos.
#YMSE: Error while deleting a single space users
deleteSpaceUsersError=No se han podido quitar los usuarios.
#YMSE: Error while deleting a single space schema
deleteSpaceSchemasError=No se han podido quitar los esquemas.
#YMSE: Error while deleting a single space connections
deleteSpaceConnectionsError=No se han podido quitar las conexiones.
#YMSE: Error while deleting a single space data
deleteSpaceDataError=No se han podido eliminar los datos del espacio.
#YMSE: Error while deleting multiple spaces
deleteSpacesError=No se han podido eliminar los espacios.
#YMSE: Error while restoring a single space
restoreSpaceError=No se ha podido restaurar el espacio.
#YMSE: Error while restoring multiple spaces
restoreSpacesError=No se han podido restaurar los espacios.
#YMSE: Error while creating users
createUsersError=No se han podido añadir los usuarios.
#YMSE: Error while removing users
removeUsersError=No se han podido quitar los usuarios.
#YMSE: Error while removing user
removeUserError=no ha podido quitar el usuario.
#YMSE: Error assigning roles to a user
roleAssignUnAssignError=No hemos podido añadir el usuario al rol dentro del alcance seleccionado. \n\n No puede añadirse a un rol dentro del alcance. Puede pedir al administrador que le añada a un rol dentro del alcance.
#YMSE: Error assigning user to the space
userAssignError=No hemos podido asignar el usuario al espacio. \n\n El usuario ya está asignado al número permitido máximo (100) de espacios en los roles dentro del alcance.
#YMSE: Error assigning users to the space
usersAssignError=No hemos podido asignar los usuarios al espacio. \n\n El usuario ya está asignado al número permitido máximo (100) de espacios en los roles dentro del alcance.
#YMSE: Error while fetching users
fetchUserWithScopedRolesError=No se han podido recuperar los usuarios. Vuelva a intentarlo más tarde.
#YMSE: Error while fetching scoped roles
fetchScopedRolesError=No se han podido recuperar los roles que hay dentro del alcance.
#YMSE: Error while fetching members
fetchUserError=No se han podido obtener los miembros. Inténtelo de nuevo más tarde.
#YMSE: Error while loading run-time database
loadRuntimeError=No se ha podido cargar información de la base de datos en tiempo de ejecución.
#YMSE: Error while loading spaces
loadSpacesError=Lo sentimos, se ha producido un problema al intentar recuperar sus espacios.
#YMSE: Error while loading haas resources
loadStorageError=Lo sentimos, se ha producido un problema al intentar recuperar los datos de almacenamiento.
#YMSE: Error no data could be loaded
loadDataError=Lo sentimos, se ha producido un problema al intentar recuperar sus datos.
#XFLD: Click to refresh storage data
clickToRefresh=Haga clic para intentarlo de nuevo.
#XTIT: Delete space popup title
deleteSpacePopupTitle=Eliminar espacio
#XCOL: Spaces table-view column name
name=Nombre
#XCOL: Spaces table-view deployment status
deploymentStatus=Estado de despliegue
#XFLD: Disk label in space details
storageLabel=Disco (GB)
#XFLD: In-Memory label in space details
ramLabel=Memoria (GB)
#XFLD: Memory label on space card
memory=Memoria para almacenamiento
#XFLD: Storage Assignment label in space details
storrageAssignmentLabel=Almacenamiento de espacio
#XFLD: Storage Type label in space details
storageTypeLabel=Tipo de almacenamiento
#XFLD: Enable Space Quota
enableSpaceQuota=Activar la cuota de espacio
#XFLD: No Space Quota
noSpaceQuota=Sin cuota de espacio
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
diskAndInMemoryText=Base de datos de SAP HANA (disco y en memoria)
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
dataLakeFilesText=Archivos de SAP HANA Data Lake
#XFLD: Available scoped roles label
availableRoles=Roles disponibles dentro del alcance
#XFLD: Selected scoped roles label
selectedRoles=Roles seleccionados dentro del alcance
#XCOL: Spaces table-view column models
models=Modelos
#XCOL: Spaces table-view column users
users=Usuarios
#XCOL: Spaces table-view column connections
connections=Conexiones
#XFLD: Section header overview in space detail
overview=Resumen
#XFLD: Section header Apache Spark in space detail
sparkManagementSection=Apache Spark
#XFLD: Section header Apache Spark in space detail
sparkManagementSubSection=Aplicaciones
#XFLD: Section header Apache Spark in space detail
taskAssignmentSubSection=Asignación de tareas
#XFLD: vCPU label in Apache Spark section
vCPULabel=vCPUs
#XFLD: Memory label in Apache Spark section
memoryLabel=Memoria (GB)
#XFLD: space configuration label in Apache Spark section
spaceConfigurationLabel=Configuración del espacio
#XFLD: Space Source label
sparkApplicationLabel=Aplicación
#XFLD: Cluster Size label
clusterSizeLabel=Tamaño de clúster
#XFLD: Driver label
driverLabel=Controlador
#XFLD: Executor label
executorLabel=Ejecutor
#XFLD: max label
maxLabel=Máximo utilizado
#XFLD: TrF Default label
trFDefaultLabel=Estándar de flujo de transformación
#XFLD: Merge Default label
mergeDefaultLabel=Estándar de fusión
#XFLD: Optimize Default label
optimizeDefaultLabel=Optimizar predeterminado
#XFLD: Deployment Default label
deploymentDefaultLabel=Despliegue de tabla local (archivo)
#XFLD: max spark source label
driverExecutorMaxUsedSparkSource={0} CPU / {1} GB
#XFLD: Object type label
taskObjectTypeLabel=Tipo de objeto
#XFLD: Task activity label
taskActivityLabel=Actividad
#XFLD: Task Application ID label
taskApplicationIDLabel=Aplicación predeterminada
#XFLD: Section header in space detail
generalSettings=Opciones generales
#XMSG: Message strip message when a space is locked
spaceLockedMessageStrip=Este espacio está bloqueado actualmente por el sistema.
#XMSG: Message strip message deploy warning
deployWarningMessageStrip=Los cambios realizados en esta sección se desplegarán de inmediato.
#XMSG: Message strip message modify space configuration warning
modifySpaceConfigurationMessageStrip=Tenga en cuenta que la modificación de estos valores puede provocar problemas de rendimiento.
#XFLD: Button text to unlock the space again
unlockSpace=Desbloquear espacio
#XFLD: Info text for audit log formatted message
auditLogText=Active los logs de auditoría para las acciones de modificación o lectura de registros (políticas de auditoría). A continuación, los administradores podrán analizar quién ha llevado a cabo cada acción y en qué momento.
#XMSG: Message strip  audit warning
auditLogWarningMessageStrip=Los logs de auditoría pueden consumir gran cantidad de almacenamiento en disco en su arrendatario. Si activa una política de auditoría (acciones de modificación o lectura), supervise regularmente el consumo del almacenamiento en disco (mediante la tarjeta Almacenamiento en disco utilizado en el monitor del sistema) para evitar interrupciones por disco lleno, ya que podrían provocar alteraciones en el servicio. Si desactiva una política de auditoría, se eliminarán todas las entradas en su log de auditoría. Si desea conservar las entradas en el log de auditoría, plantéese exportarlas antes de desactivar la política de auditoría.
#XFLD: Message strip link text to open in app help to guide user what to do to unlock his space permanently
unlockSpaceHelp=Mostrar ayuda
#XMSG: Message strip message when a space will be locked again
spaceTemporarilyUnlockedMessageStrip=Este espacio supera su almacenamiento y se bloqueará en {0} {1}.
#XMSG: Unit for remaining time until space is locked again
hours=horas
#XMSG: Unit for remaining time until space is locked again
minutes=minutos
#XFLD: Section header in space detail for auditing
detailsSectionAuditing=Auditoría
#XFLD: Subsection header in space detail for auditing
auditing=Opciones de auditoría de espacio
#XFLD: Hot space tooltip
hotSpaceCountTooltip=Espacios críticos: el almacenamiento utilizado es mayor que el 90%
#XFLD: Green space tooltip
greenSpaceCountTooltip=Espacios sanos: El almacenamiento utilizado está entre el 6% y el 90%.
#XFLD: Cold space tooltip
coldSpaceCountTooltip=Espacios fríos: El almacenamiento utilizado es del 5% o inferior.
#XFLD: Critical space tooltip
criticalSpaceCountTooltip=Espacios críticos: el almacenamiento utilizado es mayor que el 90%
#XFLD: Green space tooltip
okSpaceCountTooltip=Espacios sanos: El almacenamiento utilizado está entre el 6% y el 90%.
#XFLD: Locked space tooltip
lockedSpaceCountTooltipNew=Espacios bloqueados: están bloqueados porque la memoria es insuficiente.
#XFLD: Locked space tooltip
lockedSpaceCountTooltip=Espacios bloqueados
#YMSE: Error while deleting remote source
deleteRemoteError=No se han podido quitar las conexiones.
#XFLD: Space ID input tooltip
spaceIDInfoTooltip=El ID de espacio no se puede modificar más tarde.\nLos caracteres válidos son A - Z, 0 - 9, y _
#YMSE: Error when space name is empty
spaceNameEmptyValidationError=Introduzca el nombre de espacio.
#YMSE: Error when ECN Business name is empty
ecnNameEmptyValidationError=Introduzca un nombre empresarial.
#YMSE: Error when space id is empty
spaceIDInputEmptyValidationError=Introduzca el ID de espacio.
#YMSE: Error when space id contains invalid characters
spaceIDInputInValidError=Caracteres no válidos. Use solamente A - Z, 0 - 9 y _.
#YMSE: Error when space id exists while creation
spaceIDInputDuplicateError=Ya existe el ID de espacio.
#XFLD: Space searchfield placeholder
search=Buscar
#XMSG: Success message after creating users
createUsersSuccess=Se han añadido los usuarios
#XMSG: Success message after creating user
createUserSuccess=Se ha añadido el usuario
#XMSG: Success message after updating users
updateUsersSuccess=Se han actualizado {0} usuarios
#XMSG: Success message after updating user
updateUserSuccess=Se ha actualizado el usuario
#XMSG: Success message after removing users
removeUsersSuccess=Se han quitado {0} usuarios
#XMSG: Success message after removing user
removeUserSuccess=Se ha quitado el usuario
#XFLD: Schema name
schemaName=Nombre de esquema
#XFLD: used of total
ofTemplate={0} de {1}
#XFLD: Assigned disk label in space overview
assignedStorageTemplate=Asignado en disco ({0} de {1})
#XFLD: Assigned Memory label in space overview
assignedRAMTemplate=Memoria asignada ({0} de {1})
#XFLD: Storage ratio on space
accelearationRAM=Aceleración de memoria
#XFLD: No Storage Consumption
noStorageConsumptionText=No se ha asignado ninguna cuota de espacio.
#XFLD: Used disk label in space overview
usedStorageTemplate=Disco utilizado para el almacenamiento ({0} de {1})
#XFLD: Used Memory label in space overview
usedRAMTemplate=Memoria utilizada para el almacenamiento ({0} de {1})
#XFLD: Used Disk tooltip
usedStorageTooltipTemplate={0} de {1} de disco utilizado para el almacenamiento
#XFLD: Used Memory tooltip
usedRAMTooltipTemplate={0} de {1} memoria utilizada
#XFLD: Assigned Disk tooltip
assignedStorageTooltipTemplate={0} de {1} asignado en disco
#XFLD: Assigned Memory tooltip
assignedRAMTooltipTemplate={0} de {1} memoria asignada
#XFLD: Space related used storage tooltip
spaceRelatedUsedStorageTooltipTemplate=Datos de espacio: {0}
#XFLD: Not space related used storage tooltip
otherDataUsedStorageTooltipTemplate=Otros datos: {0}
#XFLD: Storage used exceeded popover and tooltip
storageUsedExceededText=Considere ampliar su plan, o póngase en contacto con el soporte de SAP.
#XCOL: Space table-view column used Disk
usedStorage=Disco utilizado para el almacenamiento
#XCOL: Space monitor column used Memory
usedRAM=Memoria utilizada para el almacenamiento
#XCOL: Space monitor column Schema
tableSchema=Esquema
#XCOL: Space monitor column Storage Type
tableStorageType=Tipo de almacenamiento
#XCOL: Space monitor column Table Type
tableType=Tipo de tabla
#XCOL: Space monitor column Record Count
tableRecordCount=Recuento de registros
#XFLD: Assigned Disk
assignedStorage=Disco asignado para el almacenamiento
#XFLD: Assigned Memory
assignedRAM=Memoria asignada para el almacenamiento
#XCOL: Space table-view column storage utilization
tableStorageUtilization=Almacenamiento utilizado
#XFLD: space status
spaceStatus=Estado del espacio
#XFLD: space type
spaceType=Tipo de espacio
#XFLD: space type label for space with space type "SAP BW bridge" ("SAP BW bridge" is a product name, do not translate it)
abapbridgeSpaceType=Puente de SAP BW
#XFLD: space type label for space with space type "Data Marketplace"
marketplaceSpaceType=Producto de proveedor de datos
#XMSG: Error message for when selected space cannot be deleted due to the space type
deleteTypedSpaceError=No puede eliminar el espacio {0} ya que su tipo de espacio es {1}.
#XMSG: Error message for when selected spaces cannot be deleted due to the space type
deleteTypedSpacesError=No puede eliminar los {0} espacios seleccionados. Los espacios con los siguientes tipos no se pueden eliminar: {1}.
#XFLD: space type label for space without space type
defaultSpaceType=SAP Datasphere
#XFLD: Tooltip for monitor space button
monitorSpace=Supervisar
#XFLD: Tooltip for edit space button
editSpace=Editar espacio
#XMSG: Deletion warning in messagebox
deleteConfirmation=¿Seguro que desea eliminar este espacio?
#XFLD: Tooltip for delete space button
deleteSpace=Eliminar espacio
#XFLD: storage
storage=Disco para el almacenamiento
#XFLD: username
userName=Nombre de usuario
#XFLD: port
port=Puerto
#XFLD: hostname
hostName=Nombre de host
#XFLD: password
password=Contraseña
#XBUT: Request new password button
requestPassword=Solicitar contraseña nueva
#YEXP: Usage explanation in time data section
timeDataSectionHint=Cree tablas de tiempos y dimensiones para utilizarlas en sus modelos e historias.
#YEXP: Usage explanation in Space schema section
spaceSchemaHint=¿Desea que otras herramientas o aplicaciones consuman los datos en su espacio? Si es así, cree uno o varios usuarios que puedan acceder a los datos en su espacio y seleccione si quiere que todos los datos del espacio futuros se consuman de forma predeterminada.
#XTIT: Create schema popup title
createSchemaDialogTitle=Crear esquema Open SQL
#XTIT: Generate Time Data dialog title
generateTimeDataDialogTitle=Crear tablas de tiempos y dimensiones
#XTIT: Edit Time Data dialog title
editTimeDataDialogTitle=Editar tablas de tiempos y dimensiones
#XTIT: Time Data token title
timeDataTokenTitle=Datos de tiempo
#XTIT: Time Data token title
timeDataUpdateViews=Actualizar vistas de datos de tiempo
#XFLD: Time Data token creation in progress label
timeDataCreationInProgress=Creación en curso...
#XFLD: Time Data token creation error label
timeDataCreationError=Error en la creación. Inténtelo de nuevo.
#XTIT: Time Data dialog time settings title
timeDataDialogTimeSettingsTitle=Opciones de la tabla de tiempo
#XTIT: Time Data dialog time data table title
timeDataDialogTranslationTablesTitle=Tablas de traducción
#XTIT: Time Data dialog dimensions title
timeDataDialogTimeViewsTitle=Dimensiones de tiempo
#XFLD: Time Data dialog time range label
timeRangeHint=Definir el intervalo de tiempo.
#XFLD: Time Data dialog time data table label
timeDataHint=Dar un nombre a la tabla.
#XFLD: Time Data dialog time data dimensions label
timeDimensionsHint=Dar un nombre a la dimensión.
#XFLD: Time Data Time range description label
timerangeLabel=Intervalo de tiempo
#XFLD: Time Data dialog from year label
fromYearLabel=Desde año
#XFLD: Time Data dialog to year label
toYearLabel=Hasta año
#XFLD: Time Data dialog calendar type label
calendarTypeLabel=Tipo de calendario
#XFLD: Time Data dialog granularity label
granularityLabel=Granularidad
#XFLD: Time Data dialog technical name label
technicalNameLabel=Nombre técnico
#XFLD: Time Data dialog quarters text table lable
quarterTextsTableLabel=Tabla de traducciones para trimestres
#XFLD: Time Data dialog months text table lable
monthsTextsTableLabel=Tabla de traducciones para meses
#XFLD: Time Data dialog days text table lable
daysTextsTableLabel=Tabla de traducciones para días
#XFLD: Time Data dialog year label
yearLabel=Dimensión Año
#XFLD: Time Data dialog quarter label
quarterLabel=Dimensión Trimestre
#XFLD: Time Data dialog month label
monthLabel=Dimensión Mes
#XFLD: Time Data dialog day label
dayLabel=Dimensión Día
#XFLD: Time Data dialog gregorian calendar type label
gregorian=Gregoriano
#XFLD: Time Data dialog time granularity day label
day=Día
#XFLD: Time Data dialog business name maxlength reached
timeRangeValueStateTextMaxLengthBusinessName=Se ha alcanzado la longitud máxima de 1000 caracteres.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextMaxRangeExceeded=El intervalo de tiempo máximo es 150 años.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeLower="Año de inicio" debe ser anterior a "Año de fin"
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeHigher=El "Año de inicio" debe de ser 1900 o posterior.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeHigher="Año de fin" debe ser posterior a "Año de inicio"
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeLower=El "Año de fin" debe ser anterior al actual más 100
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningFromYear=Aumentar el "Año de inicio" puede conllevar una pérdida de datos
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningToYear=Reducir el "Año de fin" puede conllevar una pérdida de datos
#XMSG: Time Data creation validation error message
timeDataValidationError=Parece que algunos campos no son válidos. Verifique los campos obligatorios para continuar.
#XMSG: Time Data deletion popup text
deleteTimeDataConfirmation=¿Seguro que desea eliminar los datos?
#XMSG: Time Data creation success message
createTimeDataSuccess=Datos de tiempo creados
#XMSG: Time Data update success message
updateTimeDataSuccess=Datos de tiempo actualizados
#XMSG: Time Data delete success message
deleteTimeDataSuccess=Datos de tiempo eliminados
#XMSG: Time Data creation error message
createTimeDataError=Se ha producido un error al intentar crear datos de tiempo.
#XMSG: Time Data update error message
updateTimeDataError=Se ha producido un error al intentar actualizar datos de tiempo.
#XMSG: Time Data creation error message
deleteTimeDataError=Se ha producido un error al intentar eliminar datos de tiempo.
#XMSG: Error message when time data could not be retrieved
retrievingTimeDataError=No ha sido posible cargar datos de tiempo.
#XTIT: Time Data deletion popup title
deleteTimeDataConfirmationTitle=Advertencia
#XTXT: Time Data deletion dependency popup text
deleteTimeDataDependencyText=No ha sido posible eliminar sus datos de tiempo porque se utilizan en otros modelos.
#XTXT: Time Data deletion dependency popup text singular
deleteTimeDataDependencyTextSingular=No ha sido posible eliminar sus datos de tiempo porque se utilizan en otro modelo.
#XMSG: Validation error when required time data form field is empty
timeDataRequiredField=Este campo es obligatorio y no se puede dejar vacío.
#XFLD: Tooltip for Open in Database Explorer button
openInDBX=Abrir en el Explorador de bases de datos
#YMSE: Dimension Year
dimensionYearView=Dimensión "Año"
#YMSE: Dimension Year
dimensionQuarterView=Dimensión "Trimestre"
#YMSE: Dimension Year
dimensionMonthView=Dimensión "Mes"
#YMSE: Dimension Year
dimensionDayView=Dimensión "Día"
#XFLD: Time Data deletion object title
timeDataUsedIn=(se utiliza en {0} modelos)
#XFLD: Time Data deletion object title singular
timeDataUsedInSingular=(se utiliza en 1 modelo)
#XFLD: Time Data deletion table column provider
provider=Proveedor
#XTIT: Time Data dependency dialog title
timeDataDependencyDialogTitle=Dependencias
#XTIT: Create schema popup title
createSpaceSchemaUserDialogTitle=Crear usuario para esquema de espacios
#XFLD: Create schema button
createSchemaButton=Crear esquema Open SQL
#XFLD: Generate TimeData button
generateTimeDataButton=Crear tablas de tiempos y dimensiones
#XFLD: Show dependencies button
showDependenciesButton=Mostrar dependencias
#XFLD: tooltip for disabled buttons when user is not member of space
mustBeMemberOfSpaceForOperation=Para realizar esta operación, su usuario debe ser un miembro del espacio.
#XFLD: Create space schema user button
createSpaceSchemaUserButton=Crear usuario de esquema de espacios
#YMSE: API Schema users load error
loadSchemaUsersError=No se ha podido cargar la lista de usuarios.
#XTIT: API Schema-config popup title
schemaUserDetailsDialogTitle=Detalles de usuario de esquema de espacios
#XMSG: API Schema user deletion popup text
deleteSpaceSchemaUserConfirmation=¿Seguro que desea eliminar el usuario seleccionado?
#XMSG: API Schema user deletion confirmation text
userDeleteSuccess=Se ha eliminado el usuario
#YMSE: API Schema user deletion error
userDeleteError=No se ha podido eliminar el usuario.
#XFLD: User deleted
userDeleted=Se ha eliminado el usuario.
#XTIT: Remove user popup title
removeUserConfirmationTitle=Advertencia
#XMSG: Remove user popup text
removeUserConfirmation=¿Confirma que desea quitar el usuario? El usuario y sus roles asignados dentro del alcance se quitarán del espacio.
#XMSG: Remove users popup text
removeUsersConfirmation=¿Confirma que desea quitar los usuarios? Los usuarios y sus roles asignados dentro del alcance se quitarán del espacio.
#XBUT: Confirmation button for remove user dialog
removeUserConfirmationBtn=Quitar
#YMSE: No data text for available roles
noDataAvailableRoles=El espacio no se añade a ningún rol dentro del alcance. \n Para poder añadir usuarios al espacio, primero se debe añadir a uno o más roles dentro del alcance.
#YMSE: No data text for selected roles
noDataSelectedRoles=No hay roles seleccionados dentro del alcance
#XTIT: Schema-config popup title
schemaDetailsDialogTitle=Detalles de configuración de esquema Open SQL
#XFLD: Label for Read Audit Log
auditLogRead=Activar log de auditoría para operaciones de lectura
#XFLD: Label for Change Audit Log
auditLogChange=Activar log de auditoría para operaciones de modificación
#XFLD: Label Audit Log Retention
auditLogRetention=Conservar logs durante
#XFLD: Label Audit Log Retention Unit
retentionUnit=días
#YMSE: Error when log retention time is set incorrectly
incorrectRetentionTime=Introduzca un número entero entre {0} y {1}
#XFLD: Label for Cross Space Schema Access
spaceSchemaAccess=Consumir datos del esquema de espacio
#XTIT: Confirm deactivation of space schema consumption dialog title
disableSpaceSchemaConsumptionDialogTitle=Detener el consumo de datos del esquema de espacio
#XTXT: Confirm deactivation of space schema consumption dialog text
disableSpaceSchemaConsumptionDialogText=Este esquema de Open SQL puede consumir datos de su esquema de espacio. Si detiene el consumo, los modelos basados en los datos del esquema de espacio podrían dejar de funcionar.
#XBUT: Confirmation button for the stop consuming space schema data dialog
stopConsuming=Detener el consumo
#XTXT: Tooltip for data lake icon
dataLakeIconTooltip=Se utiliza este espacio para acceder al lago de datos
#XFLD: Tooltip for data lake table entry
dataLakeTableEntryTooltip=Se ha activado el lake de datos
#XFLD: used In-Memory linit reached tag
usedRamLimitReachedLabel=Se ha alcanzado el límite de memoria
#XFLD: Space storage Limit reached tag
storageLimitReachedLabel=Se ha alcanzado el límite de almacenamiento
#XFLD: Minimal Storage Limit Reached tag
minStorageLimitReachedLabel=Se ha alcanzado el límite de almacenamiento mínimo
#XFLD: Space ram tag
ramLimitReachedLabel=Se ha alcanzado el límite de memoria
#XFLD: Minimal In-Memory Limit Reached tag
minRamLimitReachedLabel=Se ha alcanzado el límite de memoria mínimo
#XFLD: Popover text after click on space storage limit warning
storageLimitReachedText=Ha alcanzado el límite de almacenamiento de espacio asignado de {0}. Asigne más almacenamiento al espacio.
#XFLD: System storage tag
systemStorageLimitReachedLabel=Se ha alcanzado el límite de almacenamiento del sistema
#XMSG: Popover text after click on storage limit warning
systemStorageLimitReachedText=Ha alcanzado el límite de almacenamiento del sistema de {0}. Ahora no puede asignar más almacenamiento al espacio.
#XMSG: Schema deletion warning
deleteSchemaConfirmation=Al eliminar este esquema Open SQL, también eliminará de forma permanente todos los objetos almacenados y las asociaciones actualizadas en el esquema. ¿Desea continuar?
#XMSG: Success message after deleting a schema
schemaDeleteSuccess=Se ha eliminado el esquema
#YMSE: Error while deleting schema.
schemaDeleteError=No se ha podido eliminar el esquema.
#XMSG: Success message after update a schema
schemaUpdateSuccess=Esquema actualizado
#YMSE: Error while updating schema.
schemaUpdateError=No se ha podido actualizar el esquema.
#XMSG: Hint when no password is shown in the dialog.
newPasswordHint=Hemos proporcionado una contraseña para este esquema. Si ha olvidado su contraseña o la ha perdido, puede solicitar una nueva. Recuerde copiar o guardar la contraseña nueva.
#XMSG: Hint when showing the password in the dialog.
savePasswordHint=Copie su contraseña. Deberá configurar una conexión a este esquema. Si ha olvidado la contraseña, puede abrir este diálogo para restablecerla.
#XFLD,50: Hint on entering schema name in creation dialog
schemaNameHint=No puede cambiar el nombre tras crear el esquema.
#XFLD: Open SQL Schemas section sub headline
schemasSQL=Open SQL
#XFLD: Space schema section sub headline
schemasSpace=Espacio
#XFLD: HDI Container section header
HDIContainers=Contenedores HDI
#XTXT: Add HDI Containers button tooltip
addHDIContainers=Añadir contenedores HDI
#XTXT: Remove HDI Containers button tooltip
removeHDIContainers=Quitar contenedores HDI
#XFLD: Enable HDI Containers feature button
enableHDIContainersButton=Activar acceso
#YMSE: No data text for HDI Containers table
noDataHDIContainers=No se han añadido contenedores HDI.
#YMSE: No data text for Timedata section
noDataTimedata=No se han creado dimensiones ni tablas de tiempo.
#YMSE: No data text during runtime not available for Timedata section
runtimeNotAvailableNoDataTimedata=No puede cargar las tablas y dimensiones de tiempo porque la base de datos de tiempo de ejecución no está disponible.
#YMSE: No data text during runtime not available for HDI section
runtimeNotAvailableNoDataHDI=No puede cargar contenedores HDI porque la base de datos de tiempo de ejecución no está disponible.
#YMSE: Error while fetching HDI Containers
fetchHDIContainersError=No se han podido obtener contenedores HDI. Inténtelo de nuevo más tarde.
#XFLD Table column header for HDI Container names
HDIContainerName=Nombre de contenedor HDI
#XFLD: Enable HDI Containers feature dialog title
enableHDIContainersDialogTitle=Activar acceso
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText1New=Puede activar SAP SQL Data Warehousing en su arrendatario de SAP Datasphere para intercambiar datos entre sus contenedores HDI y sus espacios de SAP Datasphere sin necesidad de mover datos.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText2=Para ello, abra un ticket de soporte haciendo clic en el botón a continuación.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText3New=Una vez procesado el ticket, debe crear uno o más contenedores HDI en la base de datos de tiempo de ejecución de SAP Datasphere. A continuación, se sustituye el botón Activar acceso por + en la sección Contenedores HDI para todos los espacios de SAP Datasphere, y podrá añadir sus contenedores al espacio.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText41=¿Necesita más información? Vaya a %%0. Para obtener información detallada sobre lo que incluye el ticket, consulte %%1.
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkText=SAP Help
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkTextSapNote3057059=SAP Note 3057059
#XBUT: Open Ticket Button Text
openTicket=Abrir ticket
#XBUT: Add Button Text
add=Añadir
#XBUT: Next Button Text
next=Siguiente
#XBUT: Edit Button Text
editUsers=Editar
#XBUT: create user Button Text
createUser=Crear
#XBUT: Update user Button Text
updateUser=Seleccionar
#XTIT: Add HDI Containers Dialog Title
addHDIContainersDialogTitle=Añadir contenedores HDI no asignados
#XFLD: No data text for add HDI container dialog
addHDIContainersDialogNoDataText=No encontramos ningún contenedor no asignado. \N El contenedor que busca es posible que ya se haya asignado a un espacio.
#YMSE: Error while loading assigned HDI Containers
loadAssignedHDIContainersError=No se han podido cargar los contenedores HDI asignados.
#YMSE: Error while loading assigned HDI Containers
loadUnassignedHDIContainersError=No se han podido cargar los contenedores HDI.
#XMSG: Success message
succeededToAddHDIContainer=Contenedor HDI añadido
#XMSG: Success message
succeededToAddHDIContainerPlural=Contenedores HDI añadidos
#XMSG: Success message
succeededToDeleteHDIContainer=Se ha quitado el contenedor HDI
#XMSG: Success message
succeededToDeleteHDIContainerPlural=Se han quitado los contenedores HDI
#XFLD: Time data section sub headline
timeDataSection=Tablas de tiempos y dimensiones
#XFLD: Schemas list entry read audit log enabled indicator
readIndicator=Leer
#XFLD: Schemas list entry change audit log enabled indicator
changeIndicator=Modificar
#XFLD: Remote sources section sub headline
allconnections=Asignación de conexiones
#XFLD: Remote sources section sub headline
localconnections=Conexiones locales
#XFLD: Remote sources section sub headline for open Connectors
openConnectors=SAP Open Connectors
#XFLD: User section sub headline
memberassignment=Asignación de miembros
#XFLD: User assignment section sub headline
userAssignment=Asignación de usuario
#XFLD: User section Access dropdown Member
member=Miembro
#XFLD: User assignment section column name
user=Nombre de usuario
#XTXT: Selected role count
selectedRoleToolbarText=Selección: {0}
#XTIT: Space detail section connections title
detailsSectionConnections=Conexiones
#XTIT: Space detail section data access title
detailsSectionDataAccess=Acceso de esquema
#XTIT: Space detail section time data title
detailsSectionGenerateData=Datos de tiempo
#XTIT: Space detail section members title
detailsSectionUsers=Miembros
#XTIT: Space detail section Users title
detailsSectionUsersTitle=Usuarios
#XTIT: Out of Storage
insufficientStoragePopoverTitle=Memoria insuficiente
#XTIT: Storage distribution
storageDistributionPopoverTitle=Almacenamiento en disco utilizado
#XTXT: Out of Storage popover text
insufficientStorageText=Para crear otro espacio, reduzca el almacenamiento asignado de otro espacio o elimine el espacio que ya no necesite. Puede ampliar su almacenamiento del sistema llamando Gestionar plan.
#XMSG: Space id length warning
spaceIdLengthWarning=Se ha superado el máximo de {0} caracteres.
#XMSG: Space name length warning
spaceNameLengthWarning=Se ha superado el máximo de {0} caracteres.
#XMSG: Space id reserved prefix warning
spaceIdnWarningReservedPrefix=No utilice el prefijo {0} para evitar posibles conflictos.
#YMSE: Error while loading open SQL schemas
loadLocalSchemasError=No se han podido cargar los esquemas Open SQL.
#YMSE: Error while creating open SQL schema
createLocalSchemaError=No se ha podido crear el esquema Open SQL.
#YMSE: Error while loading all remote connections
loadRemoteConnectionsError=No se ha podido cargar todas las conexiones remotas.
#YMSE: Error while loading space details
loadSpaceDetailsError=No se han podido cargar los detalles del espacio.
#YMSE: Error while deploying space details
deploySpaceDetailsError=No se ha podido desplegar el espacio.
#YMSE: Error while copying space details
copySpaceDetailsError=No se ha podido copiar el espacio.
#YMSE: Error while loading storage data
loadStorageDataError=No se han podido cargar los datos de almacenamiento.
#YMSE: Error while loading all users
loadAllUsersError=No se ha podido cargar todos los usuarios.
#YMSE: Failed to reset password
resetPasswordError=No se ha podido restablecer la contraseña.
#XMSG: Success message after updating space
resetPasswordSuccessMessage=Se ha establecido una nueva contraseña para el esquema
#YMSE: DP Agent-name too long
DBAgentNameError=El nombre del agente de DP es demasiado largo.
#YMSE: Schema-name not valid.
schemaNameError=El nombre del esquema no es válido.
#YMSE: User name not valid.
UserNameError=El nombre de usuario no es válido.
#XTIT: Consumption by Storage Type
consumptionStorageTypeText=Utilización por tipo de almacenamiento
#XTIT: Consumption by Schema
consumptionSchemaText=Utilización por esquema
#XTIT: Overall Consumption by schema
overallConsumptionSchemaText=Utilización global de tabla por esquema
#XTIT: Overall Consumption by Table Type
overallConsumptionTableTypeText=Utilización global por tipo de tabla
#XTIT: Tables
tableDetailsText=Detalles de tabla
#XTIT: Table Storage Consumption
tableStorageConsumptionText=Utilización de almacenamiento de tabla
#XFLD: Table Type label
tableTypeLabel=Tipo de tabla
#XFLD: Schema label
schemaLabel=Esquema
#XFLD: reset table tooltip
resetTable=Restablecer tabla
#XFLD: In-Memory label in space monitor
inMemoryLabel=Memoria
#XFLD: Disk label in space monitor
diskLabel=Disco
#XFLD: Yes
yesLabel=Sí
#XFLD: No
noLabel=No
#XFLD: switch label: Want the data in this space to be consumable by default?
allowConsumptionByDefault=¿Desea que los datos en este espacio se consuman de forma predeterminada?
#XFLD: Business Name
businessNameLabel=Nombre empresarial
#XFLD: Refresh
refresh=Actualizar
#XMSG: No filter results title
noFilterResultsTitle=Parece que sus opciones de filtro no muestran datos.
#XMSG: No filter results message
noFilterResultsMsg=Intente ajustar sus opciones de filtro y, si aún no ve datos, cree algunas tablas en el Generador de datos. Una vez que consuman el almacenamiento, podrá supervisarlos.
#XMSG: Space monitoring Hana state red title
spaceMonitorHanaRedTitle=La base de datos en tiempo de ejecución no está disponible.
#XMSG: Space monitoring Hana state red message
spaceMonitorHanaRedMsg=Como la base de datos en tiempo de ejecución no está disponible, ciertas funciones están desactivadas y no podemos mostrar ninguna información en esta página.
#YMSE: Error while creating space schema user
createSpaceSchemaUserError=No se ha podido crear el usuario de esquema de espacios.
#YMSE: Error User name already exists
userAlreadyExistsError=Ya existe el nombre del usuario.
#YMSE: Error Authentication failed
authenticationFailedError=Error de autenticación.
#XMSG: Message strip message when a schema user is locked
userLockedMessageStrip=El usuario está bloqueado porque ha intentado iniciar sesión demasiadas veces. Solicite una nueva contraseña para desbloquear el usuario.
#XMSG: Success message after reset and unlock schema user
resetUnlockPasswordSuccessMessage=Se ha establecido la nueva contraseña y se ha desbloqueado el usuario
#XMSG: user is locked message
userLockedMessage=El usuario está bloqueado.
#XCOL: Users table-view column Role
spaceRole=Rol
#XCOL: Users table-view column Scoped Role
spaceScopedRole=Rol dentro del alcance
#XCOL: Users table-view column Space Admin
spaceAdmin=Administrador de espacio
#XFLD: User section dropdown value Viewer
viewer=Visualizador
#XFLD: User section dropdown value Modeler
modeler=Modelador
#XFLD: User section dropdown value Data Integrator
dataIntegrator=Integrador de datos
#XFLD: User section dropdown value Space Administrator
spaceAdministrator=Administrador de espacio
#XMSG: Success message -  Space Role updated successfully.
updateUserSpaceRoleSuccess=Rol de espacio actualizado
#XMSG: Error message - Failed to update Space Role.
updateUserSpaceRoleError=Error al actualizar el rol de espacio
#XFLD:
databaseUserNameSuffix=Sufijo de nombre de usuario de base de datos
#XTXT: Space Schema password text
spaceSchemaPasswordText=Para configurar una conexión con este esquema, copie su contraseña. En caso de que la haya olvidado, siempre puede solicitar una nueva.
#XFLD: Cloud Platform label in space schema dialog
cloudPlatformLabel=Cloud Platform
#XTXT: Cloud Platform text in space schema dialog
cloudPlatformText=Para configurar el acceso mediante este usuario, active el consumo y copie las credenciales. En el caso de que solo pueda copiar las credenciales sin contraseña, recuerde añadir la contraseña posteriormente.
#XFLD: enable consumption in CloudPlatform label in space schema dialog
enableConsumptionInCloudPlatformLabel=Activar el consumo en Cloud Platform
#XFLD: Credentials for User-Provided Service label in space schema dialog
cloudPlatformCredentialsLabel=Credenciales del servicio proporcionado por el usuario:
#XFLD: Credentials for User-Provided Service (Without Password) label in space schema dialog
cloudPlatformCredentialsWithoutPasswordLabel=Credenciales del servicio proporcionado por el usuario (sin contraseña):
#XFLD: Copy Credentials label in space schema dialog
copyCredentialsLabel=Copiar credenciales sin contraseña
#XFLD: Copy Credentials label in space schema dialog
copyFullCredentialsLabel=Copiar credenciales completas
#XFLD: Copy Password label in space schema dialog
copyPasswordLabel=Copiar contraseña
#XMSG: Credentials copied to clipboard
credentialsCopiedMessage=Credenciales copiadas en el portapapeles
#XMSG: Password copied to clipboard
passwordCopiedMessage=Contraseña copiada en el portapapeles
#XMSG: create database user dialog title
createDatabaseUserDialogTitle=Crear usuario de base de datos
#XMSG: Database Users section title
databaseUsers=Usuarios de base de datos
#XMSG: Database User Details dialog title
databaseUserDetailsDialogTitle=Detalles de usuario de base de datos
#XFLD: database user read audit log
databaseUserAuditLogRead=Activar logs de auditoría para operaciones de lectura y mantener logs para
#XFLD: database user change audit log
databaseUserAuditLogChange=Activar logs de auditoría para operaciones de modificación y mantener logs para
#XMSG: Cloud Platform Access
cloudPlatformAccess=Acceso a Cloud Platform
#XFLD: Enable Consumption in Cloud Platform hint
enableConsumptionCloudPlatformHint=Configure el acceso a su contenedor de HANA Deployment Infrastructure (HDI) a través de este usuario de la base de datos. Para conectarse a su contenedor de HDI, el modelado SQL debe estar activado
#XFLD: Enable HDI Consumption
enableConsumptionCloudPlatform=Activar consumo de HDI
#XFLD: Enable Consumption hint
enableConsumptionHint=¿Desea que otras herramientas o aplicaciones puedan consumir datos de su espacio?
#XFLD: Enable Consumption
enableConsumption=Activar consumo SQL
#XFLD: Enable Modeling
enableModeling=Activar modelado SQL
#XMSG: Privileges for Data Modeling
privilegesModeling=Ingesta de datos
#XMSG: Privileges for Data Consumption
privilegesConsumption=Consumo de datos para herramientas externas
#XFLD: SQL Modeling
sqlModeling=Modelado SQL
#XFLD: SQL Consumption
sqlConsumption=Consumo SQL
#XFLD: enabled
enabled=Activado
#XFLD: disabled
disabled=Desactivado
#XFLD: Edit Privileges
editPrivileges=Editar autorizaciones
#XFLD: Open Database Explorer
openDBX=Abrir explorador de bases de datos
#XFLD: create database user hint
databaseCreateHint=Tenga en cuenta que no se podrá volver a modificar el nombre de usuario después de guardar los datos.
#XFLD: Internal Schema Name
internalSchemaName=Nombre de esquema interno
#YMSE: Failed to load database users
loadDatabaseUserError=Error al cargar los usuarios de base de datos
#YMSE: Failed to delete database user
deleteDatabaseUsersError=Error al eliminar los usuarios de base de datos
#XMSG: Success message after deleting database user
deleteDatabaseUserSuccess=Se ha eliminado el usuario de base de datos
#XMSG: Success message after deleting database users
deleteDatabaseUsersSuccess=Se han eliminado los usuarios de base de datos
#XMSG: Success message after creating database user
createDatabaseUserSuccessMessage=Se ha creado el usuario de base de datos
#YMSE: Failed to create database user
createDatabaseUserError=Error al crear el usuario de base de datos
#XMSG: Success message after updating database user
updateDatabaseUserSuccessMessage=Se ha actualizado el usuario de base de datos
#YMSE: Failed to update database user
updateDatabaseUserError=Error al actualizar el usuario de base de datos
#XFLD: HDI Consumption
hdiConsumption=Consumo de HDI
#XTIT: Space detail section Database Access title
detailsSectionDatabaseAccess=Acceso a base de datos
#XTXT: Database users consumption hint
databaseUsersConsumptionHint=Haga que los datos de su espacio sean consumibles de forma predeterminada. Los modelos de los generadores permitirán automáticamente que los datos se puedan consumir.
#XFLD: Default Consumption of Space Data:
defaultConsumptionOfSpaceData=Consumo de datos de espacio predeterminado:
#XFLD: Database User Name
databaseUserName=Nombre de usuario de base de datos
#XMSG: Database User creation validation error message
databaseUserValidationError=Parece que algunos campos no son válidos. Verifique los campos obligatorios para continuar.
#XMSG: SQL modeling can’t be enabled since this user has been migrated.
migratedDatabaseUserHint=La ingesta de datos no se puede activar porque este usuario se ha migrado.
#XBUT: Remove Button Text
remove=Quitar
#XBUT: Remove Spaces Button Text
removeSpaces=Quitar espacios
#XBUT: Remove Objects Button Text
removeObjects=Quitar objetos
#XMSG: No members have been added yet.
noMembersAssigned=Aún no se han añadido miembros.
#XMSG: No users have been added yet.
noUsersAssigned=Aún no se han añadido usuarios.
#XMSG: No database users have been created or your filter is not showing any data.
noDatabaseUsers=No se ha creado ningún usuario de base de datos o su filtro no muestra ningún dato.
#XMSG: Please enter a user name.
noDatabaseUsername=Introduzca un nombre de usuario.
#XMSG: The user name is too long. Please use a shorter one.
databaseUsernameTooLong=El nombre de usuario es demasiado largo. Utilice uno más corto.
#XMSG: No privileges have been enabled and this database user will have limited functionality. Do you still want to continue?
noPrivilegesConfirmationMessage=No se han activado autorizaciones, por lo que este usuario de base de datos tendrá una funcionalidad limitada. ¿Desea continuar de todos modos?
#XMSG: To enable audit logs for change operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogChangeMessage=Para activar los logs de auditoría para operaciones de modificación, es preciso activar también la ingesta de datos. ¿Desea hacerlo?
#XMSG: To enable audit logs for read operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogReadMessage=Para activar los logs de auditoría para operaciones de lectura, es preciso activar también la ingesta de datos. ¿Desea hacerlo?
#XMSG: To enable HDI consumption data ingestion and data consumption needs to be enabled. Do you want to enable all?
enableHDIConsumptionMessage=Para activar el consumo de HDI, es preciso activar también la ingesta y el consumo de datos. ¿Desea hacerlo?
#XMSG:
databaseUserPasswordText=Para configurar una conexión con este usuario de la base de datos, copie su contraseña. En caso de que olvide su contraseña, siempre puede solicitar una nueva.
#XTIT: Space detail section members title
detailsSectionMembers=Miembros
#XMSG: New password set
newPasswordSet=Se ha establecido la nueva contraseña
#XFLD: Data Ingestion
dataIngestion=Ingesta de datos
#XFLD: Data Consumption
dataConsumption=Consumo de datos
#XFLD: Privileges
privileges=Autorizaciones
#XFLD: Enable Data ingestion
enableDataIngestion=Activar ingesta de datos
#XMSG: Log the read and change operations for data ingestion.
databaseUserAuditHint=Registre en el log las operaciones de lectura y modificación para la ingesta de datos.
#XMSG: Make your space data available in your HDI containers.
databaseUserHDIHint=Permita que los datos de su espacio estén disponibles en sus contenedores de HDI.
#XFLD: Enable Data consumption
enableDataConsumption=Activar consumo de datos
#XMSG: Allow other apps or tools to consume your space data.
enableDataConsumptionHint=Permita que otras aplicaciones o herramientas consuman los datos de su espacio.
#XMSG: To set up access via this schema user copy the credentials to your user-provided service. In case you can only copy the credentials without a password make sure to add the password later.
databaseUserHDIConsumptionHint=Para configurar el acceso mediante este usuario de base de datos, copie las credenciales en su servicio proporcionado por el usuario. En caso de que solo pueda copiar las credenciales sin contraseña, asegúrese de añadir la contraseña más tarde.
#XFLD: Data Flow Runtime Capacity label in space overview
dataFlowRuntimeCapacity=Capacidad de tiempo de ejecución del flujo de datos ({0}:{1} horas de {2} horas)
#XFLD: Could not load Data Flow Runtime Capacity
dataFlowRuntimeCapacityError=No se ha podido cargar la capacidad de tiempo de ejecución del flujo de datos
#XMSG: User can grant data consumption to other users.
databaseUserConsumptionWithGrantOptionHint=El usuario puede conceder el consumo de datos a otros usuarios.
#XFLD: Enable data consumption with grant option
enableConsumptionWithGrantOption=Activar consumo de datos con opción de concesión
#XMSG: To enable data consumption with grant option data consumption needs to be enabled. Do you want to enable both?
enableConsumptionWithGrantMessage=Para activar el consumo de datos con la opción de concesión, se debe activar el consumo de datos. ¿Desea activarlos ambos?
#XFLD: Enable Automated Predictive Library (APL) and Predictive Analysis Library (PAL)
allowUsingScriptServerFunctionality=Activar Biblioteca predictiva automatizada (APL) y Biblioteca de análisis predictivos (PAL)
#XMSG: User can use SAP HANA Cloud embedded machine learning functions.
allowUsingScriptServerFunctionalityHint=El usuario puede utilizar las funciones de aprendizaje automático incrustadas en SAP HANA Cloud.
#XFLD: Password Policy
passwordPolicy=Política de contraseñas
#XMSG: Password Policy
passwordPolicyHint=Active o desactive aquí la política de contraseñas configurada.
#XFLD: Enable Password Policy
enablePasswordPolicy=Activar política de contraseñas
#XMSG: Read Access to the Space Schema
readAccessTitle=Acceso de lectura al esquema de espacios
#XMSG: read access hint
readAccessHint=Permita que el usuario de base de datos conecte herramientas externas al esquema de espacios y vistas de lectura expuestas para su consumo.
#XFLD: Space Schema
spaceSchema=Esquema de espacios
#XFLD: Enable Read Access (SQL)
enableReadAccess=Activar acceso de lectura (SQL)
#XMSG: Allow the user to grant read access to other users.
withGrantOptionHint=Permita que el usuario conceda acceso de lectura a otros usuarios.
#XFLD: With Grant Option
withGrantOption=Con opción de concesión
#XMSG: Make your space data available in your HDI containers.
enableHDIConsumptionHint=Permita que los datos de su espacio estén disponibles en sus contenedores de HDI.
#XFLD: Enable HDI Consumption
enableHDIConsumption=Activar consumo de HDI
#XMSG: Write Access to the User’s Open SQL Schema
writeAccessTitle=Acceso de escritura al esquema Open SQL del usuario
#XMSG: write access hint
writeAccessHint=Permita que el usuario de base de datos conecte herramientas externas al esquema Open SQL del usuario para crear entidades de datos e ingerir datos para utilizar en el espacio.
#XFLD: Open SQL Schema
openSQLSchema=Esquema Open SQL
#XFLD: Enable Write Access (SQL DDL & DML)
enableWriteAccess=Activar acceso de escritura (SQL, DDL y DML)
#XMSG: audit hint
auditHint=Registre las operaciones de lectura y modificación realizadas en el esquema Open SQL.
#XMSG: data consumption hint
dataConsumptionHint=Exponga todas las vistas nuevas del espacio para su consumo de forma predeterminada. Los modeladores podrán sustituir esta opción en vistas concretas con el conmutador "Exponer para consumo" del panel lateral de salida de la vista. También puede elegir los formatos de exposición de las vistas.
#XFLD: Expose for Consumption by Default
exposeDataConsumption=Exponer para consumo de forma predeterminada
#XMSG: database users hint consumption hint
databaseUsersHint2New=Cree usuarios de base de datos para conectar herramientas externas con SAP Datasphere. Establezca autorizaciones para leer datos de espacios, así como para crear entidades de datos (DDL) e ingerir datos (DML) para utilizar en el espacio.
#XFLD: Read
read=Leer
#XFLD: Read (HDI)
readHDI=Leer (HDI)
#XFLD: Write
write=Escribir
#XMSG: HDI Containers Hint
HDIContainersHint2=Active el acceso a los contenedores de SAP HANA Deployment Infrastructure (HDI) en su espacio. Los modeladores pueden utilizar artefactos HDI como fuentes de vistas, y los clientes HDI pueden acceder a los datos de su espacio.
#XMSG: Open info dialog
openDatabaseUserInfoDialog=Abrir el diálogo de información
#XMSG: Database user is locked. Open info dialog to unlock
openLockedDatabaseUserInfoDialog=El usuario de base de datos está bloqueado. Para desbloquearlo, abra el diálogo.
#XFLD: Table
table=Tabla
#XFLD: Space section partner configuration title
partnerConnectionConfigurationSectionTitle=Conexión de socio
#XFLD: Space section partner configuration subtitle
partnerConnectionConfigurationSectionSubTitle=Configuración de conexión de socio
#XFLD: Space section partner configuration hint
partnerConnectionConfigurationSectionHint=Defina su propio mosaico de conexión de socio añadiendo su URL e icono de iFrame. Esta configuración solo está disponible para este arrendatario.
#XFLD: Table Name Field
partnerConnectionConfigurationName=Nombre de mosaico
#XFLD: Table iFrame URL field
partnerConnectionConfigurationiFrameURL=URL de iFrame
#XFLD: Table iFrame Post Message Origin field
partnerConnectionConfigurationiFramePostMessageOrigin=Origen de mensaje post de iFrame
#XFLD: Table icon/image name
partnerConnectionConfigurationIcon=Icono
#XMSG: Table no result found
partnerConnectionConfigurationNotfound=No se ha encontrado ninguna configuración de conexión de socio.
#XMSG: Partner Connection Confgurations are not available
partnerConnectionConfigurationNotAvailable=No se pueden visualizar las configuraciones de conexión de socio cuando la base de datos de tiempo de ejecución no está disponible.
#XFLD: Dialog Create Partner Connection Configuration
partnerConnectionConfigurationDialogTitleAdd=Crear configuración de conexión de socio
#XFLD: Dialog Create Partner Connection Configuration Upload Icon
partnerConnectionConfigurationButtonUloadPartnerIcon=Cargar icono
#XFLD: Dialog Create Partner Connection Configuration Select
partnerConnectionConfigurationSelectIcon=Seleccionar (tamaño máximo: 200 KB)
#XFLD: Dialog Create Partner Connection Configuration Enter a name
partnerConnectionConfigurationNamePlaceholder=Ejemplo de mosaico de socio
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL
partnerConnectionConfigurationiFrameURLPlaceholder=http://www.ejemplo.com
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL Check
partnerConnectionConfigurationiFramePostMessageOriginPlaceholder=.ejemplo.com
#XFLD: Dialog Create Partner Connection Configuration Browse
partnerConnectionConfigurationBrowseButton=Examinar
#XFLD: Create Partner Connection Configuration Msg
partnerConnectionConfigurationUploadMsg=La configuración de conexión de socio se ha creado correctamente.
#XMSG: Error while deleting Partner Connection configuration(s)
partnerConnectionConfigurationDeleteError=Se ha producido un error al eliminar la configuración de conexión de socio.
#XMSG: Partner Connection Configuration has been successfully deleted
partnerConnectionConfigurationDeleteSuccess=La configuración de conexión de socio se ha eliminado correctamente.
#XMSG: Error while retrieving Partner Connection Configurations
partnerConnectionConfigurationLoadError=Se ha producido un error al recuperar las configuraciones de conexión de socio.
#XMSG: Error fileSizeLimitExceeded
partnerConnectionConfigurationfileSizeLimitExceeded=El archivo no se ha podido cargar porque supera el tamaño máximo de 200 KB.
#XBUT: Create Partner Connection Configuration
partnerConnectionConfigurationButtonCreate=Crear configuración de conexión de socio
#XBUT: Delete Partner Connection Configuration
partnerConnectionConfigurationButtonDelete=Eliminar configuración de conexión de socio
#XMSG: Partner Tile Creation Failure
upsertPartnerTileFailed=No se ha podido crear el mosaico de socio.
#XMSG: Partner Tile Deletion Failure
deletePartnerTileFailed=No se ha podido eliminar el mosaico de socio.
#XMSG: HANA cloud connector settings reset failure
resetHANASCCSettingsFailed=Se ha producido un error al restablecer las opciones del conector de la nube de SAP HANA
#XFLD: Workload Class
workloadClass=Clase de carga de trabajo
#XFLD: Workload Management
workloadManagement=Gestión de la carga de trabajo
#XFLD: Priority
workloadClassPriority=Prioridad
#XMSG:
workloadManagementPriorityHint=Puede especificar la prioridad de este espacio al consultar la base de datos. Introduzca un valor comprendido entre 1 (la prioridad más baja) y 8 (la prioridad más alta). En una situación en la que los espacios compiten por los threads disponibles, los que tengan mayor prioridad se ejecutarán antes que los espacios con menor prioridad.
#XMSG:
workloadClassPriorityHint=Puede especificar la prioridad del espacio de 0 (la más baja) a 8 (la más alta). Las declaraciones de un espacio con una prioridad alta se ejecutan antes que las declaraciones de otros espacios que tengan una prioridad más baja. La prioridad predeterminada es 5. Como el valor 9 está reservado a las operaciones del sistema, no está disponible para un espacio.
#XFLD: Statement Limits
workloadclassStatementLimits=Límites de declaración
#XFLD: Workload Configuration
workloadConfiguration=Configuración de la carga de trabajo
#XMSG:
workloadClassStatementLimitsHint=Puede especificar el número máximo (o porcentaje) de threads y GB de memoria que pueden consumir las declaraciones que se ejecutan simultáneamente en el espacio. Puede introducir cualquier valor o porcentaje entre 0 (sin límite) y la memoria total y los threads disponibles en el arrendatario. \n\n Si especifica un límite de threads, tenga en cuenta que puede reducir el rendimiento. \n\n Si especifica un límite de memoria, las declaraciones que alcanzan el límite de memoria no se ejecutan.
#XMSG:
workloadClassStatementLimitsDescription=La configuración predeterminada proporciona unos límites de recursos generosos, a la vez que evita que un solo espacio sobrecargue el sistema.
#XMSG:
workloadClassStatementLimitCustomDescription=Puede definir los límites máximos de threads y de memoria que pueden consumir las declaraciones que se ejecutan simultáneamente en el espacio.
#XMSG:
totalStatementThreadLimitHelpText=Definir un límite de threads demasiado bajo puede afectar al rendimiento de las declaraciones, mientras que unos valores excesivamente altos o 0 pueden hacer que el espacio consuma todos los threads disponibles del sistema.
#XMSG:
totalStatementMemoryLimitHelpText=Definir un límite de memoria demasiado bajo puede causar problemas de falta de memoria, mientras que unos valores excesivamente altos o 0 pueden hacer que el espacio consuma toda la memoria disponible del sistema.
#XMSG:
totalStatementThreadLimitHelpTextRestricted=Introduzca un porcentaje entre 1% y 70% (o el número equivalente) de la cantidad total de threads disponibles en su arrendatario. Si define el límite de threads demasiado bajo, puede repercutir en el rendimiento de la sentencia, mientras que si lo define demasiado alto, puede repercutir en el rendimiento de las sentencias en otros espacios.
#XMSG:
totalStatementThreadLimitHelpTextDynamic=Introduzca un porcentaje entre 1% y {0}% (o el número equivalente) de la cantidad total de threads disponibles en su arrendatario. Si define el límite de threads demasiado bajo, puede repercutir en el rendimiento de la sentencia, mientras que si lo define demasiado alto, puede repercutir en el rendimiento de las sentencias en otros espacios.
#XMSG:
totalStatementMemoryLimitHelpTextNew=Introduzca un valor de porcentaje entre 0 (sin límite) y la cantidad total de memoria disponible en su arrendatario. Si define el límite de memoria demasiado bajo, puede repercutir en el rendimiento de la sentencia, mientras que si lo define demasiado alto, puede repercutir en el rendimiento de las sentencias en otros espacios.
#XFLD: Total Statement Thread Limit
totalStatementThreadLimit=Límite total de threads de declaración
#XFLD: Segmented button label
workloadclassPercentage=%
#XFLD: Segmented button label
workloadclassGigaByte=GB
#XFLD, 80: Segmented button label
workloadclassThreads=Threads
#XFLD: Total Statement Memory Limit
totalStatementMemoryLimit=Límite total de memoria de declaración
#YMSE: Error while loading customer hana info
loadCustomerHanaInfoError=No se ha podido cargar la información de cliente de SAP HANA.
#XMSG:
minimumLimitReached=Se ha alcanzado el límite mínimo.
#XMSG:
maximumLimitReached=Se ha alcanzado el límite máximo.
#XMSG: Name Taken for Technical Name
technical-name-taken=Ya existe una conexión con el nombre técnico especificado. Introduzca otro nombre.
#XMSG: Name Too long for Technical Name
technical-name-too-long=El nombre técnico supera los 40 caracteres. Introduzca uno más corto.
#XMSG: Technical name field empty
technical-name-field-empty=Introduzca un nombre técnico.
#XMSG: Generic error when user enters invalid characters for a field
field-invalid-characters=Solo puede usar letras (a-z), números (0-9) y guiones bajos (_) para el nombre.
#XMSG: Generic error when user enters invalid initial characters for a field
field-underscore-trim=El nombre introducido no puede empezar o finalizar con un guion bajo (_).
#XFLD: Checkbox label for Enable Statement Limits
enableStatementLimits=Activar límites de declaración
#XFLD: Connection SubSection Settings Title in space details
connectionSettings=Opciones
#XMSG: Connections tool hint in Space details section
connectionsToolHint=Para crear o editar conexiones, abra la aplicación Conexiones desde la navegación lateral o haga clic aquí:
#XBUT: Connection list Open Connection Tool Button
button-connectiontool=Ir a Conexiones
#XFLD: Not deployed label on space tile
notDeployedLabel=Aún no se ha desplegado el espacio.
#XFLD: Not deployed additional text on space tile
notDeployedText=Despliegue el espacio.
#XFLD: Corrupt space label on space tile
corruptSpace=Algo ha salido mal.
#XFLD: Corrupt space additional text on space tile
corruptSpaceHint=Intente volver a desplegar o póngase en contacto con el soporte
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Datos de log de auditoría
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Datos administrativos
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Otros datos
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Datos en espacios
#XMSG: Unlock Space confirmation text
unlockSpaceConfirmation=¿Realmente desea desbloquear el espacio?
#XMSG: Lock Space confirmation text
lockSpaceConfirmation=¿Seguro que desea bloquear del espacio?
#XFLD: Lock
lock=Bloquear
#XFLD: Unlock
unlock=Desbloquear
#XFLD: Locking
locking=Bloqueando
#XMSG: Success message after locking space
lockSpaceSuccessMsg=Espacio bloqueado
#XMSG: Success message after unlocking space
unlockSpaceSuccessMsg=Espacio desbloqueado
#XMSG: Success message after locking spaces
lockSpacesSuccessMsg=Espacios bloqueados
#XMSG: Success message after unlocking spaces
unlockSpacesSuccessMsg=Espacios desbloqueados
#YMSE: Error while locking a space
lockSpaceError=El espacio no se puede bloquear.
#YMSE: Error while unlocking a space
unlockSpaceError=El espacio no se puede desbloquear.
#XTIT: popup title Warning
confirmationWarningTitle=Advertencia
#XMSG: Message strip message when a space was locked manually
manualLockedMessage=El espacio se ha bloqueado manualmente.
#XMSG: Message strip message when a space was locked due to the amount of generated audit logs
auditLogLockedMessage=El sistema ha bloqueado el espacio porque los registros de auditoría consumen una gran cantidad de GB de disco.
#XMSG: Message strip message when a space was locked because the assigned quota was exceeded.
quotaLockedMessage=El sistema ha bloqueado el espacio porque excede sus asignaciones de almacenamiento en memoria o en disco.
#XMSG: Unlock Spaces confirmation text
unlockSpacesConfirmation=¿Realmente desea desbloquear los espacios seleccionados?
#XMSG: Lock Spaces confirmation text
lockSpacesConfirmation=¿Seguro que desea bloquear los espacios seleccionados?
#XTIT: Scoped role editor dialog title
scopedRoleEditorDialogTitle=Editor de roles dentro del alcance
#XTIT: ECN Management title
ecnManagementTitle=Gestión de espacios y nodos de computación elásticos
#XFLD: ECNs
ecns=Nodos de computación elásticos
#XFLD: ECN phase Ready
ecnReady=Listo
#XFLD: ECN phase Running
ecnRunning=En ejecución
#XFLD: ECN phase Initial
ecnInitial=No listo
#XFLD: ECN phase Starting
ecnStarting=Iniciando
#XFLD: ECN phase Starting Failed
ecnStartingFailed=No se ha podido iniciar
#XFLD: ECN phase Stopping
ecnStopping=Parando
#XFLD: ECN phase Stopping Failed
ecnStoppingFailed=No se ha podido detener
#XBTN: Assign Button
assign=Añadir espacios
#XBTN: Start Header-Button
start=Iniciar
#XBTN: Update Header-Button
repair=Actualizar
#XBTN: Stop Header-Button
stop=Detener
#XFLD: ECN hours remaining
ecnHoursRemaining=1000 horas restantes
#XFLD: ECN block-hours remaining
ecnBlockHoursRemaining={0} horas de bloque restantes
#XFLD: ECN block-hour remaining
ecnBlockHourRemaining={0} hora de bloque restante
#XTIT: ECN creation dialog title
ecnCreationDialogTitle=Crear nodo de computación elástico
#XTIT: ECN edit dialog title
ecnEditDialogTitle=Editar nodo de computación elástico
#XTIT: ECN deletion dialog title
ecnDeletionDialogTitle=Eliminar nodo de computación elástico
#XTIT: ECN Space assignment dialog title
ecnSpaceAssignmentDialogTitle=Añadir espacios
#XFLD: ECN ID
ECNIDLabel=Nodo de computación elástico
#XTXT: Selected toolbar text
selectedToolbarText=Selección: {0}
#XTIT: Elastic Compute Nodes
ECNslong=Nodos de computación elásticos
#XTIT: Object assignment - Table header: Objects available
objectsAvailable=Cantidad de objetos
#XTIT: Object assignment - Dialog header text
selectObjects=Seleccione los espacios y objetos que desea añadir al nodo de computación elástico:
#XTIT: Object assignment - Table header title: Objects
objects=Objetos
#XTIT: Object assignment - Table header: Type
type=Tipo
#XMSG: Message strip delete database user audit warning
deleteDatabaseUserWarningMessageStrip=Tenga en cuenta que, al eliminar un usuario de la base de datos, se eliminarán todas las entradas del log de auditoría generadas. Si desea conservar estos logs, considere la posibilidad de exportarlos antes de eliminar el usuario de la base de datos.
#XMSG: Message strip delete HDI container audit logs impact
deleteHDIContainerWarningMessageStrip=Tenga en cuenta que, al anular la asignación de un contenedor HDI del espacio, se eliminarán todas las entradas del log de auditoría generadas. Si desea conservar estos logs, considere la posibilidad de exportarlos antes de anular la asignación del contenedor HDI.
#XTXT: All audit logs
allAuditLogs=Todas las entradas del log de auditoría generadas para el espacio
#XMSG: Message strip disable audit warning
disableAuditWarningMessageStrip=Tenga en cuenta que, al desactivar una política de auditoría (operaciones de lectura o modificación), se eliminarán todas sus entradas del log de auditoría. Si desea conservar dichas entradas, considere la posibilidad de exportarlas antes de desactivar la política de auditoría.
#XTIT: No Spaces assigned to ECN Illustration title
noSpacesECNTitle=Aún no se ha asignado ningún espacio u objeto
#XTIT: No Spaces assigned to ECN Illustration description
noSpacesECNDescription=Para empezar a trabajar con su nodo de cálculo elástico, añádele un espacio u objetos.
#XTIT: No Spaces Illustration title
noSpacesTitle=No se ha creado ningún espacio todavía
#XTIT: No Spaces Illustration description
noSpacesDescription=Para empezar a adquirir datos, cree un espacio.
#XTIT: No Spaces Illustration title
noDeletedSpacesTitle=La papelera de reciclaje está vacía
#XTIT: No Spaces Illustration description
noDeletedSpacesDescription=Puede recuperar sus espacios eliminados desde aquí.
#XTXT:  {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDatabaseUsers=Una vez desplegado el espacio, los siguientes usuarios de la base de datos se eliminarán {0} y no se podrán recuperar:
#XTIT: Delete database users
deleteDatabaseUsersTitle=Eliminar usuarios de la base de datos
#YMSE: Error if ecn id already exists
ecnIDInputDuplicateError=El ID ya existe.
#YMSE: Error if ecn id contains invalid characters
ecnIDInputInvalidError=Utilice solo minúsculas (a - z) y números (0 - 9).
#YMSE: Error if ecn id is too short
ecnIDInputTooShortError=El ID debe tener {0} caracteres como mínimo.
#XMSG: ecn id length warning
ecnIdLengthWarning=Se ha superado el máximo de {0} caracteres.
#XFLD: open System Monitor
systemMonitor=Monitor de sistema
#XFLD: open ECN schedule dialog menu entry
schedule=Programación
#XFLD: open create ECN schedule dialog
createSchedule=Crear programa
#XFLD: open change ECN schedule dialog
changeSchedule=Editar programación
#XFLD: open delete ECN schedule dialog
deleteSchedule=Eliminar programación
#XFLD: open self assign ECN schedule dialog
selfAssignSchedule=Asignarme la programación
#XFLD: open pause ECN schedule dialog
pauseSchedule=Interrumpir programación
#XFLD: open resume ECN schedule dialog
resumeSchedule=Reanudar programación
#XFLD: View Logs
viewLogs=Ver logs
#XFLD: Compute Blocks
computeBlocks=Bloques de capacidad informática
#XFLD: Memory label in ECN creation dialog
ecnMemory=Memoria (GB)
#XFLD: Storage label in ECN creation dialog
ecnStorage=Almacenamiento (GB)
#XFLD: Number of CPU label in ECN creation dialog
ecnCPUCount=Número de CPU
#XFLD: ECN updated by label
changedBy=Modificado por
#XFLD: ECN updated on label
changedOn=Modificado el
#XMSG: Success message after creating a Elastic Compute Node
createEcnSuccessMessage=Se ha creado el nodo de computación elástico
#YMSE: Error while creating a Elastic Compute Node
createEcnError=No se ha podido crear el nodo de computación elástico
#XMSG: Success message after updating a Elastic Compute Node
updateEcnSuccessMessage=Se ha actualizado el nodo de computación elástico
#XMSG: Error while updating a Elastic Compute Node
udpateEcnError=No se ha podido actualizar el nodo de computación elástico
#XMSG: Success message after deleting a Elastic Compute Node
deleteEcnSuccessMessage=Se ha eliminado el nodo de computación elástico
#YMSE: Error while deleting a Elastic Compute Node
deleteEcnError=No se ha podido eliminar el nodo de computación elástico
#XMSG: Success message after starting a Elastic Compute Node
startingEcnSuccessMessage=Iniciando el nodo de computación elástico
#XMSG: Success message after stopping a Elastic Compute Node
stoppingEcnSuccessMessage=Deteniendo el nodo de computación elástico
#YMSE: Error while starting a Elastic Compute Node
startingEcnError=No se ha podido iniciar el nodo de computación elástico
#YMSE: Error while stopping a Elastic Compute Node
stoppingEcnError=No se ha podido detener el nodo de computación elástico
#XBUT: Add Object button for an ECN
assignObjects=Añadir objetos
#XFLD: Enable auto object assignment label
enableAutoObjAssignment=Añadir todos los objetos automáticamente
#XFLD: object type label to be assigned
objectTypeLabel=Tipo (uso semántico)
#XFLD: assigned object type label
assignedObjectTypeLabel=Tipo
#XFLD: technical name label
TechnicalNameLabel=Nombre técnico
#XFLD: Object selecttion ECN label
objectSelectionECNLabel=Seleccione los objetos que desea añadir a este nodo de cálculo elástico.
#XTIT: Add objects dialog title
assignObjectsTitle=Añadir objetos de
#XFLD: object label with object count
objectLabel=Objeto
#XMSG: No objects available to add message.
noObjectsToAssign=No hay objetos disponibles para añadir.
#XMSG: No objects assigned message.
noAssignedObjects=No se ha asignado ningún objeto.
#XTIT: Remove objects popup title
removeObjectsOrSpacesConfirmationTitle=Advertencia
#XTIT: Delete ECN popup button and title
deleteECNConfirmationButtonAndTitle=Eliminar
#XMSG: Remove objects popup text
removeObjectsConfirmation=¿Seguro que desea quitar los objetos seleccionados?
#XMSG: Remove spaces popup text
removeSpacesConfirmation=¿Seguro que desea quitar los espacios seleccionados?
#XBUT: Confirmation button for objects dialog
removeObjectsOrSpacesConfirmationBtn=Quitar espacios
#XMSG: Success message after removing objects
removeObjectsSuccessMessage=Se han quitado los objetos expuestos
#XMSG: Success message after assigning objects
assignObjectsSuccessMessage=Se han asignado los objetos expuestos
#XFLD: auto object assignment label for space tile
autoObjAssignmentLabel=Todos los objetos expuestos
#XFLD: Spaces tab label
spacesTabLabel=Espacios
#XFLD: Exposed objects tab label
consumptionObjectsTabLabel=Objetos expuestos
#XMSG: Success message after unassigning spaces
spacesUnassignedSuccessMessage=Se han quitado espacios
#XMSG: Success message after unassigning space
spaceUnassignedSuccessMessage=Se ha quitado el espacio
#YMSE: Error while assigning / unassigning spaces
spacesAssignUnassignError=No se han podido asignar o quitar espacios.
#YMSE: Error while removing objects
removeObjectsError=No hemos podido asignar ni quitar los objetos.
#YMSE: Error while removing object
removeObjectError=No hemos podido asignar ni quitar el objeto.
#YMSE: Error when saving the ecn with invalid value
invalidSizingPlanError=El número seleccionado ya no es válido. Seleccione uno válido.
#YMSE: Error when saving the ecn with invalid value
invalidPerformanceClassError=Seleccione una clase de rendimiento válida.
#YMSE: Error when saving the ecn with invalid value
performanceClassNotAvailableError=La clase de rendimiento seleccionada anteriormente "{0}" no es válida actualmente. Seleccione la clase de rendimiento válida.
#XTXT: Delete ECN confirmation text
deleteConfirmationTxtECN=¿Seguro que desea eliminar el nodo de cálculo elástico?
#XFLD: tooltip for ? button
help=Ayuda
#XFLD: ECN edit button label
editECN=Configurar
#XFLD: Technical type label for ERModel
DWC_ERMODEL=Modelo entidad-relación
#XFLD: Technical type label for Local Table
DWC_LOCAL_TABLE=Tabla local
#XFLD: Technical type label for Remote Table
DWC_REMOTE_TABLE=Tabla remota
#XFLD: Technical type label for Analytic Model
DWC_ANALYTIC_MODEL=Modelo analítico
#XFLD: Technical type label for Task Chain
DWC_TASKCHAIN=Cadena de tareas
#XFLD: Technical type label for Data Flow
DWC_DATAFLOW=Flujo de datos
#XFLD: Technical type label for Replication Flow
DWC_REPLICATIONFLOW=Flujo de replicación
#XFLD: Technical type label for Transformation Flow
DWC_TRANSFORMATIONFLOW=Flujo de transformación
#XFLD: Technical type label for Intelligent Lookup
DWC_IDT=Búsqueda inteligente
#XFLD: Technical type label for Repository
DWC_REPOSITORY_PACKAGE=Repositorio
#XFLD: Technical type label for Enterprise Search
DWC_ESMODEL=Búsqueda empresarial
#XFLD: Technical type label for View
DWC_VIEW=Vista
#XFLD: Technical type label for Data Product
DWC_DATAPRODUCT=Producto de datos
#XFLD: Technical type label for Data Access Control
DWC_DAC=Control de acceso a datos
#XFLD: Technical type label for Folder
DWC_FOLDER=Carpeta
#XFLD: Technical type label for Data Entity
DWC_BUSINESS_ENTITY=Entidad empresarial
#XFLD: Technical type label for Business Entity Variant
DWC_BUSINESS_ENTITY_VARIANT=Variante de entidad empresarial
#XFLD: Technical type label for Responsibility Scenario
DWC_AUTH_SCENARIO=Escenario de responsabilidad
#XFLD: Technical type label for Fact Model
DWC_FACT_MODEL=Modelo de hechos
#XFLD: Technical type label for Transformation Flow
DWC_PERSPECTIVE=Perspectiva
#XFLD: Technical type label for Consumption Model
DWC_CONSUMPTION_MODEL=Modelo de consumo
#XFLD: Technical type label for Remote Connection
DWC_REMOTECONNECTION=Conexión remota
#XFLD: Technical type label for Fact Model Variant
DWC_FACT_MODEL_VARIANT=Variante de modelo de hechos
#XMSG: Schedule created alert message
createScheduleSuccess=Se ha creado la programación
#XMSG: Schedule updated alert message
updateScheduleSuccess=Se ha actualizado la programación
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Se ha eliminado la programación
#XMSG: Schedule takeover success message
ownerChangeScheduleSuccess=Se le ha asignado la programación
#XMSG: Message for starting pausing of schedule
pauseScheduleStarted=Se está interrumpiendo 1 programación
#XMSG: Message for starting resuming of schedule
resumeScheduleStarted=Se está reanudando 1 programación
#XFLD: Segmented button label
availableSpacesButton=Disponible
#XFLD: Segmented button label
selectedSpacesButton=Seleccionado
#XFLD: Visit website button text
visitWebsite=Visitar sitio web
#XMSG: Warning message on unselecting source language
sourceLanguageRemovalWarning=Se eliminará el idioma de origen seleccionado previamente.
#XBUT: enable translation confirmation button
enableTranslationConfirmationBtn=Activar
#XFLD: ECN performance class label
performanceClassLabel=Clase de rendimiento
#XTXT performance class memory text
memoryText=Memoria
#XTXT performance class compute text
computeText=Cálculo
#XTXT performance class high-compute text
highComputeText=Cálculo alto
#XBUT: Recycle Bin Button Text
recycleBin=Papelera de reciclaje
#XBUT: Restore Button Text
restore=Restaurar
#XMSG: Warning message for new Workload Management UI
priorityWarning=Esta zona es de solo lectura. Puede modificar la prioridad del espacio en el área Sistema/Configuración/Gestión de la carga de trabajo.
#XMSG: Warning message for new Workload Management UI
workloadWarning=Esta zona es de solo lectura. Puede modificar la configuración de la carga de trabajo del espacio en el área Sistema/Configuración/Gestión de la carga de trabajo.
#XCOL: Space table-view column: Apache Spark vCPUs
sparkManagementVCPUs=vCPUs de Apache Spark
#XCOL: Space table-view column: Apache Spark Memory (GB)
sparkManagementMemory=Memoria de Apache Spark (GB)
#XCOL: Space type (from Space Capability) for Data Ingestion Spaces
ingestionSpaceType=Ingesta de producto de datos
#XFLD: Deployment in progress nodatatext for spark table
sparkTableDeploymentInProgress=No hay datos disponibles ya que el espacio se encuentra actualmente en proceso de despliegue.
#XFLD: Runtime data being loaded nodatatext for spark table
sparkTableDeploymentLoading=No hay datos disponibles ya que el espacio se encuentra actualmente en proceso de carga.
#XCOL: Space type (from Space Capability) for BW Private Cloud Edition
BWPCESpaceType=SAP BW
#XBUT: Go to Instance Mappings Button
navToMappingsButton=Editar asignaciones de instancia
#XFLD: SAP HANA Data Lake Storage label if less than 1GB
dataLakeStorageUsed=< 1 GB
