#DO NOT REMOVE________________________
#XFLD: Breadcrumb for space monitoring page
spaceMonitorBreadcrumb=Monitoramento
#XTXT: Type name for spaces in browser tab page title
space=Área
#_____________________________________
#XFLD: Spaces label in
spaces=Áreas
#XFLD: Manage plan button text
manageQuotaButtonText=Gerenciar plano
#XBUT: Manage resources button
manageResourcesButton=Gerenciar recursos
#XFLD: Create space button tooltip
createSpace=Criar área
#XFLD: Create
create=Criar
#XFLD: Deploy
deploy=Implementar
#XFLD: Page
page=Página
#XFLD: Cancel
cancel=Cancelar
#XFLD: Update
update=Atualizar
#XFLD: Save
save=Salvar
#XFLD: OK
ok=OK
#XFLD: days
days=Dias
#XFLD: Space tile edit button label
edit=Editar
#XFLD: Auto Assign all objects to space
autoAssign=Atribuir automaticamente
#XFLD: Space tile open monitoring button label
openMonitoring=Monitorar
#XFLD: Delete
delete=Excluir
#XFLD: Copy Space
copy=Copiar
#XFLD: Close
close=Fechar
#XCOL: Space table-view column status
status=Status
#XFLD: Space status active
activeLabel=Ativa
#XFLD: Space status locked
lockedLabel=Bloqueado
#XFLD: Space status critical
criticalLabel=Crítico
#XFLD: Space status cold
coldLabel=Frio
#XFLD: Space status deleted
deletedLabel=Excluído
#XFLD: Space status unknown
unknownLabel=Desconhecido
#XFLD: Space status ok
okLabel=Íntegro
#XFLD: Database user expired
expired=Expirado
#XFLD: deployed
deployed=Implementado
#XFLD: not deployed
notDeployed=Não implementado
#XFLD: changes to deploy
changesToDeploy=Alterações a serem implementadas
#XFLD: pending
pending=Implementando
#XFLD: designtime error
designtimeError=Erro de design
#XFLD: runtime error
runtimeError=Erro de execução
#XFLD: Space created by label
createdBy=Criado por
#XFLD: Space created on label
createdOn=Criado em
#XFLD: Space deployed on label
deployedOn=Implementado em
#XFLD: Space ID label
spaceID=ID da área
#XFLD: Priority label
priority=Prioridade
#XFLD: Space Priority label
spacePriority=Prioridade da área
#XFLD: Space Configuration label
spaceConfiguration=Configuração da área
#XFLD: Not available
notAvailable=Indisponível
#XFLD: WorkloadType default
default=Padrão
#XFLD: WorkloadType custom
custom=Personalizado
#XFLD: Data Lake option label; refers to component SAP HANA Cloud, Data Lake
dataLakeAccess=Acesso ao data lake
#XFLD: Translation label
translationLabel=Tradução
#XFLD: Source language label
sourceLanguageLabel=Idioma de origem
#XFLD: Translation CheckBox label
translationCheckBox=Ativar tradução
#XTXT: Popover hint that space has to be deployed to show user details
openDatabaseUserHintPopoverText=Implementar área para acessar os detalhes do usuário.
#XTXT: Popover hint that space has to be deployed to open database explorer
openDatabaseExplorerHintPopoverText=Implementar área para abrir o Database Explorer.
#XTXT: Text for when the Data Lake check box is disabled
dataLakeOptionDisabled=Você não pode usar esta área para acessar o data lake, ele já foi usado por outra área.
#XFLD: Data Lake check box text; refers to component SAP HANA Cloud, Data Lake
dataLakeCheckBox=Use esta área para acessar o data lake.
#XFLD: Space Priority minimum label extension
low=Baixa
#XFLD: Space Priority maximum label extension
high=Alta
#XFLD: Space name label
spaceName=Nome da área
#XFLD: Enable deploy objects checkbox
enableDeployObjects=Implementar objetos
#XTIT: Copy spaces dialog title
copySpaceDialogTitle=Copiar {0}
#XTXT Human readable text for unselected source language
unSelectedSourceLanguage=(Não selecionado)
#XTXT Human readable text for language code "af"
af=Africâner
#XTXT Human readable text for language code "ar"
ar=Árabe
#XTXT Human readable text for language code "bg"
bg=Búlgaro
#XTXT Human readable text for language code "ca"
ca=Catalão
#XTXT Human readable text for language code "zh"
zh=Chinês simplificado
#XTXT Human readable text for language code "zf"
zf=Chinês
#XTXT Human readable text for language code "hr"
hr=Croata
#XTXT Human readable text for language code "cs"
cs=Tcheco
#XTXT Human readable text for language code "cy"
cy=Galês
#XTXT Human readable text for language code "da"
da=Dinamarquês
#XTXT Human readable text for language code "nl"
nl=Holandês
#XTXT Human readable text for language code "en-UK"
en-UK=Inglês (Reino Unido)
#XTXT Human readable text for language code "en"
en=Inglês (Estados Unidos)
#XTXT Human readable text for language code "et"
et=Estoniano
#XTXT Human readable text for language code "fa"
fa=Persa
#XTXT Human readable text for language code "fi"
fi=Finlandês
#XTXT Human readable text for language code "fr-CA"
fr-CA=Francês (Canadá)
#XTXT Human readable text for language code "fr"
fr=Francês
#XTXT Human readable text for language code "de"
de=Alemão
#XTXT Human readable text for language code "el"
el=Grego
#XTXT Human readable text for language code "he"
he=Hebraico
#XTXT Human readable text for language code "hi"
hi=Hindi
#XTXT Human readable text for language code "hu"
hu=Húngaro
#XTXT Human readable text for language code "is"
is=Islandês
#XTXT Human readable text for language code "id"
id=Bahasa Indonesia
#XTXT Human readable text for language code "it"
it=Italiano
#XTXT Human readable text for language code "ja"
ja=Japonês
#XTXT Human readable text for language code "kk"
kk=Cazaque
#XTXT Human readable text for language code "ko"
ko=Coreano
#XTXT Human readable text for language code "lv"
lv=Letão
#XTXT Human readable text for language code "lt"
lt=Lituano
#XTXT Human readable text for language code "ms"
ms=Bahasa Melayu
#XTXT Human readable text for language code "no"
no=Norueguês
#XTXT Human readable text for language code "pl"
pl=Polonês
#XTXT Human readable text for language code "pt"
pt=Português (Brasil)
#XTXT Human readable text for language code "pt-PT"
pt-PT=Português (Portugal)
#XTXT Human readable text for language code "ro"
ro=Romeno
#XTXT Human readable text for language code "ru"
ru=Russo
#XTXT Human readable text for language code "sr"
sr=Sérvio
#XTXT Human readable text for language code "sh"
sh=Servo-croata
#XTXT Human readable text for language code "sk"
sk=Eslovaco
#XTXT Human readable text for language code "sl"
sl=Esloveno
#XTXT Human readable text for language code "es"
es=Espanhol
#XTXT Human readable text for language code "es-MX"
es-MX=Espanhol (México)
#XTXT Human readable text for language code "sv"
sv=Sueco
#XTXT Human readable text for language code "th"
th=Tailandês
#XTXT Human readable text for language code "tr"
tr=Turco
#XTXT Human readable text for language code "uk"
uk=Ucraniano
#XTXT Human readable text for language code "vi"
vi=Vietnamita
#XTIT: Delete spaces confirmation title
deleteConfirmationHeaderText=Excluir áreas
#XTXT: Logically delete space delete confirmation text with space name
logicallyDeleteConfirmationBodyTextWithSpaceName=Deseja realmente mover a área "{0}" para a lixeira?
#XTXT: Logically dpace delete confirmation text for multiple spaces
logicallyDeleteConfirmationBodyTextMultiple=Deseja realmente mover as {0} áreas selecionadas para a lixeira?
#XTXT: Delete space delete confirmation text with space name
deleteConfirmationBodyTextWithSpaceName=Tem certeza de que deseja excluir a área "{0}"? Essa ação não pode ser desfeita.
#XTXT: Delete space delete confirmation text for multiple spaces
deleteConfirmationBodyTextMultiple=Tem certeza de que deseja excluir as {0} áreas selecionadas? Essa ação não pode ser desfeita. O conteúdo a seguir será excluído {1}:
#XTXT: permanently
permanently=permanentemente
#XTXT: {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDetail=O conteúdo a seguir será excluído {0} e não poderá ser recuperado:
#XTXT: Hint Delete space confirmation text
deleteConfirmationHint=Digite {0} para confirmar a exclusão.
#XTXT: Delete space confirmation error check spelling
deleteConfirmationErrorCheckSpelling=Verifique a ortografia e tente novamente.
#XTXT: All Spaces
allSpaces=Todas as áreas
#XTXT: All data
allData=Todos os objetos e dados contidos na área
#XTXT: All connections
allConnections=Todas as conexões definidas na área
#XFLD: Space tile selection box tooltip
clickToSelect=Clicar para marcar
#XTXT: All database users
allDatabaseUsers=Todos os objetos e dados contidos em qualquer esquema Open SQL associado à área
#XFLD: remove members button tooltip
deleteUsers=Remover membros
#XTXT: Space long description text
description=Descrição (máximo de 4000 caracteres)
#XFLD: Add Members button tooltip
addUsers=Adicionar membros
#XFLD: Add Users button tooltip
addUsersTooltip=Adicionar usuários
#XFLD: Edit Users button tooltip
editUsersTooltip=Editar usuários
#XFLD: Remove Users button tooltip
removeUsersTooltip=Remover usuários
#XFLD: Searchfield placeholder
filter=Procurar
#XCOL: Users table-view column health
health=Integridade
#XCOL: Users table-view column access
access=Acesso
#XFLD: No user found nodatatext
noDataText=Nenhum usuário encontrado
#XTIT: Members dialog title
selectUserDialogTitle=Adicionar membros
#XTIT: User dialog title
addUserDialogTitle=Adicionar usuários
#XTIT: Delete connections confirmation title
deleteConnectionConfirmationHeaderText=Excluir conexões
#XTIT: Delete single connection confirmation title
deleteConnectionConfirmationHeaderTextSingle=Excluir conexão
#XTXT: Delete connection delete confirmation text
deleteConnectionConfirmationBodyText=Tem certeza de que deseja excluir as conexões selecionadas? Elas serão permanentemente removidas.
#XTIT: Remote source dialog title
selectRemoteConnectionDialogTitle=Selecionar conexões
#XTIT: Share connection dialog title
connectionSharingDialogTitle=Compartilhar conexão
#XTIT: Connections shared from other spaces table title
sharedConnectionsFromOtherSpaces=Conexões compartilhadas
#XFLD: Add remote source button tooltip
addRemoteConnections=Adicionar conexões
#XFLD: Delete remote source button tooltip
deleteRemoteConnections=Remover conexões
#XFLD: Share remote source button tooltip
shareConnections=Compartilhar conexões
#XFLD: Tile-layout tooltip
tileLayout=Layout de bloco
#XFLD: Table-layout tooltip
tableLayout=Layout de tabela
#XMSG: Success message after creating space
createSpaceSuccessMessage=Área criada
#XMSG: Success message after copying space
copySpaceSuccessMessage=Copiando a área "{0}" para a área "{1}"
#XMSG: Success message after deploying space
deploymentSuccessMessage=A implementação da área foi iniciada
#XMSG: Success message after Apache Spark update started
sparkUpdateStartedSuccessMessage=Atualização do Apache Spark iniciada
#YMSE: Error while starting Apache Spark update
sparkUpdateStartedErrorMessage=Falha ao atualizar o Apache Spark
#XMSG: Success message after updating space
updateSettingsSuccessMessage=Detalhes da área atualizados
#XMSG: Success message after unlocking space
unlockSpaceSuccessMessage=Área temporariamente desbloqueada
#XMSG: Success message after deleting a single space
deleteSpaceSuccessMessage=Área excluída
#XMSG: Success message after deleting multiple spaces
deleteSpacesSuccessMessage=Áreas excluídas
#XMSG: Success message after restoring a single space
restoreSpaceSuccessMessage=Área restaurada
#XMSG: Success message after restoring multiple spaces
restoreSpacesSuccessMessage=Áreas restauradas
#YMSE: Error while updating settings
updateSettingsFailureMessage=Não foi possível atualizar as configurações de área.
#YMSE: Error while updating data lake option
dataLakeEnabledInOtherSpace=O data lake já está atribuído a outra área. Somente uma área pode acessar o data lake por vez.
#YMSE: Error while updating data lake option
virtualTablesExists=Você não pode cancelar a atribuição do data lake a esta área porque ainda há dependências de tabelas virtuais*: Exclua as tabelas virtuais para cancelar essa atribuição.
#YMSE: Error while unlocking space
unlockSpaceFailureMessage=Não foi possível desbloquear a área.
#YMSE: Error while creating space
createSpaceError=Não foi possível criar a área.
#YMSE: Error while creating duplicate space
createDuplicateSpaceError=Já existe uma área com o nome {0}.
#YMSE: Error while deleting a single space
deleteSpaceError=Não foi possível excluir a área.
#YMSE: Error details while deleting a single space
deleteSpaceDetailedError=A área "{0}" não está mais funcionando corretamente. Tente excluí-la novamente. Se ainda assim ela não funcionar, peça ao administrador que ele exclua sua área ou abra um ticket.
#YMSE: Error while deleting a single space in files
deleteSpaceInFilesError=Não foi possível excluir os dados de área em Arquivos.
#YMSE: Error while deleting a single space users
deleteSpaceUsersError=Não foi possível remover os usuários.
#YMSE: Error while deleting a single space schema
deleteSpaceSchemasError=Não foi possível remover os esquemas.
#YMSE: Error while deleting a single space connections
deleteSpaceConnectionsError=Não foi possível remover as conexões.
#YMSE: Error while deleting a single space data
deleteSpaceDataError=Não foi possível excluir os dados de área.
#YMSE: Error while deleting multiple spaces
deleteSpacesError=Não foi possível excluir as áreas.
#YMSE: Error while restoring a single space
restoreSpaceError=Não foi possível restaurar a área.
#YMSE: Error while restoring multiple spaces
restoreSpacesError=Não foi possível restaurar as áreas.
#YMSE: Error while creating users
createUsersError=Não foi possível adicionar os usuários.
#YMSE: Error while removing users
removeUsersError=Não foi possível remover os usuários.
#YMSE: Error while removing user
removeUserError=Não foi possível remover o usuário.
#YMSE: Error assigning roles to a user
roleAssignUnAssignError=Não foi possível adicionar o usuário à função com escopo selecionada. \n\n Você não pode adicionar seu próprio usuário a uma função com escopo. Solicite ao administrador caso queira ser adicionado a uma função com escopo.
#YMSE: Error assigning user to the space
userAssignError=Não foi possível atribuir o usuário à área. \n\n O usuário já está atribuído ao número máximo permitido (100) de áreas entre as funções com escopo.
#YMSE: Error assigning users to the space
usersAssignError=Não foi possível atribuir os usuários à área. \n\n O usuário já está atribuído ao número máximo permitido (100) de áreas entre as funções com escopo.
#YMSE: Error while fetching users
fetchUserWithScopedRolesError=Não foi possível recuperar os usuários. Tente novamente mais tarde.
#YMSE: Error while fetching scoped roles
fetchScopedRolesError=Não foi possível recuperar as funções com escopo definido.
#YMSE: Error while fetching members
fetchUserError=Não foi possível obter os membros. Tente novamente mais tarde.
#YMSE: Error while loading run-time database
loadRuntimeError=Não foi possível carregar as informações do banco de dados de tempo de execução.
#YMSE: Error while loading spaces
loadSpacesError=Ocorreu um erro ao tentar recuperar suas áreas.
#YMSE: Error while loading haas resources
loadStorageError=Ocorreu um erro ao tentar recuperar os dados de armazenamento.
#YMSE: Error no data could be loaded
loadDataError=Ocorreu um erro ao tentar recuperar seus dados.
#XFLD: Click to refresh storage data
clickToRefresh=Clique aqui para tentar novamente.
#XTIT: Delete space popup title
deleteSpacePopupTitle=Excluir área
#XCOL: Spaces table-view column name
name=Nome
#XCOL: Spaces table-view deployment status
deploymentStatus=Status de implementação
#XFLD: Disk label in space details
storageLabel=Disco (GB)
#XFLD: In-Memory label in space details
ramLabel=Memória (GB)
#XFLD: Memory label on space card
memory=Memória para armazenamento
#XFLD: Storage Assignment label in space details
storrageAssignmentLabel=Armazenamento de área
#XFLD: Storage Type label in space details
storageTypeLabel=Tipo de armazenamento
#XFLD: Enable Space Quota
enableSpaceQuota=Ativar quota da área
#XFLD: No Space Quota
noSpaceQuota=Sem quota da área
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
diskAndInMemoryText=Banco de dados SAP HANA (disco e in-memory)
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
dataLakeFilesText=Arquivos de data lake do SAP HANA
#XFLD: Available scoped roles label
availableRoles=Funções com escopo disponíveis
#XFLD: Selected scoped roles label
selectedRoles=Funções com escopo selecionadas
#XCOL: Spaces table-view column models
models=Modelos
#XCOL: Spaces table-view column users
users=Usuários
#XCOL: Spaces table-view column connections
connections=Conexões
#XFLD: Section header overview in space detail
overview=Visão geral
#XFLD: Section header Apache Spark in space detail
sparkManagementSection=Apache Spark
#XFLD: Section header Apache Spark in space detail
sparkManagementSubSection=Aplicativos
#XFLD: Section header Apache Spark in space detail
taskAssignmentSubSection=Atribuição de tarefas
#XFLD: vCPU label in Apache Spark section
vCPULabel=vCPUs
#XFLD: Memory label in Apache Spark section
memoryLabel=Memória (GB)
#XFLD: space configuration label in Apache Spark section
spaceConfigurationLabel=Configuração da área
#XFLD: Space Source label
sparkApplicationLabel=Aplicativo
#XFLD: Cluster Size label
clusterSizeLabel=Tamanho do cluster
#XFLD: Driver label
driverLabel=Driver
#XFLD: Executor label
executorLabel=Executor
#XFLD: max label
maxLabel=Máx.usado
#XFLD: TrF Default label
trFDefaultLabel=Padrão de fluxo de transformação
#XFLD: Merge Default label
mergeDefaultLabel=Mesclar padrão
#XFLD: Optimize Default label
optimizeDefaultLabel=Otimizar padrão
#XFLD: Deployment Default label
deploymentDefaultLabel=Implementação de tabela local (arquivo)
#XFLD: max spark source label
driverExecutorMaxUsedSparkSource={0} CPU / {1} GB
#XFLD: Object type label
taskObjectTypeLabel=Tipo de objeto
#XFLD: Task activity label
taskActivityLabel=Atividade
#XFLD: Task Application ID label
taskApplicationIDLabel=Aplicativo padrão
#XFLD: Section header in space detail
generalSettings=Configurações gerais
#XMSG: Message strip message when a space is locked
spaceLockedMessageStrip=No momento, esta área está bloqueada pelo sistema.
#XMSG: Message strip message deploy warning
deployWarningMessageStrip=As alterações nesta seção serão implementadas imediatamente.
#XMSG: Message strip message modify space configuration warning
modifySpaceConfigurationMessageStrip=Observe que a alteração desses valores pode causar problemas de desempenho.
#XFLD: Button text to unlock the space again
unlockSpace=Desbloquear área
#XFLD: Info text for audit log formatted message
auditLogText=Ative os logs de auditoria para registrar ações de leitura ou alteração (políticas de auditoria). Os administradores podem então analisar quem executou essa ação e em que momento.
#XMSG: Message strip  audit warning
auditLogWarningMessageStrip=Os logs de auditoria podem consumir uma quantidade grande de armazenamento em disco no seu locatário. Quando você ativa uma política de auditoria (ações de leitura ou alteração), é necessário monitorar regularmente o uso do armazenamento em disco (por meio do cartão Armazenamento em disco usado no Monitor de sistema) para evitar falhas por disco cheio que possam levar a interrupções no serviço. Se você desativar uma política de auditoria, todas as entradas do log de auditoria serão excluídas. Caso queira mantê-las, considere exportá-las antes de desativar a política de auditoria.
#XFLD: Message strip link text to open in app help to guide user what to do to unlock his space permanently
unlockSpaceHelp=Mostrar ajuda
#XMSG: Message strip message when a space will be locked again
spaceTemporarilyUnlockedMessageStrip=Esta área excede o armazenamento de área e será bloqueada em {0} {1}.
#XMSG: Unit for remaining time until space is locked again
hours=horas
#XMSG: Unit for remaining time until space is locked again
minutes=minutos
#XFLD: Section header in space detail for auditing
detailsSectionAuditing=Auditoria
#XFLD: Subsection header in space detail for auditing
auditing=Configurações de auditoria de área
#XFLD: Hot space tooltip
hotSpaceCountTooltip=Áreas críticas: mais de 90% do armazenamento usado.
#XFLD: Green space tooltip
greenSpaceCountTooltip=Áreas íntegras: entre 6% e 90% do armazenamento usado.
#XFLD: Cold space tooltip
coldSpaceCountTooltip=Áreas frias: 5% ou menos do armazenamento usado.
#XFLD: Critical space tooltip
criticalSpaceCountTooltip=Áreas críticas: mais de 90% do armazenamento usado.
#XFLD: Green space tooltip
okSpaceCountTooltip=Áreas íntegras: entre 6% e 90% do armazenamento usado.
#XFLD: Locked space tooltip
lockedSpaceCountTooltipNew=Áreas bloqueadas: bloqueadas devido a memória insuficiente.
#XFLD: Locked space tooltip
lockedSpaceCountTooltip=Áreas bloqueadas
#YMSE: Error while deleting remote source
deleteRemoteError=Não foi possível remover as conexões.
#XFLD: Space ID input tooltip
spaceIDInfoTooltip=Não será possível alterar o ID da área mais tarde.\nCaracteres inválidos: A - Z, 0 - 9 e _
#YMSE: Error when space name is empty
spaceNameEmptyValidationError=Informe o nome da área.
#YMSE: Error when ECN Business name is empty
ecnNameEmptyValidationError=Insira um nome comercial.
#YMSE: Error when space id is empty
spaceIDInputEmptyValidationError=Informe o ID da área.
#YMSE: Error when space id contains invalid characters
spaceIDInputInValidError=Caracteres inválidos. Use apenas A-Z, 0-9 e _.
#YMSE: Error when space id exists while creation
spaceIDInputDuplicateError=Esse ID de área já existe.
#XFLD: Space searchfield placeholder
search=Procurar
#XMSG: Success message after creating users
createUsersSuccess=Usuários adicionados
#XMSG: Success message after creating user
createUserSuccess=Usuário adicionado
#XMSG: Success message after updating users
updateUsersSuccess={0} usuários atualizados
#XMSG: Success message after updating user
updateUserSuccess=Usuário atualizado
#XMSG: Success message after removing users
removeUsersSuccess={0} usuários removidos
#XMSG: Success message after removing user
removeUserSuccess=Usuário removido
#XFLD: Schema name
schemaName=Nome do esquema
#XFLD: used of total
ofTemplate={0} de {1}
#XFLD: Assigned disk label in space overview
assignedStorageTemplate=Disco atribuído ({0} de {1})
#XFLD: Assigned Memory label in space overview
assignedRAMTemplate=Memória atribuída ({0} de {1})
#XFLD: Storage ratio on space
accelearationRAM=Aceleração de memória
#XFLD: No Storage Consumption
noStorageConsumptionText=Nenhuma quota de armazenamento atribuída.
#XFLD: Used disk label in space overview
usedStorageTemplate=Disco usado para armazenamento ({0} de {1})
#XFLD: Used Memory label in space overview
usedRAMTemplate=Memória usada para armazenamento ({0} de {1})
#XFLD: Used Disk tooltip
usedStorageTooltipTemplate={0} de {1} disco usado para armazenamento
#XFLD: Used Memory tooltip
usedRAMTooltipTemplate={0} de {1} memória usada
#XFLD: Assigned Disk tooltip
assignedStorageTooltipTemplate={0} de {1} de disco atribuído
#XFLD: Assigned Memory tooltip
assignedRAMTooltipTemplate={0} de {1} memória atribuída
#XFLD: Space related used storage tooltip
spaceRelatedUsedStorageTooltipTemplate=Dados da área: {0}
#XFLD: Not space related used storage tooltip
otherDataUsedStorageTooltipTemplate=Outros dados: {0}
#XFLD: Storage used exceeded popover and tooltip
storageUsedExceededText=Considere ampliar seu plano ou entre em contato com o suporte da SAP.
#XCOL: Space table-view column used Disk
usedStorage=Disco usado para armazenamento
#XCOL: Space monitor column used Memory
usedRAM=Memória usada para armazenamento
#XCOL: Space monitor column Schema
tableSchema=Esquema
#XCOL: Space monitor column Storage Type
tableStorageType=Tipo de armazenamento
#XCOL: Space monitor column Table Type
tableType=Tipo da tabela
#XCOL: Space monitor column Record Count
tableRecordCount=Contagem de registros
#XFLD: Assigned Disk
assignedStorage=Disco atribuído para armazenamento
#XFLD: Assigned Memory
assignedRAM=Memória atribuída para armazenamento
#XCOL: Space table-view column storage utilization
tableStorageUtilization=Armazenamento usado
#XFLD: space status
spaceStatus=Status da área
#XFLD: space type
spaceType=Tipo de área
#XFLD: space type label for space with space type "SAP BW bridge" ("SAP BW bridge" is a product name, do not translate it)
abapbridgeSpaceType=Ponte SAP BW
#XFLD: space type label for space with space type "Data Marketplace"
marketplaceSpaceType=Produto de provedor de dados
#XMSG: Error message for when selected space cannot be deleted due to the space type
deleteTypedSpaceError=Você não pode excluir a área {0} porque seu tipo de área é {1}.
#XMSG: Error message for when selected spaces cannot be deleted due to the space type
deleteTypedSpacesError=Você não pode excluir as {0} áreas selecionadas. As áreas com os seguintes tipos de área não podem ser excluídas: {1}.
#XFLD: space type label for space without space type
defaultSpaceType=SAP Datasphere
#XFLD: Tooltip for monitor space button
monitorSpace=Monitorar
#XFLD: Tooltip for edit space button
editSpace=Editar área
#XMSG: Deletion warning in messagebox
deleteConfirmation=Tem certeza de que deseja excluir esta área?
#XFLD: Tooltip for delete space button
deleteSpace=Excluir área
#XFLD: storage
storage=Disco para armazenamento
#XFLD: username
userName=Nome de usuário
#XFLD: port
port=Porta
#XFLD: hostname
hostName=Nome do host
#XFLD: password
password=Senha
#XBUT: Request new password button
requestPassword=Solicitar nova senha
#YEXP: Usage explanation in time data section
timeDataSectionHint=Crie tabelas de tempos e dimensões para serem usadas em seus modelos e suas histórias.
#YEXP: Usage explanation in Space schema section
spaceSchemaHint=Quer permitir que outros aplicativos ou ferramentas consumam os dados presentes na sua área? Se sim, crie um ou vários usuários que tenham acesso aos dados na sua área e indique se deseja que todos os futuros dados da área sejam consumíveis por padrão.
#XTIT: Create schema popup title
createSchemaDialogTitle=Criar esquema SQL aberto
#XTIT: Generate Time Data dialog title
generateTimeDataDialogTitle=Criar tabelas de tempos e dimensões
#XTIT: Edit Time Data dialog title
editTimeDataDialogTitle=Editar tabelas de tempos e dimensões
#XTIT: Time Data token title
timeDataTokenTitle=Dados de tempos
#XTIT: Time Data token title
timeDataUpdateViews=Atualizar visões de dados de tempos
#XFLD: Time Data token creation in progress label
timeDataCreationInProgress=Criação em andamento...
#XFLD: Time Data token creation error label
timeDataCreationError=Falha na criação. Tente novamente.
#XTIT: Time Data dialog time settings title
timeDataDialogTimeSettingsTitle=Configurações da tabela de tempos
#XTIT: Time Data dialog time data table title
timeDataDialogTranslationTablesTitle=Tabelas de tradução
#XTIT: Time Data dialog dimensions title
timeDataDialogTimeViewsTitle=Dimensões temporais
#XFLD: Time Data dialog time range label
timeRangeHint=Defina um período.
#XFLD: Time Data dialog time data table label
timeDataHint=Dê um nome à sua tabela.
#XFLD: Time Data dialog time data dimensions label
timeDimensionsHint=Dê um nome às suas dimensões.
#XFLD: Time Data Time range description label
timerangeLabel=Período
#XFLD: Time Data dialog from year label
fromYearLabel=Desde ano
#XFLD: Time Data dialog to year label
toYearLabel=Até ano
#XFLD: Time Data dialog calendar type label
calendarTypeLabel=Tipo de calendário
#XFLD: Time Data dialog granularity label
granularityLabel=Granularidade
#XFLD: Time Data dialog technical name label
technicalNameLabel=Nome técnico
#XFLD: Time Data dialog quarters text table lable
quarterTextsTableLabel=Tabela de tradução para trimestres
#XFLD: Time Data dialog months text table lable
monthsTextsTableLabel=Tabela de tradução para meses
#XFLD: Time Data dialog days text table lable
daysTextsTableLabel=Tabela de tradução para dias
#XFLD: Time Data dialog year label
yearLabel=Dimensão de ano
#XFLD: Time Data dialog quarter label
quarterLabel=Dimensão de trimestre
#XFLD: Time Data dialog month label
monthLabel=Dimensão de mês
#XFLD: Time Data dialog day label
dayLabel=Dimensão de dia
#XFLD: Time Data dialog gregorian calendar type label
gregorian=Gregoriano
#XFLD: Time Data dialog time granularity day label
day=Dia
#XFLD: Time Data dialog business name maxlength reached
timeRangeValueStateTextMaxLengthBusinessName=O tamanho máximo de 1.000 caracteres foi alcançado.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextMaxRangeExceeded=O período máximo é de 150 anos.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeLower=O ano inicial deve ser anterior ao ano final
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeHigher=O ano inicial deve ser 1900 ou posterior:
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeHigher=O ano final deve ser posterior ao ano inicial
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeLower=O ano final deve ser anterior ao ano atual mais 100
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningFromYear=Aumentar o ano inicial pode causar perda de dados
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningToYear=Diminuir o ano final pode causar perda de dados
#XMSG: Time Data creation validation error message
timeDataValidationError=Parece que alguns campos não contêm valores válidos. Verifique os campos obrigatórios.
#XMSG: Time Data deletion popup text
deleteTimeDataConfirmation=Tem certeza de que deseja excluir os dados?
#XMSG: Time Data creation success message
createTimeDataSuccess=Dados de tempos criados
#XMSG: Time Data update success message
updateTimeDataSuccess=Dados de tempos atualizados
#XMSG: Time Data delete success message
deleteTimeDataSuccess=Dados de tempos excluídos
#XMSG: Time Data creation error message
createTimeDataError=Algo deu errado ao tentar criar os dados de tempos.
#XMSG: Time Data update error message
updateTimeDataError=Algo deu errado ao tentar atualizar os dados de tempos.
#XMSG: Time Data creation error message
deleteTimeDataError=Algo deu errado ao tentar excluir os dados de tempos.
#XMSG: Error message when time data could not be retrieved
retrievingTimeDataError=Não foi possível carregar os dados de tempos.
#XTIT: Time Data deletion popup title
deleteTimeDataConfirmationTitle=Aviso
#XTXT: Time Data deletion dependency popup text
deleteTimeDataDependencyText=Não foi possível excluir seus dados de tempos, eles estão sendo usados em outros modelos.
#XTXT: Time Data deletion dependency popup text singular
deleteTimeDataDependencyTextSingular=Não foi possível excluir seus dados de tempos, eles estão sendo usados em outro modelo.
#XMSG: Validation error when required time data form field is empty
timeDataRequiredField=Este campo é obrigatório, ele não pode ficar em branco.
#XFLD: Tooltip for Open in Database Explorer button
openInDBX=Abrir no Database Explorer
#YMSE: Dimension Year
dimensionYearView=Dimensão "Ano"
#YMSE: Dimension Year
dimensionQuarterView=Dimensão "Trimestre"
#YMSE: Dimension Year
dimensionMonthView=Dimensão "Mês"
#YMSE: Dimension Year
dimensionDayView=Dimensão "Dia"
#XFLD: Time Data deletion object title
timeDataUsedIn=(usado em {0} modelos)
#XFLD: Time Data deletion object title singular
timeDataUsedInSingular=(usado em 1 modelo)
#XFLD: Time Data deletion table column provider
provider=Provedor
#XTIT: Time Data dependency dialog title
timeDataDependencyDialogTitle=Dependências
#XTIT: Create schema popup title
createSpaceSchemaUserDialogTitle=Criar usuário para esquema de área
#XFLD: Create schema button
createSchemaButton=Criar esquema SQL aberto
#XFLD: Generate TimeData button
generateTimeDataButton=Criar tabelas de tempos e dimensões
#XFLD: Show dependencies button
showDependenciesButton=Mostrar dependências
#XFLD: tooltip for disabled buttons when user is not member of space
mustBeMemberOfSpaceForOperation=Para realizar essa operação, seu usuário deve ser um membro da área.
#XFLD: Create space schema user button
createSpaceSchemaUserButton=Criar usuário do esquema de área
#YMSE: API Schema users load error
loadSchemaUsersError=Não foi possível carregar a lista de usuários..
#XTIT: API Schema-config popup title
schemaUserDetailsDialogTitle=Detalhes de usuário do esquema de área
#XMSG: API Schema user deletion popup text
deleteSpaceSchemaUserConfirmation=Tem certeza de que deseja excluir o usuário selecionado?
#XMSG: API Schema user deletion confirmation text
userDeleteSuccess=Usuário excluído.
#YMSE: API Schema user deletion error
userDeleteError=Não foi possível excluir o usuário.
#XFLD: User deleted
userDeleted=O usuário foi excluído.
#XTIT: Remove user popup title
removeUserConfirmationTitle=Aviso
#XMSG: Remove user popup text
removeUserConfirmation=Realmente deseja remover o usuário? O usuário e as respectivas funções com escopo atribuídas serão removidos da área.
#XMSG: Remove users popup text
removeUsersConfirmation=Realmente deseja remover os usuários? Os usuários e as respectivas funções com escopo atribuídas serão removidos da área.
#XBUT: Confirmation button for remove user dialog
removeUserConfirmationBtn=Remover
#YMSE: No data text for available roles
noDataAvailableRoles=A área não foi adicionada a nenhuma função com escopo. \n Para adicionar usuários à área, ela primeiro deve ser adicionada a uma ou mais funções com escopo.
#YMSE: No data text for selected roles
noDataSelectedRoles=Nenhuma função com escopo selecionada
#XTIT: Schema-config popup title
schemaDetailsDialogTitle=Detalhes de configuração do esquema SQL aberto
#XFLD: Label for Read Audit Log
auditLogRead=Ativar log de auditoria para operações de leitura
#XFLD: Label for Change Audit Log
auditLogChange=Ativar log de auditoria para operações de alteração
#XFLD: Label Audit Log Retention
auditLogRetention=Manter logs por
#XFLD: Label Audit Log Retention Unit
retentionUnit=dias
#YMSE: Error when log retention time is set incorrectly
incorrectRetentionTime=Informe um número inteiro entre {0} e {1}
#XFLD: Label for Cross Space Schema Access
spaceSchemaAccess=Consumir dados do esquema da área
#XTIT: Confirm deactivation of space schema consumption dialog title
disableSpaceSchemaConsumptionDialogTitle=Parar de consumir dados do esquema da área
#XTXT: Confirm deactivation of space schema consumption dialog text
disableSpaceSchemaConsumptionDialogText=Este esquema Open SQL pode consumir dados de seu esquema da área. Se você parar o consumo, os modelos baseados nos dados do esquema da área poderão não funcionar mais.
#XBUT: Confirmation button for the stop consuming space schema data dialog
stopConsuming=Parar o consumo
#XTXT: Tooltip for data lake icon
dataLakeIconTooltip=Esta área é usada para acessar o data lake
#XFLD: Tooltip for data lake table entry
dataLakeTableEntryTooltip=Data lake ativado
#XFLD: used In-Memory linit reached tag
usedRamLimitReachedLabel=Limite de memória atingido
#XFLD: Space storage Limit reached tag
storageLimitReachedLabel=Limite de armazenamento atingido
#XFLD: Minimal Storage Limit Reached tag
minStorageLimitReachedLabel=Limite mínimo de armazenamento atingido
#XFLD: Space ram tag
ramLimitReachedLabel=Limite de memória atingido
#XFLD: Minimal In-Memory Limit Reached tag
minRamLimitReachedLabel=Limite mínimo de memória atingido
#XFLD: Popover text after click on space storage limit warning
storageLimitReachedText=Você atingiu o limite de armazenamento da área ({0}). Atribua mais armazenamento a ela.
#XFLD: System storage tag
systemStorageLimitReachedLabel=Atingido o limite de armazenamento do sistema
#XMSG: Popover text after click on storage limit warning
systemStorageLimitReachedText=Você atingiu o limite de armazenamento do sistema ({0}). Não é possível atribuir mais armazenamento à área no momento.
#XMSG: Schema deletion warning
deleteSchemaConfirmation=Se você excluir este esquema SQL aberto, os objetos armazenados e as associações mantidas no esquema também serão permanentemente excluídos. Continuar?
#XMSG: Success message after deleting a schema
schemaDeleteSuccess=Esquema excluído
#YMSE: Error while deleting schema.
schemaDeleteError=Não foi possível excluir o esquema.
#XMSG: Success message after update a schema
schemaUpdateSuccess=Esquema atualizado
#YMSE: Error while updating schema.
schemaUpdateError=Não foi possível atualizar o esquema.
#XMSG: Hint when no password is shown in the dialog.
newPasswordHint=Uma senha foi fornecida para esse esquema. Caso tenha esquecido ou perdido a senha, você pode solicitar uma nova. Lembre-se de copiar ou salvar a nova senha.
#XMSG: Hint when showing the password in the dialog.
savePasswordHint=Copie sua senha. Você precisará dela para configurar uma conexão para este esquema. Caso tenha esquecido a senha, abra esta caixa de diálogo para redefini-la.
#XFLD,50: Hint on entering schema name in creation dialog
schemaNameHint=Impossível alterar nome após criação de esquema.
#XFLD: Open SQL Schemas section sub headline
schemasSQL=SQL aberto
#XFLD: Space schema section sub headline
schemasSpace=Área
#XFLD: HDI Container section header
HDIContainers=Containers HDI
#XTXT: Add HDI Containers button tooltip
addHDIContainers=Adicionar containers HDI
#XTXT: Remove HDI Containers button tooltip
removeHDIContainers=Remover containers HDI
#XFLD: Enable HDI Containers feature button
enableHDIContainersButton=Habilitar acesso
#YMSE: No data text for HDI Containers table
noDataHDIContainers=Nenhum container HDI foi adicionado.
#YMSE: No data text for Timedata section
noDataTimedata=Não foram criadas dimensões nem tabelas de tempos.
#YMSE: No data text during runtime not available for Timedata section
runtimeNotAvailableNoDataTimedata=Não é possível carregar tabelas e dimensões de tempos, o banco de dados de tempo de execução não está disponível.
#YMSE: No data text during runtime not available for HDI section
runtimeNotAvailableNoDataHDI=Não é possível carregar containers HDI, o banco de dados de tempo de execução não está disponível.
#YMSE: Error while fetching HDI Containers
fetchHDIContainersError=Não foi possível obter os containers HDI. Tente novamente mais tarde.
#XFLD Table column header for HDI Container names
HDIContainerName=Nome do container HDI
#XFLD: Enable HDI Containers feature dialog title
enableHDIContainersDialogTitle=Habilitar acesso
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText1New=Você pode ativar o SAP SQL Data Warehousing no locatário do SAP Datasphere para trocar dados entre os containers HDI e suas áreas do SAP Datasphere sem a necessidade de movimentar dados.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText2=Para isso, abra um ticket de suporte clicando no botão abaixo.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText3New=Depois que o ticket for processado, você terá que criar um ou mais containers HDI no banco de dados de tempo de execução do SAP Datasphere. Em seguida, o botão Ativar acesso será substituído pelo botão + na seção Containers HDI para todas as áreas do SAP Datasphere, e você poderá adicionar seus containers a uma área.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText41=Precisa de mais informações? Vá para %%0. Para obter informações detalhadas sobre o que incluir no ticket, consulte %%1.
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkText=Ajuda da SAP
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkTextSapNote3057059=Nota SAP 3057059
#XBUT: Open Ticket Button Text
openTicket=Abrir ticket
#XBUT: Add Button Text
add=Adicionar
#XBUT: Next Button Text
next=Avançar
#XBUT: Edit Button Text
editUsers=Editar
#XBUT: create user Button Text
createUser=Criar
#XBUT: Update user Button Text
updateUser=Selecionar
#XTIT: Add HDI Containers Dialog Title
addHDIContainersDialogTitle=Adicionar containers HDI não atribuídos
#XFLD: No data text for add HDI container dialog
addHDIContainersDialogNoDataText=Não foi possível localizar nenhum container não atribuído. \n O container que você está procurando pode já ter sido atribuído a uma área.
#YMSE: Error while loading assigned HDI Containers
loadAssignedHDIContainersError=Não foi possível carregar os containers HDI atribuídos.
#YMSE: Error while loading assigned HDI Containers
loadUnassignedHDIContainersError=Não foi possível carregar os containers HDI.
#XMSG: Success message
succeededToAddHDIContainer=Container HDI adicionado
#XMSG: Success message
succeededToAddHDIContainerPlural=Containers HDI adicionados
#XMSG: Success message
succeededToDeleteHDIContainer=Container HDI removido
#XMSG: Success message
succeededToDeleteHDIContainerPlural=Containers HDI removidos
#XFLD: Time data section sub headline
timeDataSection=Tabelas de tempos e dimensões
#XFLD: Schemas list entry read audit log enabled indicator
readIndicator=Leitura
#XFLD: Schemas list entry change audit log enabled indicator
changeIndicator=Alteração
#XFLD: Remote sources section sub headline
allconnections=Atribuição de conexão
#XFLD: Remote sources section sub headline
localconnections=Conexões locais
#XFLD: Remote sources section sub headline for open Connectors
openConnectors=SAP Open Connectors
#XFLD: User section sub headline
memberassignment=Atribuição de membro
#XFLD: User assignment section sub headline
userAssignment=Atribuição de usuário
#XFLD: User section Access dropdown Member
member=Membro
#XFLD: User assignment section column name
user=Nome de usuário
#XTXT: Selected role count
selectedRoleToolbarText=Selecionado: {0}
#XTIT: Space detail section connections title
detailsSectionConnections=Conexões
#XTIT: Space detail section data access title
detailsSectionDataAccess=Acesso ao esquema
#XTIT: Space detail section time data title
detailsSectionGenerateData=Dados de tempos
#XTIT: Space detail section members title
detailsSectionUsers=Membros
#XTIT: Space detail section Users title
detailsSectionUsersTitle=Usuários
#XTIT: Out of Storage
insufficientStoragePopoverTitle=Sem armazenamento
#XTIT: Storage distribution
storageDistributionPopoverTitle=Armazenamento em disco usado
#XTXT: Out of Storage popover text
insufficientStorageText=Para criar uma área, reduza o armazenamento atribuído a alguma outra área ou exclua uma área que não seja mais necessária. Você pode aumentar o armazenamento total do sistema chamando Gerenciar plano.
#XMSG: Space id length warning
spaceIdLengthWarning=Máximo de {0} caracteres excedido.
#XMSG: Space name length warning
spaceNameLengthWarning=Máximo de {0} caracteres excedido.
#XMSG: Space id reserved prefix warning
spaceIdnWarningReservedPrefix=Não use o prefixo {0} para evitar possíveis conflitos.
#YMSE: Error while loading open SQL schemas
loadLocalSchemasError=Não foi possível carregar os esquemas Open SQL.
#YMSE: Error while creating open SQL schema
createLocalSchemaError=Não foi possível criar o esquema Open SQL.
#YMSE: Error while loading all remote connections
loadRemoteConnectionsError=Não foi possível carregar todas as conexões remotas.
#YMSE: Error while loading space details
loadSpaceDetailsError=Não foi possível carregar os detalhes da área.
#YMSE: Error while deploying space details
deploySpaceDetailsError=Não foi possível implementar a área.
#YMSE: Error while copying space details
copySpaceDetailsError=Não foi possível copiar a área.
#YMSE: Error while loading storage data
loadStorageDataError=Não foi possível carregar os dados de armazenamento.
#YMSE: Error while loading all users
loadAllUsersError=Não foi possível carregar todos os usuários.
#YMSE: Failed to reset password
resetPasswordError=Não foi possível redefinir a senha.
#XMSG: Success message after updating space
resetPasswordSuccessMessage=Nova senha definida para esquema
#YMSE: DP Agent-name too long
DBAgentNameError=O nome do agente de provisionamento de dados é grande demais.
#YMSE: Schema-name not valid.
schemaNameError=O nome do esquema não é válido.
#YMSE: User name not valid.
UserNameError=O nome de usuário não é válido.
#XTIT: Consumption by Storage Type
consumptionStorageTypeText=Consumo por tipo de armazenamento
#XTIT: Consumption by Schema
consumptionSchemaText=Consumo por esquema
#XTIT: Overall Consumption by schema
overallConsumptionSchemaText=Consumo geral da tabela por esquema
#XTIT: Overall Consumption by Table Type
overallConsumptionTableTypeText=Consumo geral por tipo de tabela
#XTIT: Tables
tableDetailsText=Detalhes da tabela
#XTIT: Table Storage Consumption
tableStorageConsumptionText=Consumo de armazenamento da tabela
#XFLD: Table Type label
tableTypeLabel=Tipo da tabela
#XFLD: Schema label
schemaLabel=Esquema
#XFLD: reset table tooltip
resetTable=Redefinir tabela
#XFLD: In-Memory label in space monitor
inMemoryLabel=Memória
#XFLD: Disk label in space monitor
diskLabel=Disco
#XFLD: Yes
yesLabel=Sim
#XFLD: No
noLabel=Não
#XFLD: switch label: Want the data in this space to be consumable by default?
allowConsumptionByDefault=Permitir que os dados nesta área sejam consumíveis por padrão?
#XFLD: Business Name
businessNameLabel=Nome comercial
#XFLD: Refresh
refresh=Atualizar
#XMSG: No filter results title
noFilterResultsTitle=Parece que suas configurações de filtro não estão mostrando dados.
#XMSG: No filter results message
noFilterResultsMsg=Tente refinar suas configurações de filtro. Se ainda assim não for exibido nenhum dado, crie algumas tabelas no Gerador de dados. Assim que elas consumirem armazenamento, você poderá monitorá-las aqui.
#XMSG: Space monitoring Hana state red title
spaceMonitorHanaRedTitle=O banco de dados de execução não está disponível.
#XMSG: Space monitoring Hana state red message
spaceMonitorHanaRedMsg=Como o banco de dados de execução não está disponível, alguns recursos estão desabilitados e não podemos exibir nenhuma informação nesta página.
#YMSE: Error while creating space schema user
createSpaceSchemaUserError=Não foi possível criar o usuário do esquema de área.
#YMSE: Error User name already exists
userAlreadyExistsError=Nome de usuário já existente.
#YMSE: Error Authentication failed
authenticationFailedError=Falha na autenticação.
#XMSG: Message strip message when a schema user is locked
userLockedMessageStrip=O usuário está bloqueado devido a muitas tentativas de login com falha. Solicite uma nova senha para desbloquear o usuário.
#XMSG: Success message after reset and unlock schema user
resetUnlockPasswordSuccessMessage=Nova senha definida e usuário desbloqueado
#XMSG: user is locked message
userLockedMessage=O usuário está bloqueado.
#XCOL: Users table-view column Role
spaceRole=Função
#XCOL: Users table-view column Scoped Role
spaceScopedRole=Função com escopo
#XCOL: Users table-view column Space Admin
spaceAdmin=Administrador da área
#XFLD: User section dropdown value Viewer
viewer=Visualizador
#XFLD: User section dropdown value Modeler
modeler=Modelador
#XFLD: User section dropdown value Data Integrator
dataIntegrator=Integrador de dados
#XFLD: User section dropdown value Space Administrator
spaceAdministrator=Administrador da área
#XMSG: Success message -  Space Role updated successfully.
updateUserSpaceRoleSuccess=Função da área atualizada
#XMSG: Error message - Failed to update Space Role.
updateUserSpaceRoleError=Função da área não foi atualizada com sucesso.
#XFLD:
databaseUserNameSuffix=Sufixo de nome de usuário do banco de dados
#XTXT: Space Schema password text
spaceSchemaPasswordText=Ao configurar uma conexão para este esquema, copie a senha. Caso tenha esquecido a senha, você pode solicitar uma nova a qualquer momento.
#XFLD: Cloud Platform label in space schema dialog
cloudPlatformLabel=Cloud Platform
#XTXT: Cloud Platform text in space schema dialog
cloudPlatformText=Para configurar o acesso com este usuário, ative o consumo e copie as credenciais. Se você só puder copiar as credenciais sem uma senha, lembre-se de adicionar a senha mais tarde.
#XFLD: enable consumption in CloudPlatform label in space schema dialog
enableConsumptionInCloudPlatformLabel=Ativar consumo no Cloud Platform
#XFLD: Credentials for User-Provided Service label in space schema dialog
cloudPlatformCredentialsLabel=Credenciais para serviço fornecido ao usuário:
#XFLD: Credentials for User-Provided Service (Without Password) label in space schema dialog
cloudPlatformCredentialsWithoutPasswordLabel=Credenciais para serviço fornecido pelo usuário (sem senha):
#XFLD: Copy Credentials label in space schema dialog
copyCredentialsLabel=Copiar credenciais sem senha
#XFLD: Copy Credentials label in space schema dialog
copyFullCredentialsLabel=Copiar credenciais completas
#XFLD: Copy Password label in space schema dialog
copyPasswordLabel=Copiar senha
#XMSG: Credentials copied to clipboard
credentialsCopiedMessage=Credenciais copiadas para área de transferência
#XMSG: Password copied to clipboard
passwordCopiedMessage=Senha copiada para área de transferência
#XMSG: create database user dialog title
createDatabaseUserDialogTitle=Criar usuário do banco de dados
#XMSG: Database Users section title
databaseUsers=Usuários do banco de dados
#XMSG: Database User Details dialog title
databaseUserDetailsDialogTitle=Detalhes do usuário do banco de dados
#XFLD: database user read audit log
databaseUserAuditLogRead=Ativar logs de auditoria para operações de leitura e manter logs por
#XFLD: database user change audit log
databaseUserAuditLogChange=Ativar logs de auditoria para operações de alteração e manter logs por
#XMSG: Cloud Platform Access
cloudPlatformAccess=Acesso ao Cloud Platform
#XFLD: Enable Consumption in Cloud Platform hint
enableConsumptionCloudPlatformHint=Configure o acesso ao seu container de HANA Deployment Infrastructure (HDI) com este usuário do banco de dados. Para estabelecer a conexão com seu container HDI, a modelagem SQL precisa estar ativada.
#XFLD: Enable HDI Consumption
enableConsumptionCloudPlatform=Ativar consumo de HDI
#XFLD: Enable Consumption hint
enableConsumptionHint=Deseja que os dados na sua área possam ser consumidos por outras ferramentas ou aplicativos?
#XFLD: Enable Consumption
enableConsumption=Ativar consumo de SQL
#XFLD: Enable Modeling
enableModeling=Ativar modelagem SQL
#XMSG: Privileges for Data Modeling
privilegesModeling=Ingestão de dados
#XMSG: Privileges for Data Consumption
privilegesConsumption=Consumo de dados para ferramentas externas
#XFLD: SQL Modeling
sqlModeling=Modelagem SQL
#XFLD: SQL Consumption
sqlConsumption=Consumo de SQL
#XFLD: enabled
enabled=Ativado
#XFLD: disabled
disabled=Desativado
#XFLD: Edit Privileges
editPrivileges=Editar privilégios
#XFLD: Open Database Explorer
openDBX=Abrir Database Explorer
#XFLD: create database user hint
databaseCreateHint=Note que você não poderá alterar novamente o nome do usuário após salvar.
#XFLD: Internal Schema Name
internalSchemaName=Nome do esquema interno
#YMSE: Failed to load database users
loadDatabaseUserError=Falha ao carregar os usuários do banco de dados
#YMSE: Failed to delete database user
deleteDatabaseUsersError=Falha ao excluir os usuários do banco de dados
#XMSG: Success message after deleting database user
deleteDatabaseUserSuccess=Usuário do banco de dados excluído
#XMSG: Success message after deleting database users
deleteDatabaseUsersSuccess=Usuários do banco de dados excluídos
#XMSG: Success message after creating database user
createDatabaseUserSuccessMessage=Usuário do banco de dados criado
#YMSE: Failed to create database user
createDatabaseUserError=Falha ao criar o usuário do banco de dados
#XMSG: Success message after updating database user
updateDatabaseUserSuccessMessage=Usuário do banco de dados atualizado
#YMSE: Failed to update database user
updateDatabaseUserError=Falha ao atualizar o usuário do banco de dados
#XFLD: HDI Consumption
hdiConsumption=Consumo de HDI
#XTIT: Space detail section Database Access title
detailsSectionDatabaseAccess=Acesso ao banco de dados
#XTXT: Database users consumption hint
databaseUsersConsumptionHint=Disponibilize os dados da sua área para consumo por padrão. Os modelos nos geradores permitirão automaticamente que os dados sejam consumíveis.
#XFLD: Default Consumption of Space Data:
defaultConsumptionOfSpaceData=Consumo padrão dos dados da área:
#XFLD: Database User Name
databaseUserName=Nome do usuário do banco de dados
#XMSG: Database User creation validation error message
databaseUserValidationError=Parece que alguns campos não contêm valores válidos. Verifique os campos obrigatórios.
#XMSG: SQL modeling can’t be enabled since this user has been migrated.
migratedDatabaseUserHint=Não é possível ingerir dados porque este usuário foi migrado.
#XBUT: Remove Button Text
remove=Remover
#XBUT: Remove Spaces Button Text
removeSpaces=Remover áreas
#XBUT: Remove Objects Button Text
removeObjects=Remover objetos
#XMSG: No members have been added yet.
noMembersAssigned=Nenhum membro foi adicionado ainda.
#XMSG: No users have been added yet.
noUsersAssigned=Nenhum usuário foi adicionado ainda.
#XMSG: No database users have been created or your filter is not showing any data.
noDatabaseUsers=Nenhum usuário de banco de dados foi criado ou seu filtro não está mostrando nenhum dado.
#XMSG: Please enter a user name.
noDatabaseUsername=Insira um nome de usuário.
#XMSG: The user name is too long. Please use a shorter one.
databaseUsernameTooLong=O nome do usuário é muito grande. Use um nome mais curto.
#XMSG: No privileges have been enabled and this database user will have limited functionality. Do you still want to continue?
noPrivilegesConfirmationMessage=Nenhum privilégio foi ativado; este usuário de banco de dados terá funcionalidade limitada. Deseja continuar mesmo assim?
#XMSG: To enable audit logs for change operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogChangeMessage=Para ativar os logs de auditoria para operações de alteração, a ingestão de dados precisa estar ativada também. Deseja fazer isso?
#XMSG: To enable audit logs for read operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogReadMessage=Para ativar os logs de auditoria para operações de leitura, a ingestão de dados precisa estar ativada também. Deseja fazer isso?
#XMSG: To enable HDI consumption data ingestion and data consumption needs to be enabled. Do you want to enable all?
enableHDIConsumptionMessage=Para ativar o consumo de HDI, a ingestão de dados e o consumo de dados precisam estar ativados também. Deseja fazer isso?
#XMSG:
databaseUserPasswordText=Ao configurar uma conexão para este usuário de banco de dados, copie a senha. Se você se esquecer dela, poderá solicitar uma nova a qualquer momento.
#XTIT: Space detail section members title
detailsSectionMembers=Membros
#XMSG: New password set
newPasswordSet=Nova senha definida
#XFLD: Data Ingestion
dataIngestion=Ingestão de dados
#XFLD: Data Consumption
dataConsumption=Consumo de dados
#XFLD: Privileges
privileges=Autorizações
#XFLD: Enable Data ingestion
enableDataIngestion=Ativar ingestão de dados
#XMSG: Log the read and change operations for data ingestion.
databaseUserAuditHint=Crie logs de leitura e operações de alteração para a ingestão de dados.
#XMSG: Make your space data available in your HDI containers.
databaseUserHDIHint=Disponibilize dados da sua área nos seus containers HDI.
#XFLD: Enable Data consumption
enableDataConsumption=Ativar consumo de dados
#XMSG: Allow other apps or tools to consume your space data.
enableDataConsumptionHint=Permitir que outros aplicativos ou ferramentas consumam os dados da sua área.
#XMSG: To set up access via this schema user copy the credentials to your user-provided service. In case you can only copy the credentials without a password make sure to add the password later.
databaseUserHDIConsumptionHint=Para configurar o acesso com este usuário do banco de dados, copie as credenciais para o seu serviço fornecido pelo usuário. Se você só puder copiar as credenciais sem uma senha, lembre-se de adicionar a senha mais tarde.
#XFLD: Data Flow Runtime Capacity label in space overview
dataFlowRuntimeCapacity=Capacidade de tempo de execução do fluxo de dados ({0}h{1} de {2} horas)
#XFLD: Could not load Data Flow Runtime Capacity
dataFlowRuntimeCapacityError=Não foi possível carregar a capacidade de tempo de execução do fluxo de dados
#XMSG: User can grant data consumption to other users.
databaseUserConsumptionWithGrantOptionHint=O usuário pode conceder consumo de dados a outros usuários.
#XFLD: Enable data consumption with grant option
enableConsumptionWithGrantOption=Ativar consumo de dados com opção de concessão
#XMSG: To enable data consumption with grant option data consumption needs to be enabled. Do you want to enable both?
enableConsumptionWithGrantMessage=Para ativar o consumo de dados com opção de concessão, o consumo de dados precisa estar ativado. Deseja ativar os dois?
#XFLD: Enable Automated Predictive Library (APL) and Predictive Analysis Library (PAL)
allowUsingScriptServerFunctionality=Ativar Automated Predictive Library (APL) e Predictive Analysis Library (PAL)
#XMSG: User can use SAP HANA Cloud embedded machine learning functions.
allowUsingScriptServerFunctionalityHint=O usuário pode usar as funções internas de machine learning do SAP HANA Cloud.
#XFLD: Password Policy
passwordPolicy=Política de senha
#XMSG: Password Policy
passwordPolicyHint=Ative ou desative a política de senha configurada aqui.
#XFLD: Enable Password Policy
enablePasswordPolicy=Ativar política de senhas
#XMSG: Read Access to the Space Schema
readAccessTitle=Acesso de leitura ao esquema de área
#XMSG: read access hint
readAccessHint=Permita que o usuário do banco de dados conecte ferramentas externas ao esquema de área e leia visões que estejam expostas para consumo.
#XFLD: Space Schema
spaceSchema=Esquema de área
#XFLD: Enable Read Access (SQL)
enableReadAccess=Ativar acesso de leitura (SQL)
#XMSG: Allow the user to grant read access to other users.
withGrantOptionHint=Permita que o usuário conceda acesso de leitura a outros usuários.
#XFLD: With Grant Option
withGrantOption=Com opção de concessão
#XMSG: Make your space data available in your HDI containers.
enableHDIConsumptionHint=Disponibilize dados da sua área nos seus containers HDI.
#XFLD: Enable HDI Consumption
enableHDIConsumption=Ativar consumo de HDI
#XMSG: Write Access to the User’s Open SQL Schema
writeAccessTitle=Acesso de gravação ao esquema Open SQL do usuário
#XMSG: write access hint
writeAccessHint=Permita que o usuário de banco de dados conecte ferramentas externas ao esquema Open SQL para criar entidades de dados e ingerir dados para uso no área.
#XFLD: Open SQL Schema
openSQLSchema=Esquema Open SQL
#XFLD: Enable Write Access (SQL DDL & DML)
enableWriteAccess=Ativar acesso de gravação (SQL, DDL e DML)
#XMSG: audit hint
auditHint=Registre as operações de leitura e alteração no esquema Open SQL.
#XMSG: data consumption hint
dataConsumptionHint=Exponha para consumo todas as novas visões na área por padrão. Os modeladores podem substituir essa configuração por visões individuais por meio da chave "Expor para consumo" no painel lateral de saída da visão. Você também pode escolher os formatos nos quais as visões serão expostas.
#XFLD: Expose for Consumption by Default
exposeDataConsumption=Expor para consumo por padrão
#XMSG: database users hint consumption hint
databaseUsersHint2New=Crie usuários de banco de dados para conectar ferramentas externas ao SAP Datasphere. Defina privilégios para permitir que os usuários leiam os dados da área, criem entidades de dados (DDL) e façam a ingestão de dados (DML) para uso na área.
#XFLD: Read
read=Leitura
#XFLD: Read (HDI)
readHDI=Leitura (HDI)
#XFLD: Write
write=Gravação
#XMSG: HDI Containers Hint
HDIContainersHint2=Ative o acesso aos containers SAP HANA Deployment Infrastructure (HDI) na sua área. Os modeladores podem usar artefatos HDI como fontes para as visões e os clientes HDI podem acessar os dados da sua área.
#XMSG: Open info dialog
openDatabaseUserInfoDialog=Abrir a caixa de diálogo de informações
#XMSG: Database user is locked. Open info dialog to unlock
openLockedDatabaseUserInfoDialog=O usuário do banco de dados está bloqueado. Abra a caixa de diálogo para desbloquear.
#XFLD: Table
table=Tabela
#XFLD: Space section partner configuration title
partnerConnectionConfigurationSectionTitle=Conexão do parceiro
#XFLD: Space section partner configuration subtitle
partnerConnectionConfigurationSectionSubTitle=Configuração da conexão do parceiro
#XFLD: Space section partner configuration hint
partnerConnectionConfigurationSectionHint=Defina seu próprio bloco de conexão de parceiro adicionando o URL eo ícone do iFrame. Essa configuração está disponível apenas para este locatário.
#XFLD: Table Name Field
partnerConnectionConfigurationName=Nome do bloco
#XFLD: Table iFrame URL field
partnerConnectionConfigurationiFrameURL=URL do iFrame
#XFLD: Table iFrame Post Message Origin field
partnerConnectionConfigurationiFramePostMessageOrigin=Origem da mensagem publicada no iFrame
#XFLD: Table icon/image name
partnerConnectionConfigurationIcon=Ícone
#XMSG: Table no result found
partnerConnectionConfigurationNotfound=Não foi possível encontrar uma configuração de conexão de parceiro.
#XMSG: Partner Connection Confgurations are not available
partnerConnectionConfigurationNotAvailable=Não é possível exibir as configurações de conexão do parceiro quando o banco de dados de tempo de execução está indisponível.
#XFLD: Dialog Create Partner Connection Configuration
partnerConnectionConfigurationDialogTitleAdd=Criar a configuração da conexão do parceiro
#XFLD: Dialog Create Partner Connection Configuration Upload Icon
partnerConnectionConfigurationButtonUloadPartnerIcon=Carregar ícone
#XFLD: Dialog Create Partner Connection Configuration Select
partnerConnectionConfigurationSelectIcon=Selecionar (tamanho máximo de 200 KB)
#XFLD: Dialog Create Partner Connection Configuration Enter a name
partnerConnectionConfigurationNamePlaceholder=Exemplo de bloco de parceiro
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL
partnerConnectionConfigurationiFrameURLPlaceholder=http://www.exemplo.com
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL Check
partnerConnectionConfigurationiFramePostMessageOriginPlaceholder=.example.com
#XFLD: Dialog Create Partner Connection Configuration Browse
partnerConnectionConfigurationBrowseButton=Procurar
#XFLD: Create Partner Connection Configuration Msg
partnerConnectionConfigurationUploadMsg=A configuração da conexão do parceiro foi criada com sucesso.
#XMSG: Error while deleting Partner Connection configuration(s)
partnerConnectionConfigurationDeleteError=Ocorreu um erro ao excluir configurações da conexão do parceiro.
#XMSG: Partner Connection Configuration has been successfully deleted
partnerConnectionConfigurationDeleteSuccess=A configuração da conexão do parceiro foi excluída com sucesso
#XMSG: Error while retrieving Partner Connection Configurations
partnerConnectionConfigurationLoadError=Ocorreu um erro ao recuperar as configurações da conexão do parceiro.
#XMSG: Error fileSizeLimitExceeded
partnerConnectionConfigurationfileSizeLimitExceeded=Não foi possível carregar o arquivo, ele excede o tamanho máximo de 200KB.
#XBUT: Create Partner Connection Configuration
partnerConnectionConfigurationButtonCreate=Criar a configuração da conexão do parceiro
#XBUT: Delete Partner Connection Configuration
partnerConnectionConfigurationButtonDelete=Excluir a configuração da conexão do parceiro.
#XMSG: Partner Tile Creation Failure
upsertPartnerTileFailed=Não foi possível criar o bloco do parceiro
#XMSG: Partner Tile Deletion Failure
deletePartnerTileFailed=Não foi possível excluir o bloco do parceiro
#XMSG: HANA cloud connector settings reset failure
resetHANASCCSettingsFailed=Falha ao reinicializar configurações de Cloud Connector do SAP HANA
#XFLD: Workload Class
workloadClass=Classe de carga de trabalho
#XFLD: Workload Management
workloadManagement=Gerenciamento de carga de trabalho
#XFLD: Priority
workloadClassPriority=Prioridade
#XMSG:
workloadManagementPriorityHint=Você pode especificar a priorização desta área ao consultar o banco de dados. Insira um valor de 1 (prioridade mais baixa) até 8 (prioridade mais alta). Em uma situação na qual as áreas concorrem pelos threads disponíveis, as áreas com prioridades mais altas são executadas antes das áreas com prioridades mais baixas.
#XMSG:
workloadClassPriorityHint=Você pode especificar a prioridade da área com valores entre 0 (mais baixa) e 8 (mais alta). As instruções de uma área com alta prioridade são executadas antes das instruções de outras áreas com prioridade mais baixa. A prioridade padrão é 5. O valor 9 é reservado às operações do sistema e, por isso, não está disponível para uma área.
#XFLD: Statement Limits
workloadclassStatementLimits=Limites para instrução
#XFLD: Workload Configuration
workloadConfiguration=Configuração de carga de trabalho
#XMSG:
workloadClassStatementLimitsHint=Você pode especificar o número máximo (ou porcentagem) de threads e GBs de memória que as instruções executadas simultaneamente na área podem consumir. Você pode inserir qualquer valor ou porcentagem entre 0 (sem limite) e a memória total e threads disponíveis no locatário. \n\n Se você especificar um limite de threads, saiba que isso poderá reduzir o desempenho. \n\nSe você especificar um limite de memória, as instruções que atingirem o limite de memória não serão executadas.
#XMSG:
workloadClassStatementLimitsDescription=A configuração padrão fornece limites de recursos generosos e, ao mesmo tempo, impede que qualquer área individual sobrecarregue o sistema.
#XMSG:
workloadClassStatementLimitCustomDescription=Você pode definir limites de total máximo de threads e de memória que as instruções em execução simultânea na área podem consumir.
#XMSG:
totalStatementThreadLimitHelpText=Um limite de thread muito baixo pode afetar o desempenho da instrução, enquanto valores excessivamente altos ou 0 podem permitir que a área consuma todos os threads disponíveis do sistema.
#XMSG:
totalStatementMemoryLimitHelpText=Um limite de memória muito baixo pode causar problemas de falta de memória, enquanto valores excessivamente altos ou 0 podem permitir que a área consuma toda a memória disponível do sistema.
#XMSG:
totalStatementThreadLimitHelpTextRestricted=Insira uma porcentagem entre 1% e 70% (ou o número equivalente) do número total de threads disponíveis no seu locatário. A definição de um limite muito baixo de threads pode afetar o desempenho das instruções, enquanto valores excessivamente altos podem afetar o desempenho das instruções em outras áreas.
#XMSG:
totalStatementThreadLimitHelpTextDynamic=Insira uma porcentagem entre 1% e {0}% (ou o número equivalente) do número total de threads disponíveis no seu locatário. A definição de um limite muito baixo de threads pode afetar o desempenho das instruções, enquanto valores excessivamente altos podem afetar o desempenho das instruções em outras áreas.
#XMSG:
totalStatementMemoryLimitHelpTextNew=Insira um valor ou porcentagem entre 0 (sem limite) e a quantidade total de memória disponível no seu locatário. A definição de um limite muito baixo de memória pode afetar o desempenho das instruções, enquanto valores excessivamente altos podem afetar o desempenho das instruções em outras áreas.
#XFLD: Total Statement Thread Limit
totalStatementThreadLimit=Limite total de threads de instrução
#XFLD: Segmented button label
workloadclassPercentage=%
#XFLD: Segmented button label
workloadclassGigaByte=GB
#XFLD, 80: Segmented button label
workloadclassThreads=Threads
#XFLD: Total Statement Memory Limit
totalStatementMemoryLimit=Limite total de memória de instrução
#YMSE: Error while loading customer hana info
loadCustomerHanaInfoError=Falha ao carregar informações do cliente SAP HANA
#XMSG:
minimumLimitReached=Limite mínimo atingido.
#XMSG:
maximumLimitReached=Limite máximo atingido.
#XMSG: Name Taken for Technical Name
technical-name-taken=Uma conexão com o nome técnico inserido já existe. Insira outro nome.
#XMSG: Name Too long for Technical Name
technical-name-too-long=O nome técnico inserido excede 40 caracteres. Insira um nome com menos caracteres.
#XMSG: Technical name field empty
technical-name-field-empty=Informe um nome técnico.
#XMSG: Generic error when user enters invalid characters for a field
field-invalid-characters=Você só pode usar letras (a-z), números (0-9) e sublinhados (_) para o nome.
#XMSG: Generic error when user enters invalid initial characters for a field
field-underscore-trim=O nome inserido não pode começar nem terminar com sublinhado (_).
#XFLD: Checkbox label for Enable Statement Limits
enableStatementLimits=Ativar limites de instrução
#XFLD: Connection SubSection Settings Title in space details
connectionSettings=Configurações
#XMSG: Connections tool hint in Space details section
connectionsToolHint=Para criar ou editar conexões, abra o app Conexões na navegação lateral ou clique aqui:
#XBUT: Connection list Open Connection Tool Button
button-connectiontool=Ir para Conexões
#XFLD: Not deployed label on space tile
notDeployedLabel=A área ainda não foi implementada.
#XFLD: Not deployed additional text on space tile
notDeployedText=Implemente a área.
#XFLD: Corrupt space label on space tile
corruptSpace=Algo ocorreu de errado.
#XFLD: Corrupt space additional text on space tile
corruptSpaceHint=Tente implementar novamente ou entre em contato com o suporte
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Dados do log de auditoria
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Dados administrativos
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Outros dados
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Dados nas áreas
#XMSG: Unlock Space confirmation text
unlockSpaceConfirmation=Realmente deseja desbloquear a área?
#XMSG: Lock Space confirmation text
lockSpaceConfirmation=Realmente deseja bloquear a área?
#XFLD: Lock
lock=Bloquear
#XFLD: Unlock
unlock=Desbloquear
#XFLD: Locking
locking=Bloqueando
#XMSG: Success message after locking space
lockSpaceSuccessMsg=Área bloqueada
#XMSG: Success message after unlocking space
unlockSpaceSuccessMsg=Área desbloqueada
#XMSG: Success message after locking spaces
lockSpacesSuccessMsg=Áreas bloqueadas
#XMSG: Success message after unlocking spaces
unlockSpacesSuccessMsg=Áreas desbloqueadas
#YMSE: Error while locking a space
lockSpaceError=A área não pode ser bloqueada.
#YMSE: Error while unlocking a space
unlockSpaceError=A área não pode ser desbloqueada.
#XTIT: popup title Warning
confirmationWarningTitle=Aviso
#XMSG: Message strip message when a space was locked manually
manualLockedMessage=A área foi bloqueada manualmente.
#XMSG: Message strip message when a space was locked due to the amount of generated audit logs
auditLogLockedMessage=A área foi bloqueada pelo sistema porque os logs de auditoria consumem uma grande quantidade de disco em GB.
#XMSG: Message strip message when a space was locked because the assigned quota was exceeded.
quotaLockedMessage=A área foi bloqueada pelo sistema porque ela excedeu sua alocação de armazenamento em memória ou em disco.
#XMSG: Unlock Spaces confirmation text
unlockSpacesConfirmation=Realmente deseja desbloquear as áreas selecionadas?
#XMSG: Lock Spaces confirmation text
lockSpacesConfirmation=Realmente deseja bloquear as áreas selecionadas?
#XTIT: Scoped role editor dialog title
scopedRoleEditorDialogTitle=Editor de funções com escopo
#XTIT: ECN Management title
ecnManagementTitle=Gerenciamento de nó de computação elástica e área
#XFLD: ECNs
ecns=Nós de computação elástica
#XFLD: ECN phase Ready
ecnReady=Pronto
#XFLD: ECN phase Running
ecnRunning=Em execução
#XFLD: ECN phase Initial
ecnInitial=Não pronto
#XFLD: ECN phase Starting
ecnStarting=Iniciando
#XFLD: ECN phase Starting Failed
ecnStartingFailed=Falha ao iniciar
#XFLD: ECN phase Stopping
ecnStopping=Interrompendo
#XFLD: ECN phase Stopping Failed
ecnStoppingFailed=Falha ao interromper
#XBTN: Assign Button
assign=Adicionar áreas
#XBTN: Start Header-Button
start=Iniciar
#XBTN: Update Header-Button
repair=Atualizar
#XBTN: Stop Header-Button
stop=Interromper
#XFLD: ECN hours remaining
ecnHoursRemaining=1000 horas restantes
#XFLD: ECN block-hours remaining
ecnBlockHoursRemaining={0} horas de bloco restantes
#XFLD: ECN block-hour remaining
ecnBlockHourRemaining={0} hora de bloco restante
#XTIT: ECN creation dialog title
ecnCreationDialogTitle=Criar nó de computação elástica
#XTIT: ECN edit dialog title
ecnEditDialogTitle=Editar nó de computação elástica
#XTIT: ECN deletion dialog title
ecnDeletionDialogTitle=Excluir nó de computação elástica
#XTIT: ECN Space assignment dialog title
ecnSpaceAssignmentDialogTitle=Atribuir áreas
#XFLD: ECN ID
ECNIDLabel=Nó de computação elástica
#XTXT: Selected toolbar text
selectedToolbarText=Selecionado: {0}
#XTIT: Elastic Compute Nodes
ECNslong=Nós de computação elástica
#XTIT: Object assignment - Table header: Objects available
objectsAvailable=Nº de objetos
#XTIT: Object assignment - Dialog header text
selectObjects=Selecione as áreas e os objetos que você deseja atribuir ao seu nó de computação elástica:
#XTIT: Object assignment - Table header title: Objects
objects=Objetos
#XTIT: Object assignment - Table header: Type
type=Tipo
#XMSG: Message strip delete database user audit warning
deleteDatabaseUserWarningMessageStrip=Saiba que excluir um usuário do banco de dados resultará na exclusão de todas as entradas de log de auditoria geradas. Se você deseja manter os logs de auditoria, considere exportá-las antes de excluir o usuário do banco de dados.
#XMSG: Message strip delete HDI container audit logs impact
deleteHDIContainerWarningMessageStrip=Saiba que cancelar a atribuição de um container HDI da área resultará na exclusão de todas as entradas de log de auditoria geradas. Se você deseja manter os logs de auditoria, considere exportá-los antes de cancelar a atribuição do container HDI.
#XTXT: All audit logs
allAuditLogs=Todas as entradas de log de auditoria geradas para a área
#XMSG: Message strip disable audit warning
disableAuditWarningMessageStrip=Saiba que desativar uma política de auditoria (operações de leitura ou alteração) resultará na exclusão de todas as entradas de log de auditoria dela. Se você deseja manter as entradas de log de auditoria, considere exportá-las antes de desativar a política de auditoria.
#XTIT: No Spaces assigned to ECN Illustration title
noSpacesECNTitle=Ainda não há áreas nem objetos atribuídos
#XTIT: No Spaces assigned to ECN Illustration description
noSpacesECNDescription=Para começar a trabalhar com seu nó de computação elástica, atribua uma área ou objetos a ele.
#XTIT: No Spaces Illustration title
noSpacesTitle=Ainda não foi criada nenhuma área
#XTIT: No Spaces Illustration description
noSpacesDescription=Para iniciar a aquisição de dados, crie uma área.
#XTIT: No Spaces Illustration title
noDeletedSpacesTitle=A lixeira está vazia
#XTIT: No Spaces Illustration description
noDeletedSpacesDescription=Você pode recuperar suas áreas excluídas aqui.
#XTXT:  {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDatabaseUsers=Depois que a área for implementada, os seguintes usuários do banco de dados serão excluídos {0} e não poderão ser recuperados:
#XTIT: Delete database users
deleteDatabaseUsersTitle=Excluir usuários do banco de dados
#YMSE: Error if ecn id already exists
ecnIDInputDuplicateError=O ID já existe.
#YMSE: Error if ecn id contains invalid characters
ecnIDInputInvalidError=Só use caracteres minúsculos a – z e números 0 – 9
#YMSE: Error if ecn id is too short
ecnIDInputTooShortError=O ID precisa ter pelo menos {0} caracteres.
#XMSG: ecn id length warning
ecnIdLengthWarning=Máximo de {0} caracteres excedido.
#XFLD: open System Monitor
systemMonitor=Monitor de sistema
#XFLD: open ECN schedule dialog menu entry
schedule=Programação
#XFLD: open create ECN schedule dialog
createSchedule=Criar programação
#XFLD: open change ECN schedule dialog
changeSchedule=Editar programação
#XFLD: open delete ECN schedule dialog
deleteSchedule=Excluir programação
#XFLD: open self assign ECN schedule dialog
selfAssignSchedule=Atribuir programação a mim
#XFLD: open pause ECN schedule dialog
pauseSchedule=Pausar programação
#XFLD: open resume ECN schedule dialog
resumeSchedule=Retomar programação
#XFLD: View Logs
viewLogs=Visualizar logs
#XFLD: Compute Blocks
computeBlocks=Blocos de computação
#XFLD: Memory label in ECN creation dialog
ecnMemory=Memória (GB)
#XFLD: Storage label in ECN creation dialog
ecnStorage=Armazenamento (GB)
#XFLD: Number of CPU label in ECN creation dialog
ecnCPUCount=Número de CPU
#XFLD: ECN updated by label
changedBy=Alterado por
#XFLD: ECN updated on label
changedOn=Alterado em
#XMSG: Success message after creating a Elastic Compute Node
createEcnSuccessMessage=Nó de computação elástica criado
#YMSE: Error while creating a Elastic Compute Node
createEcnError=Não foi possível criar o nó de computação elástica
#XMSG: Success message after updating a Elastic Compute Node
updateEcnSuccessMessage=Nó de computação elástica atualizado
#XMSG: Error while updating a Elastic Compute Node
udpateEcnError=Não foi possível atualizar o nó de computação elástica
#XMSG: Success message after deleting a Elastic Compute Node
deleteEcnSuccessMessage=Nó de computação elástica excluído
#YMSE: Error while deleting a Elastic Compute Node
deleteEcnError=Não foi possível excluir o nó de computação elástica
#XMSG: Success message after starting a Elastic Compute Node
startingEcnSuccessMessage=Iniciando nó de computação elástica
#XMSG: Success message after stopping a Elastic Compute Node
stoppingEcnSuccessMessage=Interrompendo nó de computação elástica
#YMSE: Error while starting a Elastic Compute Node
startingEcnError=Não foi possível iniciar o nó de computação elástica
#YMSE: Error while stopping a Elastic Compute Node
stoppingEcnError=Não foi possível interromper o nó de computação elástica
#XBUT: Add Object button for an ECN
assignObjects=Adicionar objetos
#XFLD: Enable auto object assignment label
enableAutoObjAssignment=Atribuir todos os objetos automaticamente
#XFLD: object type label to be assigned
objectTypeLabel=Tipo (uso semântico)
#XFLD: assigned object type label
assignedObjectTypeLabel=Tipo
#XFLD: technical name label
TechnicalNameLabel=Nome técnico
#XFLD: Object selecttion ECN label
objectSelectionECNLabel=Selecione os objetos que você deseja adicionar ao nó de computação elástica
#XTIT: Add objects dialog title
assignObjectsTitle=Atribuir objetos de
#XFLD: object label with object count
objectLabel=Objeto
#XMSG: No objects available to add message.
noObjectsToAssign=Nenhum objeto disponível para atribuição.
#XMSG: No objects assigned message.
noAssignedObjects=Nenhum objeto atribuído.
#XTIT: Remove objects popup title
removeObjectsOrSpacesConfirmationTitle=Aviso
#XTIT: Delete ECN popup button and title
deleteECNConfirmationButtonAndTitle=Excluir
#XMSG: Remove objects popup text
removeObjectsConfirmation=Deseja realmente remover os objetos selecionados?
#XMSG: Remove spaces popup text
removeSpacesConfirmation=Deseja realmente remover as áreas selecionadas?
#XBUT: Confirmation button for objects dialog
removeObjectsOrSpacesConfirmationBtn=Remover áreas
#XMSG: Success message after removing objects
removeObjectsSuccessMessage=Os objetos expostos foram removidos
#XMSG: Success message after assigning objects
assignObjectsSuccessMessage=Os objetos expostos foram atribuídos
#XFLD: auto object assignment label for space tile
autoObjAssignmentLabel=Todos os objetos expostos
#XFLD: Spaces tab label
spacesTabLabel=Áreas
#XFLD: Exposed objects tab label
consumptionObjectsTabLabel=Objetos expostos
#XMSG: Success message after unassigning spaces
spacesUnassignedSuccessMessage=As áreas foram removidas
#XMSG: Success message after unassigning space
spaceUnassignedSuccessMessage=Área removida
#YMSE: Error while assigning / unassigning spaces
spacesAssignUnassignError=Não foi possível atribuir nem remover as áreas.
#YMSE: Error while removing objects
removeObjectsError=Não foi possível atribuir nem remover os objetos.
#YMSE: Error while removing object
removeObjectError=Não foi possível atribuir nem remover o objeto.
#YMSE: Error when saving the ecn with invalid value
invalidSizingPlanError=O número selecionado anteriormente não é mais válido. Selecione um número válido.
#YMSE: Error when saving the ecn with invalid value
invalidPerformanceClassError=Selecione uma classe de desempenho válida.
#YMSE: Error when saving the ecn with invalid value
performanceClassNotAvailableError=A classe de desempenho selecionada anteriormente "{0}" não é válida atualmente. Selecione a classe de desempenho válida.
#XTXT: Delete ECN confirmation text
deleteConfirmationTxtECN=Deseja realmente excluir o nó de computação elástica?
#XFLD: tooltip for ? button
help=Ajuda
#XFLD: ECN edit button label
editECN=Configurar
#XFLD: Technical type label for ERModel
DWC_ERMODEL=Modelo entidade-relacionamento
#XFLD: Technical type label for Local Table
DWC_LOCAL_TABLE=Tabela local
#XFLD: Technical type label for Remote Table
DWC_REMOTE_TABLE=Tabela remota
#XFLD: Technical type label for Analytic Model
DWC_ANALYTIC_MODEL=Modelo de análise
#XFLD: Technical type label for Task Chain
DWC_TASKCHAIN=Cadeia de tarefas
#XFLD: Technical type label for Data Flow
DWC_DATAFLOW=Fluxo de dados
#XFLD: Technical type label for Replication Flow
DWC_REPLICATIONFLOW=Fluxo de replicação
#XFLD: Technical type label for Transformation Flow
DWC_TRANSFORMATIONFLOW=Fluxo de transformação
#XFLD: Technical type label for Intelligent Lookup
DWC_IDT=Pesquisa inteligente
#XFLD: Technical type label for Repository
DWC_REPOSITORY_PACKAGE=Repositório
#XFLD: Technical type label for Enterprise Search
DWC_ESMODEL=Pesquisa na empresa
#XFLD: Technical type label for View
DWC_VIEW=Visão
#XFLD: Technical type label for Data Product
DWC_DATAPRODUCT=Produto de dados
#XFLD: Technical type label for Data Access Control
DWC_DAC=Controle de acesso aos dados
#XFLD: Technical type label for Folder
DWC_FOLDER=Pasta
#XFLD: Technical type label for Data Entity
DWC_BUSINESS_ENTITY=Entidade empresarial
#XFLD: Technical type label for Business Entity Variant
DWC_BUSINESS_ENTITY_VARIANT=Variante de entidade empresarial
#XFLD: Technical type label for Responsibility Scenario
DWC_AUTH_SCENARIO=Cenário de responsabilidade
#XFLD: Technical type label for Fact Model
DWC_FACT_MODEL=Modelo de fatos
#XFLD: Technical type label for Transformation Flow
DWC_PERSPECTIVE=Perspectiva
#XFLD: Technical type label for Consumption Model
DWC_CONSUMPTION_MODEL=Modelo de consumo
#XFLD: Technical type label for Remote Connection
DWC_REMOTECONNECTION=Conexão remota
#XFLD: Technical type label for Fact Model Variant
DWC_FACT_MODEL_VARIANT=Variante de modelo de fatos
#XMSG: Schedule created alert message
createScheduleSuccess=Programação criada
#XMSG: Schedule updated alert message
updateScheduleSuccess=Programação atualizada
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Programação excluída
#XMSG: Schedule takeover success message
ownerChangeScheduleSuccess=Programação atribuída a você
#XMSG: Message for starting pausing of schedule
pauseScheduleStarted=Pausando 1 programação
#XMSG: Message for starting resuming of schedule
resumeScheduleStarted=Retomando 1 programação
#XFLD: Segmented button label
availableSpacesButton=Disponível
#XFLD: Segmented button label
selectedSpacesButton=Selecionada
#XFLD: Visit website button text
visitWebsite=Visitar site
#XMSG: Warning message on unselecting source language
sourceLanguageRemovalWarning=O idioma de origem selecionado anteriormente será removido.
#XBUT: enable translation confirmation button
enableTranslationConfirmationBtn=Ativar
#XFLD: ECN performance class label
performanceClassLabel=Classe de desempenho
#XTXT performance class memory text
memoryText=Memória
#XTXT performance class compute text
computeText=Computação
#XTXT performance class high-compute text
highComputeText=Alta computação
#XBUT: Recycle Bin Button Text
recycleBin=Lixeira
#XBUT: Restore Button Text
restore=Restaurar
#XMSG: Warning message for new Workload Management UI
priorityWarning=Esta seção é somente leitura. Você pode alterar a prioridade da área na seção Sistema / Configuração / Gerenciamento de carga de trabalho.
#XMSG: Warning message for new Workload Management UI
workloadWarning=Esta seção é somente leitura. Você pode alterar a configuração de carga de trabalho da área na seção Sistema / Configuração / Gerenciamento de carga de trabalho.
#XCOL: Space table-view column: Apache Spark vCPUs
sparkManagementVCPUs=vCPUs do Apache Spark
#XCOL: Space table-view column: Apache Spark Memory (GB)
sparkManagementMemory=Memória do Apache Spark (GB)
#XCOL: Space type (from Space Capability) for Data Ingestion Spaces
ingestionSpaceType=Ingestão de produto de dados
#XFLD: Deployment in progress nodatatext for spark table
sparkTableDeploymentInProgress=Nenhum dado disponível, a área está sendo atualmente implementada
#XFLD: Runtime data being loaded nodatatext for spark table
sparkTableDeploymentLoading=Nenhum dado disponível, a área está sendo atualmente carregada
#XCOL: Space type (from Space Capability) for BW Private Cloud Edition
BWPCESpaceType=SAP BW
#XBUT: Go to Instance Mappings Button
navToMappingsButton=Editar mapeamentos de instância
#XFLD: SAP HANA Data Lake Storage label if less than 1GB
dataLakeStorageUsed=< 1 GB
