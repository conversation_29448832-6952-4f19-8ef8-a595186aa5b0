#DO NOT REMOVE________________________
#XFLD: Breadcrumb for space monitoring page
spaceMonitorBreadcrumb=Jälgimine
#XTXT: Type name for spaces in browser tab page title
space=Ruum
#_____________________________________
#XFLD: Spaces label in
spaces=Ruumid
#XFLD: Manage plan button text
manageQuotaButtonText=Halda plaani
#XBUT: Manage resources button
manageResourcesButton=Halda ressursse
#XFLD: Create space button tooltip
createSpace=Loo ruum
#XFLD: Create
create=Loo
#XFLD: Deploy
deploy=Juuruta
#XFLD: Page
page=Leht
#XFLD: Cancel
cancel=Tühista
#XFLD: Update
update=Uuenda
#XFLD: Save
save=Salvesta
#XFLD: OK
ok=OK
#XFLD: days
days=päeva
#XFLD: Space tile edit button label
edit=Redigeeri
#XFLD: Auto Assign all objects to space
autoAssign=Määra automaatselt
#XFLD: Space tile open monitoring button label
openMonitoring=Jälgi
#XFLD: Delete
delete=Kustuta
#XFLD: Copy Space
copy=Kopeeri
#XFLD: Close
close=Sule
#XCOL: Space table-view column status
status=Olek
#XFLD: Space status active
activeLabel=Aktiivne
#XFLD: Space status locked
lockedLabel=Lukus
#XFLD: Space status critical
criticalLabel=Kriitiline
#XFLD: Space status cold
coldLabel=Külm
#XFLD: Space status deleted
deletedLabel=Kustutatud
#XFLD: Space status unknown
unknownLabel=Tundmatu
#XFLD: Space status ok
okLabel=Töökorras
#XFLD: Database user expired
expired=Aegunud
#XFLD: deployed
deployed=Juurutatud
#XFLD: not deployed
notDeployed=Pole juurutatud
#XFLD: changes to deploy
changesToDeploy=Juurutatavad muudatused
#XFLD: pending
pending=Juurutamine
#XFLD: designtime error
designtimeError=Kujundusaja tõrge
#XFLD: runtime error
runtimeError=Käitusaja tõrge
#XFLD: Space created by label
createdBy=Autor
#XFLD: Space created on label
createdOn=Loomiskuupäev
#XFLD: Space deployed on label
deployedOn=Juurutamiskuupäev
#XFLD: Space ID label
spaceID=Ruumi ID
#XFLD: Priority label
priority=Tähtsus
#XFLD: Space Priority label
spacePriority=Ruumi tähtsus
#XFLD: Space Configuration label
spaceConfiguration=Ruumi konfiguratsioon
#XFLD: Not available
notAvailable=Pole saadaval
#XFLD: WorkloadType default
default=Vaikeväärtus
#XFLD: WorkloadType custom
custom=Kohandatud
#XFLD: Data Lake option label; refers to component SAP HANA Cloud, Data Lake
dataLakeAccess=Andmehoidlale juurdepääs
#XFLD: Translation label
translationLabel=Tõlkimine
#XFLD: Source language label
sourceLanguageLabel=Lähtekeel
#XFLD: Translation CheckBox label
translationCheckBox=Luba tõlkimine
#XTXT: Popover hint that space has to be deployed to show user details
openDatabaseUserHintPopoverText=Kasutaja üksikasjadele juurdepääsemiseks juurutage ruum.
#XTXT: Popover hint that space has to be deployed to open database explorer
openDatabaseExplorerHintPopoverText=Database Exploreri avamiseks juurutage ruum.
#XTXT: Text for when the Data Lake check box is disabled
dataLakeOptionDisabled=Te ei pääse selle ruumi kaudu andmehoidlale juurde, kuna teine ruum juba kasutab seda.
#XFLD: Data Lake check box text; refers to component SAP HANA Cloud, Data Lake
dataLakeCheckBox=Selle ruumi kaudu pääsete andmehoidlale juurde.
#XFLD: Space Priority minimum label extension
low=Madal
#XFLD: Space Priority maximum label extension
high=Kõrge
#XFLD: Space name label
spaceName=Ruumi nimi
#XFLD: Enable deploy objects checkbox
enableDeployObjects=Juuruta objektid
#XTIT: Copy spaces dialog title
copySpaceDialogTitle=Kopeerimine: {0}
#XTXT Human readable text for unselected source language
unSelectedSourceLanguage=(Pole valitud)
#XTXT Human readable text for language code "af"
af=Afrikaans
#XTXT Human readable text for language code "ar"
ar=Araabia
#XTXT Human readable text for language code "bg"
bg=Bulgaaria
#XTXT Human readable text for language code "ca"
ca=Katalaani
#XTXT Human readable text for language code "zh"
zh=Hiina (lihtsustatud)
#XTXT Human readable text for language code "zf"
zf=Hiina
#XTXT Human readable text for language code "hr"
hr=Horvaadi
#XTXT Human readable text for language code "cs"
cs=Tšehhi
#XTXT Human readable text for language code "cy"
cy=Kõmri
#XTXT Human readable text for language code "da"
da=Taani
#XTXT Human readable text for language code "nl"
nl=Hollandi
#XTXT Human readable text for language code "en-UK"
en-UK=Inglise (Ühendkuningriik)
#XTXT Human readable text for language code "en"
en=Inglise (Ameerika Ühendriigid)
#XTXT Human readable text for language code "et"
et=Eesti
#XTXT Human readable text for language code "fa"
fa=Pärsia
#XTXT Human readable text for language code "fi"
fi=Soome
#XTXT Human readable text for language code "fr-CA"
fr-CA=Prantsuse (Kanada)
#XTXT Human readable text for language code "fr"
fr=Prantsuse
#XTXT Human readable text for language code "de"
de=Saksa
#XTXT Human readable text for language code "el"
el=Kreeka
#XTXT Human readable text for language code "he"
he=Heebrea
#XTXT Human readable text for language code "hi"
hi=Hindi
#XTXT Human readable text for language code "hu"
hu=Ungari
#XTXT Human readable text for language code "is"
is=Islandi
#XTXT Human readable text for language code "id"
id=Indoneesia
#XTXT Human readable text for language code "it"
it=Itaalia
#XTXT Human readable text for language code "ja"
ja=Jaapani
#XTXT Human readable text for language code "kk"
kk=Kasahhi
#XTXT Human readable text for language code "ko"
ko=Korea
#XTXT Human readable text for language code "lv"
lv=Läti
#XTXT Human readable text for language code "lt"
lt=Leedu
#XTXT Human readable text for language code "ms"
ms=Malai
#XTXT Human readable text for language code "no"
no=Norra
#XTXT Human readable text for language code "pl"
pl=Poola
#XTXT Human readable text for language code "pt"
pt=Portugali (Brasiilia)
#XTXT Human readable text for language code "pt-PT"
pt-PT=Portugali (Portugal)
#XTXT Human readable text for language code "ro"
ro=Rumeenia
#XTXT Human readable text for language code "ru"
ru=Vene
#XTXT Human readable text for language code "sr"
sr=Serbia
#XTXT Human readable text for language code "sh"
sh=Serbia-horvaadi
#XTXT Human readable text for language code "sk"
sk=Slovaki
#XTXT Human readable text for language code "sl"
sl=Sloveeni
#XTXT Human readable text for language code "es"
es=Hispaania
#XTXT Human readable text for language code "es-MX"
es-MX=Hispaania (Mehhiko)
#XTXT Human readable text for language code "sv"
sv=Rootsi
#XTXT Human readable text for language code "th"
th=Tai
#XTXT Human readable text for language code "tr"
tr=Türgi
#XTXT Human readable text for language code "uk"
uk=Ukraina
#XTXT Human readable text for language code "vi"
vi=Vietnami
#XTIT: Delete spaces confirmation title
deleteConfirmationHeaderText=Kustuta ruumid
#XTXT: Logically delete space delete confirmation text with space name
logicallyDeleteConfirmationBodyTextWithSpaceName=Kas soovite ruumi „{0}“ kindlasti prügikasti teisaldada?
#XTXT: Logically dpace delete confirmation text for multiple spaces
logicallyDeleteConfirmationBodyTextMultiple=Kas soovite {0} valitud ruumi kindlasti prügikasti teisaldada?
#XTXT: Delete space delete confirmation text with space name
deleteConfirmationBodyTextWithSpaceName=Kas soovite kindlasti ruumi „{0}“ kustutada? Seda toimingut ei saa tagasi võtta.
#XTXT: Delete space delete confirmation text for multiple spaces
deleteConfirmationBodyTextMultiple=Kas soovite kindlasti {0} valitud ruumi kustutada? Seda toimingut ei saa tagasi võtta. Järgmine sisu kustutatakse {1}:
#XTXT: permanently
permanently=jäädavalt
#XTXT: {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDetail=Järgmine sisu kustutatakse {0} ja seda ei saa taastada:
#XTXT: Hint Delete space confirmation text
deleteConfirmationHint=Tippige {0} kustutamise kinnitamiseks.
#XTXT: Delete space confirmation error check spelling
deleteConfirmationErrorCheckSpelling=Kontrollige õigekirja ja proovige uuesti.
#XTXT: All Spaces
allSpaces=Kõik ruumid
#XTXT: All data
allData=Kõik ruumis sisalduvad objektid ja andmed
#XTXT: All connections
allConnections=Kõik ruumis määratletud ühendused
#XFLD: Space tile selection box tooltip
clickToSelect=Valimiseks klõpsake
#XTXT: All database users
allDatabaseUsers=Kõik ruumiga seostatud Open SQL-i skeemides sisalduvad objektid ja andmed
#XFLD: remove members button tooltip
deleteUsers=Eemalda liikmed
#XTXT: Space long description text
description=Kirjeldus (kuni 4000 märki)
#XFLD: Add Members button tooltip
addUsers=Lisa liikmeid
#XFLD: Add Users button tooltip
addUsersTooltip=Lisa kasutajad
#XFLD: Edit Users button tooltip
editUsersTooltip=Redigeeri kasutajaid
#XFLD: Remove Users button tooltip
removeUsersTooltip=Eemalda kasutajad
#XFLD: Searchfield placeholder
filter=Otsi
#XCOL: Users table-view column health
health=Seisund
#XCOL: Users table-view column access
access=Juurdepääs
#XFLD: No user found nodatatext
noDataText=Kasutajaid ei leitud
#XTIT: Members dialog title
selectUserDialogTitle=Lisa liikmeid
#XTIT: User dialog title
addUserDialogTitle=Lisa kasutajad
#XTIT: Delete connections confirmation title
deleteConnectionConfirmationHeaderText=Kustuta ühendused
#XTIT: Delete single connection confirmation title
deleteConnectionConfirmationHeaderTextSingle=Kustuta ühendus
#XTXT: Delete connection delete confirmation text
deleteConnectionConfirmationBodyText=Kas soovite kindlasti valitud ühendused kustutada? Need eemaldatakse jäädavalt.
#XTIT: Remote source dialog title
selectRemoteConnectionDialogTitle=Vali ühendused
#XTIT: Share connection dialog title
connectionSharingDialogTitle=Jaga ühendust
#XTIT: Connections shared from other spaces table title
sharedConnectionsFromOtherSpaces=Jagatud ühendused
#XFLD: Add remote source button tooltip
addRemoteConnections=Lisa ühendused
#XFLD: Delete remote source button tooltip
deleteRemoteConnections=Eemalda ühendused
#XFLD: Share remote source button tooltip
shareConnections=Jaga ühendusi
#XFLD: Tile-layout tooltip
tileLayout=Paanipaigutus
#XFLD: Table-layout tooltip
tableLayout=Tabelipaigutus
#XMSG: Success message after creating space
createSpaceSuccessMessage=Ruum on loodud
#XMSG: Success message after copying space
copySpaceSuccessMessage=Ruumi „{0}“ kopeerimine ruumi „{1}“
#XMSG: Success message after deploying space
deploymentSuccessMessage=Ruumi juurutamine on käivitatud
#XMSG: Success message after Apache Spark update started
sparkUpdateStartedSuccessMessage=Apache Sparki värskendamine on alanud
#YMSE: Error while starting Apache Spark update
sparkUpdateStartedErrorMessage=Apache Sparki ei saanud värskendada
#XMSG: Success message after updating space
updateSettingsSuccessMessage=Ruumi üksikasjad on uuendatud
#XMSG: Success message after unlocking space
unlockSpaceSuccessMessage=Ruum on ajutiselt lukust avatud
#XMSG: Success message after deleting a single space
deleteSpaceSuccessMessage=Ruum on kustutatud
#XMSG: Success message after deleting multiple spaces
deleteSpacesSuccessMessage=Ruumid on kustutatud
#XMSG: Success message after restoring a single space
restoreSpaceSuccessMessage=Ruum on taastatud
#XMSG: Success message after restoring multiple spaces
restoreSpacesSuccessMessage=Ruumid on taastatud
#YMSE: Error while updating settings
updateSettingsFailureMessage=Ruumisätteid ei saanud uuendada.
#YMSE: Error while updating data lake option
dataLakeEnabledInOtherSpace=Andmehoidla on juba määratud teisele ruumile. Andmehoidlale pääseb korraga juurde ainult üks ruum.
#YMSE: Error while updating data lake option
virtualTablesExists=Te ei saa andmehoidla määrangut sellelt ruumilt eemaldada, kuna leidub virtuaaltabelit sõltuvusseoseid*. Andmehoidla määrangu eemaldamiseks sellelt ruumilt kustutage virtuaaltabelid.
#YMSE: Error while unlocking space
unlockSpaceFailureMessage=Ruumi ei saanud vabastada.
#YMSE: Error while creating space
createSpaceError=Ruumi ei saanud luua.
#YMSE: Error while creating duplicate space
createDuplicateSpaceError=Ruum nimega {0} on juba olemas.
#YMSE: Error while deleting a single space
deleteSpaceError=Ruumi ei saanud kustutada.
#YMSE: Error details while deleting a single space
deleteSpaceDetailedError=Teie ruum “{0}” ei tööta enam õigesti. Proovige seda uuesti kustutada. Kui see endiselt ei tööta, paluge oma administraatoril oma ruum kustutada või avage pilet.
#YMSE: Error while deleting a single space in files
deleteSpaceInFilesError=Ruumiandmeid ei saanud failidest kustutada.
#YMSE: Error while deleting a single space users
deleteSpaceUsersError=Kasutajaid ei saanud eemaldada.
#YMSE: Error while deleting a single space schema
deleteSpaceSchemasError=Skeeme ei saanud eemaldada.
#YMSE: Error while deleting a single space connections
deleteSpaceConnectionsError=Ühendusi ei saanud eemaldada.
#YMSE: Error while deleting a single space data
deleteSpaceDataError=Ruumiandmeid ei saanud kustutada.
#YMSE: Error while deleting multiple spaces
deleteSpacesError=Ruume ei saanud kustutada.
#YMSE: Error while restoring a single space
restoreSpaceError=Ruumi ei saanud taastada.
#YMSE: Error while restoring multiple spaces
restoreSpacesError=Ruume ei saanud taastada.
#YMSE: Error while creating users
createUsersError=Kasutajaid ei saanud lisada.
#YMSE: Error while removing users
removeUsersError=Me ei saanud kasutajaid eemaldada.
#YMSE: Error while removing user
removeUserError=ei saanud kasutajat eemaldada.
#YMSE: Error assigning roles to a user
roleAssignUnAssignError=Me ei saanud valitud reguleeritud rollile kasutajat lisada. \n\n Te ei saa iseennast reguleeritud rollile lisada. Saate paluda administraatoril teid reguleeritud rollile lisada.
#YMSE: Error assigning user to the space
userAssignError=Kasutajat ei saanud ruumile määrata. \n\n See kasutaja on ulatuses olevate rollide lõikes juba määratud lubatud arvule (100) ruumidele.
#YMSE: Error assigning users to the space
usersAssignError=Kasutajaid ei saanud ruumile määrata. \n\n See kasutaja on ulatuses olevate rollide lõikes juba määratud lubatud arvule (100) ruumidele.
#YMSE: Error while fetching users
fetchUserWithScopedRolesError=Me ei saanud kasutajaid tuua. Proovige hiljem uuesti.
#YMSE: Error while fetching scoped roles
fetchScopedRolesError=Me ei saanud rakendatud rolle tuua.
#YMSE: Error while fetching members
fetchUserError=Liikmeid ei saanud tuua. Proovige hiljem uuesti.
#YMSE: Error while loading run-time database
loadRuntimeError=Me ei saanud käitusaja andmebaasist teavet laadida.
#YMSE: Error while loading spaces
loadSpacesError=Teie ruumide toomisel läks kahjuks midagi valesti.
#YMSE: Error while loading haas resources
loadStorageError=Talletuskoha andmete toomisel läks kahjuks midagi valesti.
#YMSE: Error no data could be loaded
loadDataError=Teie andmete toomisel läks kahjuks midagi valesti.
#XFLD: Click to refresh storage data
clickToRefresh=Uuesti proovimiseks klõpsake siin.
#XTIT: Delete space popup title
deleteSpacePopupTitle=Kustuta ruum
#XCOL: Spaces table-view column name
name=Nimi
#XCOL: Spaces table-view deployment status
deploymentStatus=Juurutamisolek
#XFLD: Disk label in space details
storageLabel=Ketas (GB)
#XFLD: In-Memory label in space details
ramLabel=Mälu (GB)
#XFLD: Memory label on space card
memory=Salvestusruumi mälu
#XFLD: Storage Assignment label in space details
storrageAssignmentLabel=Salvestusruum
#XFLD: Storage Type label in space details
storageTypeLabel=Salvestustüüp
#XFLD: Enable Space Quota
enableSpaceQuota=Luba ruumi kvoot
#XFLD: No Space Quota
noSpaceQuota=Ruumi kvooti pole
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
diskAndInMemoryText=SAP HANA andmebaas (ketas ja mälusisene)
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
dataLakeFilesText=SAP HANA andmehoidla failid
#XFLD: Available scoped roles label
availableRoles=Saadaolevad reguleeritud rollid
#XFLD: Selected scoped roles label
selectedRoles=Valitud reguleeritud rollid
#XCOL: Spaces table-view column models
models=Mudelid
#XCOL: Spaces table-view column users
users=Kasutajad
#XCOL: Spaces table-view column connections
connections=Ühendused
#XFLD: Section header overview in space detail
overview=Ülevaade
#XFLD: Section header Apache Spark in space detail
sparkManagementSection=Apache Spark
#XFLD: Section header Apache Spark in space detail
sparkManagementSubSection=Rakendused
#XFLD: Section header Apache Spark in space detail
taskAssignmentSubSection=Ülesande määramine
#XFLD: vCPU label in Apache Spark section
vCPULabel=vCPU-d
#XFLD: Memory label in Apache Spark section
memoryLabel=Mälu (GB)
#XFLD: space configuration label in Apache Spark section
spaceConfigurationLabel=Ruumi konfiguratsioon
#XFLD: Space Source label
sparkApplicationLabel=Rakendus
#XFLD: Cluster Size label
clusterSizeLabel=Klastri suurus
#XFLD: Driver label
driverLabel=Draiver
#XFLD: Executor label
executorLabel=Käivitaja
#XFLD: max label
maxLabel=Max kasutatud
#XFLD: TrF Default label
trFDefaultLabel=Teisendusvoo vaikesätted
#XFLD: Merge Default label
mergeDefaultLabel=Ühendamise vaikesätted
#XFLD: Optimize Default label
optimizeDefaultLabel=Optimeerimise vaikesätted
#XFLD: Deployment Default label
deploymentDefaultLabel=Kohaliku tabeli (fail) juurutus
#XFLD: max spark source label
driverExecutorMaxUsedSparkSource={0} CPU / {1} GB
#XFLD: Object type label
taskObjectTypeLabel=Objekti tüüp
#XFLD: Task activity label
taskActivityLabel=Tegevus
#XFLD: Task Application ID label
taskApplicationIDLabel=Vaikerakendus
#XFLD: Section header in space detail
generalSettings=Üldsätted
#XMSG: Message strip message when a space is locked
spaceLockedMessageStrip=Süsteem on praegu selle ruumi lukustanud.
#XMSG: Message strip message deploy warning
deployWarningMessageStrip=Selle jaotise muudatused juurutatakse kohe.
#XMSG: Message strip message modify space configuration warning
modifySpaceConfigurationMessageStrip=Võtke arvesse, et nende väärtuste muutmine võib põhjustada jõudlusprobleeme.
#XFLD: Button text to unlock the space again
unlockSpace=Vabasta ruum
#XFLD: Info text for audit log formatted message
auditLogText=Lugemis- või muutmistoimingute (auditipoliitika) registreerimiseks lubage auditilogid. Administraatorid saavad seejärel analüüsida, kes millise toimingu millisel ajahetkel tegi.
#XMSG: Message strip  audit warning
auditLogWarningMessageStrip=Auditilogid võivad rentnikukeskkonnas enda alla võtta palju kettaruumi. Kui lubate auditipoliitika (lugemis- või muutmistoimingud), peaksite kettasalvestusruumi kasutust regulaarselt jälgima (süsteemi jälgimise akna kasutatud kettaruumi kaardil), et vältida ketta täitumist ja kaasneda võivaid teenusekatkestusi. Auditipoliitika keelamisel kustutatakse kõik selle auditilogi kirjed. Kui soovite auditilogi kirjed alles hoida, võiksite need enne auditipoliitika keelamist eksportida.
#XFLD: Message strip link text to open in app help to guide user what to do to unlock his space permanently
unlockSpaceHelp=Kuva spikker
#XMSG: Message strip message when a space will be locked again
spaceTemporarilyUnlockedMessageStrip=See ruum ületab salvestusruumi limiidi ja lukustatakse {0} {1}.
#XMSG: Unit for remaining time until space is locked again
hours=tunni pärast
#XMSG: Unit for remaining time until space is locked again
minutes=minuti pärast
#XFLD: Section header in space detail for auditing
detailsSectionAuditing=Audit
#XFLD: Subsection header in space detail for auditing
auditing=Ruumiauditi sätted
#XFLD: Hot space tooltip
hotSpaceCountTooltip=Kriitilised ruumid: kasutatud mäluruumi maht on suurem kui 90%.
#XFLD: Green space tooltip
greenSpaceCountTooltip=Töökorras ruumid: kasutatud mäluruumi maht on vahemikus 6–90%.
#XFLD: Cold space tooltip
coldSpaceCountTooltip=Külmad ruumid: kasutatud mäluruumi maht on 5% või vähem.
#XFLD: Critical space tooltip
criticalSpaceCountTooltip=Kriitilised ruumid: kasutatud mäluruumi maht on suurem kui 90%.
#XFLD: Green space tooltip
okSpaceCountTooltip=Töökorras ruumid: kasutatud mäluruumi maht on vahemikus 6–90%.
#XFLD: Locked space tooltip
lockedSpaceCountTooltipNew=Lukus ruumid: blokeeritud ebapiisava mälu tõttu.
#XFLD: Locked space tooltip
lockedSpaceCountTooltip=Lukus ruumid
#YMSE: Error while deleting remote source
deleteRemoteError=Ühendusi ei saanud eemaldada.
#XFLD: Space ID input tooltip
spaceIDInfoTooltip=Ruumi ID-d ei saa hiljem muuta.\nLubatud märgid: A–Z, 0–9 ja _
#YMSE: Error when space name is empty
spaceNameEmptyValidationError=Sisestage ruumi nimi.
#YMSE: Error when ECN Business name is empty
ecnNameEmptyValidationError=Sisestage ärinimi.
#YMSE: Error when space id is empty
spaceIDInputEmptyValidationError=Sisestage ruumi ID.
#YMSE: Error when space id contains invalid characters
spaceIDInputInValidError=Lubamatud märgid. Kasutage ainult märke A–Z, 0–9 ja _.
#YMSE: Error when space id exists while creation
spaceIDInputDuplicateError=Ruumi ID on juba olemas.
#XFLD: Space searchfield placeholder
search=Otsi
#XMSG: Success message after creating users
createUsersSuccess=Kasutajad on lisatud
#XMSG: Success message after creating user
createUserSuccess=Kasutaja on lisatud
#XMSG: Success message after updating users
updateUsersSuccess={0} kasutajat on uuendatud
#XMSG: Success message after updating user
updateUserSuccess=Kasutaja on uuendatud
#XMSG: Success message after removing users
removeUsersSuccess={0} kasutajat on eemaldatud
#XMSG: Success message after removing user
removeUserSuccess=Kasutaja on eemaldatud
#XFLD: Schema name
schemaName=Skeemi nimi
#XFLD: used of total
ofTemplate={0} / {1}
#XFLD: Assigned disk label in space overview
assignedStorageTemplate=Määratud ketas ({0} / {1})
#XFLD: Assigned Memory label in space overview
assignedRAMTemplate=Määratud mälu ({0} / {1})
#XFLD: Storage ratio on space
accelearationRAM=Mälukiirendus
#XFLD: No Storage Consumption
noStorageConsumptionText=Salvestuskvooti pole määratud.
#XFLD: Used disk label in space overview
usedStorageTemplate=Salvestusruumi jaoks kasutatud kettaruum ({0}/{1})
#XFLD: Used Memory label in space overview
usedRAMTemplate=Salvestusruumi jaoks kasutatud mälumaht ({0}/{1})
#XFLD: Used Disk tooltip
usedStorageTooltipTemplate={0}/{1} kettaruumist on salvestusruumi jaoks kasutusel
#XFLD: Used Memory tooltip
usedRAMTooltipTemplate={0} / {1} mäluruumist on kasutatud
#XFLD: Assigned Disk tooltip
assignedStorageTooltipTemplate={0} / {1} kettaruumist on määratud
#XFLD: Assigned Memory tooltip
assignedRAMTooltipTemplate={0} / {1} mäluruumist on määratud
#XFLD: Space related used storage tooltip
spaceRelatedUsedStorageTooltipTemplate=Ruumi andmed: {0}
#XFLD: Not space related used storage tooltip
otherDataUsedStorageTooltipTemplate=Muud andmed: {0}
#XFLD: Storage used exceeded popover and tooltip
storageUsedExceededText=Kaaluge oma plaani laiendamist või pöörduge SAP-i toe poole.
#XCOL: Space table-view column used Disk
usedStorage=Salvestusruumi jaoks kasutatud kettaruum
#XCOL: Space monitor column used Memory
usedRAM=Salvestusruumi jaoks kasutatud mälumaht
#XCOL: Space monitor column Schema
tableSchema=Skeem
#XCOL: Space monitor column Storage Type
tableStorageType=Salvestustüüp
#XCOL: Space monitor column Table Type
tableType=Tabelitüüp
#XCOL: Space monitor column Record Count
tableRecordCount=Kirjete arv
#XFLD: Assigned Disk
assignedStorage=Salvestusruumi jaoks määratud ketas
#XFLD: Assigned Memory
assignedRAM=Salvestusruumi jaoks määratud mälu
#XCOL: Space table-view column storage utilization
tableStorageUtilization=Kasutatud salvestusruum
#XFLD: space status
spaceStatus=Ruumi olek
#XFLD: space type
spaceType=Ruumi tüüp
#XFLD: space type label for space with space type "SAP BW bridge" ("SAP BW bridge" is a product name, do not translate it)
abapbridgeSpaceType=SAP BW Bridge
#XFLD: space type label for space with space type "Data Marketplace"
marketplaceSpaceType=Andmepakkuja toode
#XMSG: Error message for when selected space cannot be deleted due to the space type
deleteTypedSpaceError=Te ei saa ruumi {0} kustutada, kuna ruumi tüüp on {1}.
#XMSG: Error message for when selected spaces cannot be deleted due to the space type
deleteTypedSpacesError=Te ei saa {0} valitud ruumi kustutada. Järgmist tüüpi ruume ei saa kustutada: {1}.
#XFLD: space type label for space without space type
defaultSpaceType=SAP Datasphere
#XFLD: Tooltip for monitor space button
monitorSpace=Jälgi
#XFLD: Tooltip for edit space button
editSpace=Redigeeri ruumi
#XMSG: Deletion warning in messagebox
deleteConfirmation=Kas soovite kindlasti selle ruumi kustutada?
#XFLD: Tooltip for delete space button
deleteSpace=Kustuta ruum
#XFLD: storage
storage=Salvestusruumi ketas
#XFLD: username
userName=Kasutajanimi
#XFLD: port
port=Port
#XFLD: hostname
hostName=Hosti nimi
#XFLD: password
password=Parool
#XBUT: Request new password button
requestPassword=Taotle uut parooli
#YEXP: Usage explanation in time data section
timeDataSectionHint=Looge ajakavasid ja dimensioone oma mudelites ja lugudes kasutamiseks.
#YEXP: Usage explanation in Space schema section
spaceSchemaHint=Kas soovite, et teie ruumi andmed oleks kasutatavad teiste tööriistade ja rakenduste poolt? Sel juhul looge üks või mitu kasutajat, kes pääsevad teie ruumi andmetele juurde, ja valige, kas soovite, et kõik tulevased ruumiandmed oleks vaikimisi kasutatavad.
#XTIT: Create schema popup title
createSchemaDialogTitle=Loo Open SQL-i skeem
#XTIT: Generate Time Data dialog title
generateTimeDataDialogTitle=Loo ajakavad ja dimensioonid
#XTIT: Edit Time Data dialog title
editTimeDataDialogTitle=Redigeeri ajakavasid ja dimensioone
#XTIT: Time Data token title
timeDataTokenTitle=Ajaandmed
#XTIT: Time Data token title
timeDataUpdateViews=Uuenda ajaandmete vaated
#XFLD: Time Data token creation in progress label
timeDataCreationInProgress=Loomine on pooleli...
#XFLD: Time Data token creation error label
timeDataCreationError=Loomine nurjus. Proovige uuesti.
#XTIT: Time Data dialog time settings title
timeDataDialogTimeSettingsTitle=Ajakava sätted
#XTIT: Time Data dialog time data table title
timeDataDialogTranslationTablesTitle=Teisendustabelid
#XTIT: Time Data dialog dimensions title
timeDataDialogTimeViewsTitle=Ajadimensioonid
#XFLD: Time Data dialog time range label
timeRangeHint=Määratlege ajavahemik.
#XFLD: Time Data dialog time data table label
timeDataHint=Pange oma tabelile nimi.
#XFLD: Time Data dialog time data dimensions label
timeDimensionsHint=Pange oma dimensioonidele nimi.
#XFLD: Time Data Time range description label
timerangeLabel=Ajavahemik
#XFLD: Time Data dialog from year label
fromYearLabel=Algusaasta
#XFLD: Time Data dialog to year label
toYearLabel=Lõppaasta
#XFLD: Time Data dialog calendar type label
calendarTypeLabel=Kalendri tüüp
#XFLD: Time Data dialog granularity label
granularityLabel=Detailsus
#XFLD: Time Data dialog technical name label
technicalNameLabel=Tehniline nimi
#XFLD: Time Data dialog quarters text table lable
quarterTextsTableLabel=Teisendustabel kvartalite jaoks
#XFLD: Time Data dialog months text table lable
monthsTextsTableLabel=Teisendustabel kuude jaoks
#XFLD: Time Data dialog days text table lable
daysTextsTableLabel=Teisendustabel päevade jaoks
#XFLD: Time Data dialog year label
yearLabel=Aasta dimensioon
#XFLD: Time Data dialog quarter label
quarterLabel=Kvartali dimensioon
#XFLD: Time Data dialog month label
monthLabel=Kuu dimensioon
#XFLD: Time Data dialog day label
dayLabel=Päeva dimensioon
#XFLD: Time Data dialog gregorian calendar type label
gregorian=Gregoriuse
#XFLD: Time Data dialog time granularity day label
day=Päev
#XFLD: Time Data dialog business name maxlength reached
timeRangeValueStateTextMaxLengthBusinessName=Maksimumpikkus 1000 märki on täis.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextMaxRangeExceeded=Maksimaalne ajavahemik on 150 aastat.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeLower=Algusaasta peab olema varasem kui Lõppaasta.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeHigher=Algusaasta peab olema 1900 või hilisem.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeHigher=Lõppaasta peab olema hilisem kui Algusaasta
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeLower=Lõppaasta peab olema varasem kui praegune aasta pluss 100
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningFromYear=Algusaasta hilisemaks muutmine võib põhjustada andmekadu
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningToYear=Lõppaasta varasemaks muutmine võib põhjustada andmekadu
#XMSG: Time Data creation validation error message
timeDataValidationError=Näib, et mõni väli ei sobi. Jätkamiseks kontrollige kohustuslikke välju.
#XMSG: Time Data deletion popup text
deleteTimeDataConfirmation=Kas soovite kindlasti andmed kustutada?
#XMSG: Time Data creation success message
createTimeDataSuccess=Ajaandmed on loodud
#XMSG: Time Data update success message
updateTimeDataSuccess=Ajaandmed on uuendatud
#XMSG: Time Data delete success message
deleteTimeDataSuccess=Ajaandmed on kustutatud
#XMSG: Time Data creation error message
createTimeDataError=Ajaandmete loomisel läks midagi valesti.
#XMSG: Time Data update error message
updateTimeDataError=Ajaandmete uuendamisel läks midagi valesti.
#XMSG: Time Data creation error message
deleteTimeDataError=Ajaandmete kustutamisel läks midagi valesti.
#XMSG: Error message when time data could not be retrieved
retrievingTimeDataError=Ajaandmeid ei saanud laadida.
#XTIT: Time Data deletion popup title
deleteTimeDataConfirmationTitle=Hoiatus
#XTXT: Time Data deletion dependency popup text
deleteTimeDataDependencyText=Me ei saanud teie ajaandmeid kustutada, kuna neid kasutatakse muudes mudelites.
#XTXT: Time Data deletion dependency popup text singular
deleteTimeDataDependencyTextSingular=Me ei saanud teie ajaandmeid kustutada, kuna neid kasutatakse teises mudelis.
#XMSG: Validation error when required time data form field is empty
timeDataRequiredField=See väli on kohustuslik ja seda ei saa tühjaks jätta.
#XFLD: Tooltip for Open in Database Explorer button
openInDBX=Ava Database Exploreris
#YMSE: Dimension Year
dimensionYearView=Dimensioon Aasta
#YMSE: Dimension Year
dimensionQuarterView=Dimensioon Kvartal
#YMSE: Dimension Year
dimensionMonthView=Dimensioon Kuu
#YMSE: Dimension Year
dimensionDayView=Dimensioon Päev
#XFLD: Time Data deletion object title
timeDataUsedIn=(kasutusel {0} mudelis)
#XFLD: Time Data deletion object title singular
timeDataUsedInSingular=(kasutusel 1 mudelis)
#XFLD: Time Data deletion table column provider
provider=Pakkuja
#XTIT: Time Data dependency dialog title
timeDataDependencyDialogTitle=Sõltuvusseosed
#XTIT: Create schema popup title
createSpaceSchemaUserDialogTitle=Loo ruumiskeemi jaoks kasutaja
#XFLD: Create schema button
createSchemaButton=Loo Open SQL-i skeem
#XFLD: Generate TimeData button
generateTimeDataButton=Loo ajakavad ja dimensioonid
#XFLD: Show dependencies button
showDependenciesButton=Kuva sõltuvusseosed
#XFLD: tooltip for disabled buttons when user is not member of space
mustBeMemberOfSpaceForOperation=Selle toimingu tegemiseks peab teie kasutaja olema ruumi liige.
#XFLD: Create space schema user button
createSpaceSchemaUserButton=Loo ruumiskeemi kasutaja
#YMSE: API Schema users load error
loadSchemaUsersError=Kasutajate loendit ei saanud laadida.
#XTIT: API Schema-config popup title
schemaUserDetailsDialogTitle=Ruumiskeemi kasutaja üksikasjad
#XMSG: API Schema user deletion popup text
deleteSpaceSchemaUserConfirmation=Kas soovite kindlasti valitud kasutaja kustutada?
#XMSG: API Schema user deletion confirmation text
userDeleteSuccess=Kasutaja on kustutatud.
#YMSE: API Schema user deletion error
userDeleteError=Kasutajat ei saanud kustutada.
#XFLD: User deleted
userDeleted=Kasutaja on kustutatud.
#XTIT: Remove user popup title
removeUserConfirmationTitle=Hoiatus
#XMSG: Remove user popup text
removeUserConfirmation=Kas soovite kindlasti selle kasutaja eemaldada? Kasutaja ja talle määratud reguleeriud rollid eemaldatakse ruumist.
#XMSG: Remove users popup text
removeUsersConfirmation=Kas soovite kindlasti need kasutajad eemaldada? Kasutajad ja neile määratud reguleertud rollid eemaldatakse ruumist.
#XBUT: Confirmation button for remove user dialog
removeUserConfirmationBtn=Eemalda
#YMSE: No data text for available roles
noDataAvailableRoles=Ruumi ei lisata ühelegi reguleeritud rollile. \n Kasutajate lisamiseks ruumi tuleb ruum esmalt lisada vähemalt ühele reguleeritud rollile.
#YMSE: No data text for selected roles
noDataSelectedRoles=Valitud reguleeritud rolle pole
#XTIT: Schema-config popup title
schemaDetailsDialogTitle=Open SQL-i skeemi konfiguratsiooni üksikasjad
#XFLD: Label for Read Audit Log
auditLogRead=Luba auditilogi lugemistoimingute jaoks
#XFLD: Label for Change Audit Log
auditLogChange=Luba auditilogi muutmistoimingute jaoks
#XFLD: Label Audit Log Retention
auditLogRetention=Säilita logisid
#XFLD: Label Audit Log Retention Unit
retentionUnit=päeva
#YMSE: Error when log retention time is set incorrectly
incorrectRetentionTime=Sisestage täisarv vahemikus {0}-{1}
#XFLD: Label for Cross Space Schema Access
spaceSchemaAccess=Kasuta ruumiskeemi andmeid
#XTIT: Confirm deactivation of space schema consumption dialog title
disableSpaceSchemaConsumptionDialogTitle=Peata ruumiskeemi andmete kasutamine
#XTXT: Confirm deactivation of space schema consumption dialog text
disableSpaceSchemaConsumptionDialogText=See Open SQL-i skeem võib kasutada teie ruumiskeemi andmeid. Kui peatate kasutamise, ei pruugi ruumiskeemi andmetel põhinevad mudelid enam töötada.
#XBUT: Confirmation button for the stop consuming space schema data dialog
stopConsuming=Peata kasutamine
#XTXT: Tooltip for data lake icon
dataLakeIconTooltip=Selle ruumi kaudu pääseb juurde andmehoidlale
#XFLD: Tooltip for data lake table entry
dataLakeTableEntryTooltip=Andmehoidla on lubatud
#XFLD: used In-Memory linit reached tag
usedRamLimitReachedLabel=Mälulimiit on täis
#XFLD: Space storage Limit reached tag
storageLimitReachedLabel=Salvestusruumi limiit on täis
#XFLD: Minimal Storage Limit Reached tag
minStorageLimitReachedLabel=Salvestusruumi miinimumlimiit on täis
#XFLD: Space ram tag
ramLimitReachedLabel=Mälulimiit on täis
#XFLD: Minimal In-Memory Limit Reached tag
minRamLimitReachedLabel=Minimaalne mälulimiit on täis
#XFLD: Popover text after click on space storage limit warning
storageLimitReachedText=Olete jõudnud määratud ruumi salvestuslimiidini {0}. Määrake ruumile salvestusmahtu juurde.
#XFLD: System storage tag
systemStorageLimitReachedLabel=Süsteemi salvestuslimiit on täis
#XMSG: Popover text after click on storage limit warning
systemStorageLimitReachedText=Olete jõudnud süsteemi salvestuslimiidini {0}. Te ei saa enam ruumile salvestusmahtu määrata.
#XMSG: Schema deletion warning
deleteSchemaConfirmation=Selle Open SQL-i skeemi kustutamisel kustutatakse jäädavalt ka kõik salvestatud objektid ja skeemis hallatud seosed. Kas soovite jätkata?
#XMSG: Success message after deleting a schema
schemaDeleteSuccess=Skeem on kustutatud
#YMSE: Error while deleting schema.
schemaDeleteError=Skeemi ei saanud kustutada.
#XMSG: Success message after update a schema
schemaUpdateSuccess=Skeem on uuendatud
#YMSE: Error while updating schema.
schemaUpdateError=Skeemi ei saanud uuendada.
#XMSG: Hint when no password is shown in the dialog.
newPasswordHint=Oleme loonud selle skeemi jaoks parooli. Kui olete oma parooli unustanud või kaotanud, saate taotleda uue. Kopeerige või salvestage uus parool.
#XMSG: Hint when showing the password in the dialog.
savePasswordHint=Kopeerige oma parool. Vajate seda ühenduse seadistamiseks selle skeemiga. Kui olete oma parooli unustanud, avage selle lähtestamiseks see dialoog.
#XFLD,50: Hint on entering schema name in creation dialog
schemaNameHint=Seda nime ei saa pärast skeemi loomist muuta.
#XFLD: Open SQL Schemas section sub headline
schemasSQL=Open SQL
#XFLD: Space schema section sub headline
schemasSpace=Ruum
#XFLD: HDI Container section header
HDIContainers=HDI-ümbrised
#XTXT: Add HDI Containers button tooltip
addHDIContainers=Lisa HDI-ümbrised
#XTXT: Remove HDI Containers button tooltip
removeHDIContainers=Eemalda HDI-ümbrised
#XFLD: Enable HDI Containers feature button
enableHDIContainersButton=Luba juurdepääs
#YMSE: No data text for HDI Containers table
noDataHDIContainers=Ühtegi HDI-ümbrist pole lisatud.
#YMSE: No data text for Timedata section
noDataTimedata=Ajakavasid ja dimensioone pole loodud.
#YMSE: No data text during runtime not available for Timedata section
runtimeNotAvailableNoDataTimedata=Ajatabeleid ja dimensioone ei saa laadida, kuna käitusaja andmebaas pole saadaval.
#YMSE: No data text during runtime not available for HDI section
runtimeNotAvailableNoDataHDI=HDI-ümbriseid ei saa laadida, kuna käitusaja andmebaas pole saadaval.
#YMSE: Error while fetching HDI Containers
fetchHDIContainersError=HDI-ümbriseid ei saanud hankida. Proovige hiljem uuesti.
#XFLD Table column header for HDI Container names
HDIContainerName=HDI-ümbrise nimi
#XFLD: Enable HDI Containers feature dialog title
enableHDIContainersDialogTitle=Luba juurdepääs
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText1New=HDI-ümbriste ja SAP Datasphere’i ruumide vahel andmete vahetamiseks ilma andmete teisaldamise vajaduseta saate SAP Datasphere’i rentnikus lubada lahenduse SAP SQL Data Warehousing.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText2=Selleks avage tugiteenusepilet klõpsates allolevat nuppu.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText3New=Kui pilet on töödeldud, peate SAP Datasphere’i käitusaja andmebaasis koostama ühe või mitu uut HDI-ümbrist. Seejärel asendatakse nupp „Luba juurdepääs“ SAP Datasphere’i kõikide ruumide HDI-ümbriste jaotises plussmärginupuga (+) ja saate lisada oma ümbrised ruumi.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText41=Kas vajate rohkem teavet? Minge lehele %%0. Üksikasjalikumat teavet selle kohta, mida piletisse kaasata, leiate siit: %%1.
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkText=SAP-i spikker
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkTextSapNote3057059=SAP-i teadaanne 3057059
#XBUT: Open Ticket Button Text
openTicket=Ava pileti
#XBUT: Add Button Text
add=Lisa
#XBUT: Next Button Text
next=Järgmine
#XBUT: Edit Button Text
editUsers=Redigeeri
#XBUT: create user Button Text
createUser=Loo
#XBUT: Update user Button Text
updateUser=Vali
#XTIT: Add HDI Containers Dialog Title
addHDIContainersDialogTitle=Lisa määramata HDI-ümbrised
#XFLD: No data text for add HDI container dialog
addHDIContainersDialogNoDataText=Me ei leidnud määramata ümbriseid. \n Ümbris, mida otsite, võib juba olla määratud ruumile.
#YMSE: Error while loading assigned HDI Containers
loadAssignedHDIContainersError=Määratud HDI-ümbriseid ei saanud laadida.
#YMSE: Error while loading assigned HDI Containers
loadUnassignedHDIContainersError=HDI-ümbriseid ei saanud laadida.
#XMSG: Success message
succeededToAddHDIContainer=HDI-ümbris on lisatud
#XMSG: Success message
succeededToAddHDIContainerPlural=HDI-ümbrised on lisatud
#XMSG: Success message
succeededToDeleteHDIContainer=HDI-ümbris on eemaldatud
#XMSG: Success message
succeededToDeleteHDIContainerPlural=HDI-ümbrised on eemaldatud
#XFLD: Time data section sub headline
timeDataSection=Ajakavad ja dimensioonid
#XFLD: Schemas list entry read audit log enabled indicator
readIndicator=Loe
#XFLD: Schemas list entry change audit log enabled indicator
changeIndicator=Muuda
#XFLD: Remote sources section sub headline
allconnections=Ühenduse määramine
#XFLD: Remote sources section sub headline
localconnections=Kohalikud ühendused
#XFLD: Remote sources section sub headline for open Connectors
openConnectors=SAP-i avatud konnektorid
#XFLD: User section sub headline
memberassignment=Liikme määramine
#XFLD: User assignment section sub headline
userAssignment=Kasutajamäärang
#XFLD: User section Access dropdown Member
member=Liige
#XFLD: User assignment section column name
user=Kasutajanimi
#XTXT: Selected role count
selectedRoleToolbarText=Valitud: {0}
#XTIT: Space detail section connections title
detailsSectionConnections=Ühendused
#XTIT: Space detail section data access title
detailsSectionDataAccess=Skeemile juurdepääs
#XTIT: Space detail section time data title
detailsSectionGenerateData=Ajaandmed
#XTIT: Space detail section members title
detailsSectionUsers=Liikmed
#XTIT: Space detail section Users title
detailsSectionUsersTitle=Kasutajad
#XTIT: Out of Storage
insufficientStoragePopoverTitle=Mäluruum on otsas
#XTIT: Storage distribution
storageDistributionPopoverTitle=Kasutatud kettaruum
#XTXT: Out of Storage popover text
insufficientStorageText=Uue ruumi loomiseks vähendage teise ruumi määratud mäluruumi või kustutage ruum, mida te enam ei vaja. Saate oma süsteemi kogumäluruumi suurendada, kutsudes plaanihalduse.
#XMSG: Space id length warning
spaceIdLengthWarning=Märkide maksimumarv {0} on ületatud.
#XMSG: Space name length warning
spaceNameLengthWarning=Märkide maksimumarv {0} on ületatud.
#XMSG: Space id reserved prefix warning
spaceIdnWarningReservedPrefix=Võimalike konfliktide vältimiseks ärge kasutage eesliidet {0}.
#YMSE: Error while loading open SQL schemas
loadLocalSchemasError=Open SQL-i skeeme ei saanud laadida.
#YMSE: Error while creating open SQL schema
createLocalSchemaError=Open SQL-i skeemi ei saanud luua.
#YMSE: Error while loading all remote connections
loadRemoteConnectionsError=Kõiki kaugkonnektoreid ei saanud laadida.
#YMSE: Error while loading space details
loadSpaceDetailsError=Ruumi üksikasju ei saanud laadida.
#YMSE: Error while deploying space details
deploySpaceDetailsError=Ruumi ei saanud juurutada.
#YMSE: Error while copying space details
copySpaceDetailsError=Ruumi ei saanud juurutada.
#YMSE: Error while loading storage data
loadStorageDataError=Ladustusandmed ei saanud laadida.
#YMSE: Error while loading all users
loadAllUsersError=Kõiki kasutajaid ei saanud laadida.
#YMSE: Failed to reset password
resetPasswordError=Parooli ei saanud lähtestada,
#XMSG: Success message after updating space
resetPasswordSuccessMessage=Skeemi jaoks on määratud uus parool
#YMSE: DP Agent-name too long
DBAgentNameError=DP agendi nimi on liiga pikk.
#YMSE: Schema-name not valid.
schemaNameError=Skeemi nimi ei sobi.
#YMSE: User name not valid.
UserNameError=Kasutajanimi ei sobi.
#XTIT: Consumption by Storage Type
consumptionStorageTypeText=Kasutus mäluruumitüübi järgi
#XTIT: Consumption by Schema
consumptionSchemaText=Kasutus skeemi järgi
#XTIT: Overall Consumption by schema
overallConsumptionSchemaText=Üldine tabelikasutus skeemi järgi
#XTIT: Overall Consumption by Table Type
overallConsumptionTableTypeText=Üldine kasutus tabelitüübi järgi
#XTIT: Tables
tableDetailsText=Tabeli üksikasjad
#XTIT: Table Storage Consumption
tableStorageConsumptionText=Tabeli mäluruumi kasutus
#XFLD: Table Type label
tableTypeLabel=Tabelitüüp
#XFLD: Schema label
schemaLabel=Skeem
#XFLD: reset table tooltip
resetTable=Lähtesta tabel
#XFLD: In-Memory label in space monitor
inMemoryLabel=Mälu
#XFLD: Disk label in space monitor
diskLabel=Ketas
#XFLD: Yes
yesLabel=Jah
#XFLD: No
noLabel=Ei
#XFLD: switch label: Want the data in this space to be consumable by default?
allowConsumptionByDefault=Kas soovite, et selle ruumi andmed oleks vaikimisi kasutatavad?
#XFLD: Business Name
businessNameLabel=Ärinimi
#XFLD: Refresh
refresh=Värskenda
#XMSG: No filter results title
noFilterResultsTitle=Näib, et teie filtrisätted ei kuva andmeid.
#XMSG: No filter results message
noFilterResultsMsg=Proovige oma filtrisätteid täpsustada, ja kui te siiski andmeid ei näe, looge mõni tabel andmemudelite haldamises. Kui need tarbivad salvestusruumi, saate neid siit jälgida.
#XMSG: Space monitoring Hana state red title
spaceMonitorHanaRedTitle=Käitusaja andmebaas pole saadaval.
#XMSG: Space monitoring Hana state red message
spaceMonitorHanaRedMsg=Kuna käitusaja andmebaas pole saadaval, on teatud funktsioonid keelatud ja me ei saa sellel lehel teavet kuvada.
#YMSE: Error while creating space schema user
createSpaceSchemaUserError=Ruumiskeemi kasutajat ei saanud luua.
#YMSE: Error User name already exists
userAlreadyExistsError=Kasutajanimi on juba olemas.
#YMSE: Error Authentication failed
authenticationFailedError=Autentimine nurjus.
#XMSG: Message strip message when a schema user is locked
userLockedMessageStrip=Kasutaja lukustati liiga suure arvu nurjunud sisselogimiste tõttu. Kasutaja vabastamiseks taotlege uus parool.
#XMSG: Success message after reset and unlock schema user
resetUnlockPasswordSuccessMessage=Uus parool on määratud ja kasutaja on vabastatud
#XMSG: user is locked message
userLockedMessage=Kasutaja on lukus.
#XCOL: Users table-view column Role
spaceRole=Roll
#XCOL: Users table-view column Scoped Role
spaceScopedRole=Reguleeritud roll
#XCOL: Users table-view column Space Admin
spaceAdmin=Ruumi administraator
#XFLD: User section dropdown value Viewer
viewer=Vaatur
#XFLD: User section dropdown value Modeler
modeler=Mudeldi
#XFLD: User section dropdown value Data Integrator
dataIntegrator=Andmete integreerija
#XFLD: User section dropdown value Space Administrator
spaceAdministrator=Ruumi administraator
#XMSG: Success message -  Space Role updated successfully.
updateUserSpaceRoleSuccess=Ruumi roll on uuendatud
#XMSG: Error message - Failed to update Space Role.
updateUserSpaceRoleError=Ruumi rolli ei uuendatud.
#XFLD:
databaseUserNameSuffix=Andmebaasikasutaja nime järelliide
#XTXT: Space Schema password text
spaceSchemaPasswordText=Selle skeemiühenduse seadistamiseks kopeerige oma parool. Kui olete oma parooli unustanud, saate alati taotleda uue.
#XFLD: Cloud Platform label in space schema dialog
cloudPlatformLabel=Cloud Platform
#XTXT: Cloud Platform text in space schema dialog
cloudPlatformText=Selle kasutaja kaudu juurdepääsu seadistamiseks lubage kasutus ja kopeerige mandaat. Kui saate kopeerida ainult mandaadi ilma paroolita, lisage parool kindlasti hiljem.
#XFLD: enable consumption in CloudPlatform label in space schema dialog
enableConsumptionInCloudPlatformLabel=Luba kasutus Cloud Platformil
#XFLD: Credentials for User-Provided Service label in space schema dialog
cloudPlatformCredentialsLabel=Mandaat kasutaja pakutava teenuse jaoks:
#XFLD: Credentials for User-Provided Service (Without Password) label in space schema dialog
cloudPlatformCredentialsWithoutPasswordLabel=Mandaat kasutaja pakutava teenuse jaoks (ilma paroolita):
#XFLD: Copy Credentials label in space schema dialog
copyCredentialsLabel=Kopeeri mandaat ilma paroolita
#XFLD: Copy Credentials label in space schema dialog
copyFullCredentialsLabel=Kopeeri täielik mandaat
#XFLD: Copy Password label in space schema dialog
copyPasswordLabel=Kopeeri parool
#XMSG: Credentials copied to clipboard
credentialsCopiedMessage=Mandaat kopeeriti lõikelauale
#XMSG: Password copied to clipboard
passwordCopiedMessage=Parool kopeeriti lõikelauale
#XMSG: create database user dialog title
createDatabaseUserDialogTitle=Loo andmebaasikasutaja
#XMSG: Database Users section title
databaseUsers=Andmebaasikasutajad
#XMSG: Database User Details dialog title
databaseUserDetailsDialogTitle=Andmebaasikasutaja üksikasjad
#XFLD: database user read audit log
databaseUserAuditLogRead=Luba auditilogid lugemistoimingute jaoks ja säilita logid:
#XFLD: database user change audit log
databaseUserAuditLogChange=Luba auditilogid muutmistoimingute jaoks ja säilita logid:
#XMSG: Cloud Platform Access
cloudPlatformAccess=Cloud Platformile juurdepääs
#XFLD: Enable Consumption in Cloud Platform hint
enableConsumptionCloudPlatformHint=Seadistage juurdepääs oma HANA Deployment Infrastructure’i (HDI) ümbrisele selle andmebaasikasutaja kaudu. Oma HDI-ümbrisega ühenduse loomiseks peab olema SQL-i mudeldamine sisse lülitatud
#XFLD: Enable HDI Consumption
enableConsumptionCloudPlatform=Luba HDI kasutus
#XFLD: Enable Consumption hint
enableConsumptionHint=Kas soovite, et teie ruumi andmed oleks kasutatavad muude tööriistade või rakenduste poolt?
#XFLD: Enable Consumption
enableConsumption=Luba SQL-i kasutus
#XFLD: Enable Modeling
enableModeling=Luba SQL-i mudeldamine
#XMSG: Privileges for Data Modeling
privilegesModeling=Andmehulkade kiirimport
#XMSG: Privileges for Data Consumption
privilegesConsumption=Andmekasutus väliste tööriistade jaoks
#XFLD: SQL Modeling
sqlModeling=SQL-i mudeldamine
#XFLD: SQL Consumption
sqlConsumption=SQL-i kasutus
#XFLD: enabled
enabled=Lubatud
#XFLD: disabled
disabled=Keelatud
#XFLD: Edit Privileges
editPrivileges=Redigeeri õigusi
#XFLD: Open Database Explorer
openDBX=Ava Database Explorer
#XFLD: create database user hint
databaseCreateHint=Arvestage, et pärast salvestamist pole võimalik kasutajanime rohkem muuta.
#XFLD: Internal Schema Name
internalSchemaName=Sisemise skeemi nimi
#YMSE: Failed to load database users
loadDatabaseUserError=Andmebaasikasutajate laadimine nurjus
#YMSE: Failed to delete database user
deleteDatabaseUsersError=Andmebaasikasutajate kustutamine nurjus
#XMSG: Success message after deleting database user
deleteDatabaseUserSuccess=Andmebaasikasutaja on kustutatud
#XMSG: Success message after deleting database users
deleteDatabaseUsersSuccess=Andmebaasikasutajad on kustutatud
#XMSG: Success message after creating database user
createDatabaseUserSuccessMessage=Andmebaasikasutaja on loodud
#YMSE: Failed to create database user
createDatabaseUserError=Andmebaasikasutaja loomine nurjus
#XMSG: Success message after updating database user
updateDatabaseUserSuccessMessage=Andmebaasikasutaja on uuendatud
#YMSE: Failed to update database user
updateDatabaseUserError=Andmebaasikasutaja uuendamine nurjus
#XFLD: HDI Consumption
hdiConsumption=HDI kasutus
#XTIT: Space detail section Database Access title
detailsSectionDatabaseAccess=Andmebaasile juurdepääs
#XTXT: Database users consumption hint
databaseUsersConsumptionHint=Muutke oma ruumi andmed vaikimisi kasutatavaks. Koosturites olevad mudelid lubavad automaatselt andmete kasutuse.
#XFLD: Default Consumption of Space Data:
defaultConsumptionOfSpaceData=Ruumiandmete vaikekasutus:
#XFLD: Database User Name
databaseUserName=Andmebaasikasutaja nimi
#XMSG: Database User creation validation error message
databaseUserValidationError=Näib, et mõni väli ei sobi. Jätkamiseks kontrollige kohustuslikke välju.
#XMSG: SQL modeling can’t be enabled since this user has been migrated.
migratedDatabaseUserHint=Andmehulkade kiirimporti ei saa lubada, kuna see kasutaja on migreeritud.
#XBUT: Remove Button Text
remove=Eemalda
#XBUT: Remove Spaces Button Text
removeSpaces=Eemalda ruumid
#XBUT: Remove Objects Button Text
removeObjects=Eemalda objektid
#XMSG: No members have been added yet.
noMembersAssigned=Ühtegi liiget pole veel lisatud.
#XMSG: No users have been added yet.
noUsersAssigned=Ühtegi kasutajat pole veel lisatud.
#XMSG: No database users have been created or your filter is not showing any data.
noDatabaseUsers=Ühtegi andmebaasikasutajat pole loodud või teie filter ei kuva andmeid.
#XMSG: Please enter a user name.
noDatabaseUsername=Sisestage kasutajanimi.
#XMSG: The user name is too long. Please use a shorter one.
databaseUsernameTooLong=Kasutajanimi on liiga pikk. Kasutage lühemat nime.
#XMSG: No privileges have been enabled and this database user will have limited functionality. Do you still want to continue?
noPrivilegesConfirmationMessage=Ühtegi õigust pole lubatud ja sellel andmebaasikasutajal on piiratud funktsioonid. Kas soovite siiski jätkata?
#XMSG: To enable audit logs for change operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogChangeMessage=Auditilogide lubamiseks muutmistoimingute jaoks peab olema lubatud ka andmehulkade kiirimport. Kas soovite seda teha?
#XMSG: To enable audit logs for read operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogReadMessage=Auditilogide lubamiseks lugemistoimingute jaoks peab olema lubatud ka andmehulkade kiirimport. Kas soovite seda teha?
#XMSG: To enable HDI consumption data ingestion and data consumption needs to be enabled. Do you want to enable all?
enableHDIConsumptionMessage=HDI kasutuse lubamiseks peavad olema lubatud ka andmehulkade kiirimport ja andmekasutus. Kas soovite seda teha?
#XMSG:
databaseUserPasswordText=Selle andmebaasikasutajaga ühenduse seadistamiseks kopeerige oma parool. Kui olete oma parooli unustanud, saate alati taotleda uue.
#XTIT: Space detail section members title
detailsSectionMembers=Liikmed
#XMSG: New password set
newPasswordSet=Uus parool on määratud
#XFLD: Data Ingestion
dataIngestion=Andmehulkade kiirimport
#XFLD: Data Consumption
dataConsumption=Andmekasutus
#XFLD: Privileges
privileges=Õigused
#XFLD: Enable Data ingestion
enableDataIngestion=Luba andmehulkade kiirimport
#XMSG: Log the read and change operations for data ingestion.
databaseUserAuditHint=Logige lugemis- ja muutmistoimingud andmehulkade kiirimportimiseks.
#XMSG: Make your space data available in your HDI containers.
databaseUserHDIHint=Muutke oma ruumi andmed kättesaadavaks oma HDI-ümbristes.
#XFLD: Enable Data consumption
enableDataConsumption=Luba andmekasutus
#XMSG: Allow other apps or tools to consume your space data.
enableDataConsumptionHint=Lubage muudel rakendustel või tööriistadel oma ruumiandmeid kasutada.
#XMSG: To set up access via this schema user copy the credentials to your user-provided service. In case you can only copy the credentials without a password make sure to add the password later.
databaseUserHDIConsumptionHint=Selle andmebaasikasutaja kaudu juurdepääsu seadistamiseks kopeerige oma kasutaja pakutava teenuse mandaat. Kui saate kopeerida ainult mandaadi ilma paroolita, lisage parool kindlasti hiljem.
#XFLD: Data Flow Runtime Capacity label in space overview
dataFlowRuntimeCapacity=Andmevoo käitusaja võimsus ({0}:{1} tundi {2} tunnist)
#XFLD: Could not load Data Flow Runtime Capacity
dataFlowRuntimeCapacityError=Andmevoo käitusaja võimsust ei saanud laadida
#XMSG: User can grant data consumption to other users.
databaseUserConsumptionWithGrantOptionHint=Kasutaja saab anda andmekasutuse teistele.
#XFLD: Enable data consumption with grant option
enableConsumptionWithGrantOption=Luba andmekasutus andmissuvandiga
#XMSG: To enable data consumption with grant option data consumption needs to be enabled. Do you want to enable both?
enableConsumptionWithGrantMessage=Andmekasutuse lubamiseks andmissuvandiga peab olema andmekasutus lubatud. Kas soovite mõlemad lubada?
#XFLD: Enable Automated Predictive Library (APL) and Predictive Analysis Library (PAL)
allowUsingScriptServerFunctionality=Luba Automated Predictive Library (APL) ja Predictive Analysis Library (PAL)
#XMSG: User can use SAP HANA Cloud embedded machine learning functions.
allowUsingScriptServerFunctionalityHint=Kasutaja saab kasutada SAP HANA Cloudi masinõppe manusfunktsioone.
#XFLD: Password Policy
passwordPolicy=Paroolipoliitika
#XMSG: Password Policy
passwordPolicyHint=Lubage või keelake konfigureeritud paroolipoliitika siin.
#XFLD: Enable Password Policy
enablePasswordPolicy=Luba paroolipoliitika
#XMSG: Read Access to the Space Schema
readAccessTitle=Lugemispääs ruumiskeemi juurde
#XMSG: read access hint
readAccessHint=Lubage andmebaasikasutajal ühendada välised tööriistad ruumiskeemiga ja lugeda vaateid, mis on avaldatud kasutamiseks.
#XFLD: Space Schema
spaceSchema=Ruumi skeem
#XFLD: Enable Read Access (SQL)
enableReadAccess=Luba lugemispääs (SQL)
#XMSG: Allow the user to grant read access to other users.
withGrantOptionHint=Lubage kasutaja anda teistele kasutajatele lugemispääs.
#XFLD: With Grant Option
withGrantOption=Andmissuvandiga
#XMSG: Make your space data available in your HDI containers.
enableHDIConsumptionHint=Muutke oma ruumi andmed kättesaadavaks oma HDI-ümbristes.
#XFLD: Enable HDI Consumption
enableHDIConsumption=Luba HDI kasutus
#XMSG: Write Access to the User’s Open SQL Schema
writeAccessTitle=Kirjutuspääs kasutaja Open SQL-i skeemi juurde
#XMSG: write access hint
writeAccessHint=Lubage andmebaasikasutajal ühendada välised tööriistad kasutaja Open SQL-i skeemiga, et luua andmeolemid ja kiirimportida andmed ruumis kasutamiseks.
#XFLD: Open SQL Schema
openSQLSchema=Ava Open SQL-i skeem
#XFLD: Enable Write Access (SQL DDL & DML)
enableWriteAccess=Luba kirjutuspääs (SQL, DDL, & DML)
#XMSG: audit hint
auditHint=Logige lugemis- ja muutmistoimingud Open SQL-i skeemis.
#XMSG: data consumption hint
dataConsumptionHint=Avaldage kõik uued vaated ruumis vaikimisi kasutamiseks. Mudeldid võivad selle sätte alistada üksikvaadete jaoks lüliti Avalda kasutamiseks kaudu vaate väljundkülgpaanil. Saate ka valida vormingud, milles vaated avaldatakse.
#XFLD: Expose for Consumption by Default
exposeDataConsumption=Avalda vaikimisi kasutamiseks
#XMSG: database users hint consumption hint
databaseUsersHint2New=Looge andmebaasi kasutajad, et ühendada välised tööriistad SAP Datasphere’iga. Määrake õigused, et lubada kasutajatel lugeda ruumi andmeid ja luua andmeolemeid (DDL) ning kiirimportida ruumis kasutamiseks ette nähtud andmeid (DML).
#XFLD: Read
read=Loe
#XFLD: Read (HDI)
readHDI=Loe (HDI)
#XFLD: Write
write=Kirjuta
#XMSG: HDI Containers Hint
HDIContainersHint2=Lubage juurdepääs oma ruumi SAP HANA Deployment Infrastructure’i (HDI) ümbristele. Mudeldid saavad kasutada HDI artefakte vaadete allikatena ja HDI kliendid pääsevad juurde teie ruumiandmetele.
#XMSG: Open info dialog
openDatabaseUserInfoDialog=Ava teabedialoog
#XMSG: Database user is locked. Open info dialog to unlock
openLockedDatabaseUserInfoDialog=Andmebaasikasutaja on lukus. Vabastamiseks avage dialoog
#XFLD: Table
table=Tabel
#XFLD: Space section partner configuration title
partnerConnectionConfigurationSectionTitle=Partneriseos
#XFLD: Space section partner configuration subtitle
partnerConnectionConfigurationSectionSubTitle=Partneriseose konfiguratsioon
#XFLD: Space section partner configuration hint
partnerConnectionConfigurationSectionHint=Määratlege oma partneriseose paan, lisades oma iFrame’i URL-i ja ikooni. See konfiguratsioon on saadaval ainult selle rentniku jaoks.
#XFLD: Table Name Field
partnerConnectionConfigurationName=Paani nimi
#XFLD: Table iFrame URL field
partnerConnectionConfigurationiFrameURL=iFrame’i URL
#XFLD: Table iFrame Post Message Origin field
partnerConnectionConfigurationiFramePostMessageOrigin=iFrame’i teatepostituse päritolu
#XFLD: Table icon/image name
partnerConnectionConfigurationIcon=Ikoon
#XMSG: Table no result found
partnerConnectionConfigurationNotfound=Ühtegi partneriseose konfiguratsiooni ei leitud.
#XMSG: Partner Connection Confgurations are not available
partnerConnectionConfigurationNotAvailable=Partneriseoste konfiguratsioone ei saa kuvada, kui käitusaja andmebaas pole saadaval.
#XFLD: Dialog Create Partner Connection Configuration
partnerConnectionConfigurationDialogTitleAdd=Loo partneriseose konfiguratsioon
#XFLD: Dialog Create Partner Connection Configuration Upload Icon
partnerConnectionConfigurationButtonUloadPartnerIcon=Üleslaadimisikoon
#XFLD: Dialog Create Partner Connection Configuration Select
partnerConnectionConfigurationSelectIcon=Vali (maksimumaht 200 kB)
#XFLD: Dialog Create Partner Connection Configuration Enter a name
partnerConnectionConfigurationNamePlaceholder=Partneri paani näide
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL
partnerConnectionConfigurationiFrameURLPlaceholder=http://www.example.com
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL Check
partnerConnectionConfigurationiFramePostMessageOriginPlaceholder=.example.com
#XFLD: Dialog Create Partner Connection Configuration Browse
partnerConnectionConfigurationBrowseButton=Sirvi
#XFLD: Create Partner Connection Configuration Msg
partnerConnectionConfigurationUploadMsg=Partneriseose konfiguratsioon on loodud.
#XMSG: Error while deleting Partner Connection configuration(s)
partnerConnectionConfigurationDeleteError=Partneriseose konfiguratsioonide kustutamisel ilmnes tõrge.
#XMSG: Partner Connection Configuration has been successfully deleted
partnerConnectionConfigurationDeleteSuccess=Partneriseose konfiguratsioon on kustutatud.
#XMSG: Error while retrieving Partner Connection Configurations
partnerConnectionConfigurationLoadError=Partneriseose konfiguratsioonide toomisel ilmnes tõrge.
#XMSG: Error fileSizeLimitExceeded
partnerConnectionConfigurationfileSizeLimitExceeded=Faili ei saanud üles laadida, kuna selle maht ületab lubatud maksimummahu 200 kB.
#XBUT: Create Partner Connection Configuration
partnerConnectionConfigurationButtonCreate=Loo partneriseose konfiguratsioon
#XBUT: Delete Partner Connection Configuration
partnerConnectionConfigurationButtonDelete=Kustutage partneriseose konfiguratsioon
#XMSG: Partner Tile Creation Failure
upsertPartnerTileFailed=Partneripaani ei saanud luua.
#XMSG: Partner Tile Deletion Failure
deletePartnerTileFailed=Partneripaani ei saanud kustutada.
#XMSG: HANA cloud connector settings reset failure
resetHANASCCSettingsFailed=Kliendi SAP HANA Cloud Connectori sätete lähtestamine nurjus
#XFLD: Workload Class
workloadClass=Töökoormuse klass
#XFLD: Workload Management
workloadManagement=Töökoormuse haldus
#XFLD: Priority
workloadClassPriority=Tähtsus
#XMSG:
workloadManagementPriorityHint=Andmebaasi päringu ajal saate määrata selle ruumi tähtsusjärjekorra seadmise. Sisestage väärtus alates ühest (madalaima tähtsusastmega) kuni kaheksani (kõrgeima tähtsusastmega). Olukorras, kus ruumid võistlevad saadaolevate lõimede pärast, käitatakse kõrgema tähtsusastmega ruumid enne madalam tähtsusega ruume.
#XMSG:
workloadClassPriorityHint=Saate määrata ruumi tähtsusastme 0-st (madalaim) 8-ni (kõrgeim). Kõrge tähtsusastmega ruumi laused käivitatakse enne muude madalama tähtsusastmega ruumide lauseid. Vaiketähtsusaste on 5. Kuna väärtus 9 on reserveeritud süsteemitoimingutele, pole see ruumi jaoks saadaval.
#XFLD: Statement Limits
workloadclassStatementLimits=Lauselimiidid
#XFLD: Workload Configuration
workloadConfiguration=Töökoormuse konfiguratsioon
#XMSG:
workloadClassStatementLimitsHint=Saate määrata lõimede maksimumarvu (või -protsendi) ja mälu GB-de arvu, mida ruumis samaaegselt töötavad laused saavad kasutada. Saate sisestada mis tahes väärtuse või protsendi vahemikus 0 (limiidita) ja rentnikus saadaoleva mälu ja lõimede koguhulk. \n\n Kui määrate lõimelimiidi, arvestage, et see võib jõudlust vähendada. \n\n Kui määrate mälulimiidi, siis mälulimiiti ületavaid lauseid ei käivitata.
#XMSG:
workloadClassStatementLimitsDescription=Vaikekonfiguratsioon pakub mitmeid ressursipiiranguid, hoides ära ühel ruumil süsteemi ülekoormamist.
#XMSG:
workloadClassStatementLimitCustomDescription=Saate seada lõimede maksimaalse koguarvu ka mälupiirangud, mida ruumis samaaegselt käitatavad laused saavad kasutada.
#XMSG:
totalStatementThreadLimitHelpText=Lõimelimiidi liiga väike limiit võib mõjutada lause jõudlust, samas kui liiga kõrged väärtused või 0 võivad lubada ruumil tarbida kõiki saadaolevaid süsteemilõimi.
#XMSG:
totalStatementMemoryLimitHelpText=Mälulimiidi liiga väike limiit võib põhjustada mälu puudumise probleeme, samas kui liiga kõrged väärtused või 0 võivad lubada ruumil tarbida kogu saadaoleva süsteemimälu.
#XMSG:
totalStatementThreadLimitHelpTextRestricted=Sisestage protsent oma rentnikus saadaolevate lõimede koguarvust vahemikus 1–70% (või sellega võrdne arv). Liiga väike lõimelimiit võib halvendada lause jõudlust ning liiga suur väärtus võib halvendada muudes ruumides olevate lausete jõudlust.
#XMSG:
totalStatementThreadLimitHelpTextDynamic=Sisestage protsent oma rentnikus saadaolevate lõimede koguarvust vahemikus 1–{0}% (või sellega võrdne arv). Liiga väike lõimelimiit võib halvendada lause jõudlust ning liiga suur väärtus võib halvendada muudes ruumides olevate lausete jõudlust.
#XMSG:
totalStatementMemoryLimitHelpTextNew=Sisestage väärtus või protsent vahemikus alates 0 (limiit puudub) kuni teie rentnikus saadaoleva mälu koguhulgani. Liiga väike mälulimiit võib halvendada lause jõudlust ning liiga suur väärtus võib halvendada muudes ruumides olevate lausete jõudlust.
#XFLD: Total Statement Thread Limit
totalStatementThreadLimit=Lauselõimede kogulimiit
#XFLD: Segmented button label
workloadclassPercentage=%
#XFLD: Segmented button label
workloadclassGigaByte=GB
#XFLD, 80: Segmented button label
workloadclassThreads=Lõimed
#XFLD: Total Statement Memory Limit
totalStatementMemoryLimit=Lausemälu kogulimiit
#YMSE: Error while loading customer hana info
loadCustomerHanaInfoError=Kliendi SAP HANA teabe laadimine nurjus.
#XMSG:
minimumLimitReached=Miinimumlimiit on täis.
#XMSG:
maximumLimitReached=Maksimumlimiit on täis.
#XMSG: Name Taken for Technical Name
technical-name-taken=Teie sisestatud tehnilise nimega ühendus on juba olemas. Sisestage teine nimi.
#XMSG: Name Too long for Technical Name
technical-name-too-long=Teie sisestatud tehniline nimi ületab 40 märki. Sisestage väiksema arvu märkidega nimi.
#XMSG: Technical name field empty
technical-name-field-empty=Sisestage tehniline nimi.
#XMSG: Generic error when user enters invalid characters for a field
field-invalid-characters=Saate kasutada nime jaoks ainult tähti (a-z), numbreid (0-9) ja allkriipse (_).
#XMSG: Generic error when user enters invalid initial characters for a field
field-underscore-trim=Teie sisestatud nimi ei saa alata ega lõppeda allkriipsuga (_).
#XFLD: Checkbox label for Enable Statement Limits
enableStatementLimits=Luba lauselimiidid
#XFLD: Connection SubSection Settings Title in space details
connectionSettings=Sätted
#XMSG: Connections tool hint in Space details section
connectionsToolHint=Ühenduste loomiseks või redigeerimiseks avage külgmiselt navigeerimisribalt rakendus Ühendused või klõpsake siin:
#XBUT: Connection list Open Connection Tool Button
button-connectiontool=Ava Ühendused
#XFLD: Not deployed label on space tile
notDeployedLabel=Ruum pole veel juurutatud.
#XFLD: Not deployed additional text on space tile
notDeployedText=Juurutage ruum.
#XFLD: Corrupt space label on space tile
corruptSpace=Midagi läks valesti.
#XFLD: Corrupt space additional text on space tile
corruptSpaceHint=Proovige uuesti juurutada või pöörduge toe poole
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Auditilogi andmed
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Haldusandmed
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Muud andmed
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Andmed ruumides
#XMSG: Unlock Space confirmation text
unlockSpaceConfirmation=Kas soovite tõesti ruumi avada?
#XMSG: Lock Space confirmation text
lockSpaceConfirmation=Kas soovite ruumi kindlasti lukustad?
#XFLD: Lock
lock=Lukusta
#XFLD: Unlock
unlock=Vabasta
#XFLD: Locking
locking=Lukustus
#XMSG: Success message after locking space
lockSpaceSuccessMsg=Ruum on lukus
#XMSG: Success message after unlocking space
unlockSpaceSuccessMsg=Ruum on lukustamata
#XMSG: Success message after locking spaces
lockSpacesSuccessMsg=Ruumid on lukus
#XMSG: Success message after unlocking spaces
unlockSpacesSuccessMsg=Ruumid on lukustamata
#YMSE: Error while locking a space
lockSpaceError=Ruumi ei saa lukustada.
#YMSE: Error while unlocking a space
unlockSpaceError=Ruumi ei saa avada.
#XTIT: popup title Warning
confirmationWarningTitle=Hoiatus
#XMSG: Message strip message when a space was locked manually
manualLockedMessage=Ruum on käsitsi lukustatud.
#XMSG: Message strip message when a space was locked due to the amount of generated audit logs
auditLogLockedMessage=Süsteem on ruumi lukustanud, kuna auditilogid tarbivad suure mahu GB kettast.
#XMSG: Message strip message when a space was locked because the assigned quota was exceeded.
quotaLockedMessage=Süsteem on ruumi lukustanud, kuna selle maht ületab mälu või ketta jaoks eraldatud limiidi.
#XMSG: Unlock Spaces confirmation text
unlockSpacesConfirmation=Kas soovite kindlasti valitud ruumid avada?
#XMSG: Lock Spaces confirmation text
lockSpacesConfirmation=Kas soovite kindlasti valitud ruumid lukustada?
#XTIT: Scoped role editor dialog title
scopedRoleEditorDialogTitle=Reguleeritud rolli redaktor
#XTIT: ECN Management title
ecnManagementTitle=Ruumi ja elastse andmetöötlussõlme haldus
#XFLD: ECNs
ecns=Elastsed andmetöötlussõlmed
#XFLD: ECN phase Ready
ecnReady=Valmis
#XFLD: ECN phase Running
ecnRunning=Käitamine
#XFLD: ECN phase Initial
ecnInitial=Pole valmis
#XFLD: ECN phase Starting
ecnStarting=Käivitamine
#XFLD: ECN phase Starting Failed
ecnStartingFailed=Käivitamine nurjus
#XFLD: ECN phase Stopping
ecnStopping=Peatamine
#XFLD: ECN phase Stopping Failed
ecnStoppingFailed=Peatamine nurjus
#XBTN: Assign Button
assign=Määra ruumid
#XBTN: Start Header-Button
start=Käivita
#XBTN: Update Header-Button
repair=Uuenda
#XBTN: Stop Header-Button
stop=Lõpeta
#XFLD: ECN hours remaining
ecnHoursRemaining=1000 tundi on jäänud
#XFLD: ECN block-hours remaining
ecnBlockHoursRemaining={0} plokitundi on jäänud
#XFLD: ECN block-hour remaining
ecnBlockHourRemaining={0} plokitund on jäänud
#XTIT: ECN creation dialog title
ecnCreationDialogTitle=Elastse andmetöötlussõlme loomine
#XTIT: ECN edit dialog title
ecnEditDialogTitle=Elastse andmetöötlussõlme muutmine
#XTIT: ECN deletion dialog title
ecnDeletionDialogTitle=Elastse andmetöötlussõlme kustutamine
#XTIT: ECN Space assignment dialog title
ecnSpaceAssignmentDialogTitle=Määra ruumid
#XFLD: ECN ID
ECNIDLabel=Elastne andmetöötlussõlm
#XTXT: Selected toolbar text
selectedToolbarText=Valitud: {0}
#XTIT: Elastic Compute Nodes
ECNslong=Elastsed andmetöötlussõlmed
#XTIT: Object assignment - Table header: Objects available
objectsAvailable=Objektide arv
#XTIT: Object assignment - Dialog header text
selectObjects=Valige ruumid ja objektid, mille soovite määrata elastsesse andmetöötlussõlme:
#XTIT: Object assignment - Table header title: Objects
objects=Objektid
#XTIT: Object assignment - Table header: Type
type=Tüüp
#XMSG: Message strip delete database user audit warning
deleteDatabaseUserWarningMessageStrip=Võtke arvesse, et andmebaasi kasutaja kustutamise korral kustutatakse kõik genereeritud auditilogikirjed. Kui soovite auditilogid säilitada, eksportige need enne andmebaasi kasutaja kustutamist.
#XMSG: Message strip delete HDI container audit logs impact
deleteHDIContainerWarningMessageStrip=Võtke arvesse, et ruumile HDI-ümbrise määramise tühistamise korral kustutatakse kõik genereeritud auditilogikirjed. Kui soovite auditilogid säilitada, eksportige need enne HDI-ümbrise määramise tühistamist.
#XTXT: All audit logs
allAuditLogs=Kõik ruumi jaoks genereeritud auditilogikirjed
#XMSG: Message strip disable audit warning
disableAuditWarningMessageStrip=Võtke arvesse, et auditipoliitika (lugemis- või muutmistoimingute) keelamise korral kustutatakse kõik selle auditilogikirjed. Kui soovite auditilogi kirjed säilitada, eksportige need enne auditipoliitika keelamist.
#XTIT: No Spaces assigned to ECN Illustration title
noSpacesECNTitle=Ühtegi ruumi või objekti ei ole veel määratud
#XTIT: No Spaces assigned to ECN Illustration description
noSpacesECNDescription=Elastse andmetöötlussõlmega töötama hakkamiseks määrake sellele ruum või objektid.
#XTIT: No Spaces Illustration title
noSpacesTitle=Ühtki ruumi pole veel loodud
#XTIT: No Spaces Illustration description
noSpacesDescription=Andmete saamise alustamiseks looge ruum.
#XTIT: No Spaces Illustration title
noDeletedSpacesTitle=Prügikast on tühi
#XTIT: No Spaces Illustration description
noDeletedSpacesDescription=Kustutatud ruumid saate siitkaudu taastada.
#XTXT:  {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDatabaseUsers=Pärast ruumi juurutamist kustutatakse järgmised andmebaasikasutajad {0} ja neid ei saa taastada:
#XTIT: Delete database users
deleteDatabaseUsersTitle=Andmebaasi kasutajate kustutamine
#YMSE: Error if ecn id already exists
ecnIDInputDuplicateError=ID on juba olemas.
#YMSE: Error if ecn id contains invalid characters
ecnIDInputInvalidError=Kasutage ainult väiketähti a–y ja numbreid 0–9
#YMSE: Error if ecn id is too short
ecnIDInputTooShortError=ID peab olema vähemalt {0} tähemärki pikk.
#XMSG: ecn id length warning
ecnIdLengthWarning=Märkide maksimumarv {0} on ületatud.
#XFLD: open System Monitor
systemMonitor=Süsteemi jälgimine
#XFLD: open ECN schedule dialog menu entry
schedule=Graafik
#XFLD: open create ECN schedule dialog
createSchedule=Loo graafik
#XFLD: open change ECN schedule dialog
changeSchedule=Redigeeri graafikut
#XFLD: open delete ECN schedule dialog
deleteSchedule=Kustuta ajakava
#XFLD: open self assign ECN schedule dialog
selfAssignSchedule=Määra ajakava mulle
#XFLD: open pause ECN schedule dialog
pauseSchedule=Peata ajakava
#XFLD: open resume ECN schedule dialog
resumeSchedule=Jätka ajakava
#XFLD: View Logs
viewLogs=Kuva logid
#XFLD: Compute Blocks
computeBlocks=Arvutamisplokid
#XFLD: Memory label in ECN creation dialog
ecnMemory=Mälu (GB)
#XFLD: Storage label in ECN creation dialog
ecnStorage=Salvestusruum (GB)
#XFLD: Number of CPU label in ECN creation dialog
ecnCPUCount=Protsessorite arv
#XFLD: ECN updated by label
changedBy=Muutja
#XFLD: ECN updated on label
changedOn=Muutmiskuupäev
#XMSG: Success message after creating a Elastic Compute Node
createEcnSuccessMessage=Elastne andmetöötlussõlm on loodud
#YMSE: Error while creating a Elastic Compute Node
createEcnError=Elastset andmetöötlussõlme ei saanud luua
#XMSG: Success message after updating a Elastic Compute Node
updateEcnSuccessMessage=Elastne andmetöötlussõlm on värskendatud
#XMSG: Error while updating a Elastic Compute Node
udpateEcnError=Elastset andmetöötlussõlme ei saanud värskendada
#XMSG: Success message after deleting a Elastic Compute Node
deleteEcnSuccessMessage=Elastne andmetöötlussõlm on kustutatud
#YMSE: Error while deleting a Elastic Compute Node
deleteEcnError=Elastset andmetöötlussõlme ei saanud kustutada
#XMSG: Success message after starting a Elastic Compute Node
startingEcnSuccessMessage=Elastse andmetöötlussõlme käivitamine
#XMSG: Success message after stopping a Elastic Compute Node
stoppingEcnSuccessMessage=Elastse andmetöötlussõlme peatamine
#YMSE: Error while starting a Elastic Compute Node
startingEcnError=Elastset andmetöötlussõlme ei saanud käivitada
#YMSE: Error while stopping a Elastic Compute Node
stoppingEcnError=Elastset andmetöötlussõlme ei saanud peatada
#XBUT: Add Object button for an ECN
assignObjects=Lisa objektid
#XFLD: Enable auto object assignment label
enableAutoObjAssignment=Lisa kõik objektid automaatselt
#XFLD: object type label to be assigned
objectTypeLabel=Tüüp (semantiline kasutus)
#XFLD: assigned object type label
assignedObjectTypeLabel=Tüüp
#XFLD: technical name label
TechnicalNameLabel=Tehniline nimi
#XFLD: Object selecttion ECN label
objectSelectionECNLabel=Valige objektid, mille soovite määrata elastsesse andmetöötlussõlme
#XTIT: Add objects dialog title
assignObjectsTitle=Määra objektid
#XFLD: object label with object count
objectLabel=Objekt
#XMSG: No objects available to add message.
noObjectsToAssign=Määramiseks ei ole ühtegi objekti saadaval.
#XMSG: No objects assigned message.
noAssignedObjects=Ühtegi objekti pole määratud.
#XTIT: Remove objects popup title
removeObjectsOrSpacesConfirmationTitle=Hoiatus
#XTIT: Delete ECN popup button and title
deleteECNConfirmationButtonAndTitle=Kustuta
#XMSG: Remove objects popup text
removeObjectsConfirmation=Kas soovite kindlasti valitud objektid eemaldada?
#XMSG: Remove spaces popup text
removeSpacesConfirmation=Kas soovite kindlasti valitud ruumid eemaldada?
#XBUT: Confirmation button for objects dialog
removeObjectsOrSpacesConfirmationBtn=Eemalda ruumid
#XMSG: Success message after removing objects
removeObjectsSuccessMessage=Avaldatud objektid on eemaldatud
#XMSG: Success message after assigning objects
assignObjectsSuccessMessage=Avaldatud objektid on määratud
#XFLD: auto object assignment label for space tile
autoObjAssignmentLabel=Kõik avaldatud objektid
#XFLD: Spaces tab label
spacesTabLabel=Ruumid
#XFLD: Exposed objects tab label
consumptionObjectsTabLabel=Avaldatud objektid
#XMSG: Success message after unassigning spaces
spacesUnassignedSuccessMessage=Ruumid on eemaldatud
#XMSG: Success message after unassigning space
spaceUnassignedSuccessMessage=Ruum on eemaldatud
#YMSE: Error while assigning / unassigning spaces
spacesAssignUnassignError=Ruume ei saanud määrata või eemaldada.
#YMSE: Error while removing objects
removeObjectsError=Objekte ei saanud määrata ega eemaldada.
#YMSE: Error while removing object
removeObjectError=Objekti ei saanud määrata ega eemaldada.
#YMSE: Error when saving the ecn with invalid value
invalidSizingPlanError=Eelnevalt valitud arv ei ole enam kehtiv. Valige kehtiv arv.
#YMSE: Error when saving the ecn with invalid value
invalidPerformanceClassError=Valige sobiv jõudlusklass.
#YMSE: Error when saving the ecn with invalid value
performanceClassNotAvailableError=Varem valitud jõudlusklass „{0}“ praegu ei sobi. Valige sobiv jõudlusklass.
#XTXT: Delete ECN confirmation text
deleteConfirmationTxtECN=Kas soovite kindlasti elastse andmetöötlussõlme kustutada?
#XFLD: tooltip for ? button
help=Spikker
#XFLD: ECN edit button label
editECN=Konfigureeri
#XFLD: Technical type label for ERModel
DWC_ERMODEL=Olem-seos andmemudel
#XFLD: Technical type label for Local Table
DWC_LOCAL_TABLE=Kohalik tabel
#XFLD: Technical type label for Remote Table
DWC_REMOTE_TABLE=Kaugtabel
#XFLD: Technical type label for Analytic Model
DWC_ANALYTIC_MODEL=Analüütiline mudel
#XFLD: Technical type label for Task Chain
DWC_TASKCHAIN=Tegumiahel
#XFLD: Technical type label for Data Flow
DWC_DATAFLOW=Andmevoog
#XFLD: Technical type label for Replication Flow
DWC_REPLICATIONFLOW=Tiražeerimisvoog
#XFLD: Technical type label for Transformation Flow
DWC_TRANSFORMATIONFLOW=Teisendusvoog
#XFLD: Technical type label for Intelligent Lookup
DWC_IDT=Intelligentne otsing
#XFLD: Technical type label for Repository
DWC_REPOSITORY_PACKAGE=Hoidla
#XFLD: Technical type label for Enterprise Search
DWC_ESMODEL=Enterprise Search
#XFLD: Technical type label for View
DWC_VIEW=Vaade
#XFLD: Technical type label for Data Product
DWC_DATAPRODUCT=Andmetoode
#XFLD: Technical type label for Data Access Control
DWC_DAC=Andmepääsukontroll
#XFLD: Technical type label for Folder
DWC_FOLDER=Kaust
#XFLD: Technical type label for Data Entity
DWC_BUSINESS_ENTITY=Äriolem
#XFLD: Technical type label for Business Entity Variant
DWC_BUSINESS_ENTITY_VARIANT=Äriolemi variant
#XFLD: Technical type label for Responsibility Scenario
DWC_AUTH_SCENARIO=Vastutusstsenaarium
#XFLD: Technical type label for Fact Model
DWC_FACT_MODEL=Faktimudel
#XFLD: Technical type label for Transformation Flow
DWC_PERSPECTIVE=Perspektiiv
#XFLD: Technical type label for Consumption Model
DWC_CONSUMPTION_MODEL=Tarbimismudel
#XFLD: Technical type label for Remote Connection
DWC_REMOTECONNECTION=Kaugühendus
#XFLD: Technical type label for Fact Model Variant
DWC_FACT_MODEL_VARIANT=Faktimudeli variant
#XMSG: Schedule created alert message
createScheduleSuccess=Graafik on loodud
#XMSG: Schedule updated alert message
updateScheduleSuccess=Graafik on uuendatud
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Graafik on kustutatud
#XMSG: Schedule takeover success message
ownerChangeScheduleSuccess=Graafik on teile määratud
#XMSG: Message for starting pausing of schedule
pauseScheduleStarted=1 graafiku peatamine
#XMSG: Message for starting resuming of schedule
resumeScheduleStarted=1 graafiku jätkamine
#XFLD: Segmented button label
availableSpacesButton=Saadaval
#XFLD: Segmented button label
selectedSpacesButton=Valitud
#XFLD: Visit website button text
visitWebsite=Külasta veebilehte
#XMSG: Warning message on unselecting source language
sourceLanguageRemovalWarning=Varem valitud lähtekeel eemaldatakse.
#XBUT: enable translation confirmation button
enableTranslationConfirmationBtn=Luba
#XFLD: ECN performance class label
performanceClassLabel=Jõudlusklass
#XTXT performance class memory text
memoryText=Mälu
#XTXT performance class compute text
computeText=Andmetöötlus
#XTXT performance class high-compute text
highComputeText=Kõrgjõudlustöötlus
#XBUT: Recycle Bin Button Text
recycleBin=Prügikast
#XBUT: Restore Button Text
restore=Taasta
#XMSG: Warning message for new Workload Management UI
priorityWarning=See ala on kirjutuskaitstud. Ruumi prioriteeti saate muuta jaotises Süsteem / Konfigureerimine / Töökoormuse haldus.
#XMSG: Warning message for new Workload Management UI
workloadWarning=See ala on kirjutuskaitstud. Ruumi töökoormuse konfiguratsiooni saate muuta jaotises Süsteem / Konfigureerimine / Töökoormuse haldus.
#XCOL: Space table-view column: Apache Spark vCPUs
sparkManagementVCPUs=Apache Sparki vCPU-d
#XCOL: Space table-view column: Apache Spark Memory (GB)
sparkManagementMemory=Apache Sparki mälu (GB)
#XCOL: Space type (from Space Capability) for Data Ingestion Spaces
ingestionSpaceType=Andmetoote valmendus
#XFLD: Deployment in progress nodatatext for spark table
sparkTableDeploymentInProgress=Andmeid pole saadaval, kuna ruumi juurutamine on käimas
#XFLD: Runtime data being loaded nodatatext for spark table
sparkTableDeploymentLoading=Andmeid pole saadaval, kuna ruumi laadimine on käimas
#XCOL: Space type (from Space Capability) for BW Private Cloud Edition
BWPCESpaceType=SAP BW
#XBUT: Go to Instance Mappings Button
navToMappingsButton=Redigeeri eksemplaride vastendusi
#XFLD: SAP HANA Data Lake Storage label if less than 1GB
dataLakeStorageUsed=< 1 GB
