#DO NOT REMOVE________________________
#XFLD: Breadcrumb for space monitoring page
spaceMonitorBreadcrumb=Valvonta
#XTXT: Type name for spaces in browser tab page title
space=Tila
#_____________________________________
#XFLD: Spaces label in
spaces=Tilat
#XFLD: Manage plan button text
manageQuotaButtonText=Hallitse suunnitelmaa
#XBUT: Manage resources button
manageResourcesButton=Hallitse resursseja
#XFLD: Create space button tooltip
createSpace=Luo tila
#XFLD: Create
create=Luo
#XFLD: Deploy
deploy=Ota käyttöön
#XFLD: Page
page=Sivu
#XFLD: Cancel
cancel=Peruuta
#XFLD: Update
update=Päivitä
#XFLD: Save
save=Tallenna
#XFLD: OK
ok=OK
#XFLD: days
days=Pv
#XFLD: Space tile edit button label
edit=Muokkaa
#XFLD: Auto Assign all objects to space
autoAssign=Kohdista automaattisesti
#XFLD: Space tile open monitoring button label
openMonitoring=Valvonta
#XFLD: Delete
delete=Poista
#XFLD: Copy Space
copy=Kopioi
#XFLD: Close
close=Sulje
#XCOL: Space table-view column status
status=Tila
#XFLD: Space status active
activeLabel=Aktiivinen
#XFLD: Space status locked
lockedLabel=Lukittu
#XFLD: Space status critical
criticalLabel=Kriittinen
#XFLD: Space status cold
coldLabel=Kylmä
#XFLD: Space status deleted
deletedLabel=Poistettu
#XFLD: Space status unknown
unknownLabel=Tuntematon
#XFLD: Space status ok
okLabel=Kunnossa
#XFLD: Database user expired
expired=Vanhentunut
#XFLD: deployed
deployed=Otettu käyttöön
#XFLD: not deployed
notDeployed=Ei otettu käyttöön
#XFLD: changes to deploy
changesToDeploy=Käyttöön otettavat muutokset
#XFLD: pending
pending=Otetaan käyttöön
#XFLD: designtime error
designtimeError=Suunnitteluajan virhe
#XFLD: runtime error
runtimeError=Ajoajan virhe
#XFLD: Space created by label
createdBy=Tekijä
#XFLD: Space created on label
createdOn=Luontipäivämäärä
#XFLD: Space deployed on label
deployedOn=Käyttöönottopäivämäärä
#XFLD: Space ID label
spaceID=Tilan tunnus
#XFLD: Priority label
priority=Prioriteetti
#XFLD: Space Priority label
spacePriority=Tilan prioriteetti
#XFLD: Space Configuration label
spaceConfiguration=Tilan konfiguraatio
#XFLD: Not available
notAvailable=Ei käytettävissä
#XFLD: WorkloadType default
default=Oletusarvo
#XFLD: WorkloadType custom
custom=Käyttäjän määrittämä
#XFLD: Data Lake option label; refers to component SAP HANA Cloud, Data Lake
dataLakeAccess=Tietojärveen pääsy
#XFLD: Translation label
translationLabel=Käännös
#XFLD: Source language label
sourceLanguageLabel=Lähdekieli
#XFLD: Translation CheckBox label
translationCheckBox=Ota käännös käyttöön
#XTXT: Popover hint that space has to be deployed to show user details
openDatabaseUserHintPopoverText=Ota tila käyttöön, niin voit käyttää käyttäjän lisätietoja.
#XTXT: Popover hint that space has to be deployed to open database explorer
openDatabaseExplorerHintPopoverText=Ota tila käyttöön, niin voit avata Database Explorerin.
#XTXT: Text for when the Data Lake check box is disabled
dataLakeOptionDisabled=Et voi käyttää tätä tilaa tietojärven hakemiseksi, koska se on jo toisen tilan käytössä.
#XFLD: Data Lake check box text; refers to component SAP HANA Cloud, Data Lake
dataLakeCheckBox=Käytä tätä tilaa tietojärveen pääsyyn.
#XFLD: Space Priority minimum label extension
low=Matala
#XFLD: Space Priority maximum label extension
high=Korkea
#XFLD: Space name label
spaceName=Tilan nimi
#XFLD: Enable deploy objects checkbox
enableDeployObjects=Ota objektit käyttöön
#XTIT: Copy spaces dialog title
copySpaceDialogTitle=Kopioi {0}
#XTXT Human readable text for unselected source language
unSelectedSourceLanguage=(Ei valittu)
#XTXT Human readable text for language code "af"
af=Afrikaans
#XTXT Human readable text for language code "ar"
ar=Arabia
#XTXT Human readable text for language code "bg"
bg=Bulgaria
#XTXT Human readable text for language code "ca"
ca=Katalaani
#XTXT Human readable text for language code "zh"
zh=Yksinkertaistettu kiina
#XTXT Human readable text for language code "zf"
zf=Kiina
#XTXT Human readable text for language code "hr"
hr=Kroatia
#XTXT Human readable text for language code "cs"
cs=Tšekki
#XTXT Human readable text for language code "cy"
cy=Kymri
#XTXT Human readable text for language code "da"
da=Tanska
#XTXT Human readable text for language code "nl"
nl=Hollanti
#XTXT Human readable text for language code "en-UK"
en-UK=Englanti (Iso-Britannia)
#XTXT Human readable text for language code "en"
en=Englanti (Yhdysvallat)
#XTXT Human readable text for language code "et"
et=Viro
#XTXT Human readable text for language code "fa"
fa=Persia
#XTXT Human readable text for language code "fi"
fi=Suomi
#XTXT Human readable text for language code "fr-CA"
fr-CA=Ranska (Kanada)
#XTXT Human readable text for language code "fr"
fr=Ranska
#XTXT Human readable text for language code "de"
de=Saksa
#XTXT Human readable text for language code "el"
el=Kreikka
#XTXT Human readable text for language code "he"
he=Heprea
#XTXT Human readable text for language code "hi"
hi=Hindi
#XTXT Human readable text for language code "hu"
hu=Unkari
#XTXT Human readable text for language code "is"
is=Islanti
#XTXT Human readable text for language code "id"
id=Indonesia
#XTXT Human readable text for language code "it"
it=Italia
#XTXT Human readable text for language code "ja"
ja=Japani
#XTXT Human readable text for language code "kk"
kk=Kazakki
#XTXT Human readable text for language code "ko"
ko=Korea
#XTXT Human readable text for language code "lv"
lv=Latvia
#XTXT Human readable text for language code "lt"
lt=Liettua
#XTXT Human readable text for language code "ms"
ms=Bahasa Melayu
#XTXT Human readable text for language code "no"
no=Norja
#XTXT Human readable text for language code "pl"
pl=Puola
#XTXT Human readable text for language code "pt"
pt=Portugali (Brasilia)
#XTXT Human readable text for language code "pt-PT"
pt-PT=Portugali (Portugali)
#XTXT Human readable text for language code "ro"
ro=Romania
#XTXT Human readable text for language code "ru"
ru=Venäjä
#XTXT Human readable text for language code "sr"
sr=Serbia
#XTXT Human readable text for language code "sh"
sh=Serbokroaatti
#XTXT Human readable text for language code "sk"
sk=Slovakki
#XTXT Human readable text for language code "sl"
sl=Sloveeni
#XTXT Human readable text for language code "es"
es=Espanja
#XTXT Human readable text for language code "es-MX"
es-MX=Espanja (Meksiko)
#XTXT Human readable text for language code "sv"
sv=Ruotsi
#XTXT Human readable text for language code "th"
th=Thai
#XTXT Human readable text for language code "tr"
tr=Turkki
#XTXT Human readable text for language code "uk"
uk=Ukraina
#XTXT Human readable text for language code "vi"
vi=Vietnam
#XTIT: Delete spaces confirmation title
deleteConfirmationHeaderText=Poista tilat
#XTXT: Logically delete space delete confirmation text with space name
logicallyDeleteConfirmationBodyTextWithSpaceName=Haluatko varmasti siirtää tilan "{0}" roskakoriin?
#XTXT: Logically dpace delete confirmation text for multiple spaces
logicallyDeleteConfirmationBodyTextMultiple=Haluatko varmasti siirtää {0} valittua tilaa roskakoriin?
#XTXT: Delete space delete confirmation text with space name
deleteConfirmationBodyTextWithSpaceName=Haluatko varmasti poistaa tilan "{0}"? Tätä toimintoa ei voi kumota.
#XTXT: Delete space delete confirmation text for multiple spaces
deleteConfirmationBodyTextMultiple=Haluatko varmasti poistaa {0} valittua tilaa? Tätä toimintoa ei voi kumota. Seuraava sisältö poistetaan {1}:
#XTXT: permanently
permanently=pysyvästi
#XTXT: {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDetail=Seuraava sisältö poistetaan {0}, eikä sitä voi palauttaa:
#XTXT: Hint Delete space confirmation text
deleteConfirmationHint=Vahvista poisto kirjoittamalla {0}.
#XTXT: Delete space confirmation error check spelling
deleteConfirmationErrorCheckSpelling=Tarkista oikeinkirjoitus ja yritä uudelleen.
#XTXT: All Spaces
allSpaces=Kaikki tilat
#XTXT: All data
allData=Kaikki tilaan sisältyvät objektit ja tiedot
#XTXT: All connections
allConnections=Kaikki tilassa määritetyt yhteydet
#XFLD: Space tile selection box tooltip
clickToSelect=Valitse napsauttamalla
#XTXT: All database users
allDatabaseUsers=Kaikki objektit ja tiedot, jotka sisältyvät mihin tahansa tilaan liittyvään Open SQL -kaavioon
#XFLD: remove members button tooltip
deleteUsers=Poista jäseniä
#XTXT: Space long description text
description=Kuvaus (enintään 4 000 merkkiä)
#XFLD: Add Members button tooltip
addUsers=Lisää jäseniä
#XFLD: Add Users button tooltip
addUsersTooltip=Lisää käyttäjät
#XFLD: Edit Users button tooltip
editUsersTooltip=Muokkaa käyttäjiä
#XFLD: Remove Users button tooltip
removeUsersTooltip=Poista käyttäjiä
#XFLD: Searchfield placeholder
filter=Haku
#XCOL: Users table-view column health
health=Terveys
#XCOL: Users table-view column access
access=Pääsy
#XFLD: No user found nodatatext
noDataText=Käyttäjiä ei löytynyt
#XTIT: Members dialog title
selectUserDialogTitle=Lisää jäseniä
#XTIT: User dialog title
addUserDialogTitle=Lisää käyttäjät
#XTIT: Delete connections confirmation title
deleteConnectionConfirmationHeaderText=Poista yhteydet
#XTIT: Delete single connection confirmation title
deleteConnectionConfirmationHeaderTextSingle=Poista yhteys
#XTXT: Delete connection delete confirmation text
deleteConnectionConfirmationBodyText=Haluatko varmasti poistaa valitut yhteydet? Ne poistetaan pysyvästi.
#XTIT: Remote source dialog title
selectRemoteConnectionDialogTitle=Valitse yhteydet
#XTIT: Share connection dialog title
connectionSharingDialogTitle=Jaa yhteys
#XTIT: Connections shared from other spaces table title
sharedConnectionsFromOtherSpaces=Jaetut yhteydet
#XFLD: Add remote source button tooltip
addRemoteConnections=Lisää yhteyksiä
#XFLD: Delete remote source button tooltip
deleteRemoteConnections=Poista yhteyksiä
#XFLD: Share remote source button tooltip
shareConnections=Jaa yhteydet
#XFLD: Tile-layout tooltip
tileLayout=Ruudun asettelu
#XFLD: Table-layout tooltip
tableLayout=Taulun asettelu
#XMSG: Success message after creating space
createSpaceSuccessMessage=Tila luotu
#XMSG: Success message after copying space
copySpaceSuccessMessage=Kopioidaan tila "{0}" tilaan "{1}"
#XMSG: Success message after deploying space
deploymentSuccessMessage=Tilan käyttöönotto on aloitettus
#XMSG: Success message after Apache Spark update started
sparkUpdateStartedSuccessMessage=Apache Spark -päivitys on aloitettu
#YMSE: Error while starting Apache Spark update
sparkUpdateStartedErrorMessage=Apache Spark -päivitys epäonnistui
#XMSG: Success message after updating space
updateSettingsSuccessMessage=Tilan lisätiedot päivitetty
#XMSG: Success message after unlocking space
unlockSpaceSuccessMessage=Tilan lukitus poistettu tilapäisesti
#XMSG: Success message after deleting a single space
deleteSpaceSuccessMessage=Tila poistettu
#XMSG: Success message after deleting multiple spaces
deleteSpacesSuccessMessage=Tilat poistettu
#XMSG: Success message after restoring a single space
restoreSpaceSuccessMessage=Tila palautettu
#XMSG: Success message after restoring multiple spaces
restoreSpacesSuccessMessage=Tilat palautettu
#YMSE: Error while updating settings
updateSettingsFailureMessage=Tilan asetuksia ei voitu päivittää.
#YMSE: Error while updating data lake option
dataLakeEnabledInOtherSpace=Tietojärvi on jo kohdistettu toiseen tilaan. Vain yksi tila voi hakea tietojärven kerrallaan.
#YMSE: Error while updating data lake option
virtualTablesExists=Et voi kumota tietojärven kohdistusta tästä tilasta, koska sidonnaisuuksia on yhä olemassa virtuaalisiin tauluihin*. Poista virtuaaliset taulut, jotta voit kumota tietojärven kohdistuksen tästä tilasta.
#YMSE: Error while unlocking space
unlockSpaceFailureMessage=Tilan lukitusta ei valitettavasti voitu poistaa.
#YMSE: Error while creating space
createSpaceError=Tilaa ei valitettavasti voitu luoda.
#YMSE: Error while creating duplicate space
createDuplicateSpaceError=Tila, jonka nimi on {0}, on jo olemassa.
#YMSE: Error while deleting a single space
deleteSpaceError=Tilaa ei voitu poistaa.
#YMSE: Error details while deleting a single space
deleteSpaceDetailedError=Tila “{0}” ei toimi enää asianmukaisesti. Yritä vielä poistaa se. Jos se ei siltikään toimi, pyydä pääkäyttäjää poistamaan se tai tekemään ilmoituksen.
#YMSE: Error while deleting a single space in files
deleteSpaceInFilesError=Tilan tietoja ei voitu poistaa Tiedostoissa.
#YMSE: Error while deleting a single space users
deleteSpaceUsersError=Käyttäjiä ei voitu poistaa.
#YMSE: Error while deleting a single space schema
deleteSpaceSchemasError=Kaavioita ei voitu poistaa.
#YMSE: Error while deleting a single space connections
deleteSpaceConnectionsError=Yhteyksiä ei voitu poistaa.
#YMSE: Error while deleting a single space data
deleteSpaceDataError=Tilan tietoja ei voitu poistaa.
#YMSE: Error while deleting multiple spaces
deleteSpacesError=Tiloja ei voitu poistaa.
#YMSE: Error while restoring a single space
restoreSpaceError=Tilaa ei voitu palauttaa.
#YMSE: Error while restoring multiple spaces
restoreSpacesError=Tiloja ei voitu palauttaa.
#YMSE: Error while creating users
createUsersError=Käyttäjiä ei voitu lisätä.
#YMSE: Error while removing users
removeUsersError=Käyttäjiä ei voitu poistaa.
#YMSE: Error while removing user
removeUserError=Käyttäjää ei voitu poistaa.
#YMSE: Error assigning roles to a user
roleAssignUnAssignError=Käyttäjää ei voitu lisätä valittuun laajuuteen sisältyvään rooliin. \n\n Et voi lisätä itseäsi laajuuteen sisältyvään rooliin. Voit pyytää pääkäyttäjää lisäämään sinut laajuuteen sisältyvään rooliin.
#YMSE: Error assigning user to the space
userAssignError=Käyttäjää ei voitu kohdistaa tilaan. \n\n Käyttäjä on jo kohdistettu tilojen sallittuun maksimimäärään (100) laajuuteen sisältyvissä rooleissa.
#YMSE: Error assigning users to the space
usersAssignError=Käyttäjiä ei voitu kohdistaa tilaan. \n\n Käyttäjä on jo kohdistettu tilojen sallittuun maksimimäärään (100) laajuuteen liittyvissä rooleissa.
#YMSE: Error while fetching users
fetchUserWithScopedRolesError=Käyttäjiä ei voitu hakea. Yritä myöhemmin uudelleen.
#YMSE: Error while fetching scoped roles
fetchScopedRolesError=Laajuuteen sisältyviä rooleja ei voitu hakea.
#YMSE: Error while fetching members
fetchUserError=Jäseniä ei voitu noutaa. Yritä myöhemmin uudelleen.
#YMSE: Error while loading run-time database
loadRuntimeError=Tietoja ei voitu ladata ajonaikaisesta tietokannasta.
#YMSE: Error while loading spaces
loadSpacesError=Tiloja haettaessa tapahtui valitettavasti virhe.
#YMSE: Error while loading haas resources
loadStorageError=Muistitietoja haettaessa tapahtui valitettavasti virhe.
#YMSE: Error no data could be loaded
loadDataError=Tietoja haettaessa tapahtui valitettavasti virhe.
#XFLD: Click to refresh storage data
clickToRefresh=Voit yrittää uudelleen napsauttamalla tästä.
#XTIT: Delete space popup title
deleteSpacePopupTitle=Poista tila
#XCOL: Spaces table-view column name
name=Nimi
#XCOL: Spaces table-view deployment status
deploymentStatus=Käyttöönoton tila
#XFLD: Disk label in space details
storageLabel=Levy (GB)
#XFLD: In-Memory label in space details
ramLabel=Muisti (GB)
#XFLD: Memory label on space card
memory=Tallennusmuisti
#XFLD: Storage Assignment label in space details
storrageAssignmentLabel=Tilamuisti
#XFLD: Storage Type label in space details
storageTypeLabel=Muistityyppi
#XFLD: Enable Space Quota
enableSpaceQuota=Aktivoi tilan kiintiö
#XFLD: No Space Quota
noSpaceQuota=Ei tilan kiintiötä
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
diskAndInMemoryText=SAP HANA -tietokanta (levy- ja in-memory-muisti)
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
dataLakeFilesText=SAP HANA -tietojärven tiedostot
#XFLD: Available scoped roles label
availableRoles=Käytettävissä olevat laajuuteen sisältyvät roolit
#XFLD: Selected scoped roles label
selectedRoles=Valitut laajuuteen sisältyvät roolit
#XCOL: Spaces table-view column models
models=Mallit
#XCOL: Spaces table-view column users
users=Käyttäjät
#XCOL: Spaces table-view column connections
connections=Yhteydet
#XFLD: Section header overview in space detail
overview=Yleisnäkymä
#XFLD: Section header Apache Spark in space detail
sparkManagementSection=Apache Spark
#XFLD: Section header Apache Spark in space detail
sparkManagementSubSection=Sovellukset
#XFLD: Section header Apache Spark in space detail
taskAssignmentSubSection=Tehtävän kohdistus
#XFLD: vCPU label in Apache Spark section
vCPULabel=Virtuaaliset CPU:t
#XFLD: Memory label in Apache Spark section
memoryLabel=Muisti (GB)
#XFLD: space configuration label in Apache Spark section
spaceConfigurationLabel=Tilan konfiguraatio
#XFLD: Space Source label
sparkApplicationLabel=Sovellus
#XFLD: Cluster Size label
clusterSizeLabel=Ryväskoko
#XFLD: Driver label
driverLabel=Ajuri
#XFLD: Executor label
executorLabel=Suorittaja
#XFLD: max label
maxLabel=Maksimikäyttö
#XFLD: TrF Default label
trFDefaultLabel=Oletusmuuntovirta
#XFLD: Merge Default label
mergeDefaultLabel=Oletusyhdistäminen
#XFLD: Optimize Default label
optimizeDefaultLabel=Oletusoptimointi
#XFLD: Deployment Default label
deploymentDefaultLabel=Paikallisen taulun (tiedoston) käyttöönotto
#XFLD: max spark source label
driverExecutorMaxUsedSparkSource={0} CPU / {1} GB
#XFLD: Object type label
taskObjectTypeLabel=Objektityyppi
#XFLD: Task activity label
taskActivityLabel=Toiminto
#XFLD: Task Application ID label
taskApplicationIDLabel=Oletussovellus
#XFLD: Section header in space detail
generalSettings=Yleiset asetukset
#XMSG: Message strip message when a space is locked
spaceLockedMessageStrip=Järjestelmä on lukinnut tämän tilan.
#XMSG: Message strip message deploy warning
deployWarningMessageStrip=Tämän osan muutokset otetaan käyttöön välittömästi.
#XMSG: Message strip message modify space configuration warning
modifySpaceConfigurationMessageStrip=Huomaa, että arvojen muuttaminen voi rikkoa järjestelmän prosesseja.
#XFLD: Button text to unlock the space again
unlockSpace=Poista tilan lukitus
#XFLD: Info text for audit log formatted message
auditLogText=Salli tarkastuslokien tallentaa luku- tai muutostoimia (tarkastuskäytännöt). Pääkäyttäjät voivat silloin analysoida, kuka on suorittanut mitäkin toimia tiettyyn aikaan.
#XMSG: Message strip  audit warning
auditLogWarningMessageStrip=Tarkastuslokit voivat viedä suuren määrän levymuistia vuokralaisessa. Jos otat käyttöön tarkastuskäytännön (luku- tai muutostoimet), sinun on valvottava levymuistin käyttöä säännöllisesti (järjestelmänvalvonnan Levymuistia käytetty -kortin kautta), jotta levymuisti ei lopu, mikä voi johtaa palvelukatkoksiin. Jos poistat tarkastuskäytännön käytöstä, kaikki sen tarkastuslokimerkinnät poistetaan. Jos haluat säilyttää tarkastuslokimerkinnät, harkitse niiden vientiä ennen kuin poistat tarkastuskäytännön käytöstä.
#XFLD: Message strip link text to open in app help to guide user what to do to unlock his space permanently
unlockSpaceHelp=Näytä ohje
#XMSG: Message strip message when a space will be locked again
spaceTemporarilyUnlockedMessageStrip=Tämä tila ylittää tilamuistin ja lukitaan {0} {1}.
#XMSG: Unit for remaining time until space is locked again
hours=h
#XMSG: Unit for remaining time until space is locked again
minutes=min
#XFLD: Section header in space detail for auditing
detailsSectionAuditing=Tarkastus
#XFLD: Subsection header in space detail for auditing
auditing=Tilan tarkastusasetukset
#XFLD: Hot space tooltip
hotSpaceCountTooltip=Kriittiset tilat: muistia käytetty yli 90 %.
#XFLD: Green space tooltip
greenSpaceCountTooltip=Kunnossa olevat tilat: muistia käytetty 6-90 %.
#XFLD: Cold space tooltip
coldSpaceCountTooltip=Kylmät tilat: muistia käytetty 5 % tai vähemmän.
#XFLD: Critical space tooltip
criticalSpaceCountTooltip=Kriittiset tilat: muistia käytetty yli 90 %.
#XFLD: Green space tooltip
okSpaceCountTooltip=Kunnossa olevat tilat: muistia käytetty 6-90 %.
#XFLD: Locked space tooltip
lockedSpaceCountTooltipNew=Lukitut tilat. Lukittu riittämättömän muistin vuoksi.
#XFLD: Locked space tooltip
lockedSpaceCountTooltip=Lukitut tilat
#YMSE: Error while deleting remote source
deleteRemoteError=Yhteyksiä ei voitu poistaa.
#XFLD: Space ID input tooltip
spaceIDInfoTooltip=Tilan tunnusta ei voi muuttaa myöhemmin.\nSallitut merkit A - Z, 0 - 9 ja _
#YMSE: Error when space name is empty
spaceNameEmptyValidationError=Syötä tilan nimi.
#YMSE: Error when ECN Business name is empty
ecnNameEmptyValidationError=Syötä liiketoiminnallinen nimi.
#YMSE: Error when space id is empty
spaceIDInputEmptyValidationError=Syötä tilan tunnus.
#YMSE: Error when space id contains invalid characters
spaceIDInputInValidError=Virheellisiä merkkejä. Käytä vain merkkejä A - Z, 0 - 9 ja _ .
#YMSE: Error when space id exists while creation
spaceIDInputDuplicateError=Tilan tunnus jo olemassa.
#XFLD: Space searchfield placeholder
search=Haku
#XMSG: Success message after creating users
createUsersSuccess=Käyttäjät lisätty
#XMSG: Success message after creating user
createUserSuccess=Käyttäjä lisätty
#XMSG: Success message after updating users
updateUsersSuccess={0} käyttäjää päivitetty
#XMSG: Success message after updating user
updateUserSuccess=Käyttäjä päivitetty
#XMSG: Success message after removing users
removeUsersSuccess={0} käyttäjää poistettu
#XMSG: Success message after removing user
removeUserSuccess=Käyttäjä poistettu
#XFLD: Schema name
schemaName=Kaavion nimi
#XFLD: used of total
ofTemplate={0} / {1}
#XFLD: Assigned disk label in space overview
assignedStorageTemplate=Kohdistettu levy ({0} / {1})
#XFLD: Assigned Memory label in space overview
assignedRAMTemplate=Kohdistettu muisti ({0} / {1})
#XFLD: Storage ratio on space
accelearationRAM=Muistikiihdytys
#XFLD: No Storage Consumption
noStorageConsumptionText=Tallennuskiintiötä ei ole kohdistettu.
#XFLD: Used disk label in space overview
usedStorageTemplate=Tallennukseen käytetty levy ({0}/{1})
#XFLD: Used Memory label in space overview
usedRAMTemplate=Tallennukseen käytetty muisti ({0}/{1})
#XFLD: Used Disk tooltip
usedStorageTooltipTemplate={0}/{1} levyä käytetty tallennukseen
#XFLD: Used Memory tooltip
usedRAMTooltipTemplate={0} / {1} muistista käytetty
#XFLD: Assigned Disk tooltip
assignedStorageTooltipTemplate={0} / {1} levystä kohdistettu
#XFLD: Assigned Memory tooltip
assignedRAMTooltipTemplate={0} / {1} muistista kohdistettu
#XFLD: Space related used storage tooltip
spaceRelatedUsedStorageTooltipTemplate=Tilan tiedot: {0}
#XFLD: Not space related used storage tooltip
otherDataUsedStorageTooltipTemplate=Muut tiedot: {0}
#XFLD: Storage used exceeded popover and tooltip
storageUsedExceededText=Harkitse suunnitelman laajentamista tai ota yhteys SAP-tukeen.
#XCOL: Space table-view column used Disk
usedStorage=Tallennukseen käytetty levy
#XCOL: Space monitor column used Memory
usedRAM=Tallennukseen käytetty muisti
#XCOL: Space monitor column Schema
tableSchema=Skeema
#XCOL: Space monitor column Storage Type
tableStorageType=Muistityyppi
#XCOL: Space monitor column Table Type
tableType=Taulutyyppi
#XCOL: Space monitor column Record Count
tableRecordCount=Tietuemäärä
#XFLD: Assigned Disk
assignedStorage=Tallennuksen kohdistettu levy
#XFLD: Assigned Memory
assignedRAM=Tallennukseen kohdistettu muisti
#XCOL: Space table-view column storage utilization
tableStorageUtilization=Käytetty muisti
#XFLD: space status
spaceStatus=Tilan tila
#XFLD: space type
spaceType=Tilatyyppi
#XFLD: space type label for space with space type "SAP BW bridge" ("SAP BW bridge" is a product name, do not translate it)
abapbridgeSpaceType=SAP BW -silta
#XFLD: space type label for space with space type "Data Marketplace"
marketplaceSpaceType=Data Provider Product
#XMSG: Error message for when selected space cannot be deleted due to the space type
deleteTypedSpaceError=Et voi poistaa tilaa {0}, koska sen tilatyyppi on {1}.
#XMSG: Error message for when selected spaces cannot be deleted due to the space type
deleteTypedSpacesError=Et voi poistaa {0} valittua tilaa. Tiloja, joiden tilatyyppi on seuraava, ei voi poistaa: {1}.
#XFLD: space type label for space without space type
defaultSpaceType=SAP Datasphere
#XFLD: Tooltip for monitor space button
monitorSpace=Valvonta
#XFLD: Tooltip for edit space button
editSpace=Muokkaa tilaa
#XMSG: Deletion warning in messagebox
deleteConfirmation=Haluatko varmasti poistaa tämän tilan?
#XFLD: Tooltip for delete space button
deleteSpace=Poista tila
#XFLD: storage
storage=Levy tallennukseen
#XFLD: username
userName=Käyttäjänimi
#XFLD: port
port=Portti
#XFLD: hostname
hostName=Pääkoneen nimi
#XFLD: password
password=Salasana
#XBUT: Request new password button
requestPassword=Pyydä uusi salasana
#YEXP: Usage explanation in time data section
timeDataSectionHint=Luo omissa malleissa ja tietokuvauksissa käytettäviä aikatauluja ja ulottuvuuksia.
#YEXP: Usage explanation in Space schema section
spaceSchemaHint=Haluatko, että tilasi tietoja voivat käyttää muutkin työkalut tai sovellukset? Jos haluat, luo ainakin yksi käyttäjä, joka pääsee käsiksi tilan tietoihin, ja valitse, haluatko kaiken tulevan tiedon olevan käytettävissä oletusarvoisesti.
#XTIT: Create schema popup title
createSchemaDialogTitle=Luo Open SQL -kaavio
#XTIT: Generate Time Data dialog title
generateTimeDataDialogTitle=Luo aikatauluja ja ulottuvuuksia
#XTIT: Edit Time Data dialog title
editTimeDataDialogTitle=Muokkaa aikatauluja ja ulottuvuuksia
#XTIT: Time Data token title
timeDataTokenTitle=Aikatiedot
#XTIT: Time Data token title
timeDataUpdateViews=Päivitä aikatietonäkymät
#XFLD: Time Data token creation in progress label
timeDataCreationInProgress=Luonti käynnissä...
#XFLD: Time Data token creation error label
timeDataCreationError=Luonti epäonnistui. Yritä uudelleen.
#XTIT: Time Data dialog time settings title
timeDataDialogTimeSettingsTitle=Aikataulun asetukset
#XTIT: Time Data dialog time data table title
timeDataDialogTranslationTablesTitle=Muuntotaulut
#XTIT: Time Data dialog dimensions title
timeDataDialogTimeViewsTitle=Aikaulottuvuudet
#XFLD: Time Data dialog time range label
timeRangeHint=Määritä aikaväli.
#XFLD: Time Data dialog time data table label
timeDataHint=Anna taululle nimi.
#XFLD: Time Data dialog time data dimensions label
timeDimensionsHint=Anna ulottuvuuksille nimi.
#XFLD: Time Data Time range description label
timerangeLabel=Aikaväli
#XFLD: Time Data dialog from year label
fromYearLabel=Vuodesta
#XFLD: Time Data dialog to year label
toYearLabel=Vuoteen
#XFLD: Time Data dialog calendar type label
calendarTypeLabel=Kalenterityyppi
#XFLD: Time Data dialog granularity label
granularityLabel=Tarkkuusaste
#XFLD: Time Data dialog technical name label
technicalNameLabel=Tekninen nimi
#XFLD: Time Data dialog quarters text table lable
quarterTextsTableLabel=Muuntotaulu vuosineljänneksiä varten
#XFLD: Time Data dialog months text table lable
monthsTextsTableLabel=Muuntotaulu kuukausia varten
#XFLD: Time Data dialog days text table lable
daysTextsTableLabel=Muuntotaulu päiviä varten
#XFLD: Time Data dialog year label
yearLabel=Vuosiulottuvuus
#XFLD: Time Data dialog quarter label
quarterLabel=Vuosineljännesulottuvuus
#XFLD: Time Data dialog month label
monthLabel=Kuukausiulottuvuus
#XFLD: Time Data dialog day label
dayLabel=Päiväulottuvuus
#XFLD: Time Data dialog gregorian calendar type label
gregorian=Gregoriaaninen
#XFLD: Time Data dialog time granularity day label
day=Päivä
#XFLD: Time Data dialog business name maxlength reached
timeRangeValueStateTextMaxLengthBusinessName=1000 merkin maksimipituus saavutettu.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextMaxRangeExceeded=Maksimiaikaväli on 150 vuotta.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeLower=“Vuodesta” oltava pienempi kuin “Vuoteen”
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeHigher="Vuodesta" oltava 1900 tai myöhempi.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeHigher=“Vuoteen” oltava suurempi kuin “Vuodesta”
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeLower=“Vuoteen” oltava aiempi kuin nykyinen vuosi plus 100
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningFromYear=Arvon "Vuodesta" suurentaminen voi johtaa tietojen katoamiseen
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningToYear=Arvon "Vuoteen" pienentäminen voi johtaa tietojen katoamiseen
#XMSG: Time Data creation validation error message
timeDataValidationError=Osa kentistä näyttää olevan virheellisiä. Tarkista tarvittavat kentät ja jatka sen jälkeen.
#XMSG: Time Data deletion popup text
deleteTimeDataConfirmation=Haluatko varmasti poistaa tiedot?
#XMSG: Time Data creation success message
createTimeDataSuccess=Aikatiedot luotu
#XMSG: Time Data update success message
updateTimeDataSuccess=Aikatiedot päivitetty
#XMSG: Time Data delete success message
deleteTimeDataSuccess=Aikatiedot poistettu
#XMSG: Time Data creation error message
createTimeDataError=Aikatietoja luotaessa tapahtui virhe.
#XMSG: Time Data update error message
updateTimeDataError=Aikatietoja päivitettäessä tapahtui virhe.
#XMSG: Time Data creation error message
deleteTimeDataError=Aikatietoja poistettaessa tapahtui virhe.
#XMSG: Error message when time data could not be retrieved
retrievingTimeDataError=Aikatietoja ei voitu ladata.
#XTIT: Time Data deletion popup title
deleteTimeDataConfirmationTitle=Varoitus
#XTXT: Time Data deletion dependency popup text
deleteTimeDataDependencyText=Aikatietoja ei voitu poistaa, koska niitä käytetään muissa malleissa.
#XTXT: Time Data deletion dependency popup text singular
deleteTimeDataDependencyTextSingular=Aikatietoja ei voitu poistaa, koska niitä käytetään toisessa mallissa.
#XMSG: Validation error when required time data form field is empty
timeDataRequiredField=Tämä kenttä on pakollinen, eikä se voi olla tyhjä.
#XFLD: Tooltip for Open in Database Explorer button
openInDBX=Avaa Database Explorerissa
#YMSE: Dimension Year
dimensionYearView=Ulottuvuus "Vuosi"
#YMSE: Dimension Year
dimensionQuarterView=Ulottuvuus "Vuosineljännes"
#YMSE: Dimension Year
dimensionMonthView=Ulottuvuus "Kuukausi"
#YMSE: Dimension Year
dimensionDayView=Ulottuvuus "Päivä"
#XFLD: Time Data deletion object title
timeDataUsedIn=(käytetään {0} mallissa)
#XFLD: Time Data deletion object title singular
timeDataUsedInSingular=(käytetään 1 mallissa)
#XFLD: Time Data deletion table column provider
provider=Toimittaja
#XTIT: Time Data dependency dialog title
timeDataDependencyDialogTitle=Sidonnaisuudet
#XTIT: Create schema popup title
createSpaceSchemaUserDialogTitle=Luo tilakaavion käyttäjä
#XFLD: Create schema button
createSchemaButton=Luo Open SQL -kaavio
#XFLD: Generate TimeData button
generateTimeDataButton=Luo aikatauluja ja ulottuvuuksia
#XFLD: Show dependencies button
showDependenciesButton=Näytä sidonnaisuudet
#XFLD: tooltip for disabled buttons when user is not member of space
mustBeMemberOfSpaceForOperation=Vain tilan jäsenet voivat suorittaa tämän operaation.
#XFLD: Create space schema user button
createSpaceSchemaUserButton=Luo tilakaavion käyttäjä
#YMSE: API Schema users load error
loadSchemaUsersError=Käyttäjäluetteloa ei voitu ladata.
#XTIT: API Schema-config popup title
schemaUserDetailsDialogTitle=Tilakaavion käyttäjän lisätiedot
#XMSG: API Schema user deletion popup text
deleteSpaceSchemaUserConfirmation=Haluatko varmasti poistaa valitun käyttäjän?
#XMSG: API Schema user deletion confirmation text
userDeleteSuccess=Käyttäjä poistettu.
#YMSE: API Schema user deletion error
userDeleteError=Käyttäjää ei voitu poistaa.
#XFLD: User deleted
userDeleted=Käyttäjä on poistettu.
#XTIT: Remove user popup title
removeUserConfirmationTitle=Varoitus
#XMSG: Remove user popup text
removeUserConfirmation=Haluatko todella poistaa käyttäjän? Käyttäjä ja käyttäjän kohdistetut laajuuteen sisältyvät roolit poistetaan tilasta.
#XMSG: Remove users popup text
removeUsersConfirmation=Haluatko todella poistaa käyttäjät? Käyttäjät ja käyttäjien kohdistetut laajuuteen sisältyvät roolit poistetaan tilasta.
#XBUT: Confirmation button for remove user dialog
removeUserConfirmationBtn=Poista
#YMSE: No data text for available roles
noDataAvailableRoles=Tilaa ei ole lisätty laajuuteen sisältyviin rooleihin. \n Jotta tilaan voidaan lisätä käyttäjiä, se pitää ensin lisätä yhteen tai useampaan laajuuteen sisältyvään rooliin.
#YMSE: No data text for selected roles
noDataSelectedRoles=Ei valittuja laajuuteen sisältyviä rooleja
#XTIT: Schema-config popup title
schemaDetailsDialogTitle=Open SQL -kaavion konfiguraation lisätiedot
#XFLD: Label for Read Audit Log
auditLogRead=Aktivoi lukuoperaatioiden tarkastusloki
#XFLD: Label for Change Audit Log
auditLogChange=Aktivoi muutosoperaatioiden tarkastusloki
#XFLD: Label Audit Log Retention
auditLogRetention=Säilytä lokit
#XFLD: Label Audit Log Retention Unit
retentionUnit=pv
#YMSE: Error when log retention time is set incorrectly
incorrectRetentionTime=Syötä kokonaisluku väliltä {0}-{1}
#XFLD: Label for Cross Space Schema Access
spaceSchemaAccess=Käytä tilakaavion tietoja
#XTIT: Confirm deactivation of space schema consumption dialog title
disableSpaceSchemaConsumptionDialogTitle=Lopeta tilakaavion tietojen käyttö
#XTXT: Confirm deactivation of space schema consumption dialog text
disableSpaceSchemaConsumptionDialogText=Tämä Open SQL -kaavio saattaa käyttää tilakaavion tietoja. Jos lopetat käytön, tilakaavion tietoihin perustuvat mallit eivät kenties enää toimi.
#XBUT: Confirmation button for the stop consuming space schema data dialog
stopConsuming=Lopeta käyttö
#XTXT: Tooltip for data lake icon
dataLakeIconTooltip=Tätä tilaa käytetään tietojärveen pääsemiseen
#XFLD: Tooltip for data lake table entry
dataLakeTableEntryTooltip=Tietojärvi aktivoitu
#XFLD: used In-Memory linit reached tag
usedRamLimitReachedLabel=Muistin raja saavutettu
#XFLD: Space storage Limit reached tag
storageLimitReachedLabel=Muistiraja saavutettu
#XFLD: Minimal Storage Limit Reached tag
minStorageLimitReachedLabel=Muistin minimiraja saavutettu
#XFLD: Space ram tag
ramLimitReachedLabel=Muistin raja saavutettu
#XFLD: Minimal In-Memory Limit Reached tag
minRamLimitReachedLabel=Muistin minimiraja saavutettu
#XFLD: Popover text after click on space storage limit warning
storageLimitReachedText=Olet saavuttanut tilan kohdistetun muistin rajan {0}. Kohdista tilaan lisää muistia.
#XFLD: System storage tag
systemStorageLimitReachedLabel=Järjestelmämuistin raja saavutettu
#XMSG: Popover text after click on storage limit warning
systemStorageLimitReachedText=Olet saavuttanut järjestelmämuistin rajan {0}. Tilaan ei voi nyt kohdistaa lisää muistia.
#XMSG: Schema deletion warning
deleteSchemaConfirmation=Kun tämä Open SQL -kaavio poistetaan, myös kaikki kaaviossa tallennetut objektit ja ylläpidetyt yhteenkuuluvuudet poistetaan pysyvästi. Jatketaanko?
#XMSG: Success message after deleting a schema
schemaDeleteSuccess=Kaavio poistettu
#YMSE: Error while deleting schema.
schemaDeleteError=Kaaviota ei voitu poistaa.
#XMSG: Success message after update a schema
schemaUpdateSuccess=Kaavio päivitetty
#YMSE: Error while updating schema.
schemaUpdateError=Kaaviota ei voitu päivittää.
#XMSG: Hint when no password is shown in the dialog.
newPasswordHint=Olemme antaneet salasanan tätä kaaviota varten. Jos olet unohtanut tai kadottanut salasanan, voit pyytää uuden. Muista kopioida tai tallentaa uusi salasana.
#XMSG: Hint when showing the password in the dialog.
savePasswordHint=Kopioi salasana. Tarvitset sitä yhteyden muodostamiseen tähän kaavioon. Jos olet unohtanut salasanan, voit avata tämän dialogin ja palauttaa sen.
#XFLD,50: Hint on entering schema name in creation dialog
schemaNameHint=Tätä nimeä ei voi muuttaa kaavion luonnin jälkeen.
#XFLD: Open SQL Schemas section sub headline
schemasSQL=Open SQL
#XFLD: Space schema section sub headline
schemasSpace=Tila
#XFLD: HDI Container section header
HDIContainers=HDI-säilöt
#XTXT: Add HDI Containers button tooltip
addHDIContainers=Lisää HDI-säilöt
#XTXT: Remove HDI Containers button tooltip
removeHDIContainers=Poista HDI-säilöt
#XFLD: Enable HDI Containers feature button
enableHDIContainersButton=Aktivoi käyttö
#YMSE: No data text for HDI Containers table
noDataHDIContainers=HDI-säilöjä ei ole lisätty.
#YMSE: No data text for Timedata section
noDataTimedata=Aikatauluja ja ulottuvuuksia ei ole luotu.
#YMSE: No data text during runtime not available for Timedata section
runtimeNotAvailableNoDataTimedata=Aikatauluja ja ulottuvuuksia ei voi ladata, koska ajonaikainen tietokanta ei ole käytettävissä.
#YMSE: No data text during runtime not available for HDI section
runtimeNotAvailableNoDataHDI=HDI-säilöjä ei voi ladata, koska ajonaikainen tietokanta ei ole käytettävissä.
#YMSE: Error while fetching HDI Containers
fetchHDIContainersError=HDI-säilöjä ei voitu saada. Yritä myöhemmin uudelleen.
#XFLD Table column header for HDI Container names
HDIContainerName=HDI-säilön nimi
#XFLD: Enable HDI Containers feature dialog title
enableHDIContainersDialogTitle=Aktivoi käyttö
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText1New=Voit aktivoida SAP SQL Data Warehousingin SAP Datasphere -vuokralaisessasi, jos haluat vaihtaa tietoja HDI-säilöjesi ja SAP Datasphere -tilojesi välillä siirtämättä tietoja.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText2=Avaa tätä varten asiakasilmoitus napsauttamalla alla olevaa painiketta.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText3New=Kun ilmoituksesi on käsitelty, sinun on luotava yksi tai useampi uusi HDI-säilö ajonaikaisessa SAP Datasphere -tietokannassa. Sen jälkeen HDI-säilöt-osion Aktivoi käyttö -painike korvataan pluspainikkeella (+) kaikkien SAP Datasphere -tilojesi osalta, ja voit lisätä säilösi tilaan.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText41=Tarvitsetko lisätietoja? Siirry kohteeseen %%0. Lisätietoja ilmoitukseen sisällytettävistä tiedoista on kohdassa %%1.
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkText=SAP-ohje
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkTextSapNote3057059=SAP-ohje 3057059
#XBUT: Open Ticket Button Text
openTicket=Avaa ilmoitus
#XBUT: Add Button Text
add=Lisää
#XBUT: Next Button Text
next=Seuraava
#XBUT: Edit Button Text
editUsers=Muokkaa
#XBUT: create user Button Text
createUser=Luo
#XBUT: Update user Button Text
updateUser=Valitse
#XTIT: Add HDI Containers Dialog Title
addHDIContainersDialogTitle=Lisää kohdistamattomat HDI-säilöt
#XFLD: No data text for add HDI container dialog
addHDIContainersDialogNoDataText=Emme löytäneet kohdistamattomia säilöjä. \n Etsimäsi säilö voi olla jo kohdistettu johonkin tilaan.
#YMSE: Error while loading assigned HDI Containers
loadAssignedHDIContainersError=Kohdistettuja HDI-säilöjä ei voitu ladata.
#YMSE: Error while loading assigned HDI Containers
loadUnassignedHDIContainersError=HDI-säilöjä ei voitu ladata.
#XMSG: Success message
succeededToAddHDIContainer=HDI-säilö lisätty
#XMSG: Success message
succeededToAddHDIContainerPlural=HDI-säilöt lisätty
#XMSG: Success message
succeededToDeleteHDIContainer=HDI-säilö poistettu
#XMSG: Success message
succeededToDeleteHDIContainerPlural=HDI-säilöt poistettu
#XFLD: Time data section sub headline
timeDataSection=Aikataulut ja ulottuvuudet
#XFLD: Schemas list entry read audit log enabled indicator
readIndicator=Lue
#XFLD: Schemas list entry change audit log enabled indicator
changeIndicator=Muuta
#XFLD: Remote sources section sub headline
allconnections=Yhteyden kohdistus
#XFLD: Remote sources section sub headline
localconnections=Paikalliset yhteydet
#XFLD: Remote sources section sub headline for open Connectors
openConnectors=SAP:n avoimet liittimet
#XFLD: User section sub headline
memberassignment=Jäsenen kohdistus
#XFLD: User assignment section sub headline
userAssignment=Käyttäjäkohdistus
#XFLD: User section Access dropdown Member
member=Jäsen
#XFLD: User assignment section column name
user=Käyttäjänimi
#XTXT: Selected role count
selectedRoleToolbarText=Valittu: {0}
#XTIT: Space detail section connections title
detailsSectionConnections=Yhteydet
#XTIT: Space detail section data access title
detailsSectionDataAccess=Kaavion käyttö
#XTIT: Space detail section time data title
detailsSectionGenerateData=Aikatiedot
#XTIT: Space detail section members title
detailsSectionUsers=Jäsenet
#XTIT: Space detail section Users title
detailsSectionUsersTitle=Käyttäjät
#XTIT: Out of Storage
insufficientStoragePopoverTitle=Muisti loppui
#XTIT: Storage distribution
storageDistributionPopoverTitle=Levymuistia käytetty
#XTXT: Out of Storage popover text
insufficientStorageText=Jos haluat luoda uuden tilan, pienennä toiseen tilaan kohdistettua muistia tai poista tila, jota et tarvitse enää. Voit lisätä järjestelmämuistin kokonaismäärää kutsumalla suunnitelman hallinnan.
#XMSG: Space id length warning
spaceIdLengthWarning={0} merkin maksimi ylitetty.
#XMSG: Space name length warning
spaceNameLengthWarning={0} merkin maksimi ylitetty.
#XMSG: Space id reserved prefix warning
spaceIdnWarningReservedPrefix=Älä käytä etuliitettä {0} mahdollisten ristiriitojen välttämiseksi.
#YMSE: Error while loading open SQL schemas
loadLocalSchemasError=Open SQL -kaavioita ei voitu ladata.
#YMSE: Error while creating open SQL schema
createLocalSchemaError=Open SQL -kaaviota ei voitu luoda.
#YMSE: Error while loading all remote connections
loadRemoteConnectionsError=Kaikkia etäyhteyksiä ei voitu ladata.
#YMSE: Error while loading space details
loadSpaceDetailsError=Tilan lisätietoja ei voitu ladata.
#YMSE: Error while deploying space details
deploySpaceDetailsError=Tilaa ei voitu ottaa käyttöön.
#YMSE: Error while copying space details
copySpaceDetailsError=Tilaa ei voitu kopioida.
#YMSE: Error while loading storage data
loadStorageDataError=Säilytystietoja ei voitu ladata.
#YMSE: Error while loading all users
loadAllUsersError=Kaikki käyttäjiä ei voitu ladata.
#YMSE: Failed to reset password
resetPasswordError=Salasanaa ei voitu palauttaa.
#XMSG: Success message after updating space
resetPasswordSuccessMessage=Uusi salasana asetettu kaaviolle
#YMSE: DP Agent-name too long
DBAgentNameError=Tietojen toimittajan nimi on liian pitkä.
#YMSE: Schema-name not valid.
schemaNameError=Kaavion nimi on virheellinen.
#YMSE: User name not valid.
UserNameError=Käyttäjänimi on virheellinen.
#XTIT: Consumption by Storage Type
consumptionStorageTypeText=Käyttö muistityypin mukaan
#XTIT: Consumption by Schema
consumptionSchemaText=Käyttö kaavion mukaan
#XTIT: Overall Consumption by schema
overallConsumptionSchemaText=Taulun käyttö yhteensä kaavion mukaan
#XTIT: Overall Consumption by Table Type
overallConsumptionTableTypeText=Käyttö yhteensä kaaviotyypin mukaan
#XTIT: Tables
tableDetailsText=Taulun lisätiedot
#XTIT: Table Storage Consumption
tableStorageConsumptionText=Taulun muistin käyttö
#XFLD: Table Type label
tableTypeLabel=Taulutyyppi
#XFLD: Schema label
schemaLabel=Skeema
#XFLD: reset table tooltip
resetTable=Palauta taulu
#XFLD: In-Memory label in space monitor
inMemoryLabel=Muisti
#XFLD: Disk label in space monitor
diskLabel=Levy
#XFLD: Yes
yesLabel=Kyllä
#XFLD: No
noLabel=Ei
#XFLD: switch label: Want the data in this space to be consumable by default?
allowConsumptionByDefault=Haluatko, että tämän tilan tiedot ovat käytettävissä oletusarvoisesti?
#XFLD: Business Name
businessNameLabel=Liiketoiminnallinen nimi
#XFLD: Refresh
refresh=Päivitä
#XMSG: No filter results title
noFilterResultsTitle=Näyttää siltä, että suodatinasetukset eivät näytä mitään tietoja.
#XMSG: No filter results message
noFilterResultsMsg=Yritä tarkentaa suodatinasetuksia, ja jos et siltikään näe tietoja, luo tauluja tietojen muodostimessa. Kun ne käyttävät muistia, voit valvoa niitä täällä.
#XMSG: Space monitoring Hana state red title
spaceMonitorHanaRedTitle=Ajonaikainen tietokanta ei ole käytettävissä.
#XMSG: Space monitoring Hana state red message
spaceMonitorHanaRedMsg=Koska ajonaikainen tietokanta ei ole käytettävissä, tietyt ominaisuudet ovat pois käytöstä emmekä voi näyttää tietoja tällä sivulla.
#YMSE: Error while creating space schema user
createSpaceSchemaUserError=Tilakaavion käyttäjää ei voitu luoda.
#YMSE: Error User name already exists
userAlreadyExistsError=Käyttäjänimi on jo olemassa.
#YMSE: Error Authentication failed
authenticationFailedError=Todentaminen epäonnistui.
#XMSG: Message strip message when a schema user is locked
userLockedMessageStrip=Käyttäjä on lukittu liian monen epäonnistuneen kirjautumisyrityksen vuoksi. Pyydä uusi salasana käyttäjän lukituksen poistoa varten.
#XMSG: Success message after reset and unlock schema user
resetUnlockPasswordSuccessMessage=Uusi salasana asetettu ja käyttäjän lukitus poistettu
#XMSG: user is locked message
userLockedMessage=Käyttäjä on lukittu.
#XCOL: Users table-view column Role
spaceRole=Rooli
#XCOL: Users table-view column Scoped Role
spaceScopedRole=Laajuuteen sisältyvä rooli
#XCOL: Users table-view column Space Admin
spaceAdmin=Tilan pääkäyttäjä
#XFLD: User section dropdown value Viewer
viewer=Katseluohjelma
#XFLD: User section dropdown value Modeler
modeler=Mallintaja
#XFLD: User section dropdown value Data Integrator
dataIntegrator=Data Integrator
#XFLD: User section dropdown value Space Administrator
spaceAdministrator=Tilan pääkäyttäjä
#XMSG: Success message -  Space Role updated successfully.
updateUserSpaceRoleSuccess=Tilan rooli päivitetty
#XMSG: Error message - Failed to update Space Role.
updateUserSpaceRoleError=Tilan roolin päivitys epäonnistui.
#XFLD:
databaseUserNameSuffix=Tietokantakäyttäjän nimen jälkiliite
#XTXT: Space Schema password text
spaceSchemaPasswordText=Kopioi salasanasi yhteyden muodostamiseksi tähän kaavioon. Jos olet unohtanut salasanasi, voit aina pyytää uuden salasanan.
#XFLD: Cloud Platform label in space schema dialog
cloudPlatformLabel=Cloud Platform
#XTXT: Cloud Platform text in space schema dialog
cloudPlatformText=Voit määrittää pääsyn tämän käyttäjän kautta aktivoimalla kulutuksen ja kopioimalla valtuustiedot. Jos voit vain kopioida valtuustiedot ilman salasanaa, varmista, että lisäät salasanan myöhemmin.
#XFLD: enable consumption in CloudPlatform label in space schema dialog
enableConsumptionInCloudPlatformLabel=Aktivoi kulutus Cloud Platformissa
#XFLD: Credentials for User-Provided Service label in space schema dialog
cloudPlatformCredentialsLabel=Käyttäjän oman palvelun tunnistetiedot:
#XFLD: Credentials for User-Provided Service (Without Password) label in space schema dialog
cloudPlatformCredentialsWithoutPasswordLabel=Käyttäjän oman palvelun tunnistetiedot (ilman salasanaa):
#XFLD: Copy Credentials label in space schema dialog
copyCredentialsLabel=Kopioi valtuustiedot ilman salasanaa
#XFLD: Copy Credentials label in space schema dialog
copyFullCredentialsLabel=Kopioi valtuustiedot kokonaan
#XFLD: Copy Password label in space schema dialog
copyPasswordLabel=Kopioi salasana
#XMSG: Credentials copied to clipboard
credentialsCopiedMessage=Valtuustiedot kopioitu leikepöydälle
#XMSG: Password copied to clipboard
passwordCopiedMessage=Salasana kopioitu leikepöydälle
#XMSG: create database user dialog title
createDatabaseUserDialogTitle=Luo tietokantakäyttäjä
#XMSG: Database Users section title
databaseUsers=Tietokantakäyttäjät
#XMSG: Database User Details dialog title
databaseUserDetailsDialogTitle=Tietokantakäyttäjän lisätiedot
#XFLD: database user read audit log
databaseUserAuditLogRead=Aktivoi tarkastuslokit lukuoperaatioita varten ja säilytä lokit kohteelle
#XFLD: database user change audit log
databaseUserAuditLogChange=Aktivoi tarkastuslokit muutosoperaatioita varten ja säilytä lokit kohteelle
#XMSG: Cloud Platform Access
cloudPlatformAccess=Cloud Platform -käyttöoikeus
#XFLD: Enable Consumption in Cloud Platform hint
enableConsumptionCloudPlatformHint=Määritä pääsy HANA Deployment Infrastructure (HDI) -säilöön tämän tietokantakäyttäjän kautta. Yhteyden muodostamiseksi HDI-säilöön täytyy SQL-mallinnuksen olla otettu käyttöön
#XFLD: Enable HDI Consumption
enableConsumptionCloudPlatform=Aktivoi HDI-kulutus
#XFLD: Enable Consumption hint
enableConsumptionHint=Haluatko, että muut työkalut tai sovellukset voivat kuluttaa tilasi tietoja?
#XFLD: Enable Consumption
enableConsumption=Aktivoi SQL-kulutus
#XFLD: Enable Modeling
enableModeling=Aktivoi SQL-mallinnus
#XMSG: Privileges for Data Modeling
privilegesModeling=Tietojen vastaanotto
#XMSG: Privileges for Data Consumption
privilegesConsumption=Ulkoisten työkalujen tietojen kulutus
#XFLD: SQL Modeling
sqlModeling=SQL-mallinnus
#XFLD: SQL Consumption
sqlConsumption=SQL-kulutus
#XFLD: enabled
enabled=Aktivoitu
#XFLD: disabled
disabled=Aktivointi poistettu
#XFLD: Edit Privileges
editPrivileges=Muokkaa käyttöoikeuksia
#XFLD: Open Database Explorer
openDBX=Avaa Database Explorer
#XFLD: create database user hint
databaseCreateHint=Huomaa, että käyttäjänimeä ei voi muuttaa uudelleen tallennuksen jälkeen.
#XFLD: Internal Schema Name
internalSchemaName=Sisäinen kaavion nimi
#YMSE: Failed to load database users
loadDatabaseUserError=Tietokantakäyttäjien lataus epäonnistui
#YMSE: Failed to delete database user
deleteDatabaseUsersError=Tietokantakäyttäjien poistaminen epäonnistui
#XMSG: Success message after deleting database user
deleteDatabaseUserSuccess=Tietokantakäyttäjä poistettu.
#XMSG: Success message after deleting database users
deleteDatabaseUsersSuccess=Tietokantakäyttäjät poistettu
#XMSG: Success message after creating database user
createDatabaseUserSuccessMessage=Tietokantakäyttäjä luotu
#YMSE: Failed to create database user
createDatabaseUserError=Tietokantakäyttäjän luonti epäonnistui
#XMSG: Success message after updating database user
updateDatabaseUserSuccessMessage=Tietokantakäyttäjä päivitetty
#YMSE: Failed to update database user
updateDatabaseUserError=Tietokantakäyttäjän päivitys epäonnistui
#XFLD: HDI Consumption
hdiConsumption=HDI-kulutus
#XTIT: Space detail section Database Access title
detailsSectionDatabaseAccess=Tietokantahaku
#XTXT: Database users consumption hint
databaseUsersConsumptionHint=Tee tilasi tiedot käytettäviksi oletusarvoisesti. Mallit muodostimissa sallivat automaattisesti tietojen olevan käytettävissä.
#XFLD: Default Consumption of Space Data:
defaultConsumptionOfSpaceData=Tilan tietojen oletuskulutus:
#XFLD: Database User Name
databaseUserName=Tietokantakäyttäjän nimi
#XMSG: Database User creation validation error message
databaseUserValidationError=Osa kentistä näyttää olevan virheellisiä. Tarkista tarvittavat kentät ja jatka sen jälkeen.
#XMSG: SQL modeling can’t be enabled since this user has been migrated.
migratedDatabaseUserHint=Tietojen vastaanottoa ei voida aktivoida, koska tämän käyttäjä on siirretty.
#XBUT: Remove Button Text
remove=Poista
#XBUT: Remove Spaces Button Text
removeSpaces=Poista tilat
#XBUT: Remove Objects Button Text
removeObjects=Poista objektit
#XMSG: No members have been added yet.
noMembersAssigned=Jäseniä ei ole vielä lisätty.
#XMSG: No users have been added yet.
noUsersAssigned=Käyttäjiä ei ole vielä lisätty.
#XMSG: No database users have been created or your filter is not showing any data.
noDatabaseUsers=Tietokantakäyttäjiä ei ole luotu tai suodattimesi ei näytä tietoja.
#XMSG: Please enter a user name.
noDatabaseUsername=Syötä käyttäjänimi.
#XMSG: The user name is too long. Please use a shorter one.
databaseUsernameTooLong=Käyttäjänimi on liian pitkä. Käytä lyhyempää nimeä.
#XMSG: No privileges have been enabled and this database user will have limited functionality. Do you still want to continue?
noPrivilegesConfirmationMessage=Käyttöoikeuksia ei ole aktivoitu, ja tällä tietokantakäyttäjällä on rajoitetut toiminnot. Haluatko silti jatkaa?
#XMSG: To enable audit logs for change operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogChangeMessage=Jotta tarkastuslokit voidaan aktivoida muutosoperaatioita varten, myös tietojen vastaanotto on aktivoitava. Haluatko tehdä sen?
#XMSG: To enable audit logs for read operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogReadMessage=Jotta tarkastuslokit voidaan aktivoida lukuoperaatioita varten, myös tietojen vastaanotto on aktivoitava. Haluatko tehdä sen?
#XMSG: To enable HDI consumption data ingestion and data consumption needs to be enabled. Do you want to enable all?
enableHDIConsumptionMessage=Jotta HDI-kulutus voidaan aktivoida, tietojen vastaanotto ja tietojen kulutus on myös aktivoitava. Haluatko tehdä sen?
#XMSG:
databaseUserPasswordText=Kopioi salasanasi yhteyden määrittämiseksi tähän tietokantakäyttäjään. Jos olet unohtanut salasanasi, voit aina pyytää uutta salasanaa.
#XTIT: Space detail section members title
detailsSectionMembers=Jäsenet
#XMSG: New password set
newPasswordSet=Uusi salasana asetettu
#XFLD: Data Ingestion
dataIngestion=Tietojen vastaanotto
#XFLD: Data Consumption
dataConsumption=Tietojen käyttö
#XFLD: Privileges
privileges=Käyttöoikeudet
#XFLD: Enable Data ingestion
enableDataIngestion=Aktivoi tietojen vastaanotto
#XMSG: Log the read and change operations for data ingestion.
databaseUserAuditHint=Kirjaa lokiin tietojen vastaanoton luenta- ja muutosoperaatiot.
#XMSG: Make your space data available in your HDI containers.
databaseUserHDIHint=Tee tietosi käytettäviksi HDI-säilöissäsi.
#XFLD: Enable Data consumption
enableDataConsumption=Aktivoi tietojen käyttö
#XMSG: Allow other apps or tools to consume your space data.
enableDataConsumptionHint=Salli muiden sovellusten tai työkalujen käyttää tilasi tietoja.
#XMSG: To set up access via this schema user copy the credentials to your user-provided service. In case you can only copy the credentials without a password make sure to add the password later.
databaseUserHDIConsumptionHint=Voit määrittää pääsyn tämän tietokantakäyttäjän kautta kopioimalla tunnistetiedot käyttäjän omaan palveluun. Jos voit kopioida tunnistetiedot vain ilman salasanaa, varmista, että lisäät salasanan myöhemmin.
#XFLD: Data Flow Runtime Capacity label in space overview
dataFlowRuntimeCapacity=Tietovirran ajoaikakapasiteetti ({0}:{1} tuntia {2} tunnista)
#XFLD: Could not load Data Flow Runtime Capacity
dataFlowRuntimeCapacityError=Tietovirran ajoaikakapasiteettia ei voitu ladata
#XMSG: User can grant data consumption to other users.
databaseUserConsumptionWithGrantOptionHint=Käyttäjä voi myöntää tietojenkulutusoikeuksia muille käyttäjille.
#XFLD: Enable data consumption with grant option
enableConsumptionWithGrantOption=Aktivoi tietojen kulutus myöntämisvaihtoehdon kanssa
#XMSG: To enable data consumption with grant option data consumption needs to be enabled. Do you want to enable both?
enableConsumptionWithGrantMessage=Jotta tietojen kulutus myöntämisvaihtoehdon kanssa voidaan aktivoida, tietojen kulutus on aktivoitava. Haluatko aktivoida molemmat?
#XFLD: Enable Automated Predictive Library (APL) and Predictive Analysis Library (PAL)
allowUsingScriptServerFunctionality=Aktivoi Automated Predictive Library (APL) ja Predictive Analysis Library (PAL)
#XMSG: User can use SAP HANA Cloud embedded machine learning functions.
allowUsingScriptServerFunctionalityHint=Käyttäjä voi käyttää SAP HANA Cloudin integroituja koneoppimisen toimintoja.
#XFLD: Password Policy
passwordPolicy=Salasanakäytäntö
#XMSG: Password Policy
passwordPolicyHint=Aktivoi konfiguroitu salasanakäytäntö tai poista sen aktivointi.
#XFLD: Enable Password Policy
enablePasswordPolicy=Aktivoi salasanakäytäntö
#XMSG: Read Access to the Space Schema
readAccessTitle=Tilakaavion lukukäyttö
#XMSG: read access hint
readAccessHint=Salli tietokantakäyttäjän yhdistää ulkoiset työkalut tilakaavioon ja lukunäkymään, jotka näytetään kulutusta varten.
#XFLD: Space Schema
spaceSchema=Tilakaavio
#XFLD: Enable Read Access (SQL)
enableReadAccess=Aktivoi lukukäyttö (SQL)
#XMSG: Allow the user to grant read access to other users.
withGrantOptionHint=Salli käyttäjän myöntää lukukäyttö muille käyttäjille.
#XFLD: With Grant Option
withGrantOption=Myöntämisvaihtoehdon kanssa
#XMSG: Make your space data available in your HDI containers.
enableHDIConsumptionHint=Tee tietosi käytettäviksi HDI-säilöissäsi.
#XFLD: Enable HDI Consumption
enableHDIConsumption=Aktivoi HDI-kulutus
#XMSG: Write Access to the User’s Open SQL Schema
writeAccessTitle=Käyttäjän Open SQL -kaavion kirjoituskäyttö
#XMSG: write access hint
writeAccessHint=Salli tietokantakäyttäjän yhdistää ulkoiset työkalut käyttäjän Open SQL -kaavioon tietoentiteettien luomiseksi ja tietojen vastaanottamiseksi tilassa käyttöä varten.
#XFLD: Open SQL Schema
openSQLSchema=Open SQL -kaavio
#XFLD: Enable Write Access (SQL DDL & DML)
enableWriteAccess=Aktivoi kirjoituskäyttö (SQL, DDL, & DML)
#XMSG: audit hint
auditHint=Kirjaa lokiin Open SQL -kaavion luku- ja muutosoperaatiot.
#XMSG: data consumption hint
dataConsumptionHint=Näytä oletusarvoisesti tilan kaikki uudet näkymät kulutusta varten. Mallintajat voivat ohittaa tämän asetuksen yksilöllisissä näkymissä tulostuksen sivualueen valitsimen “Näytä kulutusta varten” avulla. Voit valita myös näkymien näyttömuodot.
#XFLD: Expose for Consumption by Default
exposeDataConsumption=Näytä kulutusta varten oletusarvoisesti
#XMSG: database users hint consumption hint
databaseUsersHint2New=Luo tietokantakäyttäjät ulkoisten työkalujen yhdistämiseksi kohteeseen SAP Datasphere. Aseta käyttöoikeudet, joilla sallit käyttäjien lukea tilan tietoja ja luoda tietoentiteettejä (DDL) ja vastaanottaa tietoja (DML) tilassa käyttöä varten.
#XFLD: Read
read=Lue
#XFLD: Read (HDI)
readHDI=Lue (HDI)
#XFLD: Write
write=Kirjoita
#XMSG: HDI Containers Hint
HDIContainersHint2=Aktivoi SAP HANA Deployment Infrastructure (HDI) -säilöjen käyttö tilassasi. Mallintajat voivat käyttää näkymien lähteinä HDI-artifakteja, ja HDI-asiakkaat voivat käyttää tilasi tietoja.
#XMSG: Open info dialog
openDatabaseUserInfoDialog=Avaa infodialogi
#XMSG: Database user is locked. Open info dialog to unlock
openLockedDatabaseUserInfoDialog=Tietokantakäyttäjä on lukittu. Avaa lukitus avaamalla valintaikkuna
#XFLD: Table
table=Taulu
#XFLD: Space section partner configuration title
partnerConnectionConfigurationSectionTitle=Kumppanin yhteys
#XFLD: Space section partner configuration subtitle
partnerConnectionConfigurationSectionSubTitle=Kumppanin yhteyden konfiguraatio
#XFLD: Space section partner configuration hint
partnerConnectionConfigurationSectionHint=Määritä oma kumppanin yhteysruutusi lisäämällä oma URL:n iFrame ja symboli. Tämä konfiguraatio on käytettävissä vain tätä vuokralaista varten.
#XFLD: Table Name Field
partnerConnectionConfigurationName=Ruudun nimi
#XFLD: Table iFrame URL field
partnerConnectionConfigurationiFrameURL=iFrame URL
#XFLD: Table iFrame Post Message Origin field
partnerConnectionConfigurationiFramePostMessageOrigin=iFrame-julkaisusanoman alkuperä
#XFLD: Table icon/image name
partnerConnectionConfigurationIcon=Symboli
#XMSG: Table no result found
partnerConnectionConfigurationNotfound=Kumppanin yhteyden konfiguraatio(i)ta ei löytynyt.
#XMSG: Partner Connection Confgurations are not available
partnerConnectionConfigurationNotAvailable=Kumppaniyhteyden konfiguraatioita ei voi näyttää, jos ajonaikainen tietokanta ei ole käytettävissä.
#XFLD: Dialog Create Partner Connection Configuration
partnerConnectionConfigurationDialogTitleAdd=Luo kumppanin yhteyden konfiguraatio
#XFLD: Dialog Create Partner Connection Configuration Upload Icon
partnerConnectionConfigurationButtonUloadPartnerIcon=Lataa symboli
#XFLD: Dialog Create Partner Connection Configuration Select
partnerConnectionConfigurationSelectIcon=Valitse (maksimikoko 200 kt)
#XFLD: Dialog Create Partner Connection Configuration Enter a name
partnerConnectionConfigurationNamePlaceholder=Kumppaniruudun esimerkki
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL
partnerConnectionConfigurationiFrameURLPlaceholder=http://www.example.com
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL Check
partnerConnectionConfigurationiFramePostMessageOriginPlaceholder=.example.com
#XFLD: Dialog Create Partner Connection Configuration Browse
partnerConnectionConfigurationBrowseButton=Selaa
#XFLD: Create Partner Connection Configuration Msg
partnerConnectionConfigurationUploadMsg=Kumppanin yhteyden konfiguraation luonti onnistui.
#XMSG: Error while deleting Partner Connection configuration(s)
partnerConnectionConfigurationDeleteError=Virhe poistettaessa kumppanin yhteyden konfiguraatio(i)ta.
#XMSG: Partner Connection Configuration has been successfully deleted
partnerConnectionConfigurationDeleteSuccess=Kumppanin yhteyden konfiguraation poisto onnistui.
#XMSG: Error while retrieving Partner Connection Configurations
partnerConnectionConfigurationLoadError=Virhe haettaessa kumppanin yhteyden konfiguraatioita.
#XMSG: Error fileSizeLimitExceeded
partnerConnectionConfigurationfileSizeLimitExceeded=Ruutua ei voitu ladata, koska sen koko ylittää 200 kt:n maksimikoon.
#XBUT: Create Partner Connection Configuration
partnerConnectionConfigurationButtonCreate=Luo kumppanin yhteyden konfiguraatio
#XBUT: Delete Partner Connection Configuration
partnerConnectionConfigurationButtonDelete=Poista kumppanin yhteyden konfiguraatio.
#XMSG: Partner Tile Creation Failure
upsertPartnerTileFailed=Kumppaniruutua ei voitu luoda.
#XMSG: Partner Tile Deletion Failure
deletePartnerTileFailed=Kumppaniruutua ei voitu poistaa.
#XMSG: HANA cloud connector settings reset failure
resetHANASCCSettingsFailed=Asiakkaan SAP HANA -pilviliittimen asetusten palautus epäonnistui
#XFLD: Workload Class
workloadClass=Työkuormituksen luokka
#XFLD: Workload Management
workloadManagement=Työkuormituksen hallinta
#XFLD: Priority
workloadClassPriority=Prioriteetti
#XMSG:
workloadManagementPriorityHint=Voit määrittää tämän tilan priorisoinnin, kun teet kyselyjä tietokannasta. Kirjoita arvo väliltä 1 (alin prioriteetti) arvoon 8 (suurin prioriteetti). Tilanteessa, jossa tilat kilpailevat käytettävissä olevista säikeistä, prioriteetit suoritetaan ennen tiloja, joiden prioriteetit ovat pienemmät.
#XMSG:
workloadClassPriorityHint=Voit määrittää tilan prioriteetin välillä 0 (alhaisin) ja 8 (korkein). Tilan lauseet, joiden prioriteetti on korkea, suoritetaan ennen muita tilan lauseita, joiden prioriteetti on alhaisempi. Prioriteetin oletusarvo on 5. Arvo 9 on varattu järjestelmätoimintoihin eikä sitä voi valita tilan arvoksi.
#XFLD: Statement Limits
workloadclassStatementLimits=Lauseen raja-arvot
#XFLD: Workload Configuration
workloadConfiguration=Työkuormituksen konfiguraatio
#XMSG:
workloadClassStatementLimitsHint=Voit määrittää muistin säikeiden ja gigatavujen maksimimäärän (tai prosentin), jonka rinnakkain tilassa ajettavat lauseet voivat käyttää. Voit syöttää minkä tahansa arvon tai prosentin väliltä 0 (ei rajaa) ja kokonaismuisti ja käytettävissä olevat säikeet vuokralaisessa. \n\n Jos määrität säikeille raja-arvon, ota huomioon, että suorituskyky voi alentua. \n\n Jos määrität muistille raja-arvon, niitä lauseita, jotka saavuttavat muistin raja-arvon, ei suoriteta.
#XMSG:
workloadClassStatementLimitsDescription=Oletuskonfiguraatio tarjoaa runsaasti resurssirajoituksia ja estää samalla yksittäisen tilan ylikuormittamasta järjestelmää.
#XMSG:
workloadClassStatementLimitCustomDescription=Voit määrittää säikeen ja muistin enimmäisrajat, joita tilassa samanaikaisesti suoritettavat lauseet voivat kuluttaa.
#XMSG:
totalStatementThreadLimitHelpText=Jos säieraja asetetaan liian alhaiseksi, se voi vaikuttaa lauseen suorituskykyyn, kun taas liian suuret arvot tai 0 saattavat antaa tilan kuluttaa kaikki käytettävissä olevat järjestelmäsäikeet.
#XMSG:
totalStatementMemoryLimitHelpText=Liian alhaisen muistirajan asettaminen saattaa aiheuttaa muistin loppumisen ongelmia, kun taas liian suuret arvot tai 0 saattavat antaa tilan kuluttaa kaiken käytettävissä olevan järjestelmämuistin.
#XMSG:
totalStatementThreadLimitHelpTextRestricted=Syötä vuokralaisessa käytettävissä olevien säikeiden määräksi prosentti väliltä 1­70 (tai vastaava luku). Säierajan asettaminen liian alhaiseksi voi vaikuttaa lauseen suorituskykyyn, kun taas liian suuret arvot voivat vaikuttaa lauseiden suorituskykyyn muissa tiloissa.
#XMSG:
totalStatementThreadLimitHelpTextDynamic=Syötä vuokralaisessa käytettävissä olevien säikeiden määräksi prosentti väliltä 1–{0} %(tai vastaava luku). Säierajan asettaminen liian alhaiseksi voi vaikuttaa lauseen suorituskykyyn, kun taas liian suuret arvot voivat vaikuttaa lauseiden suorituskykyyn muissa tiloissa.
#XMSG:
totalStatementMemoryLimitHelpTextNew=Syötä arvo tai prosentti väliltä 0 (ei rajaa) ja vuokralaisessa käytettävissä olevan muistin kokonaismäärä. Muistirajan asettaminen liian alhaiseksi voi vaikuttaa lauseen suorituskykyyn, kun taas liian suuret arvot voivat vaikuttaa lauseiden suorituskykyyn muissa tiloissa.
#XFLD: Total Statement Thread Limit
totalStatementThreadLimit=Lauseen säikeiden raja-arvojen yhteismäärä
#XFLD: Segmented button label
workloadclassPercentage=%
#XFLD: Segmented button label
workloadclassGigaByte=GB
#XFLD, 80: Segmented button label
workloadclassThreads=Säikeet
#XFLD: Total Statement Memory Limit
totalStatementMemoryLimit=Lauseen muistin raja-arvojen yhteismäärä
#YMSE: Error while loading customer hana info
loadCustomerHanaInfoError=Asiakkaan SAP HANA -tietojen lataus epäonnistui.
#XMSG:
minimumLimitReached=Minimiraja saavutettu.
#XMSG:
maximumLimitReached=Maksimiraja saavutettu.
#XMSG: Name Taken for Technical Name
technical-name-taken=Yhteys, jolla on syöttämäsi tekninen nimi, on jo olemassa. Syötä jokin toinen nimi.
#XMSG: Name Too long for Technical Name
technical-name-too-long=Syöttämäsi tekninen nimi ylittää 40 merkkiä. Syötä nimi, jossa on vähemmän merkkejä.
#XMSG: Technical name field empty
technical-name-field-empty=Syötä tekninen nimi.
#XMSG: Generic error when user enters invalid characters for a field
field-invalid-characters=Nimessä saa käyttää vain kirjaimia (a-z), numeroita (0-9) ja alaviivoja (_).
#XMSG: Generic error when user enters invalid initial characters for a field
field-underscore-trim=Syöttämäsi nimi ei saa alkaa alaviivalla tai päättyä alaviivaan (_).
#XFLD: Checkbox label for Enable Statement Limits
enableStatementLimits=Aktivoi lauseen raja-arvot
#XFLD: Connection SubSection Settings Title in space details
connectionSettings=Asetukset
#XMSG: Connections tool hint in Space details section
connectionsToolHint=Voit luoda tai muokata yhteyksiä avaamalla yhteyksien sovelluksen sivunavigoinnista tai napsauttamalla tästä:
#XBUT: Connection list Open Connection Tool Button
button-connectiontool=Siirry yhteyksiin
#XFLD: Not deployed label on space tile
notDeployedLabel=Tilaa ei ole vielä otettu käyttöön.
#XFLD: Not deployed additional text on space tile
notDeployedText=Ota tila käyttöön.
#XFLD: Corrupt space label on space tile
corruptSpace=Jokin meni vikaan.
#XFLD: Corrupt space additional text on space tile
corruptSpaceHint=Yritä ottaa uudelleen käyttöön tai ota yhteyttä tukeen.
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Tarkastuslokin tiedot
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Hallintatiedot
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Muut tiedot
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Tilojen tiedot
#XMSG: Unlock Space confirmation text
unlockSpaceConfirmation=Haluatko todella poistaa tilan lukituksen?
#XMSG: Lock Space confirmation text
lockSpaceConfirmation=Haluatko todella lukita tilan?
#XFLD: Lock
lock=Lukitse
#XFLD: Unlock
unlock=Poista lukitus
#XFLD: Locking
locking=Lukitaan
#XMSG: Success message after locking space
lockSpaceSuccessMsg=Tila lukittu
#XMSG: Success message after unlocking space
unlockSpaceSuccessMsg=Tilan lukitus poistettu
#XMSG: Success message after locking spaces
lockSpacesSuccessMsg=Tilat lukittu
#XMSG: Success message after unlocking spaces
unlockSpacesSuccessMsg=Tilojen lukitus poistettu
#YMSE: Error while locking a space
lockSpaceError=Tilaa ei voi lukita.
#YMSE: Error while unlocking a space
unlockSpaceError=Tilan lukitusta ei voi poistaa.
#XTIT: popup title Warning
confirmationWarningTitle=Varoitus
#XMSG: Message strip message when a space was locked manually
manualLockedMessage=Tila on lukittu manuaalisesti.
#XMSG: Message strip message when a space was locked due to the amount of generated audit logs
auditLogLockedMessage=Järjestelmä on lukinnut tilan, koska tarkastuslokit kuluttavat paljon gigatavuja levyltä.
#XMSG: Message strip message when a space was locked because the assigned quota was exceeded.
quotaLockedMessage=Järjestelmä on lukinnut tilan, koska se ylittää sille kohdistetun muistin tai levymuistin.
#XMSG: Unlock Spaces confirmation text
unlockSpacesConfirmation=Haluatko todella poistaa valittujen tilojen lukituksen?
#XMSG: Lock Spaces confirmation text
lockSpacesConfirmation=Haluatko todella lukita valitut tilat?
#XTIT: Scoped role editor dialog title
scopedRoleEditorDialogTitle=Laajuuteen sisältyvien roolien editori
#XTIT: ECN Management title
ecnManagementTitle=Tilan ja joustavan laskentasolmun hallinta
#XFLD: ECNs
ecns=Joustavat laskentasolmut
#XFLD: ECN phase Ready
ecnReady=Valmis
#XFLD: ECN phase Running
ecnRunning=Käynnissä
#XFLD: ECN phase Initial
ecnInitial=Ei valmiina
#XFLD: ECN phase Starting
ecnStarting=Käynnistetään
#XFLD: ECN phase Starting Failed
ecnStartingFailed=Käynnistys epäonnistui
#XFLD: ECN phase Stopping
ecnStopping=Pysäytetään
#XFLD: ECN phase Stopping Failed
ecnStoppingFailed=Pysäytys epäonnistui
#XBTN: Assign Button
assign=Kohdista tiloja
#XBTN: Start Header-Button
start=Käynnistä
#XBTN: Update Header-Button
repair=Päivitä
#XBTN: Stop Header-Button
stop=Pysäytä
#XFLD: ECN hours remaining
ecnHoursRemaining=1000 tuntia jäljellä
#XFLD: ECN block-hours remaining
ecnBlockHoursRemaining={0} blokkituntia jäljellä
#XFLD: ECN block-hour remaining
ecnBlockHourRemaining={0} blokkitunti jäljellä
#XTIT: ECN creation dialog title
ecnCreationDialogTitle=Luo joustava laskentasolmu
#XTIT: ECN edit dialog title
ecnEditDialogTitle=Muokkaa joustavaa laskentasolmua
#XTIT: ECN deletion dialog title
ecnDeletionDialogTitle=Poista joustava laskentasolmu
#XTIT: ECN Space assignment dialog title
ecnSpaceAssignmentDialogTitle=Kohdista tilat
#XFLD: ECN ID
ECNIDLabel=Joustava laskentasolmu
#XTXT: Selected toolbar text
selectedToolbarText=Valittu: {0}
#XTIT: Elastic Compute Nodes
ECNslong=Joustavat laskentasolmut
#XTIT: Object assignment - Table header: Objects available
objectsAvailable=Objektien lukumäärä
#XTIT: Object assignment - Dialog header text
selectObjects=Valitse tilat ja objektit, jotka haluat kohdistaa joustavaan laskentasolmuusi:
#XTIT: Object assignment - Table header title: Objects
objects=Objektit
#XTIT: Object assignment - Table header: Type
type=Tyyppi
#XMSG: Message strip delete database user audit warning
deleteDatabaseUserWarningMessageStrip=Huomaa, että tietokannan käyttäjän poistaminen johtaa kaikkien luotujen tarkastuslokimerkintöjen poistamiseen. Jos haluat säilyttää tarkastuslokit, harkitse niiden vientiä ennen tietokannan käyttäjän poistamista.
#XMSG: Message strip delete HDI container audit logs impact
deleteHDIContainerWarningMessageStrip=Huomaa, että HDI-säilön kohdistuksen kumoaminen johtaa kaikkien luotujen tarkastuslokimerkintöjen poistamiseen. Jos haluat säilyttää tarkastuslokit, harkitse niiden vientiä ennen kuin kumoat HDI-säilön kohdistuksen.
#XTXT: All audit logs
allAuditLogs=Kaikki tilaa varten luodut valvontalokimerkinnät
#XMSG: Message strip disable audit warning
disableAuditWarningMessageStrip=Huomaa, että tarkastuskäytännön (luku- tai muutostoimintojen) poistaminen käytöstä johtaa kaikkien sen tarkastuslokimerkintöjen poistamiseen. Jos haluat säilyttää tarkastuslokin merkinnät, harkitse niiden vientiä ennen kuin poistat valvontakäytännön käytöstä.
#XTIT: No Spaces assigned to ECN Illustration title
noSpacesECNTitle=Ei vielä kohdistettuja tiloja tai objekteja
#XTIT: No Spaces assigned to ECN Illustration description
noSpacesECNDescription=Jotta voit aloittaa työskentelyn joustavassa laskentasolmussa, sinun on kohdistettava siihen tila tai objekteja.
#XTIT: No Spaces Illustration title
noSpacesTitle=Tiloja ei ole vielä luotu
#XTIT: No Spaces Illustration description
noSpacesDescription=Aloita tietojen hankinta luomalla tila.
#XTIT: No Spaces Illustration title
noDeletedSpacesTitle=Roskakori on tyhjä
#XTIT: No Spaces Illustration description
noDeletedSpacesDescription=Poistetut tilat voi palauttaa tästä.
#XTXT:  {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDatabaseUsers=Kun tila otetaan käyttöön, seuraavat tietokannan käyttäjät poistetaan {0}, eikä niitä voi palauttaa:
#XTIT: Delete database users
deleteDatabaseUsersTitle=Poista tietokannan käyttäjät
#YMSE: Error if ecn id already exists
ecnIDInputDuplicateError=Tunnus on jo olemassa.
#YMSE: Error if ecn id contains invalid characters
ecnIDInputInvalidError=Käytä vain pieniä kirjaimia a - z ja numeroita 0 – 9
#YMSE: Error if ecn id is too short
ecnIDInputTooShortError=Tunnuksen on oltava vähintään {0} merkkiä pitkä.
#XMSG: ecn id length warning
ecnIdLengthWarning={0} merkin maksimi ylitetty.
#XFLD: open System Monitor
systemMonitor=Järjestelmänvalvonta
#XFLD: open ECN schedule dialog menu entry
schedule=Aikataulu
#XFLD: open create ECN schedule dialog
createSchedule=Luo aikataulu
#XFLD: open change ECN schedule dialog
changeSchedule=Muokkaa aikataulua
#XFLD: open delete ECN schedule dialog
deleteSchedule=Poista aikataulu
#XFLD: open self assign ECN schedule dialog
selfAssignSchedule=Kohdista aikataulu minulle
#XFLD: open pause ECN schedule dialog
pauseSchedule=Keskeytä aikataulu
#XFLD: open resume ECN schedule dialog
resumeSchedule=Palaa aikatauluun
#XFLD: View Logs
viewLogs=Näytä lokit
#XFLD: Compute Blocks
computeBlocks=Laskentalohkot
#XFLD: Memory label in ECN creation dialog
ecnMemory=Muisti (GB)
#XFLD: Storage label in ECN creation dialog
ecnStorage=Tallennustila (GB)
#XFLD: Number of CPU label in ECN creation dialog
ecnCPUCount=CPU:n lukumäärä
#XFLD: ECN updated by label
changedBy=Muuttaja
#XFLD: ECN updated on label
changedOn=Muutospäivämäärä
#XMSG: Success message after creating a Elastic Compute Node
createEcnSuccessMessage=Joustava laskentasolmu luotu
#YMSE: Error while creating a Elastic Compute Node
createEcnError=Joustavaa laskentasolmua ei voitu luoda
#XMSG: Success message after updating a Elastic Compute Node
updateEcnSuccessMessage=Joustava laskentasolmu päivitetty
#XMSG: Error while updating a Elastic Compute Node
udpateEcnError=Joustavaa laskentasolmua ei voitu päivittää
#XMSG: Success message after deleting a Elastic Compute Node
deleteEcnSuccessMessage=Joustava laskentasolmu poistettu
#YMSE: Error while deleting a Elastic Compute Node
deleteEcnError=Joustavaa laskentasolmua ei voitu poistaa
#XMSG: Success message after starting a Elastic Compute Node
startingEcnSuccessMessage=Joustava laskentasolmu käynnistetään
#XMSG: Success message after stopping a Elastic Compute Node
stoppingEcnSuccessMessage=Joustava laskentasolmu pysäytetään
#YMSE: Error while starting a Elastic Compute Node
startingEcnError=Joustava laskentasolmu ei voinut käynnistyä
#YMSE: Error while stopping a Elastic Compute Node
stoppingEcnError=Joustava laskentasolmu ei voinut pysähtyä
#XBUT: Add Object button for an ECN
assignObjects=Lisää objekteja
#XFLD: Enable auto object assignment label
enableAutoObjAssignment=Lisää kaikki objektit automaattisesti
#XFLD: object type label to be assigned
objectTypeLabel=Tyyppi (semanttinen käyttö)
#XFLD: assigned object type label
assignedObjectTypeLabel=Tyyppi
#XFLD: technical name label
TechnicalNameLabel=Tekninen nimi
#XFLD: Object selecttion ECN label
objectSelectionECNLabel=Valitse objektit, jotka haluat lisätä joustavaan laskentasolmuun
#XTIT: Add objects dialog title
assignObjectsTitle=Kohdista objektit:
#XFLD: object label with object count
objectLabel=Objekti
#XMSG: No objects available to add message.
noObjectsToAssign=Kohdistettavissa olevia objekteja ei ole.
#XMSG: No objects assigned message.
noAssignedObjects=Objekteja ei kohdistettu.
#XTIT: Remove objects popup title
removeObjectsOrSpacesConfirmationTitle=Varoitus
#XTIT: Delete ECN popup button and title
deleteECNConfirmationButtonAndTitle=Poista
#XMSG: Remove objects popup text
removeObjectsConfirmation=Haluatko todella poistaa valitut objektit?
#XMSG: Remove spaces popup text
removeSpacesConfirmation=Haluatko todella poistaa valitut tilat?
#XBUT: Confirmation button for objects dialog
removeObjectsOrSpacesConfirmationBtn=Poista tilat
#XMSG: Success message after removing objects
removeObjectsSuccessMessage=Näytettävät objektit on poistettu
#XMSG: Success message after assigning objects
assignObjectsSuccessMessage=Näytettävät objektit kohdistettu
#XFLD: auto object assignment label for space tile
autoObjAssignmentLabel=Kaikki näytettävät objektit
#XFLD: Spaces tab label
spacesTabLabel=Tilat
#XFLD: Exposed objects tab label
consumptionObjectsTabLabel=Näytettävät objektit
#XMSG: Success message after unassigning spaces
spacesUnassignedSuccessMessage=Tilat on poistettu
#XMSG: Success message after unassigning space
spaceUnassignedSuccessMessage=Tila on poistettu
#YMSE: Error while assigning / unassigning spaces
spacesAssignUnassignError=Tiloja ei voitu kohdistaa tai poistaa.
#YMSE: Error while removing objects
removeObjectsError=Objekteja ei voitu kohdistaa tai poistaa.
#YMSE: Error while removing object
removeObjectError=Objektia ei voitu kohdistaa tai poistaa.
#YMSE: Error when saving the ecn with invalid value
invalidSizingPlanError=Aiemmin valittu luku ei enää kelpaa. Valitse kelpaava luku.
#YMSE: Error when saving the ecn with invalid value
invalidPerformanceClassError=Valitse kelpaava suorituskykyluokka.
#YMSE: Error when saving the ecn with invalid value
performanceClassNotAvailableError=Aiemmin valittu suorituskykyluokka "{0}" ei ole tällä hetkellä voimassa. Valitse voimassa oleva suorituskykyluokka.
#XTXT: Delete ECN confirmation text
deleteConfirmationTxtECN=Haluatko varmasti poistaa joustavan laskentasolmun?
#XFLD: tooltip for ? button
help=Ohje
#XFLD: ECN edit button label
editECN=Konfiguroi
#XFLD: Technical type label for ERModel
DWC_ERMODEL=Entiteettisuhteiden malli
#XFLD: Technical type label for Local Table
DWC_LOCAL_TABLE=Paikallinen taulu
#XFLD: Technical type label for Remote Table
DWC_REMOTE_TABLE=Etätaulu
#XFLD: Technical type label for Analytic Model
DWC_ANALYTIC_MODEL=Analyyttinen malli
#XFLD: Technical type label for Task Chain
DWC_TASKCHAIN=Tehtäväketju
#XFLD: Technical type label for Data Flow
DWC_DATAFLOW=Tietovirta
#XFLD: Technical type label for Replication Flow
DWC_REPLICATIONFLOW=Replikointivirta
#XFLD: Technical type label for Transformation Flow
DWC_TRANSFORMATIONFLOW=Muuntovirta
#XFLD: Technical type label for Intelligent Lookup
DWC_IDT=Älykäs haku
#XFLD: Technical type label for Repository
DWC_REPOSITORY_PACKAGE=Tietohakemisto
#XFLD: Technical type label for Enterprise Search
DWC_ESMODEL=Yrityshaku
#XFLD: Technical type label for View
DWC_VIEW=Näkymä
#XFLD: Technical type label for Data Product
DWC_DATAPRODUCT=Datatuote
#XFLD: Technical type label for Data Access Control
DWC_DAC=Tietojen haun ohjaus
#XFLD: Technical type label for Folder
DWC_FOLDER=Kansio
#XFLD: Technical type label for Data Entity
DWC_BUSINESS_ENTITY=Liiketoimintaentiteetti
#XFLD: Technical type label for Business Entity Variant
DWC_BUSINESS_ENTITY_VARIANT=Liiketoimintaentiteetin variantti
#XFLD: Technical type label for Responsibility Scenario
DWC_AUTH_SCENARIO=Vastuuskenaario
#XFLD: Technical type label for Fact Model
DWC_FACT_MODEL=Faktamalli
#XFLD: Technical type label for Transformation Flow
DWC_PERSPECTIVE=Näkökulma
#XFLD: Technical type label for Consumption Model
DWC_CONSUMPTION_MODEL=Kulutusmalli
#XFLD: Technical type label for Remote Connection
DWC_REMOTECONNECTION=Etäyhteys
#XFLD: Technical type label for Fact Model Variant
DWC_FACT_MODEL_VARIANT=Faktamallivariantti
#XMSG: Schedule created alert message
createScheduleSuccess=Aikataulu luotu
#XMSG: Schedule updated alert message
updateScheduleSuccess=Aikataulu päivitetty
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Aikataulu poistettu
#XMSG: Schedule takeover success message
ownerChangeScheduleSuccess=Sinulle kohdistettu aikataulu
#XMSG: Message for starting pausing of schedule
pauseScheduleStarted=Keskeytetään 1 aikataulu
#XMSG: Message for starting resuming of schedule
resumeScheduleStarted=Jatketaan 1 aikataulua
#XFLD: Segmented button label
availableSpacesButton=Käytettävissä
#XFLD: Segmented button label
selectedSpacesButton=Valittu
#XFLD: Visit website button text
visitWebsite=Käy WWW-sivustolla
#XMSG: Warning message on unselecting source language
sourceLanguageRemovalWarning=Aiemmin valittu lähdekieli poistetaan.
#XBUT: enable translation confirmation button
enableTranslationConfirmationBtn=Ota käyttöön
#XFLD: ECN performance class label
performanceClassLabel=Suorituskykyluokka
#XTXT performance class memory text
memoryText=Muisti
#XTXT performance class compute text
computeText=Laskenta
#XTXT performance class high-compute text
highComputeText=Korkea laskenta
#XBUT: Recycle Bin Button Text
recycleBin=Roskakori
#XBUT: Restore Button Text
restore=Palauta
#XMSG: Warning message for new Workload Management UI
priorityWarning=Alue on vain luku -tilassa. Voit muuttaa tilan prioriteetin Järjestelmä / Konfiguraatio / Työkuormituksen hallinta -alueella.
#XMSG: Warning message for new Workload Management UI
workloadWarning=Alue on vain luku -tilassa. Voit muuttaa tilan työkuormituksen konfiguraation Järjestelmä / Konfiguraatio / Työkuormituksen hallinta -alueella.
#XCOL: Space table-view column: Apache Spark vCPUs
sparkManagementVCPUs=Apache Spark - virtuaaliset CPU:t
#XCOL: Space table-view column: Apache Spark Memory (GB)
sparkManagementMemory=Apache Spark -muisti (GB)
#XCOL: Space type (from Space Capability) for Data Ingestion Spaces
ingestionSpaceType=Tietotuotteiden vastaanotto
#XFLD: Deployment in progress nodatatext for spark table
sparkTableDeploymentInProgress=Tietoja ei ole saatavissa, koska tilaa otetaan parhaillaan käyttöön
#XFLD: Runtime data being loaded nodatatext for spark table
sparkTableDeploymentLoading=Tietoja ei ole saatavissa, koska tilaa ladataan parhaillaan
#XCOL: Space type (from Space Capability) for BW Private Cloud Edition
BWPCESpaceType=SAP BW
#XBUT: Go to Instance Mappings Button
navToMappingsButton=Muokkaa instanssin kohdistuksia
#XFLD: SAP HANA Data Lake Storage label if less than 1GB
dataLakeStorageUsed=< 1 GB
