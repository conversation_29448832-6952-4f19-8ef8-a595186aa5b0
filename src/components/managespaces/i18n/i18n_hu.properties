#DO NOT REMOVE________________________
#XFLD: Breadcrumb for space monitoring page
spaceMonitorBreadcrumb=Figyelés
#XTXT: Type name for spaces in browser tab page title
space=Tér
#_____________________________________
#XFLD: Spaces label in
spaces=Terek
#XFLD: Manage plan button text
manageQuotaButtonText=Díjcsomag kezelése
#XBUT: Manage resources button
manageResourcesButton=Erőforrások kezelése
#XFLD: Create space button tooltip
createSpace=Tér létrehozása
#XFLD: Create
create=Létrehozás
#XFLD: Deploy
deploy=Üzembe helyezés
#XFLD: Page
page=Oldal
#XFLD: Cancel
cancel=Mégse
#XFLD: Update
update=Frissítés
#XFLD: Save
save=Mentés
#XFLD: OK
ok=OK
#XFLD: days
days=nap
#XFLD: Space tile edit button label
edit=Szerkesztés
#XFLD: Auto Assign all objects to space
autoAssign=Automatikus hozzárendelés
#XFLD: Space tile open monitoring button label
openMonitoring=Figyelő
#XFLD: Delete
delete=Törlés
#XFLD: Copy Space
copy=Másolás
#XFLD: Close
close=Bezárás
#XCOL: Space table-view column status
status=Állapot
#XFLD: Space status active
activeLabel=Aktív
#XFLD: Space status locked
lockedLabel=Zárolt
#XFLD: Space status critical
criticalLabel=Kritikus
#XFLD: Space status cold
coldLabel=Hideg
#XFLD: Space status deleted
deletedLabel=Törölve
#XFLD: Space status unknown
unknownLabel=Ismeretlen
#XFLD: Space status ok
okLabel=Jó állapotú
#XFLD: Database user expired
expired=Lejárt
#XFLD: deployed
deployed=Üzembe helyezve
#XFLD: not deployed
notDeployed=Nincs üzembe helyezve
#XFLD: changes to deploy
changesToDeploy=Üzembe helyezendő módosítások
#XFLD: pending
pending=Üzembe helyezés
#XFLD: designtime error
designtimeError=Tervezési idejű hiba
#XFLD: runtime error
runtimeError=Futásidejű hiba
#XFLD: Space created by label
createdBy=Létrehozta
#XFLD: Space created on label
createdOn=Létrehozás dátuma
#XFLD: Space deployed on label
deployedOn=Üzembe helyezve
#XFLD: Space ID label
spaceID=Térazonosító
#XFLD: Priority label
priority=Prioritás
#XFLD: Space Priority label
spacePriority=Tér prioritása
#XFLD: Space Configuration label
spaceConfiguration=Térkonfiguráció
#XFLD: Not available
notAvailable=Nem érhető el
#XFLD: WorkloadType default
default=Alapértelmezett
#XFLD: WorkloadType custom
custom=Egyéni
#XFLD: Data Lake option label; refers to component SAP HANA Cloud, Data Lake
dataLakeAccess=Data lake hozzáférés
#XFLD: Translation label
translationLabel=Fordítás
#XFLD: Source language label
sourceLanguageLabel=Forrásnyelv
#XFLD: Translation CheckBox label
translationCheckBox=Fordítás engedélyezése
#XTXT: Popover hint that space has to be deployed to show user details
openDatabaseUserHintPopoverText=A tér üzembe helyezésével hozzáférhet a felhasználói adatokhoz.
#XTXT: Popover hint that space has to be deployed to open database explorer
openDatabaseExplorerHintPopoverText=A tér üzembe helyezésével megnyithatja az Adatbázis-böngészőt.
#XTXT: Text for when the Data Lake check box is disabled
dataLakeOptionDisabled=Nem használhatja ezt a teret a Data Lake-hez való hozzáférésre, mert már egy másik tér használja.
#XFLD: Data Lake check box text; refers to component SAP HANA Cloud, Data Lake
dataLakeCheckBox=Ezt a teret használom a data lake-hez való hozzáféréshez.
#XFLD: Space Priority minimum label extension
low=Alacsony
#XFLD: Space Priority maximum label extension
high=Magas
#XFLD: Space name label
spaceName=Tér neve
#XFLD: Enable deploy objects checkbox
enableDeployObjects=Objektumok üzembe helyezése
#XTIT: Copy spaces dialog title
copySpaceDialogTitle={0} másolása
#XTXT Human readable text for unselected source language
unSelectedSourceLanguage=(Nincs kiválasztva)
#XTXT Human readable text for language code "af"
af=afrikaans
#XTXT Human readable text for language code "ar"
ar=arab
#XTXT Human readable text for language code "bg"
bg=bolgár
#XTXT Human readable text for language code "ca"
ca=katalán
#XTXT Human readable text for language code "zh"
zh=egyszerűsített kínai
#XTXT Human readable text for language code "zf"
zf=kínai
#XTXT Human readable text for language code "hr"
hr=horvát
#XTXT Human readable text for language code "cs"
cs=cseh
#XTXT Human readable text for language code "cy"
cy=walesi
#XTXT Human readable text for language code "da"
da=dán
#XTXT Human readable text for language code "nl"
nl=holland
#XTXT Human readable text for language code "en-UK"
en-UK=angol (Egyesült Királyság)
#XTXT Human readable text for language code "en"
en=angol (Egyesült Államok)
#XTXT Human readable text for language code "et"
et=észt
#XTXT Human readable text for language code "fa"
fa=perzsa
#XTXT Human readable text for language code "fi"
fi=finn
#XTXT Human readable text for language code "fr-CA"
fr-CA=francia (Kanada)
#XTXT Human readable text for language code "fr"
fr=francia
#XTXT Human readable text for language code "de"
de=német
#XTXT Human readable text for language code "el"
el=görög
#XTXT Human readable text for language code "he"
he=héber
#XTXT Human readable text for language code "hi"
hi=hindi
#XTXT Human readable text for language code "hu"
hu=magyar
#XTXT Human readable text for language code "is"
is=izlandi
#XTXT Human readable text for language code "id"
id=indonéz (bahasa)
#XTXT Human readable text for language code "it"
it=olasz
#XTXT Human readable text for language code "ja"
ja=japán
#XTXT Human readable text for language code "kk"
kk=kazah
#XTXT Human readable text for language code "ko"
ko=koreai
#XTXT Human readable text for language code "lv"
lv=lett
#XTXT Human readable text for language code "lt"
lt=litván
#XTXT Human readable text for language code "ms"
ms=Bahasa Melayu
#XTXT Human readable text for language code "no"
no=norvég
#XTXT Human readable text for language code "pl"
pl=lengyel
#XTXT Human readable text for language code "pt"
pt=portugál (Brazília)
#XTXT Human readable text for language code "pt-PT"
pt-PT=portugál (Portugália)
#XTXT Human readable text for language code "ro"
ro=román
#XTXT Human readable text for language code "ru"
ru=orosz
#XTXT Human readable text for language code "sr"
sr=szerb
#XTXT Human readable text for language code "sh"
sh=szerbhorvát
#XTXT Human readable text for language code "sk"
sk=szlovák
#XTXT Human readable text for language code "sl"
sl=szlovén
#XTXT Human readable text for language code "es"
es=spanyol
#XTXT Human readable text for language code "es-MX"
es-MX=spanyol (Mexikó)
#XTXT Human readable text for language code "sv"
sv=svéd
#XTXT Human readable text for language code "th"
th=thai
#XTXT Human readable text for language code "tr"
tr=török
#XTXT Human readable text for language code "uk"
uk=ukrán
#XTXT Human readable text for language code "vi"
vi=vietnami
#XTIT: Delete spaces confirmation title
deleteConfirmationHeaderText=Terek törlése
#XTXT: Logically delete space delete confirmation text with space name
logicallyDeleteConfirmationBodyTextWithSpaceName=Biztosan a Lomtárba helyezi a(z) {0} teret?
#XTXT: Logically dpace delete confirmation text for multiple spaces
logicallyDeleteConfirmationBodyTextMultiple=Biztosan a Lomtárba helyezi a kiválasztott {0} teret?
#XTXT: Delete space delete confirmation text with space name
deleteConfirmationBodyTextWithSpaceName=Biztosan törli a(z) {0} teret? Ez a művelet nem vonható vissza.
#XTXT: Delete space delete confirmation text for multiple spaces
deleteConfirmationBodyTextMultiple=Biztosan törli a kijelölt {0} teret? Ez a művelet nem vonható vissza. Az alábbi tartalom {1} törlődik:
#XTXT: permanently
permanently=végleg
#XTXT: {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDetail=Az alábbi tartalom {0} törlődik, és nem lesz visszaállítható:
#XTXT: Hint Delete space confirmation text
deleteConfirmationHint=A törlés megerősítéséhez gépelje be a következőt: {0}.
#XTXT: Delete space confirmation error check spelling
deleteConfirmationErrorCheckSpelling=Ellenőrizze, hogy jól írta-e, és próbálkozzon újra.
#XTXT: All Spaces
allSpaces=Minden tér
#XTXT: All data
allData=A térben lévő összes objektum és adat
#XTXT: All connections
allConnections=A térben meghatározott összes kapcsolat
#XFLD: Space tile selection box tooltip
clickToSelect=Kijelölés kattintással
#XTXT: All database users
allDatabaseUsers=A térrel társított Open SQL-sémákban lévő összes objektum és adat
#XFLD: remove members button tooltip
deleteUsers=Tagok eltávolítása
#XTXT: Space long description text
description=Leírás (legfeljebb 4000 karakter)
#XFLD: Add Members button tooltip
addUsers=Tagok hozzáadása
#XFLD: Add Users button tooltip
addUsersTooltip=Felhasználók hozzáadása
#XFLD: Edit Users button tooltip
editUsersTooltip=Felhasználók szerkesztése
#XFLD: Remove Users button tooltip
removeUsersTooltip=Felhasználók eltávolítása
#XFLD: Searchfield placeholder
filter=Keresés
#XCOL: Users table-view column health
health=Rendszerállapot
#XCOL: Users table-view column access
access=Hozzáférés
#XFLD: No user found nodatatext
noDataText=Nem található felhasználó
#XTIT: Members dialog title
selectUserDialogTitle=Tagok hozzáadása
#XTIT: User dialog title
addUserDialogTitle=Felhasználók hozzáadása
#XTIT: Delete connections confirmation title
deleteConnectionConfirmationHeaderText=Kapcsolatok törlése
#XTIT: Delete single connection confirmation title
deleteConnectionConfirmationHeaderTextSingle=Kapcsolat törlése
#XTXT: Delete connection delete confirmation text
deleteConnectionConfirmationBodyText=Biztosan törli a kijelölt kapcsolatokat? Ezzel végleg eltávolítja őket.
#XTIT: Remote source dialog title
selectRemoteConnectionDialogTitle=Kapcsolatok kiválasztása
#XTIT: Share connection dialog title
connectionSharingDialogTitle=Kapcsolat megosztása
#XTIT: Connections shared from other spaces table title
sharedConnectionsFromOtherSpaces=Megosztott kapcsolatok
#XFLD: Add remote source button tooltip
addRemoteConnections=Kapcsolatok hozzáadása
#XFLD: Delete remote source button tooltip
deleteRemoteConnections=Kapcsolatok eltávolítása
#XFLD: Share remote source button tooltip
shareConnections=Kapcsolatok megosztása
#XFLD: Tile-layout tooltip
tileLayout=Csempés elrendezés
#XFLD: Table-layout tooltip
tableLayout=Táblázatos elrendezés
#XMSG: Success message after creating space
createSpaceSuccessMessage=A tér létrejött
#XMSG: Success message after copying space
copySpaceSuccessMessage=A(z) {0} tér másolása a(z) {1} térbe
#XMSG: Success message after deploying space
deploymentSuccessMessage=A tér üzembe helyezése megkezdődött
#XMSG: Success message after Apache Spark update started
sparkUpdateStartedSuccessMessage=Az Apache Spark frissítése megkezdődött
#YMSE: Error while starting Apache Spark update
sparkUpdateStartedErrorMessage=Nem sikerült frissíteni az Apache Sparkot
#XMSG: Success message after updating space
updateSettingsSuccessMessage=A tér részletei frissítve
#XMSG: Success message after unlocking space
unlockSpaceSuccessMessage=A tér átmenetileg feloldva
#XMSG: Success message after deleting a single space
deleteSpaceSuccessMessage=A tér törölve
#XMSG: Success message after deleting multiple spaces
deleteSpacesSuccessMessage=A terek törölve
#XMSG: Success message after restoring a single space
restoreSpaceSuccessMessage=A tér visszaállítva
#XMSG: Success message after restoring multiple spaces
restoreSpacesSuccessMessage=A terek visszaállítva
#YMSE: Error while updating settings
updateSettingsFailureMessage=Nem sikerült frissíteni a tér beállításait.
#YMSE: Error while updating data lake option
dataLakeEnabledInOtherSpace=A data lake már egy másik térhez van hozzárendelve. Egyszerre csak egy tér férhet hozzá a data lake-hez.
#YMSE: Error while updating data lake option
virtualTablesExists=Nem választhatja le a data lake-et erről a térről, mert még vannak függőségek virtuális tábláktól.* Törölje a virtuális táblákat ahhoz, hogy leválaszthassa a data lake-et erről a térről.
#YMSE: Error while unlocking space
unlockSpaceFailureMessage=Nem sikerült feloldani a tér zárolását.
#YMSE: Error while creating space
createSpaceError=Nem sikerült létrehozni a teret.
#YMSE: Error while creating duplicate space
createDuplicateSpaceError=Már van {0} nevű tér.
#YMSE: Error while deleting a single space
deleteSpaceError=Nem sikerült törölni a teret.
#YMSE: Error details while deleting a single space
deleteSpaceDetailedError=A(z) {0} tér már nem működik megfelelően. Próbálja meg újra törölni. Ha ez sem válik be, kérje meg az adminisztrátort, hogy törölje, vagy küldjön be hibajegyet.
#YMSE: Error while deleting a single space in files
deleteSpaceInFilesError=Nem sikerült törölni a téradatokat a Fájlok szakaszban.
#YMSE: Error while deleting a single space users
deleteSpaceUsersError=A felhasználók eltávolítása nem sikerült.
#YMSE: Error while deleting a single space schema
deleteSpaceSchemasError=A sémák eltávolítása nem sikerült.
#YMSE: Error while deleting a single space connections
deleteSpaceConnectionsError=A kapcsolatok eltávolítása nem sikerült.
#YMSE: Error while deleting a single space data
deleteSpaceDataError=Nem sikerült törölni a téradatokat.
#YMSE: Error while deleting multiple spaces
deleteSpacesError=Nem sikerült törölni a tereket.
#YMSE: Error while restoring a single space
restoreSpaceError=Nem sikerült visszaállítani a teret.
#YMSE: Error while restoring multiple spaces
restoreSpacesError=Nem sikerült visszaállítani a tereket.
#YMSE: Error while creating users
createUsersError=A felhasználók hozzáadása nem sikerült.
#YMSE: Error while removing users
removeUsersError=Nem sikerült eltávolítani a felhasználókat.
#YMSE: Error while removing user
removeUserError=Nem sikerült eltávolítani a felhasználót.
#YMSE: Error assigning roles to a user
roleAssignUnAssignError=Nem sikerült hozzáadni a felhasználót a kiválasztott hatókörös szerepekhez. \n\n Nem adhatja hozzá önmagát hatókörös szerephez. Megkérheti az adminisztrátort, hogy adja hozzá.
#YMSE: Error assigning user to the space
userAssignError=Nem sikerült hozzárendelni a felhasználót a térhez. \n\n A felhasználó már hozzá van rendelve az engedélyezett maximális számú térhez (100) a hatókörös szerepekben.
#YMSE: Error assigning users to the space
usersAssignError=Nem sikerült hozzárendelni a felhasználókat a térhez. \n\n A felhasználó már hozzá van rendelve az engedélyezett maximális számú térhez (100) a hatókörös szerepekben.
#YMSE: Error while fetching users
fetchUserWithScopedRolesError=Nem sikerült lehívni a felhasználókat. Próbálkozzon újra később.
#YMSE: Error while fetching scoped roles
fetchScopedRolesError=Nem sikerült lehívni a hatókörös szerepeket.
#YMSE: Error while fetching members
fetchUserError=A tagok lehívása nem sikerült. Próbálkozzon újra később.
#YMSE: Error while loading run-time database
loadRuntimeError=Nem sikerült betölteni az adatokat a futásidejű adatbázisból.
#YMSE: Error while loading spaces
loadSpacesError=Sajnos hiba történt a terek lehívásakor.
#YMSE: Error while loading haas resources
loadStorageError=Sajnos hiba történt a tárolóadatok lehívásakor.
#YMSE: Error no data could be loaded
loadDataError=Sajnos hiba történt az adatok lehívásakor.
#XFLD: Click to refresh storage data
clickToRefresh=Kattintson ide az újbóli próbálkozáshoz.
#XTIT: Delete space popup title
deleteSpacePopupTitle=Tér törlése
#XCOL: Spaces table-view column name
name=Név
#XCOL: Spaces table-view deployment status
deploymentStatus=Üzembehelyezési állapot
#XFLD: Disk label in space details
storageLabel=Lemez (GB)
#XFLD: In-Memory label in space details
ramLabel=Memória (GB)
#XFLD: Memory label on space card
memory=Tárolóként használt memória
#XFLD: Storage Assignment label in space details
storrageAssignmentLabel=Tértároló
#XFLD: Storage Type label in space details
storageTypeLabel=Tárolótípus
#XFLD: Enable Space Quota
enableSpaceQuota=Térkvóta engedélyezése
#XFLD: No Space Quota
noSpaceQuota=Nincs térkvóta
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
diskAndInMemoryText=SAP HANA adatbázis (lemez és memória)
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
dataLakeFilesText=SAP HANA adattófájlok
#XFLD: Available scoped roles label
availableRoles=Elérhető hatókörös szerepek
#XFLD: Selected scoped roles label
selectedRoles=Kiválasztott hatókörös szerepek
#XCOL: Spaces table-view column models
models=Modellek
#XCOL: Spaces table-view column users
users=Felhasználók
#XCOL: Spaces table-view column connections
connections=Kapcsolatok
#XFLD: Section header overview in space detail
overview=Áttekintés
#XFLD: Section header Apache Spark in space detail
sparkManagementSection=Apache Spark
#XFLD: Section header Apache Spark in space detail
sparkManagementSubSection=Alkalmazások
#XFLD: Section header Apache Spark in space detail
taskAssignmentSubSection=Feladat-hozzárendelés
#XFLD: vCPU label in Apache Spark section
vCPULabel=vCPU-k
#XFLD: Memory label in Apache Spark section
memoryLabel=Memória (GB)
#XFLD: space configuration label in Apache Spark section
spaceConfigurationLabel=Térkonfiguráció
#XFLD: Space Source label
sparkApplicationLabel=Alkalmazás
#XFLD: Cluster Size label
clusterSizeLabel=Klaszterméret
#XFLD: Driver label
driverLabel=Vezérlő
#XFLD: Executor label
executorLabel=Végrehajtó
#XFLD: max label
maxLabel=Felhasznált max.
#XFLD: TrF Default label
trFDefaultLabel=Átalakítási folyamat alapértelmezése
#XFLD: Merge Default label
mergeDefaultLabel=Alapértelmezés összevonása
#XFLD: Optimize Default label
optimizeDefaultLabel=Alapértelmezés optimalizálása
#XFLD: Deployment Default label
deploymentDefaultLabel=Helyi tábla (fájl) üzembe helyezése
#XFLD: max spark source label
driverExecutorMaxUsedSparkSource={0} CPU / {1} GB
#XFLD: Object type label
taskObjectTypeLabel=Objektumtípus
#XFLD: Task activity label
taskActivityLabel=Tevékenység
#XFLD: Task Application ID label
taskApplicationIDLabel=Alapértelmezett alkalmazás
#XFLD: Section header in space detail
generalSettings=Általános beállítások
#XMSG: Message strip message when a space is locked
spaceLockedMessageStrip=Ezt a teret jelenleg a rendszer zárolja.
#XMSG: Message strip message deploy warning
deployWarningMessageStrip=E szakasz módosításai azonnal üzembe lesznek helyezve.
#XMSG: Message strip message modify space configuration warning
modifySpaceConfigurationMessageStrip=Az értékek módosítása teljesítményproblémákat okozhat.
#XFLD: Button text to unlock the space again
unlockSpace=Tér feloldása
#XFLD: Info text for audit log formatted message
auditLogText=Engedélyezze az auditnaplókat az olvasási és módosítási műveletek (auditszabályzatok) rögzítéséhez. Ezután az adminisztrátorok elemezhetik, ki és mikor milyen műveletet hajtott végre.
#XMSG: Message strip  audit warning
auditLogWarningMessageStrip=Az auditnaplók nagy mennyiségű lemezterületet használhatnak fel a bérlőjében. Ha engedélyez egy auditszabályzatot (olvasási vagy módosítási műveletek), rendszeresen ellenőrizze a lemezterület-felhasználást (a Rendszerfigyelő Felhasznált lemezes tároló kártyáján), hogy elkerülje a lemez teljes betelését, ami fennakadásokat okozhat. Ha letiltja az auditszabályzatot, törlődnek a hozzá tartozó auditnapló-bejegyzések. Ha meg szeretné tartani, exportálja őket az auditszabályzat letiltása előtt.
#XFLD: Message strip link text to open in app help to guide user what to do to unlock his space permanently
unlockSpaceHelp=Súgó megjelenítése
#XMSG: Message strip message when a space will be locked again
spaceTemporarilyUnlockedMessageStrip=A tér túllépi a tértárolójának mennyiségét, és {0} {1} múlva zárolódik.
#XMSG: Unit for remaining time until space is locked again
hours=óra
#XMSG: Unit for remaining time until space is locked again
minutes=perc
#XFLD: Section header in space detail for auditing
detailsSectionAuditing=Auditálás
#XFLD: Subsection header in space detail for auditing
auditing=Téraudit beállításai
#XFLD: Hot space tooltip
hotSpaceCountTooltip=Kritikus állapotú terek: a tároló több mint 90%-a felhasználva
#XFLD: Green space tooltip
greenSpaceCountTooltip=Jó állapotú terek: a tároló 6–90%-a felhasználva
#XFLD: Cold space tooltip
coldSpaceCountTooltip=Kevéssé használt ("hideg") terek: a tároló kevesebb mint 5%-a felhasználva
#XFLD: Critical space tooltip
criticalSpaceCountTooltip=Kritikus állapotú terek: a tároló több mint 90%-a felhasználva
#XFLD: Green space tooltip
okSpaceCountTooltip=Jó állapotú terek: a tároló 6–90%-a felhasználva
#XFLD: Locked space tooltip
lockedSpaceCountTooltipNew=Zárolt terek: túl kevés memória miatt zárolva
#XFLD: Locked space tooltip
lockedSpaceCountTooltip=Zárolt terek
#YMSE: Error while deleting remote source
deleteRemoteError=A kapcsolatok eltávolítása nem sikerült.
#XFLD: Space ID input tooltip
spaceIDInfoTooltip=A térazonosító később nem módosítható.\nÉrvényes karakterek: A - Z, 0 - 9 és _
#YMSE: Error when space name is empty
spaceNameEmptyValidationError=Írja be a tér nevét.
#YMSE: Error when ECN Business name is empty
ecnNameEmptyValidationError=Írjon be üzleti nevet.
#YMSE: Error when space id is empty
spaceIDInputEmptyValidationError=Írja be a tér azonosítóját.
#YMSE: Error when space id contains invalid characters
spaceIDInputInValidError=Érvénytelen karakterek. Használható karakterek: A - Z, 0 - 9, és _.
#YMSE: Error when space id exists while creation
spaceIDInputDuplicateError=Már van ilyen térazonosító.
#XFLD: Space searchfield placeholder
search=Keresés
#XMSG: Success message after creating users
createUsersSuccess=A felhasználók hozzáadva
#XMSG: Success message after creating user
createUserSuccess=A felhasználó hozzáadva
#XMSG: Success message after updating users
updateUsersSuccess={0} felhasználó frissítve
#XMSG: Success message after updating user
updateUserSuccess=A felhasználó frissítve
#XMSG: Success message after removing users
removeUsersSuccess={0} felhasználó eltávolítva
#XMSG: Success message after removing user
removeUserSuccess=A felhasználó eltávolítva
#XFLD: Schema name
schemaName=Séma neve
#XFLD: used of total
ofTemplate={0} / {1}
#XFLD: Assigned disk label in space overview
assignedStorageTemplate=Hozzárendelt lemezterület ({0} / {1})
#XFLD: Assigned Memory label in space overview
assignedRAMTemplate=Hozzárendelt memória ({0} / {1})
#XFLD: Storage ratio on space
accelearationRAM=Memóriagyorsítás
#XFLD: No Storage Consumption
noStorageConsumptionText=Nincs hozzárendelve tárolókvóta.
#XFLD: Used disk label in space overview
usedStorageTemplate=Tárolóként használt lemez ({0} / {1})
#XFLD: Used Memory label in space overview
usedRAMTemplate=Tárolóként használt memória ({0} / {1})
#XFLD: Used Disk tooltip
usedStorageTooltipTemplate={0} /{1} tárolóként használt lemez
#XFLD: Used Memory tooltip
usedRAMTooltipTemplate={0} / {1} memória felhasználva
#XFLD: Assigned Disk tooltip
assignedStorageTooltipTemplate={0} / {1} lemezterület hozzárendelve
#XFLD: Assigned Memory tooltip
assignedRAMTooltipTemplate={0} / {1} memória hozzárendelve
#XFLD: Space related used storage tooltip
spaceRelatedUsedStorageTooltipTemplate=Téradatok: {0}
#XFLD: Not space related used storage tooltip
otherDataUsedStorageTooltipTemplate=Egyéb adatok: {0}
#XFLD: Storage used exceeded popover and tooltip
storageUsedExceededText=Érdemes lehet nagyobb díjcsomagra váltania, vagy forduljon az SAP támogatószolgálatához.
#XCOL: Space table-view column used Disk
usedStorage=Tárolóként használt lemez
#XCOL: Space monitor column used Memory
usedRAM=Tárolóként használt memória
#XCOL: Space monitor column Schema
tableSchema=Séma
#XCOL: Space monitor column Storage Type
tableStorageType=Tárolótípus
#XCOL: Space monitor column Table Type
tableType=Táblatípus
#XCOL: Space monitor column Record Count
tableRecordCount=Rekordok száma
#XFLD: Assigned Disk
assignedStorage=Tárolóként hozzárendelt lemez
#XFLD: Assigned Memory
assignedRAM=Tárolóként hozzárendelt memória
#XCOL: Space table-view column storage utilization
tableStorageUtilization=Felhasznált tárolómennyiség
#XFLD: space status
spaceStatus=Tér állapota
#XFLD: space type
spaceType=Tér típusa
#XFLD: space type label for space with space type "SAP BW bridge" ("SAP BW bridge" is a product name, do not translate it)
abapbridgeSpaceType=SAP BW-híd
#XFLD: space type label for space with space type "Data Marketplace"
marketplaceSpaceType=Adatszolgáltatói termék
#XMSG: Error message for when selected space cannot be deleted due to the space type
deleteTypedSpaceError=Nem törölheti a(z) {0} teret, mert a tér típusa {1}.
#XMSG: Error message for when selected spaces cannot be deleted due to the space type
deleteTypedSpacesError=Nem törölheti a kijelölt {0} teret. A következő tértípusba tartozó tereket nem lehet törölni: {1}.
#XFLD: space type label for space without space type
defaultSpaceType=SAP Datasphere
#XFLD: Tooltip for monitor space button
monitorSpace=Figyelő
#XFLD: Tooltip for edit space button
editSpace=Tér szerkesztése
#XMSG: Deletion warning in messagebox
deleteConfirmation=Biztosan törli ezt a teret?
#XFLD: Tooltip for delete space button
deleteSpace=Tér törlése
#XFLD: storage
storage=Tárolólemez
#XFLD: username
userName=Felhasználónév
#XFLD: port
port=Port
#XFLD: hostname
hostName=Állomásnév
#XFLD: password
password=Jelszó
#XBUT: Request new password button
requestPassword=Új jelszó kérése
#YEXP: Usage explanation in time data section
timeDataSectionHint=Készítsen időtáblákat és -dimenziókat a modellekhez és sztorikhoz.
#YEXP: Usage explanation in Space schema section
spaceSchemaHint=Szeretné, hogy más eszközökkel vagy alkalmazásokkal is fel lehessen használni a térben lévő adatokat? Ha igen, hozzon létre egy vagy több olyan felhasználót, aki hozzáférhet a tér adataihoz, és válassza ki, hogy a tér jövőbeli adatai alapértelmezés szerint felhasználhatók-e.
#XTIT: Create schema popup title
createSchemaDialogTitle=Open SQL-séma létrehozása
#XTIT: Generate Time Data dialog title
generateTimeDataDialogTitle=Időtáblák és -dimenziók létrehozása
#XTIT: Edit Time Data dialog title
editTimeDataDialogTitle=Időtáblák és -dimenziók szerkesztése
#XTIT: Time Data token title
timeDataTokenTitle=Időadatok
#XTIT: Time Data token title
timeDataUpdateViews=Időadatnézetek frissítése
#XFLD: Time Data token creation in progress label
timeDataCreationInProgress=Létrehozás folyamatban...
#XFLD: Time Data token creation error label
timeDataCreationError=A létrehozás nem sikerült. Próbálkozzon újra.
#XTIT: Time Data dialog time settings title
timeDataDialogTimeSettingsTitle=Időtábla beállításai
#XTIT: Time Data dialog time data table title
timeDataDialogTranslationTablesTitle=Fordítási táblák
#XTIT: Time Data dialog dimensions title
timeDataDialogTimeViewsTitle=Idődimenziók
#XFLD: Time Data dialog time range label
timeRangeHint=Adja meg az időintervallumot.
#XFLD: Time Data dialog time data table label
timeDataHint=Nevezze el a táblát.
#XFLD: Time Data dialog time data dimensions label
timeDimensionsHint=Nevezze el a dimenziókat.
#XFLD: Time Data Time range description label
timerangeLabel=Időintervallum
#XFLD: Time Data dialog from year label
fromYearLabel=Kezdő év
#XFLD: Time Data dialog to year label
toYearLabel=Záró év
#XFLD: Time Data dialog calendar type label
calendarTypeLabel=Naptár típusa
#XFLD: Time Data dialog granularity label
granularityLabel=Granularitás
#XFLD: Time Data dialog technical name label
technicalNameLabel=Technikai név
#XFLD: Time Data dialog quarters text table lable
quarterTextsTableLabel=Fordítási tábla negyedévekhez
#XFLD: Time Data dialog months text table lable
monthsTextsTableLabel=Fordítási tábla hónapokhoz
#XFLD: Time Data dialog days text table lable
daysTextsTableLabel=Fordítási tábla napokhoz
#XFLD: Time Data dialog year label
yearLabel=Év dimenzió
#XFLD: Time Data dialog quarter label
quarterLabel=Negyedév dimenzió
#XFLD: Time Data dialog month label
monthLabel=Hónap dimenzió
#XFLD: Time Data dialog day label
dayLabel=Nap dimenzió
#XFLD: Time Data dialog gregorian calendar type label
gregorian=Gergely-naptár
#XFLD: Time Data dialog time granularity day label
day=Nap
#XFLD: Time Data dialog business name maxlength reached
timeRangeValueStateTextMaxLengthBusinessName=Elérte az 1000 karakteres maximális hosszt.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextMaxRangeExceeded=Az időintervallum legfeljebb 150 év lehet.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeLower=A kezdő évnek korábbinak kell lennie a záró évnél.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeHigher=A kezdő év nem lehet 1900-nál korábbi.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeHigher=A záró évnek későbbinek kell lennie a kezdő évnél.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeLower=A záró év nem lehet későbbi, mint az aktuális év plusz 100.
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningFromYear=A kezdő év későbbire állítása adatvesztést okozhat.
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningToYear=A záró év korábbira állítása adatvesztést okozhat.
#XMSG: Time Data creation validation error message
timeDataValidationError=Úgy tűnik, néhány mező érvénytelen. Ellenőrizze a kötelező mezőket a folytatáshoz.
#XMSG: Time Data deletion popup text
deleteTimeDataConfirmation=Biztosan törli az adatokat?
#XMSG: Time Data creation success message
createTimeDataSuccess=Az időadatok létrejöttek
#XMSG: Time Data update success message
updateTimeDataSuccess=Az időadatok frissültek
#XMSG: Time Data delete success message
deleteTimeDataSuccess=Az időadatok törölve
#XMSG: Time Data creation error message
createTimeDataError=Hiba történt az időadatok létrehozásakor.
#XMSG: Time Data update error message
updateTimeDataError=Hiba történt az időadatok frissítésekor.
#XMSG: Time Data creation error message
deleteTimeDataError=Hiba történt az időadatok törlésekor.
#XMSG: Error message when time data could not be retrieved
retrievingTimeDataError=Az időadatok betöltése nem sikerült.
#XTIT: Time Data deletion popup title
deleteTimeDataConfirmationTitle=Figyelmeztetés
#XTXT: Time Data deletion dependency popup text
deleteTimeDataDependencyText=Nem sikerült törölni az időadatokat, mert más modellek használják.
#XTXT: Time Data deletion dependency popup text singular
deleteTimeDataDependencyTextSingular=Nem sikerült törölni az időadatokat, mert egy másik modell használja.
#XMSG: Validation error when required time data form field is empty
timeDataRequiredField=Ezt a mezőt kötelező kitölteni, nem hagyható üresen.
#XFLD: Tooltip for Open in Database Explorer button
openInDBX=Megnyitás az Adatbázis-böngészőben
#YMSE: Dimension Year
dimensionYearView=Év dimenzió
#YMSE: Dimension Year
dimensionQuarterView=Negyedév dimenzió
#YMSE: Dimension Year
dimensionMonthView=Hónap dimenzió
#YMSE: Dimension Year
dimensionDayView=Nap dimenzió
#XFLD: Time Data deletion object title
timeDataUsedIn=({0} modellben használatos)
#XFLD: Time Data deletion object title singular
timeDataUsedInSingular=(1 modellben használatos)
#XFLD: Time Data deletion table column provider
provider=Szolgáltató
#XTIT: Time Data dependency dialog title
timeDataDependencyDialogTitle=Függőségek
#XTIT: Create schema popup title
createSpaceSchemaUserDialogTitle=Felhasználó létrehozása a térsémához
#XFLD: Create schema button
createSchemaButton=Open SQL-séma létrehozása
#XFLD: Generate TimeData button
generateTimeDataButton=Időtáblák és -dimenziók létrehozása
#XFLD: Show dependencies button
showDependenciesButton=Függőségek megjelenítése
#XFLD: tooltip for disabled buttons when user is not member of space
mustBeMemberOfSpaceForOperation=A művelet végrehajtásához a felhasználónevének a tér tagjának kell lennie.
#XFLD: Create space schema user button
createSpaceSchemaUserButton=Térséma-felhasználó létrehozása
#YMSE: API Schema users load error
loadSchemaUsersError=A felhasználólista betöltése nem sikerült.
#XTIT: API Schema-config popup title
schemaUserDetailsDialogTitle=Térséma-felhasználó részletei
#XMSG: API Schema user deletion popup text
deleteSpaceSchemaUserConfirmation=Biztosan törli a kijelölt felhasználót?
#XMSG: API Schema user deletion confirmation text
userDeleteSuccess=A felhasználó törölve.
#YMSE: API Schema user deletion error
userDeleteError=Nem sikerült törölni a felhasználót.
#XFLD: User deleted
userDeleted=A felhasználó törlődött.
#XTIT: Remove user popup title
removeUserConfirmationTitle=Figyelmeztetés
#XMSG: Remove user popup text
removeUserConfirmation=Biztosan eltávolítja a felhasználót? A felhasználó és a hozzárendelt hatókörös szerepei el lesznek távolítva a térből.
#XMSG: Remove users popup text
removeUsersConfirmation=Biztosan eltávolítja a felhasználókat? A felhasználók és a hozzárendelt hatókörös szerepeik el lesznek távolítva a térből.
#XBUT: Confirmation button for remove user dialog
removeUserConfirmationBtn=Eltávolítás
#YMSE: No data text for available roles
noDataAvailableRoles=A tér nincs hozzáadva hatókörös szerephez. \n Csak akkor adhat hozzá felhasználókat a térhez, ha előbb hozzáadja egy vagy több hatókörös szerephez.
#YMSE: No data text for selected roles
noDataSelectedRoles=Nincsenek kiválasztott hatókörös szerepek
#XTIT: Schema-config popup title
schemaDetailsDialogTitle=Open SQL-séma konfigurációs részletei
#XFLD: Label for Read Audit Log
auditLogRead=Auditnapló engedélyezése olvasási műveleteknél
#XFLD: Label for Change Audit Log
auditLogChange=Auditnapló engedélyezése módosítási műveleteknél
#XFLD: Label Audit Log Retention
auditLogRetention=Naplók megőrzési ideje
#XFLD: Label Audit Log Retention Unit
retentionUnit=nap
#YMSE: Error when log retention time is set incorrectly
incorrectRetentionTime=Adjon meg {0} és {1} közötti egész számot
#XFLD: Label for Cross Space Schema Access
spaceSchemaAccess=Térsémaadatok felhasználása
#XTIT: Confirm deactivation of space schema consumption dialog title
disableSpaceSchemaConsumptionDialogTitle=Térsémaadatok felhasználásának leállítása
#XTXT: Confirm deactivation of space schema consumption dialog text
disableSpaceSchemaConsumptionDialogText=Ez az Open SQL-séma felhasználhatja a térséma adatait. Ha leállítja a felhasználást, előfordulhat, hogy a térséma adatain alapuló modellek nem fognak működni.
#XBUT: Confirmation button for the stop consuming space schema data dialog
stopConsuming=Felhasználás leállítása
#XTXT: Tooltip for data lake icon
dataLakeIconTooltip=Ez a tér a data lake-hez való hozzáférésre használatos
#XFLD: Tooltip for data lake table entry
dataLakeTableEntryTooltip=Data lake engedélyezve
#XFLD: used In-Memory linit reached tag
usedRamLimitReachedLabel=Elérte a memóriára vonatkozó korlátot
#XFLD: Space storage Limit reached tag
storageLimitReachedLabel=Elérte a tárolóra vonatkozó korlátot
#XFLD: Minimal Storage Limit Reached tag
minStorageLimitReachedLabel=Elérte a tárolóra vonatkozó alsó korlátot
#XFLD: Space ram tag
ramLimitReachedLabel=Elérte a memóriára vonatkozó korlátot
#XFLD: Minimal In-Memory Limit Reached tag
minRamLimitReachedLabel=Elérte a memóriára vonatkozó alsó korlátot
#XFLD: Popover text after click on space storage limit warning
storageLimitReachedText=Elérte a térhez hozzárendelt tárolóra vonatkozó korlátot ({0}). Rendeljen hozzá több tárolót a térhez.
#XFLD: System storage tag
systemStorageLimitReachedLabel=Elérte a rendszertárolóra vonatkozó korlátot
#XMSG: Popover text after click on storage limit warning
systemStorageLimitReachedText=Elérte a rendszertárolóra vonatkozó korlátot ({0}). Most nem rendelhet hozzá több tárolót a térhez.
#XMSG: Schema deletion warning
deleteSchemaConfirmation=Az Open SQL-séma törlése esetén végleg törlődik az összes tárolt objektum és karbantartott társítás is a sémából. Folytatja?
#XMSG: Success message after deleting a schema
schemaDeleteSuccess=A séma törölve
#YMSE: Error while deleting schema.
schemaDeleteError=Nem sikerült törölni a sémát.
#XMSG: Success message after update a schema
schemaUpdateSuccess=A séma frissült
#YMSE: Error while updating schema.
schemaUpdateError=Nem sikerült frissíteni a sémát.
#XMSG: Hint when no password is shown in the dialog.
newPasswordHint=Megadtunk egy jelszót ehhez a sémához. Ha elfelejtette vagy elvesztette, kérhet újat. Ne felejtse el kimásolni vagy menteni az új jelszót.
#XMSG: Hint when showing the password in the dialog.
savePasswordHint=Másolja ki a jelszót. Szüksége lesz rá, amikor kapcsolatot állít be ehhez a sémához. Ha elfelejtette a jelszavát, ezen a párbeszédpanelen visszaállíthatja.
#XFLD,50: Hint on entering schema name in creation dialog
schemaNameHint=A név nem módosítható a séma létrehozása után.
#XFLD: Open SQL Schemas section sub headline
schemasSQL=Open SQL
#XFLD: Space schema section sub headline
schemasSpace=Tér
#XFLD: HDI Container section header
HDIContainers=HDI-tárolók
#XTXT: Add HDI Containers button tooltip
addHDIContainers=HDI-tárolók hozzáadása
#XTXT: Remove HDI Containers button tooltip
removeHDIContainers=HDI-tárolók eltávolítása
#XFLD: Enable HDI Containers feature button
enableHDIContainersButton=Hozzáférés engedélyezése
#YMSE: No data text for HDI Containers table
noDataHDIContainers=Nincs hozzáadva HDI-tároló.
#YMSE: No data text for Timedata section
noDataTimedata=Nem jöttek létre táblák és dimenziók.
#YMSE: No data text during runtime not available for Timedata section
runtimeNotAvailableNoDataTimedata=Nem lehet időtáblákat és -dimenziókat betölteni, mert nem érhető el a futásidejű adatbázis.
#YMSE: No data text during runtime not available for HDI section
runtimeNotAvailableNoDataHDI=Nem lehet HDI-tárolókat betölteni, mert nem érhető el a futásidejű adatbázis.
#YMSE: Error while fetching HDI Containers
fetchHDIContainersError=Nem sikerült beolvasni a HDI-tárolókat. Próbálkozzon újra később.
#XFLD Table column header for HDI Container names
HDIContainerName=HDI-tároló neve
#XFLD: Enable HDI Containers feature dialog title
enableHDIContainersDialogTitle=Hozzáférés engedélyezése
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText1New=Engedélyezheti az SAP SQL Data Warehousingot az SAP Datasphere-bérlőjében, hogy adatáthelyezés nélkül cserélhessen adatokat a HDI-tárolói és az SAP Datasphere-terei között.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText2=Ehhez küldjön be támogatáskérési jegyet az alábbi gombra kattintva.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText3New=A jegy feldolgozása után össze kell állítania egy vagy több új HDI-tárolót az SAP Datasphere futásidejű adatbázisában. Ezután a Hozzáférés engedélyezése gomb egy + gombra cserélődik az összes SAP Datasphere-terének HDI-tárolók szakaszában, és hozzáadhatja a tárolóit a térhez.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText41=További információt szeretne? Keresse fel ezt: %%0. Részletes információk arról, hogy mit kell megadni a jegyben: %%1.
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkText=SAP-súgó
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkTextSapNote3057059=3057059. számú SAP-jegyzet
#XBUT: Open Ticket Button Text
openTicket=Jegy beküldése
#XBUT: Add Button Text
add=Hozzáadás
#XBUT: Next Button Text
next=Tovább
#XBUT: Edit Button Text
editUsers=Szerkesztés
#XBUT: create user Button Text
createUser=Létrehozás
#XBUT: Update user Button Text
updateUser=Kiválasztás
#XTIT: Add HDI Containers Dialog Title
addHDIContainersDialogTitle=Hozzárendelés nélküli HDI-tárolók hozzáadása
#XFLD: No data text for add HDI container dialog
addHDIContainersDialogNoDataText=Nem találtunk hozzárendelés nélküli tárolókat. \n Lehet, hogy a keresett tároló már hozzá van rendelve egy térhez.
#YMSE: Error while loading assigned HDI Containers
loadAssignedHDIContainersError=Nem sikerült betölteni a hozzárendelt HDI-tárolókat.
#YMSE: Error while loading assigned HDI Containers
loadUnassignedHDIContainersError=Nem sikerült betölteni a HDI-tárolókat.
#XMSG: Success message
succeededToAddHDIContainer=A HDI-tároló hozzáadva
#XMSG: Success message
succeededToAddHDIContainerPlural=A HDI-tárolók hozzáadva
#XMSG: Success message
succeededToDeleteHDIContainer=A HDI-tároló eltávolítva
#XMSG: Success message
succeededToDeleteHDIContainerPlural=A HDI-tárolók eltávolítva
#XFLD: Time data section sub headline
timeDataSection=Időtáblák és -dimenziók
#XFLD: Schemas list entry read audit log enabled indicator
readIndicator=Olvasás
#XFLD: Schemas list entry change audit log enabled indicator
changeIndicator=Módosítás
#XFLD: Remote sources section sub headline
allconnections=Kapcsolat-hozzárendelés
#XFLD: Remote sources section sub headline
localconnections=Helyi kapcsolatok
#XFLD: Remote sources section sub headline for open Connectors
openConnectors=SAP Open Connectors
#XFLD: User section sub headline
memberassignment=Taghozzárendelés
#XFLD: User assignment section sub headline
userAssignment=Felhasználó-hozzárendelés
#XFLD: User section Access dropdown Member
member=Tag
#XFLD: User assignment section column name
user=Felhasználónév
#XTXT: Selected role count
selectedRoleToolbarText=Kiválasztva: {0}
#XTIT: Space detail section connections title
detailsSectionConnections=Kapcsolatok
#XTIT: Space detail section data access title
detailsSectionDataAccess=Sémahozzáférés
#XTIT: Space detail section time data title
detailsSectionGenerateData=Időadatok
#XTIT: Space detail section members title
detailsSectionUsers=Tagok
#XTIT: Space detail section Users title
detailsSectionUsersTitle=Felhasználók
#XTIT: Out of Storage
insufficientStoragePopoverTitle=Nincs több tároló
#XTIT: Storage distribution
storageDistributionPopoverTitle=Felhasznált lemezes tároló
#XTXT: Out of Storage popover text
insufficientStorageText=Új tér létrehozásához csökkentse a más terekhez hozzárendelt tároló mennyiségét, vagy töröljön egy szükségtelen teret. Növelheti is a rendszer teljes tárolómennyiségét a Díjcsomag kezelése funkcióval.
#XMSG: Space id length warning
spaceIdLengthWarning=Túllépte a(z) {0} karakteres maximális hosszt.
#XMSG: Space name length warning
spaceNameLengthWarning=Túllépte a(z) {0} karakteres maximális hosszt.
#XMSG: Space id reserved prefix warning
spaceIdnWarningReservedPrefix=Az esetleges ütközések elkerülése érdekében ne használja a(z) {0} előtagot.
#YMSE: Error while loading open SQL schemas
loadLocalSchemasError=Nem sikerült betölteni az Open SQL-sémákat.
#YMSE: Error while creating open SQL schema
createLocalSchemaError=Nem sikerült létrehozni az Open SQL-sémát.
#YMSE: Error while loading all remote connections
loadRemoteConnectionsError=Nem sikerült betölteni minden távoli kapcsolatot.
#YMSE: Error while loading space details
loadSpaceDetailsError=Nem sikerült betölteni a tér részleteit.
#YMSE: Error while deploying space details
deploySpaceDetailsError=Nem sikerült üzembe helyezni a teret.
#YMSE: Error while copying space details
copySpaceDetailsError=Nem sikerült másolni a teret.
#YMSE: Error while loading storage data
loadStorageDataError=Nem sikerült betölteni a tárolóadatokat.
#YMSE: Error while loading all users
loadAllUsersError=Nem sikerült betölteni az összes felhasználót.
#YMSE: Failed to reset password
resetPasswordError=Nem sikerült visszaállítani a jelszót.
#XMSG: Success message after updating space
resetPasswordSuccessMessage=A séma új jelszava beállítva
#YMSE: DP Agent-name too long
DBAgentNameError=Az adatátadási ügynök neve túl hosszú.
#YMSE: Schema-name not valid.
schemaNameError=A séma neve érvénytelen.
#YMSE: User name not valid.
UserNameError=A felhasználónév érvénytelen.
#XTIT: Consumption by Storage Type
consumptionStorageTypeText=Felhasználás tárolótípus szerint
#XTIT: Consumption by Schema
consumptionSchemaText=Felhasználás séma szerint
#XTIT: Overall Consumption by schema
overallConsumptionSchemaText=Teljes táblafelhasználás séma szerint
#XTIT: Overall Consumption by Table Type
overallConsumptionTableTypeText=Teljes felhasználás táblatípus szerint
#XTIT: Tables
tableDetailsText=Tábla részletei
#XTIT: Table Storage Consumption
tableStorageConsumptionText=Tábla tárolófelhasználása
#XFLD: Table Type label
tableTypeLabel=Táblatípus
#XFLD: Schema label
schemaLabel=Séma
#XFLD: reset table tooltip
resetTable=Tábla visszaállítása
#XFLD: In-Memory label in space monitor
inMemoryLabel=Memória
#XFLD: Disk label in space monitor
diskLabel=Lemez
#XFLD: Yes
yesLabel=Igen
#XFLD: No
noLabel=Nem
#XFLD: switch label: Want the data in this space to be consumable by default?
allowConsumptionByDefault=Szeretné alapértelmezés szerint felhasználhatóvá tenni a térben lévő adatokat?
#XFLD: Business Name
businessNameLabel=Üzleti név
#XFLD: Refresh
refresh=Frissítés
#XMSG: No filter results title
noFilterResultsTitle=Úgy tűnik, hogy a szűrőbeállítások nem eredményeznek adatokat.
#XMSG: No filter results message
noFilterResultsMsg=Próbálja pontosítani a szűrőbeállításokat. Ha ekkor sem kap adatokat, hozzon létre táblákat az Adatmodell-szerkesztőben. Amint felhasználnak tárolót, itt megfigyelheti őket.
#XMSG: Space monitoring Hana state red title
spaceMonitorHanaRedTitle=A futásidejű adatbázis nem érhető el.
#XMSG: Space monitoring Hana state red message
spaceMonitorHanaRedMsg=Mivel a futásidejű adatbázis nem érhető el, egyes funkciók le vannak tiltva, és nem jeleníthetők meg adatok ezen az oldalon.
#YMSE: Error while creating space schema user
createSpaceSchemaUserError=Nem sikerült létrehozni a térséma-felhasználót.
#YMSE: Error User name already exists
userAlreadyExistsError=Már van ilyen felhasználónév.
#YMSE: Error Authentication failed
authenticationFailedError=A hitelesítés nem sikerült.
#XMSG: Message strip message when a schema user is locked
userLockedMessageStrip=A rendszer túl sok sikertelen bejelentkezés miatt zárolta a felhasználót. A feloldásához kérjen új jelszót.
#XMSG: Success message after reset and unlock schema user
resetUnlockPasswordSuccessMessage=Az új jelszó beállítva és a felhasználó feloldva
#XMSG: user is locked message
userLockedMessage=A felhasználó zárolva van.
#XCOL: Users table-view column Role
spaceRole=Szerep
#XCOL: Users table-view column Scoped Role
spaceScopedRole=Hatókörös szerep
#XCOL: Users table-view column Space Admin
spaceAdmin=Téradminisztrátor
#XFLD: User section dropdown value Viewer
viewer=Megtekintő
#XFLD: User section dropdown value Modeler
modeler=Modellező
#XFLD: User section dropdown value Data Integrator
dataIntegrator=Adatintegrátor
#XFLD: User section dropdown value Space Administrator
spaceAdministrator=Téradminisztrátor
#XMSG: Success message -  Space Role updated successfully.
updateUserSpaceRoleSuccess=A térszerep frissítve
#XMSG: Error message - Failed to update Space Role.
updateUserSpaceRoleError=A térszerep frissítése nem sikerült.
#XFLD:
databaseUserNameSuffix=Adatbázis-felhasználónév utótagja
#XTXT: Space Schema password text
spaceSchemaPasswordText=A sémával való kapcsolat beállításához másolja ki a jelszavát. Ha elfelejti a jelszavát, bármikor kérhet újat.
#XFLD: Cloud Platform label in space schema dialog
cloudPlatformLabel=Cloud Platform
#XTXT: Cloud Platform text in space schema dialog
cloudPlatformText=A felhasználón keresztül megvalósuló hozzáférés beállításához engedélyezze felhasználást, és másolja a hitelesítő adatokat. Ha csak a jelszó nélkül tudja átmásolni a hitelesítő adatokat, ne feledje el utólag hozzáadni a jelszót.
#XFLD: enable consumption in CloudPlatform label in space schema dialog
enableConsumptionInCloudPlatformLabel=Cloud Platformon történő felhasználás engedélyezése
#XFLD: Credentials for User-Provided Service label in space schema dialog
cloudPlatformCredentialsLabel=Hitelesítő adatok felhasználó által nyújtott szolgáltatáshoz:
#XFLD: Credentials for User-Provided Service (Without Password) label in space schema dialog
cloudPlatformCredentialsWithoutPasswordLabel=Hitelesítő adatok felhasználó által nyújtott szolgáltatáshoz (jelszó nélkül):
#XFLD: Copy Credentials label in space schema dialog
copyCredentialsLabel=Hitelesítő adatok másolása a jelszó nélkül
#XFLD: Copy Credentials label in space schema dialog
copyFullCredentialsLabel=Minden hitelesítő adat másolása
#XFLD: Copy Password label in space schema dialog
copyPasswordLabel=Jelszó másolása
#XMSG: Credentials copied to clipboard
credentialsCopiedMessage=A hitelesítő adatok a vágólapra másolva
#XMSG: Password copied to clipboard
passwordCopiedMessage=A jelszó a vágólapra másolva
#XMSG: create database user dialog title
createDatabaseUserDialogTitle=Adatbázis-felhasználó létrehozása
#XMSG: Database Users section title
databaseUsers=Adatbázis-felhasználók
#XMSG: Database User Details dialog title
databaseUserDetailsDialogTitle=Adatbázis-felhasználó részletei
#XFLD: database user read audit log
databaseUserAuditLogRead=Olvasási műveletek auditnaplójának engedélyezése és a naplók megőrzési ideje
#XFLD: database user change audit log
databaseUserAuditLogChange=Módosítási műveletek auditnaplójának engedélyezése és a naplók megőrzési ideje
#XMSG: Cloud Platform Access
cloudPlatformAccess=Cloud Platform-hozzáférés
#XFLD: Enable Consumption in Cloud Platform hint
enableConsumptionCloudPlatformHint=Beállíthat ezen az adatbázis-felhasználón keresztül megvalósuló hozzáférést a HANA Deployment Infrastructure (HDI) tárolóhoz. Csak akkor lehet kapcsolódni a HDI-tárolóhoz, ha az SQL-modellezés be van kapcsolva.
#XFLD: Enable HDI Consumption
enableConsumptionCloudPlatform=HDI-felhasználás engedélyezése
#XFLD: Enable Consumption hint
enableConsumptionHint=Szeretné, hogy más eszközökkel vagy alkalmazásokkal is fel lehessen használni a térben lévő adatokat?
#XFLD: Enable Consumption
enableConsumption=SQL-felhasználás engedélyezése
#XFLD: Enable Modeling
enableModeling=SQL-modellezés engedélyezése
#XMSG: Privileges for Data Modeling
privilegesModeling=Adatbevitel
#XMSG: Privileges for Data Consumption
privilegesConsumption=Adatfelhasználás külső eszközökhöz
#XFLD: SQL Modeling
sqlModeling=SQL-modellezés
#XFLD: SQL Consumption
sqlConsumption=SQL-felhasználás
#XFLD: enabled
enabled=Engedélyezve
#XFLD: disabled
disabled=Letiltva
#XFLD: Edit Privileges
editPrivileges=Jogosultságok szerkesztése
#XFLD: Open Database Explorer
openDBX=Adatbázis-böngésző megnyitása
#XFLD: create database user hint
databaseCreateHint=A mentést követően már nem változtathatja meg a felhasználónevet.
#XFLD: Internal Schema Name
internalSchemaName=Belső sémanév
#YMSE: Failed to load database users
loadDatabaseUserError=Nem sikerült betölteni az adatbázis-felhasználókat
#YMSE: Failed to delete database user
deleteDatabaseUsersError=Nem sikerült törölni az adatbázis-felhasználókat
#XMSG: Success message after deleting database user
deleteDatabaseUserSuccess=Adatbázis-felhasználó törölve
#XMSG: Success message after deleting database users
deleteDatabaseUsersSuccess=Adatbázis-felhasználók törölve
#XMSG: Success message after creating database user
createDatabaseUserSuccessMessage=Adatbázis-felhasználó létrehozva
#YMSE: Failed to create database user
createDatabaseUserError=Nem sikerült létrehozni az adatbázis-felhasználót
#XMSG: Success message after updating database user
updateDatabaseUserSuccessMessage=Adatbázis-felhasználó frissítve
#YMSE: Failed to update database user
updateDatabaseUserError=Nem sikerült frissíteni az adatbázis-felhasználót
#XFLD: HDI Consumption
hdiConsumption=HDI-felhasználás
#XTIT: Space detail section Database Access title
detailsSectionDatabaseAccess=Adatbázis-hozzáférés
#XTXT: Database users consumption hint
databaseUsersConsumptionHint=Tegye alapértelmezés szerint felhasználhatóvá a térben lévő adatokat. A szerkesztőkben lévő modellek automatikusan engedélyezni fogják az adatok felhasználását.
#XFLD: Default Consumption of Space Data:
defaultConsumptionOfSpaceData=Téradatok alapértelmezett felhasználása:
#XFLD: Database User Name
databaseUserName=Adatbázis-felhasználónév
#XMSG: Database User creation validation error message
databaseUserValidationError=Úgy tűnik, néhány mező érvénytelen. Ellenőrizze a kötelező mezőket a folytatáshoz.
#XMSG: SQL modeling can’t be enabled since this user has been migrated.
migratedDatabaseUserHint=Az adatbetöltés nem engedélyezhető, mert ezt a felhasználót migrálták.
#XBUT: Remove Button Text
remove=Eltávolítás
#XBUT: Remove Spaces Button Text
removeSpaces=Terek eltávolítása
#XBUT: Remove Objects Button Text
removeObjects=Objektumok eltávolítása
#XMSG: No members have been added yet.
noMembersAssigned=Még nincsenek hozzáadva tagok.
#XMSG: No users have been added yet.
noUsersAssigned=Még nincsenek hozzáadva felhasználók.
#XMSG: No database users have been created or your filter is not showing any data.
noDatabaseUsers=Nem jött létre adatbázis-felhasználó, vagy nincs a szűrőnek megfelelő adat.
#XMSG: Please enter a user name.
noDatabaseUsername=Adja meg a felhasználónevet.
#XMSG: The user name is too long. Please use a shorter one.
databaseUsernameTooLong=A felhasználónév túl hosszú. Válasszon rövidebb nevet.
#XMSG: No privileges have been enabled and this database user will have limited functionality. Do you still want to continue?
noPrivilegesConfirmationMessage=Nincs megadva jogosultság, és ez az adatbázis-felhasználó csak korlátozottan lesz működőképes. Mégis folytatja?
#XMSG: To enable audit logs for change operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogChangeMessage=Ahhoz, hogy a módosítási műveleteknél engedélyezettek legyenek az auditnaplók, engedélyezni kell az adatbetöltést is. Ezt szeretné?
#XMSG: To enable audit logs for read operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogReadMessage=Ahhoz, hogy az olvasási műveleteknél engedélyezettek legyenek az auditnaplók, engedélyezni kell az adatbetöltést is. Ezt szeretné?
#XMSG: To enable HDI consumption data ingestion and data consumption needs to be enabled. Do you want to enable all?
enableHDIConsumptionMessage=A HDI-felhasználás engedélyezéséhez engedélyezni kell az adatbetöltést és az adatfelhasználást is. Ezt szeretné?
#XMSG:
databaseUserPasswordText=Az adatbázis-felhasználóval való kapcsolat beállításához másolja ki a jelszavát. Ha elfelejti a jelszavát, bármikor kérhet újat.
#XTIT: Space detail section members title
detailsSectionMembers=Tagok
#XMSG: New password set
newPasswordSet=Új jelszó beállítva
#XFLD: Data Ingestion
dataIngestion=Adatbetöltés
#XFLD: Data Consumption
dataConsumption=Adatfelhasználás
#XFLD: Privileges
privileges=Jogosultságok
#XFLD: Enable Data ingestion
enableDataIngestion=Adatbetöltés engedélyezése
#XMSG: Log the read and change operations for data ingestion.
databaseUserAuditHint=Az adatbetöltéshez kapcsolódó olvasási és módosítási műveletek naplózása.
#XMSG: Make your space data available in your HDI containers.
databaseUserHDIHint=A térben lévő adatok elérhetővé tétele a HDI-tárolókban.
#XFLD: Enable Data consumption
enableDataConsumption=Adatfelhasználás engedélyezése
#XMSG: Allow other apps or tools to consume your space data.
enableDataConsumptionHint=A térben lévő adatok felhasználásának engedélyezése más alkalmazások vagy eszközök számára.
#XMSG: To set up access via this schema user copy the credentials to your user-provided service. In case you can only copy the credentials without a password make sure to add the password later.
databaseUserHDIConsumptionHint=Az adatbázis-felhasználón keresztül megvalósuló hozzáférés beállításához másolja át a hitelesítő adatokat a felhasználó által nyújtott szolgáltatásba. Ha csak a jelszó nélkül tudja átmásolni a hitelesítő adatokat, ne feledje el utólag hozzáadni a jelszót.
#XFLD: Data Flow Runtime Capacity label in space overview
dataFlowRuntimeCapacity=Adatáramlás futásidejű kapacitása ({0}:{1} {2} órából)
#XFLD: Could not load Data Flow Runtime Capacity
dataFlowRuntimeCapacityError=Nem sikerült betölteni az adatáramlás futásidejű kapacitását
#XMSG: User can grant data consumption to other users.
databaseUserConsumptionWithGrantOptionHint=A felhasználó adhat adatfelhasználási jogot más felhasználóknak.
#XFLD: Enable data consumption with grant option
enableConsumptionWithGrantOption=Adatfelhasználás engedélyezése átadási lehetőséggel
#XMSG: To enable data consumption with grant option data consumption needs to be enabled. Do you want to enable both?
enableConsumptionWithGrantMessage=Engedélyezni kell az adatfelhasználást ahhoz, hogy engedélyezhető legyen az adatfelhasználás átadási lehetőséggel. Engedélyezi mindkettőt?
#XFLD: Enable Automated Predictive Library (APL) and Predictive Analysis Library (PAL)
allowUsingScriptServerFunctionality=Automated Predictive Library (APL) és Predictive Analysis Library (PAL) engedélyezése
#XMSG: User can use SAP HANA Cloud embedded machine learning functions.
allowUsingScriptServerFunctionalityHint=A felhasználó használhatja az SAP HANA Cloud beépített gépi tanulási funkcióit.
#XFLD: Password Policy
passwordPolicy=Jelszószabályok
#XMSG: Password Policy
passwordPolicyHint=Itt engedélyezheti vagy tilthatja le a beállított jelszószabályokat.
#XFLD: Enable Password Policy
enablePasswordPolicy=Jelszószabályok engedélyezése
#XMSG: Read Access to the Space Schema
readAccessTitle=Olvasási hozzáférés a térsémához
#XMSG: read access hint
readAccessHint=Ennek segítségével az adatbázis-felhasználó külső eszközöket csatlakoztathat a térsémához, olvashatja a felhasználásra elérhetővé tett nézeteket.
#XFLD: Space Schema
spaceSchema=Térséma
#XFLD: Enable Read Access (SQL)
enableReadAccess=Olvasási hozzáférés engedélyezése (SQL)
#XMSG: Allow the user to grant read access to other users.
withGrantOptionHint=Annak engedélyezése, hogy a felhasználó olvasási hozzáférést adjon más felhasználóknak.
#XFLD: With Grant Option
withGrantOption=Jogosultságadási lehetőséggel
#XMSG: Make your space data available in your HDI containers.
enableHDIConsumptionHint=A térben lévő adatok elérhetővé tétele a HDI-tárolókban.
#XFLD: Enable HDI Consumption
enableHDIConsumption=HDI-felhasználás engedélyezése
#XMSG: Write Access to the User’s Open SQL Schema
writeAccessTitle=Olvasási hozzáférés a felhasználó Open SQL-sémájához
#XMSG: write access hint
writeAccessHint=Annak engedélyezése az adatbázis-felhasználó számára, hogy külső eszközöket csatlakoztasson a felhasználó Open SQL-sémájához, hogy adatentitásokat és beolvasott adatokat hozhasson létre a térben való használatra.
#XFLD: Open SQL Schema
openSQLSchema=Open SQL-séma
#XFLD: Enable Write Access (SQL DDL & DML)
enableWriteAccess=Írási hozzáférés engedélyezése (SQL, DDL és DML)
#XMSG: audit hint
auditHint=Az olvasási és módosítási műveletek naplózása az Open SQL-sémában.
#XMSG: data consumption hint
dataConsumptionHint=Alapértelmezés szerint a tér összes új nézetének elérhetővé tétele felhasználásra. A modellezők felülírhatják ezt a beállítást egy-egy nézet vonatkozásában a nézet kimeneti oldalpanelének Elérhetővé tétel felhasználásra kapcsolójával. Azt is kiválaszthatja, hogy a nézetek milyen formátumokban legyenek elérhetők.
#XFLD: Expose for Consumption by Default
exposeDataConsumption=Alapértelmezett elérhetővé tétel felhasználásra
#XMSG: database users hint consumption hint
databaseUsersHint2New=Adatbázis-felhasználók létrehozása külső eszközök SAP Datasphere-hez való csatlakoztatásához. Jogosultságok beállításával engedélyezheti a felhasználóknak a téradatok olvasását, illetve adatentitások (DDL) és beolvasott adatok (DML) létrehozását a térben való használatra.
#XFLD: Read
read=Olvasás
#XFLD: Read (HDI)
readHDI=Olvasás (HDI)
#XFLD: Write
write=Írás
#XMSG: HDI Containers Hint
HDIContainersHint2=Hozzáférés biztosítása a térben lévő SAP HANA Deployment Infrastructure- (HDI-) tárolókhoz. A modellezők nézetek forrásaként használhatják a HDI-műtermékeket, a HDI-kliensek pedig hozzáférhetnek a tér adataihoz.
#XMSG: Open info dialog
openDatabaseUserInfoDialog=Információs párbeszédpanel megnyitása
#XMSG: Database user is locked. Open info dialog to unlock
openLockedDatabaseUserInfoDialog=Az adatbázis-felhasználó zárolt. A feloldásához nyissa meg a párbeszédpanelt
#XFLD: Table
table=Tábla
#XFLD: Space section partner configuration title
partnerConnectionConfigurationSectionTitle=Partneri kapcsolat
#XFLD: Space section partner configuration subtitle
partnerConnectionConfigurationSectionSubTitle=Partneri kapcsolat konfigurálása
#XFLD: Space section partner configuration hint
partnerConnectionConfigurationSectionHint=Az iFrame-URL-cím és ikon hozzáadásával határozza meg a partneri kapcsolat csempéjét. Ez a konfiguráció csak ennél a bérlőnél érhető el.
#XFLD: Table Name Field
partnerConnectionConfigurationName=Csempenév
#XFLD: Table iFrame URL field
partnerConnectionConfigurationiFrameURL=iFrame-URL-cím
#XFLD: Table iFrame Post Message Origin field
partnerConnectionConfigurationiFramePostMessageOrigin=iFrame Post-üzenet eredete
#XFLD: Table icon/image name
partnerConnectionConfigurationIcon=Ikon
#XMSG: Table no result found
partnerConnectionConfigurationNotfound=Nem találhatók partnerikapcsolat-konfigurációk.
#XMSG: Partner Connection Confgurations are not available
partnerConnectionConfigurationNotAvailable=A partnerikapcsolat-konfigurációk nem jeleníthetők meg, ha nem érhető el a futásidejű adatbázis.
#XFLD: Dialog Create Partner Connection Configuration
partnerConnectionConfigurationDialogTitleAdd=Partnerikapcsolat-konfiguráció létrehozása
#XFLD: Dialog Create Partner Connection Configuration Upload Icon
partnerConnectionConfigurationButtonUloadPartnerIcon=Ikon feltöltése
#XFLD: Dialog Create Partner Connection Configuration Select
partnerConnectionConfigurationSelectIcon=Kiválasztás (legfeljebb 200 kB)
#XFLD: Dialog Create Partner Connection Configuration Enter a name
partnerConnectionConfigurationNamePlaceholder=Partnercsempe-példa
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL
partnerConnectionConfigurationiFrameURLPlaceholder=http://www.example.com
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL Check
partnerConnectionConfigurationiFramePostMessageOriginPlaceholder=.example.com
#XFLD: Dialog Create Partner Connection Configuration Browse
partnerConnectionConfigurationBrowseButton=Tallózás
#XFLD: Create Partner Connection Configuration Msg
partnerConnectionConfigurationUploadMsg=A partnerikapcsolat-konfiguráció sikeresen létrejött.
#XMSG: Error while deleting Partner Connection configuration(s)
partnerConnectionConfigurationDeleteError=Hiba történt a partnerikapcsolat-konfiguráció(k) törlésekor.
#XMSG: Partner Connection Configuration has been successfully deleted
partnerConnectionConfigurationDeleteSuccess=A partnerikapcsolat-konfiguráció sikeresen törölve.
#XMSG: Error while retrieving Partner Connection Configurations
partnerConnectionConfigurationLoadError=Hiba történt a partnerikapcsolat-konfigurációk lehívásakor.
#XMSG: Error fileSizeLimitExceeded
partnerConnectionConfigurationfileSizeLimitExceeded=Nem sikerült feltölteni a fájlt, mert meghaladja a 200 kB-os maximális méretet.
#XBUT: Create Partner Connection Configuration
partnerConnectionConfigurationButtonCreate=Partnerikapcsolat-konfiguráció létrehozása
#XBUT: Delete Partner Connection Configuration
partnerConnectionConfigurationButtonDelete=Partnerikapcsolat-konfiguráció törlése
#XMSG: Partner Tile Creation Failure
upsertPartnerTileFailed=A partnercsempe létrehozása nem sikerült.
#XMSG: Partner Tile Deletion Failure
deletePartnerTileFailed=A partnercsempe törlése nem sikerült.
#XMSG: HANA cloud connector settings reset failure
resetHANASCCSettingsFailed=Az SAP HANA Cloud Connector ügyfélbeállításainak visszaállítása nem sikerült
#XFLD: Workload Class
workloadClass=Munkaterhelés-osztály
#XFLD: Workload Management
workloadManagement=Munkaterhelés kezelése
#XFLD: Priority
workloadClassPriority=Prioritás
#XMSG:
workloadManagementPriorityHint=Itt adhatja meg a térnek az adatbázis lekérdezésekor alkalmazott prioritását. A megadható érték 1 (legalacsonyabb prioritás) és 8 (legmagasabb prioritás) között lehet. Abban az esetben, ha a terek versengenek az elérhető szálakért, a magasabb prioritású terek az alacsonyabb prioritásúak előtt futnak.
#XMSG:
workloadClassPriorityHint=A tér prioritását 0-tól (legalacsonyabb) 8-ig (legmagasabb) adhatja meg. A legmagasabb prioritású tér utasításai előbb lesznek végrehajtva a többi, alacsonyabb prioritású tér utasításainál. Az alapértelmezett prioritás az 5-ös. A 9-es prioritásérték a rendszerműveletek számára van fenntartva, ezért a terek számára nem érhető el.
#XFLD: Statement Limits
workloadclassStatementLimits=Utasításkorlátok
#XFLD: Workload Configuration
workloadConfiguration=Munkaterhelés konfigurálása
#XMSG:
workloadClassStatementLimitsHint=Megadhatja, hogy a térben egyidejűleg futó utasítások legfeljebb hány darab (vagy százaléknyi) szálat és GB-ot használhatnak fel. Bármilyen értéket vagy százalékot megadhat 0-tól (nincs korlát) a bérlőben elérhető teljes memória- és szálmennyiségig. \n\n Ha szálkorlátot állít be, ne feledje, hogy ez csökkentheti a teljestményt. \n\n Ha memóriakorlátot ad meg, akkor az azt elérő utasítások nem lesznek végrehajtva.
#XMSG:
workloadClassStatementLimitsDescription=Az alapértelmezett konfiguráció bőséges erőforráskorlátokat biztosítanak, miközben meggátolják, hogy bármely tér önmagában túlterhelje a rendszert.
#XMSG:
workloadClassStatementLimitCustomDescription=Megadhatja, hogy a térben párhuzamosan futó kifejezések maximálisan mennyi szálat és memóriát használhatnak.
#XMSG:
totalStatementThreadLimitHelpText=Ha túl alacsonyra állítja a szálak korlátját, akkor csökkenhet az utasítások teljesítménye, ha pedig túl magas vagy 0 értéket ad meg, akkor a tér felhasználhatja a rendszerben elérhető összes szálat.
#XMSG:
totalStatementMemoryLimitHelpText=Ha túl alacsonyra állítja a memóriakorlátot, akkor kevés memória miatti problémák merülhetnek fel, ha pedig túl magas vagy 0 értéket ad meg, akkor a tér felhasználhatja a rendszerben elérhető összes memóriát.
#XMSG:
totalStatementThreadLimitHelpTextRestricted=Adja meg a bérlőben összesen elérhető szálak hányadát  1% és 70% közötti százalékértékként (vagy azzal egyenértékű számértékként). Ha túl alacsonyra állítja a szálkorlátot, akkor lelassulhat az utasítások végrehajtása, a túl nagy értékek pedig a más terekben végrehajtott utasítások teljesítményére lehetnek hatással.
#XMSG:
totalStatementThreadLimitHelpTextDynamic=Adja meg a bérlőben összesen elérhető szálak hányadát 1% és {0}% közötti százalékértékként (vagy azzal egyenértékű számértékként). Ha túl alacsonyra állítja a szálkorlátot, akkor lelassulhat az utasítások végrehajtása, a túl nagy értékek pedig a más terekben végrehajtott utasítások teljesítményére lehetnek hatással.
#XMSG:
totalStatementMemoryLimitHelpTextNew=Adjon meg 0 (korlátlan) és a bérlőben elérhető összes memória közötti értéket vagy százalékot. Ha túl alacsonyra állítja a memóriakorlátot, akkor lelassulhat az utasítások végrehajtása, a túl nagy értékek pedig a más terekben végrehajtott utasítások teljesítményére lehetnek hatással.
#XFLD: Total Statement Thread Limit
totalStatementThreadLimit=Utasítások végrehajtásiszál-korlátja
#XFLD: Segmented button label
workloadclassPercentage=%
#XFLD: Segmented button label
workloadclassGigaByte=GB
#XFLD, 80: Segmented button label
workloadclassThreads=Szálak
#XFLD: Total Statement Memory Limit
totalStatementMemoryLimit=Utasítások memóriakorlátja
#YMSE: Error while loading customer hana info
loadCustomerHanaInfoError=Nem sikerült betölteni az SAP HANA-ügyfélinfót.
#XMSG:
minimumLimitReached=Elérte az alsó korlátot.
#XMSG:
maximumLimitReached=Elérte a felső korlátot.
#XMSG: Name Taken for Technical Name
technical-name-taken=Már van kapcsolat a megadott technikai névvel. Adjon meg más nevet.
#XMSG: Name Too long for Technical Name
technical-name-too-long=A megadott technikai név 40 karakternél hosszabb. Adjon meg rövidebb nevet.
#XMSG: Technical name field empty
technical-name-field-empty=Adjon meg technikai nevet.
#XMSG: Generic error when user enters invalid characters for a field
field-invalid-characters=A név csak betűket (a-z), számokat (0-9) és aláhúzásjeleket (_) tartalmazhat.
#XMSG: Generic error when user enters invalid initial characters for a field
field-underscore-trim=A megadott név nem kezdődhet és nem is végződhet aláhúzásjellel (_).
#XFLD: Checkbox label for Enable Statement Limits
enableStatementLimits=Utasításkorlátok engedélyezése
#XFLD: Connection SubSection Settings Title in space details
connectionSettings=Beállítások
#XMSG: Connections tool hint in Space details section
connectionsToolHint=Kapcsolatok létrehozásához vagy szerkesztéséhez nyissa meg a Kapcsolatok alkalmazást az oldalsó navigációs panelről, vagy kattintson ide:
#XBUT: Connection list Open Connection Tool Button
button-connectiontool=Kapcsolatok megnyitása
#XFLD: Not deployed label on space tile
notDeployedLabel=A tér még nincs üzembe helyezve.
#XFLD: Not deployed additional text on space tile
notDeployedText=Helyezze üzembe a teret.
#XFLD: Corrupt space label on space tile
corruptSpace=Hiba történt.
#XFLD: Corrupt space additional text on space tile
corruptSpaceHint=Helyezze újra üzembe, vagy forduljon a támogatószolgálathoz.
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Auditnapló-adatok
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Adminisztrációs adatok
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Egyéb adatok
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Terekben lévő adatok
#XMSG: Unlock Space confirmation text
unlockSpaceConfirmation=Biztosan feloldja a teret?
#XMSG: Lock Space confirmation text
lockSpaceConfirmation=Biztosan zárolja a teret?
#XFLD: Lock
lock=Zárolás
#XFLD: Unlock
unlock=Feloldás
#XFLD: Locking
locking=Zárolás
#XMSG: Success message after locking space
lockSpaceSuccessMsg=A tér zárolva
#XMSG: Success message after unlocking space
unlockSpaceSuccessMsg=A tér feloldva
#XMSG: Success message after locking spaces
lockSpacesSuccessMsg=A terek zárolva
#XMSG: Success message after unlocking spaces
unlockSpacesSuccessMsg=A terek feloldva
#YMSE: Error while locking a space
lockSpaceError=A tér nem zárolható.
#YMSE: Error while unlocking a space
unlockSpaceError=A tér nem oldható fel.
#XTIT: popup title Warning
confirmationWarningTitle=Figyelmeztetés
#XMSG: Message strip message when a space was locked manually
manualLockedMessage=A teret manuálisan zárolták.
#XMSG: Message strip message when a space was locked due to the amount of generated audit logs
auditLogLockedMessage=A rendszer zárolta a teret, mert az auditnaplók nagy lemezterületet használnak fel.
#XMSG: Message strip message when a space was locked because the assigned quota was exceeded.
quotaLockedMessage=A rendszer zárolta a teret, mert túllépi a számára lefoglalt memóriát vagy lemezterületet.
#XMSG: Unlock Spaces confirmation text
unlockSpacesConfirmation=Biztosan feloldja a kijelölt tereket?
#XMSG: Lock Spaces confirmation text
lockSpacesConfirmation=Biztosan zárolja a kijelölt tereket?
#XTIT: Scoped role editor dialog title
scopedRoleEditorDialogTitle=Hatókörösszerep-szerkesztő
#XTIT: ECN Management title
ecnManagementTitle=Terek és alkalmazkodó számítási csomópontok kezelése
#XFLD: ECNs
ecns=Alkalmazkodó számítási csomópontok
#XFLD: ECN phase Ready
ecnReady=Készen áll
#XFLD: ECN phase Running
ecnRunning=Fut
#XFLD: ECN phase Initial
ecnInitial=Nem áll készen
#XFLD: ECN phase Starting
ecnStarting=Indul
#XFLD: ECN phase Starting Failed
ecnStartingFailed=Nem sikerült elindítani
#XFLD: ECN phase Stopping
ecnStopping=Leáll
#XFLD: ECN phase Stopping Failed
ecnStoppingFailed=Nem sikerült leállítani
#XBTN: Assign Button
assign=Terek hozzárendelése
#XBTN: Start Header-Button
start=Indítás
#XBTN: Update Header-Button
repair=Frissítés
#XBTN: Stop Header-Button
stop=Leállítás
#XFLD: ECN hours remaining
ecnHoursRemaining=1000 óra van hátra
#XFLD: ECN block-hours remaining
ecnBlockHoursRemaining={0} blokkóra maradt
#XFLD: ECN block-hour remaining
ecnBlockHourRemaining={0} blokkóra maradt
#XTIT: ECN creation dialog title
ecnCreationDialogTitle=Alkalmazkodó számítási csomópont létrehozása
#XTIT: ECN edit dialog title
ecnEditDialogTitle=Alkalmazkodó számítási csomópont szerkesztése
#XTIT: ECN deletion dialog title
ecnDeletionDialogTitle=Alkalmazkodó számítási csomópont törlése
#XTIT: ECN Space assignment dialog title
ecnSpaceAssignmentDialogTitle=Terek hozzárendelése
#XFLD: ECN ID
ECNIDLabel=Alkalmazkodó számítási csomópont
#XTXT: Selected toolbar text
selectedToolbarText=Kiválasztva: {0}
#XTIT: Elastic Compute Nodes
ECNslong=Alkalmazkodó számítási csomópontok
#XTIT: Object assignment - Table header: Objects available
objectsAvailable=Objektumok száma
#XTIT: Object assignment - Dialog header text
selectObjects=Válassza ki az alkalmazkodó számítási csomóponthoz hozzárendelni kívánt tereket és objektumokat:
#XTIT: Object assignment - Table header title: Objects
objects=Objektumok
#XTIT: Object assignment - Table header: Type
type=Típus
#XMSG: Message strip delete database user audit warning
deleteDatabaseUserWarningMessageStrip=Ha töröl egy adatbázis-felhasználót, akkor az összes generált auditnapló-bejegyzés is törlődik. Ha meg szeretné tartani az auditnaplókat, exportálja őket az adatbázis-felhasználó törlése előtt.
#XMSG: Message strip delete HDI container audit logs impact
deleteHDIContainerWarningMessageStrip=Ha leválaszt egy HDI-tárolót a térről, akkor az összes generált auditnapló-bejegyzés törlődik. Ha meg szeretné tartani az auditnaplókat, exportálja őket a HDI-tároló leválasztása előtt.
#XTXT: All audit logs
allAuditLogs=A tér esetében generált összes auditnapló-bejegyzés
#XMSG: Message strip disable audit warning
disableAuditWarningMessageStrip=Ha letilt egy auditszabályzatot (olvasási vagy módosítási műveletek), akkor annak összes auditnapló-bejegyzése törlődik. Ha meg szeretné tartani az auditnapló-bejegyzéseket, exportálja őket az auditszabályzat letiltása előtt.
#XTIT: No Spaces assigned to ECN Illustration title
noSpacesECNTitle=Még nincs hozzárendelve tér vagy objektum
#XTIT: No Spaces assigned to ECN Illustration description
noSpacesECNDescription=Az alkalmazkodó számítási csomópont használatának megkezdéséhez rendeljen hozzá teret vagy objektumokat.
#XTIT: No Spaces Illustration title
noSpacesTitle=Még nem jött létre tér
#XTIT: No Spaces Illustration description
noSpacesDescription=Az adatok beolvasásának megkezdéséhez hozzon létre egy teret.
#XTIT: No Spaces Illustration title
noDeletedSpacesTitle=A Lomtár üres
#XTIT: No Spaces Illustration description
noDeletedSpacesDescription=Innen visszaállíthatja a törölt tereket.
#XTXT:  {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDatabaseUsers=A tér üzembe helyezése után a következő adatbázis-felhasználók {0} törölve lesznek, és nem lehet őket visszaállítani:
#XTIT: Delete database users
deleteDatabaseUsersTitle=Adatbázis-felhasználók törlése
#YMSE: Error if ecn id already exists
ecnIDInputDuplicateError=Már van ilyen azonosító.
#YMSE: Error if ecn id contains invalid characters
ecnIDInputInvalidError=Csak kisbetűket (a–z) és számokat (0–9) használjon.
#YMSE: Error if ecn id is too short
ecnIDInputTooShortError=Az azonosítónak legalább {0} karakterből kell állnia.
#XMSG: ecn id length warning
ecnIdLengthWarning=Túllépte a(z) {0} karakteres maximális hosszt.
#XFLD: open System Monitor
systemMonitor=Rendszerfigyelő
#XFLD: open ECN schedule dialog menu entry
schedule=Ütemezés
#XFLD: open create ECN schedule dialog
createSchedule=Ütemezés létrehozása
#XFLD: open change ECN schedule dialog
changeSchedule=Ütemezés szerkesztése
#XFLD: open delete ECN schedule dialog
deleteSchedule=Ütemezés törlése
#XFLD: open self assign ECN schedule dialog
selfAssignSchedule=Ütemezés magamhoz rendelése
#XFLD: open pause ECN schedule dialog
pauseSchedule=Ütemezés szüneteltetése
#XFLD: open resume ECN schedule dialog
resumeSchedule=Ütemezés folytatása
#XFLD: View Logs
viewLogs=Naplók megjelenítése
#XFLD: Compute Blocks
computeBlocks=Számítási blokkok
#XFLD: Memory label in ECN creation dialog
ecnMemory=Memória (GB)
#XFLD: Storage label in ECN creation dialog
ecnStorage=Tároló (GB)
#XFLD: Number of CPU label in ECN creation dialog
ecnCPUCount=CPU-k száma
#XFLD: ECN updated by label
changedBy=Módosította
#XFLD: ECN updated on label
changedOn=Módosítás dátuma
#XMSG: Success message after creating a Elastic Compute Node
createEcnSuccessMessage=Az alkalmazkodó számítási csomópont létrejött
#YMSE: Error while creating a Elastic Compute Node
createEcnError=Nem sikerült létrehozni az alkalmazkodó számítási csomópontot
#XMSG: Success message after updating a Elastic Compute Node
updateEcnSuccessMessage=Az alkalmazkodó számítási csomópont frissült
#XMSG: Error while updating a Elastic Compute Node
udpateEcnError=Nem sikerült frissíteni az alkalmazkodó számítási csomópontot
#XMSG: Success message after deleting a Elastic Compute Node
deleteEcnSuccessMessage=Az alkalmazkodó számítási csomópont törölve
#YMSE: Error while deleting a Elastic Compute Node
deleteEcnError=Nem sikerült törölni az alkalmazkodó számítási csomópontot
#XMSG: Success message after starting a Elastic Compute Node
startingEcnSuccessMessage=Alkalmazkodó számítási csomópont indítása
#XMSG: Success message after stopping a Elastic Compute Node
stoppingEcnSuccessMessage=Alkalmazkodó számítási csomópont leállítása
#YMSE: Error while starting a Elastic Compute Node
startingEcnError=Nem sikerült elindítani az alkalmazkodó számítási csomópontot
#YMSE: Error while stopping a Elastic Compute Node
stoppingEcnError=Nem sikerült leállítani az alkalmazkodó számítási csomópontot
#XBUT: Add Object button for an ECN
assignObjects=Objektumok hozzáadása
#XFLD: Enable auto object assignment label
enableAutoObjAssignment=Minden objektum automatikus hozzáadása
#XFLD: object type label to be assigned
objectTypeLabel=Típus (szemantikai használat)
#XFLD: assigned object type label
assignedObjectTypeLabel=Típus
#XFLD: technical name label
TechnicalNameLabel=Technikai név
#XFLD: Object selecttion ECN label
objectSelectionECNLabel=Válassza ki az alkalmazkodó számítási csomóponthoz hozzáadni kívánt objektumokat
#XTIT: Add objects dialog title
assignObjectsTitle=Objektumok hozzárendelése
#XFLD: object label with object count
objectLabel=Objektum
#XMSG: No objects available to add message.
noObjectsToAssign=Nem érhetők el hozzárendelhető objektumok.
#XMSG: No objects assigned message.
noAssignedObjects=Nincsenek hozzárendelve objektumok.
#XTIT: Remove objects popup title
removeObjectsOrSpacesConfirmationTitle=Figyelmeztetés
#XTIT: Delete ECN popup button and title
deleteECNConfirmationButtonAndTitle=Törlés
#XMSG: Remove objects popup text
removeObjectsConfirmation=Biztosan eltávolítja a kijelölt objektumokat?
#XMSG: Remove spaces popup text
removeSpacesConfirmation=Biztosan eltávolítja a kijelölt tereket?
#XBUT: Confirmation button for objects dialog
removeObjectsOrSpacesConfirmationBtn=Terek eltávolítása
#XMSG: Success message after removing objects
removeObjectsSuccessMessage=Az elérhetővé tett objektumok eltávolítva
#XMSG: Success message after assigning objects
assignObjectsSuccessMessage=Az elérhetővé tett objektumok hozzárendelve
#XFLD: auto object assignment label for space tile
autoObjAssignmentLabel=Minden elérhetővé tett objektum
#XFLD: Spaces tab label
spacesTabLabel=Terek
#XFLD: Exposed objects tab label
consumptionObjectsTabLabel=Elérhetővé tett objektumok
#XMSG: Success message after unassigning spaces
spacesUnassignedSuccessMessage=A terek eltávolítva
#XMSG: Success message after unassigning space
spaceUnassignedSuccessMessage=A tér eltávolítva
#YMSE: Error while assigning / unassigning spaces
spacesAssignUnassignError=Nem sikerült hozzárendelni vagy eltávolítani a tereket.
#YMSE: Error while removing objects
removeObjectsError=Nem sikerült hozzárendelni vagy eltávolítani az objektumokat.
#YMSE: Error while removing object
removeObjectError=Nem sikerült hozzárendelni vagy eltávolítani az objektumot.
#YMSE: Error when saving the ecn with invalid value
invalidSizingPlanError=A korábban kiválasztott szám már nem érvényes. Válasszon érvényes számot.
#YMSE: Error when saving the ecn with invalid value
invalidPerformanceClassError=Érvényes teljesítményosztályt válasszon ki.
#YMSE: Error when saving the ecn with invalid value
performanceClassNotAvailableError=A korábban választott teljesítményosztály ({0}) jelenleg nem érvényes. Válasszon érvényes teljesítményosztályt.
#XTXT: Delete ECN confirmation text
deleteConfirmationTxtECN=Biztosan törli az alkalmazkodó számítási csomópontot?
#XFLD: tooltip for ? button
help=Súgó
#XFLD: ECN edit button label
editECN=Konfigurálás
#XFLD: Technical type label for ERModel
DWC_ERMODEL=Egyed-kapcsolat modell
#XFLD: Technical type label for Local Table
DWC_LOCAL_TABLE=Helyi tábla
#XFLD: Technical type label for Remote Table
DWC_REMOTE_TABLE=Távoli tábla
#XFLD: Technical type label for Analytic Model
DWC_ANALYTIC_MODEL=Elemzési modell
#XFLD: Technical type label for Task Chain
DWC_TASKCHAIN=Feladatlánc
#XFLD: Technical type label for Data Flow
DWC_DATAFLOW=Adatáramlás
#XFLD: Technical type label for Replication Flow
DWC_REPLICATIONFLOW=Replikációs folyamat
#XFLD: Technical type label for Transformation Flow
DWC_TRANSFORMATIONFLOW=Átalakítási folyamat
#XFLD: Technical type label for Intelligent Lookup
DWC_IDT=Intelligens keresés
#XFLD: Technical type label for Repository
DWC_REPOSITORY_PACKAGE=Tárház
#XFLD: Technical type label for Enterprise Search
DWC_ESMODEL=Enterprise Search
#XFLD: Technical type label for View
DWC_VIEW=Nézet
#XFLD: Technical type label for Data Product
DWC_DATAPRODUCT=Adatfeldolgozási termék
#XFLD: Technical type label for Data Access Control
DWC_DAC=Adathozzáférés-vezérlő
#XFLD: Technical type label for Folder
DWC_FOLDER=Mappa
#XFLD: Technical type label for Data Entity
DWC_BUSINESS_ENTITY=Üzleti entitás
#XFLD: Technical type label for Business Entity Variant
DWC_BUSINESS_ENTITY_VARIANT=Üzletientitás-változat
#XFLD: Technical type label for Responsibility Scenario
DWC_AUTH_SCENARIO=Felelősségi forgatókönyv
#XFLD: Technical type label for Fact Model
DWC_FACT_MODEL=Tényadatmodell
#XFLD: Technical type label for Transformation Flow
DWC_PERSPECTIVE=Perspektíva
#XFLD: Technical type label for Consumption Model
DWC_CONSUMPTION_MODEL=Felhasználási modell
#XFLD: Technical type label for Remote Connection
DWC_REMOTECONNECTION=Távoli kapcsolat
#XFLD: Technical type label for Fact Model Variant
DWC_FACT_MODEL_VARIANT=Tényadatmodell-változat
#XMSG: Schedule created alert message
createScheduleSuccess=Az ütemezés létrejött
#XMSG: Schedule updated alert message
updateScheduleSuccess=Az ütemezés frissítve
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Az ütemezés törölve
#XMSG: Schedule takeover success message
ownerChangeScheduleSuccess=Az ütemezés hozzárendelve Önhöz
#XMSG: Message for starting pausing of schedule
pauseScheduleStarted=1 ütemezés szüneteltetése
#XMSG: Message for starting resuming of schedule
resumeScheduleStarted=1 ütemezés folytatása
#XFLD: Segmented button label
availableSpacesButton=Elérhető
#XFLD: Segmented button label
selectedSpacesButton=Kiválasztva
#XFLD: Visit website button text
visitWebsite=Weboldal megnyitása
#XMSG: Warning message on unselecting source language
sourceLanguageRemovalWarning=A korábban kiválasztott forrásnyelv el lesz távolítva.
#XBUT: enable translation confirmation button
enableTranslationConfirmationBtn=Engedélyezés
#XFLD: ECN performance class label
performanceClassLabel=Teljesítményosztály
#XTXT performance class memory text
memoryText=Memória
#XTXT performance class compute text
computeText=Számítás
#XTXT performance class high-compute text
highComputeText=Számításközpontú
#XBUT: Recycle Bin Button Text
recycleBin=Lomtár
#XBUT: Restore Button Text
restore=Visszaállítás
#XMSG: Warning message for new Workload Management UI
priorityWarning=Ez a terület írásvédett. A tér prioritását a Rendszer / Konfiguráció / Munkaterhelés-kezelés területen módosíthatja.
#XMSG: Warning message for new Workload Management UI
workloadWarning=Ez a terület írásvédett. A tér munkaterhelés-konfigurációját a Rendszer / Konfiguráció / Munkaterhelés-kezelés területen módosíthatja.
#XCOL: Space table-view column: Apache Spark vCPUs
sparkManagementVCPUs=Apache Spark vCPU-k
#XCOL: Space table-view column: Apache Spark Memory (GB)
sparkManagementMemory=Apache Spark-memória (GB)
#XCOL: Space type (from Space Capability) for Data Ingestion Spaces
ingestionSpaceType=Adatfeldolgozásitermék-betöltés
#XFLD: Deployment in progress nodatatext for spark table
sparkTableDeploymentInProgress=Nem érhetők el adatok, mert éppen zajlik a tér üzembe helyezése
#XFLD: Runtime data being loaded nodatatext for spark table
sparkTableDeploymentLoading=Nem érhetők el adatok, mert éppen betöltődik a tér
#XCOL: Space type (from Space Capability) for BW Private Cloud Edition
BWPCESpaceType=SAP BW
#XBUT: Go to Instance Mappings Button
navToMappingsButton=Példány-hozzárendelések szerkesztése
#XFLD: SAP HANA Data Lake Storage label if less than 1GB
dataLakeStorageUsed=< 1 GB
