#DO NOT REMOVE________________________
#XFLD: Breadcrumb for space monitoring page
spaceMonitorBreadcrumb=监控
#XTXT: Type name for spaces in browser tab page title
space=空间
#_____________________________________
#XFLD: Spaces label in
spaces=空间
#XFLD: Manage plan button text
manageQuotaButtonText=管理计划
#XBUT: Manage resources button
manageResourcesButton=管理资源
#XFLD: Create space button tooltip
createSpace=创建空间
#XFLD: Create
create=创建
#XFLD: Deploy
deploy=部署
#XFLD: Page
page=页面
#XFLD: Cancel
cancel=取消
#XFLD: Update
update=更新
#XFLD: Save
save=保存
#XFLD: OK
ok=确定
#XFLD: days
days=天数
#XFLD: Space tile edit button label
edit=编辑
#XFLD: Auto Assign all objects to space
autoAssign=自动分配
#XFLD: Space tile open monitoring button label
openMonitoring=监控
#XFLD: Delete
delete=删除
#XFLD: Copy Space
copy=复制
#XFLD: Close
close=关闭
#XCOL: Space table-view column status
status=状态
#XFLD: Space status active
activeLabel=活动
#XFLD: Space status locked
lockedLabel=已锁定
#XFLD: Space status critical
criticalLabel=严重
#XFLD: Space status cold
coldLabel=低用量
#XFLD: Space status deleted
deletedLabel=已删除
#XFLD: Space status unknown
unknownLabel=未知
#XFLD: Space status ok
okLabel=正常
#XFLD: Database user expired
expired=已过期
#XFLD: deployed
deployed=已部署
#XFLD: not deployed
notDeployed=未部署
#XFLD: changes to deploy
changesToDeploy=待部署更改
#XFLD: pending
pending=正在部署
#XFLD: designtime error
designtimeError=设计时错误
#XFLD: runtime error
runtimeError=运行时错误
#XFLD: Space created by label
createdBy=创建者
#XFLD: Space created on label
createdOn=创建日期
#XFLD: Space deployed on label
deployedOn=部署日期
#XFLD: Space ID label
spaceID=空间 ID
#XFLD: Priority label
priority=优先级
#XFLD: Space Priority label
spacePriority=空间优先级
#XFLD: Space Configuration label
spaceConfiguration=空间配置
#XFLD: Not available
notAvailable=不可用
#XFLD: WorkloadType default
default=默认
#XFLD: WorkloadType custom
custom=自定义
#XFLD: Data Lake option label; refers to component SAP HANA Cloud, Data Lake
dataLakeAccess=数据湖访问
#XFLD: Translation label
translationLabel=翻译
#XFLD: Source language label
sourceLanguageLabel=源语言
#XFLD: Translation CheckBox label
translationCheckBox=启用翻译
#XTXT: Popover hint that space has to be deployed to show user details
openDatabaseUserHintPopoverText=部署空间以访问用户详细信息。
#XTXT: Popover hint that space has to be deployed to open database explorer
openDatabaseExplorerHintPopoverText=部署空间以打开数据库资源管理器。
#XTXT: Text for when the Data Lake check box is disabled
dataLakeOptionDisabled=不能使用这个空间访问数据湖，因为它已被其他空间使用。
#XFLD: Data Lake check box text; refers to component SAP HANA Cloud, Data Lake
dataLakeCheckBox=使用此空间访问数据湖。
#XFLD: Space Priority minimum label extension
low=低
#XFLD: Space Priority maximum label extension
high=高
#XFLD: Space name label
spaceName=空间名称
#XFLD: Enable deploy objects checkbox
enableDeployObjects=部署对象
#XTIT: Copy spaces dialog title
copySpaceDialogTitle=复制 {0}
#XTXT Human readable text for unselected source language
unSelectedSourceLanguage=（未选择）
#XTXT Human readable text for language code "af"
af=南非荷兰语
#XTXT Human readable text for language code "ar"
ar=阿拉伯语
#XTXT Human readable text for language code "bg"
bg=保加利亚语
#XTXT Human readable text for language code "ca"
ca=加泰罗尼亚语
#XTXT Human readable text for language code "zh"
zh=简体中文
#XTXT Human readable text for language code "zf"
zf=中文
#XTXT Human readable text for language code "hr"
hr=克罗地亚语
#XTXT Human readable text for language code "cs"
cs=捷克语
#XTXT Human readable text for language code "cy"
cy=威尔士语
#XTXT Human readable text for language code "da"
da=丹麦语
#XTXT Human readable text for language code "nl"
nl=荷兰语
#XTXT Human readable text for language code "en-UK"
en-UK=英语（英国）
#XTXT Human readable text for language code "en"
en=英语（美国）
#XTXT Human readable text for language code "et"
et=爱沙尼亚语
#XTXT Human readable text for language code "fa"
fa=波斯语
#XTXT Human readable text for language code "fi"
fi=芬兰语
#XTXT Human readable text for language code "fr-CA"
fr-CA=法语（加拿大）
#XTXT Human readable text for language code "fr"
fr=法语
#XTXT Human readable text for language code "de"
de=德语
#XTXT Human readable text for language code "el"
el=希腊语
#XTXT Human readable text for language code "he"
he=希伯来语
#XTXT Human readable text for language code "hi"
hi=印地语
#XTXT Human readable text for language code "hu"
hu=匈牙利语
#XTXT Human readable text for language code "is"
is=冰岛语
#XTXT Human readable text for language code "id"
id=印度尼西亚语
#XTXT Human readable text for language code "it"
it=意大利语
#XTXT Human readable text for language code "ja"
ja=日语
#XTXT Human readable text for language code "kk"
kk=哈萨克语
#XTXT Human readable text for language code "ko"
ko=朝鲜语/韩语
#XTXT Human readable text for language code "lv"
lv=拉脱维亚语
#XTXT Human readable text for language code "lt"
lt=立陶宛语
#XTXT Human readable text for language code "ms"
ms=马来语
#XTXT Human readable text for language code "no"
no=挪威语
#XTXT Human readable text for language code "pl"
pl=波兰语
#XTXT Human readable text for language code "pt"
pt=葡萄牙语（巴西）
#XTXT Human readable text for language code "pt-PT"
pt-PT=葡萄牙语（葡萄牙）
#XTXT Human readable text for language code "ro"
ro=罗马尼亚语
#XTXT Human readable text for language code "ru"
ru=俄语
#XTXT Human readable text for language code "sr"
sr=塞尔维亚语
#XTXT Human readable text for language code "sh"
sh=塞尔维亚-克罗地亚语
#XTXT Human readable text for language code "sk"
sk=斯洛伐克语
#XTXT Human readable text for language code "sl"
sl=斯洛文尼亚语
#XTXT Human readable text for language code "es"
es=西班牙语
#XTXT Human readable text for language code "es-MX"
es-MX=西班牙语（墨西哥）
#XTXT Human readable text for language code "sv"
sv=瑞典语
#XTXT Human readable text for language code "th"
th=泰文
#XTXT Human readable text for language code "tr"
tr=土耳其语
#XTXT Human readable text for language code "uk"
uk=乌克兰语
#XTXT Human readable text for language code "vi"
vi=越南语
#XTIT: Delete spaces confirmation title
deleteConfirmationHeaderText=删除空间
#XTXT: Logically delete space delete confirmation text with space name
logicallyDeleteConfirmationBodyTextWithSpaceName=是否确定要将空间 "{0}" 移至回收站？
#XTXT: Logically dpace delete confirmation text for multiple spaces
logicallyDeleteConfirmationBodyTextMultiple=是否确定要将 {0} 个选定空间移至回收站？
#XTXT: Delete space delete confirmation text with space name
deleteConfirmationBodyTextWithSpaceName=是否确定要删除空间 “{0}”？此操作无法撤销。
#XTXT: Delete space delete confirmation text for multiple spaces
deleteConfirmationBodyTextMultiple=是否确定要删除选择的 {0} 个空间？此操作无法撤销。以下内容将被 {1} 删除：
#XTXT: permanently
permanently=永久
#XTXT: {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDetail=以下内容将被{0}删除且无法恢复：
#XTXT: Hint Delete space confirmation text
deleteConfirmationHint=请输入{0}以确认删除。
#XTXT: Delete space confirmation error check spelling
deleteConfirmationErrorCheckSpelling=请检查拼写后再试。
#XTXT: All Spaces
allSpaces=全部空间
#XTXT: All data
allData=空间中包含的所有对象和数据
#XTXT: All connections
allConnections=空间中定义的所有连接
#XFLD: Space tile selection box tooltip
clickToSelect=单击选择
#XTXT: All database users
allDatabaseUsers=与空间关联的任意开放式 SQL 模式中包含的所有对象和数据
#XFLD: remove members button tooltip
deleteUsers=移除成员
#XTXT: Space long description text
description=说明（最多 4,000 个字符）
#XFLD: Add Members button tooltip
addUsers=添加成员
#XFLD: Add Users button tooltip
addUsersTooltip=添加用户
#XFLD: Edit Users button tooltip
editUsersTooltip=编辑用户
#XFLD: Remove Users button tooltip
removeUsersTooltip=移除用户
#XFLD: Searchfield placeholder
filter=搜索
#XCOL: Users table-view column health
health=健康
#XCOL: Users table-view column access
access=访问
#XFLD: No user found nodatatext
noDataText=未找到用户
#XTIT: Members dialog title
selectUserDialogTitle=添加成员
#XTIT: User dialog title
addUserDialogTitle=添加用户
#XTIT: Delete connections confirmation title
deleteConnectionConfirmationHeaderText=删除连接
#XTIT: Delete single connection confirmation title
deleteConnectionConfirmationHeaderTextSingle=删除连接
#XTXT: Delete connection delete confirmation text
deleteConnectionConfirmationBodyText=是否确定要删除选择的连接？其将被永久移除。
#XTIT: Remote source dialog title
selectRemoteConnectionDialogTitle=选择连接
#XTIT: Share connection dialog title
connectionSharingDialogTitle=共享连接
#XTIT: Connections shared from other spaces table title
sharedConnectionsFromOtherSpaces=共享的连接
#XFLD: Add remote source button tooltip
addRemoteConnections=添加连接
#XFLD: Delete remote source button tooltip
deleteRemoteConnections=移除连接
#XFLD: Share remote source button tooltip
shareConnections=共享连接
#XFLD: Tile-layout tooltip
tileLayout=磁贴布局
#XFLD: Table-layout tooltip
tableLayout=表布局
#XMSG: Success message after creating space
createSpaceSuccessMessage=已创建空间
#XMSG: Success message after copying space
copySpaceSuccessMessage=正在将空间 "{0}" 复制到空间 "{1}"
#XMSG: Success message after deploying space
deploymentSuccessMessage=已开始部署空间
#XMSG: Success message after Apache Spark update started
sparkUpdateStartedSuccessMessage=已启动 Apache Spark 更新
#YMSE: Error while starting Apache Spark update
sparkUpdateStartedErrorMessage=更新 Apache Spark 失败
#XMSG: Success message after updating space
updateSettingsSuccessMessage=已更新空间详细信息
#XMSG: Success message after unlocking space
unlockSpaceSuccessMessage=空间已暂时解锁
#XMSG: Success message after deleting a single space
deleteSpaceSuccessMessage=空间已删除
#XMSG: Success message after deleting multiple spaces
deleteSpacesSuccessMessage=空间已删除
#XMSG: Success message after restoring a single space
restoreSpaceSuccessMessage=空间已还原
#XMSG: Success message after restoring multiple spaces
restoreSpacesSuccessMessage=空间已还原
#YMSE: Error while updating settings
updateSettingsFailureMessage=无法更新空间设置。
#YMSE: Error while updating data lake option
dataLakeEnabledInOtherSpace=数据湖已分配至其他空间。每次只有一个空间可以访问数据湖。
#YMSE: Error while updating data lake option
virtualTablesExists=无法从此空间取消分配数据湖，因为仍然存在虚拟表的依赖项*。请删除虚拟表，以从此空间取消分配数据湖。
#YMSE: Error while unlocking space
unlockSpaceFailureMessage=无法将空间解锁。
#YMSE: Error while creating space
createSpaceError=无法创建空间。
#YMSE: Error while creating duplicate space
createDuplicateSpaceError=名称为 {0} 的空间已存在。
#YMSE: Error while deleting a single space
deleteSpaceError=无法删除空间。
#YMSE: Error details while deleting a single space
deleteSpaceDetailedError=你的空间“{0}”无法再正确工作。请尝试再次将其删除。如果仍无法工作，请请求管理员删除你的空间或创建支持工单。
#YMSE: Error while deleting a single space in files
deleteSpaceInFilesError=没能删除文件中的空间数据。
#YMSE: Error while deleting a single space users
deleteSpaceUsersError=没能移除用户。
#YMSE: Error while deleting a single space schema
deleteSpaceSchemasError=没能移除模式。
#YMSE: Error while deleting a single space connections
deleteSpaceConnectionsError=没能移除连接。
#YMSE: Error while deleting a single space data
deleteSpaceDataError=没能删除空间数据。
#YMSE: Error while deleting multiple spaces
deleteSpacesError=没能删除空间。
#YMSE: Error while restoring a single space
restoreSpaceError=没能还原空间。
#YMSE: Error while restoring multiple spaces
restoreSpacesError=没能还原空间。
#YMSE: Error while creating users
createUsersError=没能添加用户。
#YMSE: Error while removing users
removeUsersError=我们无法移除用户。
#YMSE: Error while removing user
removeUserError=无法移除此用户。
#YMSE: Error assigning roles to a user
roleAssignUnAssignError=没能向选定的范围内角色添加用户。\n\n 你不能将自己添加到范围内角色。可以请求管理员将你添加到范围内角色。
#YMSE: Error assigning user to the space
userAssignError=没能向空间分配用户。 \n\n 在范围内角色内，用户已被分配到允许的最大空间数（100）。
#YMSE: Error assigning users to the space
usersAssignError=没能向空间分配用户。 \n\n 在范围内角色内，用户已被分配到允许的最大空间数（100）。
#YMSE: Error while fetching users
fetchUserWithScopedRolesError=我们无法获取用户。请稍后重试。
#YMSE: Error while fetching scoped roles
fetchScopedRolesError=我们无法获取范围内的角色。
#YMSE: Error while fetching members
fetchUserError=没能获取成员。请稍后再试。
#YMSE: Error while loading run-time database
loadRuntimeError=我们无法从运行时数据库加载信息。
#YMSE: Error while loading spaces
loadSpacesError=抱歉，尝试检索空间时出错。
#YMSE: Error while loading haas resources
loadStorageError=抱歉，尝试检索存储数据时出错。
#YMSE: Error no data could be loaded
loadDataError=抱歉，尝试检索数据时出错。
#XFLD: Click to refresh storage data
clickToRefresh=点击此处重试。
#XTIT: Delete space popup title
deleteSpacePopupTitle=删除空间
#XCOL: Spaces table-view column name
name=名称
#XCOL: Spaces table-view deployment status
deploymentStatus=部署状态
#XFLD: Disk label in space details
storageLabel=磁盘 (GB)
#XFLD: In-Memory label in space details
ramLabel=内存 (GB)
#XFLD: Memory label on space card
memory=内存存储
#XFLD: Storage Assignment label in space details
storrageAssignmentLabel=存储空间
#XFLD: Storage Type label in space details
storageTypeLabel=存储类型
#XFLD: Enable Space Quota
enableSpaceQuota=启用空间配额
#XFLD: No Space Quota
noSpaceQuota=无空间配额
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
diskAndInMemoryText=SAP HANA 数据库（磁盘和内存）
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
dataLakeFilesText=SAP HANA 数据湖文件
#XFLD: Available scoped roles label
availableRoles=可用的范围内角色
#XFLD: Selected scoped roles label
selectedRoles=选择的范围内角色
#XCOL: Spaces table-view column models
models=模型
#XCOL: Spaces table-view column users
users=用户
#XCOL: Spaces table-view column connections
connections=连接
#XFLD: Section header overview in space detail
overview=概览
#XFLD: Section header Apache Spark in space detail
sparkManagementSection=Apache Spark
#XFLD: Section header Apache Spark in space detail
sparkManagementSubSection=应用
#XFLD: Section header Apache Spark in space detail
taskAssignmentSubSection=任务分配
#XFLD: vCPU label in Apache Spark section
vCPULabel=vCPU
#XFLD: Memory label in Apache Spark section
memoryLabel=内存（GB）
#XFLD: space configuration label in Apache Spark section
spaceConfigurationLabel=空间配置
#XFLD: Space Source label
sparkApplicationLabel=应用程序
#XFLD: Cluster Size label
clusterSizeLabel=簇大小
#XFLD: Driver label
driverLabel=驱动程序
#XFLD: Executor label
executorLabel=执行程序
#XFLD: max label
maxLabel=最大使用量
#XFLD: TrF Default label
trFDefaultLabel=转换流默认值
#XFLD: Merge Default label
mergeDefaultLabel=合并默认值
#XFLD: Optimize Default label
optimizeDefaultLabel=优化默认值
#XFLD: Deployment Default label
deploymentDefaultLabel=本地表（文件）部署
#XFLD: max spark source label
driverExecutorMaxUsedSparkSource={0} CPU / {1} GB
#XFLD: Object type label
taskObjectTypeLabel=对象类型
#XFLD: Task activity label
taskActivityLabel=活动
#XFLD: Task Application ID label
taskApplicationIDLabel=默认应用
#XFLD: Section header in space detail
generalSettings=常规设置
#XMSG: Message strip message when a space is locked
spaceLockedMessageStrip=该空间目前被系统锁定。
#XMSG: Message strip message deploy warning
deployWarningMessageStrip=将立即部署此部分的更改。
#XMSG: Message strip message modify space configuration warning
modifySpaceConfigurationMessageStrip=请注意，更改这些值可能导致性能问题。
#XFLD: Button text to unlock the space again
unlockSpace=将空间解锁
#XFLD: Info text for audit log formatted message
auditLogText=启用审计日志可以记录读取或更改操作（审计策略）。这样管理员就可以分析具体执行的操作、执行时间和执行人员。
#XMSG: Message strip  audit warning
auditLogWarningMessageStrip=审计日志会占用租户上大量的磁盘存储空间。如果启用审计策略（读取或更改操作），应定期监控磁盘存储空间的使用情况（通过系统监控器中的 "已用磁盘存储空间" 卡），避免磁盘爆满导致服务中断。如果禁用审计策略，系统将删除所有审计日志条目。如果想保留审计日志条目，可以考虑将它们先导出，然后再禁用审计策略。
#XFLD: Message strip link text to open in app help to guide user what to do to unlock his space permanently
unlockSpaceHelp=显示帮助
#XMSG: Message strip message when a space will be locked again
spaceTemporarilyUnlockedMessageStrip=这个空间超出其空间存储，将在 {0} {1} 后锁定。
#XMSG: Unit for remaining time until space is locked again
hours=小时
#XMSG: Unit for remaining time until space is locked again
minutes=分钟
#XFLD: Section header in space detail for auditing
detailsSectionAuditing=审计
#XFLD: Subsection header in space detail for auditing
auditing=空间审计设置
#XFLD: Hot space tooltip
hotSpaceCountTooltip=处于严重状态的空间：已用存储大于 90%。
#XFLD: Green space tooltip
greenSpaceCountTooltip=处于正常状态的空间：已用存储在 6% 和 90% 之间。
#XFLD: Cold space tooltip
coldSpaceCountTooltip=处于低用量状态的空间：已用存储为 5% 或更低。
#XFLD: Critical space tooltip
criticalSpaceCountTooltip=处于严重状态的空间：已用存储大于 90%。
#XFLD: Green space tooltip
okSpaceCountTooltip=处于正常状态的空间：已用存储在 6% 和 90% 之间。
#XFLD: Locked space tooltip
lockedSpaceCountTooltipNew=处于已锁定状态的空间：由于内存不足，已被阻止。
#XFLD: Locked space tooltip
lockedSpaceCountTooltip=锁定的空间
#YMSE: Error while deleting remote source
deleteRemoteError=没能移除连接。
#XFLD: Space ID input tooltip
spaceIDInfoTooltip=随后无法更改空间 ID。\n有效字符为 A - Z、0 - 9 和 _
#YMSE: Error when space name is empty
spaceNameEmptyValidationError=输入空间名称。
#YMSE: Error when ECN Business name is empty
ecnNameEmptyValidationError=输入业务名称。
#YMSE: Error when space id is empty
spaceIDInputEmptyValidationError=输入空间 ID。
#YMSE: Error when space id contains invalid characters
spaceIDInputInValidError=字符无效。请仅使用 A - Z、0 - 9 和 _。
#YMSE: Error when space id exists while creation
spaceIDInputDuplicateError=空间 ID 已存在。
#XFLD: Space searchfield placeholder
search=搜索
#XMSG: Success message after creating users
createUsersSuccess=已添加用户
#XMSG: Success message after creating user
createUserSuccess=已添加用户
#XMSG: Success message after updating users
updateUsersSuccess={0} 个用户已更新
#XMSG: Success message after updating user
updateUserSuccess=已更新用户
#XMSG: Success message after removing users
removeUsersSuccess={0} 个用户已移除
#XMSG: Success message after removing user
removeUserSuccess=已移除用户
#XFLD: Schema name
schemaName=模式名称
#XFLD: used of total
ofTemplate={0} / {1}
#XFLD: Assigned disk label in space overview
assignedStorageTemplate=已分配磁盘（{0} / {1}）
#XFLD: Assigned Memory label in space overview
assignedRAMTemplate=已分配内存（{0} / {1}）
#XFLD: Storage ratio on space
accelearationRAM=内存加速
#XFLD: No Storage Consumption
noStorageConsumptionText=未分配存储配额。
#XFLD: Used disk label in space overview
usedStorageTemplate=已用磁盘存储（{0}，共 {1}）
#XFLD: Used Memory label in space overview
usedRAMTemplate=已用内存存储（{0}，共 {1}）
#XFLD: Used Disk tooltip
usedStorageTooltipTemplate=已用磁盘存储：{0} / {1}
#XFLD: Used Memory tooltip
usedRAMTooltipTemplate=已使用 {0} / {1} 内存
#XFLD: Assigned Disk tooltip
assignedStorageTooltipTemplate=已分配 {0} / {1} 磁盘
#XFLD: Assigned Memory tooltip
assignedRAMTooltipTemplate=已分配 {0} / {1} 内存
#XFLD: Space related used storage tooltip
spaceRelatedUsedStorageTooltipTemplate=空间数据：{0}
#XFLD: Not space related used storage tooltip
otherDataUsedStorageTooltipTemplate=其他数据：{0}
#XFLD: Storage used exceeded popover and tooltip
storageUsedExceededText=请考虑延长你的计划，或联系 SAP 支持人员。
#XCOL: Space table-view column used Disk
usedStorage=已用磁盘存储
#XCOL: Space monitor column used Memory
usedRAM=已用内存存储
#XCOL: Space monitor column Schema
tableSchema=模式
#XCOL: Space monitor column Storage Type
tableStorageType=存储类型
#XCOL: Space monitor column Table Type
tableType=表类型
#XCOL: Space monitor column Record Count
tableRecordCount=记录计数
#XFLD: Assigned Disk
assignedStorage=分配的磁盘存储空间
#XFLD: Assigned Memory
assignedRAM=分配的内存存储空间
#XCOL: Space table-view column storage utilization
tableStorageUtilization=已用存储空间
#XFLD: space status
spaceStatus=空间状态
#XFLD: space type
spaceType=空间类型
#XFLD: space type label for space with space type "SAP BW bridge" ("SAP BW bridge" is a product name, do not translate it)
abapbridgeSpaceType=SAP BW 网桥
#XFLD: space type label for space with space type "Data Marketplace"
marketplaceSpaceType=数据提供商产品
#XMSG: Error message for when selected space cannot be deleted due to the space type
deleteTypedSpaceError=空间 {0} 的类型为 {1}，因此无法删除。
#XMSG: Error message for when selected spaces cannot be deleted due to the space type
deleteTypedSpacesError=无法删除选择的 {0} 个空间。无法删除以下类型的空间：{1}。
#XFLD: space type label for space without space type
defaultSpaceType=SAP Datasphere
#XFLD: Tooltip for monitor space button
monitorSpace=监控
#XFLD: Tooltip for edit space button
editSpace=编辑空间
#XMSG: Deletion warning in messagebox
deleteConfirmation=是否确定要删除该空间？
#XFLD: Tooltip for delete space button
deleteSpace=删除空间
#XFLD: storage
storage=磁盘存储
#XFLD: username
userName=用户名
#XFLD: port
port=端口
#XFLD: hostname
hostName=主机名
#XFLD: password
password=密码
#XBUT: Request new password button
requestPassword=申请新密码
#YEXP: Usage explanation in time data section
timeDataSectionHint=创建要在你的模型和故事中使用的时间表和维。
#YEXP: Usage explanation in Space schema section
spaceSchemaHint=是否希望空间中的数据可供其他工具或应用使用？如果是这样，请创建一个或多个可以访问空间中数据的用户，并选择是否默认所有未来空间数据都可被使用。
#XTIT: Create schema popup title
createSchemaDialogTitle=创建开放式 SQL 模式
#XTIT: Generate Time Data dialog title
generateTimeDataDialogTitle=创建时间表和维
#XTIT: Edit Time Data dialog title
editTimeDataDialogTitle=编辑时间表和维
#XTIT: Time Data token title
timeDataTokenTitle=时间数据
#XTIT: Time Data token title
timeDataUpdateViews=更新时间数据视图
#XFLD: Time Data token creation in progress label
timeDataCreationInProgress=正在创建中...
#XFLD: Time Data token creation error label
timeDataCreationError=创建失败。请再试一次。
#XTIT: Time Data dialog time settings title
timeDataDialogTimeSettingsTitle=时间表设置
#XTIT: Time Data dialog time data table title
timeDataDialogTranslationTablesTitle=转换表
#XTIT: Time Data dialog dimensions title
timeDataDialogTimeViewsTitle=时间维
#XFLD: Time Data dialog time range label
timeRangeHint=定义时间范围
#XFLD: Time Data dialog time data table label
timeDataHint=给你的表命名。
#XFLD: Time Data dialog time data dimensions label
timeDimensionsHint=给你的维命名。
#XFLD: Time Data Time range description label
timerangeLabel=时间范围
#XFLD: Time Data dialog from year label
fromYearLabel=起始年
#XFLD: Time Data dialog to year label
toYearLabel=终止年
#XFLD: Time Data dialog calendar type label
calendarTypeLabel=日历类型
#XFLD: Time Data dialog granularity label
granularityLabel=粒度
#XFLD: Time Data dialog technical name label
technicalNameLabel=技术名称
#XFLD: Time Data dialog quarters text table lable
quarterTextsTableLabel=季度转换表
#XFLD: Time Data dialog months text table lable
monthsTextsTableLabel=月转换表
#XFLD: Time Data dialog days text table lable
daysTextsTableLabel=日转换表
#XFLD: Time Data dialog year label
yearLabel=年维
#XFLD: Time Data dialog quarter label
quarterLabel=季度维
#XFLD: Time Data dialog month label
monthLabel=月维
#XFLD: Time Data dialog day label
dayLabel=日维
#XFLD: Time Data dialog gregorian calendar type label
gregorian=公历
#XFLD: Time Data dialog time granularity day label
day=日
#XFLD: Time Data dialog business name maxlength reached
timeRangeValueStateTextMaxLengthBusinessName=已达到1000 个字符的最大长度。
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextMaxRangeExceeded=最大时间范围是 150 年。
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeLower=“起始年”应早于“终止年”
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeHigher=“起始年”不得早于 1900。
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeHigher=“终止年”应晚于“起始年”
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeLower=“终止年”必须早于当前年度再加 100 后的年度
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningFromYear=“起始年”延后可能会导致数据丢失
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningToYear=“终止年”提前可能会导致数据丢失
#XMSG: Time Data creation validation error message
timeDataValidationError=有些字段似乎无效。请检查必填字段以继续。
#XMSG: Time Data deletion popup text
deleteTimeDataConfirmation=是否确定要删除数据？
#XMSG: Time Data creation success message
createTimeDataSuccess=已创建时间数据
#XMSG: Time Data update success message
updateTimeDataSuccess=已更新时间数据
#XMSG: Time Data delete success message
deleteTimeDataSuccess=已删除时间数据
#XMSG: Time Data creation error message
createTimeDataError=尝试创建时间数据时发生错误。
#XMSG: Time Data update error message
updateTimeDataError=尝试更新时间数据时发生错误。
#XMSG: Time Data creation error message
deleteTimeDataError=尝试删除时间数据时发生错误。
#XMSG: Error message when time data could not be retrieved
retrievingTimeDataError=时间数据无法加载。
#XTIT: Time Data deletion popup title
deleteTimeDataConfirmationTitle=警告
#XTXT: Time Data deletion dependency popup text
deleteTimeDataDependencyText=我们没能删除你的时间数据，因为正在其他模型中使用。
#XTXT: Time Data deletion dependency popup text singular
deleteTimeDataDependencyTextSingular=我们没能删除你的时间数据，因为另一模型中正在使用。
#XMSG: Validation error when required time data form field is empty
timeDataRequiredField=此字段必填，不能留空。
#XFLD: Tooltip for Open in Database Explorer button
openInDBX=在数据库资源管理器中打开
#YMSE: Dimension Year
dimensionYearView=维“年”
#YMSE: Dimension Year
dimensionQuarterView=维“季度”
#YMSE: Dimension Year
dimensionMonthView=维“月”
#YMSE: Dimension Year
dimensionDayView=维“日”
#XFLD: Time Data deletion object title
timeDataUsedIn=（已在 {0} 个模型中使用）
#XFLD: Time Data deletion object title singular
timeDataUsedInSingular=（已在 1 个模型中使用）
#XFLD: Time Data deletion table column provider
provider=提供商
#XTIT: Time Data dependency dialog title
timeDataDependencyDialogTitle=依赖项
#XTIT: Create schema popup title
createSpaceSchemaUserDialogTitle=为空间模式创建用户
#XFLD: Create schema button
createSchemaButton=创建开放式 SQL 模式
#XFLD: Generate TimeData button
generateTimeDataButton=创建时间表和维
#XFLD: Show dependencies button
showDependenciesButton=显示依赖项
#XFLD: tooltip for disabled buttons when user is not member of space
mustBeMemberOfSpaceForOperation=要执行此操作，你的用户必须是空间的成员。
#XFLD: Create space schema user button
createSpaceSchemaUserButton=创建空间模式用户
#YMSE: API Schema users load error
loadSchemaUsersError=无法加载用户列表。
#XTIT: API Schema-config popup title
schemaUserDetailsDialogTitle=空间模式用户详细信息
#XMSG: API Schema user deletion popup text
deleteSpaceSchemaUserConfirmation=是否确定要删除选定用户？
#XMSG: API Schema user deletion confirmation text
userDeleteSuccess=用户已删除。
#YMSE: API Schema user deletion error
userDeleteError=无法删除用户。
#XFLD: User deleted
userDeleted=已删除用户。
#XTIT: Remove user popup title
removeUserConfirmationTitle=警告
#XMSG: Remove user popup text
removeUserConfirmation=是否确定要移除用户？将从空间中移除此用户以及为其分配的范围内角色。
#XMSG: Remove users popup text
removeUsersConfirmation=是否确定要移除用户？将从空间中移除用户以及为其分配的范围内角色。
#XBUT: Confirmation button for remove user dialog
removeUserConfirmationBtn=移除
#YMSE: No data text for available roles
noDataAvailableRoles=这个空间没有添加到任何范围内角色。\n要向空间添加用户，请先将空间添加到一个或多个范围内角色。
#YMSE: No data text for selected roles
noDataSelectedRoles=没有选择的范围内角色
#XTIT: Schema-config popup title
schemaDetailsDialogTitle=开放式 SQL 模式配置详细信息
#XFLD: Label for Read Audit Log
auditLogRead=启用读取操作的审计日志
#XFLD: Label for Change Audit Log
auditLogChange=启用更改操作的审计日志
#XFLD: Label Audit Log Retention
auditLogRetention=将日志保留
#XFLD: Label Audit Log Retention Unit
retentionUnit=天
#YMSE: Error when log retention time is set incorrectly
incorrectRetentionTime=输入 {0} 和 {1} 之间的整数
#XFLD: Label for Cross Space Schema Access
spaceSchemaAccess=使用空间模式数据
#XTIT: Confirm deactivation of space schema consumption dialog title
disableSpaceSchemaConsumptionDialogTitle=停止使用空间模式数据
#XTXT: Confirm deactivation of space schema consumption dialog text
disableSpaceSchemaConsumptionDialogText=该开放式 SQL 模式可能会使用你的空间模式的数据？如果停止使用，基于该空间模式数据的模型可能无法再工作。
#XBUT: Confirmation button for the stop consuming space schema data dialog
stopConsuming=停止使用
#XTXT: Tooltip for data lake icon
dataLakeIconTooltip=此空间用于访问数据湖
#XFLD: Tooltip for data lake table entry
dataLakeTableEntryTooltip=已启用数据湖
#XFLD: used In-Memory linit reached tag
usedRamLimitReachedLabel=已达到内存限制
#XFLD: Space storage Limit reached tag
storageLimitReachedLabel=已达到存储限制
#XFLD: Minimal Storage Limit Reached tag
minStorageLimitReachedLabel=已达到最低存储限制
#XFLD: Space ram tag
ramLimitReachedLabel=已达到内存限制
#XFLD: Minimal In-Memory Limit Reached tag
minRamLimitReachedLabel=已达到最低内存限制
#XFLD: Popover text after click on space storage limit warning
storageLimitReachedText=已达到 {0} 的已分配空间存储限制。请向空间分配更多存储。
#XFLD: System storage tag
systemStorageLimitReachedLabel=已达到系统存储限制
#XMSG: Popover text after click on storage limit warning
systemStorageLimitReachedText=已达到 {0} 的系统存储限制。目前无法向空间分配更多存储。
#XMSG: Schema deletion warning
deleteSchemaConfirmation=删除该开放式 SQL 模式也将永久删除模式中所有存储的对象和维护的关联。是否继续？
#XMSG: Success message after deleting a schema
schemaDeleteSuccess=已删除模式
#YMSE: Error while deleting schema.
schemaDeleteError=没能删除模式。
#XMSG: Success message after update a schema
schemaUpdateSuccess=已更新模式
#YMSE: Error while updating schema.
schemaUpdateError=没能更新模式。
#XMSG: Hint when no password is shown in the dialog.
newPasswordHint=我们已为该模式提供密码。如果你忘记密码或密码丢失，可以申请新密码。请记得复制或保存新密码。
#XMSG: Hint when showing the password in the dialog.
savePasswordHint=请复制密码。建立与该模式的连接需要该密码。如果你忘记了密码，可以打开该对话框重置密码。
#XFLD,50: Hint on entering schema name in creation dialog
schemaNameHint=创建模式后，无法更改此名称。
#XFLD: Open SQL Schemas section sub headline
schemasSQL=开放式 SQL
#XFLD: Space schema section sub headline
schemasSpace=空间
#XFLD: HDI Container section header
HDIContainers=HDI 容器
#XTXT: Add HDI Containers button tooltip
addHDIContainers=添加 HDI 容器
#XTXT: Remove HDI Containers button tooltip
removeHDIContainers=移除 HDI 容器
#XFLD: Enable HDI Containers feature button
enableHDIContainersButton=启用访问
#YMSE: No data text for HDI Containers table
noDataHDIContainers=未添加 HDI 容器。
#YMSE: No data text for Timedata section
noDataTimedata=未创建时间表和维。
#YMSE: No data text during runtime not available for Timedata section
runtimeNotAvailableNoDataTimedata=没能加载时间表和维，因为运行时数据库不可用。
#YMSE: No data text during runtime not available for HDI section
runtimeNotAvailableNoDataHDI=没能加载 HDI 容器，因为运行时数据库不可用。
#YMSE: Error while fetching HDI Containers
fetchHDIContainersError=没能获取 HDI 容器。请稍后再试。
#XFLD Table column header for HDI Container names
HDIContainerName=HDI 容器名称
#XFLD: Enable HDI Containers feature dialog title
enableHDIContainersDialogTitle=启用访问
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText1New=可以在 SAP Datasphere 租户上启用 SAP SQL Data Warehousing，以在 HDI 容器和 SAP Datasphere 空间之间交换数据，无需移动数据。
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText2=为此，点击下方按钮，创建支持工单。
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText3New=支持工单处理后，必须在 SAP Datasphere 运行时数据库中构建一个或多个新 HDI 容器。然后，在所有 SAP Datasphere 空间的 HDI 容器部分，“启用访问”按钮替换为 + 按钮，可以将容器添加到空间。
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText41=需要更多信息？请转到 %%0。如需详细了解工单中应包含的内容，请参阅 %%1。
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkText=SAP 帮助
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkTextSapNote3057059=SAP Note 3057059
#XBUT: Open Ticket Button Text
openTicket=创建支持工单
#XBUT: Add Button Text
add=添加
#XBUT: Next Button Text
next=下一步
#XBUT: Edit Button Text
editUsers=编辑
#XBUT: create user Button Text
createUser=创建
#XBUT: Update user Button Text
updateUser=选择
#XTIT: Add HDI Containers Dialog Title
addHDIContainersDialogTitle=添加未分配的 HDI 容器
#XFLD: No data text for add HDI container dialog
addHDIContainersDialogNoDataText=我们无法找到任何未分配的容器。\n 你正在查找的容器可能已经分配到空间。
#YMSE: Error while loading assigned HDI Containers
loadAssignedHDIContainersError=没能加载已分配的 HDI 容器。
#YMSE: Error while loading assigned HDI Containers
loadUnassignedHDIContainersError=没能加载 HDI 容器。
#XMSG: Success message
succeededToAddHDIContainer=已添加 HDI 容器
#XMSG: Success message
succeededToAddHDIContainerPlural=已添加 HDI 容器
#XMSG: Success message
succeededToDeleteHDIContainer=已移除 HDI 容器
#XMSG: Success message
succeededToDeleteHDIContainerPlural=已移除 HDI 容器
#XFLD: Time data section sub headline
timeDataSection=时间表和维
#XFLD: Schemas list entry read audit log enabled indicator
readIndicator=读取
#XFLD: Schemas list entry change audit log enabled indicator
changeIndicator=更改
#XFLD: Remote sources section sub headline
allconnections=连接分配
#XFLD: Remote sources section sub headline
localconnections=本地连接
#XFLD: Remote sources section sub headline for open Connectors
openConnectors=SAP Open Connectors
#XFLD: User section sub headline
memberassignment=成员分配
#XFLD: User assignment section sub headline
userAssignment=用户分配
#XFLD: User section Access dropdown Member
member=成员
#XFLD: User assignment section column name
user=用户名
#XTXT: Selected role count
selectedRoleToolbarText=已选择：{0}
#XTIT: Space detail section connections title
detailsSectionConnections=连接
#XTIT: Space detail section data access title
detailsSectionDataAccess=模式访问
#XTIT: Space detail section time data title
detailsSectionGenerateData=时间数据
#XTIT: Space detail section members title
detailsSectionUsers=成员
#XTIT: Space detail section Users title
detailsSectionUsersTitle=用户
#XTIT: Out of Storage
insufficientStoragePopoverTitle=存储空间不足
#XTIT: Storage distribution
storageDistributionPopoverTitle=已用磁盘存储空间
#XTXT: Out of Storage popover text
insufficientStorageText=要创建新空间，请减少其他空间分配的存储，或删除不再需要的空间。你可以通过调用“管理计划”增加总系统存储。
#XMSG: Space id length warning
spaceIdLengthWarning=已达到 {0} 的最大字符数。
#XMSG: Space name length warning
spaceNameLengthWarning=已达到 {0} 的最大字符数。
#XMSG: Space id reserved prefix warning
spaceIdnWarningReservedPrefix=为了避免可能的冲突，请不要使用 {0} 前缀。
#YMSE: Error while loading open SQL schemas
loadLocalSchemasError=没能加载开放式 SQL 模式。
#YMSE: Error while creating open SQL schema
createLocalSchemaError=没能创建开放式 SQL 模式。
#YMSE: Error while loading all remote connections
loadRemoteConnectionsError=无法加载所有远程连接。
#YMSE: Error while loading space details
loadSpaceDetailsError=没能加载空间详细信息。
#YMSE: Error while deploying space details
deploySpaceDetailsError=没能部署空间。
#YMSE: Error while copying space details
copySpaceDetailsError=没能复制空间。
#YMSE: Error while loading storage data
loadStorageDataError=没能加载存储数据。
#YMSE: Error while loading all users
loadAllUsersError=没能加载所有用户。
#YMSE: Failed to reset password
resetPasswordError=没能重置密码。
#XMSG: Success message after updating space
resetPasswordSuccessMessage=已为模式设置新密码
#YMSE: DP Agent-name too long
DBAgentNameError=数据预配代理的名称过长。
#YMSE: Schema-name not valid.
schemaNameError=模式的名称无效。
#YMSE: User name not valid.
UserNameError=用户名无效。
#XTIT: Consumption by Storage Type
consumptionStorageTypeText=使用量（按存储类型）
#XTIT: Consumption by Schema
consumptionSchemaText=使用量（按模式）
#XTIT: Overall Consumption by schema
overallConsumptionSchemaText=总表使用量（按模式）
#XTIT: Overall Consumption by Table Type
overallConsumptionTableTypeText=总使用量（按表类型）
#XTIT: Tables
tableDetailsText=表详细信息
#XTIT: Table Storage Consumption
tableStorageConsumptionText=表存储使用量
#XFLD: Table Type label
tableTypeLabel=表类型
#XFLD: Schema label
schemaLabel=模式
#XFLD: reset table tooltip
resetTable=重置表
#XFLD: In-Memory label in space monitor
inMemoryLabel=内存
#XFLD: Disk label in space monitor
diskLabel=磁盘
#XFLD: Yes
yesLabel=是
#XFLD: No
noLabel=否
#XFLD: switch label: Want the data in this space to be consumable by default?
allowConsumptionByDefault=是否希望该空间中的数据在默认情况下可使用？
#XFLD: Business Name
businessNameLabel=业务名称
#XFLD: Refresh
refresh=刷新
#XMSG: No filter results title
noFilterResultsTitle=看起来你的筛选器设置未显示任何数据。
#XMSG: No filter results message
noFilterResultsMsg=请尝试细化你的筛选器设置。如果还看不到任何数据，请在数据模型构建器中创建一些表。一旦这些表占用存储空间，你就能在这里监控它们。
#XMSG: Space monitoring Hana state red title
spaceMonitorHanaRedTitle=运行时数据库不可用。
#XMSG: Space monitoring Hana state red message
spaceMonitorHanaRedMsg=由于运行时数据库不可用，特定功能已禁用，我们无法在此页面上显示任何信息。
#YMSE: Error while creating space schema user
createSpaceSchemaUserError=没能创建空间模式用户。
#YMSE: Error User name already exists
userAlreadyExistsError=用户名已存在。
#YMSE: Error Authentication failed
authenticationFailedError=验证失败。
#XMSG: Message strip message when a schema user is locked
userLockedMessageStrip=由于登录失败次数过多，该用户已被锁定。请请求新密码以解锁该用户。
#XMSG: Success message after reset and unlock schema user
resetUnlockPasswordSuccessMessage=已设置新密码并解锁用户
#XMSG: user is locked message
userLockedMessage=用户已锁定。
#XCOL: Users table-view column Role
spaceRole=角色
#XCOL: Users table-view column Scoped Role
spaceScopedRole=范围内角色
#XCOL: Users table-view column Space Admin
spaceAdmin=空间管理员
#XFLD: User section dropdown value Viewer
viewer=查看者
#XFLD: User section dropdown value Modeler
modeler=建模者
#XFLD: User section dropdown value Data Integrator
dataIntegrator=数据集成者
#XFLD: User section dropdown value Space Administrator
spaceAdministrator=空间管理员
#XMSG: Success message -  Space Role updated successfully.
updateUserSpaceRoleSuccess=已更新空间角色
#XMSG: Error message - Failed to update Space Role.
updateUserSpaceRoleError=未成功更新空间角色。
#XFLD:
databaseUserNameSuffix=数据库用户名后缀
#XTXT: Space Schema password text
spaceSchemaPasswordText=要建立与该模式的连接，请复制你的密码。如果你忘记密码，可随时申请新密码。
#XFLD: Cloud Platform label in space schema dialog
cloudPlatformLabel=SAP Cloud Platform
#XTXT: Cloud Platform text in space schema dialog
cloudPlatformText=要设置通过此用户访问，请启用使用并复制凭据。如果你只能复制无密码的凭据，请确保稍后添加密码。
#XFLD: enable consumption in CloudPlatform label in space schema dialog
enableConsumptionInCloudPlatformLabel=启用在 SAP Cloud Platform 中使用
#XFLD: Credentials for User-Provided Service label in space schema dialog
cloudPlatformCredentialsLabel=用户所提供服务的凭据：
#XFLD: Credentials for User-Provided Service (Without Password) label in space schema dialog
cloudPlatformCredentialsWithoutPasswordLabel=用户所提供服务的凭据（无密码）：
#XFLD: Copy Credentials label in space schema dialog
copyCredentialsLabel=复制无密码的凭据
#XFLD: Copy Credentials label in space schema dialog
copyFullCredentialsLabel=复制完整凭据
#XFLD: Copy Password label in space schema dialog
copyPasswordLabel=复制密码
#XMSG: Credentials copied to clipboard
credentialsCopiedMessage=已复制到剪贴板的凭据
#XMSG: Password copied to clipboard
passwordCopiedMessage=已复制到剪贴板的密码
#XMSG: create database user dialog title
createDatabaseUserDialogTitle=创建数据库用户
#XMSG: Database Users section title
databaseUsers=数据库用户
#XMSG: Database User Details dialog title
databaseUserDetailsDialogTitle=数据库用户详细信息
#XFLD: database user read audit log
databaseUserAuditLogRead=启用读取操作的审计日志并将日志保留
#XFLD: database user change audit log
databaseUserAuditLogChange=启用更改操作的审计日志并将日志保留
#XMSG: Cloud Platform Access
cloudPlatformAccess=SAP Cloud Platform 访问
#XFLD: Enable Consumption in Cloud Platform hint
enableConsumptionCloudPlatformHint=设置通过此数据库用户访问你的 HANA 部署基础架构 (HDI) 容器。要连接 HDI 容器，必须启用 SQL 建模
#XFLD: Enable HDI Consumption
enableConsumptionCloudPlatform=启用 HDI 使用
#XFLD: Enable Consumption hint
enableConsumptionHint=是否要让其他工具或应用使用你空间中的数据？
#XFLD: Enable Consumption
enableConsumption=启用 SQL 使用
#XFLD: Enable Modeling
enableModeling=启用 SQL 建模
#XMSG: Privileges for Data Modeling
privilegesModeling=数据引入
#XMSG: Privileges for Data Consumption
privilegesConsumption=外部工具的数据使用
#XFLD: SQL Modeling
sqlModeling=SQL 建模
#XFLD: SQL Consumption
sqlConsumption=SQL 使用
#XFLD: enabled
enabled=已启用
#XFLD: disabled
disabled=已禁用
#XFLD: Edit Privileges
editPrivileges=编辑权限
#XFLD: Open Database Explorer
openDBX=打开数据库资源管理器
#XFLD: create database user hint
databaseCreateHint=请注意，保存后不能再更改用户名。
#XFLD: Internal Schema Name
internalSchemaName=内部模式名称
#YMSE: Failed to load database users
loadDatabaseUserError=没能加载数据库用户
#YMSE: Failed to delete database user
deleteDatabaseUsersError=没能删除数据库用户
#XMSG: Success message after deleting database user
deleteDatabaseUserSuccess=已删除数据库用户
#XMSG: Success message after deleting database users
deleteDatabaseUsersSuccess=已删除数据库用户
#XMSG: Success message after creating database user
createDatabaseUserSuccessMessage=已创建数据库用户
#YMSE: Failed to create database user
createDatabaseUserError=没能创建数据库用户
#XMSG: Success message after updating database user
updateDatabaseUserSuccessMessage=已更新数据库用户
#YMSE: Failed to update database user
updateDatabaseUserError=没能更新数据库用户
#XFLD: HDI Consumption
hdiConsumption=HDI 使用
#XTIT: Space detail section Database Access title
detailsSectionDatabaseAccess=数据库访问
#XTXT: Database users consumption hint
databaseUsersConsumptionHint=设置空间数据默认可用。构建器中的模型会自动允许其他应用或工具使用这些数据。
#XFLD: Default Consumption of Space Data:
defaultConsumptionOfSpaceData=空间数据的默认使用：
#XFLD: Database User Name
databaseUserName=数据库用户名
#XMSG: Database User creation validation error message
databaseUserValidationError=有些字段似乎无效。请检查必填字段以继续。
#XMSG: SQL modeling can’t be enabled since this user has been migrated.
migratedDatabaseUserHint=这个用户已经迁移，没能启用数据引入。
#XBUT: Remove Button Text
remove=移除
#XBUT: Remove Spaces Button Text
removeSpaces=移除空间
#XBUT: Remove Objects Button Text
removeObjects=移除对象
#XMSG: No members have been added yet.
noMembersAssigned=还没有添加成员。
#XMSG: No users have been added yet.
noUsersAssigned=还没有添加用户。
#XMSG: No database users have been created or your filter is not showing any data.
noDatabaseUsers=没有创建数据库用户，或你的筛选没有显示任何数据。
#XMSG: Please enter a user name.
noDatabaseUsername=请输入用户名。
#XMSG: The user name is too long. Please use a shorter one.
databaseUsernameTooLong=用户名太长。请使用较短的名称。
#XMSG: No privileges have been enabled and this database user will have limited functionality. Do you still want to continue?
noPrivilegesConfirmationMessage=没有启用权限，此数据库用户的功能将受限。是否仍要继续？
#XMSG: To enable audit logs for change operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogChangeMessage=要启用更改操作的审计日志，还需启用数据引入。是否执行此操作？
#XMSG: To enable audit logs for read operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogReadMessage=要启用读取操作的审计日志，还需启用数据引入。是否执行此操作？
#XMSG: To enable HDI consumption data ingestion and data consumption needs to be enabled. Do you want to enable all?
enableHDIConsumptionMessage=要启用 HDI 使用，还需启用数据引入和数据使用。是否执行此操作？
#XMSG:
databaseUserPasswordText=要设置与此数据库用户的连接，请复制你的密码。如果你忘记密码，可随时申请新密码。
#XTIT: Space detail section members title
detailsSectionMembers=成员
#XMSG: New password set
newPasswordSet=已设置新密码
#XFLD: Data Ingestion
dataIngestion=数据引入
#XFLD: Data Consumption
dataConsumption=数据使用
#XFLD: Privileges
privileges=权限
#XFLD: Enable Data ingestion
enableDataIngestion=启用数据引入
#XMSG: Log the read and change operations for data ingestion.
databaseUserAuditHint=记录数据引入的读取和更改操作。
#XMSG: Make your space data available in your HDI containers.
databaseUserHDIHint=设置空间数据在 HDI 容器中可用。
#XFLD: Enable Data consumption
enableDataConsumption=允许数据使用
#XMSG: Allow other apps or tools to consume your space data.
enableDataConsumptionHint=允许其他应用或工具使用你的空间数据。
#XMSG: To set up access via this schema user copy the credentials to your user-provided service. In case you can only copy the credentials without a password make sure to add the password later.
databaseUserHDIConsumptionHint=要设置通过此数据库用户访问，请将凭据复制到用户提供的服务。如果你只能复制无密码的凭据，请确保稍后添加密码。
#XFLD: Data Flow Runtime Capacity label in space overview
dataFlowRuntimeCapacity=数据流运行时容量（{0}:{1} 小时/{2} 小时）
#XFLD: Could not load Data Flow Runtime Capacity
dataFlowRuntimeCapacityError=没能加载数据流运行时容量
#XMSG: User can grant data consumption to other users.
databaseUserConsumptionWithGrantOptionHint=用户可将数据使用授予其他用户。
#XFLD: Enable data consumption with grant option
enableConsumptionWithGrantOption=启用带“授予选项”的数据使用
#XMSG: To enable data consumption with grant option data consumption needs to be enabled. Do you want to enable both?
enableConsumptionWithGrantMessage=要启用带授予选项的数据使用，需要启用数据使用。是否两者都启用？
#XFLD: Enable Automated Predictive Library (APL) and Predictive Analysis Library (PAL)
allowUsingScriptServerFunctionality=启用自动化预测库（APL）和预测分析库（PAL）
#XMSG: User can use SAP HANA Cloud embedded machine learning functions.
allowUsingScriptServerFunctionalityHint=用户可以使用 SAP HANA Cloud 嵌入式机器学习功能。
#XFLD: Password Policy
passwordPolicy=密码策略
#XMSG: Password Policy
passwordPolicyHint=在此启用或禁用配置的密码策略。
#XFLD: Enable Password Policy
enablePasswordPolicy=启用密码策略
#XMSG: Read Access to the Space Schema
readAccessTitle=对空间模式进行读取访问
#XMSG: read access hint
readAccessHint=允许数据库用户将外部工具连接到空间模式，并读取公开以供使用的视图。
#XFLD: Space Schema
spaceSchema=空间模式
#XFLD: Enable Read Access (SQL)
enableReadAccess=启用读取访问 (SQL)
#XMSG: Allow the user to grant read access to other users.
withGrantOptionHint=允许用户为其他用户授予读取访问权限。
#XFLD: With Grant Option
withGrantOption=带授予选项
#XMSG: Make your space data available in your HDI containers.
enableHDIConsumptionHint=设置空间数据在 HDI 容器中可用。
#XFLD: Enable HDI Consumption
enableHDIConsumption=启用 HDI 使用
#XMSG: Write Access to the User’s Open SQL Schema
writeAccessTitle=对用户的开放式 SQL 模式进行写入访问
#XMSG: write access hint
writeAccessHint=允许数据库用户将外部工具连接到用户的开放式 SQL 模式，以创建数据实体并引入要在空间中使用的数据。
#XFLD: Open SQL Schema
openSQLSchema=开放式 SQL 模式
#XFLD: Enable Write Access (SQL DDL & DML)
enableWriteAccess=启用写入访问（SQL、DDL 和 DML）
#XMSG: audit hint
auditHint=记录开放式 SQL 模式中的读取和更改操作。
#XMSG: data consumption hint
dataConsumptionHint=默认公开空间中的所有新视图，以供使用。建模者可以通过视图输出侧面板中的 "公开以供使用" 开关为单个视图覆盖此设置。你还可以选择公开视图的格式。
#XFLD: Expose for Consumption by Default
exposeDataConsumption=默认公开以供使用
#XMSG: database users hint consumption hint
databaseUsersHint2New=创建数据库用户，以将外部工具连接到 SAP Datasphere。设置特许权限，以允许用户读取空间数据，并创建数据实体 (DDL)，引入要在空间中使用的数据 (DML)。
#XFLD: Read
read=读取
#XFLD: Read (HDI)
readHDI=读取 (HDI)
#XFLD: Write
write=写入
#XMSG: HDI Containers Hint
HDIContainersHint2=启用对空间中 SAP HANA 部署基础架构 (HDI) 容器的访问。建模者可以使用 HDI 工件作为视图的源，HDI 客户可以访问空间数据。
#XMSG: Open info dialog
openDatabaseUserInfoDialog=打开信息对话框
#XMSG: Database user is locked. Open info dialog to unlock
openLockedDatabaseUserInfoDialog=数据库用户已被锁定。请打开对话框解锁
#XFLD: Table
table=表
#XFLD: Space section partner configuration title
partnerConnectionConfigurationSectionTitle=合作伙伴连接
#XFLD: Space section partner configuration subtitle
partnerConnectionConfigurationSectionSubTitle=合作伙伴连接配置
#XFLD: Space section partner configuration hint
partnerConnectionConfigurationSectionHint=添加 iFrame URL 和图标，定义你自己的合作伙伴连接磁贴。此配置只可用于此租户。
#XFLD: Table Name Field
partnerConnectionConfigurationName=磁贴名称
#XFLD: Table iFrame URL field
partnerConnectionConfigurationiFrameURL=iFrame URL
#XFLD: Table iFrame Post Message Origin field
partnerConnectionConfigurationiFramePostMessageOrigin=iFrame 发布消息源
#XFLD: Table icon/image name
partnerConnectionConfigurationIcon=图标
#XMSG: Table no result found
partnerConnectionConfigurationNotfound=没能找到合作伙伴连接配置。
#XMSG: Partner Connection Confgurations are not available
partnerConnectionConfigurationNotAvailable=运行时数据库不可用时，无法显示合作伙伴连接配置。
#XFLD: Dialog Create Partner Connection Configuration
partnerConnectionConfigurationDialogTitleAdd=创建合作伙伴连接配置
#XFLD: Dialog Create Partner Connection Configuration Upload Icon
partnerConnectionConfigurationButtonUloadPartnerIcon=上载图标
#XFLD: Dialog Create Partner Connection Configuration Select
partnerConnectionConfigurationSelectIcon=选择（最大 200KB）
#XFLD: Dialog Create Partner Connection Configuration Enter a name
partnerConnectionConfigurationNamePlaceholder=合作伙伴磁贴示例
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL
partnerConnectionConfigurationiFrameURLPlaceholder=http://www.example.com
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL Check
partnerConnectionConfigurationiFramePostMessageOriginPlaceholder=.example.com
#XFLD: Dialog Create Partner Connection Configuration Browse
partnerConnectionConfigurationBrowseButton=浏览
#XFLD: Create Partner Connection Configuration Msg
partnerConnectionConfigurationUploadMsg=已成功创建合作伙伴连接配置。
#XMSG: Error while deleting Partner Connection configuration(s)
partnerConnectionConfigurationDeleteError=删除合作伙伴连接配置时出错。
#XMSG: Partner Connection Configuration has been successfully deleted
partnerConnectionConfigurationDeleteSuccess=已成功删除合作伙伴连接配置。
#XMSG: Error while retrieving Partner Connection Configurations
partnerConnectionConfigurationLoadError=获取合作伙伴连接配置时出错。
#XMSG: Error fileSizeLimitExceeded
partnerConnectionConfigurationfileSizeLimitExceeded=文件没能上载，因为其超过大小上限 200KB。
#XBUT: Create Partner Connection Configuration
partnerConnectionConfigurationButtonCreate=创建合作伙伴连接配置
#XBUT: Delete Partner Connection Configuration
partnerConnectionConfigurationButtonDelete=删除合作伙伴连接配置
#XMSG: Partner Tile Creation Failure
upsertPartnerTileFailed=没能创建合作伙伴磁贴。
#XMSG: Partner Tile Deletion Failure
deletePartnerTileFailed=没能删除合作伙伴磁贴。
#XMSG: HANA cloud connector settings reset failure
resetHANASCCSettingsFailed=重置客户 SAP HANA 云连接器设置失败
#XFLD: Workload Class
workloadClass=工作负载类
#XFLD: Workload Management
workloadManagement=工作负载管理
#XFLD: Priority
workloadClassPriority=优先级
#XMSG:
workloadManagementPriorityHint=你可以指定查询数据库时此空间的优先级。输入从 1（最低优先级）到 8（最高优先级）的值。在空间争用可用线程的情况下，优先级较高的空间在优先级较低的空间之前运行。
#XMSG:
workloadClassPriorityHint=你可以指定从 0（最低）到 8（最高）的空间优先级。先执行高优先级空间的语句，然后再执行优先级较低的其他空间的语句。默认优先级为 5。值 9 保留用于系统操作，因此不可用于空间。
#XFLD: Statement Limits
workloadclassStatementLimits=语句限制
#XFLD: Workload Configuration
workloadConfiguration=工作负载配置
#XMSG:
workloadClassStatementLimitsHint=你可以指定空间中并行运行的语句可以耗用的线程和内存 GB 的最大数量（或百分比）。可以输入 0（无限制）和租户中可用总内存和线程总数之间的任意值或百分比。\n\n请注意，如果你指定线程限制，可能会降低性能。\n\n如果你指定内存限制，则达到内存限制的语句不会运行。
#XMSG:
workloadClassStatementLimitsDescription=默认配置提供宽松的资源限制，防止任何单个空间导致系统超载运行。
#XMSG:
workloadClassStatementLimitCustomDescription=可以设置空间中并行运行的语句可以耗用的线程总数和内存限制。
#XMSG:
totalStatementThreadLimitHelpText=线程限制设置过低可能影响语句性能，而设置的值过高或 0 值可能允许空间使用全部可用的系统线程。
#XMSG:
totalStatementMemoryLimitHelpText=内存限制设置过低可能引起内存不足问题，而设置的值过高或 0 值可能允许空间使用所有可用的系统内存。
#XMSG:
totalStatementThreadLimitHelpTextRestricted=输入租户中可用线程总数的百分比，介于 1% 与 70%（或等值数字）之间。线程限制设置过低可能影响语句性能，而设置的值过高可能影响其他空间中语句的性能。
#XMSG:
totalStatementThreadLimitHelpTextDynamic=输入租户中可用线程总数的百分比，介于 1% 与 {0}%（或等值数字）之间。线程限制设置过低可能影响语句性能，而设置的值过高可能影响其他空间中语句的性能。
#XMSG:
totalStatementMemoryLimitHelpTextNew=输入租户中可用的介于 0（无限制） 与内存总数之间的值或百分比。内存限制设置过低可能影响语句性能，而设置的值过高可能影响其他空间中语句的性能。
#XFLD: Total Statement Thread Limit
totalStatementThreadLimit=语句线程总限制
#XFLD: Segmented button label
workloadclassPercentage=%
#XFLD: Segmented button label
workloadclassGigaByte=GB
#XFLD, 80: Segmented button label
workloadclassThreads=线程
#XFLD: Total Statement Memory Limit
totalStatementMemoryLimit=语句内存总限制
#YMSE: Error while loading customer hana info
loadCustomerHanaInfoError=没能加载客户 SAP HANA 信息。
#XMSG:
minimumLimitReached=已达到下限。
#XMSG:
maximumLimitReached=已达到上限。
#XMSG: Name Taken for Technical Name
technical-name-taken=已存在使用你输入的技术名称的连接。请输入其他名称。
#XMSG: Name Too long for Technical Name
technical-name-too-long=你输入的技术名称超过 40 个字符。请输入字符数较少的名称。
#XMSG: Technical name field empty
technical-name-field-empty=请输入技术名称。
#XMSG: Generic error when user enters invalid characters for a field
field-invalid-characters=名称仅可使用字母 (a-z)、数字 (0-9) 和下划线 (_)。
#XMSG: Generic error when user enters invalid initial characters for a field
field-underscore-trim=输入的名称不能以下划线 (_) 开头或结尾。
#XFLD: Checkbox label for Enable Statement Limits
enableStatementLimits=启用语句限制
#XFLD: Connection SubSection Settings Title in space details
connectionSettings=设置
#XMSG: Connections tool hint in Space details section
connectionsToolHint=要创建或编辑连接，从侧边导航打开 "连接" 应用或点击此处：
#XBUT: Connection list Open Connection Tool Button
button-connectiontool=转到连接
#XFLD: Not deployed label on space tile
notDeployedLabel=还没有部署空间。
#XFLD: Not deployed additional text on space tile
notDeployedText=请部署空间。
#XFLD: Corrupt space label on space tile
corruptSpace=出现错误。
#XFLD: Corrupt space additional text on space tile
corruptSpaceHint=尝试重新部署或联系支持人员
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=审计日志数据
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=管理数据
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=其他数据
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=空间中的数据
#XMSG: Unlock Space confirmation text
unlockSpaceConfirmation=是否确实要解锁空间？
#XMSG: Lock Space confirmation text
lockSpaceConfirmation=是否确实要锁定空间？
#XFLD: Lock
lock=锁定
#XFLD: Unlock
unlock=解锁
#XFLD: Locking
locking=锁定
#XMSG: Success message after locking space
lockSpaceSuccessMsg=空间已锁定
#XMSG: Success message after unlocking space
unlockSpaceSuccessMsg=空间已解锁
#XMSG: Success message after locking spaces
lockSpacesSuccessMsg=空间已锁定
#XMSG: Success message after unlocking spaces
unlockSpacesSuccessMsg=空间已解锁
#YMSE: Error while locking a space
lockSpaceError=没能锁定空间。
#YMSE: Error while unlocking a space
unlockSpaceError=没能解锁空间。
#XTIT: popup title Warning
confirmationWarningTitle=警告
#XMSG: Message strip message when a space was locked manually
manualLockedMessage=空间已手动锁定。
#XMSG: Message strip message when a space was locked due to the amount of generated audit logs
auditLogLockedMessage=审计日志使用大量磁盘 (GB)，因此空间已被系统锁定。
#XMSG: Message strip message when a space was locked because the assigned quota was exceeded.
quotaLockedMessage=空间超过为其分配的内存或磁盘存储，因此已被系统锁定。
#XMSG: Unlock Spaces confirmation text
unlockSpacesConfirmation=是否确实要解锁选择的空间？
#XMSG: Lock Spaces confirmation text
lockSpacesConfirmation=是否确实要锁定选择的空间？
#XTIT: Scoped role editor dialog title
scopedRoleEditorDialogTitle=范围内角色编辑器
#XTIT: ECN Management title
ecnManagementTitle=空间和弹性计算节点管理
#XFLD: ECNs
ecns=弹性计算节点
#XFLD: ECN phase Ready
ecnReady=就绪
#XFLD: ECN phase Running
ecnRunning=运行中
#XFLD: ECN phase Initial
ecnInitial=未就绪
#XFLD: ECN phase Starting
ecnStarting=正在启动
#XFLD: ECN phase Starting Failed
ecnStartingFailed=启动失败
#XFLD: ECN phase Stopping
ecnStopping=正在停止
#XFLD: ECN phase Stopping Failed
ecnStoppingFailed=停止失败
#XBTN: Assign Button
assign=分配空间
#XBTN: Start Header-Button
start=启动
#XBTN: Update Header-Button
repair=更新
#XBTN: Stop Header-Button
stop=停止
#XFLD: ECN hours remaining
ecnHoursRemaining=剩余 1000 小时
#XFLD: ECN block-hours remaining
ecnBlockHoursRemaining={0} 个剩余计算区块时数
#XFLD: ECN block-hour remaining
ecnBlockHourRemaining={0} 个剩余计算区块时数
#XTIT: ECN creation dialog title
ecnCreationDialogTitle=创建弹性计算节点
#XTIT: ECN edit dialog title
ecnEditDialogTitle=编辑弹性计算节点
#XTIT: ECN deletion dialog title
ecnDeletionDialogTitle=删除弹性计算节点
#XTIT: ECN Space assignment dialog title
ecnSpaceAssignmentDialogTitle=分配空间
#XFLD: ECN ID
ECNIDLabel=弹性计算节点
#XTXT: Selected toolbar text
selectedToolbarText=已选择：{0}
#XTIT: Elastic Compute Nodes
ECNslong=弹性计算节点
#XTIT: Object assignment - Table header: Objects available
objectsAvailable=对象数
#XTIT: Object assignment - Dialog header text
selectObjects=选择要分配到弹性计算节点的空间和对象：
#XTIT: Object assignment - Table header title: Objects
objects=对象
#XTIT: Object assignment - Table header: Type
type=类型
#XMSG: Message strip delete database user audit warning
deleteDatabaseUserWarningMessageStrip=请注意，删除数据库用户将导致删除生成的全部审计日志条目。如果你想保留审计日志，应考虑在删除数据库用户之前导出这些条目。
#XMSG: Message strip delete HDI container audit logs impact
deleteHDIContainerWarningMessageStrip=请注意，从空间取消分配 HDI 容器将导致删除生成的全部审计日志条目。如果你想保留审计日志，应考虑在取消分配 HDI 容器之前导出这些条目。
#XTXT: All audit logs
allAuditLogs=为空间生成的全部审计日志条目
#XMSG: Message strip disable audit warning
disableAuditWarningMessageStrip=请注意，禁用审计策略（读取或更改操作）将导致删除其所含的全部审计日志条目。如果你想保留审计日志条目，应考虑在禁用审计策略之前导出这些条目。
#XTIT: No Spaces assigned to ECN Illustration title
noSpacesECNTitle=还没有分配空间和对象
#XTIT: No Spaces assigned to ECN Illustration description
noSpacesECNDescription=要开始处理弹性计算节点，请向其分配空间或对象。
#XTIT: No Spaces Illustration title
noSpacesTitle=还没有创建空间
#XTIT: No Spaces Illustration description
noSpacesDescription=要开始获取数据，请创建空间。
#XTIT: No Spaces Illustration title
noDeletedSpacesTitle=回收站已清空
#XTIT: No Spaces Illustration description
noDeletedSpacesDescription=可以从这里恢复已删除的空间。
#XTXT:  {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDatabaseUsers=一旦部署了空间，以下数据库用户将{0}删除且无法恢复：
#XTIT: Delete database users
deleteDatabaseUsersTitle=删除数据库用户
#YMSE: Error if ecn id already exists
ecnIDInputDuplicateError=ID 已存在。
#YMSE: Error if ecn id contains invalid characters
ecnIDInputInvalidError=请只使用小写字符 a - z 和数字 0 - 9
#YMSE: Error if ecn id is too short
ecnIDInputTooShortError=ID 的长度必须至少为 {0} 个字符。
#XMSG: ecn id length warning
ecnIdLengthWarning=已超过最大字符数 {0}。
#XFLD: open System Monitor
systemMonitor=系统监控器
#XFLD: open ECN schedule dialog menu entry
schedule=计划
#XFLD: open create ECN schedule dialog
createSchedule=创建计划
#XFLD: open change ECN schedule dialog
changeSchedule=编辑计划
#XFLD: open delete ECN schedule dialog
deleteSchedule=删除计划
#XFLD: open self assign ECN schedule dialog
selfAssignSchedule=将计划分配给我
#XFLD: open pause ECN schedule dialog
pauseSchedule=暂停计划
#XFLD: open resume ECN schedule dialog
resumeSchedule=恢复计划
#XFLD: View Logs
viewLogs=查看日志
#XFLD: Compute Blocks
computeBlocks=计算区块
#XFLD: Memory label in ECN creation dialog
ecnMemory=内存（GB）
#XFLD: Storage label in ECN creation dialog
ecnStorage=存储（GB）
#XFLD: Number of CPU label in ECN creation dialog
ecnCPUCount=CPU 数量
#XFLD: ECN updated by label
changedBy=更改者
#XFLD: ECN updated on label
changedOn=更改日期
#XMSG: Success message after creating a Elastic Compute Node
createEcnSuccessMessage=已创建弹性计算节点
#YMSE: Error while creating a Elastic Compute Node
createEcnError=没能创建弹性计算节点
#XMSG: Success message after updating a Elastic Compute Node
updateEcnSuccessMessage=弹性计算节点已更新
#XMSG: Error while updating a Elastic Compute Node
udpateEcnError=没能更新弹性计算节点
#XMSG: Success message after deleting a Elastic Compute Node
deleteEcnSuccessMessage=已删除弹性计算节点
#YMSE: Error while deleting a Elastic Compute Node
deleteEcnError=没能删除弹性计算节点
#XMSG: Success message after starting a Elastic Compute Node
startingEcnSuccessMessage=正在启动弹性计算节点
#XMSG: Success message after stopping a Elastic Compute Node
stoppingEcnSuccessMessage=正在停止弹性计算节点
#YMSE: Error while starting a Elastic Compute Node
startingEcnError=没能启动弹性计算节点
#YMSE: Error while stopping a Elastic Compute Node
stoppingEcnError=没能停止弹性计算节点
#XBUT: Add Object button for an ECN
assignObjects=添加对象
#XFLD: Enable auto object assignment label
enableAutoObjAssignment=自动添加所有对象
#XFLD: object type label to be assigned
objectTypeLabel=类型（语义用法）
#XFLD: assigned object type label
assignedObjectTypeLabel=类型
#XFLD: technical name label
TechnicalNameLabel=技术名称
#XFLD: Object selecttion ECN label
objectSelectionECNLabel=选择要添加到弹性计算节点的对象
#XTIT: Add objects dialog title
assignObjectsTitle=分配对象
#XFLD: object label with object count
objectLabel=对象
#XMSG: No objects available to add message.
noObjectsToAssign=没有可用于分配的对象。
#XMSG: No objects assigned message.
noAssignedObjects=没有分配对象。
#XTIT: Remove objects popup title
removeObjectsOrSpacesConfirmationTitle=警告
#XTIT: Delete ECN popup button and title
deleteECNConfirmationButtonAndTitle=删除
#XMSG: Remove objects popup text
removeObjectsConfirmation=是否要移除选定的对象？
#XMSG: Remove spaces popup text
removeSpacesConfirmation=是否要移除选定的空间？
#XBUT: Confirmation button for objects dialog
removeObjectsOrSpacesConfirmationBtn=移除空间
#XMSG: Success message after removing objects
removeObjectsSuccessMessage=已移除公开的对象
#XMSG: Success message after assigning objects
assignObjectsSuccessMessage=已分配公开的对象
#XFLD: auto object assignment label for space tile
autoObjAssignmentLabel=所有公开的对象
#XFLD: Spaces tab label
spacesTabLabel=空间
#XFLD: Exposed objects tab label
consumptionObjectsTabLabel=公开对象
#XMSG: Success message after unassigning spaces
spacesUnassignedSuccessMessage=已移除空间
#XMSG: Success message after unassigning space
spaceUnassignedSuccessMessage=已移除空间
#YMSE: Error while assigning / unassigning spaces
spacesAssignUnassignError=没能分配或移除空间。
#YMSE: Error while removing objects
removeObjectsError=没能分配或移除对象。
#YMSE: Error while removing object
removeObjectError=没能分配或移除对象。
#YMSE: Error when saving the ecn with invalid value
invalidSizingPlanError=先前选择的数字不再有效。请选择有效的数字。
#YMSE: Error when saving the ecn with invalid value
invalidPerformanceClassError=请选择有效的性能类。
#YMSE: Error when saving the ecn with invalid value
performanceClassNotAvailableError=先前选择的性能类 "{0}" 当前无效。请选择有效的性能类。
#XTXT: Delete ECN confirmation text
deleteConfirmationTxtECN=是否确定要删除弹性计算节点？
#XFLD: tooltip for ? button
help=帮助
#XFLD: ECN edit button label
editECN=配置
#XFLD: Technical type label for ERModel
DWC_ERMODEL=实体-关系模型
#XFLD: Technical type label for Local Table
DWC_LOCAL_TABLE=本地表
#XFLD: Technical type label for Remote Table
DWC_REMOTE_TABLE=远程表
#XFLD: Technical type label for Analytic Model
DWC_ANALYTIC_MODEL=分析模型
#XFLD: Technical type label for Task Chain
DWC_TASKCHAIN=任务链
#XFLD: Technical type label for Data Flow
DWC_DATAFLOW=数据流
#XFLD: Technical type label for Replication Flow
DWC_REPLICATIONFLOW=复制流
#XFLD: Technical type label for Transformation Flow
DWC_TRANSFORMATIONFLOW=转换流
#XFLD: Technical type label for Intelligent Lookup
DWC_IDT=智能查找
#XFLD: Technical type label for Repository
DWC_REPOSITORY_PACKAGE=资源库
#XFLD: Technical type label for Enterprise Search
DWC_ESMODEL=企业级搜索
#XFLD: Technical type label for View
DWC_VIEW=视图
#XFLD: Technical type label for Data Product
DWC_DATAPRODUCT=数据产品
#XFLD: Technical type label for Data Access Control
DWC_DAC=数据访问控制
#XFLD: Technical type label for Folder
DWC_FOLDER=文件夹
#XFLD: Technical type label for Data Entity
DWC_BUSINESS_ENTITY=业务实体
#XFLD: Technical type label for Business Entity Variant
DWC_BUSINESS_ENTITY_VARIANT=业务实体变式
#XFLD: Technical type label for Responsibility Scenario
DWC_AUTH_SCENARIO=职责方案
#XFLD: Technical type label for Fact Model
DWC_FACT_MODEL=事实模型
#XFLD: Technical type label for Transformation Flow
DWC_PERSPECTIVE=透视图
#XFLD: Technical type label for Consumption Model
DWC_CONSUMPTION_MODEL=使用模型
#XFLD: Technical type label for Remote Connection
DWC_REMOTECONNECTION=远程连接
#XFLD: Technical type label for Fact Model Variant
DWC_FACT_MODEL_VARIANT=事实模型变式
#XMSG: Schedule created alert message
createScheduleSuccess=计划已创建
#XMSG: Schedule updated alert message
updateScheduleSuccess=计划已更新
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=计划已删除
#XMSG: Schedule takeover success message
ownerChangeScheduleSuccess=已为你分配计划
#XMSG: Message for starting pausing of schedule
pauseScheduleStarted=正在暂停 1 个计划
#XMSG: Message for starting resuming of schedule
resumeScheduleStarted=正在恢复 1 个计划
#XFLD: Segmented button label
availableSpacesButton=可用
#XFLD: Segmented button label
selectedSpacesButton=已选
#XFLD: Visit website button text
visitWebsite=访问网站
#XMSG: Warning message on unselecting source language
sourceLanguageRemovalWarning=之前选择的源语言将被移除。
#XBUT: enable translation confirmation button
enableTranslationConfirmationBtn=启用
#XFLD: ECN performance class label
performanceClassLabel=性能类
#XTXT performance class memory text
memoryText=内存
#XTXT performance class compute text
computeText=计算
#XTXT performance class high-compute text
highComputeText=高性能计算
#XBUT: Recycle Bin Button Text
recycleBin=回收站
#XBUT: Restore Button Text
restore=还原
#XMSG: Warning message for new Workload Management UI
priorityWarning=这个区域为只读模式。可以在系统/配置/工作负载管理区域中更改空间优先级。
#XMSG: Warning message for new Workload Management UI
workloadWarning=这个区域为只读模式。可以在系统/配置/工作负载管理区域中更改空间工作负载配置。
#XCOL: Space table-view column: Apache Spark vCPUs
sparkManagementVCPUs=Apache Spark vCPU
#XCOL: Space table-view column: Apache Spark Memory (GB)
sparkManagementMemory=Apache Spark 内存（GB）
#XCOL: Space type (from Space Capability) for Data Ingestion Spaces
ingestionSpaceType=数据产品引入
#XFLD: Deployment in progress nodatatext for spark table
sparkTableDeploymentInProgress=由于当前正在部署空间，因此没有可用数据
#XFLD: Runtime data being loaded nodatatext for spark table
sparkTableDeploymentLoading=由于当前正在加载空间，因此没有可用数据
#XCOL: Space type (from Space Capability) for BW Private Cloud Edition
BWPCESpaceType=SAP BW
#XBUT: Go to Instance Mappings Button
navToMappingsButton=编辑实例映射
#XFLD: SAP HANA Data Lake Storage label if less than 1GB
dataLakeStorageUsed=< 1 GB
