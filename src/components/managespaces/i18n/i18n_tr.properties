#DO NOT REMOVE________________________
#XFLD: Breadcrumb for space monitoring page
spaceMonitorBreadcrumb=İzleme
#XTXT: Type name for spaces in browser tab page title
space=Alan
#_____________________________________
#XFLD: Spaces label in
spaces=Alanlar
#XFLD: Manage plan button text
manageQuotaButtonText=Planı yönet
#XBUT: Manage resources button
manageResourcesButton=Kaynakları yönet
#XFLD: Create space button tooltip
createSpace=Alan oluştur
#XFLD: Create
create=Oluştur
#XFLD: Deploy
deploy=Dağıt
#XFLD: Page
page=Sayfa
#XFLD: Cancel
cancel=İptal
#XFLD: Update
update=Güncelle
#XFLD: Save
save=Kaydet
#XFLD: OK
ok=Tamam
#XFLD: days
days=Gün
#XFLD: Space tile edit button label
edit=Düzenle
#XFLD: Auto Assign all objects to space
autoAssign=Otomatik tayin
#XFLD: Space tile open monitoring button label
openMonitoring=İzleme
#XFLD: Delete
delete=Sil
#XFLD: Copy Space
copy=Kopyala
#XFLD: Close
close=Kapat
#XCOL: Space table-view column status
status=Durum
#XFLD: Space status active
activeLabel=Etkin
#XFLD: Space status locked
lockedLabel=Kilitli
#XFLD: Space status critical
criticalLabel=Kritik
#XFLD: Space status cold
coldLabel=Az ilgili
#XFLD: Space status deleted
deletedLabel=Silindi
#XFLD: Space status unknown
unknownLabel=Bilinmiyor
#XFLD: Space status ok
okLabel=Sağlıklı
#XFLD: Database user expired
expired=Süresi doldu
#XFLD: deployed
deployed=Dağıtıldı
#XFLD: not deployed
notDeployed=Dağıtılmadı
#XFLD: changes to deploy
changesToDeploy=Dağıtılacak değişiklikler
#XFLD: pending
pending=Dağıtılıyor
#XFLD: designtime error
designtimeError=Tasarım süresi hatası
#XFLD: runtime error
runtimeError=Çalıştırma süresi hatası
#XFLD: Space created by label
createdBy=Oluşturan
#XFLD: Space created on label
createdOn=Oluşturma tarihi
#XFLD: Space deployed on label
deployedOn=Dağıtım tarihi
#XFLD: Space ID label
spaceID=Alan tanıtıcısı
#XFLD: Priority label
priority=Öncelik
#XFLD: Space Priority label
spacePriority=Alan önceliği
#XFLD: Space Configuration label
spaceConfiguration=Alan konfigürasyonu
#XFLD: Not available
notAvailable=Kullanılamıyor
#XFLD: WorkloadType default
default=Varsayılan
#XFLD: WorkloadType custom
custom=Özel
#XFLD: Data Lake option label; refers to component SAP HANA Cloud, Data Lake
dataLakeAccess=Veri gölü erişimi
#XFLD: Translation label
translationLabel=Çeviri
#XFLD: Source language label
sourceLanguageLabel=Kaynak dil
#XFLD: Translation CheckBox label
translationCheckBox=Çeviriyi etkinleştir
#XTXT: Popover hint that space has to be deployed to show user details
openDatabaseUserHintPopoverText=Kullanıcı ayrıntılarına erişmek için alanı dağıtın.
#XTXT: Popover hint that space has to be deployed to open database explorer
openDatabaseExplorerHintPopoverText=Veri tabanı gezginini açmak için alanı dağıtın.
#XTXT: Text for when the Data Lake check box is disabled
dataLakeOptionDisabled=Zaten başka bir alan tarafından kullanılmakta olduğundan, bu alanı kullanarak veri gölüne erişemezsiniz.
#XFLD: Data Lake check box text; refers to component SAP HANA Cloud, Data Lake
dataLakeCheckBox=Veri gölüne erişmek için bu alanı kullanın.
#XFLD: Space Priority minimum label extension
low=Düşük
#XFLD: Space Priority maximum label extension
high=Yüksek
#XFLD: Space name label
spaceName=Alan adı
#XFLD: Enable deploy objects checkbox
enableDeployObjects=Nesneleri dağıt
#XTIT: Copy spaces dialog title
copySpaceDialogTitle={0} alanını kopyala
#XTXT Human readable text for unselected source language
unSelectedSourceLanguage=(Seçilmedi)
#XTXT Human readable text for language code "af"
af=Afrikanca
#XTXT Human readable text for language code "ar"
ar=Arapça
#XTXT Human readable text for language code "bg"
bg=Bulgarca
#XTXT Human readable text for language code "ca"
ca=Katalanca
#XTXT Human readable text for language code "zh"
zh=Basitleştirilmiş Çince
#XTXT Human readable text for language code "zf"
zf=Çince
#XTXT Human readable text for language code "hr"
hr=Hırvatça
#XTXT Human readable text for language code "cs"
cs=Çekçe
#XTXT Human readable text for language code "cy"
cy=Galce
#XTXT Human readable text for language code "da"
da=Danca
#XTXT Human readable text for language code "nl"
nl=Felemenkçe
#XTXT Human readable text for language code "en-UK"
en-UK=İngilizce (Birleşik Krallık)
#XTXT Human readable text for language code "en"
en=İngilizce (Amerika Birleşik Devletleri)
#XTXT Human readable text for language code "et"
et=Estonca
#XTXT Human readable text for language code "fa"
fa=Farsça
#XTXT Human readable text for language code "fi"
fi=Fince
#XTXT Human readable text for language code "fr-CA"
fr-CA=Fransızca (Kanada)
#XTXT Human readable text for language code "fr"
fr=Fransızca
#XTXT Human readable text for language code "de"
de=Almanca
#XTXT Human readable text for language code "el"
el=Yunanca
#XTXT Human readable text for language code "he"
he=İbranice
#XTXT Human readable text for language code "hi"
hi=Hintçe
#XTXT Human readable text for language code "hu"
hu=Macarca
#XTXT Human readable text for language code "is"
is=İzlandaca
#XTXT Human readable text for language code "id"
id=Endonezce
#XTXT Human readable text for language code "it"
it=İtalyanca
#XTXT Human readable text for language code "ja"
ja=Japonca
#XTXT Human readable text for language code "kk"
kk=Kazakça
#XTXT Human readable text for language code "ko"
ko=Korece
#XTXT Human readable text for language code "lv"
lv=Letonca
#XTXT Human readable text for language code "lt"
lt=Litvanyaca
#XTXT Human readable text for language code "ms"
ms=Bahasa Melayu
#XTXT Human readable text for language code "no"
no=Norveççe
#XTXT Human readable text for language code "pl"
pl=Lehçe
#XTXT Human readable text for language code "pt"
pt=Portekizce (Brezilya)
#XTXT Human readable text for language code "pt-PT"
pt-PT=Portekizce (Portekiz)
#XTXT Human readable text for language code "ro"
ro=Rumence
#XTXT Human readable text for language code "ru"
ru=Rusça
#XTXT Human readable text for language code "sr"
sr=Sırpça
#XTXT Human readable text for language code "sh"
sh=Sırpça-Hırvatça
#XTXT Human readable text for language code "sk"
sk=Slovakça
#XTXT Human readable text for language code "sl"
sl=Slovence
#XTXT Human readable text for language code "es"
es=İspanyolca
#XTXT Human readable text for language code "es-MX"
es-MX=İspanyolca (Meksika)
#XTXT Human readable text for language code "sv"
sv=İsveççe
#XTXT Human readable text for language code "th"
th=Tayca
#XTXT Human readable text for language code "tr"
tr=Türkçe
#XTXT Human readable text for language code "uk"
uk=Ukraynaca
#XTXT Human readable text for language code "vi"
vi=Vietnamca
#XTIT: Delete spaces confirmation title
deleteConfirmationHeaderText=Alanları sil
#XTXT: Logically delete space delete confirmation text with space name
logicallyDeleteConfirmationBodyTextWithSpaceName="{0}" alanını geri dönüşüm kutusuna taşımak istediğinizden emin misiniz?
#XTXT: Logically dpace delete confirmation text for multiple spaces
logicallyDeleteConfirmationBodyTextMultiple=Seçilen {0} alanı geri dönüşüm kutusuna taşımak istediğinizden emin misiniz?
#XTXT: Delete space delete confirmation text with space name
deleteConfirmationBodyTextWithSpaceName="{0}" alanını silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.
#XTXT: Delete space delete confirmation text for multiple spaces
deleteConfirmationBodyTextMultiple=Seçilen {0} alanı silmek istediğinizden emin misiniz? Bu işlem geri alınamaz. Şu içerik {1} silinecek:
#XTXT: permanently
permanently=kalıcı olarak
#XTXT: {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDetail=Şu içerik {0} silinecek ve kurtarılamayacak:
#XTXT: Hint Delete space confirmation text
deleteConfirmationHint=Silmeyi teyit etmek için {0} yazın.
#XTXT: Delete space confirmation error check spelling
deleteConfirmationErrorCheckSpelling=Yazımınızı kontrol edin ve tekrar deneyin.
#XTXT: All Spaces
allSpaces=Tüm alanlar
#XTXT: All data
allData=alanda yer alan tüm nesneler ve veriler
#XTXT: All connections
allConnections=Alanda tanımlanan tüm bağlantılar
#XFLD: Space tile selection box tooltip
clickToSelect=Seçmek için tıkla
#XTXT: All database users
allDatabaseUsers=Alanla ilişkili Open SQL şemalarında yer alan nesneler ve veriler
#XFLD: remove members button tooltip
deleteUsers=Üyeleri kaldır
#XTXT: Space long description text
description=Açıklama (azami 4000 karakter)
#XFLD: Add Members button tooltip
addUsers=Üyeler ekle
#XFLD: Add Users button tooltip
addUsersTooltip=Kullanıcılar ekle
#XFLD: Edit Users button tooltip
editUsersTooltip=Kullanıcıları düzenle
#XFLD: Remove Users button tooltip
removeUsersTooltip=Kullanıcıları kaldır
#XFLD: Searchfield placeholder
filter=Ara
#XCOL: Users table-view column health
health=Durum
#XCOL: Users table-view column access
access=Erişim
#XFLD: No user found nodatatext
noDataText=Kullanıcı bulunamadı
#XTIT: Members dialog title
selectUserDialogTitle=Üyeler ekle
#XTIT: User dialog title
addUserDialogTitle=Kullanıcılar ekle
#XTIT: Delete connections confirmation title
deleteConnectionConfirmationHeaderText=Bağlantıları sil
#XTIT: Delete single connection confirmation title
deleteConnectionConfirmationHeaderTextSingle=Bağlantıyı sil
#XTXT: Delete connection delete confirmation text
deleteConnectionConfirmationBodyText=Seçilen bağlantıları silmek istediğinizden emin misiniz? Bağlantılar kalıcı olarak kaldırılacak.
#XTIT: Remote source dialog title
selectRemoteConnectionDialogTitle=Bağlantıları seçin
#XTIT: Share connection dialog title
connectionSharingDialogTitle=Bağlantıyı paylaş
#XTIT: Connections shared from other spaces table title
sharedConnectionsFromOtherSpaces=Paylaşılan bağlantılar
#XFLD: Add remote source button tooltip
addRemoteConnections=Bağlantılar ekle
#XFLD: Delete remote source button tooltip
deleteRemoteConnections=Bağlantıları kaldır
#XFLD: Share remote source button tooltip
shareConnections=Bağlantıları paylaş
#XFLD: Tile-layout tooltip
tileLayout=Kutucuk düzeni
#XFLD: Table-layout tooltip
tableLayout=Tablo düzeni
#XMSG: Success message after creating space
createSpaceSuccessMessage=Alan oluşturuldu
#XMSG: Success message after copying space
copySpaceSuccessMessage="{0}" alanı "{1}" alanına kopyalanıyor
#XMSG: Success message after deploying space
deploymentSuccessMessage=Alan dağıtımı başlatıldı
#XMSG: Success message after Apache Spark update started
sparkUpdateStartedSuccessMessage=Apache Spark güncellemesi başlatıldı
#YMSE: Error while starting Apache Spark update
sparkUpdateStartedErrorMessage=Apache Spark güncellenemedi
#XMSG: Success message after updating space
updateSettingsSuccessMessage=Alan ayrıntıları güncellendi
#XMSG: Success message after unlocking space
unlockSpaceSuccessMessage=Alan kilidi geçici olarak açıldı
#XMSG: Success message after deleting a single space
deleteSpaceSuccessMessage=Alan silindi
#XMSG: Success message after deleting multiple spaces
deleteSpacesSuccessMessage=Alanlar silindi
#XMSG: Success message after restoring a single space
restoreSpaceSuccessMessage=Alan geri yüklendi
#XMSG: Success message after restoring multiple spaces
restoreSpacesSuccessMessage=Alanlar geri yüklendi
#YMSE: Error while updating settings
updateSettingsFailureMessage=Alan ayarları güncellenemedi.
#YMSE: Error while updating data lake option
dataLakeEnabledInOtherSpace=Veri gölü zaten başka bir alana tayin edildi. Veri gölüne bir kerede yalnız bir alan erişebilir.
#YMSE: Error while updating data lake option
virtualTablesExists=Hâlâ sanal tablolara* bağlılıklar mevcut olduğu için veri gölünün bu alandan tayinini kaldıramazsınız. Veri gölünün bu alandan tayinini kaldırmak için sanal tabloları silin.
#YMSE: Error while unlocking space
unlockSpaceFailureMessage=Alan kilidi açılamadı.
#YMSE: Error while creating space
createSpaceError=Alan oluşturulamadı.
#YMSE: Error while creating duplicate space
createDuplicateSpaceError={0} adlı alan zaten mevcut.
#YMSE: Error while deleting a single space
deleteSpaceError=Alan silinemedi.
#YMSE: Error details while deleting a single space
deleteSpaceDetailedError=“{0}” alanınız artık düzgün çalışmıyor. Tekrar silmeyi deneyin. Yine de çalışmazsa yöneticinizden alanınızı silmesini isteyin veya bir talep açın.
#YMSE: Error while deleting a single space in files
deleteSpaceInFilesError=Dosyalar kısmındaki alan verileri silinemedi.
#YMSE: Error while deleting a single space users
deleteSpaceUsersError=Kullanıcılar kaldırılamadı.
#YMSE: Error while deleting a single space schema
deleteSpaceSchemasError=Şemalar kaldırılamadı.
#YMSE: Error while deleting a single space connections
deleteSpaceConnectionsError=Bağlantılar kaldırılamadı.
#YMSE: Error while deleting a single space data
deleteSpaceDataError=Alan verileri silinemedi.
#YMSE: Error while deleting multiple spaces
deleteSpacesError=Alanlar silinemedi.
#YMSE: Error while restoring a single space
restoreSpaceError=Alan geri yüklenemedi.
#YMSE: Error while restoring multiple spaces
restoreSpacesError=Alanlar geri yüklenemedi.
#YMSE: Error while creating users
createUsersError=Kullanıcılar eklenemedi.
#YMSE: Error while removing users
removeUsersError=Kullanıcıları kaldıramadık.
#YMSE: Error while removing user
removeUserError=Kullanıcı kaldırılamadı.
#YMSE: Error assigning roles to a user
roleAssignUnAssignError=Kullanıcıyı kapsamı belirlenen seçili role ekleyemedik. \n\n Kendinizi kapsamı belirlenen bir role ekleyemezsiniz. Yöneticinizden sizi kapsamı belirlenen bir role eklemesini isteyebilirsiniz.
#YMSE: Error assigning user to the space
userAssignError=Kullanıcıyı alana tayin edemedik. \n\n Kullanıcı, kapsamı belirlenen roller çapında izin verilen azami sayıda (100) alana zaten tayin edilmiş.
#YMSE: Error assigning users to the space
usersAssignError=Kullanıcıları alana tayin edemedik. \n\n Kullanıcı, kapsamı belirlenen roller çapında izin verilen azami sayıda (100) alana zaten tayin edilmiş.
#YMSE: Error while fetching users
fetchUserWithScopedRolesError=Kullanıcıları alamadık. Daha sonra tekrar deneyin.
#YMSE: Error while fetching scoped roles
fetchScopedRolesError=Kapsamı belirlenen rolleri alamadık.
#YMSE: Error while fetching members
fetchUserError=Üyeler getirilemedi. Daha sonra tekrar deneyin.
#YMSE: Error while loading run-time database
loadRuntimeError=Çalıştırma süresi veri tabanından bilgileri yükleyemedik.
#YMSE: Error while loading spaces
loadSpacesError=Üzgünüz, alanlarınızı almaya çalışırken hata oluştu.
#YMSE: Error while loading haas resources
loadStorageError=Üzgünüz, depolama verileri almaya çalışırken hata oluştu.
#YMSE: Error no data could be loaded
loadDataError=Üzgünüz, verilerinizi almaya çalışırken hata oluştu.
#XFLD: Click to refresh storage data
clickToRefresh=Tekrar denemek için buraya tıklayın.
#XTIT: Delete space popup title
deleteSpacePopupTitle=Alanı sil
#XCOL: Spaces table-view column name
name=Ad
#XCOL: Spaces table-view deployment status
deploymentStatus=Dağıtım durumu
#XFLD: Disk label in space details
storageLabel=Disk (GB)
#XFLD: In-Memory label in space details
ramLabel=Bellek (GB)
#XFLD: Memory label on space card
memory=Depolama belleği
#XFLD: Storage Assignment label in space details
storrageAssignmentLabel=Alan depolaması
#XFLD: Storage Type label in space details
storageTypeLabel=Depolama türü
#XFLD: Enable Space Quota
enableSpaceQuota=Alan kotasını etkinleştir
#XFLD: No Space Quota
noSpaceQuota=Alan kotası yok
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
diskAndInMemoryText=SAP HANA veri tabanı (disk ve bellek içi)
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
dataLakeFilesText=SAP HANA veri gölü dosyaları
#XFLD: Available scoped roles label
availableRoles=Kullanılabilir olan ve kapsamı belirlenen roller
#XFLD: Selected scoped roles label
selectedRoles=Seçilen ve kapsamı belirlenen roller
#XCOL: Spaces table-view column models
models=Modeller
#XCOL: Spaces table-view column users
users=Kullanıcılar
#XCOL: Spaces table-view column connections
connections=Bağlantılar
#XFLD: Section header overview in space detail
overview=Genel bakış
#XFLD: Section header Apache Spark in space detail
sparkManagementSection=Apache Spark
#XFLD: Section header Apache Spark in space detail
sparkManagementSubSection=Uygulamalar
#XFLD: Section header Apache Spark in space detail
taskAssignmentSubSection=Görev tayini
#XFLD: vCPU label in Apache Spark section
vCPULabel=vCPU'lar
#XFLD: Memory label in Apache Spark section
memoryLabel=Bellek (GB)
#XFLD: space configuration label in Apache Spark section
spaceConfigurationLabel=Alan konfigürasyonu
#XFLD: Space Source label
sparkApplicationLabel=Uygulama
#XFLD: Cluster Size label
clusterSizeLabel=Küme büyüklüğü
#XFLD: Driver label
driverLabel=Sürücü
#XFLD: Executor label
executorLabel=Yürütücü
#XFLD: max label
maxLabel=Azami kullanım
#XFLD: TrF Default label
trFDefaultLabel=Dönüştürme akışı varsayılanı
#XFLD: Merge Default label
mergeDefaultLabel=Birleştirme varsayılanı
#XFLD: Optimize Default label
optimizeDefaultLabel=Varsayılanı optimize et
#XFLD: Deployment Default label
deploymentDefaultLabel=Yerel tablo (dosya) dağıtımı
#XFLD: max spark source label
driverExecutorMaxUsedSparkSource={0} CPU/{1} GB
#XFLD: Object type label
taskObjectTypeLabel=Nesne türü
#XFLD: Task activity label
taskActivityLabel=Aktivite
#XFLD: Task Application ID label
taskApplicationIDLabel=Varsayılan uygulama
#XFLD: Section header in space detail
generalSettings=Genel ayarlar
#XMSG: Message strip message when a space is locked
spaceLockedMessageStrip=Bu alan şu anda sistem tarafından kilitlendi.
#XMSG: Message strip message deploy warning
deployWarningMessageStrip=Bu bölümdeki değişikler hemen dağıtılır.
#XMSG: Message strip message modify space configuration warning
modifySpaceConfigurationMessageStrip=Bu değerleri değiştirmenin performans sorunlarına neden olacağını unutmayın.
#XFLD: Button text to unlock the space again
unlockSpace=Alanın kilidini aç
#XFLD: Info text for audit log formatted message
auditLogText=Okuma veya değiştirme eylemlerini (denetim ilkeleri) kaydetmek için denetim günlüklerini etkinleştirin. Yöneticiler daha sonra kimin hangi eylemi ne zaman gerçekleştirdiğini analiz edebilir.
#XMSG: Message strip  audit warning
auditLogWarningMessageStrip=Denetim günlükleri, kiracınızda çok fazla miktarda disk belleği kullanabilir. Denetim ilkesini (okuma veya değiştirme eylemleri) etkinleştirirseniz hizmet kesintilerine yol açabilecek tam disk kesintilerini önlemek için disk depolama alanı kullanımını (Sistem İzleme kısmında Kullanılan Disk Depolama Alanı kartı aracılığıyla) düzenli olarak izlemeniz gerekir. Denetim ilkesini devre dışı bırakırsanız tüm denetim günlüğü girişleri silinir. Denetim günlüğü girişlerini saklamak istiyorsanız denetim ilkesini devre dışı bırakmadan önce bunları dışa aktarmayı düşünün.
#XFLD: Message strip link text to open in app help to guide user what to do to unlock his space permanently
unlockSpaceHelp=Yardımı göster
#XMSG: Message strip message when a space will be locked again
spaceTemporarilyUnlockedMessageStrip=Bu alan, alan depolamasını aşıyor ve {0} {1} içinde kilitlenecek.
#XMSG: Unit for remaining time until space is locked again
hours=saat
#XMSG: Unit for remaining time until space is locked again
minutes=dakika
#XFLD: Section header in space detail for auditing
detailsSectionAuditing=Denetim
#XFLD: Subsection header in space detail for auditing
auditing=Alan denetim ayarları
#XFLD: Hot space tooltip
hotSpaceCountTooltip=Kritik alanlar: Kullanılan depolama %90'dan fazla.
#XFLD: Green space tooltip
greenSpaceCountTooltip=Sağlıklı alanlar: Kullanılan depolama %6 ile %90 arasında.
#XFLD: Cold space tooltip
coldSpaceCountTooltip=Soğuk alanlar: Kullanılan alan %5 veya daha az.
#XFLD: Critical space tooltip
criticalSpaceCountTooltip=Kritik alanlar: Kullanılan depolama %90'dan fazla.
#XFLD: Green space tooltip
okSpaceCountTooltip=Sağlıklı alanlar: Kullanılan depolama %6 ile %90 arasında.
#XFLD: Locked space tooltip
lockedSpaceCountTooltipNew=Kilitli alanlar: Yetersiz bellek nedeniyle bloke edildi.
#XFLD: Locked space tooltip
lockedSpaceCountTooltip=Kilitli alanlar
#YMSE: Error while deleting remote source
deleteRemoteError=Bağlantılar kaldırılamadı.
#XFLD: Space ID input tooltip
spaceIDInfoTooltip=Alan tanıtıcısı daha sonra değiştirilemez.\nGeçerli karakterler: A - Z, 0 - 9 ve _
#YMSE: Error when space name is empty
spaceNameEmptyValidationError=Alan adı girin.
#YMSE: Error when ECN Business name is empty
ecnNameEmptyValidationError=İş adı girin.
#YMSE: Error when space id is empty
spaceIDInputEmptyValidationError=Alan tanıtıcısı girin.
#YMSE: Error when space id contains invalid characters
spaceIDInputInValidError=Geçersiz karakterler. Yalnızca A - Z, 0 - 9 ve _ kullanın.
#YMSE: Error when space id exists while creation
spaceIDInputDuplicateError=Alan tanıtıcısı zaten mevcut.
#XFLD: Space searchfield placeholder
search=Ara
#XMSG: Success message after creating users
createUsersSuccess=Kullanıcılar eklendi
#XMSG: Success message after creating user
createUserSuccess=Kullanıcı eklendi
#XMSG: Success message after updating users
updateUsersSuccess={0} kullanıcı güncellendi
#XMSG: Success message after updating user
updateUserSuccess=Kullanıcı güncellendi
#XMSG: Success message after removing users
removeUsersSuccess={0} kullanıcı kaldırıldı
#XMSG: Success message after removing user
removeUserSuccess=Kullanıcı kaldırıldı
#XFLD: Schema name
schemaName=Şema adı
#XFLD: used of total
ofTemplate={0} / {1}
#XFLD: Assigned disk label in space overview
assignedStorageTemplate=Tayin edilen disk ({0} / {1})
#XFLD: Assigned Memory label in space overview
assignedRAMTemplate=Tayin edilen bellek ({0} / {1})
#XFLD: Storage ratio on space
accelearationRAM=Bellek hızlandırma
#XFLD: No Storage Consumption
noStorageConsumptionText=Depolama kotası tayin edilmedi.
#XFLD: Used disk label in space overview
usedStorageTemplate=Depolama için kullanılan disk ({0}/{1})
#XFLD: Used Memory label in space overview
usedRAMTemplate=Depolama için kullanılan bellek ({0}/{1})
#XFLD: Used Disk tooltip
usedStorageTooltipTemplate=Depolama için kullanılan disk {0}/{1}
#XFLD: Used Memory tooltip
usedRAMTooltipTemplate={0} / {1} bellek kullanıldı
#XFLD: Assigned Disk tooltip
assignedStorageTooltipTemplate={0} / {1} disk tayin edildi
#XFLD: Assigned Memory tooltip
assignedRAMTooltipTemplate={0} / {1} bellek tayin edildi
#XFLD: Space related used storage tooltip
spaceRelatedUsedStorageTooltipTemplate=Alan verileri: {0}
#XFLD: Not space related used storage tooltip
otherDataUsedStorageTooltipTemplate=Diğer veriler: {0}
#XFLD: Storage used exceeded popover and tooltip
storageUsedExceededText=Planınızı genişletmeyi düşünün veya SAP desteğine başvurun.
#XCOL: Space table-view column used Disk
usedStorage=Depolama için kullanılan disk
#XCOL: Space monitor column used Memory
usedRAM=Depolama için kullanılan bellek
#XCOL: Space monitor column Schema
tableSchema=Şema
#XCOL: Space monitor column Storage Type
tableStorageType=Depolama türü
#XCOL: Space monitor column Table Type
tableType=Tablo türü
#XCOL: Space monitor column Record Count
tableRecordCount=Kayıt sayısı
#XFLD: Assigned Disk
assignedStorage=Depolama için tayin edilen disk
#XFLD: Assigned Memory
assignedRAM=Depolama için tayin edilen bellek
#XCOL: Space table-view column storage utilization
tableStorageUtilization=Kullanılan depolama
#XFLD: space status
spaceStatus=Alan durumu
#XFLD: space type
spaceType=Alan türü
#XFLD: space type label for space with space type "SAP BW bridge" ("SAP BW bridge" is a product name, do not translate it)
abapbridgeSpaceType=SAP BW köprüsü
#XFLD: space type label for space with space type "Data Marketplace"
marketplaceSpaceType=Veri sağlayıcı ürünü
#XMSG: Error message for when selected space cannot be deleted due to the space type
deleteTypedSpaceError=Alan türü {1} olduğundan {0} alanını silemezsiniz.
#XMSG: Error message for when selected spaces cannot be deleted due to the space type
deleteTypedSpacesError={0} seçilen alanı silemezsiniz. Aşağıdaki alan türlerine sahip alanlar silinemez: {1}.
#XFLD: space type label for space without space type
defaultSpaceType=SAP Datasphere
#XFLD: Tooltip for monitor space button
monitorSpace=İzleme
#XFLD: Tooltip for edit space button
editSpace=Alanı düzenle
#XMSG: Deletion warning in messagebox
deleteConfirmation=Bu alanı silmek istediğinizden emin misiniz?
#XFLD: Tooltip for delete space button
deleteSpace=Alanı sil
#XFLD: storage
storage=Depolama için disk
#XFLD: username
userName=Kullanıcı adı
#XFLD: port
port=Port
#XFLD: hostname
hostName=Ana sistem adı
#XFLD: password
password=Parola
#XBUT: Request new password button
requestPassword=Yeni parola talep et
#YEXP: Usage explanation in time data section
timeDataSectionHint=Modellerinizde ve hikayelerinizde kullanmak için zaman tabloları ve boyutlar oluşturun.
#YEXP: Usage explanation in Space schema section
spaceSchemaHint=Alanınızdaki verilerin diğer araçlar veya uygulamalar tarafından tüketilebilir olmasını istiyor musunuz? İstiyorsanız alanınızdaki verilere erişebilecek bir veya daha fazla kullanıcı oluşturun ve varsayılan olarak gelecekteki tüm alan verilerinin tüketilebilir olup olmayacağını seçin.
#XTIT: Create schema popup title
createSchemaDialogTitle=Open SQL şeması oluştur
#XTIT: Generate Time Data dialog title
generateTimeDataDialogTitle=Zaman tabloları ve boyutlar oluştur
#XTIT: Edit Time Data dialog title
editTimeDataDialogTitle=Zaman tablolarını ve boyutları düzenle
#XTIT: Time Data token title
timeDataTokenTitle=Zaman verileri
#XTIT: Time Data token title
timeDataUpdateViews=Zaman verileri görünümlerini güncelle
#XFLD: Time Data token creation in progress label
timeDataCreationInProgress=Oluşturma sürüyor...
#XFLD: Time Data token creation error label
timeDataCreationError=Oluşturma başarısız oldu. Tekrar deneyin.
#XTIT: Time Data dialog time settings title
timeDataDialogTimeSettingsTitle=Zaman tablosu ayarları
#XTIT: Time Data dialog time data table title
timeDataDialogTranslationTablesTitle=Çeviri tabloları
#XTIT: Time Data dialog dimensions title
timeDataDialogTimeViewsTitle=Zaman boyutları
#XFLD: Time Data dialog time range label
timeRangeHint=Zaman aralığını tanımlayın.
#XFLD: Time Data dialog time data table label
timeDataHint=Tablonuzu adlandırın.
#XFLD: Time Data dialog time data dimensions label
timeDimensionsHint=Boyutlarınızı adlandırın.
#XFLD: Time Data Time range description label
timerangeLabel=Zaman aralığı
#XFLD: Time Data dialog from year label
fromYearLabel=Başlangıç yılı
#XFLD: Time Data dialog to year label
toYearLabel=Bitiş yılı
#XFLD: Time Data dialog calendar type label
calendarTypeLabel=Takvim türü
#XFLD: Time Data dialog granularity label
granularityLabel=Granülarite
#XFLD: Time Data dialog technical name label
technicalNameLabel=Teknik ad
#XFLD: Time Data dialog quarters text table lable
quarterTextsTableLabel=Üç aylık dönemler için çeviri tablosu
#XFLD: Time Data dialog months text table lable
monthsTextsTableLabel=Aylar için çeviri tablosu
#XFLD: Time Data dialog days text table lable
daysTextsTableLabel=Günler için çeviri tablosu
#XFLD: Time Data dialog year label
yearLabel=Yıl boyutu
#XFLD: Time Data dialog quarter label
quarterLabel=Üç aylık dönem boyutu
#XFLD: Time Data dialog month label
monthLabel=Ay boyutu
#XFLD: Time Data dialog day label
dayLabel=Gün boyutu
#XFLD: Time Data dialog gregorian calendar type label
gregorian=Miladi
#XFLD: Time Data dialog time granularity day label
day=Gün
#XFLD: Time Data dialog business name maxlength reached
timeRangeValueStateTextMaxLengthBusinessName=1000 karakterlik azami uzunluğa ulaşıldı.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextMaxRangeExceeded=Azami zaman aralığı 150 yıldır.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeLower="Başlangıç yılı”, “Bitiş yılı” değerinden az olmalıdır
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeHigher="Başlangıç yılı" 1900 veya daha büyük olmalıdır.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeHigher="Bitiş yılı”, “Başlangıç yılı” değerinden yüksek olmalıdır
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeLower="Bitiş yılı" bu yıl artı 100'den düşük olmalıdır
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningFromYear="Başlangıç yılı" değerini artırma veri kaybına neden olabilir
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningToYear="Bitiş yılı" değerini düşürme veri kaybına neden olabilir
#XMSG: Time Data creation validation error message
timeDataValidationError=Görünüşe göre bazı alanlar geçersiz. Devam etmek için zorunlu alanları kontrol edin.
#XMSG: Time Data deletion popup text
deleteTimeDataConfirmation=Verileri silmek istediğinizden emin misiniz?
#XMSG: Time Data creation success message
createTimeDataSuccess=Zaman verileri oluşturuldu
#XMSG: Time Data update success message
updateTimeDataSuccess=Zaman verileri güncellendi
#XMSG: Time Data delete success message
deleteTimeDataSuccess=Zaman verileri silindi
#XMSG: Time Data creation error message
createTimeDataError=Zaman verilerini oluşturmaya çalışırken hata oluştu.
#XMSG: Time Data update error message
updateTimeDataError=Zaman verilerini güncellemeye çalışırken hata oluştu.
#XMSG: Time Data creation error message
deleteTimeDataError=Zaman verilerini silmeye çalışırken hata oluştu.
#XMSG: Error message when time data could not be retrieved
retrievingTimeDataError=Zaman verileri yüklenemedi.
#XTIT: Time Data deletion popup title
deleteTimeDataConfirmationTitle=Uyarı
#XTXT: Time Data deletion dependency popup text
deleteTimeDataDependencyText=Başka modellerde kullanıldığından zaman verilerinizi silemedik.
#XTXT: Time Data deletion dependency popup text singular
deleteTimeDataDependencyTextSingular=Başka bir modelde kullanıldığından zaman verilerinizi silemedik.
#XMSG: Validation error when required time data form field is empty
timeDataRequiredField=Bu alan zorunludur ve boş bırakılamaz.
#XFLD: Tooltip for Open in Database Explorer button
openInDBX=Veri tabanı gezgininde aç
#YMSE: Dimension Year
dimensionYearView="Yıl" boyutu
#YMSE: Dimension Year
dimensionQuarterView="Üç aylık dönem" boyutu
#YMSE: Dimension Year
dimensionMonthView="Ay" boyutu
#YMSE: Dimension Year
dimensionDayView="Gün" boyutu
#XFLD: Time Data deletion object title
timeDataUsedIn=({0} modelde kullanılıyor)
#XFLD: Time Data deletion object title singular
timeDataUsedInSingular=(1 modelde kullanılıyor)
#XFLD: Time Data deletion table column provider
provider=Sağlayıcı
#XTIT: Time Data dependency dialog title
timeDataDependencyDialogTitle=Bağlılıklar
#XTIT: Create schema popup title
createSpaceSchemaUserDialogTitle=Alan şeması için kullanıcı oluştur
#XFLD: Create schema button
createSchemaButton=Open SQL şeması oluştur
#XFLD: Generate TimeData button
generateTimeDataButton=Zaman tabloları ve boyutlar oluştur
#XFLD: Show dependencies button
showDependenciesButton=Bağlılıkları göster
#XFLD: tooltip for disabled buttons when user is not member of space
mustBeMemberOfSpaceForOperation=Bu işlemi gerçekleştirmek için kullanıcınız alanın bir üyesi olmalıdır.
#XFLD: Create space schema user button
createSpaceSchemaUserButton=Alan şeması kullanıcısı oluştur
#YMSE: API Schema users load error
loadSchemaUsersError=Kullanıcıların listesi yüklenemedi.
#XTIT: API Schema-config popup title
schemaUserDetailsDialogTitle=Alan şeması kullanıcısı ayrıntıları
#XMSG: API Schema user deletion popup text
deleteSpaceSchemaUserConfirmation=Seçilen kullanıcıyı silmek istediğinizden emin misiniz?
#XMSG: API Schema user deletion confirmation text
userDeleteSuccess=Kullanıcı silindi.
#YMSE: API Schema user deletion error
userDeleteError=Kullanıcı silinemedi.
#XFLD: User deleted
userDeleted=Kullanıcı silindi.
#XTIT: Remove user popup title
removeUserConfirmationTitle=Uyarı
#XMSG: Remove user popup text
removeUserConfirmation=Kullanıcıyı kaldırmak istediğinizden emin misiniz? Kullanıcı ve tayin edilen, kapsamı belirlenen rolleri alandan kaldırılacak.
#XMSG: Remove users popup text
removeUsersConfirmation=Kullanıcıları kaldırmak istediğinizden emin misiniz? Kullanıcılar ve tayin edilen, kapsamı belirlenen rolleri alandan kaldırılacak.
#XBUT: Confirmation button for remove user dialog
removeUserConfirmationBtn=Kaldır
#YMSE: No data text for available roles
noDataAvailableRoles=Alan, kapsama alınmış hiçbir role eklenmedi. \n Alana kullanıcı ekleyebilmek için alanın öncelikle bir veya birden fazla kapsama alınmış role eklenmesi gerekir.
#YMSE: No data text for selected roles
noDataSelectedRoles=Seçilen hiç kapsamı belirlenen rol yok
#XTIT: Schema-config popup title
schemaDetailsDialogTitle=Open SQL şema konfigürasyonu ayrıntıları
#XFLD: Label for Read Audit Log
auditLogRead=Okuma işlemleri için denetim günlüğünü etkinleştir
#XFLD: Label for Change Audit Log
auditLogChange=Değişiklik işlemleri için denetim günlüğünü etkinleştir
#XFLD: Label Audit Log Retention
auditLogRetention=Günlükleri şu süreyle tut:
#XFLD: Label Audit Log Retention Unit
retentionUnit=Gün
#YMSE: Error when log retention time is set incorrectly
incorrectRetentionTime={0} ile {1} arasında bir tamsayı girin
#XFLD: Label for Cross Space Schema Access
spaceSchemaAccess=Alan şeması verilerini tüket
#XTIT: Confirm deactivation of space schema consumption dialog title
disableSpaceSchemaConsumptionDialogTitle=Alan şeması verilerini tüketmeyi durdur
#XTXT: Confirm deactivation of space schema consumption dialog text
disableSpaceSchemaConsumptionDialogText=Bu Open SQL şeması, alan şemanızın verilerini tüketebilir. Tüketimi durdurursanız alan şeması verilerine dayanan modeller artık çalışmayabilir.
#XBUT: Confirmation button for the stop consuming space schema data dialog
stopConsuming=Tüketimi durdur
#XTXT: Tooltip for data lake icon
dataLakeIconTooltip=Bu alan, veri gölüne erişmek için kullanılır
#XFLD: Tooltip for data lake table entry
dataLakeTableEntryTooltip=Veri gölü etkinleştirildi
#XFLD: used In-Memory linit reached tag
usedRamLimitReachedLabel=Bellek limitine ulaşıldı
#XFLD: Space storage Limit reached tag
storageLimitReachedLabel=Depolama limitine ulaşıldı
#XFLD: Minimal Storage Limit Reached tag
minStorageLimitReachedLabel=Asgari depolama limitine ulaşıldı
#XFLD: Space ram tag
ramLimitReachedLabel=Bellek limitine ulaşıldı
#XFLD: Minimal In-Memory Limit Reached tag
minRamLimitReachedLabel=Asgari bellek limitine ulaşıldı
#XFLD: Popover text after click on space storage limit warning
storageLimitReachedText={0} tayin edilen alan depolama limitine ulaştınız. Alana daha fazla depolama tayin edin.
#XFLD: System storage tag
systemStorageLimitReachedLabel=Sistem depolama limitine ulaşıldı
#XMSG: Popover text after click on storage limit warning
systemStorageLimitReachedText={0} sistem depolama limitine ulaştınız. Alana artık daha fazla depolama tayin edemezsiniz.
#XMSG: Schema deletion warning
deleteSchemaConfirmation=Bu Open SQL şemasını silme, tüm depolanan nesneleri ve şemada bakımı yapılan ilişkileri kalıcı olarak silecek. Devam edilsin mi?
#XMSG: Success message after deleting a schema
schemaDeleteSuccess=Şema silindi
#YMSE: Error while deleting schema.
schemaDeleteError=Şema silinemedi.
#XMSG: Success message after update a schema
schemaUpdateSuccess=Şema güncellendi
#YMSE: Error while updating schema.
schemaUpdateError=Şema güncellendi.
#XMSG: Hint when no password is shown in the dialog.
newPasswordHint=Bu şema için parola sağladık. Parolanızı unuttuysanız veya kaybettiyseniz yeni bir parola talep edebilirsiniz. Yeni parolayı kopyalamayı veya kaydetmeyi unutmayın.
#XMSG: Hint when showing the password in the dialog.
savePasswordHint=Parolanızı kopyalayın. Bu şemaya bir bağlantı kurmanız gerekecek. Parolanızı unuttuysanız sıfırlamak için bu iletişim kutusunu açabilirsiniz.
#XFLD,50: Hint on entering schema name in creation dialog
schemaNameHint=Bu ad, şema oluşturulduktan sonra değiştirilemez.
#XFLD: Open SQL Schemas section sub headline
schemasSQL=Open SQL
#XFLD: Space schema section sub headline
schemasSpace=Alan
#XFLD: HDI Container section header
HDIContainers=HDI konteynerleri
#XTXT: Add HDI Containers button tooltip
addHDIContainers=HDI konteynerlerini ekle
#XTXT: Remove HDI Containers button tooltip
removeHDIContainers=HDI konteynerlerini kaldır
#XFLD: Enable HDI Containers feature button
enableHDIContainersButton=Erişimi etkinleştir
#YMSE: No data text for HDI Containers table
noDataHDIContainers=Hiç HDI konteyneri eklenmedi.
#YMSE: No data text for Timedata section
noDataTimedata=Hiç zaman tablosu ve boyut oluşturulmadı.
#YMSE: No data text during runtime not available for Timedata section
runtimeNotAvailableNoDataTimedata=Çalıştırma zamanı veri tabanı kullanılamadığından zaman tabloları ve boyutları yüklenemiyor.
#YMSE: No data text during runtime not available for HDI section
runtimeNotAvailableNoDataHDI=Çalıştırma zamanı veri tabanı kullanılamadığından HDI konteynerleri yüklenemiyor.
#YMSE: Error while fetching HDI Containers
fetchHDIContainersError=HDI konteynerleri alınamadı. Daha sonra tekrar deneyin.
#XFLD Table column header for HDI Container names
HDIContainerName=HDI konteyneri adı
#XFLD: Enable HDI Containers feature dialog title
enableHDIContainersDialogTitle=Erişimi etkinleştir
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText1New=Veri hareketine gerek olmadan HDI konteynerleriniz ve SAP Datasphere alanlarınız arasında veri alışverişi yapmak için SAP Datasphere kiracınızda SAP SQL Data Warehousing'i etkinleştirebilirsiniz.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText2=Bunun için aşağıdaki düğmeye tıklayarak destek talebi açın.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText3New=Talebiniz işlendiğinde, SAP Datasphere çalıştırma süresi veri tabanında bir veya birden fazla yeni HDI konteyneri oluşturmanız gerekir. Bu durumda tüm SAP Datasphere alanlarınız için HDI konteynerleri bölümündeki Erişimi etkinleştir düğmesi, + düğmesiyle değiştirilir ve konteynerlerinizi bir alana ekleyebilirsiniz.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText41=Daha fazla bilgiye mi ihtiyacınız var? Şuraya gidin: %%0. Destek talebinde nelere yer verileceği hakkında ayrıntılı bilgi için bkz. %%1.
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkText=SAP yardımı
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkTextSapNote3057059=3057059 numaralı SAP notu
#XBUT: Open Ticket Button Text
openTicket=Talep aç
#XBUT: Add Button Text
add=Ekle
#XBUT: Next Button Text
next=Sonraki
#XBUT: Edit Button Text
editUsers=Düzenle
#XBUT: create user Button Text
createUser=Oluştur
#XBUT: Update user Button Text
updateUser=Seç
#XTIT: Add HDI Containers Dialog Title
addHDIContainersDialogTitle=Tayin edilmeyen HDI konteynerlerini ekle
#XFLD: No data text for add HDI container dialog
addHDIContainersDialogNoDataText=Tayin edilmeyen konteyner bulamadık. \n Aradığınız konteyner zaten bir alana tayin edilmiş olabilir.
#YMSE: Error while loading assigned HDI Containers
loadAssignedHDIContainersError=Tayin edilen HDI konteynerleri yüklenemedi.
#YMSE: Error while loading assigned HDI Containers
loadUnassignedHDIContainersError=HDI konteynerleri yüklenemedi.
#XMSG: Success message
succeededToAddHDIContainer=HDI konteyneri eklendi
#XMSG: Success message
succeededToAddHDIContainerPlural=HDI konteynerleri eklendi
#XMSG: Success message
succeededToDeleteHDIContainer=HDI konteyneri kaldırıldı
#XMSG: Success message
succeededToDeleteHDIContainerPlural=HDI konteynerleri kaldırıldı
#XFLD: Time data section sub headline
timeDataSection=Zaman tabloları ve boyutlar
#XFLD: Schemas list entry read audit log enabled indicator
readIndicator=Oku
#XFLD: Schemas list entry change audit log enabled indicator
changeIndicator=Değiştir
#XFLD: Remote sources section sub headline
allconnections=Bağlantı tayini
#XFLD: Remote sources section sub headline
localconnections=Yerel bağlantılar
#XFLD: Remote sources section sub headline for open Connectors
openConnectors=SAP Open Connectors
#XFLD: User section sub headline
memberassignment=Üye tayini
#XFLD: User assignment section sub headline
userAssignment=Kullanıcı tayini
#XFLD: User section Access dropdown Member
member=Üye
#XFLD: User assignment section column name
user=Kullanıcı adı
#XTXT: Selected role count
selectedRoleToolbarText=Seçildi: {0}
#XTIT: Space detail section connections title
detailsSectionConnections=Bağlantılar
#XTIT: Space detail section data access title
detailsSectionDataAccess=Şema erişimi
#XTIT: Space detail section time data title
detailsSectionGenerateData=Zaman verileri
#XTIT: Space detail section members title
detailsSectionUsers=Üyeler
#XTIT: Space detail section Users title
detailsSectionUsersTitle=Kullanıcılar
#XTIT: Out of Storage
insufficientStoragePopoverTitle=Depolama alanı dolu
#XTIT: Storage distribution
storageDistributionPopoverTitle=Kullanılan disk depolaması
#XTXT: Out of Storage popover text
insufficientStorageText=Yeni alan oluşturmak için başka bir alanın tayin edilen depolamasını azaltın veya artık ihtiyacınız olmayan bir alanı silin. Plan yönet çağrısı yaparak toplam sistem depolamasını yükseltebilirsiniz.
#XMSG: Space id length warning
spaceIdLengthWarning=Azami {0} karakter aşıldı.
#XMSG: Space name length warning
spaceNameLengthWarning=Azami {0} karakter aşıldı.
#XMSG: Space id reserved prefix warning
spaceIdnWarningReservedPrefix=Olası çakışmalardan kaçınmak için, {0} öneki kullanmayın.
#YMSE: Error while loading open SQL schemas
loadLocalSchemasError=Open SQL şemaları yüklenemedi.
#YMSE: Error while creating open SQL schema
createLocalSchemaError=Open SQL şeması oluşturulamadı.
#YMSE: Error while loading all remote connections
loadRemoteConnectionsError=Tüm uzaktan bağlantılar yüklemedi.
#YMSE: Error while loading space details
loadSpaceDetailsError=Alan ayrıntıları yüklenemedi.
#YMSE: Error while deploying space details
deploySpaceDetailsError=Alan dağıtılamadı.
#YMSE: Error while copying space details
copySpaceDetailsError=Alan kopyalanamadı.
#YMSE: Error while loading storage data
loadStorageDataError=Depolama verileri yüklenemedi.
#YMSE: Error while loading all users
loadAllUsersError=Tüm kullanıcılar yüklenemedi.
#YMSE: Failed to reset password
resetPasswordError=Parola sıfırlanamadı.
#XMSG: Success message after updating space
resetPasswordSuccessMessage=Şema için yeni parola başarıyla ayarlandı
#YMSE: DP Agent-name too long
DBAgentNameError=Veri sağlama aracısının adı çok uzun.
#YMSE: Schema-name not valid.
schemaNameError=Şema adı geçersiz.
#YMSE: User name not valid.
UserNameError=Kullanıcı adı geçersiz.
#XTIT: Consumption by Storage Type
consumptionStorageTypeText=Depolama türüne göre tüketim
#XTIT: Consumption by Schema
consumptionSchemaText=Şemaya göre tüketim
#XTIT: Overall Consumption by schema
overallConsumptionSchemaText=Şemaya göre genel tablo tüketimi
#XTIT: Overall Consumption by Table Type
overallConsumptionTableTypeText=Tablo türüne göre genel tüketim
#XTIT: Tables
tableDetailsText=Tablo ayrıntıları
#XTIT: Table Storage Consumption
tableStorageConsumptionText=Tablo depolama tüketimi
#XFLD: Table Type label
tableTypeLabel=Tablo türü
#XFLD: Schema label
schemaLabel=Şema
#XFLD: reset table tooltip
resetTable=Tabloyu sıfırla
#XFLD: In-Memory label in space monitor
inMemoryLabel=Bellek
#XFLD: Disk label in space monitor
diskLabel=Disk
#XFLD: Yes
yesLabel=Evet
#XFLD: No
noLabel=Hayır
#XFLD: switch label: Want the data in this space to be consumable by default?
allowConsumptionByDefault=Bu alandaki verilerin varsayılan olarak tüketim malzemesi olmasını istiyor musunuz?
#XFLD: Business Name
businessNameLabel=İş adı
#XFLD: Refresh
refresh=Yenile
#XMSG: No filter results title
noFilterResultsTitle=Görünüşe göre filtre ayarlarınızda hiç veri gösterilmiyor.
#XMSG: No filter results message
noFilterResultsMsg=Filtre ayarlarınızı daraltmayı deneyin. Yine de hiçbir veri görmüyorsanız veri oluşturucuda birkaç tablo oluşturun. Tablolar depolama tüketimi yaptığında tabloları burada izleyebilirsiniz.
#XMSG: Space monitoring Hana state red title
spaceMonitorHanaRedTitle=Çalışma süresi veri tabanı kullanılamaz.
#XMSG: Space monitoring Hana state red message
spaceMonitorHanaRedMsg=Çalıştırma süresi veri tabanı kullanılabilir olmadığından bazı özellikler devre dışı bırakıldı, bu nedenle bu sayfada başka bilgi görüntüleyemiyoruz.
#YMSE: Error while creating space schema user
createSpaceSchemaUserError=Alan şeması kullanıcısı oluşturulamadı.
#YMSE: Error User name already exists
userAlreadyExistsError=Kullanıcı adı zaten mevcut.
#YMSE: Error Authentication failed
authenticationFailedError=Kimlik doğrulama başarısız oldu.
#XMSG: Message strip message when a schema user is locked
userLockedMessageStrip=Çok fazla başarısız oturum açmadan dolayı kullanıcı kilitlendi. Kullanıcının kilidini açmak için yeni parola talep edin.
#XMSG: Success message after reset and unlock schema user
resetUnlockPasswordSuccessMessage=Yeni parola belirlendi ve kullanıcının kilidi açıldı
#XMSG: user is locked message
userLockedMessage=Kullanıcı kilitlendi.
#XCOL: Users table-view column Role
spaceRole=Rol
#XCOL: Users table-view column Scoped Role
spaceScopedRole=Kapsamı belirlenen rol
#XCOL: Users table-view column Space Admin
spaceAdmin=Alan yöneticisi
#XFLD: User section dropdown value Viewer
viewer=Görüntüleyici
#XFLD: User section dropdown value Modeler
modeler=Modelleyici
#XFLD: User section dropdown value Data Integrator
dataIntegrator=Veri entegratörü
#XFLD: User section dropdown value Space Administrator
spaceAdministrator=Alan yöneticisi
#XMSG: Success message -  Space Role updated successfully.
updateUserSpaceRoleSuccess=Alan rolü güncellendi
#XMSG: Error message - Failed to update Space Role.
updateUserSpaceRoleError=Alan rolü başarıyla güncellenemedi.
#XFLD:
databaseUserNameSuffix=Veri tabanı kullanıcı adı soneki
#XTXT: Space Schema password text
spaceSchemaPasswordText=Bu şemaya bir bağlantı ayarlamak için parolanızı kopyalayın. Parolanızı unutmanız durumunda her zaman yeni bir parola talep edebilirsiniz.
#XFLD: Cloud Platform label in space schema dialog
cloudPlatformLabel=Cloud Platform
#XTXT: Cloud Platform text in space schema dialog
cloudPlatformText=Erişimi bu kullanıcı aracılığıyla ayarlamak için tüketimi etkinleştirin ve kimlik bilgilerini kopyalayın. Kimlik bilgilerini parola olmadan kopyalamanız durumunda parolayı daha sonra eklediğinizden emin olun.
#XFLD: enable consumption in CloudPlatform label in space schema dialog
enableConsumptionInCloudPlatformLabel=Cloud Platform'da tüketimi etkinleştir
#XFLD: Credentials for User-Provided Service label in space schema dialog
cloudPlatformCredentialsLabel=Kullanıcı tarafından sağlanan hizmet için kimlik bilgileri:
#XFLD: Credentials for User-Provided Service (Without Password) label in space schema dialog
cloudPlatformCredentialsWithoutPasswordLabel=Kullanıcı tarafından sağlanan hizmet için kimlik bilgileri (parola olmadan):
#XFLD: Copy Credentials label in space schema dialog
copyCredentialsLabel=Parola olmadan kimlik bilgilerini kopyala
#XFLD: Copy Credentials label in space schema dialog
copyFullCredentialsLabel=Tüm kimlik bilgilerini kopyala
#XFLD: Copy Password label in space schema dialog
copyPasswordLabel=Parolayı kopyala
#XMSG: Credentials copied to clipboard
credentialsCopiedMessage=Kimlik bilgileri panoya kopyalandı
#XMSG: Password copied to clipboard
passwordCopiedMessage=Parola panoya kopyalandı
#XMSG: create database user dialog title
createDatabaseUserDialogTitle=Veri tabanı kullanıcısı oluştur
#XMSG: Database Users section title
databaseUsers=Veri tabanı kullanıcıları
#XMSG: Database User Details dialog title
databaseUserDetailsDialogTitle=Veri tabanı kullanıcısı ayrıntıları
#XFLD: database user read audit log
databaseUserAuditLogRead=Okuma işlemleri için denetim günlüğünü etkinleştir ve günlükleri şu süreyle tut
#XFLD: database user change audit log
databaseUserAuditLogChange=Değişiklik işlemleri için denetim günlüğünü etkinleştir ve günlükleri şu süreyle tut
#XMSG: Cloud Platform Access
cloudPlatformAccess=Cloud Platform erişimi
#XFLD: Enable Consumption in Cloud Platform hint
enableConsumptionCloudPlatformHint=Bu veri tabanı kullanıcısı aracılığıyla HANA dağıtım altyapısı (HDI) konteynerine erişimi ayarlayın. HDI konteynerinize bağlanmak için SQL modellemesi açık olmalıdır
#XFLD: Enable HDI Consumption
enableConsumptionCloudPlatform=HDI tüketimini etkinleştir
#XFLD: Enable Consumption hint
enableConsumptionHint=Alanınızdaki verilerin diğer araçlar veya uygulamalar tarafından tüketilebilmesini istiyor musunuz?
#XFLD: Enable Consumption
enableConsumption=SQL tüketimini etkinleştir
#XFLD: Enable Modeling
enableModeling=SQL modellemesini etkinleştir
#XMSG: Privileges for Data Modeling
privilegesModeling=Veri alımı
#XMSG: Privileges for Data Consumption
privilegesConsumption=Harici araçlar için veri tüketimi
#XFLD: SQL Modeling
sqlModeling=SQL modellemesi
#XFLD: SQL Consumption
sqlConsumption=SQL tüketimi
#XFLD: enabled
enabled=Etkin
#XFLD: disabled
disabled=Devre dışı
#XFLD: Edit Privileges
editPrivileges=Ayrıcalıkları düzenle
#XFLD: Open Database Explorer
openDBX=Veri tabanı gezginini aç
#XFLD: create database user hint
databaseCreateHint=Kaydetme sonrasında kullanıcı adını yeniden değiştirmenin mümkün olmayacağını unutmayın.
#XFLD: Internal Schema Name
internalSchemaName=Dahili şema adı
#YMSE: Failed to load database users
loadDatabaseUserError=Veri tabanı kullanıcılarını yükleme başarısız oldu
#YMSE: Failed to delete database user
deleteDatabaseUsersError=Veri tabanı kullanıcılarını silme başarısız oldu
#XMSG: Success message after deleting database user
deleteDatabaseUserSuccess=Veri tabanı kullanıcısı silindi
#XMSG: Success message after deleting database users
deleteDatabaseUsersSuccess=Veri tabanı kullanıcıları silindi
#XMSG: Success message after creating database user
createDatabaseUserSuccessMessage=Veri tabanı kullanıcısı oluşturuldu
#YMSE: Failed to create database user
createDatabaseUserError=Veri tabanı kullanıcısı oluşturma başarısız oldu
#XMSG: Success message after updating database user
updateDatabaseUserSuccessMessage=Veri tabanı kullanıcısı güncellendi
#YMSE: Failed to update database user
updateDatabaseUserError=Veri tabanı kullanıcısı güncelleme başarısız oldu
#XFLD: HDI Consumption
hdiConsumption=HDI tüketimi
#XTIT: Space detail section Database Access title
detailsSectionDatabaseAccess=Veri tabanı erişimi
#XTXT: Database users consumption hint
databaseUsersConsumptionHint=Alan verilerinizi varsayılan olarak tüketilebilir yapın. Oluşturuculardaki modeller otomatik olarak verilerin tüketilir olmasına izin verir.
#XFLD: Default Consumption of Space Data:
defaultConsumptionOfSpaceData=Alan verilerinin varsayılan tüketimi:
#XFLD: Database User Name
databaseUserName=Veri tabanı kullanıcı adı
#XMSG: Database User creation validation error message
databaseUserValidationError=Görünüşe göre bazı alanlar geçersiz. Devam etmek için zorunlu alanları kontrol edin.
#XMSG: SQL modeling can’t be enabled since this user has been migrated.
migratedDatabaseUserHint=Bu kullanıcının geçişi yapıldığından veri alımı etkinleştirilemiyor.
#XBUT: Remove Button Text
remove=Kaldır
#XBUT: Remove Spaces Button Text
removeSpaces=Alanları kaldır
#XBUT: Remove Objects Button Text
removeObjects=Nesneleri kaldır
#XMSG: No members have been added yet.
noMembersAssigned=Henüz üye eklenmedi.
#XMSG: No users have been added yet.
noUsersAssigned=Henüz kullanıcı eklenmedi.
#XMSG: No database users have been created or your filter is not showing any data.
noDatabaseUsers=Veri tabanı kullanıcısı oluşturulmadı veya filtreniz hiçbir veriyi göstermiyor.
#XMSG: Please enter a user name.
noDatabaseUsername=Kullanıcı adı girin.
#XMSG: The user name is too long. Please use a shorter one.
databaseUsernameTooLong=Kullanıcı adı çok uzun. Daha kısa ad kullanın.
#XMSG: No privileges have been enabled and this database user will have limited functionality. Do you still want to continue?
noPrivilegesConfirmationMessage=Etkinleştirilen ayrıcalık yok ve bu veri tabanı kullanıcısı sınırlı işlevselliğe sahip. Yine de devam etmek istiyor musunuz?
#XMSG: To enable audit logs for change operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogChangeMessage=Değişiklik işlemleri için denetim günlüğünü etkinleştirmek için veri alımı da etkinleştirilmelidir. Bunu yapmak istiyor musunuz?
#XMSG: To enable audit logs for read operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogReadMessage=Okuma işlemleri için denetim günlüğünü etkinleştirmek için veri alımı da etkinleştirilmelidir. Bunu yapmak istiyor musunuz?
#XMSG: To enable HDI consumption data ingestion and data consumption needs to be enabled. Do you want to enable all?
enableHDIConsumptionMessage=HDI tüketimini etkinleştirmek için veri alımı ve veri tüketimi de etkinleştirilmelidir. Bunu yapmak istiyor musunuz?
#XMSG:
databaseUserPasswordText=Bu veri tabanı kullanıcısına bir bağlantı ayarlamak için parolanızı kopyalayın veya hatırlayın. Parolanızı unutmanız durumunda her zaman yeni bir parola talep edebilirsiniz.
#XTIT: Space detail section members title
detailsSectionMembers=Üyeler
#XMSG: New password set
newPasswordSet=Yeni parola belirlendi
#XFLD: Data Ingestion
dataIngestion=Veri alımı
#XFLD: Data Consumption
dataConsumption=Veri tüketimi
#XFLD: Privileges
privileges=Ayrıcalıklar
#XFLD: Enable Data ingestion
enableDataIngestion=Veri alımını etkinleştir
#XMSG: Log the read and change operations for data ingestion.
databaseUserAuditHint=Veri alımı için okuma ve değişiklik işlemlerinin günlüğünü çıkarın.
#XMSG: Make your space data available in your HDI containers.
databaseUserHDIHint=Alan verilerinizi HDI konteynerlerinizde kullanılabilir hale getirin.
#XFLD: Enable Data consumption
enableDataConsumption=Veri tüketimini etkinleştir
#XMSG: Allow other apps or tools to consume your space data.
enableDataConsumptionHint=Diğer uygulamaların veya araçların alan verilerinizi tüketmesine izin verin.
#XMSG: To set up access via this schema user copy the credentials to your user-provided service. In case you can only copy the credentials without a password make sure to add the password later.
databaseUserHDIConsumptionHint=Erişimi bu veri tabanı kullanıcısı aracılığıyla ayarlamak için kimlik bilgilerini kullanıcı tarafından sağlanan hizmetinize kopyalayın. Kimlik bilgilerini parola olmadan kopyalamanız durumunda parolayı daha sonra eklediğinizden emin olun.
#XFLD: Data Flow Runtime Capacity label in space overview
dataFlowRuntimeCapacity=Veri akışı çalıştırma süresi kapasitesi ({0}: {1} saat / {2} saat)
#XFLD: Could not load Data Flow Runtime Capacity
dataFlowRuntimeCapacityError=Veri akışı çalıştırma süresi kapasitesi yüklenemedi
#XMSG: User can grant data consumption to other users.
databaseUserConsumptionWithGrantOptionHint=Kullanıcı, diğer kullanıcılara veri tüketimi izni verebilir
#XFLD: Enable data consumption with grant option
enableConsumptionWithGrantOption=İzin seçeneğiyle veri tüketimini etkinleştir
#XMSG: To enable data consumption with grant option data consumption needs to be enabled. Do you want to enable both?
enableConsumptionWithGrantMessage=İzin seçeneğiyle veri tüketimini etkinleştirmek için veri tüketiminin etkinleştirilmesi gerekir. İkisini de etkinleştirmek istiyor musunuz?
#XFLD: Enable Automated Predictive Library (APL) and Predictive Analysis Library (PAL)
allowUsingScriptServerFunctionality=Otomatik tahmin kitaplığı (APL) ve öngörülü analiz kitaplığını (PAL) etkinleştir
#XMSG: User can use SAP HANA Cloud embedded machine learning functions.
allowUsingScriptServerFunctionalityHint=Kullanıcı, makine öğrenmesi işlevlerine gömülü SAP HANA Cloud'u kullanabilir.
#XFLD: Password Policy
passwordPolicy=Parola ilkesi
#XMSG: Password Policy
passwordPolicyHint=Burada konfigüre edilen parola ilkesini etkinleştirin veya devre dışı bırakın.
#XFLD: Enable Password Policy
enablePasswordPolicy=Parola ilkesini etkinleştir
#XMSG: Read Access to the Space Schema
readAccessTitle=Alan şemasına yönelik okuma erişimi
#XMSG: read access hint
readAccessHint=Veri tabanı kullanıcısının harici araçları alan şemasına bağlamasına ve tüketime açık görünümleri okumasına izin verin.
#XFLD: Space Schema
spaceSchema=Alan şeması
#XFLD: Enable Read Access (SQL)
enableReadAccess=Okuma erişimini etkinleştir (SQL)
#XMSG: Allow the user to grant read access to other users.
withGrantOptionHint=Kullanıcının diğer kullanıcılara okuma erişimi vermesine izin verin.
#XFLD: With Grant Option
withGrantOption=İzin seçeneği ile
#XMSG: Make your space data available in your HDI containers.
enableHDIConsumptionHint=Alan verilerinizi HDI konteynerlerinizde kullanılabilir hale getirin.
#XFLD: Enable HDI Consumption
enableHDIConsumption=HDI tüketimini etkinleştir
#XMSG: Write Access to the User’s Open SQL Schema
writeAccessTitle=Kullanıcının açık SQL şemasına yönelik yazma erişimi
#XMSG: write access hint
writeAccessHint=Veri birimleri oluşturmak ve alanda kullanım için veri almak amacıyla veri tabanı kullanıcısının, kullanıcının Open SQL şemasına harici araçlar bağlamasına izin verin.
#XFLD: Open SQL Schema
openSQLSchema=Open SQL şeması
#XFLD: Enable Write Access (SQL DDL & DML)
enableWriteAccess=Okuma erişimini etkinleştir (SQL, DDL, & DML)
#XMSG: audit hint
auditHint=Open SQL şemasındaki okuma ve değişiklik işlemlerinin günlüğünü çıkarın.
#XMSG: data consumption hint
dataConsumptionHint=Tüketim için varsayılan olarak alandaki tüm yeni görünümleri gösterin. Modelleyiciler, görünüm çıktı yan panelindeki "Tüketim için göster" anahtarı aracılığıyla bu ayarı münferit görünümler için geçersiz kılabilir. Ayrıca, görünümlerin gösterileceği biçimleri de seçebilirsiniz.
#XFLD: Expose for Consumption by Default
exposeDataConsumption=Varsayılan olarak tüketim için göster
#XMSG: database users hint consumption hint
databaseUsersHint2New=Harici araçları SAP Datasphere ürününe bağlamak için veri tabanı kullanıcıları oluşturun. Kullanıcıların; alan verilerini okumasına ve alanda kullanım için veri birimleri oluşturup (DDL) verileri almasına (DML) olanak tanımak için ayrıcalıklar belirleyin.
#XFLD: Read
read=Okuma
#XFLD: Read (HDI)
readHDI=Okuma (HDI)
#XFLD: Write
write=Yazma
#XMSG: HDI Containers Hint
HDIContainersHint2=Alanınızda SAP HANA Deployment Infrastructure'ınıza (HDI) erişimi etkinleştirin. Modelleyiciler, kaynaklar olarak HDI nesnelerini kullanabilir ve HDI istemcileri alan verilerinize erişebilir.
#XMSG: Open info dialog
openDatabaseUserInfoDialog=Bilgi iletişim penceresini açın
#XMSG: Database user is locked. Open info dialog to unlock
openLockedDatabaseUserInfoDialog=Veri tabanı kullanıcısı kilitli. Kilidini açmak için iletişim penceresini açın
#XFLD: Table
table=Tablo
#XFLD: Space section partner configuration title
partnerConnectionConfigurationSectionTitle=Muhatap bağlantısı
#XFLD: Space section partner configuration subtitle
partnerConnectionConfigurationSectionSubTitle=Muhatap bağlantısı konfigürasyonu
#XFLD: Space section partner configuration hint
partnerConnectionConfigurationSectionHint=Kendi muhatap bağlantısı kutucuğunuzu iFrame URL’nizi ve ikonunuzu ekleyerek tanımlayın. Bu konfigürasyon yalnızca bu kiracı için kullanılabilir.
#XFLD: Table Name Field
partnerConnectionConfigurationName=Kutucuk adı
#XFLD: Table iFrame URL field
partnerConnectionConfigurationiFrameURL=iFrame URL’si
#XFLD: Table iFrame Post Message Origin field
partnerConnectionConfigurationiFramePostMessageOrigin=iFrame kayıt iletisi kaynağı
#XFLD: Table icon/image name
partnerConnectionConfigurationIcon=Simge
#XMSG: Table no result found
partnerConnectionConfigurationNotfound=Muhatap bağlantısı konfigürasyonu bulunamdı.
#XMSG: Partner Connection Confgurations are not available
partnerConnectionConfigurationNotAvailable=Çalıştırma süresi veri tabanı kullanılamadığında muhatap bağlantısı konfigürasyonları görüntülenemez.
#XFLD: Dialog Create Partner Connection Configuration
partnerConnectionConfigurationDialogTitleAdd=Muhatap bağlantısı konfigürasyonu oluştur
#XFLD: Dialog Create Partner Connection Configuration Upload Icon
partnerConnectionConfigurationButtonUloadPartnerIcon=Simge yükle
#XFLD: Dialog Create Partner Connection Configuration Select
partnerConnectionConfigurationSelectIcon=Seç (azami büyüklük 200 KB)
#XFLD: Dialog Create Partner Connection Configuration Enter a name
partnerConnectionConfigurationNamePlaceholder=Muhatap kutucuğu örneği
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL
partnerConnectionConfigurationiFrameURLPlaceholder=http://www.example.com
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL Check
partnerConnectionConfigurationiFramePostMessageOriginPlaceholder=.example.com
#XFLD: Dialog Create Partner Connection Configuration Browse
partnerConnectionConfigurationBrowseButton=Gözat
#XFLD: Create Partner Connection Configuration Msg
partnerConnectionConfigurationUploadMsg=Muhatap bağlantısı konfigürasyonu başarıyla oluşturuldu.
#XMSG: Error while deleting Partner Connection configuration(s)
partnerConnectionConfigurationDeleteError=Muhatap bağlantısı konfigürasyonu/konfigürasyonları silinirken hata oluştu.
#XMSG: Partner Connection Configuration has been successfully deleted
partnerConnectionConfigurationDeleteSuccess=Muhatap bağlantısı konfigürasyonu başarıyla silindi.
#XMSG: Error while retrieving Partner Connection Configurations
partnerConnectionConfigurationLoadError=Muhatap bağlantısı konfigürasyonları alınırken hata oluştu.
#XMSG: Error fileSizeLimitExceeded
partnerConnectionConfigurationfileSizeLimitExceeded=200KB'lik azami boyutu aştığından dosya yüklenemiyor.
#XBUT: Create Partner Connection Configuration
partnerConnectionConfigurationButtonCreate=Muhatap bağlantısı konfigürasyonu oluştur
#XBUT: Delete Partner Connection Configuration
partnerConnectionConfigurationButtonDelete=Muhatap bağlantısı konfigürasyonunu sil.
#XMSG: Partner Tile Creation Failure
upsertPartnerTileFailed=Muhatap kutucuğu oluşturulamadı.
#XMSG: Partner Tile Deletion Failure
deletePartnerTileFailed=Muhatap kutucuğu silinemedi.
#XMSG: HANA cloud connector settings reset failure
resetHANASCCSettingsFailed=Müşteri SAP HANA Cloud Connector ayarlarını sıfırlama başarısız oldu
#XFLD: Workload Class
workloadClass=İş yükü sınıfı
#XFLD: Workload Management
workloadManagement=İş yükü yönetimi
#XFLD: Priority
workloadClassPriority=Öncelik
#XMSG:
workloadManagementPriorityHint=Veri tabanı sorgusunda bu alanın öncelik belirlemesini belirtebilirsiniz. 1 (en düşük öncelik) ve 8 (en yüksek öncelik) arasında bir değer girin. Alanların kullanılabilir iş parçacıkları için rekabet ettiği durumlarda daha yüksek önceliğe sahip alanlar, daha düşük önceliğe sahip alanlardan daha önce çalıştırılır.
#XMSG:
workloadClassPriorityHint=0 (en düşük) ve 8 (en yüksek) arasındaki alan önceliğini belirtebilirsiniz. Yüksek öncelikli olan alan deyimleri, düşük öncelikli olan diğer alan deyimlerinden önce yürütülür. Varsayılan öncelik 5'tir. 9 değeri, sistem işlemleri için rezerve edildiğinden bir alan için kullanılabilir değildir.
#XFLD: Statement Limits
workloadclassStatementLimits=Deyim sınırları
#XFLD: Workload Configuration
workloadConfiguration=İş yükü konfigürasyonu
#XMSG:
workloadClassStatementLimitsHint=Alanda aynı anda çalıştırılan deyimlerin kullanabileceği azami iş parçacığı sayısını (veya yüzdesini) ve bellek GB değerini belirtebilirsiniz. 0 (sınırsız) ile kiracıda kullanılabilen toplam bellek ve iş parçacıkları arasında herhangi bir değer veya yüzde girebilirsiniz. \n\n İş parçacığı sınırı belirtirseniz bunun performansı düşürebileceğini unutmayın. \n\n Bellek sınırı belirtirseniz bellek sınırına ulaşan deyimler çalıştırılmaz.
#XMSG:
workloadClassStatementLimitsDescription=Varsayılan konfigürasyon, herhangi bir alanın sistemi aşırı yüklemesine engel olurken geniş kaynak sınırları sunar.
#XMSG:
workloadClassStatementLimitCustomDescription=Alanda aynı anda çalıştırılan deyimlerin kullanabileceği azami toplam iş parçacığı ve bellek sınırlarını belirleyebilirsiniz.
#XMSG:
totalStatementThreadLimitHelpText=İş parçacığı sınırının çok düşük ayarlanması deyim performansını etkileyebilir. Aşırı yüksek değerler veya 0 ise alanın tüm kullanılabilir sistem iş parçacıklarını kullanmasına izin verebilir.
#XMSG:
totalStatementMemoryLimitHelpText=Bellek sınırının çok düşük ayarlanması yetersiz bellek sorunlarına yol açabilir. Aşırı yüksek değerler veya 0 ise alanın tüm kullanılabilir sistem belleğini kullanmasına izin verebilir.
#XMSG:
totalStatementThreadLimitHelpTextRestricted=Kiracınızda kullanılabilen toplam iş parçacığı sayısına ilişkin %1 ile %70 (veya eş değer sayı) arasında bir yüzde girin. İş parçacığı sınırının çok düşük olarak ayarlanması deyim performansını, aşırı yüksek olarak ayarlanması ise deyimlerin başka alanlardaki performansını etkileyebilir.
#XMSG:
totalStatementThreadLimitHelpTextDynamic=Kiracınızda kullanılabilen toplam iş parçacığı sayısına ilişkin %1 ile %{0} (veya eş değer sayı) arasında bir yüzde girin. İş parçacığı sınırının çok düşük olarak ayarlanması deyim performansını, aşırı yüksek olarak ayarlanması ise deyimlerin başka alanlardaki performansını etkileyebilir.
#XMSG:
totalStatementMemoryLimitHelpTextNew=0 (sınır yok) ile kiracınızda kullanılabilen toplam bellek miktarı arasında bir değer girin. Bellek sınırının çok düşük olarak ayarlanması deyim performansını, aşırı yüksek olarak ayarlanması ise deyimlerin başka alanlardaki performansını etkileyebilir.
#XFLD: Total Statement Thread Limit
totalStatementThreadLimit=Toplam deyim iş parçacığı sınırı
#XFLD: Segmented button label
workloadclassPercentage=%
#XFLD: Segmented button label
workloadclassGigaByte=GB
#XFLD, 80: Segmented button label
workloadclassThreads=İş parçacıkları
#XFLD: Total Statement Memory Limit
totalStatementMemoryLimit=Toplam deyim bellek sınırı
#YMSE: Error while loading customer hana info
loadCustomerHanaInfoError=Müşteri SAP HANA bilgileri yüklenemedi.
#XMSG:
minimumLimitReached=Asgari sınıra ulaşıldı.
#XMSG:
maximumLimitReached=Azami sınıra ulaşıldı.
#XMSG: Name Taken for Technical Name
technical-name-taken=Girdiğiniz teknik ada sahip bir bağlantı zaten var. Başka ad girin.
#XMSG: Name Too long for Technical Name
technical-name-too-long=Girdiğiniz teknik ad 40 karakteri aşıyor. Daha az karakter içeren ad girin.
#XMSG: Technical name field empty
technical-name-field-empty=Teknik ad girin.
#XMSG: Generic error when user enters invalid characters for a field
field-invalid-characters=Ad için yalnızca harfleri (a-z), rakamları (0-9) ve altçizgileri (_) kullanabilirsiniz.
#XMSG: Generic error when user enters invalid initial characters for a field
field-underscore-trim=Girdiğiniz ad bir altçizgi (_) ile başlayamaz veya bitemez.
#XFLD: Checkbox label for Enable Statement Limits
enableStatementLimits=Deyim sınırlarını etkinleştir
#XFLD: Connection SubSection Settings Title in space details
connectionSettings=Ayarlar
#XMSG: Connections tool hint in Space details section
connectionsToolHint=Bağlantılar oluşturmak veya düzenlemek için yandaki gezinme kısmından bağlantılar uygulamasını açın veya şuraya tıklayın:
#XBUT: Connection list Open Connection Tool Button
button-connectiontool=Bağlantılara git
#XFLD: Not deployed label on space tile
notDeployedLabel=Alan henüz dağıtılmadı.
#XFLD: Not deployed additional text on space tile
notDeployedText=Alanı dağıtın.
#XFLD: Corrupt space label on space tile
corruptSpace=Bir şeyler yanlış gitti.
#XFLD: Corrupt space additional text on space tile
corruptSpaceHint=Yeniden dağıtmayı deneyin veya destek ile irtibat kurun
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Denetim günlüğü verileri
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Yönetim verileri
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Diğer veriler
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Alanlardaki veriler
#XMSG: Unlock Space confirmation text
unlockSpaceConfirmation=Alanın kilidini gerçekten açmak istiyor musunuz?
#XMSG: Lock Space confirmation text
lockSpaceConfirmation=Alanı gerçekten kilitlemek istiyor musunuz?
#XFLD: Lock
lock=Kilitle
#XFLD: Unlock
unlock=Kilidi aç
#XFLD: Locking
locking=Kilitleme
#XMSG: Success message after locking space
lockSpaceSuccessMsg=Alan kilitlendi
#XMSG: Success message after unlocking space
unlockSpaceSuccessMsg=Alanın kilidi açıldı
#XMSG: Success message after locking spaces
lockSpacesSuccessMsg=Alanlar kilitlendi
#XMSG: Success message after unlocking spaces
unlockSpacesSuccessMsg=Alanların kilidi açıldı
#YMSE: Error while locking a space
lockSpaceError=Alan kilitlenemez.
#YMSE: Error while unlocking a space
unlockSpaceError=Alanların kilidi açılamaz.
#XTIT: popup title Warning
confirmationWarningTitle=Uyarı
#XMSG: Message strip message when a space was locked manually
manualLockedMessage=Alan manüel olarak kilitlenmiş.
#XMSG: Message strip message when a space was locked due to the amount of generated audit logs
auditLogLockedMessage=Denetim günlükleri için büyük miktarda disk alanı (GB) kullanıldığından, alan sistem tarafından kilitlenmiş.
#XMSG: Message strip message when a space was locked because the assigned quota was exceeded.
quotaLockedMessage=Alan bellek veya disk depolaması dağıtımlarını aştığından sistem tarafından kilitlenmiş.
#XMSG: Unlock Spaces confirmation text
unlockSpacesConfirmation=Seçilen alanların kilidini gerçekten açmak istiyor musunuz?
#XMSG: Lock Spaces confirmation text
lockSpacesConfirmation=Seçilen alanı gerçekten kilitlemek istiyor musunuz?
#XTIT: Scoped role editor dialog title
scopedRoleEditorDialogTitle=Kapsamı belirlenen rol düzenleyicisi
#XTIT: ECN Management title
ecnManagementTitle=Alan ve esnek işlem düğümü yönetimi
#XFLD: ECNs
ecns=Esnek işlem düğümleri
#XFLD: ECN phase Ready
ecnReady=Hazır
#XFLD: ECN phase Running
ecnRunning=Çalışıyor
#XFLD: ECN phase Initial
ecnInitial=Hazır değil
#XFLD: ECN phase Starting
ecnStarting=Başlatılıyor
#XFLD: ECN phase Starting Failed
ecnStartingFailed=Başlatma başarısız oldu
#XFLD: ECN phase Stopping
ecnStopping=Durduruluyor
#XFLD: ECN phase Stopping Failed
ecnStoppingFailed=Durdurma başarısız oldu
#XBTN: Assign Button
assign=Alan tayin et
#XBTN: Start Header-Button
start=Başlat
#XBTN: Update Header-Button
repair=Güncelle
#XBTN: Stop Header-Button
stop=Durdur
#XFLD: ECN hours remaining
ecnHoursRemaining=1.000 saat kaldı
#XFLD: ECN block-hours remaining
ecnBlockHoursRemaining={0} blok saat kaldı
#XFLD: ECN block-hour remaining
ecnBlockHourRemaining={0} blok saat kaldı
#XTIT: ECN creation dialog title
ecnCreationDialogTitle=Esnek işlem düğümü oluştur
#XTIT: ECN edit dialog title
ecnEditDialogTitle=Esnek işlem düğümünü düzenle
#XTIT: ECN deletion dialog title
ecnDeletionDialogTitle=Esnek işlem düğümünü sil
#XTIT: ECN Space assignment dialog title
ecnSpaceAssignmentDialogTitle=Alan tayin et
#XFLD: ECN ID
ECNIDLabel=Esnek işlem düğümü
#XTXT: Selected toolbar text
selectedToolbarText=Seçildi: {0}
#XTIT: Elastic Compute Nodes
ECNslong=Esnek işlem düğümleri
#XTIT: Object assignment - Table header: Objects available
objectsAvailable=Nesne sayısı
#XTIT: Object assignment - Dialog header text
selectObjects=Esnek işlem düğümünüzü tayin etmek istediğiniz alanları ve nesneleri seçin:
#XTIT: Object assignment - Table header title: Objects
objects=Nesneler
#XTIT: Object assignment - Table header: Type
type=Tür
#XMSG: Message strip delete database user audit warning
deleteDatabaseUserWarningMessageStrip=Bir veri tabanı kullanıcısının silinmesinin, oluşturulan tüm denetim günlüğü girişlerinin silinmesine yol açacağını unutmayın. Denetim günlüklerini tutmak istiyorsanız veri tabanı kullanıcısını silmeden önce bunları dışa aktarmayı göz önünde bulundurun.
#XMSG: Message strip delete HDI container audit logs impact
deleteHDIContainerWarningMessageStrip=Bir HDI konteynerinin alandaki tayininin kaldırılmasının, oluşturulan tüm denetim günlüğü girişlerinin silinmesine yol açacağını unutmayın. Denetim günlüklerini tutmak istiyorsanız HDI konteynerinin tayinini kaldırmadan önce bunları dışa aktarmayı göz önünde bulundurun.
#XTXT: All audit logs
allAuditLogs=Alan için oluşturulan tüm denetim günlüğü girişleri
#XMSG: Message strip disable audit warning
disableAuditWarningMessageStrip=Bir denetim ilkesinin devre dışı bırakılmasının (okuma veya değişiklik işlemleri), ilgili tüm denetim günlüğü girişlerinin silinmesine yol açacağını unutmayın. Denetim günlüğü girişlerini tutmak istiyorsanız denetim ilkesini devre dışı bırakmadan önce bunları dışa aktarmayı göz önünde bulundurun.
#XTIT: No Spaces assigned to ECN Illustration title
noSpacesECNTitle=Henüz hiç alan veya nesne tayin edilmedi
#XTIT: No Spaces assigned to ECN Illustration description
noSpacesECNDescription=Esnek işlem düğümünüzle çalışmaya başlamak için düğüme bir alan veya nesneler tayin edin.
#XTIT: No Spaces Illustration title
noSpacesTitle=Henüz hiç alan oluşturulmadı
#XTIT: No Spaces Illustration description
noSpacesDescription=Veri almaya başlamak için alan oluşturun.
#XTIT: No Spaces Illustration title
noDeletedSpacesTitle=Geri dönüşüm kutusu boş
#XTIT: No Spaces Illustration description
noDeletedSpacesDescription=Silinen alanlarınızı buradan kurtarabilirsiniz.
#XTXT:  {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDatabaseUsers=Alan dağıtıldıktan sonra şı veri tabanı kullanıcıları {0} silinecek ve kurtarılamayacak:
#XTIT: Delete database users
deleteDatabaseUsersTitle=Veri tabanı kullanıcıkarını sil
#YMSE: Error if ecn id already exists
ecnIDInputDuplicateError=Tanıtıcı zaten mevcut.
#YMSE: Error if ecn id contains invalid characters
ecnIDInputInvalidError=Yalnızca a - z arasındaki küçük harfleri ve 0 – 9 arasındaki rakamları kullanın.
#YMSE: Error if ecn id is too short
ecnIDInputTooShortError=Tanıtıcı en az {0} karakter uzunluğunda olmalıdır.
#XMSG: ecn id length warning
ecnIdLengthWarning=Azami {0} karakter aşıldı.
#XFLD: open System Monitor
systemMonitor=Sistem izleme
#XFLD: open ECN schedule dialog menu entry
schedule=Planla
#XFLD: open create ECN schedule dialog
createSchedule=Planlama oluştur
#XFLD: open change ECN schedule dialog
changeSchedule=Planlamayı düzenle
#XFLD: open delete ECN schedule dialog
deleteSchedule=Planlamayı sil
#XFLD: open self assign ECN schedule dialog
selfAssignSchedule=Planlamayı bana tayin et
#XFLD: open pause ECN schedule dialog
pauseSchedule=Planlamayı duraklat
#XFLD: open resume ECN schedule dialog
resumeSchedule=Planlamayı sürdür
#XFLD: View Logs
viewLogs=Günlükleri görüntüle
#XFLD: Compute Blocks
computeBlocks=İşlem blokları
#XFLD: Memory label in ECN creation dialog
ecnMemory=Bellek (GB)
#XFLD: Storage label in ECN creation dialog
ecnStorage=Depolama (GB)
#XFLD: Number of CPU label in ECN creation dialog
ecnCPUCount=CPU sayısı
#XFLD: ECN updated by label
changedBy=Değiştiren
#XFLD: ECN updated on label
changedOn=Değişiklik zamanı
#XMSG: Success message after creating a Elastic Compute Node
createEcnSuccessMessage=Esnek işlem düğümü oluşturuldu
#YMSE: Error while creating a Elastic Compute Node
createEcnError=Esnek işlem düğümü oluşturulamadı
#XMSG: Success message after updating a Elastic Compute Node
updateEcnSuccessMessage=Esnek işlem düğümü güncellendi
#XMSG: Error while updating a Elastic Compute Node
udpateEcnError=Esnek işlem düğümü güncellenemedi
#XMSG: Success message after deleting a Elastic Compute Node
deleteEcnSuccessMessage=Esnek işlem düğümü silindi
#YMSE: Error while deleting a Elastic Compute Node
deleteEcnError=Esnek işlem düğümü silinemedi
#XMSG: Success message after starting a Elastic Compute Node
startingEcnSuccessMessage=Esnek işlem düğümünü başlatılıyor
#XMSG: Success message after stopping a Elastic Compute Node
stoppingEcnSuccessMessage=Esnek işlem düğümü durduruluyor
#YMSE: Error while starting a Elastic Compute Node
startingEcnError=Esnek işlem düğümü başlatılamadı
#YMSE: Error while stopping a Elastic Compute Node
stoppingEcnError=Esnek işlem düğümü durdurulamadı
#XBUT: Add Object button for an ECN
assignObjects=Nesne ekle
#XFLD: Enable auto object assignment label
enableAutoObjAssignment=Tüm nesneleri otomatik olarak ekle
#XFLD: object type label to be assigned
objectTypeLabel=Tür (semantik kullanımı)
#XFLD: assigned object type label
assignedObjectTypeLabel=Tür
#XFLD: technical name label
TechnicalNameLabel=Teknik ad
#XFLD: Object selecttion ECN label
objectSelectionECNLabel=Esnek işlem düğümüne eklemek istediğiniz nesneleri seçin
#XTIT: Add objects dialog title
assignObjectsTitle=Şuna ilişkin nesneleri tayin et:
#XFLD: object label with object count
objectLabel=Nesne
#XMSG: No objects available to add message.
noObjectsToAssign=Tayin edilebilecek nesne yok.
#XMSG: No objects assigned message.
noAssignedObjects=Nesne tayin edilmedi.
#XTIT: Remove objects popup title
removeObjectsOrSpacesConfirmationTitle=Uyarı
#XTIT: Delete ECN popup button and title
deleteECNConfirmationButtonAndTitle=Sil
#XMSG: Remove objects popup text
removeObjectsConfirmation=Seçilen nesneleri kaldırmayı gerçekten istiyor musunuz?
#XMSG: Remove spaces popup text
removeSpacesConfirmation=Seçilen alanları kaldırmayı gerçekten istiyor musunuz?
#XBUT: Confirmation button for objects dialog
removeObjectsOrSpacesConfirmationBtn=Alanları kaldır
#XMSG: Success message after removing objects
removeObjectsSuccessMessage=Gösterilen nesneler kaldırıldı
#XMSG: Success message after assigning objects
assignObjectsSuccessMessage=Gösterilen nesneler tayin edildi
#XFLD: auto object assignment label for space tile
autoObjAssignmentLabel=Gösterilen tüm nesneler
#XFLD: Spaces tab label
spacesTabLabel=Alanlar
#XFLD: Exposed objects tab label
consumptionObjectsTabLabel=Gösterilen nesneler
#XMSG: Success message after unassigning spaces
spacesUnassignedSuccessMessage=Alanlar kaldırıldı
#XMSG: Success message after unassigning space
spaceUnassignedSuccessMessage=Alan kaldırıldı
#YMSE: Error while assigning / unassigning spaces
spacesAssignUnassignError=Alanlar tayin edilemedi veya kaldırılamadı.
#YMSE: Error while removing objects
removeObjectsError=Nesneleri tayin edemedik veya kaldıramadık.
#YMSE: Error while removing object
removeObjectError=Nesneyi tayin edemedik veya kaldıramadık.
#YMSE: Error when saving the ecn with invalid value
invalidSizingPlanError=Daha önce seçilen numara artık geçerli değil. Geçerli bir numara seçin.
#YMSE: Error when saving the ecn with invalid value
invalidPerformanceClassError=Geçerli bir performans sınıfı seçin.
#YMSE: Error when saving the ecn with invalid value
performanceClassNotAvailableError=Daha önce seçilen performans sınıfı "{0}" şu anda geçerli değil. Geçerli bir performans sınıfı seçin.
#XTXT: Delete ECN confirmation text
deleteConfirmationTxtECN=Esnek işlem düğümünü silmek istediğinizden emin misiniz?
#XFLD: tooltip for ? button
help=Yardım
#XFLD: ECN edit button label
editECN=Konfigüre et
#XFLD: Technical type label for ERModel
DWC_ERMODEL=Birim - ilişki modeli
#XFLD: Technical type label for Local Table
DWC_LOCAL_TABLE=Yerel tablo
#XFLD: Technical type label for Remote Table
DWC_REMOTE_TABLE=Uzak tablo
#XFLD: Technical type label for Analytic Model
DWC_ANALYTIC_MODEL=Analiz modeli
#XFLD: Technical type label for Task Chain
DWC_TASKCHAIN=Görev zinciri
#XFLD: Technical type label for Data Flow
DWC_DATAFLOW=Veri akışı
#XFLD: Technical type label for Replication Flow
DWC_REPLICATIONFLOW=Çoğaltma akışı
#XFLD: Technical type label for Transformation Flow
DWC_TRANSFORMATIONFLOW=Dönüştürme akışı
#XFLD: Technical type label for Intelligent Lookup
DWC_IDT=Akıllı arama
#XFLD: Technical type label for Repository
DWC_REPOSITORY_PACKAGE=Havuz
#XFLD: Technical type label for Enterprise Search
DWC_ESMODEL=Enterprise Search
#XFLD: Technical type label for View
DWC_VIEW=Görünüm
#XFLD: Technical type label for Data Product
DWC_DATAPRODUCT=Veri ürünü
#XFLD: Technical type label for Data Access Control
DWC_DAC=Veri erişimi denetimi
#XFLD: Technical type label for Folder
DWC_FOLDER=Klasör
#XFLD: Technical type label for Data Entity
DWC_BUSINESS_ENTITY=İş birimi
#XFLD: Technical type label for Business Entity Variant
DWC_BUSINESS_ENTITY_VARIANT=İş birimi varyantı
#XFLD: Technical type label for Responsibility Scenario
DWC_AUTH_SCENARIO=Sorumluluk senaryosu
#XFLD: Technical type label for Fact Model
DWC_FACT_MODEL=Olgu modeli
#XFLD: Technical type label for Transformation Flow
DWC_PERSPECTIVE=Perspektif
#XFLD: Technical type label for Consumption Model
DWC_CONSUMPTION_MODEL=Tüketim modeli
#XFLD: Technical type label for Remote Connection
DWC_REMOTECONNECTION=Uzaktan bağlantı
#XFLD: Technical type label for Fact Model Variant
DWC_FACT_MODEL_VARIANT=Olgu modeli varyantı
#XMSG: Schedule created alert message
createScheduleSuccess=Planlama oluşturuldu
#XMSG: Schedule updated alert message
updateScheduleSuccess=Planlama güncellendi
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Planlama silindi
#XMSG: Schedule takeover success message
ownerChangeScheduleSuccess=Planlama size tayin edildi
#XMSG: Message for starting pausing of schedule
pauseScheduleStarted=1 planlama duraklatılıyor
#XMSG: Message for starting resuming of schedule
resumeScheduleStarted=1 planlama sürdürülüyor
#XFLD: Segmented button label
availableSpacesButton=Kullanılabilir
#XFLD: Segmented button label
selectedSpacesButton=Seçildi
#XFLD: Visit website button text
visitWebsite=Web sitesini ziyaret et
#XMSG: Warning message on unselecting source language
sourceLanguageRemovalWarning=Önceden seçilen kaynak diliniz kaldırılacak.
#XBUT: enable translation confirmation button
enableTranslationConfirmationBtn=Etkinleştir
#XFLD: ECN performance class label
performanceClassLabel=Performans sınıfı
#XTXT performance class memory text
memoryText=Bellek
#XTXT performance class compute text
computeText=İşlem
#XTXT performance class high-compute text
highComputeText=Yüksek işlem
#XBUT: Recycle Bin Button Text
recycleBin=Geri dönüşüm kutusu
#XBUT: Restore Button Text
restore=Geri yükle
#XMSG: Warning message for new Workload Management UI
priorityWarning=Bu alan salt okunurdur. Sistem / Konfigürasyon / İş yükü yönetimi alanında alan önceliğini değiştirebilirsiniz.
#XMSG: Warning message for new Workload Management UI
workloadWarning=Bu alan salt okunurdur. Sistem / Konfigürasyon / İş yükü yönetimi alanında alan iş yükü konfigürasyonunu değiştirebilirsiniz.
#XCOL: Space table-view column: Apache Spark vCPUs
sparkManagementVCPUs=Apache Spark vCPU'ları
#XCOL: Space table-view column: Apache Spark Memory (GB)
sparkManagementMemory=Apache Spark belleği (GB)
#XCOL: Space type (from Space Capability) for Data Ingestion Spaces
ingestionSpaceType=Veri ürünü alımı
#XFLD: Deployment in progress nodatatext for spark table
sparkTableDeploymentInProgress=Alan şu anda dağıtılmakta olduğundan kullanılabilir veri yok
#XFLD: Runtime data being loaded nodatatext for spark table
sparkTableDeploymentLoading=Alan şu anda yüklenmekte olduğundan kullanılabilir veri yok
#XCOL: Space type (from Space Capability) for BW Private Cloud Edition
BWPCESpaceType=SAP BW
#XBUT: Go to Instance Mappings Button
navToMappingsButton=Örnek eşlemelerini düzenle
#XFLD: SAP HANA Data Lake Storage label if less than 1GB
dataLakeStorageUsed=< 1 GB
