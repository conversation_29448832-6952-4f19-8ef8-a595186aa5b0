#DO NOT REMOVE________________________
#XFLD: Breadcrumb for space monitoring page
spaceMonitorBreadcrumb=Stebėjimas
#XTXT: Type name for spaces in browser tab page title
space=<PERSON>tis
#_____________________________________
#XFLD: Spaces label in
spaces=Vietos
#XFLD: Manage plan button text
manageQuotaButtonText=Valdyti planą
#XBUT: Manage resources button
manageResourcesButton=<PERSON>dyti išteklius
#XFLD: Create space button tooltip
createSpace=Kurti vietą
#XFLD: Create
create=Kurti
#XFLD: Deploy
deploy=Diegti
#XFLD: Page
page=Puslapis
#XFLD: Cancel
cancel=Atšaukti
#XFLD: Update
update=Naujinti
#XFLD: Save
save=Įrašyti
#XFLD: OK
ok=Gerai
#XFLD: days
days=Dienos
#XFLD: Space tile edit button label
edit=Redaguoti
#XFLD: Auto Assign all objects to space
autoAssign=Automatiškai priskirti
#XFLD: Space tile open monitoring button label
openMonitoring=Stebėjimo priemonė
#XFLD: Delete
delete=Naikinti
#XFLD: Copy Space
copy=Kopijuoti
#XFLD: Close
close=Uždaryti
#XCOL: Space table-view column status
status=Būsena
#XFLD: Space status active
activeLabel=Aktyvus
#XFLD: Space status locked
lockedLabel=Užrakinta
#XFLD: Space status critical
criticalLabel=Kritinis
#XFLD: Space status cold
coldLabel=Šaltas
#XFLD: Space status deleted
deletedLabel=Panaikinta
#XFLD: Space status unknown
unknownLabel=Nežinoma
#XFLD: Space status ok
okLabel=Gerai
#XFLD: Database user expired
expired=Nebegalioja
#XFLD: deployed
deployed=Įdiegta
#XFLD: not deployed
notDeployed=Neįdiegta
#XFLD: changes to deploy
changesToDeploy=Pasikeičia į „Diegimas“
#XFLD: pending
pending=Diegiama
#XFLD: designtime error
designtimeError=Kūrimo laiko klaida
#XFLD: runtime error
runtimeError=Vykdymo laiko klaida
#XFLD: Space created by label
createdBy=Autorius
#XFLD: Space created on label
createdOn=Sukūrimo data
#XFLD: Space deployed on label
deployedOn=Įdiegimo data
#XFLD: Space ID label
spaceID=Vietos ID
#XFLD: Priority label
priority=Prioritetas
#XFLD: Space Priority label
spacePriority=Vietos prioritetas
#XFLD: Space Configuration label
spaceConfiguration=Srities konfigūracija
#XFLD: Not available
notAvailable=Neprieinama
#XFLD: WorkloadType default
default=Numatytoji
#XFLD: WorkloadType custom
custom=Pasirinktinė
#XFLD: Data Lake option label; refers to component SAP HANA Cloud, Data Lake
dataLakeAccess=Duomenų telkinio prieiga
#XFLD: Translation label
translationLabel=Vertimas
#XFLD: Source language label
sourceLanguageLabel=Šaltinio kalba
#XFLD: Translation CheckBox label
translationCheckBox=Įgalinti vertimą
#XTXT: Popover hint that space has to be deployed to show user details
openDatabaseUserHintPopoverText=Įdiekite vietą, norėdami pasiekti naudotojo išsamią informaciją.
#XTXT: Popover hint that space has to be deployed to open database explorer
openDatabaseExplorerHintPopoverText=Įdiekite vietą, norėdami atidaryti duomenų bazės naršyklę.
#XTXT: Text for when the Data Lake check box is disabled
dataLakeOptionDisabled=Negalite naudoti šios vietos, norėdami pasiekti Data Lake, nes ji jau naudojama kitoje vietoje.
#XFLD: Data Lake check box text; refers to component SAP HANA Cloud, Data Lake
dataLakeCheckBox=Naudokite šią vietą duomenų telkiniui pasiekti.
#XFLD: Space Priority minimum label extension
low=Žemas
#XFLD: Space Priority maximum label extension
high=Aukštas
#XFLD: Space name label
spaceName=Vietos pavadinimas
#XFLD: Enable deploy objects checkbox
enableDeployObjects=Diegti objektus
#XTIT: Copy spaces dialog title
copySpaceDialogTitle=Kopijuoti {0}
#XTXT Human readable text for unselected source language
unSelectedSourceLanguage=(Nieko nepasirinkta)
#XTXT Human readable text for language code "af"
af=Afrikanų
#XTXT Human readable text for language code "ar"
ar=Arabų
#XTXT Human readable text for language code "bg"
bg=Bulgarų
#XTXT Human readable text for language code "ca"
ca=Katalonų
#XTXT Human readable text for language code "zh"
zh=Kinų (supaprastintoji)
#XTXT Human readable text for language code "zf"
zf=Kinų
#XTXT Human readable text for language code "hr"
hr=Kroatų
#XTXT Human readable text for language code "cs"
cs=Čekų
#XTXT Human readable text for language code "cy"
cy=Valų
#XTXT Human readable text for language code "da"
da=Danų
#XTXT Human readable text for language code "nl"
nl=Olandų
#XTXT Human readable text for language code "en-UK"
en-UK=Anglų (Jungtinė Karalystė)
#XTXT Human readable text for language code "en"
en=Anglų (Jungtinės Valstijos)
#XTXT Human readable text for language code "et"
et=Estų
#XTXT Human readable text for language code "fa"
fa=Persų
#XTXT Human readable text for language code "fi"
fi=Suomių
#XTXT Human readable text for language code "fr-CA"
fr-CA=Prancūzų (Kanada)
#XTXT Human readable text for language code "fr"
fr=Prancūzų
#XTXT Human readable text for language code "de"
de=Vokiečių
#XTXT Human readable text for language code "el"
el=Graikų
#XTXT Human readable text for language code "he"
he=Hebrajų
#XTXT Human readable text for language code "hi"
hi=Hindi
#XTXT Human readable text for language code "hu"
hu=Vengrų
#XTXT Human readable text for language code "is"
is=Islandų
#XTXT Human readable text for language code "id"
id=Indoneziečių
#XTXT Human readable text for language code "it"
it=Italų
#XTXT Human readable text for language code "ja"
ja=Japonų
#XTXT Human readable text for language code "kk"
kk=Kazachų
#XTXT Human readable text for language code "ko"
ko=Korėjiečių
#XTXT Human readable text for language code "lv"
lv=Latvių
#XTXT Human readable text for language code "lt"
lt=Lietuvių
#XTXT Human readable text for language code "ms"
ms=Malajų
#XTXT Human readable text for language code "no"
no=Norvegų
#XTXT Human readable text for language code "pl"
pl=Lenkų
#XTXT Human readable text for language code "pt"
pt=Portugalų (Brazilija)
#XTXT Human readable text for language code "pt-PT"
pt-PT=Portugalų (Portugalija)
#XTXT Human readable text for language code "ro"
ro=Rumunų
#XTXT Human readable text for language code "ru"
ru=Rusų
#XTXT Human readable text for language code "sr"
sr=Serbų
#XTXT Human readable text for language code "sh"
sh=Serbų-kroatų
#XTXT Human readable text for language code "sk"
sk=Slovakų
#XTXT Human readable text for language code "sl"
sl=Slovėnų
#XTXT Human readable text for language code "es"
es=Ispanų
#XTXT Human readable text for language code "es-MX"
es-MX=Ispanų (Meksika)
#XTXT Human readable text for language code "sv"
sv=Švedų
#XTXT Human readable text for language code "th"
th=Tajų
#XTXT Human readable text for language code "tr"
tr=Turkų
#XTXT Human readable text for language code "uk"
uk=Ukrainiečių
#XTXT Human readable text for language code "vi"
vi=Vietnamiečių
#XTIT: Delete spaces confirmation title
deleteConfirmationHeaderText=Naikinti vietas
#XTXT: Logically delete space delete confirmation text with space name
logicallyDeleteConfirmationBodyTextWithSpaceName=Ar tikrai norite perkelti sritį „{0}“ į šiukšlinę?
#XTXT: Logically dpace delete confirmation text for multiple spaces
logicallyDeleteConfirmationBodyTextMultiple=Ar tikrai norite perkelti pasirinktas sritis ({0}) į šiukšlinę?
#XTXT: Delete space delete confirmation text with space name
deleteConfirmationBodyTextWithSpaceName=Ar tikrai norite naikinti sritį „{0}“? Šio veiksmo negalima anuliuoti.
#XTXT: Delete space delete confirmation text for multiple spaces
deleteConfirmationBodyTextMultiple=Ar tikrai norite naikinti {0} pasirinktas sritis? Šio veiksmo negalima anuliuoti. Šis turinys bus pašalintas {1}:
#XTXT: permanently
permanently=visam laikui
#XTXT: {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDetail=Šis turinys bus panaikintas {0} ir jo nebegalėsite atkurti:
#XTXT: Hint Delete space confirmation text
deleteConfirmationHint=Norėdami patvirtinti naikinimą, įvesite {0}.
#XTXT: Delete space confirmation error check spelling
deleteConfirmationErrorCheckSpelling=Patikrinkite rašybą ir pabandykite dar kartą.
#XTXT: All Spaces
allSpaces=Visos sritys
#XTXT: All data
allData=Visi šios srities objektai ir duomenys
#XTXT: All connections
allConnections=Visi šioje srityje apibrėžti ryšiai
#XFLD: Space tile selection box tooltip
clickToSelect=Spustelėkite, jei norite pažymėti
#XTXT: All database users
allDatabaseUsers=Visi objektai ir duomenys, esantys bet kurioje „Open SQL“ schemoje, susietoje su sritimi
#XFLD: remove members button tooltip
deleteUsers=Pašalinti narius
#XTXT: Space long description text
description=Aprašas (daugiausia 4000 simbolių)
#XFLD: Add Members button tooltip
addUsers=Pridėti narius
#XFLD: Add Users button tooltip
addUsersTooltip=Pridėti vartotojus
#XFLD: Edit Users button tooltip
editUsersTooltip=Redaguoti vartotojus
#XFLD: Remove Users button tooltip
removeUsersTooltip=Pašalinti vartotojus
#XFLD: Searchfield placeholder
filter=Ieškoti
#XCOL: Users table-view column health
health=Sveikata
#XCOL: Users table-view column access
access=Prieiga
#XFLD: No user found nodatatext
noDataText=Vartotojų nerasta
#XTIT: Members dialog title
selectUserDialogTitle=Pridėti narius
#XTIT: User dialog title
addUserDialogTitle=Pridėti vartotojus
#XTIT: Delete connections confirmation title
deleteConnectionConfirmationHeaderText=Naikinti ryšius
#XTIT: Delete single connection confirmation title
deleteConnectionConfirmationHeaderTextSingle=Naikinti ryšį
#XTXT: Delete connection delete confirmation text
deleteConnectionConfirmationBodyText=Ar tikrai norite naikinti pasirinktus ryšius? Jie bus pašalinti visam laikui.
#XTIT: Remote source dialog title
selectRemoteConnectionDialogTitle=Pasirinkti ryšius
#XTIT: Share connection dialog title
connectionSharingDialogTitle=Bendrinti ryšį
#XTIT: Connections shared from other spaces table title
sharedConnectionsFromOtherSpaces=Bendrinti ryšiai
#XFLD: Add remote source button tooltip
addRemoteConnections=Pridėti ryšius
#XFLD: Delete remote source button tooltip
deleteRemoteConnections=Pašalinti ryšius
#XFLD: Share remote source button tooltip
shareConnections=Bendrinti ryšius
#XFLD: Tile-layout tooltip
tileLayout=Plytelės šablonas
#XFLD: Table-layout tooltip
tableLayout=Lentelės šablonas
#XMSG: Success message after creating space
createSpaceSuccessMessage=Vieta sukurta
#XMSG: Success message after copying space
copySpaceSuccessMessage=Kopijuoti vietą „{0}“ į vietą „{1}“
#XMSG: Success message after deploying space
deploymentSuccessMessage=Vietos diegimas pradėtas
#XMSG: Success message after Apache Spark update started
sparkUpdateStartedSuccessMessage=„Apache Spark“ naujinimas pradėtas
#YMSE: Error while starting Apache Spark update
sparkUpdateStartedErrorMessage=Nepavyko atnaujinti „Apache Spark“
#XMSG: Success message after updating space
updateSettingsSuccessMessage=Vietos išsami informacija atnaujinta
#XMSG: Success message after unlocking space
unlockSpaceSuccessMessage=Vieta laikinai atrakinta
#XMSG: Success message after deleting a single space
deleteSpaceSuccessMessage=Vieta panaikinta
#XMSG: Success message after deleting multiple spaces
deleteSpacesSuccessMessage=Vietos panaikintos
#XMSG: Success message after restoring a single space
restoreSpaceSuccessMessage=Vieta atkurta
#XMSG: Success message after restoring multiple spaces
restoreSpacesSuccessMessage=Vietos atkurtos
#YMSE: Error while updating settings
updateSettingsFailureMessage=Šių vietos parametrų nepavyko atnaujinti.
#YMSE: Error while updating data lake option
dataLakeEnabledInOtherSpace=Duomenų telkinys jau yra priskirtas kitai vietai. Vienu metu tik viena vieta gali pasiekti duomenų telkinį.
#YMSE: Error while updating data lake option
virtualTablesExists=Negalite panaikinti duomenų telkinio priskyrimo prie šio vietos, nes vis dar yra priklausomybių nuo virtualiųjų lentelių*. Panaikinkite virtualiąsias lenteles, kad galėtumėte panaikinti duomenų telkinio priskyrimą prie šios vietos.
#YMSE: Error while unlocking space
unlockSpaceFailureMessage=Vietos nepavyko atrakinti.
#YMSE: Error while creating space
createSpaceError=Vietos nepavyko sukurti.
#YMSE: Error while creating duplicate space
createDuplicateSpaceError=Vieta pavadinimu {0} jau yra.
#YMSE: Error while deleting a single space
deleteSpaceError=Vietos nepavyko panaikinti.
#YMSE: Error details while deleting a single space
deleteSpaceDetailedError=Jūsų vieta „{0}“ tinkamai nebeveikia. Bandykite ją panaikinti dar kartą. Jei ji vis tiek neveikia, paprašykite savo administratoriaus, kad panaikintų jūsų vietą, arba atidarykite bilietą.
#YMSE: Error while deleting a single space in files
deleteSpaceInFilesError=Vietos duomenų failuose nepavyko panaikinti
#YMSE: Error while deleting a single space users
deleteSpaceUsersError=Vartotojų nepavyko pašalinti.
#YMSE: Error while deleting a single space schema
deleteSpaceSchemasError=Schemų nepavyko pašalinti.
#YMSE: Error while deleting a single space connections
deleteSpaceConnectionsError=Ryšių nepavyko pašalinti.
#YMSE: Error while deleting a single space data
deleteSpaceDataError=Vietos duomenų nepavyko panaikinti.
#YMSE: Error while deleting multiple spaces
deleteSpacesError=Vietų nepavyko panaikinti.
#YMSE: Error while restoring a single space
restoreSpaceError=Vietos nepavyko atkurti.
#YMSE: Error while restoring multiple spaces
restoreSpacesError=Vietų nepavyko atkurti.
#YMSE: Error while creating users
createUsersError=Vartotojų nepavyko pridėti.
#YMSE: Error while removing users
removeUsersError=Vartotojų nepavyko pašalinti.
#YMSE: Error while removing user
removeUserError=vartotojo nepavyko pašalinti.
#YMSE: Error assigning roles to a user
roleAssignUnAssignError=Nepavyko pridėti vartotojo prie pasirinkto apimties vaidmens.\n\n Negalite savęs pridėti prie apimties vaidmens. Galite paprašyti administratoriaus, kad jus pridėtų prie apimties vaidmens.
#YMSE: Error assigning user to the space
userAssignError=Nepavyko priskirti vartotojo vietai. \n\n Vartotojas jau priskirtas didžiausiam leistinam vietų skaičiui (100) apimties vaidmenyse.
#YMSE: Error assigning users to the space
usersAssignError=Nepavyko priskirti vartotojų vietai. \n\n Vartotojas jau priskirtas didžiausiam leistinam vietų skaičiui (100) apimties vaidmenyse.
#YMSE: Error while fetching users
fetchUserWithScopedRolesError=Nepavyko nuskaityti vartotojų. Bandykite dar kartą vėliau.
#YMSE: Error while fetching scoped roles
fetchScopedRolesError=Nepavyko nuskaityti apimties vaidmenų.
#YMSE: Error while fetching members
fetchUserError=Narių nepavyko iškviesti. Bandykite dar kartą vėliau.
#YMSE: Error while loading run-time database
loadRuntimeError=Nepavyko įkelti informacijos iš vykdymo laiko duomenų bazės.
#YMSE: Error while loading spaces
loadSpacesError=Deja, kažkas įvyko bandant nuskaityti jūsų vietas.
#YMSE: Error while loading haas resources
loadStorageError=Deja, kažkas įvyko bandant nuskaityti jūsų saugyklos duomenis.
#YMSE: Error no data could be loaded
loadDataError=Deja, kažkas įvyko bandant nuskaityti jūsų duomenis.
#XFLD: Click to refresh storage data
clickToRefresh=Spustelėkite čia ir bandykite dar kartą.
#XTIT: Delete space popup title
deleteSpacePopupTitle=Naikinti vietą
#XCOL: Spaces table-view column name
name=Pavadinimas
#XCOL: Spaces table-view deployment status
deploymentStatus=Diegimo būsena
#XFLD: Disk label in space details
storageLabel=Diskas (GB)
#XFLD: In-Memory label in space details
ramLabel=Atmintis (GB)
#XFLD: Memory label on space card
memory=Saugyklos atmintis
#XFLD: Storage Assignment label in space details
storrageAssignmentLabel=Vietos saugykla
#XFLD: Storage Type label in space details
storageTypeLabel=Saugojimo tipas
#XFLD: Enable Space Quota
enableSpaceQuota=Įgalinti vietos kvotą
#XFLD: No Space Quota
noSpaceQuota=Nėra vietos kvotos
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
diskAndInMemoryText=„SAP HANA“ duomenų bazė (disko ir atminties)
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
dataLakeFilesText=„SAP HANA“ duomenų telkinio failai
#XFLD: Available scoped roles label
availableRoles=Galimi apimties vaidmenys
#XFLD: Selected scoped roles label
selectedRoles=Pasirinkti apimties vaidmenys
#XCOL: Spaces table-view column models
models=Modeliai
#XCOL: Spaces table-view column users
users=Vartotojai
#XCOL: Spaces table-view column connections
connections=Ryšiai
#XFLD: Section header overview in space detail
overview=Apžvalga
#XFLD: Section header Apache Spark in space detail
sparkManagementSection=„Apache Spark“
#XFLD: Section header Apache Spark in space detail
sparkManagementSubSection=Taikomosios programos
#XFLD: Section header Apache Spark in space detail
taskAssignmentSubSection=Užduoties priskyrimas
#XFLD: vCPU label in Apache Spark section
vCPULabel=vCPU
#XFLD: Memory label in Apache Spark section
memoryLabel=Atmintis (GB)
#XFLD: space configuration label in Apache Spark section
spaceConfigurationLabel=Srities konfigūracija
#XFLD: Space Source label
sparkApplicationLabel=Taikomoji programa
#XFLD: Cluster Size label
clusterSizeLabel=Sankaupos dydis
#XFLD: Driver label
driverLabel=Vairuotojas
#XFLD: Executor label
executorLabel=Vykdytojas
#XFLD: max label
maxLabel=Maks. sunaudota
#XFLD: TrF Default label
trFDefaultLabel=Transformavimo srauto numatytieji parametrai
#XFLD: Merge Default label
mergeDefaultLabel=Sulieti numatytuosius parametrus
#XFLD: Optimize Default label
optimizeDefaultLabel=Optimizavimo numatytieji parametrai
#XFLD: Deployment Default label
deploymentDefaultLabel=Vietinės lentelės (failo) diegimas
#XFLD: max spark source label
driverExecutorMaxUsedSparkSource={0} CPU / {1} GB
#XFLD: Object type label
taskObjectTypeLabel=Objekto tipas
#XFLD: Task activity label
taskActivityLabel=Veikla
#XFLD: Task Application ID label
taskApplicationIDLabel=Numatytoji taikomoji programa
#XFLD: Section header in space detail
generalSettings=Bendrieji parametrai
#XMSG: Message strip message when a space is locked
spaceLockedMessageStrip=Sistema yra šiuo metu užrakinusi šią sritį.
#XMSG: Message strip message deploy warning
deployWarningMessageStrip=Pakeitimai šioje sekcijoje bus nedelsiant įdiegti.
#XMSG: Message strip message modify space configuration warning
modifySpaceConfigurationMessageStrip=Atkreipkite dėmesį, kad pakeitus šias reikšmes gali sutrikti sistemos veikimas.
#XFLD: Button text to unlock the space again
unlockSpace=Atrakinti vietą
#XFLD: Info text for audit log formatted message
auditLogText=Įgalinti audito žurnalus, kad būtų įrašyti skaitymo arba keitimo veiksmai (audito politika). Tada administratoriai gali analizuoti, kas kurį nors veiksmą atliko tuo metu.
#XMSG: Message strip  audit warning
auditLogWarningMessageStrip=Audito žurnalai gali užimti daug nuomininko disko saugyklos vietos. Jei įgalinate audito politiką (skaitymo ar keitimo veiksmus), turėtumėte reguliariai stebėti disko saugyklos naudojimą (naudodami sistemos monitoriaus naudojamą disko saugyklos kortelę), kad išvengtumėte viso disko išjungimo, dėl kurio gali sutrikti paslauga. Jei išjungsite audito politiką, visi jos audito žurnalo įrašai bus ištrinti. Jei norite išsaugoti audito žurnalo įrašus, apsvarstykite galimybę juos eksportuoti prieš išjungdami audito politiką.
#XFLD: Message strip link text to open in app help to guide user what to do to unlock his space permanently
unlockSpaceHelp=Rodyti žinyną
#XMSG: Message strip message when a space will be locked again
spaceTemporarilyUnlockedMessageStrip=Ši vieta viršija jos vietą saugykloje, todėl bus užrakinta po {0} {1}.
#XMSG: Unit for remaining time until space is locked again
hours=valandos
#XMSG: Unit for remaining time until space is locked again
minutes=minutės
#XFLD: Section header in space detail for auditing
detailsSectionAuditing=Auditas
#XFLD: Subsection header in space detail for auditing
auditing=Vietos audito parametrai
#XFLD: Hot space tooltip
hotSpaceCountTooltip=Kritinės vietos: sunaudota daugiau nei 90 % saugyklos.
#XFLD: Green space tooltip
greenSpaceCountTooltip=Geros vietos: sunaudota saugyklos nuo 6 iki 90 %.
#XFLD: Cold space tooltip
coldSpaceCountTooltip=Šaltosios vietos: sunaudota 5 % ar mažiau saugyklos.
#XFLD: Critical space tooltip
criticalSpaceCountTooltip=Kritinės vietos: sunaudota daugiau nei 90 % saugyklos.
#XFLD: Green space tooltip
okSpaceCountTooltip=Geros vietos: sunaudota saugyklos nuo 6 iki 90 %.
#XFLD: Locked space tooltip
lockedSpaceCountTooltipNew=Užrakintos būsenos: užblokuota dėl nepakankamos atminties.
#XFLD: Locked space tooltip
lockedSpaceCountTooltip=Užrakintos vietos
#YMSE: Error while deleting remote source
deleteRemoteError=Ryšių nepavyko pašalinti.
#XFLD: Space ID input tooltip
spaceIDInfoTooltip=Vietos ID vėliau nebus galima pakeisti.\nGalimi simboliai yra A–Z, 0–9 ir _
#YMSE: Error when space name is empty
spaceNameEmptyValidationError=Įveskite vietos pavadinimą.
#YMSE: Error when ECN Business name is empty
ecnNameEmptyValidationError=Įveskite verslo pavadinimą.
#YMSE: Error when space id is empty
spaceIDInputEmptyValidationError=Įveskite vietos ID.
#YMSE: Error when space id contains invalid characters
spaceIDInputInValidError=Negaliojantys simboliai. Naudokite tik A–Z, 0–9 ir _.
#YMSE: Error when space id exists while creation
spaceIDInputDuplicateError=Vietos ID jau yra.
#XFLD: Space searchfield placeholder
search=Ieškoti
#XMSG: Success message after creating users
createUsersSuccess=Vartotojai pridėti
#XMSG: Success message after creating user
createUserSuccess=Vartotojas pridėtas
#XMSG: Success message after updating users
updateUsersSuccess=Vartotojai ({0}) atnaujinti
#XMSG: Success message after updating user
updateUserSuccess=Vartotojas atnaujintas
#XMSG: Success message after removing users
removeUsersSuccess=Vartotojai ({0}) pašalinti
#XMSG: Success message after removing user
removeUserSuccess=Vartotojas pašalintas
#XFLD: Schema name
schemaName=Schemos pavadinimas
#XFLD: used of total
ofTemplate={0} iš {1}
#XFLD: Assigned disk label in space overview
assignedStorageTemplate=Priskirta diske ({0} iš {1})
#XFLD: Assigned Memory label in space overview
assignedRAMTemplate=Priskirta atmintyje ({0} iš {1})
#XFLD: Storage ratio on space
accelearationRAM=Atminties spartinimas
#XFLD: No Storage Consumption
noStorageConsumptionText=Nepriskirta jokia saugyklos kvota.
#XFLD: Used disk label in space overview
usedStorageTemplate=Saugyklai naudojamas diskas ({0} iš {1})
#XFLD: Used Memory label in space overview
usedRAMTemplate=Saugyklai naudojama atmintis ({0} iš {1})
#XFLD: Used Disk tooltip
usedStorageTooltipTemplate={0} iš {1} saugyklai naudojamas diskas
#XFLD: Used Memory tooltip
usedRAMTooltipTemplate={0} iš {1} sunaudota atmintyje
#XFLD: Assigned Disk tooltip
assignedStorageTooltipTemplate={0} iš {1} priskirta diske
#XFLD: Assigned Memory tooltip
assignedRAMTooltipTemplate={0} iš {1} priskirta atmintyje
#XFLD: Space related used storage tooltip
spaceRelatedUsedStorageTooltipTemplate=Vietos duomenys: {0}
#XFLD: Not space related used storage tooltip
otherDataUsedStorageTooltipTemplate=Kiti duomenys: {0}
#XFLD: Storage used exceeded popover and tooltip
storageUsedExceededText=Svarstykite plano išplėtimą arba kreipkitės į SAP palaikymą.
#XCOL: Space table-view column used Disk
usedStorage=Saugyklai naudojamas diskas
#XCOL: Space monitor column used Memory
usedRAM=Saugyklai naudojama atmintis
#XCOL: Space monitor column Schema
tableSchema=Schema
#XCOL: Space monitor column Storage Type
tableStorageType=Saugojimo tipas
#XCOL: Space monitor column Table Type
tableType=Lentelės tipas
#XCOL: Space monitor column Record Count
tableRecordCount=Įrašų skaičius
#XFLD: Assigned Disk
assignedStorage=Saugyklai priskirtas diskas
#XFLD: Assigned Memory
assignedRAM=Saugyklai priskirta atmintis
#XCOL: Space table-view column storage utilization
tableStorageUtilization=Panaudota saugykla
#XFLD: space status
spaceStatus=Vietos būsena
#XFLD: space type
spaceType=Vietos tipas
#XFLD: space type label for space with space type "SAP BW bridge" ("SAP BW bridge" is a product name, do not translate it)
abapbridgeSpaceType=SAP BW tiltas
#XFLD: space type label for space with space type "Data Marketplace"
marketplaceSpaceType=Duomenų teikėjo produktas
#XMSG: Error message for when selected space cannot be deleted due to the space type
deleteTypedSpaceError=Negalite naikinti vietos {0}, nes jos vietos tipas yra {1}.
#XMSG: Error message for when selected spaces cannot be deleted due to the space type
deleteTypedSpacesError=Negalite naikinti {0} pasirinktų vietų. Negalima naikinti vietų, su šiais vietų tipais: {1}.
#XFLD: space type label for space without space type
defaultSpaceType=SAP Datasphere
#XFLD: Tooltip for monitor space button
monitorSpace=Stebėjimo priemonė
#XFLD: Tooltip for edit space button
editSpace=Redaguoti vietą
#XMSG: Deletion warning in messagebox
deleteConfirmation=Ar tikrai norite panaikinti šią vietą?
#XFLD: Tooltip for delete space button
deleteSpace=Naikinti vietą
#XFLD: storage
storage=Saugyklos diskas
#XFLD: username
userName=Vartotojo vardas
#XFLD: port
port=Prievadas
#XFLD: hostname
hostName=Pagrindinio kompiuterio pavadinimas
#XFLD: password
password=Slaptažodis
#XBUT: Request new password button
requestPassword=Prašyti naujo slaptažodžio
#YEXP: Usage explanation in time data section
timeDataSectionHint=Kurkite laiko lenteles ir dimensijas, kurias naudosite savo modeliuose ir istorijose.
#YEXP: Usage explanation in Space schema section
spaceSchemaHint=Ar norite, kad duomenis jūsų vietoje naudotų kiti įrankiai ar taikomosios programos? Jei taip, sukurkite vieną ar kelis vartotojus, kurie gali pasiekti duomenis jūsų vietoje, ir pasirinkite, ar norite, kad pagal numatytuosius parametrus būtų sunaudojami visi būsimi vietos duomenis.
#XTIT: Create schema popup title
createSchemaDialogTitle=Kurti „Open SQL“ schemą
#XTIT: Generate Time Data dialog title
generateTimeDataDialogTitle=Kurti laiko lenteles ir dimensijas
#XTIT: Edit Time Data dialog title
editTimeDataDialogTitle=Redaguoti laiko lenteles ir dimensijas
#XTIT: Time Data token title
timeDataTokenTitle=Laiko duomenys
#XTIT: Time Data token title
timeDataUpdateViews=Naujinti laiko duomenų rakursus
#XFLD: Time Data token creation in progress label
timeDataCreationInProgress=Kuriama...
#XFLD: Time Data token creation error label
timeDataCreationError=Sukurti nepavyko. Bandykite dar kartą.
#XTIT: Time Data dialog time settings title
timeDataDialogTimeSettingsTitle=Laiko lentelės parametrai
#XTIT: Time Data dialog time data table title
timeDataDialogTranslationTablesTitle=Vertimo lentelės
#XTIT: Time Data dialog dimensions title
timeDataDialogTimeViewsTitle=Laiko dimensijos
#XFLD: Time Data dialog time range label
timeRangeHint=Apibrėžkite laiko intervalą.
#XFLD: Time Data dialog time data table label
timeDataHint=Suteikite lentelei pavadinimą.
#XFLD: Time Data dialog time data dimensions label
timeDimensionsHint=Suteikite dimensijoms pavadinimą.
#XFLD: Time Data Time range description label
timerangeLabel=Laiko intervalas
#XFLD: Time Data dialog from year label
fromYearLabel=Nuo metų
#XFLD: Time Data dialog to year label
toYearLabel=Iki metų
#XFLD: Time Data dialog calendar type label
calendarTypeLabel=Kalendoriaus tipas
#XFLD: Time Data dialog granularity label
granularityLabel=Skaidymas
#XFLD: Time Data dialog technical name label
technicalNameLabel=Techninis pavadinimas
#XFLD: Time Data dialog quarters text table lable
quarterTextsTableLabel=Ketvirčių vertimo lentelė
#XFLD: Time Data dialog months text table lable
monthsTextsTableLabel=Mėnesių vertimo lentelė
#XFLD: Time Data dialog days text table lable
daysTextsTableLabel=Dienų vertimo lentelė
#XFLD: Time Data dialog year label
yearLabel=Metų dimensija
#XFLD: Time Data dialog quarter label
quarterLabel=Ketvirčio dimensija
#XFLD: Time Data dialog month label
monthLabel=Mėnesio dimensija
#XFLD: Time Data dialog day label
dayLabel=Dienos dimensija
#XFLD: Time Data dialog gregorian calendar type label
gregorian=Grigaliaus
#XFLD: Time Data dialog time granularity day label
day=Diena
#XFLD: Time Data dialog business name maxlength reached
timeRangeValueStateTextMaxLengthBusinessName=Pasiektas maksimalus 1000 simbolių ilgis.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextMaxRangeExceeded=Maksimalus laiko intervalas yra 150 metų.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeLower=„Nuo metų“ turi būti žemiau nei „Iki metų“
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeHigher=„Nuo metų“ turi būti 1900 ar aukščiau.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeHigher=„Iki metų“ turi būti aukščiau nei „Nuo metų“
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeLower=„Iki metų“ turi būti žemiau nei esami metai plius 100
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningFromYear=Padidinus „Nuo metų“, galima netekti duomenų
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningToYear=Sumažinus „Iki metų“, galima netekti duomenų
#XMSG: Time Data creation validation error message
timeDataValidationError=Regis, kai kurie laukai neteisingi. Patikrinkite privalomus laukus, kad galėtumėte tęsti.
#XMSG: Time Data deletion popup text
deleteTimeDataConfirmation=Ar tikrai norite panaikinti duomenis?
#XMSG: Time Data creation success message
createTimeDataSuccess=Laiko duomenys sukurti
#XMSG: Time Data update success message
updateTimeDataSuccess=Laiko duomenys atnaujinti
#XMSG: Time Data delete success message
deleteTimeDataSuccess=Laiko duomenys panaikinti
#XMSG: Time Data creation error message
createTimeDataError=Kažkas įvyko bandant sukurti laiko duomenis.
#XMSG: Time Data update error message
updateTimeDataError=Kažkas įvyko bandant atnaujinti laiko duomenis.
#XMSG: Time Data creation error message
deleteTimeDataError=Kažkas įvyko bandant panaikinti laiko duomenis.
#XMSG: Error message when time data could not be retrieved
retrievingTimeDataError=Laiko duomenų nepavyko įkelti.
#XTIT: Time Data deletion popup title
deleteTimeDataConfirmationTitle=Įspėjimas
#XTXT: Time Data deletion dependency popup text
deleteTimeDataDependencyText=Nepavyko panaikinti laiko duomenų, nes jie naudojami kituose modeliuose.
#XTXT: Time Data deletion dependency popup text singular
deleteTimeDataDependencyTextSingular=Nepavyko panaikinti laiko duomenų, nes jie naudojami kitame modelyje.
#XMSG: Validation error when required time data form field is empty
timeDataRequiredField=Šis laukas yra privalomas ir negali būti paliktas tuščias.
#XFLD: Tooltip for Open in Database Explorer button
openInDBX=Atidaryti duomenų bazės naršyklėje
#YMSE: Dimension Year
dimensionYearView=Dimensija „Metai“
#YMSE: Dimension Year
dimensionQuarterView=Dimensija „Ketvirtis“
#YMSE: Dimension Year
dimensionMonthView=Dimensija „Mėnuo“
#YMSE: Dimension Year
dimensionDayView=Dimensija „Diena“
#XFLD: Time Data deletion object title
timeDataUsedIn=(naudojama tiek modelių: {0})
#XFLD: Time Data deletion object title singular
timeDataUsedInSingular=(naudojama 1 modelyje)
#XFLD: Time Data deletion table column provider
provider=Teikėjas
#XTIT: Time Data dependency dialog title
timeDataDependencyDialogTitle=Priklausomybės
#XTIT: Create schema popup title
createSpaceSchemaUserDialogTitle=Sukurti vietos schemos vartotoją
#XFLD: Create schema button
createSchemaButton=Kurti „Open SQL“ schemą
#XFLD: Generate TimeData button
generateTimeDataButton=Kurti laiko lenteles ir dimensijas
#XFLD: Show dependencies button
showDependenciesButton=Rodyti priklausomybes
#XFLD: tooltip for disabled buttons when user is not member of space
mustBeMemberOfSpaceForOperation=Norint atlikti šią operaciją, jūsų vartotojas turi būti šios vietos narys.
#XFLD: Create space schema user button
createSpaceSchemaUserButton=Kurti vietos schemos vartotoją
#YMSE: API Schema users load error
loadSchemaUsersError=Vartotojų sąrašo nepavyko įkelti.
#XTIT: API Schema-config popup title
schemaUserDetailsDialogTitle=Vietos schemos vartotojo išsami informacija
#XMSG: API Schema user deletion popup text
deleteSpaceSchemaUserConfirmation=Ar tikrai norite panaikinti pasirinktą vartotoją?
#XMSG: API Schema user deletion confirmation text
userDeleteSuccess=Vartotojas panaikintas
#YMSE: API Schema user deletion error
userDeleteError=Vartotojo nepavyko panaikinti.
#XFLD: User deleted
userDeleted=Vartotojas panaikintas.
#XTIT: Remove user popup title
removeUserConfirmationTitle=Įspėjimas
#XMSG: Remove user popup text
removeUserConfirmation=Ar tikrai norite pašalinti vartotoją? Vartotojas ir jam priskirti apimties vaidmenys bus pašalinti iš srities.
#XMSG: Remove users popup text
removeUsersConfirmation=Ar tikrai norite pašalinti vartotojus? Vartotojai ir jiems priskirti apimties vaidmenys bus pašalinti iš srities.
#XBUT: Confirmation button for remove user dialog
removeUserConfirmationBtn=Pašalinti
#YMSE: No data text for available roles
noDataAvailableRoles=Erdvė neįtraukta į jokį apimties vaidmenį. \n Kad galėtumėte pridėti vartotojus prie erdvės, pirmiausia reikia pridėti vieną ar kelis apimties vaidmenis.
#YMSE: No data text for selected roles
noDataSelectedRoles=Nėra pasirinktų apimties vaidmenų
#XTIT: Schema-config popup title
schemaDetailsDialogTitle=„Open SQL“ schemos konfigūracijos išsami informacija
#XFLD: Label for Read Audit Log
auditLogRead=Įgalinti nuskaitymo operacijų audito žurnalą
#XFLD: Label for Change Audit Log
auditLogChange=Įgalinti keitimo operacijų audito žurnalą
#XFLD: Label Audit Log Retention
auditLogRetention=Laikyti žurnalus
#XFLD: Label Audit Log Retention Unit
retentionUnit=Dienos
#YMSE: Error when log retention time is set incorrectly
incorrectRetentionTime=Įveskite sveikąjį skaičių nuo {0} iki {1}
#XFLD: Label for Cross Space Schema Access
spaceSchemaAccess=Sunaudoti vietos schemos duomenis
#XTIT: Confirm deactivation of space schema consumption dialog title
disableSpaceSchemaConsumptionDialogTitle=Stabdyti vietos schemos duomenų sunaudojimą
#XTXT: Confirm deactivation of space schema consumption dialog text
disableSpaceSchemaConsumptionDialogText=Ši „Open SQL“ schema gali sunaudoti jūsų vietos schemos duomenis. Jei sustabdysite sunaudojimą, modeliai, pagrįsti vietos schemos duomenimis, gali nebeveikti.
#XBUT: Confirmation button for the stop consuming space schema data dialog
stopConsuming=Stabdyti sunaudojimą
#XTXT: Tooltip for data lake icon
dataLakeIconTooltip=Ši vieta naudojama duomenų telkiniui pasiekti
#XFLD: Tooltip for data lake table entry
dataLakeTableEntryTooltip=Duomenų ežeras įgalintas
#XFLD: used In-Memory linit reached tag
usedRamLimitReachedLabel=Pasiektas atminties apribojimas
#XFLD: Space storage Limit reached tag
storageLimitReachedLabel=Saugyklos limitas pasiektas
#XFLD: Minimal Storage Limit Reached tag
minStorageLimitReachedLabel=Minimalus saugyklos limitas pasiektas
#XFLD: Space ram tag
ramLimitReachedLabel=Pasiektas atminties apribojimas
#XFLD: Minimal In-Memory Limit Reached tag
minRamLimitReachedLabel=Minimalus atminties apribojimas pasiektas
#XFLD: Popover text after click on space storage limit warning
storageLimitReachedText=Pasiekėte priskirtą vietos saugyklos limitą – {0}. Priskirkite daugiau saugyklos vietai.
#XFLD: System storage tag
systemStorageLimitReachedLabel=Sistemos saugyklos limitas pasiektas
#XMSG: Popover text after click on storage limit warning
systemStorageLimitReachedText=Pasiekėte sistemos saugyklos limitą – {0}. Šiuo metu negalite priskirti daugiau saugyklos vietai.
#XMSG: Schema deletion warning
deleteSchemaConfirmation=Panaikinus šią „Open SQL“ schemą, taip pat visam laikui bus panaikinti schemoje saugomi objektai ir tvarkomos asociacijos. Tęsti?
#XMSG: Success message after deleting a schema
schemaDeleteSuccess=Schema panaikinta
#YMSE: Error while deleting schema.
schemaDeleteError=Schemos nepavyko panaikinti.
#XMSG: Success message after update a schema
schemaUpdateSuccess=Schema atnaujinta
#YMSE: Error while updating schema.
schemaUpdateError=Schemos nepavyko atnaujinti.
#XMSG: Hint when no password is shown in the dialog.
newPasswordHint=Suteikėme šios schemos slaptažodį. Jei pamiršote savo slaptažodį ar jį praradote, galite prašyti naujo. Nepamirškite nukopijuoti ar įrašyti naujo slaptažodžio.
#XMSG: Hint when showing the password in the dialog.
savePasswordHint=Nukopijuokite slaptažodį. Jo reikės norint nustatyti ryšį su šia schema. Jai pamiršote slaptažodį, galite atidaryti šį dialogą ir jį nustatyti iš naujo.
#XFLD,50: Hint on entering schema name in creation dialog
schemaNameHint=Sukūrus schemą, šio pavad. nebus galima pakeisti.
#XFLD: Open SQL Schemas section sub headline
schemasSQL=„Open SQL“
#XFLD: Space schema section sub headline
schemasSpace=Vieta
#XFLD: HDI Container section header
HDIContainers=HDI konteineriai
#XTXT: Add HDI Containers button tooltip
addHDIContainers=Pridėti HDI konteinerius
#XTXT: Remove HDI Containers button tooltip
removeHDIContainers=Pašalinti HDI konteinerius
#XFLD: Enable HDI Containers feature button
enableHDIContainersButton=Įgalinti prieigą
#YMSE: No data text for HDI Containers table
noDataHDIContainers=Nėra pridėtų HDI konteinerių.
#YMSE: No data text for Timedata section
noDataTimedata=Nebuvo sukurta jokių laiko lentelių ir dimensijų.
#YMSE: No data text during runtime not available for Timedata section
runtimeNotAvailableNoDataTimedata=Nepavyksta įkelti laiko lentelių ir dimensijų, nes neprieinama vykdymo laiko duomenų bazė.
#YMSE: No data text during runtime not available for HDI section
runtimeNotAvailableNoDataHDI=Nepavyksta įkelti HDI konteinerių, nes neprieinama vykdymo laiko duomenų bazė.
#YMSE: Error while fetching HDI Containers
fetchHDIContainersError=HDI konteinerių nepavyko gauti. Bandykite dar kartą vėliau.
#XFLD Table column header for HDI Container names
HDIContainerName=HDI konteinerio pavadinimas
#XFLD: Enable HDI Containers feature dialog title
enableHDIContainersDialogTitle=Įgalinti prieigą
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText1New=Galite įgalinti „SAP SQL Data Warehousing“ savo „SAP Datasphere“ kliente, kad galima būtų keistis duomenimis tarp jūsų HDI konteinerių ir jūsų „SAP Datasphere“ sričių neperkeliant duomenų.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText2=Norėdami tai padaryti, sukurkite užklausą spustelėdami toliau esantį mygtuką.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText3New=Kai užklausa bus apdorota, „SAP Datasphere“ vykdymo laiko duomenų bazėje turite sukurti vieną ar daugiau naujų HDI konteinerių. Tada mygtukas „Įgalinti prieigą“ HDI konteinerių skiltyje visose jūsų „SAP Datasphere“ srityse pakeičiamas mygtuku „+“, ir galite pridėti konteinerius prie srities.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText41=Reikia daugiau informacijos? Eikite į %%0. Išsamią informaciją apie tai, ką įtraukti į bilietą, žr. %%1.
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkText=SAP žinynas
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkTextSapNote3057059=SAP pastaba 3057059
#XBUT: Open Ticket Button Text
openTicket=Atidaryti bilietą
#XBUT: Add Button Text
add=Pridėti
#XBUT: Next Button Text
next=Paskesnis
#XBUT: Edit Button Text
editUsers=Redaguoti
#XBUT: create user Button Text
createUser=Kurti
#XBUT: Update user Button Text
updateUser=Pasirinkti
#XTIT: Add HDI Containers Dialog Title
addHDIContainersDialogTitle=Pridėti nepriskirtus HDI konteinerius
#XFLD: No data text for add HDI container dialog
addHDIContainersDialogNoDataText=Nepavyko rasti nepriskirtų konteinerių. \n Konteineris, kurio ieškote, jau gali būti priskirtas vietai.
#YMSE: Error while loading assigned HDI Containers
loadAssignedHDIContainersError=Priskirtų HDI konteinerių nepavyko įkelti.
#YMSE: Error while loading assigned HDI Containers
loadUnassignedHDIContainersError=HDI konteinerių nepavyko įkelti.
#XMSG: Success message
succeededToAddHDIContainer=HDI konteineris pridėtas
#XMSG: Success message
succeededToAddHDIContainerPlural=HDI konteineriai pridėti
#XMSG: Success message
succeededToDeleteHDIContainer=HDI konteineris pašalintas
#XMSG: Success message
succeededToDeleteHDIContainerPlural=HDI konteineriai pašalinti
#XFLD: Time data section sub headline
timeDataSection=Laiko lentelės ir dimensijos
#XFLD: Schemas list entry read audit log enabled indicator
readIndicator=Nuskaityti
#XFLD: Schemas list entry change audit log enabled indicator
changeIndicator=Keisti
#XFLD: Remote sources section sub headline
allconnections=Ryšio priskyrimas
#XFLD: Remote sources section sub headline
localconnections=Vietos ryšiai
#XFLD: Remote sources section sub headline for open Connectors
openConnectors=SAP atviros jungtys
#XFLD: User section sub headline
memberassignment=Nario priskyrimas
#XFLD: User assignment section sub headline
userAssignment=Vartotojų priskyrimas
#XFLD: User section Access dropdown Member
member=Narys
#XFLD: User assignment section column name
user=Vartotojo vardas
#XTXT: Selected role count
selectedRoleToolbarText=Pasirinkta: {0}
#XTIT: Space detail section connections title
detailsSectionConnections=Ryšiai
#XTIT: Space detail section data access title
detailsSectionDataAccess=Schemos prieiga
#XTIT: Space detail section time data title
detailsSectionGenerateData=Laiko duomenys
#XTIT: Space detail section members title
detailsSectionUsers=Nariai
#XTIT: Space detail section Users title
detailsSectionUsersTitle=Vartotojai
#XTIT: Out of Storage
insufficientStoragePopoverTitle=Nebėra saugojimo vietos
#XTIT: Storage distribution
storageDistributionPopoverTitle=Panaudota disko saugykla
#XTXT: Out of Storage popover text
insufficientStorageText=Norėdami sukurti naują vietą, sumažinkite kitos vietos priskirtą saugyklą arba panaikinkite vietą, kurios daugiau nebereikia. Galite padidinti bendrą sistemos saugyklą iškviesdami „Valdyti planą“.
#XMSG: Space id length warning
spaceIdLengthWarning=Viršytas maksimalus {0} simbolių skaičius.
#XMSG: Space name length warning
spaceNameLengthWarning=Viršytas maksimalus {0} simbolių skaičius.
#XMSG: Space id reserved prefix warning
spaceIdnWarningReservedPrefix=Nenaudokite {0} prefikso, kad išvengtumėte galimų konfliktų.
#YMSE: Error while loading open SQL schemas
loadLocalSchemasError=„Open SQL“ schemų nepavyko įkelti.
#YMSE: Error while creating open SQL schema
createLocalSchemaError=„Open SQL“ schemos nepavyko sukurti.
#YMSE: Error while loading all remote connections
loadRemoteConnectionsError=Nepavyko įkelti visų nuotolinių ryšių.
#YMSE: Error while loading space details
loadSpaceDetailsError=Vietos išsamios informacijos nepavyko įkelti.
#YMSE: Error while deploying space details
deploySpaceDetailsError=Vietos įdiegti nepavyko.
#YMSE: Error while copying space details
copySpaceDetailsError=Vietos nukopijuoti nepavyko.
#YMSE: Error while loading storage data
loadStorageDataError=Saugojimo duomenų nepavyko įkelti.
#YMSE: Error while loading all users
loadAllUsersError=Visų vartotojų nepavyko įkelti.
#YMSE: Failed to reset password
resetPasswordError=Slaptažodžio nepavyko nustatyti iš naujo.
#XMSG: Success message after updating space
resetPasswordSuccessMessage=Schemos naujas slaptažodis nustatytas
#YMSE: DP Agent-name too long
DBAgentNameError=DP agento pavadinimas yra per ilgas.
#YMSE: Schema-name not valid.
schemaNameError=Schemos pavadinimas negalioja.
#YMSE: User name not valid.
UserNameError=Vartotojo vardas negalioja.
#XTIT: Consumption by Storage Type
consumptionStorageTypeText=Sunaudojimas pagal saugojimo tipą
#XTIT: Consumption by Schema
consumptionSchemaText=Sunaudojimas pagal schemą
#XTIT: Overall Consumption by schema
overallConsumptionSchemaText=Bendras lentelės sunaudojimas pagal schemą
#XTIT: Overall Consumption by Table Type
overallConsumptionTableTypeText=Bendras sunaudojimas pagal lentelės tipą
#XTIT: Tables
tableDetailsText=Lentelės išsami informacija
#XTIT: Table Storage Consumption
tableStorageConsumptionText=Lentelės saugyklos sunaudojimas
#XFLD: Table Type label
tableTypeLabel=Lentelės tipas
#XFLD: Schema label
schemaLabel=Schema
#XFLD: reset table tooltip
resetTable=Nustatyti iš naujo lentelę
#XFLD: In-Memory label in space monitor
inMemoryLabel=Atmintis
#XFLD: Disk label in space monitor
diskLabel=Diske
#XFLD: Yes
yesLabel=Taip
#XFLD: No
noLabel=Ne
#XFLD: switch label: Want the data in this space to be consumable by default?
allowConsumptionByDefault=Ar norite, kad duomenys šioje vietoje būtų sunaudojami pagal numatytąjį parametrą?
#XFLD: Business Name
businessNameLabel=Verslo pavadinimas
#XFLD: Refresh
refresh=Atnaujinti
#XMSG: No filter results title
noFilterResultsTitle=Regis, jūsų filtro nustatymai nerodo jokių duomenų.
#XMSG: No filter results message
noFilterResultsMsg=Bandykite patikslinti filtro nustatymus ir, jei vis tiek nematote jokių duomenų, sukurkite kelias lenteles duomenų daryklėje. Kai jos sunaudos saugyklą, galėsite juos čia stebėti.
#XMSG: Space monitoring Hana state red title
spaceMonitorHanaRedTitle=Vykdymo laiko duomenų bazė neprieinama.
#XMSG: Space monitoring Hana state red message
spaceMonitorHanaRedMsg=Jei vykdymo laiko duomenų bazė neprieinama, kai kurios funkcijos yra išjungiamos ir negalime rodyti informacijos šiame puslapyje.
#YMSE: Error while creating space schema user
createSpaceSchemaUserError=Vietos schemos naudotojo nepavyko sukurti.
#YMSE: Error User name already exists
userAlreadyExistsError=Vartotojo vardas jau yra.
#YMSE: Error Authentication failed
authenticationFailedError=Autentifikavimas nepavyko.
#XMSG: Message strip message when a schema user is locked
userLockedMessageStrip=Vartotojas užrakintas, nes atlikto per daug nesėkmingų prisijungimų. Prašykite naujo slaptažodžio, kad atrakintumėte vartotoją.
#XMSG: Success message after reset and unlock schema user
resetUnlockPasswordSuccessMessage=Naujas slaptažodis nustatytas ir vartotojas atrakintas
#XMSG: user is locked message
userLockedMessage=Vartotojas atrakintas.
#XCOL: Users table-view column Role
spaceRole=Vaidmuo
#XCOL: Users table-view column Scoped Role
spaceScopedRole=Apimties vaidmuo
#XCOL: Users table-view column Space Admin
spaceAdmin=Vietos administratorius
#XFLD: User section dropdown value Viewer
viewer=Peržiūros programa
#XFLD: User section dropdown value Modeler
modeler=Modeliavimo programa
#XFLD: User section dropdown value Data Integrator
dataIntegrator=Duomenų integratorius
#XFLD: User section dropdown value Space Administrator
spaceAdministrator=Vietos administratorius
#XMSG: Success message -  Space Role updated successfully.
updateUserSpaceRoleSuccess=Vietos vaidmuo atnaujintas
#XMSG: Error message - Failed to update Space Role.
updateUserSpaceRoleError=Vietos vaidmens nepavyko atnaujinti.
#XFLD:
databaseUserNameSuffix=Duomenų bazės vartotojo vardo sufiksas
#XTXT: Space Schema password text
spaceSchemaPasswordText=Norėdami nustatyti ryšį su šia schema, nukopijuokite savo slaptažodį. Jei pamiršote slaptažodį, visada galite paprašyti naujo.
#XFLD: Cloud Platform label in space schema dialog
cloudPlatformLabel=Debesies platforma
#XTXT: Cloud Platform text in space schema dialog
cloudPlatformText=Norėdami nustatyti prieigą per šį vartotoją, įgalinkite sunaudojimą ir nukopijuokite kredencialus. Jei galite kopijuoti duomenis tik be slaptažodžio, įsitikinkite, kad vėliau pridėsite slaptažodį.
#XFLD: enable consumption in CloudPlatform label in space schema dialog
enableConsumptionInCloudPlatformLabel=Įgalinti sunaudojimą debesies platformoje
#XFLD: Credentials for User-Provided Service label in space schema dialog
cloudPlatformCredentialsLabel=Vartotojui suteiktos paslaugos kredencialai:
#XFLD: Credentials for User-Provided Service (Without Password) label in space schema dialog
cloudPlatformCredentialsWithoutPasswordLabel=Vartotojui suteiktos paslaugos kredencialai (be slaptažodžio):
#XFLD: Copy Credentials label in space schema dialog
copyCredentialsLabel=Kopijuoti kredencialus be slaptažodžio
#XFLD: Copy Credentials label in space schema dialog
copyFullCredentialsLabel=Kopijuoti visus kredencialus
#XFLD: Copy Password label in space schema dialog
copyPasswordLabel=Kopijuoti slaptažodį
#XMSG: Credentials copied to clipboard
credentialsCopiedMessage=Kredencialai nukopijuoti į mainų sritį
#XMSG: Password copied to clipboard
passwordCopiedMessage=Slaptažodis nukopijuotas į mainų sritį
#XMSG: create database user dialog title
createDatabaseUserDialogTitle=Kurti duomenų bazės vartotoją
#XMSG: Database Users section title
databaseUsers=Duomenų bazės vartotojai
#XMSG: Database User Details dialog title
databaseUserDetailsDialogTitle=Duomenų bazės vartotojo išsami informacija
#XFLD: database user read audit log
databaseUserAuditLogRead=Įgalinti nuskaitymo operacijų audito žurnalus ir laikyti žurnalus:
#XFLD: database user change audit log
databaseUserAuditLogChange=Įgalinti keitimo operacijų audito žurnalus ir laikyti žurnalus:
#XMSG: Cloud Platform Access
cloudPlatformAccess=Debesies platformos prieiga
#XFLD: Enable Consumption in Cloud Platform hint
enableConsumptionCloudPlatformHint=Nustatykite prieigą prie savo „HANA Deployment Infrastructure“ (HDI) konteinerio naudodami šį duomenų bazės vartotoją. Norint prisijungti prie HDI konteinerio, SQL modeliavimas turi būti įjungtas
#XFLD: Enable HDI Consumption
enableConsumptionCloudPlatform=Įgalinti HDI sunaudojimą
#XFLD: Enable Consumption hint
enableConsumptionHint=Ar norite, kad duomenis jūsų vietoje galėtų naudoti kiti įrankiai ar taikomosios programos?
#XFLD: Enable Consumption
enableConsumption=Įgalinti SQL sunaudojimą
#XFLD: Enable Modeling
enableModeling=Įgalinti SQL modeliavimą
#XMSG: Privileges for Data Modeling
privilegesModeling=Duomenų įtraukimas
#XMSG: Privileges for Data Consumption
privilegesConsumption=Duomenų sunaudojimas, skirtas išoriniams įrankiams
#XFLD: SQL Modeling
sqlModeling=SQL modeliavimas
#XFLD: SQL Consumption
sqlConsumption=SQL sunaudojimas
#XFLD: enabled
enabled=Įgalinta
#XFLD: disabled
disabled=Išjungta
#XFLD: Edit Privileges
editPrivileges=Redaguoti įgaliojimus
#XFLD: Open Database Explorer
openDBX=Atidaryti duomenų bazės naršyklę
#XFLD: create database user hint
databaseCreateHint=Atkreikite dėmesį, kad įrašę nebegalėsite pakeisti vartotojo vardo.
#XFLD: Internal Schema Name
internalSchemaName=Vidinis schemos pavadinimas
#YMSE: Failed to load database users
loadDatabaseUserError=Duomenų bazės vartotojų nepavyko įkelti
#YMSE: Failed to delete database user
deleteDatabaseUsersError=Duomenų bazės vartotojų nepavyko panaikinti
#XMSG: Success message after deleting database user
deleteDatabaseUserSuccess=Duomenų bazės vartotojas panaikintas
#XMSG: Success message after deleting database users
deleteDatabaseUsersSuccess=Duomenų bazės vartotojai panaikinti
#XMSG: Success message after creating database user
createDatabaseUserSuccessMessage=Duomenų bazės vartotojas sukurtas
#YMSE: Failed to create database user
createDatabaseUserError=Duomenų bazės vartotojo nepavyko sukurti
#XMSG: Success message after updating database user
updateDatabaseUserSuccessMessage=Duomenų bazės vartotojas atnaujintas
#YMSE: Failed to update database user
updateDatabaseUserError=Duomenų bazės vartotojo nepavyko atnaujinti
#XFLD: HDI Consumption
hdiConsumption=HDI sunaudojimas
#XTIT: Space detail section Database Access title
detailsSectionDatabaseAccess=Duomenų bazės prieiga
#XTXT: Database users consumption hint
databaseUsersConsumptionHint=Nustatykite duomenų sunaudojamumą kaip numatytąjį. Modeliai daryklėse automatiškai leis sunaudoti duomenis.
#XFLD: Default Consumption of Space Data:
defaultConsumptionOfSpaceData=Vietos duomenų numatytasis sunaudojimas:
#XFLD: Database User Name
databaseUserName=Duomenų bazės vartotojo vardas
#XMSG: Database User creation validation error message
databaseUserValidationError=Regis, kai kurie laukai neteisingi. Patikrinkite privalomus laukus, kad galėtumėte tęsti.
#XMSG: SQL modeling can’t be enabled since this user has been migrated.
migratedDatabaseUserHint=Duomenų įtraukimo nepavyko įgalinti, nes šis vartotojas buvo perkeltas į kitą sistemą.
#XBUT: Remove Button Text
remove=Pašalinti
#XBUT: Remove Spaces Button Text
removeSpaces=Pašalinti vietas
#XBUT: Remove Objects Button Text
removeObjects=Pašalinti objektus
#XMSG: No members have been added yet.
noMembersAssigned=Kol kas nėra pridėtų narių.
#XMSG: No users have been added yet.
noUsersAssigned=Kol kas nėra pridėtų vartotojų.
#XMSG: No database users have been created or your filter is not showing any data.
noDatabaseUsers=Nėra sukurtų duomenų bazės vartotojų arba Jūsų filtras nerodo duomenų.
#XMSG: Please enter a user name.
noDatabaseUsername=Įveskite vartotojo vardą.
#XMSG: The user name is too long. Please use a shorter one.
databaseUsernameTooLong=Vartotojo vardas per ilgas. Naudokite trumpesnį.
#XMSG: No privileges have been enabled and this database user will have limited functionality. Do you still want to continue?
noPrivilegesConfirmationMessage=Nėra įgalintų jokių įgaliojimų, todėl šio duomenų bazės vartotojo funkcionalumas bus apribotas. Ar vis tiek norite tęsti?
#XMSG: To enable audit logs for change operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogChangeMessage=Norint įgalinti audito žurnalų keitimo operacijas, duomenų įtraukimas taip pat turi būti įgalintas.
#XMSG: To enable audit logs for read operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogReadMessage=Norint įgalinti audito žurnalų nuskaitymo operacijas, duomenų įtraukimas taip pat turi būti įgalintas.
#XMSG: To enable HDI consumption data ingestion and data consumption needs to be enabled. Do you want to enable all?
enableHDIConsumptionMessage=Norint įgalinti HDMI sunaudojimą, duomenų įtraukimas ir duomenų sunaudojimas taip pat turi būti įgalinti. Ar norite tai padaryti?
#XMSG:
databaseUserPasswordText=Norėdami nustatyti ryšį su šiuo duomenų bazės vartotoju, nukopijuokite savo slaptažodį. Jei pamiršote slaptažodį, visada galite paprašyti naujo.
#XTIT: Space detail section members title
detailsSectionMembers=Nariai
#XMSG: New password set
newPasswordSet=Naujas slaptažodis nustatytas
#XFLD: Data Ingestion
dataIngestion=Duomenų įtraukimas
#XFLD: Data Consumption
dataConsumption=Duomenų sunaudojimas
#XFLD: Privileges
privileges=Įgaliojimai
#XFLD: Enable Data ingestion
enableDataIngestion=Įgalinti duomenų įtraukimą
#XMSG: Log the read and change operations for data ingestion.
databaseUserAuditHint=Įrašyti žurnale duomenų įtraukimo nuskaitymo ir keitimo operacijas.
#XMSG: Make your space data available in your HDI containers.
databaseUserHDIHint=Padarykite vietos duomenų prieinamumą savo HDI konteineriuose.
#XFLD: Enable Data consumption
enableDataConsumption=Įgalinti duomenų sunaudojimą
#XMSG: Allow other apps or tools to consume your space data.
enableDataConsumptionHint=Leiskite kitoms taikomosioms programoms ar įrankiams sunaudoti jūsų vietos duomenis.
#XMSG: To set up access via this schema user copy the credentials to your user-provided service. In case you can only copy the credentials without a password make sure to add the password later.
databaseUserHDIConsumptionHint=Norėdami nustatyti prieigą per duomenų bazės vartotoją, nukopijuokite kredencialus į savo vartotojo suteiktą paslaugą. Jei galite kopijuoti duomenis tik be slaptažodžio, įsitikinkite, kad vėliau pridėsite slaptažodį.
#XFLD: Data Flow Runtime Capacity label in space overview
dataFlowRuntimeCapacity=Duomenų srauto vykdymo laiko pajėgumai ({0}:{1} valandos iš {2} valandų)
#XFLD: Could not load Data Flow Runtime Capacity
dataFlowRuntimeCapacityError=Duomenų srauto vykdymo laiko pajėgumų nepavyko įkelti
#XMSG: User can grant data consumption to other users.
databaseUserConsumptionWithGrantOptionHint=Vartotojas gali suteikti duomenų sunaudojimą kitiems vartotojams.
#XFLD: Enable data consumption with grant option
enableConsumptionWithGrantOption=Įgalinti duomenų sunaudojimą su suteikimo parinktimi.
#XMSG: To enable data consumption with grant option data consumption needs to be enabled. Do you want to enable both?
enableConsumptionWithGrantMessage=Norint įgalinti duomenų sunaudojimą su suteikimo parinkties duomenimis, turi būti įgalintas sunaudojimas. Ar norite įgalinti abu?
#XFLD: Enable Automated Predictive Library (APL) and Predictive Analysis Library (PAL)
allowUsingScriptServerFunctionality=Įgalinti automatizuotą prognostinę biblioteką (APL) ir prognostinę analizės biblioteką (PAL)
#XMSG: User can use SAP HANA Cloud embedded machine learning functions.
allowUsingScriptServerFunctionalityHint=Vartotojas gali naudoti „SAP HANA Cloud“ įdėtąsias mašininio mokymosi funkcijas.
#XFLD: Password Policy
passwordPolicy=Slaptažodžių politika
#XMSG: Password Policy
passwordPolicyHint=Čia galite įgalinti arba išjungti sukonfigūruotą slaptažodžių politiką.
#XFLD: Enable Password Policy
enablePasswordPolicy=Įgalinti slaptažodžių politiką
#XMSG: Read Access to the Space Schema
readAccessTitle=Skaitymo prieiga prie vietos schemos
#XMSG: read access hint
readAccessHint=Leiskite duomenų bazės vartotojui prijungti išorinius įrankius prie vietos schemos ir skaityti rakursus, kurie prieinami naudojimui.
#XFLD: Space Schema
spaceSchema=Vietos schema
#XFLD: Enable Read Access (SQL)
enableReadAccess=Įgalinti skaitymo prieigą (SQL)
#XMSG: Allow the user to grant read access to other users.
withGrantOptionHint=Leiskite vartotojui suteikti skaitymo prieigą kitiems vartotojams.
#XFLD: With Grant Option
withGrantOption=Su suteikimo parinktimi
#XMSG: Make your space data available in your HDI containers.
enableHDIConsumptionHint=Padarykite vietos duomenų prieinamumą savo HDI konteineriuose.
#XFLD: Enable HDI Consumption
enableHDIConsumption=Įgalinti HDI sunaudojimą
#XMSG: Write Access to the User’s Open SQL Schema
writeAccessTitle=Įrašymo prieiga prie vartotojo „Open SQL“ schemos
#XMSG: write access hint
writeAccessHint=Leiskite duomenų bazės vartotojui prijungti išorinius įrankius prie vartotojo „Open SQL“ schemos, kad būtų galima sukurti duomenų objektus ir įtraukti duomenis, skirtus naudoti vietoje.
#XFLD: Open SQL Schema
openSQLSchema=„Open SQL“ schema
#XFLD: Enable Write Access (SQL DDL & DML)
enableWriteAccess=Įgalinti įrašymo prieigą (SQL, DDL ir DML)
#XMSG: audit hint
auditHint=Užregistruokite skaitymo ir keitimo operacijas „Open SQL“ schemoje.
#XMSG: data consumption hint
dataConsumptionHint=Pagal numatytuosius nustatymus pateikite visus naujus vietos rakursus sunaudoti. Modeliuotojai gali nepaisyti šio parametro atskiruose rakursuose naudodami rakurso išvesties šoninio skydo jungiklį „Pateikti sunaudoti“. Taip pat galite pasirinkti formatus, kuriais pateikiami rakursai.
#XFLD: Expose for Consumption by Default
exposeDataConsumption=Pateikti sunaudoti pagal numatytuosius nustatymus
#XMSG: database users hint consumption hint
databaseUsersHint2New=Kurkite duomenų bazės vartotojus, norėdami prijungti išorinius įrankius prie „SAP Datasphere“. Nustatykite įgaliojimus, kad vartotojai galėtų nuskaityti vietos duomenis ir kurti duomenų objektus (DDL) bei įtraukti duomenis (DML), skirtus naudoti vietoje.
#XFLD: Read
read=Nuskaityti
#XFLD: Read (HDI)
readHDI=Nuskaityti (HDI)
#XFLD: Write
write=Rašyti
#XMSG: HDI Containers Hint
HDIContainersHint2=Įgalinkite prieigą prie jūsų vietos „SAP HANA Deployment Infrastructure“ (HDI) konteinerių. Modeliuotojai rakursuose gali naudoti HDI artefaktus kaip šaltinius, o HDI klientai gali pasiekti jūsų vietos duomenis.
#XMSG: Open info dialog
openDatabaseUserInfoDialog=Atidaryti informacijos dialogo langą
#XMSG: Database user is locked. Open info dialog to unlock
openLockedDatabaseUserInfoDialog=Duomenų bazės vartotojas užrakintas. Norėdami jį atrakinti, atidarykite dialogo langą
#XFLD: Table
table=Lentelė
#XFLD: Space section partner configuration title
partnerConnectionConfigurationSectionTitle=Partnerio ryšys
#XFLD: Space section partner configuration subtitle
partnerConnectionConfigurationSectionSubTitle=Partnerio ryšio konfigūracija
#XFLD: Space section partner configuration hint
partnerConnectionConfigurationSectionHint=Apibrėžkite savo partnerio ryšio poekranį, pridėdami „iFrame“ URL ir piktogramą. Ši konfigūracija prieinama tik šiame kliente.
#XFLD: Table Name Field
partnerConnectionConfigurationName=Poekranio pavadinimas
#XFLD: Table iFrame URL field
partnerConnectionConfigurationiFrameURL=„iFrame“ URL
#XFLD: Table iFrame Post Message Origin field
partnerConnectionConfigurationiFramePostMessageOrigin=„iFrame“ registravimo pranešimo kilmė
#XFLD: Table icon/image name
partnerConnectionConfigurationIcon=Piktograma
#XMSG: Table no result found
partnerConnectionConfigurationNotfound=Partnerio ryšio konfigūracijų rasti nepavyko.
#XMSG: Partner Connection Confgurations are not available
partnerConnectionConfigurationNotAvailable=Kai vykdymo laiko duomenų bazė neprieinama, partnerio ryšio konfigūracijų rodyti negalima.
#XFLD: Dialog Create Partner Connection Configuration
partnerConnectionConfigurationDialogTitleAdd=Kurti partnerio ryšio konfigūraciją
#XFLD: Dialog Create Partner Connection Configuration Upload Icon
partnerConnectionConfigurationButtonUloadPartnerIcon=Įkelti piktogramą
#XFLD: Dialog Create Partner Connection Configuration Select
partnerConnectionConfigurationSelectIcon=Pasirinkti (maksimalus dydis – 200 KB)
#XFLD: Dialog Create Partner Connection Configuration Enter a name
partnerConnectionConfigurationNamePlaceholder=Partnerio poekranio pavyzdys
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL
partnerConnectionConfigurationiFrameURLPlaceholder=http://www.example.com
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL Check
partnerConnectionConfigurationiFramePostMessageOriginPlaceholder=.example.com
#XFLD: Dialog Create Partner Connection Configuration Browse
partnerConnectionConfigurationBrowseButton=Naršyti
#XFLD: Create Partner Connection Configuration Msg
partnerConnectionConfigurationUploadMsg=Partnerio ryšio konfigūracija sėkmingai sukurta.
#XMSG: Error while deleting Partner Connection configuration(s)
partnerConnectionConfigurationDeleteError=Klaida naikinant partnerio ryšio konfigūraciją (-as).
#XMSG: Partner Connection Configuration has been successfully deleted
partnerConnectionConfigurationDeleteSuccess=Partnerio ryšio konfigūracija sėkmingai panaikinta.
#XMSG: Error while retrieving Partner Connection Configurations
partnerConnectionConfigurationLoadError=Klaida nuskaitant partnerio ryšio konfigūraciją (-as).
#XMSG: Error fileSizeLimitExceeded
partnerConnectionConfigurationfileSizeLimitExceeded=Failo nepavyko įkelti, nes jis viršija maksimalų 200KB dydį.
#XBUT: Create Partner Connection Configuration
partnerConnectionConfigurationButtonCreate=Kurti partnerio ryšio konfigūraciją
#XBUT: Delete Partner Connection Configuration
partnerConnectionConfigurationButtonDelete=Naikinti partnerio ryšio konfigūraciją.
#XMSG: Partner Tile Creation Failure
upsertPartnerTileFailed=Partnerio poekranio sukurti nepavyko.
#XMSG: Partner Tile Deletion Failure
deletePartnerTileFailed=Partnerio poekranio panaikinti nepavyko.
#XMSG: HANA cloud connector settings reset failure
resetHANASCCSettingsFailed=Nepavyko iš naujo nustatyti kliento „SAP HANA“ debesies jungties parametrų
#XFLD: Workload Class
workloadClass=Darbo krūvio klasė
#XFLD: Workload Management
workloadManagement=Darbo krūvio valdymas
#XFLD: Priority
workloadClassPriority=Prioritetas
#XMSG:
workloadManagementPriorityHint=Teikdami duomenų bazės užklausą, galite nurodyti šios srities prioritetą. Įveskite reikšmę nuo 1 (mažiausias prioritetas) iki 8 (didžiausias prioritetas). Esant situacijai, kai sritys konkuruoja dėl galimų gijų, tos, kurių prioritetai yra aukštesni, vykdomos prieš sritis su žemesniais prioritetais.
#XMSG:
workloadClassPriorityHint=Galite nurodyti vietos prioritetą nuo 0 (mažiausias) iki 8 (didžiausias). Aukšto prioriteto vietos ataskaitos vykdomos anksčiau nei kitų mažesnio prioriteto vietos ataskaitos. Numatytasis prioritetas yra 5. Kadangi 9 reikšmė yra rezervuota sistemos operacijoms, jos negalima naudoti vietoje.
#XFLD: Statement Limits
workloadclassStatementLimits=Ataskaitų limitai
#XFLD: Workload Configuration
workloadConfiguration=Darbo krūvio konfigūracija
#XMSG:
workloadClassStatementLimitsHint=Galite apibrėžti vietoje tuo pačiu metu vykdomų ataskaitų sunaudojamą maksimalų gijų skaičių (arba procentą) ir atminties GB. Galite įvesti bet kokią reikšmę arba procentus nuo 0 (be ribos) iki bendros atminties ir gijų skaičiaus, prieinamų kliente. \n\n Jei nurodysite gijų limitą, atminkite, kad tai gali sumažinti našumą. \n\n Jei nurodysite atminties limitą, jį pasiekusios ataskaitos nebus vykdomos.
#XMSG:
workloadClassStatementLimitsDescription=Numatytoji konfigūracija suteikia didelius išteklių apribojimus, tuo pačiu neleidžiant perkrauti sistemos.
#XMSG:
workloadClassStatementLimitCustomDescription=Galite nustatyti didžiausią bendrą gijų ir atminties limitą, kurį gali sunaudoti teiginiai, kurie vienu metu veikia srityje.
#XMSG:
totalStatementThreadLimitHelpText=Nustačius per mažą gijų ribą, gali būti paveiktas teiginio našumas, o per didelės vertės arba 0 gali leisti vietos užimti visas galimas sistemos gijas.
#XMSG:
totalStatementMemoryLimitHelpText=Nustačius per mažą atminties ribą, gali trūkti atminties, o per didelės reikšmės arba 0 gali leisti vietos užimti visą turimą sistemos atmintį.
#XMSG:
totalStatementThreadLimitHelpTextRestricted=Įveskite procentinę dalį nuo 1 % iki 70 % (arba lygiavertį skaičių) nuo bendro nuomininko gijų skaičiaus. Nustačius per mažą gijos ribą, gali būti paveiktas teiginių našumas, o per didelės reikšmės gali paveikti teiginių našumą kitose srityse.
#XMSG:
totalStatementThreadLimitHelpTextDynamic=Įveskite procentinę dalį nuo 1 % iki {0} % (arba lygiavertį skaičių) nuo bendro nuomininko gijų skaičiaus. Nustačius per mažą gijos ribą, gali būti paveiktas teiginių našumas, o per didelės reikšmės gali paveikti teiginių našumą kitose srityse.
#XMSG:
totalStatementMemoryLimitHelpTextNew=Įveskite reikšmę arba procentinę dalį nuo 0 (be apribojimų) iki bendro atminties dydžio pas klientą. Nustačius per mažą atminties limitą gali būti paveiktas teiginių našumas, o per didelės reikšmės gali paveikti teiginių našumas kitose srityse.
#XFLD: Total Statement Thread Limit
totalStatementThreadLimit=Bendrasis ataskaitos gijų limitas
#XFLD: Segmented button label
workloadclassPercentage=%
#XFLD: Segmented button label
workloadclassGigaByte=GB
#XFLD, 80: Segmented button label
workloadclassThreads=Gijos
#XFLD: Total Statement Memory Limit
totalStatementMemoryLimit=Bendrasis ataskaitos atminties limitas
#YMSE: Error while loading customer hana info
loadCustomerHanaInfoError=Nepavyko įkelti kliento SAP HANA informacijos.
#XMSG:
minimumLimitReached=Pasiektas minimalus limitas.
#XMSG:
maximumLimitReached=Pasiektas maksimalus limitas.
#XMSG: Name Taken for Technical Name
technical-name-taken=Jau yra ryšys su Jūsų įvestu techniniu pavadinimu. Įveskite kitą pavadinimą.
#XMSG: Name Too long for Technical Name
technical-name-too-long=Jūsų įvestas techninis pavadinimas viršija 40 simbolių. Įveskite pavadinimą, kurį sudaro mažiau simbolių.
#XMSG: Technical name field empty
technical-name-field-empty=Įveskite techninį pavadinimą.
#XMSG: Generic error when user enters invalid characters for a field
field-invalid-characters=Pavadinime galite naudoti tik raides (a–z), skaičius (0–9) ir pabraukimo brūkšnius (_).
#XMSG: Generic error when user enters invalid initial characters for a field
field-underscore-trim=Jūsų įvestas pavadinimas negali prasidėti ar baigtis pabraukimo brūkšniu (_).
#XFLD: Checkbox label for Enable Statement Limits
enableStatementLimits=Įgalinti ataskaitos limitus
#XFLD: Connection SubSection Settings Title in space details
connectionSettings=Parametrai
#XMSG: Connections tool hint in Space details section
connectionsToolHint=Norėdami kurti arba redaguoti ryšius, atidarykite TP „Ryšiai“ iš šoninio naršymo arba spustelėkite čia:
#XBUT: Connection list Open Connection Tool Button
button-connectiontool=Eikite į ryšius
#XFLD: Not deployed label on space tile
notDeployedLabel=Vieta dar neįdiegta.
#XFLD: Not deployed additional text on space tile
notDeployedText=Įdiekite vietą.
#XFLD: Corrupt space label on space tile
corruptSpace=Kažkas nepavyko.
#XFLD: Corrupt space additional text on space tile
corruptSpaceHint=Pabandykite diegti dar kartą arba kreipkitės pagalbos
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Audito žurnalo duomenys
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Administravimo duomenys
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Kiti duomenys
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Duomenys vietose
#XMSG: Unlock Space confirmation text
unlockSpaceConfirmation=Ar tikrai norite atrakinti sritį?
#XMSG: Lock Space confirmation text
lockSpaceConfirmation=Ar tikrai norite užrakinti sritį?
#XFLD: Lock
lock=Užrakinti
#XFLD: Unlock
unlock=Atrakinti
#XFLD: Locking
locking=Užrakinama
#XMSG: Success message after locking space
lockSpaceSuccessMsg=Sritis užrakinta
#XMSG: Success message after unlocking space
unlockSpaceSuccessMsg=Sritis atrakinta
#XMSG: Success message after locking spaces
lockSpacesSuccessMsg=Sritys užrakintos
#XMSG: Success message after unlocking spaces
unlockSpacesSuccessMsg=Sritys atrakintos
#YMSE: Error while locking a space
lockSpaceError=Srities užrakinti negalima.
#YMSE: Error while unlocking a space
unlockSpaceError=Srities atrakinti negalima.
#XTIT: popup title Warning
confirmationWarningTitle=Įspėjimas
#XMSG: Message strip message when a space was locked manually
manualLockedMessage=Sritis užrakinta rankiniu būdu.
#XMSG: Message strip message when a space was locked due to the amount of generated audit logs
auditLogLockedMessage=Sistema užrakino sritį, nes audito žurnalai sunaudoja daug disko vietos (GB).
#XMSG: Message strip message when a space was locked because the assigned quota was exceeded.
quotaLockedMessage=Sistema užrakino sritį, nes ji viršija atmintyje ar disko saugykloje jai skirtą vietą.
#XMSG: Unlock Spaces confirmation text
unlockSpacesConfirmation=Ar tikrai norite atrakinti pasirinktas sritis?
#XMSG: Lock Spaces confirmation text
lockSpacesConfirmation=Ar tikrai norite užrakinti pasirinktas sritis?
#XTIT: Scoped role editor dialog title
scopedRoleEditorDialogTitle=Apimties vaidmens rengyklė
#XTIT: ECN Management title
ecnManagementTitle=Srities ir elastinių skaičiavimų mazgo valdymas
#XFLD: ECNs
ecns=Elastinių skaičiavimų mazgai
#XFLD: ECN phase Ready
ecnReady=Paruošta
#XFLD: ECN phase Running
ecnRunning=Vykdoma
#XFLD: ECN phase Initial
ecnInitial=Neparuošta
#XFLD: ECN phase Starting
ecnStarting=Pradžia
#XFLD: ECN phase Starting Failed
ecnStartingFailed=Pradėti nepavyko
#XFLD: ECN phase Stopping
ecnStopping=Stabdoma
#XFLD: ECN phase Stopping Failed
ecnStoppingFailed=Sustabdyti nepavyko
#XBTN: Assign Button
assign=Priskirti sritis
#XBTN: Start Header-Button
start=Pradėti
#XBTN: Update Header-Button
repair=Naujinti
#XBTN: Stop Header-Button
stop=Stabdyti
#XFLD: ECN hours remaining
ecnHoursRemaining=Liko 1000 val.
#XFLD: ECN block-hours remaining
ecnBlockHoursRemaining=Liko {0} blokavimo valandos
#XFLD: ECN block-hour remaining
ecnBlockHourRemaining=Liko {0} blokavimo valanda
#XTIT: ECN creation dialog title
ecnCreationDialogTitle=Kurti elastinių skaičiavimų mazgą
#XTIT: ECN edit dialog title
ecnEditDialogTitle=Redaguoti elastinių skaičiavimų mazgą
#XTIT: ECN deletion dialog title
ecnDeletionDialogTitle=Naikinti elastinių skaičiavimų mazgą
#XTIT: ECN Space assignment dialog title
ecnSpaceAssignmentDialogTitle=Priskirti sritis
#XFLD: ECN ID
ECNIDLabel=Elastinių skaičiavimų mazgas
#XTXT: Selected toolbar text
selectedToolbarText=Pasirinkta: {0}
#XTIT: Elastic Compute Nodes
ECNslong=Elastinių skaičiavimų mazgai
#XTIT: Object assignment - Table header: Objects available
objectsAvailable=Objektų skaičius
#XTIT: Object assignment - Dialog header text
selectObjects=Pasirinkite sritis ir objektus, kuriuos norite priskirti elastinių skaičiavimų mazgui:
#XTIT: Object assignment - Table header title: Objects
objects=Objektai
#XTIT: Object assignment - Table header: Type
type=Tipas
#XMSG: Message strip delete database user audit warning
deleteDatabaseUserWarningMessageStrip=Atkreipkite dėmesį, kad panaikinus duomenų bazės vartotoją, bus panaikinti visi sugeneruoti audito žurnalo įrašai. Jei norite išlaikyti audito žurnalus, eksportuokite juos prieš panaikindami duomenų bazės vartotoją.
#XMSG: Message strip delete HDI container audit logs impact
deleteHDIContainerWarningMessageStrip=Atkreipkite dėmesį, kad atšaukus HDI konteinerio priskyrimą prie srities, bus panaikinti visi sugeneruoti audito žurnalo įrašai. Jei norite išlaikyti audito žurnalus, eksportuokite juos prieš atšaukdami HDI konteinerio priskyrimą.
#XTXT: All audit logs
allAuditLogs=Visi sričiai sugeneruoti audito žurnalo įrašai
#XMSG: Message strip disable audit warning
disableAuditWarningMessageStrip=Atkreipkite dėmesį, kad išjungus audito politiką (nuskaitymo ar keitimo operacijas), bus panaikinti visi audito žurnalo įrašai. Jei norite išlaikyti audito žurnalo įrašus, eksportuokite juos prieš išjungdami audito politiką.
#XTIT: No Spaces assigned to ECN Illustration title
noSpacesECNTitle=Dar nepriskirta sričių ar objektų
#XTIT: No Spaces assigned to ECN Illustration description
noSpacesECNDescription=Norėdami pradėti dirbti su savo elastinių skaičiavimų mazgu, priskirkite jam sritį arba objektus.
#XTIT: No Spaces Illustration title
noSpacesTitle=Sritis dar nesukurta
#XTIT: No Spaces Illustration description
noSpacesDescription=Norėdami pradėti gauti duomenis, sukurkite sritį.
#XTIT: No Spaces Illustration title
noDeletedSpacesTitle=Šiukšlinė tuščia
#XTIT: No Spaces Illustration description
noDeletedSpacesDescription=Galite iš čia atkurti panaikintas sritis.
#XTXT:  {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDatabaseUsers=Įdiegus vietą, šie duomenų bazės vartotojai bus {0} panaikinti ir jų nebegalėsite atkurti:
#XTIT: Delete database users
deleteDatabaseUsersTitle=Naikinti duomenų bazės vartotojus
#YMSE: Error if ecn id already exists
ecnIDInputDuplicateError=ID jau yra.
#YMSE: Error if ecn id contains invalid characters
ecnIDInputInvalidError=Naudokite tik mažąsias raides a–z ir skaitmenis 0–9
#YMSE: Error if ecn id is too short
ecnIDInputTooShortError=ID turi būti sudarytas iš bent {0} simbolių.
#XMSG: ecn id length warning
ecnIdLengthWarning=Viršytas maksimalus {0} simbolių skaičius.
#XFLD: open System Monitor
systemMonitor=Sistemos stebėjimo priemonė
#XFLD: open ECN schedule dialog menu entry
schedule=Tvarkaraštis
#XFLD: open create ECN schedule dialog
createSchedule=Kurti tvarkaraštį
#XFLD: open change ECN schedule dialog
changeSchedule=Redaguoti tvarkaraštį
#XFLD: open delete ECN schedule dialog
deleteSchedule=Naikinti tvarkaraštį
#XFLD: open self assign ECN schedule dialog
selfAssignSchedule=Priskirti tvarkaraštį man
#XFLD: open pause ECN schedule dialog
pauseSchedule=Pristabdyti tvarkaraštį
#XFLD: open resume ECN schedule dialog
resumeSchedule=Atkurti tvarkaraštį
#XFLD: View Logs
viewLogs=Peržiūrėti žurnalus
#XFLD: Compute Blocks
computeBlocks=Skaičiavimo blokai
#XFLD: Memory label in ECN creation dialog
ecnMemory=Atmintis (GB)
#XFLD: Storage label in ECN creation dialog
ecnStorage=Saugykla (GB)
#XFLD: Number of CPU label in ECN creation dialog
ecnCPUCount=CPU skaičius
#XFLD: ECN updated by label
changedBy=Keitimo autorius
#XFLD: ECN updated on label
changedOn=Keitimo data
#XMSG: Success message after creating a Elastic Compute Node
createEcnSuccessMessage=Elastinių skaičiavimų mazgas sukurtas
#YMSE: Error while creating a Elastic Compute Node
createEcnError=Elastinių skaičiavimų mazgo sukurti nepavyko
#XMSG: Success message after updating a Elastic Compute Node
updateEcnSuccessMessage=Elastinių skaičiavimų mazgas atnaujintas
#XMSG: Error while updating a Elastic Compute Node
udpateEcnError=Elastinių skaičiavimų mazgo atnaujinti nepavyko
#XMSG: Success message after deleting a Elastic Compute Node
deleteEcnSuccessMessage=Elastinių skaičiavimų mazgas panaikintas
#YMSE: Error while deleting a Elastic Compute Node
deleteEcnError=Elastinių skaičiavimų mazgo panaikinti nepavyko
#XMSG: Success message after starting a Elastic Compute Node
startingEcnSuccessMessage=Elastinių skaičiavimų mazgas paleidžiamas
#XMSG: Success message after stopping a Elastic Compute Node
stoppingEcnSuccessMessage=Elastinių skaičiavimų mazgas sustabdomas
#YMSE: Error while starting a Elastic Compute Node
startingEcnError=Elastinių skaičiavimų mazgo paleisti nepavyko
#YMSE: Error while stopping a Elastic Compute Node
stoppingEcnError=Elastinių skaičiavimų mazgo sustabdyti nepavyko
#XBUT: Add Object button for an ECN
assignObjects=Pridėti objektus
#XFLD: Enable auto object assignment label
enableAutoObjAssignment=Pridėti visus objektus automatiškai
#XFLD: object type label to be assigned
objectTypeLabel=Tipas (semantinis naudojimas)
#XFLD: assigned object type label
assignedObjectTypeLabel=Tipas
#XFLD: technical name label
TechnicalNameLabel=Techninis pavadinimas
#XFLD: Object selecttion ECN label
objectSelectionECNLabel=Pasirinkite objektus, kuriuos norite pridėti prie elastinių skaičiavimų mazgo
#XTIT: Add objects dialog title
assignObjectsTitle=Priskirti objektus
#XFLD: object label with object count
objectLabel=Objektas
#XMSG: No objects available to add message.
noObjectsToAssign=Nėra objektų, kuriuos būtų galima priskirti.
#XMSG: No objects assigned message.
noAssignedObjects=Nėra priskirtų objektų.
#XTIT: Remove objects popup title
removeObjectsOrSpacesConfirmationTitle=Įspėjimas
#XTIT: Delete ECN popup button and title
deleteECNConfirmationButtonAndTitle=Naikinti
#XMSG: Remove objects popup text
removeObjectsConfirmation=Ar tikrai norite pašalinti pasirinktus objektus?
#XMSG: Remove spaces popup text
removeSpacesConfirmation=Ar tikrai norite pašalinti pasirinktas sritis?
#XBUT: Confirmation button for objects dialog
removeObjectsOrSpacesConfirmationBtn=Pašalinti vietas
#XMSG: Success message after removing objects
removeObjectsSuccessMessage=Rodomi objektai pašalinti
#XMSG: Success message after assigning objects
assignObjectsSuccessMessage=Rodomi objektai priskirti
#XFLD: auto object assignment label for space tile
autoObjAssignmentLabel=Visi rodomi objektai
#XFLD: Spaces tab label
spacesTabLabel=Sritys
#XFLD: Exposed objects tab label
consumptionObjectsTabLabel=Rodomi objektai
#XMSG: Success message after unassigning spaces
spacesUnassignedSuccessMessage=Sritys pašalintos
#XMSG: Success message after unassigning space
spaceUnassignedSuccessMessage=Sritis pašalinta
#YMSE: Error while assigning / unassigning spaces
spacesAssignUnassignError=Nepavyko priskirti ar pašalinti sričių.
#YMSE: Error while removing objects
removeObjectsError=Nepavyko priskirti ar pašalinti objektų.
#YMSE: Error while removing object
removeObjectError=Nepavyko priskirti ar pašalinti objekto.
#YMSE: Error when saving the ecn with invalid value
invalidSizingPlanError=Anksčiau pasirinktas numeris nebegalioja. Pasirinkite galiojantį numerį.
#YMSE: Error when saving the ecn with invalid value
invalidPerformanceClassError=Pasirinkite tinkamą našumo klasę.
#YMSE: Error when saving the ecn with invalid value
performanceClassNotAvailableError=Pirmiau pasirinkta našumo klasė „{0}“ šiuo metu negalioja. Prašome pasirinkti galiojančią našumo klasę.
#XTXT: Delete ECN confirmation text
deleteConfirmationTxtECN=Ar tikrai norite naikinti elastinių skaičiavimų mazgą?
#XFLD: tooltip for ? button
help=Žinynas
#XFLD: ECN edit button label
editECN=Konfigūruoti
#XFLD: Technical type label for ERModel
DWC_ERMODEL=Esybių ryšių modelis
#XFLD: Technical type label for Local Table
DWC_LOCAL_TABLE=Vietinė lentelė
#XFLD: Technical type label for Remote Table
DWC_REMOTE_TABLE=Nuotolinė lentelė
#XFLD: Technical type label for Analytic Model
DWC_ANALYTIC_MODEL=Analitinis modelis
#XFLD: Technical type label for Task Chain
DWC_TASKCHAIN=Užduočių grandinė
#XFLD: Technical type label for Data Flow
DWC_DATAFLOW=Duomenų srautas
#XFLD: Technical type label for Replication Flow
DWC_REPLICATIONFLOW=Replikavimo srautas
#XFLD: Technical type label for Transformation Flow
DWC_TRANSFORMATIONFLOW=Transformavimo srautas
#XFLD: Technical type label for Intelligent Lookup
DWC_IDT=Intelektualioji peržvalga
#XFLD: Technical type label for Repository
DWC_REPOSITORY_PACKAGE=Saugykla
#XFLD: Technical type label for Enterprise Search
DWC_ESMODEL=Įmonės ieška
#XFLD: Technical type label for View
DWC_VIEW=Rakursas
#XFLD: Technical type label for Data Product
DWC_DATAPRODUCT=Duomenų produktas
#XFLD: Technical type label for Data Access Control
DWC_DAC=Duomenų prieigos kontrolė
#XFLD: Technical type label for Folder
DWC_FOLDER=Aplankas
#XFLD: Technical type label for Data Entity
DWC_BUSINESS_ENTITY=Verslo vienetas
#XFLD: Technical type label for Business Entity Variant
DWC_BUSINESS_ENTITY_VARIANT=Verslo vieneto variantas
#XFLD: Technical type label for Responsibility Scenario
DWC_AUTH_SCENARIO=Atsakomybės scenarijus
#XFLD: Technical type label for Fact Model
DWC_FACT_MODEL=Faktų modelis
#XFLD: Technical type label for Transformation Flow
DWC_PERSPECTIVE=Perspektyva
#XFLD: Technical type label for Consumption Model
DWC_CONSUMPTION_MODEL=Sunaudojimo modelis
#XFLD: Technical type label for Remote Connection
DWC_REMOTECONNECTION=Nuotolinis ryšys
#XFLD: Technical type label for Fact Model Variant
DWC_FACT_MODEL_VARIANT=Fakto modelio variantas
#XMSG: Schedule created alert message
createScheduleSuccess=Tvarkaraštis sukurtas
#XMSG: Schedule updated alert message
updateScheduleSuccess=Tvarkaraštis atnaujintas
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Tvarkaraštis panaikintas
#XMSG: Schedule takeover success message
ownerChangeScheduleSuccess=Tvarkaraštis priskirtas jums
#XMSG: Message for starting pausing of schedule
pauseScheduleStarted=Pristabdomas 1 tvarkaraštis
#XMSG: Message for starting resuming of schedule
resumeScheduleStarted=Atkuriamas 1 tvarkaraštis
#XFLD: Segmented button label
availableSpacesButton=Prieinama
#XFLD: Segmented button label
selectedSpacesButton=Pasirinkta
#XFLD: Visit website button text
visitWebsite=Apsilankyti svetainėje
#XMSG: Warning message on unselecting source language
sourceLanguageRemovalWarning=Anksčiau pasirinkta šaltinio kalba bus pašalinta.
#XBUT: enable translation confirmation button
enableTranslationConfirmationBtn=Įgalinti
#XFLD: ECN performance class label
performanceClassLabel=Našumo klasė
#XTXT performance class memory text
memoryText=Atmintis
#XTXT performance class compute text
computeText=Skaičiavimas
#XTXT performance class high-compute text
highComputeText=Aukštasis skaičiavimas
#XBUT: Recycle Bin Button Text
recycleBin=Šiukšlinė
#XBUT: Restore Button Text
restore=Atkurti
#XMSG: Warning message for new Workload Management UI
priorityWarning=Ši sritis yra tik skaitoma. Galite pakeisti erdvės prioritetą sistemos / konfigūracijos / darbo krūvio valdymo srityje.
#XMSG: Warning message for new Workload Management UI
workloadWarning=Ši sritis yra tik skaitoma. Galite pakeisti erdvės darbo krūvio konfigūraciją sistemos / konfigūracijos / darbo krūvio valdymo srityje.
#XCOL: Space table-view column: Apache Spark vCPUs
sparkManagementVCPUs=„Apache Spark“ vCPU
#XCOL: Space table-view column: Apache Spark Memory (GB)
sparkManagementMemory=„Apache Spark“ atmintis (GB)
#XCOL: Space type (from Space Capability) for Data Ingestion Spaces
ingestionSpaceType=Duomenų produkto įtraukimas
#XFLD: Deployment in progress nodatatext for spark table
sparkTableDeploymentInProgress=Duomenys neprieinami, nes sritis šiuo metu diegiama
#XFLD: Runtime data being loaded nodatatext for spark table
sparkTableDeploymentLoading=Duomenys neprieinami, nes sritis šiuo metu įkeliama
#XCOL: Space type (from Space Capability) for BW Private Cloud Edition
BWPCESpaceType=SAP BW
#XBUT: Go to Instance Mappings Button
navToMappingsButton=Redaguoti egzemplioriaus susiejimus
#XFLD: SAP HANA Data Lake Storage label if less than 1GB
dataLakeStorageUsed=< 1 GB
