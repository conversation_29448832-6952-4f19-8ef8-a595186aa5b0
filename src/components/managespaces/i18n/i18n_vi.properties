#DO NOT REMOVE________________________
#XFLD: Breadcrumb for space monitoring page
spaceMonitorBreadcrumb=Giám sát
#XTXT: Type name for spaces in browser tab page title
space=Vùng dữ liệu
#_____________________________________
#XFLD: Spaces label in
spaces=Vùng dữ liệu
#XFLD: Manage plan button text
manageQuotaButtonText=Quản lý kế hoạch
#XBUT: Manage resources button
manageResourcesButton=Quản lý tài nguyên
#XFLD: Create space button tooltip
createSpace=Tạo vùng dữ liệu
#XFLD: Create
create=Tạo
#XFLD: Deploy
deploy=Triển khai
#XFLD: Page
page=Trang
#XFLD: Cancel
cancel=Hủy
#XFLD: Update
update=Cập nhật
#XFLD: Save
save=Lưu
#XFLD: OK
ok=OK
#XFLD: days
days=Ngày
#XFLD: Space tile edit button label
edit=Hiệu chỉnh
#XFLD: Auto Assign all objects to space
autoAssign=Gán tự động
#XFLD: Space tile open monitoring button label
openMonitoring=Giám sát
#XFLD: Delete
delete=Xóa
#XFLD: Copy Space
copy=Sao chép
#XFLD: Close
close=Đóng
#XCOL: Space table-view column status
status=Trạng thái
#XFLD: Space status active
activeLabel=Đang hoạt động
#XFLD: Space status locked
lockedLabel=Bị khóa
#XFLD: Space status critical
criticalLabel=Tới hạn
#XFLD: Space status cold
coldLabel=Lạnh
#XFLD: Space status deleted
deletedLabel=̣Đã xóa
#XFLD: Space status unknown
unknownLabel=Không xác định
#XFLD: Space status ok
okLabel=Mạnh
#XFLD: Database user expired
expired=Đã hết hạn
#XFLD: deployed
deployed=Đã triển khai
#XFLD: not deployed
notDeployed=Chưa triển khai
#XFLD: changes to deploy
changesToDeploy=Các thay đổi về triển khai
#XFLD: pending
pending=Triển khai
#XFLD: designtime error
designtimeError=Lỗi thời gian thiết kế
#XFLD: runtime error
runtimeError=Lỗi thời gian thực hiện
#XFLD: Space created by label
createdBy=Được tạo bởi
#XFLD: Space created on label
createdOn=Được tạo vào
#XFLD: Space deployed on label
deployedOn=Được triển khai vào
#XFLD: Space ID label
spaceID=ID vùng dữ liệu
#XFLD: Priority label
priority=Ưu tiên
#XFLD: Space Priority label
spacePriority=Ưu tiên vùng dữ liệu
#XFLD: Space Configuration label
spaceConfiguration=Cấu hình vùng dữ liệu
#XFLD: Not available
notAvailable=Không có sẵn
#XFLD: WorkloadType default
default=Mặc định
#XFLD: WorkloadType custom
custom=Tùy chỉnh
#XFLD: Data Lake option label; refers to component SAP HANA Cloud, Data Lake
dataLakeAccess=Truy cập hồ dữ liệu
#XFLD: Translation label
translationLabel=Bản dịch
#XFLD: Source language label
sourceLanguageLabel=Ngôn ngữ nguồ̀n
#XFLD: Translation CheckBox label
translationCheckBox=Bật bản dịch
#XTXT: Popover hint that space has to be deployed to show user details
openDatabaseUserHintPopoverText=Triển khai vùng dữ liệu để truy cập chi tiết người dùng.
#XTXT: Popover hint that space has to be deployed to open database explorer
openDatabaseExplorerHintPopoverText=Triển khai vùng dữ liệu để mở Trình khám phá cơ sở dữ liệu.
#XTXT: Text for when the Data Lake check box is disabled
dataLakeOptionDisabled=Bạn không thể sử dụng vùng dữ liệu này để truy cập Hồ dữ liệu vì nó đã được sử dụng bởi vùng dữ liệu khác.
#XFLD: Data Lake check box text; refers to component SAP HANA Cloud, Data Lake
dataLakeCheckBox=Sử dụng vùng dữ liệu này để truy cập hồ dữ liệu.
#XFLD: Space Priority minimum label extension
low=Thấp
#XFLD: Space Priority maximum label extension
high=Cao
#XFLD: Space name label
spaceName=Tên vùng dữ liệu
#XFLD: Enable deploy objects checkbox
enableDeployObjects=Triển khai đối tượng
#XTIT: Copy spaces dialog title
copySpaceDialogTitle=Sao chép {0}
#XTXT Human readable text for unselected source language
unSelectedSourceLanguage=(Không chọn)
#XTXT Human readable text for language code "af"
af=Tiếng Hà Lan dùng ở Nam Phi
#XTXT Human readable text for language code "ar"
ar=Tiếng Ả Rập
#XTXT Human readable text for language code "bg"
bg=Tiếng Bulgaria
#XTXT Human readable text for language code "ca"
ca=Tiếng Catalan
#XTXT Human readable text for language code "zh"
zh=Tiếng Trung Giản thể
#XTXT Human readable text for language code "zf"
zf=Tiếng Trung
#XTXT Human readable text for language code "hr"
hr=Tiếng Croatia
#XTXT Human readable text for language code "cs"
cs=Tiếng Séc
#XTXT Human readable text for language code "cy"
cy=Tiếng Wales
#XTXT Human readable text for language code "da"
da=Tiếng Đan Mạch
#XTXT Human readable text for language code "nl"
nl=Tiếng Hà Lan
#XTXT Human readable text for language code "en-UK"
en-UK=Tiếng Anh (Vương quốc Anh)
#XTXT Human readable text for language code "en"
en=Tiếng Anh (Hoa Kỳ)
#XTXT Human readable text for language code "et"
et=Tiếng Estonia
#XTXT Human readable text for language code "fa"
fa=Tiếng Ba Tư
#XTXT Human readable text for language code "fi"
fi=Tiếng Phần Lan
#XTXT Human readable text for language code "fr-CA"
fr-CA=Tiếng Pháp (Canada)
#XTXT Human readable text for language code "fr"
fr=Tiếng Pháp
#XTXT Human readable text for language code "de"
de=Tiếng Đức
#XTXT Human readable text for language code "el"
el=Tiếng Hy Lạp
#XTXT Human readable text for language code "he"
he=Tiếng Do Thái
#XTXT Human readable text for language code "hi"
hi=Tiếng Hindi
#XTXT Human readable text for language code "hu"
hu=Tiếng Hungary
#XTXT Human readable text for language code "is"
is=Tiếng Iceland
#XTXT Human readable text for language code "id"
id=Tiếng Bahasa Indonesia
#XTXT Human readable text for language code "it"
it=Tiếng Ý
#XTXT Human readable text for language code "ja"
ja=Tiếng Nhật
#XTXT Human readable text for language code "kk"
kk=Tiếng Kazakh
#XTXT Human readable text for language code "ko"
ko=Tiếng Hàn
#XTXT Human readable text for language code "lv"
lv=Tiếng Latvia
#XTXT Human readable text for language code "lt"
lt=Tiếng Litva
#XTXT Human readable text for language code "ms"
ms=Tiếng Mã Lai
#XTXT Human readable text for language code "no"
no=Tiếng Na Uy
#XTXT Human readable text for language code "pl"
pl=Tiếng Ba Lan
#XTXT Human readable text for language code "pt"
pt=Tiếng Bồ Đào Nha (Brazil)
#XTXT Human readable text for language code "pt-PT"
pt-PT=Tiếng Bồ Đào Nha (Bồ Đào Nha)
#XTXT Human readable text for language code "ro"
ro=Tiếng Rumani
#XTXT Human readable text for language code "ru"
ru=Tiếng Nga
#XTXT Human readable text for language code "sr"
sr=Tiếng Serbia
#XTXT Human readable text for language code "sh"
sh=Tiếng Serbia-Croatia
#XTXT Human readable text for language code "sk"
sk=Tiếng Slovak
#XTXT Human readable text for language code "sl"
sl=Tiếng Slovenia
#XTXT Human readable text for language code "es"
es=Tiếng Tây Ban Nha
#XTXT Human readable text for language code "es-MX"
es-MX=Tiếng Tây Ban Nha (Mexico)
#XTXT Human readable text for language code "sv"
sv=Tiếng Thụy Điển
#XTXT Human readable text for language code "th"
th=Tiếng Thái
#XTXT Human readable text for language code "tr"
tr=Tiếng Thổ Nhĩ Kỳ
#XTXT Human readable text for language code "uk"
uk=Tiếng Ukraina
#XTXT Human readable text for language code "vi"
vi=Tiếng Việt
#XTIT: Delete spaces confirmation title
deleteConfirmationHeaderText=Xóa vùng dữ liệu
#XTXT: Logically delete space delete confirmation text with space name
logicallyDeleteConfirmationBodyTextWithSpaceName=Bạn có chắc là bạn muốn di chuyển vùng dữ liệu "{0}" vào thùng rác không?
#XTXT: Logically dpace delete confirmation text for multiple spaces
logicallyDeleteConfirmationBodyTextMultiple=Bạn có chắc là bạn muốn di chuyển vùng dữ liệu đã chọn {0} vào thùng rác không?
#XTXT: Delete space delete confirmation text with space name
deleteConfirmationBodyTextWithSpaceName=Bạn có chắc chắn muốn xóa vùng dữ liệu "{0}” không? Thao tác này không thể hoàn tác.
#XTXT: Delete space delete confirmation text for multiple spaces
deleteConfirmationBodyTextMultiple=Bạn có chắc là bạn muốn xóa {0} vùng dữ liệu được chọn không? Thao tác này không thể hoàn tác. Nội dung sau sẽ {1} bị xóa:
#XTXT: permanently
permanently=vĩnh viến
#XTXT: {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDetail=Nội dung sau đây sẽ bị xóa {0} và không thể khôi phục được:
#XTXT: Hint Delete space confirmation text
deleteConfirmationHint=Vui lòng nhập {0} để xác nhận thao tác xóa.
#XTXT: Delete space confirmation error check spelling
deleteConfirmationErrorCheckSpelling=Vui lòng kiểm tra chính tả của bạn và thử lại.
#XTXT: All Spaces
allSpaces=Tất cả vùng dữ liệu
#XTXT: All data
allData=Tất cả đối tượng và dữ liệu chứa trong vùng dữ liệu
#XTXT: All connections
allConnections=Tất cả kết nối được xác định trong vùng dữ liệu
#XFLD: Space tile selection box tooltip
clickToSelect=Nhấp chọn
#XTXT: All database users
allDatabaseUsers=Tất cả đối tượng và dữ liệu chứa trong bất kỳ lược đồ SQL chưa xử lý nào được liên kết với vùng dữ liệu
#XFLD: remove members button tooltip
deleteUsers=Loại bỏ thành viên
#XTXT: Space long description text
description=Mô tả (Tối đa 4000 ký tự)
#XFLD: Add Members button tooltip
addUsers=Thêm thành viên
#XFLD: Add Users button tooltip
addUsersTooltip=Thêm người dùng
#XFLD: Edit Users button tooltip
editUsersTooltip=Hiệu chỉnh người dùng
#XFLD: Remove Users button tooltip
removeUsersTooltip=Xóa người dùng
#XFLD: Searchfield placeholder
filter=Tìm kiếm
#XCOL: Users table-view column health
health=Y tế
#XCOL: Users table-view column access
access=Truy cập
#XFLD: No user found nodatatext
noDataText=Không tìm thấy người dùng
#XTIT: Members dialog title
selectUserDialogTitle=Thêm thành viên
#XTIT: User dialog title
addUserDialogTitle=Thêm người dùng
#XTIT: Delete connections confirmation title
deleteConnectionConfirmationHeaderText=Xóa kết nối
#XTIT: Delete single connection confirmation title
deleteConnectionConfirmationHeaderTextSingle=Xóa kết nối
#XTXT: Delete connection delete confirmation text
deleteConnectionConfirmationBodyText=Bạn có chắc là bạn muốn xóa các kết nối đã chọn? Chúng sẽ bị loại bỏ vĩnh viễn.
#XTIT: Remote source dialog title
selectRemoteConnectionDialogTitle=Chọn kết nối
#XTIT: Share connection dialog title
connectionSharingDialogTitle=Chia sẻ kết nối
#XTIT: Connections shared from other spaces table title
sharedConnectionsFromOtherSpaces=Kết nối được chia sẻ
#XFLD: Add remote source button tooltip
addRemoteConnections=Thêm kết nối
#XFLD: Delete remote source button tooltip
deleteRemoteConnections=Loại bỏ kết nối
#XFLD: Share remote source button tooltip
shareConnections=Chia sẻ kết nối
#XFLD: Tile-layout tooltip
tileLayout=Bố cục hình xếp
#XFLD: Table-layout tooltip
tableLayout=Bố cục bảng
#XMSG: Success message after creating space
createSpaceSuccessMessage=Vùng dữ liệu đã được tạo.
#XMSG: Success message after copying space
copySpaceSuccessMessage=Đang sao chép vùng dữ liệu "{0}" sang vùng dữ liệu "{1}"
#XMSG: Success message after deploying space
deploymentSuccessMessage=Đã bắt đầu triển khai vùng dữ liệu
#XMSG: Success message after Apache Spark update started
sparkUpdateStartedSuccessMessage=Đã bắt đầu cập nhật Apache Spark
#YMSE: Error while starting Apache Spark update
sparkUpdateStartedErrorMessage=Không cập nhật được Apache Spark
#XMSG: Success message after updating space
updateSettingsSuccessMessage=Chi tiết vùng dữ liệu đã được cập nhật.
#XMSG: Success message after unlocking space
unlockSpaceSuccessMessage=Vùng dữ liệu tạm thời được mở khóa
#XMSG: Success message after deleting a single space
deleteSpaceSuccessMessage=Đã xóa vùng dữ liệu
#XMSG: Success message after deleting multiple spaces
deleteSpacesSuccessMessage=Đã xóa vùng dữ liệu
#XMSG: Success message after restoring a single space
restoreSpaceSuccessMessage=Đã khôi phục vùng dữ liệu
#XMSG: Success message after restoring multiple spaces
restoreSpacesSuccessMessage=Đã khôi phục vùng dữ liệu
#YMSE: Error while updating settings
updateSettingsFailureMessage=Không thể cập nhật thiết lập vùng dữ liệu.
#YMSE: Error while updating data lake option
dataLakeEnabledInOtherSpace=Hồ dữ liệu đã được gán cho vùng dữ liệu khác. Chỉ một vùng dữ liệu có thể truy cập hồ dữ liệu tại một thời điểm.
#YMSE: Error while updating data lake option
virtualTablesExists=Bạn không thể hủy gán hồ dữ liệu khỏi vùng dữ liệu này vì vẫn có các phụ thuộc đối với bảng ảo*. Vui lòngxóa bảng ảo để hủy gán hồ dữ liệu khỏi vùng dữ liệu này.
#YMSE: Error while unlocking space
unlockSpaceFailureMessage=Không thể mở khóa vùng dữ liệu.
#YMSE: Error while creating space
createSpaceError=Không thể tạo vùng dữ liệu.
#YMSE: Error while creating duplicate space
createDuplicateSpaceError=Vùng dữ liệu có tên {0} đã tồn tại rồi.
#YMSE: Error while deleting a single space
deleteSpaceError=Không thể xóa vùng dữ liệu.
#YMSE: Error details while deleting a single space
deleteSpaceDetailedError=Vùng dữ liệu “{0}” của bạn không hoạt động phù hợp nữa.Vui lòng thử xóa lần nữa. Nếu vẫn không được, hãy yêu cầu quản trị viên xóa vùng dữ liệu hoặc mở phiếu.
#YMSE: Error while deleting a single space in files
deleteSpaceInFilesError=Không thể xóa dữ liệu của vùng dữ liệu trong tập tin.
#YMSE: Error while deleting a single space users
deleteSpaceUsersError=Không thể loại bỏ người dùng.
#YMSE: Error while deleting a single space schema
deleteSpaceSchemasError=Không thể loại bỏ biểu đồ.
#YMSE: Error while deleting a single space connections
deleteSpaceConnectionsError=Không thể loại bỏ kết nối.
#YMSE: Error while deleting a single space data
deleteSpaceDataError=Không thể xóa dữ liệu của vùng dữ liệu.
#YMSE: Error while deleting multiple spaces
deleteSpacesError=Không thể xóa vùng dữ liệu.
#YMSE: Error while restoring a single space
restoreSpaceError=Không thể khôi phục vùng dữ liệu.
#YMSE: Error while restoring multiple spaces
restoreSpacesError=Không thể khôi phục vùng dữ liệu.
#YMSE: Error while creating users
createUsersError=Không thể thêm người dùng.
#YMSE: Error while removing users
removeUsersError=Chúng tôi không thể xóa người dùng.
#YMSE: Error while removing user
removeUserError=không thể xóa người dùng.
#YMSE: Error assigning roles to a user
roleAssignUnAssignError=Chúng tôi không thể thêm người dùng vào vai trò trong phạm vi được chọn.\n\n Bạn không thể thêm chính mình vào vai trò trong phạm vi. Bạn có thể yêu cầu quản trị viên thêm bạn vào vai trò trong phạm vi.
#YMSE: Error assigning user to the space
userAssignError=Chúng tôi không thể gán người dùng cho vùng dữ liệu. \n\n Người dùng đã được gán cho số lượng vùng dữ liệu được cho phép tối đa (100) qua các vai trò theo phạm vi.
#YMSE: Error assigning users to the space
usersAssignError=Chúng tôi không thể gán người dùng cho vùng dữ liệu. \n\n Người dùng đã được gán cho số lượng vùng dữ liệu được cho phép tối đa (100) qua các vai trò theo phạm vi.
#YMSE: Error while fetching users
fetchUserWithScopedRolesError=Chúng tôi không thể truy xuất người dùng. Vui lòng thử lại sau.
#YMSE: Error while fetching scoped roles
fetchScopedRolesError=Chúng tôi không thể truy xuất vai trò đã tạo phạm vi.
#YMSE: Error while fetching members
fetchUserError=Không thể tìm nạp thành viên. Vui lòng thử lại sau.
#YMSE: Error while loading run-time database
loadRuntimeError=Chúng tôi không thể tải thông tin từ cơ sở dữ liệu thời gian thực hiện.
#YMSE: Error while loading spaces
loadSpacesError=Rất tiếc, đã có lỗi khi thử truy xuất vùng làm việc của bạn.
#YMSE: Error while loading haas resources
loadStorageError=Rất tiếc, đã có lỗi khi thử truy xuất dữ liệu lưu trữ.
#YMSE: Error no data could be loaded
loadDataError=Rất tiếc, đã có lỗi khi thử truy xuất dữ liệu của bạn.
#XFLD: Click to refresh storage data
clickToRefresh=Bấm vào đây để thử lại.
#XTIT: Delete space popup title
deleteSpacePopupTitle=Xóa vùng dữ liệu
#XCOL: Spaces table-view column name
name=Tên
#XCOL: Spaces table-view deployment status
deploymentStatus=Trạng thái triển khai
#XFLD: Disk label in space details
storageLabel=Đĩa (GB)
#XFLD: In-Memory label in space details
ramLabel=Bộ nhớ (GB)
#XFLD: Memory label on space card
memory=Bộ nhớ để lưu trữ
#XFLD: Storage Assignment label in space details
storrageAssignmentLabel=Lưu trữ vùng dữ liệu
#XFLD: Storage Type label in space details
storageTypeLabel=Kiểu lưu trữ
#XFLD: Enable Space Quota
enableSpaceQuota=Bật định mức vùng dữ liệu
#XFLD: No Space Quota
noSpaceQuota=Không có hạn mức vùng dữ liệu
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
diskAndInMemoryText=Cơ sở dữ liệu SAP HANA (ổ đĩa và bộ nhớ trong)
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
dataLakeFilesText=Tập tin hồ dữ liệu SAP HANA
#XFLD: Available scoped roles label
availableRoles=Vai trò trong phạm vi khả dụng
#XFLD: Selected scoped roles label
selectedRoles=Vài trò trong phạm vi được chọn
#XCOL: Spaces table-view column models
models=Mô hình
#XCOL: Spaces table-view column users
users=Người dùng
#XCOL: Spaces table-view column connections
connections=Kết nối
#XFLD: Section header overview in space detail
overview=Tổng quan
#XFLD: Section header Apache Spark in space detail
sparkManagementSection=Apache Spark
#XFLD: Section header Apache Spark in space detail
sparkManagementSubSection=Các ứng dụng
#XFLD: Section header Apache Spark in space detail
taskAssignmentSubSection=`Phép gán tác vụ
#XFLD: vCPU label in Apache Spark section
vCPULabel=vCPU
#XFLD: Memory label in Apache Spark section
memoryLabel=Bộ nhớ (GB)
#XFLD: space configuration label in Apache Spark section
spaceConfigurationLabel=Cấu hình vùng dữ liệu
#XFLD: Space Source label
sparkApplicationLabel=Ứng dụng
#XFLD: Cluster Size label
clusterSizeLabel=Kích cỡ cụm
#XFLD: Driver label
driverLabel=Trình điều khiển
#XFLD: Executor label
executorLabel=Bộ thực thi
#XFLD: max label
maxLabel=Đã sử dụng tối đa
#XFLD: TrF Default label
trFDefaultLabel=Luồng chuyển đổi mặc định
#XFLD: Merge Default label
mergeDefaultLabel=Sáp nhập mặc định
#XFLD: Optimize Default label
optimizeDefaultLabel=Tối ưu hóa mặc định
#XFLD: Deployment Default label
deploymentDefaultLabel=Triển khai bảng cục bộ (tập tin)
#XFLD: max spark source label
driverExecutorMaxUsedSparkSource={0} CPU / {1} GB
#XFLD: Object type label
taskObjectTypeLabel=Loại đối tượng
#XFLD: Task activity label
taskActivityLabel=Hoạt động
#XFLD: Task Application ID label
taskApplicationIDLabel=Ứng dụng mặc định
#XFLD: Section header in space detail
generalSettings=Thiết lập chung
#XMSG: Message strip message when a space is locked
spaceLockedMessageStrip=Hiện thời hệ thống đã khóa vùng dữ liệu này.
#XMSG: Message strip message deploy warning
deployWarningMessageStrip=Thay đổi trong mục này sẽ được triển khai ngay lập tức.
#XMSG: Message strip message modify space configuration warning
modifySpaceConfigurationMessageStrip=Xin lưu ý rằng việc thay đổi các giá trị này có thể gây ra sự cố hệ thống.
#XFLD: Button text to unlock the space again
unlockSpace=Mở khóa vùng dữ liệu
#XFLD: Info text for audit log formatted message
auditLogText=Bật nhật ký kiểm tra để ghi lại các hành động đọc hoặc thay đổi (chính sách kiểm tra). Sau đó, người quản trị có thể phân tích xem ai đã thực hiện hành động nào tại thời điểm nào.
#XMSG: Message strip  audit warning
auditLogWarningMessageStrip=Nhật ký kiểm tra có thể sử dụng một lượng lớn dung lượng lưu trữ ổ đĩa trong đối tượng thuê của bạn. Nếu bạn bật chính sách kiểm tra (hành động đọc hoặc thay đổi), bạn nên thường xuyên giám sát mức sử dụng dung lượng lưu trữ ổ đĩa (thông qua thẻ Dung lượng lưu trữ ổ đĩa đã sử dụng trong Giám sát hệ thống) để tránh tình trạng hết dung lượng ổ đĩa, có thể dẫn đến gián đoạn dịch vụ. Nếu bạn tắt chính sách kiểm tra, tất cả các mục nhật ký kiểm tra của chính sách đó sẽ bị xóa. Nếu bạn muốn giữ lại các mục nhật ký kiểm tra, hãy cân nhắc xuất chúng trước khi tắt chính sách kiểm tra.
#XFLD: Message strip link text to open in app help to guide user what to do to unlock his space permanently
unlockSpaceHelp=Hiển thị trợ giúp
#XMSG: Message strip message when a space will be locked again
spaceTemporarilyUnlockedMessageStrip=Vùng dữ liệu này vượt quá mức lưu trữ vùng dữ liệu và sẽ bị khóa trong {0} {1}.
#XMSG: Unit for remaining time until space is locked again
hours=giờ
#XMSG: Unit for remaining time until space is locked again
minutes=phút
#XFLD: Section header in space detail for auditing
detailsSectionAuditing=Kiểm tra
#XFLD: Subsection header in space detail for auditing
auditing=Thiết lập kiểm tra vùng dữ liệu
#XFLD: Hot space tooltip
hotSpaceCountTooltip=Vùng dữ liệu quan trọng: Dung lượng lưu trữ được sử dụng lớn hơn 90%.
#XFLD: Green space tooltip
greenSpaceCountTooltip=Vùng dữ liệu mạnh: Dung lượng lưu trữ được sử dụng từ 6% đến 90%.
#XFLD: Cold space tooltip
coldSpaceCountTooltip=Vùng dữ liệu lạnh: Dung lượng lưu trữ được sử dụng là 5% hoặc ít hơn.
#XFLD: Critical space tooltip
criticalSpaceCountTooltip=Vùng dữ liệu quan trọng: Dung lượng lưu trữ được sử dụng lớn hơn 90%.
#XFLD: Green space tooltip
okSpaceCountTooltip=Vùng dữ liệu mạnh: Dung lượng lưu trữ được sử dụng từ 6% đến 90%.
#XFLD: Locked space tooltip
lockedSpaceCountTooltipNew=Vùng dữ liệu bị khóa: bị khóa do thiếu bộ nhớ.
#XFLD: Locked space tooltip
lockedSpaceCountTooltip=Vùng dữ liệu bị khóa
#YMSE: Error while deleting remote source
deleteRemoteError=Không thể gỡ bỏ kết nối.
#XFLD: Space ID input tooltip
spaceIDInfoTooltip=Không thể đổi ID vùng dữ liệu sau đó.\nCác ký tự hợp lệ A - Z, 0 - 9, và _
#YMSE: Error when space name is empty
spaceNameEmptyValidationError=Nhập tên vùng dữ liệu.
#YMSE: Error when ECN Business name is empty
ecnNameEmptyValidationError=Nhập tên doanh nghiệp.
#YMSE: Error when space id is empty
spaceIDInputEmptyValidationError=Nhập ID vùng dữ liệu.
#YMSE: Error when space id contains invalid characters
spaceIDInputInValidError=Ký tự không hợp lệ. Vui lòng chỉ sử dụng A - Z, 0 - 9, và _.
#YMSE: Error when space id exists while creation
spaceIDInputDuplicateError=ID vùng dữ liệu đã tồn tại rồi.
#XFLD: Space searchfield placeholder
search=Tìm kiếm
#XMSG: Success message after creating users
createUsersSuccess=Đã thêm người dùng
#XMSG: Success message after creating user
createUserSuccess=Người dùng đã được thêm
#XMSG: Success message after updating users
updateUsersSuccess={0} người dùng được cập nhật
#XMSG: Success message after updating user
updateUserSuccess=Người dùng đã cập nhật
#XMSG: Success message after removing users
removeUsersSuccess={0} người dùng được loại bỏ
#XMSG: Success message after removing user
removeUserSuccess=Người dùng đã xóa
#XFLD: Schema name
schemaName=Tên sơ đồ
#XFLD: used of total
ofTemplate={0} của {1}
#XFLD: Assigned disk label in space overview
assignedStorageTemplate=Đĩa được gán ({0} của {1})
#XFLD: Assigned Memory label in space overview
assignedRAMTemplate=Bộ nhớ được gán ({0} của {1})
#XFLD: Storage ratio on space
accelearationRAM=Tăng tốc bộ nhớ
#XFLD: No Storage Consumption
noStorageConsumptionText=Không gán định mức lưu trữ.
#XFLD: Used disk label in space overview
usedStorageTemplate=Đĩa được sử dụng để lưu trữ ({0} trong số {1})
#XFLD: Used Memory label in space overview
usedRAMTemplate=Bộ nhớ được sử dụng để lưu trữ ({0} trong số {1})
#XFLD: Used Disk tooltip
usedStorageTooltipTemplate={0} trong số {1} đĩa được sử dụng để lưu trữ
#XFLD: Used Memory tooltip
usedRAMTooltipTemplate=Bộ nhớ {0} của {1} được sử dụng
#XFLD: Assigned Disk tooltip
assignedStorageTooltipTemplate={0} của {1} Đĩa được gán
#XFLD: Assigned Memory tooltip
assignedRAMTooltipTemplate=Bộ nhớ {0} của {1} được gán
#XFLD: Space related used storage tooltip
spaceRelatedUsedStorageTooltipTemplate=Dữ liệu của vùng dữ liệu: {0}
#XFLD: Not space related used storage tooltip
otherDataUsedStorageTooltipTemplate=Dữ liệu khác: {0}
#XFLD: Storage used exceeded popover and tooltip
storageUsedExceededText=Xem xét mở rộng kế hoạch của bạn, hoặc liên hệ Bộ phận hỗ trợ của SAP.
#XCOL: Space table-view column used Disk
usedStorage=Đĩa được sử dụng để lưu trữ
#XCOL: Space monitor column used Memory
usedRAM=Bộ nhớ được sử dụng để lưu trữ
#XCOL: Space monitor column Schema
tableSchema=Sơ đồ
#XCOL: Space monitor column Storage Type
tableStorageType=Kiểu lưu trữ
#XCOL: Space monitor column Table Type
tableType=Kiểu bảng
#XCOL: Space monitor column Record Count
tableRecordCount=Số lượng bản ghi
#XFLD: Assigned Disk
assignedStorage=Đĩa được gán để lưu trữ
#XFLD: Assigned Memory
assignedRAM=Bộ nhớ được gán để lưu trữ
#XCOL: Space table-view column storage utilization
tableStorageUtilization=Mức lưu trữ đã sử dụng
#XFLD: space status
spaceStatus=Trạng thái vùng dữ liệu
#XFLD: space type
spaceType=Kiểu vùng dữ liệu
#XFLD: space type label for space with space type "SAP BW bridge" ("SAP BW bridge" is a product name, do not translate it)
abapbridgeSpaceType=Cầu nối SAP BW
#XFLD: space type label for space with space type "Data Marketplace"
marketplaceSpaceType=Sản phẩm của nhà cung cấp dữ liệu
#XMSG: Error message for when selected space cannot be deleted due to the space type
deleteTypedSpaceError=Bạn không thể xóa vùng dữ liệu {0} vì kiểu vùng dữ liệu của nó là {1}.
#XMSG: Error message for when selected spaces cannot be deleted due to the space type
deleteTypedSpacesError=Bạn không thể xóa vùng dữ liệu được chọn {0}. Không thể xóa vùng dữ liệu có kiểu vùng dữ liệu sau đây: {1}.
#XFLD: space type label for space without space type
defaultSpaceType=SAP Datasphere
#XFLD: Tooltip for monitor space button
monitorSpace=Giám sát
#XFLD: Tooltip for edit space button
editSpace=Hiệu chỉnh vùng dữ liệu
#XMSG: Deletion warning in messagebox
deleteConfirmation=Bạn có chắc là bạn muốn xóa vùng dữ liệu này không?
#XFLD: Tooltip for delete space button
deleteSpace=Xóa vùng dữ liệu
#XFLD: storage
storage=Đĩa để lưu trữ
#XFLD: username
userName=Tên người dùng
#XFLD: port
port=Cổng
#XFLD: hostname
hostName=Tên máy chủ
#XFLD: password
password=Mật khẩu
#XBUT: Request new password button
requestPassword=Yêu cầu mật khẩu mới
#YEXP: Usage explanation in time data section
timeDataSectionHint=Tạo bảng và quy cách thời gian sử dụng trong mô hình và tập hợp dữ liệu của bạn.
#YEXP: Usage explanation in Space schema section
spaceSchemaHint=Bạn có muốn dữ liệu trong vùng dữ liệu được các công cụ hoặc ứng dụng khác sử dụng không? Nếu vậy, hãy tạo một hoặc nhiều người dùng có thể truy cập dữ liệu trong vùng dữ liệu và chọn xem liệu bạn có muốn sử dụng tất cả dữ liệu không gian trong tương lai theo mặc định hay không.
#XTIT: Create schema popup title
createSchemaDialogTitle=Tạo biểu đồ SQL mở
#XTIT: Generate Time Data dialog title
generateTimeDataDialogTitle=Tạo bảng và quy cách thời gian
#XTIT: Edit Time Data dialog title
editTimeDataDialogTitle=Hiệu chỉnh bảng và quy cách thời gian
#XTIT: Time Data token title
timeDataTokenTitle=Dữ liệu thời gian
#XTIT: Time Data token title
timeDataUpdateViews=Cập nhật màn hình dữ liệu thời gian
#XFLD: Time Data token creation in progress label
timeDataCreationInProgress=Đang tiến hành tạo....
#XFLD: Time Data token creation error label
timeDataCreationError=Tạo không thành công. Vui lòng thử lại.
#XTIT: Time Data dialog time settings title
timeDataDialogTimeSettingsTitle=Thiết lập bảng thời gian
#XTIT: Time Data dialog time data table title
timeDataDialogTranslationTablesTitle=Bảng dịch
#XTIT: Time Data dialog dimensions title
timeDataDialogTimeViewsTitle=Quy cách thời gian
#XFLD: Time Data dialog time range label
timeRangeHint=Xác định phạm vi thời gian.
#XFLD: Time Data dialog time data table label
timeDataHint=Đặt tên cho bảng.
#XFLD: Time Data dialog time data dimensions label
timeDimensionsHint=Đặt tên cho quy cách.
#XFLD: Time Data Time range description label
timerangeLabel=Phạm vi thời gian
#XFLD: Time Data dialog from year label
fromYearLabel=Từ năm
#XFLD: Time Data dialog to year label
toYearLabel=Đến năm
#XFLD: Time Data dialog calendar type label
calendarTypeLabel=Kiểu lịch
#XFLD: Time Data dialog granularity label
granularityLabel=Độ chi tiết
#XFLD: Time Data dialog technical name label
technicalNameLabel=Tên kỹ thuật
#XFLD: Time Data dialog quarters text table lable
quarterTextsTableLabel=Bảng dịch cho quý
#XFLD: Time Data dialog months text table lable
monthsTextsTableLabel=Bảng dịch cho tháng
#XFLD: Time Data dialog days text table lable
daysTextsTableLabel=Bảng dịch cho ngày
#XFLD: Time Data dialog year label
yearLabel=Quy cách năm
#XFLD: Time Data dialog quarter label
quarterLabel=Quy cách quý
#XFLD: Time Data dialog month label
monthLabel=Quy cách tháng
#XFLD: Time Data dialog day label
dayLabel=Quy cách ngày
#XFLD: Time Data dialog gregorian calendar type label
gregorian=Gregory
#XFLD: Time Data dialog time granularity day label
day=Ngày
#XFLD: Time Data dialog business name maxlength reached
timeRangeValueStateTextMaxLengthBusinessName=Đã đạt đến độ dài 1,000 ký tự tối đa.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextMaxRangeExceeded=Phạm vi thời gian tối đa là 150 năm.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeLower=“Từ năm” phải thấp hơn “Đến năm”
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeHigher="Từ năm" phải là 1900 hoặc cao hơn.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeHigher=“Đến năm” phải cao hơn “Từ năm”
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeLower=“Đến năm” phải thấp hơn năm hiện tại cộng với 100
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningFromYear=Tăng "Từ năm" có thể dẫn đến việc mất dữ liệu
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningToYear=Giảm "Đến năm" có thể dẫn đến việc mất dữ liệu
#XMSG: Time Data creation validation error message
timeDataValidationError=Có vẻ như một số trường không hợp lệ. Vui lòng kiểm tra trường được yêu cầu để tiếp tục.
#XMSG: Time Data deletion popup text
deleteTimeDataConfirmation=Bạn có chắc là muốn xóa dữ liệu  không?
#XMSG: Time Data creation success message
createTimeDataSuccess=Đã tạo dữ liệu thời gian
#XMSG: Time Data update success message
updateTimeDataSuccess=Đã cập nhật dữ liệu thời gian
#XMSG: Time Data delete success message
deleteTimeDataSuccess=Đã xóa dữ liệu thời gian
#XMSG: Time Data creation error message
createTimeDataError=Đã xảy ra lỗi khi thử tạo dữ liệu thời gian.
#XMSG: Time Data update error message
updateTimeDataError=Đã xảy ra lỗi khi thử cập nhật dữ liệu thời gian.
#XMSG: Time Data creation error message
deleteTimeDataError=Đã xảy ra lỗi khi thử xóa dữ liệu thời gian.
#XMSG: Error message when time data could not be retrieved
retrievingTimeDataError=Không thể tải dữ liệu thời gian.
#XTIT: Time Data deletion popup title
deleteTimeDataConfirmationTitle=Cảnh báo
#XTXT: Time Data deletion dependency popup text
deleteTimeDataDependencyText=Chúng tôi không thể xóa Dữ liệu thời gian của bạn vì nó được sử dụng trong các mô hình khác.
#XTXT: Time Data deletion dependency popup text singular
deleteTimeDataDependencyTextSingular=Chúng tôi không thể xóa Dữ liệu thời gian của bạn vì nó được sử dụng trong mô hình khác.
#XMSG: Validation error when required time data form field is empty
timeDataRequiredField=Trường này được yêu cầu và không thể để trống.
#XFLD: Tooltip for Open in Database Explorer button
openInDBX=Mở trong Trình tìm kiếm cơ sở dữ liệu
#YMSE: Dimension Year
dimensionYearView=Quy cách "Năm"
#YMSE: Dimension Year
dimensionQuarterView=Quy cách "Quý"
#YMSE: Dimension Year
dimensionMonthView=Quy cách "Tháng"
#YMSE: Dimension Year
dimensionDayView=Quy cách "Ngày”
#XFLD: Time Data deletion object title
timeDataUsedIn=(được sử dụng trong {0} mô hình)
#XFLD: Time Data deletion object title singular
timeDataUsedInSingular=(được sử dụng trong 1 mô hình)
#XFLD: Time Data deletion table column provider
provider=Nhà cung cấp
#XTIT: Time Data dependency dialog title
timeDataDependencyDialogTitle=Phụ thuộc
#XTIT: Create schema popup title
createSpaceSchemaUserDialogTitle=Tạo người dùng cho biểu đồ vùng dữ liệu
#XFLD: Create schema button
createSchemaButton=Tạo biểu đồ SQL mở
#XFLD: Generate TimeData button
generateTimeDataButton=Tạo bảng và quy cách thời gian
#XFLD: Show dependencies button
showDependenciesButton=Hiển thị phụ thuộc
#XFLD: tooltip for disabled buttons when user is not member of space
mustBeMemberOfSpaceForOperation=Để thực hiện thao tác này, người dùng của bạn phải là thành viên của vùng dữ liệu.
#XFLD: Create space schema user button
createSpaceSchemaUserButton=Tạo người dùng biểu đồ vùng dữ liệu
#YMSE: API Schema users load error
loadSchemaUsersError=Không thể tải danh sách người dùng.
#XTIT: API Schema-config popup title
schemaUserDetailsDialogTitle=Chi tiết người dùng biểu đồ vùng dữ liệu
#XMSG: API Schema user deletion popup text
deleteSpaceSchemaUserConfirmation=Bạn có chắc là bạn muốn xóa người dùng đã chọn không?
#XMSG: API Schema user deletion confirmation text
userDeleteSuccess=Đã xóa người dùng.
#YMSE: API Schema user deletion error
userDeleteError=Không thể xóa người dùng.
#XFLD: User deleted
userDeleted=Người dùng đã xóa.
#XTIT: Remove user popup title
removeUserConfirmationTitle=Cảnh báo
#XMSG: Remove user popup text
removeUserConfirmation=Bạn có thực sự muốn xóa người dùng không? Người dùng và các vai trò trong phạm vi được gán sẽ bị xóa khỏi vùng dữ liệu.
#XMSG: Remove users popup text
removeUsersConfirmation=Bạn có thực sự muốn xóa người dùng không? Người dùng và các vai trò trong phạm vi được gán sẽ bị xóa khỏi vùng dữ liệu.
#XBUT: Confirmation button for remove user dialog
removeUserConfirmationBtn=Xóa
#YMSE: No data text for available roles
noDataAvailableRoles=Vùng dữ liệu không được thêm vào bất kỳ vai trò theo phạm vi nào. \n Để có thể thêm người dùng vào vùng dữ liệu, trước tiên phải thêm vùng dữ liệu vào một hoặc nhiều vai trò theo phạm vi.
#YMSE: No data text for selected roles
noDataSelectedRoles=Vài trò trong phạm vi không được chọn
#XTIT: Schema-config popup title
schemaDetailsDialogTitle=Mở chi tiết cấu hình biểu đồ SQL
#XFLD: Label for Read Audit Log
auditLogRead=Bật nhật ký kiểm tra cho hoạt động đọc
#XFLD: Label for Change Audit Log
auditLogChange=Bật nhật ký kiểm tra cho hoạt động thay đổi
#XFLD: Label Audit Log Retention
auditLogRetention=Giữ nhật ký cho
#XFLD: Label Audit Log Retention Unit
retentionUnit=Ngày
#YMSE: Error when log retention time is set incorrectly
incorrectRetentionTime=Nhập số nguyên từ {0} đến {1}
#XFLD: Label for Cross Space Schema Access
spaceSchemaAccess=Sử dụng dữ liệu biểu đồ vùng dữ liệu
#XTIT: Confirm deactivation of space schema consumption dialog title
disableSpaceSchemaConsumptionDialogTitle=Ngừng sử dụng dữ liệu biểu đồ vùng dữ liệu
#XTXT: Confirm deactivation of space schema consumption dialog text
disableSpaceSchemaConsumptionDialogText=Mở biểu đồ SQL này có thể sử dụng dữ liệu của biểu đồ vùng dữ liệu. Nếu bạn ngừng sử dụng, mô hình dựa trên dữ liệu biểu đồ vùng dữ liệu có thể không còn hoạt động nữa.
#XBUT: Confirmation button for the stop consuming space schema data dialog
stopConsuming=Ngừng sử dụng
#XTXT: Tooltip for data lake icon
dataLakeIconTooltip=Vùng dữ liệu này được sử dụng để truy cập hồ dữ liệu
#XFLD: Tooltip for data lake table entry
dataLakeTableEntryTooltip=Hồ dữ liệu đã kích hoạt
#XFLD: used In-Memory linit reached tag
usedRamLimitReachedLabel=Đã đạt giới hạn bộ nhớ
#XFLD: Space storage Limit reached tag
storageLimitReachedLabel=Đã đạt đến giới hạn lưu trữ
#XFLD: Minimal Storage Limit Reached tag
minStorageLimitReachedLabel=Đã đạt đến giới hạn lưu trữ tối thiểu
#XFLD: Space ram tag
ramLimitReachedLabel=Đã đạt giới hạn bộ nhớ
#XFLD: Minimal In-Memory Limit Reached tag
minRamLimitReachedLabel=Đã đạt giới hạn bộ nhớ tối thiểu
#XFLD: Popover text after click on space storage limit warning
storageLimitReachedText=Bạn đã đạt đến giới hạn lưu trữ vùng dữ liệu được gán của {0}. Vui lòng gán thêm lưu trữ cho vùng dữ liệu.
#XFLD: System storage tag
systemStorageLimitReachedLabel=Đã đạt đến giới hạn lưu trữ hệ thống
#XMSG: Popover text after click on storage limit warning
systemStorageLimitReachedText=Bạn đã đạt đến giới hạn lưu trữ hệ thống của {0}. Hiện tại bạn không thể gán thêm lưu trữ cho vùng dữ liệu.
#XMSG: Schema deletion warning
deleteSchemaConfirmation=Xóa biểu đồ SQL mở này cũng sẽ xóa vĩnh viễn tất cả các đối tượng được lưu trữ và các liên kết được duy trì trong biểu đồ. Tiếp tục?
#XMSG: Success message after deleting a schema
schemaDeleteSuccess=Đã xóa biểu đồ
#YMSE: Error while deleting schema.
schemaDeleteError=Không thể xóa biểu đồ.
#XMSG: Success message after update a schema
schemaUpdateSuccess=Đã cập nhật biểu đồ
#YMSE: Error while updating schema.
schemaUpdateError=Không thể cập nhật biểu đồ.
#XMSG: Hint when no password is shown in the dialog.
newPasswordHint=Chúng tôi đã cung cấp mật khẩu cho biểu đồ này. Nếu bạn quên mật khẩu hoặc làm mất mật khẩu, bạn có thể yêu cầu mật khẩu mới. Vui lòng sao chép hoặc lưu mật khẩu mới lại.
#XMSG: Hint when showing the password in the dialog.
savePasswordHint=Vui lòng sao chép mật khẩu của bạn. Bạn sẽ cần nó để thiết lập kết nối đến biểu đồ này. Nếu bạn quên mật khẩu, bạn có thể mở hộp thoại này để đặt lại mật khẩu.
#XFLD,50: Hint on entering schema name in creation dialog
schemaNameHint=K.thể t.đổi tên này sau khi b.đồ được tạo.
#XFLD: Open SQL Schemas section sub headline
schemasSQL=SQL mở
#XFLD: Space schema section sub headline
schemasSpace=Vùng dữ liệu
#XFLD: HDI Container section header
HDIContainers=Vùng chứa HDI
#XTXT: Add HDI Containers button tooltip
addHDIContainers=Thêm vùng chứa HDI
#XTXT: Remove HDI Containers button tooltip
removeHDIContainers=Loại bỏ vùng chứa HDI
#XFLD: Enable HDI Containers feature button
enableHDIContainersButton=Kích hoạt truy cập
#YMSE: No data text for HDI Containers table
noDataHDIContainers=Không có vùng chứa HDI nào được thêm vào.
#YMSE: No data text for Timedata section
noDataTimedata=Không tạo quy cách và bảng thời gian.
#YMSE: No data text during runtime not available for Timedata section
runtimeNotAvailableNoDataTimedata=Không thể tải bảng thời gian và quy cách vì cơ sở dữ liệu thời gian chạy không khả dụng.
#YMSE: No data text during runtime not available for HDI section
runtimeNotAvailableNoDataHDI=Không thể tải Vùng chứa HDI vì cơ sở dữ liệu thời gian chạy không có sẵn.
#YMSE: Error while fetching HDI Containers
fetchHDIContainersError=Không thể lấy được các vùng chứa HDI. Vui lòng thử lại sau.
#XFLD Table column header for HDI Container names
HDIContainerName=Tên vùng chứa HDI
#XFLD: Enable HDI Containers feature dialog title
enableHDIContainersDialogTitle=Kích hoạt truy cập
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText1New=Bạn có thể kích hoạt SAP SQL Data Warehousing trên đối tượng thuê SAP Datasphere của bạn để trao đổi dữ liệu giữa vùng chứa HDI và vùng dữ liệu SAP Datasphere của bạn mà không cần chuyển dữ liệu.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText2=Để thực hiện điều này, hãy mở phiếu hỗ trợ bằng cách bấm vào nút bên dưới.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText3New=Một khi phiếu của bạn đã được xử lý, bạn phải tạo một hoặc nhiều vùng chứa HDI mới trong cơ sở dữ liệu thời gian chạy SAP Datasphere. Sau đó, nút Bật truy cập được thay thế bằng nút + trong phần Vùng chứa HDI cho tất cả vùng dữ liệu SAP Datasphere và bạn có thể thêm vùng chứa của mình vào vùng dữ liệu.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText41=Bạn cần thêm thông tin? Hãy đi đến %%0. Để biết thông tin chi tiết về những gì cần đưa vào phiếu yêu cầu hỗ trợ, hãy xem %%1.
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkText=Trợ giúp của SAP
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkTextSapNote3057059=Ghi chú SAP 3057059
#XBUT: Open Ticket Button Text
openTicket=Phiếu mở
#XBUT: Add Button Text
add=Thêm vào
#XBUT: Next Button Text
next=Kế tiếp
#XBUT: Edit Button Text
editUsers=Hiệu chỉnh
#XBUT: create user Button Text
createUser=Tạo
#XBUT: Update user Button Text
updateUser=Chọn
#XTIT: Add HDI Containers Dialog Title
addHDIContainersDialogTitle=Thêm vùng chứa HDI chưa được gán
#XFLD: No data text for add HDI container dialog
addHDIContainersDialogNoDataText=Chúng tôi không thể tìm thấy bất kỳ vùng chứa chưa được gán nào. \n Vùng chứa bạn đang tìm kiếm có thể đã được gán cho vùng dữ liệu.
#YMSE: Error while loading assigned HDI Containers
loadAssignedHDIContainersError=Không thể tải vùng chứa HDI được gán.
#YMSE: Error while loading assigned HDI Containers
loadUnassignedHDIContainersError=Không thể tải vùng chứa HDI.
#XMSG: Success message
succeededToAddHDIContainer=Đã thêm vùng chứa HDI
#XMSG: Success message
succeededToAddHDIContainerPlural=Đã thêm vùng chữa HDI
#XMSG: Success message
succeededToDeleteHDIContainer=Đã loại bỏ HDI
#XMSG: Success message
succeededToDeleteHDIContainerPlural=Đã loại bỏ HDI
#XFLD: Time data section sub headline
timeDataSection=Bảng và quy cách thời gian
#XFLD: Schemas list entry read audit log enabled indicator
readIndicator=Đọc
#XFLD: Schemas list entry change audit log enabled indicator
changeIndicator=Thay đổi
#XFLD: Remote sources section sub headline
allconnections=Gán kết nối
#XFLD: Remote sources section sub headline
localconnections=Kết nối cục bộ
#XFLD: Remote sources section sub headline for open Connectors
openConnectors=SAP Open Connector
#XFLD: User section sub headline
memberassignment=Gán thành viên
#XFLD: User assignment section sub headline
userAssignment=Gán người dùng
#XFLD: User section Access dropdown Member
member=Thành viên
#XFLD: User assignment section column name
user=Tên người dùng
#XTXT: Selected role count
selectedRoleToolbarText=Được chọn: {0}
#XTIT: Space detail section connections title
detailsSectionConnections=Kết nối
#XTIT: Space detail section data access title
detailsSectionDataAccess=Truy cập biểu đồ
#XTIT: Space detail section time data title
detailsSectionGenerateData=Dữ liệu thời gian
#XTIT: Space detail section members title
detailsSectionUsers=Thành viên
#XTIT: Space detail section Users title
detailsSectionUsersTitle=Người dùng
#XTIT: Out of Storage
insufficientStoragePopoverTitle=Không còn lưu trữ
#XTIT: Storage distribution
storageDistributionPopoverTitle=Mức lưu trữ đĩa đã sử dụng
#XTXT: Out of Storage popover text
insufficientStorageText=Để tạo vùng dữ liệu mới, vui lòng giảm lưu trữ được gán vùng dữ liệu khác hoặc xóa vùng dữ liệu mà bạn không còn cần nữa. Bạn có thể tăng tổng mức lưu trữ hệ thống bằng cách gọi Quản lý kế hoạch.
#XMSG: Space id length warning
spaceIdLengthWarning=Vượt quá tối đa {0} ký tự.
#XMSG: Space name length warning
spaceNameLengthWarning=Vượt quá tối đa {0} ký tự.
#XMSG: Space id reserved prefix warning
spaceIdnWarningReservedPrefix=Vui lòng không sử dụng tiền tố {0} để tránh xung đột có thể có.
#YMSE: Error while loading open SQL schemas
loadLocalSchemasError=Không thể tải biểu đồ Open SQL.
#YMSE: Error while creating open SQL schema
createLocalSchemaError=Không thể tạo biểu đồ Open SQL.
#YMSE: Error while loading all remote connections
loadRemoteConnectionsError=Không thể tải tất cả các kết nối từ xa.
#YMSE: Error while loading space details
loadSpaceDetailsError=Không thể tải chi tiết vùng dữ liệu.
#YMSE: Error while deploying space details
deploySpaceDetailsError=Không thể triển khai vùng dữ liệu.
#YMSE: Error while copying space details
copySpaceDetailsError=Không thể sao chép vùng dữ liệu.
#YMSE: Error while loading storage data
loadStorageDataError=Không thể tải dữ liệu lưu trữ.
#YMSE: Error while loading all users
loadAllUsersError=Không thể tải tất cả người dùng.
#YMSE: Failed to reset password
resetPasswordError=Không thể đặt lại mật khẩu.
#XMSG: Success message after updating space
resetPasswordSuccessMessage=Đã đặt mật khẩu mới cho biểu đồ
#YMSE: DP Agent-name too long
DBAgentNameError=Tên tác nhân DP quá dài.
#YMSE: Schema-name not valid.
schemaNameError=Tên biểu đồ không hợp lệ.
#YMSE: User name not valid.
UserNameError=Tên người dùng không hợp lệ.
#XTIT: Consumption by Storage Type
consumptionStorageTypeText=Sử dụng bởi kiểu dữ liệu
#XTIT: Consumption by Schema
consumptionSchemaText=Sử dụng bởi biểu đồ
#XTIT: Overall Consumption by schema
overallConsumptionSchemaText=Tổng lượng sử dụng bảng bởi biểu đồ
#XTIT: Overall Consumption by Table Type
overallConsumptionTableTypeText=Tổng lượng sử dụng bởi kiểu bảng
#XTIT: Tables
tableDetailsText=Chi tiết bảng
#XTIT: Table Storage Consumption
tableStorageConsumptionText=Sử dụng lưu trữ bảng
#XFLD: Table Type label
tableTypeLabel=Kiểu bảng
#XFLD: Schema label
schemaLabel=Sơ đồ
#XFLD: reset table tooltip
resetTable=Thiết lập lại bảng
#XFLD: In-Memory label in space monitor
inMemoryLabel=Bộ nhớ
#XFLD: Disk label in space monitor
diskLabel=Đĩa
#XFLD: Yes
yesLabel=Có
#XFLD: No
noLabel=Không
#XFLD: switch label: Want the data in this space to be consumable by default?
allowConsumptionByDefault=Bạn có muốn dữ liệu trong vùng dữ liệu này được tiêu thụ mặc định không?
#XFLD: Business Name
businessNameLabel=Tên doanh nghiệp
#XFLD: Refresh
refresh=Làm mới
#XMSG: No filter results title
noFilterResultsTitle=Có vẻ như thiết lập bộ lọc không hiển thị bất kỳ dữ liệu nào.
#XMSG: No filter results message
noFilterResultsMsg=Thử tinh chỉnh phần cài đặt bộ lọc của bạn và nếu lúc đó bạn vẫn không nhìn thấy bất kỳ dữ liệu nào thì hãy tạo một số bảng trong Trình tạo dữ liệu. Sau khi các bảng sử dụng mức lưu trữ, bạn sẽ có thể giám sát chúng tại đây.
#XMSG: Space monitoring Hana state red title
spaceMonitorHanaRedTitle=Cơ sở dữ liệu thời gian thực hiện không khả dụng.
#XMSG: Space monitoring Hana state red message
spaceMonitorHanaRedMsg=Vì cơ sở dữ liệu thời gian thực hiện không khả dụng, nên một số tính năng nhất định bị vô hiệu hóa và chúng tôi không thể hiển thị bất kỳ thông tin nào trên trang này.
#YMSE: Error while creating space schema user
createSpaceSchemaUserError=Không thể tạo người dùng biểu đồ vùng dữ liệu.
#YMSE: Error User name already exists
userAlreadyExistsError=Tên người dùng đã tồn tại.
#YMSE: Error Authentication failed
authenticationFailedError=Xác thực không thành công.
#XMSG: Message strip message when a schema user is locked
userLockedMessageStrip=Người dùng bị khóa do có quá nhiều lần đăng nhập không thành công. Vui lòng yêu cầu mật khẩu mới để mở khóa người dùng.
#XMSG: Success message after reset and unlock schema user
resetUnlockPasswordSuccessMessage=Đã đặt mật khẩu mới và người dùng được mở khóa
#XMSG: user is locked message
userLockedMessage=Người dùng bị khóa.
#XCOL: Users table-view column Role
spaceRole=Vai trò
#XCOL: Users table-view column Scoped Role
spaceScopedRole=Vai trò trong phạm vi
#XCOL: Users table-view column Space Admin
spaceAdmin=Quản trị viên vùng dữ liệu
#XFLD: User section dropdown value Viewer
viewer=Trình xem
#XFLD: User section dropdown value Modeler
modeler=Công cụ mô hình hóa
#XFLD: User section dropdown value Data Integrator
dataIntegrator=Bộ tích hợp dữ liệu
#XFLD: User section dropdown value Space Administrator
spaceAdministrator=Quản trị viên vùng dữ liệu
#XMSG: Success message -  Space Role updated successfully.
updateUserSpaceRoleSuccess=Đã cập nhật vai trò vùng dữ liệu
#XMSG: Error message - Failed to update Space Role.
updateUserSpaceRoleError=Không cập nhật thành công vai trò vùng dữ liệu.
#XFLD:
databaseUserNameSuffix=Hậu tố tên người dùng cơ sở dữ liệu
#XTXT: Space Schema password text
spaceSchemaPasswordText=Để thiết lập kết nối đến biểu đồ này, vui lòng sao chép mật khẩu của bạn. Trong trường hợp bạn quên mật khẩu, bạn luôn có thể yêu cầu mật khẩu mới.
#XFLD: Cloud Platform label in space schema dialog
cloudPlatformLabel=Nền tảng đám mây
#XTXT: Cloud Platform text in space schema dialog
cloudPlatformText=Để thiết lập truy cập qua người dùng này, hãy bật tiêu thụ và sao chép dữ liệu chứng thực. Trong trường hợp bạn chỉ có thể sao chép dữ liệu chứng thực không có mật khẩu, hãy chắc chắn là sau đó bạn sẽ thêm mật khẩu.
#XFLD: enable consumption in CloudPlatform label in space schema dialog
enableConsumptionInCloudPlatformLabel=Bật tiêu thụ trong nền tảng đám mây
#XFLD: Credentials for User-Provided Service label in space schema dialog
cloudPlatformCredentialsLabel=Dữ liệu chứng thực cho dịch vụ do người dùng cung cấp:
#XFLD: Credentials for User-Provided Service (Without Password) label in space schema dialog
cloudPlatformCredentialsWithoutPasswordLabel=Dữ liệu chứng thực cho dịch vụ do người dùng cung cấp (Không có mật khẩu):
#XFLD: Copy Credentials label in space schema dialog
copyCredentialsLabel=Sao chép dữ liệu chứng thực không có mật khẩu
#XFLD: Copy Credentials label in space schema dialog
copyFullCredentialsLabel=Sao chép dữ liệu chứng thực đầy đủ
#XFLD: Copy Password label in space schema dialog
copyPasswordLabel=Sao chép mật khẩu
#XMSG: Credentials copied to clipboard
credentialsCopiedMessage=Dữ liệu chứng thực được sao chép vào clipboard
#XMSG: Password copied to clipboard
passwordCopiedMessage=Mật khẩu được sao chép vào clipboard
#XMSG: create database user dialog title
createDatabaseUserDialogTitle=Tạo người dùng cơ sở dữ liệu
#XMSG: Database Users section title
databaseUsers=Người dùng cơ sở dữ liệu
#XMSG: Database User Details dialog title
databaseUserDetailsDialogTitle=Chi tiết người dùng cơ sở dữ liệu
#XFLD: database user read audit log
databaseUserAuditLogRead=Bật nhật ký kiểm tra cho hoạt động đọc và Giữ nhật ký cho
#XFLD: database user change audit log
databaseUserAuditLogChange=Bật nhật ký kiểm tra cho hoạt động thay đổi và Giữ nhật ký cho
#XMSG: Cloud Platform Access
cloudPlatformAccess=Truy cập nền tảng đám mây
#XFLD: Enable Consumption in Cloud Platform hint
enableConsumptionCloudPlatformHint=Thiết lập quyền truy cập vào vùng chứa Cơ sở hạ tầng triển khai HANA (HDI) thông qua người dùng cơ sở dữ liệu này. Để kết nối với vùng chứa HDI của bạn, mô hình SQL phải được bật lên
#XFLD: Enable HDI Consumption
enableConsumptionCloudPlatform=Bật tiêu thụ HDI
#XFLD: Enable Consumption hint
enableConsumptionHint=Bạn có muốn dữ liệu trong vùng dữ liệu có thể được tiêu thụ bởi ứng dụng hoặc công cụ khác không?
#XFLD: Enable Consumption
enableConsumption=Bật tiêu thụ SQL
#XFLD: Enable Modeling
enableModeling=Bật tạo mô hình SQL
#XMSG: Privileges for Data Modeling
privilegesModeling=Nhập dữ liệu
#XMSG: Privileges for Data Consumption
privilegesConsumption=Tiêu thụ dữ liệu cho công cụ bên ngoài
#XFLD: SQL Modeling
sqlModeling=Tạo mô hình SQL
#XFLD: SQL Consumption
sqlConsumption=Tiêu thụ SQL
#XFLD: enabled
enabled=Bật
#XFLD: disabled
disabled=Tắt
#XFLD: Edit Privileges
editPrivileges=Hiệu chỉnh đặc quyền
#XFLD: Open Database Explorer
openDBX=Mở Trình khám phá cơ sở dữ liệu
#XFLD: create database user hint
databaseCreateHint=Vui lòng lưu ý rằng bạn sẽ không thể thay đổi lại tên người dùng sau khi lưu.
#XFLD: Internal Schema Name
internalSchemaName=Tên biểu đồ nội bộ
#YMSE: Failed to load database users
loadDatabaseUserError=Không thể tải người dùng cơ sở dữ liệu.
#YMSE: Failed to delete database user
deleteDatabaseUsersError=Không thể xóa người dùng cơ sở dữ liệu.
#XMSG: Success message after deleting database user
deleteDatabaseUserSuccess=Đã xóa người dùng cơ sở dữ liệu.
#XMSG: Success message after deleting database users
deleteDatabaseUsersSuccess=Đã xóa người dùng cơ sở dữ liệu.
#XMSG: Success message after creating database user
createDatabaseUserSuccessMessage=Đã tạo người dùng cơ sở dữ liệu.
#YMSE: Failed to create database user
createDatabaseUserError=Không thể tạo người dùng cơ sở dữ liệu.
#XMSG: Success message after updating database user
updateDatabaseUserSuccessMessage=Đã cập nhật người dùng cơ sở dữ liệu.
#YMSE: Failed to update database user
updateDatabaseUserError=Không thể cập nhật người dùng cơ sở dữ liệu.
#XFLD: HDI Consumption
hdiConsumption=Tiêu thụ HDI
#XTIT: Space detail section Database Access title
detailsSectionDatabaseAccess=Truy cập cơ sở dữ liệu
#XTXT: Database users consumption hint
databaseUsersConsumptionHint=Làm cho dữ liệu không gian của bạn có thể sử dụng theo mặc định. Các mô hình trong trình tạo sẽ tự động cho phép dữ liệu có thể sử dụng được.
#XFLD: Default Consumption of Space Data:
defaultConsumptionOfSpaceData=Tiêu thụ mặc định của dữ liệu vùng dữ liệu:
#XFLD: Database User Name
databaseUserName=Tên người dùng cơ sở dữ liệu
#XMSG: Database User creation validation error message
databaseUserValidationError=Có vẻ như một số trường không hợp lệ. Vui lòng kiểm tra trường được yêu cầu để tiếp tục.
#XMSG: SQL modeling can’t be enabled since this user has been migrated.
migratedDatabaseUserHint=Việc nhập dữ liệu có thể không được bật vì người dùng này đã bị di chuyển.
#XBUT: Remove Button Text
remove=Loại bỏ
#XBUT: Remove Spaces Button Text
removeSpaces=Gỡ bỏ vùng dữ liệu
#XBUT: Remove Objects Button Text
removeObjects=Gỡ bỏ đối tượng
#XMSG: No members have been added yet.
noMembersAssigned=Chưa có thành viên đã được thêm vào.
#XMSG: No users have been added yet.
noUsersAssigned=Chưa có người dùng nào được thêm vào.
#XMSG: No database users have been created or your filter is not showing any data.
noDatabaseUsers=Không tạo người dùng cơ sở dữ liệu, hoặc bộ lọc của bạn không hiển thị bất kỳ dữ liệu nào.
#XMSG: Please enter a user name.
noDatabaseUsername=Vui lòng nhập tên người dùng.
#XMSG: The user name is too long. Please use a shorter one.
databaseUsernameTooLong=Tên người dùng quá dài. Vui lòng sử dụng tên khác ngắn hơn.
#XMSG: No privileges have been enabled and this database user will have limited functionality. Do you still want to continue?
noPrivilegesConfirmationMessage=Không có đặc quyền nào được bật và người dùng cơ sở dữ liệu này sẽ có chức năng giới hạn. Bạn vẫn muốn tiếp tục?
#XMSG: To enable audit logs for change operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogChangeMessage=Để bật nhật ký kiểm tra cho các hoạt động thay đổi, bạn cũng cần bật tính năng nhập dữ liệu. Bạn có muốn thực hiện điều này?
#XMSG: To enable audit logs for read operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogReadMessage=Để bật nhật ký kiểm tra cho các hoạt động đọc, bạn cũng cần bật tính năng nhập dữ liệu. Bạn có muốn thực hiện điều này?
#XMSG: To enable HDI consumption data ingestion and data consumption needs to be enabled. Do you want to enable all?
enableHDIConsumptionMessage=Để bật mức sử dụng HDI, bạn cũng cần bật tính năng nhập dữ liệu và sử dụng dữ liệu. Bạn có muốn thực hiện điều này?
#XMSG:
databaseUserPasswordText=Để thiết lập kết nối với người dùng cơ sở dữ liệu này, vui lòng sao chép mật khẩu của bạn. Trường hợp bạn quên mật khẩu, bạn luôn có thể yêu cầu mật khẩu mới.
#XTIT: Space detail section members title
detailsSectionMembers=Thành viên
#XMSG: New password set
newPasswordSet=Đã đặt mật khẩu mới
#XFLD: Data Ingestion
dataIngestion=Nhập dữ liệu
#XFLD: Data Consumption
dataConsumption=Sử dụng dữ liệu
#XFLD: Privileges
privileges=Ðặc quyền
#XFLD: Enable Data ingestion
enableDataIngestion=Kích hoạt nhập dữ liệu
#XMSG: Log the read and change operations for data ingestion.
databaseUserAuditHint=Ghi các hoạt động đọc và thay đổi để nhập dữ liệu.
#XMSG: Make your space data available in your HDI containers.
databaseUserHDIHint=Tạo dữ liệu không gian của bạn trong vùng chứa HDI.
#XFLD: Enable Data consumption
enableDataConsumption=Bật sử dụng dữ liệu
#XMSG: Allow other apps or tools to consume your space data.
enableDataConsumptionHint=Cho phép các ứng dụng hoặc công cụ khác sử dụng dữ liệu không gian của bạn.
#XMSG: To set up access via this schema user copy the credentials to your user-provided service. In case you can only copy the credentials without a password make sure to add the password later.
databaseUserHDIConsumptionHint=Để thiết lập quyền truy cập thông qua người dùng cơ sở dữ liệu này, hãy sao chép dữ liệu chứng thực vào dịch vụ do người dùng cung cấp. Trường hợp bạn chỉ có thể sao chép dữ liệu chứng thực mà không cần mật khẩu, hãy đảm bảo bạn sẽ thêm mật khẩu sau.
#XFLD: Data Flow Runtime Capacity label in space overview
dataFlowRuntimeCapacity=Năng suất thời gian chạy luồng dữ liệu ({0}:{1} giờ trong {2} giờ)
#XFLD: Could not load Data Flow Runtime Capacity
dataFlowRuntimeCapacityError=Không thể tải năng suất thời gian thực hiện luồng dữ liệu
#XMSG: User can grant data consumption to other users.
databaseUserConsumptionWithGrantOptionHint=Người dùng có thể cấp quyền tiêu thụ dữ liệu cho người dùng khác.
#XFLD: Enable data consumption with grant option
enableConsumptionWithGrantOption=Bật tiêu thụ dữ liệu với tùy chọn cấp quyền
#XMSG: To enable data consumption with grant option data consumption needs to be enabled. Do you want to enable both?
enableConsumptionWithGrantMessage=Để bật tiêu thụ dữ liệu với dữ liệu tùy chọn cấp quyền, tiêu thụ cần được bật. Bạn có muốn bật cả hai không?
#XFLD: Enable Automated Predictive Library (APL) and Predictive Analysis Library (PAL)
allowUsingScriptServerFunctionality=Bật thư viện phân tích dự báo (PAL) và thư viện dự báo tự động (APL)
#XMSG: User can use SAP HANA Cloud embedded machine learning functions.
allowUsingScriptServerFunctionalityHint=Người dùng có thể sử dụng chức năng học máy đã nhúng SAP HANA Cloud.
#XFLD: Password Policy
passwordPolicy=Chính sách mật khẩu
#XMSG: Password Policy
passwordPolicyHint=Bật hoặc tắt chính sách mật khẩu đã cấu hình ở đây.
#XFLD: Enable Password Policy
enablePasswordPolicy=Bật chính sách mật khẩu
#XMSG: Read Access to the Space Schema
readAccessTitle=Đọc Truy cập vào biểu đồ vùng dữ liệu
#XMSG: read access hint
readAccessHint=Cho phép người dùng cơ sở dữ liệu kết nối các công cụ bên ngoài với biểu đồ vùng dữ liệu và đọc các màn hình được hiển thị để sử dụng.
#XFLD: Space Schema
spaceSchema=Biểu đồ vùng dữ liệu
#XFLD: Enable Read Access (SQL)
enableReadAccess=Bật Đọc truy cập (SQL)
#XMSG: Allow the user to grant read access to other users.
withGrantOptionHint=Cho phép người dùng cấp quyền truy cập đọc cho những người dùng khác.
#XFLD: With Grant Option
withGrantOption=Tùy chọn cấp quyền
#XMSG: Make your space data available in your HDI containers.
enableHDIConsumptionHint=Tạo dữ liệu không gian của bạn trong vùng chứa HDI.
#XFLD: Enable HDI Consumption
enableHDIConsumption=Bật tiêu thụ HDI
#XMSG: Write Access to the User’s Open SQL Schema
writeAccessTitle=Ghi truy cập vào biểu đồ SQL mở của người dùng
#XMSG: write access hint
writeAccessHint=Cho phép người dùng cơ sở dữ liệu kết nối các công cụ bên ngoài với biểu đồ SQL mở của người dùng để tạo các thực thể dữ liệu và nhập dữ liệu để sử dụng trong vùng dữ liệu.
#XFLD: Open SQL Schema
openSQLSchema=Mở biểu đồ SQL
#XFLD: Enable Write Access (SQL DDL & DML)
enableWriteAccess=Bật ghi truy cập (SQL, DDL, & DML)
#XMSG: audit hint
auditHint=Ghi nhật ký các thao tác đọc và thay đổi trong Mở biểu đồ SQL.
#XMSG: data consumption hint
dataConsumptionHint=Theo mặc định, hiển thị tất cả các màn hình mới trong vùng dữ liệu để tiêu dùng. Người lập mô hình có thể ghi đè thiết lập này cho từng màn hình thông qua công tắc "Hiển thị để sử dụng" trong bảng điều khiển bên đầu ra màn hình. Bạn cũng có thể chọn các định dạng mà màn hình được hiển thị.
#XFLD: Expose for Consumption by Default
exposeDataConsumption=Hiển thị sử dụng theo mặc định
#XMSG: database users hint consumption hint
databaseUsersHint2New=Tạo người dùng cơ sở dữ liệu để kết nối công cụ ngoài với SAP Datasphere. Thiết lập đặc quyền để cho phép người dùng đọc dữ liệu vùng dữ liệu và tạo các thực thể dữ liệu (DDL) và nhập dữ liệu (DML) để sử dụng trong vùng dữ liệu.
#XFLD: Read
read=Đọc
#XFLD: Read (HDI)
readHDI=Đọc (HDI)
#XFLD: Write
write=Ghi
#XMSG: HDI Containers Hint
HDIContainersHint2=Cho phép truy cập vào các vùng chứa Cơ sở hạ tầng triển khai SAP HANA (HDI) trong vùng dữ liệu của bạn. Người lập mô hình có thể sử dụng tạo tác HDI làm nguồn cho các màn hình và các máy khách HDI có thể truy cập dữ liệu vùng dữ liệu của bạn.
#XMSG: Open info dialog
openDatabaseUserInfoDialog=Mở hộp thoại thông tin
#XMSG: Database user is locked. Open info dialog to unlock
openLockedDatabaseUserInfoDialog=Người dùng cơ sở dữ liệu bị khóa. Để mở khóa, hãy mở hộp thoại.
#XFLD: Table
table=Bảng
#XFLD: Space section partner configuration title
partnerConnectionConfigurationSectionTitle=Kết nối đối tác
#XFLD: Space section partner configuration subtitle
partnerConnectionConfigurationSectionSubTitle=Cấu hình kết nối đối tác
#XFLD: Space section partner configuration hint
partnerConnectionConfigurationSectionHint=Xác định hình xếp kết nối đối tác riêng của bạn bằng cách thêm biểu tượng và URL iFrame của bạn. Cấu hình này chỉ có sẵn cho đối tượng thuê này.
#XFLD: Table Name Field
partnerConnectionConfigurationName=Tên hình xếp
#XFLD: Table iFrame URL field
partnerConnectionConfigurationiFrameURL=URL iFrame
#XFLD: Table iFrame Post Message Origin field
partnerConnectionConfigurationiFramePostMessageOrigin=Nguồn gốc thông báo bài đăng iFrame
#XFLD: Table icon/image name
partnerConnectionConfigurationIcon=Biểu tượng
#XMSG: Table no result found
partnerConnectionConfigurationNotfound=Không thể tìm thấy cấu hình kết nối đối tác.
#XMSG: Partner Connection Confgurations are not available
partnerConnectionConfigurationNotAvailable=Cấu hình kết nối đối tác không thể hiển thị được khi cơ sở dữ liệu thời gian chạy không có sẵn.
#XFLD: Dialog Create Partner Connection Configuration
partnerConnectionConfigurationDialogTitleAdd=Tạo cấu hình kết nối đối tác
#XFLD: Dialog Create Partner Connection Configuration Upload Icon
partnerConnectionConfigurationButtonUloadPartnerIcon=Biểu tượng tải lên
#XFLD: Dialog Create Partner Connection Configuration Select
partnerConnectionConfigurationSelectIcon=Chọn (kích thước tối đa 200KB)
#XFLD: Dialog Create Partner Connection Configuration Enter a name
partnerConnectionConfigurationNamePlaceholder=Mẫu hình xếp đối tác
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL
partnerConnectionConfigurationiFrameURLPlaceholder=http://www.example.com
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL Check
partnerConnectionConfigurationiFramePostMessageOriginPlaceholder=.example.com
#XFLD: Dialog Create Partner Connection Configuration Browse
partnerConnectionConfigurationBrowseButton=Duyệt
#XFLD: Create Partner Connection Configuration Msg
partnerConnectionConfigurationUploadMsg=Cấu hình kết nối đối tác đã được tạo thành công.
#XMSG: Error while deleting Partner Connection configuration(s)
partnerConnectionConfigurationDeleteError=Đã xảy ra lỗ̉i trong khi xóa cấu hình kết nối đối tác.
#XMSG: Partner Connection Configuration has been successfully deleted
partnerConnectionConfigurationDeleteSuccess=Cấu hình kết nối đối tác đã được xóa thành công.
#XMSG: Error while retrieving Partner Connection Configurations
partnerConnectionConfigurationLoadError=Đã xảy ra lỗ̉i trong khi truy xuất cấu hình kết nối đối tác.
#XMSG: Error fileSizeLimitExceeded
partnerConnectionConfigurationfileSizeLimitExceeded=Không thể tải lên tập tin vì nó vượt quá kích thước tối đa 200KB.
#XBUT: Create Partner Connection Configuration
partnerConnectionConfigurationButtonCreate=Tạo cấu hình kết nối đối tác
#XBUT: Delete Partner Connection Configuration
partnerConnectionConfigurationButtonDelete=Xóa cấu hình kết nối đối tác
#XMSG: Partner Tile Creation Failure
upsertPartnerTileFailed=Không thể tạo hình xếp đối tác.
#XMSG: Partner Tile Deletion Failure
deletePartnerTileFailed=Không thể xóa hình xếp đối tác.
#XMSG: HANA cloud connector settings reset failure
resetHANASCCSettingsFailed=Thiết lập lại Cài đặt Bộ kết nối đám mây SAP HANA không thành công
#XFLD: Workload Class
workloadClass=Lớp khối lượng công việc
#XFLD: Workload Management
workloadManagement=Quản lý khối lượng công việc
#XFLD: Priority
workloadClassPriority=Ưu tiên
#XMSG:
workloadManagementPriorityHint=Bạn có thể chỉ định mức độ ưu tiên của vùng dữ liệu này khi truy vấn cơ sở dữ liệu. Nhập giá trị từ 1 (mức độ ưu tiên thấp nhất) đến 8 (mức độ ưu tiên cao nhất). Trong một tình huống mà các vùng dữ liệu tranh nhau dành các chuỗi khả dụng, vùng dữ liệu có mức độ ưu tiên cao hơn sẽ được chạy trước vùng dữ liệu có mức độ ưu tiên thấp hơn.
#XMSG:
workloadClassPriorityHint=Bạn có thể định rõ ưu tiên của vùng dữ liệu từ 0 (thấp nhất) đến 8 (cao nhất). Các câu lệnh của vùng dữ liệu có ưu tiên cao được thực hiện trước các câu lệnh của vùng dữ liệu khác có ưu tiên thấp hơn. Ưu tiên mặc định là 5. Vì giá trị 9 được dành riêng cho các hoạt động của hệ thống, nó không có sẵn cho vùng dữ liệu.
#XFLD: Statement Limits
workloadclassStatementLimits=Giới hạn câu lệnh
#XFLD: Workload Configuration
workloadConfiguration=Cấu hình khối lượng công việc
#XMSG:
workloadClassStatementLimitsHint=Bạn có thể định rõ số (hoặc phần trăm) tối đa của chuỗi và GB của bộ nhớ mà câu lệnh đang chạy đồng thời trong trong vùng dữ liệu có thể sử dụng. Bạn có thể nhập bất kỳ giá trị hoặc phần trăm nào giữa 0 (không giới hạn) và tổng bộ nhớ và chuỗi khả dụng trong đối tượng thuê. \n\n Nếu bạn định rõ giới hạn chuỗi, hãy lưu ý rằng nó có thể làm giảm hiệu suất. \n\n Nếu bạn định rõ giới hạn bộ nhớ, các câu lệnh đạt đến giới hạn bộ nhớ sẽ không được thực hiện.
#XMSG:
workloadClassStatementLimitsDescription=Cấu hình mặc định cung cấp hạn mức tài nguyên rộng rãi, đồng thời ngăn bất kỳ vùng dữ liệu đơn lẻ nào làm hệ thống quá tải.
#XMSG:
workloadClassStatementLimitCustomDescription=Bạn có thể cài tổng hạn mức chuỗi và bộ nhớ tối đa mà các câu lệnh chạy đồng thời trong vùng dữ liệu có thể sử dụng.
#XMSG:
totalStatementThreadLimitHelpText=Thiế́t lập hạn mức chuỗi quá thấp có thể ảnh hưởng đến hiệu suất của câu lệnh, trong khi giá trị quá cao hoặc bằng 0 có thể cho phép vùng dữ liệu sử dụng tất cả các chuỗi hệ thống khả dụng.
#XMSG:
totalStatementMemoryLimitHelpText=Thiết lập hạn mức bộ nhớ quá thấp có thể dẫn đến sự cố hết bộ nhớ, trong khi giá trị quá cao hoặc bằng 0 có thể cho phép vùng dữ liệu sử dụng tất cả bộ nhớ hệ thống khả dụng.
#XMSG:
totalStatementThreadLimitHelpTextRestricted=Nhập tỷ lệ phần trăm từ 1% đến 70% (hoặc số tương đương) trên tổng số chuỗi có sẵn trong đối tượng thuê của bạn. Đặt hạn mức chuỗi quá thấp có thể ảnh hưởng đến hiệu suất của câu lệnh, trong khi giá trị quá cao có thể ảnh hưởng đến hiệu suất của câu lệnh trong các vùng dữ liệu khác.
#XMSG:
totalStatementThreadLimitHelpTextDynamic=Nhập tỷ lệ phần trăm từ 1% đến {0}% (hoặc số tương đương) trên tổng số chuỗi thông báo có sẵn trong đối tượng thuê của bạn. Việc đặt giới hạn chuỗi thông báo quá thấp có thể tác động đến hiệu suất câu lệnh, trong khi giá trị cao quá mức có thể tác động đến hiệu suất của câu lệnh trong các vùng dữ liệu khác.
#XMSG:
totalStatementMemoryLimitHelpTextNew=Nhập giá trị hoặc tỷ lệ phần trăm từ 0 (không giới hạn) và tổng số lượng bộ nhớ khả dụng trong đối tượng thuê của bạn. Đặt hạn mức bộ nhớ quá thấp có thể ảnh hưởng đến hiệu suất của câu lệnh, trong khi giá trị quá cao có thể ảnh hưởng đến hiệu suất của câu lệnh trong các vùng dữ liệu khác.
#XFLD: Total Statement Thread Limit
totalStatementThreadLimit=Tổng số giới hạn chuỗi câu lệnh
#XFLD: Segmented button label
workloadclassPercentage=%
#XFLD: Segmented button label
workloadclassGigaByte=GB
#XFLD, 80: Segmented button label
workloadclassThreads=Chuỗ́i
#XFLD: Total Statement Memory Limit
totalStatementMemoryLimit=Tổng số giới hạn bộ nhớ câu lệnh
#YMSE: Error while loading customer hana info
loadCustomerHanaInfoError=Không thể tải thông tin SAP HANA của khách hàng.
#XMSG:
minimumLimitReached=Đã đạt đến giới hạn tối thiểu.
#XMSG:
maximumLimitReached=Đã đạt đến giới hạn tối đa.
#XMSG: Name Taken for Technical Name
technical-name-taken=Đã tồn tại kết nối với tên kỹ thuật mà bạn đã nhập. Vui lòng nhập tên khác.
#XMSG: Name Too long for Technical Name
technical-name-too-long=Tên kỹ thuật mà bạn đã nhập vượt quá 40 ký tự. Vui lòng nhập tên có ít ký tự hơn.
#XMSG: Technical name field empty
technical-name-field-empty=Vui lòng nhập tên kỹ thuật.
#XMSG: Generic error when user enters invalid characters for a field
field-invalid-characters=Bạn chỉ có thể sử dụng các chữ cái (a-z), số (0-9) và dấu gạch dưới (_) cho tên.
#XMSG: Generic error when user enters invalid initial characters for a field
field-underscore-trim=Tên mà bạn nhập không thể bắt đầu hoặc kết thúc bằng dấu gạch dưới (_).
#XFLD: Checkbox label for Enable Statement Limits
enableStatementLimits=Bật giới hạn câu lệnh
#XFLD: Connection SubSection Settings Title in space details
connectionSettings=Thiết lập
#XMSG: Connections tool hint in Space details section
connectionsToolHint=Để tạo hoặc hiệu chỉnh kết nối, mở ứng dụng Kết nối từ điều hướng bên hoặc nhấp vào đây:
#XBUT: Connection list Open Connection Tool Button
button-connectiontool=Đi đến Kết nối
#XFLD: Not deployed label on space tile
notDeployedLabel=Vùng dữ liệu chưa được triển khai.
#XFLD: Not deployed additional text on space tile
notDeployedText=Vui lòng triển khai vùng dữ liệu.
#XFLD: Corrupt space label on space tile
corruptSpace=Đã xảy ra lỗi.
#XFLD: Corrupt space additional text on space tile
corruptSpaceHint=Thử triển khai lại hoặc liên hệ hỗ trợ
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Kiểm tra dữ liệu nhật ký
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Dữ liệu quản trị
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Dữ liệu khác
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Dữ liệu trong vùng dữ liệu
#XMSG: Unlock Space confirmation text
unlockSpaceConfirmation=Bạn có thực sự muốn mở khóa vùng dữ liệu?
#XMSG: Lock Space confirmation text
lockSpaceConfirmation=Bạn có thực sự muốn khóa vùng dữ liệu không?
#XFLD: Lock
lock=Khóa
#XFLD: Unlock
unlock=Mở khóa
#XFLD: Locking
locking=Khóa
#XMSG: Success message after locking space
lockSpaceSuccessMsg=Vùng dữ liệu đã bị khóa
#XMSG: Success message after unlocking space
unlockSpaceSuccessMsg=Vùng dữ liệu đã được mở khóa
#XMSG: Success message after locking spaces
lockSpacesSuccessMsg=Vùng dữ liệu đã bị khóa
#XMSG: Success message after unlocking spaces
unlockSpacesSuccessMsg=Vùng dữ liệu đã được mở khóa
#YMSE: Error while locking a space
lockSpaceError=Không thể khóa vùng dữ liệu.
#YMSE: Error while unlocking a space
unlockSpaceError=Không thể mở khóa vùng dữ liệu.
#XTIT: popup title Warning
confirmationWarningTitle=Cảnh báo
#XMSG: Message strip message when a space was locked manually
manualLockedMessage=Vùng dữ liệu đã bị khóa theo cách thủ công.
#XMSG: Message strip message when a space was locked due to the amount of generated audit logs
auditLogLockedMessage=Hệ thống đã khóa vùng dữ liệu vì nhật ký kiểm tra sử dụng một lượng lớn GB ổ đĩa.
#XMSG: Message strip message when a space was locked because the assigned quota was exceeded.
quotaLockedMessage=Hệ thống đã khóa vùng dữ liệu vì vùng dữ liệu vượt quá mức phân bổ bộ nhớ hoặc mức lưu trữ đĩa của nó.
#XMSG: Unlock Spaces confirmation text
unlockSpacesConfirmation=Bạn có thực sự muốn mở khóa vùng dữ liệu được chọn không?
#XMSG: Lock Spaces confirmation text
lockSpacesConfirmation=Bạn có thực sự muốn khóa vùng dữ liệu được chọn không?
#XTIT: Scoped role editor dialog title
scopedRoleEditorDialogTitle=Trình soạn thảo vai trò trong phạm vi
#XTIT: ECN Management title
ecnManagementTitle=Quản lý vùng dữ liệu và nút Elastic Compute
#XFLD: ECNs
ecns=Nút Elastic Compute
#XFLD: ECN phase Ready
ecnReady=Sẵn sàng
#XFLD: ECN phase Running
ecnRunning=Chạy
#XFLD: ECN phase Initial
ecnInitial=Chưa sẵn sàng
#XFLD: ECN phase Starting
ecnStarting=Bắt đầu
#XFLD: ECN phase Starting Failed
ecnStartingFailed=Bắt đầu không thành công
#XFLD: ECN phase Stopping
ecnStopping=Đang dừng
#XFLD: ECN phase Stopping Failed
ecnStoppingFailed=Dừng không thành công
#XBTN: Assign Button
assign=Gán vùng dữ liệu
#XBTN: Start Header-Button
start=Bắt đầu
#XBTN: Update Header-Button
repair=Cập nhật
#XBTN: Stop Header-Button
stop=Dừng
#XFLD: ECN hours remaining
ecnHoursRemaining=1000 giờ còn lại
#XFLD: ECN block-hours remaining
ecnBlockHoursRemaining=Còn lại {0} khối giờ
#XFLD: ECN block-hour remaining
ecnBlockHourRemaining=Còn lại {0} khối giờ
#XTIT: ECN creation dialog title
ecnCreationDialogTitle=Tạo nút Elastic Compute
#XTIT: ECN edit dialog title
ecnEditDialogTitle=Hiệu chỉnh nút Elastic Compute
#XTIT: ECN deletion dialog title
ecnDeletionDialogTitle=Xóa nút Elastic Compute
#XTIT: ECN Space assignment dialog title
ecnSpaceAssignmentDialogTitle=Gán vùng dữ liệu
#XFLD: ECN ID
ECNIDLabel=Nút Elastic Compute
#XTXT: Selected toolbar text
selectedToolbarText=Được chọn: {0}
#XTIT: Elastic Compute Nodes
ECNslong=Nút Elastic Compute
#XTIT: Object assignment - Table header: Objects available
objectsAvailable=Số đối tượng
#XTIT: Object assignment - Dialog header text
selectObjects=Chọn vùng dữ liệu và đối tượng bạn muốn gán cho nút Elastic Compute của mình:
#XTIT: Object assignment - Table header title: Objects
objects=Đối tượng
#XTIT: Object assignment - Table header: Type
type=Kiểu
#XMSG: Message strip delete database user audit warning
deleteDatabaseUserWarningMessageStrip=Xin lưu ý rằng việc xóa người dùng cơ sở dữ liệu sẽ dẫn đến việc xóa tất cả mục nhật ký kiểm tra đã tạo. Nếu bạn muốn giữ nhật ký kiểm tra, hãy cân nhắc xuất chúng trước khi xóa người dùng cơ sở dữ liệu.
#XMSG: Message strip delete HDI container audit logs impact
deleteHDIContainerWarningMessageStrip=Xin lưu ý rằng việc hủy gán vùng chứa HDI từ vùng dữ liệu sẽ đến việc xóa tất cả mục nhật ký kiểm tra đã tạo. Nếu bạn muốn giữ nhật ký kiểm tra, hãy cân nhắc xuất chúng trước khi hủy gán vùng chứa HDI.
#XTXT: All audit logs
allAuditLogs=Tất cả mục nhật ký kiểm tra được tạo cho vùng dữ liệu
#XMSG: Message strip disable audit warning
disableAuditWarningMessageStrip=Xin lưu ý rằng việc vô hiệu hóa chính sách kiểm tra (đọc hoặc thay đổi hoạt động) sẽ dẫn đến việc xóa tất cả mục nhập nhật ký kiểm tra. Nếu bạn muốn giữ các mục nhật ký kiểm tra, hãy cân nhắc xuất chúng trước khi tắt chính sách kiểm tra.
#XTIT: No Spaces assigned to ECN Illustration title
noSpacesECNTitle=Chưa có vùng dữ liệu hoặc đối tượng nào được gán
#XTIT: No Spaces assigned to ECN Illustration description
noSpacesECNDescription=Để bắt đầu làm việc với nút Elastic Compute, hãy gán vùng dữ liệu hoặc đối tượng cho nút.
#XTIT: No Spaces Illustration title
noSpacesTitle=Chưa có vùng dữ liệu nào được tạo
#XTIT: No Spaces Illustration description
noSpacesDescription=Để bắt đầu nhận dữ liệu, hãy tạo một vùng dữ liệu.
#XTIT: No Spaces Illustration title
noDeletedSpacesTitle=Thùng rác trống
#XTIT: No Spaces Illustration description
noDeletedSpacesDescription=Bạn có thể khôi phục vùng dữ liệu đã xóa từ đây.
#XTXT:  {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDatabaseUsers=Một khi triển khai vùng dữ liệu, người dùng cơ sở dữ liệu sau đây sẽ bị xóa {0} và không thể khôi phục được:
#XTIT: Delete database users
deleteDatabaseUsersTitle=Xóa người dùng cơ sở dữ liệu
#YMSE: Error if ecn id already exists
ecnIDInputDuplicateError=ID đã tồn tại.
#YMSE: Error if ecn id contains invalid characters
ecnIDInputInvalidError=Vui lòng chỉ sử dụng ký tự viết thường a - z và số 0 - 9.
#YMSE: Error if ecn id is too short
ecnIDInputTooShortError=ID phải dài tối thiểu {0} ký tự.
#XMSG: ecn id length warning
ecnIdLengthWarning=Vượt quá tối đa {0} ký tự.
#XFLD: open System Monitor
systemMonitor=Giám sát hệ thống
#XFLD: open ECN schedule dialog menu entry
schedule=Lập lịch
#XFLD: open create ECN schedule dialog
createSchedule=Tạo lịch
#XFLD: open change ECN schedule dialog
changeSchedule=Hiệu chỉnh lịch
#XFLD: open delete ECN schedule dialog
deleteSchedule=Xóa lịch
#XFLD: open self assign ECN schedule dialog
selfAssignSchedule=Gán lịch cho tôi
#XFLD: open pause ECN schedule dialog
pauseSchedule=Tạm dừng lịch
#XFLD: open resume ECN schedule dialog
resumeSchedule=Tiếp tục lịch
#XFLD: View Logs
viewLogs=Xem nhật ký
#XFLD: Compute Blocks
computeBlocks=Tính toán khối
#XFLD: Memory label in ECN creation dialog
ecnMemory=Bộ nhớ (GB)
#XFLD: Storage label in ECN creation dialog
ecnStorage=Dung lượng lưu trữ (GB)
#XFLD: Number of CPU label in ECN creation dialog
ecnCPUCount=Số CPU
#XFLD: ECN updated by label
changedBy=Thay đổi bởi
#XFLD: ECN updated on label
changedOn=Thay đổi vào
#XMSG: Success message after creating a Elastic Compute Node
createEcnSuccessMessage=Đã tạo nút tính toán linh hoạt
#YMSE: Error while creating a Elastic Compute Node
createEcnError=Không thể tạo nút tính toán linh hoạt
#XMSG: Success message after updating a Elastic Compute Node
updateEcnSuccessMessage=Nút tính toán đàn hồi được cập nhật
#XMSG: Error while updating a Elastic Compute Node
udpateEcnError=Nút tính toán đàn hồi không thể được cập nhật
#XMSG: Success message after deleting a Elastic Compute Node
deleteEcnSuccessMessage=Đã xóa nút tính toán linh hoạt
#YMSE: Error while deleting a Elastic Compute Node
deleteEcnError=Không thể xóa nút tính toán linh hoạt
#XMSG: Success message after starting a Elastic Compute Node
startingEcnSuccessMessage=Bắt đầu nút tính toán linh hoạt
#XMSG: Success message after stopping a Elastic Compute Node
stoppingEcnSuccessMessage=Dừng nút tính toán linh hoạt
#YMSE: Error while starting a Elastic Compute Node
startingEcnError=Không thể bắt đầu nút tính toán linh hoạt
#YMSE: Error while stopping a Elastic Compute Node
stoppingEcnError=Không thể dừng nút tính toán linh hoạt
#XBUT: Add Object button for an ECN
assignObjects=Thêm đối tượng
#XFLD: Enable auto object assignment label
enableAutoObjAssignment=Gán tự động tất cả các đối tượng
#XFLD: object type label to be assigned
objectTypeLabel=Kiểu (sử dụng ngữ nghĩa)
#XFLD: assigned object type label
assignedObjectTypeLabel=Kiểu
#XFLD: technical name label
TechnicalNameLabel=Tên kỹ thuật
#XFLD: Object selecttion ECN label
objectSelectionECNLabel=Chọn đối tượng bạn muốn thêm vào nút tính toán đàn hồi
#XTIT: Add objects dialog title
assignObjectsTitle=Gán đối tượng của
#XFLD: object label with object count
objectLabel=Đối tượng
#XMSG: No objects available to add message.
noObjectsToAssign=Không có đối tượng nào khả dụng để gán.
#XMSG: No objects assigned message.
noAssignedObjects=Không có đối tượng nào được gán.
#XTIT: Remove objects popup title
removeObjectsOrSpacesConfirmationTitle=Cảnh báo
#XTIT: Delete ECN popup button and title
deleteECNConfirmationButtonAndTitle=Xóa
#XMSG: Remove objects popup text
removeObjectsConfirmation=Bạn có muốn xóa đối tượng được chọn không?
#XMSG: Remove spaces popup text
removeSpacesConfirmation=Bạn có thực sự muốn xóa vùng dữ liệu được chọn không?
#XBUT: Confirmation button for objects dialog
removeObjectsOrSpacesConfirmationBtn=Gỡ bỏ vùng dữ liệu
#XMSG: Success message after removing objects
removeObjectsSuccessMessage=Đã loại bỏ đối tượng hiển thị
#XMSG: Success message after assigning objects
assignObjectsSuccessMessage=Đã gán đối tượng hiển thị
#XFLD: auto object assignment label for space tile
autoObjAssignmentLabel=Tất cả các đối tượng được hiển thị
#XFLD: Spaces tab label
spacesTabLabel=Vùng dữ liệu
#XFLD: Exposed objects tab label
consumptionObjectsTabLabel=Đối tượng hiển thị
#XMSG: Success message after unassigning spaces
spacesUnassignedSuccessMessage=Đã loại bỏ vùng dữ liệu
#XMSG: Success message after unassigning space
spaceUnassignedSuccessMessage=Đã loại bỏ vùng dữ liệu
#YMSE: Error while assigning / unassigning spaces
spacesAssignUnassignError=Không thể gán hoặc loại bỏ vùng dữ liệu.
#YMSE: Error while removing objects
removeObjectsError=Chúng tôi không thể gán hoặc loại bỏ các đối tượng.
#YMSE: Error while removing object
removeObjectError=Chúng tôi không thể gán hoặc loại bỏ đối tượng.
#YMSE: Error when saving the ecn with invalid value
invalidSizingPlanError=Con số được chọn trước đó không còn hợp lệ. Vui lòng chọn số hợp lệ.
#YMSE: Error when saving the ecn with invalid value
invalidPerformanceClassError=Vui lòng chọn lớp hiệu suất hợp lệ.
#YMSE: Error when saving the ecn with invalid value
performanceClassNotAvailableError=Lớp hiệu suất đã chọn trước đó "{0}" hiện thời không hợp lệ. Vui lòng chọn lớp hiệu suất hợp lệ.
#XTXT: Delete ECN confirmation text
deleteConfirmationTxtECN=Bạn có chắc là bạn muốn xóa nút tính toán linh hoạt không?
#XFLD: tooltip for ? button
help=Trợ giúp
#XFLD: ECN edit button label
editECN=Định cấu hình
#XFLD: Technical type label for ERModel
DWC_ERMODEL=Thực thể - Mô hình mối quan hệ
#XFLD: Technical type label for Local Table
DWC_LOCAL_TABLE=Bảng cục bộ
#XFLD: Technical type label for Remote Table
DWC_REMOTE_TABLE=Bảng từ xa
#XFLD: Technical type label for Analytic Model
DWC_ANALYTIC_MODEL=Mô hình phân tích
#XFLD: Technical type label for Task Chain
DWC_TASKCHAIN=Chuỗi tác vụ
#XFLD: Technical type label for Data Flow
DWC_DATAFLOW=Luồng dữ liệu
#XFLD: Technical type label for Replication Flow
DWC_REPLICATIONFLOW=Luồng sao chép
#XFLD: Technical type label for Transformation Flow
DWC_TRANSFORMATIONFLOW=Luồng chuyển đổi
#XFLD: Technical type label for Intelligent Lookup
DWC_IDT=Tra cứu thông minh
#XFLD: Technical type label for Repository
DWC_REPOSITORY_PACKAGE=Kho lưu trữ
#XFLD: Technical type label for Enterprise Search
DWC_ESMODEL=Tìm kiếm doanh nghiệp
#XFLD: Technical type label for View
DWC_VIEW=Màn hình
#XFLD: Technical type label for Data Product
DWC_DATAPRODUCT=Sản phẩm dữ liệu
#XFLD: Technical type label for Data Access Control
DWC_DAC=Kiểm soát truy cập dữ liệu
#XFLD: Technical type label for Folder
DWC_FOLDER=Thư mục
#XFLD: Technical type label for Data Entity
DWC_BUSINESS_ENTITY=Thực thể kinh doanh
#XFLD: Technical type label for Business Entity Variant
DWC_BUSINESS_ENTITY_VARIANT=Biến thể thực thể kinh doanh
#XFLD: Technical type label for Responsibility Scenario
DWC_AUTH_SCENARIO=Kịch bản trách nhiệm
#XFLD: Technical type label for Fact Model
DWC_FACT_MODEL=Mô hình dữ kiện
#XFLD: Technical type label for Transformation Flow
DWC_PERSPECTIVE=Phối cảnh
#XFLD: Technical type label for Consumption Model
DWC_CONSUMPTION_MODEL=Mô hình tiêu thụ
#XFLD: Technical type label for Remote Connection
DWC_REMOTECONNECTION=Kết nối từ xa
#XFLD: Technical type label for Fact Model Variant
DWC_FACT_MODEL_VARIANT=Biến thể mô hình dữ kiện
#XMSG: Schedule created alert message
createScheduleSuccess=Đã tạo lịch
#XMSG: Schedule updated alert message
updateScheduleSuccess=Đã cập nhật lịch
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Đã xóa lịch
#XMSG: Schedule takeover success message
ownerChangeScheduleSuccess=Đã gán lịch cho bạn
#XMSG: Message for starting pausing of schedule
pauseScheduleStarted=Tạm dừng 1 lịch
#XMSG: Message for starting resuming of schedule
resumeScheduleStarted=Tiếp tục lại 1 lịch
#XFLD: Segmented button label
availableSpacesButton=Có sẵn
#XFLD: Segmented button label
selectedSpacesButton=Được chọn
#XFLD: Visit website button text
visitWebsite=Truy cập trang web
#XMSG: Warning message on unselecting source language
sourceLanguageRemovalWarning=Ngôn ngữ nguồn đã chọn trước đó sẽ bị loại bỏ.
#XBUT: enable translation confirmation button
enableTranslationConfirmationBtn=Bật
#XFLD: ECN performance class label
performanceClassLabel=Lớp hiệu suất
#XTXT performance class memory text
memoryText=Bộ nhớ
#XTXT performance class compute text
computeText=Tính toán
#XTXT performance class high-compute text
highComputeText=Tính toán cao
#XBUT: Recycle Bin Button Text
recycleBin=Thùng rác
#XBUT: Restore Button Text
restore=Khôi phục
#XMSG: Warning message for new Workload Management UI
priorityWarning=Phạm vi này là chỉ đọc. Bạn có thể thay đổi mức độ ưu tiên của vùng dữ liệu trong phạm vi Quản lý khối lượng công việc / Cấu hình / Hệ thống.
#XMSG: Warning message for new Workload Management UI
workloadWarning=Phạm vi này là chỉ đọc. Bạn có thể thay đổi cấu hình khối lượng công việc của vùng dữ liệu trong phạm vi Quản lý khối lượng công việc / Cấu hình / Hệ thống.
#XCOL: Space table-view column: Apache Spark vCPUs
sparkManagementVCPUs=vCPU Apache Spark
#XCOL: Space table-view column: Apache Spark Memory (GB)
sparkManagementMemory=Bộ nhớ Apache Spark (GB)
#XCOL: Space type (from Space Capability) for Data Ingestion Spaces
ingestionSpaceType=Thu thập sản phẩm dữ liệu
#XFLD: Deployment in progress nodatatext for spark table
sparkTableDeploymentInProgress=Không có sẵn dữ liệu nào vì vùng dữ liệu hiện đang được triển khai
#XFLD: Runtime data being loaded nodatatext for spark table
sparkTableDeploymentLoading=Không có sẵn dữ liệu nào vì vùng dữ liệu hiện đang được tải
#XCOL: Space type (from Space Capability) for BW Private Cloud Edition
BWPCESpaceType=SAP BW
#XBUT: Go to Instance Mappings Button
navToMappingsButton=Hiệu chỉnh ánh xạ thực thể
#XFLD: SAP HANA Data Lake Storage label if less than 1GB
dataLakeStorageUsed=< 1 GB
