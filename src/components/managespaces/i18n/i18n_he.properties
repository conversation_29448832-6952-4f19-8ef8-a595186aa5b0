#DO NOT REMOVE________________________
#XFLD: Breadcrumb for space monitoring page
spaceMonitorBreadcrumb=ניטור
#XTXT: Type name for spaces in browser tab page title
space=מרחב
#_____________________________________
#XFLD: Spaces label in
spaces=מרחבים
#XFLD: Manage plan button text
manageQuotaButtonText=נהל תוכנית
#XBUT: Manage resources button
manageResourcesButton=נהל משאבים
#XFLD: Create space button tooltip
createSpace=צור מרחב
#XFLD: Create
create=צור
#XFLD: Deploy
deploy=פרוס
#XFLD: Page
page=דף
#XFLD: Cancel
cancel=בטל
#XFLD: Update
update=עדכן
#XFLD: Save
save=שמור
#XFLD: OK
ok=OK
#XFLD: days
days=ימים
#XFLD: Space tile edit button label
edit=ערוך
#XFLD: Auto Assign all objects to space
autoAssign=הקצאה אוטומטית
#XFLD: Space tile open monitoring button label
openMonitoring=נטר
#XFLD: Delete
delete=מחק
#XFLD: Copy Space
copy=העתק
#XFLD: Close
close=סגור
#XCOL: Space table-view column status
status=סטאטוס
#XFLD: Space status active
activeLabel=פעיל
#XFLD: Space status locked
lockedLabel=נעול
#XFLD: Space status critical
criticalLabel=קריטי
#XFLD: Space status cold
coldLabel=קר
#XFLD: Space status deleted
deletedLabel=נמחק
#XFLD: Space status unknown
unknownLabel=לא ידוע
#XFLD: Space status ok
okLabel=בריא
#XFLD: Database user expired
expired=פג תוקף
#XFLD: deployed
deployed=נפרס
#XFLD: not deployed
notDeployed=לא נפרס
#XFLD: changes to deploy
changesToDeploy=שינויים לפריסה
#XFLD: pending
pending=מתבצעת פריסה
#XFLD: designtime error
designtimeError=שגיאה בזמן עיצוב
#XFLD: runtime error
runtimeError=שגיאה בזמן ריצה
#XFLD: Space created by label
createdBy=נוצר על-ידי
#XFLD: Space created on label
createdOn=נוצר בתאריך
#XFLD: Space deployed on label
deployedOn=נפרס בתאריך
#XFLD: Space ID label
spaceID=זיהוי מרחב
#XFLD: Priority label
priority=עדיפות
#XFLD: Space Priority label
spacePriority=עדיפות של מרחב
#XFLD: Space Configuration label
spaceConfiguration=תצורה של מרחב
#XFLD: Not available
notAvailable=לא זמין
#XFLD: WorkloadType default
default=ברירת מחדל
#XFLD: WorkloadType custom
custom=מותאם אישית
#XFLD: Data Lake option label; refers to component SAP HANA Cloud, Data Lake
dataLakeAccess=גישה לאגם נתונים
#XFLD: Translation label
translationLabel=תרגום
#XFLD: Source language label
sourceLanguageLabel=שפת מקור
#XFLD: Translation CheckBox label
translationCheckBox=הפשר תרגום
#XTXT: Popover hint that space has to be deployed to show user details
openDatabaseUserHintPopoverText=פרוס מרחב כדי לגשת לפרטי משתמש.
#XTXT: Popover hint that space has to be deployed to open database explorer
openDatabaseExplorerHintPopoverText=פרוס מרחב כדי לפתוח את סייר בסיס הנתונים.
#XTXT: Text for when the Data Lake check box is disabled
dataLakeOptionDisabled=לא ניתן להשתמש במרחב זה כדי לקבל גישה לאגם הנתונים כי הוא כבר נמצא בשימוש על-ידי מרחב אחר.
#XFLD: Data Lake check box text; refers to component SAP HANA Cloud, Data Lake
dataLakeCheckBox=השתמש במרחב זה כדי לגשת לאגם הנתונים.
#XFLD: Space Priority minimum label extension
low=נמוך
#XFLD: Space Priority maximum label extension
high=גבוה
#XFLD: Space name label
spaceName=שם מרחב
#XFLD: Enable deploy objects checkbox
enableDeployObjects=פרוס אובייקטים
#XTIT: Copy spaces dialog title
copySpaceDialogTitle=העתק {0}
#XTXT Human readable text for unselected source language
unSelectedSourceLanguage=(לא נבחר)
#XTXT Human readable text for language code "af"
af=אפריקנס
#XTXT Human readable text for language code "ar"
ar=ערבית
#XTXT Human readable text for language code "bg"
bg=בולגרית
#XTXT Human readable text for language code "ca"
ca=קטלאנית
#XTXT Human readable text for language code "zh"
zh=סינית מפושטת
#XTXT Human readable text for language code "zf"
zf=סינית
#XTXT Human readable text for language code "hr"
hr=קרואטית
#XTXT Human readable text for language code "cs"
cs=צ׳כית
#XTXT Human readable text for language code "cy"
cy=וולשית
#XTXT Human readable text for language code "da"
da=דנית
#XTXT Human readable text for language code "nl"
nl=הולנדית
#XTXT Human readable text for language code "en-UK"
en-UK=אנגלית (בריטניה)
#XTXT Human readable text for language code "en"
en=אנגלית (ארה"ב)
#XTXT Human readable text for language code "et"
et=אסטונית
#XTXT Human readable text for language code "fa"
fa=פרסית
#XTXT Human readable text for language code "fi"
fi=פינית
#XTXT Human readable text for language code "fr-CA"
fr-CA=צרפתית (קנדה)
#XTXT Human readable text for language code "fr"
fr=צרפתית
#XTXT Human readable text for language code "de"
de=גרמנית
#XTXT Human readable text for language code "el"
el=יוונית
#XTXT Human readable text for language code "he"
he=עברית
#XTXT Human readable text for language code "hi"
hi=הינדית
#XTXT Human readable text for language code "hu"
hu=הונגרית
#XTXT Human readable text for language code "is"
is=איסלנדית
#XTXT Human readable text for language code "id"
id=בהאסה אינדונזיה
#XTXT Human readable text for language code "it"
it=איטלקית
#XTXT Human readable text for language code "ja"
ja=יפנית
#XTXT Human readable text for language code "kk"
kk=קזאחית
#XTXT Human readable text for language code "ko"
ko=קוריאנית
#XTXT Human readable text for language code "lv"
lv=לטבית
#XTXT Human readable text for language code "lt"
lt=ליטאית
#XTXT Human readable text for language code "ms"
ms=Bahasa Melayu
#XTXT Human readable text for language code "no"
no=נורווגית
#XTXT Human readable text for language code "pl"
pl=פולנית
#XTXT Human readable text for language code "pt"
pt=פורטוגזית (ברזיל)
#XTXT Human readable text for language code "pt-PT"
pt-PT=פורטוגזית (פורטוגל)
#XTXT Human readable text for language code "ro"
ro=רומנית
#XTXT Human readable text for language code "ru"
ru=רוסית
#XTXT Human readable text for language code "sr"
sr=סרבית
#XTXT Human readable text for language code "sh"
sh=סרבו-קרואטית
#XTXT Human readable text for language code "sk"
sk=סלובקית
#XTXT Human readable text for language code "sl"
sl=סלובנית
#XTXT Human readable text for language code "es"
es=ספרדית
#XTXT Human readable text for language code "es-MX"
es-MX=ספרדית (מקסיקו)
#XTXT Human readable text for language code "sv"
sv=שוודית
#XTXT Human readable text for language code "th"
th=תאית
#XTXT Human readable text for language code "tr"
tr=טורקית
#XTXT Human readable text for language code "uk"
uk=אוקראינית
#XTXT Human readable text for language code "vi"
vi=ויאטנמית
#XTIT: Delete spaces confirmation title
deleteConfirmationHeaderText=מחק מרחבים
#XTXT: Logically delete space delete confirmation text with space name
logicallyDeleteConfirmationBodyTextWithSpaceName=האם אתה בטוח שאתה רוצה להעביר את המרחב ''{0}'' לסל המחזור?
#XTXT: Logically dpace delete confirmation text for multiple spaces
logicallyDeleteConfirmationBodyTextMultiple=האם אתה בטוח שאתה רוצה להעביר את {0} המרחבים שנבחרו לסל המחזור?
#XTXT: Delete space delete confirmation text with space name
deleteConfirmationBodyTextWithSpaceName=בטוח שברצונך למחוק את מרחב "{0}"? פעולה זו לא ניתנת לביטול.
#XTXT: Delete space delete confirmation text for multiple spaces
deleteConfirmationBodyTextMultiple=בטוח שברצונך למחוק את {0} המרחבים שנבחרו? פעולה זו לא ניתנת לביטול. התוכן הבא יימחק {1}:
#XTXT: permanently
permanently=לצמיתות
#XTXT: {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDetail=התוכן הבא יימחק {0} ולא ניתן יהיה לשחזר אותו:
#XTXT: Hint Delete space confirmation text
deleteConfirmationHint=הקש {0} כדי לאשר את המחיקה.
#XTXT: Delete space confirmation error check spelling
deleteConfirmationErrorCheckSpelling=בדוק את האיות ונסה שוב.
#XTXT: All Spaces
allSpaces=כל המרחבים
#XTXT: All data
allData=כל האובייקטים והנתונים במרחב
#XTXT: All connections
allConnections=כל החיבורים שהוגדרו במרחב
#XFLD: Space tile selection box tooltip
clickToSelect=לחץ לבחירה
#XTXT: All database users
allDatabaseUsers=כל האובייקטים והנתונים שבכל תרשים Open SQL המשויך למרחב
#XFLD: remove members button tooltip
deleteUsers=הסר משתמשים
#XTXT: Space long description text
description=תיאור (מקסימום 4000 תווים)
#XFLD: Add Members button tooltip
addUsers=הוסף משתמשים
#XFLD: Add Users button tooltip
addUsersTooltip=הוסף משתמשים
#XFLD: Edit Users button tooltip
editUsersTooltip=ערוך משתמשים
#XFLD: Remove Users button tooltip
removeUsersTooltip=הסר משתמשים
#XFLD: Searchfield placeholder
filter=חפש
#XCOL: Users table-view column health
health=תקינות
#XCOL: Users table-view column access
access=גישה
#XFLD: No user found nodatatext
noDataText=לא נמצא משתמש
#XTIT: Members dialog title
selectUserDialogTitle=הוסף משתמשים
#XTIT: User dialog title
addUserDialogTitle=הוסף משתמשים
#XTIT: Delete connections confirmation title
deleteConnectionConfirmationHeaderText=מחק חיבורים
#XTIT: Delete single connection confirmation title
deleteConnectionConfirmationHeaderTextSingle=מחק חיבור
#XTXT: Delete connection delete confirmation text
deleteConnectionConfirmationBodyText=בטוח שברצונך למחוק את החיבורים שנבחרו? הם יוסרו לצמיתות.
#XTIT: Remote source dialog title
selectRemoteConnectionDialogTitle=בחר חיבורים
#XTIT: Share connection dialog title
connectionSharingDialogTitle=שתף חיבור
#XTIT: Connections shared from other spaces table title
sharedConnectionsFromOtherSpaces=חיבורים משותפים
#XFLD: Add remote source button tooltip
addRemoteConnections=הוסף חיבורים
#XFLD: Delete remote source button tooltip
deleteRemoteConnections=הסר חיבורים
#XFLD: Share remote source button tooltip
shareConnections=שתף חיבורים
#XFLD: Tile-layout tooltip
tileLayout=פריסת אריחים
#XFLD: Table-layout tooltip
tableLayout=פריסת טבלה
#XMSG: Success message after creating space
createSpaceSuccessMessage=נוצר מרחב
#XMSG: Success message after copying space
copySpaceSuccessMessage=מעתיק מרחב "{0}" למרחב "{1}"
#XMSG: Success message after deploying space
deploymentSuccessMessage=פריסת המרחב החלה
#XMSG: Success message after Apache Spark update started
sparkUpdateStartedSuccessMessage=עדכון Apache Spark החל
#YMSE: Error while starting Apache Spark update
sparkUpdateStartedErrorMessage=עדכון Apache Spark נכשל
#XMSG: Success message after updating space
updateSettingsSuccessMessage=פרטי המרחב עודכנו
#XMSG: Success message after unlocking space
unlockSpaceSuccessMessage=נעילת המרחב בוטלה זמנית
#XMSG: Success message after deleting a single space
deleteSpaceSuccessMessage=מרחב נמחק
#XMSG: Success message after deleting multiple spaces
deleteSpacesSuccessMessage=מרחבים נמחקו
#XMSG: Success message after restoring a single space
restoreSpaceSuccessMessage=מרחב שוחזר
#XMSG: Success message after restoring multiple spaces
restoreSpacesSuccessMessage=מרחבים שוחזרו
#YMSE: Error while updating settings
updateSettingsFailureMessage=לא ניתן היה לעדכן את הגדרות המרחב.
#YMSE: Error while updating data lake option
dataLakeEnabledInOtherSpace=אגם הנתונים כבר מוקצה למרחב אחר. רק מרחב אחד יכול לגשת לאגם הנתונים בכל פעם.
#YMSE: Error while updating data lake option
virtualTablesExists=לא ניתן לבטל את ההקצאה של אגם הנתונים למרחב הזה, כי עדיין יש יחסי תלות עם טבלאות וירטואליות*. מחק את הטבלאות הוירטואליות כי לבטל את ההקצאה של אגם הנתונים למרחב הזה.
#YMSE: Error while unlocking space
unlockSpaceFailureMessage=לא ניתן היה לבטל את נעילת המרחב.
#YMSE: Error while creating space
createSpaceError=לא ניתן היה ליצור את המרחב.
#YMSE: Error while creating duplicate space
createDuplicateSpaceError=מרחב עם השם {0} כבר קיים.
#YMSE: Error while deleting a single space
deleteSpaceError=לא ניתן היה למחוק את המרחב.
#YMSE: Error details while deleting a single space
deleteSpaceDetailedError=המרחב “{0}” לא עובד כראוי יותר. נסה למחוק אותו שוב. אם הוא עדיין לא עובד, בקש ממנהל המערכת למחוק את המרחב שלך או פתח קריאת שירות.
#YMSE: Error while deleting a single space in files
deleteSpaceInFilesError=לא ניתן היה למחוק את נתוני המרחב ב'קבצים'.
#YMSE: Error while deleting a single space users
deleteSpaceUsersError=לא ניתן היה להסיר את המשתמשים.
#YMSE: Error while deleting a single space schema
deleteSpaceSchemasError=לא ניתן היה להסיר את התרשימים.
#YMSE: Error while deleting a single space connections
deleteSpaceConnectionsError=לא ניתן היה להסיר את החיבורים.
#YMSE: Error while deleting a single space data
deleteSpaceDataError=לא ניתן היה למחוק את נתוני המרחב.
#YMSE: Error while deleting multiple spaces
deleteSpacesError=לא ניתן היה למחוק את המרחבים.
#YMSE: Error while restoring a single space
restoreSpaceError=לא ניתן היה לשחזר את המרחב.
#YMSE: Error while restoring multiple spaces
restoreSpacesError=לא ניתן היה לשחזר את המרחבים.
#YMSE: Error while creating users
createUsersError=לא ניתן היה להוסיף את המשתמשים.
#YMSE: Error while removing users
removeUsersError=לא ניתן היה להסיר את המשתמשים.
#YMSE: Error while removing user
removeUserError=לא ניתן היה להסיר את המשתמש.
#YMSE: Error assigning roles to a user
roleAssignUnAssignError=לא ניתן היה להוסיף את המשתמש לתפקיד בטווח שנבחר. \n\nלא ניתן להוסיף אותך לתפקיד בטווח. ניתן לבקש nמנהל המערכת להוסיף אותך לתפקיד.
#YMSE: Error assigning user to the space
userAssignError=לא ניתן היה להקצות את המשתמש למרחב. \n\n המשתמש כבר מוקצה למספר המותר המקסימלי (100) של מרחבים בתפקידים עם טווח.
#YMSE: Error assigning users to the space
usersAssignError=לא ניתן היה להקצות את המשתמשים למרחב. \n\n המשתמש כבר מוקצה למספר המותר המקסימלי (100) של מרחבים בתפקידים עם טווח.
#YMSE: Error while fetching users
fetchUserWithScopedRolesError=לא ניתן היה לאחזר את המשתמשים.
#YMSE: Error while fetching scoped roles
fetchScopedRolesError=לא ניתן היה לאחזר את התפקידים המטווחים.
#YMSE: Error while fetching members
fetchUserError=לא ניתן היה להביא את החברים. נסה שוב מאוחר יותר.
#YMSE: Error while loading run-time database
loadRuntimeError=לא ניתן היה לטעון מידע מבסיס הנתונים של זמן ריצה.
#YMSE: Error while loading spaces
loadSpacesError=משהו השתבש במהלך ניסיון לאחזר את המרחבים שלך.
#YMSE: Error while loading haas resources
loadStorageError=משהו השתבש בניסיון לאחזר את נתוני האחסון.
#YMSE: Error no data could be loaded
loadDataError=משהו השתבש במהלך ניסיון לאחזר את הנתונים שלך.
#XFLD: Click to refresh storage data
clickToRefresh=לחץ כאן כדי לנסות שוב.
#XTIT: Delete space popup title
deleteSpacePopupTitle=מחק מרחב
#XCOL: Spaces table-view column name
name=שם
#XCOL: Spaces table-view deployment status
deploymentStatus=סטאטוס פריסה
#XFLD: Disk label in space details
storageLabel=דיסק (GB)
#XFLD: In-Memory label in space details
ramLabel=זיכרון (GB)
#XFLD: Memory label on space card
memory=זיכרון לאחסון
#XFLD: Storage Assignment label in space details
storrageAssignmentLabel=אחסון במרחב
#XFLD: Storage Type label in space details
storageTypeLabel=סוג אחסון
#XFLD: Enable Space Quota
enableSpaceQuota=הפעל מכסה של מרחב
#XFLD: No Space Quota
noSpaceQuota=אין מכסת מרחב
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
diskAndInMemoryText=בסיס נתונים של SAP HANA (דיסק וזיכרון פנימי)
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
dataLakeFilesText=קבצי אגם נתונים של SAP HANA
#XFLD: Available scoped roles label
availableRoles=תפקידים זמינים בטווח
#XFLD: Selected scoped roles label
selectedRoles=תפקידים בטווח שנבחרו
#XCOL: Spaces table-view column models
models=מודלים
#XCOL: Spaces table-view column users
users=משתמשים
#XCOL: Spaces table-view column connections
connections=חיבורים
#XFLD: Section header overview in space detail
overview=סקירה
#XFLD: Section header Apache Spark in space detail
sparkManagementSection=Apache Spark
#XFLD: Section header Apache Spark in space detail
sparkManagementSubSection=יישומים
#XFLD: Section header Apache Spark in space detail
taskAssignmentSubSection=הקצאת משימה
#XFLD: vCPU label in Apache Spark section
vCPULabel=vCPUs
#XFLD: Memory label in Apache Spark section
memoryLabel=זיכרון (GB)
#XFLD: space configuration label in Apache Spark section
spaceConfigurationLabel=תצורה של מרחב
#XFLD: Space Source label
sparkApplicationLabel=יישום
#XFLD: Cluster Size label
clusterSizeLabel=גודל אשכול
#XFLD: Driver label
driverLabel=מניע
#XFLD: Executor label
executorLabel=מבצע
#XFLD: max label
maxLabel=מקסימום בשימוש
#XFLD: TrF Default label
trFDefaultLabel=ברירת מחדל של תזרים טרנספורמציה
#XFLD: Merge Default label
mergeDefaultLabel=מזג ברירת מחדל
#XFLD: Optimize Default label
optimizeDefaultLabel=מטב ברירת מחדל
#XFLD: Deployment Default label
deploymentDefaultLabel=פריסת טבלה מקומית (קובץ)
#XFLD: max spark source label
driverExecutorMaxUsedSparkSource={0} CPU / {1} GB
#XFLD: Object type label
taskObjectTypeLabel=סוג אובייקט
#XFLD: Task activity label
taskActivityLabel=פעילות
#XFLD: Task Application ID label
taskApplicationIDLabel=יישום ברירת מחדל
#XFLD: Section header in space detail
generalSettings=הגדרות כלליות
#XMSG: Message strip message when a space is locked
spaceLockedMessageStrip=מרחב זה נעול כעת על-ידי המערכת.
#XMSG: Message strip message deploy warning
deployWarningMessageStrip=שינויים במקטע זה ייפרסו מייד.
#XMSG: Message strip message modify space configuration warning
modifySpaceConfigurationMessageStrip=שים לב, שינוי הערכים האלה יכול לגרום לבעיות ביצועים.
#XFLD: Button text to unlock the space again
unlockSpace=בטל נעילת מרחב
#XFLD: Info text for audit log formatted message
auditLogText=הפעל יומני ביקורת עדי לרשום פעולות קריאה או שינוי (מדיניות ביקורת). מנהלי מערכת יכולים לנתח מי ביצע ואיזו פעולה ובאיזו נקודה בזמן.
#XMSG: Message strip  audit warning
auditLogWarningMessageStrip=יומני ביקורת יכולים לצרוך סכום גדול של אחסון שטח בדיסק בדייר שלך. אם תפעיל מדיניות ביקורת (פעולות קריאה או שינוי) עליך לעקוב בקביעות אחר השימוש באחסון השטח בדיסק (דרך הכרטיס 'שטח אחסון בשימוש' בצג המערכת) כדי להימנע מחוסר מקום בשטח הדיסק שיכול להוביל להפרעות בשירות. אם תשבית את מדיניות הביקורת כל ההזנות ביומן הביקורת יימחקו. אם ברצונך להשאיר את ההזנות של יומן הביקורת, שקול לייצא אותן לפני שתשבית את מדיניות הביקורת.
#XFLD: Message strip link text to open in app help to guide user what to do to unlock his space permanently
unlockSpaceHelp=הצג עזרה
#XMSG: Message strip message when a space will be locked again
spaceTemporarilyUnlockedMessageStrip=מרחב זה חורג מהאחסון שלו ויינעל בעוד {0} {1}.
#XMSG: Unit for remaining time until space is locked again
hours=שעות
#XMSG: Unit for remaining time until space is locked again
minutes=דקות
#XFLD: Section header in space detail for auditing
detailsSectionAuditing=ביקורת
#XFLD: Subsection header in space detail for auditing
auditing=הגדרות ביקורת מרחב
#XFLD: Hot space tooltip
hotSpaceCountTooltip=מרחבים קריטיים: האחסון שבשימוש גדול יותר מ-90%
#XFLD: Green space tooltip
greenSpaceCountTooltip=מרחבים בריאים: האחסון שבשימוש הוא בין 6% ל-90%.
#XFLD: Cold space tooltip
coldSpaceCountTooltip=מרחבים קרים: האחסון שבשימוש הוא 5% או פחות.
#XFLD: Critical space tooltip
criticalSpaceCountTooltip=מרחבים קריטיים: האחסון שבשימוש גדול יותר מ-90%
#XFLD: Green space tooltip
okSpaceCountTooltip=מרחבים בריאים: האחסון שבשימוש הוא בין 6% ל-90%.
#XFLD: Locked space tooltip
lockedSpaceCountTooltipNew=מרחבים נעולים: חסום עקב זיכרון לא מספיק.
#XFLD: Locked space tooltip
lockedSpaceCountTooltip=מרחבים נעולים
#YMSE: Error while deleting remote source
deleteRemoteError=לא ניתן היה להסיר את החיבורים.
#XFLD: Space ID input tooltip
spaceIDInfoTooltip=לא ניתן לשנות את זיהוי המרחב מאוחר יותר.\nתווים חוקיים A - Z,‏ 0 - 9, ו-_
#YMSE: Error when space name is empty
spaceNameEmptyValidationError=הזן שם מרחב.
#YMSE: Error when ECN Business name is empty
ecnNameEmptyValidationError=הזן שם עסקי.
#YMSE: Error when space id is empty
spaceIDInputEmptyValidationError=הזן זיהוי מרחב.
#YMSE: Error when space id contains invalid characters
spaceIDInputInValidError=תווים לא חוקיים. השתמש רק בתווים הבאים: A - Z, ‏0 - 9 ו-_.
#YMSE: Error when space id exists while creation
spaceIDInputDuplicateError=זיהוי המרחב כבר קיים.
#XFLD: Space searchfield placeholder
search=חפש
#XMSG: Success message after creating users
createUsersSuccess=נוספו משתמשים.
#XMSG: Success message after creating user
createUserSuccess=נוסף משתמש
#XMSG: Success message after updating users
updateUsersSuccess={0} משתמשים עודכנו
#XMSG: Success message after updating user
updateUserSuccess=עודכן משתמש
#XMSG: Success message after removing users
removeUsersSuccess={0} משתמשים הוסרו
#XMSG: Success message after removing user
removeUserSuccess=הוסר משתמש
#XFLD: Schema name
schemaName=שם תרשים
#XFLD: used of total
ofTemplate={0} מתוך {1}
#XFLD: Assigned disk label in space overview
assignedStorageTemplate=הדיסק שהוקצה ({0} מתוך {1})
#XFLD: Assigned Memory label in space overview
assignedRAMTemplate=זיכרון שהוקצה ({0} מתוך {1})
#XFLD: Storage ratio on space
accelearationRAM=האצת הזיכרון
#XFLD: No Storage Consumption
noStorageConsumptionText=לא הוקצתה מכסת אחסון.
#XFLD: Used disk label in space overview
usedStorageTemplate=דיסק בשימוש לאחסון ({0} מתוך {1})
#XFLD: Used Memory label in space overview
usedRAMTemplate=זיכרון בשימוש לאחסון ({0} מתוך {1})
#XFLD: Used Disk tooltip
usedStorageTooltipTemplate={0} מתוך {1} דיסק בשימוש לאחסון
#XFLD: Used Memory tooltip
usedRAMTooltipTemplate={0} מתוך {1} זיכרון בשימוש
#XFLD: Assigned Disk tooltip
assignedStorageTooltipTemplate={0} מתוך {1} של הדיסק הוקצה
#XFLD: Assigned Memory tooltip
assignedRAMTooltipTemplate={0} מתוך {1} זיכרון הוקצה
#XFLD: Space related used storage tooltip
spaceRelatedUsedStorageTooltipTemplate=נתוני מרחב: {0}
#XFLD: Not space related used storage tooltip
otherDataUsedStorageTooltipTemplate=נתונים אחרים: {0}
#XFLD: Storage used exceeded popover and tooltip
storageUsedExceededText=שקול להאריך את התוכנית שלך, או צור קשר עם התמיכה של SAP.
#XCOL: Space table-view column used Disk
usedStorage=דיסק בשימוש לאחסון
#XCOL: Space monitor column used Memory
usedRAM=זיכרון בשימוש לאחסון
#XCOL: Space monitor column Schema
tableSchema=תרשים
#XCOL: Space monitor column Storage Type
tableStorageType=סוג אחסון
#XCOL: Space monitor column Table Type
tableType=סוג טבלה
#XCOL: Space monitor column Record Count
tableRecordCount=ספירת רשומות
#XFLD: Assigned Disk
assignedStorage=דיסק שהוקצה לאחסון
#XFLD: Assigned Memory
assignedRAM=זיכרון שהוקצה לאחסון
#XCOL: Space table-view column storage utilization
tableStorageUtilization=אחסון שנמצא בשימוש
#XFLD: space status
spaceStatus=סטאטוס מרחב
#XFLD: space type
spaceType=סוג מרחב
#XFLD: space type label for space with space type "SAP BW bridge" ("SAP BW bridge" is a product name, do not translate it)
abapbridgeSpaceType=גשר SAP BW
#XFLD: space type label for space with space type "Data Marketplace"
marketplaceSpaceType=מוצר של ספק נתונים
#XMSG: Error message for when selected space cannot be deleted due to the space type
deleteTypedSpaceError=לא ניתן למחוק את מרחב {0} מכיוון שסוג המרחב שלו הוא {1}.
#XMSG: Error message for when selected spaces cannot be deleted due to the space type
deleteTypedSpacesError=לא ניתן למחוק את {0} המרחבים שנבחרו. לא ניתן למחוק מרחבים מסוג המרחבים הבאים: {1}.
#XFLD: space type label for space without space type
defaultSpaceType=SAP Datasphere
#XFLD: Tooltip for monitor space button
monitorSpace=נטר
#XFLD: Tooltip for edit space button
editSpace=ערוך מרחב
#XMSG: Deletion warning in messagebox
deleteConfirmation=האם אתה בטוח שברצונך למחוק את המרחב הזה?
#XFLD: Tooltip for delete space button
deleteSpace=מחק מרחב
#XFLD: storage
storage=דיסק לאחסון
#XFLD: username
userName=שם משתמש
#XFLD: port
port=יציאה
#XFLD: hostname
hostName=שם מחשב מארח
#XFLD: password
password=סיסמה
#XBUT: Request new password button
requestPassword=בקש סיסמה חדשה
#YEXP: Usage explanation in time data section
timeDataSectionHint=צור לוחות זמנים ומדדים שניתן להשתמש בהם במודלים ובסיפורים שלך.
#YEXP: Usage explanation in Space schema section
spaceSchemaHint=האם תרצה שהנתונים במרחב שלך יהיו ניתנים לצריכה על-ידי כלים או יישומים אחרים? אם כן, צור משתמש אחד או יותר שיש להם גישה לנתונים במרחב שלך ובחר אם ברצונך שכל נתוני המרחב העתידיים יהיו ניתנים לצריכה כברירת מחדל.
#XTIT: Create schema popup title
createSchemaDialogTitle=צור תרשים Open SQL
#XTIT: Generate Time Data dialog title
generateTimeDataDialogTitle=צור לוחות זמנים וממדים
#XTIT: Edit Time Data dialog title
editTimeDataDialogTitle=ערוך לוחות זמנים וממדים
#XTIT: Time Data token title
timeDataTokenTitle=נתוני שעות עבודה
#XTIT: Time Data token title
timeDataUpdateViews=עדכן תצוגות של נתוני שעות עבודה
#XFLD: Time Data token creation in progress label
timeDataCreationInProgress=יצירה בתהליך...
#XFLD: Time Data token creation error label
timeDataCreationError=היצירה נכשלה. נסה שוב.
#XTIT: Time Data dialog time settings title
timeDataDialogTimeSettingsTitle=הגדרות של לוח זמנים
#XTIT: Time Data dialog time data table title
timeDataDialogTranslationTablesTitle=טבלאות תרגום
#XTIT: Time Data dialog dimensions title
timeDataDialogTimeViewsTitle=ממדי זמן
#XFLD: Time Data dialog time range label
timeRangeHint=הגדר את טווח הזמנים.
#XFLD: Time Data dialog time data table label
timeDataHint=תן שם לטבלה שלך.
#XFLD: Time Data dialog time data dimensions label
timeDimensionsHint=תן שם לממדים שלך.
#XFLD: Time Data Time range description label
timerangeLabel=טווח זמנים
#XFLD: Time Data dialog from year label
fromYearLabel=החל משנה
#XFLD: Time Data dialog to year label
toYearLabel=עד שנה
#XFLD: Time Data dialog calendar type label
calendarTypeLabel=סוג לוח שנה
#XFLD: Time Data dialog granularity label
granularityLabel=גרעיניות
#XFLD: Time Data dialog technical name label
technicalNameLabel=שם טכני
#XFLD: Time Data dialog quarters text table lable
quarterTextsTableLabel=טבלת תרגום עבור רבעונים
#XFLD: Time Data dialog months text table lable
monthsTextsTableLabel=טבלת תרגום עבור חודשים
#XFLD: Time Data dialog days text table lable
daysTextsTableLabel=טבלת תרגום עבור ימים
#XFLD: Time Data dialog year label
yearLabel=ממד של שנה
#XFLD: Time Data dialog quarter label
quarterLabel=ממד של רבעון
#XFLD: Time Data dialog month label
monthLabel=ממד של חודש
#XFLD: Time Data dialog day label
dayLabel=ממד של יום
#XFLD: Time Data dialog gregorian calendar type label
gregorian=גרגוריאני
#XFLD: Time Data dialog time granularity day label
day=יום
#XFLD: Time Data dialog business name maxlength reached
timeRangeValueStateTextMaxLengthBusinessName=אורך מקסימלי של 1000 תווים הושג.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextMaxRangeExceeded=טווח השעות המקסימלי הוא 150 שנה.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeLower="משנה" צריך להיות נמוך יותר מ"עד שנה"
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeHigher="משנה" חייב להיות 1900 או מעל
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeHigher="עד שנה" צריך להיות גבוה יותר מ"משנה"
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeLower="עד שנה" חייב להיות נמוך יותר מהשנה הנוכחית פלוס 100
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningFromYear=הגדלת הערך של "משנה" עלול להוביל לאובדן נתונים
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningToYear=הפחתת הערך של "עד שנה" עלול להוביל לאובדן נתונים
#XMSG: Time Data creation validation error message
timeDataValidationError=נראה שחלק מהשדות לא חוקיים. בדוק את השדות הדרושים כדי להמשיך.
#XMSG: Time Data deletion popup text
deleteTimeDataConfirmation=האם אתה בטוח שברצונך למחוק את הנתונים?
#XMSG: Time Data creation success message
createTimeDataSuccess=נוצרו נתוני שעות עבודה
#XMSG: Time Data update success message
updateTimeDataSuccess=עודכנו נתוני שעות עבודה
#XMSG: Time Data delete success message
deleteTimeDataSuccess=נמחקו נתוני שעות עבודה
#XMSG: Time Data creation error message
createTimeDataError=משהו השתבש במהלך ניסיון ליצור נתוני שעות עבודה.
#XMSG: Time Data update error message
updateTimeDataError=משהו השתבש במהלך ניסיון לעדכן נתוני שעות עבודה.
#XMSG: Time Data creation error message
deleteTimeDataError=משהו השתבש במהלך ניסיון למחוק נתוני שעות עבודה.
#XMSG: Error message when time data could not be retrieved
retrievingTimeDataError=לא ניתן היה לטעון את נתוני שעות עבודה.
#XTIT: Time Data deletion popup title
deleteTimeDataConfirmationTitle=אזהרה
#XTXT: Time Data deletion dependency popup text
deleteTimeDataDependencyText=לא ניתן היה למחוק את נתוני שעות העבודה שלך כי הם בשימוש במודלים אחרים.
#XTXT: Time Data deletion dependency popup text singular
deleteTimeDataDependencyTextSingular=לא ניתן היה למחוק את נתוני שעות העבודה שלך כי הם בשימוש במודל אחר.
#XMSG: Validation error when required time data form field is empty
timeDataRequiredField=שדות אלה, הם שדות חובה. לא ניתן להשאירם ריקים.
#XFLD: Tooltip for Open in Database Explorer button
openInDBX=פתח בסייר בסיס הנתונים
#YMSE: Dimension Year
dimensionYearView=ממד "שנה"
#YMSE: Dimension Year
dimensionQuarterView=ממד "רבעון"
#YMSE: Dimension Year
dimensionMonthView=ממד "חודש"
#YMSE: Dimension Year
dimensionDayView=ממד "יום"
#XFLD: Time Data deletion object title
timeDataUsedIn=(בשימוש ב-{0} מודלים)
#XFLD: Time Data deletion object title singular
timeDataUsedInSingular=(בשימוש במודל אחד)
#XFLD: Time Data deletion table column provider
provider=ספק
#XTIT: Time Data dependency dialog title
timeDataDependencyDialogTitle=יחסי תלות
#XTIT: Create schema popup title
createSpaceSchemaUserDialogTitle=צור משתמש עבור תרשים מרחב
#XFLD: Create schema button
createSchemaButton=צור תרשים Open SQL
#XFLD: Generate TimeData button
generateTimeDataButton=צור לוחות זמנים וממדים
#XFLD: Show dependencies button
showDependenciesButton=הצג יחסי תלות
#XFLD: tooltip for disabled buttons when user is not member of space
mustBeMemberOfSpaceForOperation=כדי לבצע פעולה זו, המשתמש שלך חייב להיות חבר במרחב.
#XFLD: Create space schema user button
createSpaceSchemaUserButton=צור משתמש של תרשים מרחב
#YMSE: API Schema users load error
loadSchemaUsersError=לא ניתן היה לטעון את רשימת המשתמשים.
#XTIT: API Schema-config popup title
schemaUserDetailsDialogTitle=פרטי משתמש של תרשים מרחב
#XMSG: API Schema user deletion popup text
deleteSpaceSchemaUserConfirmation=האם אתה בטוח שברצונך למחוק את המשתמש שנבחר?
#XMSG: API Schema user deletion confirmation text
userDeleteSuccess=משתמש נמחק.
#YMSE: API Schema user deletion error
userDeleteError=לא ניתן היה למחוק את המשתמש.
#XFLD: User deleted
userDeleted=המשתמש נמחק.
#XTIT: Remove user popup title
removeUserConfirmationTitle=אזהרה
#XMSG: Remove user popup text
removeUserConfirmation=בטוח שברצונך להסיר את המשתמש? המשתמש והתפקידים בטווח המוקצים לו יוסרו מהמרחב.
#XMSG: Remove users popup text
removeUsersConfirmation=בטוח שברצונך להסיר את המשתמשים? המשתמשים והתפקידים בטווח המוקצים להם יוסרו מהמרחב.
#XBUT: Confirmation button for remove user dialog
removeUserConfirmationBtn=הסר
#YMSE: No data text for available roles
noDataAvailableRoles=המרחב לא נוסף לאף תפקיד בטווח. \n בשביל האפשרות להוסיף משתמשים למרחב, יש להוסיף תחילה לתפקיד בטווח אחד או יותר.
#YMSE: No data text for selected roles
noDataSelectedRoles=אין תפקידים בטווח שנבחרו
#XTIT: Schema-config popup title
schemaDetailsDialogTitle=פתח את פרטי תצורת תרשים SQL
#XFLD: Label for Read Audit Log
auditLogRead=הפעל יומן ביקורת עבור פעולות קריאה
#XFLD: Label for Change Audit Log
auditLogChange=הפעל יומן ביקורת עבור פעולות שינוי
#XFLD: Label Audit Log Retention
auditLogRetention=שמור יומנים עבור
#XFLD: Label Audit Log Retention Unit
retentionUnit=ימים
#YMSE: Error when log retention time is set incorrectly
incorrectRetentionTime=הזן מספר שלם בין {0} ל-{1}
#XFLD: Label for Cross Space Schema Access
spaceSchemaAccess=צרוך נתונים של תרשים מרחב
#XTIT: Confirm deactivation of space schema consumption dialog title
disableSpaceSchemaConsumptionDialogTitle=הפסק לצרוך נתונים של תרשים מרחב
#XTXT: Confirm deactivation of space schema consumption dialog text
disableSpaceSchemaConsumptionDialogText=תרשים Open SQL זה עלול לצרוך נתונים מתרשים המרחב שלך. אם תפסיק לצרוך, מודלים שמבוססים על נתוני תרשים המרחב שלך עלולים להפסיק לעבוד.
#XBUT: Confirmation button for the stop consuming space schema data dialog
stopConsuming=הפסק לצרוך
#XTXT: Tooltip for data lake icon
dataLakeIconTooltip=מרחב זה משמש לגישה אל אגם הנתונים
#XFLD: Tooltip for data lake table entry
dataLakeTableEntryTooltip=אגם נתונים מופעל
#XFLD: used In-Memory linit reached tag
usedRamLimitReachedLabel=הגבלת הזיכרון הושגה
#XFLD: Space storage Limit reached tag
storageLimitReachedLabel=הגבלת האחסון הושגה
#XFLD: Minimal Storage Limit Reached tag
minStorageLimitReachedLabel=הגבלת האחסון המינימלית הושגה
#XFLD: Space ram tag
ramLimitReachedLabel=הגבלת הזיכרון הושגה
#XFLD: Minimal In-Memory Limit Reached tag
minRamLimitReachedLabel=הגבלת הזיכרון המינימלית הושגה
#XFLD: Popover text after click on space storage limit warning
storageLimitReachedText=הגבלת שטח האחסון של {0} במרחב המוקצה לך הושגה. יש להקצות שטח אחסון נוסף למרחב.
#XFLD: System storage tag
systemStorageLimitReachedLabel=הגבלת שטח אחסון המערכת הושגה
#XMSG: Popover text after click on storage limit warning
systemStorageLimitReachedText=הגבלת שטח אחסון המערכת של {0} הושגה. לא ניתן להקצות שטח אחסון נוסף למרחב כרגע.
#XMSG: Schema deletion warning
deleteSchemaConfirmation=בעקבות מחיקת תרשים Open SQL זה יימחקו גם האובייקטים המאוחסנים והשיוכים המתוחזקים בתרשים. האם להמשיך?
#XMSG: Success message after deleting a schema
schemaDeleteSuccess=התרשים נמחק
#YMSE: Error while deleting schema.
schemaDeleteError=לא ניתן היה למחוק את התרשים.
#XMSG: Success message after update a schema
schemaUpdateSuccess=התרשים עודכן
#YMSE: Error while updating schema.
schemaUpdateError=לא ניתן היה לעדכן את התרשים.
#XMSG: Hint when no password is shown in the dialog.
newPasswordHint=סיפקנו לך סיסמה עבור תרשים זה. אם שכחת או איבדת אותה, ניתן לבקש סיסמה חדשה. זכור להעתיק או לשמור את הסיסמה החדשה.
#XMSG: Hint when showing the password in the dialog.
savePasswordHint=העתק את הסיסמה שלך. אתה תצטרך אותה להתקנת החיבור לתרשים זה. אם שכחת את הסיסמה, ניתן לפתוח את הדיאלוג הזה כדי לאפס אותה.
#XFLD,50: Hint on entering schema name in creation dialog
schemaNameHint=לא ניתן לשנות את השם הזה לאחר שהתרשים נוצר.
#XFLD: Open SQL Schemas section sub headline
schemasSQL=Open SQL
#XFLD: Space schema section sub headline
schemasSpace=מרחב
#XFLD: HDI Container section header
HDIContainers=מכלי HDI
#XTXT: Add HDI Containers button tooltip
addHDIContainers=הוסף מכלי HDI
#XTXT: Remove HDI Containers button tooltip
removeHDIContainers=הסר מכלי HDI
#XFLD: Enable HDI Containers feature button
enableHDIContainersButton=אפשר גישה
#YMSE: No data text for HDI Containers table
noDataHDIContainers=לא נוספו מכלי HDI
#YMSE: No data text for Timedata section
noDataTimedata=לא נוצר לוחות זמנים וממדים.
#YMSE: No data text during runtime not available for Timedata section
runtimeNotAvailableNoDataTimedata=לא ניתן לטעון טבלאות זמן וממדים מכיוון שבסיס הנתונים של זמן הריצה אינו זמין.
#YMSE: No data text during runtime not available for HDI section
runtimeNotAvailableNoDataHDI=לא ניתן לטעון מכלי HDI מכיוון שבסיס הנתונים של זמן הריצה אינו זמין.
#YMSE: Error while fetching HDI Containers
fetchHDIContainersError=לא ניתן היה להשיג את מכלי ה-HDI. נסה שוב מאוחר יותר.
#XFLD Table column header for HDI Container names
HDIContainerName=שם מכל HDI
#XFLD: Enable HDI Containers feature dialog title
enableHDIContainersDialogTitle=אפשר גישה
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText1New=ניתן לאפשר את SAP SQL Data Warehousing בדייר של SAP Datasphere כדי להחליף נתונים בין מכולות HDI שלך לבין מרחבי SAP Datasphere שלך ללא הצורך בתנועת נתונים.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText2=עשה זאת באמצעות פתיחת קריאה לתמיכה על-ידי לחיצה על הלחצן שמופיע למטה.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText3New=לאחר עיבוד הכרטיס עליך לבנות אחת או יותר מכולות HDI בבסיס הנתונים של זמן ריצה של SAP Datasphere. אז הלחצן 'אפשר גישה' יוחלף בלחצן + במקטע של מכולות HDI עבור כל מרחבי SAP Datasphere שלך ותוכל להוסיף את המכולות שלך למרחב.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText41=צריך עוד מידע? עבור אל %%0. לקבלת מידע מפורט אודות מה יש לכלול בקריאה, ראה את %%1.
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkText=SAP Help
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkTextSapNote3057059=הערת SAP 3057059
#XBUT: Open Ticket Button Text
openTicket=פתח קריאת שירות
#XBUT: Add Button Text
add=הוסף
#XBUT: Next Button Text
next=הבא
#XBUT: Edit Button Text
editUsers=ערוך
#XBUT: create user Button Text
createUser=צור
#XBUT: Update user Button Text
updateUser=בחר
#XTIT: Add HDI Containers Dialog Title
addHDIContainersDialogTitle=הוסף מכלי HDI שלא הוקצו
#XFLD: No data text for add HDI container dialog
addHDIContainersDialogNoDataText=לא נמצאו מכלים שלא הוקצו. \n ייתכן שהמכל שאתה מחפש כבר הוקצה למרחב.
#YMSE: Error while loading assigned HDI Containers
loadAssignedHDIContainersError=לא ניתן היה לטעון את מכלי ה-HDI המוקצים.
#YMSE: Error while loading assigned HDI Containers
loadUnassignedHDIContainersError=לא ניתן היה לטעון את מכלי ה-HDI.
#XMSG: Success message
succeededToAddHDIContainer=נוסף מכל HDI
#XMSG: Success message
succeededToAddHDIContainerPlural=נוספו מכלי HDI
#XMSG: Success message
succeededToDeleteHDIContainer=הוסר מכל HDI
#XMSG: Success message
succeededToDeleteHDIContainerPlural=הוסרו מכלי HDI
#XFLD: Time data section sub headline
timeDataSection=לוחות זמנים וממדים
#XFLD: Schemas list entry read audit log enabled indicator
readIndicator=קרא
#XFLD: Schemas list entry change audit log enabled indicator
changeIndicator=שנה
#XFLD: Remote sources section sub headline
allconnections=הקצאת חיבור
#XFLD: Remote sources section sub headline
localconnections=חיבורים מקומיים
#XFLD: Remote sources section sub headline for open Connectors
openConnectors=מחברים פתוחים של SAP
#XFLD: User section sub headline
memberassignment=הקצאת חבר
#XFLD: User assignment section sub headline
userAssignment=הקצאת משתמש
#XFLD: User section Access dropdown Member
member=חבר
#XFLD: User assignment section column name
user=שם משתמש
#XTXT: Selected role count
selectedRoleToolbarText=נבחר: {0}
#XTIT: Space detail section connections title
detailsSectionConnections=חיבורים
#XTIT: Space detail section data access title
detailsSectionDataAccess=גישה לתרשים
#XTIT: Space detail section time data title
detailsSectionGenerateData=נתוני שעות עבודה
#XTIT: Space detail section members title
detailsSectionUsers=חברים
#XTIT: Space detail section Users title
detailsSectionUsersTitle=משתמשים
#XTIT: Out of Storage
insufficientStoragePopoverTitle=שטח האחסון מלא
#XTIT: Storage distribution
storageDistributionPopoverTitle=אחסון דיסק בשימוש
#XTXT: Out of Storage popover text
insufficientStorageText=כדי ליצור מרחב חדש, יש לצמצם את שטח האחסון המוקצה למרחב אחר או למחוק מרחב שאינך צריך עוד. באפשרותך להגדיל את שטח האחסון הכולל במערכת על-ידי קריאה לתוכנית ניהול.
#XMSG: Space id length warning
spaceIdLengthWarning=אירעה חריגה ממספר התווים המקסימלי {0}.
#XMSG: Space name length warning
spaceNameLengthWarning=אירעה חריגה ממספר התווים המקסימלי {0}.
#XMSG: Space id reserved prefix warning
spaceIdnWarningReservedPrefix=אין להשתמש בתחילית {0} כדי למנוע התנגשויות אפשריות.
#YMSE: Error while loading open SQL schemas
loadLocalSchemasError=לא ניתן היה לטעון תרשימים של Open SQL.
#YMSE: Error while creating open SQL schema
createLocalSchemaError=לא ניתן היה ליצור תרשים של Open SQL.
#YMSE: Error while loading all remote connections
loadRemoteConnectionsError=לא ניתן היה לטעון את כל החיבורים המרוחקים.
#YMSE: Error while loading space details
loadSpaceDetailsError=לא ניתן היה לטעון פרטי מרחב.
#YMSE: Error while deploying space details
deploySpaceDetailsError=לא ניתן היה לפרוס מרחב.
#YMSE: Error while copying space details
copySpaceDetailsError=לא ניתן היה להעתיק מרחב.
#YMSE: Error while loading storage data
loadStorageDataError=לא ניתן היה לטעון נתוני אחסון.
#YMSE: Error while loading all users
loadAllUsersError=לא ניתן היה לטעון את כל המשתמשים.
#YMSE: Failed to reset password
resetPasswordError=לא ניתן היה לאפס את הסיסמה.
#XMSG: Success message after updating space
resetPasswordSuccessMessage=סיסמה חדשה הוגדרה עבור תרשים
#YMSE: DP Agent-name too long
DBAgentNameError=שם סוכן ה-DP ארוך מדי.
#YMSE: Schema-name not valid.
schemaNameError=שם התרשים לא חוקי.
#YMSE: User name not valid.
UserNameError=שם המשתמש לא חוקי.
#XTIT: Consumption by Storage Type
consumptionStorageTypeText=צריכה לפי סוג אחסון
#XTIT: Consumption by Schema
consumptionSchemaText=צריכה לפי תרשים
#XTIT: Overall Consumption by schema
overallConsumptionSchemaText=צריכת טבלה כוללת לפי תרשים
#XTIT: Overall Consumption by Table Type
overallConsumptionTableTypeText=צריכה כוללת לפי סוג טבלה
#XTIT: Tables
tableDetailsText=פרטי טבלה
#XTIT: Table Storage Consumption
tableStorageConsumptionText=צריכת שטח אחסון של טבלה
#XFLD: Table Type label
tableTypeLabel=סוג טבלה
#XFLD: Schema label
schemaLabel=תרשים
#XFLD: reset table tooltip
resetTable=אפס טבלה
#XFLD: In-Memory label in space monitor
inMemoryLabel=זיכרון
#XFLD: Disk label in space monitor
diskLabel=דיסק
#XFLD: Yes
yesLabel=כן
#XFLD: No
noLabel=לא
#XFLD: switch label: Want the data in this space to be consumable by default?
allowConsumptionByDefault=האם ברצונך שהנתונים במרחב זה יהיו נתנים לצריכה כברירת מחדל?
#XFLD: Business Name
businessNameLabel=שם עסקי
#XFLD: Refresh
refresh=רענן
#XMSG: No filter results title
noFilterResultsTitle=נראה שהגדרות המסנן שלך לא מראות שום נתונים.
#XMSG: No filter results message
noFilterResultsMsg=נסה לזקק את הגדרות המסנן שלך ואם עדיין לא מופיעים נתונים, צור מספר טבלאות בבונה מודל הנתונים. ברגע שהן יצרכו שטח אחסון, תוכל לנטר אותן שם.
#XMSG: Space monitoring Hana state red title
spaceMonitorHanaRedTitle=בסיס הנתונים של זמן ריצה לא זמין.
#XMSG: Space monitoring Hana state red message
spaceMonitorHanaRedMsg=מכיוון שבסיס הנתונים לא זמין, מאפיינים מסוימים מושבתים ולא ניתן להציג מידע בעמוד.
#YMSE: Error while creating space schema user
createSpaceSchemaUserError=לא ניתן היה ליצור משתמש של תרשים מרחב.
#YMSE: Error User name already exists
userAlreadyExistsError=שם משתמש כבר קיים.
#YMSE: Error Authentication failed
authenticationFailedError=האימות נכשל.
#XMSG: Message strip message when a schema user is locked
userLockedMessageStrip=המשתמש נעול עקב מספר רב מדי של ניסיונות כניסה שנכשלו. יש לבקש סיסמה חדשה כדי לבטל את נעילת המשתמש.
#XMSG: Success message after reset and unlock schema user
resetUnlockPasswordSuccessMessage=הוגדרה סיסמה חדשה ובוטלה נעילת משתמש
#XMSG: user is locked message
userLockedMessage=המשתמש נעול.
#XCOL: Users table-view column Role
spaceRole=תפקיד
#XCOL: Users table-view column Scoped Role
spaceScopedRole=תפקיד בטווח
#XCOL: Users table-view column Space Admin
spaceAdmin=מנהל המרחב
#XFLD: User section dropdown value Viewer
viewer=מציג
#XFLD: User section dropdown value Modeler
modeler=מעצב
#XFLD: User section dropdown value Data Integrator
dataIntegrator=משלב נתונים
#XFLD: User section dropdown value Space Administrator
spaceAdministrator=מנהל המרחב
#XMSG: Success message -  Space Role updated successfully.
updateUserSpaceRoleSuccess=תפקיד מרחב עודכן
#XMSG: Error message - Failed to update Space Role.
updateUserSpaceRoleError=תפקיד המרחב לא עודכן בהצלחה.
#XFLD:
databaseUserNameSuffix=סיומת שם משתמש של בסיס נתונים
#XTXT: Space Schema password text
spaceSchemaPasswordText=כדי להגדיר חיבור לתרשים זה, העתק את הסיסמה שלך. אם שכחת אותה תמיד תוכל לבקש אחת חדשה.
#XFLD: Cloud Platform label in space schema dialog
cloudPlatformLabel=פלטפורמת ענן של SAP
#XTXT: Cloud Platform text in space schema dialog
cloudPlatformText=כדי להגדיר גישה דרך המשתמש הזה, הפעל את הצריכה והעתק את האישורים. במקרה שתעתיק רק את האישורים ללא הסיסמה, וודא שאתה מוסיף את הסיסמה מאוחר יותר.
#XFLD: enable consumption in CloudPlatform label in space schema dialog
enableConsumptionInCloudPlatformLabel=אפשר צריכה בפלטפורמת ענן של SAP
#XFLD: Credentials for User-Provided Service label in space schema dialog
cloudPlatformCredentialsLabel=אישורים עבור שירות שמסופק על-ידי המשתמש:
#XFLD: Credentials for User-Provided Service (Without Password) label in space schema dialog
cloudPlatformCredentialsWithoutPasswordLabel=אישורים עבור שירות שמסופק על-ידי המשתמש (ללא סיסמה):
#XFLD: Copy Credentials label in space schema dialog
copyCredentialsLabel=העתק אישורים ללא סיסמה
#XFLD: Copy Credentials label in space schema dialog
copyFullCredentialsLabel=העתק את כל האישורים במלואם
#XFLD: Copy Password label in space schema dialog
copyPasswordLabel=העתק סיסמה
#XMSG: Credentials copied to clipboard
credentialsCopiedMessage=האישורים הועתקו ללוח העריכה
#XMSG: Password copied to clipboard
passwordCopiedMessage=הסיסמה הועתקה ללוח העריכה
#XMSG: create database user dialog title
createDatabaseUserDialogTitle=צור משתמש בסיס נתונים
#XMSG: Database Users section title
databaseUsers=משתמשי בסיס נתונים
#XMSG: Database User Details dialog title
databaseUserDetailsDialogTitle=פרטי משתמש בסיס נתונים
#XFLD: database user read audit log
databaseUserAuditLogRead=אפשר יומני ביקורת עבור פעולות קריאה ושמור יומנים עבור
#XFLD: database user change audit log
databaseUserAuditLogChange=אפשר יומני ביקורת עבור פעולות שינוי ושמור יומנים עבור
#XMSG: Cloud Platform Access
cloudPlatformAccess=גישה לפלטפורמת ענן של SAP
#XFLD: Enable Consumption in Cloud Platform hint
enableConsumptionCloudPlatformHint=הגדר גישה למכל HDI בתשתית הפריסה של HANA דרך משתמש בסיס נתונים זה. כדי להתחבר למכל ה-HDI שלך,יש להפעיל את עיצוב ה-SQL
#XFLD: Enable HDI Consumption
enableConsumptionCloudPlatform=הפעל צריכת HDI
#XFLD: Enable Consumption hint
enableConsumptionHint=האם תרצה שהנתונים במרחב שלך יהיו ניתנים לצריכה על-ידי כלים או יישומים אחרים?
#XFLD: Enable Consumption
enableConsumption=הפעל צריכת SQL
#XFLD: Enable Modeling
enableModeling=הפעל עיצוב ב-SQL
#XMSG: Privileges for Data Modeling
privilegesModeling=בליעת נתונים
#XMSG: Privileges for Data Consumption
privilegesConsumption=צריכת נתונים עבור כלים חיצוניים
#XFLD: SQL Modeling
sqlModeling=עיצוב ב-SQL
#XFLD: SQL Consumption
sqlConsumption=צריכת SQL
#XFLD: enabled
enabled=פועל
#XFLD: disabled
disabled=מושבת
#XFLD: Edit Privileges
editPrivileges=ערוך הרשאות
#XFLD: Open Database Explorer
openDBX=פתח את סייר בסיס הנתונים
#XFLD: create database user hint
databaseCreateHint=שים לב, לא תהיה אפשרות לשנות את שם המשתמש שוב לאחר שמירה.
#XFLD: Internal Schema Name
internalSchemaName=שם של תרשים פנימי
#YMSE: Failed to load database users
loadDatabaseUserError=נכשלה טעינת משתמשי בסיס הנתונים
#YMSE: Failed to delete database user
deleteDatabaseUsersError=נכשלה מחיקת משתמשי בסיס הנתונים
#XMSG: Success message after deleting database user
deleteDatabaseUserSuccess=משתמש בסיס הנתונים נמחק
#XMSG: Success message after deleting database users
deleteDatabaseUsersSuccess=משתמשי בסיס הנתונים נמחקו
#XMSG: Success message after creating database user
createDatabaseUserSuccessMessage=משתמש בסיס הנתונים נוצר
#YMSE: Failed to create database user
createDatabaseUserError=יצירת משתמשי בסיס הנתונים נכשלה
#XMSG: Success message after updating database user
updateDatabaseUserSuccessMessage=משתמש בסיס הנתונים עודכן
#YMSE: Failed to update database user
updateDatabaseUserError=נכשל עדכון משתמש בסיס הנתונים
#XFLD: HDI Consumption
hdiConsumption=צריכת HDI
#XTIT: Space detail section Database Access title
detailsSectionDatabaseAccess=גישה לבסיס נתונים
#XTXT: Database users consumption hint
databaseUsersConsumptionHint=הפוך את נתוני המרחב שלך לניתנים לצריכה כברירת מחדל. המודלים בבונים יאפשרו לנתונים להיות נתונים לצריכה באופן אוטומטי.
#XFLD: Default Consumption of Space Data:
defaultConsumptionOfSpaceData=צריכה ברירת מחדל של נתוני מרחב:
#XFLD: Database User Name
databaseUserName=שם משתמש של בסיס נתונים
#XMSG: Database User creation validation error message
databaseUserValidationError=נראה שחלק מהשדות לא חוקיים. בדוק את השדות הדרושים כדי להמשיך.
#XMSG: SQL modeling can’t be enabled since this user has been migrated.
migratedDatabaseUserHint=לא ניתן להפעיל בליעת נתונים מאחר והמשתמש הזה הועבר.
#XBUT: Remove Button Text
remove=הסר
#XBUT: Remove Spaces Button Text
removeSpaces=הסר מרחבים
#XBUT: Remove Objects Button Text
removeObjects=הסר אובייקטים
#XMSG: No members have been added yet.
noMembersAssigned=לא נוספו חברים עדיין.
#XMSG: No users have been added yet.
noUsersAssigned=לא נוספו משתמשים עדיין.
#XMSG: No database users have been created or your filter is not showing any data.
noDatabaseUsers=לא נוצרו משתמשי בסיס נתונים או שהמסנן לא מציג שום נתונים.
#XMSG: Please enter a user name.
noDatabaseUsername=הזן שם משתמש.
#XMSG: The user name is too long. Please use a shorter one.
databaseUsernameTooLong=שם המשתמש ארוך מדי. השתמש בשם קצר יותר.
#XMSG: No privileges have been enabled and this database user will have limited functionality. Do you still want to continue?
noPrivilegesConfirmationMessage=לא הופעלו הרשאות ולמשתמש בסיס נתונים זה תהיה פונקציונאליות מוגבלת. האם עדיין ברצונך להמשיך?
#XMSG: To enable audit logs for change operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogChangeMessage=כדי להפעיל את יומני הביקורת עבור פעולות שינוי, בליעת נתונים צריכה להיות מופעלת גם כן. האם תרצה לעשות זאת?
#XMSG: To enable audit logs for read operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogReadMessage=כדי להפעיל את יומני הביקורת עבור פעולות קריאה, בליעת נתונים צריכה להיות מופעלת גם כן. האם תרצה לעשות זאת?
#XMSG: To enable HDI consumption data ingestion and data consumption needs to be enabled. Do you want to enable all?
enableHDIConsumptionMessage=כדי להפעיל צריכת HDI, יש להפעיל גם את בליעת הנתונים וצריכת הנתונים. האם תרצה לעשות זאת?
#XMSG:
databaseUserPasswordText=כדי להגדיר חיבור למשתמש בסיס נתונים זה, העתק את הסיסמה שלך. אם שכחת אותה תמיד תוכל לבקש אחת חדשה.
#XTIT: Space detail section members title
detailsSectionMembers=חברים
#XMSG: New password set
newPasswordSet=הוגדרה סיסמה חדשה
#XFLD: Data Ingestion
dataIngestion=בליעת נתונים
#XFLD: Data Consumption
dataConsumption=צריכת נתונים
#XFLD: Privileges
privileges=הרשאות
#XFLD: Enable Data ingestion
enableDataIngestion=הפעל בליעת נתונים
#XMSG: Log the read and change operations for data ingestion.
databaseUserAuditHint=רשום ביומן את פעולות השינוי והקריאה עבור בליעת נתונים.
#XMSG: Make your space data available in your HDI containers.
databaseUserHDIHint=הפוך את נתוני המרחב שלך לזמינים במכלי ה-HDI.
#XFLD: Enable Data consumption
enableDataConsumption=הפעלת צריכת נתונים
#XMSG: Allow other apps or tools to consume your space data.
enableDataConsumptionHint=התר ליישומים ולכלים אחרים לצרוך את נתוני המרחב שלך.
#XMSG: To set up access via this schema user copy the credentials to your user-provided service. In case you can only copy the credentials without a password make sure to add the password later.
databaseUserHDIConsumptionHint=כדי להגדיר גישה דרך משתמש בסיס נתונים זה, העתק את האישורים לשירות שסופק על-ידי המשתמש. במקרה שניתן להעתיק רק את האישורים ללא הסיסמה, וודא שאתה מוסיף את הסיסמה מאוחר יותר.
#XFLD: Data Flow Runtime Capacity label in space overview
dataFlowRuntimeCapacity=קיבולת זמן ריצה של תזרים נתונים ({0}:{1} שעות מתוך {2} שעות)
#XFLD: Could not load Data Flow Runtime Capacity
dataFlowRuntimeCapacityError=לא ניתן היה לטעון קיבולת זמן ריצה של תזרים נתונים
#XMSG: User can grant data consumption to other users.
databaseUserConsumptionWithGrantOptionHint=משתמש יכול להעניק צריכת נתונים למשתמשים אחרים.
#XFLD: Enable data consumption with grant option
enableConsumptionWithGrantOption=הפעל צריכת נתונים עם 'מתן הרשאה'
#XMSG: To enable data consumption with grant option data consumption needs to be enabled. Do you want to enable both?
enableConsumptionWithGrantMessage=כדי להפעיל צריכת נתונים עם מתן הרשאה, יש להפעיל את צריכת הנתונים. האם ברצונך להפעיל את שתי האפשרויות?
#XFLD: Enable Automated Predictive Library (APL) and Predictive Analysis Library (PAL)
allowUsingScriptServerFunctionality=הפעל ספריית חיזוי אוטומטית (APL) וספריית ניתוח חיזוי (PAL)
#XMSG: User can use SAP HANA Cloud embedded machine learning functions.
allowUsingScriptServerFunctionalityHint=משתמש יכול להשתמש בפונקציות למידת מכונה שמשובצות ב-SAP HANA Cloud.
#XFLD: Password Policy
passwordPolicy=מדיניות סיסמאות
#XMSG: Password Policy
passwordPolicyHint=הפעל או השבת את מדיניות הסיסמאות שתצורתה נקבעה כאן.
#XFLD: Enable Password Policy
enablePasswordPolicy=הפעל מדיניות סיסמאות
#XMSG: Read Access to the Space Schema
readAccessTitle=גישת קריאה אל תרשים המרחב
#XMSG: read access hint
readAccessHint=התר למשתמש בסיס הנתונים לחבר כלים חיצוניים לתרשים המרחב ולקרוא תצוגות שגלויות לצריכה.
#XFLD: Space Schema
spaceSchema=תרשים מרחב
#XFLD: Enable Read Access (SQL)
enableReadAccess=הפעל גישת קריאה (SQL)
#XMSG: Allow the user to grant read access to other users.
withGrantOptionHint=התר למשתמש להעניק גישת קריאה למשתמשים אחרים.
#XFLD: With Grant Option
withGrantOption=עם מתן הרשאה
#XMSG: Make your space data available in your HDI containers.
enableHDIConsumptionHint=הפוך את נתוני המרחב שלך לזמינים במכלי ה-HDI.
#XFLD: Enable HDI Consumption
enableHDIConsumption=הפעל צריכת HDI
#XMSG: Write Access to the User’s Open SQL Schema
writeAccessTitle=גישת כתיבה לתרשים Open SQL של המשתמש
#XMSG: write access hint
writeAccessHint=התר למשתמש בסיס הנתונים לחבר כלים חיצוניים לתרשים Open SQL של המשתמש כדי ליצור ישויות נתונים ולבלוע נתונים לשימוש במרחב.
#XFLD: Open SQL Schema
openSQLSchema=תרשים Open SQL
#XFLD: Enable Write Access (SQL DDL & DML)
enableWriteAccess=הפעל הרשאת כתיבה (SQL,‏ DDL ו-DML)
#XMSG: audit hint
auditHint=רשום ביומן את פעולות הקריאה והשינוי בתרשים Open SQL.
#XMSG: data consumption hint
dataConsumptionHint=חשוף את כל התצוגות החדשות במרחב כברירת מחדל עבור צריכה. מעצבים יכולים לעקוף את ההגדרה הזו עבור תצוגות יחידות באמצעות המתג "חשוף לצריכה" בלוח הצידי של פלט התצוגה. באפשרותך גם לבחור את הפורמטים שבהם התצוגות חשופות.
#XFLD: Expose for Consumption by Default
exposeDataConsumption=חשוף לצריכה כברירת מחדל
#XMSG: database users hint consumption hint
databaseUsersHint2New=צור משתמשי בסיס נתונים כדי לחבר כלים חיצוניים אל SAP Datasphere. הגדר הרשאות כדי להתיר למשתמשים לקרוא נתוני מרחב וליצור ישויות נתונים (DDL) וגם לבלוע נתונים (DML) לשימוש במרחב.
#XFLD: Read
read=קרא
#XFLD: Read (HDI)
readHDI=קרא (HDI)
#XFLD: Write
write=כתוב
#XMSG: HDI Containers Hint
HDIContainersHint2=התר גישה אל מכלי תשתית הפריסה (HDI) של ב-SAP HANA במרחב שלך. מעצבים יכולים להשתמש במכשירי HDI בתור מקורות עבור תצוגות, וסביבות HDI יכולות לגשת לנתוני המרחב שלך.
#XMSG: Open info dialog
openDatabaseUserInfoDialog=פתח את דיאלוג המידע
#XMSG: Database user is locked. Open info dialog to unlock
openLockedDatabaseUserInfoDialog=משתמש בסיס הנתונים נעול. פתח דיאלוג לביטול הנעילה
#XFLD: Table
table=טבלה
#XFLD: Space section partner configuration title
partnerConnectionConfigurationSectionTitle=חיבור שותף
#XFLD: Space section partner configuration subtitle
partnerConnectionConfigurationSectionSubTitle=תצורת חיבור שותף
#XFLD: Space section partner configuration hint
partnerConnectionConfigurationSectionHint=הגדר את אריח חיבור השותף שלך על ידי הוספת הסמל וכתובת ה-URL של ה-iFrame שלך. התצורה הזאת זמינה רק עבור הדייר הזה.
#XFLD: Table Name Field
partnerConnectionConfigurationName=שם אריח
#XFLD: Table iFrame URL field
partnerConnectionConfigurationiFrameURL=כתובת URL של iFrame
#XFLD: Table iFrame Post Message Origin field
partnerConnectionConfigurationiFramePostMessageOrigin=מקור הודעת רישום של iFrame
#XFLD: Table icon/image name
partnerConnectionConfigurationIcon=סמל
#XMSG: Table no result found
partnerConnectionConfigurationNotfound=לא ניתן היה למצוא תצורות חיבור שותף.
#XMSG: Partner Connection Confgurations are not available
partnerConnectionConfigurationNotAvailable=לא ניתן להציג תצורות חיבור שותף כאשר בסיס הנתונים של זמן הריצה אינו זמין.
#XFLD: Dialog Create Partner Connection Configuration
partnerConnectionConfigurationDialogTitleAdd=צור תצורת חיבור שותף
#XFLD: Dialog Create Partner Connection Configuration Upload Icon
partnerConnectionConfigurationButtonUloadPartnerIcon=עדכן סמל
#XFLD: Dialog Create Partner Connection Configuration Select
partnerConnectionConfigurationSelectIcon=בחר (גודל מקסימלי של 200KB)
#XFLD: Dialog Create Partner Connection Configuration Enter a name
partnerConnectionConfigurationNamePlaceholder=דוגמה של אריח שותף
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL
partnerConnectionConfigurationiFrameURLPlaceholder=http://www.example.com
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL Check
partnerConnectionConfigurationiFramePostMessageOriginPlaceholder=.example.com
#XFLD: Dialog Create Partner Connection Configuration Browse
partnerConnectionConfigurationBrowseButton=עיין
#XFLD: Create Partner Connection Configuration Msg
partnerConnectionConfigurationUploadMsg=תצורת חיבור שותף נוצרה בהצלחה.
#XMSG: Error while deleting Partner Connection configuration(s)
partnerConnectionConfigurationDeleteError=אירעה שגיאה בעת מחיקת תצורות חיבור שותף.
#XMSG: Partner Connection Configuration has been successfully deleted
partnerConnectionConfigurationDeleteSuccess=תצורת חיבור שותף נמחקה בהצלחה.
#XMSG: Error while retrieving Partner Connection Configurations
partnerConnectionConfigurationLoadError=אירעה שגיאה בעת אחזור תצורות חיבור שותף.
#XMSG: Error fileSizeLimitExceeded
partnerConnectionConfigurationfileSizeLimitExceeded=לא ניתן להעלות את הקובץ כי הוא חורג מהגודל המקסימלי של 200KB.
#XBUT: Create Partner Connection Configuration
partnerConnectionConfigurationButtonCreate=צור תצורת חיבור שותף
#XBUT: Delete Partner Connection Configuration
partnerConnectionConfigurationButtonDelete=מחק תצורת חיבור שותף.
#XMSG: Partner Tile Creation Failure
upsertPartnerTileFailed=לא ניתן היה ליצור אריח שותף.
#XMSG: Partner Tile Deletion Failure
deletePartnerTileFailed=לא ניתן היה למחוק אריח שותף.
#XMSG: HANA cloud connector settings reset failure
resetHANASCCSettingsFailed=אתחול הגדרות מחבר SAP HANA Cloud של לקוח נכשל
#XFLD: Workload Class
workloadClass=סיווג עומס עבודה
#XFLD: Workload Management
workloadManagement=ניהול עומס עבודה
#XFLD: Priority
workloadClassPriority=עדיפות
#XMSG:
workloadManagementPriorityHint=ניתן לציין את התעדוף של מרחב זה בעת הרצת שאילתה על בסיס הנתונים. הזן ערך בין 1 (עדיפות נמוכה ביותר) ל-8 (עדיפות גבוהה ביותר). במצב שבו מרחבים מתחרים על שרשורים זמינים, מרחבים עם עדיפויות גבוהות יותר ירוצו לפני מרחבים עם עדיפויות נמוכות יותר.
#XMSG:
workloadClassPriorityHint=באפשרותך לציין את העדיפות של המרחב מ-0 (הנמוך ביותר) עד 8 (הגבוה ביותר). הדוחות של המרחב עם עדיפות גבוהה מבוצעים לפני דוחות של מרחבים אחרים עם עדיפות נמוכה יותר. ברירת מחדל של עדיפות היא 5. הערך 9 משוריין עבור פעולות מערכת, לכן אינו זמין עבור מרחב.
#XFLD: Statement Limits
workloadclassStatementLimits=גבולות דוח
#XFLD: Workload Configuration
workloadConfiguration=תצורת עומס עבודה
#XMSG:
workloadClassStatementLimitsHint=ניתן לציין את המספר (או האחוז) המקסימלי של שרשורים וערכי GB של זיכרון שהדוחות הפועלים במקביל במרחב יכולים לצרוך. ניתן להזין כל ערך או אחוז בין 0 (ללא הגבלה) לבין סך הזיכרון והשרשורים הזמינים בדייר. \n\n אם תציין מגבלת שרשור, שים לב שהיא יכולה להוריד את הביצועים. \n\n אם תציין מגבלת זיכרון, הדוחות המגיעים למגבלת הזיכרון לא מופעלים.
#XMSG:
workloadClassStatementLimitsDescription=תצורת ברירת המחדל מספקת גבולות משאבים נדיבים במהלך מניעת עומס יתר של כל מרחב על המערכת.
#XMSG:
workloadClassStatementLimitCustomDescription=ניתן להגדיר גבולות מקסימליים לשרשור ולזיכרון שאותם דוחות שרצים בו-זמנית במרחב יוכלו לצרוך.
#XMSG:
totalStatementThreadLimitHelpText=הגדרת גבול שרשור נמוך מדי עשוי להשפיע על הביצועים של דוחות וערכים גבוהים מדי או 0 עשויים לאפשר למרחב לצרוך את כל שרשורי המערכת הזמינים.
#XMSG:
totalStatementMemoryLimitHelpText=הגדרת גבול זיכרון נמוך מדי עשוי עשויה לגרום לבעיות של חוסר זיכרון פנוי, וערכים גבוהים מדי או 0 עשויים לאפשר למרחב לצרוך את כל שרשורי המערכת הזמינים.
#XMSG:
totalStatementThreadLimitHelpTextRestricted=הזן אחוז בין 1% ל-70% (או מספר שווה ערך) של המספר הכולל של שרשורים הזמינים בדייר שלך. הגדרת מגבלת שרשורים נמוכה מדי עשויה להשפיע על ביצוע הדוח, בעוד שערכים גבוהים מדי עשויה להשפיע על הביצוע של דוחות במרחבים אחרים.
#XMSG:
totalStatementThreadLimitHelpTextDynamic=הזן אחוז בין 1% ל-%{0} (או מספר שווה ערך) של המספר הכולל של שרשורים הזמינים בדייר שלך. הגדרת מגבלת שרשורים נמוכה מדי עשויה להשפיע על ביצוע הדוח, בעוד שערכים גבוהים מדי עשויה להשפיע על הביצוע של דוחות במרחבים אחרים.
#XMSG:
totalStatementMemoryLimitHelpTextNew=הזן ערך או אחוז בין 0 (ללא הגבלה) והסכום הכולל של זיכרון זמין בדייר שלך. הגדרת מגבלת זיכרון נמוכה מדי עשויה להשפיע על ביצוע הדוח, בעוד שערכים גבוהים מדי עשויה להשפיע על הביצוע של דוחות במרחבים אחרים.
#XFLD: Total Statement Thread Limit
totalStatementThreadLimit=גבול שרשור דוח כולל
#XFLD: Segmented button label
workloadclassPercentage=%
#XFLD: Segmented button label
workloadclassGigaByte=GB
#XFLD, 80: Segmented button label
workloadclassThreads=שרשורים
#XFLD: Total Statement Memory Limit
totalStatementMemoryLimit=גבול זיכרון דוח כולל
#YMSE: Error while loading customer hana info
loadCustomerHanaInfoError=טעינת מידע SAP HANA של לקוח נכשלה.
#XMSG:
minimumLimitReached=הגעת לגבול המינימלי.
#XMSG:
maximumLimitReached=הגעת לגבול המקסימלי.
#XMSG: Name Taken for Technical Name
technical-name-taken=כבר קיים חיבור עם השם הטכני שהזנת. הזן שם אחר.
#XMSG: Name Too long for Technical Name
technical-name-too-long=השם הטכני שהזנת עולה על 40 תווים. הזן שם עם פחות תווים.
#XMSG: Technical name field empty
technical-name-field-empty=הזן שם טכני.
#XMSG: Generic error when user enters invalid characters for a field
field-invalid-characters=ניתן להשתמש בשם באותיות (a-z), מספרים (0-9) וקווים תחתונים (_).
#XMSG: Generic error when user enters invalid initial characters for a field
field-underscore-trim=השם שתזין לא יכול להתחיל או להסתיים בקו תחתון (_).
#XFLD: Checkbox label for Enable Statement Limits
enableStatementLimits=אפשר הגבלות דוח
#XFLD: Connection SubSection Settings Title in space details
connectionSettings=הגדרות
#XMSG: Connections tool hint in Space details section
connectionsToolHint=כדי ליצור חיבורים או לערוך אותם, פתח את יישום החיבורים מניווט הצד או לחץ כאן:
#XBUT: Connection list Open Connection Tool Button
button-connectiontool=עבור אל חיבורים
#XFLD: Not deployed label on space tile
notDeployedLabel=מרחב לא נפרס עדיין.
#XFLD: Not deployed additional text on space tile
notDeployedText=פרוס את המרחב.
#XFLD: Corrupt space label on space tile
corruptSpace=משהו השתבש.
#XFLD: Corrupt space additional text on space tile
corruptSpaceHint=נסה לפרוס מחדש או צור קשר עם תמיכה
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=נתוני יומן ביקורת
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=נתונים מנהליים
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=נתונים אחרים
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=נתונים במרחבים
#XMSG: Unlock Space confirmation text
unlockSpaceConfirmation=האם אתה בטוח שברצונך לבטל את הנעילה של המרחב?
#XMSG: Lock Space confirmation text
lockSpaceConfirmation=האם אתה בטוח שברצונך לנעול את המרחב?
#XFLD: Lock
lock=נעל
#XFLD: Unlock
unlock=בטל נעילה
#XFLD: Locking
locking=נעילה
#XMSG: Success message after locking space
lockSpaceSuccessMsg=מרחב נעול
#XMSG: Success message after unlocking space
unlockSpaceSuccessMsg=בוטלה נעילת מרחב
#XMSG: Success message after locking spaces
lockSpacesSuccessMsg=מרחבים נעולים
#XMSG: Success message after unlocking spaces
unlockSpacesSuccessMsg=בוטלה נעילת מרחבים
#YMSE: Error while locking a space
lockSpaceError=המרחב אינו ניתן לנעילה.
#YMSE: Error while unlocking a space
unlockSpaceError=לא ניתן לנעול את המרחב.
#XTIT: popup title Warning
confirmationWarningTitle=אזהרה
#XMSG: Message strip message when a space was locked manually
manualLockedMessage=המרחב ננעל ידנית.
#XMSG: Message strip message when a space was locked due to the amount of generated audit logs
auditLogLockedMessage=המרחב ננעל על-ידי המערכת מכיוון שיומני ביקורת צורכים כמות גדולה של GB של דיסק.
#XMSG: Message strip message when a space was locked because the assigned quota was exceeded.
quotaLockedMessage=המרחב ננעל על-ידי המערכת מכיוון שהוא חורג מההקצאות של אחסון בזיכרון או בדיסק.
#XMSG: Unlock Spaces confirmation text
unlockSpacesConfirmation=האם אתה בטוח שברצונך לבטל את הנעילה של המרחבים שנבחרו?
#XMSG: Lock Spaces confirmation text
lockSpacesConfirmation=האם אתה בטוח שברצונך לנעול את המרחבים שנבחרו?
#XTIT: Scoped role editor dialog title
scopedRoleEditorDialogTitle=עורך תפקיד בטווח
#XTIT: ECN Management title
ecnManagementTitle=מרחב וניהול צמתי חישוב גמיש
#XFLD: ECNs
ecns=צמתי חישוב גמיש
#XFLD: ECN phase Ready
ecnReady=מוכן
#XFLD: ECN phase Running
ecnRunning=פועל
#XFLD: ECN phase Initial
ecnInitial=לא מוכן
#XFLD: ECN phase Starting
ecnStarting=מתחיל
#XFLD: ECN phase Starting Failed
ecnStartingFailed=ההתחלה נכשלה
#XFLD: ECN phase Stopping
ecnStopping=עוצר
#XFLD: ECN phase Stopping Failed
ecnStoppingFailed=העצירה נכשלה
#XBTN: Assign Button
assign=הקצה מרחבים
#XBTN: Start Header-Button
start=התחל
#XBTN: Update Header-Button
repair=עדכן
#XBTN: Stop Header-Button
stop=עצור
#XFLD: ECN hours remaining
ecnHoursRemaining=נותרו 1000 שעות
#XFLD: ECN block-hours remaining
ecnBlockHoursRemaining={0} שעות חסימה נשארו
#XFLD: ECN block-hour remaining
ecnBlockHourRemaining=שעת חסימה {0} נשארה
#XTIT: ECN creation dialog title
ecnCreationDialogTitle=צור צומת חישוב גמיש
#XTIT: ECN edit dialog title
ecnEditDialogTitle=ערוך צומת חישוב גמיש
#XTIT: ECN deletion dialog title
ecnDeletionDialogTitle=מחק צומת חישוב גמיש
#XTIT: ECN Space assignment dialog title
ecnSpaceAssignmentDialogTitle=הקצה מרחבים
#XFLD: ECN ID
ECNIDLabel=צומת חישוב גמיש
#XTXT: Selected toolbar text
selectedToolbarText=נבחר: {0}
#XTIT: Elastic Compute Nodes
ECNslong=צמתי חישוב גמיש
#XTIT: Object assignment - Table header: Objects available
objectsAvailable=מספר אובייקטים
#XTIT: Object assignment - Dialog header text
selectObjects=בחר את המרחבים והאובייקטים שתרצה להקצות לצומת החישוב הגמיש שלך:
#XTIT: Object assignment - Table header title: Objects
objects=אובייקטים
#XTIT: Object assignment - Table header: Type
type=סוג
#XMSG: Message strip delete database user audit warning
deleteDatabaseUserWarningMessageStrip=דע שמחיקת משתמש בסיס נתונים תגרום למחיקה של כל הזנות יומן הביקורת שנוצרו. אם תרצה לשמור על יומני הביקורת חשוב על לייצא אותן לפני שתמחוק את משתמש בסיס הנתונים.
#XMSG: Message strip delete HDI container audit logs impact
deleteHDIContainerWarningMessageStrip=דע שביטול הקצאת מכולת HDI מהמרחב תגרום למחיקה של כל הזנות יומן הביקורת שנוצרו. אם תרצה לשמור על יומני הביקורת חשוב על לייצא אותן לפני שתבטל את ההקצאה של מכולת ה-HDI.
#XTXT: All audit logs
allAuditLogs=כל הזנות יומן הביקורת שנוצרו עבור המרחב
#XMSG: Message strip disable audit warning
disableAuditWarningMessageStrip=דע שהשבתת מדיניות ביקורת (קרא או שנה פעולות) תגרום למחיקה של כל הזנות יומן הביקורת שלה. אם תרצה לשמור על הזנות יומן הביקורת חשוב על לייצא אותן לפני שתשבית את מדיניות הביקורת.
#XTIT: No Spaces assigned to ECN Illustration title
noSpacesECNTitle=עדיין לא הוקצו מרחבים או אובייקטים
#XTIT: No Spaces assigned to ECN Illustration description
noSpacesECNDescription=כדי להתחיל לעבוד עם צומת המחשוב האלסטי שלך, הקצה לו מרחב או אובייקטים.
#XTIT: No Spaces Illustration title
noSpacesTitle=עדיין לא נוצר מרחב
#XTIT: No Spaces Illustration description
noSpacesDescription=כדי להתחיל ברכישת הנתונים, צור מרחב.
#XTIT: No Spaces Illustration title
noDeletedSpacesTitle=סל המחזור ריק
#XTIT: No Spaces Illustration description
noDeletedSpacesDescription=ניתן לשחזר את המרחבים שנמחקו מכאן.
#XTXT:  {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDatabaseUsers=לאחר פריסת המרחב, משתמשי בסיס הנתונים הבאים יימחקו {0} ולא ניתן יהיה לשחזר אותם:
#XTIT: Delete database users
deleteDatabaseUsersTitle=מחק משתמשי מסד נתונים
#YMSE: Error if ecn id already exists
ecnIDInputDuplicateError=זיהוי כבר קיים.
#YMSE: Error if ecn id contains invalid characters
ecnIDInputInvalidError=השתמש באותיות קטנות -a - z ו-0 - 9 בלבד.
#YMSE: Error if ecn id is too short
ecnIDInputTooShortError=הזיהוי חייב להיות באורך של {0} תווים לפחות.
#XMSG: ecn id length warning
ecnIdLengthWarning=אירעה חריגה ממספר התווים המקסימלי {0}.
#XFLD: open System Monitor
systemMonitor=מעקב מערכת
#XFLD: open ECN schedule dialog menu entry
schedule=תזמן
#XFLD: open create ECN schedule dialog
createSchedule=צור לוח זמנים
#XFLD: open change ECN schedule dialog
changeSchedule=ערוך לוח זמנים
#XFLD: open delete ECN schedule dialog
deleteSchedule=מחק תזמון
#XFLD: open self assign ECN schedule dialog
selfAssignSchedule=הקצה לי תזמון
#XFLD: open pause ECN schedule dialog
pauseSchedule=השהה תזמון
#XFLD: open resume ECN schedule dialog
resumeSchedule=חדש תזמון
#XFLD: View Logs
viewLogs=הצג יומנים
#XFLD: Compute Blocks
computeBlocks=חסימת מחשוב
#XFLD: Memory label in ECN creation dialog
ecnMemory=זיכרון (GB)
#XFLD: Storage label in ECN creation dialog
ecnStorage=אחסון (GB)
#XFLD: Number of CPU label in ECN creation dialog
ecnCPUCount=מספר יחידות עיבוד מרכזיות
#XFLD: ECN updated by label
changedBy=שונה על-ידי
#XFLD: ECN updated on label
changedOn=שונה בתאריך
#XMSG: Success message after creating a Elastic Compute Node
createEcnSuccessMessage=צומת חישוב גמיש נוצר
#YMSE: Error while creating a Elastic Compute Node
createEcnError=לא ניתן היה ליצור צומת חישוב גמיש
#XMSG: Success message after updating a Elastic Compute Node
updateEcnSuccessMessage=עודכן צומת חישוב גמיש
#XMSG: Error while updating a Elastic Compute Node
udpateEcnError=לא ניתן היה לעדכן צומת חישוב גמיש
#XMSG: Success message after deleting a Elastic Compute Node
deleteEcnSuccessMessage=צומת חישוב אלסטי גמיש
#YMSE: Error while deleting a Elastic Compute Node
deleteEcnError=לא ניתן היה למחוק צומת חישוב גמיש
#XMSG: Success message after starting a Elastic Compute Node
startingEcnSuccessMessage=מפעיל צומת חישוב גמיש
#XMSG: Success message after stopping a Elastic Compute Node
stoppingEcnSuccessMessage=מפסיק צומת חישוב גמיש
#YMSE: Error while starting a Elastic Compute Node
startingEcnError=לא ניתן היה להפעיל צומת חישוב אלסטי
#YMSE: Error while stopping a Elastic Compute Node
stoppingEcnError=לא ניתן היה להפסיק צומת חישוב אלסטי
#XBUT: Add Object button for an ECN
assignObjects=הוסף אובייקטים
#XFLD: Enable auto object assignment label
enableAutoObjAssignment=הוסף את כל האובייקטים באופן אוטומטי
#XFLD: object type label to be assigned
objectTypeLabel=סוג (שימוש סמנטי)
#XFLD: assigned object type label
assignedObjectTypeLabel=סוג
#XFLD: technical name label
TechnicalNameLabel=שם טכני
#XFLD: Object selecttion ECN label
objectSelectionECNLabel=בחר את האובייקטים שאותם תרצה להוסיף לצומת החישוב הגמיש:
#XTIT: Add objects dialog title
assignObjectsTitle=הקצה אובייקטים של
#XFLD: object label with object count
objectLabel=אובייקט
#XMSG: No objects available to add message.
noObjectsToAssign=אין אובייקטים זמינים להקצאה.
#XMSG: No objects assigned message.
noAssignedObjects=לא הוקצו אובייקטים.
#XTIT: Remove objects popup title
removeObjectsOrSpacesConfirmationTitle=אזהרה
#XTIT: Delete ECN popup button and title
deleteECNConfirmationButtonAndTitle=מחק
#XMSG: Remove objects popup text
removeObjectsConfirmation=האם אתה בטוח שברצונך להסיר את האובייקטים שנבחרו?
#XMSG: Remove spaces popup text
removeSpacesConfirmation=האם אתה בטוח שברצונך להסיר את המרחבים שנבחרו?
#XBUT: Confirmation button for objects dialog
removeObjectsOrSpacesConfirmationBtn=הסר מרחבים
#XMSG: Success message after removing objects
removeObjectsSuccessMessage=אובייקטים שנחשפו הוסרו
#XMSG: Success message after assigning objects
assignObjectsSuccessMessage=אובייקטים שנחשפו הוקצו
#XFLD: auto object assignment label for space tile
autoObjAssignmentLabel=כל האובייקטים שנחשפו
#XFLD: Spaces tab label
spacesTabLabel=מרחבים
#XFLD: Exposed objects tab label
consumptionObjectsTabLabel=אובייקטים חשופים
#XMSG: Success message after unassigning spaces
spacesUnassignedSuccessMessage=מרחבים הוסרו
#XMSG: Success message after unassigning space
spaceUnassignedSuccessMessage=מרחב הוסר
#YMSE: Error while assigning / unassigning spaces
spacesAssignUnassignError=לא ניתן היה להקצות או להסיר את המרחבים.
#YMSE: Error while removing objects
removeObjectsError=לא ניתן היה להקצות או להסיר את האובייקטים.
#YMSE: Error while removing object
removeObjectError=לא ניתן היה להקצות או להסיר את האובייקט.
#YMSE: Error when saving the ecn with invalid value
invalidSizingPlanError=המספר שנבחר קודם לכן כבר לא חוקי. בחר מספר חוקי.
#YMSE: Error when saving the ecn with invalid value
invalidPerformanceClassError=בחר סיווג ביצועים חוקי.
#YMSE: Error when saving the ecn with invalid value
performanceClassNotAvailableError=סיווג הביצועים שנבחר קודם לכן ''{0}'' אינו חוקי כרגע. בחר את סיווג הביצועים החוקי.
#XTXT: Delete ECN confirmation text
deleteConfirmationTxtECN=האם אתה בטוח שברצונך למחוק צומת חישוב גמיש?
#XFLD: tooltip for ? button
help=עזרה
#XFLD: ECN edit button label
editECN=קבע תצורה
#XFLD: Technical type label for ERModel
DWC_ERMODEL=מודל של קשרי ישויות
#XFLD: Technical type label for Local Table
DWC_LOCAL_TABLE=טבלה מקומית
#XFLD: Technical type label for Remote Table
DWC_REMOTE_TABLE=טבלה מרוחקת
#XFLD: Technical type label for Analytic Model
DWC_ANALYTIC_MODEL=מודל אנליטי
#XFLD: Technical type label for Task Chain
DWC_TASKCHAIN=שרשרת משימות
#XFLD: Technical type label for Data Flow
DWC_DATAFLOW=תזרים נתונים
#XFLD: Technical type label for Replication Flow
DWC_REPLICATIONFLOW=תזרים שכפול
#XFLD: Technical type label for Transformation Flow
DWC_TRANSFORMATIONFLOW=תזרים טרנספורמציה
#XFLD: Technical type label for Intelligent Lookup
DWC_IDT=חיפוש חכם
#XFLD: Technical type label for Repository
DWC_REPOSITORY_PACKAGE=מאגר
#XFLD: Technical type label for Enterprise Search
DWC_ESMODEL=Enterprise Search
#XFLD: Technical type label for View
DWC_VIEW=תצוגה
#XFLD: Technical type label for Data Product
DWC_DATAPRODUCT=מוצר נתונים
#XFLD: Technical type label for Data Access Control
DWC_DAC=בקרת גישה לנתונים
#XFLD: Technical type label for Folder
DWC_FOLDER=תיקייה
#XFLD: Technical type label for Data Entity
DWC_BUSINESS_ENTITY=ישות עסקית
#XFLD: Technical type label for Business Entity Variant
DWC_BUSINESS_ENTITY_VARIANT=וריאנט של ישות עסקית
#XFLD: Technical type label for Responsibility Scenario
DWC_AUTH_SCENARIO=תרחיש אחריות
#XFLD: Technical type label for Fact Model
DWC_FACT_MODEL=מודל עובדות
#XFLD: Technical type label for Transformation Flow
DWC_PERSPECTIVE=פרספקטיבה
#XFLD: Technical type label for Consumption Model
DWC_CONSUMPTION_MODEL=מודל צריכה
#XFLD: Technical type label for Remote Connection
DWC_REMOTECONNECTION=חיבור מרוחק
#XFLD: Technical type label for Fact Model Variant
DWC_FACT_MODEL_VARIANT=וריאנט מודל עובדות
#XMSG: Schedule created alert message
createScheduleSuccess=נוצר תזמון
#XMSG: Schedule updated alert message
updateScheduleSuccess=התזמון עודכן
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=התזמון נמחק
#XMSG: Schedule takeover success message
ownerChangeScheduleSuccess=תזמון הוקצה לך
#XMSG: Message for starting pausing of schedule
pauseScheduleStarted=משהה תזמון 1
#XMSG: Message for starting resuming of schedule
resumeScheduleStarted=מחדש הפעלה של תזמון 1
#XFLD: Segmented button label
availableSpacesButton=זמין
#XFLD: Segmented button label
selectedSpacesButton=נבחר
#XFLD: Visit website button text
visitWebsite=ביקור באתר
#XMSG: Warning message on unselecting source language
sourceLanguageRemovalWarning=שפת המקור שנבחרה קודם לכן תוסר.
#XBUT: enable translation confirmation button
enableTranslationConfirmationBtn=אפשר
#XFLD: ECN performance class label
performanceClassLabel=סיווג ביצוע
#XTXT performance class memory text
memoryText=זיכרון
#XTXT performance class compute text
computeText=חישוב
#XTXT performance class high-compute text
highComputeText=חישוב גבוה
#XBUT: Recycle Bin Button Text
recycleBin=סל מיחזור
#XBUT: Restore Button Text
restore=שחזר
#XMSG: Warning message for new Workload Management UI
priorityWarning=אזור זה נמצא בקריאה בלבד. ניתן לשנות את עדיפות המרחב באזור מערכת / תצורה / ניהול עומסי עבודה.
#XMSG: Warning message for new Workload Management UI
workloadWarning=אזור זה נמצא בקריאה בלבד. ניתן לשנות את תצורת עומס העבודה של המרחב באזור מערכת / תצורה / ניהול עומסי עבודה.
#XCOL: Space table-view column: Apache Spark vCPUs
sparkManagementVCPUs=Apache Spark vCPUs
#XCOL: Space table-view column: Apache Spark Memory (GB)
sparkManagementMemory=זיכרון Apache Spark (ג"ב)
#XCOL: Space type (from Space Capability) for Data Ingestion Spaces
ingestionSpaceType=קליטת מוצר נתונים
#XFLD: Deployment in progress nodatatext for spark table
sparkTableDeploymentInProgress=אין נתונים זמינים מכיוון שהמרחב נפרס כרגע
#XFLD: Runtime data being loaded nodatatext for spark table
sparkTableDeploymentLoading=אין נתונים זמינים מכיוון שהמרחב נטען כרגע
#XCOL: Space type (from Space Capability) for BW Private Cloud Edition
BWPCESpaceType=SAP BW
#XBUT: Go to Instance Mappings Button
navToMappingsButton=ערוך מיפויי מופע
#XFLD: SAP HANA Data Lake Storage label if less than 1GB
dataLakeStorageUsed=< 1 GB
