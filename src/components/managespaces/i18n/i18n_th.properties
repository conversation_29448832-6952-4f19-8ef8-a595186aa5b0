#DO NOT REMOVE________________________
#XFLD: Breadcrumb for space monitoring page
spaceMonitorBreadcrumb=การติดตามตรวจสอบ
#XTXT: Type name for spaces in browser tab page title
space=พื้นที่
#_____________________________________
#XFLD: Spaces label in
spaces=พื้นที่
#XFLD: Manage plan button text
manageQuotaButtonText=จัดการแผน
#XBUT: Manage resources button
manageResourcesButton=จัดการทรัพยากร
#XFLD: Create space button tooltip
createSpace=สร้างพื้นที่
#XFLD: Create
create=สร้าง
#XFLD: Deploy
deploy=ปรับใช้
#XFLD: Page
page=หน้า
#XFLD: Cancel
cancel=ยกเลิก
#XFLD: Update
update=อัพเดท
#XFLD: Save
save=เก็บบันทึก
#XFLD: OK
ok=ตกลง
#XFLD: days
days=วัน
#XFLD: Space tile edit button label
edit=แก้ไข
#XFLD: Auto Assign all objects to space
autoAssign=กำหนดอัตโนมัติ
#XFLD: Space tile open monitoring button label
openMonitoring=ติดตามตรวจสอบ
#XFLD: Delete
delete=ลบ
#XFLD: Copy Space
copy=คัดลอก
#XFLD: Close
close=ปิด
#XCOL: Space table-view column status
status=สถานะ
#XFLD: Space status active
activeLabel=ใช้งานได้
#XFLD: Space status locked
lockedLabel=ถูกล็อค
#XFLD: Space status critical
criticalLabel=วิกฤต
#XFLD: Space status cold
coldLabel=Cold
#XFLD: Space status deleted
deletedLabel=ลบแล้ว
#XFLD: Space status unknown
unknownLabel=ไม่ทราบ
#XFLD: Space status ok
okLabel=สภาพพร้อมใช้งาน
#XFLD: Database user expired
expired=หมดอายุแล้ว
#XFLD: deployed
deployed=ปรับใช้แล้ว
#XFLD: not deployed
notDeployed=ยังไม่ได้ปรับใช้
#XFLD: changes to deploy
changesToDeploy=มีการเปลี่ยนแปลงให้ปรับใช้
#XFLD: pending
pending=กำลังปรับใช้
#XFLD: designtime error
designtimeError=ข้อผิดพลาดของ Design Time
#XFLD: runtime error
runtimeError=ข้อผิดพลาดของรันไทม์
#XFLD: Space created by label
createdBy=สร้างโดย
#XFLD: Space created on label
createdOn=สร้างเมื่อ
#XFLD: Space deployed on label
deployedOn=ปรับใช้เมื่อ
#XFLD: Space ID label
spaceID=ID พื้นที่
#XFLD: Priority label
priority=ลำดับความสำคัญ
#XFLD: Space Priority label
spacePriority=ลำดับความสำคัญของพื้นที่
#XFLD: Space Configuration label
spaceConfiguration=การกำหนดรูปแบบพื้นที่
#XFLD: Not available
notAvailable=ไม่พร้อมใช้งาน
#XFLD: WorkloadType default
default=ค่าตั้งต้น
#XFLD: WorkloadType custom
custom=ปรับแต่งได้
#XFLD: Data Lake option label; refers to component SAP HANA Cloud, Data Lake
dataLakeAccess=การเข้าถึง Data Lake
#XFLD: Translation label
translationLabel=การแปล
#XFLD: Source language label
sourceLanguageLabel=ภาษาต้นฉบับ
#XFLD: Translation CheckBox label
translationCheckBox=เปิดใช้งานการแปล
#XTXT: Popover hint that space has to be deployed to show user details
openDatabaseUserHintPopoverText=ปรับใช้พื้นที่เพื่อเข้าถึงรายละเอียดของผู้ใช้
#XTXT: Popover hint that space has to be deployed to open database explorer
openDatabaseExplorerHintPopoverText=ปรับใช้พื้นที่เพื่อเข้าถึง Explorer ของฐานข้อมูล
#XTXT: Text for when the Data Lake check box is disabled
dataLakeOptionDisabled=คุณไม่สามารถใช้พื้นที่นี้เพื่อเข้าถึง Data Lake ได้เนื่องจากมีการใช้โดยพื้นที่อื่นแล้ว
#XFLD: Data Lake check box text; refers to component SAP HANA Cloud, Data Lake
dataLakeCheckBox=ใช้พื้นที่นี้เพื่อเข้าถึง Data Lake
#XFLD: Space Priority minimum label extension
low=ต่ำ
#XFLD: Space Priority maximum label extension
high=สูง
#XFLD: Space name label
spaceName=ชื่อพื้นที่
#XFLD: Enable deploy objects checkbox
enableDeployObjects=ปรับใช้ออบเจค
#XTIT: Copy spaces dialog title
copySpaceDialogTitle=คัดลอก {0}
#XTXT Human readable text for unselected source language
unSelectedSourceLanguage=(ไม่ได้เลือก)
#XTXT Human readable text for language code "af"
af=แอฟริกา
#XTXT Human readable text for language code "ar"
ar=อารบิก
#XTXT Human readable text for language code "bg"
bg=บัลแกเรีย
#XTXT Human readable text for language code "ca"
ca=คาตาลัน
#XTXT Human readable text for language code "zh"
zh=จีนประยุกต์
#XTXT Human readable text for language code "zf"
zf=จีน
#XTXT Human readable text for language code "hr"
hr=โครเอเชีย
#XTXT Human readable text for language code "cs"
cs=เช็ก
#XTXT Human readable text for language code "cy"
cy=เวลส์
#XTXT Human readable text for language code "da"
da=เดนมาร์ก
#XTXT Human readable text for language code "nl"
nl=ดัตช์
#XTXT Human readable text for language code "en-UK"
en-UK=อังกฤษ (สหราชอาณาจักร)
#XTXT Human readable text for language code "en"
en=อังกฤษ (สหรัฐอเมริกา)
#XTXT Human readable text for language code "et"
et=เอสโตเนีย
#XTXT Human readable text for language code "fa"
fa=เปอร์เซีย
#XTXT Human readable text for language code "fi"
fi=ฟินแลนด์
#XTXT Human readable text for language code "fr-CA"
fr-CA=ฝรั่งเศส (แคนาดา)
#XTXT Human readable text for language code "fr"
fr=ฝรั่งเศส
#XTXT Human readable text for language code "de"
de=เยอรมัน
#XTXT Human readable text for language code "el"
el=กรีก
#XTXT Human readable text for language code "he"
he=ฮีบรู
#XTXT Human readable text for language code "hi"
hi=ฮินดี
#XTXT Human readable text for language code "hu"
hu=ฮังการี
#XTXT Human readable text for language code "is"
is=ไอซ์แลนด์
#XTXT Human readable text for language code "id"
id=บาฮาซาอินโดนีเซีย
#XTXT Human readable text for language code "it"
it=อิตาลี
#XTXT Human readable text for language code "ja"
ja=ญี่ปุ่น
#XTXT Human readable text for language code "kk"
kk=คาซัค
#XTXT Human readable text for language code "ko"
ko=เกาหลี
#XTXT Human readable text for language code "lv"
lv=ลัตเวีย
#XTXT Human readable text for language code "lt"
lt=ลิทัวเนีย
#XTXT Human readable text for language code "ms"
ms=มลายู
#XTXT Human readable text for language code "no"
no=นอร์เวย์
#XTXT Human readable text for language code "pl"
pl=โปแลนด์
#XTXT Human readable text for language code "pt"
pt=โปรตุเกส (บราซิล)
#XTXT Human readable text for language code "pt-PT"
pt-PT=โปรตุเกส (โปรตุเกส)
#XTXT Human readable text for language code "ro"
ro=โรมาเนีย
#XTXT Human readable text for language code "ru"
ru=รัสเซีย
#XTXT Human readable text for language code "sr"
sr=เซอร์เบีย
#XTXT Human readable text for language code "sh"
sh=เซอร์โบ-โครเอเชีย
#XTXT Human readable text for language code "sk"
sk=สโลวัก
#XTXT Human readable text for language code "sl"
sl=สโลวีเนีย
#XTXT Human readable text for language code "es"
es=สเปน
#XTXT Human readable text for language code "es-MX"
es-MX=สเปน (เม็กซิโก)
#XTXT Human readable text for language code "sv"
sv=สวีเดน
#XTXT Human readable text for language code "th"
th=ไทย
#XTXT Human readable text for language code "tr"
tr=ตุรกี
#XTXT Human readable text for language code "uk"
uk=ยูเครน
#XTXT Human readable text for language code "vi"
vi=เวียดนาม
#XTIT: Delete spaces confirmation title
deleteConfirmationHeaderText=ลบพื้นที่
#XTXT: Logically delete space delete confirmation text with space name
logicallyDeleteConfirmationBodyTextWithSpaceName=คุณแน่ใจหรือไม่ว่าต้องการย้ายพื้นที่ "{0}" ไปยังถังรีไซเคิล?
#XTXT: Logically dpace delete confirmation text for multiple spaces
logicallyDeleteConfirmationBodyTextMultiple=คุณแน่ใจหรือไม่ว่าต้องการย้ายพื้นที่ที่เลือก "{0}" ไปยังถังรีไซเคิล?
#XTXT: Delete space delete confirmation text with space name
deleteConfirmationBodyTextWithSpaceName=คุณแน่ใจหรือไม่ว่าต้องการลบพื้นที่ "{0}"? การดำเนินการนี้จะไม่สามารถเลิกทำได้
#XTXT: Delete space delete confirmation text for multiple spaces
deleteConfirmationBodyTextMultiple=คุณแน่ใจหรือไม่ว่าต้องการลบพื้นที่ที่เลือก ''{0}''? การดำเนินการนี้จะไม่สามารถเลิกทำได้ เนื้อหาต่อไปนี้จะถูกลบ {1}:
#XTXT: permanently
permanently=อย่างถาวร
#XTXT: {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDetail=เนื้อหาต่อไปนี้จะถูกลบ{0} และไม่สามารถกู้คืนได้:
#XTXT: Hint Delete space confirmation text
deleteConfirmationHint=กรุณาพิมพ์ {0} เพื่อยืนยันการลบ
#XTXT: Delete space confirmation error check spelling
deleteConfirmationErrorCheckSpelling=กรุณาตรวจสอบการสะกดของคุณและลองอีกครั้ง
#XTXT: All Spaces
allSpaces=พื้นที่ทั้งหมด
#XTXT: All data
allData=ออบเจคและข้อมูลทั้งหมดที่อยู่ในพื้นที่
#XTXT: All connections
allConnections=การเชื่อมต่อทั้งหมดที่กำหนดไว้ในพื้นที่
#XFLD: Space tile selection box tooltip
clickToSelect=คลิกเพื่อเลือก
#XTXT: All database users
allDatabaseUsers=ออบเจคและข้อมูลทั้งหมดที่อยู่ใน Open SQL Schema ที่เกี่ยวข้องกับพื้นที่
#XFLD: remove members button tooltip
deleteUsers=ย้ายสมาชิกออก
#XTXT: Space long description text
description=คำอธิบาย (สูงสุด 4,000 อักขระ)
#XFLD: Add Members button tooltip
addUsers=เพิ่มสมาชิก
#XFLD: Add Users button tooltip
addUsersTooltip=เพิ่มผู้ใช้
#XFLD: Edit Users button tooltip
editUsersTooltip=แก้ไขผู้ใช้
#XFLD: Remove Users button tooltip
removeUsersTooltip=ย้ายผู้ใช้ออก
#XFLD: Searchfield placeholder
filter=ค้นหา
#XCOL: Users table-view column health
health=สถานะ
#XCOL: Users table-view column access
access=การเข้าถึง
#XFLD: No user found nodatatext
noDataText=ไม่พบผู้ใช้
#XTIT: Members dialog title
selectUserDialogTitle=เพิ่มสมาชิก
#XTIT: User dialog title
addUserDialogTitle=เพิ่มผู้ใช้
#XTIT: Delete connections confirmation title
deleteConnectionConfirmationHeaderText=ลบการเชื่อมต่อ
#XTIT: Delete single connection confirmation title
deleteConnectionConfirmationHeaderTextSingle=ลบการเชื่อมต่อ
#XTXT: Delete connection delete confirmation text
deleteConnectionConfirmationBodyText=คุณแน่ใจหรือไม่ว่าต้องการลบการเชื่อมต่อที่เลือก? การเชื่อมต่อจะถูกย้ายออกแบบถาวร
#XTIT: Remote source dialog title
selectRemoteConnectionDialogTitle=เลือกการเชื่อมต่อ
#XTIT: Share connection dialog title
connectionSharingDialogTitle=แชร์การเชื่อมต่อ
#XTIT: Connections shared from other spaces table title
sharedConnectionsFromOtherSpaces=การเชื่อมต่อที่แชร์
#XFLD: Add remote source button tooltip
addRemoteConnections=เพิ่มการเชื่อมต่อ
#XFLD: Delete remote source button tooltip
deleteRemoteConnections=ย้ายการเชื่อมต่อออก
#XFLD: Share remote source button tooltip
shareConnections=แชร์การเชื่อมต่อ
#XFLD: Tile-layout tooltip
tileLayout=โครงร่าง Tile
#XFLD: Table-layout tooltip
tableLayout=โครงร่างตาราง
#XMSG: Success message after creating space
createSpaceSuccessMessage=สร้างพื้นที่แล้ว
#XMSG: Success message after copying space
copySpaceSuccessMessage=กำลังคัดลอกพื้นที่ "{0}" ไปยังพื้นที่ "{1}"
#XMSG: Success message after deploying space
deploymentSuccessMessage=การปรับใช้พื้นที่เริ่มต้นแล้ว
#XMSG: Success message after Apache Spark update started
sparkUpdateStartedSuccessMessage=การอัพเดท Apache Spark เริ่มต้นแล้ว
#YMSE: Error while starting Apache Spark update
sparkUpdateStartedErrorMessage=ไม่สามารถอัพเดท Apache Spark
#XMSG: Success message after updating space
updateSettingsSuccessMessage=อัพเดทรายละเอียดพื้นที่แล้ว
#XMSG: Success message after unlocking space
unlockSpaceSuccessMessage=ปลดล็อคพื้นที่ชั่วคราวแล้ว
#XMSG: Success message after deleting a single space
deleteSpaceSuccessMessage=ลบพื้นที่แล้ว
#XMSG: Success message after deleting multiple spaces
deleteSpacesSuccessMessage=ลบพื้นที่แล้ว
#XMSG: Success message after restoring a single space
restoreSpaceSuccessMessage=คืนค่าพื้นที่แล้ว
#XMSG: Success message after restoring multiple spaces
restoreSpacesSuccessMessage=คืนค่าพื้นที่แล้ว
#YMSE: Error while updating settings
updateSettingsFailureMessage=ไม่สามารถอัพเดทการกำหนดค่าพื้นที่ได้
#YMSE: Error while updating data lake option
dataLakeEnabledInOtherSpace=Data Lake ถูกกำหนดให้กับพื้นที่อื่นแล้ว มีเพียงหนึ่งพื้นที่ที่สามารถเข้าถึง Data Lake ได้ต่อครั้ง
#YMSE: Error while updating data lake option
virtualTablesExists=คุณสามารถยกเลิกการกำหนด Data Lake จากพื้นที่นี้ได้เนื่องจากยังคงมีความสัมพันธ์กับตารางเสมือน* กรุณาลบตารางเสมือนดังกล่าวเพื่อยกเลิกการกำหนด Data Lake จากพื้นที่นี้
#YMSE: Error while unlocking space
unlockSpaceFailureMessage=ไม่สามารถปลดล็อคพื้นที่ได้
#YMSE: Error while creating space
createSpaceError=ไม่สามารถสร้างพื้นที่ได้
#YMSE: Error while creating duplicate space
createDuplicateSpaceError=พื้นที่ที่ชื่อ {0} มีอยู่แล้ว
#YMSE: Error while deleting a single space
deleteSpaceError=ไม่สามารถลบพื้นที่ได้
#YMSE: Error details while deleting a single space
deleteSpaceDetailedError=พื้นที่ "{0}" ของคุณไม่สามารถทำงานได้อย่างถูกต้องอีกต่อไป กรุณาลองลบอีกครั้ง ถ้ายังใช้ไม่ได้ ให้ขอให้ผู้ดูแลระบบลบพื้นที่ดังกล่าวหรือเปิด Ticket
#YMSE: Error while deleting a single space in files
deleteSpaceInFilesError=ไม่สามารถลบข้อมูลพื้นที่ในไฟล์ได้
#YMSE: Error while deleting a single space users
deleteSpaceUsersError=ไม่สามารถย้ายผู้ใช้ออกได้
#YMSE: Error while deleting a single space schema
deleteSpaceSchemasError=ไม่สามารถย้าย Schema ออกได้
#YMSE: Error while deleting a single space connections
deleteSpaceConnectionsError=ไม่สามารถย้ายการเชื่อมต่อออกได้
#YMSE: Error while deleting a single space data
deleteSpaceDataError=ไม่สามารถลบข้อมูลพื้นที่ได้
#YMSE: Error while deleting multiple spaces
deleteSpacesError=ไม่สามารถลบพื้นที่ได้
#YMSE: Error while restoring a single space
restoreSpaceError=ไม่สามารถคืนค่าพื้นที่ได้
#YMSE: Error while restoring multiple spaces
restoreSpacesError=ไม่สามารถคืนค่าพื้นที่ได้
#YMSE: Error while creating users
createUsersError=ไม่สามารถเพิ่มผู้ใช้ได้
#YMSE: Error while removing users
removeUsersError=เราไม่สามารถย้ายผู้ใช้ออกได้
#YMSE: Error while removing user
removeUserError=ไม่สามารถย้ายผู้ใช้ออก
#YMSE: Error assigning roles to a user
roleAssignUnAssignError=เราไม่สามารถเพิ่มผู้ใช้ในบทบาทที่กำหนดขอบเขตที่เลือกไว้ \n\n คุณไม่สามารถเพิ่มตนเองในบทบาทที่กำหนดขอบเขต แต่สามารถขอให้ผู้ดูแลระบบเพิ่มคุณในบทบาทที่กำหนดขอบเขต
#YMSE: Error assigning user to the space
userAssignError=เราไม่สามารถกำหนดผู้ใช้ให้กับพื้นที่ได้ \n\n ผู้ใช้ถูกกำหนดให้กับพื้นที่ตามบทบาทที่กำหนดขอบเขตจนถึงจำนวนสูงสุดที่อนุญาต (100) แล้ว
#YMSE: Error assigning users to the space
usersAssignError=เราไม่สามารถกำหนดผู้ใช้ให้กับพื้นที่ได้ \n\n ผู้ใช้ถูกกำหนดให้กับพื้นที่ตามบทบาทที่กำหนดขอบเขตจนถึงจำนวนสูงสุดที่อนุญาต (100) แล้ว
#YMSE: Error while fetching users
fetchUserWithScopedRolesError=เราไม่สามารถดึงข้อมูลผู้ใช้ได้ กรุณาลองอีกครั้งภายหลัง
#YMSE: Error while fetching scoped roles
fetchScopedRolesError=เราไม่สามารถดึงข้อมูลบทบาทที่กำหนดขอบเขตได้
#YMSE: Error while fetching members
fetchUserError=ไม่สามารถดึงข้อมูลสมาชิกได้ กรุณาลองอีกครั้งภายหลัง
#YMSE: Error while loading run-time database
loadRuntimeError=เราไม่สามารถโหลดข้อมูลจากฐานข้อมูลรันไทม์ได้
#YMSE: Error while loading spaces
loadSpacesError=ขออภัย มีบางอย่างผิดปกติเมื่อพยายามดึงข้อมูลพื้นที่ของคุณ
#YMSE: Error while loading haas resources
loadStorageError=ขออภัย มีบางอย่างผิดปกติเมื่อพยายามดึงข้อมูลการจัดเก็บ
#YMSE: Error no data could be loaded
loadDataError=ขออภัย มีบางอย่างผิดปกติเมื่อพยายามดึงข้อมูลของคุณ
#XFLD: Click to refresh storage data
clickToRefresh=คลิกที่นี่เพื่อลองอีกครั้ง
#XTIT: Delete space popup title
deleteSpacePopupTitle=ลบพื้นที่
#XCOL: Spaces table-view column name
name=ชื่อ
#XCOL: Spaces table-view deployment status
deploymentStatus=สถานะการปรับใช้
#XFLD: Disk label in space details
storageLabel=ดิสก์ (GB)
#XFLD: In-Memory label in space details
ramLabel=หน่วยความจำ (GB)
#XFLD: Memory label on space card
memory=หน่วยความจำสำหรับพื้นที่จัดเก็บ
#XFLD: Storage Assignment label in space details
storrageAssignmentLabel=พื้นที่จัดเก็บ
#XFLD: Storage Type label in space details
storageTypeLabel=ประเภทพื้นที่จัดเก็บ
#XFLD: Enable Space Quota
enableSpaceQuota=เปิดใช้งานโควต้าพื้นที่
#XFLD: No Space Quota
noSpaceQuota=ไม่มีโควต้าพื้นที่
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
diskAndInMemoryText=SAP HANA Database (ดิสก์และหน่วยความจำ)
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
dataLakeFilesText=SAP HANA, ไฟล์ Data Lake
#XFLD: Available scoped roles label
availableRoles=บทบาทที่กำหนดขอบเขตที่ใช้ได้
#XFLD: Selected scoped roles label
selectedRoles=บทบาทที่กำหนดขอบเขตที่เลือก
#XCOL: Spaces table-view column models
models=โมเดล
#XCOL: Spaces table-view column users
users=ผู้ใช้
#XCOL: Spaces table-view column connections
connections=การเชื่อมต่อ
#XFLD: Section header overview in space detail
overview=ภาพรวม
#XFLD: Section header Apache Spark in space detail
sparkManagementSection=Apache Spark
#XFLD: Section header Apache Spark in space detail
sparkManagementSubSection=แอพพลิเคชัน
#XFLD: Section header Apache Spark in space detail
taskAssignmentSubSection=การกำหนดงาน
#XFLD: vCPU label in Apache Spark section
vCPULabel=vCPU
#XFLD: Memory label in Apache Spark section
memoryLabel=หน่วยความจำ (GB)
#XFLD: space configuration label in Apache Spark section
spaceConfigurationLabel=การกำหนดรูปแบบพื้นที่
#XFLD: Space Source label
sparkApplicationLabel=แอพพลิเคชัน
#XFLD: Cluster Size label
clusterSizeLabel=ขนาดคลัสเตอร์
#XFLD: Driver label
driverLabel=ไดรเวอร์
#XFLD: Executor label
executorLabel=ผู้ดำเนินการ
#XFLD: max label
maxLabel=การใช้สูงสุด
#XFLD: TrF Default label
trFDefaultLabel=ค่าตั้งต้นของผังการแปลงข้อมูล
#XFLD: Merge Default label
mergeDefaultLabel=ผสานค่าตั้งต้น
#XFLD: Optimize Default label
optimizeDefaultLabel=ปรับค่าตั้งต้นให้เหมาะสม
#XFLD: Deployment Default label
deploymentDefaultLabel=การปรับใช้ตารางภายใน (ไฟล์)
#XFLD: max spark source label
driverExecutorMaxUsedSparkSource={0} CPU/{1} GB
#XFLD: Object type label
taskObjectTypeLabel=ประเภทออบเจค
#XFLD: Task activity label
taskActivityLabel=กิจกรรม
#XFLD: Task Application ID label
taskApplicationIDLabel=แอพพลิเคชันตั้งต้น
#XFLD: Section header in space detail
generalSettings=การกำหนดค่าทั่วไป
#XMSG: Message strip message when a space is locked
spaceLockedMessageStrip=ขณะนี้พื้นที่นี้ถูกล็อคโดยระบบ
#XMSG: Message strip message deploy warning
deployWarningMessageStrip=การเปลี่ยนแปลงในส่วนนี้จะถูกปรับใช้ทันที
#XMSG: Message strip message modify space configuration warning
modifySpaceConfigurationMessageStrip=กรุณาจำไว้ว่าการเปลี่ยนแปลงค่าเหล่านี้อาจเกิดปัญหาด้านประสิทธิภาพได้
#XFLD: Button text to unlock the space again
unlockSpace=ปลดล็อคพื้นที่
#XFLD: Info text for audit log formatted message
auditLogText=เปิดใช้งานล็อกการตรวจสอบเพื่อบันทึกการดำเนินการอ่านหรือเปลี่ยนแปลง (นโยบายการตรวจสอบ) จากนั้นผู้ดูแลระบบจะสามารถวิเคราะห์ได้ว่าใครเป็นผู้ดำเนินการใด ณ เวลาใด
#XMSG: Message strip  audit warning
auditLogWarningMessageStrip=ล็อกการตรวจสอบอาจใช้พื้นที่จัดเก็บในดิสก์จำนวนมากใน Tenant ของคุณ หากคุณเปิดใช้งานนโยบายการตรวจสอบ (การดำเนินการอ่านหรือเปลี่ยนแปลง) คุณควรตรวจสอบการใช้พื้นที่จัดเก็บในดิสก์เป็นประจำ (ผ่านบัตร 'พื้นที่จัดเก็บในดิสก์ที่ใช้' ในตัวติดตามตรวจสอบระบบ) เพื่อป้องกันไม่ให้เกิดการขัดข้องเมื่อดิสก์เต็ม ซึ่งอาจนำไปสู่การหยุดชะงักของบริการ หากคุณปิดใช้งานนโยบายการตรวจสอบ รายการล็อกการตรวจสอบทั้งหมดจะถูกลบ หากคุณต้องการเก็บรายการล็อกการตรวจสอบ ให้พิจารณาเอ็กซ์ปอร์ตก่อนที่คุณจะปิดใช้งานนโยบายการตรวจสอบ
#XFLD: Message strip link text to open in app help to guide user what to do to unlock his space permanently
unlockSpaceHelp=แสดงวิธีใช้
#XMSG: Message strip message when a space will be locked again
spaceTemporarilyUnlockedMessageStrip=พื้นที่นี้เกินขีดจำกัดของพื้นที่จัดเก็บและจะถูกล็อคใน {0} {1}
#XMSG: Unit for remaining time until space is locked again
hours=ชั่วโมง
#XMSG: Unit for remaining time until space is locked again
minutes=นาที
#XFLD: Section header in space detail for auditing
detailsSectionAuditing=การตรวจสอบ
#XFLD: Subsection header in space detail for auditing
auditing=การกำหนดค่าการตรวจสอบพื้นที่
#XFLD: Hot space tooltip
hotSpaceCountTooltip=พื้นที่วิกฤต: พื้นที่จัดเก็บที่ใช้ไปมากกว่า 90%
#XFLD: Green space tooltip
greenSpaceCountTooltip=พื้นที่พร้อมใช้งาน: พื้นที่จัดเก็บที่ใช้ไประหว่าง 6% ถึง 90%
#XFLD: Cold space tooltip
coldSpaceCountTooltip=พื้นที่ที่มีการใช้งานน้อย: พื้นที่จัดเก็บที่ใช้ไปไม่เกิน 5%
#XFLD: Critical space tooltip
criticalSpaceCountTooltip=พื้นที่วิกฤต: พื้นที่จัดเก็บที่ใช้ไปมากกว่า 90%
#XFLD: Green space tooltip
okSpaceCountTooltip=พื้นที่พร้อมใช้งาน: พื้นที่จัดเก็บที่ใช้ไประหว่าง 6% ถึง 90%
#XFLD: Locked space tooltip
lockedSpaceCountTooltipNew=พื้นที่ที่ถูกล็อค: ถูกบล็อคเนื่องจากหน่วยความจำไม่เพียงพอ
#XFLD: Locked space tooltip
lockedSpaceCountTooltip=พื้นที่ที่ถูกล็อค
#YMSE: Error while deleting remote source
deleteRemoteError=ไม่สามารถย้ายการเชื่อมต่อออกได้
#XFLD: Space ID input tooltip
spaceIDInfoTooltip=ไม่สามารถเปลี่ยนแปลง ID พื้นที่ได้ในภายหลัง\nอักขระที่ถูกต้อง ได้แก่ A - Z, 0 - 9 และ _
#YMSE: Error when space name is empty
spaceNameEmptyValidationError=ป้อนชื่อพื้นที่
#YMSE: Error when ECN Business name is empty
ecnNameEmptyValidationError=ป้อนชื่อทางธุรกิจ
#YMSE: Error when space id is empty
spaceIDInputEmptyValidationError=ป้อน ID พื้นที่
#YMSE: Error when space id contains invalid characters
spaceIDInputInValidError=อักขระไม่ถูกต้อง กรุณาใช้ A - Z, 0 - 9 และ _ เท่านั้น
#YMSE: Error when space id exists while creation
spaceIDInputDuplicateError=ID พื้นที่มีอยู่แล้ว
#XFLD: Space searchfield placeholder
search=ค้นหา
#XMSG: Success message after creating users
createUsersSuccess=เพิ่มผู้ใช้แล้ว
#XMSG: Success message after creating user
createUserSuccess=เพิ่มผู้ใช้แล้ว
#XMSG: Success message after updating users
updateUsersSuccess=อัพเดทผู้ใช้ {0} รายแล้ว
#XMSG: Success message after updating user
updateUserSuccess=อัพเดทผู้ใช้แล้ว
#XMSG: Success message after removing users
removeUsersSuccess=ย้ายผู้ใช้ {0} รายออกแล้ว
#XMSG: Success message after removing user
removeUserSuccess=ย้ายผู้ใช้ออกแล้ว
#XFLD: Schema name
schemaName=ชื่อ Schema
#XFLD: used of total
ofTemplate={0} จาก {1}
#XFLD: Assigned disk label in space overview
assignedStorageTemplate=ดิสก์ที่กำหนด ({0} จาก {1})
#XFLD: Assigned Memory label in space overview
assignedRAMTemplate=หน่วยความจำที่กำหนด ({0} จาก {1})
#XFLD: Storage ratio on space
accelearationRAM=การเร่งความเร็วของหน่วยความจำ
#XFLD: No Storage Consumption
noStorageConsumptionText=ไม่มีการกำหนดโควต้าพื้นที่จัดเก็บ
#XFLD: Used disk label in space overview
usedStorageTemplate=ดิสก์ที่ใช้สำหรับพื้นที่จัดเก็บ ({0} จาก {1})
#XFLD: Used Memory label in space overview
usedRAMTemplate=หน่วยความจำที่ใช้สำหรับพื้นที่จัดเก็บ ({0} จาก {1})
#XFLD: Used Disk tooltip
usedStorageTooltipTemplate={0} จาก {1} ดิสก์ที่ใช้สำหรับพื้นที่จัดเก็บ
#XFLD: Used Memory tooltip
usedRAMTooltipTemplate={0} จาก {1} หน่วยความจำที่ใช้
#XFLD: Assigned Disk tooltip
assignedStorageTooltipTemplate={0} จาก {1} ดิสก์ที่กำหนด
#XFLD: Assigned Memory tooltip
assignedRAMTooltipTemplate={0} จาก {1} หน่วยความจำที่กำหนด
#XFLD: Space related used storage tooltip
spaceRelatedUsedStorageTooltipTemplate=ข้อมูลพื้นที่: {0}
#XFLD: Not space related used storage tooltip
otherDataUsedStorageTooltipTemplate=ข้อมูลอื่น: {0}
#XFLD: Storage used exceeded popover and tooltip
storageUsedExceededText=พิจารณาขยายแผนของคุณ หรือติดต่อ SAP Support
#XCOL: Space table-view column used Disk
usedStorage=ดิสก์ที่ใช้สำหรับพื้นที่จัดเก็บ
#XCOL: Space monitor column used Memory
usedRAM=หน่วยความจำที่ใช้สำหรับพื้นที่จัดเก็บ
#XCOL: Space monitor column Schema
tableSchema=Schema
#XCOL: Space monitor column Storage Type
tableStorageType=ประเภทพื้นที่จัดเก็บ
#XCOL: Space monitor column Table Type
tableType=ประเภทตาราง
#XCOL: Space monitor column Record Count
tableRecordCount=จำนวนเรคคอร์ด
#XFLD: Assigned Disk
assignedStorage=ดิสก์ที่กำหนดไว้สำหรับพื้นที่จัดเก็บ
#XFLD: Assigned Memory
assignedRAM=หน่วยความจำที่กำหนดไว้สำหรับพื้นที่จัดเก็บ
#XCOL: Space table-view column storage utilization
tableStorageUtilization=พื้นที่จัดเก็บที่ใช้
#XFLD: space status
spaceStatus=สถานะพื้นที่
#XFLD: space type
spaceType=ประเภทพื้นที่
#XFLD: space type label for space with space type "SAP BW bridge" ("SAP BW bridge" is a product name, do not translate it)
abapbridgeSpaceType=บริดจ์ของ SAP BW
#XFLD: space type label for space with space type "Data Marketplace"
marketplaceSpaceType=ผลิตภัณฑ์ของผู้ให้บริการข้อมูล
#XMSG: Error message for when selected space cannot be deleted due to the space type
deleteTypedSpaceError=คุณไม่สามารถลบพื้นที่ {0} เนื่องจากประเภทพื้นที่คือ {1}
#XMSG: Error message for when selected spaces cannot be deleted due to the space type
deleteTypedSpacesError=คุณไม่สามารลบพื้นที่ {0} ที่เลือกไว้ พื้นที่ที่มีประเภทพื้นที่ต่อไปนี้ไม่สามารถลบได้: {1}
#XFLD: space type label for space without space type
defaultSpaceType=SAP Datasphere
#XFLD: Tooltip for monitor space button
monitorSpace=ติดตามตรวจสอบ
#XFLD: Tooltip for edit space button
editSpace=แก้ไขพื้นที่
#XMSG: Deletion warning in messagebox
deleteConfirmation=คุณแน่ใจหรือไม่ว่าต้องการลบพื้นที่นี้?
#XFLD: Tooltip for delete space button
deleteSpace=ลบพื้นที่
#XFLD: storage
storage=ดิสก์สำหรับพื้นที่จัดเก็บ
#XFLD: username
userName=ชื่อผู้ใช้
#XFLD: port
port=พอร์ต
#XFLD: hostname
hostName=ชื่อโฮสต์
#XFLD: password
password=รหัสผ่าน
#XBUT: Request new password button
requestPassword=ขอรหัสผ่านใหม่
#YEXP: Usage explanation in time data section
timeDataSectionHint=สร้างตารางและไดเมนชันเวลาเพื่อใช้ในโมเดลและเนื้อหาของคุณ
#YEXP: Usage explanation in Space schema section
spaceSchemaHint=คุณต้องการให้ข้อมูลในพื้นที่ของคุณสามารถใช้ได้โดยเครื่องมือหรือแอพอื่นๆ หรือไม่? ถ้าใช่ ให้สร้างผู้ใช้อย่างน้อยหนึ่งรายที่สามารถเข้าถึงข้อมูลในพื้นที่ของคุณ และเลือกว่าต้องการให้ข้อมูลพื้นที่ในอนาคตทั้งหมดสามารถใช้ได้ตามค่าตั้งต้นหรือไม่
#XTIT: Create schema popup title
createSchemaDialogTitle=สร้าง Open SQL Schema
#XTIT: Generate Time Data dialog title
generateTimeDataDialogTitle=สร้างตารางและไดเมนชันเวลา
#XTIT: Edit Time Data dialog title
editTimeDataDialogTitle=แก้ไขตารางและไดเมนชันเวลา
#XTIT: Time Data token title
timeDataTokenTitle=ข้อมูลเวลา
#XTIT: Time Data token title
timeDataUpdateViews=อัพเดทมุมมองข้อมูลเวลา
#XFLD: Time Data token creation in progress label
timeDataCreationInProgress=การสร้างอยู่ระหว่างดำเนินการ...
#XFLD: Time Data token creation error label
timeDataCreationError=การสร้างล้มเหลว กรุณาลองอีกครั้ง
#XTIT: Time Data dialog time settings title
timeDataDialogTimeSettingsTitle=การกำหนดค่าตารางเวลา
#XTIT: Time Data dialog time data table title
timeDataDialogTranslationTablesTitle=ตารางการแปลง
#XTIT: Time Data dialog dimensions title
timeDataDialogTimeViewsTitle=ไดเมนชันเวลา
#XFLD: Time Data dialog time range label
timeRangeHint=กำหนดช่วงเวลา
#XFLD: Time Data dialog time data table label
timeDataHint=ตั้งชื่อตารางของคุณ
#XFLD: Time Data dialog time data dimensions label
timeDimensionsHint=ตั้งชื่อไดเมนชันของคุณ
#XFLD: Time Data Time range description label
timerangeLabel=ช่วงเวลา
#XFLD: Time Data dialog from year label
fromYearLabel=จากปี
#XFLD: Time Data dialog to year label
toYearLabel=ถึงปี
#XFLD: Time Data dialog calendar type label
calendarTypeLabel=ประเภทของปฏิทิน
#XFLD: Time Data dialog granularity label
granularityLabel=Granularity
#XFLD: Time Data dialog technical name label
technicalNameLabel=ชื่อทางเทคนิค
#XFLD: Time Data dialog quarters text table lable
quarterTextsTableLabel=ตารางการแปลงสำหรับไตรมาส
#XFLD: Time Data dialog months text table lable
monthsTextsTableLabel=ตารางการแปลงสำหรับเดือน
#XFLD: Time Data dialog days text table lable
daysTextsTableLabel=ตารางการแปลงสำหรับวัน
#XFLD: Time Data dialog year label
yearLabel=ไดเมนชันของปี
#XFLD: Time Data dialog quarter label
quarterLabel=ไดเมนชันของไตรมาส
#XFLD: Time Data dialog month label
monthLabel=ไดเมนชันของเดือน
#XFLD: Time Data dialog day label
dayLabel=ไดเมนชันของวัน
#XFLD: Time Data dialog gregorian calendar type label
gregorian=คริสต์ศักราช
#XFLD: Time Data dialog time granularity day label
day=วัน
#XFLD: Time Data dialog business name maxlength reached
timeRangeValueStateTextMaxLengthBusinessName=ถึงความยาวสูงสุด 1000 อักขระแล้ว
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextMaxRangeExceeded=ช่วงเวลาสูงสุดคือ 150 ปี
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeLower="จากปี" ควรอยู่ก่อน "ถึงปี"
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeHigher="จากปี" ต้องเป็นปี 1900 ขึ้นไป
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeHigher="ถึงปี" ควรอยู่หลัง "จากปี"
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeLower="ถึงปี" ต้องอยู่ก่อนปีปัจจุบันบวก 100
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningFromYear=การเพิ่ม "จากปี" อาจทำให้เกิดการสูญหายของข้อมูล
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningToYear=การลด "ถึงปี" อาจทำให้เกิดการสูญหายของข้อมูล
#XMSG: Time Data creation validation error message
timeDataValidationError=ดูเหมือนว่าบางฟิลด์ไม่ถูกต้อง กรุณาตรวจสอบฟิลด์ที่ต้องป้อนข้อมูลเพื่อดำเนินการต่อ
#XMSG: Time Data deletion popup text
deleteTimeDataConfirmation=คุณแน่ใจหรือไม่ว่าต้องการลบข้อมูล?
#XMSG: Time Data creation success message
createTimeDataSuccess=สร้างข้อมูลเวลาแล้ว
#XMSG: Time Data update success message
updateTimeDataSuccess=อัพเดทข้อมูลเวลาแล้ว
#XMSG: Time Data delete success message
deleteTimeDataSuccess=ลบข้อมูลเวลาแล้ว
#XMSG: Time Data creation error message
createTimeDataError=มีบางอย่างผิดปกติขณะพยายามสร้างข้อมูลเวลา
#XMSG: Time Data update error message
updateTimeDataError=มีบางอย่างผิดปกติขณะพยายามอัพเดทข้อมูลเวลา
#XMSG: Time Data creation error message
deleteTimeDataError=มีบางอย่างผิดปกติขณะพยายามลบข้อมูลเวลา
#XMSG: Error message when time data could not be retrieved
retrievingTimeDataError=ไม่สามารถโหลดข้อมูลเวลาได้
#XTIT: Time Data deletion popup title
deleteTimeDataConfirmationTitle=คำเตือน
#XTXT: Time Data deletion dependency popup text
deleteTimeDataDependencyText=เราไม่สามารถลบข้อมูลเวลาของคุณได้เนื่องจากถูกใช้ในโมเดลอื่นๆ
#XTXT: Time Data deletion dependency popup text singular
deleteTimeDataDependencyTextSingular=เราไม่สามารถลบข้อมูลเวลาของคุณได้เนื่องจากถูกใช้ในโมเดลอื่น
#XMSG: Validation error when required time data form field is empty
timeDataRequiredField=ฟิลด์นี้เป็นฟิลด์ที่ต้องป้อนข้อมูลและต้องไม่เว้นว่างไว้
#XFLD: Tooltip for Open in Database Explorer button
openInDBX=เปิดใน Explorer ของฐานข้อมูล
#YMSE: Dimension Year
dimensionYearView=ไดเมนชัน "ปี"
#YMSE: Dimension Year
dimensionQuarterView=ไดเมนชัน "ไตรมาส"
#YMSE: Dimension Year
dimensionMonthView=ไดเมนชัน "เดือน"
#YMSE: Dimension Year
dimensionDayView=ไดเมนชัน "วัน"
#XFLD: Time Data deletion object title
timeDataUsedIn=(ถูกใช้ใน {0} โมเดล)
#XFLD: Time Data deletion object title singular
timeDataUsedInSingular=(ถูกใช้ใน 1 โมเดล)
#XFLD: Time Data deletion table column provider
provider=ผู้ให้บริการ
#XTIT: Time Data dependency dialog title
timeDataDependencyDialogTitle=ความสัมพันธ์
#XTIT: Create schema popup title
createSpaceSchemaUserDialogTitle=สร้างผู้ใช้สำหรับ Schema ของพื้นที่
#XFLD: Create schema button
createSchemaButton=สร้าง Open SQL Schema
#XFLD: Generate TimeData button
generateTimeDataButton=สร้างตารางและไดเมนชันเวลา
#XFLD: Show dependencies button
showDependenciesButton=แสดงความสัมพันธ์
#XFLD: tooltip for disabled buttons when user is not member of space
mustBeMemberOfSpaceForOperation=เมื่อต้องการทำการดำเนินการนี้ ผู้ใช้ของคุณต้องเป็นสมาชิกของพื้นที่
#XFLD: Create space schema user button
createSpaceSchemaUserButton=สร้างผู้ใช้สำหรับ Schema ของพื้นที่
#YMSE: API Schema users load error
loadSchemaUsersError=ไม่สามารถโหลดรายชื่อผู้ใช้ได้
#XTIT: API Schema-config popup title
schemaUserDetailsDialogTitle=รายละเอียดผู้ใช้สำหรับ Schema ของพื้นที่
#XMSG: API Schema user deletion popup text
deleteSpaceSchemaUserConfirmation=คุณแน่ใจหรือไม่ว่าต้องการลบผู้ใช้ที่เลือก?
#XMSG: API Schema user deletion confirmation text
userDeleteSuccess=ลบผู้ใช้แล้ว
#YMSE: API Schema user deletion error
userDeleteError=ไม่สามารถลบผู้ใช้ได้
#XFLD: User deleted
userDeleted=ผู้ใช้ถูกลบแล้ว
#XTIT: Remove user popup title
removeUserConfirmationTitle=คำเตือน
#XMSG: Remove user popup text
removeUserConfirmation=คุณแน่ใจหรือไม่ว่าต้องการย้ายผู้ใช้ออก? ผู้ใช้และบทบาทที่กำหนดขอบเขตที่กำหนดไว้จะถูกย้ายออกจากพื้นที่
#XMSG: Remove users popup text
removeUsersConfirmation=คุณแน่ใจหรือไม่ว่าต้องการย้ายผู้ใช้ออก? ผู้ใช้และบทบาทที่กำหนดขอบเขตที่กำหนดไว้จะถูกย้ายออกจากพื้นที่
#XBUT: Confirmation button for remove user dialog
removeUserConfirmationBtn=ย้ายออก
#YMSE: No data text for available roles
noDataAvailableRoles=พื้นที่ไม่ได้ถูกเพิ่มในบทบาทที่กำหนดขอบเขตใดๆ \n เพื่อให้สามารถเพิ่มผู้ใช้ในพื้นที่ได้ ต้องเพิ่มบทบาทที่กำหนดขอบเขตอย่างน้อยหนึ่งบทบาทก่อน
#YMSE: No data text for selected roles
noDataSelectedRoles=ไม่มีบทบาทที่กำหนดขอบเขตที่เลือก
#XTIT: Schema-config popup title
schemaDetailsDialogTitle=รายละเอียดการกำหนดรูปแบบ Open SQL Schema
#XFLD: Label for Read Audit Log
auditLogRead=เปิดใช้งานล็อกการตรวจสอบสำหรับการดำเนินการอ่าน
#XFLD: Label for Change Audit Log
auditLogChange=เปิดใช้งานล็อกการตรวจสอบสำหรับการดำเนินการเปลี่ยนแปลง
#XFLD: Label Audit Log Retention
auditLogRetention=เก็บล็อกเป็นเวลา
#XFLD: Label Audit Log Retention Unit
retentionUnit=วัน
#YMSE: Error when log retention time is set incorrectly
incorrectRetentionTime=ป้อนจำนวนเต็มระหว่าง {0} ถึง {1}
#XFLD: Label for Cross Space Schema Access
spaceSchemaAccess=ใช้ข้อมูล Schema ของพื้นที่
#XTIT: Confirm deactivation of space schema consumption dialog title
disableSpaceSchemaConsumptionDialogTitle=หยุดการใช้ข้อมูล Schema ของพื้นที่
#XTXT: Confirm deactivation of space schema consumption dialog text
disableSpaceSchemaConsumptionDialogText=Open SQL Schema นี้อาจใช้ข้อมูลของ Schema ของพื้นที่ ถ้าคุณหยุดการใช้ โมเดลที่ขึ้นกับข้อมูล Schema ของพื้นที่อาจใช้งานไม่ได้อีกต่อไป
#XBUT: Confirmation button for the stop consuming space schema data dialog
stopConsuming=หยุดการใช้
#XTXT: Tooltip for data lake icon
dataLakeIconTooltip=พื้นที่นี้ใช้เพื่อเข้าถึง Data Lake
#XFLD: Tooltip for data lake table entry
dataLakeTableEntryTooltip=เปิดใช้งาน Data Lake
#XFLD: used In-Memory linit reached tag
usedRamLimitReachedLabel=ถึงขีดจำกัดหน่วยความจำแล้ว
#XFLD: Space storage Limit reached tag
storageLimitReachedLabel=ถึงขีดจำกัดพื้นที่จัดเก็บแล้ว
#XFLD: Minimal Storage Limit Reached tag
minStorageLimitReachedLabel=ถึงขีดจำกัดพื้นที่จัดเก็บต่ำสุดแล้ว
#XFLD: Space ram tag
ramLimitReachedLabel=ถึงขีดจำกัดหน่วยความจำแล้ว
#XFLD: Minimal In-Memory Limit Reached tag
minRamLimitReachedLabel=ถึงขีดจำกัดหน่วยความจำต่ำสุดแล้ว
#XFLD: Popover text after click on space storage limit warning
storageLimitReachedText=คุณมีข้อมูลถึงขีดจำกัด {0} ของพื้นที่จัดเก็บในพื้นที่ที่กำหนดแล้ว กรุณากำหนดพื้นที่จัดเก็บเพิ่มเติมให้กับพื้นที่
#XFLD: System storage tag
systemStorageLimitReachedLabel=ถึงขีดจำกัดพื้นที่จัดเก็บในระบบแล้ว
#XMSG: Popover text after click on storage limit warning
systemStorageLimitReachedText=คุณมีข้อมูลถึงขีดจำกัด {0} ของพื้นที่จัดเก็บในระบบแล้ว คุณไม่สามารถกำหนดพื้นที่จัดเก็บให้กับพื้นที่ได้อีกในขณะนี้
#XMSG: Schema deletion warning
deleteSchemaConfirmation=การลบ Open SQL Schema นี้จะลบออบเจคที่จัดเก็บและความสัมพันธ์ที่ปรับปรุงทั้งหมดใน Schema แบบถาวรด้วย ต้องการดำเนินการต่อหรือไม่?
#XMSG: Success message after deleting a schema
schemaDeleteSuccess=ลบ Schema แล้ว
#YMSE: Error while deleting schema.
schemaDeleteError=ไม่สามารถลบ Schema ได้
#XMSG: Success message after update a schema
schemaUpdateSuccess=อัพเดท Schema แล้ว
#YMSE: Error while updating schema.
schemaUpdateError=ไม่สามารถอัพเดท Schema ได้
#XMSG: Hint when no password is shown in the dialog.
newPasswordHint=เราได้ให้รหัสผ่านสำหรับ Schema นี้แล้ว ถ้าคุณลืมรหัสผ่านหรือทำสูญหาย คุณสามารถขอรหัสผ่านใหม่ได้ อย่าลืมคัดลอกหรือเก็บบันทึกรหัสผ่านใหม่นี้
#XMSG: Hint when showing the password in the dialog.
savePasswordHint=กรุณาคัดลอกรหัสผ่าน คุณจะต้องใช้รหัสผ่านเพื่อกำหนดค่าการเชื่อมต่อกับ Schema นี้ ถ้าลืมรหัสผ่าน คุณสามารถเปิดไดอะลอกนี้เพื่อรีเซ็ตได้
#XFLD,50: Hint on entering schema name in creation dialog
schemaNameHint=ไม่สามารถเปลี่ยนแปลงชื่อนี้ได้หลังจากสร้าง Schema
#XFLD: Open SQL Schemas section sub headline
schemasSQL=Open SQL
#XFLD: Space schema section sub headline
schemasSpace=พื้นที่
#XFLD: HDI Container section header
HDIContainers=คอนเทนเนอร์ HDI
#XTXT: Add HDI Containers button tooltip
addHDIContainers=เพิ่มคอนเทนเนอร์ HDI
#XTXT: Remove HDI Containers button tooltip
removeHDIContainers=ย้ายคอนเทนเนอร์ HDI ออก
#XFLD: Enable HDI Containers feature button
enableHDIContainersButton=เปิดใช้งานการเข้าถึง
#YMSE: No data text for HDI Containers table
noDataHDIContainers=ไม่มีการเพิ่มคอนเทนเนอร์ HDI
#YMSE: No data text for Timedata section
noDataTimedata=ไม่มีการสร้างตารางเวลาและไดเมนชัน
#YMSE: No data text during runtime not available for Timedata section
runtimeNotAvailableNoDataTimedata=ไม่สามารถโหลดตารางเวลาและไดเมนชันได้เนื่องจากฐานข้อมูลรันไทม์ไม่พร้อมใช้งาน
#YMSE: No data text during runtime not available for HDI section
runtimeNotAvailableNoDataHDI=ไม่สามารถโหลดคอนเทนเนอร์ HDI ได้เนื่องจากฐานข้อมูลรันไทม์ไม่พร้อมใช้งาน
#YMSE: Error while fetching HDI Containers
fetchHDIContainersError=ไม่สามารถรับคอนเทนเนอร์ HDI ได้ กรุณาลองอีกครั้งภายหลัง
#XFLD Table column header for HDI Container names
HDIContainerName=ชื่อคอนเทนเนอร์ HDI
#XFLD: Enable HDI Containers feature dialog title
enableHDIContainersDialogTitle=เปิดใช้งานการเข้าถึง
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText1New=คุณสามารถเปิดใช้งาน SAP SQL Data Warehousing บน Tenant ของ SAP Datasphere ของคุณเพื่อแลกเปลี่ยนข้อมูลระหว่างคอนเทนเนอร์ HDI กับพื้นที่ SAP Datasphere ของคุณโดยไม่ต้องเคลื่อนย้ายข้อมูล
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText2=เมื่อต้องการดำเนินการดังกล่าว ให้เปิด Support Ticket โดยการคลิกปุ่มด้านล่าง
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText3New=เมื่อ Ticket ของคุณได้รับการดำเนินการแล้ว คุณต้องสร้างคอนเทนเนอร์ HDI ใหม่อย่างน้อยหนึ่งคอนเทนเนอร์ในฐานข้อมูลรันไทม์ของ SAP Datasphere จากนั้นปุ่ม 'เปิดใช้งานการเข้าถึง' จะถูกแทนที่โดยปุ่ม + ในเซกชัน 'คอนเทนเนอร์ HDI' สำหรับพื้นที่ทั้งหมดของ SAP Datasphere ของคุณ และคุณสามารถเพิ่มคอนเทนเนอร์ในพื้นที่ได้
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText41=ต้องการข้อมูลเพิ่มเติมหรือไม่? ไปที่ %%0 สำหรับข้อมูลโดยละเอียดเกี่ยวกับสิ่งที่ควรรวมไว้ใน Ticket กรุณาดู %%1
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkText=วิธีใช้ SAP
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkTextSapNote3057059=SAP Note 3057059
#XBUT: Open Ticket Button Text
openTicket=เปิด Ticket
#XBUT: Add Button Text
add=เพิ่ม
#XBUT: Next Button Text
next=ถัดไป
#XBUT: Edit Button Text
editUsers=แก้ไข
#XBUT: create user Button Text
createUser=สร้าง
#XBUT: Update user Button Text
updateUser=เลือก
#XTIT: Add HDI Containers Dialog Title
addHDIContainersDialogTitle=เพิ่มคอนเทนเนอร์ HDI ที่ไม่ได้กำหนด
#XFLD: No data text for add HDI container dialog
addHDIContainersDialogNoDataText=เราไม่พบคอนเทนเนอร์ที่ไม่ได้กำหนด \n คอนเทนเนอร์ที่คุณกำลังค้นหาอาจได้รับการกำหนดให้กับพื้นที่แล้ว
#YMSE: Error while loading assigned HDI Containers
loadAssignedHDIContainersError=ไม่สามารถโหลดคอนเทนเนอร์ HDI ที่กำหนด
#YMSE: Error while loading assigned HDI Containers
loadUnassignedHDIContainersError=ไม่สามารถโหลดคอนเทนเนอร์ HDI
#XMSG: Success message
succeededToAddHDIContainer=เพิ่มคอนเทนเนอร์ HDI แล้ว
#XMSG: Success message
succeededToAddHDIContainerPlural=เพิ่มคอนเทนเนอร์ HDI แล้ว
#XMSG: Success message
succeededToDeleteHDIContainer=ย้ายคอนเทนเนอร์ HDI ออกแล้ว
#XMSG: Success message
succeededToDeleteHDIContainerPlural=ย้ายคอนเทนเนอร์ HDI ออกแล้ว
#XFLD: Time data section sub headline
timeDataSection=ตารางและไดเมนชันเวลา
#XFLD: Schemas list entry read audit log enabled indicator
readIndicator=อ่าน
#XFLD: Schemas list entry change audit log enabled indicator
changeIndicator=การเปลี่ยนแปลง
#XFLD: Remote sources section sub headline
allconnections=การกำหนดการเชื่อมต่อ
#XFLD: Remote sources section sub headline
localconnections=การเชื่อมต่อภายใน
#XFLD: Remote sources section sub headline for open Connectors
openConnectors=SAP Open Connectors
#XFLD: User section sub headline
memberassignment=การกำหนดสมาชิก
#XFLD: User assignment section sub headline
userAssignment=การกำหนดผู้ใช้
#XFLD: User section Access dropdown Member
member=สมาชิก
#XFLD: User assignment section column name
user=ชื่อผู้ใช้
#XTXT: Selected role count
selectedRoleToolbarText=เลือกแล้ว: {0}
#XTIT: Space detail section connections title
detailsSectionConnections=การเชื่อมต่อ
#XTIT: Space detail section data access title
detailsSectionDataAccess=การเข้าถึง Schema
#XTIT: Space detail section time data title
detailsSectionGenerateData=ข้อมูลเวลา
#XTIT: Space detail section members title
detailsSectionUsers=สมาชิก
#XTIT: Space detail section Users title
detailsSectionUsersTitle=ผู้ใช้
#XTIT: Out of Storage
insufficientStoragePopoverTitle=พื้นที่จัดเก็บไม่เพียงพอ
#XTIT: Storage distribution
storageDistributionPopoverTitle=พื้นที่จัดเก็บในดิสก์ที่ใช้
#XTXT: Out of Storage popover text
insufficientStorageText=เมื่อต้องการสร้างพื้นที่ใหม่ กรุณาลดพื้นที่จัดเก็บที่กำหนดของพื้นที่อื่นหรือลบพื้นที่ที่คุณไม่ต้องการอีกต่อไป คุณสามารถเพิ่มพื้นที่จัดเก็บรวมของระบบได้โดยการเรียก 'จัดการแผน'
#XMSG: Space id length warning
spaceIdLengthWarning=เกินจำนวนอักขระสูงสุด {0}
#XMSG: Space name length warning
spaceNameLengthWarning=เกินจำนวนอักขระสูงสุด {0}
#XMSG: Space id reserved prefix warning
spaceIdnWarningReservedPrefix=กรุณาอย่าใช้คำนำหน้า {0} เพื่อหลีกเลี่ยงข้อขัดแย้งที่อาจเกิดขึ้น
#YMSE: Error while loading open SQL schemas
loadLocalSchemasError=ไม่สามารถโหลด Open SQL Schema ได้
#YMSE: Error while creating open SQL schema
createLocalSchemaError=ไม่สามารถสร้าง Open SQL Schema ได้
#YMSE: Error while loading all remote connections
loadRemoteConnectionsError=ไม่สามารถโหลดการเชื่อมต่อระยะไกลทั้งหมดได้
#YMSE: Error while loading space details
loadSpaceDetailsError=ไม่สามารถโหลดรายละเอียดพื้นที่
#YMSE: Error while deploying space details
deploySpaceDetailsError=ไม่สามารถปรับใช้พื้นที่ได้
#YMSE: Error while copying space details
copySpaceDetailsError=ไม่สามารถคัดลอกพื้นที่ได้
#YMSE: Error while loading storage data
loadStorageDataError=ไม่สามารถโหลดข้อมูลการจัดเก็บได้
#YMSE: Error while loading all users
loadAllUsersError=ไม่สามารถโหลดผู้ใช้ทั้งหมดได้
#YMSE: Failed to reset password
resetPasswordError=ไม่สามารถรีเซ็ตรหัสผ่านได้
#XMSG: Success message after updating space
resetPasswordSuccessMessage=กำหนดรหัสผ่านใหม่สำหรับ Schema แล้ว
#YMSE: DP Agent-name too long
DBAgentNameError=ชื่อของเอเจนต์ DP ยาวเกินไป
#YMSE: Schema-name not valid.
schemaNameError=ชื่อของ Schema ไม่ถูกต้อง
#YMSE: User name not valid.
UserNameError=ชื่อผู้ใช้ไม่ถูกต้อง
#XTIT: Consumption by Storage Type
consumptionStorageTypeText=การใช้ตามประเภทพื้นที่จัดเก็บ
#XTIT: Consumption by Schema
consumptionSchemaText=การใช้ตาม Schema
#XTIT: Overall Consumption by schema
overallConsumptionSchemaText=การใช้ตารางโดยรวมตาม Schema
#XTIT: Overall Consumption by Table Type
overallConsumptionTableTypeText=การใช้โดยรวมตามประเภทตาราง
#XTIT: Tables
tableDetailsText=รายละเอียดตาราง
#XTIT: Table Storage Consumption
tableStorageConsumptionText=การใช้พื้นที่จัดเก็บของตาราง
#XFLD: Table Type label
tableTypeLabel=ประเภทตาราง
#XFLD: Schema label
schemaLabel=Schema
#XFLD: reset table tooltip
resetTable=รีเซ็ตตาราง
#XFLD: In-Memory label in space monitor
inMemoryLabel=หน่วยความจำ
#XFLD: Disk label in space monitor
diskLabel=ดิสก์
#XFLD: Yes
yesLabel=ใช่
#XFLD: No
noLabel=ไม่ใช่
#XFLD: switch label: Want the data in this space to be consumable by default?
allowConsumptionByDefault=คุณต้องการให้ข้อมูลในพื้นที่นี้สามารถใช้ได้ตามค่าตั้งต้นหรือไม่?
#XFLD: Business Name
businessNameLabel=ชื่อทางธุรกิจ
#XFLD: Refresh
refresh=รีเฟรช
#XMSG: No filter results title
noFilterResultsTitle=ดูเหมือนว่าการกำหนดค่าฟิลเตอร์ของคุณไม่แสดงข้อมูลใดๆ
#XMSG: No filter results message
noFilterResultsMsg=ลองปรับการกำหนดค่าฟิลเตอร์ของคุณ และถ้ายังไม่เห็นข้อมูลใดๆ ให้สร้างตารางในตัวสร้างข้อมูล เมื่อตารางใช้พื้นที่จัดเก็บ คุณจะสามารถติดตามตรวจสอบได้ที่นี่
#XMSG: Space monitoring Hana state red title
spaceMonitorHanaRedTitle=ฐานข้อมูลรันไทม์ไม่พร้อมใช้งาน
#XMSG: Space monitoring Hana state red message
spaceMonitorHanaRedMsg=เนื่องจากฐานข้อมูลรันไทม์ไม่พร้อมใช้งาน ฟีเจอร์บางอย่างจึงถูกปิดใช้งานและเราไม่สามารถแสดงข้อมูลใดๆ ในหน้านี้ได้
#YMSE: Error while creating space schema user
createSpaceSchemaUserError=ไม่สามารถสร้างผู้ใช้ Schema ของพื้นที่ได้
#YMSE: Error User name already exists
userAlreadyExistsError=ชื่อผู้ใช้มีอยู่แล้ว
#YMSE: Error Authentication failed
authenticationFailedError=การรับรองความถูกต้องล้มเหลว
#XMSG: Message strip message when a schema user is locked
userLockedMessageStrip=ผู้ใช้ถูกล็อคเนื่องจากมีการเข้าสู่ระบบที่ล้มเหลวหลายครั้งเกินไป กรุณาขอรหัสผ่านใหม่เพื่อปลดล็อคผู้ใช้
#XMSG: Success message after reset and unlock schema user
resetUnlockPasswordSuccessMessage=กำหนดรหัสผ่านใหม่และปลดล็อคผู้ใช้แล้ว
#XMSG: user is locked message
userLockedMessage=ผู้ใช้ถูกล็อค
#XCOL: Users table-view column Role
spaceRole=บทบาท
#XCOL: Users table-view column Scoped Role
spaceScopedRole=บทบาทที่กำหนดขอบเขต
#XCOL: Users table-view column Space Admin
spaceAdmin=ผู้ดูแลพื้นที่
#XFLD: User section dropdown value Viewer
viewer=ตัวแสดง
#XFLD: User section dropdown value Modeler
modeler=ตัวสร้างโมเดล
#XFLD: User section dropdown value Data Integrator
dataIntegrator=Data Integrator
#XFLD: User section dropdown value Space Administrator
spaceAdministrator=ผู้ดูแลพื้นที่
#XMSG: Success message -  Space Role updated successfully.
updateUserSpaceRoleSuccess=อัพเดทบทบาทของพื้นที่แล้ว
#XMSG: Error message - Failed to update Space Role.
updateUserSpaceRoleError=อัพเดทบทบาทของพื้นที่ไม่สำเร็จ
#XFLD:
databaseUserNameSuffix=คำต่อท้ายชื่อผู้ใช้ฐานข้อมูล
#XTXT: Space Schema password text
spaceSchemaPasswordText=เมื่อต้องการกำหนดค่าการเชื่อมต่อกับ Schema นี้ กรุณาคัดลอกรหัสผ่านของคุณ กรณีที่ลืมรหัสผ่าน คุณสามารถขอรหัสผ่านใหม่ได้เสมอ
#XFLD: Cloud Platform label in space schema dialog
cloudPlatformLabel=Cloud Platform
#XTXT: Cloud Platform text in space schema dialog
cloudPlatformText=เมื่อต้องการกำหนดค่าการเข้าถึงผ่านผู้ใช้นี้ ให้เปิดใช้งานการใช้และคัดลอกข้อมูลประจำตัว กรณีที่คัดลอกได้เฉพาะข้อมูลประจำตัวแบบไม่มีรหัสผ่าน กรุณาตรวจสอบให้แน่ใจว่าได้เพิ่มรหัสผ่านในภายหลังแล้ว
#XFLD: enable consumption in CloudPlatform label in space schema dialog
enableConsumptionInCloudPlatformLabel=เปิดใช้งานการใช้ใน Cloud Platform
#XFLD: Credentials for User-Provided Service label in space schema dialog
cloudPlatformCredentialsLabel=ข้อมูลประจำตัวสำหรับบริการที่ผู้ใช้ระบุ:
#XFLD: Credentials for User-Provided Service (Without Password) label in space schema dialog
cloudPlatformCredentialsWithoutPasswordLabel=ข้อมูลประจำตัวสำหรับบริการที่ผู้ใช้ระบุ (ไม่มีรหัสผ่าน):
#XFLD: Copy Credentials label in space schema dialog
copyCredentialsLabel=คัดลอกข้อมูลประจำตัวแบบไม่มีรหัสผ่าน
#XFLD: Copy Credentials label in space schema dialog
copyFullCredentialsLabel=คัดลอกข้อมูลประจำตัวแบบเต็ม
#XFLD: Copy Password label in space schema dialog
copyPasswordLabel=คัดลอกรหัสผ่าน
#XMSG: Credentials copied to clipboard
credentialsCopiedMessage=คัดลอกข้อมูลประจำตัวไปยังคลิปบอร์ดแล้ว
#XMSG: Password copied to clipboard
passwordCopiedMessage=คัดลอกรหัสผ่านไปยังคลิปบอร์ดแล้ว
#XMSG: create database user dialog title
createDatabaseUserDialogTitle=สร้างผู้ใช้ฐานข้อมูล
#XMSG: Database Users section title
databaseUsers=ผู้ใช้ฐานข้อมูล
#XMSG: Database User Details dialog title
databaseUserDetailsDialogTitle=รายละเอียดผู้ใช้ฐานข้อมูล
#XFLD: database user read audit log
databaseUserAuditLogRead=เปิดใช้งาน 'ล็อกการตรวจสอบสำหรับการดำเนินการอ่าน' และ 'เก็บล็อกเป็นเวลา'
#XFLD: database user change audit log
databaseUserAuditLogChange=เปิดใช้งาน 'ล็อกการตรวจสอบสำหรับการดำเนินการเปลี่ยนแปลง' และ 'เก็บล็อกเป็นเวลา'
#XMSG: Cloud Platform Access
cloudPlatformAccess=การเข้าถึง Cloud Platform
#XFLD: Enable Consumption in Cloud Platform hint
enableConsumptionCloudPlatformHint=กำหนดค่าการเข้าถึงคอนเทนเนอร์โครงสร้างพื้นฐานการปรับใช้ (HDI) ของ HANA ผ่านผู้ใช้ฐานข้อมูลนี้ โดยต้องเปิดการจัดทำโมเดล SQL เพื่อเชื่อมต่อไปยังคอนเทนเนอร์ HDI ของคุณ
#XFLD: Enable HDI Consumption
enableConsumptionCloudPlatform=เปิดใช้งานการใช้ HDI
#XFLD: Enable Consumption hint
enableConsumptionHint=คุณต้องการให้ข้อมูลในพื้นที่ของคุณสามารถใช้ได้โดยเครื่องมือหรือแอพอื่นๆ หรือไม่?
#XFLD: Enable Consumption
enableConsumption=เปิดใช้งานการใช้ SQL
#XFLD: Enable Modeling
enableModeling=เปิดใช้งานการจัดทำโมเดล SQL
#XMSG: Privileges for Data Modeling
privilegesModeling=การนำข้อมูลเข้า
#XMSG: Privileges for Data Consumption
privilegesConsumption=การใช้ข้อมูลสำหรับเครื่องมือภายนอก
#XFLD: SQL Modeling
sqlModeling=การจัดทำโมเดล SQL
#XFLD: SQL Consumption
sqlConsumption=การใช้ SQL
#XFLD: enabled
enabled=เปิดใช้งาน
#XFLD: disabled
disabled=ปิดใช้งาน
#XFLD: Edit Privileges
editPrivileges=แก้ไขสิทธิพิเศษ
#XFLD: Open Database Explorer
openDBX=เปิด Explorer ของฐานข้อมูล
#XFLD: create database user hint
databaseCreateHint=กรุณาจำไว้ว่าชื่อผู้ใช้จะไม่สามารถเปลี่ยนแปลงได้อีกหลังจากเก็บบันทึก
#XFLD: Internal Schema Name
internalSchemaName=ชื่อ Schema ภายใน
#YMSE: Failed to load database users
loadDatabaseUserError=การโหลดผู้ใช้ฐานข้อมูลล้มเหลว
#YMSE: Failed to delete database user
deleteDatabaseUsersError=การลบผู้ใช้ฐานข้อมูลล้มเหลว
#XMSG: Success message after deleting database user
deleteDatabaseUserSuccess=ลบผู้ใช้ฐานข้อมูลแล้ว
#XMSG: Success message after deleting database users
deleteDatabaseUsersSuccess=ลบผู้ใช้ฐานข้อมูลแล้ว
#XMSG: Success message after creating database user
createDatabaseUserSuccessMessage=สร้างผู้ใช้ฐานข้อมูลแล้ว
#YMSE: Failed to create database user
createDatabaseUserError=การสร้างผู้ใช้ฐานข้อมูลล้มเหลว
#XMSG: Success message after updating database user
updateDatabaseUserSuccessMessage=อัพเดทผู้ใช้ฐานข้อมูลแล้ว
#YMSE: Failed to update database user
updateDatabaseUserError=การอัพเดทผู้ใช้ฐานข้อมูลล้มเหลว
#XFLD: HDI Consumption
hdiConsumption=การใช้ HDI
#XTIT: Space detail section Database Access title
detailsSectionDatabaseAccess=การเข้าถึงฐานข้อมูล
#XTXT: Database users consumption hint
databaseUsersConsumptionHint=ทำให้ข้อมูลในพื้นที่เป็นแบบสามารถใช้ได้ตามค่าตั้งต้น โมเดลในตัวสร้างจะอนุญาตให้แอพหรือเครื่องมืออื่นใช้ข้อมูลนี้ได้โดยอัตโนมัติ
#XFLD: Default Consumption of Space Data:
defaultConsumptionOfSpaceData=การใช้ข้อมูลพื้นที่ตั้งต้น:
#XFLD: Database User Name
databaseUserName=ชื่อผู้ใช้ฐานข้อมูล
#XMSG: Database User creation validation error message
databaseUserValidationError=ดูเหมือนว่าบางฟิลด์ไม่ถูกต้อง กรุณาตรวจสอบฟิลด์ที่ต้องป้อนข้อมูลเพื่อดำเนินการต่อ
#XMSG: SQL modeling can’t be enabled since this user has been migrated.
migratedDatabaseUserHint=ไม่สามารถเปิดใช้งานการนำข้อมูลเข้าได้เนื่องจากผู้ใช้นี้ถูกโอนย้ายแล้ว
#XBUT: Remove Button Text
remove=ย้ายออก
#XBUT: Remove Spaces Button Text
removeSpaces=ย้ายพื้นที่ออก
#XBUT: Remove Objects Button Text
removeObjects=ย้ายออบเจคออก
#XMSG: No members have been added yet.
noMembersAssigned=ยังไม่ได้เพิ่มสมาชิก
#XMSG: No users have been added yet.
noUsersAssigned=ยังไม่ได้เพิ่มผู้ใช้
#XMSG: No database users have been created or your filter is not showing any data.
noDatabaseUsers=ไม่ได้สร้างผู้ใช้ฐานข้อมูล หรือฟิลเตอร์ของคุณไม่แสดงข้อมูลใดๆ
#XMSG: Please enter a user name.
noDatabaseUsername=กรุณาป้อนชื่อผู้ใช้
#XMSG: The user name is too long. Please use a shorter one.
databaseUsernameTooLong=ชื่อผู้ใช้ยาวเกินไป กรุณาใช้ชื่อที่สั้นลง
#XMSG: No privileges have been enabled and this database user will have limited functionality. Do you still want to continue?
noPrivilegesConfirmationMessage=ไม่ได้เปิดใช้งานสิทธิพิเศษและผู้ใช้ฐานข้อมูลนี้จะมีฟังก์ชันที่จำกัด คุณยังต้องการดำเนินการต่อหรือไม่?
#XMSG: To enable audit logs for change operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogChangeMessage=เมื่อต้องการเปิดใช้งานล็อกการตรวจสอบสำหรับการดำเนินการเปลี่ยนแปลง จะต้องเปิดใช้งานการนำเข้ามูลเข้าด้วย คุณต้องการดำเนินการนี้หรือไม่?
#XMSG: To enable audit logs for read operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogReadMessage=เมื่อต้องการเปิดใช้งานล็อกการตรวจสอบสำหรับการดำเนินการอ่าน จะต้องเปิดใช้งานการนำเข้ามูลเข้าด้วย คุณต้องการดำเนินการนี้หรือไม่?
#XMSG: To enable HDI consumption data ingestion and data consumption needs to be enabled. Do you want to enable all?
enableHDIConsumptionMessage=เมื่อต้องการเปิดใช้งานการใช้ HDI จะต้องเปิดใช้งานการนำข้อมูลเข้าและการใช้ข้อมูลด้วย คุณต้องการดำเนินการนี้หรือไม่?
#XMSG:
databaseUserPasswordText=เมื่อต้องการกำหนดค่าการเชื่อมต่อกับผู้ใช้ฐานข้อมูลนี้ กรุณาคัดลอกรหัสผ่านของคุณ กรณีที่ลืมรหัสผ่าน คุณสามารถขอรหัสผ่านใหม่ได้เสมอ
#XTIT: Space detail section members title
detailsSectionMembers=สมาชิก
#XMSG: New password set
newPasswordSet=กำหนดรหัสผ่านใหม่แล้ว
#XFLD: Data Ingestion
dataIngestion=การนำข้อมูลเข้า
#XFLD: Data Consumption
dataConsumption=การใช้ข้อมูล
#XFLD: Privileges
privileges=สิทธิพิเศษ
#XFLD: Enable Data ingestion
enableDataIngestion=เปิดใช้งานการนำข้อมูลเข้า
#XMSG: Log the read and change operations for data ingestion.
databaseUserAuditHint=บันทึกล็อกการดำเนินการอ่านและเปลี่ยนแปลงสำหรับการนำข้อมูลเข้า
#XMSG: Make your space data available in your HDI containers.
databaseUserHDIHint=ทำให้ข้อมูลพื้นที่พร้อมใช้งานในคอนเทนเนอร์ HDI
#XFLD: Enable Data consumption
enableDataConsumption=เปิดใช้งานการใช้ข้อมูล
#XMSG: Allow other apps or tools to consume your space data.
enableDataConsumptionHint=อนุญาตให้แอพหรือเครื่องมืออื่นใช้ข้อมูลในพื้นที่ของคุณได้
#XMSG: To set up access via this schema user copy the credentials to your user-provided service. In case you can only copy the credentials without a password make sure to add the password later.
databaseUserHDIConsumptionHint=เมื่อต้องการกำหนดค่าการเข้าถึงผ่านผู้ใช้ฐานข้อมูลนี้ ให้คัดลอกข้อมูลประจำตัวไปยังบริการที่ผู้ใช้ระบุ กรณีที่คัดลอกได้เฉพาะข้อมูลประจำตัวแบบไม่มีรหัสผ่าน กรุณาตรวจสอบให้แน่ใจว่าได้เพิ่มรหัสผ่านในภายหลังแล้ว
#XFLD: Data Flow Runtime Capacity label in space overview
dataFlowRuntimeCapacity=ความสามารถรันไทม์ของผังข้อมูล ({0}:{1} ชั่วโมงจาก {2} ชั่วโมง)
#XFLD: Could not load Data Flow Runtime Capacity
dataFlowRuntimeCapacityError=ไม่สามารถโหลดความสามารถรันไทม์ของผังข้อมูล
#XMSG: User can grant data consumption to other users.
databaseUserConsumptionWithGrantOptionHint=ผู้ใช้สามารถอนุญาตให้ผู้ใช้อื่นใช้ข้อมูลได้
#XFLD: Enable data consumption with grant option
enableConsumptionWithGrantOption=เปิดใช้งานการใช้ข้อมูลด้วยตัวเลือกการอนุญาต
#XMSG: To enable data consumption with grant option data consumption needs to be enabled. Do you want to enable both?
enableConsumptionWithGrantMessage=เมื่อต้องการเปิดใช้งานการใช้ข้อมูลด้วยตัวเลือกการอนุญาต จะต้องเปิดใช้งานการใช้ข้อมูลด้วย คุณต้องการเปิดใช้งานทั้งคู่หรือไม่?
#XFLD: Enable Automated Predictive Library (APL) and Predictive Analysis Library (PAL)
allowUsingScriptServerFunctionality=เปิดใช้งาน Automated Predictive Library (APL) และ Predictive Analysis Library (PAL)
#XMSG: User can use SAP HANA Cloud embedded machine learning functions.
allowUsingScriptServerFunctionalityHint=ผู้ใช้สามารถใช้ฟังก์ชันการเรียนรู้ของเครื่องแบบฝังตัวใน SAP HANA Cloud
#XFLD: Password Policy
passwordPolicy=นโยบายรหัสผ่าน
#XMSG: Password Policy
passwordPolicyHint=เปิดหรือปิดใช้งานนโยบายรหัสผ่านที่กำหนดรูปแบบไว้ที่นี่
#XFLD: Enable Password Policy
enablePasswordPolicy=เปิดใช้งานนโยบายรหัสผ่าน
#XMSG: Read Access to the Space Schema
readAccessTitle=การเข้าถึงการอ่าน Schema ของพื้นที่
#XMSG: read access hint
readAccessHint=อนุญาตให้ผู้ใช้ฐานข้อมูลเชื่อมต่อเครื่องมือภายนอกกับ Schema ของพื้นที่และมุมมองการอ่านที่แสดงสำหรับการใช้
#XFLD: Space Schema
spaceSchema=Schema ของพื้นที่
#XFLD: Enable Read Access (SQL)
enableReadAccess=เปิดใช้งานการเข้าถึงการอ่าน (SQL)
#XMSG: Allow the user to grant read access to other users.
withGrantOptionHint=อนุญาตให้ผู้ใช้ให้สิทธิการเข้าถึงการอ่านแก่ผู้ใช้อื่น
#XFLD: With Grant Option
withGrantOption=มีตัวเลือกการอนุญาต
#XMSG: Make your space data available in your HDI containers.
enableHDIConsumptionHint=ทำให้ข้อมูลพื้นที่พร้อมใช้งานในคอนเทนเนอร์ HDI
#XFLD: Enable HDI Consumption
enableHDIConsumption=เปิดใช้งานการใช้ HDI
#XMSG: Write Access to the User’s Open SQL Schema
writeAccessTitle=การเข้าถึงการเขียน Open SQL Schema ของผู้ใช้
#XMSG: write access hint
writeAccessHint=อนุญาตให้ผู้ใช้ฐานข้อมูลเชื่อมต่อเครื่องมือภายนอกกับ Open SQL Schema ของผู้ใช้เพื่อสร้างเอนทิตี้ข้อมูลและนำเข้าข้อมูลสำหรับการใช้ในพื้นที่
#XFLD: Open SQL Schema
openSQLSchema=Open SQL Schema
#XFLD: Enable Write Access (SQL DDL & DML)
enableWriteAccess=เปิดใช้งานการเข้าถึงการเขียน (SQL, DDL และ DML)
#XMSG: audit hint
auditHint=บันทึกล็อกการดำเนินการอ่านและเปลี่ยนแปลงใน Open SQL Schema
#XMSG: data consumption hint
dataConsumptionHint=แสดงมุมมองใหม่ทั้งหมดในพื้นที่ตามค่าตั้งต้นสำหรับการใช้ ตัวสร้างโมเดลสามารถแทนที่การกำหนดค่านี้สำหรับแต่ละมุมมองผ่านสวิตช์ “แสดงสำหรับการใช้” ในแผงด้านข้างของเอาท์พุทมุมมอง นอกจากนี้คุณยังสามารถเลือกรูปแบบที่จะแสดงมุมมองได้อีกด้วย
#XFLD: Expose for Consumption by Default
exposeDataConsumption=การแสดงสำหรับการใช้ตามค่าตั้งต้น
#XMSG: database users hint consumption hint
databaseUsersHint2New=สร้างผู้ใช้ฐานข้อมูลเพื่อเชื่อมต่อเครื่องมือภายนอกกับ SAP Datasphere กำหนดค่าสิทธิพิเศษเพื่ออนุญาตให้ผู้ใช้อ่านข้อมูลพื้นที่และสร้างเอนทิตี้ข้อมูล (DDL) และนำเข้าข้อมูล (DML) สำหรับการใช้ในพื้นที่
#XFLD: Read
read=อ่าน
#XFLD: Read (HDI)
readHDI=อ่าน (HDI)
#XFLD: Write
write=เขียน
#XMSG: HDI Containers Hint
HDIContainersHint2=เปิดใช้งานการเข้าถึงคอนเทนเนอร์โครงสร้างพื้นฐานการปรับใช้ (HDI) ของ SAP HANA ในพื้นที่ของคุณ ตัวสร้างโมเดลสามารถใช้อาร์ทิแฟกต์ HDI เป็นแหล่งข้อมูลสำหรับมุมมอง และไคลเอนท์ HDI สามารถเข้าถึงข้อมูลพื้นที่ของคุณได้
#XMSG: Open info dialog
openDatabaseUserInfoDialog=เปิดไดอะลอกข้อมูล
#XMSG: Database user is locked. Open info dialog to unlock
openLockedDatabaseUserInfoDialog=ผู้ใช้ฐานข้อมูลถูกล็อค กรุณาเปิดไดอะลอกเพื่อปลดล็อค
#XFLD: Table
table=ตาราง
#XFLD: Space section partner configuration title
partnerConnectionConfigurationSectionTitle=การเชื่อมต่อคู่ค้า
#XFLD: Space section partner configuration subtitle
partnerConnectionConfigurationSectionSubTitle=การกำหนดรูปแบบการเชื่อมต่อคู่ค้า
#XFLD: Space section partner configuration hint
partnerConnectionConfigurationSectionHint=กำหนด Tile การเชื่อมต่อคู่ค้าของคุณเองโดยการเพิ่ม URL ของ iFrame และไอคอนของคุณ การกำหนดรูปแบบนี้พร้อมใช้งานสำหรับ Tenant นี้เท่านั้น
#XFLD: Table Name Field
partnerConnectionConfigurationName=ชื่อ Tile
#XFLD: Table iFrame URL field
partnerConnectionConfigurationiFrameURL=URL ของ iFrame
#XFLD: Table iFrame Post Message Origin field
partnerConnectionConfigurationiFramePostMessageOrigin=ที่มาของข้อความโพสต์ iFrame
#XFLD: Table icon/image name
partnerConnectionConfigurationIcon=ไอคอน
#XMSG: Table no result found
partnerConnectionConfigurationNotfound=ไม่พบการกำหนดรูปแบบการเชื่อมต่อคู่ค้า
#XMSG: Partner Connection Confgurations are not available
partnerConnectionConfigurationNotAvailable=ไม่สามารถแสดงการกำหนดรูปแบบการเชื่อมต่อคู่ค้าได้เมื่อฐานข้อมูลรันไทม์ไม่พร้อมใช้งาน
#XFLD: Dialog Create Partner Connection Configuration
partnerConnectionConfigurationDialogTitleAdd=สร้างการกำหนดรูปแบบการเชื่อมต่อคู่ค้า
#XFLD: Dialog Create Partner Connection Configuration Upload Icon
partnerConnectionConfigurationButtonUloadPartnerIcon=อัพโหลดไอคอน
#XFLD: Dialog Create Partner Connection Configuration Select
partnerConnectionConfigurationSelectIcon=เลือก (ขนาดสูงสุด 200KB)
#XFLD: Dialog Create Partner Connection Configuration Enter a name
partnerConnectionConfigurationNamePlaceholder=ตัวอย่าง Tile คู่ค้า
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL
partnerConnectionConfigurationiFrameURLPlaceholder=http://www.example.com
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL Check
partnerConnectionConfigurationiFramePostMessageOriginPlaceholder=.example.com
#XFLD: Dialog Create Partner Connection Configuration Browse
partnerConnectionConfigurationBrowseButton=บราวซ์
#XFLD: Create Partner Connection Configuration Msg
partnerConnectionConfigurationUploadMsg=สร้างการกำหนดรูปแบบการเชื่อมต่อคู่ค้าได้สำเร็จ
#XMSG: Error while deleting Partner Connection configuration(s)
partnerConnectionConfigurationDeleteError=มีข้อผิดพลาดเกิดขึ้นขณะลบการกำหนดรูปแบบการเชื่อมต่อคู่ค้า
#XMSG: Partner Connection Configuration has been successfully deleted
partnerConnectionConfigurationDeleteSuccess=ลบการกำหนดรูปแบบการเชื่อมต่อคู่ค้าได้สำเร็จ
#XMSG: Error while retrieving Partner Connection Configurations
partnerConnectionConfigurationLoadError=มีข้อผิดพลาดเกิดขึ้นขณะดึงข้อมูลการกำหนดรูปแบบการเชื่อมต่อคู่ค้า
#XMSG: Error fileSizeLimitExceeded
partnerConnectionConfigurationfileSizeLimitExceeded=ไม่สามารถอัพโหลดไฟล์เนื่องจากเกินขนาดสูงสุด 200KB
#XBUT: Create Partner Connection Configuration
partnerConnectionConfigurationButtonCreate=สร้างการกำหนดรูปแบบการเชื่อมต่อคู่ค้า
#XBUT: Delete Partner Connection Configuration
partnerConnectionConfigurationButtonDelete=ลบการกำหนดรูปแบบการเชื่อมต่อคู่ค้า
#XMSG: Partner Tile Creation Failure
upsertPartnerTileFailed=ไม่สามารถสร้าง Tile คู่ค้าได้
#XMSG: Partner Tile Deletion Failure
deletePartnerTileFailed=ไม่สามารถลบ Tile คู่ค้าได้
#XMSG: HANA cloud connector settings reset failure
resetHANASCCSettingsFailed=การรีเซ็ตการกำหนดค่าตัวเชื่อมต่อ SAP HANA Cloud ของลูกค้าล้มเหลว
#XFLD: Workload Class
workloadClass=คลาสปริมาณงาน
#XFLD: Workload Management
workloadManagement=การจัดการปริมาณงาน
#XFLD: Priority
workloadClassPriority=ลำดับความสำคัญ
#XMSG:
workloadManagementPriorityHint=คุณสามารถระบุการจัดลำดับความสำคัญของพื้นที่นี้ได้เมื่อสืบค้นฐานข้อมูล ป้อนค่าตั้งแต่ 1 (ลำดับความสำคัญต่ำสุด) ถึง 8 (ลำดับความสำคัญสูงสุด) ในสถานการณ์ที่พื้นที่แข่งขันกันเพื่อแย่ง Thread ที่มีอยู่ พื้นที่ที่มีลำดับความสำคัญสูงกว่าจะถูกดำเนินการก่อนพื้นที่ที่มีลำดับความสำคัญต่ำกว่า
#XMSG:
workloadClassPriorityHint=คุณสามารถระบุลำดับความสำคัญของพื้นที่ได้ตั้งแต่ 0 (ต่ำสุด) ถึง 8 (สูงสุด) คำสั่งของพื้นที่ที่มีลำดับความสำคัญสูงจะถูกดำเนินการก่อนคำสั่งของพื้นที่อื่นที่มีลำดับความสำคัญต่ำกว่า โดยลำดับความสำคัญตั้งต้นคือ 5 ทั้งนี้ค่า 9 จะถูกสงวนไว้สำหรับการดำเนินการของระบบจึงใช้งานไม่ได้สำหรับพื้นที่
#XFLD: Statement Limits
workloadclassStatementLimits=ขีดจำกัดของคำสั่ง
#XFLD: Workload Configuration
workloadConfiguration=การกำหนดรูปแบบปริมาณงาน
#XMSG:
workloadClassStatementLimitsHint=คุณสามารถระบุจำนวนสูงสุด (หรือเปอร์เซ็นต์) ของ Thread และ GB ของหน่วยความจำที่คำสั่งดำเนินการพร้อมกันในพื้นที่ซึ่งสามารถใช้ได้ คุณสามารถป้อนค่าหรือเปอร์เซ็นต์ระหว่าง 0 (ไม่มีขีดจำกัด) รวมถึงหน่วยความจำและ Thread ทั้งหมดที่มีอยู่ใน Tenant \n\n หากคุณระบุขีดจำกัดของ Thread กรุณารับทราบว่าสามารถทำให้ประสิทธิภาพลดลง \n\nหากคุณระบุขีดจำกัดของหน่วยความจำ คำสั่งที่ถึงขีดจำกัดของหน่วยความจำจะไม่ถูกดำเนินการ
#XMSG:
workloadClassStatementLimitsDescription=การกำหนดรูปแบบตั้งต้นจะกำหนดขีดจำกัดทรัพยากรให้พอเหมาะ ขณะเดียวกันก็ป้องกันไม่ให้พื้นที่ใดพื้นที่หนึ่งทำให้ระบบโอเวอร์โหลด
#XMSG:
workloadClassStatementLimitCustomDescription=คุณสามารถกำหนดขีดจำกัดของ Thread และหน่วยความจำรวมสูงสุดซึ่งคำสั่งที่ดำเนินการพร้อมกันในพื้นที่ดังกล่าวสามารถใช้ได้
#XMSG:
totalStatementThreadLimitHelpText=การกำหนดขีดจำกัดของ Thread ต่ำเกินไปอาจส่งผลต่อประสิทธิภาพของคำสั่ง ในขณะที่ค่าที่สูงเกินไปหรือ 0 ก็อาจทำให้พื้นที่สามารถใช้ Thread ทั้งหมดที่มีอยู่ของระบบได้
#XMSG:
totalStatementMemoryLimitHelpText=การกำหนดขีดจำกัดของหน่วยความจำต่ำเกินไปอาจทำให้เกิดปัญหาหน่วยความจำไม่เพียงพอ ในขณะที่ค่าที่สูงเกินไปหรือ 0 ก็อาจทำให้พื้นที่สามารถใช้หน่วยความจำทั้งหมดที่มีอยู่ของระบบได้
#XMSG:
totalStatementThreadLimitHelpTextRestricted=ป้อนเปอร์เซ็นต์ระหว่าง 1% ถึง 70% (หรือจำนวนเทียบเท่า) สำหรับจำนวนรวมของ Thread ที่มีอยู่ใน Tenant ของคุณ การกำหนดค่าขีดจำกัดของ Thread ต่ำเกินไปอาจส่งผลต่อประสิทธิภาพของคำสั่ง ในขณะที่ค่าที่สูงเกินไปอาจมีผลต่อประสิทธิภาพของคำสั่งในพื้นที่อื่น
#XMSG:
totalStatementThreadLimitHelpTextDynamic=ป้อนเปอร์เซ็นต์ระหว่าง 1% ถึง {0}% (หรือจำนวนเทียบเท่า) สำหรับจำนวนรวมของ Thread ที่มีอยู่ใน Tenant ของคุณ การกำหนดค่าขีดจำกัดของ Thread ต่ำเกินไปอาจส่งผลต่อประสิทธิภาพของคำสั่ง ในขณะที่ค่าที่สูงเกินไปอาจมีผลต่อประสิทธิภาพของคำสั่งในพื้นที่อื่น
#XMSG:
totalStatementMemoryLimitHelpTextNew=ป้อนค่าหรือเปอร์เซ็นต์ระหว่าง 0 (ไม่มีขีดจำกัด) และยอดรวมของ หน่วยความจำที่มีอยู่ใน Tenant ของคุณ การกำหนดค่าขีดจำกัดของหน่วยความจำต่ำเกินไปอาจส่งผลต่อประสิทธิภาพของคำสั่ง ในขณะที่ค่าที่สูงเกินไปอาจมีผลต่อประสิทธิภาพของคำสั่งในพื้นที่อื่น
#XFLD: Total Statement Thread Limit
totalStatementThreadLimit=ขีดจำกัดของ Thread คำสั่งทั้งหมด
#XFLD: Segmented button label
workloadclassPercentage=%
#XFLD: Segmented button label
workloadclassGigaByte=GB
#XFLD, 80: Segmented button label
workloadclassThreads=Thread
#XFLD: Total Statement Memory Limit
totalStatementMemoryLimit=ขีดจำกัดของหน่วยความจำคำสั่งทั้งหมด
#YMSE: Error while loading customer hana info
loadCustomerHanaInfoError=ไม่สามารถโหลดข้อมูล SAP HANA ของลูกค้า
#XMSG:
minimumLimitReached=ถึงขีดจำกัดต่ำสุด
#XMSG:
maximumLimitReached=ถึงขีดจำกัดสูงสุด
#XMSG: Name Taken for Technical Name
technical-name-taken=การเชื่อมต่อที่ใช้ชื่อทางเทคนิคที่คุณป้อนมีอยู่แล้ว กรุณาป้อนชื่ออื่น
#XMSG: Name Too long for Technical Name
technical-name-too-long=ชื่อทางเทคนิคที่คุณป้อนเกิน 40 อักขระ กรุณาป้อนชื่อที่มีอักขระน้อยลง
#XMSG: Technical name field empty
technical-name-field-empty=กรุณาป้อนชื่อทางเทคนิค
#XMSG: Generic error when user enters invalid characters for a field
field-invalid-characters=คุณสามารถใช้ได้เฉพาะตัวอักษร (a-z), ตัวเลข (0-9) และเครื่องหมายขีดล่าง (_) สำหรับชื่อของคุณ
#XMSG: Generic error when user enters invalid initial characters for a field
field-underscore-trim=ชื่อที่คุณป้อนต้องไม่ขึ้นต้นหรือลงท้ายด้วยเครื่องหมายขีดล่าง (_)
#XFLD: Checkbox label for Enable Statement Limits
enableStatementLimits=เปิดใช้งานขีดจำกัดคำสั่ง
#XFLD: Connection SubSection Settings Title in space details
connectionSettings=การกำหนดค่า
#XMSG: Connections tool hint in Space details section
connectionsToolHint=เมื่อต้องการสร้างหรือแก้ไขการเชื่อมต่อ ให้เปิดแอพ 'การเชื่อมต่อ' จากการเนวิเกตด้านข้างหรือคลิกที่นี่:
#XBUT: Connection list Open Connection Tool Button
button-connectiontool=ไปที่การเชื่อมต่อ
#XFLD: Not deployed label on space tile
notDeployedLabel=ยังไม่ได้ปรับใช้พื้นที่
#XFLD: Not deployed additional text on space tile
notDeployedText=กรุณาปรับใช้พื้นที่
#XFLD: Corrupt space label on space tile
corruptSpace=มีบางอย่างผิดปกติ
#XFLD: Corrupt space additional text on space tile
corruptSpaceHint=ลองปรับใช้ใหม่หรือติดต่อฝ่ายสนับสนุน
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=ข้อมูลล็อกการตรวจสอบ
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=ข้อมูลด้านการจัดการ
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=ข้อมูลอื่นๆ
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=ข้อมูลในพื้นที่
#XMSG: Unlock Space confirmation text
unlockSpaceConfirmation=คุณแน่ใจหรือไม่ว่าต้องการปลดล็อคพื้นที่?
#XMSG: Lock Space confirmation text
lockSpaceConfirmation=คุณแน่ใจหรือไม่ว่าต้องการล็อคพื้นที่?
#XFLD: Lock
lock=ล็อค
#XFLD: Unlock
unlock=ปลดล็อค
#XFLD: Locking
locking=การล็อค
#XMSG: Success message after locking space
lockSpaceSuccessMsg=พื้นที่ถูกล็อค
#XMSG: Success message after unlocking space
unlockSpaceSuccessMsg=พื้นที่ถูกปลดล็อค
#XMSG: Success message after locking spaces
lockSpacesSuccessMsg=พื้นที่ถูกล็อค
#XMSG: Success message after unlocking spaces
unlockSpacesSuccessMsg=พื้นที่ถูกปลดล็อค
#YMSE: Error while locking a space
lockSpaceError=ไม่สามารถล็อคพื้นที่ได้
#YMSE: Error while unlocking a space
unlockSpaceError=ไม่สามารถปลดล็อคพื้นที่ได้
#XTIT: popup title Warning
confirmationWarningTitle=คำเตือน
#XMSG: Message strip message when a space was locked manually
manualLockedMessage=พื้นที่นี้ถูกล็อคโดยผู้ใช้
#XMSG: Message strip message when a space was locked due to the amount of generated audit logs
auditLogLockedMessage=พื้นที่นี้ถูกล็อคโดยระบบเนื่องจากล็อกการตรวจสอบใช้ปริมาณ GB ในดิสก์ไปจำนวนมาก
#XMSG: Message strip message when a space was locked because the assigned quota was exceeded.
quotaLockedMessage=พื้นที่นี้ถูกล็อคโดยระบบเนื่องจากเกินการจัดสรรพื้นที่จัดเก็บในหน่วยความจำหรือดิสก์
#XMSG: Unlock Spaces confirmation text
unlockSpacesConfirmation=คุณแน่ใจหรือไม่ว่าต้องการปลดล็อคพื้นที่ที่เลือก?
#XMSG: Lock Spaces confirmation text
lockSpacesConfirmation=คุณแน่ใจหรือไม่ว่าต้องการล็อคพื้นที่ที่เลือก?
#XTIT: Scoped role editor dialog title
scopedRoleEditorDialogTitle=เอดิเตอร์บทบาทที่กำหนดขอบเขต
#XTIT: ECN Management title
ecnManagementTitle=การจัดการโหนดการคำนวณแบบยืดหยุ่นและพื้นที่
#XFLD: ECNs
ecns=โหนดการคำนวณแบบยืดหยุ่น
#XFLD: ECN phase Ready
ecnReady=พร้อม
#XFLD: ECN phase Running
ecnRunning=กำลังดำเนินการ
#XFLD: ECN phase Initial
ecnInitial=ไม่พร้อม
#XFLD: ECN phase Starting
ecnStarting=เริ่มต้น
#XFLD: ECN phase Starting Failed
ecnStartingFailed=การเริ่มต้นล้มเหลว
#XFLD: ECN phase Stopping
ecnStopping=กำลังหยุด
#XFLD: ECN phase Stopping Failed
ecnStoppingFailed=การหยุดล้มเหลว
#XBTN: Assign Button
assign=กำหนดพื้นที่
#XBTN: Start Header-Button
start=เริ่มต้น
#XBTN: Update Header-Button
repair=อัพเดท
#XBTN: Stop Header-Button
stop=หยุด
#XFLD: ECN hours remaining
ecnHoursRemaining=เหลืออีก 1000 ชั่วโมง
#XFLD: ECN block-hours remaining
ecnBlockHoursRemaining=เหลือจำนวนชั่วโมงที่บล็อคไว้อีก {0} ชั่วโมง
#XFLD: ECN block-hour remaining
ecnBlockHourRemaining=เหลือจำนวนชั่วโมงที่บล็อคไว้อีก {0} ชั่วโมง
#XTIT: ECN creation dialog title
ecnCreationDialogTitle=สร้างโหนดการคำนวณแบบยืดหยุ่น
#XTIT: ECN edit dialog title
ecnEditDialogTitle=แก้ไขโหนดการคำนวณแบบยืดหยุ่น
#XTIT: ECN deletion dialog title
ecnDeletionDialogTitle=ลบโหนดการคำนวณแบบยืดหยุ่น
#XTIT: ECN Space assignment dialog title
ecnSpaceAssignmentDialogTitle=กำหนดพื้นที่
#XFLD: ECN ID
ECNIDLabel=โหนดการคำนวณแบบยืดหยุ่น
#XTXT: Selected toolbar text
selectedToolbarText=เลือกแล้ว: {0}
#XTIT: Elastic Compute Nodes
ECNslong=โหนดการคำนวณแบบยืดหยุ่น
#XTIT: Object assignment - Table header: Objects available
objectsAvailable=จำนวนออบเจค
#XTIT: Object assignment - Dialog header text
selectObjects=เลือกพื้นที่และออบเจคที่คุณต้องการกำหนดให้กับโหนดการคำนวณแบบยืดหยุ่นของคุณดังนี้
#XTIT: Object assignment - Table header title: Objects
objects=ออบเจค
#XTIT: Object assignment - Table header: Type
type=ประเภท
#XMSG: Message strip delete database user audit warning
deleteDatabaseUserWarningMessageStrip=กรุณาจำไว้ว่าการลบผู้ใช้ฐานข้อมูลจะส่งผลให้มีการลบรายการล็อกการตรวจสอบที่สร้างขึ้นทั้งหมด หากคุณต้องการเก็บล็อกการตรวจสอบไว้ ให้พิจารณาเอ็กซ์ปอร์ตล็อกดังกล่าวก่อนที่คุณจะลบผู้ใช้ฐานข้อมูล
#XMSG: Message strip delete HDI container audit logs impact
deleteHDIContainerWarningMessageStrip=กรุณาจำไว้ว่าการยกเลิกการกำหนดคอนเทนเนอร์ HDI จากพื้นที่จะส่งผลให้มีการลบรายการล็อกการตรวจสอบที่สร้างขึ้นทั้งหมด หากคุณต้องการเก็บล็อกการตรวจสอบไว้ ให้พิจารณาเอ็กซ์ปอร์ตล็อกดังกล่าวก่อนที่จะยกเลิกการกำหนดคอนเทนเนอร์ HDI
#XTXT: All audit logs
allAuditLogs=รายการล็อกการตรวจสอบทั้งหมดที่สร้างขึ้นสำหรับพื้นที่
#XMSG: Message strip disable audit warning
disableAuditWarningMessageStrip=กรุณาจำไว้ว่าการปิดใช้งานนโยบายการตรวจสอบ (การดำเนินการอ่านและเปลี่ยนแปลง) จะส่งผลให้มีการลบรายการล็อกการตรวจสอบทั้งหมด หากคุณต้องการเก็บรายการล็อกการตรวจสอบไว้ ให้พิจารณาเอ็กซ์ปอร์ตรายการดังกล่าวก่อนที่จะปิดใช้งานนโยบายการตรวจสอบ
#XTIT: No Spaces assigned to ECN Illustration title
noSpacesECNTitle=ยังไม่มีการกำหนดพื้นที่หรือออบเจค
#XTIT: No Spaces assigned to ECN Illustration description
noSpacesECNDescription=เมื่อต้องการเริ่มต้นทำงานกับโหนดการคำนวณแบบยืดหยุ่น ให้กำหนดพื้นที่หรือออบเจคให้กับโหนด
#XTIT: No Spaces Illustration title
noSpacesTitle=ยังไม่มีการสร้างพื้นที่
#XTIT: No Spaces Illustration description
noSpacesDescription=เมื่อต้องการเริ่มต้นการรับข้อมูล ให้สร้างพื้นที่
#XTIT: No Spaces Illustration title
noDeletedSpacesTitle=ถังรีไซเคิลว่างเปล่า
#XTIT: No Spaces Illustration description
noDeletedSpacesDescription=คุณสามารถกู้คืนพื้นที่ที่ถูกลบได้จากที่นี่
#XTXT:  {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDatabaseUsers=เมื่อพื้นที่ถูกปรับใช้แล้ว ผู้ใช้ฐานข้อมูลต่อไปนี้จะถูกลบ{0} และไม่สามารถกู้คืนได้:
#XTIT: Delete database users
deleteDatabaseUsersTitle=ลบผู้ใช้ฐานข้อมูล
#YMSE: Error if ecn id already exists
ecnIDInputDuplicateError=ID มีอยู่แล้ว
#YMSE: Error if ecn id contains invalid characters
ecnIDInputInvalidError=กรุณาใช้อักขระตัวพิมพ์เล็ก a - z และตัวเลข 0 - 9 เท่านั้น
#YMSE: Error if ecn id is too short
ecnIDInputTooShortError=ID ต้องมีความยาวต่ำสุด {0} อักขระ
#XMSG: ecn id length warning
ecnIdLengthWarning=เกินจำนวนอักขระสูงสุด {0} ตัว
#XFLD: open System Monitor
systemMonitor=การติดตามตรวจสอบระบบ
#XFLD: open ECN schedule dialog menu entry
schedule=กำหนดการ
#XFLD: open create ECN schedule dialog
createSchedule=สร้างกำหนดการ
#XFLD: open change ECN schedule dialog
changeSchedule=แก้ไขกำหนดการ
#XFLD: open delete ECN schedule dialog
deleteSchedule=ลบกำหนดการ
#XFLD: open self assign ECN schedule dialog
selfAssignSchedule=กำหนดกำหนดการให้กับฉัน
#XFLD: open pause ECN schedule dialog
pauseSchedule=หยุดกำหนดการชั่วคราว
#XFLD: open resume ECN schedule dialog
resumeSchedule=ดำเนินการกำหนดการต่อ
#XFLD: View Logs
viewLogs=ดูล็อก
#XFLD: Compute Blocks
computeBlocks=บล็อคที่คำนวณ
#XFLD: Memory label in ECN creation dialog
ecnMemory=หน่วยความจำ (GB)
#XFLD: Storage label in ECN creation dialog
ecnStorage=พื้นที่จัดเก็บ (GB)
#XFLD: Number of CPU label in ECN creation dialog
ecnCPUCount=จำนวน CPU
#XFLD: ECN updated by label
changedBy=เปลี่ยนแปลงโดย
#XFLD: ECN updated on label
changedOn=เปลี่ยนแปลงเมื่อ
#XMSG: Success message after creating a Elastic Compute Node
createEcnSuccessMessage=สร้างโหนดการคำนวณแบบยืดหยุ่นแล้ว
#YMSE: Error while creating a Elastic Compute Node
createEcnError=ไม่สามารถสร้างโหนดการคำนวณแบบยืดหยุ่น
#XMSG: Success message after updating a Elastic Compute Node
updateEcnSuccessMessage=อัพเดทโหนดการคำนวณแบบยืดหยุ่นแล้ว
#XMSG: Error while updating a Elastic Compute Node
udpateEcnError=ไม่สามารถอัพเดทโหนดการคำนวณแบบยืดหยุ่น
#XMSG: Success message after deleting a Elastic Compute Node
deleteEcnSuccessMessage=ลบโหนดการคำนวณแบบยืดหยุ่นแล้ว
#YMSE: Error while deleting a Elastic Compute Node
deleteEcnError=ไม่สามารถลบโหนดการคำนวณแบบยืดหยุ่น
#XMSG: Success message after starting a Elastic Compute Node
startingEcnSuccessMessage=กำลังเริ่มต้นโหนดการคำนวณแบบยืดหยุ่น
#XMSG: Success message after stopping a Elastic Compute Node
stoppingEcnSuccessMessage=กำลังหยุดโหนดการคำนวณแบบยืดหยุ่น
#YMSE: Error while starting a Elastic Compute Node
startingEcnError=ไม่สามารถเริ่มต้นโหนดการคำนวณแบบยืดหยุ่น
#YMSE: Error while stopping a Elastic Compute Node
stoppingEcnError=ไม่สามารถหยุดโหนดการคำนวณแบบยืดหยุ่น
#XBUT: Add Object button for an ECN
assignObjects=เพิ่มออบเจค
#XFLD: Enable auto object assignment label
enableAutoObjAssignment=เพิ่มออบเจคทั้งหมดโดยอัตโนมัติ
#XFLD: object type label to be assigned
objectTypeLabel=ประเภท (การใช้เชิงความหมาย)
#XFLD: assigned object type label
assignedObjectTypeLabel=ประเภท
#XFLD: technical name label
TechnicalNameLabel=ชื่อทางเทคนิค
#XFLD: Object selecttion ECN label
objectSelectionECNLabel=เลือกออบเจคที่คุณต้องการเพิ่มในโหนดการคำนวณแบบยืดหยุ่น
#XTIT: Add objects dialog title
assignObjectsTitle=กำหนดออบเจคของ
#XFLD: object label with object count
objectLabel=ออบเจค
#XMSG: No objects available to add message.
noObjectsToAssign=ไม่มีออบเจคที่จะกำหนด
#XMSG: No objects assigned message.
noAssignedObjects=ไม่มีการกำหนดออบเจค
#XTIT: Remove objects popup title
removeObjectsOrSpacesConfirmationTitle=คำเตือน
#XTIT: Delete ECN popup button and title
deleteECNConfirmationButtonAndTitle=ลบ
#XMSG: Remove objects popup text
removeObjectsConfirmation=คุณแน่ใจหรือไม่ว่าต้องการย้ายออบเจคที่เลือกออก?
#XMSG: Remove spaces popup text
removeSpacesConfirmation=คุณแน่ใจหรือไม่ว่าต้องการย้ายพื้นที่ที่เลือกออก?
#XBUT: Confirmation button for objects dialog
removeObjectsOrSpacesConfirmationBtn=ย้ายพื้นที่ออก
#XMSG: Success message after removing objects
removeObjectsSuccessMessage=ย้ายออบเจคที่แสดงออกแล้ว
#XMSG: Success message after assigning objects
assignObjectsSuccessMessage=กำหนดออบเจคที่แสดงแล้ว
#XFLD: auto object assignment label for space tile
autoObjAssignmentLabel=ออบเจคที่แสดงทั้งหมด
#XFLD: Spaces tab label
spacesTabLabel=พื้นที่
#XFLD: Exposed objects tab label
consumptionObjectsTabLabel=ออบเจคที่แสดง
#XMSG: Success message after unassigning spaces
spacesUnassignedSuccessMessage=ย้ายพื้นที่ออกแล้ว
#XMSG: Success message after unassigning space
spaceUnassignedSuccessMessage=ย้ายพื้นที่ออกแล้ว
#YMSE: Error while assigning / unassigning spaces
spacesAssignUnassignError=ไม่สามารถกำหนดหรือย้ายพื้นที่ออก
#YMSE: Error while removing objects
removeObjectsError=เราไม่สามารถกำหนดหรือย้ายออบเจคออกได้
#YMSE: Error while removing object
removeObjectError=เราไม่สามารถกำหนดหรือย้ายออบเจคออกได้
#YMSE: Error when saving the ecn with invalid value
invalidSizingPlanError=ตัวเลขที่เลือกไว้ก่อนหน้านี้ไม่ถูกต้องอีกต่อไป กรุณาเลือกตัวเลขที่ถูกต้อง
#YMSE: Error when saving the ecn with invalid value
invalidPerformanceClassError=กรุณาเลือกคลาสประสิทธิภาพที่ถูกต้อง
#YMSE: Error when saving the ecn with invalid value
performanceClassNotAvailableError=คลาสประสิทธิภาพที่เลือกก่อนหน้า "{0}" ไม่สามารถใช้ได้ในขณะนี้ กรุณาเลือกคลาสประสิทธิภาพที่ถูกต้อง
#XTXT: Delete ECN confirmation text
deleteConfirmationTxtECN=คุณแน่ใจหรือไม่ว่าต้องการลบโหนดการคำนวณแบบยืดหยุ่น?
#XFLD: tooltip for ? button
help=วิธีใช้
#XFLD: ECN edit button label
editECN=กำหนดรูปแบบ
#XFLD: Technical type label for ERModel
DWC_ERMODEL=เอนทิตี้ - โมเดลความสัมพันธ์
#XFLD: Technical type label for Local Table
DWC_LOCAL_TABLE=ตารางภายใน
#XFLD: Technical type label for Remote Table
DWC_REMOTE_TABLE=ตารางระยะไกล
#XFLD: Technical type label for Analytic Model
DWC_ANALYTIC_MODEL=โมเดลการวิเคราะห์
#XFLD: Technical type label for Task Chain
DWC_TASKCHAIN=เชนงาน
#XFLD: Technical type label for Data Flow
DWC_DATAFLOW=ผังข้อมูล
#XFLD: Technical type label for Replication Flow
DWC_REPLICATIONFLOW=ผังการทำสำเนา
#XFLD: Technical type label for Transformation Flow
DWC_TRANSFORMATIONFLOW=ผังการแปลงข้อมูล
#XFLD: Technical type label for Intelligent Lookup
DWC_IDT=การค้นหาแบบอัจฉริยะ
#XFLD: Technical type label for Repository
DWC_REPOSITORY_PACKAGE=พื้นที่เก็บข้อมูล
#XFLD: Technical type label for Enterprise Search
DWC_ESMODEL=Enterprise Search
#XFLD: Technical type label for View
DWC_VIEW=ดู
#XFLD: Technical type label for Data Product
DWC_DATAPRODUCT=ผลิตภัณฑ์ข้อมูล
#XFLD: Technical type label for Data Access Control
DWC_DAC=การควบคุมการเข้าถึงข้อมูล
#XFLD: Technical type label for Folder
DWC_FOLDER=แฟ้ม
#XFLD: Technical type label for Data Entity
DWC_BUSINESS_ENTITY=เอนทิตี้ธุรกิจ
#XFLD: Technical type label for Business Entity Variant
DWC_BUSINESS_ENTITY_VARIANT=ชุดตัวเลือกเอนทิตี้ธุรกิจ
#XFLD: Technical type label for Responsibility Scenario
DWC_AUTH_SCENARIO=ภาพจำลองความรับผิดชอบ
#XFLD: Technical type label for Fact Model
DWC_FACT_MODEL=โมเดลข้อเท็จจริง
#XFLD: Technical type label for Transformation Flow
DWC_PERSPECTIVE=เพอร์สเปคทีฟ
#XFLD: Technical type label for Consumption Model
DWC_CONSUMPTION_MODEL=โมเดลการใช้
#XFLD: Technical type label for Remote Connection
DWC_REMOTECONNECTION=การเชื่อมต่อระยะไกล
#XFLD: Technical type label for Fact Model Variant
DWC_FACT_MODEL_VARIANT=ชุดตัวเลือกโมเดลข้อเท็จจริง
#XMSG: Schedule created alert message
createScheduleSuccess=สร้างกำหนดการแล้ว
#XMSG: Schedule updated alert message
updateScheduleSuccess=อัพเดทกำหนดการแล้ว
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=ลบกำหนดการแล้ว
#XMSG: Schedule takeover success message
ownerChangeScheduleSuccess=กำหนดการถูกกำหนดให้กับคุณแล้ว
#XMSG: Message for starting pausing of schedule
pauseScheduleStarted=กำลังหยุด 1 กำหนดการชั่วคราว
#XMSG: Message for starting resuming of schedule
resumeScheduleStarted=กำลังดำเนินการ 1 กำหนดการต่อ
#XFLD: Segmented button label
availableSpacesButton=รายการที่มีอยู่
#XFLD: Segmented button label
selectedSpacesButton=เลือกแล้ว
#XFLD: Visit website button text
visitWebsite=เยี่ยมชมเว็บไซต์
#XMSG: Warning message on unselecting source language
sourceLanguageRemovalWarning=ภาษาต้นฉบับที่เลือกไว้ก่อนหน้านี้จะถูกย้ายออก
#XBUT: enable translation confirmation button
enableTranslationConfirmationBtn=เปิดใช้งาน
#XFLD: ECN performance class label
performanceClassLabel=คลาสประสิทธิภาพ
#XTXT performance class memory text
memoryText=หน่วยความจำ
#XTXT performance class compute text
computeText=การคำนวณ
#XTXT performance class high-compute text
highComputeText=การคำนวณประสิทธิภาพสูง
#XBUT: Recycle Bin Button Text
recycleBin=ถังรีไซเคิล
#XBUT: Restore Button Text
restore=คืนค่า
#XMSG: Warning message for new Workload Management UI
priorityWarning=พื้นที่นี้เป็นแบบอ่านอย่างเดียว คุณสามารถเปลี่ยนแปลงลำดับความสำคัญของพื้นที่ได้ในพื้นที่ ระบบ/การกำหนดรูปแบบ/การจัดการปริมาณงาน
#XMSG: Warning message for new Workload Management UI
workloadWarning=พื้นที่นี้เป็นแบบอ่านอย่างเดียว คุณสามารถเปลี่ยนแปลงการกำหนดรูปแบบปริมาณงานของพื้นที่ได้ในพื้นที่ ระบบ/การกำหนดรูปแบบ/การจัดการปริมาณงาน
#XCOL: Space table-view column: Apache Spark vCPUs
sparkManagementVCPUs=vCPU ของ Apache Spark
#XCOL: Space table-view column: Apache Spark Memory (GB)
sparkManagementMemory=หน่วยความจำของ Apache Spark (GB)
#XCOL: Space type (from Space Capability) for Data Ingestion Spaces
ingestionSpaceType=การนำผลิตภัณฑ์ข้อมูลเข้า
#XFLD: Deployment in progress nodatatext for spark table
sparkTableDeploymentInProgress=ไม่มีข้อมูลเนื่องจากพื้นที่ดังกล่าวถูกปรับใช้อยู่ในขณะนี้
#XFLD: Runtime data being loaded nodatatext for spark table
sparkTableDeploymentLoading=ไม่มีข้อมูลเนื่องจากพื้นที่ดังกล่าวถูกโหลดอยู่ในขณะนี้
#XCOL: Space type (from Space Capability) for BW Private Cloud Edition
BWPCESpaceType=SAP BW
#XBUT: Go to Instance Mappings Button
navToMappingsButton=แก้ไขการแม็ปอินสแตนซ์
#XFLD: SAP HANA Data Lake Storage label if less than 1GB
dataLakeStorageUsed=< 1 GB
