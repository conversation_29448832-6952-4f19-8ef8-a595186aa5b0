#DO NOT REMOVE________________________
#XFLD: Breadcrumb for space monitoring page
spaceMonitorBreadcrumb=Supervisión
#XTXT: Type name for spaces in browser tab page title
space=Espacio
#_____________________________________
#XFLD: Spaces label in
spaces=Espacios
#XFLD: Manage plan button text
manageQuotaButtonText=Administrar plan
#XBUT: Manage resources button
manageResourcesButton=Administrar recursos
#XFLD: Create space button tooltip
createSpace=Crear espacio
#XFLD: Create
create=Crear
#XFLD: Deploy
deploy=Implementar
#XFLD: Page
page=Página
#XFLD: Cancel
cancel=Cancelar
#XFLD: Update
update=Actualizar
#XFLD: Save
save=Guardar
#XFLD: OK
ok=OK
#XFLD: days
days=Días
#XFLD: Space tile edit button label
edit=Editar
#XFLD: Auto Assign all objects to space
autoAssign=Asignación automática
#XFLD: Space tile open monitoring button label
openMonitoring=Supervisar
#XFLD: Delete
delete=Eliminar
#XFLD: Copy Space
copy=Copiar
#XFLD: Close
close=Cerrar
#XCOL: Space table-view column status
status=Estado
#XFLD: Space status active
activeLabel=Activo
#XFLD: Space status locked
lockedLabel=Bloqueado
#XFLD: Space status critical
criticalLabel=Crítico
#XFLD: Space status cold
coldLabel=Poco utilizado
#XFLD: Space status deleted
deletedLabel=Eliminado
#XFLD: Space status unknown
unknownLabel=Desconocido
#XFLD: Space status ok
okLabel=OK
#XFLD: Database user expired
expired=Vencido
#XFLD: deployed
deployed=Implementado
#XFLD: not deployed
notDeployed=No implementado
#XFLD: changes to deploy
changesToDeploy=Cambios para implementar
#XFLD: pending
pending=Implementando
#XFLD: designtime error
designtimeError=Error de tiempo de diseño
#XFLD: runtime error
runtimeError=Error de tiempo de ejecución
#XFLD: Space created by label
createdBy=Creado por
#XFLD: Space created on label
createdOn=Fecha de creación
#XFLD: Space deployed on label
deployedOn=Fecha de implementación
#XFLD: Space ID label
spaceID=ID de espacio
#XFLD: Priority label
priority=Prioridad
#XFLD: Space Priority label
spacePriority=Prioridad de espacio
#XFLD: Space Configuration label
spaceConfiguration=Configuración de espacio
#XFLD: Not available
notAvailable=No disponible
#XFLD: WorkloadType default
default=Predeterminado
#XFLD: WorkloadType custom
custom=Personalizado
#XFLD: Data Lake option label; refers to component SAP HANA Cloud, Data Lake
dataLakeAccess=Acceso al lago de datos
#XFLD: Translation label
translationLabel=Traducción
#XFLD: Source language label
sourceLanguageLabel=Idioma de origen
#XFLD: Translation CheckBox label
translationCheckBox=Habilitar traducción
#XTXT: Popover hint that space has to be deployed to show user details
openDatabaseUserHintPopoverText=Implemente el espacio para acceder a los detalles del usuario.
#XTXT: Popover hint that space has to be deployed to open database explorer
openDatabaseExplorerHintPopoverText=Implemente el espacio para abrir el explorador de base de datos.
#XTXT: Text for when the Data Lake check box is disabled
dataLakeOptionDisabled=No puede usar este espacio para acceder a Data Lake porque ya lo está utilizando otro espacio.
#XFLD: Data Lake check box text; refers to component SAP HANA Cloud, Data Lake
dataLakeCheckBox=Use este espacio para acceder al lago de datos.
#XFLD: Space Priority minimum label extension
low=Bajo
#XFLD: Space Priority maximum label extension
high=Alto
#XFLD: Space name label
spaceName=Nombre de espacio
#XFLD: Enable deploy objects checkbox
enableDeployObjects=Implementar objetos
#XTIT: Copy spaces dialog title
copySpaceDialogTitle=Copiar {0}
#XTXT Human readable text for unselected source language
unSelectedSourceLanguage=(No seleccionado)
#XTXT Human readable text for language code "af"
af=afrikáans
#XTXT Human readable text for language code "ar"
ar=árabe
#XTXT Human readable text for language code "bg"
bg=búlgaro
#XTXT Human readable text for language code "ca"
ca=catalán
#XTXT Human readable text for language code "zh"
zh=chino simplificado
#XTXT Human readable text for language code "zf"
zf=chino
#XTXT Human readable text for language code "hr"
hr=croata
#XTXT Human readable text for language code "cs"
cs=checo
#XTXT Human readable text for language code "cy"
cy=galés
#XTXT Human readable text for language code "da"
da=danés
#XTXT Human readable text for language code "nl"
nl=neerlandés
#XTXT Human readable text for language code "en-UK"
en-UK=inglés (Reino Unido)
#XTXT Human readable text for language code "en"
en=inglés (Estados Unidos)
#XTXT Human readable text for language code "et"
et=estonio
#XTXT Human readable text for language code "fa"
fa=persa
#XTXT Human readable text for language code "fi"
fi=finés
#XTXT Human readable text for language code "fr-CA"
fr-CA=francés (Canadá)
#XTXT Human readable text for language code "fr"
fr=francés
#XTXT Human readable text for language code "de"
de=alemán
#XTXT Human readable text for language code "el"
el=griego
#XTXT Human readable text for language code "he"
he=hebreo
#XTXT Human readable text for language code "hi"
hi=hindi
#XTXT Human readable text for language code "hu"
hu=húngaro
#XTXT Human readable text for language code "is"
is=islandés
#XTXT Human readable text for language code "id"
id=indonesio
#XTXT Human readable text for language code "it"
it=italiano
#XTXT Human readable text for language code "ja"
ja=japonés
#XTXT Human readable text for language code "kk"
kk=kazajo
#XTXT Human readable text for language code "ko"
ko=coreano
#XTXT Human readable text for language code "lv"
lv=letón
#XTXT Human readable text for language code "lt"
lt=lituano
#XTXT Human readable text for language code "ms"
ms=malayo
#XTXT Human readable text for language code "no"
no=noruego
#XTXT Human readable text for language code "pl"
pl=polaco
#XTXT Human readable text for language code "pt"
pt=portugués (Brasil)
#XTXT Human readable text for language code "pt-PT"
pt-PT=portugués (Portugal)
#XTXT Human readable text for language code "ro"
ro=rumano
#XTXT Human readable text for language code "ru"
ru=ruso
#XTXT Human readable text for language code "sr"
sr=serbio
#XTXT Human readable text for language code "sh"
sh=serbocroata
#XTXT Human readable text for language code "sk"
sk=eslovaco
#XTXT Human readable text for language code "sl"
sl=esloveno
#XTXT Human readable text for language code "es"
es=español
#XTXT Human readable text for language code "es-MX"
es-MX=español (México)
#XTXT Human readable text for language code "sv"
sv=sueco
#XTXT Human readable text for language code "th"
th=tailandés
#XTXT Human readable text for language code "tr"
tr=turco
#XTXT Human readable text for language code "uk"
uk=ucraniano
#XTXT Human readable text for language code "vi"
vi=vietnamita
#XTIT: Delete spaces confirmation title
deleteConfirmationHeaderText=Eliminar espacios
#XTXT: Logically delete space delete confirmation text with space name
logicallyDeleteConfirmationBodyTextWithSpaceName=¿Seguro que quiere mover el espacio "{0}" a la papelera de reciclaje?
#XTXT: Logically dpace delete confirmation text for multiple spaces
logicallyDeleteConfirmationBodyTextMultiple=¿Seguro que quiere mover los {0} espacios seleccionados a la papelera de reciclaje?
#XTXT: Delete space delete confirmation text with space name
deleteConfirmationBodyTextWithSpaceName=¿Seguro que desea eliminar el espacio "{0}"? Esta acción no se puede deshacer.
#XTXT: Delete space delete confirmation text for multiple spaces
deleteConfirmationBodyTextMultiple=¿Seguro que desea eliminar los {0} espacios seleccionados? Esta acción no se puede deshacer. El siguiente contenido se eliminará {1}:
#XTXT: permanently
permanently=de manera permanente
#XTXT: {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDetail=El siguiente contenido se eliminará {0} y no se podrá recuperar:
#XTXT: Hint Delete space confirmation text
deleteConfirmationHint=Escriba {0} para confirmar la eliminación.
#XTXT: Delete space confirmation error check spelling
deleteConfirmationErrorCheckSpelling=Verifique la ortografía y vuelva a intentarlo.
#XTXT: All Spaces
allSpaces=Todos los espacios
#XTXT: All data
allData=Todos los objetos y datos contenidos en el espacio
#XTXT: All connections
allConnections=Todas las conexiones definidas en el espacio
#XFLD: Space tile selection box tooltip
clickToSelect=Clic para seleccionar
#XTXT: All database users
allDatabaseUsers=Todos los objetos y datos contenidos en cualquier esquema de SQL abierto asociado con el espacio
#XFLD: remove members button tooltip
deleteUsers=Quitar miembros
#XTXT: Space long description text
description=Descripción (máximo 4000 caracteres)
#XFLD: Add Members button tooltip
addUsers=Agregar miembros
#XFLD: Add Users button tooltip
addUsersTooltip=Agregar usuarios
#XFLD: Edit Users button tooltip
editUsersTooltip=Editar usuarios
#XFLD: Remove Users button tooltip
removeUsersTooltip=Quitar usuarios
#XFLD: Searchfield placeholder
filter=Buscar
#XCOL: Users table-view column health
health=Mantenimiento
#XCOL: Users table-view column access
access=Acceso
#XFLD: No user found nodatatext
noDataText=No se encontró ningún usuario
#XTIT: Members dialog title
selectUserDialogTitle=Agregar miembros
#XTIT: User dialog title
addUserDialogTitle=Agregar usuarios
#XTIT: Delete connections confirmation title
deleteConnectionConfirmationHeaderText=Eliminar conexiones
#XTIT: Delete single connection confirmation title
deleteConnectionConfirmationHeaderTextSingle=Eliminar conexión
#XTXT: Delete connection delete confirmation text
deleteConnectionConfirmationBodyText=¿Está seguro de que desea eliminar las conexiones seleccionadas? Las quitaremos de forma permanente.
#XTIT: Remote source dialog title
selectRemoteConnectionDialogTitle=Seleccionar conexiones
#XTIT: Share connection dialog title
connectionSharingDialogTitle=Compartir conexión
#XTIT: Connections shared from other spaces table title
sharedConnectionsFromOtherSpaces=Conexiones compartidas
#XFLD: Add remote source button tooltip
addRemoteConnections=Agregar conexiones
#XFLD: Delete remote source button tooltip
deleteRemoteConnections=Quitar conexiones
#XFLD: Share remote source button tooltip
shareConnections=Compartir conexiones
#XFLD: Tile-layout tooltip
tileLayout=Diseño de mosaico
#XFLD: Table-layout tooltip
tableLayout=Diseño de tabla
#XMSG: Success message after creating space
createSpaceSuccessMessage=Espacio creado
#XMSG: Success message after copying space
copySpaceSuccessMessage=Copiando espacio "{0}" a espacio "{1}"
#XMSG: Success message after deploying space
deploymentSuccessMessage=Se inició la implementación del espacio
#XMSG: Success message after Apache Spark update started
sparkUpdateStartedSuccessMessage=Se inició la actualización de Apache Spark
#YMSE: Error while starting Apache Spark update
sparkUpdateStartedErrorMessage=Falló la actualización de Apache Spark
#XMSG: Success message after updating space
updateSettingsSuccessMessage=Detalles de espacio actualizados
#XMSG: Success message after unlocking space
unlockSpaceSuccessMessage=Espacio desbloqueado temporalmente
#XMSG: Success message after deleting a single space
deleteSpaceSuccessMessage=Espacio eliminado
#XMSG: Success message after deleting multiple spaces
deleteSpacesSuccessMessage=Espacios eliminados
#XMSG: Success message after restoring a single space
restoreSpaceSuccessMessage=Espacio restaurado
#XMSG: Success message after restoring multiple spaces
restoreSpacesSuccessMessage=Espacios restaurados
#YMSE: Error while updating settings
updateSettingsFailureMessage=No se pudo actualizar la configuración del espacio.
#YMSE: Error while updating data lake option
dataLakeEnabledInOtherSpace=El lago de datos ya está asignado a otro espacio. Solo un espacio por vez puede acceder al lago de datos.
#YMSE: Error while updating data lake option
virtualTablesExists=No puede desasignar el lago de datos de este espacio porque aún existen dependencias a tablas virtuales*. Elimine las tablas virtuales para desasignar el lago de datos de este espacio.
#YMSE: Error while unlocking space
unlockSpaceFailureMessage=No se pudo desbloquear el espacio.
#YMSE: Error while creating space
createSpaceError=No se pudo crear el espacio.
#YMSE: Error while creating duplicate space
createDuplicateSpaceError=Ya existe un espacio con el nombre {0}.
#YMSE: Error while deleting a single space
deleteSpaceError=No se pudo eliminar el espacio.
#YMSE: Error details while deleting a single space
deleteSpaceDetailedError=Su espacio “{0}” ya no funciona correctamente. Vuelva a intentar eliminarlo. Si aún no funciona, pídale a su administrador que elimine su espacio o que abra un ticket.
#YMSE: Error while deleting a single space in files
deleteSpaceInFilesError=No se pudieron eliminar los datos de espacio en Archivos.
#YMSE: Error while deleting a single space users
deleteSpaceUsersError=No se pudieron quitar los usuarios.
#YMSE: Error while deleting a single space schema
deleteSpaceSchemasError=No se pudieron quitar los esquemas.
#YMSE: Error while deleting a single space connections
deleteSpaceConnectionsError=No se pudieron quitar las conexiones.
#YMSE: Error while deleting a single space data
deleteSpaceDataError=No se pudieron eliminar los datos de espacio.
#YMSE: Error while deleting multiple spaces
deleteSpacesError=No se pudieron eliminar los espacios.
#YMSE: Error while restoring a single space
restoreSpaceError=No se pudo restaurar el espacio.
#YMSE: Error while restoring multiple spaces
restoreSpacesError=No se pudieron restaurar los espacios.
#YMSE: Error while creating users
createUsersError=No se pudieron agregar los usuarios.
#YMSE: Error while removing users
removeUsersError=No pudimos quitar los usuarios.
#YMSE: Error while removing user
removeUserError=No pudimos quitar el usuario.
#YMSE: Error assigning roles to a user
roleAssignUnAssignError=No pudimos agregar el usuario a los roles con alcance seleccionados. No se puede agregar a sí mismo a un rol con alcance. Puede pedir al administrador que lo haga.
#YMSE: Error assigning user to the space
userAssignError=No pudimos asignar el usuario al espacio. \n\n El usuario ya está asignado al número máximo (100) de espacios permitido de roles en el alcance.
#YMSE: Error assigning users to the space
usersAssignError=No pudimos asignar los usuarios al espacio. \n\n El usuario ya está asignado al número máximo (100) de espacios permitido de roles en el alcance.
#YMSE: Error while fetching users
fetchUserWithScopedRolesError=No pudimos recuperar los usuarios. Vuelva a intentarlo.
#YMSE: Error while fetching scoped roles
fetchScopedRolesError=No pudimos recuperar los roles en el alcance.
#YMSE: Error while fetching members
fetchUserError=No se pudieron recuperar los miembros. Vuelva a intentar más tarde.
#YMSE: Error while loading run-time database
loadRuntimeError=No pudimos cargar información desde la base de datos en tiempo de ejecución.
#YMSE: Error while loading spaces
loadSpacesError=Lo sentimos, se produjo un error al intentar recuperar sus espacios.
#YMSE: Error while loading haas resources
loadStorageError=Lo sentimos, se produjo un error al intentar recuperar los datos de almacenamiento.
#YMSE: Error no data could be loaded
loadDataError=Lo sentimos, se produjo un error al intentar recuperar sus datos.
#XFLD: Click to refresh storage data
clickToRefresh=Haga clic aquí para reintentar.
#XTIT: Delete space popup title
deleteSpacePopupTitle=Eliminar espacio
#XCOL: Spaces table-view column name
name=Nombre
#XCOL: Spaces table-view deployment status
deploymentStatus=Estado de implementación
#XFLD: Disk label in space details
storageLabel=Disco (GB)
#XFLD: In-Memory label in space details
ramLabel=Memoria (GB)
#XFLD: Memory label on space card
memory=Memoria para almacenamiento
#XFLD: Storage Assignment label in space details
storrageAssignmentLabel=Almacenamiento de espacio
#XFLD: Storage Type label in space details
storageTypeLabel=Tipo de almacenamiento
#XFLD: Enable Space Quota
enableSpaceQuota=Habilitar cuota de espacio
#XFLD: No Space Quota
noSpaceQuota=Sin cuota de espacio
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
diskAndInMemoryText=Base de datos de SAP HANA (disco y en memoria)
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
dataLakeFilesText=Archivos del lago de datos de SAP HANA
#XFLD: Available scoped roles label
availableRoles=Roles con alcance disponibles
#XFLD: Selected scoped roles label
selectedRoles=Roles con alcance seleccionados
#XCOL: Spaces table-view column models
models=Modelos
#XCOL: Spaces table-view column users
users=Usuarios
#XCOL: Spaces table-view column connections
connections=Conexiones
#XFLD: Section header overview in space detail
overview=Descripción general
#XFLD: Section header Apache Spark in space detail
sparkManagementSection=Apache Spark
#XFLD: Section header Apache Spark in space detail
sparkManagementSubSection=Aplicaciones
#XFLD: Section header Apache Spark in space detail
taskAssignmentSubSection=Asignación de tarea
#XFLD: vCPU label in Apache Spark section
vCPULabel=vCPU
#XFLD: Memory label in Apache Spark section
memoryLabel=Memoria (GB)
#XFLD: space configuration label in Apache Spark section
spaceConfigurationLabel=Configuración de espacio
#XFLD: Space Source label
sparkApplicationLabel=Aplicación
#XFLD: Cluster Size label
clusterSizeLabel=Tamaño de clúster
#XFLD: Driver label
driverLabel=Controlador
#XFLD: Executor label
executorLabel=Ejecutor
#XFLD: max label
maxLabel=Máx. usado
#XFLD: TrF Default label
trFDefaultLabel=Valor predeterminado de flujo de transformación
#XFLD: Merge Default label
mergeDefaultLabel=Valor predeterminado de combinación
#XFLD: Optimize Default label
optimizeDefaultLabel=Valor predeterminado de optimización
#XFLD: Deployment Default label
deploymentDefaultLabel=Implementación de tabla local (archivo)
#XFLD: max spark source label
driverExecutorMaxUsedSparkSource={0} CPU / {1} GB
#XFLD: Object type label
taskObjectTypeLabel=Tipo de objeto
#XFLD: Task activity label
taskActivityLabel=Actividad
#XFLD: Task Application ID label
taskApplicationIDLabel=Aplicación predeterminada
#XFLD: Section header in space detail
generalSettings=Configuración general
#XMSG: Message strip message when a space is locked
spaceLockedMessageStrip=Actualmente, este espacio está bloqueado por el sistema.
#XMSG: Message strip message deploy warning
deployWarningMessageStrip=Los cambios en esta sección se implementarán inmediatamente.
#XMSG: Message strip message modify space configuration warning
modifySpaceConfigurationMessageStrip=Tenga presente que el cambio de valores puede causar problemas de rendimiento.
#XFLD: Button text to unlock the space again
unlockSpace=Desbloquear espacio
#XFLD: Info text for audit log formatted message
auditLogText=Habilite los registros de auditoría para el ingreso de acciones de lectura o cambio (políticas de auditoría). Luego, los administradores podrán analizar quién realizó cada acción y en qué momento lo hizo.
#XMSG: Message strip  audit warning
auditLogWarningMessageStrip=Los registros de auditoría pueden consumir una gran cantidad de almacenamiento en disco en el inquilino. Si habilita una política de auditoría (acciones de lectura o cambio), debe supervisar regularmente el uso del almacenamiento en disco (a través de la tarjeta de almacenamiento en disco usado en Supervisión del sistema) para evitar que el disco se llene, lo que puede provocar interrupciones en el servicio. Si deshabilita una política de auditoría, todas las entradas en el registro de auditoría se eliminarán. Si desea conservar las entradas del registro de auditoría, considere exportarlas antes de deshabilitar la política de auditoría.
#XFLD: Message strip link text to open in app help to guide user what to do to unlock his space permanently
unlockSpaceHelp=Mostrar ayuda
#XMSG: Message strip message when a space will be locked again
spaceTemporarilyUnlockedMessageStrip=Este espacio excede su almacenamiento de espacio y se bloqueará en {0} {1}.
#XMSG: Unit for remaining time until space is locked again
hours=horas
#XMSG: Unit for remaining time until space is locked again
minutes=minutos
#XFLD: Section header in space detail for auditing
detailsSectionAuditing=Auditoría
#XFLD: Subsection header in space detail for auditing
auditing=Configuración de auditoría de espacio
#XFLD: Hot space tooltip
hotSpaceCountTooltip=Espacios muy utilizados: el almacenamiento utilizado es mayor que el 90 %
#XFLD: Green space tooltip
greenSpaceCountTooltip=Espacios de uso medio: el almacenamiento utilizado es entre el 6 % y el 90 %
#XFLD: Cold space tooltip
coldSpaceCountTooltip=Espacios poco utilizados: el almacenamiento utilizado es del 5 % o menos
#XFLD: Critical space tooltip
criticalSpaceCountTooltip=Espacios muy utilizados: el almacenamiento utilizado es mayor que el 90 %
#XFLD: Green space tooltip
okSpaceCountTooltip=Espacios de uso medio: el almacenamiento utilizado es entre el 6 % y el 90 %
#XFLD: Locked space tooltip
lockedSpaceCountTooltipNew=Espacios con el estado "bloqueado": el bloqueo se debe a una memoria insuficiente.
#XFLD: Locked space tooltip
lockedSpaceCountTooltip=Espacios bloqueados
#YMSE: Error while deleting remote source
deleteRemoteError=No se pudieron quitar las conexiones.
#XFLD: Space ID input tooltip
spaceIDInfoTooltip=El ID de espacio no puede modificarse más adelante.\nCaracteres válidos: A - Z, 0 - 9, y _
#YMSE: Error when space name is empty
spaceNameEmptyValidationError=Ingresar nombre de espacio.
#YMSE: Error when ECN Business name is empty
ecnNameEmptyValidationError=Ingrese un nombre empresarial.
#YMSE: Error when space id is empty
spaceIDInputEmptyValidationError=Ingresar ID de espacio.
#YMSE: Error when space id contains invalid characters
spaceIDInputInValidError=Caracteres no válidos. Use solamente A - Z, 0 - 9, y _.
#YMSE: Error when space id exists while creation
spaceIDInputDuplicateError=El ID de espacio ya existe.
#XFLD: Space searchfield placeholder
search=Buscar
#XMSG: Success message after creating users
createUsersSuccess=Usuarios agregados
#XMSG: Success message after creating user
createUserSuccess=Usuario agregado
#XMSG: Success message after updating users
updateUsersSuccess={0} usuarios actualizados
#XMSG: Success message after updating user
updateUserSuccess=Usuario actualizado
#XMSG: Success message after removing users
removeUsersSuccess={0} usuarios quitados
#XMSG: Success message after removing user
removeUserSuccess=Usuario quitado
#XFLD: Schema name
schemaName=Nombre de esquema
#XFLD: used of total
ofTemplate={0} de {1}
#XFLD: Assigned disk label in space overview
assignedStorageTemplate=Disco asignado ({0} de {1})
#XFLD: Assigned Memory label in space overview
assignedRAMTemplate=Memoria asignada ({0} de {1})
#XFLD: Storage ratio on space
accelearationRAM=Aceleración de memoria
#XFLD: No Storage Consumption
noStorageConsumptionText=No hay cuotas de almacenamiento asignadas.
#XFLD: Used disk label in space overview
usedStorageTemplate=Disco usado para almacenamiento ({0} de {1})
#XFLD: Used Memory label in space overview
usedRAMTemplate=Memoria usada para almacenamiento ({0} de {1})
#XFLD: Used Disk tooltip
usedStorageTooltipTemplate={0} de {1} - Disco usado para almacenamiento
#XFLD: Used Memory tooltip
usedRAMTooltipTemplate={0} de {1} de la memoria usada
#XFLD: Assigned Disk tooltip
assignedStorageTooltipTemplate={0} de {1} del disco asignado
#XFLD: Assigned Memory tooltip
assignedRAMTooltipTemplate={0} de {1} de la memoria asignada
#XFLD: Space related used storage tooltip
spaceRelatedUsedStorageTooltipTemplate=Datos de espacio: {0}
#XFLD: Not space related used storage tooltip
otherDataUsedStorageTooltipTemplate=Otros datos: {0}
#XFLD: Storage used exceeded popover and tooltip
storageUsedExceededText=Considere extender su plan, o comuníquese con el soporte de SAP.
#XCOL: Space table-view column used Disk
usedStorage=Disco usado para almacenamiento
#XCOL: Space monitor column used Memory
usedRAM=Memoria usada para almacenamiento
#XCOL: Space monitor column Schema
tableSchema=Esquema
#XCOL: Space monitor column Storage Type
tableStorageType=Tipo de almacenamiento
#XCOL: Space monitor column Table Type
tableType=Tipo de tabla
#XCOL: Space monitor column Record Count
tableRecordCount=Recuento de registros
#XFLD: Assigned Disk
assignedStorage=Disco asignado para almacenamiento
#XFLD: Assigned Memory
assignedRAM=Memoria asignada para almacenamiento
#XCOL: Space table-view column storage utilization
tableStorageUtilization=Almacenamiento usado
#XFLD: space status
spaceStatus=Estado de espacio
#XFLD: space type
spaceType=Tipo de espacio
#XFLD: space type label for space with space type "SAP BW bridge" ("SAP BW bridge" is a product name, do not translate it)
abapbridgeSpaceType=Puente de SAP BW
#XFLD: space type label for space with space type "Data Marketplace"
marketplaceSpaceType=Producto de proveedor de datos
#XMSG: Error message for when selected space cannot be deleted due to the space type
deleteTypedSpaceError=No puede eliminar el espacio {0} porque su tipo de espacio es {1}.
#XMSG: Error message for when selected spaces cannot be deleted due to the space type
deleteTypedSpacesError=No puede eliminar los {0} espacios seleccionados. No se pueden eliminar los espacios con el siguiente tipo de espacio: {1}.
#XFLD: space type label for space without space type
defaultSpaceType=SAP Datasphere
#XFLD: Tooltip for monitor space button
monitorSpace=Supervisar
#XFLD: Tooltip for edit space button
editSpace=Editar espacio
#XMSG: Deletion warning in messagebox
deleteConfirmation=¿Está seguro de que desea eliminar este espacio?
#XFLD: Tooltip for delete space button
deleteSpace=Eliminar espacio
#XFLD: storage
storage=Disco para almacenamiento
#XFLD: username
userName=Nombre de usuario
#XFLD: port
port=Puerto
#XFLD: hostname
hostName=Nombre de host
#XFLD: password
password=Contraseña
#XBUT: Request new password button
requestPassword=Solicitar nueva contraseña
#YEXP: Usage explanation in time data section
timeDataSectionHint=Cree dimensiones y tablas de tiempo para usar en sus historias y modelos.
#YEXP: Usage explanation in Space schema section
spaceSchemaHint=¿Desea que otras herramientas o aplicaciones consuman los datos en su espacio? Si es así, cree uno o varios usuarios que puedan acceder a los datos en su espacio y seleccione si desea que se consuman todos los datos de espacio futuros de manera predeterminada.
#XTIT: Create schema popup title
createSchemaDialogTitle=Crear esquema de SQL abierto
#XTIT: Generate Time Data dialog title
generateTimeDataDialogTitle=Crear dimensiones y tablas de tiempo
#XTIT: Edit Time Data dialog title
editTimeDataDialogTitle=Editar dimensiones y tablas de tiempo
#XTIT: Time Data token title
timeDataTokenTitle=Datos de tiempo
#XTIT: Time Data token title
timeDataUpdateViews=Actualizar vistas de datos de tiempo
#XFLD: Time Data token creation in progress label
timeDataCreationInProgress=Creación en curso...
#XFLD: Time Data token creation error label
timeDataCreationError=Falló la creación. Vuelva a intentar.
#XTIT: Time Data dialog time settings title
timeDataDialogTimeSettingsTitle=Configuración de tabla de tiempo
#XTIT: Time Data dialog time data table title
timeDataDialogTranslationTablesTitle=Tablas de traducción
#XTIT: Time Data dialog dimensions title
timeDataDialogTimeViewsTitle=Dimensiones de tiempo
#XFLD: Time Data dialog time range label
timeRangeHint=Definir el intervalo de tiempo.
#XFLD: Time Data dialog time data table label
timeDataHint=Nombre su tabla.
#XFLD: Time Data dialog time data dimensions label
timeDimensionsHint=Nombre sus dimensiones.
#XFLD: Time Data Time range description label
timerangeLabel=Intervalo de tiempo
#XFLD: Time Data dialog from year label
fromYearLabel=Desde el año
#XFLD: Time Data dialog to year label
toYearLabel=Hasta el año
#XFLD: Time Data dialog calendar type label
calendarTypeLabel=Tipo de calendario
#XFLD: Time Data dialog granularity label
granularityLabel=Granularidad
#XFLD: Time Data dialog technical name label
technicalNameLabel=Nombre técnico
#XFLD: Time Data dialog quarters text table lable
quarterTextsTableLabel=Tabla de traducción para trimestres
#XFLD: Time Data dialog months text table lable
monthsTextsTableLabel=Tabla de traducción para meses
#XFLD: Time Data dialog days text table lable
daysTextsTableLabel=Tabla de traducción para días
#XFLD: Time Data dialog year label
yearLabel=Dimensión de año
#XFLD: Time Data dialog quarter label
quarterLabel=Dimensión de trimestre
#XFLD: Time Data dialog month label
monthLabel=Dimensión de mes
#XFLD: Time Data dialog day label
dayLabel=Dimensión de día
#XFLD: Time Data dialog gregorian calendar type label
gregorian=Gregoriano
#XFLD: Time Data dialog time granularity day label
day=Día
#XFLD: Time Data dialog business name maxlength reached
timeRangeValueStateTextMaxLengthBusinessName=Se alcanzó la longitud máxima de 1000 caracteres.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextMaxRangeExceeded=El intervalo de tiempo máximo es de 150 años.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeLower=“Desde el año” debe ser inferior a “Hasta el año”
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeHigher="Desde el año" debe ser 1900 o superior.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeHigher=“Hasta el año” debe ser superior a “Desde el año”
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeLower=“Hasta el año” debe ser inferior al año actual más 100
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningFromYear=Si aumenta “Desde el año”, se puede producir la pérdida de datos
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningToYear=Si disminuye “Hasta el año”, se puede producir la pérdida de datos
#XMSG: Time Data creation validation error message
timeDataValidationError=Parece que algunos campos no son válidos. Verifique los campos requeridos para continuar.
#XMSG: Time Data deletion popup text
deleteTimeDataConfirmation=¿Está seguro de que desea eliminar los datos?
#XMSG: Time Data creation success message
createTimeDataSuccess=Datos de tiempo creados
#XMSG: Time Data update success message
updateTimeDataSuccess=Datos de tiempo actualizados
#XMSG: Time Data delete success message
deleteTimeDataSuccess=Datos de tiempo eliminados
#XMSG: Time Data creation error message
createTimeDataError=Se produjo un error al intentar crear los datos de tiempo.
#XMSG: Time Data update error message
updateTimeDataError=Se produjo un error al intentar actualizar los datos de tiempo.
#XMSG: Time Data creation error message
deleteTimeDataError=Se produjo un error al intentar eliminar los datos de tiempo.
#XMSG: Error message when time data could not be retrieved
retrievingTimeDataError=No se pudieron cargar los datos de tiempo.
#XTIT: Time Data deletion popup title
deleteTimeDataConfirmationTitle=Advertencia
#XTXT: Time Data deletion dependency popup text
deleteTimeDataDependencyText=No pudimos eliminar sus datos de tiempo porque se utilizan en otros modelos.
#XTXT: Time Data deletion dependency popup text singular
deleteTimeDataDependencyTextSingular=No pudimos eliminar sus datos de tiempo porque se utilizan en otro modelo.
#XMSG: Validation error when required time data form field is empty
timeDataRequiredField=El campo es obligatorio y no puede quedar vacío.
#XFLD: Tooltip for Open in Database Explorer button
openInDBX=Abrir en explorador de base de datos
#YMSE: Dimension Year
dimensionYearView=Dimensión "Año"
#YMSE: Dimension Year
dimensionQuarterView=Dimensión "Trimestre"
#YMSE: Dimension Year
dimensionMonthView=Dimensión "Mes"
#YMSE: Dimension Year
dimensionDayView=Dimensión "Día"
#XFLD: Time Data deletion object title
timeDataUsedIn=(usado en {0} modelos)
#XFLD: Time Data deletion object title singular
timeDataUsedInSingular=(usado en 1 modelo)
#XFLD: Time Data deletion table column provider
provider=Proveedor
#XTIT: Time Data dependency dialog title
timeDataDependencyDialogTitle=Dependencias
#XTIT: Create schema popup title
createSpaceSchemaUserDialogTitle=Crear usuario de esquema de espacio
#XFLD: Create schema button
createSchemaButton=Crear esquema de SQL abierto
#XFLD: Generate TimeData button
generateTimeDataButton=Crear dimensiones y tablas de tiempo
#XFLD: Show dependencies button
showDependenciesButton=Mostrar dependencias
#XFLD: tooltip for disabled buttons when user is not member of space
mustBeMemberOfSpaceForOperation=Para realizar esta operación, su usuario debe ser un miembro del espacio.
#XFLD: Create space schema user button
createSpaceSchemaUserButton=Crear usuario de esquema de espacio
#YMSE: API Schema users load error
loadSchemaUsersError=No se pudo cargar la lista de usuarios.
#XTIT: API Schema-config popup title
schemaUserDetailsDialogTitle=Detalles de usuario de esquema de espacio.
#XMSG: API Schema user deletion popup text
deleteSpaceSchemaUserConfirmation=¿Está seguro de que desea eliminar el usuario seleccionado?
#XMSG: API Schema user deletion confirmation text
userDeleteSuccess=Usuario eliminado.
#YMSE: API Schema user deletion error
userDeleteError=No se pudo eliminar el usuario.
#XFLD: User deleted
userDeleted=El usuario se eliminó.
#XTIT: Remove user popup title
removeUserConfirmationTitle=Advertencia
#XMSG: Remove user popup text
removeUserConfirmation=¿Seguro que desea quitar el usuario? Se quitará del espacio al usuario y sus roles con alcance asignados.
#XMSG: Remove users popup text
removeUsersConfirmation=¿Seguro que desea quitar los usuarios? Se quitarán del espacio a los usuarios y sus roles con alcance asignados.
#XBUT: Confirmation button for remove user dialog
removeUserConfirmationBtn=Quitar
#YMSE: No data text for available roles
noDataAvailableRoles=El espacio no se agregó a ningún rol con alcance. \n Para poder agregar usuarios al espacio, primero se debe agregar a uno o más roles con alcance.
#YMSE: No data text for selected roles
noDataSelectedRoles=No hay roles con alcance seleccionados
#XTIT: Schema-config popup title
schemaDetailsDialogTitle=Abrir los detalles de configuración de esquema de SQL
#XFLD: Label for Read Audit Log
auditLogRead=Habilitar registro de auditoría para operaciones de lectura
#XFLD: Label for Change Audit Log
auditLogChange=Habilitar registro de auditoría para operaciones de cambio
#XFLD: Label Audit Log Retention
auditLogRetention=Mantener registros para
#XFLD: Label Audit Log Retention Unit
retentionUnit=Días
#YMSE: Error when log retention time is set incorrectly
incorrectRetentionTime=Ingrese un número entero entre {0} y {1}
#XFLD: Label for Cross Space Schema Access
spaceSchemaAccess=Consumir datos de esquema de espacio
#XTIT: Confirm deactivation of space schema consumption dialog title
disableSpaceSchemaConsumptionDialogTitle=Dejar de consumir datos de esquema de espacio
#XTXT: Confirm deactivation of space schema consumption dialog text
disableSpaceSchemaConsumptionDialogText=Este esquema de SQL abierto puede consumir datos de su esquema de espacio. Si deja de consumir, es posible que los modelos basados en los datos de esquema de espacio dejen de funcionar.
#XBUT: Confirmation button for the stop consuming space schema data dialog
stopConsuming=Dejar de consumir
#XTXT: Tooltip for data lake icon
dataLakeIconTooltip=Este espacio se usa para acceder al lago de datos
#XFLD: Tooltip for data lake table entry
dataLakeTableEntryTooltip=Lago de datos habilitado
#XFLD: used In-Memory linit reached tag
usedRamLimitReachedLabel=Se alcanzó el límite de memoria
#XFLD: Space storage Limit reached tag
storageLimitReachedLabel=Se alcanzó el límite de almacenamiento
#XFLD: Minimal Storage Limit Reached tag
minStorageLimitReachedLabel=Se alcanzó el límite de almacenamiento mínimo
#XFLD: Space ram tag
ramLimitReachedLabel=Se alcanzó el límite de memoria
#XFLD: Minimal In-Memory Limit Reached tag
minRamLimitReachedLabel=Se alcanzó el límite de memoria mínimo
#XFLD: Popover text after click on space storage limit warning
storageLimitReachedText=Ha alcanzado el límite de almacenamiento de espacio asignado de {0}. Asigne más almacenamiento al espacio.
#XFLD: System storage tag
systemStorageLimitReachedLabel=Se alcanzó el límite de almacenamiento del sistema
#XMSG: Popover text after click on storage limit warning
systemStorageLimitReachedText=Ha alcanzado el límite de almacenamiento del sistema de {0}. Ahora no puede asignar más almacenamiento al espacio.
#XMSG: Schema deletion warning
deleteSchemaConfirmation=Si elimina este esquema de SQL abierto, también eliminará de manera permanente todos los objetos almacenados y las asociaciones mantenidas en el esquema. ¿Desea continuar?
#XMSG: Success message after deleting a schema
schemaDeleteSuccess=Esquema eliminado
#YMSE: Error while deleting schema.
schemaDeleteError=No se pudo eliminar el esquema.
#XMSG: Success message after update a schema
schemaUpdateSuccess=Esquema actualizado
#YMSE: Error while updating schema.
schemaUpdateError=No se pudo actualizar el esquema.
#XMSG: Hint when no password is shown in the dialog.
newPasswordHint=Proporcionamos una contraseña para este esquema. Si olvidó o perdió su contraseña, puede solicitar una nueva. Recuerde copiar o guardar la contraseña nueva.
#XMSG: Hint when showing the password in the dialog.
savePasswordHint=Copie su contraseña. La necesitará para configurar una conexión para este esquema. Si olvidó su contraseña, puede abrir este diálogo para restablecerla.
#XFLD,50: Hint on entering schema name in creation dialog
schemaNameHint=No puede cambiar el nombre luego de crear esquema.
#XFLD: Open SQL Schemas section sub headline
schemasSQL=SQL abierto
#XFLD: Space schema section sub headline
schemasSpace=Espacio
#XFLD: HDI Container section header
HDIContainers=Contenedores HDI
#XTXT: Add HDI Containers button tooltip
addHDIContainers=Agregar contenedores HDI
#XTXT: Remove HDI Containers button tooltip
removeHDIContainers=Quitar contenedores HDI
#XFLD: Enable HDI Containers feature button
enableHDIContainersButton=Habilitar acceso
#YMSE: No data text for HDI Containers table
noDataHDIContainers=No se agregaron contenedores HDI.
#YMSE: No data text for Timedata section
noDataTimedata=No se crearon dimensiones ni tablas de tiempo.
#YMSE: No data text during runtime not available for Timedata section
runtimeNotAvailableNoDataTimedata=No se pueden cargar tablas y dimensiones de tiempo debido a que la base de datos de tiempo de ejecución no está disponible.
#YMSE: No data text during runtime not available for HDI section
runtimeNotAvailableNoDataHDI=No se pueden cargar contenedores HDI debido a que la base de datos de tiempo de ejecución no está disponible.
#YMSE: Error while fetching HDI Containers
fetchHDIContainersError=No se pudieron obtener los contenedores HDI. Vuelva a intentar más tarde.
#XFLD Table column header for HDI Container names
HDIContainerName=Nombre de contenedor HDI
#XFLD: Enable HDI Containers feature dialog title
enableHDIContainersDialogTitle=Habilitar acceso
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText1New=Puede habilitar SAP SQL Data Warehousing en su inquilino de SAP Datasphere para intercambiar datos entre los contenedores de HDI y los espacios de SAP Datasphere sin necesidad de mover los datos.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText2=Para ello, haga clic en el botón siguiente para abrir un ticket de soporte.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText3New=Una vez que el ticket se haya procesado, debe crear uno o más contenedores de HDI nuevos en la base de datos en tiempo de ejecución de SAP Datasphere. A continuación, el botón Habilitar acceso se reemplaza por el botón + en la sección Contenedores de HDI para todos los espacios de SAP Datasphere, y puede agregar los contenedores a un espacio.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText41=¿Necesita más información? Vaya a %%0. Para obtener información detallada sobre qué incluir en el ticket, consulte %%1.
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkText=Ayuda de SAP
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkTextSapNote3057059=Nota de SAP 3057059
#XBUT: Open Ticket Button Text
openTicket=Abrir ticket
#XBUT: Add Button Text
add=Agregar
#XBUT: Next Button Text
next=Siguiente
#XBUT: Edit Button Text
editUsers=Editar
#XBUT: create user Button Text
createUser=Crear
#XBUT: Update user Button Text
updateUser=Seleccionar
#XTIT: Add HDI Containers Dialog Title
addHDIContainersDialogTitle=Agregar contenedores HDI no asignados
#XFLD: No data text for add HDI container dialog
addHDIContainersDialogNoDataText=No pudimos encontrar ningún contenedor no asignado. \n Es posible que el contenedor que está buscando ya esté asignado a un espacio.
#YMSE: Error while loading assigned HDI Containers
loadAssignedHDIContainersError=No se pudieron cargar los contenedores HDI asignados.
#YMSE: Error while loading assigned HDI Containers
loadUnassignedHDIContainersError=No se pudieron cargar los contenedores HDI.
#XMSG: Success message
succeededToAddHDIContainer=Contenedor HDI agregado
#XMSG: Success message
succeededToAddHDIContainerPlural=Contenedores HDI agregados
#XMSG: Success message
succeededToDeleteHDIContainer=Contenedor HDI quitado
#XMSG: Success message
succeededToDeleteHDIContainerPlural=Contenedores HDI quitados
#XFLD: Time data section sub headline
timeDataSection=Dimensiones y tablas de tiempo
#XFLD: Schemas list entry read audit log enabled indicator
readIndicator=Leer
#XFLD: Schemas list entry change audit log enabled indicator
changeIndicator=Cambio
#XFLD: Remote sources section sub headline
allconnections=Asignación de conexión
#XFLD: Remote sources section sub headline
localconnections=Conexiones locales
#XFLD: Remote sources section sub headline for open Connectors
openConnectors=SAP Open Connectors
#XFLD: User section sub headline
memberassignment=Asignación de miembro
#XFLD: User assignment section sub headline
userAssignment=Asignación de usuario
#XFLD: User section Access dropdown Member
member=Miembro
#XFLD: User assignment section column name
user=Nombre de usuario
#XTXT: Selected role count
selectedRoleToolbarText=Seleccionado(s): {0}
#XTIT: Space detail section connections title
detailsSectionConnections=Conexiones
#XTIT: Space detail section data access title
detailsSectionDataAccess=Acceso a esquema
#XTIT: Space detail section time data title
detailsSectionGenerateData=Datos de tiempo
#XTIT: Space detail section members title
detailsSectionUsers=Miembros
#XTIT: Space detail section Users title
detailsSectionUsersTitle=Usuarios
#XTIT: Out of Storage
insufficientStoragePopoverTitle=Sin almacenamiento
#XTIT: Storage distribution
storageDistributionPopoverTitle=Almacenamiento de disco usado
#XTXT: Out of Storage popover text
insufficientStorageText=Para crear un espacio nuevo, reduzca el almacenamiento asignado de otro espacio o elimine un espacio que ya no necesite. Puede aumentar su almacenamiento de sistema total si llama a Administrar plan.
#XMSG: Space id length warning
spaceIdLengthWarning=Se excedió el máximo de {0} caracteres.
#XMSG: Space name length warning
spaceNameLengthWarning=Se excedió el máximo de {0} caracteres.
#XMSG: Space id reserved prefix warning
spaceIdnWarningReservedPrefix=No use el prefijo {0} para evitar posibles conflictos.
#YMSE: Error while loading open SQL schemas
loadLocalSchemasError=No se pudieron cargar los esquemas de SQL abiertos.
#YMSE: Error while creating open SQL schema
createLocalSchemaError=No se pudo crear el esquema de SQL abierto.
#YMSE: Error while loading all remote connections
loadRemoteConnectionsError=No se pudieron cargar todas las conexiones remotas.
#YMSE: Error while loading space details
loadSpaceDetailsError=No se pudieron cargar los detalles de espacio.
#YMSE: Error while deploying space details
deploySpaceDetailsError=El espacio no se pudo implementar.
#YMSE: Error while copying space details
copySpaceDetailsError=El espacio no se pudo copiar.
#YMSE: Error while loading storage data
loadStorageDataError=No se pudieron cargar los datos de almacenamiento.
#YMSE: Error while loading all users
loadAllUsersError=No se pudieron cargar todos los usuarios.
#YMSE: Failed to reset password
resetPasswordError=No se pudo restablecer la contraseña.
#XMSG: Success message after updating space
resetPasswordSuccessMessage=Nueva contraseña definida para el esquema
#YMSE: DP Agent-name too long
DBAgentNameError=El nombre del agente DP es demasiado largo.
#YMSE: Schema-name not valid.
schemaNameError=El nombre del esquema no es válido.
#YMSE: User name not valid.
UserNameError=El nombre del usuario no es válido.
#XTIT: Consumption by Storage Type
consumptionStorageTypeText=Consumo por tipo de almacenamiento
#XTIT: Consumption by Schema
consumptionSchemaText=Consumo por esquema
#XTIT: Overall Consumption by schema
overallConsumptionSchemaText=Consumo de tabla total por esquema
#XTIT: Overall Consumption by Table Type
overallConsumptionTableTypeText=Consumo total por tipo de tabla
#XTIT: Tables
tableDetailsText=Detalles de tabla
#XTIT: Table Storage Consumption
tableStorageConsumptionText=Consumo de almacenamiento de tabla
#XFLD: Table Type label
tableTypeLabel=Tipo de tabla
#XFLD: Schema label
schemaLabel=Esquema
#XFLD: reset table tooltip
resetTable=Restablecer tabla
#XFLD: In-Memory label in space monitor
inMemoryLabel=Memoria
#XFLD: Disk label in space monitor
diskLabel=Disco
#XFLD: Yes
yesLabel=Sí
#XFLD: No
noLabel=No
#XFLD: switch label: Want the data in this space to be consumable by default?
allowConsumptionByDefault=¿Desea que los datos en este espacio se puedan consumir de manera predeterminada?
#XFLD: Business Name
businessNameLabel=Nombre empresarial
#XFLD: Refresh
refresh=Actualizar
#XMSG: No filter results title
noFilterResultsTitle=Parece que la configuración de su filtro no muestra ningún dato.
#XMSG: No filter results message
noFilterResultsMsg=Intente redefinir la configuración de su filtro y, si aún no ve ningún dato, cree algunas tablas en el generador de datos. Una vez que consuman almacenamiento, podrá supervisarlas aquí.
#XMSG: Space monitoring Hana state red title
spaceMonitorHanaRedTitle=La base de datos en tiempo de ejecución no está disponible.
#XMSG: Space monitoring Hana state red message
spaceMonitorHanaRedMsg=Dado que la base de datos en tiempo de ejecución no está disponible, algunas funciones están inhabilitadas y no podemos mostrar ninguna información en esta página.
#YMSE: Error while creating space schema user
createSpaceSchemaUserError=No se pudo crear el usuario de esquema de espacio.
#YMSE: Error User name already exists
userAlreadyExistsError=El nombre de usuario ya existe.
#YMSE: Error Authentication failed
authenticationFailedError=Falló la autenticación.
#XMSG: Message strip message when a schema user is locked
userLockedMessageStrip=El usuario está bloqueado debido a demasiados inicios de sesión fallidos. Solicite una nueva contraseña para desbloquear el usuario.
#XMSG: Success message after reset and unlock schema user
resetUnlockPasswordSuccessMessage=Nueva contraseña definida y usuario desbloqueado
#XMSG: user is locked message
userLockedMessage=El usuario está bloqueado.
#XCOL: Users table-view column Role
spaceRole=Función
#XCOL: Users table-view column Scoped Role
spaceScopedRole=Rol con alcance
#XCOL: Users table-view column Space Admin
spaceAdmin=Administrador de espacio
#XFLD: User section dropdown value Viewer
viewer=Visor
#XFLD: User section dropdown value Modeler
modeler=Modelador
#XFLD: User section dropdown value Data Integrator
dataIntegrator=Integrador de datos
#XFLD: User section dropdown value Space Administrator
spaceAdministrator=Administrador de espacio
#XMSG: Success message -  Space Role updated successfully.
updateUserSpaceRoleSuccess=Rol de espacio actualizado
#XMSG: Error message - Failed to update Space Role.
updateUserSpaceRoleError=La función de espacio no se actualizó correctamente.
#XFLD:
databaseUserNameSuffix=Sufijo de nombre de usuario de base de datos
#XTXT: Space Schema password text
spaceSchemaPasswordText=A fin de configurar una conexión para este esquema, copie su contraseña. En caso de que la haya olvidado, puede solicitar una nueva.
#XFLD: Cloud Platform label in space schema dialog
cloudPlatformLabel=Cloud Platform
#XTXT: Cloud Platform text in space schema dialog
cloudPlatformText=Para configurar el acceso a través de este usuario, habilite el consumo y copie las credenciales. En caso de que solo pueda copiar las credenciales sin una contraseña, asegúrese de agregar la contraseña más tarde.
#XFLD: enable consumption in CloudPlatform label in space schema dialog
enableConsumptionInCloudPlatformLabel=Habilitar el consumo en Cloud Platform
#XFLD: Credentials for User-Provided Service label in space schema dialog
cloudPlatformCredentialsLabel=Credenciales para el servicio provisto por el usuario:
#XFLD: Credentials for User-Provided Service (Without Password) label in space schema dialog
cloudPlatformCredentialsWithoutPasswordLabel=Credenciales para el servicio provisto por el usuario (sin contraseña):
#XFLD: Copy Credentials label in space schema dialog
copyCredentialsLabel=Copiar credenciales sin contraseña
#XFLD: Copy Credentials label in space schema dialog
copyFullCredentialsLabel=Copiar credenciales completas
#XFLD: Copy Password label in space schema dialog
copyPasswordLabel=Copiar contraseña
#XMSG: Credentials copied to clipboard
credentialsCopiedMessage=Credenciales copiadas en el portapapeles
#XMSG: Password copied to clipboard
passwordCopiedMessage=Contraseña copiada en el portapapeles
#XMSG: create database user dialog title
createDatabaseUserDialogTitle=Crear usuario de base de datos
#XMSG: Database Users section title
databaseUsers=Usuarios de base de datos
#XMSG: Database User Details dialog title
databaseUserDetailsDialogTitle=Detalles de usuario de base de datos
#XFLD: database user read audit log
databaseUserAuditLogRead=Habilitar registros de auditoría para operaciones de lectura y conservar los registros durante
#XFLD: database user change audit log
databaseUserAuditLogChange=Habilitar registros de auditoría para operaciones de cambio y conservar los registros durante
#XMSG: Cloud Platform Access
cloudPlatformAccess=Acceso a Cloud Platform
#XFLD: Enable Consumption in Cloud Platform hint
enableConsumptionCloudPlatformHint=Configure el acceso a su contenedor de HANA Deployment Infrastructure (HDI) a través de este usuario de base de datos. Para conectarse a su contenedor de HDI, se debe activar el modelado de SQL.
#XFLD: Enable HDI Consumption
enableConsumptionCloudPlatform=Habilitar consumo de HDI
#XFLD: Enable Consumption hint
enableConsumptionHint=¿Desea que otras herramientas o aplicaciones puedan consumir los datos en su espacio?
#XFLD: Enable Consumption
enableConsumption=Habilitar consumo de SQL
#XFLD: Enable Modeling
enableModeling=Habilitar modelado de SQL
#XMSG: Privileges for Data Modeling
privilegesModeling=Ingesta de datos
#XMSG: Privileges for Data Consumption
privilegesConsumption=Consumo de datos para herramientas externas
#XFLD: SQL Modeling
sqlModeling=Modelado de SQL
#XFLD: SQL Consumption
sqlConsumption=Consumo de SQL
#XFLD: enabled
enabled=Habilitado
#XFLD: disabled
disabled=Inhabilitado
#XFLD: Edit Privileges
editPrivileges=Editar privilegios
#XFLD: Open Database Explorer
openDBX=Abrir el explorador de base de datos
#XFLD: create database user hint
databaseCreateHint=Tenga en cuenta que no podrá cambiar su nombre una vez que lo haya guardado.
#XFLD: Internal Schema Name
internalSchemaName=Nombre de esquema interno
#YMSE: Failed to load database users
loadDatabaseUserError=Se produjo un error al cargar los usuarios de base de datos
#YMSE: Failed to delete database user
deleteDatabaseUsersError=Se produjo un error al eliminar los usuarios de base de datos
#XMSG: Success message after deleting database user
deleteDatabaseUserSuccess=Se eliminó el usuario de base de datos
#XMSG: Success message after deleting database users
deleteDatabaseUsersSuccess=Se eliminaron los usuarios de base de datos
#XMSG: Success message after creating database user
createDatabaseUserSuccessMessage=Se creó el usuario de base de datos
#YMSE: Failed to create database user
createDatabaseUserError=Se produjo un error al crear el usuario de base de datos
#XMSG: Success message after updating database user
updateDatabaseUserSuccessMessage=Se actualizó el usuario de base de datos
#YMSE: Failed to update database user
updateDatabaseUserError=Se produjo un error al actualizar el usuario de base de datos
#XFLD: HDI Consumption
hdiConsumption=Consumo de HDI
#XTIT: Space detail section Database Access title
detailsSectionDatabaseAccess=Acceso a la base de datos
#XTXT: Database users consumption hint
databaseUsersConsumptionHint=Haga que los datos de espacio se puedan consumir de manera predeterminada. Los modelos en los generadores permitirán de manera automática que los datos se puedan consumir.
#XFLD: Default Consumption of Space Data:
defaultConsumptionOfSpaceData=Consumo predeterminado de datos de espacio:
#XFLD: Database User Name
databaseUserName=Nombre de usuario de base de datos
#XMSG: Database User creation validation error message
databaseUserValidationError=Parece que algunos campos no son válidos. Verifique los campos requeridos para continuar.
#XMSG: SQL modeling can’t be enabled since this user has been migrated.
migratedDatabaseUserHint=No se puede habilitar la introducción de datos porque el usuario ha sido migrado.
#XBUT: Remove Button Text
remove=Quitar
#XBUT: Remove Spaces Button Text
removeSpaces=Quitar espacios
#XBUT: Remove Objects Button Text
removeObjects=Quitar objetos
#XMSG: No members have been added yet.
noMembersAssigned=Aún no se han agregado miembros.
#XMSG: No users have been added yet.
noUsersAssigned=Aún no se han agregado usuarios.
#XMSG: No database users have been created or your filter is not showing any data.
noDatabaseUsers=No se crearon usuarios de base de datos o su filtro no muestra datos.
#XMSG: Please enter a user name.
noDatabaseUsername=Ingrese un nombre de usuario.
#XMSG: The user name is too long. Please use a shorter one.
databaseUsernameTooLong=El nombre de usuario es demasiado largo. Utilice uno más corto.
#XMSG: No privileges have been enabled and this database user will have limited functionality. Do you still want to continue?
noPrivilegesConfirmationMessage=No se han habilitado privilegios y este usuario de base de datos tendrá funcionalidad limitada. ¿Aún desea continuar?
#XMSG: To enable audit logs for change operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogChangeMessage=A fin de habilitar registros de auditoría para las operaciones de cambio, también se debe habilitar la introducción de datos. ¿Desea hacerlo?
#XMSG: To enable audit logs for read operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogReadMessage=A fin de habilitar registros de auditoría para las operaciones de lectura, también se debe habilitar la introducción de datos. ¿Desea hacerlo?
#XMSG: To enable HDI consumption data ingestion and data consumption needs to be enabled. Do you want to enable all?
enableHDIConsumptionMessage=A fin de habilitar el consumo de HDI, también se debe habilitar la introducción y el consumo de datos. ¿Desea hacerlo?
#XMSG:
databaseUserPasswordText=A fin de configurar una conexión para este usuario de base de datos, copie su contraseña. En caso de que la haya olvidado, siempre puede solicitar una nueva.
#XTIT: Space detail section members title
detailsSectionMembers=Miembros
#XMSG: New password set
newPasswordSet=Nueva configuración de contraseña
#XFLD: Data Ingestion
dataIngestion=Ingesta de datos
#XFLD: Data Consumption
dataConsumption=Consumo de datos
#XFLD: Privileges
privileges=Privilegios
#XFLD: Enable Data ingestion
enableDataIngestion=Habilitar introducción de datos
#XMSG: Log the read and change operations for data ingestion.
databaseUserAuditHint=Registre las operaciones de cambio y lectura para la introducción de datos.
#XMSG: Make your space data available in your HDI containers.
databaseUserHDIHint=Haga que sus datos de espacio estén disponibles en sus contenedores de HDI.
#XFLD: Enable Data consumption
enableDataConsumption=Habilitar consumo de datos
#XMSG: Allow other apps or tools to consume your space data.
enableDataConsumptionHint=Permitir que otras aplicaciones o herramientas consuman sus datos de espacio.
#XMSG: To set up access via this schema user copy the credentials to your user-provided service. In case you can only copy the credentials without a password make sure to add the password later.
databaseUserHDIConsumptionHint=Para configurar el acceso a través de este usuario de base de datos y copie las credenciales a su servicio provisto por el usuario. En caso de que solo pueda copiar las credenciales sin una contraseña, asegúrese de agregar la contraseña más tarde.
#XFLD: Data Flow Runtime Capacity label in space overview
dataFlowRuntimeCapacity=Capacidad de tiempo de ejecución del flujo de datos ({0}:{1} horas de {2} horas)
#XFLD: Could not load Data Flow Runtime Capacity
dataFlowRuntimeCapacityError=No se pudo cargar la capacidad de tiempo de ejecución del flujo de datos
#XMSG: User can grant data consumption to other users.
databaseUserConsumptionWithGrantOptionHint=El usuario puede otorgar consumo de datos a los demás usuarios.
#XFLD: Enable data consumption with grant option
enableConsumptionWithGrantOption=Habilitar consumo de datos con opción de otorgar
#XMSG: To enable data consumption with grant option data consumption needs to be enabled. Do you want to enable both?
enableConsumptionWithGrantMessage=Para habilitar el consumo de datos con la opción de otorgarlos, es necesario habilitar el consumo. ¿Desea habilitar ambos?
#XFLD: Enable Automated Predictive Library (APL) and Predictive Analysis Library (PAL)
allowUsingScriptServerFunctionality=Habilitar la biblioteca predictiva automatizada (APL) y la biblioteca de análisis predictivo (PAL)
#XMSG: User can use SAP HANA Cloud embedded machine learning functions.
allowUsingScriptServerFunctionalityHint=El usuario puede emplear las funciones de aprendizaje automático integradas de SAP HANA Cloud.
#XFLD: Password Policy
passwordPolicy=Política de contraseña
#XMSG: Password Policy
passwordPolicyHint=Habilite o inhabilite aquí la política de contraseña configurada.
#XFLD: Enable Password Policy
enablePasswordPolicy=Habilitar política de contraseña
#XMSG: Read Access to the Space Schema
readAccessTitle=Acceso de lectura al esquema de espacio
#XMSG: read access hint
readAccessHint=Permita que el usuario de base de datos conecte las herramientas externas al esquema de espacio y lea las vistas que están expuestas para consumo.
#XFLD: Space Schema
spaceSchema=Esquema de espacio
#XFLD: Enable Read Access (SQL)
enableReadAccess=Habilitar acceso de lectura (SQL)
#XMSG: Allow the user to grant read access to other users.
withGrantOptionHint=Permita que el usuario otorgue acceso de lectura a otros usuarios.
#XFLD: With Grant Option
withGrantOption=Con opción para otorgar acceso
#XMSG: Make your space data available in your HDI containers.
enableHDIConsumptionHint=Haga que sus datos de espacio estén disponibles en sus contenedores de HDI.
#XFLD: Enable HDI Consumption
enableHDIConsumption=Habilitar consumo de HDI
#XMSG: Write Access to the User’s Open SQL Schema
writeAccessTitle=Acceso de escritura al esquema de SQL abierto del usuario
#XMSG: write access hint
writeAccessHint=Permita que el usuario de base de datos conecte las herramientas externas al esquema de SQL abierto del usuario para crear entidades de datos e introducir datos a fin de usarlos en el espacio.
#XFLD: Open SQL Schema
openSQLSchema=Esquema de SQL abierto
#XFLD: Enable Write Access (SQL DDL & DML)
enableWriteAccess=Habilitar acceso de escritura (SQL, DDL y DML)
#XMSG: audit hint
auditHint=Registre las operaciones de lectura y cambio en el esquema de SQL abierto.
#XMSG: data consumption hint
dataConsumptionHint=Exponga todas las vistas nuevas en el espacio de manera predeterminada para consumo. Los modeladores pueden ignorar esta configuración para las vistas individuales mediante el conmutador "Exposición para consumo" en el panel lateral de resultados de vista. También puede elegir los formatos en los que se exponen las vistas.
#XFLD: Expose for Consumption by Default
exposeDataConsumption=Exponer para consumo de manera predeterminada
#XMSG: database users hint consumption hint
databaseUsersHint2New=Cree usuarios de base de datos para conectar las herramientas externas a SAP Datasphere. Establezca privilegios para permitir que los usuarios lean los datos del espacio, y creen entidades de datos (DDL) e introduzcan datos (DML) para usarlos en el espacio.
#XFLD: Read
read=Leer
#XFLD: Read (HDI)
readHDI=Leer (HDI)
#XFLD: Write
write=Escribir
#XMSG: HDI Containers Hint
HDIContainersHint2=Habilite el acceso a los contenedores de la infraestructura de SAP HANA Deployment Infrastructure (HDI) en su espacio. Los modeladores pueden usar los artefactos de HDI como orígenes para vistas, y los clientes de HDI pueden acceder a los datos de su espacio.
#XMSG: Open info dialog
openDatabaseUserInfoDialog=Abrir diálogo de información
#XMSG: Database user is locked. Open info dialog to unlock
openLockedDatabaseUserInfoDialog=El usuario de la base de datos está bloqueado. Para desbloquearlo, abra el diálogo.
#XFLD: Table
table=Tabla
#XFLD: Space section partner configuration title
partnerConnectionConfigurationSectionTitle=Conexión del socio
#XFLD: Space section partner configuration subtitle
partnerConnectionConfigurationSectionSubTitle=Configuración de la conexión del socio
#XFLD: Space section partner configuration hint
partnerConnectionConfigurationSectionHint=Para definir su propio mosaico de conexión del socio, agregue su ícono y URL de iFrame. Esta configuración solo está disponible para este inquilino.
#XFLD: Table Name Field
partnerConnectionConfigurationName=Nombre de mosaico
#XFLD: Table iFrame URL field
partnerConnectionConfigurationiFrameURL=URL de iFrame
#XFLD: Table iFrame Post Message Origin field
partnerConnectionConfigurationiFramePostMessageOrigin=Origen del mensaje de publicación de iFrame
#XFLD: Table icon/image name
partnerConnectionConfigurationIcon=Ícono
#XMSG: Table no result found
partnerConnectionConfigurationNotfound=No se pudieron encontrar configuraciones de la conexión del socio.
#XMSG: Partner Connection Confgurations are not available
partnerConnectionConfigurationNotAvailable=Las configuraciones de la conexión del socio no se pueden mostrar cuando la base de datos de tiempo de ejecución no está disponible.
#XFLD: Dialog Create Partner Connection Configuration
partnerConnectionConfigurationDialogTitleAdd=Crear configuración de la conexión del socio
#XFLD: Dialog Create Partner Connection Configuration Upload Icon
partnerConnectionConfigurationButtonUloadPartnerIcon=Subir ícono
#XFLD: Dialog Create Partner Connection Configuration Select
partnerConnectionConfigurationSelectIcon=Seleccionar (tamaño máximo de 200 KB)
#XFLD: Dialog Create Partner Connection Configuration Enter a name
partnerConnectionConfigurationNamePlaceholder=Ejemplo de mosaico de socio
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL
partnerConnectionConfigurationiFrameURLPlaceholder=http://www.ejemplo.com
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL Check
partnerConnectionConfigurationiFramePostMessageOriginPlaceholder=.ejemplo.com
#XFLD: Dialog Create Partner Connection Configuration Browse
partnerConnectionConfigurationBrowseButton=Examinar
#XFLD: Create Partner Connection Configuration Msg
partnerConnectionConfigurationUploadMsg=La configuración de la conexión del socio se creó correctamente.
#XMSG: Error while deleting Partner Connection configuration(s)
partnerConnectionConfigurationDeleteError=Se produjo un error al eliminar la configuración de la conexión del socio.
#XMSG: Partner Connection Configuration has been successfully deleted
partnerConnectionConfigurationDeleteSuccess=La configuración de la conexión del socio se eliminó correctamente.
#XMSG: Error while retrieving Partner Connection Configurations
partnerConnectionConfigurationLoadError=Se produjo un error al recuperar las configuraciones de la conexión del socio.
#XMSG: Error fileSizeLimitExceeded
partnerConnectionConfigurationfileSizeLimitExceeded=No se puede cargar el archivo porque excede el tamaño máximo de 200 KB.
#XBUT: Create Partner Connection Configuration
partnerConnectionConfigurationButtonCreate=Crear configuración de la conexión del socio
#XBUT: Delete Partner Connection Configuration
partnerConnectionConfigurationButtonDelete=Eliminar configuración de la conexión del socio
#XMSG: Partner Tile Creation Failure
upsertPartnerTileFailed=No se pudo crear el mosaico del socio.
#XMSG: Partner Tile Deletion Failure
deletePartnerTileFailed=No se pudo eliminar el mosaico del socio.
#XMSG: HANA cloud connector settings reset failure
resetHANASCCSettingsFailed=Se produjo un error en el restablecimiento de las configuraciones del cliente en SAP HANA Cloud Connector.
#XFLD: Workload Class
workloadClass=Clase de carga de trabajo
#XFLD: Workload Management
workloadManagement=Administración de las cargas de trabajo
#XFLD: Priority
workloadClassPriority=Prioridad
#XMSG:
workloadManagementPriorityHint=Puede especificar la priorización de este espacio cuando consulte la base de datos. Ingrese un valor de 1 (prioridad más baja) a 8 (prioridad más alta). En una situación en la que los espacios compiten por los subprocesos disponibles, aquellos con las prioridades más altas se ejecutan antes que aquellos con las prioridades más bajas.
#XMSG:
workloadClassPriorityHint=Puede especificar la prioridad de espacio de 0 (prioridad más baja) a 8 (más alta). Las instrucciones de un espacio con una prioridad alta se ejecutan antes que las instrucciones de otros espacios con una prioridad más baja. La prioridad predeterminada es 5. El valor de prioridad 9 se reserva para operaciones del sistema; no está disponible para un espacio.
#XFLD: Statement Limits
workloadclassStatementLimits=Límites de instrucciones
#XFLD: Workload Configuration
workloadConfiguration=Configuración de carga de trabajo
#XMSG:
workloadClassStatementLimitsHint=Puede especificar el número máximo (o porcentaje) de subprocesos y GB de memoria que pueden consumir las instrucciones que se ejecutan en simultáneo. Puede ingresar cualquier valor o porcentaje entre 0 (sin límite) y el total disponible de memoria y subprocesos en el inquilino. \n\n Si especifica un límite de subprocesos, tenga en cuenta que puede generar una pérdida de rendimiento. \n\n Si especifica un límite de memoria, las instrucciones que alcanzan el límite de memoria no se ejecutarán.
#XMSG:
workloadClassStatementLimitsDescription=La configuración predeterminada proporciona límites de recursos generosos, a la vez que evita que un único espacio sobrecargue el sistema.
#XMSG:
workloadClassStatementLimitCustomDescription=Puede configurar límites de memoria y subprocesos totales máximos que pueden consumir las instrucciones que se ejecutan en simultáneo en el espacio.
#XMSG:
totalStatementThreadLimitHelpText=La configuración de un límite de subprocesos demasiado bajo puede impactar en el rendimiento de la instrucción, mientras que valores excesivamente altos o 0 pueden permitir que el espacio consuma todos los subprocesos disponibles del sistema.
#XMSG:
totalStatementMemoryLimitHelpText=La configuración de un límite de subprocesos demasiado bajo puede causar problemas de memoria insuficiente, mientras que valores excesivamente altos o 0 pueden permitir que el espacio consuma toda la memoria disponible del sistema.
#XMSG:
totalStatementThreadLimitHelpTextRestricted=Ingrese un porcentaje entre 1 % y 70 % (o el número equivalente) del número total de subprocesos disponibles en su inquilino. La configuración del límite de subprocesos en un valor demasiado bajo podría impactar en el rendimiento de la instrucción, mientras que valores excesivamente altos podrían impactar en el rendimiento de las instrucciones en otros espacios.
#XMSG:
totalStatementThreadLimitHelpTextDynamic=Ingrese un porcentaje entre 1 % y {0} % (o el número equivalente) del número total de subprocesos disponibles en su inquilino. La configuración del límite de subprocesos en un valor demasiado bajo podría impactar en el rendimiento de la instrucción, mientras que valores excesivamente altos podrían impactar en el rendimiento de las instrucciones en otros espacios.
#XMSG:
totalStatementMemoryLimitHelpTextNew=Ingrese un valor o porcentaje entre 0 (sin límite) y la cantidad total de memoria disponible en su inquilino. La configuración del límite de memoria en un valor demasiado bajo podría impactar en el rendimiento de la instrucción, mientras que valores excesivamente altos podrían impactar en el rendimiento de las instrucciones en otros espacios.
#XFLD: Total Statement Thread Limit
totalStatementThreadLimit=Límite de subprocesos de instrucciones totales
#XFLD: Segmented button label
workloadclassPercentage=%
#XFLD: Segmented button label
workloadclassGigaByte=GB
#XFLD, 80: Segmented button label
workloadclassThreads=Subprocesos
#XFLD: Total Statement Memory Limit
totalStatementMemoryLimit=Límite de memoria de instrucciones total
#YMSE: Error while loading customer hana info
loadCustomerHanaInfoError=Error al cargar la información de SAP HANA del cliente.
#XMSG:
minimumLimitReached=Se alcanzó el límite mínimo.
#XMSG:
maximumLimitReached=Se alcanzó el límite máximo.
#XMSG: Name Taken for Technical Name
technical-name-taken=Ya existe una conexión con el nombre técnico que ingresó. Ingrese otro nombre.
#XMSG: Name Too long for Technical Name
technical-name-too-long=El nombre técnico que ingresó tiene más de 40 caracteres. Ingrese un nombre con menos caracteres.
#XMSG: Technical name field empty
technical-name-field-empty=Ingrese un nombre técnico.
#XMSG: Generic error when user enters invalid characters for a field
field-invalid-characters=Aquí solo puede usar letras (a-z), números (0-9) y guiones bajos (_) para el nombre.
#XMSG: Generic error when user enters invalid initial characters for a field
field-underscore-trim=El valor no puede comenzar o finalizar con un guion bajo (_).
#XFLD: Checkbox label for Enable Statement Limits
enableStatementLimits=Habilitar límites de instrucción
#XFLD: Connection SubSection Settings Title in space details
connectionSettings=Opciones
#XMSG: Connections tool hint in Space details section
connectionsToolHint=Para crear o editar conexiones, abra la aplicación Conexiones desde la navegación lateral o haga clic aquí:
#XBUT: Connection list Open Connection Tool Button
button-connectiontool=Ir a Conexiones
#XFLD: Not deployed label on space tile
notDeployedLabel=El espacio aún no se implementó.
#XFLD: Not deployed additional text on space tile
notDeployedText=Implemente el espacio.
#XFLD: Corrupt space label on space tile
corruptSpace=Se produjo un error.
#XFLD: Corrupt space additional text on space tile
corruptSpaceHint=Intente volver a implementar o comuníquese con soporte
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Datos de registro de auditoría
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Datos administrativos
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Otros datos
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Datos en espacios
#XMSG: Unlock Space confirmation text
unlockSpaceConfirmation=¿Seguro que desea desbloquear el espacio?
#XMSG: Lock Space confirmation text
lockSpaceConfirmation=¿Seguro que desea bloquear el espacio?
#XFLD: Lock
lock=Bloquear
#XFLD: Unlock
unlock=Desbloquear
#XFLD: Locking
locking=Bloqueo
#XMSG: Success message after locking space
lockSpaceSuccessMsg=Espacio bloqueado
#XMSG: Success message after unlocking space
unlockSpaceSuccessMsg=Espacio desbloqueado
#XMSG: Success message after locking spaces
lockSpacesSuccessMsg=Espacios bloqueados
#XMSG: Success message after unlocking spaces
unlockSpacesSuccessMsg=Espacios desbloqueados
#YMSE: Error while locking a space
lockSpaceError=El espacio no se puede bloquear.
#YMSE: Error while unlocking a space
unlockSpaceError=El espacio no se puede desbloquear.
#XTIT: popup title Warning
confirmationWarningTitle=Advertencia
#XMSG: Message strip message when a space was locked manually
manualLockedMessage=El espacio se ha bloqueado manualmente.
#XMSG: Message strip message when a space was locked due to the amount of generated audit logs
auditLogLockedMessage=El sistema ha bloqueado el espacio porque los registros de auditoría consumen una gran cantidad de GB del disco.
#XMSG: Message strip message when a space was locked because the assigned quota was exceeded.
quotaLockedMessage=El sistema bloqueó el espacio porque excede las asignaciones de almacenamiento de memoria o disco.
#XMSG: Unlock Spaces confirmation text
unlockSpacesConfirmation=¿Seguro que desea desbloquear los espacios seleccionados?
#XMSG: Lock Spaces confirmation text
lockSpacesConfirmation=¿Seguro que desea bloquear los espacios seleccionados?
#XTIT: Scoped role editor dialog title
scopedRoleEditorDialogTitle=Editor de rol con alcance
#XTIT: ECN Management title
ecnManagementTitle=Administración de espacios y nodos de procesamiento elástico
#XFLD: ECNs
ecns=Nodos de procesamiento elástico
#XFLD: ECN phase Ready
ecnReady=Listo
#XFLD: ECN phase Running
ecnRunning=En ejecución
#XFLD: ECN phase Initial
ecnInitial=No listo
#XFLD: ECN phase Starting
ecnStarting=Iniciando
#XFLD: ECN phase Starting Failed
ecnStartingFailed=Error de inicio
#XFLD: ECN phase Stopping
ecnStopping=Detención en curso
#XFLD: ECN phase Stopping Failed
ecnStoppingFailed=Error de detención
#XBTN: Assign Button
assign=Asignar espacios
#XBTN: Start Header-Button
start=Iniciar
#XBTN: Update Header-Button
repair=Actualizar
#XBTN: Stop Header-Button
stop=Detener
#XFLD: ECN hours remaining
ecnHoursRemaining=1000 horas restantes
#XFLD: ECN block-hours remaining
ecnBlockHoursRemaining={0} horas de bloque restantes
#XFLD: ECN block-hour remaining
ecnBlockHourRemaining={0} hora de bloque restante
#XTIT: ECN creation dialog title
ecnCreationDialogTitle=Crear nodo de procesamiento elástico
#XTIT: ECN edit dialog title
ecnEditDialogTitle=Editar nodo de procesamiento elástico
#XTIT: ECN deletion dialog title
ecnDeletionDialogTitle=Eliminar nodo de procesamiento elástico
#XTIT: ECN Space assignment dialog title
ecnSpaceAssignmentDialogTitle=Asignar espacios
#XFLD: ECN ID
ECNIDLabel=Nodo de procesamiento elástico
#XTXT: Selected toolbar text
selectedToolbarText=Seleccionado(s): {0}
#XTIT: Elastic Compute Nodes
ECNslong=Nodos de procesamiento elástico
#XTIT: Object assignment - Table header: Objects available
objectsAvailable=Número de objetos
#XTIT: Object assignment - Dialog header text
selectObjects=Seleccione los espacios y objetos que desea asignar al nodo de procesamiento elástico:
#XTIT: Object assignment - Table header title: Objects
objects=Objetos
#XTIT: Object assignment - Table header: Type
type=Tipo
#XMSG: Message strip delete database user audit warning
deleteDatabaseUserWarningMessageStrip=Tenga en cuenta que, si elimina un usuario de base de datos, se eliminarán todas las entradas de registro de auditoría generadas. Si desea conservar los registros de auditoría, considere exportarlos antes de eliminar el usuario de base de datos.
#XMSG: Message strip delete HDI container audit logs impact
deleteHDIContainerWarningMessageStrip=Tenga en cuenta que, si cancela la asignación de un contenedor de HDI del espacio, se eliminarán todas las entradas de registro de auditoría generadas. Si desea conservar los registros de auditoría, considere exportarlos antes de cancelar la asignación del contenedor de HDI.
#XTXT: All audit logs
allAuditLogs=Todas las entradas de registro de auditoría generadas para el espacio
#XMSG: Message strip disable audit warning
disableAuditWarningMessageStrip=Tenga en cuenta que, si deshabilita una política de auditoría (operaciones de lectura o cambio), se eliminarán todas las entradas de registro de auditoría. Si desea conservar las entradas de registro de auditoría, considere exportarlas antes de deshabilitar la política de auditoría.
#XTIT: No Spaces assigned to ECN Illustration title
noSpacesECNTitle=Aún no se asignó ningún espacio u objeto
#XTIT: No Spaces assigned to ECN Illustration description
noSpacesECNDescription=Para empezar a trabajar con el nodo de procesamiento elástico, asígnele un espacio u objeto.
#XTIT: No Spaces Illustration title
noSpacesTitle=Aún no se creó ningún espacio
#XTIT: No Spaces Illustration description
noSpacesDescription=Para comenzar a adquirir datos, cree un espacio.
#XTIT: No Spaces Illustration title
noDeletedSpacesTitle=La papelera de reciclaje está vacía
#XTIT: No Spaces Illustration description
noDeletedSpacesDescription=Puede recuperar los espacios eliminados desde aquí.
#XTXT:  {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDatabaseUsers=Una vez que se implemente el espacio, los siguientes usuarios de base de datos se eliminarán {0} y no se podrán recuperar:
#XTIT: Delete database users
deleteDatabaseUsersTitle=Eliminar usuarios de base de datos
#YMSE: Error if ecn id already exists
ecnIDInputDuplicateError=El ID ya existe.
#YMSE: Error if ecn id contains invalid characters
ecnIDInputInvalidError=Utilice solo caracteres en minúscula (a - z) y números (0 - 9).
#YMSE: Error if ecn id is too short
ecnIDInputTooShortError=El ID debe tener como mínimo {0} caracteres.
#XMSG: ecn id length warning
ecnIdLengthWarning=Se excedió el máximo de {0} caracteres.
#XFLD: open System Monitor
systemMonitor=Supervisión del sistema
#XFLD: open ECN schedule dialog menu entry
schedule=Programar
#XFLD: open create ECN schedule dialog
createSchedule=Crear programa
#XFLD: open change ECN schedule dialog
changeSchedule=Editar programa
#XFLD: open delete ECN schedule dialog
deleteSchedule=Eliminar programa
#XFLD: open self assign ECN schedule dialog
selfAssignSchedule=Asignarme programa
#XFLD: open pause ECN schedule dialog
pauseSchedule=Pausar programa
#XFLD: open resume ECN schedule dialog
resumeSchedule=Reanudar programa
#XFLD: View Logs
viewLogs=Ver registros
#XFLD: Compute Blocks
computeBlocks=Bloques de procesamiento
#XFLD: Memory label in ECN creation dialog
ecnMemory=Memoria (GB)
#XFLD: Storage label in ECN creation dialog
ecnStorage=Almacenamiento (GB)
#XFLD: Number of CPU label in ECN creation dialog
ecnCPUCount=Número de CPU
#XFLD: ECN updated by label
changedBy=Modificado por
#XFLD: ECN updated on label
changedOn=Fecha de modificación
#XMSG: Success message after creating a Elastic Compute Node
createEcnSuccessMessage=Nodo de procesamiento elástico creado
#YMSE: Error while creating a Elastic Compute Node
createEcnError=No se pudo crear el nodo de procesamiento elástico
#XMSG: Success message after updating a Elastic Compute Node
updateEcnSuccessMessage=Nodo de procesamiento elástico actualizado
#XMSG: Error while updating a Elastic Compute Node
udpateEcnError=No se pudo actualizar el nodo de procesamiento elástico
#XMSG: Success message after deleting a Elastic Compute Node
deleteEcnSuccessMessage=Nodo de procesamiento elástico eliminado
#YMSE: Error while deleting a Elastic Compute Node
deleteEcnError=No se pudo eliminar el nodo de procesamiento elástico
#XMSG: Success message after starting a Elastic Compute Node
startingEcnSuccessMessage=Iniciando nodo de procesamiento elástico
#XMSG: Success message after stopping a Elastic Compute Node
stoppingEcnSuccessMessage=Deteniendo nodo de procesamiento elástico
#YMSE: Error while starting a Elastic Compute Node
startingEcnError=No se pudo iniciar el nodo de procesamiento elástico
#YMSE: Error while stopping a Elastic Compute Node
stoppingEcnError=No se pudo detener el nodo de procesamiento elástico
#XBUT: Add Object button for an ECN
assignObjects=Agregar objetos
#XFLD: Enable auto object assignment label
enableAutoObjAssignment=Agregar todos los objetos automáticamente
#XFLD: object type label to be assigned
objectTypeLabel=Tipo (uso semántico)
#XFLD: assigned object type label
assignedObjectTypeLabel=Tipo
#XFLD: technical name label
TechnicalNameLabel=Nombre técnico
#XFLD: Object selecttion ECN label
objectSelectionECNLabel=Seleccione los objetos que quiere agregar al nodo de procesamiento elástico.
#XTIT: Add objects dialog title
assignObjectsTitle=Asignar objetos de
#XFLD: object label with object count
objectLabel=Objeto
#XMSG: No objects available to add message.
noObjectsToAssign=No hay objetos disponibles para asignar.
#XMSG: No objects assigned message.
noAssignedObjects=No hay objetos asignados.
#XTIT: Remove objects popup title
removeObjectsOrSpacesConfirmationTitle=Advertencia
#XTIT: Delete ECN popup button and title
deleteECNConfirmationButtonAndTitle=Eliminar
#XMSG: Remove objects popup text
removeObjectsConfirmation=¿Seguro que quiere quitar los objetos seleccionados?
#XMSG: Remove spaces popup text
removeSpacesConfirmation=¿Seguro que quiere quitar los espacios seleccionados?
#XBUT: Confirmation button for objects dialog
removeObjectsOrSpacesConfirmationBtn=Quitar espacios
#XMSG: Success message after removing objects
removeObjectsSuccessMessage=Los objetos expuestos se quitaron
#XMSG: Success message after assigning objects
assignObjectsSuccessMessage=Los objetos expuestos se asignaron
#XFLD: auto object assignment label for space tile
autoObjAssignmentLabel=Todos los objetos expuestos
#XFLD: Spaces tab label
spacesTabLabel=Espacios
#XFLD: Exposed objects tab label
consumptionObjectsTabLabel=Objetos expuestos
#XMSG: Success message after unassigning spaces
spacesUnassignedSuccessMessage=Los espacios se quitaron
#XMSG: Success message after unassigning space
spaceUnassignedSuccessMessage=El espacio se quitó
#YMSE: Error while assigning / unassigning spaces
spacesAssignUnassignError=No se pudieron asignar o quitar los espacios.
#YMSE: Error while removing objects
removeObjectsError=No pudimos asignar o quitar los objetos.
#YMSE: Error while removing object
removeObjectError=No pudimos asignar o quitar el objeto.
#YMSE: Error when saving the ecn with invalid value
invalidSizingPlanError=El número antes seleccionado ya no es válido. Seleccione un número válido.
#YMSE: Error when saving the ecn with invalid value
invalidPerformanceClassError=Seleccione una clase de rendimiento válida.
#YMSE: Error when saving the ecn with invalid value
performanceClassNotAvailableError=La clase de rendimiento antes seleccionada "{0}" actualmente no es válida. Seleccione la clase de rendimiento válida.
#XTXT: Delete ECN confirmation text
deleteConfirmationTxtECN=¿Seguro que quiere eliminar el nodo de procesamiento elástico?
#XFLD: tooltip for ? button
help=Ayuda
#XFLD: ECN edit button label
editECN=Configurar
#XFLD: Technical type label for ERModel
DWC_ERMODEL=Entidad: modelo de relación
#XFLD: Technical type label for Local Table
DWC_LOCAL_TABLE=Tabla local
#XFLD: Technical type label for Remote Table
DWC_REMOTE_TABLE=Tabla remota
#XFLD: Technical type label for Analytic Model
DWC_ANALYTIC_MODEL=Modelo analítico
#XFLD: Technical type label for Task Chain
DWC_TASKCHAIN=Cadena de tareas
#XFLD: Technical type label for Data Flow
DWC_DATAFLOW=Flujo de datos
#XFLD: Technical type label for Replication Flow
DWC_REPLICATIONFLOW=Flujo de replicación
#XFLD: Technical type label for Transformation Flow
DWC_TRANSFORMATIONFLOW=Flujo de transformación
#XFLD: Technical type label for Intelligent Lookup
DWC_IDT=Búsqueda inteligente
#XFLD: Technical type label for Repository
DWC_REPOSITORY_PACKAGE=Repositorio
#XFLD: Technical type label for Enterprise Search
DWC_ESMODEL=Búsqueda empresarial
#XFLD: Technical type label for View
DWC_VIEW=Vista
#XFLD: Technical type label for Data Product
DWC_DATAPRODUCT=Producto de datos
#XFLD: Technical type label for Data Access Control
DWC_DAC=Control de acceso de datos
#XFLD: Technical type label for Folder
DWC_FOLDER=Carpeta
#XFLD: Technical type label for Data Entity
DWC_BUSINESS_ENTITY=Entidad empresarial
#XFLD: Technical type label for Business Entity Variant
DWC_BUSINESS_ENTITY_VARIANT=Variante de entidad empresarial
#XFLD: Technical type label for Responsibility Scenario
DWC_AUTH_SCENARIO=Escenario de responsabilidad
#XFLD: Technical type label for Fact Model
DWC_FACT_MODEL=Modelo de hecho
#XFLD: Technical type label for Transformation Flow
DWC_PERSPECTIVE=Perspectiva
#XFLD: Technical type label for Consumption Model
DWC_CONSUMPTION_MODEL=Modelo de consumo
#XFLD: Technical type label for Remote Connection
DWC_REMOTECONNECTION=Conexión remota
#XFLD: Technical type label for Fact Model Variant
DWC_FACT_MODEL_VARIANT=Variante de modelo de hecho
#XMSG: Schedule created alert message
createScheduleSuccess=Programa creado
#XMSG: Schedule updated alert message
updateScheduleSuccess=Programa actualizado
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Programa eliminado
#XMSG: Schedule takeover success message
ownerChangeScheduleSuccess=Programa que se le asignó
#XMSG: Message for starting pausing of schedule
pauseScheduleStarted=Pausando 1 programa
#XMSG: Message for starting resuming of schedule
resumeScheduleStarted=Reanudando 1 programa
#XFLD: Segmented button label
availableSpacesButton=Disponible
#XFLD: Segmented button label
selectedSpacesButton=Seleccionado
#XFLD: Visit website button text
visitWebsite=Visitar sitio web
#XMSG: Warning message on unselecting source language
sourceLanguageRemovalWarning=Se quitará el idioma de origen antes seleccionado.
#XBUT: enable translation confirmation button
enableTranslationConfirmationBtn=Habilitar
#XFLD: ECN performance class label
performanceClassLabel=Clase de rendimiento
#XTXT performance class memory text
memoryText=Memoria
#XTXT performance class compute text
computeText=Procesamiento
#XTXT performance class high-compute text
highComputeText=Procesamiento intensivo
#XBUT: Recycle Bin Button Text
recycleBin=Papelera de reciclaje
#XBUT: Restore Button Text
restore=Restaurar
#XMSG: Warning message for new Workload Management UI
priorityWarning=Esta área es de solo lectura. Puede cambiar la prioridad del espacio en Sistema / Configuración / Administración de las cargas de trabajo.
#XMSG: Warning message for new Workload Management UI
workloadWarning=Esta área es de solo lectura. Puede cambiar la configuración de las cargas de trabajo del espacio en Sistema / Configuración / Administración de las cargas de trabajo.
#XCOL: Space table-view column: Apache Spark vCPUs
sparkManagementVCPUs= vCPU Apache Spark
#XCOL: Space table-view column: Apache Spark Memory (GB)
sparkManagementMemory=Memoria de Apache Spark (GB)
#XCOL: Space type (from Space Capability) for Data Ingestion Spaces
ingestionSpaceType=Ingesta de producto de datos
#XFLD: Deployment in progress nodatatext for spark table
sparkTableDeploymentInProgress=No hay datos disponibles debido a que el espacio está siendo implementado actualmente
#XFLD: Runtime data being loaded nodatatext for spark table
sparkTableDeploymentLoading=No hay datos disponibles debido a que el espacio está siendo cargado actualmente
#XCOL: Space type (from Space Capability) for BW Private Cloud Edition
BWPCESpaceType=SAP BW
#XBUT: Go to Instance Mappings Button
navToMappingsButton=Editar asignaciones de instancia
#XFLD: SAP HANA Data Lake Storage label if less than 1GB
dataLakeStorageUsed=< 1 GB
