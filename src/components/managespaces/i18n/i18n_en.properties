#DO NOT REMOVE________________________
#XFLD: Breadcrumb for space monitoring page
spaceMonitorBreadcrumb=Monitoring
#XTXT: Type name for spaces in browser tab page title
space=Space
#_____________________________________
#XFLD: Spaces label in
spaces=Spaces
#XFLD: Manage plan button text
manageQuotaButtonText=Manage Plan
#XBUT: Manage resources button
manageResourcesButton=Manage Resources
#XFLD: Create space button tooltip
createSpace=Create Space
#XFLD: Create
create=Create
#XFLD: Deploy
deploy=Deploy
#XFLD: Page
page=Page
#XFLD: Cancel
cancel=Cancel
#XFLD: Update
update=Update
#XFLD: Save
save=Save
#XFLD: OK
ok=OK
#XFLD: days
days=Days
#XFLD: Space tile edit button label
edit=Edit
#XFLD: Auto Assign all objects to space
autoAssign=Auto Assign
#XFLD: Space tile open monitoring button label
openMonitoring=Monitor
#XFLD: Delete
delete=Delete
#XFLD: Copy Space
copy=Copy
#XFLD: Close
close=Close
#XCOL: Space table-view column status
status=Status
#XFLD: Space status active
activeLabel=Active
#XFLD: Space status locked
lockedLabel=Locked
#XFLD: Space status critical
criticalLabel=Critical
#XFLD: Space status cold
coldLabel=Cold
#XFLD: Space status deleted
deletedLabel=Deleted
#XFLD: Space status unknown
unknownLabel=Unknown
#XFLD: Space status ok
okLabel=Healthy
#XFLD: Database user expired
expired=Expired
#XFLD: deployed
deployed=Deployed
#XFLD: not deployed
notDeployed=Not Deployed
#XFLD: changes to deploy
changesToDeploy=Changes to Deploy
#XFLD: pending
pending=Deploying
#XFLD: designtime error
designtimeError=Design-Time Error
#XFLD: runtime error
runtimeError=Run-Time Error
#XFLD: Space created by label
createdBy=Created By
#XFLD: Space created on label
createdOn=Created On
#XFLD: Space deployed on label
deployedOn=Deployed On
#XFLD: Space ID label
spaceID=Space ID
#XFLD: Priority label
priority=Priority
#XFLD: Space Priority label
spacePriority=Space Priority
#XFLD: Space Configuration label
spaceConfiguration=Space Configuration
#XFLD: Not available
notAvailable=Not Available
#XFLD: WorkloadType default
default=Default
#XFLD: WorkloadType custom
custom=Custom
#XFLD: Data Lake option label; refers to component SAP HANA Cloud, Data Lake
dataLakeAccess=Data Lake Access
#XFLD: Translation label
translationLabel=Translation
#XFLD: Source language label
sourceLanguageLabel=Source Language
#XFLD: Translation CheckBox label
translationCheckBox=Enable Translation
#XTXT: Popover hint that space has to be deployed to show user details
openDatabaseUserHintPopoverText=Deploy space to access user details.
#XTXT: Popover hint that space has to be deployed to open database explorer
openDatabaseExplorerHintPopoverText=Deploy space to open Database Explorer.
#XTXT: Text for when the Data Lake check box is disabled
dataLakeOptionDisabled=You can’t use this space to access the Data Lake because it is already used by another space.
#XFLD: Data Lake check box text; refers to component SAP HANA Cloud, Data Lake
dataLakeCheckBox=Use this space to access the data lake.
#XFLD: Space Priority minimum label extension
low=Low
#XFLD: Space Priority maximum label extension
high=High
#XFLD: Space name label
spaceName=Space Name
#XFLD: Enable deploy objects checkbox
enableDeployObjects=Deploy Objects
#XTIT: Copy spaces dialog title
copySpaceDialogTitle=Copy {0}
#XTXT Human readable text for unselected source language
unSelectedSourceLanguage=(Not Selected)
#XTXT Human readable text for language code "af"
af=Afrikaans
#XTXT Human readable text for language code "ar"
ar=Arabic
#XTXT Human readable text for language code "bg"
bg=Bulgarian
#XTXT Human readable text for language code "ca"
ca=Catalan
#XTXT Human readable text for language code "zh"
zh=Simplified Chinese
#XTXT Human readable text for language code "zf"
zf=Chinese
#XTXT Human readable text for language code "hr"
hr=Croatian
#XTXT Human readable text for language code "cs"
cs=Czech
#XTXT Human readable text for language code "cy"
cy=Welsh
#XTXT Human readable text for language code "da"
da=Danish
#XTXT Human readable text for language code "nl"
nl=Dutch
#XTXT Human readable text for language code "en-UK"
en-UK=English (United Kingdom)
#XTXT Human readable text for language code "en"
en=English (United States)
#XTXT Human readable text for language code "et"
et=Estonian
#XTXT Human readable text for language code "fa"
fa=Persian
#XTXT Human readable text for language code "fi"
fi=Finnish
#XTXT Human readable text for language code "fr-CA"
fr-CA=French (Canada)
#XTXT Human readable text for language code "fr"
fr=French
#XTXT Human readable text for language code "de"
de=German
#XTXT Human readable text for language code "el"
el=Greek
#XTXT Human readable text for language code "he"
he=Hebrew
#XTXT Human readable text for language code "hi"
hi=Hindi
#XTXT Human readable text for language code "hu"
hu=Hungarian
#XTXT Human readable text for language code "is"
is=Icelandic
#XTXT Human readable text for language code "id"
id=Bahasa Indonesia
#XTXT Human readable text for language code "it"
it=Italian
#XTXT Human readable text for language code "ja"
ja=Japanese
#XTXT Human readable text for language code "kk"
kk=Kazakh
#XTXT Human readable text for language code "ko"
ko=Korean
#XTXT Human readable text for language code "lv"
lv=Latvian
#XTXT Human readable text for language code "lt"
lt=Lithuanian
#XTXT Human readable text for language code "ms"
ms=Bahasa Melayu
#XTXT Human readable text for language code "no"
no=Norwegian
#XTXT Human readable text for language code "pl"
pl=Polish
#XTXT Human readable text for language code "pt"
pt=Portuguese (Brasil)
#XTXT Human readable text for language code "pt-PT"
pt-PT=Portuguese (Portugal)
#XTXT Human readable text for language code "ro"
ro=Romanian
#XTXT Human readable text for language code "ru"
ru=Russian
#XTXT Human readable text for language code "sr"
sr=Serbian
#XTXT Human readable text for language code "sh"
sh=Serbo-Croatian
#XTXT Human readable text for language code "sk"
sk=Slovak
#XTXT Human readable text for language code "sl"
sl=Slovenian
#XTXT Human readable text for language code "es"
es=Spanish
#XTXT Human readable text for language code "es-MX"
es-MX=Spanish (Mexico)
#XTXT Human readable text for language code "sv"
sv=Swedish
#XTXT Human readable text for language code "th"
th=Thai
#XTXT Human readable text for language code "tr"
tr=Turkish
#XTXT Human readable text for language code "uk"
uk=Ukrainian
#XTXT Human readable text for language code "vi"
vi=Vietnamese
#XTIT: Delete spaces confirmation title
deleteConfirmationHeaderText=Delete Spaces
#XTXT: Logically delete space delete confirmation text with space name
logicallyDeleteConfirmationBodyTextWithSpaceName=Are you sure you want to move space "{0}" to the recycle bin?
#XTXT: Logically dpace delete confirmation text for multiple spaces
logicallyDeleteConfirmationBodyTextMultiple=Are you sure you want to move the {0} selected spaces to the recycle bin?.
#XTXT: Delete space delete confirmation text with space name
deleteConfirmationBodyTextWithSpaceName=Are you sure you want to delete the space "{0}"? This action cannot be undone.
#XTXT: Delete space delete confirmation text for multiple spaces
deleteConfirmationBodyTextMultiple=Are you sure you want to delete the {0} selected spaces? This action cannot be undone. The following content will be {1} deleted:
#XTXT: permanently
permanently=permanently
#XTXT: {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDetail=The following content will be {0} deleted and cannot be recovered:
#XTXT: Hint Delete space confirmation text
deleteConfirmationHint=Please type {0} to confirm the deletion.
#XTXT: Delete space confirmation error check spelling
deleteConfirmationErrorCheckSpelling=Please check your spelling and try again.
#XTXT: All Spaces
allSpaces=All Spaces
#XTXT: All data
allData=All objects and data contained in the space
#XTXT: All connections
allConnections=All connections defined in the space
#XFLD: Space tile selection box tooltip
clickToSelect=Click to Select
#XTXT: All database users
allDatabaseUsers=All objects and data contained in any Open SQL schema associated with the space
#XFLD: remove members button tooltip
deleteUsers=Remove Members
#XTXT: Space long description text
description=Description (4000 Characters Maximum)
#XFLD: Add Members button tooltip
addUsers=Add Members
#XFLD: Add Users button tooltip
addUsersTooltip=Add Users
#XFLD: Edit Users button tooltip
editUsersTooltip=Edit Users
#XFLD: Remove Users button tooltip
removeUsersTooltip=Remove Users
#XFLD: Searchfield placeholder
filter=Search
#XCOL: Users table-view column health
health=Health
#XCOL: Users table-view column access
access=Access
#XFLD: No user found nodatatext
noDataText=No User Found
#XTIT: Members dialog title
selectUserDialogTitle=Add Members
#XTIT: User dialog title
addUserDialogTitle=Add Users
#XTIT: Delete connections confirmation title
deleteConnectionConfirmationHeaderText=Delete Connections
#XTIT: Delete single connection confirmation title
deleteConnectionConfirmationHeaderTextSingle=Delete Connection
#XTXT: Delete connection delete confirmation text
deleteConnectionConfirmationBodyText=Are you sure you want to delete the selected connections? They will be permanently removed.
#XTIT: Remote source dialog title
selectRemoteConnectionDialogTitle=Select Connections
#XTIT: Share connection dialog title
connectionSharingDialogTitle=Share Connection
#XTIT: Connections shared from other spaces table title
sharedConnectionsFromOtherSpaces=Shared Connections
#XFLD: Add remote source button tooltip
addRemoteConnections=Add Connections
#XFLD: Delete remote source button tooltip
deleteRemoteConnections=Remove Connections
#XFLD: Share remote source button tooltip
shareConnections=Share Connections
#XFLD: Tile-layout tooltip
tileLayout=Tile Layout
#XFLD: Table-layout tooltip
tableLayout=Table Layout
#XMSG: Success message after creating space
createSpaceSuccessMessage=Space created
#XMSG: Success message after copying space
copySpaceSuccessMessage=Copying space "{0}" to space "{1}"
#XMSG: Success message after deploying space
deploymentSuccessMessage=Space deployment has been started
#XMSG: Success message after Apache Spark update started
sparkUpdateStartedSuccessMessage=Apache Spark update has started
#YMSE: Error while starting Apache Spark update
sparkUpdateStartedErrorMessage=Failed to update Apache Spark
#XMSG: Success message after updating space
updateSettingsSuccessMessage=Space details updated
#XMSG: Success message after unlocking space
unlockSpaceSuccessMessage=Space unlocked temporarily
#XMSG: Success message after deleting a single space
deleteSpaceSuccessMessage=Space deleted
#XMSG: Success message after deleting multiple spaces
deleteSpacesSuccessMessage=Spaces deleted
#XMSG: Success message after restoring a single space
restoreSpaceSuccessMessage=Space restored
#XMSG: Success message after restoring multiple spaces
restoreSpacesSuccessMessage=Spaces restored
#YMSE: Error while updating settings
updateSettingsFailureMessage=The space settings couldn’t be updated.
#YMSE: Error while updating data lake option
dataLakeEnabledInOtherSpace=The data lake is already assigned to another space. Only one space can access the data lake at a time.
#YMSE: Error while updating data lake option
virtualTablesExists=You cannot unassign the data lake from this space because there are still dependencies to virtual tables*. Please delete the virtual tables to unassign the data lake from this space.
#YMSE: Error while unlocking space
unlockSpaceFailureMessage=The space couldn’t be unlocked.
#YMSE: Error while creating space
createSpaceError=The space couldn’t be created.
#YMSE: Error while creating duplicate space
createDuplicateSpaceError=A space with name {0} already exists.
#YMSE: Error while deleting a single space
deleteSpaceError=The space couldn’t be deleted.
#YMSE: Error details while deleting a single space
deleteSpaceDetailedError=Your space “{0}” is not working properly anymore. Please try to delete it again. If it still doesn’t work, ask your administrator to delete your space or open a ticket.
#YMSE: Error while deleting a single space in files
deleteSpaceInFilesError=The space data in Files couldn’t be deleted.
#YMSE: Error while deleting a single space users
deleteSpaceUsersError=The users couldn’t be removed.
#YMSE: Error while deleting a single space schema
deleteSpaceSchemasError=The schemas couldn’t be removed.
#YMSE: Error while deleting a single space connections
deleteSpaceConnectionsError=The connections couldn’t be removed.
#YMSE: Error while deleting a single space data
deleteSpaceDataError=The space data couldn’t be deleted.
#YMSE: Error while deleting multiple spaces
deleteSpacesError=The spaces couldn’t be deleted.
#YMSE: Error while restoring a single space
restoreSpaceError=The space couldn’t be restored.
#YMSE: Error while restoring multiple spaces
restoreSpacesError=The spaces couldn’t be restored.
#YMSE: Error while creating users
createUsersError=The users couldn’t be added.
#YMSE: Error while removing users
removeUsersError=We couldn’t remove the users.
#YMSE: Error while removing user
removeUserError=couldn’t remove the user.
#YMSE: Error assigning roles to a user
roleAssignUnAssignError=We couldn’t add the user to the selected scoped role. \n\n You cannot add yourself to a scoped role. You can ask your administrator to add you to a scoped role.
#YMSE: Error assigning user to the space
userAssignError=We couldn’t assign the user to the space. \n\n The user is already assigned to the maximum allowed number (100) of spaces across scoped roles.
#YMSE: Error assigning users to the space
usersAssignError=We couldn’t assign the users to the space. \n\n The user is already assigned to the maximum allowed number (100) of spaces across scoped roles.
#YMSE: Error while fetching users
fetchUserWithScopedRolesError=We couldn’t retrieve the users. Please try again later.
#YMSE: Error while fetching scoped roles
fetchScopedRolesError=We couldn’t retrieve the scoped roles.
#YMSE: Error while fetching members
fetchUserError=The members couldn’t be fetched. Please try again later.
#YMSE: Error while loading run-time database
loadRuntimeError=We couldn’t load information from the run-time database.
#YMSE: Error while loading spaces
loadSpacesError=Sorry, something went wrong when trying to retrieve your spaces.
#YMSE: Error while loading haas resources
loadStorageError=Sorry, something went wrong when trying to retrieve the storage data.
#YMSE: Error no data could be loaded
loadDataError=Sorry, something went wrong when trying to retrieve your data.
#XFLD: Click to refresh storage data
clickToRefresh=Click here to try again.
#XTIT: Delete space popup title
deleteSpacePopupTitle=Delete Space
#XCOL: Spaces table-view column name
name=Name
#XCOL: Spaces table-view deployment status
deploymentStatus=Deployment Status
#XFLD: Disk label in space details
storageLabel=Disk (GB)
#XFLD: In-Memory label in space details
ramLabel=Memory (GB)
#XFLD: Memory label on space card
memory=Memory for Storage
#XFLD: Storage Assignment label in space details
storrageAssignmentLabel=Space Storage
#XFLD: Storage Type label in space details
storageTypeLabel=Storage Type
#XFLD: Enable Space Quota
enableSpaceQuota=Enable Space Quota
#XFLD: No Space Quota
noSpaceQuota=No Space Quota
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
diskAndInMemoryText=SAP HANA Database (Disk and In-Memory)
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
dataLakeFilesText=SAP HANA Data Lake Files
#XFLD: Available scoped roles label
availableRoles=Available Scoped Roles
#XFLD: Selected scoped roles label
selectedRoles=Selected Scoped Roles
#XCOL: Spaces table-view column models
models=Models
#XCOL: Spaces table-view column users
users=Users
#XCOL: Spaces table-view column connections
connections=Connections
#XFLD: Section header overview in space detail
overview=Overview
#XFLD: Section header Apache Spark in space detail
sparkManagementSection=Apache Spark
#XFLD: Section header Apache Spark in space detail
sparkManagementSubSection=Applications
#XFLD: Section header Apache Spark in space detail
taskAssignmentSubSection=Task Assignment
#XFLD: vCPU label in Apache Spark section
vCPULabel=vCPUs
#XFLD: Memory label in Apache Spark section
memoryLabel=Memory (GB)
#XFLD: space configuration label in Apache Spark section
spaceConfigurationLabel=Space Configuration
#XFLD: Space Source label
sparkApplicationLabel=Application
#XFLD: Cluster Size label
clusterSizeLabel=Cluster Size
#XFLD: Driver label
driverLabel=Driver
#XFLD: Executor label
executorLabel=Executor
#XFLD: max label
maxLabel=Max. Used
#XFLD: TrF Default label
trFDefaultLabel=Transformation Flow Default
#XFLD: Merge Default label
mergeDefaultLabel=Merge Default
#XFLD: Optimize Default label
optimizeDefaultLabel=Optimize Default
#XFLD: Deployment Default label
deploymentDefaultLabel=Local Table (File) Deployment
#XFLD: max spark source label
driverExecutorMaxUsedSparkSource={0} CPU / {1} GB
#XFLD: Object type label
taskObjectTypeLabel=Object Type
#XFLD: Task activity label
taskActivityLabel=Activity
#XFLD: Task Application ID label
taskApplicationIDLabel=Default Application
#XFLD: Section header in space detail
generalSettings=General Settings
#XMSG: Message strip message when a space is locked
spaceLockedMessageStrip=This space is currently locked by the system.
#XMSG: Message strip message deploy warning
deployWarningMessageStrip=Changes in this section will be deployed immediately.
#XMSG: Message strip message modify space configuration warning
modifySpaceConfigurationMessageStrip=Be aware that changing these values may cause performance issues.
#XFLD: Button text to unlock the space again
unlockSpace=Unlock Space
#XFLD: Info text for audit log formatted message
auditLogText=Enable audit logs to record read or change actions (audit policies). Administrators can then analyze who performed which action at which point in time.
#XMSG: Message strip  audit warning
auditLogWarningMessageStrip=Audit logs can consume a large amount of disk storage in your tenant. If you enable an audit policy (read or change actions), you should regularly monitor disk storage usage (via the Disk Storage Used card in the System Monitor) to avoid full disk outages, which can lead to service disruptions. If you disable an audit policy, all its audit log entries will be deleted. If you want to keep the audit log entries, consider exporting them before you disable the audit policy.
#XFLD: Message strip link text to open in app help to guide user what to do to unlock his space permanently
unlockSpaceHelp=Show Help
#XMSG: Message strip message when a space will be locked again
spaceTemporarilyUnlockedMessageStrip=This space exceeds its space storage and will be locked in {0} {1}.
#XMSG: Unit for remaining time until space is locked again
hours=hours
#XMSG: Unit for remaining time until space is locked again
minutes=minutes
#XFLD: Section header in space detail for auditing
detailsSectionAuditing=Auditing
#XFLD: Subsection header in space detail for auditing
auditing=Space Audit Settings
#XFLD: Hot space tooltip
hotSpaceCountTooltip=Critical spaces: Used storage is greater than 90%.
#XFLD: Green space tooltip
greenSpaceCountTooltip=Healthy spaces: Used storage is between 6% and 90%.
#XFLD: Cold space tooltip
coldSpaceCountTooltip=Cold spaces: Used storage is 5% or less.
#XFLD: Critical space tooltip
criticalSpaceCountTooltip=Critical spaces: Used storage is greater than 90%.
#XFLD: Green space tooltip
okSpaceCountTooltip=Healthy spaces: Used storage is between 6% and 90%.
#XFLD: Locked space tooltip
lockedSpaceCountTooltipNew=Locked spaces: Blocked due to insufficient memory.
#XFLD: Locked space tooltip
lockedSpaceCountTooltip=Locked Spaces
#YMSE: Error while deleting remote source
deleteRemoteError=The connections couldn’t be removed.
#XFLD: Space ID input tooltip
spaceIDInfoTooltip=Space ID cannot be changed later.\nValid characters A - Z, 0 - 9, and _
#YMSE: Error when space name is empty
spaceNameEmptyValidationError=Enter space name.
#YMSE: Error when ECN Business name is empty
ecnNameEmptyValidationError=Enter a business name.
#YMSE: Error when space id is empty
spaceIDInputEmptyValidationError=Enter space ID.
#YMSE: Error when space id contains invalid characters
spaceIDInputInValidError=Invalid characters. Please use A - Z, 0 - 9, and _ only.
#YMSE: Error when space id exists while creation
spaceIDInputDuplicateError=Space ID already exists.
#XFLD: Space searchfield placeholder
search=Search
#XMSG: Success message after creating users
createUsersSuccess=Users added
#XMSG: Success message after creating user
createUserSuccess=User added
#XMSG: Success message after updating users
updateUsersSuccess={0} Users updated
#XMSG: Success message after updating user
updateUserSuccess=User updated
#XMSG: Success message after removing users
removeUsersSuccess={0} Users removed
#XMSG: Success message after removing user
removeUserSuccess=User removed
#XFLD: Schema name
schemaName=Schema Name
#XFLD: used of total
ofTemplate={0} of {1}
#XFLD: Assigned disk label in space overview
assignedStorageTemplate=Assigned Disk ({0} of {1})
#XFLD: Assigned Memory label in space overview
assignedRAMTemplate=Assigned Memory ({0} of {1})
#XFLD: Storage ratio on space
accelearationRAM=Memory Acceleration
#XFLD: No Storage Consumption
noStorageConsumptionText=No storage quota assigned.
#XFLD: Used disk label in space overview
usedStorageTemplate=Disk Used for Storage ({0} of {1})
#XFLD: Used Memory label in space overview
usedRAMTemplate=Memory Used for Storage ({0} of {1})
#XFLD: Used Disk tooltip
usedStorageTooltipTemplate={0} of {1} Disk Used for Storage
#XFLD: Used Memory tooltip
usedRAMTooltipTemplate={0} of {1} Memory Used
#XFLD: Assigned Disk tooltip
assignedStorageTooltipTemplate={0} of {1} Disk Assigned
#XFLD: Assigned Memory tooltip
assignedRAMTooltipTemplate={0} of {1} Memory Assigned
#XFLD: Space related used storage tooltip
spaceRelatedUsedStorageTooltipTemplate=Space Data: {0}
#XFLD: Not space related used storage tooltip
otherDataUsedStorageTooltipTemplate=Other Data: {0}
#XFLD: Storage used exceeded popover and tooltip
storageUsedExceededText=Consider extending your plan, or contact SAP Support.
#XCOL: Space table-view column used Disk
usedStorage=Disk Used for Storage
#XCOL: Space monitor column used Memory
usedRAM=Memory Used for Storage
#XCOL: Space monitor column Schema
tableSchema=Schema
#XCOL: Space monitor column Storage Type
tableStorageType=Storage Type
#XCOL: Space monitor column Table Type
tableType=Table Type
#XCOL: Space monitor column Record Count
tableRecordCount=Record Count
#XFLD: Assigned Disk
assignedStorage=Disk Assigned for Storage
#XFLD: Assigned Memory
assignedRAM=Memory Assigned for Storage
#XCOL: Space table-view column storage utilization
tableStorageUtilization=Used Storage
#XFLD: space status
spaceStatus=Space Status
#XFLD: space type
spaceType=Space Type
#XFLD: space type label for space with space type "SAP BW bridge" ("SAP BW bridge" is a product name, do not translate it)
abapbridgeSpaceType=SAP BW bridge
#XFLD: space type label for space with space type "Data Marketplace"
marketplaceSpaceType=Data Provider Product
#XMSG: Error message for when selected space cannot be deleted due to the space type
deleteTypedSpaceError=You cannot delete space {0} as its space type is {1}.
#XMSG: Error message for when selected spaces cannot be deleted due to the space type
deleteTypedSpacesError=You cannot delete the {0} selected spaces. Spaces with the following space types cannot be deleted: {1}.
#XFLD: space type label for space without space type
defaultSpaceType=SAP Datasphere
#XFLD: Tooltip for monitor space button
monitorSpace=Monitor
#XFLD: Tooltip for edit space button
editSpace=Edit Space
#XMSG: Deletion warning in messagebox
deleteConfirmation=Are you sure you want to delete this space?
#XFLD: Tooltip for delete space button
deleteSpace=Delete Space
#XFLD: storage
storage=Disk for Storage
#XFLD: username
userName=User Name
#XFLD: port
port=Port
#XFLD: hostname
hostName=Host Name
#XFLD: password
password=Password
#XBUT: Request new password button
requestPassword=Request New Password
#YEXP: Usage explanation in time data section
timeDataSectionHint=Create time tables and dimensions to use in your models and stories.
#YEXP: Usage explanation in Space schema section
spaceSchemaHint=Do you want the data in your space to be consumable by other tools or apps? If so, create one or multiple users that can access the data in your space and select whether you want all future space data to be consumable by default.
#XTIT: Create schema popup title
createSchemaDialogTitle=Create Open SQL Schema
#XTIT: Generate Time Data dialog title
generateTimeDataDialogTitle=Create Time Tables and Dimensions
#XTIT: Edit Time Data dialog title
editTimeDataDialogTitle=Edit Time Tables and Dimensions
#XTIT: Time Data token title
timeDataTokenTitle=Time Data
#XTIT: Time Data token title
timeDataUpdateViews=Update Time Data Views
#XFLD: Time Data token creation in progress label
timeDataCreationInProgress=Creation in progress...
#XFLD: Time Data token creation error label
timeDataCreationError=Creation failed. Please try again.
#XTIT: Time Data dialog time settings title
timeDataDialogTimeSettingsTitle=Time Table Settings
#XTIT: Time Data dialog time data table title
timeDataDialogTranslationTablesTitle=Translation Tables
#XTIT: Time Data dialog dimensions title
timeDataDialogTimeViewsTitle=Time Dimensions
#XFLD: Time Data dialog time range label
timeRangeHint=Define the time range.
#XFLD: Time Data dialog time data table label
timeDataHint=Give your table a name.
#XFLD: Time Data dialog time data dimensions label
timeDimensionsHint=Give your dimensions a name.
#XFLD: Time Data Time range description label
timerangeLabel=Time Range
#XFLD: Time Data dialog from year label
fromYearLabel=From Year
#XFLD: Time Data dialog to year label
toYearLabel=To Year
#XFLD: Time Data dialog calendar type label
calendarTypeLabel=Calendar Type
#XFLD: Time Data dialog granularity label
granularityLabel=Granularity
#XFLD: Time Data dialog technical name label
technicalNameLabel=Technical Name
#XFLD: Time Data dialog quarters text table lable
quarterTextsTableLabel=Translation Table for Quarters
#XFLD: Time Data dialog months text table lable
monthsTextsTableLabel=Translation Table for Months
#XFLD: Time Data dialog days text table lable
daysTextsTableLabel=Translation Table for Days
#XFLD: Time Data dialog year label
yearLabel=Year Dimension
#XFLD: Time Data dialog quarter label
quarterLabel=Quarter Dimension
#XFLD: Time Data dialog month label
monthLabel=Month Dimension
#XFLD: Time Data dialog day label
dayLabel=Day Dimension
#XFLD: Time Data dialog gregorian calendar type label
gregorian=Gregorian
#XFLD: Time Data dialog time granularity day label
day=Day
#XFLD: Time Data dialog business name maxlength reached
timeRangeValueStateTextMaxLengthBusinessName=Max. length of 1000 characters reached.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextMaxRangeExceeded=The maximum time range is 150 years.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeLower=“From Year” should be lower than “To Year”
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeHigher="From Year" must be 1900 or higher.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeHigher=“To Year” should be higher than “From Year”
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeLower=“To Year” must be lower than the current year plus 100
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningFromYear=Increasing the "From Year" might lead to data loss
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningToYear=Lowering the "To Year" might lead to data loss
#XMSG: Time Data creation validation error message
timeDataValidationError=It looks likes some fields are invalid. Please check the required fields to continue.
#XMSG: Time Data deletion popup text
deleteTimeDataConfirmation=Are you sure you want to delete the data?
#XMSG: Time Data creation success message
createTimeDataSuccess=Time data created
#XMSG: Time Data update success message
updateTimeDataSuccess=Time data updated
#XMSG: Time Data delete success message
deleteTimeDataSuccess=Time data deleted
#XMSG: Time Data creation error message
createTimeDataError=Something went wrong while trying to create time data.
#XMSG: Time Data update error message
updateTimeDataError=Something went wrong while trying to update time data.
#XMSG: Time Data creation error message
deleteTimeDataError=Something went wrong while trying to delete time data.
#XMSG: Error message when time data could not be retrieved
retrievingTimeDataError=Time data could not be loaded.
#XTIT: Time Data deletion popup title
deleteTimeDataConfirmationTitle=Warning
#XTXT: Time Data deletion dependency popup text
deleteTimeDataDependencyText=We couldn’t delete your Time Data because it is used in other models.
#XTXT: Time Data deletion dependency popup text singular
deleteTimeDataDependencyTextSingular=We couldn’t delete your Time Data because it is used in another model.
#XMSG: Validation error when required time data form field is empty
timeDataRequiredField=This field is required and can’t be left empty.
#XFLD: Tooltip for Open in Database Explorer button
openInDBX=Open in Database Explorer
#YMSE: Dimension Year
dimensionYearView=Dimension "Year"
#YMSE: Dimension Year
dimensionQuarterView=Dimension "Quarter"
#YMSE: Dimension Year
dimensionMonthView=Dimension "Month"
#YMSE: Dimension Year
dimensionDayView=Dimension "Day"
#XFLD: Time Data deletion object title
timeDataUsedIn=(used in {0} models)
#XFLD: Time Data deletion object title singular
timeDataUsedInSingular=(used in 1 model)
#XFLD: Time Data deletion table column provider
provider=Provider
#XTIT: Time Data dependency dialog title
timeDataDependencyDialogTitle=Dependencies
#XTIT: Create schema popup title
createSpaceSchemaUserDialogTitle=Create User for Space Schema
#XFLD: Create schema button
createSchemaButton=Create Open SQL Schema
#XFLD: Generate TimeData button
generateTimeDataButton=Create Time Tables and Dimensions
#XFLD: Show dependencies button
showDependenciesButton=Show Dependencies
#XFLD: tooltip for disabled buttons when user is not member of space
mustBeMemberOfSpaceForOperation=To perform this operation, your user must be a member of the space.
#XFLD: Create space schema user button
createSpaceSchemaUserButton=Create Space Schema User
#YMSE: API Schema users load error
loadSchemaUsersError=List of users couldn’t be loaded.
#XTIT: API Schema-config popup title
schemaUserDetailsDialogTitle=Space Schema User Details
#XMSG: API Schema user deletion popup text
deleteSpaceSchemaUserConfirmation=Are you sure you want to delete the selected user?
#XMSG: API Schema user deletion confirmation text
userDeleteSuccess=User deleted.
#YMSE: API Schema user deletion error
userDeleteError=The user couldn’t be deleted.
#XFLD: User deleted
userDeleted=The user has been deleted.
#XTIT: Remove user popup title
removeUserConfirmationTitle=Warning
#XMSG: Remove user popup text
removeUserConfirmation=Do you really want to remove the user? The user and its assigned scoped roles will be removed from the space.
#XMSG: Remove users popup text
removeUsersConfirmation=Do you really want to remove the users? The users and their assigned scoped roles will be removed from the space.
#XBUT: Confirmation button for remove user dialog
removeUserConfirmationBtn=Remove
#YMSE: No data text for available roles
noDataAvailableRoles=The space is not added to any scoped role. \n To be able to add users to the space, it must be first added to one or more scoped roles.
#YMSE: No data text for selected roles
noDataSelectedRoles=No selected scoped roles
#XTIT: Schema-config popup title
schemaDetailsDialogTitle=Open SQL Schema Configuration Details
#XFLD: Label for Read Audit Log
auditLogRead=Enable Audit Log for Read Operations
#XFLD: Label for Change Audit Log
auditLogChange=Enable Audit Log for Change Operations
#XFLD: Label Audit Log Retention
auditLogRetention=Keep Logs for
#XFLD: Label Audit Log Retention Unit
retentionUnit=Days
#YMSE: Error when log retention time is set incorrectly
incorrectRetentionTime=Enter a whole number between {0} and {1}
#XFLD: Label for Cross Space Schema Access
spaceSchemaAccess=Consume Space Schema Data
#XTIT: Confirm deactivation of space schema consumption dialog title
disableSpaceSchemaConsumptionDialogTitle=Stop Consuming Space Schema Data
#XTXT: Confirm deactivation of space schema consumption dialog text
disableSpaceSchemaConsumptionDialogText=This Open SQL schema might consume data of your space schema. If you stop consuming, models based on the space schema data might not work anymore.
#XBUT: Confirmation button for the stop consuming space schema data dialog
stopConsuming=Stop Consuming
#XTXT: Tooltip for data lake icon
dataLakeIconTooltip=This space is used to access the data lake
#XFLD: Tooltip for data lake table entry
dataLakeTableEntryTooltip=Data Lake Enabled
#XFLD: used In-Memory linit reached tag
usedRamLimitReachedLabel=Memory Limit Reached
#XFLD: Space storage Limit reached tag
storageLimitReachedLabel=Storage Limit Reached
#XFLD: Minimal Storage Limit Reached tag
minStorageLimitReachedLabel=Minimum Storage Limit Reached
#XFLD: Space ram tag
ramLimitReachedLabel=Memory Limit Reached
#XFLD: Minimal In-Memory Limit Reached tag
minRamLimitReachedLabel=Minimum Memory Limit Reached
#XFLD: Popover text after click on space storage limit warning
storageLimitReachedText=You have reached the assigned space storage limit of {0}. Please assign more storage to the space.
#XFLD: System storage tag
systemStorageLimitReachedLabel=System Storage Limit Reached
#XMSG: Popover text after click on storage limit warning
systemStorageLimitReachedText=You have reached the system storage limit of {0}. You can’t assign any more storage to the space now.
#XMSG: Schema deletion warning
deleteSchemaConfirmation=Deleting this open SQL schema will also permanently delete all the stored objects and maintained associations in the schema. Continue?
#XMSG: Success message after deleting a schema
schemaDeleteSuccess=Schema deleted
#YMSE: Error while deleting schema.
schemaDeleteError=The schema couldn’t be deleted.
#XMSG: Success message after update a schema
schemaUpdateSuccess=Schema updated
#YMSE: Error while updating schema.
schemaUpdateError=The schema couldn’t be updated.
#XMSG: Hint when no password is shown in the dialog.
newPasswordHint=We’ve provided a password for this schema. If you’ve forgotten your password or lost it, you can request a new one. Remember to copy or save the new password.
#XMSG: Hint when showing the password in the dialog.
savePasswordHint=Please copy your password. You will need it to set up a connection to this schema. If you’ve forgotten your password, you can open this dialog to reset it.
#XFLD,50: Hint on entering schema name in creation dialog
schemaNameHint=This name cannot be changed after the schema is created.
#XFLD: Open SQL Schemas section sub headline
schemasSQL=Open SQL
#XFLD: Space schema section sub headline
schemasSpace=Space
#XFLD: HDI Container section header
HDIContainers=HDI Containers
#XTXT: Add HDI Containers button tooltip
addHDIContainers=Add HDI Containers
#XTXT: Remove HDI Containers button tooltip
removeHDIContainers=Remove HDI Containers
#XFLD: Enable HDI Containers feature button
enableHDIContainersButton=Enable Access
#YMSE: No data text for HDI Containers table
noDataHDIContainers=No HDI containers have been added.
#YMSE: No data text for Timedata section
noDataTimedata=No time tables and dimensions have been created.
#YMSE: No data text during runtime not available for Timedata section
runtimeNotAvailableNoDataTimedata=Cannot load time tables and dimensions as the run-time database is not available.
#YMSE: No data text during runtime not available for HDI section
runtimeNotAvailableNoDataHDI=Cannot load HDI Containers as the run-time database is not available.
#YMSE: Error while fetching HDI Containers
fetchHDIContainersError=The HDI Containers couldn’t be obtained. Please try again later.
#XFLD Table column header for HDI Container names
HDIContainerName=HDI Container Name
#XFLD: Enable HDI Containers feature dialog title
enableHDIContainersDialogTitle=Enable Access
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText1New=You can enable SAP SQL Data Warehousing on your SAP Datasphere tenant to exchange data between your HDI containers and your SAP Datasphere spaces without the need for data movement.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText2=To do this, open a support ticket by clicking the button below.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText3New=Once your ticket has been processed, you must build one or more new HDI containers in the SAP Datasphere run-time database. Then, the Enable Access button is replaced by the + button in the HDI Containers section for all your SAP Datasphere spaces, and you can add your containers to a space.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText41=Need more information? Go to %%0. For detailed information about what to include in the ticket, see %%1.
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkText=SAP Help
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkTextSapNote3057059=SAP Note 3057059
#XBUT: Open Ticket Button Text
openTicket=Open Ticket
#XBUT: Add Button Text
add=Add
#XBUT: Next Button Text
next=Next
#XBUT: Edit Button Text
editUsers=Edit
#XBUT: create user Button Text
createUser=Create
#XBUT: Update user Button Text
updateUser=Select
#XTIT: Add HDI Containers Dialog Title
addHDIContainersDialogTitle=Add Unassigned HDI Containers
#XFLD: No data text for add HDI container dialog
addHDIContainersDialogNoDataText=We couldn’t find any unassigned containers. \n The container you are looking for may already be assigned to a space.
#YMSE: Error while loading assigned HDI Containers
loadAssignedHDIContainersError=Assigned HDI containers couldn’t be loaded.
#YMSE: Error while loading assigned HDI Containers
loadUnassignedHDIContainersError=HDI containers couldn’t be loaded.
#XMSG: Success message
succeededToAddHDIContainer=HDI container added
#XMSG: Success message
succeededToAddHDIContainerPlural=HDI containers added
#XMSG: Success message
succeededToDeleteHDIContainer=HDI container removed
#XMSG: Success message
succeededToDeleteHDIContainerPlural=HDI containers removed
#XFLD: Time data section sub headline
timeDataSection=Time Tables and Dimensions
#XFLD: Schemas list entry read audit log enabled indicator
readIndicator=Read
#XFLD: Schemas list entry change audit log enabled indicator
changeIndicator=Change
#XFLD: Remote sources section sub headline
allconnections=Connection Assignment
#XFLD: Remote sources section sub headline
localconnections=Local Connections
#XFLD: Remote sources section sub headline for open Connectors
openConnectors=SAP Open Connectors
#XFLD: User section sub headline
memberassignment=Member Assignment
#XFLD: User assignment section sub headline
userAssignment=User Assignment
#XFLD: User section Access dropdown Member
member=Member
#XFLD: User assignment section column name
user=User Name
#XTXT: Selected role count
selectedRoleToolbarText=Selected: {0}
#XTIT: Space detail section connections title
detailsSectionConnections=Connections
#XTIT: Space detail section data access title
detailsSectionDataAccess=Schema Access
#XTIT: Space detail section time data title
detailsSectionGenerateData=Time Data
#XTIT: Space detail section members title
detailsSectionUsers=Members
#XTIT: Space detail section Users title
detailsSectionUsersTitle=Users
#XTIT: Out of Storage
insufficientStoragePopoverTitle=Out of Storage
#XTIT: Storage distribution
storageDistributionPopoverTitle=Disk Storage Used
#XTXT: Out of Storage popover text
insufficientStorageText=To create a new space, please reduce the assigned storage of another space or delete a space that you don’t need anymore. You can increase your total system storage by calling Manage Plan.
#XMSG: Space id length warning
spaceIdLengthWarning=Maximum of {0} characters exceeded.
#XMSG: Space name length warning
spaceNameLengthWarning=Maximum of {0} characters exceeded.
#XMSG: Space id reserved prefix warning
spaceIdnWarningReservedPrefix=Please do not use the {0} prefix to avoid possible conflicts.
#YMSE: Error while loading open SQL schemas
loadLocalSchemasError=Open SQL schemas couldn’t be loaded.
#YMSE: Error while creating open SQL schema
createLocalSchemaError=Open SQL schema couldn’t be created.
#YMSE: Error while loading all remote connections
loadRemoteConnectionsError=Could not load all remote connections.
#YMSE: Error while loading space details
loadSpaceDetailsError=The space details couldn’t be loaded.
#YMSE: Error while deploying space details
deploySpaceDetailsError=Space couldn’t be deployed.
#YMSE: Error while copying space details
copySpaceDetailsError=Space couldn’t be copied.
#YMSE: Error while loading storage data
loadStorageDataError=Storage data couldn’t be loaded.
#YMSE: Error while loading all users
loadAllUsersError=Could not load all users.
#YMSE: Failed to reset password
resetPasswordError=Password couldn’t be reset.
#XMSG: Success message after updating space
resetPasswordSuccessMessage=New password set for schema
#YMSE: DP Agent-name too long
DBAgentNameError=The name of the DP agent is too long.
#YMSE: Schema-name not valid.
schemaNameError=The name of the schema is invalid.
#YMSE: User name not valid.
UserNameError=The user name is invalid.
#XTIT: Consumption by Storage Type
consumptionStorageTypeText=Consumption by Storage Type
#XTIT: Consumption by Schema
consumptionSchemaText=Consumption by Schema
#XTIT: Overall Consumption by schema
overallConsumptionSchemaText=Overall Table Consumption by Schema
#XTIT: Overall Consumption by Table Type
overallConsumptionTableTypeText=Overall Consumption by Table Type
#XTIT: Tables
tableDetailsText=Table Details
#XTIT: Table Storage Consumption
tableStorageConsumptionText=Table Storage Consumption
#XFLD: Table Type label
tableTypeLabel=Table Type
#XFLD: Schema label
schemaLabel=Schema
#XFLD: reset table tooltip
resetTable=Reset Table
#XFLD: In-Memory label in space monitor
inMemoryLabel=Memory
#XFLD: Disk label in space monitor
diskLabel=Disk
#XFLD: Yes
yesLabel=Yes
#XFLD: No
noLabel=No
#XFLD: switch label: Want the data in this space to be consumable by default?
allowConsumptionByDefault=Do you want the data in this space to be consumable by default?
#XFLD: Business Name
businessNameLabel=Business Name
#XFLD: Refresh
refresh=Refresh
#XMSG: No filter results title
noFilterResultsTitle=It seems that your filter settings are not showing any data.
#XMSG: No filter results message
noFilterResultsMsg=Try refining your filter settings and if you still don’t see any data then; create some tables in the Data Builder. Once they consume storage, you’ll be able to monitor them here.
#XMSG: Space monitoring Hana state red title
spaceMonitorHanaRedTitle=The run-time database is not available.
#XMSG: Space monitoring Hana state red message
spaceMonitorHanaRedMsg=As the run-time database is not available, certain features are disabled and we cannot display any information on this page.
#YMSE: Error while creating space schema user
createSpaceSchemaUserError=Space schema user couldn’t be created.
#YMSE: Error User name already exists
userAlreadyExistsError=User name already exists.
#YMSE: Error Authentication failed
authenticationFailedError=Authentication failed.
#XMSG: Message strip message when a schema user is locked
userLockedMessageStrip=The user is locked due to too many failed logins. Please request a new password to unlock the user.
#XMSG: Success message after reset and unlock schema user
resetUnlockPasswordSuccessMessage=New password set and user unlocked
#XMSG: user is locked message
userLockedMessage=User is locked.
#XCOL: Users table-view column Role
spaceRole=Role
#XCOL: Users table-view column Scoped Role
spaceScopedRole=Scoped Role
#XCOL: Users table-view column Space Admin
spaceAdmin=Space Administrator
#XFLD: User section dropdown value Viewer
viewer=Viewer
#XFLD: User section dropdown value Modeler
modeler=Modeler
#XFLD: User section dropdown value Data Integrator
dataIntegrator=Data Integrator
#XFLD: User section dropdown value Space Administrator
spaceAdministrator=Space Administrator
#XMSG: Success message -  Space Role updated successfully.
updateUserSpaceRoleSuccess=Space role updated
#XMSG: Error message - Failed to update Space Role.
updateUserSpaceRoleError=Space Role was not successfully updated.
#XFLD:
databaseUserNameSuffix=Database User Name Suffix
#XTXT: Space Schema password text
spaceSchemaPasswordText=To set up a connection to this schema, please copy your password. In case you’ve forgotten your password, you can always request a new one.
#XFLD: Cloud Platform label in space schema dialog
cloudPlatformLabel=Cloud Platform
#XTXT: Cloud Platform text in space schema dialog
cloudPlatformText=To set up access via this user, enable the consumption and copy the credentials. In case you can only copy the credentials without a password, make sure you add the password later.
#XFLD: enable consumption in CloudPlatform label in space schema dialog
enableConsumptionInCloudPlatformLabel=Enable Consumption in Cloud Platform
#XFLD: Credentials for User-Provided Service label in space schema dialog
cloudPlatformCredentialsLabel=Credentials for User-Provided Service:
#XFLD: Credentials for User-Provided Service (Without Password) label in space schema dialog
cloudPlatformCredentialsWithoutPasswordLabel=Credentials for User-Provided Service (Without Password):
#XFLD: Copy Credentials label in space schema dialog
copyCredentialsLabel=Copy Credentials Without Password
#XFLD: Copy Credentials label in space schema dialog
copyFullCredentialsLabel=Copy Full Credentials
#XFLD: Copy Password label in space schema dialog
copyPasswordLabel=Copy Password
#XMSG: Credentials copied to clipboard
credentialsCopiedMessage=Credentials copied to clipboard
#XMSG: Password copied to clipboard
passwordCopiedMessage=Password copied to clipboard
#XMSG: create database user dialog title
createDatabaseUserDialogTitle=Create Database User
#XMSG: Database Users section title
databaseUsers=Database Users
#XMSG: Database User Details dialog title
databaseUserDetailsDialogTitle=Database User Details
#XFLD: database user read audit log
databaseUserAuditLogRead=Enable Audit Logs for Read Operations and Keep Logs for
#XFLD: database user change audit log
databaseUserAuditLogChange=Enable Audit Logs for Change Operations and Keep Logs for
#XMSG: Cloud Platform Access
cloudPlatformAccess=Cloud Platform Access
#XFLD: Enable Consumption in Cloud Platform hint
enableConsumptionCloudPlatformHint=Set up access to your HANA Deployment Infrastructure (HDI) container via this database user. To connect to your HDI container, SQL modeling must be switched on
#XFLD: Enable HDI Consumption
enableConsumptionCloudPlatform=Enable HDI Consumption
#XFLD: Enable Consumption hint
enableConsumptionHint=Do you want the data in your space to be consumable by other tools or apps?
#XFLD: Enable Consumption
enableConsumption=Enable SQL Consumption
#XFLD: Enable Modeling
enableModeling=Enable SQL Modeling
#XMSG: Privileges for Data Modeling
privilegesModeling=Data Ingestion
#XMSG: Privileges for Data Consumption
privilegesConsumption=Data Consumption for External Tools
#XFLD: SQL Modeling
sqlModeling=SQL Modeling
#XFLD: SQL Consumption
sqlConsumption=SQL Consumption
#XFLD: enabled
enabled=Enabled
#XFLD: disabled
disabled=Disabled
#XFLD: Edit Privileges
editPrivileges=Edit Privileges
#XFLD: Open Database Explorer
openDBX=Open Database Explorer
#XFLD: create database user hint
databaseCreateHint=Please note that it will not be possible to change the user name again after saving.
#XFLD: Internal Schema Name
internalSchemaName=Internal Schema Name
#YMSE: Failed to load database users
loadDatabaseUserError=Failed to load database users
#YMSE: Failed to delete database user
deleteDatabaseUsersError=Failed to delete database users
#XMSG: Success message after deleting database user
deleteDatabaseUserSuccess=Database user deleted
#XMSG: Success message after deleting database users
deleteDatabaseUsersSuccess=Database users deleted
#XMSG: Success message after creating database user
createDatabaseUserSuccessMessage=Database user created
#YMSE: Failed to create database user
createDatabaseUserError=Failed to create database user
#XMSG: Success message after updating database user
updateDatabaseUserSuccessMessage=Database user updated
#YMSE: Failed to update database user
updateDatabaseUserError=Failed to update database user
#XFLD: HDI Consumption
hdiConsumption=HDI Consumption
#XTIT: Space detail section Database Access title
detailsSectionDatabaseAccess=Database Access
#XTXT: Database users consumption hint
databaseUsersConsumptionHint=Make your space data consumable by default. The models in the builders will automatically allow the data to be consumable.
#XFLD: Default Consumption of Space Data:
defaultConsumptionOfSpaceData=Default Consumption of Space Data:
#XFLD: Database User Name
databaseUserName=Database User Name
#XMSG: Database User creation validation error message
databaseUserValidationError=It looks likes some fields are invalid. Please check the required fields to continue.
#XMSG: SQL modeling can’t be enabled since this user has been migrated.
migratedDatabaseUserHint=Data ingestion can’t be enabled since this user has been migrated.
#XBUT: Remove Button Text
remove=Remove
#XBUT: Remove Spaces Button Text
removeSpaces=Remove Spaces
#XBUT: Remove Objects Button Text
removeObjects=Remove Objects
#XMSG: No members have been added yet.
noMembersAssigned=No members have been added yet.
#XMSG: No users have been added yet.
noUsersAssigned=No users have been added yet.
#XMSG: No database users have been created or your filter is not showing any data.
noDatabaseUsers=No database users have been created, or your filter is not showing any data.
#XMSG: Please enter a user name.
noDatabaseUsername=Please enter a user name.
#XMSG: The user name is too long. Please use a shorter one.
databaseUsernameTooLong=The user name is too long. Please use a shorter one.
#XMSG: No privileges have been enabled and this database user will have limited functionality. Do you still want to continue?
noPrivilegesConfirmationMessage=No privileges have been enabled, and this database user will have limited functionality. Do you still want to continue?
#XMSG: To enable audit logs for change operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogChangeMessage=To enable audit logs for change operations, data ingestion needs to be enabled as well. Do you want to do this?
#XMSG: To enable audit logs for read operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogReadMessage=To enable audit logs for read operations, data ingestion needs to be enabled as well. Do you want to do this?
#XMSG: To enable HDI consumption data ingestion and data consumption needs to be enabled. Do you want to enable all?
enableHDIConsumptionMessage=To enable HDI consumption, data ingestion and data consumption need to be enabled as well. Do you want to do this?
#XMSG:
databaseUserPasswordText=To set up a connection to this database user, please copy your password. In case you forget your password, you can always request a new one.
#XTIT: Space detail section members title
detailsSectionMembers=Members
#XMSG: New password set
newPasswordSet=New password set
#XFLD: Data Ingestion
dataIngestion=Data Ingestion
#XFLD: Data Consumption
dataConsumption=Data Consumption
#XFLD: Privileges
privileges=Privileges
#XFLD: Enable Data ingestion
enableDataIngestion=Enable Data Ingestion
#XMSG: Log the read and change operations for data ingestion.
databaseUserAuditHint=Log the read and change operations for data ingestion.
#XMSG: Make your space data available in your HDI containers.
databaseUserHDIHint=Make your space data available in your HDI containers.
#XFLD: Enable Data consumption
enableDataConsumption=Enable Data Consumption
#XMSG: Allow other apps or tools to consume your space data.
enableDataConsumptionHint=Allow other apps or tools to consume your space data.
#XMSG: To set up access via this schema user copy the credentials to your user-provided service. In case you can only copy the credentials without a password make sure to add the password later.
databaseUserHDIConsumptionHint=To set up access via this database user, copy the credentials to your user-provided service. In case you can only copy the credentials without a password, make sure you add the password later.
#XFLD: Data Flow Runtime Capacity label in space overview
dataFlowRuntimeCapacity=Data Flow Runtime Capacity ({0}:{1} hours of {2} hours)
#XFLD: Could not load Data Flow Runtime Capacity
dataFlowRuntimeCapacityError=Could not load data flow runtime capacity
#XMSG: User can grant data consumption to other users.
databaseUserConsumptionWithGrantOptionHint=User can grant data consumption to other users.
#XFLD: Enable data consumption with grant option
enableConsumptionWithGrantOption=Enable Data Consumption with Grant Option
#XMSG: To enable data consumption with grant option data consumption needs to be enabled. Do you want to enable both?
enableConsumptionWithGrantMessage=To enable data consumption with grant option, data consumption needs to be enabled. Do you want to enable both?
#XFLD: Enable Automated Predictive Library (APL) and Predictive Analysis Library (PAL)
allowUsingScriptServerFunctionality=Enable Automated Predictive Library (APL) and Predictive Analysis Library (PAL)
#XMSG: User can use SAP HANA Cloud embedded machine learning functions.
allowUsingScriptServerFunctionalityHint=User can use SAP HANA Cloud embedded machine learning functions.
#XFLD: Password Policy
passwordPolicy=Password Policy
#XMSG: Password Policy
passwordPolicyHint=Enable or disable the configured password policy here.
#XFLD: Enable Password Policy
enablePasswordPolicy=Enable Password Policy
#XMSG: Read Access to the Space Schema
readAccessTitle=Read Access to the Space Schema
#XMSG: read access hint
readAccessHint=Allow the database user to connect external tools to the space schema and read views that are exposed for consumption.
#XFLD: Space Schema
spaceSchema=Space Schema
#XFLD: Enable Read Access (SQL)
enableReadAccess=Enable Read Access (SQL)
#XMSG: Allow the user to grant read access to other users.
withGrantOptionHint=Allow the user to grant read access to other users.
#XFLD: With Grant Option
withGrantOption=With Grant Option
#XMSG: Make your space data available in your HDI containers.
enableHDIConsumptionHint=Make your space data available in your HDI containers.
#XFLD: Enable HDI Consumption
enableHDIConsumption=Enable HDI Consumption
#XMSG: Write Access to the User’s Open SQL Schema
writeAccessTitle=Write Access to the User’s Open SQL Schema
#XMSG: write access hint
writeAccessHint=Allow the database user to connect external tools to the user’s Open SQL schema to create data entities and ingest data for use in the space.
#XFLD: Open SQL Schema
openSQLSchema=Open SQL Schema
#XFLD: Enable Write Access (SQL DDL & DML)
enableWriteAccess=Enable Write Access (SQL, DDL, & DML)
#XMSG: audit hint
auditHint=Log the read and change operations in the Open SQL schema.
#XMSG: data consumption hint
dataConsumptionHint=Expose all new views in the space by default for consumption. Modelers can override this setting for individual views via the “Expose for Consumption” switch in the view output side panel. You can also choose the formats in which views are exposed.
#XFLD: Expose for Consumption by Default
exposeDataConsumption=Expose for Consumption by Default
#XMSG: database users hint consumption hint
databaseUsersHint2New=Create database users to connect external tools to SAP Datasphere. Set privileges to allow users to read space data and to create data entities (DDL) and ingest data (DML) for use in the space.
#XFLD: Read
read=Read
#XFLD: Read (HDI)
readHDI=Read (HDI)
#XFLD: Write
write=Write
#XMSG: HDI Containers Hint
HDIContainersHint2=Enable access to your SAP HANA Deployment Infrastructure (HDI) containers in your space. Modelers can use HDI artifacts as sources for views, and HDI clients can access your space data.
#XMSG: Open info dialog
openDatabaseUserInfoDialog=Open the info dialog
#XMSG: Database user is locked. Open info dialog to unlock
openLockedDatabaseUserInfoDialog=Database user is locked. Open dialog to unlock
#XFLD: Table
table=Table
#XFLD: Space section partner configuration title
partnerConnectionConfigurationSectionTitle=Partner Connection
#XFLD: Space section partner configuration subtitle
partnerConnectionConfigurationSectionSubTitle=Partner Connection Configuration
#XFLD: Space section partner configuration hint
partnerConnectionConfigurationSectionHint=Define your own partner connection tile by adding your iFrame URL and icon. This configuration is available only for this tenant.
#XFLD: Table Name Field
partnerConnectionConfigurationName=Tile Name
#XFLD: Table iFrame URL field
partnerConnectionConfigurationiFrameURL=iFrame URL
#XFLD: Table iFrame Post Message Origin field
partnerConnectionConfigurationiFramePostMessageOrigin=iFrame Post Message Origin
#XFLD: Table icon/image name
partnerConnectionConfigurationIcon=Icon
#XMSG: Table no result found
partnerConnectionConfigurationNotfound=No partner connection configuration(s) could be found.
#XMSG: Partner Connection Confgurations are not available
partnerConnectionConfigurationNotAvailable=Partner connection configurations cannot be displayed when the runtime database is unavailable.
#XFLD: Dialog Create Partner Connection Configuration
partnerConnectionConfigurationDialogTitleAdd=Create Partner Connection Configuration
#XFLD: Dialog Create Partner Connection Configuration Upload Icon
partnerConnectionConfigurationButtonUloadPartnerIcon=Upload Icon
#XFLD: Dialog Create Partner Connection Configuration Select
partnerConnectionConfigurationSelectIcon=Select (maximum size of 200KB)
#XFLD: Dialog Create Partner Connection Configuration Enter a name
partnerConnectionConfigurationNamePlaceholder=Partner Tile Example
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL
partnerConnectionConfigurationiFrameURLPlaceholder=http://www.example.com
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL Check
partnerConnectionConfigurationiFramePostMessageOriginPlaceholder=.example.com
#XFLD: Dialog Create Partner Connection Configuration Browse
partnerConnectionConfigurationBrowseButton=Browse
#XFLD: Create Partner Connection Configuration Msg
partnerConnectionConfigurationUploadMsg=Partner Connection Configuration has been successfully created.
#XMSG: Error while deleting Partner Connection configuration(s)
partnerConnectionConfigurationDeleteError=An error occurred while deleting Partner Connection configuration(s).
#XMSG: Partner Connection Configuration has been successfully deleted
partnerConnectionConfigurationDeleteSuccess=Partner Connection Configuration has been successfully deleted.
#XMSG: Error while retrieving Partner Connection Configurations
partnerConnectionConfigurationLoadError=An error occurred while retrieving Partner Connection Configurations.
#XMSG: Error fileSizeLimitExceeded
partnerConnectionConfigurationfileSizeLimitExceeded=The file could not be uploaded because it exceeds the maximum size of 200KB.
#XBUT: Create Partner Connection Configuration
partnerConnectionConfigurationButtonCreate=Create Partner Connection Configuration
#XBUT: Delete Partner Connection Configuration
partnerConnectionConfigurationButtonDelete=Delete Partner Connection Configuration.
#XMSG: Partner Tile Creation Failure
upsertPartnerTileFailed=Partner Tile could not be created.
#XMSG: Partner Tile Deletion Failure
deletePartnerTileFailed=Partner Tile could not be deleted.
#XMSG: HANA cloud connector settings reset failure
resetHANASCCSettingsFailed=Resetting Customer SAP HANA Cloud Connector Settings failed
#XFLD: Workload Class
workloadClass=Workload Class
#XFLD: Workload Management
workloadManagement=Workload Management
#XFLD: Priority
workloadClassPriority=Priority
#XMSG:
workloadManagementPriorityHint=You can specify the prioritization of this space when querying the database. Enter a value from 1 (lowest priority) to 8 (highest priority). In a situation where spaces are competing for available threads, those with higher priorities are run before spaces with lower priorities.
#XMSG:
workloadClassPriorityHint=You can specify the priority of the space from 0 (lowest) to 8 (highest). The statements of a space with a high priority are executed before the statements of other spaces with a lower priority. The default priority is 5. As the value 9 is reserved to system operations, it is not available for a space.
#XFLD: Statement Limits
workloadclassStatementLimits=Statement Limits
#XFLD: Workload Configuration
workloadConfiguration=Workload Configuration
#XMSG:
workloadClassStatementLimitsHint=You can specify the maximum number (or percentage) of threads and GBs of memory that statements running concurrently in the space can consume. You can enter any value or percentage between 0 (no limit) and the total memory and threads available in the tenant. \n\n If you specify a thread limit, be aware that it can lower performance. \n\n If you specify a memory limit, the statements that reach the memory limit are not run.
#XMSG:
workloadClassStatementLimitsDescription=The default configuration provides generous resource limits, while preventing any single space from overloading the system.
#XMSG:
workloadClassStatementLimitCustomDescription=You can set maximum total thread and memory limits that statements running concurrently in the space can consume.
#XMSG:
totalStatementThreadLimitHelpText=Setting the thread limit too low may impact statement performance, while excessively high values or 0 may allow the space to consume all available system threads.
#XMSG:
totalStatementMemoryLimitHelpText=Setting the memory limit too low may cause out-of-memory issues, while excessively high values or 0 may allow the space to consume all available system memory.
#XMSG:
totalStatementThreadLimitHelpTextRestricted=Enter a percentage between 1% and 70% (or the equivalent number) of the total number of threads available in your tenant. Setting the thread limit too low may impact statement performance, while excessively high values may impact the performance of statements in other spaces.
#XMSG:
totalStatementThreadLimitHelpTextDynamic=Enter a percentage between 1% and {0}% (or the equivalent number) of the total number of threads available in your tenant. Setting the thread limit too low may impact statement performance, while excessively high values may impact the performance of statements in other spaces.
#XMSG:
totalStatementMemoryLimitHelpTextNew=Enter a value or percentage between 0 (no limit) and the total amount of memory available in your tenant. Setting the memory limit too low may impact statement performance, while excessively high values may impact the performance of statements in other spaces.
#XFLD: Total Statement Thread Limit
totalStatementThreadLimit=Total Statement Thread Limit
#XFLD: Segmented button label
workloadclassPercentage=%
#XFLD: Segmented button label
workloadclassGigaByte=GB
#XFLD, 80: Segmented button label
workloadclassThreads=Threads
#XFLD: Total Statement Memory Limit
totalStatementMemoryLimit=Total Statement Memory Limit
#YMSE: Error while loading customer hana info
loadCustomerHanaInfoError=Failed to load customer SAP HANA Info.
#XMSG:
minimumLimitReached=Minimum limit reached.
#XMSG:
maximumLimitReached=Maximum limit reached.
#XMSG: Name Taken for Technical Name
technical-name-taken=A connection with the technical name you entered already exists. Please enter another name.
#XMSG: Name Too long for Technical Name
technical-name-too-long=The technical name you entered exceeds 40 characters. Please enter a name with less characters.
#XMSG: Technical name field empty
technical-name-field-empty=Please enter a technical name.
#XMSG: Generic error when user enters invalid characters for a field
field-invalid-characters=You can only use letters (a-z), numbers (0-9), and underscores (_) for the name.
#XMSG: Generic error when user enters invalid initial characters for a field
field-underscore-trim=The name you enter cannot start or end with an underscore (_).
#XFLD: Checkbox label for Enable Statement Limits
enableStatementLimits=Enable Statement Limits
#XFLD: Connection SubSection Settings Title in space details
connectionSettings=Settings
#XMSG: Connections tool hint in Space details section
connectionsToolHint=To create or edit connections, open the Connections app from the side navigation or click here:
#XBUT: Connection list Open Connection Tool Button
button-connectiontool=Go to Connections
#XFLD: Not deployed label on space tile
notDeployedLabel=Space hasn’t been deployed yet.
#XFLD: Not deployed additional text on space tile
notDeployedText=Please deploy the space.
#XFLD: Corrupt space label on space tile
corruptSpace=Something went wrong.
#XFLD: Corrupt space additional text on space tile
corruptSpaceHint=Try redeploying or contact support
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Audit Log Data
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Administrative Data
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Other Data
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Data in Spaces
#XMSG: Unlock Space confirmation text
unlockSpaceConfirmation=Do you really want to unlock the space?
#XMSG: Lock Space confirmation text
lockSpaceConfirmation=Do you really want to lock the space?
#XFLD: Lock
lock=Lock
#XFLD: Unlock
unlock=Unlock
#XFLD: Locking
locking=Locking
#XMSG: Success message after locking space
lockSpaceSuccessMsg=Space locked
#XMSG: Success message after unlocking space
unlockSpaceSuccessMsg=Space unlocked
#XMSG: Success message after locking spaces
lockSpacesSuccessMsg=Spaces locked
#XMSG: Success message after unlocking spaces
unlockSpacesSuccessMsg=Spaces unlocked
#YMSE: Error while locking a space
lockSpaceError=The space cannot be locked.
#YMSE: Error while unlocking a space
unlockSpaceError=The space cannot be unlocked.
#XTIT: popup title Warning
confirmationWarningTitle=Warning
#XMSG: Message strip message when a space was locked manually
manualLockedMessage=The space has been locked manually.
#XMSG: Message strip message when a space was locked due to the amount of generated audit logs
auditLogLockedMessage=The space has been locked by the system because audit logs consume a large quantity of GB of disk.
#XMSG: Message strip message when a space was locked because the assigned quota was exceeded.
quotaLockedMessage=The space has been locked by the system because it exceeds its allocations of memory or disk storage.
#XMSG: Unlock Spaces confirmation text
unlockSpacesConfirmation=Do you really want to unlock the selected spaces?
#XMSG: Lock Spaces confirmation text
lockSpacesConfirmation=Do you really want to lock the selected spaces?
#XTIT: Scoped role editor dialog title
scopedRoleEditorDialogTitle=Scoped Role Editor
#XTIT: ECN Management title
ecnManagementTitle=Space and Elastic Compute Node Management
#XFLD: ECNs
ecns=Elastic Compute Nodes
#XFLD: ECN phase Ready
ecnReady=Ready
#XFLD: ECN phase Running
ecnRunning=Running
#XFLD: ECN phase Initial
ecnInitial=Not Ready
#XFLD: ECN phase Starting
ecnStarting=Starting
#XFLD: ECN phase Starting Failed
ecnStartingFailed=Starting Failed
#XFLD: ECN phase Stopping
ecnStopping=Stopping
#XFLD: ECN phase Stopping Failed
ecnStoppingFailed=Stopping Failed
#XBTN: Assign Button
assign=Add Spaces
#XBTN: Start Header-Button
start=Start
#XBTN: Update Header-Button
repair=Update
#XBTN: Stop Header-Button
stop=Stop
#XFLD: ECN hours remaining
ecnHoursRemaining=1000 hours remaining
#XFLD: ECN block-hours remaining
ecnBlockHoursRemaining={0} Block-Hours Remaining
#XFLD: ECN block-hour remaining
ecnBlockHourRemaining={0} Block-Hour Remaining
#XTIT: ECN creation dialog title
ecnCreationDialogTitle=Create Elastic Compute Node
#XTIT: ECN edit dialog title
ecnEditDialogTitle=Edit Elastic Compute Node
#XTIT: ECN deletion dialog title
ecnDeletionDialogTitle=Delete Elastic Compute Node
#XTIT: ECN Space assignment dialog title
ecnSpaceAssignmentDialogTitle=Add Spaces
#XFLD: ECN ID
ECNIDLabel=Elastic Compute Node
#XTXT: Selected toolbar text
selectedToolbarText=Selected: {0}
#XTIT: Elastic Compute Nodes
ECNslong=Elastic Compute Nodes
#XTIT: Object assignment - Table header: Objects available
objectsAvailable=Number of Objects
#XTIT: Object assignment - Dialog header text
selectObjects=Select the spaces and objects you want to add to your elastic compute node:
#XTIT: Object assignment - Table header title: Objects
objects=Objects
#XTIT: Object assignment - Table header: Type
type=Type
#XMSG: Message strip delete database user audit warning
deleteDatabaseUserWarningMessageStrip=Be aware that deleting a database user will result in the deletion of all generated audit log entries. If you want to keep the audit logs, consider exporting them before you delete the database user.
#XMSG: Message strip delete HDI container audit logs impact
deleteHDIContainerWarningMessageStrip=Be aware that unassigning an HDI container from the space will result in the deletion of all generated audit log entries. If you want to keep the audit logs, consider exporting them before you unassign the HDI container.
#XTXT: All audit logs
allAuditLogs=All audit log entries generated for the space
#XMSG: Message strip disable audit warning
disableAuditWarningMessageStrip=Be aware that disabling an audit policy (read or change operations) will result in the deletion of all its audit log entries. If you want to keep the audit log entries, consider exporting them before you disable the audit policy.
#XTIT: No Spaces assigned to ECN Illustration title
noSpacesECNTitle=No spaces or objects added yet
#XTIT: No Spaces assigned to ECN Illustration description
noSpacesECNDescription=To start working with your elastic compute node, add a space or objects to it.
#XTIT: No Spaces Illustration title
noSpacesTitle=No space created yet
#XTIT: No Spaces Illustration description
noSpacesDescription=To start acquiring data, create a space.
#XTIT: No Spaces Illustration title
noDeletedSpacesTitle=The recycle bin is empty
#XTIT: No Spaces Illustration description
noDeletedSpacesDescription=You can recover your deleted spaces from here.
#XTXT:  {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDatabaseUsers=Once the space is deployed, the following database users will be {0} deleted and cannot be recovered:
#XTIT: Delete database users
deleteDatabaseUsersTitle=Delete Database Users
#YMSE: Error if ecn id already exists
ecnIDInputDuplicateError=ID already exists.
#YMSE: Error if ecn id contains invalid characters
ecnIDInputInvalidError=Please use lower case characters a - z and numbers 0 – 9 only
#YMSE: Error if ecn id is too short
ecnIDInputTooShortError=ID has to be at least {0} characters long.
#XMSG: ecn id length warning
ecnIdLengthWarning=Maximum of {0} characters exceeded.
#XFLD: open System Monitor
systemMonitor=System Monitor
#XFLD: open ECN schedule dialog menu entry
schedule=Schedule
#XFLD: open create ECN schedule dialog
createSchedule=Create Schedule
#XFLD: open change ECN schedule dialog
changeSchedule=Edit Schedule
#XFLD: open delete ECN schedule dialog
deleteSchedule=Delete Schedule
#XFLD: open self assign ECN schedule dialog
selfAssignSchedule=Assign Schedule to Me
#XFLD: open pause ECN schedule dialog
pauseSchedule=Pause Schedule
#XFLD: open resume ECN schedule dialog
resumeSchedule=Resume Schedule
#XFLD: View Logs
viewLogs=View Logs
#XFLD: Compute Blocks
computeBlocks=Compute Blocks
#XFLD: Memory label in ECN creation dialog
ecnMemory=Memory (GB)
#XFLD: Storage label in ECN creation dialog
ecnStorage=Storage (GB)
#XFLD: Number of CPU label in ECN creation dialog
ecnCPUCount=Number of CPU
#XFLD: ECN updated by label
changedBy=Changed By
#XFLD: ECN updated on label
changedOn=Changed On
#XMSG: Success message after creating a Elastic Compute Node
createEcnSuccessMessage=Elastic compute node created
#YMSE: Error while creating a Elastic Compute Node
createEcnError=Elastic compute node couldn’t be created
#XMSG: Success message after updating a Elastic Compute Node
updateEcnSuccessMessage=Elastic compute node updated
#XMSG: Error while updating a Elastic Compute Node
udpateEcnError=Elastic compute node couldn’t be updated
#XMSG: Success message after deleting a Elastic Compute Node
deleteEcnSuccessMessage=Elastic compute node deleted
#YMSE: Error while deleting a Elastic Compute Node
deleteEcnError=Elastic compute node couldn’t be deleted
#XMSG: Success message after starting a Elastic Compute Node
startingEcnSuccessMessage=Starting elastic compute node
#XMSG: Success message after stopping a Elastic Compute Node
stoppingEcnSuccessMessage=Stopping elastic compute node
#YMSE: Error while starting a Elastic Compute Node
startingEcnError=Elastic compute node couldn’t start
#YMSE: Error while stopping a Elastic Compute Node
stoppingEcnError=Elastic compute node couldn’t stop
#XBUT: Add Object button for an ECN
assignObjects=Add Objects
#XFLD: Enable auto object assignment label
enableAutoObjAssignment=Add All Objects Automatically
#XFLD: object type label to be assigned
objectTypeLabel=Type (Semantic Usage)
#XFLD: assigned object type label
assignedObjectTypeLabel=Type
#XFLD: technical name label
TechnicalNameLabel=Technical Name
#XFLD: Object selecttion ECN label
objectSelectionECNLabel=Select the objects you want to add to the elastic compute node
#XTIT: Add objects dialog title
assignObjectsTitle=Add Objects of
#XFLD: object label with object count
objectLabel=Object
#XMSG: No objects available to add message.
noObjectsToAssign=No objects available to add.
#XMSG: No objects assigned message.
noAssignedObjects=No objects added.
#XTIT: Remove objects popup title
removeObjectsOrSpacesConfirmationTitle=Warning
#XTIT: Delete ECN popup button and title
deleteECNConfirmationButtonAndTitle=Delete
#XMSG: Remove objects popup text
removeObjectsConfirmation=Do you really want to remove the selected objects?
#XMSG: Remove spaces popup text
removeSpacesConfirmation=Do you really want to remove the selected spaces?
#XBUT: Confirmation button for objects dialog
removeObjectsOrSpacesConfirmationBtn=Remove Spaces
#XMSG: Success message after removing objects
removeObjectsSuccessMessage=Exposed objects have been removed
#XMSG: Success message after assigning objects
assignObjectsSuccessMessage=Exposed objects have been assigned
#XFLD: auto object assignment label for space tile
autoObjAssignmentLabel=All Exposed Objects
#XFLD: Spaces tab label
spacesTabLabel=Spaces
#XFLD: Exposed objects tab label
consumptionObjectsTabLabel=Exposed Objects
#XMSG: Success message after unassigning spaces
spacesUnassignedSuccessMessage=Spaces have been removed
#XMSG: Success message after unassigning space
spaceUnassignedSuccessMessage=Space has been removed
#YMSE: Error while assigning / unassigning spaces
spacesAssignUnassignError=Spaces couldn’t be assigned or removed.
#YMSE: Error while removing objects
removeObjectsError=We couldn't assign or remove the objects.
#YMSE: Error while removing object
removeObjectError=We couldn't assign or remove the object.
#YMSE: Error when saving the ecn with invalid value
invalidSizingPlanError=The number previously selected is no more valid. Please select a valid number.
#YMSE: Error when saving the ecn with invalid value
invalidPerformanceClassError=Please select a valid performance class.
#YMSE: Error when saving the ecn with invalid value
performanceClassNotAvailableError=The previously selected performance class "{0}" is currently not valid. Please select the valid performance class.
#XTXT: Delete ECN confirmation text
deleteConfirmationTxtECN=Are you sure you want to delete the elastic compute node?
#XFLD: tooltip for ? button
help=Help
#XFLD: ECN edit button label
editECN=Configure
#XFLD: Technical type label for ERModel
DWC_ERMODEL=Entity - Relationship Model
#XFLD: Technical type label for Local Table
DWC_LOCAL_TABLE=Local Table
#XFLD: Technical type label for Remote Table
DWC_REMOTE_TABLE=Remote Table
#XFLD: Technical type label for Analytic Model
DWC_ANALYTIC_MODEL=Analytic Model
#XFLD: Technical type label for Task Chain
DWC_TASKCHAIN=Task Chain
#XFLD: Technical type label for Data Flow
DWC_DATAFLOW=Data Flow
#XFLD: Technical type label for Replication Flow
DWC_REPLICATIONFLOW=Replication Flow
#XFLD: Technical type label for Transformation Flow
DWC_TRANSFORMATIONFLOW=Transformation Flow
#XFLD: Technical type label for Intelligent Lookup
DWC_IDT=Intelligent Lookup
#XFLD: Technical type label for Repository
DWC_REPOSITORY_PACKAGE=Repository
#XFLD: Technical type label for Enterprise Search
DWC_ESMODEL=Enterprise Search
#XFLD: Technical type label for View
DWC_VIEW=View
#XFLD: Technical type label for Data Product
DWC_DATAPRODUCT=Data Product
#XFLD: Technical type label for Data Access Control
DWC_DAC=Data Access Control
#XFLD: Technical type label for Folder
DWC_FOLDER=Folder
#XFLD: Technical type label for Data Entity
DWC_BUSINESS_ENTITY=Business Entity
#XFLD: Technical type label for Business Entity Variant
DWC_BUSINESS_ENTITY_VARIANT=Business Entity Variant
#XFLD: Technical type label for Responsibility Scenario
DWC_AUTH_SCENARIO=Responsibility Scenario
#XFLD: Technical type label for Fact Model
DWC_FACT_MODEL=Fact Model
#XFLD: Technical type label for Transformation Flow
DWC_PERSPECTIVE=Perspective
#XFLD: Technical type label for Consumption Model
DWC_CONSUMPTION_MODEL=Consumption Model
#XFLD: Technical type label for Remote Connection
DWC_REMOTECONNECTION=Remote Connection
#XFLD: Technical type label for Fact Model Variant
DWC_FACT_MODEL_VARIANT=Fact Model Variant
#XMSG: Schedule created alert message
createScheduleSuccess=Schedule created
#XMSG: Schedule updated alert message
updateScheduleSuccess=Schedule updated
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Schedule deleted
#XMSG: Schedule takeover success message
ownerChangeScheduleSuccess=Schedule assigned to you
#XMSG: Message for starting pausing of schedule
pauseScheduleStarted=Pausing 1 schedule
#XMSG: Message for starting resuming of schedule
resumeScheduleStarted=Resuming 1 schedule
#XFLD: Segmented button label
availableSpacesButton=Available
#XFLD: Segmented button label
selectedSpacesButton=Selected
#XFLD: Visit website button text
visitWebsite=Visit Website
#XMSG: Warning message on unselecting source language
sourceLanguageRemovalWarning=The previously selected source language will be removed.
#XBUT: enable translation confirmation button
enableTranslationConfirmationBtn=Enable
#XFLD: ECN performance class label
performanceClassLabel=Performance Class
#XTXT performance class memory text
memoryText=Memory
#XTXT performance class compute text
computeText=Compute
#XTXT performance class high-compute text
highComputeText=High-Compute
#XBUT: Recycle Bin Button Text
recycleBin=Recycle Bin
#XBUT: Restore Button Text
restore=Restore
#XMSG: Warning message for new Workload Management UI
priorityWarning=This area is in read-only. You can change the space priority in the System / Configuration / Workload Management area.
#XMSG: Warning message for new Workload Management UI
workloadWarning=This area is in read-only. You can change the space workload configuration in the System / Configuration / Workload Management area.
#XCOL: Space table-view column: Apache Spark vCPUs
sparkManagementVCPUs=Apache Spark vCPUs
#XCOL: Space table-view column: Apache Spark Memory (GB)
sparkManagementMemory=Apache Spark Memory (GB)
#XCOL: Space type (from Space Capability) for Data Ingestion Spaces
ingestionSpaceType=Data Product Ingestion
#XFLD: Deployment in progress nodatatext for spark table
sparkTableDeploymentInProgress=No data available as the space is currently being deployed
#XFLD: Runtime data being loaded nodatatext for spark table
sparkTableDeploymentLoading=No data available as the space is currently being loaded
#XCOL: Space type (from Space Capability) for BW Private Cloud Edition
BWPCESpaceType=SAP BW
#XBUT: Go to Instance Mappings Button
navToMappingsButton=Edit Instance Mappings
#XFLD: SAP HANA Data Lake Storage label if less than 1GB
dataLakeStorageUsed=< 1 GB
