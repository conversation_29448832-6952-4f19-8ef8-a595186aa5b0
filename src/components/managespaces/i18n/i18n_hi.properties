#DO NOT REMOVE________________________
#XFLD: Breadcrumb for space monitoring page
spaceMonitorBreadcrumb=निरीक्षण करना
#XTXT: Type name for spaces in browser tab page title
space=स्पेस
#_____________________________________
#XFLD: Spaces label in
spaces=स्पेस
#XFLD: Manage plan button text
manageQuotaButtonText=योजना प्रबंधित करें
#XBUT: Manage resources button
manageResourcesButton=संसाधन प्रतिबंधित करें
#XFLD: Create space button tooltip
createSpace=स्पेस बनाएं
#XFLD: Create
create=बनाएं
#XFLD: Deploy
deploy=परिनियोजित करें
#XFLD: Page
page=पृष्ठ
#XFLD: Cancel
cancel=रद्द करें
#XFLD: Update
update=अपडेट करें
#XFLD: Save
save=सहेजें
#XFLD: OK
ok=ठीक
#XFLD: days
days=दिन
#XFLD: Space tile edit button label
edit=संपादित करें
#XFLD: Auto Assign all objects to space
autoAssign=स्वचालित असाइन
#XFLD: Space tile open monitoring button label
openMonitoring=निरीक्षण करें
#XFLD: Delete
delete=हटाएं
#XFLD: Copy Space
copy=प्रति बनाएं
#XFLD: Close
close=बंद करें
#XCOL: Space table-view column status
status=स्थिति
#XFLD: Space status active
activeLabel=सक्रिय करें
#XFLD: Space status locked
lockedLabel=लॉक
#XFLD: Space status critical
criticalLabel=क्रिटिकल
#XFLD: Space status cold
coldLabel=ठंडा
#XFLD: Space status deleted
deletedLabel=हटाया गया
#XFLD: Space status unknown
unknownLabel=अज्ञात
#XFLD: Space status ok
okLabel=स्वस्थ
#XFLD: Database user expired
expired=समाप्त हुआ
#XFLD: deployed
deployed=परिनियोजित
#XFLD: not deployed
notDeployed=परिनियोजित नहीं किया गया
#XFLD: changes to deploy
changesToDeploy=परिनियोजन में परिवर्तन
#XFLD: pending
pending=परिनियोजित करना
#XFLD: designtime error
designtimeError=डिज़ाइन-समय त्रुटि
#XFLD: runtime error
runtimeError=रन-समय त्रुटि
#XFLD: Space created by label
createdBy=निर्माता
#XFLD: Space created on label
createdOn=निर्माण दिनांक
#XFLD: Space deployed on label
deployedOn=परिनियोजित दिनांक
#XFLD: Space ID label
spaceID=स्पेस ID
#XFLD: Priority label
priority=वरीयता
#XFLD: Space Priority label
spacePriority=स्पेस की प्राथमिकता
#XFLD: Space Configuration label
spaceConfiguration=स्थान कॉन्फ़िगरेशन
#XFLD: Not available
notAvailable=उपलब्ध नहीं है
#XFLD: WorkloadType default
default=डिफ़ॉल्ट
#XFLD: WorkloadType custom
custom=कस्टम
#XFLD: Data Lake option label; refers to component SAP HANA Cloud, Data Lake
dataLakeAccess=डेटा लेक की एक्सेस
#XFLD: Translation label
translationLabel=अनुवाद
#XFLD: Source language label
sourceLanguageLabel=स्रोत भाषा
#XFLD: Translation CheckBox label
translationCheckBox=सक्षम अनुवाद
#XTXT: Popover hint that space has to be deployed to show user details
openDatabaseUserHintPopoverText=उपयोगकर्ता विवरण तक पहुंच के लिए स्पेस नियोजित करें.
#XTXT: Popover hint that space has to be deployed to open database explorer
openDatabaseExplorerHintPopoverText=Database Explorer खोलने के लिए स्पेस नियोजित करें.
#XTXT: Text for when the Data Lake check box is disabled
dataLakeOptionDisabled=डेटा लेक तक पहुंच प्राप्त करने के लिए आप इस स्पेस का उपयोग नहीं कर सकते हैं क्योंकि यह पहले से ही दूसरे स्पेस द्वारा उपयोग किया जा रहा है.
#XFLD: Data Lake check box text; refers to component SAP HANA Cloud, Data Lake
dataLakeCheckBox=डेटा लेक को एक्सेस करने के लिए इस स्पेस का उपयोग करें.
#XFLD: Space Priority minimum label extension
low=निम्न
#XFLD: Space Priority maximum label extension
high=उच्च
#XFLD: Space name label
spaceName=स्पेस का नाम
#XFLD: Enable deploy objects checkbox
enableDeployObjects=ऑब्जेक्ट परिनियोजित करें
#XTIT: Copy spaces dialog title
copySpaceDialogTitle={0} की प्रति बनाएं
#XTXT Human readable text for unselected source language
unSelectedSourceLanguage=(चयनित नहीं है)
#XTXT Human readable text for language code "af"
af=अफ़्रीकी
#XTXT Human readable text for language code "ar"
ar=अरबी
#XTXT Human readable text for language code "bg"
bg=बुल्गारियाई
#XTXT Human readable text for language code "ca"
ca=कातालान
#XTXT Human readable text for language code "zh"
zh=सरलीकृत चीनी
#XTXT Human readable text for language code "zf"
zf=चीनी
#XTXT Human readable text for language code "hr"
hr=क्रोएशियाई
#XTXT Human readable text for language code "cs"
cs=चेक
#XTXT Human readable text for language code "cy"
cy=वेल्श
#XTXT Human readable text for language code "da"
da=डेनिश
#XTXT Human readable text for language code "nl"
nl=डच
#XTXT Human readable text for language code "en-UK"
en-UK=अंग्रेज़ी (यूनाइटेड किंगडम)
#XTXT Human readable text for language code "en"
en=अंग्रेज़ी (संयुक्त राज्य)
#XTXT Human readable text for language code "et"
et=एस्टोनियाई
#XTXT Human readable text for language code "fa"
fa=फ़ारसी
#XTXT Human readable text for language code "fi"
fi=फ़िनिश
#XTXT Human readable text for language code "fr-CA"
fr-CA=फ्रेंच (कनाडा)
#XTXT Human readable text for language code "fr"
fr=फ़्रेंच
#XTXT Human readable text for language code "de"
de=जर्मन
#XTXT Human readable text for language code "el"
el=यूनानी
#XTXT Human readable text for language code "he"
he=हिब्रू
#XTXT Human readable text for language code "hi"
hi=हिन्दी
#XTXT Human readable text for language code "hu"
hu=हंगेरियाई
#XTXT Human readable text for language code "is"
is=आइसलैंड का
#XTXT Human readable text for language code "id"
id=बहासा इंडोनेशिया
#XTXT Human readable text for language code "it"
it=इटालियन
#XTXT Human readable text for language code "ja"
ja=जापानी
#XTXT Human readable text for language code "kk"
kk=कज़ाख
#XTXT Human readable text for language code "ko"
ko=कोरियन
#XTXT Human readable text for language code "lv"
lv=लातवियाई
#XTXT Human readable text for language code "lt"
lt=लिथुआनियाई
#XTXT Human readable text for language code "ms"
ms=बहासा मेलयु
#XTXT Human readable text for language code "no"
no=नॉर्वेजियाई
#XTXT Human readable text for language code "pl"
pl=पोलिश
#XTXT Human readable text for language code "pt"
pt=पुर्तगाली (ब्राज़ील)
#XTXT Human readable text for language code "pt-PT"
pt-PT=पुर्तगाली (पुर्तगाल)
#XTXT Human readable text for language code "ro"
ro=रोमानियाई
#XTXT Human readable text for language code "ru"
ru=रूसी
#XTXT Human readable text for language code "sr"
sr=सर्बियाई
#XTXT Human readable text for language code "sh"
sh=सर्बो-क्रोएशियाई
#XTXT Human readable text for language code "sk"
sk=स्लोवाक
#XTXT Human readable text for language code "sl"
sl=स्लोवेनियाई
#XTXT Human readable text for language code "es"
es=स्पैनिश
#XTXT Human readable text for language code "es-MX"
es-MX=स्पैनिश (मेक्सिको)
#XTXT Human readable text for language code "sv"
sv=स्वीडिश
#XTXT Human readable text for language code "th"
th=थाई
#XTXT Human readable text for language code "tr"
tr=तुर्की
#XTXT Human readable text for language code "uk"
uk=यूक्रेनियाई
#XTXT Human readable text for language code "vi"
vi=वियतनामी
#XTIT: Delete spaces confirmation title
deleteConfirmationHeaderText=स्पेस हटाएं
#XTXT: Logically delete space delete confirmation text with space name
logicallyDeleteConfirmationBodyTextWithSpaceName=क्या आप वाकई स्थान "{0}" को रीसायकल बिन में स्थानांतरित करना चाहते हैं?
#XTXT: Logically dpace delete confirmation text for multiple spaces
logicallyDeleteConfirmationBodyTextMultiple=क्या आप वाकई {0} चयनित स्थान को रीसायकल बिन में स्थानांतरित करना चाहते हैं?
#XTXT: Delete space delete confirmation text with space name
deleteConfirmationBodyTextWithSpaceName=क्या आप वाकई "{0}" स्थान हटाना चाहते हैं? इस कार्रवाई वापस नहीं किया जा सकता.
#XTXT: Delete space delete confirmation text for multiple spaces
deleteConfirmationBodyTextMultiple=क्या आप निश्चित तौर पर {0} चयनित स्थानों को हटाना चाहते हैं? निम्नलिखित सामग्री {1} हटाई जाएगी:
#XTXT: permanently
permanently=स्थायी रूप से
#XTXT: {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDetail=निम्न सामग्री {0} हटा दी जाएगी और पुनर्प्राप्त नहीं की जा सकती:
#XTXT: Hint Delete space confirmation text
deleteConfirmationHint=कृपया विलोपन की पुष्टि करने के लिए {0} टाइप करें.
#XTXT: Delete space confirmation error check spelling
deleteConfirmationErrorCheckSpelling=कृपया अपनी वर्तनी की जांच करें और पुन: प्रयास करें.
#XTXT: All Spaces
allSpaces=सभी स्थान
#XTXT: All data
allData=स्थान में सभी ऑब्जेक्ट और डेटा कंटेनर शामिल किया गया
#XTXT: All connections
allConnections=सभी कनेक्शन स्थान में परिभाषित किया गया
#XFLD: Space tile selection box tooltip
clickToSelect=चयन के लिए क्लिक करें
#XTXT: All database users
allDatabaseUsers=स्थान में किसी खुले SQL स्कीमा संबंद्ध में सभी ऑब्जेक्ट और डेटा शामिल किया गया.
#XFLD: remove members button tooltip
deleteUsers=उपयोगकर्ता निकालें
#XTXT: Space long description text
description=विवरण (अधिकतम 4000 वर्ण)
#XFLD: Add Members button tooltip
addUsers=उपयोगकर्ता जोड़ें
#XFLD: Add Users button tooltip
addUsersTooltip=उपयोगकर्ताओं को जोड़ें
#XFLD: Edit Users button tooltip
editUsersTooltip=उपयोगकर्ताओं को संपादित करें
#XFLD: Remove Users button tooltip
removeUsersTooltip=उपयोगकर्ता निकालें
#XFLD: Searchfield placeholder
filter=खोजें
#XCOL: Users table-view column health
health=स्वास्थ्य
#XCOL: Users table-view column access
access=पहुंच
#XFLD: No user found nodatatext
noDataText=कोई उपयोगकर्ता नहीं मिला
#XTIT: Members dialog title
selectUserDialogTitle=उपयोगकर्ता जोड़ें
#XTIT: User dialog title
addUserDialogTitle=उपयोगकर्ताओं को जोड़ें
#XTIT: Delete connections confirmation title
deleteConnectionConfirmationHeaderText=कनेक्शन हटाएं
#XTIT: Delete single connection confirmation title
deleteConnectionConfirmationHeaderTextSingle=कनेक्शन हटाएं
#XTXT: Delete connection delete confirmation text
deleteConnectionConfirmationBodyText=क्या आप वाकई चयनित कनेक्शन हटाना चाहते हैं? उन्हें स्थायी रूप से निकाल दिया जाएगा.
#XTIT: Remote source dialog title
selectRemoteConnectionDialogTitle=कनेक्शन चुनें
#XTIT: Share connection dialog title
connectionSharingDialogTitle=कनेक्शन साझा करें
#XTIT: Connections shared from other spaces table title
sharedConnectionsFromOtherSpaces=कनेक्शन साझा किया गया
#XFLD: Add remote source button tooltip
addRemoteConnections=कनेक्शन जोड़ें
#XFLD: Delete remote source button tooltip
deleteRemoteConnections=कनेक्शन निकालें
#XFLD: Share remote source button tooltip
shareConnections=कनेक्शन साझा करें
#XFLD: Tile-layout tooltip
tileLayout=टाइल लेआउट
#XFLD: Table-layout tooltip
tableLayout=तालिका लेआउट
#XMSG: Success message after creating space
createSpaceSuccessMessage=स्पेस बनाया गया
#XMSG: Success message after copying space
copySpaceSuccessMessage=स्थान "{0}" की स्थान "{1}" में प्रति बनाई जा रही है
#XMSG: Success message after deploying space
deploymentSuccessMessage=स्थान परिनियोजन प्रारंभ हो गया है
#XMSG: Success message after Apache Spark update started
sparkUpdateStartedSuccessMessage=Apache Spark अपडेट प्रारंभ हो गया है
#YMSE: Error while starting Apache Spark update
sparkUpdateStartedErrorMessage=Apache Spark अपडेट करने में विफल
#XMSG: Success message after updating space
updateSettingsSuccessMessage=स्पेस का विवरण अपडेट किया गया
#XMSG: Success message after unlocking space
unlockSpaceSuccessMessage=स्थान अस्थायी रूप से अनलॉक किया गया
#XMSG: Success message after deleting a single space
deleteSpaceSuccessMessage=स्थान हटा दिया गया
#XMSG: Success message after deleting multiple spaces
deleteSpacesSuccessMessage=स्थान हटा दिए गए
#XMSG: Success message after restoring a single space
restoreSpaceSuccessMessage=स्थान पुनर्स्थापित किया गया
#XMSG: Success message after restoring multiple spaces
restoreSpacesSuccessMessage=स्थान पुनर्स्थापित किए गए
#YMSE: Error while updating settings
updateSettingsFailureMessage=स्पेस सेटिंग को अपडेट नहीं किया जा सकता है.
#YMSE: Error while updating data lake option
dataLakeEnabledInOtherSpace=डेटा लेक पहले से ही किसी दूसरे स्पेस को असाइन किया गया है. एक बार में केवल एक ही डेटा लेक तक पहुंच प्राप्त किया जा सकता है.
#YMSE: Error while updating data lake option
virtualTablesExists=आप इस स्थान से डेटा लेक को अनसाइन नहीं कर सकते क्योंकि अभी भी वर्चुअल तालिका* पर निर्भरताएँ हैं. कृपया इस स्थान से डेटा लेक को हटाने के लिए वर्चुअल तालिकाओं को हटा दें.
#YMSE: Error while unlocking space
unlockSpaceFailureMessage=स्पेस को अनलॉक नहीं किया जा सका.
#YMSE: Error while creating space
createSpaceError=स्पेस को बनाया नहीं जा सका.
#YMSE: Error while creating duplicate space
createDuplicateSpaceError={0} नाम वाला स्पेस पहले से मौजूद है.
#YMSE: Error while deleting a single space
deleteSpaceError=स्पेस को हटाया नहीं जा सका.
#YMSE: Error details while deleting a single space
deleteSpaceDetailedError=आपका स्पेस “{0}” अब ठीक से काम नहीं कर रहा है. कृपया इसे फिर से हटाने का प्रयास करें. यदि यह अभी भी काम नहीं करता है, तो अपने व्यवस्थापक से अपना स्पेस हटाने या टिकट खोलने के लिए कहें.
#YMSE: Error while deleting a single space in files
deleteSpaceInFilesError=फ़ाइल का स्पेस डेटा हटाया नहीं जा सका.
#YMSE: Error while deleting a single space users
deleteSpaceUsersError=उपयोगकर्ताओं को निकाला नहीं जा सका.
#YMSE: Error while deleting a single space schema
deleteSpaceSchemasError=स्कीमा निकाले नहीं जा सके.
#YMSE: Error while deleting a single space connections
deleteSpaceConnectionsError=कनेक्शन निकाले नहीं जा सके.
#YMSE: Error while deleting a single space data
deleteSpaceDataError=स्पेस डेटा हटाया नहीं जा सका.
#YMSE: Error while deleting multiple spaces
deleteSpacesError=स्पेस हटाए नहीं जा सके.
#YMSE: Error while restoring a single space
restoreSpaceError=स्थान को पुनर्स्थापित नहीं किया जा सका.
#YMSE: Error while restoring multiple spaces
restoreSpacesError=स्थान पुनर्स्थापित नहीं किए जा सके.
#YMSE: Error while creating users
createUsersError=उपयोगकर्ताओं को जोड़ा नहीं जा सका.
#YMSE: Error while removing users
removeUsersError=हम उपयोगकर्ताओं को निकाल नहीं सकते.
#YMSE: Error while removing user
removeUserError=उपयोगकर्ताओं को निकाल नहीं सकते.
#YMSE: Error assigning roles to a user
roleAssignUnAssignError=हम उपयोगकर्ता को चयनित दायरे वाली भूमिका में नहीं जोड़ सके। \n\nआप अपने आप को किसी दायरे वाली भूमिका में नहीं जोड़ सकते. आप अपने व्यवस्थापक से आपको एक दायरे वाली भूमिका में जोड़ने के लिए कह सकते हैं.
#YMSE: Error assigning user to the space
userAssignError=हम उपयोगकर्ता को स्थान असाइन नहीं कर सके. \n\nउपयोगकर्ता को पहले से ही दायरे वाली भूमिकाओं में रिक्त स्थान की अधिकतम अनुमत संख्या (100) असाइन की गई है.
#YMSE: Error assigning users to the space
usersAssignError=हम उपयोगकर्ताओं को स्थान असाइन नहीं कर सके। \n\nउपयोगकर्ता को पहले से ही दायरे वाली भूमिकाओं में अधिकतम अनुमत संख्या (100) स्थान असाइन की गई है.
#YMSE: Error while fetching users
fetchUserWithScopedRolesError=हम उपयोगकर्ताओं को पुनः प्राप्त नहीं कर सकते. कृपया बाद में दुबारा से प्रयास करें.
#YMSE: Error while fetching scoped roles
fetchScopedRolesError=हम दायरा भूमिकां को पुनः प्राप्त नहीं कर सकतें.
#YMSE: Error while fetching members
fetchUserError=सदस्यों को फ़ेच नहीं किया जा सका. बाद में पुन: प्रयास करें.
#YMSE: Error while loading run-time database
loadRuntimeError=हम रन-टाइम डेटाबेस से जानकारी लोड नहीं कर सके.
#YMSE: Error while loading spaces
loadSpacesError=क्षमा करें, आपके स्पेस को पुनः प्राप्त करते समय कुछ गलत हो गया.
#YMSE: Error while loading haas resources
loadStorageError=क्षमा करें, संग्रहण डेटा को पुनः प्राप्त करते समय कुछ गलत हो गया.
#YMSE: Error no data could be loaded
loadDataError=क्षमा करें, आपके डेटा को पुनः प्राप्त करते समय कुछ गलत हो गया.
#XFLD: Click to refresh storage data
clickToRefresh=फिर से कोशिश करने के लिए यहां क्लिक करें.
#XTIT: Delete space popup title
deleteSpacePopupTitle=स्पेस हटाएं
#XCOL: Spaces table-view column name
name=नाम
#XCOL: Spaces table-view deployment status
deploymentStatus=परिनियोजन की स्थिति
#XFLD: Disk label in space details
storageLabel=डिस्क (GB)
#XFLD: In-Memory label in space details
ramLabel=मेमोरी (GB)
#XFLD: Memory label on space card
memory=स्टोरेज के लिए उपयोग की जाने वाली मेमोरी
#XFLD: Storage Assignment label in space details
storrageAssignmentLabel=स्थान संग्रहण
#XFLD: Storage Type label in space details
storageTypeLabel=संग्रहण प्रकार
#XFLD: Enable Space Quota
enableSpaceQuota=स्पेस कोटा सक्षम करें
#XFLD: No Space Quota
noSpaceQuota=कोई स्थान कोटा नहीं
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
diskAndInMemoryText=SAP HANA Database (डिस्क और इन-मेमोरी)
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
dataLakeFilesText=SAP HANA डेटा लेक फ़ाइल
#XFLD: Available scoped roles label
availableRoles=उपलब्ध दायरा भूमिकाएं
#XFLD: Selected scoped roles label
selectedRoles=चयनित दायरा भूमिकाएं
#XCOL: Spaces table-view column models
models=मॉडल
#XCOL: Spaces table-view column users
users=उपयोगकर्ता
#XCOL: Spaces table-view column connections
connections=कनेक्शन
#XFLD: Section header overview in space detail
overview=ओवरव्यू
#XFLD: Section header Apache Spark in space detail
sparkManagementSection=Apache Spark
#XFLD: Section header Apache Spark in space detail
sparkManagementSubSection=एप्लिकेशन
#XFLD: Section header Apache Spark in space detail
taskAssignmentSubSection=कार्य असाइनमेंट
#XFLD: vCPU label in Apache Spark section
vCPULabel=vCPUs 
#XFLD: Memory label in Apache Spark section
memoryLabel=मेमोरी (GB)
#XFLD: space configuration label in Apache Spark section
spaceConfigurationLabel=स्थान कॉन्फ़िगरेशन
#XFLD: Space Source label
sparkApplicationLabel=एप्लिकेशन
#XFLD: Cluster Size label
clusterSizeLabel=क्लस्टर का आकार
#XFLD: Driver label
driverLabel=ड्राइवर
#XFLD: Executor label
executorLabel=निष्पादक
#XFLD: max label
maxLabel=अधिकतम उपयोग
#XFLD: TrF Default label
trFDefaultLabel=रूपांतरण प्रवाह डिफ़ॉल्ट
#XFLD: Merge Default label
mergeDefaultLabel=डिफ़ॉल्ट को मर्ज करें
#XFLD: Optimize Default label
optimizeDefaultLabel=डिफ़ॉल्ट ऑप्टिमाइज़ करें
#XFLD: Deployment Default label
deploymentDefaultLabel=स्थानीय तालिका (फ़ाइल) परिनियोजन
#XFLD: max spark source label
driverExecutorMaxUsedSparkSource={0} CPU / {1} GB
#XFLD: Object type label
taskObjectTypeLabel=ऑब्जेक्ट प्रकार
#XFLD: Task activity label
taskActivityLabel=गतिविधि
#XFLD: Task Application ID label
taskApplicationIDLabel=डिफ़ॉल्ट एप्लिकेशन
#XFLD: Section header in space detail
generalSettings=सामान्य सेटिंग
#XMSG: Message strip message when a space is locked
spaceLockedMessageStrip=यह स्पेस वर्तमान में सिस्टम द्वारा लॉक है.
#XMSG: Message strip message deploy warning
deployWarningMessageStrip=इस सेक्शन में परिवर्तन तुरंत नियोजित किया गया.
#XMSG: Message strip message modify space configuration warning
modifySpaceConfigurationMessageStrip=कृपया ध्यान दें कि मूल्यों को बदलने से आपके सिस्टम में प्रक्रियाएं टूट सकती हैं.
#XFLD: Button text to unlock the space again
unlockSpace=स्पेस अनलॉक करें
#XFLD: Info text for audit log formatted message
auditLogText=क्रियाओं को रिकॉर्ड, पढ़ने या परिवर्तित करने के लिए ऑडिट लॉग सक्षम करें (ऑडिट नीतियां). व्यवस्थापक तब यह विश्लेषण कर सकते हैं कि किसने किस समय किस कार्रवाई को निष्पादित किया.
#XMSG: Message strip  audit warning
auditLogWarningMessageStrip=ऑडिट लॉग आपके किराएदार में बड़ी मात्रा में डिस्क संग्रहण का उपभोग कर सकते हैं. यदि आप एक ऑडिट नीति (क्रियाओं को पढ़ना या परिवर्तित करना) को सक्षम करते हैं, तो आपको पूर्ण डिस्क आउटेज से बचने के लिए नियमित रूप से डिस्क संग्रहण उपयोग (सिस्टम मॉनिटर में डिस्क संग्रहण के उपयोग किए गए कार्ड के माध्यम से) को मॉनिटर करना चाहिए, जिससे सेवा व्यवधान हो सकते हैं. यदि आप किसी ऑडिट नीति को अक्षम करते हैं, तो इसकी सभी ऑडिट लॉग प्रविष्टियां हटा दी जाएंगी. यदि आप ऑडिट लॉग प्रविष्टियां रखना चाहते हैं, तो ऑडिट नीति को अक्षम करने से पहले, उन्हें निर्यात करने पर विचार करें.
#XFLD: Message strip link text to open in app help to guide user what to do to unlock his space permanently
unlockSpaceHelp=सहायता दिखाएं
#XMSG: Message strip message when a space will be locked again
spaceTemporarilyUnlockedMessageStrip=यह स्थान अपने स्थान भंडारण से अधिक है और {0}{1} में लॉक कर दिया जाएगा.
#XMSG: Unit for remaining time until space is locked again
hours=घंटे
#XMSG: Unit for remaining time until space is locked again
minutes=मिनट
#XFLD: Section header in space detail for auditing
detailsSectionAuditing=ऑडिट
#XFLD: Subsection header in space detail for auditing
auditing=स्पेस ऑडिट करने की सेटिंग्स
#XFLD: Hot space tooltip
hotSpaceCountTooltip=जोखिम स्थान: 90% से अधिक संग्रहण का उपयोग किया गया है.
#XFLD: Green space tooltip
greenSpaceCountTooltip=स्वस्थ स्पेस: उपयोग किया गया संग्रहण 6% और 90% के बीच है.
#XFLD: Cold space tooltip
coldSpaceCountTooltip=कोल्ड स्पेस: उपयोग किया गया संग्रहण 5% या इससे कम है.
#XFLD: Critical space tooltip
criticalSpaceCountTooltip=जोखिम स्थान: 90% से अधिक संग्रहण का उपयोग किया गया है.
#XFLD: Green space tooltip
okSpaceCountTooltip=स्वस्थ स्पेस: उपयोग किया गया संग्रहण 6% और 90% के बीच है.
#XFLD: Locked space tooltip
lockedSpaceCountTooltipNew=लॉक किया गया स्थानः अपर्याप्त मेमरी के कारण ब्लॉक किया गया.
#XFLD: Locked space tooltip
lockedSpaceCountTooltip=लॉक स्पेस
#YMSE: Error while deleting remote source
deleteRemoteError=कनेक्शन निकाले नहीं जा सके.
#XFLD: Space ID input tooltip
spaceIDInfoTooltip=स्पेस ID को बाद में बदला नहीं जा सका.\nमान्य वर्ण A - Z, 0 - 9, और _ है
#YMSE: Error when space name is empty
spaceNameEmptyValidationError=स्पेस नाम दर्ज करें.
#YMSE: Error when ECN Business name is empty
ecnNameEmptyValidationError=व्यवसाय का नाम दर्ज करें.
#YMSE: Error when space id is empty
spaceIDInputEmptyValidationError=स्पेस ID दर्ज करें.
#YMSE: Error when space id contains invalid characters
spaceIDInputInValidError=अमान्य वर्ण. कृपया केवल A - Z, 0 - 9, और _ का उपयोग करें.
#YMSE: Error when space id exists while creation
spaceIDInputDuplicateError=स्पेस ID पहले से मौजूद है.
#XFLD: Space searchfield placeholder
search=खोजें
#XMSG: Success message after creating users
createUsersSuccess=उपयोगकर्ता जोड़े गए
#XMSG: Success message after creating user
createUserSuccess=उपयोगकर्ता जोड़ें गए
#XMSG: Success message after updating users
updateUsersSuccess={0} उपयोगकर्ता अपडेट किए गए
#XMSG: Success message after updating user
updateUserSuccess=उपयोगकर्ता को अपडेट किया गया
#XMSG: Success message after removing users
removeUsersSuccess={0}उपयोगकर्ताओं को निकाला गया
#XMSG: Success message after removing user
removeUserSuccess=उपयोगकर्ता निकाले गए
#XFLD: Schema name
schemaName=स्कीमा नाम
#XFLD: used of total
ofTemplate={0} का {1}
#XFLD: Assigned disk label in space overview
assignedStorageTemplate=असाइन किया हुआ डिस्क ({1} में से {0})
#XFLD: Assigned Memory label in space overview
assignedRAMTemplate=असाइन की गई मेमरी ({0} का {1})
#XFLD: Storage ratio on space
accelearationRAM=मेमोरी त्वरण
#XFLD: No Storage Consumption
noStorageConsumptionText=कोई संग्रहण कोटा असाइन नहीं किया गया है.
#XFLD: Used disk label in space overview
usedStorageTemplate=संग्रहण के लिए उपयोग की जाने वाला डिस्क ({1} में से {0})
#XFLD: Used Memory label in space overview
usedRAMTemplate=स्टोरेज के लिए उपयोग की जाने वाली मेमोरी ( {1} में से {0})
#XFLD: Used Disk tooltip
usedStorageTooltipTemplate=संग्रहण के लिए उपयोग की जाने वाला {1} में से {0} डिस्क
#XFLD: Used Memory tooltip
usedRAMTooltipTemplate={0} का {1} मेमरी का उपयोग किया गया
#XFLD: Assigned Disk tooltip
assignedStorageTooltipTemplate={1} में से {0} डिस्क असाइन किया गया
#XFLD: Assigned Memory tooltip
assignedRAMTooltipTemplate=({0} का {1}) मेमरी असाइन की गई
#XFLD: Space related used storage tooltip
spaceRelatedUsedStorageTooltipTemplate=स्पेस डेटा: {0}
#XFLD: Not space related used storage tooltip
otherDataUsedStorageTooltipTemplate=अन्य डेटा: {0}
#XFLD: Storage used exceeded popover and tooltip
storageUsedExceededText=अपनी योजना का विस्तार करने पर विचार करें, या SAP सहायता से संपर्क करें.
#XCOL: Space table-view column used Disk
usedStorage=संग्रहण के लिए उपयोग की जाने वाला डिस्क
#XCOL: Space monitor column used Memory
usedRAM=स्टोरेज के लिए उपयोग की जाने वाली मेमोरी
#XCOL: Space monitor column Schema
tableSchema=स्कीमा
#XCOL: Space monitor column Storage Type
tableStorageType=संग्रहण प्रकार
#XCOL: Space monitor column Table Type
tableType=तालिका प्रकार
#XCOL: Space monitor column Record Count
tableRecordCount=रिकॉर्ड संख्या
#XFLD: Assigned Disk
assignedStorage=संग्रहण के लिए असाइन किया गया डिस्क
#XFLD: Assigned Memory
assignedRAM=संग्रहण के लिए असाइन की गई मेमोरी 
#XCOL: Space table-view column storage utilization
tableStorageUtilization=उपयोग किया गया संग्रहण
#XFLD: space status
spaceStatus=स्पेस की स्थिति
#XFLD: space type
spaceType=स्पेस प्रकार
#XFLD: space type label for space with space type "SAP BW bridge" ("SAP BW bridge" is a product name, do not translate it)
abapbridgeSpaceType=SAP BW ब्रिज
#XFLD: space type label for space with space type "Data Marketplace"
marketplaceSpaceType=डेटा प्रजान करने वाला उत्पाद
#XMSG: Error message for when selected space cannot be deleted due to the space type
deleteTypedSpaceError=आप स्पेस {0} को हटा नहीं सकते क्योंकि इसका स्पेस प्रकार {1} है.
#XMSG: Error message for when selected spaces cannot be deleted due to the space type
deleteTypedSpacesError=आप {0} चयनित स्पेस को हटा नहीं सकते. निम्न प्रकार के स्पेस वाले स्पेस को हटाया नहीं जा सकता: {1}.
#XFLD: space type label for space without space type
defaultSpaceType=SAP Datasphere
#XFLD: Tooltip for monitor space button
monitorSpace=निरीक्षण करें
#XFLD: Tooltip for edit space button
editSpace=स्पेस संपादित करें
#XMSG: Deletion warning in messagebox
deleteConfirmation=क्या आप वाकई इस स्पेस को हटाना चाहते हैं?
#XFLD: Tooltip for delete space button
deleteSpace=स्पेस हटाएं
#XFLD: storage
storage=संग्रहण के लिए डिस्क
#XFLD: username
userName=उपयोगकर्ता नाम
#XFLD: port
port=पोर्ट
#XFLD: hostname
hostName=होस्ट का नाम
#XFLD: password
password=पासवर्ड
#XBUT: Request new password button
requestPassword=नए पासवर्ड का अनुरोध करें
#YEXP: Usage explanation in time data section
timeDataSectionHint=अपने मॉडल और स्टोरी में उपयोग करने के लिए समय सारणी और आयाम बनाएं.
#YEXP: Usage explanation in Space schema section
spaceSchemaHint=क्या आप चाहते हैं कि आपके स्पेस का डेटा अन्य टूल या ऐप द्वारा उपयोग योग्य हो? यदि ऐसा है, तो एक या एक से अधिक उपयोगकर्ता बनाएँ जो आपके स्पेस के डेटा तक पहुंच सकते हैं और यह चुन सकते हैं कि आप भविष्य के सभी डेटा को डिफ़ॉल्ट रूप से उपयोग करना चाहते हैं या नहीं.
#XTIT: Create schema popup title
createSchemaDialogTitle=ओपन SQL स्कीमा बनाएं
#XTIT: Generate Time Data dialog title
generateTimeDataDialogTitle=समय सारणी और आयाम बनाएं
#XTIT: Edit Time Data dialog title
editTimeDataDialogTitle=समय सारणी और आयाम संपादित करें
#XTIT: Time Data token title
timeDataTokenTitle=समय डेटा
#XTIT: Time Data token title
timeDataUpdateViews=समय डेटा दृश्यों को अपडेट करें
#XFLD: Time Data token creation in progress label
timeDataCreationInProgress=बनया जा रहा है...
#XFLD: Time Data token creation error label
timeDataCreationError=बनाना विफल रहा. कृपया फिर से कोशिश करें.
#XTIT: Time Data dialog time settings title
timeDataDialogTimeSettingsTitle=समय सारणी सेटिंग
#XTIT: Time Data dialog time data table title
timeDataDialogTranslationTablesTitle=अनुवाद तालिकाएं
#XTIT: Time Data dialog dimensions title
timeDataDialogTimeViewsTitle=समय आयाम
#XFLD: Time Data dialog time range label
timeRangeHint=समय श्रेणी परिभाषित करें.
#XFLD: Time Data dialog time data table label
timeDataHint=अपनी तालिका को कोई नाम दें.
#XFLD: Time Data dialog time data dimensions label
timeDimensionsHint=अपने आयामों का कोई नाम दें.
#XFLD: Time Data Time range description label
timerangeLabel=समय श्रेणी
#XFLD: Time Data dialog from year label
fromYearLabel=वर्ष से
#XFLD: Time Data dialog to year label
toYearLabel=वर्ष के लिए
#XFLD: Time Data dialog calendar type label
calendarTypeLabel=कैलेंडर प्रकार
#XFLD: Time Data dialog granularity label
granularityLabel=ग्रैन्यूलारिटी
#XFLD: Time Data dialog technical name label
technicalNameLabel=तकनीकी नाम
#XFLD: Time Data dialog quarters text table lable
quarterTextsTableLabel=तीमाही के लिए अनुवाद तालिका
#XFLD: Time Data dialog months text table lable
monthsTextsTableLabel=माह के लिए अनुवाद तालिका
#XFLD: Time Data dialog days text table lable
daysTextsTableLabel=दिनों के लिए अनुवाद तालिका
#XFLD: Time Data dialog year label
yearLabel=वर्ष आयाम
#XFLD: Time Data dialog quarter label
quarterLabel=तीमाही आयाम
#XFLD: Time Data dialog month label
monthLabel=माह आयाम
#XFLD: Time Data dialog day label
dayLabel=दिन आयाम
#XFLD: Time Data dialog gregorian calendar type label
gregorian=ग्रेगोरियन
#XFLD: Time Data dialog time granularity day label
day=दिन
#XFLD: Time Data dialog business name maxlength reached
timeRangeValueStateTextMaxLengthBusinessName=वर्णों की अधिकतम संख्या 1000 तक पहुंच गए हैं.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextMaxRangeExceeded=अधिकतम समय सीमा 150 वर्ष है.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeLower=“वर्ष से” “वर्ष तक” से कम होना चाहिए
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeHigher=“वर्ष से"1900 या उससे अधिक होना चाहिए.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeHigher=“वर्ष तक” “वर्ष से” से अधिक होना चाहिए
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeLower=“वर्ष तक” वर्तमान वर्ष से कम 100 से अधिक होना चाहिए
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningFromYear="वर्ष से" अधिक होने से डेटा हानि हो सकती है
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningToYear="वर्ष तक" कम होने से डेटा हानि हो सकती है
#XMSG: Time Data creation validation error message
timeDataValidationError=ऐसा लगता है कि कुछ फ़ील्ड अमान्य हैं. कृपया जारी रखने के लिए आवश्यक फ़ील्ड जांचें.
#XMSG: Time Data deletion popup text
deleteTimeDataConfirmation=क्या आप वाकई डेटा को हटाना चाहते हैं?
#XMSG: Time Data creation success message
createTimeDataSuccess=समय डेटा निर्मित किया गया
#XMSG: Time Data update success message
updateTimeDataSuccess=समय डेटा अपडेट किया गया
#XMSG: Time Data delete success message
deleteTimeDataSuccess=समय डेटा हटाया गया
#XMSG: Time Data creation error message
createTimeDataError=समय डेटा बनाते समय कुछ गलत हो गया.
#XMSG: Time Data update error message
updateTimeDataError=समय डेटा अपडेट करते समय कुछ गलत हो गया.
#XMSG: Time Data creation error message
deleteTimeDataError=समय डेटा हटाते समय कुछ गलत हो गया.
#XMSG: Error message when time data could not be retrieved
retrievingTimeDataError=समय डेटा लोड नहीं किया जा सका.
#XTIT: Time Data deletion popup title
deleteTimeDataConfirmationTitle=चेतावनी
#XTXT: Time Data deletion dependency popup text
deleteTimeDataDependencyText=हम आपके समय डेटा को नहीं हटा सकते क्योंकि इसका उपयोग अन्य मॉडल में किया जाता है.
#XTXT: Time Data deletion dependency popup text singular
deleteTimeDataDependencyTextSingular=हम आपके समय डेटा को नहीं हटा सकते क्योंकि इसका उपयोग अन्य मॉडल में किया जाता है.
#XMSG: Validation error when required time data form field is empty
timeDataRequiredField=इस फ़ील्ड की आवश्यकता है और इसे खाली नहीं छोड़ा जा सकता है.
#XFLD: Tooltip for Open in Database Explorer button
openInDBX=Database Explorer में खोलें
#YMSE: Dimension Year
dimensionYearView=आयाम "वर्ष"
#YMSE: Dimension Year
dimensionQuarterView=आयाम "तिमाही"
#YMSE: Dimension Year
dimensionMonthView=आयाम "मासिक”
#YMSE: Dimension Year
dimensionDayView=आयाम "दिन"
#XFLD: Time Data deletion object title
timeDataUsedIn=({0} मॉडल में उपयोग किया जाता है)
#XFLD: Time Data deletion object title singular
timeDataUsedInSingular=(1 मॉडल में उपयोग किया जाता है)
#XFLD: Time Data deletion table column provider
provider=प्रदाता
#XTIT: Time Data dependency dialog title
timeDataDependencyDialogTitle=निर्भरताएं
#XTIT: Create schema popup title
createSpaceSchemaUserDialogTitle=स्पेस स्कीमा के लिए उपयोगकर्ता बनाएं
#XFLD: Create schema button
createSchemaButton=ओपन SQL स्कीमा बनाएं
#XFLD: Generate TimeData button
generateTimeDataButton=समय सारणी और आयाम बनाएं
#XFLD: Show dependencies button
showDependenciesButton=निर्भरताएं दिखाएं
#XFLD: tooltip for disabled buttons when user is not member of space
mustBeMemberOfSpaceForOperation=इस ऑपरेशन को करने के लिए, आपका उपयोगकर्ता स्पेस का सदस्य होना चाहिए.
#XFLD: Create space schema user button
createSpaceSchemaUserButton=स्पेस स्कीमा उपयोगकर्ता बनाएं
#YMSE: API Schema users load error
loadSchemaUsersError=उपयोगकर्ताओं की सूची लोड नहीं की जा सकी.
#XTIT: API Schema-config popup title
schemaUserDetailsDialogTitle=स्पेस स्कीमा उपयोगकर्ता का विवरण
#XMSG: API Schema user deletion popup text
deleteSpaceSchemaUserConfirmation=क्या आप वाकई चयनित उपयोगकर्ता को हटाना चाहते हैं?
#XMSG: API Schema user deletion confirmation text
userDeleteSuccess=उपयोगकर्ता हटाए गए.
#YMSE: API Schema user deletion error
userDeleteError=उपयोगकर्ता हटाए नहीं जा सके.
#XFLD: User deleted
userDeleted=उपयोगकर्ता हटाया गया.
#XTIT: Remove user popup title
removeUserConfirmationTitle=चेतावनी
#XMSG: Remove user popup text
removeUserConfirmation=क्या आप वाकई उपयोगकर्ता को निकालना चाहते हैं? उपयोगकर्ता और उसकी असाइन की गई दायरा भूमिकाओं से निकाली जाएगी.
#XMSG: Remove users popup text
removeUsersConfirmation=क्या आप वाकई उपयोगकर्ताओं को निकालना चाहते हैं? उपयोगकर्ताएं और उसकी असाइन की गई भूमिकाएं स्पेस से निकाल दी जाएंगी.
#XBUT: Confirmation button for remove user dialog
removeUserConfirmationBtn=निकालें
#YMSE: No data text for available roles
noDataAvailableRoles=स्थान को किसी भी दायरे वाली भूमिका में नहीं जोड़ा जाता है. \n उपयोगकर्ताओं को स्थान में जोड़ने में सक्षम होने के लिए, इसे पहले एक या अधिक दायरे वाली भूमिकाओं में जोड़ा जाना चाहिए.
#YMSE: No data text for selected roles
noDataSelectedRoles=कोई चयनित दायरे वाली भूमिकाएं नहीं
#XTIT: Schema-config popup title
schemaDetailsDialogTitle=SQL स्कीमा कॉन्फ़िगरेशन विवरण खोलें
#XFLD: Label for Read Audit Log
auditLogRead=रीड ऑपरेशंस के लिए ऑडिट लॉग सक्षम करें
#XFLD: Label for Change Audit Log
auditLogChange=परिवर्तन ऑपरेशंस के लिए ऑडिट लॉग सक्षम करें
#XFLD: Label Audit Log Retention
auditLogRetention=लॉग रखने के दिन
#XFLD: Label Audit Log Retention Unit
retentionUnit=दिन
#YMSE: Error when log retention time is set incorrectly
incorrectRetentionTime={0} और {1} के बीच संपूर्ण संख्या दर्ज करें
#XFLD: Label for Cross Space Schema Access
spaceSchemaAccess=स्पेस स्कीमा डेटा का उपयोग करें
#XTIT: Confirm deactivation of space schema consumption dialog title
disableSpaceSchemaConsumptionDialogTitle=स्पेस स्कीमा डेटा का उपयोग करना बंद करें
#XTXT: Confirm deactivation of space schema consumption dialog text
disableSpaceSchemaConsumptionDialogText=यह ओपन SQL स्कीमा आपके स्पेस स्कीमा के डेटा का उपयोग कर सकता है. यदि आप उपयोग करना बंद कर देते हैं, तो हो सकता है कि स्पेस स्कीमा डेटा पर आधारित मॉडल अब काम नहीं करे.
#XBUT: Confirmation button for the stop consuming space schema data dialog
stopConsuming=उपयोग करना बंद करें
#XTXT: Tooltip for data lake icon
dataLakeIconTooltip=इस स्पेस का इस्तेमाल डेटा लेक को एक्सेस करने के लिए किया जाता है
#XFLD: Tooltip for data lake table entry
dataLakeTableEntryTooltip=डेटा लेक सक्षम किया गया है
#XFLD: used In-Memory linit reached tag
usedRamLimitReachedLabel=मेमोरी की सीमा समाप्त हो गई
#XFLD: Space storage Limit reached tag
storageLimitReachedLabel=संग्रहण की सीमा समाप्त हो गई
#XFLD: Minimal Storage Limit Reached tag
minStorageLimitReachedLabel=संग्रहण की न्यूनतम सीमा समाप्त हो गई
#XFLD: Space ram tag
ramLimitReachedLabel=मेमोरी की सीमा समाप्त हो गई
#XFLD: Minimal In-Memory Limit Reached tag
minRamLimitReachedLabel=मेमोरी की न्यूनतम सीमा समाप्त हो गई है
#XFLD: Popover text after click on space storage limit warning
storageLimitReachedText=आप {0} की निर्दिष्ट स्पेस संग्रहण सीमा तक पहुंच गए हैं. कृपया स्पेस के लिए अधिक संग्रहण असाइन करें.
#XFLD: System storage tag
systemStorageLimitReachedLabel=सिस्टम संग्रहण की सीमा समाप्त हो गई
#XMSG: Popover text after click on storage limit warning
systemStorageLimitReachedText=आप सिस्टम संग्रहण की {0} सीमा तक पहुँच गए हैं. अब आप स्पेस के लिए अधिक संग्रहण असाइन नहीं कर सकते.
#XMSG: Schema deletion warning
deleteSchemaConfirmation=इस ओपन SQL स्कीमा को हटाने से स्कीमा के सभी संग्रहीत ऑब्जेक्ट और बनाए रखे गए एसोसिएशन को स्थायी रूप से हटा दिया जाएगा. जारी रखे?
#XMSG: Success message after deleting a schema
schemaDeleteSuccess=स्कीमा हटाया गया
#YMSE: Error while deleting schema.
schemaDeleteError=स्कीमा हटाया नहीं जा सका.
#XMSG: Success message after update a schema
schemaUpdateSuccess=स्कीमा अपडेट किया गया
#YMSE: Error while updating schema.
schemaUpdateError=स्कीमा अपडेट नहीं किया जा सका.
#XMSG: Hint when no password is shown in the dialog.
newPasswordHint=हमने इस स्कीमा के लिए एक पासवर्ड दिया है. यदि आप अपना पासवर्ड भूल गए हैं या यह गुम हो गया है, तो आप नए पासवर्ड के लिए अनुरोध कर सकते हैं. नया पासवर्ड कॉपी करें या सहेजें.
#XMSG: Hint when showing the password in the dialog.
savePasswordHint=कृपया अपना पासवर्ड कॉपी करें. आपको इस स्कीमा से संबंध स्थापित करने के लिए इसकी आवश्यकता होगी. यदि आप अपना पासवर्ड भूल गए हैं, तो आप इसे रीसेट करने के लिए इस डायलॉग को खोल सकते हैं.
#XFLD,50: Hint on entering schema name in creation dialog
schemaNameHint=स्कीमा बनने के बाद इस नाम को बदला नहीं जा सकता.
#XFLD: Open SQL Schemas section sub headline
schemasSQL=ओपन SQL
#XFLD: Space schema section sub headline
schemasSpace=स्पेस
#XFLD: HDI Container section header
HDIContainers=HDI कंटेनर
#XTXT: Add HDI Containers button tooltip
addHDIContainers=HDI कंटेनर जोड़ें
#XTXT: Remove HDI Containers button tooltip
removeHDIContainers=HDI कंटेनर हटाएं
#XFLD: Enable HDI Containers feature button
enableHDIContainersButton=एक्सेस सक्षम करें
#YMSE: No data text for HDI Containers table
noDataHDIContainers=कोई HDI कंटेनर नहीं जोड़ा गया है.
#YMSE: No data text for Timedata section
noDataTimedata=कोई समय तालिका और आयाम निर्मित नहीं हैं.
#YMSE: No data text during runtime not available for Timedata section
runtimeNotAvailableNoDataTimedata=रन-टाइम डेटाबेस उपलब्ध नहीं होने के कारण समय सारणी और आयाम लोड नहीं किए जा सकते.
#YMSE: No data text during runtime not available for HDI section
runtimeNotAvailableNoDataHDI=HDI कंटेनर लोड नहीं किया जा सकता क्योंकि रन-टाइम डेटाबेस उपलब्ध नहीं है.
#YMSE: Error while fetching HDI Containers
fetchHDIContainersError=HDI कंटेनर प्राप्त नहीं किया जा सका. बाद में पुन: प्रयास करें.
#XFLD Table column header for HDI Container names
HDIContainerName=HDI कंटेनर का नाम
#XFLD: Enable HDI Containers feature dialog title
enableHDIContainersDialogTitle=एक्सेस सक्षम करें
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText1New=आप डेटा गतिविधि की आवश्यकता के बिना अपने HDI कंटेनर और अपने SAP Datasphere tenant स्थान के बीच डेटा का विनिमय करने के लिए अपने SAP Datasphere tenant पर SAP SQL Data Warehousin को सक्षम कर सकते हैं.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText2=ऐसा करने के लिए, नीचे दिए गए बटन पर क्लिक करके एक समर्थन टिकट खोलें.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText3New=एक बार आपका टिकट संसाधित हो जाने के बाद, आपको SAP Datasphere run-time database में एक या अधिक नए HDI कंटेनर बनाने होंगे. फिर, आपके सभी SAP Datasphere spaces के लिए HDI कंटेनर अनुभाग में एक्सेस सक्षम बटन को + बटन से बदल दिया जाता है, और आप अपने कंटेनर को स्पेस में जोड़ सकते हैं.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText41=अधिक जानकारी चाहिए? %%0 पर जाएं. टिकट में क्या शामिल करना है, इसके बारे में विस्तृत जानकारी के लिए, %%1 देखें.
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkText=SAP मदद
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkTextSapNote3057059=SAP नोट 3057059
#XBUT: Open Ticket Button Text
openTicket=टिकट खोलें
#XBUT: Add Button Text
add=जोड़ें
#XBUT: Next Button Text
next=अगला
#XBUT: Edit Button Text
editUsers=संपादित करें
#XBUT: create user Button Text
createUser=बनाएं
#XBUT: Update user Button Text
updateUser=चयन करें
#XTIT: Add HDI Containers Dialog Title
addHDIContainersDialogTitle=बिना असाइन किया गया HDI कंटेनर जोड़ें
#XFLD: No data text for add HDI container dialog
addHDIContainersDialogNoDataText=हमें कोई अप्रमाणित कंटेनर नहीं मिला. \ n आप जिस कंटेनर की तलाश कर रहे हैं, हो सकता है कि वह पहले से ही किसी स्पेस को असाइन किया गया हो.
#YMSE: Error while loading assigned HDI Containers
loadAssignedHDIContainersError=असाइन किए गए HDI कंटेनर को लोड नहीं किए जा सके.
#YMSE: Error while loading assigned HDI Containers
loadUnassignedHDIContainersError=HDI कंटेनर को लोड नहीं किए जा सके.
#XMSG: Success message
succeededToAddHDIContainer=HDI कंटेनर जोड़ दिया गया
#XMSG: Success message
succeededToAddHDIContainerPlural=HDI कंटेनर जोड़ दिए गए
#XMSG: Success message
succeededToDeleteHDIContainer=HDI कंटेनर निकाल दिया गया
#XMSG: Success message
succeededToDeleteHDIContainerPlural=HDI कंटेनर निकाल दिए गए
#XFLD: Time data section sub headline
timeDataSection=समय सारणी और आयाम
#XFLD: Schemas list entry read audit log enabled indicator
readIndicator=पठन करें
#XFLD: Schemas list entry change audit log enabled indicator
changeIndicator=बदलें
#XFLD: Remote sources section sub headline
allconnections=कनेक्शन असाइनमेंट
#XFLD: Remote sources section sub headline
localconnections=लोकल कनेक्शन
#XFLD: Remote sources section sub headline for open Connectors
openConnectors=SAP ओपन कनेक्टर
#XFLD: User section sub headline
memberassignment=सदस्य असाइनमेंट
#XFLD: User assignment section sub headline
userAssignment=उपयोगकर्ता असाइनमेंट
#XFLD: User section Access dropdown Member
member=सदस्य
#XFLD: User assignment section column name
user=उपयोगकर्ता नाम
#XTXT: Selected role count
selectedRoleToolbarText=चयनित किया गया: {0}
#XTIT: Space detail section connections title
detailsSectionConnections=कनेक्शन
#XTIT: Space detail section data access title
detailsSectionDataAccess=स्कीमा की एक्सेस
#XTIT: Space detail section time data title
detailsSectionGenerateData=समय डेटा
#XTIT: Space detail section members title
detailsSectionUsers=सदस्य
#XTIT: Space detail section Users title
detailsSectionUsersTitle=उपयोगकर्ता
#XTIT: Out of Storage
insufficientStoragePopoverTitle=संग्रहण खाली नहीं है
#XTIT: Storage distribution
storageDistributionPopoverTitle=उपयोग किया गया डिस्क संग्रहण
#XTXT: Out of Storage popover text
insufficientStorageText=कोई नया स्पेस बनाने के लिए, कृपया किसी अन्य स्पेस के असाइन संग्रहण को कम करें या ऐसा स्पेस हटाएं, जिसकी आपको कोई आवश्यकता नहीं है. आप मैनेज प्लान को कॉल करके अपने सिस्टम के कुल संग्रहण को बढ़ा सकते हैं.
#XMSG: Space id length warning
spaceIdLengthWarning={0} अधिकतम वर्णों की संख्या तक पहुंच गया है.
#XMSG: Space name length warning
spaceNameLengthWarning={0} अधिकतम वर्णों की संख्या तक पहुंच गया है.
#XMSG: Space id reserved prefix warning
spaceIdnWarningReservedPrefix=संभावित विरोध अनदेखा करने के लिए कृपया {0} उपसर्ग का उपयोग नहीं करें.
#YMSE: Error while loading open SQL schemas
loadLocalSchemasError=ओपन SQL स्कीमा लोड नहीं किए जा सके.
#YMSE: Error while creating open SQL schema
createLocalSchemaError=ओपन SQL स्कीमा बनाया नहीं जा सका.
#YMSE: Error while loading all remote connections
loadRemoteConnectionsError=सभी दूरस्थ कनेक्शन को लोड नहीं किया जा सका.
#YMSE: Error while loading space details
loadSpaceDetailsError=स्पेस विवरण लोड नहीं किया जा सका.
#YMSE: Error while deploying space details
deploySpaceDetailsError=स्पेस नियोजित नहीं किया जा सका.
#YMSE: Error while copying space details
copySpaceDetailsError=स्पेस कॉपी नहीं किया जा सका.
#YMSE: Error while loading storage data
loadStorageDataError=संग्रहण डेटा लोड नहीं किया जा सका.
#YMSE: Error while loading all users
loadAllUsersError=सभी उपयोगकर्ताओं को लोड नहीं किया जा सका.
#YMSE: Failed to reset password
resetPasswordError=पासवर्ड रिसेट नहीं किया जा सका.
#XMSG: Success message after updating space
resetPasswordSuccessMessage=स्कीमा के लिए नया पासवर्ड सेट किया गया
#YMSE: DP Agent-name too long
DBAgentNameError=DP एजेंट का नाम बहुत बड़ा है.
#YMSE: Schema-name not valid.
schemaNameError=स्कीमा का नाम अमान्य है.
#YMSE: User name not valid.
UserNameError=उपयोगकर्ता नाम अमान्य है.
#XTIT: Consumption by Storage Type
consumptionStorageTypeText=संग्रहण प्रकार द्वारा उपयोग
#XTIT: Consumption by Schema
consumptionSchemaText=स्कीमा द्वारा उपयोग
#XTIT: Overall Consumption by schema
overallConsumptionSchemaText=स्कीमा द्वारा ओवरऑल तालिका का उपयोग
#XTIT: Overall Consumption by Table Type
overallConsumptionTableTypeText=तालिका प्रकार द्वारा ओवरऑल उपयोग
#XTIT: Tables
tableDetailsText=तालिका का विवरण
#XTIT: Table Storage Consumption
tableStorageConsumptionText=तालिका संग्रहण का उपयोग
#XFLD: Table Type label
tableTypeLabel=तालिका प्रकार
#XFLD: Schema label
schemaLabel=स्कीमा
#XFLD: reset table tooltip
resetTable=तालिका रिसेट करें
#XFLD: In-Memory label in space monitor
inMemoryLabel=मेमोरी
#XFLD: Disk label in space monitor
diskLabel=डिस्क
#XFLD: Yes
yesLabel=हां
#XFLD: No
noLabel=नहीं
#XFLD: switch label: Want the data in this space to be consumable by default?
allowConsumptionByDefault=क्या आप चाहते हैं कि इस स्पेस का डेटा डिफ़ॉल्ट रूप से उपभोग योग्य हो?
#XFLD: Business Name
businessNameLabel=व्यावसायिक नाम
#XFLD: Refresh
refresh=रीफ़्रेश करें
#XMSG: No filter results title
noFilterResultsTitle=ऐसा लगता है कि आपकी फ़िल्टर सेटिंग कोई डेटा नहीं दिखा रही हैं.
#XMSG: No filter results message
noFilterResultsMsg=अपनी फ़िल्टर सेटिंग को संशोधित करने का प्रयास करें. यदि आपको अभी भी कोई डेटा दिखाई नहीं देता है, तो डेटा बिल्डर में कुछ तालिकाएं बनाएं. संग्रहण का उपभोग कर लेने के बाद, आप उन्हें यहां देख सकेंगे.
#XMSG: Space monitoring Hana state red title
spaceMonitorHanaRedTitle=रन-टाइम डेटाबेस उपलब्ध नहीं है.
#XMSG: Space monitoring Hana state red message
spaceMonitorHanaRedMsg=रन-टाइम डेटाबेस उपलब्ध नहीं होने के कारण कुछ सुविधाएं अक्षम हैं और हम इस पृष्ठ पर कोई जानकारी प्रदर्शित नहीं कर सकते हैं.
#YMSE: Error while creating space schema user
createSpaceSchemaUserError=स्थान स्कीमा उपयोगकर्ता नहीं बनाया जा सका.
#YMSE: Error User name already exists
userAlreadyExistsError=उपयोगकर्ता का नाम पहले से मौजूद है.
#YMSE: Error Authentication failed
authenticationFailedError=प्रमाणीकरण विफल हो गया.
#XMSG: Message strip message when a schema user is locked
userLockedMessageStrip=बहुत सारे लॉगिन विफल होने के कारण उपयोगकर्ता को लॉक कर दिया गया है. कृपया उपयोगकर्ता को अनलॉक करने के लिए नए पासवर्ड के लिए अनुरोध करें.
#XMSG: Success message after reset and unlock schema user
resetUnlockPasswordSuccessMessage=नया पासवर्ड सेट किया गया और उपयोगकर्ता को अनलॉक किया गया
#XMSG: user is locked message
userLockedMessage=उपयोगकर्ता लॉक है.
#XCOL: Users table-view column Role
spaceRole=भूमिका
#XCOL: Users table-view column Scoped Role
spaceScopedRole=दायरे वाली भूमिका
#XCOL: Users table-view column Space Admin
spaceAdmin=स्पेस व्यवस्थापक
#XFLD: User section dropdown value Viewer
viewer=दर्शक
#XFLD: User section dropdown value Modeler
modeler=मॉडलर
#XFLD: User section dropdown value Data Integrator
dataIntegrator=डेटा एकीकरण
#XFLD: User section dropdown value Space Administrator
spaceAdministrator=स्पेस व्यवस्थापक
#XMSG: Success message -  Space Role updated successfully.
updateUserSpaceRoleSuccess=स्थान भूमिका अपडेट की गई
#XMSG: Error message - Failed to update Space Role.
updateUserSpaceRoleError=स्पेस रोल सफलतापूर्वक अपडेट नहीं किया गया.
#XFLD:
databaseUserNameSuffix=डेटाबेस उपयोगकर्ता नाम प्रत्यय
#XTXT: Space Schema password text
spaceSchemaPasswordText=इस स्कीमा से संबंध स्थापित करने के लिए, कृपया अपना पासवर्ड कॉपी करें. यदि आप अपना पासवर्ड भूल गए हैं, तो आप हमेशा एक नया अनुरोध कर सकते हैं.
#XFLD: Cloud Platform label in space schema dialog
cloudPlatformLabel=क्लाउड प्लेटफ़ॉर्म
#XTXT: Cloud Platform text in space schema dialog
cloudPlatformText=इस उपयोगकर्ता के माध्यम से पहुंच सेट करने के लिए, उपयोग सक्षम करें और क्रेडेंशियल कॉपी करें. ऐसे मामले में आप केवल पासवर्ड के बिना क्रेडेंशियल की प्रतिलिपि बना सकते हैं, सुनिश्चित करें कि आप बाद में पासवर्ड जोड़ सकते हैं.
#XFLD: enable consumption in CloudPlatform label in space schema dialog
enableConsumptionInCloudPlatformLabel=क्लाउड प्लेटफ़ॉर्म में उपयोग सक्षम करें
#XFLD: Credentials for User-Provided Service label in space schema dialog
cloudPlatformCredentialsLabel=उपयोगकर्ता द्वारा दी गई सेवा के लिए क्रेडेंशियल:
#XFLD: Credentials for User-Provided Service (Without Password) label in space schema dialog
cloudPlatformCredentialsWithoutPasswordLabel=उपयोगकर्ता द्वारा दी गई सेवा के लिए क्रेडेंशियल (पासवर्ड के बिना):
#XFLD: Copy Credentials label in space schema dialog
copyCredentialsLabel=पासवर्ड के बिना क्रेडेंशियल कॉपी करें
#XFLD: Copy Credentials label in space schema dialog
copyFullCredentialsLabel=पूरा क्रेडेंशियल कॉपी करें
#XFLD: Copy Password label in space schema dialog
copyPasswordLabel=पासवर्ड कॉपी करें
#XMSG: Credentials copied to clipboard
credentialsCopiedMessage=क्रेडेंशियल क्लिपबोर्ड में कॉपी किए गए
#XMSG: Password copied to clipboard
passwordCopiedMessage=पासवर्ड क्लिपबोर्ड में कॉपी किए गए
#XMSG: create database user dialog title
createDatabaseUserDialogTitle=डेटाबेस उपयोगकर्ता बनाएं
#XMSG: Database Users section title
databaseUsers=डेटाबेस उपयोगकर्ता
#XMSG: Database User Details dialog title
databaseUserDetailsDialogTitle=डेटाबेस उपयोगकर्ता विवरण
#XFLD: database user read audit log
databaseUserAuditLogRead=रीड ऑपरेशन के लिए ऑडिट लॉग सक्षम करें और लॉग इसके लिए रखें
#XFLD: database user change audit log
databaseUserAuditLogChange=परिवर्तन ऑपरेशन के लिए ऑडिट लॉग सक्षम करें और लॉग इसके लिए रखें
#XMSG: Cloud Platform Access
cloudPlatformAccess=क्लाउड प्लेटफ़ॉर्म पहुंच
#XFLD: Enable Consumption in Cloud Platform hint
enableConsumptionCloudPlatformHint=इस डेटाबेस उपयोगकर्ता के माध्यम से अपने HANA परिनियोजन इंफ्रास्ट्रक्चर (HDI) कंटेनर तक पहुंच स्थापित करें. अपने HDI कंटेनर से कनेक्ट करने के लिए, SQL मॉडलिंग को चालू करना होगा
#XFLD: Enable HDI Consumption
enableConsumptionCloudPlatform=HDI उपयोग सक्षम करें
#XFLD: Enable Consumption hint
enableConsumptionHint=क्या आप चाहते हैं कि आपके स्पेस का डेटा अन्य टूल या ऐप द्वारा उपयोग करने योग्य हो?
#XFLD: Enable Consumption
enableConsumption=SQL उपयोग सक्षम करें
#XFLD: Enable Modeling
enableModeling=SQL मॉडलिंग सक्षम करें
#XMSG: Privileges for Data Modeling
privilegesModeling=डेटा लेना
#XMSG: Privileges for Data Consumption
privilegesConsumption=बाहरी टूल के लिए डेटा लेना
#XFLD: SQL Modeling
sqlModeling=SQL मॉडलिंग
#XFLD: SQL Consumption
sqlConsumption=SQL उपयोग
#XFLD: enabled
enabled=सक्षम
#XFLD: disabled
disabled=अक्षम
#XFLD: Edit Privileges
editPrivileges=विशेषाधिकार संपादित करें
#XFLD: Open Database Explorer
openDBX=Database Explorer में खोलें
#XFLD: create database user hint
databaseCreateHint=कृपया ध्यान दें कि सहेजने के बाद उपयोगकर्ता नाम फिर से बदलना संभव नहीं होगा.
#XFLD: Internal Schema Name
internalSchemaName=आंतरिक स्कीमा नाम
#YMSE: Failed to load database users
loadDatabaseUserError=डेटाबेस उपयोगकर्ता लोड करने में विफ़ल
#YMSE: Failed to delete database user
deleteDatabaseUsersError=डेटाबेस उपयोगकर्ता हटाने में विफ़ल
#XMSG: Success message after deleting database user
deleteDatabaseUserSuccess=डेटाबेस उपयोगकर्ता को हटाया गया
#XMSG: Success message after deleting database users
deleteDatabaseUsersSuccess=डेटाबेस उपयोगकर्ता हटाए गए
#XMSG: Success message after creating database user
createDatabaseUserSuccessMessage=डेटाबेस उपयोगकर्ता को बनाया गया
#YMSE: Failed to create database user
createDatabaseUserError=डेटाबेस उपयोगकर्ता बनाने में विफ़ल
#XMSG: Success message after updating database user
updateDatabaseUserSuccessMessage=डेटाबेस उपयोगकर्ता को अपडेट किया गया
#YMSE: Failed to update database user
updateDatabaseUserError=डेटाबेस उपयोगकर्ता अपडेट करने में विफ़ल
#XFLD: HDI Consumption
hdiConsumption=HDI उपयोग
#XTIT: Space detail section Database Access title
detailsSectionDatabaseAccess=डेटाबेस पहुंच
#XTXT: Database users consumption hint
databaseUsersConsumptionHint=अपने स्पेस डेटा को डिफ़ॉल्ट रूप से उपभोग करने योग्य बनाएं. बिल्डर मॉडल स्वचालित रूप से डेटा को उपभोग्य होने की अनुमति देंगे.
#XFLD: Default Consumption of Space Data:
defaultConsumptionOfSpaceData=स्पेस डेटा का डिफ़ॉल्ट उपयोग:
#XFLD: Database User Name
databaseUserName=डेटाबेस उपयोगकर्ता नाम
#XMSG: Database User creation validation error message
databaseUserValidationError=ऐसा लगता है कि कुछ फ़ील्ड अमान्य हैं. कृपया जारी रखने के लिए आवश्यक फ़ील्ड जांचें.
#XMSG: SQL modeling can’t be enabled since this user has been migrated.
migratedDatabaseUserHint=उपयोगकर्ता द्वारा माइग्रेट किए जाने के बाद से डेटा अंतर्ग्रहण को सक्षम नहीं किया जा सकता है.
#XBUT: Remove Button Text
remove=निकालें
#XBUT: Remove Spaces Button Text
removeSpaces=स्थान निकालें
#XBUT: Remove Objects Button Text
removeObjects=ऑब्जेक्ट निकालें
#XMSG: No members have been added yet.
noMembersAssigned=अभी तक कोई सदस्य नहीं जोड़े गए हैं.
#XMSG: No users have been added yet.
noUsersAssigned=अभी तक कोई उपयोगकर्ता नहीं जोड़ा गया है.
#XMSG: No database users have been created or your filter is not showing any data.
noDatabaseUsers=कोई डेटाबेस उपयोगकर्ता नहीं बनाया गया है, या आपका फ़िल्टर कोई डेटा नहीं दिखा रहा है.
#XMSG: Please enter a user name.
noDatabaseUsername=कृपया उपयोगकर्ता नाम दर्ज करें.
#XMSG: The user name is too long. Please use a shorter one.
databaseUsernameTooLong=उपयोगकर्ता नाम बहुत लंबा है. कृपया छोटे नाम का उपयोग करें.
#XMSG: No privileges have been enabled and this database user will have limited functionality. Do you still want to continue?
noPrivilegesConfirmationMessage=कोई विशेषाधिकार सक्षम नहीं किए गए हैं, और इस डेटाबेस उपयोगकर्ता की सीमित कार्यक्षमता होगी. क्या आप अभी भी जारी रखना चाहते हैं?
#XMSG: To enable audit logs for change operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogChangeMessage=परिवर्तन कार्यों के लिए ऑडिट लॉग को सक्षम करने के लिए, डेटा अंतर्ग्रहण को भी सक्षम करने की आवश्यकता है. क्या आप ऐसा करना चाहते हैं?
#XMSG: To enable audit logs for read operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogReadMessage=रीड ऑपरेशन के लिए ऑडिट लॉग को सक्षम करने के लिए, डेटा अंतर्ग्रहण को भी सक्षम करने की आवश्यकता है। क्या आप ऐसा करना चाहते हैं?
#XMSG: To enable HDI consumption data ingestion and data consumption needs to be enabled. Do you want to enable all?
enableHDIConsumptionMessage=HDI उपयोग को सक्षम करने के लिए डेटा अंतर्ग्रहण और डेटा उपयोग को सक्षम करने की आवश्यकता है. क्या आप ऐसा करना चाहते हैं?
#XMSG:
databaseUserPasswordText=इस डेटाबेस उपयोगकर्ता से संबंध स्थापित करने के लिए, कृपया अपना पासवर्ड कॉपी करें. यदि आप अपना पासवर्ड भूल जाते हैं, तो आप हमेशा एक नया अनुरोध कर सकते हैं.
#XTIT: Space detail section members title
detailsSectionMembers=सदस्य
#XMSG: New password set
newPasswordSet=नया पासवर्ड सेट
#XFLD: Data Ingestion
dataIngestion=डेटा लेना
#XFLD: Data Consumption
dataConsumption=डेटा का उपयोग
#XFLD: Privileges
privileges=विशेषाधिकार
#XFLD: Enable Data ingestion
enableDataIngestion=डेटा अंतर्ग्रहण सक्षम करें
#XMSG: Log the read and change operations for data ingestion.
databaseUserAuditHint=डेटा अंतर्ग्रहण के लिए रीड और चेंज ऑपरेशन लॉग इन करें.
#XMSG: Make your space data available in your HDI containers.
databaseUserHDIHint=अपने स्पेस डेटा को अपने HDI कंटेनर में उपलब्ध कराएं.
#XFLD: Enable Data consumption
enableDataConsumption=डेटा उपयोग सक्षम करें
#XMSG: Allow other apps or tools to consume your space data.
enableDataConsumptionHint=अन्य एप्लिकेशन या टूल को आपके स्पेस डेटा का उपभोग करने की अनुमति दें.
#XMSG: To set up access via this schema user copy the credentials to your user-provided service. In case you can only copy the credentials without a password make sure to add the password later.
databaseUserHDIConsumptionHint=इस डेटाबेस उपयोगकर्ता के माध्यम से पहुंच स्थापित करने के लिए, अपने उपयोगकर्ता द्वारा प्रदान की गई सेवा की क्रेडेंशियल की प्रतिलिपि बनाएं. ऐसे मामले में आप केवल पासवर्ड के बिना क्रेडेंशियल्स की प्रतिलिपि बना सकते हैं, सुनिश्चित करें कि आप बाद में पासवर्ड जोड़ सकते हैं.
#XFLD: Data Flow Runtime Capacity label in space overview
dataFlowRuntimeCapacity=डेटा फ़्लो रनटाइम क्षमता ({2} घंटे का {0} घंटा {1} मिनट)
#XFLD: Could not load Data Flow Runtime Capacity
dataFlowRuntimeCapacityError=डेटा फ़्लो रनटाइम क्षमता लोड नहीं किया जा सका
#XMSG: User can grant data consumption to other users.
databaseUserConsumptionWithGrantOptionHint=उपयोगकर्ता अन्य उपयोगकर्ताओं को डेटा की खपत दे सकता है.
#XFLD: Enable data consumption with grant option
enableConsumptionWithGrantOption=अनुदान विकल्प के साथ डेटा की खपत सक्षम करें
#XMSG: To enable data consumption with grant option data consumption needs to be enabled. Do you want to enable both?
enableConsumptionWithGrantMessage=डेटा खपत को अनुदान विकल्प डेटा के साथ सक्षम करने के लिए, खपत को सक्षम करने की आवश्यकता है. क्या आप दोनों को सक्षम करना चाहते हैं?
#XFLD: Enable Automated Predictive Library (APL) and Predictive Analysis Library (PAL)
allowUsingScriptServerFunctionality=स्वचालित भावी लाइब्रेरी (APL) और भावी विश्लेषण लाइब्रेरी (PAL) सक्षम करें
#XMSG: User can use SAP HANA Cloud embedded machine learning functions.
allowUsingScriptServerFunctionalityHint=उपयोगकर्ता SAP HANA क्लाउड एंबेड की गई मशीन शिक्षण फ़ंक्शन का उपयोग कर सकते हैं.
#XFLD: Password Policy
passwordPolicy=पासवर्ड नीति
#XMSG: Password Policy
passwordPolicyHint=यहां कॉन्फ़िगर पासवर्ड नीति को सक्षम या अक्षम करें.
#XFLD: Enable Password Policy
enablePasswordPolicy=पासवर्ड नीति सक्षम करें
#XMSG: Read Access to the Space Schema
readAccessTitle=स्पेस स्कीमा तक पठन पहुंच
#XMSG: read access hint
readAccessHint=डेटाबेस उपयोगकर्ता को बाहरी टूल को स्पेस स्कीमा और पठन दृश्यों से कनेक्ट करने की अनुमति दें जो खपत के लिए दिखाए जाते हैं.
#XFLD: Space Schema
spaceSchema=स्पेस स्कीमा
#XFLD: Enable Read Access (SQL)
enableReadAccess=पठन पहुंच (SQL) सक्षम करें
#XMSG: Allow the user to grant read access to other users.
withGrantOptionHint=उपयोगकर्ता को अन्य उपयोगकर्ताओं को पठन पहुंच देने की अनुमति दें.
#XFLD: With Grant Option
withGrantOption=ग्रांट विकल्प के साथ
#XMSG: Make your space data available in your HDI containers.
enableHDIConsumptionHint=अपने स्पेस डेटा को अपने HDI कंटेनर में उपलब्ध कराएं.
#XFLD: Enable HDI Consumption
enableHDIConsumption=HDI उपयोग सक्षम करें
#XMSG: Write Access to the User’s Open SQL Schema
writeAccessTitle=उपयोगकर्ता के ओपन SQL स्कीमा तक लेखन पहुंच
#XMSG: write access hint
writeAccessHint=डेटाबेस उपयोगकर्ता को बाहरी टूल को उपयोगकर्ता के ओपन SQL स्कीमा से कनेक्ट करने डेटा निकाय बनाने औऱ स्पेस में उपयोग करने के लिए डेटा इंजेस्ट करने हेतु अनुमति दें.
#XFLD: Open SQL Schema
openSQLSchema=ओपन SQL स्कीमा
#XFLD: Enable Write Access (SQL DDL & DML)
enableWriteAccess=लेखन पहुंच (SQL, DDL, & DML) सक्षम करें
#XMSG: audit hint
auditHint=ओपन SQL स्कीमा में रीड एंड चेंज ऑपरेशन को लॉग करें.
#XMSG: data consumption hint
dataConsumptionHint=खपत के लिए डिफ़ॉल्ट रूप से स्पेस में सभी नए विचारों को दिखाएं. मॉडल आउटपुट साइड पैनल में "उपभोग के लिए दिखाए गए" स्विच के माध्यम से व्यक्तिगत दृश्यों के लिए इस सेटिंग को ओवरराइड कर सकते हैं. आप उन स्वरूपों को भी चुन सकते हैं जिनमें दृश्य दिखाए जाते हैं.
#XFLD: Expose for Consumption by Default
exposeDataConsumption=डिफ़ॉल्ट रूप से खपत के लिए एक्सपोज़:
#XMSG: database users hint consumption hint
databaseUsersHint2New=बाहरी उपकरणों को SAP Datasphere से जोड़ने के लिए डेटाबेस उपयोगकर्ता बनाएं. उपयोगकर्ताओं को स्पेस डेटा पढ़ने और स्पेस में उपयोग के लिए डेटा निकाय (DDL) बनाने और डेटा (DML) बनाने की अनुमति देने के लिए विशेषाधिकार सेट करें.
#XFLD: Read
read=पठन
#XFLD: Read (HDI)
readHDI=पठन (HDI)
#XFLD: Write
write=लेखन
#XMSG: HDI Containers Hint
HDIContainersHint2=अपने स्पेस में अपने SAP HANA परिनियोजन  इंफ़्रास्टरक्चर (HDI) कंटेनर तक पहुंच सक्षम करें. मॉडलर HDI कलाकृतियों को दृश्यों के लिए स्रोत के रूप में उपयोग कर सकते हैं, और HDI क्लाइंट आपके स्पेस डेटा तक पहुंच सकते हैं.
#XMSG: Open info dialog
openDatabaseUserInfoDialog=जानकारी संवाद खोलें
#XMSG: Database user is locked. Open info dialog to unlock
openLockedDatabaseUserInfoDialog=डेटाबेस उपयोगकर्ता लॉक है. अनलॉक करने के लिए संवाद खोलें
#XFLD: Table
table=तालिका
#XFLD: Space section partner configuration title
partnerConnectionConfigurationSectionTitle=भागीदार कनेक्शन
#XFLD: Space section partner configuration subtitle
partnerConnectionConfigurationSectionSubTitle=भागीदार कनेक्शन कॉन्फ़िगरेशन
#XFLD: Space section partner configuration hint
partnerConnectionConfigurationSectionHint=अपना iFrame URL और आइकन जोड़कर अपनी खुद की भागीदार कनेक्शन टाइल परिभाषित करें. यह कॉन्फ़िगरेशन केवल इस टेनेंट के लिए उपलब्ध है.
#XFLD: Table Name Field
partnerConnectionConfigurationName=टाइल नाम
#XFLD: Table iFrame URL field
partnerConnectionConfigurationiFrameURL=iFrame URL
#XFLD: Table iFrame Post Message Origin field
partnerConnectionConfigurationiFramePostMessageOrigin=iFrame पोस्ट संदेश मूल
#XFLD: Table icon/image name
partnerConnectionConfigurationIcon=आइकन
#XMSG: Table no result found
partnerConnectionConfigurationNotfound=कोई भागीदार कनेक्शन कॉन्फ़िगरेशन नहीं मिल सका.
#XMSG: Partner Connection Confgurations are not available
partnerConnectionConfigurationNotAvailable=रनटाइम डेटाबेस अनुपलब्ध होने पर, भागीदार कनेक्शन कॉन्फ़िगरेशन प्रदर्शित नहीं किया जा सकता.
#XFLD: Dialog Create Partner Connection Configuration
partnerConnectionConfigurationDialogTitleAdd=भागीदार कनेक्शन कॉन्फ़िगरेशन बनाएं
#XFLD: Dialog Create Partner Connection Configuration Upload Icon
partnerConnectionConfigurationButtonUloadPartnerIcon=आइकन अपलोड करें
#XFLD: Dialog Create Partner Connection Configuration Select
partnerConnectionConfigurationSelectIcon=चुनें (अधिकतम आकार 200KB)
#XFLD: Dialog Create Partner Connection Configuration Enter a name
partnerConnectionConfigurationNamePlaceholder=भागीदार टाइल उदाहरण
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL
partnerConnectionConfigurationiFrameURLPlaceholder=http://www.example.com
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL Check
partnerConnectionConfigurationiFramePostMessageOriginPlaceholder=.example.com
#XFLD: Dialog Create Partner Connection Configuration Browse
partnerConnectionConfigurationBrowseButton=ब्राउज़ करें
#XFLD: Create Partner Connection Configuration Msg
partnerConnectionConfigurationUploadMsg=भागीदार कनेक्शन कॉन्फ़िगरेशन सफ़लतापूर्वक बनाया गया है.
#XMSG: Error while deleting Partner Connection configuration(s)
partnerConnectionConfigurationDeleteError=भागीदार कनेक्शन कॉन्फ़िगरेशन(कॉन्फ़िगरेशन) को हटाते समय त्रुटि हुई.
#XMSG: Partner Connection Configuration has been successfully deleted
partnerConnectionConfigurationDeleteSuccess=भागीदार कनेक्शन कॉन्फ़िगरेशन को सफलतापूर्वक हटा दिया गया है.
#XMSG: Error while retrieving Partner Connection Configurations
partnerConnectionConfigurationLoadError=भागीदार कनेक्शन कॉन्फ़िगरेशन पुनर्प्राप्त करते समय कोई त्रुटि हुई.
#XMSG: Error fileSizeLimitExceeded
partnerConnectionConfigurationfileSizeLimitExceeded=फ़ाइल अपलोड नहीं की जा सकी क्योंकि यह 200KB के अधिकतम आकार से अधिक है.
#XBUT: Create Partner Connection Configuration
partnerConnectionConfigurationButtonCreate=भागीदार कनेक्शन कॉन्फ़िगरेशन बनाएं
#XBUT: Delete Partner Connection Configuration
partnerConnectionConfigurationButtonDelete=भागीदार कनेक्शन कॉन्फ़िगरेशन हटाएं.
#XMSG: Partner Tile Creation Failure
upsertPartnerTileFailed=भागीदार टाइल नहीं बनाई जा सकी.
#XMSG: Partner Tile Deletion Failure
deletePartnerTileFailed=भागीदार टाइल को हटाया नहीं जा सका.
#XMSG: HANA cloud connector settings reset failure
resetHANASCCSettingsFailed=ग्राहक SAP HANA क्लाउड कनेक्टर सेटिंग्स को रीसेट करना विफल रहा
#XFLD: Workload Class
workloadClass=कार्यभार वर्ग
#XFLD: Workload Management
workloadManagement=कार्यलोड प्रबंधन
#XFLD: Priority
workloadClassPriority=वरीयता
#XMSG:
workloadManagementPriorityHint=डेटाबेस को क्वेरी करते समय आप इस स्थान की प्राथमिकता निर्दिष्ट कर सकते हैं. 1 (निम्नतम प्राथमिकता) से 8 (उच्चतम प्राथमिकता) तक का मान दर्ज करें. ऐसी स्थिति में जहां रिक्त स्थान उपलब्ध थ्रेड्स के लिए प्रतिस्पर्धा कर रहे हैं, उच्च प्राथमिकता वाले लोगों को निम्न प्राथमिकताओं वाले रिक्त स्थान से पहले रन किया जाता है.
#XMSG:
workloadClassPriorityHint=आप स्पेस की प्राथमिकता 0 (निम्नतम) से 8 (उच्चतम) तक निर्दिष्ट कर सकते हैं. उच्च प्राथमिकता वाले स्थान के विवरणों को कम प्राथमिकता वाले अन्य स्थानों के विवरणों से पहले निष्पादित किया जाता है. डिफ़ॉल्ट प्राथमिकता 5 है. चूंकि मान 9 सिस्टम संचालन के लिए आरक्षित है, इसलिए यह स्पेस के लिए उपलब्ध नहीं है.
#XFLD: Statement Limits
workloadclassStatementLimits=विवरण सीमा
#XFLD: Workload Configuration
workloadConfiguration=कार्यलोड कॉन्फ़िगरेशन
#XMSG:
workloadClassStatementLimitsHint=आप थ्रेड और GB मेमोरी की अधिकतम संख्या (या प्रतिशत) बता सकते हैं जो स्पेस में एक साथ चलने वाले स्टेटमेंट उपभोग कर सकते हैं. आप 0 (कोई सीमा नहीं) और टैनेंट में उपलब्ध कुल मेमोरी और थ्रेड के बीच कोई भी मान या प्रतिशत डाल सकते हैं. \n\n अगर आप थ्रेड सीमा बताते हैं, तो ध्यान रखें कि यह प्रदर्शन को कम कर सकता है. \n\n अगर आप मेमोरी सीमा बताते हैं, तो मेमोरी सीमा तक पहुंचने वाले स्टेटमेंट रन नहीं किए जाते हैं.
#XMSG:
workloadClassStatementLimitsDescription=डिफ़ॉल्ट कॉन्फ़िगरेशन उदार संसाधन सीमा प्रदान करता है, जबकि किसी एकल स्थान को सिस्टम को ओवरलोड करने से रोकता है.
#XMSG:
workloadClassStatementLimitCustomDescription=आप अधिकतम कुल थ्रेड और मेमरी सीमा निर्धारित कर सकते हैं जो स्थान में समवर्ती रूप से रन कर रहे हैं, विवरणों का उपभोग कर सकते हैं.
#XMSG:
totalStatementThreadLimitHelpText=थ्रेड सीमा को बहुत कम सेट करना विवरण निष्पादन को प्रभावित कर सकता है, जबकि अत्यधिक उच्च मान या 0 स्पेस को सभी उपलब्ध सिस्टम थ्रेड्स का उपभोग करने की अनुमति दे सकता है.
#XMSG:
totalStatementMemoryLimitHelpText=मेमोरी सीमा को बहुत कम सेट करने से आउट-ऑफ-मेमोरी समस्याएं हो सकती हैं, जबकि अत्यधिक उच्च मान स्पेस को सभी उपलब्ध सिस्टम मेमोरी का उपभोग करने की अनुमति दे सकते हैं.
#XMSG:
totalStatementThreadLimitHelpTextRestricted=आपके टैनेंट में उपलब्ध थ्रेड्स की कुल संख्या का 1% और 70% (या समतुल्य संख्या) के बीच का प्रतिशत दर्ज करें. थ्रेड सीमा को बहुत कम सेट करने से विवरण का निष्पादन प्रभावित हो सकता है, जबकि अत्यधिक उच्च मान अन्य स्पेस में विवरण के निष्पादन को प्रभावित कर सकते हैं.
#XMSG:
totalStatementThreadLimitHelpTextDynamic=आपके टैनेंट में उपलब्ध थ्रेड्स की कुल संख्या का 1% और {0}% (या समतुल्य संख्या) के बीच का प्रतिशत दर्ज करें. थ्रेड सीमा को बहुत कम सेट करने से विवरण का निष्पादन प्रभावित हो सकता है, जबकि अत्यधिक उच्च मान अन्य स्पेस में विवरण के निष्पादन को प्रभावित कर सकते हैं.
#XMSG:
totalStatementMemoryLimitHelpTextNew=0 (कोई सीमा नहीं) और आपके टैनेंट में उपलब्ध मेमोरी की कुल मात्रा के बीच कोई मान या प्रतिशत दर्ज करें। स्मृति सीमा को बहुत कम सेट करने से कथन प्रदर्शन प्रभावित हो सकता है, जबकि अत्यधिक उच्च मान अन्य स्थानों में कथन के प्रदर्शन को प्रभावित कर सकते हैं.
#XFLD: Total Statement Thread Limit
totalStatementThreadLimit=कुल विवरण मेमोरी सीमा
#XFLD: Segmented button label
workloadclassPercentage=%
#XFLD: Segmented button label
workloadclassGigaByte=GB
#XFLD, 80: Segmented button label
workloadclassThreads=थ्रेड
#XFLD: Total Statement Memory Limit
totalStatementMemoryLimit=कुल विवरण मेमोरी सीमा
#YMSE: Error while loading customer hana info
loadCustomerHanaInfoError=ग्राहक SAP HANA जानकारी लोड करने में विफल.
#XMSG:
minimumLimitReached=न्यूनतम सीमा तक पहुंच गया.
#XMSG:
maximumLimitReached=अधिकतम सीमा तक पहुंच गया.
#XMSG: Name Taken for Technical Name
technical-name-taken=आपके द्वारा दर्ज किए गए तकनीकी नाम के साथ एक कनेक्शन पहले से मौजूद है. कृपया कोई अन्य नाम दर्ज करें.
#XMSG: Name Too long for Technical Name
technical-name-too-long=आपके द्वारा दर्ज किया गया तकनीकी नाम 40 वर्णों से अधिक है. कृपया कम वर्णों वाला नाम दर्ज करें.
#XMSG: Technical name field empty
technical-name-field-empty=कृपया कोई तकनीकी नाम दर्ज करें.
#XMSG: Generic error when user enters invalid characters for a field
field-invalid-characters=आप नाम के लिए केवल अक्षर (a-z), अंक (0-9), और अंडरस्कोर (_) का उपयोग कर सकते हैं.
#XMSG: Generic error when user enters invalid initial characters for a field
field-underscore-trim=आपके द्वारा दर्ज किया गया नाम अंडरस्कोर (_) से शुरू या समाप्त नहीं हो सकता है.
#XFLD: Checkbox label for Enable Statement Limits
enableStatementLimits=विवरण सीमाएं सक्षम करें
#XFLD: Connection SubSection Settings Title in space details
connectionSettings=सेटिंग
#XMSG: Connections tool hint in Space details section
connectionsToolHint=कनेक्शन बनाने या संपादित करने के लिए साइड नेविगेशन से कनेक्शन ऐप खोलें या यहां क्लिक करें:
#XBUT: Connection list Open Connection Tool Button
button-connectiontool=कनेक्शन पर जाएं
#XFLD: Not deployed label on space tile
notDeployedLabel=स्थान अभी तक परिनियोजित नहीं किया गया.
#XFLD: Not deployed additional text on space tile
notDeployedText=कृपया स्थान परिनियोजित करें.
#XFLD: Corrupt space label on space tile
corruptSpace=कुछ गलत हो गया.
#XFLD: Corrupt space additional text on space tile
corruptSpaceHint=पुन: परिनियोजित करने का प्रयास करें या समर्थन से संपर्क करें
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=ऑडिट लॉग डेटा
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=व्यवस्थापकीय डेटा
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=दूसरा डेटा
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=स्थान में डेटा
#XMSG: Unlock Space confirmation text
unlockSpaceConfirmation=क्या आप वास्तव में स्पेस को अनलॉक करना चाहते हैं?
#XMSG: Lock Space confirmation text
lockSpaceConfirmation=क्या आप वास्तव में स्पेस को लॉक करना चाहते हैं?
#XFLD: Lock
lock=लॉक करें
#XFLD: Unlock
unlock=अनलॉक करें
#XFLD: Locking
locking=लॉक करें
#XMSG: Success message after locking space
lockSpaceSuccessMsg=स्पेस लॉक किया गया
#XMSG: Success message after unlocking space
unlockSpaceSuccessMsg=स्पेस लॉक किया गया
#XMSG: Success message after locking spaces
lockSpacesSuccessMsg=स्पेस लॉक किया गया
#XMSG: Success message after unlocking spaces
unlockSpacesSuccessMsg=स्पेस अनलॉक किया गया
#YMSE: Error while locking a space
lockSpaceError=स्पेस लॉक नहीं किया जा सकता.
#YMSE: Error while unlocking a space
unlockSpaceError=स्पेस अनलॉक नहीं किया जा सकता.
#XTIT: popup title Warning
confirmationWarningTitle=चेतावनी
#XMSG: Message strip message when a space was locked manually
manualLockedMessage=स्पेस मैन्युअल रूप से लॉक किया गया है.
#XMSG: Message strip message when a space was locked due to the amount of generated audit logs
auditLogLockedMessage=सिस्टम द्वारा स्थान को लॉक कर दिया गया है क्योंकि ऑडिट लॉग में बड़ी मात्रा में GB डिस्क की उपभोग की जाती है.
#XMSG: Message strip message when a space was locked because the assigned quota was exceeded.
quotaLockedMessage=स्पेस सिस्टम द्वारा लॉक किया गया है क्योंकि यह यह मेमोरी या डिस्क स्टोरेज के आवंटन से अधिक है.
#XMSG: Unlock Spaces confirmation text
unlockSpacesConfirmation=क्या आप वास्तव में चयनित स्थानों को अनलॉक करना चाहते हैं?
#XMSG: Lock Spaces confirmation text
lockSpacesConfirmation=क्या आप वास्तव में चयनित स्थानों को लॉक करना चाहते हैं?
#XTIT: Scoped role editor dialog title
scopedRoleEditorDialogTitle=दायरे वाली भूमिका संपादक
#XTIT: ECN Management title
ecnManagementTitle=स्थान और इलास्टिक कंप्यूट नोड प्रबंधन
#XFLD: ECNs
ecns=इलास्टिक कंप्यूट नोड
#XFLD: ECN phase Ready
ecnReady=तैयार
#XFLD: ECN phase Running
ecnRunning=रन हो रहा है
#XFLD: ECN phase Initial
ecnInitial=तैयार नहीं
#XFLD: ECN phase Starting
ecnStarting=प्रारंभ कर रहा है
#XFLD: ECN phase Starting Failed
ecnStartingFailed=प्रारंभिक विफल हुआ
#XFLD: ECN phase Stopping
ecnStopping=रोका जा रहा है
#XFLD: ECN phase Stopping Failed
ecnStoppingFailed=रोकना विफल हुआ
#XBTN: Assign Button
assign=स्पेस असाइन करें
#XBTN: Start Header-Button
start=प्रारंभ करें
#XBTN: Update Header-Button
repair=अपडेट करें
#XBTN: Stop Header-Button
stop=रुकें
#XFLD: ECN hours remaining
ecnHoursRemaining=शेष 1000 घंटे
#XFLD: ECN block-hours remaining
ecnBlockHoursRemaining={0} ब्लॉक-घंटे शेष
#XFLD: ECN block-hour remaining
ecnBlockHourRemaining={0} ब्लॉक-घंटे शेष
#XTIT: ECN creation dialog title
ecnCreationDialogTitle=इलास्टिक कम्प्यूट नोड बनाएं
#XTIT: ECN edit dialog title
ecnEditDialogTitle=इलास्टिक कम्प्यूट नोड संपादित करें
#XTIT: ECN deletion dialog title
ecnDeletionDialogTitle=इलास्टिक कम्प्यूट नोड हटाएं
#XTIT: ECN Space assignment dialog title
ecnSpaceAssignmentDialogTitle=स्पेस असाइन करें
#XFLD: ECN ID
ECNIDLabel=मूल्य-सापेक्षता कम्प्यूट नोड
#XTXT: Selected toolbar text
selectedToolbarText=चयनित किया गया: {0}
#XTIT: Elastic Compute Nodes
ECNslong=इलास्टिक कंप्यूट नोड
#XTIT: Object assignment - Table header: Objects available
objectsAvailable=ऑब्जेक्ट की संख्या
#XTIT: Object assignment - Dialog header text
selectObjects=स्थान और ऑब्जेक्ट का चयन करें आप अपने इलास्टिक कंप्यूट नोड के लिए असाइन करेंः
#XTIT: Object assignment - Table header title: Objects
objects=ऑब्जेक्ट
#XTIT: Object assignment - Table header: Type
type=प्रकार
#XMSG: Message strip delete database user audit warning
deleteDatabaseUserWarningMessageStrip=सावधान रहें कि डेटाबेस उपयोगकर्ता को हटाने से सभी उत्पन्न ऑडिट लॉग प्रविष्टियां हट जाएंगी. यदि आप ऑडिट लॉग रखना चाहते हैं, तो डेटाबेस उपयोगकर्ता को हटाने से पहले उन्हें निर्यात करने पर विचार करें.
#XMSG: Message strip delete HDI container audit logs impact
deleteHDIContainerWarningMessageStrip=सावधान रहें कि स्पेस से एक HDI कंटेनर को अनअसाइन करने से सभी जनरेट किए गए ऑडिट लॉग प्रविष्टियां हटा दी जाएंगी. अगर आप ऑडिट लॉग रखना चाहते हैं, तो HDI कंटेनर को अनअसाइन करने से पहले उन्हें निर्यात करने पर विचार करें.
#XTXT: All audit logs
allAuditLogs=स्थान हेतु सभी ऑडिट लॉग प्रविष्टियों को जनरेट किया गया
#XMSG: Message strip disable audit warning
disableAuditWarningMessageStrip=सावधान रहें कि ऑडिट नीति को अक्षम करने (पठन या संचालन को बदलने) के परिणामस्वरूप इसकी सभी ऑडिट लॉग प्रविष्टियां हटाई जाएंगी. यदि आप ऑडिट लॉग प्रविष्टियां रखना चाहते हैं, तो ऑडिट नीति को अक्षम करने से पहले उन्हें निर्यात करने पर विचार करें.
#XTIT: No Spaces assigned to ECN Illustration title
noSpacesECNTitle=अभी तक कोई स्पेस या ऑब्जेक्ट असाइन नहीं किया गया है
#XTIT: No Spaces assigned to ECN Illustration description
noSpacesECNDescription=अपने इलास्टिक कंप्यूट नोड के साथ काम करना प्रारंभ करने के लिए, इसे कोई स्थान या ऑब्जेक्ट असाइन करें.
#XTIT: No Spaces Illustration title
noSpacesTitle=अभी तक कोई स्थान नहीं बनाया गया है
#XTIT: No Spaces Illustration description
noSpacesDescription=डेटा प्राप्त करना प्रारंभ करने के लिए, एक स्थान बनाएं.
#XTIT: No Spaces Illustration title
noDeletedSpacesTitle=रीसायकल बिन खाली है
#XTIT: No Spaces Illustration description
noDeletedSpacesDescription=आप यहां से अपने हटाए गए रिक्त स्थान पुनर्प्राप्त कर सकते हैं.
#XTXT:  {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDatabaseUsers=एक बार स्थान नियोजित हो जाने के बाद, निम्न डेटाबेस उपयोगकर्ता {0} हटा दिए जाएंगे और उन्हें पुनर्प्राप्त नहीं किया जा सकता है:
#XTIT: Delete database users
deleteDatabaseUsersTitle=डेटाबेस उपयोगकर्ता हटाएं
#YMSE: Error if ecn id already exists
ecnIDInputDuplicateError=ID पहले से मौजूद है.
#YMSE: Error if ecn id contains invalid characters
ecnIDInputInvalidError=कृपया केवल निम्न केस वर्ण a - z और संख्याओं 0 - 9 का उपयोग करें.
#YMSE: Error if ecn id is too short
ecnIDInputTooShortError=ID कम से कम {0} वर्ण लंबा होना चाहिए.
#XMSG: ecn id length warning
ecnIdLengthWarning={0} अधिकतम वर्णों की संख्या तक पहुंच गया है.
#XFLD: open System Monitor
systemMonitor=सिस्टम मॉनिटरिंग
#XFLD: open ECN schedule dialog menu entry
schedule=शेड्यूल
#XFLD: open create ECN schedule dialog
createSchedule=शेड्यूल बनाएं
#XFLD: open change ECN schedule dialog
changeSchedule=शेड्यूल संपादित करें
#XFLD: open delete ECN schedule dialog
deleteSchedule=शेड्यूल हटाएं
#XFLD: open self assign ECN schedule dialog
selfAssignSchedule=मुझे शेड्यूल असाइन करें
#XFLD: open pause ECN schedule dialog
pauseSchedule=शेड्यूल रोकें
#XFLD: open resume ECN schedule dialog
resumeSchedule=शेड्यूल फिर से शुरू करें
#XFLD: View Logs
viewLogs=लॉग देखें
#XFLD: Compute Blocks
computeBlocks=कंप्यूट ब्लॉक
#XFLD: Memory label in ECN creation dialog
ecnMemory=स्मृति (GB)
#XFLD: Storage label in ECN creation dialog
ecnStorage=संग्रहण (GB)
#XFLD: Number of CPU label in ECN creation dialog
ecnCPUCount=CPU की संख्या
#XFLD: ECN updated by label
changedBy=परिवर्तनकर्ता
#XFLD: ECN updated on label
changedOn=इस दिन परिवर्तन हुआ
#XMSG: Success message after creating a Elastic Compute Node
createEcnSuccessMessage=इलास्टिक कंप्यूट नोड निर्मित किया गया
#YMSE: Error while creating a Elastic Compute Node
createEcnError=इलास्टिक कंप्यूट नोड निर्मित नहीं हो सकता
#XMSG: Success message after updating a Elastic Compute Node
updateEcnSuccessMessage=इलास्टिक कंप्यूट नोड अपडेट किया गया
#XMSG: Error while updating a Elastic Compute Node
udpateEcnError=इलास्टिक कंप्यूट नोड अपडेट नहीं हो सकता
#XMSG: Success message after deleting a Elastic Compute Node
deleteEcnSuccessMessage=इलास्टिक कंप्यूट नोड हटाया गया
#YMSE: Error while deleting a Elastic Compute Node
deleteEcnError=इलास्टिक कंप्यूट नोड हटाया नहीं जा सकता
#XMSG: Success message after starting a Elastic Compute Node
startingEcnSuccessMessage=इलास्टिक कंप्यूट नोड प्रारंभ करना
#XMSG: Success message after stopping a Elastic Compute Node
stoppingEcnSuccessMessage=इलास्टिक कम्प्यूट नोड रोकना
#YMSE: Error while starting a Elastic Compute Node
startingEcnError=इलास्टिक कंप्यूट नोड प्रारंभ नहीं हो सकता
#YMSE: Error while stopping a Elastic Compute Node
stoppingEcnError=इलास्टिक कंप्यूट नोड रोका नहीं जा सकता
#XBUT: Add Object button for an ECN
assignObjects=ऑब्जेक्ट जोड़ें
#XFLD: Enable auto object assignment label
enableAutoObjAssignment=सभी ऑब्जेक्ट स्वचालित रूप से जोड़ें
#XFLD: object type label to be assigned
objectTypeLabel=प्रकार (सिमेंटिक उपयोग)
#XFLD: assigned object type label
assignedObjectTypeLabel=प्रकार
#XFLD: technical name label
TechnicalNameLabel=तकनीकी नाम
#XFLD: Object selecttion ECN label
objectSelectionECNLabel=उन ऑब्जेक्ट का चयन करें जिन्हें आप इलास्टिक कंप्यूट नोड में जोड़ना चाहते हैं
#XTIT: Add objects dialog title
assignObjectsTitle=के ऑब्जेक्ट असाइन करें
#XFLD: object label with object count
objectLabel=ऑब्जेक्ट
#XMSG: No objects available to add message.
noObjectsToAssign=असाइन करने के लिए कोई ऑब्जेक्ट उपलब्ध नहीं है.
#XMSG: No objects assigned message.
noAssignedObjects=कोई ऑब्जेक्ट असाइन नहीं किया गया.
#XTIT: Remove objects popup title
removeObjectsOrSpacesConfirmationTitle=चेतावनी
#XTIT: Delete ECN popup button and title
deleteECNConfirmationButtonAndTitle=हटाएं
#XMSG: Remove objects popup text
removeObjectsConfirmation=क्या आप चयनित ऑब्जेक्ट हटाना चाहते हैं?
#XMSG: Remove spaces popup text
removeSpacesConfirmation=क्या आप वास्तव में चयनित स्थानों निकालना चाहते हैं?
#XBUT: Confirmation button for objects dialog
removeObjectsOrSpacesConfirmationBtn=स्थान निकालें
#XMSG: Success message after removing objects
removeObjectsSuccessMessage=एक्सपोज़ किए गए ऑब्जेक्ट निकाले गए हैं
#XMSG: Success message after assigning objects
assignObjectsSuccessMessage=एक्सपोज़ किए गए ऑब्जेक्ट असाइन किए गए है.
#XFLD: auto object assignment label for space tile
autoObjAssignmentLabel=सभी एक्सपोज़ ऑब्जेक्ट
#XFLD: Spaces tab label
spacesTabLabel=स्पेस
#XFLD: Exposed objects tab label
consumptionObjectsTabLabel=एक्सपोज़ हुए ऑब्जेक्ट
#XMSG: Success message after unassigning spaces
spacesUnassignedSuccessMessage=स्थान निकाले गए हैं
#XMSG: Success message after unassigning space
spaceUnassignedSuccessMessage=स्थान निकाला गया है
#YMSE: Error while assigning / unassigning spaces
spacesAssignUnassignError=स्थान असाइन या निकाले नहीं जा सके.
#YMSE: Error while removing objects
removeObjectsError=हम ऑब्जेक्ट असाइन नहीं कर सके या हटा नहीं सके.
#YMSE: Error while removing object
removeObjectError=हम ऑब्जेक्ट असाइन नहीं कर सके या निकाल नहीं सके.
#YMSE: Error when saving the ecn with invalid value
invalidSizingPlanError=पहले चयनित संख्या अब मान्य नहीं है. कृपया  मान्य संख्या का चयन करें.
#YMSE: Error when saving the ecn with invalid value
invalidPerformanceClassError=कृपया एक मान्य प्रदर्शन वर्ग चयन करें.
#YMSE: Error when saving the ecn with invalid value
performanceClassNotAvailableError=पहले चयनित प्रदर्शन वर्ग "{0}" वर्तमान में मान्य नहीं है. कृपया मान्य प्रदर्शन वर्ग का चयन करें.
#XTXT: Delete ECN confirmation text
deleteConfirmationTxtECN=क्या आप वास्तव में इलास्टिक कंप्यूट नोड को हटाना चाहते हैं?
#XFLD: tooltip for ? button
help=सहायता
#XFLD: ECN edit button label
editECN=कॉन्फ़िगर करें
#XFLD: Technical type label for ERModel
DWC_ERMODEL=निकाय - संबंध मॉडल
#XFLD: Technical type label for Local Table
DWC_LOCAL_TABLE=लोकल तालिका
#XFLD: Technical type label for Remote Table
DWC_REMOTE_TABLE=रीमोट तालिका
#XFLD: Technical type label for Analytic Model
DWC_ANALYTIC_MODEL=विश्लेषिकी मॉडल
#XFLD: Technical type label for Task Chain
DWC_TASKCHAIN=कार्य श्रृंखला
#XFLD: Technical type label for Data Flow
DWC_DATAFLOW=डेटा प्रवाह
#XFLD: Technical type label for Replication Flow
DWC_REPLICATIONFLOW=प्रतिकृति प्रवाह
#XFLD: Technical type label for Transformation Flow
DWC_TRANSFORMATIONFLOW=रूपांतरण प्रवाह
#XFLD: Technical type label for Intelligent Lookup
DWC_IDT=इंटेलिजेंट लुकअप
#XFLD: Technical type label for Repository
DWC_REPOSITORY_PACKAGE=संग्राहक
#XFLD: Technical type label for Enterprise Search
DWC_ESMODEL=एंटरप्राइज़ खोज
#XFLD: Technical type label for View
DWC_VIEW=दृश्य
#XFLD: Technical type label for Data Product
DWC_DATAPRODUCT=डेटा उत्पाद
#XFLD: Technical type label for Data Access Control
DWC_DAC=डेटा पहुंच नियंत्रण
#XFLD: Technical type label for Folder
DWC_FOLDER=फ़ोल्डर
#XFLD: Technical type label for Data Entity
DWC_BUSINESS_ENTITY=व्यवसाय निकाय
#XFLD: Technical type label for Business Entity Variant
DWC_BUSINESS_ENTITY_VARIANT=व्यवसाय निकाय भिन्न रूप
#XFLD: Technical type label for Responsibility Scenario
DWC_AUTH_SCENARIO=ज़िम्मेदारी परिदृश्य
#XFLD: Technical type label for Fact Model
DWC_FACT_MODEL=तथ्य मॉडल
#XFLD: Technical type label for Transformation Flow
DWC_PERSPECTIVE=परिप्रेक्ष्य
#XFLD: Technical type label for Consumption Model
DWC_CONSUMPTION_MODEL=उपभोग मॉडल
#XFLD: Technical type label for Remote Connection
DWC_REMOTECONNECTION=दूरस्थ कनेक्शन
#XFLD: Technical type label for Fact Model Variant
DWC_FACT_MODEL_VARIANT=तथ्य मॉडल भिन्न रूप
#XMSG: Schedule created alert message
createScheduleSuccess=शेड्यूल बनाए गए
#XMSG: Schedule updated alert message
updateScheduleSuccess=शेड्यूल अपडेट किए गए
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=शेड्यूल हटाया गया
#XMSG: Schedule takeover success message
ownerChangeScheduleSuccess=शेड्यूल आपको असाइन किया गया
#XMSG: Message for starting pausing of schedule
pauseScheduleStarted=1 शेड्यूल को रोकना
#XMSG: Message for starting resuming of schedule
resumeScheduleStarted=1  शेड्यूल को फिर से शुरू करना
#XFLD: Segmented button label
availableSpacesButton=उपलब्ध
#XFLD: Segmented button label
selectedSpacesButton=चयनित किया गया
#XFLD: Visit website button text
visitWebsite=वेबसाइट पर जाएं
#XMSG: Warning message on unselecting source language
sourceLanguageRemovalWarning=पहले से चयनित स्रोत भाषा को निकाला जाएगा.
#XBUT: enable translation confirmation button
enableTranslationConfirmationBtn=सक्षम करें
#XFLD: ECN performance class label
performanceClassLabel=प्रदर्शन वर्ग
#XTXT performance class memory text
memoryText=मेमोरी
#XTXT performance class compute text
computeText=कंप्यूट
#XTXT performance class high-compute text
highComputeText=उच्च-कंप्यूट
#XBUT: Recycle Bin Button Text
recycleBin=रीसायकल बिन
#XBUT: Restore Button Text
restore=पुनर्स्थापित करें
#XMSG: Warning message for new Workload Management UI
priorityWarning=यह क्षेत्र केवल-पठन योग्य है. आप सिस्टम / कॉन्फ़िगरेशन / कार्यभार प्रबंधन क्षेत्र में स्थान वरीयता परिवर्तित कर सकते हैं.
#XMSG: Warning message for new Workload Management UI
workloadWarning=यह क्षेत्र केवल-पठन योग्य है. आप सिस्टम / कॉन्फ़िगरेशन / कार्यभार प्रबंधन क्षेत्र में स्थान कार्यभार कॉन्फ़िगरेशन परिवर्तित कर सकते हैं.
#XCOL: Space table-view column: Apache Spark vCPUs
sparkManagementVCPUs=Apache Spark vCPUs
#XCOL: Space table-view column: Apache Spark Memory (GB)
sparkManagementMemory=Apache Spark vCPUs (GB)
#XCOL: Space type (from Space Capability) for Data Ingestion Spaces
ingestionSpaceType=डेटा लेना
#XFLD: Deployment in progress nodatatext for spark table
sparkTableDeploymentInProgress=कोई डेटा उपलब्ध नहीं है क्योंकि स्थान वर्तमान में परिनियोजित किया जा रहा है
#XFLD: Runtime data being loaded nodatatext for spark table
sparkTableDeploymentLoading=कोई डेटा उपलब्ध नहीं है क्योंकि स्थान वर्तमान में लोड किया जा रहा है
#XCOL: Space type (from Space Capability) for BW Private Cloud Edition
BWPCESpaceType=SAP BW
#XBUT: Go to Instance Mappings Button
navToMappingsButton=आवृत्ति मैपिंग संपादित करें
#XFLD: SAP HANA Data Lake Storage label if less than 1GB
dataLakeStorageUsed=< 1 GB
