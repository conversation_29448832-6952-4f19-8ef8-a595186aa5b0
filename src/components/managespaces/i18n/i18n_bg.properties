#DO NOT REMOVE________________________
#XFLD: Breadcrumb for space monitoring page
spaceMonitorBreadcrumb=Мониторинг
#XTXT: Type name for spaces in browser tab page title
space=Пространство
#_____________________________________
#XFLD: Spaces label in
spaces=Пространства
#XFLD: Manage plan button text
manageQuotaButtonText=Управление на плана
#XBUT: Manage resources button
manageResourcesButton=Управление на ресурсите
#XFLD: Create space button tooltip
createSpace=Създаване на пространство
#XFLD: Create
create=Създаване
#XFLD: Deploy
deploy=Разгръщане
#XFLD: Page
page=Страница
#XFLD: Cancel
cancel=Отказ
#XFLD: Update
update=Актуализация
#XFLD: Save
save=Запазване
#XFLD: OK
ok=OK
#XFLD: days
days=дни
#XFLD: Space tile edit button label
edit=Редактиране
#XFLD: Auto Assign all objects to space
autoAssign=Автоматично присъединяване
#XFLD: Space tile open monitoring button label
openMonitoring=Наблюдение
#XFLD: Delete
delete=Изтриване
#XFLD: Copy Space
copy=Копиране
#XFLD: Close
close=Затваряне
#XCOL: Space table-view column status
status=Статус
#XFLD: Space status active
activeLabel=Активно
#XFLD: Space status locked
lockedLabel=Заключено
#XFLD: Space status critical
criticalLabel=Критично
#XFLD: Space status cold
coldLabel=Студено
#XFLD: Space status deleted
deletedLabel=Изтрито
#XFLD: Space status unknown
unknownLabel=Неизвестен
#XFLD: Space status ok
okLabel=В изправност
#XFLD: Database user expired
expired=С изтекъл срок
#XFLD: deployed
deployed=С разгръщане
#XFLD: not deployed
notDeployed=Без разгръщане
#XFLD: changes to deploy
changesToDeploy=Промени за разгръщане
#XFLD: pending
pending=Разгръщане
#XFLD: designtime error
designtimeError=Грешка във време на дизайн
#XFLD: runtime error
runtimeError=Грешка във време на изпълнение
#XFLD: Space created by label
createdBy=Създадено от
#XFLD: Space created on label
createdOn=Създадено на
#XFLD: Space deployed on label
deployedOn=Дата на разгръщане
#XFLD: Space ID label
spaceID=ИД на пространството
#XFLD: Priority label
priority=Приоритет
#XFLD: Space Priority label
spacePriority=Приоритет на пространството
#XFLD: Space Configuration label
spaceConfiguration=Конфигурация на пространство
#XFLD: Not available
notAvailable=Няма
#XFLD: WorkloadType default
default=По подразбиране
#XFLD: WorkloadType custom
custom=Персонализирано
#XFLD: Data Lake option label; refers to component SAP HANA Cloud, Data Lake
dataLakeAccess=Достъп до езеро за данни (Data Lake)
#XFLD: Translation label
translationLabel=Превод
#XFLD: Source language label
sourceLanguageLabel=Изходен език
#XFLD: Translation CheckBox label
translationCheckBox=Активиране на превод
#XTXT: Popover hint that space has to be deployed to show user details
openDatabaseUserHintPopoverText=Разгърнете пространството за достъп до данните на потребителя.
#XTXT: Popover hint that space has to be deployed to open database explorer
openDatabaseExplorerHintPopoverText=Разгърнете пространството, за да отворите Database Explorer.
#XTXT: Text for when the Data Lake check box is disabled
dataLakeOptionDisabled=Не може да използвате това пространство за достъп до езерото за данни, защото то вече се използва от друго пространство.
#XFLD: Data Lake check box text; refers to component SAP HANA Cloud, Data Lake
dataLakeCheckBox=Използвайте това пространство за достъп до езерото за данни.
#XFLD: Space Priority minimum label extension
low=Нисък
#XFLD: Space Priority maximum label extension
high=Висок
#XFLD: Space name label
spaceName=Име на пространството
#XFLD: Enable deploy objects checkbox
enableDeployObjects=Разгръщане на обекти
#XTIT: Copy spaces dialog title
copySpaceDialogTitle=Копиране {0}
#XTXT Human readable text for unselected source language
unSelectedSourceLanguage=(Не е избрано)
#XTXT Human readable text for language code "af"
af=африкаанс
#XTXT Human readable text for language code "ar"
ar=арабски
#XTXT Human readable text for language code "bg"
bg=български
#XTXT Human readable text for language code "ca"
ca=каталонски
#XTXT Human readable text for language code "zh"
zh=китайски - опростен
#XTXT Human readable text for language code "zf"
zf=китайски
#XTXT Human readable text for language code "hr"
hr=хърватски
#XTXT Human readable text for language code "cs"
cs=чешки
#XTXT Human readable text for language code "cy"
cy=уелски
#XTXT Human readable text for language code "da"
da=датски
#XTXT Human readable text for language code "nl"
nl=нидерландски
#XTXT Human readable text for language code "en-UK"
en-UK=английски (Обединено кралство)
#XTXT Human readable text for language code "en"
en=английски (САЩ)
#XTXT Human readable text for language code "et"
et=естонски
#XTXT Human readable text for language code "fa"
fa=персийски
#XTXT Human readable text for language code "fi"
fi=фински
#XTXT Human readable text for language code "fr-CA"
fr-CA=френски (Канада)
#XTXT Human readable text for language code "fr"
fr=френски
#XTXT Human readable text for language code "de"
de=немски
#XTXT Human readable text for language code "el"
el=гръцки
#XTXT Human readable text for language code "he"
he=иврит
#XTXT Human readable text for language code "hi"
hi=хинди
#XTXT Human readable text for language code "hu"
hu=унгарски
#XTXT Human readable text for language code "is"
is=исландски
#XTXT Human readable text for language code "id"
id=бахаса Индонезия
#XTXT Human readable text for language code "it"
it=италиански
#XTXT Human readable text for language code "ja"
ja=японски
#XTXT Human readable text for language code "kk"
kk=казахстански
#XTXT Human readable text for language code "ko"
ko=корейски
#XTXT Human readable text for language code "lv"
lv=латвийски
#XTXT Human readable text for language code "lt"
lt=литовски
#XTXT Human readable text for language code "ms"
ms=малайски
#XTXT Human readable text for language code "no"
no=норвежки
#XTXT Human readable text for language code "pl"
pl=полски
#XTXT Human readable text for language code "pt"
pt=португалски (Бразилия)
#XTXT Human readable text for language code "pt-PT"
pt-PT=португалски (Португалия)
#XTXT Human readable text for language code "ro"
ro=румънски
#XTXT Human readable text for language code "ru"
ru=руски
#XTXT Human readable text for language code "sr"
sr=сръбски
#XTXT Human readable text for language code "sh"
sh=сърбо-хърватски
#XTXT Human readable text for language code "sk"
sk=словашки
#XTXT Human readable text for language code "sl"
sl=словенски
#XTXT Human readable text for language code "es"
es=испански
#XTXT Human readable text for language code "es-MX"
es-MX=испански (Мексико)
#XTXT Human readable text for language code "sv"
sv=шведски
#XTXT Human readable text for language code "th"
th=тай
#XTXT Human readable text for language code "tr"
tr=турски
#XTXT Human readable text for language code "uk"
uk=украински
#XTXT Human readable text for language code "vi"
vi=виетнамски
#XTIT: Delete spaces confirmation title
deleteConfirmationHeaderText=Изтриване на пространства
#XTXT: Logically delete space delete confirmation text with space name
logicallyDeleteConfirmationBodyTextWithSpaceName=Наистина ли желаете да преместите пространството „{0}“ в кошчето?
#XTXT: Logically dpace delete confirmation text for multiple spaces
logicallyDeleteConfirmationBodyTextMultiple=Наистина ли желаете да преместите избраните {0} пространства в кошчето?
#XTXT: Delete space delete confirmation text with space name
deleteConfirmationBodyTextWithSpaceName=Наистина ли желаете да изтриете пространството „{0}“? Това действие е необратимо.
#XTXT: Delete space delete confirmation text for multiple spaces
deleteConfirmationBodyTextMultiple=Наистина ли желаете да изтриете избраните {0} пространства? Това действие е необратимо. Следното съдържание ще бъде премахнато {1}:
#XTXT: permanently
permanently=окончателно
#XTXT: {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDetail=Следното съдържание ще бъде {0} изтрито и няма да може да бъде възстановено:
#XTXT: Hint Delete space confirmation text
deleteConfirmationHint=Моля, напишете "{0}", за да потвърдите изтриването.
#XTXT: Delete space confirmation error check spelling
deleteConfirmationErrorCheckSpelling=Моля, проверете за печатни грешки и опитайте пак.
#XTXT: All Spaces
allSpaces=Всички пространства
#XTXT: All data
allData=Всички обекти и данни, които пространството съдържа
#XTXT: All connections
allConnections=Всички връзки, дефинирани в това пространство
#XFLD: Space tile selection box tooltip
clickToSelect=Кликване за избор
#XTXT: All database users
allDatabaseUsers=Всички обекти и данни, които съдържат свързаните с това пространство Open SQL схеми.
#XFLD: remove members button tooltip
deleteUsers=Премахване на членове
#XTXT: Space long description text
description=Описание (най-много 4000 символа)
#XFLD: Add Members button tooltip
addUsers=Добавяне на членове
#XFLD: Add Users button tooltip
addUsersTooltip=Добавяне на потребители
#XFLD: Edit Users button tooltip
editUsersTooltip=Редактиране на потребители
#XFLD: Remove Users button tooltip
removeUsersTooltip=Премахване на потребители
#XFLD: Searchfield placeholder
filter=Търсене
#XCOL: Users table-view column health
health=Изправност
#XCOL: Users table-view column access
access=Достъп
#XFLD: No user found nodatatext
noDataText=Не е намерен потребител
#XTIT: Members dialog title
selectUserDialogTitle=Добавяне на членове
#XTIT: User dialog title
addUserDialogTitle=Добавяне на потребители
#XTIT: Delete connections confirmation title
deleteConnectionConfirmationHeaderText=Изтриване на връзки
#XTIT: Delete single connection confirmation title
deleteConnectionConfirmationHeaderTextSingle=Изтриване на връзка
#XTXT: Delete connection delete confirmation text
deleteConnectionConfirmationBodyText=Наистина ли искате да изтриете избраните връзки? Те ще бъдат трайно премахнати.
#XTIT: Remote source dialog title
selectRemoteConnectionDialogTitle=Избор на връзки
#XTIT: Share connection dialog title
connectionSharingDialogTitle=Споделяне на връзка
#XTIT: Connections shared from other spaces table title
sharedConnectionsFromOtherSpaces=Споделени връзки
#XFLD: Add remote source button tooltip
addRemoteConnections=Добавяне на връзки
#XFLD: Delete remote source button tooltip
deleteRemoteConnections=Премахване на връзки
#XFLD: Share remote source button tooltip
shareConnections=Споделяне на връзки
#XFLD: Tile-layout tooltip
tileLayout=Оформление с плочки
#XFLD: Table-layout tooltip
tableLayout=Оформление с таблица
#XMSG: Success message after creating space
createSpaceSuccessMessage=Пространството е създадено успешно.
#XMSG: Success message after copying space
copySpaceSuccessMessage=Копиране на пространство „{0}“ в пространство „{1}“
#XMSG: Success message after deploying space
deploymentSuccessMessage=Разгръщането на пространството е стартирано
#XMSG: Success message after Apache Spark update started
sparkUpdateStartedSuccessMessage=Актуализацията на Apache Spark започна
#YMSE: Error while starting Apache Spark update
sparkUpdateStartedErrorMessage=Неуспешна актуализация на Apache Spark
#XMSG: Success message after updating space
updateSettingsSuccessMessage=Данните на пространството са актуализирани успешно.
#XMSG: Success message after unlocking space
unlockSpaceSuccessMessage=Пространството е временно отключено
#XMSG: Success message after deleting a single space
deleteSpaceSuccessMessage=Пространството е изтрито
#XMSG: Success message after deleting multiple spaces
deleteSpacesSuccessMessage=Пространствата са изтрити
#XMSG: Success message after restoring a single space
restoreSpaceSuccessMessage=Пространството е възстановено
#XMSG: Success message after restoring multiple spaces
restoreSpacesSuccessMessage=Пространствата са възстановени
#YMSE: Error while updating settings
updateSettingsFailureMessage=Не беше възможно актуализиране на настройките за пространството.
#YMSE: Error while updating data lake option
dataLakeEnabledInOtherSpace=Езерото за данни вече е присъединено на друго пространство. Само по едно пространство може да има достъп до езерото за данни.
#YMSE: Error while updating data lake option
virtualTablesExists=Не може да отмените присъединяването на езерото за данни към това пространство,защото все още има зависимости към виртуални таблици*. Моля, изтрийте виртуалните таблици, за да отмените присъединяването на езерото за данни към това пространство.
#YMSE: Error while unlocking space
unlockSpaceFailureMessage=Не беше възможно отключване на пространството.
#YMSE: Error while creating space
createSpaceError=Не беше възможно създаване на пространство.
#YMSE: Error while creating duplicate space
createDuplicateSpaceError=Вече съществува пространство с име {0}.
#YMSE: Error while deleting a single space
deleteSpaceError=Не беше възможно изтриване на пространството.
#YMSE: Error details while deleting a single space
deleteSpaceDetailedError=Вашето пространство “{0}” вече не работи правилно. Моля, опитайте се да го изтриете пак. Ако въпреки това продължава да не работи, помолете администратора да изтрие вашето пространство или регистрирайте заявка за обслужване.
#YMSE: Error while deleting a single space in files
deleteSpaceInFilesError=Не беше възможно изтриване на данните за пространство във “Файлове”.
#YMSE: Error while deleting a single space users
deleteSpaceUsersError=Не беше възможно премахване на потребителите.
#YMSE: Error while deleting a single space schema
deleteSpaceSchemasError=Не беше възможно премахване на схемите.
#YMSE: Error while deleting a single space connections
deleteSpaceConnectionsError=Не беше възможно премахване на връзките.
#YMSE: Error while deleting a single space data
deleteSpaceDataError=Не беше възможно изтриване на данните за пространство.
#YMSE: Error while deleting multiple spaces
deleteSpacesError=Не беше възможно изтриване на пространствата.
#YMSE: Error while restoring a single space
restoreSpaceError=Не беше възможно възстановяване на пространството.
#YMSE: Error while restoring multiple spaces
restoreSpacesError=Не беше възможно възстановяване на пространствата.
#YMSE: Error while creating users
createUsersError=Потребителите не бяха добавени успешно.
#YMSE: Error while removing users
removeUsersError=Не успяхме да премахнем потребителите.
#YMSE: Error while removing user
removeUserError=невъзможно премахване на потребителите.
#YMSE: Error assigning roles to a user
roleAssignUnAssignError=Не успяхме да добавим потребителя към избраната роля с обхват. \n\nНе може да добавите себе си към роля с обхват. Потърсете съдействие от администратор.
#YMSE: Error assigning user to the space
userAssignError=Не успяхме да присъединим потребителя към пространството. \n\nПотребителят вече е присъединен към максималния разрешен брой (100) пространства за ролите в обхвата.
#YMSE: Error assigning users to the space
usersAssignError=Не успяхме да присъединим потребителите към пространството. \n\nПотребителят вече е присъединен към максималния разрешен брой (100) пространства за ролите в обхвата.
#YMSE: Error while fetching users
fetchUserWithScopedRolesError=Не успяхме да извлечем потребителите. Моля, опитайте отново по-късно.
#YMSE: Error while fetching scoped roles
fetchScopedRolesError=Не успяхме да извлечем ролите от обхвата.
#YMSE: Error while fetching members
fetchUserError=Извличането на членове беше неуспешно. Моля, опитайте пак по-късно.
#YMSE: Error while loading run-time database
loadRuntimeError=Не можахме да заредим информацията от базата данни за времето на изпълнение.
#YMSE: Error while loading spaces
loadSpacesError=За съжаление, нещо се обърка при опита за извличане на вашите пространства.
#YMSE: Error while loading haas resources
loadStorageError=За съжаление, нещо се обърка при опита за извличане на данните за вашето пространство за съхранение.
#YMSE: Error no data could be loaded
loadDataError=За съжаление, нещо се обърка при опита за извличане на вашите данни.
#XFLD: Click to refresh storage data
clickToRefresh=Кликнете тук, за да опитате пак.
#XTIT: Delete space popup title
deleteSpacePopupTitle=Изтриване на пространство
#XCOL: Spaces table-view column name
name=Име
#XCOL: Spaces table-view deployment status
deploymentStatus=Статус на разгръщане
#XFLD: Disk label in space details
storageLabel=Твърд диск (GB)
#XFLD: In-Memory label in space details
ramLabel=Памет (GB)
#XFLD: Memory label on space card
memory=Памет за съхранение
#XFLD: Storage Assignment label in space details
storrageAssignmentLabel=Пространство за съхранение
#XFLD: Storage Type label in space details
storageTypeLabel=Вид съхранение
#XFLD: Enable Space Quota
enableSpaceQuota=Активиране на квота за пространство
#XFLD: No Space Quota
noSpaceQuota=Без квота за пространство
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
diskAndInMemoryText=База данни SAP HANA (диск и в паметта)
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
dataLakeFilesText=Файлове на SAP HANA, Data Lake
#XFLD: Available scoped roles label
availableRoles=Налични роли от обхват
#XFLD: Selected scoped roles label
selectedRoles=Избрани роли от обхват
#XCOL: Spaces table-view column models
models=Модели
#XCOL: Spaces table-view column users
users=Потребители
#XCOL: Spaces table-view column connections
connections=Връзки
#XFLD: Section header overview in space detail
overview=Общ преглед
#XFLD: Section header Apache Spark in space detail
sparkManagementSection=Apache Spark
#XFLD: Section header Apache Spark in space detail
sparkManagementSubSection=Приложения
#XFLD: Section header Apache Spark in space detail
taskAssignmentSubSection=Присъединяване на задачи
#XFLD: vCPU label in Apache Spark section
vCPULabel=vCPU
#XFLD: Memory label in Apache Spark section
memoryLabel=Памет (GB)
#XFLD: space configuration label in Apache Spark section
spaceConfigurationLabel=Конфигурация на пространство
#XFLD: Space Source label
sparkApplicationLabel=Приложение
#XFLD: Cluster Size label
clusterSizeLabel=Размер на клъстер
#XFLD: Driver label
driverLabel=Драйвер
#XFLD: Executor label
executorLabel=Изпълнител
#XFLD: max label
maxLabel=Максимално използвано
#XFLD: TrF Default label
trFDefaultLabel=Поток за трансформация по подразбиране
#XFLD: Merge Default label
mergeDefaultLabel=Сливане по подразбиране
#XFLD: Optimize Default label
optimizeDefaultLabel=Оптимизиране на стойността по подразбиране
#XFLD: Deployment Default label
deploymentDefaultLabel=Разгръщане на локална таблица (файл)
#XFLD: max spark source label
driverExecutorMaxUsedSparkSource={0} CPU / {1} GB
#XFLD: Object type label
taskObjectTypeLabel=Вид обект
#XFLD: Task activity label
taskActivityLabel=Дейност
#XFLD: Task Application ID label
taskApplicationIDLabel=Приложение по подразбиране
#XFLD: Section header in space detail
generalSettings=Общи настройки
#XMSG: Message strip message when a space is locked
spaceLockedMessageStrip=Това пространство в момента е заключено от системата.
#XMSG: Message strip message deploy warning
deployWarningMessageStrip=Промените в този раздел ще бъдат разгърнати незабавно.
#XMSG: Message strip message modify space configuration warning
modifySpaceConfigurationMessageStrip=Имайте предвид, че промяната на стойностите може да причини проблеми в изпълнението.
#XFLD: Button text to unlock the space again
unlockSpace=Отключване на пространството
#XFLD: Info text for audit log formatted message
auditLogText=Активирайте одитните журнали, за да записвате, четете или променяте действия (одитни политики). След това администраторите могат да анализират кой е извършил дадено действие и кога.
#XMSG: Message strip  audit warning
auditLogWarningMessageStrip=Одитните журнали може да заeмат голяма част от мястото на съхранение на диска във вашия наемател.  Ако активирате одитна политика (действия на четене или промяна), трябва редовно да наблюдавате употребата на мястото на съхранение на диска (от картата „Използвано място за съхранение на диск“ в системния монитор), за да избегнете проблеми с диска, които могат да доведат до смущения в услугата. Ако дезактивирате дадена одитна политика, всички нейни записи в одитния журнал ще бъдат изтрити. Ако желаете да запазите записите в одитния журнал, може да ги експортирате, преди да дезактивирате одитната политика.
#XFLD: Message strip link text to open in app help to guide user what to do to unlock his space permanently
unlockSpaceHelp=Показване на помощ
#XMSG: Message strip message when a space will be locked again
spaceTemporarilyUnlockedMessageStrip=Това пространство надвишава присъединеното му място за съхранение и ще бъде заключено след {0} {1}.
#XMSG: Unit for remaining time until space is locked again
hours=часа
#XMSG: Unit for remaining time until space is locked again
minutes=минути
#XFLD: Section header in space detail for auditing
detailsSectionAuditing=Одитиране
#XFLD: Subsection header in space detail for auditing
auditing=Настройки за одит на пространството
#XFLD: Hot space tooltip
hotSpaceCountTooltip=Критични пространства: използваното място за съхранение е повече от 90%
#XFLD: Green space tooltip
greenSpaceCountTooltip=Пространства в изправност: използваното място за съхранение е между 6% и 90%
#XFLD: Cold space tooltip
coldSpaceCountTooltip=Студени пространства: използваното място за съхранение е 5% или по-малко.
#XFLD: Critical space tooltip
criticalSpaceCountTooltip=Критични пространства: използваното място за съхранение е повече от 90%
#XFLD: Green space tooltip
okSpaceCountTooltip=Пространства в изправност: използваното място за съхранение е между 6% и 90%
#XFLD: Locked space tooltip
lockedSpaceCountTooltipNew=Заключени пространства: блокирани поради недостатъчна памет.
#XFLD: Locked space tooltip
lockedSpaceCountTooltip=Заключени пространства
#YMSE: Error while deleting remote source
deleteRemoteError=Връзките не бяха премахнати успешно.
#XFLD: Space ID input tooltip
spaceIDInfoTooltip=Зададеният ИД на пространството не може да бъде променен по-късно.\nВалидни символи: A - Z, 0 - 9, и _
#YMSE: Error when space name is empty
spaceNameEmptyValidationError=Въведете име за пространството.
#YMSE: Error when ECN Business name is empty
ecnNameEmptyValidationError=Въведете бизнес наименование.
#YMSE: Error when space id is empty
spaceIDInputEmptyValidationError=Въведете ИД за пространството.
#YMSE: Error when space id contains invalid characters
spaceIDInputInValidError=Невалидни символи. Моля, използвайте само A - Z, 0 - 9, и _ .
#YMSE: Error when space id exists while creation
spaceIDInputDuplicateError=Вече съществува пространство с този ИД.
#XFLD: Space searchfield placeholder
search=Търсене
#XMSG: Success message after creating users
createUsersSuccess=Членовете са добавени.
#XMSG: Success message after creating user
createUserSuccess=Потребител е добавен
#XMSG: Success message after updating users
updateUsersSuccess=Актуализирани потребители: {0}
#XMSG: Success message after updating user
updateUserSuccess=Потребител е актуализиран
#XMSG: Success message after removing users
removeUsersSuccess=Премахнати потребители: {0}
#XMSG: Success message after removing user
removeUserSuccess=Потребителят е премахнат
#XFLD: Schema name
schemaName=Име на схемата
#XFLD: used of total
ofTemplate={0} от {1}
#XFLD: Assigned disk label in space overview
assignedStorageTemplate=Присъединено дисково пространство ({0} от {1})
#XFLD: Assigned Memory label in space overview
assignedRAMTemplate=Присъединена памет ({0} от {1})
#XFLD: Storage ratio on space
accelearationRAM=Ускорение на паметта
#XFLD: No Storage Consumption
noStorageConsumptionText=Не е присъединена квота за съхранение.
#XFLD: Used disk label in space overview
usedStorageTemplate=Използван диск за съхранение ({0} от {1})
#XFLD: Used Memory label in space overview
usedRAMTemplate=Използвана памет за съхранение ({0} от {1})
#XFLD: Used Disk tooltip
usedStorageTooltipTemplate={0} от {1} използвани диска за съхранение
#XFLD: Used Memory tooltip
usedRAMTooltipTemplate={0} от {1} използваната памет
#XFLD: Assigned Disk tooltip
assignedStorageTooltipTemplate=Присъединени са {0} от {1} дисково пространство
#XFLD: Assigned Memory tooltip
assignedRAMTooltipTemplate={0} от {1} присъединената памет
#XFLD: Space related used storage tooltip
spaceRelatedUsedStorageTooltipTemplate=Данни за пространство: {0}
#XFLD: Not space related used storage tooltip
otherDataUsedStorageTooltipTemplate=Други данни: {0}
#XFLD: Storage used exceeded popover and tooltip
storageUsedExceededText=Помислете за надграждане на плана или се свържете с поддръжката на SAP.
#XCOL: Space table-view column used Disk
usedStorage=Използван диск за съхранение
#XCOL: Space monitor column used Memory
usedRAM=Използвана памет за съхранение
#XCOL: Space monitor column Schema
tableSchema=Схема
#XCOL: Space monitor column Storage Type
tableStorageType=Вид съхранение
#XCOL: Space monitor column Table Type
tableType=Вид таблица
#XCOL: Space monitor column Record Count
tableRecordCount=Брой записи
#XFLD: Assigned Disk
assignedStorage=Присъединен диск за съхранение
#XFLD: Assigned Memory
assignedRAM=Присъединена памет за съхранение
#XCOL: Space table-view column storage utilization
tableStorageUtilization=Използвано съхранение
#XFLD: space status
spaceStatus=Статус на пространството
#XFLD: space type
spaceType=Вид пространство
#XFLD: space type label for space with space type "SAP BW bridge" ("SAP BW bridge" is a product name, do not translate it)
abapbridgeSpaceType=SAP BW свързващо приложение
#XFLD: space type label for space with space type "Data Marketplace"
marketplaceSpaceType=Продукт на доставчик на данни
#XMSG: Error message for when selected space cannot be deleted due to the space type
deleteTypedSpaceError=Не можете да изтриете пространство {0}, защото вида му е {1}.
#XMSG: Error message for when selected spaces cannot be deleted due to the space type
deleteTypedSpacesError=Не можете да изтриете избраните {0} пространства. Пространства от следните видове не може да бъдат изтрити: {1}.
#XFLD: space type label for space without space type
defaultSpaceType=SAP Datasphere
#XFLD: Tooltip for monitor space button
monitorSpace=Наблюдение
#XFLD: Tooltip for edit space button
editSpace=Редактиране на пространството
#XMSG: Deletion warning in messagebox
deleteConfirmation=Наистина ли искате да изтриете това пространство?
#XFLD: Tooltip for delete space button
deleteSpace=Изтриване на пространство
#XFLD: storage
storage=Диск за съхранение
#XFLD: username
userName=Потребителско име
#XFLD: port
port=Порт
#XFLD: hostname
hostName=Име на хост
#XFLD: password
password=Парола
#XBUT: Request new password button
requestPassword=Заявка за нова парола
#YEXP: Usage explanation in time data section
timeDataSectionHint=Създайте времеви таблици и измерения, които да използвате в моделите и историите.
#YEXP: Usage explanation in Space schema section
spaceSchemaHint=Искате ли данните във вашето пространство да бъдат използваеми за други инструменти или приложения? Ако да, създайте един или повече потребители, които имат достъп до данните във вашето пространство, и изберете дали желаете всички бъдещи данни в пространството да бъдат използваеми по подразбиране.
#XTIT: Create schema popup title
createSchemaDialogTitle=Създаване на Open SQL схема
#XTIT: Generate Time Data dialog title
generateTimeDataDialogTitle=Създаване на времеви таблици и измерения
#XTIT: Edit Time Data dialog title
editTimeDataDialogTitle=Редактиране на времеви таблици и измерения
#XTIT: Time Data token title
timeDataTokenTitle=Времеви данни
#XTIT: Time Data token title
timeDataUpdateViews=Актуализиране на изгледи на времеви данни
#XFLD: Time Data token creation in progress label
timeDataCreationInProgress=Създаването е в процес на изпълнение...
#XFLD: Time Data token creation error label
timeDataCreationError=Създаването беше неуспешно. Моля, опитайте отново.
#XTIT: Time Data dialog time settings title
timeDataDialogTimeSettingsTitle=Настройки на времева таблица
#XTIT: Time Data dialog time data table title
timeDataDialogTranslationTablesTitle=Таблици за преобразуване
#XTIT: Time Data dialog dimensions title
timeDataDialogTimeViewsTitle=Времеви измерения
#XFLD: Time Data dialog time range label
timeRangeHint=Определете времевия диапазон.
#XFLD: Time Data dialog time data table label
timeDataHint=Дайте име на вашата таблица.
#XFLD: Time Data dialog time data dimensions label
timeDimensionsHint=Дайте име на вашето измерение.
#XFLD: Time Data Time range description label
timerangeLabel=Времеви диапазон
#XFLD: Time Data dialog from year label
fromYearLabel=Начална година
#XFLD: Time Data dialog to year label
toYearLabel=Крайна година
#XFLD: Time Data dialog calendar type label
calendarTypeLabel=Вид календар
#XFLD: Time Data dialog granularity label
granularityLabel=Грануларност
#XFLD: Time Data dialog technical name label
technicalNameLabel=Техническо име
#XFLD: Time Data dialog quarters text table lable
quarterTextsTableLabel=Таблица за преобразуване за тримесечия
#XFLD: Time Data dialog months text table lable
monthsTextsTableLabel=Таблица за преобразуване за месеци
#XFLD: Time Data dialog days text table lable
daysTextsTableLabel=Таблица за преобразуване за дни
#XFLD: Time Data dialog year label
yearLabel=Измерение за година
#XFLD: Time Data dialog quarter label
quarterLabel=Измерение за тримесечие
#XFLD: Time Data dialog month label
monthLabel=Измерение за месец
#XFLD: Time Data dialog day label
dayLabel=Измерение за ден
#XFLD: Time Data dialog gregorian calendar type label
gregorian=Грегориански
#XFLD: Time Data dialog time granularity day label
day=Ден
#XFLD: Time Data dialog business name maxlength reached
timeRangeValueStateTextMaxLengthBusinessName=Достигната е максималната дължина от 1000 символа.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextMaxRangeExceeded=Максималният времеви диапазон е 150 години.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeLower=“Начална година” трябва да е по-малка стойност от “Крайна година”
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeHigher=“Начална година” трябва да е 1900 или по-голяма стойност.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeHigher=“Крайна година” трябва да е по-голяма стойност от “Начална година”
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeLower=“Крайна година” трябва да е по-ниска стойност от текущата година плюс 100
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningFromYear=Увеличаването на стойността “Начална година” може да доведе до загуба на данни
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningToYear=Намаляването на стойността “Крайна година” може да доведе до загуба на данни
#XMSG: Time Data creation validation error message
timeDataValidationError=Изглежда, че някои полета са невалидни. Моля, проверете задължителните полета, за да продължите.
#XMSG: Time Data deletion popup text
deleteTimeDataConfirmation=Наистина ли искате да изтриете данните?
#XMSG: Time Data creation success message
createTimeDataSuccess=Времевите данни са създадени
#XMSG: Time Data update success message
updateTimeDataSuccess=Времевите данни са актуализирани
#XMSG: Time Data delete success message
deleteTimeDataSuccess=Времевите данни са изтрити
#XMSG: Time Data creation error message
createTimeDataError=Нещо се обърка при опита за създаване на времеви данни.
#XMSG: Time Data update error message
updateTimeDataError=Нещо се обърка при опита за актуализиране на времеви данни.
#XMSG: Time Data creation error message
deleteTimeDataError=Нещо се обърка при опита за изтриване на времеви данни.
#XMSG: Error message when time data could not be retrieved
retrievingTimeDataError=Не беше възможно зареждане на времевите данни.
#XTIT: Time Data deletion popup title
deleteTimeDataConfirmationTitle=Предупреждение
#XTXT: Time Data deletion dependency popup text
deleteTimeDataDependencyText=Не можахме да изтрием вашите времеви данни, защото те се използват в други модели.
#XTXT: Time Data deletion dependency popup text singular
deleteTimeDataDependencyTextSingular=Не можахме да изтрием вашите времеви данни, защото те се използват в друг модел.
#XMSG: Validation error when required time data form field is empty
timeDataRequiredField=Това поле е задължително и не може да остане празно.
#XFLD: Tooltip for Open in Database Explorer button
openInDBX=Отваряне в Database Explorer
#YMSE: Dimension Year
dimensionYearView=Измерение “Година”
#YMSE: Dimension Year
dimensionQuarterView=Измерение “Тримесечие”
#YMSE: Dimension Year
dimensionMonthView=Измерение “Месец”
#YMSE: Dimension Year
dimensionDayView=Измерение “Ден”
#XFLD: Time Data deletion object title
timeDataUsedIn=(използва се в {0} модела)
#XFLD: Time Data deletion object title singular
timeDataUsedInSingular=(използва се в 1 модел)
#XFLD: Time Data deletion table column provider
provider=Доставчик
#XTIT: Time Data dependency dialog title
timeDataDependencyDialogTitle=Зависимости
#XTIT: Create schema popup title
createSpaceSchemaUserDialogTitle=Създаване на потребител за схемата на пространството
#XFLD: Create schema button
createSchemaButton=Създаване на Open SQL схема
#XFLD: Generate TimeData button
generateTimeDataButton=Създаване на времеви таблици и измерения
#XFLD: Show dependencies button
showDependenciesButton=Показване на зависимости
#XFLD: tooltip for disabled buttons when user is not member of space
mustBeMemberOfSpaceForOperation=За да изпълни тази операция, вашият потребител трябва да бъде член на пространството.
#XFLD: Create space schema user button
createSpaceSchemaUserButton=Създаване на потребител за схема на пространство
#YMSE: API Schema users load error
loadSchemaUsersError=Списъкът на потребителите не беше зареден успешно.
#XTIT: API Schema-config popup title
schemaUserDetailsDialogTitle=Подробни данни за потребител на схема на пространство
#XMSG: API Schema user deletion popup text
deleteSpaceSchemaUserConfirmation=Наистина ли искате да изтриете избрания потребител?
#XMSG: API Schema user deletion confirmation text
userDeleteSuccess=Потребителят е изтрит.
#YMSE: API Schema user deletion error
userDeleteError=Не беше възможно изтриване на потребителя.
#XFLD: User deleted
userDeleted=Потребителят е изтрит.
#XTIT: Remove user popup title
removeUserConfirmationTitle=Предупреждение
#XMSG: Remove user popup text
removeUserConfirmation=Наистина ли искате да премахнете потребителя? Потребителят и присъединените му роли от обхвата ще бъдат премахнати от пространството.
#XMSG: Remove users popup text
removeUsersConfirmation=Наистина ли искате да премахнете потребителите? Потребителите и присъединените им роли от обхвата ще бъдат премахнати от пространството.
#XBUT: Confirmation button for remove user dialog
removeUserConfirmationBtn=Премахване
#YMSE: No data text for available roles
noDataAvailableRoles=Пространството не се добавя към никоя роля от обхват. \n За да може да добавяте потребители към пространството, то трябва първо да бъде добавено към една или повече роли с определен обхват.
#YMSE: No data text for selected roles
noDataSelectedRoles=Не са избрани роли от обхват
#XTIT: Schema-config popup title
schemaDetailsDialogTitle=Подробни данни за конфигурация на Open SQL схема
#XFLD: Label for Read Audit Log
auditLogRead=Активиране на одитен журнал за операции на четене
#XFLD: Label for Change Audit Log
auditLogChange=Активиране на одитен журнал за операции на промяна
#XFLD: Label Audit Log Retention
auditLogRetention=Запазване на журналите в продължение на
#XFLD: Label Audit Log Retention Unit
retentionUnit=дни
#YMSE: Error when log retention time is set incorrectly
incorrectRetentionTime=Въведете цяло число между {0} и {1}
#XFLD: Label for Cross Space Schema Access
spaceSchemaAccess=Използване на данни за схема на пространство
#XTIT: Confirm deactivation of space schema consumption dialog title
disableSpaceSchemaConsumptionDialogTitle=Спиране на използването на данни за схема на пространство
#XTXT: Confirm deactivation of space schema consumption dialog text
disableSpaceSchemaConsumptionDialogText=Тази Open SQL схема може би използва данни от вашата схема на пространство. Ако спрете използването, моделите, базирани на данните от схемата на пространството, може да престанат да работят.
#XBUT: Confirmation button for the stop consuming space schema data dialog
stopConsuming=Спиране на използването
#XTXT: Tooltip for data lake icon
dataLakeIconTooltip=Това пространство се използва за достъп до езерото за данни
#XFLD: Tooltip for data lake table entry
dataLakeTableEntryTooltip=Активирано езеро за данни
#XFLD: used In-Memory linit reached tag
usedRamLimitReachedLabel=Достигнат е лимитът за памет
#XFLD: Space storage Limit reached tag
storageLimitReachedLabel=Достигнат е лимитът за съхранение
#XFLD: Minimal Storage Limit Reached tag
minStorageLimitReachedLabel=Достигнат е минималният лимит за съхранение
#XFLD: Space ram tag
ramLimitReachedLabel=Достигнат е лимитът за памет
#XFLD: Minimal In-Memory Limit Reached tag
minRamLimitReachedLabel=Достигнат е минималният лимит за памет
#XFLD: Popover text after click on space storage limit warning
storageLimitReachedText=Достигнахте лимита на присъединеното съхранение в пространството от {0}. Моля, присъединете още съхранение в пространството.
#XFLD: System storage tag
systemStorageLimitReachedLabel=Достигнат е системният лимит за съхранение
#XMSG: Popover text after click on storage limit warning
systemStorageLimitReachedText=Достигнахте системния лимит за съхранение от {0}. Вече не можете да присъединявате допълнително съхранение към пространството.
#XMSG: Schema deletion warning
deleteSchemaConfirmation=Изтриването на тази Open SQL схема ще доведе до трайно изтриване и на всички съхранени обекти и поддържани асоциации в схемата. Искате ли да продължите?
#XMSG: Success message after deleting a schema
schemaDeleteSuccess=Схемата е изтрита
#YMSE: Error while deleting schema.
schemaDeleteError=Не беше възможно изтриване на схемата.
#XMSG: Success message after update a schema
schemaUpdateSuccess=Схемата е актуализирана
#YMSE: Error while updating schema.
schemaUpdateError=Не беше възможно актуализиране на схемата.
#XMSG: Hint when no password is shown in the dialog.
newPasswordHint=Дали сме ви парола за тази схема. Ако сте я забравили или изгубили, можете да заявите нова. Не забравяйте да копирате или запазите новата си парола.
#XMSG: Hint when showing the password in the dialog.
savePasswordHint=Моля, копирайте паролата си. Тя ще ви е необходима за конфигурирането на връзка към тази схема. Ако сте забравили паролата си, можете да отворите този диалогов прозорец, за да зададете нова.
#XFLD,50: Hint on entering schema name in creation dialog
schemaNameHint=Името не може да се променя след създаването.
#XFLD: Open SQL Schemas section sub headline
schemasSQL=Open SQL
#XFLD: Space schema section sub headline
schemasSpace=Пространство
#XFLD: HDI Container section header
HDIContainers=HDI контейнери
#XTXT: Add HDI Containers button tooltip
addHDIContainers=Добавяне на HDI контейнери
#XTXT: Remove HDI Containers button tooltip
removeHDIContainers=Премахване на HDI контейнери
#XFLD: Enable HDI Containers feature button
enableHDIContainersButton=Активиране на достъп
#YMSE: No data text for HDI Containers table
noDataHDIContainers=Не са добавени HDI контейнери.
#YMSE: No data text for Timedata section
noDataTimedata=Няма създадени времеви таблици и измерения.
#YMSE: No data text during runtime not available for Timedata section
runtimeNotAvailableNoDataTimedata=Невъзможно зареждане на времеви таблици и измерения, тъй като базата данни за време на изпълнение не е налична.
#YMSE: No data text during runtime not available for HDI section
runtimeNotAvailableNoDataHDI=Зареждането на HDI контейнери не е възможно, тъй като базата данни за време на изпълнение не е налична.
#YMSE: Error while fetching HDI Containers
fetchHDIContainersError=HDI контейнерите не бяха получени успешно. Моля, опитайте пак по-късно.
#XFLD Table column header for HDI Container names
HDIContainerName=Име на HDI контейнер
#XFLD: Enable HDI Containers feature dialog title
enableHDIContainersDialogTitle=Активиране на достъп
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText1New=Може да активирате SAP SQL Data Warehousing във вашия наемател в SAP Datasphere, за да обменяте данни между вашите HDI контейнери и вашите пространства в SAP Datasphere без необходимост от движение на данни.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText2=За да направите това, създайте заявка за поддръжка, като натиснете бутона по-долу.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText3New=След като заявката ви бъде обработена, трябва да създадете поне един нов HDI контейнер в базата данни за време на изпълнение на SAP Datasphere. След това бутонът „Разрешаване на достъп“ ще бъде заменен с бутона „+“ в раздела за HDI контейнери на всички ваши SAP Datasphere пространства, а вие ще може да добавите вашите контейнери към някое пространство.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText41=Имате нужда от още информация? Посетете %%0. За подробна информация какво да включите в заявката за обслужване, вижте %%1.
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkText=SAP Help
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkTextSapNote3057059=SAP бележка 3057059
#XBUT: Open Ticket Button Text
openTicket=Регистриране на заявка за обслужване
#XBUT: Add Button Text
add=Добавяне
#XBUT: Next Button Text
next=Напред
#XBUT: Edit Button Text
editUsers=Редактиране
#XBUT: create user Button Text
createUser=Създаване
#XBUT: Update user Button Text
updateUser=Избор
#XTIT: Add HDI Containers Dialog Title
addHDIContainersDialogTitle=Добавяне на неприсъединени HDI контейнери
#XFLD: No data text for add HDI container dialog
addHDIContainersDialogNoDataText=Не намерихме неприсъединени контейнери. \n Контейнерът, който търсите, може вече да е присъединен към пространство.
#YMSE: Error while loading assigned HDI Containers
loadAssignedHDIContainersError=Присъединените HDI контейнери не бяха заредени успешно.
#YMSE: Error while loading assigned HDI Containers
loadUnassignedHDIContainersError=HDI контейнерите не бяха заредени успешно.
#XMSG: Success message
succeededToAddHDIContainer=HDI контейнерът е добавен
#XMSG: Success message
succeededToAddHDIContainerPlural=HDI контейнерите са добавени
#XMSG: Success message
succeededToDeleteHDIContainer=HDI контейнерът е премахнат
#XMSG: Success message
succeededToDeleteHDIContainerPlural=HDI контейнерите са премахнати
#XFLD: Time data section sub headline
timeDataSection=Времеви таблици и измерения
#XFLD: Schemas list entry read audit log enabled indicator
readIndicator=Четене
#XFLD: Schemas list entry change audit log enabled indicator
changeIndicator=Промяна
#XFLD: Remote sources section sub headline
allconnections=Присъединяване на връзка
#XFLD: Remote sources section sub headline
localconnections=Локални връзки
#XFLD: Remote sources section sub headline for open Connectors
openConnectors=SAP Open Connectors
#XFLD: User section sub headline
memberassignment=Присъединяване на членове
#XFLD: User assignment section sub headline
userAssignment=Присъединяване на потребител
#XFLD: User section Access dropdown Member
member=Член
#XFLD: User assignment section column name
user=Потребителско име
#XTXT: Selected role count
selectedRoleToolbarText=Избрано: {0}
#XTIT: Space detail section connections title
detailsSectionConnections=Връзки
#XTIT: Space detail section data access title
detailsSectionDataAccess=Достъп до схемата
#XTIT: Space detail section time data title
detailsSectionGenerateData=Времеви данни
#XTIT: Space detail section members title
detailsSectionUsers=Членове
#XTIT: Space detail section Users title
detailsSectionUsersTitle=Потребители
#XTIT: Out of Storage
insufficientStoragePopoverTitle=Изчерпано място за съхранение
#XTIT: Storage distribution
storageDistributionPopoverTitle=Използвано място за съхранение на диска
#XTXT: Out of Storage popover text
insufficientStorageText=За да създадете ново пространство, моля намалете присъединеното място за съхранение на друго пространство или изтрийте пространство, от което повече нямате нужда. Можете да увеличите общото системно съхранение, като се обадите на “Управление на план”.
#XMSG: Space id length warning
spaceIdLengthWarning=Превишен е максимумът от {0} символа.
#XMSG: Space name length warning
spaceNameLengthWarning=Превишен е максимумът от {0} символа.
#XMSG: Space id reserved prefix warning
spaceIdnWarningReservedPrefix=Моля, не използвайте префикса на {0} за избягване на възможни конфликти.
#YMSE: Error while loading open SQL schemas
loadLocalSchemasError=Open SQL схемите не бяха заредени успешно.
#YMSE: Error while creating open SQL schema
createLocalSchemaError=Open SQL схемата не беше създадена успешно.
#YMSE: Error while loading all remote connections
loadRemoteConnectionsError=Не можаха да бъдат заредени всички отдалечени връзки.
#YMSE: Error while loading space details
loadSpaceDetailsError=Подробните данни за пространство не бяха заредени.
#YMSE: Error while deploying space details
deploySpaceDetailsError=Пространството не беше разгърнато.
#YMSE: Error while copying space details
copySpaceDetailsError=Пространството не беше копирано.
#YMSE: Error while loading storage data
loadStorageDataError=Мястото за съхранение не беше заредено успешно.
#YMSE: Error while loading all users
loadAllUsersError=Не можаха да бъдат заредени всички потребители.
#YMSE: Failed to reset password
resetPasswordError=Паролата не можа да бъде зададена наново.
#XMSG: Success message after updating space
resetPasswordSuccessMessage=Зададена е нова парола за схема
#YMSE: DP Agent-name too long
DBAgentNameError=Името на DP агента е твърде дълго.
#YMSE: Schema-name not valid.
schemaNameError=Името на схемата е невалидно.
#YMSE: User name not valid.
UserNameError=Потребителското име е невалидно.
#XTIT: Consumption by Storage Type
consumptionStorageTypeText=Използване по вид съхранение
#XTIT: Consumption by Schema
consumptionSchemaText=Използване по схема
#XTIT: Overall Consumption by schema
overallConsumptionSchemaText=Общо използване на таблица от схема
#XTIT: Overall Consumption by Table Type
overallConsumptionTableTypeText=Общо използвано място за съхранение по вид таблица
#XTIT: Tables
tableDetailsText=Подробни данни за таблица
#XTIT: Table Storage Consumption
tableStorageConsumptionText=Използвано място за съхранение на таблици
#XFLD: Table Type label
tableTypeLabel=Вид таблица
#XFLD: Schema label
schemaLabel=Схема
#XFLD: reset table tooltip
resetTable=Връщане към изходно състояние
#XFLD: In-Memory label in space monitor
inMemoryLabel=Памет
#XFLD: Disk label in space monitor
diskLabel=Твърд диск
#XFLD: Yes
yesLabel=Да
#XFLD: No
noLabel=Не
#XFLD: switch label: Want the data in this space to be consumable by default?
allowConsumptionByDefault=Искате ли данните в това пространство да бъдат използваеми по подразбиране?
#XFLD: Business Name
businessNameLabel=Бизнес наименование
#XFLD: Refresh
refresh=Опресняване
#XMSG: No filter results title
noFilterResultsTitle=Изглежда, че вашите настройки на филтъра не показват никакви данни.
#XMSG: No filter results message
noFilterResultsMsg=Опитайте се да прецизирате настройките на филтъра. Ако продължавате да не виждате данни, създайте няколко таблици в генератора на данни. Когато те започнат да използват място за съхранение, ще можете да ги наблюдавате тук.
#XMSG: Space monitoring Hana state red title
spaceMonitorHanaRedTitle=Базата данни за време на изпълнение не е достъпна.
#XMSG: Space monitoring Hana state red message
spaceMonitorHanaRedMsg=Тъй като базата данни за време на изпълнение не е достъпна, някои функции са дезактивирани и не можем да покажем никаква информация на тази страница.
#YMSE: Error while creating space schema user
createSpaceSchemaUserError=Не беше създаден потребител на схема на пространство.
#YMSE: Error User name already exists
userAlreadyExistsError=Потребителското име вече съществува.
#YMSE: Error Authentication failed
authenticationFailedError=Неуспешно удостоверяване.
#XMSG: Message strip message when a schema user is locked
userLockedMessageStrip=Потребителят е заключен поради твърде много неуспешни опити за вход. За да отключите потребителя, моля заявете нова парола.
#XMSG: Success message after reset and unlock schema user
resetUnlockPasswordSuccessMessage=Зададена е нова парола и потребителят е отключен
#XMSG: user is locked message
userLockedMessage=Потребителят е заключен.
#XCOL: Users table-view column Role
spaceRole=Роля
#XCOL: Users table-view column Scoped Role
spaceScopedRole=Роля от обхват
#XCOL: Users table-view column Space Admin
spaceAdmin=Администратор на пространство
#XFLD: User section dropdown value Viewer
viewer=Средство за преглед
#XFLD: User section dropdown value Modeler
modeler=Средство за моделиране
#XFLD: User section dropdown value Data Integrator
dataIntegrator=Интегратор на данни
#XFLD: User section dropdown value Space Administrator
spaceAdministrator=Администратор на пространство
#XMSG: Success message -  Space Role updated successfully.
updateUserSpaceRoleSuccess=Ролята на пространството е актуализирана
#XMSG: Error message - Failed to update Space Role.
updateUserSpaceRoleError=Ролята на пространството не беше актуализирана успешно.
#XFLD:
databaseUserNameSuffix=Суфикс на име на потребител на база данни
#XTXT: Space Schema password text
spaceSchemaPasswordText=За да конфигурирате връзка към тази схема, копирайте паролата си. В случай че я забравите, винаги можете да поискате нова.
#XFLD: Cloud Platform label in space schema dialog
cloudPlatformLabel=Cloud Platform
#XTXT: Cloud Platform text in space schema dialog
cloudPlatformText=За да конфигурирате достъп чрез този потребител, активирайте потреблението и копирайте идентификационните данни. В случай че можете да копирате само идентификационните данните без паролата, не забравяйте да добавите и паролата след това.
#XFLD: enable consumption in CloudPlatform label in space schema dialog
enableConsumptionInCloudPlatformLabel=Активиране на потребление в Cloud Platform
#XFLD: Credentials for User-Provided Service label in space schema dialog
cloudPlatformCredentialsLabel=Идентификационни данни за услуга, предоставена от потребител:
#XFLD: Credentials for User-Provided Service (Without Password) label in space schema dialog
cloudPlatformCredentialsWithoutPasswordLabel=Идентификационни данни за услуга, предоставена от потребител (без парола):
#XFLD: Copy Credentials label in space schema dialog
copyCredentialsLabel=Копиране на идентификационни данни без парола
#XFLD: Copy Credentials label in space schema dialog
copyFullCredentialsLabel=Копиране на пълните идентификационни данни
#XFLD: Copy Password label in space schema dialog
copyPasswordLabel=Копиране на парола
#XMSG: Credentials copied to clipboard
credentialsCopiedMessage=Идентификационните данни са копирани в клипборда
#XMSG: Password copied to clipboard
passwordCopiedMessage=Паролата е копирана в клипборда
#XMSG: create database user dialog title
createDatabaseUserDialogTitle=Създаване на потребител на база данни
#XMSG: Database Users section title
databaseUsers=Потребители на база данни
#XMSG: Database User Details dialog title
databaseUserDetailsDialogTitle=Подробни данни за потребител на база данни
#XFLD: database user read audit log
databaseUserAuditLogRead=Активиране на одитни журнали за операции на четене и запазване на журналите в продължение на
#XFLD: database user change audit log
databaseUserAuditLogChange=Активиране на одитни журнали за операции на промяна и запазване на журналите в продължение на
#XMSG: Cloud Platform Access
cloudPlatformAccess=Достъп до Cloud Platform
#XFLD: Enable Consumption in Cloud Platform hint
enableConsumptionCloudPlatformHint=Конфигурирайте достъпа до вашия контейнер в HANA Deployment Infrastructure (HDI) чрез потребителя за базата данни. За да свържете контейнера в HDI, SQL моделирането трябва да бъде включено.
#XFLD: Enable HDI Consumption
enableConsumptionCloudPlatform=Активиране на HDI потребление
#XFLD: Enable Consumption hint
enableConsumptionHint=Желаете ли да направите възможно използването на данните във вашето пространство от други инструменти и приложения?
#XFLD: Enable Consumption
enableConsumption=Активиране на SQL потребление
#XFLD: Enable Modeling
enableModeling=Активиране на SQL моделиране
#XMSG: Privileges for Data Modeling
privilegesModeling=Вливане на данни
#XMSG: Privileges for Data Consumption
privilegesConsumption=Потребление на данни за външни инструменти
#XFLD: SQL Modeling
sqlModeling=SQL моделиране
#XFLD: SQL Consumption
sqlConsumption=SQL потребление
#XFLD: enabled
enabled=Активирано
#XFLD: disabled
disabled=Дезактивирано
#XFLD: Edit Privileges
editPrivileges=Редактиране на привилегии
#XFLD: Open Database Explorer
openDBX=Отваряне на Database Explorer
#XFLD: create database user hint
databaseCreateHint=Моля, имайте предвид, че след запазването няма да можете да промените потребителското име.
#XFLD: Internal Schema Name
internalSchemaName=Вътрешно име на схема
#YMSE: Failed to load database users
loadDatabaseUserError=Неуспешно зареждане на потребители на базата данни
#YMSE: Failed to delete database user
deleteDatabaseUsersError=Изтриването на потребителите на базата данни е неуспешно
#XMSG: Success message after deleting database user
deleteDatabaseUserSuccess=Изтриването на потребителя на базата данни е успешно
#XMSG: Success message after deleting database users
deleteDatabaseUsersSuccess=Изтриването на потребителите на базата данни е успешно
#XMSG: Success message after creating database user
createDatabaseUserSuccessMessage=Създаването на потребител на базата данни е успешно
#YMSE: Failed to create database user
createDatabaseUserError=Създаването на потребител на базата данни е неуспешно
#XMSG: Success message after updating database user
updateDatabaseUserSuccessMessage=Актуализирането на потребителя на базата данни е успешно
#YMSE: Failed to update database user
updateDatabaseUserError=Актуализирането на потребителя на базата данни е неуспешно
#XFLD: HDI Consumption
hdiConsumption=HDI потребление
#XTIT: Space detail section Database Access title
detailsSectionDatabaseAccess=Достъп до база данни
#XTXT: Database users consumption hint
databaseUsersConsumptionHint=Направете данните си достъпни за използване по подразбиране. Моделите в конструкторите автоматично ще активират тази възможност.
#XFLD: Default Consumption of Space Data:
defaultConsumptionOfSpaceData=Използване на данни в пространство по подразбиране:
#XFLD: Database User Name
databaseUserName=Име на потребител на база данни
#XMSG: Database User creation validation error message
databaseUserValidationError=Изглежда, че някои полета са невалидни. Моля, проверете задължителните полета, за да продължите.
#XMSG: SQL modeling can’t be enabled since this user has been migrated.
migratedDatabaseUserHint=Вливането на данни не може да бъде активирано, тъй като потребителят е мигриран.
#XBUT: Remove Button Text
remove=Премахване
#XBUT: Remove Spaces Button Text
removeSpaces=Премахване на пространства
#XBUT: Remove Objects Button Text
removeObjects=Премахване на обекти
#XMSG: No members have been added yet.
noMembersAssigned=Още няма добавени членове.
#XMSG: No users have been added yet.
noUsersAssigned=Още няма добавени потребители.
#XMSG: No database users have been created or your filter is not showing any data.
noDatabaseUsers=Няма създадени потребители на база данни или филтърът ви не показва никакви данни.
#XMSG: Please enter a user name.
noDatabaseUsername=Моля, въведете потребителско име.
#XMSG: The user name is too long. Please use a shorter one.
databaseUsernameTooLong=Потребителското име е твърде дълго. Моля, използвайте по-късо.
#XMSG: No privileges have been enabled and this database user will have limited functionality. Do you still want to continue?
noPrivilegesConfirmationMessage=Няма активирани привилегии и този потребител за базата данни ще има ограничена функционалност. Искате ли да продължите?
#XMSG: To enable audit logs for change operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogChangeMessage=За да активирате журнали на проверка за операции промяна, трябва да бъде активирано и вливането на данни. Искате ли да го активирате?
#XMSG: To enable audit logs for read operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogReadMessage=За да активирате журнали на проверка за операции четене, трябва да бъде активирано и вливането на данни. Искате ли да го активирате?
#XMSG: To enable HDI consumption data ingestion and data consumption needs to be enabled. Do you want to enable all?
enableHDIConsumptionMessage=За да активирате потребление HDI, трябва да бъдат активирани също вливането на данни и потреблението на данни. Искате ли да ги активирате?
#XMSG:
databaseUserPasswordText=За да конфигурирате връзка към този потребител на базата данни, копирайте паролата си. В случай че я забравите, винаги можете да поискате нова.
#XTIT: Space detail section members title
detailsSectionMembers=Членове
#XMSG: New password set
newPasswordSet=Зададена е нова парола
#XFLD: Data Ingestion
dataIngestion=Вливане на данни
#XFLD: Data Consumption
dataConsumption=Потребление на данни
#XFLD: Privileges
privileges=Привилегии
#XFLD: Enable Data ingestion
enableDataIngestion=Активиране на вливането на данни
#XMSG: Log the read and change operations for data ingestion.
databaseUserAuditHint=Записвайте операциите за четене и промяна при вливане на данни в журнал.
#XMSG: Make your space data available in your HDI containers.
databaseUserHDIHint=Направете данните в пространството си налични в HDI контейнерите.
#XFLD: Enable Data consumption
enableDataConsumption=Активиране на потреблението на данни
#XMSG: Allow other apps or tools to consume your space data.
enableDataConsumptionHint=Позволете на други приложения и инструменти да използват данните от вашето пространство
#XMSG: To set up access via this schema user copy the credentials to your user-provided service. In case you can only copy the credentials without a password make sure to add the password later.
databaseUserHDIConsumptionHint=За да конфигурирате достъп чрез този потребител на базата, копирайте идентификационните му данни в услугата, предоставена от потребител. В случай че не можете да копирате паролата, не забравяйте да добавите я добавите след копирането.
#XFLD: Data Flow Runtime Capacity label in space overview
dataFlowRuntimeCapacity=Капацитет на време на изпълнение на поток данни ({0}:{1} часа от {2} часа)
#XFLD: Could not load Data Flow Runtime Capacity
dataFlowRuntimeCapacityError=Неуспешно зареждане на капацитет на време на изпълнение на поток данни
#XMSG: User can grant data consumption to other users.
databaseUserConsumptionWithGrantOptionHint=Потребителят може да предостави потребление на данни на други потребители.
#XFLD: Enable data consumption with grant option
enableConsumptionWithGrantOption=Активиране на потреблението на данни с опция за предоставяне
#XMSG: To enable data consumption with grant option data consumption needs to be enabled. Do you want to enable both?
enableConsumptionWithGrantMessage=За да активирате потреблението с опция за предоставяне, потреблението на данни трябва също да е активирано. Желаете ли да активирате и двете?
#XFLD: Enable Automated Predictive Library (APL) and Predictive Analysis Library (PAL)
allowUsingScriptServerFunctionality=Активиране на Automated Predictive Library (APL) и Predictive Analysis Library (PAL)
#XMSG: User can use SAP HANA Cloud embedded machine learning functions.
allowUsingScriptServerFunctionalityHint=Потребителят може да използва вградените в SAP HANA Cloud функции за машинно самообучение.
#XFLD: Password Policy
passwordPolicy=Политика за пароли
#XMSG: Password Policy
passwordPolicyHint=Тук можете да активирате или дезактивирате конфигурираните политики за пароли.
#XFLD: Enable Password Policy
enablePasswordPolicy=Активиране на политика за пароли
#XMSG: Read Access to the Space Schema
readAccessTitle=Достъп за четене до схемата на пространство
#XMSG: read access hint
readAccessHint=Разрешаване на потребителя на базата данни да свързва външни инструменти към схемата на пространство и да чете изгледи, видими за потребление.
#XFLD: Space Schema
spaceSchema=Схема на пространство
#XFLD: Enable Read Access (SQL)
enableReadAccess=Активиране на достъп за четене (SQL)
#XMSG: Allow the user to grant read access to other users.
withGrantOptionHint=Разрешаване на потребителя да дава достъп за четене на други потребители.
#XFLD: With Grant Option
withGrantOption=С опция за предоставяне
#XMSG: Make your space data available in your HDI containers.
enableHDIConsumptionHint=Направете данните в пространството си налични в HDI контейнерите.
#XFLD: Enable HDI Consumption
enableHDIConsumption=Активиране на HDI потребление
#XMSG: Write Access to the User’s Open SQL Schema
writeAccessTitle=Достъп за запис до Open SQL схемата на потребителя
#XMSG: write access hint
writeAccessHint=Разрешаване на потребителя на базата данни да свързва външни инструменти към своята Open SQL схема, за да създава единици данни и да влива данни за използване в пространството.
#XFLD: Open SQL Schema
openSQLSchema=Open SQL схема
#XFLD: Enable Write Access (SQL DDL & DML)
enableWriteAccess=Активиране на достъп за запис (SQL, DDL и DML)
#XMSG: audit hint
auditHint=Записване в журнала на операциите по четене и промяна в Open SQL схемата.
#XMSG: data consumption hint
dataConsumptionHint=Направете така, че всички нови изгледи в пространството по подразбиране да бъдат достъпни за потребление. Средствата за моделиране могат да игнорират тази настройка за отделни изгледи чрез превключвателя „Разкриване за потребление“ в страничния панел за изходни данни. Може също да изберете форматите, в да са достъпни изгледите.
#XFLD: Expose for Consumption by Default
exposeDataConsumption=Разкриване за потребление по подразбиране
#XMSG: database users hint consumption hint
databaseUsersHint2New=Създайте потребители на база данни с цел свързване на външни инструменти към SAP Datasphere. Задайте привилегии, които разрешават потребителите да четат данни в пространството и да създават единици данни (DDL), както и да вливат данни (DML) за използване в него.
#XFLD: Read
read=Четене
#XFLD: Read (HDI)
readHDI=Четене (HDI)
#XFLD: Write
write=Писане
#XMSG: HDI Containers Hint
HDIContainersHint2=Активирайте достъпа до контейнерите на SAP HANA Deployment Infrastructure (HDI) във вашето пространство. Средствата за моделиране могат да използват HDI артефакти като източници за изгледи, а HDI клиентите – да осъществяват достъп до данните във вашите пространства.
#XMSG: Open info dialog
openDatabaseUserInfoDialog=Отваряне на диалоговия прозорец с информация
#XMSG: Database user is locked. Open info dialog to unlock
openLockedDatabaseUserInfoDialog=Потребителят на база данни е заключен. За да го отключите, отворете диалоговия прозорец.
#XFLD: Table
table=Таблица
#XFLD: Space section partner configuration title
partnerConnectionConfigurationSectionTitle=Партньорска връзка
#XFLD: Space section partner configuration subtitle
partnerConnectionConfigurationSectionSubTitle=Конфигурация на партньорска връзка
#XFLD: Space section partner configuration hint
partnerConnectionConfigurationSectionHint=Дефинирайте собствена плочка за партньорска връзка, като добавите iFrame URL и икона. Тази конфигурация е налична само за този притежател.
#XFLD: Table Name Field
partnerConnectionConfigurationName=Име на плочка
#XFLD: Table iFrame URL field
partnerConnectionConfigurationiFrameURL=iFrame URL
#XFLD: Table iFrame Post Message Origin field
partnerConnectionConfigurationiFramePostMessageOrigin=Произход на съобщенията в iFrame
#XFLD: Table icon/image name
partnerConnectionConfigurationIcon=Икона
#XMSG: Table no result found
partnerConnectionConfigurationNotfound=Не намерихме партньорски връзки.
#XMSG: Partner Connection Confgurations are not available
partnerConnectionConfigurationNotAvailable=Конфигурациите на партньорските връзки не могат да бъдат показани, когато базата данни за изпълнение е недостъпна.
#XFLD: Dialog Create Partner Connection Configuration
partnerConnectionConfigurationDialogTitleAdd=Създаване на конфигурация на връзка на партньор
#XFLD: Dialog Create Partner Connection Configuration Upload Icon
partnerConnectionConfigurationButtonUloadPartnerIcon=Качване на икона
#XFLD: Dialog Create Partner Connection Configuration Select
partnerConnectionConfigurationSelectIcon=Избор (максимален размер 200KB)
#XFLD: Dialog Create Partner Connection Configuration Enter a name
partnerConnectionConfigurationNamePlaceholder=Примерна плочка на партньор
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL
partnerConnectionConfigurationiFrameURLPlaceholder=http://www.example.com
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL Check
partnerConnectionConfigurationiFramePostMessageOriginPlaceholder=.example.com
#XFLD: Dialog Create Partner Connection Configuration Browse
partnerConnectionConfigurationBrowseButton=Преглед
#XFLD: Create Partner Connection Configuration Msg
partnerConnectionConfigurationUploadMsg=Успешно създаване на конфигурация на връзка на партньор.
#XMSG: Error while deleting Partner Connection configuration(s)
partnerConnectionConfigurationDeleteError=Възникна грешка при изтриването на конфигурациите на връзки на партньор.
#XMSG: Partner Connection Configuration has been successfully deleted
partnerConnectionConfigurationDeleteSuccess=Успешно изтриване на конфигурация на връзка на партньор.
#XMSG: Error while retrieving Partner Connection Configurations
partnerConnectionConfigurationLoadError=Възникна грешка при извличането на конфигурации на връзки на партньор.
#XMSG: Error fileSizeLimitExceeded
partnerConnectionConfigurationfileSizeLimitExceeded=Файлът не може да бъде качен, защото надвишава максималния размер от 200 KB.
#XBUT: Create Partner Connection Configuration
partnerConnectionConfigurationButtonCreate=Създаване на конфигурация на връзка на партньор
#XBUT: Delete Partner Connection Configuration
partnerConnectionConfigurationButtonDelete=Изтриване на конфигурация на връзка на партньор.
#XMSG: Partner Tile Creation Failure
upsertPartnerTileFailed=Плочката на партньора не беше създадена успешно.
#XMSG: Partner Tile Deletion Failure
deletePartnerTileFailed=Плочката на партньора не можа да бъде изтрита.
#XMSG: HANA cloud connector settings reset failure
resetHANASCCSettingsFailed=Неуспешно повторно задаване на настройки за клиентски SAP HANA Cloud Connector
#XFLD: Workload Class
workloadClass=Клас на работно натоварване
#XFLD: Workload Management
workloadManagement=Управление на работно натоварване
#XFLD: Priority
workloadClassPriority=Приоритет
#XMSG:
workloadManagementPriorityHint=Може да определите какъв да е приоритетът на това пространство при изпращане на заявки към базата данни. Въведете стойност от 1 (най-нисък приоритет) до 8 (най-висок). В ситуация, където пространствата се конкурират за налични нишки, тези с по-висок приоритет се изпълняват преди онези с по-нисък.
#XMSG:
workloadClassPriorityHint=Можете да посочите приоритета на пространството – от 0 (най-нисък) до 8 (най-висок). Инструкциите от пространство с висок приоритет се изпълняват преди тези от пространства с по-нисък приоритет. Стойността по подразбиране е 5, а стойността 9 е запазена за системни операции и не може да се използва за пространство.
#XFLD: Statement Limits
workloadclassStatementLimits=Лимити за инструкции
#XFLD: Workload Configuration
workloadConfiguration=Конфигурация на работно натоварване
#XMSG:
workloadClassStatementLimitsHint=Можете да посочите максималния брой (или процент) нишки и GB памет, които изпълняващите се на момента инструкции в пространството могат да използват. Можете да въведете стойност или процент по желание между 0 (без лимит) и наличните обща памет и нишки в притежателя. \n\n Имайте предвид, че ако посочите лимит за нишките, това може да влоши производителността. \n\n Ако посочите лимит за паметта, след достигането му инструкциите няма да се изпълняват.
#XMSG:
workloadClassStatementLimitsDescription=Конфигурацията по подразбиране поставя широки граници на ресурсите, предотвратявайки в същото време натоварване на системата от единичните пространства.
#XMSG:
workloadClassStatementLimitCustomDescription=Може да зададете максималните общи граници за нишки и памет, които изпълняваните едновременно с това инструкции могат да използват.
#XMSG:
totalStatementThreadLimitHelpText=Задаването на твърде нисък лимит за нишките може да повлияе на производителността на инструкциите, а прекалено високите стойности или 0 може да позволят на пространството да използва всички налични системни нишки.
#XMSG:
totalStatementMemoryLimitHelpText=Задаването на твърде нисък лимит за памет може да доведе до недостиг на такава, а прекалено високите стойности или 0 може да позволят на пространството да използва цялата налична системна памет.
#XMSG:
totalStatementThreadLimitHelpTextRestricted=Въведете процент между 1% и 70% (или съответстващо число) от общия брой налични нишки във вашия притежател. Задаването на твърде нисък лимит на нишки, може да повлияе на производителността на инструкциите, докато прекомерно високите стойности може да повлияят на производителността на инструкциите в други пространства.
#XMSG:
totalStatementThreadLimitHelpTextDynamic=Въведете процент между 1% и {0}% (или еквивалентното число) от общия брой налични нишки във вашия наемател. Задаването на твърде нисък лимит за нишките може да повлияе на производителността на инструкциите, а прекомерно високите стойности може да повлияят на производителността на инструкциите в други пространства.
#XMSG:
totalStatementMemoryLimitHelpTextNew=Въведете стойност или процент между 0 (без ограничение) и общото налично количество памет във вашия притежател. Задаването на твърде нисък лимит за памет, може да повлияе на производителността на инструкциите, докато прекомерно високите стойности може да повлияят на производителността на инструкциите в други пространства.
#XFLD: Total Statement Thread Limit
totalStatementThreadLimit=Общ лимит за нишки за инструкция
#XFLD: Segmented button label
workloadclassPercentage=%
#XFLD: Segmented button label
workloadclassGigaByte=GB
#XFLD, 80: Segmented button label
workloadclassThreads=Нишки
#XFLD: Total Statement Memory Limit
totalStatementMemoryLimit=Общ лимит за памет за инструкция
#YMSE: Error while loading customer hana info
loadCustomerHanaInfoError=Неуспешно зареждане на информация от SAP HANA за клиента.
#XMSG:
minimumLimitReached=Минималният лимит е достигнат.
#XMSG:
maximumLimitReached=Максималният лимит е достигнат.
#XMSG: Name Taken for Technical Name
technical-name-taken=Вече има връзка с посоченото от вас техническо име. Моля, въведете друга стойност.
#XMSG: Name Too long for Technical Name
technical-name-too-long=Въведеното техническото име надхвърля 40 символа. Моля, въведете по-кратка стойност.
#XMSG: Technical name field empty
technical-name-field-empty=Моля, въведете техническо име.
#XMSG: Generic error when user enters invalid characters for a field
field-invalid-characters=Името може да съдържа само букви (a-z), числа (0-9) и долни черти (_).
#XMSG: Generic error when user enters invalid initial characters for a field
field-underscore-trim=Стойността не може да започва или да завършва с долна черта (_).
#XFLD: Checkbox label for Enable Statement Limits
enableStatementLimits=Активиране на лимити за оператори.
#XFLD: Connection SubSection Settings Title in space details
connectionSettings=Настройки
#XMSG: Connections tool hint in Space details section
connectionsToolHint=За създаване или редактиране на връзки, отворете приложението "Връзки" от страничния панел или кликнете тук:
#XBUT: Connection list Open Connection Tool Button
button-connectiontool=Към "Връзки"
#XFLD: Not deployed label on space tile
notDeployedLabel=Областта още не е разгърната.
#XFLD: Not deployed additional text on space tile
notDeployedText=Моля, разгърнете областта.
#XFLD: Corrupt space label on space tile
corruptSpace=Нещо се обърка.
#XFLD: Corrupt space additional text on space tile
corruptSpaceHint=Опитайте да разгърнете повторно или се свържете с отдел Поддръжка
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Данни от журнал на одит
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Административни данни
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Други данни
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Данни в области
#XMSG: Unlock Space confirmation text
unlockSpaceConfirmation=Наистина ли желаете да отключите пространството?
#XMSG: Lock Space confirmation text
lockSpaceConfirmation=Наистина ли желаете да заключите пространството?
#XFLD: Lock
lock=Заключване
#XFLD: Unlock
unlock=Отключване
#XFLD: Locking
locking=Заключване
#XMSG: Success message after locking space
lockSpaceSuccessMsg=Пространството е заключено
#XMSG: Success message after unlocking space
unlockSpaceSuccessMsg=Пространството е отключено
#XMSG: Success message after locking spaces
lockSpacesSuccessMsg=Пространствата са заключени
#XMSG: Success message after unlocking spaces
unlockSpacesSuccessMsg=Пространствата са отключени
#YMSE: Error while locking a space
lockSpaceError=Пространството не може да бъде заключено.
#YMSE: Error while unlocking a space
unlockSpaceError=Пространството не може да бъде отключено.
#XTIT: popup title Warning
confirmationWarningTitle=Внимание
#XMSG: Message strip message when a space was locked manually
manualLockedMessage=Пространството е заключено ръчно.
#XMSG: Message strip message when a space was locked due to the amount of generated audit logs
auditLogLockedMessage=Пространството е заключено от системата, защото журналите на одит използват голямо количество GB на диска.
#XMSG: Message strip message when a space was locked because the assigned quota was exceeded.
quotaLockedMessage=Пространството е заключено от системата, тъй като надхвърля определените за него дялове от паметта или твърдия диск.
#XMSG: Unlock Spaces confirmation text
unlockSpacesConfirmation=Наистина ли желаете да отключите избраните пространства?
#XMSG: Lock Spaces confirmation text
lockSpacesConfirmation=Наистина ли желаете да заключите избраните пространства?
#XTIT: Scoped role editor dialog title
scopedRoleEditorDialogTitle=Редактор на роля от обхват
#XTIT: ECN Management title
ecnManagementTitle=Управление на еластични изчислителни възли и пространства
#XFLD: ECNs
ecns=Еластични изчислителни възли
#XFLD: ECN phase Ready
ecnReady=Готово
#XFLD: ECN phase Running
ecnRunning=Изпълнява се
#XFLD: ECN phase Initial
ecnInitial=Не е готово
#XFLD: ECN phase Starting
ecnStarting=Започва
#XFLD: ECN phase Starting Failed
ecnStartingFailed=Неуспешно стартиране
#XFLD: ECN phase Stopping
ecnStopping=Спира
#XFLD: ECN phase Stopping Failed
ecnStoppingFailed=Неуспешно спиране
#XBTN: Assign Button
assign=Присъединяване на пространства
#XBTN: Start Header-Button
start=Начало
#XBTN: Update Header-Button
repair=Актуализация
#XBTN: Stop Header-Button
stop=Край
#XFLD: ECN hours remaining
ecnHoursRemaining=Остават 1000 часа
#XFLD: ECN block-hours remaining
ecnBlockHoursRemaining=Оставащи блокчасове: {0}
#XFLD: ECN block-hour remaining
ecnBlockHourRemaining=Оставащ блокчас: {0} 
#XTIT: ECN creation dialog title
ecnCreationDialogTitle=Създаване на еластичен изчислителен възел
#XTIT: ECN edit dialog title
ecnEditDialogTitle=Редактиране на еластичен изчислителен възел
#XTIT: ECN deletion dialog title
ecnDeletionDialogTitle=Изтриване на еластичен изчислителен възел
#XTIT: ECN Space assignment dialog title
ecnSpaceAssignmentDialogTitle=Присъединяване на пространства
#XFLD: ECN ID
ECNIDLabel=Еластичен изчислителен възел
#XTXT: Selected toolbar text
selectedToolbarText=Избрано: {0}
#XTIT: Elastic Compute Nodes
ECNslong=Еластични изчислителни възли
#XTIT: Object assignment - Table header: Objects available
objectsAvailable=Брой обекти
#XTIT: Object assignment - Dialog header text
selectObjects=Изберете пространствата и обектите, които искате да присъедините към вашия еластичен изчислителен възел:
#XTIT: Object assignment - Table header title: Objects
objects=Обекти
#XTIT: Object assignment - Table header: Type
type=Вид
#XMSG: Message strip delete database user audit warning
deleteDatabaseUserWarningMessageStrip=Имайте предвид, че изтриването на потребител на базата данни ще доведе до изтриването на всички генерирани записи в журнала на одитa. Ако искате да ги запазите, експортирайте ги, преди да изтриете потребителя на базата данни.
#XMSG: Message strip delete HDI container audit logs impact
deleteHDIContainerWarningMessageStrip=Имайте предвид, че отмяната на присъединяването на HDI контейнер ще доведе до изтриването на всички генерирани записи в журнала на одитa. Ако искате да ги запазите, експортирайте ги, преди да отмените присъединяването на HDI контейнера.
#XTXT: All audit logs
allAuditLogs=Всички записи в журнала на одита, генерирани за пространството
#XMSG: Message strip disable audit warning
disableAuditWarningMessageStrip=Имайте предвид, че дезактивирането на политиката за одити (операции по промяна или четене) ще доведе до изтриването на всичките ѝ записи в журнала на одитa. Ако искате да ги запазите, експортирайте ги, преди да дезактивирате политиката за одити.
#XTIT: No Spaces assigned to ECN Illustration title
noSpacesECNTitle=Все още няма присъединени пространства или обекти
#XTIT: No Spaces assigned to ECN Illustration description
noSpacesECNDescription=За да започнете работа с вашия еластичен изчислителен възел, присъединете към него пространство или обекти.
#XTIT: No Spaces Illustration title
noSpacesTitle=Още няма създадено пространство
#XTIT: No Spaces Illustration description
noSpacesDescription=За да започнете да придобивате данни, създайте пространство.
#XTIT: No Spaces Illustration title
noDeletedSpacesTitle=Кошчето е празно
#XTIT: No Spaces Illustration description
noDeletedSpacesDescription=Може да възстановите изтритите си пространства оттук.
#XTXT:  {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDatabaseUsers=След разгръщането на пространството следните потребители на базата данни ще бъдат {0} изтрити и няма да могат да бъдат възстановени:
#XTIT: Delete database users
deleteDatabaseUsersTitle=Изтриване на потребители на база данни
#YMSE: Error if ecn id already exists
ecnIDInputDuplicateError=Ид вече съществува.
#YMSE: Error if ecn id contains invalid characters
ecnIDInputInvalidError=Моля, използвайте само малки букви a - z и цифри 0 - 9
#YMSE: Error if ecn id is too short
ecnIDInputTooShortError=ИД трябва да е дължина поне {0} символа.
#XMSG: ecn id length warning
ecnIdLengthWarning=Превишен е максимумът от {0} символа.
#XFLD: open System Monitor
systemMonitor=Системен монитор
#XFLD: open ECN schedule dialog menu entry
schedule=Планиране
#XFLD: open create ECN schedule dialog
createSchedule=Създаване на график
#XFLD: open change ECN schedule dialog
changeSchedule=Редактиране на графика
#XFLD: open delete ECN schedule dialog
deleteSchedule=Изтриване на графика
#XFLD: open self assign ECN schedule dialog
selfAssignSchedule=Присъединяване на график към мен
#XFLD: open pause ECN schedule dialog
pauseSchedule=Поставяне на график на пауза
#XFLD: open resume ECN schedule dialog
resumeSchedule=Възобновяване на график
#XFLD: View Logs
viewLogs=Преглед на журналите
#XFLD: Compute Blocks
computeBlocks=Изчислителни блокове
#XFLD: Memory label in ECN creation dialog
ecnMemory=Памет (GB)
#XFLD: Storage label in ECN creation dialog
ecnStorage=Място за съхранение (GB)
#XFLD: Number of CPU label in ECN creation dialog
ecnCPUCount=Брой на централните процесори
#XFLD: ECN updated by label
changedBy=Променено от
#XFLD: ECN updated on label
changedOn=Променено на
#XMSG: Success message after creating a Elastic Compute Node
createEcnSuccessMessage=Създаден е еластичен изчислителен възел
#YMSE: Error while creating a Elastic Compute Node
createEcnError=Не беше създаден еластичен изчислителен възел
#XMSG: Success message after updating a Elastic Compute Node
updateEcnSuccessMessage=Актуализиран е еластичен изчислителен възел
#XMSG: Error while updating a Elastic Compute Node
udpateEcnError=Не успяхме да актуализираме еластичния изчислителен възел
#XMSG: Success message after deleting a Elastic Compute Node
deleteEcnSuccessMessage=Изтрит е еластичен изчислителен възел
#YMSE: Error while deleting a Elastic Compute Node
deleteEcnError=Еластичния изчислителен възел не беше изтрит
#XMSG: Success message after starting a Elastic Compute Node
startingEcnSuccessMessage=Стартиране на еластичен изчислителен възел
#XMSG: Success message after stopping a Elastic Compute Node
stoppingEcnSuccessMessage=Спиране на еластичен изчислителен възел
#YMSE: Error while starting a Elastic Compute Node
startingEcnError=Еластичния изчислителен възел не може да се стартира
#YMSE: Error while stopping a Elastic Compute Node
stoppingEcnError=Еластичния изчислителен възел не може да спре
#XBUT: Add Object button for an ECN
assignObjects=Добавяне на обекти
#XFLD: Enable auto object assignment label
enableAutoObjAssignment=Автоматично добавяне на всички обекти
#XFLD: object type label to be assigned
objectTypeLabel=Вид (семантична употреба)
#XFLD: assigned object type label
assignedObjectTypeLabel=Вид
#XFLD: technical name label
TechnicalNameLabel=Техническо име
#XFLD: Object selecttion ECN label
objectSelectionECNLabel=Изберете обектите, които искате да добавите към вашия еластичен изчислителен възел
#XTIT: Add objects dialog title
assignObjectsTitle=Присъединяване на обекти от
#XFLD: object label with object count
objectLabel=Обект
#XMSG: No objects available to add message.
noObjectsToAssign=Няма налични обекти за присъединяване.
#XMSG: No objects assigned message.
noAssignedObjects=Няма присъединени обекти.
#XTIT: Remove objects popup title
removeObjectsOrSpacesConfirmationTitle=Внимание
#XTIT: Delete ECN popup button and title
deleteECNConfirmationButtonAndTitle=Изтриване
#XMSG: Remove objects popup text
removeObjectsConfirmation=Наистина ли искате да премахнете избраните обекти?
#XMSG: Remove spaces popup text
removeSpacesConfirmation=Наистина ли искате да премахнете избраните пространства?
#XBUT: Confirmation button for objects dialog
removeObjectsOrSpacesConfirmationBtn=Премахване на пространства
#XMSG: Success message after removing objects
removeObjectsSuccessMessage=Разкритите обекти са премахнати
#XMSG: Success message after assigning objects
assignObjectsSuccessMessage=Разкритите обекти са присъединени
#XFLD: auto object assignment label for space tile
autoObjAssignmentLabel=Всички разкрити обекти
#XFLD: Spaces tab label
spacesTabLabel=Пространства
#XFLD: Exposed objects tab label
consumptionObjectsTabLabel=Разкрити обекти
#XMSG: Success message after unassigning spaces
spacesUnassignedSuccessMessage=Пространствата са премахнати
#XMSG: Success message after unassigning space
spaceUnassignedSuccessMessage=Пространството е премахнато
#YMSE: Error while assigning / unassigning spaces
spacesAssignUnassignError=Не успяхме да присъединим или премахнем пространствата.
#YMSE: Error while removing objects
removeObjectsError=Не успяхме да присъединим или премахнем обектите.
#YMSE: Error while removing object
removeObjectError=Не успяхме да присъединим или премахнем обекта.
#YMSE: Error when saving the ecn with invalid value
invalidSizingPlanError=Присъединената досега стойност вече е невалидна. Изберете валидна такава.
#YMSE: Error when saving the ecn with invalid value
invalidPerformanceClassError=Изберете валиден клас на представяне.
#YMSE: Error when saving the ecn with invalid value
performanceClassNotAvailableError=Избраният преди това клас на представяне „{0}” е невалиден в момента. Изберете валиден клас.
#XTXT: Delete ECN confirmation text
deleteConfirmationTxtECN=Наистина ли искате да изтриете избрания еластичен изчислителен възел?
#XFLD: tooltip for ? button
help=Помощ
#XFLD: ECN edit button label
editECN=Конфигуриране
#XFLD: Technical type label for ERModel
DWC_ERMODEL=Модел entity-relationship
#XFLD: Technical type label for Local Table
DWC_LOCAL_TABLE=Локална таблица
#XFLD: Technical type label for Remote Table
DWC_REMOTE_TABLE=Отдалечена таблица
#XFLD: Technical type label for Analytic Model
DWC_ANALYTIC_MODEL=Аналитичен модел
#XFLD: Technical type label for Task Chain
DWC_TASKCHAIN=Верига задачи
#XFLD: Technical type label for Data Flow
DWC_DATAFLOW=Поток от данни
#XFLD: Technical type label for Replication Flow
DWC_REPLICATIONFLOW=Поток на репликация
#XFLD: Technical type label for Transformation Flow
DWC_TRANSFORMATIONFLOW=Поток от трансформации
#XFLD: Technical type label for Intelligent Lookup
DWC_IDT=Интелигентно търсене
#XFLD: Technical type label for Repository
DWC_REPOSITORY_PACKAGE=Хранилище
#XFLD: Technical type label for Enterprise Search
DWC_ESMODEL=Enterprise Search
#XFLD: Technical type label for View
DWC_VIEW=Изглед
#XFLD: Technical type label for Data Product
DWC_DATAPRODUCT=Продукт от данни
#XFLD: Technical type label for Data Access Control
DWC_DAC=Контрол на достъпа до данни
#XFLD: Technical type label for Folder
DWC_FOLDER=Папка
#XFLD: Technical type label for Data Entity
DWC_BUSINESS_ENTITY=Бизнес единица
#XFLD: Technical type label for Business Entity Variant
DWC_BUSINESS_ENTITY_VARIANT=Вариант на бизнес единица
#XFLD: Technical type label for Responsibility Scenario
DWC_AUTH_SCENARIO=Сценарий за отговорност
#XFLD: Technical type label for Fact Model
DWC_FACT_MODEL=Фактологичен модел
#XFLD: Technical type label for Transformation Flow
DWC_PERSPECTIVE=Перспектива
#XFLD: Technical type label for Consumption Model
DWC_CONSUMPTION_MODEL=Модел на потребление
#XFLD: Technical type label for Remote Connection
DWC_REMOTECONNECTION=Отдалечена връзка
#XFLD: Technical type label for Fact Model Variant
DWC_FACT_MODEL_VARIANT=Вариант на фактологичен модел
#XMSG: Schedule created alert message
createScheduleSuccess=Графикът е създаден
#XMSG: Schedule updated alert message
updateScheduleSuccess=Графикът е актуализиран
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Графикът е изтрит
#XMSG: Schedule takeover success message
ownerChangeScheduleSuccess=Графикът е присъединен на вас
#XMSG: Message for starting pausing of schedule
pauseScheduleStarted=1 график е поставен на пауза
#XMSG: Message for starting resuming of schedule
resumeScheduleStarted=1 график е възобновен
#XFLD: Segmented button label
availableSpacesButton=Налично
#XFLD: Segmented button label
selectedSpacesButton=Избрано
#XFLD: Visit website button text
visitWebsite=Посетете уебсайта
#XMSG: Warning message on unselecting source language
sourceLanguageRemovalWarning=Избраният преди това изходен език ще бъде премахнат.
#XBUT: enable translation confirmation button
enableTranslationConfirmationBtn=Активиране
#XFLD: ECN performance class label
performanceClassLabel=Клас на представяне
#XTXT performance class memory text
memoryText=Памет
#XTXT performance class compute text
computeText=Изчисления
#XTXT performance class high-compute text
highComputeText=Висок обем изчисления
#XBUT: Recycle Bin Button Text
recycleBin=Кошче
#XBUT: Restore Button Text
restore=Възстановяване
#XMSG: Warning message for new Workload Management UI
priorityWarning=Тази област е само за четене. Можете да промените приоритета на пространството в областта Система/ Конфигурация / Управление на работно натоварване.
#XMSG: Warning message for new Workload Management UI
workloadWarning=Тази област е само за четене. Можете да промените конфигурацията на работното натоварване на пространството в областта Система/ Конфигурация / Управление на работно натоварване.
#XCOL: Space table-view column: Apache Spark vCPUs
sparkManagementVCPUs=Apache Spark vCPUs
#XCOL: Space table-view column: Apache Spark Memory (GB)
sparkManagementMemory=Памет в Apache Spark (GB)
#XCOL: Space type (from Space Capability) for Data Ingestion Spaces
ingestionSpaceType=Вливане на продукт от данни
#XFLD: Deployment in progress nodatatext for spark table
sparkTableDeploymentInProgress=Няма налични данни, тъй като пространството се разгръща в момента
#XFLD: Runtime data being loaded nodatatext for spark table
sparkTableDeploymentLoading=Няма налични данни, тъй като пространството се зарежда в момента
#XCOL: Space type (from Space Capability) for BW Private Cloud Edition
BWPCESpaceType=SAP BW
#XBUT: Go to Instance Mappings Button
navToMappingsButton=Редактиране на мапирания на инстанции
#XFLD: SAP HANA Data Lake Storage label if less than 1GB
dataLakeStorageUsed=< 1 GB
