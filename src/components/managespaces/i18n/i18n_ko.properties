#DO NOT REMOVE________________________
#XFLD: Breadcrumb for space monitoring page
spaceMonitorBreadcrumb=모니터링
#XTXT: Type name for spaces in browser tab page title
space=공간
#_____________________________________
#XFLD: Spaces label in
spaces=공간
#XFLD: Manage plan button text
manageQuotaButtonText=플랜 관리
#XBUT: Manage resources button
manageResourcesButton=리소스 관리
#XFLD: Create space button tooltip
createSpace=공간 생성
#XFLD: Create
create=생성
#XFLD: Deploy
deploy=배포
#XFLD: Page
page=페이지
#XFLD: Cancel
cancel=취소
#XFLD: Update
update=업데이트
#XFLD: Save
save=저장
#XFLD: OK
ok=확인
#XFLD: days
days=일
#XFLD: Space tile edit button label
edit=편집
#XFLD: Auto Assign all objects to space
autoAssign=자동 지정
#XFLD: Space tile open monitoring button label
openMonitoring=모니터
#XFLD: Delete
delete=삭제
#XFLD: Copy Space
copy=복사
#XFLD: Close
close=닫기
#XCOL: Space table-view column status
status=상태
#XFLD: Space status active
activeLabel=활성
#XFLD: Space status locked
lockedLabel=잠김
#XFLD: Space status critical
criticalLabel=심각
#XFLD: Space status cold
coldLabel=콜드
#XFLD: Space status deleted
deletedLabel=삭제됨
#XFLD: Space status unknown
unknownLabel=알 수 없음
#XFLD: Space status ok
okLabel=양호
#XFLD: Database user expired
expired=만료됨
#XFLD: deployed
deployed=배포됨
#XFLD: not deployed
notDeployed=배포되지 않음
#XFLD: changes to deploy
changesToDeploy=변경사항 배포 대상
#XFLD: pending
pending=배포 중
#XFLD: designtime error
designtimeError=디자인 타임 오류
#XFLD: runtime error
runtimeError=런타임 오류
#XFLD: Space created by label
createdBy=생성자
#XFLD: Space created on label
createdOn=생성일
#XFLD: Space deployed on label
deployedOn=배포일
#XFLD: Space ID label
spaceID=공간 ID
#XFLD: Priority label
priority=우선순위
#XFLD: Space Priority label
spacePriority=공간 우선순위
#XFLD: Space Configuration label
spaceConfiguration=공간 구성
#XFLD: Not available
notAvailable=사용할 수 없음
#XFLD: WorkloadType default
default=기본값
#XFLD: WorkloadType custom
custom=사용자 정의
#XFLD: Data Lake option label; refers to component SAP HANA Cloud, Data Lake
dataLakeAccess=데이터 레이크 액세스
#XFLD: Translation label
translationLabel=번역
#XFLD: Source language label
sourceLanguageLabel=소스 언어
#XFLD: Translation CheckBox label
translationCheckBox=번역 사용
#XTXT: Popover hint that space has to be deployed to show user details
openDatabaseUserHintPopoverText=사용자 세부사항에 액세스하려면 공간을 배포합니다.
#XTXT: Popover hint that space has to be deployed to open database explorer
openDatabaseExplorerHintPopoverText=데이터베이스 탐색기를 열려면 공간을 배포합니다.
#XTXT: Text for when the Data Lake check box is disabled
dataLakeOptionDisabled=이 공간을 사용하여 데이터 레이크에 액세스할 수 없습니다. 이미 다른 공간에서 사용되고 있습니다.
#XFLD: Data Lake check box text; refers to component SAP HANA Cloud, Data Lake
dataLakeCheckBox=이 공간을 사용하여 데이터 레이크에 액세스하십시오.
#XFLD: Space Priority minimum label extension
low=낮음
#XFLD: Space Priority maximum label extension
high=높음
#XFLD: Space name label
spaceName=공간 이름
#XFLD: Enable deploy objects checkbox
enableDeployObjects=오브젝트 배포
#XTIT: Copy spaces dialog title
copySpaceDialogTitle={0} 복사
#XTXT Human readable text for unselected source language
unSelectedSourceLanguage=(선택되지 않음)
#XTXT Human readable text for language code "af"
af=아프리칸스어
#XTXT Human readable text for language code "ar"
ar=아랍어
#XTXT Human readable text for language code "bg"
bg=불가리아어
#XTXT Human readable text for language code "ca"
ca=카탈로니아어
#XTXT Human readable text for language code "zh"
zh=중국어 간체
#XTXT Human readable text for language code "zf"
zf=중국어
#XTXT Human readable text for language code "hr"
hr=크로아티아어
#XTXT Human readable text for language code "cs"
cs=체코어
#XTXT Human readable text for language code "cy"
cy=웨일스어
#XTXT Human readable text for language code "da"
da=덴마크어
#XTXT Human readable text for language code "nl"
nl=네덜란드어
#XTXT Human readable text for language code "en-UK"
en-UK=영어(영국)
#XTXT Human readable text for language code "en"
en=영어(미국)
#XTXT Human readable text for language code "et"
et=에스토니아어
#XTXT Human readable text for language code "fa"
fa=페르시아어
#XTXT Human readable text for language code "fi"
fi=핀란드어
#XTXT Human readable text for language code "fr-CA"
fr-CA=프랑스어(캐나다)
#XTXT Human readable text for language code "fr"
fr=프랑스어
#XTXT Human readable text for language code "de"
de=독일어
#XTXT Human readable text for language code "el"
el=그리스어
#XTXT Human readable text for language code "he"
he=히브리어
#XTXT Human readable text for language code "hi"
hi=힌디어
#XTXT Human readable text for language code "hu"
hu=헝가리어
#XTXT Human readable text for language code "is"
is=아이슬란드어
#XTXT Human readable text for language code "id"
id=인도네시아어
#XTXT Human readable text for language code "it"
it=이탈리아어
#XTXT Human readable text for language code "ja"
ja=일본어
#XTXT Human readable text for language code "kk"
kk=카자흐어
#XTXT Human readable text for language code "ko"
ko=한국어
#XTXT Human readable text for language code "lv"
lv=라트비아어
#XTXT Human readable text for language code "lt"
lt=리투아니아어
#XTXT Human readable text for language code "ms"
ms=말레이어
#XTXT Human readable text for language code "no"
no=노르웨이어
#XTXT Human readable text for language code "pl"
pl=폴란드어
#XTXT Human readable text for language code "pt"
pt=포르투갈어(브라질)
#XTXT Human readable text for language code "pt-PT"
pt-PT=포르투갈어(포르투갈)
#XTXT Human readable text for language code "ro"
ro=루마니아어
#XTXT Human readable text for language code "ru"
ru=러시아어
#XTXT Human readable text for language code "sr"
sr=세르비아어
#XTXT Human readable text for language code "sh"
sh=세르보크로아티아어
#XTXT Human readable text for language code "sk"
sk=슬로바키아어
#XTXT Human readable text for language code "sl"
sl=슬로베니아어
#XTXT Human readable text for language code "es"
es=스페인어
#XTXT Human readable text for language code "es-MX"
es-MX=스페인어(멕시코)
#XTXT Human readable text for language code "sv"
sv=스웨덴어
#XTXT Human readable text for language code "th"
th=태국어
#XTXT Human readable text for language code "tr"
tr=터키어
#XTXT Human readable text for language code "uk"
uk=우크라이나어
#XTXT Human readable text for language code "vi"
vi=베트남어
#XTIT: Delete spaces confirmation title
deleteConfirmationHeaderText=공간 삭제
#XTXT: Logically delete space delete confirmation text with space name
logicallyDeleteConfirmationBodyTextWithSpaceName=공간 "{0}"을(를) 휴지통으로 이동하시겠습니까?
#XTXT: Logically dpace delete confirmation text for multiple spaces
logicallyDeleteConfirmationBodyTextMultiple=선택한 {0}개의 공간을 휴지통으로 이동하시겠습니까?
#XTXT: Delete space delete confirmation text with space name
deleteConfirmationBodyTextWithSpaceName="{0}" 공간을 삭제하시겠습니까? 다음 컨텐트가 영구적으로 삭제됩니다.
#XTXT: Delete space delete confirmation text for multiple spaces
deleteConfirmationBodyTextMultiple=선택한 {0}개의 공간을 삭제하시겠습니까? 이 작업은 취소할 수 없습니다. 다음 컨텐트가 {1} 삭제됩니다.
#XTXT: permanently
permanently=영구적으로
#XTXT: {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDetail=다음 컨텐트가 {0} 삭제되고 복구될 수 없음:
#XTXT: Hint Delete space confirmation text
deleteConfirmationHint=삭제를 확인하려면 {0}을(를) 입력하십시오.
#XTXT: Delete space confirmation error check spelling
deleteConfirmationErrorCheckSpelling=맞춤법을 검사하고 다시 시도하십시오.
#XTXT: All Spaces
allSpaces=모든 공간
#XTXT: All data
allData=공간에 포함된 모든 오브젝트 및 데이터
#XTXT: All connections
allConnections=공간에 정의된 모든 연결
#XFLD: Space tile selection box tooltip
clickToSelect=선택하려면 클릭
#XTXT: All database users
allDatabaseUsers=공간에 연결된 Open SQL 스키마에 포함된 모든 오브젝트 및 데이터
#XFLD: remove members button tooltip
deleteUsers=멤버 제거
#XTXT: Space long description text
description=내역(최대 4000자)
#XFLD: Add Members button tooltip
addUsers=멤버 추가
#XFLD: Add Users button tooltip
addUsersTooltip=사용자 추가
#XFLD: Edit Users button tooltip
editUsersTooltip=사용자 편집
#XFLD: Remove Users button tooltip
removeUsersTooltip=사용자 제거
#XFLD: Searchfield placeholder
filter=검색
#XCOL: Users table-view column health
health=상태
#XCOL: Users table-view column access
access=액세스
#XFLD: No user found nodatatext
noDataText=사용자 없음
#XTIT: Members dialog title
selectUserDialogTitle=멤버 추가
#XTIT: User dialog title
addUserDialogTitle=사용자 추가
#XTIT: Delete connections confirmation title
deleteConnectionConfirmationHeaderText=연결 삭제
#XTIT: Delete single connection confirmation title
deleteConnectionConfirmationHeaderTextSingle=연결 삭제
#XTXT: Delete connection delete confirmation text
deleteConnectionConfirmationBodyText=선택한 연결을 삭제하시겠습니까? 연결이 영구적으로 제거됩니다.
#XTIT: Remote source dialog title
selectRemoteConnectionDialogTitle=연결 선택
#XTIT: Share connection dialog title
connectionSharingDialogTitle=연결 공유
#XTIT: Connections shared from other spaces table title
sharedConnectionsFromOtherSpaces=공유된 연결
#XFLD: Add remote source button tooltip
addRemoteConnections=연결 추가
#XFLD: Delete remote source button tooltip
deleteRemoteConnections=연결 제거
#XFLD: Share remote source button tooltip
shareConnections=연결 공유
#XFLD: Tile-layout tooltip
tileLayout=타일 레이아웃
#XFLD: Table-layout tooltip
tableLayout=테이블 레이아웃
#XMSG: Success message after creating space
createSpaceSuccessMessage=공간이 생성되었습니다.
#XMSG: Success message after copying space
copySpaceSuccessMessage="{0}" 공간을 "{1}" 공간으로 복사하는 중입니다.
#XMSG: Success message after deploying space
deploymentSuccessMessage=공간 배포가 시작되었습니다.
#XMSG: Success message after Apache Spark update started
sparkUpdateStartedSuccessMessage=Apache Spark 업데이트가 시작되었습니다.
#YMSE: Error while starting Apache Spark update
sparkUpdateStartedErrorMessage=Apache Spark 업데이트에 실패했습니다.
#XMSG: Success message after updating space
updateSettingsSuccessMessage=공간 세부사항이 업데이트되었습니다.
#XMSG: Success message after unlocking space
unlockSpaceSuccessMessage=공간이 일시적으로 잠금 해제되었습니다.
#XMSG: Success message after deleting a single space
deleteSpaceSuccessMessage=공간이 삭제되었습니다.
#XMSG: Success message after deleting multiple spaces
deleteSpacesSuccessMessage=공간이 삭제되었습니다.
#XMSG: Success message after restoring a single space
restoreSpaceSuccessMessage=공간이 복원되었습니다.
#XMSG: Success message after restoring multiple spaces
restoreSpacesSuccessMessage=공간이 복원되었습니다.
#YMSE: Error while updating settings
updateSettingsFailureMessage=공간 설정을 업데이트할 수 없습니다.
#YMSE: Error while updating data lake option
dataLakeEnabledInOtherSpace=데이터 레이크가 이미 다른 공간에 지정되었습니다. 한 번에 하나의 공간에서만 데이터 레이크에 액세스할 수 있습니다.
#YMSE: Error while updating data lake option
virtualTablesExists=가상 테이블*에 대한 종속성이 여전히 존재하므로 이 공간에서 데이터 레이크를 지정 해제할 수 없습니다. 이 공간에서 데이터 레이크를 지정 해제하려면 가상 테이블을 삭제하십시오.
#YMSE: Error while unlocking space
unlockSpaceFailureMessage=공간을 잠금 해제할 수 없습니다.
#YMSE: Error while creating space
createSpaceError=공간을 생성할 수 없습니다.
#YMSE: Error while creating duplicate space
createDuplicateSpaceError=이름이 {0}인 공간이 이미 있습니다.
#YMSE: Error while deleting a single space
deleteSpaceError=공간을 삭제할 수 없습니다.
#YMSE: Error details while deleting a single space
deleteSpaceDetailedError=“{0}” 공간이 더 이상 제대로 작동하지 않습니다. 다시 삭제해 보십시오. 여전히 작동하지 않으면 관리자에게 공간을 삭제해 달라고 요청하거나 티켓을 작성하십시오.
#YMSE: Error while deleting a single space in files
deleteSpaceInFilesError=파일의 공간 데이터를 삭제할 수 없습니다.
#YMSE: Error while deleting a single space users
deleteSpaceUsersError=사용자를 제거할 수 없습니다.
#YMSE: Error while deleting a single space schema
deleteSpaceSchemasError=스키마를 제거할 수 없습니다.
#YMSE: Error while deleting a single space connections
deleteSpaceConnectionsError=연결을 제거할 수 없습니다.
#YMSE: Error while deleting a single space data
deleteSpaceDataError=공간 데이터를 삭제할 수 없습니다.
#YMSE: Error while deleting multiple spaces
deleteSpacesError=공간을 삭제할 수 없습니다.
#YMSE: Error while restoring a single space
restoreSpaceError=공간을 복원할 수 없습니다.
#YMSE: Error while restoring multiple spaces
restoreSpacesError=공간을 복원할 수 없습니다.
#YMSE: Error while creating users
createUsersError=사용자를 추가할 수 없습니다.
#YMSE: Error while removing users
removeUsersError=사용자를 제거할 수 없습니다.
#YMSE: Error while removing user
removeUserError=사용자를 제거할 수 없습니다.
#YMSE: Error assigning roles to a user
roleAssignUnAssignError=선택한 범위 지정된 역할에 사용자를 추가할 수 없습니다. \N\n 범위 지정된 역할에 자기 자신을 추가할 수 없습니다. 자신을 범위 지정된 역할에 추가하려면 관리자에게 문의하십시오.
#YMSE: Error assigning user to the space
userAssignError=공간에 사용자를 지정할 수 없습니다. \n\n 범위 지정된 역할 전반에서, 허용되는 최대 공간 수(100)에 이미 사용자가 지정되었습니다.
#YMSE: Error assigning users to the space
usersAssignError=공간에 사용자를 지정할 수 없습니다. \n\n 범위 지정된 역할 전반에서, 허용되는 최대 공간 수(100)에 이미 사용자가 지정되었습니다.
#YMSE: Error while fetching users
fetchUserWithScopedRolesError=사용자를 검색할 수 없습니다. 나중에 다시 시도하십시오.
#YMSE: Error while fetching scoped roles
fetchScopedRolesError=범위 지정된 역할을 검색할 수 없습니다.
#YMSE: Error while fetching members
fetchUserError=멤버를 가져올 수 없습니다. 나중에 다시 시도하십시오.
#YMSE: Error while loading run-time database
loadRuntimeError=런타임 데이터베이스에서 정보를 로드할 수 없습니다.
#YMSE: Error while loading spaces
loadSpacesError=죄송합니다. 공간을 가져오는 동안 문제가 발생했습니다.
#YMSE: Error while loading haas resources
loadStorageError=죄송합니다. 저장소 데이터를 가져오는 동안 문제가 발생했습니다.
#YMSE: Error no data could be loaded
loadDataError=죄송합니다. 데이터를 가져오는 동안 문제가 발생했습니다.
#XFLD: Click to refresh storage data
clickToRefresh=다시 시도하려면 여기를 클릭하십시오.
#XTIT: Delete space popup title
deleteSpacePopupTitle=공간 삭제
#XCOL: Spaces table-view column name
name=이름
#XCOL: Spaces table-view deployment status
deploymentStatus=배포 상태
#XFLD: Disk label in space details
storageLabel=디스크(GB)
#XFLD: In-Memory label in space details
ramLabel=메모리(GB)
#XFLD: Memory label on space card
memory=저장소 메모리
#XFLD: Storage Assignment label in space details
storrageAssignmentLabel=공간 저장소
#XFLD: Storage Type label in space details
storageTypeLabel=저장소 유형
#XFLD: Enable Space Quota
enableSpaceQuota=공간 쿼터 사용
#XFLD: No Space Quota
noSpaceQuota=공간 쿼터 없음
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
diskAndInMemoryText=SAP HANA 데이터베이스(디스크 및 인메모리)
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
dataLakeFilesText=SAP HANA 데이터 레이크 파일
#XFLD: Available scoped roles label
availableRoles=사용 가능한 범위 지정된 역할
#XFLD: Selected scoped roles label
selectedRoles=선택한 범위 지정된 역할
#XCOL: Spaces table-view column models
models=모델
#XCOL: Spaces table-view column users
users=사용자
#XCOL: Spaces table-view column connections
connections=연결
#XFLD: Section header overview in space detail
overview=개요
#XFLD: Section header Apache Spark in space detail
sparkManagementSection=Apache Spark
#XFLD: Section header Apache Spark in space detail
sparkManagementSubSection=어플리케이션
#XFLD: Section header Apache Spark in space detail
taskAssignmentSubSection=태스크 지정
#XFLD: vCPU label in Apache Spark section
vCPULabel=vCPU
#XFLD: Memory label in Apache Spark section
memoryLabel=메모리(GB)
#XFLD: space configuration label in Apache Spark section
spaceConfigurationLabel=공간 구성
#XFLD: Space Source label
sparkApplicationLabel=어플리케이션
#XFLD: Cluster Size label
clusterSizeLabel=클러스터 크기
#XFLD: Driver label
driverLabel=드라이버
#XFLD: Executor label
executorLabel=실행자
#XFLD: max label
maxLabel=최대 사용
#XFLD: TrF Default label
trFDefaultLabel=변환 흐름 기본값
#XFLD: Merge Default label
mergeDefaultLabel=병합 기본값
#XFLD: Optimize Default label
optimizeDefaultLabel=최적화 기본값
#XFLD: Deployment Default label
deploymentDefaultLabel=로컬 테이블(파일) 배포
#XFLD: max spark source label
driverExecutorMaxUsedSparkSource={0} CPU / {1}GB
#XFLD: Object type label
taskObjectTypeLabel=오브젝트 유형
#XFLD: Task activity label
taskActivityLabel=액티비티
#XFLD: Task Application ID label
taskApplicationIDLabel=기본 어플리케이션
#XFLD: Section header in space detail
generalSettings=일반 설정
#XMSG: Message strip message when a space is locked
spaceLockedMessageStrip=이 공간은 현재 시스템에 의해 잠겨 있습니다.
#XMSG: Message strip message deploy warning
deployWarningMessageStrip=이 섹션의 변경사항은 즉시 배포됩니다.
#XMSG: Message strip message modify space configuration warning
modifySpaceConfigurationMessageStrip=이 값을 변경하면 성능 문제가 발생할 수 있습니다.
#XFLD: Button text to unlock the space again
unlockSpace=공간 잠금 해제
#XFLD: Info text for audit log formatted message
auditLogText=읽기나 변경 액션(감사 정책)을 기록하려면 감사 로그를 사용하십시오. 로그를 사용하면 관리자가 누가, 어떠한 액션을, 언제 수행했는지 분석할 수 있습니다.
#XMSG: Message strip  audit warning
auditLogWarningMessageStrip=테넌트에서 감사 로그가 대용량의 디스크 저장소를 차지할 수 있습니다. 감사 정책(읽기 또는 변경 액션)을 사용하는 경우에 ('시스템 모니터'의 '사용된 디스크 저장소' 카드를 통해) 디스크 저장소 사용량을 정기적으로 모니터링해야 디스크가 가득 차는 것을 막을 수 있습니다. 디스크가 부족하면 서비스가 중단될 수 있습니다. 감사 정책을 비활성화하면 감사 로그 엔트리가 모두 삭제됩니다. 감사 로그 엔트리를 보존하려면 감사 정책을 비활성화하기 전에 엑스포트하는 것을 고려하십시오.
#XFLD: Message strip link text to open in app help to guide user what to do to unlock his space permanently
unlockSpaceHelp=도움말 표시
#XMSG: Message strip message when a space will be locked again
spaceTemporarilyUnlockedMessageStrip=이 공간은 공간 저장소를 초과하여 {0} {1} 후에 잠깁니다.
#XMSG: Unit for remaining time until space is locked again
hours=시간
#XMSG: Unit for remaining time until space is locked again
minutes=분
#XFLD: Section header in space detail for auditing
detailsSectionAuditing=감사
#XFLD: Subsection header in space detail for auditing
auditing=공간 감사 설정
#XFLD: Hot space tooltip
hotSpaceCountTooltip=심각 상태인 공간: 사용된 저장소가 90% 초과
#XFLD: Green space tooltip
greenSpaceCountTooltip=양호한 공간: 사용된 저장소가 6% ~ 90% 사이
#XFLD: Cold space tooltip
coldSpaceCountTooltip=콜드 공간: 사용된 저장소가 5% 이하
#XFLD: Critical space tooltip
criticalSpaceCountTooltip=심각 상태인 공간: 사용된 저장소가 90% 초과
#XFLD: Green space tooltip
okSpaceCountTooltip=양호한 공간: 사용된 저장소가 6% ~ 90% 사이
#XFLD: Locked space tooltip
lockedSpaceCountTooltipNew=잠김 상태인 공간: 메모리 부족으로 인해 보류됨
#XFLD: Locked space tooltip
lockedSpaceCountTooltip=잠긴 공간
#YMSE: Error while deleting remote source
deleteRemoteError=연결을 제거할 수 없습니다.
#XFLD: Space ID input tooltip
spaceIDInfoTooltip=공간 ID는 나중에 변경할 수 없습니다.\n유효한 문자는 A-Z, 0-9, _입니다.
#YMSE: Error when space name is empty
spaceNameEmptyValidationError=공간 이름을 입력하십시오.
#YMSE: Error when ECN Business name is empty
ecnNameEmptyValidationError=업무 이름을 입력하십시오.
#YMSE: Error when space id is empty
spaceIDInputEmptyValidationError=공간 ID를 입력하십시오.
#YMSE: Error when space id contains invalid characters
spaceIDInputInValidError=문자가 올바르지 않습니다. A-Z, 0-9, _만 사용하십시오.
#YMSE: Error when space id exists while creation
spaceIDInputDuplicateError=공간 ID가 이미 있습니다.
#XFLD: Space searchfield placeholder
search=검색
#XMSG: Success message after creating users
createUsersSuccess=사용자가 추가되었습니다.
#XMSG: Success message after creating user
createUserSuccess=사용자가 추가되었습니다.
#XMSG: Success message after updating users
updateUsersSuccess={0}명의 사용자가 업데이트되었습니다.
#XMSG: Success message after updating user
updateUserSuccess=사용자가 업데이트되었습니다.
#XMSG: Success message after removing users
removeUsersSuccess={0}명의 사용자가 제거되었습니다.
#XMSG: Success message after removing user
removeUserSuccess=사용자가 제거되었습니다.
#XFLD: Schema name
schemaName=스키마 이름
#XFLD: used of total
ofTemplate={0}/{1}
#XFLD: Assigned disk label in space overview
assignedStorageTemplate=지정된 디스크({0}/{1})
#XFLD: Assigned Memory label in space overview
assignedRAMTemplate=지정된 메모리({0}/{1})
#XFLD: Storage ratio on space
accelearationRAM=메모리 액셀러레이션
#XFLD: No Storage Consumption
noStorageConsumptionText=저장소 쿼터가 지정되어 있지 않습니다.
#XFLD: Used disk label in space overview
usedStorageTemplate=저장소에 사용된 디스크({0} / {1})
#XFLD: Used Memory label in space overview
usedRAMTemplate=저장소에 사용된 메모리({0} / {1})
#XFLD: Used Disk tooltip
usedStorageTooltipTemplate=저장소에 사용된 디스크({0} / {1})
#XFLD: Used Memory tooltip
usedRAMTooltipTemplate={0}/{1} 메모리 사용됨
#XFLD: Assigned Disk tooltip
assignedStorageTooltipTemplate={0}/{1} 디스크 지정됨
#XFLD: Assigned Memory tooltip
assignedRAMTooltipTemplate={0}/{1} 메모리 지정됨
#XFLD: Space related used storage tooltip
spaceRelatedUsedStorageTooltipTemplate=공간 데이터: {0}
#XFLD: Not space related used storage tooltip
otherDataUsedStorageTooltipTemplate=기타 데이터: {0}
#XFLD: Storage used exceeded popover and tooltip
storageUsedExceededText=플랜을 연장하거나 SAP Support에 문의하십시오.
#XCOL: Space table-view column used Disk
usedStorage=저장소에 사용된 디스크
#XCOL: Space monitor column used Memory
usedRAM=저장소에 사용된 메모리
#XCOL: Space monitor column Schema
tableSchema=스키마
#XCOL: Space monitor column Storage Type
tableStorageType=저장소 유형
#XCOL: Space monitor column Table Type
tableType=테이블 유형
#XCOL: Space monitor column Record Count
tableRecordCount=레코드 개수
#XFLD: Assigned Disk
assignedStorage=저장소에 지정된 디스크
#XFLD: Assigned Memory
assignedRAM=저장소에 지정된 메모리
#XCOL: Space table-view column storage utilization
tableStorageUtilization=사용된 저장소
#XFLD: space status
spaceStatus=공간 상태
#XFLD: space type
spaceType=공간 유형
#XFLD: space type label for space with space type "SAP BW bridge" ("SAP BW bridge" is a product name, do not translate it)
abapbridgeSpaceType=SAP BW 브리지
#XFLD: space type label for space with space type "Data Marketplace"
marketplaceSpaceType=데이터 프로바이더 제품
#XMSG: Error message for when selected space cannot be deleted due to the space type
deleteTypedSpaceError=공간 {0}의 유형이 {1}이기 때문에 삭제할 수 없습니다.
#XMSG: Error message for when selected spaces cannot be deleted due to the space type
deleteTypedSpacesError={0}개의 선택된 공간을 삭제할 수 없습니다. {1} 공간 유형의 공간은 삭제할 수 없습니다.
#XFLD: space type label for space without space type
defaultSpaceType=SAP Datasphere
#XFLD: Tooltip for monitor space button
monitorSpace=모니터
#XFLD: Tooltip for edit space button
editSpace=공간 편집
#XMSG: Deletion warning in messagebox
deleteConfirmation=이 공간을 삭제하시겠습니까?
#XFLD: Tooltip for delete space button
deleteSpace=공간 삭제
#XFLD: storage
storage=저장소 디스크
#XFLD: username
userName=사용자 이름
#XFLD: port
port=포트
#XFLD: hostname
hostName=호스트 이름
#XFLD: password
password=비밀번호
#XBUT: Request new password button
requestPassword=신규 비밀번호 요청
#YEXP: Usage explanation in time data section
timeDataSectionHint=모델과 스토리에서 사용할 시간 테이블과 차원을 생성합니다.
#YEXP: Usage explanation in Space schema section
spaceSchemaHint=공간의 데이터를 다른 툴이나 앱에서 사용하려고 하십니까? 그렇다면, 공간의 데이터에 액세스할 수 있는 하나 또는 여러 사용자를 생성하고, 기본적으로 향후 모든 공간 데이터를 사용할 것인지 선택하십시오.
#XTIT: Create schema popup title
createSchemaDialogTitle=Open SQL 스키마 생성
#XTIT: Generate Time Data dialog title
generateTimeDataDialogTitle=시간 테이블 및 차원 생성
#XTIT: Edit Time Data dialog title
editTimeDataDialogTitle=시간 테이블 및 차원 편집
#XTIT: Time Data token title
timeDataTokenTitle=시간 데이터
#XTIT: Time Data token title
timeDataUpdateViews=시간 데이터 뷰 업데이트
#XFLD: Time Data token creation in progress label
timeDataCreationInProgress=생성 중...
#XFLD: Time Data token creation error label
timeDataCreationError=생성하지 못했습니다. 다시 시도하십시오.
#XTIT: Time Data dialog time settings title
timeDataDialogTimeSettingsTitle=시간 테이블 설정
#XTIT: Time Data dialog time data table title
timeDataDialogTranslationTablesTitle=환산 테이블
#XTIT: Time Data dialog dimensions title
timeDataDialogTimeViewsTitle=시간 차원
#XFLD: Time Data dialog time range label
timeRangeHint=시간 범위를 정의하십시오.
#XFLD: Time Data dialog time data table label
timeDataHint=테이블에 이름을 지정하십시오.
#XFLD: Time Data dialog time data dimensions label
timeDimensionsHint=차원에 이름을 지정하십시오.
#XFLD: Time Data Time range description label
timerangeLabel=시간 범위
#XFLD: Time Data dialog from year label
fromYearLabel=시작 연도
#XFLD: Time Data dialog to year label
toYearLabel=종료 연도
#XFLD: Time Data dialog calendar type label
calendarTypeLabel=달력 유형
#XFLD: Time Data dialog granularity label
granularityLabel=단위
#XFLD: Time Data dialog technical name label
technicalNameLabel=기술적 이름
#XFLD: Time Data dialog quarters text table lable
quarterTextsTableLabel=분기 환산 테이블
#XFLD: Time Data dialog months text table lable
monthsTextsTableLabel=월 환산 테이블
#XFLD: Time Data dialog days text table lable
daysTextsTableLabel=일 환산 테이블
#XFLD: Time Data dialog year label
yearLabel=연도 차원
#XFLD: Time Data dialog quarter label
quarterLabel=분기 차원
#XFLD: Time Data dialog month label
monthLabel=월 차원
#XFLD: Time Data dialog day label
dayLabel=일 차원
#XFLD: Time Data dialog gregorian calendar type label
gregorian=그레고리오력
#XFLD: Time Data dialog time granularity day label
day=일
#XFLD: Time Data dialog business name maxlength reached
timeRangeValueStateTextMaxLengthBusinessName=최대 길이 1,000자에 도달했습니다.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextMaxRangeExceeded=최대 시간 범위는 150년입니다.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeLower=“시작 연도”는 “종료 연도”보다 작아야 합니다.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeHigher="시작 연도"는 1900 이후여야 합니다.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeHigher=“종료 연도”는 “시작 연도”보다 커야 합니다.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeLower=“종료 연도”는 현재 연도에 100을 더한 값보다 작아야 합니다.
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningFromYear="시작 연도"를 늘리면 데이터가 손실될 수 있습니다.
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningToYear="종료 연도"를 줄이면 데이터가 손실될 수 있습니다.
#XMSG: Time Data creation validation error message
timeDataValidationError=일부 필드가 올바르지 않은 것 같습니다. 계속하려면 필수 필드를 점검하십시오.
#XMSG: Time Data deletion popup text
deleteTimeDataConfirmation=데이터를 삭제하시겠습니까?
#XMSG: Time Data creation success message
createTimeDataSuccess=시간 데이터가 생성되었습니다.
#XMSG: Time Data update success message
updateTimeDataSuccess=시간 데이터가 업데이트되었습니다.
#XMSG: Time Data delete success message
deleteTimeDataSuccess=시간 데이터가 삭제되었습니다.
#XMSG: Time Data creation error message
createTimeDataError=시간 데이터를 생성하는 동안 문제가 발생했습니다.
#XMSG: Time Data update error message
updateTimeDataError=시간 데이터를 업데이트하는 동안 문제가 발생했습니다.
#XMSG: Time Data creation error message
deleteTimeDataError=시간 데이터를 삭제하는 동안 문제가 발생했습니다.
#XMSG: Error message when time data could not be retrieved
retrievingTimeDataError=시간 데이터를 로드할 수 없습니다.
#XTIT: Time Data deletion popup title
deleteTimeDataConfirmationTitle=경고
#XTXT: Time Data deletion dependency popup text
deleteTimeDataDependencyText=시간 데이터가 다른 모델에서 사용되고 있어서 삭제할 수 없습니다.
#XTXT: Time Data deletion dependency popup text singular
deleteTimeDataDependencyTextSingular=시간 데이터가 또 다른 모델에서 사용되고 있어서 삭제할 수 없습니다.
#XMSG: Validation error when required time data form field is empty
timeDataRequiredField=이 필드는 필수 항목이므로 비워 둘 수 없습니다.
#XFLD: Tooltip for Open in Database Explorer button
openInDBX=데이터베이스 탐색기에서 열기
#YMSE: Dimension Year
dimensionYearView=차원 "연도"
#YMSE: Dimension Year
dimensionQuarterView=차원 "분기"
#YMSE: Dimension Year
dimensionMonthView=차원 "월"
#YMSE: Dimension Year
dimensionDayView=차원 "일"
#XFLD: Time Data deletion object title
timeDataUsedIn=({0}개 모델에서 사용됨)
#XFLD: Time Data deletion object title singular
timeDataUsedInSingular=(1개 모델에서 사용됨)
#XFLD: Time Data deletion table column provider
provider=프로바이더
#XTIT: Time Data dependency dialog title
timeDataDependencyDialogTitle=종속성
#XTIT: Create schema popup title
createSpaceSchemaUserDialogTitle=공간 스키마 사용자 생성
#XFLD: Create schema button
createSchemaButton=Open SQL 스키마 생성
#XFLD: Generate TimeData button
generateTimeDataButton=시간 테이블 및 차원 생성
#XFLD: Show dependencies button
showDependenciesButton=종속성 표시
#XFLD: tooltip for disabled buttons when user is not member of space
mustBeMemberOfSpaceForOperation=이 작업을 수행하려면 사용자가 해당 공간의 멤버여야 합니다.
#XFLD: Create space schema user button
createSpaceSchemaUserButton=공간 스키마 사용자 생성
#YMSE: API Schema users load error
loadSchemaUsersError=사용자 리스트를 로드할 수 없습니다.
#XTIT: API Schema-config popup title
schemaUserDetailsDialogTitle=공간 스키마 사용자 세부사항
#XMSG: API Schema user deletion popup text
deleteSpaceSchemaUserConfirmation=선택한 사용자를 삭제하시겠습니까?
#XMSG: API Schema user deletion confirmation text
userDeleteSuccess=사용자가 삭제되었습니다.
#YMSE: API Schema user deletion error
userDeleteError=사용자를 삭제할 수 없습니다.
#XFLD: User deleted
userDeleted=사용자가 삭제되었습니다.
#XTIT: Remove user popup title
removeUserConfirmationTitle=경고
#XMSG: Remove user popup text
removeUserConfirmation=사용자를 제거하시겠습니까? 사용자와 사용자에게 지정된 범위 지정된 역할이 공간에서 제거됩니다.
#XMSG: Remove users popup text
removeUsersConfirmation=사용자를 제거하시겠습니까? 사용자와 사용자에게 지정된 범위 지정된 역할이 공간에서 제거됩니다.
#XBUT: Confirmation button for remove user dialog
removeUserConfirmationBtn=제거
#YMSE: No data text for available roles
noDataAvailableRoles=공간이 범위 지정된 역할에 추가되지 않았습니다. \n공간에 사용자를 추가할 수 있으려면 이 공간이 먼저 하나 이상의 범위 지정된 역할에 추가되어 있어야 합니다.
#YMSE: No data text for selected roles
noDataSelectedRoles=선택한 범위 지정된 역할이 없습니다.
#XTIT: Schema-config popup title
schemaDetailsDialogTitle=Open SQL 스키마 구성 세부사항
#XFLD: Label for Read Audit Log
auditLogRead=읽기 작업에 대한 감사 로그 사용
#XFLD: Label for Change Audit Log
auditLogChange=변경 작업에 대한 감사 로그 사용
#XFLD: Label Audit Log Retention
auditLogRetention=로그 유지 기간
#XFLD: Label Audit Log Retention Unit
retentionUnit=일
#YMSE: Error when log retention time is set incorrectly
incorrectRetentionTime={0}과(와) {1} 사이의 정수를 입력하십시오.
#XFLD: Label for Cross Space Schema Access
spaceSchemaAccess=공간 스키마 데이터 사용
#XTIT: Confirm deactivation of space schema consumption dialog title
disableSpaceSchemaConsumptionDialogTitle=공간 스키마 데이터 사용 중지
#XTXT: Confirm deactivation of space schema consumption dialog text
disableSpaceSchemaConsumptionDialogText=이 Open SQL 스키마에서 공간 스키마의 데이터를 사용하고 있을 수 있습니다. 사용을 중지하면 해당 공간 스키마를 기반으로 하는 모델이 더 이상 작동하지 않을 수 있습니다.
#XBUT: Confirmation button for the stop consuming space schema data dialog
stopConsuming=사용 중지
#XTXT: Tooltip for data lake icon
dataLakeIconTooltip=이 공간은 데이터 레이크에 액세스하는 데 사용됩니다.
#XFLD: Tooltip for data lake table entry
dataLakeTableEntryTooltip=데이터 레이크 사용
#XFLD: used In-Memory linit reached tag
usedRamLimitReachedLabel=메모리 한도 도달
#XFLD: Space storage Limit reached tag
storageLimitReachedLabel=저장소 한도 도달
#XFLD: Minimal Storage Limit Reached tag
minStorageLimitReachedLabel=최소 저장소 한도 도달
#XFLD: Space ram tag
ramLimitReachedLabel=메모리 한도 도달
#XFLD: Minimal In-Memory Limit Reached tag
minRamLimitReachedLabel=최소 인메모리 한도 도달
#XFLD: Popover text after click on space storage limit warning
storageLimitReachedText=지정된 공간 저장소 한도인 {0}에 도달했습니다. 공간에 추가 저장소를 지정하십시오.
#XFLD: System storage tag
systemStorageLimitReachedLabel=시스템 저장소 한도 도달
#XMSG: Popover text after click on storage limit warning
systemStorageLimitReachedText=시스템 저장소 한도인 {0}에 도달했습니다. 이제 공간에 추가 저장소를 지정할 수 없습니다.
#XMSG: Schema deletion warning
deleteSchemaConfirmation=이 Open SQL 스키마를 삭제하면 스키마에 저장된 오브젝트와 설정된 연관성이 모두 영구적으로 삭제됩니다. 계속하시겠습니까?
#XMSG: Success message after deleting a schema
schemaDeleteSuccess=스키마가 삭제되었습니다.
#YMSE: Error while deleting schema.
schemaDeleteError=스키마를 삭제할 수 없습니다.
#XMSG: Success message after update a schema
schemaUpdateSuccess=스키마가 업데이트되었습니다.
#YMSE: Error while updating schema.
schemaUpdateError=스키마를 업데이트할 수 없습니다.
#XMSG: Hint when no password is shown in the dialog.
newPasswordHint=이 스키마에 대한 비밀번호가 제공되었습니다. 비밀번호를 잊어버렸거나 분실한 경우 신규 비밀번호를 요청할 수 있습니다. 신규 비밀번호를 복사하거나 저장하는 것을 잊지 마십시오.
#XMSG: Hint when showing the password in the dialog.
savePasswordHint=비밀번호를 복사하십시오. 이 스키마에 연결을 설정할 때 비밀번호가 필요합니다. 비밀번호를 잊어버린 경우 이 다이얼로그를 열어 비밀번호를 재설정할 수 있습니다.
#XFLD,50: Hint on entering schema name in creation dialog
schemaNameHint=스키마가 생성된 후에는 이 이름을 변경할 수 없습니다.
#XFLD: Open SQL Schemas section sub headline
schemasSQL=Open SQL
#XFLD: Space schema section sub headline
schemasSpace=공간
#XFLD: HDI Container section header
HDIContainers=HDI 컨테이너
#XTXT: Add HDI Containers button tooltip
addHDIContainers=HDI 컨테이너 추가
#XTXT: Remove HDI Containers button tooltip
removeHDIContainers=HDI 컨테이너 제거
#XFLD: Enable HDI Containers feature button
enableHDIContainersButton=액세스 사용
#YMSE: No data text for HDI Containers table
noDataHDIContainers=HDI 컨테이너가 추가되지 않았습니다.
#YMSE: No data text for Timedata section
noDataTimedata=시간 테이블 및 차원이 생성되지 않았습니다.
#YMSE: No data text during runtime not available for Timedata section
runtimeNotAvailableNoDataTimedata=런타임 데이터베이스를 사용할 수 없어서 시간 테이블 및 차원을 로드할 수 없습니다.
#YMSE: No data text during runtime not available for HDI section
runtimeNotAvailableNoDataHDI=런타임 데이터베이스를 사용할 수 없어서 HDI 컨테이너를 로드할 수 없습니다.
#YMSE: Error while fetching HDI Containers
fetchHDIContainersError=HDI 컨테이너를 가져올 수 없습니다. 나중에 다시 시도하십시오.
#XFLD Table column header for HDI Container names
HDIContainerName=HDI 컨테이너 이름
#XFLD: Enable HDI Containers feature dialog title
enableHDIContainersDialogTitle=액세스 사용
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText1New=데이터를 이동할 필요 없이 HDI 컨테이너와 SAP Datasphere 공간 간에 데이터를 교환하기 위해 SAP Datasphere 테넌트에 있는 SAP SQL Data Warehousing을 사용할 수 있습니다.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText2=이를 위해 아래 버튼을 클릭하여 지원 티켓을 여십시오.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText3New=티켓이 처리되면 SAP Datasphere 런타임 데이터베이스에서 하나 이상의 신규 HDI 컨테이너를 구성해야 합니다. 그러면 모든 SAP Datasphere 공간의 HDI 컨테이너 섹션에서 액세스 사용 버튼이 + 버튼으로 변경되고, 사용자의 컨테이너를 공간에 추가할 수 있습니다.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText41=더 많은 정보가 필요하십니까? %%0(으)로 이동하십시오. 티켓에 포함할 내용에 대한 세부 정보는 %%1에서 확인하십시오.
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkText=SAP 도움말
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkTextSapNote3057059=SAP Note 3057059
#XBUT: Open Ticket Button Text
openTicket=티켓 작성
#XBUT: Add Button Text
add=추가
#XBUT: Next Button Text
next=다음
#XBUT: Edit Button Text
editUsers=편집
#XBUT: create user Button Text
createUser=생성
#XBUT: Update user Button Text
updateUser=선택
#XTIT: Add HDI Containers Dialog Title
addHDIContainersDialogTitle=미지정 HDI 컨테이너 추가
#XFLD: No data text for add HDI container dialog
addHDIContainersDialogNoDataText=미지정 컨테이너가 없습니다. \n 찾고 있는 컨테이너가 이미 공간에 지정된 것일 수 있습니다.
#YMSE: Error while loading assigned HDI Containers
loadAssignedHDIContainersError=지정된 HDI 컨테이너를 로드할 수 없습니다.
#YMSE: Error while loading assigned HDI Containers
loadUnassignedHDIContainersError=HDI 컨테이너를 로드할 수 없습니다.
#XMSG: Success message
succeededToAddHDIContainer=HDI 컨테이너가 추가되었습니다.
#XMSG: Success message
succeededToAddHDIContainerPlural=HDI 컨테이너가 추가되었습니다.
#XMSG: Success message
succeededToDeleteHDIContainer=HDI 컨테이너가 제거되었습니다.
#XMSG: Success message
succeededToDeleteHDIContainerPlural=HDI 컨테이너가 제거되었습니다.
#XFLD: Time data section sub headline
timeDataSection=시간 테이블 및 차원
#XFLD: Schemas list entry read audit log enabled indicator
readIndicator=읽기
#XFLD: Schemas list entry change audit log enabled indicator
changeIndicator=변경
#XFLD: Remote sources section sub headline
allconnections=연결 지정
#XFLD: Remote sources section sub headline
localconnections=로컬 연결
#XFLD: Remote sources section sub headline for open Connectors
openConnectors=SAP Open Connectors
#XFLD: User section sub headline
memberassignment=멤버 지정
#XFLD: User assignment section sub headline
userAssignment=사용자 지정
#XFLD: User section Access dropdown Member
member=멤버
#XFLD: User assignment section column name
user=사용자 이름
#XTXT: Selected role count
selectedRoleToolbarText=선택됨: {0}
#XTIT: Space detail section connections title
detailsSectionConnections=연결
#XTIT: Space detail section data access title
detailsSectionDataAccess=스키마 액세스
#XTIT: Space detail section time data title
detailsSectionGenerateData=시간 데이터
#XTIT: Space detail section members title
detailsSectionUsers=멤버
#XTIT: Space detail section Users title
detailsSectionUsersTitle=사용자
#XTIT: Out of Storage
insufficientStoragePopoverTitle=저장소 부족
#XTIT: Storage distribution
storageDistributionPopoverTitle=사용된 디스크 저장소
#XTXT: Out of Storage popover text
insufficientStorageText=신규 공간을 생성하려면 다른 공간의 지정된 저장소를 줄이거나 더 이상 필요하지 않은 공간을 삭제하십시오. '플랜 관리'를 호출하여 전체 시스템 저장소를 늘릴 수 있습니다.
#XMSG: Space id length warning
spaceIdLengthWarning=최대 문자 수인 {0}자를 초과했습니다.
#XMSG: Space name length warning
spaceNameLengthWarning=최대 문자 수인 {0}자를 초과했습니다.
#XMSG: Space id reserved prefix warning
spaceIdnWarningReservedPrefix=충돌을 방지하려면 {0} 접두부를 사용하지 마십시오.
#YMSE: Error while loading open SQL schemas
loadLocalSchemasError=Open SQL 스키마를 로드할 수 없습니다.
#YMSE: Error while creating open SQL schema
createLocalSchemaError=Open SQL 스키마를 생성할 수 없습니다.
#YMSE: Error while loading all remote connections
loadRemoteConnectionsError=원격 연결을 모두 로드하지는 못했습니다.
#YMSE: Error while loading space details
loadSpaceDetailsError=공간 세부사항을 로드할 수 없습니다.
#YMSE: Error while deploying space details
deploySpaceDetailsError=공간을 배포할 수 없습니다.
#YMSE: Error while copying space details
copySpaceDetailsError=공간을 복사할 수 없습니다.
#YMSE: Error while loading storage data
loadStorageDataError=저장소 데이터를 로드할 수 없습니다.
#YMSE: Error while loading all users
loadAllUsersError=모든 사용자를 로드하지는 못했습니다.
#YMSE: Failed to reset password
resetPasswordError=비밀번호를 재설정할 수 없습니다.
#XMSG: Success message after updating space
resetPasswordSuccessMessage=스키마에 신규 비밀번호가 설정되었습니다.
#YMSE: DP Agent-name too long
DBAgentNameError=DP Agent의 이름이 너무 깁니다.
#YMSE: Schema-name not valid.
schemaNameError=스키마 이름이 잘못되었습니다.
#YMSE: User name not valid.
UserNameError=사용자 이름이 잘못되었습니다.
#XTIT: Consumption by Storage Type
consumptionStorageTypeText=저장소 유형별 사용
#XTIT: Consumption by Schema
consumptionSchemaText=스키마별 사용
#XTIT: Overall Consumption by schema
overallConsumptionSchemaText=스키마별 전체 테이블 사용
#XTIT: Overall Consumption by Table Type
overallConsumptionTableTypeText=테이블 유형별 전체 사용
#XTIT: Tables
tableDetailsText=테이블 세부사항
#XTIT: Table Storage Consumption
tableStorageConsumptionText=테이블 저장소 사용
#XFLD: Table Type label
tableTypeLabel=테이블 유형
#XFLD: Schema label
schemaLabel=스키마
#XFLD: reset table tooltip
resetTable=테이블 재설정
#XFLD: In-Memory label in space monitor
inMemoryLabel=메모리
#XFLD: Disk label in space monitor
diskLabel=디스크
#XFLD: Yes
yesLabel=예
#XFLD: No
noLabel=아니오
#XFLD: switch label: Want the data in this space to be consumable by default?
allowConsumptionByDefault=기본적으로 이 공간의 데이터 사용을 허용하시겠습니까?
#XFLD: Business Name
businessNameLabel=업무 이름
#XFLD: Refresh
refresh=새로 고침
#XMSG: No filter results title
noFilterResultsTitle=필터 설정과 일치하는 데이터가 없는 것 같습니다.
#XMSG: No filter results message
noFilterResultsMsg=필터 설정을 조정해 보십시오. 여전히 데이터가 표시되지 않으면 데이터 빌더에서 테이블을 몇 개 생성하십시오. 테이블에 저장소가 사용되면 여기에서 모니터링이 가능합니다.
#XMSG: Space monitoring Hana state red title
spaceMonitorHanaRedTitle=런타임 데이터베이스를 사용할 수 없습니다.
#XMSG: Space monitoring Hana state red message
spaceMonitorHanaRedMsg=런타임 데이터베이스를 사용할 수 없어서 일부 기능이 비활성화되었고 이 페이지에 정보를 표시할 수 없습니다.
#YMSE: Error while creating space schema user
createSpaceSchemaUserError=공간 스키마 사용자를 생성할 수 없습니다.
#YMSE: Error User name already exists
userAlreadyExistsError=사용자 이름이 이미 있습니다.
#YMSE: Error Authentication failed
authenticationFailedError=인증에 실패했습니다.
#XMSG: Message strip message when a schema user is locked
userLockedMessageStrip=로그인에 너무 많이 실패하여 사용자가 잠겨 있습니다. 사용자 잠금을 해제하려면 신규 비밀번호를 요청하십시오.
#XMSG: Success message after reset and unlock schema user
resetUnlockPasswordSuccessMessage=신규 비밀번호가 설정되고 사용자 잠금이 해제되었습니다.
#XMSG: user is locked message
userLockedMessage=사용자가 잠겨 있습니다.
#XCOL: Users table-view column Role
spaceRole=역할
#XCOL: Users table-view column Scoped Role
spaceScopedRole=범위 지정된 역할
#XCOL: Users table-view column Space Admin
spaceAdmin=공간 관리자
#XFLD: User section dropdown value Viewer
viewer=조회자
#XFLD: User section dropdown value Modeler
modeler=모델러
#XFLD: User section dropdown value Data Integrator
dataIntegrator=데이터 통합자
#XFLD: User section dropdown value Space Administrator
spaceAdministrator=공간 관리자
#XMSG: Success message -  Space Role updated successfully.
updateUserSpaceRoleSuccess=공간 역할이 업데이트되었습니다.
#XMSG: Error message - Failed to update Space Role.
updateUserSpaceRoleError=공간 역할이 업데이트되지 않았습니다.
#XFLD:
databaseUserNameSuffix=데이터베이스 사용자 이름 접미부
#XTXT: Space Schema password text
spaceSchemaPasswordText=이 스키마에 연결을 설정하려면 비밀번호를 복사하십시오. 비밀번호를 잊어버린 경우 언제든지 새로운 비밀번호를 요청할 수 있습니다.
#XFLD: Cloud Platform label in space schema dialog
cloudPlatformLabel=Cloud Platform
#XTXT: Cloud Platform text in space schema dialog
cloudPlatformText=이 사용자를 통해 액세스를 설정하려면 사용을 활성화하고 자격 증명을 복사하십시오. 비밀번호 없이 자격 증명의 복사만 가능한 경우 나중에 반드시 비밀번호를 추가해야 합니다.
#XFLD: enable consumption in CloudPlatform label in space schema dialog
enableConsumptionInCloudPlatformLabel=Cloud Platform에서 사용 활성화
#XFLD: Credentials for User-Provided Service label in space schema dialog
cloudPlatformCredentialsLabel=사용자 제공 서비스를 위한 자격 증명:
#XFLD: Credentials for User-Provided Service (Without Password) label in space schema dialog
cloudPlatformCredentialsWithoutPasswordLabel=사용자 제공 서비스를 위한 자격 증명(비밀번호 없음):
#XFLD: Copy Credentials label in space schema dialog
copyCredentialsLabel=비밀번호 없이 자격 증명 복사
#XFLD: Copy Credentials label in space schema dialog
copyFullCredentialsLabel=전체 자격 증명 복사
#XFLD: Copy Password label in space schema dialog
copyPasswordLabel=비밀번호 복사
#XMSG: Credentials copied to clipboard
credentialsCopiedMessage=자격 증명이 클립보드에 복사되었습니다.
#XMSG: Password copied to clipboard
passwordCopiedMessage=비밀번호가 클립보드에 복사되었습니다.
#XMSG: create database user dialog title
createDatabaseUserDialogTitle=데이터베이스 사용자 생성
#XMSG: Database Users section title
databaseUsers=데이터베이스 사용자
#XMSG: Database User Details dialog title
databaseUserDetailsDialogTitle=데이터베이스 사용자 세부사항
#XFLD: database user read audit log
databaseUserAuditLogRead=읽기 작업에 대한 감사 로그 사용 및 로그 유지
#XFLD: database user change audit log
databaseUserAuditLogChange=변경 작업에 대한 감사 로그 사용 및 로그 유지
#XMSG: Cloud Platform Access
cloudPlatformAccess=Cloud Platform 액세스
#XFLD: Enable Consumption in Cloud Platform hint
enableConsumptionCloudPlatformHint=이 데이터베이스 사용자를 통해 HANA Deployment Infrastructure(HDI) 컨테이너에 대한 액세스를 설정합니다. HDI 컨테이너에 연결하려면 SQL 모델링을 설정해야 합니다.
#XFLD: Enable HDI Consumption
enableConsumptionCloudPlatform=HDI 사용 활성화
#XFLD: Enable Consumption hint
enableConsumptionHint=내 공간의 데이터가 다른 툴 또는 앱에서 사용되게 하시겠습니까?
#XFLD: Enable Consumption
enableConsumption=SQL 사용 활성화
#XFLD: Enable Modeling
enableModeling=SQL 모델링 사용
#XMSG: Privileges for Data Modeling
privilegesModeling=데이터 수집
#XMSG: Privileges for Data Consumption
privilegesConsumption=외부 툴에서 데이터 사용
#XFLD: SQL Modeling
sqlModeling=SQL 모델링
#XFLD: SQL Consumption
sqlConsumption=SQL 사용
#XFLD: enabled
enabled=사용
#XFLD: disabled
disabled=사용 안 함
#XFLD: Edit Privileges
editPrivileges=권한 편집
#XFLD: Open Database Explorer
openDBX=데이터베이스 탐색기 열기
#XFLD: create database user hint
databaseCreateHint=저장 후에는 사용자 이름을 다시 변경할 수 없음에 유의하십시오.
#XFLD: Internal Schema Name
internalSchemaName=내부 스키마 이름
#YMSE: Failed to load database users
loadDatabaseUserError=데이터베이스 사용자를 로드할 수 없음
#YMSE: Failed to delete database user
deleteDatabaseUsersError=데이터베이스 사용자를 삭제하지 못했습니다.
#XMSG: Success message after deleting database user
deleteDatabaseUserSuccess=데이터베이스 사용자가 삭제되었습니다.
#XMSG: Success message after deleting database users
deleteDatabaseUsersSuccess=데이터베이스 사용자가 삭제되었습니다.
#XMSG: Success message after creating database user
createDatabaseUserSuccessMessage=데이터베이스 사용자가 생성되었습니다.
#YMSE: Failed to create database user
createDatabaseUserError=데이터베이스 사용자를 생성하지 못했습니다.
#XMSG: Success message after updating database user
updateDatabaseUserSuccessMessage=데이터베이스 사용자가 업데이트되었습니다.
#YMSE: Failed to update database user
updateDatabaseUserError=데이터베이스 사용자를 업데이트하지 못했습니다.
#XFLD: HDI Consumption
hdiConsumption=HDI 사용
#XTIT: Space detail section Database Access title
detailsSectionDatabaseAccess=데이터베이스 액세스
#XTXT: Database users consumption hint
databaseUsersConsumptionHint=기본적으로 이 공간의 데이터를 사용 가능하게 설정합니다. 빌더 내의 모델에서 데이터가 자동으로 사용 허용됩니다.
#XFLD: Default Consumption of Space Data:
defaultConsumptionOfSpaceData=공간 데이터의 기본 사용:
#XFLD: Database User Name
databaseUserName=데이터베이스 사용자 이름
#XMSG: Database User creation validation error message
databaseUserValidationError=일부 필드가 올바르지 않은 것 같습니다. 계속하려면 필수 필드를 점검하십시오.
#XMSG: SQL modeling can’t be enabled since this user has been migrated.
migratedDatabaseUserHint=이 사용자가 마이그레이션되었으므로 데이터 수집을 사용할 수 없습니다.
#XBUT: Remove Button Text
remove=제거
#XBUT: Remove Spaces Button Text
removeSpaces=공간 제거
#XBUT: Remove Objects Button Text
removeObjects=오브젝트 제거
#XMSG: No members have been added yet.
noMembersAssigned=아직 멤버가 추가되지 않았습니다.
#XMSG: No users have been added yet.
noUsersAssigned=아직 사용자가 추가되지 않았습니다.
#XMSG: No database users have been created or your filter is not showing any data.
noDatabaseUsers=데이터베이스 사용자가 생성되지 않았습니다. 또는 필터가 데이터를 표시하지 않습니다.
#XMSG: Please enter a user name.
noDatabaseUsername=사용자 이름을 입력하십시오.
#XMSG: The user name is too long. Please use a shorter one.
databaseUsernameTooLong=사용자 이름이 너무 깁니다. 더 짧은 이름을 사용하십시오.
#XMSG: No privileges have been enabled and this database user will have limited functionality. Do you still want to continue?
noPrivilegesConfirmationMessage=권한이 활성화되지 않아 이 데이터베이스 사용자의 기능이 제한됩니다. 계속하시겠습니까?
#XMSG: To enable audit logs for change operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogChangeMessage=변경 작업에 대한 감사 로그를 활성화하려면 데이터 수집도 활성화해야 합니다. 이렇게 하시겠습니까?
#XMSG: To enable audit logs for read operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogReadMessage=읽기 작업에 대한 감사 로그를 활성화하려면 데이터 수집도 활성화해야 합니다. 이렇게 하시겠습니까?
#XMSG: To enable HDI consumption data ingestion and data consumption needs to be enabled. Do you want to enable all?
enableHDIConsumptionMessage=HDI 사용을 활성화하려면 데이터 수집과 데이터 사용도 활성화해야 합니다. 이렇게 하시겠습니까?
#XMSG:
databaseUserPasswordText=이 데이터베이스 사용자에 대한 연결을 설정하려면 비밀번호를 복사하십시오. 비밀번호를 잊어버린 경우 언제든지 새로운 비밀번호를 요청할 수 있습니다.
#XTIT: Space detail section members title
detailsSectionMembers=멤버
#XMSG: New password set
newPasswordSet=신규 비밀번호 설정됨
#XFLD: Data Ingestion
dataIngestion=데이터 수집
#XFLD: Data Consumption
dataConsumption=데이터 사용
#XFLD: Privileges
privileges=권한
#XFLD: Enable Data ingestion
enableDataIngestion=데이터 수집 사용
#XMSG: Log the read and change operations for data ingestion.
databaseUserAuditHint=데이터 수집에 대한 읽기 및 변경 작업을 로그에 기록합니다.
#XMSG: Make your space data available in your HDI containers.
databaseUserHDIHint=HDI 컨테이너에서 공간 데이터 사용을 설정합니다.
#XFLD: Enable Data consumption
enableDataConsumption=데이터 소비 사용
#XMSG: Allow other apps or tools to consume your space data.
enableDataConsumptionHint=다른 앱이나 툴에서 공간 데이터의 사용을 허용합니다.
#XMSG: To set up access via this schema user copy the credentials to your user-provided service. In case you can only copy the credentials without a password make sure to add the password later.
databaseUserHDIConsumptionHint=이 데이터베이스 사용자를 통한 액세스를 설정하려면 사용자 제공 서비스에 자격 증명을 복사하십시오. 비밀번호 없는 자격 증명의 복사만 가능한 경우 나중에 반드시 비밀번호를 추가해야 합니다.
#XFLD: Data Flow Runtime Capacity label in space overview
dataFlowRuntimeCapacity=데이터 흐름 런타임 용량({0}:{1}시간/{2}시간)
#XFLD: Could not load Data Flow Runtime Capacity
dataFlowRuntimeCapacityError=데이터 흐름 런타임 용량을 로드할 수 없음
#XMSG: User can grant data consumption to other users.
databaseUserConsumptionWithGrantOptionHint=다른 사용자의 데이터 사용을 승인할 수 있습니다.
#XFLD: Enable data consumption with grant option
enableConsumptionWithGrantOption=승인 옵션으로 데이터 소비 사용
#XMSG: To enable data consumption with grant option data consumption needs to be enabled. Do you want to enable both?
enableConsumptionWithGrantMessage=승인 옵션으로 데이터 소비를 사용하도록 설정하려면 데이터 사용을 활성화해야 합니다. 둘 다 활성화하시겠습니까?
#XFLD: Enable Automated Predictive Library (APL) and Predictive Analysis Library (PAL)
allowUsingScriptServerFunctionality=Automated Predictive Library(APL)과 Predictive Analysis Library(PAL) 사용
#XMSG: User can use SAP HANA Cloud embedded machine learning functions.
allowUsingScriptServerFunctionalityHint=사용자는 SAP HANA Cloud embedded machine learning 기능을 사용할 수 있습니다.
#XFLD: Password Policy
passwordPolicy=비밀번호 정책
#XMSG: Password Policy
passwordPolicyHint=구성된 비밀번호 정책을 여기에서 사용 또는 사용 안 으로 설정하십시오.
#XFLD: Enable Password Policy
enablePasswordPolicy=비밀번호 정책 사용
#XMSG: Read Access to the Space Schema
readAccessTitle=공간 스키마에 대한 읽기 액세스
#XMSG: read access hint
readAccessHint=데이터베이스 사용자가 공간 스키마에 외부 툴을 연결하고, 사용을 위해 표시된 뷰를 읽을 수 있도록 허용합니다.
#XFLD: Space Schema
spaceSchema=공간 스키마
#XFLD: Enable Read Access (SQL)
enableReadAccess=읽기 액세스 사용(SQL)
#XMSG: Allow the user to grant read access to other users.
withGrantOptionHint=사용자가 다른 사용자에게 읽기 권한을 승인하도록 허용합니다.
#XFLD: With Grant Option
withGrantOption=승인 옵션 포함
#XMSG: Make your space data available in your HDI containers.
enableHDIConsumptionHint=HDI 컨테이너에서 공간 데이터를 사용하도록 허용합니다.
#XFLD: Enable HDI Consumption
enableHDIConsumption=HDI 사용 활성화
#XMSG: Write Access to the User’s Open SQL Schema
writeAccessTitle=사용자의 Open SQL 스키마에 대한 쓰기 액세스
#XMSG: write access hint
writeAccessHint=데이터베이스 사용자가 사용자의 Open SQL 스키마에 외부 툴을 연결하고, 데이터 엔티티를 생성하며 공간에서 사용할 데이터를 수집하도록 허용합니다.
#XFLD: Open SQL Schema
openSQLSchema=Open SQL 스키마
#XFLD: Enable Write Access (SQL DDL & DML)
enableWriteAccess=쓰기 액세스 사용(SQL, DDL 및 DML)
#XMSG: audit hint
auditHint=Open SQL 스키마에서의 읽기 및 변경 작업을 기록합니다.
#XMSG: data consumption hint
dataConsumptionHint=이 공간의 모든 신규 뷰를 기본적으로 사용을 위해 표시합니다. 모델러는 뷰 출력 사이드 패널의 "사용을 위해 표시" 스위치를 통해 개별 뷰에 대한 이 설정을 재정의할 수 있습니다. 뷰가 표시되는 형식도 선택할 수 있습니다.
#XFLD: Expose for Consumption by Default
exposeDataConsumption=기본적으로 사용을 위해 표시
#XMSG: database users hint consumption hint
databaseUsersHint2New=SAP Datasphere에 외부 툴을 연결할 데이터베이스 사용자를 생성합니다. 사용자가 공간 데이터를 읽고 데이터 엔티티(DDL)를 생성하며 이 공간에서 사용할 데이터(DML)를 수집할 수 있도록 권한을 설정합니다.
#XFLD: Read
read=읽기
#XFLD: Read (HDI)
readHDI=읽기(HDI)
#XFLD: Write
write=쓰기
#XMSG: HDI Containers Hint
HDIContainersHint2=공간에서 SAP HANA Deployment Infrastructure(HDI) 컨테이너에 액세스할 수 있습니다. 모델러는 뷰 소스로 HDI 아티팩트를 사용할 수 있으며 HDI 클라이언트는 사용자의 공간 데이터에 액세스할 수 있습니다.
#XMSG: Open info dialog
openDatabaseUserInfoDialog=정보 대화 상자 열기
#XMSG: Database user is locked. Open info dialog to unlock
openLockedDatabaseUserInfoDialog=데이터베이스 사용자가 잠겨 있습니다. 잠금을 해제하려면 대화 상자를 여십시오.
#XFLD: Table
table=테이블
#XFLD: Space section partner configuration title
partnerConnectionConfigurationSectionTitle=파트너 연결
#XFLD: Space section partner configuration subtitle
partnerConnectionConfigurationSectionSubTitle=파트너 연결 구성
#XFLD: Space section partner configuration hint
partnerConnectionConfigurationSectionHint=iFrame URL 및 아이콘을 추가하여 자체 파트너 연결 타일을 정의합니다. 이 구성은 이 테넌트에만 사용할 수 있습니다.
#XFLD: Table Name Field
partnerConnectionConfigurationName=타일 이름
#XFLD: Table iFrame URL field
partnerConnectionConfigurationiFrameURL=iFrame URL
#XFLD: Table iFrame Post Message Origin field
partnerConnectionConfigurationiFramePostMessageOrigin=iFrame postMessage 출처
#XFLD: Table icon/image name
partnerConnectionConfigurationIcon=아이콘
#XMSG: Table no result found
partnerConnectionConfigurationNotfound=파트너 연결 구성을 찾을 수 없습니다.
#XMSG: Partner Connection Confgurations are not available
partnerConnectionConfigurationNotAvailable=런타임 데이터베이스를 사용할 수 없을 때는 파트너 연결 구성을 조회할 수 없습니다.
#XFLD: Dialog Create Partner Connection Configuration
partnerConnectionConfigurationDialogTitleAdd=파트너 연결 구성 생성
#XFLD: Dialog Create Partner Connection Configuration Upload Icon
partnerConnectionConfigurationButtonUloadPartnerIcon=아이콘 업로드
#XFLD: Dialog Create Partner Connection Configuration Select
partnerConnectionConfigurationSelectIcon=선택(최대 크기 200KB)
#XFLD: Dialog Create Partner Connection Configuration Enter a name
partnerConnectionConfigurationNamePlaceholder=파트너 타일 예시
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL
partnerConnectionConfigurationiFrameURLPlaceholder=http://www.example.com
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL Check
partnerConnectionConfigurationiFramePostMessageOriginPlaceholder=.example.com
#XFLD: Dialog Create Partner Connection Configuration Browse
partnerConnectionConfigurationBrowseButton=찾아보기
#XFLD: Create Partner Connection Configuration Msg
partnerConnectionConfigurationUploadMsg=파트너 연결 구성이 생성되었습니다.
#XMSG: Error while deleting Partner Connection configuration(s)
partnerConnectionConfigurationDeleteError=파트너 연결 구성을 삭제하는 동안 오류가 발생했습니다.
#XMSG: Partner Connection Configuration has been successfully deleted
partnerConnectionConfigurationDeleteSuccess=파트너 연결 구성이 삭제되었습니다.
#XMSG: Error while retrieving Partner Connection Configurations
partnerConnectionConfigurationLoadError=파트너 연결 구성을 검색하는 동안 오류가 발생했습니다.
#XMSG: Error fileSizeLimitExceeded
partnerConnectionConfigurationfileSizeLimitExceeded=최대 크기 200KB가 초과되어 파일을 업로드할 수 없습니다.
#XBUT: Create Partner Connection Configuration
partnerConnectionConfigurationButtonCreate=파트너 연결 구성 생성
#XBUT: Delete Partner Connection Configuration
partnerConnectionConfigurationButtonDelete=파트너 연결 구성 삭제
#XMSG: Partner Tile Creation Failure
upsertPartnerTileFailed=파트너 타일을 생성할 수 없습니다.
#XMSG: Partner Tile Deletion Failure
deletePartnerTileFailed=파트너 타일을 삭제할 수 없습니다.
#XMSG: HANA cloud connector settings reset failure
resetHANASCCSettingsFailed=고객 SAP HANA Cloud Connector 설정을 재설정하지 못했습니다.
#XFLD: Workload Class
workloadClass=작업 부하 클래스
#XFLD: Workload Management
workloadManagement=작업 부하 관리
#XFLD: Priority
workloadClassPriority=우선순위
#XMSG:
workloadManagementPriorityHint=데이터베이스에 쿼리를 실행할 때 이 공간의 우선순위를 지정할 수 있습니다. 1(최저)에서 8(최고) 사이의 값을 입력합니다. 사용 가능한 스레드를 놓고 공간이 서로 경쟁하는 상황일 때 우선순위가 높은 공간이 우선순위가 낮은 공간보다 먼저 실행됩니다.
#XMSG:
workloadClassPriorityHint=0(최저)부터 8(최고)까지 공간 우선순위를 지정할 수 있습니다. 우선순위가 높은 공간의 명령문이 우선순위가 낮은 다른 공간의 명령문보다 먼저 실행됩니다. 기본 우선순위는 5입니다. 값 9는 시스템 작업에 예약되어 있으므로 공간에 사용할 수 없습니다.
#XFLD: Statement Limits
workloadclassStatementLimits=명령문 한도
#XFLD: Workload Configuration
workloadConfiguration=작업 부하 구성
#XMSG:
workloadClassStatementLimitsHint=공간에서 동시에 실행되는 명령문에서 소비할 수 있는 메모리의 크기(GB)나 스레드의 최대 수(또는 비율)를 지정할 수 있습니다. 0(무제한)과 테넌트에서 사용 가능한 최대 메모리 및 스레드 사이의 임의 값 또는 비율을 입력할 수 있습니다.\n\n 스레드 한도를 지정하면 성능이 저하될 수 있습니다. \n\n 메모리 한도를 지정하면 메모리 한도에 도달하는 명령문은 실행되지 않습니다.
#XMSG:
workloadClassStatementLimitsDescription=단일 공간으로 시스템 과부하가 발생하지 않도록 기본 구성의 리소스 한도는 여유있게 제공됩니다.
#XMSG:
workloadClassStatementLimitCustomDescription=현재 공간에서 실행되는 명령문이 사용할 수 있는 최대 총 스레드 및 메모리 한도를 설정할 수 있습니다.
#XMSG:
totalStatementThreadLimitHelpText=스레드 한도를 너무 낮게 설정하면 명령문 성능에 영향을 미칠 수 있으며, 값이 너무 높거나 0이면 사용 가능한 모든 시스템 스레드가 공간에 사용되도록 허용될 수 있습니다.
#XMSG:
totalStatementMemoryLimitHelpText=메모리 한도를 너무 낮게 설정하면 메모리 부족 문제가 발생할 수 있으며, 값이 너무 높거나 0이면 사용 가능한 시스템 메모리가 공간에 사용되도록 허용될 수 있습니다.
#XMSG:
totalStatementThreadLimitHelpTextRestricted=테넌트에서 사용 가능한 총 스레드 수의 1%에서 70%(또는 이에 상응하는 숫자) 사이의 백분율을 입력합니다. 스레드 제한을 너무 낮게 설정하면 명령문 성능에 영향을 줄 수 있고, 값을 너무 높게 설정하면 다른 공간의 명령문 성능에 영향을 미칠 수 있습니다.
#XMSG:
totalStatementThreadLimitHelpTextDynamic=테넌트에서 사용 가능한 총 스레드 수의 1%에서 {0}%(또는 이에 상응하는 숫자) 사이의 백분율을 입력합니다. 스레드 제한을 너무 낮게 설정하면 명령문 성능에 영향을 줄 수 있고, 값을 너무 높게 설정하면 다른 공간의 명령문 성능에 영향을 미칠 수 있습니다.
#XMSG:
totalStatementMemoryLimitHelpTextNew=0(제한 없음)과 테넌트에서 가용 메모리 총량 사이의 값 또는 백분율을 입력합니다. 메모리 제한을 너무 낮게 설정하면 명령문 성능에 영향을 줄 수 있고, 값을 너무 높게 설정하면 다른 공간의 명령문 성능에 영향을 미칠 수 있습니다.
#XFLD: Total Statement Thread Limit
totalStatementThreadLimit=명령문 스레드 한도 총계
#XFLD: Segmented button label
workloadclassPercentage=%
#XFLD: Segmented button label
workloadclassGigaByte=GB
#XFLD, 80: Segmented button label
workloadclassThreads=스레드
#XFLD: Total Statement Memory Limit
totalStatementMemoryLimit=명령문 메모리 한도 총계
#YMSE: Error while loading customer hana info
loadCustomerHanaInfoError=고객 SAP HANA 정보를 로드하지 못했습니다.
#XMSG:
minimumLimitReached=최소 한도에 도달했습니다.
#XMSG:
maximumLimitReached=최대 한도에 도달했습니다.
#XMSG: Name Taken for Technical Name
technical-name-taken=입력한 기술적 이름이 있는 연결이 이미 있습니다. 다른 이름을 입력하십시오.
#XMSG: Name Too long for Technical Name
technical-name-too-long=입력한 기술적 이름은 40자를 초과합니다. 이름을 40자 미만으로 입력하십시오.
#XMSG: Technical name field empty
technical-name-field-empty=기술적 이름을 입력하십시오.
#XMSG: Generic error when user enters invalid characters for a field
field-invalid-characters=이름에는 문자(a-z), 숫자(0-9), 밑줄(_)만 사용할 수 있습니다.
#XMSG: Generic error when user enters invalid initial characters for a field
field-underscore-trim=입력한 이름은 밑줄(_)로 시작하거나 끝나지 않아야 합니다.
#XFLD: Checkbox label for Enable Statement Limits
enableStatementLimits=명령문 한도 사용
#XFLD: Connection SubSection Settings Title in space details
connectionSettings=설정
#XMSG: Connections tool hint in Space details section
connectionsToolHint=연결을 생성하거나 편집하려면 측면 탐색에서 연결 앱을 열거나 여기를 클릭합니다.
#XBUT: Connection list Open Connection Tool Button
button-connectiontool=연결로 이동
#XFLD: Not deployed label on space tile
notDeployedLabel=공간이 아직 배포되지 않았습니다.
#XFLD: Not deployed additional text on space tile
notDeployedText=공간을 배포하십시오.
#XFLD: Corrupt space label on space tile
corruptSpace=오류가 발생했습니다.
#XFLD: Corrupt space additional text on space tile
corruptSpaceHint=다시 배포하거나 지원 부서에 문의하십시오.
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=감사 로그 데이터
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=관리 데이터
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=기타 데이터
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=공간 데이터
#XMSG: Unlock Space confirmation text
unlockSpaceConfirmation=공간 잠금을 해제하시겠습니까?
#XMSG: Lock Space confirmation text
lockSpaceConfirmation=공간을 잠그시겠습니까?
#XFLD: Lock
lock=잠금
#XFLD: Unlock
unlock=잠금 해제
#XFLD: Locking
locking=잠금
#XMSG: Success message after locking space
lockSpaceSuccessMsg=공간이 잠겼습니다.
#XMSG: Success message after unlocking space
unlockSpaceSuccessMsg=공간 잠금이 해제되었습니다.
#XMSG: Success message after locking spaces
lockSpacesSuccessMsg=공간이 잠겼습니다.
#XMSG: Success message after unlocking spaces
unlockSpacesSuccessMsg=공간 잠금이 해제되었습니다.
#YMSE: Error while locking a space
lockSpaceError=공간을 잠글 수 없습니다.
#YMSE: Error while unlocking a space
unlockSpaceError=공간 잠금을 해제할 수 없습니다.
#XTIT: popup title Warning
confirmationWarningTitle=경고
#XMSG: Message strip message when a space was locked manually
manualLockedMessage=공간이 수동으로 잠겨 있습니다.
#XMSG: Message strip message when a space was locked due to the amount of generated audit logs
auditLogLockedMessage=감사 로그가 많은 양의 GB 디스크를 사용하기 때문에 공간이 시스템에 의해 잠겼습니다.
#XMSG: Message strip message when a space was locked because the assigned quota was exceeded.
quotaLockedMessage=메모리 또는 디스크 저장소 할당을 초과하여 공간이 시스템에 의해 잠겼습니다.
#XMSG: Unlock Spaces confirmation text
unlockSpacesConfirmation=선택한 공간의 잠금을 해제하시겠습니까?
#XMSG: Lock Spaces confirmation text
lockSpacesConfirmation=선택한 공간을 잠그시겠습니까?
#XTIT: Scoped role editor dialog title
scopedRoleEditorDialogTitle=범위 지정된 역할 편집기
#XTIT: ECN Management title
ecnManagementTitle=공간 및 탄력적 계산 노드 관리
#XFLD: ECNs
ecns=탄력적 계산 노드
#XFLD: ECN phase Ready
ecnReady=준비됨
#XFLD: ECN phase Running
ecnRunning=실행 중
#XFLD: ECN phase Initial
ecnInitial=준비 중
#XFLD: ECN phase Starting
ecnStarting=시작하는 중
#XFLD: ECN phase Starting Failed
ecnStartingFailed=시작하지 못함
#XFLD: ECN phase Stopping
ecnStopping=중지 중
#XFLD: ECN phase Stopping Failed
ecnStoppingFailed=중지하지 못함
#XBTN: Assign Button
assign=공간 지정
#XBTN: Start Header-Button
start=시작
#XBTN: Update Header-Button
repair=업데이트
#XBTN: Stop Header-Button
stop=중지
#XFLD: ECN hours remaining
ecnHoursRemaining=1000시간 남음
#XFLD: ECN block-hours remaining
ecnBlockHoursRemaining={0}블록 시간 남음
#XFLD: ECN block-hour remaining
ecnBlockHourRemaining={0}블록 시간 남음
#XTIT: ECN creation dialog title
ecnCreationDialogTitle=탄력적 계산 노드 생성
#XTIT: ECN edit dialog title
ecnEditDialogTitle=탄력적 계산 노드 편집
#XTIT: ECN deletion dialog title
ecnDeletionDialogTitle=탄력적 계산 노드 삭제
#XTIT: ECN Space assignment dialog title
ecnSpaceAssignmentDialogTitle=공간 지정
#XFLD: ECN ID
ECNIDLabel=탄력적 계산 노드
#XTXT: Selected toolbar text
selectedToolbarText=선택됨: {0}
#XTIT: Elastic Compute Nodes
ECNslong=탄력적 계산 노드
#XTIT: Object assignment - Table header: Objects available
objectsAvailable=오브젝트 수
#XTIT: Object assignment - Dialog header text
selectObjects=탄력적 계산 노드에 지정할 공간 및 오브젝트를 선택하십시오.
#XTIT: Object assignment - Table header title: Objects
objects=오브젝트
#XTIT: Object assignment - Table header: Type
type=유형
#XMSG: Message strip delete database user audit warning
deleteDatabaseUserWarningMessageStrip=데이터베이스 사용자를 삭제하면 생성된 모든 감사 로그 엔트리가 삭제됩니다. 감사 로그를 그대로 유지하려면 데이터베이스 사용자를 삭제하기 전에 엔트리를 엑스포트하는 것을 고려해 보십시오.
#XMSG: Message strip delete HDI container audit logs impact
deleteHDIContainerWarningMessageStrip=공간에 지정된 HDI 컨테이너를 해제하면 생성된 모든 감사 로그 엔트리가 삭제됩니다. 감사 로그를 그대로 유지하려면 HDI 컨테이너 지정을 해제하기 전에 엔트리를 엑스포트하는 것을 고려해 보십시오.
#XTXT: All audit logs
allAuditLogs=공간에 생성된 모든 감사 로그 엔트리
#XMSG: Message strip disable audit warning
disableAuditWarningMessageStrip=감사 정책(읽기 또는 변경 작업)을 비활성화하면 모든 감사 로그 엔트리가 삭제됩니다. 감사 로그 엔트리를 그대로 유지하려면 감사 정책을 비활성화하기 전에 엔트리를 엑스포트하는 것을 고려해 보십시오.
#XTIT: No Spaces assigned to ECN Illustration title
noSpacesECNTitle=공간 또는 오브젝트가 아직 지정되지 않음
#XTIT: No Spaces assigned to ECN Illustration description
noSpacesECNDescription=탄력적 계산 노드 사용을 시작하려면 공간 또는 오브젝트를 추가하십시오.
#XTIT: No Spaces Illustration title
noSpacesTitle=아직 공간이 생성되지 않음
#XTIT: No Spaces Illustration description
noSpacesDescription=데이터 수집을 시작하려면 공간을 생성하십시오.
#XTIT: No Spaces Illustration title
noDeletedSpacesTitle=휴지통이 비어 있음
#XTIT: No Spaces Illustration description
noDeletedSpacesDescription=여기에서 삭제된 공간을 복구할 수 있습니다.
#XTXT:  {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDatabaseUsers=공간이 배포되면 다음 데이터베이스 사용자는 {0} 삭제되고 복구될 수 없음:
#XTIT: Delete database users
deleteDatabaseUsersTitle=데이터베이스 사용자 삭제
#YMSE: Error if ecn id already exists
ecnIDInputDuplicateError=ID가 이미 있습니다.
#YMSE: Error if ecn id contains invalid characters
ecnIDInputInvalidError=소문자 a - z와 숫자 0 - 9만 사용하십시오.
#YMSE: Error if ecn id is too short
ecnIDInputTooShortError=ID 길이는 {0}자 이상이어야 합니다.
#XMSG: ecn id length warning
ecnIdLengthWarning=최대 문자 수인 {0}자를 초과했습니다.
#XFLD: open System Monitor
systemMonitor=시스템 모니터
#XFLD: open ECN schedule dialog menu entry
schedule=일정
#XFLD: open create ECN schedule dialog
createSchedule=일정 생성
#XFLD: open change ECN schedule dialog
changeSchedule=일정 편집
#XFLD: open delete ECN schedule dialog
deleteSchedule=일정 삭제
#XFLD: open self assign ECN schedule dialog
selfAssignSchedule=나에게 일정 지정
#XFLD: open pause ECN schedule dialog
pauseSchedule=일정 일시 중지
#XFLD: open resume ECN schedule dialog
resumeSchedule=일정 다시 시작
#XFLD: View Logs
viewLogs=로그 보기
#XFLD: Compute Blocks
computeBlocks=컴퓨팅 블록
#XFLD: Memory label in ECN creation dialog
ecnMemory=메모리(GB)
#XFLD: Storage label in ECN creation dialog
ecnStorage=저장소(GB)
#XFLD: Number of CPU label in ECN creation dialog
ecnCPUCount=CPU 수
#XFLD: ECN updated by label
changedBy=변경자
#XFLD: ECN updated on label
changedOn=변경일
#XMSG: Success message after creating a Elastic Compute Node
createEcnSuccessMessage=탄력적 계산 노드가 생성됨
#YMSE: Error while creating a Elastic Compute Node
createEcnError=탄력적 계산 노드를 생성할 수 없음
#XMSG: Success message after updating a Elastic Compute Node
updateEcnSuccessMessage=탄력적 계산 노드가 업데이트됨
#XMSG: Error while updating a Elastic Compute Node
udpateEcnError=탄력적 계산 노드를 업데이트할 수 없음
#XMSG: Success message after deleting a Elastic Compute Node
deleteEcnSuccessMessage=탄력적 계산 노드가 삭제됨
#YMSE: Error while deleting a Elastic Compute Node
deleteEcnError=탄력적 계산 노드를 삭제할 수 없음
#XMSG: Success message after starting a Elastic Compute Node
startingEcnSuccessMessage=탄력적 계산 노드를 시작하는 중
#XMSG: Success message after stopping a Elastic Compute Node
stoppingEcnSuccessMessage=탄력적 계산 노드를 중지하는 중
#YMSE: Error while starting a Elastic Compute Node
startingEcnError=탄력적 계산 노드를 시작할 수 없음
#YMSE: Error while stopping a Elastic Compute Node
stoppingEcnError=탄력적 계산 노드를 중지할 수 없음
#XBUT: Add Object button for an ECN
assignObjects=오브젝트 추가
#XFLD: Enable auto object assignment label
enableAutoObjAssignment=모든 오브젝트 자동 추가
#XFLD: object type label to be assigned
objectTypeLabel=유형(시맨틱 사용)
#XFLD: assigned object type label
assignedObjectTypeLabel=유형
#XFLD: technical name label
TechnicalNameLabel=기술적 이름
#XFLD: Object selecttion ECN label
objectSelectionECNLabel=탄력적 계산 노드에 추가할 오브젝트를 선택하십시오.
#XTIT: Add objects dialog title
assignObjectsTitle=다음 항목의 오브젝트 지정
#XFLD: object label with object count
objectLabel=오브젝트
#XMSG: No objects available to add message.
noObjectsToAssign=지정할 오브젝트가 없습니다.
#XMSG: No objects assigned message.
noAssignedObjects=오브젝트가 지정되지 않았습니다.
#XTIT: Remove objects popup title
removeObjectsOrSpacesConfirmationTitle=경고
#XTIT: Delete ECN popup button and title
deleteECNConfirmationButtonAndTitle=삭제
#XMSG: Remove objects popup text
removeObjectsConfirmation=선택한 오브젝트를 제거하시겠습니까?
#XMSG: Remove spaces popup text
removeSpacesConfirmation=선택한 공간을 제거하시겠습니까?
#XBUT: Confirmation button for objects dialog
removeObjectsOrSpacesConfirmationBtn=공간 제거
#XMSG: Success message after removing objects
removeObjectsSuccessMessage=표시된 오브젝트가 제거되었습니다.
#XMSG: Success message after assigning objects
assignObjectsSuccessMessage=표시된 오브젝트가 지정되었습니다.
#XFLD: auto object assignment label for space tile
autoObjAssignmentLabel=모든 표시된 오브젝트
#XFLD: Spaces tab label
spacesTabLabel=공간
#XFLD: Exposed objects tab label
consumptionObjectsTabLabel=표시된 오브젝트
#XMSG: Success message after unassigning spaces
spacesUnassignedSuccessMessage=공간이 제거되었습니다.
#XMSG: Success message after unassigning space
spaceUnassignedSuccessMessage=공간이 제거되었습니다.
#YMSE: Error while assigning / unassigning spaces
spacesAssignUnassignError=공간을 지정하거나 제거할 수 없습니다.
#YMSE: Error while removing objects
removeObjectsError=오브젝트를 지정하거나 제거할 수 없습니다.
#YMSE: Error while removing object
removeObjectError=오브젝트를 지정하거나 제거할 수 없습니다.
#YMSE: Error when saving the ecn with invalid value
invalidSizingPlanError=이전에 선택한 숫자가 더 이상 유효하지 않습니다. 유효한 숫자를 선택하십시오.
#YMSE: Error when saving the ecn with invalid value
invalidPerformanceClassError=유효한 성능 클래스를 선택하십시오.
#YMSE: Error when saving the ecn with invalid value
performanceClassNotAvailableError=이전에 선택한 성능 클래스 "{0}"은(는) 현재 유효하지 않습니다. 유효한 성능 클래스를 선택하십시오.
#XTXT: Delete ECN confirmation text
deleteConfirmationTxtECN=탄력적 계산 노드를 삭제하시겠습니까?
#XFLD: tooltip for ? button
help=도움말
#XFLD: ECN edit button label
editECN=구성
#XFLD: Technical type label for ERModel
DWC_ERMODEL=엔티티 - 관계 모델
#XFLD: Technical type label for Local Table
DWC_LOCAL_TABLE=로컬 테이블
#XFLD: Technical type label for Remote Table
DWC_REMOTE_TABLE=원격 테이블
#XFLD: Technical type label for Analytic Model
DWC_ANALYTIC_MODEL=분석 모델
#XFLD: Technical type label for Task Chain
DWC_TASKCHAIN=태스크 체인
#XFLD: Technical type label for Data Flow
DWC_DATAFLOW=데이터 흐름
#XFLD: Technical type label for Replication Flow
DWC_REPLICATIONFLOW=복제 흐름
#XFLD: Technical type label for Transformation Flow
DWC_TRANSFORMATIONFLOW=변환 흐름
#XFLD: Technical type label for Intelligent Lookup
DWC_IDT=인텔리전트 조회
#XFLD: Technical type label for Repository
DWC_REPOSITORY_PACKAGE=저장소
#XFLD: Technical type label for Enterprise Search
DWC_ESMODEL=Enterprise Search
#XFLD: Technical type label for View
DWC_VIEW=뷰
#XFLD: Technical type label for Data Product
DWC_DATAPRODUCT=데이터 제품
#XFLD: Technical type label for Data Access Control
DWC_DAC=데이터 액세스 제어
#XFLD: Technical type label for Folder
DWC_FOLDER=폴더
#XFLD: Technical type label for Data Entity
DWC_BUSINESS_ENTITY=비즈니스 엔티티
#XFLD: Technical type label for Business Entity Variant
DWC_BUSINESS_ENTITY_VARIANT=비즈니스 엔티티 변형
#XFLD: Technical type label for Responsibility Scenario
DWC_AUTH_SCENARIO=책임 시나리오
#XFLD: Technical type label for Fact Model
DWC_FACT_MODEL=팩트 모델
#XFLD: Technical type label for Transformation Flow
DWC_PERSPECTIVE=퍼스펙티브
#XFLD: Technical type label for Consumption Model
DWC_CONSUMPTION_MODEL=사용 모델
#XFLD: Technical type label for Remote Connection
DWC_REMOTECONNECTION=원격 연결
#XFLD: Technical type label for Fact Model Variant
DWC_FACT_MODEL_VARIANT=팩트 모델 변형
#XMSG: Schedule created alert message
createScheduleSuccess=일정이 생성되었습니다.
#XMSG: Schedule updated alert message
updateScheduleSuccess=일정이 업데이트되었습니다.
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=일정이 삭제되었습니다.
#XMSG: Schedule takeover success message
ownerChangeScheduleSuccess=일정이 나에게 지정되었습니다.
#XMSG: Message for starting pausing of schedule
pauseScheduleStarted=1개의 일정을 일시 중지하는 중
#XMSG: Message for starting resuming of schedule
resumeScheduleStarted=1개의 일정을 다시 시작하는 중
#XFLD: Segmented button label
availableSpacesButton=사용 가능
#XFLD: Segmented button label
selectedSpacesButton=선택됨
#XFLD: Visit website button text
visitWebsite=웹사이트 방문
#XMSG: Warning message on unselecting source language
sourceLanguageRemovalWarning=이전에 선택한 소스 언어가 제거됩니다.
#XBUT: enable translation confirmation button
enableTranslationConfirmationBtn=사용
#XFLD: ECN performance class label
performanceClassLabel=성능 클래스
#XTXT performance class memory text
memoryText=메모리
#XTXT performance class compute text
computeText=계산
#XTXT performance class high-compute text
highComputeText=고성능 컴퓨팅
#XBUT: Recycle Bin Button Text
recycleBin=휴지통
#XBUT: Restore Button Text
restore=복원
#XMSG: Warning message for new Workload Management UI
priorityWarning=이 영역은 읽기 전용 영역입니다. 시스템/구성/작업 부하 관리 영역에서 공간 우선순위를 변경할 수 있습니다.
#XMSG: Warning message for new Workload Management UI
workloadWarning=이 영역은 읽기 전용 영역입니다. 시스템/구성/작업 부하 관리 영역에서 공간 작업 부하 구성을 변경할 수 있습니다.
#XCOL: Space table-view column: Apache Spark vCPUs
sparkManagementVCPUs=Apache Spark vCPU
#XCOL: Space table-view column: Apache Spark Memory (GB)
sparkManagementMemory=Apache Spark 메모리(GB)
#XCOL: Space type (from Space Capability) for Data Ingestion Spaces
ingestionSpaceType=데이터 제품 수집
#XFLD: Deployment in progress nodatatext for spark table
sparkTableDeploymentInProgress=공간이 현재 배포되고 있으므로 데이터를 사용할 수 없습니다.
#XFLD: Runtime data being loaded nodatatext for spark table
sparkTableDeploymentLoading=공간이 현재 로드되고 있으므로 데이터를 사용할 수 없습니다.
#XCOL: Space type (from Space Capability) for BW Private Cloud Edition
BWPCESpaceType=SAP BW
#XBUT: Go to Instance Mappings Button
navToMappingsButton=인스턴스 매핑 편집
#XFLD: SAP HANA Data Lake Storage label if less than 1GB
dataLakeStorageUsed=< 1GB
