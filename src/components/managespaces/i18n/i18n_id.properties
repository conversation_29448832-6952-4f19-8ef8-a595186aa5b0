#DO NOT REMOVE________________________
#XFLD: Breadcrumb for space monitoring page
spaceMonitorBreadcrumb=Pemantauan
#XTXT: Type name for spaces in browser tab page title
space=Ruang
#_____________________________________
#XFLD: Spaces label in
spaces=Ruang
#XFLD: Manage plan button text
manageQuotaButtonText=Kelola Rencana
#XBUT: Manage resources button
manageResourcesButton=Kelola Sumber Daya
#XFLD: Create space button tooltip
createSpace=Buat Ruang
#XFLD: Create
create=Buat
#XFLD: Deploy
deploy=Sebarkan
#XFLD: Page
page=Halaman
#XFLD: Cancel
cancel=Batalkan
#XFLD: Update
update=Perbarui
#XFLD: Save
save=Simpan
#XFLD: OK
ok=OKE
#XFLD: days
days=Hari
#XFLD: Space tile edit button label
edit=Edit
#XFLD: Auto Assign all objects to space
autoAssign=Otomatis Tetapkan
#XFLD: Space tile open monitoring button label
openMonitoring=Pantau
#XFLD: Delete
delete=Hapus Permanen
#XFLD: Copy Space
copy=Salin
#XFLD: Close
close=Tutup
#XCOL: Space table-view column status
status=Status
#XFLD: Space status active
activeLabel=Aktif
#XFLD: Space status locked
lockedLabel=Terkunci
#XFLD: Space status critical
criticalLabel=Kritis
#XFLD: Space status cold
coldLabel=Aman
#XFLD: Space status deleted
deletedLabel=Dihapus Permanen
#XFLD: Space status unknown
unknownLabel=Tidak Diketahui
#XFLD: Space status ok
okLabel=Sesuai
#XFLD: Database user expired
expired=Kedaluwarsa
#XFLD: deployed
deployed=Disebarkan
#XFLD: not deployed
notDeployed=Tidak Disebarkan
#XFLD: changes to deploy
changesToDeploy=Perubahan untuk Disebarkan
#XFLD: pending
pending=Menyebarkan
#XFLD: designtime error
designtimeError=Kesalahan Waktu Rancangan
#XFLD: runtime error
runtimeError=Kesalahan Run-Time
#XFLD: Space created by label
createdBy=Dibuat Oleh
#XFLD: Space created on label
createdOn=Dibuat Pada
#XFLD: Space deployed on label
deployedOn=Disebarkan Pada
#XFLD: Space ID label
spaceID=ID Ruang
#XFLD: Priority label
priority=Prioritas
#XFLD: Space Priority label
spacePriority=Prioritas Ruang
#XFLD: Space Configuration label
spaceConfiguration=Konfigurasi Ruang
#XFLD: Not available
notAvailable=Tidak Tersedia
#XFLD: WorkloadType default
default=Default
#XFLD: WorkloadType custom
custom=Kustom
#XFLD: Data Lake option label; refers to component SAP HANA Cloud, Data Lake
dataLakeAccess=Akses Data Lake
#XFLD: Translation label
translationLabel=Terjemahan
#XFLD: Source language label
sourceLanguageLabel=Bahasa Sumber
#XFLD: Translation CheckBox label
translationCheckBox=Aktifkan Terjemahan
#XTXT: Popover hint that space has to be deployed to show user details
openDatabaseUserHintPopoverText=Sebarkan ruang untuk mengakses detail pengguna.
#XTXT: Popover hint that space has to be deployed to open database explorer
openDatabaseExplorerHintPopoverText=Sebarkan ruang untuk membuka Penjelajah Basis Data.
#XTXT: Text for when the Data Lake check box is disabled
dataLakeOptionDisabled=Anda tidak dapat menggunakan ruang ini untuk mengakses Data Lake karena telah digunakan oleh ruang lain.
#XFLD: Data Lake check box text; refers to component SAP HANA Cloud, Data Lake
dataLakeCheckBox=Gunakan ruang ini untuk mengakses data lake.
#XFLD: Space Priority minimum label extension
low=Rendah
#XFLD: Space Priority maximum label extension
high=Tinggi
#XFLD: Space name label
spaceName=Nama Ruang
#XFLD: Enable deploy objects checkbox
enableDeployObjects=Sebarkan Objek
#XTIT: Copy spaces dialog title
copySpaceDialogTitle=Salin {0}
#XTXT Human readable text for unselected source language
unSelectedSourceLanguage=(Tidak Dipilih)
#XTXT Human readable text for language code "af"
af=Bahasa Afrika
#XTXT Human readable text for language code "ar"
ar=Bahasa Arab
#XTXT Human readable text for language code "bg"
bg=Bulgaria
#XTXT Human readable text for language code "ca"
ca=Katala
#XTXT Human readable text for language code "zh"
zh=Bahasa Tionghoa Aksara Sederhana
#XTXT Human readable text for language code "zf"
zf=Bahasa Tionghoa
#XTXT Human readable text for language code "hr"
hr=Kroasia
#XTXT Human readable text for language code "cs"
cs=Cheska
#XTXT Human readable text for language code "cy"
cy=Welsh
#XTXT Human readable text for language code "da"
da=Dansk
#XTXT Human readable text for language code "nl"
nl=Belanda
#XTXT Human readable text for language code "en-UK"
en-UK=Bahasa Inggris (Inggris)
#XTXT Human readable text for language code "en"
en=Bahasa Inggris (Amerika Serikat)
#XTXT Human readable text for language code "et"
et=Bahasa Estonia
#XTXT Human readable text for language code "fa"
fa=Bahasa Persia
#XTXT Human readable text for language code "fi"
fi=Suomi
#XTXT Human readable text for language code "fr-CA"
fr-CA=Prancis (Kanada)
#XTXT Human readable text for language code "fr"
fr=Prancis
#XTXT Human readable text for language code "de"
de=Jerman
#XTXT Human readable text for language code "el"
el=Yunani
#XTXT Human readable text for language code "he"
he=Bahasa Ibrani
#XTXT Human readable text for language code "hi"
hi=Hindi
#XTXT Human readable text for language code "hu"
hu=Hungaria
#XTXT Human readable text for language code "is"
is=Bahasa Islandia
#XTXT Human readable text for language code "id"
id=Bahasa Indonesia
#XTXT Human readable text for language code "it"
it=Italia
#XTXT Human readable text for language code "ja"
ja=Jepang
#XTXT Human readable text for language code "kk"
kk=Bahasa Kazakh
#XTXT Human readable text for language code "ko"
ko=Korea
#XTXT Human readable text for language code "lv"
lv=Bahasa Latvia
#XTXT Human readable text for language code "lt"
lt=Lituavi
#XTXT Human readable text for language code "ms"
ms=Bahasa Melayu
#XTXT Human readable text for language code "no"
no=Norwegia
#XTXT Human readable text for language code "pl"
pl=Polski
#XTXT Human readable text for language code "pt"
pt=Bahasa Portugis (Brasil)
#XTXT Human readable text for language code "pt-PT"
pt-PT=Portugis (Portugal)
#XTXT Human readable text for language code "ro"
ro=Rumania
#XTXT Human readable text for language code "ru"
ru=Rusia
#XTXT Human readable text for language code "sr"
sr=Bahasa Serbia
#XTXT Human readable text for language code "sh"
sh=Bahasa Serbia-Kroasia
#XTXT Human readable text for language code "sk"
sk=Slowak
#XTXT Human readable text for language code "sl"
sl=Slovenia
#XTXT Human readable text for language code "es"
es=Spanyol
#XTXT Human readable text for language code "es-MX"
es-MX=Spanyol (Meksiko)
#XTXT Human readable text for language code "sv"
sv=Swedia
#XTXT Human readable text for language code "th"
th=Thai
#XTXT Human readable text for language code "tr"
tr=Turki
#XTXT Human readable text for language code "uk"
uk=Ukraina
#XTXT Human readable text for language code "vi"
vi=Vietnam
#XTIT: Delete spaces confirmation title
deleteConfirmationHeaderText=Hapus Permanen Ruang-Ruang
#XTXT: Logically delete space delete confirmation text with space name
logicallyDeleteConfirmationBodyTextWithSpaceName=Apakah Anda yakin ingin memindahkan ruang "{0}" ke tempat sampah?
#XTXT: Logically dpace delete confirmation text for multiple spaces
logicallyDeleteConfirmationBodyTextMultiple=Apakah Anda yakin ingin memindahkan {0} ruang yang dipilih ke tempat sampah?
#XTXT: Delete space delete confirmation text with space name
deleteConfirmationBodyTextWithSpaceName=Apakah Anda yakin ingin menghapus permanen ruang "{0}"? Tindakan ini tidak dapat dibatalkan.
#XTXT: Delete space delete confirmation text for multiple spaces
deleteConfirmationBodyTextMultiple=Apakah Anda yakin ingin menghapus permanen {0} ruang yang dipilih? Tindakan ini tidak dapat dibatalkan. Konten berikut akan {1} dihapus secara permanen:
#XTXT: permanently
permanently=secara permanen
#XTXT: {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDetail=Konten berikut akan dihapus permanen {0} dan tidak dapat dipulihkan:
#XTXT: Hint Delete space confirmation text
deleteConfirmationHint=Silakan ketik {0} untuk mengonfirmasi penghapusan.
#XTXT: Delete space confirmation error check spelling
deleteConfirmationErrorCheckSpelling=Silakan periksa ejaan Anda dan coba lagi.
#XTXT: All Spaces
allSpaces=Semua Ruang
#XTXT: All data
allData=Semua objek dan data yang terdapat di dalam ruang
#XTXT: All connections
allConnections=Semua koneksi yang ditentukan di dalam ruang
#XFLD: Space tile selection box tooltip
clickToSelect=Klik untuk Memilih
#XTXT: All database users
allDatabaseUsers=Semua objek dan data yang terdapat di dalam skema SQL Terbuka yang terkait dengan ruang
#XFLD: remove members button tooltip
deleteUsers=Hapus Anggota
#XTXT: Space long description text
description=Deskripsi (Maksimum 4.000 Karakter)
#XFLD: Add Members button tooltip
addUsers=Tambahkan Anggota
#XFLD: Add Users button tooltip
addUsersTooltip=Tambahkan Pengguna
#XFLD: Edit Users button tooltip
editUsersTooltip=Edit Pengguna
#XFLD: Remove Users button tooltip
removeUsersTooltip=Hapus Pengguna
#XFLD: Searchfield placeholder
filter=Pencarian
#XCOL: Users table-view column health
health=Kesehatan
#XCOL: Users table-view column access
access=Akses
#XFLD: No user found nodatatext
noDataText=Pengguna Tidak Ditemukan
#XTIT: Members dialog title
selectUserDialogTitle=Tambahkan Anggota
#XTIT: User dialog title
addUserDialogTitle=Tambahkan Pengguna
#XTIT: Delete connections confirmation title
deleteConnectionConfirmationHeaderText=Hapus Permanen Koneksi
#XTIT: Delete single connection confirmation title
deleteConnectionConfirmationHeaderTextSingle=Hapus Permanen Koneksi
#XTXT: Delete connection delete confirmation text
deleteConnectionConfirmationBodyText=Apakah Anda yakin ingin menghapus permanen koneksi yang dipilih? Koneksi yang dipilih akan dihapus.
#XTIT: Remote source dialog title
selectRemoteConnectionDialogTitle=Pilih Koneksi
#XTIT: Share connection dialog title
connectionSharingDialogTitle=Bagikan Koneksi
#XTIT: Connections shared from other spaces table title
sharedConnectionsFromOtherSpaces=Koneksi yang Dibagikan
#XFLD: Add remote source button tooltip
addRemoteConnections=Tambahkan Koneksi
#XFLD: Delete remote source button tooltip
deleteRemoteConnections=Hapus Koneksi
#XFLD: Share remote source button tooltip
shareConnections=Bagikan Koneksi
#XFLD: Tile-layout tooltip
tileLayout=Tata Letak Ubin
#XFLD: Table-layout tooltip
tableLayout=Tata Letak Tabel
#XMSG: Success message after creating space
createSpaceSuccessMessage=Ruang dibuat
#XMSG: Success message after copying space
copySpaceSuccessMessage=Menyalin ruang "{0}" ke ruang "{1}"
#XMSG: Success message after deploying space
deploymentSuccessMessage=Penyebaran ruang telah dimulai
#XMSG: Success message after Apache Spark update started
sparkUpdateStartedSuccessMessage=Pembaruan Apache Spark telah dimulai
#YMSE: Error while starting Apache Spark update
sparkUpdateStartedErrorMessage=Pembaruan Apache Spark gagal
#XMSG: Success message after updating space
updateSettingsSuccessMessage=Rincian ruang diperbarui
#XMSG: Success message after unlocking space
unlockSpaceSuccessMessage=Kunci ruang terbuka untuk sementara
#XMSG: Success message after deleting a single space
deleteSpaceSuccessMessage=Ruang dihapus permanen
#XMSG: Success message after deleting multiple spaces
deleteSpacesSuccessMessage=Ruang dihapus permanen
#XMSG: Success message after restoring a single space
restoreSpaceSuccessMessage=Ruang dipulihkan
#XMSG: Success message after restoring multiple spaces
restoreSpacesSuccessMessage=Ruang dipulihkan
#YMSE: Error while updating settings
updateSettingsFailureMessage=Pengaturan ruang tidak dapat diperbarui.
#YMSE: Error while updating data lake option
dataLakeEnabledInOtherSpace=Data lake telah ditetapkan ke ruang lain. Hanya satu ruang yang dapat mengakses data lake pada satu waktu.
#YMSE: Error while updating data lake option
virtualTablesExists=Anda tidak dapat batal menetapkan data lake dari ruang ini karena masih terdapat dependensi ke tabel virtual*. Silakan hapus permanen tabel virtual untuk batal menetapkan data lake dari ruang ini.
#YMSE: Error while unlocking space
unlockSpaceFailureMessage=Kunci ruang tidak dapat dibuka.
#YMSE: Error while creating space
createSpaceError=Ruang tidak dapat dibuat.
#YMSE: Error while creating duplicate space
createDuplicateSpaceError=Ruang dengan nama {0} sudah ada.
#YMSE: Error while deleting a single space
deleteSpaceError=Ruang tidak dapat dihapus permanen.
#YMSE: Error details while deleting a single space
deleteSpaceDetailedError=Ruang “{0}” Anda tidak lagi berfungsi dengan baik. Coba hapus kembali. Jika masih tidak berfungsi, minta administrator Anda untuk menghapus ruang Anda atau membuka tiket.
#YMSE: Error while deleting a single space in files
deleteSpaceInFilesError=Data ruang di File tidak dapat dihapus permanen.
#YMSE: Error while deleting a single space users
deleteSpaceUsersError=Pengguna tidak dapat dihapus permanen.
#YMSE: Error while deleting a single space schema
deleteSpaceSchemasError=Skema tidak dapat dihapus permanen.
#YMSE: Error while deleting a single space connections
deleteSpaceConnectionsError=Koneksi tidak dapat dihapus.
#YMSE: Error while deleting a single space data
deleteSpaceDataError=Data ruang tidak dapat dihapus permanen.
#YMSE: Error while deleting multiple spaces
deleteSpacesError=Ruang tidak dapat dihapus permanen.
#YMSE: Error while restoring a single space
restoreSpaceError=Ruang tidak dapat dipulihkan.
#YMSE: Error while restoring multiple spaces
restoreSpacesError=Ruang tidak dapat dipulihkan.
#YMSE: Error while creating users
createUsersError=Pengguna tidak dapat ditambahkan.
#YMSE: Error while removing users
removeUsersError=Kami tidak dapat menghapus pengguna.
#YMSE: Error while removing user
removeUserError=tidak dapat menghapus pengguna.
#YMSE: Error assigning roles to a user
roleAssignUnAssignError=Kami tidak dapat menambahkan pengguna ke peran tercakup yang dipilih. \n\n Anda tidak dapat menambahkan diri Anda sendiri ke peran tercakup. Anda dapat meminta administrator untuk menambahkan Anda ke peran tercakup.
#YMSE: Error assigning user to the space
userAssignError=Kami tidak dapat menetapkan pengguna ke ruang tersebut. \n\n Pengguna sudah ditetapkan ke jumlah maksimum yang diizinkan (100) ruang di seluruh cakupan peran.
#YMSE: Error assigning users to the space
usersAssignError=Kami tidak dapat menetapkan pengguna ke ruang tersebut. \n\n Pengguna sudah ditetapkan ke jumlah maksimum yang diizinkan (100) ruang di seluruh cakupan peran.
#YMSE: Error while fetching users
fetchUserWithScopedRolesError=Kami tidak dapat mengambil pengguna. Silakan coba lagi nanti.
#YMSE: Error while fetching scoped roles
fetchScopedRolesError=Kami tidak dapat mengambil peran yang dicakup.
#YMSE: Error while fetching members
fetchUserError=Anggota tidak dapat diambil. Silakan coba lagi nanti.
#YMSE: Error while loading run-time database
loadRuntimeError=Kami tidak dapat memuat informasi dari basis data run-time.
#YMSE: Error while loading spaces
loadSpacesError=Maaf, terjadi kesalahan saat mencoba mengambil ruang Anda.
#YMSE: Error while loading haas resources
loadStorageError=Maaf, terjadi kesalahan saat mencoba mengambil data penyimpanan.
#YMSE: Error no data could be loaded
loadDataError=Maaf, terjadi kesalahan saat mencoba mengambil data Anda.
#XFLD: Click to refresh storage data
clickToRefresh=Klik di sini untuk mencoba lagi.
#XTIT: Delete space popup title
deleteSpacePopupTitle=Hapus Permanen Ruang
#XCOL: Spaces table-view column name
name=Nama
#XCOL: Spaces table-view deployment status
deploymentStatus=Status Penyebaran
#XFLD: Disk label in space details
storageLabel=Disk (GB)
#XFLD: In-Memory label in space details
ramLabel=Memori (GB)
#XFLD: Memory label on space card
memory=Memori untuk Penyimpanan
#XFLD: Storage Assignment label in space details
storrageAssignmentLabel=Penyimpanan Ruang
#XFLD: Storage Type label in space details
storageTypeLabel=Tipe Penyimpanan
#XFLD: Enable Space Quota
enableSpaceQuota=Aktifkan Kuota Ruang
#XFLD: No Space Quota
noSpaceQuota=Tidak Ada Kuota Ruang
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
diskAndInMemoryText=Basis Data SAP HANA (Disk dan dalam Memori)
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
dataLakeFilesText=File Data Lake SAP HANA
#XFLD: Available scoped roles label
availableRoles=Peran Tercakup yang Tersedia
#XFLD: Selected scoped roles label
selectedRoles=Peran Tercakup yang Dipilih
#XCOL: Spaces table-view column models
models=Model
#XCOL: Spaces table-view column users
users=Pengguna
#XCOL: Spaces table-view column connections
connections=Koneksi
#XFLD: Section header overview in space detail
overview=Gambaran Umum
#XFLD: Section header Apache Spark in space detail
sparkManagementSection=Apache Spark
#XFLD: Section header Apache Spark in space detail
sparkManagementSubSection=Aplikasi
#XFLD: Section header Apache Spark in space detail
taskAssignmentSubSection=Penetapan Tugas
#XFLD: vCPU label in Apache Spark section
vCPULabel=vCPU
#XFLD: Memory label in Apache Spark section
memoryLabel=Memori (GB)
#XFLD: space configuration label in Apache Spark section
spaceConfigurationLabel=Konfigurasi Ruang
#XFLD: Space Source label
sparkApplicationLabel=Aplikasi
#XFLD: Cluster Size label
clusterSizeLabel=Ukuran Kluster
#XFLD: Driver label
driverLabel=Driver
#XFLD: Executor label
executorLabel=Pelaksana
#XFLD: max label
maxLabel=Penggunaan Maksimum
#XFLD: TrF Default label
trFDefaultLabel=Aliran Perubahan Default
#XFLD: Merge Default label
mergeDefaultLabel=Penggabungan Default
#XFLD: Optimize Default label
optimizeDefaultLabel=Optimalkan Pengaturan Default
#XFLD: Deployment Default label
deploymentDefaultLabel=Penyebaran Tabel (File) Lokal
#XFLD: max spark source label
driverExecutorMaxUsedSparkSource={0} CPU / {1} GB
#XFLD: Object type label
taskObjectTypeLabel=Tipe Objek
#XFLD: Task activity label
taskActivityLabel=Aktivitas
#XFLD: Task Application ID label
taskApplicationIDLabel=Aplikasi Default
#XFLD: Section header in space detail
generalSettings=Pengaturan Umum
#XMSG: Message strip message when a space is locked
spaceLockedMessageStrip=Saat ini ruang dikunci oleh sistem.
#XMSG: Message strip message deploy warning
deployWarningMessageStrip=Perubahan di bagian ini akan segera disebarkan.
#XMSG: Message strip message modify space configuration warning
modifySpaceConfigurationMessageStrip=Perlu diperhatikan bahwa mengubah nilai dapat menyebabkan masalah kinerja.
#XFLD: Button text to unlock the space again
unlockSpace=Buka Kunci Ruang
#XFLD: Info text for audit log formatted message
auditLogText=Aktifkan log audit untuk mencatat tindakan membaca atau mengubah data (kebijakan audit). Administrator selanjutnya dapat menganalisis pengguna yang melakukan tindakan tertentu pada waktu tertentu.
#XMSG: Message strip  audit warning
auditLogWarningMessageStrip=Log audit dapat menghabiskan banyak penyimpanan disk di penyewa Anda. Jika Anda mengaktifkan kebijakan audit (tindakan membaca atau mengubah data), Anda harus memantau penggunaan penyimpanan disk secara rutin (melalui kartu Penyimpanan Disk yang Digunakan di Pemantau Sistem) untuk menghindari kehabisan ruang disk, yang dapat menyebabkan gangguan layanan. Jika Anda menonaktifkan kebijakan audit, semua entri log auditnya akan dihapus permanen. Jika Anda ingin menyimpan entri log audit, pertimbangkan untuk mengekspornya sebelum Anda menonaktifkan kebijakan audit.
#XFLD: Message strip link text to open in app help to guide user what to do to unlock his space permanently
unlockSpaceHelp=Tampilkan Bantuan
#XMSG: Message strip message when a space will be locked again
spaceTemporarilyUnlockedMessageStrip=Ruang ini melampaui ruang penyimpanannya dan akan dikunci dalam {0} {1}.
#XMSG: Unit for remaining time until space is locked again
hours=jam
#XMSG: Unit for remaining time until space is locked again
minutes=menit
#XFLD: Section header in space detail for auditing
detailsSectionAuditing=Pengauditan
#XFLD: Subsection header in space detail for auditing
auditing=Pengaturan Audit Ruang
#XFLD: Hot space tooltip
hotSpaceCountTooltip=Ruang kritis: Penyimpanan yang digunakan lebih dari 90%.
#XFLD: Green space tooltip
greenSpaceCountTooltip=Ruang sesuai: Penyimpanan yang digunakan antara 6% hingga 90%.
#XFLD: Cold space tooltip
coldSpaceCountTooltip=Ruang aman: Penyimpanan yang digunakan sebesar 5% atau kurang.
#XFLD: Critical space tooltip
criticalSpaceCountTooltip=Ruang kritis: Penyimpanan yang digunakan lebih dari 90%.
#XFLD: Green space tooltip
okSpaceCountTooltip=Ruang sesuai: Penyimpanan yang digunakan antara 6% hingga 90%.
#XFLD: Locked space tooltip
lockedSpaceCountTooltipNew=Ruang terkunci: Diblokir karena memori tidak memadai.
#XFLD: Locked space tooltip
lockedSpaceCountTooltip=Ruang yang Dikunci
#YMSE: Error while deleting remote source
deleteRemoteError=Koneksi tidak dapat dihapus.
#XFLD: Space ID input tooltip
spaceIDInfoTooltip=ID Ruang tidak dapat diubah nanti.\nKarakter yang valid A - Z, 0 - 9, dan _
#YMSE: Error when space name is empty
spaceNameEmptyValidationError=Masukkan nama ruang.
#YMSE: Error when ECN Business name is empty
ecnNameEmptyValidationError=Masukkan nama bisnis.
#YMSE: Error when space id is empty
spaceIDInputEmptyValidationError=Masukkan ID ruang.
#YMSE: Error when space id contains invalid characters
spaceIDInputInValidError=Karakter tidak valid. Gunakan A - Z, 0 - 9, dan _ saja.
#YMSE: Error when space id exists while creation
spaceIDInputDuplicateError=ID Ruang sudah ada.
#XFLD: Space searchfield placeholder
search=Pencarian
#XMSG: Success message after creating users
createUsersSuccess=Pengguna ditambahkan
#XMSG: Success message after creating user
createUserSuccess=Pengguna ditambahkan
#XMSG: Success message after updating users
updateUsersSuccess={0} Pengguna diperbarui
#XMSG: Success message after updating user
updateUserSuccess=Pengguna diperbarui
#XMSG: Success message after removing users
removeUsersSuccess={0} Pengguna dihapus
#XMSG: Success message after removing user
removeUserSuccess=Pengguna dihapus
#XFLD: Schema name
schemaName=Nama Skema
#XFLD: used of total
ofTemplate={0} dari {1}
#XFLD: Assigned disk label in space overview
assignedStorageTemplate=Disk yang Ditetapkan ({0} dari {1})
#XFLD: Assigned Memory label in space overview
assignedRAMTemplate=Memori yang Ditetapkan ({0} dari {1})
#XFLD: Storage ratio on space
accelearationRAM=Percepatan Memori
#XFLD: No Storage Consumption
noStorageConsumptionText=Tidak ada kuota penyimpanan yang ditetapkan.
#XFLD: Used disk label in space overview
usedStorageTemplate=Disk yang Digunakan untuk Penyimpanan ({0} dari {1})
#XFLD: Used Memory label in space overview
usedRAMTemplate=Memori yang Digunakan untuk Penyimpanan ({0} dari {1})
#XFLD: Used Disk tooltip
usedStorageTooltipTemplate={0} dari {1} Disk Digunakan untuk Penyimpanan
#XFLD: Used Memory tooltip
usedRAMTooltipTemplate={0} dari {1} Memori yang Digunakan
#XFLD: Assigned Disk tooltip
assignedStorageTooltipTemplate={0} dari {1} Disk Ditetapkan
#XFLD: Assigned Memory tooltip
assignedRAMTooltipTemplate={0} dari {1} Memori yang Ditetapkan
#XFLD: Space related used storage tooltip
spaceRelatedUsedStorageTooltipTemplate=Data Ruang: {0}
#XFLD: Not space related used storage tooltip
otherDataUsedStorageTooltipTemplate=Data Lain: {0}
#XFLD: Storage used exceeded popover and tooltip
storageUsedExceededText=Pertimbangkan untuk memperpanjang rencana Anda atau hubungi Dukungan SAP.
#XCOL: Space table-view column used Disk
usedStorage=Disk yang Digunakan untuk Penyimpanan
#XCOL: Space monitor column used Memory
usedRAM=Memori yang Digunakan untuk Penyimpanan
#XCOL: Space monitor column Schema
tableSchema=Skema
#XCOL: Space monitor column Storage Type
tableStorageType=Tipe Penyimpanan
#XCOL: Space monitor column Table Type
tableType=Tipe Tabel
#XCOL: Space monitor column Record Count
tableRecordCount=Jumlah Catatan
#XFLD: Assigned Disk
assignedStorage=Disk yang Ditetapkan untuk Penyimpanan
#XFLD: Assigned Memory
assignedRAM=Memori yang Ditetapkan untuk Penyimpanan
#XCOL: Space table-view column storage utilization
tableStorageUtilization=Penyimpanan yang Digunakan
#XFLD: space status
spaceStatus=Status Ruang
#XFLD: space type
spaceType=Jenis Ruang
#XFLD: space type label for space with space type "SAP BW bridge" ("SAP BW bridge" is a product name, do not translate it)
abapbridgeSpaceType=SAP BW bridge
#XFLD: space type label for space with space type "Data Marketplace"
marketplaceSpaceType=Produk Penyedia Data
#XMSG: Error message for when selected space cannot be deleted due to the space type
deleteTypedSpaceError=Anda tidak dapat menghapus permanen ruang {0} karena jenis ruang tersebut adalah {1}.
#XMSG: Error message for when selected spaces cannot be deleted due to the space type
deleteTypedSpacesError=Anda tidak dapat menghapus permanen {0} ruang yang dipilih. Ruang dengan jenis ruang berikut tidak dapat dihapus permanen: {1}.
#XFLD: space type label for space without space type
defaultSpaceType=SAP Datasphere
#XFLD: Tooltip for monitor space button
monitorSpace=Pantau
#XFLD: Tooltip for edit space button
editSpace=Edit Ruang
#XMSG: Deletion warning in messagebox
deleteConfirmation=Apakah Anda yakin ingin menghapus permanen ruang ini?
#XFLD: Tooltip for delete space button
deleteSpace=Hapus Permanen Ruang
#XFLD: storage
storage=Disk untuk Penyimpanan
#XFLD: username
userName=Nama Pengguna
#XFLD: port
port=Port
#XFLD: hostname
hostName=Nama Host
#XFLD: password
password=Kata Sandi
#XBUT: Request new password button
requestPassword=Minta Kata Sandi Baru
#YEXP: Usage explanation in time data section
timeDataSectionHint=Buat dimensi dan tabel waktu untuk digunakan di model dan stori Anda.
#YEXP: Usage explanation in Space schema section
spaceSchemaHint=Apakah Anda ingin data di ruang Anda dapat dipakai oleh alat atau aplikasi lain? Jika demikian, buat satu atau beberapa pengguna yang dapat mengakses data di ruang Anda dan pilih apakah Anda ingin data ruang di masa mendatang dapat dipakai secara default.
#XTIT: Create schema popup title
createSchemaDialogTitle=Buat Skema SQL Terbuka
#XTIT: Generate Time Data dialog title
generateTimeDataDialogTitle=Buat Dimensi dan Tabel Waktu
#XTIT: Edit Time Data dialog title
editTimeDataDialogTitle=Edit Dimensi dan Tabel Waktu
#XTIT: Time Data token title
timeDataTokenTitle=Data Waktu
#XTIT: Time Data token title
timeDataUpdateViews=Perbarui Tampilan Data Waktu
#XFLD: Time Data token creation in progress label
timeDataCreationInProgress=Pembuatan sedang berlangsung...
#XFLD: Time Data token creation error label
timeDataCreationError=Pembuatan gagal. Silakan coba lagi.
#XTIT: Time Data dialog time settings title
timeDataDialogTimeSettingsTitle=Pengaturan Tabel Waktu
#XTIT: Time Data dialog time data table title
timeDataDialogTranslationTablesTitle=Tabel Terjemahan
#XTIT: Time Data dialog dimensions title
timeDataDialogTimeViewsTitle=Dimensi Waktu
#XFLD: Time Data dialog time range label
timeRangeHint=Tentukan rentang waktu.
#XFLD: Time Data dialog time data table label
timeDataHint=Beri nama tabel Anda.
#XFLD: Time Data dialog time data dimensions label
timeDimensionsHint=Beri nama dimensi Anda.
#XFLD: Time Data Time range description label
timerangeLabel=Rentang Waktu
#XFLD: Time Data dialog from year label
fromYearLabel=Dari Tahun
#XFLD: Time Data dialog to year label
toYearLabel=Hingga Tahun
#XFLD: Time Data dialog calendar type label
calendarTypeLabel=Tipe Kalender
#XFLD: Time Data dialog granularity label
granularityLabel=Rincian
#XFLD: Time Data dialog technical name label
technicalNameLabel=Nama Teknis
#XFLD: Time Data dialog quarters text table lable
quarterTextsTableLabel=Tabel Terjemahan untuk Triwulan
#XFLD: Time Data dialog months text table lable
monthsTextsTableLabel=Tabel Terjemahan untuk Bulan
#XFLD: Time Data dialog days text table lable
daysTextsTableLabel=Tabel Terjemahan untuk Hari
#XFLD: Time Data dialog year label
yearLabel=Dimensi Tahun
#XFLD: Time Data dialog quarter label
quarterLabel=Dimensi Triwulan
#XFLD: Time Data dialog month label
monthLabel=Dimensi Bulan
#XFLD: Time Data dialog day label
dayLabel=Dimensi Hari
#XFLD: Time Data dialog gregorian calendar type label
gregorian=Masehi
#XFLD: Time Data dialog time granularity day label
day=Hari
#XFLD: Time Data dialog business name maxlength reached
timeRangeValueStateTextMaxLengthBusinessName=Mencapai panjang maksimum 1000 karakter.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextMaxRangeExceeded=Rentang waktu maksimum adalah 150 tahun.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeLower="Dari Tahun" harus lebih kecil dari "Hingga Tahun"
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeHigher="Dari Tahun" harus 1900 atau lebih tinggi.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeHigher="Hingga Tahun" harus lebih besar dari "Dari Tahun"
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeLower="Hingga Tahun" harus lebih rendah dari tahun ini ditambah 100
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningFromYear=Meningkatkan "Dari Tahun" dapat mengakibatkan data hilang
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningToYear=Menurunkan "Hingga Tahun" dapat mengakibatkan data hilang
#XMSG: Time Data creation validation error message
timeDataValidationError=Tampaknya beberapa bidang tidak valid. Silakan periksa bidang yang diperlukan untuk melanjutkan.
#XMSG: Time Data deletion popup text
deleteTimeDataConfirmation=Apakah Anda yakin ingin menghapus permanen data ini?
#XMSG: Time Data creation success message
createTimeDataSuccess=Data waktu dibuat
#XMSG: Time Data update success message
updateTimeDataSuccess=Data waktu diperbarui
#XMSG: Time Data delete success message
deleteTimeDataSuccess=Data waktu dihapus permanen
#XMSG: Time Data creation error message
createTimeDataError=Terjadi kesalahan saat mencoba membuat data waktu.
#XMSG: Time Data update error message
updateTimeDataError=Terjadi kesalahan saat mencoba memperbarui data waktu.
#XMSG: Time Data creation error message
deleteTimeDataError=Terjadi kesalahan saat mencoba menghapus permanen data waktu.
#XMSG: Error message when time data could not be retrieved
retrievingTimeDataError=Data waktu tidak dapat dimuat.
#XTIT: Time Data deletion popup title
deleteTimeDataConfirmationTitle=Peringatan
#XTXT: Time Data deletion dependency popup text
deleteTimeDataDependencyText=Kami tidak dapat menghapus permanen Data Waktu Anda karena digunakan di model lain.
#XTXT: Time Data deletion dependency popup text singular
deleteTimeDataDependencyTextSingular=Kami tidak dapat menghapus permanen Data Waktu Anda karena digunakan di model lain.
#XMSG: Validation error when required time data form field is empty
timeDataRequiredField=Bidang ini wajib diisi dan tidak boleh kosong.
#XFLD: Tooltip for Open in Database Explorer button
openInDBX=Buka di Penjelajah Basis Data
#YMSE: Dimension Year
dimensionYearView=Dimensi "Tahun"
#YMSE: Dimension Year
dimensionQuarterView=Dimensi "Triwulan"
#YMSE: Dimension Year
dimensionMonthView=Dimensi "Bulan"
#YMSE: Dimension Year
dimensionDayView=Dimensi "Hari"
#XFLD: Time Data deletion object title
timeDataUsedIn=(digunakan dalam {0} model)
#XFLD: Time Data deletion object title singular
timeDataUsedInSingular=(digunakan dalam 1 model)
#XFLD: Time Data deletion table column provider
provider=Penyedia
#XTIT: Time Data dependency dialog title
timeDataDependencyDialogTitle=Dependensi
#XTIT: Create schema popup title
createSpaceSchemaUserDialogTitle=Buat Pengguna untuk Skema Ruang
#XFLD: Create schema button
createSchemaButton=Buat Skema SQL Terbuka
#XFLD: Generate TimeData button
generateTimeDataButton=Buat Dimensi dan Tabel Waktu
#XFLD: Show dependencies button
showDependenciesButton=Tampilkan Dependensi
#XFLD: tooltip for disabled buttons when user is not member of space
mustBeMemberOfSpaceForOperation=Untuk menjalankan operasi ini, pengguna Anda harus merupakan anggota ruang tersebut.
#XFLD: Create space schema user button
createSpaceSchemaUserButton=Buat Pengguna Skema Ruang
#YMSE: API Schema users load error
loadSchemaUsersError=Daftar pengguna tidak dapat dimuat.
#XTIT: API Schema-config popup title
schemaUserDetailsDialogTitle=Rincian Pengguna Skema Ruang
#XMSG: API Schema user deletion popup text
deleteSpaceSchemaUserConfirmation=Apakah Anda yakin ingin menghapus permanen pengguna yang dipilih?
#XMSG: API Schema user deletion confirmation text
userDeleteSuccess=Pengguna dihapus permanen.
#YMSE: API Schema user deletion error
userDeleteError=Pengguna tidak dapat dihapus permanen.
#XFLD: User deleted
userDeleted=Pengguna telah dihapus permanen.
#XTIT: Remove user popup title
removeUserConfirmationTitle=Peringatan
#XMSG: Remove user popup text
removeUserConfirmation=Apakah Anda benar-benar ingin menghapus pengguna? Pengguna dan peran tercakup yang ditetapkannya akan dihapus dari ruang tersebut.
#XMSG: Remove users popup text
removeUsersConfirmation=Apakah Anda benar-benar ingin menghapus pengguna? Pengguna dan peran tercakup yang ditetapkan untuk mereka akan dihapus dari ruang tersebut.
#XBUT: Confirmation button for remove user dialog
removeUserConfirmationBtn=Hapus
#YMSE: No data text for available roles
noDataAvailableRoles=Ruang tidak ditambahkan ke peran cakupan apa pun. \n Untuk dapat menambahkan pengguna ke ruang, ruang tersebut harus ditambahkan terlebih dahulu ke satu atau beberapa peran cakupan.
#YMSE: No data text for selected roles
noDataSelectedRoles=Tidak ada peran tercakup yang dipilih
#XTIT: Schema-config popup title
schemaDetailsDialogTitle=Rincian Konfigurasi Skema SQL Terbuka
#XFLD: Label for Read Audit Log
auditLogRead=Aktifkan Log Audit untuk Operasi Baca
#XFLD: Label for Change Audit Log
auditLogChange=Aktifkan Log Audit untuk Operasi Perubahan
#XFLD: Label Audit Log Retention
auditLogRetention=Simpan Log selama
#XFLD: Label Audit Log Retention Unit
retentionUnit=Hari
#YMSE: Error when log retention time is set incorrectly
incorrectRetentionTime=Masukkan seluruh angka antara {0} hingga {1}
#XFLD: Label for Cross Space Schema Access
spaceSchemaAccess=Pakai Data Skema Ruang
#XTIT: Confirm deactivation of space schema consumption dialog title
disableSpaceSchemaConsumptionDialogTitle=Berhenti Pakai Data Skema Ruang
#XTXT: Confirm deactivation of space schema consumption dialog text
disableSpaceSchemaConsumptionDialogText=Skema SQL Terbuka ini dapat memakai data skema ruang Anda. Jika Anda berhenti memakainya, model yang didasarkan pada data skema ruang dapat tidak berfungsi lagi.
#XBUT: Confirmation button for the stop consuming space schema data dialog
stopConsuming=Berhenti Pakai
#XTXT: Tooltip for data lake icon
dataLakeIconTooltip=Ruang ini digunakan untuk mengakses data lake
#XFLD: Tooltip for data lake table entry
dataLakeTableEntryTooltip=Data Lake Diaktifkan
#XFLD: used In-Memory linit reached tag
usedRamLimitReachedLabel=Batas Memori Tercapai
#XFLD: Space storage Limit reached tag
storageLimitReachedLabel=Mencapai Batas Penyimpanan
#XFLD: Minimal Storage Limit Reached tag
minStorageLimitReachedLabel=Mencapai Batas Penyimpanan Minimum
#XFLD: Space ram tag
ramLimitReachedLabel=Batas Memori Tercapai
#XFLD: Minimal In-Memory Limit Reached tag
minRamLimitReachedLabel=Batas Memori Minimum Tercapai
#XFLD: Popover text after click on space storage limit warning
storageLimitReachedText=Anda telah mencapai batas penyimpanan ruang yang ditetapkan sebesar {0}. Silakan tetapkan lebih banyak penyimpanan ke ruang.
#XFLD: System storage tag
systemStorageLimitReachedLabel=Mencapai Batas Penyimpanan Sistem
#XMSG: Popover text after click on storage limit warning
systemStorageLimitReachedText=Anda telah mencapai batas penyimpanan sistem sebesar {0}. Kini Anda tidak dapat menetapkan lebih banyak penyimpanan lagi ke ruang.
#XMSG: Schema deletion warning
deleteSchemaConfirmation=Menghapus permanen skema SQL terbuka ini akan secara permanen menghapus juga semua objek yang disimpan dan asosiasi yang dipertahankan dalam skema. Lanjutkan?
#XMSG: Success message after deleting a schema
schemaDeleteSuccess=Skema dihapus permanen
#YMSE: Error while deleting schema.
schemaDeleteError=Skema tidak dapat dihapus permanen.
#XMSG: Success message after update a schema
schemaUpdateSuccess=Skema diperbarui
#YMSE: Error while updating schema.
schemaUpdateError=Skema tidak dapat diperbarui.
#XMSG: Hint when no password is shown in the dialog.
newPasswordHint=Kami memberikan kata sandi untuk skema ini. Jika Anda lupa atau kehilangan kata sandi, Anda dapat meminta kata sandi yang baru. Harap ingat untuk menyalin atau menyimpan kata sandi Anda yang baru.
#XMSG: Hint when showing the password in the dialog.
savePasswordHint=Silakan salin kata sandi Anda. Anda memerlukan kata sandi untuk mengatur koneksi ke skema ini. Jika Anda lupa kata sandi, Anda dapat membuka dialog ini untuk mengatur ulang kata sandi.
#XFLD,50: Hint on entering schema name in creation dialog
schemaNameHint=Nama ini tidak dapat diubah setelah skema dibuat.
#XFLD: Open SQL Schemas section sub headline
schemasSQL=SQL Terbuka
#XFLD: Space schema section sub headline
schemasSpace=Ruang
#XFLD: HDI Container section header
HDIContainers=Kontainer HDI
#XTXT: Add HDI Containers button tooltip
addHDIContainers=Tambahkan Kontainer HDI
#XTXT: Remove HDI Containers button tooltip
removeHDIContainers=Hapus Kontainer HDI
#XFLD: Enable HDI Containers feature button
enableHDIContainersButton=Aktifkan Akses
#YMSE: No data text for HDI Containers table
noDataHDIContainers=Tidak ada kontainer HDI yang ditambahkan.
#YMSE: No data text for Timedata section
noDataTimedata=Tidak ada tabel dan dimensi waktu yang telah dibuat.
#YMSE: No data text during runtime not available for Timedata section
runtimeNotAvailableNoDataTimedata=Tidak dapat memuat tabel dan dimensi waktu karena basis data run-time tidak tersedia.
#YMSE: No data text during runtime not available for HDI section
runtimeNotAvailableNoDataHDI=Tidak dapat memuat Kontainer HDI karena basis data run-time tidak tersedia.
#YMSE: Error while fetching HDI Containers
fetchHDIContainersError=Kontainer HDI tidak dapat diperoleh. Silakan coba lagi nanti.
#XFLD Table column header for HDI Container names
HDIContainerName=Nama Kontainer HDI
#XFLD: Enable HDI Containers feature dialog title
enableHDIContainersDialogTitle=Aktifkan Akses
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText1New=Anda dapat mengaktifkan SAP SQL Data Warehousing pada penyewa SAP Datasphere Anda untuk bertukar data antara ruang kontainer HDI dan SAP Datasphere Anda tanpa memerlukan pergerakan data.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText2=Untuk melakukannya, buka tiket dukungan dengan mengklik tombol di bawah ini.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText3New=Setelah tiket Anda telah diproses, Anda harus membangun satu atau beberapa kontainer HDI baru dalam basis data run-time SAP Datasphere. Kemudian, tombol Aktifkan Akses diganti dengan tombol + dalam bagian Kontainer HDI untuk semua ruang SAP Datasphere, dan Anda dapat menambahkan kontainer Anda ke ruang.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText41=Perlu informasi selengkapnya? Buka %%0. Untuk informasi terperinci tentang aspek yang perlu disertakan dalam tiket, lihat %%1.
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkText=Bantuan SAP
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkTextSapNote3057059=Catatan SAP 3057059
#XBUT: Open Ticket Button Text
openTicket=Buka Tiket
#XBUT: Add Button Text
add=Tambahkan
#XBUT: Next Button Text
next=Selanjutnya
#XBUT: Edit Button Text
editUsers=Edit
#XBUT: create user Button Text
createUser=Buat
#XBUT: Update user Button Text
updateUser=Pilih
#XTIT: Add HDI Containers Dialog Title
addHDIContainersDialogTitle=Tambahkan Kontainer HDI yang Belum Ditetapkan
#XFLD: No data text for add HDI container dialog
addHDIContainersDialogNoDataText=Kami tidak dapat menemukan kontainer apa pun yang belum ditetapkan. \n Kontainer yang Anda cari mungkin telah ditetapkan ke suatu ruang.
#YMSE: Error while loading assigned HDI Containers
loadAssignedHDIContainersError=Kontainer HDI yang ditetapkan tidak dapat dimuat.
#YMSE: Error while loading assigned HDI Containers
loadUnassignedHDIContainersError=Kontainer HDI tidak dapat dimuat.
#XMSG: Success message
succeededToAddHDIContainer=Kontainer HDI ditambahkan
#XMSG: Success message
succeededToAddHDIContainerPlural=Kontainer HDI ditambahkan
#XMSG: Success message
succeededToDeleteHDIContainer=Kontainer HDI dihapus
#XMSG: Success message
succeededToDeleteHDIContainerPlural=Kontainer HDI dihapus
#XFLD: Time data section sub headline
timeDataSection=Dimensi dan Tabel Waktu
#XFLD: Schemas list entry read audit log enabled indicator
readIndicator=Baca
#XFLD: Schemas list entry change audit log enabled indicator
changeIndicator=Ubah
#XFLD: Remote sources section sub headline
allconnections=Penetapan Koneksi
#XFLD: Remote sources section sub headline
localconnections=Koneksi Lokal
#XFLD: Remote sources section sub headline for open Connectors
openConnectors=SAP Open Connectors
#XFLD: User section sub headline
memberassignment=Penetapan Anggota
#XFLD: User assignment section sub headline
userAssignment=Penetapan Pengguna
#XFLD: User section Access dropdown Member
member=Anggota
#XFLD: User assignment section column name
user=Nama Pengguna
#XTXT: Selected role count
selectedRoleToolbarText=Dipilih: {0}
#XTIT: Space detail section connections title
detailsSectionConnections=Koneksi
#XTIT: Space detail section data access title
detailsSectionDataAccess=Akses Skema
#XTIT: Space detail section time data title
detailsSectionGenerateData=Data Waktu
#XTIT: Space detail section members title
detailsSectionUsers=Anggota
#XTIT: Space detail section Users title
detailsSectionUsersTitle=Pengguna
#XTIT: Out of Storage
insufficientStoragePopoverTitle=Penyimpanan Habis
#XTIT: Storage distribution
storageDistributionPopoverTitle=Penyimpanan Disk yang Digunakan
#XTXT: Out of Storage popover text
insufficientStorageText=Untuk membuat ruang baru, silakan kurangi penyimpanan ruang lain yang ditetapkan atau hapus permanen ruang yang tidak Anda perlukan lagi. Anda dapat meningkatkan total penyimpanan sistem Anda dengan memanggil Kelola Rencana.
#XMSG: Space id length warning
spaceIdLengthWarning=Melampaui karakter maksimum sebesar {0}.
#XMSG: Space name length warning
spaceNameLengthWarning=Melampaui karakter maksimum sebesar {0}.
#XMSG: Space id reserved prefix warning
spaceIdnWarningReservedPrefix=Harap jangan menggunakan prefiks {0} untuk menghindari kemungkinan ketidaksesuaian.
#YMSE: Error while loading open SQL schemas
loadLocalSchemasError=Skema SQL Terbuka tidak dapat dimuat.
#YMSE: Error while creating open SQL schema
createLocalSchemaError=Skema SQL Terbuka tidak dapat dibuat.
#YMSE: Error while loading all remote connections
loadRemoteConnectionsError=Tidak dapat memuat semua koneksi jarak jauh.
#YMSE: Error while loading space details
loadSpaceDetailsError=Rincian ruang tidak dapat dimuat.
#YMSE: Error while deploying space details
deploySpaceDetailsError=Ruang tidak dapat disebarkan.
#YMSE: Error while copying space details
copySpaceDetailsError=Ruang tidak dapat disalin.
#YMSE: Error while loading storage data
loadStorageDataError=Data penyimpanan tidak dapat dimuat.
#YMSE: Error while loading all users
loadAllUsersError=Tidak dapat memuat semua pengguna.
#YMSE: Failed to reset password
resetPasswordError=Kata sandi tidak dapat diatur ulang.
#XMSG: Success message after updating space
resetPasswordSuccessMessage=Kata sandi yang baru ditetapkan untuk skema
#YMSE: DP Agent-name too long
DBAgentNameError=Nama agen DP terlalu panjang.
#YMSE: Schema-name not valid.
schemaNameError=Nama skema tidak valid.
#YMSE: User name not valid.
UserNameError=Nama pengguna tidak valid.
#XTIT: Consumption by Storage Type
consumptionStorageTypeText=Pemakaian berdasarkan Tipe Penyimpanan
#XTIT: Consumption by Schema
consumptionSchemaText=Pemakaian berdasarkan Skema
#XTIT: Overall Consumption by schema
overallConsumptionSchemaText=Keseluruhan Pemakaian Tabel berdasarkan Skema
#XTIT: Overall Consumption by Table Type
overallConsumptionTableTypeText=Keseluruhan Pemakaian berdasarkan Tipe Tabel
#XTIT: Tables
tableDetailsText=Rincian Tabel
#XTIT: Table Storage Consumption
tableStorageConsumptionText=Pemakaian Penyimpanan Tabel
#XFLD: Table Type label
tableTypeLabel=Tipe Tabel
#XFLD: Schema label
schemaLabel=Skema
#XFLD: reset table tooltip
resetTable=Atur Ulang Tabel
#XFLD: In-Memory label in space monitor
inMemoryLabel=Memori
#XFLD: Disk label in space monitor
diskLabel=Disk
#XFLD: Yes
yesLabel=Ya
#XFLD: No
noLabel=Tidak
#XFLD: switch label: Want the data in this space to be consumable by default?
allowConsumptionByDefault=Apakah Anda ingin data dalam ruang ini dapat dipakai secara default?
#XFLD: Business Name
businessNameLabel=Nama Bisnis
#XFLD: Refresh
refresh=Segarkan
#XMSG: No filter results title
noFilterResultsTitle=Tampaknya pengaturan filter Anda tidak menampilkan data apa pun.
#XMSG: No filter results message
noFilterResultsMsg=Coba saring pengaturan filter Anda dan jika Anda masih tidak melihat data apa pun; buat beberapa tabel di Data Builder. Setelah tabel memakai penyimpanan, Anda dapat memantaunya di sini.
#XMSG: Space monitoring Hana state red title
spaceMonitorHanaRedTitle=Basis data run-time tidak tersedia.
#XMSG: Space monitoring Hana state red message
spaceMonitorHanaRedMsg=Karena basis data run-time tidak tersedia, fitur tertentu dinonaktifkan dan kami tidak dapat menampilkan informasi apa pun pada halaman ini.
#YMSE: Error while creating space schema user
createSpaceSchemaUserError=Pengguna skema ruang tidak dapat dibuat.
#YMSE: Error User name already exists
userAlreadyExistsError=Nama pengguna sudah ada.
#YMSE: Error Authentication failed
authenticationFailedError=Autentikasi gagal.
#XMSG: Message strip message when a schema user is locked
userLockedMessageStrip=Pengguna dikunci karena terlalu banyak gagal login. Silakan minta kata sandi yang baru untuk membuka kunci pengguna ini.
#XMSG: Success message after reset and unlock schema user
resetUnlockPasswordSuccessMessage=Kata sandi baru telah ditetapkan dan kunci pengguna dibuka
#XMSG: user is locked message
userLockedMessage=Pengguna dikunci.
#XCOL: Users table-view column Role
spaceRole=Peran
#XCOL: Users table-view column Scoped Role
spaceScopedRole=Peran Cakupan
#XCOL: Users table-view column Space Admin
spaceAdmin=Administrator Ruang
#XFLD: User section dropdown value Viewer
viewer=Penonton
#XFLD: User section dropdown value Modeler
modeler=Pemodel
#XFLD: User section dropdown value Data Integrator
dataIntegrator=Integrator Data
#XFLD: User section dropdown value Space Administrator
spaceAdministrator=Administrator Ruang
#XMSG: Success message -  Space Role updated successfully.
updateUserSpaceRoleSuccess=Peran ruang diperbarui
#XMSG: Error message - Failed to update Space Role.
updateUserSpaceRoleError=Peran Ruang gagal diperbarui.
#XFLD:
databaseUserNameSuffix=Sufiks Nama Pengguna Basis Data
#XTXT: Space Schema password text
spaceSchemaPasswordText=Untuk mengatur koneksi ke skema ini, silakan salin kata sandi Anda. Jika Anda lupa kata sandi, Anda dapat meminta kata sandi yang baru kapan saja.
#XFLD: Cloud Platform label in space schema dialog
cloudPlatformLabel=Cloud Platform
#XTXT: Cloud Platform text in space schema dialog
cloudPlatformText=Untuk mengatur akses melalui pengguna ini, aktifkan pemakaian dan salin kredensial. Jika Anda hanya dapat menyalin kredensial tanpa kata sandi, pastikan bahwa Anda menambahkan kata sandi nanti.
#XFLD: enable consumption in CloudPlatform label in space schema dialog
enableConsumptionInCloudPlatformLabel=Aktifkan Pemakaian di Cloud Platform
#XFLD: Credentials for User-Provided Service label in space schema dialog
cloudPlatformCredentialsLabel=Kredensial untuk Layanan yang Disediakan Pengguna:
#XFLD: Credentials for User-Provided Service (Without Password) label in space schema dialog
cloudPlatformCredentialsWithoutPasswordLabel=Kredensial untuk Layanan yang Disediakan Pengguna (Tanpa Kata Sandi):
#XFLD: Copy Credentials label in space schema dialog
copyCredentialsLabel=Salin Kredensial Tanpa Kata Sandi
#XFLD: Copy Credentials label in space schema dialog
copyFullCredentialsLabel=Salin Kredensial Lengkap
#XFLD: Copy Password label in space schema dialog
copyPasswordLabel=Salin Kata Sandi
#XMSG: Credentials copied to clipboard
credentialsCopiedMessage=Kredensial disalin ke clipboard
#XMSG: Password copied to clipboard
passwordCopiedMessage=Kata sandi disalin ke clipboard
#XMSG: create database user dialog title
createDatabaseUserDialogTitle=Buat Pengguna Basis Data
#XMSG: Database Users section title
databaseUsers=Pengguna Basis Data
#XMSG: Database User Details dialog title
databaseUserDetailsDialogTitle=Detail Pengguna Basis Data
#XFLD: database user read audit log
databaseUserAuditLogRead=Aktifkan Log Audit untuk Operasi Baca dan Simpan Log untuk
#XFLD: database user change audit log
databaseUserAuditLogChange=Aktifkan Log Audit untuk Operasi Ubah dan Simpan Log untuk
#XMSG: Cloud Platform Access
cloudPlatformAccess=Akses Cloud Platform
#XFLD: Enable Consumption in Cloud Platform hint
enableConsumptionCloudPlatformHint=Atur akses ke kontainer HANA Deployment Infrastructure (HDI) melalui pengguna basis data ini. Untuk terhubung ke kontainer HDI Anda, pemodelan SQL harus diaktifkan
#XFLD: Enable HDI Consumption
enableConsumptionCloudPlatform=Aktifkan Pemakaian HDI
#XFLD: Enable Consumption hint
enableConsumptionHint=Apakah Anda ingin data di ruang Anda dapat dipakai oleh alat atau aplikasi lain?
#XFLD: Enable Consumption
enableConsumption=Aktifkan Pemakaian SQL
#XFLD: Enable Modeling
enableModeling=Aktifkan Pemodelan SQL
#XMSG: Privileges for Data Modeling
privilegesModeling=Pengisian Data
#XMSG: Privileges for Data Consumption
privilegesConsumption=Pemakaian Data untuk Alat Eksternal
#XFLD: SQL Modeling
sqlModeling=Pemodelan SQL
#XFLD: SQL Consumption
sqlConsumption=Pemakaian SQL
#XFLD: enabled
enabled=Diaktifkan
#XFLD: disabled
disabled=Dinonaktifkan
#XFLD: Edit Privileges
editPrivileges=Edit Hak Istimewa
#XFLD: Open Database Explorer
openDBX=Buka Penjelajah Basis Data
#XFLD: create database user hint
databaseCreateHint=Perlu diperhatikan bahwa Anda tidak akan dapat mengubah nama pengguna lagi setelah menyimpan.
#XFLD: Internal Schema Name
internalSchemaName=Nama Skema Internal
#YMSE: Failed to load database users
loadDatabaseUserError=Gagal memuat pengguna basis data
#YMSE: Failed to delete database user
deleteDatabaseUsersError=Gagal menghapus permanen pengguna basis data
#XMSG: Success message after deleting database user
deleteDatabaseUserSuccess=Pengguna basis data dihapus permanen
#XMSG: Success message after deleting database users
deleteDatabaseUsersSuccess=Pengguna basis data dihapus permanen
#XMSG: Success message after creating database user
createDatabaseUserSuccessMessage=Pengguna basis data dibuat
#YMSE: Failed to create database user
createDatabaseUserError=Gagal membuat pengguna basis data
#XMSG: Success message after updating database user
updateDatabaseUserSuccessMessage=Pengguna basis data diperbarui
#YMSE: Failed to update database user
updateDatabaseUserError=Gagal memperbarui pengguna basis data
#XFLD: HDI Consumption
hdiConsumption=Pemakaian HDI
#XTIT: Space detail section Database Access title
detailsSectionDatabaseAccess=Akses Basis Data
#XTXT: Database users consumption hint
databaseUsersConsumptionHint=Jadikan data ruang Anda dapat dipakai secara default. Model-model dalam pembuat akan secara otomatis memungkinkan data dapat dipakai.
#XFLD: Default Consumption of Space Data:
defaultConsumptionOfSpaceData=Pemakaian Default Data Ruang:
#XFLD: Database User Name
databaseUserName=Nama Pengguna Basis Data
#XMSG: Database User creation validation error message
databaseUserValidationError=Tampaknya beberapa bidang tidak valid. Silakan periksa bidang yang diperlukan untuk melanjutkan.
#XMSG: SQL modeling can’t be enabled since this user has been migrated.
migratedDatabaseUserHint=Pengisian data tidak dapat diaktifkan karena pengguna ini telah dimigrasi.
#XBUT: Remove Button Text
remove=Hapus
#XBUT: Remove Spaces Button Text
removeSpaces=Hapus Ruang
#XBUT: Remove Objects Button Text
removeObjects=Hapus Objek
#XMSG: No members have been added yet.
noMembersAssigned=Belum ada anggota yang ditambahkan.
#XMSG: No users have been added yet.
noUsersAssigned=Belum ada pengguna yang ditambahkan.
#XMSG: No database users have been created or your filter is not showing any data.
noDatabaseUsers=Belum ada pengguna basis data yang dibuat, atau filter Anda tidak menunjukkan data apa pun.
#XMSG: Please enter a user name.
noDatabaseUsername=Silakan masukkan nama pengguna.
#XMSG: The user name is too long. Please use a shorter one.
databaseUsernameTooLong=Nama pengguna terlalu panjang. Silakan gunakan nama yang lebih pendek.
#XMSG: No privileges have been enabled and this database user will have limited functionality. Do you still want to continue?
noPrivilegesConfirmationMessage=Belum ada hak istimewa yang diaktifkan, dan pengguna basis data ini akan memiliki fungsi yang terbatas. Apakah Anda masih ingin melanjutkan?
#XMSG: To enable audit logs for change operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogChangeMessage=Untuk mengaktifkan log audit untuk operasi perubahan, pengambilan data juga harus diaktifkan. Apakah Anda ingin melakukan ini?
#XMSG: To enable audit logs for read operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogReadMessage=Untuk mengaktifkan log audit untuk operasi baca, pengambilan data juga harus diaktifkan. Apakah Anda ingin melakukan ini?
#XMSG: To enable HDI consumption data ingestion and data consumption needs to be enabled. Do you want to enable all?
enableHDIConsumptionMessage=Untuk mengaktifkan penggunaan HDI, pengambilan data dan penggunaan data juga harus diaktifkan. Apakah Anda ingin melakukan ini?
#XMSG:
databaseUserPasswordText=Untuk mengatur koneksi ke pengguna basis data ini, silakan salin kata sandi Anda. Jika Anda lupa kata sandi, Anda dapat meminta kata sandi baru kapan saja.
#XTIT: Space detail section members title
detailsSectionMembers=Anggota
#XMSG: New password set
newPasswordSet=Kata sandi baru ditentukan
#XFLD: Data Ingestion
dataIngestion=Pengisian Data
#XFLD: Data Consumption
dataConsumption=Pemakaian Data
#XFLD: Privileges
privileges=Hak Istimewa
#XFLD: Enable Data ingestion
enableDataIngestion=Aktifkan Pengisian Data
#XMSG: Log the read and change operations for data ingestion.
databaseUserAuditHint=Catat operasi baca dan ubah untuk pengisian data.
#XMSG: Make your space data available in your HDI containers.
databaseUserHDIHint=Sediakan data ruang di kontainer HDI Anda.
#XFLD: Enable Data consumption
enableDataConsumption=Aktifkan Pemakaian Data
#XMSG: Allow other apps or tools to consume your space data.
enableDataConsumptionHint=Izinkan aplikasi atau tool lain menggunakan data ruang Anda.
#XMSG: To set up access via this schema user copy the credentials to your user-provided service. In case you can only copy the credentials without a password make sure to add the password later.
databaseUserHDIConsumptionHint=Untuk mengatur akses melalui pengguna basis data ini, salin kredensial ke layanan yang disediakan pengguna. Jika Anda hanya dapat menyalin kredensial tanpa kata sandi, pastikan bahwa Anda menambahkan kata sandi nanti.
#XFLD: Data Flow Runtime Capacity label in space overview
dataFlowRuntimeCapacity=Kapasitas Runtime Aliran Data ({0}:{1} jam dari {2} jam)
#XFLD: Could not load Data Flow Runtime Capacity
dataFlowRuntimeCapacityError=Tidak dapat memuat kapasitas runtime aliran data
#XMSG: User can grant data consumption to other users.
databaseUserConsumptionWithGrantOptionHint=Pengguna dapat memberikan pemakaian data kepada pengguna lainnya.
#XFLD: Enable data consumption with grant option
enableConsumptionWithGrantOption=Aktifkan Pemakaian Data dengan Opsi Pemberian
#XMSG: To enable data consumption with grant option data consumption needs to be enabled. Do you want to enable both?
enableConsumptionWithGrantMessage=Untuk memungkinkan pemakaian data dengan opsi pemberian, pemakaian data harus diaktifkan. Apakah Anda ingin mengaktifkan keduanya?
#XFLD: Enable Automated Predictive Library (APL) and Predictive Analysis Library (PAL)
allowUsingScriptServerFunctionality=Aktifkan Pustaka Prediktif Otomatis (Automated Predictive Library - "APL") dan Pustaka Analisis Prediktif (Predictive Analysis Library - "PAL")
#XMSG: User can use SAP HANA Cloud embedded machine learning functions.
allowUsingScriptServerFunctionalityHint=Pengguna dapat menggunakan fungsi pembelajaran mesin tersemat SAP HANA Cloud.
#XFLD: Password Policy
passwordPolicy=Kebijakan Kata Sandi
#XMSG: Password Policy
passwordPolicyHint=Aktifkan atau nonaktifkan kebijakan kata sandi yang dikonfigurasi di sini.
#XFLD: Enable Password Policy
enablePasswordPolicy=Aktifkan Kebijakan Kata Sandi
#XMSG: Read Access to the Space Schema
readAccessTitle=Akses Baca ke Skema Ruang
#XMSG: read access hint
readAccessHint=Izinkan pengguna basis data untuk menghubungkan alat eksternal ke skema ruang dan membaca tampilan yang dipaparkan untuk pemakaian.
#XFLD: Space Schema
spaceSchema=Skema Ruang
#XFLD: Enable Read Access (SQL)
enableReadAccess=Aktifkan Akses Baca (SQL)
#XMSG: Allow the user to grant read access to other users.
withGrantOptionHint=Izinkan pengguna untuk memberikan akses baca kepada pengguna lain.
#XFLD: With Grant Option
withGrantOption=Dengan Opsi Pemberian
#XMSG: Make your space data available in your HDI containers.
enableHDIConsumptionHint=Sediakan data ruang di kontainer HDI Anda.
#XFLD: Enable HDI Consumption
enableHDIConsumption=Aktifkan Pemakaian HDI
#XMSG: Write Access to the User’s Open SQL Schema
writeAccessTitle=Akses Tulis untuk Skema SQL Terbuka milik Pengguna
#XMSG: write access hint
writeAccessHint=Izinkan pengguna basis data untuk menghubungkan alat eksternal ke skema SQL Terbuka milik pengguna untuk membuat entitas data dan mengambil data untuk digunakan dalam ruang.
#XFLD: Open SQL Schema
openSQLSchema=Skema SQL Terbuka
#XFLD: Enable Write Access (SQL DDL & DML)
enableWriteAccess=Aktifkan Akses Tulis (SQL, DDL, & DML)
#XMSG: audit hint
auditHint=Catat operasi baca dan perubahan dalam skema SQL Terbuka.
#XMSG: data consumption hint
dataConsumptionHint=Paparkan semua tampilan yang baru dalam ruang secara default untuk pemakaian. Pembuat model dapat menimpa pengaturan ini untuk tampilan individu melalui pengalih “Paparkan untuk Pemakaian” dalam panel sisi output tampilan. Anda juga dapat memilih format di mana tampilan dipaparkan.
#XFLD: Expose for Consumption by Default
exposeDataConsumption=Paparkan untuk Pemakaian secara Default
#XMSG: database users hint consumption hint
databaseUsersHint2New=Buat pengguna basis data untuk menghubungkan alat eksternal ke SAP Datasphere. Tetapkan hak istimewa guna memungkinkan pengguna untuk membaca data ruang dan untuk membuat entitas data (DDL) dan mengambil data (DML) untuk digunakan dalam ruang.
#XFLD: Read
read=Baca
#XFLD: Read (HDI)
readHDI=Baca (HDI)
#XFLD: Write
write=Tulis
#XMSG: HDI Containers Hint
HDIContainersHint2=Aktifkan akses ke kontainer SAP HANA Deployment Infrastructure (HDI) Anda di ruang Anda. Pembuat Model dapat menggunakan artefak HDI sebagai sumber untuk tampilan dan klien HDI dapat mengakses data ruang Anda.
#XMSG: Open info dialog
openDatabaseUserInfoDialog=Buka dialog info
#XMSG: Database user is locked. Open info dialog to unlock
openLockedDatabaseUserInfoDialog=Pengguna basis data terkunci. Untuk membuka kunci, buka dialog
#XFLD: Table
table=Tabel
#XFLD: Space section partner configuration title
partnerConnectionConfigurationSectionTitle=Koneksi Mitra
#XFLD: Space section partner configuration subtitle
partnerConnectionConfigurationSectionSubTitle=Konfigurasi Koneksi Mitra
#XFLD: Space section partner configuration hint
partnerConnectionConfigurationSectionHint=Tentukan ubin koneksi mitra Anda sendiri dengan menambahkan ikon dan URL iFrame Anda. Konfigurasi ini tersedia hanya untuk penyewa ini.
#XFLD: Table Name Field
partnerConnectionConfigurationName=Nama Ubin
#XFLD: Table iFrame URL field
partnerConnectionConfigurationiFrameURL=URL iFrame
#XFLD: Table iFrame Post Message Origin field
partnerConnectionConfigurationiFramePostMessageOrigin=Asal Posting Pesan iFrame
#XFLD: Table icon/image name
partnerConnectionConfigurationIcon=Ikon
#XMSG: Table no result found
partnerConnectionConfigurationNotfound=Tidak ada konfigurasi(-konfigurasi) koneksi mitra yang dapat ditemukan.
#XMSG: Partner Connection Confgurations are not available
partnerConnectionConfigurationNotAvailable=Konfigurasi koneksi mitra tidak dapat ditampilkan jika basis data runtime tidak tersedia.
#XFLD: Dialog Create Partner Connection Configuration
partnerConnectionConfigurationDialogTitleAdd=Buat Konfigurasi Koneksi Mitra
#XFLD: Dialog Create Partner Connection Configuration Upload Icon
partnerConnectionConfigurationButtonUloadPartnerIcon=Unggah Ikon
#XFLD: Dialog Create Partner Connection Configuration Select
partnerConnectionConfigurationSelectIcon=Pilih (ukuran maksimum sebesar 200KB)
#XFLD: Dialog Create Partner Connection Configuration Enter a name
partnerConnectionConfigurationNamePlaceholder=Contoh Ubin Mitra
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL
partnerConnectionConfigurationiFrameURLPlaceholder=http://www.example.com
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL Check
partnerConnectionConfigurationiFramePostMessageOriginPlaceholder=.example.com
#XFLD: Dialog Create Partner Connection Configuration Browse
partnerConnectionConfigurationBrowseButton=Telusuri
#XFLD: Create Partner Connection Configuration Msg
partnerConnectionConfigurationUploadMsg=Konfigurasi Koneksi Mitra berhasil dibuat.
#XMSG: Error while deleting Partner Connection configuration(s)
partnerConnectionConfigurationDeleteError=Terjadi kesalahan saat menghapus konfigurasi(-konfigurasi) Koneksi Mitra.
#XMSG: Partner Connection Configuration has been successfully deleted
partnerConnectionConfigurationDeleteSuccess=Konfigurasi Koneksi Mitra berhasil dihapus permanen.
#XMSG: Error while retrieving Partner Connection Configurations
partnerConnectionConfigurationLoadError=Terjadi kesalahan saat mengambil Konfigurasi Koneksi Mitra.
#XMSG: Error fileSizeLimitExceeded
partnerConnectionConfigurationfileSizeLimitExceeded=File tidak dapat diunggah karena melampaui ukuran maksimum 200KB.
#XBUT: Create Partner Connection Configuration
partnerConnectionConfigurationButtonCreate=Buat Konfigurasi Koneksi Mitra
#XBUT: Delete Partner Connection Configuration
partnerConnectionConfigurationButtonDelete=Hapus Permanen Konfigurasi Koneksi Mitra.
#XMSG: Partner Tile Creation Failure
upsertPartnerTileFailed=Ubin Mitra tidak dapat dibuat.
#XMSG: Partner Tile Deletion Failure
deletePartnerTileFailed=Ubin Mitra tidak dapat dihapus permanen.
#XMSG: HANA cloud connector settings reset failure
resetHANASCCSettingsFailed=Gagal mengatur ulang Pengaturan Konektor Cloud SAP HANA Pelanggan
#XFLD: Workload Class
workloadClass=Kelas Beban Kerja
#XFLD: Workload Management
workloadManagement=Manajemen Beban Kerja
#XFLD: Priority
workloadClassPriority=Prioritas
#XMSG:
workloadManagementPriorityHint=Anda dapat menentukan prioritas ruang ini saat melakukan kueri basis data. Masukkan nilai dari 1 (prioritas terendah) hingga 8 (prioritas tertinggi). Saat ruang bersaing untuk utas yang tersedia, ruang dengan prioritas lebih tinggi dieksekusi sebelum ruang dengan prioritas lebih rendah.
#XMSG:
workloadClassPriorityHint=Anda dapat menentukan prioritas ruang dari 0 (terendah) hingga 8 (tertinggi). Pernyataan ruang dengan prioritas tinggi dilaksanakan sebelum pernyataan ruang lain dengan prioritas yang lebih rendah. Prioritas default adalah 5. Karena nilai 9 dicadangkan untuk operasi sistem, maka nilai ini tidak tersedia untuk ruang.
#XFLD: Statement Limits
workloadclassStatementLimits=Batas Pernyataan
#XFLD: Workload Configuration
workloadConfiguration=Konfigurasi Beban Kerja
#XMSG:
workloadClassStatementLimitsHint=Anda dapat menentukan jumlah maksimum (atau persentase) utas dan GB memori yang dapat digunakan oleh pernyataan yang sedang dieksekusi secara bersamaan di ruang tersebut. Anda dapat memasukkan nilai atau persentase apa pun antara 0 (tanpa batas) dan total memori dan utas yang tersedia di penyewa. \n\n Jika Anda menentukan batas utas, ketahuilah bahwa hal tersebut dapat menurunkan kinerja. \n\n Jika Anda menentukan batas memori, pernyataan yang mencapai batas memori tidak akan dieksekusi.
#XMSG:
workloadClassStatementLimitsDescription=Konfigurasi default memberikan batas sumber daya yang besar, sekaligus mencegah satu ruang apa pun agar tidak membebani sistem secara berlebihan.
#XMSG:
workloadClassStatementLimitCustomDescription=Anda dapat menetapkan batas maksimum total utas dan memori yang dapat digunakan oleh pernyataan yang sedang dieksekusi secara bersamaan di ruang tersebut.
#XMSG:
totalStatementThreadLimitHelpText=Mengatur batas utas terlalu rendah dapat memengaruhi kinerja pernyataan, sementara nilai yang terlalu tinggi atau 0 dapat memungkinkan ruang untuk menggunakan semua utas sistem yang tersedia.
#XMSG:
totalStatementMemoryLimitHelpText=Mengatur batas memori terlalu rendah dapat menyebabkan masalah kehabisan memori, sementara nilai yang terlalu tinggi atau 0 dapat memungkinkan ruang untuk menggunakan semua memori sistem yang tersedia.
#XMSG:
totalStatementThreadLimitHelpTextRestricted=Masukkan persentase antara 1% dan 70% (atau jumlah yang setara) dari total jumlah utas yang tersedia di penyewa Anda. Mengatur batas utas terlalu rendah dapat memengaruhi kinerja pernyataan, sementara nilai yang terlalu tinggi dapat memengaruhi kinerja pernyataan di ruang lain.
#XMSG:
totalStatementThreadLimitHelpTextDynamic=Masukkan persentase antara 1% dan {0}% (atau jumlah yang setara) dari total jumlah utas yang tersedia di penyewa Anda. Mengatur batas utas terlalu rendah dapat memengaruhi kinerja pernyataan, sementara nilai yang terlalu tinggi dapat memengaruhi kinerja pernyataan di ruang lain.
#XMSG:
totalStatementMemoryLimitHelpTextNew=Masukkan nilai atau persentase antara 0 (tanpa batas) dan jumlah total memori yang tersedia di penyewa Anda. Mengatur batas memori yang terlalu rendah dapat memengaruhi kinerja pernyataan, sementara nilai yang terlalu tinggi dapat memengaruhi kinerja pernyataan di ruang lain.
#XFLD: Total Statement Thread Limit
totalStatementThreadLimit=Total Batas Utas Pernyataan
#XFLD: Segmented button label
workloadclassPercentage=%
#XFLD: Segmented button label
workloadclassGigaByte=GB
#XFLD, 80: Segmented button label
workloadclassThreads=Utas
#XFLD: Total Statement Memory Limit
totalStatementMemoryLimit=Total Batas Memori Pernyataan
#YMSE: Error while loading customer hana info
loadCustomerHanaInfoError=Gagal memuat info SAP HANA pelanggan.
#XMSG:
minimumLimitReached=Batas minimum tercapai.
#XMSG:
maximumLimitReached=Batas maksimum tercapai.
#XMSG: Name Taken for Technical Name
technical-name-taken=Koneksi dengan nama teknis yang Anda masukkan sudah ada. Harap masukkan nama lain.
#XMSG: Name Too long for Technical Name
technical-name-too-long=Nama teknis yang Anda masukkan melebihi 40 karakter. Harap masukkan nama dengan jumlah karakter yang lebih sedikit.
#XMSG: Technical name field empty
technical-name-field-empty=Masukkan nama teknis.
#XMSG: Generic error when user enters invalid characters for a field
field-invalid-characters=Anda hanya dapat menggunakan huruf (a-z), angka (0-9), dan garis bawah (_) untuk nama tersebut.
#XMSG: Generic error when user enters invalid initial characters for a field
field-underscore-trim=Nama yang Anda masukkan tidak boleh dimulai atau diakhiri dengan garis bawah (_).
#XFLD: Checkbox label for Enable Statement Limits
enableStatementLimits=Aktifkan Batas Pernyataan
#XFLD: Connection SubSection Settings Title in space details
connectionSettings=Pengaturan
#XMSG: Connections tool hint in Space details section
connectionsToolHint=Untuk membuat atau mengedit koneksi, buka aplikasi Koneksi dari navigasi samping atau klik di sini:
#XBUT: Connection list Open Connection Tool Button
button-connectiontool=Buka Koneksi
#XFLD: Not deployed label on space tile
notDeployedLabel=Ruang belum disebarkan.
#XFLD: Not deployed additional text on space tile
notDeployedText=Harap menyebarkan ruang.
#XFLD: Corrupt space label on space tile
corruptSpace=Terjadi kesalahan.
#XFLD: Corrupt space additional text on space tile
corruptSpaceHint=Coba sebar ulang atau hubungi dukungan
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Data Log Audit
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Data Administratif
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Data Lain
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Data dalam Ruang
#XMSG: Unlock Space confirmation text
unlockSpaceConfirmation=Apakah Anda benar-benar ingin membuka kunci ruang?
#XMSG: Lock Space confirmation text
lockSpaceConfirmation=Apakah Anda benar-benar ingin mengunci ruang?
#XFLD: Lock
lock=Kunci
#XFLD: Unlock
unlock=Buka Kunci
#XFLD: Locking
locking=Mengunci
#XMSG: Success message after locking space
lockSpaceSuccessMsg=Ruang dikunci
#XMSG: Success message after unlocking space
unlockSpaceSuccessMsg=Ruang dibuka
#XMSG: Success message after locking spaces
lockSpacesSuccessMsg=Ruang dikunci
#XMSG: Success message after unlocking spaces
unlockSpacesSuccessMsg=Ruang dibuka
#YMSE: Error while locking a space
lockSpaceError=Ruang tidak dapat dikunci.
#YMSE: Error while unlocking a space
unlockSpaceError=Ruang tidak dapat dibuka.
#XTIT: popup title Warning
confirmationWarningTitle=Peringatan
#XMSG: Message strip message when a space was locked manually
manualLockedMessage=Ruang telah dikunci secara manual.
#XMSG: Message strip message when a space was locked due to the amount of generated audit logs
auditLogLockedMessage=Ruang telah dikunci oleh sistem karena log audit memakai banyak GB dari disk.
#XMSG: Message strip message when a space was locked because the assigned quota was exceeded.
quotaLockedMessage=Ruang telah dikunci oleh sistem karena melebihi alokasi memori atau penyimpanan disk.
#XMSG: Unlock Spaces confirmation text
unlockSpacesConfirmation=Apakah Anda benar-benar ingin membuka kunci ruang yang dipilih?
#XMSG: Lock Spaces confirmation text
lockSpacesConfirmation=Apakah Anda benar-benar ingin mengunci ruang yang dipilih?
#XTIT: Scoped role editor dialog title
scopedRoleEditorDialogTitle=Editor Peran Tercakup
#XTIT: ECN Management title
ecnManagementTitle=Manajemen Node Komputasi Ruang dan Komputasi Elastis
#XFLD: ECNs
ecns=Node Komputasi Elastis
#XFLD: ECN phase Ready
ecnReady=Siap
#XFLD: ECN phase Running
ecnRunning=Sedang Dieksekusi
#XFLD: ECN phase Initial
ecnInitial=Belum Siap
#XFLD: ECN phase Starting
ecnStarting=Memulai
#XFLD: ECN phase Starting Failed
ecnStartingFailed=Gagal Memulai
#XFLD: ECN phase Stopping
ecnStopping=Menghentikan
#XFLD: ECN phase Stopping Failed
ecnStoppingFailed=Gagal Menghentikan
#XBTN: Assign Button
assign=Tambahkan Ruang
#XBTN: Start Header-Button
start=Mulai
#XBTN: Update Header-Button
repair=Perbarui
#XBTN: Stop Header-Button
stop=Hentikan
#XFLD: ECN hours remaining
ecnHoursRemaining=Sisa 1000 jam lagi
#XFLD: ECN block-hours remaining
ecnBlockHoursRemaining=Sisa {0} Blok Jam
#XFLD: ECN block-hour remaining
ecnBlockHourRemaining=Sisa {0} Blok Jam
#XTIT: ECN creation dialog title
ecnCreationDialogTitle=Buat Node Komputasi Elastis
#XTIT: ECN edit dialog title
ecnEditDialogTitle=Edit Node Komputasi Elastis
#XTIT: ECN deletion dialog title
ecnDeletionDialogTitle=Hapus Permanen Node Komputasi Elastis
#XTIT: ECN Space assignment dialog title
ecnSpaceAssignmentDialogTitle=Tambahkan Ruang
#XFLD: ECN ID
ECNIDLabel=Node Komputasi Elastis
#XTXT: Selected toolbar text
selectedToolbarText=Dipilih: {0}
#XTIT: Elastic Compute Nodes
ECNslong=Node Komputasi Elastis
#XTIT: Object assignment - Table header: Objects available
objectsAvailable=Jumlah Objek
#XTIT: Object assignment - Dialog header text
selectObjects=Pilih ruang dan objek yang ingin Anda tetapkan ke node komputasi elastis:
#XTIT: Object assignment - Table header title: Objects
objects=Objek
#XTIT: Object assignment - Table header: Type
type=Tipe
#XMSG: Message strip delete database user audit warning
deleteDatabaseUserWarningMessageStrip=Perlu diketahui bahwa menghapus permanen pengguna basis data akan mengakibatkan terhapusnya semua entri log audit yang dibuat secara permanen. Jika Anda ingin menyimpan log audit, pertimbangkan untuk mengekspornya sebelum menghapus permanen pengguna basis data.
#XMSG: Message strip delete HDI container audit logs impact
deleteHDIContainerWarningMessageStrip=Perlu diketahui bahwa membatalkan penugasan kontainer HDI dari ruang akan mengakibatkan terhapusnya semua entri log audit yang dibuat. Jika Anda ingin menyimpan log audit, pertimbangkan untuk mengekspornya sebelum membatalkan penugasan kontainer HDI.
#XTXT: All audit logs
allAuditLogs=Semua entri log audit yang dibuat untuk ruang tersebut
#XMSG: Message strip disable audit warning
disableAuditWarningMessageStrip=Perlu diketahui bahwa menonaktifkan kebijakan audit (operasi baca atau ubah) akan mengakibatkan terhapusnya semua entri log audit. Jika Anda ingin menyimpan entri log audit, pertimbangkan untuk mengekspornya sebelum menonaktifkan kebijakan audit.
#XTIT: No Spaces assigned to ECN Illustration title
noSpacesECNTitle=Belum ada ruang atau objek yang ditetapkan
#XTIT: No Spaces assigned to ECN Illustration description
noSpacesECNDescription=Untuk menggunakan node komputasi elastis Anda, tetapkan ruang atau objek untuknya.
#XTIT: No Spaces Illustration title
noSpacesTitle=Belum ada ruang yang dibuat
#XTIT: No Spaces Illustration description
noSpacesDescription=Untuk menggunakan perolehan data, buat ruang.
#XTIT: No Spaces Illustration title
noDeletedSpacesTitle=Tempat sampah kosong
#XTIT: No Spaces Illustration description
noDeletedSpacesDescription=Anda dapat memulihkan ruang yang telah Anda hapus permanen dari sini.
#XTXT:  {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDatabaseUsers=Setelah ruang disebarkan, pengguna basis data berikut akan dihapus permanen {0} dan tidak dapat dipulihkan:
#XTIT: Delete database users
deleteDatabaseUsersTitle=Hapus Permanen Pengguna Basis Data
#YMSE: Error if ecn id already exists
ecnIDInputDuplicateError=ID sudah ada.
#YMSE: Error if ecn id contains invalid characters
ecnIDInputInvalidError=Silakan gunakan karakter huruf kecil dari a - z dan angka 0 – 9 saja
#YMSE: Error if ecn id is too short
ecnIDInputTooShortError=ID harus terdiri dari setidaknya {0} karakter.
#XMSG: ecn id length warning
ecnIdLengthWarning=Melampaui karakter maksimum sebesar {0}.
#XFLD: open System Monitor
systemMonitor=Pemantau Sistem
#XFLD: open ECN schedule dialog menu entry
schedule=Jadwal
#XFLD: open create ECN schedule dialog
createSchedule=Buat Jadwal
#XFLD: open change ECN schedule dialog
changeSchedule=Edit Jadwal
#XFLD: open delete ECN schedule dialog
deleteSchedule=Hapus Permanen Jadwal
#XFLD: open self assign ECN schedule dialog
selfAssignSchedule=Tetapkan Jadwal untuk Saya
#XFLD: open pause ECN schedule dialog
pauseSchedule=Jeda Jadwal
#XFLD: open resume ECN schedule dialog
resumeSchedule=Lanjutkan Jadwal
#XFLD: View Logs
viewLogs=Tampilkan Log
#XFLD: Compute Blocks
computeBlocks=Blok Komputasi
#XFLD: Memory label in ECN creation dialog
ecnMemory=Memori (GB)
#XFLD: Storage label in ECN creation dialog
ecnStorage=Penyimpanan (GB)
#XFLD: Number of CPU label in ECN creation dialog
ecnCPUCount=Jumlah CPU
#XFLD: ECN updated by label
changedBy=Diubah Oleh
#XFLD: ECN updated on label
changedOn=Diubah Pada
#XMSG: Success message after creating a Elastic Compute Node
createEcnSuccessMessage=Node komputasi elastis dibuat
#YMSE: Error while creating a Elastic Compute Node
createEcnError=Node komputasi elastis tidak dapat dibuat
#XMSG: Success message after updating a Elastic Compute Node
updateEcnSuccessMessage=Node komputasi elastis diperbarui
#XMSG: Error while updating a Elastic Compute Node
udpateEcnError=Node komputasi elastis tidak dapat diperbarui
#XMSG: Success message after deleting a Elastic Compute Node
deleteEcnSuccessMessage=Node komputasi elastis dihapus permanen
#YMSE: Error while deleting a Elastic Compute Node
deleteEcnError=Node komputasi elastis tidak dapat dihapus permanen
#XMSG: Success message after starting a Elastic Compute Node
startingEcnSuccessMessage=Memulai node komputasi elastis
#XMSG: Success message after stopping a Elastic Compute Node
stoppingEcnSuccessMessage=Menghentikan node komputasi elastis
#YMSE: Error while starting a Elastic Compute Node
startingEcnError=Node komputasi elastis tidak dapat dimulai
#YMSE: Error while stopping a Elastic Compute Node
stoppingEcnError=Node komputasi elastis tidak dapat dihentikan
#XBUT: Add Object button for an ECN
assignObjects=Tambahkan Objek
#XFLD: Enable auto object assignment label
enableAutoObjAssignment=Tambahkan Semua Objek Secara Otomatis
#XFLD: object type label to be assigned
objectTypeLabel=Tipe (Penggunaan Semantik)
#XFLD: assigned object type label
assignedObjectTypeLabel=Tipe
#XFLD: technical name label
TechnicalNameLabel=Nama Teknis
#XFLD: Object selecttion ECN label
objectSelectionECNLabel=Pilih objek yang ingin Anda tambahkan ke node komputasi elastis
#XTIT: Add objects dialog title
assignObjectsTitle=Tetapkan Objek
#XFLD: object label with object count
objectLabel=Objek
#XMSG: No objects available to add message.
noObjectsToAssign=Tidak ada objek yang tersedia untuk ditetapkan.
#XMSG: No objects assigned message.
noAssignedObjects=Tidak ada objek yang ditetapkan.
#XTIT: Remove objects popup title
removeObjectsOrSpacesConfirmationTitle=Peringatan
#XTIT: Delete ECN popup button and title
deleteECNConfirmationButtonAndTitle=Hapus Permanen
#XMSG: Remove objects popup text
removeObjectsConfirmation=Apakah Anda yakin ingin menghapus objek yang dipilih?
#XMSG: Remove spaces popup text
removeSpacesConfirmation=Apakah Anda yakin ingin menghapus ruang yang dipilih?
#XBUT: Confirmation button for objects dialog
removeObjectsOrSpacesConfirmationBtn=Hapus Ruang
#XMSG: Success message after removing objects
removeObjectsSuccessMessage=Objek yang diekspos telah dihapus
#XMSG: Success message after assigning objects
assignObjectsSuccessMessage=Objek yang diekspos telah ditetapkan
#XFLD: auto object assignment label for space tile
autoObjAssignmentLabel=Semua Objek yang Diekspos
#XFLD: Spaces tab label
spacesTabLabel=Ruang
#XFLD: Exposed objects tab label
consumptionObjectsTabLabel=Objek yang Diekspos
#XMSG: Success message after unassigning spaces
spacesUnassignedSuccessMessage=Ruang telah dihapus
#XMSG: Success message after unassigning space
spaceUnassignedSuccessMessage=Ruang telah dihapus
#YMSE: Error while assigning / unassigning spaces
spacesAssignUnassignError=Ruang tidak dapat ditetapkan atau dihapus.
#YMSE: Error while removing objects
removeObjectsError=Kami tidak dapat menetapkan atau menghapus objek.
#YMSE: Error while removing object
removeObjectError=Kami tidak dapat menetapkan atau menghapus objek.
#YMSE: Error when saving the ecn with invalid value
invalidSizingPlanError=Angka yang dipilih sebelumnya tidak valid lagi. Silakan pilih angka yang valid.
#YMSE: Error when saving the ecn with invalid value
invalidPerformanceClassError=Pilih kelas kinerja yang valid.
#YMSE: Error when saving the ecn with invalid value
performanceClassNotAvailableError=Kelas kinerja "{0}" yang dipilih sebelumnya saat ini tidak valid. Silakan pilih kelas kinerja yang valid.
#XTXT: Delete ECN confirmation text
deleteConfirmationTxtECN=Apakah Anda yakin ingin menghapus permanen node komputasi elastis?
#XFLD: tooltip for ? button
help=Bantuan
#XFLD: ECN edit button label
editECN=Konfigurasikan
#XFLD: Technical type label for ERModel
DWC_ERMODEL=Model Hubungan Entitas
#XFLD: Technical type label for Local Table
DWC_LOCAL_TABLE=Tabel Lokal
#XFLD: Technical type label for Remote Table
DWC_REMOTE_TABLE=Tabel Jarak Jauh
#XFLD: Technical type label for Analytic Model
DWC_ANALYTIC_MODEL=Model Analitik
#XFLD: Technical type label for Task Chain
DWC_TASKCHAIN=Rantai Tugas
#XFLD: Technical type label for Data Flow
DWC_DATAFLOW=Aliran Data
#XFLD: Technical type label for Replication Flow
DWC_REPLICATIONFLOW=Aliran Replikasi
#XFLD: Technical type label for Transformation Flow
DWC_TRANSFORMATIONFLOW=Aliran Perubahan
#XFLD: Technical type label for Intelligent Lookup
DWC_IDT=Pencarian Cerdas
#XFLD: Technical type label for Repository
DWC_REPOSITORY_PACKAGE=Repositori
#XFLD: Technical type label for Enterprise Search
DWC_ESMODEL=Pencarian Perusahaan
#XFLD: Technical type label for View
DWC_VIEW=Tampilkan
#XFLD: Technical type label for Data Product
DWC_DATAPRODUCT=Produk Data
#XFLD: Technical type label for Data Access Control
DWC_DAC=Kontrol Akses Data
#XFLD: Technical type label for Folder
DWC_FOLDER=Folder
#XFLD: Technical type label for Data Entity
DWC_BUSINESS_ENTITY=Entitas Bisnis
#XFLD: Technical type label for Business Entity Variant
DWC_BUSINESS_ENTITY_VARIANT=Varian Entitas Bisnis
#XFLD: Technical type label for Responsibility Scenario
DWC_AUTH_SCENARIO=Skenario Tanggung Jawab
#XFLD: Technical type label for Fact Model
DWC_FACT_MODEL=Model Fakta
#XFLD: Technical type label for Transformation Flow
DWC_PERSPECTIVE=Perspektif
#XFLD: Technical type label for Consumption Model
DWC_CONSUMPTION_MODEL=Model Pemakaian
#XFLD: Technical type label for Remote Connection
DWC_REMOTECONNECTION=Koneksi Jarak Jauh
#XFLD: Technical type label for Fact Model Variant
DWC_FACT_MODEL_VARIANT=Varian Model Fakta
#XMSG: Schedule created alert message
createScheduleSuccess=Jadwal dibuat
#XMSG: Schedule updated alert message
updateScheduleSuccess=Jadwal diperbarui
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Jadwal dihapus permanen
#XMSG: Schedule takeover success message
ownerChangeScheduleSuccess=Jadwal dialihkan kepada Anda
#XMSG: Message for starting pausing of schedule
pauseScheduleStarted=Menjeda 1 jadwal
#XMSG: Message for starting resuming of schedule
resumeScheduleStarted=Melanjutkan 1 jadwal
#XFLD: Segmented button label
availableSpacesButton=Tersedia
#XFLD: Segmented button label
selectedSpacesButton=Terpilih
#XFLD: Visit website button text
visitWebsite=Kunjungi Situs Web
#XMSG: Warning message on unselecting source language
sourceLanguageRemovalWarning=Bahasa sumber yang dipilih sebelumnya akan dihapus.
#XBUT: enable translation confirmation button
enableTranslationConfirmationBtn=Aktifkan
#XFLD: ECN performance class label
performanceClassLabel=Kelas Kinerja
#XTXT performance class memory text
memoryText=Memori
#XTXT performance class compute text
computeText=Komputasi
#XTXT performance class high-compute text
highComputeText=Komputasi Tinggi
#XBUT: Recycle Bin Button Text
recycleBin=Tempat Sampah
#XBUT: Restore Button Text
restore=Pulihkan
#XMSG: Warning message for new Workload Management UI
priorityWarning=Area ini dalam mode hanya baca. Anda dapat mengubah prioritas ruang di area Sistem / Konfigurasi / Manajemen Beban Kerja.
#XMSG: Warning message for new Workload Management UI
workloadWarning=Area ini dalam mode hanya baca. Anda dapat mengubah konfigurasi beban kerja ruang di area Sistem / Konfigurasi / Manajemen Beban Kerja.
#XCOL: Space table-view column: Apache Spark vCPUs
sparkManagementVCPUs=vCPU Apache Spark
#XCOL: Space table-view column: Apache Spark Memory (GB)
sparkManagementMemory=Memori Apache Spark (GB)
#XCOL: Space type (from Space Capability) for Data Ingestion Spaces
ingestionSpaceType=Pengisian Produk Data
#XFLD: Deployment in progress nodatatext for spark table
sparkTableDeploymentInProgress=Tidak ada data yang tersedia karena saat ini ruang sedang disebarkan
#XFLD: Runtime data being loaded nodatatext for spark table
sparkTableDeploymentLoading=Tidak ada data yang tersedia karena saat ini ruang sedang dimuat
#XCOL: Space type (from Space Capability) for BW Private Cloud Edition
BWPCESpaceType=SAP BW
#XBUT: Go to Instance Mappings Button
navToMappingsButton=Edit Pemetaan Instance
#XFLD: SAP HANA Data Lake Storage label if less than 1GB
dataLakeStorageUsed=< 1 GB
