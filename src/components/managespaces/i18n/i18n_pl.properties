#DO NOT REMOVE________________________
#XFLD: Breadcrumb for space monitoring page
spaceMonitorBreadcrumb=Monitorowanie
#XTXT: Type name for spaces in browser tab page title
space=Przestrzeń
#_____________________________________
#XFLD: Spaces label in
spaces=Przestrzenie
#XFLD: Manage plan button text
manageQuotaButtonText=Zarządzaj planem
#XBUT: Manage resources button
manageResourcesButton=Zarządzaj zasobami
#XFLD: Create space button tooltip
createSpace=Utwórz przestrzeń
#XFLD: Create
create=Utwórz
#XFLD: Deploy
deploy=Wdróż
#XFLD: Page
page=Strona
#XFLD: Cancel
cancel=Anuluj
#XFLD: Update
update=Aktualizuj
#XFLD: Save
save=Zapisz
#XFLD: OK
ok=OK
#XFLD: days
days=Dni
#XFLD: Space tile edit button label
edit=Edytuj
#XFLD: Auto Assign all objects to space
autoAssign=Przypisz automatycznie
#XFLD: Space tile open monitoring button label
openMonitoring=Monitoruj
#XFLD: Delete
delete=Usuń
#XFLD: Copy Space
copy=Kopiuj
#XFLD: Close
close=Zamknij
#XCOL: Space table-view column status
status=Status
#XFLD: Space status active
activeLabel=Aktywne
#XFLD: Space status locked
lockedLabel=Zablokowane
#XFLD: Space status critical
criticalLabel=Krytyczne
#XFLD: Space status cold
coldLabel=Zimne
#XFLD: Space status deleted
deletedLabel=Usunięte
#XFLD: Space status unknown
unknownLabel=Nieznane
#XFLD: Space status ok
okLabel=OK
#XFLD: Database user expired
expired=Wygasłe
#XFLD: deployed
deployed=Wdrożone
#XFLD: not deployed
notDeployed=Niewdrożone
#XFLD: changes to deploy
changesToDeploy=Zmiany do wdrożenia
#XFLD: pending
pending=Wdrażanie
#XFLD: designtime error
designtimeError=Błąd czasu projektowania
#XFLD: runtime error
runtimeError=Błąd czasu wykonania
#XFLD: Space created by label
createdBy=Autor
#XFLD: Space created on label
createdOn=Data utworzenia
#XFLD: Space deployed on label
deployedOn=Wdrożone dnia
#XFLD: Space ID label
spaceID=ID przestrzeni
#XFLD: Priority label
priority=Priorytet
#XFLD: Space Priority label
spacePriority=Priorytet przestrzeni
#XFLD: Space Configuration label
spaceConfiguration=Konfiguracja przestrzeni
#XFLD: Not available
notAvailable=Niedostępne
#XFLD: WorkloadType default
default=Domyślnie
#XFLD: WorkloadType custom
custom=Niestandardowe
#XFLD: Data Lake option label; refers to component SAP HANA Cloud, Data Lake
dataLakeAccess=Dostęp do jeziora danych
#XFLD: Translation label
translationLabel=Tłumaczenie
#XFLD: Source language label
sourceLanguageLabel=Język źródłowy
#XFLD: Translation CheckBox label
translationCheckBox=Włącz tłumaczenie
#XTXT: Popover hint that space has to be deployed to show user details
openDatabaseUserHintPopoverText=Wdróż przestrzeń, aby uzyskać dostęp do szczegółów użytkownika.
#XTXT: Popover hint that space has to be deployed to open database explorer
openDatabaseExplorerHintPopoverText=Wdróż przestrzeń, aby otworzyć eksplorator bazy danych.
#XTXT: Text for when the Data Lake check box is disabled
dataLakeOptionDisabled=Nie można użyć tej przestrzeni w celu uzyskania dostępu do jeziora danych, ponieważ jest ono już wykorzystywane przez inną przestrzeń.
#XFLD: Data Lake check box text; refers to component SAP HANA Cloud, Data Lake
dataLakeCheckBox=Użyj tej przestrzeni, aby uzyskać dostęp do jeziora danych.
#XFLD: Space Priority minimum label extension
low=Niskie
#XFLD: Space Priority maximum label extension
high=Wysokie
#XFLD: Space name label
spaceName=Nazwa przestrzeni
#XFLD: Enable deploy objects checkbox
enableDeployObjects=Rozmieść obiekty
#XTIT: Copy spaces dialog title
copySpaceDialogTitle=Kopiuj {0}
#XTXT Human readable text for unselected source language
unSelectedSourceLanguage=(Nie wybrano)
#XTXT Human readable text for language code "af"
af=afrikaans
#XTXT Human readable text for language code "ar"
ar=arabski
#XTXT Human readable text for language code "bg"
bg=bułgarski
#XTXT Human readable text for language code "ca"
ca=kataloński
#XTXT Human readable text for language code "zh"
zh=chiński (uproszczony)
#XTXT Human readable text for language code "zf"
zf=chiński
#XTXT Human readable text for language code "hr"
hr=chorwacki
#XTXT Human readable text for language code "cs"
cs=czeski
#XTXT Human readable text for language code "cy"
cy=walijski
#XTXT Human readable text for language code "da"
da=duński
#XTXT Human readable text for language code "nl"
nl=niderlandzki
#XTXT Human readable text for language code "en-UK"
en-UK=angielski (Wielka Brytania)
#XTXT Human readable text for language code "en"
en=angielski (USA)
#XTXT Human readable text for language code "et"
et=estoński
#XTXT Human readable text for language code "fa"
fa=perski
#XTXT Human readable text for language code "fi"
fi=fiński
#XTXT Human readable text for language code "fr-CA"
fr-CA=francuski (Kanada)
#XTXT Human readable text for language code "fr"
fr=francuski
#XTXT Human readable text for language code "de"
de=niemiecki
#XTXT Human readable text for language code "el"
el=grecki
#XTXT Human readable text for language code "he"
he=hebrajski
#XTXT Human readable text for language code "hi"
hi=hindi
#XTXT Human readable text for language code "hu"
hu=węgierski
#XTXT Human readable text for language code "is"
is=islandzki
#XTXT Human readable text for language code "id"
id=Bahasa Indonesia
#XTXT Human readable text for language code "it"
it=włoski
#XTXT Human readable text for language code "ja"
ja=japoński
#XTXT Human readable text for language code "kk"
kk=kazachski
#XTXT Human readable text for language code "ko"
ko=koreański
#XTXT Human readable text for language code "lv"
lv=łotewski
#XTXT Human readable text for language code "lt"
lt=litewski
#XTXT Human readable text for language code "ms"
ms=bahasa Melayu
#XTXT Human readable text for language code "no"
no=norweski
#XTXT Human readable text for language code "pl"
pl=polski
#XTXT Human readable text for language code "pt"
pt=portugalski (Brazylia)
#XTXT Human readable text for language code "pt-PT"
pt-PT=portugalski (Portugalia)
#XTXT Human readable text for language code "ro"
ro=rumuński
#XTXT Human readable text for language code "ru"
ru=rosyjski
#XTXT Human readable text for language code "sr"
sr=serbski
#XTXT Human readable text for language code "sh"
sh=serbsko-chorwacki
#XTXT Human readable text for language code "sk"
sk=słowacki
#XTXT Human readable text for language code "sl"
sl=słoweński
#XTXT Human readable text for language code "es"
es=hiszpański
#XTXT Human readable text for language code "es-MX"
es-MX=hiszpański (Meksyk)
#XTXT Human readable text for language code "sv"
sv=szwedzki
#XTXT Human readable text for language code "th"
th=tajski
#XTXT Human readable text for language code "tr"
tr=turecki
#XTXT Human readable text for language code "uk"
uk=ukraiński
#XTXT Human readable text for language code "vi"
vi=wietnamski
#XTIT: Delete spaces confirmation title
deleteConfirmationHeaderText=Usuń przestrzenie
#XTXT: Logically delete space delete confirmation text with space name
logicallyDeleteConfirmationBodyTextWithSpaceName=Czy na pewno chcesz przenieść przestrzeń "{0}" do kosza?
#XTXT: Logically dpace delete confirmation text for multiple spaces
logicallyDeleteConfirmationBodyTextMultiple=Czy na pewno chcesz przenieść wybrane przestrzenie ({0}) do kosza?
#XTXT: Delete space delete confirmation text with space name
deleteConfirmationBodyTextWithSpaceName=Czy na pewno chcesz usunąć przestrzeń "{0}"? Tej czynności nie można cofnąć.
#XTXT: Delete space delete confirmation text for multiple spaces
deleteConfirmationBodyTextMultiple=Czy na pewno chcesz usunąć następującą liczbę wybranych przestrzeni: {0} ? Następująca zawartość zostanie {1} usunięta:
#XTXT: permanently
permanently=trwale
#XTXT: {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDetail=Następująca zawartość zostanie {0} usunięta i nie będzie można jej odtworzyć:
#XTXT: Hint Delete space confirmation text
deleteConfirmationHint=Wprowadź {0}, aby potwierdzić usuwanie.
#XTXT: Delete space confirmation error check spelling
deleteConfirmationErrorCheckSpelling=Sprawdź pisownię i spróbuj ponownie.
#XTXT: All Spaces
allSpaces=Wszystkie przestrzenie
#XTXT: All data
allData=Wszystkie obiekty i dane zawarte w tej przestrzeni
#XTXT: All connections
allConnections=Wszystkie połączenia zdefiniowane w przestrzeni
#XFLD: Space tile selection box tooltip
clickToSelect=Kliknij, aby wybrać
#XTXT: All database users
allDatabaseUsers=Wszystkie obiekty i dane zawarte w schemacie Open SQL powiązanym z tą przestrzenią
#XFLD: remove members button tooltip
deleteUsers=Usuń członków
#XTXT: Space long description text
description=Opis (maksymalnie 4000 znaków)
#XFLD: Add Members button tooltip
addUsers=Dodaj członków
#XFLD: Add Users button tooltip
addUsersTooltip=Dodaj użytkowników
#XFLD: Edit Users button tooltip
editUsersTooltip=Edycja użytkowników
#XFLD: Remove Users button tooltip
removeUsersTooltip=Usuń użytkowników
#XFLD: Searchfield placeholder
filter=Wyszukaj
#XCOL: Users table-view column health
health=Status
#XCOL: Users table-view column access
access=Dostęp
#XFLD: No user found nodatatext
noDataText=Nie znaleziono użytkownika
#XTIT: Members dialog title
selectUserDialogTitle=Dodaj członków
#XTIT: User dialog title
addUserDialogTitle=Dodaj użytkowników
#XTIT: Delete connections confirmation title
deleteConnectionConfirmationHeaderText=Usuń połączenia
#XTIT: Delete single connection confirmation title
deleteConnectionConfirmationHeaderTextSingle=Usuń połączenie
#XTXT: Delete connection delete confirmation text
deleteConnectionConfirmationBodyText=Czy na pewno chcesz usunąć wybrane połączenia? Usunięcie będzie miało trwały skutek.
#XTIT: Remote source dialog title
selectRemoteConnectionDialogTitle=Wybierz połączenia
#XTIT: Share connection dialog title
connectionSharingDialogTitle=Udostępnij połączenie
#XTIT: Connections shared from other spaces table title
sharedConnectionsFromOtherSpaces=Udostępnione połączenia
#XFLD: Add remote source button tooltip
addRemoteConnections=Dodaj połączenia
#XFLD: Delete remote source button tooltip
deleteRemoteConnections=Usuń połączenia
#XFLD: Share remote source button tooltip
shareConnections=Udostępnij połączenia
#XFLD: Tile-layout tooltip
tileLayout=Podzielony układ ekranu
#XFLD: Table-layout tooltip
tableLayout=Układ tabeli
#XMSG: Success message after creating space
createSpaceSuccessMessage=Utworzono przestrzeń
#XMSG: Success message after copying space
copySpaceSuccessMessage=Kopiowanie przestrzeni "{0}" do przestrzeni "{1}"
#XMSG: Success message after deploying space
deploymentSuccessMessage=Rozpoczęto wdrażanie przestrzeni
#XMSG: Success message after Apache Spark update started
sparkUpdateStartedSuccessMessage=Rozpoczęła się aktualizacja Apache Spark
#YMSE: Error while starting Apache Spark update
sparkUpdateStartedErrorMessage=Aktualizacja Apache Spark nie powiodła się
#XMSG: Success message after updating space
updateSettingsSuccessMessage=Zaktualizowano szczegóły przestrzeni
#XMSG: Success message after unlocking space
unlockSpaceSuccessMessage=Tymczasowo odblokowano przestrzeń
#XMSG: Success message after deleting a single space
deleteSpaceSuccessMessage=Usunięto przestrzeń
#XMSG: Success message after deleting multiple spaces
deleteSpacesSuccessMessage=Usunięto przestrzenie
#XMSG: Success message after restoring a single space
restoreSpaceSuccessMessage=Przywrócono przestrzeń
#XMSG: Success message after restoring multiple spaces
restoreSpacesSuccessMessage=Przywrócono przestrzenie
#YMSE: Error while updating settings
updateSettingsFailureMessage=Nie można było zaktualizować ustawień przestrzeni.
#YMSE: Error while updating data lake option
dataLakeEnabledInOtherSpace=Jezioro danych zostało już przypisane do innej przestrzeni. Tylko jedna przestrzeń może uzyskać dostęp do jeziora danych w tym samym czasie.
#YMSE: Error while updating data lake option
virtualTablesExists=Nie możesz anulować przypisania jeziora danych dla tej przestrzeni, ponieważ wciąż istnieją zależności od tabel wirtualnych*. Aby anulować przypisanie jeziora danych dla tej przestrzeni, usuń tabele wirtualne.
#YMSE: Error while unlocking space
unlockSpaceFailureMessage=Nie można było odblokować przestrzeni.
#YMSE: Error while creating space
createSpaceError=Nie można było utworzyć przestrzeni.
#YMSE: Error while creating duplicate space
createDuplicateSpaceError=Przestrzeń o nazwie {0} już istnieje.
#YMSE: Error while deleting a single space
deleteSpaceError=Nie można było usunąć przestrzeni.
#YMSE: Error details while deleting a single space
deleteSpaceDetailedError=Przestrzeń “{0}” nie działa już prawidłowo. Spróbuj ponownie ją usunąć. Jeśli nadal nie będzie działać, poproś administratora o usunięcie swojej przestrzeni lub utwórz zgłoszenie.
#YMSE: Error while deleting a single space in files
deleteSpaceInFilesError=Nie można było usunąć danych przestrzeni w plikach.
#YMSE: Error while deleting a single space users
deleteSpaceUsersError=Nie można było usunąć użytkowników.
#YMSE: Error while deleting a single space schema
deleteSpaceSchemasError=Nie można było usunąć schematów.
#YMSE: Error while deleting a single space connections
deleteSpaceConnectionsError=Nie można było usunąć połączeń.
#YMSE: Error while deleting a single space data
deleteSpaceDataError=Nie można było usunąć danych przestrzeni.
#YMSE: Error while deleting multiple spaces
deleteSpacesError=Nie można było usunąć przestrzeni.
#YMSE: Error while restoring a single space
restoreSpaceError=Nie można było przywrócić przestrzeni.
#YMSE: Error while restoring multiple spaces
restoreSpacesError=Nie można było przywrócić przestrzeni.
#YMSE: Error while creating users
createUsersError=Nie można było dodać użytkowników.
#YMSE: Error while removing users
removeUsersError=Nie mogliśmy usunąć użytkowników.
#YMSE: Error while removing user
removeUserError=Nie można było usunąć użytkownika.
#YMSE: Error assigning roles to a user
roleAssignUnAssignError=Nie można było dodać użytkownika do wybranej roli z zakresu. \n\n Nie można dodać siebie do roli z zakresu. Możesz poprosić swojego administratora, aby dodał Cię do roli z zakresu.
#YMSE: Error assigning user to the space
userAssignError=Nie można było przypisać użytkownika do przestrzeni. \n\n Użytkownik jest już przypisany do maksymalnej dozwolonej liczby (100) przestrzeni w rolach z zakresu.
#YMSE: Error assigning users to the space
usersAssignError=Nie można było przypisać użytkownika do przestrzeni. \n\n Użytkownik jest już przypisany do maksymalnej dozwolonej liczby (100) przestrzeni w rolach z zakresu.
#YMSE: Error while fetching users
fetchUserWithScopedRolesError=Nie można było wywołać użytkowników. Spróbuj ponownie później.
#YMSE: Error while fetching scoped roles
fetchScopedRolesError=Nie można było wywołać ról z zakresu.
#YMSE: Error while fetching members
fetchUserError=Nie można było pobrać członków. Spróbuj ponownie później.
#YMSE: Error while loading run-time database
loadRuntimeError=Nie można było wczytać informacji z bazy danych czasu wykonania.
#YMSE: Error while loading spaces
loadSpacesError=Przepraszamy, wystąpił błąd podczas próby wywołania Twoich przestrzeni.
#YMSE: Error while loading haas resources
loadStorageError=Przepraszamy, wystąpił błąd podczas próby wywołania danych pamięci.
#YMSE: Error no data could be loaded
loadDataError=Przepraszamy, wystąpił błąd podczas próby wywołania Twoich danych.
#XFLD: Click to refresh storage data
clickToRefresh=Kliknij tutaj, aby spróbować ponownie.
#XTIT: Delete space popup title
deleteSpacePopupTitle=Usuń przestrzenie
#XCOL: Spaces table-view column name
name=Nazwa
#XCOL: Spaces table-view deployment status
deploymentStatus=Status wdrożenia
#XFLD: Disk label in space details
storageLabel=Dysk (GB)
#XFLD: In-Memory label in space details
ramLabel=Pamięć (GB)
#XFLD: Memory label on space card
memory=Pamięć do przechowywania
#XFLD: Storage Assignment label in space details
storrageAssignmentLabel=Pamięć przestrzeni
#XFLD: Storage Type label in space details
storageTypeLabel=Rodzaj pamięci
#XFLD: Enable Space Quota
enableSpaceQuota=Włącz limit przestrzeni
#XFLD: No Space Quota
noSpaceQuota=Brak limitu przestrzeni
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
diskAndInMemoryText=Baza danych SAP HANA (dysk oraz in-memory)
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
dataLakeFilesText=SAP HANA, pliki jeziora danych
#XFLD: Available scoped roles label
availableRoles=Dostępne role z zakresu
#XFLD: Selected scoped roles label
selectedRoles=Wybrane role z zakresu
#XCOL: Spaces table-view column models
models=Modele
#XCOL: Spaces table-view column users
users=Użytkownicy
#XCOL: Spaces table-view column connections
connections=Połączenia
#XFLD: Section header overview in space detail
overview=Przegląd
#XFLD: Section header Apache Spark in space detail
sparkManagementSection=Apache Spark
#XFLD: Section header Apache Spark in space detail
sparkManagementSubSection=Aplikacje
#XFLD: Section header Apache Spark in space detail
taskAssignmentSubSection=Przypisanie zadania
#XFLD: vCPU label in Apache Spark section
vCPULabel=vCPU
#XFLD: Memory label in Apache Spark section
memoryLabel=Pamięć (GB)
#XFLD: space configuration label in Apache Spark section
spaceConfigurationLabel=Konfiguracja przestrzeni
#XFLD: Space Source label
sparkApplicationLabel=Aplikacja
#XFLD: Cluster Size label
clusterSizeLabel=Rozmiar klastra
#XFLD: Driver label
driverLabel=Sterownik
#XFLD: Executor label
executorLabel=Wykonawca
#XFLD: max label
maxLabel=Maks. wykorzystanie
#XFLD: TrF Default label
trFDefaultLabel=Wartość domyślna przepływu transformacji
#XFLD: Merge Default label
mergeDefaultLabel=Wartość domyślna scalania
#XFLD: Optimize Default label
optimizeDefaultLabel=Optymalizuj wartość domyślną
#XFLD: Deployment Default label
deploymentDefaultLabel=Wdrożenie lokalnej tabeli (plik)
#XFLD: max spark source label
driverExecutorMaxUsedSparkSource={0} CPU / {1} GB
#XFLD: Object type label
taskObjectTypeLabel=Typ obiektu
#XFLD: Task activity label
taskActivityLabel=Działanie
#XFLD: Task Application ID label
taskApplicationIDLabel=Domyślna aplikacja
#XFLD: Section header in space detail
generalSettings=Ustawienia ogólne
#XMSG: Message strip message when a space is locked
spaceLockedMessageStrip=Ta przestrzeń jest obecnie zablokowana przez system.
#XMSG: Message strip message deploy warning
deployWarningMessageStrip=Zmiany w tej sekcji zostaną wdrożone natychmiast.
#XMSG: Message strip message modify space configuration warning
modifySpaceConfigurationMessageStrip=Zauważ, że zmiana tych wartości może spowodować problemy z wydajnością.
#XFLD: Button text to unlock the space again
unlockSpace=Odblokuj przestrzeń
#XFLD: Info text for audit log formatted message
auditLogText=Włącz logi audytu w celu rejestrowania czynności odczytu lub zmiany (zgodnie z zasadami audytu). Dzięki temu administratorzy będą mogli analizować informacje o tym kto i kiedy, wykonywał dane czynności. 
#XMSG: Message strip  audit warning
auditLogWarningMessageStrip=Logi audytu mogą zużywać dużą ilość pamięci dyskowej tenanta. Jeśli włączysz zasady audytu (czynności odczytu lub zmiany), trzeba będzie regularnie monitorować wykorzystanie pamięci dyskowej (za pośrednictwem karty Wykorzystywana pamięć dyskowa w Monitorze systemu), aby uniknąć przestojów związanych z zapełnieniem dysku, które mogą prowadzić do zakłóceń usługi. Jeśli wyłączysz zasady audytu, wszystkie wpisy logu audytu związane z tymi zasadami zostaną usunięte. Jeśli chcesz zachować wpisy logu audytu, rozważ wyeksportowanie ich przed wyłączeniem zasad audytu.
#XFLD: Message strip link text to open in app help to guide user what to do to unlock his space permanently
unlockSpaceHelp=Pokaż pomoc
#XMSG: Message strip message when a space will be locked again
spaceTemporarilyUnlockedMessageStrip=Ta przestrzeń przekracza swoją pamięć przestrzeni i zostanie zablokowana za {0} {1}.
#XMSG: Unit for remaining time until space is locked again
hours=godz.
#XMSG: Unit for remaining time until space is locked again
minutes=min
#XFLD: Section header in space detail for auditing
detailsSectionAuditing=Audyt
#XFLD: Subsection header in space detail for auditing
auditing=Ustawienia kontroli przestrzeni
#XFLD: Hot space tooltip
hotSpaceCountTooltip=Krytyczne przestrzenie: Wykorzystanie pamięci przekracza 90%.
#XFLD: Green space tooltip
greenSpaceCountTooltip=Zdrowe przestrzenie: Wykorzystanie pamięci mieści się w granicach między 6% a 90%.
#XFLD: Cold space tooltip
coldSpaceCountTooltip=Zimne przestrzenie: Wykorzystanie pamięci nie przekracza 5%.
#XFLD: Critical space tooltip
criticalSpaceCountTooltip=Krytyczne przestrzenie: Wykorzystanie pamięci przekracza 90%.
#XFLD: Green space tooltip
okSpaceCountTooltip=Zdrowe przestrzenie: Wykorzystanie pamięci mieści się w granicach między 6% a 90%.
#XFLD: Locked space tooltip
lockedSpaceCountTooltipNew=Zablokowane przestrzenie: Zablokowano z powodu niewystarczającej pamięci.
#XFLD: Locked space tooltip
lockedSpaceCountTooltip=Zblokowane przestrzenie
#YMSE: Error while deleting remote source
deleteRemoteError=Nie można było usunąć połączeń.
#XFLD: Space ID input tooltip
spaceIDInfoTooltip=ID przestrzeni nie można zmienić później.\n Prawidłowe znaki A - Z, 0 - 9, oraz _
#YMSE: Error when space name is empty
spaceNameEmptyValidationError=Wprowadź nazwę przestrzeni.
#YMSE: Error when ECN Business name is empty
ecnNameEmptyValidationError=Wprowadź nazwę biznesową.
#YMSE: Error when space id is empty
spaceIDInputEmptyValidationError=Wprowadź ID przestrzeni.
#YMSE: Error when space id contains invalid characters
spaceIDInputInValidError=Nieprawidłowe znaki. Proszę używać tylko A - Z, 0 - 9, i _.
#YMSE: Error when space id exists while creation
spaceIDInputDuplicateError=ID przestrzeni już istnieje.
#XFLD: Space searchfield placeholder
search=Wyszukaj
#XMSG: Success message after creating users
createUsersSuccess=Dodano użytkowników
#XMSG: Success message after creating user
createUserSuccess=Użytkownik dodany
#XMSG: Success message after updating users
updateUsersSuccess=Liczba zaktualizowanych użytkowników: {0}
#XMSG: Success message after updating user
updateUserSuccess=Użytkownik zaktualizowany
#XMSG: Success message after removing users
removeUsersSuccess=Liczba usuniętych użytkowników: {0}
#XMSG: Success message after removing user
removeUserSuccess=Usunięto użytkownika
#XFLD: Schema name
schemaName=Nazwa schematu
#XFLD: used of total
ofTemplate={0} z {1}
#XFLD: Assigned disk label in space overview
assignedStorageTemplate=Przypisany dysk ({0} z {1})
#XFLD: Assigned Memory label in space overview
assignedRAMTemplate=Przypisana pamięć ({0} z {1})
#XFLD: Storage ratio on space
accelearationRAM=Przyspieszenie pamięci
#XFLD: No Storage Consumption
noStorageConsumptionText=Nie przypisano limitu magazynu.
#XFLD: Used disk label in space overview
usedStorageTemplate=Dysk wykorzystywany do przechowywania ({0} z {1})
#XFLD: Used Memory label in space overview
usedRAMTemplate=Pamięć wykorzystywana do przechowywania ({0} z {1})
#XFLD: Used Disk tooltip
usedStorageTooltipTemplate={0} z {1} dysku wykorzystano do przechowywania
#XFLD: Used Memory tooltip
usedRAMTooltipTemplate={0} z {1} wykorzystanej pamięci
#XFLD: Assigned Disk tooltip
assignedStorageTooltipTemplate=Przypisany dysk: {0} z {1}
#XFLD: Assigned Memory tooltip
assignedRAMTooltipTemplate={0} z {1} przypisanej pamięci
#XFLD: Space related used storage tooltip
spaceRelatedUsedStorageTooltipTemplate=Dane przestrzeni: {0}
#XFLD: Not space related used storage tooltip
otherDataUsedStorageTooltipTemplate=Inne dane: {0}
#XFLD: Storage used exceeded popover and tooltip
storageUsedExceededText=Rozważ rozszerzenie swojego planu lub skontaktuj się z działem wsparcia SAP.
#XCOL: Space table-view column used Disk
usedStorage=Dysk wykorzystany do przechowywania
#XCOL: Space monitor column used Memory
usedRAM=Pamięć wykorzystana do przechowywania
#XCOL: Space monitor column Schema
tableSchema=Schemat
#XCOL: Space monitor column Storage Type
tableStorageType=Rodzaj pamięci
#XCOL: Space monitor column Table Type
tableType=Typ tabeli
#XCOL: Space monitor column Record Count
tableRecordCount=Liczba rekordów
#XFLD: Assigned Disk
assignedStorage=Dysk przypisany do przechowywania
#XFLD: Assigned Memory
assignedRAM=Pamięć przypisana do przechowywania
#XCOL: Space table-view column storage utilization
tableStorageUtilization=Wykorzystana pamięć
#XFLD: space status
spaceStatus=Status przestrzeni
#XFLD: space type
spaceType=Rodzaj przestrzeni
#XFLD: space type label for space with space type "SAP BW bridge" ("SAP BW bridge" is a product name, do not translate it)
abapbridgeSpaceType=Łączenie SAP BW
#XFLD: space type label for space with space type "Data Marketplace"
marketplaceSpaceType=Produkt dostawcy danych
#XMSG: Error message for when selected space cannot be deleted due to the space type
deleteTypedSpaceError=Nie można usunąć przestrzeni {0}, ponieważ ma rodzaj {1}.
#XMSG: Error message for when selected spaces cannot be deleted due to the space type
deleteTypedSpacesError=Nie można usunąć {0} wybranych przestrzeni. Nie można usunąć następujących rodzajów przestrzeni: {1}.
#XFLD: space type label for space without space type
defaultSpaceType=SAP Datasphere
#XFLD: Tooltip for monitor space button
monitorSpace=Monitoruj
#XFLD: Tooltip for edit space button
editSpace=Edytuj przestrzeń
#XMSG: Deletion warning in messagebox
deleteConfirmation=Czy na pewno chcesz usunąć tę przestrzeń?
#XFLD: Tooltip for delete space button
deleteSpace=Usuń przestrzeń
#XFLD: storage
storage=Dysk do przechowywania
#XFLD: username
userName=Nazwa użytkownika
#XFLD: port
port=Port
#XFLD: hostname
hostName=Nazwa hosta
#XFLD: password
password=Hasło
#XBUT: Request new password button
requestPassword=Poproś o nowe hasło
#YEXP: Usage explanation in time data section
timeDataSectionHint=Utwórz tabele czasów i wymiary, aby używać ich w swoich modelach i raportach.
#YEXP: Usage explanation in Space schema section
spaceSchemaHint=Czy chcesz, aby dane w Twojej przestrzeni były używane przez inne narzędzia lub aplikacje? Jeśli tak, utwórz co najmniej jednego użytkownika z dostępem do danych w Twojej przestrzeni i zaznacz, czy wszystkie przyszłe dane przestrzeni mają myć domyślnie dostępne do użytku.
#XTIT: Create schema popup title
createSchemaDialogTitle=Utwórz schemat Open SQL
#XTIT: Generate Time Data dialog title
generateTimeDataDialogTitle=Utwórz tabele czasów i wymiary
#XTIT: Edit Time Data dialog title
editTimeDataDialogTitle=Edytuj tabele czasów i wymiary
#XTIT: Time Data token title
timeDataTokenTitle=Dane czasu
#XTIT: Time Data token title
timeDataUpdateViews=Aktualizacja widoków danych czasu
#XFLD: Time Data token creation in progress label
timeDataCreationInProgress=Tworzenie w toku...
#XFLD: Time Data token creation error label
timeDataCreationError=Tworzenie nie powiodło się. Spróbuj ponownie.
#XTIT: Time Data dialog time settings title
timeDataDialogTimeSettingsTitle=Ustawienia tabeli czasu
#XTIT: Time Data dialog time data table title
timeDataDialogTranslationTablesTitle=Tabele przeliczania
#XTIT: Time Data dialog dimensions title
timeDataDialogTimeViewsTitle=Wymiary czasu
#XFLD: Time Data dialog time range label
timeRangeHint=Zdefiniuj przedział czasu.
#XFLD: Time Data dialog time data table label
timeDataHint=Nadaj nazwę tabeli.
#XFLD: Time Data dialog time data dimensions label
timeDimensionsHint=Nadaj nazwę wymiarom.
#XFLD: Time Data Time range description label
timerangeLabel=Przedział czasu
#XFLD: Time Data dialog from year label
fromYearLabel=Od roku
#XFLD: Time Data dialog to year label
toYearLabel=Do roku
#XFLD: Time Data dialog calendar type label
calendarTypeLabel=Typ kalendarza
#XFLD: Time Data dialog granularity label
granularityLabel=Poziom szczegółowości
#XFLD: Time Data dialog technical name label
technicalNameLabel=Nazwa techniczna
#XFLD: Time Data dialog quarters text table lable
quarterTextsTableLabel=Tabela przeliczania dla kwartałów
#XFLD: Time Data dialog months text table lable
monthsTextsTableLabel=Tabela przeliczania dla miesięcy
#XFLD: Time Data dialog days text table lable
daysTextsTableLabel=Tabela przeliczania dla dni
#XFLD: Time Data dialog year label
yearLabel=Wymiar roku
#XFLD: Time Data dialog quarter label
quarterLabel=Wymiar kwartału
#XFLD: Time Data dialog month label
monthLabel=Wymiar miesiąca
#XFLD: Time Data dialog day label
dayLabel=Wymiar dnia
#XFLD: Time Data dialog gregorian calendar type label
gregorian=Gregoriański
#XFLD: Time Data dialog time granularity day label
day=Dzień
#XFLD: Time Data dialog business name maxlength reached
timeRangeValueStateTextMaxLengthBusinessName=Osiągnięto maks. długość 1000 znaków.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextMaxRangeExceeded=Maksymalny przedział czasu wynosi 150 lat.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeLower=Wartość „Od roku” powinna być niższa niż „Do roku”.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeHigher=Minimalna wartość „Od roku” to 1900.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeHigher=Wartość „Do roku” powinna być wyższa niż „Od roku”.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeLower=Wartość „Do roku” musi być niższa od bieżącego roku plus 100
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningFromYear=Wyższa wartość „Od roku” może prowadzić do utraty danych
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningToYear=Niższa wartość „Do roku” może prowadzić do utraty danych
#XMSG: Time Data creation validation error message
timeDataValidationError=Prawdopodobnie niektóre pola są nieprawidłowe. Aby kontynuować, sprawdź wymagane pola.
#XMSG: Time Data deletion popup text
deleteTimeDataConfirmation=Czy na pewno chcesz usunąć te dane?
#XMSG: Time Data creation success message
createTimeDataSuccess=Utworzono dane czasu
#XMSG: Time Data update success message
updateTimeDataSuccess=Zaktualizowano dane czasu
#XMSG: Time Data delete success message
deleteTimeDataSuccess=Usunięto dane czasu
#XMSG: Time Data creation error message
createTimeDataError=Wystąpił błąd podczas próby tworzenia danych czasu.
#XMSG: Time Data update error message
updateTimeDataError=Wystąpił błąd podczas próby aktualizacji danych czasu.
#XMSG: Time Data creation error message
deleteTimeDataError=Wystąpił błąd podczas próby usunięcia danych czasu.
#XMSG: Error message when time data could not be retrieved
retrievingTimeDataError=Nie można było wczytać danych czasu.
#XTIT: Time Data deletion popup title
deleteTimeDataConfirmationTitle=Ostrzeżenie
#XTXT: Time Data deletion dependency popup text
deleteTimeDataDependencyText=Nie można było usunąć danych czasu, ponieważ są one używane w innych modelach.
#XTXT: Time Data deletion dependency popup text singular
deleteTimeDataDependencyTextSingular=Nie można było usunąć danych czasu, ponieważ są one używane w innym modelu.
#XMSG: Validation error when required time data form field is empty
timeDataRequiredField=To pole jest wymagane i nie może być puste.
#XFLD: Tooltip for Open in Database Explorer button
openInDBX=Otwórz w eksploratorze bazy danych
#YMSE: Dimension Year
dimensionYearView=Wymiar "Rok"
#YMSE: Dimension Year
dimensionQuarterView=Wymiar "Kwartał"
#YMSE: Dimension Year
dimensionMonthView=Wymiar "Miesiąc"
#YMSE: Dimension Year
dimensionDayView=Wymiar "Dzień"
#XFLD: Time Data deletion object title
timeDataUsedIn=(używany w {0} modelach)
#XFLD: Time Data deletion object title singular
timeDataUsedInSingular=(używany w 1 modelu)
#XFLD: Time Data deletion table column provider
provider=Dostawca
#XTIT: Time Data dependency dialog title
timeDataDependencyDialogTitle=Zależności
#XTIT: Create schema popup title
createSpaceSchemaUserDialogTitle=Tworzenie użytkownika dla schematu przestrzeni
#XFLD: Create schema button
createSchemaButton=Utwórz schemat Open SQL
#XFLD: Generate TimeData button
generateTimeDataButton=Utwórz tabele czasów i wymiary
#XFLD: Show dependencies button
showDependenciesButton=Pokaż zależności
#XFLD: tooltip for disabled buttons when user is not member of space
mustBeMemberOfSpaceForOperation=Aby wykonać tę operację, użytkownik musi być członkiem przestrzeni.
#XFLD: Create space schema user button
createSpaceSchemaUserButton=Utwórz użytkownika schematu przestrzeni
#YMSE: API Schema users load error
loadSchemaUsersError=Nie można było wczytać listy użytkowników.
#XTIT: API Schema-config popup title
schemaUserDetailsDialogTitle=Szczegóły użytkownika schematu przestrzeni
#XMSG: API Schema user deletion popup text
deleteSpaceSchemaUserConfirmation=Czy na pewno chcesz usunąć wybranego użytkownika?
#XMSG: API Schema user deletion confirmation text
userDeleteSuccess=Usunięto użytkownika.
#YMSE: API Schema user deletion error
userDeleteError=Nie można było usunąć użytkownika.
#XFLD: User deleted
userDeleted=Usunięto użytkownika.
#XTIT: Remove user popup title
removeUserConfirmationTitle=Ostrzeżenie
#XMSG: Remove user popup text
removeUserConfirmation=Czy na pewno chcesz usunąć użytkownika? Użytkownik i jego przypisane role z zakresu zostaną usunięte z przestrzeni.
#XMSG: Remove users popup text
removeUsersConfirmation=Czy na pewno chcesz usunąć użytkowników? Użytkownicy i ich przypisane role z zakresu zostaną usunięte z przestrzeni.
#XBUT: Confirmation button for remove user dialog
removeUserConfirmationBtn=Usuń
#YMSE: No data text for available roles
noDataAvailableRoles=Przestrzeni nie dodano do żadnej roli z zakresu. \n Aby móc dodawać użytkowników do przestrzeni, przestrzeń należy najpierw dodać do co najmniej jednej roli z zakresu.
#YMSE: No data text for selected roles
noDataSelectedRoles=Brak wybranych ról z zakresu
#XTIT: Schema-config popup title
schemaDetailsDialogTitle=Szczegóły konfiguracji schematu Open SQL
#XFLD: Label for Read Audit Log
auditLogRead=Aktywuj log kontroli dla operacji odczytu
#XFLD: Label for Change Audit Log
auditLogChange=Aktywuj log kontroli dla operacji zmiany
#XFLD: Label Audit Log Retention
auditLogRetention=Zachowaj logi przez
#XFLD: Label Audit Log Retention Unit
retentionUnit=Dni
#YMSE: Error when log retention time is set incorrectly
incorrectRetentionTime=Wprowadź liczbę całkowitą pomiędzy {0} i {1}
#XFLD: Label for Cross Space Schema Access
spaceSchemaAccess=Wykorzystaj dane schematu przestrzeni
#XTIT: Confirm deactivation of space schema consumption dialog title
disableSpaceSchemaConsumptionDialogTitle=Zatrzymaj wykorzystanie danych schematu przestrzeni
#XTXT: Confirm deactivation of space schema consumption dialog text
disableSpaceSchemaConsumptionDialogText=Ten schemat Open SQL może wykorzystywać dane Twojego schematu przestrzeni. Jeśli zatrzymasz wykorzystanie, modele bazujące na tych danych schematu przestrzeni mogą przestać działać.
#XBUT: Confirmation button for the stop consuming space schema data dialog
stopConsuming=Zatrzymaj wykorzystanie
#XTXT: Tooltip for data lake icon
dataLakeIconTooltip=Ta przestrzeń jest używana w celu uzyskania dostępu do jeziora danych
#XFLD: Tooltip for data lake table entry
dataLakeTableEntryTooltip=Jezioro danych aktywne
#XFLD: used In-Memory linit reached tag
usedRamLimitReachedLabel=Osiągnięto limit pamięci
#XFLD: Space storage Limit reached tag
storageLimitReachedLabel=Osiągnięto limit pamięci
#XFLD: Minimal Storage Limit Reached tag
minStorageLimitReachedLabel=Osiągnięto minimalny limit pamięci
#XFLD: Space ram tag
ramLimitReachedLabel=Osiągnięto limit pamięci
#XFLD: Minimal In-Memory Limit Reached tag
minRamLimitReachedLabel=Osiągnięto limit minimalnej pamięci
#XFLD: Popover text after click on space storage limit warning
storageLimitReachedText=Osiągnięto limit przypisanej pamięci przestrzeni wynoszący {0}. Przypisz więcej pamięci do tej przestrzeni.
#XFLD: System storage tag
systemStorageLimitReachedLabel=Osiągnięto limit pamięci systemu
#XMSG: Popover text after click on storage limit warning
systemStorageLimitReachedText=Osiągnięto limit pamięci systemu wynoszący {0}. Nie możesz już przypisać więcej pamięci do tej przestrzeni.
#XMSG: Schema deletion warning
deleteSchemaConfirmation=Usunięcie tego schematu Open SQL spowoduje także trwałe usunięcie wszystkich zapisanych obiektów i opracowanych powiązań w schemacie. Czy kontynuować?
#XMSG: Success message after deleting a schema
schemaDeleteSuccess=Usunięto schemat
#YMSE: Error while deleting schema.
schemaDeleteError=Nie można było usunąć schematu.
#XMSG: Success message after update a schema
schemaUpdateSuccess=Zaktualizowano schemat
#YMSE: Error while updating schema.
schemaUpdateError=Nie można było zaktualizować schematu.
#XMSG: Hint when no password is shown in the dialog.
newPasswordHint=Ten schemat wymaga wprowadzenia przesłanego hasła. Jeśli go nie pamiętasz, możesz poprosić o nowe hasło. Pamiętaj o skopiowaniu lub zapisaniu nowego hasła.
#XMSG: Hint when showing the password in the dialog.
savePasswordHint=Skopiuj hasło. Będzie ono potrzebne do skonfigurowania połączenia z tym schematem. Jeśli nie pamiętasz hasła, możesz otworzyć to okno dialogowe, aby zresetować hasło.
#XFLD,50: Hint on entering schema name in creation dialog
schemaNameHint=Tej nazwy nie można zmienić po utworz. schematu.
#XFLD: Open SQL Schemas section sub headline
schemasSQL=Open SQL
#XFLD: Space schema section sub headline
schemasSpace=Przestrzeń
#XFLD: HDI Container section header
HDIContainers=Kontenery HDI
#XTXT: Add HDI Containers button tooltip
addHDIContainers=Dodaj kontenery HDI
#XTXT: Remove HDI Containers button tooltip
removeHDIContainers=Usuń kontenery HDI
#XFLD: Enable HDI Containers feature button
enableHDIContainersButton=Aktywuj dostęp
#YMSE: No data text for HDI Containers table
noDataHDIContainers=Nie dodano kontenerów HDI.
#YMSE: No data text for Timedata section
noDataTimedata=Nie utworzono tabel czasów i wymiarów.
#YMSE: No data text during runtime not available for Timedata section
runtimeNotAvailableNoDataTimedata=Nie można wczytać wymiarów i tabel czasu, ponieważ baza danych czasu wykonania nie jest dostępna.
#YMSE: No data text during runtime not available for HDI section
runtimeNotAvailableNoDataHDI=Nie można wczytać kontenerów HDI, ponieważ baza danych czasu wykonania nie jest dostępna.
#YMSE: Error while fetching HDI Containers
fetchHDIContainersError=Nie można było pozyskać kontenerów HDI. Spróbuj ponownie później.
#XFLD Table column header for HDI Container names
HDIContainerName=Nazwa kontenera HDI
#XFLD: Enable HDI Containers feature dialog title
enableHDIContainersDialogTitle=Aktywuj dostęp
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText1New=Możesz włączyć SAP SQL Data Warehousing dla tenanta SAP Datasphere, aby wymieniać dane między kontenerami HDI i przestrzeniami SAP Datasphere bez potrzeby przenoszenia danych.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText2=W tym celu otwórz zgłoszenie wparcia, klikając poniższy przycisk.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText3New=Po pomyślnym przetworzeniu zgłoszenia musisz utworzyć co najmniej jeden nowy kontener HDI w bazie danych czasu wykonania SAP Datasphere. Wówczas przycisk Aktywuj dostęp jest zastępowany przez przycisk + w sekcji Kontenery HDI dla wszystkich przestrzeni SAP Datasphere i możesz dodać swoje kontenery do przestrzeni.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText41=Potrzebujesz dodatkowych informacji? Przejdź do %%0. Szczegółowe informacje dotyczące danych, które należy uwzględnić w zgłoszeniu, można znaleźć w %%1.
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkText=Pomoc SAP
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkTextSapNote3057059=Nota SAP 3057059
#XBUT: Open Ticket Button Text
openTicket=Otwórz zgłoszenie
#XBUT: Add Button Text
add=Dodaj
#XBUT: Next Button Text
next=Następny
#XBUT: Edit Button Text
editUsers=Edytuj
#XBUT: create user Button Text
createUser=Utwórz
#XBUT: Update user Button Text
updateUser=Wybierz
#XTIT: Add HDI Containers Dialog Title
addHDIContainersDialogTitle=Dodaj nieprzypisane kontenery HDI
#XFLD: No data text for add HDI container dialog
addHDIContainersDialogNoDataText=Nie udało się znaleźć nieprzypisanych kontenerów. \n Być może kontener, którego szukasz, jest już przypisany do przestrzeni.
#YMSE: Error while loading assigned HDI Containers
loadAssignedHDIContainersError=Nie można było wczytać przypisanych kontenerów HDI.
#YMSE: Error while loading assigned HDI Containers
loadUnassignedHDIContainersError=Nie można było wczytać kontenerów HDI.
#XMSG: Success message
succeededToAddHDIContainer=Dodano kontener HDI
#XMSG: Success message
succeededToAddHDIContainerPlural=Dodano kontenery HDI
#XMSG: Success message
succeededToDeleteHDIContainer=Usunięto kontener HDI
#XMSG: Success message
succeededToDeleteHDIContainerPlural=Usunięto kontenery HDI
#XFLD: Time data section sub headline
timeDataSection=Tabele czasów i wymiary
#XFLD: Schemas list entry read audit log enabled indicator
readIndicator=Odczyt
#XFLD: Schemas list entry change audit log enabled indicator
changeIndicator=Zmiana
#XFLD: Remote sources section sub headline
allconnections=Przypisanie połączenia
#XFLD: Remote sources section sub headline
localconnections=Połączenia lokalne
#XFLD: Remote sources section sub headline for open Connectors
openConnectors=SAP Open Connectors
#XFLD: User section sub headline
memberassignment=Przypisanie członków
#XFLD: User assignment section sub headline
userAssignment=Przypisanie użytkownika
#XFLD: User section Access dropdown Member
member=Członek
#XFLD: User assignment section column name
user=Nazwa użytkownika
#XTXT: Selected role count
selectedRoleToolbarText=Wybrane: {0}
#XTIT: Space detail section connections title
detailsSectionConnections=Połączenia
#XTIT: Space detail section data access title
detailsSectionDataAccess=Dostęp do schematu
#XTIT: Space detail section time data title
detailsSectionGenerateData=Dane czasu
#XTIT: Space detail section members title
detailsSectionUsers=Członkowie
#XTIT: Space detail section Users title
detailsSectionUsersTitle=Użytkownicy
#XTIT: Out of Storage
insufficientStoragePopoverTitle=Koniec pamięci
#XTIT: Storage distribution
storageDistributionPopoverTitle=Wykorzystywana pamięć dyskowa
#XTXT: Out of Storage popover text
insufficientStorageText=Aby utworzyć nową przestrzeń, zredukuj przypisaną pamięć innej przestrzeni lub usuń przestrzeń, której już nie potrzebujesz. Łączną pamięć systemu możesz zwiększyć poprzez opcję Zarządzaj planem.
#XMSG: Space id length warning
spaceIdLengthWarning=Przekroczono maksymalną liczbę {0} znaków.
#XMSG: Space name length warning
spaceNameLengthWarning=Przekroczono maksymalną liczbę {0} znaków.
#XMSG: Space id reserved prefix warning
spaceIdnWarningReservedPrefix=Nie stosuj prefiksu {0}, aby uniknąć ewentualnych konfliktów.
#YMSE: Error while loading open SQL schemas
loadLocalSchemasError=Nie można było wczytać schematów Open SQL.
#YMSE: Error while creating open SQL schema
createLocalSchemaError=Nie można było utworzyć schematu Open SQL.
#YMSE: Error while loading all remote connections
loadRemoteConnectionsError=Nie można było wczytać wszystkich zdalnych połączeń.
#YMSE: Error while loading space details
loadSpaceDetailsError=Nie można było wczytać szczegółów przestrzeni.
#YMSE: Error while deploying space details
deploySpaceDetailsError=Nie można było wdrożyć przestrzeni.
#YMSE: Error while copying space details
copySpaceDetailsError=Nie można było skopiować przestrzeni.
#YMSE: Error while loading storage data
loadStorageDataError=Nie można było wczytać danych pamięci.
#YMSE: Error while loading all users
loadAllUsersError=Nie można było wczytać wszystkich użytkowników.
#YMSE: Failed to reset password
resetPasswordError=Nie można było zresetować hasła.
#XMSG: Success message after updating space
resetPasswordSuccessMessage=Ustawiono nowe hasło dla schematu
#YMSE: DP Agent-name too long
DBAgentNameError=Nazwa agenta dostarczania danych jest za długa.
#YMSE: Schema-name not valid.
schemaNameError=Nazwa schematu jest nieprawidłowa.
#YMSE: User name not valid.
UserNameError=Nazwa użytkownika jest nieprawidłowa.
#XTIT: Consumption by Storage Type
consumptionStorageTypeText=Wykorzystanie według typu pamięci
#XTIT: Consumption by Schema
consumptionSchemaText=Wykorzystanie według schematu
#XTIT: Overall Consumption by schema
overallConsumptionSchemaText=Łączne wykorzystanie tabeli przez schemat
#XTIT: Overall Consumption by Table Type
overallConsumptionTableTypeText=Łączne wykorzystanie według typu tabeli
#XTIT: Tables
tableDetailsText=Szczegóły tabeli
#XTIT: Table Storage Consumption
tableStorageConsumptionText=Wykorzystanie pamięci tabeli
#XFLD: Table Type label
tableTypeLabel=Typ tabeli
#XFLD: Schema label
schemaLabel=Schemat
#XFLD: reset table tooltip
resetTable=Resetuj tabelę
#XFLD: In-Memory label in space monitor
inMemoryLabel=Pamięć
#XFLD: Disk label in space monitor
diskLabel=Dysk
#XFLD: Yes
yesLabel=Tak
#XFLD: No
noLabel=Nie
#XFLD: switch label: Want the data in this space to be consumable by default?
allowConsumptionByDefault=Czy chcesz, aby dane w tej przestrzeni były domyślnie możliwe do wykorzystania?
#XFLD: Business Name
businessNameLabel=Nazwa biznesowa
#XFLD: Refresh
refresh=Odśwież
#XMSG: No filter results title
noFilterResultsTitle=Prawdopodobnie dane nie są wyświetlane z powodu ustawień filtra.
#XMSG: No filter results message
noFilterResultsMsg=Spróbuj zmienić ustawienia filtra. Jeśli nadal dane nie będą wyświetlane, utwórz kilka tabel w edytorze danych. Gdy tabele te zaczną korzystać z pamięci, możliwe będzie ich monitorowanie.
#XMSG: Space monitoring Hana state red title
spaceMonitorHanaRedTitle=Baza danych czasu wykonania jest niedostępna.
#XMSG: Space monitoring Hana state red message
spaceMonitorHanaRedMsg=Ponieważ baza danych czasu wykonania jest niedostępna, niektóre funkcje są wyłączone i nie można wyświetlić informacji na tej stronie.
#YMSE: Error while creating space schema user
createSpaceSchemaUserError=Nie można było utworzyć użytkownika schematu przestrzeni.
#YMSE: Error User name already exists
userAlreadyExistsError=Nazwa użytkownika już istnieje.
#YMSE: Error Authentication failed
authenticationFailedError=Uwierzytelnienie nie powiodło się.
#XMSG: Message strip message when a schema user is locked
userLockedMessageStrip=Użytkownik jest zablokowany z powodu zbyt wielu niepomyślnych prób logowania. Aby odblokować użytkownika, poproś o nowe hasło.
#XMSG: Success message after reset and unlock schema user
resetUnlockPasswordSuccessMessage=Ustawiono nowe hasło i odblokowano użytkownika
#XMSG: user is locked message
userLockedMessage=Użytkownik jest zablokowany.
#XCOL: Users table-view column Role
spaceRole=Rola
#XCOL: Users table-view column Scoped Role
spaceScopedRole=Rola z zakresu
#XCOL: Users table-view column Space Admin
spaceAdmin=Administrator przestrzeni
#XFLD: User section dropdown value Viewer
viewer=Przeglądarka
#XFLD: User section dropdown value Modeler
modeler=Modeler
#XFLD: User section dropdown value Data Integrator
dataIntegrator=Integrator danych
#XFLD: User section dropdown value Space Administrator
spaceAdministrator=Administrator przestrzeni
#XMSG: Success message -  Space Role updated successfully.
updateUserSpaceRoleSuccess=Zaktualizowano rolę przestrzeni
#XMSG: Error message - Failed to update Space Role.
updateUserSpaceRoleError=Aktualizacja roli przestrzeni nie powiodła się.
#XFLD:
databaseUserNameSuffix=Sufiks nazwy użytkownika bazy danych
#XTXT: Space Schema password text
spaceSchemaPasswordText=Aby skonfigurować połączenie z tym schematem, skopiuj swoje hasło. Jeśli nie pamiętasz hasła, możesz zawsze poprosić o nowe.
#XFLD: Cloud Platform label in space schema dialog
cloudPlatformLabel=Cloud Platform
#XTXT: Cloud Platform text in space schema dialog
cloudPlatformText=Aby skonfigurować dostęp za pośrednictwem tego użytkownika, aktywuj wykorzystanie i skopiuj dane uwierzytelniające. Jeśli możesz jedynie kopiować dane uwierzytelniające bez hasła, zadbaj o dodania hasła w późniejszym czasie.
#XFLD: enable consumption in CloudPlatform label in space schema dialog
enableConsumptionInCloudPlatformLabel=Aktywuj wykorzystanie w Cloud Platform
#XFLD: Credentials for User-Provided Service label in space schema dialog
cloudPlatformCredentialsLabel=Dane uwierzytelniające dla usługi dostarczanej przez użytkownika:
#XFLD: Credentials for User-Provided Service (Without Password) label in space schema dialog
cloudPlatformCredentialsWithoutPasswordLabel=Dane uwierzytelniające dla usługi dostarczanej przez użytkownika (bez hasła):
#XFLD: Copy Credentials label in space schema dialog
copyCredentialsLabel=Kopiuj dane uwierzytelniające bez hasła
#XFLD: Copy Credentials label in space schema dialog
copyFullCredentialsLabel=Kopiuj pełne dane uwierzytelniające
#XFLD: Copy Password label in space schema dialog
copyPasswordLabel=Kopiuj hasło
#XMSG: Credentials copied to clipboard
credentialsCopiedMessage=Dane uwierzytelniające skopiowano do schowka
#XMSG: Password copied to clipboard
passwordCopiedMessage=Hasło skopiowano do schowka
#XMSG: create database user dialog title
createDatabaseUserDialogTitle=Utwórz użytkownika bazy danych
#XMSG: Database Users section title
databaseUsers=Użytkownicy bazy danych
#XMSG: Database User Details dialog title
databaseUserDetailsDialogTitle=Szczegóły użytkownika bazy danych
#XFLD: database user read audit log
databaseUserAuditLogRead=Aktywuj logi kontroli dla operacji odczytu i zachowaj logi przez
#XFLD: database user change audit log
databaseUserAuditLogChange=Aktywuj logi kontroli dla operacji zmiany i zachowaj logi przez
#XMSG: Cloud Platform Access
cloudPlatformAccess=Dostęp do Cloud Platform
#XFLD: Enable Consumption in Cloud Platform hint
enableConsumptionCloudPlatformHint=Skonfiguruj dostęp do swojego kontenera HANA Deployment Infrastructure (HDI) za pośrednictwem tego użytkownika bazy danych. Warunkiem połączenia się z kontenerem HDI jest włączenie modelowania SQL
#XFLD: Enable HDI Consumption
enableConsumptionCloudPlatform=Włącz wykorzystanie HDI
#XFLD: Enable Consumption hint
enableConsumptionHint=Czy chcesz, aby dane w przestrzeni były możliwe do wykorzystania przez inne narzędzia lub aplikacje?
#XFLD: Enable Consumption
enableConsumption=Włącz wykorzystanie SQL
#XFLD: Enable Modeling
enableModeling=Włącz modelowanie SQL
#XMSG: Privileges for Data Modeling
privilegesModeling=Pozyskiwanie danych
#XMSG: Privileges for Data Consumption
privilegesConsumption=Wykorzystanie danych dla narzędzi zewnętrznych
#XFLD: SQL Modeling
sqlModeling=Modelowanie SQL
#XFLD: SQL Consumption
sqlConsumption=Wykorzystanie SQL
#XFLD: enabled
enabled=Włączono
#XFLD: disabled
disabled=Wyłączono
#XFLD: Edit Privileges
editPrivileges=Edytuj uprawnienia
#XFLD: Open Database Explorer
openDBX=Otwórz eksplorator bazy danych
#XFLD: create database user hint
databaseCreateHint=Pamiętaj, że po zapisaniu zmiana nazwy użytkownika nie będzie już możliwa.
#XFLD: Internal Schema Name
internalSchemaName=Nazwa wewnętrzna schematu
#YMSE: Failed to load database users
loadDatabaseUserError=Wczytanie użytkowników bazy danych nie powiodło się
#YMSE: Failed to delete database user
deleteDatabaseUsersError=Usunięcie użytkowników bazy danych nie powiodło się
#XMSG: Success message after deleting database user
deleteDatabaseUserSuccess=Usunięto użytkownika bazy danych
#XMSG: Success message after deleting database users
deleteDatabaseUsersSuccess=Usunięto użytkowników bazy danych
#XMSG: Success message after creating database user
createDatabaseUserSuccessMessage=Utworzono użytkownika bazy danych
#YMSE: Failed to create database user
createDatabaseUserError=Utworzenie użytkownika bazy danych nie powiodło się
#XMSG: Success message after updating database user
updateDatabaseUserSuccessMessage=Zaktualizowano użytkownika bazy danych
#YMSE: Failed to update database user
updateDatabaseUserError=Aktualizacja użytkownika bazy danych nie powiodła się
#XFLD: HDI Consumption
hdiConsumption=Wykorzystanie HDI
#XTIT: Space detail section Database Access title
detailsSectionDatabaseAccess=Dostęp do bazy danych
#XTXT: Database users consumption hint
databaseUsersConsumptionHint=Twoje dane przestrzeni mogą być domyślnie możliwe do wykorzystania. Modele w edytorach będą automatycznie zezwalać na wykorzystanie danych.
#XFLD: Default Consumption of Space Data:
defaultConsumptionOfSpaceData=Domyślne wykorzystanie danych przestrzeni:
#XFLD: Database User Name
databaseUserName=Nazwa użytkownika bazy danych
#XMSG: Database User creation validation error message
databaseUserValidationError=Prawdopodobnie niektóre pola są nieprawidłowe. Aby kontynuować, sprawdź wymagane pola.
#XMSG: SQL modeling can’t be enabled since this user has been migrated.
migratedDatabaseUserHint=Nie można włączyć pozyskiwania danych, ponieważ wykonano migrację tego użytkownika.
#XBUT: Remove Button Text
remove=Usuń
#XBUT: Remove Spaces Button Text
removeSpaces=Usuń przestrzenie
#XBUT: Remove Objects Button Text
removeObjects=Usuń obiekty
#XMSG: No members have been added yet.
noMembersAssigned=Nie dodano jeszcze członków.
#XMSG: No users have been added yet.
noUsersAssigned=Nie dodano jeszcze użytkowników.
#XMSG: No database users have been created or your filter is not showing any data.
noDatabaseUsers=Nie utworzono użytkowników bazy danych lub filtr nie wyświetla żadnych danych.
#XMSG: Please enter a user name.
noDatabaseUsername=Wprowadź nazwę użytkownika.
#XMSG: The user name is too long. Please use a shorter one.
databaseUsernameTooLong=Nazwa użytkownika jest zbyt długa. Wprowadź krótszą nazwę.
#XMSG: No privileges have been enabled and this database user will have limited functionality. Do you still want to continue?
noPrivilegesConfirmationMessage=Nie włączono żadnych uprawnień, co oznacza, że ten użytkownik bazy danych będzie miał dostęp do ograniczonego zakresu funkcji. Czy mimo to chcesz kontynuować?
#XMSG: To enable audit logs for change operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogChangeMessage=Aby włączyć logi kontroli dla operacji zmiany, należy także włączyć pozyskiwanie danych. Czy chcesz wykonać tę czynność?
#XMSG: To enable audit logs for read operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogReadMessage=Aby włączyć logi kontroli dla operacji odczytu, należy także włączyć pozyskiwanie danych. Czy chcesz wykonać tę czynność?
#XMSG: To enable HDI consumption data ingestion and data consumption needs to be enabled. Do you want to enable all?
enableHDIConsumptionMessage=Aby włączyć wykorzystanie HDI, należy również włączyć pozyskiwanie danych i wykorzystanie danych. Czy chcesz wykonać tę czynność?
#XMSG:
databaseUserPasswordText=Aby skonfigurować połączenie z tym użytkownikiem bazy danych, skopiuj swoje hasło. Jeśli nie pamiętasz hasła, możesz zawsze poprosić o nowe.
#XTIT: Space detail section members title
detailsSectionMembers=Członkowie
#XMSG: New password set
newPasswordSet=Ustawiono nowe hasło
#XFLD: Data Ingestion
dataIngestion=Pozyskiwanie danych
#XFLD: Data Consumption
dataConsumption=Wykorzystanie danych
#XFLD: Privileges
privileges=Uprawnienia
#XFLD: Enable Data ingestion
enableDataIngestion=Włącz pozyskiwanie danych
#XMSG: Log the read and change operations for data ingestion.
databaseUserAuditHint=Zarejestruj operacje odczytu i zmiany dla pozyskiwania danych.
#XMSG: Make your space data available in your HDI containers.
databaseUserHDIHint=Udostępnij swoje dane przestrzeni w kontenerach HDI.
#XFLD: Enable Data consumption
enableDataConsumption=Włącz wykorzystanie danych
#XMSG: Allow other apps or tools to consume your space data.
enableDataConsumptionHint=Zezwalaj innym aplikacjom lub narzędziom na wykorzystanie danych przestrzeni.
#XMSG: To set up access via this schema user copy the credentials to your user-provided service. In case you can only copy the credentials without a password make sure to add the password later.
databaseUserHDIConsumptionHint=Aby skonfigurować dostęp za pośrednictwem tego użytkownika bazy danych, skopiuj dane uwierzytelniające do swojej usługi dostarczanej przez użytkownika. Jeśli możesz jedynie kopiować dane uwierzytelniające bez hasła, zadbaj o dodania hasła w późniejszym czasie.
#XFLD: Data Flow Runtime Capacity label in space overview
dataFlowRuntimeCapacity=Zdolności czasu wykonania przepływu danych ({0}:{1} godz. z {2} godz.)
#XFLD: Could not load Data Flow Runtime Capacity
dataFlowRuntimeCapacityError=Nie można wczytać zdolności czasu wykonania przepływu danych
#XMSG: User can grant data consumption to other users.
databaseUserConsumptionWithGrantOptionHint=Użytkownik może przyznać możliwość wykorzystania danych innym użytkownikom.
#XFLD: Enable data consumption with grant option
enableConsumptionWithGrantOption=Włącz wykorzystanie danych z opcją przyznania możliwości
#XMSG: To enable data consumption with grant option data consumption needs to be enabled. Do you want to enable both?
enableConsumptionWithGrantMessage=Aby włączyć wykorzystanie danych z opcjami przyznania, musi być włączone wykorzystanie danych. Czy chcesz włączyć obie funkcje?
#XFLD: Enable Automated Predictive Library (APL) and Predictive Analysis Library (PAL)
allowUsingScriptServerFunctionality=Włącz Automated Predictive Library (APL) i Predictive Analysis Library (PAL)
#XMSG: User can use SAP HANA Cloud embedded machine learning functions.
allowUsingScriptServerFunctionalityHint=Użytkownik może skorzystać z funkcji uczenia maszynowego osadzonych w SAP HANA Cloud.
#XFLD: Password Policy
passwordPolicy=Reguły dotyczące hasła
#XMSG: Password Policy
passwordPolicyHint=Tutaj możesz włączyć lub wyłączyć skonfigurowane reguły dotyczące hasła.
#XFLD: Enable Password Policy
enablePasswordPolicy=Włącz reguły dotyczące hasła
#XMSG: Read Access to the Space Schema
readAccessTitle=Dostęp z prawem odczytu do schematu przestrzeni
#XMSG: read access hint
readAccessHint=Możesz zezwolić użytkownikowi bazy danych na połączenie narzędzi zewnętrznych ze schematem przestrzeni oraz odczytywać widoki udostępnione do wykorzystania.
#XFLD: Space Schema
spaceSchema=Schemat przestrzeni
#XFLD: Enable Read Access (SQL)
enableReadAccess=Aktywuj dostęp z prawem odczytu (SQL)
#XMSG: Allow the user to grant read access to other users.
withGrantOptionHint=Zezwalaj użytkownikowi na udzielanie innym użytkownikom dostępu z prawem odczytu.
#XFLD: With Grant Option
withGrantOption=Z opcją przyznania
#XMSG: Make your space data available in your HDI containers.
enableHDIConsumptionHint=Udostępnij swoje dane przestrzeni w kontenerach HDI.
#XFLD: Enable HDI Consumption
enableHDIConsumption=Włącz wykorzystanie HDI
#XMSG: Write Access to the User’s Open SQL Schema
writeAccessTitle=Dostęp z prawem zapisu do schematu Open SQL użytkownika
#XMSG: write access hint
writeAccessHint=Możesz zezwolić użytkownikowi bazy danych na połączenie narzędzi zewnętrznych ze schematem Open SQL użytkownika w celu utworzenia encji danych oraz pozyskania danych do wykorzystania w tej przestrzeni.
#XFLD: Open SQL Schema
openSQLSchema=Schemat Open SQL
#XFLD: Enable Write Access (SQL DDL & DML)
enableWriteAccess=Aktywuj dostęp z prawem zapisu (SQL, DDL i DML)
#XMSG: audit hint
auditHint=Zarejestruj operacje odczytu i zmiany dla pozyskiwania w schemacie Open SQL.
#XMSG: data consumption hint
dataConsumptionHint=Możesz domyślnie udostępnić do wykorzystania wszystkie nowe widoki w przestrzeni. Modelerzy mogą zastąpić to ustawienie dla poszczególnych widoków za pomocą przycisku “Udostępnij do wykorzystania” w panelu bocznym wyprowadzania widoku. Możesz również wybrać formaty, w których udostępniane będą widoki.
#XFLD: Expose for Consumption by Default
exposeDataConsumption=Domyślnie udostępnij do wykorzystania
#XMSG: database users hint consumption hint
databaseUsersHint2New=Możesz tworzyć użytkowników bazy danych, aby połączyć narzędzia zewnętrzne z SAP Datasphere. Odpowiednie ustawienie uprawnień umożliwia zezwalanie użytkownikom na odczyt danych przestrzeni i tworzenie encji danych (DDL) oraz pozyskanie danych (DML) do wykorzystania w przestrzeni.
#XFLD: Read
read=Odczyt
#XFLD: Read (HDI)
readHDI=Odczyt (HDI)
#XFLD: Write
write=Zapis
#XMSG: HDI Containers Hint
HDIContainersHint2=Możesz aktywować dostęp do kontenerów SAP HANA Deployment Infrastructure (HDI) w swojej przestrzeni. Modelerzy mogą wykorzystać artefakty HDI jako źródła dla widoków, a klientom HDI można zapewnić dostęp do danych przestrzeni.
#XMSG: Open info dialog
openDatabaseUserInfoDialog=Otwórz okno dialogowe z informacjami
#XMSG: Database user is locked. Open info dialog to unlock
openLockedDatabaseUserInfoDialog=Użytkownik bazy danych jest zablokowany. Aby go odblokować, otwórz okno dialogowe.
#XFLD: Table
table=Tabela
#XFLD: Space section partner configuration title
partnerConnectionConfigurationSectionTitle=Połączenie partnera
#XFLD: Space section partner configuration subtitle
partnerConnectionConfigurationSectionSubTitle=Konfiguracja połączenia partnera
#XFLD: Space section partner configuration hint
partnerConnectionConfigurationSectionHint=Zdefiniuj własny kafelek połączenia partnera, dodając URL iFrame oraz ikonę. Ta konfiguracja jest dostępna tylko dla tego tenanta.
#XFLD: Table Name Field
partnerConnectionConfigurationName=Nazwa kafelka
#XFLD: Table iFrame URL field
partnerConnectionConfigurationiFrameURL=URL iFrame
#XFLD: Table iFrame Post Message Origin field
partnerConnectionConfigurationiFramePostMessageOrigin=Pochodzenie postMessage iFrame
#XFLD: Table icon/image name
partnerConnectionConfigurationIcon=Ikona
#XMSG: Table no result found
partnerConnectionConfigurationNotfound=Nie można było znaleźć konfiguracji połączenia partnera.
#XMSG: Partner Connection Confgurations are not available
partnerConnectionConfigurationNotAvailable=Nie można wyświetlić konfiguracji połączenia partnera, gdy baza danych czasu wykonania jest niedostępna.
#XFLD: Dialog Create Partner Connection Configuration
partnerConnectionConfigurationDialogTitleAdd=Utwórz konfigurację połączenia partnera
#XFLD: Dialog Create Partner Connection Configuration Upload Icon
partnerConnectionConfigurationButtonUloadPartnerIcon=Wczytaj ikonę
#XFLD: Dialog Create Partner Connection Configuration Select
partnerConnectionConfigurationSelectIcon=Wybierz (maksymalny rozmiar 200 KB)
#XFLD: Dialog Create Partner Connection Configuration Enter a name
partnerConnectionConfigurationNamePlaceholder=Przykładowy kafelek partnera
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL
partnerConnectionConfigurationiFrameURLPlaceholder=http://www.example.com
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL Check
partnerConnectionConfigurationiFramePostMessageOriginPlaceholder=.example.com
#XFLD: Dialog Create Partner Connection Configuration Browse
partnerConnectionConfigurationBrowseButton=Przeglądaj
#XFLD: Create Partner Connection Configuration Msg
partnerConnectionConfigurationUploadMsg=Pomyślnie utworzono konfigurację połączenia partnera.
#XMSG: Error while deleting Partner Connection configuration(s)
partnerConnectionConfigurationDeleteError=Wystąpił błąd podczas usuwania konfiguracji połączenia partnera.
#XMSG: Partner Connection Configuration has been successfully deleted
partnerConnectionConfigurationDeleteSuccess=Pomyślnie usunięto konfigurację połączenia partnera.
#XMSG: Error while retrieving Partner Connection Configurations
partnerConnectionConfigurationLoadError=Wystąpił błąd podczas wywoływania konfiguracji połączenia partnera.
#XMSG: Error fileSizeLimitExceeded
partnerConnectionConfigurationfileSizeLimitExceeded=Nie można było przesłać pliku, ponieważ przekracza maksymalny rozmiar 200 KB.
#XBUT: Create Partner Connection Configuration
partnerConnectionConfigurationButtonCreate=Utwórz konfigurację połączenia partnera
#XBUT: Delete Partner Connection Configuration
partnerConnectionConfigurationButtonDelete=Usuń konfigurację połączenia partnera.
#XMSG: Partner Tile Creation Failure
upsertPartnerTileFailed=Nie można było utworzyć kafelka partnera.
#XMSG: Partner Tile Deletion Failure
deletePartnerTileFailed=Nie można było usunąć kafelka partnera.
#XMSG: HANA cloud connector settings reset failure
resetHANASCCSettingsFailed=Ponowne dokonywanie ustawień niestandardowego konektora z chmurą SAP HANA nie powiodło się
#XFLD: Workload Class
workloadClass=Klasa obciążenia
#XFLD: Workload Management
workloadManagement=Zarządzanie obciążeniem
#XFLD: Priority
workloadClassPriority=Priorytet
#XMSG:
workloadManagementPriorityHint=Możesz określić ustalanie priorytetów tej przestrzeni podczas przesyłania zapytania do bazy danych. Wprowadź wartość od 1 (najniższy priorytet) do 8 (najwyższy priorytet). W sytuacji, w której przestrzenie konkurują o dostępne wątki, przestrzenie o wyższych priorytetach są uruchamiane przed przestrzeniami o niższych priorytetach.
#XMSG:
workloadClassPriorityHint=Możesz określić priorytet przestrzeni od 0 (najniższy) do 8 (najwyższy). Instrukcje przestrzeni o wyższym priorytecie są wykonywane przed instrukcjami pozostałych przestrzeni o niższym priorytecie. Domyślny priorytet to 5. Ponieważ wartość 9 jest zarezerwowana dla operacji systemowych, nie jest dostępny dla przestrzeni.
#XFLD: Statement Limits
workloadclassStatementLimits=Limity instrukcji
#XFLD: Workload Configuration
workloadConfiguration=Konfiguracja obciążenia
#XMSG:
workloadClassStatementLimitsHint=Można określić maksymalną liczbę (lub wartość procentową) wątków i gigabajtów pamięci możliwą do wykorzystania przez wykonywane równolegle instrukcje. Można wprowadzić dowolną wartość, w tym procentową, pomiędzy 0 (brak ograniczeń) a łączną ilością pamięci i liczbą wątków dostępnych w mandancie. \n\n Określenie limitu wątków może obniżyć wydajność. \n\n Określenie limitu pamięci, spowoduje, że instrukcje, które osiągną limit pamięci, nie będą wykonywane.
#XMSG:
workloadClassStatementLimitsDescription=Domyślna konfiguracja zapewnia duże limity zasobów, zapobiegając sytuacji, w której pojedyncza przestrzeń przeciąża system.
#XMSG:
workloadClassStatementLimitCustomDescription=Możesz określić maksymalne łączne limity dla wątków i pamięci, które mogą być wykorzystywane przez wykonywane równolegle instrukcje.
#XMSG:
totalStatementThreadLimitHelpText=Ustawienie zbyt niskiego limitu wątków może negatywnie wpłynąć na wydajność instrukcji, natomiast zbyt wysokie wartości lub wartość 0 mogą pozwolić przestrzeni na wykorzystanie wszystkich dostępnych wątków systemu.
#XMSG:
totalStatementMemoryLimitHelpText=Ustawienie zbyt niskiego limitu wątków może powodować błędy braku pamięci, natomiast zbyt wysokie wartości lub wartość 0 mogą pozwolić przestrzeni na wykorzystanie całej dostępnej pamięci systemu.
#XMSG:
totalStatementThreadLimitHelpTextRestricted=Wprowadź wartość procentową w przedziale między 1% a 70% (lub równoważną liczbę) dla łącznej liczby wątków dostępnych w tenancie. Ustawienie zbyt niskiego limitu wątków może negatywnie wpłynąć na wydajność instrukcji, natomiast zbyt wysokie wartości mogą negatywnie wpłynąć na wydajność instrukcji w innych przestrzeniach.
#XMSG:
totalStatementThreadLimitHelpTextDynamic=Wprowadź wartość procentową w przedziale między 1% a {0}% (lub równoważną liczbę) dla łącznej liczby wątków dostępnych w tenancie. Ustawienie zbyt niskiego limitu wątków może negatywnie wpłynąć na wydajność instrukcji, natomiast zbyt wysokie wartości mogą negatywnie wpłynąć na wydajność instrukcji w innych przestrzeniach.
#XMSG:
totalStatementMemoryLimitHelpTextNew=Wprowadź wartość lub wartość procentową w przedziale między 0 (brak limitu) a całkowitą pamięcią dostępną w tenancie. Ustawienie zbyt niskiego limitu może negatywnie wpłynąć na wydajność instrukcji, natomiast zbyt wysokie wartości mogą negatywnie wpłynąć na wydajność instrukcji w innych przestrzeniach.
#XFLD: Total Statement Thread Limit
totalStatementThreadLimit=Całkowity limit wątków instrukcji
#XFLD: Segmented button label
workloadclassPercentage=%
#XFLD: Segmented button label
workloadclassGigaByte=GB
#XFLD, 80: Segmented button label
workloadclassThreads=Wątki
#XFLD: Total Statement Memory Limit
totalStatementMemoryLimit=Całkowity limit pamięci instrukcji
#YMSE: Error while loading customer hana info
loadCustomerHanaInfoError=Wczytanie informacji SAP HANA Info klienta.
#XMSG:
minimumLimitReached=Osiągnięto limit minimalny.
#XMSG:
maximumLimitReached=Osiągnięto limit maksymalny.
#XMSG: Name Taken for Technical Name
technical-name-taken=Połączenie z wprowadzoną nazwą techniczną już istnieje. Wprowadź inną nazwę.
#XMSG: Name Too long for Technical Name
technical-name-too-long=Długość wprowadzonej nazwy technicznej przekracza 40 znaków. Wprowadź nazwę zawierającą mniejszą liczbę znaków.
#XMSG: Technical name field empty
technical-name-field-empty=Wprowadź nazwę techniczną.
#XMSG: Generic error when user enters invalid characters for a field
field-invalid-characters=Nazwa może składać się tylko z liter (a–z), liczb (0–9) i podkreśleń (_).
#XMSG: Generic error when user enters invalid initial characters for a field
field-underscore-trim=Wprowadzona nazwa nie powinna rozpoczynać się ani kończyć podkreśleniem (_).
#XFLD: Checkbox label for Enable Statement Limits
enableStatementLimits=Aktywuj limity instrukcji
#XFLD: Connection SubSection Settings Title in space details
connectionSettings=Ustawienia
#XMSG: Connections tool hint in Space details section
connectionsToolHint=Aby tworzyć lub edytować połączenia, otwórz aplikację Połączenia w nawigacji bocznej lub kliknij tutaj:
#XBUT: Connection list Open Connection Tool Button
button-connectiontool=Przejdź do aplikacji Połączenia
#XFLD: Not deployed label on space tile
notDeployedLabel=Przestrzeń nie została jeszcze wdrożona.
#XFLD: Not deployed additional text on space tile
notDeployedText=Wdróż przestrzeń.
#XFLD: Corrupt space label on space tile
corruptSpace=Coś poszło nie tak.
#XFLD: Corrupt space additional text on space tile
corruptSpaceHint=Spróbuj wdrożyć ponownie lub skontaktuj się z działem wsparcia
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Dane logów audytu
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Dane administracyjne
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Inne dane
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Dane w przestrzeniach
#XMSG: Unlock Space confirmation text
unlockSpaceConfirmation=Czy na pewno chcesz odblokować przestrzeń?
#XMSG: Lock Space confirmation text
lockSpaceConfirmation=Czy na pewno chcesz zablokować przestrzeń?
#XFLD: Lock
lock=Zablokuj
#XFLD: Unlock
unlock=Odblokuj
#XFLD: Locking
locking=Blokowanie
#XMSG: Success message after locking space
lockSpaceSuccessMsg=Zablokowano przestrzeń
#XMSG: Success message after unlocking space
unlockSpaceSuccessMsg=Odblokowano przestrzeń
#XMSG: Success message after locking spaces
lockSpacesSuccessMsg=Zablokowano przestrzenie
#XMSG: Success message after unlocking spaces
unlockSpacesSuccessMsg=Odblokowano przestrzenie
#YMSE: Error while locking a space
lockSpaceError=Nie można zablokować przestrzeni.
#YMSE: Error while unlocking a space
unlockSpaceError=Nie można odblokować przestrzeni.
#XTIT: popup title Warning
confirmationWarningTitle=Ostrzeżenie
#XMSG: Message strip message when a space was locked manually
manualLockedMessage=Przestrzeń zablokowano ręcznie.
#XMSG: Message strip message when a space was locked due to the amount of generated audit logs
auditLogLockedMessage=Przestrzeń została zablokowana przez system, ponieważ logi audytu zajmują dużą ilość miejsca GB na dysku.
#XMSG: Message strip message when a space was locked because the assigned quota was exceeded.
quotaLockedMessage=Przestrzeń została zablokowana przez system, ponieważ przekracza przydział pamięci lub miejsca na dysku.
#XMSG: Unlock Spaces confirmation text
unlockSpacesConfirmation=Czy na pewno chcesz odblokować wybrane przestrzenie?
#XMSG: Lock Spaces confirmation text
lockSpacesConfirmation=Czy na pewno chcesz zablokować wybrane przestrzenie?
#XTIT: Scoped role editor dialog title
scopedRoleEditorDialogTitle=Edytor ról z zakresu
#XTIT: ECN Management title
ecnManagementTitle=Zarządzanie przestrzenią i elastycznymi węzłami obliczeniowymi
#XFLD: ECNs
ecns=Elastyczne węzły obliczeniowe
#XFLD: ECN phase Ready
ecnReady=Gotowe
#XFLD: ECN phase Running
ecnRunning=Aktywne
#XFLD: ECN phase Initial
ecnInitial=Niegotowe
#XFLD: ECN phase Starting
ecnStarting=Uruchamianie
#XFLD: ECN phase Starting Failed
ecnStartingFailed=Uruchamianie nie powiodło się
#XFLD: ECN phase Stopping
ecnStopping=Zatrzymywanie
#XFLD: ECN phase Stopping Failed
ecnStoppingFailed=Zatrzymywanie nie powiodło się
#XBTN: Assign Button
assign=Przypisz przestrzenie
#XBTN: Start Header-Button
start=Rozpocznij
#XBTN: Update Header-Button
repair=Aktualizuj
#XBTN: Stop Header-Button
stop=Zatrzymaj
#XFLD: ECN hours remaining
ecnHoursRemaining=Pozostało 1000 godzin
#XFLD: ECN block-hours remaining
ecnBlockHoursRemaining=Pozostało {0} godz. bloku
#XFLD: ECN block-hour remaining
ecnBlockHourRemaining=Pozostała {0} godz. bloku
#XTIT: ECN creation dialog title
ecnCreationDialogTitle=Utwórz elastyczny węzeł obliczeniowy
#XTIT: ECN edit dialog title
ecnEditDialogTitle=Edytuj elastyczny węzeł obliczeniowy
#XTIT: ECN deletion dialog title
ecnDeletionDialogTitle=Usuń elastyczny węzeł obliczeniowy
#XTIT: ECN Space assignment dialog title
ecnSpaceAssignmentDialogTitle=Przypisanie przestrzeni
#XFLD: ECN ID
ECNIDLabel=Elastyczny węzeł obliczeniowy
#XTXT: Selected toolbar text
selectedToolbarText=Wybrane: {0}
#XTIT: Elastic Compute Nodes
ECNslong=Elastyczne węzły obliczeniowe
#XTIT: Object assignment - Table header: Objects available
objectsAvailable=Liczba obiektów
#XTIT: Object assignment - Dialog header text
selectObjects=Wybierz przestrzenie i obiekty, które chcesz przypisać do swojego elastycznego węzła obliczeniowego:
#XTIT: Object assignment - Table header title: Objects
objects=Obiekty
#XTIT: Object assignment - Table header: Type
type=Typ
#XMSG: Message strip delete database user audit warning
deleteDatabaseUserWarningMessageStrip=Pamiętaj, że usunięcie użytkownika bazy danych spowoduje usunięcie wszystkich wygenerowanych wpisów logu audytu. Jeśli chcesz zachować logi audytu, rozważ wyeksportowanie ich przed usunięciem użytkownika bazy danych.
#XMSG: Message strip delete HDI container audit logs impact
deleteHDIContainerWarningMessageStrip=Pamiętaj, że anulowanie przypisania kontenera HDI dla przestrzeni spowoduje usunięcie wszystkich wygenerowanych wpisów logu audytu. Jeśli chcesz zachować logi audytu, rozważ wyeksportowanie ich przed anulowaniem przypisania kontenera HDI.
#XTXT: All audit logs
allAuditLogs=Wszystkie wpisy logu audytu wygenerowane dla przestrzeni
#XMSG: Message strip disable audit warning
disableAuditWarningMessageStrip=Pamiętaj, że dezaktywacja reguł audytowania (operacje odczytu lub zmiany) spowoduje usunięcie wszystkich wpisów logu audytu. Jeśli chcesz zachować wpisy logu audytu, rozważ wyeksportowanie ich przed dezaktywacją reguł audytowania.
#XTIT: No Spaces assigned to ECN Illustration title
noSpacesECNTitle=Nie przypisano jeszcze przestrzeni ani obiektów
#XTIT: No Spaces assigned to ECN Illustration description
noSpacesECNDescription=Aby zacząć pracę z elastycznym węzłem obliczeniowym, przypisz do niej przestrzeń lub obiekty.
#XTIT: No Spaces Illustration title
noSpacesTitle=Nie utworzono jeszcze przestrzeni.
#XTIT: No Spaces Illustration description
noSpacesDescription=Utwórz przestrzeń, aby rozpocząć pozyskiwanie danych.
#XTIT: No Spaces Illustration title
noDeletedSpacesTitle=Kosz jest pusty
#XTIT: No Spaces Illustration description
noDeletedSpacesDescription=Możesz stąd przywrócić usunięte przestrzenie.
#XTXT:  {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDatabaseUsers=Po wdrożeniu przestrzeni następujący użytkownicy zostaną {0} usunięci i nie będzie można ich odtworzyć:
#XTIT: Delete database users
deleteDatabaseUsersTitle=Usuwanie użytkowników bazy danych
#YMSE: Error if ecn id already exists
ecnIDInputDuplicateError=ID już istnieje.
#YMSE: Error if ecn id contains invalid characters
ecnIDInputInvalidError=Użyj wyłącznie małych liter a - z i cyfr 0 – 9
#YMSE: Error if ecn id is too short
ecnIDInputTooShortError=ID musi mieć długość co najmniej{0} znaków.
#XMSG: ecn id length warning
ecnIdLengthWarning=Przekroczono maksymalną liczbę {0} znaków.
#XFLD: open System Monitor
systemMonitor=Monitorowanie systemu
#XFLD: open ECN schedule dialog menu entry
schedule=Harmonogram
#XFLD: open create ECN schedule dialog
createSchedule=Utwórz harmonogram
#XFLD: open change ECN schedule dialog
changeSchedule=Edytuj harmonogram
#XFLD: open delete ECN schedule dialog
deleteSchedule=Usuń harmonogram
#XFLD: open self assign ECN schedule dialog
selfAssignSchedule=Przypisz harmonogram do mnie
#XFLD: open pause ECN schedule dialog
pauseSchedule=Wstrzymaj harmonogram
#XFLD: open resume ECN schedule dialog
resumeSchedule=Wznów harmonogram
#XFLD: View Logs
viewLogs=Wyświetl logi
#XFLD: Compute Blocks
computeBlocks=Bloki obliczania
#XFLD: Memory label in ECN creation dialog
ecnMemory=Pamięć (GB)
#XFLD: Storage label in ECN creation dialog
ecnStorage=Pamięć (GB)
#XFLD: Number of CPU label in ECN creation dialog
ecnCPUCount=Liczba CPU
#XFLD: ECN updated by label
changedBy=Autor zmiany
#XFLD: ECN updated on label
changedOn=Data zmiany
#XMSG: Success message after creating a Elastic Compute Node
createEcnSuccessMessage=Utworzono elastyczny węzeł obliczeniowy
#YMSE: Error while creating a Elastic Compute Node
createEcnError=Nie można było utworzyć elastycznego węzła obliczeniowego
#XMSG: Success message after updating a Elastic Compute Node
updateEcnSuccessMessage=Zaktualizowano elastyczny węzeł obliczeniowy
#XMSG: Error while updating a Elastic Compute Node
udpateEcnError=Nie można było zaktualizować elastycznego węzła obliczeniowego
#XMSG: Success message after deleting a Elastic Compute Node
deleteEcnSuccessMessage=Usunięto elastyczny węzeł obliczeniowy
#YMSE: Error while deleting a Elastic Compute Node
deleteEcnError=Nie można było usunąć elastycznego węzła obliczeniowego
#XMSG: Success message after starting a Elastic Compute Node
startingEcnSuccessMessage=Rozpoczynanie elastycznego węzła obliczeniowego
#XMSG: Success message after stopping a Elastic Compute Node
stoppingEcnSuccessMessage=Zatrzymywanie elastycznego węzła obliczeniowego
#YMSE: Error while starting a Elastic Compute Node
startingEcnError=Nie można było rozpocząć elastycznego węzła obliczeniowego
#YMSE: Error while stopping a Elastic Compute Node
stoppingEcnError=Nie można było zatrzymać elastycznego węzła obliczeniowego
#XBUT: Add Object button for an ECN
assignObjects=Dodaj obiekty
#XFLD: Enable auto object assignment label
enableAutoObjAssignment=Automatycznie dodaj wszystkie obiekty
#XFLD: object type label to be assigned
objectTypeLabel=Typ (wykorzystanie semantyczne)
#XFLD: assigned object type label
assignedObjectTypeLabel=Typ
#XFLD: technical name label
TechnicalNameLabel=Nazwa techniczna
#XFLD: Object selecttion ECN label
objectSelectionECNLabel=Wybierz obiekty, które chcesz dodać do elastycznego węzła obliczeniowego
#XTIT: Add objects dialog title
assignObjectsTitle=Przypisanie obiektów
#XFLD: object label with object count
objectLabel=Obiekt
#XMSG: No objects available to add message.
noObjectsToAssign=Brak dostępnych obiektów do przypisania.
#XMSG: No objects assigned message.
noAssignedObjects=Nie przypisano obiektów.
#XTIT: Remove objects popup title
removeObjectsOrSpacesConfirmationTitle=Ostrzeżenie
#XTIT: Delete ECN popup button and title
deleteECNConfirmationButtonAndTitle=Usuń
#XMSG: Remove objects popup text
removeObjectsConfirmation=Czy na pewno chcesz usunąć wybrane obiekty?
#XMSG: Remove spaces popup text
removeSpacesConfirmation=Czy na pewno chcesz usunąć wybrane przestrzenie?
#XBUT: Confirmation button for objects dialog
removeObjectsOrSpacesConfirmationBtn=Usuń przestrzenie
#XMSG: Success message after removing objects
removeObjectsSuccessMessage=Usunięto udostępnione obiekty
#XMSG: Success message after assigning objects
assignObjectsSuccessMessage=Przypisano udostępnione obiekty
#XFLD: auto object assignment label for space tile
autoObjAssignmentLabel=Wszystkie udostępnione obiekty
#XFLD: Spaces tab label
spacesTabLabel=Przestrzenie
#XFLD: Exposed objects tab label
consumptionObjectsTabLabel=Udostępnione obiekty
#XMSG: Success message after unassigning spaces
spacesUnassignedSuccessMessage=Przestrzenie zostały usunięte
#XMSG: Success message after unassigning space
spaceUnassignedSuccessMessage=Przestrzeń została usunięta
#YMSE: Error while assigning / unassigning spaces
spacesAssignUnassignError=Nie można było przypisać lub usunąć przestrzeni.
#YMSE: Error while removing objects
removeObjectsError=Nie mogliśmy przypisać lub usunąć obiektów.
#YMSE: Error while removing object
removeObjectError=Nie mogliśmy przypisać lub usunąć obiektu.
#YMSE: Error when saving the ecn with invalid value
invalidSizingPlanError=Wcześniej wybrana liczba nie jest już prawidłowa. Wybierz prawidłową liczbę.
#YMSE: Error when saving the ecn with invalid value
invalidPerformanceClassError=Wybierz prawidłową klasę wydajności.
#YMSE: Error when saving the ecn with invalid value
performanceClassNotAvailableError=Wcześniej wybrana klasa wydajności "{0}" jest obecnie nieprawidłowa. Wybierz prawidłową klasę wydajności.
#XTXT: Delete ECN confirmation text
deleteConfirmationTxtECN=Czy na pewno chcesz usunąć elastyczny węzeł obliczeniowy?
#XFLD: tooltip for ? button
help=Pomoc
#XFLD: ECN edit button label
editECN=Konfiguruj
#XFLD: Technical type label for ERModel
DWC_ERMODEL=Diagram związków encji
#XFLD: Technical type label for Local Table
DWC_LOCAL_TABLE=Tabela lokalna
#XFLD: Technical type label for Remote Table
DWC_REMOTE_TABLE=Tabela zdalna
#XFLD: Technical type label for Analytic Model
DWC_ANALYTIC_MODEL=Model analityczny
#XFLD: Technical type label for Task Chain
DWC_TASKCHAIN=Łańcuch zadań
#XFLD: Technical type label for Data Flow
DWC_DATAFLOW=Przepływ danych
#XFLD: Technical type label for Replication Flow
DWC_REPLICATIONFLOW=Przepływ replikacji
#XFLD: Technical type label for Transformation Flow
DWC_TRANSFORMATIONFLOW=Przepływ transformacji
#XFLD: Technical type label for Intelligent Lookup
DWC_IDT=Inteligentne wyszukiwanie
#XFLD: Technical type label for Repository
DWC_REPOSITORY_PACKAGE=Repozytorium
#XFLD: Technical type label for Enterprise Search
DWC_ESMODEL=Wyszukiwanie w przedsiębiorstwie
#XFLD: Technical type label for View
DWC_VIEW=Widok
#XFLD: Technical type label for Data Product
DWC_DATAPRODUCT=Produkt danych
#XFLD: Technical type label for Data Access Control
DWC_DAC=Kontrola dostępu do danych
#XFLD: Technical type label for Folder
DWC_FOLDER=Folder
#XFLD: Technical type label for Data Entity
DWC_BUSINESS_ENTITY=Encja biznesowa
#XFLD: Technical type label for Business Entity Variant
DWC_BUSINESS_ENTITY_VARIANT=Wariant encji biznesowej
#XFLD: Technical type label for Responsibility Scenario
DWC_AUTH_SCENARIO=Scenariusz odpowiedzialności
#XFLD: Technical type label for Fact Model
DWC_FACT_MODEL=Model faktów
#XFLD: Technical type label for Transformation Flow
DWC_PERSPECTIVE=Perspektywa
#XFLD: Technical type label for Consumption Model
DWC_CONSUMPTION_MODEL=Model wykorzystania
#XFLD: Technical type label for Remote Connection
DWC_REMOTECONNECTION=Połączenie zdalne
#XFLD: Technical type label for Fact Model Variant
DWC_FACT_MODEL_VARIANT=Wariant modelu faktów
#XMSG: Schedule created alert message
createScheduleSuccess=Utworzono harmonogram
#XMSG: Schedule updated alert message
updateScheduleSuccess=Zaktualizowano harmonogram
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Usunięto harmonogram
#XMSG: Schedule takeover success message
ownerChangeScheduleSuccess=Masz przypisany harmonogram
#XMSG: Message for starting pausing of schedule
pauseScheduleStarted=Wstrzymywanie 1 harmonogramu
#XMSG: Message for starting resuming of schedule
resumeScheduleStarted=Wznawianie 1 harmonogramu
#XFLD: Segmented button label
availableSpacesButton=Dostępne
#XFLD: Segmented button label
selectedSpacesButton=Wybrane
#XFLD: Visit website button text
visitWebsite=Odwiedź stronę WWW
#XMSG: Warning message on unselecting source language
sourceLanguageRemovalWarning=Wcześniej wybrany język źródłowy zostanie usunięty.
#XBUT: enable translation confirmation button
enableTranslationConfirmationBtn=Włącz
#XFLD: ECN performance class label
performanceClassLabel=Klasa wydajności
#XTXT performance class memory text
memoryText=Pamięciowa
#XTXT performance class compute text
computeText=Obliczeniowa
#XTXT performance class high-compute text
highComputeText=Wysoka wydajność obliczeniowa
#XBUT: Recycle Bin Button Text
recycleBin=Kosz
#XBUT: Restore Button Text
restore=Przywróć
#XMSG: Warning message for new Workload Management UI
priorityWarning=Ten obszar jest dostępny tylko do odczytu. Możesz zmienić priorytet przestrzeni w obszarze System / Konfiguracja / Zarządzanie obciążeniem.
#XMSG: Warning message for new Workload Management UI
workloadWarning=Ten obszar jest dostępny tylko do odczytu. Możesz zmienić konfigurację obciążenia przestrzeni w obszarze System / Konfiguracja / Zarządzanie obciążeniem.
#XCOL: Space table-view column: Apache Spark vCPUs
sparkManagementVCPUs=Apache Spark - vCPU
#XCOL: Space table-view column: Apache Spark Memory (GB)
sparkManagementMemory=Apache Spark - pamięć (GB)
#XCOL: Space type (from Space Capability) for Data Ingestion Spaces
ingestionSpaceType=Pozyskiwanie produktu danych
#XFLD: Deployment in progress nodatatext for spark table
sparkTableDeploymentInProgress=Brak dostępnych danych, ponieważ ta przestrzeń jest aktualnie wdrażana
#XFLD: Runtime data being loaded nodatatext for spark table
sparkTableDeploymentLoading=Brak dostępnych danych, ponieważ aktualnie trwa załadunek do tej przestrzeni
#XCOL: Space type (from Space Capability) for BW Private Cloud Edition
BWPCESpaceType=SAP BW
#XBUT: Go to Instance Mappings Button
navToMappingsButton=Edytuj mapowania instancji
#XFLD: SAP HANA Data Lake Storage label if less than 1GB
dataLakeStorageUsed=< 1 GB
