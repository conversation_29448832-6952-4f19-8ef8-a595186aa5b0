#DO NOT REMOVE________________________
#XFLD: Breadcrumb for space monitoring page
spaceMonitorBreadcrumb=Monitorização
#XTXT: Type name for spaces in browser tab page title
space=Espaço
#_____________________________________
#XFLD: Spaces label in
spaces=Espaços
#XFLD: Manage plan button text
manageQuotaButtonText=Gerir plano
#XBUT: Manage resources button
manageResourcesButton=Gerir recursos
#XFLD: Create space button tooltip
createSpace=Criar espaço
#XFLD: Create
create=Criar
#XFLD: Deploy
deploy=Implementar
#XFLD: Page
page=Página
#XFLD: Cancel
cancel=Cancelar
#XFLD: Update
update=Atualizar
#XFLD: Save
save=Guardar
#XFLD: OK
ok=OK
#XFLD: days
days=Dias
#XFLD: Space tile edit button label
edit=Editar
#XFLD: Auto Assign all objects to space
autoAssign=Atribuir automaticamente
#XFLD: Space tile open monitoring button label
openMonitoring=Monitorizar
#XFLD: Delete
delete=Eliminar
#XFLD: Copy Space
copy=Copiar
#XFLD: Close
close=Fechar
#XCOL: Space table-view column status
status=Estado
#XFLD: Space status active
activeLabel=Ativo
#XFLD: Space status locked
lockedLabel=Bloqueado
#XFLD: Space status critical
criticalLabel=Crítico
#XFLD: Space status cold
coldLabel=Frio
#XFLD: Space status deleted
deletedLabel=Eliminado
#XFLD: Space status unknown
unknownLabel=Desconhecido
#XFLD: Space status ok
okLabel=Saudável
#XFLD: Database user expired
expired=Expirado
#XFLD: deployed
deployed=Implementado
#XFLD: not deployed
notDeployed=Não implementado
#XFLD: changes to deploy
changesToDeploy=Alterações a implementar
#XFLD: pending
pending=A implementar
#XFLD: designtime error
designtimeError=Erro em tempo de design
#XFLD: runtime error
runtimeError=Erro em tempo de execução
#XFLD: Space created by label
createdBy=Criado por
#XFLD: Space created on label
createdOn=Criado a
#XFLD: Space deployed on label
deployedOn=Implementado em
#XFLD: Space ID label
spaceID=ID de espaço
#XFLD: Priority label
priority=Prioridade
#XFLD: Space Priority label
spacePriority=Prioridade de espaço
#XFLD: Space Configuration label
spaceConfiguration=Configuração de espaço
#XFLD: Not available
notAvailable=Não disponível
#XFLD: WorkloadType default
default=Padrão
#XFLD: WorkloadType custom
custom=Personalizado
#XFLD: Data Lake option label; refers to component SAP HANA Cloud, Data Lake
dataLakeAccess=Acesso a Data Lake
#XFLD: Translation label
translationLabel=Tradução
#XFLD: Source language label
sourceLanguageLabel=Idioma de origem
#XFLD: Translation CheckBox label
translationCheckBox=Ativar tradução
#XTXT: Popover hint that space has to be deployed to show user details
openDatabaseUserHintPopoverText=Implemente espaço para aceder aos detalhes do utilizador.
#XTXT: Popover hint that space has to be deployed to open database explorer
openDatabaseExplorerHintPopoverText=Implemente espaço para abrir o explorador de base de dados.
#XTXT: Text for when the Data Lake check box is disabled
dataLakeOptionDisabled=Não pode utilizar este espaço para aceder ao Data Lake porque já está a ser utilizado por outro espaço.
#XFLD: Data Lake check box text; refers to component SAP HANA Cloud, Data Lake
dataLakeCheckBox=Utilize este espaço para aceder ao Data Lake.
#XFLD: Space Priority minimum label extension
low=Baixo
#XFLD: Space Priority maximum label extension
high=Alto
#XFLD: Space name label
spaceName=Nome do espaço
#XFLD: Enable deploy objects checkbox
enableDeployObjects=Implementar objetos
#XTIT: Copy spaces dialog title
copySpaceDialogTitle=Copiar {0}
#XTXT Human readable text for unselected source language
unSelectedSourceLanguage=(Não selecionado)
#XTXT Human readable text for language code "af"
af=Africâner
#XTXT Human readable text for language code "ar"
ar=Árabe
#XTXT Human readable text for language code "bg"
bg=Búlgaro
#XTXT Human readable text for language code "ca"
ca=Catalão
#XTXT Human readable text for language code "zh"
zh=Chinês Simplificado
#XTXT Human readable text for language code "zf"
zf=Chinês
#XTXT Human readable text for language code "hr"
hr=Croata
#XTXT Human readable text for language code "cs"
cs=Checo
#XTXT Human readable text for language code "cy"
cy=Galês
#XTXT Human readable text for language code "da"
da=Dinamarquês
#XTXT Human readable text for language code "nl"
nl=Holandês
#XTXT Human readable text for language code "en-UK"
en-UK=Inglês (Reino Unido)
#XTXT Human readable text for language code "en"
en=Inglês (Estados Unidos)
#XTXT Human readable text for language code "et"
et=Estónio
#XTXT Human readable text for language code "fa"
fa=Persa
#XTXT Human readable text for language code "fi"
fi=Finlandês
#XTXT Human readable text for language code "fr-CA"
fr-CA=Francês (Canadá)
#XTXT Human readable text for language code "fr"
fr=Francês
#XTXT Human readable text for language code "de"
de=Alemão
#XTXT Human readable text for language code "el"
el=Grego
#XTXT Human readable text for language code "he"
he=Hebraico
#XTXT Human readable text for language code "hi"
hi=Híndi
#XTXT Human readable text for language code "hu"
hu=Húngaro
#XTXT Human readable text for language code "is"
is=Islandês
#XTXT Human readable text for language code "id"
id=Bahasa Indonesia
#XTXT Human readable text for language code "it"
it=Italiano
#XTXT Human readable text for language code "ja"
ja=Japonês
#XTXT Human readable text for language code "kk"
kk=Cazaque
#XTXT Human readable text for language code "ko"
ko=Coreano
#XTXT Human readable text for language code "lv"
lv=Letão
#XTXT Human readable text for language code "lt"
lt=Lituano
#XTXT Human readable text for language code "ms"
ms=Bahasa Melayu
#XTXT Human readable text for language code "no"
no=Norueguês
#XTXT Human readable text for language code "pl"
pl=Polaco
#XTXT Human readable text for language code "pt"
pt=Português (Brasil)
#XTXT Human readable text for language code "pt-PT"
pt-PT=Português (Portugal)
#XTXT Human readable text for language code "ro"
ro=Romeno
#XTXT Human readable text for language code "ru"
ru=Russo
#XTXT Human readable text for language code "sr"
sr=Sérvio
#XTXT Human readable text for language code "sh"
sh=Servo-croata
#XTXT Human readable text for language code "sk"
sk=Eslovaco
#XTXT Human readable text for language code "sl"
sl=Esloveno
#XTXT Human readable text for language code "es"
es=Espanhol
#XTXT Human readable text for language code "es-MX"
es-MX=Espanhol (México)
#XTXT Human readable text for language code "sv"
sv=Sueco
#XTXT Human readable text for language code "th"
th=Tailandês
#XTXT Human readable text for language code "tr"
tr=Turco
#XTXT Human readable text for language code "uk"
uk=Ucraniano
#XTXT Human readable text for language code "vi"
vi=Vietnamita
#XTIT: Delete spaces confirmation title
deleteConfirmationHeaderText=Eliminar espaços
#XTXT: Logically delete space delete confirmation text with space name
logicallyDeleteConfirmationBodyTextWithSpaceName=Tem a certeza de que quer mover espaço "{0}" para a reciclagem?
#XTXT: Logically dpace delete confirmation text for multiple spaces
logicallyDeleteConfirmationBodyTextMultiple=Tem a certeza de que quer mover os espaços selecionados "{0}" para a reciclagem?
#XTXT: Delete space delete confirmation text with space name
deleteConfirmationBodyTextWithSpaceName=Tem a certeza de que pretende eliminar o espaço "{0}"? Esta ação não pode ser anulada.
#XTXT: Delete space delete confirmation text for multiple spaces
deleteConfirmationBodyTextMultiple=Tem a certeza de que pretende eliminar os {0} espaços selecionados? Esta ação não pode ser anulada. O seguinte conteúdo será eliminado {1}:
#XTXT: permanently
permanently=de forma permanente
#XTXT: {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDetail=O seguinte conteúdo será eliminado {0} e não pode ser recuperado:
#XTXT: Hint Delete space confirmation text
deleteConfirmationHint=Digite {0} para confirmar a eliminação.
#XTXT: Delete space confirmation error check spelling
deleteConfirmationErrorCheckSpelling=Verifique a ortografia e tente novamente.
#XTXT: All Spaces
allSpaces=Todos os espaços
#XTXT: All data
allData=Todos os objetos e dados contidos no espaço
#XTXT: All connections
allConnections=Todas as ligações definidas no espaço
#XFLD: Space tile selection box tooltip
clickToSelect=Clicar para selecionar
#XTXT: All database users
allDatabaseUsers=Todos os objetos e dados contidos em qualquer esquema Open SQL associado ao espaço
#XFLD: remove members button tooltip
deleteUsers=Remover membros
#XTXT: Space long description text
description=Descrição (máximo de 4000 carateres)
#XFLD: Add Members button tooltip
addUsers=Adicionar membros
#XFLD: Add Users button tooltip
addUsersTooltip=Adicionar utilizadores
#XFLD: Edit Users button tooltip
editUsersTooltip=Editar utilizadores
#XFLD: Remove Users button tooltip
removeUsersTooltip=Remover utilizadores
#XFLD: Searchfield placeholder
filter=Procurar
#XCOL: Users table-view column health
health=Estado de funcionamento
#XCOL: Users table-view column access
access=Acesso
#XFLD: No user found nodatatext
noDataText=Nenhum utilizador encontrado
#XTIT: Members dialog title
selectUserDialogTitle=Adicionar membros
#XTIT: User dialog title
addUserDialogTitle=Adicionar utilizadores
#XTIT: Delete connections confirmation title
deleteConnectionConfirmationHeaderText=Eliminar ligações
#XTIT: Delete single connection confirmation title
deleteConnectionConfirmationHeaderTextSingle=Eliminar ligação
#XTXT: Delete connection delete confirmation text
deleteConnectionConfirmationBodyText=Tem a certeza de que pretende eliminar as ligações selecionadas? Elas serão removidas de forma permanente.
#XTIT: Remote source dialog title
selectRemoteConnectionDialogTitle=Selecionar ligações
#XTIT: Share connection dialog title
connectionSharingDialogTitle=Partilhar ligação
#XTIT: Connections shared from other spaces table title
sharedConnectionsFromOtherSpaces=Ligações partilhadas
#XFLD: Add remote source button tooltip
addRemoteConnections=Adicionar ligações
#XFLD: Delete remote source button tooltip
deleteRemoteConnections=Remover ligações
#XFLD: Share remote source button tooltip
shareConnections=Partilhar ligações
#XFLD: Tile-layout tooltip
tileLayout=Esquema de mosaico
#XFLD: Table-layout tooltip
tableLayout=Esquema de tabela
#XMSG: Success message after creating space
createSpaceSuccessMessage=Espaço criado
#XMSG: Success message after copying space
copySpaceSuccessMessage=A copiar espaço "{0}" para espaço "{1}"
#XMSG: Success message after deploying space
deploymentSuccessMessage=A implementação de espaço foi iniciada
#XMSG: Success message after Apache Spark update started
sparkUpdateStartedSuccessMessage=A atualização de Apache Spark foi iniciada
#YMSE: Error while starting Apache Spark update
sparkUpdateStartedErrorMessage=Falha ao atualizar Apache Spark
#XMSG: Success message after updating space
updateSettingsSuccessMessage=Detalhes de espaço atualizados
#XMSG: Success message after unlocking space
unlockSpaceSuccessMessage=Espaço desbloqueado temporariamente
#XMSG: Success message after deleting a single space
deleteSpaceSuccessMessage=Espaço eliminado
#XMSG: Success message after deleting multiple spaces
deleteSpacesSuccessMessage=Espaços eliminados
#XMSG: Success message after restoring a single space
restoreSpaceSuccessMessage=Espaço restaurado
#XMSG: Success message after restoring multiple spaces
restoreSpacesSuccessMessage=Espaços restaurados
#YMSE: Error while updating settings
updateSettingsFailureMessage=Não foi possível atualizar as definições de espaço.
#YMSE: Error while updating data lake option
dataLakeEnabledInOtherSpace=O Data Lake já está atribuído a outro espaço. Apenas um espaço de cada vez pode aceder ao Data Lake.
#YMSE: Error while updating data lake option
virtualTablesExists=Não pode anular a atribuição do Data Lake deste espaço porque ainda existem dependências de tabelas virtuais*. Elimine as tabelas virtuais para anular a atribuição do Data Lake deste espaço.
#YMSE: Error while unlocking space
unlockSpaceFailureMessage=Não foi possível desbloquear o espaço.
#YMSE: Error while creating space
createSpaceError=Não foi possível criar o espaço.
#YMSE: Error while creating duplicate space
createDuplicateSpaceError=Já existe um espaço com o nome {0}.
#YMSE: Error while deleting a single space
deleteSpaceError=Não foi possível eliminar o espaço.
#YMSE: Error details while deleting a single space
deleteSpaceDetailedError=O espaço “{0}” já não está a funcionar adequadamente. Tente eliminá-lo novamente. Se ainda não funcionar, peça ao administrador para eliminar o seu espaço ou abra um pedido.
#YMSE: Error while deleting a single space in files
deleteSpaceInFilesError=Não foi possível eliminar dados de espaço em ficheiros.
#YMSE: Error while deleting a single space users
deleteSpaceUsersError=Não foi possível remover os utilizadores.
#YMSE: Error while deleting a single space schema
deleteSpaceSchemasError=Não foi possível remover os esquemas.
#YMSE: Error while deleting a single space connections
deleteSpaceConnectionsError=Não foi possível remover as ligações.
#YMSE: Error while deleting a single space data
deleteSpaceDataError=Não foi possível eliminar os dados de espaço.
#YMSE: Error while deleting multiple spaces
deleteSpacesError=Não foi possível eliminar os espaços.
#YMSE: Error while restoring a single space
restoreSpaceError=Não foi possível restaurar o espaço.
#YMSE: Error while restoring multiple spaces
restoreSpacesError=Não foi possível restaurar os espaços.
#YMSE: Error while creating users
createUsersError=Não foi possível adicionar os utilizadores.
#YMSE: Error while removing users
removeUsersError=Não conseguimos remover os utilizadores.
#YMSE: Error while removing user
removeUserError=não foi possível remover o utilizador.
#YMSE: Error assigning roles to a user
roleAssignUnAssignError=Não conseguimos adicionar o utilizador à função incluída no âmbito selecionada. \n\n Não pode adicionar-se a si mesmo a uma função incluída no âmbito. Pode pedir ao seu administrador que o adicione a uma função incluída no âmbito.
#YMSE: Error assigning user to the space
userAssignError=Não foi possível atribuir o utilizador ao espaço. \n\n O utilizador já está atribuído ao número máximo permitido (100) de espaços em todas as funções incluídas no âmbito.
#YMSE: Error assigning users to the space
usersAssignError=Não foi possível atribuir os utilizadores ao espaço. \n\n O utilizador já está atribuído ao número máximo permitido (100) de espaços em todas as funções incluídas no âmbito.
#YMSE: Error while fetching users
fetchUserWithScopedRolesError=Não conseguimos obter os utilizadores. Tente novamente mais tarde.
#YMSE: Error while fetching scoped roles
fetchScopedRolesError=Não conseguimos obter as funções incluídas no âmbito.
#YMSE: Error while fetching members
fetchUserError=Não foi possível obter os membros. Tente novamente mais tarde.
#YMSE: Error while loading run-time database
loadRuntimeError=Não conseguimos carregar informações da base de dados de tempo de execução.
#YMSE: Error while loading spaces
loadSpacesError=Lamentamos, mas ocorreu algum problema ao tentar obter os espaços.
#YMSE: Error while loading haas resources
loadStorageError=Lamentamos, mas ocorreu algum problema ao tentar obter os dados de armazenamento.
#YMSE: Error no data could be loaded
loadDataError=Lamentamos, mas ocorreu algum problema ao tentar obter os dados.
#XFLD: Click to refresh storage data
clickToRefresh=Clique aqui para tentar novamente.
#XTIT: Delete space popup title
deleteSpacePopupTitle=Eliminar espaço
#XCOL: Spaces table-view column name
name=Nome
#XCOL: Spaces table-view deployment status
deploymentStatus=Estado de implementação
#XFLD: Disk label in space details
storageLabel=Disco (GB)
#XFLD: In-Memory label in space details
ramLabel=Memória (GB)
#XFLD: Memory label on space card
memory=Memória para armazenamento
#XFLD: Storage Assignment label in space details
storrageAssignmentLabel=Armazenamento de espaço
#XFLD: Storage Type label in space details
storageTypeLabel=Tipo de armazenamento
#XFLD: Enable Space Quota
enableSpaceQuota=Ativar quota de espaço
#XFLD: No Space Quota
noSpaceQuota=Nenhuma quota de espaço
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
diskAndInMemoryText=Base de dados SAP HANA (disco e memória interna)
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
dataLakeFilesText=Ficheiros Data Lake SAP HANA
#XFLD: Available scoped roles label
availableRoles=Funções incluídas no âmbito disponíveis
#XFLD: Selected scoped roles label
selectedRoles=Funções incluídas no âmbito selecionadas
#XCOL: Spaces table-view column models
models=Modelos
#XCOL: Spaces table-view column users
users=Utilizadores
#XCOL: Spaces table-view column connections
connections=Ligações
#XFLD: Section header overview in space detail
overview=Descrição geral
#XFLD: Section header Apache Spark in space detail
sparkManagementSection=Apache Spark
#XFLD: Section header Apache Spark in space detail
sparkManagementSubSection=Aplicações
#XFLD: Section header Apache Spark in space detail
taskAssignmentSubSection=Atribuição de tarefas
#XFLD: vCPU label in Apache Spark section
vCPULabel=vCPUs
#XFLD: Memory label in Apache Spark section
memoryLabel=Memória (GB)
#XFLD: space configuration label in Apache Spark section
spaceConfigurationLabel=Configuração de espaço
#XFLD: Space Source label
sparkApplicationLabel=Aplicação
#XFLD: Cluster Size label
clusterSizeLabel=Tamanho do cluster
#XFLD: Driver label
driverLabel=Motorista
#XFLD: Executor label
executorLabel=Executor
#XFLD: max label
maxLabel=Máximo utilizado
#XFLD: TrF Default label
trFDefaultLabel=Predefinição de fluxo de transformação
#XFLD: Merge Default label
mergeDefaultLabel=Unir predefinição
#XFLD: Optimize Default label
optimizeDefaultLabel=Otimizar predefinição
#XFLD: Deployment Default label
deploymentDefaultLabel=Implementação de tabela local (ficheiro)
#XFLD: max spark source label
driverExecutorMaxUsedSparkSource={0} CPU / {1} GB
#XFLD: Object type label
taskObjectTypeLabel=Tipo de objeto
#XFLD: Task activity label
taskActivityLabel=Atividade
#XFLD: Task Application ID label
taskApplicationIDLabel=Aplicação predefinida
#XFLD: Section header in space detail
generalSettings=Definições gerais
#XMSG: Message strip message when a space is locked
spaceLockedMessageStrip=Este espaço está atualmente bloqueado pelo sistema.
#XMSG: Message strip message deploy warning
deployWarningMessageStrip=As alterações nesta secção serão implementadas imediatamente.
#XMSG: Message strip message modify space configuration warning
modifySpaceConfigurationMessageStrip=Tenha em atenção que alterar estes valores pode causar problemas de desempenho.
#XFLD: Button text to unlock the space again
unlockSpace=Desbloquear espaço
#XFLD: Info text for audit log formatted message
auditLogText=Ative os registos de auditoria para registar as ações de leitura ou alteração (políticas de auditoria). Em seguida, os administradores podem analisar quem executou que ação, em que momento.
#XMSG: Message strip  audit warning
auditLogWarningMessageStrip=Os registos de auditoria podem consumir uma grande quantidade de armazenamento em disco no seu inquilino. Se ativar uma política de auditoria (ações de leitura ou alteração), deve monitorizar regularmente a utilização do armazenamento em disco (através do cartão Armazenamento em disco utilizado no Monitor do sistema) para evitar falhas totais do disco que podem levar a interrupções do serviço. Se desativar uma política de auditoria, todas as entradas do registo de auditoria serão eliminadas. Se quiser manter as entradas do registo de auditoria, considere exportá-las antes de desativar a política de auditoria.
#XFLD: Message strip link text to open in app help to guide user what to do to unlock his space permanently
unlockSpaceHelp=Mostrar ajuda
#XMSG: Message strip message when a space will be locked again
spaceTemporarilyUnlockedMessageStrip=Este espaço excede o respetivo armazenamento de espaço e será bloqueado em {0} {1}.
#XMSG: Unit for remaining time until space is locked again
hours=horas
#XMSG: Unit for remaining time until space is locked again
minutes=minutos
#XFLD: Section header in space detail for auditing
detailsSectionAuditing=Auditoria
#XFLD: Subsection header in space detail for auditing
auditing=Definições de auditoria de espaço
#XFLD: Hot space tooltip
hotSpaceCountTooltip=Espaços críticos: o armazenamento utilizado é superior a 90%.
#XFLD: Green space tooltip
greenSpaceCountTooltip=Espaços saudáveis: o armazenamento utilizado está entre 6% e 90%.
#XFLD: Cold space tooltip
coldSpaceCountTooltip=Espaços frios: o armazenamento utilizado é igual ou inferior a 5%.
#XFLD: Critical space tooltip
criticalSpaceCountTooltip=Espaços críticos: o armazenamento utilizado é superior a 90%.
#XFLD: Green space tooltip
okSpaceCountTooltip=Espaços saudáveis: o armazenamento utilizado está entre 6% e 90%.
#XFLD: Locked space tooltip
lockedSpaceCountTooltipNew=Espaços bloqueados: bloqueados devido a memória insuficiente.
#XFLD: Locked space tooltip
lockedSpaceCountTooltip=Espaços bloqueados
#YMSE: Error while deleting remote source
deleteRemoteError=Não foi possível remover as ligações.
#XFLD: Space ID input tooltip
spaceIDInfoTooltip=Não é possível alterar o ID de espaço mais tarde.\nCarateres válidos A - Z, 0 - 9, e _
#YMSE: Error when space name is empty
spaceNameEmptyValidationError=Introduzir nome do espaço.
#YMSE: Error when ECN Business name is empty
ecnNameEmptyValidationError=Introduzir um nome comercial.
#YMSE: Error when space id is empty
spaceIDInputEmptyValidationError=Introduzir ID de espaço.
#YMSE: Error when space id contains invalid characters
spaceIDInputInValidError=Carateres inválidos. Utilize apenas A - Z, 0 - 9, e _.
#YMSE: Error when space id exists while creation
spaceIDInputDuplicateError=O ID de espaço já existe.
#XFLD: Space searchfield placeholder
search=Procurar
#XMSG: Success message after creating users
createUsersSuccess=Utilizadores adicionados
#XMSG: Success message after creating user
createUserSuccess=Utilizador adicionado
#XMSG: Success message after updating users
updateUsersSuccess={0} utilizadores atualizados
#XMSG: Success message after updating user
updateUserSuccess=Utilizador atualizado
#XMSG: Success message after removing users
removeUsersSuccess={0} utilizadores removidos
#XMSG: Success message after removing user
removeUserSuccess=Utilizador removido
#XFLD: Schema name
schemaName=Nome do esquema
#XFLD: used of total
ofTemplate={0} de {1}
#XFLD: Assigned disk label in space overview
assignedStorageTemplate=Disco atribuído ({0} de {1})
#XFLD: Assigned Memory label in space overview
assignedRAMTemplate=Memória atribuída ({0} de {1})
#XFLD: Storage ratio on space
accelearationRAM=Aceleração de memória
#XFLD: No Storage Consumption
noStorageConsumptionText=Nenhuma quota de armazenamento atribuída.
#XFLD: Used disk label in space overview
usedStorageTemplate=Disco utilizado para armazenamento ({0} de {1})
#XFLD: Used Memory label in space overview
usedRAMTemplate=Memória utilizada para armazenamento ({0} de {1})
#XFLD: Used Disk tooltip
usedStorageTooltipTemplate={0} de {1} de disco utilizado(s) para armazenamento
#XFLD: Used Memory tooltip
usedRAMTooltipTemplate={0} de {1} de memória utilizado(s)
#XFLD: Assigned Disk tooltip
assignedStorageTooltipTemplate={0} de {1} disco atribuído
#XFLD: Assigned Memory tooltip
assignedRAMTooltipTemplate={0} de {1} memória atribuída
#XFLD: Space related used storage tooltip
spaceRelatedUsedStorageTooltipTemplate=Dados de espaço: {0}
#XFLD: Not space related used storage tooltip
otherDataUsedStorageTooltipTemplate=Outros dados: {0}
#XFLD: Storage used exceeded popover and tooltip
storageUsedExceededText=Considere ampliar o seu plano ou contacte o suporte SAP.
#XCOL: Space table-view column used Disk
usedStorage=Disco utilizado para armazenamento
#XCOL: Space monitor column used Memory
usedRAM=Memória utilizada para armazenamento
#XCOL: Space monitor column Schema
tableSchema=Esquema
#XCOL: Space monitor column Storage Type
tableStorageType=Tipo de armazenamento
#XCOL: Space monitor column Table Type
tableType=Tipo de tabela
#XCOL: Space monitor column Record Count
tableRecordCount=Contagem de registos
#XFLD: Assigned Disk
assignedStorage=Disco atribuído para armazenamento
#XFLD: Assigned Memory
assignedRAM=Memória atribuída para armazenamento
#XCOL: Space table-view column storage utilization
tableStorageUtilization=Armazenamento utilizado
#XFLD: space status
spaceStatus=Estado do espaço
#XFLD: space type
spaceType=Tipo de espaço
#XFLD: space type label for space with space type "SAP BW bridge" ("SAP BW bridge" is a product name, do not translate it)
abapbridgeSpaceType=SAP BW bridge
#XFLD: space type label for space with space type "Data Marketplace"
marketplaceSpaceType=Produto do fornecedor de dados
#XMSG: Error message for when selected space cannot be deleted due to the space type
deleteTypedSpaceError=Não pode eliminar o espaço {0} pois o tipo de espaço é {1}.
#XMSG: Error message for when selected spaces cannot be deleted due to the space type
deleteTypedSpacesError=Não pode eliminar os {0} espaços selecionados. Os espaços com os seguintes tipos de espaço não podem ser eliminados: {1}.
#XFLD: space type label for space without space type
defaultSpaceType=SAP Datasphere
#XFLD: Tooltip for monitor space button
monitorSpace=Monitorizar
#XFLD: Tooltip for edit space button
editSpace=Editar espaço
#XMSG: Deletion warning in messagebox
deleteConfirmation=Tem a certeza de que pretende eliminar este espaço?
#XFLD: Tooltip for delete space button
deleteSpace=Eliminar espaço
#XFLD: storage
storage=Disco para armazenamento
#XFLD: username
userName=Nome do utilizador
#XFLD: port
port=Porta
#XFLD: hostname
hostName=Nome do anfitrião
#XFLD: password
password=Palavra-passe
#XBUT: Request new password button
requestPassword=Pedir palavra-passe nova
#YEXP: Usage explanation in time data section
timeDataSectionHint=Crie tabelas de tempos e dimensões para utilizar nos seus modelos e histórias.
#YEXP: Usage explanation in Space schema section
spaceSchemaHint=Pretende que os dados no seu espaço possam ser consumidos por outras ferramentas ou aplicações? Se pretender, crie um ou vários utilizadores que podem aceder aos dados no seu espaço e selecione se pretende que todos os dados de espaço futuros sejam consumíveis por predefinição.
#XTIT: Create schema popup title
createSchemaDialogTitle=Criar esquema Open SQL
#XTIT: Generate Time Data dialog title
generateTimeDataDialogTitle=Criar tabelas de tempos e dimensões
#XTIT: Edit Time Data dialog title
editTimeDataDialogTitle=Editar tabelas de tempos e dimensões
#XTIT: Time Data token title
timeDataTokenTitle=Dados de tempo
#XTIT: Time Data token title
timeDataUpdateViews=Atualizar vistas de dados de tempo
#XFLD: Time Data token creation in progress label
timeDataCreationInProgress=Criação em curso...
#XFLD: Time Data token creation error label
timeDataCreationError=Criação falhada. Tente novamente.
#XTIT: Time Data dialog time settings title
timeDataDialogTimeSettingsTitle=Definições de tabela de tempos
#XTIT: Time Data dialog time data table title
timeDataDialogTranslationTablesTitle=Tabelas de conversão
#XTIT: Time Data dialog dimensions title
timeDataDialogTimeViewsTitle=Dimensões de tempo
#XFLD: Time Data dialog time range label
timeRangeHint=Definir o intervalo de tempo.
#XFLD: Time Data dialog time data table label
timeDataHint=Dê um nome à sua tabela.
#XFLD: Time Data dialog time data dimensions label
timeDimensionsHint=Dê um nome às suas dimensões.
#XFLD: Time Data Time range description label
timerangeLabel=Intervalo de tempo
#XFLD: Time Data dialog from year label
fromYearLabel=Do ano
#XFLD: Time Data dialog to year label
toYearLabel=Até ao ano
#XFLD: Time Data dialog calendar type label
calendarTypeLabel=Tipo de calendário
#XFLD: Time Data dialog granularity label
granularityLabel=Granularidade
#XFLD: Time Data dialog technical name label
technicalNameLabel=Nome técnico
#XFLD: Time Data dialog quarters text table lable
quarterTextsTableLabel=Tabela de conversão para trimestres
#XFLD: Time Data dialog months text table lable
monthsTextsTableLabel=Tabela de conversão para meses
#XFLD: Time Data dialog days text table lable
daysTextsTableLabel=Tabela de conversão para dias
#XFLD: Time Data dialog year label
yearLabel=Dimensão do ano
#XFLD: Time Data dialog quarter label
quarterLabel=Dimensão do trimestre
#XFLD: Time Data dialog month label
monthLabel=Dimensão do mês
#XFLD: Time Data dialog day label
dayLabel=Dimensão do dia
#XFLD: Time Data dialog gregorian calendar type label
gregorian=Gregoriano
#XFLD: Time Data dialog time granularity day label
day=Dia
#XFLD: Time Data dialog business name maxlength reached
timeRangeValueStateTextMaxLengthBusinessName=Comprimento máximo de 1000 carateres atingindo.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextMaxRangeExceeded=O intervalo de tempo máximo é de 150 anos.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeLower="Do ano" deve ser anterior do que "Até o ano"
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeHigher="Do ano" deve ser 1900 ou posterior.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeHigher="Até o ano" deve ser posterior a "Do ano"
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeLower="Até o ano" deve ser anterior ao ano atual mais 100
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningFromYear=O aumento de "Do ano" pode levar à perda de dados
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningToYear=A diminuição de "Até o ano" pode levar à perda de dados
#XMSG: Time Data creation validation error message
timeDataValidationError=Parece que alguns campos são inválidos. Verifique os campos obrigatórios para continuar.
#XMSG: Time Data deletion popup text
deleteTimeDataConfirmation=Tem a certeza de que pretende eliminar os dados?
#XMSG: Time Data creation success message
createTimeDataSuccess=Dados de tempo criados
#XMSG: Time Data update success message
updateTimeDataSuccess=Dados de tempo atualizados
#XMSG: Time Data delete success message
deleteTimeDataSuccess=Dados de tempo eliminados
#XMSG: Time Data creation error message
createTimeDataError=Ocorreu algum problema ao tentar criar dados de tempo.
#XMSG: Time Data update error message
updateTimeDataError=Ocorreu algum problema ao tentar atualizar dados de tempo.
#XMSG: Time Data creation error message
deleteTimeDataError=Ocorreu algum problema ao tentar eliminar dados de tempo.
#XMSG: Error message when time data could not be retrieved
retrievingTimeDataError=Não foi possível carregar dados de tempo.
#XTIT: Time Data deletion popup title
deleteTimeDataConfirmationTitle=Aviso
#XTXT: Time Data deletion dependency popup text
deleteTimeDataDependencyText=Não conseguimos eliminar os dados de tempo porque são utilizados noutros modelos.
#XTXT: Time Data deletion dependency popup text singular
deleteTimeDataDependencyTextSingular=Não conseguimos eliminar os dados de tempo porque são utilizados noutro modelo.
#XMSG: Validation error when required time data form field is empty
timeDataRequiredField=Este campo é obrigatório e não pode ficar vazio.
#XFLD: Tooltip for Open in Database Explorer button
openInDBX=Abrir no explorador de base de dados
#YMSE: Dimension Year
dimensionYearView=Dimensão "Ano"
#YMSE: Dimension Year
dimensionQuarterView=Dimensão "Trimestre"
#YMSE: Dimension Year
dimensionMonthView=Dimensão "Mês"
#YMSE: Dimension Year
dimensionDayView=Dimensão "Dia"
#XFLD: Time Data deletion object title
timeDataUsedIn=(utilizado em {0} modelos)
#XFLD: Time Data deletion object title singular
timeDataUsedInSingular=(utilizado em 1 modelo)
#XFLD: Time Data deletion table column provider
provider=Fornecedor
#XTIT: Time Data dependency dialog title
timeDataDependencyDialogTitle=Dependências
#XTIT: Create schema popup title
createSpaceSchemaUserDialogTitle=Criar utilizador para esquema de espaço
#XFLD: Create schema button
createSchemaButton=Criar esquema Open SQL
#XFLD: Generate TimeData button
generateTimeDataButton=Criar tabelas de tempos e dimensões
#XFLD: Show dependencies button
showDependenciesButton=Mostrar dependências
#XFLD: tooltip for disabled buttons when user is not member of space
mustBeMemberOfSpaceForOperation=Para executar esta operação, o utilizador tem de ser um membro do espaço.
#XFLD: Create space schema user button
createSpaceSchemaUserButton=Criar utilizador de esquema de espaço
#YMSE: API Schema users load error
loadSchemaUsersError=Não foi possível carregar a lista de utilizadores.
#XTIT: API Schema-config popup title
schemaUserDetailsDialogTitle=Detalhes de utilizador do esquema de espaço
#XMSG: API Schema user deletion popup text
deleteSpaceSchemaUserConfirmation=Tem a certeza de que pretende eliminar o utilizador selecionado?
#XMSG: API Schema user deletion confirmation text
userDeleteSuccess=Utilizador eliminado.
#YMSE: API Schema user deletion error
userDeleteError=Não foi possível eliminar o utilizador.
#XFLD: User deleted
userDeleted=O utilizador foi eliminado.
#XTIT: Remove user popup title
removeUserConfirmationTitle=Aviso
#XMSG: Remove user popup text
removeUserConfirmation=Pretende realmente remover o utilizador? O utilizador e as funções incluídas no âmbito atribuídas serão removidos do espaço.
#XMSG: Remove users popup text
removeUsersConfirmation=Pretende realmente remover os utilizadores? Os utilizadores e as funções incluídas no âmbito atribuídas serão removidos do espaço.
#XBUT: Confirmation button for remove user dialog
removeUserConfirmationBtn=Remover
#YMSE: No data text for available roles
noDataAvailableRoles=O espaço não está adicionado a nenhuma função incluída no âmbito. \n Para poder adicionar utilizadores ao espaço, deve ser adicionado primeiro a uma ou mais funções incluídas no âmbito.
#YMSE: No data text for selected roles
noDataSelectedRoles=Nenhuma função incluída no âmbito selecionada
#XTIT: Schema-config popup title
schemaDetailsDialogTitle=Abrir detalhes de configuração de esquema SQL
#XFLD: Label for Read Audit Log
auditLogRead=Ativar registo de auditoria para operações de leitura
#XFLD: Label for Change Audit Log
auditLogChange=Ativar registo de auditoria para operações de alteração
#XFLD: Label Audit Log Retention
auditLogRetention=Manter registos durante
#XFLD: Label Audit Log Retention Unit
retentionUnit=Dias
#YMSE: Error when log retention time is set incorrectly
incorrectRetentionTime=Introduzir um número inteiro entre {0} e {1}
#XFLD: Label for Cross Space Schema Access
spaceSchemaAccess=Consumir dados de esquema de espaço
#XTIT: Confirm deactivation of space schema consumption dialog title
disableSpaceSchemaConsumptionDialogTitle=Deixar de consumir dados de esquema de espaço
#XTXT: Confirm deactivation of space schema consumption dialog text
disableSpaceSchemaConsumptionDialogText=Este esquema SQL aberto pode consumir dados do seu esquema de espaço. Se você deixar de consumir, os modelos baseados nos dados de esquema de espaço podem já não funcionar.
#XBUT: Confirmation button for the stop consuming space schema data dialog
stopConsuming=Deixar de consumir
#XTXT: Tooltip for data lake icon
dataLakeIconTooltip=Este espaço é utilizado para aceder ao Data Lake
#XFLD: Tooltip for data lake table entry
dataLakeTableEntryTooltip=Data Lake ativado
#XFLD: used In-Memory linit reached tag
usedRamLimitReachedLabel=Limite de memória interna atingido
#XFLD: Space storage Limit reached tag
storageLimitReachedLabel=Limite de armazenamento atingido
#XFLD: Minimal Storage Limit Reached tag
minStorageLimitReachedLabel=Limite de armazenamento mínimo atingido
#XFLD: Space ram tag
ramLimitReachedLabel=Limite de memória atingido
#XFLD: Minimal In-Memory Limit Reached tag
minRamLimitReachedLabel=Limite mínimo de memória atingido
#XFLD: Popover text after click on space storage limit warning
storageLimitReachedText=Atingiu o limite de armazenamento de espaço atribuído de {0}. Atribua mais armazenamento ao espaço.
#XFLD: System storage tag
systemStorageLimitReachedLabel=Limite de armazenamento de sistema atingido
#XMSG: Popover text after click on storage limit warning
systemStorageLimitReachedText=Atingiu o limite de armazenamento de sistema de {0}. Não pode atribuir mais armazenamento ao espaço agora.
#XMSG: Schema deletion warning
deleteSchemaConfirmation=A eliminação deste esquema SQL aberto eliminará também de forma permanente todos os objetos armazenados e associações mantidas no esquema. Continuar?
#XMSG: Success message after deleting a schema
schemaDeleteSuccess=Esquema eliminado
#YMSE: Error while deleting schema.
schemaDeleteError=Não foi possível eliminar o esquema.
#XMSG: Success message after update a schema
schemaUpdateSuccess=Esquema atualizado
#YMSE: Error while updating schema.
schemaUpdateError=Não foi possível atualizar o esquema.
#XMSG: Hint when no password is shown in the dialog.
newPasswordHint=Fornecemos uma palavra-passe para este esquema. Se tiver esquecido ou perdido a sua palavra-passe, pode pedir uma nova. Lembre-se de copiar ou guardar a nova palavra-passe.
#XMSG: Hint when showing the password in the dialog.
savePasswordHint=Copie a palavra-passe. Precisará dela para configurar uma ligação a este esquema. Se tiver esquecido a palavra-passe, pode abrir este diálogo para repô-la.
#XFLD,50: Hint on entering schema name in creation dialog
schemaNameHint=Impossível alterar este nome após criação esquema.
#XFLD: Open SQL Schemas section sub headline
schemasSQL=Open SQL
#XFLD: Space schema section sub headline
schemasSpace=Espaço
#XFLD: HDI Container section header
HDIContainers=Contentores HDI
#XTXT: Add HDI Containers button tooltip
addHDIContainers=Adicionar contentores HDI
#XTXT: Remove HDI Containers button tooltip
removeHDIContainers=Remover contentores HDI
#XFLD: Enable HDI Containers feature button
enableHDIContainersButton=Ativar acesso
#YMSE: No data text for HDI Containers table
noDataHDIContainers=Não foram adicionados contentores HDI.
#YMSE: No data text for Timedata section
noDataTimedata=Não foram criadas tabelas de tempos nem dimensões.
#YMSE: No data text during runtime not available for Timedata section
runtimeNotAvailableNoDataTimedata=Impossível carregar tabelas e dimensões de tempo, uma vez que a base de dados de tempo de execução não está disponível.
#YMSE: No data text during runtime not available for HDI section
runtimeNotAvailableNoDataHDI=Impossível carregar contentores HDI, uma vez que a base de dados de tempo de execução não está disponível.
#YMSE: Error while fetching HDI Containers
fetchHDIContainersError=Não foi possível obter os contentores HDI. Tente novamente mais tarde.
#XFLD Table column header for HDI Container names
HDIContainerName=Nome do contentor HDI
#XFLD: Enable HDI Containers feature dialog title
enableHDIContainersDialogTitle=Ativar acesso
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText1New=Pode ativar o SAP SQL Data Warehousing no seu inquilino do SAP Datasphere para trocar dados entre os contentores HDI e os espaços do SAP Datasphere, sem necessidade de movimentar dados.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText2=Para fazer isso, abra um pedido de suporte, clicando no botão abaixo.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText3New=Assim que o seu pedido tiver sido processado, tem de criar um ou mais contentores HDI novos na base de dados de tempo de execução do SAP Datasphere. Em seguida, o botão Ativar acesso é substituído pelo botão + na secção Contentores HDI para todos os seus espaços do SAP Datasphere, e pode adicionar os seus contentores a um espaço.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText41=Precisa de mais informações? Vá para %%0. Para informações detalhadas sobre o que deve ser incluído no pedido, consulte %%1.
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkText=SAP Help
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkTextSapNote3057059=Nota SAP 3057059
#XBUT: Open Ticket Button Text
openTicket=Abrir pedido
#XBUT: Add Button Text
add=Adicionar
#XBUT: Next Button Text
next=Seguinte
#XBUT: Edit Button Text
editUsers=Editar
#XBUT: create user Button Text
createUser=Criar
#XBUT: Update user Button Text
updateUser=Selecionar
#XTIT: Add HDI Containers Dialog Title
addHDIContainersDialogTitle=Adicionar contentores HDI não atribuídos
#XFLD: No data text for add HDI container dialog
addHDIContainersDialogNoDataText=Não conseguimos encontrar contentores não atribuídos. \N O contentor que procura pode estar já atribuído a um espaço.
#YMSE: Error while loading assigned HDI Containers
loadAssignedHDIContainersError=Não foi possível carregar contentores HDI atribuídos.
#YMSE: Error while loading assigned HDI Containers
loadUnassignedHDIContainersError=Não foi possível carregar contentores HDI.
#XMSG: Success message
succeededToAddHDIContainer=Contentor HDI adicionado
#XMSG: Success message
succeededToAddHDIContainerPlural=Contentores HDI adicionados
#XMSG: Success message
succeededToDeleteHDIContainer=Contentor HDI removido
#XMSG: Success message
succeededToDeleteHDIContainerPlural=Contentores HDI removidos
#XFLD: Time data section sub headline
timeDataSection=Tabelas de tempos e dimensões
#XFLD: Schemas list entry read audit log enabled indicator
readIndicator=Ler
#XFLD: Schemas list entry change audit log enabled indicator
changeIndicator=Alterar
#XFLD: Remote sources section sub headline
allconnections=Atribuição de ligação
#XFLD: Remote sources section sub headline
localconnections=Ligações locais
#XFLD: Remote sources section sub headline for open Connectors
openConnectors=SAP Open Connectors
#XFLD: User section sub headline
memberassignment=Atribuição de membro
#XFLD: User assignment section sub headline
userAssignment=Atribuição de utilizador
#XFLD: User section Access dropdown Member
member=Membro
#XFLD: User assignment section column name
user=Nome do utilizador
#XTXT: Selected role count
selectedRoleToolbarText=Selecionado: {0}
#XTIT: Space detail section connections title
detailsSectionConnections=Ligações
#XTIT: Space detail section data access title
detailsSectionDataAccess=Acesso a esquema
#XTIT: Space detail section time data title
detailsSectionGenerateData=Dados de tempo
#XTIT: Space detail section members title
detailsSectionUsers=Membros
#XTIT: Space detail section Users title
detailsSectionUsersTitle=Utilizadores
#XTIT: Out of Storage
insufficientStoragePopoverTitle=Sem armazenamento
#XTIT: Storage distribution
storageDistributionPopoverTitle=Armazenamento de disco utilizado
#XTXT: Out of Storage popover text
insufficientStorageText=Para criar um novo espaço, reduza o armazenamento atribuído de outro espaço ou elimine um espaço de que não precisa mais. Pode aumentar o armazenamento total do sistema ao chamar Gerir plano.
#XMSG: Space id length warning
spaceIdLengthWarning=Máximo de {0} carateres excedido.
#XMSG: Space name length warning
spaceNameLengthWarning=Máximo de {0} carateres excedido.
#XMSG: Space id reserved prefix warning
spaceIdnWarningReservedPrefix=Não utilize o prefixo {0} para evitar possíveis conflitos.
#YMSE: Error while loading open SQL schemas
loadLocalSchemasError=Não foi possível carregar esquemas Open SQL.
#YMSE: Error while creating open SQL schema
createLocalSchemaError=Não foi possível criar esquema Open SQL.
#YMSE: Error while loading all remote connections
loadRemoteConnectionsError=Não foi possível carregar todas as ligações remotas.
#YMSE: Error while loading space details
loadSpaceDetailsError=Não foi possível carregar os detalhes do espaço.
#YMSE: Error while deploying space details
deploySpaceDetailsError=Não foi possível implementar o espaço.
#YMSE: Error while copying space details
copySpaceDetailsError=Não foi possível copiar o espaço.
#YMSE: Error while loading storage data
loadStorageDataError=Não foi possível carregar os dados de armazenamento.
#YMSE: Error while loading all users
loadAllUsersError=Não foi possível carregar todos os utilizadores.
#YMSE: Failed to reset password
resetPasswordError=Não foi possível repor a palavra-passe.
#XMSG: Success message after updating space
resetPasswordSuccessMessage=Nova palavra-passe definida para esquema.
#YMSE: DP Agent-name too long
DBAgentNameError=O nome do agente de aprovisionamento de dados é demasiado longo.
#YMSE: Schema-name not valid.
schemaNameError=O nome do esquema é inválido.
#YMSE: User name not valid.
UserNameError=O nome do utilizador é inválido.
#XTIT: Consumption by Storage Type
consumptionStorageTypeText=Consumo por tipo de armazenamento
#XTIT: Consumption by Schema
consumptionSchemaText=Consumo por esquema
#XTIT: Overall Consumption by schema
overallConsumptionSchemaText=Consumo total de tabela por esquema
#XTIT: Overall Consumption by Table Type
overallConsumptionTableTypeText=Consumo total por tipo de tabela
#XTIT: Tables
tableDetailsText=Detalhes da tabela
#XTIT: Table Storage Consumption
tableStorageConsumptionText=Consumo de armazenamento de tabela
#XFLD: Table Type label
tableTypeLabel=Tipo de tabela
#XFLD: Schema label
schemaLabel=Esquema
#XFLD: reset table tooltip
resetTable=Repor tabela
#XFLD: In-Memory label in space monitor
inMemoryLabel=Memória
#XFLD: Disk label in space monitor
diskLabel=Disco
#XFLD: Yes
yesLabel=Sim
#XFLD: No
noLabel=Não
#XFLD: switch label: Want the data in this space to be consumable by default?
allowConsumptionByDefault=Pretende que os dados neste espaço sejam consumíveis por predefinição?
#XFLD: Business Name
businessNameLabel=Nome comercial
#XFLD: Refresh
refresh=Atualizar
#XMSG: No filter results title
noFilterResultsTitle=Parece que as definições de filtro não mostram dados.
#XMSG: No filter results message
noFilterResultsMsg=Tente refinar as definições de filtro. Caso continue a não ver dados, crie algumas tabelas no gerador de dados. Logo que elas consumam armazenamento, poderá monitorizá-las aqui.
#XMSG: Space monitoring Hana state red title
spaceMonitorHanaRedTitle=A base de dados de tempo de execução não está disponível.
#XMSG: Space monitoring Hana state red message
spaceMonitorHanaRedMsg=Como a base de dados de tempo de execução não está disponível, algumas funcionalidades estão desativadas e não podemos apresentar informações nesta página.
#YMSE: Error while creating space schema user
createSpaceSchemaUserError=Não foi possível criar utilizador de esquema de espaço.
#YMSE: Error User name already exists
userAlreadyExistsError=O nome do utilizador já existe.
#YMSE: Error Authentication failed
authenticationFailedError=Autenticação falhada.
#XMSG: Message strip message when a schema user is locked
userLockedMessageStrip=O utilizador está bloqueado devido a demasiados inícios de sessão falhados. Peça uma nova palavra-passe para desbloquear o utilizador.
#XMSG: Success message after reset and unlock schema user
resetUnlockPasswordSuccessMessage=Nova palavra-passe definida e utilizador desbloqueado
#XMSG: user is locked message
userLockedMessage=O utilizador está bloqueado.
#XCOL: Users table-view column Role
spaceRole=Função
#XCOL: Users table-view column Scoped Role
spaceScopedRole=Função incluída no âmbito
#XCOL: Users table-view column Space Admin
spaceAdmin=Administrador de espaço
#XFLD: User section dropdown value Viewer
viewer=Visualizador
#XFLD: User section dropdown value Modeler
modeler=Modelador
#XFLD: User section dropdown value Data Integrator
dataIntegrator=Integrador de dados
#XFLD: User section dropdown value Space Administrator
spaceAdministrator=Administrador de espaço
#XMSG: Success message -  Space Role updated successfully.
updateUserSpaceRoleSuccess=Função de espaço atualizada
#XMSG: Error message - Failed to update Space Role.
updateUserSpaceRoleError=A função de espaço não foi atualizada com êxito.
#XFLD:
databaseUserNameSuffix=Sufixo do nome do utilizador da base de dados
#XTXT: Space Schema password text
spaceSchemaPasswordText=Para configurar uma ligação a este utilizador esquema, copie a sua palavra-passe. Se se tiver esquecido da palavra-passe, pode pedir uma nova.
#XFLD: Cloud Platform label in space schema dialog
cloudPlatformLabel=Cloud Platform
#XTXT: Cloud Platform text in space schema dialog
cloudPlatformText=Para configurar o acesso através deste utilizador, ative o consumo e copie as credenciais. Se só puder copiar as credenciais sem uma palavra-passe, certifique-se de que adiciona a palavra-passe mais tarde.
#XFLD: enable consumption in CloudPlatform label in space schema dialog
enableConsumptionInCloudPlatformLabel=Ativar consumo no Cloud Platform
#XFLD: Credentials for User-Provided Service label in space schema dialog
cloudPlatformCredentialsLabel=Credenciais para serviço fornecido pelo utilizador:
#XFLD: Credentials for User-Provided Service (Without Password) label in space schema dialog
cloudPlatformCredentialsWithoutPasswordLabel=Credenciais para serviço fornecido pelo utilizador (sem palavra-passe):
#XFLD: Copy Credentials label in space schema dialog
copyCredentialsLabel=Copiar credenciais sem palavra-passe
#XFLD: Copy Credentials label in space schema dialog
copyFullCredentialsLabel=Copiar credenciais completas
#XFLD: Copy Password label in space schema dialog
copyPasswordLabel=Copiar palavra-passe
#XMSG: Credentials copied to clipboard
credentialsCopiedMessage=Credenciais copiadas para área de transferência
#XMSG: Password copied to clipboard
passwordCopiedMessage=Palavra-passe copiada para área de transferência
#XMSG: create database user dialog title
createDatabaseUserDialogTitle=Criar utilizador da base de dados
#XMSG: Database Users section title
databaseUsers=Utilizadores da base de dados
#XMSG: Database User Details dialog title
databaseUserDetailsDialogTitle=Detalhes do utilizador da base de dados
#XFLD: database user read audit log
databaseUserAuditLogRead=Ativar registos de auditoria para operações de leitura e manter registos durante
#XFLD: database user change audit log
databaseUserAuditLogChange=Ativar registos de auditoria para operações de alteração e manter registos durante
#XMSG: Cloud Platform Access
cloudPlatformAccess=Acesso a Cloud Platform
#XFLD: Enable Consumption in Cloud Platform hint
enableConsumptionCloudPlatformHint=Configure o acesso ao seu contentor SAP HANA Deployment Infrastructure (HDI) através deste utilizador da base de dados. Para ligar ao sei contentor HDI, a modelação SQL deve estar ativada.
#XFLD: Enable HDI Consumption
enableConsumptionCloudPlatform=Ativar consumo HDI
#XFLD: Enable Consumption hint
enableConsumptionHint=Pretende que os dados no seu espaço possam ser consumidos por outras ferramentas ou aplicações?
#XFLD: Enable Consumption
enableConsumption=Ativar consumo SQL
#XFLD: Enable Modeling
enableModeling=Ativar modelação SQL
#XMSG: Privileges for Data Modeling
privilegesModeling=Ingestão de dados
#XMSG: Privileges for Data Consumption
privilegesConsumption=Consumo de dados para ferramentas externas
#XFLD: SQL Modeling
sqlModeling=Modelação SQL
#XFLD: SQL Consumption
sqlConsumption=Consumo SQL
#XFLD: enabled
enabled=Ativado
#XFLD: disabled
disabled=Desativado
#XFLD: Edit Privileges
editPrivileges=Editar privilégios
#XFLD: Open Database Explorer
openDBX=Abrir explorador de base de dados
#XFLD: create database user hint
databaseCreateHint=Considere que não será possível alterar o nome de utilizador novamente depois de guardar.
#XFLD: Internal Schema Name
internalSchemaName=Nome de esquema interno
#YMSE: Failed to load database users
loadDatabaseUserError=Falha ao carregar utilizadores da base de dados
#YMSE: Failed to delete database user
deleteDatabaseUsersError=Falha ao eliminar utilizadores da base de dados
#XMSG: Success message after deleting database user
deleteDatabaseUserSuccess=Utilizador da base de dados eliminado
#XMSG: Success message after deleting database users
deleteDatabaseUsersSuccess=Utilizadores da base de dados eliminados
#XMSG: Success message after creating database user
createDatabaseUserSuccessMessage=Utilizador da base de dados criado
#YMSE: Failed to create database user
createDatabaseUserError=Falha ao criar utilizador da base de dados
#XMSG: Success message after updating database user
updateDatabaseUserSuccessMessage=Utilizador da base de dados atualizado
#YMSE: Failed to update database user
updateDatabaseUserError=Falha ao atualizar utilizador da base de dados
#XFLD: HDI Consumption
hdiConsumption=Consumo HDI
#XTIT: Space detail section Database Access title
detailsSectionDatabaseAccess=Acesso à base de dados
#XTXT: Database users consumption hint
databaseUsersConsumptionHint=Torne os seus dados de espaço consumíveis por predefinição. Os modelos nos geradores permitirão automaticamente que os dados sejam consumíveis.
#XFLD: Default Consumption of Space Data:
defaultConsumptionOfSpaceData=Consumo predefinido de dados de espaço:
#XFLD: Database User Name
databaseUserName=Nome do utilizador da base de dados
#XMSG: Database User creation validation error message
databaseUserValidationError=Parece que alguns campos são inválidos. Verifique os campos obrigatórios para continuar.
#XMSG: SQL modeling can’t be enabled since this user has been migrated.
migratedDatabaseUserHint=A ingestão de dados não pode ser ativada uma vez que este utilizador foi migrado.
#XBUT: Remove Button Text
remove=Remover
#XBUT: Remove Spaces Button Text
removeSpaces=Remover espaços
#XBUT: Remove Objects Button Text
removeObjects=Remover objetos
#XMSG: No members have been added yet.
noMembersAssigned=Ainda não foram adicionados membros.
#XMSG: No users have been added yet.
noUsersAssigned=Ainda não foram adicionados utilizadores.
#XMSG: No database users have been created or your filter is not showing any data.
noDatabaseUsers=Não foram criados utilizadores da base de dados ou o seu filtro não está a mostrar dados.
#XMSG: Please enter a user name.
noDatabaseUsername=Introduza um nome de utilizador.
#XMSG: The user name is too long. Please use a shorter one.
databaseUsernameTooLong=O nome de utilizador é demasiado longo. utilize um mais curto.
#XMSG: No privileges have been enabled and this database user will have limited functionality. Do you still want to continue?
noPrivilegesConfirmationMessage=Não foram ativados privilégios e este utilizador da base de dados terá funcionalidades limitadas. Ainda pretende continuar?
#XMSG: To enable audit logs for change operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogChangeMessage=Para ativar registos de auditoria para operações de alteração, é necessário ativar também a ingestão de dados. Pretende efetuar isso?
#XMSG: To enable audit logs for read operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogReadMessage=Para ativar registos de auditoria para operações de leitura, é necessário ativar também a ingestão de dados. Pretende efetuar isso?
#XMSG: To enable HDI consumption data ingestion and data consumption needs to be enabled. Do you want to enable all?
enableHDIConsumptionMessage=Para ativar o consumo HDI, é necessário ativar também a ingestão de dados e o consumo de dados. Pretende efetuar isso?
#XMSG:
databaseUserPasswordText=Para configurar uma ligação a este utilizador da base de dados, copie a sua palavra-passe. Se se tiver esquecido a palavra-passe, pode pedir uma nova.
#XTIT: Space detail section members title
detailsSectionMembers=Membros
#XMSG: New password set
newPasswordSet=Nova palavra-passe definida
#XFLD: Data Ingestion
dataIngestion=Ingestão de dados
#XFLD: Data Consumption
dataConsumption=Consumo de dados
#XFLD: Privileges
privileges=Privilégios
#XFLD: Enable Data ingestion
enableDataIngestion=Ativar ingestão de dados
#XMSG: Log the read and change operations for data ingestion.
databaseUserAuditHint=Registe as operações de leitura e de alteração para ingestão de dados.
#XMSG: Make your space data available in your HDI containers.
databaseUserHDIHint=Disponibilize os seus dados de espaço nos seus contentores HDI.
#XFLD: Enable Data consumption
enableDataConsumption=Ativar consumo de dados
#XMSG: Allow other apps or tools to consume your space data.
enableDataConsumptionHint=Permita que outras aplicações ou ferramentas consuma os seus dados de espaço.
#XMSG: To set up access via this schema user copy the credentials to your user-provided service. In case you can only copy the credentials without a password make sure to add the password later.
databaseUserHDIConsumptionHint=Para configurar o acesso através deste utilizador de base de dados, copie as credenciais para o seu serviço fornecido pelo utilizador. Se só puder copiar as credenciais sem uma palavra-passe, certifique-se de que adiciona a palavra-passe mais tarde.
#XFLD: Data Flow Runtime Capacity label in space overview
dataFlowRuntimeCapacity=Capacidade de tempo de execução de fluxo de dados ({0}:{1} horas de {2} horas)
#XFLD: Could not load Data Flow Runtime Capacity
dataFlowRuntimeCapacityError=Não foi possível carregar a capacidade de tempo de execução de fluxo de dados
#XMSG: User can grant data consumption to other users.
databaseUserConsumptionWithGrantOptionHint=O utilizador não pode conceder o consumo de dados a outros utilizadores.
#XFLD: Enable data consumption with grant option
enableConsumptionWithGrantOption=Ativar consumo de dados com opção de concessão
#XMSG: To enable data consumption with grant option data consumption needs to be enabled. Do you want to enable both?
enableConsumptionWithGrantMessage=Para ativar o consumo de dados com opção de concessão, é necessário ativar o consumo de dados. Deseja ativar ambos?
#XFLD: Enable Automated Predictive Library (APL) and Predictive Analysis Library (PAL)
allowUsingScriptServerFunctionality=Ativar Automated Predictive Library (APL) e Predictive Analysis Library (PAL)
#XMSG: User can use SAP HANA Cloud embedded machine learning functions.
allowUsingScriptServerFunctionalityHint=O utilizador pode utilizar funções de machine learning incorporadas do SAP HANA Cloud.
#XFLD: Password Policy
passwordPolicy=Política de palavra-passe
#XMSG: Password Policy
passwordPolicyHint=Ative ou desative a política de palavra-passe configurada aqui.
#XFLD: Enable Password Policy
enablePasswordPolicy=Ativar política de palavra-passe
#XMSG: Read Access to the Space Schema
readAccessTitle=Acesso de leitura ao esquema de espaço
#XMSG: read access hint
readAccessHint=Permita ao utilizador da base de dados ligar ferramentas externas ao esquema de espaço e ler vistas que estão expostas para consumo.
#XFLD: Space Schema
spaceSchema=Esquema de espaço
#XFLD: Enable Read Access (SQL)
enableReadAccess=Ativar acesso de leitura (SQL)
#XMSG: Allow the user to grant read access to other users.
withGrantOptionHint=Permita que o utilizador conceda acesso de leitura a outros utilizadores.
#XFLD: With Grant Option
withGrantOption=Com opção de concessão
#XMSG: Make your space data available in your HDI containers.
enableHDIConsumptionHint=Disponibilize os seus dados de espaço nos seus contentores HDI.
#XFLD: Enable HDI Consumption
enableHDIConsumption=Ativar consumo HDI
#XMSG: Write Access to the User’s Open SQL Schema
writeAccessTitle=Acesso de escrita ao esquema Open SQL do utilizador
#XMSG: write access hint
writeAccessHint=Permita ao utilizador da base de dados ligar ferramentas externas ao esquema Open SQL do utilizador para criar entidades de dados e ingerir dados para utilização no espaço.
#XFLD: Open SQL Schema
openSQLSchema=Esquema Open SQL
#XFLD: Enable Write Access (SQL DDL & DML)
enableWriteAccess=Ativar acesso de escrita (SQL, DDL, & DML)
#XMSG: audit hint
auditHint=Registe as operações de leitura e de alteração no esquema Open SQL
#XMSG: data consumption hint
dataConsumptionHint=Por predefinição, exponha todas as novas vistas no espaço para consumo. Os modeladores podem ignorar esta definição para vistas individuais, através do comutador “Expor para consumo” no painel lateral de saída da vista. Também pode escolher os formatos em que as vistas são expostas.
#XFLD: Expose for Consumption by Default
exposeDataConsumption=Expor para consumo por predefinição
#XMSG: database users hint consumption hint
databaseUsersHint2New=Crie utilizadores da base de dados para ligar ferramentas externas ao SAP Datasphere. Defina privilégios para permitir que os utilizadores leiam os dados de espaço e criem entidades de dados (DDL) e ingiram dados (DML) para utilização no espaço.
#XFLD: Read
read=Ler
#XFLD: Read (HDI)
readHDI=Ler (HDI)
#XFLD: Write
write=Escrever
#XMSG: HDI Containers Hint
HDIContainersHint2=Ative o acesso aos contentores de SAP HANA Deployment Infrastructure (HDI) no seu espaço. Os modeladores podem utilizar artefactos como origens para vistas e os clientes HDI podem aceder aos seus dados de espaço.
#XMSG: Open info dialog
openDatabaseUserInfoDialog=Abrir o diálogo Informações
#XMSG: Database user is locked. Open info dialog to unlock
openLockedDatabaseUserInfoDialog=O utilizador da base de dados está bloqueado. Abrir o diálogo para desbloquear
#XFLD: Table
table=Tabela
#XFLD: Space section partner configuration title
partnerConnectionConfigurationSectionTitle=Ligação de parceiro
#XFLD: Space section partner configuration subtitle
partnerConnectionConfigurationSectionSubTitle=Configuração de ligação de parceiro
#XFLD: Space section partner configuration hint
partnerConnectionConfigurationSectionHint=Defina o seu próprio mosaico de ligação de parceiro, adicionando o seu URL e ícone de iFrame. Esta configuração só está disponível para este inquilino.
#XFLD: Table Name Field
partnerConnectionConfigurationName=Nome do mosaico
#XFLD: Table iFrame URL field
partnerConnectionConfigurationiFrameURL=URL de iFrame
#XFLD: Table iFrame Post Message Origin field
partnerConnectionConfigurationiFramePostMessageOrigin=Origem de postMessage de iFrame
#XFLD: Table icon/image name
partnerConnectionConfigurationIcon=Ícone
#XMSG: Table no result found
partnerConnectionConfigurationNotfound=Não foi possível encontrar nenhuma configuração de ligação de parceiro.
#XMSG: Partner Connection Confgurations are not available
partnerConnectionConfigurationNotAvailable=As configurações da ligação do parceiro não podem ser apresentadas quando a base de dados de tempo de execução estiver indisponível.
#XFLD: Dialog Create Partner Connection Configuration
partnerConnectionConfigurationDialogTitleAdd=Criar configuração de ligação de parceiro
#XFLD: Dialog Create Partner Connection Configuration Upload Icon
partnerConnectionConfigurationButtonUloadPartnerIcon=Carregar ícone
#XFLD: Dialog Create Partner Connection Configuration Select
partnerConnectionConfigurationSelectIcon=Selecionar (tamanho máximo de 200KB)
#XFLD: Dialog Create Partner Connection Configuration Enter a name
partnerConnectionConfigurationNamePlaceholder=Exemplo de mosaico de parceiro
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL
partnerConnectionConfigurationiFrameURLPlaceholder=http://www.example.com
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL Check
partnerConnectionConfigurationiFramePostMessageOriginPlaceholder=.example.com
#XFLD: Dialog Create Partner Connection Configuration Browse
partnerConnectionConfigurationBrowseButton=Procurar
#XFLD: Create Partner Connection Configuration Msg
partnerConnectionConfigurationUploadMsg=A configuração da ligação de parceiro foi criada com êxito.
#XMSG: Error while deleting Partner Connection configuration(s)
partnerConnectionConfigurationDeleteError=Ocorreu um erro ao eliminar as configurações da ligação de parceiro.
#XMSG: Partner Connection Configuration has been successfully deleted
partnerConnectionConfigurationDeleteSuccess=A configuração da ligação de parceiro foi eliminada com êxito.
#XMSG: Error while retrieving Partner Connection Configurations
partnerConnectionConfigurationLoadError=Ocorreu um erro ao obter as configurações da ligação de parceiro.
#XMSG: Error fileSizeLimitExceeded
partnerConnectionConfigurationfileSizeLimitExceeded=Não foi possível carregar o ficheiro, porque excede o tamanho máximo de 200KB.
#XBUT: Create Partner Connection Configuration
partnerConnectionConfigurationButtonCreate=Criar configuração de ligação de parceiro
#XBUT: Delete Partner Connection Configuration
partnerConnectionConfigurationButtonDelete=Eliminar configuração de ligação de parceiro
#XMSG: Partner Tile Creation Failure
upsertPartnerTileFailed=Não foi possível criar o mosaico de parceiro.
#XMSG: Partner Tile Deletion Failure
deletePartnerTileFailed=Não foi possível eliminar o mosaico de parceiro.
#XMSG: HANA cloud connector settings reset failure
resetHANASCCSettingsFailed=Falha ao repor definições do SAP HANA Cloud Connector do cliente
#XFLD: Workload Class
workloadClass=Classe de carga de trabalho
#XFLD: Workload Management
workloadManagement=Gestão de cargas de trabalho
#XFLD: Priority
workloadClassPriority=Prioridade
#XMSG:
workloadManagementPriorityHint=Pode especificar a atribuição de prioridades deste espaço ao consultar a base de dados. Introduza um valor de 1 (prioridade mais baixa) a 8 (prioridade mais alta). Numa situação em que os espaços competem por threads disponíveis, os que têm prioridades mais altas são executados antes dos espaços com prioridades mais baixas.
#XMSG:
workloadClassPriorityHint=Pode especificar a prioridade do espaço de 0 (mais baixa) a 8 (mais alta). As instruções de um espaço com uma prioridade alta são executadas antes das instruções de outros espaços com uma prioridade baixa. A prioridade predefinida é 5. Como o valor 9 está reservado para operações do sistema, não está disponível para um espaço.
#XFLD: Statement Limits
workloadclassStatementLimits=Limites de instruções
#XFLD: Workload Configuration
workloadConfiguration=Configuração de carga de trabalho
#XMSG:
workloadClassStatementLimitsHint=Pode especificar o número máximo (ou percentagem) de threads e GBs de memória que as instruções executadas em simultâneo no espaço podem consumir. Pode introduzir qualquer valor ou percentagem entre 0 (sem limite) e o total de memória e de threads disponíveis no inquilino. \n\n Se especificar um limite de threads, considere que isso poderá baixar o desempenho. \n\n Se especificar um limite de memória, as instruções que atingirem o limite de memória não são executadas.
#XMSG:
workloadClassStatementLimitsDescription=A configuração predefinida fornece generosos limites de recursos ao impedir que qualquer espaço individual sobrecarregue o sistema.
#XMSG:
workloadClassStatementLimitCustomDescription=Pode definir limites máximos de threads e de memória total que as instruções em execução simultânea podem consumir no espaço.
#XMSG:
totalStatementThreadLimitHelpText=Definir o limite de threads com um valor demasiado baixo pode afetar o desempenho na instrução, ao passo que valores excessivamente altos ou 0 podem permitir que o espaço consuma todos os threads do sistema disponíveis.
#XMSG:
totalStatementMemoryLimitHelpText=Definir o limite de memória com um valor demasiado baixo pode causar problemas de memória esgotada, ao passo que valores excessivamente altos ou 0 podem permitir que o espaço consuma toda a memória do sistema disponível.
#XMSG:
totalStatementThreadLimitHelpTextRestricted=Introduza uma percentagem entre 1% e 70% (ou o número equivalente) do número total de threads disponíveis no seu inquilino. A definição de um limite de threads demasiado baixo poderá ter impacto no desempenho da instrução, enquanto que valores excessivamente altos poderão ter impacto no desempenho de instruções noutros espaços.
#XMSG:
totalStatementThreadLimitHelpTextDynamic=Introduza uma percentagem entre 1% e {0}% (ou o número equivalente) do número total de threads disponíveis no seu inquilino. A definição de um limite de threads demasiado baixo poderá ter impacto no desempenho da instrução, enquanto que valores excessivamente altos poderão ter impacto no desempenho de instruções noutros espaços.
#XMSG:
totalStatementMemoryLimitHelpTextNew=Introduza um valor ou percentagem entre 0 (sem limite) e o valor total de memória disponível no seu inquilino. A definição de um limite de memória demasiado baixo poderá ter impacto no desempenho da instrução, enquanto valores excessivamente altos poderão ter impacto no desempenho de instruções noutros espaços.
#XFLD: Total Statement Thread Limit
totalStatementThreadLimit=Limite total de threads de instrução
#XFLD: Segmented button label
workloadclassPercentage=%
#XFLD: Segmented button label
workloadclassGigaByte=GB
#XFLD, 80: Segmented button label
workloadclassThreads=Threads
#XFLD: Total Statement Memory Limit
totalStatementMemoryLimit=Limite total de memória de instrução
#YMSE: Error while loading customer hana info
loadCustomerHanaInfoError=Falha ao carregar informações SAP HANA do cliente.
#XMSG:
minimumLimitReached=Limite mínimo atingido.
#XMSG:
maximumLimitReached=Limite máximo atingido.
#XMSG: Name Taken for Technical Name
technical-name-taken=Já existe uma ligação com o nome técnico introduzido. Introduza outro nome.
#XMSG: Name Too long for Technical Name
technical-name-too-long=O nome técnico introduzido excede os 40 caracteres. Introduza um nome com menos caracteres.
#XMSG: Technical name field empty
technical-name-field-empty=Introduza um nome técnico.
#XMSG: Generic error when user enters invalid characters for a field
field-invalid-characters=Só pode utilizar letras (a-z), números (0-9) e carateres de sublinhado (_) para o nome.
#XMSG: Generic error when user enters invalid initial characters for a field
field-underscore-trim=O nome introduzido não pode começar ou terminar com um caráter de sublinhado (_).
#XFLD: Checkbox label for Enable Statement Limits
enableStatementLimits=Ativar limites de instruções
#XFLD: Connection SubSection Settings Title in space details
connectionSettings=Definições
#XMSG: Connections tool hint in Space details section
connectionsToolHint=Para criar ou editar ligações, abra a aplicação Ligações a partir da navegação lateral ou clique aqui:
#XBUT: Connection list Open Connection Tool Button
button-connectiontool=Ir para Ligações
#XFLD: Not deployed label on space tile
notDeployedLabel=O espaço ainda não foi implementado.
#XFLD: Not deployed additional text on space tile
notDeployedText=Implemente o espaço.
#XFLD: Corrupt space label on space tile
corruptSpace=Ocorreu algum problema.
#XFLD: Corrupt space additional text on space tile
corruptSpaceHint=Tente reimplementar ou contacte o suporte
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Dados do registo de auditoria
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Dados administrativos
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Outros dados
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Dados em espaços
#XMSG: Unlock Space confirmation text
unlockSpaceConfirmation=Tem a certeza de que pretende desbloquear o espaço?
#XMSG: Lock Space confirmation text
lockSpaceConfirmation=Tem a certeza de que pretende bloquear o espaço?
#XFLD: Lock
lock=Bloquear
#XFLD: Unlock
unlock=Desbloquear
#XFLD: Locking
locking=A bloquear
#XMSG: Success message after locking space
lockSpaceSuccessMsg=Espaço bloqueado
#XMSG: Success message after unlocking space
unlockSpaceSuccessMsg=Espaço desbloqueado
#XMSG: Success message after locking spaces
lockSpacesSuccessMsg=Espaços bloqueados
#XMSG: Success message after unlocking spaces
unlockSpacesSuccessMsg=Espaços desbloqueados
#YMSE: Error while locking a space
lockSpaceError=O espaço não pode ser bloqueado.
#YMSE: Error while unlocking a space
unlockSpaceError=O espaço não pode ser desbloqueado.
#XTIT: popup title Warning
confirmationWarningTitle=Aviso
#XMSG: Message strip message when a space was locked manually
manualLockedMessage=O espaço foi bloqueado manualmente.
#XMSG: Message strip message when a space was locked due to the amount of generated audit logs
auditLogLockedMessage=O espaço foi bloqueado pelo sistema porque os registos de auditoria consomem uma grande quantidade de GB de disco.
#XMSG: Message strip message when a space was locked because the assigned quota was exceeded.
quotaLockedMessage=O espaço foi bloqueado pelo sistema porque excede as respetivas alocações de memória ou de armazenamento em disco.
#XMSG: Unlock Spaces confirmation text
unlockSpacesConfirmation=Tem a certeza de que pretende desbloquear os espaços selecionados?
#XMSG: Lock Spaces confirmation text
lockSpacesConfirmation=Tem a certeza de que pretende bloquear os espaços selecionados?
#XTIT: Scoped role editor dialog title
scopedRoleEditorDialogTitle=Editor de função incluída no âmbito
#XTIT: ECN Management title
ecnManagementTitle=Gestão de espaços e de nós de computação elásticos
#XFLD: ECNs
ecns=Nós de computação elásticos
#XFLD: ECN phase Ready
ecnReady=Pronto
#XFLD: ECN phase Running
ecnRunning=Em execução
#XFLD: ECN phase Initial
ecnInitial=Não pronto
#XFLD: ECN phase Starting
ecnStarting=A iniciar
#XFLD: ECN phase Starting Failed
ecnStartingFailed=Falha ao iniciar
#XFLD: ECN phase Stopping
ecnStopping=A parar
#XFLD: ECN phase Stopping Failed
ecnStoppingFailed=Falha ao parar
#XBTN: Assign Button
assign=Atribuir espaços
#XBTN: Start Header-Button
start=Iniciar
#XBTN: Update Header-Button
repair=Atualizar
#XBTN: Stop Header-Button
stop=Parar
#XFLD: ECN hours remaining
ecnHoursRemaining=1000 horas restantes
#XFLD: ECN block-hours remaining
ecnBlockHoursRemaining={0} bloco de horas restantes
#XFLD: ECN block-hour remaining
ecnBlockHourRemaining={0} bloco de horas restantes
#XTIT: ECN creation dialog title
ecnCreationDialogTitle=Criar nó de computação elástico
#XTIT: ECN edit dialog title
ecnEditDialogTitle=Editar nó de computação elástico
#XTIT: ECN deletion dialog title
ecnDeletionDialogTitle=Eliminar nó de computação elástico
#XTIT: ECN Space assignment dialog title
ecnSpaceAssignmentDialogTitle=Atribuir espaços
#XFLD: ECN ID
ECNIDLabel=Nó de computação elástico
#XTXT: Selected toolbar text
selectedToolbarText=Selecionado: {0}
#XTIT: Elastic Compute Nodes
ECNslong=Nós de computação elásticos
#XTIT: Object assignment - Table header: Objects available
objectsAvailable=Número de objetos
#XTIT: Object assignment - Dialog header text
selectObjects=Selecione os espaços e objetos que pretende atribuir ao seu nó de computação elástico:
#XTIT: Object assignment - Table header title: Objects
objects=Objetos
#XTIT: Object assignment - Table header: Type
type=Tipo
#XMSG: Message strip delete database user audit warning
deleteDatabaseUserWarningMessageStrip=Tenha em atenção que eliminar um utilizador da base de dados irá resultar na eliminação de todas as entradas do registo de auditoria geradas. Caso pretenda manter os registos de auditoria, considere exportá-los antes de eliminar o utilizador da base de dados.
#XMSG: Message strip delete HDI container audit logs impact
deleteHDIContainerWarningMessageStrip=Tenha em atenção que anular a atribuição de um contentor HDI do espaço irá resultar na eliminação de todas as entradas do registo de auditoria geradas. Caso pretenda manter os registos de auditoria, considere exportá-los antes de anular a atribuição do contentor HDI.
#XTXT: All audit logs
allAuditLogs=Todas as entradas do registo de auditoria geradas para o espaço
#XMSG: Message strip disable audit warning
disableAuditWarningMessageStrip=Tenha em atenção que desativar uma política de auditoria (ler ou alterar operações) irá resultar na eliminação de todas as suas entradas do registo de auditoria. Caso pretenda manter as entradas do registo de auditoria, considere exportá-las antes de desativar a política de auditoria.
#XTIT: No Spaces assigned to ECN Illustration title
noSpacesECNTitle=Não há nenhum espaço atribuído ainda
#XTIT: No Spaces assigned to ECN Illustration description
noSpacesECNDescription=Para começar a trabalhar com o seu nó de computação elástico, atribua-lhe um espaço ou objetos.
#XTIT: No Spaces Illustration title
noSpacesTitle=Ainda não foi criado nenhum espaço
#XTIT: No Spaces Illustration description
noSpacesDescription=Para começar a obter dados, crie um espaço.
#XTIT: No Spaces Illustration title
noDeletedSpacesTitle=A reciclagem está vazia
#XTIT: No Spaces Illustration description
noDeletedSpacesDescription=Pode recuperar os seus espaços eliminados a partir daqui.
#XTXT:  {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDatabaseUsers=Assim que o espaço for implementado, os seguintes utilizadores da base de dados serão eliminados {0} e não podem ser recuperados:
#XTIT: Delete database users
deleteDatabaseUsersTitle=Eliminar utilizadores da base de dados
#YMSE: Error if ecn id already exists
ecnIDInputDuplicateError=O ID já existe.
#YMSE: Error if ecn id contains invalid characters
ecnIDInputInvalidError=Utilize apenas carateres minúsculos a - z e os números 0 - 9.
#YMSE: Error if ecn id is too short
ecnIDInputTooShortError=O ID tem de ter, pelo menos, {0} carateres.
#XMSG: ecn id length warning
ecnIdLengthWarning=Máximo de {0} carateres excedido.
#XFLD: open System Monitor
systemMonitor=Monitor de sistema
#XFLD: open ECN schedule dialog menu entry
schedule=Agenda
#XFLD: open create ECN schedule dialog
createSchedule=Criar agenda
#XFLD: open change ECN schedule dialog
changeSchedule=Editar agenda
#XFLD: open delete ECN schedule dialog
deleteSchedule=Eliminar agendamento
#XFLD: open self assign ECN schedule dialog
selfAssignSchedule=Atribuir-me o agendamento
#XFLD: open pause ECN schedule dialog
pauseSchedule=Interromper o agendamento
#XFLD: open resume ECN schedule dialog
resumeSchedule=Retomar o agendamento
#XFLD: View Logs
viewLogs=Visualizar registos
#XFLD: Compute Blocks
computeBlocks=Blocos de computação
#XFLD: Memory label in ECN creation dialog
ecnMemory=Memória (GB)
#XFLD: Storage label in ECN creation dialog
ecnStorage=Armazenamento (GB)
#XFLD: Number of CPU label in ECN creation dialog
ecnCPUCount=Número de CPU
#XFLD: ECN updated by label
changedBy=Alterado por
#XFLD: ECN updated on label
changedOn=Alterado em
#XMSG: Success message after creating a Elastic Compute Node
createEcnSuccessMessage=Nó de computação elástico criado
#YMSE: Error while creating a Elastic Compute Node
createEcnError=Não foi possível criar nó de computação elástico
#XMSG: Success message after updating a Elastic Compute Node
updateEcnSuccessMessage=Nó de computação elástico atualizado
#XMSG: Error while updating a Elastic Compute Node
udpateEcnError=Não foi possível atualizar nó de computação elástico
#XMSG: Success message after deleting a Elastic Compute Node
deleteEcnSuccessMessage=Nó de computação elástico eliminado
#YMSE: Error while deleting a Elastic Compute Node
deleteEcnError=Não foi possível eliminar nó de computação elástico
#XMSG: Success message after starting a Elastic Compute Node
startingEcnSuccessMessage=A iniciar nó de computação elástico
#XMSG: Success message after stopping a Elastic Compute Node
stoppingEcnSuccessMessage=A parar nó de computação elástico
#YMSE: Error while starting a Elastic Compute Node
startingEcnError=Não foi possível iniciar nó de computação elástico
#YMSE: Error while stopping a Elastic Compute Node
stoppingEcnError=Não foi possível parar nó de computação elástico
#XBUT: Add Object button for an ECN
assignObjects=Adicionar objetos
#XFLD: Enable auto object assignment label
enableAutoObjAssignment=Adicionar todos os objetos automaticamente
#XFLD: object type label to be assigned
objectTypeLabel=Tipo (utilização semântica)
#XFLD: assigned object type label
assignedObjectTypeLabel=Tipo
#XFLD: technical name label
TechnicalNameLabel=Nome técnico
#XFLD: Object selecttion ECN label
objectSelectionECNLabel=Selecione os objetos que pretende adicionar ao nó de computação elástico.
#XTIT: Add objects dialog title
assignObjectsTitle=Atribuir objetos de
#XFLD: object label with object count
objectLabel=Objeto
#XMSG: No objects available to add message.
noObjectsToAssign=Nenhum objeto disponível para atribuir.
#XMSG: No objects assigned message.
noAssignedObjects=Nenhum objeto atribuído.
#XTIT: Remove objects popup title
removeObjectsOrSpacesConfirmationTitle=Aviso
#XTIT: Delete ECN popup button and title
deleteECNConfirmationButtonAndTitle=Eliminar
#XMSG: Remove objects popup text
removeObjectsConfirmation=Pretende realmente remover os objetos selecionados?
#XMSG: Remove spaces popup text
removeSpacesConfirmation=Pretende realmente remover os espaços selecionados?
#XBUT: Confirmation button for objects dialog
removeObjectsOrSpacesConfirmationBtn=Remover espaços
#XMSG: Success message after removing objects
removeObjectsSuccessMessage=Os objetos expostos foram removidos
#XMSG: Success message after assigning objects
assignObjectsSuccessMessage=Os objetos expostos foram atribuídos
#XFLD: auto object assignment label for space tile
autoObjAssignmentLabel=Todos os objetos expostos
#XFLD: Spaces tab label
spacesTabLabel=Espaços
#XFLD: Exposed objects tab label
consumptionObjectsTabLabel=Objetos expostos
#XMSG: Success message after unassigning spaces
spacesUnassignedSuccessMessage=Foram removidos espaços
#XMSG: Success message after unassigning space
spaceUnassignedSuccessMessage=O espaço foi removido
#YMSE: Error while assigning / unassigning spaces
spacesAssignUnassignError=Não foi possível atribuir ou remover espaços.
#YMSE: Error while removing objects
removeObjectsError=Não foi possível atribuir nem remover os objetos.
#YMSE: Error while removing object
removeObjectError=Não foi possível atribuir nem remover o objeto.
#YMSE: Error when saving the ecn with invalid value
invalidSizingPlanError=O número selecionado anteriormente já não é válido. Selecione um número válido.
#YMSE: Error when saving the ecn with invalid value
invalidPerformanceClassError=Selecione uma classe de desempenho válida.
#YMSE: Error when saving the ecn with invalid value
performanceClassNotAvailableError=A classe de desempenho "{0}" selecionada anteriormente não é válida de momento. Selecione a classe de desempenho válida.
#XTXT: Delete ECN confirmation text
deleteConfirmationTxtECN=Tem a certeza de que pretende eliminar o nó de computação elástico?
#XFLD: tooltip for ? button
help=Ajuda
#XFLD: ECN edit button label
editECN=Configurar
#XFLD: Technical type label for ERModel
DWC_ERMODEL=Modelo de relações de entidades
#XFLD: Technical type label for Local Table
DWC_LOCAL_TABLE=Tabela local
#XFLD: Technical type label for Remote Table
DWC_REMOTE_TABLE=Tabela remota
#XFLD: Technical type label for Analytic Model
DWC_ANALYTIC_MODEL=Modelo de análise
#XFLD: Technical type label for Task Chain
DWC_TASKCHAIN=Cadeia de tarefas
#XFLD: Technical type label for Data Flow
DWC_DATAFLOW=Fluxo de dados
#XFLD: Technical type label for Replication Flow
DWC_REPLICATIONFLOW=Fluxo de replicação
#XFLD: Technical type label for Transformation Flow
DWC_TRANSFORMATIONFLOW=Fluxo de transformação
#XFLD: Technical type label for Intelligent Lookup
DWC_IDT=Pesquisa inteligente
#XFLD: Technical type label for Repository
DWC_REPOSITORY_PACKAGE=Repositório
#XFLD: Technical type label for Enterprise Search
DWC_ESMODEL=Enterprise Search
#XFLD: Technical type label for View
DWC_VIEW=Vista
#XFLD: Technical type label for Data Product
DWC_DATAPRODUCT=Produto de dados
#XFLD: Technical type label for Data Access Control
DWC_DAC=Controlo de acesso a dados
#XFLD: Technical type label for Folder
DWC_FOLDER=Pasta
#XFLD: Technical type label for Data Entity
DWC_BUSINESS_ENTITY=Entidade de negócio
#XFLD: Technical type label for Business Entity Variant
DWC_BUSINESS_ENTITY_VARIANT=Variante da entidade de negócio
#XFLD: Technical type label for Responsibility Scenario
DWC_AUTH_SCENARIO=Cenário de responsabilidade
#XFLD: Technical type label for Fact Model
DWC_FACT_MODEL=Modelo de factos
#XFLD: Technical type label for Transformation Flow
DWC_PERSPECTIVE=Perspetiva
#XFLD: Technical type label for Consumption Model
DWC_CONSUMPTION_MODEL=Modelo de consumo
#XFLD: Technical type label for Remote Connection
DWC_REMOTECONNECTION=Ligação remota
#XFLD: Technical type label for Fact Model Variant
DWC_FACT_MODEL_VARIANT=Variante do modelo de factos
#XMSG: Schedule created alert message
createScheduleSuccess=Agenda criada
#XMSG: Schedule updated alert message
updateScheduleSuccess=Agenda atualizada
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Agenda eliminada
#XMSG: Schedule takeover success message
ownerChangeScheduleSuccess=Agenda atribuída a si
#XMSG: Message for starting pausing of schedule
pauseScheduleStarted=A interromper 1 agendamento
#XMSG: Message for starting resuming of schedule
resumeScheduleStarted=A retomar 1 agendamento
#XFLD: Segmented button label
availableSpacesButton=Disponível
#XFLD: Segmented button label
selectedSpacesButton=Selecionado
#XFLD: Visit website button text
visitWebsite=Visitar Web site
#XMSG: Warning message on unselecting source language
sourceLanguageRemovalWarning=O idioma de origem selecionado previamente será removido.
#XBUT: enable translation confirmation button
enableTranslationConfirmationBtn=Ativar
#XFLD: ECN performance class label
performanceClassLabel=Classe de desempenho
#XTXT performance class memory text
memoryText=Memória
#XTXT performance class compute text
computeText=Computação
#XTXT performance class high-compute text
highComputeText=Alta computação
#XBUT: Recycle Bin Button Text
recycleBin=Reciclagem
#XBUT: Restore Button Text
restore=Restaurar
#XMSG: Warning message for new Workload Management UI
priorityWarning=Esta área está no modo só de leitura. Pode alterar a prioridade de espaço na área Sistema/Configuração/Gestão de cargas de trabalho.
#XMSG: Warning message for new Workload Management UI
workloadWarning=Esta área está no modo só de leitura. Pode alterar a configuração da carga de trabalho de espaço na área Sistema/Configuração/Gestão de cargas de trabalho.
#XCOL: Space table-view column: Apache Spark vCPUs
sparkManagementVCPUs=vCPUs Apache Spark 
#XCOL: Space table-view column: Apache Spark Memory (GB)
sparkManagementMemory=Memória Apache Spark (GB)
#XCOL: Space type (from Space Capability) for Data Ingestion Spaces
ingestionSpaceType=Ingestão de produto de dados
#XFLD: Deployment in progress nodatatext for spark table
sparkTableDeploymentInProgress=Não há dados disponíveis, pois o espaço está a ser implementado
#XFLD: Runtime data being loaded nodatatext for spark table
sparkTableDeploymentLoading=Não há dados disponíveis, pois o espaço está a ser carregado
#XCOL: Space type (from Space Capability) for BW Private Cloud Edition
BWPCESpaceType=SAP BW
#XBUT: Go to Instance Mappings Button
navToMappingsButton=Editar mapeamentos de instância
#XFLD: SAP HANA Data Lake Storage label if less than 1GB
dataLakeStorageUsed=< 1 GB
