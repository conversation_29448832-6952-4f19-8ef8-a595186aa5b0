#DO NOT REMOVE________________________
#XFLD: Breadcrumb for space monitoring page
spaceMonitorBreadcrumb=Pārraudzība
#XTXT: Type name for spaces in browser tab page title
space=Vieta
#_____________________________________
#XFLD: Spaces label in
spaces=Atstarpes
#XFLD: Manage plan button text
manageQuotaButtonText=Pārvaldīt plānu
#XBUT: Manage resources button
manageResourcesButton=Pārvaldīt resursus
#XFLD: Create space button tooltip
createSpace=Izveidot vietu
#XFLD: Create
create=Izveidot
#XFLD: Deploy
deploy=Izvietot
#XFLD: Page
page=Lapa
#XFLD: Cancel
cancel=Atcelt
#XFLD: Update
update=Atjaunināt
#XFLD: Save
save=Saglabāt
#XFLD: OK
ok=Labi
#XFLD: days
days=Dienas
#XFLD: Space tile edit button label
edit=Rediģēt
#XFLD: Auto Assign all objects to space
autoAssign=Automātiski piešķirt
#XFLD: Space tile open monitoring button label
openMonitoring=Pārraudzīt
#XFLD: Delete
delete=Dzēst
#XFLD: Copy Space
copy=Kopēt
#XFLD: Close
close=Aizvērt
#XCOL: Space table-view column status
status=Statuss
#XFLD: Space status active
activeLabel=Aktīvs
#XFLD: Space status locked
lockedLabel=Bloķēts
#XFLD: Space status critical
criticalLabel=Kritisks
#XFLD: Space status cold
coldLabel=Auksts
#XFLD: Space status deleted
deletedLabel=Izdzēsts
#XFLD: Space status unknown
unknownLabel=Nezināms
#XFLD: Space status ok
okLabel=Vesels
#XFLD: Database user expired
expired=Beidzies derīgums
#XFLD: deployed
deployed=Izvietots
#XFLD: not deployed
notDeployed=Nav izvietots
#XFLD: changes to deploy
changesToDeploy=Izvietojamās izmaiņas
#XFLD: pending
pending=Izvietošana
#XFLD: designtime error
designtimeError=Dizaina laika kļūda
#XFLD: runtime error
runtimeError=Izpildlaika kļūda
#XFLD: Space created by label
createdBy=Izveidoja
#XFLD: Space created on label
createdOn=Izveides datums
#XFLD: Space deployed on label
deployedOn=Izvietots:
#XFLD: Space ID label
spaceID=Vietas ID
#XFLD: Priority label
priority=Prioritāte
#XFLD: Space Priority label
spacePriority=Vietas prioritāte
#XFLD: Space Configuration label
spaceConfiguration=Vietas konfigurācija
#XFLD: Not available
notAvailable=Nav pieejams
#XFLD: WorkloadType default
default=Noklusējums
#XFLD: WorkloadType custom
custom=Pielāgots
#XFLD: Data Lake option label; refers to component SAP HANA Cloud, Data Lake
dataLakeAccess=Datu ezera piekļuve
#XFLD: Translation label
translationLabel=Tulkošana
#XFLD: Source language label
sourceLanguageLabel=Avota valoda
#XFLD: Translation CheckBox label
translationCheckBox=Iespējot tulkošanu
#XTXT: Popover hint that space has to be deployed to show user details
openDatabaseUserHintPopoverText=Izvietojiet vietu, lai piekļūtu lietotāja detalizētajai informācijai.
#XTXT: Popover hint that space has to be deployed to open database explorer
openDatabaseExplorerHintPopoverText=Izvietojiet vietu, lai atvērtu datu bāzes pārlūku.
#XTXT: Text for when the Data Lake check box is disabled
dataLakeOptionDisabled=Jūs nevarat izmantot šo vietu, lai piekļūtu datu ezeram, jo to jau izmanto cita vieta.
#XFLD: Data Lake check box text; refers to component SAP HANA Cloud, Data Lake
dataLakeCheckBox=Izmantojiet šo vietu, lai piekļūtu datu ezeram.
#XFLD: Space Priority minimum label extension
low=Zems
#XFLD: Space Priority maximum label extension
high=Augsts
#XFLD: Space name label
spaceName=Vietas nosaukums
#XFLD: Enable deploy objects checkbox
enableDeployObjects=Izvietot objektus
#XTIT: Copy spaces dialog title
copySpaceDialogTitle=Kopēt {0}
#XTXT Human readable text for unselected source language
unSelectedSourceLanguage=(Nav atlasīts)
#XTXT Human readable text for language code "af"
af=Afrikandu
#XTXT Human readable text for language code "ar"
ar=Arābu
#XTXT Human readable text for language code "bg"
bg=Bulgāru
#XTXT Human readable text for language code "ca"
ca=Katalāņu
#XTXT Human readable text for language code "zh"
zh=Ķīniešu vienkāršotā
#XTXT Human readable text for language code "zf"
zf=Ķīniešu
#XTXT Human readable text for language code "hr"
hr=Horvātu
#XTXT Human readable text for language code "cs"
cs=Čehu
#XTXT Human readable text for language code "cy"
cy=Velsiešu
#XTXT Human readable text for language code "da"
da=Dāņu
#XTXT Human readable text for language code "nl"
nl=Holandiešu
#XTXT Human readable text for language code "en-UK"
en-UK=Angļu (Apvienotā Karaliste)
#XTXT Human readable text for language code "en"
en=Angļu (Amerikas Savienotās Valstis)
#XTXT Human readable text for language code "et"
et=Igauņu
#XTXT Human readable text for language code "fa"
fa=Persiešu
#XTXT Human readable text for language code "fi"
fi=Somu
#XTXT Human readable text for language code "fr-CA"
fr-CA=Franču (Kanāda)
#XTXT Human readable text for language code "fr"
fr=Franču
#XTXT Human readable text for language code "de"
de=Vācu
#XTXT Human readable text for language code "el"
el=Grieķu
#XTXT Human readable text for language code "he"
he=Ivrits
#XTXT Human readable text for language code "hi"
hi=Hindi
#XTXT Human readable text for language code "hu"
hu=Ungāru
#XTXT Human readable text for language code "is"
is=Islandiešu
#XTXT Human readable text for language code "id"
id=Indonēziešu
#XTXT Human readable text for language code "it"
it=Itāļu
#XTXT Human readable text for language code "ja"
ja=Japāņu
#XTXT Human readable text for language code "kk"
kk=Kazahu
#XTXT Human readable text for language code "ko"
ko=Korejiešu
#XTXT Human readable text for language code "lv"
lv=Latviešu
#XTXT Human readable text for language code "lt"
lt=Lietuviešu
#XTXT Human readable text for language code "ms"
ms=Malajiešu
#XTXT Human readable text for language code "no"
no=Norvēģu
#XTXT Human readable text for language code "pl"
pl=Poļu
#XTXT Human readable text for language code "pt"
pt=Portugāļu (Brazīlija)
#XTXT Human readable text for language code "pt-PT"
pt-PT=Portugāļu (Portugāle)
#XTXT Human readable text for language code "ro"
ro=Rumāņu
#XTXT Human readable text for language code "ru"
ru=Krievu
#XTXT Human readable text for language code "sr"
sr=Serbu
#XTXT Human readable text for language code "sh"
sh=Serbhorvātu
#XTXT Human readable text for language code "sk"
sk=Slovāku
#XTXT Human readable text for language code "sl"
sl=Slovēņu
#XTXT Human readable text for language code "es"
es=Spāņu
#XTXT Human readable text for language code "es-MX"
es-MX=Spāņu (Meksika)
#XTXT Human readable text for language code "sv"
sv=Zviedru
#XTXT Human readable text for language code "th"
th=Taju
#XTXT Human readable text for language code "tr"
tr=Turku
#XTXT Human readable text for language code "uk"
uk=Ukraiņu
#XTXT Human readable text for language code "vi"
vi=Vjetnamiešu
#XTIT: Delete spaces confirmation title
deleteConfirmationHeaderText=Dzēst vietas
#XTXT: Logically delete space delete confirmation text with space name
logicallyDeleteConfirmationBodyTextWithSpaceName=Vai tiešām vēlaties vietu "{0}" pārvietot uz atkritni?
#XTXT: Logically dpace delete confirmation text for multiple spaces
logicallyDeleteConfirmationBodyTextMultiple=Vai tiešām vēlaties {0} atlasītās vietas pārvietot uz atkritni?
#XTXT: Delete space delete confirmation text with space name
deleteConfirmationBodyTextWithSpaceName=Vai tiešām vēlaties dzēst vietu “{0}”? Šo darbību nevar atsaukt.
#XTXT: Delete space delete confirmation text for multiple spaces
deleteConfirmationBodyTextMultiple=Vai tiešām vēlaties dzēst {0} atlasītās vietas? Šo darbību nevar atsaukt. Tiks {1} dzēsts šāds saturs:
#XTXT: permanently
permanently=neatgriezeniski
#XTXT: {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDetail=Tālāk norādītais saturs tiks {0} izdzēsts, un to nevarēs atkopt:
#XTXT: Hint Delete space confirmation text
deleteConfirmationHint=Ievadiet {0}, lai apstiprinātu dzēšanu.
#XTXT: Delete space confirmation error check spelling
deleteConfirmationErrorCheckSpelling=Pārbaudiet pareizrakstību un mēģiniet vēlreiz.
#XTXT: All Spaces
allSpaces=Visas vietas
#XTXT: All data
allData=Visi objekti un dati vietā
#XTXT: All connections
allConnections=Visi vietā definētie savienojumi
#XFLD: Space tile selection box tooltip
clickToSelect=Noklikšķiniet, lai atlasītu
#XTXT: All database users
allDatabaseUsers=Visi objekti un dati, kas ir jebkurā Open SQL shēmā, kas saistīta ar vietu
#XFLD: remove members button tooltip
deleteUsers=Noņemt dalībniekus
#XTXT: Space long description text
description=Apraksts (Maks. 4000 rakstzīmes)
#XFLD: Add Members button tooltip
addUsers=Pievienot dalībniekus
#XFLD: Add Users button tooltip
addUsersTooltip=Pievienot lietotājus
#XFLD: Edit Users button tooltip
editUsersTooltip=Rediģēt lietotājus
#XFLD: Remove Users button tooltip
removeUsersTooltip=Noņemt lietotājus
#XFLD: Searchfield placeholder
filter=Meklēt
#XCOL: Users table-view column health
health=Veselība
#XCOL: Users table-view column access
access=Piekļūt
#XFLD: No user found nodatatext
noDataText=Nav atrasts neviens lietotājs
#XTIT: Members dialog title
selectUserDialogTitle=Dalībnieku pievienošana
#XTIT: User dialog title
addUserDialogTitle=Lietotāju pievienošana
#XTIT: Delete connections confirmation title
deleteConnectionConfirmationHeaderText=Dzēst savienojumus
#XTIT: Delete single connection confirmation title
deleteConnectionConfirmationHeaderTextSingle=Dzēst savienojumu
#XTXT: Delete connection delete confirmation text
deleteConnectionConfirmationBodyText=Vai tiešām vēlaties dzēst atlasītos savienojumus? Tie tiks neatgriezeniski noņemti.
#XTIT: Remote source dialog title
selectRemoteConnectionDialogTitle=Atlasīt savienojumus
#XTIT: Share connection dialog title
connectionSharingDialogTitle=Kopīgot savienojumu
#XTIT: Connections shared from other spaces table title
sharedConnectionsFromOtherSpaces=Kopīgotie savienojumi
#XFLD: Add remote source button tooltip
addRemoteConnections=Pievienot savienojumus
#XFLD: Delete remote source button tooltip
deleteRemoteConnections=Noņemt savienojumus
#XFLD: Share remote source button tooltip
shareConnections=Kopīgot savienojumus
#XFLD: Tile-layout tooltip
tileLayout=Flīzīšu izkārtojums
#XFLD: Table-layout tooltip
tableLayout=Tabulas izkārtojums
#XMSG: Success message after creating space
createSpaceSuccessMessage=Vieta ir izveidota
#XMSG: Success message after copying space
copySpaceSuccessMessage=Notiek vietas "{0}" kopēšana uz vietu "{1}"
#XMSG: Success message after deploying space
deploymentSuccessMessage=Vietas izvietošana ir sākta
#XMSG: Success message after Apache Spark update started
sparkUpdateStartedSuccessMessage=Apache Spark atjaunināšana ir sākusies
#YMSE: Error while starting Apache Spark update
sparkUpdateStartedErrorMessage=Neizdevās atjaunināt Apache Spark
#XMSG: Success message after updating space
updateSettingsSuccessMessage=Vietas detalizētā informācija ir atjaunināta
#XMSG: Success message after unlocking space
unlockSpaceSuccessMessage=Vieta īslaicīgi atbloķēta
#XMSG: Success message after deleting a single space
deleteSpaceSuccessMessage=Vieta izdzēsta
#XMSG: Success message after deleting multiple spaces
deleteSpacesSuccessMessage=Vietas izdzēstas
#XMSG: Success message after restoring a single space
restoreSpaceSuccessMessage=Vieta atjaunota
#XMSG: Success message after restoring multiple spaces
restoreSpacesSuccessMessage=Vietas atjaunotas
#YMSE: Error while updating settings
updateSettingsFailureMessage=Vietas iestatījumus nevarēja atjaunināt.
#YMSE: Error while updating data lake option
dataLakeEnabledInOtherSpace=Šis datu ezers jau ir piešķirts citai vietai. Tikai viena vieta var vienlaikus piekļūt datu ezeram.
#YMSE: Error while updating data lake option
virtualTablesExists=Jūs nevarat atsaistīt datu ezeru no šīs vietas, jo vēl ir atkarības no virtuālajām tabulām*. Lūdzu, izdzēsiet virtuālās tabulas, lai atsaistītu datu ezeru no šīs vietas.
#YMSE: Error while unlocking space
unlockSpaceFailureMessage=Vietu nevarēja atbloķēt.
#YMSE: Error while creating space
createSpaceError=Vietu nevarēja izveidot.
#YMSE: Error while creating duplicate space
createDuplicateSpaceError=Vieta ar nosaukumu {0} jau pastāv.
#YMSE: Error while deleting a single space
deleteSpaceError=Vietu nevarēja izdzēst.
#YMSE: Error details while deleting a single space
deleteSpaceDetailedError=Jūsu vieta “{0}” vairs nedarbojas pareizi. Lūdzu, vēlreiz mēģiniet to izdzēst. Ja tas joprojām neizdodas, lūdziet, ali administrators izdzēš jūsu vietu vai atveriet biļeti.
#YMSE: Error while deleting a single space in files
deleteSpaceInFilesError=Vietas datus sadaļā Faili nevarēja izdzēst.
#YMSE: Error while deleting a single space users
deleteSpaceUsersError=Lietotājus nevarēja noņemt.
#YMSE: Error while deleting a single space schema
deleteSpaceSchemasError=Shēmas nevarēja noņemt.
#YMSE: Error while deleting a single space connections
deleteSpaceConnectionsError=Savienojumus nevarēja noņemt.
#YMSE: Error while deleting a single space data
deleteSpaceDataError=Vietas datus nevarēja izdzēst.
#YMSE: Error while deleting multiple spaces
deleteSpacesError=Vietas nevarēja izdzēst.
#YMSE: Error while restoring a single space
restoreSpaceError=Vietu nevarēja atjaunot.
#YMSE: Error while restoring multiple spaces
restoreSpacesError=Vietas nevarēja atjaunot.
#YMSE: Error while creating users
createUsersError=Lietotājus nevarēja pievienot.
#YMSE: Error while removing users
removeUsersError=Nevarējām noņemt lietotājus.
#YMSE: Error while removing user
removeUserError=nevarēja noņemt lietotājus.
#YMSE: Error assigning roles to a user
roleAssignUnAssignError=Nevarējām pievienot lietotāju atlasītajai apjoma lomai. \n\n Jūs nevarat sevi pievienot apjoma lomai. Jūs varat palūgt administratoram pievienot jūs apjoma lomai.
#YMSE: Error assigning user to the space
userAssignError=Mēs nevarējām piešķirt vietai lietotāju. \n\n Tvēruma lomās lietotājs jau ir piešķirts maksimālajam atļautajam vietu skaitam (100).
#YMSE: Error assigning users to the space
usersAssignError=Mēs nevarējām piešķirt vietai lietotājus. \n\n Tvēruma lomās lietotājs jau ir piešķirts maksimālajam atļautajam vietu skaitam (100).
#YMSE: Error while fetching users
fetchUserWithScopedRolesError=Mēs nevarējām izgūt lietotājus. Lūdzu, vēlāk mēģiniet vēlreiz.
#YMSE: Error while fetching scoped roles
fetchScopedRolesError=Mēs nevarējām izgūt lomas ar apjomu.
#YMSE: Error while fetching members
fetchUserError=Dalībniekus nevarēja ienest. Lūdzu, vēlāk mēģiniet vēlreiz.
#YMSE: Error while loading run-time database
loadRuntimeError=Mēs nevarējām ielādēt informāciju no izpildlaika datu bāzes.
#YMSE: Error while loading spaces
loadSpacesError=Diemžēl kaut kas nogāja greizi, kad mēģinājām izgūt jūsu vietas.
#YMSE: Error while loading haas resources
loadStorageError=Diemžēl kaut kas nogāja greizi, kad mēģinājām izgūt krātuves datus.
#YMSE: Error no data could be loaded
loadDataError=Diemžēl kaut kas nogāja greizi, kad mēģinājām izgūt jūsu datus.
#XFLD: Click to refresh storage data
clickToRefresh=Noklikšķiniet šeit, lai mēģinātu vēlreiz.
#XTIT: Delete space popup title
deleteSpacePopupTitle=Dzēst vietu
#XCOL: Spaces table-view column name
name=Nosaukums
#XCOL: Spaces table-view deployment status
deploymentStatus=Izvietojuma statuss
#XFLD: Disk label in space details
storageLabel=Disks (GB)
#XFLD: In-Memory label in space details
ramLabel=Atmiņa (GB)
#XFLD: Memory label on space card
memory=Atmiņa krātuvei
#XFLD: Storage Assignment label in space details
storrageAssignmentLabel=Vietas krātuve
#XFLD: Storage Type label in space details
storageTypeLabel=Krātuves tips
#XFLD: Enable Space Quota
enableSpaceQuota=Iespējot vietas kvotu
#XFLD: No Space Quota
noSpaceQuota=Nav vietas kvotu
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
diskAndInMemoryText=SAP HANA datu bāze (disks un atmiņa)
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
dataLakeFilesText=SAP HANA datu ezera faili
#XFLD: Available scoped roles label
availableRoles=Pieejamās apjoma lomas
#XFLD: Selected scoped roles label
selectedRoles=Atlasītās apjoma lomas
#XCOL: Spaces table-view column models
models=Modeļi
#XCOL: Spaces table-view column users
users=Lietotāji
#XCOL: Spaces table-view column connections
connections=Savienojumi
#XFLD: Section header overview in space detail
overview=Apskats
#XFLD: Section header Apache Spark in space detail
sparkManagementSection=Apache Spark
#XFLD: Section header Apache Spark in space detail
sparkManagementSubSection=Lietojumprogrammas
#XFLD: Section header Apache Spark in space detail
taskAssignmentSubSection=Uzdevuma piešķire
#XFLD: vCPU label in Apache Spark section
vCPULabel=vCPU
#XFLD: Memory label in Apache Spark section
memoryLabel=Atmiņa (GB)
#XFLD: space configuration label in Apache Spark section
spaceConfigurationLabel=Vietas konfigurācija
#XFLD: Space Source label
sparkApplicationLabel=Lietojumprogramma
#XFLD: Cluster Size label
clusterSizeLabel=Klastera izmērs
#XFLD: Driver label
driverLabel=Draiveris
#XFLD: Executor label
executorLabel=Izpildītājs
#XFLD: max label
maxLabel=Maks. izmantotais
#XFLD: TrF Default label
trFDefaultLabel=Transformācijas plūsmas noklusējums
#XFLD: Merge Default label
mergeDefaultLabel=Sapludināšanas noklusējums
#XFLD: Optimize Default label
optimizeDefaultLabel=Optimizēšanas noklusējums
#XFLD: Deployment Default label
deploymentDefaultLabel=Lokālās tabulas (faila) izvietojums
#XFLD: max spark source label
driverExecutorMaxUsedSparkSource={0} CPU / {1} GB
#XFLD: Object type label
taskObjectTypeLabel=Objekta tips
#XFLD: Task activity label
taskActivityLabel=Aktivitāte
#XFLD: Task Application ID label
taskApplicationIDLabel=Noklusējuma lietojumprogramma
#XFLD: Section header in space detail
generalSettings=Vispārīgie iestatījumi
#XMSG: Message strip message when a space is locked
spaceLockedMessageStrip=Šī vieta pašlaik sistēmā ir bloķēta.
#XMSG: Message strip message deploy warning
deployWarningMessageStrip=Izmaiņas šajā sadaļā tiks nekavējoties izvietotas.
#XMSG: Message strip message modify space configuration warning
modifySpaceConfigurationMessageStrip=Lūdzu, ņemiet vērā: vērtību mainīšana var radīt problēmas jūsu veiktspējā.
#XFLD: Button text to unlock the space again
unlockSpace=Atbloķēt vietu
#XFLD: Info text for audit log formatted message
auditLogText=Iespējot audita žurnālus ierakstīt lasīšanas vai mainīšanas darbības (audita politikas). Pēc tam administratori var analizēt, kurš izpildīja kuru darbību kurā laika brīdī.
#XMSG: Message strip  audit warning
auditLogWarningMessageStrip=Audita žurnāli var patērēt daudz vietas jūsu nomnieka diska krātuvē. Ja iespējojat audita politiku (lasīšanas vai mainīšanas darbības), jums ir regulāri jāuzrauga diska krātuves lietojums (izmantojot kartīti “Izmantotā diska krātuve” sistēmas pārraugā), lai izvairītos no diska piepildīšanās izraisītām atteicēm, kuras var izraisīt pakalpojuma pārtraukumus. Ja kādu audita politiku atspējojat, visi tās audita žurnāla ieraksti tiek dzēsti. Ja audita žurnāla ierakstus vēlaties paturēt, apsveriet iespēju tos eksportēt, pirms atspējojat attiecīgo audita politiku.
#XFLD: Message strip link text to open in app help to guide user what to do to unlock his space permanently
unlockSpaceHelp=Rādīt palīdzību
#XMSG: Message strip message when a space will be locked again
spaceTemporarilyUnlockedMessageStrip=Šai vietai ir pārsniegta tās vietas krātuve, un tā tiks bloķēta pēc {0} {1}.
#XMSG: Unit for remaining time until space is locked again
hours=stundas
#XMSG: Unit for remaining time until space is locked again
minutes=minūtes
#XFLD: Section header in space detail for auditing
detailsSectionAuditing=Auditēšana
#XFLD: Subsection header in space detail for auditing
auditing=Vietas audita iestatījumi
#XFLD: Hot space tooltip
hotSpaceCountTooltip=Kritiskās vietas: krātuvē ir izmantots vairāk par 90%.
#XFLD: Green space tooltip
greenSpaceCountTooltip=Veselīgās vietas: krātuvē ir izmantots no 6% līdz 90%.
#XFLD: Cold space tooltip
coldSpaceCountTooltip=Aukstās vietas: krātuvē ir izmantots ne vairāk par 5%.
#XFLD: Critical space tooltip
criticalSpaceCountTooltip=Kritiskās vietas: krātuvē ir izmantots vairāk par 90%.
#XFLD: Green space tooltip
okSpaceCountTooltip=Veselīgās vietas: krātuvē ir izmantots no 6% līdz 90%.
#XFLD: Locked space tooltip
lockedSpaceCountTooltipNew=Slēgtās vietas: bloķētas nepietiekamas atmiņas dēļ.
#XFLD: Locked space tooltip
lockedSpaceCountTooltip=Slēgtās vietas
#YMSE: Error while deleting remote source
deleteRemoteError=Savienojumus nevarēja noņemt.
#XFLD: Space ID input tooltip
spaceIDInfoTooltip=Vietas ID vēlāk nevar mainīt.\nDerīgās rakstzīmes: A–Z, 0–9, un _
#YMSE: Error when space name is empty
spaceNameEmptyValidationError=Ievadiet vietas nosaukumu.
#YMSE: Error when ECN Business name is empty
ecnNameEmptyValidationError=Ievadiet biznesa nosaukumu.
#YMSE: Error when space id is empty
spaceIDInputEmptyValidationError=Ievadies vietas ID.
#YMSE: Error when space id contains invalid characters
spaceIDInputInValidError=Nederīgas rakstzīmes. Lūdzu, izmantojiet tikai A-Z, 0-9 un _.
#YMSE: Error when space id exists while creation
spaceIDInputDuplicateError=Vietas ID jau pastāv.
#XFLD: Space searchfield placeholder
search=Meklēt
#XMSG: Success message after creating users
createUsersSuccess=Lietotāji ir pievienoti
#XMSG: Success message after creating user
createUserSuccess=Lietotājs ir pievienots
#XMSG: Success message after updating users
updateUsersSuccess={0} lietotāji atjaunināti
#XMSG: Success message after updating user
updateUserSuccess=Lietotājs ir atjaunināts
#XMSG: Success message after removing users
removeUsersSuccess={0} lietotāji noņemti
#XMSG: Success message after removing user
removeUserSuccess=Lietotājs ir noņemts
#XFLD: Schema name
schemaName=Shēmas nosaukums
#XFLD: used of total
ofTemplate={0} no {1}
#XFLD: Assigned disk label in space overview
assignedStorageTemplate=Piešķirtais disks ({0} no {1})
#XFLD: Assigned Memory label in space overview
assignedRAMTemplate=Piešķirtā atmiņa ({0} no {1})
#XFLD: Storage ratio on space
accelearationRAM=Atmiņas paātrināšana
#XFLD: No Storage Consumption
noStorageConsumptionText=Krātuves kvotas nav piešķirtas.
#XFLD: Used disk label in space overview
usedStorageTemplate=Krātuvei izmantotais disks ({0} no {1})
#XFLD: Used Memory label in space overview
usedRAMTemplate=Krātuvei izmantotā atmiņa ({0} no {1})
#XFLD: Used Disk tooltip
usedStorageTooltipTemplate={0} no {1} disks izmantots krātuvei
#XFLD: Used Memory tooltip
usedRAMTooltipTemplate={0} no {1} atmiņas ir izmantoti
#XFLD: Assigned Disk tooltip
assignedStorageTooltipTemplate={0} no {1} diska ir piešķirti
#XFLD: Assigned Memory tooltip
assignedRAMTooltipTemplate={0} no {1} atmiņas ir piešķirti
#XFLD: Space related used storage tooltip
spaceRelatedUsedStorageTooltipTemplate=Vietas dati: {0}
#XFLD: Not space related used storage tooltip
otherDataUsedStorageTooltipTemplate=Citi dati: {0}
#XFLD: Storage used exceeded popover and tooltip
storageUsedExceededText=Apsveriet sava plāna pagarināšanu vai sazinieties ar SAP atbalsta dienestu.
#XCOL: Space table-view column used Disk
usedStorage=Krātuvei izmantotais disks
#XCOL: Space monitor column used Memory
usedRAM=Krātuvei izmantotā atmiņa
#XCOL: Space monitor column Schema
tableSchema=Shēma
#XCOL: Space monitor column Storage Type
tableStorageType=Krātuves tips
#XCOL: Space monitor column Table Type
tableType=Tabulas tips
#XCOL: Space monitor column Record Count
tableRecordCount=Ierakstu skaits
#XFLD: Assigned Disk
assignedStorage=Disks piešķirts krātuvei
#XFLD: Assigned Memory
assignedRAM=Atmiņa piešķirta krātuvei
#XCOL: Space table-view column storage utilization
tableStorageUtilization=Izmantotā krātuve
#XFLD: space status
spaceStatus=Vietas statuss
#XFLD: space type
spaceType=Vietas tips
#XFLD: space type label for space with space type "SAP BW bridge" ("SAP BW bridge" is a product name, do not translate it)
abapbridgeSpaceType=SAP BW tilts
#XFLD: space type label for space with space type "Data Marketplace"
marketplaceSpaceType=Datu nodrošinātāja produkts
#XMSG: Error message for when selected space cannot be deleted due to the space type
deleteTypedSpaceError=Jūs nevarat izdzēst vietu {0}, jo tās vietas tips ir {1}.
#XMSG: Error message for when selected spaces cannot be deleted due to the space type
deleteTypedSpacesError=Jūs nevarat izdzēst {0} atlasītās vietas. Vietas ar šādiem vietas tipiem nevar izdzēst: {1}.
#XFLD: space type label for space without space type
defaultSpaceType=SAP Datasphere
#XFLD: Tooltip for monitor space button
monitorSpace=Pārraudzīt
#XFLD: Tooltip for edit space button
editSpace=Rediģēt vietu
#XMSG: Deletion warning in messagebox
deleteConfirmation=Vai tiešām vēlaties izdzēst šo vietu?
#XFLD: Tooltip for delete space button
deleteSpace=Dzēst vietu
#XFLD: storage
storage=Disks krātuvei
#XFLD: username
userName=Lietotājvārds
#XFLD: port
port=Ports
#XFLD: hostname
hostName=Resursdatora nosaukums
#XFLD: password
password=Parole
#XBUT: Request new password button
requestPassword=Pieprasīt jaunu paroli
#YEXP: Usage explanation in time data section
timeDataSectionHint=Izveidojiet laika tabulas un dimensijas, ko izmantot savos modeļos un stāstos.
#YEXP: Usage explanation in Space schema section
spaceSchemaHint=Vai vēlaties, lai datus jūsu vietā varētu patērēt citi rīki vai lietojumprogrammas? Ja tā, izveidojiet vienu vai vairākus lietotājus, kas var piekļūt datiem jūsu vietā un atlasiet, vai vēlaties, lai visi nākotnes vietas dati būtu patērējami pēc noklusējuma.
#XTIT: Create schema popup title
createSchemaDialogTitle=Izveidot Open SQL shēmu
#XTIT: Generate Time Data dialog title
generateTimeDataDialogTitle=Izveidot laika tabulas un dimensijas
#XTIT: Edit Time Data dialog title
editTimeDataDialogTitle=Rediģēt laika tabulas un dimensijas
#XTIT: Time Data token title
timeDataTokenTitle=Laika dati
#XTIT: Time Data token title
timeDataUpdateViews=Atjaunināt laika datu skatus
#XFLD: Time Data token creation in progress label
timeDataCreationInProgress=Notiek izveide...
#XFLD: Time Data token creation error label
timeDataCreationError=Izveide neizdevās. Lūdzu, mēģiniet vēlreiz.
#XTIT: Time Data dialog time settings title
timeDataDialogTimeSettingsTitle=Laika tabulu iestatījumi
#XTIT: Time Data dialog time data table title
timeDataDialogTranslationTablesTitle=Pārrēķina tabulas
#XTIT: Time Data dialog dimensions title
timeDataDialogTimeViewsTitle=Laika dimensijas
#XFLD: Time Data dialog time range label
timeRangeHint=Definējiet laika diapazonu.
#XFLD: Time Data dialog time data table label
timeDataHint=Piešķiriet savai tabulai nosaukumu.
#XFLD: Time Data dialog time data dimensions label
timeDimensionsHint=Piešķiriet savām dimensijām nosaukumu.
#XFLD: Time Data Time range description label
timerangeLabel=Laika diapazons
#XFLD: Time Data dialog from year label
fromYearLabel=Sākuma gads
#XFLD: Time Data dialog to year label
toYearLabel=Beigu gads
#XFLD: Time Data dialog calendar type label
calendarTypeLabel=Kalendāra tips
#XFLD: Time Data dialog granularity label
granularityLabel=Granularitāte
#XFLD: Time Data dialog technical name label
technicalNameLabel=Tehniskais nosaukums
#XFLD: Time Data dialog quarters text table lable
quarterTextsTableLabel=Pārrēķina tabula ceturkšņiem
#XFLD: Time Data dialog months text table lable
monthsTextsTableLabel=Pārrēķina tabula mēnešiem
#XFLD: Time Data dialog days text table lable
daysTextsTableLabel=Pārrēķina tabula dienām
#XFLD: Time Data dialog year label
yearLabel=Gada dimensija
#XFLD: Time Data dialog quarter label
quarterLabel=Ceturkšņa dimensija
#XFLD: Time Data dialog month label
monthLabel=Mēneša dimensija
#XFLD: Time Data dialog day label
dayLabel=Dienas dimensija
#XFLD: Time Data dialog gregorian calendar type label
gregorian=Gregoriāņu
#XFLD: Time Data dialog time granularity day label
day=Diena
#XFLD: Time Data dialog business name maxlength reached
timeRangeValueStateTextMaxLengthBusinessName=Sasniegts maksimālais 1000 rakstzīmju garums.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextMaxRangeExceeded=Maksimālais laika diapazons ir 150 gadu.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeLower=Vērtībai “Sākuma gads” ir jābūt mazākai par “Beigu gads”
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeHigher=Vērtībai “Sākuma gads” ir jābūt 1900 vai lielākai.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeHigher=Vērtībai “Beigu gads” ir jābūt lielākai par “Sākuma gads”
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeLower=Vērtībai “Beigu gads” ir jābūt mazākai par pašreizējais gads plus 100
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningFromYear=Vērtības “Sākuma gads” palielināšana var novest pie datu zaudēšanas
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningToYear=Vērtības “Sākuma gads” samazināšana var novest pie datu zaudēšanas
#XMSG: Time Data creation validation error message
timeDataValidationError=Šķiet, daži lauki ir nederīgi. Lai turpinātu, pārbaudiet obligātos laukus.
#XMSG: Time Data deletion popup text
deleteTimeDataConfirmation=Vai tiešām vēlaties izdzēst šos datus?
#XMSG: Time Data creation success message
createTimeDataSuccess=Laika dati izveidoti
#XMSG: Time Data update success message
updateTimeDataSuccess=Laika dati atjaunināti
#XMSG: Time Data delete success message
deleteTimeDataSuccess=Laika dati izdzēsti
#XMSG: Time Data creation error message
createTimeDataError=Mēģinot izveidot laika datus, kaut kas nogāja greizi.
#XMSG: Time Data update error message
updateTimeDataError=Mēģinot atjaunināt laika datus, kaut kas nogāja greizi.
#XMSG: Time Data creation error message
deleteTimeDataError=Mēģinot izdzēst laika datus, kaut kas nogāja greizi.
#XMSG: Error message when time data could not be retrieved
retrievingTimeDataError=Laika datus nevarēja ielādēt.
#XTIT: Time Data deletion popup title
deleteTimeDataConfirmationTitle=Brīdinājums
#XTXT: Time Data deletion dependency popup text
deleteTimeDataDependencyText=Mēs nevarējām izdzēst jūsu laika datus, jo tie tiek lietoti citos modeļos.
#XTXT: Time Data deletion dependency popup text singular
deleteTimeDataDependencyTextSingular=Mēs nevarējām izdzēst jūsu laika datus, jo tie tiek lietoti citā modelī.
#XMSG: Validation error when required time data form field is empty
timeDataRequiredField=Šis lauks ir obligāts, un to nedrīkst atstāt tukšu.
#XFLD: Tooltip for Open in Database Explorer button
openInDBX=Atvērt datu bāzes pārlūkā
#YMSE: Dimension Year
dimensionYearView=Dimensija “Gads”
#YMSE: Dimension Year
dimensionQuarterView=Dimensija “Ceturksnis”
#YMSE: Dimension Year
dimensionMonthView=Dimensija “Mēnesis”
#YMSE: Dimension Year
dimensionDayView=Dimensija “Diena”
#XFLD: Time Data deletion object title
timeDataUsedIn=(izmantots {0} modeļos)
#XFLD: Time Data deletion object title singular
timeDataUsedInSingular=(izmantots 1 modelī)
#XFLD: Time Data deletion table column provider
provider=Nodrošinātājs
#XTIT: Time Data dependency dialog title
timeDataDependencyDialogTitle=Atkarības
#XTIT: Create schema popup title
createSpaceSchemaUserDialogTitle=Izveidot lietotāju vietas shēmai
#XFLD: Create schema button
createSchemaButton=Izveidot Open SQL shēmu
#XFLD: Generate TimeData button
generateTimeDataButton=Izveidot laika tabulas un dimensijas
#XFLD: Show dependencies button
showDependenciesButton=Rādīt atkarības
#XFLD: tooltip for disabled buttons when user is not member of space
mustBeMemberOfSpaceForOperation=Lai veiktu šo darbību, jūsu lietotājam ir jābūt vietas dalībniekam.
#XFLD: Create space schema user button
createSpaceSchemaUserButton=Izveidot vietas shēmas dalībnieku
#YMSE: API Schema users load error
loadSchemaUsersError=Lietotāju sarakstu nevarēja ielādēt.
#XTIT: API Schema-config popup title
schemaUserDetailsDialogTitle=Vietas shēmas lietotāja detalizētā informācija
#XMSG: API Schema user deletion popup text
deleteSpaceSchemaUserConfirmation=Vai tiešām vēlaties izdzēst atlasīto lietotāju?
#XMSG: API Schema user deletion confirmation text
userDeleteSuccess=Lietotājs ir izdzēsts.
#YMSE: API Schema user deletion error
userDeleteError=Šo lietotāju nevarēja izdzēst.
#XFLD: User deleted
userDeleted=Lietotājs ir izdzēsts.
#XTIT: Remove user popup title
removeUserConfirmationTitle=Brīdinājums
#XMSG: Remove user popup text
removeUserConfirmation=Vai tiešām vēlaties noņemt lietotāju? Lietotājs un tam piešķirtās apjoma lomas tiks noņemtas no vietas.
#XMSG: Remove users popup text
removeUsersConfirmation=Vai tiešām vēlaties noņemt lietotājus? Lietotāji un tiem piešķirtās apjoma lomas tiks noņemtas no vietas.
#XBUT: Confirmation button for remove user dialog
removeUserConfirmationBtn=Noņemt
#YMSE: No data text for available roles
noDataAvailableRoles=Vieta nav pievienota nekādai apjoma lomai. \n Lai vietai varētu pievienot lietotājus, tā vispirms ir jāpievieno vienai vai vairākām apjoma lomām.
#YMSE: No data text for selected roles
noDataSelectedRoles=Nav atlasītu apjoma lomu
#XTIT: Schema-config popup title
schemaDetailsDialogTitle=Open SQL shēmas konfigurācijas detalizētā informācija
#XFLD: Label for Read Audit Log
auditLogRead=Iespējot audita žurnālu lasīšanas darbībām
#XFLD: Label for Change Audit Log
auditLogChange=Iespējot audita žurnālu izmaiņu darbībām
#XFLD: Label Audit Log Retention
auditLogRetention=Uzturēt žurnālus:
#XFLD: Label Audit Log Retention Unit
retentionUnit=Dienas
#YMSE: Error when log retention time is set incorrectly
incorrectRetentionTime=Ievadiet veselu skaitli no {0} līdz {1}
#XFLD: Label for Cross Space Schema Access
spaceSchemaAccess=Patērēt vietas shēmas datus
#XTIT: Confirm deactivation of space schema consumption dialog title
disableSpaceSchemaConsumptionDialogTitle=Beigt patērēt vietas shēmas datus
#XTXT: Confirm deactivation of space schema consumption dialog text
disableSpaceSchemaConsumptionDialogText=Šī Open SQL shēma, iespējams, patērē jūsu vietas shēmas datus. Ja pārtrauksit patērēšanu, modeļi, kuru pamatā ir vietas shēmas dati, iespējams, vairs nedarbosies.
#XBUT: Confirmation button for the stop consuming space schema data dialog
stopConsuming=Beigt patērēt
#XTXT: Tooltip for data lake icon
dataLakeIconTooltip=Šī vieta tiek izmantota, lai piekļūtu datu ezeram
#XFLD: Tooltip for data lake table entry
dataLakeTableEntryTooltip=Datu ezers ir iespējots
#XFLD: used In-Memory linit reached tag
usedRamLimitReachedLabel=Sasniegts atmiņas limits
#XFLD: Space storage Limit reached tag
storageLimitReachedLabel=Sasniegts krātuves limits
#XFLD: Minimal Storage Limit Reached tag
minStorageLimitReachedLabel=Sasniegts minimālais krātuves limits
#XFLD: Space ram tag
ramLimitReachedLabel=Sasniegts atmiņas limits
#XFLD: Minimal In-Memory Limit Reached tag
minRamLimitReachedLabel=Sasniegts minimālais atmiņas limits
#XFLD: Popover text after click on space storage limit warning
storageLimitReachedText=Esat sasniedzis piešķirto vietas krātuves limitu, kas ir {0}. Lūdzu, piešķiriet šai vietai lielāku krātuvi.
#XFLD: System storage tag
systemStorageLimitReachedLabel=Sasniegts sistēmas krātuves limits
#XMSG: Popover text after click on storage limit warning
systemStorageLimitReachedText=Esat sasniedzis sistēmas krātuves limitu, kas ir {0}. Jūs šobrīd nevarat piešķirt šai vietai lielāku krātuvi.
#XMSG: Schema deletion warning
deleteSchemaConfirmation=Dzēšot šo Open SQL shēmu, tiks neatgriezeniski izdzēsti arī visi saglabātie objekti un uzturētās saistības šajā shēmā. Vai turpināt?
#XMSG: Success message after deleting a schema
schemaDeleteSuccess=Shēma izdzēsta
#YMSE: Error while deleting schema.
schemaDeleteError=Šo shēmu nevarēja izdzēst.
#XMSG: Success message after update a schema
schemaUpdateSuccess=Shēma atjaunināta
#YMSE: Error while updating schema.
schemaUpdateError=Šo shēmu nevarēja atjaunināt.
#XMSG: Hint when no password is shown in the dialog.
newPasswordHint=Esam nodrošinājuši šai shēmai paroli. Ja esat aizmirsis savu paroli vai pazaudējis to, varat pieprasīt jaunu. Neaizmirstiet nokopēt vai saglabāt jauno paroli.
#XMSG: Hint when showing the password in the dialog.
savePasswordHint=Lūdzu, nokopējiet savu paroli. Jums tā būs vajadzīga, lai iestatītu savienojumu ar šo shēmu. Ja esat aizmirsis savu paroli, varat atvērt šo dialogu un atiestatīt to.
#XFLD,50: Hint on entering schema name in creation dialog
schemaNameHint=Pēc tam, kad shēma ir izv., šo nos. nevar mainīt.
#XFLD: Open SQL Schemas section sub headline
schemasSQL=Open SQL
#XFLD: Space schema section sub headline
schemasSpace=Vieta
#XFLD: HDI Container section header
HDIContainers=HDI konteineri
#XTXT: Add HDI Containers button tooltip
addHDIContainers=Pievienot Hdi konteinerus
#XTXT: Remove HDI Containers button tooltip
removeHDIContainers=Noņemt HDI konteinerus
#XFLD: Enable HDI Containers feature button
enableHDIContainersButton=Iespējot piekļuvi
#YMSE: No data text for HDI Containers table
noDataHDIContainers=Neviens HDI konteiners netika pievienots.
#YMSE: No data text for Timedata section
noDataTimedata=Nav izveidota neviena laika tabula un dimensija.
#YMSE: No data text during runtime not available for Timedata section
runtimeNotAvailableNoDataTimedata=Nevar ielādēt laika tabulas un dimensijas, jo izpildlaika datu bāze nav pieejama.
#YMSE: No data text during runtime not available for HDI section
runtimeNotAvailableNoDataHDI=Nevar ielādēt HDI konteinerus, jo izpildlaika datu bāze nav pieejama.
#YMSE: Error while fetching HDI Containers
fetchHDIContainersError=Šos HDI konteinerus nevarēja iegūt. Lūdzu, vēlāk mēģiniet vēlreiz.
#XFLD Table column header for HDI Container names
HDIContainerName=HDI konteinera nosaukums
#XFLD: Enable HDI Containers feature dialog title
enableHDIContainersDialogTitle=Iespējot piekļuvi
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText1New=Jūsu SAP Datasphere nomniekā varat iespējot SAP SQL Data Warehousing, lai apmainītu datus starp jūsu HDI konteineriem un jūsu SAP Datasphere vietām bez nepieciešamības izmantot datu kustību.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText2=Lai to paveiktu, noklikšķiniet uz pogas zemāk un atveriet atbalsta biļeti.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText3New=Kad biļete ir apstrādāta, SAP Datasphere izpildlaika datu bāzē izveidojiet vienu vai vairākus jaunus HDI konteinerus. Pēc tam poga “Iespējot piekļuvi” tiek aizstāta ar pogu “+” sadaļā HDI konteineri visām jūsu SAP Datasphere vietām, un jūs varat vietai pievienot konteinerus.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText41=Vai nepieciešams vairāk informācijas? Dodieties uz %%0. Detalizētu informāciju par to, ko iekļaut talonā, skatiet %%1.
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkText=SAP Help
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkTextSapNote3057059=SAP piezīme 3057059
#XBUT: Open Ticket Button Text
openTicket=Atvērt biļeti
#XBUT: Add Button Text
add=Pievienot
#XBUT: Next Button Text
next=Tālāk
#XBUT: Edit Button Text
editUsers=Rediģēt
#XBUT: create user Button Text
createUser=Izveidot
#XBUT: Update user Button Text
updateUser=Atlasīt
#XTIT: Add HDI Containers Dialog Title
addHDIContainersDialogTitle=Pievienot nepiešķirtos HDI konteinerus
#XFLD: No data text for add HDI container dialog
addHDIContainersDialogNoDataText=Mēs nevarējām atrast nevienu nepiešķirtu konteineru. \N Konteiners, ko meklējat, iespējams, jau ir piešķirts vietai.
#YMSE: Error while loading assigned HDI Containers
loadAssignedHDIContainersError=Piešķirtos HDI konteinerus nevarēja ielādēt.
#YMSE: Error while loading assigned HDI Containers
loadUnassignedHDIContainersError=HDI konteinerus nevarēja ielādēt.
#XMSG: Success message
succeededToAddHDIContainer=HDI konteiners pievienots
#XMSG: Success message
succeededToAddHDIContainerPlural=HDI konteineri pievienoti
#XMSG: Success message
succeededToDeleteHDIContainer=HDI konteiners noņemts
#XMSG: Success message
succeededToDeleteHDIContainerPlural=HDI konteineri noņemti
#XFLD: Time data section sub headline
timeDataSection=Laika tabulas un dimensijas
#XFLD: Schemas list entry read audit log enabled indicator
readIndicator=Lasīt
#XFLD: Schemas list entry change audit log enabled indicator
changeIndicator=Mainīt
#XFLD: Remote sources section sub headline
allconnections=Savienojuma piešķire
#XFLD: Remote sources section sub headline
localconnections=Lokālie savienojumi
#XFLD: Remote sources section sub headline for open Connectors
openConnectors=SAP atvērtie savienotāji
#XFLD: User section sub headline
memberassignment=Dalībnieku piešķire
#XFLD: User assignment section sub headline
userAssignment=Lietotāju piešķire
#XFLD: User section Access dropdown Member
member=Dalībnieks
#XFLD: User assignment section column name
user=Lietotājvārds
#XTXT: Selected role count
selectedRoleToolbarText=Atlasīts: {0}
#XTIT: Space detail section connections title
detailsSectionConnections=Savienojumi
#XTIT: Space detail section data access title
detailsSectionDataAccess=Shēmas piešķire
#XTIT: Space detail section time data title
detailsSectionGenerateData=Laika dati
#XTIT: Space detail section members title
detailsSectionUsers=Dalībnieki
#XTIT: Space detail section Users title
detailsSectionUsersTitle=Lietotāji
#XTIT: Out of Storage
insufficientStoragePopoverTitle=Krātuvē nav vietas
#XTIT: Storage distribution
storageDistributionPopoverTitle=Izmantotā diska krātuve
#XTXT: Out of Storage popover text
insufficientStorageText=Lai izveidotu jaunu vietu, samaziniet citai vietai piešķirtās krātuves lielumu vai izdzēsiet vietu, kas jums vairs nav vajadzīga. Varat palielināt savu kopējo sistēmas krātuvi, izsaucot iespēju Pārvaldīt plānu.
#XMSG: Space id length warning
spaceIdLengthWarning=Pārsniegts maksimums, kas ir {0} rakstzīmes.
#XMSG: Space name length warning
spaceNameLengthWarning=Pārsniegts maksimums, kas ir {0} rakstzīmes.
#XMSG: Space id reserved prefix warning
spaceIdnWarningReservedPrefix=Lūdzu, neizmantojiet prefiksu {0}, lai izvairītos no iespējamiem konfliktiem.
#YMSE: Error while loading open SQL schemas
loadLocalSchemasError=Open SQL shēmas nevarēja ielādēt.
#YMSE: Error while creating open SQL schema
createLocalSchemaError=Open SQL shēmu nevarēja izveidot.
#YMSE: Error while loading all remote connections
loadRemoteConnectionsError=Nevarēja ielādēt visus attālos savienojumus.
#YMSE: Error while loading space details
loadSpaceDetailsError=Vietas detalizēto informāciju nevarēja ielādēt.
#YMSE: Error while deploying space details
deploySpaceDetailsError=Vietu nevarēja izvietot.
#YMSE: Error while copying space details
copySpaceDetailsError=Vietu nevarēja nokopēt.
#YMSE: Error while loading storage data
loadStorageDataError=Krātuves datus nevarēja ielādēt.
#YMSE: Error while loading all users
loadAllUsersError=Nevarēja ielādēt visus lietotājus.
#YMSE: Failed to reset password
resetPasswordError=Paroli nevarēja atiestatīt.
#XMSG: Success message after updating space
resetPasswordSuccessMessage=Iestatīta jauna parole shēmai
#YMSE: DP Agent-name too long
DBAgentNameError=DP aģenta nosaukums ir pārāk garš.
#YMSE: Schema-name not valid.
schemaNameError=Shēmas nosaukums ir nederīgs.
#YMSE: User name not valid.
UserNameError=Lietotājvārds ir nederīgs.
#XTIT: Consumption by Storage Type
consumptionStorageTypeText=Patēriņš pēc krātuves tipa
#XTIT: Consumption by Schema
consumptionSchemaText=Patēriņš pēc shēmas
#XTIT: Overall Consumption by schema
overallConsumptionSchemaText=Kopējais tabulas patēriņš pēc shēmas
#XTIT: Overall Consumption by Table Type
overallConsumptionTableTypeText=Kopējais patēriņš pēc tabulas tipa
#XTIT: Tables
tableDetailsText=Tabulas detalizētā informācija
#XTIT: Table Storage Consumption
tableStorageConsumptionText=Tabulas krātuves patēriņš
#XFLD: Table Type label
tableTypeLabel=Tabulas tips
#XFLD: Schema label
schemaLabel=Shēma
#XFLD: reset table tooltip
resetTable=Atiestatīt tabulu
#XFLD: In-Memory label in space monitor
inMemoryLabel=Atmiņa
#XFLD: Disk label in space monitor
diskLabel=Disks
#XFLD: Yes
yesLabel=Jā
#XFLD: No
noLabel=Nē
#XFLD: switch label: Want the data in this space to be consumable by default?
allowConsumptionByDefault=Vai vēlaties, lai dati šajā vietā būtu patērējami pēc noklusējuma?
#XFLD: Business Name
businessNameLabel=Biznesa nosaukums
#XFLD: Refresh
refresh=Atsvaidzināt
#XMSG: No filter results title
noFilterResultsTitle=Šķiet, jūsu filtra iestatījumi neuzrāda nekādus datus.
#XMSG: No filter results message
noFilterResultsMsg=Mēģiniet precizēt savus filtra iestatījumus un ja joprojām neredzat nekādus datus, izveidojiet datu veidotājā dažas tabulas. Tiklīdz tās patērēs krātuvē vietu, jūs varēsit tās šeit pārraudzīt.
#XMSG: Space monitoring Hana state red title
spaceMonitorHanaRedTitle=Izpildlaika datu bāze nav pieejama.
#XMSG: Space monitoring Hana state red message
spaceMonitorHanaRedMsg=Tā kā izpildlaika datu bāze nav pieejama, noteiktas funkcijas ir atspējotas un mēs nevaram šajā lapā parādīt nekādu informāciju.
#YMSE: Error while creating space schema user
createSpaceSchemaUserError=Vietas shēmas lietotāju nevarēja izveidot.
#YMSE: Error User name already exists
userAlreadyExistsError=Lietotājvārds jau pastāv.
#YMSE: Error Authentication failed
authenticationFailedError=Autentifikācija neizdevās.
#XMSG: Message strip message when a schema user is locked
userLockedMessageStrip=Lietotājs ir bloķēts, jo bija pārāk daudz neizdevušos pieteikšanās mēģinājumu. Lai atbloķētu lietotāju, pieprasiet jaunu paroli.
#XMSG: Success message after reset and unlock schema user
resetUnlockPasswordSuccessMessage=Iestatīta jauna parole, un lietotājs atbloķēts
#XMSG: user is locked message
userLockedMessage=Lietotājs ir atbloķēts.
#XCOL: Users table-view column Role
spaceRole=Loma
#XCOL: Users table-view column Scoped Role
spaceScopedRole=Apjoma loma
#XCOL: Users table-view column Space Admin
spaceAdmin=Vietas administrators
#XFLD: User section dropdown value Viewer
viewer=Skatītājs
#XFLD: User section dropdown value Modeler
modeler=Modelētājs
#XFLD: User section dropdown value Data Integrator
dataIntegrator=Datu integrētājs
#XFLD: User section dropdown value Space Administrator
spaceAdministrator=Vietas administrators
#XMSG: Success message -  Space Role updated successfully.
updateUserSpaceRoleSuccess=Vietas loma atjaunināta
#XMSG: Error message - Failed to update Space Role.
updateUserSpaceRoleError=Vietas loma netika sekmīgi atjaunināta.
#XFLD:
databaseUserNameSuffix=Datu bāzes lietotājvārda sufikss
#XTXT: Space Schema password text
spaceSchemaPasswordText=Lai iestatītu savienojumu ar šo shēmu, nokopējiet savu paroli. Gadījumā, ja esat aizmirsis savu paroli, jūs vienmēr varat pieprasīt jaunu.
#XFLD: Cloud Platform label in space schema dialog
cloudPlatformLabel=Mākoņplatforma
#XTXT: Cloud Platform text in space schema dialog
cloudPlatformText=Lai iestatītu piekļuvi, izmantojot šo lietotāju, iespējojiet patēriņu un nokopējiet akreditācijas datus. Gadījumā, ja varat nokopēt tikai akreditācijas datus bez paroles, noteikti pievienojiet paroli vēlāk.
#XFLD: enable consumption in CloudPlatform label in space schema dialog
enableConsumptionInCloudPlatformLabel=Iespējot patēriņu mākoņplatformā
#XFLD: Credentials for User-Provided Service label in space schema dialog
cloudPlatformCredentialsLabel=Akreditācijas dati lietotāja nodrošinātam pakalpojumam:
#XFLD: Credentials for User-Provided Service (Without Password) label in space schema dialog
cloudPlatformCredentialsWithoutPasswordLabel=Akreditācijas dati lietotāja nodrošinātam pakalpojumam (bez paroles):
#XFLD: Copy Credentials label in space schema dialog
copyCredentialsLabel=Kopēt akreditācijas datus bez paroles
#XFLD: Copy Credentials label in space schema dialog
copyFullCredentialsLabel=Kopēt visus akreditācijas datus
#XFLD: Copy Password label in space schema dialog
copyPasswordLabel=Kopēt paroli
#XMSG: Credentials copied to clipboard
credentialsCopiedMessage=Akreditācijas dati nokopēti uz starpliktuvi
#XMSG: Password copied to clipboard
passwordCopiedMessage=Parole nokopēta uz starpliktuvi
#XMSG: create database user dialog title
createDatabaseUserDialogTitle=Izveidot datu bāzes lietotāju
#XMSG: Database Users section title
databaseUsers=Datu bāzes lietotāji
#XMSG: Database User Details dialog title
databaseUserDetailsDialogTitle=Datu bāzes lietotāja detalizētā informācija
#XFLD: database user read audit log
databaseUserAuditLogRead=Iespējot audita žurnālus lasīšanas darbībām un paturēt žurnālus
#XFLD: database user change audit log
databaseUserAuditLogChange=Iespējot audita žurnālus izmaiņu darbībām un paturēt žurnālus
#XMSG: Cloud Platform Access
cloudPlatformAccess=Mākoņplatformas piekļuve
#XFLD: Enable Consumption in Cloud Platform hint
enableConsumptionCloudPlatformHint=Iestatiet piekļuvi savam HANA izvietošanas infrastruktūras (HDI) konteineram, izmantojot šo datu bāzes lietotāju. Lai izveidotu savienojumu ar jūsu HDI konteineru, ir jābūt ieslēgtai SQL modelēšanai
#XFLD: Enable HDI Consumption
enableConsumptionCloudPlatform=Iespējot HDI patēriņu
#XFLD: Enable Consumption hint
enableConsumptionHint=Vai vēlaties, lai jūsu vietā esošos datus patērētu citi rīki vai lietojumprogrammas?
#XFLD: Enable Consumption
enableConsumption=Iespējot SQL patēriņu
#XFLD: Enable Modeling
enableModeling=Iespējot SQL modelēšanu
#XMSG: Privileges for Data Modeling
privilegesModeling=Datu uzņemšana
#XMSG: Privileges for Data Consumption
privilegesConsumption=Datu patēriņš ārējiem rīkiem
#XFLD: SQL Modeling
sqlModeling=SQL modelēšana
#XFLD: SQL Consumption
sqlConsumption=SQL patēriņš
#XFLD: enabled
enabled=Iespējots
#XFLD: disabled
disabled=Atspējots
#XFLD: Edit Privileges
editPrivileges=Rediģēt privilēģijas
#XFLD: Open Database Explorer
openDBX=Atklātās datu bāzes pārlūks
#XFLD: create database user hint
databaseCreateHint=Lūdzu, ņemiet vērā, ka pēc saglabāšanas lietotājvārdu vairs nevarēs mainīt.
#XFLD: Internal Schema Name
internalSchemaName=Iekšējās shēmas nosaukums
#YMSE: Failed to load database users
loadDatabaseUserError=Neizdevās ielādēt datu bāzes lietotājus
#YMSE: Failed to delete database user
deleteDatabaseUsersError=Neizdevās dzēst datu bāzes lietotājus
#XMSG: Success message after deleting database user
deleteDatabaseUserSuccess=Datu bāzes lietotājs ir izdzēsts
#XMSG: Success message after deleting database users
deleteDatabaseUsersSuccess=Datu bāzes lietotāji ir izdzēsti
#XMSG: Success message after creating database user
createDatabaseUserSuccessMessage=Datu bāzes lietotājs ir izveidots
#YMSE: Failed to create database user
createDatabaseUserError=Neizdevās izveidot datu bāzes lietotāju
#XMSG: Success message after updating database user
updateDatabaseUserSuccessMessage=Datu bāzes lietotājs ir atjaunināts
#YMSE: Failed to update database user
updateDatabaseUserError=Neizdevās atjaunināt datu bāzes lietotāju
#XFLD: HDI Consumption
hdiConsumption=HDI patēriņš
#XTIT: Space detail section Database Access title
detailsSectionDatabaseAccess=Datu bāzes piekļuve
#XTXT: Database users consumption hint
databaseUsersConsumptionHint=Padariet savas vietas datus patērējamus pēc noklusējuma. Šie modeļi veidotājos automātiski atļaus datiem būt patērējamiem.
#XFLD: Default Consumption of Space Data:
defaultConsumptionOfSpaceData=Vietas datu noklusējuma patēriņš:
#XFLD: Database User Name
databaseUserName=Datu bāzes lietotājvārds
#XMSG: Database User creation validation error message
databaseUserValidationError=Šķiet, daži lauki ir nederīgi. Lai turpinātu, pārbaudiet obligātos laukus.
#XMSG: SQL modeling can’t be enabled since this user has been migrated.
migratedDatabaseUserHint=Datu uzņemšanu nevar iespējot, jo šis lietotājs ir migrēts.
#XBUT: Remove Button Text
remove=Noņemt
#XBUT: Remove Spaces Button Text
removeSpaces=Noņemt vietas
#XBUT: Remove Objects Button Text
removeObjects=Noņemt objektus
#XMSG: No members have been added yet.
noMembersAssigned=Vēl nav pievienots neviens dalībnieks.
#XMSG: No users have been added yet.
noUsersAssigned=Vēl nav pievienots neviens lietotājs.
#XMSG: No database users have been created or your filter is not showing any data.
noDatabaseUsers=Nav izveidots neviens datu bāzes lietotājs, vai arī jūsu filtrs neuzrāda nekādus datus.
#XMSG: Please enter a user name.
noDatabaseUsername=Ievadiet lietotājvārdu.
#XMSG: The user name is too long. Please use a shorter one.
databaseUsernameTooLong=Lietotājvārds ir pārāk garš. Lūdzu, izmantojiet īsāku.
#XMSG: No privileges have been enabled and this database user will have limited functionality. Do you still want to continue?
noPrivilegesConfirmationMessage=Nav iespējotas nekādas privilēģijas, un šim datu bāzes lietotājam būs ierobežota funkcionalitāte. Vai joprojām vēlaties turpināt?
#XMSG: To enable audit logs for change operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogChangeMessage=Lai audita žurnāliem iepējotu izmaiņu darbības, ir jāiespējo arī datu uzņemšana. Vai vēlaties to darīt?
#XMSG: To enable audit logs for read operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogReadMessage=Lai audita žurnāliem iepējotu lasīšanas darbības, ir jāiespējo arī datu uzņemšana. Vai vēlaties to darīt?
#XMSG: To enable HDI consumption data ingestion and data consumption needs to be enabled. Do you want to enable all?
enableHDIConsumptionMessage=Lai iespējotu HDI patēriņu, ir jāiespējo arī datu uzņemšanas un datu patēriņš. Vai vēlaties to darīt?
#XMSG:
databaseUserPasswordText=Lai iestatītu savienojumu ar šo datu bāzes lietotāju, nokopējiet savu paroli. Gadījumā, ja aizmirsīsit savu paroli, jūs vienmēr varēsit pieprasīt jaunu.
#XTIT: Space detail section members title
detailsSectionMembers=Dalībnieki
#XMSG: New password set
newPasswordSet=Ir iestatīta jauna parole
#XFLD: Data Ingestion
dataIngestion=Datu uzņemšana
#XFLD: Data Consumption
dataConsumption=Datu patēriņš
#XFLD: Privileges
privileges=Privilēģijas
#XFLD: Enable Data ingestion
enableDataIngestion=Iespējot datu uzņemšanu
#XMSG: Log the read and change operations for data ingestion.
databaseUserAuditHint=Reģistrējiet lasīšanas un izmaiņu darbības datu uzņemšanai.
#XMSG: Make your space data available in your HDI containers.
databaseUserHDIHint=Padariet savus vietas datus pieejamus savos HDI konteineros.
#XFLD: Enable Data consumption
enableDataConsumption=Iespējot datu patēriņu
#XMSG: Allow other apps or tools to consume your space data.
enableDataConsumptionHint=Atļaujiet citām lietojumprogrammām vai rīkiem patērēt jūsu vietas datus.
#XMSG: To set up access via this schema user copy the credentials to your user-provided service. In case you can only copy the credentials without a password make sure to add the password later.
databaseUserHDIConsumptionHint=Lai iestatītu piekļuvi, izmantojot datu bāzes lietotāju, nokopējiet akreditācijas datus uz jūsu lietotāja nodrošināto pakalpojumu. Gadījumā, ja varat nokopēt tikai akreditācijas datus bez paroles, noteikti pievienojiet paroli vēlāk.
#XFLD: Data Flow Runtime Capacity label in space overview
dataFlowRuntimeCapacity=Datu plūsmas izpildlaika jauda ({0}:{1} stundas no {2} stundām)
#XFLD: Could not load Data Flow Runtime Capacity
dataFlowRuntimeCapacityError=Nevarēja ielādēt datu plūsmas izpildlaika jaudu
#XMSG: User can grant data consumption to other users.
databaseUserConsumptionWithGrantOptionHint=Lietotājs var piešķirt datu patēriņu citiem lietotājiem.
#XFLD: Enable data consumption with grant option
enableConsumptionWithGrantOption=Iespējot datu patēriņu ar piešķiršanas iespēju
#XMSG: To enable data consumption with grant option data consumption needs to be enabled. Do you want to enable both?
enableConsumptionWithGrantMessage=Lai iespējotu datu patēriņu ar piešķiršanas iespēju, ir jāiespējo datu patēriņš. Vai vēlaties iespējot abus?
#XFLD: Enable Automated Predictive Library (APL) and Predictive Analysis Library (PAL)
allowUsingScriptServerFunctionality=Iespējot automatizēto prognozējošo bibliotēku (APL) un prognozējošo analīzes bibliotēku (PAL)
#XMSG: User can use SAP HANA Cloud embedded machine learning functions.
allowUsingScriptServerFunctionalityHint=Lietotājs var izmantot SAP HANA Cloud iegultās mašīnmācīšanās funkcijas.
#XFLD: Password Policy
passwordPolicy=Paroles politika
#XMSG: Password Policy
passwordPolicyHint=Iespējojiet vai atspējojiet konfigurēto paroles politiku šeit.
#XFLD: Enable Password Policy
enablePasswordPolicy=Iespējot paroles politiku
#XMSG: Read Access to the Space Schema
readAccessTitle=Lasīšanas piekļuve vietas shēmai
#XMSG: read access hint
readAccessHint=Atļaut datu bāzes lietotājam savienot ārējos rīkus ar vietas shēmu un lasīt skatus, kas ir pakļauti patēriņam.
#XFLD: Space Schema
spaceSchema=Vietas shēma
#XFLD: Enable Read Access (SQL)
enableReadAccess=Iespējot lasīšanas piekļuvi (SQL)
#XMSG: Allow the user to grant read access to other users.
withGrantOptionHint=Atļaujiet lietotājam piešķirt citiem lietotājiem lasīšanas piekļuvi.
#XFLD: With Grant Option
withGrantOption=Ar piešķiršanas iespēju
#XMSG: Make your space data available in your HDI containers.
enableHDIConsumptionHint=Padariet savus vietas datus pieejamus savos HDI konteineros.
#XFLD: Enable HDI Consumption
enableHDIConsumption=Iespējot HDI patēriņu
#XMSG: Write Access to the User’s Open SQL Schema
writeAccessTitle=Rakstīšanas piekļuve lietotāja Open SQL shēmai
#XMSG: write access hint
writeAccessHint=Atļaujiet datu bāzes lietotājiem izveidot ārējo rīku savienojumu ar Open SQL shēmu, lai izveidotu datu entītijas un uzņemtu datus izmantošanai šajā vietā.
#XFLD: Open SQL Schema
openSQLSchema=Open SQL shēma
#XFLD: Enable Write Access (SQL DDL & DML)
enableWriteAccess=Iespējot rakstīšanas piekļuvi (SQL, DDL un DML)
#XMSG: audit hint
auditHint=Reģistrējiet lasīšanas un izmaiņu darbības Open SQL shēmā.
#XMSG: data consumption hint
dataConsumptionHint=Visus jaunos skatus vietā pēc noklusējuma padarīt pieejamus patēriņam. Modelētāji var ignorēt šo iestatījumu atsevišķiem skatiem, izmantojot skata izvades sānu panelī esošo slēdzi “Padarīt pieejamu patēriņam”.
#XFLD: Expose for Consumption by Default
exposeDataConsumption=Padarīt pieejamu patēriņam pēc noklusējuma
#XMSG: database users hint consumption hint
databaseUsersHint2New=Izveidojiet datu bāzes lietotājus, lai izveidotu ārējo rīku savienojumu ar SAP Datasphere. Iestatiet privilēģijas, lai ļautu lietotājiem lasīt vietas datus un izveidot datu entītijas (DDL) un uzņemt datus (DML) lietošanai vietā.
#XFLD: Read
read=Lasīt
#XFLD: Read (HDI)
readHDI=Lasīt (HDI)
#XFLD: Write
write=Rakstīt
#XMSG: HDI Containers Hint
HDIContainersHint2=Atļaujiet piekļuvi saviem SAP HANA Deployment Infrastructure (HDI) konteineriem savā vietā. Modelētāji var izmantot HDI artefaktus kā avotus skatiem, un HDI klienti var piekļūt jūsu vietas datiem.
#XMSG: Open info dialog
openDatabaseUserInfoDialog=Atvērt informācijas dialoglodziņu
#XMSG: Database user is locked. Open info dialog to unlock
openLockedDatabaseUserInfoDialog=Datu bāzes lietotājs ir bloķēts. Lai atbloķētu, atveriet dialoglodziņu
#XFLD: Table
table=Tabula
#XFLD: Space section partner configuration title
partnerConnectionConfigurationSectionTitle=Partneru savienojums
#XFLD: Space section partner configuration subtitle
partnerConnectionConfigurationSectionSubTitle=Partneru savienojuma konfigurācija
#XFLD: Space section partner configuration hint
partnerConnectionConfigurationSectionHint=Definējiet pats savu partnera savienojuma flīzīti, pievienojot savu iFrame URL un ikonu. Šī konfigurācija ir pieejama tikai šim nomniekam.
#XFLD: Table Name Field
partnerConnectionConfigurationName=Flīzītes nosaukums
#XFLD: Table iFrame URL field
partnerConnectionConfigurationiFrameURL=iFrame URL
#XFLD: Table iFrame Post Message Origin field
partnerConnectionConfigurationiFramePostMessageOrigin=iFrame publicēšanas ziņojuma izcelsme
#XFLD: Table icon/image name
partnerConnectionConfigurationIcon=Ikona
#XMSG: Table no result found
partnerConnectionConfigurationNotfound=Nevarēja atrast nekādu(as) partneru savienojuma konfigurāciju(as).
#XMSG: Partner Connection Confgurations are not available
partnerConnectionConfigurationNotAvailable=Partneru savienojumu konfigurācijas nevar parādīt, kad izpildlaiks ir nepieejams.
#XFLD: Dialog Create Partner Connection Configuration
partnerConnectionConfigurationDialogTitleAdd=Izveidot partneru savienojuma konfigurāciju
#XFLD: Dialog Create Partner Connection Configuration Upload Icon
partnerConnectionConfigurationButtonUloadPartnerIcon=Augšupielādes ikona
#XFLD: Dialog Create Partner Connection Configuration Select
partnerConnectionConfigurationSelectIcon=Atlasīt (maksimālais lielums 200 KB)
#XFLD: Dialog Create Partner Connection Configuration Enter a name
partnerConnectionConfigurationNamePlaceholder=Partnera flīzītes piemērs
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL
partnerConnectionConfigurationiFrameURLPlaceholder=http://www.example.com
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL Check
partnerConnectionConfigurationiFramePostMessageOriginPlaceholder=.example.com
#XFLD: Dialog Create Partner Connection Configuration Browse
partnerConnectionConfigurationBrowseButton=Pārlūkot
#XFLD: Create Partner Connection Configuration Msg
partnerConnectionConfigurationUploadMsg=Partneru savienojuma konfigurācija ir sekmīgi izveidota.
#XMSG: Error while deleting Partner Connection configuration(s)
partnerConnectionConfigurationDeleteError=Dzēšot partneru savienojuma konfigurāciju(as), radās kļūda.
#XMSG: Partner Connection Configuration has been successfully deleted
partnerConnectionConfigurationDeleteSuccess=Partneru savienojuma konfigurācija ir sekmīgi izdzēsta.
#XMSG: Error while retrieving Partner Connection Configurations
partnerConnectionConfigurationLoadError=Izgūstot partneru savienojuma konfigurāciju(as), radās kļūda.
#XMSG: Error fileSizeLimitExceeded
partnerConnectionConfigurationfileSizeLimitExceeded=Šo failu nevarēja augšupielādēt, jo tas pārsniedz maksimālo lielumu, kas ir 200 KB.
#XBUT: Create Partner Connection Configuration
partnerConnectionConfigurationButtonCreate=Izveidot partneru savienojuma konfigurāciju
#XBUT: Delete Partner Connection Configuration
partnerConnectionConfigurationButtonDelete=Dzēst partneru savienojuma konfigurāciju
#XMSG: Partner Tile Creation Failure
upsertPartnerTileFailed=Partnera flīzīti nevarēja izveidot.
#XMSG: Partner Tile Deletion Failure
deletePartnerTileFailed=Partnera flīzīti nevarēja izdzēst.
#XMSG: HANA cloud connector settings reset failure
resetHANASCCSettingsFailed=Klienta SAP HANA Cloud savienotāja iestatījumu atiestatīšana neizdevās
#XFLD: Workload Class
workloadClass=Darba slodzes klase
#XFLD: Workload Management
workloadManagement=Darba slodzes pārvaldība
#XFLD: Priority
workloadClassPriority=Prioritāte
#XMSG:
workloadManagementPriorityHint=Veidojot datu bāzes vaicājumu, varat norādīt šīs vietas prioritizāciju. Ievadiet vērtību no 1 (zemākā prioritāte) līdz 8 (augstākā prioritāte). Ja rodas situācija, kad vietas konkurē pieejamo pavedienu dēļ, vispirms tiek izpildītas vietas ar augstākajām prioritātēm, bet pēc tam - ar zemākajām.
#XMSG:
workloadClassPriorityHint=Varat norādīt vietas prioritāti ar vērtējumu no 0 (viszemākais) līdz 8 (visaugstākais). Vietas priekšraksti ar augstu prioritāti tiek izpildīti pirms citu vietu priekšrakstiem ar zemāku prioritāti. Noklusējuma prioritāte ir 5. Tā kā vērtība 9 ir rezervēta sistēmas darbībām, tā vietai nav pieejama.
#XFLD: Statement Limits
workloadclassStatementLimits=Priekšrakstu limiti
#XFLD: Workload Configuration
workloadConfiguration=Darba slodzes konfigurācija
#XMSG:
workloadClassStatementLimitsHint=Varat norādīt pavedienu maksimālo skaitu (vai procentuālo vērtību) un atmiņas GB skaitu, ko var patērēt priekšraksti, kas pašlaik tiek izpildīti šajā vietā. Varat ievadīt jebkādu skaitli vai procentuālo vērtību no 0 (bez ierobežojuma) un kopējo nomniekā pieejamo atmiņu un pavedienus. \n\n Ja norādāt pavedienu limitu, ņemiet vērā, ka tas var samazināt veiktspēju. \n\n Ja norādāt atmiņas limitu, Priekšraksti, kas sasniedz atmiņas limitu, netiek izpildīti.
#XMSG:
workloadClassStatementLimitsDescription=Noklusējuma konfigurācija piedāvā bagātīgus resursu limitus un vienlaikus novērš situāciju, kad kāda viena vieta pārslogo sistēmu.
#XMSG:
workloadClassStatementLimitCustomDescription=Varat iestatīt maks. kopējos pavedienu un atmiņas limitus, kādus priekšraksti var patērēt, vienlaikus darbojoties sistēmā.
#XMSG:
totalStatementThreadLimitHelpText=Ja ir iestatīts pārāk zems pavediena limits, tas var ietekmēt priekšraksta veiktspēju, bet pārāk lielas vērtības vai 0 var vietai atļaut patērēt visus pieejamos sistēmas pavedienus.
#XMSG:
totalStatementMemoryLimitHelpText=Ja ir iestatīta pārāk maza atmiņa, var rasties problēmas ar nepietiekamu atmiņu, bet pārāk lielas vērtības vai 0 var vietai atļaut patērēt visu pieejamo sistēmas atmiņu.
#XMSG:
totalStatementThreadLimitHelpTextRestricted=Ievadiet procentuālo daļu no 1% līdz 70% (vai līdzvērtīgu skaitli) kopējam nomniekā pieejamajam pavedienu skaitam. Ja tiks iestatīts pārāk zems pavedienu limits, tas var ietekmēt priekšraksta veiktspēju, savukārt pārāk augstas vērtības var ietekmēt priekšrakstu veiktspēju citās vietās.
#XMSG:
totalStatementThreadLimitHelpTextDynamic=Ievadiet procentuālo daļu no 1% līdz {0}% (vai līdzvērtīgu skaitli) kopējam nomniekā pieejamajam pavedienu skaitam. Ja tiks iestatīts pārāk zems pavedienu limits, tas var ietekmēt priekšraksta veiktspēju, savukārt pārāk augstas vērtības var ietekmēt priekšrakstu veiktspēju citās vietās.
#XMSG:
totalStatementMemoryLimitHelpTextNew=Ievadiet vērtību vai procentuālo daļu diapazonā no 0 (bez limita) līdz kopējam nomniekā pieejamajam atmiņas daudzumam. Ja tiks iestatīts pārāk zems atmiņas limits, tas var ietekmēt priekšraksta veiktspēju, savukārt pārāk augstas vērtības var ietekmēt priekšrakstu veiktspēju citās vietās.
#XFLD: Total Statement Thread Limit
totalStatementThreadLimit=Kopējo priekšrakstu pavedienu limits
#XFLD: Segmented button label
workloadclassPercentage=%
#XFLD: Segmented button label
workloadclassGigaByte=GB
#XFLD, 80: Segmented button label
workloadclassThreads=Pavedieni
#XFLD: Total Statement Memory Limit
totalStatementMemoryLimit=Kopējās priekšrakstu atmiņas limits
#YMSE: Error while loading customer hana info
loadCustomerHanaInfoError=Neizdevās ielādēt klienta SAP HANA informāciju.
#XMSG:
minimumLimitReached=Sasniegts minimālais limits.
#XMSG:
maximumLimitReached=Sasniegts maksimālais limits.
#XMSG: Name Taken for Technical Name
technical-name-taken=Jau pastāv savienojums ar tādu tehnisko nosaukumu, ko jau ievadījāt. Lūdzu, ievadiet citu nosaukumu.
#XMSG: Name Too long for Technical Name
technical-name-too-long=Tehniskais nosaukums, ko ievadījāt, pārsniedz 40 rakstzīmes. Lūdzu, ievadiet nosaukumu ar mazāk rakstzīmēm.
#XMSG: Technical name field empty
technical-name-field-empty=Lūdzu, ievadiet tehnisko nosaukumu.
#XMSG: Generic error when user enters invalid characters for a field
field-invalid-characters=Nosaukumam varat izmantot tikai burtus (a–z), ciparus (0–9) un pasvītrojuma zīmes (_).
#XMSG: Generic error when user enters invalid initial characters for a field
field-underscore-trim=Nosaukums, ko ievadāt, nevar sākties vai beigties ar pasvītrojumu (_).
#XFLD: Checkbox label for Enable Statement Limits
enableStatementLimits=Iespējot priekšrakstu limitus
#XFLD: Connection SubSection Settings Title in space details
connectionSettings=Iestatījumi
#XMSG: Connections tool hint in Space details section
connectionsToolHint=Lai izveidotu vai rediģētu savienojumus, atveriet lietojumprogrammu Savienojumi no sānu navigācijas joslas vai noklikšķiniet šeit:
#XBUT: Connection list Open Connection Tool Button
button-connectiontool=Atvērt Savienojumi
#XFLD: Not deployed label on space tile
notDeployedLabel=Vieta vēl nav izvietota.
#XFLD: Not deployed additional text on space tile
notDeployedText=Lūdzu, izvietojiet vietu.
#XFLD: Corrupt space label on space tile
corruptSpace=Kaut kas nogāja greizi.
#XFLD: Corrupt space additional text on space tile
corruptSpaceHint=Mēģiniet izvietot atkārtoti vai sazinieties ar atbalstu
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Audita žurnāla dati
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Administratīvie dati
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Citi dati
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Dati vietās
#XMSG: Unlock Space confirmation text
unlockSpaceConfirmation=Vai tiešām vēlaties atbloķēt vietu?
#XMSG: Lock Space confirmation text
lockSpaceConfirmation=Vai tiešām vēlaties bloķēt šo vietu?
#XFLD: Lock
lock=Bloķēt
#XFLD: Unlock
unlock=Atbloķēt
#XFLD: Locking
locking=Bloķēšana
#XMSG: Success message after locking space
lockSpaceSuccessMsg=Vieta bloķēta
#XMSG: Success message after unlocking space
unlockSpaceSuccessMsg=Vieta atbloķēta
#XMSG: Success message after locking spaces
lockSpacesSuccessMsg=Vietas bloķētas
#XMSG: Success message after unlocking spaces
unlockSpacesSuccessMsg=Vietas atbloķētas
#YMSE: Error while locking a space
lockSpaceError=Vietu nevar bloķēt.
#YMSE: Error while unlocking a space
unlockSpaceError=Vietu nevar atbloķēt.
#XTIT: popup title Warning
confirmationWarningTitle=Brīdinājums
#XMSG: Message strip message when a space was locked manually
manualLockedMessage=Šī vieta ir bloķēta manuāli.
#XMSG: Message strip message when a space was locked due to the amount of generated audit logs
auditLogLockedMessage=Šo vietu bloķēja sistēma, jo audita žurnāli patērē diskā daudz GB.
#XMSG: Message strip message when a space was locked because the assigned quota was exceeded.
quotaLockedMessage=Šo vietu bloķēja sistēma, jo tā pārsniedz tai atvēlēto atmiņas vai diska krātuves apjomu.
#XMSG: Unlock Spaces confirmation text
unlockSpacesConfirmation=Vai tiešām vēlaties atbloķēt atlasītās vietas?
#XMSG: Lock Spaces confirmation text
lockSpacesConfirmation=Vai tiešām vēlaties bloķēt atlasītās vietas?
#XTIT: Scoped role editor dialog title
scopedRoleEditorDialogTitle=Apjoma lomas redaktors
#XTIT: ECN Management title
ecnManagementTitle=Vietu un elastīgās aprēķināšanas mezglu pārvaldība
#XFLD: ECNs
ecns=Elastīgās aprēķināšanas mezgli
#XFLD: ECN phase Ready
ecnReady=Gatavs
#XFLD: ECN phase Running
ecnRunning=Tiek izpildīts
#XFLD: ECN phase Initial
ecnInitial=Nav gatavs
#XFLD: ECN phase Starting
ecnStarting=Sākšana
#XFLD: ECN phase Starting Failed
ecnStartingFailed=Sākšana neizdevās
#XFLD: ECN phase Stopping
ecnStopping=Tiek apturēts
#XFLD: ECN phase Stopping Failed
ecnStoppingFailed=Apturēšana neizdevās
#XBTN: Assign Button
assign=Piešķirt vietas
#XBTN: Start Header-Button
start=Sākt
#XBTN: Update Header-Button
repair=Atjaunināt
#XBTN: Stop Header-Button
stop=Pārtraukt
#XFLD: ECN hours remaining
ecnHoursRemaining=Atlikušas 1000 stundas
#XFLD: ECN block-hours remaining
ecnBlockHoursRemaining={0} blokstundas atlikušas
#XFLD: ECN block-hour remaining
ecnBlockHourRemaining={0} blokstunda atlikusi
#XTIT: ECN creation dialog title
ecnCreationDialogTitle=Izveidot elastīgās aprēķināšanas mezglu
#XTIT: ECN edit dialog title
ecnEditDialogTitle=Elastīgās aprēķināšanas mezgla rediģēšana
#XTIT: ECN deletion dialog title
ecnDeletionDialogTitle=Dzēst elastīgās aprēķināšanas mezglu
#XTIT: ECN Space assignment dialog title
ecnSpaceAssignmentDialogTitle=Piešķirt vietas
#XFLD: ECN ID
ECNIDLabel=Elastīgās aprēķināšanas mezgls
#XTXT: Selected toolbar text
selectedToolbarText=Atlasīts: {0}
#XTIT: Elastic Compute Nodes
ECNslong=Elastīgās aprēķināšanas mezgli
#XTIT: Object assignment - Table header: Objects available
objectsAvailable=Objektu skaits
#XTIT: Object assignment - Dialog header text
selectObjects=Atlasiet vietas un objektus, kurus vēlaties piešķirt savam elastīgās aprēķināšanas mezglam:
#XTIT: Object assignment - Table header title: Objects
objects=Objekti
#XTIT: Object assignment - Table header: Type
type=Tips
#XMSG: Message strip delete database user audit warning
deleteDatabaseUserWarningMessageStrip=Ņemiet vērā, ka datu bāzes lietotāja dzēšana izraisīs visu ģenerēto audita žurnāla ierakstu dzēšanu. Ja vēlaties saglabāt audita žurnāla ierakstus, apsveriet to eksportēšanu, pirms dzēšat datu bāzes lietotāju.
#XMSG: Message strip delete HDI container audit logs impact
deleteHDIContainerWarningMessageStrip=Ņemiet vērā, ka HDI konteinera piešķires vietai atcelšana izraisīs visu ģenerēto audita žurnāla ierakstu dzēšanu. Ja vēlaties saglabāt audita žurnālus, apsveriet to eksportēšanu, pirms atceļat HDI konteinera piešķiri.
#XTXT: All audit logs
allAuditLogs=Visi vietai ģenerētie audita žurnāla ieraksti
#XMSG: Message strip disable audit warning
disableAuditWarningMessageStrip=Ņemiet vērā, ka audita politikas (lasīšanas un izmaiņu darbību) atspējošana izraisīs visu tās audita žurnāla ierakstu dzēšanu. Ja vēlaties saglabāt audita žurnāla ierakstus, apsveriet to eksportēšanu, pirms atspējojat audita politiku.
#XTIT: No Spaces assigned to ECN Illustration title
noSpacesECNTitle=Neviena atstarpe vai objekti vēl nav piesaistīti.
#XTIT: No Spaces assigned to ECN Illustration description
noSpacesECNDescription=Lai sāktu darbu ar jūsu elastīgo skaitļošanas mezglu, piešķiriet tam vietu un objektus.
#XTIT: No Spaces Illustration title
noSpacesTitle=Vēl nav izveidota neviena vieta
#XTIT: No Spaces Illustration description
noSpacesDescription=Lai sāktu datu iegūšanu, izveidojiet vietu.
#XTIT: No Spaces Illustration title
noDeletedSpacesTitle=Atkritne ir tukša
#XTIT: No Spaces Illustration description
noDeletedSpacesDescription=Savas izdzēstās vietas varat atgūt no šejienes.
#XTXT:  {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDatabaseUsers=Kad vieta tiks izvietota, tālāk norādītie datu bāzes lietotāji tiks {0} izdzēsti, un tos nevarēs atkopt:
#XTIT: Delete database users
deleteDatabaseUsersTitle=Dzēst datu bāzes lietotājus
#YMSE: Error if ecn id already exists
ecnIDInputDuplicateError=ID jau pastāv.
#YMSE: Error if ecn id contains invalid characters
ecnIDInputInvalidError=Lūdzu, izmantojiet tikai mazos burtus a–z un ciparus 0–9
#YMSE: Error if ecn id is too short
ecnIDInputTooShortError=Identifikatoram ir jābūt vismaz {0} rakstzīmju garam.
#XMSG: ecn id length warning
ecnIdLengthWarning=Pārsniegts maksimums – {0} rakstzīmes.
#XFLD: open System Monitor
systemMonitor=Sistēmas pārraugs
#XFLD: open ECN schedule dialog menu entry
schedule=Grafiks
#XFLD: open create ECN schedule dialog
createSchedule=Izveidot grafiku
#XFLD: open change ECN schedule dialog
changeSchedule=Rediģēt grafiku
#XFLD: open delete ECN schedule dialog
deleteSchedule=Dzēst grafiku
#XFLD: open self assign ECN schedule dialog
selfAssignSchedule=Piešķirt grafiku man
#XFLD: open pause ECN schedule dialog
pauseSchedule=Aizturēt grafiku
#XFLD: open resume ECN schedule dialog
resumeSchedule=Turpināt grafiku
#XFLD: View Logs
viewLogs=Skatīt žurnālus
#XFLD: Compute Blocks
computeBlocks=Skaitļošanas bloki
#XFLD: Memory label in ECN creation dialog
ecnMemory=Atmiņa (GB)
#XFLD: Storage label in ECN creation dialog
ecnStorage=Krātuve (GB)
#XFLD: Number of CPU label in ECN creation dialog
ecnCPUCount=CP skaits
#XFLD: ECN updated by label
changedBy=Mainīja
#XFLD: ECN updated on label
changedOn=Mainīšanas datums
#XMSG: Success message after creating a Elastic Compute Node
createEcnSuccessMessage=Izveidots elastīgās aprēķināšanas mezgls
#YMSE: Error while creating a Elastic Compute Node
createEcnError=Nevarēja izveidot elastīgās aprēķināšanas mezglu
#XMSG: Success message after updating a Elastic Compute Node
updateEcnSuccessMessage=Elastīgās aprēķināšanas mezgls atjaunināts
#XMSG: Error while updating a Elastic Compute Node
udpateEcnError=Elastīgās aprēķināšanas mezglu nevarēja atjaunināt
#XMSG: Success message after deleting a Elastic Compute Node
deleteEcnSuccessMessage=Izdzēsts elastīgās aprēķināšanas mezgls
#YMSE: Error while deleting a Elastic Compute Node
deleteEcnError=Nevarēja izdzēst elastīgās aprēķināšanas mezglu
#XMSG: Success message after starting a Elastic Compute Node
startingEcnSuccessMessage=Elastīgās aprēķināšanas mezgla startēšana
#XMSG: Success message after stopping a Elastic Compute Node
stoppingEcnSuccessMessage=Elastīgās aprēķināšanas mezgla apturēšana
#YMSE: Error while starting a Elastic Compute Node
startingEcnError=Nevarēja startēt elastīgās aprēķināšanas mezglu
#YMSE: Error while stopping a Elastic Compute Node
stoppingEcnError=Nevarēja apturēt elastīgās aprēķināšanas mezglu
#XBUT: Add Object button for an ECN
assignObjects=Pievienot objektus
#XFLD: Enable auto object assignment label
enableAutoObjAssignment=Piešķirt visus objektus automātiski
#XFLD: object type label to be assigned
objectTypeLabel=Tips (semantiskais lietojums)
#XFLD: assigned object type label
assignedObjectTypeLabel=Tips
#XFLD: technical name label
TechnicalNameLabel=Tehniskais nosaukums
#XFLD: Object selecttion ECN label
objectSelectionECNLabel=Atlasiet objektus, kurus vēlaties pievienot elastīgās aprēķināšanas mezglam
#XTIT: Add objects dialog title
assignObjectsTitle=Piešķirt objektus no:
#XFLD: object label with object count
objectLabel=Objekts
#XMSG: No objects available to add message.
noObjectsToAssign=Nav pieejamu objektu, ko piešķirt.
#XMSG: No objects assigned message.
noAssignedObjects=Nav piešķirts neviens objekts.
#XTIT: Remove objects popup title
removeObjectsOrSpacesConfirmationTitle=Brīdinājums
#XTIT: Delete ECN popup button and title
deleteECNConfirmationButtonAndTitle=Dzēst
#XMSG: Remove objects popup text
removeObjectsConfirmation=Vai tiešām vēlaties noņemt atlasītos objektus?
#XMSG: Remove spaces popup text
removeSpacesConfirmation=Vai tiešām vēlaties noņemt atlasītās vietas?
#XBUT: Confirmation button for objects dialog
removeObjectsOrSpacesConfirmationBtn=Noņemt vietas
#XMSG: Success message after removing objects
removeObjectsSuccessMessage=Atklātie objekti ir noņemti
#XMSG: Success message after assigning objects
assignObjectsSuccessMessage=Atklātie objekti ir piešķirti
#XFLD: auto object assignment label for space tile
autoObjAssignmentLabel=Visi atklātie objekti
#XFLD: Spaces tab label
spacesTabLabel=Vietas
#XFLD: Exposed objects tab label
consumptionObjectsTabLabel=Atklātie objekti
#XMSG: Success message after unassigning spaces
spacesUnassignedSuccessMessage=Vietas ir noņemtas
#XMSG: Success message after unassigning space
spaceUnassignedSuccessMessage=Vieta ir noņemta
#YMSE: Error while assigning / unassigning spaces
spacesAssignUnassignError=Vietas nevarēja piešķirt vai noņemt.
#YMSE: Error while removing objects
removeObjectsError=Nevarējām piešķirt vai noņemt objektus.
#YMSE: Error while removing object
removeObjectError=Nevarējām piešķirt vai noņemt objektu.
#YMSE: Error when saving the ecn with invalid value
invalidSizingPlanError=Iepriekš atlasītais skaits vairs nav derīgs. Lūdzu, atlasiet derīgu skaitu.
#YMSE: Error when saving the ecn with invalid value
invalidPerformanceClassError=Lūdzu, atlasiet derīgu veiktspējas klasi.
#YMSE: Error when saving the ecn with invalid value
performanceClassNotAvailableError=Iepriekš atlasītā veiktspējas klase "{0}" pašlaik nav derīga. Lūdzu, atlasiet derīgu veiktspējas klasi.
#XTXT: Delete ECN confirmation text
deleteConfirmationTxtECN=Vai tiešām vēlaties dzēst elastīgās aprēķināšanas mezglu?
#XFLD: tooltip for ? button
help=Palīdzība
#XFLD: ECN edit button label
editECN=Kobfigurēt
#XFLD: Technical type label for ERModel
DWC_ERMODEL=Entītija - attiecību modelis
#XFLD: Technical type label for Local Table
DWC_LOCAL_TABLE=Lokālā tabula
#XFLD: Technical type label for Remote Table
DWC_REMOTE_TABLE=Attālā tabula
#XFLD: Technical type label for Analytic Model
DWC_ANALYTIC_MODEL=Analītiskais modelis
#XFLD: Technical type label for Task Chain
DWC_TASKCHAIN=Uzdevumu ķēde
#XFLD: Technical type label for Data Flow
DWC_DATAFLOW=Datu plūsma
#XFLD: Technical type label for Replication Flow
DWC_REPLICATIONFLOW=Replicēšanas plūsma
#XFLD: Technical type label for Transformation Flow
DWC_TRANSFORMATIONFLOW=Transformācijas plūsma
#XFLD: Technical type label for Intelligent Lookup
DWC_IDT=Intelektiskā uzmeklēšana
#XFLD: Technical type label for Repository
DWC_REPOSITORY_PACKAGE=Repozitorijs
#XFLD: Technical type label for Enterprise Search
DWC_ESMODEL=Uzņēmuma meklēšana
#XFLD: Technical type label for View
DWC_VIEW=Skats
#XFLD: Technical type label for Data Product
DWC_DATAPRODUCT=Datu produkts
#XFLD: Technical type label for Data Access Control
DWC_DAC=Datu piekļuves vadīkla
#XFLD: Technical type label for Folder
DWC_FOLDER=Mape
#XFLD: Technical type label for Data Entity
DWC_BUSINESS_ENTITY=Saimnieciskā vienība
#XFLD: Technical type label for Business Entity Variant
DWC_BUSINESS_ENTITY_VARIANT=Saimnieciskās vienības variants
#XFLD: Technical type label for Responsibility Scenario
DWC_AUTH_SCENARIO=Atbildības scenārijs
#XFLD: Technical type label for Fact Model
DWC_FACT_MODEL=Faktu modelis
#XFLD: Technical type label for Transformation Flow
DWC_PERSPECTIVE=Perspektīva
#XFLD: Technical type label for Consumption Model
DWC_CONSUMPTION_MODEL=Patēriņa modelis
#XFLD: Technical type label for Remote Connection
DWC_REMOTECONNECTION=Attālais savienojums
#XFLD: Technical type label for Fact Model Variant
DWC_FACT_MODEL_VARIANT=Faktu modeļa variants
#XMSG: Schedule created alert message
createScheduleSuccess=Grafiks izveidots
#XMSG: Schedule updated alert message
updateScheduleSuccess=Grafiks atjaunināts
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Grafiks izdzēsts
#XMSG: Schedule takeover success message
ownerChangeScheduleSuccess=Grafiks piešķirts jums
#XMSG: Message for starting pausing of schedule
pauseScheduleStarted=1 grafika pauzēšana
#XMSG: Message for starting resuming of schedule
resumeScheduleStarted=1 grafika atsākšana
#XFLD: Segmented button label
availableSpacesButton=Pieejams
#XFLD: Segmented button label
selectedSpacesButton=Atlasīts
#XFLD: Visit website button text
visitWebsite=Apmeklēt tīmekļa vietni
#XMSG: Warning message on unselecting source language
sourceLanguageRemovalWarning=Iepriekš atlasītā avota valoda tiks noņemta.
#XBUT: enable translation confirmation button
enableTranslationConfirmationBtn=Iespējot
#XFLD: ECN performance class label
performanceClassLabel=Veiktspējas klase
#XTXT performance class memory text
memoryText=Atmiņa
#XTXT performance class compute text
computeText=Skaitļošana
#XTXT performance class high-compute text
highComputeText=Augsta skaitļošana
#XBUT: Recycle Bin Button Text
recycleBin=Atkritne
#XBUT: Restore Button Text
restore=Atjaunot
#XMSG: Warning message for new Workload Management UI
priorityWarning=Šis apgabals ir tikai lasāms. Vietas prioritāti mainīt varat apgabalā Sistēma / Konfigurācija / Darba slodzes pārvaldība.
#XMSG: Warning message for new Workload Management UI
workloadWarning=Šis apgabals ir tikai lasāms. Vietas darba slodzes konfigurāciju mainīt varat apgabalā Sistēma / Konfigurācija / Darba slodzes pārvaldība.
#XCOL: Space table-view column: Apache Spark vCPUs
sparkManagementVCPUs=Apache Spark vCPU
#XCOL: Space table-view column: Apache Spark Memory (GB)
sparkManagementMemory=Apache Spark atmiņa (GB)
#XCOL: Space type (from Space Capability) for Data Ingestion Spaces
ingestionSpaceType=Datu produkta uzņemšana
#XFLD: Deployment in progress nodatatext for spark table
sparkTableDeploymentInProgress=Nekādi dati nav pieejami, jo vieta pašlaik tiek izvietota
#XFLD: Runtime data being loaded nodatatext for spark table
sparkTableDeploymentLoading=Nekādi dati nav pieejami, jo vieta pašlaik tiek ielādēta
#XCOL: Space type (from Space Capability) for BW Private Cloud Edition
BWPCESpaceType=SAP BW
#XBUT: Go to Instance Mappings Button
navToMappingsButton=Rediģēt instanču kartējumus
#XFLD: SAP HANA Data Lake Storage label if less than 1GB
dataLakeStorageUsed=< 1 GB
