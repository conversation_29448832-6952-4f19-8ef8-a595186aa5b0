#DO NOT REMOVE________________________
#XFLD: Breadcrumb for space monitoring page
spaceMonitorBreadcrumb=Supervisió
#XTXT: Type name for spaces in browser tab page title
space=Espai
#_____________________________________
#XFLD: Spaces label in
spaces=Espais
#XFLD: Manage plan button text
manageQuotaButtonText=Gestionar pla
#XBUT: Manage resources button
manageResourcesButton=Gestionar recursos
#XFLD: Create space button tooltip
createSpace=Crear espai
#XFLD: Create
create=Crear
#XFLD: Deploy
deploy=Desplegar
#XFLD: Page
page=Pàgina
#XFLD: Cancel
cancel=Cancel·lar
#XFLD: Update
update=Actualitzar
#XFLD: Save
save=Desar
#XFLD: OK
ok=D'acord
#XFLD: days
days=Dies
#XFLD: Space tile edit button label
edit=Editar
#XFLD: Auto Assign all objects to space
autoAssign=Assignar automàticament
#XFLD: Space tile open monitoring button label
openMonitoring=Supervisar
#XFLD: Delete
delete=Suprimir
#XFLD: Copy Space
copy=Copiar
#XFLD: Close
close=Tancar
#XCOL: Space table-view column status
status=Estat
#XFLD: Space status active
activeLabel=Actiu
#XFLD: Space status locked
lockedLabel=Bloquejat
#XFLD: Space status critical
criticalLabel=Crític
#XFLD: Space status cold
coldLabel=Inactiu
#XFLD: Space status deleted
deletedLabel=Suprimit
#XFLD: Space status unknown
unknownLabel=Desconegut
#XFLD: Space status ok
okLabel=Correcte
#XFLD: Database user expired
expired=Vençut
#XFLD: deployed
deployed=Desplegat
#XFLD: not deployed
notDeployed=No desplegat
#XFLD: changes to deploy
changesToDeploy=Modificacions per desplegar
#XFLD: pending
pending=Desplegant
#XFLD: designtime error
designtimeError=Error de temps de disseny
#XFLD: runtime error
runtimeError=Error de temps d'execució
#XFLD: Space created by label
createdBy=Autor
#XFLD: Space created on label
createdOn=Data de creació
#XFLD: Space deployed on label
deployedOn=Desplegat el
#XFLD: Space ID label
spaceID=ID d’espai
#XFLD: Priority label
priority=Prioritat
#XFLD: Space Priority label
spacePriority=Prioritat d’espai
#XFLD: Space Configuration label
spaceConfiguration=Configuració d'espai
#XFLD: Not available
notAvailable=No disponible
#XFLD: WorkloadType default
default=Estàndard
#XFLD: WorkloadType custom
custom=Personalitzat
#XFLD: Data Lake option label; refers to component SAP HANA Cloud, Data Lake
dataLakeAccess=Accés a llac de dades
#XFLD: Translation label
translationLabel=Traducció
#XFLD: Source language label
sourceLanguageLabel=Idioma d'origen
#XFLD: Translation CheckBox label
translationCheckBox=Habilitar traducció
#XTXT: Popover hint that space has to be deployed to show user details
openDatabaseUserHintPopoverText=Desplegueu l’espai per accedir als detalls d’usuari.
#XTXT: Popover hint that space has to be deployed to open database explorer
openDatabaseExplorerHintPopoverText=Desplegueu l’espai per obrir l’Explorador de bases de dades.
#XTXT: Text for when the Data Lake check box is disabled
dataLakeOptionDisabled=No podeu utilitzar aquest espai per accedir al data lake perquè ja l'utilitza un altre espai.
#XFLD: Data Lake check box text; refers to component SAP HANA Cloud, Data Lake
dataLakeCheckBox=Utilitzeu aquest espai per accedir al llac de dades.
#XFLD: Space Priority minimum label extension
low=Baixa
#XFLD: Space Priority maximum label extension
high=Alta
#XFLD: Space name label
spaceName=Nom d’espai
#XFLD: Enable deploy objects checkbox
enableDeployObjects=Desplegar objectes
#XTIT: Copy spaces dialog title
copySpaceDialogTitle=Copiar {0}
#XTXT Human readable text for unselected source language
unSelectedSourceLanguage=(No seleccionat)
#XTXT Human readable text for language code "af"
af=Afrikaans
#XTXT Human readable text for language code "ar"
ar=Àrab
#XTXT Human readable text for language code "bg"
bg=búlgar
#XTXT Human readable text for language code "ca"
ca=català
#XTXT Human readable text for language code "zh"
zh=Xinès simplificat
#XTXT Human readable text for language code "zf"
zf=Xinès
#XTXT Human readable text for language code "hr"
hr=croat
#XTXT Human readable text for language code "cs"
cs=txec
#XTXT Human readable text for language code "cy"
cy=gal·lès
#XTXT Human readable text for language code "da"
da=danès
#XTXT Human readable text for language code "nl"
nl=neerlandès
#XTXT Human readable text for language code "en-UK"
en-UK=Anglès (Regne Unit)
#XTXT Human readable text for language code "en"
en=Anglès (Estats Units)
#XTXT Human readable text for language code "et"
et=Estonià
#XTXT Human readable text for language code "fa"
fa=Persa
#XTXT Human readable text for language code "fi"
fi=finès
#XTXT Human readable text for language code "fr-CA"
fr-CA=francès (Canadà)
#XTXT Human readable text for language code "fr"
fr=francès
#XTXT Human readable text for language code "de"
de=alemany
#XTXT Human readable text for language code "el"
el=grec
#XTXT Human readable text for language code "he"
he=Hebreu
#XTXT Human readable text for language code "hi"
hi=hindi
#XTXT Human readable text for language code "hu"
hu=hongarès
#XTXT Human readable text for language code "is"
is=Islandès
#XTXT Human readable text for language code "id"
id=malai
#XTXT Human readable text for language code "it"
it=italià
#XTXT Human readable text for language code "ja"
ja=japonès
#XTXT Human readable text for language code "kk"
kk=Kazakh
#XTXT Human readable text for language code "ko"
ko=coreà
#XTXT Human readable text for language code "lv"
lv=Letó
#XTXT Human readable text for language code "lt"
lt=lituà
#XTXT Human readable text for language code "ms"
ms=Bahasa Melayu
#XTXT Human readable text for language code "no"
no=noruec
#XTXT Human readable text for language code "pl"
pl=polonès
#XTXT Human readable text for language code "pt"
pt=Portuguès (Brasil)
#XTXT Human readable text for language code "pt-PT"
pt-PT=portuguès (Portugal)
#XTXT Human readable text for language code "ro"
ro=romanès
#XTXT Human readable text for language code "ru"
ru=rus
#XTXT Human readable text for language code "sr"
sr=Serbi
#XTXT Human readable text for language code "sh"
sh=Serbocroat
#XTXT Human readable text for language code "sk"
sk=eslovac
#XTXT Human readable text for language code "sl"
sl=eslovè
#XTXT Human readable text for language code "es"
es=espanyol
#XTXT Human readable text for language code "es-MX"
es-MX=espanyol (Mèxic)
#XTXT Human readable text for language code "sv"
sv=suec
#XTXT Human readable text for language code "th"
th=tai
#XTXT Human readable text for language code "tr"
tr=turc
#XTXT Human readable text for language code "uk"
uk=ucraïnès
#XTXT Human readable text for language code "vi"
vi=vietnamita
#XTIT: Delete spaces confirmation title
deleteConfirmationHeaderText=Suprimir espais
#XTXT: Logically delete space delete confirmation text with space name
logicallyDeleteConfirmationBodyTextWithSpaceName=Segur que voleu moure l''espai "{0}" a la paperera de reciclatge?
#XTXT: Logically dpace delete confirmation text for multiple spaces
logicallyDeleteConfirmationBodyTextMultiple=Segur que voleu moure els {0} espais seleccionats a la paperera de reciclatge?
#XTXT: Delete space delete confirmation text with space name
deleteConfirmationBodyTextWithSpaceName=Segur que voleu suprimir l’espai "{0}"? Aquesta acció no es pot desfer.
#XTXT: Delete space delete confirmation text for multiple spaces
deleteConfirmationBodyTextMultiple=Segur que voleu suprimir els {0} espais seleccionats? Aquesta acció no es pot desfer. Se suprimirà el contingut següent {1}:
#XTXT: permanently
permanently=de forma permanent
#XTXT: {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDetail=Se suprimirà {0} el contingut següent i no es podrà recuperar:
#XTXT: Hint Delete space confirmation text
deleteConfirmationHint=Escriviu {0} per confirmar la supressió.
#XTXT: Delete space confirmation error check spelling
deleteConfirmationErrorCheckSpelling=Verifiqueu l’ortografia i torneu a provar-ho.
#XTXT: All Spaces
allSpaces=Tots els espais
#XTXT: All data
allData=Tots els objectes i les dades inclosos a l'espai
#XTXT: All connections
allConnections=Totes les connexions definides a l'espai
#XFLD: Space tile selection box tooltip
clickToSelect=Feu clic per seleccionar
#XTXT: All database users
allDatabaseUsers=Tots els objectes i les dades inclosos a qualsevol esquema Open SQL associat a l'espai
#XFLD: remove members button tooltip
deleteUsers=Eliminar membres
#XTXT: Space long description text
description=Descripció (4.000 caràcters màxim)
#XFLD: Add Members button tooltip
addUsers=Afegir membres
#XFLD: Add Users button tooltip
addUsersTooltip=Afegir usuaris
#XFLD: Edit Users button tooltip
editUsersTooltip=Editar usuaris
#XFLD: Remove Users button tooltip
removeUsersTooltip=Eliminar usuaris
#XFLD: Searchfield placeholder
filter=Cercar
#XCOL: Users table-view column health
health=Salut
#XCOL: Users table-view column access
access=Accés
#XFLD: No user found nodatatext
noDataText=No existeix cap usuari
#XTIT: Members dialog title
selectUserDialogTitle=Afegir membres
#XTIT: User dialog title
addUserDialogTitle=Afegir usuaris
#XTIT: Delete connections confirmation title
deleteConnectionConfirmationHeaderText=Suprimir connexions
#XTIT: Delete single connection confirmation title
deleteConnectionConfirmationHeaderTextSingle=Suprimir connexió
#XTXT: Delete connection delete confirmation text
deleteConnectionConfirmationBodyText=Segur que voleu suprimir les connexions seleccionades? S’eliminaran per sempre.
#XTIT: Remote source dialog title
selectRemoteConnectionDialogTitle=Seleccionar connexions
#XTIT: Share connection dialog title
connectionSharingDialogTitle=Compartir connexió
#XTIT: Connections shared from other spaces table title
sharedConnectionsFromOtherSpaces=Connexions compartides
#XFLD: Add remote source button tooltip
addRemoteConnections=Afegir connexions
#XFLD: Delete remote source button tooltip
deleteRemoteConnections=Eliminar connexions
#XFLD: Share remote source button tooltip
shareConnections=Compartir connexions
#XFLD: Tile-layout tooltip
tileLayout=Disposició de mosaic
#XFLD: Table-layout tooltip
tableLayout=Disposició de taula
#XMSG: Success message after creating space
createSpaceSuccessMessage=Espai creat
#XMSG: Success message after copying space
copySpaceSuccessMessage=Còpia d''espai "{0}" a l''espai "{1}"
#XMSG: Success message after deploying space
deploymentSuccessMessage=S’ha iniciat el desplegament de l’espai
#XMSG: Success message after Apache Spark update started
sparkUpdateStartedSuccessMessage=S'ha iniciat l'actualització d'Apache Spark
#YMSE: Error while starting Apache Spark update
sparkUpdateStartedErrorMessage=Error en actualitzar Apache Spark
#XMSG: Success message after updating space
updateSettingsSuccessMessage=Detalls d'espai actualitzats
#XMSG: Success message after unlocking space
unlockSpaceSuccessMessage=Espai desbloquejat temporalment
#XMSG: Success message after deleting a single space
deleteSpaceSuccessMessage=Espai suprimit
#XMSG: Success message after deleting multiple spaces
deleteSpacesSuccessMessage=Espais suprimits
#XMSG: Success message after restoring a single space
restoreSpaceSuccessMessage=Espai restablert
#XMSG: Success message after restoring multiple spaces
restoreSpacesSuccessMessage=Espais restablerts
#YMSE: Error while updating settings
updateSettingsFailureMessage=No s’han pogut actualitzar les opcions d’espai.
#YMSE: Error while updating data lake option
dataLakeEnabledInOtherSpace=El llac de dades ja està assignat a un altre espai. Només un espai pot accedir al llac de dades cada vegada.
#YMSE: Error while updating data lake option
virtualTablesExists=No podeu anul·lar l'assignació del llac de dades d'aquest espai perquè encara hi ha dependències a taules virtuals*. Suprimiu les taules virtuals per anul·lar l'assignació del llac de dades d'aquest espai.
#YMSE: Error while unlocking space
unlockSpaceFailureMessage=No s’ha pogut desbloquejar l’espai.
#YMSE: Error while creating space
createSpaceError=No s’ha pogut crear l’espai.
#YMSE: Error while creating duplicate space
createDuplicateSpaceError=Ja existeix un espai amb el nom {0}.
#YMSE: Error while deleting a single space
deleteSpaceError=No s’ha pogut suprimir l’espai.
#YMSE: Error details while deleting a single space
deleteSpaceDetailedError=El vostre espai “{0}” ja no funciona correctament. Proveu de suprimir-lo un altre cop. Si no us en sortiu, demaneu a l''administrador que el suprimeixi o que obri un tiquet.
#YMSE: Error while deleting a single space in files
deleteSpaceInFilesError=Les dades d'espai de Fitxers no s’han pogut suprimir.
#YMSE: Error while deleting a single space users
deleteSpaceUsersError=Els usuaris no s’han pogut eliminar.
#YMSE: Error while deleting a single space schema
deleteSpaceSchemasError=Els esquemes no s’han pogut eliminar.
#YMSE: Error while deleting a single space connections
deleteSpaceConnectionsError=Les connexions no s’han pogut eliminar.
#YMSE: Error while deleting a single space data
deleteSpaceDataError=Les dades d’espai no s’ha pogut suprimir.
#YMSE: Error while deleting multiple spaces
deleteSpacesError=Els espais no s’han pogut suprimir.
#YMSE: Error while restoring a single space
restoreSpaceError=No s’ha pogut restablir l’espai.
#YMSE: Error while restoring multiple spaces
restoreSpacesError=No s’han pogut restablir els espais.
#YMSE: Error while creating users
createUsersError=Els usuaris no s’han pogut afegir.
#YMSE: Error while removing users
removeUsersError=No hem pogut eliminar els usuaris.
#YMSE: Error while removing user
removeUserError=No s'ha pogut eliminar l'usuari.
#YMSE: Error assigning roles to a user
roleAssignUnAssignError=No hem pogut afegir l'usuari al rol amb abast definit seleccionat. \n\n No us podeu afegir a un rol amb abast definit. Podeu demanar a l'administrador que us afegeixi a un rol amb abast definit.
#YMSE: Error assigning user to the space
userAssignError=No hem pogut assignar l'usuari a l'espai. \n\n L'usuari ja està assignat al nombre màxim permès (100) d'espais entre rols amb abast definit.
#YMSE: Error assigning users to the space
usersAssignError=No hem pogut assignar els usuaris a l'espai. \n\n L'usuari ja està assignat al nombre màxim permès (100) d'espais entre rols amb abast definit.
#YMSE: Error while fetching users
fetchUserWithScopedRolesError=No hem pogut recuperar els usuaris. Intenteu-ho més tard.
#YMSE: Error while fetching scoped roles
fetchScopedRolesError=No hem pogut recuperar els rols amb abast.
#YMSE: Error while fetching members
fetchUserError=Els membres no s’han pogut obtenir. Torneu a provar-ho més tard.
#YMSE: Error while loading run-time database
loadRuntimeError=No s'ha pogut carregar la informació de la base de dades en temps d'execució.
#YMSE: Error while loading spaces
loadSpacesError=Alguna cosa ha anat malament en intentar recuperar els vostres espais.
#YMSE: Error while loading haas resources
loadStorageError=Alguna cosa ha anat malament en intentar recuperar les dades de magatzem.
#YMSE: Error no data could be loaded
loadDataError=Alguna cosa ha anat malament en intentar recuperar les vostres dades.
#XFLD: Click to refresh storage data
clickToRefresh=Feu clic aquí per intentar-ho de nou.
#XTIT: Delete space popup title
deleteSpacePopupTitle=Suprimir espai
#XCOL: Spaces table-view column name
name=Nom
#XCOL: Spaces table-view deployment status
deploymentStatus=Estat de desplegament
#XFLD: Disk label in space details
storageLabel=Disc (GB)
#XFLD: In-Memory label in space details
ramLabel=Memòria (GB)
#XFLD: Memory label on space card
memory=Memòria per a l'emmagatzematge
#XFLD: Storage Assignment label in space details
storrageAssignmentLabel=Espai d'emmagatzematge
#XFLD: Storage Type label in space details
storageTypeLabel=Tipus d’emmagatzematge
#XFLD: Enable Space Quota
enableSpaceQuota=Activar quota d'espai
#XFLD: No Space Quota
noSpaceQuota=Sense quota d'espai
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
diskAndInMemoryText=SAP HANA Database (Disk i In-Memory)
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
dataLakeFilesText=Fitxers de SAP HANA Data Lake
#XFLD: Available scoped roles label
availableRoles=Funcions disponibles
#XFLD: Selected scoped roles label
selectedRoles=Funcions seleccionades
#XCOL: Spaces table-view column models
models=Models
#XCOL: Spaces table-view column users
users=Usuaris
#XCOL: Spaces table-view column connections
connections=Connexions
#XFLD: Section header overview in space detail
overview=Resum
#XFLD: Section header Apache Spark in space detail
sparkManagementSection=Apache Spark
#XFLD: Section header Apache Spark in space detail
sparkManagementSubSection=Aplicacions
#XFLD: Section header Apache Spark in space detail
taskAssignmentSubSection=Assignació de tasques
#XFLD: vCPU label in Apache Spark section
vCPULabel=vCPUs
#XFLD: Memory label in Apache Spark section
memoryLabel=Memòria (GB)
#XFLD: space configuration label in Apache Spark section
spaceConfigurationLabel=Configuració d'espai
#XFLD: Space Source label
sparkApplicationLabel=Aplicació
#XFLD: Cluster Size label
clusterSizeLabel=Mida de clúster
#XFLD: Driver label
driverLabel=Controlador
#XFLD: Executor label
executorLabel=Executor
#XFLD: max label
maxLabel=Màx.utilitzat
#XFLD: TrF Default label
trFDefaultLabel=Valor predeterminat del flux de transformació
#XFLD: Merge Default label
mergeDefaultLabel=Fusiona el valor predeterminat
#XFLD: Optimize Default label
optimizeDefaultLabel=Optimitza el valor predeterminat
#XFLD: Deployment Default label
deploymentDefaultLabel=Desplegament de la taula local (fitxer)
#XFLD: max spark source label
driverExecutorMaxUsedSparkSource={0} CPU / {1} GB
#XFLD: Object type label
taskObjectTypeLabel=Tipus d'objecte
#XFLD: Task activity label
taskActivityLabel=Activitat
#XFLD: Task Application ID label
taskApplicationIDLabel=Aplicació per defecte
#XFLD: Section header in space detail
generalSettings=Configuracions generals
#XMSG: Message strip message when a space is locked
spaceLockedMessageStrip=Aquest espai està bloquejat actualment pel sistema.
#XMSG: Message strip message deploy warning
deployWarningMessageStrip=Les modificacions en aquesta secció es desplegaran immediatament.
#XMSG: Message strip message modify space configuration warning
modifySpaceConfigurationMessageStrip=Tingueu en compte que el fet de modificar aquests valors pot provocar problemes de rendiment.
#XFLD: Button text to unlock the space again
unlockSpace=Desbloquejar espai
#XFLD: Info text for audit log formatted message
auditLogText=Activar els logs d'auditoria perquè enregistrin accions de lectura o modificació (polítiques d'auditoria). En acabat, els administradors poden analitzar qui ha realitzat quina acció i en quin moment.
#XMSG: Message strip  audit warning
auditLogWarningMessageStrip=Els logs d'auditoria poden utilitzar una gran quantitat d'emmagatzematge al disc en el vostre arrendatari. Si activeu una política d'auditoria (accions de lectura o modificació), haureu de supervisar amb regularitat l'ús de l'emmagatzematge al disc (mitjançant la targeta Emmagatzematge al disc utilitzat en el Monitor del sistema) per tal d'evitar talls per disc ple, que poden dur a interrupcions del servei. Si desactiveu una política d'auditoria, totes les seves entrades del log d'auditoria s'eliminaran. Si voleu conservar les entrades del log d'auditoria, penseu si les voleu exportar abans de desactivar la política d'auditoria.
#XFLD: Message strip link text to open in app help to guide user what to do to unlock his space permanently
unlockSpaceHelp=Mostrar ajuda
#XMSG: Message strip message when a space will be locked again
spaceTemporarilyUnlockedMessageStrip=Aquest espai supera l''espai d''emmagatzematge i es bloquejarà en {0} {1}.
#XMSG: Unit for remaining time until space is locked again
hours=hores
#XMSG: Unit for remaining time until space is locked again
minutes=minuts
#XFLD: Section header in space detail for auditing
detailsSectionAuditing=Auditoria
#XFLD: Subsection header in space detail for auditing
auditing=Opcions d’auditoria d’espai
#XFLD: Hot space tooltip
hotSpaceCountTooltip=Espais crítics: L’emmagatzematge utilitzat supera el 90%.
#XFLD: Green space tooltip
greenSpaceCountTooltip=Espais correctes: L’emmagatzematge utilitzat està entre el 6% i el 90%.
#XFLD: Cold space tooltip
coldSpaceCountTooltip=Espais freds: L’emmagatzematge utilitzat és del 5% o menys.
#XFLD: Critical space tooltip
criticalSpaceCountTooltip=Espais crítics: L’emmagatzematge utilitzat supera el 90%.
#XFLD: Green space tooltip
okSpaceCountTooltip=Espais correctes: L’emmagatzematge utilitzat està entre el 6% i el 90%.
#XFLD: Locked space tooltip
lockedSpaceCountTooltipNew=Espais bloquejats: Bloquejats per manca de memòria.
#XFLD: Locked space tooltip
lockedSpaceCountTooltip=Espais bloquejats
#YMSE: Error while deleting remote source
deleteRemoteError=Les connexions no s’han pogut eliminar.
#XFLD: Space ID input tooltip
spaceIDInfoTooltip=L’ID d’espai no es pot modificar més endavant.\nEls caràcters vàlids són A - Z, 0 - 9 i _
#YMSE: Error when space name is empty
spaceNameEmptyValidationError=Introduir nom d’espai.
#YMSE: Error when ECN Business name is empty
ecnNameEmptyValidationError=Introduïu un nom empresarial.
#YMSE: Error when space id is empty
spaceIDInputEmptyValidationError=Introduir ID d’espai.
#YMSE: Error when space id contains invalid characters
spaceIDInputInValidError=Caràcters no vàlids. Utilitzeu només A - Z, 0 - 9, i _.
#YMSE: Error when space id exists while creation
spaceIDInputDuplicateError=L’ID d’espai ja existeix.
#XFLD: Space searchfield placeholder
search=Cercar
#XMSG: Success message after creating users
createUsersSuccess=Usuaris afegits
#XMSG: Success message after creating user
createUserSuccess=Usuari afegit
#XMSG: Success message after updating users
updateUsersSuccess={0} Usuaris actualitzats
#XMSG: Success message after updating user
updateUserSuccess=Usuari actualitzat
#XMSG: Success message after removing users
removeUsersSuccess={0} Usuaris eliminats
#XMSG: Success message after removing user
removeUserSuccess=Usuari eliminat
#XFLD: Schema name
schemaName=Nom d'esquema
#XFLD: used of total
ofTemplate={0} de {1}
#XFLD: Assigned disk label in space overview
assignedStorageTemplate=Disc assignat ({0} de {1})
#XFLD: Assigned Memory label in space overview
assignedRAMTemplate=Memòria assignada ({0} de {1})
#XFLD: Storage ratio on space
accelearationRAM=Acceleració de memòria
#XFLD: No Storage Consumption
noStorageConsumptionText=Cap quota d'emmagatzematge assignada.
#XFLD: Used disk label in space overview
usedStorageTemplate=Disc utilitzat per a l''emmagatzematge ({0} de {1})
#XFLD: Used Memory label in space overview
usedRAMTemplate=Memòria utilitzada per a l''emmagatzematge ({0} de {1})
#XFLD: Used Disk tooltip
usedStorageTooltipTemplate={0} de {1} disc utilitzat per a l''emmagatzematge
#XFLD: Used Memory tooltip
usedRAMTooltipTemplate={0} de {1} de memòria utilitzada
#XFLD: Assigned Disk tooltip
assignedStorageTooltipTemplate={0} de {1} de disc assignat
#XFLD: Assigned Memory tooltip
assignedRAMTooltipTemplate={0} de {1} de memòria assignada
#XFLD: Space related used storage tooltip
spaceRelatedUsedStorageTooltipTemplate=Dades d''espai: {0}
#XFLD: Not space related used storage tooltip
otherDataUsedStorageTooltipTemplate=Altres dades: {0}
#XFLD: Storage used exceeded popover and tooltip
storageUsedExceededText=Valoreu la possibilitat d'ampliar el pla, o adreceu-vos al servei d'assistència de SAP.
#XCOL: Space table-view column used Disk
usedStorage=Disc utilitzat per a l'emmagatzematge
#XCOL: Space monitor column used Memory
usedRAM=Memòria utilitzada per a l'emmagatzematge
#XCOL: Space monitor column Schema
tableSchema=Esquema
#XCOL: Space monitor column Storage Type
tableStorageType=Tipus d’emmagatzematge
#XCOL: Space monitor column Table Type
tableType=Tipus de taula
#XCOL: Space monitor column Record Count
tableRecordCount=Recompte de registres
#XFLD: Assigned Disk
assignedStorage=Disc assignat per a l'emmagatzematge
#XFLD: Assigned Memory
assignedRAM=Memòria assignada per a l'emmagatzematge
#XCOL: Space table-view column storage utilization
tableStorageUtilization=Emmagatzematge utilitzat
#XFLD: space status
spaceStatus=Estat d’espai
#XFLD: space type
spaceType=Tipus d'espai
#XFLD: space type label for space with space type "SAP BW bridge" ("SAP BW bridge" is a product name, do not translate it)
abapbridgeSpaceType=Pont de SAP BW
#XFLD: space type label for space with space type "Data Marketplace"
marketplaceSpaceType=Producte de proveïdor de dades
#XMSG: Error message for when selected space cannot be deleted due to the space type
deleteTypedSpaceError=No podeu suprimir l''espai {0} perquè té el tipus {1}.
#XMSG: Error message for when selected spaces cannot be deleted due to the space type
deleteTypedSpacesError=No podeu suprimir els {0} espais seleccionats. No es poden suprimir els espais que tinguin els tipus següents: {1}.
#XFLD: space type label for space without space type
defaultSpaceType=SAP Datasphere
#XFLD: Tooltip for monitor space button
monitorSpace=Supervisar
#XFLD: Tooltip for edit space button
editSpace=Editar espai
#XMSG: Deletion warning in messagebox
deleteConfirmation=Segur que voleu suprimir aquest espai?
#XFLD: Tooltip for delete space button
deleteSpace=Suprimir espai
#XFLD: storage
storage=Disc per a l'emmagatzematge
#XFLD: username
userName=Nom d'usuari
#XFLD: port
port=Port
#XFLD: hostname
hostName=Nom d’amfitrió
#XFLD: password
password=Contrasenya
#XBUT: Request new password button
requestPassword=Sol·licitar contrasenya nova
#YEXP: Usage explanation in time data section
timeDataSectionHint=Creeu dimensions i taules de temps per usar-les en els vostres models i històries.
#YEXP: Usage explanation in Space schema section
spaceSchemaHint=Voleu que les dades del vostre espai puguin utilitzar-les altres eines o aplicacions? Si és que sí, creeu un o diversos usuaris que puguin accedir a les dades del vostre espai i seleccioneu si voleu que totes les dades d'espai futures puguin ser utilitzades de forma predeterminada.
#XTIT: Create schema popup title
createSchemaDialogTitle=Crear esquema SQL obert
#XTIT: Generate Time Data dialog title
generateTimeDataDialogTitle=Crear dimensions i taules de temps
#XTIT: Edit Time Data dialog title
editTimeDataDialogTitle=Editar dimensions i taules de temps
#XTIT: Time Data token title
timeDataTokenTitle=Dades de temps
#XTIT: Time Data token title
timeDataUpdateViews=Actualitzar vistes de dades temporals
#XFLD: Time Data token creation in progress label
timeDataCreationInProgress=Creació en curs...
#XFLD: Time Data token creation error label
timeDataCreationError=Error en crear. Torneu a provar-ho.
#XTIT: Time Data dialog time settings title
timeDataDialogTimeSettingsTitle=Opcions de taula de temps
#XTIT: Time Data dialog time data table title
timeDataDialogTranslationTablesTitle=Taules de conversió
#XTIT: Time Data dialog dimensions title
timeDataDialogTimeViewsTitle=Dimensions de temps
#XFLD: Time Data dialog time range label
timeRangeHint=Definir interval de temps.
#XFLD: Time Data dialog time data table label
timeDataHint=Doneu un nom a la vostra taula.
#XFLD: Time Data dialog time data dimensions label
timeDimensionsHint=Doneu un nom a les vostres dimensions.
#XFLD: Time Data Time range description label
timerangeLabel=Interval
#XFLD: Time Data dialog from year label
fromYearLabel=Des de l'any
#XFLD: Time Data dialog to year label
toYearLabel=Fins a l'any
#XFLD: Time Data dialog calendar type label
calendarTypeLabel=Tipus de calendari
#XFLD: Time Data dialog granularity label
granularityLabel=Granularitat
#XFLD: Time Data dialog technical name label
technicalNameLabel=Nom tècnic
#XFLD: Time Data dialog quarters text table lable
quarterTextsTableLabel=Taula de conversió per a trimestres
#XFLD: Time Data dialog months text table lable
monthsTextsTableLabel=Taula de conversió per a mesos
#XFLD: Time Data dialog days text table lable
daysTextsTableLabel=Taula de conversió per a dies
#XFLD: Time Data dialog year label
yearLabel=Dimensió anual
#XFLD: Time Data dialog quarter label
quarterLabel=Dimensió trimestral
#XFLD: Time Data dialog month label
monthLabel=Dimensió mensual
#XFLD: Time Data dialog day label
dayLabel=Dimensió diària
#XFLD: Time Data dialog gregorian calendar type label
gregorian=Gregorià
#XFLD: Time Data dialog time granularity day label
day=Dia
#XFLD: Time Data dialog business name maxlength reached
timeRangeValueStateTextMaxLengthBusinessName=Longitud màxima de 1000 caràcters assolida.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextMaxRangeExceeded=L’interval de temps màxim és de 150 anys.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeLower="Any d’inici" ha de ser anterior a "Any de fi"
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeHigher="Any d'inici" ha de ser 1900 o posterior.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeHigher="Any de fi" ha de ser posterior a "Any d’inici"
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeLower="Any de fi" ha de ser anterior a l'any actual més 100
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningFromYear=Incrementar el valor "Any d’inici" pot provocar una pèrdua de dades
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningToYear=Reduir el valor "Any de fi" pot provocar una pèrdua de dades
#XMSG: Time Data creation validation error message
timeDataValidationError=Sembla que alguns camps no són vàlids. Verifiqueu els camps obligatoris per continuar.
#XMSG: Time Data deletion popup text
deleteTimeDataConfirmation=Segur que voleu suprimir les dades?
#XMSG: Time Data creation success message
createTimeDataSuccess=Dades de temps creades
#XMSG: Time Data update success message
updateTimeDataSuccess=Dades de temps actualitzades
#XMSG: Time Data delete success message
deleteTimeDataSuccess=Dades de temps suprimides
#XMSG: Time Data creation error message
createTimeDataError=Alguna cosa ha anat malament en intentar crear dades de temps.
#XMSG: Time Data update error message
updateTimeDataError=Alguna cosa ha anat malament en intentar actualitzar les dades de temps.
#XMSG: Time Data creation error message
deleteTimeDataError=Alguna cosa ha anat malament en intentar suprimir les dades de temps.
#XMSG: Error message when time data could not be retrieved
retrievingTimeDataError=Les dades de temps no s’han pogut carregar.
#XTIT: Time Data deletion popup title
deleteTimeDataConfirmationTitle=Advertència
#XTXT: Time Data deletion dependency popup text
deleteTimeDataDependencyText=No hem pogut suprimir les vostres dades de temps perquè s’utilitzen en altres models.
#XTXT: Time Data deletion dependency popup text singular
deleteTimeDataDependencyTextSingular=No hem pogut suprimir les vostres dades de temps perquè s’utilitzen en un altre model.
#XMSG: Validation error when required time data form field is empty
timeDataRequiredField=Aquest camp és obligatori i no es pot deixar en blanc.
#XFLD: Tooltip for Open in Database Explorer button
openInDBX=Obrir a l'explorador de bases de dades
#YMSE: Dimension Year
dimensionYearView=Dimensió "Any"
#YMSE: Dimension Year
dimensionQuarterView=Dimensió "Trimestre"
#YMSE: Dimension Year
dimensionMonthView=Dimensió "Mes"
#YMSE: Dimension Year
dimensionDayView=Dimensió "Dia"
#XFLD: Time Data deletion object title
timeDataUsedIn=(usat en {0} models)
#XFLD: Time Data deletion object title singular
timeDataUsedInSingular=(usat en 1 model)
#XFLD: Time Data deletion table column provider
provider=Proveïdor
#XTIT: Time Data dependency dialog title
timeDataDependencyDialogTitle=Relació
#XTIT: Create schema popup title
createSpaceSchemaUserDialogTitle=Crear usuari per a esquema d'espai
#XFLD: Create schema button
createSchemaButton=Crear esquema SQL obert
#XFLD: Generate TimeData button
generateTimeDataButton=Crear dimensions i taules de temps
#XFLD: Show dependencies button
showDependenciesButton=Mostrar dependències
#XFLD: tooltip for disabled buttons when user is not member of space
mustBeMemberOfSpaceForOperation=Per realitzar aquesta operació, l’usuari ha de ser un membre de l'espai.
#XFLD: Create space schema user button
createSpaceSchemaUserButton=Crear usuari d'esquema d'espai
#YMSE: API Schema users load error
loadSchemaUsersError=No s'ha pogut carregar la llista d’usuaris.
#XTIT: API Schema-config popup title
schemaUserDetailsDialogTitle=Detalls de l’usuari d’esquema d’espai
#XMSG: API Schema user deletion popup text
deleteSpaceSchemaUserConfirmation=Segur que voleu suprimir l’usuari seleccionat?
#XMSG: API Schema user deletion confirmation text
userDeleteSuccess=Usuari suprimit.
#YMSE: API Schema user deletion error
userDeleteError=L’usuari no s’ha pogut suprimir.
#XFLD: User deleted
userDeleted=S'ha suprimit l'usuari.
#XTIT: Remove user popup title
removeUserConfirmationTitle=Advertència
#XMSG: Remove user popup text
removeUserConfirmation=Segur que voleu eliminar l'usuari? L'usuari i les seves funcions assignades amb abast definit s'eliminaran de l'espai.
#XMSG: Remove users popup text
removeUsersConfirmation=Segur que voleu eliminar els usuaris? Els usuaris i les funcions assignades amb abast definit s'eliminaran de l'espai.
#XBUT: Confirmation button for remove user dialog
removeUserConfirmationBtn=Eliminar
#YMSE: No data text for available roles
noDataAvailableRoles=L'espai no s'afegeix a cap funció amb abast definit. \n Per poder afegir usuaris a l'espai, primer s'ha d'afegir a una o més funcions amb abast definit.
#YMSE: No data text for selected roles
noDataSelectedRoles=Cap funció seleccionada amb abast definit
#XTIT: Schema-config popup title
schemaDetailsDialogTitle=Detalls de configuració de l'esquema SQL obert
#XFLD: Label for Read Audit Log
auditLogRead=Activar log d’auditoria per a operacions de lectura
#XFLD: Label for Change Audit Log
auditLogChange=Activar log d’auditoria per a operacions de modificació
#XFLD: Label Audit Log Retention
auditLogRetention=Conservar logs durant
#XFLD: Label Audit Log Retention Unit
retentionUnit=Dies
#YMSE: Error when log retention time is set incorrectly
incorrectRetentionTime=Introduïu un nombre enter entre {0} i {1}
#XFLD: Label for Cross Space Schema Access
spaceSchemaAccess=Consumir dades d’esquema d'espai
#XTIT: Confirm deactivation of space schema consumption dialog title
disableSpaceSchemaConsumptionDialogTitle=Deixar de consumir dades d’esquema d'espai
#XTXT: Confirm deactivation of space schema consumption dialog text
disableSpaceSchemaConsumptionDialogText=Aquest esquema SQL obert podria consumir dades del vostre esquema d’espai. Si deixeu de consumir, els models que es basen en les dades d'esquema d'espai podrien deixar de funcionar.
#XBUT: Confirmation button for the stop consuming space schema data dialog
stopConsuming=Deixar de consumir
#XTXT: Tooltip for data lake icon
dataLakeIconTooltip=Aquest espai s’utilitza per accedir al llac de dades
#XFLD: Tooltip for data lake table entry
dataLakeTableEntryTooltip=Llac de dades activat
#XFLD: used In-Memory linit reached tag
usedRamLimitReachedLabel=S’ha assolit el límit de memòria
#XFLD: Space storage Limit reached tag
storageLimitReachedLabel=S’ha assolit el límit d’emmagatzematge
#XFLD: Minimal Storage Limit Reached tag
minStorageLimitReachedLabel=S’ha assolit el límit d’emmagatzematge mínim
#XFLD: Space ram tag
ramLimitReachedLabel=S’ha assolit el límit de memòria
#XFLD: Minimal In-Memory Limit Reached tag
minRamLimitReachedLabel=S’ha assolit el límit de memòria mínima
#XFLD: Popover text after click on space storage limit warning
storageLimitReachedText=Heu assolit el límit d’emmagatzematge d''espai assignat de {0}. Assigneu més emmagatzematge a l''espai.
#XFLD: System storage tag
systemStorageLimitReachedLabel=S’ha assolit el límit d’emmagatzematge del sistema
#XMSG: Popover text after click on storage limit warning
systemStorageLimitReachedText=Heu assolit el límit d’emmagatzematge del sistema de {0}. Ara ja no podeu assignar més emmagatzematge a l''espai.
#XMSG: Schema deletion warning
deleteSchemaConfirmation=Si suprimiu aquest esquema SQL obert també se suprimiran de forma permanent tots els objectes desats i les associacions actualitzades a l'esquema. Voleu continuar?
#XMSG: Success message after deleting a schema
schemaDeleteSuccess=Esquema suprimit
#YMSE: Error while deleting schema.
schemaDeleteError=L'esquema no s’ha pogut suprimir.
#XMSG: Success message after update a schema
schemaUpdateSuccess=Esquema actualitzat
#YMSE: Error while updating schema.
schemaUpdateError=L'esquema no s’ha pogut actualitzar.
#XMSG: Hint when no password is shown in the dialog.
newPasswordHint=Hem indicat una contrasenya per a aquest esquema. Si heu oblidat la contrasenya o l’heu perdut, podeu demanar-ne una de nova. Recordeu copiar o desar la contrasenya nova.
#XMSG: Hint when showing the password in the dialog.
savePasswordHint=Copieu la contrasenya. La necessitareu per establir una connexió amb aquest esquema. Si l’heu oblidat, obriu aquest diàleg per restablir-la.
#XFLD,50: Hint on entering schema name in creation dialog
schemaNameHint=EL nom no es pot modificar un cop creat l'esquema.
#XFLD: Open SQL Schemas section sub headline
schemasSQL=SQL obert
#XFLD: Space schema section sub headline
schemasSpace=Espai
#XFLD: HDI Container section header
HDIContainers=Contenidors HDI
#XTXT: Add HDI Containers button tooltip
addHDIContainers=Afegir contenidors HDI
#XTXT: Remove HDI Containers button tooltip
removeHDIContainers=Eliminar contenidors HDI
#XFLD: Enable HDI Containers feature button
enableHDIContainersButton=Activar accés
#YMSE: No data text for HDI Containers table
noDataHDIContainers=No s’han afegit contenidors HDI.
#YMSE: No data text for Timedata section
noDataTimedata=No s'ha creat cap taula ni dimensió de temps.
#YMSE: No data text during runtime not available for Timedata section
runtimeNotAvailableNoDataTimedata=No es poden carregar taules de temps i dimensions perquè la base de dades de temps d'execució no està disponible.
#YMSE: No data text during runtime not available for HDI section
runtimeNotAvailableNoDataHDI=No es poden carregar els contenidors HDI perquè la base de dades en temps d'execució no està disponible.
#YMSE: Error while fetching HDI Containers
fetchHDIContainersError=No s’han pogut obtenir els contenidors HDI. Torneu a provar-ho més tard.
#XFLD Table column header for HDI Container names
HDIContainerName=Nom del contenidor HDI
#XFLD: Enable HDI Containers feature dialog title
enableHDIContainersDialogTitle=Activar accés
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText1New=Podeu habilitar SAP SQL Data Warehousing al vostre arrendatari de SAP Datasphere per intercanviar dades entre els vostres contenidors HDI i els vostres espais de SAP Datasphere sense haver de fer moviments de dades.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText2=Per fer-ho, obriu un tiquet d'assistència fent clic al botó de sota.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText3New=Un cop s'hagi processat el tiquet, heu de crear un o més contenidors HDI nous a la base de dades en temps d'execució de SAP Datasphere. En acabat, el botó Habilitar accés se substitueix pel botó + a la secció Contenidors HDI de tots els vostres espais de SAP Datasphere i podeu afegir els contenidors a un espai.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText41=Cal més informació? Aneu a %%0. Per obtenir informació més detallada sobre què incloure al ticket, consulteu %%1.
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkText=SAP Help
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkTextSapNote3057059=Nota de SAP 3057059
#XBUT: Open Ticket Button Text
openTicket=Obrir tiquet
#XBUT: Add Button Text
add=Afegir
#XBUT: Next Button Text
next=Següent
#XBUT: Edit Button Text
editUsers=Editar
#XBUT: create user Button Text
createUser=Crear
#XBUT: Update user Button Text
updateUser=Seleccionar
#XTIT: Add HDI Containers Dialog Title
addHDIContainersDialogTitle=Afegir contenidors HDI no assignats
#XFLD: No data text for add HDI container dialog
addHDIContainersDialogNoDataText=No hem trobat contenidors sense assignar. \n El contenidor que busqueu pot ser que ja estigui assignat a un espai.
#YMSE: Error while loading assigned HDI Containers
loadAssignedHDIContainersError=No s’han pogut carregar els contenidors HDI assignats.
#YMSE: Error while loading assigned HDI Containers
loadUnassignedHDIContainersError=No s’han pogut carregar els contenidors HDI.
#XMSG: Success message
succeededToAddHDIContainer=Contenidor HDI afegit
#XMSG: Success message
succeededToAddHDIContainerPlural=Contenidors HDI afegits
#XMSG: Success message
succeededToDeleteHDIContainer=Contenidor HDI eliminat
#XMSG: Success message
succeededToDeleteHDIContainerPlural=Contenidors HDI eliminats
#XFLD: Time data section sub headline
timeDataSection=Dimensions i taules de temps
#XFLD: Schemas list entry read audit log enabled indicator
readIndicator=Llegir
#XFLD: Schemas list entry change audit log enabled indicator
changeIndicator=Modificar
#XFLD: Remote sources section sub headline
allconnections=Assignació de connexió
#XFLD: Remote sources section sub headline
localconnections=Connexions locals
#XFLD: Remote sources section sub headline for open Connectors
openConnectors=SAP Open Connectors
#XFLD: User section sub headline
memberassignment=Assignació de membres
#XFLD: User assignment section sub headline
userAssignment=Assignació d’usuari
#XFLD: User section Access dropdown Member
member=Membre
#XFLD: User assignment section column name
user=Nom d'usuari
#XTXT: Selected role count
selectedRoleToolbarText=Seleccionats: {0}
#XTIT: Space detail section connections title
detailsSectionConnections=Connexions
#XTIT: Space detail section data access title
detailsSectionDataAccess=Accés a esquema
#XTIT: Space detail section time data title
detailsSectionGenerateData=Dades de temps
#XTIT: Space detail section members title
detailsSectionUsers=Membres
#XTIT: Space detail section Users title
detailsSectionUsersTitle=Usuaris
#XTIT: Out of Storage
insufficientStoragePopoverTitle=Sense emmagatzematge
#XTIT: Storage distribution
storageDistributionPopoverTitle=Emmagatzematge de disc utilitzat
#XTXT: Out of Storage popover text
insufficientStorageText=Per crear un espai nou, reduïu l’emmagatzematge assignat d’un altre espai o suprimiu un espai que ja no us faci falta. Podeu incrementar l’emmagatzematge total del sistema cridant Gestionar pla.
#XMSG: Space id length warning
spaceIdLengthWarning=S’ha superat el màxim de {0} caràcters.
#XMSG: Space name length warning
spaceNameLengthWarning=S’ha superat el màxim de {0} caràcters.
#XMSG: Space id reserved prefix warning
spaceIdnWarningReservedPrefix=No utilitzeu el prefix {0} per evitar possibles conflictes.
#YMSE: Error while loading open SQL schemas
loadLocalSchemasError=No s’han pogut carregar els esquemes Open SQL.
#YMSE: Error while creating open SQL schema
createLocalSchemaError=No s’ha pogut crear l'esquema Open SQL.
#YMSE: Error while loading all remote connections
loadRemoteConnectionsError=No s’han pogut carregar totes les connexions remotes.
#YMSE: Error while loading space details
loadSpaceDetailsError=No s’han pogut carregar els detalls d'espai.
#YMSE: Error while deploying space details
deploySpaceDetailsError=No s’ha pogut desplegar l’espai.
#YMSE: Error while copying space details
copySpaceDetailsError=No s'ha pogut copiar l'espai.
#YMSE: Error while loading storage data
loadStorageDataError=No s’han pogut carregar les dades d’emmagatzematge.
#YMSE: Error while loading all users
loadAllUsersError=No s’han pogut carregar tots els usuaris.
#YMSE: Failed to reset password
resetPasswordError=No s’ha pogut restablir la contrasenya.
#XMSG: Success message after updating space
resetPasswordSuccessMessage=Contrasenya nova fixada per a l'esquema
#YMSE: DP Agent-name too long
DBAgentNameError=El nom de l'agent DP és massa llarg.
#YMSE: Schema-name not valid.
schemaNameError=El nom de l'esquema no és vàlid.
#YMSE: User name not valid.
UserNameError=El nom d’usuari no és vàlid.
#XTIT: Consumption by Storage Type
consumptionStorageTypeText=Consum per tipus d’emmagatzematge
#XTIT: Consumption by Schema
consumptionSchemaText=Consum per esquema
#XTIT: Overall Consumption by schema
overallConsumptionSchemaText=Consum de taula global per esquema
#XTIT: Overall Consumption by Table Type
overallConsumptionTableTypeText=Consum global per tipus de taula
#XTIT: Tables
tableDetailsText=Detalls de taula
#XTIT: Table Storage Consumption
tableStorageConsumptionText=Consum d’emmagatzematge de taula
#XFLD: Table Type label
tableTypeLabel=Tipus de taula
#XFLD: Schema label
schemaLabel=Esquema
#XFLD: reset table tooltip
resetTable=Restablir taula
#XFLD: In-Memory label in space monitor
inMemoryLabel=Memòria
#XFLD: Disk label in space monitor
diskLabel=Disc
#XFLD: Yes
yesLabel=Sí
#XFLD: No
noLabel=No
#XFLD: switch label: Want the data in this space to be consumable by default?
allowConsumptionByDefault=Voleu que les dades d’aquest espai es puguin utilitzar per defecte?
#XFLD: Business Name
businessNameLabel=Nom empresarial
#XFLD: Refresh
refresh=Actualitzar
#XMSG: No filter results title
noFilterResultsTitle=Sembla que les vostres opcions de filtre no mostren dades.
#XMSG: No filter results message
noFilterResultsMsg=Proveu de refinar les vostres opcions de filtre i, si tot i així no es mostren dades, creeu taules al Generador de dades. Un cop consumeixin emmagatzematge, les podreu supervisar aquí.
#XMSG: Space monitoring Hana state red title
spaceMonitorHanaRedTitle=La base de dades en temps d'execució no està disponible.
#XMSG: Space monitoring Hana state red message
spaceMonitorHanaRedMsg=Atès que la base de dades de temps d'execució no està disponible, algunes funcions estan desactivades i no es pot mostrar informació en aquesta pàgina.
#YMSE: Error while creating space schema user
createSpaceSchemaUserError=No s’ha pogut crear un esquema d'espai.
#YMSE: Error User name already exists
userAlreadyExistsError=El nom d’usuari ja existeix.
#YMSE: Error Authentication failed
authenticationFailedError=Error en autenticar.
#XMSG: Message strip message when a schema user is locked
userLockedMessageStrip=L’usuari està bloquejat perquè s’han produït massa inicis de sessió fallits. Sol·liciteu una contrasenya nova per desbloquejar l’usuari.
#XMSG: Success message after reset and unlock schema user
resetUnlockPasswordSuccessMessage=Contrasenya nova fixada i usuari desbloquejat
#XMSG: user is locked message
userLockedMessage=L’usuari està bloquejat.
#XCOL: Users table-view column Role
spaceRole=Rol
#XCOL: Users table-view column Scoped Role
spaceScopedRole=Funció amb abast definit
#XCOL: Users table-view column Space Admin
spaceAdmin=Administrador d’espai
#XFLD: User section dropdown value Viewer
viewer=Visualitzador
#XFLD: User section dropdown value Modeler
modeler=Modelador
#XFLD: User section dropdown value Data Integrator
dataIntegrator=Integrador de dades
#XFLD: User section dropdown value Space Administrator
spaceAdministrator=Administrador d’espai
#XMSG: Success message -  Space Role updated successfully.
updateUserSpaceRoleSuccess=Rol d'espai actualitzat
#XMSG: Error message - Failed to update Space Role.
updateUserSpaceRoleError=El rol d'espai s’ha actualitzat amb errors.
#XFLD:
databaseUserNameSuffix=Sufix de nom d’usuari de base de dades
#XTXT: Space Schema password text
spaceSchemaPasswordText=Per configurar una connexió amb aquest esquema, copieu la contrasenya. Si l’heu oblidat, sempre en podeu sol·licitar una de nova.
#XFLD: Cloud Platform label in space schema dialog
cloudPlatformLabel=SAP Cloud Platform
#XTXT: Cloud Platform text in space schema dialog
cloudPlatformText=Per configurar un accés amb aquest usuari, activeu el consum i la còpia de credencials. Si només podeu copiar les credencials sense una contrasenya, assegureu-vos d’afegir-la més endavant.
#XFLD: enable consumption in CloudPlatform label in space schema dialog
enableConsumptionInCloudPlatformLabel=Activar consum a SAP Cloud Platform
#XFLD: Credentials for User-Provided Service label in space schema dialog
cloudPlatformCredentialsLabel=Credencials per a servei prestat per l’usuari:
#XFLD: Credentials for User-Provided Service (Without Password) label in space schema dialog
cloudPlatformCredentialsWithoutPasswordLabel=Credencials per a servei prestat per usuari (sense contrasenya):
#XFLD: Copy Credentials label in space schema dialog
copyCredentialsLabel=Copiar credencials sense contrasenya
#XFLD: Copy Credentials label in space schema dialog
copyFullCredentialsLabel=Copiar credencials completes
#XFLD: Copy Password label in space schema dialog
copyPasswordLabel=Copiar contrasenya
#XMSG: Credentials copied to clipboard
credentialsCopiedMessage=Credencials copiades a porta-retalls
#XMSG: Password copied to clipboard
passwordCopiedMessage=Contrasenya copiada a porta-retalls
#XMSG: create database user dialog title
createDatabaseUserDialogTitle=Crear usuari de base de dades
#XMSG: Database Users section title
databaseUsers=Usuaris de bases de dades
#XMSG: Database User Details dialog title
databaseUserDetailsDialogTitle=Detalls d’usuari de base de dades
#XFLD: database user read audit log
databaseUserAuditLogRead=Activar logs d’auditoria per a operacions de lectura i conservar logs durant
#XFLD: database user change audit log
databaseUserAuditLogChange=Activar logs d’auditoria per a operacions de modificació i conservar logs durant
#XMSG: Cloud Platform Access
cloudPlatformAccess=Accés a SAP Cloud Platform
#XFLD: Enable Consumption in Cloud Platform hint
enableConsumptionCloudPlatformHint=Configureu l’accés al contenidor de HANA Deployment Infrastructure (HDI) mitjançant aquest usuari de base de dades. Per connectar-vos al vostre contenidor HDI, el modelatge SQL ha d'estar activat
#XFLD: Enable HDI Consumption
enableConsumptionCloudPlatform=Activar consum HDI
#XFLD: Enable Consumption hint
enableConsumptionHint=Voleu que altres eines o aplicacions puguin utilitzar les dades que teniu a l'espai?
#XFLD: Enable Consumption
enableConsumption=Activar consum SQL
#XFLD: Enable Modeling
enableModeling=Activar modelatge SQL
#XMSG: Privileges for Data Modeling
privilegesModeling=Ingesta de dades
#XMSG: Privileges for Data Consumption
privilegesConsumption=Consum de dades per a eines externes
#XFLD: SQL Modeling
sqlModeling=Modelatge SQL
#XFLD: SQL Consumption
sqlConsumption=Consum SQL
#XFLD: enabled
enabled=Activat
#XFLD: disabled
disabled=Desactivat
#XFLD: Edit Privileges
editPrivileges=Editar privilegis
#XFLD: Open Database Explorer
openDBX=Obrir l'explorador de bases de dades
#XFLD: create database user hint
databaseCreateHint=Tingueu en compte que no podreu tornar a modificar el nom d’usuari un cop hàgiu desat les dades.
#XFLD: Internal Schema Name
internalSchemaName=Nom d'esquema intern
#YMSE: Failed to load database users
loadDatabaseUserError=Error en carregar usuaris de bases de dades
#YMSE: Failed to delete database user
deleteDatabaseUsersError=Error en suprimir usuaris de bases de dades
#XMSG: Success message after deleting database user
deleteDatabaseUserSuccess=Usuari de base de dades suprimit
#XMSG: Success message after deleting database users
deleteDatabaseUsersSuccess=Usuaris de base de dades suprimits
#XMSG: Success message after creating database user
createDatabaseUserSuccessMessage=Usuari de base de dades creat
#YMSE: Failed to create database user
createDatabaseUserError=Error en crear l'usuari de base de dades
#XMSG: Success message after updating database user
updateDatabaseUserSuccessMessage=Usuari de base de dades actualitzat
#YMSE: Failed to update database user
updateDatabaseUserError=Error en actualitzar l’usuari de base de dades
#XFLD: HDI Consumption
hdiConsumption=Consum HDI
#XTIT: Space detail section Database Access title
detailsSectionDatabaseAccess=Accés a base de dades
#XTXT: Database users consumption hint
databaseUsersConsumptionHint=Feu que les dades de l’espai es puguin consumir per defecte. Així, els models dels generadors permetran automàticament que es puguin consumir les dades.
#XFLD: Default Consumption of Space Data:
defaultConsumptionOfSpaceData=Consum predeterminat de dades d'espai:
#XFLD: Database User Name
databaseUserName=Nom d’usuari de base de dades
#XMSG: Database User creation validation error message
databaseUserValidationError=Sembla que alguns camps no són vàlids. Verifiqueu els camps obligatoris per continuar.
#XMSG: SQL modeling can’t be enabled since this user has been migrated.
migratedDatabaseUserHint=La ingesta de dades no es pot activar perquè aquest usuari s’ha migrat.
#XBUT: Remove Button Text
remove=Eliminar
#XBUT: Remove Spaces Button Text
removeSpaces=Eliminar espais
#XBUT: Remove Objects Button Text
removeObjects=Eliminar objectes
#XMSG: No members have been added yet.
noMembersAssigned=Encara no s'han afegit membres.
#XMSG: No users have been added yet.
noUsersAssigned=Encara no s'han afegit usuaris.
#XMSG: No database users have been created or your filter is not showing any data.
noDatabaseUsers=No s’ha creat cap usuari de base de dades o el filtre no mostra dades.
#XMSG: Please enter a user name.
noDatabaseUsername=Introduïu un nom d'usuari.
#XMSG: The user name is too long. Please use a shorter one.
databaseUsernameTooLong=El nom d'usuari és massa llarg. Utilitzeu-ne un de més curt.
#XMSG: No privileges have been enabled and this database user will have limited functionality. Do you still want to continue?
noPrivilegesConfirmationMessage=No s'ha activat cap privilegi i aquest usuari de base de daes tindrà funcionalitat limitada. Voleu continuar?
#XMSG: To enable audit logs for change operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogChangeMessage=Per activar els logs d’auditoria per a les operacions de modificació també cal activar la ingesta de dades. Voleu fer-ho?
#XMSG: To enable audit logs for read operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogReadMessage=Per activar els logs d’auditoria per a les operacions de lectura també cal activar la ingesta de dades. Voleu fer-ho?
#XMSG: To enable HDI consumption data ingestion and data consumption needs to be enabled. Do you want to enable all?
enableHDIConsumptionMessage=Per activar el consum HDI també cal activar la ingesta i el consum de dades. Voleu fer-ho?
#XMSG:
databaseUserPasswordText=Per configurar una connexió amb aquest usuari de base de dades, copieu la contrasenya. Si l’oblideu, sempre en podeu sol·licitar una de nova.
#XTIT: Space detail section members title
detailsSectionMembers=Membres
#XMSG: New password set
newPasswordSet=Contrasenya nova fixada
#XFLD: Data Ingestion
dataIngestion=Ingesta de dades
#XFLD: Data Consumption
dataConsumption=Consum de dades
#XFLD: Privileges
privileges=Privilegis
#XFLD: Enable Data ingestion
enableDataIngestion=Activar ingesta de dades
#XMSG: Log the read and change operations for data ingestion.
databaseUserAuditHint=Deseu al log les operacions de lectura i modificació per a la ingesta de dades.
#XMSG: Make your space data available in your HDI containers.
databaseUserHDIHint=Feu accessibles les dades de l’espai als contenidors HDI.
#XFLD: Enable Data consumption
enableDataConsumption=Activar consum de dades
#XMSG: Allow other apps or tools to consume your space data.
enableDataConsumptionHint=Permeteu que altres aplicacions o eines consumeixin les dades de l’espai.
#XMSG: To set up access via this schema user copy the credentials to your user-provided service. In case you can only copy the credentials without a password make sure to add the password later.
databaseUserHDIConsumptionHint=Per configurar l’accés amb aquest usuari de base de dades, copieu les credencials al servei prestat per l’usuari. Si només podeu copiar les credencials sense una contrasenya, assegureu-vos d’afegir-la més endavant.
#XFLD: Data Flow Runtime Capacity label in space overview
dataFlowRuntimeCapacity=Capacitat de temps d’execució de flux de dades ({0}:{1} hores de {2} hores)
#XFLD: Could not load Data Flow Runtime Capacity
dataFlowRuntimeCapacityError=No s’ha pogut carregar la capacitat de temps d’execució de flux de dades
#XMSG: User can grant data consumption to other users.
databaseUserConsumptionWithGrantOptionHint=L’usuari pot concedir el consum de dades a altres usuaris.
#XFLD: Enable data consumption with grant option
enableConsumptionWithGrantOption=Activar consum de dades amb l’opció de concessió
#XMSG: To enable data consumption with grant option data consumption needs to be enabled. Do you want to enable both?
enableConsumptionWithGrantMessage=Per activar el consum de dades amb l’opció de concessió, cal activar el consum de dades. Voleu activar-los tots dos?
#XFLD: Enable Automated Predictive Library (APL) and Predictive Analysis Library (PAL)
allowUsingScriptServerFunctionality=Activar Biblioteca predictiva automatitzada (APL) i Biblioteca d’anàlisis predictives (PAL)
#XMSG: User can use SAP HANA Cloud embedded machine learning functions.
allowUsingScriptServerFunctionalityHint=L’usuari pot utilitzar les funcions d’aprenentatge automàtic integrades a SAP HANA Cloud.
#XFLD: Password Policy
passwordPolicy=Política de contrasenyes
#XMSG: Password Policy
passwordPolicyHint=Activeu o desactiveu aquí la política de contrasenyes configurada.
#XFLD: Enable Password Policy
enablePasswordPolicy=Activar política de contrasenyes
#XMSG: Read Access to the Space Schema
readAccessTitle=Accés de lectura a l’esquema d'espai
#XMSG: read access hint
readAccessHint=Permeteu a l’usuari de base de dades connectar eines externes a l’esquema d’espai i llegir les vistes exposades per al consum.
#XFLD: Space Schema
spaceSchema=Esquema d’espai
#XFLD: Enable Read Access (SQL)
enableReadAccess=Activar accés de lectura (SQL)
#XMSG: Allow the user to grant read access to other users.
withGrantOptionHint=Permeteu a l’usuari concedir accés de lectura a altres usuaris.
#XFLD: With Grant Option
withGrantOption=Amb opció de concessió
#XMSG: Make your space data available in your HDI containers.
enableHDIConsumptionHint=Feu accessibles les dades de l’espai als contenidors HDI.
#XFLD: Enable HDI Consumption
enableHDIConsumption=Activar consum HDI
#XMSG: Write Access to the User’s Open SQL Schema
writeAccessTitle=Accés d’escriptura a l’esquema Open SQL de l’usuari
#XMSG: write access hint
writeAccessHint=Permeteu a l’usuari de base de dades connectar eines externes a l’esquema Open SQL de l’usuari per crear entitats de dades i ingerir dades per utilitzar-les a l’espai.
#XFLD: Open SQL Schema
openSQLSchema=Esquema Open SQL
#XFLD: Enable Write Access (SQL DDL & DML)
enableWriteAccess=Activar accés d’escriptura (SQL, DDL i DML)
#XMSG: audit hint
auditHint=Deseu al log les operacions de lectura i modificació a l’esquema Open SQL.
#XMSG: data consumption hint
dataConsumptionHint=Exposeu per defecte totes les vistes noves de l’espai per al consum. Els modeladors poden substituir aquesta opció en vistes concretes amb el commutador “Exposar per a consum” al panell lateral de sortida de la vista. També podeu seleccionar els formats en què s’exposen les vistes.
#XFLD: Expose for Consumption by Default
exposeDataConsumption=Exposar per a consum per defecte
#XMSG: database users hint consumption hint
databaseUsersHint2New=Creeu usuaris de bases de dades per connectar eines externes a SAP Datasphere. Fixeu privilegis per permetre als usuaris llegir dades de l’espai i crear entitats de dades (DDL) i ingerir dades (DML) per utilitzar-les a l’espai.
#XFLD: Read
read=Lectura
#XFLD: Read (HDI)
readHDI=Lectura (HDI)
#XFLD: Write
write=Escriptura
#XMSG: HDI Containers Hint
HDIContainersHint2=Activeu l’accés als contenidors de SAP HANA Deployment Infrastructure (HDI) al vostre espai. Els modeladors poden utilitzar artefactes HDI com a fonts de vistes i els clients HDI poden accedir a les vostres dades d’espais.
#XMSG: Open info dialog
openDatabaseUserInfoDialog=Obrir el diàleg informatiu
#XMSG: Database user is locked. Open info dialog to unlock
openLockedDatabaseUserInfoDialog=L’usuari de base de dades està bloquejat. Per desbloquejar-lo, obriu el diàleg.
#XFLD: Table
table=Taula
#XFLD: Space section partner configuration title
partnerConnectionConfigurationSectionTitle=Connexió de soci
#XFLD: Space section partner configuration subtitle
partnerConnectionConfigurationSectionSubTitle=Configuració de la connexió de soci
#XFLD: Space section partner configuration hint
partnerConnectionConfigurationSectionHint=Definiu el vostre mosaic de connexió de soci afegint els vostres URL i símbol d'iFrame. Aquesta configuració només està disponible per a aquest arrendatari.
#XFLD: Table Name Field
partnerConnectionConfigurationName=Nom de mosaic
#XFLD: Table iFrame URL field
partnerConnectionConfigurationiFrameURL=URL d'iFrame
#XFLD: Table iFrame Post Message Origin field
partnerConnectionConfigurationiFramePostMessageOrigin=Origen de missatge POST d'iFrame
#XFLD: Table icon/image name
partnerConnectionConfigurationIcon=Símbol
#XMSG: Table no result found
partnerConnectionConfigurationNotfound=No existeix cap configuració de connexió de soci.
#XMSG: Partner Connection Confgurations are not available
partnerConnectionConfigurationNotAvailable=No es poden visualitzar les configuracions de connexió de soci quan no estigui disponible la base de dades del temps d'execució.
#XFLD: Dialog Create Partner Connection Configuration
partnerConnectionConfigurationDialogTitleAdd=Crear configuració de connexió de soci
#XFLD: Dialog Create Partner Connection Configuration Upload Icon
partnerConnectionConfigurationButtonUloadPartnerIcon=Carregar símbol
#XFLD: Dialog Create Partner Connection Configuration Select
partnerConnectionConfigurationSelectIcon=Seleccionar (mida màxima de 200KB)
#XFLD: Dialog Create Partner Connection Configuration Enter a name
partnerConnectionConfigurationNamePlaceholder=Exemple de mosaic de soci
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL
partnerConnectionConfigurationiFrameURLPlaceholder=http://www.example.com
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL Check
partnerConnectionConfigurationiFramePostMessageOriginPlaceholder=.example.com
#XFLD: Dialog Create Partner Connection Configuration Browse
partnerConnectionConfigurationBrowseButton=Navegar
#XFLD: Create Partner Connection Configuration Msg
partnerConnectionConfigurationUploadMsg=La configuració de la connexió de soci s'ha creat correctament.
#XMSG: Error while deleting Partner Connection configuration(s)
partnerConnectionConfigurationDeleteError=S'ha produït un error en suprimir la configuració de la connexió de soci.
#XMSG: Partner Connection Configuration has been successfully deleted
partnerConnectionConfigurationDeleteSuccess=La configuració de la connexió de soci s'ha suprimit correctament.
#XMSG: Error while retrieving Partner Connection Configurations
partnerConnectionConfigurationLoadError=S'ha produït un error en recuperar configuracions de la connexió de soci.
#XMSG: Error fileSizeLimitExceeded
partnerConnectionConfigurationfileSizeLimitExceeded=El fitxer no s’ha pogut carregar perquè supera la mida màxima d’200KB.
#XBUT: Create Partner Connection Configuration
partnerConnectionConfigurationButtonCreate=Crear configuració de connexió de soci
#XBUT: Delete Partner Connection Configuration
partnerConnectionConfigurationButtonDelete=Suprimir configuració de connexió de soci.
#XMSG: Partner Tile Creation Failure
upsertPartnerTileFailed=No s'ha creat el mosaic de soci.
#XMSG: Partner Tile Deletion Failure
deletePartnerTileFailed=No s'ha suprimit el mosaic de soci.
#XMSG: HANA cloud connector settings reset failure
resetHANASCCSettingsFailed=No s'han pogut restablir les opcions de SAP HANA Cloud Connector del client
#XFLD: Workload Class
workloadClass=Classe de càrrega de treball
#XFLD: Workload Management
workloadManagement=Gestió de la càrrega de treball
#XFLD: Priority
workloadClassPriority=Prioritat
#XMSG:
workloadManagementPriorityHint=Podeu especificar la priorització d'aquest espai a l'hora de consultar la base de dades. Introduïu un valor comprès entre 1 (la prioritat més baixa) i 8 (la prioritat més alta). En una situació en què els espais competeixen pels fils disponibles, els que tinguin prioritats més altes s'executaran abans que els espais que tinguin prioritats més baixes.
#XMSG:
workloadClassPriorityHint=Podeu especificar la prioritat de l'espai entre 0 (la més baixa) i 8 (la més alta). Les declaracions d'un espai amb una prioritat alta s'executen abans que les declaracions d'altres espais que tenen una prioritat més baixa. La prioritat predeterminada és 5. Com que el valor 9 està reservat per a les operacions del sistema, no està disponible per a cap espai.
#XFLD: Statement Limits
workloadclassStatementLimits=Límits de declaració
#XFLD: Workload Configuration
workloadConfiguration=Configuració de la càrrega de treball
#XMSG:
workloadClassStatementLimitsHint=Podeu especificar el número màxim (o percentatge) de cadenes i GBs de memòria que poden consumir les declaracions que s'estan executant simultàniament a l'espai. Podeu introduir qualsevol valor o percentatge entre 0 (sense límit) i el total de memòria i cadenes disponibles a l'arrendatari. \n\n Si indiqueu un límit de cadena, tingueu present que se'n pot veure perjudicat el rendiment. \N\n Si indiqueu un límit de memòria, no s'executaran les declaracions que arribin al límit de memòria.
#XMSG:
workloadClassStatementLimitsDescription=La configuració predeterminada ofereix límits de recursos generosos, alhora que evita que qualsevol espai sobrecarregui el sistema.
#XMSG:
workloadClassStatementLimitCustomDescription=Podeu definir límits màxims de memòria i de fils total que les declaracions que s'executin simultàniament a l'espai poden consumir.
#XMSG:
totalStatementThreadLimitHelpText=Definir el límit de fils massa baix pot afectar el rendiment de les declaracions, mentre que uns valors excessivament alts o 0 poden fer que l'espai consumeixi tots els fils disponibles del sistema.
#XMSG:
totalStatementMemoryLimitHelpText=Definir el límit de memòria massa baix pot provocar problemes de falta de memòria, mentre que uns valors excessivament alts o 0 poden fer que l'espai consumeixi tota la memòria disponible del sistema.
#XMSG:
totalStatementThreadLimitHelpTextRestricted=Introduïu un percentatge comprès entre l'1 i el 70% (o el nombre equivalent) del nombre total de fils disponibles al vostre arrendatari. Si fixeu un límit de fils massa baix, pot afectar el rendiment de les declaracions, mentre que uns valors excessivament alts poden afectar el rendiment de les declaracions en altres espais.
#XMSG:
totalStatementThreadLimitHelpTextDynamic=Introduïu un percentatge comprès entre l''1 i el {0}% (o el nombre equivalent) del nombre total de fils disponibles al vostre arrendatari. Si fixeu un límit de fils massa baix, pot afectar el rendiment de les declaracions, mentre que uns valors excessivament alts poden afectar el rendiment de les declaracions en altres espais.
#XMSG:
totalStatementMemoryLimitHelpTextNew=Introduïu un valor o percentatge comprès entre 0 (sense límit) i la quantitat total de memòria disponible a l'arrendatari. Establir el límit de memòria massa baix pot afectar el rendiment de les declaracions, mentre que uns valors excessivament alts poden afectar el rendiment de les declaracions en altres espais.
#XFLD: Total Statement Thread Limit
totalStatementThreadLimit=Límit total de cadena de declaració
#XFLD: Segmented button label
workloadclassPercentage=%
#XFLD: Segmented button label
workloadclassGigaByte=GB
#XFLD, 80: Segmented button label
workloadclassThreads=Cadenes
#XFLD: Total Statement Memory Limit
totalStatementMemoryLimit=Límit total de memòria de declaració
#YMSE: Error while loading customer hana info
loadCustomerHanaInfoError=Error en carregar info de SAP HANA del client.
#XMSG:
minimumLimitReached=Límit mínim assolit.
#XMSG:
maximumLimitReached=Límit màxim assolit.
#XMSG: Name Taken for Technical Name
technical-name-taken=Ja existeix una connexió amb el nom tècnic que heu introduït. Indiqueu-ne un altre.
#XMSG: Name Too long for Technical Name
technical-name-too-long=El nom tècnic que heu introduït té més de 40 caràcters. Indiqueu un nom més curt.
#XMSG: Technical name field empty
technical-name-field-empty=Introduïu un nom tècnic.
#XMSG: Generic error when user enters invalid characters for a field
field-invalid-characters=Només podeu utilitzar lletres (a-z), números (0-9) i caràcters de subratllat (_).
#XMSG: Generic error when user enters invalid initial characters for a field
field-underscore-trim=El nom que indiqueu no pot començar ni acabar amb un caràcter de subratllat (_).
#XFLD: Checkbox label for Enable Statement Limits
enableStatementLimits=Activar límits de declaració
#XFLD: Connection SubSection Settings Title in space details
connectionSettings=Opcions
#XMSG: Connections tool hint in Space details section
connectionsToolHint=Per crear o editar connexions, obriu l'aplicació Connexions des de la navegació lateral o feu clic aquí:
#XBUT: Connection list Open Connection Tool Button
button-connectiontool=Anar a Connexions
#XFLD: Not deployed label on space tile
notDeployedLabel=L'espai encara no s'ha desplegat.
#XFLD: Not deployed additional text on space tile
notDeployedText=Desplegueu l'espai.
#XFLD: Corrupt space label on space tile
corruptSpace=S'ha produït un error.
#XFLD: Corrupt space additional text on space tile
corruptSpaceHint=Proveu de tornar a desplegar-lo o poseu-vos en contacte amb l'equip d'assistència.
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Dades del log d'auditoria
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Dades administratives
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Altres dades
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Dades dels espais
#XMSG: Unlock Space confirmation text
unlockSpaceConfirmation=Segur que voleu desbloquejar l'espai?
#XMSG: Lock Space confirmation text
lockSpaceConfirmation=Segur que voleu bloquejar l'espai?
#XFLD: Lock
lock=Bloquejar
#XFLD: Unlock
unlock=Desbloquejar
#XFLD: Locking
locking=Bloqueig
#XMSG: Success message after locking space
lockSpaceSuccessMsg=Espai bloquejat
#XMSG: Success message after unlocking space
unlockSpaceSuccessMsg=Espai desbloquejat
#XMSG: Success message after locking spaces
lockSpacesSuccessMsg=Espais bloquejats
#XMSG: Success message after unlocking spaces
unlockSpacesSuccessMsg=Espais desbloquejats
#YMSE: Error while locking a space
lockSpaceError=L'espai no es pot bloquejar.
#YMSE: Error while unlocking a space
unlockSpaceError=L'espai no es pot desbloquejar.
#XTIT: popup title Warning
confirmationWarningTitle=Advertència
#XMSG: Message strip message when a space was locked manually
manualLockedMessage=L'espai s'ha bloquejat manualment.
#XMSG: Message strip message when a space was locked due to the amount of generated audit logs
auditLogLockedMessage=El sistema ha bloquejat l'espai perquè els registres d'auditoria consumeixen una gran quantitat de GB de disc.
#XMSG: Message strip message when a space was locked because the assigned quota was exceeded.
quotaLockedMessage=El sistema ha bloquejat l'espai perquè supera les seves assignacions d'emmagatzematge de memòria o de disc.
#XMSG: Unlock Spaces confirmation text
unlockSpacesConfirmation=Segur que voleu desbloquejar els espais seleccionats?
#XMSG: Lock Spaces confirmation text
lockSpacesConfirmation=Segur que voleu bloquejar els espais seleccionats?
#XTIT: Scoped role editor dialog title
scopedRoleEditorDialogTitle=Editor de funció amb abast definit
#XTIT: ECN Management title
ecnManagementTitle=Gestió del node de computació flexible i espai
#XFLD: ECNs
ecns=Nodes de computació flexibles
#XFLD: ECN phase Ready
ecnReady=Preparat
#XFLD: ECN phase Running
ecnRunning=En execució
#XFLD: ECN phase Initial
ecnInitial=No preparat
#XFLD: ECN phase Starting
ecnStarting=Inicial
#XFLD: ECN phase Starting Failed
ecnStartingFailed=Error en iniciar
#XFLD: ECN phase Stopping
ecnStopping=D'aturada
#XFLD: ECN phase Stopping Failed
ecnStoppingFailed=Error en aturar
#XBTN: Assign Button
assign=Assignar espais
#XBTN: Start Header-Button
start=Inici
#XBTN: Update Header-Button
repair=Actualitzar
#XBTN: Stop Header-Button
stop=Aturar
#XFLD: ECN hours remaining
ecnHoursRemaining=1000 hores restants
#XFLD: ECN block-hours remaining
ecnBlockHoursRemaining={0} Hores de bloc restants
#XFLD: ECN block-hour remaining
ecnBlockHourRemaining={0} Hora de bloc restant
#XTIT: ECN creation dialog title
ecnCreationDialogTitle=Crear node de computació flexible
#XTIT: ECN edit dialog title
ecnEditDialogTitle=Editar node de computació flexible
#XTIT: ECN deletion dialog title
ecnDeletionDialogTitle=Eliminar node de computació flexible
#XTIT: ECN Space assignment dialog title
ecnSpaceAssignmentDialogTitle=Assignar espais
#XFLD: ECN ID
ECNIDLabel=Node de computació flexible
#XTXT: Selected toolbar text
selectedToolbarText=Seleccionats: {0}
#XTIT: Elastic Compute Nodes
ECNslong=Nodes de computació flexibles
#XTIT: Object assignment - Table header: Objects available
objectsAvailable=Nombre d'objectes
#XTIT: Object assignment - Dialog header text
selectObjects=Seleccioneu els espais i els objectes que voleu assignar al vostre node de computació flexible:
#XTIT: Object assignment - Table header title: Objects
objects=Objectes
#XTIT: Object assignment - Table header: Type
type=Tipus
#XMSG: Message strip delete database user audit warning
deleteDatabaseUserWarningMessageStrip=Tingueu en compte que en desactivar un usuari de la base de dades se n'eliminaran totes les entrades generades del log d'auditoria. Si voleu conservar els logs d'auditoria, valoreu l'opció d'exportar-los abans d'eliminar l'usuari.
#XMSG: Message strip delete HDI container audit logs impact
deleteHDIContainerWarningMessageStrip=Tingueu en compte que en anul·lar l'assignació d'un contenidor HDI a l'espai, se n'eliminaran totes les entrades generades al log d'auditoria. Si voleu conservar els logs d'auditoria, valoreu l'opció d'exportar-los abans d'anul·lar l'assignació del contenidor HDI.
#XTXT: All audit logs
allAuditLogs=Totes les entrades del log d'auditoria generades per a l'espai
#XMSG: Message strip disable audit warning
disableAuditWarningMessageStrip=Tingueu en compte que en desactivar una política d'auditoria (operacions de lectura o modificació) se n'eliminaran totes les entrades al log d'auditoria. Si voleu conservar les entrades del log d'auditoria, valoreu l'opció d'exportar-les abans de desactivar la política.
#XTIT: No Spaces assigned to ECN Illustration title
noSpacesECNTitle=Encara no s'ha assignat cap espai o objecte
#XTIT: No Spaces assigned to ECN Illustration description
noSpacesECNDescription=Per començar a treballar amb el node de computació flexible, assigneu-hi un espai o objectes.
#XTIT: No Spaces Illustration title
noSpacesTitle=Encara no s'ha creat cap espai
#XTIT: No Spaces Illustration description
noSpacesDescription=Per començar a adquirir dades, creeu un espai.
#XTIT: No Spaces Illustration title
noDeletedSpacesTitle=La paperera de reciclatge està buida
#XTIT: No Spaces Illustration description
noDeletedSpacesDescription=Aquí podeu recuperar els espais suprimits.
#XTXT:  {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDatabaseUsers=Un cop s''hagi desplegat l''espai, s''eliminaran {0} els següents usuaris de bases de dades i no es podran recuperar:
#XTIT: Delete database users
deleteDatabaseUsersTitle=Eliminar usuaris de bases de dades
#YMSE: Error if ecn id already exists
ecnIDInputDuplicateError=L'ID ja existeix.
#YMSE: Error if ecn id contains invalid characters
ecnIDInputInvalidError=Utilitzeu només els caràcters en minúscules a - z i 0 - 9.
#YMSE: Error if ecn id is too short
ecnIDInputTooShortError=L''ID ha de tenir {0} caràcters de longitud com a mínim.
#XMSG: ecn id length warning
ecnIdLengthWarning=S’ha superat el màxim de {0} caràcters.
#XFLD: open System Monitor
systemMonitor=Monitor del sistema
#XFLD: open ECN schedule dialog menu entry
schedule=Programar
#XFLD: open create ECN schedule dialog
createSchedule=Crear programació
#XFLD: open change ECN schedule dialog
changeSchedule=Editar programació
#XFLD: open delete ECN schedule dialog
deleteSchedule=Suprimir programa
#XFLD: open self assign ECN schedule dialog
selfAssignSchedule=Assignar-me programa
#XFLD: open pause ECN schedule dialog
pauseSchedule=Pausar programa
#XFLD: open resume ECN schedule dialog
resumeSchedule=Reprendre programa
#XFLD: View Logs
viewLogs=Veure logs
#XFLD: Compute Blocks
computeBlocks=Blocs de computació
#XFLD: Memory label in ECN creation dialog
ecnMemory=Memòria (GB)
#XFLD: Storage label in ECN creation dialog
ecnStorage=Emmagatzematge (GB)
#XFLD: Number of CPU label in ECN creation dialog
ecnCPUCount=Número de CPU
#XFLD: ECN updated by label
changedBy=Modificat per
#XFLD: ECN updated on label
changedOn=Data de modificació
#XMSG: Success message after creating a Elastic Compute Node
createEcnSuccessMessage=Node de computació flexible creat
#YMSE: Error while creating a Elastic Compute Node
createEcnError=No s'ha pogut crear el node de computació flexible
#XMSG: Success message after updating a Elastic Compute Node
updateEcnSuccessMessage=Node de computació flexible actualitzat
#XMSG: Error while updating a Elastic Compute Node
udpateEcnError=No s'ha pogut actualitzar el node de computació flexible
#XMSG: Success message after deleting a Elastic Compute Node
deleteEcnSuccessMessage=Node de computació flexible suprimit
#YMSE: Error while deleting a Elastic Compute Node
deleteEcnError=No s'ha pogut suprimir el node de computació flexible
#XMSG: Success message after starting a Elastic Compute Node
startingEcnSuccessMessage=S'inicia el node de computació flexible
#XMSG: Success message after stopping a Elastic Compute Node
stoppingEcnSuccessMessage=S'atura el node de computació flexible
#YMSE: Error while starting a Elastic Compute Node
startingEcnError=No s'ha pogut iniciar el node de computació flexible
#YMSE: Error while stopping a Elastic Compute Node
stoppingEcnError=No s'ha pogut aturar el node de computació flexible
#XBUT: Add Object button for an ECN
assignObjects=Afegir objectes
#XFLD: Enable auto object assignment label
enableAutoObjAssignment=Afegir tots els objectes automàticament
#XFLD: object type label to be assigned
objectTypeLabel=Tipus (ús semàntic)
#XFLD: assigned object type label
assignedObjectTypeLabel=Tipus
#XFLD: technical name label
TechnicalNameLabel=Nom tècnic
#XFLD: Object selecttion ECN label
objectSelectionECNLabel=Seleccioneu els objectes que voleu afegir al node de computació flexible.
#XTIT: Add objects dialog title
assignObjectsTitle=Assignar objectes de
#XFLD: object label with object count
objectLabel=Objecte
#XMSG: No objects available to add message.
noObjectsToAssign=No hi ha objectes disponibles per assignar.
#XMSG: No objects assigned message.
noAssignedObjects=No s'ha assignat cap objecte.
#XTIT: Remove objects popup title
removeObjectsOrSpacesConfirmationTitle=Advertència
#XTIT: Delete ECN popup button and title
deleteECNConfirmationButtonAndTitle=Suprimir
#XMSG: Remove objects popup text
removeObjectsConfirmation=Segur que voleu eliminar els objectes seleccionats?
#XMSG: Remove spaces popup text
removeSpacesConfirmation=Segur que voleu eliminar els espais seleccionats?
#XBUT: Confirmation button for objects dialog
removeObjectsOrSpacesConfirmationBtn=Eliminar espais
#XMSG: Success message after removing objects
removeObjectsSuccessMessage=S'han eliminat objectes exposats
#XMSG: Success message after assigning objects
assignObjectsSuccessMessage=S'han assignat objectes exposats
#XFLD: auto object assignment label for space tile
autoObjAssignmentLabel=Tots els objectes exposats
#XFLD: Spaces tab label
spacesTabLabel=Espais
#XFLD: Exposed objects tab label
consumptionObjectsTabLabel=Objectes exposats
#XMSG: Success message after unassigning spaces
spacesUnassignedSuccessMessage=S'han eliminat espais
#XMSG: Success message after unassigning space
spaceUnassignedSuccessMessage=S'ha eliminat l'espai
#YMSE: Error while assigning / unassigning spaces
spacesAssignUnassignError=No s'han pogut assignar o eliminar espais.
#YMSE: Error while removing objects
removeObjectsError=No hem pogut assignar ni eliminar els objectes.
#YMSE: Error while removing object
removeObjectError=No hem pogut assignar ni eliminar l'objecte.
#YMSE: Error when saving the ecn with invalid value
invalidSizingPlanError=El número seleccionat ja no és vàlid. Seleccioneu-ne un de vàlid.
#YMSE: Error when saving the ecn with invalid value
invalidPerformanceClassError=Seleccioneu una classe de rendiment vàlida.
#YMSE: Error when saving the ecn with invalid value
performanceClassNotAvailableError=La classe d''acompliment seleccionada abans "{0}" actualment no és vàlida. Seleccioneu la classe d''acompliment vàlida.
#XTXT: Delete ECN confirmation text
deleteConfirmationTxtECN=Segur que voleu suprimir el node de computació flexible?
#XFLD: tooltip for ? button
help=Ajuda
#XFLD: ECN edit button label
editECN=Configurar
#XFLD: Technical type label for ERModel
DWC_ERMODEL=Model entitat-relació
#XFLD: Technical type label for Local Table
DWC_LOCAL_TABLE=Taula local
#XFLD: Technical type label for Remote Table
DWC_REMOTE_TABLE=Taula remota
#XFLD: Technical type label for Analytic Model
DWC_ANALYTIC_MODEL=Model analític
#XFLD: Technical type label for Task Chain
DWC_TASKCHAIN=Cadena de tasques
#XFLD: Technical type label for Data Flow
DWC_DATAFLOW=Flux de dades
#XFLD: Technical type label for Replication Flow
DWC_REPLICATIONFLOW=Flux de replicació
#XFLD: Technical type label for Transformation Flow
DWC_TRANSFORMATIONFLOW=Flux de transformació
#XFLD: Technical type label for Intelligent Lookup
DWC_IDT=Cerca intel·ligent
#XFLD: Technical type label for Repository
DWC_REPOSITORY_PACKAGE=Dipòsit
#XFLD: Technical type label for Enterprise Search
DWC_ESMODEL=Cerca empresarial
#XFLD: Technical type label for View
DWC_VIEW=Visualitzar
#XFLD: Technical type label for Data Product
DWC_DATAPRODUCT=Producte de dades
#XFLD: Technical type label for Data Access Control
DWC_DAC=Control d'accés de dades
#XFLD: Technical type label for Folder
DWC_FOLDER=Carpeta
#XFLD: Technical type label for Data Entity
DWC_BUSINESS_ENTITY=Entitat empresarial
#XFLD: Technical type label for Business Entity Variant
DWC_BUSINESS_ENTITY_VARIANT=Variant d'entitat empresarial
#XFLD: Technical type label for Responsibility Scenario
DWC_AUTH_SCENARIO=Escenari de dipòsit
#XFLD: Technical type label for Fact Model
DWC_FACT_MODEL=Model de fets
#XFLD: Technical type label for Transformation Flow
DWC_PERSPECTIVE=Perspectiva
#XFLD: Technical type label for Consumption Model
DWC_CONSUMPTION_MODEL=Model de consum
#XFLD: Technical type label for Remote Connection
DWC_REMOTECONNECTION=Connexió remota
#XFLD: Technical type label for Fact Model Variant
DWC_FACT_MODEL_VARIANT=Variant de model de fets
#XMSG: Schedule created alert message
createScheduleSuccess=Programació creada
#XMSG: Schedule updated alert message
updateScheduleSuccess=Programació actualitzada
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Programació suprimida
#XMSG: Schedule takeover success message
ownerChangeScheduleSuccess=Se us ha assignat la programació
#XMSG: Message for starting pausing of schedule
pauseScheduleStarted=S'està pausant una programació
#XMSG: Message for starting resuming of schedule
resumeScheduleStarted=S'està reprenent una programació
#XFLD: Segmented button label
availableSpacesButton=Disponible
#XFLD: Segmented button label
selectedSpacesButton=Seleccionat
#XFLD: Visit website button text
visitWebsite=Visiteu el lloc web
#XMSG: Warning message on unselecting source language
sourceLanguageRemovalWarning=L'idioma font seleccionat abans s'eliminarà.
#XBUT: enable translation confirmation button
enableTranslationConfirmationBtn=Activar
#XFLD: ECN performance class label
performanceClassLabel=Classe de rendiment
#XTXT performance class memory text
memoryText=Memòria
#XTXT performance class compute text
computeText=Càlcul
#XTXT performance class high-compute text
highComputeText=Càlcul superior
#XBUT: Recycle Bin Button Text
recycleBin=Paperera de reciclatge
#XBUT: Restore Button Text
restore=Restaurar
#XMSG: Warning message for new Workload Management UI
priorityWarning=Aquesta àrea és només de lectura. Podeu modificar la prioritat d'espai a l'àrea Sistema / Configuració / Gestió de la càrrega de treball.
#XMSG: Warning message for new Workload Management UI
workloadWarning=Aquesta àrea és només de lectura. Podeu modificar la configuració de la càrrega de treball d'espai a l'àrea Sistema / Configuració / Gestió de la càrrega de treball.
#XCOL: Space table-view column: Apache Spark vCPUs
sparkManagementVCPUs=vCPUs d'Apache Spark
#XCOL: Space table-view column: Apache Spark Memory (GB)
sparkManagementMemory=Memòria d'Apache Spark (GB)
#XCOL: Space type (from Space Capability) for Data Ingestion Spaces
ingestionSpaceType=Ingesta de productes de dades
#XFLD: Deployment in progress nodatatext for spark table
sparkTableDeploymentInProgress=No hi ha dades disponibles perquè s'està desplegant l'espai
#XFLD: Runtime data being loaded nodatatext for spark table
sparkTableDeploymentLoading=No hi ha dades disponibles perquè s'està carregant l'espai
#XCOL: Space type (from Space Capability) for BW Private Cloud Edition
BWPCESpaceType=SAP BW
#XBUT: Go to Instance Mappings Button
navToMappingsButton=Editar assignacions d'instància
#XFLD: SAP HANA Data Lake Storage label if less than 1GB
dataLakeStorageUsed=< 1 GB
