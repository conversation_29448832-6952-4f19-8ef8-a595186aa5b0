#DO NOT REMOVE________________________
#XFLD: Breadcrumb for space monitoring page
spaceMonitorBreadcrumb=Überwachung
#XTXT: Type name for spaces in browser tab page title
space=Space
#_____________________________________
#XFLD: Spaces label in
spaces=Spaces
#XFLD: Manage plan button text
manageQuotaButtonText=<PERSON><PERSON><PERSON> verwalten
#XBUT: Manage resources button
manageResourcesButton=Ressourcen verwalten
#XFLD: Create space button tooltip
createSpace=Space anlegen
#XFLD: Create
create=Anlegen
#XFLD: Deploy
deploy=Aktivieren
#XFLD: Page
page=Seite
#XFLD: Cancel
cancel=Abbrechen
#XFLD: Update
update=Aktualisieren
#XFLD: Save
save=Sichern
#XFLD: OK
ok=OK
#XFLD: days
days=Tage
#XFLD: Space tile edit button label
edit=Bearbeiten
#XFLD: Auto Assign all objects to space
autoAssign=Automatisch zuordnen
#XFLD: Space tile open monitoring button label
openMonitoring=Überwachen
#XFLD: Delete
delete=Löschen
#XFLD: Copy Space
copy=Kopieren
#XFLD: Close
close=Schließen
#XCOL: Space table-view column status
status=Status
#XFLD: Space status active
activeLabel=Aktiv
#XFLD: Space status locked
lockedLabel=Gesperrt
#XFLD: Space status critical
criticalLabel=Kritisch
#XFLD: Space status cold
coldLabel=Kalt
#XFLD: Space status deleted
deletedLabel=Gelöscht
#XFLD: Space status unknown
unknownLabel=Unbekannt
#XFLD: Space status ok
okLabel=Integer
#XFLD: Database user expired
expired=Abgelaufen
#XFLD: deployed
deployed=Aktiviert
#XFLD: not deployed
notDeployed=Nicht aktiviert
#XFLD: changes to deploy
changesToDeploy=Zu aktivierende Änderungen
#XFLD: pending
pending=Wird aktiviert
#XFLD: designtime error
designtimeError=Entwurfszeitfehler
#XFLD: runtime error
runtimeError=Laufzeitfehler
#XFLD: Space created by label
createdBy=Angelegt von
#XFLD: Space created on label
createdOn=Angelegt am
#XFLD: Space deployed on label
deployedOn=Aktiviert am
#XFLD: Space ID label
spaceID=Space-ID
#XFLD: Priority label
priority=Priorität
#XFLD: Space Priority label
spacePriority=Space-Priorität
#XFLD: Space Configuration label
spaceConfiguration=Space-Konfiguration
#XFLD: Not available
notAvailable=Nicht verfügbar
#XFLD: WorkloadType default
default=Standard
#XFLD: WorkloadType custom
custom=Benutzerdefiniert
#XFLD: Data Lake option label; refers to component SAP HANA Cloud, Data Lake
dataLakeAccess=Data-Lake-Zugriff
#XFLD: Translation label
translationLabel=Übersetzung
#XFLD: Source language label
sourceLanguageLabel=Quellsprache
#XFLD: Translation CheckBox label
translationCheckBox=Übersetzung aktivieren
#XTXT: Popover hint that space has to be deployed to show user details
openDatabaseUserHintPopoverText=Space aktivieren, um auf Benutzerdetails zuzugreifen.
#XTXT: Popover hint that space has to be deployed to open database explorer
openDatabaseExplorerHintPopoverText=Space aktivieren, um Datenbank-Explorer zu öffnen.
#XTXT: Text for when the Data Lake check box is disabled
dataLakeOptionDisabled=Sie können diesen Space nicht für den Zugriff auf den Data Lake verwenden, da er bereits von einem anderen Space verwendet wird.
#XFLD: Data Lake check box text; refers to component SAP HANA Cloud, Data Lake
dataLakeCheckBox=Diesen Space für den Zugriff auf den Data Lake verwenden
#XFLD: Space Priority minimum label extension
low=Niedrig
#XFLD: Space Priority maximum label extension
high=Hoch
#XFLD: Space name label
spaceName=Space-Name
#XFLD: Enable deploy objects checkbox
enableDeployObjects=Objekte aktivieren
#XTIT: Copy spaces dialog title
copySpaceDialogTitle={0} kopieren
#XTXT Human readable text for unselected source language
unSelectedSourceLanguage=(Nicht ausgewählt)
#XTXT Human readable text for language code "af"
af=Afrikaans
#XTXT Human readable text for language code "ar"
ar=Arabisch
#XTXT Human readable text for language code "bg"
bg=Bulgarisch
#XTXT Human readable text for language code "ca"
ca=Katalanisch
#XTXT Human readable text for language code "zh"
zh=Chinesisch (vereinfacht)
#XTXT Human readable text for language code "zf"
zf=Chinesisch
#XTXT Human readable text for language code "hr"
hr=Kroatisch
#XTXT Human readable text for language code "cs"
cs=Tschechisch
#XTXT Human readable text for language code "cy"
cy=Walisisch
#XTXT Human readable text for language code "da"
da=Dänisch
#XTXT Human readable text for language code "nl"
nl=Niederländisch
#XTXT Human readable text for language code "en-UK"
en-UK=Englisch (Vereinigtes Königreich)
#XTXT Human readable text for language code "en"
en=Englisch (USA)
#XTXT Human readable text for language code "et"
et=Estnisch
#XTXT Human readable text for language code "fa"
fa=Persisch
#XTXT Human readable text for language code "fi"
fi=Finnisch
#XTXT Human readable text for language code "fr-CA"
fr-CA=Französisch (Kanada)
#XTXT Human readable text for language code "fr"
fr=Französisch
#XTXT Human readable text for language code "de"
de=Deutsch
#XTXT Human readable text for language code "el"
el=Griechisch
#XTXT Human readable text for language code "he"
he=Hebräisch
#XTXT Human readable text for language code "hi"
hi=Hindi
#XTXT Human readable text for language code "hu"
hu=Ungarisch
#XTXT Human readable text for language code "is"
is=Isländisch
#XTXT Human readable text for language code "id"
id=Bahasa Indonesia
#XTXT Human readable text for language code "it"
it=Italienisch
#XTXT Human readable text for language code "ja"
ja=Japanisch
#XTXT Human readable text for language code "kk"
kk=Kasachisch
#XTXT Human readable text for language code "ko"
ko=Koreanisch
#XTXT Human readable text for language code "lv"
lv=Lettisch
#XTXT Human readable text for language code "lt"
lt=Litauisch
#XTXT Human readable text for language code "ms"
ms=Bahasa Melayu
#XTXT Human readable text for language code "no"
no=Norwegisch
#XTXT Human readable text for language code "pl"
pl=Polnisch
#XTXT Human readable text for language code "pt"
pt=Portugiesisch (Brasilien)
#XTXT Human readable text for language code "pt-PT"
pt-PT=Portugiesisch (Portugal)
#XTXT Human readable text for language code "ro"
ro=Rumänisch
#XTXT Human readable text for language code "ru"
ru=Russisch
#XTXT Human readable text for language code "sr"
sr=Serbisch
#XTXT Human readable text for language code "sh"
sh=Serbokroatisch
#XTXT Human readable text for language code "sk"
sk=Slowakisch
#XTXT Human readable text for language code "sl"
sl=Slowenisch
#XTXT Human readable text for language code "es"
es=Spanisch
#XTXT Human readable text for language code "es-MX"
es-MX=Spanisch (Mexiko)
#XTXT Human readable text for language code "sv"
sv=Schwedisch
#XTXT Human readable text for language code "th"
th=Thailändisch
#XTXT Human readable text for language code "tr"
tr=Türkisch
#XTXT Human readable text for language code "uk"
uk=Ukrainisch
#XTXT Human readable text for language code "vi"
vi=Vietnamesisch
#XTIT: Delete spaces confirmation title
deleteConfirmationHeaderText=Spaces löschen
#XTXT: Logically delete space delete confirmation text with space name
logicallyDeleteConfirmationBodyTextWithSpaceName=Möchten Sie den Space "{0}" wirklich in den Papierkorb verschieben?
#XTXT: Logically dpace delete confirmation text for multiple spaces
logicallyDeleteConfirmationBodyTextMultiple=Möchten Sie die {0} ausgewählten Spaces wirklich in den Papierkorb verschieben?
#XTXT: Delete space delete confirmation text with space name
deleteConfirmationBodyTextWithSpaceName=Möchten Sie den Space "{0}" wirklich löschen? Diese Aktion kann nicht rückgängig gemacht werden.
#XTXT: Delete space delete confirmation text for multiple spaces
deleteConfirmationBodyTextMultiple=Möchten Sie die {0} ausgewählten Spaces wirklich löschen? Diese Aktion kann nicht rückgängig gemacht werden. Der folgende Inhalt wird {1} gelöscht.
#XTXT: permanently
permanently=endgültig
#XTXT: {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDetail=Der folgende Inhalt wird gelöscht und kann nicht wiederhergestellt werden: {0}
#XTXT: Hint Delete space confirmation text
deleteConfirmationHint=Geben Sie {0} ein, um die Löschung zu bestätigen.
#XTXT: Delete space confirmation error check spelling
deleteConfirmationErrorCheckSpelling=Überprüfen Sie die Schreibweise und versuchen Sie es erneut.
#XTXT: All Spaces
allSpaces=alle Spaces
#XTXT: All data
allData=alle im Space enthaltenen Objekte und Daten
#XTXT: All connections
allConnections=alle im Space definierten Verbindungen
#XFLD: Space tile selection box tooltip
clickToSelect=Zum Auswählen klicken
#XTXT: All database users
allDatabaseUsers=alle Objekte und Daten, die in einem mit dem Space verknüpften Open-SQL-Schema enthalten sind
#XFLD: remove members button tooltip
deleteUsers=Mitglieder entfernen
#XTXT: Space long description text
description=Beschreibung (maximal 4000 Zeichen)
#XFLD: Add Members button tooltip
addUsers=Mitglieder hinzufügen
#XFLD: Add Users button tooltip
addUsersTooltip=Benutzer hinzufügen
#XFLD: Edit Users button tooltip
editUsersTooltip=Benutzer bearbeiten
#XFLD: Remove Users button tooltip
removeUsersTooltip=Benutzer entfernen
#XFLD: Searchfield placeholder
filter=Suchen
#XCOL: Users table-view column health
health=Fehlerfreiheit
#XCOL: Users table-view column access
access=Zugriff
#XFLD: No user found nodatatext
noDataText=Kein Benutzer gefunden
#XTIT: Members dialog title
selectUserDialogTitle=Mitglieder hinzufügen
#XTIT: User dialog title
addUserDialogTitle=Benutzer hinzufügen
#XTIT: Delete connections confirmation title
deleteConnectionConfirmationHeaderText=Verbindungen löschen
#XTIT: Delete single connection confirmation title
deleteConnectionConfirmationHeaderTextSingle=Verbindung löschen
#XTXT: Delete connection delete confirmation text
deleteConnectionConfirmationBodyText=Möchten Sie die markierten Verbindungen wirklich löschen? Sie werden endgültig entfernt.
#XTIT: Remote source dialog title
selectRemoteConnectionDialogTitle=Verbindungen auswählen
#XTIT: Share connection dialog title
connectionSharingDialogTitle=Verbindung teilen
#XTIT: Connections shared from other spaces table title
sharedConnectionsFromOtherSpaces=Geteilte Verbindungen
#XFLD: Add remote source button tooltip
addRemoteConnections=Verbindungen hinzufügen
#XFLD: Delete remote source button tooltip
deleteRemoteConnections=Verbindungen entfernen
#XFLD: Share remote source button tooltip
shareConnections=Verbindungen teilen
#XFLD: Tile-layout tooltip
tileLayout=Kachellayout
#XFLD: Table-layout tooltip
tableLayout=Tabellenlayout
#XMSG: Success message after creating space
createSpaceSuccessMessage=Space wurde angelegt.
#XMSG: Success message after copying space
copySpaceSuccessMessage=Space "{0}" wird in Space "{1}" kopiert
#XMSG: Success message after deploying space
deploymentSuccessMessage=Space-Aktivierung gestartet
#XMSG: Success message after Apache Spark update started
sparkUpdateStartedSuccessMessage=Apache-Spark-Aktualisierung gestartet
#YMSE: Error while starting Apache Spark update
sparkUpdateStartedErrorMessage=Aktualisierung von Apache Spark fehlgeschlagen
#XMSG: Success message after updating space
updateSettingsSuccessMessage=Space-Details wurden aktualisiert.
#XMSG: Success message after unlocking space
unlockSpaceSuccessMessage=Space vorübergehend entsperrt
#XMSG: Success message after deleting a single space
deleteSpaceSuccessMessage=Space gelöscht
#XMSG: Success message after deleting multiple spaces
deleteSpacesSuccessMessage=Spaces gelöscht
#XMSG: Success message after restoring a single space
restoreSpaceSuccessMessage=Space wiederhergestellt
#XMSG: Success message after restoring multiple spaces
restoreSpacesSuccessMessage=Spaces wiederhergestellt
#YMSE: Error while updating settings
updateSettingsFailureMessage=Space-Einstellungen konnten nicht aktualisiert werden.
#YMSE: Error while updating data lake option
dataLakeEnabledInOtherSpace=Der Data Lake ist bereits einem anderen Space zugeordnet. Auf den Data Lake kann nur von jeweils von einem Space zugegriffen werden.
#YMSE: Error while updating data lake option
virtualTablesExists=Sie können die Zuordnung des Data Lake zu diesem Space nicht aufheben, da er noch Abhängigkeiten zu virtuellen Tabellen* aufweist. Löschen Sie die virtuelle Tabelle, um die Zuordnung des Data Lake zu diesem Space aufzuheben.
#YMSE: Error while unlocking space
unlockSpaceFailureMessage=Der Space konnte nicht entsperrt werden.
#YMSE: Error while creating space
createSpaceError=Der Space konnte nicht angelegt werden.
#YMSE: Error while creating duplicate space
createDuplicateSpaceError=Ein Space mit dem Namen {0} ist bereits vorhanden.
#YMSE: Error while deleting a single space
deleteSpaceError=Der Space konnte nicht gelöscht werden.
#YMSE: Error details while deleting a single space
deleteSpaceDetailedError=Ihr Space "{0}" funktioniert nicht mehr ordnungsgemäß. Versuchen Sie, ihn zu löschen. Sollte er immer noch nicht funktionieren, bitten Sie Ihren Administrator, den Space zu löschen, oder geben Sie eine Meldung auf.
#YMSE: Error while deleting a single space in files
deleteSpaceInFilesError=Die Space-Daten unter "Dateien" konnten nicht gelöscht werden.
#YMSE: Error while deleting a single space users
deleteSpaceUsersError=Die Benutzer konnten nicht entfernt werden.
#YMSE: Error while deleting a single space schema
deleteSpaceSchemasError=Die Schemas konnten nicht entfernt werden.
#YMSE: Error while deleting a single space connections
deleteSpaceConnectionsError=Die Verbindungen konnten nicht entfernt werden.
#YMSE: Error while deleting a single space data
deleteSpaceDataError=Die Space-Daten konnten nicht gelöscht werden.
#YMSE: Error while deleting multiple spaces
deleteSpacesError=Die Spaces konnten nicht gelöscht werden.
#YMSE: Error while restoring a single space
restoreSpaceError=Der Space konnte nicht wiederhergestellt werden.
#YMSE: Error while restoring multiple spaces
restoreSpacesError=Die Spaces konnten nicht wiederhergestellt werden.
#YMSE: Error while creating users
createUsersError=Die Benutzer konnten nicht hinzugefügt werden.
#YMSE: Error while removing users
removeUsersError=Die Benutzer konnten nicht entfernt werden.
#YMSE: Error while removing user
removeUserError=Der Benutzer konnte nicht entfernt werden.
#YMSE: Error assigning roles to a user
roleAssignUnAssignError=Wir konnten den Benutzer nicht zur ausgewählten Anwendungsbereichsrolle hinzufügen. \n\n Sie können sich selbst nicht zu einer Anwendungsbereichsrolle hinzufügen. Sie können Ihren Administrator darum bitten, Sie zu einer Anwendungsbereichsrolle hinzuzufügen.
#YMSE: Error assigning user to the space
userAssignError=Wir konnten den Benutzer nicht dem Space zuordnen. \n\n Der Benutzer ist bereits der maximal zulässigen Anzahl (100) von Spaces in verschiedenen Anwendungsbereichsrollen zugewiesen.
#YMSE: Error assigning users to the space
usersAssignError=Wir konnten die Benutzer nicht dem Space zuordnen. \n\n Die Benutzer sind bereits der maximal zulässigen Anzahl (100) von Spaces in verschiedenen Anwendungsbereichsrollen zugewiesen.
#YMSE: Error while fetching users
fetchUserWithScopedRolesError=Die Benutzer konnten nicht abgerufen werden. Versuchen Sie es später erneut.
#YMSE: Error while fetching scoped roles
fetchScopedRolesError=Die Anwendungsbereichsrollen konnten nicht abgerufen werden.
#YMSE: Error while fetching members
fetchUserError=Die Mitglieder konnten nicht abgerufen werden. Versuchen Sie es später erneut.
#YMSE: Error while loading run-time database
loadRuntimeError=Informationen aus der Laufzeitdatenbank konnten nicht geladen werden.
#YMSE: Error while loading spaces
loadSpacesError=Beim Versuch, Ihre Spaces abzurufen, ist ein Fehler aufgetreten.
#YMSE: Error while loading haas resources
loadStorageError=Beim Versuch Ihre Speicherdaten abzurufen, ist ein Fehler aufgetreten.
#YMSE: Error no data could be loaded
loadDataError=Beim Versuch, Ihre Daten abzurufen, ist ein Fehler aufgetreten.
#XFLD: Click to refresh storage data
clickToRefresh=Hier klicken, um es erneut zu versuchen.
#XTIT: Delete space popup title
deleteSpacePopupTitle=Space löschen
#XCOL: Spaces table-view column name
name=Name
#XCOL: Spaces table-view deployment status
deploymentStatus=Aktivierungsstatus
#XFLD: Disk label in space details
storageLabel=Festplatte (GB)
#XFLD: In-Memory label in space details
ramLabel=Arbeitsspeicher (GB)
#XFLD: Memory label on space card
memory=Arbeitsspeicher für Speicherung
#XFLD: Storage Assignment label in space details
storrageAssignmentLabel=Space-Speicherplatz
#XFLD: Storage Type label in space details
storageTypeLabel=Speichertyp
#XFLD: Enable Space Quota
enableSpaceQuota=Space-Kontingent aktivieren
#XFLD: No Space Quota
noSpaceQuota=Kein Space-Kontingent
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
diskAndInMemoryText=SAP-HANA-Datenbank (Festplatte und In-Memory)
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
dataLakeFilesText=SAP-HANA-Data-Lake-Dateien
#XFLD: Available scoped roles label
availableRoles=Verfügbare Anwendungsbereichsrollen
#XFLD: Selected scoped roles label
selectedRoles=Ausgewählte Anwendungsbereichsrollen
#XCOL: Spaces table-view column models
models=Modelle
#XCOL: Spaces table-view column users
users=Benutzer
#XCOL: Spaces table-view column connections
connections=Verbindungen
#XFLD: Section header overview in space detail
overview=Übersicht
#XFLD: Section header Apache Spark in space detail
sparkManagementSection=Apache Spark
#XFLD: Section header Apache Spark in space detail
sparkManagementSubSection=Anwendungen
#XFLD: Section header Apache Spark in space detail
taskAssignmentSubSection=Aufgabenzuordnung
#XFLD: vCPU label in Apache Spark section
vCPULabel=vCPUs
#XFLD: Memory label in Apache Spark section
memoryLabel=Arbeitsspeicher (GB)
#XFLD: space configuration label in Apache Spark section
spaceConfigurationLabel=Space-Konfiguration
#XFLD: Space Source label
sparkApplicationLabel=Anwendung
#XFLD: Cluster Size label
clusterSizeLabel=Cluster-Größe
#XFLD: Driver label
driverLabel=Treiber
#XFLD: Executor label
executorLabel=Ausführer
#XFLD: max label
maxLabel=Max. Verwendung
#XFLD: TrF Default label
trFDefaultLabel=Transformationsfluss – Standard
#XFLD: Merge Default label
mergeDefaultLabel=Zusammenführen – Standard
#XFLD: Optimize Default label
optimizeDefaultLabel=Standard optimieren
#XFLD: Deployment Default label
deploymentDefaultLabel=Aktivierung der lokalen Tabelle (Datei)
#XFLD: max spark source label
driverExecutorMaxUsedSparkSource={0} CPU / {1} GB
#XFLD: Object type label
taskObjectTypeLabel=Objekttyp
#XFLD: Task activity label
taskActivityLabel=Aktivität
#XFLD: Task Application ID label
taskApplicationIDLabel=Standardanwendung
#XFLD: Section header in space detail
generalSettings=Allgemeine Einstellungen
#XMSG: Message strip message when a space is locked
spaceLockedMessageStrip=Dieser Space ist gegenwärtig durch das System gesperrt.
#XMSG: Message strip message deploy warning
deployWarningMessageStrip=Änderungen in diesem Abschnitt werden sofort aktiviert.
#XMSG: Message strip message modify space configuration warning
modifySpaceConfigurationMessageStrip=Beachten Sie, dass durch das Ändern der Werte Performance-Probleme auftreten können.
#XFLD: Button text to unlock the space again
unlockSpace=Space entsperren
#XFLD: Info text for audit log formatted message
auditLogText=Aktivieren Sie Audit-Protokolle, um Lese- oder Änderungsaktionen aufzuzeichnen (Audit-Richtlinien). Administratoren können dann analysieren, wer wann welche Aktion ausgeführt hat.
#XMSG: Message strip  audit warning
auditLogWarningMessageStrip=Audit-Protokolle können sehr viel Speicher beanspruchen. Wenn Sie eine Audit-Richtlinie (Lese- oder Änderungsaktionen) aktivieren, sollten Sie die Verwendung des Festplattenspeichers regelmäßig überwachen (über die Karte "Verwendeter Festplattenspeicher" im Systemmonitor), um Betriebsstörungen infolge von vollem Festplattenspeicher zu vermeiden. Wenn Sie eine Audit-Richtlinie deaktivieren, werden alle zugehörigen Audit-Protokolleinträge gelöscht. Wenn Sie die Audit-Protokolleinträge beibehalten möchten, sollten Sie sie vor dem Deaktivieren der Audit-Richtlinie exportieren.
#XFLD: Message strip link text to open in app help to guide user what to do to unlock his space permanently
unlockSpaceHelp=Hilfe anzeigen
#XMSG: Message strip message when a space will be locked again
spaceTemporarilyUnlockedMessageStrip=Dieser Space überschreitet den Space-Speicherplatz und wird in {0} {1} gesperrt.
#XMSG: Unit for remaining time until space is locked again
hours=Stunden
#XMSG: Unit for remaining time until space is locked again
minutes=Minuten
#XFLD: Section header in space detail for auditing
detailsSectionAuditing=Auditing
#XFLD: Subsection header in space detail for auditing
auditing=Einstellungen für Space-Audit
#XFLD: Hot space tooltip
hotSpaceCountTooltip=Kritische Spaces: Verwendeter Speicher liegt über 90 %.
#XFLD: Green space tooltip
greenSpaceCountTooltip=Integre Spaces: Verwendeter Speicher liegt zwischen 6 % und 90 %.
#XFLD: Cold space tooltip
coldSpaceCountTooltip=Kalte Spaces: Verwendeter Speicher beträgt 5 % oder weniger.
#XFLD: Critical space tooltip
criticalSpaceCountTooltip=Kritische Spaces: Verwendeter Speicher liegt über 90 %.
#XFLD: Green space tooltip
okSpaceCountTooltip=Integre Spaces: Verwendeter Speicher liegt zwischen 6 % und 90 %.
#XFLD: Locked space tooltip
lockedSpaceCountTooltipNew=Gesperrte Spaces: Wegen zu wenig Speicherplatz gesperrt.
#XFLD: Locked space tooltip
lockedSpaceCountTooltip=Gesperrte Spaces
#YMSE: Error while deleting remote source
deleteRemoteError=Die Verbindungen konnten nicht entfernt werden.
#XFLD: Space ID input tooltip
spaceIDInfoTooltip=Space-ID kann später nicht mehr geändert werden.\nGültige Zeichen: A–Z, 0–9 und _
#YMSE: Error when space name is empty
spaceNameEmptyValidationError=Space-Name eingeben
#YMSE: Error when ECN Business name is empty
ecnNameEmptyValidationError=Betriebswirtschaftlichen Namen eingeben
#YMSE: Error when space id is empty
spaceIDInputEmptyValidationError=Space-ID eingeben
#YMSE: Error when space id contains invalid characters
spaceIDInputInValidError=Ungültige Zeichen. Verwenden Sei nur A–Z, 0–9 und _.
#YMSE: Error when space id exists while creation
spaceIDInputDuplicateError=Space-ID ist bereits vorahnden.
#XFLD: Space searchfield placeholder
search=Suchen
#XMSG: Success message after creating users
createUsersSuccess=Benutzer hinzugefügt
#XMSG: Success message after creating user
createUserSuccess=Benutzer hinzugefügt
#XMSG: Success message after updating users
updateUsersSuccess={0} Benutzer aktualisiert
#XMSG: Success message after updating user
updateUserSuccess=Benutzer aktualisiert
#XMSG: Success message after removing users
removeUsersSuccess={0} Benutzer entfernt
#XMSG: Success message after removing user
removeUserSuccess=Benutzer entfernt
#XFLD: Schema name
schemaName=Schemaname
#XFLD: used of total
ofTemplate={0} von {1}
#XFLD: Assigned disk label in space overview
assignedStorageTemplate=Verfügbarer Festplattenspeicher ({0} von {1})
#XFLD: Assigned Memory label in space overview
assignedRAMTemplate=Verfügbarer Arbeitsspeicher ({0} von {1})
#XFLD: Storage ratio on space
accelearationRAM=Arbeitsspeicher-Beschleunigung
#XFLD: No Storage Consumption
noStorageConsumptionText=Kein Speicherplatzkontingent zugeordnet.
#XFLD: Used disk label in space overview
usedStorageTemplate=Für Speicherung verwendeter Festplattenspeicher ({0} von {1})
#XFLD: Used Memory label in space overview
usedRAMTemplate=Für Speicherung verwendeter Arbeitsspeicher ({0} von {1})
#XFLD: Used Disk tooltip
usedStorageTooltipTemplate={0} von {1} Festplattenspeicher für Speicherung verwendet
#XFLD: Used Memory tooltip
usedRAMTooltipTemplate={0} von {1} Arbeitsspeicher verwendet
#XFLD: Assigned Disk tooltip
assignedStorageTooltipTemplate={0} von {1} Festplattenspeicher verfügbar
#XFLD: Assigned Memory tooltip
assignedRAMTooltipTemplate={0} von {1} Arbeitsspeicher verfügbar
#XFLD: Space related used storage tooltip
spaceRelatedUsedStorageTooltipTemplate=Space-Daten: {0}
#XFLD: Not space related used storage tooltip
otherDataUsedStorageTooltipTemplate=Andere Daten: {0}
#XFLD: Storage used exceeded popover and tooltip
storageUsedExceededText=Erweitern Sie den Plan ggf., oder wenden Sie sich an den SAP-Support.
#XCOL: Space table-view column used Disk
usedStorage=Für Speicherung verwendeter Festplattenspeicher
#XCOL: Space monitor column used Memory
usedRAM=Für Speicherung verwendeter Arbeitsspeicher
#XCOL: Space monitor column Schema
tableSchema=Schema
#XCOL: Space monitor column Storage Type
tableStorageType=Speichertyp
#XCOL: Space monitor column Table Type
tableType=Tabellentyp
#XCOL: Space monitor column Record Count
tableRecordCount=Anzahl Datensätze
#XFLD: Assigned Disk
assignedStorage=Für Speicherung zugeordneter Festplattenspeicher
#XFLD: Assigned Memory
assignedRAM=Für Speicherung zugeordneter Arbeitsspeicher
#XCOL: Space table-view column storage utilization
tableStorageUtilization=Verwendeter Speicherplatz
#XFLD: space status
spaceStatus=Space-Status
#XFLD: space type
spaceType=Space-Typ
#XFLD: space type label for space with space type "SAP BW bridge" ("SAP BW bridge" is a product name, do not translate it)
abapbridgeSpaceType=SAP-BW-Bridge
#XFLD: space type label for space with space type "Data Marketplace"
marketplaceSpaceType=Datenanbieter-Produkt
#XMSG: Error message for when selected space cannot be deleted due to the space type
deleteTypedSpaceError=Sie können den Space {0} nicht löschen, da sein Space-Typ {1} lautet.
#XMSG: Error message for when selected spaces cannot be deleted due to the space type
deleteTypedSpacesError=Sie können die {0} ausgewählten Spaces nicht löschen. Spaces mit den folgenden Space-Typen können nicht gelöscht werden: {1}.
#XFLD: space type label for space without space type
defaultSpaceType=SAP Datasphere
#XFLD: Tooltip for monitor space button
monitorSpace=Überwachen
#XFLD: Tooltip for edit space button
editSpace=Space bearbeiten
#XMSG: Deletion warning in messagebox
deleteConfirmation=Möchten Sie diesen Space wirklich löschen?
#XFLD: Tooltip for delete space button
deleteSpace=Space löschen
#XFLD: storage
storage=Festplattenspeicher für Speicherung
#XFLD: username
userName=Benutzername
#XFLD: port
port=Port
#XFLD: hostname
hostName=Hostname
#XFLD: password
password=Kennwort
#XBUT: Request new password button
requestPassword=Neues Kennwort anfordern
#YEXP: Usage explanation in time data section
timeDataSectionHint=Legen Sie Zeittabellen und -dimensionen zur Verwendung in Ihren Modellen und Storys an.
#YEXP: Usage explanation in Space schema section
spaceSchemaHint=Sollen die Daten in Ihrem Space auch von anderen Tools und Apps verwendet werden können? Dann legen Sie mindestens einen Benutzer an, der auf die Daten in Ihrem Space zugreifen kann, und legen Sie fest, ob auch alle künftigen Daten des Space standardmäßig verwendet werden dürfen.
#XTIT: Create schema popup title
createSchemaDialogTitle=Open-SQL-Schema anlegen
#XTIT: Generate Time Data dialog title
generateTimeDataDialogTitle=Zeittabellen und -dimensionen anlegen
#XTIT: Edit Time Data dialog title
editTimeDataDialogTitle=Zeittabellen und -dimensionen bearbeiten
#XTIT: Time Data token title
timeDataTokenTitle=Zeitdaten
#XTIT: Time Data token title
timeDataUpdateViews=Zeitdaten-Views aktualisieren
#XFLD: Time Data token creation in progress label
timeDataCreationInProgress=Erstellvorgang läuft …
#XFLD: Time Data token creation error label
timeDataCreationError=Anlegen fehlgeschlagen. Versuchen Sie es erneut.
#XTIT: Time Data dialog time settings title
timeDataDialogTimeSettingsTitle=Einstellungen für Zeittabelle
#XTIT: Time Data dialog time data table title
timeDataDialogTranslationTablesTitle=Umwandlungstabelle
#XTIT: Time Data dialog dimensions title
timeDataDialogTimeViewsTitle=Zeitdimensionen
#XFLD: Time Data dialog time range label
timeRangeHint=Definieren Sie einen Zeitraum.
#XFLD: Time Data dialog time data table label
timeDataHint=Weisen Sie der Tabelle einen Namen zu.
#XFLD: Time Data dialog time data dimensions label
timeDimensionsHint=Weisen Sie der Dimension einen Namen zu.
#XFLD: Time Data Time range description label
timerangeLabel=Zeitraum
#XFLD: Time Data dialog from year label
fromYearLabel=Startjahr
#XFLD: Time Data dialog to year label
toYearLabel=Endejahr
#XFLD: Time Data dialog calendar type label
calendarTypeLabel=Kalendertyp
#XFLD: Time Data dialog granularity label
granularityLabel=Granularität
#XFLD: Time Data dialog technical name label
technicalNameLabel=Technischer Name
#XFLD: Time Data dialog quarters text table lable
quarterTextsTableLabel=Umwandlungstabelle für Quartale
#XFLD: Time Data dialog months text table lable
monthsTextsTableLabel=Umwandlungstabelle für Monate
#XFLD: Time Data dialog days text table lable
daysTextsTableLabel=Umwandlungstabelle für Tage
#XFLD: Time Data dialog year label
yearLabel=Dimension "Jahr"
#XFLD: Time Data dialog quarter label
quarterLabel=Dimension "Quartal"
#XFLD: Time Data dialog month label
monthLabel=Dimension "Monat"
#XFLD: Time Data dialog day label
dayLabel=Dimension "Tag"
#XFLD: Time Data dialog gregorian calendar type label
gregorian=Gregorianisch
#XFLD: Time Data dialog time granularity day label
day=Tag
#XFLD: Time Data dialog business name maxlength reached
timeRangeValueStateTextMaxLengthBusinessName=Max. Länge von 1000 Zeichen erreicht
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextMaxRangeExceeded=Der maximale Zeitraum beträgt 150 Jahre.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeLower=Startjahr muss vor Endejahr liegen
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeHigher=Startjahr muss mindestens 1900 entsprechen
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeHigher=Endejahr muss nach Startjahr liegen
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeLower=Endejahr muss vor dem aktuellen Jahr plus 100 liegen
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningFromYear=Das Erhöhen des Startjahres kann zum Datenverlust führen.
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningToYear=Das Verringern des Endejahre kann zum Datenverlust führen.
#XMSG: Time Data creation validation error message
timeDataValidationError=Einige Felder sind offenbar ungültig. Überprüfen Sie die erforderlichen Felder, um fortfahren zu können.
#XMSG: Time Data deletion popup text
deleteTimeDataConfirmation=Möchten Sie die Daten wirklich löschen?
#XMSG: Time Data creation success message
createTimeDataSuccess=Zeitdaten angelegt
#XMSG: Time Data update success message
updateTimeDataSuccess=Zeitdaten aktualisiert
#XMSG: Time Data delete success message
deleteTimeDataSuccess=Zeitdaten gelöscht
#XMSG: Time Data creation error message
createTimeDataError=Beim Anlegen der Zeitdaten ist ein Fehler aufgetreten.
#XMSG: Time Data update error message
updateTimeDataError=Beim Aktualisieren der Zeitdaten ist ein Fehler aufgetreten.
#XMSG: Time Data creation error message
deleteTimeDataError=Beim Löschen der Zeitdaten ist ein Fehler aufgetreten.
#XMSG: Error message when time data could not be retrieved
retrievingTimeDataError=Die Zeitdaten konnten nicht geladen werden.
#XTIT: Time Data deletion popup title
deleteTimeDataConfirmationTitle=Warnung
#XTXT: Time Data deletion dependency popup text
deleteTimeDataDependencyText=Ihre Zeitdaten konnten nicht gelöscht werden, da sie in anderen Modellen verwendet werden.
#XTXT: Time Data deletion dependency popup text singular
deleteTimeDataDependencyTextSingular=Ihre Zeitdaten konnten nicht gelöscht werden, da sie in einem anderen Modell verwendet werden.
#XMSG: Validation error when required time data form field is empty
timeDataRequiredField=Dieses Feld ist erforderlich und darf nicht leer gelassen werden.
#XFLD: Tooltip for Open in Database Explorer button
openInDBX=Im Datenbank-Explorer öffnen
#YMSE: Dimension Year
dimensionYearView=Dimension "Jahr"
#YMSE: Dimension Year
dimensionQuarterView=Dimension "Quartal"
#YMSE: Dimension Year
dimensionMonthView=Dimension "Monat"
#YMSE: Dimension Year
dimensionDayView=Dimension "Tag"
#XFLD: Time Data deletion object title
timeDataUsedIn=(wird in {0} Modellen verwendet)
#XFLD: Time Data deletion object title singular
timeDataUsedInSingular=(wird in 1 Modell verwendet)
#XFLD: Time Data deletion table column provider
provider=Anbieter
#XTIT: Time Data dependency dialog title
timeDataDependencyDialogTitle=Abhängigkeiten
#XTIT: Create schema popup title
createSpaceSchemaUserDialogTitle=Benutzer für Space-Schema anlegen
#XFLD: Create schema button
createSchemaButton=Open-SQL-Schema anlegen
#XFLD: Generate TimeData button
generateTimeDataButton=Zeittabellen und -dimensionen anlegen
#XFLD: Show dependencies button
showDependenciesButton=Abhängigkeiten anzeigen
#XFLD: tooltip for disabled buttons when user is not member of space
mustBeMemberOfSpaceForOperation=Um diesen Vorgang ausführen zu können, muss Ihr Benutzer ein Mitglied des Space sein.
#XFLD: Create space schema user button
createSpaceSchemaUserButton=Benutzer für Space-Schema anlegen
#YMSE: API Schema users load error
loadSchemaUsersError=Benutzerliste konnte nicht geladen werden.
#XTIT: API Schema-config popup title
schemaUserDetailsDialogTitle=Details zu Space-Schemabenutzer
#XMSG: API Schema user deletion popup text
deleteSpaceSchemaUserConfirmation=Möchten Sie den ausgewählten Benutzer wirklich löschen?
#XMSG: API Schema user deletion confirmation text
userDeleteSuccess=Benutzer gelöscht.
#YMSE: API Schema user deletion error
userDeleteError=Benutzer konnte nicht gelöscht werden.
#XFLD: User deleted
userDeleted=Der Benutzer wurde gelöscht.
#XTIT: Remove user popup title
removeUserConfirmationTitle=Warnung
#XMSG: Remove user popup text
removeUserConfirmation=Möchten Sie den Benutzer wirklich entfernen? Der Benutzer und die ihm zugeordneten Anwendungsbereichsrollen werden aus dem Space entfernt.
#XMSG: Remove users popup text
removeUsersConfirmation=Möchten Sie die Benutzer wirklich entfernen? Die Benutzer und die ihnen zugeordneten Anwendungsbereichsrollen werden aus dem Space entfernt.
#XBUT: Confirmation button for remove user dialog
removeUserConfirmationBtn=Entfernen
#YMSE: No data text for available roles
noDataAvailableRoles=Der Space wurde keiner Anwendungsbereichsrolle hinzugefügt. \n Um dem Space Benutzer hinzufügen zu können, muss er zunächst mindestens einer Anwendungsbereichsrolle hinzugefügt werden.
#YMSE: No data text for selected roles
noDataSelectedRoles=Keine Anwendungsbereichsrollen ausgewählt
#XTIT: Schema-config popup title
schemaDetailsDialogTitle=Konfigurationsdetails zu Open-SQL-Schema
#XFLD: Label for Read Audit Log
auditLogRead=Audit-Protokoll für Lesevorgänge aktivieren
#XFLD: Label for Change Audit Log
auditLogChange=Audit-Protokoll für Änderungsvorgänge aktivieren
#XFLD: Label Audit Log Retention
auditLogRetention=Aufbewahrung von Protokollen:
#XFLD: Label Audit Log Retention Unit
retentionUnit=Tage
#YMSE: Error when log retention time is set incorrectly
incorrectRetentionTime=Geben Sie eine Ganzzahl zwischen {0} und {1} ein.
#XFLD: Label for Cross Space Schema Access
spaceSchemaAccess=Space-Schemadaten verwenden
#XTIT: Confirm deactivation of space schema consumption dialog title
disableSpaceSchemaConsumptionDialogTitle=Verwendung von Space-Schemadaten stoppen
#XTXT: Confirm deactivation of space schema consumption dialog text
disableSpaceSchemaConsumptionDialogText=Dieses Open-SQL-Schema verwendet unter Umständen Daten Ihres Space-Schemas. Wenn Sie die Verwendung stoppen, funktionieren Modelle, die auf diesen Space-Schemadaten basieren, ggf. nicht mehr.
#XBUT: Confirmation button for the stop consuming space schema data dialog
stopConsuming=Verwendung stoppen
#XTXT: Tooltip for data lake icon
dataLakeIconTooltip=Dieser Space wird für den Zugriff auf den Data Lake verwendet
#XFLD: Tooltip for data lake table entry
dataLakeTableEntryTooltip=Data Lake aktiviert
#XFLD: used In-Memory linit reached tag
usedRamLimitReachedLabel=Arbeitsspeichergrenze erreicht
#XFLD: Space storage Limit reached tag
storageLimitReachedLabel=Speicherlimit erreicht
#XFLD: Minimal Storage Limit Reached tag
minStorageLimitReachedLabel=Mindestspeicherlimit erreicht
#XFLD: Space ram tag
ramLimitReachedLabel=Arbeitsspeichergrenze erreicht
#XFLD: Minimal In-Memory Limit Reached tag
minRamLimitReachedLabel=Arbeitsspeicher-Mindestgrenze erreicht
#XFLD: Popover text after click on space storage limit warning
storageLimitReachedText=Sie haben das zugeordnete Speicherlimit von {0} für den Space erreicht. Ordnen Sie dem Space weiteren Speicherplatz zu.
#XFLD: System storage tag
systemStorageLimitReachedLabel=System-Speicherlimit erreicht
#XMSG: Popover text after click on storage limit warning
systemStorageLimitReachedText=Sie haben das Speicherlimit des Systems von {0} erreicht. Sie können dem Space keinen weiteren Speicher zuordnen.
#XMSG: Schema deletion warning
deleteSchemaConfirmation=Wenn Sie dieses Open-SQL-Schema löschen, werden auch alle im Schema gespeicherten Objekte und Assoziationen gelöscht. Fortfahren?
#XMSG: Success message after deleting a schema
schemaDeleteSuccess=Schema gelöscht
#YMSE: Error while deleting schema.
schemaDeleteError=Schema konnte nicht gelöscht werden.
#XMSG: Success message after update a schema
schemaUpdateSuccess=Schema aktualisiert
#YMSE: Error while updating schema.
schemaUpdateError=Schema konnte nicht aktualisiert werden.
#XMSG: Hint when no password is shown in the dialog.
newPasswordHint=Für dieses Schema wurde bereits ein Kennwort zur Verfügung gestellt. Wenn Sie Ihr Kennwort vergessen oder verloren haben, können Sie ein neues Kennwort anfordern. Kopieren oder sichern Sie das neue Kennwort.
#XMSG: Hint when showing the password in the dialog.
savePasswordHint=Kopieren Sie Ihr Kennwort. Sie benötigen es, um eine Verbindung zu diesem Schema herzustellen. Wenn Sie Ihr Kennwort vergessen haben, können Sie es über dieses Dialogfeld zurückzusetzen.
#XFLD,50: Hint on entering schema name in creation dialog
schemaNameHint=Der Schemaname kann nicht mehr geändert werden.
#XFLD: Open SQL Schemas section sub headline
schemasSQL=Open SQL
#XFLD: Space schema section sub headline
schemasSpace=Space
#XFLD: HDI Container section header
HDIContainers=HDI-Container
#XTXT: Add HDI Containers button tooltip
addHDIContainers=HDI-Container hinzufügen
#XTXT: Remove HDI Containers button tooltip
removeHDIContainers=HDI-Container entfernen
#XFLD: Enable HDI Containers feature button
enableHDIContainersButton=Zugriff aktivieren
#YMSE: No data text for HDI Containers table
noDataHDIContainers=Es wurden keine HDI-Container hinzugefügt.
#YMSE: No data text for Timedata section
noDataTimedata=Es wurden keine Zeittabellen und -dimensionen angelegt.
#YMSE: No data text during runtime not available for Timedata section
runtimeNotAvailableNoDataTimedata=Zeittabellen und -dimensionen können nicht geladen werden, da die Laufzeitdatenbank nicht verfügbar ist.
#YMSE: No data text during runtime not available for HDI section
runtimeNotAvailableNoDataHDI=HDI-Container können nicht geladen werden, da die Laufzeitdatenbank nicht verfügbar ist.
#YMSE: Error while fetching HDI Containers
fetchHDIContainersError=Die HDI-Container konnten nicht abgerufen werden. Versuchen Sie es später erneut.
#XFLD Table column header for HDI Container names
HDIContainerName=Name des HDI-Containers
#XFLD: Enable HDI Containers feature dialog title
enableHDIContainersDialogTitle=Zugriff aktivieren
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText1New=Sie können SAP SQL Data Warehousing für Ihren SAP-Datasphere-Tenant aktivieren, um Daten zwischen Ihren HDI-Containern und Ihren SAP-Datasphere-Spaces auszutauschen, ohne dass Daten verschoben werden müssen.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText2=Geben Sie hierzu eine Support-Meldung auf, indem Sie auf die Schaltfläche unten klicken.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText3New=Wenn die Meldung bearbeitet wurde, müssen Sie in der SAP-Datasphere-Laufzeitdatenbank einen oder mehrere neue HDI-Container erstellen. Anschließend wird die Schaltfläche "Zugriff aktivieren" in der HDI-Container-Auswahl für all Ihre SAP-Datasphere-Spaces durch die Schaltfläche "+" ersetzt, und Sie können die Container einem Space zuordnen.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText41=Sie benötigen weitere Informationen? Dann wechseln Sie zu %%0. Detaillierte Information dazu, was in der Meldung enthalten sein sollte, finden Sie unter %%1.
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkText=SAP-Hilfe
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkTextSapNote3057059=SAP-Hinweis 3057059
#XBUT: Open Ticket Button Text
openTicket=Meldung öffnen
#XBUT: Add Button Text
add=Hinzufügen
#XBUT: Next Button Text
next=Weiter
#XBUT: Edit Button Text
editUsers=Bearbeiten
#XBUT: create user Button Text
createUser=Anlegen
#XBUT: Update user Button Text
updateUser=Auswählen
#XTIT: Add HDI Containers Dialog Title
addHDIContainersDialogTitle=Nicht zugeordnete HDI-Container hinzufügen
#XFLD: No data text for add HDI container dialog
addHDIContainersDialogNoDataText=Es wurden keine nicht zugeordneten Container gefunden. \n Der gesuchte Container ist möglicherweise bereits einem Space zugeordnet.
#YMSE: Error while loading assigned HDI Containers
loadAssignedHDIContainersError=Zugeordnete HDI-Container konnten nicht geladen werden.
#YMSE: Error while loading assigned HDI Containers
loadUnassignedHDIContainersError=HDI-Container konnten nicht geladen werden.
#XMSG: Success message
succeededToAddHDIContainer=HDI-Container hinzugefügt
#XMSG: Success message
succeededToAddHDIContainerPlural=HDI-Container hinzugefügt
#XMSG: Success message
succeededToDeleteHDIContainer=HDI-Container entfernt
#XMSG: Success message
succeededToDeleteHDIContainerPlural=HDI-Container entfernt
#XFLD: Time data section sub headline
timeDataSection=Zeittabellen und -dimensionen
#XFLD: Schemas list entry read audit log enabled indicator
readIndicator=Lesen
#XFLD: Schemas list entry change audit log enabled indicator
changeIndicator=Ändern
#XFLD: Remote sources section sub headline
allconnections=Verbindungszuordnung
#XFLD: Remote sources section sub headline
localconnections=Lokale Verbindungen
#XFLD: Remote sources section sub headline for open Connectors
openConnectors=SAP Open Connectors
#XFLD: User section sub headline
memberassignment=Mitgliedszuordnung
#XFLD: User assignment section sub headline
userAssignment=Benutzerzuordnung
#XFLD: User section Access dropdown Member
member=Mitglied
#XFLD: User assignment section column name
user=Benutzername
#XTXT: Selected role count
selectedRoleToolbarText=Ausgewählt: {0}
#XTIT: Space detail section connections title
detailsSectionConnections=Verbindungen
#XTIT: Space detail section data access title
detailsSectionDataAccess=Schemazugriff
#XTIT: Space detail section time data title
detailsSectionGenerateData=Zeitdaten
#XTIT: Space detail section members title
detailsSectionUsers=Mitglieder
#XTIT: Space detail section Users title
detailsSectionUsersTitle=Benutzer
#XTIT: Out of Storage
insufficientStoragePopoverTitle=Speicher voll
#XTIT: Storage distribution
storageDistributionPopoverTitle=Verwendeter Festplattenspeicher
#XTXT: Out of Storage popover text
insufficientStorageText=Damit Sie einen neuen Space anlegen können, müssen Sie den zugewiesenen Speicherplatz eines anderen Space verringern oder einen Space löschen, den Sie nicht mehr benötigen. Mit der Option "Tarif verwalten" können Sie den Systemspeicher insgesamt erweitern.
#XMSG: Space id length warning
spaceIdLengthWarning=Maximum von {0} Zeichen überschritten.
#XMSG: Space name length warning
spaceNameLengthWarning=Maximum von {0} Zeichen überschritten.
#XMSG: Space id reserved prefix warning
spaceIdnWarningReservedPrefix=Um mögliche Konflikte zu vermeiden, verwenden Sie nicht das Präfix {0}.
#YMSE: Error while loading open SQL schemas
loadLocalSchemasError=Open-SQL-Schemas konnten nicht geladen werden.
#YMSE: Error while creating open SQL schema
createLocalSchemaError=Open-SQL-Schema konnte nicht geladen werden.
#YMSE: Error while loading all remote connections
loadRemoteConnectionsError=Nicht alle Remote-Verbindungen konnten geladen werden.
#YMSE: Error while loading space details
loadSpaceDetailsError=Die Space-Details konnten nicht geladen werden.
#YMSE: Error while deploying space details
deploySpaceDetailsError=Der Space konnte nicht aktiviert werden.
#YMSE: Error while copying space details
copySpaceDetailsError=Der Space konnte nicht kopiert werden.
#YMSE: Error while loading storage data
loadStorageDataError=Die Speicherdaten konnten nicht geladen werden.
#YMSE: Error while loading all users
loadAllUsersError=Nicht alle Benutzer konnten geladen werden.
#YMSE: Failed to reset password
resetPasswordError=Kennwort konnte nicht zurückgesetzt werden.
#XMSG: Success message after updating space
resetPasswordSuccessMessage=Neues Kennwort für Schema festgelegt
#YMSE: DP Agent-name too long
DBAgentNameError=Name des DP-Agenten ist zu lang.
#YMSE: Schema-name not valid.
schemaNameError=Schemaname ist ungültig.
#YMSE: User name not valid.
UserNameError=Benutzername ist ungültig.
#XTIT: Consumption by Storage Type
consumptionStorageTypeText=Verbrauch nach Speichertyp
#XTIT: Consumption by Schema
consumptionSchemaText=Verbrauch nach Schema
#XTIT: Overall Consumption by schema
overallConsumptionSchemaText=Tabellenverbrauch nach Schema insgesamt
#XTIT: Overall Consumption by Table Type
overallConsumptionTableTypeText=Gesamtverbrauch nach Tabellentyp
#XTIT: Tables
tableDetailsText=Tabellendetails
#XTIT: Table Storage Consumption
tableStorageConsumptionText=Tabellenspeicherverbrauch
#XFLD: Table Type label
tableTypeLabel=Tabellentyp
#XFLD: Schema label
schemaLabel=Schema
#XFLD: reset table tooltip
resetTable=Tabelle zurücksetzen
#XFLD: In-Memory label in space monitor
inMemoryLabel=Arbeitsspeicher
#XFLD: Disk label in space monitor
diskLabel=Festplatte
#XFLD: Yes
yesLabel=Ja
#XFLD: No
noLabel=Nein
#XFLD: switch label: Want the data in this space to be consumable by default?
allowConsumptionByDefault=Sollen die Daten in diesem Space standardmäßig verwendet werden dürfen?
#XFLD: Business Name
businessNameLabel=Betriebswirtschaftlicher Name
#XFLD: Refresh
refresh=Aktualisieren
#XMSG: No filter results title
noFilterResultsTitle=Durch Ihre Filtereinstellungen werden keine Daten angezeigt.
#XMSG: No filter results message
noFilterResultsMsg=Versuchen Sie, Ihre Filtereinstellungen zu verfeinern. Wenn dann immer noch keine Daten angezeigt werden, legen Sie Tabellen im Data Builder an. Sobald diese Speicherplatz beanspruchen, können Sie sie hier überwachen.
#XMSG: Space monitoring Hana state red title
spaceMonitorHanaRedTitle=Die Laufzeitdatenbank ist nicht verfügbar.
#XMSG: Space monitoring Hana state red message
spaceMonitorHanaRedMsg=Da die Laufzeitdatenbank nicht verfügbar ist, sind bestimmte Funktionen deaktiviert und es können keine Informationen auf dieser Seite angezeigt werden.
#YMSE: Error while creating space schema user
createSpaceSchemaUserError=Space-Schemabenutzer konnte nicht angelegt werden.
#YMSE: Error User name already exists
userAlreadyExistsError=Benutzername ist bereits vorhanden.
#YMSE: Error Authentication failed
authenticationFailedError=Authentifizierung fehlgeschlagen
#XMSG: Message strip message when a schema user is locked
userLockedMessageStrip=Der Benutzer wurde aufgrund von zu vielen fehlgeschlagenen Anmeldeversuchen gesperrt. Fordern Sie ein neues Kennwort an, um den Benutzer zu entsperren.
#XMSG: Success message after reset and unlock schema user
resetUnlockPasswordSuccessMessage=Neues Kennwort festgelegt und Benutzer entsperrt
#XMSG: user is locked message
userLockedMessage=Der Benutzer ist gesperrt.
#XCOL: Users table-view column Role
spaceRole=Rolle
#XCOL: Users table-view column Scoped Role
spaceScopedRole=Anwendungsbereichsrolle
#XCOL: Users table-view column Space Admin
spaceAdmin=Space-Administrator
#XFLD: User section dropdown value Viewer
viewer=Viewer
#XFLD: User section dropdown value Modeler
modeler=Modeler
#XFLD: User section dropdown value Data Integrator
dataIntegrator=Daten-Integrator
#XFLD: User section dropdown value Space Administrator
spaceAdministrator=Space-Administrator
#XMSG: Success message -  Space Role updated successfully.
updateUserSpaceRoleSuccess=Space-Rolle aktualisiert
#XMSG: Error message - Failed to update Space Role.
updateUserSpaceRoleError=Space-Rolle konnte nicht aktualisiert werden.
#XFLD:
databaseUserNameSuffix=Suffix für Datenbankbenutzernamen
#XTXT: Space Schema password text
spaceSchemaPasswordText=Um eine Verbindung zu diesem Schema herzustellen, kopieren Sie Ihr Kennwort. Falls Sie es vergessen sollten, können Sie jederzeit ein neues anfordern.
#XFLD: Cloud Platform label in space schema dialog
cloudPlatformLabel=SAP Cloud Platform
#XTXT: Cloud Platform text in space schema dialog
cloudPlatformText=Um den Zugriff über diesen Benutzer einzurichten, aktivieren Sie die Nutzung und kopieren die Anmeldeinformationen. Falls Sie Ihre Anmeldeinformationen nur ohne Kennwort kopieren können, müssen Sie das Kennwort später eingeben.
#XFLD: enable consumption in CloudPlatform label in space schema dialog
enableConsumptionInCloudPlatformLabel=Nutzung in SAP Cloud Platform aktivieren
#XFLD: Credentials for User-Provided Service label in space schema dialog
cloudPlatformCredentialsLabel=Anmeldeinformationen für benutzereigenen Service:
#XFLD: Credentials for User-Provided Service (Without Password) label in space schema dialog
cloudPlatformCredentialsWithoutPasswordLabel=Anmeldeinformationen für benutzereigenen Service (ohne Kennwort):
#XFLD: Copy Credentials label in space schema dialog
copyCredentialsLabel=Anmeldeinformationen ohne Kennwort kopieren
#XFLD: Copy Credentials label in space schema dialog
copyFullCredentialsLabel=Vollständige Anmeldeinformationen kopieren
#XFLD: Copy Password label in space schema dialog
copyPasswordLabel=Kennwort kopieren
#XMSG: Credentials copied to clipboard
credentialsCopiedMessage=Anmeldeinformationen in Zwischenablage kopiert
#XMSG: Password copied to clipboard
passwordCopiedMessage=Kennwort in Zwischenablage kopiert
#XMSG: create database user dialog title
createDatabaseUserDialogTitle=Datenbankbenutzer anlegen
#XMSG: Database Users section title
databaseUsers=Datenbankbenutzer
#XMSG: Database User Details dialog title
databaseUserDetailsDialogTitle=Datenbankbenutzerdetails
#XFLD: database user read audit log
databaseUserAuditLogRead=Audit-Protokolle für Lesevorgänge aktivieren und Protokolle speichern für
#XFLD: database user change audit log
databaseUserAuditLogChange=Audit-Protokolle für Änderungsvorgänge aktivieren und Protokolle speichern für
#XMSG: Cloud Platform Access
cloudPlatformAccess=Zugriff auf SAP Cloud Platform
#XFLD: Enable Consumption in Cloud Platform hint
enableConsumptionCloudPlatformHint=Richten Sie den Zugriff auf Ihren SAP-HANA-Deplyoment-Infrastructure-Container (HDI-Container) über diesen Datenbankbenutzer ein. Um eine Verbindung zu Ihrem HDI-Container herzustellen, muss die SQL-Modellierung aktiviert sein.
#XFLD: Enable HDI Consumption
enableConsumptionCloudPlatform=HDI-Nutzung aktivieren
#XFLD: Enable Consumption hint
enableConsumptionHint=Sollen die Daten in Ihrem Space von anderen Tools und Apps verwendet werden dürfen?
#XFLD: Enable Consumption
enableConsumption=SQL-Nutzung aktivieren
#XFLD: Enable Modeling
enableModeling=SQL-Modellierung aktivieren
#XMSG: Privileges for Data Modeling
privilegesModeling=Datenaufnahme
#XMSG: Privileges for Data Consumption
privilegesConsumption=Datennutzung durch externe Tools
#XFLD: SQL Modeling
sqlModeling=SQL-Modellierung
#XFLD: SQL Consumption
sqlConsumption=SQL-Nutzung
#XFLD: enabled
enabled=Aktiviert
#XFLD: disabled
disabled=Deaktiviert
#XFLD: Edit Privileges
editPrivileges=Berechtigungen bearbeiten
#XFLD: Open Database Explorer
openDBX=Datenbank-Explorer öffnen
#XFLD: create database user hint
databaseCreateHint=Beachten Sie, dass der Name nach dem Sichern nicht mehr geändert werden kann.
#XFLD: Internal Schema Name
internalSchemaName=Interner Schemaname
#YMSE: Failed to load database users
loadDatabaseUserError=Laden von Datenbankbenutzern fehlgeschlagen
#YMSE: Failed to delete database user
deleteDatabaseUsersError=Löschen von Datenbankbenutzern fehlgeschlagen
#XMSG: Success message after deleting database user
deleteDatabaseUserSuccess=Datenbankbenutzer gelöscht
#XMSG: Success message after deleting database users
deleteDatabaseUsersSuccess=Datenbankbenutzer gelöscht
#XMSG: Success message after creating database user
createDatabaseUserSuccessMessage=Datenbankbenutzer angelegt
#YMSE: Failed to create database user
createDatabaseUserError=Anlegen des Datenbankbenutzers fehlgeschlagen
#XMSG: Success message after updating database user
updateDatabaseUserSuccessMessage=Datenbankbenutzer aktualisiert
#YMSE: Failed to update database user
updateDatabaseUserError=Aktualisieren des Datenbankbenutzers fehlgeschlagen
#XFLD: HDI Consumption
hdiConsumption=HDI-Nutzung
#XTIT: Space detail section Database Access title
detailsSectionDatabaseAccess=Datenbankzugriff
#XTXT: Database users consumption hint
databaseUsersConsumptionHint=Ermöglichen Sie die Nutzung der Daten in Ihrem Space standardmäßig. Dadurch lassen die Modelle in allen Builders automatisch die Verwendung der Daten zu.
#XFLD: Default Consumption of Space Data:
defaultConsumptionOfSpaceData=Standardnutzung von Space-Daten:
#XFLD: Database User Name
databaseUserName=Datenbankbenutzername
#XMSG: Database User creation validation error message
databaseUserValidationError=Einige Felder sind offenbar ungültig. Überprüfen Sie die erforderlichen Felder, um fortfahren zu können.
#XMSG: SQL modeling can’t be enabled since this user has been migrated.
migratedDatabaseUserHint=Die Datenerfassung kann nicht aktiviert werden, da dieser Benutzer migriert wurde.
#XBUT: Remove Button Text
remove=Entfernen
#XBUT: Remove Spaces Button Text
removeSpaces=Spaces entfernen
#XBUT: Remove Objects Button Text
removeObjects=Objekte entfernen
#XMSG: No members have been added yet.
noMembersAssigned=Es wurden noch keine Mitglieder hinzugefügt.
#XMSG: No users have been added yet.
noUsersAssigned=Es wurden noch keine Benutzer hinzugefügt.
#XMSG: No database users have been created or your filter is not showing any data.
noDatabaseUsers=Es wurden keine Datenbankbenutzer angelegt, oder Ihr Filter zeigt keine Daten an.
#XMSG: Please enter a user name.
noDatabaseUsername=Geben Sie einen Benutzernamen ein.
#XMSG: The user name is too long. Please use a shorter one.
databaseUsernameTooLong=Der Benutzername ist zu lang. Geben Sie einen kürzeren ein.
#XMSG: No privileges have been enabled and this database user will have limited functionality. Do you still want to continue?
noPrivilegesConfirmationMessage=Es wurden keine Berechtigungen aktiviert, sodass dieser Datenbankbenutzer nur begrenzte Funktionen aufweist. Möchten Sie trotzdem fortfahren?
#XMSG: To enable audit logs for change operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogChangeMessage=Um Audit-Protokolle für Änderungsvorgänge zu aktivieren, muss die Datenerfassung ebenfalls aktiviert werden. Möchten Sie die Datenerfassung aktivieren?
#XMSG: To enable audit logs for read operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogReadMessage=Um Audit-Protokolle für Lesevorgänge zu aktivieren, muss die Datenerfassung ebenfalls aktiviert werden. Möchten Sie die Datenerfassung aktivieren?
#XMSG: To enable HDI consumption data ingestion and data consumption needs to be enabled. Do you want to enable all?
enableHDIConsumptionMessage=Um die HDI-Nutzung zu aktivieren, müssen auch die Datenerfassung und die Datennutzung aktiviert werden. Möchten Sie die Datenerfassung und die Datennutzung aktivieren?
#XMSG:
databaseUserPasswordText=Um eine Verbindung zu diesem Datenbankbenutzer einzurichten, kopieren Sie Ihr Kennwort. Falls Sie Ihr Kennwort vergessen sollten, können Sie jederzeit ein neues anfordern.
#XTIT: Space detail section members title
detailsSectionMembers=Mitglieder
#XMSG: New password set
newPasswordSet=Neues Kennwort festgelegt
#XFLD: Data Ingestion
dataIngestion=Datenerfassung
#XFLD: Data Consumption
dataConsumption=Datennutzung
#XFLD: Privileges
privileges=Berechtigungen
#XFLD: Enable Data ingestion
enableDataIngestion=Datenerfassung aktivieren
#XMSG: Log the read and change operations for data ingestion.
databaseUserAuditHint=Protokollieren Sie die Lese- und Änderungsvorgänge für die Datenerfassung.
#XMSG: Make your space data available in your HDI containers.
databaseUserHDIHint=Stellen Sie die Daten in Ihrem Space in Ihren HDI-Containern zur Verfügung.
#XFLD: Enable Data consumption
enableDataConsumption=Datennutzung aktivieren
#XMSG: Allow other apps or tools to consume your space data.
enableDataConsumptionHint=Erlauben Sie, dass andere Apps oder Tools auf die Daten in Ihrem Space zugreifen können.
#XMSG: To set up access via this schema user copy the credentials to your user-provided service. In case you can only copy the credentials without a password make sure to add the password later.
databaseUserHDIConsumptionHint=Um den Zugriff über diesen Datenbankbenutzer einzurichten, kopieren Sie die Anmeldeinformationen in Ihren benutzereigenen Service. Falls Sie Ihre Anmeldeinformationen nur ohne Kennwort kopieren können, müssen Sie das Kennwort später eingeben.
#XFLD: Data Flow Runtime Capacity label in space overview
dataFlowRuntimeCapacity=Datenfluss-Laufzeitkapazität ({0}:{1} Stunden von {2} Stunden)
#XFLD: Could not load Data Flow Runtime Capacity
dataFlowRuntimeCapacityError=Datenfluss-Laufzeitkapazität konnte nicht geladen werden
#XMSG: User can grant data consumption to other users.
databaseUserConsumptionWithGrantOptionHint=Der Benutzer kann anderen Benutzern die Datennutzung gewähren.
#XFLD: Enable data consumption with grant option
enableConsumptionWithGrantOption=Datennutzung mit Option zur Erteilung von Berechtigungen aktivieren
#XMSG: To enable data consumption with grant option data consumption needs to be enabled. Do you want to enable both?
enableConsumptionWithGrantMessage=Um die Datennutzung mit Option zur Erteilung von Berechtigungen zu aktivieren, muss die Datennutzung aktiviert sein. Möchten Sie beides aktivieren?
#XFLD: Enable Automated Predictive Library (APL) and Predictive Analysis Library (PAL)
allowUsingScriptServerFunctionality=Automated Predictive Library (APL) und Predictive Analysis Library (PAL) aktivieren
#XMSG: User can use SAP HANA Cloud embedded machine learning functions.
allowUsingScriptServerFunctionalityHint=Der Benutzer kann die in SAP HANA Cloud integrierten Funktionen für maschinelles Lernen verwenden.
#XFLD: Password Policy
passwordPolicy=Kennwortrichtlinie
#XMSG: Password Policy
passwordPolicyHint=Aktivieren oder deaktivieren Sie die konfigurierte Kennwortrichtlinie hier.
#XFLD: Enable Password Policy
enablePasswordPolicy=Kennwortrichtlinie aktivieren
#XMSG: Read Access to the Space Schema
readAccessTitle=Lesezugriff auf das Space-Schema
#XMSG: read access hint
readAccessHint=Erlauben Sie dem Datenbankbenutzer, externe Werkzeuge mit dem Space-Schema zu verbinden und Views anzuzeigen, die für die Verwerndung verfügbar gemacht wurden.
#XFLD: Space Schema
spaceSchema=Space-Schema
#XFLD: Enable Read Access (SQL)
enableReadAccess=Lesezugriff aktivieren (SQL)
#XMSG: Allow the user to grant read access to other users.
withGrantOptionHint=Erlauben Sie dem Benutzer, anderen Benutzern Lesezugriff zu erteilen.
#XFLD: With Grant Option
withGrantOption=Mit Option zur Erteilung von Berechtigungen
#XMSG: Make your space data available in your HDI containers.
enableHDIConsumptionHint=Stellen Sie die Daten in Ihrem Space in Ihren HDI-Containern zur Verfügung.
#XFLD: Enable HDI Consumption
enableHDIConsumption=HDI-Nutzung aktivieren
#XMSG: Write Access to the User’s Open SQL Schema
writeAccessTitle=Schreibzugriff für das Open-SQL-Schema des Benutzers
#XMSG: write access hint
writeAccessHint=Erlauben Sie dem Datenbankbenutzer, externe Werkzeuge mit dem Open-SQL-Schema des Benutzers zu verbinden, um Datenentitäten anzulegen und Daten zur Verwendung im Space zu erfassen.
#XFLD: Open SQL Schema
openSQLSchema=Open-SQL-Schema
#XFLD: Enable Write Access (SQL DDL & DML)
enableWriteAccess=Schreibzugriff aktivieren (SQL, DDL und DML)
#XMSG: audit hint
auditHint=Protokollieren Sie die Lese- und Änderungsvorgänge im Open-SQL-Schema.
#XMSG: data consumption hint
dataConsumptionHint=Machen Sie alle neuen Views im Space standardmäßig für die Verwendung verfügbar. Modeler können diese Einstellung über den Schalter "Für die Verwendung verfügbar machen" im Seitenbereich der View-Ausgabe für einzelne Views überschreiben. Sie können auch auswählen, in welchem Format Views verfügbar gemacht werden.
#XFLD: Expose for Consumption by Default
exposeDataConsumption=Standardmäßig für die Verwerndung verfügbar machen
#XMSG: database users hint consumption hint
databaseUsersHint2New=Legen Sie Datenbankbenutzer an, um externe Werkzeuge mit SAP Datasphere zu verbinden. Richten Sie Berechtigungen ein, um Benutzern zu erlauben, Space-Daten zu lesen und Datenentitäten (DDL) anzulegen sowie Daten zur Verwendung im Space zu erfassen (DML).
#XFLD: Read
read=Lesen
#XFLD: Read (HDI)
readHDI=Lesen (HDI)
#XFLD: Write
write=Schreiben
#XMSG: HDI Containers Hint
HDIContainersHint2=Erlauben Sie Zugriff auf die Container Ihrer SAP HANA Deployment Infrastructure (HDI) in Ihrem Space. Modeler können HDI-Artefakte als Quelle für Views verwenden, und HDI-Clients können auf Ihre Space-Daten zugreifen.
#XMSG: Open info dialog
openDatabaseUserInfoDialog=Info-Dialogfeld öffnen
#XMSG: Database user is locked. Open info dialog to unlock
openLockedDatabaseUserInfoDialog=Datenbankbenutzer ist gesperrt. Öffnen Sie das Dialogfeld, um ihn zu entsperren.
#XFLD: Table
table=Tabelle
#XFLD: Space section partner configuration title
partnerConnectionConfigurationSectionTitle=Partnerverbindung
#XFLD: Space section partner configuration subtitle
partnerConnectionConfigurationSectionSubTitle=Partnerverbindungskonfiguration
#XFLD: Space section partner configuration hint
partnerConnectionConfigurationSectionHint=Definieren Sie eine eigene Partnerverbindungskachel, indem Sie die iFrame-URL und das entsprechende Symbol hinzufügen. Die Konfiguration ist nur für diesen Tenant verfügbar.
#XFLD: Table Name Field
partnerConnectionConfigurationName=Kachelname
#XFLD: Table iFrame URL field
partnerConnectionConfigurationiFrameURL=iFrame-URL
#XFLD: Table iFrame Post Message Origin field
partnerConnectionConfigurationiFramePostMessageOrigin=Herkunft iFrame-PostMessage
#XFLD: Table icon/image name
partnerConnectionConfigurationIcon=Symbol
#XMSG: Table no result found
partnerConnectionConfigurationNotfound=Es wurde keine Konfiguration für Partnerverbindungen gefunden.
#XMSG: Partner Connection Confgurations are not available
partnerConnectionConfigurationNotAvailable=Partnerverbindungskonfigurationen können nicht angezeigt werden, wenn die Laufzeitdatenbank nicht verfügbar ist.
#XFLD: Dialog Create Partner Connection Configuration
partnerConnectionConfigurationDialogTitleAdd=Konfiguration für Partnerverbindung anlegen
#XFLD: Dialog Create Partner Connection Configuration Upload Icon
partnerConnectionConfigurationButtonUloadPartnerIcon=Hochladen-Symbol
#XFLD: Dialog Create Partner Connection Configuration Select
partnerConnectionConfigurationSelectIcon=Auswahl (maximale Größe 200 KB)
#XFLD: Dialog Create Partner Connection Configuration Enter a name
partnerConnectionConfigurationNamePlaceholder=Beispiel für Partnerkachel
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL
partnerConnectionConfigurationiFrameURLPlaceholder=http://www.Beispiel.de
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL Check
partnerConnectionConfigurationiFramePostMessageOriginPlaceholder=.Beispiel.de
#XFLD: Dialog Create Partner Connection Configuration Browse
partnerConnectionConfigurationBrowseButton=Durchsuchen
#XFLD: Create Partner Connection Configuration Msg
partnerConnectionConfigurationUploadMsg=Partnerverbindungskonfiguration wurde erfolgreich angelegt.
#XMSG: Error while deleting Partner Connection configuration(s)
partnerConnectionConfigurationDeleteError=Beim Löschen der Partnerverbindungskonfiguration ist ein Fehler aufgetreten.
#XMSG: Partner Connection Configuration has been successfully deleted
partnerConnectionConfigurationDeleteSuccess=Partnerverbindungskonfiguration wurde erfolgreich gelöscht.
#XMSG: Error while retrieving Partner Connection Configurations
partnerConnectionConfigurationLoadError=Beim Abrufen der Partnerverbindungskonfiguration ist ein Fehler aufgetreten.
#XMSG: Error fileSizeLimitExceeded
partnerConnectionConfigurationfileSizeLimitExceeded=Datei konnte nicht hochgeladen werden, da sie die maximale Größe von 200 KB überschreitet.
#XBUT: Create Partner Connection Configuration
partnerConnectionConfigurationButtonCreate=Konfiguration für Partnerverbindung anlegen
#XBUT: Delete Partner Connection Configuration
partnerConnectionConfigurationButtonDelete=Partnerverbindungskonfiguration löschen
#XMSG: Partner Tile Creation Failure
upsertPartnerTileFailed=Partnerkachel konnte nicht angelegt werden.
#XMSG: Partner Tile Deletion Failure
deletePartnerTileFailed=Partnerkachel konnte nicht gelöscht werden.
#XMSG: HANA cloud connector settings reset failure
resetHANASCCSettingsFailed=Zurücksetzen der benutzerdefinierten SAP-HANA-Cloud-Connector-Einstellungen fehlgeschlagen
#XFLD: Workload Class
workloadClass=Workload-Klasse
#XFLD: Workload Management
workloadManagement=Workload-Management
#XFLD: Priority
workloadClassPriority=Priorität
#XMSG:
workloadManagementPriorityHint=Sie können die Priorisierung dieses Space beim Abfragen der Datenbank festlegen. Geben Sie einen Wert zwischen 1 (niedrigste Priorität) und 8 (höchste Priorität) ein. In Situationen, in denen Spaces um verfügbare Threads konkurrieren, werden die mit höherer Priorität vor Spaces mit niedrigerer Priorität ausgeführt.
#XMSG:
workloadClassPriorityHint=Sie können die Priorität für den Space von 0 (niedrigste) bis 8 (höchste) angeben. Die Anweisungen für einen Space mit hoher Priorität werden vor denen von Spaces mit niedrigerer Priorität ausgeführt. Die Standardpriorität lautet 5. Da der Wert 9 für Systemvorgänge reserviert ist, kann er nicht für Spaces ausgewählt werden.
#XFLD: Statement Limits
workloadclassStatementLimits=Anweisungsgrenzen (Statement Limits)
#XFLD: Workload Configuration
workloadConfiguration=Workload-Konfiguration
#XMSG:
workloadClassStatementLimitsHint=Sie können die maximale Anzahl (oder den maximalen Prozentsatz) an Threads oder GB an Speicher angeben, die gleichzeitig im Space ausgeführte Anweisungen in Anspruch nehmen können. Sie können einen beliebigen Wert oder einen Prozentsatz zwischen 0 (uneingeschränkt) und dem auf dem Tenant insgesamt verfügbaren Speicher und der insgesamt verfügbaren Threads eingeben.\n\n Wenn Sie eine Grenze für Threads angeben, beachten Sie, dass sich dadurch die Performance verschlechtern kann. \n\n Wenn Sie eine Grenze für den Speicher angeben, werden die Anweisungen, die diese Grenze erreichen, nicht mehr ausgeführt.
#XMSG:
workloadClassStatementLimitsDescription=Die Standardkonfiguration bietet großzügige Ressourcengrenzen und verhindert gleichzeitig, dass einzelne Spaces das System überlasten.
#XMSG:
workloadClassStatementLimitCustomDescription=Sie können maximale Gesamtgrenzwerte für Threads und Speicher festlegen, die gleichzeitig im Space ausgeführte Anweisungen in Anspruch nehmen können.
#XMSG:
totalStatementThreadLimitHelpText=Eine zu niedrige Thread-Grenze kann die Performance von Anweisungen beeinträchtigen, während extrem hohe Werte oder 0 dazu führen können, dass ein Space alle verfügbaren System-Threads in Anspruch nimmt.
#XMSG:
totalStatementMemoryLimitHelpText=Eine zu niedrige Speichergrenze kann zu Out-of-Memory-Problemen führen, während extrem hohe Werte oder 0 dazu führen können, dass ein Space den gesamten verfügbaren Systemspeicher in Anspruch nimmt.
#XMSG:
totalStatementThreadLimitHelpTextRestricted=Geben Sie einen Prozentsatz zwischen 1 und 70 % (oder die entsprechende Anzahl) der auf Ihrem Tenant insgesamt verfügbaren Threads ein. Eine zu niedrige Thread-Grenze kann die Performance von Anweisungen beeinträchtigen, während extrem hohe Werte die Performance von Anweisungen in anderen Spaces beeinträchtigen kann.
#XMSG:
totalStatementThreadLimitHelpTextDynamic=Geben Sie einen Prozentsatz zwischen 1 und {0} % (oder die entsprechende Anzahl) der auf Ihrem Tenant insgesamt verfügbaren Threads ein. Eine zu niedrige Thread-Grenze kann die Performance von Anweisungen beeinträchtigen, während extrem hohe Werte die Performance von Anweisungen in anderen Spaces beeinträchtigen kann.
#XMSG:
totalStatementMemoryLimitHelpTextNew=Geben Sie einen Wert oder einen Prozentsatz zwischen 0 (uneingeschränkt) und dem auf dem Tenant insgesamt verfügbaren Speicher ein. Eine zu niedrige Speichergrenze kann die Performance von Anweisungen beeinträchtigen, während extrem hohe Werte die Performance von Anweisungen in anderen Spaces beeinträchtigen kann.
#XFLD: Total Statement Thread Limit
totalStatementThreadLimit=Total Statement Thread Limit
#XFLD: Segmented button label
workloadclassPercentage=%
#XFLD: Segmented button label
workloadclassGigaByte=GB
#XFLD, 80: Segmented button label
workloadclassThreads=Threads
#XFLD: Total Statement Memory Limit
totalStatementMemoryLimit=Total Statement Memory Limit
#YMSE: Error while loading customer hana info
loadCustomerHanaInfoError=SAP-HANA-Infos des Kunden konnten nicht geladen werden.
#XMSG:
minimumLimitReached=Untergrenze erreicht.
#XMSG:
maximumLimitReached=Obergrenze erreicht.
#XMSG: Name Taken for Technical Name
technical-name-taken=Es gibt bereits eine Verbindung mit dem von Ihnen eingegebenen technischen Namen. Geben Sie einen anderen Namen ein.
#XMSG: Name Too long for Technical Name
technical-name-too-long=Der von Ihnen eingegebene technische Name ist länger als 40 Zeichen. Geben Sie einen kürzeren Namen ein.
#XMSG: Technical name field empty
technical-name-field-empty=Geben Sie einen technischen Namen ein.
#XMSG: Generic error when user enters invalid characters for a field
field-invalid-characters=Sie dürfen nur Buchstaben (a–z), Zahlen (0–9) und Unterstriche (_) für den Namen verwenden.
#XMSG: Generic error when user enters invalid initial characters for a field
field-underscore-trim=Der von Ihnen eingegebene Name darf nicht mit einem Unterstrich (_) beginnen oder enden.
#XFLD: Checkbox label for Enable Statement Limits
enableStatementLimits=Anweisungsgrenzen (Statement Limits) aktivieren
#XFLD: Connection SubSection Settings Title in space details
connectionSettings=Einstellungen
#XMSG: Connections tool hint in Space details section
connectionsToolHint=Um Verbindungen anzulegen oder zu bearbeiten, öffnen Sie die App "Verbindungen" in der Seitennavigation oder klicken Sie hier:
#XBUT: Connection list Open Connection Tool Button
button-connectiontool=Zu "Verbindungen" wechseln
#XFLD: Not deployed label on space tile
notDeployedLabel=Space wurde noch nicht aktiviert.
#XFLD: Not deployed additional text on space tile
notDeployedText=Aktivieren Sie den Space.
#XFLD: Corrupt space label on space tile
corruptSpace=Es ist ein Fehler aufgetreten.
#XFLD: Corrupt space additional text on space tile
corruptSpaceHint=Versuchen Sie, den Space erneut zu aktivieren, oder wenden Sie sich an den Support
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Auditprotokolldaten
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Verwaltungsdaten
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Sonstige Daten
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Daten in Spaces
#XMSG: Unlock Space confirmation text
unlockSpaceConfirmation=Möchten Sie den Space wirklich entsperren?
#XMSG: Lock Space confirmation text
lockSpaceConfirmation=Möchten Sie den Space wirklich sperren?
#XFLD: Lock
lock=Sperren
#XFLD: Unlock
unlock=Entsperren
#XFLD: Locking
locking=Wird gesperrt...
#XMSG: Success message after locking space
lockSpaceSuccessMsg=Space gesperrt
#XMSG: Success message after unlocking space
unlockSpaceSuccessMsg=Space entsperrt
#XMSG: Success message after locking spaces
lockSpacesSuccessMsg=Spaces gesperrt
#XMSG: Success message after unlocking spaces
unlockSpacesSuccessMsg=Spaces entsperrt
#YMSE: Error while locking a space
lockSpaceError=Der Space kann nicht gesperrt werden.
#YMSE: Error while unlocking a space
unlockSpaceError=Der Space kann nicht entsperrt werden.
#XTIT: popup title Warning
confirmationWarningTitle=Warnung
#XMSG: Message strip message when a space was locked manually
manualLockedMessage=Der Space wurde manuell gesperrt.
#XMSG: Message strip message when a space was locked due to the amount of generated audit logs
auditLogLockedMessage=Der Space wurde durch das System gesperrt, da Audit-Protokolle eine große Menge an GB Festplattenspeicher verbrauchen.
#XMSG: Message strip message when a space was locked because the assigned quota was exceeded.
quotaLockedMessage=Der Space wurde durch das System gesperrt, da er den ihm zugeordneten Arbeitsspeicher- oder Festplattenspeicher überschreitet.
#XMSG: Unlock Spaces confirmation text
unlockSpacesConfirmation=Möchten Sie die ausgewählten Spaces wirklich entsperren?
#XMSG: Lock Spaces confirmation text
lockSpacesConfirmation=Möchten Sie die ausgewählten Spaces wirklich sperren?
#XTIT: Scoped role editor dialog title
scopedRoleEditorDialogTitle=Editor für Anwendungsbereichsrollen
#XTIT: ECN Management title
ecnManagementTitle=Verwaltung von Spaces und elastischen Rechenleistungsknoten
#XFLD: ECNs
ecns=Elastische Rechenleistungsknoten
#XFLD: ECN phase Ready
ecnReady=Bereit
#XFLD: ECN phase Running
ecnRunning=Wird ausgeführt
#XFLD: ECN phase Initial
ecnInitial=Nicht bereit
#XFLD: ECN phase Starting
ecnStarting=Startet
#XFLD: ECN phase Starting Failed
ecnStartingFailed=Starten fehlgeschlagen
#XFLD: ECN phase Stopping
ecnStopping=Wird gestoppt
#XFLD: ECN phase Stopping Failed
ecnStoppingFailed=Stoppen fehlgeschlagen
#XBTN: Assign Button
assign=Spaces zuordnen
#XBTN: Start Header-Button
start=Starten
#XBTN: Update Header-Button
repair=Aktualisieren
#XBTN: Stop Header-Button
stop=Stoppen
#XFLD: ECN hours remaining
ecnHoursRemaining=1000 Stunden übrig
#XFLD: ECN block-hours remaining
ecnBlockHoursRemaining={0} Blockstunden verbleibend
#XFLD: ECN block-hour remaining
ecnBlockHourRemaining={0} Blockstunde verbleibend
#XTIT: ECN creation dialog title
ecnCreationDialogTitle=Elastischen Rechenleistungsknoten anlegen
#XTIT: ECN edit dialog title
ecnEditDialogTitle=Elastischen Rechenleistungsknoten bearbeiten
#XTIT: ECN deletion dialog title
ecnDeletionDialogTitle=Elastischen Rechenleistungsknoten löschen
#XTIT: ECN Space assignment dialog title
ecnSpaceAssignmentDialogTitle=Spaces zuordnen
#XFLD: ECN ID
ECNIDLabel=Elastischer Rechenleistungsknoten
#XTXT: Selected toolbar text
selectedToolbarText=Ausgewählt: {0}
#XTIT: Elastic Compute Nodes
ECNslong=Elastische Rechenleistungsknoten
#XTIT: Object assignment - Table header: Objects available
objectsAvailable=Anzahl der Objekte
#XTIT: Object assignment - Dialog header text
selectObjects=Wählen Sie die Spaces und Objekte aus, die Sie Ihrem elastischen Rechenleistungsknoten zuordnen möchten:
#XTIT: Object assignment - Table header title: Objects
objects=Objekte
#XTIT: Object assignment - Table header: Type
type=Typ
#XMSG: Message strip delete database user audit warning
deleteDatabaseUserWarningMessageStrip=Beachten Sie, dass das Löschen eines Datenbankbenutzers dazu führt, dass alle generierten Audit-Protokolleinträge gelöscht werden. Wenn Sie die Audit-Protokolle beibehalten möchten, sollten Sie sie exportieren, bevor Sie den Datenbankbenutzer löschen.
#XMSG: Message strip delete HDI container audit logs impact
deleteHDIContainerWarningMessageStrip=Beachten Sie, dass das Aufheben der Zuordnung eines HDI-Containers zu dem Space dazu führt, dass alle generierten Audit-Protokolleinträge gelöscht werden. Wenn Sie die Audit-Protokolle beibehalten möchten, sollten Sie sie exportieren, bevor Sie die Zuordnung des HDI-Containers aufheben.
#XTXT: All audit logs
allAuditLogs=Alle für den Space generierten Audit-Protokolleinträge
#XMSG: Message strip disable audit warning
disableAuditWarningMessageStrip=Beachten Sie, dass das Deaktivieren einer Audit-Richtlinie (Lese- oder Änderungsvorgänge) dazu führt, dass alle zugehörigen Audit-Protokolleinträge gelöscht werden. Wenn Sie die Audit-Protokolleinträge beibehalten möchten, sollten Sie sie vor dem Deaktivieren der Audit-Richtlinie exportieren.
#XTIT: No Spaces assigned to ECN Illustration title
noSpacesECNTitle=Noch keine Spaces oder Objekte zugeordnet
#XTIT: No Spaces assigned to ECN Illustration description
noSpacesECNDescription=Um mit Ihrem elastischen Rechenleistungsknoten zu arbeiten, weisen Sie ihm einen Space oder Objekte zu.
#XTIT: No Spaces Illustration title
noSpacesTitle=Noch kein Space angelegt
#XTIT: No Spaces Illustration description
noSpacesDescription=Um mit dem Importieren von Daten zu beginnen, legen Sie einen Space an.
#XTIT: No Spaces Illustration title
noDeletedSpacesTitle=Der Papierkorb ist leer.
#XTIT: No Spaces Illustration description
noDeletedSpacesDescription=Hier können Sie Ihre gelöschten Spaces wiederherstellen.
#XTXT:  {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDatabaseUsers=Sobald der Space aktiviert wird, werden die folgenden Datenbankbenutzer gelöscht und können nicht wiederhergestellt werden: {0}
#XTIT: Delete database users
deleteDatabaseUsersTitle=Datenbankbenutzer löschen
#YMSE: Error if ecn id already exists
ecnIDInputDuplicateError=ID ist bereits vorhanden.
#YMSE: Error if ecn id contains invalid characters
ecnIDInputInvalidError=Verwenden Sie nur Kleinbuchstaben (a - z) und Ziffern (0 - 9)
#YMSE: Error if ecn id is too short
ecnIDInputTooShortError=ID muss mindestens {0} Zeichen lang sein.
#XMSG: ecn id length warning
ecnIdLengthWarning=Maximum von {0} Zeichen überschritten.
#XFLD: open System Monitor
systemMonitor=Systemmonitor
#XFLD: open ECN schedule dialog menu entry
schedule=Zeitplan
#XFLD: open create ECN schedule dialog
createSchedule=Zeitplan anlegen
#XFLD: open change ECN schedule dialog
changeSchedule=Zeitplan bearbeiten
#XFLD: open delete ECN schedule dialog
deleteSchedule=Zeitplan löschen
#XFLD: open self assign ECN schedule dialog
selfAssignSchedule=Zeitplan mir zuweisen
#XFLD: open pause ECN schedule dialog
pauseSchedule=Zeitplan pausieren
#XFLD: open resume ECN schedule dialog
resumeSchedule=Zeitplan fortsetzen
#XFLD: View Logs
viewLogs=Protokolle anzeigen
#XFLD: Compute Blocks
computeBlocks=Rechenleistungsblöcke
#XFLD: Memory label in ECN creation dialog
ecnMemory=Arbeitsspeicher (GB)
#XFLD: Storage label in ECN creation dialog
ecnStorage=Speicherplatz (GB)
#XFLD: Number of CPU label in ECN creation dialog
ecnCPUCount=Anzahl CPUs
#XFLD: ECN updated by label
changedBy=Geändert von
#XFLD: ECN updated on label
changedOn=Geändert am
#XMSG: Success message after creating a Elastic Compute Node
createEcnSuccessMessage=Elastischer Rechenleistungsknoten angelegt
#YMSE: Error while creating a Elastic Compute Node
createEcnError=Elastischer Rechenleistungsknoten konnte nicht angelegt werden
#XMSG: Success message after updating a Elastic Compute Node
updateEcnSuccessMessage=Elastischer Rechenleistungsknoten aktualisiert
#XMSG: Error while updating a Elastic Compute Node
udpateEcnError=Elastischer Rechenleistungsknoten konnte nicht aktualisiert werden
#XMSG: Success message after deleting a Elastic Compute Node
deleteEcnSuccessMessage=Elastischer Rechenleistungsknoten gelöscht
#YMSE: Error while deleting a Elastic Compute Node
deleteEcnError=Elastischer Rechenleistungsknoten konnte nicht gelöscht werden
#XMSG: Success message after starting a Elastic Compute Node
startingEcnSuccessMessage=Elastischer Rechenleistungsknoten wird gestartet
#XMSG: Success message after stopping a Elastic Compute Node
stoppingEcnSuccessMessage=Elastischer Rechenleistungsknoten wird gestoppt
#YMSE: Error while starting a Elastic Compute Node
startingEcnError=Elastischer Rechenleistungsknoten konnte nicht gestartet werden
#YMSE: Error while stopping a Elastic Compute Node
stoppingEcnError=Elastischer Rechenleistungsknoten konnte nicht gestoppt werden
#XBUT: Add Object button for an ECN
assignObjects=Objekte hinzufügen
#XFLD: Enable auto object assignment label
enableAutoObjAssignment=Alle Objekte automatisch hinzufügen
#XFLD: object type label to be assigned
objectTypeLabel=Typ (semantische Verwendung)
#XFLD: assigned object type label
assignedObjectTypeLabel=Typ
#XFLD: technical name label
TechnicalNameLabel=Technischer Name
#XFLD: Object selecttion ECN label
objectSelectionECNLabel=Wählen Sie die Objekte aus, die Sie dem elastischen Rechenleistungsknoten hinzufügen möchten:
#XTIT: Add objects dialog title
assignObjectsTitle=Objekte zuordnen von
#XFLD: object label with object count
objectLabel=Objekt
#XMSG: No objects available to add message.
noObjectsToAssign=Keine Objekte zum Zuordnen verfügbar.
#XMSG: No objects assigned message.
noAssignedObjects=Keine Objekte zugeordnet.
#XTIT: Remove objects popup title
removeObjectsOrSpacesConfirmationTitle=Warnung
#XTIT: Delete ECN popup button and title
deleteECNConfirmationButtonAndTitle=Löschen
#XMSG: Remove objects popup text
removeObjectsConfirmation=Möchten Sie die ausgewählten Objekte wirklich entfernen?
#XMSG: Remove spaces popup text
removeSpacesConfirmation=Möchten Sie die ausgewählten Spaces wirklich entfernen?
#XBUT: Confirmation button for objects dialog
removeObjectsOrSpacesConfirmationBtn=Spaces entfernen
#XMSG: Success message after removing objects
removeObjectsSuccessMessage=Verfügbar gemachte Objekte entfernt
#XMSG: Success message after assigning objects
assignObjectsSuccessMessage=Verfügbar gemachte Objekte zugeordnet
#XFLD: auto object assignment label for space tile
autoObjAssignmentLabel=Alle verfügbar gemachten Objekte
#XFLD: Spaces tab label
spacesTabLabel=Spaces
#XFLD: Exposed objects tab label
consumptionObjectsTabLabel=Verfügbar gemachte Objekte
#XMSG: Success message after unassigning spaces
spacesUnassignedSuccessMessage=Spaces entfernt
#XMSG: Success message after unassigning space
spaceUnassignedSuccessMessage=Space entfernt
#YMSE: Error while assigning / unassigning spaces
spacesAssignUnassignError=Spaces konnten nicht zugeordnet oder entfernt werden.
#YMSE: Error while removing objects
removeObjectsError=Die Objekte konnten nicht zugeordnet oder entfernt werden.
#YMSE: Error while removing object
removeObjectError=Das Objekt konnte nicht zugeordnet oder entfernt werden.
#YMSE: Error when saving the ecn with invalid value
invalidSizingPlanError=Die zuvor ausgewählte Zahl ist nicht mehr gültig. Wählen Sie eine gültige Zahl aus.
#YMSE: Error when saving the ecn with invalid value
invalidPerformanceClassError=Wählen Sie eine gültige Leistungsklasse aus.
#YMSE: Error when saving the ecn with invalid value
performanceClassNotAvailableError=Die zuletzt ausgewählte Leistungsklasse "{0}" ist derzeit nicht gültig. Wählen Sie eine gültige Leistungsklasse aus.
#XTXT: Delete ECN confirmation text
deleteConfirmationTxtECN=Möchten Sie den elastischen Berechnungsknoten wirklich löschen?
#XFLD: tooltip for ? button
help=Hilfe
#XFLD: ECN edit button label
editECN=Konfigurieren
#XFLD: Technical type label for ERModel
DWC_ERMODEL=Entity-Relationship-Modell
#XFLD: Technical type label for Local Table
DWC_LOCAL_TABLE=Lokale Tabelle
#XFLD: Technical type label for Remote Table
DWC_REMOTE_TABLE=Remote-Tabelle
#XFLD: Technical type label for Analytic Model
DWC_ANALYTIC_MODEL=Analysemodell
#XFLD: Technical type label for Task Chain
DWC_TASKCHAIN=Aufgabenkette
#XFLD: Technical type label for Data Flow
DWC_DATAFLOW=Datenfluss
#XFLD: Technical type label for Replication Flow
DWC_REPLICATIONFLOW=Replikationsfluss
#XFLD: Technical type label for Transformation Flow
DWC_TRANSFORMATIONFLOW=Transformationsfluss
#XFLD: Technical type label for Intelligent Lookup
DWC_IDT=Intelligente Suche
#XFLD: Technical type label for Repository
DWC_REPOSITORY_PACKAGE=Repository
#XFLD: Technical type label for Enterprise Search
DWC_ESMODEL=Enterprise Search
#XFLD: Technical type label for View
DWC_VIEW=View
#XFLD: Technical type label for Data Product
DWC_DATAPRODUCT=Datenprodukt
#XFLD: Technical type label for Data Access Control
DWC_DAC=Datenzugriffskontrolle
#XFLD: Technical type label for Folder
DWC_FOLDER=Ordner
#XFLD: Technical type label for Data Entity
DWC_BUSINESS_ENTITY=Geschäftsentität
#XFLD: Technical type label for Business Entity Variant
DWC_BUSINESS_ENTITY_VARIANT=Variante von Geschäftsentität
#XFLD: Technical type label for Responsibility Scenario
DWC_AUTH_SCENARIO=Zuständigkeitsszenario
#XFLD: Technical type label for Fact Model
DWC_FACT_MODEL=Faktenmodell
#XFLD: Technical type label for Transformation Flow
DWC_PERSPECTIVE=Perspektive
#XFLD: Technical type label for Consumption Model
DWC_CONSUMPTION_MODEL=Verwendungsmodell
#XFLD: Technical type label for Remote Connection
DWC_REMOTECONNECTION=Remote-Verbindung
#XFLD: Technical type label for Fact Model Variant
DWC_FACT_MODEL_VARIANT=Faktenmodellvariante
#XMSG: Schedule created alert message
createScheduleSuccess=Zeitplan wurde angelegt
#XMSG: Schedule updated alert message
updateScheduleSuccess=Zeitplan wurde aktualisiert
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Zeitplan wurde gelöscht
#XMSG: Schedule takeover success message
ownerChangeScheduleSuccess=Zeitplan wurde Ihnen zugewiesen
#XMSG: Message for starting pausing of schedule
pauseScheduleStarted=1 Zeitplan wird pausiert
#XMSG: Message for starting resuming of schedule
resumeScheduleStarted=1 Zeitplan wird fortgesetzt
#XFLD: Segmented button label
availableSpacesButton=Verfügbar
#XFLD: Segmented button label
selectedSpacesButton=Ausgewählt
#XFLD: Visit website button text
visitWebsite=Website besuchen
#XMSG: Warning message on unselecting source language
sourceLanguageRemovalWarning=Die bisher ausgewählte Quellsprache wird entfernt.
#XBUT: enable translation confirmation button
enableTranslationConfirmationBtn=Aktivieren
#XFLD: ECN performance class label
performanceClassLabel=Leistungsklasse
#XTXT performance class memory text
memoryText=Arbeitsspeicher
#XTXT performance class compute text
computeText=Rechenleistung
#XTXT performance class high-compute text
highComputeText=Hohe Rechenleistung
#XBUT: Recycle Bin Button Text
recycleBin=Papierkorb
#XBUT: Restore Button Text
restore=Wiederherstellen
#XMSG: Warning message for new Workload Management UI
priorityWarning=Dieser Bereich ist schreibgeschützt. Sie können die Priorität des Space im Bereich System / Konfiguration / Workload-Management ändern.
#XMSG: Warning message for new Workload Management UI
workloadWarning=Dieser Bereich ist schreibgeschützt. Sie können die Workload-Konfiguration des Space im Bereich System / Konfiguration / Workload-Management ändern.
#XCOL: Space table-view column: Apache Spark vCPUs
sparkManagementVCPUs=Apache-Spark-vCPUs
#XCOL: Space table-view column: Apache Spark Memory (GB)
sparkManagementMemory=Apache-Spark-Speicher (GB)
#XCOL: Space type (from Space Capability) for Data Ingestion Spaces
ingestionSpaceType=Datenprodukterfassung
#XFLD: Deployment in progress nodatatext for spark table
sparkTableDeploymentInProgress=Keine Daten verfügbar, da der Space derzeit aktiviert wird
#XFLD: Runtime data being loaded nodatatext for spark table
sparkTableDeploymentLoading=Keine Daten verfügbar, da der Space derzeit geladen wird
#XCOL: Space type (from Space Capability) for BW Private Cloud Edition
BWPCESpaceType=SAP BW
#XBUT: Go to Instance Mappings Button
navToMappingsButton=Instanzzuordnungen bearbeiten
#XFLD: SAP HANA Data Lake Storage label if less than 1GB
dataLakeStorageUsed=< 1 GB
