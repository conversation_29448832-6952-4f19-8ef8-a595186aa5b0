#DO NOT REMOVE________________________
#XFLD: Breadcrumb for space monitoring page
spaceMonitorBreadcrumb=Overvågning
#XTXT: Type name for spaces in browser tab page title
space=Space
#_____________________________________
#XFLD: Spaces label in
spaces=Spaces
#XFLD: Manage plan button text
manageQuotaButtonText=Administrer plan
#XBUT: Manage resources button
manageResourcesButton=Administrer ressourcer
#XFLD: Create space button tooltip
createSpace=Opret space
#XFLD: Create
create=Opret
#XFLD: Deploy
deploy=Implementer
#XFLD: Page
page=Side
#XFLD: Cancel
cancel=Annuller
#XFLD: Update
update=Opdater
#XFLD: Save
save=Gem
#XFLD: OK
ok=OK
#XFLD: days
days=Dage
#XFLD: Space tile edit button label
edit=Rediger
#XFLD: Auto Assign all objects to space
autoAssign=Tildel automatisk
#XFLD: Space tile open monitoring button label
openMonitoring=Overvåg
#XFLD: Delete
delete=Slet
#XFLD: Copy Space
copy=Kopiér
#XFLD: Close
close=Luk
#XCOL: Space table-view column status
status=Status
#XFLD: Space status active
activeLabel=Aktiv
#XFLD: Space status locked
lockedLabel=Spærret
#XFLD: Space status critical
criticalLabel=Kritisk
#XFLD: Space status cold
coldLabel=Kold
#XFLD: Space status deleted
deletedLabel=Slettet
#XFLD: Space status unknown
unknownLabel=Ukendt
#XFLD: Space status ok
okLabel=OK
#XFLD: Database user expired
expired=Udløbet
#XFLD: deployed
deployed=Implementeret
#XFLD: not deployed
notDeployed=Ikke implementeret
#XFLD: changes to deploy
changesToDeploy=Ændringer, der skal implementeres
#XFLD: pending
pending=Implementerer
#XFLD: designtime error
designtimeError=Fejl ved designtid
#XFLD: runtime error
runtimeError=Fejl ved kørselstid
#XFLD: Space created by label
createdBy=Oprettet af
#XFLD: Space created on label
createdOn=Oprettet den
#XFLD: Space deployed on label
deployedOn=Implementeret den
#XFLD: Space ID label
spaceID=Space-id
#XFLD: Priority label
priority=Prioritet
#XFLD: Space Priority label
spacePriority=Space-prioritet
#XFLD: Space Configuration label
spaceConfiguration=Konfiguration af space
#XFLD: Not available
notAvailable=Ikke tilgængelig
#XFLD: WorkloadType default
default=Standard
#XFLD: WorkloadType custom
custom=Brugerdefineret
#XFLD: Data Lake option label; refers to component SAP HANA Cloud, Data Lake
dataLakeAccess=Adgang til data lake
#XFLD: Translation label
translationLabel=Oversættelse
#XFLD: Source language label
sourceLanguageLabel=Kildesprog
#XFLD: Translation CheckBox label
translationCheckBox=Aktiver oversættelse
#XTXT: Popover hint that space has to be deployed to show user details
openDatabaseUserHintPopoverText=Implementer space for at få adgang til brugerdetaljer.
#XTXT: Popover hint that space has to be deployed to open database explorer
openDatabaseExplorerHintPopoverText=Implementer space for at åbne Database Explorer.
#XTXT: Text for when the Data Lake check box is disabled
dataLakeOptionDisabled=Du kan ikke anvende dette space til at tilgå datasøen, fordi den allerede anvendes af et andet space.
#XFLD: Data Lake check box text; refers to component SAP HANA Cloud, Data Lake
dataLakeCheckBox=Anvend dette space til at få adgang til denne data lake.
#XFLD: Space Priority minimum label extension
low=Lav
#XFLD: Space Priority maximum label extension
high=Høj
#XFLD: Space name label
spaceName=Navn på space
#XFLD: Enable deploy objects checkbox
enableDeployObjects=Implementer objekter
#XTIT: Copy spaces dialog title
copySpaceDialogTitle=Kopiér {0}
#XTXT Human readable text for unselected source language
unSelectedSourceLanguage=(Ikke valgt)
#XTXT Human readable text for language code "af"
af=Afrikaans
#XTXT Human readable text for language code "ar"
ar=Arabisk
#XTXT Human readable text for language code "bg"
bg=Bulgarsk
#XTXT Human readable text for language code "ca"
ca=Katalansk
#XTXT Human readable text for language code "zh"
zh=Forenklet kinesisk
#XTXT Human readable text for language code "zf"
zf=Kinesisk
#XTXT Human readable text for language code "hr"
hr=Kroatisk
#XTXT Human readable text for language code "cs"
cs=Tjekkisk
#XTXT Human readable text for language code "cy"
cy=Walisisk
#XTXT Human readable text for language code "da"
da=Dansk
#XTXT Human readable text for language code "nl"
nl=Hollandsk
#XTXT Human readable text for language code "en-UK"
en-UK=Engelsk (Storbritannien)
#XTXT Human readable text for language code "en"
en=Engelsk (USA)
#XTXT Human readable text for language code "et"
et=Estisk
#XTXT Human readable text for language code "fa"
fa=Persisk
#XTXT Human readable text for language code "fi"
fi=Finsk
#XTXT Human readable text for language code "fr-CA"
fr-CA=Fransk (Canada)
#XTXT Human readable text for language code "fr"
fr=Fransk
#XTXT Human readable text for language code "de"
de=Tysk
#XTXT Human readable text for language code "el"
el=Græsk
#XTXT Human readable text for language code "he"
he=Hebraisk
#XTXT Human readable text for language code "hi"
hi=Hindi
#XTXT Human readable text for language code "hu"
hu=Ungarsk
#XTXT Human readable text for language code "is"
is=Islandsk
#XTXT Human readable text for language code "id"
id=Bahasa Indonesia
#XTXT Human readable text for language code "it"
it=Italiensk
#XTXT Human readable text for language code "ja"
ja=Japansk
#XTXT Human readable text for language code "kk"
kk=Kasakhisk
#XTXT Human readable text for language code "ko"
ko=Koreansk
#XTXT Human readable text for language code "lv"
lv=Lettisk
#XTXT Human readable text for language code "lt"
lt=Litauisk
#XTXT Human readable text for language code "ms"
ms=Bahasa Melayu
#XTXT Human readable text for language code "no"
no=Norsk
#XTXT Human readable text for language code "pl"
pl=Polsk
#XTXT Human readable text for language code "pt"
pt=Portugisisk (Brasilien)
#XTXT Human readable text for language code "pt-PT"
pt-PT=Portugisisk (Portugal)
#XTXT Human readable text for language code "ro"
ro=Rumænsk
#XTXT Human readable text for language code "ru"
ru=Russisk
#XTXT Human readable text for language code "sr"
sr=Serbisk
#XTXT Human readable text for language code "sh"
sh=Serbokroatisk
#XTXT Human readable text for language code "sk"
sk=Slovakisk
#XTXT Human readable text for language code "sl"
sl=Slovensk
#XTXT Human readable text for language code "es"
es=Spansk
#XTXT Human readable text for language code "es-MX"
es-MX=Spansk (Mexico)
#XTXT Human readable text for language code "sv"
sv=Svensk
#XTXT Human readable text for language code "th"
th=Thai
#XTXT Human readable text for language code "tr"
tr=Tyrkisk
#XTXT Human readable text for language code "uk"
uk=Ukrainsk
#XTXT Human readable text for language code "vi"
vi=Vietnamesisk
#XTIT: Delete spaces confirmation title
deleteConfirmationHeaderText=Slet spaces
#XTXT: Logically delete space delete confirmation text with space name
logicallyDeleteConfirmationBodyTextWithSpaceName=Er du sikker på, at du vil flytte space "{0}" til papirkurven?
#XTXT: Logically dpace delete confirmation text for multiple spaces
logicallyDeleteConfirmationBodyTextMultiple=Er du sikker på, at du vil flytte de {0} valgte spaces til papirkurven?
#XTXT: Delete space delete confirmation text with space name
deleteConfirmationBodyTextWithSpaceName=Er du sikker på, at du vil slette spacet "{0}"? Denne handling kan ikke fortrydes.
#XTXT: Delete space delete confirmation text for multiple spaces
deleteConfirmationBodyTextMultiple=Er du sikker på, at du vil slette de {0} valgte spaces? Denne handling kan ikke fortrydes. Følgende indhold slettes {1}:
#XTXT: permanently
permanently=permanent
#XTXT: {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDetail=Følgende indhold {0} slettes og kan ikke gendannes:
#XTXT: Hint Delete space confirmation text
deleteConfirmationHint=Indtast {0} for at bekræfte sletningen.
#XTXT: Delete space confirmation error check spelling
deleteConfirmationErrorCheckSpelling=Kontroller din skrivemåde, og prøv igen.
#XTXT: All Spaces
allSpaces=Alle spaces
#XTXT: All data
allData=Alle objekter og data i spacet
#XTXT: All connections
allConnections=Alle forbindelser defineret i spacet
#XFLD: Space tile selection box tooltip
clickToSelect=Klik for at vælge
#XTXT: All database users
allDatabaseUsers=Alle objekter og data i et Open SQL-skema, der er knyttet til spacet
#XFLD: remove members button tooltip
deleteUsers=Fjern medlemmer
#XTXT: Space long description text
description=Beskrivelse (maks. 4000 tegn)
#XFLD: Add Members button tooltip
addUsers=Tilføj medlemmer
#XFLD: Add Users button tooltip
addUsersTooltip=Tilføj brugere
#XFLD: Edit Users button tooltip
editUsersTooltip=Rediger brugere
#XFLD: Remove Users button tooltip
removeUsersTooltip=Fjern brugere
#XFLD: Searchfield placeholder
filter=Søg
#XCOL: Users table-view column health
health=Sundhed
#XCOL: Users table-view column access
access=Adgang
#XFLD: No user found nodatatext
noDataText=Ingen bruger fundet
#XTIT: Members dialog title
selectUserDialogTitle=Tilføj medlemmer
#XTIT: User dialog title
addUserDialogTitle=Tilføj brugere
#XTIT: Delete connections confirmation title
deleteConnectionConfirmationHeaderText=Slet forbindelser
#XTIT: Delete single connection confirmation title
deleteConnectionConfirmationHeaderTextSingle=Slet forbindelse
#XTXT: Delete connection delete confirmation text
deleteConnectionConfirmationBodyText=Er du sikker på, at du vil slette de valgte forbindelser? De fjernes permanent.
#XTIT: Remote source dialog title
selectRemoteConnectionDialogTitle=Vælg forbindelser
#XTIT: Share connection dialog title
connectionSharingDialogTitle=Del forbindelse
#XTIT: Connections shared from other spaces table title
sharedConnectionsFromOtherSpaces=Delte forbindelser
#XFLD: Add remote source button tooltip
addRemoteConnections=Tilføj forbindelser
#XFLD: Delete remote source button tooltip
deleteRemoteConnections=Fjern forbindelser
#XFLD: Share remote source button tooltip
shareConnections=Del forbindelser
#XFLD: Tile-layout tooltip
tileLayout=Fliselayout
#XFLD: Table-layout tooltip
tableLayout=Tabellayout
#XMSG: Success message after creating space
createSpaceSuccessMessage=Space blev oprettet
#XMSG: Success message after copying space
copySpaceSuccessMessage=Kopierer space "{0}" til space "{1}"
#XMSG: Success message after deploying space
deploymentSuccessMessage=Implementering af space blev startet
#XMSG: Success message after Apache Spark update started
sparkUpdateStartedSuccessMessage=Apache Spark-opdatering er startet
#YMSE: Error while starting Apache Spark update
sparkUpdateStartedErrorMessage=Kunne ikke opdatere Apache Spark
#XMSG: Success message after updating space
updateSettingsSuccessMessage=Detaljer om space blev opdateret
#XMSG: Success message after unlocking space
unlockSpaceSuccessMessage=Space blev låst op midlertidigt
#XMSG: Success message after deleting a single space
deleteSpaceSuccessMessage=Space slettet
#XMSG: Success message after deleting multiple spaces
deleteSpacesSuccessMessage=Spaces slettet
#XMSG: Success message after restoring a single space
restoreSpaceSuccessMessage=Space gendannet
#XMSG: Success message after restoring multiple spaces
restoreSpacesSuccessMessage=Spaces gendannet
#YMSE: Error while updating settings
updateSettingsFailureMessage=Indstillinger for space kunne ikke opdateres.
#YMSE: Error while updating data lake option
dataLakeEnabledInOtherSpace=Data lake er allerede tildelt til et andet space. Kun ét space kan få adgang til denne data lake ad gangen.
#YMSE: Error while updating data lake option
virtualTablesExists=Du kan ikke ophæve tildelingen af denne data lake fra dette space, fordi der stadig er afhængigheder til virtuelle tabeller*. Slet de virtuelle tabeller for at ophæve tildelingen af denne data lake fra dette space.
#YMSE: Error while unlocking space
unlockSpaceFailureMessage=Space kunne ikke låses op.
#YMSE: Error while creating space
createSpaceError=Space kunne ikke oprettes.
#YMSE: Error while creating duplicate space
createDuplicateSpaceError=Et space med navnet {0} findes allerede.
#YMSE: Error while deleting a single space
deleteSpaceError=Space kunne ikke slettes.
#YMSE: Error details while deleting a single space
deleteSpaceDetailedError=Dit space "{0}" fungerer ikke længere korrekt. Forsøg at slette det igen. Hvis det stadig ikke fungerer, kan du bede din administrator om at slette dit space eller åbne en supportmeddelelse.
#YMSE: Error while deleting a single space in files
deleteSpaceInFilesError=Space-data i filer kunne ikke slettes.
#YMSE: Error while deleting a single space users
deleteSpaceUsersError=Brugerne kunne ikke fjernes.
#YMSE: Error while deleting a single space schema
deleteSpaceSchemasError=Skemaerne kunne ikke fjernes.
#YMSE: Error while deleting a single space connections
deleteSpaceConnectionsError=Forbindelserne kunne ikke fjernes.
#YMSE: Error while deleting a single space data
deleteSpaceDataError=Space-data kunne ikke slettes.
#YMSE: Error while deleting multiple spaces
deleteSpacesError=Spaces kunne ikke slettes.
#YMSE: Error while restoring a single space
restoreSpaceError=Spacet kunne ikke gendannes.
#YMSE: Error while restoring multiple spaces
restoreSpacesError=Disse spaces kunne ikke gendannes.
#YMSE: Error while creating users
createUsersError=Brugerne kunne ikke tilføjes.
#YMSE: Error while removing users
removeUsersError=Vi kunne ikke fjerne brugerne.
#YMSE: Error while removing user
removeUserError=kunne ikke fjerne brugeren.
#YMSE: Error assigning roles to a user
roleAssignUnAssignError=Vi kunne ikke tilføje brugeren til den valgte rolle i omfang. \n\n Du kan ikke tilføje dig selv til en rolle i omfang. Du kan bede din administrator om at føje dig til en rolle i omfang.
#YMSE: Error assigning user to the space
userAssignError=Vi kunne ikke tildele brugeren til spacet. \n\n Brugeren er allerede tildelt til det maksimalt tilladte antal (100) spaces på tværs af roller i omfang.
#YMSE: Error assigning users to the space
usersAssignError=Vi kunne ikke tildele brugerne til spacet. \n\n Brugeren er allerede tildelt til det maksimalt tilladte antal (100) spaces på tværs af roller i omfang.
#YMSE: Error while fetching users
fetchUserWithScopedRolesError=Vi kunne ikke hente brugerne. Prøv igen senere.
#YMSE: Error while fetching scoped roles
fetchScopedRolesError=Vi kunne ikke hente rollerne i omfanget.
#YMSE: Error while fetching members
fetchUserError=Medlemmerne kunne ikke hentes. Prøv igen senere.
#YMSE: Error while loading run-time database
loadRuntimeError=Vi kunne ikke indlæse oplysninger fra kørselstidsdatabasen.
#YMSE: Error while loading spaces
loadSpacesError=Beklager, noget gik galt ved forsøg på at hente dine spaces.
#YMSE: Error while loading haas resources
loadStorageError=Beklager, noget gik galt ved forsøg på at hente lagerdata.
#YMSE: Error no data could be loaded
loadDataError=Beklager, noget gik galt ved forsøg på at hente dine data.
#XFLD: Click to refresh storage data
clickToRefresh=Klik her, for at forsøge igen.
#XTIT: Delete space popup title
deleteSpacePopupTitle=Slet space
#XCOL: Spaces table-view column name
name=Navn
#XCOL: Spaces table-view deployment status
deploymentStatus=Implementeringsstatus
#XFLD: Disk label in space details
storageLabel=Harddisk (GB)
#XFLD: In-Memory label in space details
ramLabel=Hukommelse (GB)
#XFLD: Memory label on space card
memory=Hukommelse til lager
#XFLD: Storage Assignment label in space details
storrageAssignmentLabel=Space-lager
#XFLD: Storage Type label in space details
storageTypeLabel=Lagertype
#XFLD: Enable Space Quota
enableSpaceQuota=Aktiver Space-kvote
#XFLD: No Space Quota
noSpaceQuota=Ingen Space-kvote
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
diskAndInMemoryText=SAP HANA-database (disk og in-memory)
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
dataLakeFilesText=SAP HANA, datasøfiler
#XFLD: Available scoped roles label
availableRoles=Tilgængelige roller i omfanget
#XFLD: Selected scoped roles label
selectedRoles=Valgte roller i omfanget
#XCOL: Spaces table-view column models
models=Modeller
#XCOL: Spaces table-view column users
users=Brugere
#XCOL: Spaces table-view column connections
connections=Forbindelser
#XFLD: Section header overview in space detail
overview=Oversigt
#XFLD: Section header Apache Spark in space detail
sparkManagementSection=Apache Spark
#XFLD: Section header Apache Spark in space detail
sparkManagementSubSection=Applikationer
#XFLD: Section header Apache Spark in space detail
taskAssignmentSubSection=Opgavetildeling
#XFLD: vCPU label in Apache Spark section
vCPULabel=vCPU'er
#XFLD: Memory label in Apache Spark section
memoryLabel=Hukommelse (GB)
#XFLD: space configuration label in Apache Spark section
spaceConfigurationLabel=Konfiguration af space
#XFLD: Space Source label
sparkApplicationLabel=Applikation
#XFLD: Cluster Size label
clusterSizeLabel=Klyngestørrelse
#XFLD: Driver label
driverLabel=Driver
#XFLD: Executor label
executorLabel=Udførelsesprogram
#XFLD: max label
maxLabel=Maks. brugt
#XFLD: TrF Default label
trFDefaultLabel=Standard for transformationsflow
#XFLD: Merge Default label
mergeDefaultLabel=Fletningsstandard
#XFLD: Optimize Default label
optimizeDefaultLabel=Optimer standard
#XFLD: Deployment Default label
deploymentDefaultLabel=Implementering af lokal tabel (fil)
#XFLD: max spark source label
driverExecutorMaxUsedSparkSource={0} CPU / {1} GB
#XFLD: Object type label
taskObjectTypeLabel=Objekttype
#XFLD: Task activity label
taskActivityLabel=Aktivitet
#XFLD: Task Application ID label
taskApplicationIDLabel=Standardapplikation
#XFLD: Section header in space detail
generalSettings=Generelle indstillinger
#XMSG: Message strip message when a space is locked
spaceLockedMessageStrip=Dette space er aktuelt låst af systemet.
#XMSG: Message strip message deploy warning
deployWarningMessageStrip=Ændringer i dette afsnit implementeres med det samme.
#XMSG: Message strip message modify space configuration warning
modifySpaceConfigurationMessageStrip=Bemærk, at ændring af disse værdier kan forårsage problemer med ydeevnen.
#XFLD: Button text to unlock the space again
unlockSpace=Lås space op
#XFLD: Info text for audit log formatted message
auditLogText=Aktiver revisionslogfiler til at registrere læse- eller ændringshandlinger (revisionspolitikker). Administratorer kan derefter analysere, hvem der udførte hvilken handling på hvilket tidspunkt.
#XMSG: Message strip  audit warning
auditLogWarningMessageStrip=Revisionslogfiler kan forbruge en stor mængde diskplads i din tenant. Hvis du aktiverer en revisionspolitik (læse- eller ændringshandlinger), bør du regelmæssigt overvåge brugen af disklager (via kortet Disklager brugt i Systemovervågning) for at undgå udfald på grund af opbrugt disklager, hvilket kan føre til serviceforstyrrelser. Hvis du deaktiverer en revisionspolitik, slettes alle dens revisionslogposter. Hvis du vil beholde revisionslogposterne, skal du overveje at eksportere dem, før du deaktiverer revisionspolitikken.
#XFLD: Message strip link text to open in app help to guide user what to do to unlock his space permanently
unlockSpaceHelp=Vis hjælp
#XMSG: Message strip message when a space will be locked again
spaceTemporarilyUnlockedMessageStrip=Dette space overskrider dets space-lager og låses i {0} {1}.
#XMSG: Unit for remaining time until space is locked again
hours=timer
#XMSG: Unit for remaining time until space is locked again
minutes=minutter
#XFLD: Section header in space detail for auditing
detailsSectionAuditing=Revision
#XFLD: Subsection header in space detail for auditing
auditing=Revisionsindstillinger for space
#XFLD: Hot space tooltip
hotSpaceCountTooltip=Kritiske spaces: Anvendt lager er større end 90 %
#XFLD: Green space tooltip
greenSpaceCountTooltip=Spaces med status "OK": Anvendt lager er mellem 6 % og 90 %
#XFLD: Cold space tooltip
coldSpaceCountTooltip=Kolde spaces: Anvendt lager er 5 % eller mindre.
#XFLD: Critical space tooltip
criticalSpaceCountTooltip=Kritiske spaces: Anvendt lager er større end 90 %
#XFLD: Green space tooltip
okSpaceCountTooltip=Spaces med status "OK": Anvendt lager er mellem 6 % og 90 %
#XFLD: Locked space tooltip
lockedSpaceCountTooltipNew=Låste spaces': Er spærret pga. utilstrækkelig hukommelse.
#XFLD: Locked space tooltip
lockedSpaceCountTooltip=Låste spaces
#YMSE: Error while deleting remote source
deleteRemoteError=Forbindelserne kunne ikke fjernes.
#XFLD: Space ID input tooltip
spaceIDInfoTooltip=Space-id kan ikke ændres senere.\nGyldige tegn er A - Z, 0 - 9 og _
#YMSE: Error when space name is empty
spaceNameEmptyValidationError=Indtast navn på space.
#YMSE: Error when ECN Business name is empty
ecnNameEmptyValidationError=Indtast et forretningsnavn.
#YMSE: Error when space id is empty
spaceIDInputEmptyValidationError=Indtast space-id.
#YMSE: Error when space id contains invalid characters
spaceIDInputInValidError=Ugyldige tegn. Anvend kun A - Z, 0 - 9 og _.
#YMSE: Error when space id exists while creation
spaceIDInputDuplicateError=Space-id findes allerede.
#XFLD: Space searchfield placeholder
search=Søg
#XMSG: Success message after creating users
createUsersSuccess=Brugere blev tilføjet
#XMSG: Success message after creating user
createUserSuccess=Bruger tilføjet
#XMSG: Success message after updating users
updateUsersSuccess={0} brugere opdateret
#XMSG: Success message after updating user
updateUserSuccess=Bruger opdateret
#XMSG: Success message after removing users
removeUsersSuccess={0} brugere fjernet
#XMSG: Success message after removing user
removeUserSuccess=Bruger blev fjernet
#XFLD: Schema name
schemaName=Skemanavn
#XFLD: used of total
ofTemplate={0} af {1}
#XFLD: Assigned disk label in space overview
assignedStorageTemplate=Tildelt harddisk ({0} af {1})
#XFLD: Assigned Memory label in space overview
assignedRAMTemplate=Tildelt hukommelse ({0} af {1})
#XFLD: Storage ratio on space
accelearationRAM=Hukommelsesacceleration
#XFLD: No Storage Consumption
noStorageConsumptionText=Der er ikke tildelt nogen lagerkvote.
#XFLD: Used disk label in space overview
usedStorageTemplate=Disk brugt til lager ({0} af {1})
#XFLD: Used Memory label in space overview
usedRAMTemplate=Hukommelse brugt til lager ({0} af {1})
#XFLD: Used Disk tooltip
usedStorageTooltipTemplate={0} af {1} disk brugt til lager
#XFLD: Used Memory tooltip
usedRAMTooltipTemplate={0} af {1} hukommelse brugt
#XFLD: Assigned Disk tooltip
assignedStorageTooltipTemplate={0} af {1} harddisk tildelt
#XFLD: Assigned Memory tooltip
assignedRAMTooltipTemplate={0} af {1} hukommelse tildelt
#XFLD: Space related used storage tooltip
spaceRelatedUsedStorageTooltipTemplate=Space-data: {0}
#XFLD: Not space related used storage tooltip
otherDataUsedStorageTooltipTemplate=Andre data: {0}
#XFLD: Storage used exceeded popover and tooltip
storageUsedExceededText=Overvej at udvide din plan, eller kontakt SAP-support.
#XCOL: Space table-view column used Disk
usedStorage=Disk brugt til lager
#XCOL: Space monitor column used Memory
usedRAM=Hukommelse brugt til lager
#XCOL: Space monitor column Schema
tableSchema=Skema
#XCOL: Space monitor column Storage Type
tableStorageType=Lagertype
#XCOL: Space monitor column Table Type
tableType=Tabeltype
#XCOL: Space monitor column Record Count
tableRecordCount=Antal poster
#XFLD: Assigned Disk
assignedStorage=Disk tildelt til lager
#XFLD: Assigned Memory
assignedRAM=Hukommelse tildelt til lager
#XCOL: Space table-view column storage utilization
tableStorageUtilization=Anvendt lagerplads
#XFLD: space status
spaceStatus=Space-status
#XFLD: space type
spaceType=Spacetype
#XFLD: space type label for space with space type "SAP BW bridge" ("SAP BW bridge" is a product name, do not translate it)
abapbridgeSpaceType=SAP BW Bridge
#XFLD: space type label for space with space type "Data Marketplace"
marketplaceSpaceType=Produkt fra dataleverandør
#XMSG: Error message for when selected space cannot be deleted due to the space type
deleteTypedSpaceError=Du kan ikke slette space {0}, da dets spacetype er {1}.
#XMSG: Error message for when selected spaces cannot be deleted due to the space type
deleteTypedSpacesError=Du kan ikke slette de {0} valgte spaces. Spaces med følgende spacetyper kan ikke slettes: {1}.
#XFLD: space type label for space without space type
defaultSpaceType=SAP Datasphere
#XFLD: Tooltip for monitor space button
monitorSpace=Overvåg
#XFLD: Tooltip for edit space button
editSpace=Rediger space
#XMSG: Deletion warning in messagebox
deleteConfirmation=Er du sikker på, at du vil slette dette space?
#XFLD: Tooltip for delete space button
deleteSpace=Slet space
#XFLD: storage
storage=Disk til lager
#XFLD: username
userName=Brugernavn
#XFLD: port
port=Port
#XFLD: hostname
hostName=Host-navn
#XFLD: password
password=Adgangskode
#XBUT: Request new password button
requestPassword=Anmod om ny adgangskode
#YEXP: Usage explanation in time data section
timeDataSectionHint=Opret tidstabeller og dimensioner til anvendelse i dine modeller og stories.
#YEXP: Usage explanation in Space schema section
spaceSchemaHint=Ønsker du, at data i dit space kan forbruges af andre værktøjer eller apps? Hvis ja, skal du oprette en eller flere brugere, der kan tilgå data i dit space og vælge, om du ønsker, at alle fremtidige data som standard kan forbruges.
#XTIT: Create schema popup title
createSchemaDialogTitle=Opret Open SQL-skema
#XTIT: Generate Time Data dialog title
generateTimeDataDialogTitle=Opret tidstabeller og dimensioner
#XTIT: Edit Time Data dialog title
editTimeDataDialogTitle=Rediger tidstabeller og dimensioner
#XTIT: Time Data token title
timeDataTokenTitle=Tidsdata
#XTIT: Time Data token title
timeDataUpdateViews=Opdater tidsdatavisninger
#XFLD: Time Data token creation in progress label
timeDataCreationInProgress=Oprettelse i gang...
#XFLD: Time Data token creation error label
timeDataCreationError=Oprettelse mislykkedes. Forsøg igen.
#XTIT: Time Data dialog time settings title
timeDataDialogTimeSettingsTitle=Tidstabelindstillinger
#XTIT: Time Data dialog time data table title
timeDataDialogTranslationTablesTitle=Omregningstabeller
#XTIT: Time Data dialog dimensions title
timeDataDialogTimeViewsTitle=Tidsdimensioner
#XFLD: Time Data dialog time range label
timeRangeHint=Definer tidsintervallet.
#XFLD: Time Data dialog time data table label
timeDataHint=Giv din tabel et navn.
#XFLD: Time Data dialog time data dimensions label
timeDimensionsHint=Giv dine dimensioner et navn.
#XFLD: Time Data Time range description label
timerangeLabel=Tidsinterval
#XFLD: Time Data dialog from year label
fromYearLabel=Fra år
#XFLD: Time Data dialog to year label
toYearLabel=Til år
#XFLD: Time Data dialog calendar type label
calendarTypeLabel=Kalendertype
#XFLD: Time Data dialog granularity label
granularityLabel=Granularitet
#XFLD: Time Data dialog technical name label
technicalNameLabel=Teknisk navn
#XFLD: Time Data dialog quarters text table lable
quarterTextsTableLabel=Omregningstabel for kvartaler
#XFLD: Time Data dialog months text table lable
monthsTextsTableLabel=Omregningstabel for måneder
#XFLD: Time Data dialog days text table lable
daysTextsTableLabel=Omregningstabel for dage
#XFLD: Time Data dialog year label
yearLabel=Årsdimension
#XFLD: Time Data dialog quarter label
quarterLabel=Kvartalsdimension
#XFLD: Time Data dialog month label
monthLabel=Månedsdimension
#XFLD: Time Data dialog day label
dayLabel=Dagsdimension
#XFLD: Time Data dialog gregorian calendar type label
gregorian=Gregoriansk
#XFLD: Time Data dialog time granularity day label
day=Dag
#XFLD: Time Data dialog business name maxlength reached
timeRangeValueStateTextMaxLengthBusinessName=Maks. længde på 1000 tegn nået.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextMaxRangeExceeded=Det maksimale tidsinterval er 150 år.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeLower="Fra år" skal være lavere end "Til år"
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeHigher="Fra år" skal være 1900 eller senere.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeHigher="Til år" skal være højere end "Fra år"
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeLower="Til år" skal være lavere end det aktuelle år plus 100
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningFromYear=Forøgelse af "Fra år" kan medføre datatab
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningToYear=Reducering af "Til år" kan medføre datatab
#XMSG: Time Data creation validation error message
timeDataValidationError=Det ser ud til, at nogle felter er ugyldige. Kontroller de krævede felter for at fortsætte.
#XMSG: Time Data deletion popup text
deleteTimeDataConfirmation=Er du sikker på, at du vil slette dataene?
#XMSG: Time Data creation success message
createTimeDataSuccess=Tidsdata oprettet
#XMSG: Time Data update success message
updateTimeDataSuccess=Tidsdata opdateret
#XMSG: Time Data delete success message
deleteTimeDataSuccess=Tidsdata slettet
#XMSG: Time Data creation error message
createTimeDataError=Noget gik galt ved forsøg på at oprette tidsdata.
#XMSG: Time Data update error message
updateTimeDataError=Noget gik galt ved forsøg på at opdatere tidsdata.
#XMSG: Time Data creation error message
deleteTimeDataError=Noget gik galt ved forsøg på at slette tidsdata.
#XMSG: Error message when time data could not be retrieved
retrievingTimeDataError=Tidsdata kunne ikke indlæses.
#XTIT: Time Data deletion popup title
deleteTimeDataConfirmationTitle=Advarsel
#XTXT: Time Data deletion dependency popup text
deleteTimeDataDependencyText=Vi kunne ikke slette dine tidsdata, fordi de bruges i andre modeller.
#XTXT: Time Data deletion dependency popup text singular
deleteTimeDataDependencyTextSingular=Vi kunne ikke slette dine tidsdata, fordi de bruges i en anden model.
#XMSG: Validation error when required time data form field is empty
timeDataRequiredField=Dette felt er obligatorisk og må ikke være tomt.
#XFLD: Tooltip for Open in Database Explorer button
openInDBX=Åbn i Database Explorer
#YMSE: Dimension Year
dimensionYearView=Dimension "År"
#YMSE: Dimension Year
dimensionQuarterView=Dimension "Kvartal"
#YMSE: Dimension Year
dimensionMonthView=Dimension "Måned"
#YMSE: Dimension Year
dimensionDayView=Dimension "Dag"
#XFLD: Time Data deletion object title
timeDataUsedIn=(anvendes i {0} modeller)
#XFLD: Time Data deletion object title singular
timeDataUsedInSingular=(anvendes i 1 model)
#XFLD: Time Data deletion table column provider
provider=Udbyder
#XTIT: Time Data dependency dialog title
timeDataDependencyDialogTitle=Afhængigheder
#XTIT: Create schema popup title
createSpaceSchemaUserDialogTitle=Opret bruger for space-skema
#XFLD: Create schema button
createSchemaButton=Opret Open SQL-skema
#XFLD: Generate TimeData button
generateTimeDataButton=Opret tidstabeller og dimensioner
#XFLD: Show dependencies button
showDependenciesButton=Vis afhængigheder
#XFLD: tooltip for disabled buttons when user is not member of space
mustBeMemberOfSpaceForOperation=For at udføre denne operation skal din bruger være medlem af dette space.
#XFLD: Create space schema user button
createSpaceSchemaUserButton=Opret space-skemabruger
#YMSE: API Schema users load error
loadSchemaUsersError=Liste over brugere kunne ikke indlæses.
#XTIT: API Schema-config popup title
schemaUserDetailsDialogTitle=Detaljer for space-skemabruger
#XMSG: API Schema user deletion popup text
deleteSpaceSchemaUserConfirmation=Er du sikker på, at du vil slette den valgte bruger?
#XMSG: API Schema user deletion confirmation text
userDeleteSuccess=Bruger slettet
#YMSE: API Schema user deletion error
userDeleteError=Brugeren kunne ikke slettes.
#XFLD: User deleted
userDeleted=Brugeren er slettet.
#XTIT: Remove user popup title
removeUserConfirmationTitle=Advarsel
#XMSG: Remove user popup text
removeUserConfirmation=Er du sikker på, at du vil fjerne brugeren? Brugeren og dennes tildelte roller i omfanget fjernes fra spacet.
#XMSG: Remove users popup text
removeUsersConfirmation=Er du sikker på, at du vil fjerne brugerne? Brugerne og deres tildelte roller i omfanget fjernes fra spacet.
#XBUT: Confirmation button for remove user dialog
removeUserConfirmationBtn=Fjern
#YMSE: No data text for available roles
noDataAvailableRoles=Spacet er ikke tilføjet nogen rolle i omfang. \n For at der kan tilføjes brugere til spacet, skal det først tilføjes til en eller flere roller i omfang.
#YMSE: No data text for selected roles
noDataSelectedRoles=Ingen valgte roller i omfanget
#XTIT: Schema-config popup title
schemaDetailsDialogTitle=Konfigurationsdetaljer for Open SQL-skema
#XFLD: Label for Read Audit Log
auditLogRead=Aktiver revisionslog for læseoperationer
#XFLD: Label for Change Audit Log
auditLogChange=Aktiver revisionslog for ændringsoperationer
#XFLD: Label Audit Log Retention
auditLogRetention=Behold logge i
#XFLD: Label Audit Log Retention Unit
retentionUnit=Dage
#YMSE: Error when log retention time is set incorrectly
incorrectRetentionTime=Indtast et heltal mellem {0} og {1}
#XFLD: Label for Cross Space Schema Access
spaceSchemaAccess=Forbrug space-skemadata
#XTIT: Confirm deactivation of space schema consumption dialog title
disableSpaceSchemaConsumptionDialogTitle=Stop forbrug af space-skemadata
#XTXT: Confirm deactivation of space schema consumption dialog text
disableSpaceSchemaConsumptionDialogText=Dette Open SQL-skema kan forbruge data for dit space-skema. Hvis du stopper forbrug, vil modeller baseret på space-skemaet muligvis ikke længere fungere.
#XBUT: Confirmation button for the stop consuming space schema data dialog
stopConsuming=Stop forbrug
#XTXT: Tooltip for data lake icon
dataLakeIconTooltip=Dette space anvendes til at tilgå data lake
#XFLD: Tooltip for data lake table entry
dataLakeTableEntryTooltip=Datasø er aktiveret
#XFLD: used In-Memory linit reached tag
usedRamLimitReachedLabel=Hukommelsesgrænse nået
#XFLD: Space storage Limit reached tag
storageLimitReachedLabel=Lagergrænse nået
#XFLD: Minimal Storage Limit Reached tag
minStorageLimitReachedLabel=Min. lagergrænse nået
#XFLD: Space ram tag
ramLimitReachedLabel=Hukommelsesgrænse nået
#XFLD: Minimal In-Memory Limit Reached tag
minRamLimitReachedLabel=Minimum for hukommelsesgrænse nået
#XFLD: Popover text after click on space storage limit warning
storageLimitReachedText=Du har nået den tildelte lagergrænse for space på {0}. Tildel mere lagerplads til space.
#XFLD: System storage tag
systemStorageLimitReachedLabel=Systemlagergrænse nået
#XMSG: Popover text after click on storage limit warning
systemStorageLimitReachedText=Du har nået systemlagergrænsen på {0}. Du kan ikke tildele mere lagerplads til dette space nu.
#XMSG: Schema deletion warning
deleteSchemaConfirmation=Ved sletning af dette åbne SQL-skema slettes også alle gemte objekter og vedligeholdte tilknytninger i skemaet permanent. Fortsæt?
#XMSG: Success message after deleting a schema
schemaDeleteSuccess=Skema slettet
#YMSE: Error while deleting schema.
schemaDeleteError=Skemaet kunne ikke slettes.
#XMSG: Success message after update a schema
schemaUpdateSuccess=Skema opdateret
#YMSE: Error while updating schema.
schemaUpdateError=Skemaet kunne ikke opdateres.
#XMSG: Hint when no password is shown in the dialog.
newPasswordHint=Vi har angivet en adgangskode for dette skema. Hvis du har glemt din adgangskode eller har mistet den, kan du anmode om en ny. Huske at kopiere eller gemme den nye adgangskode.
#XMSG: Hint when showing the password in the dialog.
savePasswordHint=Kopier din adgangskode. Du skal bruge den til at konfigurere en forbindelse til dette skema. Hvis du har glemt din adgangskode, kan du åbne denne dialog for at nulstille den.
#XFLD,50: Hint on entering schema name in creation dialog
schemaNameHint=Dette navn kan ikke ændres, når skema er oprettet.
#XFLD: Open SQL Schemas section sub headline
schemasSQL=Open SQL
#XFLD: Space schema section sub headline
schemasSpace=Space
#XFLD: HDI Container section header
HDIContainers=HDI-containere
#XTXT: Add HDI Containers button tooltip
addHDIContainers=Tilføj HDI-containere
#XTXT: Remove HDI Containers button tooltip
removeHDIContainers=Fjern HDI-containere
#XFLD: Enable HDI Containers feature button
enableHDIContainersButton=Aktiver adgang
#YMSE: No data text for HDI Containers table
noDataHDIContainers=Ingen HDI-containere blev tilføjet.
#YMSE: No data text for Timedata section
noDataTimedata=Der blev ikke oprettet nogen tidstabeller eller dimensioner.
#YMSE: No data text during runtime not available for Timedata section
runtimeNotAvailableNoDataTimedata=Kan ikke indlæse tidstabeller og dimensioner, da kørselstidsdatabasen ikke er tilgængelig.
#YMSE: No data text during runtime not available for HDI section
runtimeNotAvailableNoDataHDI=Kan ikke indlæse HDI-containere, da kørselstidsdatabasen ikke er tilgængelig.
#YMSE: Error while fetching HDI Containers
fetchHDIContainersError=HDI-containerne kunne ikke hentes. Prøv igen senere.
#XFLD Table column header for HDI Container names
HDIContainerName=Navn på HDI-container
#XFLD: Enable HDI Containers feature dialog title
enableHDIContainersDialogTitle=Aktiver adgang
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText1New=Du kan aktivere SAP SQL Data Warehousing i din SAP Datasphere-tenant til at udveksle data mellem dine HDI-containere og dine SAP Datasphere-spaces, uden at det er nødvendigt at flytte data.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText2=Det gør du ved at åbne en supportmeddelelse ved at klikke på knappen nedenfor.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText3New=Når din meddelelse er behandlet, skal du bygge en eller flere nye HDI-containere i SAP Datasphere-kørselstidsdatabasen. Derefter erstattes knappen Aktiver adgang af knappen + i afsnittet HDI-containere for alle dine SAP Datasphere-spaces, og du kan føje dine containere til et space.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText41=Har du brug for flere oplysninger? Gå til %%0. Se %%1 for at få detaljerede oplysninger om, hvad der skal inkluderes i supportmeddelelsen.
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkText=SAP Help
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkTextSapNote3057059=SAP-note 3057059
#XBUT: Open Ticket Button Text
openTicket=Åbn supportmeddelelse
#XBUT: Add Button Text
add=Tilføj
#XBUT: Next Button Text
next=Næste
#XBUT: Edit Button Text
editUsers=Rediger
#XBUT: create user Button Text
createUser=Opret
#XBUT: Update user Button Text
updateUser=Vælg
#XTIT: Add HDI Containers Dialog Title
addHDIContainersDialogTitle=Tilføj ikketildelte HDI-containere
#XFLD: No data text for add HDI container dialog
addHDIContainersDialogNoDataText=Vi kunne ikke finde nogen ikketildelte containere. \n Den container, du søger efter, kan allerede være tildelt til et space.
#YMSE: Error while loading assigned HDI Containers
loadAssignedHDIContainersError=Tildelte HDI-containers kunne ikke indlæses.
#YMSE: Error while loading assigned HDI Containers
loadUnassignedHDIContainersError=HDI-containere kunne ikke indlæses.
#XMSG: Success message
succeededToAddHDIContainer=HDI-container tilføjet
#XMSG: Success message
succeededToAddHDIContainerPlural=HDI-containere tilføjet
#XMSG: Success message
succeededToDeleteHDIContainer=HDI-container fjernet
#XMSG: Success message
succeededToDeleteHDIContainerPlural=HDI-containere fjernet
#XFLD: Time data section sub headline
timeDataSection=Tidstabeller og dimensioner
#XFLD: Schemas list entry read audit log enabled indicator
readIndicator=Læs
#XFLD: Schemas list entry change audit log enabled indicator
changeIndicator=Ændr
#XFLD: Remote sources section sub headline
allconnections=Forbindelsestildeling
#XFLD: Remote sources section sub headline
localconnections=Lokale forbindelser
#XFLD: Remote sources section sub headline for open Connectors
openConnectors=SAP Open Connectors
#XFLD: User section sub headline
memberassignment=Medlemstildeling
#XFLD: User assignment section sub headline
userAssignment=Brugertildeling
#XFLD: User section Access dropdown Member
member=Medlem
#XFLD: User assignment section column name
user=Brugernavn
#XTXT: Selected role count
selectedRoleToolbarText=Valgt: {0}
#XTIT: Space detail section connections title
detailsSectionConnections=Forbindelser
#XTIT: Space detail section data access title
detailsSectionDataAccess=Skemaadgang
#XTIT: Space detail section time data title
detailsSectionGenerateData=Tidsdata
#XTIT: Space detail section members title
detailsSectionUsers=Medlemmer
#XTIT: Space detail section Users title
detailsSectionUsersTitle=Brugere
#XTIT: Out of Storage
insufficientStoragePopoverTitle=Ikke mere lager
#XTIT: Storage distribution
storageDistributionPopoverTitle=Disklager brugt
#XTXT: Out of Storage popover text
insufficientStorageText=For at oprette et nyt space skal du reducere den tildelte lagerplads for et andet space eller slette et space, som du ikke længere har brug for. Du kan øge dit samlede systemlager ved at kalde Administrer plan.
#XMSG: Space id length warning
spaceIdLengthWarning=Maksimum på {0} tegn overskredet.
#XMSG: Space name length warning
spaceNameLengthWarning=Maksimum på {0} tegn overskredet.
#XMSG: Space id reserved prefix warning
spaceIdnWarningReservedPrefix=Brug venligst ikke præfikset {0} for at undgå mulige konflikter.
#YMSE: Error while loading open SQL schemas
loadLocalSchemasError=Open SQL-skemaer kunne ikke indlæses.
#YMSE: Error while creating open SQL schema
createLocalSchemaError=Open SQL-skema kunne ikke oprettes.
#YMSE: Error while loading all remote connections
loadRemoteConnectionsError=Kunne ikke indlæse alle fjernforbindelser.
#YMSE: Error while loading space details
loadSpaceDetailsError=Detaljer om space kunne ikke indlæses.
#YMSE: Error while deploying space details
deploySpaceDetailsError=Space kunne ikke implementeres.
#YMSE: Error while copying space details
copySpaceDetailsError=Space kunne ikke kopieres.
#YMSE: Error while loading storage data
loadStorageDataError=Lagerdata kunne ikke indlæses.
#YMSE: Error while loading all users
loadAllUsersError=Kunne ikke indlæse alle brugere.
#YMSE: Failed to reset password
resetPasswordError=Adgangskode kunne ikke nulstilles.
#XMSG: Success message after updating space
resetPasswordSuccessMessage=Ny adgangskode indstillet for skema
#YMSE: DP Agent-name too long
DBAgentNameError=Navnet på DP-agenten er for langt.
#YMSE: Schema-name not valid.
schemaNameError=Navnet på skemaet er ugyldigt.
#YMSE: User name not valid.
UserNameError=Brugernavnet er ugyldigt.
#XTIT: Consumption by Storage Type
consumptionStorageTypeText=Forbrug efter lagertype
#XTIT: Consumption by Schema
consumptionSchemaText=Forbrug efter skema
#XTIT: Overall Consumption by schema
overallConsumptionSchemaText=Samlet tabelforbrug efter skema
#XTIT: Overall Consumption by Table Type
overallConsumptionTableTypeText=Samlet forbrug efter tabeltype
#XTIT: Tables
tableDetailsText=Tabeldetaljer
#XTIT: Table Storage Consumption
tableStorageConsumptionText=Lagerforbrug for tabel
#XFLD: Table Type label
tableTypeLabel=Tabeltype
#XFLD: Schema label
schemaLabel=Skema
#XFLD: reset table tooltip
resetTable=Nulstil tabel
#XFLD: In-Memory label in space monitor
inMemoryLabel=Hukommelse
#XFLD: Disk label in space monitor
diskLabel=Harddisk
#XFLD: Yes
yesLabel=Ja
#XFLD: No
noLabel=Nej
#XFLD: switch label: Want the data in this space to be consumable by default?
allowConsumptionByDefault=Skal data i dette space som standard kunne forbruges?
#XFLD: Business Name
businessNameLabel=Forretningsnavn
#XFLD: Refresh
refresh=Opdater
#XMSG: No filter results title
noFilterResultsTitle=Det ser ud til, at dine filterindstillinger ikke viser nogen data.
#XMSG: No filter results message
noFilterResultsMsg=Forsøg at præcisere dine filterindstillinger, og hvis du så stadig ikke ser data, kan du oprette nogle tabeller i datageneratoren. Når de forbruger lagerplads, kan du overvåge dem her.
#XMSG: Space monitoring Hana state red title
spaceMonitorHanaRedTitle=Kørselstidsdatabasen er ikke tilgængelig.
#XMSG: Space monitoring Hana state red message
spaceMonitorHanaRedMsg=Da kørselstidsdatabasen ikke er tilgængelig, er visse funktioner deaktiveret, og vi kan ikke vise oplysninger på denne side.
#YMSE: Error while creating space schema user
createSpaceSchemaUserError=Space-skemabruger kunne ikke oprettes.
#YMSE: Error User name already exists
userAlreadyExistsError=Brugernavn findes allerede.
#YMSE: Error Authentication failed
authenticationFailedError=Godkendelse mislykkedes.
#XMSG: Message strip message when a schema user is locked
userLockedMessageStrip=Brugeren er låst pga. for mange mislykkede logonforsøg. Anmod om en ny adgangskode for at låse brugeren op.
#XMSG: Success message after reset and unlock schema user
resetUnlockPasswordSuccessMessage=Ny adgangskode er indstillet, og brugeren er låst op
#XMSG: user is locked message
userLockedMessage=Bruger er spærret.
#XCOL: Users table-view column Role
spaceRole=Rolle
#XCOL: Users table-view column Scoped Role
spaceScopedRole=Rolle i omfang
#XCOL: Users table-view column Space Admin
spaceAdmin=Space-administrator
#XFLD: User section dropdown value Viewer
viewer=Fremviser
#XFLD: User section dropdown value Modeler
modeler=Modeler
#XFLD: User section dropdown value Data Integrator
dataIntegrator=Data Integrator
#XFLD: User section dropdown value Space Administrator
spaceAdministrator=Space-administrator
#XMSG: Success message -  Space Role updated successfully.
updateUserSpaceRoleSuccess=Space-rolle opdateret
#XMSG: Error message - Failed to update Space Role.
updateUserSpaceRoleError=Space-rolle blev ikke opdateret.
#XFLD:
databaseUserNameSuffix=Suffiks for databasebrugernavn
#XTXT: Space Schema password text
spaceSchemaPasswordText=For at konfigurere en forbindelse til dette skema skal du kopiere din adgangskode. Hvis du har glemt din adgangskode, kan du altid anmode om en ny.
#XFLD: Cloud Platform label in space schema dialog
cloudPlatformLabel=Cloud Platform
#XTXT: Cloud Platform text in space schema dialog
cloudPlatformText=For at konfigurere adgang via denne bruger, skal du aktivere forbruget og kopiere legitimationsoplysningerne. Hvis du kun kan kopiere legitimationsoplysningerne uden en adgangskode, skal du sørge for at tilføje adgangskoden senere.
#XFLD: enable consumption in CloudPlatform label in space schema dialog
enableConsumptionInCloudPlatformLabel=Aktiver forbrug i Cloud Platform
#XFLD: Credentials for User-Provided Service label in space schema dialog
cloudPlatformCredentialsLabel=Legitimationsoplysninger for brugerangivet service:
#XFLD: Credentials for User-Provided Service (Without Password) label in space schema dialog
cloudPlatformCredentialsWithoutPasswordLabel=Legitimationsoplysninger for brugerangivet service (uden adgangskode):
#XFLD: Copy Credentials label in space schema dialog
copyCredentialsLabel=Kopier legitimationsoplysninger uden adgangskode
#XFLD: Copy Credentials label in space schema dialog
copyFullCredentialsLabel=Kopier fulde legitimationsoplysninger
#XFLD: Copy Password label in space schema dialog
copyPasswordLabel=Kopier adgangskode
#XMSG: Credentials copied to clipboard
credentialsCopiedMessage=Legitimationsoplysninger kopieret til udklipsholder
#XMSG: Password copied to clipboard
passwordCopiedMessage=Adgangskode kopieret til udklipsholder
#XMSG: create database user dialog title
createDatabaseUserDialogTitle=Opret databasebruger
#XMSG: Database Users section title
databaseUsers=Databasebrugere
#XMSG: Database User Details dialog title
databaseUserDetailsDialogTitle=Databasebrugerdetaljer
#XFLD: database user read audit log
databaseUserAuditLogRead=Aktiver revisionslogge for læseoperationer og behold logge for
#XFLD: database user change audit log
databaseUserAuditLogChange=Aktiver revisionslogge for ændringsoperationer og behold logge for
#XMSG: Cloud Platform Access
cloudPlatformAccess=Cloud Platform-adgang
#XFLD: Enable Consumption in Cloud Platform hint
enableConsumptionCloudPlatformHint=Konfigurer adgang til din SAP HANA Deployment Infrastructure (HDI)-container via denne databasebruger. SQL-modellering skal være aktiveret for at oprette forbindelse til din HDI-container
#XFLD: Enable HDI Consumption
enableConsumptionCloudPlatform=Aktiver HDI-forbrug
#XFLD: Enable Consumption hint
enableConsumptionHint=Ønsker du, at data i dit space skal kunne forbruges af andre værktøjer eller apps?
#XFLD: Enable Consumption
enableConsumption=Aktiver SQL-forbrug
#XFLD: Enable Modeling
enableModeling=Aktiver SQL-modellering
#XMSG: Privileges for Data Modeling
privilegesModeling=Dataindtagelse
#XMSG: Privileges for Data Consumption
privilegesConsumption=Dataforbrug for eksterne værktøjer
#XFLD: SQL Modeling
sqlModeling=SQL-modellering
#XFLD: SQL Consumption
sqlConsumption=SQL-forbrug
#XFLD: enabled
enabled=Aktiveret
#XFLD: disabled
disabled=Inaktiveret
#XFLD: Edit Privileges
editPrivileges=Rediger rettigheder
#XFLD: Open Database Explorer
openDBX=Åbn Database Explorer
#XFLD: create database user hint
databaseCreateHint=Bemærk, at det ikke længere vil være muligt at ændre brugernavnet igen, når du har gemt.
#XFLD: Internal Schema Name
internalSchemaName=Internt skemanavn
#YMSE: Failed to load database users
loadDatabaseUserError=Databasebrugere kunne ikke indlæses
#YMSE: Failed to delete database user
deleteDatabaseUsersError=Databasebrugere kunne ikke slettes
#XMSG: Success message after deleting database user
deleteDatabaseUserSuccess=Databasebruger slettet
#XMSG: Success message after deleting database users
deleteDatabaseUsersSuccess=Databasebrugere slettet
#XMSG: Success message after creating database user
createDatabaseUserSuccessMessage=Databasebruger oprettet
#YMSE: Failed to create database user
createDatabaseUserError=Databasebruger kunne ikke oprettes
#XMSG: Success message after updating database user
updateDatabaseUserSuccessMessage=Databasebruger opdateret
#YMSE: Failed to update database user
updateDatabaseUserError=Databasebruger kunne ikke opdateres
#XFLD: HDI Consumption
hdiConsumption=HDI-forbrug
#XTIT: Space detail section Database Access title
detailsSectionDatabaseAccess=Databaseadgang
#XTXT: Database users consumption hint
databaseUsersConsumptionHint=Gør data for dit space til at forbruge som standard. Modellerne i generatorerne vil automatisk tillade, at dataene kan forbruges.
#XFLD: Default Consumption of Space Data:
defaultConsumptionOfSpaceData=Standardforbrug af space-data:
#XFLD: Database User Name
databaseUserName=Databasebrugernavn
#XMSG: Database User creation validation error message
databaseUserValidationError=Det ser ud til, at nogle felter er ugyldige. Kontroller de krævede felter for at fortsætte.
#XMSG: SQL modeling can’t be enabled since this user has been migrated.
migratedDatabaseUserHint=Dataindtagelse kan ikke aktiveres, da denne bruger er migreret.
#XBUT: Remove Button Text
remove=Fjern
#XBUT: Remove Spaces Button Text
removeSpaces=Fjern spaces
#XBUT: Remove Objects Button Text
removeObjects=Fjern objekter
#XMSG: No members have been added yet.
noMembersAssigned=Der er endnu ikke tilføjet nogen medlemmer.
#XMSG: No users have been added yet.
noUsersAssigned=Der er endnu ikke tilføjet nogen brugere.
#XMSG: No database users have been created or your filter is not showing any data.
noDatabaseUsers=Der er ikke oprettet nogen databasebrugere, eller dit filter viser ingen data.
#XMSG: Please enter a user name.
noDatabaseUsername=Indtast et brugernavn.
#XMSG: The user name is too long. Please use a shorter one.
databaseUsernameTooLong=Brugernavnet er for langt. Brug et kortere brugernavn.
#XMSG: No privileges have been enabled and this database user will have limited functionality. Do you still want to continue?
noPrivilegesConfirmationMessage=Der er ikke aktiveret nogen rettigheder, og denne databasebruger vil have begrænsede funktioner. Vil du stadig fortsætte?
#XMSG: To enable audit logs for change operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogChangeMessage=For at aktivere revisionslogge for ændringshandlingerne skal dataindtagelse også aktiveres. Vil du gøre dette?
#XMSG: To enable audit logs for read operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogReadMessage=For at aktivere revisionslogge for læsehandlingerne skal dataindtagelse også aktiveres. Vil du gøre dette?
#XMSG: To enable HDI consumption data ingestion and data consumption needs to be enabled. Do you want to enable all?
enableHDIConsumptionMessage=For at aktivere HDI-forbrug skal dataindtagelse og dataforbrug også aktiveres. Vil du gøre dette?
#XMSG:
databaseUserPasswordText=For at konfigurere en forbindelse til denne databasebruger skal du kopiere din adgangskode. Hvis du har glemt din adgangskode, kan du altid anmode om en ny.
#XTIT: Space detail section members title
detailsSectionMembers=Medlemmer
#XMSG: New password set
newPasswordSet=Ny adgangskode er indstillet
#XFLD: Data Ingestion
dataIngestion=Dataindtagelse
#XFLD: Data Consumption
dataConsumption=Dataforbrug
#XFLD: Privileges
privileges=Rettigheder
#XFLD: Enable Data ingestion
enableDataIngestion=Aktiver dataindtagelse
#XMSG: Log the read and change operations for data ingestion.
databaseUserAuditHint=Log læse- og ændringshandlingerne til dataindtagelse.
#XMSG: Make your space data available in your HDI containers.
databaseUserHDIHint=Gør dine data for spacet tilgængeligt i dine HDI-containere.
#XFLD: Enable Data consumption
enableDataConsumption=Aktiver dataforbrug
#XMSG: Allow other apps or tools to consume your space data.
enableDataConsumptionHint=Tillad andre apps eller værktøjer at forbruge dine data for spacet.
#XMSG: To set up access via this schema user copy the credentials to your user-provided service. In case you can only copy the credentials without a password make sure to add the password later.
databaseUserHDIConsumptionHint=For at konfigurere adgang via denne bruger skal du kopiere legitimationsoplysningerne til din brugerangivne service. Hvis du kun kan kopiere legitimationsoplysningerne uden en adgangskode, skal du sørge for at tilføje adgangskoden senere.
#XFLD: Data Flow Runtime Capacity label in space overview
dataFlowRuntimeCapacity=Dataflows kørselstidskapacitet ({0}:{1} timer af {2} timer)
#XFLD: Could not load Data Flow Runtime Capacity
dataFlowRuntimeCapacityError=Dataflows kørselstidskapacitet blev ikke indlæst
#XMSG: User can grant data consumption to other users.
databaseUserConsumptionWithGrantOptionHint=Bruger kan tilldele dataforbrug til andre brugere.
#XFLD: Enable data consumption with grant option
enableConsumptionWithGrantOption=Aktivér dataforbrug med tildelingsindstillingen
#XMSG: To enable data consumption with grant option data consumption needs to be enabled. Do you want to enable both?
enableConsumptionWithGrantMessage=Hvis dataforbrug skal aktiveres med tildelingsindstillingen, skal dataforbrug være aktiveret. Vil du aktivere begge?
#XFLD: Enable Automated Predictive Library (APL) and Predictive Analysis Library (PAL)
allowUsingScriptServerFunctionality=Aktiver Automated Predictive Library (APL) og Predictive Analysis Library (PAL)
#XMSG: User can use SAP HANA Cloud embedded machine learning functions.
allowUsingScriptServerFunctionalityHint=Bruger kan bruge SAP HANA Cloud-integrerede maskinindlæringsfunktioner.
#XFLD: Password Policy
passwordPolicy=Adgangskodepolitik
#XMSG: Password Policy
passwordPolicyHint=Aktiver eller deaktiver den konfigurerede adgangskodepolitik her.
#XFLD: Enable Password Policy
enablePasswordPolicy=Aktiver adgangskodepolitik
#XMSG: Read Access to the Space Schema
readAccessTitle=Læseadgang til space-skema
#XMSG: read access hint
readAccessHint=Tillad, at databasebrugeren forbinder eksterne værktøjer til space-skemaet og læsevisninger, der er eksponeret for forbrug.
#XFLD: Space Schema
spaceSchema=Space-skema
#XFLD: Enable Read Access (SQL)
enableReadAccess=Aktiver læseadgang (SQL)
#XMSG: Allow the user to grant read access to other users.
withGrantOptionHint=Tillad, at brugeren giver læseadgang til andre brugere.
#XFLD: With Grant Option
withGrantOption=Med tildelingsmulighed
#XMSG: Make your space data available in your HDI containers.
enableHDIConsumptionHint=Gør dine data for spacet tilgængeligt i dine HDI-containere.
#XFLD: Enable HDI Consumption
enableHDIConsumption=Aktiver HDI-forbrug
#XMSG: Write Access to the User’s Open SQL Schema
writeAccessTitle=Skriveadgang til brugerens Open SQL-skema
#XMSG: write access hint
writeAccessHint=Tillad, at databasebrugeren forbinder eksterne værktøjer til brugerens Open SQL-skema til at oprette dataentiteter og indsamle data til brug i spacet.
#XFLD: Open SQL Schema
openSQLSchema=Open SQL-skema
#XFLD: Enable Write Access (SQL DDL & DML)
enableWriteAccess=Aktiver skriveadgang (SQL, DDL og DML)
#XMSG: audit hint
auditHint=Log læse- og ændringshandlingerne i Open SQL-skemaet.
#XMSG: data consumption hint
dataConsumptionHint=Eksponer som standard alle nye visninger i spacet for forbrug. En modeler kan tilsidesætte denne indstilling for individuelle visninger via kontakten "Eksponer for forbrug" i sidepanelet for visningsoutput. Du kan også vælge det format, i hvilket visninger eksponeres.
#XFLD: Expose for Consumption by Default
exposeDataConsumption=Eksponer for forbrug som standard
#XMSG: database users hint consumption hint
databaseUsersHint2New=Opret databasebrugere til at forbinde eksterne værktøjer til SAP Datasphere. Angiv rettigheder, der tillader, at brugere læser data for spacet og opretter dataentiteter (DDL) og indsamler data (DML) til brug i spacet.
#XFLD: Read
read=Læs
#XFLD: Read (HDI)
readHDI=Læs (HDI)
#XFLD: Write
write=Skriv
#XMSG: HDI Containers Hint
HDIContainersHint2=Aktiver adgang til dine containere for SAP HANA Deployment Infrastructure (HDI) i dit space. En modeler kan bruge HDI-artefakter som kilder for visninger, og HDI-klienter kan få adgang til data for dit space.
#XMSG: Open info dialog
openDatabaseUserInfoDialog=Åbn infodialogen
#XMSG: Database user is locked. Open info dialog to unlock
openLockedDatabaseUserInfoDialog=Databasebruger er låst. Åbn dialogen for at låse op.
#XFLD: Table
table=Tabel
#XFLD: Space section partner configuration title
partnerConnectionConfigurationSectionTitle=Partnerforbindelse
#XFLD: Space section partner configuration subtitle
partnerConnectionConfigurationSectionSubTitle=Konfiguration af partnerforbindelse
#XFLD: Space section partner configuration hint
partnerConnectionConfigurationSectionHint=Definer din egen partnerforbindelsesflise ved at tilføje din iFrame-URL og ikon. Denne konfiguration er kun tilgængelig for denne tenant.
#XFLD: Table Name Field
partnerConnectionConfigurationName=Navn på flise
#XFLD: Table iFrame URL field
partnerConnectionConfigurationiFrameURL=iFrame-URL
#XFLD: Table iFrame Post Message Origin field
partnerConnectionConfigurationiFramePostMessageOrigin=Oprindelse for iFrame Post Message
#XFLD: Table icon/image name
partnerConnectionConfigurationIcon=Symbol
#XMSG: Table no result found
partnerConnectionConfigurationNotfound=Der blev ikke fundet konfiguration(er) af partnerforbindelse(r).
#XMSG: Partner Connection Confgurations are not available
partnerConnectionConfigurationNotAvailable=Konfiguration af partnerforbindelser kan ikke vises, når kørselstidsdatabasen er utilgængelig.
#XFLD: Dialog Create Partner Connection Configuration
partnerConnectionConfigurationDialogTitleAdd=Opret konfiguration af partnerforbindelse
#XFLD: Dialog Create Partner Connection Configuration Upload Icon
partnerConnectionConfigurationButtonUloadPartnerIcon=Upload-ikon
#XFLD: Dialog Create Partner Connection Configuration Select
partnerConnectionConfigurationSelectIcon=Vælg (maksimal størrelse på 200 KB)
#XFLD: Dialog Create Partner Connection Configuration Enter a name
partnerConnectionConfigurationNamePlaceholder=Eksempel på partnerflise
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL
partnerConnectionConfigurationiFrameURLPlaceholder=http://www.example.com
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL Check
partnerConnectionConfigurationiFramePostMessageOriginPlaceholder=.example.com
#XFLD: Dialog Create Partner Connection Configuration Browse
partnerConnectionConfigurationBrowseButton=Gennemse
#XFLD: Create Partner Connection Configuration Msg
partnerConnectionConfigurationUploadMsg=Konfiguration af partnerforbindelse er oprettet.
#XMSG: Error while deleting Partner Connection configuration(s)
partnerConnectionConfigurationDeleteError=Der opstod en fejl i forbindelse med sletning af konfiguration(er) af partnerforbindelse(r).
#XMSG: Partner Connection Configuration has been successfully deleted
partnerConnectionConfigurationDeleteSuccess=Konfiguration af partnerforbindelse er slettet.
#XMSG: Error while retrieving Partner Connection Configurations
partnerConnectionConfigurationLoadError=Der opstod en fejl i forbindelse med hentning af konfigurationer af partnerforbindelser.
#XMSG: Error fileSizeLimitExceeded
partnerConnectionConfigurationfileSizeLimitExceeded=Filen kunne ikke uploades, fordi den overstiger den maksimale størrelse på 200 KB.
#XBUT: Create Partner Connection Configuration
partnerConnectionConfigurationButtonCreate=Opret konfiguration af partnerforbindelse
#XBUT: Delete Partner Connection Configuration
partnerConnectionConfigurationButtonDelete=Slet konfiguration af partnerforbindelse
#XMSG: Partner Tile Creation Failure
upsertPartnerTileFailed=Partnerflise kunne ikke oprettes.
#XMSG: Partner Tile Deletion Failure
deletePartnerTileFailed=Partnerflise kunne ikke slettes.
#XMSG: HANA cloud connector settings reset failure
resetHANASCCSettingsFailed=Nulstilling af brugerdefinerede indstillinger for SAP HANA Cloud-konnektor mislykkedes
#XFLD: Workload Class
workloadClass=Arbejdsbelastningsklasse
#XFLD: Workload Management
workloadManagement=Workload management
#XFLD: Priority
workloadClassPriority=Prioritet
#XMSG:
workloadManagementPriorityHint=Du kan angive prioriteringen af dette space, når du laver forespørgsler til databasen. Indtast en værdi fra 1 (laveste prioritet) til 8 (højeste prioritet). I tilfælde af at spaces konkurrerer om tilgængelige tråde, køres spaces med højere prioritet før spaces med lavere prioritet.
#XMSG:
workloadClassPriorityHint=Du kan angive prioriteten af et space fra 0 (lavest) til 8 (højest). Sætningerne i et space med en høj prioritet udføres før sætningerne i andre spaces med en lavere prioritet. Standardprioriteten er 5. Da værdien 9 er reserveret til systemoperationer, er den ikke tilgængelig for et space.
#XFLD: Statement Limits
workloadclassStatementLimits=Sætningsgrænser
#XFLD: Workload Configuration
workloadConfiguration=Konfiguration af arbejdsbelastning
#XMSG:
workloadClassStatementLimitsHint=Du kan angive det maksimale antal (eller procent) af tråde og GB hukommelse, som sætninger, der kører samtidigt i det pågældende space, kan forbruge. Du kan angive en vilkårlig værdi eller procent mellem 0 (ingen grænse) og den samlede hukommelse og tråde, der er tilgængelige i tenant. \n\n Hvis du angiver en trådgrænse, skal du være opmærksom på, at den kan nedsætte ydelsen. \n\n Hvis du angiver en hukommelsesgrænse, udføres de sætninger, der når hukommelsesgrænsen, ikke.
#XMSG:
workloadClassStatementLimitsDescription=I standardkonfigurationen er der afsat god plads til ressourcegrænser, og samtidig forhindrer konfigurationen, at et enkelt space overbelaster systemet.
#XMSG:
workloadClassStatementLimitCustomDescription=Du kan angive en maksimal samlet tråd og hukommelsesgrænser, som sætninger, der køres samtidigt i det pågældende space, kan forbruge.
#XMSG:
totalStatementThreadLimitHelpText=Hvis grænsen for tråden sættes for lavt, kan det påvirke sætningens performance, mens for højt satte værdier eller 0 kan give spacet mulighed for at forbruge alle tilgængelige systemtråde.
#XMSG:
totalStatementMemoryLimitHelpText=Hvis hukommelsesgrænsen sættes for lavt, kan det forårsage problemer med manglende hukommelse, mens for højt satte værdier eller 0 kan give spacet mulighed for at forbruge al tilgængelig systemhukommelse.
#XMSG:
totalStatementThreadLimitHelpTextRestricted=Indtast en procentsats mellem 1 % og 70 % (eller det tilsvarende antal) af det totale antal tråde, der er tilgængelige i din tenant. Hvis trådens grænse sættes for lavt, kan det påvirke sætningens ydeevne, mens for højt satte værdier kan påvirke sætningers ydeevne i andre spaces.
#XMSG:
totalStatementThreadLimitHelpTextDynamic=Indtast en procentsats mellem 1 % og {0} % (eller det tilsvarende antal) af det totale antal tråde, der er tilgængelige i din tenant. Hvis trådens grænse sættes for lavt, kan det påvirke sætningens ydeevne, mens for højt satte værdier kan påvirke sætningers ydeevne i andre spaces.
#XMSG:
totalStatementMemoryLimitHelpTextNew=Indtast en værdi eller procentsats mellem 0 (ingen grænse) og den samlede mængde hukommelse, der er tilgængelig i din tenant. Hvis hukommelsesgrænsen sættes for lavt, kan det påvirke sætningens ydeevne, mens for højt satte værdier kan påvirke sætningers ydeevne i andre spaces.
#XFLD: Total Statement Thread Limit
totalStatementThreadLimit=Grænse for samlet sætningstråd
#XFLD: Segmented button label
workloadclassPercentage=%
#XFLD: Segmented button label
workloadclassGigaByte=GB
#XFLD, 80: Segmented button label
workloadclassThreads=Tråde
#XFLD: Total Statement Memory Limit
totalStatementMemoryLimit=Grænse for samlet sætningshukommelse
#YMSE: Error while loading customer hana info
loadCustomerHanaInfoError=SAP HANA-oplysninger for kunde kunne ikke indlæses.
#XMSG:
minimumLimitReached=Minimumsgrænse nået.
#XMSG:
maximumLimitReached=Maksimumsgrænse nået.
#XMSG: Name Taken for Technical Name
technical-name-taken=En forbindelse med det tekniske navn, du har indtastet, findes allerede. Indtast et andet navn.
#XMSG: Name Too long for Technical Name
technical-name-too-long=Det tekniske navn, du har indtastet, overskrider 40 tegn. Indtast et navn med færre tegn.
#XMSG: Technical name field empty
technical-name-field-empty=Indtast et teknisk navn.
#XMSG: Generic error when user enters invalid characters for a field
field-invalid-characters=Du kan kun bruge bogstaver (a-z), tal (0-9) og underscores (_) til navnet.
#XMSG: Generic error when user enters invalid initial characters for a field
field-underscore-trim=Det navn, du har indtastet, må ikke starte eller slutte med underscore (_).
#XFLD: Checkbox label for Enable Statement Limits
enableStatementLimits=Aktiver sætningsgrænser
#XFLD: Connection SubSection Settings Title in space details
connectionSettings=Indstillinger
#XMSG: Connections tool hint in Space details section
connectionsToolHint=Hvis du vil oprette eller redigere forbindelser, skal du åbne appen Forbindelser i sidenavigationen eller klikke her:
#XBUT: Connection list Open Connection Tool Button
button-connectiontool=Gå til Forbindelser
#XFLD: Not deployed label on space tile
notDeployedLabel=Space er endnu ikke implementeret.
#XFLD: Not deployed additional text on space tile
notDeployedText=Implementer det pågældende space.
#XFLD: Corrupt space label on space tile
corruptSpace=Noget gik galt.
#XFLD: Corrupt space additional text on space tile
corruptSpaceHint=Prøv at implementere igen, eller kontakt support
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Revisionslogdata
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Administrationsdata
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Andre data
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Data i spaces
#XMSG: Unlock Space confirmation text
unlockSpaceConfirmation=Er du sikker på, at du vil låse spacet op?
#XMSG: Lock Space confirmation text
lockSpaceConfirmation=Er du sikker på, at du vil låse spacet?
#XFLD: Lock
lock=Lås
#XFLD: Unlock
unlock=Lås op
#XFLD: Locking
locking=Låser
#XMSG: Success message after locking space
lockSpaceSuccessMsg=Space er låst
#XMSG: Success message after unlocking space
unlockSpaceSuccessMsg=Space er låst op
#XMSG: Success message after locking spaces
lockSpacesSuccessMsg=Spaces er låst
#XMSG: Success message after unlocking spaces
unlockSpacesSuccessMsg=Spaces er låst op
#YMSE: Error while locking a space
lockSpaceError=Spacet kan ikke låses.
#YMSE: Error while unlocking a space
unlockSpaceError=Spacet kan ikke låses op.
#XTIT: popup title Warning
confirmationWarningTitle=Advarsel
#XMSG: Message strip message when a space was locked manually
manualLockedMessage=Spacet er låst manuelt.
#XMSG: Message strip message when a space was locked due to the amount of generated audit logs
auditLogLockedMessage=Spacet er låst af systemet, fordi revisionslogge forbruger en stor mængde diskplads i GB.
#XMSG: Message strip message when a space was locked because the assigned quota was exceeded.
quotaLockedMessage=Spacet er låst af systemet, fordi det overskrider dets allokeringer af hukommelse eller disklager.
#XMSG: Unlock Spaces confirmation text
unlockSpacesConfirmation=Er du sikker på, at du vil låse de valgte spaces op?
#XMSG: Lock Spaces confirmation text
lockSpacesConfirmation=Er du sikker på, at du vil låse de valgte spaces?
#XTIT: Scoped role editor dialog title
scopedRoleEditorDialogTitle=Editor for rolle i omfanget
#XTIT: ECN Management title
ecnManagementTitle=Administration af spaces og elastiske beregningsnoder
#XFLD: ECNs
ecns=Elastiske beregningsnoder
#XFLD: ECN phase Ready
ecnReady=Klar
#XFLD: ECN phase Running
ecnRunning=Kører
#XFLD: ECN phase Initial
ecnInitial=Ikke klar
#XFLD: ECN phase Starting
ecnStarting=Starter
#XFLD: ECN phase Starting Failed
ecnStartingFailed=Kunne ikke starte
#XFLD: ECN phase Stopping
ecnStopping=Stopper
#XFLD: ECN phase Stopping Failed
ecnStoppingFailed=Kunne ikke stoppe
#XBTN: Assign Button
assign=Tildel spaces
#XBTN: Start Header-Button
start=Start
#XBTN: Update Header-Button
repair=Opdater
#XBTN: Stop Header-Button
stop=Stop
#XFLD: ECN hours remaining
ecnHoursRemaining=1.000 timer resterer
#XFLD: ECN block-hours remaining
ecnBlockHoursRemaining={0} resterende bloktimer
#XFLD: ECN block-hour remaining
ecnBlockHourRemaining={0} resterende bloktime
#XTIT: ECN creation dialog title
ecnCreationDialogTitle=Opret elastisk beregningsnode
#XTIT: ECN edit dialog title
ecnEditDialogTitle=Rediger elastisk beregningsnode
#XTIT: ECN deletion dialog title
ecnDeletionDialogTitle=Slet elastisk beregningsnode
#XTIT: ECN Space assignment dialog title
ecnSpaceAssignmentDialogTitle=Tildel spaces
#XFLD: ECN ID
ECNIDLabel=Elastisk beregningsnode
#XTXT: Selected toolbar text
selectedToolbarText=Valgt: {0}
#XTIT: Elastic Compute Nodes
ECNslong=Elastiske beregningsnoder
#XTIT: Object assignment - Table header: Objects available
objectsAvailable=Antal objekter
#XTIT: Object assignment - Dialog header text
selectObjects=Vælg de spaces og objekter, du vil tildele til din elastiske beregningsnode:
#XTIT: Object assignment - Table header title: Objects
objects=Objekter
#XTIT: Object assignment - Table header: Type
type=Type
#XMSG: Message strip delete database user audit warning
deleteDatabaseUserWarningMessageStrip=Vær opmærksom på, at sletning af en databasebruger vil medføre sletning af alle genererede revisionslogposter. Hvis du vil beholde revisionsposterne, skal du overveje at eksportere dem, inden du sletter databasebrugeren.
#XMSG: Message strip delete HDI container audit logs impact
deleteHDIContainerWarningMessageStrip=Vær opmærksom på, at fjernelse af tildelingen af en HDI-container fra spacet vil medføre sletning af alle genererede revisionslogposter. Hvis du vil beholde revisionsposterne, skal du overveje at eksportere dem, inden du fjerner tildelingen af HDI-containeren.
#XTXT: All audit logs
allAuditLogs=Alle revisionslogposter genereret for spacet
#XMSG: Message strip disable audit warning
disableAuditWarningMessageStrip=Vær opmærksom på, at deaktivering af en revisionspolitik (læse- eller ændringshandlinger) vil medføre sletning af alle dens revisionslogposter. Hvis du vil beholde revisionslogposterne, skal du overveje at eksportere dem, inden du deaktiverer revisionspolitikken.
#XTIT: No Spaces assigned to ECN Illustration title
noSpacesECNTitle=Der er ingen spaces eller objekter tildelt endnu
#XTIT: No Spaces assigned to ECN Illustration description
noSpacesECNDescription=Du starter med at arbejde med din elastiske beregningsnode ved at tildele et space eller objekter til den.
#XTIT: No Spaces Illustration title
noSpacesTitle=Der er endnu ikke oprettet noget space
#XTIT: No Spaces Illustration description
noSpacesDescription=Opret et space for at begynde at indsamle data.
#XTIT: No Spaces Illustration title
noDeletedSpacesTitle=Papirkurven er tom
#XTIT: No Spaces Illustration description
noDeletedSpacesDescription=Du kan gendanne dine slettede spaces herfra.
#XTXT:  {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDatabaseUsers=Når spacet er implementeret, slettes følgende databasebrugere {0} og kan ikke gendannes:
#XTIT: Delete database users
deleteDatabaseUsersTitle=Slet databasebrugere
#YMSE: Error if ecn id already exists
ecnIDInputDuplicateError=Id findes allerede.
#YMSE: Error if ecn id contains invalid characters
ecnIDInputInvalidError=Brug kun små bogstaver a-z og 0-9
#YMSE: Error if ecn id is too short
ecnIDInputTooShortError=Id skal være på mindst {0} tegn.
#XMSG: ecn id length warning
ecnIdLengthWarning=Maksimum på {0} tegn overskredet.
#XFLD: open System Monitor
systemMonitor=Systemmonitor
#XFLD: open ECN schedule dialog menu entry
schedule=Tidsplan
#XFLD: open create ECN schedule dialog
createSchedule=Opret tidsplan
#XFLD: open change ECN schedule dialog
changeSchedule=Rediger tidsplan
#XFLD: open delete ECN schedule dialog
deleteSchedule=Slet tidsplan
#XFLD: open self assign ECN schedule dialog
selfAssignSchedule=Tildel tidsplan til mig
#XFLD: open pause ECN schedule dialog
pauseSchedule=Sæt tidsplan på pause
#XFLD: open resume ECN schedule dialog
resumeSchedule=Genoptag tidsplan
#XFLD: View Logs
viewLogs=Vis logge
#XFLD: Compute Blocks
computeBlocks=Beregningsblokke
#XFLD: Memory label in ECN creation dialog
ecnMemory=Hukommelse (GB)
#XFLD: Storage label in ECN creation dialog
ecnStorage=Lagerplads (GB)
#XFLD: Number of CPU label in ECN creation dialog
ecnCPUCount=Antal CPU
#XFLD: ECN updated by label
changedBy=Ændret af
#XFLD: ECN updated on label
changedOn=Ændret den
#XMSG: Success message after creating a Elastic Compute Node
createEcnSuccessMessage=Elastisk beregningsnode oprettet
#YMSE: Error while creating a Elastic Compute Node
createEcnError=Elastisk beregningsnode kunne ikke oprettes
#XMSG: Success message after updating a Elastic Compute Node
updateEcnSuccessMessage=Elastisk beregningsnode opdateret
#XMSG: Error while updating a Elastic Compute Node
udpateEcnError=Elastisk beregningsnode blev ikke opdateret
#XMSG: Success message after deleting a Elastic Compute Node
deleteEcnSuccessMessage=Elastisk beregningsnode slettet
#YMSE: Error while deleting a Elastic Compute Node
deleteEcnError=Elastisk beregningsnode kunne ikke slettes
#XMSG: Success message after starting a Elastic Compute Node
startingEcnSuccessMessage=Starter elastisk beregningsnode
#XMSG: Success message after stopping a Elastic Compute Node
stoppingEcnSuccessMessage=Stopper elastisk beregningsnode
#YMSE: Error while starting a Elastic Compute Node
startingEcnError=Elastisk beregningsnode kunne ikke starte
#YMSE: Error while stopping a Elastic Compute Node
stoppingEcnError=Elastisk beregningsnode kunne ikke stoppe
#XBUT: Add Object button for an ECN
assignObjects=Tilføj objekter
#XFLD: Enable auto object assignment label
enableAutoObjAssignment=Tilføj alle objekter automatisk
#XFLD: object type label to be assigned
objectTypeLabel=Type (semantisk brug)
#XFLD: assigned object type label
assignedObjectTypeLabel=Type
#XFLD: technical name label
TechnicalNameLabel=Teknisk navn
#XFLD: Object selecttion ECN label
objectSelectionECNLabel=Vælg de objekter, du vil tilføje til den elastiske beregningsnode
#XTIT: Add objects dialog title
assignObjectsTitle=Tildel objekter for
#XFLD: object label with object count
objectLabel=Objekt
#XMSG: No objects available to add message.
noObjectsToAssign=Ingen tilgængelige objekter at tildele.
#XMSG: No objects assigned message.
noAssignedObjects=Ingen objekter tildelt.
#XTIT: Remove objects popup title
removeObjectsOrSpacesConfirmationTitle=Advarsel
#XTIT: Delete ECN popup button and title
deleteECNConfirmationButtonAndTitle=Slet
#XMSG: Remove objects popup text
removeObjectsConfirmation=Vil du virkelig fjerne de valgte objekter?
#XMSG: Remove spaces popup text
removeSpacesConfirmation=Vil du virkelig fjerne de valgte spaces?
#XBUT: Confirmation button for objects dialog
removeObjectsOrSpacesConfirmationBtn=Fjern spaces
#XMSG: Success message after removing objects
removeObjectsSuccessMessage=Eksponerede objekter blev fjernet
#XMSG: Success message after assigning objects
assignObjectsSuccessMessage=Eksponerede objekter blev tildelt
#XFLD: auto object assignment label for space tile
autoObjAssignmentLabel=Alle eksponerede objekter
#XFLD: Spaces tab label
spacesTabLabel=Spaces
#XFLD: Exposed objects tab label
consumptionObjectsTabLabel=Eksponerede objekter
#XMSG: Success message after unassigning spaces
spacesUnassignedSuccessMessage=Spaces blev fjernet
#XMSG: Success message after unassigning space
spaceUnassignedSuccessMessage=Space blev fjernet
#YMSE: Error while assigning / unassigning spaces
spacesAssignUnassignError=Spaces kunne ikke tildeles eller fjernes.
#YMSE: Error while removing objects
removeObjectsError=Vi kunne ikke tildele eller fjerne objekterne.
#YMSE: Error while removing object
removeObjectError=Vi kunne ikke tildele eller fjerne objektet.
#YMSE: Error when saving the ecn with invalid value
invalidSizingPlanError=Det tidligere valgte tal er ikke længere gyldigt. Vælg et gyldigt tal.
#YMSE: Error when saving the ecn with invalid value
invalidPerformanceClassError=Vælg en gyldig ydeevneklasse.
#YMSE: Error when saving the ecn with invalid value
performanceClassNotAvailableError=Den tidligere valgte ydeevneklasse "{0}" er i øjeblikket ikke gyldig. Vælg den gyldige ydeevneklasse.
#XTXT: Delete ECN confirmation text
deleteConfirmationTxtECN=Er du sikker på, at du vil slette den elastiske beregningsnode?
#XFLD: tooltip for ? button
help=Hjælp
#XFLD: ECN edit button label
editECN=Konfigurer
#XFLD: Technical type label for ERModel
DWC_ERMODEL=Entitet – relationsmodel
#XFLD: Technical type label for Local Table
DWC_LOCAL_TABLE=Lokal tabel
#XFLD: Technical type label for Remote Table
DWC_REMOTE_TABLE=Ekstern tabel
#XFLD: Technical type label for Analytic Model
DWC_ANALYTIC_MODEL=Analytisk model
#XFLD: Technical type label for Task Chain
DWC_TASKCHAIN=Opgavekæde
#XFLD: Technical type label for Data Flow
DWC_DATAFLOW=Dataflow
#XFLD: Technical type label for Replication Flow
DWC_REPLICATIONFLOW=Replikeringsflow
#XFLD: Technical type label for Transformation Flow
DWC_TRANSFORMATIONFLOW=Transformationsflow
#XFLD: Technical type label for Intelligent Lookup
DWC_IDT=Intelligent opslag
#XFLD: Technical type label for Repository
DWC_REPOSITORY_PACKAGE=Lager
#XFLD: Technical type label for Enterprise Search
DWC_ESMODEL=Virksomhedssøgning
#XFLD: Technical type label for View
DWC_VIEW=Visning
#XFLD: Technical type label for Data Product
DWC_DATAPRODUCT=Dataprodukt
#XFLD: Technical type label for Data Access Control
DWC_DAC=Dataadgangskontrol
#XFLD: Technical type label for Folder
DWC_FOLDER=Mappe
#XFLD: Technical type label for Data Entity
DWC_BUSINESS_ENTITY=Forretningsentitet
#XFLD: Technical type label for Business Entity Variant
DWC_BUSINESS_ENTITY_VARIANT=Variant af forretningsentitet
#XFLD: Technical type label for Responsibility Scenario
DWC_AUTH_SCENARIO=Ansvarsområdescenario
#XFLD: Technical type label for Fact Model
DWC_FACT_MODEL=Faktamodel
#XFLD: Technical type label for Transformation Flow
DWC_PERSPECTIVE=Perspektiv
#XFLD: Technical type label for Consumption Model
DWC_CONSUMPTION_MODEL=Forbrugsmodel
#XFLD: Technical type label for Remote Connection
DWC_REMOTECONNECTION=Ekstern forbindelse
#XFLD: Technical type label for Fact Model Variant
DWC_FACT_MODEL_VARIANT=Faktamodelvariant
#XMSG: Schedule created alert message
createScheduleSuccess=Tidsplan oprettet
#XMSG: Schedule updated alert message
updateScheduleSuccess=Tidsplan opdateret
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Tidsplan slettet
#XMSG: Schedule takeover success message
ownerChangeScheduleSuccess=Tidsplan tildelt til dig
#XMSG: Message for starting pausing of schedule
pauseScheduleStarted=Sætter 1 tidsplan på pause
#XMSG: Message for starting resuming of schedule
resumeScheduleStarted=Genoptager 1 tidsplan
#XFLD: Segmented button label
availableSpacesButton=Tilgængelig
#XFLD: Segmented button label
selectedSpacesButton=Valgt
#XFLD: Visit website button text
visitWebsite=Besøg websted
#XMSG: Warning message on unselecting source language
sourceLanguageRemovalWarning=Det tidligere valgte kildesprog fjernes.
#XBUT: enable translation confirmation button
enableTranslationConfirmationBtn=Aktivér
#XFLD: ECN performance class label
performanceClassLabel=Ydeevneklasse
#XTXT performance class memory text
memoryText=Hukommelse
#XTXT performance class compute text
computeText=Processering
#XTXT performance class high-compute text
highComputeText=Høj processering
#XBUT: Recycle Bin Button Text
recycleBin=Papirkurv
#XBUT: Restore Button Text
restore=Gendan
#XMSG: Warning message for new Workload Management UI
priorityWarning=Dette område er skrivebeskyttet. Du kan ændre space-prioriteten i området System / Konfiguration / Arbejdslisteadministration.
#XMSG: Warning message for new Workload Management UI
workloadWarning=Dette område er skrivebeskyttet. Du kan ændre space-arbejdslisteadministration i området System / Konfiguration / Arbejdslisteadministration.
#XCOL: Space table-view column: Apache Spark vCPUs
sparkManagementVCPUs=Apache Spark-vCPU'er
#XCOL: Space table-view column: Apache Spark Memory (GB)
sparkManagementMemory=Apache Spark-hukommelse (GB)
#XCOL: Space type (from Space Capability) for Data Ingestion Spaces
ingestionSpaceType=Dataproduktindtagelse
#XFLD: Deployment in progress nodatatext for spark table
sparkTableDeploymentInProgress=Ingen data tilgængelige, da dette space i øjeblikket implementeres
#XFLD: Runtime data being loaded nodatatext for spark table
sparkTableDeploymentLoading=Ingen data tilgængelige, da dette space i øjeblikket indlæses
#XCOL: Space type (from Space Capability) for BW Private Cloud Edition
BWPCESpaceType=SAP BW
#XBUT: Go to Instance Mappings Button
navToMappingsButton=Rediger tilknytninger af instans
#XFLD: SAP HANA Data Lake Storage label if less than 1GB
dataLakeStorageUsed=< 1 GB
