#DO NOT REMOVE________________________
#XFLD: Breadcrumb for space monitoring page
spaceMonitorBreadcrumb=監視
#XTXT: Type name for spaces in browser tab page title
space=スペース
#_____________________________________
#XFLD: Spaces label in
spaces=スペース
#XFLD: Manage plan button text
manageQuotaButtonText=プランの管理
#XBUT: Manage resources button
manageResourcesButton=リソースの管理
#XFLD: Create space button tooltip
createSpace=スペースの作成
#XFLD: Create
create=作成
#XFLD: Deploy
deploy=デプロイ
#XFLD: Page
page=ページ
#XFLD: Cancel
cancel=キャンセル
#XFLD: Update
update=更新
#XFLD: Save
save=保存
#XFLD: OK
ok=OK
#XFLD: days
days=日
#XFLD: Space tile edit button label
edit=編集
#XFLD: Auto Assign all objects to space
autoAssign=自動割り当て
#XFLD: Space tile open monitoring button label
openMonitoring=監視
#XFLD: Delete
delete=削除
#XFLD: Copy Space
copy=コピー
#XFLD: Close
close=閉じる
#XCOL: Space table-view column status
status=ステータス
#XFLD: Space status active
activeLabel=有効
#XFLD: Space status locked
lockedLabel=ロック済み
#XFLD: Space status critical
criticalLabel=クリティカル
#XFLD: Space status cold
coldLabel=コールド
#XFLD: Space status deleted
deletedLabel=削除済み
#XFLD: Space status unknown
unknownLabel=不明
#XFLD: Space status ok
okLabel=ヘルシー
#XFLD: Database user expired
expired=期限切れ
#XFLD: deployed
deployed=デプロイ済み
#XFLD: not deployed
notDeployed=未デプロイ
#XFLD: changes to deploy
changesToDeploy=デプロイ対象の変更
#XFLD: pending
pending=デプロイしています
#XFLD: designtime error
designtimeError=設計時エラー
#XFLD: runtime error
runtimeError=実行時エラー
#XFLD: Space created by label
createdBy=作成者
#XFLD: Space created on label
createdOn=作成日付
#XFLD: Space deployed on label
deployedOn=デプロイ日付
#XFLD: Space ID label
spaceID=スペース ID
#XFLD: Priority label
priority=優先度
#XFLD: Space Priority label
spacePriority=スペース優先度
#XFLD: Space Configuration label
spaceConfiguration=スペース設定
#XFLD: Not available
notAvailable=利用不可
#XFLD: WorkloadType default
default=デフォルト
#XFLD: WorkloadType custom
custom=ユーザ定義
#XFLD: Data Lake option label; refers to component SAP HANA Cloud, Data Lake
dataLakeAccess=データレイクアクセス
#XFLD: Translation label
translationLabel=翻訳
#XFLD: Source language label
sourceLanguageLabel=翻訳元言語
#XFLD: Translation CheckBox label
translationCheckBox=翻訳を有効化
#XTXT: Popover hint that space has to be deployed to show user details
openDatabaseUserHintPopoverText=ユーザ詳細を表示するにはスペースをデプロイします。
#XTXT: Popover hint that space has to be deployed to open database explorer
openDatabaseExplorerHintPopoverText=データベースエクスプローラを開くにはスペースをデプロイします。
#XTXT: Text for when the Data Lake check box is disabled
dataLakeOptionDisabled=データレイクはすでに別のスペースで使用されているため、このスペースを使用してデータレイクにアクセスすることはできません。
#XFLD: Data Lake check box text; refers to component SAP HANA Cloud, Data Lake
dataLakeCheckBox=データレイクにアクセスするには、このスペースを使用します。
#XFLD: Space Priority minimum label extension
low=低
#XFLD: Space Priority maximum label extension
high=高
#XFLD: Space name label
spaceName=スペース名
#XFLD: Enable deploy objects checkbox
enableDeployObjects=オブジェクトをデプロイ
#XTIT: Copy spaces dialog title
copySpaceDialogTitle={0} のコピー
#XTXT Human readable text for unselected source language
unSelectedSourceLanguage=(未選択)
#XTXT Human readable text for language code "af"
af=アフリカーンス語
#XTXT Human readable text for language code "ar"
ar=アラビア語
#XTXT Human readable text for language code "bg"
bg=ブルガリア語
#XTXT Human readable text for language code "ca"
ca=カタロニア語
#XTXT Human readable text for language code "zh"
zh=簡体字中国語
#XTXT Human readable text for language code "zf"
zf=中国語
#XTXT Human readable text for language code "hr"
hr=クロアチア語
#XTXT Human readable text for language code "cs"
cs=チェコ語
#XTXT Human readable text for language code "cy"
cy=ウェールズ語
#XTXT Human readable text for language code "da"
da=デンマーク語
#XTXT Human readable text for language code "nl"
nl=オランダ語
#XTXT Human readable text for language code "en-UK"
en-UK=英語 (英国)
#XTXT Human readable text for language code "en"
en=英語 (米国)
#XTXT Human readable text for language code "et"
et=エストニア語
#XTXT Human readable text for language code "fa"
fa=ペルシャ語
#XTXT Human readable text for language code "fi"
fi=フィンランド語
#XTXT Human readable text for language code "fr-CA"
fr-CA=フランス語 (カナダ)
#XTXT Human readable text for language code "fr"
fr=フランス語
#XTXT Human readable text for language code "de"
de=ドイツ語
#XTXT Human readable text for language code "el"
el=ギリシャ語
#XTXT Human readable text for language code "he"
he=ヘブライ語
#XTXT Human readable text for language code "hi"
hi=ヒンディー語
#XTXT Human readable text for language code "hu"
hu=ハンガリー語
#XTXT Human readable text for language code "is"
is=アイスランド語
#XTXT Human readable text for language code "id"
id=インドネシア語
#XTXT Human readable text for language code "it"
it=イタリア語
#XTXT Human readable text for language code "ja"
ja=日本語
#XTXT Human readable text for language code "kk"
kk=カザフ語
#XTXT Human readable text for language code "ko"
ko=韓国語
#XTXT Human readable text for language code "lv"
lv=ラトビア語
#XTXT Human readable text for language code "lt"
lt=リトアニア語
#XTXT Human readable text for language code "ms"
ms=マレー語 (マレーシア)
#XTXT Human readable text for language code "no"
no=ノルウェー語
#XTXT Human readable text for language code "pl"
pl=ポーランド語
#XTXT Human readable text for language code "pt"
pt=ポルトガル語 (ブラジル)
#XTXT Human readable text for language code "pt-PT"
pt-PT=ポルトガル語 (ポルトガル)
#XTXT Human readable text for language code "ro"
ro=ルーマニア語
#XTXT Human readable text for language code "ru"
ru=ロシア語
#XTXT Human readable text for language code "sr"
sr=セルビア語
#XTXT Human readable text for language code "sh"
sh=セルビアクロアチア語
#XTXT Human readable text for language code "sk"
sk=スロバキア語
#XTXT Human readable text for language code "sl"
sl=スロベニア語
#XTXT Human readable text for language code "es"
es=スペイン語
#XTXT Human readable text for language code "es-MX"
es-MX=スペイン語 (メキシコ)
#XTXT Human readable text for language code "sv"
sv=スウェーデン語
#XTXT Human readable text for language code "th"
th=タイ文字
#XTXT Human readable text for language code "tr"
tr=トルコ語
#XTXT Human readable text for language code "uk"
uk=ウクライナ語
#XTXT Human readable text for language code "vi"
vi=ベトナム語
#XTIT: Delete spaces confirmation title
deleteConfirmationHeaderText=スペースの削除
#XTXT: Logically delete space delete confirmation text with space name
logicallyDeleteConfirmationBodyTextWithSpaceName=スペース "{0}" をごみ箱に移動してもよろしいですか?
#XTXT: Logically dpace delete confirmation text for multiple spaces
logicallyDeleteConfirmationBodyTextMultiple=選択した {0} 個のスペースをごみ箱に移動してもよろしいですか?
#XTXT: Delete space delete confirmation text with space name
deleteConfirmationBodyTextWithSpaceName=スペース "{0}" を削除してもよろしいですか? この操作は元に戻すことができません。
#XTXT: Delete space delete confirmation text for multiple spaces
deleteConfirmationBodyTextMultiple=選択した {0} 個のスペースを削除してもよろしいですか? この操作は元に戻すことができません。次のコンテンツは "{1}" 削除されます:
#XTXT: permanently
permanently=完全に
#XTXT: {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDetail=次のコンテンツは "{0}" 削除され、復元できません:
#XTXT: Hint Delete space confirmation text
deleteConfirmationHint=削除を確定するには、{0} を入力してください。
#XTXT: Delete space confirmation error check spelling
deleteConfirmationErrorCheckSpelling=スペルを確認してから、もう一度実行してください。
#XTXT: All Spaces
allSpaces=すべてのスペース
#XTXT: All data
allData=スペースに含まれるすべてのオブジェクトおよびデータ
#XTXT: All connections
allConnections=スペースで定義されているすべての接続
#XFLD: Space tile selection box tooltip
clickToSelect=クリックして選択
#XTXT: All database users
allDatabaseUsers=スペースに関連付けられている任意の Open SQL スキーマに含まれるすべてのオブジェクトおよびデータ
#XFLD: remove members button tooltip
deleteUsers=メンバーを削除
#XTXT: Space long description text
description=説明 (最大 4000 文字)
#XFLD: Add Members button tooltip
addUsers=メンバーを追加
#XFLD: Add Users button tooltip
addUsersTooltip=ユーザを追加
#XFLD: Edit Users button tooltip
editUsersTooltip=ユーザを編集
#XFLD: Remove Users button tooltip
removeUsersTooltip=ユーザを削除
#XFLD: Searchfield placeholder
filter=検索
#XCOL: Users table-view column health
health=動作状態
#XCOL: Users table-view column access
access=アクセス
#XFLD: No user found nodatatext
noDataText=ユーザが見つかりません
#XTIT: Members dialog title
selectUserDialogTitle=メンバーを追加
#XTIT: User dialog title
addUserDialogTitle=ユーザを追加
#XTIT: Delete connections confirmation title
deleteConnectionConfirmationHeaderText=接続の削除
#XTIT: Delete single connection confirmation title
deleteConnectionConfirmationHeaderTextSingle=接続の削除
#XTXT: Delete connection delete confirmation text
deleteConnectionConfirmationBodyText=選択した接続を削除してもよろしいですか? 接続は完全に削除されます。
#XTIT: Remote source dialog title
selectRemoteConnectionDialogTitle=接続の選択
#XTIT: Share connection dialog title
connectionSharingDialogTitle=接続の共有
#XTIT: Connections shared from other spaces table title
sharedConnectionsFromOtherSpaces=共有接続
#XFLD: Add remote source button tooltip
addRemoteConnections=接続の追加
#XFLD: Delete remote source button tooltip
deleteRemoteConnections=接続の削除
#XFLD: Share remote source button tooltip
shareConnections=接続の共有
#XFLD: Tile-layout tooltip
tileLayout=タイルレイアウト
#XFLD: Table-layout tooltip
tableLayout=テーブルレイアウト
#XMSG: Success message after creating space
createSpaceSuccessMessage=スペースが作成されました
#XMSG: Success message after copying space
copySpaceSuccessMessage=スペースを "{0}" をスペース "{1}" にコピーしています
#XMSG: Success message after deploying space
deploymentSuccessMessage=スペースのデプロイメントが開始されました
#XMSG: Success message after Apache Spark update started
sparkUpdateStartedSuccessMessage=Apache Spark の更新が開始されました
#YMSE: Error while starting Apache Spark update
sparkUpdateStartedErrorMessage=Apache Spark の更新に失敗しました
#XMSG: Success message after updating space
updateSettingsSuccessMessage=スペース詳細が更新されました
#XMSG: Success message after unlocking space
unlockSpaceSuccessMessage=スペースのロックが一時的に解除されました
#XMSG: Success message after deleting a single space
deleteSpaceSuccessMessage=スペースが削除されました
#XMSG: Success message after deleting multiple spaces
deleteSpacesSuccessMessage=スペースが削除されました
#XMSG: Success message after restoring a single space
restoreSpaceSuccessMessage=スペースが復元されました
#XMSG: Success message after restoring multiple spaces
restoreSpacesSuccessMessage=スペースが復元されました
#YMSE: Error while updating settings
updateSettingsFailureMessage=スペース設定を更新できませんでした。
#YMSE: Error while updating data lake option
dataLakeEnabledInOtherSpace=データレイクはすでに別のスペースに割り当てられています。データレイクに同時にアクセスできるのは、1 つのスペースからの場合に限定されます。
#YMSE: Error while updating data lake option
virtualTablesExists=仮想テーブル*への依存関係がまだ存在するため、このスペースからデータレイクへの割り当てを解除できません。このスペースからデータレイクへの割り当てを解除するには、仮想テーブルを削除してください。
#YMSE: Error while unlocking space
unlockSpaceFailureMessage=スペースのロックを解除できませんでした。
#YMSE: Error while creating space
createSpaceError=スペースを作成できませんでした。
#YMSE: Error while creating duplicate space
createDuplicateSpaceError={0} という名前のスペースがすでに存在します。
#YMSE: Error while deleting a single space
deleteSpaceError=スペースを削除できませんでした。
#YMSE: Error details while deleting a single space
deleteSpaceDetailedError=スペース "{0}" は正しく動作していません。このスペースをもう一度削除してみてください。それでもスペースが動作しない場合は、スペースの削除を管理者に依頼するか、チケットをオープンしてください。
#YMSE: Error while deleting a single space in files
deleteSpaceInFilesError=ファイル内のスペースデータを削除できませんでした。
#YMSE: Error while deleting a single space users
deleteSpaceUsersError=ユーザを削除できませんでした。
#YMSE: Error while deleting a single space schema
deleteSpaceSchemasError=スキーマを削除できませんでした。
#YMSE: Error while deleting a single space connections
deleteSpaceConnectionsError=接続を削除できませんでした。
#YMSE: Error while deleting a single space data
deleteSpaceDataError=スペースデータを削除できませんでした。
#YMSE: Error while deleting multiple spaces
deleteSpacesError=スペースを削除できませんでした。
#YMSE: Error while restoring a single space
restoreSpaceError=スペースを復元できませんでした。
#YMSE: Error while restoring multiple spaces
restoreSpacesError=スペースを復元できませんでした。
#YMSE: Error while creating users
createUsersError=ユーザを追加できませんでした。
#YMSE: Error while removing users
removeUsersError=ユーザを削除できませんでした。
#YMSE: Error while removing user
removeUserError=ユーザを削除できませんでした。
#YMSE: Error assigning roles to a user
roleAssignUnAssignError=選択したスコープロールにユーザを追加できませんでした。\n\n 自分自身をスコープロールに追加することはできません。スコープロールに追加するよう管理者に依頼することができます。
#YMSE: Error assigning user to the space
userAssignError=ユーザをスペースに割り当てることができませんでした。\n\n ユーザはスコープロール全体で割り当て可能な最大スペース数 (100) にすでに割り当てられています。
#YMSE: Error assigning users to the space
usersAssignError=ユーザをスペースに割り当てることができませんでした。\n\n ユーザはスコープロール全体で割り当て可能な最大スペース数 (100) にすでに割り当てられています。
#YMSE: Error while fetching users
fetchUserWithScopedRolesError=ユーザを取得できませんでした。後でもう一度実行してください。
#YMSE: Error while fetching scoped roles
fetchScopedRolesError=スコープロールを取得できませんでした。
#YMSE: Error while fetching members
fetchUserError=メンバーをフェッチできませんでした。後でもう一度実行してください。
#YMSE: Error while loading run-time database
loadRuntimeError=実行時データベースから情報をロードできませんでした。
#YMSE: Error while loading spaces
loadSpacesError=スペースの取得中に何らかの問題が発生しました。
#YMSE: Error while loading haas resources
loadStorageError=ストレージデータの取得中に何らかの問題が発生しました。
#YMSE: Error no data could be loaded
loadDataError=データの取得中に何らかの問題が発生しました。
#XFLD: Click to refresh storage data
clickToRefresh=ここをクリックしてもう一度実行してください。
#XTIT: Delete space popup title
deleteSpacePopupTitle=スペースの削除
#XCOL: Spaces table-view column name
name=名前
#XCOL: Spaces table-view deployment status
deploymentStatus=デプロイメントステータス
#XFLD: Disk label in space details
storageLabel=ディスク (GB)
#XFLD: In-Memory label in space details
ramLabel=メモリ (GB)
#XFLD: Memory label on space card
memory=ストレージのメモリ
#XFLD: Storage Assignment label in space details
storrageAssignmentLabel=スペースストレージ
#XFLD: Storage Type label in space details
storageTypeLabel=ストレージタイプ
#XFLD: Enable Space Quota
enableSpaceQuota=スペース割当を有効化
#XFLD: No Space Quota
noSpaceQuota=スペース割当なし
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
diskAndInMemoryText=SAP HANA データベース (ディスクおよびインメモリ)
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
dataLakeFilesText=SAP HANA データレイクファイル
#XFLD: Available scoped roles label
availableRoles=利用可能なスコープロール
#XFLD: Selected scoped roles label
selectedRoles=選択したスコープロール
#XCOL: Spaces table-view column models
models=モデル
#XCOL: Spaces table-view column users
users=ユーザ
#XCOL: Spaces table-view column connections
connections=接続
#XFLD: Section header overview in space detail
overview=概要
#XFLD: Section header Apache Spark in space detail
sparkManagementSection=Apache Spark
#XFLD: Section header Apache Spark in space detail
sparkManagementSubSection=アプリケーション
#XFLD: Section header Apache Spark in space detail
taskAssignmentSubSection=タスク割り当て
#XFLD: vCPU label in Apache Spark section
vCPULabel=vCPU
#XFLD: Memory label in Apache Spark section
memoryLabel=メモリ (GB)
#XFLD: space configuration label in Apache Spark section
spaceConfigurationLabel=スペース設定
#XFLD: Space Source label
sparkApplicationLabel=アプリケーション
#XFLD: Cluster Size label
clusterSizeLabel=クラスタサイズ
#XFLD: Driver label
driverLabel=ドライバ
#XFLD: Executor label
executorLabel=実行者
#XFLD: max label
maxLabel=最大使用
#XFLD: TrF Default label
trFDefaultLabel=変換フローのデフォルト
#XFLD: Merge Default label
mergeDefaultLabel=マージのデフォルト
#XFLD: Optimize Default label
optimizeDefaultLabel=デフォルトを最適化
#XFLD: Deployment Default label
deploymentDefaultLabel=ローカルテーブル (ファイル) のデプロイメント
#XFLD: max spark source label
driverExecutorMaxUsedSparkSource={0} CPU / {1} GB
#XFLD: Object type label
taskObjectTypeLabel=オブジェクトタイプ
#XFLD: Task activity label
taskActivityLabel=アクティビティ
#XFLD: Task Application ID label
taskApplicationIDLabel=デフォルトアプリケーション
#XFLD: Section header in space detail
generalSettings=一般設定
#XMSG: Message strip message when a space is locked
spaceLockedMessageStrip=現在、このスペースはロックされています。
#XMSG: Message strip message deploy warning
deployWarningMessageStrip=このセクションの変更は即座にデプロイされます。
#XMSG: Message strip message modify space configuration warning
modifySpaceConfigurationMessageStrip=これらの値を変更すると、パフォーマンス上の問題が発生する可能性があることに注意してください。
#XFLD: Button text to unlock the space again
unlockSpace=スペースのロックを解除
#XFLD: Info text for audit log formatted message
auditLogText=監査ログを有効化して、読み込みアクションまたは変更アクション (監査ポリシー) を記録します。それにより、管理者は誰がどのアクションをどの時点で実行したかを分析できます。
#XMSG: Message strip  audit warning
auditLogWarningMessageStrip=監査ログではテナントで大量のディスクストレージが消費される可能性があります。監査ポリシー (読み込みアクションまたは変更アクション) を有効化する場合は、サービスの中断が発生する可能性のある、ディスクの完全な機能停止を避けるために、(システムモニタの "使用されているディスクストレージ" カードを介して) ディスクストレージの使用状況を定期的に監視する必要があります。監査ポリシーを無効化すると、すべての監査ログエントリが削除されます。監査ログエントリを保持する場合は、監査ポリシーを無効化する前に監査ログエントリをエクスポートすることを検討してください。
#XFLD: Message strip link text to open in app help to guide user what to do to unlock his space permanently
unlockSpaceHelp=ヘルプ表示
#XMSG: Message strip message when a space will be locked again
spaceTemporarilyUnlockedMessageStrip=このスペースはスペースストレージを超えており、{0} {1} 以内にロックされます。
#XMSG: Unit for remaining time until space is locked again
hours=時
#XMSG: Unit for remaining time until space is locked again
minutes=分
#XFLD: Section header in space detail for auditing
detailsSectionAuditing=監査
#XFLD: Subsection header in space detail for auditing
auditing=スペース監査設定
#XFLD: Hot space tooltip
hotSpaceCountTooltip=クリティカルスペース: ストレージ使用量が 90% を超えている
#XFLD: Green space tooltip
greenSpaceCountTooltip=ヘルシースペース: ストレージ使用量が 6% から 90% の範囲内
#XFLD: Cold space tooltip
coldSpaceCountTooltip=コールドスペース: ストレージ使用量が 5% 以下
#XFLD: Critical space tooltip
criticalSpaceCountTooltip=クリティカルスペース: ストレージ使用量が 90% を超えている
#XFLD: Green space tooltip
okSpaceCountTooltip=ヘルシースペース: ストレージ使用量が 6% から 90% の範囲内
#XFLD: Locked space tooltip
lockedSpaceCountTooltipNew=ロック済みのスペース: メモリ不足が原因でブロックされている
#XFLD: Locked space tooltip
lockedSpaceCountTooltip=ロック済みのスペース
#YMSE: Error while deleting remote source
deleteRemoteError=接続を削除できませんでした。
#XFLD: Space ID input tooltip
spaceIDInfoTooltip=スペース ID は後で変更できません。\n有効な文字: A-Z、0-9、および _
#YMSE: Error when space name is empty
spaceNameEmptyValidationError=スペース名を入力してください。
#YMSE: Error when ECN Business name is empty
ecnNameEmptyValidationError=ビジネス名を入力してください。
#YMSE: Error when space id is empty
spaceIDInputEmptyValidationError=スペース ID を入力してください。
#YMSE: Error when space id contains invalid characters
spaceIDInputInValidError=文字が無効です。A-Z、0-9、および _ のみを使用してください。
#YMSE: Error when space id exists while creation
spaceIDInputDuplicateError=スペース ID がすでに存在します。
#XFLD: Space searchfield placeholder
search=検索
#XMSG: Success message after creating users
createUsersSuccess=ユーザが追加されました
#XMSG: Success message after creating user
createUserSuccess=ユーザが追加されした
#XMSG: Success message after updating users
updateUsersSuccess={0} ユーザが更新されました
#XMSG: Success message after updating user
updateUserSuccess=ユーザが更新されました
#XMSG: Success message after removing users
removeUsersSuccess={0} ユーザが削除されました
#XMSG: Success message after removing user
removeUserSuccess=ユーザが削除されました
#XFLD: Schema name
schemaName=スキーマ名
#XFLD: used of total
ofTemplate={0} / {1}
#XFLD: Assigned disk label in space overview
assignedStorageTemplate=割り当て済みディスク ({0}/{1})
#XFLD: Assigned Memory label in space overview
assignedRAMTemplate=割り当て済みメモリ ({0}/{1})
#XFLD: Storage ratio on space
accelearationRAM=メモリ加速度
#XFLD: No Storage Consumption
noStorageConsumptionText=ストレージが割り当てられていません。
#XFLD: Used disk label in space overview
usedStorageTemplate=ストレージに使用されているディスク ({0}/{1})
#XFLD: Used Memory label in space overview
usedRAMTemplate=ストレージに使用されているメモリ ({0}/{1})
#XFLD: Used Disk tooltip
usedStorageTooltipTemplate=ストレージに {0}/{1} ディスクを使用済み
#XFLD: Used Memory tooltip
usedRAMTooltipTemplate={0}/{1} メモリを使用済み
#XFLD: Assigned Disk tooltip
assignedStorageTooltipTemplate={0}/{1} ディスクを割り当て済み
#XFLD: Assigned Memory tooltip
assignedRAMTooltipTemplate={0}/{1} メモリを割り当て済み
#XFLD: Space related used storage tooltip
spaceRelatedUsedStorageTooltipTemplate=スペースデータ: {0}
#XFLD: Not space related used storage tooltip
otherDataUsedStorageTooltipTemplate=他のデータ: {0}
#XFLD: Storage used exceeded popover and tooltip
storageUsedExceededText=計画の拡張を検討するか、SAP サポートに連絡してください。
#XCOL: Space table-view column used Disk
usedStorage=ストレージに使用されているディスク
#XCOL: Space monitor column used Memory
usedRAM=ストレージに使用されているメモリ
#XCOL: Space monitor column Schema
tableSchema=スキーマ
#XCOL: Space monitor column Storage Type
tableStorageType=ストレージタイプ
#XCOL: Space monitor column Table Type
tableType=テーブルタイプ
#XCOL: Space monitor column Record Count
tableRecordCount=レコード数
#XFLD: Assigned Disk
assignedStorage=ストレージに割り当てられているディスク
#XFLD: Assigned Memory
assignedRAM=ストレージに割り当てられているメモリ
#XCOL: Space table-view column storage utilization
tableStorageUtilization=使用済みのストレージ
#XFLD: space status
spaceStatus=スペースステータス
#XFLD: space type
spaceType=スペースタイプ
#XFLD: space type label for space with space type "SAP BW bridge" ("SAP BW bridge" is a product name, do not translate it)
abapbridgeSpaceType=SAP BW ブリッジ
#XFLD: space type label for space with space type "Data Marketplace"
marketplaceSpaceType=データプロバイダプロダクト
#XMSG: Error message for when selected space cannot be deleted due to the space type
deleteTypedSpaceError=スペースタイプが {1} であるため、スペース {0} を削除できません。
#XMSG: Error message for when selected spaces cannot be deleted due to the space type
deleteTypedSpacesError=選択された {0} 個のスペースを削除できません。次のスペースタイプのスペースは削除できません: {1}。
#XFLD: space type label for space without space type
defaultSpaceType=SAP Datasphere
#XFLD: Tooltip for monitor space button
monitorSpace=監視
#XFLD: Tooltip for edit space button
editSpace=スペースの編集
#XMSG: Deletion warning in messagebox
deleteConfirmation=このスペースを削除してもよろしいですか?
#XFLD: Tooltip for delete space button
deleteSpace=スペースの削除
#XFLD: storage
storage=ストレージのディスク
#XFLD: username
userName=ユーザ名
#XFLD: port
port=ポート
#XFLD: hostname
hostName=ホスト名
#XFLD: password
password=パスワード
#XBUT: Request new password button
requestPassword=新しいパスワードを申請
#YEXP: Usage explanation in time data section
timeDataSectionHint=モデルとストーリーで使用する時間テーブルおよびディメンションを作成します。
#YEXP: Usage explanation in Space schema section
spaceSchemaHint=スペース内のデータを他のツールやアプリで使用できるようにするには、スペース内のデータにアクセス可能な 1 つまたは複数のユーザを作成し、今後のスペースデータをデフォルトですべて使用可能にするかどうかを選択します。
#XTIT: Create schema popup title
createSchemaDialogTitle=Open SQL スキーマの作成
#XTIT: Generate Time Data dialog title
generateTimeDataDialogTitle=時間テーブルおよびディメンションの作成
#XTIT: Edit Time Data dialog title
editTimeDataDialogTitle=時間テーブルおよびディメンションの編集
#XTIT: Time Data token title
timeDataTokenTitle=時間データ
#XTIT: Time Data token title
timeDataUpdateViews=時間データビューを更新
#XFLD: Time Data token creation in progress label
timeDataCreationInProgress=作成が進行中です...
#XFLD: Time Data token creation error label
timeDataCreationError=作成に失敗しました。もう一度実行してください。
#XTIT: Time Data dialog time settings title
timeDataDialogTimeSettingsTitle=時間テーブルの設定
#XTIT: Time Data dialog time data table title
timeDataDialogTranslationTablesTitle=換算テーブル
#XTIT: Time Data dialog dimensions title
timeDataDialogTimeViewsTitle=時間ディメンション
#XFLD: Time Data dialog time range label
timeRangeHint=時間範囲を定義します。
#XFLD: Time Data dialog time data table label
timeDataHint=テーブル名を入力します。
#XFLD: Time Data dialog time data dimensions label
timeDimensionsHint=ディメンション名を入力します。
#XFLD: Time Data Time range description label
timerangeLabel=時間範囲
#XFLD: Time Data dialog from year label
fromYearLabel=開始年
#XFLD: Time Data dialog to year label
toYearLabel=終了年
#XFLD: Time Data dialog calendar type label
calendarTypeLabel=カレンダタイプ
#XFLD: Time Data dialog granularity label
granularityLabel=粒度
#XFLD: Time Data dialog technical name label
technicalNameLabel=技術名
#XFLD: Time Data dialog quarters text table lable
quarterTextsTableLabel=四半期の変換テーブル
#XFLD: Time Data dialog months text table lable
monthsTextsTableLabel=月の変換テーブル
#XFLD: Time Data dialog days text table lable
daysTextsTableLabel=日の変換テーブル
#XFLD: Time Data dialog year label
yearLabel=年ディメンション
#XFLD: Time Data dialog quarter label
quarterLabel=四半期ディメンション
#XFLD: Time Data dialog month label
monthLabel=月ディメンション
#XFLD: Time Data dialog day label
dayLabel=日ディメンション
#XFLD: Time Data dialog gregorian calendar type label
gregorian=グレゴリオ暦
#XFLD: Time Data dialog time granularity day label
day=日
#XFLD: Time Data dialog business name maxlength reached
timeRangeValueStateTextMaxLengthBusinessName=最大長である 1000 文字に達しました。
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextMaxRangeExceeded=最大時間範囲は 150 年です。
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeLower="開始年" は "終了年" より前である必要があります
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeHigher="開始年" は 1900 以上にする必要があります。
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeHigher="終了年" は "開始年" より後である必要があります
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeLower=終了年" は "今年 + 100" より前にする必要があります
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningFromYear="開始年" を後にすると、データ損失が発生する場合があります
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningToYear="終了年" を前にすると、データ損失が発生する場合があります
#XMSG: Time Data creation validation error message
timeDataValidationError=一部のフィールドが無効のようです。続行するには必須フィールドを確認してください。
#XMSG: Time Data deletion popup text
deleteTimeDataConfirmation=データを削除してもよろしいですか?
#XMSG: Time Data creation success message
createTimeDataSuccess=時間データが作成されました
#XMSG: Time Data update success message
updateTimeDataSuccess=時間データが更新されました
#XMSG: Time Data delete success message
deleteTimeDataSuccess=時間データが削除されました
#XMSG: Time Data creation error message
createTimeDataError=時間データの作成中に何らかの問題が発生しました。
#XMSG: Time Data update error message
updateTimeDataError=時間データの更新中に何らかの問題が発生しました。
#XMSG: Time Data creation error message
deleteTimeDataError=時間データの削除中に何らかの問題が発生しました。
#XMSG: Error message when time data could not be retrieved
retrievingTimeDataError=時間データをロードできませんでした。
#XTIT: Time Data deletion popup title
deleteTimeDataConfirmationTitle=警告
#XTXT: Time Data deletion dependency popup text
deleteTimeDataDependencyText=他のモデルで使用されているため、時間データを削除できませんでした。
#XTXT: Time Data deletion dependency popup text singular
deleteTimeDataDependencyTextSingular=別のモデルで使用されているため、時間データを削除できませんでした。
#XMSG: Validation error when required time data form field is empty
timeDataRequiredField=これは必須フィールドであり、空白にすることはできません。
#XFLD: Tooltip for Open in Database Explorer button
openInDBX=データベースエクスプローラで開く
#YMSE: Dimension Year
dimensionYearView=ディメンション "年"
#YMSE: Dimension Year
dimensionQuarterView=ディメンション "四半期"
#YMSE: Dimension Year
dimensionMonthView=ディメンション "月"
#YMSE: Dimension Year
dimensionDayView=ディメンション "日
#XFLD: Time Data deletion object title
timeDataUsedIn=({0} 個のモデルで使用中)
#XFLD: Time Data deletion object title singular
timeDataUsedInSingular=(1 個のモデルで使用中)
#XFLD: Time Data deletion table column provider
provider=プロバイダ
#XTIT: Time Data dependency dialog title
timeDataDependencyDialogTitle=依存関係
#XTIT: Create schema popup title
createSpaceSchemaUserDialogTitle=スペーススキーマユーザの作成
#XFLD: Create schema button
createSchemaButton=Open SQL スキーマの作成
#XFLD: Generate TimeData button
generateTimeDataButton=時間テーブルおよびディメンションの作成
#XFLD: Show dependencies button
showDependenciesButton=依存関係の表示
#XFLD: tooltip for disabled buttons when user is not member of space
mustBeMemberOfSpaceForOperation=この操作を実行するには、ユーザがスペースのメンバーである必要があります。
#XFLD: Create space schema user button
createSpaceSchemaUserButton=スペーススキーマユーザの作成
#YMSE: API Schema users load error
loadSchemaUsersError=ユーザの一覧をロードできませんでした。
#XTIT: API Schema-config popup title
schemaUserDetailsDialogTitle=スペーススキーマユーザの詳細
#XMSG: API Schema user deletion popup text
deleteSpaceSchemaUserConfirmation=選択したユーザを削除してもよろしいですか?
#XMSG: API Schema user deletion confirmation text
userDeleteSuccess=ユーザが削除されました。
#YMSE: API Schema user deletion error
userDeleteError=ユーザを削除できませんでした。
#XFLD: User deleted
userDeleted=ユーザが削除されました。
#XTIT: Remove user popup title
removeUserConfirmationTitle=警告
#XMSG: Remove user popup text
removeUserConfirmation=ユーザを削除してもよろしいですか? ユーザと、そのユーザに割り当てられたスコープロールがスペースから削除されます。
#XMSG: Remove users popup text
removeUsersConfirmation=ユーザを削除してもよろしいですか? ユーザと、それらのユーザに割り当てられたスコープロールがスペースから削除されます。
#XBUT: Confirmation button for remove user dialog
removeUserConfirmationBtn=削除
#YMSE: No data text for available roles
noDataAvailableRoles=スペースがスコープロールに追加されていません。\n ユーザをスペースに追加するには、最初に 1 つまたは複数のスコープロールに追加する必要があります。
#YMSE: No data text for selected roles
noDataSelectedRoles=選択したスコープロールがありません
#XTIT: Schema-config popup title
schemaDetailsDialogTitle=Open SQL スキーマ設定の詳細
#XFLD: Label for Read Audit Log
auditLogRead=読み込み操作の監査ログを有効化
#XFLD: Label for Change Audit Log
auditLogChange=変更操作の監査ログを有効化
#XFLD: Label Audit Log Retention
auditLogRetention=ログの保持期間:
#XFLD: Label Audit Log Retention Unit
retentionUnit=日
#YMSE: Error when log retention time is set incorrectly
incorrectRetentionTime={0} から {1} の範囲の整数を入力してください
#XFLD: Label for Cross Space Schema Access
spaceSchemaAccess=スペーススキーマデータの使用
#XTIT: Confirm deactivation of space schema consumption dialog title
disableSpaceSchemaConsumptionDialogTitle=スペーススキーマデータの使用の停止
#XTXT: Confirm deactivation of space schema consumption dialog text
disableSpaceSchemaConsumptionDialogText=この Open SQL スキーマによってスペーススキーマのデータが使用される可能性があります。使用を停止すると、スペーススキーマデータに基づくモデルが機能しなくなる可能性があります。
#XBUT: Confirmation button for the stop consuming space schema data dialog
stopConsuming=使用の停止
#XTXT: Tooltip for data lake icon
dataLakeIconTooltip=このスペースはデータレイクにアクセスするために使用されます
#XFLD: Tooltip for data lake table entry
dataLakeTableEntryTooltip=データレイクが有効
#XFLD: used In-Memory linit reached tag
usedRamLimitReachedLabel=メモリ制限に到達
#XFLD: Space storage Limit reached tag
storageLimitReachedLabel=ストレージ容量の上限に到達
#XFLD: Minimal Storage Limit Reached tag
minStorageLimitReachedLabel=ストレージ容量の下限に到達
#XFLD: Space ram tag
ramLimitReachedLabel=メモリ制限に到達
#XFLD: Minimal In-Memory Limit Reached tag
minRamLimitReachedLabel=メモリの下限に到達
#XFLD: Popover text after click on space storage limit warning
storageLimitReachedText=スペースに割り当てらたストレージ容量の制限 ({0}) に到達しました。追加のストレージ容量をスペースに割り当ててください。
#XFLD: System storage tag
systemStorageLimitReachedLabel=システムストレージ容量の上限に到達
#XMSG: Popover text after click on storage limit warning
systemStorageLimitReachedText=システムストレージ容量の上限 ({0}) に到達しました。現在はスペースに追加のストレージ容量を割り当てることができません。
#XMSG: Schema deletion warning
deleteSchemaConfirmation=この Open SQL スキーマを削除すると、スキーマ内のすべての保存済みオブジェクトと更新済みアソシエーションも完全に削除されます。続行しますか?
#XMSG: Success message after deleting a schema
schemaDeleteSuccess=スキーマが削除されました
#YMSE: Error while deleting schema.
schemaDeleteError=スキーマを削除できませんでした。
#XMSG: Success message after update a schema
schemaUpdateSuccess=スキーマが更新されました
#YMSE: Error while updating schema.
schemaUpdateError=スキーマを更新できませんでした。
#XMSG: Hint when no password is shown in the dialog.
newPasswordHint=このスキーマにはパスワードが設定されています。パスワードを忘れたり紛失したりした場合は、新しいパスワードを申請できます。新しいパスワードは忘れずにコピーまたは保存してください。
#XMSG: Hint when showing the password in the dialog.
savePasswordHint=パスワードをコピーしてください。パスワードはこのスキーマへの接続を確立するときに必要になります。パスワードを忘れた場合は、このダイアログを開いてパスワードをリセットすることができます。
#XFLD,50: Hint on entering schema name in creation dialog
schemaNameHint=この名前はスキーマを作成した後は変更できません。
#XFLD: Open SQL Schemas section sub headline
schemasSQL=Open SQL
#XFLD: Space schema section sub headline
schemasSpace=スペース
#XFLD: HDI Container section header
HDIContainers=HDI コンテナ
#XTXT: Add HDI Containers button tooltip
addHDIContainers=HDI コンテナの追加
#XTXT: Remove HDI Containers button tooltip
removeHDIContainers=HDI コンテナの削除
#XFLD: Enable HDI Containers feature button
enableHDIContainersButton=アクセスを許可
#YMSE: No data text for HDI Containers table
noDataHDIContainers=追加された HDI コンテナはありません。
#YMSE: No data text for Timedata section
noDataTimedata=時間テーブルおよびディメンションが作成されていません。
#YMSE: No data text during runtime not available for Timedata section
runtimeNotAvailableNoDataTimedata=実行時データベースを利用できないため、時間テーブルおよびディメンションをロードできません。
#YMSE: No data text during runtime not available for HDI section
runtimeNotAvailableNoDataHDI=実行時データベースを利用できないため、HDI コンテナをロードできません。
#YMSE: Error while fetching HDI Containers
fetchHDIContainersError=HDI コンテナを取得できませんでした。後でもう一度実行してください。
#XFLD Table column header for HDI Container names
HDIContainerName=HDI コンテナ名
#XFLD: Enable HDI Containers feature dialog title
enableHDIContainersDialogTitle=アクセスの許可
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText1New=データを移動せずに SAP Datasphere スペースと HDI コンテナ間でデータを交換するには、SAP Datasphere テナント上で SAP SQL Data Warehousing を有効にする必要があります。
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText2=そうするには、下記のボタンをクリックすることで、サポートチケットを開いてください。
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText3New=チケットの処理が完了したら、SAP Datasphere 実行時データベース内で 1 つ以上の新しい HDI コンテナを構築する必要があります。そうすると、すべての SAP Datasphere スペースの HDI コンテナセクションに、"アクセスを許可" ボタンに代わって + ボタンが表示され、HDI コンテナをスペースに追加できるようになります。
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText41=さらに情報が必要ですか? %%0 に移動してください。チケットに含まれる内容の詳細については、%%1 を参照してください。
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkText=SAP ヘルプ
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkTextSapNote3057059=SAP ノート 3057059
#XBUT: Open Ticket Button Text
openTicket=チケットを開く
#XBUT: Add Button Text
add=追加
#XBUT: Next Button Text
next=次
#XBUT: Edit Button Text
editUsers=編集
#XBUT: create user Button Text
createUser=作成
#XBUT: Update user Button Text
updateUser=選択
#XTIT: Add HDI Containers Dialog Title
addHDIContainersDialogTitle=未割り当ての HDI コンテナの追加
#XFLD: No data text for add HDI container dialog
addHDIContainersDialogNoDataText=未割り当てのコンテナが見つかりませんでした。\n検索しているコンテナはすでにスペースに割り当てられている可能性があります。
#YMSE: Error while loading assigned HDI Containers
loadAssignedHDIContainersError=割り当て済みの HDI コンテナをロードできませんでした。
#YMSE: Error while loading assigned HDI Containers
loadUnassignedHDIContainersError=HDI コンテナをロードできませんでした。
#XMSG: Success message
succeededToAddHDIContainer=HDI コンテナが追加されました
#XMSG: Success message
succeededToAddHDIContainerPlural=HDI コンテナが追加されました
#XMSG: Success message
succeededToDeleteHDIContainer=HDI コンテナが削除されました
#XMSG: Success message
succeededToDeleteHDIContainerPlural=HDI コンテナが削除されました
#XFLD: Time data section sub headline
timeDataSection=時間テーブルおよびディメンション
#XFLD: Schemas list entry read audit log enabled indicator
readIndicator=読み込み
#XFLD: Schemas list entry change audit log enabled indicator
changeIndicator=変更
#XFLD: Remote sources section sub headline
allconnections=接続割り当て
#XFLD: Remote sources section sub headline
localconnections=ローカル接続
#XFLD: Remote sources section sub headline for open Connectors
openConnectors=SAP Open Connector
#XFLD: User section sub headline
memberassignment=メンバー割り当て
#XFLD: User assignment section sub headline
userAssignment=ユーザ割当
#XFLD: User section Access dropdown Member
member=メンバー
#XFLD: User assignment section column name
user=ユーザ名
#XTXT: Selected role count
selectedRoleToolbarText=選択済み: {0}
#XTIT: Space detail section connections title
detailsSectionConnections=接続
#XTIT: Space detail section data access title
detailsSectionDataAccess=スキーマアクセス
#XTIT: Space detail section time data title
detailsSectionGenerateData=時間データ
#XTIT: Space detail section members title
detailsSectionUsers=メンバー
#XTIT: Space detail section Users title
detailsSectionUsersTitle=ユーザ
#XTIT: Out of Storage
insufficientStoragePopoverTitle=ストレージ容量不足
#XTIT: Storage distribution
storageDistributionPopoverTitle=使用されているディスクストレージ
#XTXT: Out of Storage popover text
insufficientStorageText=新しいスペースを作成するには、不要になったスペースを削除するか、別のスペースに割り当てられているストレージ容量を削減してください。"プランの管理" を呼び出すことで、システムストレージ全体の容量を増やすこともできます。
#XMSG: Space id length warning
spaceIdLengthWarning=最大 {0} 文字を超えました。
#XMSG: Space name length warning
spaceNameLengthWarning=最大 {0} 文字を超えました。
#XMSG: Space id reserved prefix warning
spaceIdnWarningReservedPrefix=競合の可能性を避けるために、{0} 接頭辞を使用しないでください。
#YMSE: Error while loading open SQL schemas
loadLocalSchemasError=Open SQL スキーマをロードできませんでした。
#YMSE: Error while creating open SQL schema
createLocalSchemaError=Open SQL スキーマを作成できませんでした。
#YMSE: Error while loading all remote connections
loadRemoteConnectionsError=すべてのリモート接続をロードできませんでした。
#YMSE: Error while loading space details
loadSpaceDetailsError=スペースの詳細をロードできませんでした。
#YMSE: Error while deploying space details
deploySpaceDetailsError=スペースをデプロイできませんでした。
#YMSE: Error while copying space details
copySpaceDetailsError=スペースをコピーできませんでした。
#YMSE: Error while loading storage data
loadStorageDataError=ストレージデータをロードできませんでした。
#YMSE: Error while loading all users
loadAllUsersError=すべてのユーザをロードできませんでした。
#YMSE: Failed to reset password
resetPasswordError=パスワードをリセットできませんでした。
#XMSG: Success message after updating space
resetPasswordSuccessMessage=スキーマに対して新しいパスワードが設定されました
#YMSE: DP Agent-name too long
DBAgentNameError=DP エージェントの名前が長すぎます。
#YMSE: Schema-name not valid.
schemaNameError=スキーマの名前が無効です。
#YMSE: User name not valid.
UserNameError=ユーザ名が無効です。
#XTIT: Consumption by Storage Type
consumptionStorageTypeText=使用量 (ストレージタイプ別)
#XTIT: Consumption by Schema
consumptionSchemaText=使用量 (スキーマ別)
#XTIT: Overall Consumption by schema
overallConsumptionSchemaText=全体のテーブル使用量 (スキーマ別)
#XTIT: Overall Consumption by Table Type
overallConsumptionTableTypeText=全体の使用量 (テーブルタイプ別)
#XTIT: Tables
tableDetailsText=テーブルの詳細
#XTIT: Table Storage Consumption
tableStorageConsumptionText=テーブルストレージ使用量
#XFLD: Table Type label
tableTypeLabel=テーブルタイプ
#XFLD: Schema label
schemaLabel=スキーマ
#XFLD: reset table tooltip
resetTable=テーブルのリセット
#XFLD: In-Memory label in space monitor
inMemoryLabel=メモリ
#XFLD: Disk label in space monitor
diskLabel=ディスク
#XFLD: Yes
yesLabel=はい
#XFLD: No
noLabel=いいえ
#XFLD: switch label: Want the data in this space to be consumable by default?
allowConsumptionByDefault=このスペース内のデータをデフォルトで使用可能にしますか?
#XFLD: Business Name
businessNameLabel=ビジネス名
#XFLD: Refresh
refresh=リフレッシュ
#XMSG: No filter results title
noFilterResultsTitle=フィルタ設定により、データが表示されていないようです。
#XMSG: No filter results message
noFilterResultsMsg=フィルタ設定を調整してみてください。それでもデータが表示されない場合は、いくつかのテーブルをデータビルダで作成してください。テーブルによってストレージが消費されると、ここでテーブルを監視できるようになります。
#XMSG: Space monitoring Hana state red title
spaceMonitorHanaRedTitle=実行時データベースを利用できません。
#XMSG: Space monitoring Hana state red message
spaceMonitorHanaRedMsg=実行時データベースを利用できないため、特定の機能が無効化されており、このページに関する情報を表示できません。
#YMSE: Error while creating space schema user
createSpaceSchemaUserError=スペーススキーマユーザを作成できませんでした。
#YMSE: Error User name already exists
userAlreadyExistsError=ユーザ名がすでに存在します。
#YMSE: Error Authentication failed
authenticationFailedError=認証が失敗しました。
#XMSG: Message strip message when a schema user is locked
userLockedMessageStrip=ログイン失敗回数が多すぎるため、ユーザがロックされました。ユーザのロックを解除するには、新しいパスワードを申請してください。
#XMSG: Success message after reset and unlock schema user
resetUnlockPasswordSuccessMessage=新しいパスワードが設定され、ユーザのロックが解除されました
#XMSG: user is locked message
userLockedMessage=ユーザがロックされました。
#XCOL: Users table-view column Role
spaceRole=ロール
#XCOL: Users table-view column Scoped Role
spaceScopedRole=スコープロール
#XCOL: Users table-view column Space Admin
spaceAdmin=スペース管理者
#XFLD: User section dropdown value Viewer
viewer=ビューア
#XFLD: User section dropdown value Modeler
modeler=モデラ
#XFLD: User section dropdown value Data Integrator
dataIntegrator=データインテグレータ
#XFLD: User section dropdown value Space Administrator
spaceAdministrator=スペース管理者
#XMSG: Success message -  Space Role updated successfully.
updateUserSpaceRoleSuccess=スペースロールが更新されました
#XMSG: Error message - Failed to update Space Role.
updateUserSpaceRoleError=スペースロールが正常に更新されませんでした。
#XFLD:
databaseUserNameSuffix=データベースユーザ名の接尾辞
#XTXT: Space Schema password text
spaceSchemaPasswordText=このスキーマへの接続を設定するには、パスワードをコピーしてください。パスワードを忘れた場合は、新しいパスワードをいつでも申請できます。
#XFLD: Cloud Platform label in space schema dialog
cloudPlatformLabel=Cloud Platform
#XTXT: Cloud Platform text in space schema dialog
cloudPlatformText=このユーザ経由のアクセスを設定するには、利用を有効化して、認証情報をコピーします、パスワードなしで認証情報をコピーできる場合は、後で必ずパスワードを追加してください。
#XFLD: enable consumption in CloudPlatform label in space schema dialog
enableConsumptionInCloudPlatformLabel=Cloud Platform で利用を有効化
#XFLD: Credentials for User-Provided Service label in space schema dialog
cloudPlatformCredentialsLabel=ユーザ提供サービス用の認証情報:
#XFLD: Credentials for User-Provided Service (Without Password) label in space schema dialog
cloudPlatformCredentialsWithoutPasswordLabel=ユーザ提供サービス用の認証情報 (パスワードなし):
#XFLD: Copy Credentials label in space schema dialog
copyCredentialsLabel=パスワードなしで認証情報をコピー
#XFLD: Copy Credentials label in space schema dialog
copyFullCredentialsLabel=完全な認証情報のコピー
#XFLD: Copy Password label in space schema dialog
copyPasswordLabel=パスワードのコピー
#XMSG: Credentials copied to clipboard
credentialsCopiedMessage=クリップボードにコピーされた認証情報
#XMSG: Password copied to clipboard
passwordCopiedMessage=クリップボードにコピーされたパスワード
#XMSG: create database user dialog title
createDatabaseUserDialogTitle=データベースユーザの作成
#XMSG: Database Users section title
databaseUsers=データベースユーザ
#XMSG: Database User Details dialog title
databaseUserDetailsDialogTitle=データベースユーザ詳細
#XFLD: database user read audit log
databaseUserAuditLogRead=読み込み操作の監査ログを有効化 - ログの保持期間:
#XFLD: database user change audit log
databaseUserAuditLogChange=変更操作の監査ログを有効化 - ログの保持期間:
#XMSG: Cloud Platform Access
cloudPlatformAccess=Cloud Platform アクセス
#XFLD: Enable Consumption in Cloud Platform hint
enableConsumptionCloudPlatformHint=このデータベースユーザ経由で HANA Deployment Infrastructure (HDI) コンテナへのアクセスを設定します。HDI コンテナに接続するには、SQL モデリングを有効化する必要があります。
#XFLD: Enable HDI Consumption
enableConsumptionCloudPlatform=HDI 利用を有効化
#XFLD: Enable Consumption hint
enableConsumptionHint=スペース内のデータを他のツールまたはアプリで利用可能にしますか?
#XFLD: Enable Consumption
enableConsumption=SQL 利用を有効化
#XFLD: Enable Modeling
enableModeling=SQL モデリングを有効化
#XMSG: Privileges for Data Modeling
privilegesModeling=データインジェスト
#XMSG: Privileges for Data Consumption
privilegesConsumption=外部ツールでのデータ利用
#XFLD: SQL Modeling
sqlModeling=SQL モデリング
#XFLD: SQL Consumption
sqlConsumption=SQL 利用
#XFLD: enabled
enabled=有効化
#XFLD: disabled
disabled=無効化
#XFLD: Edit Privileges
editPrivileges=権限の編集
#XFLD: Open Database Explorer
openDBX=データベースエクスプローラを開く
#XFLD: create database user hint
databaseCreateHint=保存後はユーザ名を再度変更できなくなることに注意してください。
#XFLD: Internal Schema Name
internalSchemaName=内部スキーマ名
#YMSE: Failed to load database users
loadDatabaseUserError=データベースユーザのロードに失敗しました
#YMSE: Failed to delete database user
deleteDatabaseUsersError=データベースユーザの削除に失敗しました
#XMSG: Success message after deleting database user
deleteDatabaseUserSuccess=データベースユーザが削除されました
#XMSG: Success message after deleting database users
deleteDatabaseUsersSuccess=データベースユーザが削除されました
#XMSG: Success message after creating database user
createDatabaseUserSuccessMessage=データベースユーザが作成されました
#YMSE: Failed to create database user
createDatabaseUserError=データベースユーザの作成に失敗しました
#XMSG: Success message after updating database user
updateDatabaseUserSuccessMessage=データベースユーザが更新されました
#YMSE: Failed to update database user
updateDatabaseUserError=データベースユーザの更新に失敗しました
#XFLD: HDI Consumption
hdiConsumption=HDI 利用
#XTIT: Space detail section Database Access title
detailsSectionDatabaseAccess=データベースアクセス
#XTXT: Database users consumption hint
databaseUsersConsumptionHint=スペースデータをデフォルトで利用可能にします。ビルダのモデルでは、データが自動的に利用可能になります。
#XFLD: Default Consumption of Space Data:
defaultConsumptionOfSpaceData=デフォルトでスペースデータを利用:
#XFLD: Database User Name
databaseUserName=データベースユーザ名
#XMSG: Database User creation validation error message
databaseUserValidationError=一部のフィールドが無効なようです。続行するには、必須フィールドを確認してください。
#XMSG: SQL modeling can’t be enabled since this user has been migrated.
migratedDatabaseUserHint=このユーザは移行されているため、データインジェストを有効化できません。
#XBUT: Remove Button Text
remove=削除
#XBUT: Remove Spaces Button Text
removeSpaces=スペースを削除
#XBUT: Remove Objects Button Text
removeObjects=オブジェクトを削除
#XMSG: No members have been added yet.
noMembersAssigned=メンバーがまだ追加されていません。
#XMSG: No users have been added yet.
noUsersAssigned=ユーザがまだ追加されていません。
#XMSG: No database users have been created or your filter is not showing any data.
noDatabaseUsers=データベースユーザが作成されていません。または、フィルタにデータが表示されていません。
#XMSG: Please enter a user name.
noDatabaseUsername=ユーザ名を入力してください。
#XMSG: The user name is too long. Please use a shorter one.
databaseUsernameTooLong=ユーザ名が長すぎます。短い名前を使用してください。
#XMSG: No privileges have been enabled and this database user will have limited functionality. Do you still want to continue?
noPrivilegesConfirmationMessage=権限が有効化されていないため、このデータベースユーザの機能は制限されます。続行してもよろしいですか?
#XMSG: To enable audit logs for change operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogChangeMessage=変更操作の監査ログを有効化するには、データインジェストも有効化する必要があります。これを行いますか?
#XMSG: To enable audit logs for read operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogReadMessage=読み込み操作の監査ログを有効化するには、データインジェストも有効化する必要があります。これを行いますか?
#XMSG: To enable HDI consumption data ingestion and data consumption needs to be enabled. Do you want to enable all?
enableHDIConsumptionMessage=HDI 利用を有効化するには、データインジェストおよびデータ利用も有効化する必要があります。これを行いますか?
#XMSG:
databaseUserPasswordText=このデータベースユーザへの接続を設定するには、パスワードをコピーしてください。パスワードを忘れた場合は、新しいパスワードをいつでも申請できます。
#XTIT: Space detail section members title
detailsSectionMembers=メンバー
#XMSG: New password set
newPasswordSet=新しいパスワードが設定されました
#XFLD: Data Ingestion
dataIngestion=データインジェスト
#XFLD: Data Consumption
dataConsumption=データ利用
#XFLD: Privileges
privileges=権限
#XFLD: Enable Data ingestion
enableDataIngestion=データインジェストを有効化
#XMSG: Log the read and change operations for data ingestion.
databaseUserAuditHint=データインジェストの読み込みおよび変更操作をログに記録します。
#XMSG: Make your space data available in your HDI containers.
databaseUserHDIHint=HDI コンテナでスペースデータを利用可能にします。
#XFLD: Enable Data consumption
enableDataConsumption=データ利用を有効化
#XMSG: Allow other apps or tools to consume your space data.
enableDataConsumptionHint=他のアプリまたはツールでスペースデータを利用できるようにします。
#XMSG: To set up access via this schema user copy the credentials to your user-provided service. In case you can only copy the credentials without a password make sure to add the password later.
databaseUserHDIConsumptionHint=このデータベースユーザ経由のアクセスを設定するには、認証情報をユーザ提供サービスにコピーします、パスワードなしで認証情報をコピーできる場合は、後で必ずパスワードを追加してください。
#XFLD: Data Flow Runtime Capacity label in space overview
dataFlowRuntimeCapacity=データフロー実行時キャパシティ ({2} 時間のうち {0} 時間 {1} 分)
#XFLD: Could not load Data Flow Runtime Capacity
dataFlowRuntimeCapacityError=データフロー実行時キャパシティをロードできませんでした
#XMSG: User can grant data consumption to other users.
databaseUserConsumptionWithGrantOptionHint=ユーザがデータ利用を他のユーザに許可できます。
#XFLD: Enable data consumption with grant option
enableConsumptionWithGrantOption=付与オプション付きのデータ利用を有効化
#XMSG: To enable data consumption with grant option data consumption needs to be enabled. Do you want to enable both?
enableConsumptionWithGrantMessage=付与オプション付きのデータ利用を有効化するには、データ利用を有効化する必要があります。両方とも有効化しますか?
#XFLD: Enable Automated Predictive Library (APL) and Predictive Analysis Library (PAL)
allowUsingScriptServerFunctionality=自動予測ライブラリ (APL) および予測分析ライブラリ (PAL) を有効化
#XMSG: User can use SAP HANA Cloud embedded machine learning functions.
allowUsingScriptServerFunctionalityHint=ユーザは SAP HANA Cloud に組み込まれた機械学習機能を使用できます。
#XFLD: Password Policy
passwordPolicy=パスワードポリシー
#XMSG: Password Policy
passwordPolicyHint=設定されたパスワードポリシーをここで有効化または無効化します。
#XFLD: Enable Password Policy
enablePasswordPolicy=パスワードポリシーを有効化
#XMSG: Read Access to the Space Schema
readAccessTitle=スペーススキーマに対する読み込みアクセス
#XMSG: read access hint
readAccessHint=データベースユーザが外部ツールをスペーススキーマに接続し、利用のために公開されたビューを読み込みできるようにします。
#XFLD: Space Schema
spaceSchema=スペーススキーマ
#XFLD: Enable Read Access (SQL)
enableReadAccess=読み込みアクセスを有効化 (SQL)
#XMSG: Allow the user to grant read access to other users.
withGrantOptionHint=ユーザが読み込みアクセスを他のユーザに付与できるようにします。
#XFLD: With Grant Option
withGrantOption=付与オプション付き
#XMSG: Make your space data available in your HDI containers.
enableHDIConsumptionHint=HDI コンテナでスペースデータを利用可能にします。
#XFLD: Enable HDI Consumption
enableHDIConsumption=HDI 利用を有効化
#XMSG: Write Access to the User’s Open SQL Schema
writeAccessTitle=ユーザの Open SQL スキーマに対する書き込みアクセス
#XMSG: write access hint
writeAccessHint=データベースユーザが外部ツールをユーザの Open SQL スキーマに接続し、データエンティティを作成できるようにするほか、スペースで使用するデータを摂取できるようにします。
#XFLD: Open SQL Schema
openSQLSchema=Open SQL スキーマ
#XFLD: Enable Write Access (SQL DDL & DML)
enableWriteAccess=書き込みアクセスを有効化 (SQL、DDL、および DML)
#XMSG: audit hint
auditHint=Open SQL スキーマでの読み込み操作と変更操作をログに記録します。
#XMSG: data consumption hint
dataConsumptionHint=スペース内のすべての新規ビューをデフォルトで公開して利用可能にします。モデラはビュー出力側パネルで "利用のために公開" スイッチを使用して、この設定を個々のビューごとに上書きすることができます。ビューが公開されるフォーマットを選択することもできます。
#XFLD: Expose for Consumption by Default
exposeDataConsumption=利用のためにデフォルトで公開
#XMSG: database users hint consumption hint
databaseUsersHint2New=外部ツールを SAP Datasphere に接続するためのデータベースユーザを作成します。ユーザがスペースデータを読み込んでデータエンティティを作成できるほか (DDL)、スペースで使用するデータを摂取できるように (DML)、ユーザ権限を設定します。
#XFLD: Read
read=読み込み
#XFLD: Read (HDI)
readHDI=読み込み (HDI)
#XFLD: Write
write=書き込み
#XMSG: HDI Containers Hint
HDIContainersHint2=スペース内の SAP HANA Deployment Infrastructure (HDI) コンテナにアクセスできるようにします。モデラはビューのソースとして HDI アーチファクトを使用でき、HDI クライアントはスペースデータにアクセスできます。
#XMSG: Open info dialog
openDatabaseUserInfoDialog=情報ダイアログを開く
#XMSG: Database user is locked. Open info dialog to unlock
openLockedDatabaseUserInfoDialog=データベースユーザがロックされています。ロックを解除するには、ダイアログを開いてください。
#XFLD: Table
table=テーブル
#XFLD: Space section partner configuration title
partnerConnectionConfigurationSectionTitle=パートナ接続
#XFLD: Space section partner configuration subtitle
partnerConnectionConfigurationSectionSubTitle=パートナ接続の設定
#XFLD: Space section partner configuration hint
partnerConnectionConfigurationSectionHint=iFrame URL とアイコンを追加することで、ユーザ独自のパートナ接続タイルを定義します。この設定はこのテナントのみで使用できます。
#XFLD: Table Name Field
partnerConnectionConfigurationName=タイル名
#XFLD: Table iFrame URL field
partnerConnectionConfigurationiFrameURL=iFrame URL
#XFLD: Table iFrame Post Message Origin field
partnerConnectionConfigurationiFramePostMessageOrigin=iFrame Post メッセージのソース
#XFLD: Table icon/image name
partnerConnectionConfigurationIcon=アイコン
#XMSG: Table no result found
partnerConnectionConfigurationNotfound=パートナ接続の設定が見つかりませんでした。
#XMSG: Partner Connection Confgurations are not available
partnerConnectionConfigurationNotAvailable=実行時データベースを利用できない場合、パートナ接続の設定は表示できません。
#XFLD: Dialog Create Partner Connection Configuration
partnerConnectionConfigurationDialogTitleAdd=パートナ接続の設定を作成
#XFLD: Dialog Create Partner Connection Configuration Upload Icon
partnerConnectionConfigurationButtonUloadPartnerIcon=アップロードアイコン
#XFLD: Dialog Create Partner Connection Configuration Select
partnerConnectionConfigurationSelectIcon=選択 (最大サイズは 200KB)
#XFLD: Dialog Create Partner Connection Configuration Enter a name
partnerConnectionConfigurationNamePlaceholder=パートナタイルの例
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL
partnerConnectionConfigurationiFrameURLPlaceholder=http://www.example.com
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL Check
partnerConnectionConfigurationiFramePostMessageOriginPlaceholder=.example.com
#XFLD: Dialog Create Partner Connection Configuration Browse
partnerConnectionConfigurationBrowseButton=参照
#XFLD: Create Partner Connection Configuration Msg
partnerConnectionConfigurationUploadMsg=パートナ接続の設定が正常に作成されました。
#XMSG: Error while deleting Partner Connection configuration(s)
partnerConnectionConfigurationDeleteError=パートナ接続の設定を削除するときに、エラーが発生しました。
#XMSG: Partner Connection Configuration has been successfully deleted
partnerConnectionConfigurationDeleteSuccess=パートナ接続の設定が正常に削除されました。
#XMSG: Error while retrieving Partner Connection Configurations
partnerConnectionConfigurationLoadError=パートナ接続の設定を取得するときに、エラーが発生しました。
#XMSG: Error fileSizeLimitExceeded
partnerConnectionConfigurationfileSizeLimitExceeded=ファイルのサイズが最大サイズの 200KB を超えているため、ファイルをアップロードできませんでした。
#XBUT: Create Partner Connection Configuration
partnerConnectionConfigurationButtonCreate=パートナ接続の設定を作成
#XBUT: Delete Partner Connection Configuration
partnerConnectionConfigurationButtonDelete=パートナ接続の設定を削除
#XMSG: Partner Tile Creation Failure
upsertPartnerTileFailed=パートナタイルを作成できませんでした。
#XMSG: Partner Tile Deletion Failure
deletePartnerTileFailed=パートナタイルを削除できませんでした。
#XMSG: HANA cloud connector settings reset failure
resetHANASCCSettingsFailed=カスタム SAP HANA Cloud Connector 設定のリセットに失敗しました
#XFLD: Workload Class
workloadClass=ワークロードクラス
#XFLD: Workload Management
workloadManagement=ワークロード管理
#XFLD: Priority
workloadClassPriority=優先度
#XMSG:
workloadManagementPriorityHint=データベースにクエリを発行するときに、このスペースの優先度を指定することができます。1 (最低優先度) から 8 (最高優先度) までの値を入力してください。利用可能なスレッドに関して複数のスペース間で競合状態が発生する場合は、優先度の高いスペースが優先度の低いスペースよりも前に実行されます。
#XMSG:
workloadClassPriorityHint=スペースの優先度として、0 (最低優先度) から 8 (最高優先度) の値を指定できます。優先度が高いスペースの文は、優先度が低い他のスペースの文よりも前に実行されます。デフォルトの優先度は 5 です。優先度 = 9 はシステム運用向けに予約されているため、スペースでは使用できません。
#XFLD: Statement Limits
workloadclassStatementLimits=文の制限
#XFLD: Workload Configuration
workloadConfiguration=ワークロード設定
#XMSG:
workloadClassStatementLimitsHint=スペース内で同時に実行される文によって消費できるスレッドの最大数 (または比率 (%)) およびメモリの制限 (GB 単位) を指定することができます。指定できる値または比率 (%) の最小値は 0 (制限なし)、最大値はテナント内で利用できるスレッドの最大数およびメモリの合計です。\n\nメモリ制限を指定した場合、そのメモリ制限に達した文は実行されません。
#XMSG:
workloadClassStatementLimitsDescription=デフォルト設定では、厳しいリソース制限はかかっていませんが、単一のスペースによってシステムに過負荷がかからないようになっています。
#XMSG:
workloadClassStatementLimitCustomDescription=スペース内で同時に実行される文によって消費できるスレッドの最大数およびメモリの制限を設定することができます。
#XMSG:
totalStatementThreadLimitHelpText=スレッドの数の制限を低すぎる値に設定すると、文のパフォーマンスが影響を受ける可能性があります。一方、スレッドの数が多すぎるか 0 であると、使用可能なすべてのシステムスレッドがスペースによって消費される可能性があります。
#XMSG:
totalStatementMemoryLimitHelpText=メモリの制限を低すぎる値に設定すると、メモリ不足の問題が発生する可能性があります。一方、メモリの制限を高すぎる値または 0 に設定すると、使用可能なすべてのシステムメモリがスペースによって消費される可能性があります。
#XMSG:
totalStatementThreadLimitHelpTextRestricted=テナントで使用可能なスレッドの合計数の割合を 1% から 70% の間 (またはこれに相当する数) で入力します。スレッド数の制限を低くし過ぎると文のパフォーマンスに影響がある場合がある一方で、値が高すぎると他のスペースの文のパフォーマンスに影響を及ぼすことがあります。
#XMSG:
totalStatementThreadLimitHelpTextDynamic=テナントで使用可能なスレッドの合計数の割合を 1% から {0}% の間 (またはこれに相当する数) で入力します。スレッド数の制限を低くし過ぎると文のパフォーマンスに影響がある場合がある一方で、値が高すぎると他のスペースの文のパフォーマンスに影響を及ぼすことがあります。
#XMSG:
totalStatementMemoryLimitHelpTextNew=値または割合を 0 (制限なし) からテナントで使用可能なメモリの総量の間で入力します。メモリの制限を低くし過ぎると文のパフォーマンスに影響がある場合がある一方で、値が高すぎると他のスペースの文のパフォーマンスに影響を及ぼすことがあります。
#XFLD: Total Statement Thread Limit
totalStatementThreadLimit=文のスレッド合計に関する制限
#XFLD: Segmented button label
workloadclassPercentage=%
#XFLD: Segmented button label
workloadclassGigaByte=GB
#XFLD, 80: Segmented button label
workloadclassThreads=スレッド
#XFLD: Total Statement Memory Limit
totalStatementMemoryLimit=文のメモリ合計に関する制限
#YMSE: Error while loading customer hana info
loadCustomerHanaInfoError=カスタム SAP HANA 情報のロードに失敗しました。
#XMSG:
minimumLimitReached=最小制限に達しました。
#XMSG:
maximumLimitReached=最大制限に達しました。
#XMSG: Name Taken for Technical Name
technical-name-taken=入力した技術名を持つ接続がすでに存在します。別の技術名を入力してください。
#XMSG: Name Too long for Technical Name
technical-name-too-long=入力した技術名が 40 文字を超えています。40 文字以下の技術名を入力してください。
#XMSG: Technical name field empty
technical-name-field-empty=技術名を入力してください。
#XMSG: Generic error when user enters invalid characters for a field
field-invalid-characters=名前には文字 (a-z)、数字 (0-9)、およびアンダースコア (_) のみを使用できます。
#XMSG: Generic error when user enters invalid initial characters for a field
field-underscore-trim=入力する名前をアンダースコア (_) で開始または終了することはできません。
#XFLD: Checkbox label for Enable Statement Limits
enableStatementLimits=文の制限を有効化
#XFLD: Connection SubSection Settings Title in space details
connectionSettings=設定
#XMSG: Connections tool hint in Space details section
connectionsToolHint=接続を作成または編集するには、サイドナビゲーションから接続アプリを開くか、以下をクリックします。
#XBUT: Connection list Open Connection Tool Button
button-connectiontool=接続に移動
#XFLD: Not deployed label on space tile
notDeployedLabel=スペースはまだ配置されていません。
#XFLD: Not deployed additional text on space tile
notDeployedText=スペースを配置してください。
#XFLD: Corrupt space label on space tile
corruptSpace=何らかの問題が発生しました。
#XFLD: Corrupt space additional text on space tile
corruptSpaceHint=再配置を試みるか、サポートに連絡してください。
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=監査ログデータ
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=管理データ
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=他のデータ
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=スペース内のデータ
#XMSG: Unlock Space confirmation text
unlockSpaceConfirmation=スペースのロックを解除してもよろしいですか?
#XMSG: Lock Space confirmation text
lockSpaceConfirmation=スペースをロックしてもよろしいですか?
#XFLD: Lock
lock=ロック
#XFLD: Unlock
unlock=ロック解除
#XFLD: Locking
locking=ロック中
#XMSG: Success message after locking space
lockSpaceSuccessMsg=スペースはロック済
#XMSG: Success message after unlocking space
unlockSpaceSuccessMsg=スペースはロック解除済
#XMSG: Success message after locking spaces
lockSpacesSuccessMsg=スペースはロック済
#XMSG: Success message after unlocking spaces
unlockSpacesSuccessMsg=スペースはロック解除済
#YMSE: Error while locking a space
lockSpaceError=スペースをロックできません。
#YMSE: Error while unlocking a space
unlockSpaceError=スペースのロックを解除できません。
#XTIT: popup title Warning
confirmationWarningTitle=警告
#XMSG: Message strip message when a space was locked manually
manualLockedMessage=スペースはマニュアルでロックされました。
#XMSG: Message strip message when a space was locked due to the amount of generated audit logs
auditLogLockedMessage=監査ログによって大量 (GB 単位) のディスクが消費されたため。スペースはシステムによってロックされました。
#XMSG: Message strip message when a space was locked because the assigned quota was exceeded.
quotaLockedMessage=メモリおよびディスクストレージの割り当てを超過したため、スペースはシステムによってロックされました。
#XMSG: Unlock Spaces confirmation text
unlockSpacesConfirmation=選択したスペースのロックを解除してもよろしいですか?
#XMSG: Lock Spaces confirmation text
lockSpacesConfirmation=選択したスペースをロックしてもよろしいですか?
#XTIT: Scoped role editor dialog title
scopedRoleEditorDialogTitle=スコープロールエディタ
#XTIT: ECN Management title
ecnManagementTitle=スペースおよび Elastic Compute ノードの管理
#XFLD: ECNs
ecns=Elastic Compute ノード
#XFLD: ECN phase Ready
ecnReady=準備完了
#XFLD: ECN phase Running
ecnRunning=実行しています
#XFLD: ECN phase Initial
ecnInitial=準備中
#XFLD: ECN phase Starting
ecnStarting=開始しています
#XFLD: ECN phase Starting Failed
ecnStartingFailed=開始が失敗しました
#XFLD: ECN phase Stopping
ecnStopping=停止しています
#XFLD: ECN phase Stopping Failed
ecnStoppingFailed=停止に失敗しました
#XBTN: Assign Button
assign=スペースを割り当て
#XBTN: Start Header-Button
start=開始
#XBTN: Update Header-Button
repair=更新
#XBTN: Stop Header-Button
stop=停止
#XFLD: ECN hours remaining
ecnHoursRemaining=残り時間は 1000 時間です
#XFLD: ECN block-hours remaining
ecnBlockHoursRemaining=残りブロック時間は {0} 時間
#XFLD: ECN block-hour remaining
ecnBlockHourRemaining=残りブロック時間は {0} 時間
#XTIT: ECN creation dialog title
ecnCreationDialogTitle=Elastic Compute ノードの作成
#XTIT: ECN edit dialog title
ecnEditDialogTitle=Elastic Compute ノードの編集
#XTIT: ECN deletion dialog title
ecnDeletionDialogTitle=Elastic Compute ノードの削除
#XTIT: ECN Space assignment dialog title
ecnSpaceAssignmentDialogTitle=スペースの割り当て
#XFLD: ECN ID
ECNIDLabel=Elastic Compute ノード
#XTXT: Selected toolbar text
selectedToolbarText=選択済み: {0}
#XTIT: Elastic Compute Nodes
ECNslong=Elastic Compute ノード
#XTIT: Object assignment - Table header: Objects available
objectsAvailable=オブジェクト数
#XTIT: Object assignment - Dialog header text
selectObjects=Elastic Compute ノードに割り当てるスペースおよびオブジェクトを選択します。
#XTIT: Object assignment - Table header title: Objects
objects=オブジェクト
#XTIT: Object assignment - Table header: Type
type=タイプ
#XMSG: Message strip delete database user audit warning
deleteDatabaseUserWarningMessageStrip=データベースユーザを削除すると、生成されたすべての監査ログエントリが削除されることに注意してください。監査ログを保持する場合は、データベースユーザを削除する前に監査ログをエクスポートすることを検討してください。
#XMSG: Message strip delete HDI container audit logs impact
deleteHDIContainerWarningMessageStrip=スペースから HDI コンテナを割り当て解除すると、生成されたすべての監査ログエントリが削除されることに注意してください。監査ログを保持する場合は、HDI コンテナを割り当て解除する前に監査ログをエクスポートすることを検討してください。
#XTXT: All audit logs
allAuditLogs=スペースに対して生成されたすべての監査ログエントリ
#XMSG: Message strip disable audit warning
disableAuditWarningMessageStrip=監査ポリシーを無効化する (読み込み操作または変更操作) と、すべての監査ログエントリが削除されることに注意してください。監査ログエントリを保持する場合は、監査ポリシーを無効化する前に監査ログエントリをエクスポートすることを検討してください。
#XTIT: No Spaces assigned to ECN Illustration title
noSpacesECNTitle=スペースまたはオブジェクトがまだ割り当てられていません
#XTIT: No Spaces assigned to ECN Illustration description
noSpacesECNDescription=Elastic Compute ノードの使用を開始するには、Elastic Compute ノードにスペースまたはオブジェクトを割り当てます。
#XTIT: No Spaces Illustration title
noSpacesTitle=スペースがまだ作成されていません
#XTIT: No Spaces Illustration description
noSpacesDescription=データの取得を開始するには、スペースを作成してください。
#XTIT: No Spaces Illustration title
noDeletedSpacesTitle=ごみ箱が空です
#XTIT: No Spaces Illustration description
noDeletedSpacesDescription=削除したスペースをここから復元できます。
#XTXT:  {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDatabaseUsers=スペースがデプロイされると、次のデータベースユーザは "{0}" 削除され、復元できません:
#XTIT: Delete database users
deleteDatabaseUsersTitle=データベースユーザを削除
#YMSE: Error if ecn id already exists
ecnIDInputDuplicateError=ID はすでに存在します。
#YMSE: Error if ecn id contains invalid characters
ecnIDInputInvalidError=小文字の a から z および数字の 0 から 9 のみを使用してください。
#YMSE: Error if ecn id is too short
ecnIDInputTooShortError=ID の長さは少なくとも {0} 文字である必要があります。
#XMSG: ecn id length warning
ecnIdLengthWarning=最大文字数の {0} を超えました。
#XFLD: open System Monitor
systemMonitor=システムモニタ
#XFLD: open ECN schedule dialog menu entry
schedule=スケジュール
#XFLD: open create ECN schedule dialog
createSchedule=スケジュールを作成
#XFLD: open change ECN schedule dialog
changeSchedule=スケジュールを編集
#XFLD: open delete ECN schedule dialog
deleteSchedule=スケジュールを削除
#XFLD: open self assign ECN schedule dialog
selfAssignSchedule=スケジュールを自分に割り当て
#XFLD: open pause ECN schedule dialog
pauseSchedule=スケジュールを一時停止
#XFLD: open resume ECN schedule dialog
resumeSchedule=スケジュールを再開
#XFLD: View Logs
viewLogs=ログの表示
#XFLD: Compute Blocks
computeBlocks=コンピュートブロック
#XFLD: Memory label in ECN creation dialog
ecnMemory=メモリ (GB)
#XFLD: Storage label in ECN creation dialog
ecnStorage=ストレージ (GB)
#XFLD: Number of CPU label in ECN creation dialog
ecnCPUCount=CPU 数
#XFLD: ECN updated by label
changedBy=変更者
#XFLD: ECN updated on label
changedOn=変更日付
#XMSG: Success message after creating a Elastic Compute Node
createEcnSuccessMessage=Elastic Compute ノードが作成されました
#YMSE: Error while creating a Elastic Compute Node
createEcnError=Elastic Compute ノードを作成できませんでした
#XMSG: Success message after updating a Elastic Compute Node
updateEcnSuccessMessage=Elastic Compute ノードが更新されました
#XMSG: Error while updating a Elastic Compute Node
udpateEcnError=Elastic Compute ノードを更新できませんでした
#XMSG: Success message after deleting a Elastic Compute Node
deleteEcnSuccessMessage=Elastic Compute ノードが削除されました
#YMSE: Error while deleting a Elastic Compute Node
deleteEcnError=Elastic Compute ノードを削除できませんでした
#XMSG: Success message after starting a Elastic Compute Node
startingEcnSuccessMessage=Elastic Compute ノードを開始しています
#XMSG: Success message after stopping a Elastic Compute Node
stoppingEcnSuccessMessage=Elastic Compute ノードを停止しています
#YMSE: Error while starting a Elastic Compute Node
startingEcnError=Elastic Compute ノードを開始できませんでした
#YMSE: Error while stopping a Elastic Compute Node
stoppingEcnError=Elastic Compute ノードを停止できませんでした
#XBUT: Add Object button for an ECN
assignObjects=オブジェクトを追加
#XFLD: Enable auto object assignment label
enableAutoObjAssignment=すべてのオブジェクトを自動的に追加
#XFLD: object type label to be assigned
objectTypeLabel=タイプ (セマンティック用途)
#XFLD: assigned object type label
assignedObjectTypeLabel=タイプ
#XFLD: technical name label
TechnicalNameLabel=技術名
#XFLD: Object selecttion ECN label
objectSelectionECNLabel=Elastic Compute ノードに追加するオブジェクトを選択します。
#XTIT: Add objects dialog title
assignObjectsTitle=オブジェクトの割り当て
#XFLD: object label with object count
objectLabel=オブジェクト
#XMSG: No objects available to add message.
noObjectsToAssign=割り当て可能なオブジェクトがありません。
#XMSG: No objects assigned message.
noAssignedObjects=割り当て済みのオブジェクトがありません。
#XTIT: Remove objects popup title
removeObjectsOrSpacesConfirmationTitle=警告
#XTIT: Delete ECN popup button and title
deleteECNConfirmationButtonAndTitle=削除
#XMSG: Remove objects popup text
removeObjectsConfirmation=選択したオブジェクトを削除してもよろしいですか?
#XMSG: Remove spaces popup text
removeSpacesConfirmation=選択したスペースを削除してもよろしいですか?
#XBUT: Confirmation button for objects dialog
removeObjectsOrSpacesConfirmationBtn=スペースを削除
#XMSG: Success message after removing objects
removeObjectsSuccessMessage=公開されたオブジェクトが削除されました
#XMSG: Success message after assigning objects
assignObjectsSuccessMessage=公開されたオブジェクトが割り当てられました
#XFLD: auto object assignment label for space tile
autoObjAssignmentLabel=公開されたすべてのオブジェクト
#XFLD: Spaces tab label
spacesTabLabel=スペース
#XFLD: Exposed objects tab label
consumptionObjectsTabLabel=公開されたオブジェクト
#XMSG: Success message after unassigning spaces
spacesUnassignedSuccessMessage=スペースが削除されました
#XMSG: Success message after unassigning space
spaceUnassignedSuccessMessage=スペースが削除されました
#YMSE: Error while assigning / unassigning spaces
spacesAssignUnassignError=スペースを割り当てたり削除したりすることができませんでした。
#YMSE: Error while removing objects
removeObjectsError=オブジェクトの割り当てまたは削除ができませんでした。
#YMSE: Error while removing object
removeObjectError=オブジェクトの割り当てまたは削除ができませんでした。
#YMSE: Error when saving the ecn with invalid value
invalidSizingPlanError=以前に選択した数値が無効になりました。有効な数値を選択してください。
#YMSE: Error when saving the ecn with invalid value
invalidPerformanceClassError=有効なパフォーマンスクラスを選択してください。
#YMSE: Error when saving the ecn with invalid value
performanceClassNotAvailableError=以前に選択したパフォーマンスクラス "{0}" は現在無効です。有効なパフォーマンスクラスを選択してください。
#XTXT: Delete ECN confirmation text
deleteConfirmationTxtECN=Elastic Compute ノードを削除してもよろしいですか?
#XFLD: tooltip for ? button
help=ヘルプ
#XFLD: ECN edit button label
editECN=設定
#XFLD: Technical type label for ERModel
DWC_ERMODEL=ER モデル
#XFLD: Technical type label for Local Table
DWC_LOCAL_TABLE=ローカルテーブル
#XFLD: Technical type label for Remote Table
DWC_REMOTE_TABLE=リモートテーブル
#XFLD: Technical type label for Analytic Model
DWC_ANALYTIC_MODEL=分析モデル
#XFLD: Technical type label for Task Chain
DWC_TASKCHAIN=タスクチェーン
#XFLD: Technical type label for Data Flow
DWC_DATAFLOW=データフロー
#XFLD: Technical type label for Replication Flow
DWC_REPLICATIONFLOW=複製フロー
#XFLD: Technical type label for Transformation Flow
DWC_TRANSFORMATIONFLOW=変換フロー
#XFLD: Technical type label for Intelligent Lookup
DWC_IDT=インテリジェントルックアップ
#XFLD: Technical type label for Repository
DWC_REPOSITORY_PACKAGE=リポジトリ
#XFLD: Technical type label for Enterprise Search
DWC_ESMODEL=エンタープライズサーチ
#XFLD: Technical type label for View
DWC_VIEW=ビュー
#XFLD: Technical type label for Data Product
DWC_DATAPRODUCT=データプロダクト
#XFLD: Technical type label for Data Access Control
DWC_DAC=データアクセス制御
#XFLD: Technical type label for Folder
DWC_FOLDER=フォルダ
#XFLD: Technical type label for Data Entity
DWC_BUSINESS_ENTITY=ビジネスエンティティ
#XFLD: Technical type label for Business Entity Variant
DWC_BUSINESS_ENTITY_VARIANT=ビジネスエンティティバリアント
#XFLD: Technical type label for Responsibility Scenario
DWC_AUTH_SCENARIO=責任シナリオ
#XFLD: Technical type label for Fact Model
DWC_FACT_MODEL=ファクトモデル
#XFLD: Technical type label for Transformation Flow
DWC_PERSPECTIVE=パースペクティブ
#XFLD: Technical type label for Consumption Model
DWC_CONSUMPTION_MODEL=利用モデル
#XFLD: Technical type label for Remote Connection
DWC_REMOTECONNECTION=リモート接続
#XFLD: Technical type label for Fact Model Variant
DWC_FACT_MODEL_VARIANT=ファクトモデルバリアント
#XMSG: Schedule created alert message
createScheduleSuccess=スケジュールが作成されました
#XMSG: Schedule updated alert message
updateScheduleSuccess=スケジュールが更新されました
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=スケジュールが削除されました
#XMSG: Schedule takeover success message
ownerChangeScheduleSuccess=スケジュールが割り当てられました
#XMSG: Message for starting pausing of schedule
pauseScheduleStarted=1 スケジュールを一時停止しています
#XMSG: Message for starting resuming of schedule
resumeScheduleStarted=1 スケジュールを再開しています
#XFLD: Segmented button label
availableSpacesButton=利用可能
#XFLD: Segmented button label
selectedSpacesButton=選択済み
#XFLD: Visit website button text
visitWebsite=Web サイトの訪問
#XMSG: Warning message on unselecting source language
sourceLanguageRemovalWarning=以前に選択したソース言語は削除されます。
#XBUT: enable translation confirmation button
enableTranslationConfirmationBtn=有効化
#XFLD: ECN performance class label
performanceClassLabel=パフォーマンスクラス
#XTXT performance class memory text
memoryText=メモリ
#XTXT performance class compute text
computeText=計算
#XTXT performance class high-compute text
highComputeText=計算 (高)
#XBUT: Recycle Bin Button Text
recycleBin=ごみ箱
#XBUT: Restore Button Text
restore=復元
#XMSG: Warning message for new Workload Management UI
priorityWarning=この領域は参照専用です。システム/設定/ワークロード管理領域でスペースの優先度を変更できます。
#XMSG: Warning message for new Workload Management UI
workloadWarning=この領域は参照専用です。システム/設定/ワークロード管理領域でスペースのワークロード設定を変更できます。
#XCOL: Space table-view column: Apache Spark vCPUs
sparkManagementVCPUs=Apache Spark の vCPU
#XCOL: Space table-view column: Apache Spark Memory (GB)
sparkManagementMemory=Apache Spark のメモリ (GB)
#XCOL: Space type (from Space Capability) for Data Ingestion Spaces
ingestionSpaceType=データプロダクトインジェスト
#XFLD: Deployment in progress nodatatext for spark table
sparkTableDeploymentInProgress=現在スペースがデプロイされているため、データを利用できません
#XFLD: Runtime data being loaded nodatatext for spark table
sparkTableDeploymentLoading=現在スペースがロードされているため、データを利用できません
#XCOL: Space type (from Space Capability) for BW Private Cloud Edition
BWPCESpaceType=SAP BW
#XBUT: Go to Instance Mappings Button
navToMappingsButton=インスタンスマッピングを編集
#XFLD: SAP HANA Data Lake Storage label if less than 1GB
dataLakeStorageUsed=< 1 GB
