#DO NOT REMOVE________________________
#XFLD: Breadcrumb for space monitoring page
spaceMonitorBreadcrumb=Övervakning
#XTXT: Type name for spaces in browser tab page title
space=Utrymme
#_____________________________________
#XFLD: Spaces label in
spaces=Utrymmen
#XFLD: Manage plan button text
manageQuotaButtonText=Hantera plan
#XBUT: Manage resources button
manageResourcesButton=Hantera resurser
#XFLD: Create space button tooltip
createSpace=Skapa utrymme
#XFLD: Create
create=Skapa
#XFLD: Deploy
deploy=Distribuera
#XFLD: Page
page=Sida
#XFLD: Cancel
cancel=Avbryt
#XFLD: Update
update=Uppdatera
#XFLD: Save
save=Spara
#XFLD: OK
ok=OK
#XFLD: days
days=Dagar
#XFLD: Space tile edit button label
edit=Redigera
#XFLD: Auto Assign all objects to space
autoAssign=Allokera automatiskt
#XFLD: Space tile open monitoring button label
openMonitoring=Övervaka
#XFLD: Delete
delete=Radera
#XFLD: Copy Space
copy=Kopiera
#XFLD: Close
close=Stäng
#XCOL: Space table-view column status
status=Status
#XFLD: Space status active
activeLabel=Aktiv
#XFLD: Space status locked
lockedLabel=Spärrad
#XFLD: Space status critical
criticalLabel=Kritisk
#XFLD: Space status cold
coldLabel=Kall
#XFLD: Space status deleted
deletedLabel=Raderad
#XFLD: Space status unknown
unknownLabel=Okänd
#XFLD: Space status ok
okLabel=OK
#XFLD: Database user expired
expired=Utgången
#XFLD: deployed
deployed=Distribuerad
#XFLD: not deployed
notDeployed=Inte distribuerad
#XFLD: changes to deploy
changesToDeploy=Ändringar att distribuera
#XFLD: pending
pending=Distribuerar
#XFLD: designtime error
designtimeError=Designtidsfel
#XFLD: runtime error
runtimeError=Körningstidsfel
#XFLD: Space created by label
createdBy=Uppläggning av
#XFLD: Space created on label
createdOn=Uppläggning den
#XFLD: Space deployed on label
deployedOn=Distribution den
#XFLD: Space ID label
spaceID=Utrymmes-ID
#XFLD: Priority label
priority=Prioritet
#XFLD: Space Priority label
spacePriority=Utrymmesprioritet
#XFLD: Space Configuration label
spaceConfiguration=Utrymmeskonfiguration
#XFLD: Not available
notAvailable=Ej tillgänglig
#XFLD: WorkloadType default
default=Standard
#XFLD: WorkloadType custom
custom=Användardefinierad
#XFLD: Data Lake option label; refers to component SAP HANA Cloud, Data Lake
dataLakeAccess=Åtkomst till datasjö
#XFLD: Translation label
translationLabel=Översättning
#XFLD: Source language label
sourceLanguageLabel=Källspråk
#XFLD: Translation CheckBox label
translationCheckBox=Aktivera översättning
#XTXT: Popover hint that space has to be deployed to show user details
openDatabaseUserHintPopoverText=Distribuera utrymme för åtkomst till användardetaljer.
#XTXT: Popover hint that space has to be deployed to open database explorer
openDatabaseExplorerHintPopoverText=Distribuera utrymme för att öppna Databasutforskaren.
#XTXT: Text for when the Data Lake check box is disabled
dataLakeOptionDisabled=Det här utrymmet kan inte användas för åtkomst till datasjön eftersom det redan används av ett annat utrymme.
#XFLD: Data Lake check box text; refers to component SAP HANA Cloud, Data Lake
dataLakeCheckBox=Använd detta utrymme för åtkomst till datasjön.
#XFLD: Space Priority minimum label extension
low=Låg
#XFLD: Space Priority maximum label extension
high=Hög
#XFLD: Space name label
spaceName=Namn på utrymme
#XFLD: Enable deploy objects checkbox
enableDeployObjects=Distribuera objekt
#XTIT: Copy spaces dialog title
copySpaceDialogTitle=Kopiera {0}
#XTXT Human readable text for unselected source language
unSelectedSourceLanguage=(Inte valt)
#XTXT Human readable text for language code "af"
af=Afrikaans
#XTXT Human readable text for language code "ar"
ar=Arabiska
#XTXT Human readable text for language code "bg"
bg=Bulgariska
#XTXT Human readable text for language code "ca"
ca=Katalanska
#XTXT Human readable text for language code "zh"
zh=Förenklad kinesiska
#XTXT Human readable text for language code "zf"
zf=Kinesiska
#XTXT Human readable text for language code "hr"
hr=Kroatiska
#XTXT Human readable text for language code "cs"
cs=Tjeckiska
#XTXT Human readable text for language code "cy"
cy=Walesiska
#XTXT Human readable text for language code "da"
da=Danska
#XTXT Human readable text for language code "nl"
nl=Nederländska
#XTXT Human readable text for language code "en-UK"
en-UK=Engelska (Storbritannien)
#XTXT Human readable text for language code "en"
en=Engelska (USA)
#XTXT Human readable text for language code "et"
et=Estniska
#XTXT Human readable text for language code "fa"
fa=Persiska
#XTXT Human readable text for language code "fi"
fi=Finska
#XTXT Human readable text for language code "fr-CA"
fr-CA=Franska (Kanada)
#XTXT Human readable text for language code "fr"
fr=Franska
#XTXT Human readable text for language code "de"
de=Tyska
#XTXT Human readable text for language code "el"
el=Grekiska
#XTXT Human readable text for language code "he"
he=Hebreiska
#XTXT Human readable text for language code "hi"
hi=Hindi
#XTXT Human readable text for language code "hu"
hu=Ungerska
#XTXT Human readable text for language code "is"
is=Isländska
#XTXT Human readable text for language code "id"
id=Bahasa Indonesia
#XTXT Human readable text for language code "it"
it=Italienska
#XTXT Human readable text for language code "ja"
ja=Japanska
#XTXT Human readable text for language code "kk"
kk=Kazakiska
#XTXT Human readable text for language code "ko"
ko=Koreanska
#XTXT Human readable text for language code "lv"
lv=Lettiska
#XTXT Human readable text for language code "lt"
lt=Litauiska
#XTXT Human readable text for language code "ms"
ms=Bahasa Melayu
#XTXT Human readable text for language code "no"
no=Norska
#XTXT Human readable text for language code "pl"
pl=Polska
#XTXT Human readable text for language code "pt"
pt=Portugisiska (Brasilien)
#XTXT Human readable text for language code "pt-PT"
pt-PT=Portugisiska (Portugal)
#XTXT Human readable text for language code "ro"
ro=Rumänska
#XTXT Human readable text for language code "ru"
ru=Ryska
#XTXT Human readable text for language code "sr"
sr=Serbiska
#XTXT Human readable text for language code "sh"
sh=Serbokroatiska
#XTXT Human readable text for language code "sk"
sk=Slovakiska
#XTXT Human readable text for language code "sl"
sl=Slovenska
#XTXT Human readable text for language code "es"
es=Spanska
#XTXT Human readable text for language code "es-MX"
es-MX=Spanska (Mexiko)
#XTXT Human readable text for language code "sv"
sv=Svenska
#XTXT Human readable text for language code "th"
th=Thailändska
#XTXT Human readable text for language code "tr"
tr=Turkiska
#XTXT Human readable text for language code "uk"
uk=Ukrainska
#XTXT Human readable text for language code "vi"
vi=Vietnamesiska
#XTIT: Delete spaces confirmation title
deleteConfirmationHeaderText=Radera utrymmen
#XTXT: Logically delete space delete confirmation text with space name
logicallyDeleteConfirmationBodyTextWithSpaceName=Vill du flytta utrymme "{0}" till papperskorgen?
#XTXT: Logically dpace delete confirmation text for multiple spaces
logicallyDeleteConfirmationBodyTextMultiple=Vill du flytta de {0} valda utrymmena till papperskorgen?
#XTXT: Delete space delete confirmation text with space name
deleteConfirmationBodyTextWithSpaceName=Vill du radera utrymme "{0}"? Åtgärden kan inte ångras.
#XTXT: Delete space delete confirmation text for multiple spaces
deleteConfirmationBodyTextMultiple=Vill du radera {0} valda utrymmen? Åtgärden kan inte ångras. Följande innehåll tas bort {1}:
#XTXT: permanently
permanently=permanent
#XTXT: {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDetail=Följande innehåll kommer att raderas {0} och kan inte återställas:
#XTXT: Hint Delete space confirmation text
deleteConfirmationHint=Skriv {0} för att bekräfta radering.
#XTXT: Delete space confirmation error check spelling
deleteConfirmationErrorCheckSpelling=Kontrollera stavningen och försök igen.
#XTXT: All Spaces
allSpaces=Alla utrymmen
#XTXT: All data
allData=Alla objekt och data som finns i utrymmet
#XTXT: All connections
allConnections=Alla anslutningar som definierats i utrymmet
#XFLD: Space tile selection box tooltip
clickToSelect=Klicka för att välja
#XTXT: All database users
allDatabaseUsers=Alla objekt och data som finns i något Open SQL-schema som är associerat till utrymmet
#XFLD: remove members button tooltip
deleteUsers=Ta bort element
#XTXT: Space long description text
description=Beskrivning (maximalt 4 000 tecken)
#XFLD: Add Members button tooltip
addUsers=Lägg till element
#XFLD: Add Users button tooltip
addUsersTooltip=Lägg till användare
#XFLD: Edit Users button tooltip
editUsersTooltip=Redigera användare
#XFLD: Remove Users button tooltip
removeUsersTooltip=Ta bort användare
#XFLD: Searchfield placeholder
filter=Sök
#XCOL: Users table-view column health
health=Hälsa
#XCOL: Users table-view column access
access=Åtkomst
#XFLD: No user found nodatatext
noDataText=Ingen användare hittades
#XTIT: Members dialog title
selectUserDialogTitle=Lägg till element
#XTIT: User dialog title
addUserDialogTitle=Lägg till användare
#XTIT: Delete connections confirmation title
deleteConnectionConfirmationHeaderText=Radera anslutningar
#XTIT: Delete single connection confirmation title
deleteConnectionConfirmationHeaderTextSingle=Radera anslutning
#XTXT: Delete connection delete confirmation text
deleteConnectionConfirmationBodyText=Vill du radera valda anslutningar? De tas bort permanent.
#XTIT: Remote source dialog title
selectRemoteConnectionDialogTitle=Välj anslutningar
#XTIT: Share connection dialog title
connectionSharingDialogTitle=Dela anslutning
#XTIT: Connections shared from other spaces table title
sharedConnectionsFromOtherSpaces=Delade anslutningar
#XFLD: Add remote source button tooltip
addRemoteConnections=Lägg till anslutningar
#XFLD: Delete remote source button tooltip
deleteRemoteConnections=Ta bort anslutningar
#XFLD: Share remote source button tooltip
shareConnections=Dela anslutningar
#XFLD: Tile-layout tooltip
tileLayout=Panellayout
#XFLD: Table-layout tooltip
tableLayout=Tabellayout
#XMSG: Success message after creating space
createSpaceSuccessMessage=Utrymme har skapats
#XMSG: Success message after copying space
copySpaceSuccessMessage=Kopierar utrymme "{0}" till utrymme "{1}"
#XMSG: Success message after deploying space
deploymentSuccessMessage=Distribution av utrymme har startat
#XMSG: Success message after Apache Spark update started
sparkUpdateStartedSuccessMessage=Uppdatering av Apache Spark har startat
#YMSE: Error while starting Apache Spark update
sparkUpdateStartedErrorMessage=Apache Spark kunde inte uppdateras
#XMSG: Success message after updating space
updateSettingsSuccessMessage=Detaljer för utrymme har uppdaterats
#XMSG: Success message after unlocking space
unlockSpaceSuccessMessage=Spärr för utrymme har hävts tillfälligt
#XMSG: Success message after deleting a single space
deleteSpaceSuccessMessage=Utrymme har raderats
#XMSG: Success message after deleting multiple spaces
deleteSpacesSuccessMessage=Utrymmen har raderats
#XMSG: Success message after restoring a single space
restoreSpaceSuccessMessage=Utrymme har återställts
#XMSG: Success message after restoring multiple spaces
restoreSpacesSuccessMessage=Utrymmen har återställts
#YMSE: Error while updating settings
updateSettingsFailureMessage=Inställningar för utrymme kunde inte uppdateras.
#YMSE: Error while updating data lake option
dataLakeEnabledInOtherSpace=Datasjön är redan allokerad till ett annat utrymme. Endast ett utrymme i taget kan ha åtkomst till datasjön.
#YMSE: Error while updating data lake option
virtualTablesExists=Datasjön kan inte avallokeras från detta utrymme eftersom det fortfarande finns beroenden till virtuella tabeller*. Radera virtuella tabeller för att kunna avallokera datasjön från detta utrymme.
#YMSE: Error while unlocking space
unlockSpaceFailureMessage=Spärr för utrymme kunde inte hävas.
#YMSE: Error while creating space
createSpaceError=Utrymme kunde inte skapas.
#YMSE: Error while creating duplicate space
createDuplicateSpaceError=Utrymme med namn {0} finns redan.
#YMSE: Error while deleting a single space
deleteSpaceError=Utrymme kunde inte raderas.
#YMSE: Error details while deleting a single space
deleteSpaceDetailedError=Ditt utrymme "{0}" fungerar inte ordentligt längre. Försök att radera det igen. Om det fortfarande inte fungerar, be din administratör att radera utrymmet eller öppna ett ärende.
#YMSE: Error while deleting a single space in files
deleteSpaceInFilesError=Utrymmesdata i Filer kunde inte raderas.
#YMSE: Error while deleting a single space users
deleteSpaceUsersError=Användare kunde inte tas bort.
#YMSE: Error while deleting a single space schema
deleteSpaceSchemasError=Scheman kunde inte tas bort.
#YMSE: Error while deleting a single space connections
deleteSpaceConnectionsError=Anslutningar kunde inte tas bort.
#YMSE: Error while deleting a single space data
deleteSpaceDataError=Utrymmesdata kunde inte raderas.
#YMSE: Error while deleting multiple spaces
deleteSpacesError=Utrymmen kunde inte raderas.
#YMSE: Error while restoring a single space
restoreSpaceError=Utrymme kunde inte återställas.
#YMSE: Error while restoring multiple spaces
restoreSpacesError=Utrymmen kunde inte återställas.
#YMSE: Error while creating users
createUsersError=Användare kunde inte läggas till.
#YMSE: Error while removing users
removeUsersError=Användare kunde inte tas bort.
#YMSE: Error while removing user
removeUserError=kunde inte ta bort användaren.
#YMSE: Error assigning roles to a user
roleAssignUnAssignError=Vi kunde inte lägga till användaren i vald omfattningsroll. \n\n Du kan inte lägga till dig själv i en omfattningsroll. Du kan be din administratör att lägga till dig i en omfattningsroll.
#YMSE: Error assigning user to the space
userAssignError=Vi kunde inte allokera användaren till utrymmet. \n\n Användaren har redan allokerats till maximalt antal tillåtna (100) utrymmen i omfattningsroller.
#YMSE: Error assigning users to the space
usersAssignError=Vi kunde inte allokera användarna till utrymmet. \n\n Användaren har redan allokerats till maximalt antal tillåtna (100) utrymmen i omfattningsroller.
#YMSE: Error while fetching users
fetchUserWithScopedRolesError=Användare kunde inte hämtas. Försök igen senare.
#YMSE: Error while fetching scoped roles
fetchScopedRolesError=Omfattningsroller kunde inte hämtas.
#YMSE: Error while fetching members
fetchUserError=Element kunde inte hämtas. Försök igen senare.
#YMSE: Error while loading run-time database
loadRuntimeError=Information från körningstidsdatabas kunde inte läsas in.
#YMSE: Error while loading spaces
loadSpacesError=Något gick fel vid försök att hämta dina utrymmen.
#YMSE: Error while loading haas resources
loadStorageError=Något gick fel vid försök att hämta lagrade data.
#YMSE: Error no data could be loaded
loadDataError=Något gick fel vid försök att hämta dina data.
#XFLD: Click to refresh storage data
clickToRefresh=Klicka här för att försöka igen.
#XTIT: Delete space popup title
deleteSpacePopupTitle=Radera utrymme
#XCOL: Spaces table-view column name
name=Namn
#XCOL: Spaces table-view deployment status
deploymentStatus=Distributionsstatus
#XFLD: Disk label in space details
storageLabel=Disk (GB)
#XFLD: In-Memory label in space details
ramLabel=Minne (GB)
#XFLD: Memory label on space card
memory=Minne för lagring
#XFLD: Storage Assignment label in space details
storrageAssignmentLabel=Utrymmeslagring
#XFLD: Storage Type label in space details
storageTypeLabel=Lagringstyp
#XFLD: Enable Space Quota
enableSpaceQuota=Aktivera utrymmeskvot
#XFLD: No Space Quota
noSpaceQuota=Ingen utrymmeskvot
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
diskAndInMemoryText=SAP HANA-databas (disk- och minnesbaserad)
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
dataLakeFilesText=SAP HANA, datasjöfiler
#XFLD: Available scoped roles label
availableRoles=Tillgängliga omfattningsroller
#XFLD: Selected scoped roles label
selectedRoles=Valda omfattningsroller
#XCOL: Spaces table-view column models
models=Modeller
#XCOL: Spaces table-view column users
users=Användare
#XCOL: Spaces table-view column connections
connections=Anslutningar
#XFLD: Section header overview in space detail
overview=Översikt
#XFLD: Section header Apache Spark in space detail
sparkManagementSection=Apache Spark
#XFLD: Section header Apache Spark in space detail
sparkManagementSubSection=Applikationer
#XFLD: Section header Apache Spark in space detail
taskAssignmentSubSection=Uppgiftsallokering
#XFLD: vCPU label in Apache Spark section
vCPULabel=vCPU:er
#XFLD: Memory label in Apache Spark section
memoryLabel=Minne (GB)
#XFLD: space configuration label in Apache Spark section
spaceConfigurationLabel=Utrymmeskonfiguration
#XFLD: Space Source label
sparkApplicationLabel=Applikation
#XFLD: Cluster Size label
clusterSizeLabel=Klusterstorlek
#XFLD: Driver label
driverLabel=Drivrutin
#XFLD: Executor label
executorLabel=Utförare
#XFLD: max label
maxLabel=Max. användning
#XFLD: TrF Default label
trFDefaultLabel=Transformationsflöde - standard
#XFLD: Merge Default label
mergeDefaultLabel=Slå samman - standard
#XFLD: Optimize Default label
optimizeDefaultLabel=Optimera - standard
#XFLD: Deployment Default label
deploymentDefaultLabel=Distribution för lokal tabell (fil)
#XFLD: max spark source label
driverExecutorMaxUsedSparkSource={0} CPU/{1} GB
#XFLD: Object type label
taskObjectTypeLabel=Objekttyp
#XFLD: Task activity label
taskActivityLabel=Aktivitet
#XFLD: Task Application ID label
taskApplicationIDLabel=Standardapplikation
#XFLD: Section header in space detail
generalSettings=Allmänna inställningar
#XMSG: Message strip message when a space is locked
spaceLockedMessageStrip=Detta utrymme spärras för närvarande av systemet.
#XMSG: Message strip message deploy warning
deployWarningMessageStrip=Ändringar i detta avsnitt distribueras direkt.
#XMSG: Message strip message modify space configuration warning
modifySpaceConfigurationMessageStrip=Observera att ändring av dessa värden kan orsaka prestandaproblem.
#XFLD: Button text to unlock the space again
unlockSpace=Häv spärr för utrymme
#XFLD: Info text for audit log formatted message
auditLogText=Aktivera revisionsloggar för att registrera läs- eller ändringsåtgärder (revisionsriktlinjer). Administratörer kan sedan analysera vem som utförde vilken åtgärd vid vilken tidpunkt.
#XMSG: Message strip  audit warning
auditLogWarningMessageStrip=Revisionsloggar kan förbruka en stor mängd disklagring i tenanten. Om du aktiverar en revisionsriktlinje (läs- eller ändringsåtgärder) bör du regelbundet övervaka använd disklagring (via kortet Använd disklagring i systemmonitorn) för att undvika att lagringsutrymmet blir fullt och orsakar tjänsteavbrott. Om du inaktiverar en revisionsriktlinje raderas alla dess revisionsloggsuppgifter. Om du vill behålla revisionsloggsuppgifterna bör du överväga att exportera dem innan du inaktiverar revisionsriktlinjen.
#XFLD: Message strip link text to open in app help to guide user what to do to unlock his space permanently
unlockSpaceHelp=Visa hjälp
#XMSG: Message strip message when a space will be locked again
spaceTemporarilyUnlockedMessageStrip=Det här utrymmet överskrider sin utrymmeslagring och kommer att spärras om {0} {1}
#XMSG: Unit for remaining time until space is locked again
hours=timmar
#XMSG: Unit for remaining time until space is locked again
minutes=minuter
#XFLD: Section header in space detail for auditing
detailsSectionAuditing=Granskning
#XFLD: Subsection header in space detail for auditing
auditing=Granskningsinställningar för utrymme
#XFLD: Hot space tooltip
hotSpaceCountTooltip=Kritiska utrymmen: Använt lagringsutrymme är större än 90 %.
#XFLD: Green space tooltip
greenSpaceCountTooltip=Utrymmen med status "OK": Använt lagringsutrymme är mellan 6 % och 90 %.
#XFLD: Cold space tooltip
coldSpaceCountTooltip=Kalla utrymmen: Använt lagringsutrymme är 5 % eller mindre.
#XFLD: Critical space tooltip
criticalSpaceCountTooltip=Kritiska utrymmen: Använt lagringsutrymme är större än 90 %.
#XFLD: Green space tooltip
okSpaceCountTooltip=Utrymmen med status "OK": Använt lagringsutrymme är mellan 6 % och 90 %.
#XFLD: Locked space tooltip
lockedSpaceCountTooltipNew=Spärrade utrymmen: Spärrade på grund av otillräckligt minne.
#XFLD: Locked space tooltip
lockedSpaceCountTooltip=Spärrade utrymmen
#YMSE: Error while deleting remote source
deleteRemoteError=Anslutningar kunde inte tas bort.
#XFLD: Space ID input tooltip
spaceIDInfoTooltip=Utrymmes-ID kan inte ändras senare.\nGiltiga tecken A - Z, 0 - 9 och _
#YMSE: Error when space name is empty
spaceNameEmptyValidationError=Ange namn på utrymme.
#YMSE: Error when ECN Business name is empty
ecnNameEmptyValidationError=Ange affärsnamn.
#YMSE: Error when space id is empty
spaceIDInputEmptyValidationError=Ange utrymmes-ID.
#YMSE: Error when space id contains invalid characters
spaceIDInputInValidError=Ogiltiga tecken. Använd endast A - Z, 0 - 9 och _.
#YMSE: Error when space id exists while creation
spaceIDInputDuplicateError=Utrymmes-ID finns redan.
#XFLD: Space searchfield placeholder
search=Sök
#XMSG: Success message after creating users
createUsersSuccess=Användare har lagts till
#XMSG: Success message after creating user
createUserSuccess=Användare har lagts till
#XMSG: Success message after updating users
updateUsersSuccess={0} användare har uppdaterats
#XMSG: Success message after updating user
updateUserSuccess=Användare har uppdaterats
#XMSG: Success message after removing users
removeUsersSuccess={0} användare har tagits bort
#XMSG: Success message after removing user
removeUserSuccess=Användare har tagits bort
#XFLD: Schema name
schemaName=Schemanamn
#XFLD: used of total
ofTemplate={0} av {1}
#XFLD: Assigned disk label in space overview
assignedStorageTemplate=Allokerad disk ({0} av {1})
#XFLD: Assigned Memory label in space overview
assignedRAMTemplate=Allokerat minne ({0} av {1})
#XFLD: Storage ratio on space
accelearationRAM=Minnesacceleration
#XFLD: No Storage Consumption
noStorageConsumptionText=Ingen lagringskvot har allokerats.
#XFLD: Used disk label in space overview
usedStorageTemplate=Disk använd för lagring ({0} av {1})
#XFLD: Used Memory label in space overview
usedRAMTemplate=Minne använt för lagring ({0} av {1})
#XFLD: Used Disk tooltip
usedStorageTooltipTemplate={0} av {1} diskar använd för lagring
#XFLD: Used Memory tooltip
usedRAMTooltipTemplate={0} av {1} minne används
#XFLD: Assigned Disk tooltip
assignedStorageTooltipTemplate={0} av {1} disklagring allokerad
#XFLD: Assigned Memory tooltip
assignedRAMTooltipTemplate={0} av {1} minne har allokerats
#XFLD: Space related used storage tooltip
spaceRelatedUsedStorageTooltipTemplate=Utrymmesdata: {0}
#XFLD: Not space related used storage tooltip
otherDataUsedStorageTooltipTemplate=Övriga data: {0}
#XFLD: Storage used exceeded popover and tooltip
storageUsedExceededText=Överväg att utöka din plan eller kontakta SAP-support.
#XCOL: Space table-view column used Disk
usedStorage=Disk använd för lagring
#XCOL: Space monitor column used Memory
usedRAM=Minne använt för lagring
#XCOL: Space monitor column Schema
tableSchema=Schema
#XCOL: Space monitor column Storage Type
tableStorageType=Lagringstyp
#XCOL: Space monitor column Table Type
tableType=Tabelltyp
#XCOL: Space monitor column Record Count
tableRecordCount=Antal poster
#XFLD: Assigned Disk
assignedStorage=Disk allokerad för lagring
#XFLD: Assigned Memory
assignedRAM=Minne allokerat för lagring
#XCOL: Space table-view column storage utilization
tableStorageUtilization=Använt lagringsutrymme
#XFLD: space status
spaceStatus=Status för utrymme
#XFLD: space type
spaceType=Utrymmestyp
#XFLD: space type label for space with space type "SAP BW bridge" ("SAP BW bridge" is a product name, do not translate it)
abapbridgeSpaceType=SAP BW Bridge
#XFLD: space type label for space with space type "Data Marketplace"
marketplaceSpaceType=Produkt från dataleverantör
#XMSG: Error message for when selected space cannot be deleted due to the space type
deleteTypedSpaceError=Du kan inte radera utrymme {0} då utrymmestyp är {1}.
#XMSG: Error message for when selected spaces cannot be deleted due to the space type
deleteTypedSpacesError=Du kan inte radera {0} valda utrymmen. Utrymmen med följande utrymmestyper kan inte raderas: {1}.
#XFLD: space type label for space without space type
defaultSpaceType=SAP Datasphere
#XFLD: Tooltip for monitor space button
monitorSpace=Övervaka
#XFLD: Tooltip for edit space button
editSpace=Redigera utrymme
#XMSG: Deletion warning in messagebox
deleteConfirmation=Ska utrymme raderas?
#XFLD: Tooltip for delete space button
deleteSpace=Radera utrymme
#XFLD: storage
storage=Disk för lagring
#XFLD: username
userName=Användarnamn
#XFLD: port
port=Port
#XFLD: hostname
hostName=Värddatornamn
#XFLD: password
password=Lösenord
#XBUT: Request new password button
requestPassword=Begär nytt lösenord
#YEXP: Usage explanation in time data section
timeDataSectionHint=Skapa tidtabeller och dimensioner att använda i dina modeller och storyer.
#YEXP: Usage explanation in Space schema section
spaceSchemaHint=Vill du att data i ditt utrymme ska kunna användas av andra verktyg eller appar? Skapa i så fall en eller flera användare som har åtkomst till data i ditt utrymme och välj om du vill att alla framtida data i utrymmet ska kunna användas som standard.
#XTIT: Create schema popup title
createSchemaDialogTitle=Skapa Open SQL-schema
#XTIT: Generate Time Data dialog title
generateTimeDataDialogTitle=Skapa tidtabeller och dimensioner
#XTIT: Edit Time Data dialog title
editTimeDataDialogTitle=Redigera tidtabeller och dimensioner
#XTIT: Time Data token title
timeDataTokenTitle=Tidsdata
#XTIT: Time Data token title
timeDataUpdateViews=Uppdatera tidsdatavyer
#XFLD: Time Data token creation in progress label
timeDataCreationInProgress=Uppläggning pågår...
#XFLD: Time Data token creation error label
timeDataCreationError=Uppläggning misslyckades. Försök igen.
#XTIT: Time Data dialog time settings title
timeDataDialogTimeSettingsTitle=Inställningar för tidtabell
#XTIT: Time Data dialog time data table title
timeDataDialogTranslationTablesTitle=Översättningstabeller
#XTIT: Time Data dialog dimensions title
timeDataDialogTimeViewsTitle=Tidsdimensioner
#XFLD: Time Data dialog time range label
timeRangeHint=Definiera tidsintervall.
#XFLD: Time Data dialog time data table label
timeDataHint=Ge din tabell ett namn.
#XFLD: Time Data dialog time data dimensions label
timeDimensionsHint=Ge dina dimensioner ett namn.
#XFLD: Time Data Time range description label
timerangeLabel=Tidsintervall
#XFLD: Time Data dialog from year label
fromYearLabel=Från år
#XFLD: Time Data dialog to year label
toYearLabel=Till år
#XFLD: Time Data dialog calendar type label
calendarTypeLabel=Kalendertyp
#XFLD: Time Data dialog granularity label
granularityLabel=Granularitet
#XFLD: Time Data dialog technical name label
technicalNameLabel=Tekniskt namn
#XFLD: Time Data dialog quarters text table lable
quarterTextsTableLabel=Översättningstabell för kvartal
#XFLD: Time Data dialog months text table lable
monthsTextsTableLabel=Översättningstabell för månader
#XFLD: Time Data dialog days text table lable
daysTextsTableLabel=Översättningstabell för dagar
#XFLD: Time Data dialog year label
yearLabel=Årsdimension
#XFLD: Time Data dialog quarter label
quarterLabel=Kvartalsdimension
#XFLD: Time Data dialog month label
monthLabel=Månadsdimension
#XFLD: Time Data dialog day label
dayLabel=Dagsdimension
#XFLD: Time Data dialog gregorian calendar type label
gregorian=Gregoriansk
#XFLD: Time Data dialog time granularity day label
day=Dag
#XFLD: Time Data dialog business name maxlength reached
timeRangeValueStateTextMaxLengthBusinessName=Max. längd på 1000 tecken har nåtts.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextMaxRangeExceeded=Maximalt tidsintervall är 150 år.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeLower="Från år" ska vara mindre än "Till år"
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeHigher="Från år" måste vara 1900 eller högre.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeHigher="Till år" ska vara större än "Från år"
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeLower="Till år" måste vara lägre än aktuellt år plus 100
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningFromYear=Ökning av "Från år" kan leda till förlust av data
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningToYear=Minskning av "Till år" kan leda till förlust av data
#XMSG: Time Data creation validation error message
timeDataValidationError=Några fält verkar vara ogiltiga. Kontrollera obligatoriska fält för att fortsätta.
#XMSG: Time Data deletion popup text
deleteTimeDataConfirmation=Ska data raderas?
#XMSG: Time Data creation success message
createTimeDataSuccess=Tidsdata har skapats
#XMSG: Time Data update success message
updateTimeDataSuccess=Tidsdata har uppdaterats
#XMSG: Time Data delete success message
deleteTimeDataSuccess=Tidsdata har raderats
#XMSG: Time Data creation error message
createTimeDataError=Något gick fel vid försök att skapa tidsdata.
#XMSG: Time Data update error message
updateTimeDataError=Något gick fel vid försök att uppdatera tidsdata.
#XMSG: Time Data creation error message
deleteTimeDataError=Något gick fel vid försök att radera tidsdata.
#XMSG: Error message when time data could not be retrieved
retrievingTimeDataError=Tidsdata kunde inte läsas in.
#XTIT: Time Data deletion popup title
deleteTimeDataConfirmationTitle=Varning
#XTXT: Time Data deletion dependency popup text
deleteTimeDataDependencyText=Tidsdata kunde inte raderas eftersom de används i andra modeller.
#XTXT: Time Data deletion dependency popup text singular
deleteTimeDataDependencyTextSingular=Tidsdata kunde inte raderas eftersom de används i en annan modell.
#XMSG: Validation error when required time data form field is empty
timeDataRequiredField=Detta fält är obligatoriskt och kan inte lämnas tomt.
#XFLD: Tooltip for Open in Database Explorer button
openInDBX=Öppna i Databasutforskaren
#YMSE: Dimension Year
dimensionYearView=Dimension "År"
#YMSE: Dimension Year
dimensionQuarterView=Dimension "Kvartal"
#YMSE: Dimension Year
dimensionMonthView=Dimension "Månad"
#YMSE: Dimension Year
dimensionDayView=Dimension "Dag"
#XFLD: Time Data deletion object title
timeDataUsedIn=(används i {0} modeller)
#XFLD: Time Data deletion object title singular
timeDataUsedInSingular=(används i 1 modell)
#XFLD: Time Data deletion table column provider
provider=Leverantör
#XTIT: Time Data dependency dialog title
timeDataDependencyDialogTitle=Beroenden
#XTIT: Create schema popup title
createSpaceSchemaUserDialogTitle=Skapa användare för utrymmesschema
#XFLD: Create schema button
createSchemaButton=Skapa Open SQL-schema
#XFLD: Generate TimeData button
generateTimeDataButton=Skapa tidtabeller och dimensioner
#XFLD: Show dependencies button
showDependenciesButton=Visa beroenden
#XFLD: tooltip for disabled buttons when user is not member of space
mustBeMemberOfSpaceForOperation=För att utföra denna operation måste användaren vara medlem i utrymmet.
#XFLD: Create space schema user button
createSpaceSchemaUserButton=Skapa användare för utrymmesschema
#YMSE: API Schema users load error
loadSchemaUsersError=Lista över användare kunde inte läsas in.
#XTIT: API Schema-config popup title
schemaUserDetailsDialogTitle=Användardetaljer för utrymmesschema
#XMSG: API Schema user deletion popup text
deleteSpaceSchemaUserConfirmation=Ska vald användare raderas?
#XMSG: API Schema user deletion confirmation text
userDeleteSuccess=Användare har raderats.
#YMSE: API Schema user deletion error
userDeleteError=Användare kunde inte raderas.
#XFLD: User deleted
userDeleted=Användaren har raderats.
#XTIT: Remove user popup title
removeUserConfirmationTitle=Varning
#XMSG: Remove user popup text
removeUserConfirmation=Vill du ta bort användaren? Användaren och dess allokerade omfattningsroller kommer att tas bort från utrymmet.
#XMSG: Remove users popup text
removeUsersConfirmation=Vill du ta bort användarna? Användarna och deras allokerade omfattningsroller kommer att tas bort från utrymmet.
#XBUT: Confirmation button for remove user dialog
removeUserConfirmationBtn=Ta bort
#YMSE: No data text for available roles
noDataAvailableRoles=Utrymmet har inte lagts till i någon omfattningsroll. \n För att kunna lägga till användare i utrymmet måste det först läggas till i en eller flera omfattningsroller.
#YMSE: No data text for selected roles
noDataSelectedRoles=Inga valda omfattningsroller
#XTIT: Schema-config popup title
schemaDetailsDialogTitle=Konfigurationsdetaljer för Open SQL-schema
#XFLD: Label for Read Audit Log
auditLogRead=Aktivera revisionslogg för läsoperationer
#XFLD: Label for Change Audit Log
auditLogChange=Aktivera revisionslogg för ändringsoperationer
#XFLD: Label Audit Log Retention
auditLogRetention=Behåll loggar i
#XFLD: Label Audit Log Retention Unit
retentionUnit=Dagar
#YMSE: Error when log retention time is set incorrectly
incorrectRetentionTime=Ange ett heltal mellan {0} och {1}
#XFLD: Label for Cross Space Schema Access
spaceSchemaAccess=Förbruka data för utrymmesschema
#XTIT: Confirm deactivation of space schema consumption dialog title
disableSpaceSchemaConsumptionDialogTitle=Stoppa förbrukning av data för utrymmesschema
#XTXT: Confirm deactivation of space schema consumption dialog text
disableSpaceSchemaConsumptionDialogText=Detta Open SQL-schema kan förbruka data från ditt utrymmesschema. Om du stoppar förbrukningen kanske inte modeller som baseras på utrymmesschemat fungerar längre.
#XBUT: Confirmation button for the stop consuming space schema data dialog
stopConsuming=Stoppa förbrukning
#XTXT: Tooltip for data lake icon
dataLakeIconTooltip=Detta utrymme används för åtkomst till datasjö
#XFLD: Tooltip for data lake table entry
dataLakeTableEntryTooltip=Datasjö har aktiverats
#XFLD: used In-Memory linit reached tag
usedRamLimitReachedLabel=Minnesgräns har nåtts
#XFLD: Space storage Limit reached tag
storageLimitReachedLabel=Lagringsgräns har nåtts
#XFLD: Minimal Storage Limit Reached tag
minStorageLimitReachedLabel=Minsta lagringsgräns har nåtts
#XFLD: Space ram tag
ramLimitReachedLabel=Minnesgräns har nåtts
#XFLD: Minimal In-Memory Limit Reached tag
minRamLimitReachedLabel=Minsta minnesgräns har nåtts
#XFLD: Popover text after click on space storage limit warning
storageLimitReachedText=Du har nått allokerat lagringsgräns på {0} för utrymmet. Allokera mer lagring till utrymmet.
#XFLD: System storage tag
systemStorageLimitReachedLabel=Systemlagringsgräns har nåtts
#XMSG: Popover text after click on storage limit warning
systemStorageLimitReachedText=Systemlagringsgränsen på {0} har nåtts. Du kan inte allokera mer lagring till utrymmet nu.
#XMSG: Schema deletion warning
deleteSchemaConfirmation=Om detta Open SQL-schema raderas så raderas även alla lagrade objekt och underhållna associationer i schemat permanent. Fortsätta?
#XMSG: Success message after deleting a schema
schemaDeleteSuccess=Schema har raderats
#YMSE: Error while deleting schema.
schemaDeleteError=Schema kunde inte raderas.
#XMSG: Success message after update a schema
schemaUpdateSuccess=Schema har uppdaterats
#YMSE: Error while updating schema.
schemaUpdateError=Schema kunde inte uppdateras.
#XMSG: Hint when no password is shown in the dialog.
newPasswordHint=Du har fått ett lösenord för detta schema. Om du har glömt eller tappat bort ditt lösenord kan du begära ett nytt. Kom ihåg att kopiera eller spara det nya lösenordet.
#XMSG: Hint when showing the password in the dialog.
savePasswordHint=Kopiera ditt lösenord. Du behöver det för att upprätta en anslutning till detta schema. Om du har glömt ditt lösenord kan du öppna den här dialogrutan för att återställa det.
#XFLD,50: Hint on entering schema name in creation dialog
schemaNameHint=Namn kan inte ändras efter att schemat skapats.
#XFLD: Open SQL Schemas section sub headline
schemasSQL=Open SQL
#XFLD: Space schema section sub headline
schemasSpace=Utrymme
#XFLD: HDI Container section header
HDIContainers=HDI-containrar
#XTXT: Add HDI Containers button tooltip
addHDIContainers=Lägg till HDI-containrar
#XTXT: Remove HDI Containers button tooltip
removeHDIContainers=Ta bort HDI-containrar
#XFLD: Enable HDI Containers feature button
enableHDIContainersButton=Aktivera åtkomst
#YMSE: No data text for HDI Containers table
noDataHDIContainers=Inga HDI-containrar har lagts till.
#YMSE: No data text for Timedata section
noDataTimedata=Inga tidtabeller eller dimensioner har skapats.
#YMSE: No data text during runtime not available for Timedata section
runtimeNotAvailableNoDataTimedata=Tidtabeller och dimensioner kan inte läsas in eftersom körtidsdatabasen inte är tillgänglig.
#YMSE: No data text during runtime not available for HDI section
runtimeNotAvailableNoDataHDI=HDI-containrar kan inte läsas in eftersom körtidsdatabasen inte är tillgänglig.
#YMSE: Error while fetching HDI Containers
fetchHDIContainersError=HDI-containrar kunde inte hämtas. Försök igen senare.
#XFLD Table column header for HDI Container names
HDIContainerName=Namn på HDI-container
#XFLD: Enable HDI Containers feature dialog title
enableHDIContainersDialogTitle=Aktivera åtkomst
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText1New=Du kan aktivera SAP SQL Data Warehousing i din tenant för SAP Datasphere för att utbyta data mellan dina HDI-containrar och utrymmen för SAP Datasphere utan att behöva flytta data.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText2=Öppna ett supportärende genom att klicka på knappen nedan för att göra detta.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText3New=När ditt ärende har behandlats måste du generera en eller flera nya HDI-containrar i körtidsdatabasen för SAP Datasphere. Knappen Aktivera åtkomst ersätts då av knappen + i avsnittet för HDI-containrar för alla dina utrymmen för SAP Datasphere, och du kan lägga till dina containrar till ett utrymme.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText41=Behöver du mer information? Gå till %%0. För detaljerad information om vad som ska vara med i ärendet, se %%1.
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkText=SAP Help
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkTextSapNote3057059=SAP-not 3057059
#XBUT: Open Ticket Button Text
openTicket=Öppna ärende
#XBUT: Add Button Text
add=Lägg till
#XBUT: Next Button Text
next=Nästa
#XBUT: Edit Button Text
editUsers=Redigera
#XBUT: create user Button Text
createUser=Skapa
#XBUT: Update user Button Text
updateUser=Välj
#XTIT: Add HDI Containers Dialog Title
addHDIContainersDialogTitle=Lägg till icke allokerade HDI-containrar
#XFLD: No data text for add HDI container dialog
addHDIContainersDialogNoDataText=Inga icke allokerade containrar hittades. \n Den container du söker kan ske redan är allokerad till ett utrymme.
#YMSE: Error while loading assigned HDI Containers
loadAssignedHDIContainersError=Allokerade HDI-containrar kunde inte läsas in.
#YMSE: Error while loading assigned HDI Containers
loadUnassignedHDIContainersError=HDI-containrar kunde inte läsas in.
#XMSG: Success message
succeededToAddHDIContainer=HDI-container har lagts till
#XMSG: Success message
succeededToAddHDIContainerPlural=HDI-containrar har lagts till
#XMSG: Success message
succeededToDeleteHDIContainer=HDI-container har tagits bort
#XMSG: Success message
succeededToDeleteHDIContainerPlural=HDI-containrar har tagits bort
#XFLD: Time data section sub headline
timeDataSection=Tidtabeller och dimensioner
#XFLD: Schemas list entry read audit log enabled indicator
readIndicator=Läs
#XFLD: Schemas list entry change audit log enabled indicator
changeIndicator=Ändra
#XFLD: Remote sources section sub headline
allconnections=Anslutningsallokering
#XFLD: Remote sources section sub headline
localconnections=Lokala anslutningar
#XFLD: Remote sources section sub headline for open Connectors
openConnectors=SAP Open Connectors
#XFLD: User section sub headline
memberassignment=Medlemsallokering
#XFLD: User assignment section sub headline
userAssignment=Användarallokering
#XFLD: User section Access dropdown Member
member=Medlem
#XFLD: User assignment section column name
user=Användarnamn
#XTXT: Selected role count
selectedRoleToolbarText=Valda: {0}
#XTIT: Space detail section connections title
detailsSectionConnections=Anslutningar
#XTIT: Space detail section data access title
detailsSectionDataAccess=Schemaåtkomst
#XTIT: Space detail section time data title
detailsSectionGenerateData=Tidsdata
#XTIT: Space detail section members title
detailsSectionUsers=Element
#XTIT: Space detail section Users title
detailsSectionUsersTitle=Användare
#XTIT: Out of Storage
insufficientStoragePopoverTitle=Slut på lagringsutrymme
#XTIT: Storage distribution
storageDistributionPopoverTitle=Använd disklagring
#XTXT: Out of Storage popover text
insufficientStorageText=För att skapa ett nytt utrymme, minska det allokerade lagringsutrymmet för ett annat utrymme eller radera ett utrymme som du inte behöver länägre. Du kan öka systemets totala lagringsutrymme genom att välja Hantera plan.
#XMSG: Space id length warning
spaceIdLengthWarning=Maximum på {0} tecken har överskridits.
#XMSG: Space name length warning
spaceNameLengthWarning=Maximum på {0} tecken har överskridits.
#XMSG: Space id reserved prefix warning
spaceIdnWarningReservedPrefix=Använd inte prefixet {0} för att undvika eventuella konflikter.
#YMSE: Error while loading open SQL schemas
loadLocalSchemasError=Open SQL-scheman kunde inte läsas in.
#YMSE: Error while creating open SQL schema
createLocalSchemaError=Open SQL-schema kunde inte skapas.
#YMSE: Error while loading all remote connections
loadRemoteConnectionsError=Alla fjärranslutningar kunde inte läsas in.
#YMSE: Error while loading space details
loadSpaceDetailsError=Detaljer för utrymme kunde inte läsas in.
#YMSE: Error while deploying space details
deploySpaceDetailsError=Utrymme kunde inte distribueras.
#YMSE: Error while copying space details
copySpaceDetailsError=Utrymme kunde inte kopieras.
#YMSE: Error while loading storage data
loadStorageDataError=Lagringsdata kunde tyvärr inte läsas in.
#YMSE: Error while loading all users
loadAllUsersError=Alla användare kunde inte läsas in.
#YMSE: Failed to reset password
resetPasswordError=Lösenord kunde inte återställas.
#XMSG: Success message after updating space
resetPasswordSuccessMessage=Nytt lösenord har ställts in för schema
#YMSE: DP Agent-name too long
DBAgentNameError=Namn på datahämtningsagent är för långt.
#YMSE: Schema-name not valid.
schemaNameError=Namn på schema är ogiltigt.
#YMSE: User name not valid.
UserNameError=Användarnamn är ogiltigt.
#XTIT: Consumption by Storage Type
consumptionStorageTypeText=Förbrukning per lagringstyp
#XTIT: Consumption by Schema
consumptionSchemaText=Förbrukning per schema
#XTIT: Overall Consumption by schema
overallConsumptionSchemaText=Total tabellförbrukning per schema
#XTIT: Overall Consumption by Table Type
overallConsumptionTableTypeText=Total förbrukning per tabelltyp
#XTIT: Tables
tableDetailsText=Tabelldetaljer
#XTIT: Table Storage Consumption
tableStorageConsumptionText=Tabellagringsförbrukning
#XFLD: Table Type label
tableTypeLabel=Tabelltyp
#XFLD: Schema label
schemaLabel=Schema
#XFLD: reset table tooltip
resetTable=Återställ tabell
#XFLD: In-Memory label in space monitor
inMemoryLabel=Minne
#XFLD: Disk label in space monitor
diskLabel=Disk
#XFLD: Yes
yesLabel=Ja
#XFLD: No
noLabel=Nej
#XFLD: switch label: Want the data in this space to be consumable by default?
allowConsumptionByDefault=Ska data i detta utrymme kunna användas som standard?
#XFLD: Business Name
businessNameLabel=Affärsnamn
#XFLD: Refresh
refresh=Uppdatera
#XMSG: No filter results title
noFilterResultsTitle=Dina filterinställningar verkar inte visa några data.
#XMSG: No filter results message
noFilterResultsMsg=Pröva att precisera dina filterinställningar och om du fortfarande inte ser några data, skapa några tabeller i Data Builder. När de förbrukar lagringsutrymme kan du övervaka dem här.
#XMSG: Space monitoring Hana state red title
spaceMonitorHanaRedTitle=Körningstidsdatabasen är inte tillgänglig.
#XMSG: Space monitoring Hana state red message
spaceMonitorHanaRedMsg=Eftersom körningstidsdatabasen inte är tillgänglig har vissa funktioner inaktiverats och information kan inte visas på den här sidan.
#YMSE: Error while creating space schema user
createSpaceSchemaUserError=Användare för utrymmesschema kunde inte skapas.
#YMSE: Error User name already exists
userAlreadyExistsError=Användarnamn finns redan.
#YMSE: Error Authentication failed
authenticationFailedError=Autentisering misslyckades.
#XMSG: Message strip message when a schema user is locked
userLockedMessageStrip=Användaren är spärrad på grund av för många misslyckade inloggningsförsök. Begär ett nytt lösenord för att låsa upp användaren.
#XMSG: Success message after reset and unlock schema user
resetUnlockPasswordSuccessMessage=Nytt lösenord har ställts in och användaren har låsts upp
#XMSG: user is locked message
userLockedMessage=Användaren är spärrad.
#XCOL: Users table-view column Role
spaceRole=Roll
#XCOL: Users table-view column Scoped Role
spaceScopedRole=Omfattningsroll
#XCOL: Users table-view column Space Admin
spaceAdmin=Utrymmesadministratör
#XFLD: User section dropdown value Viewer
viewer=Viewer
#XFLD: User section dropdown value Modeler
modeler=Modellerare
#XFLD: User section dropdown value Data Integrator
dataIntegrator=Data Integrator
#XFLD: User section dropdown value Space Administrator
spaceAdministrator=Utrymmesadministratör
#XMSG: Success message -  Space Role updated successfully.
updateUserSpaceRoleSuccess=Roll för utrymme har uppdaterats
#XMSG: Error message - Failed to update Space Role.
updateUserSpaceRoleError=Roll för utrymme kunde inte uppdateras.
#XFLD:
databaseUserNameSuffix=Suffix för databasanvändarnamn
#XTXT: Space Schema password text
spaceSchemaPasswordText=För att skapa en anslutning till detta schema, kopiera ditt lösenord. Om du har glömt ditt lösenord kan du alltid begära ett nytt.
#XFLD: Cloud Platform label in space schema dialog
cloudPlatformLabel=Molnplattform
#XTXT: Cloud Platform text in space schema dialog
cloudPlatformText=För att skapa åtkomst via denna användare, aktivera förbrukningen och kopiera inloggningsuppgifterna. Om du bara kan kopiera inloggningsuppgifterna utan lösenord, se till att du lägger till lösenordet sen.
#XFLD: enable consumption in CloudPlatform label in space schema dialog
enableConsumptionInCloudPlatformLabel=Aktivera förbrukning på molnplattform
#XFLD: Credentials for User-Provided Service label in space schema dialog
cloudPlatformCredentialsLabel=Inloggningsuppgifter för tjänst tillhandahållen av användare:
#XFLD: Credentials for User-Provided Service (Without Password) label in space schema dialog
cloudPlatformCredentialsWithoutPasswordLabel=Inloggningsuppgifter för tjänst tillhandahållen av användare (utan lösenord):
#XFLD: Copy Credentials label in space schema dialog
copyCredentialsLabel=Kopiera inloggningsuppgifter utan lösenord
#XFLD: Copy Credentials label in space schema dialog
copyFullCredentialsLabel=Kopiera fullständiga inloggningsuppgifter
#XFLD: Copy Password label in space schema dialog
copyPasswordLabel=Kopiera lösenord
#XMSG: Credentials copied to clipboard
credentialsCopiedMessage=Inloggningsuppgifter har kopierats till urklipp
#XMSG: Password copied to clipboard
passwordCopiedMessage=Lösenord har kopierats till urklipp
#XMSG: create database user dialog title
createDatabaseUserDialogTitle=Skapa databasanvändare
#XMSG: Database Users section title
databaseUsers=Databasanvändare
#XMSG: Database User Details dialog title
databaseUserDetailsDialogTitle=Detaljer för databasanvändare
#XFLD: database user read audit log
databaseUserAuditLogRead=Aktivera revisionslogg för läsoperationer och behåll loggar i
#XFLD: database user change audit log
databaseUserAuditLogChange=Aktivera revisionslogg för ändringsoperationer och behåll loggar i
#XMSG: Cloud Platform Access
cloudPlatformAccess=Åtkomst till molnplattform
#XFLD: Enable Consumption in Cloud Platform hint
enableConsumptionCloudPlatformHint=Skapa åtkomst till din HDI-container (HANA Deployment Infrastructure) via denna databasanvändare. För att HDI-containern ska kunna anslutas måste SQL-modellering ha aktiverats.
#XFLD: Enable HDI Consumption
enableConsumptionCloudPlatform=Aktivera HDI-förbrukning
#XFLD: Enable Consumption hint
enableConsumptionHint=Vill du att data i ditt utrymma ska kunna användas av andra verktyg och appar?
#XFLD: Enable Consumption
enableConsumption=Aktivera SQL-förbrukning
#XFLD: Enable Modeling
enableModeling=Aktivera SQL-modellering
#XMSG: Privileges for Data Modeling
privilegesModeling=Dataintag
#XMSG: Privileges for Data Consumption
privilegesConsumption=Dataförbrukning för externa verktyg
#XFLD: SQL Modeling
sqlModeling=SQL-modellering
#XFLD: SQL Consumption
sqlConsumption=SQL-förbrukning
#XFLD: enabled
enabled=Aktiverad
#XFLD: disabled
disabled=Inaktiverad
#XFLD: Edit Privileges
editPrivileges=Redigera behörigheter
#XFLD: Open Database Explorer
openDBX=Öppna Databasutforskaren
#XFLD: create database user hint
databaseCreateHint=Observera att det inte går att ändra användarnamnet när du har sparat.
#XFLD: Internal Schema Name
internalSchemaName=Internt schemanamn
#YMSE: Failed to load database users
loadDatabaseUserError=Databasanvändare kunde inte läsas in
#YMSE: Failed to delete database user
deleteDatabaseUsersError=Databasanvändare kunde inte raderas
#XMSG: Success message after deleting database user
deleteDatabaseUserSuccess=Databasanvändare har raderats
#XMSG: Success message after deleting database users
deleteDatabaseUsersSuccess=Databasanvändare har raderats
#XMSG: Success message after creating database user
createDatabaseUserSuccessMessage=Databasanvändare har skapats
#YMSE: Failed to create database user
createDatabaseUserError=Databasanvändare kunde inte skapas
#XMSG: Success message after updating database user
updateDatabaseUserSuccessMessage=Databasanvändare har uppdaterats
#YMSE: Failed to update database user
updateDatabaseUserError=Databasanvändare kunde inte uppdateras
#XFLD: HDI Consumption
hdiConsumption=HDI-förbrukning
#XTIT: Space detail section Database Access title
detailsSectionDatabaseAccess=Databasåtkomst
#XTXT: Database users consumption hint
databaseUsersConsumptionHint=Ställ in att dina utrymmesdata ska kunna förbrukas som standard. Modellerna i builders kommer automatiskt att tillåta förbrukning av data.
#XFLD: Default Consumption of Space Data:
defaultConsumptionOfSpaceData=Standardförbrukning av utrymmesdata:
#XFLD: Database User Name
databaseUserName=Databasanvändarnamn
#XMSG: Database User creation validation error message
databaseUserValidationError=Några fält verkar vara ogiltiga. Kontrollera obligatoriska fält för att fortsätta.
#XMSG: SQL modeling can’t be enabled since this user has been migrated.
migratedDatabaseUserHint=Dataintag kan inte aktiveras eftersom denna användare har migrerats.
#XBUT: Remove Button Text
remove=Ta bort
#XBUT: Remove Spaces Button Text
removeSpaces=Ta bort utrymmen
#XBUT: Remove Objects Button Text
removeObjects=Ta bort objekt
#XMSG: No members have been added yet.
noMembersAssigned=Inga element har lagts till än.
#XMSG: No users have been added yet.
noUsersAssigned=Inga användare har lagts till än.
#XMSG: No database users have been created or your filter is not showing any data.
noDatabaseUsers=Inga databasanvändare har lagts upp eller ditt filter visar inga data.
#XMSG: Please enter a user name.
noDatabaseUsername=Ange ett användarnamn.
#XMSG: The user name is too long. Please use a shorter one.
databaseUsernameTooLong=Användarnamn är för långt. Använd ett kortare namn.
#XMSG: No privileges have been enabled and this database user will have limited functionality. Do you still want to continue?
noPrivilegesConfirmationMessage=Inga behörigheter har aktiverats och denna databasanvändare kommer att ha begränsade funktioner. Fortsätta?
#XMSG: To enable audit logs for change operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogChangeMessage=För att revisionsloggar ska kunna aktiveras för ändringsoperationer måste även dataintag aktiveras. Vill du göra detta?
#XMSG: To enable audit logs for read operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogReadMessage=För att revisionsloggar ska kunna aktiveras för läsoperationer måste även dataintag aktiveras. Vill du göra detta?
#XMSG: To enable HDI consumption data ingestion and data consumption needs to be enabled. Do you want to enable all?
enableHDIConsumptionMessage=För att HDI-förbrukning ska kunna aktiveras måste även dataintag och dataförbrukning aktiveras. Vill du göra detta?
#XMSG:
databaseUserPasswordText=För att skapa en anslutning till denna databasanvändare, kopiera ditt lösenord. Om du glömmer ditt lösenord kan du alltid begära ett nytt.
#XTIT: Space detail section members title
detailsSectionMembers=Element
#XMSG: New password set
newPasswordSet=Nytt lösenord har ställts in
#XFLD: Data Ingestion
dataIngestion=Dataintag
#XFLD: Data Consumption
dataConsumption=Dataförbrukning
#XFLD: Privileges
privileges=Behörigheter
#XFLD: Enable Data ingestion
enableDataIngestion=Aktivera dataintag
#XMSG: Log the read and change operations for data ingestion.
databaseUserAuditHint=Protokollför läs- och ändringsoperationer för dataintag.
#XMSG: Make your space data available in your HDI containers.
databaseUserHDIHint=Gör dina utrymmesdata tillgängliga i dina HDI-containrar.
#XFLD: Enable Data consumption
enableDataConsumption=Aktivera dataförbrukning
#XMSG: Allow other apps or tools to consume your space data.
enableDataConsumptionHint=Tillåt att andra appar eller verktyg förbrukar dina utrymmesdata.
#XMSG: To set up access via this schema user copy the credentials to your user-provided service. In case you can only copy the credentials without a password make sure to add the password later.
databaseUserHDIConsumptionHint=För att skapa åtkomst via denna databasanvändare, kopiera inloggningsuppgifterna till din tjänst. Om du bara kan kopiera inloggningsuppgifterna utan lösenord, se till att du lägger till lösenordet sen.
#XFLD: Data Flow Runtime Capacity label in space overview
dataFlowRuntimeCapacity=Körtidskapacitet för dataflöde ({0}:{1} timmar av {2} timmar)
#XFLD: Could not load Data Flow Runtime Capacity
dataFlowRuntimeCapacityError=Körtidskapacitet för dataflöde kunde inte läsas in
#XMSG: User can grant data consumption to other users.
databaseUserConsumptionWithGrantOptionHint=Användare kan tilldela dataförbrukning till andra användare.
#XFLD: Enable data consumption with grant option
enableConsumptionWithGrantOption=Aktivera dataförbrukning med tilldelningsalternativ
#XMSG: To enable data consumption with grant option data consumption needs to be enabled. Do you want to enable both?
enableConsumptionWithGrantMessage=Dataförbrukning måste vara aktiverad för att aktivera dataförbrukning med tilldelningsalternativ. Ska båda aktiveras?
#XFLD: Enable Automated Predictive Library (APL) and Predictive Analysis Library (PAL)
allowUsingScriptServerFunctionality=Aktivera Automated Predictive Library (APL) och Predictive Analysis Library (PAL)
#XMSG: User can use SAP HANA Cloud embedded machine learning functions.
allowUsingScriptServerFunctionalityHint=Användare kan använda integrerade maskininlärningsfunktioner i SAP HANA Cloud.
#XFLD: Password Policy
passwordPolicy=Lösenordspolicy
#XMSG: Password Policy
passwordPolicyHint=Här kan du aktivera eller inaktivera konfigurerad lösenordspolicy.
#XFLD: Enable Password Policy
enablePasswordPolicy=Aktivera lösenordspolicy
#XMSG: Read Access to the Space Schema
readAccessTitle=Läsåtkomst till utrymmesschema
#XMSG: read access hint
readAccessHint=Tillåt att databasanvändaren ansluter externa verktyg till utrymmesschemat och läser vyer som är exponerade för förbrukning.
#XFLD: Space Schema
spaceSchema=Utrymmesschema
#XFLD: Enable Read Access (SQL)
enableReadAccess=Aktivera läsåtkomst (SQL)
#XMSG: Allow the user to grant read access to other users.
withGrantOptionHint=Tillåt att användaren ger läsåtkomst till andra användare.
#XFLD: With Grant Option
withGrantOption=Med tilldelningsalternativ
#XMSG: Make your space data available in your HDI containers.
enableHDIConsumptionHint=Gör dina utrymmesdata tillgängliga i dina HDI-containrar.
#XFLD: Enable HDI Consumption
enableHDIConsumption=Aktivera HDI-förbrukning
#XMSG: Write Access to the User’s Open SQL Schema
writeAccessTitle=Skrivåtkomst till användarens Open SQL-schema
#XMSG: write access hint
writeAccessHint=Tillåt att databasanvändaren ansluter externa verktyg till användarens Open SQL-schema för att skapa dataentiteter och importerar data för användning i utrymmet.
#XFLD: Open SQL Schema
openSQLSchema=Open SQL-schema
#XFLD: Enable Write Access (SQL DDL & DML)
enableWriteAccess=Aktivera skrivåtkomst (SQL, DDL och DML)
#XMSG: audit hint
auditHint=Protokollför läs- och ändringsoperationer i Open SQL-schemat.
#XMSG: data consumption hint
dataConsumptionHint=Exponera som standard alla nya vyer i utrymmet för förbrukning. Modellerare kan åsidosätta denna inställning för enskilda vyer via indikatorn "Exponera för förbrukning" i sidopanelen för vyutmatning. Du kan även välja vilka format som vyerna ska exponeras i.
#XFLD: Expose for Consumption by Default
exposeDataConsumption=Exponera för förbrukning som standard
#XMSG: database users hint consumption hint
databaseUsersHint2New=Skapa databasanvändare för att ansluta externa verktyg till SAP Datasphere. Ställ in behörigheter för att låta användare läsa utrymmesdata och skapa dataentiteter (DDL) och importera data (DML) att använda i utrymmet.
#XFLD: Read
read=Läs
#XFLD: Read (HDI)
readHDI=Läs (HDI)
#XFLD: Write
write=Skriv
#XMSG: HDI Containers Hint
HDIContainersHint2=Aktivera åtkomst till dina containrar för SAP HANA Deployment Infrastructure (HDI) i ditt utrymme. Modellerare kan använda HDI-artefakter som källa för vyer och HDI-klienter kan få åtkomst till dina utrymmesdata.
#XMSG: Open info dialog
openDatabaseUserInfoDialog=Öppna informationsdialogen
#XMSG: Database user is locked. Open info dialog to unlock
openLockedDatabaseUserInfoDialog=Databasanvändare är spärrad. Öppna dialogen för att häva spärren
#XFLD: Table
table=Tabell
#XFLD: Space section partner configuration title
partnerConnectionConfigurationSectionTitle=Partneranslutning
#XFLD: Space section partner configuration subtitle
partnerConnectionConfigurationSectionSubTitle=Konfiguration för partneranslutning
#XFLD: Space section partner configuration hint
partnerConnectionConfigurationSectionHint=Definiera din egen panel för partneranslutning genom att lägga till din iFrame-URL och symbol. Konfigurationen är endast tillgänglig för denna tenant.
#XFLD: Table Name Field
partnerConnectionConfigurationName=Panelnamn
#XFLD: Table iFrame URL field
partnerConnectionConfigurationiFrameURL=iFrame-URL
#XFLD: Table iFrame Post Message Origin field
partnerConnectionConfigurationiFramePostMessageOrigin=Ursprung för 'post'-meddelande för iFrame
#XFLD: Table icon/image name
partnerConnectionConfigurationIcon=Symbol
#XMSG: Table no result found
partnerConnectionConfigurationNotfound=Inga konfigurationer för partneranslutning hittades.
#XMSG: Partner Connection Confgurations are not available
partnerConnectionConfigurationNotAvailable=Konfigurationer för partneranslutningar kan inte visas när körningstidsdatabasen inte är tillgänglig.
#XFLD: Dialog Create Partner Connection Configuration
partnerConnectionConfigurationDialogTitleAdd=Skapa konfiguration för partneranslutning
#XFLD: Dialog Create Partner Connection Configuration Upload Icon
partnerConnectionConfigurationButtonUloadPartnerIcon=Läs in symbol
#XFLD: Dialog Create Partner Connection Configuration Select
partnerConnectionConfigurationSelectIcon=Välj (maximal storlek på 200KB)
#XFLD: Dialog Create Partner Connection Configuration Enter a name
partnerConnectionConfigurationNamePlaceholder=Exempel på partnerpanel
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL
partnerConnectionConfigurationiFrameURLPlaceholder=http://www.example.com
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL Check
partnerConnectionConfigurationiFramePostMessageOriginPlaceholder=.example.com
#XFLD: Dialog Create Partner Connection Configuration Browse
partnerConnectionConfigurationBrowseButton=Bläddra
#XFLD: Create Partner Connection Configuration Msg
partnerConnectionConfigurationUploadMsg=Konfiguration för partneranslutning har skapats.
#XMSG: Error while deleting Partner Connection configuration(s)
partnerConnectionConfigurationDeleteError=Fel vid radering av konfiguration för partneranslutning.
#XMSG: Partner Connection Configuration has been successfully deleted
partnerConnectionConfigurationDeleteSuccess=Konfiguration för partneranslutning har raderats.
#XMSG: Error while retrieving Partner Connection Configurations
partnerConnectionConfigurationLoadError=Fel vid hämtning av konfigurationer för partneranslutning.
#XMSG: Error fileSizeLimitExceeded
partnerConnectionConfigurationfileSizeLimitExceeded=Fil kunde inte läsas in eftersom den överskrider maximal storlek på 200KB.
#XBUT: Create Partner Connection Configuration
partnerConnectionConfigurationButtonCreate=Skapa konfiguration för partneranslutning
#XBUT: Delete Partner Connection Configuration
partnerConnectionConfigurationButtonDelete=Radera konfiguration för partneranslutning.
#XMSG: Partner Tile Creation Failure
upsertPartnerTileFailed=Partnerpanel kunde inte skapas.
#XMSG: Partner Tile Deletion Failure
deletePartnerTileFailed=Partnerpanel kunde inte raderas.
#XMSG: HANA cloud connector settings reset failure
resetHANASCCSettingsFailed=Återställning av kundens molnkonnektorinställningar för SAP HANA misslyckades
#XFLD: Workload Class
workloadClass=Arbetsmängdsklass
#XFLD: Workload Management
workloadManagement=Arbetsmängdshantering
#XFLD: Priority
workloadClassPriority=Prioritet
#XMSG:
workloadManagementPriorityHint=Du kan ange prioritering för detta utrymme vid databasfråga. Ange ett värde från 1 (lägsta prioritet) till 8 (högsta prioritet). När utrymmen konkurrerar om tillgängliga trådar körs utrymmen med högre prioritet före de med lägre prioritet.
#XMSG:
workloadClassPriorityHint=Du kan ange prioritet för utrymmet från 0 (lägsta) till 8 (högsta). Instruktioner för ett utrymme med hög prioritet utförs innan instruktioner för andra utrymmen med lägre prioritet. Standardprioritet är 5. Värdet 9 har reserverats för systemoperationer och är inte tillgängligt för ett utrymme.
#XFLD: Statement Limits
workloadclassStatementLimits=Instruktionsbegränsningar
#XFLD: Workload Configuration
workloadConfiguration=Arbetsmängdskonfiguration
#XMSG:
workloadClassStatementLimitsHint=Du kan ange maximalt antal (eller procentsats) trådar och GB minne som instruktioner som körs samtidigt i utrymmet kan använda. Du kan ange ett värde eller en procentsats mellan 0 (ingen gräns) och totalt minne och totalt antal trådar som är tillgängligt i tenanten. Om du anger en gräns för trådar, tänk på att det kan sänka prestandan. \n\n Om du anger en gräns för minne utförs inte instruktioner som når den gränsen.
#XMSG:
workloadClassStatementLimitsDescription=Standardkonfigurationen har generösa resursgränser, och förhindrar samtidigt att enskilda utrymmen överbelastar systemet.
#XMSG:
workloadClassStatementLimitCustomDescription=Du kan ange maxgränser för trådar och minne som instruktioner som körs samtidigt i utrymmet kan använda.
#XMSG:
totalStatementThreadLimitHelpText=För lågt angiven trådgräns kan påverka instruktionsprestanda medan höga värden eller 0 kan göra att utrymmet använder alla tillgängliga systemtrådar.
#XMSG:
totalStatementMemoryLimitHelpText=För lågt angiven minnesgräns kan orsaka minnesbrist medan höga värden eller 0 kan göra att utrymmet använder allt tillgängligt systemminne.
#XMSG:
totalStatementThreadLimitHelpTextRestricted=Ange en procentsats mellan 1 och 70 (eller motsvarande siffra) för det totala antalet tillgängliga trådar i tenanten. För lågt angiven trådgräns kan påverka instruktionsprestanda medan höga värden kan påverka instruktionsprestanda i andra utrymmen.
#XMSG:
totalStatementThreadLimitHelpTextDynamic=Ange en procentsats mellan 1 % och {0} % (eller motsvarande siffra) för det totala antalet tillgängliga trådar i tenanten. För lågt angiven trådgräns kan påverka instruktionsprestanda medan höga värden kan påverka instruktionsprestanda i andra utrymmen.
#XMSG:
totalStatementMemoryLimitHelpTextNew=Ange ett värde eller en procentsats mellan 0 (ingen gräns) och total mängd tillgängligt minne i tenanten. För lågt angiven minnesgräns kan påverka instruktionsprestanda medan höga värden kan påverka instruktionsprestanda i andra utrymmen.
#XFLD: Total Statement Thread Limit
totalStatementThreadLimit=Total trådgräns för instruktion
#XFLD: Segmented button label
workloadclassPercentage=%
#XFLD: Segmented button label
workloadclassGigaByte=GB
#XFLD, 80: Segmented button label
workloadclassThreads=Trådar
#XFLD: Total Statement Memory Limit
totalStatementMemoryLimit=Total minnesgräns för instruktion
#YMSE: Error while loading customer hana info
loadCustomerHanaInfoError=SAP HANA-info för kund kunde inte läsas in.
#XMSG:
minimumLimitReached=Minsta gräns har nåtts.
#XMSG:
maximumLimitReached=Maxgräns har nåtts.
#XMSG: Name Taken for Technical Name
technical-name-taken=En anslutning med angivet tekniskt namn finns redan. Ange ett annat namn.
#XMSG: Name Too long for Technical Name
technical-name-too-long=Angivet tekniskt namn överskrider 40 tecken. Ange ett namn med färre tecken.
#XMSG: Technical name field empty
technical-name-field-empty=Ange ett tekniskt namn.
#XMSG: Generic error when user enters invalid characters for a field
field-invalid-characters=Du kan endast använda bokstäver (a-z), siffror (0-9) och understreck (_) i namnet.
#XMSG: Generic error when user enters invalid initial characters for a field
field-underscore-trim=Namn som anges får inte börja eller sluta med understreck (_).
#XFLD: Checkbox label for Enable Statement Limits
enableStatementLimits=Aktivera instruktionsbegränsningar
#XFLD: Connection SubSection Settings Title in space details
connectionSettings=Inställningar
#XMSG: Connections tool hint in Space details section
connectionsToolHint=Skapa eller redigera anslutningar genom att öppna appen Anslutningar i sidonavigeringen eller klicka här:
#XBUT: Connection list Open Connection Tool Button
button-connectiontool=Gå till Anslutningar
#XFLD: Not deployed label on space tile
notDeployedLabel=Utrymme har inte distribuerats än.
#XFLD: Not deployed additional text on space tile
notDeployedText=Distribuera utrymmet.
#XFLD: Corrupt space label on space tile
corruptSpace=Något gick fel.
#XFLD: Corrupt space additional text on space tile
corruptSpaceHint=Pröva att distribuera på nytt eller kontakta support
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Data för revisionslogg
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Administrativa data
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Andra data
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Data i utrymmen
#XMSG: Unlock Space confirmation text
unlockSpaceConfirmation=Vill du häva spärren för utrymmet?
#XMSG: Lock Space confirmation text
lockSpaceConfirmation=Vill du spärra utrymmet?
#XFLD: Lock
lock=Spärra
#XFLD: Unlock
unlock=Häv spärr
#XFLD: Locking
locking=Spärrar
#XMSG: Success message after locking space
lockSpaceSuccessMsg=Utrymme spärrat
#XMSG: Success message after unlocking space
unlockSpaceSuccessMsg=Spärr hävd för utrymme
#XMSG: Success message after locking spaces
lockSpacesSuccessMsg=Utrymmen spärrade
#XMSG: Success message after unlocking spaces
unlockSpacesSuccessMsg=Spärr hävd för utrymmen
#YMSE: Error while locking a space
lockSpaceError=Utrymmet kan inte spärras.
#YMSE: Error while unlocking a space
unlockSpaceError=Spärr kan inte hävas för utrymme.
#XTIT: popup title Warning
confirmationWarningTitle=Varning
#XMSG: Message strip message when a space was locked manually
manualLockedMessage=Utrymmet har spärrats manuellt.
#XMSG: Message strip message when a space was locked due to the amount of generated audit logs
auditLogLockedMessage=Utrymmet har spärrats av systemet eftersom revisionsloggar tar många GB diskutrymme i anspråk.
#XMSG: Message strip message when a space was locked because the assigned quota was exceeded.
quotaLockedMessage=Utrymmet har spärrats av systemet eftersom det överskrider allokerad mängd minne eller disklagring.
#XMSG: Unlock Spaces confirmation text
unlockSpacesConfirmation=Vill du häva spärren för valda utrymmen?
#XMSG: Lock Spaces confirmation text
lockSpacesConfirmation=Vill du spärra valda utrymmen?
#XTIT: Scoped role editor dialog title
scopedRoleEditorDialogTitle=Editor för omfattningsroll
#XTIT: ECN Management title
ecnManagementTitle=Hantering av utrymmen och elastiska beräkningsnoder
#XFLD: ECNs
ecns=Elastiska beräkningsnoder
#XFLD: ECN phase Ready
ecnReady=Klar
#XFLD: ECN phase Running
ecnRunning=Körs
#XFLD: ECN phase Initial
ecnInitial=Inte klar
#XFLD: ECN phase Starting
ecnStarting=Startar
#XFLD: ECN phase Starting Failed
ecnStartingFailed=Start misslyckades
#XFLD: ECN phase Stopping
ecnStopping=Stoppas
#XFLD: ECN phase Stopping Failed
ecnStoppingFailed=Stopp misslyckades
#XBTN: Assign Button
assign=Allokera utrymmen
#XBTN: Start Header-Button
start=Starta
#XBTN: Update Header-Button
repair=Uppdatera
#XBTN: Stop Header-Button
stop=Stoppa
#XFLD: ECN hours remaining
ecnHoursRemaining=1 000 timmar kvar
#XFLD: ECN block-hours remaining
ecnBlockHoursRemaining={0} blocktimmar kvar
#XFLD: ECN block-hour remaining
ecnBlockHourRemaining={0} blocktimme kvar
#XTIT: ECN creation dialog title
ecnCreationDialogTitle=Skapa elastisk beräkningsnod
#XTIT: ECN edit dialog title
ecnEditDialogTitle=Redigera elastisk beräkningsnod
#XTIT: ECN deletion dialog title
ecnDeletionDialogTitle=Radera elastisk beräkningsnod
#XTIT: ECN Space assignment dialog title
ecnSpaceAssignmentDialogTitle=Allokera utrymmen
#XFLD: ECN ID
ECNIDLabel=Elastisk beräkningsnod
#XTXT: Selected toolbar text
selectedToolbarText=Valda: {0}
#XTIT: Elastic Compute Nodes
ECNslong=Elastiska beräkningsnoder
#XTIT: Object assignment - Table header: Objects available
objectsAvailable=Antal objekt
#XTIT: Object assignment - Dialog header text
selectObjects=Välj de utrymmen och objekt som du vill allokera till din elastiska beräkningsnod:
#XTIT: Object assignment - Table header title: Objects
objects=Objekt
#XTIT: Object assignment - Table header: Type
type=Typ
#XMSG: Message strip delete database user audit warning
deleteDatabaseUserWarningMessageStrip=Tänk på att om du raderar en databasanvändare så raderas alla genererade revisionsloggsuppgifter. Om du vill behålla revisionsloggarna bör du exportera dem innan du raderar databasanvändaren.
#XMSG: Message strip delete HDI container audit logs impact
deleteHDIContainerWarningMessageStrip=Tänk på att om du avallokerar en HDI-container från utrymmet så raderas alla genererade revisionsloggsuppgifter. Om du vill behålla revisionsloggarna bör du exportera dem innan du avallokerar HDI-containern.
#XTXT: All audit logs
allAuditLogs=Alla revisionsloggsuppgifter som genererats för utrymmet
#XMSG: Message strip disable audit warning
disableAuditWarningMessageStrip=Tänk på att om du inaktiverar en revisionsriktlinje (läs- eller ändringsoperationer) så raderas alla dess revisionsloggsuppgifter. Om du vill behålla uppgifterna bör du exportera dem innan du inaktiverar riktlinjen.
#XTIT: No Spaces assigned to ECN Illustration title
noSpacesECNTitle=Inga utrymmen eller objekt har allokerats än
#XTIT: No Spaces assigned to ECN Illustration description
noSpacesECNDescription=Allokera utrymme eller objekt till din elastiska beräkningsnod för att börja arbeta med den.
#XTIT: No Spaces Illustration title
noSpacesTitle=Inget utrymme har skapats än
#XTIT: No Spaces Illustration description
noSpacesDescription=Skapa ett utrymme för att börja hämta data.
#XTIT: No Spaces Illustration title
noDeletedSpacesTitle=Papperskorgen är tom
#XTIT: No Spaces Illustration description
noDeletedSpacesDescription=Du kan återskapa dina raderade utrymmen härifrån.
#XTXT:  {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDatabaseUsers=När utrymmet har distribuerats raderas följande databasanvändare och kan inte återställas: {0}
#XTIT: Delete database users
deleteDatabaseUsersTitle=Radera databasanvändare
#YMSE: Error if ecn id already exists
ecnIDInputDuplicateError=ID finns redan.
#YMSE: Error if ecn id contains invalid characters
ecnIDInputInvalidError=Använd endast små bokstäverna a–z och sifforna 0–9.
#YMSE: Error if ecn id is too short
ecnIDInputTooShortError=ID måste vara minst {0} tecken lång.
#XMSG: ecn id length warning
ecnIdLengthWarning=Maximum på {0} tecken har överskridits.
#XFLD: open System Monitor
systemMonitor=Systemmonitor
#XFLD: open ECN schedule dialog menu entry
schedule=Planera in
#XFLD: open create ECN schedule dialog
createSchedule=Skapa schema
#XFLD: open change ECN schedule dialog
changeSchedule=Redigera schema
#XFLD: open delete ECN schedule dialog
deleteSchedule=Radera schema
#XFLD: open self assign ECN schedule dialog
selfAssignSchedule=Allokera schema till mig
#XFLD: open pause ECN schedule dialog
pauseSchedule=Pausa schema
#XFLD: open resume ECN schedule dialog
resumeSchedule=Återuppta schema
#XFLD: View Logs
viewLogs=Visa protokoll
#XFLD: Compute Blocks
computeBlocks=Beräkningsblock
#XFLD: Memory label in ECN creation dialog
ecnMemory=Minne (GB)
#XFLD: Storage label in ECN creation dialog
ecnStorage=Lagring (GB)
#XFLD: Number of CPU label in ECN creation dialog
ecnCPUCount=Antal processorer
#XFLD: ECN updated by label
changedBy=Ändring av
#XFLD: ECN updated on label
changedOn=Ändring den
#XMSG: Success message after creating a Elastic Compute Node
createEcnSuccessMessage=Elastisk beräkningsnod har skapats
#YMSE: Error while creating a Elastic Compute Node
createEcnError=Elastisk beräkningsnod kunde inte skapas
#XMSG: Success message after updating a Elastic Compute Node
updateEcnSuccessMessage=Elastisk beräkningsnod har uppdaterats
#XMSG: Error while updating a Elastic Compute Node
udpateEcnError=Elastisk beräkningsnod kunde inte uppdateras
#XMSG: Success message after deleting a Elastic Compute Node
deleteEcnSuccessMessage=Elastisk beräkningsnod har raderats
#YMSE: Error while deleting a Elastic Compute Node
deleteEcnError=Elastisk beräkningsnod kunde inte raderas
#XMSG: Success message after starting a Elastic Compute Node
startingEcnSuccessMessage=Startar elastisk beräkningsnod
#XMSG: Success message after stopping a Elastic Compute Node
stoppingEcnSuccessMessage=Stoppar elastisk beräkningsnod
#YMSE: Error while starting a Elastic Compute Node
startingEcnError=Elastisk beräkningsnod kunde inte startas
#YMSE: Error while stopping a Elastic Compute Node
stoppingEcnError=Elastisk beräkningsnod kunde inte stoppas
#XBUT: Add Object button for an ECN
assignObjects=Lägg till objekt
#XFLD: Enable auto object assignment label
enableAutoObjAssignment=Lägg till alla objekt automatiskt
#XFLD: object type label to be assigned
objectTypeLabel=Typ (semantisk användning)
#XFLD: assigned object type label
assignedObjectTypeLabel=Typ
#XFLD: technical name label
TechnicalNameLabel=Tekniskt namn
#XFLD: Object selecttion ECN label
objectSelectionECNLabel=Välj de objekt som du vill lägga till i elastisk beräkningsnod.
#XTIT: Add objects dialog title
assignObjectsTitle=Allokera objekt för
#XFLD: object label with object count
objectLabel=Objekt
#XMSG: No objects available to add message.
noObjectsToAssign=Inga objekt tillgängliga att allokera.
#XMSG: No objects assigned message.
noAssignedObjects=Inga objekt allokerade.
#XTIT: Remove objects popup title
removeObjectsOrSpacesConfirmationTitle=Varning
#XTIT: Delete ECN popup button and title
deleteECNConfirmationButtonAndTitle=Radera
#XMSG: Remove objects popup text
removeObjectsConfirmation=Vill du ta bort valda objekt?
#XMSG: Remove spaces popup text
removeSpacesConfirmation=Vill du ta bort valda utrymmen?
#XBUT: Confirmation button for objects dialog
removeObjectsOrSpacesConfirmationBtn=Ta bort utrymmen
#XMSG: Success message after removing objects
removeObjectsSuccessMessage=Exponerade objekt har tagits bort
#XMSG: Success message after assigning objects
assignObjectsSuccessMessage=Exponerade objekt har allokerats
#XFLD: auto object assignment label for space tile
autoObjAssignmentLabel=Alla exponerade objekt
#XFLD: Spaces tab label
spacesTabLabel=Utrymmen
#XFLD: Exposed objects tab label
consumptionObjectsTabLabel=Exponerade objekt
#XMSG: Success message after unassigning spaces
spacesUnassignedSuccessMessage=Utrymmen har tagits bort
#XMSG: Success message after unassigning space
spaceUnassignedSuccessMessage=Utrymme har tagits bort
#YMSE: Error while assigning / unassigning spaces
spacesAssignUnassignError=Utrymmen kunde inte allokeras eller tas bort.
#YMSE: Error while removing objects
removeObjectsError=Objekt kunde inte allokeras eller tas bort.
#YMSE: Error while removing object
removeObjectError=Objekt kunde inte allokeras eller tas bort.
#YMSE: Error when saving the ecn with invalid value
invalidSizingPlanError=Tidigare valt antal är inte längre giltigt. Välj ett giltigt antal.
#YMSE: Error when saving the ecn with invalid value
invalidPerformanceClassError=Välj en giltig prestandaklass.
#YMSE: Error when saving the ecn with invalid value
performanceClassNotAvailableError=Tidigare vald prestandaklass "{0}" är inte giltig för närvarande. Välj giltig prestandaklass.
#XTXT: Delete ECN confirmation text
deleteConfirmationTxtECN=Vill du ta bort elastisk beräkningsnod?
#XFLD: tooltip for ? button
help=Hjälp
#XFLD: ECN edit button label
editECN=Konfigurera
#XFLD: Technical type label for ERModel
DWC_ERMODEL=Entitet - relationsmodell
#XFLD: Technical type label for Local Table
DWC_LOCAL_TABLE=Lokal tabell
#XFLD: Technical type label for Remote Table
DWC_REMOTE_TABLE=Fjärrtabell
#XFLD: Technical type label for Analytic Model
DWC_ANALYTIC_MODEL=Analysmodell
#XFLD: Technical type label for Task Chain
DWC_TASKCHAIN=Uppgiftskedja
#XFLD: Technical type label for Data Flow
DWC_DATAFLOW=Dataflöde
#XFLD: Technical type label for Replication Flow
DWC_REPLICATIONFLOW=Replikeringsflöde
#XFLD: Technical type label for Transformation Flow
DWC_TRANSFORMATIONFLOW=Transformationsflöde
#XFLD: Technical type label for Intelligent Lookup
DWC_IDT=Intelligent sökning
#XFLD: Technical type label for Repository
DWC_REPOSITORY_PACKAGE=Repository
#XFLD: Technical type label for Enterprise Search
DWC_ESMODEL=Enterprise Search
#XFLD: Technical type label for View
DWC_VIEW=Vy
#XFLD: Technical type label for Data Product
DWC_DATAPRODUCT=Dataprodukt
#XFLD: Technical type label for Data Access Control
DWC_DAC=Dataåtkomstkontroll
#XFLD: Technical type label for Folder
DWC_FOLDER=Mapp
#XFLD: Technical type label for Data Entity
DWC_BUSINESS_ENTITY=Affärsentitet
#XFLD: Technical type label for Business Entity Variant
DWC_BUSINESS_ENTITY_VARIANT=Affärsentitetsvariant
#XFLD: Technical type label for Responsibility Scenario
DWC_AUTH_SCENARIO=Ansvarsscenario
#XFLD: Technical type label for Fact Model
DWC_FACT_MODEL=Faktamodell
#XFLD: Technical type label for Transformation Flow
DWC_PERSPECTIVE=Perspektiv
#XFLD: Technical type label for Consumption Model
DWC_CONSUMPTION_MODEL=Förbrukningsmodell
#XFLD: Technical type label for Remote Connection
DWC_REMOTECONNECTION=Fjärranslutning
#XFLD: Technical type label for Fact Model Variant
DWC_FACT_MODEL_VARIANT=Faktamodellvariant
#XMSG: Schedule created alert message
createScheduleSuccess=Schema har skapats
#XMSG: Schedule updated alert message
updateScheduleSuccess=Schema har uppdaterats
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Schema har raderats
#XMSG: Schedule takeover success message
ownerChangeScheduleSuccess=Schema har allokerats till dig
#XMSG: Message for starting pausing of schedule
pauseScheduleStarted=Pausar 1 schema
#XMSG: Message for starting resuming of schedule
resumeScheduleStarted=Återupptar 1 schema
#XFLD: Segmented button label
availableSpacesButton=Tillgänglig
#XFLD: Segmented button label
selectedSpacesButton=Valda
#XFLD: Visit website button text
visitWebsite=Besök webbplats
#XMSG: Warning message on unselecting source language
sourceLanguageRemovalWarning=Tidigare valt källspråk kommer att tas bort.
#XBUT: enable translation confirmation button
enableTranslationConfirmationBtn=Aktivera
#XFLD: ECN performance class label
performanceClassLabel=Prestandaklass
#XTXT performance class memory text
memoryText=Minne
#XTXT performance class compute text
computeText=Compute
#XTXT performance class high-compute text
highComputeText=High Compute
#XBUT: Recycle Bin Button Text
recycleBin=Papperskorg
#XBUT: Restore Button Text
restore=Återställ
#XMSG: Warning message for new Workload Management UI
priorityWarning=Det här området är skrivskyddat. Du kan ändra utrymmesprioriteten i området System/Konfiguration/Arbetsmängdshantering.
#XMSG: Warning message for new Workload Management UI
workloadWarning=Det här området är skrivskyddat. Du kan ändra arbetsmängdskonfigurationen för utrymmet i området System/Konfiguration/Arbetsmängdshantering.
#XCOL: Space table-view column: Apache Spark vCPUs
sparkManagementVCPUs=Apache Spark vCPUs
#XCOL: Space table-view column: Apache Spark Memory (GB)
sparkManagementMemory=Apache Spark-minne (GB)
#XCOL: Space type (from Space Capability) for Data Ingestion Spaces
ingestionSpaceType=Dataproduktintag
#XFLD: Deployment in progress nodatatext for spark table
sparkTableDeploymentInProgress=Inga data tillgängliga eftersom utrymmet distribueras
#XFLD: Runtime data being loaded nodatatext for spark table
sparkTableDeploymentLoading=Inga data tillgängliga eftersom utrymmet läses in
#XCOL: Space type (from Space Capability) for BW Private Cloud Edition
BWPCESpaceType=SAP BW
#XBUT: Go to Instance Mappings Button
navToMappingsButton=Redigera instansmappningar
#XFLD: SAP HANA Data Lake Storage label if less than 1GB
dataLakeStorageUsed=< 1 GB
