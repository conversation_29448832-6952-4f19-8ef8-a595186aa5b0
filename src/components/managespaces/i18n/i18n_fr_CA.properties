#DO NOT REMOVE________________________
#XFLD: Breadcrumb for space monitoring page
spaceMonitorBreadcrumb=Suivi
#XTXT: Type name for spaces in browser tab page title
space=Espace
#_____________________________________
#XFLD: Spaces label in
spaces=Espaces
#XFLD: Manage plan button text
manageQuotaButtonText=Gérer le plan
#XBUT: Manage resources button
manageResourcesButton=Gérer les ressources
#XFLD: Create space button tooltip
createSpace=Créer un espace
#XFLD: Create
create=Créer
#XFLD: Deploy
deploy=Déployer
#XFLD: Page
page=Page
#XFLD: Cancel
cancel=Annuler
#XFLD: Update
update=Mettre à jour
#XFLD: Save
save=Enregistrer
#XFLD: OK
ok=OK
#XFLD: days
days=Jours
#XFLD: Space tile edit button label
edit=Modifier
#XFLD: Auto Assign all objects to space
autoAssign=Affecter automatiquement
#XFLD: Space tile open monitoring button label
openMonitoring=Surveiller
#XFLD: Delete
delete=Supprimer
#XFLD: Copy Space
copy=Copier
#XFLD: Close
close=Fermer
#XCOL: Space table-view column status
status=Statut
#XFLD: Space status active
activeLabel=Actif
#XFLD: Space status locked
lockedLabel=Verrouillé
#XFLD: Space status critical
criticalLabel=Critique
#XFLD: Space status cold
coldLabel=Froid
#XFLD: Space status deleted
deletedLabel=Supprimé
#XFLD: Space status unknown
unknownLabel=Inconnu
#XFLD: Space status ok
okLabel=Sain
#XFLD: Database user expired
expired=Expiré
#XFLD: deployed
deployed=Déployé
#XFLD: not deployed
notDeployed=Non déployé
#XFLD: changes to deploy
changesToDeploy=Modifications à déployer
#XFLD: pending
pending=Déploiement en cours
#XFLD: designtime error
designtimeError=Erreur de conception
#XFLD: runtime error
runtimeError=Erreur d'exécution
#XFLD: Space created by label
createdBy=Auteur de la création
#XFLD: Space created on label
createdOn=Date de création
#XFLD: Space deployed on label
deployedOn=Date de déploiement
#XFLD: Space ID label
spaceID=ID d'espace
#XFLD: Priority label
priority=Priorité
#XFLD: Space Priority label
spacePriority=Priorité de l'espace
#XFLD: Space Configuration label
spaceConfiguration=Configuration de l'espace
#XFLD: Not available
notAvailable=Non disponible
#XFLD: WorkloadType default
default=Par défaut
#XFLD: WorkloadType custom
custom=Personnalisé
#XFLD: Data Lake option label; refers to component SAP HANA Cloud, Data Lake
dataLakeAccess=Accès au Data Lake
#XFLD: Translation label
translationLabel=Traduction
#XFLD: Source language label
sourceLanguageLabel=Langue source
#XFLD: Translation CheckBox label
translationCheckBox=Activer la traduction
#XTXT: Popover hint that space has to be deployed to show user details
openDatabaseUserHintPopoverText=Déployer un espace pour accéder aux détails de l'utilisateur
#XTXT: Popover hint that space has to be deployed to open database explorer
openDatabaseExplorerHintPopoverText=Déployer un espace pour ouvrir l'Explorateur de bases de données
#XTXT: Text for when the Data Lake check box is disabled
dataLakeOptionDisabled=Vous ne pouvez pas utiliser cet espace pour accéder au Data Lake car il est déjà utilisé par un autre espace.
#XFLD: Data Lake check box text; refers to component SAP HANA Cloud, Data Lake
dataLakeCheckBox=Utiliser cet espace pour accéder au Data Lake.
#XFLD: Space Priority minimum label extension
low=Faible
#XFLD: Space Priority maximum label extension
high=Élevée
#XFLD: Space name label
spaceName=Nom de l'espace
#XFLD: Enable deploy objects checkbox
enableDeployObjects=Déployer les objets
#XTIT: Copy spaces dialog title
copySpaceDialogTitle=Copier {0}
#XTXT Human readable text for unselected source language
unSelectedSourceLanguage=(Non sélectionné)
#XTXT Human readable text for language code "af"
af=Afrikaans
#XTXT Human readable text for language code "ar"
ar=Arabe
#XTXT Human readable text for language code "bg"
bg=Bulgare
#XTXT Human readable text for language code "ca"
ca=Catalan
#XTXT Human readable text for language code "zh"
zh=Chinois simplifié
#XTXT Human readable text for language code "zf"
zf=Chinois
#XTXT Human readable text for language code "hr"
hr=Croate
#XTXT Human readable text for language code "cs"
cs=Tchèque
#XTXT Human readable text for language code "cy"
cy=Gallois
#XTXT Human readable text for language code "da"
da=Danois
#XTXT Human readable text for language code "nl"
nl=Néerlandais
#XTXT Human readable text for language code "en-UK"
en-UK=Anglais (Royaume-Uni)
#XTXT Human readable text for language code "en"
en=Anglais (États-Unis)
#XTXT Human readable text for language code "et"
et=Estonien
#XTXT Human readable text for language code "fa"
fa=Persan
#XTXT Human readable text for language code "fi"
fi=Finnois
#XTXT Human readable text for language code "fr-CA"
fr-CA=Français (Canada)
#XTXT Human readable text for language code "fr"
fr=Français
#XTXT Human readable text for language code "de"
de=Allemand
#XTXT Human readable text for language code "el"
el=Grec
#XTXT Human readable text for language code "he"
he=Hébreu
#XTXT Human readable text for language code "hi"
hi=Hindi
#XTXT Human readable text for language code "hu"
hu=Hongrois
#XTXT Human readable text for language code "is"
is=Islandais
#XTXT Human readable text for language code "id"
id=Indonésien
#XTXT Human readable text for language code "it"
it=Italien
#XTXT Human readable text for language code "ja"
ja=Japonais
#XTXT Human readable text for language code "kk"
kk=Kazakh
#XTXT Human readable text for language code "ko"
ko=Coréen
#XTXT Human readable text for language code "lv"
lv=Letton
#XTXT Human readable text for language code "lt"
lt=Lituanien
#XTXT Human readable text for language code "ms"
ms=Bahasa Melayu
#XTXT Human readable text for language code "no"
no=Norvégien
#XTXT Human readable text for language code "pl"
pl=Polonais
#XTXT Human readable text for language code "pt"
pt=Portugais (Brésil)
#XTXT Human readable text for language code "pt-PT"
pt-PT=Portugais (Portugal)
#XTXT Human readable text for language code "ro"
ro=Roumain
#XTXT Human readable text for language code "ru"
ru=Russe
#XTXT Human readable text for language code "sr"
sr=Serbe
#XTXT Human readable text for language code "sh"
sh=Serbo-croate
#XTXT Human readable text for language code "sk"
sk=Slovaque
#XTXT Human readable text for language code "sl"
sl=Slovène
#XTXT Human readable text for language code "es"
es=Espagnol
#XTXT Human readable text for language code "es-MX"
es-MX=Espagnol (Mexique)
#XTXT Human readable text for language code "sv"
sv=Suédois
#XTXT Human readable text for language code "th"
th=Thaï
#XTXT Human readable text for language code "tr"
tr=Turc
#XTXT Human readable text for language code "uk"
uk=Ukrainien
#XTXT Human readable text for language code "vi"
vi=Vietnamien
#XTIT: Delete spaces confirmation title
deleteConfirmationHeaderText=Supprimer les espaces
#XTXT: Logically delete space delete confirmation text with space name
logicallyDeleteConfirmationBodyTextWithSpaceName=Voulez-vous vraiment déplacer l''espace "{0}" dans la corbeille?
#XTXT: Logically dpace delete confirmation text for multiple spaces
logicallyDeleteConfirmationBodyTextMultiple=Voulez-vous vraiment déplacer les {0} espaces sélectionnés dans la corbeille?
#XTXT: Delete space delete confirmation text with space name
deleteConfirmationBodyTextWithSpaceName=Voulez-vous vraiment supprimer l''espace "{0}"? Cette action ne peut pas être annulée.
#XTXT: Delete space delete confirmation text for multiple spaces
deleteConfirmationBodyTextMultiple=Voulez-vous vraiment supprimer les {0} espaces sélectionnés? Cette action ne peut pas être annulée. Le contenu suivant sera supprimé {1} :
#XTXT: permanently
permanently=de façon définitive
#XTXT: {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDetail=Le contenu suivant sera supprimé {0} et ne pourra pas être récupéré :
#XTXT: Hint Delete space confirmation text
deleteConfirmationHint=Veuillez saisir {0} pour confirmer la suppression.
#XTXT: Delete space confirmation error check spelling
deleteConfirmationErrorCheckSpelling=Veuillez contrôler l'orthographe et réessayer.
#XTXT: All Spaces
allSpaces=Tous les espaces
#XTXT: All data
allData=Tous les objets et toutes les données contenus dans l'espace
#XTXT: All connections
allConnections=Toutes les connexions définies dans l'espace
#XFLD: Space tile selection box tooltip
clickToSelect=Cliquer pour sélectionner
#XTXT: All database users
allDatabaseUsers=Tous les objets et toutes les données contenus dans un schéma Open SQL associé à l'espace
#XFLD: remove members button tooltip
deleteUsers=Retirer les membres
#XTXT: Space long description text
description=Description (4 000 caractères maximum)
#XFLD: Add Members button tooltip
addUsers=Ajouter des membres
#XFLD: Add Users button tooltip
addUsersTooltip=Ajouter des utilisateurs
#XFLD: Edit Users button tooltip
editUsersTooltip=Modifier des utilisateurs
#XFLD: Remove Users button tooltip
removeUsersTooltip=Retirer les utilisateurs
#XFLD: Searchfield placeholder
filter=Rechercher
#XCOL: Users table-view column health
health=Santé
#XCOL: Users table-view column access
access=Accès
#XFLD: No user found nodatatext
noDataText=Aucun utilisateur trouvé
#XTIT: Members dialog title
selectUserDialogTitle=Ajouter des membres
#XTIT: User dialog title
addUserDialogTitle=Ajouter des utilisateurs
#XTIT: Delete connections confirmation title
deleteConnectionConfirmationHeaderText=Supprimer les connexions
#XTIT: Delete single connection confirmation title
deleteConnectionConfirmationHeaderTextSingle=Supprimer la connexion
#XTXT: Delete connection delete confirmation text
deleteConnectionConfirmationBodyText=Voulez-vous vraiment supprimer les connexions sélectionnées? La suppression sera définitive.
#XTIT: Remote source dialog title
selectRemoteConnectionDialogTitle=Sélectionner des connexions
#XTIT: Share connection dialog title
connectionSharingDialogTitle=Partager la connexion
#XTIT: Connections shared from other spaces table title
sharedConnectionsFromOtherSpaces=Connexions partagées
#XFLD: Add remote source button tooltip
addRemoteConnections=Ajouter des connexions
#XFLD: Delete remote source button tooltip
deleteRemoteConnections=Retirer les connexions
#XFLD: Share remote source button tooltip
shareConnections=Partager les connexions
#XFLD: Tile-layout tooltip
tileLayout=Mise en forme de la vignette
#XFLD: Table-layout tooltip
tableLayout=Mise en forme de la table
#XMSG: Success message after creating space
createSpaceSuccessMessage=Espace créé
#XMSG: Success message after copying space
copySpaceSuccessMessage=Copie de l''espace "{0}" vers l''espace "{1}"
#XMSG: Success message after deploying space
deploymentSuccessMessage=Le déploiement d'espace a été lancé.
#XMSG: Success message after Apache Spark update started
sparkUpdateStartedSuccessMessage=La mise à jour d'Apache Spark a commencé.
#YMSE: Error while starting Apache Spark update
sparkUpdateStartedErrorMessage=Impossible de mettre à jour Apache Spark
#XMSG: Success message after updating space
updateSettingsSuccessMessage=Détails de l'espace mis à jour
#XMSG: Success message after unlocking space
unlockSpaceSuccessMessage=Espace temporairement déverrouillé
#XMSG: Success message after deleting a single space
deleteSpaceSuccessMessage=Espace supprimé
#XMSG: Success message after deleting multiple spaces
deleteSpacesSuccessMessage=Espaces supprimés
#XMSG: Success message after restoring a single space
restoreSpaceSuccessMessage=Espace restauré
#XMSG: Success message after restoring multiple spaces
restoreSpacesSuccessMessage=Espaces restaurés
#YMSE: Error while updating settings
updateSettingsFailureMessage=Impossible de mettre à jour les paramètres de l'espace.
#YMSE: Error while updating data lake option
dataLakeEnabledInOtherSpace=Le Data Lake est déjà affecté à un autre espace. Un seul espace à la fois peut accéder au Data Lake.
#YMSE: Error while updating data lake option
virtualTablesExists=Vous ne pouvez pas annuler l'affectation du Data Lake à cet espace car il existe toujours des dépendances aux tables virtuelles*. Veuillez supprimer les tables virtuelles pour annuler l'affectation du Data Lake à cet espace.
#YMSE: Error while unlocking space
unlockSpaceFailureMessage=Impossible de déverrouiller l'espace.
#YMSE: Error while creating space
createSpaceError=Impossible de créer l'espace.
#YMSE: Error while creating duplicate space
createDuplicateSpaceError=Un espace nommé "{0}" existe déjà.
#YMSE: Error while deleting a single space
deleteSpaceError=Impossible de supprimer l'espace.
#YMSE: Error details while deleting a single space
deleteSpaceDetailedError=Votre espace "{0}" ne fonctionne plus correctement. Veuillez essayer de le supprimer à nouveau. Si le problème persiste, contactez votre administrateur pour lui demander de supprimer votre espace ou d''ouvrir un message d''incident.
#YMSE: Error while deleting a single space in files
deleteSpaceInFilesError=Impossible de supprimer les données de l'espace dans Fichiers.
#YMSE: Error while deleting a single space users
deleteSpaceUsersError=Impossible de retirer les utilisateurs.
#YMSE: Error while deleting a single space schema
deleteSpaceSchemasError=Impossible de retirer les schémas.
#YMSE: Error while deleting a single space connections
deleteSpaceConnectionsError=Impossible de retirer les connexions.
#YMSE: Error while deleting a single space data
deleteSpaceDataError=Impossible de supprimer les données de l'espace.
#YMSE: Error while deleting multiple spaces
deleteSpacesError=Impossible de supprimer les espaces.
#YMSE: Error while restoring a single space
restoreSpaceError=Impossible de restaurer l'espace.
#YMSE: Error while restoring multiple spaces
restoreSpacesError=Impossible de restaurer les espaces.
#YMSE: Error while creating users
createUsersError=Impossible d'ajouter les utilisateurs.
#YMSE: Error while removing users
removeUsersError=Nous n'avons pas pu retirer les utilisateurs.
#YMSE: Error while removing user
removeUserError=Nous n'avons pas pu retirer l'utilisateur.
#YMSE: Error assigning roles to a user
roleAssignUnAssignError=Nous n'avons pas pu ajouter l'utilisateur au rôle dans le périmètre sélectionné. \n\n Vous ne pouvez pas vous ajouter vous-même à un rôle dans le périmètre. Vous pouvez demander à votre administrateur de vous ajouter à un rôle dans le périmètre.
#YMSE: Error assigning user to the space
userAssignError=Nous n'avons pas pu ajouter l'utilisateur à l'espace. \n\n L'utilisateur est déjà affecté au nombre maximal d'espaces autorisé (100) dans les rôles dans le périmètre.
#YMSE: Error assigning users to the space
usersAssignError=Nous n'avons pas pu ajouter les utilisateurs à l'espace. \n\n L'utilisateur est déjà affecté au nombre maximal d'espaces autorisé (100) dans les rôles dans le périmètre.
#YMSE: Error while fetching users
fetchUserWithScopedRolesError=Nous n'avons pas pu récupérer les utilisateurs. Veuillez réessayer ultérieurement.
#YMSE: Error while fetching scoped roles
fetchScopedRolesError=Nous n'avons pas pu récupérer les rôles dans le périmètre.
#YMSE: Error while fetching members
fetchUserError=Impossible d'extraire les membres. Veuillez réessayer ultérieurement.
#YMSE: Error while loading run-time database
loadRuntimeError=Nous n'avons pas pu charger les informations de la base de données d'exécution.
#YMSE: Error while loading spaces
loadSpacesError=Désolés, une erreur s'est produite lors de la tentative de récupération de vos espaces.
#YMSE: Error while loading haas resources
loadStorageError=Désolés, une erreur s'est produite lors de la tentative de récupération des données de stockage.
#YMSE: Error no data could be loaded
loadDataError=Désolés, une erreur s'est produite lors de la tentative de récupération de vos données.
#XFLD: Click to refresh storage data
clickToRefresh=Cliquez ici pour réessayer.
#XTIT: Delete space popup title
deleteSpacePopupTitle=Supprimer l'espace
#XCOL: Spaces table-view column name
name=Nom
#XCOL: Spaces table-view deployment status
deploymentStatus=Statut du déploiement
#XFLD: Disk label in space details
storageLabel=Disque (Go)
#XFLD: In-Memory label in space details
ramLabel=Mémoire (Go)
#XFLD: Memory label on space card
memory=Mémoire de stockage
#XFLD: Storage Assignment label in space details
storrageAssignmentLabel=Espace de stockage
#XFLD: Storage Type label in space details
storageTypeLabel=Type de stockage
#XFLD: Enable Space Quota
enableSpaceQuota=Activer le quota d'espace
#XFLD: No Space Quota
noSpaceQuota=Aucun quota d'espace
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
diskAndInMemoryText=Base de données SAP HANA (disque et In-Memory)
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
dataLakeFilesText=SAP HANA Cloud, fichiers du lac de données
#XFLD: Available scoped roles label
availableRoles=Rôles dans le périmètre disponibles
#XFLD: Selected scoped roles label
selectedRoles=Rôles dans le périmètre sélectionnés
#XCOL: Spaces table-view column models
models=Modèles
#XCOL: Spaces table-view column users
users=Utilisateurs
#XCOL: Spaces table-view column connections
connections=Connexions
#XFLD: Section header overview in space detail
overview=Synthèse
#XFLD: Section header Apache Spark in space detail
sparkManagementSection=Apache Spark
#XFLD: Section header Apache Spark in space detail
sparkManagementSubSection=Applications
#XFLD: Section header Apache Spark in space detail
taskAssignmentSubSection=Affectation des tâches
#XFLD: vCPU label in Apache Spark section
vCPULabel=vCPU
#XFLD: Memory label in Apache Spark section
memoryLabel=Mémoire (Go)
#XFLD: space configuration label in Apache Spark section
spaceConfigurationLabel=Configuration de l'espace
#XFLD: Space Source label
sparkApplicationLabel=Application
#XFLD: Cluster Size label
clusterSizeLabel=Taille du cluster
#XFLD: Driver label
driverLabel=Pilote
#XFLD: Executor label
executorLabel=Exécuteur
#XFLD: max label
maxLabel=Utilisation max.
#XFLD: TrF Default label
trFDefaultLabel=Flux de transformation par défaut
#XFLD: Merge Default label
mergeDefaultLabel=Fusion par défaut
#XFLD: Optimize Default label
optimizeDefaultLabel=Optimisation par défaut
#XFLD: Deployment Default label
deploymentDefaultLabel=Déploiement de la table locale (fichier)
#XFLD: max spark source label
driverExecutorMaxUsedSparkSource= {0} UC/{1} Go
#XFLD: Object type label
taskObjectTypeLabel=Type d'objet
#XFLD: Task activity label
taskActivityLabel=Activité
#XFLD: Task Application ID label
taskApplicationIDLabel=Application par défaut
#XFLD: Section header in space detail
generalSettings=Paramètres généraux
#XMSG: Message strip message when a space is locked
spaceLockedMessageStrip=Cet espace est actuellement verrouillé par le système.
#XMSG: Message strip message deploy warning
deployWarningMessageStrip=Les modifications dans cette section seront déployées immédiatement.
#XMSG: Message strip message modify space configuration warning
modifySpaceConfigurationMessageStrip=Notez que modifier ces valeurs peut entraîner des problèmes de performance.
#XFLD: Button text to unlock the space again
unlockSpace=Déverrouiller l'espace
#XFLD: Info text for audit log formatted message
auditLogText=Activez les journaux d'audit pour enregistrer les actions de lecture ou d'affichage (stratégies d'audit). Les administrateurs peuvent ensuite analyser l'auteur et la date/l'heure d'exécution de l'action.
#XMSG: Message strip  audit warning
auditLogWarningMessageStrip=Les journaux d'audit peuvent utiliser une grande partie du stockage de disque chez votre locataire. Si vous activez une stratégie d'audit (actions de lecture ou de modification), vous devez régulièrement surveiller l'utilisation du stockage de disque (via la fiche Stockage de disque utilisé dans le moniteur du système) pour éviter que le disque ne tombe en panne car il est plein, ce qui peut causer une interruption du service. Si vous désactivez une stratégie d'audit, toutes les entrées de son journal d'audit seront supprimées. Si vous souhaitez conserver les entrées du journal d'audit, pensez à les exporter avant de désactiver la stratégie d'audit.
#XFLD: Message strip link text to open in app help to guide user what to do to unlock his space permanently
unlockSpaceHelp=Afficher l'aide
#XMSG: Message strip message when a space will be locked again
spaceTemporarilyUnlockedMessageStrip=Cet espace dépasse son espace de stockage et sera verrouillé dans {0} {1}.
#XMSG: Unit for remaining time until space is locked again
hours=heures
#XMSG: Unit for remaining time until space is locked again
minutes=minutes
#XFLD: Section header in space detail for auditing
detailsSectionAuditing=Audit
#XFLD: Subsection header in space detail for auditing
auditing=Paramètre d'audit des espaces
#XFLD: Hot space tooltip
hotSpaceCountTooltip=Espaces critiques : plus de 90 % du stockage est utilisé.
#XFLD: Green space tooltip
greenSpaceCountTooltip=Espaces sains : 6 à 90 % du stockage est utilisé.
#XFLD: Cold space tooltip
coldSpaceCountTooltip=Espaces froids : 5 % ou moins du stockage est utilisé.
#XFLD: Critical space tooltip
criticalSpaceCountTooltip=Espaces critiques : plus de 90 % du stockage est utilisé.
#XFLD: Green space tooltip
okSpaceCountTooltip=Espaces sains : 6 à 90 % du stockage est utilisé.
#XFLD: Locked space tooltip
lockedSpaceCountTooltipNew=Espaces verrouillés : le stockage est bloqué en raison d'une mémoire insuffisante.
#XFLD: Locked space tooltip
lockedSpaceCountTooltip=Espaces verrouillés
#YMSE: Error while deleting remote source
deleteRemoteError=Impossible de retirer les connexions.
#XFLD: Space ID input tooltip
spaceIDInfoTooltip=L'ID d'espace ne peut pas être modifié plus tard.\nCaractères valides : A - Z, 0 - 9 et _
#YMSE: Error when space name is empty
spaceNameEmptyValidationError=Saisissez un nom d'espace.
#YMSE: Error when ECN Business name is empty
ecnNameEmptyValidationError=Saisissez une appellation.
#YMSE: Error when space id is empty
spaceIDInputEmptyValidationError=Saisissez un ID d'espace.
#YMSE: Error when space id contains invalid characters
spaceIDInputInValidError=Caractères non valides. Utilisez uniquement A - Z, 0 - 9 et _.
#YMSE: Error when space id exists while creation
spaceIDInputDuplicateError=Cet ID d'espace existe déjà.
#XFLD: Space searchfield placeholder
search=Rechercher
#XMSG: Success message after creating users
createUsersSuccess=Utilisateurs ajoutés
#XMSG: Success message after creating user
createUserSuccess=Utilisateur ajouté
#XMSG: Success message after updating users
updateUsersSuccess={0} utilisateurs mis à jour
#XMSG: Success message after updating user
updateUserSuccess=Utilisateur mis à jour
#XMSG: Success message after removing users
removeUsersSuccess={0} utilisateurs retirés
#XMSG: Success message after removing user
removeUserSuccess=Utilisateur retiré
#XFLD: Schema name
schemaName=Nom du schéma
#XFLD: used of total
ofTemplate={0} sur {1}
#XFLD: Assigned disk label in space overview
assignedStorageTemplate=Disque affecté ({0} sur {1})
#XFLD: Assigned Memory label in space overview
assignedRAMTemplate=Mémoire affectée ({0} sur {1})
#XFLD: Storage ratio on space
accelearationRAM=Accélération de la mémoire
#XFLD: No Storage Consumption
noStorageConsumptionText=Aucun quota d'espace affecté
#XFLD: Used disk label in space overview
usedStorageTemplate=Disque utilisé pour le stockage ({0} sur {1})
#XFLD: Used Memory label in space overview
usedRAMTemplate=Mémoire utilisée pour le stockage ({0} sur {1})
#XFLD: Used Disk tooltip
usedStorageTooltipTemplate={0} sur {1} du disque utilisé(s) pour le stockage
#XFLD: Used Memory tooltip
usedRAMTooltipTemplate={0} sur {1} de la mémoire utilisé(s)
#XFLD: Assigned Disk tooltip
assignedStorageTooltipTemplate={0} sur {1} du disque affecté(s)
#XFLD: Assigned Memory tooltip
assignedRAMTooltipTemplate={0} sur {1} de la mémoire affecté(s)
#XFLD: Space related used storage tooltip
spaceRelatedUsedStorageTooltipTemplate=Données d''espace : {0}
#XFLD: Not space related used storage tooltip
otherDataUsedStorageTooltipTemplate=Autres données : {0}
#XFLD: Storage used exceeded popover and tooltip
storageUsedExceededText=Envisagez de prolonger votre plan ou contactez le support SAP.
#XCOL: Space table-view column used Disk
usedStorage=Disque utilisé pour le stockage
#XCOL: Space monitor column used Memory
usedRAM=Mémoire utilisée pour le stockage
#XCOL: Space monitor column Schema
tableSchema=Schéma
#XCOL: Space monitor column Storage Type
tableStorageType=Type de stockage
#XCOL: Space monitor column Table Type
tableType=Type de table
#XCOL: Space monitor column Record Count
tableRecordCount=Nombre d'enregistrements
#XFLD: Assigned Disk
assignedStorage=Disque affecté pour le stockage
#XFLD: Assigned Memory
assignedRAM=Mémoire affectée pour le stockage
#XCOL: Space table-view column storage utilization
tableStorageUtilization=Espace de stockage utilisé
#XFLD: space status
spaceStatus=Statut de l'espace
#XFLD: space type
spaceType=Type d'espace
#XFLD: space type label for space with space type "SAP BW bridge" ("SAP BW bridge" is a product name, do not translate it)
abapbridgeSpaceType=Passerelle SAP BW
#XFLD: space type label for space with space type "Data Marketplace"
marketplaceSpaceType=Produit du fournisseur de données
#XMSG: Error message for when selected space cannot be deleted due to the space type
deleteTypedSpaceError=Vous ne pouvez pas supprimer l''espace {0} car il est de type {1}.
#XMSG: Error message for when selected spaces cannot be deleted due to the space type
deleteTypedSpacesError=Vous ne pouvez pas supprimer les espaces {0} sélectionnés. Impossible de supprimer les espaces appartenant aux types suivants : {1}.
#XFLD: space type label for space without space type
defaultSpaceType=SAP Datasphere
#XFLD: Tooltip for monitor space button
monitorSpace=Surveiller
#XFLD: Tooltip for edit space button
editSpace=Modifier l'espace
#XMSG: Deletion warning in messagebox
deleteConfirmation=Voulez-vous vraiment supprimer cet espace?
#XFLD: Tooltip for delete space button
deleteSpace=Supprimer l'espace
#XFLD: storage
storage=Disque pour le stockage
#XFLD: username
userName=Nom d'utilisateur
#XFLD: port
port=Port
#XFLD: hostname
hostName=Nom de l'hôte
#XFLD: password
password=Mot de passe
#XBUT: Request new password button
requestPassword=Demander un nouveau mot de passe
#YEXP: Usage explanation in time data section
timeDataSectionHint=Créez des tables et dimensions de temps à utiliser dans vos modèles et présentations.
#YEXP: Usage explanation in Space schema section
spaceSchemaHint=Voulez-vous que d'autres outils ou applis puissent utiliser les données de votre espace? Si oui, créez un ou plusieurs utilisateurs pouvant accéder aux données de votre espace et décidez si vous voulez que toutes les futures données de votre espace soient utilisables par défaut.
#XTIT: Create schema popup title
createSchemaDialogTitle=Créer un schéma Open SQL
#XTIT: Generate Time Data dialog title
generateTimeDataDialogTitle=Créer des tables et dimensions de temps
#XTIT: Edit Time Data dialog title
editTimeDataDialogTitle=Modifier les tables et dimensions de temps
#XTIT: Time Data token title
timeDataTokenTitle=Données de période
#XTIT: Time Data token title
timeDataUpdateViews=Mettre à jour les vues de données de période
#XFLD: Time Data token creation in progress label
timeDataCreationInProgress=Création en cours...
#XFLD: Time Data token creation error label
timeDataCreationError=Échec de la création. Réessayez.
#XTIT: Time Data dialog time settings title
timeDataDialogTimeSettingsTitle=Paramètres de la table de temps
#XTIT: Time Data dialog time data table title
timeDataDialogTranslationTablesTitle=Tables de traduction
#XTIT: Time Data dialog dimensions title
timeDataDialogTimeViewsTitle=Dimensions de temps
#XFLD: Time Data dialog time range label
timeRangeHint=Définissez l'intervalle de temps.
#XFLD: Time Data dialog time data table label
timeDataHint=Attribuez un nom à votre table.
#XFLD: Time Data dialog time data dimensions label
timeDimensionsHint=Attribuez un nom à vos dimensions.
#XFLD: Time Data Time range description label
timerangeLabel=Intervalle de temps
#XFLD: Time Data dialog from year label
fromYearLabel=Année de début
#XFLD: Time Data dialog to year label
toYearLabel=Année de fin
#XFLD: Time Data dialog calendar type label
calendarTypeLabel=Type de calendrier
#XFLD: Time Data dialog granularity label
granularityLabel=Granularité
#XFLD: Time Data dialog technical name label
technicalNameLabel=Nom technique
#XFLD: Time Data dialog quarters text table lable
quarterTextsTableLabel=Table de traduction pour les trimestres
#XFLD: Time Data dialog months text table lable
monthsTextsTableLabel=Table de traduction pour les mois
#XFLD: Time Data dialog days text table lable
daysTextsTableLabel=Table de traduction pour les jours
#XFLD: Time Data dialog year label
yearLabel=Dimension Année
#XFLD: Time Data dialog quarter label
quarterLabel=Dimension Trimestre
#XFLD: Time Data dialog month label
monthLabel=Dimension Mois
#XFLD: Time Data dialog day label
dayLabel=Dimension Jour
#XFLD: Time Data dialog gregorian calendar type label
gregorian=Grégorien
#XFLD: Time Data dialog time granularity day label
day=Jour
#XFLD: Time Data dialog business name maxlength reached
timeRangeValueStateTextMaxLengthBusinessName=Longueur maximale de 1000 caractères atteinte
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextMaxRangeExceeded=L'intervalle de temps maximal est de 150 ans.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeLower=La valeur de "Année de début" doit être inférieure à celle de "Année de fin".
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeHigher=La valeur de "Année de début" doit être supérieure ou égale à 1900.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeHigher=La valeur de "Année de fin" doit être supérieure à celle de "Année de début".
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeLower=La valeur de "Année de fin" doit être inférieure à celle de l'année en cours plus 100.
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningFromYear=Augmenter la valeur de "Année de début" risque de provoquer la perte de données.
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningToYear=Diminuer la valeur de "Année de fin" risque de provoquer la perte de données.
#XMSG: Time Data creation validation error message
timeDataValidationError=Certaines zones semblent ne pas être valides. Contrôlez les zones obligatoires pour poursuivre.
#XMSG: Time Data deletion popup text
deleteTimeDataConfirmation=Voulez-vous vraiment supprimer les données?
#XMSG: Time Data creation success message
createTimeDataSuccess=Données de période créées
#XMSG: Time Data update success message
updateTimeDataSuccess=Données de période mises à jour
#XMSG: Time Data delete success message
deleteTimeDataSuccess=Données de période supprimées
#XMSG: Time Data creation error message
createTimeDataError=Une erreur s'est produite lors de la tentative de création des données de période.
#XMSG: Time Data update error message
updateTimeDataError=Une erreur s'est produite lors de la tentative de mise à jour des données de période.
#XMSG: Time Data creation error message
deleteTimeDataError=Une erreur s'est produite lors de la tentative de suppression des données de période.
#XMSG: Error message when time data could not be retrieved
retrievingTimeDataError=Impossible de charger les données de période
#XTIT: Time Data deletion popup title
deleteTimeDataConfirmationTitle=Avertissement
#XTXT: Time Data deletion dependency popup text
deleteTimeDataDependencyText=Nous n'avons pas pu supprimer vos données de période car elles sont utilisées dans d'autres modèles.
#XTXT: Time Data deletion dependency popup text singular
deleteTimeDataDependencyTextSingular=Nous n'avons pas pu supprimer vos données de période car elles sont utilisées dans un autre modèle.
#XMSG: Validation error when required time data form field is empty
timeDataRequiredField=Cette zone est obligatoire et doit être renseignée.
#XFLD: Tooltip for Open in Database Explorer button
openInDBX=Ouvrir dans l'Explorateur de bases de données
#YMSE: Dimension Year
dimensionYearView=Dimension "Année"
#YMSE: Dimension Year
dimensionQuarterView=Dimension "Trimestre"
#YMSE: Dimension Year
dimensionMonthView=Dimension "Mois"
#YMSE: Dimension Year
dimensionDayView=Dimension "Jour"
#XFLD: Time Data deletion object title
timeDataUsedIn=(utilisé dans {0} modèles)
#XFLD: Time Data deletion object title singular
timeDataUsedInSingular=(utilisé dans 1 modèle)
#XFLD: Time Data deletion table column provider
provider=Fournisseur
#XTIT: Time Data dependency dialog title
timeDataDependencyDialogTitle=Dépendances
#XTIT: Create schema popup title
createSpaceSchemaUserDialogTitle=Créer un utilisateur pour le schéma d'espace
#XFLD: Create schema button
createSchemaButton=Créer un schéma Open SQL
#XFLD: Generate TimeData button
generateTimeDataButton=Créer des tables et dimensions de temps
#XFLD: Show dependencies button
showDependenciesButton=Afficher les dépendances
#XFLD: tooltip for disabled buttons when user is not member of space
mustBeMemberOfSpaceForOperation=Pour effectuer cette opération, votre utilisateur doit être un membre de l'espace.
#XFLD: Create space schema user button
createSpaceSchemaUserButton=Créer un utilisateur pour le schéma d'espace
#YMSE: API Schema users load error
loadSchemaUsersError=Impossible de charger la liste des utilisateurs.
#XTIT: API Schema-config popup title
schemaUserDetailsDialogTitle=Détails de l'utilisateur du schéma d'espace
#XMSG: API Schema user deletion popup text
deleteSpaceSchemaUserConfirmation=Voulez-vous vraiment supprimer l'utilisateur sélectionné?
#XMSG: API Schema user deletion confirmation text
userDeleteSuccess=Utilisateur supprimé.
#YMSE: API Schema user deletion error
userDeleteError=Impossible de supprimer l'utilisateur.
#XFLD: User deleted
userDeleted=L'utilisateur a été supprimé.
#XTIT: Remove user popup title
removeUserConfirmationTitle=Avertissement
#XMSG: Remove user popup text
removeUserConfirmation=Voulez-vous vraiment retirer l'utilisateur? L'utilisateur et les rôles dans le périmètre qui lui sont affectés seront retirés de l'espace.
#XMSG: Remove users popup text
removeUsersConfirmation=Voulez-vous vraiment retirer les utilisateurs? Les utilisateurs et les rôles dans le périmètre qui leur sont affectés seront retirés de l'espace.
#XBUT: Confirmation button for remove user dialog
removeUserConfirmationBtn=Retirer
#YMSE: No data text for available roles
noDataAvailableRoles=L'espace n'est ajouté à aucun rôle dans le périmètre. \n Pour que l'ajout d'utilisateurs à cet espace soit possible, il doit d'abord être ajouté à au moins un rôle dans le périmètre.
#YMSE: No data text for selected roles
noDataSelectedRoles=Aucun rôle dans le périmètre sélectionné
#XTIT: Schema-config popup title
schemaDetailsDialogTitle=Détails de la configuration du schéma Open SQL
#XFLD: Label for Read Audit Log
auditLogRead=Activer le journal d'audit pour les opérations de lecture
#XFLD: Label for Change Audit Log
auditLogChange=Activer le journal d'audit pour les opérations de modification
#XFLD: Label Audit Log Retention
auditLogRetention=Conserver les journaux pendant
#XFLD: Label Audit Log Retention Unit
retentionUnit=Jours
#YMSE: Error when log retention time is set incorrectly
incorrectRetentionTime=Saisissez un nombre entier compris entre {0} et {1}.
#XFLD: Label for Cross Space Schema Access
spaceSchemaAccess=Utiliser les données de schéma de l'espace
#XTIT: Confirm deactivation of space schema consumption dialog title
disableSpaceSchemaConsumptionDialogTitle=Arrêter d'utiliser les données de schéma de l'espace
#XTXT: Confirm deactivation of space schema consumption dialog text
disableSpaceSchemaConsumptionDialogText=Il se peut que ce schéma Open SQL utilise des données de votre schéma d'espace. Si vous arrêtez de les utiliser, il est possible que les modèles basés sur les données de schéma de l'espace ne fonctionnent plus.
#XBUT: Confirmation button for the stop consuming space schema data dialog
stopConsuming=Arrêter d'utiliser
#XTXT: Tooltip for data lake icon
dataLakeIconTooltip=Cet espace est utilisé pour accéder au Data Lake.
#XFLD: Tooltip for data lake table entry
dataLakeTableEntryTooltip=Data Lake activé
#XFLD: used In-Memory linit reached tag
usedRamLimitReachedLabel=Limite de mémoire atteinte
#XFLD: Space storage Limit reached tag
storageLimitReachedLabel=Limite de stockage atteinte
#XFLD: Minimal Storage Limit Reached tag
minStorageLimitReachedLabel=Limite de stockage minimale atteinte
#XFLD: Space ram tag
ramLimitReachedLabel=Limite de mémoire atteinte
#XFLD: Minimal In-Memory Limit Reached tag
minRamLimitReachedLabel=Limite de mémoire minimale atteinte
#XFLD: Popover text after click on space storage limit warning
storageLimitReachedText=Vous avez atteint la limite de stockage de l''espace affectée de {0}. Affectez plus de stockage à l''espace.
#XFLD: System storage tag
systemStorageLimitReachedLabel=Limite de stockage système atteinte
#XMSG: Popover text after click on storage limit warning
systemStorageLimitReachedText=Vous avez atteint la limite de stockage système de {0}. Vous ne pouvez maintenant plus affecter de stockage à l''espace.
#XMSG: Schema deletion warning
deleteSchemaConfirmation=La suppression de ce schéma Open SQL entraînera également la suppression définitive de tous les objets stockés et de toutes les associations gérées dans le schéma. Voulez-vous poursuivre?
#XMSG: Success message after deleting a schema
schemaDeleteSuccess=Schéma supprimé
#YMSE: Error while deleting schema.
schemaDeleteError=Impossible de supprimer le schéma.
#XMSG: Success message after update a schema
schemaUpdateSuccess=Schéma mis à jour
#YMSE: Error while updating schema.
schemaUpdateError=Impossible de mettre à jour le schéma.
#XMSG: Hint when no password is shown in the dialog.
newPasswordHint=Nous vous avons fourni un mot de passe pour ce schéma. Si vous l'avez oublié ou perdu, vous pouvez en demander un nouveau. N'oubliez pas de copier ou d'enregistrer le nouveau mot de passe.
#XMSG: Hint when showing the password in the dialog.
savePasswordHint=Copiez votre mot de passe. Vous en aurez besoin pour établir une connexion pour ce schéma. Si vous avez oublié votre mot de passe, vous pouvez ouvrir cette boîte de dialogue pour le réinitialiser.
#XFLD,50: Hint on entering schema name in creation dialog
schemaNameHint=Nom non modifiable une fois le schéma créé
#XFLD: Open SQL Schemas section sub headline
schemasSQL=Open SQL
#XFLD: Space schema section sub headline
schemasSpace=Espace
#XFLD: HDI Container section header
HDIContainers=Conteneurs HDI
#XTXT: Add HDI Containers button tooltip
addHDIContainers=Ajouter des conteneurs HDI
#XTXT: Remove HDI Containers button tooltip
removeHDIContainers=Retirer des conteneurs HDI
#XFLD: Enable HDI Containers feature button
enableHDIContainersButton=Autoriser l'accès
#YMSE: No data text for HDI Containers table
noDataHDIContainers=Aucun conteneur HDI n'a été ajouté.
#YMSE: No data text for Timedata section
noDataTimedata=Aucune table et dimension de temps n'a été créée.
#YMSE: No data text during runtime not available for Timedata section
runtimeNotAvailableNoDataTimedata=Impossible de charger les tables et les dimensions de temps car la base de données d'exécution n'est pas disponible.
#YMSE: No data text during runtime not available for HDI section
runtimeNotAvailableNoDataHDI=Impossible de charger les conteneurs HDI car la base de données d'exécution n'est pas disponible.
#YMSE: Error while fetching HDI Containers
fetchHDIContainersError=Impossible d'accéder aux conteneurs HDI. Veuillez réessayer plus tard.
#XFLD Table column header for HDI Container names
HDIContainerName=Nom du conteneur HDI
#XFLD: Enable HDI Containers feature dialog title
enableHDIContainersDialogTitle=Autoriser l'accès
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText1New=Vous pouvez activer SAP SQL Data Warehousing sur votre locataire SAP Datasphere pour échanger des données entre vos conteneurs HDI et vos espaces SAP Datasphere sans avoir à déplacer les données.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText2=Pour ce faire, ouvrez un message d'incident en cliquant sur le bouton ci-dessous.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText3New=Une fois le message d'incident traité, vous devez créer un ou plusieurs conteneurs HDI dans la base de données d'exécution SAP Datasphere. Ensuite, le bouton Autoriser l'accès est remplacé par le bouton + dans la section Conteneurs HDI pour tous vos espaces SAP Datasphere et vous pouvez ajouter vos conteneurs à un espace.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText41=Vous souhaitez davantage d'informations? Accédez à %%0. Pour des informations détaillées sur les éléments à inclure dans le message d'incident, voir %%1.
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkText=Aide SAP
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkTextSapNote3057059=Note SAP 3057059
#XBUT: Open Ticket Button Text
openTicket=Créer un message d'incident
#XBUT: Add Button Text
add=Ajouter
#XBUT: Next Button Text
next=Suivant
#XBUT: Edit Button Text
editUsers=Modifier
#XBUT: create user Button Text
createUser=Créer
#XBUT: Update user Button Text
updateUser=Sélectionner
#XTIT: Add HDI Containers Dialog Title
addHDIContainersDialogTitle=Ajouter des conteneurs HDI non affectés
#XFLD: No data text for add HDI container dialog
addHDIContainersDialogNoDataText=Nous n'avons trouvé aucun conteneur non affecté. \n Le conteneur que vous recherchez est peut-être déjà affecté à un espace.
#YMSE: Error while loading assigned HDI Containers
loadAssignedHDIContainersError=Impossible de charger les conteneurs HDI affectés
#YMSE: Error while loading assigned HDI Containers
loadUnassignedHDIContainersError=Impossible de charger les conteneurs HDI
#XMSG: Success message
succeededToAddHDIContainer=Conteneur HDI ajouté
#XMSG: Success message
succeededToAddHDIContainerPlural=Conteneurs HDI ajoutés
#XMSG: Success message
succeededToDeleteHDIContainer=Conteneur HDI retiré
#XMSG: Success message
succeededToDeleteHDIContainerPlural=Conteneurs HDI retirés
#XFLD: Time data section sub headline
timeDataSection=Tables et dimensions de temps
#XFLD: Schemas list entry read audit log enabled indicator
readIndicator=Lire
#XFLD: Schemas list entry change audit log enabled indicator
changeIndicator=Modifier
#XFLD: Remote sources section sub headline
allconnections=Affectation de connexion
#XFLD: Remote sources section sub headline
localconnections=Connexions locales
#XFLD: Remote sources section sub headline for open Connectors
openConnectors=SAP Cloud Platform Open Connectors
#XFLD: User section sub headline
memberassignment=Affectation de membre
#XFLD: User assignment section sub headline
userAssignment=Affectation d'utilisateurs
#XFLD: User section Access dropdown Member
member=Membre
#XFLD: User assignment section column name
user=Nom d'utilisateur
#XTXT: Selected role count
selectedRoleToolbarText=Sélectionné : {0}
#XTIT: Space detail section connections title
detailsSectionConnections=Connexions
#XTIT: Space detail section data access title
detailsSectionDataAccess=Accès au schéma
#XTIT: Space detail section time data title
detailsSectionGenerateData=Données de période
#XTIT: Space detail section members title
detailsSectionUsers=Membres
#XTIT: Space detail section Users title
detailsSectionUsersTitle=Utilisateurs
#XTIT: Out of Storage
insufficientStoragePopoverTitle=Mémoire insuffisante
#XTIT: Storage distribution
storageDistributionPopoverTitle=Stockage de disque utilisé
#XTXT: Out of Storage popover text
insufficientStorageText=Pour créer un espace, veuillez réduire le stockage affecté à un autre espace ou supprimer un espace dont vous n'avez plus besoin. Vous pouvez augmenter l'espace de stockage total de votre système en appelant Gérer le plan.
#XMSG: Space id length warning
spaceIdLengthWarning=Maximum de {0} caractères dépassé.
#XMSG: Space name length warning
spaceNameLengthWarning=Maximum de {0} caractères dépassé.
#XMSG: Space id reserved prefix warning
spaceIdnWarningReservedPrefix=N''utilisez pas le préfixe {0} pour éviter tout conflit.
#YMSE: Error while loading open SQL schemas
loadLocalSchemasError=Impossible de charger les schémas Open SQL.
#YMSE: Error while creating open SQL schema
createLocalSchemaError=Impossible de créer les schémas Open SQL.
#YMSE: Error while loading all remote connections
loadRemoteConnectionsError=Impossible de charger toutes les connexions distantes.
#YMSE: Error while loading space details
loadSpaceDetailsError=Les détails de l'espace n'ont pas pu être chargés.
#YMSE: Error while deploying space details
deploySpaceDetailsError=Impossible de déployer l'espace.
#YMSE: Error while copying space details
copySpaceDetailsError=Impossible de copier l'espace.
#YMSE: Error while loading storage data
loadStorageDataError=Impossible de charger les données de stockage.
#YMSE: Error while loading all users
loadAllUsersError=Impossible de charger tous les utilisateurs.
#YMSE: Failed to reset password
resetPasswordError=Impossible de réinitialiser le mot de passe.
#XMSG: Success message after updating space
resetPasswordSuccessMessage=Nouveau mot de passe défini pour le schéma
#YMSE: DP Agent-name too long
DBAgentNameError=Le nom de l'agent de mise à disposition des données est trop long.
#YMSE: Schema-name not valid.
schemaNameError=Le nom du schéma n'est pas valide.
#YMSE: User name not valid.
UserNameError=Le nom de l'utilisateur n'est pas valide.
#XTIT: Consumption by Storage Type
consumptionStorageTypeText=Utilisation par type de données
#XTIT: Consumption by Schema
consumptionSchemaText=Utilisation par schéma
#XTIT: Overall Consumption by schema
overallConsumptionSchemaText=Utilisation globale de table par schéma
#XTIT: Overall Consumption by Table Type
overallConsumptionTableTypeText=Utilisation globale par type de table
#XTIT: Tables
tableDetailsText=Détails de la table
#XTIT: Table Storage Consumption
tableStorageConsumptionText=Utilisation du stockage de la table
#XFLD: Table Type label
tableTypeLabel=Type de table
#XFLD: Schema label
schemaLabel=Schéma
#XFLD: reset table tooltip
resetTable=Réinitialiser la table
#XFLD: In-Memory label in space monitor
inMemoryLabel=Mémoire
#XFLD: Disk label in space monitor
diskLabel=Disque
#XFLD: Yes
yesLabel=Oui
#XFLD: No
noLabel=Non
#XFLD: switch label: Want the data in this space to be consumable by default?
allowConsumptionByDefault=Voulez-vous que les données de cet espace soient utilisables par défaut?
#XFLD: Business Name
businessNameLabel=Appellation
#XFLD: Refresh
refresh=Actualiser
#XMSG: No filter results title
noFilterResultsTitle=Il semblerait que vos paramètres de filtre n'affichent aucune donnée.
#XMSG: No filter results message
noFilterResultsMsg=Essayez d'affiner vos paramètres de filtre et si malgré cela vous n'obtenez toujours aucune donnée, créez des tables dans le Data Builder. Une fois qu'elles auront consommé du stockage, vous pourrez les suivre ici.
#XMSG: Space monitoring Hana state red title
spaceMonitorHanaRedTitle=La base de données d'exécution n'est pas disponible.
#XMSG: Space monitoring Hana state red message
spaceMonitorHanaRedMsg=Comme la base de données d'exécution n'est pas disponible, certaines fonctionnalités sont désactivées et nous ne pouvons afficher aucune information sur cette page.
#YMSE: Error while creating space schema user
createSpaceSchemaUserError=Impossible de créer l'utilisateur pour le schéma d'espace.
#YMSE: Error User name already exists
userAlreadyExistsError=Le nom d'utilisateur existe déjà.
#YMSE: Error Authentication failed
authenticationFailedError=L'authentification a échoué.
#XMSG: Message strip message when a schema user is locked
userLockedMessageStrip=L'utilisateur est verrouillé en raison d'un trop grand nombre d'échecs de connexion. Veuillez demander un nouveau mot de passe pour déverrouiller l'utilisateur.
#XMSG: Success message after reset and unlock schema user
resetUnlockPasswordSuccessMessage=Nouveau mot de passe défini et utilisateur déverrouillé
#XMSG: user is locked message
userLockedMessage=L'utilisateur est verrouillé.
#XCOL: Users table-view column Role
spaceRole=Rôle
#XCOL: Users table-view column Scoped Role
spaceScopedRole=Rôle dans le périmètre
#XCOL: Users table-view column Space Admin
spaceAdmin=Administrateur des espaces
#XFLD: User section dropdown value Viewer
viewer=Visualiseur
#XFLD: User section dropdown value Modeler
modeler=Outil de modélisation
#XFLD: User section dropdown value Data Integrator
dataIntegrator=Intégrateur de données
#XFLD: User section dropdown value Space Administrator
spaceAdministrator=Administrateur des espaces
#XMSG: Success message -  Space Role updated successfully.
updateUserSpaceRoleSuccess=Rôle de l'espace mis à jour
#XMSG: Error message - Failed to update Space Role.
updateUserSpaceRoleError=La mise à jour du rôle de l'espace a échoué.
#XFLD:
databaseUserNameSuffix=Suffixe du nom d'utilisateur de la base de données
#XTXT: Space Schema password text
spaceSchemaPasswordText=Pour établir une connexion à ce schéma, copiez votre mot de passe. Si vous l'avez oublié, vous pouvez toujours en demander un autre.
#XFLD: Cloud Platform label in space schema dialog
cloudPlatformLabel=Cloud Platform
#XTXT: Cloud Platform text in space schema dialog
cloudPlatformText=Pour configurer l'accès via cet utilisateur, activez l'utilisation et copiez les identifiants. Si vous pouvez uniquement copier les identifiants sans mot de passe, assurez-vous d'ajouter le mot de passe ultérieurement.
#XFLD: enable consumption in CloudPlatform label in space schema dialog
enableConsumptionInCloudPlatformLabel=Activer l'utilisation dans Cloud Platform
#XFLD: Credentials for User-Provided Service label in space schema dialog
cloudPlatformCredentialsLabel=Identifiants pour le service fourni par l'utilisateur :
#XFLD: Credentials for User-Provided Service (Without Password) label in space schema dialog
cloudPlatformCredentialsWithoutPasswordLabel=Identifiants pour le service fourni par l'utilisateur (sans mot de passe) :
#XFLD: Copy Credentials label in space schema dialog
copyCredentialsLabel=Copier les identifiants sans mot de passe
#XFLD: Copy Credentials label in space schema dialog
copyFullCredentialsLabel=Copier l'intégralité des identifiants
#XFLD: Copy Password label in space schema dialog
copyPasswordLabel=Copier le mot de passe
#XMSG: Credentials copied to clipboard
credentialsCopiedMessage=Identifiants copiés dans le presse-papiers
#XMSG: Password copied to clipboard
passwordCopiedMessage=Mot de passe copié dans le presse-papiers
#XMSG: create database user dialog title
createDatabaseUserDialogTitle=Créer un utilisateur de base de données
#XMSG: Database Users section title
databaseUsers=Utilisateurs de base de données
#XMSG: Database User Details dialog title
databaseUserDetailsDialogTitle=Détails de l'utilisateur de base de données
#XFLD: database user read audit log
databaseUserAuditLogRead=Activer les journaux d'audit pour les opérations de lecture et conserver les journaux pendant
#XFLD: database user change audit log
databaseUserAuditLogChange=Activer les journaux d'audit pour les opérations de modification et conserver les journaux pendant
#XMSG: Cloud Platform Access
cloudPlatformAccess=Accès à Cloud Platform
#XFLD: Enable Consumption in Cloud Platform hint
enableConsumptionCloudPlatformHint=Configurez l'accès à votre infrastructure de déploiement HANA (HDI) via cet utilisateur de base de données. Pour vous connecter à votre conteneur HDI, la modélisation SQL doit être activée.
#XFLD: Enable HDI Consumption
enableConsumptionCloudPlatform=Activer l'utilisation HDI
#XFLD: Enable Consumption hint
enableConsumptionHint=Voulez-vous que d'autres outils ou applications puissent utiliser les données se trouvant dans votre espace?
#XFLD: Enable Consumption
enableConsumption=Activer l'utilisation SQL
#XFLD: Enable Modeling
enableModeling=Activer la modélisation SQL
#XMSG: Privileges for Data Modeling
privilegesModeling=Ingestion de données
#XMSG: Privileges for Data Consumption
privilegesConsumption=Utilisation de données pour outils externes
#XFLD: SQL Modeling
sqlModeling=Modélisation SQL
#XFLD: SQL Consumption
sqlConsumption=Utilisation SQL
#XFLD: enabled
enabled=Activé
#XFLD: disabled
disabled=Désactivé
#XFLD: Edit Privileges
editPrivileges=Modifier les droits
#XFLD: Open Database Explorer
openDBX=Ouvrir l'Explorateur de bases de données
#XFLD: create database user hint
databaseCreateHint=Veuillez noter que vous ne pourrez plus remodifier le nom d'utilisateur après l'enregistrement.
#XFLD: Internal Schema Name
internalSchemaName=Nom du schéma interne
#YMSE: Failed to load database users
loadDatabaseUserError=Le chargement des utilisateurs de la base de données a échoué.
#YMSE: Failed to delete database user
deleteDatabaseUsersError=La suppression des utilisateurs de la base de données a échoué.
#XMSG: Success message after deleting database user
deleteDatabaseUserSuccess=L'utilisateur de la base de données a été supprimé.
#XMSG: Success message after deleting database users
deleteDatabaseUsersSuccess=Les utilisateurs de la base de données ont été supprimés.
#XMSG: Success message after creating database user
createDatabaseUserSuccessMessage=L'utilisateur de la base de données a été créé.
#YMSE: Failed to create database user
createDatabaseUserError=La création de l'utilisateur de la base de données a échoué.
#XMSG: Success message after updating database user
updateDatabaseUserSuccessMessage=L'utilisateur de la base de données a été mis à jour.
#YMSE: Failed to update database user
updateDatabaseUserError=La mise à jour de l'utilisateur de la base de données a échoué.
#XFLD: HDI Consumption
hdiConsumption=Utilisation HDI
#XTIT: Space detail section Database Access title
detailsSectionDatabaseAccess=Accès à la base de données
#XTXT: Database users consumption hint
databaseUsersConsumptionHint=Rendez vos données d'espace consommables par défaut. Les modèles dans les générateurs vont automatiquement autoriser que ces données soient consommables.
#XFLD: Default Consumption of Space Data:
defaultConsumptionOfSpaceData=Utilisation par défaut des données de l'espace :
#XFLD: Database User Name
databaseUserName=Nom d'utilisateur de la base de données
#XMSG: Database User creation validation error message
databaseUserValidationError=Certaines zones semblent ne pas être valides. Contrôlez les zones obligatoires pour poursuivre.
#XMSG: SQL modeling can’t be enabled since this user has been migrated.
migratedDatabaseUserHint=L'ingestion des données ne peut pas être activée car cet utilisateur a été migré.
#XBUT: Remove Button Text
remove=Retirer
#XBUT: Remove Spaces Button Text
removeSpaces=Retirer les espaces
#XBUT: Remove Objects Button Text
removeObjects=Retirer les objets
#XMSG: No members have been added yet.
noMembersAssigned=Aucun membre n'a pour l'instant été ajouté.
#XMSG: No users have been added yet.
noUsersAssigned=Aucun utilisateur n'a pour l'instant été ajouté.
#XMSG: No database users have been created or your filter is not showing any data.
noDatabaseUsers=Aucun utilisateur de base de données n'a été créé ou votre filtre n'affiche aucune donnée.
#XMSG: Please enter a user name.
noDatabaseUsername=Saisissez un nom d'utilisateur.
#XMSG: The user name is too long. Please use a shorter one.
databaseUsernameTooLong=Le nom d'utilisateur est trop long. Veuillez utiliser un nom plus court.
#XMSG: No privileges have been enabled and this database user will have limited functionality. Do you still want to continue?
noPrivilegesConfirmationMessage=Aucun droit n'a été activé, et cet utilisateur de base de données disposera de fonctionnalités limitées. Voulez-vous continuer malgré tout?
#XMSG: To enable audit logs for change operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogChangeMessage=Pour activer les journaux d'audit pour les opérations de modification, il est nécessaire d'activer également l'ingestion de données. Voulez-vous effectuer cette opération?
#XMSG: To enable audit logs for read operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogReadMessage=Pour activer les journaux d'audit pour les opérations de lecture, il est nécessaire d'activer également l'ingestion de données. Voulez-vous effectuer cette opération?
#XMSG: To enable HDI consumption data ingestion and data consumption needs to be enabled. Do you want to enable all?
enableHDIConsumptionMessage=Pour activer l'utilisation HDI, il est nécessaire d'activer également l'ingestion de données et la consommation de données. Voulez-vous effectuer cette opération?
#XMSG:
databaseUserPasswordText=Pour configurer une connexion à cette base de données, copiez votre mot de passe. Si vous l'oubliez, vous pouvez toujours en demander un autre.
#XTIT: Space detail section members title
detailsSectionMembers=Membres
#XMSG: New password set
newPasswordSet=Nouveau mot de passe défini
#XFLD: Data Ingestion
dataIngestion=Ingestion de données
#XFLD: Data Consumption
dataConsumption=Consommation de données
#XFLD: Privileges
privileges=Privilèges
#XFLD: Enable Data ingestion
enableDataIngestion=Activer l'ingestion de données
#XMSG: Log the read and change operations for data ingestion.
databaseUserAuditHint=Journalisez les opérations de modification et de lecture pour l'ingestion de données.
#XMSG: Make your space data available in your HDI containers.
databaseUserHDIHint=Rendez les données d'espace dans vos conteneurs HDI disponibles.
#XFLD: Enable Data consumption
enableDataConsumption=Activer la consommation de données
#XMSG: Allow other apps or tools to consume your space data.
enableDataConsumptionHint=Autorisez d'autres applications ou outils de consommer vos données d'espace.
#XMSG: To set up access via this schema user copy the credentials to your user-provided service. In case you can only copy the credentials without a password make sure to add the password later.
databaseUserHDIConsumptionHint=Pour configurer l'accès via cet utilisateur de base de données copiez les identifiants vers votre service fourni par l'utilisateur. Si vous pouvez uniquement copier les identifiants sans mot de passe, assurez-vous d'ajouter le mot de passe ultérieurement.
#XFLD: Data Flow Runtime Capacity label in space overview
dataFlowRuntimeCapacity=Capacité d''exécution du flux de données ({0}:{1} heures sur {2} heures)
#XFLD: Could not load Data Flow Runtime Capacity
dataFlowRuntimeCapacityError=Impossible de charger la capacité d'exécution du flux de données
#XMSG: User can grant data consumption to other users.
databaseUserConsumptionWithGrantOptionHint=L'utilisateur peut octroyer de la consommation de données à d'autres utilisateurs.
#XFLD: Enable data consumption with grant option
enableConsumptionWithGrantOption=Activer la consommation de données via une option d'octroi
#XMSG: To enable data consumption with grant option data consumption needs to be enabled. Do you want to enable both?
enableConsumptionWithGrantMessage=Pour autoriser la consommation de données via une option d'octroi, la consommation de données doit être activée. Voulez-vous activer les deux?
#XFLD: Enable Automated Predictive Library (APL) and Predictive Analysis Library (PAL)
allowUsingScriptServerFunctionality=Activer Automated Predictive Library (APL) et la bibliothèque d'analyses prédictives SAP HANA (PAL)
#XMSG: User can use SAP HANA Cloud embedded machine learning functions.
allowUsingScriptServerFunctionalityHint=L'utilisateur peut utiliser les fonctions d'apprentissage automatique intégrées à SAP HANA Cloud.
#XFLD: Password Policy
passwordPolicy=Stratégie de mot de passe
#XMSG: Password Policy
passwordPolicyHint=Activez ou désactivez la stratégie de mot de passe configurée ici.
#XFLD: Enable Password Policy
enablePasswordPolicy=Activer la stratégie de mot de passe
#XMSG: Read Access to the Space Schema
readAccessTitle=Accès en lecture au schéma d'espace
#XMSG: read access hint
readAccessHint=Autorisez l'utilisateur de la base de données à connecter des outils externes au schéma d'espace et à lire des vues exposées pour consommation.
#XFLD: Space Schema
spaceSchema=Schéma d'espace
#XFLD: Enable Read Access (SQL)
enableReadAccess=Activer l'accès en lecture (SQL)
#XMSG: Allow the user to grant read access to other users.
withGrantOptionHint=Autorisez l'utilisateur à octroyer un accès en lecture à d'autres utilisateurs.
#XFLD: With Grant Option
withGrantOption=Avec option d'octroi
#XMSG: Make your space data available in your HDI containers.
enableHDIConsumptionHint=Rendez les données d'espace dans vos conteneurs HDI disponibles.
#XFLD: Enable HDI Consumption
enableHDIConsumption=Activer l'utilisation HDI
#XMSG: Write Access to the User’s Open SQL Schema
writeAccessTitle=Accès en lecture au schéma Open SQL de l'utilisateur
#XMSG: write access hint
writeAccessHint=Autorisez l'utilisateur de base de données à connecter des outils externes au schéma Open SQL de l'utilisateur pour créer des entités de données et ingérer des données à utiliser dans l'espace.
#XFLD: Open SQL Schema
openSQLSchema=Schéma Open SQL
#XFLD: Enable Write Access (SQL DDL & DML)
enableWriteAccess=Activer l'accès en écriture (SQL, DDL et DML)
#XMSG: audit hint
auditHint=Journalisez les opérations de lecture et de modification dans le schéma Open SQL.
#XMSG: data consumption hint
dataConsumptionHint=Exposez toutes les nouvelles vues dans l'espace par défaut pour la consommation. Les outils de modélisation peuvent écraser ce paramètre pour les vues individuelles au moyen du commutateur "Exposer pour consommation" dans le panneau latéral d'édition de la vue. Vous pouvez également choisir les formats pour l'exposition des vues.
#XFLD: Expose for Consumption by Default
exposeDataConsumption=Exposer pour consommation par défaut
#XMSG: database users hint consumption hint
databaseUsersHint2New=Créez des utilisateurs de base de données pour connecter des outils externes à SAP Datasphere. Définissez des droits pour permettre aux utilisateurs de lire les données des espaces, de créer des entités de données (DDL) et d'ingérer des données (DML) pour une utilisation dans l'espace.
#XFLD: Read
read=Lecture
#XFLD: Read (HDI)
readHDI=Lecture (HDI)
#XFLD: Write
write=Écriture
#XMSG: HDI Containers Hint
HDIContainersHint2=Autorisez l'accès à vos conteneurs SAP HANA Deployment Infrastructure (HDI) dans votre espace. Les outils de modélisation peuvent utiliser des artefacts HDI en tant que sources pour des vues et les clients HDI peuvent accéder à vos données d'espace.
#XMSG: Open info dialog
openDatabaseUserInfoDialog=Ouvrir la boîte de dialogue d'informations
#XMSG: Database user is locked. Open info dialog to unlock
openLockedDatabaseUserInfoDialog=L'utilisateur de la base de données est verrouillé. Pour le déverrouiller, ouvrez la boîte de dialogue.
#XFLD: Table
table=Table
#XFLD: Space section partner configuration title
partnerConnectionConfigurationSectionTitle=Connexion du partenaire
#XFLD: Space section partner configuration subtitle
partnerConnectionConfigurationSectionSubTitle=Configuration de la connexion du partenaire
#XFLD: Space section partner configuration hint
partnerConnectionConfigurationSectionHint=Définissez votre propre vignette de connexion du partenaire en ajoutant votre URL iFrame et votre icône. Cette configuration est disponible uniquement pour ce locataire.
#XFLD: Table Name Field
partnerConnectionConfigurationName=Nom de la vignette
#XFLD: Table iFrame URL field
partnerConnectionConfigurationiFrameURL=URL iFrame
#XFLD: Table iFrame Post Message Origin field
partnerConnectionConfigurationiFramePostMessageOrigin=Origine de postMessage iFrame
#XFLD: Table icon/image name
partnerConnectionConfigurationIcon=Icône
#XMSG: Table no result found
partnerConnectionConfigurationNotfound=Aucune configuration de la connexion du partenaire n'a été trouvée.
#XMSG: Partner Connection Confgurations are not available
partnerConnectionConfigurationNotAvailable=Impossible d'afficher les configurations de la connexion du partenaire lorsque la base de données d'exécution n'est pas disponible.
#XFLD: Dialog Create Partner Connection Configuration
partnerConnectionConfigurationDialogTitleAdd=Créer une configuration de connexion du partenaire
#XFLD: Dialog Create Partner Connection Configuration Upload Icon
partnerConnectionConfigurationButtonUloadPartnerIcon=Téléverser l'icône
#XFLD: Dialog Create Partner Connection Configuration Select
partnerConnectionConfigurationSelectIcon=Sélectionner (taille maximale de 200 Ko)
#XFLD: Dialog Create Partner Connection Configuration Enter a name
partnerConnectionConfigurationNamePlaceholder=Exemple de vignette partenaire
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL
partnerConnectionConfigurationiFrameURLPlaceholder=http://www.exemple.com
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL Check
partnerConnectionConfigurationiFramePostMessageOriginPlaceholder=.exemple.com
#XFLD: Dialog Create Partner Connection Configuration Browse
partnerConnectionConfigurationBrowseButton=Parcourir
#XFLD: Create Partner Connection Configuration Msg
partnerConnectionConfigurationUploadMsg=La configuration de la connexion du partenaire a été correctement créée.
#XMSG: Error while deleting Partner Connection configuration(s)
partnerConnectionConfigurationDeleteError=Une erreur s'est produite lors de la suppression de la ou des configuration(s) de la connexion du partenaire.
#XMSG: Partner Connection Configuration has been successfully deleted
partnerConnectionConfigurationDeleteSuccess=La configuration de la connexion du partenaire a été correctement supprimée.
#XMSG: Error while retrieving Partner Connection Configurations
partnerConnectionConfigurationLoadError=Une erreur s'est produite lors de la récupération des configurations de la connexion du partenaire.
#XMSG: Error fileSizeLimitExceeded
partnerConnectionConfigurationfileSizeLimitExceeded=Impossible de téléverser le fichier car il dépasse la taille maximale de 200 Ko
#XBUT: Create Partner Connection Configuration
partnerConnectionConfigurationButtonCreate=Créer une configuration de connexion du partenaire
#XBUT: Delete Partner Connection Configuration
partnerConnectionConfigurationButtonDelete=Supprimer la configuration de la connexion du partenaire
#XMSG: Partner Tile Creation Failure
upsertPartnerTileFailed=Impossible de créer la vignette partenaire
#XMSG: Partner Tile Deletion Failure
deletePartnerTileFailed=Impossible de supprimer la vignette partenaire
#XMSG: HANA cloud connector settings reset failure
resetHANASCCSettingsFailed=Échec de la réinitialisation des paramètres du connecteur SAP HANA Cloud client
#XFLD: Workload Class
workloadClass=Classe de charge de travail
#XFLD: Workload Management
workloadManagement=Gestion de la charge de travail
#XFLD: Priority
workloadClassPriority=Priorité
#XMSG:
workloadManagementPriorityHint=Vous pouvez indiquer la priorisation de cet espace lors de l'interrogation de la base de données. Pour cela, saisissez une valeur entre 1 (priorité minimale) et 8 (priorité maximale). En cas de concurrence entre des espaces pour des threads disponibles, ce sont les espaces avec des priorités élevées qui sont exécutés avant les espaces avec des priorités basses.
#XMSG:
workloadClassPriorityHint=Vous pouvez indiquer la priorité de l'espace de 0 (la plus basse) à 8 (la plus haute). Les instructions d'un espace ayant une priorité élevée sont exécutées avant les instructions des autres espaces ayant une priorité inférieure. La priorité par défaut est 5. La valeur 9 étant réservée aux opérations du système, elle n'est pas disponible pour un espace.
#XFLD: Statement Limits
workloadclassStatementLimits=Limites d'instruction
#XFLD: Workload Configuration
workloadConfiguration=Configuration de la charge de travail
#XMSG:
workloadClassStatementLimitsHint=Vous pouvez indiquer le nombre maximal (ou le pourcentage) de threads et la quantité maximale en Go de mémoire que les instructions qui s'exécutent simultanément dans l'espace peuvent consommer. Vous pouvez saisir n'importe quelle valeur ou pourcentage compris entre 0 (aucune limite) et le nombre total de threads et la quantité maximale de mémoire disponibles dans le locataire. \n\n Si vous indiquez une limite de threads, sachez qu'elle peut réduire les performances. \n\n Si vous indiquez une limite de mémoire, les instructions qui atteignent la limite de mémoire ne sont pas exécutées.
#XMSG:
workloadClassStatementLimitsDescription=La configuration par défaut met à disposition des limites de ressources généreuses et évite qu'un espace unique ne surcharge le système.
#XMSG:
workloadClassStatementLimitCustomDescription=Vous pouvez définir des limites maximales en termes de threads et de mémoire que les instructions exécutées simultanément dans l'espace peuvent consommer.
#XMSG:
totalStatementThreadLimitHelpText=Définir une limite de threads trop basse peut avoir un impact négatif sur les performances d'une instruction, mais des valeurs excessivement élevées ou 0 peuvent aussi permettre à l'espace de consommer la totalité des threads des systèmes disponibles.
#XMSG:
totalStatementMemoryLimitHelpText=Définir une limite de mémoire trop basse peut entraîner des erreurs liées à une mémoire insuffisante, mais des valeurs excessivement élevées ou 0 peuvent aussi permettre à l'espace de consommer la totalité de la mémoire système disponible.
#XMSG:
totalStatementThreadLimitHelpTextRestricted=Saisissez un pourcentage entre 1 % et 70 % (ou un nombre équivalent) pour indiquer le nombre total de threads disponibles dans votre locataire. La définition d'une limite de threads trop basse peut avoir un impact négatif sur les performances d'une instruction, mais des valeurs excessivement élevées peuvent aussi avoir un impact négatif sur les performances d'instructions dans d'autres espaces.
#XMSG:
totalStatementThreadLimitHelpTextDynamic=Saisissez un pourcentage entre 1 % et {0} % (ou un nombre équivalent) pour indiquer le nombre total de threads disponibles dans votre locataire. La définition d''une limite de threads trop basse peut avoir un impact négatif sur les performances d''une instruction, mais des valeurs excessivement élevées peuvent aussi avoir un impact négatif sur les performances d''instructions dans d''autres espaces.
#XMSG:
totalStatementMemoryLimitHelpTextNew=Saisissez une valeur ou un pourcentage entre 0 (aucune limite) et la quantité totale de mémoire disponible dans votre locataire. La définition d'une limite de mémoire trop basse peut avoir un impact négatif sur les performances d'une instruction, mais des valeurs excessivement élevées peuvent aussi avoir un impact négatif sur les performances d'instructions dans d'autres espaces.
#XFLD: Total Statement Thread Limit
totalStatementThreadLimit=Total des instructions - Limite de threads
#XFLD: Segmented button label
workloadclassPercentage=%
#XFLD: Segmented button label
workloadclassGigaByte=Go
#XFLD, 80: Segmented button label
workloadclassThreads=Threads
#XFLD: Total Statement Memory Limit
totalStatementMemoryLimit=Total des instructions - Limite de mémoire
#YMSE: Error while loading customer hana info
loadCustomerHanaInfoError=Échec du chargement des infos client SAP HANA
#XMSG:
minimumLimitReached=Limite minimale atteinte
#XMSG:
maximumLimitReached=Limite maximale atteinte
#XMSG: Name Taken for Technical Name
technical-name-taken=Une connexion avec le nom technique saisi existe déjà. Saisissez un autre nom.
#XMSG: Name Too long for Technical Name
technical-name-too-long=Le nom technique saisi dépasse les 40 caractères. Saisissez un nom comportant moins de caractères.
#XMSG: Technical name field empty
technical-name-field-empty=Saisissez un nom technique.
#XMSG: Generic error when user enters invalid characters for a field
field-invalid-characters=Vous pouvez utiliser uniquement des lettres (a-z), des chiffres (0-9) et des traits de soulignement (_) pour le nom.
#XMSG: Generic error when user enters invalid initial characters for a field
field-underscore-trim=Le nom ne peut pas commencer ou se terminer par un trait de soulignement (_).
#XFLD: Checkbox label for Enable Statement Limits
enableStatementLimits=Activer les limites d'instruction
#XFLD: Connection SubSection Settings Title in space details
connectionSettings=Paramètres
#XMSG: Connections tool hint in Space details section
connectionsToolHint=Pour créer ou modifier des connexions, ouvrez l'application Connexions depuis la navigation latérale ou cliquez ici :
#XBUT: Connection list Open Connection Tool Button
button-connectiontool=Accéder à Connexions
#XFLD: Not deployed label on space tile
notDeployedLabel=L'espace n'a pas encore été déployé.
#XFLD: Not deployed additional text on space tile
notDeployedText=Veuillez déployer l'espace.
#XFLD: Corrupt space label on space tile
corruptSpace=Une erreur s'est produite.
#XFLD: Corrupt space additional text on space tile
corruptSpaceHint=Essayez de redéployer ou contactez le support.
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Données du journal d'audit
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Métadonnées de gestion
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Autres données
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Données dans les espaces
#XMSG: Unlock Space confirmation text
unlockSpaceConfirmation=Voulez-vous vraiment déverrouiller l'espace?
#XMSG: Lock Space confirmation text
lockSpaceConfirmation=Voulez-vous vraiment verrouiller l'espace?
#XFLD: Lock
lock=Verrouiller
#XFLD: Unlock
unlock=Déverrouiller
#XFLD: Locking
locking=Verrouillage en cours
#XMSG: Success message after locking space
lockSpaceSuccessMsg=Espace verrouillé
#XMSG: Success message after unlocking space
unlockSpaceSuccessMsg=Espace déverrouillé
#XMSG: Success message after locking spaces
lockSpacesSuccessMsg=Espaces verrouillés
#XMSG: Success message after unlocking spaces
unlockSpacesSuccessMsg=Espaces déverrouillés
#YMSE: Error while locking a space
lockSpaceError=Impossible de verrouiller l'espace
#YMSE: Error while unlocking a space
unlockSpaceError=Impossible de déverrouiller l'espace
#XTIT: popup title Warning
confirmationWarningTitle=Avertissement
#XMSG: Message strip message when a space was locked manually
manualLockedMessage=L'espace a été manuellement verrouillé.
#XMSG: Message strip message when a space was locked due to the amount of generated audit logs
auditLogLockedMessage=Le système a verrouillé l'espace car les journaux d'audit utilisent une grande quantité de mémoire (Go) sur le disque.
#XMSG: Message strip message when a space was locked because the assigned quota was exceeded.
quotaLockedMessage=Le système a verrouillé l'espace car celui-ci dépasse la mémoire ou le stockage de disque qui lui est attribué(e).
#XMSG: Unlock Spaces confirmation text
unlockSpacesConfirmation=Voulez-vous vraiment déverrouiller les espaces sélectionnés?
#XMSG: Lock Spaces confirmation text
lockSpacesConfirmation=Voulez-vous vraiment déverrouiller les espaces sélectionnés?
#XTIT: Scoped role editor dialog title
scopedRoleEditorDialogTitle=Éditeur de rôles dans le périmètre
#XTIT: ECN Management title
ecnManagementTitle=Espace et gestion du nœud de calcul flexible
#XFLD: ECNs
ecns=Nœuds de calcul flexibles
#XFLD: ECN phase Ready
ecnReady=Prêt
#XFLD: ECN phase Running
ecnRunning=En cours d'exécution
#XFLD: ECN phase Initial
ecnInitial=Pas prêt
#XFLD: ECN phase Starting
ecnStarting=Lancement
#XFLD: ECN phase Starting Failed
ecnStartingFailed=Échec du lancement
#XFLD: ECN phase Stopping
ecnStopping=Arrêt en cours
#XFLD: ECN phase Stopping Failed
ecnStoppingFailed=Échec de l'arrêt
#XBTN: Assign Button
assign=Affecter des espaces
#XBTN: Start Header-Button
start=Lancer
#XBTN: Update Header-Button
repair=Mettre à jour
#XBTN: Stop Header-Button
stop=Arrêter
#XFLD: ECN hours remaining
ecnHoursRemaining=1 000 heures restantes
#XFLD: ECN block-hours remaining
ecnBlockHoursRemaining=Temps de calcul par bloc restant : {0} heures
#XFLD: ECN block-hour remaining
ecnBlockHourRemaining=Temps de calcul par bloc restant : {0} heure
#XTIT: ECN creation dialog title
ecnCreationDialogTitle=Créer un nœud de calcul flexible
#XTIT: ECN edit dialog title
ecnEditDialogTitle=Modifier un nœud de calcul flexible
#XTIT: ECN deletion dialog title
ecnDeletionDialogTitle=Supprimer un nœud de calcul flexible
#XTIT: ECN Space assignment dialog title
ecnSpaceAssignmentDialogTitle=Affecter des espaces
#XFLD: ECN ID
ECNIDLabel=Nœud de calcul flexible
#XTXT: Selected toolbar text
selectedToolbarText=Sélectionné : {0}
#XTIT: Elastic Compute Nodes
ECNslong=Nœuds de calcul flexibles
#XTIT: Object assignment - Table header: Objects available
objectsAvailable=Nombre d'objets
#XTIT: Object assignment - Dialog header text
selectObjects=Sélectionnez les espaces et les objets que vous voulez affecter à votre nœud de calcul flexible.
#XTIT: Object assignment - Table header title: Objects
objects=Objets
#XTIT: Object assignment - Table header: Type
type=Type
#XMSG: Message strip delete database user audit warning
deleteDatabaseUserWarningMessageStrip=Notez que la suppression d'un utilisateur de base de données entraînera la suppression de toutes les entrées de journal d'audit générées. Si vous voulez conserver les journaux d'audit, pensez à les exporter avant de supprimer l'utilisateur de base de données.
#XMSG: Message strip delete HDI container audit logs impact
deleteHDIContainerWarningMessageStrip=Notez que l'annulation de l'affectation d'un conteneur HDI à l'espace entraînera la suppression de toutes les entrées de journal d'audit générées. Si vous voulez conserver les journaux d'audit, pensez à les exporter avant d'annuler l'affectation du conteneur HDI.
#XTXT: All audit logs
allAuditLogs=Toutes les entrées du journal d'audit générées pour l'espace
#XMSG: Message strip disable audit warning
disableAuditWarningMessageStrip=Notez que la désactivation d'une stratégie d'audit (opérations de lecture et de modification) entraînera la suppression de toutes ses entrées dans le journal d'audit. Si vous voulez conserver les entrées du journal d'audit, pensez à les exporter avant de désactiver la stratégie d'audit.
#XTIT: No Spaces assigned to ECN Illustration title
noSpacesECNTitle=Aucun espace ou objet n'est affecté pour l'instant.
#XTIT: No Spaces assigned to ECN Illustration description
noSpacesECNDescription=Pour commencer à travailler avec votre nœud de calcul flexible, affectez-lui un espace ou des objets.
#XTIT: No Spaces Illustration title
noSpacesTitle=Aucun espace n'a été créé pour l'instant.
#XTIT: No Spaces Illustration description
noSpacesDescription=Pour commencer à acquérir des données, créez un espace.
#XTIT: No Spaces Illustration title
noDeletedSpacesTitle=La corbeille est vide.
#XTIT: No Spaces Illustration description
noDeletedSpacesDescription=Ici, vous pouvez récupérer vos espaces supprimés.
#XTXT:  {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDatabaseUsers=Une fois l''espace déployé, les utilisateurs de base de données suivants seront supprimés {0} et ne pourront pas être récupérés :
#XTIT: Delete database users
deleteDatabaseUsersTitle=Supprimer les utilisateurs de base de données
#YMSE: Error if ecn id already exists
ecnIDInputDuplicateError=Cet ID existe déjà.
#YMSE: Error if ecn id contains invalid characters
ecnIDInputInvalidError=Utilisez uniquement des lettres minuscules de a à z et des chiffres de 0 à 9.
#YMSE: Error if ecn id is too short
ecnIDInputTooShortError=L''ID doit comporter au moins {0} caractères.
#XMSG: ecn id length warning
ecnIdLengthWarning=Maximum de {0} caractères dépassé.
#XFLD: open System Monitor
systemMonitor=Moniteur du système
#XFLD: open ECN schedule dialog menu entry
schedule=Planifier
#XFLD: open create ECN schedule dialog
createSchedule=Créer une planification
#XFLD: open change ECN schedule dialog
changeSchedule=Modifier la planification
#XFLD: open delete ECN schedule dialog
deleteSchedule=Supprimer la planification
#XFLD: open self assign ECN schedule dialog
selfAssignSchedule=M'affecter la planification
#XFLD: open pause ECN schedule dialog
pauseSchedule=Suspendre la planification
#XFLD: open resume ECN schedule dialog
resumeSchedule=Reprendre la planification
#XFLD: View Logs
viewLogs=Afficher les journaux
#XFLD: Compute Blocks
computeBlocks=Blocs de calcul
#XFLD: Memory label in ECN creation dialog
ecnMemory=Mémoire (Go)
#XFLD: Storage label in ECN creation dialog
ecnStorage=Stockage (Go)
#XFLD: Number of CPU label in ECN creation dialog
ecnCPUCount=Nombre d'UC
#XFLD: ECN updated by label
changedBy=Auteur de la modification
#XFLD: ECN updated on label
changedOn=Date de modification
#XMSG: Success message after creating a Elastic Compute Node
createEcnSuccessMessage=Nœud de calcul flexible créé
#YMSE: Error while creating a Elastic Compute Node
createEcnError=Impossible de créer le nœud de calcul flexible
#XMSG: Success message after updating a Elastic Compute Node
updateEcnSuccessMessage=Nœud de calcul flexible mis à jour
#XMSG: Error while updating a Elastic Compute Node
udpateEcnError=Impossible de mettre à jour le nœud de calcul flexible
#XMSG: Success message after deleting a Elastic Compute Node
deleteEcnSuccessMessage=Nœud de calcul flexible supprimé
#YMSE: Error while deleting a Elastic Compute Node
deleteEcnError=Impossible de supprimer le nœud de calcul flexible
#XMSG: Success message after starting a Elastic Compute Node
startingEcnSuccessMessage=Lancement du nœud de calcul flexible
#XMSG: Success message after stopping a Elastic Compute Node
stoppingEcnSuccessMessage=Arrêt du nœud de calcul flexible
#YMSE: Error while starting a Elastic Compute Node
startingEcnError=Impossible de lancer le nœud de calcul flexible
#YMSE: Error while stopping a Elastic Compute Node
stoppingEcnError=Impossible d'arrêter le nœud de calcul flexible
#XBUT: Add Object button for an ECN
assignObjects=Ajouter des objets
#XFLD: Enable auto object assignment label
enableAutoObjAssignment=Affecter tous les objets automatiquement
#XFLD: object type label to be assigned
objectTypeLabel=Type (utilisation sémantique)
#XFLD: assigned object type label
assignedObjectTypeLabel=Type
#XFLD: technical name label
TechnicalNameLabel=Nom technique
#XFLD: Object selecttion ECN label
objectSelectionECNLabel=Sélectionnez les objets que vous voulez ajouter au nœud de calcul flexible.
#XTIT: Add objects dialog title
assignObjectsTitle=Affecter les objets de
#XFLD: object label with object count
objectLabel=Objet
#XMSG: No objects available to add message.
noObjectsToAssign=Aucun objet disponible à affecter.
#XMSG: No objects assigned message.
noAssignedObjects=Aucun objet affecté.
#XTIT: Remove objects popup title
removeObjectsOrSpacesConfirmationTitle=Avertissement
#XTIT: Delete ECN popup button and title
deleteECNConfirmationButtonAndTitle=Supprimer
#XMSG: Remove objects popup text
removeObjectsConfirmation=Voulez-vous vraiment retirer les objets sélectionnés?
#XMSG: Remove spaces popup text
removeSpacesConfirmation=Voulez-vous vraiment retirer les espaces sélectionnés?
#XBUT: Confirmation button for objects dialog
removeObjectsOrSpacesConfirmationBtn=Retirer les espaces
#XMSG: Success message after removing objects
removeObjectsSuccessMessage=Les objets exposés ont été retirés.
#XMSG: Success message after assigning objects
assignObjectsSuccessMessage=Les objets exposés ont été affectés.
#XFLD: auto object assignment label for space tile
autoObjAssignmentLabel=Tous les objets exposés
#XFLD: Spaces tab label
spacesTabLabel=Espaces
#XFLD: Exposed objects tab label
consumptionObjectsTabLabel=Objets exposés
#XMSG: Success message after unassigning spaces
spacesUnassignedSuccessMessage=Les espaces ont été retirés.
#XMSG: Success message after unassigning space
spaceUnassignedSuccessMessage=L'espace a été retiré.
#YMSE: Error while assigning / unassigning spaces
spacesAssignUnassignError=Les espaces n'ont pas pu être affectés ou retirés.
#YMSE: Error while removing objects
removeObjectsError=Impossible d'affecter ou de retirer les objets
#YMSE: Error while removing object
removeObjectError=Impossible d'affecter ou de retirer l'objet
#YMSE: Error when saving the ecn with invalid value
invalidSizingPlanError=Le numéro précédemment sélectionné n'est plus valide. Sélectionnez un numéro valide.
#YMSE: Error when saving the ecn with invalid value
invalidPerformanceClassError=Sélectionnez une classe de performance valide.
#YMSE: Error when saving the ecn with invalid value
performanceClassNotAvailableError=La classe de performance précédemment sélectionnée "{0}" est actuellement non valide. Sélectionnez la classe de performance valide.
#XTXT: Delete ECN confirmation text
deleteConfirmationTxtECN=Voulez-vous vraiment supprimer le nœud de calcul flexible?
#XFLD: tooltip for ? button
help=Aide
#XFLD: ECN edit button label
editECN=Configurer
#XFLD: Technical type label for ERModel
DWC_ERMODEL=Modèle de relation entre les entités
#XFLD: Technical type label for Local Table
DWC_LOCAL_TABLE=Table locale
#XFLD: Technical type label for Remote Table
DWC_REMOTE_TABLE=Table distante
#XFLD: Technical type label for Analytic Model
DWC_ANALYTIC_MODEL=Modèle analytique
#XFLD: Technical type label for Task Chain
DWC_TASKCHAIN=Chaîne de tâches
#XFLD: Technical type label for Data Flow
DWC_DATAFLOW=Flux de données
#XFLD: Technical type label for Replication Flow
DWC_REPLICATIONFLOW=Flux de réplication
#XFLD: Technical type label for Transformation Flow
DWC_TRANSFORMATIONFLOW=Flux de transformation
#XFLD: Technical type label for Intelligent Lookup
DWC_IDT=Recherche intelligente
#XFLD: Technical type label for Repository
DWC_REPOSITORY_PACKAGE=Référentiel
#XFLD: Technical type label for Enterprise Search
DWC_ESMODEL=Enterprise Search
#XFLD: Technical type label for View
DWC_VIEW=Vue
#XFLD: Technical type label for Data Product
DWC_DATAPRODUCT=Produit de données
#XFLD: Technical type label for Data Access Control
DWC_DAC=Contrôle de l'accès aux données
#XFLD: Technical type label for Folder
DWC_FOLDER=Dossier
#XFLD: Technical type label for Data Entity
DWC_BUSINESS_ENTITY=Entité commerciale
#XFLD: Technical type label for Business Entity Variant
DWC_BUSINESS_ENTITY_VARIANT=Variante d'entité commerciale
#XFLD: Technical type label for Responsibility Scenario
DWC_AUTH_SCENARIO=Scénario de responsabilité
#XFLD: Technical type label for Fact Model
DWC_FACT_MODEL=Modèle de fait
#XFLD: Technical type label for Transformation Flow
DWC_PERSPECTIVE=Perspective
#XFLD: Technical type label for Consumption Model
DWC_CONSUMPTION_MODEL=Modèle de consommation
#XFLD: Technical type label for Remote Connection
DWC_REMOTECONNECTION=Connexion distante
#XFLD: Technical type label for Fact Model Variant
DWC_FACT_MODEL_VARIANT=Variante de modèle de fait
#XMSG: Schedule created alert message
createScheduleSuccess=Planification créée
#XMSG: Schedule updated alert message
updateScheduleSuccess=Planification mise à jour
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Planification supprimée
#XMSG: Schedule takeover success message
ownerChangeScheduleSuccess=La planification vous est affectée.
#XMSG: Message for starting pausing of schedule
pauseScheduleStarted=Suspension d'une planification
#XMSG: Message for starting resuming of schedule
resumeScheduleStarted=Reprise d'une planification
#XFLD: Segmented button label
availableSpacesButton=Disponible
#XFLD: Segmented button label
selectedSpacesButton=Sélectionné(es)
#XFLD: Visit website button text
visitWebsite=Visiter le site Web
#XMSG: Warning message on unselecting source language
sourceLanguageRemovalWarning=La langue source précédemment sélectionnée sera retirée.
#XBUT: enable translation confirmation button
enableTranslationConfirmationBtn=Activer
#XFLD: ECN performance class label
performanceClassLabel=Classe de performance
#XTXT performance class memory text
memoryText=Mémoire
#XTXT performance class compute text
computeText=Calcul
#XTXT performance class high-compute text
highComputeText=Calcul de haute performance
#XBUT: Recycle Bin Button Text
recycleBin=Corbeille
#XBUT: Restore Button Text
restore=Restaurer
#XMSG: Warning message for new Workload Management UI
priorityWarning=Cette zone est en lecture seule. Vous pouvez modifier la priorité de l'espace dans la zone Système / Configuration / Gestion de la charge de travail.
#XMSG: Warning message for new Workload Management UI
workloadWarning=Cette zone est en lecture seule. Vous pouvez modifier la configuration de la charge de travail de l'espace dans la zone Système / Configuration / Gestion de la charge de travail.
#XCOL: Space table-view column: Apache Spark vCPUs
sparkManagementVCPUs=vCPU Apache Spark 
#XCOL: Space table-view column: Apache Spark Memory (GB)
sparkManagementMemory=Mémoire (Go) Apache Spark
#XCOL: Space type (from Space Capability) for Data Ingestion Spaces
ingestionSpaceType=Ingestion du produit de données
#XFLD: Deployment in progress nodatatext for spark table
sparkTableDeploymentInProgress=Aucune donnée n'est disponible car l'espace est en cours de déploiement.
#XFLD: Runtime data being loaded nodatatext for spark table
sparkTableDeploymentLoading=Aucune donnée n'est disponible car l'espace est en cours de chargement.
#XCOL: Space type (from Space Capability) for BW Private Cloud Edition
BWPCESpaceType=SAP BW
#XBUT: Go to Instance Mappings Button
navToMappingsButton=Modifier les mappages d'instance
#XFLD: SAP HANA Data Lake Storage label if less than 1GB
dataLakeStorageUsed=< 1 GB
