#DO NOT REMOVE________________________
#XFLD: Breadcrumb for space monitoring page
spaceMonitorBreadcrumb=Nadzor
#XTXT: Type name for spaces in browser tab page title
space=Prostor
#_____________________________________
#XFLD: Spaces label in
spaces=Prostori
#XFLD: Manage plan button text
manageQuotaButtonText=Upravljaj paket
#XBUT: Manage resources button
manageResourcesButton=Upravljanje resursov
#XFLD: Create space button tooltip
createSpace=Ustvari prostor
#XFLD: Create
create=Ustvari
#XFLD: Deploy
deploy=Postavi
#XFLD: Page
page=Stran
#XFLD: Cancel
cancel=Prekliči
#XFLD: Update
update=Posodobitev
#XFLD: Save
save=Shrani
#XFLD: OK
ok=V redu
#XFLD: days
days=Dnevi
#XFLD: Space tile edit button label
edit=Uredi
#XFLD: Auto Assign all objects to space
autoAssign=Samodejna dodelitev
#XFLD: Space tile open monitoring button label
openMonitoring=Nadzor
#XFLD: Delete
delete=Izbriši
#XFLD: Copy Space
copy=Kopiraj
#XFLD: Close
close=Zapri
#XCOL: Space table-view column status
status=Status
#XFLD: Space status active
activeLabel=Aktivno
#XFLD: Space status locked
lockedLabel=Zaklenjeno
#XFLD: Space status critical
criticalLabel=Kritično
#XFLD: Space status cold
coldLabel=Hladno
#XFLD: Space status deleted
deletedLabel=Izbrisano
#XFLD: Space status unknown
unknownLabel=Neznano
#XFLD: Space status ok
okLabel=V dobrem stanju
#XFLD: Database user expired
expired=Poteklo
#XFLD: deployed
deployed=Postavljeno
#XFLD: not deployed
notDeployed=Ni postavljeno
#XFLD: changes to deploy
changesToDeploy=Spremembe za postavitev
#XFLD: pending
pending=Postavitev poteka
#XFLD: designtime error
designtimeError=Napaka časa oblikovanja
#XFLD: runtime error
runtimeError=Napaka časa izvajanja
#XFLD: Space created by label
createdBy=Ustvaril
#XFLD: Space created on label
createdOn=Ustvarjeno dne
#XFLD: Space deployed on label
deployedOn=Postavljeno dne
#XFLD: Space ID label
spaceID=ID prostora
#XFLD: Priority label
priority=Prioriteta
#XFLD: Space Priority label
spacePriority=Prednost prostora
#XFLD: Space Configuration label
spaceConfiguration=Konfiguracija prostora
#XFLD: Not available
notAvailable=Ni na voljo
#XFLD: WorkloadType default
default=Privzeto
#XFLD: WorkloadType custom
custom=Po meri
#XFLD: Data Lake option label; refers to component SAP HANA Cloud, Data Lake
dataLakeAccess=Dostop do jezera podatkov
#XFLD: Translation label
translationLabel=Prevod
#XFLD: Source language label
sourceLanguageLabel=Izvorni jezik
#XFLD: Translation CheckBox label
translationCheckBox=Omogoči prevod
#XTXT: Popover hint that space has to be deployed to show user details
openDatabaseUserHintPopoverText=Postavite prostor za dostop do podrobnosti o uporabniku.
#XTXT: Popover hint that space has to be deployed to open database explorer
openDatabaseExplorerHintPopoverText=Postavite prostor za odpiranje raziskovalca zbirk podatkov
#XTXT: Text for when the Data Lake check box is disabled
dataLakeOptionDisabled=Tega prostora ni mogoče uporabiti za dostop do podatkovnega jezera, ker ga že uporablja drug prostor.
#XFLD: Data Lake check box text; refers to component SAP HANA Cloud, Data Lake
dataLakeCheckBox=Za dostop do jezera podatkov uporabite ta prostor.
#XFLD: Space Priority minimum label extension
low=Nizko
#XFLD: Space Priority maximum label extension
high=Visoko
#XFLD: Space name label
spaceName=Ime prostora
#XFLD: Enable deploy objects checkbox
enableDeployObjects=Postavitev objektov
#XTIT: Copy spaces dialog title
copySpaceDialogTitle=Kopiraj {0}
#XTXT Human readable text for unselected source language
unSelectedSourceLanguage=(Ni izbrano)
#XTXT Human readable text for language code "af"
af=Afrikanščina
#XTXT Human readable text for language code "ar"
ar=Arabščina
#XTXT Human readable text for language code "bg"
bg=Bolgarščina
#XTXT Human readable text for language code "ca"
ca=Katalonščina
#XTXT Human readable text for language code "zh"
zh=Poenostavljena kitajščina
#XTXT Human readable text for language code "zf"
zf=Kitajščina
#XTXT Human readable text for language code "hr"
hr=Hrvaščina
#XTXT Human readable text for language code "cs"
cs=Češčina
#XTXT Human readable text for language code "cy"
cy=Valižanščina
#XTXT Human readable text for language code "da"
da=Danščina
#XTXT Human readable text for language code "nl"
nl=Nizozemščina
#XTXT Human readable text for language code "en-UK"
en-UK=Angleščina (Združeno kraljestvo)
#XTXT Human readable text for language code "en"
en=Angleščina (Združene države Amerike)
#XTXT Human readable text for language code "et"
et=Estonščina
#XTXT Human readable text for language code "fa"
fa=Perzijščina
#XTXT Human readable text for language code "fi"
fi=Finščina
#XTXT Human readable text for language code "fr-CA"
fr-CA=Francoščina (Kanada)
#XTXT Human readable text for language code "fr"
fr=Francoščina
#XTXT Human readable text for language code "de"
de=Nemščina
#XTXT Human readable text for language code "el"
el=Grščina
#XTXT Human readable text for language code "he"
he=Hebrejščina
#XTXT Human readable text for language code "hi"
hi=Hindijščina
#XTXT Human readable text for language code "hu"
hu=Madžarščina
#XTXT Human readable text for language code "is"
is=Islandščina
#XTXT Human readable text for language code "id"
id=Bahasa Indonesia
#XTXT Human readable text for language code "it"
it=Italijanščina
#XTXT Human readable text for language code "ja"
ja=Japonščina
#XTXT Human readable text for language code "kk"
kk=Kazahstanščina
#XTXT Human readable text for language code "ko"
ko=Korejščina
#XTXT Human readable text for language code "lv"
lv=Latvijščina
#XTXT Human readable text for language code "lt"
lt=Litovščina
#XTXT Human readable text for language code "ms"
ms=Bahasa Melayu
#XTXT Human readable text for language code "no"
no=Norveščina
#XTXT Human readable text for language code "pl"
pl=Poljščina
#XTXT Human readable text for language code "pt"
pt=Portugalščina (Brazilija)
#XTXT Human readable text for language code "pt-PT"
pt-PT=Portugalščina (Portugalska)
#XTXT Human readable text for language code "ro"
ro=Romunščina
#XTXT Human readable text for language code "ru"
ru=Ruščina
#XTXT Human readable text for language code "sr"
sr=Srbščina
#XTXT Human readable text for language code "sh"
sh=Srbohrvaščina
#XTXT Human readable text for language code "sk"
sk=Slovaščina
#XTXT Human readable text for language code "sl"
sl=Slovenščina
#XTXT Human readable text for language code "es"
es=Španščina
#XTXT Human readable text for language code "es-MX"
es-MX=Španščina (Mehika)
#XTXT Human readable text for language code "sv"
sv=Švedščina
#XTXT Human readable text for language code "th"
th=Tajščina
#XTXT Human readable text for language code "tr"
tr=Turščina
#XTXT Human readable text for language code "uk"
uk=Ukrajinščina
#XTXT Human readable text for language code "vi"
vi=Vietnamščina
#XTIT: Delete spaces confirmation title
deleteConfirmationHeaderText=Izbrišite prostore
#XTXT: Logically delete space delete confirmation text with space name
logicallyDeleteConfirmationBodyTextWithSpaceName=Res želite prostor "{0}" premakniti v koš?
#XTXT: Logically dpace delete confirmation text for multiple spaces
logicallyDeleteConfirmationBodyTextMultiple=Res želite izbrane prostore {0} premakniti v koš?
#XTXT: Delete space delete confirmation text with space name
deleteConfirmationBodyTextWithSpaceName=Res želite izbrisati prostor "{0}"? Tega dejanja ni mogoče razveljaviti.
#XTXT: Delete space delete confirmation text for multiple spaces
deleteConfirmationBodyTextMultiple=Res želite izbrisati toliko izbranih prostorov: {0}? Tega dejanja ni mogoče razveljaviti. Naslednja vsebina bo {1} izbrisana:
#XTXT: permanently
permanently=izbrisana:
#XTXT: {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDetail=Naslednja vsebina bo {0} izbrisana in je ne bo mogoče obnoviti:
#XTXT: Hint Delete space confirmation text
deleteConfirmationHint=Za potrditev izbrisa vnesite {0}.
#XTXT: Delete space confirmation error check spelling
deleteConfirmationErrorCheckSpelling=Preverite napisano in poskusite znova.
#XTXT: All Spaces
allSpaces=Vsi prostori
#XTXT: All data
allData=Vsi objekti in podatki, vsebovani v prostoru
#XTXT: All connections
allConnections=Vse povezave, definirane v prostoru
#XFLD: Space tile selection box tooltip
clickToSelect=Kliknite za izbiro
#XTXT: All database users
allDatabaseUsers=Vsi objekti in podatki, vsebovani v kateri koli shemi Open SQL, povezani s prostorom
#XFLD: remove members button tooltip
deleteUsers=Odstrani člane
#XTXT: Space long description text
description=Opis (največ 4000 znakov)
#XFLD: Add Members button tooltip
addUsers=Dodajanje članov
#XFLD: Add Users button tooltip
addUsersTooltip=Dodajanje uporabnikov
#XFLD: Edit Users button tooltip
editUsersTooltip=Urejanje uporabnikov
#XFLD: Remove Users button tooltip
removeUsersTooltip=Odstranitev uporabnikov
#XFLD: Searchfield placeholder
filter=Išči
#XCOL: Users table-view column health
health=Zdravje
#XCOL: Users table-view column access
access=Dostop
#XFLD: No user found nodatatext
noDataText=Uporabnik ni najden
#XTIT: Members dialog title
selectUserDialogTitle=Dodajanje članov
#XTIT: User dialog title
addUserDialogTitle=Dodajanje uporabnikov
#XTIT: Delete connections confirmation title
deleteConnectionConfirmationHeaderText=Izbriši povezave
#XTIT: Delete single connection confirmation title
deleteConnectionConfirmationHeaderTextSingle=Izbriši povezavo
#XTXT: Delete connection delete confirmation text
deleteConnectionConfirmationBodyText=Res želite izbrisati izbrane povezave? Trajno bodo odstranjene.
#XTIT: Remote source dialog title
selectRemoteConnectionDialogTitle=Izberi povezave
#XTIT: Share connection dialog title
connectionSharingDialogTitle=Deljenje povezave
#XTIT: Connections shared from other spaces table title
sharedConnectionsFromOtherSpaces=Povezave v skupni rabi
#XFLD: Add remote source button tooltip
addRemoteConnections=Dodaj povezave
#XFLD: Delete remote source button tooltip
deleteRemoteConnections=Odstrani povezave
#XFLD: Share remote source button tooltip
shareConnections=Deljenje povezav
#XFLD: Tile-layout tooltip
tileLayout=Izgled ploščic
#XFLD: Table-layout tooltip
tableLayout=Izgled tabele
#XMSG: Success message after creating space
createSpaceSuccessMessage=Prostor ustvarjen
#XMSG: Success message after copying space
copySpaceSuccessMessage=Kopiranje prostora "{0}" v prostor"{1}"
#XMSG: Success message after deploying space
deploymentSuccessMessage=Postavljanje prostora se je začelo
#XMSG: Success message after Apache Spark update started
sparkUpdateStartedSuccessMessage=Posodobitev Apache Sparka se je začela
#YMSE: Error while starting Apache Spark update
sparkUpdateStartedErrorMessage=Posodobitev Apache Sparka ni uspela
#XMSG: Success message after updating space
updateSettingsSuccessMessage=Podrobnosti prostora posodobljene
#XMSG: Success message after unlocking space
unlockSpaceSuccessMessage=Prostor začasno odklenjen
#XMSG: Success message after deleting a single space
deleteSpaceSuccessMessage=Prostor izbrisan
#XMSG: Success message after deleting multiple spaces
deleteSpacesSuccessMessage=Prostori izbrisani
#XMSG: Success message after restoring a single space
restoreSpaceSuccessMessage=Prostor obnovljen
#XMSG: Success message after restoring multiple spaces
restoreSpacesSuccessMessage=Prostori obnovljeni
#YMSE: Error while updating settings
updateSettingsFailureMessage=Nastavitev prostora ni bilo mogoče posodobiti.
#YMSE: Error while updating data lake option
dataLakeEnabledInOtherSpace=Jezero podatkov je že dodeljeno drugemu prostoru. Do jezera podatkov lahko hkrati dostopa le en prostor.
#YMSE: Error while updating data lake option
virtualTablesExists=Dodelitve jezera podatkov temu prostoru ni mogoče preklicati, ker še vedno obstajajo odvisnosti od navideznih tabel*. Za preklic dodelitve jezera podatkov temu prostoru izbrišite navidezne tabele.
#YMSE: Error while unlocking space
unlockSpaceFailureMessage=Prostora ni bilo mogoče odkleniti.
#YMSE: Error while creating space
createSpaceError=Prostora ni bilo mogoče ustvariti.
#YMSE: Error while creating duplicate space
createDuplicateSpaceError=Prostor z imenom {0} že obstaja.
#YMSE: Error while deleting a single space
deleteSpaceError=Prostora ni bilo mogoče izbrisati.
#YMSE: Error details while deleting a single space
deleteSpaceDetailedError=Vaš prostor "{0}" ne deluje več pravilno. Poskusite ga znova izbrisati. Če še vedno ne deluje, prosite skrbnika, da izbriše vaš prostor, ali pa odprite prijavo težave.
#YMSE: Error while deleting a single space in files
deleteSpaceInFilesError=Podatkov prostora ni bilo mogoče izbrisati.
#YMSE: Error while deleting a single space users
deleteSpaceUsersError=Uporabnikov ni bilo mogoče odstraniti.
#YMSE: Error while deleting a single space schema
deleteSpaceSchemasError=Shem ni bilo mogoče odstraniti.
#YMSE: Error while deleting a single space connections
deleteSpaceConnectionsError=Povezav ni bilo mogoče odstraniti.
#YMSE: Error while deleting a single space data
deleteSpaceDataError=Podatkov prostora ni bilo mogoče izbrisati.
#YMSE: Error while deleting multiple spaces
deleteSpacesError=Prostorov ni bilo mogoče izbrisati.
#YMSE: Error while restoring a single space
restoreSpaceError=Prostora ni bilo mogoče obnoviti.
#YMSE: Error while restoring multiple spaces
restoreSpacesError=Prostorov ni bilo mogoče obnoviti.
#YMSE: Error while creating users
createUsersError=Uporabnikov ni bilo mogoče dodati.
#YMSE: Error while removing users
removeUsersError=Uporabnikov ni bilo mogoče odstraniti.
#YMSE: Error while removing user
removeUserError=Uporabnika ni bilo mogoče odstraniti.
#YMSE: Error assigning roles to a user
roleAssignUnAssignError=Uporabnika ni bilo mogoče dodati izbrani vlogi v obsegu. \n\n Sebe ne morete dodati vlogi v obsegu. Svojega skrbnika lahko prosite, da vas doda vlogi v obsegu.
#YMSE: Error assigning user to the space
userAssignError=Uporabnika ni mogoče dodeliti prostoru. \n\n Uporabnik je že dodeljen največjemu dovoljenemu številu (100) prostorov v vlogah v obsegu.
#YMSE: Error assigning users to the space
usersAssignError=Uporabnikov ni mogoče dodeliti prostoru. \n\n Uporabnik je že dodeljen največjemu dovoljenemu številu (100) prostorov v vlogah v obsegu.
#YMSE: Error while fetching users
fetchUserWithScopedRolesError=Uporabnikov ni bilo mogoče priklicati. Poskusite znova pozneje.
#YMSE: Error while fetching scoped roles
fetchScopedRolesError=Vlog v obsegu ni bilo mogoče priklicati.
#YMSE: Error while fetching members
fetchUserError=Članov ni bilo mogoče priklicati. Poskusite znova pozneje.
#YMSE: Error while loading run-time database
loadRuntimeError=Podatkov iz zbirke podatkov časa izvajanja ni bilo mogoče naložiti.
#YMSE: Error while loading spaces
loadSpacesError=Oprostite, prišlo je do napake pri poskusu priklica vaših prostorov.
#YMSE: Error while loading haas resources
loadStorageError=Oprostite, prišlo je do napake pri poskusu priklica podatkov prostora za shranjevanje.
#YMSE: Error no data could be loaded
loadDataError=Oprostite, prišlo je do napake pri poskusu priklica vaših podatkov.
#XFLD: Click to refresh storage data
clickToRefresh=Kliknite tu in poskusite znova.
#XTIT: Delete space popup title
deleteSpacePopupTitle=Izbriši prostor
#XCOL: Spaces table-view column name
name=Ime
#XCOL: Spaces table-view deployment status
deploymentStatus=Status postavitve
#XFLD: Disk label in space details
storageLabel=Disk (GB)
#XFLD: In-Memory label in space details
ramLabel=Pomnilnik (GB)
#XFLD: Memory label on space card
memory=Pomnilnik za shrambo
#XFLD: Storage Assignment label in space details
storrageAssignmentLabel=Shramba prostora
#XFLD: Storage Type label in space details
storageTypeLabel=Vrsta prostora za shranjevanje
#XFLD: Enable Space Quota
enableSpaceQuota=Omogočenje kvote prostora
#XFLD: No Space Quota
noSpaceQuota=Ni količinske omejitve prostora
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
diskAndInMemoryText=Baza podatkov SAP HANA (disk in notranji pomnilnik)
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
dataLakeFilesText=Datoteke podatkovnega jezera za SAP HANA
#XFLD: Available scoped roles label
availableRoles=Razpoložljive vloge v obsegu
#XFLD: Selected scoped roles label
selectedRoles=Izbrane vloge v obsegu
#XCOL: Spaces table-view column models
models=Modeli
#XCOL: Spaces table-view column users
users=Uporabniki
#XCOL: Spaces table-view column connections
connections=Povezave
#XFLD: Section header overview in space detail
overview=Pregled
#XFLD: Section header Apache Spark in space detail
sparkManagementSection=Apache Spark
#XFLD: Section header Apache Spark in space detail
sparkManagementSubSection=Aplikacije
#XFLD: Section header Apache Spark in space detail
taskAssignmentSubSection=Dodelitev naloge
#XFLD: vCPU label in Apache Spark section
vCPULabel=vCPU-ji
#XFLD: Memory label in Apache Spark section
memoryLabel=Pomnilnik (GB)
#XFLD: space configuration label in Apache Spark section
spaceConfigurationLabel=Konfiguracija prostora
#XFLD: Space Source label
sparkApplicationLabel=Aplikacija
#XFLD: Cluster Size label
clusterSizeLabel=Velikost gruče
#XFLD: Driver label
driverLabel=Gonilnik
#XFLD: Executor label
executorLabel=Izvajalnik
#XFLD: max label
maxLabel=Največ uporabljenih
#XFLD: TrF Default label
trFDefaultLabel=Privzeti tok preoblikovanja
#XFLD: Merge Default label
mergeDefaultLabel=Privzeta združitev
#XFLD: Optimize Default label
optimizeDefaultLabel=Optimizacija privzetega
#XFLD: Deployment Default label
deploymentDefaultLabel=Namestitev lokalne tabele (datoteka)
#XFLD: max spark source label
driverExecutorMaxUsedSparkSource={0} CPU / {1} GB
#XFLD: Object type label
taskObjectTypeLabel=Vrsta objekta
#XFLD: Task activity label
taskActivityLabel=Dejavnost
#XFLD: Task Application ID label
taskApplicationIDLabel=Privzeta aplikacija
#XFLD: Section header in space detail
generalSettings=Splošne nastavitve
#XMSG: Message strip message when a space is locked
spaceLockedMessageStrip=Ta prostor je trenutno zaklenil sistem.
#XMSG: Message strip message deploy warning
deployWarningMessageStrip=Spremembe v tem segmentu bodo takoj postavljene.
#XMSG: Message strip message modify space configuration warning
modifySpaceConfigurationMessageStrip=Upoštevajte, da lahko sprememba vrednosti povzroči težave z učinkovitostjo delovanja.
#XFLD: Button text to unlock the space again
unlockSpace=Odkleni prostor
#XFLD: Info text for audit log formatted message
auditLogText=Omogočite dnevnike nadzora za beleženje dejanj branja ali spreminjanja (pravilniki nadzora). Skrbniki lahko nato analizirajo, kdo je v določenem trenutku izvedel katero dejanje.
#XMSG: Message strip  audit warning
auditLogWarningMessageStrip=Dnevniki nadzora lahko zasedejo veliko prostora na disku vašega najemnika. Če omogočite pravilnik nadzora (dejanja branja ali spreminjanja), morate redno spremljati zasedenost diska (prek kartice Zasedenost pomnilnika v monitorju sistema), da preprečite nedelovanje celotnega diska, kar lahko povzroči motnje v storitvi. Če pravilnik nadzora onemogočite, bodo izbrisani vsi vnosi v dnevniku nadzora. Če želite ohraniti vnose v dnevniku nadzora, jih izvozite, preden onemogočite pravilnik nadzora.
#XFLD: Message strip link text to open in app help to guide user what to do to unlock his space permanently
unlockSpaceHelp=Pokaži pomoč
#XMSG: Message strip message when a space will be locked again
spaceTemporarilyUnlockedMessageStrip=Ta prostor presega svojo shrambo prostora in bo zaklenjen čez {0} {1}.
#XMSG: Unit for remaining time until space is locked again
hours=Ure
#XMSG: Unit for remaining time until space is locked again
minutes=Minute
#XFLD: Section header in space detail for auditing
detailsSectionAuditing=Revizija
#XFLD: Subsection header in space detail for auditing
auditing=Nastavitve revizije prostora
#XFLD: Hot space tooltip
hotSpaceCountTooltip=Kritični prostori: Uporabljeno je več kot 90 % prostora za shranjevanje.
#XFLD: Green space tooltip
greenSpaceCountTooltip=Prostori v dobrem stanju: Uporabljeno je med 6 % in 90 % prostora za shranjevanje.
#XFLD: Cold space tooltip
coldSpaceCountTooltip=Neaktivni prostori: Uporabljeno je manj kot 5 % prostora za shranjevanje.
#XFLD: Critical space tooltip
criticalSpaceCountTooltip=Kritični prostori: Uporabljeno je več kot 90 % prostora za shranjevanje.
#XFLD: Green space tooltip
okSpaceCountTooltip=Prostori v dobrem stanju: Uporabljeno je med 6 % in 90 % prostora za shranjevanje.
#XFLD: Locked space tooltip
lockedSpaceCountTooltipNew=Zaklenjeni prostori: Blokirano, ker ni dovolj pomnilnika.
#XFLD: Locked space tooltip
lockedSpaceCountTooltip=Zaklenjeni prostori
#YMSE: Error while deleting remote source
deleteRemoteError=Povezav ni bilo mogoče odstraniti.
#XFLD: Space ID input tooltip
spaceIDInfoTooltip=ID-a prostora pozneje ne bo mogoče spremeniti.\nVeljavni znaki so A–Z, 0–9 in _
#YMSE: Error when space name is empty
spaceNameEmptyValidationError=Vnesite ime prostora.
#YMSE: Error when ECN Business name is empty
ecnNameEmptyValidationError=Vnesite poslovno ime.
#YMSE: Error when space id is empty
spaceIDInputEmptyValidationError=Vnesite ID prostora.
#YMSE: Error when space id contains invalid characters
spaceIDInputInValidError=Neveljavni znaki: Uporabite le A–Z, 0–9 in _.
#YMSE: Error when space id exists while creation
spaceIDInputDuplicateError=ID prostora že obstaja.
#XFLD: Space searchfield placeholder
search=Išči
#XMSG: Success message after creating users
createUsersSuccess=Uporabniki dodani
#XMSG: Success message after creating user
createUserSuccess=Uporabnik dodan
#XMSG: Success message after updating users
updateUsersSuccess={0} uporabnikov posodobljenih
#XMSG: Success message after updating user
updateUserSuccess=Uporabnik posodobljen
#XMSG: Success message after removing users
removeUsersSuccess={0} uporabnikov odstranjenih
#XMSG: Success message after removing user
removeUserSuccess=Uporabnik odstranjen
#XFLD: Schema name
schemaName=Ime sheme
#XFLD: used of total
ofTemplate={0} od {1}
#XFLD: Assigned disk label in space overview
assignedStorageTemplate=Dodeljeni prostor na disku ({0} od {1})
#XFLD: Assigned Memory label in space overview
assignedRAMTemplate=Dodeljeni pomnilnik ({0} od {1})
#XFLD: Storage ratio on space
accelearationRAM=Pospeševanje pomnilnika
#XFLD: No Storage Consumption
noStorageConsumptionText=Kvota shrambe ni dodeljena.
#XFLD: Used disk label in space overview
usedStorageTemplate=Zasedeni disk za shrambo ({0} od {1})
#XFLD: Used Memory label in space overview
usedRAMTemplate=Zasedeni pomnilnik za shrambo ({0} od {1})
#XFLD: Used Disk tooltip
usedStorageTooltipTemplate={0} od {1} zasedenega diska za shrambo
#XFLD: Used Memory tooltip
usedRAMTooltipTemplate=Uporabljenega je {0} od {1} pomnilnika
#XFLD: Assigned Disk tooltip
assignedStorageTooltipTemplate=Dodeljenega je {0} od {1} prostora na disku
#XFLD: Assigned Memory tooltip
assignedRAMTooltipTemplate=Dodeljenega je {0} od {1} pomnilnika
#XFLD: Space related used storage tooltip
spaceRelatedUsedStorageTooltipTemplate=Podatki prostora: {0}
#XFLD: Not space related used storage tooltip
otherDataUsedStorageTooltipTemplate=Drugi podatki: {0}
#XFLD: Storage used exceeded popover and tooltip
storageUsedExceededText=Razmislite o razširitvi svojega načrta ali se obrnite na SAP-jevo podporo.
#XCOL: Space table-view column used Disk
usedStorage=Zasedeni disk za shrambo
#XCOL: Space monitor column used Memory
usedRAM=Zasedeni pomnilnik za shrambo
#XCOL: Space monitor column Schema
tableSchema=Shema
#XCOL: Space monitor column Storage Type
tableStorageType=Vrsta prostora za shranjevanje
#XCOL: Space monitor column Table Type
tableType=Tip tabele
#XCOL: Space monitor column Record Count
tableRecordCount=Število zapisov
#XFLD: Assigned Disk
assignedStorage=Disk, predviden za shrambo
#XFLD: Assigned Memory
assignedRAM=Pomnilnik, predviden za shrambo
#XCOL: Space table-view column storage utilization
tableStorageUtilization=Uporabljena shramba
#XFLD: space status
spaceStatus=Status prostora
#XFLD: space type
spaceType=Vrsta prostora
#XFLD: space type label for space with space type "SAP BW bridge" ("SAP BW bridge" is a product name, do not translate it)
abapbridgeSpaceType=Most za SAP BW
#XFLD: space type label for space with space type "Data Marketplace"
marketplaceSpaceType=Produkt dobavitelja podatkov
#XMSG: Error message for when selected space cannot be deleted due to the space type
deleteTypedSpaceError=Prostora {0} ni mogoče izbrisati, ker je vrste {1}.
#XMSG: Error message for when selected spaces cannot be deleted due to the space type
deleteTypedSpacesError={0} Izbranih prostorov ne morete izbrisati. Prostorov naslednjih vrst ni mogoče izbrisati: {1}.
#XFLD: space type label for space without space type
defaultSpaceType=SAP Datasphere
#XFLD: Tooltip for monitor space button
monitorSpace=Nadzor
#XFLD: Tooltip for edit space button
editSpace=Uredi prostor
#XMSG: Deletion warning in messagebox
deleteConfirmation=Res želite izbrisati ta prostor?
#XFLD: Tooltip for delete space button
deleteSpace=Izbriši prostor
#XFLD: storage
storage=Disk za shrambo
#XFLD: username
userName=Uporabniško ime
#XFLD: port
port=Vrata
#XFLD: hostname
hostName=Ime gostitelja
#XFLD: password
password=Geslo
#XBUT: Request new password button
requestPassword=Zahtevaj novo geslo
#YEXP: Usage explanation in time data section
timeDataSectionHint=Ustvarite časovne tabele in dimenzije, ki jih boste uporabili v svojih modelih in zgodbah.
#YEXP: Usage explanation in Space schema section
spaceSchemaHint=Želite, da podatke v vašem prostoru porabljajo druga orodja ali aplikacije? Če da, ustvarite enega ali več uporabnikov, ki lahko dostopajo do podatkov v vašem prostoru, in izberite, ali želite, da bo za prihodnje podatke prostora privzeto omogočena poraba.
#XTIT: Create schema popup title
createSchemaDialogTitle=Ustvari shemo Open SQL
#XTIT: Generate Time Data dialog title
generateTimeDataDialogTitle=Ustvari časovne tabele in dimenzije
#XTIT: Edit Time Data dialog title
editTimeDataDialogTitle=Uredi časovne tabele in dimenzije
#XTIT: Time Data token title
timeDataTokenTitle=Časovni podatki
#XTIT: Time Data token title
timeDataUpdateViews=Posodobitev pogledov časovnih podatkov
#XFLD: Time Data token creation in progress label
timeDataCreationInProgress=Ustvarjanje poteka ...
#XFLD: Time Data token creation error label
timeDataCreationError=Ustvarjanje ni uspelo. Poskusite znova.
#XTIT: Time Data dialog time settings title
timeDataDialogTimeSettingsTitle=Nastavitve časovne tabele
#XTIT: Time Data dialog time data table title
timeDataDialogTranslationTablesTitle=Tabele pretvorbe
#XTIT: Time Data dialog dimensions title
timeDataDialogTimeViewsTitle=Časovne dimenzije
#XFLD: Time Data dialog time range label
timeRangeHint=Opredelite obdobje.
#XFLD: Time Data dialog time data table label
timeDataHint=Poimenujte svojo tabelo.
#XFLD: Time Data dialog time data dimensions label
timeDimensionsHint=Poimenujte svojo dimenzijo.
#XFLD: Time Data Time range description label
timerangeLabel=Obdobje
#XFLD: Time Data dialog from year label
fromYearLabel=Od leta
#XFLD: Time Data dialog to year label
toYearLabel=Do leta
#XFLD: Time Data dialog calendar type label
calendarTypeLabel=Tip koledarja
#XFLD: Time Data dialog granularity label
granularityLabel=Zrnatost
#XFLD: Time Data dialog technical name label
technicalNameLabel=Tehnično ime
#XFLD: Time Data dialog quarters text table lable
quarterTextsTableLabel=Tabela pretvorbe za četrtletja
#XFLD: Time Data dialog months text table lable
monthsTextsTableLabel=Tabela pretvorbe za mesece
#XFLD: Time Data dialog days text table lable
daysTextsTableLabel=Tabela pretvorbe za dneve
#XFLD: Time Data dialog year label
yearLabel=Dimenzija leta
#XFLD: Time Data dialog quarter label
quarterLabel=Dimenzija četrtletja
#XFLD: Time Data dialog month label
monthLabel=Dimenzija meseca
#XFLD: Time Data dialog day label
dayLabel=Dimenzija dneva
#XFLD: Time Data dialog gregorian calendar type label
gregorian=Gregorjanski
#XFLD: Time Data dialog time granularity day label
day=Dan
#XFLD: Time Data dialog business name maxlength reached
timeRangeValueStateTextMaxLengthBusinessName=Največja dolžina 1000 znakov je dosežena.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextMaxRangeExceeded=Najdaljše obdobje je 150 let.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeLower="Leto začetka" mora biti pred "Letom konca"
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeHigher="Leto začetka" mora biti 1900 ali pozneje.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeHigher="Leto konca " mora biti za "Letom začetka"
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeLower="Leto konca" mora biti pred tekočim letom plus 100
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningFromYear=Zvišanje "Leta začetka" bo morda povzročilo izgubo podatkov
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningToYear=Znižanje "Leta konca" bo morda povzročilo izgubo podatkov
#XMSG: Time Data creation validation error message
timeDataValidationError=Očitno so nekatera polja neveljavna. Preverite obvezna polja, da boste lahko nadaljevali.
#XMSG: Time Data deletion popup text
deleteTimeDataConfirmation=Res želite izbrisati podatke?
#XMSG: Time Data creation success message
createTimeDataSuccess=Časovni podatki ustvarjeni
#XMSG: Time Data update success message
updateTimeDataSuccess=Časovni podatki posodobljeni
#XMSG: Time Data delete success message
deleteTimeDataSuccess=Časovni podatki izbrisani
#XMSG: Time Data creation error message
createTimeDataError=Prišlo je do napake pri poskusu ustvarjanja časovnih podatkov.
#XMSG: Time Data update error message
updateTimeDataError=Prišlo je do napake pri poskusu posodobitve časovnih podatkov.
#XMSG: Time Data creation error message
deleteTimeDataError=Prišlo je do napake pri poskusu izbrisu časovnih podatkov.
#XMSG: Error message when time data could not be retrieved
retrievingTimeDataError=Časovnih podatkov ni bilo mogoče naložiti.
#XTIT: Time Data deletion popup title
deleteTimeDataConfirmationTitle=Opozorilo
#XTXT: Time Data deletion dependency popup text
deleteTimeDataDependencyText=Vaših časovnih podatkov ni bilo mogoče izbrisati, ker so v uporabi v drugih modelih.
#XTXT: Time Data deletion dependency popup text singular
deleteTimeDataDependencyTextSingular=Vaših časovnih podatkov ni bilo mogoče izbrisati, ker so v uporabi v drugem modelu.
#XMSG: Validation error when required time data form field is empty
timeDataRequiredField=To polje je obvezno in ne sme biti prazno.
#XFLD: Tooltip for Open in Database Explorer button
openInDBX=Odpri v raziskovalcu baz podatkov
#YMSE: Dimension Year
dimensionYearView=Dimenzija "Leto"
#YMSE: Dimension Year
dimensionQuarterView=Dimenzija "Četrtletje"
#YMSE: Dimension Year
dimensionMonthView=Dimenzija "Mesec"
#YMSE: Dimension Year
dimensionDayView=Dimenzija "Dan"
#XFLD: Time Data deletion object title
timeDataUsedIn=(se uporablja v {0} modelih)
#XFLD: Time Data deletion object title singular
timeDataUsedInSingular=(se uporablja v 1 modelu)
#XFLD: Time Data deletion table column provider
provider=Ponudnik
#XTIT: Time Data dependency dialog title
timeDataDependencyDialogTitle=Odvisnosti
#XTIT: Create schema popup title
createSpaceSchemaUserDialogTitle=Ustvari uporabnika za shemo prostora
#XFLD: Create schema button
createSchemaButton=Ustvari shemo Open SQL
#XFLD: Generate TimeData button
generateTimeDataButton=Ustvari časovne tabele in dimenzije
#XFLD: Show dependencies button
showDependenciesButton=Pokaži odvisnosti
#XFLD: tooltip for disabled buttons when user is not member of space
mustBeMemberOfSpaceForOperation=Uporabnik mora biti član prostora, da lahko izvede ta postopek.
#XFLD: Create space schema user button
createSpaceSchemaUserButton=Ustvari uporabnika sheme prostora
#YMSE: API Schema users load error
loadSchemaUsersError=Seznama uporabnikov ni bilo mogoče naložiti.
#XTIT: API Schema-config popup title
schemaUserDetailsDialogTitle=Podrobnosti uporabnika sheme prostora
#XMSG: API Schema user deletion popup text
deleteSpaceSchemaUserConfirmation=Res želite izbrisati izbranega uporabnika?
#XMSG: API Schema user deletion confirmation text
userDeleteSuccess=Uporabnik izbrisan.
#YMSE: API Schema user deletion error
userDeleteError=Uporabnika ni bilo mogoče izbrisati.
#XFLD: User deleted
userDeleted=Uporabnik je bil izbrisan.
#XTIT: Remove user popup title
removeUserConfirmationTitle=Opozorilo
#XMSG: Remove user popup text
removeUserConfirmation=Res želite odstraniti uporabnika? Uporabnik in njegove dodeljene vloge v obsegu bodo odstranjeni iz prostora.
#XMSG: Remove users popup text
removeUsersConfirmation=Res želite odstraniti uporabnike? Uporabniki in njihove dodeljene vloge v obsegu bodo odstranjeni iz prostora.
#XBUT: Confirmation button for remove user dialog
removeUserConfirmationBtn=Odstrani
#YMSE: No data text for available roles
noDataAvailableRoles=Prostor ni dodan nobeni vlogi v obsegu. \n Če želite v prostor dodati uporabnike, ga morate najprej dodati eni ali več vlogam v obsegu.
#YMSE: No data text for selected roles
noDataSelectedRoles=Ni izbranih vlog v obsegu
#XTIT: Schema-config popup title
schemaDetailsDialogTitle=Podrobnosti konfiguracije sheme Open SQL
#XFLD: Label for Read Audit Log
auditLogRead=Omogočanje zapisnika revizije za postopke branja
#XFLD: Label for Change Audit Log
auditLogChange=Omogočanje zapisnika revizije za postopke spremembe
#XFLD: Label Audit Log Retention
auditLogRetention=Ohrani zapisnike za
#XFLD: Label Audit Log Retention Unit
retentionUnit=dni
#YMSE: Error when log retention time is set incorrectly
incorrectRetentionTime=Vnesite celo število med {0} in {1}.
#XFLD: Label for Cross Space Schema Access
spaceSchemaAccess=Porabi podatke sheme prostora
#XTIT: Confirm deactivation of space schema consumption dialog title
disableSpaceSchemaConsumptionDialogTitle=Prenehaj porabljati podatke sheme prostora
#XTXT: Confirm deactivation of space schema consumption dialog text
disableSpaceSchemaConsumptionDialogText=Ta shema Open SQL morda porablja podatke sheme vašega prostora. Če prenehate porabljati, modeli na podlagi podatkov sheme prostora morda ne bodo več delovali.
#XBUT: Confirmation button for the stop consuming space schema data dialog
stopConsuming=Prenehaj porabljati
#XTXT: Tooltip for data lake icon
dataLakeIconTooltip=Ta prostor se uporablja za dostop do jezera podatkov
#XFLD: Tooltip for data lake table entry
dataLakeTableEntryTooltip=Podatkovno jezero omogočeno
#XFLD: used In-Memory linit reached tag
usedRamLimitReachedLabel=Omejitev pomnilnika dosežena
#XFLD: Space storage Limit reached tag
storageLimitReachedLabel=Omejitev prostora za shranjevanje dosežena
#XFLD: Minimal Storage Limit Reached tag
minStorageLimitReachedLabel=Najmanjša omejitev prostora za shranjevanje dosežena
#XFLD: Space ram tag
ramLimitReachedLabel=Omejitev pomnilnika dosežena
#XFLD: Minimal In-Memory Limit Reached tag
minRamLimitReachedLabel=Najmanjša omejitev pomnilnika dosežena
#XFLD: Popover text after click on space storage limit warning
storageLimitReachedText=Dosegli ste omejitev dodeljenega prostora za shranjevanje {0} za prostor. Prostoru dodelite več prostora za shranjevanje.
#XFLD: System storage tag
systemStorageLimitReachedLabel=Sistemska omejitev prostora za shranjevanje dosežena
#XMSG: Popover text after click on storage limit warning
systemStorageLimitReachedText=Dosegli ste sistemsko omejitev prostora za shranjevanje {0}. Prostorom zdaj ne morete več dodeliti dodatnega prostora za shranjevanje.
#XMSG: Schema deletion warning
deleteSchemaConfirmation=Če izbrišete to shemo Open SQL, boste trajno izbrisali tudi vse shranjene podatke in vzdrževane povezave v shemi. Želite nadaljevati?
#XMSG: Success message after deleting a schema
schemaDeleteSuccess=Shema izbrisana
#YMSE: Error while deleting schema.
schemaDeleteError=Sheme ni bilo mogoče izbrisati.
#XMSG: Success message after update a schema
schemaUpdateSuccess=Shema posodobljena
#YMSE: Error while updating schema.
schemaUpdateError=Sheme ni bilo mogoče posodobiti.
#XMSG: Hint when no password is shown in the dialog.
newPasswordHint=Za to shemo smo dodelili geslo. Če ste svoje geslo pozabili ali izgubili, lahko zahtevate novega. Ne pozabite kopirati ali shraniti novega gesla.
#XMSG: Hint when showing the password in the dialog.
savePasswordHint=Kopirajte svoje geslo. Potrebovali ga boste za nastavitev povezave do te sheme. Če ste svoje geslo pozabili, lahko odprete to pogovorno okno in ga ponastavite.
#XFLD,50: Hint on entering schema name in creation dialog
schemaNameHint=Tega imena ni mogoče spremeniti po ustvar. sheme.
#XFLD: Open SQL Schemas section sub headline
schemasSQL=Open SQL
#XFLD: Space schema section sub headline
schemasSpace=Prostor
#XFLD: HDI Container section header
HDIContainers=Zbirniki HDI
#XTXT: Add HDI Containers button tooltip
addHDIContainers=Dodaj zbirnike HDI
#XTXT: Remove HDI Containers button tooltip
removeHDIContainers=Odstrani zbirnike HDI
#XFLD: Enable HDI Containers feature button
enableHDIContainersButton=Omogoči dostop
#YMSE: No data text for HDI Containers table
noDataHDIContainers=Zbirniki HDI niso bili dodani.
#YMSE: No data text for Timedata section
noDataTimedata=Časovne tabele in dimenzije niso bile ustvarjene.
#YMSE: No data text during runtime not available for Timedata section
runtimeNotAvailableNoDataTimedata=Časovnih tabel in dimenzij ni mogoče prenesti, ker baza podatkov o času izvajanja ni na voljo.
#YMSE: No data text during runtime not available for HDI section
runtimeNotAvailableNoDataHDI=Zbirnikov HDI ni mogoče prenesti, ker baza podatkov o času izvajanja ni na voljo.
#YMSE: Error while fetching HDI Containers
fetchHDIContainersError=Zbirnikov HDI ni bilo mogoče pridobiti. Poskusite znova pozneje.
#XFLD Table column header for HDI Container names
HDIContainerName=Ime zbirnika HDI
#XFLD: Enable HDI Containers feature dialog title
enableHDIContainersDialogTitle=Omogoči dostop
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText1New=Za izmenjavo podatkov med zbirniki HDI in prostori SAP Datasphere brez potrebe po premiku podatkov lahko omogočite SAP SQL Data Warehousing na najemniku SAP Datasphere.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText2=To storite tako, da odprete prijavo težave s klikom na spodnji gumb.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText3New=Ko je vaša prijava obdelana, morate izdelati enega ali več novih zbirnikov HDI v zbirki podatkov časa izvajanja SAP Datasphere. Nato je gumb Omogoči dostop zamenjan z gumbom + v segmentu Zbirniki HDI za vse vaše prostore SAP Datasphere in v prostor lahko dodate svoje zbirnike.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText41=Potrebujete več informacij? Obiščite %%0. Za podrobnejše informacije o tem, kaj vključiti v kartico, glejte %%1.
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkText=SAP-jeva pomoč
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkTextSapNote3057059=SAP-jevo navodilo 3057059
#XBUT: Open Ticket Button Text
openTicket=Odpri prijavo težave
#XBUT: Add Button Text
add=Dodaj
#XBUT: Next Button Text
next=Naprej
#XBUT: Edit Button Text
editUsers=Uredi
#XBUT: create user Button Text
createUser=Ustvari
#XBUT: Update user Button Text
updateUser=Izbira
#XTIT: Add HDI Containers Dialog Title
addHDIContainersDialogTitle=Dodaj nedodeljene zbirnike HDI
#XFLD: No data text for add HDI container dialog
addHDIContainersDialogNoDataText=Nedodeljeni zbirniki niso bili najdeni.\nZbirnik, ki ga iščete, je morda že dodeljen prostoru.
#YMSE: Error while loading assigned HDI Containers
loadAssignedHDIContainersError=Dodeljenih zbirnikov HDI ni bilo mogoče prenesti.
#YMSE: Error while loading assigned HDI Containers
loadUnassignedHDIContainersError=Zbirnikov HDI ni bilo mogoče prenesti.
#XMSG: Success message
succeededToAddHDIContainer=Zbirnik HDI dodan
#XMSG: Success message
succeededToAddHDIContainerPlural=Zbirniki HDI dodani
#XMSG: Success message
succeededToDeleteHDIContainer=Zbirnik HDI odstranjen
#XMSG: Success message
succeededToDeleteHDIContainerPlural=Zbirniki HDI odstranjeni
#XFLD: Time data section sub headline
timeDataSection=Časovne tabele in dimenzije
#XFLD: Schemas list entry read audit log enabled indicator
readIndicator=Branje
#XFLD: Schemas list entry change audit log enabled indicator
changeIndicator=Sprememba
#XFLD: Remote sources section sub headline
allconnections=Dodelitev povezave
#XFLD: Remote sources section sub headline
localconnections=Lokalne povezave
#XFLD: Remote sources section sub headline for open Connectors
openConnectors=SAP Open Connectorji
#XFLD: User section sub headline
memberassignment=Dodelitev članov
#XFLD: User assignment section sub headline
userAssignment=Dodelitev uporabnika
#XFLD: User section Access dropdown Member
member=Član
#XFLD: User assignment section column name
user=Uporabniško ime
#XTXT: Selected role count
selectedRoleToolbarText=Izbrano: {0}
#XTIT: Space detail section connections title
detailsSectionConnections=Povezave
#XTIT: Space detail section data access title
detailsSectionDataAccess=Dostop do sheme
#XTIT: Space detail section time data title
detailsSectionGenerateData=Časovni podatki
#XTIT: Space detail section members title
detailsSectionUsers=Člani
#XTIT: Space detail section Users title
detailsSectionUsersTitle=Uporabniki
#XTIT: Out of Storage
insufficientStoragePopoverTitle=Prostor za shranjevanje ni več na voljo
#XTIT: Storage distribution
storageDistributionPopoverTitle=Zasedena shramba za disk
#XTXT: Out of Storage popover text
insufficientStorageText=Če želite ustvariti nov prostor, zmanjšajte dodeljeni prostor za shranjevanje drugega prostora ali izbrišite katerega od prostorov, ki ga ne potrebujete več. Skupni prostor sistema za shranjevanje lahko povečate, tako da prikličete Upravljaj paket.
#XMSG: Space id length warning
spaceIdLengthWarning=Preseženo je največje število znakov: {0}.
#XMSG: Space name length warning
spaceNameLengthWarning=Preseženo je največje število znakov: {0}.
#XMSG: Space id reserved prefix warning
spaceIdnWarningReservedPrefix=Ne uporabljajte predpone {0}, da preprečite morebitne spore.
#YMSE: Error while loading open SQL schemas
loadLocalSchemasError=Shem Open SQL ni bilo mogoče prenesti.
#YMSE: Error while creating open SQL schema
createLocalSchemaError=Sheme Open SQL ni bilo mogoče ustvariti.
#YMSE: Error while loading all remote connections
loadRemoteConnectionsError=Vseh oddaljenih povezav ni bilo mogoče naložiti.
#YMSE: Error while loading space details
loadSpaceDetailsError=Podrobnosti prostora ni bilo mogoče prenesti.
#YMSE: Error while deploying space details
deploySpaceDetailsError=Prostora ni bilo mogoče postaviti.
#YMSE: Error while copying space details
copySpaceDetailsError=Prostora ni bilo mogoče kopirati.
#YMSE: Error while loading storage data
loadStorageDataError=Podatkov prostora za shranjevanje ni bilo mogoče prenesti.
#YMSE: Error while loading all users
loadAllUsersError=Vseh uprabnikov ni bilo mogoče naložiti.
#YMSE: Failed to reset password
resetPasswordError=Gesla ni bilo mogoče ponastaviti.
#XMSG: Success message after updating space
resetPasswordSuccessMessage=Novo geslo za shemo nastavljeno
#YMSE: DP Agent-name too long
DBAgentNameError=Ime agenta za pripravo podatkov je predolgo.
#YMSE: Schema-name not valid.
schemaNameError=Ime sheme ni veljavno.
#YMSE: User name not valid.
UserNameError=Uporabniško ime ni veljavno.
#XTIT: Consumption by Storage Type
consumptionStorageTypeText=Poraba po tipu prostora za shranjevanje
#XTIT: Consumption by Schema
consumptionSchemaText=Poraba po shemi
#XTIT: Overall Consumption by schema
overallConsumptionSchemaText=Skupna poraba tabele po shemi
#XTIT: Overall Consumption by Table Type
overallConsumptionTableTypeText=Skupna poraba po tipu tabele
#XTIT: Tables
tableDetailsText=Podrobnosti tabele
#XTIT: Table Storage Consumption
tableStorageConsumptionText=Poraba prostora za shranjevanje tabele
#XFLD: Table Type label
tableTypeLabel=Tip tabele
#XFLD: Schema label
schemaLabel=Shema
#XFLD: reset table tooltip
resetTable=Ponastavi tabelo
#XFLD: In-Memory label in space monitor
inMemoryLabel=Pomnilnik
#XFLD: Disk label in space monitor
diskLabel=Prostor na disku
#XFLD: Yes
yesLabel=Da
#XFLD: No
noLabel=Ne
#XFLD: switch label: Want the data in this space to be consumable by default?
allowConsumptionByDefault=Želite, da je ta prostor mogoče privzeto porabljati?
#XFLD: Business Name
businessNameLabel=Poslovno ime
#XFLD: Refresh
refresh=Osveži
#XMSG: No filter results title
noFilterResultsTitle=Vaše nastavitve filtra očitno ne prikazujejo nobenih podatkov.
#XMSG: No filter results message
noFilterResultsMsg=Poskusite natančneje opredeliti svoje nastavitve filtra. Če nato še vedno ne vidite podatkov, ustvarite nekaj tabel v graditelju podatkov. Ko porabijo prostor za shranjevanje, jih boste lahko nadzirali tukaj.
#XMSG: Space monitoring Hana state red title
spaceMonitorHanaRedTitle=Baza podatkov časa izvajanja ni na voljo.
#XMSG: Space monitoring Hana state red message
spaceMonitorHanaRedMsg=Ker baza podatkov časa izvajanja ni na voljo, bodo določene funkcije onemogočene in na tej strani ni mogoče prikazati informacij.
#YMSE: Error while creating space schema user
createSpaceSchemaUserError=Uporabnika sheme prostora ni bilo mogoče ustvariti.
#YMSE: Error User name already exists
userAlreadyExistsError=Uporabniško ime že obstaja.
#YMSE: Error Authentication failed
authenticationFailedError=Preverjanje pristnosti ni uspelo.
#XMSG: Message strip message when a schema user is locked
userLockedMessageStrip=Uporabnik je zaklenjen zaradi preveč neuspelih prijav. Zahtevajte novo geslo, da odklenete uporabnika.
#XMSG: Success message after reset and unlock schema user
resetUnlockPasswordSuccessMessage=Novo geslo nastavljeno in uporabnik odklenjen
#XMSG: user is locked message
userLockedMessage=Uporabnik je zaklenjen.
#XCOL: Users table-view column Role
spaceRole=Vloga
#XCOL: Users table-view column Scoped Role
spaceScopedRole=Vloga v obsegu
#XCOL: Users table-view column Space Admin
spaceAdmin=Skrbnik prostora
#XFLD: User section dropdown value Viewer
viewer=Ogledovalec
#XFLD: User section dropdown value Modeler
modeler=Oblikovalnik
#XFLD: User section dropdown value Data Integrator
dataIntegrator=Integrator podatkov
#XFLD: User section dropdown value Space Administrator
spaceAdministrator=Skrbnik prostora
#XMSG: Success message -  Space Role updated successfully.
updateUserSpaceRoleSuccess=Vloga prostora posodobljena
#XMSG: Error message - Failed to update Space Role.
updateUserSpaceRoleError=Vloga prostora ni bila uspešno posodobljena.
#XFLD:
databaseUserNameSuffix=Pripona imena uporabnika zbirke podatkov
#XTXT: Space Schema password text
spaceSchemaPasswordText=Za vzpostavitev povezave s to shemo kopirajte svoje geslo. Če ste geslo pozabili, lahko vedno zahtevate novo.
#XFLD: Cloud Platform label in space schema dialog
cloudPlatformLabel=Cloud Platform
#XTXT: Cloud Platform text in space schema dialog
cloudPlatformText=Za vzpostavitev dostopa prek tega uporabnika omogočite porabo in kopirajte poverilnice. Če lahko poverilnice kopirate le brez gesla, ne pozabite pozneje dodati gesla.
#XFLD: enable consumption in CloudPlatform label in space schema dialog
enableConsumptionInCloudPlatformLabel=Omogočanje porabe v platformi Cloud Platform
#XFLD: Credentials for User-Provided Service label in space schema dialog
cloudPlatformCredentialsLabel=Poverilnice za storitev, ki jo zagotovi uporabnik:
#XFLD: Credentials for User-Provided Service (Without Password) label in space schema dialog
cloudPlatformCredentialsWithoutPasswordLabel=Poverilnice za storitev, ki jo zagotovi uporabnik (brez gesla):
#XFLD: Copy Credentials label in space schema dialog
copyCredentialsLabel=Kopiranje poverilnic brez gesla
#XFLD: Copy Credentials label in space schema dialog
copyFullCredentialsLabel=Kopiranje celotnih poverilnic
#XFLD: Copy Password label in space schema dialog
copyPasswordLabel=Kopiranje gesla
#XMSG: Credentials copied to clipboard
credentialsCopiedMessage=Poverilnice kopirane v odložišče
#XMSG: Password copied to clipboard
passwordCopiedMessage=Geslo kopirano v odložišče
#XMSG: create database user dialog title
createDatabaseUserDialogTitle=Ustvarite uporabnika zbirke podatkov
#XMSG: Database Users section title
databaseUsers=Uporabniki zbirke podatkov
#XMSG: Database User Details dialog title
databaseUserDetailsDialogTitle=Podrobnosti uporabnikov zbirke podatkov
#XFLD: database user read audit log
databaseUserAuditLogRead=Omogočanje zapisnikov revizije za postopke branja in hramba zapisnikov
#XFLD: database user change audit log
databaseUserAuditLogChange=Omogočanje zapisnikov revizije za postopke spremembe in hramba zapisnikov
#XMSG: Cloud Platform Access
cloudPlatformAccess=Dosotp do platforme Cloud Platform
#XFLD: Enable Consumption in Cloud Platform hint
enableConsumptionCloudPlatformHint=Vzpostavite dostop do svojega zbirnika infrastrukture postavitve SAP HANA (HDI) prek tega uporabnika zbirke podatkov. Za vzpostavitev povezave s svojim zbirnikom HDI mora biti SQL-modeliranje vključeno
#XFLD: Enable HDI Consumption
enableConsumptionCloudPlatform=Omogočanje HDI-porabe
#XFLD: Enable Consumption hint
enableConsumptionHint=Želite, da bi lahko druga orodja in aplikacije porabljali podatke v vašem prostoru?
#XFLD: Enable Consumption
enableConsumption=Omogočanje SQL-porabe
#XFLD: Enable Modeling
enableModeling=Omogočanje SQL-modeliranje
#XMSG: Privileges for Data Modeling
privilegesModeling=Pridobivanje in preoblikovanje podatkov
#XMSG: Privileges for Data Consumption
privilegesConsumption=Poraba podatkov za zunanja orodja
#XFLD: SQL Modeling
sqlModeling=SQL-modeliranje
#XFLD: SQL Consumption
sqlConsumption=SQL-poraba
#XFLD: enabled
enabled=Omogočeno
#XFLD: disabled
disabled=Onemogočeno
#XFLD: Edit Privileges
editPrivileges=Urejanje pravic
#XFLD: Open Database Explorer
openDBX=Odpiranje v raziskovalcu zbirk podatkov
#XFLD: create database user hint
databaseCreateHint=Upoštevajte, da po shranjevanju ne boste mogli več spremeniti uporabniškega imena.
#XFLD: Internal Schema Name
internalSchemaName=Ime interne sheme
#YMSE: Failed to load database users
loadDatabaseUserError=Prenos uporabnikov zbirke podatkov ni uspel
#YMSE: Failed to delete database user
deleteDatabaseUsersError=Izbris uporabnikov zbirke podatkov ni uspel
#XMSG: Success message after deleting database user
deleteDatabaseUserSuccess=Uporabnik zbirke podatkov izbrisan
#XMSG: Success message after deleting database users
deleteDatabaseUsersSuccess=Uporabniki zbirke podatkov izbrisani
#XMSG: Success message after creating database user
createDatabaseUserSuccessMessage=Uporabnik zbirke podatkov ustvarjen
#YMSE: Failed to create database user
createDatabaseUserError=Ustvaritev uporabnika zbirke podatkov ni uspela
#XMSG: Success message after updating database user
updateDatabaseUserSuccessMessage=Uporabnik zbirke podatkov posodobljen
#YMSE: Failed to update database user
updateDatabaseUserError=Posodobitev uporabnika zbirke podatkov ni uspela
#XFLD: HDI Consumption
hdiConsumption=HDI-poraba
#XTIT: Space detail section Database Access title
detailsSectionDatabaseAccess=Dostop do zbirke podatkov
#XTXT: Database users consumption hint
databaseUsersConsumptionHint=Nastavite, da se bodo podatki v tem prostoru privzeto porabljali. Modeli v graditeljih bodo samodejno dovolili porabo podatkov:
#XFLD: Default Consumption of Space Data:
defaultConsumptionOfSpaceData=Privzeta poraba podatkov prostora:
#XFLD: Database User Name
databaseUserName=Ime uporabnika zbirke podatkov
#XMSG: Database User creation validation error message
databaseUserValidationError=Očitno so nekatera polja neveljavna. Preverite obvezna polja, da boste lahko nadaljevali.
#XMSG: SQL modeling can’t be enabled since this user has been migrated.
migratedDatabaseUserHint=Pridobitve podatkov ni mogoče omogočiti, ker je bil ta uporabnik preseljen.
#XBUT: Remove Button Text
remove=Odstrani
#XBUT: Remove Spaces Button Text
removeSpaces=Odstranitev prostorov
#XBUT: Remove Objects Button Text
removeObjects=Odstranitev predmetov
#XMSG: No members have been added yet.
noMembersAssigned=Člani še niso bili dodani.
#XMSG: No users have been added yet.
noUsersAssigned=Uporabniki še niso bili dodani.
#XMSG: No database users have been created or your filter is not showing any data.
noDatabaseUsers=Uporabniki zbirke podatkov niso bili ustvarjeni ali pa vaš filter ne prikazuje podatkov.
#XMSG: Please enter a user name.
noDatabaseUsername=Vnesite uporabniško ime.
#XMSG: The user name is too long. Please use a shorter one.
databaseUsernameTooLong=Uporabniško ime je predolgo. Uporbite krajše.
#XMSG: No privileges have been enabled and this database user will have limited functionality. Do you still want to continue?
noPrivilegesConfirmationMessage=Pravice niso bile omogočene, ta uporabnik zbirke podatkov pa bo imel omejeno fukncionalnost. Res želite nadaljevati?
#XMSG: To enable audit logs for change operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogChangeMessage=Da omogočite zapisnike nadzora za postopke sprememb, mora biti omogočeno tudi pridobivanje podatkov. Ali želite to izvesti?
#XMSG: To enable audit logs for read operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogReadMessage=Da omogočite zapisnike nadzora za postopke branja, mora biti omogočeno tudi pridobivanje podatkov. Ali želite to izvesti?
#XMSG: To enable HDI consumption data ingestion and data consumption needs to be enabled. Do you want to enable all?
enableHDIConsumptionMessage=Da omogočite porabo HDI, morata biti omogočena tudi pridobivanje in poraba podatkov. Ali želite to izvesti?
#XMSG:
databaseUserPasswordText=Za vzpostavitev povezave s tem uporabnikom zbirke podatkov kopirajte svoje geslo. Če ste geslo pozabili, lahko vedno zahtevate novo.
#XTIT: Space detail section members title
detailsSectionMembers=Člani
#XMSG: New password set
newPasswordSet=Novo geslo nastavljeno
#XFLD: Data Ingestion
dataIngestion=Pridobivanje podatkov
#XFLD: Data Consumption
dataConsumption=Poraba podatkov
#XFLD: Privileges
privileges=Pravice
#XFLD: Enable Data ingestion
enableDataIngestion=Omogoči pridobivanje podatkov
#XMSG: Log the read and change operations for data ingestion.
databaseUserAuditHint=Zabeležite postopke branja in spreminjanja za pridobivanje podatkov.
#XMSG: Make your space data available in your HDI containers.
databaseUserHDIHint=Dajte podatke svojega prostora na voljo v svojih zbirnikih HDI.
#XFLD: Enable Data consumption
enableDataConsumption=Omogoči porabo podatkov
#XMSG: Allow other apps or tools to consume your space data.
enableDataConsumptionHint=Dovolite drugim aplikacijam ali orodjim, da porabijo podatke vašega prostora.
#XMSG: To set up access via this schema user copy the credentials to your user-provided service. In case you can only copy the credentials without a password make sure to add the password later.
databaseUserHDIConsumptionHint=Za vzpostavitev dostopa prek tega uporabnika zbirke podatkov kopirajte poverilnice za vašo storitev, ki jo zagotavlja uporabnik. Če lahko poverilnice kopirate le brez gesla, ne pozabite pozneje dodati gesla.
#XFLD: Data Flow Runtime Capacity label in space overview
dataFlowRuntimeCapacity=Kapaciteta časa izvajanja toka podatkov ({0}:{1} ur od {2} ur)
#XFLD: Could not load Data Flow Runtime Capacity
dataFlowRuntimeCapacityError=Kapacitete časa izvajanja toka podatkov ni bilo mogoče naložiti
#XMSG: User can grant data consumption to other users.
databaseUserConsumptionWithGrantOptionHint=Uporabnik lahko dovoli porabo podatkov drugim uporabnikom.
#XFLD: Enable data consumption with grant option
enableConsumptionWithGrantOption=Omogočenje porabe podatkov z možnostjo odobritve dovoljenja
#XMSG: To enable data consumption with grant option data consumption needs to be enabled. Do you want to enable both?
enableConsumptionWithGrantMessage=Za omogočenje porabe podatkov z možnostjo odobritve mora biti omogočena poraba podatkov. Ali želite omogočiti oboje?
#XFLD: Enable Automated Predictive Library (APL) and Predictive Analysis Library (PAL)
allowUsingScriptServerFunctionality=Omogoči Automated Predictive Library (APL) in Predictive Analysis Library (PAL)
#XMSG: User can use SAP HANA Cloud embedded machine learning functions.
allowUsingScriptServerFunctionalityHint=Uporabnik lahko uporablja funkcije strojnega učenja, vdelane v SAP HANA Cloud.
#XFLD: Password Policy
passwordPolicy=Pravilnik za gesla
#XMSG: Password Policy
passwordPolicyHint=Tukaj omogočite ali onemogočite konfiguriran pravilnik za gesla.
#XFLD: Enable Password Policy
enablePasswordPolicy=Omogočenje pravilnika za gesla
#XMSG: Read Access to the Space Schema
readAccessTitle=Dostop za branje sheme prostora
#XMSG: read access hint
readAccessHint=Omogočite uporabniku zbirke podatkov povezavo zunanjih orodij s shemo prostora in branje pogledov, ki so izpostavljeni za porabo.
#XFLD: Space Schema
spaceSchema=Shema prostora
#XFLD: Enable Read Access (SQL)
enableReadAccess=Omogočenje dostopa za branje (SQL)
#XMSG: Allow the user to grant read access to other users.
withGrantOptionHint=Omogočite uporabniku odobritev dostopa za branje drugim uporabnikom.
#XFLD: With Grant Option
withGrantOption=Z možnostjo odobritve
#XMSG: Make your space data available in your HDI containers.
enableHDIConsumptionHint=Omogočite podatke svojega prostora v zbirnikih HDI.
#XFLD: Enable HDI Consumption
enableHDIConsumption=Omogočanje HDI-porabe
#XMSG: Write Access to the User’s Open SQL Schema
writeAccessTitle=Dostop za pisanje v uporabnikovo shemo Open SQL
#XMSG: write access hint
writeAccessHint=Omogočite uporabniku zbirke podatkov povezavo zunanjih orodij z uporabnikovo shemo Open SQL za ustvarjanje entitet podatkov in pridobivanje podatkov za uporabo v prostoru.
#XFLD: Open SQL Schema
openSQLSchema=Shema Open SQL
#XFLD: Enable Write Access (SQL DDL & DML)
enableWriteAccess=Omogočenje dostopa za pisanje (SQL, DDL, & DML)
#XMSG: audit hint
auditHint=Beležite postopke branja in spreminjanja v shemi Open SQL.
#XMSG: data consumption hint
dataConsumptionHint=Privzeto izpostavite vse nove poglede v prostoru za porabo. Oblikovalci lahko prepišejo to nastavitev za posamezne poglede s stikalom “Izpostavitev za porabo” na stranski plošči za izdajo prikaza. Izberete lahko tudi oblike, v katerih so izpostavljeni pogledi.
#XFLD: Expose for Consumption by Default
exposeDataConsumption=Privzeto izpostavi za porabo
#XMSG: database users hint consumption hint
databaseUsersHint2New=Ustvarite uporabnike zbirke podatkov za povezavo zunanjih orodij s SAP Datasphere. Nastavite pravice, da se uporabnikom omogoči branje podatkov prostora, ustvarjanje entitet podatkov (DDL) in pridobivanje podatkov (DML) za uporabo v prostoru.
#XFLD: Read
read=Branje
#XFLD: Read (HDI)
readHDI=Branje (HDI)
#XFLD: Write
write=Pisanje
#XMSG: HDI Containers Hint
HDIContainersHint2=Omogočite dostop do zbirnikov infrastrukture postavitve SAP HANA (HDI) v svojem prostoru. Oblikovalci lahko uporabljajo HDI-komponente kot vire za poglede, HDI-odjemalci pa lahko dostopajo do vaših podatkov prostora.
#XMSG: Open info dialog
openDatabaseUserInfoDialog=Odprite informativno pogovorno okno
#XMSG: Database user is locked. Open info dialog to unlock
openLockedDatabaseUserInfoDialog=Uporabnik baze podatkov je zaklenjen. Za odklepanje odprite pogovorno okno
#XFLD: Table
table=Tabela
#XFLD: Space section partner configuration title
partnerConnectionConfigurationSectionTitle=Povezava partnerja
#XFLD: Space section partner configuration subtitle
partnerConnectionConfigurationSectionSubTitle=Konfiguracija povezave partnerja
#XFLD: Space section partner configuration hint
partnerConnectionConfigurationSectionHint=Določite lastno ploščico povezave partnerja, tako da dodate URL in ikono svojega iFrame. Ta konfiguracija je na voljo le za tega najemnika.
#XFLD: Table Name Field
partnerConnectionConfigurationName=Ime ploščice
#XFLD: Table iFrame URL field
partnerConnectionConfigurationiFrameURL=iFrame URL
#XFLD: Table iFrame Post Message Origin field
partnerConnectionConfigurationiFramePostMessageOrigin=Izvor sporočila objave iFrame
#XFLD: Table icon/image name
partnerConnectionConfigurationIcon=Ikona
#XMSG: Table no result found
partnerConnectionConfigurationNotfound=Nobena konfiguracija povezave partnerja ni bila najdena.
#XMSG: Partner Connection Confgurations are not available
partnerConnectionConfigurationNotAvailable=Konfiguracij povezav partnerja ni mogoče prikazati, če baza podatkov časa izvajanja ni na voljo.
#XFLD: Dialog Create Partner Connection Configuration
partnerConnectionConfigurationDialogTitleAdd=Kreirajte konfiguracijo povezave partnerja
#XFLD: Dialog Create Partner Connection Configuration Upload Icon
partnerConnectionConfigurationButtonUloadPartnerIcon=Ikona prenosa iz strežnika
#XFLD: Dialog Create Partner Connection Configuration Select
partnerConnectionConfigurationSelectIcon=Izbira (največja velikost 200 KB)
#XFLD: Dialog Create Partner Connection Configuration Enter a name
partnerConnectionConfigurationNamePlaceholder=Primer ploščice partnerja
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL
partnerConnectionConfigurationiFrameURLPlaceholder=http://www.example.com
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL Check
partnerConnectionConfigurationiFramePostMessageOriginPlaceholder=.example.com
#XFLD: Dialog Create Partner Connection Configuration Browse
partnerConnectionConfigurationBrowseButton=Brskanje
#XFLD: Create Partner Connection Configuration Msg
partnerConnectionConfigurationUploadMsg=Konfiguracija povezave partnerja je bila uspešno kreirana.
#XMSG: Error while deleting Partner Connection configuration(s)
partnerConnectionConfigurationDeleteError=Prišlo je do napake pri izbrisu konfiguracij povezave partnerja.
#XMSG: Partner Connection Configuration has been successfully deleted
partnerConnectionConfigurationDeleteSuccess=Konfiguracija povezave partnerja je bila uspešno izbrisana.
#XMSG: Error while retrieving Partner Connection Configurations
partnerConnectionConfigurationLoadError=Prišlo je do napake pri priklicu konfiguracij povezave partnerja.
#XMSG: Error fileSizeLimitExceeded
partnerConnectionConfigurationfileSizeLimitExceeded=Datoteke ni bilo mogoče prenesti v strežnik, ker presega največjo dovoljeno velikost 200 KB.
#XBUT: Create Partner Connection Configuration
partnerConnectionConfigurationButtonCreate=Kreiraj konfiguracijo povezave partnerja
#XBUT: Delete Partner Connection Configuration
partnerConnectionConfigurationButtonDelete=Izbriši konfiguracijo povezave partnerja.
#XMSG: Partner Tile Creation Failure
upsertPartnerTileFailed=Ploščice partnerja ni bilo mogoče kreirati.
#XMSG: Partner Tile Deletion Failure
deletePartnerTileFailed=Ploščice partnerja ni bilo mogoče izbrisati.
#XMSG: HANA cloud connector settings reset failure
resetHANASCCSettingsFailed=Ponastavitev nastavitev stranke povezovalnika za oblak SAP HANA ni uspela
#XFLD: Workload Class
workloadClass=Razred delovne obremenitve
#XFLD: Workload Management
workloadManagement=Upravljanje delovne obremenitve
#XFLD: Priority
workloadClassPriority=Prednost
#XMSG:
workloadManagementPriorityHint=Določite lahko prednostno razvrščanje tega prostora pri pošiljanju poizvedb za zbirko podatkov. Vnesite vrednost od 1 (najnižja prednost) do 8 (najvišja prednost). Če se prostori potegujejo za razpoložljive niti, bodo prostori z višjimi prednostmi izvedeni pred tistimi z nižjimi prednostmi.
#XMSG:
workloadClassPriorityHint=Določite lahko prednostno razvrščanje prostora od 0 (najnižja prednost) do 8 (najvišja prednost). Izjave prostora z visoko prednostjo so izvedene pred izjavami drugih prostorov z nižjo prednostjo. Privzeta prednost je 5. Ker je vrednost 9 je rezervirana za sistemske operacije, ni na voljo za prostor.
#XFLD: Statement Limits
workloadclassStatementLimits=Omejitve izjav
#XFLD: Workload Configuration
workloadConfiguration=Konfiguracija delovne obremenitve
#XMSG:
workloadClassStatementLimitsHint=Določite lahko maksimalno število (ali odstotek) niti in GB pomnilnika, ki ga lahko porabljajo izjave, ki se trenutno izvajajo v prostoru. Vnesete lahko poljubno vrednost ali odstotek med 0 (brez omejitev) in skupno količino pomnilnika in razpoložljivimi nitmi v najemniku. \n\n Upoštevajte, da lahko določitev omejitve niti poslabša učinkovitost delovanja. \n\n Omejitev pomnilnika pa povzroči, da izjave, ki bodo dosegle omejitev pomnilnika, ne bodo izvedene.
#XMSG:
workloadClassStatementLimitsDescription=Privzeta konfiguracija zagotavlja precejšnje omejitve resursov, hkrati pa preprečuje, da bi poljuben prostor preobremenil sistem.
#XMSG:
workloadClassStatementLimitCustomDescription=Nastavite lahko največje število niti in omejitve pomnilnika, ki ga lahko porabljajo izjave, ki se izvajajo sočasno v prostoru.
#XMSG:
totalStatementThreadLimitHelpText=Če nastavite omejitev niti prenizko, lahko to vpliva na učinkovitost delovanja izjave, pretirano visoke vrednosti ali 0 pa lahko prostoru dovolijo, da porabi vse razpoložljive sistemske niti.
#XMSG:
totalStatementMemoryLimitHelpText=Če nastavite omejitev pomnilnika prenizko, lahko to povzroči težave, ker na voljo ni dovolj pomnilnika, pretirano visoke vrednosti ali 0 pa lahko prostoru dovolijo, da porabi ves razpoložljiv sistemski pomnilnik.
#XMSG:
totalStatementThreadLimitHelpTextRestricted=Vnesite odstotek med 1 % in 70 % (ali ustrezno število) skupnega števila niti, ki so na voljo v vašem najemniku. Če nastavite omejitev niti prenizko, lahko to vpliva na učinkovitost delovanja izjave, pretirano visoke vrednosti pa lahko vplivajo na učinkovitost delovanja izjav v drugih prostorih.
#XMSG:
totalStatementThreadLimitHelpTextDynamic=Vnesite odstotek med 1 % in {0} % (ali ustrezno število) skupnega števila niti, ki so na voljo v vašem najemniku. Če nastavite omejitev niti prenizko, lahko to vpliva na učinkovitost delovanja izjave, pretirano visoke vrednosti pa lahko vplivajo na učinkovitost delovanja izjav v drugih prostorih.
#XMSG:
totalStatementMemoryLimitHelpTextNew=Vnesite vrednost ali odstotek med 0 (brez omejitev) in skupnim prostorom pomnilnika, ki je na voljo v vašem najemniku. Če nastavite omejitev pomnilnika prenizko, lahko to vpliva na učinkovitost delovanja izjave, pretirano visoke vrednosti pa lahko vplivajo na učinkovitost delovanja izjav v drugih prostorih.
#XFLD: Total Statement Thread Limit
totalStatementThreadLimit=Omejitev skupnih niti izjav
#XFLD: Segmented button label
workloadclassPercentage=%
#XFLD: Segmented button label
workloadclassGigaByte=GB
#XFLD, 80: Segmented button label
workloadclassThreads=Niti
#XFLD: Total Statement Memory Limit
totalStatementMemoryLimit=Omejitev skupnega pomnilnika izjav
#YMSE: Error while loading customer hana info
loadCustomerHanaInfoError=Prenos podatkov stranke za SAP HANA ni uspel.
#XMSG:
minimumLimitReached=Minimalna omejitev dosežena.
#XMSG:
maximumLimitReached=Maksimalna omejitev dosežena.
#XMSG: Name Taken for Technical Name
technical-name-taken=Povezava s tehničnim imenom, ki ste ga vnesli, že obstaja. Vnesite drugo ime.
#XMSG: Name Too long for Technical Name
technical-name-too-long=Tehnično ime, ki ste ga vnesli, presega 40 znakov. Vnesite ime z manj znaki.
#XMSG: Technical name field empty
technical-name-field-empty=Vnesite tehnično ime.
#XMSG: Generic error when user enters invalid characters for a field
field-invalid-characters=Za ime lahko uporabite le črke (a-z), številke (0-9) in podčrtaje (_).
#XMSG: Generic error when user enters invalid initial characters for a field
field-underscore-trim=Ime, ki ste ga vnesli, se ne sme začeti ali končati s podčrtajem (_).
#XFLD: Checkbox label for Enable Statement Limits
enableStatementLimits=Omogočenje omejitev izjave
#XFLD: Connection SubSection Settings Title in space details
connectionSettings=Nastavitve
#XMSG: Connections tool hint in Space details section
connectionsToolHint=Za ustvarjanje ali urejanje povezav odprite aplikacijo Povezave na stranski navigaciji ali kliknite tukaj:
#XBUT: Connection list Open Connection Tool Button
button-connectiontool=Pojdi v Povezave
#XFLD: Not deployed label on space tile
notDeployedLabel=Prostor še ni bil postavljen.
#XFLD: Not deployed additional text on space tile
notDeployedText=Postavite prostor.
#XFLD: Corrupt space label on space tile
corruptSpace=Prišlo je do napake.
#XFLD: Corrupt space additional text on space tile
corruptSpaceHint=Poskusite z vnovično postavitvijo ali se obrnite na podporo
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Podatki zapisnika nadzora
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Administrativni podatki
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Drugi podatki
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Podatki v prostorih
#XMSG: Unlock Space confirmation text
unlockSpaceConfirmation=Ali res želite odkleniti prostor?
#XMSG: Lock Space confirmation text
lockSpaceConfirmation=Ali res želite zakleniti prostor?
#XFLD: Lock
lock=Zakleni
#XFLD: Unlock
unlock=Odkleni
#XFLD: Locking
locking=Zaklepanje
#XMSG: Success message after locking space
lockSpaceSuccessMsg=Prostor zaklenjen
#XMSG: Success message after unlocking space
unlockSpaceSuccessMsg=Prostor odklenjen
#XMSG: Success message after locking spaces
lockSpacesSuccessMsg=Prostori zaklenjeni
#XMSG: Success message after unlocking spaces
unlockSpacesSuccessMsg=Prostori odklenjeni
#YMSE: Error while locking a space
lockSpaceError=Prostora ni mogoče zakleniti.
#YMSE: Error while unlocking a space
unlockSpaceError=Prostora ni mogoče odkleniti.
#XTIT: popup title Warning
confirmationWarningTitle=Opozorilo
#XMSG: Message strip message when a space was locked manually
manualLockedMessage=Prostor je bil ročno zaklenjen.
#XMSG: Message strip message when a space was locked due to the amount of generated audit logs
auditLogLockedMessage=Prostor je zaklenil sistem, ker dnevniki nadzora zavzemajo veliko GB prostora na disku.
#XMSG: Message strip message when a space was locked because the assigned quota was exceeded.
quotaLockedMessage=Prostor je zaklenil sistem, ker presega dodelitve pomnilnika ali prostora za shrambo na disku.
#XMSG: Unlock Spaces confirmation text
unlockSpacesConfirmation=Želite res odkleniti izbrane prostore?
#XMSG: Lock Spaces confirmation text
lockSpacesConfirmation=Želite res zakleniti izbrane prostore?
#XTIT: Scoped role editor dialog title
scopedRoleEditorDialogTitle=Urejevalnik vlog v obsegu
#XTIT: ECN Management title
ecnManagementTitle=Upravljanje prostora in vozlišča za elastično računanje
#XFLD: ECNs
ecns=Vozlišče za elastično računanje
#XFLD: ECN phase Ready
ecnReady=Pripravljeno
#XFLD: ECN phase Running
ecnRunning=Se izvaja
#XFLD: ECN phase Initial
ecnInitial=Nedosegljivo
#XFLD: ECN phase Starting
ecnStarting=Zagon
#XFLD: ECN phase Starting Failed
ecnStartingFailed=Začetek ni uspel
#XFLD: ECN phase Stopping
ecnStopping=Se zaustavlja
#XFLD: ECN phase Stopping Failed
ecnStoppingFailed=Zaustavitev ni uspela
#XBTN: Assign Button
assign=Dodelitev prostorov
#XBTN: Start Header-Button
start=Začetek
#XBTN: Update Header-Button
repair=Posodobitev
#XBTN: Stop Header-Button
stop=Zaustavitev
#XFLD: ECN hours remaining
ecnHoursRemaining=1000 preostalih ur
#XFLD: ECN block-hours remaining
ecnBlockHoursRemaining={0} Preostalih blokovnih ur
#XFLD: ECN block-hour remaining
ecnBlockHourRemaining={0} Preostala blokovna ura
#XTIT: ECN creation dialog title
ecnCreationDialogTitle=Kreiranje vozlišča za elastično računanje
#XTIT: ECN edit dialog title
ecnEditDialogTitle=Urejanje vozlišča za elastično računanje
#XTIT: ECN deletion dialog title
ecnDeletionDialogTitle=Izbris vozlišča za elastično računanje
#XTIT: ECN Space assignment dialog title
ecnSpaceAssignmentDialogTitle=Dodeli prostore
#XFLD: ECN ID
ECNIDLabel=Vozlišče za elastično računanje
#XTXT: Selected toolbar text
selectedToolbarText=Izbrano: {0}
#XTIT: Elastic Compute Nodes
ECNslong=Vozlišče za elastično računanje
#XTIT: Object assignment - Table header: Objects available
objectsAvailable=Število objektov
#XTIT: Object assignment - Dialog header text
selectObjects=Izberite prostore in objekte, ki jih želite dodeliti vozlišču za elastično računanje:
#XTIT: Object assignment - Table header title: Objects
objects=Objekti
#XTIT: Object assignment - Table header: Type
type=Tip
#XMSG: Message strip delete database user audit warning
deleteDatabaseUserWarningMessageStrip=Upoštevajte, da se bodo ob izbrisu uporabnika zbirke podatkov izbrisali vsi generirani vnosi v zapisniku nadzora. Če želite ohraniti zapisnike nadzora, jih izvozite, preden izbrišete uporabnika zbirke podatkov.
#XMSG: Message strip delete HDI container audit logs impact
deleteHDIContainerWarningMessageStrip=Upoštevajte, da se bodo z odstranitvijo zbirnika HDI iz prostora izbrisali vsi generirani vnosi v zapisniku nadzora. Če želite ohraniti zapisnike nadzora, razmislite o njihovem izvozu, preden odstranite zbirnik HDI.
#XTXT: All audit logs
allAuditLogs=Generirani so vsi vnosi v zapisniku nadzora za prostor
#XMSG: Message strip disable audit warning
disableAuditWarningMessageStrip=Upoštevajte, da bo onemogočitev smernice za nadzor (postopki branja ali spreminjanja) povzročila izbris vseh njenih vnosov v zapisniku nadzora. Če želite ohraniti vnose v zapisniku nadzora, razmislite o njihovem izvozu, preden onemogočite smernico za nadzor.
#XTIT: No Spaces assigned to ECN Illustration title
noSpacesECNTitle=Prostori ali objekti še niso dodeljeni
#XTIT: No Spaces assigned to ECN Illustration description
noSpacesECNDescription=Za začetek dela z vašim vozliščem za elastično računanje mu najprej dodelite prostor ali objekte.
#XTIT: No Spaces Illustration title
noSpacesTitle=Noben prostor še ni ustvarjen
#XTIT: No Spaces Illustration description
noSpacesDescription=Za začetek pridobivanja podatkov ustvarite prostor.
#XTIT: No Spaces Illustration title
noDeletedSpacesTitle=Koš je prazen
#XTIT: No Spaces Illustration description
noDeletedSpacesDescription=Tu lahko obnovite svoje izbrisane prostore.
#XTXT:  {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDatabaseUsers=Ko je prostor postavljen, bodo naslednji uporabniki baze podatkov {0} izbrisani in jih ne bodo mogoče obnoviti:
#XTIT: Delete database users
deleteDatabaseUsersTitle=Izbriši uporabnikov baze podatkov
#YMSE: Error if ecn id already exists
ecnIDInputDuplicateError=ID že obstaja.
#YMSE: Error if ecn id contains invalid characters
ecnIDInputInvalidError=Prosimo, male črke a–z in števila 0–9.
#YMSE: Error if ecn id is too short
ecnIDInputTooShortError=ID mora biti dolg vsaj {0} znakov.
#XMSG: ecn id length warning
ecnIdLengthWarning=Preseženo je največje število znakov: {0}.
#XFLD: open System Monitor
systemMonitor=Monitor sistema
#XFLD: open ECN schedule dialog menu entry
schedule=Časovni načrt
#XFLD: open create ECN schedule dialog
createSchedule=Ustvarjenje časovnega načrta
#XFLD: open change ECN schedule dialog
changeSchedule=Urejanje časovnega načrta
#XFLD: open delete ECN schedule dialog
deleteSchedule=Izbriši časovni načrt
#XFLD: open self assign ECN schedule dialog
selfAssignSchedule=Dodeli časovni načrt meni
#XFLD: open pause ECN schedule dialog
pauseSchedule=Začasno prekini časovni načrt
#XFLD: open resume ECN schedule dialog
resumeSchedule=Nadaljuj časovni načrt
#XFLD: View Logs
viewLogs=Prikaz dnevnikov
#XFLD: Compute Blocks
computeBlocks=Bloki izračuna
#XFLD: Memory label in ECN creation dialog
ecnMemory=Pomnilnik (GB)
#XFLD: Storage label in ECN creation dialog
ecnStorage=Shramba (GB)
#XFLD: Number of CPU label in ECN creation dialog
ecnCPUCount=Število CPU-jev
#XFLD: ECN updated by label
changedBy=Spremenil
#XFLD: ECN updated on label
changedOn=Spremenjeno dne
#XMSG: Success message after creating a Elastic Compute Node
createEcnSuccessMessage=Vozlišče elastičnega izračunavanja je ustvarjeno
#YMSE: Error while creating a Elastic Compute Node
createEcnError=Vozlišča elastičnega izračunavanja ni bilo mogoče ustvariti
#XMSG: Success message after updating a Elastic Compute Node
updateEcnSuccessMessage=Vozlišče elastičnega izračunavanja je posodobljeno
#XMSG: Error while updating a Elastic Compute Node
udpateEcnError=Vozlišča elastičnega izračunavanja ni bilo mogoče posodobiti
#XMSG: Success message after deleting a Elastic Compute Node
deleteEcnSuccessMessage=Vozlišče elastičnega izračunavanja je izbrisano
#YMSE: Error while deleting a Elastic Compute Node
deleteEcnError=Vozlišča elastičnega izračunavanja ni bilo mogoče izbrisati
#XMSG: Success message after starting a Elastic Compute Node
startingEcnSuccessMessage=Zagon vozlišča elastičnega izračunavanja
#XMSG: Success message after stopping a Elastic Compute Node
stoppingEcnSuccessMessage=Zaustavljanje vozlišča elastičnega izračunavanja
#YMSE: Error while starting a Elastic Compute Node
startingEcnError=Vozlišča elastičnega izračunavanja ni bilo mogoče zagnati
#YMSE: Error while stopping a Elastic Compute Node
stoppingEcnError=Vozlišča elastičnega izračunavanja ni bilo mogoče zaustaviti
#XBUT: Add Object button for an ECN
assignObjects=Dodaj objekte
#XFLD: Enable auto object assignment label
enableAutoObjAssignment=Samodejna dodelitev vseh objektov
#XFLD: object type label to be assigned
objectTypeLabel=Vrsta (semantična uporaba)
#XFLD: assigned object type label
assignedObjectTypeLabel=Vrsta
#XFLD: technical name label
TechnicalNameLabel=Tehnično ime
#XFLD: Object selecttion ECN label
objectSelectionECNLabel=Izberite objekte, ki jih želite dodeliti vozlišču elastičnega izračunavanja
#XTIT: Add objects dialog title
assignObjectsTitle=Dodelitev objektov od
#XFLD: object label with object count
objectLabel=Objekt
#XMSG: No objects available to add message.
noObjectsToAssign=Noben objekt ni na voljo za dodelitev.
#XMSG: No objects assigned message.
noAssignedObjects=Noben objekt ni dodeljen.
#XTIT: Remove objects popup title
removeObjectsOrSpacesConfirmationTitle=Opozorilo
#XTIT: Delete ECN popup button and title
deleteECNConfirmationButtonAndTitle=Izbris
#XMSG: Remove objects popup text
removeObjectsConfirmation=Res želite odstraniti izbrane objekte?
#XMSG: Remove spaces popup text
removeSpacesConfirmation=Res želite odstraniti izbrane prostore?
#XBUT: Confirmation button for objects dialog
removeObjectsOrSpacesConfirmationBtn=Odstrani prostore
#XMSG: Success message after removing objects
removeObjectsSuccessMessage=Izpostavljeni objekti so odstranjeni
#XMSG: Success message after assigning objects
assignObjectsSuccessMessage=Izpostavljeni objekti so dodeljeni
#XFLD: auto object assignment label for space tile
autoObjAssignmentLabel=Vsi izpostavljeni objekti
#XFLD: Spaces tab label
spacesTabLabel=Prostori
#XFLD: Exposed objects tab label
consumptionObjectsTabLabel=Izpostavljeni objekti
#XMSG: Success message after unassigning spaces
spacesUnassignedSuccessMessage=Prostori so odstranjeni
#XMSG: Success message after unassigning space
spaceUnassignedSuccessMessage=Prostor je odstranjen
#YMSE: Error while assigning / unassigning spaces
spacesAssignUnassignError=Prostorov ni bilo mogoče dodeliti ali odstraniti.
#YMSE: Error while removing objects
removeObjectsError=Objektov ni mogoče dodeliti ali odstraniti.
#YMSE: Error while removing object
removeObjectError=Objekta ni mogoče dodeliti ali odstraniti.
#YMSE: Error when saving the ecn with invalid value
invalidSizingPlanError=Prehodno izbrano število več ni veljavno. Izberite veljavno število.
#YMSE: Error when saving the ecn with invalid value
invalidPerformanceClassError=Izberite veljaven razred učinkovitosti.
#YMSE: Error when saving the ecn with invalid value
performanceClassNotAvailableError=Predhodno izbrani razred učinkovitosti "{0}" trenutno ni veljaven. Izberite veljaven razred učinkovitosti.
#XTXT: Delete ECN confirmation text
deleteConfirmationTxtECN=Želite izbrisati vozlišče za elastično računanje?
#XFLD: tooltip for ? button
help=Pomoč
#XFLD: ECN edit button label
editECN=Konfiguracija
#XFLD: Technical type label for ERModel
DWC_ERMODEL=Model odnosov med entitetami
#XFLD: Technical type label for Local Table
DWC_LOCAL_TABLE=Lokalna tabela
#XFLD: Technical type label for Remote Table
DWC_REMOTE_TABLE=Oddaljena tabela
#XFLD: Technical type label for Analytic Model
DWC_ANALYTIC_MODEL=Analitični model
#XFLD: Technical type label for Task Chain
DWC_TASKCHAIN=Veriga naloge
#XFLD: Technical type label for Data Flow
DWC_DATAFLOW=Tok podatkov
#XFLD: Technical type label for Replication Flow
DWC_REPLICATIONFLOW=Tok podvajanja
#XFLD: Technical type label for Transformation Flow
DWC_TRANSFORMATIONFLOW=Tok preoblikovanja
#XFLD: Technical type label for Intelligent Lookup
DWC_IDT=Pametno iskanje
#XFLD: Technical type label for Repository
DWC_REPOSITORY_PACKAGE=Odložišče
#XFLD: Technical type label for Enterprise Search
DWC_ESMODEL=Enterprise Search
#XFLD: Technical type label for View
DWC_VIEW=Pogled
#XFLD: Technical type label for Data Product
DWC_DATAPRODUCT=Podatkovni proizvod
#XFLD: Technical type label for Data Access Control
DWC_DAC=Kontrolnik za dostop do podatkov
#XFLD: Technical type label for Folder
DWC_FOLDER=Mapa
#XFLD: Technical type label for Data Entity
DWC_BUSINESS_ENTITY=Poslovna entiteta
#XFLD: Technical type label for Business Entity Variant
DWC_BUSINESS_ENTITY_VARIANT=Varianta poslovne entitete
#XFLD: Technical type label for Responsibility Scenario
DWC_AUTH_SCENARIO=Scenarij odgovornosti
#XFLD: Technical type label for Fact Model
DWC_FACT_MODEL=Model podatkov
#XFLD: Technical type label for Transformation Flow
DWC_PERSPECTIVE=Perspektiva
#XFLD: Technical type label for Consumption Model
DWC_CONSUMPTION_MODEL=Model porabe
#XFLD: Technical type label for Remote Connection
DWC_REMOTECONNECTION=Oddaljena povezava
#XFLD: Technical type label for Fact Model Variant
DWC_FACT_MODEL_VARIANT=Varianta modela podatkov
#XMSG: Schedule created alert message
createScheduleSuccess=Časovni razpored ustvarjen
#XMSG: Schedule updated alert message
updateScheduleSuccess=Časovni razpored posodobljen
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Časovni razpored izbrisan
#XMSG: Schedule takeover success message
ownerChangeScheduleSuccess=Plan vam je dodeljen
#XMSG: Message for starting pausing of schedule
pauseScheduleStarted=Časovni načrt 1 bo začasno prekinjen
#XMSG: Message for starting resuming of schedule
resumeScheduleStarted=Časovni načrt 1 se bo nadaljeval
#XFLD: Segmented button label
availableSpacesButton=Na voljo
#XFLD: Segmented button label
selectedSpacesButton=Izbrano
#XFLD: Visit website button text
visitWebsite=Obišči spletno mesto
#XMSG: Warning message on unselecting source language
sourceLanguageRemovalWarning=Predhodno izbrani izvorni jezik bo odstranjen.
#XBUT: enable translation confirmation button
enableTranslationConfirmationBtn=Omogoči
#XFLD: ECN performance class label
performanceClassLabel=Razred učinkovitosti
#XTXT performance class memory text
memoryText=Pomnilnik
#XTXT performance class compute text
computeText=Izračun
#XTXT performance class high-compute text
highComputeText=Visoki izračun
#XBUT: Recycle Bin Button Text
recycleBin=Koš
#XBUT: Restore Button Text
restore=Obnovi
#XMSG: Warning message for new Workload Management UI
priorityWarning=To območje je samo za branje. Prednostno razvrščanje prostora lahko spremenite v območju Sistem/Konfiguracija/Upravljanje delovne obremenitve.
#XMSG: Warning message for new Workload Management UI
workloadWarning=To območje je samo za branje. Konfiguracijo delovne obremenitve prostora lahko spremenite v območju Sistem/Konfiguracija/Upravljanje delovne obremenitve.
#XCOL: Space table-view column: Apache Spark vCPUs
sparkManagementVCPUs=vCPU-ji Apache Spark
#XCOL: Space table-view column: Apache Spark Memory (GB)
sparkManagementMemory=Pomnilnik Apache Spark (GB)
#XCOL: Space type (from Space Capability) for Data Ingestion Spaces
ingestionSpaceType=Pridobivanje podatkovnega produkta
#XFLD: Deployment in progress nodatatext for spark table
sparkTableDeploymentInProgress=Podatki niso na voljo, ker se prostor trenutno postavlja
#XFLD: Runtime data being loaded nodatatext for spark table
sparkTableDeploymentLoading=Podatki niso na voljo, ker se prostor trenutno prenaša
#XCOL: Space type (from Space Capability) for BW Private Cloud Edition
BWPCESpaceType=SAP BW
#XBUT: Go to Instance Mappings Button
navToMappingsButton=Urejanje preslikav primerka
#XFLD: SAP HANA Data Lake Storage label if less than 1GB
dataLakeStorageUsed=< 1 GB
