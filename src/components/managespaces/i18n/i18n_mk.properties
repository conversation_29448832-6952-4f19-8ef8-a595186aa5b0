#DO NOT REMOVE________________________
#XFLD: Breadcrumb for space monitoring page
spaceMonitorBreadcrumb=Следење
#XTXT: Type name for spaces in browser tab page title
space=Простор
#_____________________________________
#XFLD: Spaces label in
spaces=Простори
#XFLD: Manage plan button text
manageQuotaButtonText=Управувај со план
#XBUT: Manage resources button
manageResourcesButton=Управувај со ресурси
#XFLD: Create space button tooltip
createSpace=Создај простор
#XFLD: Create
create=Создај
#XFLD: Deploy
deploy=Примени
#XFLD: Page
page=Страница
#XFLD: Cancel
cancel=Откажи
#XFLD: Update
update=Ажурирај
#XFLD: Save
save=Зачувај
#XFLD: OK
ok=Во ред
#XFLD: days
days=Денови
#XFLD: Space tile edit button label
edit=Уреди
#XFLD: Auto Assign all objects to space
autoAssign=Додели автоматски
#XFLD: Space tile open monitoring button label
openMonitoring=Следи
#XFLD: Delete
delete=Избриши
#XFLD: Copy Space
copy=Копирај
#XFLD: Close
close=Затвори
#XCOL: Space table-view column status
status=Статус
#XFLD: Space status active
activeLabel=Активно
#XFLD: Space status locked
lockedLabel=Заклучено
#XFLD: Space status critical
criticalLabel=Критично
#XFLD: Space status cold
coldLabel=Неактивно
#XFLD: Space status deleted
deletedLabel=Избришано
#XFLD: Space status unknown
unknownLabel=Непознато
#XFLD: Space status ok
okLabel=Умерено
#XFLD: Database user expired
expired=Истечено
#XFLD: deployed
deployed=Применето
#XFLD: not deployed
notDeployed=Не е применето
#XFLD: changes to deploy
changesToDeploy=Промени што треба да се применат
#XFLD: pending
pending=Се применува
#XFLD: designtime error
designtimeError=Грешка во времето на дизајнирање
#XFLD: runtime error
runtimeError=Грешка во времето на извршување
#XFLD: Space created by label
createdBy=Создадено од
#XFLD: Space created on label
createdOn=Создадено на
#XFLD: Space deployed on label
deployedOn=Применето на
#XFLD: Space ID label
spaceID=ИД на просторот
#XFLD: Priority label
priority=Приоритет
#XFLD: Space Priority label
spacePriority=Приоритет на просторот
#XFLD: Space Configuration label
spaceConfiguration=Конфигурација на просторот
#XFLD: Not available
notAvailable=Недостапно
#XFLD: WorkloadType default
default=Стандардно
#XFLD: WorkloadType custom
custom=Приспособено
#XFLD: Data Lake option label; refers to component SAP HANA Cloud, Data Lake
dataLakeAccess=Пристап до езерото од податоци
#XFLD: Translation label
translationLabel=Превод
#XFLD: Source language label
sourceLanguageLabel=Изворен јазик
#XFLD: Translation CheckBox label
translationCheckBox=Овозможи превод
#XTXT: Popover hint that space has to be deployed to show user details
openDatabaseUserHintPopoverText=Применете го просторот за да пристапите до деталите за корисникот.
#XTXT: Popover hint that space has to be deployed to open database explorer
openDatabaseExplorerHintPopoverText=Применете го просторот за да го отворите пребарувачот на базата со податоци.
#XTXT: Text for when the Data Lake check box is disabled
dataLakeOptionDisabled=Не можете да го користите овој простор за да пристапите до езерото од податоци бидејќи друг простор веќе го користи.
#XFLD: Data Lake check box text; refers to component SAP HANA Cloud, Data Lake
dataLakeCheckBox=Користете го овој простор за да пристапите до езерото од податоци.
#XFLD: Space Priority minimum label extension
low=Ниско
#XFLD: Space Priority maximum label extension
high=Високо
#XFLD: Space name label
spaceName=Назив на просторот
#XFLD: Enable deploy objects checkbox
enableDeployObjects=Примени објекти
#XTIT: Copy spaces dialog title
copySpaceDialogTitle=Копирај {0}
#XTXT Human readable text for unselected source language
unSelectedSourceLanguage=(Не е избрано)
#XTXT Human readable text for language code "af"
af=Afrikaans
#XTXT Human readable text for language code "ar"
ar=Арапски
#XTXT Human readable text for language code "bg"
bg=Бугарски
#XTXT Human readable text for language code "ca"
ca=Каталонски
#XTXT Human readable text for language code "zh"
zh=Поедноставен кинески
#XTXT Human readable text for language code "zf"
zf=Кинески
#XTXT Human readable text for language code "hr"
hr=Хрватски
#XTXT Human readable text for language code "cs"
cs=Чешки
#XTXT Human readable text for language code "cy"
cy=Велшки
#XTXT Human readable text for language code "da"
da=Дански
#XTXT Human readable text for language code "nl"
nl=Холандски
#XTXT Human readable text for language code "en-UK"
en-UK=Англиски (Обединето Кралство)
#XTXT Human readable text for language code "en"
en=Англиски (Соединети Американски Држави)
#XTXT Human readable text for language code "et"
et=Естонски
#XTXT Human readable text for language code "fa"
fa=Персиски
#XTXT Human readable text for language code "fi"
fi=Фински
#XTXT Human readable text for language code "fr-CA"
fr-CA=Француски (Канада)
#XTXT Human readable text for language code "fr"
fr=Француски
#XTXT Human readable text for language code "de"
de=Германски
#XTXT Human readable text for language code "el"
el=Грчки
#XTXT Human readable text for language code "he"
he=Хебрејски
#XTXT Human readable text for language code "hi"
hi=Хинди
#XTXT Human readable text for language code "hu"
hu=Унгарски
#XTXT Human readable text for language code "is"
is=Исландски
#XTXT Human readable text for language code "id"
id=Индонезиски
#XTXT Human readable text for language code "it"
it=Италијански
#XTXT Human readable text for language code "ja"
ja=Јапонски
#XTXT Human readable text for language code "kk"
kk=Казашки
#XTXT Human readable text for language code "ko"
ko=Корејски
#XTXT Human readable text for language code "lv"
lv=Летонски
#XTXT Human readable text for language code "lt"
lt=Литвански
#XTXT Human readable text for language code "ms"
ms=Малајски
#XTXT Human readable text for language code "no"
no=Норвешки
#XTXT Human readable text for language code "pl"
pl=Полски
#XTXT Human readable text for language code "pt"
pt=Португалски (Бразил)
#XTXT Human readable text for language code "pt-PT"
pt-PT=Португалски (Португалија)
#XTXT Human readable text for language code "ro"
ro=Романски
#XTXT Human readable text for language code "ru"
ru=Руски
#XTXT Human readable text for language code "sr"
sr=Српски
#XTXT Human readable text for language code "sh"
sh=Српскохрватски
#XTXT Human readable text for language code "sk"
sk=Словачки
#XTXT Human readable text for language code "sl"
sl=Словенечки
#XTXT Human readable text for language code "es"
es=Шпански
#XTXT Human readable text for language code "es-MX"
es-MX=Шпански (Мексико)
#XTXT Human readable text for language code "sv"
sv=Шведски
#XTXT Human readable text for language code "th"
th=Тајландски
#XTXT Human readable text for language code "tr"
tr=Турски
#XTXT Human readable text for language code "uk"
uk=Украински
#XTXT Human readable text for language code "vi"
vi=Виетнамски
#XTIT: Delete spaces confirmation title
deleteConfirmationHeaderText=Избриши ги просторите
#XTXT: Logically delete space delete confirmation text with space name
logicallyDeleteConfirmationBodyTextWithSpaceName=Дали сигурно сакате да го преместите просторот „{0}“ во корпата за отпадоци?
#XTXT: Logically dpace delete confirmation text for multiple spaces
logicallyDeleteConfirmationBodyTextMultiple=Дали сигурно сакате да преместите {0} избрани простори во корпата за отпадоци?
#XTXT: Delete space delete confirmation text with space name
deleteConfirmationBodyTextWithSpaceName=Дали сигурно сакате да го избришете просторот „{0}“? Дејството не може да се поништи.
#XTXT: Delete space delete confirmation text for multiple spaces
deleteConfirmationBodyTextMultiple=Дали сигурно сакате да ги избришете {0} избрани простори? Дејството не може да се поништи. Следнава содржина ќе се {1} избрише:
#XTXT: permanently
permanently=трајно
#XTXT: {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDetail=Следнава содржина ќе се {0} избрише и не може да се обнови:
#XTXT: Hint Delete space confirmation text
deleteConfirmationHint=Напишете {0} за да го потврдите бришењето.
#XTXT: Delete space confirmation error check spelling
deleteConfirmationErrorCheckSpelling=Проверете го правописот и обидете се повторно.
#XTXT: All Spaces
allSpaces=Сите простори
#XTXT: All data
allData=Сите објекти и податоци што се содржани во просторот
#XTXT: All connections
allConnections=Сите врски што се дефинирани во просторот
#XFLD: Space tile selection box tooltip
clickToSelect=Кликнете за да изберете
#XTXT: All database users
allDatabaseUsers=Сите објекти и податоци содржани во шемата Open SQL што се поврзани со просторот
#XFLD: remove members button tooltip
deleteUsers=Отстрани членови
#XTXT: Space long description text
description=Опис (најмногу 4.000 знаци)
#XFLD: Add Members button tooltip
addUsers=Додај членови
#XFLD: Add Users button tooltip
addUsersTooltip=Додај корисници
#XFLD: Edit Users button tooltip
editUsersTooltip=Уреди корисници
#XFLD: Remove Users button tooltip
removeUsersTooltip=Отстрани корисници
#XFLD: Searchfield placeholder
filter=Пребарај
#XCOL: Users table-view column health
health=Здравје
#XCOL: Users table-view column access
access=Пристап
#XFLD: No user found nodatatext
noDataText=Не е најден корисник
#XTIT: Members dialog title
selectUserDialogTitle=Додај членови
#XTIT: User dialog title
addUserDialogTitle=Додај корисници
#XTIT: Delete connections confirmation title
deleteConnectionConfirmationHeaderText=Избриши ги врските
#XTIT: Delete single connection confirmation title
deleteConnectionConfirmationHeaderTextSingle=Избриши ја врската
#XTXT: Delete connection delete confirmation text
deleteConnectionConfirmationBodyText=Дали сигурно сакате да ги избришете избраните врски? Ќе бидат трајно отстранети.
#XTIT: Remote source dialog title
selectRemoteConnectionDialogTitle=Избери врски
#XTIT: Share connection dialog title
connectionSharingDialogTitle=Сподели ја врската
#XTIT: Connections shared from other spaces table title
sharedConnectionsFromOtherSpaces=Споделени врски
#XFLD: Add remote source button tooltip
addRemoteConnections=Додај врски
#XFLD: Delete remote source button tooltip
deleteRemoteConnections=Отстрани врски
#XFLD: Share remote source button tooltip
shareConnections=Сподели врски
#XFLD: Tile-layout tooltip
tileLayout=Распоред на плочката
#XFLD: Table-layout tooltip
tableLayout=Распоред на табелата
#XMSG: Success message after creating space
createSpaceSuccessMessage=Просторот е создаден
#XMSG: Success message after copying space
copySpaceSuccessMessage=Просторот „{0}“ се копира во просторот „{1}“
#XMSG: Success message after deploying space
deploymentSuccessMessage=Применувањето на просторот започна
#XMSG: Success message after Apache Spark update started
sparkUpdateStartedSuccessMessage=Започна ажурирањето на Apache Spark
#YMSE: Error while starting Apache Spark update
sparkUpdateStartedErrorMessage=Ажурирањето на Apache Spark не успеа
#XMSG: Success message after updating space
updateSettingsSuccessMessage=Деталите за просторот се ажурирани
#XMSG: Success message after unlocking space
unlockSpaceSuccessMessage=Просторот е привремено отклучен
#XMSG: Success message after deleting a single space
deleteSpaceSuccessMessage=Просторот е избришан
#XMSG: Success message after deleting multiple spaces
deleteSpacesSuccessMessage=Просторите се избришани
#XMSG: Success message after restoring a single space
restoreSpaceSuccessMessage=Просторот е обновен
#XMSG: Success message after restoring multiple spaces
restoreSpacesSuccessMessage=Просторите се обновени
#YMSE: Error while updating settings
updateSettingsFailureMessage=Поставките за просторот не може да се ажурираат.
#YMSE: Error while updating data lake option
dataLakeEnabledInOtherSpace=Езерото од податоци е веќе доделено на друг простор. Само еден простор може да пристапи до податоците истовремено.
#YMSE: Error while updating data lake option
virtualTablesExists=Не можете да го поништите доделувањето на езерото од податоци од овој простор бидејќи сè уште постојат зависности во врска со виртуелните табели*. Избришете ги виртуелните табели за да го поништите доделувањето на податоците од овој простор.
#YMSE: Error while unlocking space
unlockSpaceFailureMessage=Просторот не може да се отклучи.
#YMSE: Error while creating space
createSpaceError=Просторот не може да се создаде.
#YMSE: Error while creating duplicate space
createDuplicateSpaceError=Веќе постои простор со назив {0}.
#YMSE: Error while deleting a single space
deleteSpaceError=Просторот не може да се избрише.
#YMSE: Error details while deleting a single space
deleteSpaceDetailedError=Просторот „{0}“ повеќе не работи соодветно. Обидете се повторно да го избришете. Ако сè уште не работи, побарајте од администраторот да го избрише просторот или да отвори билет.
#YMSE: Error while deleting a single space in files
deleteSpaceInFilesError=Просторот во Датотеките не може да се избрише.
#YMSE: Error while deleting a single space users
deleteSpaceUsersError=Корисниците не може да се отстранат.
#YMSE: Error while deleting a single space schema
deleteSpaceSchemasError=Шемите не може да се отстранат.
#YMSE: Error while deleting a single space connections
deleteSpaceConnectionsError=Врските не може да се отстранат.
#YMSE: Error while deleting a single space data
deleteSpaceDataError=Податоците за просторот не може да се избришат.
#YMSE: Error while deleting multiple spaces
deleteSpacesError=Просторите не може да се избришат.
#YMSE: Error while restoring a single space
restoreSpaceError=Просторот не може да се обнови.
#YMSE: Error while restoring multiple spaces
restoreSpacesError=Просторите не може да се обноват.
#YMSE: Error while creating users
createUsersError=Корисниците не може да се додадат.
#YMSE: Error while removing users
removeUsersError=Не можеме да ги отстраниме корисниците.
#YMSE: Error while removing user
removeUserError=Не може да се отстрани корисникот.
#YMSE: Error assigning roles to a user
roleAssignUnAssignError=Не можеме да го додадеме корисникот во избраната улога со одреден опсег. \n\n Не можете да се додадете себеси во улогата со одреден опсег. Можете да побарате од администраторот да ве додаде во улогата со одреден опсег.
#YMSE: Error assigning user to the space
userAssignError=Не можеме да му го доделиме просторот на корисникот. \n\n На корисникот веќе му се доделени максимален број дозволени простори (100) во рамките на улогите со опсег.
#YMSE: Error assigning users to the space
usersAssignError=Не можеме да им го доделиме просторот на корисниците. \n\n На корисникот веќе му се доделени максимален број дозволени простори (100) во рамките на улогите со опсег.
#YMSE: Error while fetching users
fetchUserWithScopedRolesError=Не можеме да ги повикаме корисниците. Обидете се повторно подоцна.
#YMSE: Error while fetching scoped roles
fetchScopedRolesError=Не можеме да ги повикаме улогите со одреден опсег.
#YMSE: Error while fetching members
fetchUserError=Не може да се земат членовите. Обидете се повторно подоцна.
#YMSE: Error while loading run-time database
loadRuntimeError=Не можеме да ги вчитаме информациите од базата со податоци за времето на извршување.
#YMSE: Error while loading spaces
loadSpacesError=Се извинуваме, настана грешка при обидот да се повикаат просторите.
#YMSE: Error while loading haas resources
loadStorageError=Се извинуваме, настана грешка при обидот да се повикаат податоците за складот.
#YMSE: Error no data could be loaded
loadDataError=Се извинуваме, настана грешка при обидот да се повикаат податоците.
#XFLD: Click to refresh storage data
clickToRefresh=Кликнете тука за да се обидете повторно.
#XTIT: Delete space popup title
deleteSpacePopupTitle=Избриши го просторот
#XCOL: Spaces table-view column name
name=Назив
#XCOL: Spaces table-view deployment status
deploymentStatus=Статус на применувањето
#XFLD: Disk label in space details
storageLabel=Диск (GB)
#XFLD: In-Memory label in space details
ramLabel=Меморија (GB)
#XFLD: Memory label on space card
memory=Меморија за складирање
#XFLD: Storage Assignment label in space details
storrageAssignmentLabel=Простор за складирање
#XFLD: Storage Type label in space details
storageTypeLabel=Тип склад
#XFLD: Enable Space Quota
enableSpaceQuota=Овозоможи ја квотата за простор
#XFLD: No Space Quota
noSpaceQuota=Нема квота за простор
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
diskAndInMemoryText=База со податоци SAP HANA (на диск и во меморијата)
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
dataLakeFilesText=Датотеки на SAP HANA Data Lake
#XFLD: Available scoped roles label
availableRoles=Достапни улоги со одреден опсег
#XFLD: Selected scoped roles label
selectedRoles=Избрани улоги со одреден опсег
#XCOL: Spaces table-view column models
models=Модели
#XCOL: Spaces table-view column users
users=Корисници
#XCOL: Spaces table-view column connections
connections=Врски
#XFLD: Section header overview in space detail
overview=Преглед
#XFLD: Section header Apache Spark in space detail
sparkManagementSection=Apache Spark
#XFLD: Section header Apache Spark in space detail
sparkManagementSubSection=Апликации
#XFLD: Section header Apache Spark in space detail
taskAssignmentSubSection=Доделување задача
#XFLD: vCPU label in Apache Spark section
vCPULabel=vCPU-единици
#XFLD: Memory label in Apache Spark section
memoryLabel=Меморија (GB)
#XFLD: space configuration label in Apache Spark section
spaceConfigurationLabel=Конфигурација на просторот
#XFLD: Space Source label
sparkApplicationLabel=Апликација
#XFLD: Cluster Size label
clusterSizeLabel=Големина на кластерот
#XFLD: Driver label
driverLabel=Двигател
#XFLD: Executor label
executorLabel=Извршител
#XFLD: max label
maxLabel=Макс. искористено
#XFLD: TrF Default label
trFDefaultLabel=Стандарден трансформациски тек
#XFLD: Merge Default label
mergeDefaultLabel=Стандардно спојување
#XFLD: Optimize Default label
optimizeDefaultLabel=Оптимизирај го стандардното
#XFLD: Deployment Default label
deploymentDefaultLabel=Применување на локалната табела (датотека)
#XFLD: max spark source label
driverExecutorMaxUsedSparkSource={0} CPU/{1} GB
#XFLD: Object type label
taskObjectTypeLabel=Тип објект
#XFLD: Task activity label
taskActivityLabel=Активност
#XFLD: Task Application ID label
taskApplicationIDLabel=Стандардна апликација
#XFLD: Section header in space detail
generalSettings=Општи поставки
#XMSG: Message strip message when a space is locked
spaceLockedMessageStrip=Овој простор е заклучен од системот во моментов.
#XMSG: Message strip message deploy warning
deployWarningMessageStrip=Промените во оваа секција ќе се применат веднаш.
#XMSG: Message strip message modify space configuration warning
modifySpaceConfigurationMessageStrip=Имајте предвид дека промената на овие вредности може да предизвика проблеми со учинокот.
#XFLD: Button text to unlock the space again
unlockSpace=Отклучи го просторот
#XFLD: Info text for audit log formatted message
auditLogText=Овозможи дневници за ревизија за евидентирање на дејствата за читање или менување (политики за ревизија). Тогаш, администраторите ќе можат да анализираат кој го извршил кое дејство во кое време.
#XMSG: Message strip  audit warning
auditLogWarningMessageStrip=Дневниците за ревизија може да користат голема количина меморија на дискот кај закупецот. Ако ја овозможите политиката за ревизија (дејства за читање или менување), треба редовно да ја следите искористеноста на меморијата на дискот (преку картичката Искористена меморија на дискот во Следење на системот) за да избегнете прекин на работа на полниот диск, што може да предизвика нарушувања на услугата. Ако ја оневозможите политиката за ревизија, ќе се избришат сите внесови во дневникот за ревизија. Ако сакате да ги зачувате внесовите во дневникот за ревизија, извезете ги пред да ја оневозможите политиката за ревизија.
#XFLD: Message strip link text to open in app help to guide user what to do to unlock his space permanently
unlockSpaceHelp=Покажи помош
#XMSG: Message strip message when a space will be locked again
spaceTemporarilyUnlockedMessageStrip=Овој простор ја надминува меморијата на просторот и ќе се заклучи во {0} {1}.
#XMSG: Unit for remaining time until space is locked again
hours=часови
#XMSG: Unit for remaining time until space is locked again
minutes=минути
#XFLD: Section header in space detail for auditing
detailsSectionAuditing=Ревизија
#XFLD: Subsection header in space detail for auditing
auditing=Поставки за ревизијата на просторот
#XFLD: Hot space tooltip
hotSpaceCountTooltip=Критични простори: искористениот простор за складирање е поголем од 90 %.
#XFLD: Green space tooltip
greenSpaceCountTooltip=Умерени простори: искористениот простор за складирање е меѓу 6 % и 90 %.
#XFLD: Cold space tooltip
coldSpaceCountTooltip=Неактивни простори: искористениот простор за складирање е 5 % или помалку.
#XFLD: Critical space tooltip
criticalSpaceCountTooltip=Критични простори: искористениот простор за складирање е поголем од 90 %.
#XFLD: Green space tooltip
okSpaceCountTooltip=Умерени простори: искористениот простор за складирање е меѓу 6 % и 90 %.
#XFLD: Locked space tooltip
lockedSpaceCountTooltipNew=Заклучени простори: блокирани поради недоволна меморија.
#XFLD: Locked space tooltip
lockedSpaceCountTooltip=Заклучени простори
#YMSE: Error while deleting remote source
deleteRemoteError=Врските не може да се отстранат.
#XFLD: Space ID input tooltip
spaceIDInfoTooltip=ИД-бројот на просторот не може да се промени подоцна.\nВажечки знаци A – Ш, 0 – 9, и _
#YMSE: Error when space name is empty
spaceNameEmptyValidationError=Внеси назив на просторот.
#YMSE: Error when ECN Business name is empty
ecnNameEmptyValidationError=Внеси деловен назив.
#YMSE: Error when space id is empty
spaceIDInputEmptyValidationError=Внеси ИД-број на просторот.
#YMSE: Error when space id contains invalid characters
spaceIDInputInValidError=Неважечки знаци. Користете само A–Ш, 0–9, и _.
#YMSE: Error when space id exists while creation
spaceIDInputDuplicateError=ИД-бројот на просторот веќе постои.
#XFLD: Space searchfield placeholder
search=Пребарај
#XMSG: Success message after creating users
createUsersSuccess=Корисниците се додадени
#XMSG: Success message after creating user
createUserSuccess=Корисникот е додаден
#XMSG: Success message after updating users
updateUsersSuccess={0} корисници се ажурирани
#XMSG: Success message after updating user
updateUserSuccess=Корисникот е ажуриран
#XMSG: Success message after removing users
removeUsersSuccess={0} корисници се отстранети
#XMSG: Success message after removing user
removeUserSuccess=Корисникот е отстранет
#XFLD: Schema name
schemaName=Назив на шемата
#XFLD: used of total
ofTemplate={0} од {1}
#XFLD: Assigned disk label in space overview
assignedStorageTemplate=Доделен простор на дискот ({0} од {1})
#XFLD: Assigned Memory label in space overview
assignedRAMTemplate=Доделена меморија ({0} од {1})
#XFLD: Storage ratio on space
accelearationRAM=Забрзување на меморијата
#XFLD: No Storage Consumption
noStorageConsumptionText=Не е доделена квота за складот.
#XFLD: Used disk label in space overview
usedStorageTemplate=Диск искористен за складирање ({0} од {1})
#XFLD: Used Memory label in space overview
usedRAMTemplate=Меморија искористена за складирање ({0} од {1})
#XFLD: Used Disk tooltip
usedStorageTooltipTemplate={0} од {1} од дискот е искористен за складирање
#XFLD: Used Memory tooltip
usedRAMTooltipTemplate=Искористена меморија, {0} од {1}
#XFLD: Assigned Disk tooltip
assignedStorageTooltipTemplate=Доделен простор на диск, {0} од {1}
#XFLD: Assigned Memory tooltip
assignedRAMTooltipTemplate=Доделена меморија, {0} од {1}
#XFLD: Space related used storage tooltip
spaceRelatedUsedStorageTooltipTemplate=Податоци за просторот: {0}
#XFLD: Not space related used storage tooltip
otherDataUsedStorageTooltipTemplate=Други податоци: {0}
#XFLD: Storage used exceeded popover and tooltip
storageUsedExceededText=Размислите да го проширите планот или контактирајте со подршката на SAP.
#XCOL: Space table-view column used Disk
usedStorage=Диск искористен за складирање
#XCOL: Space monitor column used Memory
usedRAM=Меморија искористена за складирање
#XCOL: Space monitor column Schema
tableSchema=Шема
#XCOL: Space monitor column Storage Type
tableStorageType=Тип склад
#XCOL: Space monitor column Table Type
tableType=Тип табела
#XCOL: Space monitor column Record Count
tableRecordCount=Број записи
#XFLD: Assigned Disk
assignedStorage=Диск доделен за складирање
#XFLD: Assigned Memory
assignedRAM=Меморија доделена за складирање
#XCOL: Space table-view column storage utilization
tableStorageUtilization=Искористен склад
#XFLD: space status
spaceStatus=Статус на просторот
#XFLD: space type
spaceType=Тип простор
#XFLD: space type label for space with space type "SAP BW bridge" ("SAP BW bridge" is a product name, do not translate it)
abapbridgeSpaceType=Премостување на апликацијата SAP BW
#XFLD: space type label for space with space type "Data Marketplace"
marketplaceSpaceType=Производ на давателот на податоци
#XMSG: Error message for when selected space cannot be deleted due to the space type
deleteTypedSpaceError=Не можете да го избришете просторот {0} бидејќи типот простор е {1}.
#XMSG: Error message for when selected spaces cannot be deleted due to the space type
deleteTypedSpacesError=Не можете да ги избришете избраните {0} простори. Не може да се избришат просторите со следниве типови простор: {1}.
#XFLD: space type label for space without space type
defaultSpaceType=SAP Datasphere
#XFLD: Tooltip for monitor space button
monitorSpace=Следи
#XFLD: Tooltip for edit space button
editSpace=Уреди го просторот
#XMSG: Deletion warning in messagebox
deleteConfirmation=Дали сигурно сакате да го избришете овој простор?
#XFLD: Tooltip for delete space button
deleteSpace=Избриши го просторот
#XFLD: storage
storage=Диск за складирање
#XFLD: username
userName=Корисничко име
#XFLD: port
port=Порта
#XFLD: hostname
hostName=Назив на домаќинот
#XFLD: password
password=Лозинка
#XBUT: Request new password button
requestPassword=Побарајте нова лозинка
#YEXP: Usage explanation in time data section
timeDataSectionHint=Создајте временски табели и димензии за користење во моделите и приказните.
#YEXP: Usage explanation in Space schema section
spaceSchemaHint=Дали сакате податоците во просторот да бидат достапни за други алатки или апликации? Ако сакате, создајте еден или повеќе корисници што можат да пристапат до податоците во просторот и изберете дали сакате сите идни податоци за просторот да бидат стандардно достапни за користење.
#XTIT: Create schema popup title
createSchemaDialogTitle=Создај шема Open SQL
#XTIT: Generate Time Data dialog title
generateTimeDataDialogTitle=Создај временски табели и димензии
#XTIT: Edit Time Data dialog title
editTimeDataDialogTitle=Уреди ги временските табели и димензии
#XTIT: Time Data token title
timeDataTokenTitle=Податоци за време
#XTIT: Time Data token title
timeDataUpdateViews=Ажурирај ги приказите за податоците за време
#XFLD: Time Data token creation in progress label
timeDataCreationInProgress=Создавање во тек...
#XFLD: Time Data token creation error label
timeDataCreationError=Создавањето не успеа. Обидете се повторно.
#XTIT: Time Data dialog time settings title
timeDataDialogTimeSettingsTitle=Поставки за временските табели
#XTIT: Time Data dialog time data table title
timeDataDialogTranslationTablesTitle=Табели за пресметка
#XTIT: Time Data dialog dimensions title
timeDataDialogTimeViewsTitle=Временски димензии
#XFLD: Time Data dialog time range label
timeRangeHint=Дефинирајте го временскиот опсег.
#XFLD: Time Data dialog time data table label
timeDataHint=Дајте ѝ назив на табелата.
#XFLD: Time Data dialog time data dimensions label
timeDimensionsHint=Дајте им назив на димензиите.
#XFLD: Time Data Time range description label
timerangeLabel=Временски опсег
#XFLD: Time Data dialog from year label
fromYearLabel=Од година
#XFLD: Time Data dialog to year label
toYearLabel=До година
#XFLD: Time Data dialog calendar type label
calendarTypeLabel=Тип календар
#XFLD: Time Data dialog granularity label
granularityLabel=Грануларност
#XFLD: Time Data dialog technical name label
technicalNameLabel=Технички назив
#XFLD: Time Data dialog quarters text table lable
quarterTextsTableLabel=Табели за пресметка за тромесечја
#XFLD: Time Data dialog months text table lable
monthsTextsTableLabel=Табели за пресметка за месеци
#XFLD: Time Data dialog days text table lable
daysTextsTableLabel=Табели за пресметка за денови
#XFLD: Time Data dialog year label
yearLabel=Димензија на година
#XFLD: Time Data dialog quarter label
quarterLabel=Димензија на тромесечје
#XFLD: Time Data dialog month label
monthLabel=Димензија на месец
#XFLD: Time Data dialog day label
dayLabel=Димензија на ден
#XFLD: Time Data dialog gregorian calendar type label
gregorian=Грегоријански
#XFLD: Time Data dialog time granularity day label
day=Ден
#XFLD: Time Data dialog business name maxlength reached
timeRangeValueStateTextMaxLengthBusinessName=Максималната должина од 1 000 знаци е достигната.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextMaxRangeExceeded=Максималниот временски опсег е 150 години.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeLower=„Од година“ треба да биде помало од „До година“
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeHigher=„Од година“ мора да биде 1 900 или поголемо.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeHigher=„До година“ треба да биде поголемо од „Од година“
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeLower=„До година“ треба да биде помало од тековната година, плус 100
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningFromYear=Зголемувањето на „Од година“ може да предизвика загуба на податоци
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningToYear=Зголемувањето на „Од година“ може да предизвика загуба на податоци
#XMSG: Time Data creation validation error message
timeDataValidationError=Изгледа дека некои полиња се неважечки. Проверете ги задолжителните полиња за да продолжите.
#XMSG: Time Data deletion popup text
deleteTimeDataConfirmation=Дали сигурно сакате да ги избришете податоците?
#XMSG: Time Data creation success message
createTimeDataSuccess=Податоците за време се создадени
#XMSG: Time Data update success message
updateTimeDataSuccess=Податоците за време се ажурирани
#XMSG: Time Data delete success message
deleteTimeDataSuccess=Податоците за време се избришани
#XMSG: Time Data creation error message
createTimeDataError=Дојде до проблем при обидот да се создадат податоци за времето.
#XMSG: Time Data update error message
updateTimeDataError=Дојде до проблем при обидот да се ажурираат податоците за времето.
#XMSG: Time Data creation error message
deleteTimeDataError=Дојде до проблем при обидот да се избришат податоците за времето.
#XMSG: Error message when time data could not be retrieved
retrievingTimeDataError=Податоците за време не може да се вчитаат.
#XTIT: Time Data deletion popup title
deleteTimeDataConfirmationTitle=Предупредување
#XTXT: Time Data deletion dependency popup text
deleteTimeDataDependencyText=Не можеме да ги избришеме Податоците за време бидејќи се користат во други модели.
#XTXT: Time Data deletion dependency popup text singular
deleteTimeDataDependencyTextSingular=Не можеме да ги избришеме Податоците за време бидејќи се користат во друг модел.
#XMSG: Validation error when required time data form field is empty
timeDataRequiredField=Ова поле е задолжително и не може да биде празно.
#XFLD: Tooltip for Open in Database Explorer button
openInDBX=Отвори во пребарувачот на базата со податоци
#YMSE: Dimension Year
dimensionYearView=Димензија „Година“
#YMSE: Dimension Year
dimensionQuarterView=Димензија „Квартал“
#YMSE: Dimension Year
dimensionMonthView=Димензија „Месец“
#YMSE: Dimension Year
dimensionDayView=Димензија „Ден“
#XFLD: Time Data deletion object title
timeDataUsedIn=(се користи во {0} модели)
#XFLD: Time Data deletion object title singular
timeDataUsedInSingular=(се користи во 1 модел)
#XFLD: Time Data deletion table column provider
provider=Давател
#XTIT: Time Data dependency dialog title
timeDataDependencyDialogTitle=Зависности
#XTIT: Create schema popup title
createSpaceSchemaUserDialogTitle=Создај корисник на шемата за просторот
#XFLD: Create schema button
createSchemaButton=Создај шема Open SQL
#XFLD: Generate TimeData button
generateTimeDataButton=Создај временски табели и димензии
#XFLD: Show dependencies button
showDependenciesButton=Покажи зависности
#XFLD: tooltip for disabled buttons when user is not member of space
mustBeMemberOfSpaceForOperation=За да ја изврши оваа операција, корисникот мора да биде член на просторот.
#XFLD: Create space schema user button
createSpaceSchemaUserButton=Создај корисник на шемата за просторот
#YMSE: API Schema users load error
loadSchemaUsersError=Списокот со корисници не може да се вчита.
#XTIT: API Schema-config popup title
schemaUserDetailsDialogTitle=Детали за корисникот на шемата за просторот
#XMSG: API Schema user deletion popup text
deleteSpaceSchemaUserConfirmation=Дали сигурно сакате да го избришете избраниот корисник?
#XMSG: API Schema user deletion confirmation text
userDeleteSuccess=Корисникот е избришан.
#YMSE: API Schema user deletion error
userDeleteError=Корисникот не може да се избрише.
#XFLD: User deleted
userDeleted=Корисникот е избришан.
#XTIT: Remove user popup title
removeUserConfirmationTitle=Предупредување
#XMSG: Remove user popup text
removeUserConfirmation=Дали навистина сакате да го отстраните корисникот? Корисникот и неговите доделени улоги со одреден опсег ќе се отстранат од просторот.
#XMSG: Remove users popup text
removeUsersConfirmation=Дали навистина сакате да ги отстраните корисниците? Корисниците и нивните доделени улоги со одреден опсег ќе се отстранат од просторот.
#XBUT: Confirmation button for remove user dialog
removeUserConfirmationBtn=Отстрани
#YMSE: No data text for available roles
noDataAvailableRoles=Просторот не е додаден во ниту една улога со одреден опсег. \n За да може да додадете корисник во просторот, мора прво да го додадете во една или повеќе улоги со одреден опсег.
#YMSE: No data text for selected roles
noDataSelectedRoles=Нема избрани улоги со одреден опсег
#XTIT: Schema-config popup title
schemaDetailsDialogTitle=Детали за конфигурацијата на шемата Open SQL
#XFLD: Label for Read Audit Log
auditLogRead=Овозможи го дневникот за ревизија за операциите за читање
#XFLD: Label for Change Audit Log
auditLogChange=Овозможи го дневникот за ревизија за операциите за промена
#XFLD: Label Audit Log Retention
auditLogRetention=Задржи ги дневниците за
#XFLD: Label Audit Log Retention Unit
retentionUnit=Денови
#YMSE: Error when log retention time is set incorrectly
incorrectRetentionTime=Внесете цел број меѓу {0} и {1}.
#XFLD: Label for Cross Space Schema Access
spaceSchemaAccess=Употреби ги податоците на шемата за просторот
#XTIT: Confirm deactivation of space schema consumption dialog title
disableSpaceSchemaConsumptionDialogTitle=Престани со употреба на податоците на шемата за просторот
#XTXT: Confirm deactivation of space schema consumption dialog text
disableSpaceSchemaConsumptionDialogText=Оваа Open SQL-шема може да користи податоци од шемата за простор. Ако престанете со користење, моделите засновани на податоците за шемата за просторот може да не функционираат повеќе.
#XBUT: Confirmation button for the stop consuming space schema data dialog
stopConsuming=Запри го користењето
#XTXT: Tooltip for data lake icon
dataLakeIconTooltip=Овој простор се користи за пристап до езерото од податоци.
#XFLD: Tooltip for data lake table entry
dataLakeTableEntryTooltip=Езерото од податоци е овозможено
#XFLD: used In-Memory linit reached tag
usedRamLimitReachedLabel=Достигнато е ограничувањето на меморијата
#XFLD: Space storage Limit reached tag
storageLimitReachedLabel=Достигнато е ограничувањето на складот
#XFLD: Minimal Storage Limit Reached tag
minStorageLimitReachedLabel=Достигнато е минималното ограничување на складот
#XFLD: Space ram tag
ramLimitReachedLabel=Достигнато е ограничувањето на меморијата
#XFLD: Minimal In-Memory Limit Reached tag
minRamLimitReachedLabel=Достигнато е минималното ограничување на меморијата
#XFLD: Popover text after click on space storage limit warning
storageLimitReachedText=Го достигнавте ограничувањето на доделениот простор за складирање за просторот {0}. Доделете повеќе простор за складирање за просторот.
#XFLD: System storage tag
systemStorageLimitReachedLabel=Достигнато е ограничувањето на складот за системот
#XMSG: Popover text after click on storage limit warning
systemStorageLimitReachedText=Го достигнавте ограничувањето на складот за просторот {0}. Не можете да доделите уште простор за складирање за просторот.
#XMSG: Schema deletion warning
deleteSchemaConfirmation=Бришењето на оваа open SQL-шема трајно ќе ги избрише сите складирани објекти и одржувани асоцијации во шемата. Продолжи?
#XMSG: Success message after deleting a schema
schemaDeleteSuccess=Шемата е избришана
#YMSE: Error while deleting schema.
schemaDeleteError=Шемата не може да се избрише.
#XMSG: Success message after update a schema
schemaUpdateSuccess=Шемата е ажурирана
#YMSE: Error while updating schema.
schemaUpdateError=Шемата не може да се ажурира.
#XMSG: Hint when no password is shown in the dialog.
newPasswordHint=Обезбедивме лозинка за оваа шема. Ако сте ја заборавиле или сте ја изгубиле, можете да побарате нова. Запомнете да ја копирате или да ја зачувате новата лозинка.
#XMSG: Hint when showing the password in the dialog.
savePasswordHint=Копирајте ја лозинката. Ќе треба повторно да поставите врска до оваа шема. Ако сте ја заборавиле лозинката, можете да го отворите овој дијалог за да ја ресетирате.
#XFLD,50: Hint on entering schema name in creation dialog
schemaNameHint=Називот не се менува кога ќе се создаде шемата.
#XFLD: Open SQL Schemas section sub headline
schemasSQL=Отвори SQL
#XFLD: Space schema section sub headline
schemasSpace=Простор
#XFLD: HDI Container section header
HDIContainers=Контејнери HDI
#XTXT: Add HDI Containers button tooltip
addHDIContainers=Додајте контејнери HDI
#XTXT: Remove HDI Containers button tooltip
removeHDIContainers=Отстранете контејнери HDI
#XFLD: Enable HDI Containers feature button
enableHDIContainersButton=Овозможи пристап
#YMSE: No data text for HDI Containers table
noDataHDIContainers=Не се додадени контејнери HDI.
#YMSE: No data text for Timedata section
noDataTimedata=Не се создадени табели за време и димензии.
#YMSE: No data text during runtime not available for Timedata section
runtimeNotAvailableNoDataTimedata=Не може да се вчитаат временските табели и димензии бидејќи базата на податоци за времето за извршување не е достапна.
#YMSE: No data text during runtime not available for HDI section
runtimeNotAvailableNoDataHDI=Не може да се вчитаат контејнерите HDI бидејќи базата на податоци за времето за извршување не е достапна.
#YMSE: Error while fetching HDI Containers
fetchHDIContainersError=Не може да се земат контејнерите HDI. Обидете се повторно подоцна.
#XFLD Table column header for HDI Container names
HDIContainerName=Назив на контејнерот HDI
#XFLD: Enable HDI Containers feature dialog title
enableHDIContainersDialogTitle=Овозможи пристап
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText1New=Можете да овозможите SAP SQL Data Warehousing во закупецот на решението SAP Datasphere за да разменувате податоци меѓу контејнерите HDI и просторите на SAP Datasphere без да треба да ги преместувате податоците.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText2=За да го направите ова, отворете билет за поддршка со кликнување на копчето подолу.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText3New=Откако билетот ќе се обработи, мора да создадете еден или повеќе контејнери HDI во базата со податоци за времето на извршување на SAP Datasphere. Потоа, копчето + го менува копчето Овозможи пристап во секцијата Контејнери HDI за сите ваши простори во SAP Datasphere, и можете своите контејнери да ги додадете во просторот.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText41=Потребни ви се повеќе информации? Одете во %%0. За подетални информации за тоа што да вклучите во билетот, видете во %%1.
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkText=SAP Help
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkTextSapNote3057059=Белешка од SAP 3057059
#XBUT: Open Ticket Button Text
openTicket=Отвори билет
#XBUT: Add Button Text
add=Додај
#XBUT: Next Button Text
next=Следно
#XBUT: Edit Button Text
editUsers=Уреди
#XBUT: create user Button Text
createUser=Создај
#XBUT: Update user Button Text
updateUser=Избери
#XTIT: Add HDI Containers Dialog Title
addHDIContainersDialogTitle=Додајте недоделени контејнери HDI
#XFLD: No data text for add HDI container dialog
addHDIContainersDialogNoDataText=Не можеме да најдеме недоделени контејнери. \n Контејнерот што го барате можеби е веќе доделен на простор.
#YMSE: Error while loading assigned HDI Containers
loadAssignedHDIContainersError=Доделените контејнери HDI не може да се вчитаат.
#YMSE: Error while loading assigned HDI Containers
loadUnassignedHDIContainersError=Доделените контејнери HDI не може да се вчитаат.
#XMSG: Success message
succeededToAddHDIContainer=Додаден е контејнер HDI
#XMSG: Success message
succeededToAddHDIContainerPlural=Додадени се контејнери HDI
#XMSG: Success message
succeededToDeleteHDIContainer=Контејнерот HDI е отстранет
#XMSG: Success message
succeededToDeleteHDIContainerPlural=Контејнерите HDI се отстранети
#XFLD: Time data section sub headline
timeDataSection=Табели за време и димензии
#XFLD: Schemas list entry read audit log enabled indicator
readIndicator=Читај
#XFLD: Schemas list entry change audit log enabled indicator
changeIndicator=Промени
#XFLD: Remote sources section sub headline
allconnections=Доделување врска
#XFLD: Remote sources section sub headline
localconnections=Локални врски
#XFLD: Remote sources section sub headline for open Connectors
openConnectors=SAP Open Connectors
#XFLD: User section sub headline
memberassignment=Доделување член
#XFLD: User assignment section sub headline
userAssignment=Доделување корисник
#XFLD: User section Access dropdown Member
member=Член
#XFLD: User assignment section column name
user=Корисничко име
#XTXT: Selected role count
selectedRoleToolbarText=Избрано: {0}
#XTIT: Space detail section connections title
detailsSectionConnections=Врски
#XTIT: Space detail section data access title
detailsSectionDataAccess=Пристап до шема
#XTIT: Space detail section time data title
detailsSectionGenerateData=Податоци за време
#XTIT: Space detail section members title
detailsSectionUsers=Членови
#XTIT: Space detail section Users title
detailsSectionUsersTitle=Корисници
#XTIT: Out of Storage
insufficientStoragePopoverTitle=Нема меморија
#XTIT: Storage distribution
storageDistributionPopoverTitle=Искористена меморија на дискот
#XTXT: Out of Storage popover text
insufficientStorageText=За да создадете нов простор, намалете го доделениот простор за складирање на друг простор или избришете простор што не ви е потребен повеќе. Можете да го зголемите вкупниот простор за складирање на системот со повикување на функцијата Управувај со планот.
#XMSG: Space id length warning
spaceIdLengthWarning=Надминат е максималниот број од {0} знаци.
#XMSG: Space name length warning
spaceNameLengthWarning=Надминат е максималниот број од {0} знаци.
#XMSG: Space id reserved prefix warning
spaceIdnWarningReservedPrefix=Не користите го префиксот {0} за да избегнете можни конфликти.
#YMSE: Error while loading open SQL schemas
loadLocalSchemasError=Шемите Open SQL не може да се вчитаат.
#YMSE: Error while creating open SQL schema
createLocalSchemaError=Шемата Open SQL не може да се создаде.
#YMSE: Error while loading all remote connections
loadRemoteConnectionsError=Не може да се вчитаат сите далечински врски.
#YMSE: Error while loading space details
loadSpaceDetailsError=Деталите за просторот не може да се вчитаат.
#YMSE: Error while deploying space details
deploySpaceDetailsError=Просторот не може да се примени.
#YMSE: Error while copying space details
copySpaceDetailsError=Просторот не може да се копира.
#YMSE: Error while loading storage data
loadStorageDataError=Складот не може да се вчита.
#YMSE: Error while loading all users
loadAllUsersError=Не може да се вчитаат сите корисници.
#YMSE: Failed to reset password
resetPasswordError=Лозинката не може да се ресетира.
#XMSG: Success message after updating space
resetPasswordSuccessMessage=Поставена е нова лозинка за шемата
#YMSE: DP Agent-name too long
DBAgentNameError=Називот на агентот за дистрибуција на податоци е предолг.
#YMSE: Schema-name not valid.
schemaNameError=Називот на шемата е неважечки.
#YMSE: User name not valid.
UserNameError=Корисничкото име е неважечко.
#XTIT: Consumption by Storage Type
consumptionStorageTypeText=Потрошувачка според типот складирање
#XTIT: Consumption by Schema
consumptionSchemaText=Потрошувачка според шема
#XTIT: Overall Consumption by schema
overallConsumptionSchemaText=Вкупна употреба на табелата по шема
#XTIT: Overall Consumption by Table Type
overallConsumptionTableTypeText=Вкупна употреба по тип табела
#XTIT: Tables
tableDetailsText=Детали за табелата
#XTIT: Table Storage Consumption
tableStorageConsumptionText=Употреба на складот на табелата
#XFLD: Table Type label
tableTypeLabel=Тип табела
#XFLD: Schema label
schemaLabel=Шема
#XFLD: reset table tooltip
resetTable=Ресетирај ја табелата
#XFLD: In-Memory label in space monitor
inMemoryLabel=Меморија
#XFLD: Disk label in space monitor
diskLabel=Диск
#XFLD: Yes
yesLabel=Да
#XFLD: No
noLabel=Не
#XFLD: switch label: Want the data in this space to be consumable by default?
allowConsumptionByDefault=Дали сакате податоците во овој простор да се употребуваат стандардно?
#XFLD: Business Name
businessNameLabel=Деловен назив
#XFLD: Refresh
refresh=Освежи
#XMSG: No filter results title
noFilterResultsTitle=Изгледа дека поставките за филтерот не покажуваат никакви податоци.
#XMSG: No filter results message
noFilterResultsMsg=Обидете се детално да ги одредите поставките на филтерот и ако сè уште не гледате никакви податоци, создајте неколку табели во Алатката за градење податоци. Откако ќе почнат да го користат складот, ќе можете да ги следите овде.
#XMSG: Space monitoring Hana state red title
spaceMonitorHanaRedTitle=Базата на податоци за времето на извршување не е достапна.
#XMSG: Space monitoring Hana state red message
spaceMonitorHanaRedMsg=Бидејќи базата со податоци за времето на извршување не е достапна, одредени функции се оневозможени и не можеме да прикажеме никакви информации на страницава.
#YMSE: Error while creating space schema user
createSpaceSchemaUserError=Шемата за просторот не може да се создаде.
#YMSE: Error User name already exists
userAlreadyExistsError=Корисничкото име веќе постои.
#YMSE: Error Authentication failed
authenticationFailedError=Автентикацијата не успеа.
#XMSG: Message strip message when a schema user is locked
userLockedMessageStrip=Корисникот е заклучен поради премногу неуспешни најавувања. Побарајте нова лозинка за да го отклучите корисникот.
#XMSG: Success message after reset and unlock schema user
resetUnlockPasswordSuccessMessage=Поставена е нова лозинка и корисникот е отклучен
#XMSG: user is locked message
userLockedMessage=Корисникот е заклучен.
#XCOL: Users table-view column Role
spaceRole=Улога
#XCOL: Users table-view column Scoped Role
spaceScopedRole=Улога со одреден опсег
#XCOL: Users table-view column Space Admin
spaceAdmin=Администратор на просторот
#XFLD: User section dropdown value Viewer
viewer=Прикажувач
#XFLD: User section dropdown value Modeler
modeler=Уредувач
#XFLD: User section dropdown value Data Integrator
dataIntegrator=Интегратор на податоци
#XFLD: User section dropdown value Space Administrator
spaceAdministrator=Администратор на просторот
#XMSG: Success message -  Space Role updated successfully.
updateUserSpaceRoleSuccess=Улогата на просторот е ажурирана
#XMSG: Error message - Failed to update Space Role.
updateUserSpaceRoleError=Улогата на просторот не е успешно ажурирана.
#XFLD:
databaseUserNameSuffix=Додавка на името на корисникот на базата со податоци
#XTXT: Space Schema password text
spaceSchemaPasswordText=За да ја поставите врската до оваа шема, копирајте ја лозинката. Ако сте ја заборавиле лозинката, секогаш можете да побарате нова.
#XFLD: Cloud Platform label in space schema dialog
cloudPlatformLabel=Cloud Platform
#XTXT: Cloud Platform text in space schema dialog
cloudPlatformText=За да поставите пристап преку овој корисник, овозможете ја употребата и копирајте ги акредитивите. Во случај да можете да ги копирате само акредитивите без лозинка, подоцна додајте ја лозинката.
#XFLD: enable consumption in CloudPlatform label in space schema dialog
enableConsumptionInCloudPlatformLabel=Овозможи употреба во Cloud Platform
#XFLD: Credentials for User-Provided Service label in space schema dialog
cloudPlatformCredentialsLabel=Акредитиви за услугата обезбедена од корисник:
#XFLD: Credentials for User-Provided Service (Without Password) label in space schema dialog
cloudPlatformCredentialsWithoutPasswordLabel=Акредитиви за услугата обезбедена од корисник (без лозинка):
#XFLD: Copy Credentials label in space schema dialog
copyCredentialsLabel=Копирај ги акредитивите без лозинка
#XFLD: Copy Credentials label in space schema dialog
copyFullCredentialsLabel=Копирај ги целосните акредитиви
#XFLD: Copy Password label in space schema dialog
copyPasswordLabel=Копирај ја лозинката
#XMSG: Credentials copied to clipboard
credentialsCopiedMessage=Акредитивите се копирани во складот
#XMSG: Password copied to clipboard
passwordCopiedMessage=Лозинката е копирана во складот
#XMSG: create database user dialog title
createDatabaseUserDialogTitle=Создај база на податоци
#XMSG: Database Users section title
databaseUsers=Корисници на базата со податоци
#XMSG: Database User Details dialog title
databaseUserDetailsDialogTitle=Детали за корисникот на базата со податоци
#XFLD: database user read audit log
databaseUserAuditLogRead=Овозможи ги дневниците за ревизија за операциите за читање и задржи ги дневниците за
#XFLD: database user change audit log
databaseUserAuditLogChange=Овозможи ги дневниците за ревизија за операциите за промена и задржи ги дневниците за
#XMSG: Cloud Platform Access
cloudPlatformAccess=Пристап до Cloud Platform
#XFLD: Enable Consumption in Cloud Platform hint
enableConsumptionCloudPlatformHint=Поставете пристап до контејнерот HANA Deployment Infrastructure (HDI) преку овој корисник на базата со податоци. За да се поврзете со контејнерот HDI container, SQL-моделирањето мора да биде вклучено
#XFLD: Enable HDI Consumption
enableConsumptionCloudPlatform=Овозможи употреба на HDI
#XFLD: Enable Consumption hint
enableConsumptionHint=Дали сакате други алатки и апликации да ги употребуваат податоците во просторот?
#XFLD: Enable Consumption
enableConsumption=Овозможи употреба на SQL
#XFLD: Enable Modeling
enableModeling=Овозможи SQL-моделирање
#XMSG: Privileges for Data Modeling
privilegesModeling=Увоз на големи количини податоци
#XMSG: Privileges for Data Consumption
privilegesConsumption=Употреба на податоци за надворешни алатки
#XFLD: SQL Modeling
sqlModeling=SQL-моделирање
#XFLD: SQL Consumption
sqlConsumption=Употреба на SQL
#XFLD: enabled
enabled=Овозможено
#XFLD: disabled
disabled=Оневозможено
#XFLD: Edit Privileges
editPrivileges=Уреди ги овластувањата
#XFLD: Open Database Explorer
openDBX=Отвори го пребарувачот на базата со податоци
#XFLD: create database user hint
databaseCreateHint=Имајте предвид дека нема да можете да го смените корисничкото име повторно по зачувувањето.
#XFLD: Internal Schema Name
internalSchemaName=Назив на внатрешната шема
#YMSE: Failed to load database users
loadDatabaseUserError=Вчитувањето на корисниците на базата со податоци не успеа
#YMSE: Failed to delete database user
deleteDatabaseUsersError=Бришењето на корисниците на базата со податоци не успеа
#XMSG: Success message after deleting database user
deleteDatabaseUserSuccess=Корисникот на базата со податоци е избришан
#XMSG: Success message after deleting database users
deleteDatabaseUsersSuccess=Корисниците на базата со податоци се избришани
#XMSG: Success message after creating database user
createDatabaseUserSuccessMessage=Создаден е корисник на базата со податоци
#YMSE: Failed to create database user
createDatabaseUserError=Создавањето на корисникот на базата со податоци не успеа
#XMSG: Success message after updating database user
updateDatabaseUserSuccessMessage=Корисникот на базата со податоци е ажуриран
#YMSE: Failed to update database user
updateDatabaseUserError=Ажурирањето на корисникот на базата со податоци не успеа
#XFLD: HDI Consumption
hdiConsumption=Употреба на HDI
#XTIT: Space detail section Database Access title
detailsSectionDatabaseAccess=Пристап до базата со податоци
#XTXT: Database users consumption hint
databaseUsersConsumptionHint=Поставете ги податоците да можат да се употребуваат стандардно. Моделите во алатките за градење автоматски овозможуваат употреба на податоци.
#XFLD: Default Consumption of Space Data:
defaultConsumptionOfSpaceData=Стандардно користење на податоците за просторот:
#XFLD: Database User Name
databaseUserName=Име на корисникот на базата со податоци
#XMSG: Database User creation validation error message
databaseUserValidationError=Изгледа дека некои полиња се неважечки. Проверете ги задолжителните полиња за да продолжите.
#XMSG: SQL modeling can’t be enabled since this user has been migrated.
migratedDatabaseUserHint=Увозот на голема количина податоци не може да се овозможи бидејќи овој корисник е мигриран.
#XBUT: Remove Button Text
remove=Отстрани
#XBUT: Remove Spaces Button Text
removeSpaces=Отстрани простори
#XBUT: Remove Objects Button Text
removeObjects=Отстрани објекти
#XMSG: No members have been added yet.
noMembersAssigned=Сè уште не се додадени членови.
#XMSG: No users have been added yet.
noUsersAssigned=Сè уште не се додадени корисници.
#XMSG: No database users have been created or your filter is not showing any data.
noDatabaseUsers=Сè уште не се создадени корисници на базата со податоци или филтерот не покажува никакви податоци.
#XMSG: Please enter a user name.
noDatabaseUsername=Внесете корисничко име.
#XMSG: The user name is too long. Please use a shorter one.
databaseUsernameTooLong=Корисничкото име е предолго. Користете пократко корисничко име.
#XMSG: No privileges have been enabled and this database user will have limited functionality. Do you still want to continue?
noPrivilegesConfirmationMessage=Не се овозможени никакви привилегии, корисников на базата со податоци ќе има ограничена функционалност. Дали сакате да продолжите?
#XMSG: To enable audit logs for change operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogChangeMessage=За да ги овозможите дневниците за ревизија за операциите за менување, потребно е да активирате и увоз на големи количини податоци. Дали сакате да го направите ова?
#XMSG: To enable audit logs for read operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogReadMessage=За да ги овозможите дневниците за ревизија за операциите за читање, потребно е да активирате и увоз на големи количини податоци. Дали сакате да го направите ова?
#XMSG: To enable HDI consumption data ingestion and data consumption needs to be enabled. Do you want to enable all?
enableHDIConsumptionMessage=За да овозможите употреба на HDI, треба да се овозможат опциите увоз на големи количини податоци и употреба на податоци. Дали сакате да го направите ова?
#XMSG:
databaseUserPasswordText=За да ја поставите врската до овој корисник на базата со податоци, копирајте ја лозинката. Ако сте ја заборавиле лозинката, секогаш можете да побарате нова.
#XTIT: Space detail section members title
detailsSectionMembers=Членови
#XMSG: New password set
newPasswordSet=Поставена е нова лозинка
#XFLD: Data Ingestion
dataIngestion=Увоз на големи количини податоци
#XFLD: Data Consumption
dataConsumption=Потрошувачка на податоци
#XFLD: Privileges
privileges=Привилегии
#XFLD: Enable Data ingestion
enableDataIngestion=Овозможи Увоз на големи количини податоци
#XMSG: Log the read and change operations for data ingestion.
databaseUserAuditHint=Евидентирајте го читањето и променете ги операциите за увоз на големи количини податоци.
#XMSG: Make your space data available in your HDI containers.
databaseUserHDIHint=Направете ги податоците за просторот достапни во контејнерите HDI.
#XFLD: Enable Data consumption
enableDataConsumption=Овозможи употреба на податоци
#XMSG: Allow other apps or tools to consume your space data.
enableDataConsumptionHint=Дозволи други апликации или алатки да ги употребуваат податоците за просторот.
#XMSG: To set up access via this schema user copy the credentials to your user-provided service. In case you can only copy the credentials without a password make sure to add the password later.
databaseUserHDIConsumptionHint=За да поставите пристап преку овој корисник на базата со податоци, копирајте ги акредитивите во услугата што ја обезбедува корисникот. Во случај да можете да ги копирате само акредитивите без лозинка, подоцна додадете ја лозинката.
#XFLD: Data Flow Runtime Capacity label in space overview
dataFlowRuntimeCapacity=Капацитет на времето на извршување за податочниот тек ({0}:{1} часови од {2} часови)
#XFLD: Could not load Data Flow Runtime Capacity
dataFlowRuntimeCapacityError=Не може да се вчита капацитетот на времето на извршување за податочниот тек
#XMSG: User can grant data consumption to other users.
databaseUserConsumptionWithGrantOptionHint=Корисникот може да одобри употреба на податоци за другите корисници.
#XFLD: Enable data consumption with grant option
enableConsumptionWithGrantOption=Овозможи употреба на податоци со опцијата Одобри
#XMSG: To enable data consumption with grant option data consumption needs to be enabled. Do you want to enable both?
enableConsumptionWithGrantMessage=За да овозможите користење на податоците со опцијата за доделување, мора да се овозможи користење на податоците. Дали сакате да ги овозможите и двете?
#XFLD: Enable Automated Predictive Library (APL) and Predictive Analysis Library (PAL)
allowUsingScriptServerFunctionality=Овозможи Automated Predictive Library (APL) и Predictive Analysis Library (PAL)
#XMSG: User can use SAP HANA Cloud embedded machine learning functions.
allowUsingScriptServerFunctionalityHint=Корисникот може да ги користи функциите за машинско учење што се вградени во SAP HANA Cloud.
#XFLD: Password Policy
passwordPolicy=Политика за лозинки
#XMSG: Password Policy
passwordPolicyHint=Овозможете или оневозможете ја конфигурираната политика за лозинки овде.
#XFLD: Enable Password Policy
enablePasswordPolicy=Овозможи ја политиката за лозинки
#XMSG: Read Access to the Space Schema
readAccessTitle=Пристап за читање на шемата за просторот
#XMSG: read access hint
readAccessHint=Дозволи му на корисникот на базата со податоци да ги поврзе надворешните алатки со шемата на просторот и да ги чита приказите што се изложени за употреба.
#XFLD: Space Schema
spaceSchema=Шема на просторот
#XFLD: Enable Read Access (SQL)
enableReadAccess=Озвоможи пристап за читање (SQL)
#XMSG: Allow the user to grant read access to other users.
withGrantOptionHint=Дозволи му на корисникот да дава пристап за читање на други корисници.
#XFLD: With Grant Option
withGrantOption=Со опцијата за доделување
#XMSG: Make your space data available in your HDI containers.
enableHDIConsumptionHint=Направете ги податоците за просторот достапни во контејнерите HDI.
#XFLD: Enable HDI Consumption
enableHDIConsumption=Овозможи употреба на HDI
#XMSG: Write Access to the User’s Open SQL Schema
writeAccessTitle=Пристап за пишување во шемата Open SQL на корисникот
#XMSG: write access hint
writeAccessHint=Дозволете му на корисникот на базата со податоци да ги поврзе надворешните алатки со шемата Open SQL на корисникот за создавање податочни ентитети и увоз на податоци за употреба во просторот.
#XFLD: Open SQL Schema
openSQLSchema=Шема Open SQL
#XFLD: Enable Write Access (SQL DDL & DML)
enableWriteAccess=Овозможи пристап за читање (SQL, DDL, и DML)
#XMSG: audit hint
auditHint=Евидентирај ги операциите за читање и промена во шемата Open SQL.
#XMSG: data consumption hint
dataConsumptionHint=Стандардно изложи ги сите нови прикази во просторот за употреба. Алатките за уредување можат да ја заменат оваа поставка за поединечни прикази преку прекинувачот „Изложи за употреба“ во бочната табла за излез на прикази. Можете да ги изберете и форматите во кои се изложени приказите.
#XFLD: Expose for Consumption by Default
exposeDataConsumption=Стандардно изложи за употреба
#XMSG: database users hint consumption hint
databaseUsersHint2New=Создајте корисници на базите со податоци за поврзување на надворешните алатки со SAP Datasphere. Поставете ги привилегиите за да им дозволите на корисниците да ги читаат податоците за просторот и да создаваат податочни ентитети (DDL) и да увезуваат податоци (DML) за користење во просторот.
#XFLD: Read
read=Читај
#XFLD: Read (HDI)
readHDI=Читај (HDI)
#XFLD: Write
write=Запиши
#XMSG: HDI Containers Hint
HDIContainersHint2=Овозможете пристап до контејнерот SAP HANA Deployment Infrastructure (HDI) во просторот. Алатките за моделирање може да користат артефакти HDI како извори за прикази, и клиентите HDI можат да пристапат до податоците за просторот.
#XMSG: Open info dialog
openDatabaseUserInfoDialog=Отвори го дијалогот со информации
#XMSG: Database user is locked. Open info dialog to unlock
openLockedDatabaseUserInfoDialog=Корисникот на базата со податоци е заклучен. Отворете го дијалогот за да го отклучите
#XFLD: Table
table=Табела
#XFLD: Space section partner configuration title
partnerConnectionConfigurationSectionTitle=Врска на партнерот
#XFLD: Space section partner configuration subtitle
partnerConnectionConfigurationSectionSubTitle=Конфигурација на врската на партнерот
#XFLD: Space section partner configuration hint
partnerConnectionConfigurationSectionHint=Дефинирајте ја плочката за врската на партнерот со додавање на URL-адресата на iFrame и икона. Оваа конфигурација е достапна само за овој закупец.
#XFLD: Table Name Field
partnerConnectionConfigurationName=Назив на плочката
#XFLD: Table iFrame URL field
partnerConnectionConfigurationiFrameURL=iFrame URL
#XFLD: Table iFrame Post Message Origin field
partnerConnectionConfigurationiFramePostMessageOrigin=Потекло на порака за објавување на iFrame
#XFLD: Table icon/image name
partnerConnectionConfigurationIcon=Икона
#XMSG: Table no result found
partnerConnectionConfigurationNotfound=Не може да се најдат конфигурации за врската на партнерот
#XMSG: Partner Connection Confgurations are not available
partnerConnectionConfigurationNotAvailable=Конфигурациите за врските на партнерот не можат да се прикажат кога е недостапната базата со податоци за времето на извршување.
#XFLD: Dialog Create Partner Connection Configuration
partnerConnectionConfigurationDialogTitleAdd=Создај конфигурација за врската на партнерот
#XFLD: Dialog Create Partner Connection Configuration Upload Icon
partnerConnectionConfigurationButtonUloadPartnerIcon=Постави икона
#XFLD: Dialog Create Partner Connection Configuration Select
partnerConnectionConfigurationSelectIcon=Избери (максимална големина од 200KB)
#XFLD: Dialog Create Partner Connection Configuration Enter a name
partnerConnectionConfigurationNamePlaceholder=Пример за плочка на партнер
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL
partnerConnectionConfigurationiFrameURLPlaceholder=http://www.example.com
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL Check
partnerConnectionConfigurationiFramePostMessageOriginPlaceholder=example.com
#XFLD: Dialog Create Partner Connection Configuration Browse
partnerConnectionConfigurationBrowseButton=Прелистај
#XFLD: Create Partner Connection Configuration Msg
partnerConnectionConfigurationUploadMsg=Конфигурацијата за врската на партнерот е успешно создадена.
#XMSG: Error while deleting Partner Connection configuration(s)
partnerConnectionConfigurationDeleteError=Настана грешка при бришењето на конфигурациите за врската на партнерот.
#XMSG: Partner Connection Configuration has been successfully deleted
partnerConnectionConfigurationDeleteSuccess=Конфигурацијата за врската на партнерот е успешно избришана.
#XMSG: Error while retrieving Partner Connection Configurations
partnerConnectionConfigurationLoadError=Настана грешка при повикувањето на конфигурациите за врската на партнерот.
#XMSG: Error fileSizeLimitExceeded
partnerConnectionConfigurationfileSizeLimitExceeded=Датотеката не може да се постави бидејќи ја надминува максималната големина од 200 KB.
#XBUT: Create Partner Connection Configuration
partnerConnectionConfigurationButtonCreate=Создај конфигурација за врската на партнерот
#XBUT: Delete Partner Connection Configuration
partnerConnectionConfigurationButtonDelete=Избриши ја конфигурацијата на врската на партнерот.
#XMSG: Partner Tile Creation Failure
upsertPartnerTileFailed=Плочката за партнерот не може да се создаде.
#XMSG: Partner Tile Deletion Failure
deletePartnerTileFailed=Плочката за партнерот не може да се избрише.
#XMSG: HANA cloud connector settings reset failure
resetHANASCCSettingsFailed=Ресетирањето на поставките за SAP HANA Cloud Connector не успеа
#XFLD: Workload Class
workloadClass=Класа на работно оптеретување
#XFLD: Workload Management
workloadManagement=Управување со работно оптеретување
#XFLD: Priority
workloadClassPriority=Приоритет
#XMSG:
workloadManagementPriorityHint=Можете да го одредите приоритетот на овој простор при поставувањето прашалници за базата со податоци. Внесете вредност од 1 (најнизок приоритет) до 8 (највисок приоритет). Во ситуација кога просторите се натпреваруваат за достапни нишки, тие со повисоки приоритети се извршуваат пред просторите со пониски приоритети.
#XMSG:
workloadClassPriorityHint=Можете да го одредите приоритетот на просторот од 0 (најниско) до 8 (највисокo). Наредбите за простор со висок приоритет се извршуваат пред наредбите на другите простори со понизок приоритет. Стандардниот приоритет е 5. Бидејќи вредноста 9 е резервирана за системските операции, таа не е достапна за просторот.
#XFLD: Statement Limits
workloadclassStatementLimits=Ограничувања на наредба
#XFLD: Workload Configuration
workloadConfiguration=Конфигурација за работното оптеретување
#XMSG:
workloadClassStatementLimitsHint=Можете да го одредите максималниот број (или процент) нишки или меморија во GB што можат да ги потрошат наредбите што се извршуваат истовремено. Можете да внесете која било вредност или процент меѓу 0 (без ограничување) и вкупната меморија и број на нишки достапни во закупецот. \n\n Ако поставите ограничување на нишката, имајте предвид дека тоа може да ги намали перформансите. \n\n Ако наведете ограничување на меморијата, наредбите што го достигнуваат ограничувањето на меморијата нема да се извршат.
#XMSG:
workloadClassStatementLimitsDescription=Стандардната конфигурација обезбедува богати ограничувања на ресурсите, а истовремено спречува кој било поединечен простор да го преоптовари системот.
#XMSG:
workloadClassStatementLimitCustomDescription=Можете да поставите ограничување на максималниот вкупен број нишки и меморија за тоа кои наредби што се извршуваат истовремено може да се користат.
#XMSG:
totalStatementThreadLimitHelpText=Поставувањето на ограничувањето на нишката на прениска вредност може да влијае на перформансот на наредбата, додека превисоките вредности или 0 можат да дозволат просторот да ги употреби сите достапни нишки на системот.
#XMSG:
totalStatementMemoryLimitHelpText=Поставувањето на ограничувањето на нишката на прениска вредност може да предизвика проблеми со недостаток на меморија, додека превисоките вредности или 0 можат да дозволат просторот да ја употреби целата достапна меморија на системот.
#XMSG:
totalStatementThreadLimitHelpTextRestricted=Внесете процент меѓу 1 % и 70 % (или еквивалентен број) на вкупниот број нишки што се достапни во закупецот. Поставувањето на ограничувањето на нишката на прениско може да влијае врз перформансот на наредбата, додека превисоките вредности можат да влијаат врз перформансот на наредбите во други простори.
#XMSG:
totalStatementThreadLimitHelpTextDynamic=Внесете процент меѓу 1 % и {0} % (или еквивалентен број) на вкупниот број нишки што се достапни во закупецот. Поставувањето на ограничувањето на нишката на прениско може да влијае врз перформансот на наредбата, додека превисоките вредности можат да влијаат врз перформансот на наредбите во други простори.
#XMSG:
totalStatementMemoryLimitHelpTextNew=Внесете вредности или процент меѓу 0 (без ограничување) и вкупниот износ на меморија што е достапна во закупецот. Поставувањето на ограничувањето на меморијата на прениско може да влијае врз перформансот на наредбата, додека превисоките вредности можат да влијаат врз перформансот на наредбите во други простори.
#XFLD: Total Statement Thread Limit
totalStatementThreadLimit=Вкупно ограничување на нишката на наредбата
#XFLD: Segmented button label
workloadclassPercentage=%
#XFLD: Segmented button label
workloadclassGigaByte=GB
#XFLD, 80: Segmented button label
workloadclassThreads=Нишки
#XFLD: Total Statement Memory Limit
totalStatementMemoryLimit=Вкупно ограничување на меморијата на наредбата
#YMSE: Error while loading customer hana info
loadCustomerHanaInfoError=Вчитувањето на корисничките податоци за SAP HANA не успеа.
#XMSG:
minimumLimitReached=Достигнато е минималното ограничување.
#XMSG:
maximumLimitReached=Достигнато е максималното ограничување.
#XMSG: Name Taken for Technical Name
technical-name-taken=Веќе постои врска со техничкиот назив што го внесовте. Изберете друг назив.
#XMSG: Name Too long for Technical Name
technical-name-too-long=Техничкиот назив што го внесовте надминува 40 знаци. Внесете назив со помалку карактери.
#XMSG: Technical name field empty
technical-name-field-empty=Внесете технички назив.
#XMSG: Generic error when user enters invalid characters for a field
field-invalid-characters=Тука може да користите само букви (a – ш), бројки (0 – 9) и долни црти (_) за називот.
#XMSG: Generic error when user enters invalid initial characters for a field
field-underscore-trim=Називот што ќе го внесете не може да почнува или да завршува со долна црта (_).
#XFLD: Checkbox label for Enable Statement Limits
enableStatementLimits=Овозможи ограничувања на наредба
#XFLD: Connection SubSection Settings Title in space details
connectionSettings=Поставки
#XMSG: Connections tool hint in Space details section
connectionsToolHint=За да создадете или уредите врски, отворете ја апликацијата Врски од страничната навигација или кликнете тука:
#XBUT: Connection list Open Connection Tool Button
button-connectiontool=Појди во Врски
#XFLD: Not deployed label on space tile
notDeployedLabel=Просторот сè уште не е применет.
#XFLD: Not deployed additional text on space tile
notDeployedText=Применете го просторот.
#XFLD: Corrupt space label on space tile
corruptSpace=Настана грешка.
#XFLD: Corrupt space additional text on space tile
corruptSpaceHint=Обидете се повторно да примените или контактирајте со поддршката
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Податоци за дневникот за ревизија
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Административни податоци
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Други податоци
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Податоци во просторите
#XMSG: Unlock Space confirmation text
unlockSpaceConfirmation=Дали навистина сакате да го отклучите просторот?
#XMSG: Lock Space confirmation text
lockSpaceConfirmation=Дали навистина сакате да го заклучите просторот?
#XFLD: Lock
lock=Заклучи
#XFLD: Unlock
unlock=Отклучи
#XFLD: Locking
locking=Се заклучува
#XMSG: Success message after locking space
lockSpaceSuccessMsg=Се отклучува
#XMSG: Success message after unlocking space
unlockSpaceSuccessMsg=Просторот е отклучен
#XMSG: Success message after locking spaces
lockSpacesSuccessMsg=Просторите се заклучени
#XMSG: Success message after unlocking spaces
unlockSpacesSuccessMsg=Просторите се отклучени
#YMSE: Error while locking a space
lockSpaceError=Просторот не може да се заклучи.
#YMSE: Error while unlocking a space
unlockSpaceError=Просторот не може да се отклучи.
#XTIT: popup title Warning
confirmationWarningTitle=Предупредување
#XMSG: Message strip message when a space was locked manually
manualLockedMessage=Просторот е заклучен рачно.
#XMSG: Message strip message when a space was locked due to the amount of generated audit logs
auditLogLockedMessage=Системот го заклучи просторот бидејќи дневниците за ревизија користат голема количина GB на дискот.
#XMSG: Message strip message when a space was locked because the assigned quota was exceeded.
quotaLockedMessage=Системот го заклучи просторот бидејќи ги надминува своите доделувања на просторот во меморијата или меморијата на дискот.
#XMSG: Unlock Spaces confirmation text
unlockSpacesConfirmation=Дали навистина сакате да ги отклучите избраните простори?
#XMSG: Lock Spaces confirmation text
lockSpacesConfirmation=Дали навистина сакате да ги заклучите избраните простори?
#XTIT: Scoped role editor dialog title
scopedRoleEditorDialogTitle=Уредник за улоги со одреден опсег
#XTIT: ECN Management title
ecnManagementTitle=Управување со просторите и еластичните јазли за пресметка
#XFLD: ECNs
ecns=Еластични јазли на пресметка
#XFLD: ECN phase Ready
ecnReady=Подготвено
#XFLD: ECN phase Running
ecnRunning=Се извршува
#XFLD: ECN phase Initial
ecnInitial=Не е подготвено
#XFLD: ECN phase Starting
ecnStarting=Започнува
#XFLD: ECN phase Starting Failed
ecnStartingFailed=Започнувањето не успеа
#XFLD: ECN phase Stopping
ecnStopping=Се запира
#XFLD: ECN phase Stopping Failed
ecnStoppingFailed=Запирањето не успеа
#XBTN: Assign Button
assign=Додај простори
#XBTN: Start Header-Button
start=Започни
#XBTN: Update Header-Button
repair=Ажурирај
#XBTN: Stop Header-Button
stop=Запри
#XFLD: ECN hours remaining
ecnHoursRemaining=преостануваат 1 000 часа
#XFLD: ECN block-hours remaining
ecnBlockHoursRemaining={0} преостанати блок-часови
#XFLD: ECN block-hour remaining
ecnBlockHourRemaining={0} преостанат блок-часови
#XTIT: ECN creation dialog title
ecnCreationDialogTitle=Создај еластичен јазол на пресметка
#XTIT: ECN edit dialog title
ecnEditDialogTitle=Уреди го еластичниот јазол на пресметка
#XTIT: ECN deletion dialog title
ecnDeletionDialogTitle=Избриши го еластичниот јазол на пресметка
#XTIT: ECN Space assignment dialog title
ecnSpaceAssignmentDialogTitle=Додај простори
#XFLD: ECN ID
ECNIDLabel=Еластичен јазол на пресметка
#XTXT: Selected toolbar text
selectedToolbarText=Избрано: {0}
#XTIT: Elastic Compute Nodes
ECNslong=Еластични јазли на пресметка
#XTIT: Object assignment - Table header: Objects available
objectsAvailable=Број на објекти
#XTIT: Object assignment - Dialog header text
selectObjects=Изберете ги просторите и објектите што сакате да ги додадете во еластичниот јазол на пресметка:
#XTIT: Object assignment - Table header title: Objects
objects=Објекти
#XTIT: Object assignment - Table header: Type
type=Тип
#XMSG: Message strip delete database user audit warning
deleteDatabaseUserWarningMessageStrip=Имајте предвид дека бришењето на корисникот на базата со податоци ќе доведе до бришење на сите генерирани записи во дневникот за ревизија. Ако сакате да ги задржите дневниците за ревизија, размислете да ги извезете пред да го избришете корисникот на базата со податоци.
#XMSG: Message strip delete HDI container audit logs impact
deleteHDIContainerWarningMessageStrip=Имајте предвид дека поништувањето на доделувањето на контејнерот HDI од просторот ќе доведе до бришење на сите генерирани записи во дневникот за ревизија. Ако сакате да ги задржите дневниците за ревизија, размислете да ги извезете пред да го поништите доделувањето на контејнерот HDI.
#XTXT: All audit logs
allAuditLogs=Сите записи во дневникот за ревизија се генерирани за просторот
#XMSG: Message strip disable audit warning
disableAuditWarningMessageStrip=Имајте предвид дека оневозможувањето на политиката за ревизија (операциите за читање и промена) ќе доведат до бришење на сите записи во дневникот за ревизија. Ако сакате да ги зачувате записите во дневникот за ревизија, размислете да ги извезете пред да ја оневозможите политиката за ревизија.
#XTIT: No Spaces assigned to ECN Illustration title
noSpacesECNTitle=Сè уште не се додадени простори и објекти
#XTIT: No Spaces assigned to ECN Illustration description
noSpacesECNDescription=За да започнете со работење на еластичниот јазол за пресметка, додајте простор или објекти во него.
#XTIT: No Spaces Illustration title
noSpacesTitle=Сè уште не е создаден простор
#XTIT: No Spaces Illustration description
noSpacesDescription=За да почнете да собирате податоци, создајте простор.
#XTIT: No Spaces Illustration title
noDeletedSpacesTitle=Корпата за отпадоци е празна
#XTIT: No Spaces Illustration description
noDeletedSpacesDescription=Избришаните простори може да ги обновите од овде.
#XTXT:  {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDatabaseUsers=Откако просторот ќе се примени, следниве корисници на базата со податоци ќе {0} се избришат и нема да можат да се обноват:
#XTIT: Delete database users
deleteDatabaseUsersTitle=Корисници на базата со податоци
#YMSE: Error if ecn id already exists
ecnIDInputDuplicateError=ИД-бројот веќе постои.
#YMSE: Error if ecn id contains invalid characters
ecnIDInputInvalidError=Користете само мали букви a – ш и броеви 0 – 9
#YMSE: Error if ecn id is too short
ecnIDInputTooShortError=ИД-бројот мора да содржи најмалку {0} знаци.
#XMSG: ecn id length warning
ecnIdLengthWarning=Надминат е максималниот број од {0} знаци.
#XFLD: open System Monitor
systemMonitor=Монитор на системот
#XFLD: open ECN schedule dialog menu entry
schedule=Распоред
#XFLD: open create ECN schedule dialog
createSchedule=Создај распоред
#XFLD: open change ECN schedule dialog
changeSchedule=Уреди го распоредот
#XFLD: open delete ECN schedule dialog
deleteSchedule=Избриши го распоредот
#XFLD: open self assign ECN schedule dialog
selfAssignSchedule=Додели ми го распоредот
#XFLD: open pause ECN schedule dialog
pauseSchedule=Паузирај го распоредот
#XFLD: open resume ECN schedule dialog
resumeSchedule=Продолжи го распоредот
#XFLD: View Logs
viewLogs=Прикажи ги дневниците
#XFLD: Compute Blocks
computeBlocks=Блокови за пресметка
#XFLD: Memory label in ECN creation dialog
ecnMemory=Меморија (GB)
#XFLD: Storage label in ECN creation dialog
ecnStorage=Склад (GB)
#XFLD: Number of CPU label in ECN creation dialog
ecnCPUCount=Број CPU
#XFLD: ECN updated by label
changedBy=Променето од
#XFLD: ECN updated on label
changedOn=Променето на
#XMSG: Success message after creating a Elastic Compute Node
createEcnSuccessMessage=Создаден е еластичен јазол на пресметка
#YMSE: Error while creating a Elastic Compute Node
createEcnError=Не може да се создаде еластичен јазол на пресметка
#XMSG: Success message after updating a Elastic Compute Node
updateEcnSuccessMessage=Еластичниот јазол на пресметка е ажуриран
#XMSG: Error while updating a Elastic Compute Node
udpateEcnError=Еластичниот јазол на пресметка не може да се ажурира
#XMSG: Success message after deleting a Elastic Compute Node
deleteEcnSuccessMessage=Еластичниот јазол на пресметка е избришан
#YMSE: Error while deleting a Elastic Compute Node
deleteEcnError=Еластичниот јазол на пресметка не може да се избрише
#XMSG: Success message after starting a Elastic Compute Node
startingEcnSuccessMessage=Започнување на еластичниот јазол на пресметка
#XMSG: Success message after stopping a Elastic Compute Node
stoppingEcnSuccessMessage=Запирање на еластичниот јазол на пресметка
#YMSE: Error while starting a Elastic Compute Node
startingEcnError=Еластичниот јазол на пресметка не може да започне
#YMSE: Error while stopping a Elastic Compute Node
stoppingEcnError=Еластичниот јазол на пресметка не може да запре
#XBUT: Add Object button for an ECN
assignObjects=Додај објекти
#XFLD: Enable auto object assignment label
enableAutoObjAssignment=Автоматски додај ги сите објекти
#XFLD: object type label to be assigned
objectTypeLabel=Тип (Семантичка употреба)
#XFLD: assigned object type label
assignedObjectTypeLabel=Тип
#XFLD: technical name label
TechnicalNameLabel=Технички назив
#XFLD: Object selecttion ECN label
objectSelectionECNLabel=Изберете ги објектите што сакате да ги додадете во еластичниот јазол на пресметка
#XTIT: Add objects dialog title
assignObjectsTitle=Додај објекти од
#XFLD: object label with object count
objectLabel=Објект
#XMSG: No objects available to add message.
noObjectsToAssign=Нема објекти достапни за додавање.
#XMSG: No objects assigned message.
noAssignedObjects=Не се додадени објекти.
#XTIT: Remove objects popup title
removeObjectsOrSpacesConfirmationTitle=Предупредување
#XTIT: Delete ECN popup button and title
deleteECNConfirmationButtonAndTitle=Избриши
#XMSG: Remove objects popup text
removeObjectsConfirmation=Дали навистина сакате да ги отстраните избраните објекти?
#XMSG: Remove spaces popup text
removeSpacesConfirmation=Дали навистина сакате да ги отстраните избраните простори?
#XBUT: Confirmation button for objects dialog
removeObjectsOrSpacesConfirmationBtn=Отстрани простори
#XMSG: Success message after removing objects
removeObjectsSuccessMessage=Изложените објекти се отстранети
#XMSG: Success message after assigning objects
assignObjectsSuccessMessage=Изложените објекти се доделени
#XFLD: auto object assignment label for space tile
autoObjAssignmentLabel=Сите изложени објекти
#XFLD: Spaces tab label
spacesTabLabel=Простори
#XFLD: Exposed objects tab label
consumptionObjectsTabLabel=Изложени објекти
#XMSG: Success message after unassigning spaces
spacesUnassignedSuccessMessage=Просторите се отстранети
#XMSG: Success message after unassigning space
spaceUnassignedSuccessMessage=Просторот е отстранет
#YMSE: Error while assigning / unassigning spaces
spacesAssignUnassignError=Просторите не може да се доделат или отстранат.
#YMSE: Error while removing objects
removeObjectsError=Не можеме да ги доделиме или отстраниме објектите.
#YMSE: Error while removing object
removeObjectError=Не можеме да го доделиме или отстраниме објектот.
#YMSE: Error when saving the ecn with invalid value
invalidSizingPlanError=Претходно избраниот број повеќе не е важечки. Изберете важечки број.
#YMSE: Error when saving the ecn with invalid value
invalidPerformanceClassError=Изберете важечка класа на учинокот.
#YMSE: Error when saving the ecn with invalid value
performanceClassNotAvailableError=Претходно избраната класа на учинок „{0}“ во моментов не е важечка. Изберете важечка класа за учинокот.
#XTXT: Delete ECN confirmation text
deleteConfirmationTxtECN=Дали сигурно сакате да го избришете еластичниот јазол на пресметка?
#XFLD: tooltip for ? button
help=Помош
#XFLD: ECN edit button label
editECN=Конфигурирај
#XFLD: Technical type label for ERModel
DWC_ERMODEL=Модел на односи на ентитет
#XFLD: Technical type label for Local Table
DWC_LOCAL_TABLE=Локална табела
#XFLD: Technical type label for Remote Table
DWC_REMOTE_TABLE=Табела на далечина
#XFLD: Technical type label for Analytic Model
DWC_ANALYTIC_MODEL=Аналитички модел
#XFLD: Technical type label for Task Chain
DWC_TASKCHAIN=Синџир од задачи
#XFLD: Technical type label for Data Flow
DWC_DATAFLOW=Податочен тек
#XFLD: Technical type label for Replication Flow
DWC_REPLICATIONFLOW=Репликациски тек
#XFLD: Technical type label for Transformation Flow
DWC_TRANSFORMATIONFLOW=Трансформациски тек
#XFLD: Technical type label for Intelligent Lookup
DWC_IDT=Интелигентно пребарување
#XFLD: Technical type label for Repository
DWC_REPOSITORY_PACKAGE=Репозиториум
#XFLD: Technical type label for Enterprise Search
DWC_ESMODEL=Пребарување претпријатија
#XFLD: Technical type label for View
DWC_VIEW=Приказ
#XFLD: Technical type label for Data Product
DWC_DATAPRODUCT=Податочен производ
#XFLD: Technical type label for Data Access Control
DWC_DAC=Контрола за пристап до податоци
#XFLD: Technical type label for Folder
DWC_FOLDER=Папка
#XFLD: Technical type label for Data Entity
DWC_BUSINESS_ENTITY=Деловен ентитет
#XFLD: Technical type label for Business Entity Variant
DWC_BUSINESS_ENTITY_VARIANT=Варијанта на деловниот ентитет
#XFLD: Technical type label for Responsibility Scenario
DWC_AUTH_SCENARIO=Сценарио за одговорност
#XFLD: Technical type label for Fact Model
DWC_FACT_MODEL=Модел на факти
#XFLD: Technical type label for Transformation Flow
DWC_PERSPECTIVE=Перспектива
#XFLD: Technical type label for Consumption Model
DWC_CONSUMPTION_MODEL=Модел на потрошувачка
#XFLD: Technical type label for Remote Connection
DWC_REMOTECONNECTION=Далечинска врска
#XFLD: Technical type label for Fact Model Variant
DWC_FACT_MODEL_VARIANT=Варијанта на моделот на факти
#XMSG: Schedule created alert message
createScheduleSuccess=Распоредот е создаден
#XMSG: Schedule updated alert message
updateScheduleSuccess=Распоредот е ажуриран
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Распоредот е избришан
#XMSG: Schedule takeover success message
ownerChangeScheduleSuccess=Распоредот Ви е доделен
#XMSG: Message for starting pausing of schedule
pauseScheduleStarted=Се паузира 1 распоред
#XMSG: Message for starting resuming of schedule
resumeScheduleStarted=Се продолжува 1 распоред
#XFLD: Segmented button label
availableSpacesButton=Достапно
#XFLD: Segmented button label
selectedSpacesButton=Избрано
#XFLD: Visit website button text
visitWebsite=Посети веб-локација
#XMSG: Warning message on unselecting source language
sourceLanguageRemovalWarning=Претходно избраниот изворен јазик ќе се отстрани.
#XBUT: enable translation confirmation button
enableTranslationConfirmationBtn=Овозможи
#XFLD: ECN performance class label
performanceClassLabel=Класа на учинок
#XTXT performance class memory text
memoryText=Меморија
#XTXT performance class compute text
computeText=Пресметај
#XTXT performance class high-compute text
highComputeText=Пресметка од високо ниво
#XBUT: Recycle Bin Button Text
recycleBin=Корпа за отпадоци
#XBUT: Restore Button Text
restore=Обнови
#XMSG: Warning message for new Workload Management UI
priorityWarning=Оваа област е само за читање. Може да го промените приоритетот на просторот во Систем/Конфигурација/Област за управување со работното оптоварување.
#XMSG: Warning message for new Workload Management UI
workloadWarning=Оваа област е само за читање. Може да ја промените конфигурацијата на просторот за работно оптоварување во Систем/Конфигурација/Област за управување со работното оптоварување.
#XCOL: Space table-view column: Apache Spark vCPUs
sparkManagementVCPUs=vCPU-единици на Apache Spark
#XCOL: Space table-view column: Apache Spark Memory (GB)
sparkManagementMemory=Меморија (GB) на Apache Spark
#XCOL: Space type (from Space Capability) for Data Ingestion Spaces
ingestionSpaceType=Увоз на голема количина податочни производи
#XFLD: Deployment in progress nodatatext for spark table
sparkTableDeploymentInProgress=Нема достапни податоци бидејќи просторот се применува во моментов
#XFLD: Runtime data being loaded nodatatext for spark table
sparkTableDeploymentLoading=Нема достапни податоци бидејќи просторот се вчитува во моментов
#XCOL: Space type (from Space Capability) for BW Private Cloud Edition
BWPCESpaceType=SAP BW
#XBUT: Go to Instance Mappings Button
navToMappingsButton=Уреди ги мапирањата на инстанцата
#XFLD: SAP HANA Data Lake Storage label if less than 1GB
dataLakeStorageUsed=< 1 GB
