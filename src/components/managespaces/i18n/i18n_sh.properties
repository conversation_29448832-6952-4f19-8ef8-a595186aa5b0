#DO NOT REMOVE________________________
#XFLD: Breadcrumb for space monitoring page
spaceMonitorBreadcrumb=Nadzor
#XTXT: Type name for spaces in browser tab page title
space=Prostor
#_____________________________________
#XFLD: Spaces label in
spaces=Prostori
#XFLD: Manage plan button text
manageQuotaButtonText=Upravljaj planom
#XBUT: Manage resources button
manageResourcesButton=Upravljaj resursima
#XFLD: Create space button tooltip
createSpace=Kreiraj prostor
#XFLD: Create
create=Kreiraj
#XFLD: Deploy
deploy=Implementiraj
#XFLD: Page
page=Stranica
#XFLD: Cancel
cancel=Odustani
#XFLD: Update
update=Ažuriraj
#XFLD: Save
save=Sačuvaj
#XFLD: OK
ok=OK
#XFLD: days
days=Dani
#XFLD: Space tile edit button label
edit=Uredi
#XFLD: Auto Assign all objects to space
autoAssign=Automatski dodeli
#XFLD: Space tile open monitoring button label
openMonitoring=Nadgledaj
#XFLD: Delete
delete=Izbriši
#XFLD: Copy Space
copy=Kopiraj
#XFLD: Close
close=Zatvori
#XCOL: Space table-view column status
status=Status
#XFLD: Space status active
activeLabel=Aktivno
#XFLD: Space status locked
lockedLabel=Zaključano
#XFLD: Space status critical
criticalLabel=Kritično
#XFLD: Space status cold
coldLabel=Neaktivno
#XFLD: Space status deleted
deletedLabel=Izbrisano
#XFLD: Space status unknown
unknownLabel=Nepoznato
#XFLD: Space status ok
okLabel=U dobrom stanju
#XFLD: Database user expired
expired=Isteklo
#XFLD: deployed
deployed=Implementirano
#XFLD: not deployed
notDeployed=Nije implementirano
#XFLD: changes to deploy
changesToDeploy=Promene za implementaciju
#XFLD: pending
pending=Implementacija
#XFLD: designtime error
designtimeError=Greška vremena dizajna
#XFLD: runtime error
runtimeError=Greška vremena izvođenja
#XFLD: Space created by label
createdBy=Kreirao
#XFLD: Space created on label
createdOn=Kreirano
#XFLD: Space deployed on label
deployedOn=Implementirano
#XFLD: Space ID label
spaceID=ID prostora
#XFLD: Priority label
priority=Prioritet
#XFLD: Space Priority label
spacePriority=Prioritet prostora
#XFLD: Space Configuration label
spaceConfiguration=Konfiguracija prostora
#XFLD: Not available
notAvailable=Nije dostupno
#XFLD: WorkloadType default
default=Standardno
#XFLD: WorkloadType custom
custom=Korisnički definisano
#XFLD: Data Lake option label; refers to component SAP HANA Cloud, Data Lake
dataLakeAccess=Pristup jezeru podataka
#XFLD: Translation label
translationLabel=Prevod
#XFLD: Source language label
sourceLanguageLabel=Izvorni jezik
#XFLD: Translation CheckBox label
translationCheckBox=Aktiviraj prevod
#XTXT: Popover hint that space has to be deployed to show user details
openDatabaseUserHintPopoverText=Implementirajte prostor da biste pristupili detaljima korisnika.
#XTXT: Popover hint that space has to be deployed to open database explorer
openDatabaseExplorerHintPopoverText=Implementirajte prostor da biste otvorili pretraživač baze podataka.
#XTXT: Text for when the Data Lake check box is disabled
dataLakeOptionDisabled=Ne možete koristiti ovaj prostor za pristup jezeru podataka jer ga već koristi drugi prostor.
#XFLD: Data Lake check box text; refers to component SAP HANA Cloud, Data Lake
dataLakeCheckBox=Koristite ovaj prostor za pristup jezeru podataka.
#XFLD: Space Priority minimum label extension
low=Nisko
#XFLD: Space Priority maximum label extension
high=Visoko
#XFLD: Space name label
spaceName=Naziv prostora
#XFLD: Enable deploy objects checkbox
enableDeployObjects=Implementiraj objekte
#XTIT: Copy spaces dialog title
copySpaceDialogTitle=Kopiraj {0}
#XTXT Human readable text for unselected source language
unSelectedSourceLanguage=(Nije odabrano)
#XTXT Human readable text for language code "af"
af=Afrikans
#XTXT Human readable text for language code "ar"
ar=Arapski
#XTXT Human readable text for language code "bg"
bg=Bugarski
#XTXT Human readable text for language code "ca"
ca=Katalonski
#XTXT Human readable text for language code "zh"
zh=Pojednostavljeni kineski
#XTXT Human readable text for language code "zf"
zf=Kineski
#XTXT Human readable text for language code "hr"
hr=Hrvatski
#XTXT Human readable text for language code "cs"
cs=Češki
#XTXT Human readable text for language code "cy"
cy=Velški
#XTXT Human readable text for language code "da"
da=Danski
#XTXT Human readable text for language code "nl"
nl=Holandski
#XTXT Human readable text for language code "en-UK"
en-UK=Engleski (Ujedinjeno Kraljevstvo)
#XTXT Human readable text for language code "en"
en=Engleski (Sjedinjene Američke Države)
#XTXT Human readable text for language code "et"
et=Estonski
#XTXT Human readable text for language code "fa"
fa=Persijski
#XTXT Human readable text for language code "fi"
fi=Finski
#XTXT Human readable text for language code "fr-CA"
fr-CA=Francuski (Kanada)
#XTXT Human readable text for language code "fr"
fr=Francuski
#XTXT Human readable text for language code "de"
de=Nemački
#XTXT Human readable text for language code "el"
el=Grčki
#XTXT Human readable text for language code "he"
he=Hebrejski
#XTXT Human readable text for language code "hi"
hi=Hindi
#XTXT Human readable text for language code "hu"
hu=Mađarski
#XTXT Human readable text for language code "is"
is=Islandski
#XTXT Human readable text for language code "id"
id=Indonežanski
#XTXT Human readable text for language code "it"
it=Italijanski
#XTXT Human readable text for language code "ja"
ja=Japanski
#XTXT Human readable text for language code "kk"
kk=Kazaški
#XTXT Human readable text for language code "ko"
ko=Korejski
#XTXT Human readable text for language code "lv"
lv=Letonski
#XTXT Human readable text for language code "lt"
lt=Litvanski
#XTXT Human readable text for language code "ms"
ms=Malajski
#XTXT Human readable text for language code "no"
no=Norveški
#XTXT Human readable text for language code "pl"
pl=Poljski
#XTXT Human readable text for language code "pt"
pt=Portugalski (Brazil)
#XTXT Human readable text for language code "pt-PT"
pt-PT=Portugalski (Portugal)
#XTXT Human readable text for language code "ro"
ro=Rumunski
#XTXT Human readable text for language code "ru"
ru=Ruski
#XTXT Human readable text for language code "sr"
sr=Srpski
#XTXT Human readable text for language code "sh"
sh=Srpskohrvatski
#XTXT Human readable text for language code "sk"
sk=Slovački
#XTXT Human readable text for language code "sl"
sl=Slovenački
#XTXT Human readable text for language code "es"
es=Španski
#XTXT Human readable text for language code "es-MX"
es-MX=Španski (Meksiko)
#XTXT Human readable text for language code "sv"
sv=Švedski
#XTXT Human readable text for language code "th"
th=Tajlandski
#XTXT Human readable text for language code "tr"
tr=Turski
#XTXT Human readable text for language code "uk"
uk=Ukrajinski
#XTXT Human readable text for language code "vi"
vi=Vijetnamski
#XTIT: Delete spaces confirmation title
deleteConfirmationHeaderText=Izbriši prostore
#XTXT: Logically delete space delete confirmation text with space name
logicallyDeleteConfirmationBodyTextWithSpaceName=Da li sigurno želite da pomerite prostor "{0}" u korpu za otpatke?
#XTXT: Logically dpace delete confirmation text for multiple spaces
logicallyDeleteConfirmationBodyTextMultiple=Da li sigurno želite da pomerite {0} odabranih prostora u korpu za otpatke?
#XTXT: Delete space delete confirmation text with space name
deleteConfirmationBodyTextWithSpaceName=Da li zaista želite da izbrišete prostor "{0}"? Ova radnja se ne može poništiti.
#XTXT: Delete space delete confirmation text for multiple spaces
deleteConfirmationBodyTextMultiple=Da li zaista želite da izbrišete {0} odabranih prostora? Ova radnja se ne može poništiti. Sledeći sadržaj će biti {1} izbrisan:
#XTXT: permanently
permanently=trajno
#XTXT: {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDetail=Sledeći sadržaj će biti {0} izbrisan i ne može se obnoviti:
#XTXT: Hint Delete space confirmation text
deleteConfirmationHint=Upišite {0} da biste potvrdili brisanje.
#XTXT: Delete space confirmation error check spelling
deleteConfirmationErrorCheckSpelling=Proverite pravopis i pokušajte ponovo.
#XTXT: All Spaces
allSpaces=Svi prostori
#XTXT: All data
allData=Svi objekti i podaci sadržani u prostoru
#XTXT: All connections
allConnections=Sve veze definisane u prostoru
#XFLD: Space tile selection box tooltip
clickToSelect=Kliknite da odaberete
#XTXT: All database users
allDatabaseUsers=Svi objekti i podaci sadržani u svim Open SQL šemama povezanim s prostorom
#XFLD: remove members button tooltip
deleteUsers=Ukloni članove
#XTXT: Space long description text
description=Opis (maksimalno 4000 znakova)
#XFLD: Add Members button tooltip
addUsers=Dodaj članove
#XFLD: Add Users button tooltip
addUsersTooltip=Dodaj korisnike
#XFLD: Edit Users button tooltip
editUsersTooltip=Uredi korisnike
#XFLD: Remove Users button tooltip
removeUsersTooltip=Ukloni korisnike
#XFLD: Searchfield placeholder
filter=Traži
#XCOL: Users table-view column health
health=Ispravnost
#XCOL: Users table-view column access
access=Pristup
#XFLD: No user found nodatatext
noDataText=Korisnik nije nađen
#XTIT: Members dialog title
selectUserDialogTitle=Dodaj članove
#XTIT: User dialog title
addUserDialogTitle=Dodaj korisnike
#XTIT: Delete connections confirmation title
deleteConnectionConfirmationHeaderText=Izbriši veze
#XTIT: Delete single connection confirmation title
deleteConnectionConfirmationHeaderTextSingle=Izbriši vezu
#XTXT: Delete connection delete confirmation text
deleteConnectionConfirmationBodyText=Da li zaista želite da izbrišete odabrane veze? Biće trajno uklonjene.
#XTIT: Remote source dialog title
selectRemoteConnectionDialogTitle=Odaberi veze
#XTIT: Share connection dialog title
connectionSharingDialogTitle=Deli vezu
#XTIT: Connections shared from other spaces table title
sharedConnectionsFromOtherSpaces=Deljene veze
#XFLD: Add remote source button tooltip
addRemoteConnections=Dodaj veze
#XFLD: Delete remote source button tooltip
deleteRemoteConnections=Ukloni veze
#XFLD: Share remote source button tooltip
shareConnections=Deli veze
#XFLD: Tile-layout tooltip
tileLayout=Izgled podekrana
#XFLD: Table-layout tooltip
tableLayout=Izgled tabele
#XMSG: Success message after creating space
createSpaceSuccessMessage=Prostor kreiran
#XMSG: Success message after copying space
copySpaceSuccessMessage=Kopiranje prostora "{0}" u prostor "{1}"
#XMSG: Success message after deploying space
deploymentSuccessMessage=Implementacija prostora je pokrenuta
#XMSG: Success message after Apache Spark update started
sparkUpdateStartedSuccessMessage=Ažuriranje mehanizma Apache Spark pokrenuto
#YMSE: Error while starting Apache Spark update
sparkUpdateStartedErrorMessage=Nije uspelo ažuriranje mehanizma Apache Spark
#XMSG: Success message after updating space
updateSettingsSuccessMessage=Detalji prostora ažurirani
#XMSG: Success message after unlocking space
unlockSpaceSuccessMessage=Prostor privremeno otključan
#XMSG: Success message after deleting a single space
deleteSpaceSuccessMessage=Prostor izbrisan
#XMSG: Success message after deleting multiple spaces
deleteSpacesSuccessMessage=Prostori izbrisani
#XMSG: Success message after restoring a single space
restoreSpaceSuccessMessage=Prostor obnovljen
#XMSG: Success message after restoring multiple spaces
restoreSpacesSuccessMessage=Prostori obnovljeni
#YMSE: Error while updating settings
updateSettingsFailureMessage=Podešavanja prostora se ne mogu ažurirati.
#YMSE: Error while updating data lake option
dataLakeEnabledInOtherSpace=Jezero podataka je već dodeljeno drugom prostoru. Istovremeni pristup više prostora jezeru podataka nije moguć.
#YMSE: Error while updating data lake option
virtualTablesExists=Ne možete poništiti dodelu jezera podataka za ovaj prostor jer još postoje zavisnosti u odnosu na virtuelne tabele*. Izbrišite virtuelne tabele da biste poništili dodelu jezera podataka za ovaj prostor.
#YMSE: Error while unlocking space
unlockSpaceFailureMessage=Prostor se ne može otključati.
#YMSE: Error while creating space
createSpaceError=Prostor se ne može kreirati.
#YMSE: Error while creating duplicate space
createDuplicateSpaceError=Prostor s nazivom {0} već postoji.
#YMSE: Error while deleting a single space
deleteSpaceError=Prostor se ne može izbrisati.
#YMSE: Error details while deleting a single space
deleteSpaceDetailedError=Vaš prostor “{0}” više ne funkcioniše ispravno. Pokušajte ponovo da ga izbrišete. Ako i dalje ne bude funkcionisao, zamolite administratora da izbriše vaš prostor ili kreirajte tiket.
#YMSE: Error while deleting a single space in files
deleteSpaceInFilesError=Podaci prostora u fajlovima se ne mogu izbrisati.
#YMSE: Error while deleting a single space users
deleteSpaceUsersError=Korisnici se ne mogu ukloniti.
#YMSE: Error while deleting a single space schema
deleteSpaceSchemasError=Šeme se ne mogu ukloniti.
#YMSE: Error while deleting a single space connections
deleteSpaceConnectionsError=Veze se ne mogu ukloniti.
#YMSE: Error while deleting a single space data
deleteSpaceDataError=Podaci prostora se ne mogu izbrisati.
#YMSE: Error while deleting multiple spaces
deleteSpacesError=Prostori se ne mogu izbrisati.
#YMSE: Error while restoring a single space
restoreSpaceError=Prostor se ne može obnoviti.
#YMSE: Error while restoring multiple spaces
restoreSpacesError=Prostori se ne mogu obnoviti.
#YMSE: Error while creating users
createUsersError=Korisnici se ne mogu dodati.
#YMSE: Error while removing users
removeUsersError=Ne možemo ukloniti korisnike.
#YMSE: Error while removing user
removeUserError=Ne možemo ukloniti korisnika.
#YMSE: Error assigning roles to a user
roleAssignUnAssignError=Ne možemo dodati korisnika odabranoj ulozi u opsegu. \n\n Ne možete dodati sebe ulozi u opsegu. Možete tražiti od administratora da vas doda ulozi u opsegu.
#YMSE: Error assigning user to the space
userAssignError=Nismo mogli da dodelimo korisnika prostoru. \n\n Korisnik je već dodeljen maksimalnom dozvoljenom broju (100) prostora u svim ulogama u opsegu.
#YMSE: Error assigning users to the space
usersAssignError=Nismo mogli da dodelimo korisnike prostoru. \n\n Korisnik je već dodeljen maksimalnom dozvoljenom broju (100) prostora u svim ulogama u opsegu.
#YMSE: Error while fetching users
fetchUserWithScopedRolesError=Ne možemo pozvati korisnike. Pokušajte ponovo kasnije.
#YMSE: Error while fetching scoped roles
fetchScopedRolesError=Ne možemo pozvati uloge u opsegu.
#YMSE: Error while fetching members
fetchUserError=Članovi se ne mogu pozvati. Pokušajte ponovo kasnije.
#YMSE: Error while loading run-time database
loadRuntimeError=Ne možemo učitati informacije iz baze podataka vremena izvođenja.
#YMSE: Error while loading spaces
loadSpacesError=Nažalost, došlo je do problema pri pokušaju pozivanja vaših prostora.
#YMSE: Error while loading haas resources
loadStorageError=Nažalost, došlo je do problema pri pokušaju pozivanja podataka skladišta.
#YMSE: Error no data could be loaded
loadDataError=Nažalost, došlo je do problema pri pokušaju pozivanja vaših podataka.
#XFLD: Click to refresh storage data
clickToRefresh=Kliknite ovde da pokušate ponovo.
#XTIT: Delete space popup title
deleteSpacePopupTitle=Izbriši prostor
#XCOL: Spaces table-view column name
name=Naziv
#XCOL: Spaces table-view deployment status
deploymentStatus=Status implementacije
#XFLD: Disk label in space details
storageLabel=Prostor na disku (GB)
#XFLD: In-Memory label in space details
ramLabel=Memorija (GB)
#XFLD: Memory label on space card
memory=Memorija za skladištenje
#XFLD: Storage Assignment label in space details
storrageAssignmentLabel=Skladište prostora
#XFLD: Storage Type label in space details
storageTypeLabel=Tip skladišta
#XFLD: Enable Space Quota
enableSpaceQuota=Aktiviraj kvotu prostora
#XFLD: No Space Quota
noSpaceQuota=Nema kvote prostora
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
diskAndInMemoryText=Baza podataka SAP HANA (na disku i u memoriji)
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
dataLakeFilesText=Fajlovi SAP HANA Data Lake
#XFLD: Available scoped roles label
availableRoles=Dostupne uloge u opsegu
#XFLD: Selected scoped roles label
selectedRoles=Odabrane uloge u opsegu
#XCOL: Spaces table-view column models
models=Modeli
#XCOL: Spaces table-view column users
users=Korisnici
#XCOL: Spaces table-view column connections
connections=Veze
#XFLD: Section header overview in space detail
overview=Pregled
#XFLD: Section header Apache Spark in space detail
sparkManagementSection=Apache Spark
#XFLD: Section header Apache Spark in space detail
sparkManagementSubSection=Aplikacije
#XFLD: Section header Apache Spark in space detail
taskAssignmentSubSection=Dodela zadatka
#XFLD: vCPU label in Apache Spark section
vCPULabel=vCPU
#XFLD: Memory label in Apache Spark section
memoryLabel=Memorija (GB)
#XFLD: space configuration label in Apache Spark section
spaceConfigurationLabel=Konfiguracija prostora
#XFLD: Space Source label
sparkApplicationLabel=Aplikacija
#XFLD: Cluster Size label
clusterSizeLabel=Veličina skupa
#XFLD: Driver label
driverLabel=Pokretač
#XFLD: Executor label
executorLabel=Izvršilac
#XFLD: max label
maxLabel=Maksimalno iskorišćeno
#XFLD: TrF Default label
trFDefaultLabel=Standardni tok transformacije
#XFLD: Merge Default label
mergeDefaultLabel=Standardno spajanje
#XFLD: Optimize Default label
optimizeDefaultLabel=Standardno optimiziranje
#XFLD: Deployment Default label
deploymentDefaultLabel=Implementacija lokalne tabele (fajl)
#XFLD: max spark source label
driverExecutorMaxUsedSparkSource={0} CPU / {1} GB
#XFLD: Object type label
taskObjectTypeLabel=Tip objekta
#XFLD: Task activity label
taskActivityLabel=Aktivnost
#XFLD: Task Application ID label
taskApplicationIDLabel=Stnadardna aplikacija
#XFLD: Section header in space detail
generalSettings=Opšta podešavanja
#XMSG: Message strip message when a space is locked
spaceLockedMessageStrip=Sistem je trenutno zaključao ovaj prostor.
#XMSG: Message strip message deploy warning
deployWarningMessageStrip=Promene u ovom odeljku će biti odmah implementirane.
#XMSG: Message strip message modify space configuration warning
modifySpaceConfigurationMessageStrip=Imajte u vidu da promena ovih vrednosti može prouzrokovati probleme izvođenja.
#XFLD: Button text to unlock the space again
unlockSpace=Otključaj prostor
#XFLD: Info text for audit log formatted message
auditLogText=Omogućite da protokoli revizije snimaju, čitaju ili menjaju radnje (politike revizije). Administratori zatim mogu analizirati ko je izvršio koju radnju u kom trenutku.
#XMSG: Message strip  audit warning
auditLogWarningMessageStrip=Protokoli revizije koriste veliku količinu memorije diska u vašem klijentu. Ako aktivirate politiku revizije (radnje čitanja ili promene), treba redovno da pratite korišćenje memorije diska (putem kartice Iskorišćena memorija diska u Monitoru sistema) kako biste izbegli potpune prekide rada diska koji mogu dovesti do prekida u pružanju usluga. Ako deaktivirate politiku revizije, svi unosi protokola revizije biće izbrisani. Ako želite da zadržite unose protokola revizije, razmotrite njihov izvoz pre nego što deaktivirate politiku revizije.
#XFLD: Message strip link text to open in app help to guide user what to do to unlock his space permanently
unlockSpaceHelp=Pokaži pomoć
#XMSG: Message strip message when a space will be locked again
spaceTemporarilyUnlockedMessageStrip=Ovaj prostor prekoračuje memoriju prostora i biće zaključan u {0} {1}.
#XMSG: Unit for remaining time until space is locked again
hours=sati
#XMSG: Unit for remaining time until space is locked again
minutes=minuti
#XFLD: Section header in space detail for auditing
detailsSectionAuditing=Revizija
#XFLD: Subsection header in space detail for auditing
auditing=Podešavanja revizije prostora
#XFLD: Hot space tooltip
hotSpaceCountTooltip=Kritični prostori: Iskorišćeni prostor za skladištenje je veći od 90%.
#XFLD: Green space tooltip
greenSpaceCountTooltip=Prostori u dobrom stanju: Iskorišćeni prostor za skladištenje je između 6% i 90%.
#XFLD: Cold space tooltip
coldSpaceCountTooltip=Neaktivni prostori: Iskorišćeni prostor za skladištenje je 5% ili manje.
#XFLD: Critical space tooltip
criticalSpaceCountTooltip=Kritični prostori: Iskorišćeni prostor za skladištenje je veći od 90%.
#XFLD: Green space tooltip
okSpaceCountTooltip=Prostori u dobrom stanju: Iskorišćeni prostor za skladištenje je između 6% i 90%.
#XFLD: Locked space tooltip
lockedSpaceCountTooltipNew=Zaključani prostori: Blokirano zbog nedovoljno memorije.
#XFLD: Locked space tooltip
lockedSpaceCountTooltip=Zaključani prostori
#YMSE: Error while deleting remote source
deleteRemoteError=Veze se ne mogu ukloniti.
#XFLD: Space ID input tooltip
spaceIDInfoTooltip=ID prostora se ne može promeniti kasnije.\nVažeći znakovi A - Z, 0 - 9 i _
#YMSE: Error when space name is empty
spaceNameEmptyValidationError=Unesite naziv prostora.
#YMSE: Error when ECN Business name is empty
ecnNameEmptyValidationError=Unesite poslovni naziv.
#YMSE: Error when space id is empty
spaceIDInputEmptyValidationError=Unesite ID prostora.
#YMSE: Error when space id contains invalid characters
spaceIDInputInValidError=Nevažeći znakovi. Koristite samo A - Z, 0 - 9 i _.
#YMSE: Error when space id exists while creation
spaceIDInputDuplicateError=ID prostora već postoji.
#XFLD: Space searchfield placeholder
search=Traži
#XMSG: Success message after creating users
createUsersSuccess=Korisnici dodati
#XMSG: Success message after creating user
createUserSuccess=Korisnik dodat
#XMSG: Success message after updating users
updateUsersSuccess={0} korisnika ažurirano
#XMSG: Success message after updating user
updateUserSuccess=Korisnik ažuriran
#XMSG: Success message after removing users
removeUsersSuccess={0} korisnika uklonjeno
#XMSG: Success message after removing user
removeUserSuccess=Korisnik uklonjen
#XFLD: Schema name
schemaName=Naziv šeme
#XFLD: used of total
ofTemplate={0} od {1}
#XFLD: Assigned disk label in space overview
assignedStorageTemplate=Dodeljeni prostor na disku ({0} od {1})
#XFLD: Assigned Memory label in space overview
assignedRAMTemplate=Dodeljena memorija ({0} od {1})
#XFLD: Storage ratio on space
accelearationRAM=Ubrzanje memorije
#XFLD: No Storage Consumption
noStorageConsumptionText=Kvota skladišta nije dodeljena.
#XFLD: Used disk label in space overview
usedStorageTemplate=Disk korišćen za skladištenje ({0} od {1})
#XFLD: Used Memory label in space overview
usedRAMTemplate=Memorija korišćena za skladištenje ({0} od {1})
#XFLD: Used Disk tooltip
usedStorageTooltipTemplate={0} od {1} diska iskorišćeno za skladištenje
#XFLD: Used Memory tooltip
usedRAMTooltipTemplate=Iskorišćeno {0} od {1} memorije
#XFLD: Assigned Disk tooltip
assignedStorageTooltipTemplate=Dodeljeno {0} od {1} prostora na disku
#XFLD: Assigned Memory tooltip
assignedRAMTooltipTemplate=Dodeljeno {0} od {1} memorije
#XFLD: Space related used storage tooltip
spaceRelatedUsedStorageTooltipTemplate=Podaci prostora: {0}
#XFLD: Not space related used storage tooltip
otherDataUsedStorageTooltipTemplate=Drugi podaci: {0}
#XFLD: Storage used exceeded popover and tooltip
storageUsedExceededText=Razmotrite proširivanje vašeg plana ili kontaktirajte SAP Support.
#XCOL: Space table-view column used Disk
usedStorage=Disk korišćen za skladištenje
#XCOL: Space monitor column used Memory
usedRAM=Memorija korišćena za skladištenje
#XCOL: Space monitor column Schema
tableSchema=Šema
#XCOL: Space monitor column Storage Type
tableStorageType=Tip skladišta
#XCOL: Space monitor column Table Type
tableType=Tip tabele
#XCOL: Space monitor column Record Count
tableRecordCount=Broj zapisa
#XFLD: Assigned Disk
assignedStorage=Disk dodeljen za skladištenje
#XFLD: Assigned Memory
assignedRAM=Memorija dodeljena za skladištenje
#XCOL: Space table-view column storage utilization
tableStorageUtilization=Iskorišćeni prostor
#XFLD: space status
spaceStatus=Status prostora
#XFLD: space type
spaceType=Tip prostora
#XFLD: space type label for space with space type "SAP BW bridge" ("SAP BW bridge" is a product name, do not translate it)
abapbridgeSpaceType=Premošćavanje aplikacije SAP BW
#XFLD: space type label for space with space type "Data Marketplace"
marketplaceSpaceType=Proizvod davaoca podataka
#XMSG: Error message for when selected space cannot be deleted due to the space type
deleteTypedSpaceError=Ne možete izbrisati prostor {0} jer je njegov tip prostora {1}.
#XMSG: Error message for when selected spaces cannot be deleted due to the space type
deleteTypedSpacesError=Ne možete izbrisati {0} odabranih prostora. Prostori sa sledećim tipovima prostora se ne mogu izbrisati: {1}.
#XFLD: space type label for space without space type
defaultSpaceType=SAP Datasphere
#XFLD: Tooltip for monitor space button
monitorSpace=Nadgledaj
#XFLD: Tooltip for edit space button
editSpace=Uredi prostor
#XMSG: Deletion warning in messagebox
deleteConfirmation=Da li zaista želite da izbrišete ovaj prostor?
#XFLD: Tooltip for delete space button
deleteSpace=Izbriši prostor
#XFLD: storage
storage=Disk za skladištenje 
#XFLD: username
userName=Korisničko ime
#XFLD: port
port=Port
#XFLD: hostname
hostName=Naziv glavnog kompjutera
#XFLD: password
password=Lozinka
#XBUT: Request new password button
requestPassword=Zahtevaj novu lozinku
#YEXP: Usage explanation in time data section
timeDataSectionHint=Kreirajte rasporede i dimenzije za upotrebu u modelima i pričama.
#YEXP: Usage explanation in Space schema section
spaceSchemaHint=Da li želite da podaci u vašem prostoru budu dostupni za upotrebu u drugim alatima ili aplikacijama? Ako želite, kreirajte jednog ili više korisnika koji mogu pristupiti podacima u vašem prostoru i odaberite da li želite da svi budući podaci prostora standardno budu dostupni za upotrebu.
#XTIT: Create schema popup title
createSchemaDialogTitle=Kreiraj Open SQL šemu
#XTIT: Generate Time Data dialog title
generateTimeDataDialogTitle=Kreiraj rasporede i dimenzije
#XTIT: Edit Time Data dialog title
editTimeDataDialogTitle=Uredi rasporede i dimenzije
#XTIT: Time Data token title
timeDataTokenTitle=Podaci o vremenu
#XTIT: Time Data token title
timeDataUpdateViews=Ažuriraj poglede podataka o vremenu
#XFLD: Time Data token creation in progress label
timeDataCreationInProgress=Kreiranje u toku...
#XFLD: Time Data token creation error label
timeDataCreationError=Kreiranje nije uspelo. Pokušajte ponovo.
#XTIT: Time Data dialog time settings title
timeDataDialogTimeSettingsTitle=Podešavanja rasporeda
#XTIT: Time Data dialog time data table title
timeDataDialogTranslationTablesTitle=Tabele preračunavanja
#XTIT: Time Data dialog dimensions title
timeDataDialogTimeViewsTitle=Dimenzije vremena
#XFLD: Time Data dialog time range label
timeRangeHint=Definišite vremenski raspon.
#XFLD: Time Data dialog time data table label
timeDataHint=Dajte naziv vašoj tabeli.
#XFLD: Time Data dialog time data dimensions label
timeDimensionsHint=Dajte naziv vašim dimenzijama.
#XFLD: Time Data Time range description label
timerangeLabel=Vremenski raspon
#XFLD: Time Data dialog from year label
fromYearLabel=Godina početka
#XFLD: Time Data dialog to year label
toYearLabel=Godina završetka
#XFLD: Time Data dialog calendar type label
calendarTypeLabel=Tip kalendara
#XFLD: Time Data dialog granularity label
granularityLabel=Granulacija
#XFLD: Time Data dialog technical name label
technicalNameLabel=Tehnički naziv
#XFLD: Time Data dialog quarters text table lable
quarterTextsTableLabel=Tabela preračunavanja za kvartale
#XFLD: Time Data dialog months text table lable
monthsTextsTableLabel=Tabela preračunavanja za mesece
#XFLD: Time Data dialog days text table lable
daysTextsTableLabel=Tabela preračunavanja za dane
#XFLD: Time Data dialog year label
yearLabel=Dimenzija godine
#XFLD: Time Data dialog quarter label
quarterLabel=Dimenzija kvartala
#XFLD: Time Data dialog month label
monthLabel=Dimenzija meseca
#XFLD: Time Data dialog day label
dayLabel=Dimenzija dana
#XFLD: Time Data dialog gregorian calendar type label
gregorian=Gregorijanski
#XFLD: Time Data dialog time granularity day label
day=Dan
#XFLD: Time Data dialog business name maxlength reached
timeRangeValueStateTextMaxLengthBusinessName=Maksimalna dužina od 1000 znakova dostignuta.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextMaxRangeExceeded=Maksimalni vremenski raspon je 150 godina.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeLower=“Godina početka” mora da bude pre “Godine završetka”
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeHigher="Godina početka" mora da bude 1900. ili kasnija.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeHigher=“Godina završetka” mora da bude posle “Godine početka”
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeLower=“Godina završetka” mora da bude pre tekuće godine plus 100
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningFromYear=Kasnija "Godina početka" može dovesti do gubitka podataka
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningToYear=Ranija "Godina početka" može dovesti do gubitka podataka
#XMSG: Time Data creation validation error message
timeDataValidationError=Izgleda da su neka polja nevažeća. Proverite obavezna polja da biste nastavili.
#XMSG: Time Data deletion popup text
deleteTimeDataConfirmation=Da li zaista želite da izbrišete podatke?
#XMSG: Time Data creation success message
createTimeDataSuccess=Podaci o vremenu kreirani
#XMSG: Time Data update success message
updateTimeDataSuccess=Podaci o vremenu ažurirani
#XMSG: Time Data delete success message
deleteTimeDataSuccess=Podaci o vremenu izbrisani
#XMSG: Time Data creation error message
createTimeDataError=Došlo je do greške pri pokušaju kreiranja podataka o vremenu.
#XMSG: Time Data update error message
updateTimeDataError=Došlo je do greške pri pokušaju ažuriranja podataka o vremenu.
#XMSG: Time Data creation error message
deleteTimeDataError=Došlo je do greške pri pokušaju brisanja podataka o vremenu.
#XMSG: Error message when time data could not be retrieved
retrievingTimeDataError=Podaci o vremenu se ne mogu učitati.
#XTIT: Time Data deletion popup title
deleteTimeDataConfirmationTitle=Upozorenje
#XTXT: Time Data deletion dependency popup text
deleteTimeDataDependencyText=Ne možemo izbrisati vaše podatke o vremenu jer se koriste u drugim modelima.
#XTXT: Time Data deletion dependency popup text singular
deleteTimeDataDependencyTextSingular=Ne možemo izbrisati vaše podatke o vremenu jer se koriste u drugom modelu.
#XMSG: Validation error when required time data form field is empty
timeDataRequiredField=Ovo polje je obavezno i ne može se ostaviti prazno.
#XFLD: Tooltip for Open in Database Explorer button
openInDBX=Otvorite u pretraživaču baze podataka
#YMSE: Dimension Year
dimensionYearView=Dimenzija "Godina"
#YMSE: Dimension Year
dimensionQuarterView=Dimenzija "Kvartal"
#YMSE: Dimension Year
dimensionMonthView=Dimenzija "Mesec"
#YMSE: Dimension Year
dimensionDayView=Dimenzija "Dan"
#XFLD: Time Data deletion object title
timeDataUsedIn=(koristi se u {0} modela)
#XFLD: Time Data deletion object title singular
timeDataUsedInSingular=(koristi se u 1 modelu)
#XFLD: Time Data deletion table column provider
provider=Davalac
#XTIT: Time Data dependency dialog title
timeDataDependencyDialogTitle=Zavisnosti
#XTIT: Create schema popup title
createSpaceSchemaUserDialogTitle=Kreiraj korisnika za šemu prostora
#XFLD: Create schema button
createSchemaButton=Kreiraj Open SQL šemu
#XFLD: Generate TimeData button
generateTimeDataButton=Kreiraj rasporede i dimenzije
#XFLD: Show dependencies button
showDependenciesButton=Pokaži zavisnosti
#XFLD: tooltip for disabled buttons when user is not member of space
mustBeMemberOfSpaceForOperation=Da bi se izvršila ova operacija, vaš korisnik mora biti član prostora.
#XFLD: Create space schema user button
createSpaceSchemaUserButton=Kreiraj korisnik šeme prostora
#YMSE: API Schema users load error
loadSchemaUsersError=Lista korisnika se ne može učitati.
#XTIT: API Schema-config popup title
schemaUserDetailsDialogTitle=Detalji korisnika šeme prostora
#XMSG: API Schema user deletion popup text
deleteSpaceSchemaUserConfirmation=Da li zaista želite da izbrišete odabrani korisnik?
#XMSG: API Schema user deletion confirmation text
userDeleteSuccess=Korisnik izbrisan.
#YMSE: API Schema user deletion error
userDeleteError=Korisnik se ne može izbrisati.
#XFLD: User deleted
userDeleted=Korisnik je izbrisan.
#XTIT: Remove user popup title
removeUserConfirmationTitle=Upozorenje
#XMSG: Remove user popup text
removeUserConfirmation=Da li zaista želite da uklonite korisnik? Korisnik i njegove dodeljene uloge u opsegu će biti uklonjeni iz prostora.
#XMSG: Remove users popup text
removeUsersConfirmation=Da li zaista želite da uklonite korisnike? Korisnici i njihove dodeljene uloge u opsegu će biti uklonjeni iz prostora.
#XBUT: Confirmation button for remove user dialog
removeUserConfirmationBtn=Ukloni
#YMSE: No data text for available roles
noDataAvailableRoles=Prostor nije dodat nijednoj ulozi u opsegu. \n Da biste mogli da dodajete korisnike u prostor, on prvo mora biti dodat najmanje jednoj ulozi u opsegu.
#YMSE: No data text for selected roles
noDataSelectedRoles=Nisu odabrane uloge u opsegu
#XTIT: Schema-config popup title
schemaDetailsDialogTitle=Detalji konfiguracije Open SQL šeme
#XFLD: Label for Read Audit Log
auditLogRead=Aktiviraj protokol revizije za operacije čitanja
#XFLD: Label for Change Audit Log
auditLogChange=Aktiviraj protokol revizije za operacije promene
#XFLD: Label Audit Log Retention
auditLogRetention=Zadrži protokole za
#XFLD: Label Audit Log Retention Unit
retentionUnit=dana
#YMSE: Error when log retention time is set incorrectly
incorrectRetentionTime=Unesite ceo broj između {0} i {1}
#XFLD: Label for Cross Space Schema Access
spaceSchemaAccess=Koristi podatke šeme prostora
#XTIT: Confirm deactivation of space schema consumption dialog title
disableSpaceSchemaConsumptionDialogTitle=Zaustavi korišćenje podataka šeme prostora
#XTXT: Confirm deactivation of space schema consumption dialog text
disableSpaceSchemaConsumptionDialogText=Ova Open SQL šema možda koristi podatke vaše šeme prostora. Ako zaustavite korišćenje, modeli zasnovani na podacima šeme prostora možda više neće funkcionisati.
#XBUT: Confirmation button for the stop consuming space schema data dialog
stopConsuming=Zaustavi korišćenje
#XTXT: Tooltip for data lake icon
dataLakeIconTooltip=Ovaj prostor se koristi za pristup jezeru podataka
#XFLD: Tooltip for data lake table entry
dataLakeTableEntryTooltip=Jezero podataka aktivirano
#XFLD: used In-Memory linit reached tag
usedRamLimitReachedLabel=Ograničenje memorije dostignuto
#XFLD: Space storage Limit reached tag
storageLimitReachedLabel=Ograničenje prostora za skladištenje dostignuto
#XFLD: Minimal Storage Limit Reached tag
minStorageLimitReachedLabel=Minimalno ograničenje prostora za skladištenje dostignuto
#XFLD: Space ram tag
ramLimitReachedLabel=Ograničenje memorije dostignuto
#XFLD: Minimal In-Memory Limit Reached tag
minRamLimitReachedLabel=Minimalno ograničenje memorije dostignuto
#XFLD: Popover text after click on space storage limit warning
storageLimitReachedText=Dostigli ste ograničenje dodeljenog prostora za skladištenje od {0}. Dodelite još prostora za skladištenje prostoru.
#XFLD: System storage tag
systemStorageLimitReachedLabel=Dostignuto ograničenje prostora za skladištenje sistema
#XMSG: Popover text after click on storage limit warning
systemStorageLimitReachedText=Dostigli ste ograničenje prostora za skladištenje sistema od {0}. Trenutno prostoru ne možete dodeliti još prostora za skladištenje.
#XMSG: Schema deletion warning
deleteSchemaConfirmation=Brisanje ove Open SQL šeme takođe će trajno izbrisati sve skladištene objekte i održavane asocijacije u šemi. Nastaviti?
#XMSG: Success message after deleting a schema
schemaDeleteSuccess=Šema izbrisana
#YMSE: Error while deleting schema.
schemaDeleteError=Šema se ne može izbrisati.
#XMSG: Success message after update a schema
schemaUpdateSuccess=Šema ažurirana
#YMSE: Error while updating schema.
schemaUpdateError=Šema se ne može ažurirati.
#XMSG: Hint when no password is shown in the dialog.
newPasswordHint=Obezbedili smo lozinku za ovu šemu. Ako ste zaboravili svoju lozinku ili ste je izgubili, možete zatražiti novu. Ne zaboravite da kopirate ili sačuvate novu lozinku.
#XMSG: Hint when showing the password in the dialog.
savePasswordHint=Kopirajte svoju lozinku. Biće vam potrebna da uspostavite vezu sa ovom šemom. Ako ste zaboravili lozinku, možete otvoriti ovaj dijalog da biste je ponovo postavili.
#XFLD,50: Hint on entering schema name in creation dialog
schemaNameHint=Ovaj naziv se ne može prom.nakon kreiranja šeme.
#XFLD: Open SQL Schemas section sub headline
schemasSQL=Open SQL
#XFLD: Space schema section sub headline
schemasSpace=Prostor
#XFLD: HDI Container section header
HDIContainers=Spremnici HDI
#XTXT: Add HDI Containers button tooltip
addHDIContainers=Dodaj spremnike HDI
#XTXT: Remove HDI Containers button tooltip
removeHDIContainers=Ukloni spremnike HDI
#XFLD: Enable HDI Containers feature button
enableHDIContainersButton=Omogući pristup
#YMSE: No data text for HDI Containers table
noDataHDIContainers=Spremnici HDI nisu dodati.
#YMSE: No data text for Timedata section
noDataTimedata=Tabele vremena i dimenzije nisu kreirane.
#YMSE: No data text during runtime not available for Timedata section
runtimeNotAvailableNoDataTimedata=Nije moguće učitati tabele vremena i dimenzije jer baza podataka vremena izvođenja nije dostupna.
#YMSE: No data text during runtime not available for HDI section
runtimeNotAvailableNoDataHDI=Nije moguće učitati spremnike HDI jer baza podataka vremena izvođenja nije dostupna.
#YMSE: Error while fetching HDI Containers
fetchHDIContainersError=Spremnici HDI nisu obezbeđeni. Pokušajte ponovo kasnije.
#XFLD Table column header for HDI Container names
HDIContainerName=Naziv spremnika HDI
#XFLD: Enable HDI Containers feature dialog title
enableHDIContainersDialogTitle=Omogući pristup
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText1New=Možete aktivirati SAP SQL Data Warehousing u vašem klijentu rešenja SAP Datasphere za razmenu podataka između vaših spremnika HDI i vaših prostora rešenja SAP Datasphere bez potrebe za pomeranjem podataka.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText2=Da biste to uradili, kreirajte tiket tako što ćete kliknuti na dugme u nastavku.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText3New=Kada se obradi tiket, morate kreirati jedan novi spremnik HDI ili više njih u bazi podataka vremena izvođenja rešenja SAP Datasphere. Zatim dugme + menja dugme Omogući pristup u odeljku Spremnici HDI za sve vaše prostore rešenja SAP Datasphere i možete dodati svoje spremnike prostoru.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText41=Potrebno vam je više informacija? Idite na %%0. Za detaljne informacije o tome šta da uključite u prijavu problema, pogledajte %%1.
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkText=SAP Help
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkTextSapNote3057059=SAP beleška 3057059
#XBUT: Open Ticket Button Text
openTicket=Otvori tiket
#XBUT: Add Button Text
add=Dodaj
#XBUT: Next Button Text
next=Sledeće
#XBUT: Edit Button Text
editUsers=Uredi
#XBUT: create user Button Text
createUser=Kreiraj
#XBUT: Update user Button Text
updateUser=Odaberi
#XTIT: Add HDI Containers Dialog Title
addHDIContainersDialogTitle=Dodaj nedodeljene spremnike HDI
#XFLD: No data text for add HDI container dialog
addHDIContainersDialogNoDataText=Nismo našli nedodeljene spremnike. \n Spremnik koji tražite možda je već dodeljen prostoru.
#YMSE: Error while loading assigned HDI Containers
loadAssignedHDIContainersError=Dodeljeni spremnici HDI se ne mogu učitati.
#YMSE: Error while loading assigned HDI Containers
loadUnassignedHDIContainersError=Spremnici HDI se ne mogu učitati.
#XMSG: Success message
succeededToAddHDIContainer=Spremnik HDI dodat
#XMSG: Success message
succeededToAddHDIContainerPlural=Spremnici HDI dodati
#XMSG: Success message
succeededToDeleteHDIContainer=Spremnik HDI uklonjen
#XMSG: Success message
succeededToDeleteHDIContainerPlural=Spremnici HDI uklonjeni
#XFLD: Time data section sub headline
timeDataSection=Rasporedi i dimenzije
#XFLD: Schemas list entry read audit log enabled indicator
readIndicator=Čitaj
#XFLD: Schemas list entry change audit log enabled indicator
changeIndicator=Promeni
#XFLD: Remote sources section sub headline
allconnections=Dodela veze
#XFLD: Remote sources section sub headline
localconnections=Lokalne veze
#XFLD: Remote sources section sub headline for open Connectors
openConnectors=SAP Open Connectors
#XFLD: User section sub headline
memberassignment=Dodela člana
#XFLD: User assignment section sub headline
userAssignment=Dodela korisnika
#XFLD: User section Access dropdown Member
member=Član
#XFLD: User assignment section column name
user=Korisničko ime
#XTXT: Selected role count
selectedRoleToolbarText=Odabrano: {0}
#XTIT: Space detail section connections title
detailsSectionConnections=Veze
#XTIT: Space detail section data access title
detailsSectionDataAccess=Pristup šemi
#XTIT: Space detail section time data title
detailsSectionGenerateData=Podaci o vremenu
#XTIT: Space detail section members title
detailsSectionUsers=Članovi
#XTIT: Space detail section Users title
detailsSectionUsersTitle=Korisnici
#XTIT: Out of Storage
insufficientStoragePopoverTitle=Nema prostora za skladištenje
#XTIT: Storage distribution
storageDistributionPopoverTitle=Iskorišćena memorija diska
#XTXT: Out of Storage popover text
insufficientStorageText=Da biste kreirali novi prostor, smanjite dodeljeni prostor za skladištenje drugog prostora ili izbrišite prostor koji vam više nije potreban. Možete povećati svoj ukupni prostor za skladištenje sistema pozivanjem funkcije Upravljaj planom.
#XMSG: Space id length warning
spaceIdLengthWarning=Maksimum od {0} znakova prekoračen.
#XMSG: Space name length warning
spaceNameLengthWarning=Maksimum od {0} znakova prekoračen.
#XMSG: Space id reserved prefix warning
spaceIdnWarningReservedPrefix=Nemojte koristiti prefiks {0} da biste izbegli moguće konflikte.
#YMSE: Error while loading open SQL schemas
loadLocalSchemasError=Open SQL šeme se ne mogu učitati.
#YMSE: Error while creating open SQL schema
createLocalSchemaError=Open SQL šema se ne može kreirati.
#YMSE: Error while loading all remote connections
loadRemoteConnectionsError=Nije moguće učitati sve udaljene veze.
#YMSE: Error while loading space details
loadSpaceDetailsError=Detalji prostora se ne mogu učitati.
#YMSE: Error while deploying space details
deploySpaceDetailsError=Prostor se ne može implementirati.
#YMSE: Error while copying space details
copySpaceDetailsError=Prostor se ne može kopirati.
#YMSE: Error while loading storage data
loadStorageDataError=Podaci skladišta se ne mogu učitati.
#YMSE: Error while loading all users
loadAllUsersError=Nije moguće učitati sve korisnike.
#YMSE: Failed to reset password
resetPasswordError=Lozinka se ne može ponovo postaviti.
#XMSG: Success message after updating space
resetPasswordSuccessMessage=Nova lozinka postavljena za šemu
#YMSE: DP Agent-name too long
DBAgentNameError=Naziv agenta distribucije podataka je predug.
#YMSE: Schema-name not valid.
schemaNameError=Naziv šeme je nevažeći.
#YMSE: User name not valid.
UserNameError=Korisničko ime je nevažeće.
#XTIT: Consumption by Storage Type
consumptionStorageTypeText=Korišćenje po tipu skladišta
#XTIT: Consumption by Schema
consumptionSchemaText=Korišćenje po šemi
#XTIT: Overall Consumption by schema
overallConsumptionSchemaText=Ukupno korišćenje tabele po šemi
#XTIT: Overall Consumption by Table Type
overallConsumptionTableTypeText=Ukupno korišćenje po tipu tabele
#XTIT: Tables
tableDetailsText=Detalji tabele
#XTIT: Table Storage Consumption
tableStorageConsumptionText=Korišćenje skladišta tabele
#XFLD: Table Type label
tableTypeLabel=Tip tabele
#XFLD: Schema label
schemaLabel=Šema
#XFLD: reset table tooltip
resetTable=Ponovo postavi tabelu
#XFLD: In-Memory label in space monitor
inMemoryLabel=Memorija
#XFLD: Disk label in space monitor
diskLabel=Disk
#XFLD: Yes
yesLabel=Da
#XFLD: No
noLabel=Ne
#XFLD: switch label: Want the data in this space to be consumable by default?
allowConsumptionByDefault=Da li želite da se podaci u ovom prostoru standardno koriste?
#XFLD: Business Name
businessNameLabel=Poslovni naziv
#XFLD: Refresh
refresh=Osveži
#XMSG: No filter results title
noFilterResultsTitle=Izgleda da vaša podešavanja filtera ne prikazuju podatke.
#XMSG: No filter results message
noFilterResultsMsg=Pokušajte da detaljno odredite podešavanja filtera, a ako i dalje ne budete videli podatke, kreirajte tabele u generatoru podataka. Nakon što počnu da koriste skladište, ovde ćete moći da ih nadgledate.
#XMSG: Space monitoring Hana state red title
spaceMonitorHanaRedTitle=Baza podataka vremena izvođenja nije dostupna.
#XMSG: Space monitoring Hana state red message
spaceMonitorHanaRedMsg=Pošto baza podataka vremena izvođenja nije dostupna, određene funkcije su deaktivirane i ne možemo da prikažemo informacije na ovoj stranici.
#YMSE: Error while creating space schema user
createSpaceSchemaUserError=Korisnik šeme prostora se ne može kreirati.
#YMSE: Error User name already exists
userAlreadyExistsError=Korisničko ime već postoji.
#YMSE: Error Authentication failed
authenticationFailedError=Potvrda identiteta nije uspela.
#XMSG: Message strip message when a schema user is locked
userLockedMessageStrip=Korisnik je zaključan zbog previše neuspešnih prijava. Zatražite novu lozinku da biste otključali korisnika.
#XMSG: Success message after reset and unlock schema user
resetUnlockPasswordSuccessMessage=Nova lozinka postavljena i korisnik otključan
#XMSG: user is locked message
userLockedMessage=Korisnik je zaključan.
#XCOL: Users table-view column Role
spaceRole=Uloga
#XCOL: Users table-view column Scoped Role
spaceScopedRole=Uloga u opsegu
#XCOL: Users table-view column Space Admin
spaceAdmin=Administrator prostora
#XFLD: User section dropdown value Viewer
viewer=Pregledač
#XFLD: User section dropdown value Modeler
modeler=Modelator
#XFLD: User section dropdown value Data Integrator
dataIntegrator=Integrator podataka
#XFLD: User section dropdown value Space Administrator
spaceAdministrator=Administrator prostora
#XMSG: Success message -  Space Role updated successfully.
updateUserSpaceRoleSuccess=Uloga prostora ažurirana
#XMSG: Error message - Failed to update Space Role.
updateUserSpaceRoleError=Uloga prostora nije uspešno ažurirana.
#XFLD:
databaseUserNameSuffix=Sufiks korisničkog imena baze podataka
#XTXT: Space Schema password text
spaceSchemaPasswordText=Da biste uspostavili vezu sa ovom šemom, kopirajte svoju lozinku. Ako ste zaboravili svoju lozinku, uvek možete zatražiti novu.
#XFLD: Cloud Platform label in space schema dialog
cloudPlatformLabel=Cloud Platform
#XTXT: Cloud Platform text in space schema dialog
cloudPlatformText=Da biste konfigurisali pristup preko ovog korisničkog naloga, aktivirajte korišćenje i kopirajte podatke pristupa. Ako samo možete da kopirate podatke pristupa bez lozinke, lozinku obavezno dodajte naknadno.
#XFLD: enable consumption in CloudPlatform label in space schema dialog
enableConsumptionInCloudPlatformLabel=Aktiviraj korišćenje na platformi Cloud Platform
#XFLD: Credentials for User-Provided Service label in space schema dialog
cloudPlatformCredentialsLabel=Podaci pristupa za uslugu koju obezbeđuje korisnik:
#XFLD: Credentials for User-Provided Service (Without Password) label in space schema dialog
cloudPlatformCredentialsWithoutPasswordLabel=Podaci pristupa za uslugu koju obezbeđuje korisnik (bez lozinke):
#XFLD: Copy Credentials label in space schema dialog
copyCredentialsLabel=Kopiraj podatke pristupa bez lozinke
#XFLD: Copy Credentials label in space schema dialog
copyFullCredentialsLabel=Kopiraj potpune podatke pristupa
#XFLD: Copy Password label in space schema dialog
copyPasswordLabel=Kopiraj lozinku
#XMSG: Credentials copied to clipboard
credentialsCopiedMessage=Podaci pristupa kopirani u prelaznu memoriju
#XMSG: Password copied to clipboard
passwordCopiedMessage=Lozinka kopirana u prelaznu memoriju
#XMSG: create database user dialog title
createDatabaseUserDialogTitle=Kreiraj korisnik baze podataka
#XMSG: Database Users section title
databaseUsers=Korisnici baze podataka
#XMSG: Database User Details dialog title
databaseUserDetailsDialogTitle=Detalji korisnika baze podataka
#XFLD: database user read audit log
databaseUserAuditLogRead=Aktiviraj protokole revizije za operacije čitanja i zadrži protokole za
#XFLD: database user change audit log
databaseUserAuditLogChange=Aktiviraj protokole revizije za operacije promene i zadrži protokole za
#XMSG: Cloud Platform Access
cloudPlatformAccess=Pristup u platformu Cloud Platform
#XFLD: Enable Consumption in Cloud Platform hint
enableConsumptionCloudPlatformHint=Konfigurišite pristup spremniku HANA Deployment Infrastructure (HDI) preko ovog korisnika baze podataka. Da biste se povezali sa spremnikom HDI, SQL modeliranje mora biti uključeno
#XFLD: Enable HDI Consumption
enableConsumptionCloudPlatform=Aktiviraj upotrebu HDI
#XFLD: Enable Consumption hint
enableConsumptionHint=Da li želite da podatke u vašem prostoru mogu da koriste drugi alati i aplikacije?
#XFLD: Enable Consumption
enableConsumption=Aktiviraj korišćenje SQL
#XFLD: Enable Modeling
enableModeling=Aktiviraj modeliranje SQL
#XMSG: Privileges for Data Modeling
privilegesModeling=Uvoz velike količine podataka
#XMSG: Privileges for Data Consumption
privilegesConsumption=Korišćenje podataka za eksterne alate
#XFLD: SQL Modeling
sqlModeling=Modeliranje SQL
#XFLD: SQL Consumption
sqlConsumption=Korišćenje SQL
#XFLD: enabled
enabled=Aktivirano
#XFLD: disabled
disabled=Deaktivirano
#XFLD: Edit Privileges
editPrivileges=Ovlašćenja za uređivanje
#XFLD: Open Database Explorer
openDBX=Otvori pretraživač baze podataka
#XFLD: create database user hint
databaseCreateHint=Imajte u vidu da nakon snimanja neće biti moguće ponovo promeniti korisničko ime.
#XFLD: Internal Schema Name
internalSchemaName=Naziv interne šeme
#YMSE: Failed to load database users
loadDatabaseUserError=Nije uspelo učitavanje korisnika baze podataka
#YMSE: Failed to delete database user
deleteDatabaseUsersError=Nije uspelo brisanje korisnika baze podataka
#XMSG: Success message after deleting database user
deleteDatabaseUserSuccess=Korisnik baze podataka izbrisan
#XMSG: Success message after deleting database users
deleteDatabaseUsersSuccess=Korisnici baze podataka izbrisani
#XMSG: Success message after creating database user
createDatabaseUserSuccessMessage=Korisnik baze podataka kreiran
#YMSE: Failed to create database user
createDatabaseUserError=Nije uspelo kreiranje korisnika baze podataka
#XMSG: Success message after updating database user
updateDatabaseUserSuccessMessage=Korisnik baze podataka ažuriran
#YMSE: Failed to update database user
updateDatabaseUserError=Nije uspelo ažuriranje korisnika baze podataka
#XFLD: HDI Consumption
hdiConsumption=Korišćenje HDI
#XTIT: Space detail section Database Access title
detailsSectionDatabaseAccess=Pristup bazi podataka
#XTXT: Database users consumption hint
databaseUsersConsumptionHint=Postavite svoje podatke prostora da se mogu standardno koristiti. Modeli u generatorima će automatski omogućiti korišćenje podataka.
#XFLD: Default Consumption of Space Data:
defaultConsumptionOfSpaceData=Standardno korišćenje podataka prostora:
#XFLD: Database User Name
databaseUserName=Ime korisnika baze podataka
#XMSG: Database User creation validation error message
databaseUserValidationError=Izgleda da su neka polja nevažeća. Proverite obavezna polja da biste nastavili.
#XMSG: SQL modeling can’t be enabled since this user has been migrated.
migratedDatabaseUserHint=Uvoz velike količine podataka se ne može aktivirati jer je ovaj korisnik migriran.
#XBUT: Remove Button Text
remove=Ukloni
#XBUT: Remove Spaces Button Text
removeSpaces=Ukloni prostore
#XBUT: Remove Objects Button Text
removeObjects=Ukloni objekte
#XMSG: No members have been added yet.
noMembersAssigned=Članovi još nisu dodati.
#XMSG: No users have been added yet.
noUsersAssigned=Korisnici još nisu dodati.
#XMSG: No database users have been created or your filter is not showing any data.
noDatabaseUsers=Korisnici baze podataka nisu kreirani ili vaš filter ne prikazuje podatke.
#XMSG: Please enter a user name.
noDatabaseUsername=Unesite korisničko ime.
#XMSG: The user name is too long. Please use a shorter one.
databaseUsernameTooLong=Korisničko ime je predugo. Koristite kraće korisničko ime.
#XMSG: No privileges have been enabled and this database user will have limited functionality. Do you still want to continue?
noPrivilegesConfirmationMessage=Ovlašćenja nisu aktivirana i ovaj korisnik baze podataka će imati ograničenu funkcionalnost. Da li ipak želite da nastavite?
#XMSG: To enable audit logs for change operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogChangeMessage=Da biste aktivirali protokole revizije za operacije promene, potrebno je da aktivirate i uvoz velike količine podataka. Da li želite da izvršite ovo?
#XMSG: To enable audit logs for read operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogReadMessage=Da biste aktivirali protokole revizije za operacije čitanja, potrebno je da aktivirate i uvoz velike količine podataka. Da li želite da izvršite ovo?
#XMSG: To enable HDI consumption data ingestion and data consumption needs to be enabled. Do you want to enable all?
enableHDIConsumptionMessage=Da biste aktivirali korišćenje HDI, potrebno je da aktivirate i uvoz velike količine podataka i korišćenje podataka. Da li želite da izvršite ovo?
#XMSG:
databaseUserPasswordText=Da biste uspostavili vezu sa ovim korisnikom baze podataka, kopirajte svoju lozinku. Ako ste zaboravili svoju lozinku, uvek možete zatražiti novu.
#XTIT: Space detail section members title
detailsSectionMembers=Članovi
#XMSG: New password set
newPasswordSet=Nova lozinka postavljena
#XFLD: Data Ingestion
dataIngestion=Uvoz velike količine podataka
#XFLD: Data Consumption
dataConsumption=Korišćenje podataka
#XFLD: Privileges
privileges=Ovlašćenja
#XFLD: Enable Data ingestion
enableDataIngestion=Aktiviraj uvoz velike količine podataka
#XMSG: Log the read and change operations for data ingestion.
databaseUserAuditHint=Protokolirajte operacije čitanja i promene za uvoz velike količine podataka.
#XMSG: Make your space data available in your HDI containers.
databaseUserHDIHint=Učinite podatke prostora dostupnim u vašim spremnicima HDI.
#XFLD: Enable Data consumption
enableDataConsumption=Aktiviraj korišćenje podataka
#XMSG: Allow other apps or tools to consume your space data.
enableDataConsumptionHint=Dozvolite drugim aplikacijama ili alatima da koriste vaše podatke prostora.
#XMSG: To set up access via this schema user copy the credentials to your user-provided service. In case you can only copy the credentials without a password make sure to add the password later.
databaseUserHDIConsumptionHint=Da biste konfigurisali pristup preko ovog korisnika baze podataka, kopirajte podatke pristupa u vašu uslugu koju obezbeđuje korisnik. Ako samo možete da kopirate podatke pristupa bez lozinke, lozinku obavezno dodajte naknadno.
#XFLD: Data Flow Runtime Capacity label in space overview
dataFlowRuntimeCapacity=Kapacitet vremena izvođenja toka podataka ({0}:{1} sati od {2} sati)
#XFLD: Could not load Data Flow Runtime Capacity
dataFlowRuntimeCapacityError=Nije moguće učitati kapacitet vremena izvođenja toka podataka
#XMSG: User can grant data consumption to other users.
databaseUserConsumptionWithGrantOptionHint=Korisnik može odobriti korišćenje podataka drugim korisnicima.
#XFLD: Enable data consumption with grant option
enableConsumptionWithGrantOption=Aktiviraj korišćenje podataka pomoću opcije odobrenja
#XMSG: To enable data consumption with grant option data consumption needs to be enabled. Do you want to enable both?
enableConsumptionWithGrantMessage=Da biste aktivirali korišćenje podataka pomoću opcije odobrenja, potrebno je aktivirati korišćenje podataka. Da li želite da aktivirate i jedno i drugo?
#XFLD: Enable Automated Predictive Library (APL) and Predictive Analysis Library (PAL)
allowUsingScriptServerFunctionality=Aktiviraj Automated Predictive Library (APL) i Predictive Analysis Library (PAL)
#XMSG: User can use SAP HANA Cloud embedded machine learning functions.
allowUsingScriptServerFunctionalityHint=Korisnik može da koristi funkcije mašinskog učenja ugrađene u SAP HANA Cloud.
#XFLD: Password Policy
passwordPolicy=Smernice za lozinku
#XMSG: Password Policy
passwordPolicyHint=Ovde aktivirajte ili deaktivirajte konfigurisane smernice za lozinku.
#XFLD: Enable Password Policy
enablePasswordPolicy=Aktiviraj smernice za lozinku
#XMSG: Read Access to the Space Schema
readAccessTitle=Pristup za čitanje šeme prostora
#XMSG: read access hint
readAccessHint=Omogućite korisniku baze podataka da poveže eksterne alate sa šemom prostora i čita poglede koji su izloženi za korišćenje.
#XFLD: Space Schema
spaceSchema=Šema prostora
#XFLD: Enable Read Access (SQL)
enableReadAccess=Aktiviraj pristup za čitanje (SQL)
#XMSG: Allow the user to grant read access to other users.
withGrantOptionHint=Omogućite korisniku da drugim korisnicima odobri pristup za čitanje.
#XFLD: With Grant Option
withGrantOption=Pomoću opcije odobrenja
#XMSG: Make your space data available in your HDI containers.
enableHDIConsumptionHint=Učinite podatke prostora dostupnim u vašim spremnicima HDI.
#XFLD: Enable HDI Consumption
enableHDIConsumption=Aktiviraj upotrebu HDI
#XMSG: Write Access to the User’s Open SQL Schema
writeAccessTitle=Pristup za pisanje u Open SQL šemu korisnika
#XMSG: write access hint
writeAccessHint=Omogućite korisniku baze podataka da poveže eksterne alate sa Open SQL šemom korisnika radi kreiranja entiteta podataka i uvoza velike količine podataka za korišćenje u prostoru.
#XFLD: Open SQL Schema
openSQLSchema=Open SQL šema
#XFLD: Enable Write Access (SQL DDL & DML)
enableWriteAccess=Aktiviraj pristup za pisanje (SQL, DDL i DML)
#XMSG: audit hint
auditHint=Protokolirajte operacije čitanja i promene u Open SQL šemi.
#XMSG: data consumption hint
dataConsumptionHint=Standardno izložite sve nove poglede u prostoru za korišćenje. Modelatori mogu da zamene ovo podešavanje za pojedinačne poglede pomoću prekidača “Izloži za korišćenje” na bočnom panelu izdavanja pogleda. Takođe možete izabrati formate u kojima se izlažu pogledi.
#XFLD: Expose for Consumption by Default
exposeDataConsumption=Standardno izloži za korišćenje
#XMSG: database users hint consumption hint
databaseUsersHint2New=Kreirajte korisnike baze podataka za povezivanje eksternih alata s rešenjem SAP Datasphere. Postavite ovlašćenja da biste omogućili korisnicima da čitaju podatke prostora i da kreiraju entitete podataka (DDL) i uvoze velike količine podataka (DML) za upotrebu u prostoru.
#XFLD: Read
read=Čitaj
#XFLD: Read (HDI)
readHDI=Čitaj (HDI)
#XFLD: Write
write=Piši
#XMSG: HDI Containers Hint
HDIContainersHint2=Omogućite pristup u vaše spremnike SAP HANA Deployment Infrastructure (HDI) u vašem prostoru. Modelatori mogu da koriste artefakte HDI kao izvore za poglede, a klijenti HDI mogu da pristupe vašim podacima prostora.
#XMSG: Open info dialog
openDatabaseUserInfoDialog=Otvori dijalog sa informacijama
#XMSG: Database user is locked. Open info dialog to unlock
openLockedDatabaseUserInfoDialog=Korisnik baze podataka je zaključan. Otvorite dijalog da biste ga otključali
#XFLD: Table
table=Tabela
#XFLD: Space section partner configuration title
partnerConnectionConfigurationSectionTitle=Veza partnera
#XFLD: Space section partner configuration subtitle
partnerConnectionConfigurationSectionSubTitle=Konfiguracija veze partnera
#XFLD: Space section partner configuration hint
partnerConnectionConfigurationSectionHint=Definišite podekran veze partnera tako što ćete dodati iFrame URL i ikonu. Ova konfiguracija je dostupna samo za ovaj klijent.
#XFLD: Table Name Field
partnerConnectionConfigurationName=Naziv podekrana
#XFLD: Table iFrame URL field
partnerConnectionConfigurationiFrameURL=iFrame URL
#XFLD: Table iFrame Post Message Origin field
partnerConnectionConfigurationiFramePostMessageOrigin=Poreklo poruke objavljivanja iFrame-a
#XFLD: Table icon/image name
partnerConnectionConfigurationIcon=Ikona
#XMSG: Table no result found
partnerConnectionConfigurationNotfound=Konfiguracije veze partnera nisu nađene.
#XMSG: Partner Connection Confgurations are not available
partnerConnectionConfigurationNotAvailable=Konfiguracije veze partnera se ne mogu prikazati kada je baza podataka vremena izvođenja nedostupna.
#XFLD: Dialog Create Partner Connection Configuration
partnerConnectionConfigurationDialogTitleAdd=Kreiraj konfiguraciju veze partnera
#XFLD: Dialog Create Partner Connection Configuration Upload Icon
partnerConnectionConfigurationButtonUloadPartnerIcon=Ikona prenosa na server
#XFLD: Dialog Create Partner Connection Configuration Select
partnerConnectionConfigurationSelectIcon=Odaberi (maksimalna veličina 200 KB)
#XFLD: Dialog Create Partner Connection Configuration Enter a name
partnerConnectionConfigurationNamePlaceholder=Primer podekrana partnera
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL
partnerConnectionConfigurationiFrameURLPlaceholder=http://www.primer.com
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL Check
partnerConnectionConfigurationiFramePostMessageOriginPlaceholder=.primer.com
#XFLD: Dialog Create Partner Connection Configuration Browse
partnerConnectionConfigurationBrowseButton=Pretraži
#XFLD: Create Partner Connection Configuration Msg
partnerConnectionConfigurationUploadMsg=Konfiguracija veze partnera je uspešno kreirana.
#XMSG: Error while deleting Partner Connection configuration(s)
partnerConnectionConfigurationDeleteError=Greška pri brisanju konfiguracija veze partnera.
#XMSG: Partner Connection Configuration has been successfully deleted
partnerConnectionConfigurationDeleteSuccess=Konfiguracija veze partnera je uspešno izbrisana.
#XMSG: Error while retrieving Partner Connection Configurations
partnerConnectionConfigurationLoadError=Greška pri pozivanju konfiguracija veze partnera.
#XMSG: Error fileSizeLimitExceeded
partnerConnectionConfigurationfileSizeLimitExceeded=Fajl se ne može preneti na server jer prekoračuje maksimalnu veličinu od 200 KB.
#XBUT: Create Partner Connection Configuration
partnerConnectionConfigurationButtonCreate=Kreiraj konfiguraciju veze partnera
#XBUT: Delete Partner Connection Configuration
partnerConnectionConfigurationButtonDelete=Izbriši konfiguraciju veze partnera.
#XMSG: Partner Tile Creation Failure
upsertPartnerTileFailed=Podekran partnera se ne može kreirati.
#XMSG: Partner Tile Deletion Failure
deletePartnerTileFailed=Podekran partnera se ne može izbrisati.
#XMSG: HANA cloud connector settings reset failure
resetHANASCCSettingsFailed=Ponovno postavljanje podešavanja za SAP HANA Cloud Connector nije uspelo
#XFLD: Workload Class
workloadClass=Klasa radnog opterećenja
#XFLD: Workload Management
workloadManagement=Upravljanje radnim opterećenjem
#XFLD: Priority
workloadClassPriority=Prioritet
#XMSG:
workloadManagementPriorityHint=Možete postaviti određivanje prioriteta ovog prostora pri postavljanju upita za bazu podataka. Unesite vrednost od 1 (najniži prioritet) do 8 (najviši prioritet). U situaciji kada se prostori takmiče za dostupne niti, prostori s višim prioritetima izvode se pre prostora s nižim prioritetima.
#XMSG:
workloadClassPriorityHint=Možete postaviti prioritet prostora od 0 (najniži) do 8 (najviši). Naredbe prostora s visokim prioritetom se izvršavaju pre naredbi ostalih prostora s nižim prioritetom. Standardni prioritet je 5. Pošto je vrednost 9 rezervisana za sistemske operacije, ona nije dostupna za prostor.
#XFLD: Statement Limits
workloadclassStatementLimits=Ograničenja naredbe
#XFLD: Workload Configuration
workloadConfiguration=Konfiguracija radnog opterećenja
#XMSG:
workloadClassStatementLimitsHint=Možete odrediti maksimalni broj (ili procenat) niti i GB memorije koje mogu trošiti naredbe koje se istovremeno izvode u prostoru. Možete uneti bilo koju vrednost ili procenat između 0 (bez ograničenja) i ukupne memorije i broja niti dostupnih u klijentu. \n\n Ako odredite ograničenje niti, imajte u vidu da to može negativno uticati na izvođenje. \n\n Ako odredite ograničenje memorije, naredbe koje dostignu ograničenje memorije neće biti izvršene.
#XMSG:
workloadClassStatementLimitsDescription=Standardna konfiguracija obezbeđuje izdašna ograničenja resursa, istovremeno sprečavajući da bilo koji pojedinačni prostor preoptereti sistem.
#XMSG:
workloadClassStatementLimitCustomDescription=Možete postaviti ograničenje maksimalnog ukupnog broja niti i memorije koje mogu trošiti naredbe koje se istovremeno izvode.
#XMSG:
totalStatementThreadLimitHelpText=Postavljanje ograničenja niti na prenisku vrednost može uticati na performanse naredbe, dok previsoke vrednosti ili 0 mogu dozvoliti da prostor iskoristi sve dostupne niti sistema.
#XMSG:
totalStatementMemoryLimitHelpText=Postavljanje ograničenja memorije na prenisku vrednost može prouzrokovati probleme nedostatka memorije, dok previsoke vrednosti ili 0 mogu dozvoliti da prostor iskoristi svu dostupnu memoriju sistema.
#XMSG:
totalStatementThreadLimitHelpTextRestricted=Unesite procenat između 1% i 70% (ili ekvivalentan broj) od ukupnog broja niti dostupnih u vašem klijentu. Postavljanje ograničenja niti na prenisku vrednost može uticati na performanse naredbi, dok previsoke vrednosti mogu uticati na performanse naredbi u drugim prostorima.
#XMSG:
totalStatementThreadLimitHelpTextDynamic=Unesite procenat između 1% i {0}% (ili ekvivalentan broj) od ukupnog broja niti dostupnih u vašem klijentu. Postavljanje ograničenja niti na prenisku vrednost može uticati na performanse naredbi, dok previsoke vrednosti mogu uticati na performanse naredbi u drugim prostorima.
#XMSG:
totalStatementMemoryLimitHelpTextNew=Unesite vrednost ili procenat između 0 (bez ograničenja) i ukupnog iznosa dostupne memorije u vašem klijentu. Postavljanje ograničenja memorije na prenisku vrednost može uticati na performanse naredbi, dok previsoke vrednosti mogu uticati na performanse naredbi u drugim prostorima.
#XFLD: Total Statement Thread Limit
totalStatementThreadLimit=Ukupno ograničenje niti naredbe
#XFLD: Segmented button label
workloadclassPercentage=%
#XFLD: Segmented button label
workloadclassGigaByte=GB
#XFLD, 80: Segmented button label
workloadclassThreads=Niti
#XFLD: Total Statement Memory Limit
totalStatementMemoryLimit=Ukupno ograničenje memorije naredbe
#YMSE: Error while loading customer hana info
loadCustomerHanaInfoError=Nije uspelo učitavanje korisničkih informacija baze podataka SAP HANA.
#XMSG:
minimumLimitReached=Minimalno ograničenje dostignuto.
#XMSG:
maximumLimitReached=Maksimalno ograničenje dostignuto.
#XMSG: Name Taken for Technical Name
technical-name-taken=Veza s tehničkim nazivom koji ste uneli već postoji. Unesite drugi naziv.
#XMSG: Name Too long for Technical Name
technical-name-too-long=Tehnički naziv koji ste uneli prekoračuje 40 znakova. Unesite naziv s manje znakova.
#XMSG: Technical name field empty
technical-name-field-empty=Unesite tehnički naziv.
#XMSG: Generic error when user enters invalid characters for a field
field-invalid-characters=Ovde možete koristiti samo slova (a-z), brojeve (0-9) i donje crte (_) za naziv.
#XMSG: Generic error when user enters invalid initial characters for a field
field-underscore-trim=Naziv koji unesete ne sme počinjati ili se završavati donjom crtom (_).
#XFLD: Checkbox label for Enable Statement Limits
enableStatementLimits=Aktiviraj ograničenja naredbe
#XFLD: Connection SubSection Settings Title in space details
connectionSettings=Podešavanja
#XMSG: Connections tool hint in Space details section
connectionsToolHint=Da biste kreirali ili uredili veze, otvorite aplikaciju Veze u bočnom usmeravanju ili kliknite ovde:
#XBUT: Connection list Open Connection Tool Button
button-connectiontool=Idi na Veze
#XFLD: Not deployed label on space tile
notDeployedLabel=Prostor još nije implementiran.
#XFLD: Not deployed additional text on space tile
notDeployedText=Implementirajte prostor.
#XFLD: Corrupt space label on space tile
corruptSpace=Došlo je do greške.
#XFLD: Corrupt space additional text on space tile
corruptSpaceHint=Pokušajte da ponovo implementirate ili se obratite podršci
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Podaci protokola revizije
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Administrativni podaci
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Drugi podaci
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Podaci u prostorima
#XMSG: Unlock Space confirmation text
unlockSpaceConfirmation=Da li zaista želite da otključate prostor?
#XMSG: Lock Space confirmation text
lockSpaceConfirmation=Da li zaista želite da zaključate prostor?
#XFLD: Lock
lock=Zaključaj
#XFLD: Unlock
unlock=Otključaj
#XFLD: Locking
locking=Zaključavanje
#XMSG: Success message after locking space
lockSpaceSuccessMsg=Prostor zaključan
#XMSG: Success message after unlocking space
unlockSpaceSuccessMsg=Prostor otključan
#XMSG: Success message after locking spaces
lockSpacesSuccessMsg=Prostori zaključani
#XMSG: Success message after unlocking spaces
unlockSpacesSuccessMsg=Prostori otključani
#YMSE: Error while locking a space
lockSpaceError=Prostor se ne može zaključati.
#YMSE: Error while unlocking a space
unlockSpaceError=Prostor se ne može otključati.
#XTIT: popup title Warning
confirmationWarningTitle=Upozorenje
#XMSG: Message strip message when a space was locked manually
manualLockedMessage=Prostor je ručno zaključan.
#XMSG: Message strip message when a space was locked due to the amount of generated audit logs
auditLogLockedMessage=Sistem je zaključao prostor jer protokoli revizije koriste veliku količinu GB na disku.
#XMSG: Message strip message when a space was locked because the assigned quota was exceeded.
quotaLockedMessage=Sistem je zaključao prostor jer prekoračuje svoje alokacije prostora u memoriji ili memorije diska.
#XMSG: Unlock Spaces confirmation text
unlockSpacesConfirmation=Da li zaista želite da otključate odabrane prostore?
#XMSG: Lock Spaces confirmation text
lockSpacesConfirmation=Da li zaista želite da zaključate odabrane prostore?
#XTIT: Scoped role editor dialog title
scopedRoleEditorDialogTitle=Uređivač uloga u opsegu
#XTIT: ECN Management title
ecnManagementTitle=Upravljanje prostorima i elastičnim čvorovima izračunavanja
#XFLD: ECNs
ecns=Elastični čvorovi izračunavanja
#XFLD: ECN phase Ready
ecnReady=Spremno
#XFLD: ECN phase Running
ecnRunning=Izvodi se
#XFLD: ECN phase Initial
ecnInitial=Nije spremno
#XFLD: ECN phase Starting
ecnStarting=Pokretanje
#XFLD: ECN phase Starting Failed
ecnStartingFailed=Pokretanje nije uspelo
#XFLD: ECN phase Stopping
ecnStopping=Zaustavljanje
#XFLD: ECN phase Stopping Failed
ecnStoppingFailed=Zaustavljanje nije uspelo
#XBTN: Assign Button
assign=Dodaj prostore
#XBTN: Start Header-Button
start=Pokreni
#XBTN: Update Header-Button
repair=Ažuriraj
#XBTN: Stop Header-Button
stop=Zaustavi
#XFLD: ECN hours remaining
ecnHoursRemaining=Preostalo 1000 sati
#XFLD: ECN block-hours remaining
ecnBlockHoursRemaining=Preostaje {0} sati blokade
#XFLD: ECN block-hour remaining
ecnBlockHourRemaining=Preostaje {0} sat blokade
#XTIT: ECN creation dialog title
ecnCreationDialogTitle=Kreiraj elastični čvor izračunavanja
#XTIT: ECN edit dialog title
ecnEditDialogTitle=Uredi elastični čvor izračunavanja
#XTIT: ECN deletion dialog title
ecnDeletionDialogTitle=Izbriši elastični čvor izračunavanja
#XTIT: ECN Space assignment dialog title
ecnSpaceAssignmentDialogTitle=Dodaj prostore
#XFLD: ECN ID
ECNIDLabel=Elastični čvor izračunavanja
#XTXT: Selected toolbar text
selectedToolbarText=Odabrano: {0}
#XTIT: Elastic Compute Nodes
ECNslong=Elastični čvorovi izračunavanja
#XTIT: Object assignment - Table header: Objects available
objectsAvailable=Broj objekata
#XTIT: Object assignment - Dialog header text
selectObjects=Odaberite prostore i objekte koje želite da dodelite svom elastičnom čvoru izračunavanja:
#XTIT: Object assignment - Table header title: Objects
objects=Objekti
#XTIT: Object assignment - Table header: Type
type=Tip
#XMSG: Message strip delete database user audit warning
deleteDatabaseUserWarningMessageStrip=Imajte u vidu da će brisanje korisnika baze podataka dovesti do brisanja svih generisanih unosa protokola revizije. Ako želite da zadržite protokole revizije, razmotrite da ih izvezete pre nego što izbrišete korisnika baze podataka.
#XMSG: Message strip delete HDI container audit logs impact
deleteHDIContainerWarningMessageStrip=Imajte u vidu da poništavanje dodele spremnika HDI iz prostora će dovesti do brisanja svih generisanih unosa protokola revizije. Ako želite da zadržite protokole revizije, razmotrite da ih izvezete pre nego što poništite dodelu spremnika HDI.
#XTXT: All audit logs
allAuditLogs=Svi unosi protokola revizije generisani za prostor
#XMSG: Message strip disable audit warning
disableAuditWarningMessageStrip=Imajte u vidu da deaktiviranje smernica revizije (operacije čitanja ili promene) dovešće do brisanja svih njihovih unosa protokola revizije. Ako želite da zadržite unose protokola revizije, razmotrite da ih izvezete pre nego što deaktivirate smernice revizije.
#XTIT: No Spaces assigned to ECN Illustration title
noSpacesECNTitle=Prostori ili objekti još nisu dodeljeni
#XTIT: No Spaces assigned to ECN Illustration description
noSpacesECNDescription=Da biste započeli rad sa elastičnim čvorom izračunavanja, dodelite mu prostor ili objekte.
#XTIT: No Spaces Illustration title
noSpacesTitle=Prostor još nije kreiran
#XTIT: No Spaces Illustration description
noSpacesDescription=Da biste započeli prikupljanje podataka, kreirajte prostor.
#XTIT: No Spaces Illustration title
noDeletedSpacesTitle=Korpa za otpatke je prazna
#XTIT: No Spaces Illustration description
noDeletedSpacesDescription=Odavde možete da vratite izbrisane prostore.
#XTXT:  {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDatabaseUsers=Kada se prostor implementira, sledeći korisnici baze podataka biće {0} izbrisani i ne mogu se obnoviti:
#XTIT: Delete database users
deleteDatabaseUsersTitle=Izbriši korisnike baze podataka
#YMSE: Error if ecn id already exists
ecnIDInputDuplicateError=ID već postoji.
#YMSE: Error if ecn id contains invalid characters
ecnIDInputInvalidError=Koristite samo mala slova a – z i brojeve 0 – 9
#YMSE: Error if ecn id is too short
ecnIDInputTooShortError=ID mora da sadrži najmanje {0} znakova.
#XMSG: ecn id length warning
ecnIdLengthWarning=Maksimum od {0} znakova prekoračen.
#XFLD: open System Monitor
systemMonitor=Monitor sistema
#XFLD: open ECN schedule dialog menu entry
schedule=Raspored
#XFLD: open create ECN schedule dialog
createSchedule=Kreiraj raspored
#XFLD: open change ECN schedule dialog
changeSchedule=Uredi raspored
#XFLD: open delete ECN schedule dialog
deleteSchedule=Izbriši raspored
#XFLD: open self assign ECN schedule dialog
selfAssignSchedule=Dodeli mi raspored
#XFLD: open pause ECN schedule dialog
pauseSchedule=Pauziraj raspored
#XFLD: open resume ECN schedule dialog
resumeSchedule=Nastavi raspored
#XFLD: View Logs
viewLogs=Prikaži protokole
#XFLD: Compute Blocks
computeBlocks=Blokovi računanja
#XFLD: Memory label in ECN creation dialog
ecnMemory=Memorija (GB)
#XFLD: Storage label in ECN creation dialog
ecnStorage=Prostor za skladištenje (GB)
#XFLD: Number of CPU label in ECN creation dialog
ecnCPUCount=Broj CPU
#XFLD: ECN updated by label
changedBy=Promenio
#XFLD: ECN updated on label
changedOn=Promenjeno
#XMSG: Success message after creating a Elastic Compute Node
createEcnSuccessMessage=Elastični čvor izračunavanja kreiran
#YMSE: Error while creating a Elastic Compute Node
createEcnError=Nije moguće kreirati elastični čvor izračunavanja
#XMSG: Success message after updating a Elastic Compute Node
updateEcnSuccessMessage=Elastični čvor izračunavanja ažuriran
#XMSG: Error while updating a Elastic Compute Node
udpateEcnError=Nije moguće ažurirati elastični čvor izračunavanja
#XMSG: Success message after deleting a Elastic Compute Node
deleteEcnSuccessMessage=Elastični čvor izračunavanja izbrisan
#YMSE: Error while deleting a Elastic Compute Node
deleteEcnError=Nije moguće izbrisati elastični čvor izračunavanja
#XMSG: Success message after starting a Elastic Compute Node
startingEcnSuccessMessage=Pokretanje elastičnog čvora izračunavanja
#XMSG: Success message after stopping a Elastic Compute Node
stoppingEcnSuccessMessage=Zaustavljanje elastičnog čvora izračunavanja
#YMSE: Error while starting a Elastic Compute Node
startingEcnError=Nije moguće pokrenuti elastični čvor izračunavanja
#YMSE: Error while stopping a Elastic Compute Node
stoppingEcnError=Nije moguće zaustaviti elastični čvor izračunavanja
#XBUT: Add Object button for an ECN
assignObjects=Dodaj objekte
#XFLD: Enable auto object assignment label
enableAutoObjAssignment=Dodaj sve objekte automatski
#XFLD: object type label to be assigned
objectTypeLabel=Tip (semantička upotreba)
#XFLD: assigned object type label
assignedObjectTypeLabel=Tip
#XFLD: technical name label
TechnicalNameLabel=Tehnički naziv
#XFLD: Object selecttion ECN label
objectSelectionECNLabel=Odaberite objekte koje želite da dodate elastičnom čvoru izračunavanja
#XTIT: Add objects dialog title
assignObjectsTitle=Dodeli objekte
#XFLD: object label with object count
objectLabel=Objekat
#XMSG: No objects available to add message.
noObjectsToAssign=Nema objekata raspoloživih za dodavanje.
#XMSG: No objects assigned message.
noAssignedObjects=Objekti nisu dodati.
#XTIT: Remove objects popup title
removeObjectsOrSpacesConfirmationTitle=Upozorenje
#XTIT: Delete ECN popup button and title
deleteECNConfirmationButtonAndTitle=Izbriši
#XMSG: Remove objects popup text
removeObjectsConfirmation=Da li zaista želite da uklonite odabrane objekte?
#XMSG: Remove spaces popup text
removeSpacesConfirmation=Da li zaista želite da uklonite odabrane prostore?
#XBUT: Confirmation button for objects dialog
removeObjectsOrSpacesConfirmationBtn=Ukloni prostore
#XMSG: Success message after removing objects
removeObjectsSuccessMessage=Izloženi objekti su uklonjeni
#XMSG: Success message after assigning objects
assignObjectsSuccessMessage=Izloženi objekti su dodeljeni
#XFLD: auto object assignment label for space tile
autoObjAssignmentLabel=Svi izloženi objekti
#XFLD: Spaces tab label
spacesTabLabel=Prostori
#XFLD: Exposed objects tab label
consumptionObjectsTabLabel=Izloženi objekti
#XMSG: Success message after unassigning spaces
spacesUnassignedSuccessMessage=Prostori su uklonjeni
#XMSG: Success message after unassigning space
spaceUnassignedSuccessMessage=Prostor je uklonjen
#YMSE: Error while assigning / unassigning spaces
spacesAssignUnassignError=Prostori se ne mogu dodeliti ni ukloniti.
#YMSE: Error while removing objects
removeObjectsError=Nije moguće dodeliti niti ukloniti objekte.
#YMSE: Error while removing object
removeObjectError=Nije moguće dodeliti niti ukloniti objekat.
#YMSE: Error when saving the ecn with invalid value
invalidSizingPlanError=Prethodno odabrani broj više ne važi. Odaberite važeći broj.
#YMSE: Error when saving the ecn with invalid value
invalidPerformanceClassError=Odaberite važeću klasu učinka.
#YMSE: Error when saving the ecn with invalid value
performanceClassNotAvailableError=Prethodno odabrana klasa učinka "{0}" trenutno nije važeća. Odaberite važeću klasu učinka.
#XTXT: Delete ECN confirmation text
deleteConfirmationTxtECN=Da li zaista želite da izbrišete elastični čvor izračunavanja?
#XFLD: tooltip for ? button
help=Pomoć
#XFLD: ECN edit button label
editECN=Konfiguriši
#XFLD: Technical type label for ERModel
DWC_ERMODEL=Model odnosa entiteta
#XFLD: Technical type label for Local Table
DWC_LOCAL_TABLE=Lokalna tabela
#XFLD: Technical type label for Remote Table
DWC_REMOTE_TABLE=Udaljena tabela
#XFLD: Technical type label for Analytic Model
DWC_ANALYTIC_MODEL=Analitički model
#XFLD: Technical type label for Task Chain
DWC_TASKCHAIN=Lanac zadataka
#XFLD: Technical type label for Data Flow
DWC_DATAFLOW=Tok podataka
#XFLD: Technical type label for Replication Flow
DWC_REPLICATIONFLOW=Tok replikacije
#XFLD: Technical type label for Transformation Flow
DWC_TRANSFORMATIONFLOW=Tok transformacije
#XFLD: Technical type label for Intelligent Lookup
DWC_IDT=Pametno traženje
#XFLD: Technical type label for Repository
DWC_REPOSITORY_PACKAGE=Repozitorijum
#XFLD: Technical type label for Enterprise Search
DWC_ESMODEL=Enterprise Search
#XFLD: Technical type label for View
DWC_VIEW=Prikaži
#XFLD: Technical type label for Data Product
DWC_DATAPRODUCT=Proizvod podataka
#XFLD: Technical type label for Data Access Control
DWC_DAC=Kontrola pristupa podacima
#XFLD: Technical type label for Folder
DWC_FOLDER=Folder
#XFLD: Technical type label for Data Entity
DWC_BUSINESS_ENTITY=Poslovni entitet
#XFLD: Technical type label for Business Entity Variant
DWC_BUSINESS_ENTITY_VARIANT=Varijanta poslovnog entiteta
#XFLD: Technical type label for Responsibility Scenario
DWC_AUTH_SCENARIO=Scenario odgovornosti
#XFLD: Technical type label for Fact Model
DWC_FACT_MODEL=Model činjenica
#XFLD: Technical type label for Transformation Flow
DWC_PERSPECTIVE=Perspektiva
#XFLD: Technical type label for Consumption Model
DWC_CONSUMPTION_MODEL=Model potrošnje
#XFLD: Technical type label for Remote Connection
DWC_REMOTECONNECTION=Udaljena veza
#XFLD: Technical type label for Fact Model Variant
DWC_FACT_MODEL_VARIANT=Varijanta modela činjenica
#XMSG: Schedule created alert message
createScheduleSuccess=Plan kreiran
#XMSG: Schedule updated alert message
updateScheduleSuccess=Plan ažuriran
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Plan izbrisan
#XMSG: Schedule takeover success message
ownerChangeScheduleSuccess=Raspored dodeljen vama
#XMSG: Message for starting pausing of schedule
pauseScheduleStarted=Pauziranje 1 plana
#XMSG: Message for starting resuming of schedule
resumeScheduleStarted=Nastavak 1 plana
#XFLD: Segmented button label
availableSpacesButton=Dostupno
#XFLD: Segmented button label
selectedSpacesButton=Odabrano
#XFLD: Visit website button text
visitWebsite=Poseti veb-sajt
#XMSG: Warning message on unselecting source language
sourceLanguageRemovalWarning=Prethodno odabrani izvorni jezik biće uklonjen.
#XBUT: enable translation confirmation button
enableTranslationConfirmationBtn=Aktiviraj
#XFLD: ECN performance class label
performanceClassLabel=Klasa učinka
#XTXT performance class memory text
memoryText=Memorija
#XTXT performance class compute text
computeText=Izračunaj
#XTXT performance class high-compute text
highComputeText=Izračunavanje visokog nivoa
#XBUT: Recycle Bin Button Text
recycleBin=Korpa za otpatke
#XBUT: Restore Button Text
restore=Obnovi
#XMSG: Warning message for new Workload Management UI
priorityWarning=Ova oblast je samo za čitanje. Možete koristiti prioritet prostora u oblasti Sistem / Konfiguracija / Upravljanje radnim opterećenjem.
#XMSG: Warning message for new Workload Management UI
workloadWarning=Ova oblast je samo za čitanje. Možete koristiti konfiguraciju radnog opterećenja prostora u oblasti Sistem / Konfiguracija / Upravljanje radnim opterećenjem.
#XCOL: Space table-view column: Apache Spark vCPUs
sparkManagementVCPUs=Apache Spark vCPU
#XCOL: Space table-view column: Apache Spark Memory (GB)
sparkManagementMemory=Memorija za Apache Spark (GB)
#XCOL: Space type (from Space Capability) for Data Ingestion Spaces
ingestionSpaceType=Uvoz velike količine podataka proizvoda
#XFLD: Deployment in progress nodatatext for spark table
sparkTableDeploymentInProgress=Podaci nisu dostupni jer se prostor trenutno implementira
#XFLD: Runtime data being loaded nodatatext for spark table
sparkTableDeploymentLoading=Podaci nisu dostupni jer se prostor trenutno učitava
#XCOL: Space type (from Space Capability) for BW Private Cloud Edition
BWPCESpaceType=SAP BW
#XBUT: Go to Instance Mappings Button
navToMappingsButton=Uredi preslikavanja instance
#XFLD: SAP HANA Data Lake Storage label if less than 1GB
dataLakeStorageUsed=< 1 GB
