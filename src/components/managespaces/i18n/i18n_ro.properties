#DO NOT REMOVE________________________
#XFLD: Breadcrumb for space monitoring page
spaceMonitorBreadcrumb=Monitorizare
#XTXT: Type name for spaces in browser tab page title
space=Spațiu
#_____________________________________
#XFLD: Spaces label in
spaces=Spații
#XFLD: Manage plan button text
manageQuotaButtonText=Gestionare plan
#XBUT: Manage resources button
manageResourcesButton=Gestionare resurse
#XFLD: Create space button tooltip
createSpace=Creare spațiu
#XFLD: Create
create=Creare
#XFLD: Deploy
deploy=Implementare
#XFLD: Page
page=Pagină
#XFLD: Cancel
cancel=Anulare
#XFLD: Update
update=Actualizare
#XFLD: Save
save=Salvare
#XFLD: OK
ok=OK
#XFLD: days
days=Zile
#XFLD: Space tile edit button label
edit=Editare
#XFLD: Auto Assign all objects to space
autoAssign=Alocare automată
#XFLD: Space tile open monitoring button label
openMonitoring=Monitorizare
#XFLD: Delete
delete=Ștergere
#XFLD: Copy Space
copy=Copiere
#XFLD: Close
close=Închidere
#XCOL: Space table-view column status
status=Stare
#XFLD: Space status active
activeLabel=Activ
#XFLD: Space status locked
lockedLabel=Blocat
#XFLD: Space status critical
criticalLabel=Critic
#XFLD: Space status cold
coldLabel=Rece
#XFLD: Space status deleted
deletedLabel=Șters
#XFLD: Space status unknown
unknownLabel=Necunoscut
#XFLD: Space status ok
okLabel=Sănătos
#XFLD: Database user expired
expired=Expirat
#XFLD: deployed
deployed=Implementat
#XFLD: not deployed
notDeployed=Neimplementat
#XFLD: changes to deploy
changesToDeploy=Modificări de implementat
#XFLD: pending
pending=Implementare în curs
#XFLD: designtime error
designtimeError=Eroare de timp de design
#XFLD: runtime error
runtimeError=Eroare de timp de execuție
#XFLD: Space created by label
createdBy=Creat de
#XFLD: Space created on label
createdOn=Creat pe
#XFLD: Space deployed on label
deployedOn=Implementat pe
#XFLD: Space ID label
spaceID=ID spațiu
#XFLD: Priority label
priority=Prioritate
#XFLD: Space Priority label
spacePriority=Prioritate spațiu
#XFLD: Space Configuration label
spaceConfiguration=Configurare spațiu
#XFLD: Not available
notAvailable=Indisponibil
#XFLD: WorkloadType default
default=Implicit
#XFLD: WorkloadType custom
custom=Definit de utilizator
#XFLD: Data Lake option label; refers to component SAP HANA Cloud, Data Lake
dataLakeAccess=Acces la lac de date
#XFLD: Translation label
translationLabel=Traducere
#XFLD: Source language label
sourceLanguageLabel=Limba sursă
#XFLD: Translation CheckBox label
translationCheckBox=Activare traducere
#XTXT: Popover hint that space has to be deployed to show user details
openDatabaseUserHintPopoverText=Implementați spațiul pentru a accesa detaliile de utilizator.
#XTXT: Popover hint that space has to be deployed to open database explorer
openDatabaseExplorerHintPopoverText=Implementați spațiul pentru a deschide explorerul de bază de date.
#XTXT: Text for when the Data Lake check box is disabled
dataLakeOptionDisabled=Nu puteți utiliza acest spațiu pentru a accesa lacul de date deoarece este deja utilizat de un alt spațiu.
#XFLD: Data Lake check box text; refers to component SAP HANA Cloud, Data Lake
dataLakeCheckBox=Utilizați acest spațiu pentru a accesa lacul de date.
#XFLD: Space Priority minimum label extension
low=Mică
#XFLD: Space Priority maximum label extension
high=Mare
#XFLD: Space name label
spaceName=Nume spațiu
#XFLD: Enable deploy objects checkbox
enableDeployObjects=Implementare obiecte
#XTIT: Copy spaces dialog title
copySpaceDialogTitle=Copiere {0}
#XTXT Human readable text for unselected source language
unSelectedSourceLanguage=(Neselectat)
#XTXT Human readable text for language code "af"
af=afrikaans
#XTXT Human readable text for language code "ar"
ar=arabă
#XTXT Human readable text for language code "bg"
bg=bulgară
#XTXT Human readable text for language code "ca"
ca=catalană
#XTXT Human readable text for language code "zh"
zh=chineză simplificată
#XTXT Human readable text for language code "zf"
zf=chineză
#XTXT Human readable text for language code "hr"
hr=croată
#XTXT Human readable text for language code "cs"
cs=cehă
#XTXT Human readable text for language code "cy"
cy=galeză
#XTXT Human readable text for language code "da"
da=daneză
#XTXT Human readable text for language code "nl"
nl=neerlandeză
#XTXT Human readable text for language code "en-UK"
en-UK=engleză (Regatul Unit)
#XTXT Human readable text for language code "en"
en=engleză (Statele Unite)
#XTXT Human readable text for language code "et"
et=estonă
#XTXT Human readable text for language code "fa"
fa=persană
#XTXT Human readable text for language code "fi"
fi=finlandeză
#XTXT Human readable text for language code "fr-CA"
fr-CA=franceză (Canada)
#XTXT Human readable text for language code "fr"
fr=franceză
#XTXT Human readable text for language code "de"
de=germană
#XTXT Human readable text for language code "el"
el=greacă
#XTXT Human readable text for language code "he"
he=ebraică
#XTXT Human readable text for language code "hi"
hi=hindi
#XTXT Human readable text for language code "hu"
hu=maghiară
#XTXT Human readable text for language code "is"
is=islandeză
#XTXT Human readable text for language code "id"
id=indoneziană (Bahasa Indonesia)
#XTXT Human readable text for language code "it"
it=italiană
#XTXT Human readable text for language code "ja"
ja=japoneză
#XTXT Human readable text for language code "kk"
kk=kazahă
#XTXT Human readable text for language code "ko"
ko=coreeană
#XTXT Human readable text for language code "lv"
lv=letonă
#XTXT Human readable text for language code "lt"
lt=lituaniană
#XTXT Human readable text for language code "ms"
ms=Bahasa Melayu
#XTXT Human readable text for language code "no"
no=norvegiană
#XTXT Human readable text for language code "pl"
pl=poloneză
#XTXT Human readable text for language code "pt"
pt=portugheză (Brazilia)
#XTXT Human readable text for language code "pt-PT"
pt-PT=portugheză (Portugalia)
#XTXT Human readable text for language code "ro"
ro=română
#XTXT Human readable text for language code "ru"
ru=rusă
#XTXT Human readable text for language code "sr"
sr=sârbă
#XTXT Human readable text for language code "sh"
sh=sârbo-croată
#XTXT Human readable text for language code "sk"
sk=slovacă
#XTXT Human readable text for language code "sl"
sl=slovenă
#XTXT Human readable text for language code "es"
es=spaniolă
#XTXT Human readable text for language code "es-MX"
es-MX=spaniolă (Mexic)
#XTXT Human readable text for language code "sv"
sv=suedeză
#XTXT Human readable text for language code "th"
th=thailandeză
#XTXT Human readable text for language code "tr"
tr=turcă
#XTXT Human readable text for language code "uk"
uk=ucraineană
#XTXT Human readable text for language code "vi"
vi=vietnameză
#XTIT: Delete spaces confirmation title
deleteConfirmationHeaderText=Ștergere spații
#XTXT: Logically delete space delete confirmation text with space name
logicallyDeleteConfirmationBodyTextWithSpaceName=Sigur doriți să mutați spațiul "{0}" în coșul de gunoi?
#XTXT: Logically dpace delete confirmation text for multiple spaces
logicallyDeleteConfirmationBodyTextMultiple=Sigur doriți să mutați spațiile selectate {0} în coșul de gunoi?
#XTXT: Delete space delete confirmation text with space name
deleteConfirmationBodyTextWithSpaceName=Sigur doriți să ștergeți spațiul "{0}"? Această acțiune nu poate fi anulată.
#XTXT: Delete space delete confirmation text for multiple spaces
deleteConfirmationBodyTextMultiple=Sigur doriți să ștergeți cele {0} spații selectate? Această acțiune nu poate fi anulată. Următorul conținut va fi șters {1}:
#XTXT: permanently
permanently=permanent
#XTXT: {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDetail=Următorul conținut va fi șters {0} și nu poate fi recuperat:
#XTXT: Hint Delete space confirmation text
deleteConfirmationHint=Introduceți {0} pentru a confirma ștergerea.
#XTXT: Delete space confirmation error check spelling
deleteConfirmationErrorCheckSpelling=Efectuați verificarea ortografică și reîncercați.
#XTXT: All Spaces
allSpaces=Toate spațiile
#XTXT: All data
allData=Toate obiectele și datele conținute în spațiu
#XTXT: All connections
allConnections=Toate conexiunile definite în spațiu
#XFLD: Space tile selection box tooltip
clickToSelect=Click pentru selectare
#XTXT: All database users
allDatabaseUsers=Toate obiectele și datele conținute în orice schemă Open SQL asociate cu spațiul
#XFLD: remove members button tooltip
deleteUsers=Eliminare membri
#XTXT: Space long description text
description=Descriere (maxim 4000 de caractere)
#XFLD: Add Members button tooltip
addUsers=Adăugare membri
#XFLD: Add Users button tooltip
addUsersTooltip=Adăugare utilizatori
#XFLD: Edit Users button tooltip
editUsersTooltip=Editare utilizatori
#XFLD: Remove Users button tooltip
removeUsersTooltip=Eliminare utilizatori
#XFLD: Searchfield placeholder
filter=Căutare
#XCOL: Users table-view column health
health=Sănătate
#XCOL: Users table-view column access
access=Acces
#XFLD: No user found nodatatext
noDataText=Niciun utilizator găsit
#XTIT: Members dialog title
selectUserDialogTitle=Adăugare membri
#XTIT: User dialog title
addUserDialogTitle=Adăugare utilizatori
#XTIT: Delete connections confirmation title
deleteConnectionConfirmationHeaderText=Ștergere conexiuni
#XTIT: Delete single connection confirmation title
deleteConnectionConfirmationHeaderTextSingle=Ștergere conexiune
#XTXT: Delete connection delete confirmation text
deleteConnectionConfirmationBodyText=Sigur doriți să ștergeți conexiunile selectate? Vor fi eliminate definitiv.
#XTIT: Remote source dialog title
selectRemoteConnectionDialogTitle=Selectare conexiuni
#XTIT: Share connection dialog title
connectionSharingDialogTitle=Partajare conexiune
#XTIT: Connections shared from other spaces table title
sharedConnectionsFromOtherSpaces=Conexiuni partajate
#XFLD: Add remote source button tooltip
addRemoteConnections=Adăugare conexiuni
#XFLD: Delete remote source button tooltip
deleteRemoteConnections=Eliminare conexiuni
#XFLD: Share remote source button tooltip
shareConnections=Partajare conexiuni
#XFLD: Tile-layout tooltip
tileLayout=Layout mozaicuri
#XFLD: Table-layout tooltip
tableLayout=Layout tabel
#XMSG: Success message after creating space
createSpaceSuccessMessage=Spațiu creat
#XMSG: Success message after copying space
copySpaceSuccessMessage=Copiere spațiu "{0}" în spațiu "{1}"
#XMSG: Success message after deploying space
deploymentSuccessMessage=Implementarea spațiului a fost lansată
#XMSG: Success message after Apache Spark update started
sparkUpdateStartedSuccessMessage=Actualizarea Apache Spark a fost lansată
#YMSE: Error while starting Apache Spark update
sparkUpdateStartedErrorMessage=Eroare la actualizare Apache Spark
#XMSG: Success message after updating space
updateSettingsSuccessMessage=Detalii spațiu actualizate
#XMSG: Success message after unlocking space
unlockSpaceSuccessMessage=Spațiu deblocat temporar
#XMSG: Success message after deleting a single space
deleteSpaceSuccessMessage=Spațiu șters
#XMSG: Success message after deleting multiple spaces
deleteSpacesSuccessMessage=Spații șterse
#XMSG: Success message after restoring a single space
restoreSpaceSuccessMessage=Spațiu restaurat
#XMSG: Success message after restoring multiple spaces
restoreSpacesSuccessMessage=Spații restaurate
#YMSE: Error while updating settings
updateSettingsFailureMessage=Setările de spațiu nu au putut fi actualizate.
#YMSE: Error while updating data lake option
dataLakeEnabledInOtherSpace=Lacul de date este deja alocat la un alt spațiu. Un singur spațiu, pe rând, poate accesa lacul de date.
#YMSE: Error while updating data lake option
virtualTablesExists=Nu puteți anula alocarea lacului de date din acest spațiu deoarece încă există dependențe la tabele virtuale*. Ștergeți tabelele virtuale pentru a anula alocarea lacului de date din acest spațiu.
#YMSE: Error while unlocking space
unlockSpaceFailureMessage=Spațiul nu a putut fi deblocat.
#YMSE: Error while creating space
createSpaceError=Spațiul nu a putut fi creat.
#YMSE: Error while creating duplicate space
createDuplicateSpaceError=Un spațiu cu numele {0} există deja.
#YMSE: Error while deleting a single space
deleteSpaceError=Spațiul nu a putut fi șters.
#YMSE: Error details while deleting a single space
deleteSpaceDetailedError=Spațiul dvs. “{0}” nu mai funcționează corespunzător. Încercați din nou să îl ștergeți. Dacă tot nu funcționează, solicitați-i administratorului să vă șteargă spațiul sau să deschidă un tichet.
#YMSE: Error while deleting a single space in files
deleteSpaceInFilesError=Datele spațiului din Fișiere nu au putut fi șterse.
#YMSE: Error while deleting a single space users
deleteSpaceUsersError=Utilizatorii nu au putut fi eliminați.
#YMSE: Error while deleting a single space schema
deleteSpaceSchemasError=Schemele nu au putut fi eliminate.
#YMSE: Error while deleting a single space connections
deleteSpaceConnectionsError=Conexiunile nu au putut fi eliminate.
#YMSE: Error while deleting a single space data
deleteSpaceDataError=Datele spațiului nu a putut fi șterse.
#YMSE: Error while deleting multiple spaces
deleteSpacesError=Spațiile nu au putut fi șterse.
#YMSE: Error while restoring a single space
restoreSpaceError=Spațiul nu a putut fi restaurat.
#YMSE: Error while restoring multiple spaces
restoreSpacesError=Spațiile nu au putut fi restaurate.
#YMSE: Error while creating users
createUsersError=Utilizatorii nu au putut fi adăugați.
#YMSE: Error while removing users
removeUsersError=Nu am putut elimina utilizatorii.
#YMSE: Error while removing user
removeUserError=nu a putut elimina utilizatorul.
#YMSE: Error assigning roles to a user
roleAssignUnAssignError=Nu am putut adăuga utilizatorul la rolurile în volum selectate. \n\n Nu vă puteți adăuga la un rol în volum. Puteți solicita administratorului să vă adauge la un rol în volum.
#YMSE: Error assigning user to the space
userAssignError=Nu am putut aloca utilizatorul la spațiu. \n\n Utilizatorul este deja alocat la numărul maxim permis (100) de spații în rolurile în volum.
#YMSE: Error assigning users to the space
usersAssignError=Nu am putut aloca utilizatorii la spațiu. \n\n Utilizatorul este deja alocat la numărul maxim permis (100) de spații în rolurile în volum.
#YMSE: Error while fetching users
fetchUserWithScopedRolesError=Nu am putut regăsi utilizatorii. Reîncercați mai târziu.
#YMSE: Error while fetching scoped roles
fetchScopedRolesError=Nu am putut regăsi rolurile în volum.
#YMSE: Error while fetching members
fetchUserError=Membrii nu au putut fi obținuți. Reîncercați mai târziu.
#YMSE: Error while loading run-time database
loadRuntimeError=Nu am putut încărca informațiile din baza de date cu timp de execuție.
#YMSE: Error while loading spaces
loadSpacesError=A apărut o problemă la încercarea de regăsire a spațiilor dvs.
#YMSE: Error while loading haas resources
loadStorageError=A apărut o problemă la încercarea de regăsire a datelor stocate.
#YMSE: Error no data could be loaded
loadDataError=A apărut o problemă la încercarea de regăsire a datelor dvs.
#XFLD: Click to refresh storage data
clickToRefresh=Efectuați click aici pentru a reîncerca.
#XTIT: Delete space popup title
deleteSpacePopupTitle=Ștergere spațiu
#XCOL: Spaces table-view column name
name=Nume
#XCOL: Spaces table-view deployment status
deploymentStatus=Stare implementare
#XFLD: Disk label in space details
storageLabel=Disc (GB)
#XFLD: In-Memory label in space details
ramLabel=Memorie (GB)
#XFLD: Memory label on space card
memory=Memorie pentru stocare
#XFLD: Storage Assignment label in space details
storrageAssignmentLabel=Stocare spațiu
#XFLD: Storage Type label in space details
storageTypeLabel=Tip de stocare
#XFLD: Enable Space Quota
enableSpaceQuota=Activare cotă de spațiu
#XFLD: No Space Quota
noSpaceQuota=Fără cotă de spațiu
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
diskAndInMemoryText=Bază de date SAP HANA (disc și memorie internă)
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
dataLakeFilesText=Fișiere lac de date SAP HANA
#XFLD: Available scoped roles label
availableRoles=Roluri în volum disponibile
#XFLD: Selected scoped roles label
selectedRoles=Roluri în volum selectate
#XCOL: Spaces table-view column models
models=Modele
#XCOL: Spaces table-view column users
users=Utilizatori
#XCOL: Spaces table-view column connections
connections=Conexiuni
#XFLD: Section header overview in space detail
overview=Imagine generală
#XFLD: Section header Apache Spark in space detail
sparkManagementSection=Apache Spark
#XFLD: Section header Apache Spark in space detail
sparkManagementSubSection=Aplicații
#XFLD: Section header Apache Spark in space detail
taskAssignmentSubSection=Alocare sarcină
#XFLD: vCPU label in Apache Spark section
vCPULabel=vCPU-uri
#XFLD: Memory label in Apache Spark section
memoryLabel=Memorie (GB)
#XFLD: space configuration label in Apache Spark section
spaceConfigurationLabel=Configurare spațiu
#XFLD: Space Source label
sparkApplicationLabel=Aplicație
#XFLD: Cluster Size label
clusterSizeLabel=Dimensiune cluster
#XFLD: Driver label
driverLabel=Driver
#XFLD: Executor label
executorLabel=Executor
#XFLD: max label
maxLabel=Maxim utilizat
#XFLD: TrF Default label
trFDefaultLabel=Valoare implicită flux de transformare
#XFLD: Merge Default label
mergeDefaultLabel=Concatenare valoare implicită
#XFLD: Optimize Default label
optimizeDefaultLabel=Optimizare valoare implicită
#XFLD: Deployment Default label
deploymentDefaultLabel=Implementare tabel local (fișier)
#XFLD: max spark source label
driverExecutorMaxUsedSparkSource={0} CPU / {1} GB
#XFLD: Object type label
taskObjectTypeLabel=Tip obiect
#XFLD: Task activity label
taskActivityLabel=Activitate
#XFLD: Task Application ID label
taskApplicationIDLabel=Aplicație implicită
#XFLD: Section header in space detail
generalSettings=Setări generale
#XMSG: Message strip message when a space is locked
spaceLockedMessageStrip=Acest spațiu este blocat în prezent de sistem.
#XMSG: Message strip message deploy warning
deployWarningMessageStrip=Modificările din această secțiune vor fi implementate imediat.
#XMSG: Message strip message modify space configuration warning
modifySpaceConfigurationMessageStrip=Rețineți că modificarea acestor valori poate cauza probleme de performanță.
#XFLD: Button text to unlock the space again
unlockSpace=Deblocare spațiu
#XFLD: Info text for audit log formatted message
auditLogText=Permiteți jurnalelor de audit să înregistreze acțiunile de citire sau modificare (politicile de audit). Administratorii pot analiza apoi cine a efectuat o acțiune la un moment în timp.
#XMSG: Message strip  audit warning
auditLogWarningMessageStrip=Jurnalele de audit pot consuma o cantitate mare de spațiu de stocare pe disc în tenantul dvs. Dacă activați o politică de audit (acțiuni de citire sau modificare), trebuie să monitorizați în mod regulat utilizarea spațiului de stocare pe disc (prin intermediul cardului de stocare pe disc utilizat în monitorul de sistem) pentru a evita întreruperile complete ale discului, ceea ce poate duce la întreruperi ale serviciului. Dacă dezactivați o politică de audit, toate intrările de jurnal de audit vor fi șterse. Dacă doriți să păstrați intrările jurnalului de audit, luați în considerare exportul acestora înainte de a dezactiva politica de audit.
#XFLD: Message strip link text to open in app help to guide user what to do to unlock his space permanently
unlockSpaceHelp=Afișare ajutor
#XMSG: Message strip message when a space will be locked again
spaceTemporarilyUnlockedMessageStrip=Acest spațiu depășește spațiul său de stocare alocat și va fi blocat în {0} {1}.
#XMSG: Unit for remaining time until space is locked again
hours=ore
#XMSG: Unit for remaining time until space is locked again
minutes=minute
#XFLD: Section header in space detail for auditing
detailsSectionAuditing=Auditing
#XFLD: Subsection header in space detail for auditing
auditing=Setări de audit spațiu
#XFLD: Hot space tooltip
hotSpaceCountTooltip=Spații critice: spațiul de stocare utilizat este peste 90%.
#XFLD: Green space tooltip
greenSpaceCountTooltip=Spații sănătoase: spațiul de stocare utilizat este cuprins între 6% și 90%.
#XFLD: Cold space tooltip
coldSpaceCountTooltip=Spații cold: spațiul de stocare utilizat este 5% sau mai puțin.
#XFLD: Critical space tooltip
criticalSpaceCountTooltip=Spații critice: spațiul de stocare utilizat este peste 90%.
#XFLD: Green space tooltip
okSpaceCountTooltip=Spații sănătoase: spațiul de stocare utilizat este cuprins între 6% și 90%.
#XFLD: Locked space tooltip
lockedSpaceCountTooltipNew=Spații cu starea “blocată”: blocarea este cauzată de memoria insuficientă.
#XFLD: Locked space tooltip
lockedSpaceCountTooltip=Spații blocate
#YMSE: Error while deleting remote source
deleteRemoteError=Conexiunile nu au putut fi eliminate.
#XFLD: Space ID input tooltip
spaceIDInfoTooltip=ID spațiu nu poate fi modificat ulterior.\nCaractere valabile A - Z, 0 - 9 și _
#YMSE: Error when space name is empty
spaceNameEmptyValidationError=Introduceți nume de spațiu.
#YMSE: Error when ECN Business name is empty
ecnNameEmptyValidationError=Introduceți un nume comercial.
#YMSE: Error when space id is empty
spaceIDInputEmptyValidationError=Introduceți ID spațiu.
#YMSE: Error when space id contains invalid characters
spaceIDInputInValidError=Caractere nevalabile. Utilizați doar A - Z, 0 - 9 și _.
#YMSE: Error when space id exists while creation
spaceIDInputDuplicateError=ID spațiu există deja.
#XFLD: Space searchfield placeholder
search=Căutare
#XMSG: Success message after creating users
createUsersSuccess=Utilizatori adăugați
#XMSG: Success message after creating user
createUserSuccess=Utilizator adăugat
#XMSG: Success message after updating users
updateUsersSuccess={0} utilizatori actualizați
#XMSG: Success message after updating user
updateUserSuccess=Utilizator actualizat
#XMSG: Success message after removing users
removeUsersSuccess={0} utilizatori eliminați
#XMSG: Success message after removing user
removeUserSuccess=Utilizator eliminat
#XFLD: Schema name
schemaName=Nume schemă
#XFLD: used of total
ofTemplate={0} din {1}
#XFLD: Assigned disk label in space overview
assignedStorageTemplate=Disc alocat ({0} din {1})
#XFLD: Assigned Memory label in space overview
assignedRAMTemplate=Memorie alocată ({0} din {1})
#XFLD: Storage ratio on space
accelearationRAM=Accelerare memorie
#XFLD: No Storage Consumption
noStorageConsumptionText=Nicio cotă de stocare alocată.
#XFLD: Used disk label in space overview
usedStorageTemplate=Disc utilizat pentru stocare ({0} din {1})
#XFLD: Used Memory label in space overview
usedRAMTemplate=Memorie utilizată pentru stocare ({0} din {1})
#XFLD: Used Disk tooltip
usedStorageTooltipTemplate={0} din {1} disc utilizat pentru stocare
#XFLD: Used Memory tooltip
usedRAMTooltipTemplate={0} din {1} memorie utilizată
#XFLD: Assigned Disk tooltip
assignedStorageTooltipTemplate=Disc alocat: {0} din {1}
#XFLD: Assigned Memory tooltip
assignedRAMTooltipTemplate={0} din {1} memorie alocată
#XFLD: Space related used storage tooltip
spaceRelatedUsedStorageTooltipTemplate=Date spațiu: {0}
#XFLD: Not space related used storage tooltip
otherDataUsedStorageTooltipTemplate=Alte date: {0}
#XFLD: Storage used exceeded popover and tooltip
storageUsedExceededText=Luați în considerare extinderea planului dvs. sau contactați suportul SAP.
#XCOL: Space table-view column used Disk
usedStorage=Disc utilizat pentru stocare
#XCOL: Space monitor column used Memory
usedRAM=Memorie utilizată pentru stocare
#XCOL: Space monitor column Schema
tableSchema=Schemă
#XCOL: Space monitor column Storage Type
tableStorageType=Tip de stocare
#XCOL: Space monitor column Table Type
tableType=Tip tabel
#XCOL: Space monitor column Record Count
tableRecordCount=Număr de înregistrări
#XFLD: Assigned Disk
assignedStorage=Disc alocat pentru stocare
#XFLD: Assigned Memory
assignedRAM=Memorie alocată pentru stocare
#XCOL: Space table-view column storage utilization
tableStorageUtilization=Spațiu de stocare utilizat
#XFLD: space status
spaceStatus=Stare spațiu
#XFLD: space type
spaceType=Tip de spațiu
#XFLD: space type label for space with space type "SAP BW bridge" ("SAP BW bridge" is a product name, do not translate it)
abapbridgeSpaceType=SAP BW Bridge
#XFLD: space type label for space with space type "Data Marketplace"
marketplaceSpaceType=Produs furnizor de date
#XMSG: Error message for when selected space cannot be deleted due to the space type
deleteTypedSpaceError=Nu puteți șterge spațiul {0} deoarece tipul său de spațiu este {1}.
#XMSG: Error message for when selected spaces cannot be deleted due to the space type
deleteTypedSpacesError=Nu puteți șterge cele {0} spații selectate. Spațiile cu următoarele tipuri de spațiu nu pot fi șterse: {1}.
#XFLD: space type label for space without space type
defaultSpaceType=SAP Datasphere
#XFLD: Tooltip for monitor space button
monitorSpace=Monitorizare
#XFLD: Tooltip for edit space button
editSpace=Editare spațiu
#XMSG: Deletion warning in messagebox
deleteConfirmation=Sigur doriți să ștergeți acest spațiu?
#XFLD: Tooltip for delete space button
deleteSpace=Ștergere spațiu
#XFLD: storage
storage=Disc pentru stocare
#XFLD: username
userName=Nume utilizator
#XFLD: port
port=Port
#XFLD: hostname
hostName=Nume gazdă
#XFLD: password
password=Parolă
#XBUT: Request new password button
requestPassword=Solicitare parolă nouă
#YEXP: Usage explanation in time data section
timeDataSectionHint=Creați tabele de timp și dimensiuni pe care să le utilizați în modelele și relatările dvs.
#YEXP: Usage explanation in Space schema section
spaceSchemaHint=Doriți ca datele din spațiul dvs. să poată fi consumate de alte instrumente sau aplicații? Dacă da, creați unul sau mai mulți utilizatori care pot accesa datele din spațiul dvs. și selectați dacă doriți ca toate datele de spațiu viitoare să fie implicit consumabile.
#XTIT: Create schema popup title
createSchemaDialogTitle=Creare schemă Open SQL
#XTIT: Generate Time Data dialog title
generateTimeDataDialogTitle=Creare tabele de timp și dimensiuni
#XTIT: Edit Time Data dialog title
editTimeDataDialogTitle=Editare tabele de timp și dimensiuni
#XTIT: Time Data token title
timeDataTokenTitle=Date de timp
#XTIT: Time Data token title
timeDataUpdateViews=Actualizare imagini de date de timp
#XFLD: Time Data token creation in progress label
timeDataCreationInProgress=Creare în desfășurare...
#XFLD: Time Data token creation error label
timeDataCreationError=Creare nereușită. Reîncercați.
#XTIT: Time Data dialog time settings title
timeDataDialogTimeSettingsTitle=Setări tabel de timp
#XTIT: Time Data dialog time data table title
timeDataDialogTranslationTablesTitle=Tabele de conversie
#XTIT: Time Data dialog dimensions title
timeDataDialogTimeViewsTitle=Dimensiuni de timp
#XFLD: Time Data dialog time range label
timeRangeHint=Definire interval de timp.
#XFLD: Time Data dialog time data table label
timeDataHint=Dați un nume tabelului dvs.
#XFLD: Time Data dialog time data dimensions label
timeDimensionsHint=Dați un nume dimensiunilor dvs.
#XFLD: Time Data Time range description label
timerangeLabel=Interval de timp
#XFLD: Time Data dialog from year label
fromYearLabel=De la an
#XFLD: Time Data dialog to year label
toYearLabel=Până la an
#XFLD: Time Data dialog calendar type label
calendarTypeLabel=Tip de calendar
#XFLD: Time Data dialog granularity label
granularityLabel=Granularitate
#XFLD: Time Data dialog technical name label
technicalNameLabel=Nume tehnic
#XFLD: Time Data dialog quarters text table lable
quarterTextsTableLabel=Tabel de conversie pentru trimestre
#XFLD: Time Data dialog months text table lable
monthsTextsTableLabel=Tabel de conversie pentru luni
#XFLD: Time Data dialog days text table lable
daysTextsTableLabel=Tabel de conversie pentru zile
#XFLD: Time Data dialog year label
yearLabel=Dimensiune an
#XFLD: Time Data dialog quarter label
quarterLabel=Dimensiune trimestru
#XFLD: Time Data dialog month label
monthLabel=Dimensiune lună
#XFLD: Time Data dialog day label
dayLabel=Dimensiune zi
#XFLD: Time Data dialog gregorian calendar type label
gregorian=Gregorian
#XFLD: Time Data dialog time granularity day label
day=Zi
#XFLD: Time Data dialog business name maxlength reached
timeRangeValueStateTextMaxLengthBusinessName=Lungimea maximă de 1000 de caractere a fost atinsă.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextMaxRangeExceeded=Intervalul de timp maxim este de 150 de ani.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeLower=“De la an” trebuie să fie înainte de “Până la an”
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeHigher="De la an" trebuie să fie 1900 sau mai mult.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeHigher=“Până la an” trebuie să fie după “De la an”
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeLower=“Până la an” trebuie să fie mai puțin decât anul curent plus 100.
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningFromYear=Creșterea valorii pentru "De la an" poate duce la pierderi de date
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningToYear=Scăderea valorii pentru "Până la an" poate duce la pierderi de date
#XMSG: Time Data creation validation error message
timeDataValidationError=Se pare că unele câmpuri sunt nevalabile. Verificați câmpurile obligatorii pentru a continua.
#XMSG: Time Data deletion popup text
deleteTimeDataConfirmation=Sigur doriți să ștergeți aceste date?
#XMSG: Time Data creation success message
createTimeDataSuccess=Date de timp create
#XMSG: Time Data update success message
updateTimeDataSuccess=Date de timp actualizate
#XMSG: Time Data delete success message
deleteTimeDataSuccess=Date de timp șterse
#XMSG: Time Data creation error message
createTimeDataError=A apărut o problemă la încercare de creare date de timp.
#XMSG: Time Data update error message
updateTimeDataError=A apărut o problemă la încercare de actualizare date de timp.
#XMSG: Time Data creation error message
deleteTimeDataError=A apărut o problemă la încercare de ștergere date de timp.
#XMSG: Error message when time data could not be retrieved
retrievingTimeDataError=Date de timp nu au putut fi încărcate.
#XTIT: Time Data deletion popup title
deleteTimeDataConfirmationTitle=Avertizare
#XTXT: Time Data deletion dependency popup text
deleteTimeDataDependencyText=Nu am putut șterge datele dvs. de timp pentru că sunt utilizate în alte modele.
#XTXT: Time Data deletion dependency popup text singular
deleteTimeDataDependencyTextSingular=Nu am putut șterge datele dvs. de timp pentru că sunt utilizate în alt model.
#XMSG: Validation error when required time data form field is empty
timeDataRequiredField=Acest câmp este obligatoriu și nu poate fi lăsat gol.
#XFLD: Tooltip for Open in Database Explorer button
openInDBX=Deschidere în explorer de bază de date
#YMSE: Dimension Year
dimensionYearView=Dimensiune "An"
#YMSE: Dimension Year
dimensionQuarterView=Dimensiune "Trimestru"
#YMSE: Dimension Year
dimensionMonthView=Dimensiune "Lună"
#YMSE: Dimension Year
dimensionDayView=Dimensiune "Zi"
#XFLD: Time Data deletion object title
timeDataUsedIn=(utilizate în {0} modele)
#XFLD: Time Data deletion object title singular
timeDataUsedInSingular=(utilizate în 1 model)
#XFLD: Time Data deletion table column provider
provider=Furnizor
#XTIT: Time Data dependency dialog title
timeDataDependencyDialogTitle=Dependențe
#XTIT: Create schema popup title
createSpaceSchemaUserDialogTitle=Creare utilizator pentru schemă spațiu
#XFLD: Create schema button
createSchemaButton=Creare schemă Open SQL
#XFLD: Generate TimeData button
generateTimeDataButton=Creare tabele de timp și dimensiuni
#XFLD: Show dependencies button
showDependenciesButton=Afișare dependențe
#XFLD: tooltip for disabled buttons when user is not member of space
mustBeMemberOfSpaceForOperation=Pentru a efectua această operațiune, utilizatorul dvs. trebuie să fie un membru al spațiului.
#XFLD: Create space schema user button
createSpaceSchemaUserButton=Creare utilizator schemă de spațiu
#YMSE: API Schema users load error
loadSchemaUsersError=Lista de utilizatori nu a putut fi încărcată.
#XTIT: API Schema-config popup title
schemaUserDetailsDialogTitle=Detalii utilizator schemă de spațiu
#XMSG: API Schema user deletion popup text
deleteSpaceSchemaUserConfirmation=Sigur doriți să ștergeți utilizatorul selectat?
#XMSG: API Schema user deletion confirmation text
userDeleteSuccess=Utilizator șters.
#YMSE: API Schema user deletion error
userDeleteError=Utilizatorul nu a putut fi șters.
#XFLD: User deleted
userDeleted=Utilizatorul a fost șters.
#XTIT: Remove user popup title
removeUserConfirmationTitle=Avertizare
#XMSG: Remove user popup text
removeUserConfirmation=Sigur doriți să eliminați utilizatorul? Utilizatorul și rolurile sale în volum alocate vor fi eliminate din spațiu.
#XMSG: Remove users popup text
removeUsersConfirmation=Sigur doriți să eliminați utilizatorii? Utilizatorii și rolurile lor în volum alocate vor fi eliminate din spațiu.
#XBUT: Confirmation button for remove user dialog
removeUserConfirmationBtn=Eliminare
#YMSE: No data text for available roles
noDataAvailableRoles=Spațiul nu este adăugat la niciun rol în volum. \n Pentru a putea adăuga utilizatori la spațiu, acesta trebuie mai întâi adăugat la unul sau mai multe roluri în volum.
#YMSE: No data text for selected roles
noDataSelectedRoles=Niciun rol în volum selectat
#XTIT: Schema-config popup title
schemaDetailsDialogTitle=Detalii configurare schemă Open SQL
#XFLD: Label for Read Audit Log
auditLogRead=Activare jurnal de audit pentru operații de citire
#XFLD: Label for Change Audit Log
auditLogChange=Activare jurnal de audit pentru operații de modificare
#XFLD: Label Audit Log Retention
auditLogRetention=Păstrare jurnale timp de
#XFLD: Label Audit Log Retention Unit
retentionUnit=Zile
#YMSE: Error when log retention time is set incorrectly
incorrectRetentionTime=Introduceți un număr între cuprins între {0} și {1}
#XFLD: Label for Cross Space Schema Access
spaceSchemaAccess=Consumare date schemă de spațiu
#XTIT: Confirm deactivation of space schema consumption dialog title
disableSpaceSchemaConsumptionDialogTitle=Oprire consumare date schemă de spațiu
#XTXT: Confirm deactivation of space schema consumption dialog text
disableSpaceSchemaConsumptionDialogText=Această schemă Open SQL poate consuma datele schemei dvs. de spațiu. Dacă opriți consumul, modelele bazate pe datele schemei de spațiu pot să nu mai funcționeze.
#XBUT: Confirmation button for the stop consuming space schema data dialog
stopConsuming=Oprire consumare
#XTXT: Tooltip for data lake icon
dataLakeIconTooltip=Acest spațiu este utilizat pentru a accesa lacul de date
#XFLD: Tooltip for data lake table entry
dataLakeTableEntryTooltip=Lac de date activat
#XFLD: used In-Memory linit reached tag
usedRamLimitReachedLabel=Limită de memorie atinsă
#XFLD: Space storage Limit reached tag
storageLimitReachedLabel=Limită de stocare atinsă
#XFLD: Minimal Storage Limit Reached tag
minStorageLimitReachedLabel=Limită de stocare minimă atinsă
#XFLD: Space ram tag
ramLimitReachedLabel=Limită de memorie atinsă
#XFLD: Minimal In-Memory Limit Reached tag
minRamLimitReachedLabel=Limită de memorie internă minimă atinsă
#XFLD: Popover text after click on space storage limit warning
storageLimitReachedText=Ați atins limita de stocare a spațiului alocată de {0}. Alocați mai mult spațiu de stocare la spațiu.
#XFLD: System storage tag
systemStorageLimitReachedLabel=Limită de stocare de sistem atinsă
#XMSG: Popover text after click on storage limit warning
systemStorageLimitReachedText=Ați atins limita de stocare a sistemului de {0}. Acum nu puteți aloca mai mult spațiu de stocare la spațiu.
#XMSG: Schema deletion warning
deleteSchemaConfirmation=Ștergerea acestei scheme Open SQL va șterge definitiv și toate obiectele memorate și asocierile întreținute în schemă. Continuați?
#XMSG: Success message after deleting a schema
schemaDeleteSuccess=Schemă ștearsă
#YMSE: Error while deleting schema.
schemaDeleteError=Schema nu a putut fi ștearsă.
#XMSG: Success message after update a schema
schemaUpdateSuccess=Schemă actualizată
#YMSE: Error while updating schema.
schemaUpdateError=Schema nu a putut fi actualizată.
#XMSG: Hint when no password is shown in the dialog.
newPasswordHint=Am furnizat o parolă pentru această schemă. Dacă ați uitat parola sau dacă ați pierdut-o, puteți solicita una nouă. Nu uitați să copiați sau să salvați parola nouă.
#XMSG: Hint when showing the password in the dialog.
savePasswordHint=Vă rugăm să copiați parola dvs. Veți avea nevoie de aceasta pentru a configura o conexiune la această schemă. Dacă ați uitat parola, puteți deschide acest dialog pentru a o reseta.
#XFLD,50: Hint on entering schema name in creation dialog
schemaNameHint=Nume nu poate fi modificat după creare schemă.
#XFLD: Open SQL Schemas section sub headline
schemasSQL=Open SQL
#XFLD: Space schema section sub headline
schemasSpace=Spațiu
#XFLD: HDI Container section header
HDIContainers=Containere HDI
#XTXT: Add HDI Containers button tooltip
addHDIContainers=Adăugare containere HDI
#XTXT: Remove HDI Containers button tooltip
removeHDIContainers=Eliminare containere HDI
#XFLD: Enable HDI Containers feature button
enableHDIContainersButton=Activare acces
#YMSE: No data text for HDI Containers table
noDataHDIContainers=Niciun container HDI nu a fost adăugat.
#YMSE: No data text for Timedata section
noDataTimedata=Niciun tabel de timp și nicio dimensiune nu a fost creată.
#YMSE: No data text during runtime not available for Timedata section
runtimeNotAvailableNoDataTimedata=Imposibil de încărcat tabele de timp și dimensiuni deoarece baza de date cu timp de execuție nu este disponibilă.
#YMSE: No data text during runtime not available for HDI section
runtimeNotAvailableNoDataHDI=Imposibil de încărcat containere HDI deoarece baza de date cu timp de execuție nu este disponibilă.
#YMSE: Error while fetching HDI Containers
fetchHDIContainersError=Containere HDI nu au putut fi obținute. Reîncercați mai târziu.
#XFLD Table column header for HDI Container names
HDIContainerName=Nume container HDI
#XFLD: Enable HDI Containers feature dialog title
enableHDIContainersDialogTitle=Activare acces
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText1New=Puteți activa SAP SQL Data Warehousing în tenantul dvs. SAP Datasphere pentru a schimba date între containerele dvs. HDI și spațiile SAP Datasphere fără nevoia de deplasare a datelor.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText2=În acest scop, deschideți un tichet de suport efectuând click pe butonul de mai jos.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText3New=După ce tichetul dvs. a fost prelucrat, trebuie să creați cel puțin un container HDI nou în baza de date cu timp de execuție SAP Datasphere. Apoi, butonul Activare acces este înlocuit de butonul + în secțiunea de containere HDI pentru toate spațiile dvs. SAP Datasphere și puteți adăuga containere la un spațiu.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText41=Aveți nevoie de mai multe informații? Accesați %%0. Pentru informații detaliate despre ce trebuie să includeți în tichet, consultați %%1.
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkText=Ajutor SAP
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkTextSapNote3057059=Nota SAP 3057059
#XBUT: Open Ticket Button Text
openTicket=Deschidere tichet
#XBUT: Add Button Text
add=Adăugare
#XBUT: Next Button Text
next=Următor
#XBUT: Edit Button Text
editUsers=Editare
#XBUT: create user Button Text
createUser=Creare
#XBUT: Update user Button Text
updateUser=Selectare
#XTIT: Add HDI Containers Dialog Title
addHDIContainersDialogTitle=Adăugare containere HDI nealocate
#XFLD: No data text for add HDI container dialog
addHDIContainersDialogNoDataText=Nu am găsit containere nealocate. \n Containerul pe care îl căutați poate fi deja alocat la un spațiu.
#YMSE: Error while loading assigned HDI Containers
loadAssignedHDIContainersError=Containerele HDI alocate nu au putut fi încărcate.
#YMSE: Error while loading assigned HDI Containers
loadUnassignedHDIContainersError=Containerele HDI nu au putut fi încărcate.
#XMSG: Success message
succeededToAddHDIContainer=Container HDI adăugat
#XMSG: Success message
succeededToAddHDIContainerPlural=Containere HDI adăugate
#XMSG: Success message
succeededToDeleteHDIContainer=Container HDI eliminat
#XMSG: Success message
succeededToDeleteHDIContainerPlural=Containere HDI eliminate
#XFLD: Time data section sub headline
timeDataSection=Tabele de timp și dimensiuni
#XFLD: Schemas list entry read audit log enabled indicator
readIndicator=Citire
#XFLD: Schemas list entry change audit log enabled indicator
changeIndicator=Modificare
#XFLD: Remote sources section sub headline
allconnections=Alocare conexiune
#XFLD: Remote sources section sub headline
localconnections=Conexiuni locale
#XFLD: Remote sources section sub headline for open Connectors
openConnectors=SAP Open Connectors
#XFLD: User section sub headline
memberassignment=Alocare membru
#XFLD: User assignment section sub headline
userAssignment=Alocare utilizator
#XFLD: User section Access dropdown Member
member=Membru
#XFLD: User assignment section column name
user=Nume utilizator
#XTXT: Selected role count
selectedRoleToolbarText=Selectat: {0}
#XTIT: Space detail section connections title
detailsSectionConnections=Conexiuni
#XTIT: Space detail section data access title
detailsSectionDataAccess=Acces la schemă
#XTIT: Space detail section time data title
detailsSectionGenerateData=Date de timp
#XTIT: Space detail section members title
detailsSectionUsers=Membri
#XTIT: Space detail section Users title
detailsSectionUsersTitle=Utilizatori
#XTIT: Out of Storage
insufficientStoragePopoverTitle=Fără spațiu de stocare
#XTIT: Storage distribution
storageDistributionPopoverTitle=Stocare pe disc utilizată
#XTXT: Out of Storage popover text
insufficientStorageText=Pentru a crea un spațiu nou, reduceți stocarea alocată a altui spațiu sau ștergeți un spațiu de care nu mai aveți nevoie. Puteți crește spațiul de stocare total al sistemului apelând Gestionare plan.
#XMSG: Space id length warning
spaceIdLengthWarning=Număr maxim de {0} caractere a fost depășit.
#XMSG: Space name length warning
spaceNameLengthWarning=Număr maxim de {0} caractere a fost depășit.
#XMSG: Space id reserved prefix warning
spaceIdnWarningReservedPrefix=Nu utilizați prefixul {0} pentru a evita posibile conflicte.
#YMSE: Error while loading open SQL schemas
loadLocalSchemasError=Scheme Open SQL nu au putut fi încărcate.
#YMSE: Error while creating open SQL schema
createLocalSchemaError=Schemă Open SQL nu a putut fi creată.
#YMSE: Error while loading all remote connections
loadRemoteConnectionsError=Nu au putut fi încărcate toate conexiunile la distanță.
#YMSE: Error while loading space details
loadSpaceDetailsError=Detalii spațiu nu au putut fi încărcate.
#YMSE: Error while deploying space details
deploySpaceDetailsError=Spațiul nu a putut fi implementat.
#YMSE: Error while copying space details
copySpaceDetailsError=Spațiul nu a putut fi copiat.
#YMSE: Error while loading storage data
loadStorageDataError=Datele despre stocare nu au putut fi încărcate.
#YMSE: Error while loading all users
loadAllUsersError=Nu au putut fi încărcați toți utilizatorii.
#YMSE: Failed to reset password
resetPasswordError=Parola nu a putut fi resetată.
#XMSG: Success message after updating space
resetPasswordSuccessMessage=Parola nouă a fost setată cu succes pentru schemă
#YMSE: DP Agent-name too long
DBAgentNameError=Numele agentului de furnizare date este prea lung.
#YMSE: Schema-name not valid.
schemaNameError=Numele schemei este nevalabil.
#YMSE: User name not valid.
UserNameError=Nume de utilizator este nevalabil.
#XTIT: Consumption by Storage Type
consumptionStorageTypeText=Consum după tip de stocare
#XTIT: Consumption by Schema
consumptionSchemaText=Consum după schemă
#XTIT: Overall Consumption by schema
overallConsumptionSchemaText=Consum tabel global după schemă
#XTIT: Overall Consumption by Table Type
overallConsumptionTableTypeText=Consum global după tip de tabel
#XTIT: Tables
tableDetailsText=Detalii tabel
#XTIT: Table Storage Consumption
tableStorageConsumptionText=Consum stocare tabel
#XFLD: Table Type label
tableTypeLabel=Tip tabel
#XFLD: Schema label
schemaLabel=Schemă
#XFLD: reset table tooltip
resetTable=Resetare tabel
#XFLD: In-Memory label in space monitor
inMemoryLabel=Memorie
#XFLD: Disk label in space monitor
diskLabel=Disc
#XFLD: Yes
yesLabel=Da
#XFLD: No
noLabel=Nu
#XFLD: switch label: Want the data in this space to be consumable by default?
allowConsumptionByDefault=Doriți ca datele din acest spațiu să fie consumabile implicit?
#XFLD: Business Name
businessNameLabel=Nume comercial
#XFLD: Refresh
refresh=Împrospătare
#XMSG: No filter results title
noFilterResultsTitle=Se pare că setările dvs. de filtrare nu afișează date.
#XMSG: No filter results message
noFilterResultsMsg=Încercați să rafinați setările de filtrare și, dacă tot nu vedeți date, creați câteva tabele în generatorul de date. După ce vor consuma din spațiul de stocare, le veți putea monitoriza aici.
#XMSG: Space monitoring Hana state red title
spaceMonitorHanaRedTitle=Baza de date cu timp de execuție nu este disponibilă.
#XMSG: Space monitoring Hana state red message
spaceMonitorHanaRedMsg=Deoarece baza de date cu timp de execuție nu este disponibilă, unele caracteristici sunt dezactivate și nu putem afișa nicio informație pe această pagină.
#YMSE: Error while creating space schema user
createSpaceSchemaUserError=Utilizator schemă de spațiu nu a putut fi creat.
#YMSE: Error User name already exists
userAlreadyExistsError=Nume de utilizator există deja.
#YMSE: Error Authentication failed
authenticationFailedError=Autentificare nereușită.
#XMSG: Message strip message when a schema user is locked
userLockedMessageStrip=Utilizatorul este blocat din cauza prea multor încercări de conectare nereușite. Solicitați o parolă nouă pentru a debloca utilizatorul.
#XMSG: Success message after reset and unlock schema user
resetUnlockPasswordSuccessMessage=Parola nouă a fost setată și utilizatorul a fost deblocat
#XMSG: user is locked message
userLockedMessage=Utilizator este blocat.
#XCOL: Users table-view column Role
spaceRole=Rol
#XCOL: Users table-view column Scoped Role
spaceScopedRole=Rol în volum
#XCOL: Users table-view column Space Admin
spaceAdmin=Administrator spațiu
#XFLD: User section dropdown value Viewer
viewer=Vizualizator
#XFLD: User section dropdown value Modeler
modeler=Modelator
#XFLD: User section dropdown value Data Integrator
dataIntegrator=Integrator date
#XFLD: User section dropdown value Space Administrator
spaceAdministrator=Administrator spațiu
#XMSG: Success message -  Space Role updated successfully.
updateUserSpaceRoleSuccess=Rol spațiu actualizat
#XMSG: Error message - Failed to update Space Role.
updateUserSpaceRoleError=Rol spațiu nu a fost actualizat cu succes.
#XFLD:
databaseUserNameSuffix=Sufix nume utilizator de bază de date
#XTXT: Space Schema password text
spaceSchemaPasswordText=Pentru a configura o conexiune la această schemă, copiați parola dvs. În cazul în care ați uitat parola, puteți solicita oricând una nouă.
#XFLD: Cloud Platform label in space schema dialog
cloudPlatformLabel=Platforma cloud
#XTXT: Cloud Platform text in space schema dialog
cloudPlatformText=Pentru a configura accesul prin acest utilizator, activați consumul și copiați acreditările. În cazul în care puteți copia doar acreditările, fără parolă, asigurați-vă că adăugați parola mai târziu.
#XFLD: enable consumption in CloudPlatform label in space schema dialog
enableConsumptionInCloudPlatformLabel=Activare consum pe platforma cloud
#XFLD: Credentials for User-Provided Service label in space schema dialog
cloudPlatformCredentialsLabel=Acreditări pentru serviciul furnizat de utilizator:
#XFLD: Credentials for User-Provided Service (Without Password) label in space schema dialog
cloudPlatformCredentialsWithoutPasswordLabel=Acreditări pentru serviciul furnizat de utilizator (fără parolă):
#XFLD: Copy Credentials label in space schema dialog
copyCredentialsLabel=Copiere acreditări fără parolă
#XFLD: Copy Credentials label in space schema dialog
copyFullCredentialsLabel=Copiere acreditări complete
#XFLD: Copy Password label in space schema dialog
copyPasswordLabel=Copiere parolă
#XMSG: Credentials copied to clipboard
credentialsCopiedMessage=Acreditări copiate în clipboard
#XMSG: Password copied to clipboard
passwordCopiedMessage=Parolă copiată în clipboard
#XMSG: create database user dialog title
createDatabaseUserDialogTitle=Creare utilizator de bază de date
#XMSG: Database Users section title
databaseUsers=Utilizatori de bază de date
#XMSG: Database User Details dialog title
databaseUserDetailsDialogTitle=Detalii utilizator de bază de date
#XFLD: database user read audit log
databaseUserAuditLogRead=Activare jurnale de audit pentru operații de citire și păstrare jurnale timp de
#XFLD: database user change audit log
databaseUserAuditLogChange=Activare jurnale de audit pentru operații de modificare și păstrare jurnale timp de
#XMSG: Cloud Platform Access
cloudPlatformAccess=Acces la platforma cloud
#XFLD: Enable Consumption in Cloud Platform hint
enableConsumptionCloudPlatformHint=Configurați accesul la containerul HANA Deployment Infrastructure (HDI) prin acest utilizator de bază de date. Pentru a vă conecta la containerul HDI, trebuie să fie activată modelarea SQL
#XFLD: Enable HDI Consumption
enableConsumptionCloudPlatform=Activare consum HDI
#XFLD: Enable Consumption hint
enableConsumptionHint=Doriți ca datele din spațiul dvs. să poată fi consumate de alte instrumente sau aplicații?
#XFLD: Enable Consumption
enableConsumption=Activare consum SQL?
#XFLD: Enable Modeling
enableModeling=Activare modelare SQL
#XMSG: Privileges for Data Modeling
privilegesModeling=Ingestie de date
#XMSG: Privileges for Data Consumption
privilegesConsumption=Consum de date pentru instrumente externe
#XFLD: SQL Modeling
sqlModeling=Modelare SQL
#XFLD: SQL Consumption
sqlConsumption=Consum SQL
#XFLD: enabled
enabled=Activat
#XFLD: disabled
disabled=Dezactivat
#XFLD: Edit Privileges
editPrivileges=Editare privilegii
#XFLD: Open Database Explorer
openDBX=Deschidere explorer de bază de date
#XFLD: create database user hint
databaseCreateHint=Rețineți că nu vă veți mai putea schimba numele de utilizator după salvare.
#XFLD: Internal Schema Name
internalSchemaName=Nume schemă intern
#YMSE: Failed to load database users
loadDatabaseUserError=Eroare la încărcare utilizatori de bază de date
#YMSE: Failed to delete database user
deleteDatabaseUsersError=Eroare la ștergere utilizatori de bază de date
#XMSG: Success message after deleting database user
deleteDatabaseUserSuccess=Utilizator de bază de date șters
#XMSG: Success message after deleting database users
deleteDatabaseUsersSuccess=Utilizatori de bază de date șterși
#XMSG: Success message after creating database user
createDatabaseUserSuccessMessage=Utilizator de bază de date creat
#YMSE: Failed to create database user
createDatabaseUserError=Eroare la creare utilizator de bază de date
#XMSG: Success message after updating database user
updateDatabaseUserSuccessMessage=Utilizator de bază de date actualizat
#YMSE: Failed to update database user
updateDatabaseUserError=Eroare la actualizare utilizator de bază de date
#XFLD: HDI Consumption
hdiConsumption=Consum HDI
#XTIT: Space detail section Database Access title
detailsSectionDatabaseAccess=Acces la baza de date
#XTXT: Database users consumption hint
databaseUsersConsumptionHint=Setați ca datele spațiului să poată fi consumate implicit. Modelele din generatoare vor permite automat ca datele să poată fi consumate.
#XFLD: Default Consumption of Space Data:
defaultConsumptionOfSpaceData=Consum implicit de date de spațiu:
#XFLD: Database User Name
databaseUserName=Nume utilizator de bază de date
#XMSG: Database User creation validation error message
databaseUserValidationError=Se pare că unele câmpuri sunt nevalabile. Verificați câmpurile obligatorii pentru a continua.
#XMSG: SQL modeling can’t be enabled since this user has been migrated.
migratedDatabaseUserHint=Ingestia de date nu poate fi activată deoarece utilizatorul a fost migrat.
#XBUT: Remove Button Text
remove=Eliminare
#XBUT: Remove Spaces Button Text
removeSpaces=Eliminare spații
#XBUT: Remove Objects Button Text
removeObjects=Eliminare obiecte
#XMSG: No members have been added yet.
noMembersAssigned=Niciun membru nu a fost încă adăugat.
#XMSG: No users have been added yet.
noUsersAssigned=Niciun utilizator nu a fost încă adăugat.
#XMSG: No database users have been created or your filter is not showing any data.
noDatabaseUsers=Niciun utilizator de bază de date nu a fost creat sau filtrul dvs. nu afișează date.
#XMSG: Please enter a user name.
noDatabaseUsername=Introduceți un nume de utilizator.
#XMSG: The user name is too long. Please use a shorter one.
databaseUsernameTooLong=Numele de utilizator este prea lung. Utilizați unul mai scurt.
#XMSG: No privileges have been enabled and this database user will have limited functionality. Do you still want to continue?
noPrivilegesConfirmationMessage=Nu au fost activate privilegii și acest utilizator de bază de date va avea funcționalitate limitată. Mai doriți să continuați?
#XMSG: To enable audit logs for change operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogChangeMessage=Pentru a activa jurnalele de audit pentru operațiile de modificare, ingestia de date trebuie, de asemenea, activată. Doriți să efectuați acest lucru?
#XMSG: To enable audit logs for read operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogReadMessage=Pentru a activa jurnalele de audit pentru operațiile de citire, ingestia de date trebuie, de asemenea, activată. Doriți să efectuați acest lucru?
#XMSG: To enable HDI consumption data ingestion and data consumption needs to be enabled. Do you want to enable all?
enableHDIConsumptionMessage=Pentru a activa consumul HDI, ingestia de date și consumul de date trebuie, de asemenea, activate. Doriți să efectuați acest lucru?
#XMSG:
databaseUserPasswordText=Pentru a configura o conexiune la acest utilizator de bază de date, copiați parola dvs. În cazul în care ați uitat parola, puteți solicita oricând una nouă.
#XTIT: Space detail section members title
detailsSectionMembers=Membri
#XMSG: New password set
newPasswordSet=Parolă nouă setată
#XFLD: Data Ingestion
dataIngestion=Ingestie de date
#XFLD: Data Consumption
dataConsumption=Consum de date
#XFLD: Privileges
privileges=Privilegii
#XFLD: Enable Data ingestion
enableDataIngestion=Activare ingestie de date
#XMSG: Log the read and change operations for data ingestion.
databaseUserAuditHint=Jurnalizați operațiile de citire și modificare pentru ingestia de date.
#XMSG: Make your space data available in your HDI containers.
databaseUserHDIHint=Puneți la dispoziție datele spațiului în containerele dvs. HDI.
#XFLD: Enable Data consumption
enableDataConsumption=Activare consum de date
#XMSG: Allow other apps or tools to consume your space data.
enableDataConsumptionHint=Permiteți altor aplicații sau instrumente să consume datele spațiului dvs.
#XMSG: To set up access via this schema user copy the credentials to your user-provided service. In case you can only copy the credentials without a password make sure to add the password later.
databaseUserHDIConsumptionHint=Pentru a configura accesul prin acest utilizator de bază de date, copiați acreditările în serviciul furnizat de utilizator. În cazul în care puteți copia doar acreditările, fără parolă, asigurați-vă că adăugați parola mai târziu.
#XFLD: Data Flow Runtime Capacity label in space overview
dataFlowRuntimeCapacity=Capacitate timp de execuție flux de date ({0}:{1} ore din {2} ore)
#XFLD: Could not load Data Flow Runtime Capacity
dataFlowRuntimeCapacityError=Capacitate timp de execuție flux de date nu a putut fi încărcată
#XMSG: User can grant data consumption to other users.
databaseUserConsumptionWithGrantOptionHint=Utilizatorul poate permite consumul de date de către alți utilizatori.
#XFLD: Enable data consumption with grant option
enableConsumptionWithGrantOption=Activare consum de date cu opțiune grant
#XMSG: To enable data consumption with grant option data consumption needs to be enabled. Do you want to enable both?
enableConsumptionWithGrantMessage=Pentru a activa consumul de date cu opțiunea grant, consumul de date trebuie activat. Doriți să le activați pe ambele?
#XFLD: Enable Automated Predictive Library (APL) and Predictive Analysis Library (PAL)
allowUsingScriptServerFunctionality=Activare Automated Predictive Library (APL) și Predictive Analysis Library (PAL)
#XMSG: User can use SAP HANA Cloud embedded machine learning functions.
allowUsingScriptServerFunctionalityHint=Utilizatorul poate utiliza funcțiile de învățare automată integrate ale SAP HANA Cloud.
#XFLD: Password Policy
passwordPolicy=Politica pentru parole
#XMSG: Password Policy
passwordPolicyHint=Activați sau dezactivați aici politica pentru parole configurată.
#XFLD: Enable Password Policy
enablePasswordPolicy=Activare politică pentru parole
#XMSG: Read Access to the Space Schema
readAccessTitle=Acces în citire la schemă de spațiu
#XMSG: read access hint
readAccessHint=Permiteți-i utilizatorului de bază de date să conecteze instrumentele externe la schema de spațiu și să citească imaginile care sunt expuse pentru consum.
#XFLD: Space Schema
spaceSchema=Schemă de spațiu
#XFLD: Enable Read Access (SQL)
enableReadAccess=Activare acces în citire (SQL)
#XMSG: Allow the user to grant read access to other users.
withGrantOptionHint=Permiteți-i utilizatorului să acorde acces în citire la alți utilizatori.
#XFLD: With Grant Option
withGrantOption=Cu opțiune grant
#XMSG: Make your space data available in your HDI containers.
enableHDIConsumptionHint=Puneți la dispoziție datele spațiului în containerele dvs. HDI.
#XFLD: Enable HDI Consumption
enableHDIConsumption=Activare consum HDI
#XMSG: Write Access to the User’s Open SQL Schema
writeAccessTitle=Acces în scriere la schema Open SQL a utilizatorului
#XMSG: write access hint
writeAccessHint=Permiteți-i utilizatorului de bază de date să conecteze instrumentele externe la schema Open SQL a utilizatorului pentru a crea entități de date și a introduce date pentru utilizarea în spațiu.
#XFLD: Open SQL Schema
openSQLSchema=Schemă Open SQL
#XFLD: Enable Write Access (SQL DDL & DML)
enableWriteAccess=Activare acces în scriere (SQL, DDL și DML)
#XMSG: audit hint
auditHint=Jurnalizați operațiile de citire și modificare în schema Open SQL.
#XMSG: data consumption hint
dataConsumptionHint=Expuneți toate imaginile noi în spațiu implicit pentru consum. Modelatorii pot înlocui această setare pentru imagini individuale prin comutatorul “Expunere pentru consum” în panoul lateral de ieșire. De asemenea, puteți alege formatele în care sunt expuse imaginile.
#XFLD: Expose for Consumption by Default
exposeDataConsumption=Expunere pentru consum implicit
#XMSG: database users hint consumption hint
databaseUsersHint2New=Creați utilizatori de bază de date pentru a conecta instrumentele externe la SAP Datasphere. Setați privilegii pentru a le permite utilizatorilor să citească date de spațiu, să creeze entități de date (DDL) și să introducă datele (DML) pentru a fi utilizate în spațiu.
#XFLD: Read
read=Citire
#XFLD: Read (HDI)
readHDI=Citire (HDI)
#XFLD: Write
write=Scriere
#XMSG: HDI Containers Hint
HDIContainersHint2=Activați accesul la containerele dvs. SAP HANA Deployment Infrastructure (HDI) din spațiul dvs. Modelatorii pot utiliza artefactele HDI ca surse pentru imagini și clienții HDI pot accesa datele spațiului dvs.
#XMSG: Open info dialog
openDatabaseUserInfoDialog=Deschideți dialogul de informații
#XMSG: Database user is locked. Open info dialog to unlock
openLockedDatabaseUserInfoDialog=Utilizatorul de bază de date este blocat. Pentru a-l debloca, deschideți dialogul
#XFLD: Table
table=Tabel
#XFLD: Space section partner configuration title
partnerConnectionConfigurationSectionTitle=Conexiune partener
#XFLD: Space section partner configuration subtitle
partnerConnectionConfigurationSectionSubTitle=Configurare conexiune partener
#XFLD: Space section partner configuration hint
partnerConnectionConfigurationSectionHint=Definiți propriul dvs. mozaic de conexiune partener adăugând iconul și URL-ul iFrame. Această configurare este disponibilă doar pentru acest tenant.
#XFLD: Table Name Field
partnerConnectionConfigurationName=Nume mozaic
#XFLD: Table iFrame URL field
partnerConnectionConfigurationiFrameURL=URL iFrame
#XFLD: Table iFrame Post Message Origin field
partnerConnectionConfigurationiFramePostMessageOrigin=Origine postMessage iFrame
#XFLD: Table icon/image name
partnerConnectionConfigurationIcon=Icon
#XMSG: Table no result found
partnerConnectionConfigurationNotfound=Nicio configurare de conexiune partener nu a putut fi găsită.
#XMSG: Partner Connection Confgurations are not available
partnerConnectionConfigurationNotAvailable=Configurările conexiunii partener nu pot fi afișate când baza de date a timpului de execuție este indisponibilă.
#XFLD: Dialog Create Partner Connection Configuration
partnerConnectionConfigurationDialogTitleAdd=Creare configurare de conexiune partener
#XFLD: Dialog Create Partner Connection Configuration Upload Icon
partnerConnectionConfigurationButtonUloadPartnerIcon=Încărcare icon
#XFLD: Dialog Create Partner Connection Configuration Select
partnerConnectionConfigurationSelectIcon=Selectare (dimensiune maximă 200 KB)
#XFLD: Dialog Create Partner Connection Configuration Enter a name
partnerConnectionConfigurationNamePlaceholder=Exemplu de mozaic partener
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL
partnerConnectionConfigurationiFrameURLPlaceholder=http://www.example.com
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL Check
partnerConnectionConfigurationiFramePostMessageOriginPlaceholder=.example.com
#XFLD: Dialog Create Partner Connection Configuration Browse
partnerConnectionConfigurationBrowseButton=Răsfoire
#XFLD: Create Partner Connection Configuration Msg
partnerConnectionConfigurationUploadMsg=Configurare conexiune partener a fost creată cu succes.
#XMSG: Error while deleting Partner Connection configuration(s)
partnerConnectionConfigurationDeleteError=A apărut o eroare la ștergere configurări de conexiune partener.
#XMSG: Partner Connection Configuration has been successfully deleted
partnerConnectionConfigurationDeleteSuccess=Configurare conexiune partener a fost ștearsă cu succes.
#XMSG: Error while retrieving Partner Connection Configurations
partnerConnectionConfigurationLoadError=A apărut o eroare la regăsire configurări de conexiune partener.
#XMSG: Error fileSizeLimitExceeded
partnerConnectionConfigurationfileSizeLimitExceeded=Fișierul nu a putut fi încărcat deoarece depășește dimensiunea maximă de 200 KB.
#XBUT: Create Partner Connection Configuration
partnerConnectionConfigurationButtonCreate=Creare configurare de conexiune partener
#XBUT: Delete Partner Connection Configuration
partnerConnectionConfigurationButtonDelete=Ștergeți configurarea de conexiune partener.
#XMSG: Partner Tile Creation Failure
upsertPartnerTileFailed=Mozaic partener nu a putut fi creat.
#XMSG: Partner Tile Deletion Failure
deletePartnerTileFailed=Mozaic partener nu a putut fi șters.
#XMSG: HANA cloud connector settings reset failure
resetHANASCCSettingsFailed=Resetarea setărilor pentru conectorul cloud SAP HANA de client a eșuat
#XFLD: Workload Class
workloadClass=Clasă încărcare de lucru
#XFLD: Workload Management
workloadManagement=Gestiune încărcare de lucru
#XFLD: Priority
workloadClassPriority=Prioritate
#XMSG:
workloadManagementPriorityHint=Puteți specifica prioritizarea acestui spațiu la interogarea bazei de date. Introduceți o valoare de la 1 (prioritate minimă) până la 8 (prioritate maximă). Într-o situație în care spațiile concurează pentru threaduri disponibile, cele cu priorități mai mari sunt executate înaintea spațiilor cu priorități mai mici.
#XMSG:
workloadClassPriorityHint=Puteți specifica prioritatea spațiului de la 0 (minimă) la 8 (maximă). Instrucțiunile unui spațiu cu prioritate maximă sunt executate înainte de instrucțiunile altor spații cu prioritate minimă. Prioritatea implicită este 5. Deoarece valoarea 9 este rezervată operațiilor de sistem, nu este disponibilă pentru un spațiu.
#XFLD: Statement Limits
workloadclassStatementLimits=Limite de instrucțiuni
#XFLD: Workload Configuration
workloadConfiguration=Configurare încărcare de lucru
#XMSG:
workloadClassStatementLimitsHint=Puteți specifica numărul maxim (sau procentajul) de threaduri și GB de memorie pe care le pot consuma instrucțiunile executate simultan în spațiul de lucru. Puteți introduce orice valoare sau procentaj între 0 (nicio limită) și memoria și threadurile totale disponibile în tenant. \n\n Dacă specificați o limită de threaduri, rețineți că vă poate reduce performanța. \n\n Dacă specificați o limită de memorie, instrucțiunile care ating limita de memorie nu sunt executate.
#XMSG:
workloadClassStatementLimitsDescription=Configurarea implicită oferă limite de resursă generoase, împiedicând ca un spațiu individual să supraîncarce sistemul.
#XMSG:
workloadClassStatementLimitCustomDescription=Puteți seta limite de memorie și threaduri totale maxime pe care le pot consuma instrucțiunile care sunt executate simultan în spațiu.
#XMSG:
totalStatementThreadLimitHelpText=Setarea unei limite de threaduri prea mică poate afecta performanța instrucțiunilor, în timp ce valorile excesiv de ridicate sau 0 îi pot permite spațiului să consume toate threadurile de sistem disponibile.
#XMSG:
totalStatementMemoryLimitHelpText=Setarea unei limite de memorie prea mică poate cauza probleme de lipsă de memorie, în timp ce valorile excesiv de ridicate sau 0 îi pot permite spațiului să consume toată memoria de sistem disponibilă.
#XMSG:
totalStatementThreadLimitHelpTextRestricted=Introduceți un procentaj între 1% și 70% (sau numărul echivalent) din numărul total de threaduri disponibile în tenantul dvs. Setarea limitei de threaduri prea mici poate afecta performanța instrucțiunilor, în timp ce valorile extrem de ridicate pot afecta performanța instrucțiunilor din celelalte spații.
#XMSG:
totalStatementThreadLimitHelpTextDynamic=Introduceți un procentaj între 1% și {0}% (sau numărul echivalent) din numărul total de threaduri disponibile în tenantul dvs. Setarea limitei de threaduri prea mici poate afecta performanța instrucțiunilor, în timp ce valorile extrem de ridicate pot afecta performanța instrucțiunilor din celelalte spații.
#XMSG:
totalStatementMemoryLimitHelpTextNew=Introduceți o valoare sau un procentaj între 0 (nicio limită) și valoarea totală a memoriei disponibile în tenantul dvs. Setarea limitei de memorie prea mici poate afecta performanța instrucțiunilor, în timp ce valorile extrem de ridicate pot afecta performanța instrucțiunilor din celelalte spații.
#XFLD: Total Statement Thread Limit
totalStatementThreadLimit=Limită totală de threaduri de instrucțiuni
#XFLD: Segmented button label
workloadclassPercentage=%
#XFLD: Segmented button label
workloadclassGigaByte=GB
#XFLD, 80: Segmented button label
workloadclassThreads=Threaduri
#XFLD: Total Statement Memory Limit
totalStatementMemoryLimit=Limită totală de memorie de instrucțiuni
#YMSE: Error while loading customer hana info
loadCustomerHanaInfoError=Eroare la încărcare informații SAP HANA de client.
#XMSG:
minimumLimitReached=Limită minimă atinsă.
#XMSG:
maximumLimitReached=Limită maximă atinsă.
#XMSG: Name Taken for Technical Name
technical-name-taken=O conexiune cu numele tehnic pe care l-ați introdus există deja. Introduceți un alt nume.
#XMSG: Name Too long for Technical Name
technical-name-too-long=Numele tehnic pe care l-ați introdus depășește 40 de caractere. Introduceți un nume cu mai puține caractere.
#XMSG: Technical name field empty
technical-name-field-empty=Introduceți un nume tehnic.
#XMSG: Generic error when user enters invalid characters for a field
field-invalid-characters=Puteți utiliza doar litere (a-z), cifre (0-9) și underscore (_) pentru nume.
#XMSG: Generic error when user enters invalid initial characters for a field
field-underscore-trim=Numele pe care îl introduceți nu poate începe și nu se poate termina cu underscore (_).
#XFLD: Checkbox label for Enable Statement Limits
enableStatementLimits=Activare limite de instrucțiuni
#XFLD: Connection SubSection Settings Title in space details
connectionSettings=Setări
#XMSG: Connections tool hint in Space details section
connectionsToolHint=Pentru a crea sau edita conexiuni, deschideți aplicația Conexiuni din navigarea laterală sau efectuați click aici:
#XBUT: Connection list Open Connection Tool Button
button-connectiontool=Salt la Conexiuni
#XFLD: Not deployed label on space tile
notDeployedLabel=Spațiul nu a fost încă implementat.
#XFLD: Not deployed additional text on space tile
notDeployedText=Implementați spațiul.
#XFLD: Corrupt space label on space tile
corruptSpace=A apărut o problemă.
#XFLD: Corrupt space additional text on space tile
corruptSpaceHint=Încercați să reimplementați sau contactați suportul
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Date jurnal de audit
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Date administrative
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Alte date
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Date în spații
#XMSG: Unlock Space confirmation text
unlockSpaceConfirmation=Sigur doriți să deblocați spațiul?
#XMSG: Lock Space confirmation text
lockSpaceConfirmation=Sigur doriți să blocați spațiul?
#XFLD: Lock
lock=Blocare
#XFLD: Unlock
unlock=Deblocare
#XFLD: Locking
locking=Blocare în curs
#XMSG: Success message after locking space
lockSpaceSuccessMsg=Spațiu blocat
#XMSG: Success message after unlocking space
unlockSpaceSuccessMsg=Spațiu deblocat
#XMSG: Success message after locking spaces
lockSpacesSuccessMsg=Spații blocate
#XMSG: Success message after unlocking spaces
unlockSpacesSuccessMsg=Spații deblocate
#YMSE: Error while locking a space
lockSpaceError=Spațiul nu poate fi blocat.
#YMSE: Error while unlocking a space
unlockSpaceError=Spațiul nu poate fi deblocat.
#XTIT: popup title Warning
confirmationWarningTitle=Avertizare
#XMSG: Message strip message when a space was locked manually
manualLockedMessage=Spațiul a fost blocat manual.
#XMSG: Message strip message when a space was locked due to the amount of generated audit logs
auditLogLockedMessage=Spațiul a fost blocat de către sistem, deoarece jurnalele de audit consumă o cantitate mare de GB pe disc.
#XMSG: Message strip message when a space was locked because the assigned quota was exceeded.
quotaLockedMessage=Spațiul a fost blocat de către sistem, deoarece depășește alocările sale de memorie sau spațiu de stocare pe disc.
#XMSG: Unlock Spaces confirmation text
unlockSpacesConfirmation=Sigur doriți să deblocați spațiile selectate?
#XMSG: Lock Spaces confirmation text
lockSpacesConfirmation=Sigur doriți să blocați spațiile selectate?
#XTIT: Scoped role editor dialog title
scopedRoleEditorDialogTitle=Editor de roluri în volum
#XTIT: ECN Management title
ecnManagementTitle=Gestiune spații și noduri de calcul elastice
#XFLD: ECNs
ecns=Noduri de calcul elastice
#XFLD: ECN phase Ready
ecnReady=Pregătit
#XFLD: ECN phase Running
ecnRunning=În execuție
#XFLD: ECN phase Initial
ecnInitial=Nepregătit
#XFLD: ECN phase Starting
ecnStarting=În curs de începere
#XFLD: ECN phase Starting Failed
ecnStartingFailed=Lansare nereușită
#XFLD: ECN phase Stopping
ecnStopping=În curs de oprire
#XFLD: ECN phase Stopping Failed
ecnStoppingFailed=Oprire nereușită
#XBTN: Assign Button
assign=Alocare spații
#XBTN: Start Header-Button
start=Începere
#XBTN: Update Header-Button
repair=Actualizare
#XBTN: Stop Header-Button
stop=Oprire
#XFLD: ECN hours remaining
ecnHoursRemaining=1000 de ore rămase
#XFLD: ECN block-hours remaining
ecnBlockHoursRemaining={0} ore de bloc rămase
#XFLD: ECN block-hour remaining
ecnBlockHourRemaining={0} oră de bloc rămasă
#XTIT: ECN creation dialog title
ecnCreationDialogTitle=Creare nod de calcul elastic
#XTIT: ECN edit dialog title
ecnEditDialogTitle=Editare nod de calcul elastic
#XTIT: ECN deletion dialog title
ecnDeletionDialogTitle=Ștergere nod de calcul elastic
#XTIT: ECN Space assignment dialog title
ecnSpaceAssignmentDialogTitle=Alocare spații
#XFLD: ECN ID
ECNIDLabel=Nod de calcul elastic
#XTXT: Selected toolbar text
selectedToolbarText=Selectat: {0}
#XTIT: Elastic Compute Nodes
ECNslong=Noduri de calcul elastice
#XTIT: Object assignment - Table header: Objects available
objectsAvailable=Număr de obiecte
#XTIT: Object assignment - Dialog header text
selectObjects=Selectați spațiile și obiectele pe care doriți să le alocați la nodul dvs. de calcul elastic:
#XTIT: Object assignment - Table header title: Objects
objects=Obiecte
#XTIT: Object assignment - Table header: Type
type=Tip
#XMSG: Message strip delete database user audit warning
deleteDatabaseUserWarningMessageStrip=Rețineți că ștergerea unui utilizator de bază de date va conduce la ștergerea tuturor intrărilor de jurnal de audit generate. Dacă doriți să păstrați jurnalele de audit, luați în considerare exportarea acestora înainte de a șterge utilizatorul de bază de date.
#XMSG: Message strip delete HDI container audit logs impact
deleteHDIContainerWarningMessageStrip=Rețineți că anularea alocării unui container HDI din spațiu va conduce la ștergerea tuturor intrărilor sale de jurnal de audit generate. Dacă doriți să păstrați jurnalele de audit, luați în considerare exportarea acestora înainte de a anula alocarea containerului HDI.
#XTXT: All audit logs
allAuditLogs=Toate intrările jurnalului de audit generate pentru spațiu
#XMSG: Message strip disable audit warning
disableAuditWarningMessageStrip=Rețineți că dezactivarea unei politici de audit (operații de citire sau modificare) va conduce la ștergerea tuturor intrărilor sale de jurnal de audit. Dacă doriți să păstrați intrările jurnalului de audit, luați în considerare exportarea acestora înainte de a dezactiva politica de audit.
#XTIT: No Spaces assigned to ECN Illustration title
noSpacesECNTitle=Nu este alocat niciun spațiu sau obiect deocamdată
#XTIT: No Spaces assigned to ECN Illustration description
noSpacesECNDescription=Pentru a începe să lucrați cu nodul dvs. de calcul elastic, alocați un spațiu sau obiecte la acesta.
#XTIT: No Spaces Illustration title
noSpacesTitle=Niciun spațiu creat încă
#XTIT: No Spaces Illustration description
noSpacesDescription=Pentru a începe să achiziționați date, creați un spațiu.
#XTIT: No Spaces Illustration title
noDeletedSpacesTitle=Coșul de gunoi este gol
#XTIT: No Spaces Illustration description
noDeletedSpacesDescription=Puteți să vă recuperați spațiile șterse de aici.
#XTXT:  {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDatabaseUsers=După ce spațiul este implementat, următorii utilizatori de bază de date vor fi șterși {0} și nu mai pot fi recuperați:
#XTIT: Delete database users
deleteDatabaseUsersTitle=Ștergere utilizatori de bază de date
#YMSE: Error if ecn id already exists
ecnIDInputDuplicateError=ID-ul există deja.
#YMSE: Error if ecn id contains invalid characters
ecnIDInputInvalidError=Utilizați doar caractere minuscule de la a la z și numere de la 0 la 9.
#YMSE: Error if ecn id is too short
ecnIDInputTooShortError=ID-ul trebuie să aibă o lungime de cel puțin {0} caractere.
#XMSG: ecn id length warning
ecnIdLengthWarning=Număr maxim de {0} caractere a fost depășit.
#XFLD: open System Monitor
systemMonitor=Monitor sistem
#XFLD: open ECN schedule dialog menu entry
schedule=Programare
#XFLD: open create ECN schedule dialog
createSchedule=Creare programare
#XFLD: open change ECN schedule dialog
changeSchedule=Editare programare
#XFLD: open delete ECN schedule dialog
deleteSchedule=Ștergere programare
#XFLD: open self assign ECN schedule dialog
selfAssignSchedule=Alocare programare la mine
#XFLD: open pause ECN schedule dialog
pauseSchedule=Suspendare programare
#XFLD: open resume ECN schedule dialog
resumeSchedule=Reluare programare
#XFLD: View Logs
viewLogs=Vizualizare jurnale
#XFLD: Compute Blocks
computeBlocks=Blocuri de calcule
#XFLD: Memory label in ECN creation dialog
ecnMemory=Memorie (GB)
#XFLD: Storage label in ECN creation dialog
ecnStorage=Spațiu de stocare (GB)
#XFLD: Number of CPU label in ECN creation dialog
ecnCPUCount=Număr CPU-uri
#XFLD: ECN updated by label
changedBy=Modificat de
#XFLD: ECN updated on label
changedOn=Modificat pe
#XMSG: Success message after creating a Elastic Compute Node
createEcnSuccessMessage=Nod de calcul elastic creat
#YMSE: Error while creating a Elastic Compute Node
createEcnError=Nod de calcul elastic nu a putut fi creat
#XMSG: Success message after updating a Elastic Compute Node
updateEcnSuccessMessage=Nod de calcul elastic actualizat
#XMSG: Error while updating a Elastic Compute Node
udpateEcnError=Nod de calcul elastic nu a putut fi actualizat
#XMSG: Success message after deleting a Elastic Compute Node
deleteEcnSuccessMessage=Nod de calcul elastic șters
#YMSE: Error while deleting a Elastic Compute Node
deleteEcnError=Nod de calcul elastic nu a putut fi șters
#XMSG: Success message after starting a Elastic Compute Node
startingEcnSuccessMessage=Lansare nod de calcul elastic...
#XMSG: Success message after stopping a Elastic Compute Node
stoppingEcnSuccessMessage=Oprire nod de calcul elastic...
#YMSE: Error while starting a Elastic Compute Node
startingEcnError=Nod de calcul elastic nu a putut fi lansat
#YMSE: Error while stopping a Elastic Compute Node
stoppingEcnError=Nod de calcul elastic nu a putut fi oprit
#XBUT: Add Object button for an ECN
assignObjects=Adăugare obiecte
#XFLD: Enable auto object assignment label
enableAutoObjAssignment=Alocare toate obiectele automat
#XFLD: object type label to be assigned
objectTypeLabel=Tip (utilizare semantică)
#XFLD: assigned object type label
assignedObjectTypeLabel=Tip
#XFLD: technical name label
TechnicalNameLabel=Nume tehnic
#XFLD: Object selecttion ECN label
objectSelectionECNLabel=Selectați obiectele pe care doriți să le adăugați la nodul de calcul elastic
#XTIT: Add objects dialog title
assignObjectsTitle=Alocare obiecte pentru
#XFLD: object label with object count
objectLabel=Obiect
#XMSG: No objects available to add message.
noObjectsToAssign=Niciun obiect disponibil pentru alocare.
#XMSG: No objects assigned message.
noAssignedObjects=Niciun obiect alocat.
#XTIT: Remove objects popup title
removeObjectsOrSpacesConfirmationTitle=Avertizare
#XTIT: Delete ECN popup button and title
deleteECNConfirmationButtonAndTitle=Ștergere
#XMSG: Remove objects popup text
removeObjectsConfirmation=Sigur doriți să eliminați obiectele selectate?
#XMSG: Remove spaces popup text
removeSpacesConfirmation=Sigur doriți să eliminați spațiile selectate?
#XBUT: Confirmation button for objects dialog
removeObjectsOrSpacesConfirmationBtn=Eliminare spații
#XMSG: Success message after removing objects
removeObjectsSuccessMessage=Obiectele expuse au fost eliminate
#XMSG: Success message after assigning objects
assignObjectsSuccessMessage=Obiectele expuse au fost alocate
#XFLD: auto object assignment label for space tile
autoObjAssignmentLabel=Toate obiectele expuse
#XFLD: Spaces tab label
spacesTabLabel=Spații
#XFLD: Exposed objects tab label
consumptionObjectsTabLabel=Obiecte expuse
#XMSG: Success message after unassigning spaces
spacesUnassignedSuccessMessage=Spațiile au fost eliminate
#XMSG: Success message after unassigning space
spaceUnassignedSuccessMessage=Spațiul a fost eliminat
#YMSE: Error while assigning / unassigning spaces
spacesAssignUnassignError=Spațiile nu au putut fi alocate sau eliminate.
#YMSE: Error while removing objects
removeObjectsError=Nu am putut aloca sau elimina obiectele.
#YMSE: Error while removing object
removeObjectError=Nu am putut aloca sau elimina obiectul.
#YMSE: Error when saving the ecn with invalid value
invalidSizingPlanError=Numărul selectat anterior nu mai este valabil. Selectați un număr valabil.
#YMSE: Error when saving the ecn with invalid value
invalidPerformanceClassError=Selectați o clasă de performanță valabilă.
#YMSE: Error when saving the ecn with invalid value
performanceClassNotAvailableError=Clasa de performanță selectată anterior "{0}" nu este valabilă în prezent. Selectați clasa de performanță valabilă.
#XTXT: Delete ECN confirmation text
deleteConfirmationTxtECN=Sigur doriți să ștergeți nodul de calcul elastic?
#XFLD: tooltip for ? button
help=Ajutor
#XFLD: ECN edit button label
editECN=Configurare
#XFLD: Technical type label for ERModel
DWC_ERMODEL=Model entitate - relație
#XFLD: Technical type label for Local Table
DWC_LOCAL_TABLE=Tabel local
#XFLD: Technical type label for Remote Table
DWC_REMOTE_TABLE=Tabel la distanță
#XFLD: Technical type label for Analytic Model
DWC_ANALYTIC_MODEL=Model analitic
#XFLD: Technical type label for Task Chain
DWC_TASKCHAIN=Lanț de sarcini
#XFLD: Technical type label for Data Flow
DWC_DATAFLOW=Flux de date
#XFLD: Technical type label for Replication Flow
DWC_REPLICATIONFLOW=Flux de replicare
#XFLD: Technical type label for Transformation Flow
DWC_TRANSFORMATIONFLOW=Flux de transformare
#XFLD: Technical type label for Intelligent Lookup
DWC_IDT=Căutare inteligentă
#XFLD: Technical type label for Repository
DWC_REPOSITORY_PACKAGE=Registru
#XFLD: Technical type label for Enterprise Search
DWC_ESMODEL=Enterprise Search
#XFLD: Technical type label for View
DWC_VIEW=Vizualizare
#XFLD: Technical type label for Data Product
DWC_DATAPRODUCT=Produs de date
#XFLD: Technical type label for Data Access Control
DWC_DAC=Control de acces la date
#XFLD: Technical type label for Folder
DWC_FOLDER=Folder
#XFLD: Technical type label for Data Entity
DWC_BUSINESS_ENTITY=Entitate de afaceri
#XFLD: Technical type label for Business Entity Variant
DWC_BUSINESS_ENTITY_VARIANT=Variantă de entitate de afaceri
#XFLD: Technical type label for Responsibility Scenario
DWC_AUTH_SCENARIO=Scenariu responsabilitate
#XFLD: Technical type label for Fact Model
DWC_FACT_MODEL=Model de fapte
#XFLD: Technical type label for Transformation Flow
DWC_PERSPECTIVE=Perspectivă
#XFLD: Technical type label for Consumption Model
DWC_CONSUMPTION_MODEL=Model de consum
#XFLD: Technical type label for Remote Connection
DWC_REMOTECONNECTION=Conexiune la distanță
#XFLD: Technical type label for Fact Model Variant
DWC_FACT_MODEL_VARIANT=Variantă model de fapte
#XMSG: Schedule created alert message
createScheduleSuccess=Programare creată
#XMSG: Schedule updated alert message
updateScheduleSuccess=Programare actualizată
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Programare ștearsă
#XMSG: Schedule takeover success message
ownerChangeScheduleSuccess=Programare alocată la dvs.
#XMSG: Message for starting pausing of schedule
pauseScheduleStarted=Se suspendă 1 programare
#XMSG: Message for starting resuming of schedule
resumeScheduleStarted=Se reia 1 programare
#XFLD: Segmented button label
availableSpacesButton=Disponibil
#XFLD: Segmented button label
selectedSpacesButton=Selectat
#XFLD: Visit website button text
visitWebsite=Vizitați site-ul web
#XMSG: Warning message on unselecting source language
sourceLanguageRemovalWarning=Limba sursă selectată anterior va fi eliminată.
#XBUT: enable translation confirmation button
enableTranslationConfirmationBtn=Activare
#XFLD: ECN performance class label
performanceClassLabel=Clasă de performanță
#XTXT performance class memory text
memoryText=Memorie
#XTXT performance class compute text
computeText=Calcul
#XTXT performance class high-compute text
highComputeText=Calcul ridicat
#XBUT: Recycle Bin Button Text
recycleBin=Coș de gunoi
#XBUT: Restore Button Text
restore=Restaurare
#XMSG: Warning message for new Workload Management UI
priorityWarning=Această zonă este numai pentru citire. Puteți modifica prioritatea spațiului în zona Sistem/Configurare/Gestiune încărcare de lucru.
#XMSG: Warning message for new Workload Management UI
workloadWarning=Această zonă este numai pentru citire. Puteți modifica configurarea încărcării de lucru pentru spațiu în zona Sistem/Configurare/Gestiune încărcare de lucru.
#XCOL: Space table-view column: Apache Spark vCPUs
sparkManagementVCPUs=vCPU-uri Apache Spark
#XCOL: Space table-view column: Apache Spark Memory (GB)
sparkManagementMemory=Memorie Apache Spark (GB)
#XCOL: Space type (from Space Capability) for Data Ingestion Spaces
ingestionSpaceType=Ingestie produs de date
#XFLD: Deployment in progress nodatatext for spark table
sparkTableDeploymentInProgress=Fără date disponibile deoarece spațiul este în curs de implementare
#XFLD: Runtime data being loaded nodatatext for spark table
sparkTableDeploymentLoading=Fără date disponibile deoarece spațiul este în curs de încărcare
#XCOL: Space type (from Space Capability) for BW Private Cloud Edition
BWPCESpaceType=SAP BW
#XBUT: Go to Instance Mappings Button
navToMappingsButton=Editare mapări de instanță
#XFLD: SAP HANA Data Lake Storage label if less than 1GB
dataLakeStorageUsed=< 1 GB
