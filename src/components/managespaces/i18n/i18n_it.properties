#DO NOT REMOVE________________________
#XFLD: Breadcrumb for space monitoring page
spaceMonitorBreadcrumb=Monitoraggio
#XTXT: Type name for spaces in browser tab page title
space=Spazio
#_____________________________________
#XFLD: Spaces label in
spaces=Spazi
#XFLD: Manage plan button text
manageQuotaButtonText=Gest<PERSON>ci piano
#XBUT: Manage resources button
manageResourcesButton=Gestisci risorse
#XFLD: Create space button tooltip
createSpace=Crea spazio
#XFLD: Create
create=Crea
#XFLD: Deploy
deploy=Distribuisci
#XFLD: Page
page=Pagina
#XFLD: Cancel
cancel=Annulla
#XFLD: Update
update=Aggiorna
#XFLD: Save
save=Salva
#XFLD: OK
ok=OK
#XFLD: days
days=Giorni
#XFLD: Space tile edit button label
edit=Modifica
#XFLD: Auto Assign all objects to space
autoAssign=Assegna automaticamente
#XFLD: Space tile open monitoring button label
openMonitoring=Monitora
#XFLD: Delete
delete=Elimina
#XFLD: Copy Space
copy=Copia
#XFLD: Close
close=Chiudi
#XCOL: Space table-view column status
status=Stato
#XFLD: Space status active
activeLabel=Attivo
#XFLD: Space status locked
lockedLabel=Bloccato
#XFLD: Space status critical
criticalLabel=Critico
#XFLD: Space status cold
coldLabel=Poco utilizzato
#XFLD: Space status deleted
deletedLabel=Eliminato
#XFLD: Space status unknown
unknownLabel=Sconosciuto
#XFLD: Space status ok
okLabel=Buone condizioni
#XFLD: Database user expired
expired=Scaduto
#XFLD: deployed
deployed=Distribuito
#XFLD: not deployed
notDeployed=Non distribuito
#XFLD: changes to deploy
changesToDeploy=Modifiche a distribuzione
#XFLD: pending
pending=Distribuzione
#XFLD: designtime error
designtimeError=Errore fase di progettazione
#XFLD: runtime error
runtimeError=Errore runtime
#XFLD: Space created by label
createdBy=Autore creazione
#XFLD: Space created on label
createdOn=Data di creazione
#XFLD: Space deployed on label
deployedOn=Data di distribuzione
#XFLD: Space ID label
spaceID=ID spazio
#XFLD: Priority label
priority=Priorità
#XFLD: Space Priority label
spacePriority=Priorità spazio
#XFLD: Space Configuration label
spaceConfiguration=Configurazione spazio
#XFLD: Not available
notAvailable=Non disponibile
#XFLD: WorkloadType default
default=Predefinito
#XFLD: WorkloadType custom
custom=Personalizzato
#XFLD: Data Lake option label; refers to component SAP HANA Cloud, Data Lake
dataLakeAccess=Accesso data lake
#XFLD: Translation label
translationLabel=Traduzione
#XFLD: Source language label
sourceLanguageLabel=Lingua di origine
#XFLD: Translation CheckBox label
translationCheckBox=Abilita traduzione
#XTXT: Popover hint that space has to be deployed to show user details
openDatabaseUserHintPopoverText=Distribuisci spazio per accedere ai dettagli utente.
#XTXT: Popover hint that space has to be deployed to open database explorer
openDatabaseExplorerHintPopoverText=Distribuisci spazio per aprire Explorer database.
#XTXT: Text for when the Data Lake check box is disabled
dataLakeOptionDisabled=Impossibile utilizzare questo spazio per accedere al data lake poiché è già utilizzato da un altro spazio.
#XFLD: Data Lake check box text; refers to component SAP HANA Cloud, Data Lake
dataLakeCheckBox=Utilizza questo spazio per accedere al data lake.
#XFLD: Space Priority minimum label extension
low=Bassa
#XFLD: Space Priority maximum label extension
high=Alta
#XFLD: Space name label
spaceName=Nome spazio
#XFLD: Enable deploy objects checkbox
enableDeployObjects=Distribuisci oggetti
#XTIT: Copy spaces dialog title
copySpaceDialogTitle=Copia {0}
#XTXT Human readable text for unselected source language
unSelectedSourceLanguage=(Non selezionato)
#XTXT Human readable text for language code "af"
af=Afrikaans
#XTXT Human readable text for language code "ar"
ar=Arabo
#XTXT Human readable text for language code "bg"
bg=Bulgaro
#XTXT Human readable text for language code "ca"
ca=Catalano
#XTXT Human readable text for language code "zh"
zh=Cinese semplificato
#XTXT Human readable text for language code "zf"
zf=Cinese
#XTXT Human readable text for language code "hr"
hr=Croato
#XTXT Human readable text for language code "cs"
cs=Ceco
#XTXT Human readable text for language code "cy"
cy=Gallese
#XTXT Human readable text for language code "da"
da=Danese
#XTXT Human readable text for language code "nl"
nl=Olandese
#XTXT Human readable text for language code "en-UK"
en-UK=Inglese (Regno Unito)
#XTXT Human readable text for language code "en"
en=Inglese (Stati Uniti)
#XTXT Human readable text for language code "et"
et=Estone
#XTXT Human readable text for language code "fa"
fa=Persiano
#XTXT Human readable text for language code "fi"
fi=Finlandese
#XTXT Human readable text for language code "fr-CA"
fr-CA=Francese (Canada)
#XTXT Human readable text for language code "fr"
fr=Francese
#XTXT Human readable text for language code "de"
de=Tedesco
#XTXT Human readable text for language code "el"
el=Greco
#XTXT Human readable text for language code "he"
he=Ebraico
#XTXT Human readable text for language code "hi"
hi=Hindi
#XTXT Human readable text for language code "hu"
hu=Ungherese
#XTXT Human readable text for language code "is"
is=Islandese
#XTXT Human readable text for language code "id"
id=Bahasa Indonesia
#XTXT Human readable text for language code "it"
it=Italiano
#XTXT Human readable text for language code "ja"
ja=Giapponese
#XTXT Human readable text for language code "kk"
kk=Kazako
#XTXT Human readable text for language code "ko"
ko=Coreano
#XTXT Human readable text for language code "lv"
lv=Lettone
#XTXT Human readable text for language code "lt"
lt=Lituano
#XTXT Human readable text for language code "ms"
ms=Bahasa Melayu
#XTXT Human readable text for language code "no"
no=Norvegese
#XTXT Human readable text for language code "pl"
pl=Polacco
#XTXT Human readable text for language code "pt"
pt=Portoghese (Brasile)
#XTXT Human readable text for language code "pt-PT"
pt-PT=Portoghese (Portogallo)
#XTXT Human readable text for language code "ro"
ro=Rumeno
#XTXT Human readable text for language code "ru"
ru=Russo
#XTXT Human readable text for language code "sr"
sr=Serbo
#XTXT Human readable text for language code "sh"
sh=Serbo-croato
#XTXT Human readable text for language code "sk"
sk=Slovacco
#XTXT Human readable text for language code "sl"
sl=Sloveno
#XTXT Human readable text for language code "es"
es=Spagnolo
#XTXT Human readable text for language code "es-MX"
es-MX=Spagnolo (Messico)
#XTXT Human readable text for language code "sv"
sv=Svedese
#XTXT Human readable text for language code "th"
th=Thailandese
#XTXT Human readable text for language code "tr"
tr=Turco
#XTXT Human readable text for language code "uk"
uk=Ucraino
#XTXT Human readable text for language code "vi"
vi=Vietnamita
#XTIT: Delete spaces confirmation title
deleteConfirmationHeaderText=Elimina spazi
#XTXT: Logically delete space delete confirmation text with space name
logicallyDeleteConfirmationBodyTextWithSpaceName=Confermare lo spostamento dello spazio "{0}" nel cestino?
#XTXT: Logically dpace delete confirmation text for multiple spaces
logicallyDeleteConfirmationBodyTextMultiple=Confermare lo spostamento dei {0} spazi selezionati nel cestino?
#XTXT: Delete space delete confirmation text with space name
deleteConfirmationBodyTextWithSpaceName=Confermare l''eliminazione dello spazio "{0}"? L''azione non può essere annullata.
#XTXT: Delete space delete confirmation text for multiple spaces
deleteConfirmationBodyTextMultiple=Confermare l''eliminazione degli spazi selezionati ({0})? L''azione non può essere annullata. I contenuti seguenti verranno eliminati {1}:
#XTXT: permanently
permanently=definitivamente
#XTXT: {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDetail=I contenuti seguenti verranno eliminati {0} e non potranno essere recuperati:
#XTXT: Hint Delete space confirmation text
deleteConfirmationHint=Digitare {0} per confermare l''eliminazione.
#XTXT: Delete space confirmation error check spelling
deleteConfirmationErrorCheckSpelling=Verificare l'ortografia e riprovare.
#XTXT: All Spaces
allSpaces=Tutti gli spazi
#XTXT: All data
allData=Tutti gli oggetti e tutti i dati contenuti nello spazio
#XTXT: All connections
allConnections=Tutte le connessioni definite nello spazio
#XFLD: Space tile selection box tooltip
clickToSelect=Fai clic per selezionare
#XTXT: All database users
allDatabaseUsers=Tutti gli oggetti e i dati contenuti in uno schema Open SQL associato allo spazio
#XFLD: remove members button tooltip
deleteUsers=Rimuovi membri
#XTXT: Space long description text
description=Descrizione (massimo 4000 caratteri)
#XFLD: Add Members button tooltip
addUsers=Aggiungi membri
#XFLD: Add Users button tooltip
addUsersTooltip=Aggiungi utenti
#XFLD: Edit Users button tooltip
editUsersTooltip=Modifica utenti
#XFLD: Remove Users button tooltip
removeUsersTooltip=Rimuovi utenti
#XFLD: Searchfield placeholder
filter=Cerca
#XCOL: Users table-view column health
health=Stato
#XCOL: Users table-view column access
access=Accesso
#XFLD: No user found nodatatext
noDataText=Nessun utente trovato
#XTIT: Members dialog title
selectUserDialogTitle=Aggiungi membri
#XTIT: User dialog title
addUserDialogTitle=Aggiungi utenti
#XTIT: Delete connections confirmation title
deleteConnectionConfirmationHeaderText=Elimina connessioni
#XTIT: Delete single connection confirmation title
deleteConnectionConfirmationHeaderTextSingle=Elimina connessione
#XTXT: Delete connection delete confirmation text
deleteConnectionConfirmationBodyText=Eliminare le connessioni selezionate? Verranno rimosse in maniera definitiva.
#XTIT: Remote source dialog title
selectRemoteConnectionDialogTitle=Seleziona connessioni
#XTIT: Share connection dialog title
connectionSharingDialogTitle=Condividi connessione
#XTIT: Connections shared from other spaces table title
sharedConnectionsFromOtherSpaces=Connessioni condivise
#XFLD: Add remote source button tooltip
addRemoteConnections=Aggiungi connessioni
#XFLD: Delete remote source button tooltip
deleteRemoteConnections=Rimuovi connessioni
#XFLD: Share remote source button tooltip
shareConnections=Condividi connessioni
#XFLD: Tile-layout tooltip
tileLayout=Layout riquadro
#XFLD: Table-layout tooltip
tableLayout=Layout tabella
#XMSG: Success message after creating space
createSpaceSuccessMessage=Spazio creato
#XMSG: Success message after copying space
copySpaceSuccessMessage=Copia dello spazio "{0}" nello spazio "{1}"
#XMSG: Success message after deploying space
deploymentSuccessMessage=La distribuzione spazio è iniziata
#XMSG: Success message after Apache Spark update started
sparkUpdateStartedSuccessMessage=Aggiornamento Apache Spark avviato
#YMSE: Error while starting Apache Spark update
sparkUpdateStartedErrorMessage=Aggiornamento Apache Spark non riuscito
#XMSG: Success message after updating space
updateSettingsSuccessMessage=Dettagli spazio aggiornati
#XMSG: Success message after unlocking space
unlockSpaceSuccessMessage=Spazio sbloccato temporaneamente
#XMSG: Success message after deleting a single space
deleteSpaceSuccessMessage=Spazio eliminato
#XMSG: Success message after deleting multiple spaces
deleteSpacesSuccessMessage=Spazi eliminati
#XMSG: Success message after restoring a single space
restoreSpaceSuccessMessage=Spazio ripristinato
#XMSG: Success message after restoring multiple spaces
restoreSpacesSuccessMessage=Spazi ripristinati
#YMSE: Error while updating settings
updateSettingsFailureMessage=Impossibile aggiornare le impostazioni spazio.
#YMSE: Error while updating data lake option
dataLakeEnabledInOtherSpace=Il data lake è già assegnato a un altro spazio. Solo uno spazio alla volta può accedere al data lake.
#YMSE: Error while updating data lake option
virtualTablesExists=Impossibile annullare l'assegnazione del data lake da questo spazio poiché sono ancora presenti dipendenze alle tabelle virtuali*.Eliminare le tabelle virtuali per annullare l'assegnazione del data lake da questo spazio.
#YMSE: Error while unlocking space
unlockSpaceFailureMessage=Impossibile sbloccare lo spazio.
#YMSE: Error while creating space
createSpaceError=Impossibile creare lo spazio.
#YMSE: Error while creating duplicate space
createDuplicateSpaceError=Spazio con nome {0} già esistente.
#YMSE: Error while deleting a single space
deleteSpaceError=Impossibile eliminare lo spazio.
#YMSE: Error details while deleting a single space
deleteSpaceDetailedError=Lo spazio “{0}” dell''utente non sta funzionando più correttamente. Provare a eliminarlo di nuovo. Se ancora non funziona, chiedere all''amministratore di eliminare lo spazio o aprire un ticket.
#YMSE: Error while deleting a single space in files
deleteSpaceInFilesError=Impossibile eliminare i dati spazio in File.
#YMSE: Error while deleting a single space users
deleteSpaceUsersError=Impossibile rimuovere gli utenti.
#YMSE: Error while deleting a single space schema
deleteSpaceSchemasError=Impossibile rimuovere gli schemi.
#YMSE: Error while deleting a single space connections
deleteSpaceConnectionsError=Impossibile rimuovere le connessioni.
#YMSE: Error while deleting a single space data
deleteSpaceDataError=Impossibile eliminare i dati spazio.
#YMSE: Error while deleting multiple spaces
deleteSpacesError=Impossibile eliminare gli spazi.
#YMSE: Error while restoring a single space
restoreSpaceError=Impossibile ripristinare lo spazio.
#YMSE: Error while restoring multiple spaces
restoreSpacesError=Impossibile ripristinare gli spazi.
#YMSE: Error while creating users
createUsersError=Non è stato possibile aggiungere gli utenti.
#YMSE: Error while removing users
removeUsersError=Non è stato possibile rimuovere gli utenti.
#YMSE: Error while removing user
removeUserError=Non è stato possibile rimuovere l'utente.
#YMSE: Error assigning roles to a user
roleAssignUnAssignError=Non è stato possibile aggiungere l'utente al ruolo nell'ambito selezionato.\n\nNon è possibile aggiungere se stessi a un ruolo nell'ambito, ma è possibile chiedere all'amministratore di farlo in propria vece.
#YMSE: Error assigning user to the space
userAssignError=Impossibile assegnare l'utente allo spazio. \n\n L'utente è già assegnato al numero massimo consentito (100) di spazi tra ruoli nell'ambito.
#YMSE: Error assigning users to the space
usersAssignError=Impossibile assegnare utenti allo spazio. \n\n L'utente è già assegnato al numero massimo consentito (100) di spazi tra ruoli nell'ambito.
#YMSE: Error while fetching users
fetchUserWithScopedRolesError=Non è stato possibile recuperare gli utenti. Riprovare più tardi.
#YMSE: Error while fetching scoped roles
fetchScopedRolesError=Non è stato possibile recuperare i ruoli inclusi nell'ambito della soluzione.
#YMSE: Error while fetching members
fetchUserError=Non è stato possibile chiamare i membri. Riprovare più tardi.
#YMSE: Error while loading run-time database
loadRuntimeError=Impossibile caricare le informazioni del database di runtime.
#YMSE: Error while loading spaces
loadSpacesError=Si è verificato un errore durante il tentativo di recupero degli spazi.
#YMSE: Error while loading haas resources
loadStorageError=Si è verificato un errore durante il tentativo di recupero i dati archivio.
#YMSE: Error no data could be loaded
loadDataError=Si è verificato un errore durante il tentativo di recupero dei dati.
#XFLD: Click to refresh storage data
clickToRefresh=Fai clic qui per riprovare.
#XTIT: Delete space popup title
deleteSpacePopupTitle=Elimina spazio
#XCOL: Spaces table-view column name
name=Nome
#XCOL: Spaces table-view deployment status
deploymentStatus=Stato distribuzione
#XFLD: Disk label in space details
storageLabel=Disco (GB)
#XFLD: In-Memory label in space details
ramLabel=Memoria (GB)
#XFLD: Memory label on space card
memory=Memoria per archivio
#XFLD: Storage Assignment label in space details
storrageAssignmentLabel=Archivio spazio
#XFLD: Storage Type label in space details
storageTypeLabel=Tipo di archivio
#XFLD: Enable Space Quota
enableSpaceQuota=Abilita quota spazio
#XFLD: No Space Quota
noSpaceQuota=Nessuna quota spazio
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
diskAndInMemoryText=Database SAP HANA (disco e in memoria)
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
dataLakeFilesText=File data lake SAP HANA
#XFLD: Available scoped roles label
availableRoles=Ruoli nell'ambito disponibili
#XFLD: Selected scoped roles label
selectedRoles=Ruoli nell'ambito selezionati
#XCOL: Spaces table-view column models
models=Modelli
#XCOL: Spaces table-view column users
users=Utenti
#XCOL: Spaces table-view column connections
connections=Connessioni
#XFLD: Section header overview in space detail
overview=Panoramica
#XFLD: Section header Apache Spark in space detail
sparkManagementSection=Apache Spark
#XFLD: Section header Apache Spark in space detail
sparkManagementSubSection=Applicazioni
#XFLD: Section header Apache Spark in space detail
taskAssignmentSubSection=Assegnazione task
#XFLD: vCPU label in Apache Spark section
vCPULabel=vCPU
#XFLD: Memory label in Apache Spark section
memoryLabel=Memoria (GB)
#XFLD: space configuration label in Apache Spark section
spaceConfigurationLabel=Configurazione spazio
#XFLD: Space Source label
sparkApplicationLabel=Applicazione
#XFLD: Cluster Size label
clusterSizeLabel=Dimensioni cluster
#XFLD: Driver label
driverLabel=Driver
#XFLD: Executor label
executorLabel=Esecutore
#XFLD: max label
maxLabel=Utilizzo massimo
#XFLD: TrF Default label
trFDefaultLabel=Valore predefinito flusso di trasformazione
#XFLD: Merge Default label
mergeDefaultLabel=Unisci valore predefinito
#XFLD: Optimize Default label
optimizeDefaultLabel=Ottimizza valore predefinito
#XFLD: Deployment Default label
deploymentDefaultLabel=Distribuzione tabella locale (file)
#XFLD: max spark source label
driverExecutorMaxUsedSparkSource={0} CPU / {1} GB
#XFLD: Object type label
taskObjectTypeLabel=Tipo di oggetto
#XFLD: Task activity label
taskActivityLabel=Attività
#XFLD: Task Application ID label
taskApplicationIDLabel=Applicazione predefinita
#XFLD: Section header in space detail
generalSettings=Impostazioni generali
#XMSG: Message strip message when a space is locked
spaceLockedMessageStrip=Questo spazio è attualmente bloccato dal sistema.
#XMSG: Message strip message deploy warning
deployWarningMessageStrip=Le modifiche in questa sezione verranno distribuite immediatamente.
#XMSG: Message strip message modify space configuration warning
modifySpaceConfigurationMessageStrip=La modifica di questi valori potrebbe causare problemi di prestazioni.
#XFLD: Button text to unlock the space again
unlockSpace=Sblocca spazio
#XFLD: Info text for audit log formatted message
auditLogText=Abilitare i registri di audit per registrare le azioni di lettura o modifica (criteri di audit). Gli amministratori possono quindi analizzare chi ha eseguito quale azione in quale momento.
#XMSG: Message strip  audit warning
auditLogWarningMessageStrip=I registri di audit possono consumare una grande quantità di archiviazione su disco nel tenant. Se si abilita un criterio di audit (azioni di lettura o di modifica), è necessario monitorare regolarmente l'utilizzo dello spazio di archiviazione su disco (tramite la scheda Archivio su disco utilizzato nel Monitor di sistema) per evitare interruzioni complete del disco, che possono portare a interruzioni del servizio. Se si disabilita un criterio di audit, tutte le relative voci del registro di audit verranno eliminate. Se si desidera conservare le voci del registro di audit, si consiglia di esportarle prima di disabilitare il criterio di audit.
#XFLD: Message strip link text to open in app help to guide user what to do to unlock his space permanently
unlockSpaceHelp=Mostra help
#XMSG: Message strip message when a space will be locked again
spaceTemporarilyUnlockedMessageStrip=Questo spazio supera l''archivio spazio e verrà bloccato in {0} {1}.
#XMSG: Unit for remaining time until space is locked again
hours=ore
#XMSG: Unit for remaining time until space is locked again
minutes=minuti
#XFLD: Section header in space detail for auditing
detailsSectionAuditing=Auditing
#XFLD: Subsection header in space detail for auditing
auditing=Impostazioni audit spazio
#XFLD: Hot space tooltip
hotSpaceCountTooltip=Spazi critici: archivio utilizzato superiore al 90%.
#XFLD: Green space tooltip
greenSpaceCountTooltip=Spazi in buone condizioni: archivio utilizzato compreso tra il 6% e il 90%.
#XFLD: Cold space tooltip
coldSpaceCountTooltip=Spazi poco utilizzati: archivio utilizzato pari o inferiore al 5%.
#XFLD: Critical space tooltip
criticalSpaceCountTooltip=Spazi critici: archivio utilizzato superiore al 90%.
#XFLD: Green space tooltip
okSpaceCountTooltip=Spazi in buone condizioni: archivio utilizzato compreso tra il 6% e il 90%.
#XFLD: Locked space tooltip
lockedSpaceCountTooltipNew=Spazi bloccati: bloccati per memoria insufficiente.
#XFLD: Locked space tooltip
lockedSpaceCountTooltip=Spazi bloccati
#YMSE: Error while deleting remote source
deleteRemoteError=Non è stato possibile rimuovere le connessioni.
#XFLD: Space ID input tooltip
spaceIDInfoTooltip=Impossibile modificare in seguito l'ID spazio.\nCaratteri validi A - Z, 0 - 9 e _
#YMSE: Error when space name is empty
spaceNameEmptyValidationError=Inserire nome spazio.
#YMSE: Error when ECN Business name is empty
ecnNameEmptyValidationError=Immettere un nome aziendale.
#YMSE: Error when space id is empty
spaceIDInputEmptyValidationError=Inserire ID spazio.
#YMSE: Error when space id contains invalid characters
spaceIDInputInValidError=Caratteri non validi. Utilizzare solo A - Z, 0 - 9 e _.
#YMSE: Error when space id exists while creation
spaceIDInputDuplicateError=ID spazio già esistente.
#XFLD: Space searchfield placeholder
search=Cerca
#XMSG: Success message after creating users
createUsersSuccess=Utenti aggiunti
#XMSG: Success message after creating user
createUserSuccess=Utente aggiunto
#XMSG: Success message after updating users
updateUsersSuccess={0} utenti aggiunti
#XMSG: Success message after updating user
updateUserSuccess=Utente aggiornato
#XMSG: Success message after removing users
removeUsersSuccess={0} utenti rimossi
#XMSG: Success message after removing user
removeUserSuccess=Utente rimosso
#XFLD: Schema name
schemaName=Nome schema
#XFLD: used of total
ofTemplate={0} di {1}
#XFLD: Assigned disk label in space overview
assignedStorageTemplate=Disco assegnato ({0} di {1})
#XFLD: Assigned Memory label in space overview
assignedRAMTemplate=Memoria assegnata ({0} di {1})
#XFLD: Storage ratio on space
accelearationRAM=Accelerazione memoria
#XFLD: No Storage Consumption
noStorageConsumptionText=Nessuna quota di archiviazione assegnata.
#XFLD: Used disk label in space overview
usedStorageTemplate=Disco utilizzato per archivio ({0} di {1})
#XFLD: Used Memory label in space overview
usedRAMTemplate=Memoria utilizzata per archivio ({0} di {1})
#XFLD: Used Disk tooltip
usedStorageTooltipTemplate={0} di {1} disco utilizzato per archivio
#XFLD: Used Memory tooltip
usedRAMTooltipTemplate={0} di {1} memoria utilizzata
#XFLD: Assigned Disk tooltip
assignedStorageTooltipTemplate={0} di {1} disco assegnato
#XFLD: Assigned Memory tooltip
assignedRAMTooltipTemplate={0} di {1} memoria assegnata
#XFLD: Space related used storage tooltip
spaceRelatedUsedStorageTooltipTemplate=Dati spazio: {0}
#XFLD: Not space related used storage tooltip
otherDataUsedStorageTooltipTemplate=Altri dati: {0}
#XFLD: Storage used exceeded popover and tooltip
storageUsedExceededText=È consigliabile ampliare il piano; in alternativa contatta il supporto SAP.
#XCOL: Space table-view column used Disk
usedStorage=Disco utilizzato per archivio
#XCOL: Space monitor column used Memory
usedRAM=Memoria utilizzata per archivio
#XCOL: Space monitor column Schema
tableSchema=Schema
#XCOL: Space monitor column Storage Type
tableStorageType=Tipo di archivio
#XCOL: Space monitor column Table Type
tableType=Tipo di tabella
#XCOL: Space monitor column Record Count
tableRecordCount=Conteggio record
#XFLD: Assigned Disk
assignedStorage=Disco assegnato per archivio
#XFLD: Assigned Memory
assignedRAM=Memoria assegnata per archivio
#XCOL: Space table-view column storage utilization
tableStorageUtilization=Archivio utilizzato
#XFLD: space status
spaceStatus=Stato spazio
#XFLD: space type
spaceType=Tipo di spazio
#XFLD: space type label for space with space type "SAP BW bridge" ("SAP BW bridge" is a product name, do not translate it)
abapbridgeSpaceType=Ponte SAP BW
#XFLD: space type label for space with space type "Data Marketplace"
marketplaceSpaceType=Prodotto data provider
#XMSG: Error message for when selected space cannot be deleted due to the space type
deleteTypedSpaceError=Impossibile eliminare lo spazio {0} poiché il relativo tipo è {1}.
#XMSG: Error message for when selected spaces cannot be deleted due to the space type
deleteTypedSpacesError=Impossibile eliminare i {0} spazi selezionati. Gli spazi con i seguenti tipi non possono essere eliminati: {1}.
#XFLD: space type label for space without space type
defaultSpaceType=SAP Datasphere
#XFLD: Tooltip for monitor space button
monitorSpace=Monitora
#XFLD: Tooltip for edit space button
editSpace=Modifica spazio
#XMSG: Deletion warning in messagebox
deleteConfirmation=Eliminare questo spazio?
#XFLD: Tooltip for delete space button
deleteSpace=Elimina spazio
#XFLD: storage
storage=Disco per archivio
#XFLD: username
userName=Nome utente
#XFLD: port
port=Porta
#XFLD: hostname
hostName=Nome host
#XFLD: password
password=Password
#XBUT: Request new password button
requestPassword=Richiedi nuova password
#YEXP: Usage explanation in time data section
timeDataSectionHint=Creare tabelle e dimensioni temporali da utilizzare in modelli e storie.
#YEXP: Usage explanation in Space schema section
spaceSchemaHint=Si desidera che i dati nello spazio siano utilizzabili da altre app o altri strumenti? Se sì, creare uno o più utenti che possano accedere ai dati nello spazio e decidere se tutti i dati spazio futuri sono utilizzabili per impostazione predefinita.
#XTIT: Create schema popup title
createSchemaDialogTitle=Crea schema Open SQL
#XTIT: Generate Time Data dialog title
generateTimeDataDialogTitle=Crea tabelle e dimensioni temporali
#XTIT: Edit Time Data dialog title
editTimeDataDialogTitle=Modifica tabelle e dimensioni temporali
#XTIT: Time Data token title
timeDataTokenTitle=Dati temporali
#XTIT: Time Data token title
timeDataUpdateViews=Aggiorna viste Dati temporali
#XFLD: Time Data token creation in progress label
timeDataCreationInProgress=Creazione in corso...
#XFLD: Time Data token creation error label
timeDataCreationError=Creazione non riuscita. Riprovare.
#XTIT: Time Data dialog time settings title
timeDataDialogTimeSettingsTitle=Impostazioni tabella temporale
#XTIT: Time Data dialog time data table title
timeDataDialogTranslationTablesTitle=Tabelle di traduzione
#XTIT: Time Data dialog dimensions title
timeDataDialogTimeViewsTitle=Dimensioni temporali
#XFLD: Time Data dialog time range label
timeRangeHint=Definire l'intervallo temporale.
#XFLD: Time Data dialog time data table label
timeDataHint=Scegliere un nome per la tabella.
#XFLD: Time Data dialog time data dimensions label
timeDimensionsHint=Scegliere un nome per la dimensione.
#XFLD: Time Data Time range description label
timerangeLabel=Intervallo temporale
#XFLD: Time Data dialog from year label
fromYearLabel=Da anno
#XFLD: Time Data dialog to year label
toYearLabel=Ad anno
#XFLD: Time Data dialog calendar type label
calendarTypeLabel=Tipo di calendario
#XFLD: Time Data dialog granularity label
granularityLabel=Granularità
#XFLD: Time Data dialog technical name label
technicalNameLabel=Nome tecnico
#XFLD: Time Data dialog quarters text table lable
quarterTextsTableLabel=Tabella di traduzione per trimestri
#XFLD: Time Data dialog months text table lable
monthsTextsTableLabel=Tabella di traduzione per mesi
#XFLD: Time Data dialog days text table lable
daysTextsTableLabel=Tabella di traduzione per giorni
#XFLD: Time Data dialog year label
yearLabel=Dimensione anno
#XFLD: Time Data dialog quarter label
quarterLabel=Dimensione trimestre
#XFLD: Time Data dialog month label
monthLabel=Dimensione mese
#XFLD: Time Data dialog day label
dayLabel=Dimensione giorno
#XFLD: Time Data dialog gregorian calendar type label
gregorian=Gregoriano
#XFLD: Time Data dialog time granularity day label
day=Giorno
#XFLD: Time Data dialog business name maxlength reached
timeRangeValueStateTextMaxLengthBusinessName=Lunghezza massima di 1000 caratteri raggiunta.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextMaxRangeExceeded=L'intervallo temporale massimo è di 150 anni.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeLower="Da anno" deve essere inferiore a "Ad anno"
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeHigher="Da anno" deve essere 1900 o successivo.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeHigher="Ad anno" deve essere superiore a "Da anno"
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeLower=“Ad anno” deve essere inferiore ad anno corrente più 100
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningFromYear=L'aumento del valore "Da anno" potrebbe determinare la perdita di dati
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningToYear=La diminuzione del valore "Ad anno" potrebbe determinare la perdita di dati
#XMSG: Time Data creation validation error message
timeDataValidationError=Sembra che alcuni campi non siano validi. Controllare i campi obbligatori per continuare.
#XMSG: Time Data deletion popup text
deleteTimeDataConfirmation=Eliminare i dati?
#XMSG: Time Data creation success message
createTimeDataSuccess=Dati temporali creati
#XMSG: Time Data update success message
updateTimeDataSuccess=Dati temporali aggiornati
#XMSG: Time Data delete success message
deleteTimeDataSuccess=Dati temporali eliminati
#XMSG: Time Data creation error message
createTimeDataError=Si è verificato un errore durante il tentativo di creazione dei dati temporali.
#XMSG: Time Data update error message
updateTimeDataError=Si è verificato un errore durante il tentativo di aggiornamento dei dati temporali.
#XMSG: Time Data creation error message
deleteTimeDataError=Si è verificato un errore durante il tentativo di eliminazione dei dati temporali.
#XMSG: Error message when time data could not be retrieved
retrievingTimeDataError=Impossibile caricare i dati temporali.
#XTIT: Time Data deletion popup title
deleteTimeDataConfirmationTitle=Avvertimento
#XTXT: Time Data deletion dependency popup text
deleteTimeDataDependencyText=Non è stato possibile eliminare i dati temporali perché sono utilizzati in altri modelli.
#XTXT: Time Data deletion dependency popup text singular
deleteTimeDataDependencyTextSingular=Non è stato possibile eliminare i dati temporali perché sono utilizzati in un altro modello.
#XMSG: Validation error when required time data form field is empty
timeDataRequiredField=Questo campo è obbligatorio e non può essere lasciato vuoto.
#XFLD: Tooltip for Open in Database Explorer button
openInDBX=Apri in Explorer database
#YMSE: Dimension Year
dimensionYearView=Dimensione "Anno"
#YMSE: Dimension Year
dimensionQuarterView=Dimensione "Trimestre"
#YMSE: Dimension Year
dimensionMonthView=Dimensione "Mese"
#YMSE: Dimension Year
dimensionDayView=Dimensione "Giorno"
#XFLD: Time Data deletion object title
timeDataUsedIn=(utilizzati in {0} modelli)
#XFLD: Time Data deletion object title singular
timeDataUsedInSingular=(utilizzati in 1 modello)
#XFLD: Time Data deletion table column provider
provider=Provider
#XTIT: Time Data dependency dialog title
timeDataDependencyDialogTitle=Dipendenze
#XTIT: Create schema popup title
createSpaceSchemaUserDialogTitle=Crea utente per schema spazio
#XFLD: Create schema button
createSchemaButton=Crea schema Open SQL
#XFLD: Generate TimeData button
generateTimeDataButton=Crea tabelle e dimensioni temporali
#XFLD: Show dependencies button
showDependenciesButton=Mostra dipendenze
#XFLD: tooltip for disabled buttons when user is not member of space
mustBeMemberOfSpaceForOperation=Per effettuare questa operazione, l'utente deve essere un membro dello spazio.
#XFLD: Create space schema user button
createSpaceSchemaUserButton=Crea utente schema spazio
#YMSE: API Schema users load error
loadSchemaUsersError=Non è stato possibile caricare l'elenco di utenti.
#XTIT: API Schema-config popup title
schemaUserDetailsDialogTitle=Dettagli utente schema spazio
#XMSG: API Schema user deletion popup text
deleteSpaceSchemaUserConfirmation=Eliminare l'utente selezionato?
#XMSG: API Schema user deletion confirmation text
userDeleteSuccess=Utente eliminato.
#YMSE: API Schema user deletion error
userDeleteError=Impossibile eliminare l'utente.
#XFLD: User deleted
userDeleted=L'utente è stato eliminato.
#XTIT: Remove user popup title
removeUserConfirmationTitle=Avviso
#XMSG: Remove user popup text
removeUserConfirmation=Confermare la rimozione dell'utente? L'utente e i ruoli nell'ambito ad esso assegnati verranno rimossi dallo spazio.
#XMSG: Remove users popup text
removeUsersConfirmation=Confermare la rimozione degli utenti? Gli utenti e i ruoli nell'ambito ad essi assegnati verranno rimossi dallo spazio.
#XBUT: Confirmation button for remove user dialog
removeUserConfirmationBtn=Rimuovi
#YMSE: No data text for available roles
noDataAvailableRoles=Lo spazio non è aggiunto ad alcun ruolo nell'ambito. \n Per poter aggiungere utenti allo spazio, è prima necessario aggiungerlo a uno o più ruoli nell'ambito.
#YMSE: No data text for selected roles
noDataSelectedRoles=Nessun ruolo nell'ambito selezionato
#XTIT: Schema-config popup title
schemaDetailsDialogTitle=Dettagli configurazione schema Open SQL
#XFLD: Label for Read Audit Log
auditLogRead=Attiva registro di audit per operazioni di lettura
#XFLD: Label for Change Audit Log
auditLogChange=Attiva registro di audit per operazioni di modifica
#XFLD: Label Audit Log Retention
auditLogRetention=Mantieni registri per
#XFLD: Label Audit Log Retention Unit
retentionUnit=giorni
#YMSE: Error when log retention time is set incorrectly
incorrectRetentionTime=Inserire un numero intero tra {0} e {1}
#XFLD: Label for Cross Space Schema Access
spaceSchemaAccess=Utilizza dati schema spazio
#XTIT: Confirm deactivation of space schema consumption dialog title
disableSpaceSchemaConsumptionDialogTitle=Interrompi utilizzo dei dati schema spazio
#XTXT: Confirm deactivation of space schema consumption dialog text
disableSpaceSchemaConsumptionDialogText=Questo schema Open SQL potrebbe utilizzare i dati dello schema spazio. Se si interrompe l'utilizzo, i modelli basati sui dati dello schema spazio potrebbero non funzionare più.
#XBUT: Confirmation button for the stop consuming space schema data dialog
stopConsuming=Interrompi utilizzo
#XTXT: Tooltip for data lake icon
dataLakeIconTooltip=Questo spazio viene utilizzato per accedere al data lake
#XFLD: Tooltip for data lake table entry
dataLakeTableEntryTooltip=Data lake attivato
#XFLD: used In-Memory linit reached tag
usedRamLimitReachedLabel=Limite memoria raggiunto
#XFLD: Space storage Limit reached tag
storageLimitReachedLabel=Limite archivio raggiunto
#XFLD: Minimal Storage Limit Reached tag
minStorageLimitReachedLabel=Limite archivio minimo raggiunto
#XFLD: Space ram tag
ramLimitReachedLabel=Limite memoria raggiunto
#XFLD: Minimal In-Memory Limit Reached tag
minRamLimitReachedLabel=Limite memoria minimo raggiunto
#XFLD: Popover text after click on space storage limit warning
storageLimitReachedText=È stato raggiunto il limite archivio di spazio assegnato di {0}. Assegnare più archivio allo spazio.
#XFLD: System storage tag
systemStorageLimitReachedLabel=Limite archivio sistema raggiunto
#XMSG: Popover text after click on storage limit warning
systemStorageLimitReachedText=È stato raggiunto il limite archivio sistema di {0}. Impossibile assegnare ora altro archivio allo spazio.
#XMSG: Schema deletion warning
deleteSchemaConfirmation=L'eliminazione di questo schema Open SQL eliminerà in maniera definitiva anche tutti gli oggetti archiviati e le associazioni eseguite nello schema. Continuare?
#XMSG: Success message after deleting a schema
schemaDeleteSuccess=Schema eliminato
#YMSE: Error while deleting schema.
schemaDeleteError=Impossibile eliminare lo schema.
#XMSG: Success message after update a schema
schemaUpdateSuccess=Schema aggiornato
#YMSE: Error while updating schema.
schemaUpdateError=Impossibile aggiornare lo schema.
#XMSG: Hint when no password is shown in the dialog.
newPasswordHint=È stata fornita una password per questo schema. Se si è dimenticata o smarrita la password, è possibile richiederne una nuova. Ricordare di copiare o salvare la nuova password.
#XMSG: Hint when showing the password in the dialog.
savePasswordHint=Copiare la password. Sarà necessaria per configurare una connessione a questo schema. Se si è dimenticata la password, è possibile aprire questa finestra di dialogo per reimpostarla.
#XFLD,50: Hint on entering schema name in creation dialog
schemaNameHint=Impossibile modificare nome dopo creazione schema.
#XFLD: Open SQL Schemas section sub headline
schemasSQL=Open SQL
#XFLD: Space schema section sub headline
schemasSpace=Spazio
#XFLD: HDI Container section header
HDIContainers=Contenitori HDI
#XTXT: Add HDI Containers button tooltip
addHDIContainers=Aggiungi contenitori HDI
#XTXT: Remove HDI Containers button tooltip
removeHDIContainers=Rimuovi contenitori HDI
#XFLD: Enable HDI Containers feature button
enableHDIContainersButton=Abilita accesso
#YMSE: No data text for HDI Containers table
noDataHDIContainers=Nessun contenitore HDI aggiunto.
#YMSE: No data text for Timedata section
noDataTimedata=Nessuna tabella e dimensione temporale creata.
#YMSE: No data text during runtime not available for Timedata section
runtimeNotAvailableNoDataTimedata=Impossibile caricare tabelle e dimensioni temporali poiché il database di runtime non è disponibile.
#YMSE: No data text during runtime not available for HDI section
runtimeNotAvailableNoDataHDI=Impossibile caricare contenitori HDI poiché il database di runtime non è disponibile.
#YMSE: Error while fetching HDI Containers
fetchHDIContainersError=Impossibile ottenere i contenitori HDI. Riprovare più tardi.
#XFLD Table column header for HDI Container names
HDIContainerName=Nome contenitore HDI
#XFLD: Enable HDI Containers feature dialog title
enableHDIContainersDialogTitle=Abilita accesso
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText1New=È possibile attivare SAP SQL Data Warehousing sul proprio tenant SAP Datasphere per scambiare i dati tra i propri contenitori HDI e i propri spazi SAP Datasphere senza dover spostare i dati.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText2=A tale fine, aprire un ticket di supporto facendo clic sul pulsante sottostante.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText3New=Una volta elaborato il ticket, è necessario creare uno o più contenitori HDI nuovi nel database di runtime di SAP Datasphere, dopo di che il pulsante Abilita accesso verrà sostituito dal pulsante + nella sezione Contenitori HDI per tutti gli spazi SAP Datasphere e sarà possibile aggiungere i propri contenitori a uno spazio.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText41=Per ulteriori informazioni passare a %%0. Per informazioni dettagliate su cosa includere nel ticket vedere %%1.
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkText=SAP Help
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkTextSapNote3057059=Nota SAP 3057059
#XBUT: Open Ticket Button Text
openTicket=Apri ticket
#XBUT: Add Button Text
add=Aggiungi
#XBUT: Next Button Text
next=Avanti
#XBUT: Edit Button Text
editUsers=Modifica
#XBUT: create user Button Text
createUser=Crea
#XBUT: Update user Button Text
updateUser=Seleziona
#XTIT: Add HDI Containers Dialog Title
addHDIContainersDialogTitle=Aggiungi contenitori HDI non assegnati
#XFLD: No data text for add HDI container dialog
addHDIContainersDialogNoDataText=Impossibile trovare contenitori non assegnati. \n Il contenitore che si sta cercando potrebbe già essere assegnato a uno spazio.
#YMSE: Error while loading assigned HDI Containers
loadAssignedHDIContainersError=Non è stato possibile caricare i contenitori HDI assegnati.
#YMSE: Error while loading assigned HDI Containers
loadUnassignedHDIContainersError=Non è stato possibile caricare i contenitori HDI.
#XMSG: Success message
succeededToAddHDIContainer=Contenitore HDI aggiunto
#XMSG: Success message
succeededToAddHDIContainerPlural=Contenitori HDI aggiunti
#XMSG: Success message
succeededToDeleteHDIContainer=Contenitore HDI rimosso
#XMSG: Success message
succeededToDeleteHDIContainerPlural=Contenitori HDI rimossi
#XFLD: Time data section sub headline
timeDataSection=Tabelle e dimensioni temporali
#XFLD: Schemas list entry read audit log enabled indicator
readIndicator=Lettura
#XFLD: Schemas list entry change audit log enabled indicator
changeIndicator=Modifica
#XFLD: Remote sources section sub headline
allconnections=Assegnamento connessione
#XFLD: Remote sources section sub headline
localconnections=Connessioni locali
#XFLD: Remote sources section sub headline for open Connectors
openConnectors=SAP Open Connectors
#XFLD: User section sub headline
memberassignment=Assegnazione membro
#XFLD: User assignment section sub headline
userAssignment=Assegnazione utente
#XFLD: User section Access dropdown Member
member=Membro
#XFLD: User assignment section column name
user=Nome utente
#XTXT: Selected role count
selectedRoleToolbarText=Selezionati: {0}
#XTIT: Space detail section connections title
detailsSectionConnections=Connessioni
#XTIT: Space detail section data access title
detailsSectionDataAccess=Accesso schema
#XTIT: Space detail section time data title
detailsSectionGenerateData=Dati temporali
#XTIT: Space detail section members title
detailsSectionUsers=Membri
#XTIT: Space detail section Users title
detailsSectionUsersTitle=Utenti
#XTIT: Out of Storage
insufficientStoragePopoverTitle=Al di fuori dell'archivio
#XTIT: Storage distribution
storageDistributionPopoverTitle=Archivio su disco utilizzato
#XTXT: Out of Storage popover text
insufficientStorageText=Per creare un nuovo spazio, ridurre l'archivio assegnato di altro spazio o eliminare uno spazio non più necessario. È possibile aumentare l'archivio totale di sistema chiamando Gestisci piano.
#XMSG: Space id length warning
spaceIdLengthWarning=Massimo di {0} caratteri superato.
#XMSG: Space name length warning
spaceNameLengthWarning=Massimo di {0} caratteri superato.
#XMSG: Space id reserved prefix warning
spaceIdnWarningReservedPrefix=Non utilizzare il prefisso {0} per evitare possibili conflitti.
#YMSE: Error while loading open SQL schemas
loadLocalSchemasError=Non è stato possibile caricare schemi Open SQL.
#YMSE: Error while creating open SQL schema
createLocalSchemaError=Non è stato possibile creare schemi Open SQL.
#YMSE: Error while loading all remote connections
loadRemoteConnectionsError=Impossibile caricare tutte le connessioni remote.
#YMSE: Error while loading space details
loadSpaceDetailsError=Non è stato possibile caricare i dettagli dello spazio.
#YMSE: Error while deploying space details
deploySpaceDetailsError=Non è stato possibile distribuire lo spazio.
#YMSE: Error while copying space details
copySpaceDetailsError=Non è stato possibile copiare lo spazio.
#YMSE: Error while loading storage data
loadStorageDataError=Non è stato possibile caricare i dati di archiviazione.
#YMSE: Error while loading all users
loadAllUsersError=Impossibile caricare tutti gli utenti.
#YMSE: Failed to reset password
resetPasswordError=Impossibile reimpostare la password.
#XMSG: Success message after updating space
resetPasswordSuccessMessage=Nuova password impostata per lo schema
#YMSE: DP Agent-name too long
DBAgentNameError=Nome dell'agente DP troppo lungo.
#YMSE: Schema-name not valid.
schemaNameError=Nome dello schema non valido.
#YMSE: User name not valid.
UserNameError=Nome utente non valido.
#XTIT: Consumption by Storage Type
consumptionStorageTypeText=Utilizzo in base a tipo di archivio
#XTIT: Consumption by Schema
consumptionSchemaText=Utilizzo in base a schema
#XTIT: Overall Consumption by schema
overallConsumptionSchemaText=Utilizzo tabella complessivo in base a schema
#XTIT: Overall Consumption by Table Type
overallConsumptionTableTypeText=Utilizzo complessivo in base a tipo di tabella
#XTIT: Tables
tableDetailsText=Dettagli tabella
#XTIT: Table Storage Consumption
tableStorageConsumptionText=Utilizzo archivio tabella
#XFLD: Table Type label
tableTypeLabel=Tipo di tabella
#XFLD: Schema label
schemaLabel=Schema
#XFLD: reset table tooltip
resetTable=Reimposta tabella
#XFLD: In-Memory label in space monitor
inMemoryLabel=Memoria
#XFLD: Disk label in space monitor
diskLabel=Disco
#XFLD: Yes
yesLabel=Sì
#XFLD: No
noLabel=No
#XFLD: switch label: Want the data in this space to be consumable by default?
allowConsumptionByDefault=Si desidera che i dati in questo spazio siano utilizzabili per impostazione predefinita?
#XFLD: Business Name
businessNameLabel=Nome aziendale
#XFLD: Refresh
refresh=Aggiorna
#XMSG: No filter results title
noFilterResultsTitle=Sembra che le impostazioni filtro non mostrino alcun dato.
#XMSG: No filter results message
noFilterResultsMsg=Tentare di affinare le impostazioni di filtro. Se ancora non viene visualizzato alcun dato, creare alcune tabelle nel Generatore di dati. Una volta che utilizzano l'archiviazione, sarà possibile monitorare qui i dati.
#XMSG: Space monitoring Hana state red title
spaceMonitorHanaRedTitle=Il database di runtime non è disponibile.
#XMSG: Space monitoring Hana state red message
spaceMonitorHanaRedMsg=Poiché il database di runtime non è disponibile, alcune funzionalità sono disabilitate e non è possibile visualizzare alcuna informazione su questa pagina.
#YMSE: Error while creating space schema user
createSpaceSchemaUserError=Non è stato possibile creare l'utente schema spazio.
#YMSE: Error User name already exists
userAlreadyExistsError=Nome utente già esistente.
#YMSE: Error Authentication failed
authenticationFailedError=Autenticazione non riuscita.
#XMSG: Message strip message when a schema user is locked
userLockedMessageStrip=L'utente è bloccato a causa di troppi logon non riusciti. Richiedere una nuova password per sbloccare l'utente.
#XMSG: Success message after reset and unlock schema user
resetUnlockPasswordSuccessMessage=Nuova password impostata e utente sbloccato
#XMSG: user is locked message
userLockedMessage=Utente bloccato.
#XCOL: Users table-view column Role
spaceRole=Ruolo
#XCOL: Users table-view column Scoped Role
spaceScopedRole=Ruolo nell'ambito
#XCOL: Users table-view column Space Admin
spaceAdmin=Amministratore spazio
#XFLD: User section dropdown value Viewer
viewer=Visualizzatore
#XFLD: User section dropdown value Modeler
modeler=Modellatore
#XFLD: User section dropdown value Data Integrator
dataIntegrator=Integratore dati
#XFLD: User section dropdown value Space Administrator
spaceAdministrator=Amministratore spazio
#XMSG: Success message -  Space Role updated successfully.
updateUserSpaceRoleSuccess=Ruolo spazio aggiornato
#XMSG: Error message - Failed to update Space Role.
updateUserSpaceRoleError=Ruolo spazio non aggiornato correttamente.
#XFLD:
databaseUserNameSuffix=Suffisso nome utente database
#XTXT: Space Schema password text
spaceSchemaPasswordText=Per configurare una connessione a questo schema, copiare la password. Qualora si dimenticasse la password, sarà sempre possibile richiederne una nuova.
#XFLD: Cloud Platform label in space schema dialog
cloudPlatformLabel=SAP Cloud Platform
#XTXT: Cloud Platform text in space schema dialog
cloudPlatformText=Per configurare l'accesso tramite questo utente, attivare l'utilizzo e copiare le credenziali. Qualora sia possibile copiare solo le credenziali senza password, assicurarsi di aggiungere una password più tardi.
#XFLD: enable consumption in CloudPlatform label in space schema dialog
enableConsumptionInCloudPlatformLabel=Attiva utilizzo in SAP Cloud Platform
#XFLD: Credentials for User-Provided Service label in space schema dialog
cloudPlatformCredentialsLabel=Credenziali per servizio fornito dall'utente:
#XFLD: Credentials for User-Provided Service (Without Password) label in space schema dialog
cloudPlatformCredentialsWithoutPasswordLabel=Credenziali per servizio fornito dall'utente (senza password):
#XFLD: Copy Credentials label in space schema dialog
copyCredentialsLabel=Copia credenziali senza password
#XFLD: Copy Credentials label in space schema dialog
copyFullCredentialsLabel=Copia credenziali complete
#XFLD: Copy Password label in space schema dialog
copyPasswordLabel=Copia password
#XMSG: Credentials copied to clipboard
credentialsCopiedMessage=Credenziali copiate negli Appunti
#XMSG: Password copied to clipboard
passwordCopiedMessage=Password copiata negli Appunti
#XMSG: create database user dialog title
createDatabaseUserDialogTitle=Crea utente database
#XMSG: Database Users section title
databaseUsers=Utenti database
#XMSG: Database User Details dialog title
databaseUserDetailsDialogTitle=Dettagli utente database
#XFLD: database user read audit log
databaseUserAuditLogRead=Attiva registri di audit per operazioni di lettura e mantieni registri per
#XFLD: database user change audit log
databaseUserAuditLogChange=Attiva registri di audit per operazioni di modifica e mantieni registri per
#XMSG: Cloud Platform Access
cloudPlatformAccess=Accesso a SAP Cloud Platform
#XFLD: Enable Consumption in Cloud Platform hint
enableConsumptionCloudPlatformHint=Configura l'accesso al contenitore HANA Deployment Infrastructure (HDI) tramite questo utente database. Per connetterti al contenitore HDI, è necessario attivare la modellazione SQL.
#XFLD: Enable HDI Consumption
enableConsumptionCloudPlatform=Attiva utilizzo HDI
#XFLD: Enable Consumption hint
enableConsumptionHint=Si desidera far utilizzare i dati nel proprio spazio da altri strumenti o applicazioni?
#XFLD: Enable Consumption
enableConsumption=Attiva utilizzo SQL
#XFLD: Enable Modeling
enableModeling=Attiva modellazione SQL
#XMSG: Privileges for Data Modeling
privilegesModeling=Inserimento dati
#XMSG: Privileges for Data Consumption
privilegesConsumption=Utilizzo dati per strumenti esterni
#XFLD: SQL Modeling
sqlModeling=Modellazione SQL
#XFLD: SQL Consumption
sqlConsumption=Utilizzo SQL
#XFLD: enabled
enabled=Attivato
#XFLD: disabled
disabled=Disattivato
#XFLD: Edit Privileges
editPrivileges=Modifica privilegi
#XFLD: Open Database Explorer
openDBX=Apri Explorer database
#XFLD: create database user hint
databaseCreateHint=Si noti che non sarà possibile modificare di nuovo il nome dopo il salvataggio.
#XFLD: Internal Schema Name
internalSchemaName=Nome schema interno
#YMSE: Failed to load database users
loadDatabaseUserError=Impossibile caricare utenti database
#YMSE: Failed to delete database user
deleteDatabaseUsersError=Impossibile eliminare utenti database
#XMSG: Success message after deleting database user
deleteDatabaseUserSuccess=Utente database eliminato
#XMSG: Success message after deleting database users
deleteDatabaseUsersSuccess=Utenti database eliminati
#XMSG: Success message after creating database user
createDatabaseUserSuccessMessage=Utente database creato
#YMSE: Failed to create database user
createDatabaseUserError=Impossibile creare utente database
#XMSG: Success message after updating database user
updateDatabaseUserSuccessMessage=Utente database aggiornato
#YMSE: Failed to update database user
updateDatabaseUserError=Impossibile aggiornare utente database
#XFLD: HDI Consumption
hdiConsumption=Utilizzo HDI
#XTIT: Space detail section Database Access title
detailsSectionDatabaseAccess=Accesso database
#XTXT: Database users consumption hint
databaseUsersConsumptionHint=Rendi i dati contenuti in questo spazio utilizzabili per impostazione predefinita. I modelli nei generatori consentiranno automaticamente l'utilizzo dei dati.
#XFLD: Default Consumption of Space Data:
defaultConsumptionOfSpaceData=Utilizzo predefinito dei dati spazio:
#XFLD: Database User Name
databaseUserName=Nome utente database
#XMSG: Database User creation validation error message
databaseUserValidationError=Sembra che alcuni campi non siano validi. Controllare i campi obbligatori per continuare.
#XMSG: SQL modeling can’t be enabled since this user has been migrated.
migratedDatabaseUserHint=Impossibile attivare l'inserimento dati poiché non è stata eseguita la migrazione di questo utente.
#XBUT: Remove Button Text
remove=Rimuovi
#XBUT: Remove Spaces Button Text
removeSpaces=Rimuovi spazi
#XBUT: Remove Objects Button Text
removeObjects=Rimuovi oggetti
#XMSG: No members have been added yet.
noMembersAssigned=Non è stato aggiunto ancora nessun membro.
#XMSG: No users have been added yet.
noUsersAssigned=Non è stato aggiunto ancora nessun utente.
#XMSG: No database users have been created or your filter is not showing any data.
noDatabaseUsers=Non è stato creato nessun utente database oppure il filtro non consente di visualizzare i dati.
#XMSG: Please enter a user name.
noDatabaseUsername=Inserire un nome utente.
#XMSG: The user name is too long. Please use a shorter one.
databaseUsernameTooLong=Il nome utente è troppo lungo. Utilizzarne uno più breve.
#XMSG: No privileges have been enabled and this database user will have limited functionality. Do you still want to continue?
noPrivilegesConfirmationMessage=Non è stato attivato alcun privilegio, pertanto questo utente database disporrà di funzionalità limitate. Continuare comunque?
#XMSG: To enable audit logs for change operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogChangeMessage=Per abilitare i registri di audit per operazioni di modifica, è necessario attivare anche l'inserimento dati. Eseguire questa operazione?
#XMSG: To enable audit logs for read operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogReadMessage=Per abilitare i registri di audit per operazioni di lettura, è necessario attivare anche l'inserimento dati. Eseguire questa operazione?
#XMSG: To enable HDI consumption data ingestion and data consumption needs to be enabled. Do you want to enable all?
enableHDIConsumptionMessage=Per abilitare l'utilizzo HDI, devono essere attivati anche l'inserimento e l'utilizzo dati. Eseguire questa operazione?
#XMSG:
databaseUserPasswordText=Per configurare una connessione a questo utente database, copiare la password. Qualora si dimenticasse la password, sarà sempre possibile richiederne una nuova.
#XTIT: Space detail section members title
detailsSectionMembers=Membri
#XMSG: New password set
newPasswordSet=Nuova password impostata
#XFLD: Data Ingestion
dataIngestion=Inserimento dati
#XFLD: Data Consumption
dataConsumption=Utilizzo dati
#XFLD: Privileges
privileges=Privilegi
#XFLD: Enable Data ingestion
enableDataIngestion=Abilita inserimento dati
#XMSG: Log the read and change operations for data ingestion.
databaseUserAuditHint=Registrare le operazioni di lettura e modifica dell'inserimento dati.
#XMSG: Make your space data available in your HDI containers.
databaseUserHDIHint=Rendere i propri dati dello spazio disponibili nei contenitori HDI.
#XFLD: Enable Data consumption
enableDataConsumption=Abilita utilizzo dati
#XMSG: Allow other apps or tools to consume your space data.
enableDataConsumptionHint=Consentire ad altre app o altri strumenti di utilizzare i dati dello spazio.
#XMSG: To set up access via this schema user copy the credentials to your user-provided service. In case you can only copy the credentials without a password make sure to add the password later.
databaseUserHDIConsumptionHint=Per configurare l'accesso tramite questo utente database, copiare le credenziali nel servizio fornito dall'utente. Qualora sia possibile copiare solo le credenziali senza password, assicurarsi di aggiungere una password più tardi.
#XFLD: Data Flow Runtime Capacity label in space overview
dataFlowRuntimeCapacity=Capacità runtime flusso di dati ({0}:{1} ore di {2} ore)
#XFLD: Could not load Data Flow Runtime Capacity
dataFlowRuntimeCapacityError=Impossibile caricare capacità runtime flusso di dati
#XMSG: User can grant data consumption to other users.
databaseUserConsumptionWithGrantOptionHint=L'utente può concedere l'utilizzo dati ad altri utenti.
#XFLD: Enable data consumption with grant option
enableConsumptionWithGrantOption=Attiva utilizzo dati con opzione di concessione
#XMSG: To enable data consumption with grant option data consumption needs to be enabled. Do you want to enable both?
enableConsumptionWithGrantMessage=Per attivare l'utilizzo dati con opzione di concessione, è necessario che sia attivato l'utilizzo dati. Attivare entrambi?
#XFLD: Enable Automated Predictive Library (APL) and Predictive Analysis Library (PAL)
allowUsingScriptServerFunctionality=Attiva APL (Automated Predictive Library) e PAL (Predictive Analysis Library)
#XMSG: User can use SAP HANA Cloud embedded machine learning functions.
allowUsingScriptServerFunctionalityHint=L'utente può utilizzare le funzioni di apprendimento automatico integrate di SAP HANA Cloud.
#XFLD: Password Policy
passwordPolicy=Criterio password
#XMSG: Password Policy
passwordPolicyHint=Attivare o disattivare qui il criterio password configurato.
#XFLD: Enable Password Policy
enablePasswordPolicy=Attiva criterio password
#XMSG: Read Access to the Space Schema
readAccessTitle=Accesso di lettura allo schema spazio
#XMSG: read access hint
readAccessHint=Consentire all'utente database di connettere strumenti esterni allo schema spazio e leggere le viste esposte per il consumo.
#XFLD: Space Schema
spaceSchema=Schema spazio
#XFLD: Enable Read Access (SQL)
enableReadAccess=Attiva accesso di lettura (SQL)
#XMSG: Allow the user to grant read access to other users.
withGrantOptionHint=Consentire all'utente di concedere l'accesso di lettura ad altri utenti.
#XFLD: With Grant Option
withGrantOption=Con opzione di concessione dell'autorizzazione
#XMSG: Make your space data available in your HDI containers.
enableHDIConsumptionHint=Rendere i propri dati dello spazio disponibili nei contenitori HDI.
#XFLD: Enable HDI Consumption
enableHDIConsumption=Attiva consumo HDI
#XMSG: Write Access to the User’s Open SQL Schema
writeAccessTitle=Accesso di scrittura allo schema Open SQL dell'utente
#XMSG: write access hint
writeAccessHint=Consentire all'utente database di connettere strumenti esterni allo schema Open SQL dell'utente per creare entità di dati e inserirli per l'utilizzo nello spazio.
#XFLD: Open SQL Schema
openSQLSchema=Schema Open SQL
#XFLD: Enable Write Access (SQL DDL & DML)
enableWriteAccess=Attiva accesso di scrittura (SQL, DDL e DML)
#XMSG: audit hint
auditHint=Registrare le operazioni di lettura e scrittura nello schema Open SQL.
#XMSG: data consumption hint
dataConsumptionHint=Esporre tutte le nuove viste nello spazio per il consumo per impostazione predefinita. I modellatori possono sovrascrivere questa impostazione per singole viste mediante lo switch "Esponi per consumo" nel pannello laterale di output della vista. È anche possibile scegliere i formati nei quali sono esposte le viste.
#XFLD: Expose for Consumption by Default
exposeDataConsumption=Esponi per consumo per impostazione predefinita
#XMSG: database users hint consumption hint
databaseUsersHint2New=Creare utenti database per connettere strumenti esterni a SAP Datasphere. Impostare privilegi per consentire agli utenti di leggere i dati degli spazi, creare entità di dati (DDL) e inserire dati (DML) per l'utilizzo nello spazio.
#XFLD: Read
read=Lettura
#XFLD: Read (HDI)
readHDI=Lettura (HDI)
#XFLD: Write
write=Scrittura
#XMSG: HDI Containers Hint
HDIContainersHint2=Attivare l'accesso ai contenitori HDI (SAP HANA Deployment Infrastructure) nello spazio. I modellatori possono utilizzare gli artefatti HDI come origine per le viste, e i client HDI possono accedere ai dati degli spazi.
#XMSG: Open info dialog
openDatabaseUserInfoDialog=Aprire la finestra di dialogo informativa
#XMSG: Database user is locked. Open info dialog to unlock
openLockedDatabaseUserInfoDialog=Utente database bloccato. Per sbloccarlo, aprire la finestra di dialogo
#XFLD: Table
table=Tabella
#XFLD: Space section partner configuration title
partnerConnectionConfigurationSectionTitle=Connessione partner
#XFLD: Space section partner configuration subtitle
partnerConnectionConfigurationSectionSubTitle=Configurazione di connessione partner
#XFLD: Space section partner configuration hint
partnerConnectionConfigurationSectionHint=Definire il proprio riquadro di connessione partner aggiungendo l'URL iFrame e l'icona. Questa configurazione è disponibile solo per questo tenant.
#XFLD: Table Name Field
partnerConnectionConfigurationName=Nome riquadro
#XFLD: Table iFrame URL field
partnerConnectionConfigurationiFrameURL=URL iFrame
#XFLD: Table iFrame Post Message Origin field
partnerConnectionConfigurationiFramePostMessageOrigin=Origine del messaggio "POST" iFrame
#XFLD: Table icon/image name
partnerConnectionConfigurationIcon=Icona
#XMSG: Table no result found
partnerConnectionConfigurationNotfound=Nessuna configurazione di connessione partner trovata.
#XMSG: Partner Connection Confgurations are not available
partnerConnectionConfigurationNotAvailable=Impossibile visualizzare le configurazioni di connessione partner quando il database di run-time non è disponibile.
#XFLD: Dialog Create Partner Connection Configuration
partnerConnectionConfigurationDialogTitleAdd=Crea configurazione di connessione partner
#XFLD: Dialog Create Partner Connection Configuration Upload Icon
partnerConnectionConfigurationButtonUloadPartnerIcon=Carica icona
#XFLD: Dialog Create Partner Connection Configuration Select
partnerConnectionConfigurationSelectIcon=Seleziona (dimensioni massime di 200 KB)
#XFLD: Dialog Create Partner Connection Configuration Enter a name
partnerConnectionConfigurationNamePlaceholder=Esempio riquadro partner
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL
partnerConnectionConfigurationiFrameURLPlaceholder=http://www.example.com
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL Check
partnerConnectionConfigurationiFramePostMessageOriginPlaceholder=.example.com
#XFLD: Dialog Create Partner Connection Configuration Browse
partnerConnectionConfigurationBrowseButton=Sfoglia
#XFLD: Create Partner Connection Configuration Msg
partnerConnectionConfigurationUploadMsg=Creazione configurazione di connessione partner completata.
#XMSG: Error while deleting Partner Connection configuration(s)
partnerConnectionConfigurationDeleteError=Si è verificato un errore nell'eliminazione delle configurazioni di connessione partner.
#XMSG: Partner Connection Configuration has been successfully deleted
partnerConnectionConfigurationDeleteSuccess=Eliminazione configurazione di connessione partner completata.
#XMSG: Error while retrieving Partner Connection Configurations
partnerConnectionConfigurationLoadError=Si è verificato un errore nel reperimento delle configurazioni di connessione partner.
#XMSG: Error fileSizeLimitExceeded
partnerConnectionConfigurationfileSizeLimitExceeded=Impossibile caricare il file perché supera la dimensione massima di 200KB.
#XBUT: Create Partner Connection Configuration
partnerConnectionConfigurationButtonCreate=Crea configurazione di connessione partner
#XBUT: Delete Partner Connection Configuration
partnerConnectionConfigurationButtonDelete=Elimina configurazione di connessione partner
#XMSG: Partner Tile Creation Failure
upsertPartnerTileFailed=Non è stato possibile creare il riquadro partner.
#XMSG: Partner Tile Deletion Failure
deletePartnerTileFailed=Non è stato possibile eliminare il riquadro partner.
#XMSG: HANA cloud connector settings reset failure
resetHANASCCSettingsFailed=Reset delle impostazioni del connettore SAP HANA Cloud del cliente non riuscito
#XFLD: Workload Class
workloadClass=Classe workload
#XFLD: Workload Management
workloadManagement=Gestione del carico di lavoro
#XFLD: Priority
workloadClassPriority=Priorità
#XMSG:
workloadManagementPriorityHint=È possibile specificare la priorità di questo spazio durante l'interrogazione del database. Immettere un valore da 1 (priorità più bassa) e 8 (priorità più alta). In una situazione in cui gli spazi sono in competizione per i thread disponibili, quelli con priorità più alta vengono eseguiti prima degli spazi con priorità più bassa.
#XMSG:
workloadClassPriorityHint=È possibile specificare la priorità dello spazio da 0 (più bassa) a 8 (più alta). Le istruzioni di uno spazio con priorità più alta vengono eseguite prima delle istruzioni di altri spazi con priorità più bassa. La priorità predefinita è 5. Dal momento che il valore 9 è riservato per le operazioni di sistema, non è disponibile per uno spazio.
#XFLD: Statement Limits
workloadclassStatementLimits=Limiti di istruzioni
#XFLD: Workload Configuration
workloadConfiguration=Configurazione del carico di lavoro
#XMSG:
workloadClassStatementLimitsHint=È possibile specificare il numero (o la percentuale) massimo di thread e GB della memoria che le istruzioni attualmente in esecuzione nello spazio possono consumare. È possibile immettere un qualsiasi valore o percentuale compreso tra 0 (nessun limite) e la memoria e i thread totali disponibili nel tenant. \N\n Notare che la specifica un limite thread può ridurre le prestazioni. \N\n Se si specifica un limite memoria, le istruzioni che lo raggiungono non vengono eseguite.
#XMSG:
workloadClassStatementLimitsDescription=La configurazione predefinita offre limiti di risorse generosi, evitando al contempo che un singolo spazio sovraccarichi il sistema.
#XMSG:
workloadClassStatementLimitCustomDescription=È possibile impostare i limiti di memoria e di thread totali massimi che le istruzioni attualmente in esecuzione nello spazio possono consumare.
#XMSG:
totalStatementThreadLimitHelpText=L'impostazione di un limite di thread troppo basso può influire sulle prestazioni dell'istruzione, mentre valori eccessivamente elevati o pari a 0 potrebbero consentire allo spazio di consumare tutti i thread di sistema disponibili.
#XMSG:
totalStatementMemoryLimitHelpText=L'impostazione di un limite di memoria troppo basso può causare problemi di memoria esaurita, mentre valori eccessivamente elevati o pari a 0 potrebbero consentire allo spazio di consumare tutta la memoria di sistema disponibile.
#XMSG:
totalStatementThreadLimitHelpTextRestricted=Inserire una percentuale tra 1% e 70% (o numero equivalente) del numero totale di thread disponibili nel tenant. L'impostazione di un limite thread troppo basso può influire sulle prestazioni dell'istruzione, mentre valori troppo elevati possono influire sulla prestazione di istruzioni in altri spazi.
#XMSG:
totalStatementThreadLimitHelpTextDynamic=Immettere una percentuale tra 1% e {0}% (o numero equivalente) del numero totale di thread disponibili nel tenant. L''impostazione di un limite thread troppo basso può influire sulle prestazioni dell''istruzione, mentre valori troppo elevati possono influire sulle prestazioni di istruzioni in altri spazi.
#XMSG:
totalStatementMemoryLimitHelpTextNew=Inserire un valore o una percentuale tra 0 (nessun limite) e la quantità totale di memoria disponibile nel tenant. L'impostazione di un limite memoria troppo basso può influire sulle prestazioni dell'istruzione, mentre valori troppo elevati possono influire sulla prestazione di istruzioni in altri spazi.
#XFLD: Total Statement Thread Limit
totalStatementThreadLimit=Limite thread totale per istruzioni
#XFLD: Segmented button label
workloadclassPercentage=%
#XFLD: Segmented button label
workloadclassGigaByte=GB
#XFLD, 80: Segmented button label
workloadclassThreads=Threads
#XFLD: Total Statement Memory Limit
totalStatementMemoryLimit=Limite memoria totale per istruzioni
#YMSE: Error while loading customer hana info
loadCustomerHanaInfoError=Caricamento delle informazioni SAP HANA cliente non riuscito.
#XMSG:
minimumLimitReached=Limite minimo raggiunto.
#XMSG:
maximumLimitReached=Limite massimo raggiunto.
#XMSG: Name Taken for Technical Name
technical-name-taken=Connessione con il nome tecnico immesso già esistente. Immettere un altro nome.
#XMSG: Name Too long for Technical Name
technical-name-too-long=Il nome tecnico immesso supera i 40 caratteri. Immettere un nome tecnico più breve.
#XMSG: Technical name field empty
technical-name-field-empty=Inserire un nome tecnico.
#XMSG: Generic error when user enters invalid characters for a field
field-invalid-characters=È possibile utilizzare per il nome solo lettere (a-z), numeri (0-9) e trattini bassi (_).
#XMSG: Generic error when user enters invalid initial characters for a field
field-underscore-trim=Il nome immesso non può iniziare o finire con un trattino basso (_).
#XFLD: Checkbox label for Enable Statement Limits
enableStatementLimits=Abilita limiti di istruzioni
#XFLD: Connection SubSection Settings Title in space details
connectionSettings=Impostazioni
#XMSG: Connections tool hint in Space details section
connectionsToolHint=Per creare o modificare connessioni aprire l'app Connessioni dalla navigazione laterale o fare clic qui:
#XBUT: Connection list Open Connection Tool Button
button-connectiontool=Vai a Connessioni
#XFLD: Not deployed label on space tile
notDeployedLabel=Lo spazio non è ancora stato distribuito.
#XFLD: Not deployed additional text on space tile
notDeployedText=Distribuire lo spazio.
#XFLD: Corrupt space label on space tile
corruptSpace=Si è verificato un errore.
#XFLD: Corrupt space additional text on space tile
corruptSpaceHint=Tentate di nuovo la distribuzione o contattare il supporto
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Dati registro di audit
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Dati amministrativi
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Altri dati
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Dati negli spazi
#XMSG: Unlock Space confirmation text
unlockSpaceConfirmation=Sbloccare lo spazio?
#XMSG: Lock Space confirmation text
lockSpaceConfirmation=Bloccare lo spazio?
#XFLD: Lock
lock=Blocca
#XFLD: Unlock
unlock=Sblocca
#XFLD: Locking
locking=Blocco in corso
#XMSG: Success message after locking space
lockSpaceSuccessMsg=Spazio bloccato
#XMSG: Success message after unlocking space
unlockSpaceSuccessMsg=Spazio sboccato
#XMSG: Success message after locking spaces
lockSpacesSuccessMsg=Spazi bloccati
#XMSG: Success message after unlocking spaces
unlockSpacesSuccessMsg=Spazi sboccati
#YMSE: Error while locking a space
lockSpaceError=Questo spazio non può essere bloccato.
#YMSE: Error while unlocking a space
unlockSpaceError=Questo spazio non può essere sbloccato.
#XTIT: popup title Warning
confirmationWarningTitle=Avvertimento
#XMSG: Message strip message when a space was locked manually
manualLockedMessage=Lo spazio è stato bloccato manualmente.
#XMSG: Message strip message when a space was locked due to the amount of generated audit logs
auditLogLockedMessage=Lo spazio è stato bloccato dal sistema perché i registri di audit consumano un'elevata quantità di GB su disco.
#XMSG: Message strip message when a space was locked because the assigned quota was exceeded.
quotaLockedMessage=Lo spazio è stato bloccato dal sistema perché supera l'allocazione di archiviazione in memoria o su disco.
#XMSG: Unlock Spaces confirmation text
unlockSpacesConfirmation=Sbloccare gli spazi selezionati?
#XMSG: Lock Spaces confirmation text
lockSpacesConfirmation=Bloccare gli spazi selezionati?
#XTIT: Scoped role editor dialog title
scopedRoleEditorDialogTitle=Editor di ruoli nell'ambito
#XTIT: ECN Management title
ecnManagementTitle=Gestione spazio e nodo di calcolo elastico
#XFLD: ECNs
ecns=Nodi di calcolo elastico
#XFLD: ECN phase Ready
ecnReady=Pronto
#XFLD: ECN phase Running
ecnRunning=In esecuzione
#XFLD: ECN phase Initial
ecnInitial=Non pronto
#XFLD: ECN phase Starting
ecnStarting=Avvio in corso
#XFLD: ECN phase Starting Failed
ecnStartingFailed=Avvio non riuscito
#XFLD: ECN phase Stopping
ecnStopping=Interruzione in corso
#XFLD: ECN phase Stopping Failed
ecnStoppingFailed=Interruzione non riuscita
#XBTN: Assign Button
assign=Assegna spazi
#XBTN: Start Header-Button
start=Avvia
#XBTN: Update Header-Button
repair=Aggiorna
#XBTN: Stop Header-Button
stop=Interrompi
#XFLD: ECN hours remaining
ecnHoursRemaining=1000 ore rimanenti
#XFLD: ECN block-hours remaining
ecnBlockHoursRemaining={0} ore di blocco rimanenti
#XFLD: ECN block-hour remaining
ecnBlockHourRemaining={0} ora di blocco rimanente
#XTIT: ECN creation dialog title
ecnCreationDialogTitle=Crea nodo di calcolo elastico
#XTIT: ECN edit dialog title
ecnEditDialogTitle=Modifica nodo di calcolo elastico
#XTIT: ECN deletion dialog title
ecnDeletionDialogTitle=Elimina nodo di calcolo elastico
#XTIT: ECN Space assignment dialog title
ecnSpaceAssignmentDialogTitle=Assegna spazi
#XFLD: ECN ID
ECNIDLabel=Nodo di calcolo elastico
#XTXT: Selected toolbar text
selectedToolbarText=Selezionati: {0}
#XTIT: Elastic Compute Nodes
ECNslong=Nodi di calcolo elastico
#XTIT: Object assignment - Table header: Objects available
objectsAvailable=Numero di oggetti
#XTIT: Object assignment - Dialog header text
selectObjects=Selezionare spazi e oggetti da assegnare al nodo di calcolo elastico:
#XTIT: Object assignment - Table header title: Objects
objects=Oggetti
#XTIT: Object assignment - Table header: Type
type=Tipo
#XMSG: Message strip delete database user audit warning
deleteDatabaseUserWarningMessageStrip=Tenere presente che l'eliminazione di un utente database risulterà nell'eliminazione di tutte le immissioni nel registro di audit generate. Per mantenere i registri di audit, è consigliabile esportali prima di eliminare l'utente database.
#XMSG: Message strip delete HDI container audit logs impact
deleteHDIContainerWarningMessageStrip=Tenere presente che l'annullamento dell'assegnazione di un contenitore HDI dallo spazio risulterà nell'eliminazione di tutte le immissioni nel registro di audit generate. Per mantenere i registri di audit, è consigliabile esportarli prima di annullare l'assegnazione del contenitore HDI.
#XTXT: All audit logs
allAuditLogs=Tutte le immissioni nel registro di audit generate per lo spazio
#XMSG: Message strip disable audit warning
disableAuditWarningMessageStrip=Tenere presente che la disattivazione di un criterio di audit (operazioni di lettura o modifica) risulterà nell'eliminazione di tutte le immissioni nel registro di audit. Per mantenere le immissioni nel registro di audit, è consigliabile esportarle prima di disattivare il criterio di audit.
#XTIT: No Spaces assigned to ECN Illustration title
noSpacesECNTitle=Ancora nessuno spazio o oggetto assegnato
#XTIT: No Spaces assigned to ECN Illustration description
noSpacesECNDescription=Per iniziare a utilizzare il nodo di calcolo elastico, assegnare a esso uno spazio o degli oggetti.
#XTIT: No Spaces Illustration title
noSpacesTitle=Nessuno spazio ancora creato
#XTIT: No Spaces Illustration description
noSpacesDescription=Per iniziare ad acquisire i dati, creare uno spazio.
#XTIT: No Spaces Illustration title
noDeletedSpacesTitle=Il cestino è vuoto
#XTIT: No Spaces Illustration description
noDeletedSpacesDescription=È possibile recuperare gli spazi eliminati da qui.
#XTXT:  {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDatabaseUsers=Una volta distribuito lo spazio, i seguenti utenti database verranno eliminati {0} e non potranno essere recuperati:
#XTIT: Delete database users
deleteDatabaseUsersTitle=Elimina utenti database
#YMSE: Error if ecn id already exists
ecnIDInputDuplicateError=ID già esistente.
#YMSE: Error if ecn id contains invalid characters
ecnIDInputInvalidError=Utilizzare solo caratteri minuscoli a - z e numeri 0 - 9
#YMSE: Error if ecn id is too short
ecnIDInputTooShortError=L''ID deve essere lungo almeno {0} caratteri.
#XMSG: ecn id length warning
ecnIdLengthWarning=Massimo di {0} caratteri superato.
#XFLD: open System Monitor
systemMonitor=Monitor di sistema
#XFLD: open ECN schedule dialog menu entry
schedule=Pianificazione
#XFLD: open create ECN schedule dialog
createSchedule=Crea pianificazione
#XFLD: open change ECN schedule dialog
changeSchedule=Modifica pianificazione
#XFLD: open delete ECN schedule dialog
deleteSchedule=Elimina pianificazione
#XFLD: open self assign ECN schedule dialog
selfAssignSchedule=Assegna pianificazione a me
#XFLD: open pause ECN schedule dialog
pauseSchedule=Metti pianificazione in pausa
#XFLD: open resume ECN schedule dialog
resumeSchedule=Riprendi pianificazione
#XFLD: View Logs
viewLogs=Visualizza registri
#XFLD: Compute Blocks
computeBlocks=Blocchi calcolo
#XFLD: Memory label in ECN creation dialog
ecnMemory=Memoria (GB)
#XFLD: Storage label in ECN creation dialog
ecnStorage=Spazio di archiviazione (GB)
#XFLD: Number of CPU label in ECN creation dialog
ecnCPUCount=Numero di CPU
#XFLD: ECN updated by label
changedBy=Autore modifica
#XFLD: ECN updated on label
changedOn=Data di modifica
#XMSG: Success message after creating a Elastic Compute Node
createEcnSuccessMessage=Nodo di calcolo elastico creato
#YMSE: Error while creating a Elastic Compute Node
createEcnError=Non è stato possibile creare il nodo di calcolo elastico
#XMSG: Success message after updating a Elastic Compute Node
updateEcnSuccessMessage=Nodo di calcolo elastico aggiornato
#XMSG: Error while updating a Elastic Compute Node
udpateEcnError=Non è stato possibile aggiornare il nodo di calcolo elastico
#XMSG: Success message after deleting a Elastic Compute Node
deleteEcnSuccessMessage=Nodo di calcolo elastico eliminato
#YMSE: Error while deleting a Elastic Compute Node
deleteEcnError=Non è stato possibile eliminare il nodo di calcolo elastico
#XMSG: Success message after starting a Elastic Compute Node
startingEcnSuccessMessage=Avvio del nodo di calcolo elastico
#XMSG: Success message after stopping a Elastic Compute Node
stoppingEcnSuccessMessage=Interruzione del nodo di calcolo elastico
#YMSE: Error while starting a Elastic Compute Node
startingEcnError=Non è stato possibile avviare il nodo di calcolo elastico
#YMSE: Error while stopping a Elastic Compute Node
stoppingEcnError=Non è stato possibile interrompere il nodo di calcolo elastico
#XBUT: Add Object button for an ECN
assignObjects=Aggiungi oggetti
#XFLD: Enable auto object assignment label
enableAutoObjAssignment=Aggiungi tutti gli oggetti automaticamente
#XFLD: object type label to be assigned
objectTypeLabel=Tipo (utilizzo semantico)
#XFLD: assigned object type label
assignedObjectTypeLabel=Tipo
#XFLD: technical name label
TechnicalNameLabel=Nome tecnico
#XFLD: Object selecttion ECN label
objectSelectionECNLabel=Selezionare gli oggetti da aggiungere al nodo di calcolo elastico
#XTIT: Add objects dialog title
assignObjectsTitle=Assegna oggetti di
#XFLD: object label with object count
objectLabel=Oggetto
#XMSG: No objects available to add message.
noObjectsToAssign=Nessun oggetto disponibile per l'assegnazione.
#XMSG: No objects assigned message.
noAssignedObjects=Nessun oggetto assegnato.
#XTIT: Remove objects popup title
removeObjectsOrSpacesConfirmationTitle=Avviso
#XTIT: Delete ECN popup button and title
deleteECNConfirmationButtonAndTitle=Elimina
#XMSG: Remove objects popup text
removeObjectsConfirmation=Confermare la rimozione degli oggetti selezionati?
#XMSG: Remove spaces popup text
removeSpacesConfirmation=Confermare la rimozione degli spazi selezionati?
#XBUT: Confirmation button for objects dialog
removeObjectsOrSpacesConfirmationBtn=Rimuovi spazi
#XMSG: Success message after removing objects
removeObjectsSuccessMessage=Gli oggetti esposti sono stati rimossi
#XMSG: Success message after assigning objects
assignObjectsSuccessMessage=Gli oggetti esposti sono stati assegnati
#XFLD: auto object assignment label for space tile
autoObjAssignmentLabel=Tutti gli oggetti esposti
#XFLD: Spaces tab label
spacesTabLabel=Spazi
#XFLD: Exposed objects tab label
consumptionObjectsTabLabel=Oggetti esposti
#XMSG: Success message after unassigning spaces
spacesUnassignedSuccessMessage=Gli spazi sono stati rimossi
#XMSG: Success message after unassigning space
spaceUnassignedSuccessMessage=Lo spazio è stato rimosso
#YMSE: Error while assigning / unassigning spaces
spacesAssignUnassignError=Non è stato possibile assegnare o rimuovere gli spazi.
#YMSE: Error while removing objects
removeObjectsError=Non è stato possibile assegnare o rimuovere gli oggetti.
#YMSE: Error while removing object
removeObjectError=Non è stato possibile assegnare o rimuovere l'oggetto.
#YMSE: Error when saving the ecn with invalid value
invalidSizingPlanError=Il numero precedentemente selezionato non è più valido. Selezionare un numero valido.
#YMSE: Error when saving the ecn with invalid value
invalidPerformanceClassError=Selezionare una classe di prestazione valida.
#YMSE: Error when saving the ecn with invalid value
performanceClassNotAvailableError=La classe di prestazione "{0}" precedentemente selezionata attualmente non è valida. Selezionare la classe di prestazione valida.
#XTXT: Delete ECN confirmation text
deleteConfirmationTxtECN=Confermare l'eliminazione del nodo di calcolo elastico?
#XFLD: tooltip for ? button
help=Help
#XFLD: ECN edit button label
editECN=Configura
#XFLD: Technical type label for ERModel
DWC_ERMODEL=Modello entità-relazione
#XFLD: Technical type label for Local Table
DWC_LOCAL_TABLE=Tabella locale
#XFLD: Technical type label for Remote Table
DWC_REMOTE_TABLE=Tabella remota
#XFLD: Technical type label for Analytic Model
DWC_ANALYTIC_MODEL=Modello analitico
#XFLD: Technical type label for Task Chain
DWC_TASKCHAIN=Catena di task
#XFLD: Technical type label for Data Flow
DWC_DATAFLOW=Flusso di dati
#XFLD: Technical type label for Replication Flow
DWC_REPLICATIONFLOW=Flusso di replicazione
#XFLD: Technical type label for Transformation Flow
DWC_TRANSFORMATIONFLOW=Flusso di trasformazione
#XFLD: Technical type label for Intelligent Lookup
DWC_IDT=Ricerca intelligente
#XFLD: Technical type label for Repository
DWC_REPOSITORY_PACKAGE=Repository
#XFLD: Technical type label for Enterprise Search
DWC_ESMODEL=Ricerca aziendale
#XFLD: Technical type label for View
DWC_VIEW=Vista
#XFLD: Technical type label for Data Product
DWC_DATAPRODUCT=Prodotto dati
#XFLD: Technical type label for Data Access Control
DWC_DAC=Controllo di accesso ai dati
#XFLD: Technical type label for Folder
DWC_FOLDER=Cartella
#XFLD: Technical type label for Data Entity
DWC_BUSINESS_ENTITY=Entità aziendale
#XFLD: Technical type label for Business Entity Variant
DWC_BUSINESS_ENTITY_VARIANT=Variante entità aziendale
#XFLD: Technical type label for Responsibility Scenario
DWC_AUTH_SCENARIO=Scenario responsabilità
#XFLD: Technical type label for Fact Model
DWC_FACT_MODEL=Modello fattuale
#XFLD: Technical type label for Transformation Flow
DWC_PERSPECTIVE=Prospettiva
#XFLD: Technical type label for Consumption Model
DWC_CONSUMPTION_MODEL=Modello di consumo
#XFLD: Technical type label for Remote Connection
DWC_REMOTECONNECTION=Connessione remota
#XFLD: Technical type label for Fact Model Variant
DWC_FACT_MODEL_VARIANT=Variante modello fattuale
#XMSG: Schedule created alert message
createScheduleSuccess=Pianificazione creata
#XMSG: Schedule updated alert message
updateScheduleSuccess=Pianificazione aggiornata
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Pianificazione eliminata
#XMSG: Schedule takeover success message
ownerChangeScheduleSuccess=Pianificazione assegnata all'utente
#XMSG: Message for starting pausing of schedule
pauseScheduleStarted=Messa in pausa di 1 pianificazione
#XMSG: Message for starting resuming of schedule
resumeScheduleStarted=Ripresa di 1 pianificazione
#XFLD: Segmented button label
availableSpacesButton=Disponibile
#XFLD: Segmented button label
selectedSpacesButton=Selezionato
#XFLD: Visit website button text
visitWebsite=Visita sito Web
#XMSG: Warning message on unselecting source language
sourceLanguageRemovalWarning=La lingua di origine selezionata in precedenza verrà rimossa.
#XBUT: enable translation confirmation button
enableTranslationConfirmationBtn=Abilita
#XFLD: ECN performance class label
performanceClassLabel=Classe di prestazione
#XTXT performance class memory text
memoryText=Memoria
#XTXT performance class compute text
computeText=Calcolo
#XTXT performance class high-compute text
highComputeText=Calcolo elevato
#XBUT: Recycle Bin Button Text
recycleBin=Cestino
#XBUT: Restore Button Text
restore=Ripristina
#XMSG: Warning message for new Workload Management UI
priorityWarning=Quest'area è di sola lettura. È possibile modificare la priorità dello spazio nell'area Sistema / Configurazione / Gestione del carico di lavoro.
#XMSG: Warning message for new Workload Management UI
workloadWarning=Quest'area è di sola lettura. È possibile modificare la configurazione del carico di lavoro dello spazio nell'area Sistema / Configurazione / Gestione del carico di lavoro.
#XCOL: Space table-view column: Apache Spark vCPUs
sparkManagementVCPUs=vCPU Apache Spark
#XCOL: Space table-view column: Apache Spark Memory (GB)
sparkManagementMemory=Memoria (GB) Apache Spark
#XCOL: Space type (from Space Capability) for Data Ingestion Spaces
ingestionSpaceType=Inserimento prodotti dati
#XFLD: Deployment in progress nodatatext for spark table
sparkTableDeploymentInProgress=Nessun dato disponibile poiché lo spazio è attualmente in distribuzione
#XFLD: Runtime data being loaded nodatatext for spark table
sparkTableDeploymentLoading=Nessun dato disponibile poiché lo spazio è attualmente in caricamento
#XCOL: Space type (from Space Capability) for BW Private Cloud Edition
BWPCESpaceType=SAP BW
#XBUT: Go to Instance Mappings Button
navToMappingsButton=Modifica mappature istanza
#XFLD: SAP HANA Data Lake Storage label if less than 1GB
dataLakeStorageUsed=< 1 GB
