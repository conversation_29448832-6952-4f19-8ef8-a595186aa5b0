#DO NOT REMOVE________________________
#XFLD: Breadcrumb for space monitoring page
spaceMonitorBreadcrumb=Monitorování
#XTXT: Type name for spaces in browser tab page title
space=Prostor
#_____________________________________
#XFLD: Spaces label in
spaces=Prostory
#XFLD: Manage plan button text
manageQuotaButtonText=Spravovat plán
#XBUT: Manage resources button
manageResourcesButton=Spravovat zdroje
#XFLD: Create space button tooltip
createSpace=Vytvořit prostor
#XFLD: Create
create=Vytvořit
#XFLD: Deploy
deploy=Nasadit
#XFLD: Page
page=Stránka
#XFLD: Cancel
cancel=Zrušit
#XFLD: Update
update=Aktualizovat
#XFLD: Save
save=Uložit
#XFLD: OK
ok=OK
#XFLD: days
days=Dny
#XFLD: Space tile edit button label
edit=Upravit
#XFLD: Auto Assign all objects to space
autoAssign=Automaticky přiřadit
#XFLD: Space tile open monitoring button label
openMonitoring=Monitorovat
#XFLD: Delete
delete=Odstranit
#XFLD: Copy Space
copy=Kopírovat
#XFLD: Close
close=Zavřít
#XCOL: Space table-view column status
status=Status
#XFLD: Space status active
activeLabel=Aktivní
#XFLD: Space status locked
lockedLabel=Blokováno
#XFLD: Space status critical
criticalLabel=Kritický
#XFLD: Space status cold
coldLabel=Studený
#XFLD: Space status deleted
deletedLabel=Odstraněno
#XFLD: Space status unknown
unknownLabel=Není známo
#XFLD: Space status ok
okLabel=Stav
#XFLD: Database user expired
expired=Platnost vypršela
#XFLD: deployed
deployed=Nasazeno
#XFLD: not deployed
notDeployed=Nenasazeno
#XFLD: changes to deploy
changesToDeploy=Změny k nasazení
#XFLD: pending
pending=Nasazování
#XFLD: designtime error
designtimeError=Chyba doby návrhu
#XFLD: runtime error
runtimeError=Chyba doby běhu
#XFLD: Space created by label
createdBy=Vytvořil
#XFLD: Space created on label
createdOn=Vytvořeno dne
#XFLD: Space deployed on label
deployedOn=Nasazeno dne
#XFLD: Space ID label
spaceID=ID prostoru
#XFLD: Priority label
priority=Priorita
#XFLD: Space Priority label
spacePriority=Priorita prostoru
#XFLD: Space Configuration label
spaceConfiguration=Konfigurace prostoru
#XFLD: Not available
notAvailable=Nedostupné
#XFLD: WorkloadType default
default=Standardní
#XFLD: WorkloadType custom
custom=Vlastní
#XFLD: Data Lake option label; refers to component SAP HANA Cloud, Data Lake
dataLakeAccess=Přístup k datovému jezeru
#XFLD: Translation label
translationLabel=Překlad
#XFLD: Source language label
sourceLanguageLabel=Zdrojový jazyk
#XFLD: Translation CheckBox label
translationCheckBox=Aktivovat překlad
#XTXT: Popover hint that space has to be deployed to show user details
openDatabaseUserHintPopoverText=Nasadit prostor pro přístup k detailům uživatele.
#XTXT: Popover hint that space has to be deployed to open database explorer
openDatabaseExplorerHintPopoverText=Nasadit prostor pro otevření průzkumníka databáze.
#XTXT: Text for when the Data Lake check box is disabled
dataLakeOptionDisabled=Tento prostor nemůžete použít pro přístup k datovému jezeru, protože to již používá jiný prostor.
#XFLD: Data Lake check box text; refers to component SAP HANA Cloud, Data Lake
dataLakeCheckBox=Použijte tento prostor pro přístup k datovému jezeru.
#XFLD: Space Priority minimum label extension
low=Nízká
#XFLD: Space Priority maximum label extension
high=Vysoká
#XFLD: Space name label
spaceName=Název prostoru
#XFLD: Enable deploy objects checkbox
enableDeployObjects=Nasadit objekty
#XTIT: Copy spaces dialog title
copySpaceDialogTitle=Kopírovat {0}
#XTXT Human readable text for unselected source language
unSelectedSourceLanguage=(Nevybráno)
#XTXT Human readable text for language code "af"
af=afrikánština
#XTXT Human readable text for language code "ar"
ar=arabština
#XTXT Human readable text for language code "bg"
bg=bulharština
#XTXT Human readable text for language code "ca"
ca=katalánština
#XTXT Human readable text for language code "zh"
zh=zjednodušená čínština
#XTXT Human readable text for language code "zf"
zf=čínština
#XTXT Human readable text for language code "hr"
hr=chorvatština
#XTXT Human readable text for language code "cs"
cs=čeština
#XTXT Human readable text for language code "cy"
cy=velština
#XTXT Human readable text for language code "da"
da=dánština
#XTXT Human readable text for language code "nl"
nl=nizozemština
#XTXT Human readable text for language code "en-UK"
en-UK=angličtina (Spojené království)
#XTXT Human readable text for language code "en"
en=angličtina (USA)
#XTXT Human readable text for language code "et"
et=estonština
#XTXT Human readable text for language code "fa"
fa=perština
#XTXT Human readable text for language code "fi"
fi=finština
#XTXT Human readable text for language code "fr-CA"
fr-CA=francouzština (Kanada)
#XTXT Human readable text for language code "fr"
fr=francouzština
#XTXT Human readable text for language code "de"
de=němčina
#XTXT Human readable text for language code "el"
el=řečtina
#XTXT Human readable text for language code "he"
he=hebrejština
#XTXT Human readable text for language code "hi"
hi=hindština
#XTXT Human readable text for language code "hu"
hu=maďarština
#XTXT Human readable text for language code "is"
is=islandština
#XTXT Human readable text for language code "id"
id=Bahasa Indonesia
#XTXT Human readable text for language code "it"
it=italština
#XTXT Human readable text for language code "ja"
ja=japonština
#XTXT Human readable text for language code "kk"
kk=kazaština
#XTXT Human readable text for language code "ko"
ko=korejština
#XTXT Human readable text for language code "lv"
lv=lotyština
#XTXT Human readable text for language code "lt"
lt=litevština
#XTXT Human readable text for language code "ms"
ms=Bahasa Melayu
#XTXT Human readable text for language code "no"
no=norština
#XTXT Human readable text for language code "pl"
pl=polština
#XTXT Human readable text for language code "pt"
pt=portugalština (Brazílie)
#XTXT Human readable text for language code "pt-PT"
pt-PT=portugalština (portugalská)
#XTXT Human readable text for language code "ro"
ro=rumunština
#XTXT Human readable text for language code "ru"
ru=ruština
#XTXT Human readable text for language code "sr"
sr=srbština
#XTXT Human readable text for language code "sh"
sh=srbochorvatština
#XTXT Human readable text for language code "sk"
sk=slovenština
#XTXT Human readable text for language code "sl"
sl=slovinština
#XTXT Human readable text for language code "es"
es=španělština
#XTXT Human readable text for language code "es-MX"
es-MX=španělština (Mexiko)
#XTXT Human readable text for language code "sv"
sv=švédština
#XTXT Human readable text for language code "th"
th=thajština
#XTXT Human readable text for language code "tr"
tr=turečtina
#XTXT Human readable text for language code "uk"
uk=ukrajinština
#XTXT Human readable text for language code "vi"
vi=vietnamština
#XTIT: Delete spaces confirmation title
deleteConfirmationHeaderText=Odstranit prostory
#XTXT: Logically delete space delete confirmation text with space name
logicallyDeleteConfirmationBodyTextWithSpaceName=Opravdu chcete přemístit prostor "{0}" do koše?
#XTXT: Logically dpace delete confirmation text for multiple spaces
logicallyDeleteConfirmationBodyTextMultiple=Opravdu chcete přemístit {0} vybrané prostory do koše?
#XTXT: Delete space delete confirmation text with space name
deleteConfirmationBodyTextWithSpaceName=Opravdu chcete odstranit prostor "{0}"? Tuto akci nelze vzít zpět.
#XTXT: Delete space delete confirmation text for multiple spaces
deleteConfirmationBodyTextMultiple=Opravdu chcete odstranit {0} vybrané prostory? Tuto akci nelze vzít zpět. Následující obsah bude {1} odstraněn:
#XTXT: permanently
permanently=permanentně
#XTXT: {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDetail=Následující obsah bude {0} odstraněn a nemůže být obnoven:
#XTXT: Hint Delete space confirmation text
deleteConfirmationHint=Zadejte {0} pro potvrzení odstranění.
#XTXT: Delete space confirmation error check spelling
deleteConfirmationErrorCheckSpelling=Zkontrolujte pravopis a zkuste to znovu.
#XTXT: All Spaces
allSpaces=Všechny prostory
#XTXT: All data
allData=Všechny objekty a data obsažená v prostoru
#XTXT: All connections
allConnections=Všechna připojení definovaná v prostoru
#XFLD: Space tile selection box tooltip
clickToSelect=Kliknutím vyberte
#XTXT: All database users
allDatabaseUsers=Všechny objekty a data obsažená v kterémkoli schématu Open SQL přidruženém k prostoru
#XFLD: remove members button tooltip
deleteUsers=Odebrat členy
#XTXT: Space long description text
description=Popis (maximálně 4000 znaků)
#XFLD: Add Members button tooltip
addUsers=Přidat členy
#XFLD: Add Users button tooltip
addUsersTooltip=Přidat uživatele
#XFLD: Edit Users button tooltip
editUsersTooltip=Upravit uživatele
#XFLD: Remove Users button tooltip
removeUsersTooltip=Odebrat uživatele
#XFLD: Searchfield placeholder
filter=Hledat
#XCOL: Users table-view column health
health=Stav
#XCOL: Users table-view column access
access=Přístup
#XFLD: No user found nodatatext
noDataText=Žádný uživatel nenalezen
#XTIT: Members dialog title
selectUserDialogTitle=Přidat členy
#XTIT: User dialog title
addUserDialogTitle=Přidat uživatele
#XTIT: Delete connections confirmation title
deleteConnectionConfirmationHeaderText=Odstranit připojení
#XTIT: Delete single connection confirmation title
deleteConnectionConfirmationHeaderTextSingle=Odstranit připojení
#XTXT: Delete connection delete confirmation text
deleteConnectionConfirmationBodyText=Opravdu chcete odstranit vybraná připojení? Budou permanentně odebrána.
#XTIT: Remote source dialog title
selectRemoteConnectionDialogTitle=Vybrat připojení
#XTIT: Share connection dialog title
connectionSharingDialogTitle=Sdílet připojení
#XTIT: Connections shared from other spaces table title
sharedConnectionsFromOtherSpaces=Sdílená připojení
#XFLD: Add remote source button tooltip
addRemoteConnections=Přidat připojení
#XFLD: Delete remote source button tooltip
deleteRemoteConnections=Odebrat připojení
#XFLD: Share remote source button tooltip
shareConnections=Sdílet připojení
#XFLD: Tile-layout tooltip
tileLayout=Layout dlaždic
#XFLD: Table-layout tooltip
tableLayout=Layout tabulky
#XMSG: Success message after creating space
createSpaceSuccessMessage=Prostor vytvořen
#XMSG: Success message after copying space
copySpaceSuccessMessage=Kopírování prostoru "{0}" do prostoru "{1}"
#XMSG: Success message after deploying space
deploymentSuccessMessage=Nasazení prostoru bylo spuštěno
#XMSG: Success message after Apache Spark update started
sparkUpdateStartedSuccessMessage=Aktualizace Apache Spark zahájena
#YMSE: Error while starting Apache Spark update
sparkUpdateStartedErrorMessage=Nezdařilo se aktualizovat Apache Spark
#XMSG: Success message after updating space
updateSettingsSuccessMessage=Detaily prostoru aktualizovány
#XMSG: Success message after unlocking space
unlockSpaceSuccessMessage=Prostor dočasně odblokován
#XMSG: Success message after deleting a single space
deleteSpaceSuccessMessage=Prostor odstraněn
#XMSG: Success message after deleting multiple spaces
deleteSpacesSuccessMessage=Prostory odstraněny
#XMSG: Success message after restoring a single space
restoreSpaceSuccessMessage=Prostor obnoven
#XMSG: Success message after restoring multiple spaces
restoreSpacesSuccessMessage=Prostory obnoveny
#YMSE: Error while updating settings
updateSettingsFailureMessage=Nastavení prostoru nebylo možné aktualizovat.
#YMSE: Error while updating data lake option
dataLakeEnabledInOtherSpace=Datové jezero je již přiřazeno k jinému jezeru. K datovému jezeru může přistupovat najednou jen jeden prostor.
#YMSE: Error while updating data lake option
virtualTablesExists=Přiřazení datového jezera k tomuto prostoru nemůžete zrušit, protože existují ještě závislosti na virtuálních tabulkách*. Odstraňte virtuální tabulky, abyste mohli přiřazení datového jezera k tomuto prostoru.
#YMSE: Error while unlocking space
unlockSpaceFailureMessage=Prostor nebylo možné odblokovat.
#YMSE: Error while creating space
createSpaceError=Prostor nebylo možné vytvořit.
#YMSE: Error while creating duplicate space
createDuplicateSpaceError=Prostor s názvem {0} již existuje.
#YMSE: Error while deleting a single space
deleteSpaceError=Prostor nebylo možné odstranit.
#YMSE: Error details while deleting a single space
deleteSpaceDetailedError=Váš prostor “{0}” již správně nefunguje. Pokuste se ho znovu odstranit. Pokud stále nefunguje, požádejte administrátora, aby váš prostor odstranil, nebo otevřete tiket.
#YMSE: Error while deleting a single space in files
deleteSpaceInFilesError=Data prostoru v souborech nebylo možné odstranit.
#YMSE: Error while deleting a single space users
deleteSpaceUsersError=Uživatele nebylo možné odebrat.
#YMSE: Error while deleting a single space schema
deleteSpaceSchemasError=Schémata nebylo možné odebrat.
#YMSE: Error while deleting a single space connections
deleteSpaceConnectionsError=Připojení nebylo možné odebrat.
#YMSE: Error while deleting a single space data
deleteSpaceDataError=Data prostoru nebylo možné odstranit.
#YMSE: Error while deleting multiple spaces
deleteSpacesError=Prostory nebylo možné odstranit.
#YMSE: Error while restoring a single space
restoreSpaceError=Prostor nebylo možné obnovit.
#YMSE: Error while restoring multiple spaces
restoreSpacesError=Prostory nebylo možné obnovit.
#YMSE: Error while creating users
createUsersError=Uživatele nebylo možné přidat.
#YMSE: Error while removing users
removeUsersError=Nemohli jsme odebrat uživatele.
#YMSE: Error while removing user
removeUserError=nebylo možné odebrat uživatele.
#YMSE: Error assigning roles to a user
roleAssignUnAssignError=Nemohli jsme k vybraným rolím z rozsahu přidat uživatele.\n\n Do role z rozsahu se nemůžete přidat. Můžete požádat svého administrátora, aby vás přidal do role z rozsahu.
#YMSE: Error assigning user to the space
userAssignError=Nemohli jsme přiřadit uživatele k prostoru. \n\n Uživatel je již přiřazen k maximálnímu povolenému počtu prostorů (100) v rámci vymezených rolí.
#YMSE: Error assigning users to the space
usersAssignError=Nemohli jsme přiřadit uživatele k prostoru. \n\n Uživatel je již přiřazen k maximálnímu povolenému počtu prostorů (100) v rámci vymezených rolí.
#YMSE: Error while fetching users
fetchUserWithScopedRolesError=Nemohli jsme načíst uživatele. Zkuste to znovu později.
#YMSE: Error while fetching scoped roles
fetchScopedRolesError=Nemohli jsme načíst role z rozsahu.
#YMSE: Error while fetching members
fetchUserError=Členy nebylo možné vyvolat. Zkuste to znovu později.
#YMSE: Error while loading run-time database
loadRuntimeError=Nemohli jsme načíst informace z databáze v reálném čase.
#YMSE: Error while loading spaces
loadSpacesError=Něco se bohužel pokazilo, když jste se snažili vyvolat vaše prostory.
#YMSE: Error while loading haas resources
loadStorageError=Něco se bohužel pokazilo, když jste se snažili vyvolat data úložiště.
#YMSE: Error no data could be loaded
loadDataError=Něco se bohužel pokazilo, když jste se snažili vyvolat vaše data.
#XFLD: Click to refresh storage data
clickToRefresh=Kliknutím sem to zkuste znovu.
#XTIT: Delete space popup title
deleteSpacePopupTitle=Odstranit prostor
#XCOL: Spaces table-view column name
name=Název
#XCOL: Spaces table-view deployment status
deploymentStatus=Status nasazení
#XFLD: Disk label in space details
storageLabel=Disk (GB)
#XFLD: In-Memory label in space details
ramLabel=Paměť (GB)
#XFLD: Memory label on space card
memory=Paměť pro úložiště
#XFLD: Storage Assignment label in space details
storrageAssignmentLabel=Úložiště prostoru
#XFLD: Storage Type label in space details
storageTypeLabel=Typ úložiště
#XFLD: Enable Space Quota
enableSpaceQuota=Aktivovat kvótu prostoru
#XFLD: No Space Quota
noSpaceQuota=Žádná kvóta prostoru
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
diskAndInMemoryText=Databáze SAP HANA (disk a vnitřní paměť)
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
dataLakeFilesText=Soubory datového jezera SAP HANA
#XFLD: Available scoped roles label
availableRoles=Dostupné role z rozsahu
#XFLD: Selected scoped roles label
selectedRoles=Vybrané role z rozsahu
#XCOL: Spaces table-view column models
models=Modely
#XCOL: Spaces table-view column users
users=Uživatelé
#XCOL: Spaces table-view column connections
connections=Připojení
#XFLD: Section header overview in space detail
overview=Přehled
#XFLD: Section header Apache Spark in space detail
sparkManagementSection=Apache Spark
#XFLD: Section header Apache Spark in space detail
sparkManagementSubSection=Aplikace
#XFLD: Section header Apache Spark in space detail
taskAssignmentSubSection=Přiřazení úlohy
#XFLD: vCPU label in Apache Spark section
vCPULabel=vCPUs
#XFLD: Memory label in Apache Spark section
memoryLabel=Paměť (GB)
#XFLD: space configuration label in Apache Spark section
spaceConfigurationLabel=Konfigurace prostoru
#XFLD: Space Source label
sparkApplicationLabel=Aplikace
#XFLD: Cluster Size label
clusterSizeLabel=Velikost clusteru
#XFLD: Driver label
driverLabel=Ovladač
#XFLD: Executor label
executorLabel=Provádějící
#XFLD: max label
maxLabel=Max. využito
#XFLD: TrF Default label
trFDefaultLabel=Standard toku transformace
#XFLD: Merge Default label
mergeDefaultLabel=Standardní sloučení
#XFLD: Optimize Default label
optimizeDefaultLabel=Optimalizovat standard
#XFLD: Deployment Default label
deploymentDefaultLabel=Nasazení lokální tabulky (souboru)
#XFLD: max spark source label
driverExecutorMaxUsedSparkSource={0} CPU / {1} GB
#XFLD: Object type label
taskObjectTypeLabel=Typ objektu
#XFLD: Task activity label
taskActivityLabel=Činnost
#XFLD: Task Application ID label
taskApplicationIDLabel=Standardní aplikace
#XFLD: Section header in space detail
generalSettings=Obecná nastavení
#XMSG: Message strip message when a space is locked
spaceLockedMessageStrip=Tento prostor je momentálně blokován systémem.
#XMSG: Message strip message deploy warning
deployWarningMessageStrip=Změny v tomto výběru budou nasazeny okamžitě.
#XMSG: Message strip message modify space configuration warning
modifySpaceConfigurationMessageStrip=Uvědomte si, že změna těchto hodnot může způsobit problémy s výkonem.
#XFLD: Button text to unlock the space again
unlockSpace=Odblokovat prostor
#XFLD: Info text for audit log formatted message
auditLogText=Povolte protokoly auditu pro záznam akcí čtení nebo změn (zásady auditu). Správci pak mohou analyzovat, kdo v jakém okamžiku jakou akci provedl.
#XMSG: Message strip  audit warning
auditLogWarningMessageStrip=Protokoly auditu mohou spotřebovat velké množství diskového prostoru vašeho tenanta. Pokud povolíte zásadu auditu (akce čtení nebo změny), měli byste pravidelně monitorovat využití diskového úložiště (prostřednictvím karty Disk Storage Used v nástroji Sledování systému), abyste se vyhnuli úplným výpadkům disku, což může vést k přerušení služeb. Pokud zakážete zásadu auditu, všechny její položky protokolu auditu budou odstraněny. Pokud chcete zachovat záznamy protokolu auditu, zvažte jejich export, než zakážete zásady auditu.
#XFLD: Message strip link text to open in app help to guide user what to do to unlock his space permanently
unlockSpaceHelp=Zobrazit nápovědu
#XMSG: Message strip message when a space will be locked again
spaceTemporarilyUnlockedMessageStrip=Tento prostor překračuje své úložiště a bude blokován za {0} {1}.
#XMSG: Unit for remaining time until space is locked again
hours=hodiny
#XMSG: Unit for remaining time until space is locked again
minutes=minuty
#XFLD: Section header in space detail for auditing
detailsSectionAuditing=Audit
#XFLD: Subsection header in space detail for auditing
auditing=Nastavení auditu prostoru
#XFLD: Hot space tooltip
hotSpaceCountTooltip=Kritické prostory: Použité úložiště je více než 90%
#XFLD: Green space tooltip
greenSpaceCountTooltip=Zdravé prostory: Použité úložiště je mezi 6% a 90%
#XFLD: Cold space tooltip
coldSpaceCountTooltip=Chladné prostory: Použité úložiště je 5% nebo méně.
#XFLD: Critical space tooltip
criticalSpaceCountTooltip=Kritické prostory: Použité úložiště je více než 90%
#XFLD: Green space tooltip
okSpaceCountTooltip=Zdravé prostory: Použité úložiště je mezi 6% a 90%
#XFLD: Locked space tooltip
lockedSpaceCountTooltipNew=Blokované prostory: Blokováno kvůli nedostatku paměti.
#XFLD: Locked space tooltip
lockedSpaceCountTooltip=Blokované prostory
#YMSE: Error while deleting remote source
deleteRemoteError=Připojení nebylo možné odebrat.
#XFLD: Space ID input tooltip
spaceIDInfoTooltip=ID prostoru nelze později změnit.\nPlatné znaky A - Z, 0 - 9, a _
#YMSE: Error when space name is empty
spaceNameEmptyValidationError=Zadejte název prostoru.
#YMSE: Error when ECN Business name is empty
ecnNameEmptyValidationError=Zadejte business název.
#YMSE: Error when space id is empty
spaceIDInputEmptyValidationError=Zadejte ID prostoru.
#YMSE: Error when space id contains invalid characters
spaceIDInputInValidError=Neplatné znaky. Používejte jen A - Z, 0 - 9, a _.
#YMSE: Error when space id exists while creation
spaceIDInputDuplicateError=ID prostoru již existuje.
#XFLD: Space searchfield placeholder
search=Hledat
#XMSG: Success message after creating users
createUsersSuccess=Uživatelé přidáni
#XMSG: Success message after creating user
createUserSuccess=Uživatel přidán
#XMSG: Success message after updating users
updateUsersSuccess={0} uživatelé aktualizováni
#XMSG: Success message after updating user
updateUserSuccess=Uživatel aktualizován
#XMSG: Success message after removing users
removeUsersSuccess={0} uživatelé odebráni
#XMSG: Success message after removing user
removeUserSuccess=Uživatel odebrán
#XFLD: Schema name
schemaName=Název schématu
#XFLD: used of total
ofTemplate={0} z {1}
#XFLD: Assigned disk label in space overview
assignedStorageTemplate=Přiřazený disk ({0} z {1})
#XFLD: Assigned Memory label in space overview
assignedRAMTemplate=Přiřazená paměť ({0} z {1})
#XFLD: Storage ratio on space
accelearationRAM=Zrychlení paměti
#XFLD: No Storage Consumption
noStorageConsumptionText=Nebyla přiřazena kvóta úložiště.
#XFLD: Used disk label in space overview
usedStorageTemplate=Disk využitý pro úložiště ({0} z {1})
#XFLD: Used Memory label in space overview
usedRAMTemplate=Paměť využitá pro úložiště ({0} z {1})
#XFLD: Used Disk tooltip
usedStorageTooltipTemplate={0} z {1} disku využito pro úložiště
#XFLD: Used Memory tooltip
usedRAMTooltipTemplate={0} z {1} paměti využito
#XFLD: Assigned Disk tooltip
assignedStorageTooltipTemplate={0} z {1} disku přiřazeno
#XFLD: Assigned Memory tooltip
assignedRAMTooltipTemplate={0} z {1} paměti přiřazeno
#XFLD: Space related used storage tooltip
spaceRelatedUsedStorageTooltipTemplate=Data prostoru: {0}
#XFLD: Not space related used storage tooltip
otherDataUsedStorageTooltipTemplate=Jiný data: {0}
#XFLD: Storage used exceeded popover and tooltip
storageUsedExceededText=Zvažte rozšíření vašeho plánu nebo kontaktujte podporu SAP.
#XCOL: Space table-view column used Disk
usedStorage=Disk využitý pro úložiště
#XCOL: Space monitor column used Memory
usedRAM=Paměť využitá pro úložiště
#XCOL: Space monitor column Schema
tableSchema=Schéma
#XCOL: Space monitor column Storage Type
tableStorageType=Typ úložiště
#XCOL: Space monitor column Table Type
tableType=Typ tabulky
#XCOL: Space monitor column Record Count
tableRecordCount=Počet záznamů
#XFLD: Assigned Disk
assignedStorage=Disk přiřazený pro úložiště
#XFLD: Assigned Memory
assignedRAM=Paměť přiřazená pro úložiště
#XCOL: Space table-view column storage utilization
tableStorageUtilization=Využité úložiště
#XFLD: space status
spaceStatus=Status prostoru
#XFLD: space type
spaceType=Typ prostoru
#XFLD: space type label for space with space type "SAP BW bridge" ("SAP BW bridge" is a product name, do not translate it)
abapbridgeSpaceType=Most SAP BW
#XFLD: space type label for space with space type "Data Marketplace"
marketplaceSpaceType=Produkt poskytovatele dat
#XMSG: Error message for when selected space cannot be deleted due to the space type
deleteTypedSpaceError=Nemůžete odstranit prostor {0}, protože má typ prostoru {1}.
#XMSG: Error message for when selected spaces cannot be deleted due to the space type
deleteTypedSpacesError=Nemůžete odstranit {0} vybrané prostory. Nelze odstranit prostory následujícího typu: {1}.
#XFLD: space type label for space without space type
defaultSpaceType=SAP Datasphere
#XFLD: Tooltip for monitor space button
monitorSpace=Monitorovat
#XFLD: Tooltip for edit space button
editSpace=Upravit prostor
#XMSG: Deletion warning in messagebox
deleteConfirmation=Opravdu chcete odstranit tento prostor?
#XFLD: Tooltip for delete space button
deleteSpace=Odstranit prostor
#XFLD: storage
storage=Disk pro úložiště
#XFLD: username
userName=Uživatelské jméno
#XFLD: port
port=Port
#XFLD: hostname
hostName=Název hostitele
#XFLD: password
password=Heslo
#XBUT: Request new password button
requestPassword=Požadovat nové heslo
#YEXP: Usage explanation in time data section
timeDataSectionHint=Vytvořte časové tabulky a dimenze pro použití ve vašich modelech a story.
#YEXP: Usage explanation in Space schema section
spaceSchemaHint=Chcete, aby bylo možné data ve vašem prostoru spotřebovávat jinými nástroji nebo aplikacemi? Pokud ano, vytvořte jednoho nebo více uživatelů, kteří budou moci přistupovat k datům ve vašem prostoru, a zvolte, zda chcete, aby bylo možné všechna budoucí data prostoru standardně spotřebovávat.
#XTIT: Create schema popup title
createSchemaDialogTitle=Vytvořit schéma Open SQL
#XTIT: Generate Time Data dialog title
generateTimeDataDialogTitle=Vytvořit časové tabulky a dimenze
#XTIT: Edit Time Data dialog title
editTimeDataDialogTitle=Upravit časové tabulky a dimenze
#XTIT: Time Data token title
timeDataTokenTitle=Časová data
#XTIT: Time Data token title
timeDataUpdateViews=Aktualizovat pohledy na časová data
#XFLD: Time Data token creation in progress label
timeDataCreationInProgress=Vytváření probíhá...
#XFLD: Time Data token creation error label
timeDataCreationError=Vytvoření se nezdařilo. Zkuste to znovu.
#XTIT: Time Data dialog time settings title
timeDataDialogTimeSettingsTitle=Nastavení časové tabulky
#XTIT: Time Data dialog time data table title
timeDataDialogTranslationTablesTitle=Převodové tabulky
#XTIT: Time Data dialog dimensions title
timeDataDialogTimeViewsTitle=Časové dimenze
#XFLD: Time Data dialog time range label
timeRangeHint=Definujte časový interval.
#XFLD: Time Data dialog time data table label
timeDataHint=Pojmenujte vaši tabulku.
#XFLD: Time Data dialog time data dimensions label
timeDimensionsHint=Pojmenujte vaše dimenze.
#XFLD: Time Data Time range description label
timerangeLabel=Časový interval
#XFLD: Time Data dialog from year label
fromYearLabel=Od roku
#XFLD: Time Data dialog to year label
toYearLabel=Do roku
#XFLD: Time Data dialog calendar type label
calendarTypeLabel=Typ kalendáře
#XFLD: Time Data dialog granularity label
granularityLabel=Granularita
#XFLD: Time Data dialog technical name label
technicalNameLabel=Technický název
#XFLD: Time Data dialog quarters text table lable
quarterTextsTableLabel=Převodová tabulka pro čtvrtletí
#XFLD: Time Data dialog months text table lable
monthsTextsTableLabel=Převodová tabulka pro měsíce
#XFLD: Time Data dialog days text table lable
daysTextsTableLabel=Převodová tabulka pro dny
#XFLD: Time Data dialog year label
yearLabel=Dimenze rok
#XFLD: Time Data dialog quarter label
quarterLabel=Dimenze čtvrtletí
#XFLD: Time Data dialog month label
monthLabel=Dimenze měsíc
#XFLD: Time Data dialog day label
dayLabel=Dimenze den
#XFLD: Time Data dialog gregorian calendar type label
gregorian=Gregoriánský
#XFLD: Time Data dialog time granularity day label
day=Den
#XFLD: Time Data dialog business name maxlength reached
timeRangeValueStateTextMaxLengthBusinessName=Dosaženo maximální délky 1000 znaků.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextMaxRangeExceeded=Maximální časový interval je 150 roků.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeLower="Od roku" musí být nižší než "Do roku"
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeHigher="Od roku" musí být 1900 nebo vyšší.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeHigher="Do roku" musí být vyšší než "Od roku"
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeLower="Do roku" musí být nižší, než aktuální rok plus 100
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningFromYear=Zvyšující se "Od roku" může vést ke ztrátě dat
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningToYear=Snižující se "Do roku" může vést ke ztrátě dat
#XMSG: Time Data creation validation error message
timeDataValidationError=Zdá se, že některá pole jsou neplatná. Před pokračováním zkontrolujte požadovaná pole.
#XMSG: Time Data deletion popup text
deleteTimeDataConfirmation=Opravdu chcete odstranit data?
#XMSG: Time Data creation success message
createTimeDataSuccess=Časová data vytvořena
#XMSG: Time Data update success message
updateTimeDataSuccess=Časová data aktualizována
#XMSG: Time Data delete success message
deleteTimeDataSuccess=Časová data odstraněna
#XMSG: Time Data creation error message
createTimeDataError=Něco se pokazilo při pokusu o vytvoření časových dat.
#XMSG: Time Data update error message
updateTimeDataError=Něco se pokazilo při pokusu o aktualizaci časových dat.
#XMSG: Time Data creation error message
deleteTimeDataError=Něco se pokazilo při pokusu o odstranění časových dat.
#XMSG: Error message when time data could not be retrieved
retrievingTimeDataError=Časová data nebylo možné načíst.
#XTIT: Time Data deletion popup title
deleteTimeDataConfirmationTitle=Upozornění
#XTXT: Time Data deletion dependency popup text
deleteTimeDataDependencyText=Nemohli jsme odstranit vaše časová data, protože se používají v jiných modelech.
#XTXT: Time Data deletion dependency popup text singular
deleteTimeDataDependencyTextSingular=Nemohli jsme odstranit vaše časová data, protože se používají v jiném modelu.
#XMSG: Validation error when required time data form field is empty
timeDataRequiredField=Toto pole je povinné a nemůže zůstat prázdné.
#XFLD: Tooltip for Open in Database Explorer button
openInDBX=Otevřít v průzkumníku databáze
#YMSE: Dimension Year
dimensionYearView=Dimenze "Rok"
#YMSE: Dimension Year
dimensionQuarterView=Dimenze "Čtvrtletí"
#YMSE: Dimension Year
dimensionMonthView=Dimenze "Měsíc"
#YMSE: Dimension Year
dimensionDayView=Dimenze "Den"
#XFLD: Time Data deletion object title
timeDataUsedIn=(použito v {0} modelech)
#XFLD: Time Data deletion object title singular
timeDataUsedInSingular=(použito v 1 modelu)
#XFLD: Time Data deletion table column provider
provider=Poskytovatel
#XTIT: Time Data dependency dialog title
timeDataDependencyDialogTitle=Závislosti
#XTIT: Create schema popup title
createSpaceSchemaUserDialogTitle=Vytvořit uživatele pro schéma prostoru
#XFLD: Create schema button
createSchemaButton=Vytvořit schéma Open SQL
#XFLD: Generate TimeData button
generateTimeDataButton=Vytvořit časové tabulky a dimenze
#XFLD: Show dependencies button
showDependenciesButton=Zobrazit závislosti
#XFLD: tooltip for disabled buttons when user is not member of space
mustBeMemberOfSpaceForOperation=K provedení této operace musí být váš uživatele členem prostoru.
#XFLD: Create space schema user button
createSpaceSchemaUserButton=Vytvořit uživatele schématu prostoru
#YMSE: API Schema users load error
loadSchemaUsersError=Seznam uživatelů nebylo možné načíst.
#XTIT: API Schema-config popup title
schemaUserDetailsDialogTitle=Detaily uživatele schématu prostoru
#XMSG: API Schema user deletion popup text
deleteSpaceSchemaUserConfirmation=Opravdu chcete odstranit vybraného uživatele?
#XMSG: API Schema user deletion confirmation text
userDeleteSuccess=Uživatel odstraněn.
#YMSE: API Schema user deletion error
userDeleteError=Uživatele nebylo možné odstranit.
#XFLD: User deleted
userDeleted=Uživatel byl odstraněn
#XTIT: Remove user popup title
removeUserConfirmationTitle=Upozornění
#XMSG: Remove user popup text
removeUserConfirmation=Opravdu chcete odebrat uživatele? Uživatel a jeho přiřazené role z rozsahu budou z prostoru odebrány.
#XMSG: Remove users popup text
removeUsersConfirmation=Opravdu chcete odebrat uživatele? Uživatelé a jejich přiřazené role z rozsahu budou z prostoru odebrány.
#XBUT: Confirmation button for remove user dialog
removeUserConfirmationBtn=Odebrat
#YMSE: No data text for available roles
noDataAvailableRoles=Tento prostor není přidán do žádné role z rozsahu. \n Aby bylo možné do prostoru přidat uživatele, musí být prostor nejprve přidán do jedné nebo více rolí z rozsahu.
#YMSE: No data text for selected roles
noDataSelectedRoles=Žádné vybrané role z rozsahu
#XTIT: Schema-config popup title
schemaDetailsDialogTitle=Detaily konfigurace schématu Open SQL
#XFLD: Label for Read Audit Log
auditLogRead=Aktivovat protokol auditu pro operace čtení
#XFLD: Label for Change Audit Log
auditLogChange=Aktivovat protokol auditu pro operace změny
#XFLD: Label Audit Log Retention
auditLogRetention=Uchovat protokoly po dobu
#XFLD: Label Audit Log Retention Unit
retentionUnit=Dny
#YMSE: Error when log retention time is set incorrectly
incorrectRetentionTime=Zadejte celé číslo mezi {0} a {1}
#XFLD: Label for Cross Space Schema Access
spaceSchemaAccess=Spotřebovat data schématu prostoru
#XTIT: Confirm deactivation of space schema consumption dialog title
disableSpaceSchemaConsumptionDialogTitle=Zastavit spotřebu dat schématu prostoru
#XTXT: Confirm deactivation of space schema consumption dialog text
disableSpaceSchemaConsumptionDialogText=Toto schéma Open SQL může spotřebovávat data vašeho schématu prostoru. Když zastavíte spotřebu, modely na bázi dat schématu prostoru již nemusí dále fungovat.
#XBUT: Confirmation button for the stop consuming space schema data dialog
stopConsuming=Zastavit spotřebu
#XTXT: Tooltip for data lake icon
dataLakeIconTooltip=Tento prostor se používá pro přístup k datovému jezeru
#XFLD: Tooltip for data lake table entry
dataLakeTableEntryTooltip=Datové jezero aktivováno
#XFLD: used In-Memory linit reached tag
usedRamLimitReachedLabel=Dosaženo limitu paměti
#XFLD: Space storage Limit reached tag
storageLimitReachedLabel=Dosaženo limitu úložiště
#XFLD: Minimal Storage Limit Reached tag
minStorageLimitReachedLabel=Dosaženo minimálního limitu úložiště
#XFLD: Space ram tag
ramLimitReachedLabel=Dosaženo limitu paměti
#XFLD: Minimal In-Memory Limit Reached tag
minRamLimitReachedLabel=Dosaženo minimálního limitu paměti
#XFLD: Popover text after click on space storage limit warning
storageLimitReachedText=Dosáhli jste limitu úložiště přiřazeného prostoru {0}. Přiřaďte k prostoru větší úložiště.
#XFLD: System storage tag
systemStorageLimitReachedLabel=Dosaženo limitu systémového úložiště
#XMSG: Popover text after click on storage limit warning
systemStorageLimitReachedText=Dosáhli jste limitu systémového úložiště {0}. Nyní nemůžete k prostoru přiřadit větší úložiště.
#XMSG: Schema deletion warning
deleteSchemaConfirmation=Odstraněním tohoto schématu open SQL se permanentně odstraní také všechny uložené objekty a zpracovaná přidružení ve schématu. Pokračovat?
#XMSG: Success message after deleting a schema
schemaDeleteSuccess=Schéma odstraněno
#YMSE: Error while deleting schema.
schemaDeleteError=Schéma nebylo možné odstranit.
#XMSG: Success message after update a schema
schemaUpdateSuccess=Schéma aktualizováno
#YMSE: Error while updating schema.
schemaUpdateError=Schéma nebylo možné aktualizovat.
#XMSG: Hint when no password is shown in the dialog.
newPasswordHint=Pro toto schéma jsme poskytli heslo. Pokud jste vaše heslo zapomněli nebo ztratili, můžete si vyžádat nové. Nezapomeňte nové heslo zkopírovat nebo uložit.
#XMSG: Hint when showing the password in the dialog.
savePasswordHint=Zkopírujte vaše heslo. Budete ho potřebovat ke konfiguraci připojení k tomuto schématu. Pokud jste vaše heslo zapomněli, můžete otevřít tento dialog pro jeho resetování.
#XFLD,50: Hint on entering schema name in creation dialog
schemaNameHint=Po vytvoření schématu nelze tento název změnit.
#XFLD: Open SQL Schemas section sub headline
schemasSQL=Open SQL
#XFLD: Space schema section sub headline
schemasSpace=Prostor
#XFLD: HDI Container section header
HDIContainers=Kontejnery HDI
#XTXT: Add HDI Containers button tooltip
addHDIContainers=Přidat kontejnery HDI
#XTXT: Remove HDI Containers button tooltip
removeHDIContainers=Odebrat kontejnery HDI
#XFLD: Enable HDI Containers feature button
enableHDIContainersButton=Umožnit přístup
#YMSE: No data text for HDI Containers table
noDataHDIContainers=Žádné kontejnery HDI nebyly přidány.
#YMSE: No data text for Timedata section
noDataTimedata=Žádné časové tabulky a dimenze nebyly vytvořeny
#YMSE: No data text during runtime not available for Timedata section
runtimeNotAvailableNoDataTimedata=Tabulky a dimenze času nelze načíst, protože databáze doby běhu není k dispozici.
#YMSE: No data text during runtime not available for HDI section
runtimeNotAvailableNoDataHDI=Kontejnery HDI nelze načíst, protože databáze doby běhu není k dispozici.
#YMSE: Error while fetching HDI Containers
fetchHDIContainersError=Kontejnery HDI nebylo možné získat. Zkuste to znovu později.
#XFLD Table column header for HDI Container names
HDIContainerName=Název kontejneru HDI
#XFLD: Enable HDI Containers feature dialog title
enableHDIContainersDialogTitle=Umožnit přístup
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText1New=Můžete aktivovat SAP SQL Data Warehousing ve vašem tenantu SAP Datasphere pro výměnu dat mezi vašimi kontejnery HDI a vašimi prostory SAP Datasphere, aniž byste museli přesouvat data.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText2=Otevřete k tomu tiket pro podporu kliknutím na níže umístěné tlačítko.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText3New=Po zpracování vašeho tiketu musíte sestavit jeden nebo více nových kontejnerů HDI v databázi SAP Datasphere v reálném čase. Pak se tlačítko Povolit přístup nahradí tlačítkem + v sekci Kontejnery HDI pro všechny vaše prostory SAP Datasphere a vy budete moci přidávat své kontejnery do prostoru.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText41=Potřebujete další informace? Přejděte k %%0. Detailní informace o tom, co se má zahrnout do tiketu, najdete v %%1.
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkText=Nápověda SAP
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkTextSapNote3057059=Pokyn SAP 3057059
#XBUT: Open Ticket Button Text
openTicket=Otevřít tiket
#XBUT: Add Button Text
add=Přidat
#XBUT: Next Button Text
next=Další
#XBUT: Edit Button Text
editUsers=Upravit
#XBUT: create user Button Text
createUser=Vytvořit
#XBUT: Update user Button Text
updateUser=Vybrat
#XTIT: Add HDI Containers Dialog Title
addHDIContainersDialogTitle=Přidat nepřiřazené kontejnery HDI
#XFLD: No data text for add HDI container dialog
addHDIContainersDialogNoDataText=Nemohli jsme najít žádné nepřiřazené kontejnery. \nVámi hledaný kontejner je možná už k prostoru přiřazen.
#YMSE: Error while loading assigned HDI Containers
loadAssignedHDIContainersError=Přiřazené kontejnery HDI nebylo možné načíst.
#YMSE: Error while loading assigned HDI Containers
loadUnassignedHDIContainersError=Kontejnery HDI nebylo možné načíst.
#XMSG: Success message
succeededToAddHDIContainer=Kontejner HDI přidán
#XMSG: Success message
succeededToAddHDIContainerPlural=Kontejnery HDI přidány
#XMSG: Success message
succeededToDeleteHDIContainer=Kontejner HDI odebrán
#XMSG: Success message
succeededToDeleteHDIContainerPlural=Kontejnery HDI odebrány
#XFLD: Time data section sub headline
timeDataSection=Časové tabulky a dimenze
#XFLD: Schemas list entry read audit log enabled indicator
readIndicator=Číst
#XFLD: Schemas list entry change audit log enabled indicator
changeIndicator=Změnit
#XFLD: Remote sources section sub headline
allconnections=Přiřazení připojení
#XFLD: Remote sources section sub headline
localconnections=Lokální připojení
#XFLD: Remote sources section sub headline for open Connectors
openConnectors=SAP Open Connectors
#XFLD: User section sub headline
memberassignment=Přiřazení členů
#XFLD: User assignment section sub headline
userAssignment=Přiřazení uživatele
#XFLD: User section Access dropdown Member
member=Člen
#XFLD: User assignment section column name
user=Uživatelské jméno
#XTXT: Selected role count
selectedRoleToolbarText=Vybráno: {0}
#XTIT: Space detail section connections title
detailsSectionConnections=Připojení
#XTIT: Space detail section data access title
detailsSectionDataAccess=Přístup ke schématu
#XTIT: Space detail section time data title
detailsSectionGenerateData=Časová data
#XTIT: Space detail section members title
detailsSectionUsers=Členové
#XTIT: Space detail section Users title
detailsSectionUsersTitle=Uživatelé
#XTIT: Out of Storage
insufficientStoragePopoverTitle=Nedostatek místa v úložišti
#XTIT: Storage distribution
storageDistributionPopoverTitle=Využité úložiště na disku
#XTXT: Out of Storage popover text
insufficientStorageText=Pro vytvoření nového prostoru zmenšete přiřazené úložiště jiného prostoru nebo odstraňte prostor, který již nepotřebujete.Vaše celkové systémové úložiště můžete zvětšit vyvoláním správy plánu.
#XMSG: Space id length warning
spaceIdLengthWarning=Překročen maximální počet {0} znaků.
#XMSG: Space name length warning
spaceNameLengthWarning=Překročen maximální počet {0} znaků.
#XMSG: Space id reserved prefix warning
spaceIdnWarningReservedPrefix=Nepoužívejte prefix {0}, abyste zabránili možným konfliktům.
#YMSE: Error while loading open SQL schemas
loadLocalSchemasError=Schémata Open SQL nebylo možné načíst.
#YMSE: Error while creating open SQL schema
createLocalSchemaError=Schéma Open SQL nebylo možné vytvořit.
#YMSE: Error while loading all remote connections
loadRemoteConnectionsError=Nebylo možné načíst všechny vzdálená připojení.
#YMSE: Error while loading space details
loadSpaceDetailsError=Nebylo možné načíst detaily prostoru.
#YMSE: Error while deploying space details
deploySpaceDetailsError=Prostor nebylo možné nasadit.
#YMSE: Error while copying space details
copySpaceDetailsError=Prostor nebylo možné zkopírovat.
#YMSE: Error while loading storage data
loadStorageDataError=Nebylo možné načíst data úložiště.
#YMSE: Error while loading all users
loadAllUsersError=Nebylo možné načíst všechny uživatele.
#YMSE: Failed to reset password
resetPasswordError=Heslo nebylo možné resetovat.
#XMSG: Success message after updating space
resetPasswordSuccessMessage=Nové heslo pro schéma nastaveno
#YMSE: DP Agent-name too long
DBAgentNameError=Název agenta DP je příliš dlouhý.
#YMSE: Schema-name not valid.
schemaNameError=Název schématu je neplatný.
#YMSE: User name not valid.
UserNameError=Uživatelské jméno je neplatné.
#XTIT: Consumption by Storage Type
consumptionStorageTypeText=Spotřeba podle typu úložiště
#XTIT: Consumption by Schema
consumptionSchemaText=Spotřeba podle schématu
#XTIT: Overall Consumption by schema
overallConsumptionSchemaText=Celková spotřeba tabulky podle schématu
#XTIT: Overall Consumption by Table Type
overallConsumptionTableTypeText=Celková spotřeba podle typu tabulky
#XTIT: Tables
tableDetailsText=Detaily tabulky
#XTIT: Table Storage Consumption
tableStorageConsumptionText=Spotřeba úložiště tabulky
#XFLD: Table Type label
tableTypeLabel=Typ tabulky
#XFLD: Schema label
schemaLabel=Schéma
#XFLD: reset table tooltip
resetTable=Resetovat tabulku
#XFLD: In-Memory label in space monitor
inMemoryLabel=Paměť
#XFLD: Disk label in space monitor
diskLabel=Disk
#XFLD: Yes
yesLabel=Ano
#XFLD: No
noLabel=Ne
#XFLD: switch label: Want the data in this space to be consumable by default?
allowConsumptionByDefault=Chcete, aby bylo standardně možné, data v tomto prostoru spotřebovávat?
#XFLD: Business Name
businessNameLabel=Business název
#XFLD: Refresh
refresh=Aktualizovat
#XMSG: No filter results title
noFilterResultsTitle=Zdá se, že při vašem nastavení filtru se nezobrazují žádná data.
#XMSG: No filter results message
noFilterResultsMsg=Zkuste upřesnit vaše nastavení filtru a pokud stále nevidíte žádná data, vytvořte nějaké tabulky v editoru dat. Jakmile budou spotřebovávat úložiště, budete je zde moci monitorovat.
#XMSG: Space monitoring Hana state red title
spaceMonitorHanaRedTitle=Databáze v reálném čase není k dispozici.
#XMSG: Space monitoring Hana state red message
spaceMonitorHanaRedMsg=Protože databáze v reálném čase není k dispozici, jsou určité funkce deaktivovány a nemůžeme na této stránce zobrazit žádné informace.
#YMSE: Error while creating space schema user
createSpaceSchemaUserError=Uživatele schématu prostoru nebylo možné vytvořit.
#YMSE: Error User name already exists
userAlreadyExistsError=Uživatelské jméno již existuje.
#YMSE: Error Authentication failed
authenticationFailedError=Autentizace se nezdařila.
#XMSG: Message strip message when a schema user is locked
userLockedMessageStrip=Uživatel je blokován kvůli příliš mnoha nezdařeným přihlášením. Požádejte o nové heslo pro odblokování uživatele.
#XMSG: Success message after reset and unlock schema user
resetUnlockPasswordSuccessMessage=Nové heslo nastaveno a uživatel odblokován
#XMSG: user is locked message
userLockedMessage=Uživatel je blokován.
#XCOL: Users table-view column Role
spaceRole=Role
#XCOL: Users table-view column Scoped Role
spaceScopedRole=Role z rozsahu
#XCOL: Users table-view column Space Admin
spaceAdmin=Správce prostoru
#XFLD: User section dropdown value Viewer
viewer=Prohlížeč
#XFLD: User section dropdown value Modeler
modeler=Modeler
#XFLD: User section dropdown value Data Integrator
dataIntegrator=Integrátor dat
#XFLD: User section dropdown value Space Administrator
spaceAdministrator=Správce prostoru
#XMSG: Success message -  Space Role updated successfully.
updateUserSpaceRoleSuccess=Role prostoru aktualizována
#XMSG: Error message - Failed to update Space Role.
updateUserSpaceRoleError=Role prostoru nebyla úspěšně aktualizována.
#XFLD:
databaseUserNameSuffix=Sufix jména uživatele databáze
#XTXT: Space Schema password text
spaceSchemaPasswordText=Pro nastavení spojení s tímto schématem zkopírujte vaše heslo. Pokud jste vaše heslo zapomněli, můžete vždy požádat o nové.
#XFLD: Cloud Platform label in space schema dialog
cloudPlatformLabel=Cloudová platforma
#XTXT: Cloud Platform text in space schema dialog
cloudPlatformText=Pro nastavení přístupu skrze tohoto uživatele aktivujte spotřebu a zkopírujte přihlašovací údaje. Pokud můžete jen zkopírovat přihlašovací údaje bez hesla, zajistěte, abyste heslo přidali později.
#XFLD: enable consumption in CloudPlatform label in space schema dialog
enableConsumptionInCloudPlatformLabel=Aktivovat spotřebu v cloudové platformě
#XFLD: Credentials for User-Provided Service label in space schema dialog
cloudPlatformCredentialsLabel=Přihlašovací údaje pro uživatelskou službu:
#XFLD: Credentials for User-Provided Service (Without Password) label in space schema dialog
cloudPlatformCredentialsWithoutPasswordLabel=Přihlašovací údaje pro uživatelskou službu (bez hesla):
#XFLD: Copy Credentials label in space schema dialog
copyCredentialsLabel=Kopírovat přihlašovací údaje bez hesla
#XFLD: Copy Credentials label in space schema dialog
copyFullCredentialsLabel=Kopírovat kompletní přihlašovací údaje
#XFLD: Copy Password label in space schema dialog
copyPasswordLabel=Kopírovat heslo
#XMSG: Credentials copied to clipboard
credentialsCopiedMessage=Přihlašovací údaje zkopírovány do schránky
#XMSG: Password copied to clipboard
passwordCopiedMessage=Heslo zkopírováno do schránky
#XMSG: create database user dialog title
createDatabaseUserDialogTitle=Vytvořit uživatele databáze
#XMSG: Database Users section title
databaseUsers=Uživatelé databáze
#XMSG: Database User Details dialog title
databaseUserDetailsDialogTitle=Detaily uživatele databáze
#XFLD: database user read audit log
databaseUserAuditLogRead=Aktivovat protokoly auditu pro operace čtení a uchovat protokoly pro
#XFLD: database user change audit log
databaseUserAuditLogChange=Aktivovat protokoly auditu pro operace změny a uchovat protokoly pro
#XMSG: Cloud Platform Access
cloudPlatformAccess=Přístup k cloudové platformě
#XFLD: Enable Consumption in Cloud Platform hint
enableConsumptionCloudPlatformHint=Nastavit přístup k vašemu kontejneru infrastruktury nasazení HANA (HDI) pomocí tohoto uživatele databáze. Pro připojení vašeho kontejneru HDI musí být zapnuto modelování SQL
#XFLD: Enable HDI Consumption
enableConsumptionCloudPlatform=Aktivovat spotřebu HDI
#XFLD: Enable Consumption hint
enableConsumptionHint=Chcete, aby byla data ve vašem prostoru spotřebovatelná jinými nástroji nebo aplikacemi?
#XFLD: Enable Consumption
enableConsumption=Aktivovat spotřebu SQL
#XFLD: Enable Modeling
enableModeling=Aktivovat modelování SQL
#XMSG: Privileges for Data Modeling
privilegesModeling=Příjem dat
#XMSG: Privileges for Data Consumption
privilegesConsumption=Spotřeba dat pro externí nástroje
#XFLD: SQL Modeling
sqlModeling=Modelování SQL
#XFLD: SQL Consumption
sqlConsumption=Spotřeba SQL
#XFLD: enabled
enabled=Aktivováno
#XFLD: disabled
disabled=Deaktivováno
#XFLD: Edit Privileges
editPrivileges=Upravit oprávnění
#XFLD: Open Database Explorer
openDBX=Otevřít průzkumník databáze
#XFLD: create database user hint
databaseCreateHint=Uvědomte si, že po uložení již nebudete moci změnit uživatelské jméno.
#XFLD: Internal Schema Name
internalSchemaName=Interní název schématu
#YMSE: Failed to load database users
loadDatabaseUserError=Nezdařilo se načíst uživatele databáze
#YMSE: Failed to delete database user
deleteDatabaseUsersError=Nezdařilo se odstranit uživatele databáze
#XMSG: Success message after deleting database user
deleteDatabaseUserSuccess=Uživatel databáze odstraněn
#XMSG: Success message after deleting database users
deleteDatabaseUsersSuccess=Uživatelé databáze odstraněni
#XMSG: Success message after creating database user
createDatabaseUserSuccessMessage=Uživatel databáze vytvořen
#YMSE: Failed to create database user
createDatabaseUserError=Nezdařilo se vytvořit uživatele databáze
#XMSG: Success message after updating database user
updateDatabaseUserSuccessMessage=Uživatel databáze aktualizován
#YMSE: Failed to update database user
updateDatabaseUserError=Nezdařilo se aktualizovat uživatele databáze
#XFLD: HDI Consumption
hdiConsumption=Spotřeba HDI
#XTIT: Space detail section Database Access title
detailsSectionDatabaseAccess=Přístup do databáze
#XTXT: Database users consumption hint
databaseUsersConsumptionHint=Umožněte spotřebovávat data vašeho prostoru standardně. Modely v editorech automaticky povolí spotřebovávání dat.
#XFLD: Default Consumption of Space Data:
defaultConsumptionOfSpaceData=Standardní spotřeba dat prostoru:
#XFLD: Database User Name
databaseUserName=Jméno uživatele databáze
#XMSG: Database User creation validation error message
databaseUserValidationError=Zdá se, že některá pole jsou neplatná. Před pokračováním zkontrolujte požadovaná pole.
#XMSG: SQL modeling can’t be enabled since this user has been migrated.
migratedDatabaseUserHint=Příjem dat nelze aktivovat, neboť nebyla provedena migrace tohoto uživatele.
#XBUT: Remove Button Text
remove=Odebrat
#XBUT: Remove Spaces Button Text
removeSpaces=Odebrat prostory
#XBUT: Remove Objects Button Text
removeObjects=Odebrat objekty
#XMSG: No members have been added yet.
noMembersAssigned=Žádní uživatelé nebyli ještě přidáni.
#XMSG: No users have been added yet.
noUsersAssigned=Žádní uživatelé nebyly ještě přidáni.
#XMSG: No database users have been created or your filter is not showing any data.
noDatabaseUsers=Žádní uživatelé databáze nebyli vytvořeni nebo váš filtr nezobrazuje žádná data.
#XMSG: Please enter a user name.
noDatabaseUsername=Zadejte uživatelské jméno.
#XMSG: The user name is too long. Please use a shorter one.
databaseUsernameTooLong=Uživatelské jméno je příliš dlouhé. Použijte kratší.
#XMSG: No privileges have been enabled and this database user will have limited functionality. Do you still want to continue?
noPrivilegesConfirmationMessage=Nebyla aktivována žádná oprávnění a tento uživatel databáze bude mít omezenou funkčnost. Chcete ještě pokračovat?
#XMSG: To enable audit logs for change operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogChangeMessage=K aktivaci protokolů auditu pro změnové operace je třeba aktivovat také příjem dat. Chcete to provést?
#XMSG: To enable audit logs for read operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogReadMessage=K aktivaci protokolů auditu pro čtecí operace je třeba aktivovat také příjem dat. Chcete to provést?
#XMSG: To enable HDI consumption data ingestion and data consumption needs to be enabled. Do you want to enable all?
enableHDIConsumptionMessage=K aktivaci spotřeby HDI je třeba také aktivovat příjem dat a spotřebu dat. Chcete to provést?
#XMSG:
databaseUserPasswordText=Pro nastavení spojení s tímto uživatelem databáze zkopírujte vaše heslo. Pokud jste vaše heslo zapomněli, můžete vždy požádat o nové.
#XTIT: Space detail section members title
detailsSectionMembers=Členové
#XMSG: New password set
newPasswordSet=Nové heslo nastaveno
#XFLD: Data Ingestion
dataIngestion=Příjem dat
#XFLD: Data Consumption
dataConsumption=Spotřebovávání dat
#XFLD: Privileges
privileges=Oprávnění
#XFLD: Enable Data ingestion
enableDataIngestion=Aktivovat příjem dat
#XMSG: Log the read and change operations for data ingestion.
databaseUserAuditHint=Protokolovat čtecí a změnové operace pro příjem dat
#XMSG: Make your space data available in your HDI containers.
databaseUserHDIHint=Zpřístupnit vaše data prostoru pro dostupnost ve vašich kontejnerech HDI
#XFLD: Enable Data consumption
enableDataConsumption=Aktivovat spotřebu dat
#XMSG: Allow other apps or tools to consume your space data.
enableDataConsumptionHint=Umožnit jiným aplikacím nebo nástrojům spotřebovávat data vašeho prostoru.
#XMSG: To set up access via this schema user copy the credentials to your user-provided service. In case you can only copy the credentials without a password make sure to add the password later.
databaseUserHDIConsumptionHint=Pro nastavení přístupu skrze tohoto uživatele databáze zkopírujte přihlašovací údaje do vaší uživatelské služby. Pokud můžete jen zkopírovat přihlašovací údaje bez hesla, zajistěte, abyste heslo přidali později.
#XFLD: Data Flow Runtime Capacity label in space overview
dataFlowRuntimeCapacity=Kapacita doby běhu datového toku ({0}:{1} hodin z {2} hodin)
#XFLD: Could not load Data Flow Runtime Capacity
dataFlowRuntimeCapacityError=Nebylo možné načíst kapacitu doby běhu datového toku
#XMSG: User can grant data consumption to other users.
databaseUserConsumptionWithGrantOptionHint=Uživatel může udělit spotřebovávání dat jiným uživatelům.
#XFLD: Enable data consumption with grant option
enableConsumptionWithGrantOption=Aktivovat spotřebu dat s možností udělení
#XMSG: To enable data consumption with grant option data consumption needs to be enabled. Do you want to enable both?
enableConsumptionWithGrantMessage=Aktivace spotřeby dat s možností udělení vyžaduje aktivaci spotřeby dat. Chcete aktivovat oboje?
#XFLD: Enable Automated Predictive Library (APL) and Predictive Analysis Library (PAL)
allowUsingScriptServerFunctionality=Aktivovat automatizovanou prediktivní knihovnu (Automated Predictive Library - APL) a prediktivní analytickou knihovnu (Predictive Analysis Library - PAL)
#XMSG: User can use SAP HANA Cloud embedded machine learning functions.
allowUsingScriptServerFunctionalityHint=Uživatelé mohou použít funkce strojového učení integrované do SAP HANA Cloud.
#XFLD: Password Policy
passwordPolicy=Zásady hesel
#XMSG: Password Policy
passwordPolicyHint=Aktivovat nebo deaktivovat zde konfigurované zásady pro hesla.
#XFLD: Enable Password Policy
enablePasswordPolicy=Aktivovat zásadu pro hesla
#XMSG: Read Access to the Space Schema
readAccessTitle=Čtecí přístup ke schématu prostoru
#XMSG: read access hint
readAccessHint=Povolit uživateli databáze připojení externích nástrojů ke schématu prostoru a pohledy čtení, které jsou vystaveny pro spotřebu.
#XFLD: Space Schema
spaceSchema=Schéma prostoru
#XFLD: Enable Read Access (SQL)
enableReadAccess=Povolit čtecí přístup (SQL)
#XMSG: Allow the user to grant read access to other users.
withGrantOptionHint=Dovolit uživateli udělení čtecího přístupu pro jiné uživatele.
#XFLD: With Grant Option
withGrantOption=S možností udělení
#XMSG: Make your space data available in your HDI containers.
enableHDIConsumptionHint=Zpřístupnit vaše data prostoru pro dostupnost ve vašich kontejnerech HDI
#XFLD: Enable HDI Consumption
enableHDIConsumption=Aktivovat spotřebu HDI
#XMSG: Write Access to the User’s Open SQL Schema
writeAccessTitle=Zápisový přístup pro schéma Open SQL uživatele
#XMSG: write access hint
writeAccessHint=Povolit uživateli databáze připojení externích nástrojů ke schématu Open SQL uživatele za účelem vytvoření datových entit a přijímání dat pro použití v prostoru.
#XFLD: Open SQL Schema
openSQLSchema=Schéma Open SQL
#XFLD: Enable Write Access (SQL DDL & DML)
enableWriteAccess=Povolit zápisový přístup (SQL, DDL, & DML)
#XMSG: audit hint
auditHint=Protokolovat čtecí a změnové operace ve schématu Open SQL.
#XMSG: data consumption hint
dataConsumptionHint=Vystavit všechny nové pohledy v prostoru standardně pro spotřebu. Modelery mohou přepsat toto nastavení pro jednotlivé pohledy přepínačem „Vystavit pro spotřebu“ ve výstupním postranním panelu pohledu. Můžete také zvolit formáty, v nichž jsou pohledy vystaveny.
#XFLD: Expose for Consumption by Default
exposeDataConsumption=Vystavit pro spotřebu standardně
#XMSG: database users hint consumption hint
databaseUsersHint2New=Vytvořit uživatele databáze pro připojení externích nástrojů k SAP Datasphere. Nastavit privilegia pro povolení uživatelům číst data prostoru a vytvářet datové entity (DDL) a přijímat data (DML) pro použití v prostoru.
#XFLD: Read
read=Číst
#XFLD: Read (HDI)
readHDI=Číst (HDI)
#XFLD: Write
write=Zapisovat
#XMSG: HDI Containers Hint
HDIContainersHint2=Povolit přístup k vašim kontejnerům SAP HANA Deployment Infrastructure (HDI) ve vašem prostoru. Modelery mohou použít artefakty HDI jako zdroje pro pohledy a klienti HDI mohou přistupovat k vašim datům prostoru.
#XMSG: Open info dialog
openDatabaseUserInfoDialog=Otevřít informační dialog
#XMSG: Database user is locked. Open info dialog to unlock
openLockedDatabaseUserInfoDialog=Uživatel databáze je blokován. Pro odblokování otevřete dialog
#XFLD: Table
table=Tabulka
#XFLD: Space section partner configuration title
partnerConnectionConfigurationSectionTitle=Připojení partnera
#XFLD: Space section partner configuration subtitle
partnerConnectionConfigurationSectionSubTitle=Konfigurace připojení partnera
#XFLD: Space section partner configuration hint
partnerConnectionConfigurationSectionHint=Definujte vaši vlastní dlaždici připojení partnera přidáním vašeho URL iFrame a ikony. Tato konfigurace je dostupná jen pro tohoto tenanta.
#XFLD: Table Name Field
partnerConnectionConfigurationName=Název dlaždice
#XFLD: Table iFrame URL field
partnerConnectionConfigurationiFrameURL=URL iFrame
#XFLD: Table iFrame Post Message Origin field
partnerConnectionConfigurationiFramePostMessageOrigin=Původ dodatečné zprávy iFrame
#XFLD: Table icon/image name
partnerConnectionConfigurationIcon=Ikona
#XMSG: Table no result found
partnerConnectionConfigurationNotfound=Žádné konfigurace připojení partnera nebylo možné najít.
#XMSG: Partner Connection Confgurations are not available
partnerConnectionConfigurationNotAvailable=Konfigurace připojení partnera nelze zobrazit, když je databáze v reálném čase nedostupná.
#XFLD: Dialog Create Partner Connection Configuration
partnerConnectionConfigurationDialogTitleAdd=Vytvořit konfiguraci připojení partnera
#XFLD: Dialog Create Partner Connection Configuration Upload Icon
partnerConnectionConfigurationButtonUloadPartnerIcon=Ikona odeslání
#XFLD: Dialog Create Partner Connection Configuration Select
partnerConnectionConfigurationSelectIcon=Vybrat (maximální velikost 200KB)
#XFLD: Dialog Create Partner Connection Configuration Enter a name
partnerConnectionConfigurationNamePlaceholder=Příklad dlaždice partnera
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL
partnerConnectionConfigurationiFrameURLPlaceholder=http://www.example.com
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL Check
partnerConnectionConfigurationiFramePostMessageOriginPlaceholder=.example.com
#XFLD: Dialog Create Partner Connection Configuration Browse
partnerConnectionConfigurationBrowseButton=Procházet
#XFLD: Create Partner Connection Configuration Msg
partnerConnectionConfigurationUploadMsg=Konfigurace připojení partnera byla úspěšně vytvořena.
#XMSG: Error while deleting Partner Connection configuration(s)
partnerConnectionConfigurationDeleteError=Při odstraňování konfigurace připojení partnera došlo k chybě.
#XMSG: Partner Connection Configuration has been successfully deleted
partnerConnectionConfigurationDeleteSuccess=Konfigurace připojení partnera byla úspěšně odstraněna.
#XMSG: Error while retrieving Partner Connection Configurations
partnerConnectionConfigurationLoadError=Při vyvolání konfigurace připojení partnera došlo k chybě.
#XMSG: Error fileSizeLimitExceeded
partnerConnectionConfigurationfileSizeLimitExceeded=Soubor nelze odeslat, protože překračuje maximální velikost 200KB.
#XBUT: Create Partner Connection Configuration
partnerConnectionConfigurationButtonCreate=Vytvořit konfiguraci připojení partnera
#XBUT: Delete Partner Connection Configuration
partnerConnectionConfigurationButtonDelete=Odstranit konfiguraci připojení partnera.
#XMSG: Partner Tile Creation Failure
upsertPartnerTileFailed=Dlaždici partnera nebylo možné vytvořit.
#XMSG: Partner Tile Deletion Failure
deletePartnerTileFailed=Dlaždici partnera nebylo možné odstranit.
#XMSG: HANA cloud connector settings reset failure
resetHANASCCSettingsFailed=Resetování nastavení konektoru zákazníka SAP HANA Cloud se nezdařilo
#XFLD: Workload Class
workloadClass=Třída zatížení
#XFLD: Workload Management
workloadManagement=Řízení zatížení
#XFLD: Priority
workloadClassPriority=Priorita
#XMSG:
workloadManagementPriorityHint=Můžete určit prioritu tohoto prostoru při dotazování databáze. Zadejte hodnotu od 1 (nejnižší priorita) do 8 (nejvyšší priorita). V situaci, kdy prostory soutěží o dostupná vlákna, jsou prostory s vyšší prioritou spuštěny před prostory s nižšími prioritami.
#XMSG:
workloadClassPriorityHint=Můžete zadat prioritu prostoru 0 (nejnižší) až 8 (nejvyšší). Příkazy prostoru s vysokou prioritou jsou prováděny před příkazy jiných prostorů s nižší prioritou. Standardní priorita je 5. Hodnota priority 9 je rezervována pro operace systému, není k dispozici pro prostor.
#XFLD: Statement Limits
workloadclassStatementLimits=Limity příkazu
#XFLD: Workload Configuration
workloadConfiguration=Konfigurace zatížení
#XMSG:
workloadClassStatementLimitsHint=Můžete zadat maximální počet (nebo procenta) vláken a GB paměti, který mohou spotřebovat příkazy běžící současně v prostoru. Můžete zadat jakoukoli hodnotu nebo procenta mezi 0 (žádný limit) a celkovou pamětí a všemi vlákny dostupnými v tenantu. \n\n Když zadáte limit paměti, příkazy, které dosáhnou limitu paměti, nebudou provedeny.
#XMSG:
workloadClassStatementLimitsDescription=Standardní konfigurace poskytuje velkorysé limity zdrojů a zároveň zabraňuje tomu, aby jakýkoli jednotlivý prostor přetěžoval systém.
#XMSG:
workloadClassStatementLimitCustomDescription=Můžete nastavit maximální limity celkového počtu vláken a paměti, které mohou příkazy běžící souběžně v prostoru spotřebovat.
#XMSG:
totalStatementThreadLimitHelpText=Nastavení příliš nízkého limitu vláken může ovlivnit výkon příkazu, zatímco příliš vysoké hodnoty nebo 0 mohou umožnit, aby prostor spotřeboval všechna dostupná systémová vlákna.
#XMSG:
totalStatementMemoryLimitHelpText=Nastavení příliš nízkého limitu paměti může způsobit problémy s nedostatkem paměti, zatímco příliš vysoké hodnoty nebo 0 mohou umožnit, aby prostor spotřeboval veškerou dostupnou systémovou paměť.
#XMSG:
totalStatementThreadLimitHelpTextRestricted=Zadejte procentuální hodnotu mezi 1% a 70% (nebo ekvivalentní číslo) z celkového počtu vláken dostupných ve vašem tenantovi. Nastavení příliš nízkého limitu počtu vláken může ovlivnit výkon příkazů, zatímco příliš vysoké hodnoty mohou ovlivnit výkon příkazů v jiných prostorech.
#XMSG:
totalStatementThreadLimitHelpTextDynamic=Zadejte procentuální hodnotu mezi 1% a {0}% (nebo ekvivalentní číslo) z celkového počtu vláken dostupných ve vašem tenantu. Nastavení příliš nízkého limitu počtu vláken může ovlivnit výkon příkazů, zatímco příliš vysoké hodnoty mohou ovlivnit výkon příkazů v jiných prostorech.
#XMSG:
totalStatementMemoryLimitHelpTextNew=Zadejte procentuální hodnotu mezi 0 (neomezeně) a celkovou velikostí paměti dostupnou ve vašem tenantovi. Nastavení příliš nízkého limitu paměti může ovlivnit výkon příkazů, zatímco příliš vysoké hodnoty mohou ovlivnit výkon příkazů v jiných prostorech.
#XFLD: Total Statement Thread Limit
totalStatementThreadLimit=Celkový limit vlákna příkazu
#XFLD: Segmented button label
workloadclassPercentage=%
#XFLD: Segmented button label
workloadclassGigaByte=GB
#XFLD, 80: Segmented button label
workloadclassThreads=Vlákna
#XFLD: Total Statement Memory Limit
totalStatementMemoryLimit=Celkový limit paměti příkazu
#YMSE: Error while loading customer hana info
loadCustomerHanaInfoError=Nezdařilo se zavést zákaznické informace SAP HANA.
#XMSG:
minimumLimitReached=Dosaženo minimálního limitu.
#XMSG:
maximumLimitReached=Dosaženo maximálního limitu.
#XMSG: Name Taken for Technical Name
technical-name-taken=Připojení s vámi zadaným technickým názvem již existuje. Zadejte jiný název.
#XMSG: Name Too long for Technical Name
technical-name-too-long=Vámi zadaný technický název překračuje 40 znaků. Zadejte název s méně znaky.
#XMSG: Technical name field empty
technical-name-field-empty=Zadejte technický název.
#XMSG: Generic error when user enters invalid characters for a field
field-invalid-characters=Pro název můžete používat jen písmena (a-z), čísla (0-9) a podtržítka (_).
#XMSG: Generic error when user enters invalid initial characters for a field
field-underscore-trim=Vámi zadaný název nemůže začínat nebo končit podtržítkem (_).
#XFLD: Checkbox label for Enable Statement Limits
enableStatementLimits=Aktivovat limity příkazu
#XFLD: Connection SubSection Settings Title in space details
connectionSettings=Nastavení
#XMSG: Connections tool hint in Space details section
connectionsToolHint=Pro vytvoření nebo úpravu připojení otevřete aplikaci Připojení nebo klikněte zde:
#XBUT: Connection list Open Connection Tool Button
button-connectiontool=Přejít k připojením
#XFLD: Not deployed label on space tile
notDeployedLabel=Prostor nebyl ještě nasazen.
#XFLD: Not deployed additional text on space tile
notDeployedText=Nasaďte prostor.
#XFLD: Corrupt space label on space tile
corruptSpace=Něco se pokazilo.
#XFLD: Corrupt space additional text on space tile
corruptSpaceHint=Pokuste se o nové nasazení nebo kontaktujte podporu
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Data protokolu auditu
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Administrativní data
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Další data
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Data v prostorech
#XMSG: Unlock Space confirmation text
unlockSpaceConfirmation=Opravdu chcete odblokovat tento prostor?
#XMSG: Lock Space confirmation text
lockSpaceConfirmation=Opravdu chcete blokovat tento prostor?
#XFLD: Lock
lock=Blokovat
#XFLD: Unlock
unlock=Odblokovat
#XFLD: Locking
locking=Blokování
#XMSG: Success message after locking space
lockSpaceSuccessMsg=Prostor blokován
#XMSG: Success message after unlocking space
unlockSpaceSuccessMsg=Prostor odblokován
#XMSG: Success message after locking spaces
lockSpacesSuccessMsg=Prostory blokovány
#XMSG: Success message after unlocking spaces
unlockSpacesSuccessMsg=Prostory odblokovány
#YMSE: Error while locking a space
lockSpaceError=Prostor nelze blokovat.
#YMSE: Error while unlocking a space
unlockSpaceError=Prostor nelze odblokovat.
#XTIT: popup title Warning
confirmationWarningTitle=Upozornění
#XMSG: Message strip message when a space was locked manually
manualLockedMessage=Prostor byl blokován manuálně.
#XMSG: Message strip message when a space was locked due to the amount of generated audit logs
auditLogLockedMessage=Prostor byl blokován systémem, protože auditní protokoly příliš mnoho GB místa na disku.
#XMSG: Message strip message when a space was locked because the assigned quota was exceeded.
quotaLockedMessage=Prostor byl blokován systémem, protože překračuje jeho alokace paměti nebo úložiště na disku.
#XMSG: Unlock Spaces confirmation text
unlockSpacesConfirmation=Opravdu chcete odblokovat vybrané prostory?
#XMSG: Lock Spaces confirmation text
lockSpacesConfirmation=Opravdu chcete blokovat vybrané prostory?
#XTIT: Scoped role editor dialog title
scopedRoleEditorDialogTitle=Editor role z rozsahu
#XTIT: ECN Management title
ecnManagementTitle=Prostor a správa elastického výpočetního uzlu
#XFLD: ECNs
ecns=Elastické výpočetní uzly
#XFLD: ECN phase Ready
ecnReady=Připraveno
#XFLD: ECN phase Running
ecnRunning=Probíhá
#XFLD: ECN phase Initial
ecnInitial=Nepřipraveno
#XFLD: ECN phase Starting
ecnStarting=Spouštění
#XFLD: ECN phase Starting Failed
ecnStartingFailed=Spouštění se nezdařilo
#XFLD: ECN phase Stopping
ecnStopping=Zastavuje se
#XFLD: ECN phase Stopping Failed
ecnStoppingFailed=Zastavování se nezdařilo
#XBTN: Assign Button
assign=Přiřadit prostory
#XBTN: Start Header-Button
start=Spustit
#XBTN: Update Header-Button
repair=Aktualizovat
#XBTN: Stop Header-Button
stop=Zastavit
#XFLD: ECN hours remaining
ecnHoursRemaining=Zbývá 1000 hodin
#XFLD: ECN block-hours remaining
ecnBlockHoursRemaining=Zbývá {0} blokových hodin
#XFLD: ECN block-hour remaining
ecnBlockHourRemaining=Zbývá {0} blokových hodin
#XTIT: ECN creation dialog title
ecnCreationDialogTitle=Vytvořit elastický výpočetní uzel
#XTIT: ECN edit dialog title
ecnEditDialogTitle=Upravit elastický výpočetní uzel
#XTIT: ECN deletion dialog title
ecnDeletionDialogTitle=Odstranit elastický výpočetní uzel
#XTIT: ECN Space assignment dialog title
ecnSpaceAssignmentDialogTitle=Přiřadit prostory
#XFLD: ECN ID
ECNIDLabel=Elastický výpočetní uzel
#XTXT: Selected toolbar text
selectedToolbarText=Vybráno: {0}
#XTIT: Elastic Compute Nodes
ECNslong=Elastické výpočetní uzly
#XTIT: Object assignment - Table header: Objects available
objectsAvailable=Počet objektů
#XTIT: Object assignment - Dialog header text
selectObjects=Vyberte prostory a objekty, které chcete přiřadit k vašemu elastickému výpočetnímu uzlu:
#XTIT: Object assignment - Table header title: Objects
objects=Objekty
#XTIT: Object assignment - Table header: Type
type=Typ
#XMSG: Message strip delete database user audit warning
deleteDatabaseUserWarningMessageStrip=Uvědomte si, že odstranění uživatele databáze bude mít za následek odstranění všech vygenerovaných záznamů protokolu auditu. Pokud si chcete protokoly auditu ponechat, zvažte jejich export před odstraněním uživatele databáze.
#XMSG: Message strip delete HDI container audit logs impact
deleteHDIContainerWarningMessageStrip=Uvědomte si, že zrušení přiřazení kontejneru HDI k prostoru bude mít za následek odstranění všech záznamů protokolu auditu. Pokud chcete zachovat protokoly auditu, zvažte jejich export, než zrušíte jejich přiřazení ke kontejneru HDI.
#XTXT: All audit logs
allAuditLogs=Všechny záznamy protokolu auditu vygenerované pro prostor
#XMSG: Message strip disable audit warning
disableAuditWarningMessageStrip=Uvědomte si, že deaktivace auditní směrnice (operace čtení nebo změny) bude mít za následek odstranění všech položek protokolu auditu. Pokud chcete zachovat záznamy protokolu auditu, zvažte jejich export, než zakážete auditní směrnici.
#XTIT: No Spaces assigned to ECN Illustration title
noSpacesECNTitle=Ještě nejsou přiřazeny žádné prostory ani objekty
#XTIT: No Spaces assigned to ECN Illustration description
noSpacesECNDescription=Abyste mohli zahájit práci s vaším výpočetním uzlem, přiřaďte mu prostor nebo objekty.
#XTIT: No Spaces Illustration title
noSpacesTitle=Žádný prostor dosud nevytvořen
#XTIT: No Spaces Illustration description
noSpacesDescription=Pro zahájení získávání dat vytvořte prostor.
#XTIT: No Spaces Illustration title
noDeletedSpacesTitle=Koš je prázdný
#XTIT: No Spaces Illustration description
noDeletedSpacesDescription=Zde můžete obnovit odstraněné prostory.
#XTXT:  {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDatabaseUsers=Jakmile je prostor nasazen, následující uživatelé databáze budou {0} odstraněni a nebude možné je obnovit:
#XTIT: Delete database users
deleteDatabaseUsersTitle=Odstranit uživatele databáze
#YMSE: Error if ecn id already exists
ecnIDInputDuplicateError=ID již existuje.
#YMSE: Error if ecn id contains invalid characters
ecnIDInputInvalidError=Používejte jen malá písmena a - z a číslice 0 - 9.
#YMSE: Error if ecn id is too short
ecnIDInputTooShortError=ID musí být dlouhé alespoň {0} znaků.
#XMSG: ecn id length warning
ecnIdLengthWarning=Překročen maximální počet {0} znaků.
#XFLD: open System Monitor
systemMonitor=Monitor systému
#XFLD: open ECN schedule dialog menu entry
schedule=Naplánovat
#XFLD: open create ECN schedule dialog
createSchedule=Vytvořit časový plán
#XFLD: open change ECN schedule dialog
changeSchedule=Upravit časový plán
#XFLD: open delete ECN schedule dialog
deleteSchedule=Odstranit plán
#XFLD: open self assign ECN schedule dialog
selfAssignSchedule=Přiřadit plán mně
#XFLD: open pause ECN schedule dialog
pauseSchedule=Pozastavit plán
#XFLD: open resume ECN schedule dialog
resumeSchedule=Obnovit plán
#XFLD: View Logs
viewLogs=Zobrazení protokolů
#XFLD: Compute Blocks
computeBlocks=Bloky výpočtu
#XFLD: Memory label in ECN creation dialog
ecnMemory=Paměť (GB)
#XFLD: Storage label in ECN creation dialog
ecnStorage=Úložiště (GB)
#XFLD: Number of CPU label in ECN creation dialog
ecnCPUCount=Počet CPU
#XFLD: ECN updated by label
changedBy=Změnil
#XFLD: ECN updated on label
changedOn=Změněno dne
#XMSG: Success message after creating a Elastic Compute Node
createEcnSuccessMessage=Elastický výpočetní uzel vytvořen
#YMSE: Error while creating a Elastic Compute Node
createEcnError=Elastický výpočetní uzel nebylo možné vytvořit
#XMSG: Success message after updating a Elastic Compute Node
updateEcnSuccessMessage=Elastický výpočetní uzel aktualizován
#XMSG: Error while updating a Elastic Compute Node
udpateEcnError=Elastický výpočetní uzel nebylo možné aktualizovat
#XMSG: Success message after deleting a Elastic Compute Node
deleteEcnSuccessMessage=Elastický výpočetní uzel odstraněn
#YMSE: Error while deleting a Elastic Compute Node
deleteEcnError=Elastický výpočetní uzel nebylo možné odstranit
#XMSG: Success message after starting a Elastic Compute Node
startingEcnSuccessMessage=Spuštění elastického výpočetního uzlu
#XMSG: Success message after stopping a Elastic Compute Node
stoppingEcnSuccessMessage=Zastavení elastického výpočetního uzlu
#YMSE: Error while starting a Elastic Compute Node
startingEcnError=Elastický výpočetní uzel nebylo možné spustit
#YMSE: Error while stopping a Elastic Compute Node
stoppingEcnError=Elastický výpočetní uzel nebylo možné zastavit
#XBUT: Add Object button for an ECN
assignObjects=Přidat objekty
#XFLD: Enable auto object assignment label
enableAutoObjAssignment=Přiřadit všechny objekty automaticky
#XFLD: object type label to be assigned
objectTypeLabel=Typ (sémantické použití)
#XFLD: assigned object type label
assignedObjectTypeLabel=Typ
#XFLD: technical name label
TechnicalNameLabel=Technický název
#XFLD: Object selecttion ECN label
objectSelectionECNLabel=Vyberte objekty, které chcete přiřadit k elastickému výpočetnímu uzlu.
#XTIT: Add objects dialog title
assignObjectsTitle=Přiřadit objekty z
#XFLD: object label with object count
objectLabel=Objekt
#XMSG: No objects available to add message.
noObjectsToAssign=Neexistují žádné objekty k přiřazení.
#XMSG: No objects assigned message.
noAssignedObjects=Žádné objekty nepřiřazeny.
#XTIT: Remove objects popup title
removeObjectsOrSpacesConfirmationTitle=Upozornění
#XTIT: Delete ECN popup button and title
deleteECNConfirmationButtonAndTitle=Odstranit
#XMSG: Remove objects popup text
removeObjectsConfirmation=Opravdu chcete odebrat vybrané objekty?
#XMSG: Remove spaces popup text
removeSpacesConfirmation=Opravdu chcete odebrat vybrané prostory?
#XBUT: Confirmation button for objects dialog
removeObjectsOrSpacesConfirmationBtn=Odebrat prostory
#XMSG: Success message after removing objects
removeObjectsSuccessMessage=Vystavené objekty byly odebrány
#XMSG: Success message after assigning objects
assignObjectsSuccessMessage=Vystavené objekty byly přiřazeny
#XFLD: auto object assignment label for space tile
autoObjAssignmentLabel=Všechny vystavené objekty
#XFLD: Spaces tab label
spacesTabLabel=Prostory
#XFLD: Exposed objects tab label
consumptionObjectsTabLabel=Vystavené objekty
#XMSG: Success message after unassigning spaces
spacesUnassignedSuccessMessage=Prostory byly odebrány
#XMSG: Success message after unassigning space
spaceUnassignedSuccessMessage=Prostor byl odebrán
#YMSE: Error while assigning / unassigning spaces
spacesAssignUnassignError=Prostory nebylo možné přiřadit nebo odebrat.
#YMSE: Error while removing objects
removeObjectsError=Nemohli jsme přiřadit nebo odebrat objekty.
#YMSE: Error while removing object
removeObjectError=Nemohli jsme přiřadit nebo odebrat objekt.
#YMSE: Error when saving the ecn with invalid value
invalidSizingPlanError=Předtím vybraná čísla již nejsou platná. Vyberte platné číslo.
#YMSE: Error when saving the ecn with invalid value
invalidPerformanceClassError=Vyberte platnou výkonnostní třídu.
#YMSE: Error when saving the ecn with invalid value
performanceClassNotAvailableError=Předchozí vybraná výkonnostní třída "{0}" je momentálně neplatná. Vyberte platnou výkonnostní třídu.
#XTXT: Delete ECN confirmation text
deleteConfirmationTxtECN=Opravdu chcete odstranit elastický výpočetní uzel?
#XFLD: tooltip for ? button
help=Nápověda
#XFLD: ECN edit button label
editECN=Konfigurovat
#XFLD: Technical type label for ERModel
DWC_ERMODEL=Model vztahů entit
#XFLD: Technical type label for Local Table
DWC_LOCAL_TABLE=Lokální tabulka
#XFLD: Technical type label for Remote Table
DWC_REMOTE_TABLE=Vzdálená tabulka
#XFLD: Technical type label for Analytic Model
DWC_ANALYTIC_MODEL=Analytický model
#XFLD: Technical type label for Task Chain
DWC_TASKCHAIN=Řetězec úloh
#XFLD: Technical type label for Data Flow
DWC_DATAFLOW=Datový tok
#XFLD: Technical type label for Replication Flow
DWC_REPLICATIONFLOW=Replikační tok
#XFLD: Technical type label for Transformation Flow
DWC_TRANSFORMATIONFLOW=Tok transformace
#XFLD: Technical type label for Intelligent Lookup
DWC_IDT=Inteligentní vyhledání
#XFLD: Technical type label for Repository
DWC_REPOSITORY_PACKAGE=Úložiště
#XFLD: Technical type label for Enterprise Search
DWC_ESMODEL=Komplexní hledání
#XFLD: Technical type label for View
DWC_VIEW=Pohled
#XFLD: Technical type label for Data Product
DWC_DATAPRODUCT=Datový produkt
#XFLD: Technical type label for Data Access Control
DWC_DAC=Řízení přístupu k datům
#XFLD: Technical type label for Folder
DWC_FOLDER=Složka
#XFLD: Technical type label for Data Entity
DWC_BUSINESS_ENTITY=Business entita
#XFLD: Technical type label for Business Entity Variant
DWC_BUSINESS_ENTITY_VARIANT=Varianta business entity
#XFLD: Technical type label for Responsibility Scenario
DWC_AUTH_SCENARIO=Scénář odpovědnosti
#XFLD: Technical type label for Fact Model
DWC_FACT_MODEL=Model faktů
#XFLD: Technical type label for Transformation Flow
DWC_PERSPECTIVE=Perspektiva
#XFLD: Technical type label for Consumption Model
DWC_CONSUMPTION_MODEL=Model spotřeby
#XFLD: Technical type label for Remote Connection
DWC_REMOTECONNECTION=Dálkové spojení
#XFLD: Technical type label for Fact Model Variant
DWC_FACT_MODEL_VARIANT=Varianta modelu faktů
#XMSG: Schedule created alert message
createScheduleSuccess=Časový plán vytvořen
#XMSG: Schedule updated alert message
updateScheduleSuccess=Časový plán aktualizován
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Časový plán odstraněn
#XMSG: Schedule takeover success message
ownerChangeScheduleSuccess=Časový plán přiřazen k vám
#XMSG: Message for starting pausing of schedule
pauseScheduleStarted=Pozastavování 1 plánu
#XMSG: Message for starting resuming of schedule
resumeScheduleStarted=Obnovování 1 plánu
#XFLD: Segmented button label
availableSpacesButton=Dostupné
#XFLD: Segmented button label
selectedSpacesButton=Vybráno
#XFLD: Visit website button text
visitWebsite=Navštivte webovou stránku
#XMSG: Warning message on unselecting source language
sourceLanguageRemovalWarning=Dříve vybraný zdrojový jazyk bude odebrán.
#XBUT: enable translation confirmation button
enableTranslationConfirmationBtn=Aktivovat
#XFLD: ECN performance class label
performanceClassLabel=Výkonnostní třída
#XTXT performance class memory text
memoryText=Paměť
#XTXT performance class compute text
computeText=Vypočítat
#XTXT performance class high-compute text
highComputeText=Vysoce výkonný výpočet
#XBUT: Recycle Bin Button Text
recycleBin=Koš
#XBUT: Restore Button Text
restore=Obnovit
#XMSG: Warning message for new Workload Management UI
priorityWarning=Tato oblast je pouze pro čtení. Prioritu prostoru můžete změnit v systému / konfiguraci / v oblasti řízení zatížení.
#XMSG: Warning message for new Workload Management UI
workloadWarning=Tato oblast je pouze pro čtení. Konfiguraci zatížení prostoru můžete změnit v systému / konfiguraci / v oblasti řízení zatížení.
#XCOL: Space table-view column: Apache Spark vCPUs
sparkManagementVCPUs=Apache Spark vCPUs
#XCOL: Space table-view column: Apache Spark Memory (GB)
sparkManagementMemory=Paměť Apache Spark (GB)
#XCOL: Space type (from Space Capability) for Data Ingestion Spaces
ingestionSpaceType=Příjem datového produktu
#XFLD: Deployment in progress nodatatext for spark table
sparkTableDeploymentInProgress=Nejsou dostupná žádná data, protože prostor je právě nasazován
#XFLD: Runtime data being loaded nodatatext for spark table
sparkTableDeploymentLoading=Nejsou dostupná žádná data, protože prostor je právě načítán
#XCOL: Space type (from Space Capability) for BW Private Cloud Edition
BWPCESpaceType=SAP BW
#XBUT: Go to Instance Mappings Button
navToMappingsButton=Upravit mapování instancí
#XFLD: SAP HANA Data Lake Storage label if less than 1GB
dataLakeStorageUsed=< 1 GB
