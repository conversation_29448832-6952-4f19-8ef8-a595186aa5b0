#DO NOT REMOVE________________________
#XFLD: Breadcrumb for space monitoring page
spaceMonitorBreadcrumb=Overvåkning
#XTXT: Type name for spaces in browser tab page title
space=Rom
#_____________________________________
#XFLD: Spaces label in
spaces=Rom
#XFLD: Manage plan button text
manageQuotaButtonText=Administrer plan
#XBUT: Manage resources button
manageResourcesButton=Administrer ressurser
#XFLD: Create space button tooltip
createSpace=Opprett rom
#XFLD: Create
create=Opprett
#XFLD: Deploy
deploy=Distribuer
#XFLD: Page
page=Side
#XFLD: Cancel
cancel=Avbryt
#XFLD: Update
update=Oppdater
#XFLD: Save
save=Lagre
#XFLD: OK
ok=OK
#XFLD: days
days=Dager
#XFLD: Space tile edit button label
edit=Rediger
#XFLD: Auto Assign all objects to space
autoAssign=Automatisk tilordning
#XFLD: Space tile open monitoring button label
openMonitoring=Overvåk
#XFLD: Delete
delete=Slett
#XFLD: Copy Space
copy=Kopier
#XFLD: Close
close=Lukk
#XCOL: Space table-view column status
status=Status
#XFLD: Space status active
activeLabel=Aktiv
#XFLD: Space status locked
lockedLabel=Sperret
#XFLD: Space status critical
criticalLabel=Kritisk
#XFLD: Space status cold
coldLabel=Kald
#XFLD: Space status deleted
deletedLabel=Slettet
#XFLD: Space status unknown
unknownLabel=Ukjent
#XFLD: Space status ok
okLabel=OK
#XFLD: Database user expired
expired=Utløpt
#XFLD: deployed
deployed=Distribuert
#XFLD: not deployed
notDeployed=Ikke distribuert
#XFLD: changes to deploy
changesToDeploy=Endringer som skal distribueres
#XFLD: pending
pending=Distribuerer
#XFLD: designtime error
designtimeError=Designtidsfeil
#XFLD: runtime error
runtimeError=Kjøringstidsfeil
#XFLD: Space created by label
createdBy=Opprettet av
#XFLD: Space created on label
createdOn=Opprettet
#XFLD: Space deployed on label
deployedOn=Distribuert den
#XFLD: Space ID label
spaceID=Rom-ID
#XFLD: Priority label
priority=Prioritet
#XFLD: Space Priority label
spacePriority=Romprioritet
#XFLD: Space Configuration label
spaceConfiguration=Romkonfigurasjon
#XFLD: Not available
notAvailable=Ikke tilgjengelig
#XFLD: WorkloadType default
default=Standard
#XFLD: WorkloadType custom
custom=Egendefinert
#XFLD: Data Lake option label; refers to component SAP HANA Cloud, Data Lake
dataLakeAccess=Tilgang til datasjø
#XFLD: Translation label
translationLabel=Oversettelse
#XFLD: Source language label
sourceLanguageLabel=Kildespråk
#XFLD: Translation CheckBox label
translationCheckBox=Aktiver oversettelse
#XTXT: Popover hint that space has to be deployed to show user details
openDatabaseUserHintPopoverText=Distribuer rom for å vise brukerdetaljer.
#XTXT: Popover hint that space has to be deployed to open database explorer
openDatabaseExplorerHintPopoverText=Distribuer rom for å åpne databaseutforsker.
#XTXT: Text for when the Data Lake check box is disabled
dataLakeOptionDisabled=Du kan ikke bruke dette rommet til å få tilgang til datasjøen fordi den allerede brukes av et annet rom.
#XFLD: Data Lake check box text; refers to component SAP HANA Cloud, Data Lake
dataLakeCheckBox=Bruk dette rommet til å få tilgang til datasjøen.
#XFLD: Space Priority minimum label extension
low=Lav
#XFLD: Space Priority maximum label extension
high=Høy
#XFLD: Space name label
spaceName=Romnavn
#XFLD: Enable deploy objects checkbox
enableDeployObjects=Implementer objekter
#XTIT: Copy spaces dialog title
copySpaceDialogTitle=Kopier {0}
#XTXT Human readable text for unselected source language
unSelectedSourceLanguage=(Ikke valgt)
#XTXT Human readable text for language code "af"
af=Afrikaans
#XTXT Human readable text for language code "ar"
ar=Arabisk
#XTXT Human readable text for language code "bg"
bg=Bulgarsk
#XTXT Human readable text for language code "ca"
ca=Katalansk
#XTXT Human readable text for language code "zh"
zh=Forenklet kinesisk
#XTXT Human readable text for language code "zf"
zf=Kinesisk
#XTXT Human readable text for language code "hr"
hr=Kroatisk
#XTXT Human readable text for language code "cs"
cs=Tsjekkisk
#XTXT Human readable text for language code "cy"
cy=Walisisk
#XTXT Human readable text for language code "da"
da=Dansk
#XTXT Human readable text for language code "nl"
nl=Nederlandsk
#XTXT Human readable text for language code "en-UK"
en-UK=Engelsk (Storbritannia)
#XTXT Human readable text for language code "en"
en=Engelsk (USA)
#XTXT Human readable text for language code "et"
et=Estisk
#XTXT Human readable text for language code "fa"
fa=Farsi
#XTXT Human readable text for language code "fi"
fi=Finsk
#XTXT Human readable text for language code "fr-CA"
fr-CA=Fransk (Canada)
#XTXT Human readable text for language code "fr"
fr=Fransk
#XTXT Human readable text for language code "de"
de=Tysk
#XTXT Human readable text for language code "el"
el=Gresk
#XTXT Human readable text for language code "he"
he=Hebraisk
#XTXT Human readable text for language code "hi"
hi=Hindi
#XTXT Human readable text for language code "hu"
hu=Ungarsk
#XTXT Human readable text for language code "is"
is=Islandsk
#XTXT Human readable text for language code "id"
id=Bahasa Indonesia
#XTXT Human readable text for language code "it"
it=Italiensk
#XTXT Human readable text for language code "ja"
ja=Japansk
#XTXT Human readable text for language code "kk"
kk=Kasakhisk
#XTXT Human readable text for language code "ko"
ko=Koreansk
#XTXT Human readable text for language code "lv"
lv=Latvisk
#XTXT Human readable text for language code "lt"
lt=Litauisk
#XTXT Human readable text for language code "ms"
ms=Bahasa Melayu
#XTXT Human readable text for language code "no"
no=Norsk
#XTXT Human readable text for language code "pl"
pl=Polsk
#XTXT Human readable text for language code "pt"
pt=Portugisisk (Brasil)
#XTXT Human readable text for language code "pt-PT"
pt-PT=Portugisisk (Portugal)
#XTXT Human readable text for language code "ro"
ro=Rumensk
#XTXT Human readable text for language code "ru"
ru=Russisk
#XTXT Human readable text for language code "sr"
sr=Serbisk
#XTXT Human readable text for language code "sh"
sh=Serbokroatisk
#XTXT Human readable text for language code "sk"
sk=Slovakisk
#XTXT Human readable text for language code "sl"
sl=Slovensk
#XTXT Human readable text for language code "es"
es=Spansk
#XTXT Human readable text for language code "es-MX"
es-MX=Spansk (Mexico)
#XTXT Human readable text for language code "sv"
sv=Svensk
#XTXT Human readable text for language code "th"
th=Thai
#XTXT Human readable text for language code "tr"
tr=Tyrkisk
#XTXT Human readable text for language code "uk"
uk=Ukrainsk
#XTXT Human readable text for language code "vi"
vi=Vietnamesisk
#XTIT: Delete spaces confirmation title
deleteConfirmationHeaderText=Slett rom
#XTXT: Logically delete space delete confirmation text with space name
logicallyDeleteConfirmationBodyTextWithSpaceName=Er du sikker på at du vil flytte rom "{0}" til papirkurven?
#XTXT: Logically dpace delete confirmation text for multiple spaces
logicallyDeleteConfirmationBodyTextMultiple=Er du sikker på at du vil flytte de {0} valgte rommene til papirkurven?
#XTXT: Delete space delete confirmation text with space name
deleteConfirmationBodyTextWithSpaceName=Er du sikker på at du vil slette rommet "{0}"? Denne aktiviteten kan ikke angres.
#XTXT: Delete space delete confirmation text for multiple spaces
deleteConfirmationBodyTextMultiple=Er du sikker på at du vil slette de {0} rommene som er valgt? Aktiviteten kan ikke angres. Følgende innhold blir slettet {1}:
#XTXT: permanently
permanently=permanent
#XTXT: {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDetail=Følgende innhold blir {0} slettet og kan ikke gjenopprettes:
#XTXT: Hint Delete space confirmation text
deleteConfirmationHint=Skriv {0} for å bekrefte slettingen.
#XTXT: Delete space confirmation error check spelling
deleteConfirmationErrorCheckSpelling=Sjekk stavemåten din og prøv igjen.
#XTXT: All Spaces
allSpaces=Alle rom
#XTXT: All data
allData=Alle objekter og data som er inkludert i rommet
#XTXT: All connections
allConnections=Alle forbindelser som er definert i rommet
#XFLD: Space tile selection box tooltip
clickToSelect=Klikk for å velge
#XTXT: All database users
allDatabaseUsers=Alle objekter og data som er inkludert i Open SAL-skjemaer som er tilknyttet rommet
#XFLD: remove members button tooltip
deleteUsers=Fjern medlemmer
#XTXT: Space long description text
description=Beskrivelse (maksimalt 4000 tegn)
#XFLD: Add Members button tooltip
addUsers=Legg til medlemmer
#XFLD: Add Users button tooltip
addUsersTooltip=Legg til brukere
#XFLD: Edit Users button tooltip
editUsersTooltip=Rediger brukere
#XFLD: Remove Users button tooltip
removeUsersTooltip=Fjern brukere
#XFLD: Searchfield placeholder
filter=Søk
#XCOL: Users table-view column health
health=Helse
#XCOL: Users table-view column access
access=Tilgang
#XFLD: No user found nodatatext
noDataText=Finner ingen bruker
#XTIT: Members dialog title
selectUserDialogTitle=Legg til medlemmer
#XTIT: User dialog title
addUserDialogTitle=Legg til brukere
#XTIT: Delete connections confirmation title
deleteConnectionConfirmationHeaderText=Slett forbindelser
#XTIT: Delete single connection confirmation title
deleteConnectionConfirmationHeaderTextSingle=Slett forbindelse
#XTXT: Delete connection delete confirmation text
deleteConnectionConfirmationBodyText=Er du sikker på at du vil slette de valgte forbindelsene? De fjernes for godt.
#XTIT: Remote source dialog title
selectRemoteConnectionDialogTitle=Velg forbindelser
#XTIT: Share connection dialog title
connectionSharingDialogTitle=Del forbindelse
#XTIT: Connections shared from other spaces table title
sharedConnectionsFromOtherSpaces=Delte forbindelser
#XFLD: Add remote source button tooltip
addRemoteConnections=Legg til forbindelser
#XFLD: Delete remote source button tooltip
deleteRemoteConnections=Fjern forbindelser
#XFLD: Share remote source button tooltip
shareConnections=Del forbindelser
#XFLD: Tile-layout tooltip
tileLayout=Delbildeoppsett
#XFLD: Table-layout tooltip
tableLayout=Tabelloppsett
#XMSG: Success message after creating space
createSpaceSuccessMessage=Rommet er opprettet
#XMSG: Success message after copying space
copySpaceSuccessMessage=Kopierer rom "{0}" til rom "{1}"
#XMSG: Success message after deploying space
deploymentSuccessMessage=Romdistribusjonen har startet
#XMSG: Success message after Apache Spark update started
sparkUpdateStartedSuccessMessage=Apache Spark-oppdatering har startet
#YMSE: Error while starting Apache Spark update
sparkUpdateStartedErrorMessage=Kan ikke oppdatere Apache Spark
#XMSG: Success message after updating space
updateSettingsSuccessMessage=Romdetaljene er oppdatert
#XMSG: Success message after unlocking space
unlockSpaceSuccessMessage=Sperring av rommet er midlertidig opphevet
#XMSG: Success message after deleting a single space
deleteSpaceSuccessMessage=Rommet er slettet
#XMSG: Success message after deleting multiple spaces
deleteSpacesSuccessMessage=Rommene er slettet
#XMSG: Success message after restoring a single space
restoreSpaceSuccessMessage=Rommet er gjenopprettet
#XMSG: Success message after restoring multiple spaces
restoreSpacesSuccessMessage=Rommene er gjenopprettet
#YMSE: Error while updating settings
updateSettingsFailureMessage=Kan ikke oppdatere rominnstillingene.
#YMSE: Error while updating data lake option
dataLakeEnabledInOtherSpace=Datasjøen er allerede tilordnet et annet rom. Bare ett rom har tilgang til datasjøen om gangen.
#YMSE: Error while updating data lake option
virtualTablesExists=Du kan ikke fjerne tilordningen av datasjøen til rommet, for det finnes fortsatt avhengigheter til virtuelle tabeller*. Slett de virtuelle tabellene for å fjerne tilordningen av datasjøen til rommet.
#YMSE: Error while unlocking space
unlockSpaceFailureMessage=Kan ikke låse opp rommet.
#YMSE: Error while creating space
createSpaceError=Kan ikke opprette rommet.
#YMSE: Error while creating duplicate space
createDuplicateSpaceError=Det finnes allerede et rom med navnet {0}.
#YMSE: Error while deleting a single space
deleteSpaceError=Kan ikke slette rommet.
#YMSE: Error details while deleting a single space
deleteSpaceDetailedError=Rommet "{0}" fungerer ikke som det skal lenger. Vent før du prøver å slette igjen. Hvis det fortsatt ikke fungerer, ber du administratoren din om å slette rommet eller åpne en henvendelse.
#YMSE: Error while deleting a single space in files
deleteSpaceInFilesError=Kan ikke slette romdataene i Filer.
#YMSE: Error while deleting a single space users
deleteSpaceUsersError=Kan ikke fjerne brukerne.
#YMSE: Error while deleting a single space schema
deleteSpaceSchemasError=Kan ikke fjerne skjemaene.
#YMSE: Error while deleting a single space connections
deleteSpaceConnectionsError=Kan ikke fjerne forbindelsene.
#YMSE: Error while deleting a single space data
deleteSpaceDataError=Kan ikke slette romdataene.
#YMSE: Error while deleting multiple spaces
deleteSpacesError=Kan ikke slette rommene.
#YMSE: Error while restoring a single space
restoreSpaceError=Kan ikke gjenopprette rommet.
#YMSE: Error while restoring multiple spaces
restoreSpacesError=Kan ikke gjenopprette rommene.
#YMSE: Error while creating users
createUsersError=Kan ikke legge til brukerne.
#YMSE: Error while removing users
removeUsersError=Vi kan ikke fjerne brukerne.
#YMSE: Error while removing user
removeUserError=kan ikke fjerne brukeren.
#YMSE: Error assigning roles to a user
roleAssignUnAssignError=Vi kan ikke legge til brukeren i de valgte rollene i omfanget. \n\n Du kan ikke legge til deg selv i rollen i omfanget. Du kan be administratoren din om å legge deg til i en rolle i omfanget.
#YMSE: Error assigning user to the space
userAssignError=Vi kan ikke tilordne brukeren til rommet. \n\n Brukeren er allerede tilordnet til maksimalt antall rom (100) som er tillatt på tvers av roller i omfanget.
#YMSE: Error assigning users to the space
usersAssignError=Vi kan ikke tilordne brukerne til rommet. \n\n Brukeren er allerede tilordnet til maksimalt antall rom (100) som er tillatt på tvers av roller i omfanget.
#YMSE: Error while fetching users
fetchUserWithScopedRolesError=Vi kan ikke hente brukerne. Prøv igjen senere.
#YMSE: Error while fetching scoped roles
fetchScopedRolesError=Vi kan ikke hente rollene i omfanget.
#YMSE: Error while fetching members
fetchUserError=Kan ikke hente medlemmene. Prøv igjen senere.
#YMSE: Error while loading run-time database
loadRuntimeError=Vi kan ikke laste informasjon fra kjøringstidsdatabasen.
#YMSE: Error while loading spaces
loadSpacesError=Det oppstod en feil under forsøk på å hente rommene dine.
#YMSE: Error while loading haas resources
loadStorageError=Det oppstod en feil under forsøk på å hente lagringsplassdataene.
#YMSE: Error no data could be loaded
loadDataError=Det oppstod en feil under forsøk på å hente dataene dine.
#XFLD: Click to refresh storage data
clickToRefresh=Klikk her for å prøve på nytt.
#XTIT: Delete space popup title
deleteSpacePopupTitle=Slett rom
#XCOL: Spaces table-view column name
name=Navn
#XCOL: Spaces table-view deployment status
deploymentStatus=Distribueringsstatus
#XFLD: Disk label in space details
storageLabel=Disk (GB)
#XFLD: In-Memory label in space details
ramLabel=Minne (GB)
#XFLD: Memory label on space card
memory=Minne for lagringsplass
#XFLD: Storage Assignment label in space details
storrageAssignmentLabel=Lagringsplass for rom
#XFLD: Storage Type label in space details
storageTypeLabel=Lagringsplasstype
#XFLD: Enable Space Quota
enableSpaceQuota=Akiver romkvote
#XFLD: No Space Quota
noSpaceQuota=Ingen romkvote
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
diskAndInMemoryText=SAP HANA-database (Disk og In-Memory)
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
dataLakeFilesText=SAP HANA-datasjøfiler
#XFLD: Available scoped roles label
availableRoles=Tilgjengelige roller i omfanget
#XFLD: Selected scoped roles label
selectedRoles=Valgte roller i omfanget
#XCOL: Spaces table-view column models
models=Modeller
#XCOL: Spaces table-view column users
users=Brukere
#XCOL: Spaces table-view column connections
connections=Forbindelser
#XFLD: Section header overview in space detail
overview=Oversikt
#XFLD: Section header Apache Spark in space detail
sparkManagementSection=Apache Spark
#XFLD: Section header Apache Spark in space detail
sparkManagementSubSection=Applikasjoner
#XFLD: Section header Apache Spark in space detail
taskAssignmentSubSection=Oppgavetilordning
#XFLD: vCPU label in Apache Spark section
vCPULabel=vCPU-er
#XFLD: Memory label in Apache Spark section
memoryLabel=Minne (GB)
#XFLD: space configuration label in Apache Spark section
spaceConfigurationLabel=Romkonfigurasjon
#XFLD: Space Source label
sparkApplicationLabel=Applikasjon
#XFLD: Cluster Size label
clusterSizeLabel=Clusterstørrelse
#XFLD: Driver label
driverLabel=Driver
#XFLD: Executor label
executorLabel=Utfører
#XFLD: max label
maxLabel=Maks. brukt
#XFLD: TrF Default label
trFDefaultLabel=Transformasjonsflyt standard
#XFLD: Merge Default label
mergeDefaultLabel=Sammenslåing standard
#XFLD: Optimize Default label
optimizeDefaultLabel=Optimer standard
#XFLD: Deployment Default label
deploymentDefaultLabel=Distribusjon av lokal tabell (fil)
#XFLD: max spark source label
driverExecutorMaxUsedSparkSource={0} CPU / {1} GB
#XFLD: Object type label
taskObjectTypeLabel=Objekttype
#XFLD: Task activity label
taskActivityLabel=Aktivitet
#XFLD: Task Application ID label
taskApplicationIDLabel=Standardapplikasjon
#XFLD: Section header in space detail
generalSettings=Generelle innstillinger
#XMSG: Message strip message when a space is locked
spaceLockedMessageStrip=Dette rommet er for øyeblikket sperret av systemet.
#XMSG: Message strip message deploy warning
deployWarningMessageStrip=Endringer i denne delen blir distribuert med en gang.
#XMSG: Message strip message modify space configuration warning
modifySpaceConfigurationMessageStrip=Vær oppmerksom på at hvis du endrer disse verdiene, kan det føre til problemer med ytelsen.
#XFLD: Button text to unlock the space again
unlockSpace=Opphev sperring av rom
#XFLD: Info text for audit log formatted message
auditLogText=Aktiver revisjonsprotokoller for å registrere lese- eller endringshandlinger (revisjonspolicyer). Administratorer kan deretter analysere hvem som utførte hvilken handling på hvilket tidspunkt.
#XMSG: Message strip  audit warning
auditLogWarningMessageStrip=Revisjonsprotokoller kan bruke en stor del av disklagringsplassen i klienten. Hvis du aktiverer en revisjonspolicy (lese- eller endringshandlinger), må du overvåke disklagringsbruken regelmessig (via kortet Disklagringsplass brukt i Systemovervåking) for å unngå at all disklagringsplass brukes, noe som kan føre til at tjenester blir avbrutt. Hvis du deaktiverer en revisjonspolicy, blir alle oppføringene i revisjonsprotokollen slettet. Hvis du vil beholde oppføringene i revisjonsprotokollen, kan du eksportere dem før du deaktiverer revisjonspolicyen.
#XFLD: Message strip link text to open in app help to guide user what to do to unlock his space permanently
unlockSpaceHelp=Vis hjelp
#XMSG: Message strip message when a space will be locked again
spaceTemporarilyUnlockedMessageStrip=Dette rommet overskrider romlagringsplassen og sperres om {0} {1}.
#XMSG: Unit for remaining time until space is locked again
hours=timer
#XMSG: Unit for remaining time until space is locked again
minutes=minutter
#XFLD: Section header in space detail for auditing
detailsSectionAuditing=Revisjon
#XFLD: Subsection header in space detail for auditing
auditing=Innstillinger for romrevisjon
#XFLD: Hot space tooltip
hotSpaceCountTooltip=Kritiske rom: Brukt lagringsplass er mer enn 90 %.
#XFLD: Green space tooltip
greenSpaceCountTooltip=OK rom: Brukt lagringsplass ligger på mellom 6 % og 90 %.
#XFLD: Cold space tooltip
coldSpaceCountTooltip=Kalde rom: Brukt lagringsplass er 5 % eller mindre.
#XFLD: Critical space tooltip
criticalSpaceCountTooltip=Kritiske rom: Brukt lagringsplass er mer enn 90 %.
#XFLD: Green space tooltip
okSpaceCountTooltip=OK rom: Brukt lagringsplass ligger på mellom 6 % og 90 %.
#XFLD: Locked space tooltip
lockedSpaceCountTooltipNew=Sperrede rom: Blokkert på grunn av for lite minne.
#XFLD: Locked space tooltip
lockedSpaceCountTooltip=Sperrede rom
#YMSE: Error while deleting remote source
deleteRemoteError=Kan ikke fjerne forbindelsene.
#XFLD: Space ID input tooltip
spaceIDInfoTooltip=Rom-ID-en kan ikke endres senere.\nGyldige tegn: A–Z, 0–9 og _
#YMSE: Error when space name is empty
spaceNameEmptyValidationError=Oppgi romnavn.
#YMSE: Error when ECN Business name is empty
ecnNameEmptyValidationError=Oppgi et forretningsnavn.
#YMSE: Error when space id is empty
spaceIDInputEmptyValidationError=Oppgi rom-ID.
#YMSE: Error when space id contains invalid characters
spaceIDInputInValidError=Ugyldige tegn. Du kan bare bruke A–Z, 0–9 og _.
#YMSE: Error when space id exists while creation
spaceIDInputDuplicateError=Rom-ID-en finnes allerede.
#XFLD: Space searchfield placeholder
search=Søk
#XMSG: Success message after creating users
createUsersSuccess=Brukere er lagt til
#XMSG: Success message after creating user
createUserSuccess=Bruker er lagt til
#XMSG: Success message after updating users
updateUsersSuccess={0} brukere er oppdatert
#XMSG: Success message after updating user
updateUserSuccess=Bruker er oppdatert
#XMSG: Success message after removing users
removeUsersSuccess={0} brukere er fjernet
#XMSG: Success message after removing user
removeUserSuccess=Bruker er fjernet
#XFLD: Schema name
schemaName=Skjemanavn
#XFLD: used of total
ofTemplate={0} av {1}
#XFLD: Assigned disk label in space overview
assignedStorageTemplate=Tilordnet disk ({0} av {1})
#XFLD: Assigned Memory label in space overview
assignedRAMTemplate=Tilordnet minne ({0} av {1})
#XFLD: Storage ratio on space
accelearationRAM=Minneakselerasjon
#XFLD: No Storage Consumption
noStorageConsumptionText=Ingen lagringsplasskvote tilordnet.
#XFLD: Used disk label in space overview
usedStorageTemplate=Disk brukt for lagring ({0} av {1})
#XFLD: Used Memory label in space overview
usedRAMTemplate=Minne brukt for lagring ({0} av {1})
#XFLD: Used Disk tooltip
usedStorageTooltipTemplate= {0} av {1} disk brukt for lagring
#XFLD: Used Memory tooltip
usedRAMTooltipTemplate={0} av {1} minne brukt
#XFLD: Assigned Disk tooltip
assignedStorageTooltipTemplate={0} av {1} disk tilordnet
#XFLD: Assigned Memory tooltip
assignedRAMTooltipTemplate={0} av {1} minne tilordnet
#XFLD: Space related used storage tooltip
spaceRelatedUsedStorageTooltipTemplate=Romdata: {0}
#XFLD: Not space related used storage tooltip
otherDataUsedStorageTooltipTemplate=Andre data: {0}
#XFLD: Storage used exceeded popover and tooltip
storageUsedExceededText=Vurder å utvide planen eller kontakte SAP-brukerstøtte.
#XCOL: Space table-view column used Disk
usedStorage=Disk brukt for lagring
#XCOL: Space monitor column used Memory
usedRAM=Minne brukt for lagring
#XCOL: Space monitor column Schema
tableSchema=Skjema
#XCOL: Space monitor column Storage Type
tableStorageType=Lagringsplasstype
#XCOL: Space monitor column Table Type
tableType=Tabelltype
#XCOL: Space monitor column Record Count
tableRecordCount=Antall poster
#XFLD: Assigned Disk
assignedStorage=Disk tilordnet for lagring
#XFLD: Assigned Memory
assignedRAM=Minne tilordnet for lagring
#XCOL: Space table-view column storage utilization
tableStorageUtilization=Brukt lagringsplass
#XFLD: space status
spaceStatus=Romstatus
#XFLD: space type
spaceType=Romtype
#XFLD: space type label for space with space type "SAP BW bridge" ("SAP BW bridge" is a product name, do not translate it)
abapbridgeSpaceType=SAP BW-bro
#XFLD: space type label for space with space type "Data Marketplace"
marketplaceSpaceType=Dataleverandørprodukt
#XMSG: Error message for when selected space cannot be deleted due to the space type
deleteTypedSpaceError=Du kan ikke slette rom {0}, da romtypen er {1}.
#XMSG: Error message for when selected spaces cannot be deleted due to the space type
deleteTypedSpacesError=Du kan ikke slette {0} valgte rom. Rom med følgende romtyper kan ikke slettes: {1}.
#XFLD: space type label for space without space type
defaultSpaceType=SAP Datasphere
#XFLD: Tooltip for monitor space button
monitorSpace=Overvåk
#XFLD: Tooltip for edit space button
editSpace=Rediger rom
#XMSG: Deletion warning in messagebox
deleteConfirmation=Er du sikker på at du vil slette dette rommet?
#XFLD: Tooltip for delete space button
deleteSpace=Slett rom
#XFLD: storage
storage=Disk for lagring
#XFLD: username
userName=Brukernavn
#XFLD: port
port=Port
#XFLD: hostname
hostName=Vertsmaskinnavn
#XFLD: password
password=Passord
#XBUT: Request new password button
requestPassword=Be om nytt passord
#YEXP: Usage explanation in time data section
timeDataSectionHint=Opprett tidstabeller og dimensjoner du kan bruke i modeller og historier.
#YEXP: Usage explanation in Space schema section
spaceSchemaHint=Vil du at dataene i rommet ditt skal kunne forbrukes av andre verktøy eller apper? Er svaret ja, oppretter du én eller flere brukere som har tilgang til dataene i rommet ditt, og velger om du vil at alle fremtidige romdata skal kunne forbrukes som standard.
#XTIT: Create schema popup title
createSchemaDialogTitle=Opprett åpent SQL-skjema
#XTIT: Generate Time Data dialog title
generateTimeDataDialogTitle=Opprett tidstabeller og dimensjoner
#XTIT: Edit Time Data dialog title
editTimeDataDialogTitle=Rediger tidstabeller og dimensjoner
#XTIT: Time Data token title
timeDataTokenTitle=Tidsdata
#XTIT: Time Data token title
timeDataUpdateViews=Oppdater tidsdatavisninger
#XFLD: Time Data token creation in progress label
timeDataCreationInProgress=Oppretting pågår ...
#XFLD: Time Data token creation error label
timeDataCreationError=Opprettingen mislyktes. Prøv på nytt.
#XTIT: Time Data dialog time settings title
timeDataDialogTimeSettingsTitle=Innstillinger for tidstabell
#XTIT: Time Data dialog time data table title
timeDataDialogTranslationTablesTitle=Omregningstabell
#XTIT: Time Data dialog dimensions title
timeDataDialogTimeViewsTitle=Tidsdimensjoner
#XFLD: Time Data dialog time range label
timeRangeHint=Definer tidsintervallet.
#XFLD: Time Data dialog time data table label
timeDataHint=Gi tabellen din et navn.
#XFLD: Time Data dialog time data dimensions label
timeDimensionsHint=Gi dimensjonene dine et navn.
#XFLD: Time Data Time range description label
timerangeLabel=Tidsintervall
#XFLD: Time Data dialog from year label
fromYearLabel=Fra år
#XFLD: Time Data dialog to year label
toYearLabel=Til år
#XFLD: Time Data dialog calendar type label
calendarTypeLabel=Kalendertype
#XFLD: Time Data dialog granularity label
granularityLabel=Granularitet
#XFLD: Time Data dialog technical name label
technicalNameLabel=Teknisk navn
#XFLD: Time Data dialog quarters text table lable
quarterTextsTableLabel=Omregningstabell for kvartal
#XFLD: Time Data dialog months text table lable
monthsTextsTableLabel=Omregningstabell for måneder
#XFLD: Time Data dialog days text table lable
daysTextsTableLabel=Omregningstabell for dager
#XFLD: Time Data dialog year label
yearLabel=Årdimensjon
#XFLD: Time Data dialog quarter label
quarterLabel=Kvartaldimensjon
#XFLD: Time Data dialog month label
monthLabel=Månedsdimensjon
#XFLD: Time Data dialog day label
dayLabel=Dagsdimensjon
#XFLD: Time Data dialog gregorian calendar type label
gregorian=Gregoriansk
#XFLD: Time Data dialog time granularity day label
day=Dag
#XFLD: Time Data dialog business name maxlength reached
timeRangeValueStateTextMaxLengthBusinessName=Den maksimale lengden på 1000 tegn er nådd.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextMaxRangeExceeded=Det maksimale tidsintervallet er 150 år.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeLower="Fra år" skal være lavere enn "Til år"
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeHigher="Fra år" skal være 1900 eller høyere.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeHigher="Til år" skal være høyere enn "Fra år"
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeLower="Til år" skal være lavere enn inneværende år pluss 100
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningFromYear=Hvis du øker "Fra år", kan det føre til tap av data
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningToYear=Hvis du reduserer "Til år", kan det føre til tap av data
#XMSG: Time Data creation validation error message
timeDataValidationError=Det ser ut til at noen av feltene er ugyldige. Kontroller de obligatoriske feltene for å fortsette.
#XMSG: Time Data deletion popup text
deleteTimeDataConfirmation=Er du sikker på at du vil slette dataene?
#XMSG: Time Data creation success message
createTimeDataSuccess=Tidsdata opprettet
#XMSG: Time Data update success message
updateTimeDataSuccess=Tidsdata oppdatert
#XMSG: Time Data delete success message
deleteTimeDataSuccess=Tidsdata slettet
#XMSG: Time Data creation error message
createTimeDataError=Det oppstod en feil under forsøk på å opprette tidsdata.
#XMSG: Time Data update error message
updateTimeDataError=Det oppstod en feil under forsøk på å oppdatere tidsdata.
#XMSG: Time Data creation error message
deleteTimeDataError=Det oppstod en feil under forsøk på å slette tidsdata.
#XMSG: Error message when time data could not be retrieved
retrievingTimeDataError=Kan ikke laste tidsdata.
#XTIT: Time Data deletion popup title
deleteTimeDataConfirmationTitle=Advarsel
#XTXT: Time Data deletion dependency popup text
deleteTimeDataDependencyText=Vi kan ikke slette tidsdataene dine fordi de brukes i andre modeller.
#XTXT: Time Data deletion dependency popup text singular
deleteTimeDataDependencyTextSingular=Vi kan ikke slette tidsdataene dine fordi de brukes i en annen modell.
#XMSG: Validation error when required time data form field is empty
timeDataRequiredField=Dette feltet er obligatorisk og kan ikke stå tomt.
#XFLD: Tooltip for Open in Database Explorer button
openInDBX=Åpne i databaseutforsker
#YMSE: Dimension Year
dimensionYearView=Dimensjon "År"
#YMSE: Dimension Year
dimensionQuarterView=Dimensjon "Kvartal"
#YMSE: Dimension Year
dimensionMonthView=Dimensjon "Måned"
#YMSE: Dimension Year
dimensionDayView=Dimensjon "Dag"
#XFLD: Time Data deletion object title
timeDataUsedIn=(brukes i {0} modeller)
#XFLD: Time Data deletion object title singular
timeDataUsedInSingular=(brukes i 1 modell)
#XFLD: Time Data deletion table column provider
provider=Leverandør
#XTIT: Time Data dependency dialog title
timeDataDependencyDialogTitle=Avhengigheter
#XTIT: Create schema popup title
createSpaceSchemaUserDialogTitle=Opprett bruker for romskjema
#XFLD: Create schema button
createSchemaButton=Opprett åpent SQL-skjema
#XFLD: Generate TimeData button
generateTimeDataButton=Opprett tidstabeller og dimensjoner
#XFLD: Show dependencies button
showDependenciesButton=Vis avhengigheter
#XFLD: tooltip for disabled buttons when user is not member of space
mustBeMemberOfSpaceForOperation=Brukeren din må være medlem av rommet for å kunne utføre denne handlingen.
#XFLD: Create space schema user button
createSpaceSchemaUserButton=Opprett romskjemabruker
#YMSE: API Schema users load error
loadSchemaUsersError=Kan ikke laste listen over brukere.
#XTIT: API Schema-config popup title
schemaUserDetailsDialogTitle=Detaljer om romskjemabruker
#XMSG: API Schema user deletion popup text
deleteSpaceSchemaUserConfirmation=Er du sikker på at du vil slette den valgte brukeren?
#XMSG: API Schema user deletion confirmation text
userDeleteSuccess=Brukeren er slettet.
#YMSE: API Schema user deletion error
userDeleteError=Kan ikke slette brukeren.
#XFLD: User deleted
userDeleted=Brukeren er slettet.
#XTIT: Remove user popup title
removeUserConfirmationTitle=Advarsel
#XMSG: Remove user popup text
removeUserConfirmation=Er du sikker på at du vil fjerne brukeren? Brukeren og de tilordnede rollene i omfanget vil bli fjernet fra rommet.
#XMSG: Remove users popup text
removeUsersConfirmation=Er du sikker på at du vil fjerne brukerne? Brukerne og de tilordnede rollene i omfanget vil bli fjernet fra rommet.
#XBUT: Confirmation button for remove user dialog
removeUserConfirmationBtn=Fjern
#YMSE: No data text for available roles
noDataAvailableRoles=Rommet er ikke lagt til i en rolle i omfanget. \n Hvis du vil legge til brukere i rommet, må det først bli lagt til i én eller flere roller i omfanget.
#YMSE: No data text for selected roles
noDataSelectedRoles=Ingen valgte roller i omfanget
#XTIT: Schema-config popup title
schemaDetailsDialogTitle=Åpne konfigurasjonsdetaljer for SQL-skjema
#XFLD: Label for Read Audit Log
auditLogRead=Aktiver revisjonsprotokoll for leseoperasjoner
#XFLD: Label for Change Audit Log
auditLogChange=Aktiver revisjonsprotokoll for endringsoperasjoner
#XFLD: Label Audit Log Retention
auditLogRetention=Behold protokoller i
#XFLD: Label Audit Log Retention Unit
retentionUnit=Dager
#YMSE: Error when log retention time is set incorrectly
incorrectRetentionTime=Oppgi et heltall mellom {0} og {1}
#XFLD: Label for Cross Space Schema Access
spaceSchemaAccess=Forbruk romskjemadata
#XTIT: Confirm deactivation of space schema consumption dialog title
disableSpaceSchemaConsumptionDialogTitle=Stopp forbruk av romskjemadata
#XTXT: Confirm deactivation of space schema consumption dialog text
disableSpaceSchemaConsumptionDialogText=Dette åpne SQL-skjemaet kan forbruke dataene til romskjemaet ditt. Hvis du stopper forbruket, kan det hende at modeller som er basert på romskjemadataene ikke fungerer lenger.
#XBUT: Confirmation button for the stop consuming space schema data dialog
stopConsuming=Stopp forbruk
#XTXT: Tooltip for data lake icon
dataLakeIconTooltip=Dette rommet brukes for å få tilgang til datasjøen
#XFLD: Tooltip for data lake table entry
dataLakeTableEntryTooltip=Datasjø aktivert
#XFLD: used In-Memory linit reached tag
usedRamLimitReachedLabel=Grense for minne er nådd
#XFLD: Space storage Limit reached tag
storageLimitReachedLabel=Grensen for lagringsplass er nådd
#XFLD: Minimal Storage Limit Reached tag
minStorageLimitReachedLabel=Grensen for min. lagringsplass er nådd
#XFLD: Space ram tag
ramLimitReachedLabel=Grense for minne er nådd
#XFLD: Minimal In-Memory Limit Reached tag
minRamLimitReachedLabel=Minimumsminnegrense er nådd
#XFLD: Popover text after click on space storage limit warning
storageLimitReachedText=Du har nådd grensen for tilordnet romlagringsplass på {0}. Tilordne mer lagringsplass til rommet.
#XFLD: System storage tag
systemStorageLimitReachedLabel=Grensen for systemlagringsplass er nådd
#XMSG: Popover text after click on storage limit warning
systemStorageLimitReachedText=Du har nådd grensen for systemlagringsplass på {0}. Du kan ikke tilordne mer lagringsplass til rommet nå.
#XMSG: Schema deletion warning
deleteSchemaConfirmation=Hvis du sletter dette åpne SQL-skjemaet, slettes også alle lagrede objekter og vedlikeholdte tilknytninger i skjemaet for godt. Vil du fortsette?
#XMSG: Success message after deleting a schema
schemaDeleteSuccess=Skjemaet er slettet.
#YMSE: Error while deleting schema.
schemaDeleteError=Kan ikke slette skjemaet.
#XMSG: Success message after update a schema
schemaUpdateSuccess=Skjemaet er oppdatert.
#YMSE: Error while updating schema.
schemaUpdateError=Kan ikke oppdatere skjemaet.
#XMSG: Hint when no password is shown in the dialog.
newPasswordHint=Vi har gitt deg et passord for dette skjemaet. Hvis du har glemt passordet eller mistet det, kan du be om et nytt. Husk å kopiere eller lagre det nye passordet.
#XMSG: Hint when showing the password in the dialog.
savePasswordHint=Kopier passordet. Du trenger det for å konfigurere en forbindelse til dette skjemaet. Hvis du har glemt passordet, kan du åpne denne dialogen for å tilbakestille det.
#XFLD,50: Hint on entering schema name in creation dialog
schemaNameHint=Navnet kan ikke endres når skjemaet er opprettet.
#XFLD: Open SQL Schemas section sub headline
schemasSQL=Åpen SQL
#XFLD: Space schema section sub headline
schemasSpace=Rom
#XFLD: HDI Container section header
HDIContainers=HDI-containere
#XTXT: Add HDI Containers button tooltip
addHDIContainers=Legg til HDI-containere
#XTXT: Remove HDI Containers button tooltip
removeHDIContainers=Fjern HDI-containere
#XFLD: Enable HDI Containers feature button
enableHDIContainersButton=Aktiver tilgang
#YMSE: No data text for HDI Containers table
noDataHDIContainers=Det er ikke lagt til noen HDI-containere.
#YMSE: No data text for Timedata section
noDataTimedata=Ingen tidstabeller og dimensjoner er opprettet.
#YMSE: No data text during runtime not available for Timedata section
runtimeNotAvailableNoDataTimedata=Kan ikke laste tidstabeller og dimensjoner fordi kjøretidsdatabasen er utilgjengelig. 
#YMSE: No data text during runtime not available for HDI section
runtimeNotAvailableNoDataHDI=Kan ikke laste HDI-containere fordi kjøretidsdatabasen er utilgjengelig. 
#YMSE: Error while fetching HDI Containers
fetchHDIContainersError=Vi kan ikke hente HDI-containerne. Prøv igjen senere.
#XFLD Table column header for HDI Container names
HDIContainerName=Navn på HDI-container
#XFLD: Enable HDI Containers feature dialog title
enableHDIContainersDialogTitle=Aktiver tilgang
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText1New=Du kan aktivere SAP SQL Data Warehousing på SAP Datasphere-tenanten for å utveksle data mellom HDI-containerne dine og SAP Datasphere-rommene dine uten at du trenger å flytte data.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText2=Du gjør dette ved å åpne en supporthenvendelse ved å klikke på knappen nedenfor.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText3New=Når henvendelsen din er behandlet, må du bygge en eller flere nye HDI-containere i kjøringstidsdatabasen for SAP Datasphere. Da erstattes knappen "Aktiver tilgang" med knappen "+" i delen HDI-containere for alle SAP Datasphere-rommene dine, og du kan legge til containerne i et rom.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText41=Trenger du mer informasjon? Gå til %%0. Hvis du trenger detaljert informasjon om hva som skal tas med i henvendelsen, kan du se %%1.
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkText=SAP Hjelp
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkTextSapNote3057059=SAP Note 3057059
#XBUT: Open Ticket Button Text
openTicket=Åpne henvendelse
#XBUT: Add Button Text
add=Legg til
#XBUT: Next Button Text
next=Neste
#XBUT: Edit Button Text
editUsers=Rediger
#XBUT: create user Button Text
createUser=Opprett
#XBUT: Update user Button Text
updateUser=Velg
#XTIT: Add HDI Containers Dialog Title
addHDIContainersDialogTitle=Legg til HDI-containere som ikke er tilordnet
#XFLD: No data text for add HDI container dialog
addHDIContainersDialogNoDataText=Vi finner ingen beholdere som ikke er tilordnet. \n Beholderen du ser etter, er kanskje allerede tilordnet et rom.
#YMSE: Error while loading assigned HDI Containers
loadAssignedHDIContainersError=Vi kan ikke laste tilordnede HDI-containere.
#YMSE: Error while loading assigned HDI Containers
loadUnassignedHDIContainersError=Vi kan ikke laste HDI-containere.
#XMSG: Success message
succeededToAddHDIContainer=HDI-container lagt til.
#XMSG: Success message
succeededToAddHDIContainerPlural=HDI-containere lagt til.
#XMSG: Success message
succeededToDeleteHDIContainer=HDI-container fjernet.
#XMSG: Success message
succeededToDeleteHDIContainerPlural=HDI-containere fjernet.
#XFLD: Time data section sub headline
timeDataSection=Tidstabeller og dimensjoner
#XFLD: Schemas list entry read audit log enabled indicator
readIndicator=Les
#XFLD: Schemas list entry change audit log enabled indicator
changeIndicator=Endre
#XFLD: Remote sources section sub headline
allconnections=Tilordning av forbindelse
#XFLD: Remote sources section sub headline
localconnections=Lokale forbindelser
#XFLD: Remote sources section sub headline for open Connectors
openConnectors=SAP Open Connectors
#XFLD: User section sub headline
memberassignment=Tilordning av medlem
#XFLD: User assignment section sub headline
userAssignment=Brukertilordning
#XFLD: User section Access dropdown Member
member=Medlem
#XFLD: User assignment section column name
user=Brukernavn
#XTXT: Selected role count
selectedRoleToolbarText=Valgt: {0}
#XTIT: Space detail section connections title
detailsSectionConnections=Forbindelser
#XTIT: Space detail section data access title
detailsSectionDataAccess=Skjematilgang
#XTIT: Space detail section time data title
detailsSectionGenerateData=Tidsdata
#XTIT: Space detail section members title
detailsSectionUsers=Medlemmer
#XTIT: Space detail section Users title
detailsSectionUsersTitle=Brukere
#XTIT: Out of Storage
insufficientStoragePopoverTitle=Ingen tilgjengelig lagringsplass
#XTIT: Storage distribution
storageDistributionPopoverTitle=Disklagringsplass brukt
#XTXT: Out of Storage popover text
insufficientStorageText=Hvis du vil opprette et nytt rom, må du redusere den tilordnede lagringsplassen til et annet rom eller slette et rom som du ikke trenger lenger. Du kan øke den totale systemlagringsplassen ved å anrope Administrer plan.
#XMSG: Space id length warning
spaceIdLengthWarning=Maksgrensen på {0} tegn er overskredet.
#XMSG: Space name length warning
spaceNameLengthWarning=Maksgrensen på {0} tegn er overskredet.
#XMSG: Space id reserved prefix warning
spaceIdnWarningReservedPrefix=Ikke bruk prefikset {0}, da unngår du mulige konflikter.
#YMSE: Error while loading open SQL schemas
loadLocalSchemasError=Kan ikke laste åpne SQL-skjemaer.
#YMSE: Error while creating open SQL schema
createLocalSchemaError=Kan ikke opprette åpne SQL-skjemaer
#YMSE: Error while loading all remote connections
loadRemoteConnectionsError=Kan ikke laste alle eksterne forbindelser.
#YMSE: Error while loading space details
loadSpaceDetailsError=Kan ikke laste romdetaljene.
#YMSE: Error while deploying space details
deploySpaceDetailsError=Kan ikke distribuere rom.
#YMSE: Error while copying space details
copySpaceDetailsError=Kan ikke kopiere rom.
#YMSE: Error while loading storage data
loadStorageDataError=Kan ikke laste lagringsdata.
#YMSE: Error while loading all users
loadAllUsersError=Kan ikke laste alle brukere.
#YMSE: Failed to reset password
resetPasswordError=Kan ikke tilbakestille passord.
#XMSG: Success message after updating space
resetPasswordSuccessMessage=Nytt passord er definert for skjema.
#YMSE: DP Agent-name too long
DBAgentNameError=Navnet på DP-agenten er for langt.
#YMSE: Schema-name not valid.
schemaNameError=Ugyldig navn på skjema.
#YMSE: User name not valid.
UserNameError=Ugyldig brukernavn.
#XTIT: Consumption by Storage Type
consumptionStorageTypeText=Forbruk etter lagringsplasstype
#XTIT: Consumption by Schema
consumptionSchemaText=Forbruk etter skjema
#XTIT: Overall Consumption by schema
overallConsumptionSchemaText=Samlet tabellforbruk etter skjema
#XTIT: Overall Consumption by Table Type
overallConsumptionTableTypeText=Samlet forbruk etter tabelltype
#XTIT: Tables
tableDetailsText=Tabelldetaljer
#XTIT: Table Storage Consumption
tableStorageConsumptionText=Lagringsplassforbruk for tabell
#XFLD: Table Type label
tableTypeLabel=Tabelltype
#XFLD: Schema label
schemaLabel=Skjema
#XFLD: reset table tooltip
resetTable=Tilbakestill tabell
#XFLD: In-Memory label in space monitor
inMemoryLabel=Minne
#XFLD: Disk label in space monitor
diskLabel=Disk
#XFLD: Yes
yesLabel=Ja
#XFLD: No
noLabel=Nei
#XFLD: switch label: Want the data in this space to be consumable by default?
allowConsumptionByDefault=Vil du at dataene i dette rommet skal kunne forbrukes som standard?
#XFLD: Business Name
businessNameLabel=Forretningsnavn
#XFLD: Refresh
refresh=Oppdater
#XMSG: No filter results title
noFilterResultsTitle=Det ser ut til at filterinnstillingene dine ikke viser noen data.
#XMSG: No filter results message
noFilterResultsMsg=Prøv å avgrense filterinnstillingene dine. Hvis du fortsatt ikke ser noen data, kan du opprette noen tabeller i databyggeren. Når de forbruker lagringsplass, kan du overvåke dem her.
#XMSG: Space monitoring Hana state red title
spaceMonitorHanaRedTitle=Kjøringstidsdatabasen er ikke tilgjengelig.
#XMSG: Space monitoring Hana state red message
spaceMonitorHanaRedMsg=Siden kjøringstidsdatabasen ikke er tilgjengelig, er visse funksjoner deaktivert, og vi kan ikke vise informasjon på denne siden.
#YMSE: Error while creating space schema user
createSpaceSchemaUserError=Kan ikke opprette romskjemabruker.
#YMSE: Error User name already exists
userAlreadyExistsError=Brukernavnet finnes allerede.
#YMSE: Error Authentication failed
authenticationFailedError=Autentiseringen mislyktes.
#XMSG: Message strip message when a schema user is locked
userLockedMessageStrip=Brukeren er sperret på grunn av for mange mislykkede påloggingsforsøk. Be om et nytt passord for å oppheve sperringen av brukeren.
#XMSG: Success message after reset and unlock schema user
resetUnlockPasswordSuccessMessage=Nytt passord er definert, og sperringen av brukeren er opphevet.
#XMSG: user is locked message
userLockedMessage=Brukeren er sperret.
#XCOL: Users table-view column Role
spaceRole=Rolle
#XCOL: Users table-view column Scoped Role
spaceScopedRole=Rolle i omfanget
#XCOL: Users table-view column Space Admin
spaceAdmin=Romadministrator
#XFLD: User section dropdown value Viewer
viewer=Viser
#XFLD: User section dropdown value Modeler
modeler=Modellerer
#XFLD: User section dropdown value Data Integrator
dataIntegrator=Dataintegrator
#XFLD: User section dropdown value Space Administrator
spaceAdministrator=Romadministrator
#XMSG: Success message -  Space Role updated successfully.
updateUserSpaceRoleSuccess=Romrolle er oppdatert.
#XMSG: Error message - Failed to update Space Role.
updateUserSpaceRoleError=Kunne ikke oppdatere romrollen.
#XFLD:
databaseUserNameSuffix=Suffiks for databasebrukernavn
#XTXT: Space Schema password text
spaceSchemaPasswordText=Hvis du vil sette opp en forbindelse til dette skjemaet, må du kopiere passordet. Hvis du har glemt passordet, kan du alltid be om å få et nytt.
#XFLD: Cloud Platform label in space schema dialog
cloudPlatformLabel=Cloud Platform
#XTXT: Cloud Platform text in space schema dialog
cloudPlatformText=Hvis du vil sette opp tilgang via denne brukeren, må du aktivere forbruk og kopiere påloggingsinformasjonen. Hvis du bare kan kopiere påloggingsinformasjonen uten passord, må du sørge for å legge inn passordet senere.
#XFLD: enable consumption in CloudPlatform label in space schema dialog
enableConsumptionInCloudPlatformLabel=Aktiver forbruk i Cloud Platform
#XFLD: Credentials for User-Provided Service label in space schema dialog
cloudPlatformCredentialsLabel=Påloggingsinformasjon for brukerlevert tjeneste:
#XFLD: Credentials for User-Provided Service (Without Password) label in space schema dialog
cloudPlatformCredentialsWithoutPasswordLabel=Påloggingsinformasjon for brukerlevert tjeneste (uten passord):
#XFLD: Copy Credentials label in space schema dialog
copyCredentialsLabel=Kopier påloggingsinformasjon uten passord
#XFLD: Copy Credentials label in space schema dialog
copyFullCredentialsLabel=Kopier hele påloggingsinformasjonen
#XFLD: Copy Password label in space schema dialog
copyPasswordLabel=Kopier passord
#XMSG: Credentials copied to clipboard
credentialsCopiedMessage=Påloggingsinformasjonen er kopiert til utklippstavlen
#XMSG: Password copied to clipboard
passwordCopiedMessage=Passordet er kopiert til utklippstavlen
#XMSG: create database user dialog title
createDatabaseUserDialogTitle=Opprett databasebruker
#XMSG: Database Users section title
databaseUsers=Databasebrukere
#XMSG: Database User Details dialog title
databaseUserDetailsDialogTitle=Detaljer for databasebruker
#XFLD: database user read audit log
databaseUserAuditLogRead=Aktiver revisjonsprotokoller for leseoperasjoner og behold protokoller i
#XFLD: database user change audit log
databaseUserAuditLogChange=Aktiver revisjonsprotokoller for endringsoperasjoner og behold protokoller i
#XMSG: Cloud Platform Access
cloudPlatformAccess=Cloud Platform-tilgang
#XFLD: Enable Consumption in Cloud Platform hint
enableConsumptionCloudPlatformHint=Konfigurer tilgang til HDI-containeren (SAP HANA Deployment Infrastructure) via denne databasebrukeren. For at du skal kunne koble til HDI-containeren må SQL-modellering være aktivert.
#XFLD: Enable HDI Consumption
enableConsumptionCloudPlatform=Aktiver HDI-forbruk
#XFLD: Enable Consumption hint
enableConsumptionHint=Vil du at dataene i rommet skal kunne forbrukes av andre verktøy eller apper?
#XFLD: Enable Consumption
enableConsumption=Aktiver SQL-forbruk
#XFLD: Enable Modeling
enableModeling=Aktiver SQL-modellering
#XMSG: Privileges for Data Modeling
privilegesModeling=Dataoverføring
#XMSG: Privileges for Data Consumption
privilegesConsumption=Dataforbruk for eksterne verktøy
#XFLD: SQL Modeling
sqlModeling=SQL-modellering
#XFLD: SQL Consumption
sqlConsumption=SQL-forbruk
#XFLD: enabled
enabled=Aktivert
#XFLD: disabled
disabled=Deaktivert
#XFLD: Edit Privileges
editPrivileges=Rediger tilgang
#XFLD: Open Database Explorer
openDBX=Åpne databaseutforsker
#XFLD: create database user hint
databaseCreateHint=Vær oppmerksom på at du ikke vil kunne endre brukernavnet etter at du har lagret.
#XFLD: Internal Schema Name
internalSchemaName=Navn på internt skjema
#YMSE: Failed to load database users
loadDatabaseUserError=Kan ikke laste databasebrukere
#YMSE: Failed to delete database user
deleteDatabaseUsersError=Kan ikke slette databasebrukere
#XMSG: Success message after deleting database user
deleteDatabaseUserSuccess=Databasebruker er slettet
#XMSG: Success message after deleting database users
deleteDatabaseUsersSuccess=Databasebrukere er slettet
#XMSG: Success message after creating database user
createDatabaseUserSuccessMessage=Databasebruker er opprettet
#YMSE: Failed to create database user
createDatabaseUserError=Kan ikke opprette databasebruker
#XMSG: Success message after updating database user
updateDatabaseUserSuccessMessage=Databasebruker er oppdatert
#YMSE: Failed to update database user
updateDatabaseUserError=Kan ikke oppdatere databasebruker
#XFLD: HDI Consumption
hdiConsumption=HDI-forbruk
#XTIT: Space detail section Database Access title
detailsSectionDatabaseAccess=Databasetilgang
#XTXT: Database users consumption hint
databaseUsersConsumptionHint=Definer at romdataene dine kan forbrukes, som standard. Modellene i byggerne tillater automatisk at dataene kan forbrukes.
#XFLD: Default Consumption of Space Data:
defaultConsumptionOfSpaceData=Standardforbruk av romdata:
#XFLD: Database User Name
databaseUserName=Databasebrukernavn
#XMSG: Database User creation validation error message
databaseUserValidationError=Det ser ut til at noen av feltene er ugyldige. Kontroller de obligatoriske feltene for å fortsette.
#XMSG: SQL modeling can’t be enabled since this user has been migrated.
migratedDatabaseUserHint=Dataoverføring kan ikke aktiveres fordi denne brukeren er migrert.
#XBUT: Remove Button Text
remove=Fjern
#XBUT: Remove Spaces Button Text
removeSpaces=Fjern rom
#XBUT: Remove Objects Button Text
removeObjects=Fjern objekter
#XMSG: No members have been added yet.
noMembersAssigned=Ingen medlemmer er lagt til ennå.
#XMSG: No users have been added yet.
noUsersAssigned=Ingen brukere er lagt til ennå.
#XMSG: No database users have been created or your filter is not showing any data.
noDatabaseUsers=Ingen databasebrukere er opprettet, eller filteret viser ingen data.
#XMSG: Please enter a user name.
noDatabaseUsername=Oppgi et brukernavn.
#XMSG: The user name is too long. Please use a shorter one.
databaseUsernameTooLong=Brukernavnet er for langt. Bruk et kortere navn.
#XMSG: No privileges have been enabled and this database user will have limited functionality. Do you still want to continue?
noPrivilegesConfirmationMessage=Ingen autorisasjoner er aktivert, og denne databasebrukeren vil ha begrenset funksjonalitet. Vil du fortsette likevel?
#XMSG: To enable audit logs for change operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogChangeMessage=Hvis du vil aktivere revisjonsprotokoller for endringsoperasjoner, må du også aktivere dataoverføring. Vil du gjøre dette?
#XMSG: To enable audit logs for read operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogReadMessage=Hvis du vil aktivere revisjonsprotokoller for leseoperasjoner, må du også aktivere dataoverføring. Vil du gjøre dette?
#XMSG: To enable HDI consumption data ingestion and data consumption needs to be enabled. Do you want to enable all?
enableHDIConsumptionMessage=Hvis du vil aktivere HDI-forbruk, må du også aktivere dataoverføring og dataforbruk. Vil du gjøre dette?
#XMSG:
databaseUserPasswordText=Hvis du vil sette opp en forbindelse til denne databasebrukeren, må du kopiere passordet. Hvis du har glemt passordet, kan du alltid be om å få et nytt.
#XTIT: Space detail section members title
detailsSectionMembers=Medlemmer
#XMSG: New password set
newPasswordSet=Nytt passord er definert
#XFLD: Data Ingestion
dataIngestion=Dataoverføring
#XFLD: Data Consumption
dataConsumption=Dataforbruk
#XFLD: Privileges
privileges=Autorisasjoner
#XFLD: Enable Data ingestion
enableDataIngestion=Aktiver dataoverføring
#XMSG: Log the read and change operations for data ingestion.
databaseUserAuditHint=Protokollfør lese- og endringsoperasjonene for dataoverføring.
#XMSG: Make your space data available in your HDI containers.
databaseUserHDIHint=Gjør romdataene tilgjengelige i HDI-containerne.
#XFLD: Enable Data consumption
enableDataConsumption=Aktiver dataforbruk
#XMSG: Allow other apps or tools to consume your space data.
enableDataConsumptionHint=Tillat at andre apper eller verktøy kan forbruke romdataene dine.
#XMSG: To set up access via this schema user copy the credentials to your user-provided service. In case you can only copy the credentials without a password make sure to add the password later.
databaseUserHDIConsumptionHint=Hvis du vil sette opp tilgang via denne databasebrukeren, kopierer du påloggingsinformasjonen til den brukerleverte tjenesten. Hvis du bare kan kopiere påloggingsinformasjonen uten passord, må du huske å legge til passordet senere.
#XFLD: Data Flow Runtime Capacity label in space overview
dataFlowRuntimeCapacity=Kjøringstidskapasitet for dataflyt ({0}:{1} timer av {2} timer)
#XFLD: Could not load Data Flow Runtime Capacity
dataFlowRuntimeCapacityError=Kan ikke laste kjøringstidskapasitet for dataflyt
#XMSG: User can grant data consumption to other users.
databaseUserConsumptionWithGrantOptionHint=Brukeren kan tildele dataforbruk til andre brukere.
#XFLD: Enable data consumption with grant option
enableConsumptionWithGrantOption=Aktiver dataforbruk med tildelingsalternativ
#XMSG: To enable data consumption with grant option data consumption needs to be enabled. Do you want to enable both?
enableConsumptionWithGrantMessage=For å aktivere dataforbruk med tildelingsalternativ må du aktivere dataforbruk. Vil du aktivere begge deler?
#XFLD: Enable Automated Predictive Library (APL) and Predictive Analysis Library (PAL)
allowUsingScriptServerFunctionality=Aktiver Automated Predictive Library (APL) og Predictive Analysis Library (PAL)
#XMSG: User can use SAP HANA Cloud embedded machine learning functions.
allowUsingScriptServerFunctionalityHint=Brukeren kan bruke innebygde maskinlæringsfunksjoner i SAP HANA Cloud.
#XFLD: Password Policy
passwordPolicy=Retningslinjer for passord
#XMSG: Password Policy
passwordPolicyHint=Aktiver eller deaktiver de konfigurerte retningslinjene for passord her.
#XFLD: Enable Password Policy
enablePasswordPolicy=Aktiver retningslinjer for passord
#XMSG: Read Access to the Space Schema
readAccessTitle=Lesetilgang til romskjemaet
#XMSG: read access hint
readAccessHint=Tillat at databasebrukeren kobler eksterne verktøy til romskjemaet og lesevisninger som er eksponert for forbruk.
#XFLD: Space Schema
spaceSchema=Romskjema
#XFLD: Enable Read Access (SQL)
enableReadAccess=Aktiver lesetilgang (SQL)
#XMSG: Allow the user to grant read access to other users.
withGrantOptionHint=Tillat at brukeren gir lesetilgang til andre brukere.
#XFLD: With Grant Option
withGrantOption=Med tildelingsalternativ
#XMSG: Make your space data available in your HDI containers.
enableHDIConsumptionHint=Gjør romdataene tilgjengelige i HDI-containerne.
#XFLD: Enable HDI Consumption
enableHDIConsumption=Aktiver HDI-forbruk
#XMSG: Write Access to the User’s Open SQL Schema
writeAccessTitle=Skrivetilgang til brukerens Open SQL-skjema
#XMSG: write access hint
writeAccessHint=Tillat at databasebrukeren kobler eksterne verktøy til brukerens Open SQL-skjema for å opprette dataentiteter og overføre data til bruk i rommet.
#XFLD: Open SQL Schema
openSQLSchema=Open SQL-skjema
#XFLD: Enable Write Access (SQL DDL & DML)
enableWriteAccess=Aktiver skrivetilgang (SQL, DDL og DML)
#XMSG: audit hint
auditHint=Protokollfør lese- og endringsoperasjonene i Open SQL-skjemaet.
#XMSG: data consumption hint
dataConsumptionHint=Eksponer som standard alle nye visninger for forbruk. Modellerere kan overstyre denne innstillingen for enkeltvisninger via bryteren "Eksponer for forbruk" i sidepanelet for visningen. Du kan også velge formatene som visningene skal eksponeres i.
#XFLD: Expose for Consumption by Default
exposeDataConsumption=Eksponer for forbruk som standard
#XMSG: database users hint consumption hint
databaseUsersHint2New=Opprett databasebrukere for å koble eksterne verktøy til SAP Datasphere. Definer rettigheter for å tillate at brukere leser romdata, og for å opprette dataentiteter (DDL) og overføre data (DML) til bruk i rommet.
#XFLD: Read
read=Les
#XFLD: Read (HDI)
readHDI=Les (HDI)
#XFLD: Write
write=Skriv
#XMSG: HDI Containers Hint
HDIContainersHint2=Aktiver tilgang til HDI-containerne dine (SAP HANA Deployment Infrastructure) i rommet ditt. Modellerere kan bruke HDI-artefakter som kilder for visninger, og HDI-klienter har tilgang til romdataene dine.
#XMSG: Open info dialog
openDatabaseUserInfoDialog=Åpne infodialogen
#XMSG: Database user is locked. Open info dialog to unlock
openLockedDatabaseUserInfoDialog=Databasebrukeren er sperret. Åpne dialogen for å oppheve sperringen.
#XFLD: Table
table=Tabell
#XFLD: Space section partner configuration title
partnerConnectionConfigurationSectionTitle=Partnerforbindelse
#XFLD: Space section partner configuration subtitle
partnerConnectionConfigurationSectionSubTitle=Konfigurasjon av partnerforbindelse
#XFLD: Space section partner configuration hint
partnerConnectionConfigurationSectionHint=Definer din egen partnerforbindelsesrute ved å legge til iFrame-URL og -ikon. Denne konfigurasjonen er tilgjengelig bare for denne tenanten.
#XFLD: Table Name Field
partnerConnectionConfigurationName=Navn på rute
#XFLD: Table iFrame URL field
partnerConnectionConfigurationiFrameURL=iFrame-URL
#XFLD: Table iFrame Post Message Origin field
partnerConnectionConfigurationiFramePostMessageOrigin=iFrame-PostMessage - opprinnelse
#XFLD: Table icon/image name
partnerConnectionConfigurationIcon=Ikon
#XMSG: Table no result found
partnerConnectionConfigurationNotfound=Finner ingen konfigurasjon(er) av partnerforbindelse.
#XMSG: Partner Connection Confgurations are not available
partnerConnectionConfigurationNotAvailable=Kan ikke vise konfigurasjoner for partnerforbindelse når kjøringstidsdatabasen er utilgjengelig.
#XFLD: Dialog Create Partner Connection Configuration
partnerConnectionConfigurationDialogTitleAdd=Opprett konfigurasjon av partnerforbindelse
#XFLD: Dialog Create Partner Connection Configuration Upload Icon
partnerConnectionConfigurationButtonUloadPartnerIcon=Last opp ikon
#XFLD: Dialog Create Partner Connection Configuration Select
partnerConnectionConfigurationSelectIcon=Velg (maksimal størrelse på 200 kB)
#XFLD: Dialog Create Partner Connection Configuration Enter a name
partnerConnectionConfigurationNamePlaceholder=Eksempel på partnerrute
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL
partnerConnectionConfigurationiFrameURLPlaceholder=http://www.example.com
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL Check
partnerConnectionConfigurationiFramePostMessageOriginPlaceholder=.example.com
#XFLD: Dialog Create Partner Connection Configuration Browse
partnerConnectionConfigurationBrowseButton=Bla gjennom
#XFLD: Create Partner Connection Configuration Msg
partnerConnectionConfigurationUploadMsg=Konfigurasjon av partnerforbindelse er opprettet.
#XMSG: Error while deleting Partner Connection configuration(s)
partnerConnectionConfigurationDeleteError=Det oppstod en feil ved sletting av partnerforbindelseskonfigurasjon(er).
#XMSG: Partner Connection Configuration has been successfully deleted
partnerConnectionConfigurationDeleteSuccess=Konfigurasjon av partnerforbindelse er slettet.
#XMSG: Error while retrieving Partner Connection Configurations
partnerConnectionConfigurationLoadError=Det oppstod en feil ved henting av partnerforbindelseskonfigurasjon(er).
#XMSG: Error fileSizeLimitExceeded
partnerConnectionConfigurationfileSizeLimitExceeded=Kan ikke laste opp filen fordi den overskrider maksstørrelsen på 200 kB.
#XBUT: Create Partner Connection Configuration
partnerConnectionConfigurationButtonCreate=Opprett konfigurasjon av partnerforbindelse
#XBUT: Delete Partner Connection Configuration
partnerConnectionConfigurationButtonDelete=Slett konfigurasjon av partnerforbindelse
#XMSG: Partner Tile Creation Failure
upsertPartnerTileFailed=Kan ikke opprette partnerrute.
#XMSG: Partner Tile Deletion Failure
deletePartnerTileFailed=Kan ikke slette partnerrute.
#XMSG: HANA cloud connector settings reset failure
resetHANASCCSettingsFailed=Tilbakestilling av kundeinnstillingene for SAP HANA-nettskykonnektor mislyktes
#XFLD: Workload Class
workloadClass=Arbeidsbelastningsklasse
#XFLD: Workload Management
workloadManagement=Workload Management
#XFLD: Priority
workloadClassPriority=Prioritet
#XMSG:
workloadManagementPriorityHint=Du kan oppgi prioriteringen av dette rommet når du sender spørring til databasen. Oppgi en verdi fra 1 (laveste prioritet) til 8 (høyeste prioritet). I tilfeller der flere rom konkurrerer om tilgjengelige tråder, kjøres de med høy prioritet før de med lavere prioritet.
#XMSG:
workloadClassPriorityHint=Du kan oppgi en prioritet for rommet fra 0 (laveste) til 8 (høyeste). Setningene for et rom med høy prioritet utføres før setningene for andre rom med lavere prioritet. Standardprioritet er 5. Siden verdien 9 er reservert for systemoperasjoner, er den ikke tilgjengelig for et rom.
#XFLD: Statement Limits
workloadclassStatementLimits=Grenser for setninger
#XFLD: Workload Configuration
workloadConfiguration=Arbeidsbelastningskonfigurasjon
#XMSG:
workloadClassStatementLimitsHint=Du kan oppgi maksimalt antall (eller maksimal prosentsats) for tråder og GB-minne som setninger som kjører samtidig i rommet, kan forbruke. Du kan oppgi hvilken som helst verdi eller prosentsats mellom 0 (ingen grense) og totalt minne og totalt antall tråder som er tilgjengelige i tenanten. \n\n Hvis du oppgir en grense for tråd, må du være oppmerksom på at det kan redusere ytelsen. \n\n Hvis du oppgir en grense for minne, blir de setningene som når minnegrensen, ikke kjørt.
#XMSG:
workloadClassStatementLimitsDescription=Standardkonfigurasjonen har rikelige ressursgrenser, og forhindrer at ett enkelt rom overbelaster systemet.
#XMSG:
workloadClassStatementLimitCustomDescription=Du kan definere maksimumsgrenser for tråd og minne som kan forbrukes av setninger som kjøres samtidig i rommet.
#XMSG:
totalStatementThreadLimitHelpText=Hvis trådgrensen er satt for lavt, kan det påvirke setningsytelsen, mens for høye verdier eller 0 kan føre til at rommet forbruker alle tilgjengelige systemtråder.
#XMSG:
totalStatementMemoryLimitHelpText=Hvis minnegrensen er satt for lavt, kan du gå tom for minne, mens for høye verdier eller 0 kan føre til at rommet forbruker alt tilgjengelig systemminne.
#XMSG:
totalStatementThreadLimitHelpTextRestricted=Oppgi en prosentandel mellom 1 % og 70 % (eller tilsvarende tall) av det samlede antallet tråder tilgjengelig i tenanten din. Hvis du setter trådgrensen for lavt, kan det påvirke setningenes ytelse, mens for høye verdier kan påvirke setningenes ytelse i andre rom.
#XMSG:
totalStatementThreadLimitHelpTextDynamic=Oppgi en prosentandel mellom 1 % og {0} % (eller tilsvarende tall) av det samlede antallet tråder som er tilgjengelig i tenanten din. Hvis du setter trådgrensen for lavt, kan det påvirke setningenes ytelse, mens for høye verdier kan påvirke setningenes ytelse i andre rom.
#XMSG:
totalStatementMemoryLimitHelpTextNew=Oppgi en verdi eller prosentandel mellom 0 (ingen grense) og det samlede minnet som er tilgjengelig i tenanten din. Hvis du setter minnegrensen for lavt, kan det påvirke setningenes ytelse, mens for høye verdier kan påvirke setningenes ytelse i andre rom.
#XFLD: Total Statement Thread Limit
totalStatementThreadLimit=Total grense for setningstråd
#XFLD: Segmented button label
workloadclassPercentage=%
#XFLD: Segmented button label
workloadclassGigaByte=GB
#XFLD, 80: Segmented button label
workloadclassThreads=Tråder
#XFLD: Total Statement Memory Limit
totalStatementMemoryLimit=Total grense for setningsminne
#YMSE: Error while loading customer hana info
loadCustomerHanaInfoError=Kan ikke laste SAP HANA-info for kunde.
#XMSG:
minimumLimitReached=Minimumsgrense nådd.
#XMSG:
maximumLimitReached=Maksimumsgrense nådd.
#XMSG: Name Taken for Technical Name
technical-name-taken=Det finnes allerede en forbindelse med det tekniske navnet du oppgav. Oppgi et annet navn.
#XMSG: Name Too long for Technical Name
technical-name-too-long=Det tekniske navnet du oppgav, overskrider 40 tegn. Oppgi et navn med færre tegn.
#XMSG: Technical name field empty
technical-name-field-empty=Oppgi et teknisk navn.
#XMSG: Generic error when user enters invalid characters for a field
field-invalid-characters=Du kan bare bruke bokstaver (a–z), tall (0–9) og understreker (_) i navnet.
#XMSG: Generic error when user enters invalid initial characters for a field
field-underscore-trim=Navnet du oppgir, kan ikke begynne på eller slutte med en understrek (_).
#XFLD: Checkbox label for Enable Statement Limits
enableStatementLimits=Aktiver grenser for setninger
#XFLD: Connection SubSection Settings Title in space details
connectionSettings=Innstillinger
#XMSG: Connections tool hint in Space details section
connectionsToolHint=Hvis du vil opprette eller redigere forbindelser, åpner du appen Forbindelser fra sidenavigeringen, eller klikker her:
#XBUT: Connection list Open Connection Tool Button
button-connectiontool=Gå til Forbindelser
#XFLD: Not deployed label on space tile
notDeployedLabel=Rom er ikke distribuert ennå.
#XFLD: Not deployed additional text on space tile
notDeployedText=Distribuer rom.
#XFLD: Corrupt space label on space tile
corruptSpace=Noe gikk galt.
#XFLD: Corrupt space additional text on space tile
corruptSpaceHint=Forsøk å distribuere på nytt eller kontakt brukerstøtte
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Revisjonsprotokolldata
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Administrative data
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Andre data
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Data i rom
#XMSG: Unlock Space confirmation text
unlockSpaceConfirmation=Er du sikker på at du vil oppheve sperring av rommet?
#XMSG: Lock Space confirmation text
lockSpaceConfirmation=Er du sikker på at du vil sperre rommet?
#XFLD: Lock
lock=Sperr
#XFLD: Unlock
unlock=Opphev sperring
#XFLD: Locking
locking=Sperrer
#XMSG: Success message after locking space
lockSpaceSuccessMsg=Rom sperret
#XMSG: Success message after unlocking space
unlockSpaceSuccessMsg=Sperring er opphevet for rom
#XMSG: Success message after locking spaces
lockSpacesSuccessMsg=Rom sperret
#XMSG: Success message after unlocking spaces
unlockSpacesSuccessMsg=Sperring er opphevet for rom
#YMSE: Error while locking a space
lockSpaceError=Rommet kan ikke sperres.
#YMSE: Error while unlocking a space
unlockSpaceError=Sperring kan ikke oppheves for rommet.
#XTIT: popup title Warning
confirmationWarningTitle=Advarsel
#XMSG: Message strip message when a space was locked manually
manualLockedMessage=Rommet har blitt sperret manuelt.
#XMSG: Message strip message when a space was locked due to the amount of generated audit logs
auditLogLockedMessage=Rommet har blitt sperret av systemet fordi revisjonsprotokollene forbruker mange GB med diskplass.
#XMSG: Message strip message when a space was locked because the assigned quota was exceeded.
quotaLockedMessage=Rommet har blitt sperret av systemet fordi det overskrider tildelt minne- eller disklagringsplass.
#XMSG: Unlock Spaces confirmation text
unlockSpacesConfirmation=Er du sikker på at du vil oppheve sperring av de valgte rommene?
#XMSG: Lock Spaces confirmation text
lockSpacesConfirmation=Er du sikker på at du vil sperre de valgte rommene?
#XTIT: Scoped role editor dialog title
scopedRoleEditorDialogTitle=Redigeringsprogram for rolle i omfanget
#XTIT: ECN Management title
ecnManagementTitle=Rom og administrasjon av elastisk beregningsnode
#XFLD: ECNs
ecns=Elastisk beregningsnode
#XFLD: ECN phase Ready
ecnReady=Klar
#XFLD: ECN phase Running
ecnRunning=Kjører
#XFLD: ECN phase Initial
ecnInitial=Ikke klar
#XFLD: ECN phase Starting
ecnStarting=Starter
#XFLD: ECN phase Starting Failed
ecnStartingFailed=Starting mislyktes
#XFLD: ECN phase Stopping
ecnStopping=Stopper
#XFLD: ECN phase Stopping Failed
ecnStoppingFailed=Stopping mislyktes
#XBTN: Assign Button
assign=Tilordne rom
#XBTN: Start Header-Button
start=Start
#XBTN: Update Header-Button
repair=Oppdater
#XBTN: Stop Header-Button
stop=Stopp
#XFLD: ECN hours remaining
ecnHoursRemaining=1000 timer gjenstår
#XFLD: ECN block-hours remaining
ecnBlockHoursRemaining={0} blokktimer som gjenstår
#XFLD: ECN block-hour remaining
ecnBlockHourRemaining={0} blokktimer som gjenstår
#XTIT: ECN creation dialog title
ecnCreationDialogTitle=Opprett elastisk beregningsnode
#XTIT: ECN edit dialog title
ecnEditDialogTitle=Rediger elastisk beregningsnode
#XTIT: ECN deletion dialog title
ecnDeletionDialogTitle=Slett elastisk beregningsnode
#XTIT: ECN Space assignment dialog title
ecnSpaceAssignmentDialogTitle=Tilordne rom
#XFLD: ECN ID
ECNIDLabel=Elastisk beregningsknutepunkt
#XTXT: Selected toolbar text
selectedToolbarText=Valgt: {0}
#XTIT: Elastic Compute Nodes
ECNslong=Elastisk beregningsnode
#XTIT: Object assignment - Table header: Objects available
objectsAvailable=Antall objekter
#XTIT: Object assignment - Dialog header text
selectObjects=Velg områdene og objektene som du vil tilordne til din elastiske beregningsnode:
#XTIT: Object assignment - Table header title: Objects
objects=Objekter
#XTIT: Object assignment - Table header: Type
type=Type
#XMSG: Message strip delete database user audit warning
deleteDatabaseUserWarningMessageStrip=Vær oppmerksom på at sletting av en databasebruker vil resultere i slettingen av alle genererte revisjonsprotokollposter. Hvis du vil beholde revisjonsprotokollene, vurder å eksportere dem før du sletter databasebrukeren.
#XMSG: Message strip delete HDI container audit logs impact
deleteHDIContainerWarningMessageStrip=Vær oppmerksom på at opphevingen av tilordningen av en HDI-container fra rommet vil resultere i slettingen av alle genererte revisjonsprotokollposter. Hvis du vil beholde revisjonsprotokollene, vurder å eksportere dem før du opphever tilordningen av HDI-containeren.
#XTXT: All audit logs
allAuditLogs=Alle revisjonsprotokollposter som er generert for rommet
#XMSG: Message strip disable audit warning
disableAuditWarningMessageStrip=Vær oppmerksom på at deaktivering av en revisjonsretningslinje (lese- eller endringsoperasjoner) vil resultere i slettingen av alle dens revisjonsprotokollposter. Hvis du vil beholde revisjonsprotokollpostene, vurder å eksportere dem før du deaktiverer revisjonsretningslinjen.
#XTIT: No Spaces assigned to ECN Illustration title
noSpacesECNTitle=Det er ikke tilordnet noen rom eller objekter ennå
#XTIT: No Spaces assigned to ECN Illustration description
noSpacesECNDescription=For å begynne å jobbe med den elastiske beregningsnoden må du tilordne et rom eller objekter til den.
#XTIT: No Spaces Illustration title
noSpacesTitle=Rom er ikke tilordnet ennå
#XTIT: No Spaces Illustration description
noSpacesDescription=Opprett et rom for å begynne å innhente data.
#XTIT: No Spaces Illustration title
noDeletedSpacesTitle=Papirkurven er tom
#XTIT: No Spaces Illustration description
noDeletedSpacesDescription=Du kan gjenopprette de slettede rommene fra her.
#XTXT:  {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDatabaseUsers=Når rommet er distribuert, blir følgende databasebrukere {0} slettet og kan ikke gjenopprettes:
#XTIT: Delete database users
deleteDatabaseUsersTitle=Slett databasebrukere
#YMSE: Error if ecn id already exists
ecnIDInputDuplicateError=ID eksisterer allerede.
#YMSE: Error if ecn id contains invalid characters
ecnIDInputInvalidError=Bruk bare små bokstaver a–z og tallene 0–9
#YMSE: Error if ecn id is too short
ecnIDInputTooShortError=ID må inneholde minst {0} tegn.
#XMSG: ecn id length warning
ecnIdLengthWarning=Maksgrensen på {0} tegn er overskredet.
#XFLD: open System Monitor
systemMonitor=Systemmonitor
#XFLD: open ECN schedule dialog menu entry
schedule=Tidsplan
#XFLD: open create ECN schedule dialog
createSchedule=Opprett tidsplan
#XFLD: open change ECN schedule dialog
changeSchedule=Rediger tidsplan
#XFLD: open delete ECN schedule dialog
deleteSchedule=Slett tidsplan
#XFLD: open self assign ECN schedule dialog
selfAssignSchedule=Tilordne tidsplan til meg
#XFLD: open pause ECN schedule dialog
pauseSchedule=Sett tidsplan på pause
#XFLD: open resume ECN schedule dialog
resumeSchedule=Fortsett tidsplan
#XFLD: View Logs
viewLogs=Vis protokoller
#XFLD: Compute Blocks
computeBlocks=Beregningsblokker
#XFLD: Memory label in ECN creation dialog
ecnMemory=Minne (GB)
#XFLD: Storage label in ECN creation dialog
ecnStorage=Lagringsplass (GB)
#XFLD: Number of CPU label in ECN creation dialog
ecnCPUCount=Antall CPU-er
#XFLD: ECN updated by label
changedBy=Endret av
#XFLD: ECN updated on label
changedOn=Endret
#XMSG: Success message after creating a Elastic Compute Node
createEcnSuccessMessage=Elastisk beregningsnode er opprettet
#YMSE: Error while creating a Elastic Compute Node
createEcnError=Kan ikke opprette elastisk beregningsnode
#XMSG: Success message after updating a Elastic Compute Node
updateEcnSuccessMessage=Elastisk beregningsnode er oppdatert
#XMSG: Error while updating a Elastic Compute Node
udpateEcnError=Kan ikke oppdatere elastisk beregningsnode
#XMSG: Success message after deleting a Elastic Compute Node
deleteEcnSuccessMessage=Elastisk beregningsnode er slettet
#YMSE: Error while deleting a Elastic Compute Node
deleteEcnError=Kan ikke slette elastisk beregningsnode
#XMSG: Success message after starting a Elastic Compute Node
startingEcnSuccessMessage=Starter elastisk beregningsnode
#XMSG: Success message after stopping a Elastic Compute Node
stoppingEcnSuccessMessage=Stopper elastisk beregningsnode
#YMSE: Error while starting a Elastic Compute Node
startingEcnError=Kan ikke starte elastisk beregningsnode
#YMSE: Error while stopping a Elastic Compute Node
stoppingEcnError=Kan ikke stoppe elastisk beregningsnode
#XBUT: Add Object button for an ECN
assignObjects=Legg til objekter
#XFLD: Enable auto object assignment label
enableAutoObjAssignment=Legg til alle objekter automatisk
#XFLD: object type label to be assigned
objectTypeLabel=Type (semantisk bruk)
#XFLD: assigned object type label
assignedObjectTypeLabel=Type
#XFLD: technical name label
TechnicalNameLabel=Teknisk navn
#XFLD: Object selecttion ECN label
objectSelectionECNLabel=Velg objektene du vil legge til i den elastiske beregningsnoden
#XTIT: Add objects dialog title
assignObjectsTitle=Tilordne objekter for
#XFLD: object label with object count
objectLabel=Objekt
#XMSG: No objects available to add message.
noObjectsToAssign=Ingen objekter tilgjengelige for å tilordne.
#XMSG: No objects assigned message.
noAssignedObjects=Ingen objekter er tilordnet.
#XTIT: Remove objects popup title
removeObjectsOrSpacesConfirmationTitle=Advarsel
#XTIT: Delete ECN popup button and title
deleteECNConfirmationButtonAndTitle=Slett
#XMSG: Remove objects popup text
removeObjectsConfirmation=Er du sikker på at du vil fjerne de valgte objektene?
#XMSG: Remove spaces popup text
removeSpacesConfirmation=Er du sikker på at du vil fjerne de valgte rommene?
#XBUT: Confirmation button for objects dialog
removeObjectsOrSpacesConfirmationBtn=Fjern rom
#XMSG: Success message after removing objects
removeObjectsSuccessMessage=Eksponerte objekter er fjernet.
#XMSG: Success message after assigning objects
assignObjectsSuccessMessage=Eksponerte objekter er tilordnet.
#XFLD: auto object assignment label for space tile
autoObjAssignmentLabel=Alle eksponerte objekter
#XFLD: Spaces tab label
spacesTabLabel=Rom
#XFLD: Exposed objects tab label
consumptionObjectsTabLabel=Eksponerte objekter
#XMSG: Success message after unassigning spaces
spacesUnassignedSuccessMessage=Rom er fjernet.
#XMSG: Success message after unassigning space
spaceUnassignedSuccessMessage=Rom er fjernet.
#YMSE: Error while assigning / unassigning spaces
spacesAssignUnassignError=Kan ikke tilordne eller fjerne rom.
#YMSE: Error while removing objects
removeObjectsError=Vi kan ikke tilordne eller fjerne objektene.
#YMSE: Error while removing object
removeObjectError=Vi kan ikke tilordne eller fjerne objektet.
#YMSE: Error when saving the ecn with invalid value
invalidSizingPlanError=Nummeret som er valgt tidligere er ikke lenger gyldig. Velg et gyldig nummer.
#YMSE: Error when saving the ecn with invalid value
invalidPerformanceClassError=Velg en gyldig ytelsesklasse.
#YMSE: Error when saving the ecn with invalid value
performanceClassNotAvailableError=Tidligere valgt ytelsesklasse "{0}" er ikke gyldig for øyeblikket. Velg gyldig ytelsesklasse.
#XTXT: Delete ECN confirmation text
deleteConfirmationTxtECN=Er du sikker på at du vil slette den elastiske beregningsnoden?
#XFLD: tooltip for ? button
help=Hjelp
#XFLD: ECN edit button label
editECN=Konfigurer
#XFLD: Technical type label for ERModel
DWC_ERMODEL=Entitet – relasjonsmodell
#XFLD: Technical type label for Local Table
DWC_LOCAL_TABLE=Lokal tabell
#XFLD: Technical type label for Remote Table
DWC_REMOTE_TABLE=Fjerntabell
#XFLD: Technical type label for Analytic Model
DWC_ANALYTIC_MODEL=Analysemodell
#XFLD: Technical type label for Task Chain
DWC_TASKCHAIN=Oppgavekjede
#XFLD: Technical type label for Data Flow
DWC_DATAFLOW=Dataflyt
#XFLD: Technical type label for Replication Flow
DWC_REPLICATIONFLOW=Replikeringsflyt
#XFLD: Technical type label for Transformation Flow
DWC_TRANSFORMATIONFLOW=Transformasjonsflyt
#XFLD: Technical type label for Intelligent Lookup
DWC_IDT=Intelligent søk
#XFLD: Technical type label for Repository
DWC_REPOSITORY_PACKAGE=Repository
#XFLD: Technical type label for Enterprise Search
DWC_ESMODEL=Foretakssøk
#XFLD: Technical type label for View
DWC_VIEW=Visning
#XFLD: Technical type label for Data Product
DWC_DATAPRODUCT=Dataprodukt
#XFLD: Technical type label for Data Access Control
DWC_DAC=Datatilgangskontroll
#XFLD: Technical type label for Folder
DWC_FOLDER=Mappe
#XFLD: Technical type label for Data Entity
DWC_BUSINESS_ENTITY=Forretningsentitet
#XFLD: Technical type label for Business Entity Variant
DWC_BUSINESS_ENTITY_VARIANT=Variant av forretningsentitet
#XFLD: Technical type label for Responsibility Scenario
DWC_AUTH_SCENARIO=Ansvarsområdescenario
#XFLD: Technical type label for Fact Model
DWC_FACT_MODEL=Faktamodell
#XFLD: Technical type label for Transformation Flow
DWC_PERSPECTIVE=Perspektiv
#XFLD: Technical type label for Consumption Model
DWC_CONSUMPTION_MODEL=Forbruksmodell
#XFLD: Technical type label for Remote Connection
DWC_REMOTECONNECTION=Fjernforbindelse
#XFLD: Technical type label for Fact Model Variant
DWC_FACT_MODEL_VARIANT=Variant av faktamodell
#XMSG: Schedule created alert message
createScheduleSuccess=Tidsplan opprettet
#XMSG: Schedule updated alert message
updateScheduleSuccess=Tidsplan oppdatert
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Tidsplan slettet
#XMSG: Schedule takeover success message
ownerChangeScheduleSuccess=Tidsplan tilordnet til deg
#XMSG: Message for starting pausing of schedule
pauseScheduleStarted=Setter 1 tidsplan på pause
#XMSG: Message for starting resuming of schedule
resumeScheduleStarted=Fortsetter 1 tidsplan
#XFLD: Segmented button label
availableSpacesButton=Tilgjengelig
#XFLD: Segmented button label
selectedSpacesButton=Valgt
#XFLD: Visit website button text
visitWebsite=Besøk nettsted
#XMSG: Warning message on unselecting source language
sourceLanguageRemovalWarning=Det tidligere valgte kildespråket vil bli fjernet.
#XBUT: enable translation confirmation button
enableTranslationConfirmationBtn=Aktiver
#XFLD: ECN performance class label
performanceClassLabel=Ytelsesklasse
#XTXT performance class memory text
memoryText=Minne
#XTXT performance class compute text
computeText=Beregning
#XTXT performance class high-compute text
highComputeText=High-Compute
#XBUT: Recycle Bin Button Text
recycleBin=Papirkurv
#XBUT: Restore Button Text
restore=Gjenopprett
#XMSG: Warning message for new Workload Management UI
priorityWarning=Dette området er skrivebeskyttet. Du kan endre romprioriteten i området System / Konfigurasjon / Behandling av arbeidsbelastning.
#XMSG: Warning message for new Workload Management UI
workloadWarning=Dette området er skrivebeskyttet. Du kan endre konfigurasjonen av romarbeidsbelastningen i området System / Konfigurasjon / Behandling av arbeidsbelastning.
#XCOL: Space table-view column: Apache Spark vCPUs
sparkManagementVCPUs=Apache Spark-vCPU-er
#XCOL: Space table-view column: Apache Spark Memory (GB)
sparkManagementMemory=Apache Spark-minne (GB)
#XCOL: Space type (from Space Capability) for Data Ingestion Spaces
ingestionSpaceType=Dataproduktoverføring
#XFLD: Deployment in progress nodatatext for spark table
sparkTableDeploymentInProgress=Ingen data tilgjengelig ettersom rommet distribueres for øyeblikket
#XFLD: Runtime data being loaded nodatatext for spark table
sparkTableDeploymentLoading=Ingen data er tilgjengelig ettersom rommet lastes for øyeblikket
#XCOL: Space type (from Space Capability) for BW Private Cloud Edition
BWPCESpaceType=SAP BW
#XBUT: Go to Instance Mappings Button
navToMappingsButton=Rediger instanstilordninger
#XFLD: SAP HANA Data Lake Storage label if less than 1GB
dataLakeStorageUsed=< 1 GB
