#DO NOT REMOVE________________________
#XFLD: Breadcrumb for space monitoring page
spaceMonitorBreadcrumb=Παρακολούθηση
#XTXT: Type name for spaces in browser tab page title
space=Χώρος
#_____________________________________
#XFLD: Spaces label in
spaces=Χώροι
#XFLD: Manage plan button text
manageQuotaButtonText=Διαχείριση Προγράμματος
#XBUT: Manage resources button
manageResourcesButton=Διαχείριση Πόρων
#XFLD: Create space button tooltip
createSpace=Δημιουργία Χώρου
#XFLD: Create
create=Δημιουργία
#XFLD: Deploy
deploy=Ανάπτυξη
#XFLD: Page
page=Σελίδα
#XFLD: Cancel
cancel=Ακύρωση
#XFLD: Update
update=Ενημέρωση
#XFLD: Save
save=Αποθήκευση
#XFLD: OK
ok=ΟΚ
#XFLD: days
days=Ημέρες
#XFLD: Space tile edit button label
edit=Επεξεργασία
#XFLD: Auto Assign all objects to space
autoAssign=Αυτόματη Αντιστοίχιση
#XFLD: Space tile open monitoring button label
openMonitoring=Παρακολούθηση
#XFLD: Delete
delete=Διαγραφή
#XFLD: Copy Space
copy=Αντιγραφή
#XFLD: Close
close=Κλείσιμο
#XCOL: Space table-view column status
status=Κατάσταση
#XFLD: Space status active
activeLabel=Ενεργό
#XFLD: Space status locked
lockedLabel=Κλειδωμένο
#XFLD: Space status critical
criticalLabel=Κρίσιμος
#XFLD: Space status cold
coldLabel=Ψυχρό
#XFLD: Space status deleted
deletedLabel=Διαγραμμένο
#XFLD: Space status unknown
unknownLabel=Αγνωστο
#XFLD: Space status ok
okLabel=Υγιές
#XFLD: Database user expired
expired=Λήξη
#XFLD: deployed
deployed=Αναπτύχθηκε
#XFLD: not deployed
notDeployed=Μη Αναπτυγμένο
#XFLD: changes to deploy
changesToDeploy=Αλλαγές για Ανάπτυξη
#XFLD: pending
pending=Ανάπτυξη
#XFLD: designtime error
designtimeError=Σφάλμα χρόνου σχεδίασης
#XFLD: runtime error
runtimeError=Σφάλμα χρόνου εκτέλεσης
#XFLD: Space created by label
createdBy=Δημιουργημένο Από
#XFLD: Space created on label
createdOn=Δημιουργημ.στις
#XFLD: Space deployed on label
deployedOn=Αναπτύχθηκε στις
#XFLD: Space ID label
spaceID=ID Χώρου
#XFLD: Priority label
priority=Προτεραιότητα
#XFLD: Space Priority label
spacePriority=Προτεραιότητα Χώρου
#XFLD: Space Configuration label
spaceConfiguration=Διαμόρφωση Χώρων
#XFLD: Not available
notAvailable=Μη Διαθέσιμο
#XFLD: WorkloadType default
default=Προεπιλογή
#XFLD: WorkloadType custom
custom=Παραμ.
#XFLD: Data Lake option label; refers to component SAP HANA Cloud, Data Lake
dataLakeAccess=Πρόσβαση σε Λίμνη Δεδομένων
#XFLD: Translation label
translationLabel=Μετάφραση
#XFLD: Source language label
sourceLanguageLabel=Γλώσσα Πηγή
#XFLD: Translation CheckBox label
translationCheckBox=Γλώσσα Στόχος
#XTXT: Popover hint that space has to be deployed to show user details
openDatabaseUserHintPopoverText=Ανάπτυξη χώρου για πρόσβαση σε στοιχεία χρήστη.
#XTXT: Popover hint that space has to be deployed to open database explorer
openDatabaseExplorerHintPopoverText=Ανάπτυξη χώρου για άνοιγμα του Explorer Βάσης Δεδομένων.
#XTXT: Text for when the Data Lake check box is disabled
dataLakeOptionDisabled=Δεν μπορείτε να χρησιμοποιήσετε αυτόν τον χώρο για να ανοίξετε την Λίμνη Δεδομένων γιατί χρησιμοποιείται ήδη από άλλο χώρο.
#XFLD: Data Lake check box text; refers to component SAP HANA Cloud, Data Lake
dataLakeCheckBox=Χρησιμοποιήστε αυτόν τον χώρο για να ανοίξετε την λίμνη δεδομένων.
#XFLD: Space Priority minimum label extension
low=Χαμηλή
#XFLD: Space Priority maximum label extension
high=Υψηλή
#XFLD: Space name label
spaceName=Όνομα Χώρου
#XFLD: Enable deploy objects checkbox
enableDeployObjects=Ανάπτυξη Αντικειμένων
#XTIT: Copy spaces dialog title
copySpaceDialogTitle=Αντιγραφή {0}
#XTXT Human readable text for unselected source language
unSelectedSourceLanguage=(Μη Επιλεγμένο)
#XTXT Human readable text for language code "af"
af=Αφρικανικά
#XTXT Human readable text for language code "ar"
ar=Αραβικά
#XTXT Human readable text for language code "bg"
bg=Βουλγαρικά
#XTXT Human readable text for language code "ca"
ca=Καταλανικά
#XTXT Human readable text for language code "zh"
zh=Απλοποιημένα Κινέζικα
#XTXT Human readable text for language code "zf"
zf=Κινέζικα
#XTXT Human readable text for language code "hr"
hr=Κροατικά
#XTXT Human readable text for language code "cs"
cs=Τσεχικά
#XTXT Human readable text for language code "cy"
cy=Ουαλικά
#XTXT Human readable text for language code "da"
da=Δανικά
#XTXT Human readable text for language code "nl"
nl=Ολλανδικά
#XTXT Human readable text for language code "en-UK"
en-UK=Αγγλικά (Ηνωμένο Βασίλειο)
#XTXT Human readable text for language code "en"
en=Αγγλικά (Ηνωμένες Πολιτείες)
#XTXT Human readable text for language code "et"
et=Εσθονικά
#XTXT Human readable text for language code "fa"
fa=Περσικά
#XTXT Human readable text for language code "fi"
fi=Φινλανδικά
#XTXT Human readable text for language code "fr-CA"
fr-CA=Γαλλικά (Καναδάς)
#XTXT Human readable text for language code "fr"
fr=Γαλλικά
#XTXT Human readable text for language code "de"
de=Γερμανικά
#XTXT Human readable text for language code "el"
el=Ελληνικά
#XTXT Human readable text for language code "he"
he=Εβραϊκά
#XTXT Human readable text for language code "hi"
hi=Χίντι
#XTXT Human readable text for language code "hu"
hu=Ουγγρικά
#XTXT Human readable text for language code "is"
is=Ισλανδικά
#XTXT Human readable text for language code "id"
id=Bahasa Indonesia
#XTXT Human readable text for language code "it"
it=Ιταλικά
#XTXT Human readable text for language code "ja"
ja=Ιαπωνικά
#XTXT Human readable text for language code "kk"
kk=Καζακστανικά
#XTXT Human readable text for language code "ko"
ko=Κορεάτικα
#XTXT Human readable text for language code "lv"
lv=Λετονικά
#XTXT Human readable text for language code "lt"
lt=Λιθουανικά
#XTXT Human readable text for language code "ms"
ms=Bahasa Melayu
#XTXT Human readable text for language code "no"
no=Νορβηγικά
#XTXT Human readable text for language code "pl"
pl=Πολωνικά
#XTXT Human readable text for language code "pt"
pt=Πορτογαλικά (Βραζιλία)
#XTXT Human readable text for language code "pt-PT"
pt-PT=Πορτογαλικά (Πορτογαλία)
#XTXT Human readable text for language code "ro"
ro=Ρουμανικά
#XTXT Human readable text for language code "ru"
ru=Ρωσικά
#XTXT Human readable text for language code "sr"
sr=Σερβικά
#XTXT Human readable text for language code "sh"
sh=Σερβοκροάτικα
#XTXT Human readable text for language code "sk"
sk=Σλοβακικά
#XTXT Human readable text for language code "sl"
sl=Σλοβενικά
#XTXT Human readable text for language code "es"
es=Ισπανικά
#XTXT Human readable text for language code "es-MX"
es-MX=Ισπανικά (Μεξικό)
#XTXT Human readable text for language code "sv"
sv=Σουηδικά
#XTXT Human readable text for language code "th"
th=Ταϋλανδέζικα
#XTXT Human readable text for language code "tr"
tr=Τουρκικά
#XTXT Human readable text for language code "uk"
uk=Ουκρανικά
#XTXT Human readable text for language code "vi"
vi=Βιετναμικά
#XTIT: Delete spaces confirmation title
deleteConfirmationHeaderText=Διαγραφή Χώρων
#XTXT: Logically delete space delete confirmation text with space name
logicallyDeleteConfirmationBodyTextWithSpaceName=Θέλετε να μετακινήσετε τον χώρο "{0}" στον κάδο ανακύκλωσης;
#XTXT: Logically dpace delete confirmation text for multiple spaces
logicallyDeleteConfirmationBodyTextMultiple=Θέλετε να μετακινήσετε τους {0} επιλεγμένους χώρους στον κάδο ανακύκλωσης;
#XTXT: Delete space delete confirmation text with space name
deleteConfirmationBodyTextWithSpaceName=Θέλετε να διαγράψετε τον χώρο "{0}"; Αυτή η ενέργεια είναι αμετάκλητη.
#XTXT: Delete space delete confirmation text for multiple spaces
deleteConfirmationBodyTextMultiple=Θέλετε να διαγράψετε τους {0} επιλεγμένους χώρους; Αυτή η ενέργεια είναι αμετάκλητη. Το παρακάτω περιεχόμενο θα {1} διαγραφεί:
#XTXT: permanently
permanently=οριστικά
#XTXT: {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDetail=Το παρακάτω περιεχόμενο θα διαγραφεί {0} και δεν θα μπορεί να ανακτηθεί:
#XTXT: Hint Delete space confirmation text
deleteConfirmationHint=Πληκτρολογήστε {0} για να επιβεβαιώσετε τη διαγραφή.
#XTXT: Delete space confirmation error check spelling
deleteConfirmationErrorCheckSpelling=Ελέγξτε την ορθογραφία και δοκιμάστε ξανά.
#XTXT: All Spaces
allSpaces=Ολοι οι Χώροι
#XTXT: All data
allData=Ολα τα αντικείμενα και τα δεδομένα που περιέχονται στον χώρο
#XTXT: All connections
allConnections=Ολες οι συνδέσεις
#XFLD: Space tile selection box tooltip
clickToSelect=Κάντε Κλικ για Επιλογή
#XTXT: All database users
allDatabaseUsers=Ολα τα αντικείμενα και τα δεδομένα που περιέχονται σε ένα Ανοικτό σχήμα SQL που σχετίζεται με τον χώρο
#XFLD: remove members button tooltip
deleteUsers=Διαγραφή Μελών
#XTXT: Space long description text
description=Περιγραφή (Μέγιστος Αριθμός 4000 Χαρακτήρες)
#XFLD: Add Members button tooltip
addUsers=Προσθήκη Μελών
#XFLD: Add Users button tooltip
addUsersTooltip=Προσθήκη Χρηστών
#XFLD: Edit Users button tooltip
editUsersTooltip=Επεξεργασία Χρηστών
#XFLD: Remove Users button tooltip
removeUsersTooltip=Διαγραφή Χρηστών
#XFLD: Searchfield placeholder
filter=Αναζήτηση
#XCOL: Users table-view column health
health=Υγεία
#XCOL: Users table-view column access
access=Πρόσβαση
#XFLD: No user found nodatatext
noDataText=Δεν βρέθηκε χρήστης
#XTIT: Members dialog title
selectUserDialogTitle=Προσθήκη Μελών
#XTIT: User dialog title
addUserDialogTitle=Προσθήκη Χρηστών
#XTIT: Delete connections confirmation title
deleteConnectionConfirmationHeaderText=Διαγραφή Συνδέσεων
#XTIT: Delete single connection confirmation title
deleteConnectionConfirmationHeaderTextSingle=Διαγραφή Σύνδεσης
#XTXT: Delete connection delete confirmation text
deleteConnectionConfirmationBodyText=Σίγουρα θέλετε να διαγράψετε τις επιλεγμένες συνδέσεις; Θα διαγραφούν οριστικά.
#XTIT: Remote source dialog title
selectRemoteConnectionDialogTitle=Επιλογή Συνδέσεων
#XTIT: Share connection dialog title
connectionSharingDialogTitle=Κοινοποίηση Σύνδεσης
#XTIT: Connections shared from other spaces table title
sharedConnectionsFromOtherSpaces=Κοινοποιημένες Συνδέσεις
#XFLD: Add remote source button tooltip
addRemoteConnections=Προσθήκη Συνδέσεων
#XFLD: Delete remote source button tooltip
deleteRemoteConnections=Διαγραφή Συνδέσεων
#XFLD: Share remote source button tooltip
shareConnections=Κοινοποίηση Συνδέσεων
#XFLD: Tile-layout tooltip
tileLayout=Διάταξη Πλακιδίου
#XFLD: Table-layout tooltip
tableLayout=Διάταξη Πίνακα
#XMSG: Success message after creating space
createSpaceSuccessMessage=Ο χώρος δημιουργήθηκε
#XMSG: Success message after copying space
copySpaceSuccessMessage=Αντιγραφή χώρου "{0}" σε χώρο "{1}"
#XMSG: Success message after deploying space
deploymentSuccessMessage=Η ανάπτυξη χώρου άρχισε
#XMSG: Success message after Apache Spark update started
sparkUpdateStartedSuccessMessage=Ενημέρωση Apache Spark άρχισε
#YMSE: Error while starting Apache Spark update
sparkUpdateStartedErrorMessage=Αδύνατη ενημέρωση Apache Spark
#XMSG: Success message after updating space
updateSettingsSuccessMessage=Οι λεπτομέρειες χώρου ενημερώθηκαν.
#XMSG: Success message after unlocking space
unlockSpaceSuccessMessage=Ο χώρος ξεκλειδώθηκε προσωρινά
#XMSG: Success message after deleting a single space
deleteSpaceSuccessMessage=Ο χώρος διαγράφηκε
#XMSG: Success message after deleting multiple spaces
deleteSpacesSuccessMessage=Οι χώροι διαγράφηκαν
#XMSG: Success message after restoring a single space
restoreSpaceSuccessMessage=Ο χώρος επανήλθε
#XMSG: Success message after restoring multiple spaces
restoreSpacesSuccessMessage=Οι χωροι επανήλθαν
#YMSE: Error while updating settings
updateSettingsFailureMessage=Οι ρυθμίσεις χώρου δεν ενημερώθηκαν.
#YMSE: Error while updating data lake option
dataLakeEnabledInOtherSpace=Η λίμνη δεδομένων είναι αντιστοιχισμένη σε άλλο χώρο. Μόνο ένας χώρος μπορεί να ανοίγει την λίμνη δεδομένων κάθε φορά.
#YMSE: Error while updating data lake option
virtualTablesExists=Δεν μπορείτε να ακυρώσετε την αντιστοίχιση λίμνης δεδομένων από αυτό το χώρο γιατί υπάρχουν ακόμα εξαρτήσεις σε εικονικούς πίνακες*. Διαγράψτε τους εικονικούς πίνακες για να ακυρωθεί η αντιστοίχιση.
#YMSE: Error while unlocking space
unlockSpaceFailureMessage=Αδύνατο ξεκλείδωμα χώρου.
#YMSE: Error while creating space
createSpaceError=Αδύνατη δημιουργία χώρου.
#YMSE: Error while creating duplicate space
createDuplicateSpaceError=Ένας χώρος με το όνομα {0} υπάρχει ήδη.
#YMSE: Error while deleting a single space
deleteSpaceError=Ο χώρος δεν διαγράφηκε.
#YMSE: Error details while deleting a single space
deleteSpaceDetailedError=Ο χώρος σας “{0}” δεν λειτουργεί πλέον. Δοκιμάστε να τον διαγράψετε ξανά. Εάν εξακολουθεί να μην λειτουργεί, ζητήστε από τον διαχειριστή σας να τον διαγράψει ή δημιουργήστε ένα μήνυμα.
#YMSE: Error while deleting a single space in files
deleteSpaceInFilesError=Τα δεδομένα χώρου στα Αρχεία δεν διαγράφηκαν.
#YMSE: Error while deleting a single space users
deleteSpaceUsersError=Οι χρήστες δεν διαγράφηκαν.
#YMSE: Error while deleting a single space schema
deleteSpaceSchemasError=Τα σχήματα δεν διαγράφηκαν.
#YMSE: Error while deleting a single space connections
deleteSpaceConnectionsError=Οι συνδέσεις δεν διαγράφηκαν.
#YMSE: Error while deleting a single space data
deleteSpaceDataError=Τα δεδομένα χώρου δεν διαγράφηκαν.
#YMSE: Error while deleting multiple spaces
deleteSpacesError=Οι χώροι δεν διαγράφηκαν.
#YMSE: Error while restoring a single space
restoreSpaceError=Ο χώρος δεν μπόρεσε να επανέλθει.
#YMSE: Error while restoring multiple spaces
restoreSpacesError=Οι χώροι δεν μπόρεσαν να επανέλθουν.
#YMSE: Error while creating users
createUsersError=Οι χρήστες δεν προστέθηκαν.
#YMSE: Error while removing users
removeUsersError=Δεν μπορέσαμε να διαγράψουμε τους χρήστες.
#YMSE: Error while removing user
removeUserError=Δεν μπορέσαμε να διαγράψουμε τον χρήστη.
#YMSE: Error assigning roles to a user
roleAssignUnAssignError=Δεν μπορέσαμε να προσθέσουμε τον χρήστη στον επιλεγμένο προσαρμοσμένο ρόλο. \n\n Δεν μπορείτε να προσθέσετε τον εαυτό σας σε έναν προσαρμοσμένο ρόλο. Ζητήστε από τον διαχειριστή σας να σας προσθέσει σε έναν προσαρμοσμένο ρόλο.
#YMSE: Error assigning user to the space
userAssignError=Δεν αναθέσαμε το χρήστη στο διάστημα. \n\n Ο χρήστης έχει ήδη ανατεθεί στον μέγιστο επιτρεπτό αριθμό (100) διαστημάτων σε ρόλους πεδίων εφαρμογής.
#YMSE: Error assigning users to the space
usersAssignError=Δεν αναθέσαμε τους χρήστες στο διάστημα. \n\n Ο χρήστης έχει ήδη ανατεθεί στον μέγιστο επιτρεπτό αριθμό (100) διαστημάτων σε ρόλους πεδίων εφαρμογής.
#YMSE: Error while fetching users
fetchUserWithScopedRolesError=Δεν μπορέσαμε να ανακτήσουμε τους χρήστες. Δοκιμάστε πάλι αργότερα.
#YMSE: Error while fetching scoped roles
fetchScopedRolesError=Δεν μπορέσαμε να ανακτήσουμε τους ρόλους.
#YMSE: Error while fetching members
fetchUserError=Τα μέλη δεν επιλέχθηκαν. Δοκιμάστε ξανά αργότερα.
#YMSE: Error while loading run-time database
loadRuntimeError=Δεν μπορέσαμε να φορτώσουμε πληροφορίες από τη βάση δεδομένων χρόνου εκτέλεσης.
#YMSE: Error while loading spaces
loadSpacesError=Λυπούμαστε, αλλά κάποιο λάθος συνέβη όταν προσπαθήσατε να ανακτήσετε τους χώρους σας.
#YMSE: Error while loading haas resources
loadStorageError=Λυπούμαστε, αλλά κάποιο λάθος συνέβη όταν προσπαθήσατε να ανακτήσετε τα δεδομένα αποθήκευσης.
#YMSE: Error no data could be loaded
loadDataError=Λυπούμαστε, αλλά κάποιο λάθος συνέβη όταν προσπαθήσατε να ανακτήσετε τα δεδομένα σας.
#XFLD: Click to refresh storage data
clickToRefresh=Πατήστε εδώ για να προσπαθήσετε πάλι.
#XTIT: Delete space popup title
deleteSpacePopupTitle=Διαγραφή Χώρου
#XCOL: Spaces table-view column name
name=Ονομα
#XCOL: Spaces table-view deployment status
deploymentStatus=Κατάσταση Ανάπτυξης
#XFLD: Disk label in space details
storageLabel=Δίσκος (GB)
#XFLD: In-Memory label in space details
ramLabel=Μνήμη (GB)
#XFLD: Memory label on space card
memory=Μνήμη για Αποθήκευση
#XFLD: Storage Assignment label in space details
storrageAssignmentLabel=Αποθήκευση Χώρων
#XFLD: Storage Type label in space details
storageTypeLabel=Τύπος Αποθήκευσης
#XFLD: Enable Space Quota
enableSpaceQuota=Ενεργοποίηση Ποσοστού Χώρου
#XFLD: No Space Quota
noSpaceQuota=Δεν υπάρχει Ποσοστό Χώρου
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
diskAndInMemoryText=Βάση Δεδομένων SAP HANA (Disk and In-Memory)
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
dataLakeFilesText=Αρχεία SAP HANA Data Lake 
#XFLD: Available scoped roles label
availableRoles=Διαθέσιμοι Ρόλοι
#XFLD: Selected scoped roles label
selectedRoles=Επιλεγμένοι Ρόλοι
#XCOL: Spaces table-view column models
models=Μοντέλα
#XCOL: Spaces table-view column users
users=Χρήστες
#XCOL: Spaces table-view column connections
connections=Συνδέσεις
#XFLD: Section header overview in space detail
overview=Επισκόπηση
#XFLD: Section header Apache Spark in space detail
sparkManagementSection=Apache Spark
#XFLD: Section header Apache Spark in space detail
sparkManagementSubSection=Εφαρμογές
#XFLD: Section header Apache Spark in space detail
taskAssignmentSubSection=Αντιστοίχιση Εργασίας
#XFLD: vCPU label in Apache Spark section
vCPULabel=vCPUs
#XFLD: Memory label in Apache Spark section
memoryLabel=Μνήμη (GB)
#XFLD: space configuration label in Apache Spark section
spaceConfigurationLabel=Διαμόρφωση Χώρων
#XFLD: Space Source label
sparkApplicationLabel=Εφαρμογή
#XFLD: Cluster Size label
clusterSizeLabel=Μέγεθος Συστοιχίας
#XFLD: Driver label
driverLabel=Οδηγός
#XFLD: Executor label
executorLabel=Πρόγραμμα Εκτέλεσης
#XFLD: max label
maxLabel=Μέγ.Αρ.Χρησιμοποιημένων
#XFLD: TrF Default label
trFDefaultLabel=Προεπιλογή Ροής Μετασχηματισμού
#XFLD: Merge Default label
mergeDefaultLabel=Συγχώνευση Προεπιλογής
#XFLD: Optimize Default label
optimizeDefaultLabel=Βελτιστοποίηση Προεπιλογής
#XFLD: Deployment Default label
deploymentDefaultLabel=Ανάπτυξη Τοπικού Πίνακα (Αρχείο)
#XFLD: max spark source label
driverExecutorMaxUsedSparkSource={0} CPU / {1} GB
#XFLD: Object type label
taskObjectTypeLabel=Τύπ.Αντικ.
#XFLD: Task activity label
taskActivityLabel=Δραστηριότητα
#XFLD: Task Application ID label
taskApplicationIDLabel=Προεπιλεγμένη Εφαρμογή
#XFLD: Section header in space detail
generalSettings=Γενικές Ρυθμίσεις
#XMSG: Message strip message when a space is locked
spaceLockedMessageStrip=Αυτός ο χώρος είναι προσωρινά κλειδωμένος από το σύστημα.
#XMSG: Message strip message deploy warning
deployWarningMessageStrip=Αλλαγές σε αυτή την ενότητα θα αναπτυχθούν άμεσα.
#XMSG: Message strip message modify space configuration warning
modifySpaceConfigurationMessageStrip=Σημειώστε ότι η αλλαγή τιμών μπορεί να προκαλέσει προβλήματα απόδοσης.
#XFLD: Button text to unlock the space again
unlockSpace=Ξεκλείδωμα Χώρου
#XFLD: Info text for audit log formatted message
auditLogText=Ενεργοποίηση ημερολογίων ελέγχου για καταγραφή ενεργειών ανάγνωσης ή αλλαγής (πολιτικές ελέγχου). Οι διαχειριστές μπορούν να αναλύουν ποιος εκτέλεσε ποια ενέργεια και πότε.
#XMSG: Message strip  audit warning
auditLogWarningMessageStrip=Τα ημερολόγια ελέγχου μπορούν να χρησιμοποιήσουν ένα μεγάλο ποσό αποθήκευσης δίσκου στον μισθωτή σας. Αν ενεργοποιήσετε μία πολιτική ελέγχου (ενέργειες ανάγνωσης ή αλλαγής), πρέπει να παρακολουθείτε τακτικά τη χρήση αποθήκευσης δίσκου (μέσω της κάρτας Χρησιμοποιημένη Αποθήκευση Δίσκου στην Παρακολούθηση Συστήματος) για να αποφεύγονται προβλήματα στην αποθήκευση του δίσκου που μπορεί να οδηγήσουν σε διαταραχές της υπηρεσίας. Αν απενεργοποιήσετε την πολιτική ελέγχου, όλες οι καταχωρίσεις ημερολογίου της θα διαγραφούν. Αν θέλετε να διατηρήσετε τις καταχωρίσεις ημερολογίου ελέγχου, πρέπει να τις εξαγάγετε πριν απενεργοποιήσετε την πολιτική ελέγχου.
#XFLD: Message strip link text to open in app help to guide user what to do to unlock his space permanently
unlockSpaceHelp=Εμφ.Βοήθ.
#XMSG: Message strip message when a space will be locked again
spaceTemporarilyUnlockedMessageStrip=Αυτός ο χώρος υπερβαίνει την αποθήκευση χώρων του και θα κλειδωθεί στο {0} {1}.
#XMSG: Unit for remaining time until space is locked again
hours=ώρες
#XMSG: Unit for remaining time until space is locked again
minutes=λεπτά
#XFLD: Section header in space detail for auditing
detailsSectionAuditing=Έλεγχος
#XFLD: Subsection header in space detail for auditing
auditing=Ρυθμίσεις Ελέγχου Χώρου
#XFLD: Hot space tooltip
hotSpaceCountTooltip=Σημαντικοί χώροι: Η χρησιμοποιημένη αποθήκευση είναι μεγαλύτερη από 90%.
#XFLD: Green space tooltip
greenSpaceCountTooltip=Υγιείς χώροι: Η χρησιμοποιημένη αποθήκευση είναι μεταξύ 6% και 90%.
#XFLD: Cold space tooltip
coldSpaceCountTooltip=Ψυχροί χώροι: Η χρησιμοποιημένη αποθήκευση είναι 5% ή μικρότερη.
#XFLD: Critical space tooltip
criticalSpaceCountTooltip=Σημαντικοί χώροι: Η χρησιμοποιημένη αποθήκευση είναι μεγαλύτερη από 90%.
#XFLD: Green space tooltip
okSpaceCountTooltip=Υγιείς χώροι: Η χρησιμοποιημένη αποθήκευση είναι μεταξύ 6% και 90%.
#XFLD: Locked space tooltip
lockedSpaceCountTooltipNew=Κλειδωμένοι χώροι: δεσμευμένο λόγω ανεπαρκούς μνήμης.
#XFLD: Locked space tooltip
lockedSpaceCountTooltip=Κλειδωμένοι Χώροι
#YMSE: Error while deleting remote source
deleteRemoteError=Οι συνδέσεις δεν διαγράφηκαν.
#XFLD: Space ID input tooltip
spaceIDInfoTooltip=Το ID χώρου δεν μπορεί να αλλάξει αργότερα.\nΈγκυροι χαρακτήρες A - Z, 0 - 9, και _
#YMSE: Error when space name is empty
spaceNameEmptyValidationError=Εισάγετε όνομα χώρου.
#YMSE: Error when ECN Business name is empty
ecnNameEmptyValidationError=Εισάγετε επωνυμία επιχείρησης.
#YMSE: Error when space id is empty
spaceIDInputEmptyValidationError=Εισάγετε ID χώρου.
#YMSE: Error when space id contains invalid characters
spaceIDInputInValidError=Άκυροι χαρακτήρες. Χρησιμοποιήστε τα A - Z, 0 - 9, και _ μόνο.
#YMSE: Error when space id exists while creation
spaceIDInputDuplicateError=Το ID χώρου υπάρχει ήδη.
#XFLD: Space searchfield placeholder
search=Αναζήτηση
#XMSG: Success message after creating users
createUsersSuccess=Χρήστες προστέθηκαν
#XMSG: Success message after creating user
createUserSuccess=Ο χρήστης προστέθηκε
#XMSG: Success message after updating users
updateUsersSuccess={0} Οι χρήστες ενημερώθηκαν
#XMSG: Success message after updating user
updateUserSuccess=Ο χρήστης ενημερώθηκε
#XMSG: Success message after removing users
removeUsersSuccess={0} Οι χρήστες διαγράφηκαν
#XMSG: Success message after removing user
removeUserSuccess=Ο χρήστης διαγράφηκε
#XFLD: Schema name
schemaName=Ονομα Σχήματος
#XFLD: used of total
ofTemplate={0} του {1}
#XFLD: Assigned disk label in space overview
assignedStorageTemplate=Αντιστοιχισμένος Δίσκος ({0} από {1})
#XFLD: Assigned Memory label in space overview
assignedRAMTemplate=Αντιστοιχισμένη Μνήμη ({0} από {1})
#XFLD: Storage ratio on space
accelearationRAM=Επιτάχυνση Μνήμης
#XFLD: No Storage Consumption
noStorageConsumptionText=Δεν αντιστοιχίστηκε ποσοστό χώρου.
#XFLD: Used disk label in space overview
usedStorageTemplate=Δίσκος Χρησιμοποιημένος για Αποθήκευση ({0} του {1})
#XFLD: Used Memory label in space overview
usedRAMTemplate=Μνήμη Χρησιμοποιημένη για Αποθήκευση ({0} του {1})
#XFLD: Used Disk tooltip
usedStorageTooltipTemplate={0} του {1} Δίσκου που χρησιμοποιήθηκε για Αποθήκευση
#XFLD: Used Memory tooltip
usedRAMTooltipTemplate={0} από {1} Μνήμη Χρησιμοποιείται
#XFLD: Assigned Disk tooltip
assignedStorageTooltipTemplate={0} από {1} Δίσκο Αντιστοιχισμένο
#XFLD: Assigned Memory tooltip
assignedRAMTooltipTemplate={0} από {1} Μνήμη Αντιστοιχίστηκε
#XFLD: Space related used storage tooltip
spaceRelatedUsedStorageTooltipTemplate=Δεδομένα Χώρου: {0}
#XFLD: Not space related used storage tooltip
otherDataUsedStorageTooltipTemplate=Άλλα Δεδομένα: {0}
#XFLD: Storage used exceeded popover and tooltip
storageUsedExceededText=Σκεφθείτε να επεκτείνετε το πλάνο σας ή επικοινωνήστε με το SAP Support.
#XCOL: Space table-view column used Disk
usedStorage=Δίσκος Χρησιμοποιημένος για Αποθήκευση
#XCOL: Space monitor column used Memory
usedRAM=Μνήμη Χρησιμοποιημένη για Αποθήκευση
#XCOL: Space monitor column Schema
tableSchema=Σχήμα
#XCOL: Space monitor column Storage Type
tableStorageType=Τύπος Αποθήκευσης
#XCOL: Space monitor column Table Type
tableType=Τύπος Πίνακα
#XCOL: Space monitor column Record Count
tableRecordCount=Αριθμός Εγγραφών
#XFLD: Assigned Disk
assignedStorage=Δίσκος Αντιστοιχισμένος για Αποθήκευση
#XFLD: Assigned Memory
assignedRAM=Μνήμη Αντιστοιχισμένη για Αποθήκευση
#XCOL: Space table-view column storage utilization
tableStorageUtilization=Χρησιμοποιημένη Αποθήκευση
#XFLD: space status
spaceStatus=Κατάσταση Χώρου
#XFLD: space type
spaceType=Τύπος Χώρου
#XFLD: space type label for space with space type "SAP BW bridge" ("SAP BW bridge" is a product name, do not translate it)
abapbridgeSpaceType=SAP BW bridge
#XFLD: space type label for space with space type "Data Marketplace"
marketplaceSpaceType=Προϊόν Παρόχου Δεδομένων
#XMSG: Error message for when selected space cannot be deleted due to the space type
deleteTypedSpaceError=Δεν μπορείτε να διαγράψετε τον χώρο {0} γιατί ο τύπος του είναι {1}.
#XMSG: Error message for when selected spaces cannot be deleted due to the space type
deleteTypedSpacesError=Δεν μπορείτε να διαγράψετε τους {0} επιλεγμένους χώρους. Οι χώροι με τους ακόλουθους τύπους δεν μπορούν να διαγραφούν: {1}.
#XFLD: space type label for space without space type
defaultSpaceType=SAP Datasphere
#XFLD: Tooltip for monitor space button
monitorSpace=Παρακολούθηση
#XFLD: Tooltip for edit space button
editSpace=Επεξεργασία Χώρου
#XMSG: Deletion warning in messagebox
deleteConfirmation=Σίγουρα θέλετε να διαγράψετε αυτό τον χώρο;
#XFLD: Tooltip for delete space button
deleteSpace=Διαγραφή Χώρου
#XFLD: storage
storage=Δίσκος για Αποθήκευση
#XFLD: username
userName=Όνομα Χρήστη
#XFLD: port
port=Θύρα
#XFLD: hostname
hostName=Όνομα Κεντρικού Υπολογιστή
#XFLD: password
password=Κωδικός Πρόσβασης
#XBUT: Request new password button
requestPassword=Απαίτηση Νέου Κωδικού Πρόσβασης
#YEXP: Usage explanation in time data section
timeDataSectionHint=Δημιουργήστε χρονοδιαγράμματα και διαστάσεις που θα χρησιμοποιήσετε στα μοντέλα και τις παρουσιάσεις σας.
#YEXP: Usage explanation in Space schema section
spaceSchemaHint=Θέλετε τα δεδομένα στον χώρο σας να χρησιμοποιούνται από άλλα εργαλεία ή εφαρμογές; Εάν ναι, τότε δημιουργήστε έναν ή περισσότερους χρήστες που θα έχουν πρόσβαση σε αυτά στον χώρο σας και επιλέξτε εάν θέλετε όλα τα μελλοντικά δεδομένα του χώρου να είναι αναλώσιμα από προεπιλογή.
#XTIT: Create schema popup title
createSchemaDialogTitle=Δημιουργία Ανοικτού Σχήματος SQL
#XTIT: Generate Time Data dialog title
generateTimeDataDialogTitle=Δημιουργία Χρονοδιαγραμμάτων και Διαστάσεων
#XTIT: Edit Time Data dialog title
editTimeDataDialogTitle=Επεξεργασία Χρονοδιαγραμμάτων και Διαστάσεων
#XTIT: Time Data token title
timeDataTokenTitle=Δεδομένα Χρόνου
#XTIT: Time Data token title
timeDataUpdateViews=Ενημέρωση Προβολών Δεδομένων Χρόνου
#XFLD: Time Data token creation in progress label
timeDataCreationInProgress=Δημιουργία σε εξέλιξη...
#XFLD: Time Data token creation error label
timeDataCreationError=Δημιουργία απέτυχε. Δοκιμάστε ξανά.
#XTIT: Time Data dialog time settings title
timeDataDialogTimeSettingsTitle=Ρυθμίσεις Χρονοδιαγραμμάτων
#XTIT: Time Data dialog time data table title
timeDataDialogTranslationTablesTitle=Πίνακες Μετάφρασης
#XTIT: Time Data dialog dimensions title
timeDataDialogTimeViewsTitle=Διαστάσεις Χρόνου
#XFLD: Time Data dialog time range label
timeRangeHint=Καθορισμός χρονικού εύρους.
#XFLD: Time Data dialog time data table label
timeDataHint=Δώστε ένα όνομα στον πίνακά σας.
#XFLD: Time Data dialog time data dimensions label
timeDimensionsHint=Δώστε ένα όνομα στις διαστάσεις σας.
#XFLD: Time Data Time range description label
timerangeLabel=Χρονικό Εύρος
#XFLD: Time Data dialog from year label
fromYearLabel=Από Ετος
#XFLD: Time Data dialog to year label
toYearLabel=Εως Ετος
#XFLD: Time Data dialog calendar type label
calendarTypeLabel=Τύπος Ημερολογίου
#XFLD: Time Data dialog granularity label
granularityLabel=Βαθμός Πιστότητας
#XFLD: Time Data dialog technical name label
technicalNameLabel=Τεχνικό Ονομα
#XFLD: Time Data dialog quarters text table lable
quarterTextsTableLabel=Μετάφραση: Πίνακες για Τρίμηνα
#XFLD: Time Data dialog months text table lable
monthsTextsTableLabel=Μετάφραση: Πίνακες για Μήνες
#XFLD: Time Data dialog days text table lable
daysTextsTableLabel=Μετάφραση: Πίνακες για Ημέρες
#XFLD: Time Data dialog year label
yearLabel=Διάσταση για Έτος
#XFLD: Time Data dialog quarter label
quarterLabel=Διάσταση για Τρίμηνο
#XFLD: Time Data dialog month label
monthLabel=Διάσταση για Μήνα
#XFLD: Time Data dialog day label
dayLabel=Διάσταση για Ημέρα
#XFLD: Time Data dialog gregorian calendar type label
gregorian=Γρηγοριανό
#XFLD: Time Data dialog time granularity day label
day=Ημέρα
#XFLD: Time Data dialog business name maxlength reached
timeRangeValueStateTextMaxLengthBusinessName=Μέγιστο μήκος 1000 χαρακτήρων καλύφθηκε.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextMaxRangeExceeded=Το μέγιστο χρονικό εύρος είναι 150 έτη.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeLower=“Έτος Από” πρέπει να είναι πριν το “Έτος Εως”
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeHigher=«Έτος Από» πρέπει να είναι από το 1900 κι έπειτα.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeHigher=“Έτος Έως” πρέπει να είναι μετά το “Έτος Από”
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeLower=«Έτος Έως» πρέπει να είναι μικρότερο από το τρέχον έτος συν 100
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningFromYear=Η αύξηση της τιμής «Έτος Από" ίσως οδηγήσει σε απώλεια δεδομένων
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningToYear=Η μείωση της τιμής "Έτος Εως" ίσως οδηγήσει σε απώλεια δεδομένων
#XMSG: Time Data creation validation error message
timeDataValidationError=Ίσως ορισμένα πεδία είναι άκυρα. Ελέγξτε τα υποχρεωτικά πεδία για να συνεχίσετε.
#XMSG: Time Data deletion popup text
deleteTimeDataConfirmation=Σίγουρα θέλετε να διαγράψετε τα δεδομένα;
#XMSG: Time Data creation success message
createTimeDataSuccess=Τα δεδομένα χρόνου δημιουργήθηκαν
#XMSG: Time Data update success message
updateTimeDataSuccess=Τα δεδομένα χρόνου ενημερώθηκαν
#XMSG: Time Data delete success message
deleteTimeDataSuccess=Τα δεδομένα χρόνου διαγράφηκαν
#XMSG: Time Data creation error message
createTimeDataError=Κάποιο λάθος συνέβη όταν προσπαθήσατε να δημιουργήσετε δεδομένα χρόνου.
#XMSG: Time Data update error message
updateTimeDataError=Κάποιο λάθος συνέβη όταν προσπαθήσατε να ενημερώσετε δεδομένα χρόνου.
#XMSG: Time Data creation error message
deleteTimeDataError=Κάποιο λάθος συνέβη όταν προσπαθήσατε να διαγράψετε δεδομένα χρόνου.
#XMSG: Error message when time data could not be retrieved
retrievingTimeDataError=Αδύνατη φόρτωση δεδομένων χρόνου.
#XTIT: Time Data deletion popup title
deleteTimeDataConfirmationTitle=Προειδοποίηση
#XTXT: Time Data deletion dependency popup text
deleteTimeDataDependencyText=Δεν μπορέσαμε να διαγράψουμε τα δεδομένα χρόνου γιατί χρησιμοποιούνται σε άλλα μοντέλα.
#XTXT: Time Data deletion dependency popup text singular
deleteTimeDataDependencyTextSingular=Δεν μπορέσαμε να διαγράψουμε τα δεδομένα χρόνου γιατί χρησιμοποιούνται σε άλλο μοντέλο.
#XMSG: Validation error when required time data form field is empty
timeDataRequiredField=Αυτό το πεδίο είναι υποχρεωτικό και δεν μπορεί να μείνει κενό.
#XFLD: Tooltip for Open in Database Explorer button
openInDBX=Άνοιγμα στο Database Explorer
#YMSE: Dimension Year
dimensionYearView=Διάσταση «Έτος»
#YMSE: Dimension Year
dimensionQuarterView=Διάσταση «Τρίμηνο»
#YMSE: Dimension Year
dimensionMonthView=Διάσταση «Μήνας»
#YMSE: Dimension Year
dimensionDayView=Διάσταση «Ημέρα»
#XFLD: Time Data deletion object title
timeDataUsedIn=(χρησιμοποιείται σε {0} μοντέλα)
#XFLD: Time Data deletion object title singular
timeDataUsedInSingular=(χρησιμοποιείται σε 1 μοντέλο)
#XFLD: Time Data deletion table column provider
provider=Πάροχος
#XTIT: Time Data dependency dialog title
timeDataDependencyDialogTitle=Εξαρτήσεις
#XTIT: Create schema popup title
createSpaceSchemaUserDialogTitle=Δημιουργία Χρήστη για Σχήμα Χώρου
#XFLD: Create schema button
createSchemaButton=Δημιουργία Ανοικτού Σχήματος SQL
#XFLD: Generate TimeData button
generateTimeDataButton=Δημιουργία Πινάκων Χρόνων και Διαστάσεων
#XFLD: Show dependencies button
showDependenciesButton=Εμφάνιση Εξαρτήσεων
#XFLD: tooltip for disabled buttons when user is not member of space
mustBeMemberOfSpaceForOperation=Για να εκτελέσετε αυτή την λειτουργία, ο χρήστης σας πρέπει να είναι μέλος του χώρου.
#XFLD: Create space schema user button
createSpaceSchemaUserButton=Δημιουργία Χρήστη Σχήματος Χώρου
#YMSE: API Schema users load error
loadSchemaUsersError=Αδύνατη φόρτωση λίστας χρηστών.
#XTIT: API Schema-config popup title
schemaUserDetailsDialogTitle=Λεπτομέρειες για Χρήστη Σχήματος Χώρου
#XMSG: API Schema user deletion popup text
deleteSpaceSchemaUserConfirmation=Σίγουρα θέλετε να διαγράψετε τον επιλεγμένο χρήστη;
#XMSG: API Schema user deletion confirmation text
userDeleteSuccess=Χρήστης διαγράφηκε.
#YMSE: API Schema user deletion error
userDeleteError=Ο χρήστης δεν μπόρεσε να διαγραφεί.
#XFLD: User deleted
userDeleted=Ο χρήστης διαγράφηκε.
#XTIT: Remove user popup title
removeUserConfirmationTitle=Προειδοποίηση
#XMSG: Remove user popup text
removeUserConfirmation=Θέλετε να διαγράψετε τον χρήστη; Ο χρήστης και οι αντιστοιχισμένοι σε αυτόν ρόλοι θα διαγραφούν από τον χώρο.
#XMSG: Remove users popup text
removeUsersConfirmation=Θέλετε να διαγράψετε τους χρήστες; Οι χρήστες και οι αντιστοιχισμένοι σε αυτούς ρόλοι θα διαγραφούν από τον χώρο.
#XBUT: Confirmation button for remove user dialog
removeUserConfirmationBtn=Διαγραφή
#YMSE: No data text for available roles
noDataAvailableRoles=Ο χώρος δεν προστέθηκε σε συγκεκριμένο ρόλο. \n Για να προσθέσετε χρήστες στον χώρο, πρέπει πρώτα να προστεθεί σε έναν ή περισσότερους ρόλους.
#YMSE: No data text for selected roles
noDataSelectedRoles=Δεν επιλέχθηκαν ρόλοι
#XTIT: Schema-config popup title
schemaDetailsDialogTitle=Λεπτομέρειες για Διαμόρφωση Ανοικτού Σχήματος SQL
#XFLD: Label for Read Audit Log
auditLogRead=Ενεργοποίηση Ημερολογίου Ελέγχου για Λειτουργίες Ανάγνωσης
#XFLD: Label for Change Audit Log
auditLogChange=Ενεργοποίηση Ημερολογίου Ελέγχου για Λειτουργίες Αλλαγής
#XFLD: Label Audit Log Retention
auditLogRetention=Διατήρηση Ημερολογίων για
#XFLD: Label Audit Log Retention Unit
retentionUnit=Ημέρες
#YMSE: Error when log retention time is set incorrectly
incorrectRetentionTime=Εισαγωγή ενός ακέραιου αριθμού μεταξύ {0} και {1}
#XFLD: Label for Cross Space Schema Access
spaceSchemaAccess=Χρήση Δεδομένων Σχήματος Χώρου
#XTIT: Confirm deactivation of space schema consumption dialog title
disableSpaceSchemaConsumptionDialogTitle=Διακοπή Χρήσης Δεδομένων Σχήματος Χώρου
#XTXT: Confirm deactivation of space schema consumption dialog text
disableSpaceSchemaConsumptionDialogText=Αυτό το ανοικτό σχήμα SQL ενδέχεται να χρησιμοποιήσει δεδομένα του σχήματος χώρου σας. Εάν διακόψετε την χρήση, τα μοντέλα που βασίζονται στα δεδομένα σχήματος χώρου μπορεί να μην λειτουργούν πια.
#XBUT: Confirmation button for the stop consuming space schema data dialog
stopConsuming=Διακοπή Χρήσης
#XTXT: Tooltip for data lake icon
dataLakeIconTooltip=Αυτός ο χώρος χρησιμοποιείται για την πρόσβαση στην λίμνη δεδομένων
#XFLD: Tooltip for data lake table entry
dataLakeTableEntryTooltip=Ενεργή Λίμνη Δεδομένων
#XFLD: used In-Memory linit reached tag
usedRamLimitReachedLabel=Οριο Μνήμης Καλύφθηκε
#XFLD: Space storage Limit reached tag
storageLimitReachedLabel=Όριο Αποθήκευσης Καλύφθηκε
#XFLD: Minimal Storage Limit Reached tag
minStorageLimitReachedLabel=Ελάχιστο Όριο Αποθήκευσης Καλύφθηκε
#XFLD: Space ram tag
ramLimitReachedLabel=Όριο Μνήμης Καλύφθηκε
#XFLD: Minimal In-Memory Limit Reached tag
minRamLimitReachedLabel=Ελάχιστο Όριο Μνήμης Καλύφθηκε
#XFLD: Popover text after click on space storage limit warning
storageLimitReachedText=Καλύψατε το όριο της αντιστοιχισμένης αποθήκευσης χώρου των {0}. Αντιστοιχίστε περισσότερη αποθήκευση στον χώρο.
#XFLD: System storage tag
systemStorageLimitReachedLabel=Όριο Αποθήκευσης Συστήματος Καλύφθηκε
#XMSG: Popover text after click on storage limit warning
systemStorageLimitReachedText=Έχετε καλύψει το όριο της αποθήκευσης συστήματος των {0}. Δεν μπορείτε να αντιστοιχίσετε πια άλλη αποθήκευση στον χώρο.
#XMSG: Schema deletion warning
deleteSchemaConfirmation=Εάν διαγράψετε αυτό το ανοικτό σχήμα SQL θα διαγραφούν οριστικά και όλα τα αποθηκευμένα αντικείμενα και συντηρημένες σχέσεις στο σχήμα. Συνέχεια;
#XMSG: Success message after deleting a schema
schemaDeleteSuccess=Το σχήμα διαγράφηκε
#YMSE: Error while deleting schema.
schemaDeleteError=Το σχήμα δεν μπόρεσε να διαγραφεί.
#XMSG: Success message after update a schema
schemaUpdateSuccess=Το σχήμα ενημερώθηκε
#YMSE: Error while updating schema.
schemaUpdateError=Το σχήμα δεν μπόρεσε να ενημερωθεί.
#XMSG: Hint when no password is shown in the dialog.
newPasswordHint=Σας έχουμε δώσει ένα κωδικό πρόσβασης για αυτό το σχήμα. Εάν ξεχάσατε τον κωδικό πρόσβασής σας ή τον έχετε χάσει, μπορείτε να ζητήσετε έναν νέο. Αντιγράψτε ή αποθηκεύστε τον νέο κωδικό πρόσβασης.
#XMSG: Hint when showing the password in the dialog.
savePasswordHint=Αντιγράψτε τον κωδικό πρόσβασής σας. Θα τον χρειαστείτε για να δημιουργήσετε μία σύνδεση με αυτό το σχήμα. Εάν τον ξεχάσατε, μπορείτε να ανοίξετε αυτόν τον διάλογο για να τον επανακαθορίσετε.
#XFLD,50: Hint on entering schema name in creation dialog
schemaNameHint=Το όνομα δεν αλλάζει αφού δημιουργήσετε το σχήμα.
#XFLD: Open SQL Schemas section sub headline
schemasSQL=Ανοικτό SQL
#XFLD: Space schema section sub headline
schemasSpace=Χώρος
#XFLD: HDI Container section header
HDIContainers=HDI Containers
#XTXT: Add HDI Containers button tooltip
addHDIContainers=Προσθήκη HDI Containers
#XTXT: Remove HDI Containers button tooltip
removeHDIContainers=Διαγραφή HDI Containers
#XFLD: Enable HDI Containers feature button
enableHDIContainersButton=Ενεργοποίηση Πρόσβασης
#YMSE: No data text for HDI Containers table
noDataHDIContainers=Δεν προστέθηκαν HDI containers.
#YMSE: No data text for Timedata section
noDataTimedata=Δεν δημιουργήθηκαν χρονοδιαγράμματα και διαστάσεις.
#YMSE: No data text during runtime not available for Timedata section
runtimeNotAvailableNoDataTimedata=Αδύνατη φόρτωση πινάκων χρόνου και διαστάσεων καθώς η βάση δεδομένων χρόνου εκτέλεσης δεν είναι διαθέσιμη.
#YMSE: No data text during runtime not available for HDI section
runtimeNotAvailableNoDataHDI=Αδύνατη φόρτωση HDI Containers καθώς η βάση δεδομένων χρόνου εκτέλεσης δεν είναι διαθέσιμη.
#YMSE: Error while fetching HDI Containers
fetchHDIContainersError=Τα HDI Containers δεν μπόρεσαν να αποκτηθούν. Δοκιμάστε ξανά αργότερα.
#XFLD Table column header for HDI Container names
HDIContainerName=Όνομα HDI Container
#XFLD: Enable HDI Containers feature dialog title
enableHDIContainersDialogTitle=Ενεργοποίηση Πρόσβασης
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText1New=Μπορείτε να ενεργοποιήσετε το SAP SQL Data Warehousing στον μισθωτή SAP Datasphere για να ανταλλάξετε δεδομένα μεταξύ των HDI containers και των χώρων SAP Datasphere χωρίς να απαιτείται μετακίνηση δεδομένων.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText2=Για να το κάνετε, δημιουργήστε ένα μήνυμα υποστήριξης πατώντας στο παρακάτω κουμπί.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText3New=Μόλις ολοκληρωθεί το μήνυμά σας, θα πρέπει να δημιουργήσετε ένα ή περισσότερα νέα HDI containers στη βάση δεδομένων χρόνου εκτέλεσης του SAP Datasphere. Επειτα, το κουμπί Ενεργοποίηση Πρόσβασης αντικαθίστται από το κουμπί + στην ενότητα HDI Containers για όλους τους χώρους SAP Datasphere και μπορείτε να προσθέσετε τα containers σε έναν χώρο.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText41=Χρειάζεστε περισσότερες πληροφορίες; Μεταβείτε στο %%0. Για λεπτομέρειες σχετικά με το τι θα περιλαμβάνεται στο μήνυμά, δείτε %%1.
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkText=SAP Help
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkTextSapNote3057059=SAP Note 3057059
#XBUT: Open Ticket Button Text
openTicket=Άνοιγμα Μηνύματος
#XBUT: Add Button Text
add=Προσθήκη
#XBUT: Next Button Text
next=Επόμενο
#XBUT: Edit Button Text
editUsers=Επεξεργασία
#XBUT: create user Button Text
createUser=Δημιουργία
#XBUT: Update user Button Text
updateUser=Επιλογή
#XTIT: Add HDI Containers Dialog Title
addHDIContainersDialogTitle=Προσθήκη Μη αντιστοιχισμένων HDI Containers
#XFLD: No data text for add HDI container dialog
addHDIContainersDialogNoDataText=Δεν μπορέσαμε να βρούμε μη αντιστοιχισμένα containers. \n Tο container που αναζητάτε ίσως είναι ήδη αντιστοιχισμένο σε έναν χώρο.
#YMSE: Error while loading assigned HDI Containers
loadAssignedHDIContainersError=Τα αντιστοιχισμένα HDI containers δεν μπόρεσαν να φορτωθούν.
#YMSE: Error while loading assigned HDI Containers
loadUnassignedHDIContainersError=Τα HDI containers δεν μπόρεσαν να φορτωθούν.
#XMSG: Success message
succeededToAddHDIContainer=HDI container προστέθηκε
#XMSG: Success message
succeededToAddHDIContainerPlural=HDI containers προστέθηκαν
#XMSG: Success message
succeededToDeleteHDIContainer=HDI container διαγράφηκε
#XMSG: Success message
succeededToDeleteHDIContainerPlural=HDI containers διαγράφηκαν
#XFLD: Time data section sub headline
timeDataSection=Χρονοδιαγράμματα και Διαστάσεις
#XFLD: Schemas list entry read audit log enabled indicator
readIndicator=Ανάγνωση
#XFLD: Schemas list entry change audit log enabled indicator
changeIndicator=Αλλαγή
#XFLD: Remote sources section sub headline
allconnections=Αντιστοίχιση Σύνδεσης
#XFLD: Remote sources section sub headline
localconnections=Τοπικές Συνδέσεις
#XFLD: Remote sources section sub headline for open Connectors
openConnectors=SAP Open Connectors
#XFLD: User section sub headline
memberassignment=Αντιστοίχιση Μέλους
#XFLD: User assignment section sub headline
userAssignment=Αντιστοίχιση Χρήστη
#XFLD: User section Access dropdown Member
member=Μέλος
#XFLD: User assignment section column name
user=Όνομα Χρήστη
#XTXT: Selected role count
selectedRoleToolbarText=Επιλεγμένο: {0}
#XTIT: Space detail section connections title
detailsSectionConnections=Συνδέσεις
#XTIT: Space detail section data access title
detailsSectionDataAccess=Πρόσβαση σε Σχήμα
#XTIT: Space detail section time data title
detailsSectionGenerateData=Δεδομένα Χρόνου
#XTIT: Space detail section members title
detailsSectionUsers=Μέλη
#XTIT: Space detail section Users title
detailsSectionUsersTitle=Χρήστες
#XTIT: Out of Storage
insufficientStoragePopoverTitle=Εκτός Αποθηκευτικού Χώρου
#XTIT: Storage distribution
storageDistributionPopoverTitle=Αποθήκευση Δίσκου Χρησιμοποιήθηκε
#XTXT: Out of Storage popover text
insufficientStorageText=Για να δημιουργήσετε έναν νέο χώρο, μειώστε την αντιστοιχισμένη αποθήκευση ενός άλλου χώρου ή διαγράψτε έναν χώρο που δεν χρειάζεστε πια. Μπορείτε να αυξήσετε την συνολική αποθήκευση συστήματος καλώντας το Διαχείριση Προγράμματος.
#XMSG: Space id length warning
spaceIdLengthWarning=Μέγιστος αριθμός {0} χαρακτήρων έχει καλυφθεί.
#XMSG: Space name length warning
spaceNameLengthWarning=Μέγιστος αριθμός {0} χαρακτήρων έχει καλυφθεί.
#XMSG: Space id reserved prefix warning
spaceIdnWarningReservedPrefix=Μην χρησιμοποιήσετε το πρόθεμα {0} για να αποφύγετε τυχόν αντιφάσεις.
#YMSE: Error while loading open SQL schemas
loadLocalSchemasError=Αδύνατη φόρτωση ανοικτών σχημάτων SQL.
#YMSE: Error while creating open SQL schema
createLocalSchemaError=Αδύνατη δημιουργία ανοικτού σχήματος SQL.
#YMSE: Error while loading all remote connections
loadRemoteConnectionsError=Αδύνατη φόρτωση όλων των απομακρυσμένων συνδέσεων.
#YMSE: Error while loading space details
loadSpaceDetailsError=Αδύνατη φόρτωση στοιχείων χώρου.
#YMSE: Error while deploying space details
deploySpaceDetailsError=Αδύνατη ανάπτυξη χώρου.
#YMSE: Error while copying space details
copySpaceDetailsError=Αδύνατη αντιγραφή χώρου.
#YMSE: Error while loading storage data
loadStorageDataError=Αδύνατη φόρτωση δεδομένων αποθήκευσης.
#YMSE: Error while loading all users
loadAllUsersError=Αδύνατη φόρτωση όλων των χρηστών.
#YMSE: Failed to reset password
resetPasswordError=Αδύνατος επανακαθορισμός κωδικού πρόσβασης.
#XMSG: Success message after updating space
resetPasswordSuccessMessage=Νέος κωδικός πρόσβασης ορίστηκε για σχήμα
#YMSE: DP Agent-name too long
DBAgentNameError=Το όνομα του χρήστη DP είναι εκτενές.
#YMSE: Schema-name not valid.
schemaNameError=Το όνομα του σχήματος είναι άκυρο.
#YMSE: User name not valid.
UserNameError=Το όνομα του χρήστη είναι άκυρο.
#XTIT: Consumption by Storage Type
consumptionStorageTypeText=Χρήση βάσει Τύπου Αποθήκευσης
#XTIT: Consumption by Schema
consumptionSchemaText=Χρήση βάσει Σχήματος
#XTIT: Overall Consumption by schema
overallConsumptionSchemaText=Συνολική Χρήση Πίνακα βάσει Σχήματος
#XTIT: Overall Consumption by Table Type
overallConsumptionTableTypeText=Συνολική Ανάλωση βάσει Τύπου Πίνακα
#XTIT: Tables
tableDetailsText=Λεπτομέρειες Πίνακα
#XTIT: Table Storage Consumption
tableStorageConsumptionText=Χρήση Αποθήκευσης Πίνακα
#XFLD: Table Type label
tableTypeLabel=Τύπος Πίνακα
#XFLD: Schema label
schemaLabel=Σχήμα
#XFLD: reset table tooltip
resetTable=Επανακαθορισμός Πίνακα
#XFLD: In-Memory label in space monitor
inMemoryLabel=Μνήμη
#XFLD: Disk label in space monitor
diskLabel=Δίσκος
#XFLD: Yes
yesLabel=Ναι
#XFLD: No
noLabel=Οχι
#XFLD: switch label: Want the data in this space to be consumable by default?
allowConsumptionByDefault=Θέλετε τα δεδομένα σε αυτόν τον χώρο να χρησιμοποιούνται από προεπιλογή;
#XFLD: Business Name
businessNameLabel=Επιχειρηματικό Όνομα
#XFLD: Refresh
refresh=Ανανέωση
#XMSG: No filter results title
noFilterResultsTitle=Οι ρυθμίσεις φίλτρου σας δεν εμφανίζουν δεδομένα.
#XMSG: No filter results message
noFilterResultsMsg=Δοκιμάστε να περιορίσετε τις ρυθμίσεις φίλτρου. Εάν εξακολουθούν να μην εμφανίζονται δεδομένα, τότε, δημιουργήστε ορισμένους πίνακες στο Data Builder. Εφόσον χρησιμοποιήσουν την αποθήκευση, τότε θα μπορείτε να τα παρακολουθείτε από εδώ.
#XMSG: Space monitoring Hana state red title
spaceMonitorHanaRedTitle=Η βάση δεδομένων χρόνου εκτέλεσης δεν είναι διαθέσιμη.
#XMSG: Space monitoring Hana state red message
spaceMonitorHanaRedMsg=Εφόσον η βάση δεδομένων χρόνου εκτέλεσης δεν είναι διαθέσιμη, ορισμένα χαρακτηριστικά είναι ανενεργά και δεν μπορούν να εμφανιστούν πληροφορίες σε αυτή τη σελίδα.
#YMSE: Error while creating space schema user
createSpaceSchemaUserError=Αδύνατη δημιουργία χρήστη σχήματος χώρου.
#YMSE: Error User name already exists
userAlreadyExistsError=Το όνομα χρήστη υπάρχει ήδη.
#YMSE: Error Authentication failed
authenticationFailedError=Ταυτοποίηση απέτυχε.
#XMSG: Message strip message when a schema user is locked
userLockedMessageStrip=Ο χρήστης είναι κλειδωμένος εξαιτίας πολλών αποτυχημένων προσπαθειών. Ζητήστε ένα νέο κωδικό πρόσβασης για να ξεκλειδώσετε τον χρήστη.
#XMSG: Success message after reset and unlock schema user
resetUnlockPasswordSuccessMessage=Νέος κωδικός πρόσβασης ορίστηκε και χρήστης ξεκλειδώθηκε
#XMSG: user is locked message
userLockedMessage=Ο χρήστης είναι κλειδωμένος.
#XCOL: Users table-view column Role
spaceRole=Ρόλος
#XCOL: Users table-view column Scoped Role
spaceScopedRole=Ρόλος
#XCOL: Users table-view column Space Admin
spaceAdmin=Διαχειριστής Χώρου
#XFLD: User section dropdown value Viewer
viewer=Viewer
#XFLD: User section dropdown value Modeler
modeler=Modeler
#XFLD: User section dropdown value Data Integrator
dataIntegrator=Data Integrator
#XFLD: User section dropdown value Space Administrator
spaceAdministrator=Διαχειριστής Χώρου
#XMSG: Success message -  Space Role updated successfully.
updateUserSpaceRoleSuccess=Ρόλος χώρου ενημερώθηκε
#XMSG: Error message - Failed to update Space Role.
updateUserSpaceRoleError=Ρόλος Χώρου δεν ενημερώθηκε.
#XFLD:
databaseUserNameSuffix=Κατάληξη Ονόματος Χρήστη Βάσης Δεδομένων
#XTXT: Space Schema password text
spaceSchemaPasswordText=Για να διαμορφώσετε μία σύνδεση με αυτόν τον χρήστη ανάλυσης, αντιγράψτε τον κωδικό πρόσβασής σας. Σε περίπτωση που τον ξεχάσατε, μπορείτε πάντα να ζητήσετε έναν νέο.
#XFLD: Cloud Platform label in space schema dialog
cloudPlatformLabel=Cloud Platform
#XTXT: Cloud Platform text in space schema dialog
cloudPlatformText=Για να διαμορφώσετε πρόσβαση μέσω αυτού του χρήστη, ενεργοποιήστε την ανάλωση και αντιγράψτε τα διαπιστευτήρια. Σε περίπτωση που μπορείτε μόνο να αντιγράψετε τα διαπιστευτήρια χωρίς κωδικό πρόσβασης, βεβαιωθείτε ότι θα τον προσθέσετε αργότερα.
#XFLD: enable consumption in CloudPlatform label in space schema dialog
enableConsumptionInCloudPlatformLabel=Ενεργοποίηση Ανάλωσης στο Cloud Platform
#XFLD: Credentials for User-Provided Service label in space schema dialog
cloudPlatformCredentialsLabel=Διαπιστευτήρια για Παρεχόμενη από Χρήστη Υπηρεσία:
#XFLD: Credentials for User-Provided Service (Without Password) label in space schema dialog
cloudPlatformCredentialsWithoutPasswordLabel=Διαπιστευτήρια για Παρεχόμενη από Χρήστη Υπηρεσία (Χωρίς Κωδικό Πρόσβασης):
#XFLD: Copy Credentials label in space schema dialog
copyCredentialsLabel=Αντιγραφή Διαπιστευτηρίων χωρίς Κωδικό Πρόσβασης
#XFLD: Copy Credentials label in space schema dialog
copyFullCredentialsLabel=Αντιγραφή Πλήρων Διαπιστευτηρίων
#XFLD: Copy Password label in space schema dialog
copyPasswordLabel=Αντιγραφή Κωδικού Πρόσβασης
#XMSG: Credentials copied to clipboard
credentialsCopiedMessage=Διαπιστευτήρια αντιγράφηκαν σε πίνακα σημειώσεων
#XMSG: Password copied to clipboard
passwordCopiedMessage=Ο κωδικός πρόσβασης αντιγράφηκε σε πίνακα σημειώσεων
#XMSG: create database user dialog title
createDatabaseUserDialogTitle=Δημιουργία Χρήστη Βάσης Δεδομένων
#XMSG: Database Users section title
databaseUsers=Χρήστες Βάσης Δεδομένων
#XMSG: Database User Details dialog title
databaseUserDetailsDialogTitle=Λεπτομέρειες για Χρήστη Βάσης Δεδομένων
#XFLD: database user read audit log
databaseUserAuditLogRead=Ενεργοποίηση Ημερολογίων Ελέγχου για Ανάγνωση Διαδικασιών και Διατήρηση Ημερολογίων για
#XFLD: database user change audit log
databaseUserAuditLogChange=Ενεργοποίηση Ημερολογίων Ελέγχου για Αλλαγή Διαδικασιών και Διατήρηση Ημερολογίων για
#XMSG: Cloud Platform Access
cloudPlatformAccess=Πρόσβαση στο Cloud Platform
#XFLD: Enable Consumption in Cloud Platform hint
enableConsumptionCloudPlatformHint=Διαμορφώστε πρόσβαση στον αποδέκτη HANA Deployment Infrastructure (HDI) μέσω αυτού του χρήστη βάσης δεδομένων. Για να συνδεθείτε με τον αποδέκτη HDI, πρέπει να είναι ενεργή η μοντελοποίηση SQL.
#XFLD: Enable HDI Consumption
enableConsumptionCloudPlatform=Ενεργοποίηση Ανάλωσης HDI
#XFLD: Enable Consumption hint
enableConsumptionHint=Θέλετε αυτά τα δεδομένα στον χώρο σας να χρησιμοποιούνται από άλλα εργαλεία ή εφαρμογές;
#XFLD: Enable Consumption
enableConsumption=Ενεργοποίηση Ανάλωσης SQL
#XFLD: Enable Modeling
enableModeling=Ενεργοποίηση Μοντελοποίηση SQL
#XMSG: Privileges for Data Modeling
privilegesModeling=Απορρόφηση δεδομένων
#XMSG: Privileges for Data Consumption
privilegesConsumption=Ανάλωση Δεδομένων για Εξωτερικά Εργαλεία
#XFLD: SQL Modeling
sqlModeling=Μοντελοποίηση SQL
#XFLD: SQL Consumption
sqlConsumption=Ανάλωση SQL
#XFLD: enabled
enabled=Ενεργοποιημένο
#XFLD: disabled
disabled=Απενεργοποιημένο
#XFLD: Edit Privileges
editPrivileges=Επεξεργασία Προνομίων
#XFLD: Open Database Explorer
openDBX=Ανοιγμα του Database Explorer
#XFLD: create database user hint
databaseCreateHint=Λάβετε υπόψη ότι δεν θα μπορείτε να αλλάξετε το όνομα χρήστη μετά την αποθήκευση.
#XFLD: Internal Schema Name
internalSchemaName=Ονομα Εσωτερικού Σχήματος
#YMSE: Failed to load database users
loadDatabaseUserError=Αδύνατη φόρτωση χρηστών βάσης δεδομένων
#YMSE: Failed to delete database user
deleteDatabaseUsersError=Αδύνατη διαγραφή χρηστών βάσης δεδομένων
#XMSG: Success message after deleting database user
deleteDatabaseUserSuccess=Ο χρήστης βάσης δεδομένων διαγράφηκε
#XMSG: Success message after deleting database users
deleteDatabaseUsersSuccess=Οι χρήστες βάσης δεδομένων διαγράφηκαν
#XMSG: Success message after creating database user
createDatabaseUserSuccessMessage=Ο χρήστης βάσης δεδομένων δημιουργήθηκε
#YMSE: Failed to create database user
createDatabaseUserError=Αδύνατη δημιουργία χρήστη βάσης δεδομένων
#XMSG: Success message after updating database user
updateDatabaseUserSuccessMessage=Χρήστης βάσης δεδομένων ενημερώθηκε
#YMSE: Failed to update database user
updateDatabaseUserError=Αδύνατη ενημέρωση χρήστη βάσης δεδομένων
#XFLD: HDI Consumption
hdiConsumption=Ανάλωση HDI
#XTIT: Space detail section Database Access title
detailsSectionDatabaseAccess=Πρόσβαση σε Βάση Δεδομένων
#XTXT: Database users consumption hint
databaseUsersConsumptionHint=Προεπιλέξτε την δυνατότητα χρήσης των δεδομένων χώρου σας. Τα μοντέλα στους δημιουργούς θα επιτρέπουν αυτόματα την δυνατότητα χρήσης των δεδομένων.
#XFLD: Default Consumption of Space Data:
defaultConsumptionOfSpaceData=Προεπιλεγμένη Ανάλωση Δεδομένων Χώρου:
#XFLD: Database User Name
databaseUserName=Ονομα Χρήστη Βάσης Δεδομένων
#XMSG: Database User creation validation error message
databaseUserValidationError=Ίσως ορισμένα πεδία είναι άκυρα. Ελέγξτε τα υποχρεωτικά πεδία για να συνεχίσετε.
#XMSG: SQL modeling can’t be enabled since this user has been migrated.
migratedDatabaseUserHint=Η ενσωμάτωση δεδομένων δεν μπορεί να ενεργοποιηθεί εφόσον αυτός ο χρήστης μεταφέρθηκε.
#XBUT: Remove Button Text
remove=Διαγραφή
#XBUT: Remove Spaces Button Text
removeSpaces=Διαγραφή
#XBUT: Remove Objects Button Text
removeObjects=Διαγραφή Αντικειμένων
#XMSG: No members have been added yet.
noMembersAssigned=Δεν προστέθηκαν ακόμα μέλη.
#XMSG: No users have been added yet.
noUsersAssigned=Δεν προστέθηκαν ακόμα χρήστες.
#XMSG: No database users have been created or your filter is not showing any data.
noDatabaseUsers=Δεν δημιουργήθηκαν ακόμα χρήστες βάσης δεδομένων ή τα φίλτρα σας δεν εμφανίζουν δεδομένα.
#XMSG: Please enter a user name.
noDatabaseUsername=Εισάγετε όνομα χρήστη.
#XMSG: The user name is too long. Please use a shorter one.
databaseUsernameTooLong=Το όνομα χρήστη είναι εκτενές. Χρησιμοποιήστε ένα μικρότερο.
#XMSG: No privileges have been enabled and this database user will have limited functionality. Do you still want to continue?
noPrivilegesConfirmationMessage=Δεν ενεργοποιήθηκαν προνόμια, και αυτός ο χρήστης βάσης δεδομένων θα έχει περιορισμένη λειτουργικότητα. Θέλετε να συνεχίσετε;
#XMSG: To enable audit logs for change operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogChangeMessage=Για να ενεργοποιήσετε τα ημερολόγια ελέγχου για λειτουργίες αλλαγής, πρέπει να ενεργοποιήσετε την απορρόφηση δεδομένων. Θέλετε να το κάνετε;
#XMSG: To enable audit logs for read operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogReadMessage=Για να ενεργοποιήσετε τα ημερολόγια ελέγχου για λειτουργίες ανάγνωσης, πρέπει να ενεργοποιήσετε την απορρόφηση δεδομένων. Θέλετε να το κάνετε;
#XMSG: To enable HDI consumption data ingestion and data consumption needs to be enabled. Do you want to enable all?
enableHDIConsumptionMessage=Για να ενεργοποιήσετε την ανάλωση HDI, πρέπει να ενεργοποιήσετε την απορρόφηση και χρήση δεδομένων. Θέλετε να το κάνετε;
#XMSG:
databaseUserPasswordText=Για να διαμορφώσετε μία σύνδεση με αυτόν τον χρήστη βάσης δεδομένων, αντιγράψτε τον κωδικό πρόσβασής σας. Σε περίπτωση που τον ξεχάσατε, μπορείτε πάντα να ζητήσετε έναν νέο.
#XTIT: Space detail section members title
detailsSectionMembers=Μέλη
#XMSG: New password set
newPasswordSet=Νέος κωδικός πρόσβασης καθορίστηκε
#XFLD: Data Ingestion
dataIngestion=Απορρόφηση δεδομένων
#XFLD: Data Consumption
dataConsumption=Χρήση Δεδομένων
#XFLD: Privileges
privileges=Προνόμια
#XFLD: Enable Data ingestion
enableDataIngestion=Ενεργοποίηση Ενσωμάτωσης Δεδομένων
#XMSG: Log the read and change operations for data ingestion.
databaseUserAuditHint=Καταχωρίστε τις λειτουργίες ανάγνωσης και αλλαγής για ενσωμάτωση δεδομένων.
#XMSG: Make your space data available in your HDI containers.
databaseUserHDIHint=Καταστήστε διαθέσιμα τα δεδομένα χώρου σας στα HDI containers.
#XFLD: Enable Data consumption
enableDataConsumption=Ενεργοποίηση Χρήσης Δεδομένων
#XMSG: Allow other apps or tools to consume your space data.
enableDataConsumptionHint=Να επιτρέπεται η χρήση των δεδομένων χώρου σας από άλλες εφαρμογές ή εργαλεία.
#XMSG: To set up access via this schema user copy the credentials to your user-provided service. In case you can only copy the credentials without a password make sure to add the password later.
databaseUserHDIConsumptionHint=Για να διαμορφώσετε την πρόσβαση μέσω του χρήστη βάσης δεδομένων, αντιγράψτε τα διαπιστευτήριά σας στην παρεχόμενη από τον χρήστη σας υπηρεσία. Σε περίπτωση που μπορείτε να αντιγράψετε τα διαπιστευτήρια χωρίς κωδικό πρόσβασης, βεβαιωθείτε ότι θα προσθέσετε αργότερα τον κωδικό πρόσβασής σας.
#XFLD: Data Flow Runtime Capacity label in space overview
dataFlowRuntimeCapacity=Δυναμικότητα Χρόνου Εκτέλεσης Ρής Δεδομένων ({0}:{1} ώρες από {2} ώρες)
#XFLD: Could not load Data Flow Runtime Capacity
dataFlowRuntimeCapacityError=Αδύνατη φόρτωση δυναμικότητας χρόνου εκτέλεσης ροής δεδομένων
#XMSG: User can grant data consumption to other users.
databaseUserConsumptionWithGrantOptionHint=Ο χρήστης μπορεί να χορηγήσει άδεια ανάλωσης δεδομένων σε άλλους χρήστες.
#XFLD: Enable data consumption with grant option
enableConsumptionWithGrantOption=Ενεργοποίηση Ανάλωσης Δεδομένων με Επιλογή Χορήγησης
#XMSG: To enable data consumption with grant option data consumption needs to be enabled. Do you want to enable both?
enableConsumptionWithGrantMessage=Εάν θέλετε να ενεργοποιήσετε την ανάλωση δεδομένων με επιλογή χορήγησης, πρέπει να ενεργοποιήσετε την ανάλωση δεδομένων. Θέλετε να τα ενεργοποιήσετε αμφότερα;
#XFLD: Enable Automated Predictive Library (APL) and Predictive Analysis Library (PAL)
allowUsingScriptServerFunctionality=Ενεργοποίηση της Βιβλιοθήκης Αυτοματοποιημένης Πρόγνωσης (APL) και Βιβλιοθήκη Ανάλυσης Πρόγνωσης (PAL)
#XMSG: User can use SAP HANA Cloud embedded machine learning functions.
allowUsingScriptServerFunctionalityHint=Ο χρήστης μπορεί να χρησιμοποιήσει τις λειτουργίες ενσωματωμένης μηχανικής εκμάθησης του SAP HANA Cloud.
#XFLD: Password Policy
passwordPolicy=Κανόνες Κωδικού Πρόσβασης
#XMSG: Password Policy
passwordPolicyHint=Ενεργοποιήστε ή απενεργοποιήστε τους κανόνες διαμόρφωσης κωδικού πρόσβασης εδώ.
#XFLD: Enable Password Policy
enablePasswordPolicy=Ενεργοποίηση Κανόνων Κωδικού Πρόσβασης
#XMSG: Read Access to the Space Schema
readAccessTitle=Πρόσβαση Ανάγνωσης σε Σχήμα Χώρου
#XMSG: read access hint
readAccessHint=Να επιτρέπεται στον χρήστη βάσης δεδομένων να συνδέσει εξωτερικά εργαλεία με το σχήμα χώρου και να διαβάσει προβολές που εκτίθενται για ανάλωση.
#XFLD: Space Schema
spaceSchema=Σχήμα Χώρου
#XFLD: Enable Read Access (SQL)
enableReadAccess=Ενεργοποίηση Πρόσβασης Ανάγνωσης (SQL)
#XMSG: Allow the user to grant read access to other users.
withGrantOptionHint=Να επιτρέπεται στον χρήστη η παραχώρηση πρόσβασης ανάγνωσης σε άλλους χρήστες.
#XFLD: With Grant Option
withGrantOption=Με Επιλογή Παραχώρησης
#XMSG: Make your space data available in your HDI containers.
enableHDIConsumptionHint=Καταστήστε διαθέσιμα τα δεδομένα χώρου σας στα HDI containers.
#XFLD: Enable HDI Consumption
enableHDIConsumption=Ενεργοποίηση Ανάλωσης HDI
#XMSG: Write Access to the User’s Open SQL Schema
writeAccessTitle=Με Πρόσβαση στο Σχήμα Ανοικτού SQL του Χρήστη
#XMSG: write access hint
writeAccessHint=Να επιτρέπεται στον χρήστη βάσης δεδομένων να συνδέσει εξωτερικά εργαλεία με το σχήμα Ανοικτού SQL για την δημιουργία οντοτήτων δεδομένων και την παροχή δεδομένων για χρήση στον χώρο.
#XFLD: Open SQL Schema
openSQLSchema=Σχήμα Ανοικτού SQL
#XFLD: Enable Write Access (SQL DDL & DML)
enableWriteAccess=Ενεργοποίηση Πρόσβασης Εγγραφής (SQL, DDL, & DML)
#XMSG: audit hint
auditHint=Καταχωρήστε τις λειτουργίες ανάγνωσης και αλλαγής στο σχήμα Ανοικτού SQL.
#XMSG: data consumption hint
dataConsumptionHint=Εκθέστε όλες τις νέες προβολές στον χώρο με προεπιλογή για ανάλωση. Οι μοντελοποιητές μπορούν να αντικαταστήσουν αυτή την ρύθμιση για μεμονωμένες προβολές μέσω του διακόπτη «Εκθεση για Ανάλωση» στον πλαϊνό πίνακα εξόδου προβολής. Μπορείτε, επίσης, να επιλέξετε τις μορφές στις οποίες θα εκτίθενται οι προβολές.
#XFLD: Expose for Consumption by Default
exposeDataConsumption=Εκθεση για Ανάλωση με Προεπιλογή
#XMSG: database users hint consumption hint
databaseUsersHint2New=Δημιουργήστε χρήστες βάσης δεδομένων για να συνδέσετε εξωτερικά εργαλεία με το SAP Datasphere. Καθορίστε δικαιώματα που θα επιτρέπουν στους χρήστες να διαβάσουν τα δεδομένα χώρου και να δημιουργήσουν οντότητες δεδομένων (DDL) καθώς και να παρέχουν δεδομένα (DML) για χρήση στον χώρο.
#XFLD: Read
read=Ανάγνωση
#XFLD: Read (HDI)
readHDI=Ανάγνωση (HDI)
#XFLD: Write
write=Εγγραφή
#XMSG: HDI Containers Hint
HDIContainersHint2=Ενεργοποιήστε την πρόσβαση στους αποδέκτες του SAP HANA Deployment Infrastructure (HDI) στον χώρο σας. Οι μοντελοποιητές μπορούν να χρησιμοποιήσουν τα αντικείμενα HDI ως πηγές για προβολές, και οι εντολείς HDI μπορούν να έχουν πρόσβαση στα δεδομένα του χώρου σας.
#XMSG: Open info dialog
openDatabaseUserInfoDialog=Ανοιγμα διαλόγου πληροφοριών
#XMSG: Database user is locked. Open info dialog to unlock
openLockedDatabaseUserInfoDialog=Ο χρήστης βάσης δεδομένων κλειδώθηκε. Για να τον ξεκλειδώσετε, ανοίξτε τον διάλογο.
#XFLD: Table
table=Πίνακας
#XFLD: Space section partner configuration title
partnerConnectionConfigurationSectionTitle=Σύνδεση Εταίρου
#XFLD: Space section partner configuration subtitle
partnerConnectionConfigurationSectionSubTitle=Διαμόρφωση Σύνδεσης Εταίρου
#XFLD: Space section partner configuration hint
partnerConnectionConfigurationSectionHint=Καθορίστε το δικό σας πλακίδιο σύνδεσης εταίρου προσθέτοντας το iFrame URL και εικονίδιο. Αυτή η διαμόρφωση είναι διαθέσιμη μόνο για αυτόν τον μισθωτή.
#XFLD: Table Name Field
partnerConnectionConfigurationName=Ονομα Πλακιδίου
#XFLD: Table iFrame URL field
partnerConnectionConfigurationiFrameURL=iFrame URL
#XFLD: Table iFrame Post Message Origin field
partnerConnectionConfigurationiFramePostMessageOrigin=Προέλευση Μηνύματος Καταχώρισης iFrame
#XFLD: Table icon/image name
partnerConnectionConfigurationIcon=Εικονίδιο
#XMSG: Table no result found
partnerConnectionConfigurationNotfound=Δεν βρέθηκε διαμόρφωση σύνδεσης εταίρου.
#XMSG: Partner Connection Confgurations are not available
partnerConnectionConfigurationNotAvailable=Αδύνατη εμφάνιση διαμορφώσεων σύνδεσης εταίρου αν δεν υπάρχει βάση δεδομένων χρόνου εκτέλεσης.
#XFLD: Dialog Create Partner Connection Configuration
partnerConnectionConfigurationDialogTitleAdd=Δημιουργία Διαμόρφωσης Σύνδεσης Εταίρου
#XFLD: Dialog Create Partner Connection Configuration Upload Icon
partnerConnectionConfigurationButtonUloadPartnerIcon=Φόρτωση Εικονιδίου
#XFLD: Dialog Create Partner Connection Configuration Select
partnerConnectionConfigurationSelectIcon=Επιλογή (μέγιστο μέγεθος 200KB)
#XFLD: Dialog Create Partner Connection Configuration Enter a name
partnerConnectionConfigurationNamePlaceholder=Παράδειγμα Πλακιδίου Εταίρου
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL
partnerConnectionConfigurationiFrameURLPlaceholder=http://www.example.com
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL Check
partnerConnectionConfigurationiFramePostMessageOriginPlaceholder=.example.com
#XFLD: Dialog Create Partner Connection Configuration Browse
partnerConnectionConfigurationBrowseButton=Αναζήτηση
#XFLD: Create Partner Connection Configuration Msg
partnerConnectionConfigurationUploadMsg=Διαμόρφωση Σύνδεσης Εταίρου δημιουργήθηκε επιτυχώς.
#XMSG: Error while deleting Partner Connection configuration(s)
partnerConnectionConfigurationDeleteError=Σφάλμα κατά τη διαγραφή διαμορφώσεων Σύνδεσης Εταίρου.
#XMSG: Partner Connection Configuration has been successfully deleted
partnerConnectionConfigurationDeleteSuccess=Διαμόρφωση Σύνδεσης Εταίρου διαγράφηκε επιτυχώς.
#XMSG: Error while retrieving Partner Connection Configurations
partnerConnectionConfigurationLoadError=Σφάλμα κατά την ανάκτηση Διαμορφώσεων Σύνδεσης Εταίρου.
#XMSG: Error fileSizeLimitExceeded
partnerConnectionConfigurationfileSizeLimitExceeded=Το αρχείο δεν μπόρεσε να φορτωθεί γιατί υπερβαίνει το μέγιστο μέγεθος του 200KB.
#XBUT: Create Partner Connection Configuration
partnerConnectionConfigurationButtonCreate=Δημιουργία Διαμόρφωσης Σύνδεσης Εταίρου
#XBUT: Delete Partner Connection Configuration
partnerConnectionConfigurationButtonDelete=Διαγραφή Διαμόρφωσης Σύνδεσης Εταίρου.
#XMSG: Partner Tile Creation Failure
upsertPartnerTileFailed=Πλακίδιο Εταίρου δεν δημιουργήθηκε.
#XMSG: Partner Tile Deletion Failure
deletePartnerTileFailed=Πλακίδιο Εταίρου δεν διαγράφηκε.
#XMSG: HANA cloud connector settings reset failure
resetHANASCCSettingsFailed=Επαναφορά Ρυθμίσεων SAP HANA Cloud Connector Πελάτη απέτυχε
#XFLD: Workload Class
workloadClass=Κατηγορία Φόρτου Εργασίας
#XFLD: Workload Management
workloadManagement=Διαχείριση Φόρτου Εργασίας
#XFLD: Priority
workloadClassPriority=Προτεραιότητα
#XMSG:
workloadManagementPriorityHint=Μπορείτε να καθορίσετε την προτεραιότητα του χώρου όταν αναζητάτε την βάση δεδομένων. Εισάγετε μία τιμή από 1 (κατώτατη προτεραιότητα) σε 8 (ανώτατη). Στην περίπτωση που οι χώροι ανταγωνίζονται για διαφορετικά νήματα, οι χώροι με ανώτατη προτεραιότητα εκτελούνται πριν από αυτούς με την χαμηλότερη.
#XMSG:
workloadClassPriorityHint=Μπορείτε να καθορίσετε την προτεραιότητα του χώρου από 0 (κατώτατη) σε 8 (ανώτατη). Οι προτάσεις ενός χώρου με υψηλή προτεραιότητα εκτελούνται πριν τις προτάσεις άλλων χώρων με χαμηλότερη προτεραιότητα. Η προεπιλεγμένη προτεραιότητα είναι 5. Καθώς η τιμή 9 είναι δεσμευμένη στις λειτουργίες συστήματος δεν είναι διαθέσιμη για έναν χώρο.
#XFLD: Statement Limits
workloadclassStatementLimits=Ορια Προτάσεων
#XFLD: Workload Configuration
workloadConfiguration=Διαμόρφωση Φόρτου Εργασίας
#XMSG:
workloadClassStatementLimitsHint=Μπορείτε να καθορίσετε μέγιστο αριθμό ή ποσοστό νημάτων και GB μνήμης που μπορούν να χρησιμοποιήσουν οι προτάσεις που εκτελούνται ταυτόχρονα στον χώρο. Μπορείτε να εισάγετε μία σταθερή τιμή μνήμης ή νημάτων, ή ένα ποσοστό. \n\n Εάν καθορίσετε ένα όριο νημάτων, αυτό μπορεί να επιδεινώσει την απόδοση. \n\n Εάν καθορίσετε ένα όριο μνήμης, οι προτάσεις που αγγίζουν το όριο μνήμης δεν θα εκτελούνται.
#XMSG:
workloadClassStatementLimitsDescription=Η προεπιλεγμένη διαμόρφωση παρέχει γενικά όρια πόρων ενώ δεν επιτρέπει την υπερφόρτωση του συστήματος από μεμονωμένους χώρους.
#XMSG:
workloadClassStatementLimitCustomDescription=Μπορείτε να καθορίσετε μέγιστο συνολικά όρια μνήμης και νήματος που μπορούν να καταναλώσουν οι προτάσεις που εκτελούνται ταυτόχρονα σε έναν χώρο.
#XMSG:
totalStatementThreadLimitHelpText=Καθορίζοντας το όριο νήματος σε πολύ χαμηλό μπορεί να επηρεαστεί η απόδοση πρότασης, ενώ οι υπερβολικά υψηλές τιμές ή 0 μπορεί να επιτρέψουν στον χώρο να καταναλώσει όλα τα διαθέσιμα νήματα συστήματος.
#XMSG:
totalStatementMemoryLimitHelpText=Καθορίζοντας το όριο μνήμης σε πολύ χαμηλό μπορεί να προκληθούν ζητήματα εξάντλησης χώρου μνήμης, ενώ οι υπερβολικά υψηλές τιμές ή 0 μπορεί να επιτρέψουν στον χώρο να καταναλώσει όλες τις διαθέσιμες μνήμες συστήματος.
#XMSG:
totalStatementThreadLimitHelpTextRestricted=Εισάγετε ένα ποσοστό από 1% ως 70% (ή ισοδύναμο αριθμό) του συνολικού αριθμού νημάτων που είναι διαθέσιμα στον μισθωτή σας. Ο καθορισμός ενός χαμηλού ορίου νήματος ίσως επηρεάσει την απόδοση της δήλωσης, ενώ οι υπερβολικά υψηλές τιμές την απόδοση των δηλώσεων σε άλλους χώρους.
#XMSG:
totalStatementThreadLimitHelpTextDynamic=Εισάγετε ένα ποσοστό από 1% ως {0}% (ή ισοδύναμο αριθμό) του συνολικού αριθμού νημάτων που είναι διαθέσιμα στον μισθωτή σας. Ο καθορισμός ενός χαμηλού ορίου νήματος ίσως επηρεάσει την απόδοση της δήλωσης, ενώ οι υπερβολικά υψηλές τιμές την απόδοση των δηλώσεων σε άλλους χώρους.
#XMSG:
totalStatementMemoryLimitHelpTextNew=Εισάγετε ένα ποσοστό από 0 (χωρίς όριο) έως το συνολικό ποσό της διαθέσιμης μνήμης στον μισθωτή σας. Ο καθορισμός ενός χαμηλού ορίου νήματος ίσως επηρεάσει την απόδοση της δήλωσης, ενώ οι υπερβολικά υψηλές τιμές την απόδοση των δηλώσεων σε άλλους χώρους.
#XFLD: Total Statement Thread Limit
totalStatementThreadLimit=Συνολικό Οριο Νημάτων Πρότασης
#XFLD: Segmented button label
workloadclassPercentage=%
#XFLD: Segmented button label
workloadclassGigaByte=GB
#XFLD, 80: Segmented button label
workloadclassThreads=Νήματα
#XFLD: Total Statement Memory Limit
totalStatementMemoryLimit=Συνολικό Οριο Μνήμης Προτάσεων
#YMSE: Error while loading customer hana info
loadCustomerHanaInfoError=Αδύνατη φόρτωση Πληροφοριών SAP HANA πελατών.
#XMSG:
minimumLimitReached=Ελάχιστο όριο επιτεύχθηκε.
#XMSG:
maximumLimitReached=Μέγιστο όριο επιτεύχθηκε.
#XMSG: Name Taken for Technical Name
technical-name-taken=Μία σύνδεση με το τεχνικό όνομα που εισαγάγατε υπάρχει ήδη. Εισάγετε άλλο όνομα.
#XMSG: Name Too long for Technical Name
technical-name-too-long=Το τεχνικό όνομα που εισαγάγατε υπερβαίνει τους 40 χαρακτήρες. Εισάγετε ένα όνομα με λιγότερους χαρακτήρες.
#XMSG: Technical name field empty
technical-name-field-empty=Εισάγετε ένα τεχνικό όνομα.
#XMSG: Generic error when user enters invalid characters for a field
field-invalid-characters=Μπορείτε να χρησιμοποιήσετε τα γράμματα (a-z), αριθμούς (0-9) και κάτω παύλες (_) για το όνομα.
#XMSG: Generic error when user enters invalid initial characters for a field
field-underscore-trim=Το όνομα που εισαγάγατε δεν πρέπει να ξεκινάει ή να καταλήγει σε κάτω παύλα (_).
#XFLD: Checkbox label for Enable Statement Limits
enableStatementLimits=Ενεργοποίηση Ορίων Δήλωσης
#XFLD: Connection SubSection Settings Title in space details
connectionSettings=Ρυθμίσεις
#XMSG: Connections tool hint in Space details section
connectionsToolHint=Για να δημιουργήσετε ή να επεξεργαστείτε συνδέσεις, ανοίξτε την εφαρμογή Συνδέσεις από την πλαϊνή πλοήγηση ή πατήστε εδώ:
#XBUT: Connection list Open Connection Tool Button
button-connectiontool=Ανοίξτε τις Συνδέσεις
#XFLD: Not deployed label on space tile
notDeployedLabel=Ο χώρος δεν αναπτύχθηκε ακόμα.
#XFLD: Not deployed additional text on space tile
notDeployedText=Αναπτύξτε τον χώρο.
#XFLD: Corrupt space label on space tile
corruptSpace=Κάτι πήγε λάθος.
#XFLD: Corrupt space additional text on space tile
corruptSpaceHint=Προσπαθήστε να αναπτύξετε ξανά ή επικοινωνήστε με τμήμα υποστήριξης
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Δεδομένα Ημερολογίου Ελέγχου
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Διαχειριστ.Δεδομένα
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Αλλα Δεδ.
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Δεδομένα σε Χώρους
#XMSG: Unlock Space confirmation text
unlockSpaceConfirmation=Θέλετε να ξεκλειδώσετε τον χώρο;
#XMSG: Lock Space confirmation text
lockSpaceConfirmation=Θέλετε να ξεκλειδώσετε τον χώρο;
#XFLD: Lock
lock=Κλείδωμα
#XFLD: Unlock
unlock=Ξεκλείδωμα
#XFLD: Locking
locking=Κλείδωμα
#XMSG: Success message after locking space
lockSpaceSuccessMsg=Χώρος κλειδωμένος
#XMSG: Success message after unlocking space
unlockSpaceSuccessMsg=Χώρος ξεκλειδωμένος
#XMSG: Success message after locking spaces
lockSpacesSuccessMsg=Χώροι κλειδωμένοι
#XMSG: Success message after unlocking spaces
unlockSpacesSuccessMsg=Χώροι ξεκλειδωμένοι
#YMSE: Error while locking a space
lockSpaceError=Ο χώρος δεν μπορεί να κλειδωθεί.
#YMSE: Error while unlocking a space
unlockSpaceError=Ο χώρος δεν μπορεί να ξεκλειδωθεί.
#XTIT: popup title Warning
confirmationWarningTitle=Προειδοποίηση
#XMSG: Message strip message when a space was locked manually
manualLockedMessage=Ο χώρος κλειδώθηκε μη αυτόματα.
#XMSG: Message strip message when a space was locked due to the amount of generated audit logs
auditLogLockedMessage=Ο χώρος κλειδώθηκε από το σύστημα γιατί τα ημερολόγια ελέγχου χρησιμοποιούν μεγάλη ποσότητα GB του δίσκου.
#XMSG: Message strip message when a space was locked because the assigned quota was exceeded.
quotaLockedMessage=Ο χώρος κλειδώθηκε από το σύστημα γιατί υπερβαίνει τις κατανομές αποθήκευσης μνήμης ή δίσκου.
#XMSG: Unlock Spaces confirmation text
unlockSpacesConfirmation=Θέλετε να ξεκλειδώσετε τους επιλεγμένους χώρους;
#XMSG: Lock Spaces confirmation text
lockSpacesConfirmation=Θέλετε να κλειδώσετε τους επιλεγμένους χώρους;
#XTIT: Scoped role editor dialog title
scopedRoleEditorDialogTitle=Επεξεργαστής Ρόλου
#XTIT: ECN Management title
ecnManagementTitle=Διαχείριση Κόμβων Ελαστικού Υπολογισμού και Χώρων
#XFLD: ECNs
ecns=Κόμβοι Ελαστικού Υπολογισμού
#XFLD: ECN phase Ready
ecnReady=Ετοιμο
#XFLD: ECN phase Running
ecnRunning=Εκτελείται
#XFLD: ECN phase Initial
ecnInitial=Μη Ετοιμο
#XFLD: ECN phase Starting
ecnStarting=Εναρξη
#XFLD: ECN phase Starting Failed
ecnStartingFailed=Εναρξη Απέτυχε
#XFLD: ECN phase Stopping
ecnStopping=Διακόπηκε
#XFLD: ECN phase Stopping Failed
ecnStoppingFailed=Διακοπή Απέτυχε
#XBTN: Assign Button
assign=Αντιστοίχιση Χώρων
#XBTN: Start Header-Button
start=Εναρξη
#XBTN: Update Header-Button
repair=Ενημέρωση
#XBTN: Stop Header-Button
stop=Διακοπή
#XFLD: ECN hours remaining
ecnHoursRemaining=1000 ώρες απομένουν
#XFLD: ECN block-hours remaining
ecnBlockHoursRemaining={0} Υπόλοιπη Ωρα Δέσμευσης
#XFLD: ECN block-hour remaining
ecnBlockHourRemaining={0} Υπόλοιπες Ωρες Δέσμευσης
#XTIT: ECN creation dialog title
ecnCreationDialogTitle=Δημιουργία Κόμβου Ελαστικού Υπολογισμού
#XTIT: ECN edit dialog title
ecnEditDialogTitle=Επεξεργασία Κόμβου Ελαστικού Υπολογισμού
#XTIT: ECN deletion dialog title
ecnDeletionDialogTitle=Διαγραφή Κόμβου Ελαστικού Υπολογισμού
#XTIT: ECN Space assignment dialog title
ecnSpaceAssignmentDialogTitle=Αντιστοίχιση Χώρων
#XFLD: ECN ID
ECNIDLabel=Κόμβος Ελαστικού Υπολογισμού
#XTXT: Selected toolbar text
selectedToolbarText=Επιλεγμένο: {0}
#XTIT: Elastic Compute Nodes
ECNslong=Κόμβοι Ελαστικού Υπολογισμού
#XTIT: Object assignment - Table header: Objects available
objectsAvailable=Αριθμός Αντικειμένων
#XTIT: Object assignment - Dialog header text
selectObjects=Επιλέξτε τους χώρους και τα αντικείμενα που θέλετε να αντιστοιχίσετε στον κόμβο ελαστικού υπολογισμού:
#XTIT: Object assignment - Table header title: Objects
objects=Αντικείμενα
#XTIT: Object assignment - Table header: Type
type=Τύπος
#XMSG: Message strip delete database user audit warning
deleteDatabaseUserWarningMessageStrip=Αν διαγράψετε έναν χρήστη βάσης δεδομένων θα διαγραφούν όλες οι δημιουργημένες καταχωρίσεις του ημερολογίου ελέγχου. Αν θέλετε να διατηρήσετε τα ημερολόγια ελέγχου, εξάγετέ τα πριν διαγράψετε τον χρήστη της βάσης δεδομένων.
#XMSG: Message strip delete HDI container audit logs impact
deleteHDIContainerWarningMessageStrip=Αν απενεργοποιήσετε ένα HDI container από τον χώρο θα διαγραφούν όλες οι καταχωρίσεις του ημερολογίου ελέγχου. Αν θέλετε να τις διατηρήσετε, εξάγετέτε τις πριν ακυρώσετε την αντιστοίχιση του HDI container.
#XTXT: All audit logs
allAuditLogs=Ολες οι καταχωρίσεις του ημερολογίου ελέγχου δημιουργήθηκαν για τον χώρο
#XMSG: Message strip disable audit warning
disableAuditWarningMessageStrip=Αν απενεργοποιήσετε μία πολιτική ελέγχου (λειτουργίες ανάγνωσης ή αλλαγής) θα διαγραφούν όλες οι καταχωρίσεις του ημερολογίου ελέγχου. Αν θέλετε να τις διατηρήσετε, εξάγετέτε τις πριν απενεργοποιήσετε την πολιτική.
#XTIT: No Spaces assigned to ECN Illustration title
noSpacesECNTitle=Δεν αντιστοιχίστηκαν χώροι ή αντικείμενα ακόμα
#XTIT: No Spaces assigned to ECN Illustration description
noSpacesECNDescription=Για να ξεκινήσετε να εργάζεστε με τον ελαστικό κόμβο υπολογισμού, αντιστοιχίστε έναν χώρο ή αντικείμενα σε αυτόν.
#XTIT: No Spaces Illustration title
noSpacesTitle=Δεν δημιουργήθηκε χώρος ακόμα
#XTIT: No Spaces Illustration description
noSpacesDescription=Για να αποκτήσετε δεδομένα, δημιουργήστε πρώτα έναν χώρο.
#XTIT: No Spaces Illustration title
noDeletedSpacesTitle=Ο κάδος ανακύκλωσης είναι κενός
#XTIT: No Spaces Illustration description
noDeletedSpacesDescription=Μπορείτε να ανακτήσετε τους διαγραμμένους χώρους από εδώ.
#XTXT:  {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDatabaseUsers=Όταν αναπτυχθεί ο χώρος, οι παρακάτω χρήστες βάσης δεδομένων θα διαγραφούν {0} και δεν θα μπορούν να ανακτηθούν:
#XTIT: Delete database users
deleteDatabaseUsersTitle=Διαγραφή Χρηστών Βάσης Δεδομένων
#YMSE: Error if ecn id already exists
ecnIDInputDuplicateError=ID υπάρχει ήδη.
#YMSE: Error if ecn id contains invalid characters
ecnIDInputInvalidError=Χρήση πεζών χαρακτήρων α-ω και 0-9 μόνο
#YMSE: Error if ecn id is too short
ecnIDInputTooShortError=Το ID πρέπει να έχει τουλάχιστον {0} χαρακτήρες.
#XMSG: ecn id length warning
ecnIdLengthWarning=Μέγιστος αριθμός {0} χαρακτήρων έχει καλυφθεί.
#XFLD: open System Monitor
systemMonitor=Παρακολούθηση Συστήματος
#XFLD: open ECN schedule dialog menu entry
schedule=Χρονοδ/μα
#XFLD: open create ECN schedule dialog
createSchedule=Δημιουργία Προγράμματος
#XFLD: open change ECN schedule dialog
changeSchedule=Επεξεργασία Προγράμματος
#XFLD: open delete ECN schedule dialog
deleteSchedule=Διαγραφή Προγράμματος
#XFLD: open self assign ECN schedule dialog
selfAssignSchedule=Αντιστοίχιση Προγράμματος σε Εμένα
#XFLD: open pause ECN schedule dialog
pauseSchedule=Διακοπή προγράμματος
#XFLD: open resume ECN schedule dialog
resumeSchedule=Επανέναρξη Προγράμματος
#XFLD: View Logs
viewLogs=ΠροβΗμερλ
#XFLD: Compute Blocks
computeBlocks=Δεσμεύσεις Υπολογισμού
#XFLD: Memory label in ECN creation dialog
ecnMemory=Μνήμη (GB)
#XFLD: Storage label in ECN creation dialog
ecnStorage=Αποθήκευση (GB)
#XFLD: Number of CPU label in ECN creation dialog
ecnCPUCount=Αριθμός CPU
#XFLD: ECN updated by label
changedBy=Αλλαγμένο Από
#XFLD: ECN updated on label
changedOn=Αλλαγή Στις
#XMSG: Success message after creating a Elastic Compute Node
createEcnSuccessMessage=Δημιουργημένος κόμβος ελαστικού υπολογισμού
#YMSE: Error while creating a Elastic Compute Node
createEcnError=Αδύνατη δημιουργία κόμβου ελαστικού υπολογισμού
#XMSG: Success message after updating a Elastic Compute Node
updateEcnSuccessMessage=Κόμβος ελαστικού υπολογισμού ενημερωμένος
#XMSG: Error while updating a Elastic Compute Node
udpateEcnError=Κόμβος ελαστικού υπολογισμού δεν ενημερώθηκε
#XMSG: Success message after deleting a Elastic Compute Node
deleteEcnSuccessMessage=Διαγραμμένος κόμβος ελαστικού υπολογισμού
#YMSE: Error while deleting a Elastic Compute Node
deleteEcnError=Αδύνατη διαγραφή κόμβου ελαστικού υπολογισμού
#XMSG: Success message after starting a Elastic Compute Node
startingEcnSuccessMessage=Έναρξη Κόμβου Ελαστικού Υπολογισμού
#XMSG: Success message after stopping a Elastic Compute Node
stoppingEcnSuccessMessage=Διακοπή Κόμβου Ελαστικού Υπολογισμού
#YMSE: Error while starting a Elastic Compute Node
startingEcnError=Αδύνατη έναρξη κόμβου ελαστικού υπολογισμού
#YMSE: Error while stopping a Elastic Compute Node
stoppingEcnError=Αδύνατη διακοπή κόμβου ελαστικού υπολογισμού
#XBUT: Add Object button for an ECN
assignObjects=Προσθήκη Αντικειμένων
#XFLD: Enable auto object assignment label
enableAutoObjAssignment=Προσθήκη όλων των αντικειμένων αυτόματα
#XFLD: object type label to be assigned
objectTypeLabel=Τύπος (Σημασιολογική Χρήση)
#XFLD: assigned object type label
assignedObjectTypeLabel=Τύπος
#XFLD: technical name label
TechnicalNameLabel=Τεχνικό Ονομα
#XFLD: Object selecttion ECN label
objectSelectionECNLabel=Επιλέξτε τα αντικείμενα που θέλετε να προσθέσετε στον κόμβο ελαστικού υπολογισμού
#XTIT: Add objects dialog title
assignObjectsTitle=Αντιστοίχιση Αντικειμένων
#XFLD: object label with object count
objectLabel=Αντικείμενο
#XMSG: No objects available to add message.
noObjectsToAssign=Δεν υπάρχουν αντικείμενα για αντιστοίχιση.
#XMSG: No objects assigned message.
noAssignedObjects=Δεν αντιστοιχίστηκαν αντικείμενα.
#XTIT: Remove objects popup title
removeObjectsOrSpacesConfirmationTitle=Προειδοποίηση
#XTIT: Delete ECN popup button and title
deleteECNConfirmationButtonAndTitle=Διαγραφή
#XMSG: Remove objects popup text
removeObjectsConfirmation=Θέλετε να διαγράψετε τα επιλεγμένα αντικείμενα;
#XMSG: Remove spaces popup text
removeSpacesConfirmation=Θέλετε να κλειδώσετε τους επιλεγμένους χώρους;
#XBUT: Confirmation button for objects dialog
removeObjectsOrSpacesConfirmationBtn=Διαγραφή
#XMSG: Success message after removing objects
removeObjectsSuccessMessage=Εκτεθειμένα αντικείμενα διαγράφηκαν
#XMSG: Success message after assigning objects
assignObjectsSuccessMessage=Εκτεθειμένα αντικείμενα αντιστοιχίστηκαν
#XFLD: auto object assignment label for space tile
autoObjAssignmentLabel=Ολα τα Εκτεθειμένα Αντικείμενα
#XFLD: Spaces tab label
spacesTabLabel=Χώροι
#XFLD: Exposed objects tab label
consumptionObjectsTabLabel=Εκτεθειμένα Αντικείμενα
#XMSG: Success message after unassigning spaces
spacesUnassignedSuccessMessage=Οι χώροι διαγράφηκαν
#XMSG: Success message after unassigning space
spaceUnassignedSuccessMessage=Ο χώρος διαγράφηκε
#YMSE: Error while assigning / unassigning spaces
spacesAssignUnassignError=Αδύνατη αντιστοίχιση ή διαγραφή χώρων.
#YMSE: Error while removing objects
removeObjectsError=Δεν μπορέσαμε να αντιστοιχίσουμε ή διαγράψουμε τα αντικείμενα.
#YMSE: Error while removing object
removeObjectError=Δεν μπορέσαμε να αντιστοιχίσουμε ή διαγράψουμε το αντικείμενο.
#YMSE: Error when saving the ecn with invalid value
invalidSizingPlanError=Ο αριθμός που επιλέξατε δεν ισχύει πια. Επιλέξτε έναν έγκυρο αριθμό.
#YMSE: Error when saving the ecn with invalid value
invalidPerformanceClassError=Επιλέξτε έγκυρη κατηγορία απόδοσης.
#YMSE: Error when saving the ecn with invalid value
performanceClassNotAvailableError=Η ήδη επιλεγμένη κατηγορία απόδοσης "{0}" δεν ισχύει. Επιλέξτε έγκυρη κατηγορία απόδοσης.
#XTXT: Delete ECN confirmation text
deleteConfirmationTxtECN=Θέλετε να διαγράψετε τον κόμβο ελαστικού υπολογισμού;
#XFLD: tooltip for ? button
help=Βοήθεια
#XFLD: ECN edit button label
editECN=Διαμόρφωση
#XFLD: Technical type label for ERModel
DWC_ERMODEL=Οντότητα - Μοντέλο Σχέσης
#XFLD: Technical type label for Local Table
DWC_LOCAL_TABLE=Τοπικός Πίνακας
#XFLD: Technical type label for Remote Table
DWC_REMOTE_TABLE=Απομακρυσμένος Πίνακας
#XFLD: Technical type label for Analytic Model
DWC_ANALYTIC_MODEL=Αναλυτικό Μοντέλο
#XFLD: Technical type label for Task Chain
DWC_TASKCHAIN=Αλυσίδα Εργασιών
#XFLD: Technical type label for Data Flow
DWC_DATAFLOW=Ροή Δεδομένων
#XFLD: Technical type label for Replication Flow
DWC_REPLICATIONFLOW=Ροή Αντιγραφής
#XFLD: Technical type label for Transformation Flow
DWC_TRANSFORMATIONFLOW=Ροή Μετασχηματισμού
#XFLD: Technical type label for Intelligent Lookup
DWC_IDT=Έξυπνη Αναζήτηση
#XFLD: Technical type label for Repository
DWC_REPOSITORY_PACKAGE=Αποθήκη
#XFLD: Technical type label for Enterprise Search
DWC_ESMODEL=Enterprise Search
#XFLD: Technical type label for View
DWC_VIEW=Προβολή
#XFLD: Technical type label for Data Product
DWC_DATAPRODUCT=Προϊόν Δεδομένων
#XFLD: Technical type label for Data Access Control
DWC_DAC=Έλεγχος Πρόσβασης Δεδομένων
#XFLD: Technical type label for Folder
DWC_FOLDER=Φάκελος
#XFLD: Technical type label for Data Entity
DWC_BUSINESS_ENTITY=Επιχειρηματική Οντότητα
#XFLD: Technical type label for Business Entity Variant
DWC_BUSINESS_ENTITY_VARIANT=Μεταβλητή Επιχειρηματικής Οντότητας
#XFLD: Technical type label for Responsibility Scenario
DWC_AUTH_SCENARIO=Σενάριο Αρμοδιοτήτων
#XFLD: Technical type label for Fact Model
DWC_FACT_MODEL=Μοντέλο Πληροφοριών
#XFLD: Technical type label for Transformation Flow
DWC_PERSPECTIVE=Προοπτική
#XFLD: Technical type label for Consumption Model
DWC_CONSUMPTION_MODEL=Μοντέλο Ανάλωσης
#XFLD: Technical type label for Remote Connection
DWC_REMOTECONNECTION=Απομακρυσμένη Σύνδεση
#XFLD: Technical type label for Fact Model Variant
DWC_FACT_MODEL_VARIANT=Μεταβλητή Μοντέλου Πληροφοριών
#XMSG: Schedule created alert message
createScheduleSuccess=Πρόγραμμα δημιουργήθηκε
#XMSG: Schedule updated alert message
updateScheduleSuccess=Πρόγραμμα ενημερώθηκε
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Το πρόγραμμα διαγράφηκε
#XMSG: Schedule takeover success message
ownerChangeScheduleSuccess=Το πρόγραμμα αντιστοιχίστηκε σε εσάς
#XMSG: Message for starting pausing of schedule
pauseScheduleStarted=Διακοπή 1 προγράμματος
#XMSG: Message for starting resuming of schedule
resumeScheduleStarted=Επανεκκίνηση 1 προγράμματος
#XFLD: Segmented button label
availableSpacesButton=Διαθέσιμο
#XFLD: Segmented button label
selectedSpacesButton=Επιλεγμένο
#XFLD: Visit website button text
visitWebsite=Επίσκεψη Ιστοσελίδας
#XMSG: Warning message on unselecting source language
sourceLanguageRemovalWarning=Η πρόσφατα επιλεγμένη γλώσσα πηγή θα διαγραφεί.
#XBUT: enable translation confirmation button
enableTranslationConfirmationBtn=Ενεργοποίηση
#XFLD: ECN performance class label
performanceClassLabel=Κατηγορία Απόδοσης
#XTXT performance class memory text
memoryText=Μνήμη
#XTXT performance class compute text
computeText=Υπολογισμός
#XTXT performance class high-compute text
highComputeText=Υψηλός Υπολογισμός
#XBUT: Recycle Bin Button Text
recycleBin=Κάδος Ανακύκλωσης
#XBUT: Restore Button Text
restore=Επαναφορά
#XMSG: Warning message for new Workload Management UI
priorityWarning=Αυτή η περιοχή είναι μόνο για ανάγνωση. Μπορείτε να αλλάξετε την προτεραιότητα χώρων στην περιοχή Σύστημα/Διαμόρφωση/Διαχείριση Φόρτου Εργασίας.
#XMSG: Warning message for new Workload Management UI
workloadWarning=Αυτή η περιοχή είναι μόνο για ανάγνωση. Μπορείτε να αλλάξετε την διαμόρφωση χώρων στην περιοχή Σύστημα/Διαμόρφωση/Διαχείριση Φόρτου Εργασίας.
#XCOL: Space table-view column: Apache Spark vCPUs
sparkManagementVCPUs=Apache Spark vCPUs
#XCOL: Space table-view column: Apache Spark Memory (GB)
sparkManagementMemory=Μνήμη Apache Spark (GB)
#XCOL: Space type (from Space Capability) for Data Ingestion Spaces
ingestionSpaceType=Μεταφορά Προϊόντος Δεδομένων
#XFLD: Deployment in progress nodatatext for spark table
sparkTableDeploymentInProgress=Δεν υπάρχουν δεδομένα γιατί ο χώρος ήδη αναπτύσσεται
#XFLD: Runtime data being loaded nodatatext for spark table
sparkTableDeploymentLoading=Δεν υπάρχουν δεδομένα γιατί ο χώρος ήδη φορτώνεται
#XCOL: Space type (from Space Capability) for BW Private Cloud Edition
BWPCESpaceType=SAP BW
#XBUT: Go to Instance Mappings Button
navToMappingsButton=Επεξεργασία Αντιστοιχίσεων Παρουσίας
#XFLD: SAP HANA Data Lake Storage label if less than 1GB
dataLakeStorageUsed=< 1 GB
