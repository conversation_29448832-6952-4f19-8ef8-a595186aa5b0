#DO NOT REMOVE________________________
#XFLD: Breadcrumb for space monitoring page
spaceMonitorBreadcrumb=Моніторинг
#XTXT: Type name for spaces in browser tab page title
space=Простір
#_____________________________________
#XFLD: Spaces label in
spaces=Простори
#XFLD: Manage plan button text
manageQuotaButtonText=Керування планом
#XBUT: Manage resources button
manageResourcesButton=Керування ресурсами
#XFLD: Create space button tooltip
createSpace=Створити простір
#XFLD: Create
create=Створити
#XFLD: Deploy
deploy=Розгорнути
#XFLD: Page
page=Сторінка
#XFLD: Cancel
cancel=Скасувати
#XFLD: Update
update=Оновити
#XFLD: Save
save=Зберегти
#XFLD: OK
ok=OK
#XFLD: days
days=Дні
#XFLD: Space tile edit button label
edit=Редагувати
#XFLD: Auto Assign all objects to space
autoAssign=Присвоїти автоматично
#XFLD: Space tile open monitoring button label
openMonitoring=Монітор
#XFLD: Delete
delete=Видалити
#XFLD: Copy Space
copy=Копіювати
#XFLD: Close
close=Закрити
#XCOL: Space table-view column status
status=Статус
#XFLD: Space status active
activeLabel=Активний
#XFLD: Space status locked
lockedLabel=Заблоковано
#XFLD: Space status critical
criticalLabel=Критичний
#XFLD: Space status cold
coldLabel=Холодний
#XFLD: Space status deleted
deletedLabel=Видалено
#XFLD: Space status unknown
unknownLabel=Невідомо
#XFLD: Space status ok
okLabel=OK
#XFLD: Database user expired
expired=Прострочено
#XFLD: deployed
deployed=Розгорнено
#XFLD: not deployed
notDeployed=Не розгорнуто
#XFLD: changes to deploy
changesToDeploy=Зміни до розгортання
#XFLD: pending
pending=Розгортання
#XFLD: designtime error
designtimeError=Помилка моделювання
#XFLD: runtime error
runtimeError=Помилка часу виконання
#XFLD: Space created by label
createdBy=Автор створення
#XFLD: Space created on label
createdOn=Дата створення
#XFLD: Space deployed on label
deployedOn=Дата розгортання
#XFLD: Space ID label
spaceID=Ідентифікатор простору
#XFLD: Priority label
priority=Пріоритет
#XFLD: Space Priority label
spacePriority=Пріоритет простору
#XFLD: Space Configuration label
spaceConfiguration=Конфігурація простору
#XFLD: Not available
notAvailable=Недоступно
#XFLD: WorkloadType default
default=Усталений
#XFLD: WorkloadType custom
custom=Користувацький
#XFLD: Data Lake option label; refers to component SAP HANA Cloud, Data Lake
dataLakeAccess=Доступ до озера даних
#XFLD: Translation label
translationLabel=Переклад
#XFLD: Source language label
sourceLanguageLabel=Вихідна мова
#XFLD: Translation CheckBox label
translationCheckBox=Увімкнути переклад
#XTXT: Popover hint that space has to be deployed to show user details
openDatabaseUserHintPopoverText=Розгортання простору, щоб отримати доступ до відомостей про користувача.
#XTXT: Popover hint that space has to be deployed to open database explorer
openDatabaseExplorerHintPopoverText=Розгортання простору, щоб відкрити провідник баз даних.
#XTXT: Text for when the Data Lake check box is disabled
dataLakeOptionDisabled=Цей простір не можна використовувати для доступу до озера даних, оскільки його вже використовує інший простір.
#XFLD: Data Lake check box text; refers to component SAP HANA Cloud, Data Lake
dataLakeCheckBox=Використовуйте цей простір для доступу до озера даних.
#XFLD: Space Priority minimum label extension
low=низький
#XFLD: Space Priority maximum label extension
high=високий
#XFLD: Space name label
spaceName=Ім’я простору
#XFLD: Enable deploy objects checkbox
enableDeployObjects=Розгорнути об'єкти
#XTIT: Copy spaces dialog title
copySpaceDialogTitle=Копіювання "{0}"
#XTXT Human readable text for unselected source language
unSelectedSourceLanguage=(Не вибрано)
#XTXT Human readable text for language code "af"
af=африкаанс
#XTXT Human readable text for language code "ar"
ar=арабська
#XTXT Human readable text for language code "bg"
bg=болгарська
#XTXT Human readable text for language code "ca"
ca=каталонська
#XTXT Human readable text for language code "zh"
zh=китайська (спрощене письмо)
#XTXT Human readable text for language code "zf"
zf=китайська
#XTXT Human readable text for language code "hr"
hr=хорватська
#XTXT Human readable text for language code "cs"
cs=чеська
#XTXT Human readable text for language code "cy"
cy=валлійська
#XTXT Human readable text for language code "da"
da=данська
#XTXT Human readable text for language code "nl"
nl=нідерландська
#XTXT Human readable text for language code "en-UK"
en-UK=англійська (Сполучене Королівство)
#XTXT Human readable text for language code "en"
en=англійська (Сполучені Штати Америки)
#XTXT Human readable text for language code "et"
et=естонська
#XTXT Human readable text for language code "fa"
fa=перська
#XTXT Human readable text for language code "fi"
fi=фінська
#XTXT Human readable text for language code "fr-CA"
fr-CA=французька (Канада)
#XTXT Human readable text for language code "fr"
fr=французька
#XTXT Human readable text for language code "de"
de=німецька
#XTXT Human readable text for language code "el"
el=грецька
#XTXT Human readable text for language code "he"
he=іврит
#XTXT Human readable text for language code "hi"
hi=гінді
#XTXT Human readable text for language code "hu"
hu=угорська
#XTXT Human readable text for language code "is"
is=ісландська
#XTXT Human readable text for language code "id"
id=індонезійська
#XTXT Human readable text for language code "it"
it=італійська
#XTXT Human readable text for language code "ja"
ja=японська
#XTXT Human readable text for language code "kk"
kk=казахська
#XTXT Human readable text for language code "ko"
ko=корейська
#XTXT Human readable text for language code "lv"
lv=латиська
#XTXT Human readable text for language code "lt"
lt=литовська
#XTXT Human readable text for language code "ms"
ms=малайська
#XTXT Human readable text for language code "no"
no=норвезька
#XTXT Human readable text for language code "pl"
pl=польська
#XTXT Human readable text for language code "pt"
pt=португальська (Бразилія)
#XTXT Human readable text for language code "pt-PT"
pt-PT=португальська (Португалія)
#XTXT Human readable text for language code "ro"
ro=румунська
#XTXT Human readable text for language code "ru"
ru=російська
#XTXT Human readable text for language code "sr"
sr=сербська
#XTXT Human readable text for language code "sh"
sh=сербохорватська
#XTXT Human readable text for language code "sk"
sk=словацька
#XTXT Human readable text for language code "sl"
sl=словенська
#XTXT Human readable text for language code "es"
es=іспанська
#XTXT Human readable text for language code "es-MX"
es-MX=іспанська (Мексика)
#XTXT Human readable text for language code "sv"
sv=шведська
#XTXT Human readable text for language code "th"
th=тайська
#XTXT Human readable text for language code "tr"
tr=турецька
#XTXT Human readable text for language code "uk"
uk=українська
#XTXT Human readable text for language code "vi"
vi=вʼєтнамська
#XTIT: Delete spaces confirmation title
deleteConfirmationHeaderText=Видалити простори
#XTXT: Logically delete space delete confirmation text with space name
logicallyDeleteConfirmationBodyTextWithSpaceName=Справді перемістити простір "{0}" до кошика?
#XTXT: Logically dpace delete confirmation text for multiple spaces
logicallyDeleteConfirmationBodyTextMultiple=Справді перемістити вибрані простори ({0}) до кошика?
#XTXT: Delete space delete confirmation text with space name
deleteConfirmationBodyTextWithSpaceName=Справді видалити простір "{0}"? Цю дію неможливо скасувати.
#XTXT: Delete space delete confirmation text for multiple spaces
deleteConfirmationBodyTextMultiple=Справді видалити вибрані простори ({0})? Цю дію неможливо скасувати. Цей вміст буде видалено {1}:
#XTXT: permanently
permanently=остаточно
#XTXT: {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDetail=Цей вміст буде видалено {0} без можливості відновлення:
#XTXT: Hint Delete space confirmation text
deleteConfirmationHint=Введіть {0}, щоб підтвердити видалення.
#XTXT: Delete space confirmation error check spelling
deleteConfirmationErrorCheckSpelling=Перевірте правильність написаного й спробуйте ще раз.
#XTXT: All Spaces
allSpaces=Усі простори
#XTXT: All data
allData=Усі об’єкти та дані, що містяться в просторі
#XTXT: All connections
allConnections=Усі зв’язки, визначені в просторі
#XFLD: Space tile selection box tooltip
clickToSelect=Клацніть, щоб вибрати
#XTXT: All database users
allDatabaseUsers=Усі об’єкти та дані, що містяться в будь-якій схемі Open SQL, пов’язаній із простором
#XFLD: remove members button tooltip
deleteUsers=Видалити учасників
#XTXT: Space long description text
description=Опис (максимум 4000 символів)
#XFLD: Add Members button tooltip
addUsers=Додати учасників
#XFLD: Add Users button tooltip
addUsersTooltip=Додати користувачів
#XFLD: Edit Users button tooltip
editUsersTooltip=Редагувати користувачів
#XFLD: Remove Users button tooltip
removeUsersTooltip=Вилучити користувачів
#XFLD: Searchfield placeholder
filter=Пошук
#XCOL: Users table-view column health
health=Здоров’я
#XCOL: Users table-view column access
access=Доступ
#XFLD: No user found nodatatext
noDataText=Користувачів не знайдено
#XTIT: Members dialog title
selectUserDialogTitle=Додати учасників
#XTIT: User dialog title
addUserDialogTitle=Додати користувачів
#XTIT: Delete connections confirmation title
deleteConnectionConfirmationHeaderText=Видалити з’єднання
#XTIT: Delete single connection confirmation title
deleteConnectionConfirmationHeaderTextSingle=Видалити з’єднання
#XTXT: Delete connection delete confirmation text
deleteConnectionConfirmationBodyText=Справді видалити вибрані з’єднання? Їх буде вилучено назавжди.
#XTIT: Remote source dialog title
selectRemoteConnectionDialogTitle=Вибрати з’єднання
#XTIT: Share connection dialog title
connectionSharingDialogTitle=Поширити з’єднання
#XTIT: Connections shared from other spaces table title
sharedConnectionsFromOtherSpaces=Спільні з’єднання
#XFLD: Add remote source button tooltip
addRemoteConnections=Додати з’єднання
#XFLD: Delete remote source button tooltip
deleteRemoteConnections=Вилучити з’єднання
#XFLD: Share remote source button tooltip
shareConnections=Поширити з’єднання
#XFLD: Tile-layout tooltip
tileLayout=Формат підекрана
#XFLD: Table-layout tooltip
tableLayout=Формат таблиці
#XMSG: Success message after creating space
createSpaceSuccessMessage=Простір створено
#XMSG: Success message after copying space
copySpaceSuccessMessage=Копіювання простору "{0}" до простору "{1}"
#XMSG: Success message after deploying space
deploymentSuccessMessage=Розпочато розгортання простору
#XMSG: Success message after Apache Spark update started
sparkUpdateStartedSuccessMessage=Розпочато оновлення Apache Spark
#YMSE: Error while starting Apache Spark update
sparkUpdateStartedErrorMessage=Не вдалося оновити Apache Spark
#XMSG: Success message after updating space
updateSettingsSuccessMessage=Інформацію про простір оновлено
#XMSG: Success message after unlocking space
unlockSpaceSuccessMessage=Простір тимчасово розблоковано
#XMSG: Success message after deleting a single space
deleteSpaceSuccessMessage=Простір видалено
#XMSG: Success message after deleting multiple spaces
deleteSpacesSuccessMessage=Простори видалено
#XMSG: Success message after restoring a single space
restoreSpaceSuccessMessage=Простір відновлено
#XMSG: Success message after restoring multiple spaces
restoreSpacesSuccessMessage=Простори відновлено
#YMSE: Error while updating settings
updateSettingsFailureMessage=Не вдалось оновити настройки простору.
#YMSE: Error while updating data lake option
dataLakeEnabledInOtherSpace=Озеро даних вже призначене в інший простір. Простори можуть отримувати доступ до озера даних одночасно.
#YMSE: Error while updating data lake option
virtualTablesExists=Ви не можете скасувати призначення озера даних в цей простір, оскільки все ще існують залежності від віртуальних таблиць*. Видаліть віртуальні таблиці, щоб скасувати призначення озера даних в цей простір.
#YMSE: Error while unlocking space
unlockSpaceFailureMessage=Не вдалося розблокувати простір.
#YMSE: Error while creating space
createSpaceError=Не вдалося створити простір.
#YMSE: Error while creating duplicate space
createDuplicateSpaceError=Простір з іменем {0} вже існує.
#YMSE: Error while deleting a single space
deleteSpaceError=Не вдалося видалити простір.
#YMSE: Error details while deleting a single space
deleteSpaceDetailedError=Ваш простір "{0}" більше не працює належним чином. Спробуйте видалити його ще раз. Якщо це не допоможе, попросіть адміністратора видалити ваш простір або зверніться до служби підтримки.
#YMSE: Error while deleting a single space in files
deleteSpaceInFilesError=Не вдалося видалити дані простору у файлах.
#YMSE: Error while deleting a single space users
deleteSpaceUsersError=Не вдалося вилучити користувачів.
#YMSE: Error while deleting a single space schema
deleteSpaceSchemasError=Не вдалося вилучити схеми.
#YMSE: Error while deleting a single space connections
deleteSpaceConnectionsError=Не вдалося вилучити з’єднання.
#YMSE: Error while deleting a single space data
deleteSpaceDataError=Не вдалося видалити дані простору.
#YMSE: Error while deleting multiple spaces
deleteSpacesError=Не вдалося видалити простори.
#YMSE: Error while restoring a single space
restoreSpaceError=Не вдалося відновити простір.
#YMSE: Error while restoring multiple spaces
restoreSpacesError=Не вдалося відновити простори.
#YMSE: Error while creating users
createUsersError=Не вдалося додати користувачів.
#YMSE: Error while removing users
removeUsersError=Нам не вдалося вилучити користувачів.
#YMSE: Error while removing user
removeUserError=не вдалося вилучити користувачів.
#YMSE: Error assigning roles to a user
roleAssignUnAssignError=Нам не вдалося додати користувача до вибраних ролей з областю застосування. \n\n Ви не можете додати себе до ролі з областю застосування, але можете попросити зробити це свого адміністратора.
#YMSE: Error assigning user to the space
userAssignError=Не вдалося призначити користувача простору. \n\n Користувача вже призначено до максимально дозволеної кількості просторів (100) для всіх ролей з областю застосування.
#YMSE: Error assigning users to the space
usersAssignError=Не вдалося призначити користувачів простору. \n\n Користувача вже призначено до максимально дозволеної кількості просторів (100) для всіх ролей з областю застосування.
#YMSE: Error while fetching users
fetchUserWithScopedRolesError=Нам не вдалося отримати користувачів. Повторіть спробу згодом.
#YMSE: Error while fetching scoped roles
fetchScopedRolesError=Нам не вдалося отримати ролі з областю застосування.
#YMSE: Error while fetching members
fetchUserError=Не вдалось отримати учасників. Спробуйте ще раз пізніше.
#YMSE: Error while loading run-time database
loadRuntimeError=Не вдалося завантажити інформацію з бази даних часу виконання.
#YMSE: Error while loading spaces
loadSpacesError=Під час спроби відновити простори щось пішло не так.
#YMSE: Error while loading haas resources
loadStorageError=Під час спроби відновити дані сховища щось пішло не так.
#YMSE: Error no data could be loaded
loadDataError=Під час спроби відновити дані щось пішло не так.
#XFLD: Click to refresh storage data
clickToRefresh=Клацніть тут, щоб спробувати ще раз.
#XTIT: Delete space popup title
deleteSpacePopupTitle=Видалити простір
#XCOL: Spaces table-view column name
name=Ім’я
#XCOL: Spaces table-view deployment status
deploymentStatus=Статус розгортання
#XFLD: Disk label in space details
storageLabel=Диск (ГБ)
#XFLD: In-Memory label in space details
ramLabel=Пам'ять (ГБ)
#XFLD: Memory label on space card
memory=Пам'ять для сховища
#XFLD: Storage Assignment label in space details
storrageAssignmentLabel=Сховище просторів
#XFLD: Storage Type label in space details
storageTypeLabel=Тип сховища
#XFLD: Enable Space Quota
enableSpaceQuota=Увімкнути квотування простору
#XFLD: No Space Quota
noSpaceQuota=Немає квоти для простору
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
diskAndInMemoryText=База даних SAP HANA (на диску і в пам'яті)
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
dataLakeFilesText=Файли озера даних SAP HANA
#XFLD: Available scoped roles label
availableRoles=Доступні ролі з областю застосування
#XFLD: Selected scoped roles label
selectedRoles=Вибрані ролі з областю застосування
#XCOL: Spaces table-view column models
models=Моделі
#XCOL: Spaces table-view column users
users=Користувачі
#XCOL: Spaces table-view column connections
connections=З’єднання
#XFLD: Section header overview in space detail
overview=Огляд
#XFLD: Section header Apache Spark in space detail
sparkManagementSection=Apache Spark
#XFLD: Section header Apache Spark in space detail
sparkManagementSubSection=Застосунки
#XFLD: Section header Apache Spark in space detail
taskAssignmentSubSection=Присвоєння завдання
#XFLD: vCPU label in Apache Spark section
vCPULabel=Віртуальні ЦП
#XFLD: Memory label in Apache Spark section
memoryLabel=Пам'ять (ГБ)
#XFLD: space configuration label in Apache Spark section
spaceConfigurationLabel=Конфігурація простору
#XFLD: Space Source label
sparkApplicationLabel=Застосунок
#XFLD: Cluster Size label
clusterSizeLabel=Розмір кластера
#XFLD: Driver label
driverLabel=Драйвер
#XFLD: Executor label
executorLabel=Виконавець
#XFLD: max label
maxLabel=Максимум використано
#XFLD: TrF Default label
trFDefaultLabel=Усталений потік трансформації
#XFLD: Merge Default label
mergeDefaultLabel=Усталене об'єднання
#XFLD: Optimize Default label
optimizeDefaultLabel=Оптимізувати усталене
#XFLD: Deployment Default label
deploymentDefaultLabel=Розгортання локальної таблиці (файлу)
#XFLD: max spark source label
driverExecutorMaxUsedSparkSource={0} ЦП / {1} ГБ
#XFLD: Object type label
taskObjectTypeLabel=Тип об'єкта
#XFLD: Task activity label
taskActivityLabel=Операція
#XFLD: Task Application ID label
taskApplicationIDLabel=Усталений застосунок
#XFLD: Section header in space detail
generalSettings=Загальні настройки
#XMSG: Message strip message when a space is locked
spaceLockedMessageStrip=Цей простір зараз заблоковано системою.
#XMSG: Message strip message deploy warning
deployWarningMessageStrip=Зміни в цьому розділі буде розгорнуто негайно.
#XMSG: Message strip message modify space configuration warning
modifySpaceConfigurationMessageStrip=Зверніть увагу, що зміна значень може викликати ускладнення з продуктивністю.
#XFLD: Button text to unlock the space again
unlockSpace=Розблокувати простір
#XFLD: Info text for audit log formatted message
auditLogText=Активуйте журнали аудиту, щоб записувати дії читання або внесення змін (політики аудиту). У такому разі адміністратори зможуть проаналізувати, хто які дії виконував і коли саме.
#XMSG: Message strip  audit warning
auditLogWarningMessageStrip=Журнал аудиту можуть займати багато місця в дисковому сховищі орендатора. Якщо активовано політику аудиту (дії читання і внесення змін), треба регулярно відстежувати використання дискового сховища (за допомогою картки "Використаний обсяг сховища на диску" в системному моніторі), щоб уникнути відключення через повне використання диску, що може призвести до переривань у роботі сервісів. Якщо вимкнути політику аудиту, усі записи журналу аудиту буде видалено. Щоб зберегти записи журналу аудиту, радимо експортувати їх перед вимкненням політики аудиту.
#XFLD: Message strip link text to open in app help to guide user what to do to unlock his space permanently
unlockSpaceHelp=Показати довідку
#XMSG: Message strip message when a space will be locked again
spaceTemporarilyUnlockedMessageStrip=Цей простір перевищує обсяг сховища просторів, і його буде заблоковано через {0} {1}.
#XMSG: Unit for remaining time until space is locked again
hours=години
#XMSG: Unit for remaining time until space is locked again
minutes=хвилини
#XFLD: Section header in space detail for auditing
detailsSectionAuditing=Аудит
#XFLD: Subsection header in space detail for auditing
auditing=Настройки аудиту простору
#XFLD: Hot space tooltip
hotSpaceCountTooltip=Критичні простори: використано понад 90% сховища
#XFLD: Green space tooltip
greenSpaceCountTooltip=Нормальні простори: використано від 6% до 90% сховища
#XFLD: Cold space tooltip
coldSpaceCountTooltip=Безпечні простори: використано до 5% сховища
#XFLD: Critical space tooltip
criticalSpaceCountTooltip=Критичні простори: використано понад 90% сховища
#XFLD: Green space tooltip
okSpaceCountTooltip=Нормальні простори: використано від 6% до 90% сховища
#XFLD: Locked space tooltip
lockedSpaceCountTooltipNew=Заблоковані простори: заблоковані через недостатню кількість пам’яті.
#XFLD: Locked space tooltip
lockedSpaceCountTooltip=Заблоковані простори
#YMSE: Error while deleting remote source
deleteRemoteError=Не вдалося вилучити з'єднання.
#XFLD: Space ID input tooltip
spaceIDInfoTooltip=Ідентифікатор простору неможливо змінити пізніше.\nПрипустимі символи: A–Z, 0–9 і _
#YMSE: Error when space name is empty
spaceNameEmptyValidationError=Введіть ім’я простору.
#YMSE: Error when ECN Business name is empty
ecnNameEmptyValidationError=Введіть бізнес-ім'я.
#YMSE: Error when space id is empty
spaceIDInputEmptyValidationError=Введіть ідентифікатор простору.
#YMSE: Error when space id contains invalid characters
spaceIDInputInValidError=Неприпустимі символи. Використовуйте лише символи A–Z, 0–9 і _.
#YMSE: Error when space id exists while creation
spaceIDInputDuplicateError=Ідентифікатор простору вже існує.
#XFLD: Space searchfield placeholder
search=Пошук
#XMSG: Success message after creating users
createUsersSuccess=Користувачів додано
#XMSG: Success message after creating user
createUserSuccess=Користувача додано
#XMSG: Success message after updating users
updateUsersSuccess=Оновлено користувачів: {0}
#XMSG: Success message after updating user
updateUserSuccess=Оновлено користувача
#XMSG: Success message after removing users
removeUsersSuccess=Вилучено користувачів: {0}
#XMSG: Success message after removing user
removeUserSuccess=Вилучено користувача
#XFLD: Schema name
schemaName=Назва схеми
#XFLD: used of total
ofTemplate={0} з {1}
#XFLD: Assigned disk label in space overview
assignedStorageTemplate=Присвоєно на диску ({0} з {1})
#XFLD: Assigned Memory label in space overview
assignedRAMTemplate=Призначена пам’ять ({0} з {1})
#XFLD: Storage ratio on space
accelearationRAM=Прискорення в пам'яті
#XFLD: No Storage Consumption
noStorageConsumptionText=Квоту сховища не призначено.
#XFLD: Used disk label in space overview
usedStorageTemplate=Диск, використаний для сховища ({0} з {1})
#XFLD: Used Memory label in space overview
usedRAMTemplate=Пам''ять, використана для сховища ({0} з {1})
#XFLD: Used Disk tooltip
usedStorageTooltipTemplate=Використаний для сховища диск: {0} з {1} 
#XFLD: Used Memory tooltip
usedRAMTooltipTemplate=Використано пам’яті: {0} з {1}
#XFLD: Assigned Disk tooltip
assignedStorageTooltipTemplate={0} з {1} присвоєно на диску
#XFLD: Assigned Memory tooltip
assignedRAMTooltipTemplate=Призначено пам’яті: {0} з {1}
#XFLD: Space related used storage tooltip
spaceRelatedUsedStorageTooltipTemplate=Дані простору: {0}
#XFLD: Not space related used storage tooltip
otherDataUsedStorageTooltipTemplate=Інші дані: {0}
#XFLD: Storage used exceeded popover and tooltip
storageUsedExceededText=Розгляньте можливість продовження плану чи зверніться до служби підтримки SAP.
#XCOL: Space table-view column used Disk
usedStorage=Диск, використаний для сховища
#XCOL: Space monitor column used Memory
usedRAM=Пам'ять, використана для сховища
#XCOL: Space monitor column Schema
tableSchema=Схема
#XCOL: Space monitor column Storage Type
tableStorageType=Тип сховища
#XCOL: Space monitor column Table Type
tableType=Тип таблиці
#XCOL: Space monitor column Record Count
tableRecordCount=Кількість записів
#XFLD: Assigned Disk
assignedStorage=Диск, присвоєний для сховища
#XFLD: Assigned Memory
assignedRAM=Пам'ять, присвоєна для сховища
#XCOL: Space table-view column storage utilization
tableStorageUtilization=Використаний простір у сховищі
#XFLD: space status
spaceStatus=Статус простору
#XFLD: space type
spaceType=Тип простору
#XFLD: space type label for space with space type "SAP BW bridge" ("SAP BW bridge" is a product name, do not translate it)
abapbridgeSpaceType=Міст SAP BW
#XFLD: space type label for space with space type "Data Marketplace"
marketplaceSpaceType=Продукт постачальника даних
#XMSG: Error message for when selected space cannot be deleted due to the space type
deleteTypedSpaceError=Не можна видалити простір {0}, оскільки він має тип {1}.
#XMSG: Error message for when selected spaces cannot be deleted due to the space type
deleteTypedSpacesError=Не можна видалити виділені простори {0}. Простори таких типів видалити не вдасться: {1}.
#XFLD: space type label for space without space type
defaultSpaceType=SAP Datasphere
#XFLD: Tooltip for monitor space button
monitorSpace=Монітор
#XFLD: Tooltip for edit space button
editSpace=Редагувати простір
#XMSG: Deletion warning in messagebox
deleteConfirmation=Справді видалити цей простір?
#XFLD: Tooltip for delete space button
deleteSpace=Видалити простір
#XFLD: storage
storage=Диск для сховища
#XFLD: username
userName=Ім’я користувача
#XFLD: port
port=Порт
#XFLD: hostname
hostName=Ім’я хоста
#XFLD: password
password=Пароль
#XBUT: Request new password button
requestPassword=Запит нового пароля
#YEXP: Usage explanation in time data section
timeDataSectionHint=Створюйте часові таблиці та виміри для використання у своїх моделях та історіях.
#YEXP: Usage explanation in Space schema section
spaceSchemaHint=Бажаєте, щоб дані у вашому просторі використовувались іншими інструментами чи програмами? Якщо так, створіть одного або кількох користувачів, які зможуть отримати доступ до даних у вашому просторі, і виберіть, чи потрібно, щоб усі майбутні дані простору використовувались усталено.
#XTIT: Create schema popup title
createSchemaDialogTitle=Створити схему Open SQL
#XTIT: Generate Time Data dialog title
generateTimeDataDialogTitle=Створити часові таблиці та виміри
#XTIT: Edit Time Data dialog title
editTimeDataDialogTitle=Редагувати часові таблиці та виміри
#XTIT: Time Data token title
timeDataTokenTitle=Часові дані
#XTIT: Time Data token title
timeDataUpdateViews=Оновити подання часових даних
#XFLD: Time Data token creation in progress label
timeDataCreationInProgress=Триває створення...
#XFLD: Time Data token creation error label
timeDataCreationError=Не вдалося створити. Спробуйте ще раз.
#XTIT: Time Data dialog time settings title
timeDataDialogTimeSettingsTitle=Настройки часової таблиці
#XTIT: Time Data dialog time data table title
timeDataDialogTranslationTablesTitle=Таблиці перетворення
#XTIT: Time Data dialog dimensions title
timeDataDialogTimeViewsTitle=Часові виміри
#XFLD: Time Data dialog time range label
timeRangeHint=Визначте часовий діапазон.
#XFLD: Time Data dialog time data table label
timeDataHint=Дайте ім’я таблиці.
#XFLD: Time Data dialog time data dimensions label
timeDimensionsHint=Дайте ім’я вимірам.
#XFLD: Time Data Time range description label
timerangeLabel=Діапазон часу
#XFLD: Time Data dialog from year label
fromYearLabel=Від року
#XFLD: Time Data dialog to year label
toYearLabel=До року
#XFLD: Time Data dialog calendar type label
calendarTypeLabel=Тип календаря
#XFLD: Time Data dialog granularity label
granularityLabel=Гранулярність
#XFLD: Time Data dialog technical name label
technicalNameLabel=Технічне ім’я
#XFLD: Time Data dialog quarters text table lable
quarterTextsTableLabel=Таблиця перетворення для кварталів
#XFLD: Time Data dialog months text table lable
monthsTextsTableLabel=Таблиця перетворення для місяців
#XFLD: Time Data dialog days text table lable
daysTextsTableLabel=Таблиця перетворення для днів
#XFLD: Time Data dialog year label
yearLabel=Річний вимір
#XFLD: Time Data dialog quarter label
quarterLabel=Квартальний розмір
#XFLD: Time Data dialog month label
monthLabel=Місячний вимір
#XFLD: Time Data dialog day label
dayLabel=Денний вимір
#XFLD: Time Data dialog gregorian calendar type label
gregorian=Григоріанський
#XFLD: Time Data dialog time granularity day label
day=День
#XFLD: Time Data dialog business name maxlength reached
timeRangeValueStateTextMaxLengthBusinessName=Досягнуто макс. довжини 1000 символів.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextMaxRangeExceeded=Максимальний часовий діапазон становить 150 років.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeLower=Значення "Від року" має бути меншим за значення "До року"
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeHigher=Значення в полі "Від року" має бути 1900 або більше.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeHigher=Значення "До року" має перевищувати значення "Від року"
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeLower=Значення в полі "До року" має бути меншим за поточний рік + 100.
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningFromYear=Збільшення значення "Від року" може призвести до втрати даних
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningToYear=Зменшення значення "До року" може призвести до втрати даних
#XMSG: Time Data creation validation error message
timeDataValidationError=Схоже, деякі поля недійсні. Перевірте обов’язкові поля, щоб продовжити.
#XMSG: Time Data deletion popup text
deleteTimeDataConfirmation=Справді видалити дані?
#XMSG: Time Data creation success message
createTimeDataSuccess=Створено часові дані
#XMSG: Time Data update success message
updateTimeDataSuccess=Часові дані оновлено
#XMSG: Time Data delete success message
deleteTimeDataSuccess=Часові дані видалено
#XMSG: Time Data creation error message
createTimeDataError=Під час спроби створити часові дані щось пішло не так.
#XMSG: Time Data update error message
updateTimeDataError=Під час спроби оновити часові дані щось пішло не так.
#XMSG: Time Data creation error message
deleteTimeDataError=Під час спроби видалити часові дані щось пішло не так.
#XMSG: Error message when time data could not be retrieved
retrievingTimeDataError=Часові дані не вдалося завантажити.
#XTIT: Time Data deletion popup title
deleteTimeDataConfirmationTitle=Застереження
#XTXT: Time Data deletion dependency popup text
deleteTimeDataDependencyText=Не вдалося видалити ваші часові дані, оскільки вони використовуються в інших моделях.
#XTXT: Time Data deletion dependency popup text singular
deleteTimeDataDependencyTextSingular=Не вдалося видалити ваші часові дані, оскільки вони використовуються в іншій моделі.
#XMSG: Validation error when required time data form field is empty
timeDataRequiredField=Це поле обов’язкове, і його не можна залишати пустим.
#XFLD: Tooltip for Open in Database Explorer button
openInDBX=Відкрити в провіднику баз даних
#YMSE: Dimension Year
dimensionYearView=Вимір "Рік"
#YMSE: Dimension Year
dimensionQuarterView=Вимір "Квартал"
#YMSE: Dimension Year
dimensionMonthView=Вимір "Місяць"
#YMSE: Dimension Year
dimensionDayView=Вимір "День"
#XFLD: Time Data deletion object title
timeDataUsedIn=(використовується в моделях {0})
#XFLD: Time Data deletion object title singular
timeDataUsedInSingular=(використовується в 1 моделі)
#XFLD: Time Data deletion table column provider
provider=Провайдер
#XTIT: Time Data dependency dialog title
timeDataDependencyDialogTitle=Залежності
#XTIT: Create schema popup title
createSpaceSchemaUserDialogTitle=Створення користувача для схеми простору
#XFLD: Create schema button
createSchemaButton=Створити схему Open SQL
#XFLD: Generate TimeData button
generateTimeDataButton=Створити часові таблиці та виміри
#XFLD: Show dependencies button
showDependenciesButton=Показати залежності
#XFLD: tooltip for disabled buttons when user is not member of space
mustBeMemberOfSpaceForOperation=Для виконання цієї операції ваш користувач має бути учасником простору.
#XFLD: Create space schema user button
createSpaceSchemaUserButton=Створити користувача схеми простору
#YMSE: API Schema users load error
loadSchemaUsersError=Не вдалося завантажити список користувачів.
#XTIT: API Schema-config popup title
schemaUserDetailsDialogTitle=Інформація про користувача схеми простору
#XMSG: API Schema user deletion popup text
deleteSpaceSchemaUserConfirmation=Справді видалити вибраного користувача?
#XMSG: API Schema user deletion confirmation text
userDeleteSuccess=Користувача видалено.
#YMSE: API Schema user deletion error
userDeleteError=Не вдалося видалити користувача.
#XFLD: User deleted
userDeleted=Користувача видалено.
#XTIT: Remove user popup title
removeUserConfirmationTitle=Застереження
#XMSG: Remove user popup text
removeUserConfirmation=Справді вилучити користувача? Користувача та призначені йому ролі з областю застосування буде вилучено з простору.
#XMSG: Remove users popup text
removeUsersConfirmation=Справді вилучити користувачів? Користувачів та призначені їм ролі з областю застосування буде вилучено з простору.
#XBUT: Confirmation button for remove user dialog
removeUserConfirmationBtn=Вилучити
#YMSE: No data text for available roles
noDataAvailableRoles=Простір не додано до жодної ролі з областю застосування. \n Щоб мати змогу додавати користувачів до простору, його спочатку слід додати до однієї або кількох ролей з областю застосування.
#YMSE: No data text for selected roles
noDataSelectedRoles=Немає ролей із областю застосування
#XTIT: Schema-config popup title
schemaDetailsDialogTitle=Відомості про конфігурацію схеми Open SQL
#XFLD: Label for Read Audit Log
auditLogRead=Увімкнути журнал аудиту для операцій зчитування
#XFLD: Label for Change Audit Log
auditLogChange=Увімкнути журнал аудиту для операцій змінення
#XFLD: Label Audit Log Retention
auditLogRetention=Зберігати журнали протягом
#XFLD: Label Audit Log Retention Unit
retentionUnit=Дні
#YMSE: Error when log retention time is set incorrectly
incorrectRetentionTime=Введіть ціле число між {0} і {1}
#XFLD: Label for Cross Space Schema Access
spaceSchemaAccess=Використовувати дані схеми простору
#XTIT: Confirm deactivation of space schema consumption dialog title
disableSpaceSchemaConsumptionDialogTitle=Припинити використовувати дані схеми простору
#XTXT: Confirm deactivation of space schema consumption dialog text
disableSpaceSchemaConsumptionDialogText=Ця відкрита схема SQL може використовувати дані вашої схеми простору. Якщо ви припините використання, моделі, засновані на даних схеми простору, можуть більше не працювати.
#XBUT: Confirmation button for the stop consuming space schema data dialog
stopConsuming=Припинити використання
#XTXT: Tooltip for data lake icon
dataLakeIconTooltip=Цей простір використовується для доступу до озера даних
#XFLD: Tooltip for data lake table entry
dataLakeTableEntryTooltip=Озеро даних увімкнено
#XFLD: used In-Memory linit reached tag
usedRamLimitReachedLabel=Досягнуто ліміту пам'яті
#XFLD: Space storage Limit reached tag
storageLimitReachedLabel=Досягнуто ліміту сховища
#XFLD: Minimal Storage Limit Reached tag
minStorageLimitReachedLabel=Досягнуто мінімального ліміту сховища
#XFLD: Space ram tag
ramLimitReachedLabel=Досягнуто ліміту пам'яті
#XFLD: Minimal In-Memory Limit Reached tag
minRamLimitReachedLabel=Досягнуто ліміту мінімального обсягу пам'яті
#XFLD: Popover text after click on space storage limit warning
storageLimitReachedText=Досягнуто ліміту присвоєного сховища простору ({0}). Присвойте додаткове сховище в простір.
#XFLD: System storage tag
systemStorageLimitReachedLabel=Досягнуто ліміту системного сховища
#XMSG: Popover text after click on storage limit warning
systemStorageLimitReachedText=Досягнуто ліміту системного сховища ({0}). Зараз ви не можете присвоїти більше сховища в простір.
#XMSG: Schema deletion warning
deleteSchemaConfirmation=Видалення цієї схеми Open SQL також назавжди видалить усі збережені об’єкти та підтримувані асоціації в схемі. Продовжити?
#XMSG: Success message after deleting a schema
schemaDeleteSuccess=Схему видалено
#YMSE: Error while deleting schema.
schemaDeleteError=Не вдалося видалити схему.
#XMSG: Success message after update a schema
schemaUpdateSuccess=Схему оновлено
#YMSE: Error while updating schema.
schemaUpdateError=Не вдалось оновити схему.
#XMSG: Hint when no password is shown in the dialog.
newPasswordHint=Ми надали пароль для цієї схеми. Якщо ви забули свій пароль або втратили його, ви можете подати запит на новий пароль. Обов’язково скопіюйте або збережіть новий пароль.
#XMSG: Hint when showing the password in the dialog.
savePasswordHint=Скопіюйте свій пароль. Він знадобиться для настройки з’єднання для цієї схеми. Якщо ви забули свій пароль, ви можете відкрити це діалогове вікно, щоб скинути його.
#XFLD,50: Hint on entering schema name in creation dialog
schemaNameHint=Це ім’я неможливо змінити після створення схеми.
#XFLD: Open SQL Schemas section sub headline
schemasSQL=Open SQL
#XFLD: Space schema section sub headline
schemasSpace=Простір
#XFLD: HDI Container section header
HDIContainers=Контейнери HDI
#XTXT: Add HDI Containers button tooltip
addHDIContainers=Додати контейнери HDI
#XTXT: Remove HDI Containers button tooltip
removeHDIContainers=Видалити контейнери HDI
#XFLD: Enable HDI Containers feature button
enableHDIContainersButton=Увімкнути доступ
#YMSE: No data text for HDI Containers table
noDataHDIContainers=Контейнери HDI не додано.
#YMSE: No data text for Timedata section
noDataTimedata=Не створено жодної таблиці часу і жодної величини.
#YMSE: No data text during runtime not available for Timedata section
runtimeNotAvailableNoDataTimedata=Не вдалося завантажити величини та таблиці часу, оскільки база даних часу виконання недоступна.
#YMSE: No data text during runtime not available for HDI section
runtimeNotAvailableNoDataHDI=Не вдалося завантажити контейнери HDI, оскільки база даних часу виконання недоступна.
#YMSE: Error while fetching HDI Containers
fetchHDIContainersError=Не вдалось отримати контейнери HDI. Спробуйте пізніше.
#XFLD Table column header for HDI Container names
HDIContainerName=Ім’я контейнера HDI
#XFLD: Enable HDI Containers feature dialog title
enableHDIContainersDialogTitle=Увімкнути доступ
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText1New=Ви можете ввімкнути SAP SQL Data Warehouse на своєму орендарі SAP Datasphere для обміну даними між своїми контейнерами HDI і просторами SAP Datasphere без необхідності переміщувати дані.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText2=Для цього створіть запит у службу підтримки, натиснувши кнопку нижче.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText3New=Щойно ваш запит буде оброблено, потрібно створити один або кілька нових контейнерів HDI в базі даних часу виконання SAP Datasphere. Потім кнопку "Увімкнути доступ" буде замінено кнопкою "+" у розділі "Контейнери HDI" для всіх ваших просторів SAP Datasphere, і ви зможете додати свої контейнери в цей простір.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText41=Потрібна додаткова інформація? Перейдіть за посиланням %%0. Докладну інформацію про те, що треба додати в тікет, див. тут: %%1.
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkText=Довідка SAP
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkTextSapNote3057059=Нотатка SAP 3057059
#XBUT: Open Ticket Button Text
openTicket=Створити запит
#XBUT: Add Button Text
add=Додати
#XBUT: Next Button Text
next=Далі
#XBUT: Edit Button Text
editUsers=Редагувати
#XBUT: create user Button Text
createUser=Створити
#XBUT: Update user Button Text
updateUser=Виділити
#XTIT: Add HDI Containers Dialog Title
addHDIContainersDialogTitle=Додати непризначені контейнери HDI
#XFLD: No data text for add HDI container dialog
addHDIContainersDialogNoDataText=Не вдалося знайти непризначені контейнери. \n Контейнер, який ви шукаєте, може вже бути присвоєно в простір.
#YMSE: Error while loading assigned HDI Containers
loadAssignedHDIContainersError=Не вдалося завантажити призначені контейнери HDI.
#YMSE: Error while loading assigned HDI Containers
loadUnassignedHDIContainersError=Не вдалося завантажити контейнери HDI.
#XMSG: Success message
succeededToAddHDIContainer=Додано контейнер HDI
#XMSG: Success message
succeededToAddHDIContainerPlural=Додано контейнери HDI
#XMSG: Success message
succeededToDeleteHDIContainer=Контейнер HDI вилучено
#XMSG: Success message
succeededToDeleteHDIContainerPlural=Контейнери HDI вилучено
#XFLD: Time data section sub headline
timeDataSection=Часові таблиці та виміри
#XFLD: Schemas list entry read audit log enabled indicator
readIndicator=Читати
#XFLD: Schemas list entry change audit log enabled indicator
changeIndicator=Змінити
#XFLD: Remote sources section sub headline
allconnections=Присвоєння з’єднання
#XFLD: Remote sources section sub headline
localconnections=Локальні з’єднання
#XFLD: Remote sources section sub headline for open Connectors
openConnectors=Відкриті конектори SAP
#XFLD: User section sub headline
memberassignment=Присвоєння учасників
#XFLD: User assignment section sub headline
userAssignment=Призначення користувача
#XFLD: User section Access dropdown Member
member=Учасник
#XFLD: User assignment section column name
user=Ім’я користувача
#XTXT: Selected role count
selectedRoleToolbarText=Вибрано: {0}
#XTIT: Space detail section connections title
detailsSectionConnections=З’єднання
#XTIT: Space detail section data access title
detailsSectionDataAccess=Доступ до схеми
#XTIT: Space detail section time data title
detailsSectionGenerateData=Часові дані
#XTIT: Space detail section members title
detailsSectionUsers=Учасники
#XTIT: Space detail section Users title
detailsSectionUsersTitle=Користувачі
#XTIT: Out of Storage
insufficientStoragePopoverTitle=Сховище вичерпалося
#XTIT: Storage distribution
storageDistributionPopoverTitle=Використаний обсяг сховища на диску
#XTXT: Out of Storage popover text
insufficientStorageText=Щоб створити новий простір, зменште присвоєне сховище іншого простору або видаліть простір, який більше не потрібен. Ви можете збільшити загальне системне сховище, викликавши засіб керування планом.
#XMSG: Space id length warning
spaceIdLengthWarning=Перевищено максимум {0} символів.
#XMSG: Space name length warning
spaceNameLengthWarning=Перевищено максимум {0} символів.
#XMSG: Space id reserved prefix warning
spaceIdnWarningReservedPrefix=Не використовуйте префікс {0}, щоб уникнути можливих конфліктів.
#YMSE: Error while loading open SQL schemas
loadLocalSchemasError=Не вдалося завантажити схеми Open SQL.
#YMSE: Error while creating open SQL schema
createLocalSchemaError=Не вдалося створити схему Open SQL.
#YMSE: Error while loading all remote connections
loadRemoteConnectionsError=Не вдалося завантажити всі віддалені з’єднання.
#YMSE: Error while loading space details
loadSpaceDetailsError=Не вдалося завантажити дані про простори.
#YMSE: Error while deploying space details
deploySpaceDetailsError=Не вдалося розгорнути простір.
#YMSE: Error while copying space details
copySpaceDetailsError=Не вдалося скопіювати простір.
#YMSE: Error while loading storage data
loadStorageDataError=Не вдалося завантажити дані сховища.
#YMSE: Error while loading all users
loadAllUsersError=Не вдалося завантажити всіх користувачів.
#YMSE: Failed to reset password
resetPasswordError=Не вдалося скинути пароль.
#XMSG: Success message after updating space
resetPasswordSuccessMessage=Для схеми встановлено новий пароль
#YMSE: DP Agent-name too long
DBAgentNameError=Ім’я агента DP задовге.
#YMSE: Schema-name not valid.
schemaNameError=Ім’я схеми недійсне.
#YMSE: User name not valid.
UserNameError=Ім’я користувача недійсне.
#XTIT: Consumption by Storage Type
consumptionStorageTypeText=Використання за типом сховища
#XTIT: Consumption by Schema
consumptionSchemaText=Використання за схемою
#XTIT: Overall Consumption by schema
overallConsumptionSchemaText=Загальне використання таблиці за схемою
#XTIT: Overall Consumption by Table Type
overallConsumptionTableTypeText=Загальне використання за типом таблиці
#XTIT: Tables
tableDetailsText=Відомості таблиці
#XTIT: Table Storage Consumption
tableStorageConsumptionText=Використання сховища таблиці
#XFLD: Table Type label
tableTypeLabel=Тип таблиці
#XFLD: Schema label
schemaLabel=Схема
#XFLD: reset table tooltip
resetTable=Скинути таблицю
#XFLD: In-Memory label in space monitor
inMemoryLabel=Пам'ять
#XFLD: Disk label in space monitor
diskLabel=Диск
#XFLD: Yes
yesLabel=Так
#XFLD: No
noLabel=Ні
#XFLD: switch label: Want the data in this space to be consumable by default?
allowConsumptionByDefault=Потрібно, щоб дані в цьому просторі використовувались усталено?
#XFLD: Business Name
businessNameLabel=Бізнес-ім’я
#XFLD: Refresh
refresh=Оновити
#XMSG: No filter results title
noFilterResultsTitle=Схоже, що в настройках вашого фільтра не відображаються дані.
#XMSG: No filter results message
noFilterResultsMsg=Спробуйте уточнити настройки фільтра, а якщо ви так і не побачите жодних даних, створіть кілька таблиць у Конструкторі моделей даних. Щойно вони почнуть використовувати сховище, ви зможете бачити їх тут.
#XMSG: Space monitoring Hana state red title
spaceMonitorHanaRedTitle=База даних часу виконання недоступна.
#XMSG: Space monitoring Hana state red message
spaceMonitorHanaRedMsg=Оскільки база даних часу виконання недоступна, певні функції вимкнено й ми не можемо відображати жодної інформації на цій сторінці.
#YMSE: Error while creating space schema user
createSpaceSchemaUserError=Не вдалося створити користувача схеми простору.
#YMSE: Error User name already exists
userAlreadyExistsError=Ім’я користувача вже існує.
#YMSE: Error Authentication failed
authenticationFailedError=Помилка автентифікації.
#XMSG: Message strip message when a schema user is locked
userLockedMessageStrip=Користувача заблоковано через завелику кількість невдалих входів. Запросіть новий пароль, щоб розблокувати користувача.
#XMSG: Success message after reset and unlock schema user
resetUnlockPasswordSuccessMessage=Задано новий пароль, користувача розблоковано
#XMSG: user is locked message
userLockedMessage=Користувача заблоковано.
#XCOL: Users table-view column Role
spaceRole=Роль
#XCOL: Users table-view column Scoped Role
spaceScopedRole=Роль з областю застосування
#XCOL: Users table-view column Space Admin
spaceAdmin=Адміністратор простору
#XFLD: User section dropdown value Viewer
viewer=Переглядач
#XFLD: User section dropdown value Modeler
modeler=Розробник моделей
#XFLD: User section dropdown value Data Integrator
dataIntegrator=Інтегратор даних
#XFLD: User section dropdown value Space Administrator
spaceAdministrator=Адміністратор простору
#XMSG: Success message -  Space Role updated successfully.
updateUserSpaceRoleSuccess=Роль простору оновлено
#XMSG: Error message - Failed to update Space Role.
updateUserSpaceRoleError=Роль простору не було оновлено успішно.
#XFLD:
databaseUserNameSuffix=Суфікс імені користувача бази даних
#XTXT: Space Schema password text
spaceSchemaPasswordText=Щоб установити з’єднання з цим користувачем схеми, скопіюйте свій пароль. Якщо ви забули свій пароль, ви завжди можете подати запит на новий.
#XFLD: Cloud Platform label in space schema dialog
cloudPlatformLabel=Cloud Platform
#XTXT: Cloud Platform text in space schema dialog
cloudPlatformText=Щоб налаштувати доступ через цього користувача, увімкніть споживання та скопіюйте облікові дані. Якщо ви можете скопіювати лише облікові дані без пароля, обов’язково додайте пароль пізніше.
#XFLD: enable consumption in CloudPlatform label in space schema dialog
enableConsumptionInCloudPlatformLabel=Увімкнути споживання в Cloud Platform
#XFLD: Credentials for User-Provided Service label in space schema dialog
cloudPlatformCredentialsLabel=Реєстраційні дані для наданої користувачем служби:
#XFLD: Credentials for User-Provided Service (Without Password) label in space schema dialog
cloudPlatformCredentialsWithoutPasswordLabel=Реєстраційні дані для наданої користувачем служби (без пароля):
#XFLD: Copy Credentials label in space schema dialog
copyCredentialsLabel=Скопіювати реєстраційні дані без пароля
#XFLD: Copy Credentials label in space schema dialog
copyFullCredentialsLabel=Скопіювати реєстраційні дані повністю
#XFLD: Copy Password label in space schema dialog
copyPasswordLabel=Скопіювати пароль
#XMSG: Credentials copied to clipboard
credentialsCopiedMessage=Реєстраційні дані скопійовано до буфера обміну
#XMSG: Password copied to clipboard
passwordCopiedMessage=Пароль скопійовано до буфера обміну
#XMSG: create database user dialog title
createDatabaseUserDialogTitle=Створити користувача бази даних
#XMSG: Database Users section title
databaseUsers=Користувачі бази даних
#XMSG: Database User Details dialog title
databaseUserDetailsDialogTitle=Відомості про користувача бази даних
#XFLD: database user read audit log
databaseUserAuditLogRead=Увімкнути журнали аудиту для операцій зчитування та зберігати журнали протягом
#XFLD: database user change audit log
databaseUserAuditLogChange=Увімкнути журнали аудиту для операцій змінення та зберігати журнали протягом
#XMSG: Cloud Platform Access
cloudPlatformAccess=Доступ до Cloud Platform
#XFLD: Enable Consumption in Cloud Platform hint
enableConsumptionCloudPlatformHint=Налаштуйте доступ до контейнера HANA Deployment Infrastructure (HDI) через цього користувача бази даних. Щоб підключитися до свого контейнера HDI, потрібно ввімкнути моделювання SQL.
#XFLD: Enable HDI Consumption
enableConsumptionCloudPlatform=Увімкнути споживання HDI
#XFLD: Enable Consumption hint
enableConsumptionHint=Чи потрібно, щоб дані у вашому просторі використовувались іншими інструментами чи програмами?
#XFLD: Enable Consumption
enableConsumption=Увімкнути споживання SQL
#XFLD: Enable Modeling
enableModeling=Увімкнути моделювання SQL
#XMSG: Privileges for Data Modeling
privilegesModeling=Поглинання даних
#XMSG: Privileges for Data Consumption
privilegesConsumption=Споживання даних для зовнішніх інструментів
#XFLD: SQL Modeling
sqlModeling=Моделювання SQL
#XFLD: SQL Consumption
sqlConsumption=Споживання SQL
#XFLD: enabled
enabled=Активовано
#XFLD: disabled
disabled=Деактивовано
#XFLD: Edit Privileges
editPrivileges=Редагувати привілеї
#XFLD: Open Database Explorer
openDBX=Відкрити провідник баз даних
#XFLD: create database user hint
databaseCreateHint=Зауважте, що після збереження ви більше не зможете змінити ім’я користувача.
#XFLD: Internal Schema Name
internalSchemaName=Назва внутрішньої схеми
#YMSE: Failed to load database users
loadDatabaseUserError=Не вдалося завантажити користувачів бази даних
#YMSE: Failed to delete database user
deleteDatabaseUsersError=Не вдалося видалити користувачів бази даних
#XMSG: Success message after deleting database user
deleteDatabaseUserSuccess=Користувача бази даних видалено
#XMSG: Success message after deleting database users
deleteDatabaseUsersSuccess=Користувачів бази даних видалено
#XMSG: Success message after creating database user
createDatabaseUserSuccessMessage=Користувача бази даних створено
#YMSE: Failed to create database user
createDatabaseUserError=Не вдалося створити користувача бази даних
#XMSG: Success message after updating database user
updateDatabaseUserSuccessMessage=Користувача бази даних оновлено
#YMSE: Failed to update database user
updateDatabaseUserError=Не вдалося оновити користувача бази даних
#XFLD: HDI Consumption
hdiConsumption=Споживання HDI
#XTIT: Space detail section Database Access title
detailsSectionDatabaseAccess=Доступ до бази даних
#XTXT: Database users consumption hint
databaseUsersConsumptionHint=Зробіть, щоб дані у вашому просторі споживалися усталено. Моделі в конструкторах автоматично дозволять споживати дані усталено.
#XFLD: Default Consumption of Space Data:
defaultConsumptionOfSpaceData=Усталене споживання даних у просторі:
#XFLD: Database User Name
databaseUserName=Ім’я користувача бази даних
#XMSG: Database User creation validation error message
databaseUserValidationError=Схоже, деякі поля недійсні. Перевірте обов’язкові поля, щоб продовжити.
#XMSG: SQL modeling can’t be enabled since this user has been migrated.
migratedDatabaseUserHint=Поглинання даних неможливо ввімкнути, оскільки цього користувача було переміщено.
#XBUT: Remove Button Text
remove=Видалити
#XBUT: Remove Spaces Button Text
removeSpaces=Вилучити простори
#XBUT: Remove Objects Button Text
removeObjects=Вилучити об'єкти
#XMSG: No members have been added yet.
noMembersAssigned=Учасників ще не додано.
#XMSG: No users have been added yet.
noUsersAssigned=Користувачів ще не додано.
#XMSG: No database users have been created or your filter is not showing any data.
noDatabaseUsers=Користувачів бази даних не створено, або фільтр не показує жодних даних.
#XMSG: Please enter a user name.
noDatabaseUsername=Введіть ім’я користувача.
#XMSG: The user name is too long. Please use a shorter one.
databaseUsernameTooLong=Ім’я користувача задовге. Використовуйте коротше ім’я.
#XMSG: No privileges have been enabled and this database user will have limited functionality. Do you still want to continue?
noPrivilegesConfirmationMessage=Жодних прав не активовано, і цей користувач бази даних матиме обмежений функціонал. Справді продовжити?
#XMSG: To enable audit logs for change operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogChangeMessage=Щоб увімкнути журнали аудиту для операцій змінення, потрібно також увімкнути поглинання даних. Справді зробити це?
#XMSG: To enable audit logs for read operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogReadMessage=Щоб увімкнути журнали аудиту для операцій читання, потрібно також увімкнути поглинання даних. Справді зробити це?
#XMSG: To enable HDI consumption data ingestion and data consumption needs to be enabled. Do you want to enable all?
enableHDIConsumptionMessage=Щоб увімкнути споживання HDI потрібно також увімкнути поглинання даних і споживання даних. Справді зробити це?
#XMSG:
databaseUserPasswordText=Щоб установити з’єднання з цим користувачем бази даних, скопіюйте свій пароль. Якщо ви забули свій пароль, ви завжди можете подати запит на новий.
#XTIT: Space detail section members title
detailsSectionMembers=Учасники
#XMSG: New password set
newPasswordSet=Новий пароль задано
#XFLD: Data Ingestion
dataIngestion=Поглинання даних
#XFLD: Data Consumption
dataConsumption=Споживання даних
#XFLD: Privileges
privileges=Права
#XFLD: Enable Data ingestion
enableDataIngestion=Увімкнути поглинання даних
#XMSG: Log the read and change operations for data ingestion.
databaseUserAuditHint=Реєструйте операції читання та зміни для поглинання даних.
#XMSG: Make your space data available in your HDI containers.
databaseUserHDIHint=Переконайтеся, що дані простору доступні у ваших контейнерах HDI.
#XFLD: Enable Data consumption
enableDataConsumption=Активувати споживання даних
#XMSG: Allow other apps or tools to consume your space data.
enableDataConsumptionHint=Дозвольте іншим програмам або інструментам споживати дані вашого простору.
#XMSG: To set up access via this schema user copy the credentials to your user-provided service. In case you can only copy the credentials without a password make sure to add the password later.
databaseUserHDIConsumptionHint=Щоб налаштувати доступ через користувача бази даних, скопіюйте облікові дані в надану користувачем службу. Якщо ви можете скопіювати лише облікові дані без пароля, обов’язково додайте пароль пізніше.
#XFLD: Data Flow Runtime Capacity label in space overview
dataFlowRuntimeCapacity=Час виконання потоку даних ({0}:{1} год. з {2} год.)
#XFLD: Could not load Data Flow Runtime Capacity
dataFlowRuntimeCapacityError=Не вдалося завантажити час виконання потоку даних
#XMSG: User can grant data consumption to other users.
databaseUserConsumptionWithGrantOptionHint=Користувач може надавати споживання даних іншим користувачам.
#XFLD: Enable data consumption with grant option
enableConsumptionWithGrantOption=Увімкніть споживання даних за допомогою параметра надання.
#XMSG: To enable data consumption with grant option data consumption needs to be enabled. Do you want to enable both?
enableConsumptionWithGrantMessage=Щоб увімкнути споживання даних за допомогою параметра надання, потрібно ввімкнути споживання даних. Справді активувати обидва параметри?
#XFLD: Enable Automated Predictive Library (APL) and Predictive Analysis Library (PAL)
allowUsingScriptServerFunctionality=Увімкнути автоматизовану бібліотеку прогнозування (APL) та бібліотеку прогнозного аналізу (PAL)
#XMSG: User can use SAP HANA Cloud embedded machine learning functions.
allowUsingScriptServerFunctionalityHint=Користувач може використовувати вбудовані функції машинного навчання SAP HANA Cloud.
#XFLD: Password Policy
passwordPolicy=Політика для паролів
#XMSG: Password Policy
passwordPolicyHint=Увімкніть або вимкніть налаштовану політику для паролів тут.
#XFLD: Enable Password Policy
enablePasswordPolicy=Увімкнути політику для паролів
#XMSG: Read Access to the Space Schema
readAccessTitle=Доступ для читання до схеми простору
#XMSG: read access hint
readAccessHint=Дозвіл користувачу бази даних підключати зовнішні інструменти до схеми простору та читати подання, доступні для споживання.
#XFLD: Space Schema
spaceSchema=Схема простору
#XFLD: Enable Read Access (SQL)
enableReadAccess=Увімкнути доступ для читання (SQL)
#XMSG: Allow the user to grant read access to other users.
withGrantOptionHint=Дозвіл користувачу надавати іншим користувачам доступ для читання.
#XFLD: With Grant Option
withGrantOption=З можливістю надання
#XMSG: Make your space data available in your HDI containers.
enableHDIConsumptionHint=Переконайтеся, що дані простору доступні у ваших контейнерах HDI.
#XFLD: Enable HDI Consumption
enableHDIConsumption=Увімкнути споживання HDI
#XMSG: Write Access to the User’s Open SQL Schema
writeAccessTitle=Доступ для запису до схеми Open SQL користувача
#XMSG: write access hint
writeAccessHint=Дозвіл користувачу бази даних підключати зовнішні інструменти до схеми Open SQL користувача, щоб створювати сутності даних і приймати дані для використання в просторі.
#XFLD: Open SQL Schema
openSQLSchema=Відкрити схему Open SQL
#XFLD: Enable Write Access (SQL DDL & DML)
enableWriteAccess=Увімкнути доступ для запису (SQL, DDL і DML)
#XMSG: audit hint
auditHint=Запис операцій читання та зміни в схему Open SQL.
#XMSG: data consumption hint
dataConsumptionHint=Відкриття всіх нових подань у просторі для усталеного споживання. Розробники моделей можуть перепризначити цей параметр для окремих подань за допомогою перемикача "Відкрити для споживання" на бічній панелі виведення простору. Також можна вибрати формати, в яких доступні подання.
#XFLD: Expose for Consumption by Default
exposeDataConsumption=Відкрити для споживання усталено
#XMSG: database users hint consumption hint
databaseUsersHint2New=Створення користувачів бази даних для підключення зовнішніх інструментів до SAP Datasphere. Встановлення дозволів користувачам для читання даних просторів і створення сутностей даних (DDL), а також приймання даних (DML) для використання в просторі.
#XFLD: Read
read=Зчитати
#XFLD: Read (HDI)
readHDI=Зчитати (HDI)
#XFLD: Write
write=Записати
#XMSG: HDI Containers Hint
HDIContainersHint2=Увімкнення доступу до контейнерів SAP HANA Deployment Infrastructure (HDI) у вашому просторі. Розробники моделей можуть використовувати артефакти HDI як джерела для подань, а клієнти HDI можуть отримати доступ до даних простору.
#XMSG: Open info dialog
openDatabaseUserInfoDialog=Відкрити інформаційне діалогове вікно
#XMSG: Database user is locked. Open info dialog to unlock
openLockedDatabaseUserInfoDialog=Користувача бази даних заблоковано. Щоб розблокувати його, відкрийте діалогове вікно.
#XFLD: Table
table=Таблиця
#XFLD: Space section partner configuration title
partnerConnectionConfigurationSectionTitle=З’єднання партнера
#XFLD: Space section partner configuration subtitle
partnerConnectionConfigurationSectionSubTitle=Конфігурація з’єднання партнера
#XFLD: Space section partner configuration hint
partnerConnectionConfigurationSectionHint=Визначте власну плитку з’єднання партнера, додавши URL-адресу та піктограму iFrame. Ця конфігурація доступна лише для цього орендаря.
#XFLD: Table Name Field
partnerConnectionConfigurationName=Назва плитки
#XFLD: Table iFrame URL field
partnerConnectionConfigurationiFrameURL=URL-адреса iFrame
#XFLD: Table iFrame Post Message Origin field
partnerConnectionConfigurationiFramePostMessageOrigin=Джерело повідомлення Post iFrame
#XFLD: Table icon/image name
partnerConnectionConfigurationIcon=Піктограма
#XMSG: Table no result found
partnerConnectionConfigurationNotfound=Не вдалося знайти конфігурації з’єднання партнера.
#XMSG: Partner Connection Confgurations are not available
partnerConnectionConfigurationNotAvailable=Конфігурації з'єднання партнера не можна відобразити, якщо база даних середовища виконання недоступна.
#XFLD: Dialog Create Partner Connection Configuration
partnerConnectionConfigurationDialogTitleAdd=Створити конфігурацію з’єднання партнера
#XFLD: Dialog Create Partner Connection Configuration Upload Icon
partnerConnectionConfigurationButtonUloadPartnerIcon=Вивантажити піктограму
#XFLD: Dialog Create Partner Connection Configuration Select
partnerConnectionConfigurationSelectIcon=Виберіть (макс. розмір: 200 КБ)
#XFLD: Dialog Create Partner Connection Configuration Enter a name
partnerConnectionConfigurationNamePlaceholder=Приклад плитки партнера
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL
partnerConnectionConfigurationiFrameURLPlaceholder=http://www.example.com
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL Check
partnerConnectionConfigurationiFramePostMessageOriginPlaceholder=.example.com
#XFLD: Dialog Create Partner Connection Configuration Browse
partnerConnectionConfigurationBrowseButton=Огляд
#XFLD: Create Partner Connection Configuration Msg
partnerConnectionConfigurationUploadMsg=Конфігурацію з’єднання партнера успішно створено.
#XMSG: Error while deleting Partner Connection configuration(s)
partnerConnectionConfigurationDeleteError=Під час видалення конфігурацій з’єднання партнера сталася помилка.
#XMSG: Partner Connection Configuration has been successfully deleted
partnerConnectionConfigurationDeleteSuccess=Конфігурацію з’єднання партнера успішно видалено.
#XMSG: Error while retrieving Partner Connection Configurations
partnerConnectionConfigurationLoadError=Під час отримання конфігурацій з’єднання партнера сталася помилка.
#XMSG: Error fileSizeLimitExceeded
partnerConnectionConfigurationfileSizeLimitExceeded=Не вдалося вивантажити файл, оскільки він перевищує максимальний розмір у 200 КБ.
#XBUT: Create Partner Connection Configuration
partnerConnectionConfigurationButtonCreate=Створити конфігурацію з’єднання партнера
#XBUT: Delete Partner Connection Configuration
partnerConnectionConfigurationButtonDelete=Видалити конфігурацію з’єднання партнера
#XMSG: Partner Tile Creation Failure
upsertPartnerTileFailed=Не вдалося створити плитку партнера.
#XMSG: Partner Tile Deletion Failure
deletePartnerTileFailed=Не вдалося видалити плитку партнера.
#XMSG: HANA cloud connector settings reset failure
resetHANASCCSettingsFailed=Не вдалося скинути користувацькі настроювання для хмарного конектора SAP HANA
#XFLD: Workload Class
workloadClass=Клас робочого навантаження
#XFLD: Workload Management
workloadManagement=Керування робочим навантаженням
#XFLD: Priority
workloadClassPriority=Пріоритет
#XMSG:
workloadManagementPriorityHint=Ви можете вказати пріоритет цього простору під час запиту до бази даних. Введіть значення від 1 (найнижчий пріоритет) до 8 (найвищий пріоритет). У ситуації, коли простори конкурують за доступні потоки, простори з більш високим пріоритетом запускаються раніше просторів із нижчим пріоритетом.
#XMSG:
workloadClassPriorityHint=Можна встановити для простору пріоритет від 0 (найнижчий) до 8 (найвищий). Інструкції простору з високим пріоритетом виконуються раніше інструкцій інших просторів із нижчим пріоритетом. Усталений пріоритет – 5. Оскільки значення 9 зарезервовано для системних операцій, цей пріоритет недоступний для простору.
#XFLD: Statement Limits
workloadclassStatementLimits=Обмеження для інструкцій
#XFLD: Workload Configuration
workloadConfiguration=Конфігурація робочого навантаження
#XMSG:
workloadClassStatementLimitsHint=Щоб запобігти споживанню надто великого обсягу пам’яті під час одночасного виконання інструкцій у просторі, можна вказати максимальну кількість (або відсоток) потоків і обсяг пам’яті, що їх можуть споживати такі інструкції. Ви можете призначити будь-яке фіксоване значення або відсоток від 0 (без обмежень) до максимального значення для пам’яті та потоків, доступних в орендарі. \n\n Якщо ви вказали обмеження для потоків, пам’ятайте, що це може знизити ефективність. \n\n Якщо ви вказали обмеження для пам’яті, інструкції, які досягнуть обмеження щодо пам’яті, виконуватися не будуть.
#XMSG:
workloadClassStatementLimitsDescription=Усталена конфігурація забезпечує значні обмеження ресурсів, не допускаючи перевантаження системи будь-яким простором.
#XMSG:
workloadClassStatementLimitCustomDescription=Ви можете задати обмеження для максимальної загальної кількості потоків і пам’яті, які можуть споживати інструкції, що виконуються одночасно в просторі.
#XMSG:
totalStatementThreadLimitHelpText=Занизьке обмеження кількості потоків може вплинути на продуктивність інструкцій, тоді як зависокі значення або 0 можуть дозволити простору використовувати всі доступні системні потоки.
#XMSG:
totalStatementMemoryLimitHelpText=Занизькі обмеження щодо пам’яті можуть призвести до проблем із нестачею пам’яті, тоді як зависокі значення або 0 можуть дозволити простору використовувати всю доступну системну пам’ять.
#XMSG:
totalStatementThreadLimitHelpTextRestricted=Введіть відсоток від 1% до 70% (або еквівалентне число) від загальної кількості потоків, доступних у вашому клієнті. Встановлення занизької межі потоків може вплинути на продуктивність інструкцій, тоді як зависокі значення можуть вплинути на продуктивність інструкцій в інших просторах.
#XMSG:
totalStatementThreadLimitHelpTextDynamic=Введіть відсоток від 1% до {0}% (або еквівалентне число) від загальної кількості потоків, доступних у вашому клієнті. Встановлення занизької межі потоків може вплинути на продуктивність інструкцій, тоді як зависокі значення можуть вплинути на продуктивність інструкцій в інших просторах.
#XMSG:
totalStatementMemoryLimitHelpTextNew=Введіть значення або відсоток від 0 (без обмежень) до загального обсягу пам’яті, доступного у вашому орендарі. Встановлення замалого обсягу пам’яті може вплинути на продуктивність інструкцій, тоді як зависокі значення пам’яті можуть вплинути на продуктивність інструкцій в інших просторах.
#XFLD: Total Statement Thread Limit
totalStatementThreadLimit=Загальний поріг потоків для інструкцій
#XFLD: Segmented button label
workloadclassPercentage=%
#XFLD: Segmented button label
workloadclassGigaByte=ГБ
#XFLD, 80: Segmented button label
workloadclassThreads=Потоків
#XFLD: Total Statement Memory Limit
totalStatementMemoryLimit=Загальний поріг пам’яті для інструкцій
#YMSE: Error while loading customer hana info
loadCustomerHanaInfoError=Не вдалося завантажити відомості про клієнта SAP HANA.
#XMSG:
minimumLimitReached=Досягнуто мінімального значення.
#XMSG:
maximumLimitReached=Досягнуто максимального значення.
#XMSG: Name Taken for Technical Name
technical-name-taken=Зв’язок із таким введеним технічним іменем вже існує. Введіть інше ім’я.
#XMSG: Name Too long for Technical Name
technical-name-too-long=Довжина введеного технічного імені перевищує 40 символів. Введіть коротше ім’я.
#XMSG: Technical name field empty
technical-name-field-empty=Введіть технічне ім’я.
#XMSG: Generic error when user enters invalid characters for a field
field-invalid-characters=Для імені можна використовувати лише літери (a–z), цифри (0-9) та символ підкреслення (_).
#XMSG: Generic error when user enters invalid initial characters for a field
field-underscore-trim=Введене ім’я не може починатися або закінчуватися символом підкреслення (_).
#XFLD: Checkbox label for Enable Statement Limits
enableStatementLimits=Увімкнути ліміти інструкції
#XFLD: Connection SubSection Settings Title in space details
connectionSettings=Настройки
#XMSG: Connections tool hint in Space details section
connectionsToolHint=Щоб створити або змінити з’єднання, відкрийте застосунок "З’єднання" на бічній навігаційній панелі або натисніть тут:
#XBUT: Connection list Open Connection Tool Button
button-connectiontool=Перейти до з’єднань
#XFLD: Not deployed label on space tile
notDeployedLabel=Простір ще не розгорнуто.
#XFLD: Not deployed additional text on space tile
notDeployedText=Розгорніть простір
#XFLD: Corrupt space label on space tile
corruptSpace=Щось пішло не так
#XFLD: Corrupt space additional text on space tile
corruptSpaceHint=Спробуйте розгорнути повторно або зверніться до служби підтримки
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Дані журналу аудиту
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Адміністративні дані
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Інші дані
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Дані в просторах
#XMSG: Unlock Space confirmation text
unlockSpaceConfirmation=Справді розблокувати простір?
#XMSG: Lock Space confirmation text
lockSpaceConfirmation=Справді заблоковувати простір?
#XFLD: Lock
lock=Заблокувати
#XFLD: Unlock
unlock=Розблокувати
#XFLD: Locking
locking=Блокування
#XMSG: Success message after locking space
lockSpaceSuccessMsg=Простір заблоковано
#XMSG: Success message after unlocking space
unlockSpaceSuccessMsg=Простір розблоковано
#XMSG: Success message after locking spaces
lockSpacesSuccessMsg=Простори заблоковано
#XMSG: Success message after unlocking spaces
unlockSpacesSuccessMsg=Простори розблоковано
#YMSE: Error while locking a space
lockSpaceError=Неможливо заблокувати простір.
#YMSE: Error while unlocking a space
unlockSpaceError=Неможливо розблокувати простір.
#XTIT: popup title Warning
confirmationWarningTitle=Застереження
#XMSG: Message strip message when a space was locked manually
manualLockedMessage=Простір було заблоковано вручну.
#XMSG: Message strip message when a space was locked due to the amount of generated audit logs
auditLogLockedMessage=Простір було заблоковано системою, оскільки журнали аудиту займають велику кількість ГБ на диску.
#XMSG: Message strip message when a space was locked because the assigned quota was exceeded.
quotaLockedMessage=Простір заблоковано системою, оскільки він споживає більше пам'яті або обсягу сховища на диску, ніж йому виділено.
#XMSG: Unlock Spaces confirmation text
unlockSpacesConfirmation=Справді розблокувати вибрані простори?
#XMSG: Lock Spaces confirmation text
lockSpacesConfirmation=Справді заблокувати вибрані простори?
#XTIT: Scoped role editor dialog title
scopedRoleEditorDialogTitle=Редактор ролей із областю застосування
#XTIT: ECN Management title
ecnManagementTitle=Керування простором й обчислювальним вузлом Elastic
#XFLD: ECNs
ecns=Комп’ютерні вузли Elastic
#XFLD: ECN phase Ready
ecnReady=Готовий
#XFLD: ECN phase Running
ecnRunning=Виконується
#XFLD: ECN phase Initial
ecnInitial=Не готовий
#XFLD: ECN phase Starting
ecnStarting=Початок
#XFLD: ECN phase Starting Failed
ecnStartingFailed=Не вдалося почати
#XFLD: ECN phase Stopping
ecnStopping=Зупиняється
#XFLD: ECN phase Stopping Failed
ecnStoppingFailed=Не вдалося зупинити
#XBTN: Assign Button
assign=Призначити простори
#XBTN: Start Header-Button
start=Запустити
#XBTN: Update Header-Button
repair=Оновити
#XBTN: Stop Header-Button
stop=Зупинити
#XFLD: ECN hours remaining
ecnHoursRemaining=Залишилося 1000 годин
#XFLD: ECN block-hours remaining
ecnBlockHoursRemaining=Залишилося: {0} блок-год.
#XFLD: ECN block-hour remaining
ecnBlockHourRemaining=Залишилася {0} блок-година
#XTIT: ECN creation dialog title
ecnCreationDialogTitle=Створити обчислювальний вузол Elastic
#XTIT: ECN edit dialog title
ecnEditDialogTitle=Редагувати обчислювальний вузол Elastic
#XTIT: ECN deletion dialog title
ecnDeletionDialogTitle=Видалити обчислювальний вузол Elastic
#XTIT: ECN Space assignment dialog title
ecnSpaceAssignmentDialogTitle=Призначити простори
#XFLD: ECN ID
ECNIDLabel=Комп’ютерний вузол Elastic
#XTXT: Selected toolbar text
selectedToolbarText=Вибрано: {0}
#XTIT: Elastic Compute Nodes
ECNslong=Комп’ютерні вузли Elastic
#XTIT: Object assignment - Table header: Objects available
objectsAvailable=Кількість об'єктів
#XTIT: Object assignment - Dialog header text
selectObjects=Виберіть простори та об’єкти, які ви хочете призначити своєму обчислювальному вузлу Elastic:
#XTIT: Object assignment - Table header title: Objects
objects=Об’єкти
#XTIT: Object assignment - Table header: Type
type=Тип
#XMSG: Message strip delete database user audit warning
deleteDatabaseUserWarningMessageStrip=Майте на увазі, що видалення користувача бази даних призведе до видалення всіх створених записів журналу аудиту. Якщо потрібно зберегти записи журналу аудиту, радимо експортувати їх, перш ніж видаляти користувача бази даних.
#XMSG: Message strip delete HDI container audit logs impact
deleteHDIContainerWarningMessageStrip=Майте на увазі, що скасування призначення контейнера HDI із простору призведе до видалення всіх створених записів журналу аудиту. Якщо потрібно зберегти записи журналу аудиту, радимо експортувати їх, перш ніж скасовувати призначення контейнера HDI.
#XTXT: All audit logs
allAuditLogs=Усі записи журналу аудиту, створені для простору
#XMSG: Message strip disable audit warning
disableAuditWarningMessageStrip=Майте на увазі, що вимкнення політики аудиту (операції читання або зміни) призведе до видалення всіх записів журналу аудиту. Якщо потрібно зберегти записи журналу аудиту, радимо експортувати їх, перш ніж вимикати політику аудиту.
#XTIT: No Spaces assigned to ECN Illustration title
noSpacesECNTitle=Ще не призначено ні простори, ні об’єкти
#XTIT: No Spaces assigned to ECN Illustration description
noSpacesECNDescription=Щоб почати працювати з обчислювальним вузлом Elastic, призначте простір або об’єкти для нього.
#XTIT: No Spaces Illustration title
noSpacesTitle=Простір ще не створено
#XTIT: No Spaces Illustration description
noSpacesDescription=Щоб почати збирати дані, створіть простір.
#XTIT: No Spaces Illustration title
noDeletedSpacesTitle=Кошик порожній
#XTIT: No Spaces Illustration description
noDeletedSpacesDescription=Звідси можна відновити видалені простори.
#XTXT:  {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDatabaseUsers=Коли простір буде розгорнуто, цих користувачів буде видалено {0} без можливості відновлення:
#XTIT: Delete database users
deleteDatabaseUsersTitle=Видалення користувачів бази даних
#YMSE: Error if ecn id already exists
ecnIDInputDuplicateError=Ідентифікатор уже існує.
#YMSE: Error if ecn id contains invalid characters
ecnIDInputInvalidError=Використовуйте тільки малі букви a–z і цифри 0–9.
#YMSE: Error if ecn id is too short
ecnIDInputTooShortError=Ідентифікатор має містити щонайменше таку кількість символів: {0}.
#XMSG: ecn id length warning
ecnIdLengthWarning=Перевищено максимальну кількість символів ({0}).
#XFLD: open System Monitor
systemMonitor=Системний монітор
#XFLD: open ECN schedule dialog menu entry
schedule=Розклад
#XFLD: open create ECN schedule dialog
createSchedule=Створити розклад
#XFLD: open change ECN schedule dialog
changeSchedule=Змінити розклад
#XFLD: open delete ECN schedule dialog
deleteSchedule=Видалення розкладу
#XFLD: open self assign ECN schedule dialog
selfAssignSchedule=Призначення мені розкладу
#XFLD: open pause ECN schedule dialog
pauseSchedule=Призупинити розклад
#XFLD: open resume ECN schedule dialog
resumeSchedule=Відновити розклад
#XFLD: View Logs
viewLogs=Переглянути журнали
#XFLD: Compute Blocks
computeBlocks=Блоки обчислень
#XFLD: Memory label in ECN creation dialog
ecnMemory=Пам'ять (ГБ)
#XFLD: Storage label in ECN creation dialog
ecnStorage=Сховище (ГБ)
#XFLD: Number of CPU label in ECN creation dialog
ecnCPUCount=Кількість ЦП
#XFLD: ECN updated by label
changedBy=Автор зміни
#XFLD: ECN updated on label
changedOn=Дата зміни
#XMSG: Success message after creating a Elastic Compute Node
createEcnSuccessMessage=Створено обчислювальний вузол Elastic
#YMSE: Error while creating a Elastic Compute Node
createEcnError=Не вдалося створити обчислювальний вузол Elastic
#XMSG: Success message after updating a Elastic Compute Node
updateEcnSuccessMessage=Оновлено обчислювальний вузол Elastic
#XMSG: Error while updating a Elastic Compute Node
udpateEcnError=Не вдалося оновити обчислювальний вузол Elastic
#XMSG: Success message after deleting a Elastic Compute Node
deleteEcnSuccessMessage=Видалено обчислювальний вузол Elastic
#YMSE: Error while deleting a Elastic Compute Node
deleteEcnError=Не вдалося видалити обчислювальний вузол Elastic
#XMSG: Success message after starting a Elastic Compute Node
startingEcnSuccessMessage=Запуск обчислювального вузла Elastic
#XMSG: Success message after stopping a Elastic Compute Node
stoppingEcnSuccessMessage=Зупинка обчислювального вузла Elastic
#YMSE: Error while starting a Elastic Compute Node
startingEcnError=Не вдалося запустити обчислювальний вузол Elastic
#YMSE: Error while stopping a Elastic Compute Node
stoppingEcnError=Не вдалося зупинити обчислювальний вузол Elastic
#XBUT: Add Object button for an ECN
assignObjects=Додати об'єкти
#XFLD: Enable auto object assignment label
enableAutoObjAssignment=Додавати всі об'єкти автоматично
#XFLD: object type label to be assigned
objectTypeLabel=Тип (використання семантики)
#XFLD: assigned object type label
assignedObjectTypeLabel=Тип
#XFLD: technical name label
TechnicalNameLabel=Технічне ім'я
#XFLD: Object selecttion ECN label
objectSelectionECNLabel=Виберіть об'єкти, які ви хочете додати до обчислювального вузла Elastic
#XTIT: Add objects dialog title
assignObjectsTitle=Присвоювання об'єктів
#XFLD: object label with object count
objectLabel=Об'єкт
#XMSG: No objects available to add message.
noObjectsToAssign=Немає доступних об'єктів для присвоювання.
#XMSG: No objects assigned message.
noAssignedObjects=Не присвоєно жодного об'єкта.
#XTIT: Remove objects popup title
removeObjectsOrSpacesConfirmationTitle=Застереження
#XTIT: Delete ECN popup button and title
deleteECNConfirmationButtonAndTitle=Видалити
#XMSG: Remove objects popup text
removeObjectsConfirmation=Справді вилучити вибрані об’єкти?
#XMSG: Remove spaces popup text
removeSpacesConfirmation=Справді вилучити вибрані простори?
#XBUT: Confirmation button for objects dialog
removeObjectsOrSpacesConfirmationBtn=Вилучити простори
#XMSG: Success message after removing objects
removeObjectsSuccessMessage=Розкриті об'єкти вилучено
#XMSG: Success message after assigning objects
assignObjectsSuccessMessage=Присвоєно розкриті об'єкти
#XFLD: auto object assignment label for space tile
autoObjAssignmentLabel=Усі розкриті об'єкти
#XFLD: Spaces tab label
spacesTabLabel=Простори
#XFLD: Exposed objects tab label
consumptionObjectsTabLabel=Розкриті об'єкти
#XMSG: Success message after unassigning spaces
spacesUnassignedSuccessMessage=Простори вилучено
#XMSG: Success message after unassigning space
spaceUnassignedSuccessMessage=Простір вилучено
#YMSE: Error while assigning / unassigning spaces
spacesAssignUnassignError=Не вдалося присвоїти або вилучити простори.
#YMSE: Error while removing objects
removeObjectsError=Нам не вдалося присвоїти або вилучити об'єкти.
#YMSE: Error while removing object
removeObjectError=Нам не вдалося присвоїти або вилучити об'єкт.
#YMSE: Error when saving the ecn with invalid value
invalidSizingPlanError=Вибране раніше число стало недійсним. Виберіть інше число.
#YMSE: Error when saving the ecn with invalid value
invalidPerformanceClassError=Виберіть дійсний клас продуктивності.
#YMSE: Error when saving the ecn with invalid value
performanceClassNotAvailableError=Попередньо вибраний клас продуктивності "{0}" наразі недійсний. Виберіть дійсний клас продуктивності.
#XTXT: Delete ECN confirmation text
deleteConfirmationTxtECN=Справді видалити обчислювальний вузол Elastic?
#XFLD: tooltip for ? button
help=Довідка
#XFLD: ECN edit button label
editECN=Конфігурувати
#XFLD: Technical type label for ERModel
DWC_ERMODEL=Модель "сутність – зв'язок"
#XFLD: Technical type label for Local Table
DWC_LOCAL_TABLE=Локальна таблиця
#XFLD: Technical type label for Remote Table
DWC_REMOTE_TABLE=Віддалена таблиця
#XFLD: Technical type label for Analytic Model
DWC_ANALYTIC_MODEL=Аналітична модель
#XFLD: Technical type label for Task Chain
DWC_TASKCHAIN=Ланцюжок завдань
#XFLD: Technical type label for Data Flow
DWC_DATAFLOW=Потік даних
#XFLD: Technical type label for Replication Flow
DWC_REPLICATIONFLOW=Потік реплікації
#XFLD: Technical type label for Transformation Flow
DWC_TRANSFORMATIONFLOW=Потік трансформації
#XFLD: Technical type label for Intelligent Lookup
DWC_IDT=Інтелектуальний пошук
#XFLD: Technical type label for Repository
DWC_REPOSITORY_PACKAGE=Репозиторій
#XFLD: Technical type label for Enterprise Search
DWC_ESMODEL=Корпоративний пошук
#XFLD: Technical type label for View
DWC_VIEW=Подання
#XFLD: Technical type label for Data Product
DWC_DATAPRODUCT=Продукт даних
#XFLD: Technical type label for Data Access Control
DWC_DAC=Контроль доступу до даних
#XFLD: Technical type label for Folder
DWC_FOLDER=Папка
#XFLD: Technical type label for Data Entity
DWC_BUSINESS_ENTITY=Бізнес-сутність
#XFLD: Technical type label for Business Entity Variant
DWC_BUSINESS_ENTITY_VARIANT=Варіант бізнес-сутності
#XFLD: Technical type label for Responsibility Scenario
DWC_AUTH_SCENARIO=Сценарій відповідальності
#XFLD: Technical type label for Fact Model
DWC_FACT_MODEL=Модель фактів
#XFLD: Technical type label for Transformation Flow
DWC_PERSPECTIVE=Перспектива
#XFLD: Technical type label for Consumption Model
DWC_CONSUMPTION_MODEL=Модель споживання
#XFLD: Technical type label for Remote Connection
DWC_REMOTECONNECTION=Віддалене з'єднання
#XFLD: Technical type label for Fact Model Variant
DWC_FACT_MODEL_VARIANT=Варіант моделі фактів
#XMSG: Schedule created alert message
createScheduleSuccess=Розклад створено
#XMSG: Schedule updated alert message
updateScheduleSuccess=Розклад оновлено
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Розклад видалено
#XMSG: Schedule takeover success message
ownerChangeScheduleSuccess=Вам присвоєно розклад
#XMSG: Message for starting pausing of schedule
pauseScheduleStarted=Призупинення 1 розкладу
#XMSG: Message for starting resuming of schedule
resumeScheduleStarted=Відновлення 1 розкладу
#XFLD: Segmented button label
availableSpacesButton=Доступно
#XFLD: Segmented button label
selectedSpacesButton=Вибрано
#XFLD: Visit website button text
visitWebsite=Відвідати вебсайт
#XMSG: Warning message on unselecting source language
sourceLanguageRemovalWarning=Вибрану раніше вихідну мову буде вилучено.
#XBUT: enable translation confirmation button
enableTranslationConfirmationBtn=Увімкнути
#XFLD: ECN performance class label
performanceClassLabel=Клас продуктивності
#XTXT performance class memory text
memoryText=Пам'ять
#XTXT performance class compute text
computeText=Розрахувати
#XTXT performance class high-compute text
highComputeText=Ресурсоємні обчислення
#XBUT: Recycle Bin Button Text
recycleBin=Кошик
#XBUT: Restore Button Text
restore=Відновити
#XMSG: Warning message for new Workload Management UI
priorityWarning=Ця область доступна лише для читання. Пріоритет простору можна змінити в області "Система / Конфігурація / Керування робочим навантаженням"
#XMSG: Warning message for new Workload Management UI
workloadWarning=Ця область доступна лише для читання. Конфігурацію робочого навантаження простору можна змінити в області "Система / Конфігурація / Керування робочим навантаженням"
#XCOL: Space table-view column: Apache Spark vCPUs
sparkManagementVCPUs=Віртуальні ЦП Apache Spark
#XCOL: Space table-view column: Apache Spark Memory (GB)
sparkManagementMemory=Пам'ять Apache Spark (ГБ)
#XCOL: Space type (from Space Capability) for Data Ingestion Spaces
ingestionSpaceType=Приймання продукту даних
#XFLD: Deployment in progress nodatatext for spark table
sparkTableDeploymentInProgress=Немає доступних даних, оскільки простір наразі розгорнуто
#XFLD: Runtime data being loaded nodatatext for spark table
sparkTableDeploymentLoading=Немає доступних даних, оскільки простір наразі завантажено
#XCOL: Space type (from Space Capability) for BW Private Cloud Edition
BWPCESpaceType=SAP BW
#XBUT: Go to Instance Mappings Button
navToMappingsButton=Редагувати меппінги інстанцій
#XFLD: SAP HANA Data Lake Storage label if less than 1GB
dataLakeStorageUsed=< 1 ГБ
