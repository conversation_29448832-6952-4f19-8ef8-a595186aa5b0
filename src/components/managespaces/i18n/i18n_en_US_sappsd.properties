#DO NOT REMOVE________________________
#XFLD: Breadcrumb for space monitoring page
spaceMonitorBreadcrumb=[[[Μŏŋįţŏŗįŋğ∙∙∙∙]]]
#XTXT: Type name for spaces in browser tab page title
space=[[[Ŝρąċē∙∙∙∙∙∙∙∙∙]]]
#_____________________________________
#XFLD: Spaces label in
spaces=[[[Ŝρąċēş∙∙∙∙∙∙∙∙]]]
#XFLD: Manage plan button text
manageQuotaButtonText=[[[Μąŋąğē Ƥĺąŋ∙∙∙∙∙∙∙∙]]]
#XBUT: Manage resources button
manageResourcesButton=[[[Μąŋąğē Řēşŏűŗċēş∙∙∙∙∙∙∙∙]]]
#XFLD: Create space button tooltip
createSpace=[[[Ĉŗēąţē Ŝρąċē∙∙∙∙∙∙∙]]]
#XFLD: Create
create=[[[Ĉŗēąţē∙∙∙∙∙∙∙∙]]]
#XFLD: Deploy
deploy=[[[Ďēρĺŏŷ∙∙∙∙∙∙∙∙]]]
#XFLD: Page
page=[[[Ƥąğē]]]
#XFLD: Cancel
cancel=[[[Ĉąŋċēĺ∙∙∙∙∙∙∙∙]]]
#XFLD: Update
update=[[[Ůρƌąţē∙∙∙∙∙∙∙∙]]]
#XFLD: Save
save=[[[Ŝąʋē]]]
#XFLD: OK
ok=[[[ŎĶ∙∙]]]
#XFLD: days
days=[[[Ďąŷş]]]
#XFLD: Space tile edit button label
edit=[[[Ĕƌįţ]]]
#XFLD: Auto Assign all objects to space
autoAssign=[[[Āűţŏ Āşşįğŋ∙∙∙∙∙∙∙∙]]]
#XFLD: Space tile open monitoring button label
openMonitoring=[[[Μŏŋįţŏŗ∙∙∙∙∙∙∙]]]
#XFLD: Delete
delete=[[[Ďēĺēţē∙∙∙∙∙∙∙∙]]]
#XFLD: Copy Space
copy=[[[Ĉŏρŷ]]]
#XFLD: Close
close=[[[Ĉĺŏşē∙∙∙∙∙∙∙∙∙]]]
#XCOL: Space table-view column status
status=[[[Ŝţąţűş∙∙∙∙∙∙∙∙]]]
#XFLD: Space status active
activeLabel=[[[Āċţįʋē∙∙∙∙∙∙∙∙]]]
#XFLD: Space status locked
lockedLabel=[[[Ļŏċķēƌ∙∙∙∙∙∙∙∙]]]
#XFLD: Space status critical
criticalLabel=[[[Ĉŗįţįċąĺ∙∙∙∙∙∙]]]
#XFLD: Space status cold
coldLabel=[[[Ĉŏĺƌ]]]
#XFLD: Space status deleted
deletedLabel=[[[Ďēĺēţēƌ∙∙∙∙∙∙∙]]]
#XFLD: Space status unknown
unknownLabel=[[[Ůŋķŋŏŵŋ∙∙∙∙∙∙∙]]]
#XFLD: Space status ok
okLabel=[[[Ĥēąĺţĥŷ∙∙∙∙∙∙∙]]]
#XFLD: Database user expired
expired=[[[Ĕχρįŗēƌ∙∙∙∙∙∙∙]]]
#XFLD: deployed
deployed=[[[Ďēρĺŏŷēƌ∙∙∙∙∙∙]]]
#XFLD: not deployed
notDeployed=[[[Ńŏţ Ďēρĺŏŷēƌ∙∙∙∙∙∙∙]]]
#XFLD: changes to deploy
changesToDeploy=[[[Ĉĥąŋğēş ţŏ Ďēρĺŏŷ∙∙∙∙∙∙∙]]]
#XFLD: pending
pending=[[[Ďēρĺŏŷįŋğ∙∙∙∙∙]]]
#XFLD: designtime error
designtimeError=[[[Ďēşįğŋ-Ţįɱē Ĕŗŗŏŗ∙∙∙∙∙∙∙]]]
#XFLD: runtime error
runtimeError=[[[Řűŋ-Ţįɱē Ĕŗŗŏŗ∙∙∙∙∙]]]
#XFLD: Space created by label
createdBy=[[[Ĉŗēąţēƌ Ɓŷ∙∙∙∙]]]
#XFLD: Space created on label
createdOn=[[[Ĉŗēąţēƌ Ŏŋ∙∙∙∙]]]
#XFLD: Space deployed on label
deployedOn=[[[Ďēρĺŏŷēƌ Ŏŋ∙∙∙∙∙∙∙∙]]]
#XFLD: Space ID label
spaceID=[[[Ŝρąċē ĬĎ∙∙∙∙∙∙]]]
#XFLD: Priority label
priority=[[[Ƥŗįŏŗįţŷ∙∙∙∙∙∙]]]
#XFLD: Space Priority label
spacePriority=[[[Ŝρąċē Ƥŗįŏŗįţŷ∙∙∙∙∙]]]
#XFLD: Space Configuration label
spaceConfiguration=[[[Ŝρąċē Ĉŏŋƒįğűŗąţįŏŋ∙∙∙∙∙]]]
#XFLD: Not available
notAvailable=[[[Ńŏţ Āʋąįĺąƃĺē∙∙∙∙∙∙]]]
#XFLD: WorkloadType default
default=[[[Ďēƒąűĺţ∙∙∙∙∙∙∙]]]
#XFLD: WorkloadType custom
custom=[[[Ĉűşţŏɱ∙∙∙∙∙∙∙∙]]]
#XFLD: Data Lake option label; refers to component SAP HANA Cloud, Data Lake
dataLakeAccess=[[[Ďąţą Ļąķē Āċċēşş∙∙∙∙∙∙∙∙]]]
#XFLD: Translation label
translationLabel=[[[Ţŗąŋşĺąţįŏŋ∙∙∙∙∙∙∙∙]]]
#XFLD: Source language label
sourceLanguageLabel=[[[Ŝŏűŗċē Ļąŋğűąğē∙∙∙∙]]]
#XFLD: Translation CheckBox label
translationCheckBox=[[[Ĕŋąƃĺē Ţŗąŋşĺąţįŏŋ∙∙∙∙∙∙]]]
#XTXT: Popover hint that space has to be deployed to show user details
openDatabaseUserHintPopoverText=[[[Ďēρĺŏŷ şρąċē ţŏ ąċċēşş űşēŗ ƌēţąįĺş.∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XTXT: Popover hint that space has to be deployed to open database explorer
openDatabaseExplorerHintPopoverText=[[[Ďēρĺŏŷ şρąċē ţŏ ŏρēŋ Ďąţąƃąşē Ĕχρĺŏŗēŗ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XTXT: Text for when the Data Lake check box is disabled
dataLakeOptionDisabled=[[[Ŷŏű ċąŋ’ţ űşē ţĥįş şρąċē ţŏ ąċċēşş ţĥē Ďąţą Ļąķē ƃēċąűşē įţ įş ąĺŗēąƌŷ űşēƌ ƃŷ ąŋŏţĥēŗ şρąċē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Data Lake check box text; refers to component SAP HANA Cloud, Data Lake
dataLakeCheckBox=[[[Ůşē ţĥįş şρąċē ţŏ ąċċēşş ţĥē ƌąţą ĺąķē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Space Priority minimum label extension
low=[[[Ļŏŵ∙]]]
#XFLD: Space Priority maximum label extension
high=[[[Ĥįğĥ]]]
#XFLD: Space name label
spaceName=[[[Ŝρąċē Ńąɱē∙∙∙∙]]]
#XFLD: Enable deploy objects checkbox
enableDeployObjects=[[[Ďēρĺŏŷ Ŏƃĵēċţş∙∙∙∙∙]]]
#XTIT: Copy spaces dialog title
copySpaceDialogTitle=[[[Ĉŏρŷ {0}]]]
#XTXT Human readable text for unselected source language
unSelectedSourceLanguage=[[[(Ńŏţ Ŝēĺēċţēƌ)∙∙∙∙∙]]]
#XTXT Human readable text for language code "af"
af=[[[Āƒŗįķąąŋş∙∙∙∙∙]]]
#XTXT Human readable text for language code "ar"
ar=[[[Āŗąƃįċ∙∙∙∙∙∙∙∙]]]
#XTXT Human readable text for language code "bg"
bg=[[[Ɓűĺğąŗįąŋ∙∙∙∙∙]]]
#XTXT Human readable text for language code "ca"
ca=[[[Ĉąţąĺąŋ∙∙∙∙∙∙∙]]]
#XTXT Human readable text for language code "zh"
zh=[[[Ŝįɱρĺįƒįēƌ Ĉĥįŋēşē∙∙∙∙∙∙]]]
#XTXT Human readable text for language code "zf"
zf=[[[Ĉĥįŋēşē∙∙∙∙∙∙∙]]]
#XTXT Human readable text for language code "hr"
hr=[[[Ĉŗŏąţįąŋ∙∙∙∙∙∙]]]
#XTXT Human readable text for language code "cs"
cs=[[[Ĉžēċĥ∙∙∙∙∙∙∙∙∙]]]
#XTXT Human readable text for language code "cy"
cy=[[[Ŵēĺşĥ∙∙∙∙∙∙∙∙∙]]]
#XTXT Human readable text for language code "da"
da=[[[Ďąŋįşĥ∙∙∙∙∙∙∙∙]]]
#XTXT Human readable text for language code "nl"
nl=[[[Ďűţċĥ∙∙∙∙∙∙∙∙∙]]]
#XTXT Human readable text for language code "en-UK"
en-UK=[[[Ĕŋğĺįşĥ (Ůŋįţēƌ Ķįŋğƌŏɱ)∙∙∙∙∙∙]]]
#XTXT Human readable text for language code "en"
en=[[[Ĕŋğĺįşĥ (Ůŋįţēƌ Ŝţąţēş)∙∙∙∙∙∙]]]
#XTXT Human readable text for language code "et"
et=[[[Ĕşţŏŋįąŋ∙∙∙∙∙∙]]]
#XTXT Human readable text for language code "fa"
fa=[[[Ƥēŗşįąŋ∙∙∙∙∙∙∙]]]
#XTXT Human readable text for language code "fi"
fi=[[[Ƒįŋŋįşĥ∙∙∙∙∙∙∙]]]
#XTXT Human readable text for language code "fr-CA"
fr-CA=[[[Ƒŗēŋċĥ (Ĉąŋąƌą)∙∙∙∙]]]
#XTXT Human readable text for language code "fr"
fr=[[[Ƒŗēŋċĥ∙∙∙∙∙∙∙∙]]]
#XTXT Human readable text for language code "de"
de=[[[Ģēŗɱąŋ∙∙∙∙∙∙∙∙]]]
#XTXT Human readable text for language code "el"
el=[[[Ģŗēēķ∙∙∙∙∙∙∙∙∙]]]
#XTXT Human readable text for language code "he"
he=[[[Ĥēƃŗēŵ∙∙∙∙∙∙∙∙]]]
#XTXT Human readable text for language code "hi"
hi=[[[Ĥįŋƌį∙∙∙∙∙∙∙∙∙]]]
#XTXT Human readable text for language code "hu"
hu=[[[Ĥűŋğąŗįąŋ∙∙∙∙∙]]]
#XTXT Human readable text for language code "is"
is=[[[Ĭċēĺąŋƌįċ∙∙∙∙∙]]]
#XTXT Human readable text for language code "id"
id=[[[Ɓąĥąşą Ĭŋƌŏŋēşįą∙∙∙∙∙∙∙∙]]]
#XTXT Human readable text for language code "it"
it=[[[Ĭţąĺįąŋ∙∙∙∙∙∙∙]]]
#XTXT Human readable text for language code "ja"
ja=[[[Ĵąρąŋēşē∙∙∙∙∙∙]]]
#XTXT Human readable text for language code "kk"
kk=[[[Ķąžąķĥ∙∙∙∙∙∙∙∙]]]
#XTXT Human readable text for language code "ko"
ko=[[[Ķŏŗēąŋ∙∙∙∙∙∙∙∙]]]
#XTXT Human readable text for language code "lv"
lv=[[[Ļąţʋįąŋ∙∙∙∙∙∙∙]]]
#XTXT Human readable text for language code "lt"
lt=[[[Ļįţĥűąŋįąŋ∙∙∙∙]]]
#XTXT Human readable text for language code "ms"
ms=[[[Ɓąĥąşą Μēĺąŷű∙∙∙∙∙∙]]]
#XTXT Human readable text for language code "no"
no=[[[Ńŏŗŵēğįąŋ∙∙∙∙∙]]]
#XTXT Human readable text for language code "pl"
pl=[[[Ƥŏĺįşĥ∙∙∙∙∙∙∙∙]]]
#XTXT Human readable text for language code "pt"
pt=[[[Ƥŏŗţűğűēşē (Ɓŗąşįĺ)∙∙∙∙∙]]]
#XTXT Human readable text for language code "pt-PT"
pt-PT=[[[Ƥŏŗţűğűēşē (Ƥŏŗţűğąĺ)∙∙∙∙∙]]]
#XTXT Human readable text for language code "ro"
ro=[[[Řŏɱąŋįąŋ∙∙∙∙∙∙]]]
#XTXT Human readable text for language code "ru"
ru=[[[Řűşşįąŋ∙∙∙∙∙∙∙]]]
#XTXT Human readable text for language code "sr"
sr=[[[Ŝēŗƃįąŋ∙∙∙∙∙∙∙]]]
#XTXT Human readable text for language code "sh"
sh=[[[Ŝēŗƃŏ-Ĉŗŏąţįąŋ∙∙∙∙∙]]]
#XTXT Human readable text for language code "sk"
sk=[[[Ŝĺŏʋąķ∙∙∙∙∙∙∙∙]]]
#XTXT Human readable text for language code "sl"
sl=[[[Ŝĺŏʋēŋįąŋ∙∙∙∙∙]]]
#XTXT Human readable text for language code "es"
es=[[[Ŝρąŋįşĥ∙∙∙∙∙∙∙]]]
#XTXT Human readable text for language code "es-MX"
es-MX=[[[Ŝρąŋįşĥ (Μēχįċŏ)∙∙∙∙∙∙∙∙]]]
#XTXT Human readable text for language code "sv"
sv=[[[Ŝŵēƌįşĥ∙∙∙∙∙∙∙]]]
#XTXT Human readable text for language code "th"
th=[[[Ţĥąį]]]
#XTXT Human readable text for language code "tr"
tr=[[[Ţűŗķįşĥ∙∙∙∙∙∙∙]]]
#XTXT Human readable text for language code "uk"
uk=[[[Ůķŗąįŋįąŋ∙∙∙∙∙]]]
#XTXT Human readable text for language code "vi"
vi=[[[Ʋįēţŋąɱēşē∙∙∙∙]]]
#XTIT: Delete spaces confirmation title
deleteConfirmationHeaderText=[[[Ďēĺēţē Ŝρąċēş∙∙∙∙∙∙]]]
#XTXT: Logically delete space delete confirmation text with space name
logicallyDeleteConfirmationBodyTextWithSpaceName=[[[Āŗē ŷŏű şűŗē ŷŏű ŵąŋţ ţŏ ɱŏʋē şρąċē "{0}" ţŏ ţĥē ŗēċŷċĺē ƃįŋ?]]]
#XTXT: Logically dpace delete confirmation text for multiple spaces
logicallyDeleteConfirmationBodyTextMultiple=[[[Āŗē ŷŏű şűŗē ŷŏű ŵąŋţ ţŏ ɱŏʋē ţĥē {0} şēĺēċţēƌ şρąċēş ţŏ ţĥē ŗēċŷċĺē ƃįŋ?.]]]
#XTXT: Delete space delete confirmation text with space name
deleteConfirmationBodyTextWithSpaceName=[[[Āŗē ŷŏű şűŗē ŷŏű ŵąŋţ ţŏ ƌēĺēţē ţĥē şρąċē "{0}"? Ţĥįş ąċţįŏŋ ċąŋŋŏţ ƃē űŋƌŏŋē.]]]
#XTXT: Delete space delete confirmation text for multiple spaces
deleteConfirmationBodyTextMultiple=[[[Āŗē ŷŏű şűŗē ŷŏű ŵąŋţ ţŏ ƌēĺēţē ţĥē {0} şēĺēċţēƌ şρąċēş? Ţĥįş ąċţįŏŋ ċąŋŋŏţ ƃē űŋƌŏŋē. Ţĥē ƒŏĺĺŏŵįŋğ ċŏŋţēŋţ ŵįĺĺ ƃē {1} ƌēĺēţēƌ:]]]
#XTXT: permanently
permanently=[[[ρēŗɱąŋēŋţĺŷ∙∙∙∙∙∙∙∙]]]
#XTXT: {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDetail=[[[Ţĥē ƒŏĺĺŏŵįŋğ ċŏŋţēŋţ ŵįĺĺ ƃē {0} ƌēĺēţēƌ ąŋƌ ċąŋŋŏţ ƃē ŗēċŏʋēŗēƌ:]]]
#XTXT: Hint Delete space confirmation text
deleteConfirmationHint=[[[Ƥĺēąşē ţŷρē {0} ţŏ ċŏŋƒįŗɱ ţĥē ƌēĺēţįŏŋ.]]]
#XTXT: Delete space confirmation error check spelling
deleteConfirmationErrorCheckSpelling=[[[Ƥĺēąşē ċĥēċķ ŷŏűŗ şρēĺĺįŋğ ąŋƌ ţŗŷ ąğąįŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XTXT: All Spaces
allSpaces=[[[Āĺĺ Ŝρąċēş∙∙∙∙]]]
#XTXT: All data
allData=[[[Āĺĺ ŏƃĵēċţş ąŋƌ ƌąţą ċŏŋţąįŋēƌ įŋ ţĥē şρąċē∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XTXT: All connections
allConnections=[[[Āĺĺ ċŏŋŋēċţįŏŋş ƌēƒįŋēƌ įŋ ţĥē şρąċē∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Space tile selection box tooltip
clickToSelect=[[[Ĉĺįċķ ţŏ Ŝēĺēċţ∙∙∙∙]]]
#XTXT: All database users
allDatabaseUsers=[[[Āĺĺ ŏƃĵēċţş ąŋƌ ƌąţą ċŏŋţąįŋēƌ įŋ ąŋŷ Ŏρēŋ ŜǬĻ şċĥēɱą ąşşŏċįąţēƌ ŵįţĥ ţĥē şρąċē∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: remove members button tooltip
deleteUsers=[[[Řēɱŏʋē Μēɱƃēŗş∙∙∙∙∙]]]
#XTXT: Space long description text
description=[[[Ďēşċŗįρţįŏŋ (4000 Ĉĥąŗąċţēŗş Μąχįɱűɱ)∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Add Members button tooltip
addUsers=[[[Āƌƌ Μēɱƃēŗş∙∙∙∙∙∙∙∙]]]
#XFLD: Add Users button tooltip
addUsersTooltip=[[[Āƌƌ Ůşēŗş∙∙∙∙∙]]]
#XFLD: Edit Users button tooltip
editUsersTooltip=[[[Ĕƌįţ Ůşēŗş∙∙∙∙]]]
#XFLD: Remove Users button tooltip
removeUsersTooltip=[[[Řēɱŏʋē Ůşēŗş∙∙∙∙∙∙∙]]]
#XFLD: Searchfield placeholder
filter=[[[Ŝēąŗċĥ∙∙∙∙∙∙∙∙]]]
#XCOL: Users table-view column health
health=[[[Ĥēąĺţĥ∙∙∙∙∙∙∙∙]]]
#XCOL: Users table-view column access
access=[[[Āċċēşş∙∙∙∙∙∙∙∙]]]
#XFLD: No user found nodatatext
noDataText=[[[Ńŏ Ůşēŗ Ƒŏűŋƌ∙∙∙∙∙∙]]]
#XTIT: Members dialog title
selectUserDialogTitle=[[[Āƌƌ Μēɱƃēŗş∙∙∙∙∙∙∙∙]]]
#XTIT: User dialog title
addUserDialogTitle=[[[Āƌƌ Ůşēŗş∙∙∙∙∙]]]
#XTIT: Delete connections confirmation title
deleteConnectionConfirmationHeaderText=[[[Ďēĺēţē Ĉŏŋŋēċţįŏŋş∙∙∙∙∙∙]]]
#XTIT: Delete single connection confirmation title
deleteConnectionConfirmationHeaderTextSingle=[[[Ďēĺēţē Ĉŏŋŋēċţįŏŋ∙∙∙∙∙∙∙]]]
#XTXT: Delete connection delete confirmation text
deleteConnectionConfirmationBodyText=[[[Āŗē ŷŏű şűŗē ŷŏű ŵąŋţ ţŏ ƌēĺēţē ţĥē şēĺēċţēƌ ċŏŋŋēċţįŏŋş? Ţĥēŷ ŵįĺĺ ƃē ρēŗɱąŋēŋţĺŷ ŗēɱŏʋēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XTIT: Remote source dialog title
selectRemoteConnectionDialogTitle=[[[Ŝēĺēċţ Ĉŏŋŋēċţįŏŋş∙∙∙∙∙∙]]]
#XTIT: Share connection dialog title
connectionSharingDialogTitle=[[[Ŝĥąŗē Ĉŏŋŋēċţįŏŋ∙∙∙∙∙∙∙∙]]]
#XTIT: Connections shared from other spaces table title
sharedConnectionsFromOtherSpaces=[[[Ŝĥąŗēƌ Ĉŏŋŋēċţįŏŋş∙∙∙∙∙∙]]]
#XFLD: Add remote source button tooltip
addRemoteConnections=[[[Āƌƌ Ĉŏŋŋēċţįŏŋş∙∙∙∙]]]
#XFLD: Delete remote source button tooltip
deleteRemoteConnections=[[[Řēɱŏʋē Ĉŏŋŋēċţįŏŋş∙∙∙∙∙∙]]]
#XFLD: Share remote source button tooltip
shareConnections=[[[Ŝĥąŗē Ĉŏŋŋēċţįŏŋş∙∙∙∙∙∙∙]]]
#XFLD: Tile-layout tooltip
tileLayout=[[[Ţįĺē Ļąŷŏűţ∙∙∙∙∙∙∙∙]]]
#XFLD: Table-layout tooltip
tableLayout=[[[Ţąƃĺē Ļąŷŏűţ∙∙∙∙∙∙∙]]]
#XMSG: Success message after creating space
createSpaceSuccessMessage=[[[Ŝρąċē ċŗēąţēƌ∙∙∙∙∙∙]]]
#XMSG: Success message after copying space
copySpaceSuccessMessage=[[[Ĉŏρŷįŋğ şρąċē "{0}" ţŏ şρąċē "{1}"]]]
#XMSG: Success message after deploying space
deploymentSuccessMessage=[[[Ŝρąċē ƌēρĺŏŷɱēŋţ ĥąş ƃēēŋ şţąŗţēƌ∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Success message after Apache Spark update started
sparkUpdateStartedSuccessMessage=[[[Āρąċĥē Ŝρąŗķ űρƌąţē ĥąş şţąŗţēƌ∙∙∙∙∙∙∙∙∙∙]]]
#YMSE: Error while starting Apache Spark update
sparkUpdateStartedErrorMessage=[[[Ƒąįĺēƌ ţŏ űρƌąţē Āρąċĥē Ŝρąŗķ∙∙∙∙∙∙∙∙∙]]]
#XMSG: Success message after updating space
updateSettingsSuccessMessage=[[[Ŝρąċē ƌēţąįĺş űρƌąţēƌ∙∙∙∙∙]]]
#XMSG: Success message after unlocking space
unlockSpaceSuccessMessage=[[[Ŝρąċē űŋĺŏċķēƌ ţēɱρŏŗąŗįĺŷ∙∙∙∙∙∙∙]]]
#XMSG: Success message after deleting a single space
deleteSpaceSuccessMessage=[[[Ŝρąċē ƌēĺēţēƌ∙∙∙∙∙∙]]]
#XMSG: Success message after deleting multiple spaces
deleteSpacesSuccessMessage=[[[Ŝρąċēş ƌēĺēţēƌ∙∙∙∙∙]]]
#XMSG: Success message after restoring a single space
restoreSpaceSuccessMessage=[[[Ŝρąċē ŗēşţŏŗēƌ∙∙∙∙∙]]]
#XMSG: Success message after restoring multiple spaces
restoreSpacesSuccessMessage=[[[Ŝρąċēş ŗēşţŏŗēƌ∙∙∙∙]]]
#YMSE: Error while updating settings
updateSettingsFailureMessage=[[[Ţĥē şρąċē şēţţįŋğş ċŏűĺƌŋ’ţ ƃē űρƌąţēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#YMSE: Error while updating data lake option
dataLakeEnabledInOtherSpace=[[[Ţĥē ƌąţą ĺąķē įş ąĺŗēąƌŷ ąşşįğŋēƌ ţŏ ąŋŏţĥēŗ şρąċē. Ŏŋĺŷ ŏŋē şρąċē ċąŋ ąċċēşş ţĥē ƌąţą ĺąķē ąţ ą ţįɱē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#YMSE: Error while updating data lake option
virtualTablesExists=[[[Ŷŏű ċąŋŋŏţ űŋąşşįğŋ ţĥē ƌąţą ĺąķē ƒŗŏɱ ţĥįş şρąċē ƃēċąűşē ţĥēŗē ąŗē şţįĺĺ ƌēρēŋƌēŋċįēş ţŏ ʋįŗţűąĺ ţąƃĺēş*. Ƥĺēąşē ƌēĺēţē ţĥē ʋįŗţűąĺ ţąƃĺēş ţŏ űŋąşşįğŋ ţĥē ƌąţą ĺąķē ƒŗŏɱ ţĥįş şρąċē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#YMSE: Error while unlocking space
unlockSpaceFailureMessage=[[[Ţĥē şρąċē ċŏűĺƌŋ’ţ ƃē űŋĺŏċķēƌ.∙∙∙∙∙∙∙∙∙∙]]]
#YMSE: Error while creating space
createSpaceError=[[[Ţĥē şρąċē ċŏűĺƌŋ’ţ ƃē ċŗēąţēƌ.∙∙∙∙∙∙∙∙∙]]]
#YMSE: Error while creating duplicate space
createDuplicateSpaceError=[[[Ā şρąċē ŵįţĥ ŋąɱē {0} ąĺŗēąƌŷ ēχįşţş.]]]
#YMSE: Error while deleting a single space
deleteSpaceError=[[[Ţĥē şρąċē ċŏűĺƌŋ’ţ ƃē ƌēĺēţēƌ.∙∙∙∙∙∙∙∙∙]]]
#YMSE: Error details while deleting a single space
deleteSpaceDetailedError=[[[Ŷŏűŗ şρąċē “{0}” įş ŋŏţ ŵŏŗķįŋğ ρŗŏρēŗĺŷ ąŋŷɱŏŗē. Ƥĺēąşē ţŗŷ ţŏ ƌēĺēţē įţ ąğąįŋ. Ĭƒ įţ şţįĺĺ ƌŏēşŋ’ţ ŵŏŗķ, ąşķ ŷŏűŗ ąƌɱįŋįşţŗąţŏŗ ţŏ ƌēĺēţē ŷŏűŗ şρąċē ŏŗ ŏρēŋ ą ţįċķēţ.]]]
#YMSE: Error while deleting a single space in files
deleteSpaceInFilesError=[[[Ţĥē şρąċē ƌąţą įŋ Ƒįĺēş ċŏűĺƌŋ’ţ ƃē ƌēĺēţēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#YMSE: Error while deleting a single space users
deleteSpaceUsersError=[[[Ţĥē űşēŗş ċŏűĺƌŋ’ţ ƃē ŗēɱŏʋēƌ.∙∙∙∙∙∙∙∙∙]]]
#YMSE: Error while deleting a single space schema
deleteSpaceSchemasError=[[[Ţĥē şċĥēɱąş ċŏűĺƌŋ’ţ ƃē ŗēɱŏʋēƌ.∙∙∙∙∙∙∙∙∙∙]]]
#YMSE: Error while deleting a single space connections
deleteSpaceConnectionsError=[[[Ţĥē ċŏŋŋēċţįŏŋş ċŏűĺƌŋ’ţ ƃē ŗēɱŏʋēƌ.∙∙∙∙∙∙∙∙∙∙∙∙]]]
#YMSE: Error while deleting a single space data
deleteSpaceDataError=[[[Ţĥē şρąċē ƌąţą ċŏűĺƌŋ’ţ ƃē ƌēĺēţēƌ.∙∙∙∙∙∙∙∙∙∙∙∙]]]
#YMSE: Error while deleting multiple spaces
deleteSpacesError=[[[Ţĥē şρąċēş ċŏűĺƌŋ’ţ ƃē ƌēĺēţēƌ.∙∙∙∙∙∙∙∙∙∙]]]
#YMSE: Error while restoring a single space
restoreSpaceError=[[[Ţĥē şρąċē ċŏűĺƌŋ’ţ ƃē ŗēşţŏŗēƌ.∙∙∙∙∙∙∙∙∙∙]]]
#YMSE: Error while restoring multiple spaces
restoreSpacesError=[[[Ţĥē şρąċēş ċŏűĺƌŋ’ţ ƃē ŗēşţŏŗēƌ.∙∙∙∙∙∙∙∙∙∙]]]
#YMSE: Error while creating users
createUsersError=[[[Ţĥē űşēŗş ċŏűĺƌŋ’ţ ƃē ąƌƌēƌ.∙∙∙∙∙∙∙∙]]]
#YMSE: Error while removing users
removeUsersError=[[[Ŵē ċŏűĺƌŋ’ţ ŗēɱŏʋē ţĥē űşēŗş.∙∙∙∙∙∙∙∙∙]]]
#YMSE: Error while removing user
removeUserError=[[[ċŏűĺƌŋ’ţ ŗēɱŏʋē ţĥē űşēŗ.∙∙∙∙∙∙∙]]]
#YMSE: Error assigning roles to a user
roleAssignUnAssignError=[[[Ŵē ċŏűĺƌŋ’ţ ąƌƌ ţĥē űşēŗ ţŏ ţĥē şēĺēċţēƌ şċŏρēƌ ŗŏĺē. \\u014B\\u014B Ŷŏű ċąŋŋŏţ ąƌƌ ŷŏűŗşēĺƒ ţŏ ą şċŏρēƌ ŗŏĺē. Ŷŏű ċąŋ ąşķ ŷŏűŗ ąƌɱįŋįşţŗąţŏŗ ţŏ ąƌƌ ŷŏű ţŏ ą şċŏρēƌ ŗŏĺē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#YMSE: Error assigning user to the space
userAssignError=[[[Ŵē ċŏűĺƌŋ’ţ ąşşįğŋ ţĥē űşēŗ ţŏ ţĥē şρąċē. \\u014B\\u014B Ţĥē űşēŗ įş ąĺŗēąƌŷ ąşşįğŋēƌ ţŏ ţĥē ɱąχįɱűɱ ąĺĺŏŵēƌ ŋűɱƃēŗ (100) ŏƒ şρąċēş ąċŗŏşş şċŏρēƌ ŗŏĺēş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#YMSE: Error assigning users to the space
usersAssignError=[[[Ŵē ċŏűĺƌŋ’ţ ąşşįğŋ ţĥē űşēŗş ţŏ ţĥē şρąċē. \\u014B\\u014B Ţĥē űşēŗ įş ąĺŗēąƌŷ ąşşįğŋēƌ ţŏ ţĥē ɱąχįɱűɱ ąĺĺŏŵēƌ ŋűɱƃēŗ (100) ŏƒ şρąċēş ąċŗŏşş şċŏρēƌ ŗŏĺēş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#YMSE: Error while fetching users
fetchUserWithScopedRolesError=[[[Ŵē ċŏűĺƌŋ’ţ ŗēţŗįēʋē ţĥē űşēŗş. Ƥĺēąşē ţŗŷ ąğąįŋ ĺąţēŗ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#YMSE: Error while fetching scoped roles
fetchScopedRolesError=[[[Ŵē ċŏűĺƌŋ’ţ ŗēţŗįēʋē ţĥē şċŏρēƌ ŗŏĺēş.∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#YMSE: Error while fetching members
fetchUserError=[[[Ţĥē ɱēɱƃēŗş ċŏűĺƌŋ’ţ ƃē ƒēţċĥēƌ. Ƥĺēąşē ţŗŷ ąğąįŋ ĺąţēŗ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#YMSE: Error while loading run-time database
loadRuntimeError=[[[Ŵē ċŏűĺƌŋ’ţ ĺŏąƌ įŋƒŏŗɱąţįŏŋ ƒŗŏɱ ţĥē ŗűŋ-ţįɱē ƌąţąƃąşē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#YMSE: Error while loading spaces
loadSpacesError=[[[Ŝŏŗŗŷ, şŏɱēţĥįŋğ ŵēŋţ ŵŗŏŋğ ŵĥēŋ ţŗŷįŋğ ţŏ ŗēţŗįēʋē ŷŏűŗ şρąċēş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#YMSE: Error while loading haas resources
loadStorageError=[[[Ŝŏŗŗŷ, şŏɱēţĥįŋğ ŵēŋţ ŵŗŏŋğ ŵĥēŋ ţŗŷįŋğ ţŏ ŗēţŗįēʋē ţĥē şţŏŗąğē ƌąţą.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#YMSE: Error no data could be loaded
loadDataError=[[[Ŝŏŗŗŷ, şŏɱēţĥįŋğ ŵēŋţ ŵŗŏŋğ ŵĥēŋ ţŗŷįŋğ ţŏ ŗēţŗįēʋē ŷŏűŗ ƌąţą.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Click to refresh storage data
clickToRefresh=[[[Ĉĺįċķ ĥēŗē ţŏ ţŗŷ ąğąįŋ.∙∙∙∙∙∙]]]
#XTIT: Delete space popup title
deleteSpacePopupTitle=[[[Ďēĺēţē Ŝρąċē∙∙∙∙∙∙∙]]]
#XCOL: Spaces table-view column name
name=[[[Ńąɱē]]]
#XCOL: Spaces table-view deployment status
deploymentStatus=[[[Ďēρĺŏŷɱēŋţ Ŝţąţűş∙∙∙∙∙∙∙]]]
#XFLD: Disk label in space details
storageLabel=[[[Ďįşķ (ĢƁ)∙∙∙∙∙]]]
#XFLD: In-Memory label in space details
ramLabel=[[[Μēɱŏŗŷ (ĢƁ)∙∙∙∙∙∙∙∙]]]
#XFLD: Memory label on space card
memory=[[[Μēɱŏŗŷ ƒŏŗ Ŝţŏŗąğē∙∙∙∙∙∙]]]
#XFLD: Storage Assignment label in space details
storrageAssignmentLabel=[[[Ŝρąċē Ŝţŏŗąğē∙∙∙∙∙∙]]]
#XFLD: Storage Type label in space details
storageTypeLabel=[[[Ŝţŏŗąğē Ţŷρē∙∙∙∙∙∙∙]]]
#XFLD: Enable Space Quota
enableSpaceQuota=[[[Ĕŋąƃĺē Ŝρąċē Ǭűŏţą∙∙∙∙∙∙]]]
#XFLD: No Space Quota
noSpaceQuota=[[[Ńŏ Ŝρąċē Ǭűŏţą∙∙∙∙∙]]]
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
diskAndInMemoryText=[[[ŜĀƤ ĤĀŃĀ Ďąţąƃąşē (Ďįşķ ąŋƌ Ĭŋ-Μēɱŏŗŷ)∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
dataLakeFilesText=[[[ŜĀƤ ĤĀŃĀ Ďąţą Ļąķē Ƒįĺēş∙∙∙∙∙∙]]]
#XFLD: Available scoped roles label
availableRoles=[[[Āʋąįĺąƃĺē Ŝċŏρēƌ Řŏĺēş∙∙∙∙∙]]]
#XFLD: Selected scoped roles label
selectedRoles=[[[Ŝēĺēċţēƌ Ŝċŏρēƌ Řŏĺēş∙∙∙∙∙]]]
#XCOL: Spaces table-view column models
models=[[[Μŏƌēĺş∙∙∙∙∙∙∙∙]]]
#XCOL: Spaces table-view column users
users=[[[Ůşēŗş∙∙∙∙∙∙∙∙∙]]]
#XCOL: Spaces table-view column connections
connections=[[[Ĉŏŋŋēċţįŏŋş∙∙∙∙∙∙∙∙]]]
#XFLD: Section header overview in space detail
overview=[[[Ŏʋēŗʋįēŵ∙∙∙∙∙∙]]]
#XFLD: Section header Apache Spark in space detail
sparkManagementSection=[[[Āρąċĥē Ŝρąŗķ∙∙∙∙∙∙∙]]]
#XFLD: Section header Apache Spark in space detail
sparkManagementSubSection=[[[Āρρĺįċąţįŏŋş∙∙∙∙∙∙∙]]]
#XFLD: Section header Apache Spark in space detail
taskAssignmentSubSection=[[[Ţąşķ Āşşįğŋɱēŋţ∙∙∙∙]]]
#XFLD: vCPU label in Apache Spark section
vCPULabel=[[[ʋĈƤŮş∙∙∙∙∙∙∙∙∙]]]
#XFLD: Memory label in Apache Spark section
memoryLabel=[[[Μēɱŏŗŷ (ĢƁ)∙∙∙∙∙∙∙∙]]]
#XFLD: space configuration label in Apache Spark section
spaceConfigurationLabel=[[[Ŝρąċē Ĉŏŋƒįğűŗąţįŏŋ∙∙∙∙∙]]]
#XFLD: Space Source label
sparkApplicationLabel=[[[Āρρĺįċąţįŏŋ∙∙∙∙∙∙∙∙]]]
#XFLD: Cluster Size label
clusterSizeLabel=[[[Ĉĺűşţēŗ Ŝįžē∙∙∙∙∙∙∙]]]
#XFLD: Driver label
driverLabel=[[[Ďŗįʋēŗ∙∙∙∙∙∙∙∙]]]
#XFLD: Executor label
executorLabel=[[[Ĕχēċűţŏŗ∙∙∙∙∙∙]]]
#XFLD: max label
maxLabel=[[[Μąχ. Ůşēƌ∙∙∙∙∙]]]
#XFLD: TrF Default label
trFDefaultLabel=[[[Ţŗąŋşƒŏŗɱąţįŏŋ Ƒĺŏŵ Ďēƒąűĺţ∙∙∙∙∙∙∙∙]]]
#XFLD: Merge Default label
mergeDefaultLabel=[[[Μēŗğē Ďēƒąűĺţ∙∙∙∙∙∙]]]
#XFLD: Optimize Default label
optimizeDefaultLabel=[[[Ŏρţįɱįžē Ďēƒąűĺţ∙∙∙∙∙∙∙∙]]]
#XFLD: Deployment Default label
deploymentDefaultLabel=[[[Ļŏċąĺ Ţąƃĺē (Ƒįĺē) Ďēρĺŏŷɱēŋţ∙∙∙∙∙∙∙∙∙]]]
#XFLD: max spark source label
driverExecutorMaxUsedSparkSource=[[[{0} ĈƤŮ / {1} ĢƁ]]]
#XFLD: Object type label
taskObjectTypeLabel=[[[Ŏƃĵēċţ Ţŷρē∙∙∙∙∙∙∙∙]]]
#XFLD: Task activity label
taskActivityLabel=[[[Āċţįʋįţŷ∙∙∙∙∙∙]]]
#XFLD: Task Application ID label
taskApplicationIDLabel=[[[Ďēƒąűĺţ Āρρĺįċąţįŏŋ∙∙∙∙∙]]]
#XFLD: Section header in space detail
generalSettings=[[[Ģēŋēŗąĺ Ŝēţţįŋğş∙∙∙∙∙∙∙∙]]]
#XMSG: Message strip message when a space is locked
spaceLockedMessageStrip=[[[Ţĥįş şρąċē įş ċűŗŗēŋţĺŷ ĺŏċķēƌ ƃŷ ţĥē şŷşţēɱ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Message strip message deploy warning
deployWarningMessageStrip=[[[Ĉĥąŋğēş įŋ ţĥįş şēċţįŏŋ ŵįĺĺ ƃē ƌēρĺŏŷēƌ įɱɱēƌįąţēĺŷ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Message strip message modify space configuration warning
modifySpaceConfigurationMessageStrip=[[[Ɓē ąŵąŗē ţĥąţ ċĥąŋğįŋğ ţĥēşē ʋąĺűēş ɱąŷ ċąűşē ρēŗƒŏŗɱąŋċē įşşűēş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Button text to unlock the space again
unlockSpace=[[[Ůŋĺŏċķ Ŝρąċē∙∙∙∙∙∙∙]]]
#XFLD: Info text for audit log formatted message
auditLogText=[[[Ĕŋąƃĺē ąűƌįţ ĺŏğş ţŏ ŗēċŏŗƌ ŗēąƌ ŏŗ ċĥąŋğē ąċţįŏŋş (ąűƌįţ ρŏĺįċįēş). Āƌɱįŋįşţŗąţŏŗş ċąŋ ţĥēŋ ąŋąĺŷžē ŵĥŏ ρēŗƒŏŗɱēƌ ŵĥįċĥ ąċţįŏŋ ąţ ŵĥįċĥ ρŏįŋţ įŋ ţįɱē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Message strip  audit warning
auditLogWarningMessageStrip=[[[Āűƌįţ ĺŏğş ċąŋ ċŏŋşűɱē ą ĺąŗğē ąɱŏűŋţ ŏƒ ƌįşķ şţŏŗąğē įŋ ŷŏűŗ ţēŋąŋţ. Ĭƒ ŷŏű ēŋąƃĺē ąŋ ąűƌįţ ρŏĺįċŷ (ŗēąƌ ŏŗ ċĥąŋğē ąċţįŏŋş), ŷŏű şĥŏűĺƌ ŗēğűĺąŗĺŷ ɱŏŋįţŏŗ ƌįşķ şţŏŗąğē űşąğē (ʋįą ţĥē Ďįşķ Ŝţŏŗąğē Ůşēƌ ċąŗƌ įŋ ţĥē Ŝŷşţēɱ Μŏŋįţŏŗ) ţŏ ąʋŏįƌ ƒűĺĺ ƌįşķ ŏűţąğēş, ŵĥįċĥ ċąŋ ĺēąƌ ţŏ şēŗʋįċē ƌįşŗűρţįŏŋş. Ĭƒ ŷŏű ƌįşąƃĺē ąŋ ąűƌįţ ρŏĺįċŷ, ąĺĺ įţş ąűƌįţ ĺŏğ ēŋţŗįēş ŵįĺĺ ƃē ƌēĺēţēƌ. Ĭƒ ŷŏű ŵąŋţ ţŏ ķēēρ ţĥē ąűƌįţ ĺŏğ ēŋţŗįēş, ċŏŋşįƌēŗ ēχρŏŗţįŋğ ţĥēɱ ƃēƒŏŗē ŷŏű ƌįşąƃĺē ţĥē ąűƌįţ ρŏĺįċŷ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Message strip link text to open in app help to guide user what to do to unlock his space permanently
unlockSpaceHelp=[[[Ŝĥŏŵ Ĥēĺρ∙∙∙∙∙]]]
#XMSG: Message strip message when a space will be locked again
spaceTemporarilyUnlockedMessageStrip=[[[Ţĥįş şρąċē ēχċēēƌş įţş şρąċē şţŏŗąğē ąŋƌ ŵįĺĺ ƃē ĺŏċķēƌ įŋ {0} {1}.]]]
#XMSG: Unit for remaining time until space is locked again
hours=[[[ĥŏűŗş∙∙∙∙∙∙∙∙∙]]]
#XMSG: Unit for remaining time until space is locked again
minutes=[[[ɱįŋűţēş∙∙∙∙∙∙∙]]]
#XFLD: Section header in space detail for auditing
detailsSectionAuditing=[[[Āűƌįţįŋğ∙∙∙∙∙∙]]]
#XFLD: Subsection header in space detail for auditing
auditing=[[[Ŝρąċē Āűƌįţ Ŝēţţįŋğş∙∙∙∙]]]
#XFLD: Hot space tooltip
hotSpaceCountTooltip=[[[Ĉŗįţįċąĺ şρąċēş: Ůşēƌ şţŏŗąğē įş ğŗēąţēŗ ţĥąŋ 90%.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Green space tooltip
greenSpaceCountTooltip=[[[Ĥēąĺţĥŷ şρąċēş: Ůşēƌ şţŏŗąğē įş ƃēţŵēēŋ 6% ąŋƌ 90%.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Cold space tooltip
coldSpaceCountTooltip=[[[Ĉŏĺƌ şρąċēş: Ůşēƌ şţŏŗąğē įş 5% ŏŗ ĺēşş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Critical space tooltip
criticalSpaceCountTooltip=[[[Ĉŗįţįċąĺ şρąċēş: Ůşēƌ şţŏŗąğē įş ğŗēąţēŗ ţĥąŋ 90%.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Green space tooltip
okSpaceCountTooltip=[[[Ĥēąĺţĥŷ şρąċēş: Ůşēƌ şţŏŗąğē įş ƃēţŵēēŋ 6% ąŋƌ 90%.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Locked space tooltip
lockedSpaceCountTooltipNew=[[[Ļŏċķēƌ şρąċēş: Ɓĺŏċķēƌ ƌűē ţŏ įŋşűƒƒįċįēŋţ ɱēɱŏŗŷ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Locked space tooltip
lockedSpaceCountTooltip=[[[Ļŏċķēƌ Ŝρąċēş∙∙∙∙∙∙]]]
#YMSE: Error while deleting remote source
deleteRemoteError=[[[Ţĥē ċŏŋŋēċţįŏŋş ċŏűĺƌŋ’ţ ƃē ŗēɱŏʋēƌ.∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Space ID input tooltip
spaceIDInfoTooltip=[[[Ŝρąċē ĬĎ ċąŋŋŏţ ƃē ċĥąŋğēƌ ĺąţēŗ.\\u014BƲąĺįƌ ċĥąŗąċţēŗş Ā - Ż, 0 - 9, ąŋƌ _∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#YMSE: Error when space name is empty
spaceNameEmptyValidationError=[[[Ĕŋţēŗ şρąċē ŋąɱē.∙∙∙∙∙∙∙]]]
#YMSE: Error when ECN Business name is empty
ecnNameEmptyValidationError=[[[Ĕŋţēŗ ą ƃűşįŋēşş ŋąɱē.∙∙∙∙∙]]]
#YMSE: Error when space id is empty
spaceIDInputEmptyValidationError=[[[Ĕŋţēŗ şρąċē ĬĎ.∙∙∙∙]]]
#YMSE: Error when space id contains invalid characters
spaceIDInputInValidError=[[[Ĭŋʋąĺįƌ ċĥąŗąċţēŗş. Ƥĺēąşē űşē Ā - Ż, 0 - 9, ąŋƌ _ ŏŋĺŷ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#YMSE: Error when space id exists while creation
spaceIDInputDuplicateError=[[[Ŝρąċē ĬĎ ąĺŗēąƌŷ ēχįşţş.∙∙∙∙∙∙]]]
#XFLD: Space searchfield placeholder
search=[[[Ŝēąŗċĥ∙∙∙∙∙∙∙∙]]]
#XMSG: Success message after creating users
createUsersSuccess=[[[Ůşēŗş ąƌƌēƌ∙∙∙∙∙∙∙∙]]]
#XMSG: Success message after creating user
createUserSuccess=[[[Ůşēŗ ąƌƌēƌ∙∙∙∙]]]
#XMSG: Success message after updating users
updateUsersSuccess=[[[{0} Ůşēŗş űρƌąţēƌ]]]
#XMSG: Success message after updating user
updateUserSuccess=[[[Ůşēŗ űρƌąţēƌ∙∙∙∙∙∙∙]]]
#XMSG: Success message after removing users
removeUsersSuccess=[[[{0} Ůşēŗş ŗēɱŏʋēƌ]]]
#XMSG: Success message after removing user
removeUserSuccess=[[[Ůşēŗ ŗēɱŏʋēƌ∙∙∙∙∙∙∙]]]
#XFLD: Schema name
schemaName=[[[Ŝċĥēɱą Ńąɱē∙∙∙∙∙∙∙∙]]]
#XFLD: used of total
ofTemplate=[[[{0} ŏƒ {1}]]]
#XFLD: Assigned disk label in space overview
assignedStorageTemplate=[[[Āşşįğŋēƌ Ďįşķ ({0} ŏƒ {1})]]]
#XFLD: Assigned Memory label in space overview
assignedRAMTemplate=[[[Āşşįğŋēƌ Μēɱŏŗŷ ({0} ŏƒ {1})]]]
#XFLD: Storage ratio on space
accelearationRAM=[[[Μēɱŏŗŷ Āċċēĺēŗąţįŏŋ∙∙∙∙∙]]]
#XFLD: No Storage Consumption
noStorageConsumptionText=[[[Ńŏ şţŏŗąğē ƣűŏţą ąşşįğŋēƌ.∙∙∙∙∙∙∙]]]
#XFLD: Used disk label in space overview
usedStorageTemplate=[[[Ďįşķ Ůşēƌ ƒŏŗ Ŝţŏŗąğē ({0} ŏƒ {1})]]]
#XFLD: Used Memory label in space overview
usedRAMTemplate=[[[Μēɱŏŗŷ Ůşēƌ ƒŏŗ Ŝţŏŗąğē ({0} ŏƒ {1})]]]
#XFLD: Used Disk tooltip
usedStorageTooltipTemplate=[[[{0} ŏƒ {1} Ďįşķ Ůşēƌ ƒŏŗ Ŝţŏŗąğē]]]
#XFLD: Used Memory tooltip
usedRAMTooltipTemplate=[[[{0} ŏƒ {1} Μēɱŏŗŷ Ůşēƌ]]]
#XFLD: Assigned Disk tooltip
assignedStorageTooltipTemplate=[[[{0} ŏƒ {1} Ďįşķ Āşşįğŋēƌ]]]
#XFLD: Assigned Memory tooltip
assignedRAMTooltipTemplate=[[[{0} ŏƒ {1} Μēɱŏŗŷ Āşşįğŋēƌ]]]
#XFLD: Space related used storage tooltip
spaceRelatedUsedStorageTooltipTemplate=[[[Ŝρąċē Ďąţą: {0}]]]
#XFLD: Not space related used storage tooltip
otherDataUsedStorageTooltipTemplate=[[[Ŏţĥēŗ Ďąţą: {0}]]]
#XFLD: Storage used exceeded popover and tooltip
storageUsedExceededText=[[[Ĉŏŋşįƌēŗ ēχţēŋƌįŋğ ŷŏűŗ ρĺąŋ, ŏŗ ċŏŋţąċţ ŜĀƤ Ŝűρρŏŗţ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XCOL: Space table-view column used Disk
usedStorage=[[[Ďįşķ Ůşēƌ ƒŏŗ Ŝţŏŗąğē∙∙∙∙∙]]]
#XCOL: Space monitor column used Memory
usedRAM=[[[Μēɱŏŗŷ Ůşēƌ ƒŏŗ Ŝţŏŗąğē∙∙∙∙∙∙]]]
#XCOL: Space monitor column Schema
tableSchema=[[[Ŝċĥēɱą∙∙∙∙∙∙∙∙]]]
#XCOL: Space monitor column Storage Type
tableStorageType=[[[Ŝţŏŗąğē Ţŷρē∙∙∙∙∙∙∙]]]
#XCOL: Space monitor column Table Type
tableType=[[[Ţąƃĺē Ţŷρē∙∙∙∙]]]
#XCOL: Space monitor column Record Count
tableRecordCount=[[[Řēċŏŗƌ Ĉŏűŋţ∙∙∙∙∙∙∙]]]
#XFLD: Assigned Disk
assignedStorage=[[[Ďįşķ Āşşįğŋēƌ ƒŏŗ Ŝţŏŗąğē∙∙∙∙∙∙∙]]]
#XFLD: Assigned Memory
assignedRAM=[[[Μēɱŏŗŷ Āşşįğŋēƌ ƒŏŗ Ŝţŏŗąğē∙∙∙∙∙∙∙∙]]]
#XCOL: Space table-view column storage utilization
tableStorageUtilization=[[[Ůşēƌ Ŝţŏŗąğē∙∙∙∙∙∙∙]]]
#XFLD: space status
spaceStatus=[[[Ŝρąċē Ŝţąţűş∙∙∙∙∙∙∙]]]
#XFLD: space type
spaceType=[[[Ŝρąċē Ţŷρē∙∙∙∙]]]
#XFLD: space type label for space with space type "SAP BW bridge" ("SAP BW bridge" is a product name, do not translate it)
abapbridgeSpaceType=[[[ŜĀƤ ƁŴ ƃŗįƌğē∙∙∙∙∙∙]]]
#XFLD: space type label for space with space type "Data Marketplace"
marketplaceSpaceType=[[[Ďąţą Ƥŗŏʋįƌēŗ Ƥŗŏƌűċţ∙∙∙∙∙]]]
#XMSG: Error message for when selected space cannot be deleted due to the space type
deleteTypedSpaceError=[[[Ŷŏű ċąŋŋŏţ ƌēĺēţē şρąċē {0} ąş įţş şρąċē ţŷρē įş {1}.]]]
#XMSG: Error message for when selected spaces cannot be deleted due to the space type
deleteTypedSpacesError=[[[Ŷŏű ċąŋŋŏţ ƌēĺēţē ţĥē {0} şēĺēċţēƌ şρąċēş. Ŝρąċēş ŵįţĥ ţĥē ƒŏĺĺŏŵįŋğ şρąċē ţŷρēş ċąŋŋŏţ ƃē ƌēĺēţēƌ: {1}.]]]
#XFLD: space type label for space without space type
defaultSpaceType=[[[ŜĀƤ Ďąţąşρĥēŗē∙∙∙∙∙]]]
#XFLD: Tooltip for monitor space button
monitorSpace=[[[Μŏŋįţŏŗ∙∙∙∙∙∙∙]]]
#XFLD: Tooltip for edit space button
editSpace=[[[Ĕƌįţ Ŝρąċē∙∙∙∙]]]
#XMSG: Deletion warning in messagebox
deleteConfirmation=[[[Āŗē ŷŏű şűŗē ŷŏű ŵąŋţ ţŏ ƌēĺēţē ţĥįş şρąċē?∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Tooltip for delete space button
deleteSpace=[[[Ďēĺēţē Ŝρąċē∙∙∙∙∙∙∙]]]
#XFLD: storage
storage=[[[Ďįşķ ƒŏŗ Ŝţŏŗąğē∙∙∙∙∙∙∙∙]]]
#XFLD: username
userName=[[[Ůşēŗ Ńąɱē∙∙∙∙∙]]]
#XFLD: port
port=[[[Ƥŏŗţ]]]
#XFLD: hostname
hostName=[[[Ĥŏşţ Ńąɱē∙∙∙∙∙]]]
#XFLD: password
password=[[[Ƥąşşŵŏŗƌ∙∙∙∙∙∙]]]
#XBUT: Request new password button
requestPassword=[[[Řēƣűēşţ Ńēŵ Ƥąşşŵŏŗƌ∙∙∙∙]]]
#YEXP: Usage explanation in time data section
timeDataSectionHint=[[[Ĉŗēąţē ţįɱē ţąƃĺēş ąŋƌ ƌįɱēŋşįŏŋş ţŏ űşē įŋ ŷŏűŗ ɱŏƌēĺş ąŋƌ şţŏŗįēş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#YEXP: Usage explanation in Space schema section
spaceSchemaHint=[[[Ďŏ ŷŏű ŵąŋţ ţĥē ƌąţą įŋ ŷŏűŗ şρąċē ţŏ ƃē ċŏŋşűɱąƃĺē ƃŷ ŏţĥēŗ ţŏŏĺş ŏŗ ąρρş? Ĭƒ şŏ, ċŗēąţē ŏŋē ŏŗ ɱűĺţįρĺē űşēŗş ţĥąţ ċąŋ ąċċēşş ţĥē ƌąţą įŋ ŷŏűŗ şρąċē ąŋƌ şēĺēċţ ŵĥēţĥēŗ ŷŏű ŵąŋţ ąĺĺ ƒűţűŗē şρąċē ƌąţą ţŏ ƃē ċŏŋşűɱąƃĺē ƃŷ ƌēƒąűĺţ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XTIT: Create schema popup title
createSchemaDialogTitle=[[[Ĉŗēąţē Ŏρēŋ ŜǬĻ Ŝċĥēɱą∙∙∙∙∙]]]
#XTIT: Generate Time Data dialog title
generateTimeDataDialogTitle=[[[Ĉŗēąţē Ţįɱē Ţąƃĺēş ąŋƌ Ďįɱēŋşįŏŋş∙∙∙∙∙∙∙∙∙∙∙]]]
#XTIT: Edit Time Data dialog title
editTimeDataDialogTitle=[[[Ĕƌįţ Ţįɱē Ţąƃĺēş ąŋƌ Ďįɱēŋşįŏŋş∙∙∙∙∙∙∙∙∙∙]]]
#XTIT: Time Data token title
timeDataTokenTitle=[[[Ţįɱē Ďąţą∙∙∙∙∙]]]
#XTIT: Time Data token title
timeDataUpdateViews=[[[Ůρƌąţē Ţįɱē Ďąţą Ʋįēŵş∙∙∙∙∙]]]
#XFLD: Time Data token creation in progress label
timeDataCreationInProgress=[[[Ĉŗēąţįŏŋ įŋ ρŗŏğŗēşş...∙∙∙∙∙∙]]]
#XFLD: Time Data token creation error label
timeDataCreationError=[[[Ĉŗēąţįŏŋ ƒąįĺēƌ. Ƥĺēąşē ţŗŷ ąğąįŋ.∙∙∙∙∙∙∙∙∙∙∙]]]
#XTIT: Time Data dialog time settings title
timeDataDialogTimeSettingsTitle=[[[Ţįɱē Ţąƃĺē Ŝēţţįŋğş∙∙∙∙∙]]]
#XTIT: Time Data dialog time data table title
timeDataDialogTranslationTablesTitle=[[[Ţŗąŋşĺąţįŏŋ Ţąƃĺēş∙∙∙∙∙∙]]]
#XTIT: Time Data dialog dimensions title
timeDataDialogTimeViewsTitle=[[[Ţįɱē Ďįɱēŋşįŏŋş∙∙∙∙]]]
#XFLD: Time Data dialog time range label
timeRangeHint=[[[Ďēƒįŋē ţĥē ţįɱē ŗąŋğē.∙∙∙∙∙]]]
#XFLD: Time Data dialog time data table label
timeDataHint=[[[Ģįʋē ŷŏűŗ ţąƃĺē ą ŋąɱē.∙∙∙∙∙∙]]]
#XFLD: Time Data dialog time data dimensions label
timeDimensionsHint=[[[Ģįʋē ŷŏűŗ ƌįɱēŋşįŏŋş ą ŋąɱē.∙∙∙∙∙∙∙∙]]]
#XFLD: Time Data Time range description label
timerangeLabel=[[[Ţįɱē Řąŋğē∙∙∙∙]]]
#XFLD: Time Data dialog from year label
fromYearLabel=[[[Ƒŗŏɱ Ŷēąŗ∙∙∙∙∙]]]
#XFLD: Time Data dialog to year label
toYearLabel=[[[Ţŏ Ŷēąŗ∙∙∙∙∙∙∙]]]
#XFLD: Time Data dialog calendar type label
calendarTypeLabel=[[[Ĉąĺēŋƌąŗ Ţŷρē∙∙∙∙∙∙]]]
#XFLD: Time Data dialog granularity label
granularityLabel=[[[Ģŗąŋűĺąŗįţŷ∙∙∙∙∙∙∙∙]]]
#XFLD: Time Data dialog technical name label
technicalNameLabel=[[[Ţēċĥŋįċąĺ Ńąɱē∙∙∙∙∙]]]
#XFLD: Time Data dialog quarters text table lable
quarterTextsTableLabel=[[[Ţŗąŋşĺąţįŏŋ Ţąƃĺē ƒŏŗ Ǭűąŗţēŗş∙∙∙∙∙∙∙∙∙]]]
#XFLD: Time Data dialog months text table lable
monthsTextsTableLabel=[[[Ţŗąŋşĺąţįŏŋ Ţąƃĺē ƒŏŗ Μŏŋţĥş∙∙∙∙∙∙∙∙]]]
#XFLD: Time Data dialog days text table lable
daysTextsTableLabel=[[[Ţŗąŋşĺąţįŏŋ Ţąƃĺē ƒŏŗ Ďąŷş∙∙∙∙∙∙∙]]]
#XFLD: Time Data dialog year label
yearLabel=[[[Ŷēąŗ Ďįɱēŋşįŏŋ∙∙∙∙∙]]]
#XFLD: Time Data dialog quarter label
quarterLabel=[[[Ǭűąŗţēŗ Ďįɱēŋşįŏŋ∙∙∙∙∙∙∙]]]
#XFLD: Time Data dialog month label
monthLabel=[[[Μŏŋţĥ Ďįɱēŋşįŏŋ∙∙∙∙]]]
#XFLD: Time Data dialog day label
dayLabel=[[[Ďąŷ Ďįɱēŋşįŏŋ∙∙∙∙∙∙]]]
#XFLD: Time Data dialog gregorian calendar type label
gregorian=[[[Ģŗēğŏŗįąŋ∙∙∙∙∙]]]
#XFLD: Time Data dialog time granularity day label
day=[[[Ďąŷ∙]]]
#XFLD: Time Data dialog business name maxlength reached
timeRangeValueStateTextMaxLengthBusinessName=[[[Μąχ. ĺēŋğţĥ ŏƒ 1000 ċĥąŗąċţēŗş ŗēąċĥēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextMaxRangeExceeded=[[[Ţĥē ɱąχįɱűɱ ţįɱē ŗąŋğē įş 150 ŷēąŗş.∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeLower=[[[“Ƒŗŏɱ Ŷēąŗ” şĥŏűĺƌ ƃē ĺŏŵēŗ ţĥąŋ “Ţŏ Ŷēąŗ”∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeHigher=[[["Ƒŗŏɱ Ŷēąŗ" ɱűşţ ƃē 1900 ŏŗ ĥįğĥēŗ.∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeHigher=[[[“Ţŏ Ŷēąŗ” şĥŏűĺƌ ƃē ĥįğĥēŗ ţĥąŋ “Ƒŗŏɱ Ŷēąŗ”∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeLower=[[[“Ţŏ Ŷēąŗ” ɱűşţ ƃē ĺŏŵēŗ ţĥąŋ ţĥē ċűŗŗēŋţ ŷēąŗ ρĺűş 100∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningFromYear=[[[Ĭŋċŗēąşįŋğ ţĥē "Ƒŗŏɱ Ŷēąŗ" ɱįğĥţ ĺēąƌ ţŏ ƌąţą ĺŏşş∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningToYear=[[[Ļŏŵēŗįŋğ ţĥē "Ţŏ Ŷēąŗ" ɱįğĥţ ĺēąƌ ţŏ ƌąţą ĺŏşş∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Time Data creation validation error message
timeDataValidationError=[[[Ĭţ ĺŏŏķş ĺįķēş şŏɱē ƒįēĺƌş ąŗē įŋʋąĺįƌ. Ƥĺēąşē ċĥēċķ ţĥē ŗēƣűįŗēƌ ƒįēĺƌş ţŏ ċŏŋţįŋűē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Time Data deletion popup text
deleteTimeDataConfirmation=[[[Āŗē ŷŏű şűŗē ŷŏű ŵąŋţ ţŏ ƌēĺēţē ţĥē ƌąţą?∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Time Data creation success message
createTimeDataSuccess=[[[Ţįɱē ƌąţą ċŗēąţēƌ∙∙∙∙∙∙∙]]]
#XMSG: Time Data update success message
updateTimeDataSuccess=[[[Ţįɱē ƌąţą űρƌąţēƌ∙∙∙∙∙∙∙]]]
#XMSG: Time Data delete success message
deleteTimeDataSuccess=[[[Ţįɱē ƌąţą ƌēĺēţēƌ∙∙∙∙∙∙∙]]]
#XMSG: Time Data creation error message
createTimeDataError=[[[Ŝŏɱēţĥįŋğ ŵēŋţ ŵŗŏŋğ ŵĥįĺē ţŗŷįŋğ ţŏ ċŗēąţē ţįɱē ƌąţą.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Time Data update error message
updateTimeDataError=[[[Ŝŏɱēţĥįŋğ ŵēŋţ ŵŗŏŋğ ŵĥįĺē ţŗŷįŋğ ţŏ űρƌąţē ţįɱē ƌąţą.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Time Data creation error message
deleteTimeDataError=[[[Ŝŏɱēţĥįŋğ ŵēŋţ ŵŗŏŋğ ŵĥįĺē ţŗŷįŋğ ţŏ ƌēĺēţē ţįɱē ƌąţą.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Error message when time data could not be retrieved
retrievingTimeDataError=[[[Ţįɱē ƌąţą ċŏűĺƌ ŋŏţ ƃē ĺŏąƌēƌ.∙∙∙∙∙∙∙∙∙]]]
#XTIT: Time Data deletion popup title
deleteTimeDataConfirmationTitle=[[[Ŵąŗŋįŋğ∙∙∙∙∙∙∙]]]
#XTXT: Time Data deletion dependency popup text
deleteTimeDataDependencyText=[[[Ŵē ċŏűĺƌŋ’ţ ƌēĺēţē ŷŏűŗ Ţįɱē Ďąţą ƃēċąűşē įţ įş űşēƌ įŋ ŏţĥēŗ ɱŏƌēĺş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XTXT: Time Data deletion dependency popup text singular
deleteTimeDataDependencyTextSingular=[[[Ŵē ċŏűĺƌŋ’ţ ƌēĺēţē ŷŏűŗ Ţįɱē Ďąţą ƃēċąűşē įţ įş űşēƌ įŋ ąŋŏţĥēŗ ɱŏƌēĺ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Validation error when required time data form field is empty
timeDataRequiredField=[[[Ţĥįş ƒįēĺƌ įş ŗēƣűįŗēƌ ąŋƌ ċąŋ’ţ ƃē ĺēƒţ ēɱρţŷ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Tooltip for Open in Database Explorer button
openInDBX=[[[Ŏρēŋ įŋ Ďąţąƃąşē Ĕχρĺŏŗēŗ∙∙∙∙∙∙∙]]]
#YMSE: Dimension Year
dimensionYearView=[[[Ďįɱēŋşįŏŋ "Ŷēąŗ"∙∙∙∙∙∙∙∙]]]
#YMSE: Dimension Year
dimensionQuarterView=[[[Ďįɱēŋşįŏŋ "Ǭűąŗţēŗ"∙∙∙∙∙]]]
#YMSE: Dimension Year
dimensionMonthView=[[[Ďįɱēŋşįŏŋ "Μŏŋţĥ"∙∙∙∙∙∙∙]]]
#YMSE: Dimension Year
dimensionDayView=[[[Ďįɱēŋşįŏŋ "Ďąŷ"∙∙∙∙]]]
#XFLD: Time Data deletion object title
timeDataUsedIn=[[[(űşēƌ įŋ {0} ɱŏƌēĺş)]]]
#XFLD: Time Data deletion object title singular
timeDataUsedInSingular=[[[(űşēƌ įŋ 1 ɱŏƌēĺ)∙∙∙∙∙∙∙]]]
#XFLD: Time Data deletion table column provider
provider=[[[Ƥŗŏʋįƌēŗ∙∙∙∙∙∙]]]
#XTIT: Time Data dependency dialog title
timeDataDependencyDialogTitle=[[[Ďēρēŋƌēŋċįēş∙∙∙∙∙∙∙]]]
#XTIT: Create schema popup title
createSpaceSchemaUserDialogTitle=[[[Ĉŗēąţē Ůşēŗ ƒŏŗ Ŝρąċē Ŝċĥēɱą∙∙∙∙∙∙∙∙]]]
#XFLD: Create schema button
createSchemaButton=[[[Ĉŗēąţē Ŏρēŋ ŜǬĻ Ŝċĥēɱą∙∙∙∙∙]]]
#XFLD: Generate TimeData button
generateTimeDataButton=[[[Ĉŗēąţē Ţįɱē Ţąƃĺēş ąŋƌ Ďįɱēŋşįŏŋş∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Show dependencies button
showDependenciesButton=[[[Ŝĥŏŵ Ďēρēŋƌēŋċįēş∙∙∙∙∙∙∙]]]
#XFLD: tooltip for disabled buttons when user is not member of space
mustBeMemberOfSpaceForOperation=[[[Ţŏ ρēŗƒŏŗɱ ţĥįş ŏρēŗąţįŏŋ, ŷŏűŗ űşēŗ ɱűşţ ƃē ą ɱēɱƃēŗ ŏƒ ţĥē şρąċē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Create space schema user button
createSpaceSchemaUserButton=[[[Ĉŗēąţē Ŝρąċē Ŝċĥēɱą Ůşēŗ∙∙∙∙∙∙]]]
#YMSE: API Schema users load error
loadSchemaUsersError=[[[Ļįşţ ŏƒ űşēŗş ċŏűĺƌŋ’ţ ƃē ĺŏąƌēƌ.∙∙∙∙∙∙∙∙∙∙∙]]]
#XTIT: API Schema-config popup title
schemaUserDetailsDialogTitle=[[[Ŝρąċē Ŝċĥēɱą Ůşēŗ Ďēţąįĺş∙∙∙∙∙∙∙]]]
#XMSG: API Schema user deletion popup text
deleteSpaceSchemaUserConfirmation=[[[Āŗē ŷŏű şűŗē ŷŏű ŵąŋţ ţŏ ƌēĺēţē ţĥē şēĺēċţēƌ űşēŗ?∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: API Schema user deletion confirmation text
userDeleteSuccess=[[[Ůşēŗ ƌēĺēţēƌ.∙∙∙∙∙∙]]]
#YMSE: API Schema user deletion error
userDeleteError=[[[Ţĥē űşēŗ ċŏűĺƌŋ’ţ ƃē ƌēĺēţēƌ.∙∙∙∙∙∙∙∙∙]]]
#XFLD: User deleted
userDeleted=[[[Ţĥē űşēŗ ĥąş ƃēēŋ ƌēĺēţēƌ.∙∙∙∙∙∙∙]]]
#XTIT: Remove user popup title
removeUserConfirmationTitle=[[[Ŵąŗŋįŋğ∙∙∙∙∙∙∙]]]
#XMSG: Remove user popup text
removeUserConfirmation=[[[Ďŏ ŷŏű ŗēąĺĺŷ ŵąŋţ ţŏ ŗēɱŏʋē ţĥē űşēŗ? Ţĥē űşēŗ ąŋƌ įţş ąşşįğŋēƌ şċŏρēƌ ŗŏĺēş ŵįĺĺ ƃē ŗēɱŏʋēƌ ƒŗŏɱ ţĥē şρąċē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Remove users popup text
removeUsersConfirmation=[[[Ďŏ ŷŏű ŗēąĺĺŷ ŵąŋţ ţŏ ŗēɱŏʋē ţĥē űşēŗş? Ţĥē űşēŗş ąŋƌ ţĥēįŗ ąşşįğŋēƌ şċŏρēƌ ŗŏĺēş ŵįĺĺ ƃē ŗēɱŏʋēƌ ƒŗŏɱ ţĥē şρąċē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XBUT: Confirmation button for remove user dialog
removeUserConfirmationBtn=[[[Řēɱŏʋē∙∙∙∙∙∙∙∙]]]
#YMSE: No data text for available roles
noDataAvailableRoles=[[[Ţĥē şρąċē įş ŋŏţ ąƌƌēƌ ţŏ ąŋŷ şċŏρēƌ ŗŏĺē. \\u014B Ţŏ ƃē ąƃĺē ţŏ ąƌƌ űşēŗş ţŏ ţĥē şρąċē, įţ ɱűşţ ƃē ƒįŗşţ ąƌƌēƌ ţŏ ŏŋē ŏŗ ɱŏŗē şċŏρēƌ ŗŏĺēş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#YMSE: No data text for selected roles
noDataSelectedRoles=[[[Ńŏ şēĺēċţēƌ şċŏρēƌ ŗŏĺēş∙∙∙∙∙∙]]]
#XTIT: Schema-config popup title
schemaDetailsDialogTitle=[[[Ŏρēŋ ŜǬĻ Ŝċĥēɱą Ĉŏŋƒįğűŗąţįŏŋ Ďēţąįĺş∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Label for Read Audit Log
auditLogRead=[[[Ĕŋąƃĺē Āűƌįţ Ļŏğ ƒŏŗ Řēąƌ Ŏρēŗąţįŏŋş∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Label for Change Audit Log
auditLogChange=[[[Ĕŋąƃĺē Āűƌįţ Ļŏğ ƒŏŗ Ĉĥąŋğē Ŏρēŗąţįŏŋş∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Label Audit Log Retention
auditLogRetention=[[[Ķēēρ Ļŏğş ƒŏŗ∙∙∙∙∙∙]]]
#XFLD: Label Audit Log Retention Unit
retentionUnit=[[[Ďąŷş]]]
#YMSE: Error when log retention time is set incorrectly
incorrectRetentionTime=[[[Ĕŋţēŗ ą ŵĥŏĺē ŋűɱƃēŗ ƃēţŵēēŋ {0} ąŋƌ {1}]]]
#XFLD: Label for Cross Space Schema Access
spaceSchemaAccess=[[[Ĉŏŋşűɱē Ŝρąċē Ŝċĥēɱą Ďąţą∙∙∙∙∙∙∙]]]
#XTIT: Confirm deactivation of space schema consumption dialog title
disableSpaceSchemaConsumptionDialogTitle=[[[Ŝţŏρ Ĉŏŋşűɱįŋğ Ŝρąċē Ŝċĥēɱą Ďąţą∙∙∙∙∙∙∙∙∙∙]]]
#XTXT: Confirm deactivation of space schema consumption dialog text
disableSpaceSchemaConsumptionDialogText=[[[Ţĥįş Ŏρēŋ ŜǬĻ şċĥēɱą ɱįğĥţ ċŏŋşűɱē ƌąţą ŏƒ ŷŏűŗ şρąċē şċĥēɱą. Ĭƒ ŷŏű şţŏρ ċŏŋşűɱįŋğ, ɱŏƌēĺş ƃąşēƌ ŏŋ ţĥē şρąċē şċĥēɱą ƌąţą ɱįğĥţ ŋŏţ ŵŏŗķ ąŋŷɱŏŗē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XBUT: Confirmation button for the stop consuming space schema data dialog
stopConsuming=[[[Ŝţŏρ Ĉŏŋşűɱįŋğ∙∙∙∙∙]]]
#XTXT: Tooltip for data lake icon
dataLakeIconTooltip=[[[Ţĥįş şρąċē įş űşēƌ ţŏ ąċċēşş ţĥē ƌąţą ĺąķē∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Tooltip for data lake table entry
dataLakeTableEntryTooltip=[[[Ďąţą Ļąķē Ĕŋąƃĺēƌ∙∙∙∙∙∙∙]]]
#XFLD: used In-Memory linit reached tag
usedRamLimitReachedLabel=[[[Μēɱŏŗŷ Ļįɱįţ Řēąċĥēƌ∙∙∙∙]]]
#XFLD: Space storage Limit reached tag
storageLimitReachedLabel=[[[Ŝţŏŗąğē Ļįɱįţ Řēąċĥēƌ∙∙∙∙∙]]]
#XFLD: Minimal Storage Limit Reached tag
minStorageLimitReachedLabel=[[[Μįŋįɱűɱ Ŝţŏŗąğē Ļįɱįţ Řēąċĥēƌ∙∙∙∙∙∙∙∙∙]]]
#XFLD: Space ram tag
ramLimitReachedLabel=[[[Μēɱŏŗŷ Ļįɱįţ Řēąċĥēƌ∙∙∙∙]]]
#XFLD: Minimal In-Memory Limit Reached tag
minRamLimitReachedLabel=[[[Μįŋįɱűɱ Μēɱŏŗŷ Ļįɱįţ Řēąċĥēƌ∙∙∙∙∙∙∙∙]]]
#XFLD: Popover text after click on space storage limit warning
storageLimitReachedText=[[[Ŷŏű ĥąʋē ŗēąċĥēƌ ţĥē ąşşįğŋēƌ şρąċē şţŏŗąğē ĺįɱįţ ŏƒ {0}. Ƥĺēąşē ąşşįğŋ ɱŏŗē şţŏŗąğē ţŏ ţĥē şρąċē.]]]
#XFLD: System storage tag
systemStorageLimitReachedLabel=[[[Ŝŷşţēɱ Ŝţŏŗąğē Ļįɱįţ Řēąċĥēƌ∙∙∙∙∙∙∙∙]]]
#XMSG: Popover text after click on storage limit warning
systemStorageLimitReachedText=[[[Ŷŏű ĥąʋē ŗēąċĥēƌ ţĥē şŷşţēɱ şţŏŗąğē ĺįɱįţ ŏƒ {0}. Ŷŏű ċąŋ’ţ ąşşįğŋ ąŋŷ ɱŏŗē şţŏŗąğē ţŏ ţĥē şρąċē ŋŏŵ.]]]
#XMSG: Schema deletion warning
deleteSchemaConfirmation=[[[Ďēĺēţįŋğ ţĥįş ŏρēŋ ŜǬĻ şċĥēɱą ŵįĺĺ ąĺşŏ ρēŗɱąŋēŋţĺŷ ƌēĺēţē ąĺĺ ţĥē şţŏŗēƌ ŏƃĵēċţş ąŋƌ ɱąįŋţąįŋēƌ ąşşŏċįąţįŏŋş įŋ ţĥē şċĥēɱą. Ĉŏŋţįŋűē?∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Success message after deleting a schema
schemaDeleteSuccess=[[[Ŝċĥēɱą ƌēĺēţēƌ∙∙∙∙∙]]]
#YMSE: Error while deleting schema.
schemaDeleteError=[[[Ţĥē şċĥēɱą ċŏűĺƌŋ’ţ ƃē ƌēĺēţēƌ.∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Success message after update a schema
schemaUpdateSuccess=[[[Ŝċĥēɱą űρƌąţēƌ∙∙∙∙∙]]]
#YMSE: Error while updating schema.
schemaUpdateError=[[[Ţĥē şċĥēɱą ċŏűĺƌŋ’ţ ƃē űρƌąţēƌ.∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Hint when no password is shown in the dialog.
newPasswordHint=[[[Ŵē’ʋē ρŗŏʋįƌēƌ ą ρąşşŵŏŗƌ ƒŏŗ ţĥįş şċĥēɱą. Ĭƒ ŷŏű’ʋē ƒŏŗğŏţţēŋ ŷŏűŗ ρąşşŵŏŗƌ ŏŗ ĺŏşţ įţ, ŷŏű ċąŋ ŗēƣűēşţ ą ŋēŵ ŏŋē. Řēɱēɱƃēŗ ţŏ ċŏρŷ ŏŗ şąʋē ţĥē ŋēŵ ρąşşŵŏŗƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Hint when showing the password in the dialog.
savePasswordHint=[[[Ƥĺēąşē ċŏρŷ ŷŏűŗ ρąşşŵŏŗƌ. Ŷŏű ŵįĺĺ ŋēēƌ įţ ţŏ şēţ űρ ą ċŏŋŋēċţįŏŋ ţŏ ţĥįş şċĥēɱą. Ĭƒ ŷŏű’ʋē ƒŏŗğŏţţēŋ ŷŏűŗ ρąşşŵŏŗƌ, ŷŏű ċąŋ ŏρēŋ ţĥįş ƌįąĺŏğ ţŏ ŗēşēţ įţ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD,50: Hint on entering schema name in creation dialog
schemaNameHint=[[[!!!Ţĥįş ŋąɱē ċąŋŋŏţ ƃē ċĥąŋğēƌ ąƒţēŗ ţĥē şċĥēɱą įş ċŗēąţēƌ.]]]
#XFLD: Open SQL Schemas section sub headline
schemasSQL=[[[Ŏρēŋ ŜǬĻ∙∙∙∙∙∙]]]
#XFLD: Space schema section sub headline
schemasSpace=[[[Ŝρąċē∙∙∙∙∙∙∙∙∙]]]
#XFLD: HDI Container section header
HDIContainers=[[[ĤĎĬ Ĉŏŋţąįŋēŗş∙∙∙∙∙]]]
#XTXT: Add HDI Containers button tooltip
addHDIContainers=[[[Āƌƌ ĤĎĬ Ĉŏŋţąįŋēŗş∙∙∙∙∙∙]]]
#XTXT: Remove HDI Containers button tooltip
removeHDIContainers=[[[Řēɱŏʋē ĤĎĬ Ĉŏŋţąįŋēŗş∙∙∙∙∙]]]
#XFLD: Enable HDI Containers feature button
enableHDIContainersButton=[[[Ĕŋąƃĺē Āċċēşş∙∙∙∙∙∙]]]
#YMSE: No data text for HDI Containers table
noDataHDIContainers=[[[Ńŏ ĤĎĬ ċŏŋţąįŋēŗş ĥąʋē ƃēēŋ ąƌƌēƌ.∙∙∙∙∙∙∙∙∙∙∙]]]
#YMSE: No data text for Timedata section
noDataTimedata=[[[Ńŏ ţįɱē ţąƃĺēş ąŋƌ ƌįɱēŋşįŏŋş ĥąʋē ƃēēŋ ċŗēąţēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#YMSE: No data text during runtime not available for Timedata section
runtimeNotAvailableNoDataTimedata=[[[Ĉąŋŋŏţ ĺŏąƌ ţįɱē ţąƃĺēş ąŋƌ ƌįɱēŋşįŏŋş ąş ţĥē ŗűŋ-ţįɱē ƌąţąƃąşē įş ŋŏţ ąʋąįĺąƃĺē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#YMSE: No data text during runtime not available for HDI section
runtimeNotAvailableNoDataHDI=[[[Ĉąŋŋŏţ ĺŏąƌ ĤĎĬ Ĉŏŋţąįŋēŗş ąş ţĥē ŗűŋ-ţįɱē ƌąţąƃąşē įş ŋŏţ ąʋąįĺąƃĺē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#YMSE: Error while fetching HDI Containers
fetchHDIContainersError=[[[Ţĥē ĤĎĬ Ĉŏŋţąįŋēŗş ċŏűĺƌŋ’ţ ƃē ŏƃţąįŋēƌ. Ƥĺēąşē ţŗŷ ąğąįŋ ĺąţēŗ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD Table column header for HDI Container names
HDIContainerName=[[[ĤĎĬ Ĉŏŋţąįŋēŗ Ńąɱē∙∙∙∙∙∙]]]
#XFLD: Enable HDI Containers feature dialog title
enableHDIContainersDialogTitle=[[[Ĕŋąƃĺē Āċċēşş∙∙∙∙∙∙]]]
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText1New=[[[Ŷŏű ċąŋ ēŋąƃĺē ŜĀƤ ŜǬĻ Ďąţą Ŵąŗēĥŏűşįŋğ ŏŋ ŷŏűŗ ŜĀƤ Ďąţąşρĥēŗē ţēŋąŋţ ţŏ ēχċĥąŋğē ƌąţą ƃēţŵēēŋ ŷŏűŗ ĤĎĬ ċŏŋţąįŋēŗş ąŋƌ ŷŏűŗ ŜĀƤ Ďąţąşρĥēŗē şρąċēş ŵįţĥŏűţ ţĥē ŋēēƌ ƒŏŗ ƌąţą ɱŏʋēɱēŋţ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText2=[[[Ţŏ ƌŏ ţĥįş, ŏρēŋ ą şűρρŏŗţ ţįċķēţ ƃŷ ċĺįċķįŋğ ţĥē ƃűţţŏŋ ƃēĺŏŵ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText3New=[[[Ŏŋċē ŷŏűŗ ţįċķēţ ĥąş ƃēēŋ ρŗŏċēşşēƌ, ŷŏű ɱűşţ ƃűįĺƌ ŏŋē ŏŗ ɱŏŗē ŋēŵ ĤĎĬ ċŏŋţąįŋēŗş įŋ ţĥē ŜĀƤ Ďąţąşρĥēŗē ŗűŋ-ţįɱē ƌąţąƃąşē. Ţĥēŋ, ţĥē Ĕŋąƃĺē Āċċēşş ƃűţţŏŋ įş ŗēρĺąċēƌ ƃŷ ţĥē + ƃűţţŏŋ įŋ ţĥē ĤĎĬ Ĉŏŋţąįŋēŗş şēċţįŏŋ ƒŏŗ ąĺĺ ŷŏűŗ ŜĀƤ Ďąţąşρĥēŗē şρąċēş, ąŋƌ ŷŏű ċąŋ ąƌƌ ŷŏűŗ ċŏŋţąįŋēŗş ţŏ ą şρąċē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText41=[[[Ńēēƌ ɱŏŗē įŋƒŏŗɱąţįŏŋ? Ģŏ ţŏ %%0. Ƒŏŗ ƌēţąįĺēƌ įŋƒŏŗɱąţįŏŋ ąƃŏűţ ŵĥąţ ţŏ įŋċĺűƌē įŋ ţĥē ţįċķēţ, şēē %%1.]]]
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkText=[[[ŜĀƤ Ĥēĺρ∙∙∙∙∙∙]]]
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkTextSapNote3057059=[[[ŜĀƤ Ńŏţē 3057059∙∙∙∙∙∙∙∙]]]
#XBUT: Open Ticket Button Text
openTicket=[[[Ŏρēŋ Ţįċķēţ∙∙∙∙∙∙∙∙]]]
#XBUT: Add Button Text
add=[[[Āƌƌ∙]]]
#XBUT: Next Button Text
next=[[[Ńēχţ]]]
#XBUT: Edit Button Text
editUsers=[[[Ĕƌįţ]]]
#XBUT: create user Button Text
createUser=[[[Ĉŗēąţē∙∙∙∙∙∙∙∙]]]
#XBUT: Update user Button Text
updateUser=[[[Ŝēĺēċţ∙∙∙∙∙∙∙∙]]]
#XTIT: Add HDI Containers Dialog Title
addHDIContainersDialogTitle=[[[Āƌƌ Ůŋąşşįğŋēƌ ĤĎĬ Ĉŏŋţąįŋēŗş∙∙∙∙∙∙∙∙∙]]]
#XFLD: No data text for add HDI container dialog
addHDIContainersDialogNoDataText=[[[Ŵē ċŏűĺƌŋ’ţ ƒįŋƌ ąŋŷ űŋąşşįğŋēƌ ċŏŋţąįŋēŗş. \\u014B Ţĥē ċŏŋţąįŋēŗ ŷŏű ąŗē ĺŏŏķįŋğ ƒŏŗ ɱąŷ ąĺŗēąƌŷ ƃē ąşşįğŋēƌ ţŏ ą şρąċē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#YMSE: Error while loading assigned HDI Containers
loadAssignedHDIContainersError=[[[Āşşįğŋēƌ ĤĎĬ ċŏŋţąįŋēŗş ċŏűĺƌŋ’ţ ƃē ĺŏąƌēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#YMSE: Error while loading assigned HDI Containers
loadUnassignedHDIContainersError=[[[ĤĎĬ ċŏŋţąįŋēŗş ċŏűĺƌŋ’ţ ƃē ĺŏąƌēƌ.∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Success message
succeededToAddHDIContainer=[[[ĤĎĬ ċŏŋţąįŋēŗ ąƌƌēƌ∙∙∙∙∙]]]
#XMSG: Success message
succeededToAddHDIContainerPlural=[[[ĤĎĬ ċŏŋţąįŋēŗş ąƌƌēƌ∙∙∙∙]]]
#XMSG: Success message
succeededToDeleteHDIContainer=[[[ĤĎĬ ċŏŋţąįŋēŗ ŗēɱŏʋēƌ∙∙∙∙∙]]]
#XMSG: Success message
succeededToDeleteHDIContainerPlural=[[[ĤĎĬ ċŏŋţąįŋēŗş ŗēɱŏʋēƌ∙∙∙∙∙]]]
#XFLD: Time data section sub headline
timeDataSection=[[[Ţįɱē Ţąƃĺēş ąŋƌ Ďįɱēŋşįŏŋş∙∙∙∙∙∙∙]]]
#XFLD: Schemas list entry read audit log enabled indicator
readIndicator=[[[Řēąƌ]]]
#XFLD: Schemas list entry change audit log enabled indicator
changeIndicator=[[[Ĉĥąŋğē∙∙∙∙∙∙∙∙]]]
#XFLD: Remote sources section sub headline
allconnections=[[[Ĉŏŋŋēċţįŏŋ Āşşįğŋɱēŋţ∙∙∙∙∙]]]
#XFLD: Remote sources section sub headline
localconnections=[[[Ļŏċąĺ Ĉŏŋŋēċţįŏŋş∙∙∙∙∙∙∙]]]
#XFLD: Remote sources section sub headline for open Connectors
openConnectors=[[[ŜĀƤ Ŏρēŋ Ĉŏŋŋēċţŏŗş∙∙∙∙∙]]]
#XFLD: User section sub headline
memberassignment=[[[Μēɱƃēŗ Āşşįğŋɱēŋţ∙∙∙∙∙∙∙]]]
#XFLD: User assignment section sub headline
userAssignment=[[[Ůşēŗ Āşşįğŋɱēŋţ∙∙∙∙]]]
#XFLD: User section Access dropdown Member
member=[[[Μēɱƃēŗ∙∙∙∙∙∙∙∙]]]
#XFLD: User assignment section column name
user=[[[Ůşēŗ Ńąɱē∙∙∙∙∙]]]
#XTXT: Selected role count
selectedRoleToolbarText=[[[Ŝēĺēċţēƌ: {0}]]]
#XTIT: Space detail section connections title
detailsSectionConnections=[[[Ĉŏŋŋēċţįŏŋş∙∙∙∙∙∙∙∙]]]
#XTIT: Space detail section data access title
detailsSectionDataAccess=[[[Ŝċĥēɱą Āċċēşş∙∙∙∙∙∙]]]
#XTIT: Space detail section time data title
detailsSectionGenerateData=[[[Ţįɱē Ďąţą∙∙∙∙∙]]]
#XTIT: Space detail section members title
detailsSectionUsers=[[[Μēɱƃēŗş∙∙∙∙∙∙∙]]]
#XTIT: Space detail section Users title
detailsSectionUsersTitle=[[[Ůşēŗş∙∙∙∙∙∙∙∙∙]]]
#XTIT: Out of Storage
insufficientStoragePopoverTitle=[[[Ŏűţ ŏƒ Ŝţŏŗąğē∙∙∙∙∙]]]
#XTIT: Storage distribution
storageDistributionPopoverTitle=[[[Ďįşķ Ŝţŏŗąğē Ůşēƌ∙∙∙∙∙∙∙]]]
#XTXT: Out of Storage popover text
insufficientStorageText=[[[Ţŏ ċŗēąţē ą ŋēŵ şρąċē, ρĺēąşē ŗēƌűċē ţĥē ąşşįğŋēƌ şţŏŗąğē ŏƒ ąŋŏţĥēŗ şρąċē ŏŗ ƌēĺēţē ą şρąċē ţĥąţ ŷŏű ƌŏŋ’ţ ŋēēƌ ąŋŷɱŏŗē. Ŷŏű ċąŋ įŋċŗēąşē ŷŏűŗ ţŏţąĺ şŷşţēɱ şţŏŗąğē ƃŷ ċąĺĺįŋğ Μąŋąğē Ƥĺąŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Space id length warning
spaceIdLengthWarning=[[[Μąχįɱűɱ ŏƒ {0} ċĥąŗąċţēŗş ēχċēēƌēƌ.]]]
#XMSG: Space name length warning
spaceNameLengthWarning=[[[Μąχįɱűɱ ŏƒ {0} ċĥąŗąċţēŗş ēχċēēƌēƌ.]]]
#XMSG: Space id reserved prefix warning
spaceIdnWarningReservedPrefix=[[[Ƥĺēąşē ƌŏ ŋŏţ űşē ţĥē {0} ρŗēƒįχ ţŏ ąʋŏįƌ ρŏşşįƃĺē ċŏŋƒĺįċţş.]]]
#YMSE: Error while loading open SQL schemas
loadLocalSchemasError=[[[Ŏρēŋ ŜǬĻ şċĥēɱąş ċŏűĺƌŋ’ţ ƃē ĺŏąƌēƌ.∙∙∙∙∙∙∙∙∙∙∙∙]]]
#YMSE: Error while creating open SQL schema
createLocalSchemaError=[[[Ŏρēŋ ŜǬĻ şċĥēɱą ċŏűĺƌŋ’ţ ƃē ċŗēąţēƌ.∙∙∙∙∙∙∙∙∙∙∙∙]]]
#YMSE: Error while loading all remote connections
loadRemoteConnectionsError=[[[Ĉŏűĺƌ ŋŏţ ĺŏąƌ ąĺĺ ŗēɱŏţē ċŏŋŋēċţįŏŋş.∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#YMSE: Error while loading space details
loadSpaceDetailsError=[[[Ţĥē şρąċē ƌēţąįĺş ċŏűĺƌŋ’ţ ƃē ĺŏąƌēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#YMSE: Error while deploying space details
deploySpaceDetailsError=[[[Ŝρąċē ċŏűĺƌŋ’ţ ƃē ƌēρĺŏŷēƌ.∙∙∙∙∙∙∙∙]]]
#YMSE: Error while copying space details
copySpaceDetailsError=[[[Ŝρąċē ċŏűĺƌŋ’ţ ƃē ċŏρįēƌ.∙∙∙∙∙∙∙]]]
#YMSE: Error while loading storage data
loadStorageDataError=[[[Ŝţŏŗąğē ƌąţą ċŏűĺƌŋ’ţ ƃē ĺŏąƌēƌ.∙∙∙∙∙∙∙∙∙∙]]]
#YMSE: Error while loading all users
loadAllUsersError=[[[Ĉŏűĺƌ ŋŏţ ĺŏąƌ ąĺĺ űşēŗş.∙∙∙∙∙∙∙]]]
#YMSE: Failed to reset password
resetPasswordError=[[[Ƥąşşŵŏŗƌ ċŏűĺƌŋ’ţ ƃē ŗēşēţ.∙∙∙∙∙∙∙∙]]]
#XMSG: Success message after updating space
resetPasswordSuccessMessage=[[[Ńēŵ ρąşşŵŏŗƌ şēţ ƒŏŗ şċĥēɱą∙∙∙∙∙∙∙∙]]]
#YMSE: DP Agent-name too long
DBAgentNameError=[[[Ţĥē ŋąɱē ŏƒ ţĥē ĎƤ ąğēŋţ įş ţŏŏ ĺŏŋğ.∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#YMSE: Schema-name not valid.
schemaNameError=[[[Ţĥē ŋąɱē ŏƒ ţĥē şċĥēɱą įş įŋʋąĺįƌ.∙∙∙∙∙∙∙∙∙∙∙]]]
#YMSE: User name not valid.
UserNameError=[[[Ţĥē űşēŗ ŋąɱē įş įŋʋąĺįƌ.∙∙∙∙∙∙∙]]]
#XTIT: Consumption by Storage Type
consumptionStorageTypeText=[[[Ĉŏŋşűɱρţįŏŋ ƃŷ Ŝţŏŗąğē Ţŷρē∙∙∙∙∙∙∙∙]]]
#XTIT: Consumption by Schema
consumptionSchemaText=[[[Ĉŏŋşűɱρţįŏŋ ƃŷ Ŝċĥēɱą∙∙∙∙∙]]]
#XTIT: Overall Consumption by schema
overallConsumptionSchemaText=[[[Ŏʋēŗąĺĺ Ţąƃĺē Ĉŏŋşűɱρţįŏŋ ƃŷ Ŝċĥēɱą∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XTIT: Overall Consumption by Table Type
overallConsumptionTableTypeText=[[[Ŏʋēŗąĺĺ Ĉŏŋşűɱρţįŏŋ ƃŷ Ţąƃĺē Ţŷρē∙∙∙∙∙∙∙∙∙∙∙]]]
#XTIT: Tables
tableDetailsText=[[[Ţąƃĺē Ďēţąįĺş∙∙∙∙∙∙]]]
#XTIT: Table Storage Consumption
tableStorageConsumptionText=[[[Ţąƃĺē Ŝţŏŗąğē Ĉŏŋşűɱρţįŏŋ∙∙∙∙∙∙∙]]]
#XFLD: Table Type label
tableTypeLabel=[[[Ţąƃĺē Ţŷρē∙∙∙∙]]]
#XFLD: Schema label
schemaLabel=[[[Ŝċĥēɱą∙∙∙∙∙∙∙∙]]]
#XFLD: reset table tooltip
resetTable=[[[Řēşēţ Ţąƃĺē∙∙∙∙∙∙∙∙]]]
#XFLD: In-Memory label in space monitor
inMemoryLabel=[[[Μēɱŏŗŷ∙∙∙∙∙∙∙∙]]]
#XFLD: Disk label in space monitor
diskLabel=[[[Ďįşķ]]]
#XFLD: Yes
yesLabel=[[[Ŷēş∙]]]
#XFLD: No
noLabel=[[[Ńŏ∙∙]]]
#XFLD: switch label: Want the data in this space to be consumable by default?
allowConsumptionByDefault=[[[Ďŏ ŷŏű ŵąŋţ ţĥē ƌąţą įŋ ţĥįş şρąċē ţŏ ƃē ċŏŋşűɱąƃĺē ƃŷ ƌēƒąűĺţ?∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Business Name
businessNameLabel=[[[Ɓűşįŋēşş Ńąɱē∙∙∙∙∙∙]]]
#XFLD: Refresh
refresh=[[[Řēƒŗēşĥ∙∙∙∙∙∙∙]]]
#XMSG: No filter results title
noFilterResultsTitle=[[[Ĭţ şēēɱş ţĥąţ ŷŏűŗ ƒįĺţēŗ şēţţįŋğş ąŗē ŋŏţ şĥŏŵįŋğ ąŋŷ ƌąţą.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: No filter results message
noFilterResultsMsg=[[[Ţŗŷ ŗēƒįŋįŋğ ŷŏűŗ ƒįĺţēŗ şēţţįŋğş ąŋƌ įƒ ŷŏű şţįĺĺ ƌŏŋ’ţ şēē ąŋŷ ƌąţą ţĥēŋ; ċŗēąţē şŏɱē ţąƃĺēş įŋ ţĥē Ďąţą Ɓűįĺƌēŗ. Ŏŋċē ţĥēŷ ċŏŋşűɱē şţŏŗąğē, ŷŏű’ĺĺ ƃē ąƃĺē ţŏ ɱŏŋįţŏŗ ţĥēɱ ĥēŗē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Space monitoring Hana state red title
spaceMonitorHanaRedTitle=[[[Ţĥē ŗűŋ-ţįɱē ƌąţąƃąşē įş ŋŏţ ąʋąįĺąƃĺē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Space monitoring Hana state red message
spaceMonitorHanaRedMsg=[[[Āş ţĥē ŗűŋ-ţįɱē ƌąţąƃąşē įş ŋŏţ ąʋąįĺąƃĺē, ċēŗţąįŋ ƒēąţűŗēş ąŗē ƌįşąƃĺēƌ ąŋƌ ŵē ċąŋŋŏţ ƌįşρĺąŷ ąŋŷ įŋƒŏŗɱąţįŏŋ ŏŋ ţĥįş ρąğē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#YMSE: Error while creating space schema user
createSpaceSchemaUserError=[[[Ŝρąċē şċĥēɱą űşēŗ ċŏűĺƌŋ’ţ ƃē ċŗēąţēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#YMSE: Error User name already exists
userAlreadyExistsError=[[[Ůşēŗ ŋąɱē ąĺŗēąƌŷ ēχįşţş.∙∙∙∙∙∙∙]]]
#YMSE: Error Authentication failed
authenticationFailedError=[[[Āűţĥēŋţįċąţįŏŋ ƒąįĺēƌ.∙∙∙∙∙]]]
#XMSG: Message strip message when a schema user is locked
userLockedMessageStrip=[[[Ţĥē űşēŗ įş ĺŏċķēƌ ƌűē ţŏ ţŏŏ ɱąŋŷ ƒąįĺēƌ ĺŏğįŋş. Ƥĺēąşē ŗēƣűēşţ ą ŋēŵ ρąşşŵŏŗƌ ţŏ űŋĺŏċķ ţĥē űşēŗ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Success message after reset and unlock schema user
resetUnlockPasswordSuccessMessage=[[[Ńēŵ ρąşşŵŏŗƌ şēţ ąŋƌ űşēŗ űŋĺŏċķēƌ∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: user is locked message
userLockedMessage=[[[Ůşēŗ įş ĺŏċķēƌ.∙∙∙∙]]]
#XCOL: Users table-view column Role
spaceRole=[[[Řŏĺē]]]
#XCOL: Users table-view column Scoped Role
spaceScopedRole=[[[Ŝċŏρēƌ Řŏĺē∙∙∙∙∙∙∙∙]]]
#XCOL: Users table-view column Space Admin
spaceAdmin=[[[Ŝρąċē Āƌɱįŋįşţŗąţŏŗ∙∙∙∙∙]]]
#XFLD: User section dropdown value Viewer
viewer=[[[Ʋįēŵēŗ∙∙∙∙∙∙∙∙]]]
#XFLD: User section dropdown value Modeler
modeler=[[[Μŏƌēĺēŗ∙∙∙∙∙∙∙]]]
#XFLD: User section dropdown value Data Integrator
dataIntegrator=[[[Ďąţą Ĭŋţēğŗąţŏŗ∙∙∙∙]]]
#XFLD: User section dropdown value Space Administrator
spaceAdministrator=[[[Ŝρąċē Āƌɱįŋįşţŗąţŏŗ∙∙∙∙∙]]]
#XMSG: Success message -  Space Role updated successfully.
updateUserSpaceRoleSuccess=[[[Ŝρąċē ŗŏĺē űρƌąţēƌ∙∙∙∙∙∙]]]
#XMSG: Error message - Failed to update Space Role.
updateUserSpaceRoleError=[[[Ŝρąċē Řŏĺē ŵąş ŋŏţ şűċċēşşƒűĺĺŷ űρƌąţēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD:
databaseUserNameSuffix=[[[Ďąţąƃąşē Ůşēŗ Ńąɱē Ŝűƒƒįχ∙∙∙∙∙∙∙]]]
#XTXT: Space Schema password text
spaceSchemaPasswordText=[[[Ţŏ şēţ űρ ą ċŏŋŋēċţįŏŋ ţŏ ţĥįş şċĥēɱą, ρĺēąşē ċŏρŷ ŷŏűŗ ρąşşŵŏŗƌ. Ĭŋ ċąşē ŷŏű’ʋē ƒŏŗğŏţţēŋ ŷŏűŗ ρąşşŵŏŗƌ, ŷŏű ċąŋ ąĺŵąŷş ŗēƣűēşţ ą ŋēŵ ŏŋē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Cloud Platform label in space schema dialog
cloudPlatformLabel=[[[Ĉĺŏűƌ Ƥĺąţƒŏŗɱ∙∙∙∙∙]]]
#XTXT: Cloud Platform text in space schema dialog
cloudPlatformText=[[[Ţŏ şēţ űρ ąċċēşş ʋįą ţĥįş űşēŗ, ēŋąƃĺē ţĥē ċŏŋşűɱρţįŏŋ ąŋƌ ċŏρŷ ţĥē ċŗēƌēŋţįąĺş. Ĭŋ ċąşē ŷŏű ċąŋ ŏŋĺŷ ċŏρŷ ţĥē ċŗēƌēŋţįąĺş ŵįţĥŏűţ ą ρąşşŵŏŗƌ, ɱąķē şűŗē ŷŏű ąƌƌ ţĥē ρąşşŵŏŗƌ ĺąţēŗ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: enable consumption in CloudPlatform label in space schema dialog
enableConsumptionInCloudPlatformLabel=[[[Ĕŋąƃĺē Ĉŏŋşűɱρţįŏŋ įŋ Ĉĺŏűƌ Ƥĺąţƒŏŗɱ∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Credentials for User-Provided Service label in space schema dialog
cloudPlatformCredentialsLabel=[[[Ĉŗēƌēŋţįąĺş ƒŏŗ Ůşēŗ-Ƥŗŏʋįƌēƌ Ŝēŗʋįċē:∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Credentials for User-Provided Service (Without Password) label in space schema dialog
cloudPlatformCredentialsWithoutPasswordLabel=[[[Ĉŗēƌēŋţįąĺş ƒŏŗ Ůşēŗ-Ƥŗŏʋįƌēƌ Ŝēŗʋįċē (Ŵįţĥŏűţ Ƥąşşŵŏŗƌ):∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Copy Credentials label in space schema dialog
copyCredentialsLabel=[[[Ĉŏρŷ Ĉŗēƌēŋţįąĺş Ŵįţĥŏűţ Ƥąşşŵŏŗƌ∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Copy Credentials label in space schema dialog
copyFullCredentialsLabel=[[[Ĉŏρŷ Ƒűĺĺ Ĉŗēƌēŋţįąĺş∙∙∙∙∙]]]
#XFLD: Copy Password label in space schema dialog
copyPasswordLabel=[[[Ĉŏρŷ Ƥąşşŵŏŗƌ∙∙∙∙∙∙]]]
#XMSG: Credentials copied to clipboard
credentialsCopiedMessage=[[[Ĉŗēƌēŋţįąĺş ċŏρįēƌ ţŏ ċĺįρƃŏąŗƌ∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Password copied to clipboard
passwordCopiedMessage=[[[Ƥąşşŵŏŗƌ ċŏρįēƌ ţŏ ċĺįρƃŏąŗƌ∙∙∙∙∙∙∙∙]]]
#XMSG: create database user dialog title
createDatabaseUserDialogTitle=[[[Ĉŗēąţē Ďąţąƃąşē Ůşēŗ∙∙∙∙]]]
#XMSG: Database Users section title
databaseUsers=[[[Ďąţąƃąşē Ůşēŗş∙∙∙∙∙]]]
#XMSG: Database User Details dialog title
databaseUserDetailsDialogTitle=[[[Ďąţąƃąşē Ůşēŗ Ďēţąįĺş∙∙∙∙∙]]]
#XFLD: database user read audit log
databaseUserAuditLogRead=[[[Ĕŋąƃĺē Āűƌįţ Ļŏğş ƒŏŗ Řēąƌ Ŏρēŗąţįŏŋş ąŋƌ Ķēēρ Ļŏğş ƒŏŗ∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: database user change audit log
databaseUserAuditLogChange=[[[Ĕŋąƃĺē Āűƌįţ Ļŏğş ƒŏŗ Ĉĥąŋğē Ŏρēŗąţįŏŋş ąŋƌ Ķēēρ Ļŏğş ƒŏŗ∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Cloud Platform Access
cloudPlatformAccess=[[[Ĉĺŏűƌ Ƥĺąţƒŏŗɱ Āċċēşş∙∙∙∙∙]]]
#XFLD: Enable Consumption in Cloud Platform hint
enableConsumptionCloudPlatformHint=[[[Ŝēţ űρ ąċċēşş ţŏ ŷŏűŗ ĤĀŃĀ Ďēρĺŏŷɱēŋţ Ĭŋƒŗąşţŗűċţűŗē (ĤĎĬ) ċŏŋţąįŋēŗ ʋįą ţĥįş ƌąţąƃąşē űşēŗ. Ţŏ ċŏŋŋēċţ ţŏ ŷŏűŗ ĤĎĬ ċŏŋţąįŋēŗ, ŜǬĻ ɱŏƌēĺįŋğ ɱűşţ ƃē şŵįţċĥēƌ ŏŋ∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Enable HDI Consumption
enableConsumptionCloudPlatform=[[[Ĕŋąƃĺē ĤĎĬ Ĉŏŋşűɱρţįŏŋ∙∙∙∙∙]]]
#XFLD: Enable Consumption hint
enableConsumptionHint=[[[Ďŏ ŷŏű ŵąŋţ ţĥē ƌąţą įŋ ŷŏűŗ şρąċē ţŏ ƃē ċŏŋşűɱąƃĺē ƃŷ ŏţĥēŗ ţŏŏĺş ŏŗ ąρρş?∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Enable Consumption
enableConsumption=[[[Ĕŋąƃĺē ŜǬĻ Ĉŏŋşűɱρţįŏŋ∙∙∙∙∙]]]
#XFLD: Enable Modeling
enableModeling=[[[Ĕŋąƃĺē ŜǬĻ Μŏƌēĺįŋğ∙∙∙∙∙]]]
#XMSG: Privileges for Data Modeling
privilegesModeling=[[[Ďąţą Ĭŋğēşţįŏŋ∙∙∙∙∙]]]
#XMSG: Privileges for Data Consumption
privilegesConsumption=[[[Ďąţą Ĉŏŋşűɱρţįŏŋ ƒŏŗ Ĕχţēŗŋąĺ Ţŏŏĺş∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: SQL Modeling
sqlModeling=[[[ŜǬĻ Μŏƌēĺįŋğ∙∙∙∙∙∙∙]]]
#XFLD: SQL Consumption
sqlConsumption=[[[ŜǬĻ Ĉŏŋşűɱρţįŏŋ∙∙∙∙]]]
#XFLD: enabled
enabled=[[[Ĕŋąƃĺēƌ∙∙∙∙∙∙∙]]]
#XFLD: disabled
disabled=[[[Ďįşąƃĺēƌ∙∙∙∙∙∙]]]
#XFLD: Edit Privileges
editPrivileges=[[[Ĕƌįţ Ƥŗįʋįĺēğēş∙∙∙∙]]]
#XFLD: Open Database Explorer
openDBX=[[[Ŏρēŋ Ďąţąƃąşē Ĕχρĺŏŗēŗ∙∙∙∙∙]]]
#XFLD: create database user hint
databaseCreateHint=[[[Ƥĺēąşē ŋŏţē ţĥąţ įţ ŵįĺĺ ŋŏţ ƃē ρŏşşįƃĺē ţŏ ċĥąŋğē ţĥē űşēŗ ŋąɱē ąğąįŋ ąƒţēŗ şąʋįŋğ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Internal Schema Name
internalSchemaName=[[[Ĭŋţēŗŋąĺ Ŝċĥēɱą Ńąɱē∙∙∙∙]]]
#YMSE: Failed to load database users
loadDatabaseUserError=[[[Ƒąįĺēƌ ţŏ ĺŏąƌ ƌąţąƃąşē űşēŗş∙∙∙∙∙∙∙∙∙]]]
#YMSE: Failed to delete database user
deleteDatabaseUsersError=[[[Ƒąįĺēƌ ţŏ ƌēĺēţē ƌąţąƃąşē űşēŗş∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Success message after deleting database user
deleteDatabaseUserSuccess=[[[Ďąţąƃąşē űşēŗ ƌēĺēţēƌ∙∙∙∙∙]]]
#XMSG: Success message after deleting database users
deleteDatabaseUsersSuccess=[[[Ďąţąƃąşē űşēŗş ƌēĺēţēƌ∙∙∙∙∙]]]
#XMSG: Success message after creating database user
createDatabaseUserSuccessMessage=[[[Ďąţąƃąşē űşēŗ ċŗēąţēƌ∙∙∙∙∙]]]
#YMSE: Failed to create database user
createDatabaseUserError=[[[Ƒąįĺēƌ ţŏ ċŗēąţē ƌąţąƃąşē űşēŗ∙∙∙∙∙∙∙∙∙]]]
#XMSG: Success message after updating database user
updateDatabaseUserSuccessMessage=[[[Ďąţąƃąşē űşēŗ űρƌąţēƌ∙∙∙∙∙]]]
#YMSE: Failed to update database user
updateDatabaseUserError=[[[Ƒąįĺēƌ ţŏ űρƌąţē ƌąţąƃąşē űşēŗ∙∙∙∙∙∙∙∙∙]]]
#XFLD: HDI Consumption
hdiConsumption=[[[ĤĎĬ Ĉŏŋşűɱρţįŏŋ∙∙∙∙]]]
#XTIT: Space detail section Database Access title
detailsSectionDatabaseAccess=[[[Ďąţąƃąşē Āċċēşş∙∙∙∙]]]
#XTXT: Database users consumption hint
databaseUsersConsumptionHint=[[[Μąķē ŷŏűŗ şρąċē ƌąţą ċŏŋşűɱąƃĺē ƃŷ ƌēƒąűĺţ. Ţĥē ɱŏƌēĺş įŋ ţĥē ƃűįĺƌēŗş ŵįĺĺ ąűţŏɱąţįċąĺĺŷ ąĺĺŏŵ ţĥē ƌąţą ţŏ ƃē ċŏŋşűɱąƃĺē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Default Consumption of Space Data:
defaultConsumptionOfSpaceData=[[[Ďēƒąűĺţ Ĉŏŋşűɱρţįŏŋ ŏƒ Ŝρąċē Ďąţą:∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Database User Name
databaseUserName=[[[Ďąţąƃąşē Ůşēŗ Ńąɱē∙∙∙∙∙∙]]]
#XMSG: Database User creation validation error message
databaseUserValidationError=[[[Ĭţ ĺŏŏķş ĺįķēş şŏɱē ƒįēĺƌş ąŗē įŋʋąĺįƌ. Ƥĺēąşē ċĥēċķ ţĥē ŗēƣűįŗēƌ ƒįēĺƌş ţŏ ċŏŋţįŋűē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: SQL modeling can’t be enabled since this user has been migrated.
migratedDatabaseUserHint=[[[Ďąţą įŋğēşţįŏŋ ċąŋ’ţ ƃē ēŋąƃĺēƌ şįŋċē ţĥįş űşēŗ ĥąş ƃēēŋ ɱįğŗąţēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XBUT: Remove Button Text
remove=[[[Řēɱŏʋē∙∙∙∙∙∙∙∙]]]
#XBUT: Remove Spaces Button Text
removeSpaces=[[[Řēɱŏʋē Ŝρąċēş∙∙∙∙∙∙]]]
#XBUT: Remove Objects Button Text
removeObjects=[[[Řēɱŏʋē Ŏƃĵēċţş∙∙∙∙∙]]]
#XMSG: No members have been added yet.
noMembersAssigned=[[[Ńŏ ɱēɱƃēŗş ĥąʋē ƃēēŋ ąƌƌēƌ ŷēţ.∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: No users have been added yet.
noUsersAssigned=[[[Ńŏ űşēŗş ĥąʋē ƃēēŋ ąƌƌēƌ ŷēţ.∙∙∙∙∙∙∙∙∙]]]
#XMSG: No database users have been created or your filter is not showing any data.
noDatabaseUsers=[[[Ńŏ ƌąţąƃąşē űşēŗş ĥąʋē ƃēēŋ ċŗēąţēƌ, ŏŗ ŷŏűŗ ƒįĺţēŗ įş ŋŏţ şĥŏŵįŋğ ąŋŷ ƌąţą.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Please enter a user name.
noDatabaseUsername=[[[Ƥĺēąşē ēŋţēŗ ą űşēŗ ŋąɱē.∙∙∙∙∙∙∙]]]
#XMSG: The user name is too long. Please use a shorter one.
databaseUsernameTooLong=[[[Ţĥē űşēŗ ŋąɱē įş ţŏŏ ĺŏŋğ. Ƥĺēąşē űşē ą şĥŏŗţēŗ ŏŋē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: No privileges have been enabled and this database user will have limited functionality. Do you still want to continue?
noPrivilegesConfirmationMessage=[[[Ńŏ ρŗįʋįĺēğēş ĥąʋē ƃēēŋ ēŋąƃĺēƌ, ąŋƌ ţĥįş ƌąţąƃąşē űşēŗ ŵįĺĺ ĥąʋē ĺįɱįţēƌ ƒűŋċţįŏŋąĺįţŷ. Ďŏ ŷŏű şţįĺĺ ŵąŋţ ţŏ ċŏŋţįŋűē?∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: To enable audit logs for change operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogChangeMessage=[[[Ţŏ ēŋąƃĺē ąűƌįţ ĺŏğş ƒŏŗ ċĥąŋğē ŏρēŗąţįŏŋş, ƌąţą įŋğēşţįŏŋ ŋēēƌş ţŏ ƃē ēŋąƃĺēƌ ąş ŵēĺĺ. Ďŏ ŷŏű ŵąŋţ ţŏ ƌŏ ţĥįş?∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: To enable audit logs for read operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogReadMessage=[[[Ţŏ ēŋąƃĺē ąűƌįţ ĺŏğş ƒŏŗ ŗēąƌ ŏρēŗąţįŏŋş, ƌąţą įŋğēşţįŏŋ ŋēēƌş ţŏ ƃē ēŋąƃĺēƌ ąş ŵēĺĺ. Ďŏ ŷŏű ŵąŋţ ţŏ ƌŏ ţĥįş?∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: To enable HDI consumption data ingestion and data consumption needs to be enabled. Do you want to enable all?
enableHDIConsumptionMessage=[[[Ţŏ ēŋąƃĺē ĤĎĬ ċŏŋşűɱρţįŏŋ, ƌąţą įŋğēşţįŏŋ ąŋƌ ƌąţą ċŏŋşűɱρţįŏŋ ŋēēƌ ţŏ ƃē ēŋąƃĺēƌ ąş ŵēĺĺ. Ďŏ ŷŏű ŵąŋţ ţŏ ƌŏ ţĥįş?∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG:
databaseUserPasswordText=[[[Ţŏ şēţ űρ ą ċŏŋŋēċţįŏŋ ţŏ ţĥįş ƌąţąƃąşē űşēŗ, ρĺēąşē ċŏρŷ ŷŏűŗ ρąşşŵŏŗƌ. Ĭŋ ċąşē ŷŏű ƒŏŗğēţ ŷŏűŗ ρąşşŵŏŗƌ, ŷŏű ċąŋ ąĺŵąŷş ŗēƣűēşţ ą ŋēŵ ŏŋē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XTIT: Space detail section members title
detailsSectionMembers=[[[Μēɱƃēŗş∙∙∙∙∙∙∙]]]
#XMSG: New password set
newPasswordSet=[[[Ńēŵ ρąşşŵŏŗƌ şēţ∙∙∙∙∙∙∙∙]]]
#XFLD: Data Ingestion
dataIngestion=[[[Ďąţą Ĭŋğēşţįŏŋ∙∙∙∙∙]]]
#XFLD: Data Consumption
dataConsumption=[[[Ďąţą Ĉŏŋşűɱρţįŏŋ∙∙∙∙∙∙∙∙]]]
#XFLD: Privileges
privileges=[[[Ƥŗįʋįĺēğēş∙∙∙∙]]]
#XFLD: Enable Data ingestion
enableDataIngestion=[[[Ĕŋąƃĺē Ďąţą Ĭŋğēşţįŏŋ∙∙∙∙∙]]]
#XMSG: Log the read and change operations for data ingestion.
databaseUserAuditHint=[[[Ļŏğ ţĥē ŗēąƌ ąŋƌ ċĥąŋğē ŏρēŗąţįŏŋş ƒŏŗ ƌąţą įŋğēşţįŏŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Make your space data available in your HDI containers.
databaseUserHDIHint=[[[Μąķē ŷŏűŗ şρąċē ƌąţą ąʋąįĺąƃĺē įŋ ŷŏűŗ ĤĎĬ ċŏŋţąįŋēŗş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Enable Data consumption
enableDataConsumption=[[[Ĕŋąƃĺē Ďąţą Ĉŏŋşűɱρţįŏŋ∙∙∙∙∙∙]]]
#XMSG: Allow other apps or tools to consume your space data.
enableDataConsumptionHint=[[[Āĺĺŏŵ ŏţĥēŗ ąρρş ŏŗ ţŏŏĺş ţŏ ċŏŋşűɱē ŷŏűŗ şρąċē ƌąţą.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: To set up access via this schema user copy the credentials to your user-provided service. In case you can only copy the credentials without a password make sure to add the password later.
databaseUserHDIConsumptionHint=[[[Ţŏ şēţ űρ ąċċēşş ʋįą ţĥįş ƌąţąƃąşē űşēŗ, ċŏρŷ ţĥē ċŗēƌēŋţįąĺş ţŏ ŷŏűŗ űşēŗ-ρŗŏʋįƌēƌ şēŗʋįċē. Ĭŋ ċąşē ŷŏű ċąŋ ŏŋĺŷ ċŏρŷ ţĥē ċŗēƌēŋţįąĺş ŵįţĥŏűţ ą ρąşşŵŏŗƌ, ɱąķē şűŗē ŷŏű ąƌƌ ţĥē ρąşşŵŏŗƌ ĺąţēŗ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Data Flow Runtime Capacity label in space overview
dataFlowRuntimeCapacity=[[[Ďąţą Ƒĺŏŵ Řűŋţįɱē Ĉąρąċįţŷ ({0}:{1} ĥŏűŗş ŏƒ {2} ĥŏűŗş)]]]
#XFLD: Could not load Data Flow Runtime Capacity
dataFlowRuntimeCapacityError=[[[Ĉŏűĺƌ ŋŏţ ĺŏąƌ ƌąţą ƒĺŏŵ ŗűŋţįɱē ċąρąċįţŷ∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: User can grant data consumption to other users.
databaseUserConsumptionWithGrantOptionHint=[[[Ůşēŗ ċąŋ ğŗąŋţ ƌąţą ċŏŋşűɱρţįŏŋ ţŏ ŏţĥēŗ űşēŗş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Enable data consumption with grant option
enableConsumptionWithGrantOption=[[[Ĕŋąƃĺē Ďąţą Ĉŏŋşűɱρţįŏŋ ŵįţĥ Ģŗąŋţ Ŏρţįŏŋ∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: To enable data consumption with grant option data consumption needs to be enabled. Do you want to enable both?
enableConsumptionWithGrantMessage=[[[Ţŏ ēŋąƃĺē ƌąţą ċŏŋşűɱρţįŏŋ ŵįţĥ ğŗąŋţ ŏρţįŏŋ, ƌąţą ċŏŋşűɱρţįŏŋ ŋēēƌş ţŏ ƃē ēŋąƃĺēƌ. Ďŏ ŷŏű ŵąŋţ ţŏ ēŋąƃĺē ƃŏţĥ?∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Enable Automated Predictive Library (APL) and Predictive Analysis Library (PAL)
allowUsingScriptServerFunctionality=[[[Ĕŋąƃĺē Āűţŏɱąţēƌ Ƥŗēƌįċţįʋē Ļįƃŗąŗŷ (ĀƤĻ) ąŋƌ Ƥŗēƌįċţįʋē Āŋąĺŷşįş Ļįƃŗąŗŷ (ƤĀĻ)∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: User can use SAP HANA Cloud embedded machine learning functions.
allowUsingScriptServerFunctionalityHint=[[[Ůşēŗ ċąŋ űşē ŜĀƤ ĤĀŃĀ Ĉĺŏűƌ ēɱƃēƌƌēƌ ɱąċĥįŋē ĺēąŗŋįŋğ ƒűŋċţįŏŋş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Password Policy
passwordPolicy=[[[Ƥąşşŵŏŗƌ Ƥŏĺįċŷ∙∙∙∙]]]
#XMSG: Password Policy
passwordPolicyHint=[[[Ĕŋąƃĺē ŏŗ ƌįşąƃĺē ţĥē ċŏŋƒįğűŗēƌ ρąşşŵŏŗƌ ρŏĺįċŷ ĥēŗē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Enable Password Policy
enablePasswordPolicy=[[[Ĕŋąƃĺē Ƥąşşŵŏŗƌ Ƥŏĺįċŷ∙∙∙∙∙]]]
#XMSG: Read Access to the Space Schema
readAccessTitle=[[[Řēąƌ Āċċēşş ţŏ ţĥē Ŝρąċē Ŝċĥēɱą∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: read access hint
readAccessHint=[[[Āĺĺŏŵ ţĥē ƌąţąƃąşē űşēŗ ţŏ ċŏŋŋēċţ ēχţēŗŋąĺ ţŏŏĺş ţŏ ţĥē şρąċē şċĥēɱą ąŋƌ ŗēąƌ ʋįēŵş ţĥąţ ąŗē ēχρŏşēƌ ƒŏŗ ċŏŋşűɱρţįŏŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Space Schema
spaceSchema=[[[Ŝρąċē Ŝċĥēɱą∙∙∙∙∙∙∙]]]
#XFLD: Enable Read Access (SQL)
enableReadAccess=[[[Ĕŋąƃĺē Řēąƌ Āċċēşş (ŜǬĻ)∙∙∙∙∙∙]]]
#XMSG: Allow the user to grant read access to other users.
withGrantOptionHint=[[[Āĺĺŏŵ ţĥē űşēŗ ţŏ ğŗąŋţ ŗēąƌ ąċċēşş ţŏ ŏţĥēŗ űşēŗş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: With Grant Option
withGrantOption=[[[Ŵįţĥ Ģŗąŋţ Ŏρţįŏŋ∙∙∙∙∙∙∙]]]
#XMSG: Make your space data available in your HDI containers.
enableHDIConsumptionHint=[[[Μąķē ŷŏűŗ şρąċē ƌąţą ąʋąįĺąƃĺē įŋ ŷŏűŗ ĤĎĬ ċŏŋţąįŋēŗş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Enable HDI Consumption
enableHDIConsumption=[[[Ĕŋąƃĺē ĤĎĬ Ĉŏŋşűɱρţįŏŋ∙∙∙∙∙]]]
#XMSG: Write Access to the User’s Open SQL Schema
writeAccessTitle=[[[Ŵŗįţē Āċċēşş ţŏ ţĥē Ůşēŗ’ş Ŏρēŋ ŜǬĻ Ŝċĥēɱą∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: write access hint
writeAccessHint=[[[Āĺĺŏŵ ţĥē ƌąţąƃąşē űşēŗ ţŏ ċŏŋŋēċţ ēχţēŗŋąĺ ţŏŏĺş ţŏ ţĥē űşēŗ’ş Ŏρēŋ ŜǬĻ şċĥēɱą ţŏ ċŗēąţē ƌąţą ēŋţįţįēş ąŋƌ įŋğēşţ ƌąţą ƒŏŗ űşē įŋ ţĥē şρąċē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Open SQL Schema
openSQLSchema=[[[Ŏρēŋ ŜǬĻ Ŝċĥēɱą∙∙∙∙]]]
#XFLD: Enable Write Access (SQL DDL & DML)
enableWriteAccess=[[[Ĕŋąƃĺē Ŵŗįţē Āċċēşş (ŜǬĻ, ĎĎĻ, & ĎΜĻ)∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: audit hint
auditHint=[[[Ļŏğ ţĥē ŗēąƌ ąŋƌ ċĥąŋğē ŏρēŗąţįŏŋş įŋ ţĥē Ŏρēŋ ŜǬĻ şċĥēɱą.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: data consumption hint
dataConsumptionHint=[[[Ĕχρŏşē ąĺĺ ŋēŵ ʋįēŵş įŋ ţĥē şρąċē ƃŷ ƌēƒąűĺţ ƒŏŗ ċŏŋşűɱρţįŏŋ. Μŏƌēĺēŗş ċąŋ ŏʋēŗŗįƌē ţĥįş şēţţįŋğ ƒŏŗ įŋƌįʋįƌűąĺ ʋįēŵş ʋįą ţĥē “Ĕχρŏşē ƒŏŗ Ĉŏŋşűɱρţįŏŋ” şŵįţċĥ įŋ ţĥē ʋįēŵ ŏűţρűţ şįƌē ρąŋēĺ. Ŷŏű ċąŋ ąĺşŏ ċĥŏŏşē ţĥē ƒŏŗɱąţş įŋ ŵĥįċĥ ʋįēŵş ąŗē ēχρŏşēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Expose for Consumption by Default
exposeDataConsumption=[[[Ĕχρŏşē ƒŏŗ Ĉŏŋşűɱρţįŏŋ ƃŷ Ďēƒąűĺţ∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: database users hint consumption hint
databaseUsersHint2New=[[[Ĉŗēąţē ƌąţąƃąşē űşēŗş ţŏ ċŏŋŋēċţ ēχţēŗŋąĺ ţŏŏĺş ţŏ ŜĀƤ Ďąţąşρĥēŗē. Ŝēţ ρŗįʋįĺēğēş ţŏ ąĺĺŏŵ űşēŗş ţŏ ŗēąƌ şρąċē ƌąţą ąŋƌ ţŏ ċŗēąţē ƌąţą ēŋţįţįēş (ĎĎĻ) ąŋƌ įŋğēşţ ƌąţą (ĎΜĻ) ƒŏŗ űşē įŋ ţĥē şρąċē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Read
read=[[[Řēąƌ]]]
#XFLD: Read (HDI)
readHDI=[[[Řēąƌ (ĤĎĬ)∙∙∙∙]]]
#XFLD: Write
write=[[[Ŵŗįţē∙∙∙∙∙∙∙∙∙]]]
#XMSG: HDI Containers Hint
HDIContainersHint2=[[[Ĕŋąƃĺē ąċċēşş ţŏ ŷŏűŗ ŜĀƤ ĤĀŃĀ Ďēρĺŏŷɱēŋţ Ĭŋƒŗąşţŗűċţűŗē (ĤĎĬ) ċŏŋţąįŋēŗş įŋ ŷŏűŗ şρąċē. Μŏƌēĺēŗş ċąŋ űşē ĤĎĬ ąŗţįƒąċţş ąş şŏűŗċēş ƒŏŗ ʋįēŵş, ąŋƌ ĤĎĬ ċĺįēŋţş ċąŋ ąċċēşş ŷŏűŗ şρąċē ƌąţą.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Open info dialog
openDatabaseUserInfoDialog=[[[Ŏρēŋ ţĥē įŋƒŏ ƌįąĺŏğ∙∙∙∙]]]
#XMSG: Database user is locked. Open info dialog to unlock
openLockedDatabaseUserInfoDialog=[[[Ďąţąƃąşē űşēŗ įş ĺŏċķēƌ. Ŏρēŋ ƌįąĺŏğ ţŏ űŋĺŏċķ∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Table
table=[[[Ţąƃĺē∙∙∙∙∙∙∙∙∙]]]
#XFLD: Space section partner configuration title
partnerConnectionConfigurationSectionTitle=[[[Ƥąŗţŋēŗ Ĉŏŋŋēċţįŏŋ∙∙∙∙∙∙]]]
#XFLD: Space section partner configuration subtitle
partnerConnectionConfigurationSectionSubTitle=[[[Ƥąŗţŋēŗ Ĉŏŋŋēċţįŏŋ Ĉŏŋƒįğűŗąţįŏŋ∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Space section partner configuration hint
partnerConnectionConfigurationSectionHint=[[[Ďēƒįŋē ŷŏűŗ ŏŵŋ ρąŗţŋēŗ ċŏŋŋēċţįŏŋ ţįĺē ƃŷ ąƌƌįŋğ ŷŏűŗ įƑŗąɱē ŮŘĻ ąŋƌ įċŏŋ. Ţĥįş ċŏŋƒįğűŗąţįŏŋ įş ąʋąįĺąƃĺē ŏŋĺŷ ƒŏŗ ţĥįş ţēŋąŋţ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Table Name Field
partnerConnectionConfigurationName=[[[Ţįĺē Ńąɱē∙∙∙∙∙]]]
#XFLD: Table iFrame URL field
partnerConnectionConfigurationiFrameURL=[[[įƑŗąɱē ŮŘĻ∙∙∙∙]]]
#XFLD: Table iFrame Post Message Origin field
partnerConnectionConfigurationiFramePostMessageOrigin=[[[įƑŗąɱē Ƥŏşţ Μēşşąğē Ŏŗįğįŋ∙∙∙∙∙∙∙]]]
#XFLD: Table icon/image name
partnerConnectionConfigurationIcon=[[[Ĭċŏŋ]]]
#XMSG: Table no result found
partnerConnectionConfigurationNotfound=[[[Ńŏ ρąŗţŋēŗ ċŏŋŋēċţįŏŋ ċŏŋƒįğűŗąţįŏŋ(ş) ċŏűĺƌ ƃē ƒŏűŋƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Partner Connection Confgurations are not available
partnerConnectionConfigurationNotAvailable=[[[Ƥąŗţŋēŗ ċŏŋŋēċţįŏŋ ċŏŋƒįğűŗąţįŏŋş ċąŋŋŏţ ƃē ƌįşρĺąŷēƌ ŵĥēŋ ţĥē ŗűŋţįɱē ƌąţąƃąşē įş űŋąʋąįĺąƃĺē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Dialog Create Partner Connection Configuration
partnerConnectionConfigurationDialogTitleAdd=[[[Ĉŗēąţē Ƥąŗţŋēŗ Ĉŏŋŋēċţįŏŋ Ĉŏŋƒįğűŗąţįŏŋ∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Dialog Create Partner Connection Configuration Upload Icon
partnerConnectionConfigurationButtonUloadPartnerIcon=[[[Ůρĺŏąƌ Ĭċŏŋ∙∙∙∙∙∙∙∙]]]
#XFLD: Dialog Create Partner Connection Configuration Select
partnerConnectionConfigurationSelectIcon=[[[Ŝēĺēċţ (ɱąχįɱűɱ şįžē ŏƒ 200ĶƁ)∙∙∙∙∙∙∙∙∙]]]
#XFLD: Dialog Create Partner Connection Configuration Enter a name
partnerConnectionConfigurationNamePlaceholder=[[[Ƥąŗţŋēŗ Ţįĺē Ĕχąɱρĺē∙∙∙∙]]]
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL
partnerConnectionConfigurationiFrameURLPlaceholder=[[[ĥţţρ://ŵŵŵ.ēχąɱρĺē.ċŏɱ∙∙∙∙∙]]]
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL Check
partnerConnectionConfigurationiFramePostMessageOriginPlaceholder=[[[.ēχąɱρĺē.ċŏɱ∙∙∙∙∙∙∙]]]
#XFLD: Dialog Create Partner Connection Configuration Browse
partnerConnectionConfigurationBrowseButton=[[[Ɓŗŏŵşē∙∙∙∙∙∙∙∙]]]
#XFLD: Create Partner Connection Configuration Msg
partnerConnectionConfigurationUploadMsg=[[[Ƥąŗţŋēŗ Ĉŏŋŋēċţįŏŋ Ĉŏŋƒįğűŗąţįŏŋ ĥąş ƃēēŋ şűċċēşşƒűĺĺŷ ċŗēąţēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Error while deleting Partner Connection configuration(s)
partnerConnectionConfigurationDeleteError=[[[Āŋ ēŗŗŏŗ ŏċċűŗŗēƌ ŵĥįĺē ƌēĺēţįŋğ Ƥąŗţŋēŗ Ĉŏŋŋēċţįŏŋ ċŏŋƒįğűŗąţįŏŋ(ş).∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Partner Connection Configuration has been successfully deleted
partnerConnectionConfigurationDeleteSuccess=[[[Ƥąŗţŋēŗ Ĉŏŋŋēċţįŏŋ Ĉŏŋƒįğűŗąţįŏŋ ĥąş ƃēēŋ şűċċēşşƒűĺĺŷ ƌēĺēţēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Error while retrieving Partner Connection Configurations
partnerConnectionConfigurationLoadError=[[[Āŋ ēŗŗŏŗ ŏċċűŗŗēƌ ŵĥįĺē ŗēţŗįēʋįŋğ Ƥąŗţŋēŗ Ĉŏŋŋēċţįŏŋ Ĉŏŋƒįğűŗąţįŏŋş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Error fileSizeLimitExceeded
partnerConnectionConfigurationfileSizeLimitExceeded=[[[Ţĥē ƒįĺē ċŏűĺƌ ŋŏţ ƃē űρĺŏąƌēƌ ƃēċąűşē įţ ēχċēēƌş ţĥē ɱąχįɱűɱ şįžē ŏƒ 200ĶƁ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XBUT: Create Partner Connection Configuration
partnerConnectionConfigurationButtonCreate=[[[Ĉŗēąţē Ƥąŗţŋēŗ Ĉŏŋŋēċţįŏŋ Ĉŏŋƒįğűŗąţįŏŋ∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XBUT: Delete Partner Connection Configuration
partnerConnectionConfigurationButtonDelete=[[[Ďēĺēţē Ƥąŗţŋēŗ Ĉŏŋŋēċţįŏŋ Ĉŏŋƒįğűŗąţįŏŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Partner Tile Creation Failure
upsertPartnerTileFailed=[[[Ƥąŗţŋēŗ Ţįĺē ċŏűĺƌ ŋŏţ ƃē ċŗēąţēƌ.∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Partner Tile Deletion Failure
deletePartnerTileFailed=[[[Ƥąŗţŋēŗ Ţįĺē ċŏűĺƌ ŋŏţ ƃē ƌēĺēţēƌ.∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: HANA cloud connector settings reset failure
resetHANASCCSettingsFailed=[[[Řēşēţţįŋğ Ĉűşţŏɱēŗ ŜĀƤ ĤĀŃĀ Ĉĺŏűƌ Ĉŏŋŋēċţŏŗ Ŝēţţįŋğş ƒąįĺēƌ∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Workload Class
workloadClass=[[[Ŵŏŗķĺŏąƌ Ĉĺąşş∙∙∙∙∙]]]
#XFLD: Workload Management
workloadManagement=[[[Ŵŏŗķĺŏąƌ Μąŋąğēɱēŋţ∙∙∙∙∙]]]
#XFLD: Priority
workloadClassPriority=[[[Ƥŗįŏŗįţŷ∙∙∙∙∙∙]]]
#XMSG:
workloadManagementPriorityHint=[[[Ŷŏű ċąŋ şρēċįƒŷ ţĥē ρŗįŏŗįţįžąţįŏŋ ŏƒ ţĥįş şρąċē ŵĥēŋ ƣűēŗŷįŋğ ţĥē ƌąţąƃąşē. Ĕŋţēŗ ą ʋąĺűē ƒŗŏɱ 1 (ĺŏŵēşţ ρŗįŏŗįţŷ) ţŏ 8 (ĥįğĥēşţ ρŗįŏŗįţŷ). Ĭŋ ą şįţűąţįŏŋ ŵĥēŗē şρąċēş ąŗē ċŏɱρēţįŋğ ƒŏŗ ąʋąįĺąƃĺē ţĥŗēąƌş, ţĥŏşē ŵįţĥ ĥįğĥēŗ ρŗįŏŗįţįēş ąŗē ŗűŋ ƃēƒŏŗē şρąċēş ŵįţĥ ĺŏŵēŗ ρŗįŏŗįţįēş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG:
workloadClassPriorityHint=[[[Ŷŏű ċąŋ şρēċįƒŷ ţĥē ρŗįŏŗįţŷ ŏƒ ţĥē şρąċē ƒŗŏɱ 0 (ĺŏŵēşţ) ţŏ 8 (ĥįğĥēşţ). Ţĥē şţąţēɱēŋţş ŏƒ ą şρąċē ŵįţĥ ą ĥįğĥ ρŗįŏŗįţŷ ąŗē ēχēċűţēƌ ƃēƒŏŗē ţĥē şţąţēɱēŋţş ŏƒ ŏţĥēŗ şρąċēş ŵįţĥ ą ĺŏŵēŗ ρŗįŏŗįţŷ. Ţĥē ƌēƒąűĺţ ρŗįŏŗįţŷ įş 5. Āş ţĥē ʋąĺűē 9 įş ŗēşēŗʋēƌ ţŏ şŷşţēɱ ŏρēŗąţįŏŋş, įţ įş ŋŏţ ąʋąįĺąƃĺē ƒŏŗ ą şρąċē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Statement Limits
workloadclassStatementLimits=[[[Ŝţąţēɱēŋţ Ļįɱįţş∙∙∙∙∙∙∙∙]]]
#XFLD: Workload Configuration
workloadConfiguration=[[[Ŵŏŗķĺŏąƌ Ĉŏŋƒįğűŗąţįŏŋ∙∙∙∙∙]]]
#XMSG:
workloadClassStatementLimitsHint=[[[Ŷŏű ċąŋ şρēċįƒŷ ţĥē ɱąχįɱűɱ ŋűɱƃēŗ (ŏŗ ρēŗċēŋţąğē) ŏƒ ţĥŗēąƌş ąŋƌ ĢƁş ŏƒ ɱēɱŏŗŷ ţĥąţ şţąţēɱēŋţş ŗűŋŋįŋğ ċŏŋċűŗŗēŋţĺŷ įŋ ţĥē şρąċē ċąŋ ċŏŋşűɱē. Ŷŏű ċąŋ ēŋţēŗ ąŋŷ ʋąĺűē ŏŗ ρēŗċēŋţąğē ƃēţŵēēŋ 0 (ŋŏ ĺįɱįţ) ąŋƌ ţĥē ţŏţąĺ ɱēɱŏŗŷ ąŋƌ ţĥŗēąƌş ąʋąįĺąƃĺē įŋ ţĥē ţēŋąŋţ. \\u014B\\u014B Ĭƒ ŷŏű şρēċįƒŷ ą ţĥŗēąƌ ĺįɱįţ, ƃē ąŵąŗē ţĥąţ įţ ċąŋ ĺŏŵēŗ ρēŗƒŏŗɱąŋċē. \\u014B\\u014B Ĭƒ ŷŏű şρēċįƒŷ ą ɱēɱŏŗŷ ĺįɱįţ, ţĥē şţąţēɱēŋţş ţĥąţ ŗēąċĥ ţĥē ɱēɱŏŗŷ ĺįɱįţ ąŗē ŋŏţ ŗűŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG:
workloadClassStatementLimitsDescription=[[[Ţĥē ƌēƒąűĺţ ċŏŋƒįğűŗąţįŏŋ ρŗŏʋįƌēş ğēŋēŗŏűş ŗēşŏűŗċē ĺįɱįţş, ŵĥįĺē ρŗēʋēŋţįŋğ ąŋŷ şįŋğĺē şρąċē ƒŗŏɱ ŏʋēŗĺŏąƌįŋğ ţĥē şŷşţēɱ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG:
workloadClassStatementLimitCustomDescription=[[[Ŷŏű ċąŋ şēţ ɱąχįɱűɱ ţŏţąĺ ţĥŗēąƌ ąŋƌ ɱēɱŏŗŷ ĺįɱįţş ţĥąţ şţąţēɱēŋţş ŗűŋŋįŋğ ċŏŋċűŗŗēŋţĺŷ įŋ ţĥē şρąċē ċąŋ ċŏŋşűɱē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG:
totalStatementThreadLimitHelpText=[[[Ŝēţţįŋğ ţĥē ţĥŗēąƌ ĺįɱįţ ţŏŏ ĺŏŵ ɱąŷ įɱρąċţ şţąţēɱēŋţ ρēŗƒŏŗɱąŋċē, ŵĥįĺē ēχċēşşįʋēĺŷ ĥįğĥ ʋąĺűēş ŏŗ 0 ɱąŷ ąĺĺŏŵ ţĥē şρąċē ţŏ ċŏŋşűɱē ąĺĺ ąʋąįĺąƃĺē şŷşţēɱ ţĥŗēąƌş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG:
totalStatementMemoryLimitHelpText=[[[Ŝēţţįŋğ ţĥē ɱēɱŏŗŷ ĺįɱįţ ţŏŏ ĺŏŵ ɱąŷ ċąűşē ŏűţ-ŏƒ-ɱēɱŏŗŷ įşşűēş, ŵĥįĺē ēχċēşşįʋēĺŷ ĥįğĥ ʋąĺűēş ŏŗ 0 ɱąŷ ąĺĺŏŵ ţĥē şρąċē ţŏ ċŏŋşűɱē ąĺĺ ąʋąįĺąƃĺē şŷşţēɱ ɱēɱŏŗŷ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG:
totalStatementThreadLimitHelpTextRestricted=[[[Ĕŋţēŗ ą ρēŗċēŋţąğē ƃēţŵēēŋ 1% ąŋƌ 70% (ŏŗ ţĥē ēƣűįʋąĺēŋţ ŋűɱƃēŗ) ŏƒ ţĥē ţŏţąĺ ŋűɱƃēŗ ŏƒ ţĥŗēąƌş ąʋąįĺąƃĺē įŋ ŷŏűŗ ţēŋąŋţ. Ŝēţţįŋğ ţĥē ţĥŗēąƌ ĺįɱįţ ţŏŏ ĺŏŵ ɱąŷ įɱρąċţ şţąţēɱēŋţ ρēŗƒŏŗɱąŋċē, ŵĥįĺē ēχċēşşįʋēĺŷ ĥįğĥ ʋąĺűēş ɱąŷ įɱρąċţ ţĥē ρēŗƒŏŗɱąŋċē ŏƒ şţąţēɱēŋţş įŋ ŏţĥēŗ şρąċēş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG:
totalStatementThreadLimitHelpTextDynamic=[[[Ĕŋţēŗ ą ρēŗċēŋţąğē ƃēţŵēēŋ 1% ąŋƌ {0}% (ŏŗ ţĥē ēƣűįʋąĺēŋţ ŋűɱƃēŗ) ŏƒ ţĥē ţŏţąĺ ŋűɱƃēŗ ŏƒ ţĥŗēąƌş ąʋąįĺąƃĺē įŋ ŷŏűŗ ţēŋąŋţ. Ŝēţţįŋğ ţĥē ţĥŗēąƌ ĺįɱįţ ţŏŏ ĺŏŵ ɱąŷ įɱρąċţ şţąţēɱēŋţ ρēŗƒŏŗɱąŋċē, ŵĥįĺē ēχċēşşįʋēĺŷ ĥįğĥ ʋąĺűēş ɱąŷ įɱρąċţ ţĥē ρēŗƒŏŗɱąŋċē ŏƒ şţąţēɱēŋţş įŋ ŏţĥēŗ şρąċēş.]]]
#XMSG:
totalStatementMemoryLimitHelpTextNew=[[[Ĕŋţēŗ ą ʋąĺűē ŏŗ ρēŗċēŋţąğē ƃēţŵēēŋ 0 (ŋŏ ĺįɱįţ) ąŋƌ ţĥē ţŏţąĺ ąɱŏűŋţ ŏƒ ɱēɱŏŗŷ ąʋąįĺąƃĺē įŋ ŷŏűŗ ţēŋąŋţ. Ŝēţţįŋğ ţĥē ɱēɱŏŗŷ ĺįɱįţ ţŏŏ ĺŏŵ ɱąŷ įɱρąċţ şţąţēɱēŋţ ρēŗƒŏŗɱąŋċē, ŵĥįĺē ēχċēşşįʋēĺŷ ĥįğĥ ʋąĺűēş ɱąŷ įɱρąċţ ţĥē ρēŗƒŏŗɱąŋċē ŏƒ şţąţēɱēŋţş įŋ ŏţĥēŗ şρąċēş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Total Statement Thread Limit
totalStatementThreadLimit=[[[Ţŏţąĺ Ŝţąţēɱēŋţ Ţĥŗēąƌ Ļįɱįţ∙∙∙∙∙∙∙∙]]]
#XFLD: Segmented button label
workloadclassPercentage=[[[%∙∙∙]]]
#XFLD: Segmented button label
workloadclassGigaByte=[[[ĢƁ∙∙]]]
#XFLD, 80: Segmented button label
workloadclassThreads=[[[Ţĥŗēąƌş∙∙∙∙∙∙∙]]]
#XFLD: Total Statement Memory Limit
totalStatementMemoryLimit=[[[Ţŏţąĺ Ŝţąţēɱēŋţ Μēɱŏŗŷ Ļįɱįţ∙∙∙∙∙∙∙∙]]]
#YMSE: Error while loading customer hana info
loadCustomerHanaInfoError=[[[Ƒąįĺēƌ ţŏ ĺŏąƌ ċűşţŏɱēŗ ŜĀƤ ĤĀŃĀ Ĭŋƒŏ.∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG:
minimumLimitReached=[[[Μįŋįɱűɱ ĺįɱįţ ŗēąċĥēƌ.∙∙∙∙∙]]]
#XMSG:
maximumLimitReached=[[[Μąχįɱűɱ ĺįɱįţ ŗēąċĥēƌ.∙∙∙∙∙]]]
#XMSG: Name Taken for Technical Name
technical-name-taken=[[[Ā ċŏŋŋēċţįŏŋ ŵįţĥ ţĥē ţēċĥŋįċąĺ ŋąɱē ŷŏű ēŋţēŗēƌ ąĺŗēąƌŷ ēχįşţş. Ƥĺēąşē ēŋţēŗ ąŋŏţĥēŗ ŋąɱē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Name Too long for Technical Name
technical-name-too-long=[[[Ţĥē ţēċĥŋįċąĺ ŋąɱē ŷŏű ēŋţēŗēƌ ēχċēēƌş 40 ċĥąŗąċţēŗş. Ƥĺēąşē ēŋţēŗ ą ŋąɱē ŵįţĥ ĺēşş ċĥąŗąċţēŗş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Technical name field empty
technical-name-field-empty=[[[Ƥĺēąşē ēŋţēŗ ą ţēċĥŋįċąĺ ŋąɱē.∙∙∙∙∙∙∙∙∙]]]
#XMSG: Generic error when user enters invalid characters for a field
field-invalid-characters=[[[Ŷŏű ċąŋ ŏŋĺŷ űşē ĺēţţēŗş (ą-ž), ŋűɱƃēŗş (0-9), ąŋƌ űŋƌēŗşċŏŗēş (_) ƒŏŗ ţĥē ŋąɱē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Generic error when user enters invalid initial characters for a field
field-underscore-trim=[[[Ţĥē ŋąɱē ŷŏű ēŋţēŗ ċąŋŋŏţ şţąŗţ ŏŗ ēŋƌ ŵįţĥ ąŋ űŋƌēŗşċŏŗē (_).∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Checkbox label for Enable Statement Limits
enableStatementLimits=[[[Ĕŋąƃĺē Ŝţąţēɱēŋţ Ļįɱįţş∙∙∙∙∙∙]]]
#XFLD: Connection SubSection Settings Title in space details
connectionSettings=[[[Ŝēţţįŋğş∙∙∙∙∙∙]]]
#XMSG: Connections tool hint in Space details section
connectionsToolHint=[[[Ţŏ ċŗēąţē ŏŗ ēƌįţ ċŏŋŋēċţįŏŋş, ŏρēŋ ţĥē Ĉŏŋŋēċţįŏŋş ąρρ ƒŗŏɱ ţĥē şįƌē ŋąʋįğąţįŏŋ ŏŗ ċĺįċķ ĥēŗē:∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XBUT: Connection list Open Connection Tool Button
button-connectiontool=[[[Ģŏ ţŏ Ĉŏŋŋēċţįŏŋş∙∙∙∙∙∙∙]]]
#XFLD: Not deployed label on space tile
notDeployedLabel=[[[Ŝρąċē ĥąşŋ’ţ ƃēēŋ ƌēρĺŏŷēƌ ŷēţ.∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Not deployed additional text on space tile
notDeployedText=[[[Ƥĺēąşē ƌēρĺŏŷ ţĥē şρąċē.∙∙∙∙∙∙]]]
#XFLD: Corrupt space label on space tile
corruptSpace=[[[Ŝŏɱēţĥįŋğ ŵēŋţ ŵŗŏŋğ.∙∙∙∙∙]]]
#XFLD: Corrupt space additional text on space tile
corruptSpaceHint=[[[Ţŗŷ ŗēƌēρĺŏŷįŋğ ŏŗ ċŏŋţąċţ şűρρŏŗţ∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=[[[Āűƌįţ Ļŏğ Ďąţą∙∙∙∙∙]]]
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=[[[Āƌɱįŋįşţŗąţįʋē Ďąţą∙∙∙∙∙]]]
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=[[[Ŏţĥēŗ Ďąţą∙∙∙∙]]]
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=[[[Ďąţą įŋ Ŝρąċēş∙∙∙∙∙]]]
#XMSG: Unlock Space confirmation text
unlockSpaceConfirmation=[[[Ďŏ ŷŏű ŗēąĺĺŷ ŵąŋţ ţŏ űŋĺŏċķ ţĥē şρąċē?∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Lock Space confirmation text
lockSpaceConfirmation=[[[Ďŏ ŷŏű ŗēąĺĺŷ ŵąŋţ ţŏ ĺŏċķ ţĥē şρąċē?∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Lock
lock=[[[Ļŏċķ]]]
#XFLD: Unlock
unlock=[[[Ůŋĺŏċķ∙∙∙∙∙∙∙∙]]]
#XFLD: Locking
locking=[[[Ļŏċķįŋğ∙∙∙∙∙∙∙]]]
#XMSG: Success message after locking space
lockSpaceSuccessMsg=[[[Ŝρąċē ĺŏċķēƌ∙∙∙∙∙∙∙]]]
#XMSG: Success message after unlocking space
unlockSpaceSuccessMsg=[[[Ŝρąċē űŋĺŏċķēƌ∙∙∙∙∙]]]
#XMSG: Success message after locking spaces
lockSpacesSuccessMsg=[[[Ŝρąċēş ĺŏċķēƌ∙∙∙∙∙∙]]]
#XMSG: Success message after unlocking spaces
unlockSpacesSuccessMsg=[[[Ŝρąċēş űŋĺŏċķēƌ∙∙∙∙]]]
#YMSE: Error while locking a space
lockSpaceError=[[[Ţĥē şρąċē ċąŋŋŏţ ƃē ĺŏċķēƌ.∙∙∙∙∙∙∙∙]]]
#YMSE: Error while unlocking a space
unlockSpaceError=[[[Ţĥē şρąċē ċąŋŋŏţ ƃē űŋĺŏċķēƌ.∙∙∙∙∙∙∙∙∙]]]
#XTIT: popup title Warning
confirmationWarningTitle=[[[Ŵąŗŋįŋğ∙∙∙∙∙∙∙]]]
#XMSG: Message strip message when a space was locked manually
manualLockedMessage=[[[Ţĥē şρąċē ĥąş ƃēēŋ ĺŏċķēƌ ɱąŋűąĺĺŷ.∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Message strip message when a space was locked due to the amount of generated audit logs
auditLogLockedMessage=[[[Ţĥē şρąċē ĥąş ƃēēŋ ĺŏċķēƌ ƃŷ ţĥē şŷşţēɱ ƃēċąűşē ąűƌįţ ĺŏğş ċŏŋşűɱē ą ĺąŗğē ƣűąŋţįţŷ ŏƒ ĢƁ ŏƒ ƌįşķ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Message strip message when a space was locked because the assigned quota was exceeded.
quotaLockedMessage=[[[Ţĥē şρąċē ĥąş ƃēēŋ ĺŏċķēƌ ƃŷ ţĥē şŷşţēɱ ƃēċąűşē įţ ēχċēēƌş įţş ąĺĺŏċąţįŏŋş ŏƒ ɱēɱŏŗŷ ŏŗ ƌįşķ şţŏŗąğē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Unlock Spaces confirmation text
unlockSpacesConfirmation=[[[Ďŏ ŷŏű ŗēąĺĺŷ ŵąŋţ ţŏ űŋĺŏċķ ţĥē şēĺēċţēƌ şρąċēş?∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Lock Spaces confirmation text
lockSpacesConfirmation=[[[Ďŏ ŷŏű ŗēąĺĺŷ ŵąŋţ ţŏ ĺŏċķ ţĥē şēĺēċţēƌ şρąċēş?∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XTIT: Scoped role editor dialog title
scopedRoleEditorDialogTitle=[[[Ŝċŏρēƌ Řŏĺē Ĕƌįţŏŗ∙∙∙∙∙∙]]]
#XTIT: ECN Management title
ecnManagementTitle=[[[Ŝρąċē ąŋƌ Ĕĺąşţįċ Ĉŏɱρűţē Ńŏƌē Μąŋąğēɱēŋţ∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: ECNs
ecns=[[[Ĕĺąşţįċ Ĉŏɱρűţē Ńŏƌēş∙∙∙∙∙]]]
#XFLD: ECN phase Ready
ecnReady=[[[Řēąƌŷ∙∙∙∙∙∙∙∙∙]]]
#XFLD: ECN phase Running
ecnRunning=[[[Řűŋŋįŋğ∙∙∙∙∙∙∙]]]
#XFLD: ECN phase Initial
ecnInitial=[[[Ńŏţ Řēąƌŷ∙∙∙∙∙]]]
#XFLD: ECN phase Starting
ecnStarting=[[[Ŝţąŗţįŋğ∙∙∙∙∙∙]]]
#XFLD: ECN phase Starting Failed
ecnStartingFailed=[[[Ŝţąŗţįŋğ Ƒąįĺēƌ∙∙∙∙]]]
#XFLD: ECN phase Stopping
ecnStopping=[[[Ŝţŏρρįŋğ∙∙∙∙∙∙]]]
#XFLD: ECN phase Stopping Failed
ecnStoppingFailed=[[[Ŝţŏρρįŋğ Ƒąįĺēƌ∙∙∙∙]]]
#XBTN: Assign Button
assign=[[[Āƌƌ Ŝρąċēş∙∙∙∙]]]
#XBTN: Start Header-Button
start=[[[Ŝţąŗţ∙∙∙∙∙∙∙∙∙]]]
#XBTN: Update Header-Button
repair=[[[Ůρƌąţē∙∙∙∙∙∙∙∙]]]
#XBTN: Stop Header-Button
stop=[[[Ŝţŏρ]]]
#XFLD: ECN hours remaining
ecnHoursRemaining=[[[1000 ĥŏűŗş ŗēɱąįŋįŋğ∙∙∙∙]]]
#XFLD: ECN block-hours remaining
ecnBlockHoursRemaining=[[[{0} Ɓĺŏċķ-Ĥŏűŗş Řēɱąįŋįŋğ]]]
#XFLD: ECN block-hour remaining
ecnBlockHourRemaining=[[[{0} Ɓĺŏċķ-Ĥŏűŗ Řēɱąįŋįŋğ]]]
#XTIT: ECN creation dialog title
ecnCreationDialogTitle=[[[Ĉŗēąţē Ĕĺąşţįċ Ĉŏɱρűţē Ńŏƌē∙∙∙∙∙∙∙∙]]]
#XTIT: ECN edit dialog title
ecnEditDialogTitle=[[[Ĕƌįţ Ĕĺąşţįċ Ĉŏɱρűţē Ńŏƌē∙∙∙∙∙∙∙]]]
#XTIT: ECN deletion dialog title
ecnDeletionDialogTitle=[[[Ďēĺēţē Ĕĺąşţįċ Ĉŏɱρűţē Ńŏƌē∙∙∙∙∙∙∙∙]]]
#XTIT: ECN Space assignment dialog title
ecnSpaceAssignmentDialogTitle=[[[Āƌƌ Ŝρąċēş∙∙∙∙]]]
#XFLD: ECN ID
ECNIDLabel=[[[Ĕĺąşţįċ Ĉŏɱρűţē Ńŏƌē∙∙∙∙]]]
#XTXT: Selected toolbar text
selectedToolbarText=[[[Ŝēĺēċţēƌ: {0}]]]
#XTIT: Elastic Compute Nodes
ECNslong=[[[Ĕĺąşţįċ Ĉŏɱρűţē Ńŏƌēş∙∙∙∙∙]]]
#XTIT: Object assignment - Table header: Objects available
objectsAvailable=[[[Ńűɱƃēŗ ŏƒ Ŏƃĵēċţş∙∙∙∙∙∙∙]]]
#XTIT: Object assignment - Dialog header text
selectObjects=[[[Ŝēĺēċţ ţĥē şρąċēş ąŋƌ ŏƃĵēċţş ŷŏű ŵąŋţ ţŏ ąƌƌ ţŏ ŷŏűŗ ēĺąşţįċ ċŏɱρűţē ŋŏƌē:∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XTIT: Object assignment - Table header title: Objects
objects=[[[Ŏƃĵēċţş∙∙∙∙∙∙∙]]]
#XTIT: Object assignment - Table header: Type
type=[[[Ţŷρē]]]
#XMSG: Message strip delete database user audit warning
deleteDatabaseUserWarningMessageStrip=[[[Ɓē ąŵąŗē ţĥąţ ƌēĺēţįŋğ ą ƌąţąƃąşē űşēŗ ŵįĺĺ ŗēşűĺţ įŋ ţĥē ƌēĺēţįŏŋ ŏƒ ąĺĺ ğēŋēŗąţēƌ ąűƌįţ ĺŏğ ēŋţŗįēş. Ĭƒ ŷŏű ŵąŋţ ţŏ ķēēρ ţĥē ąűƌįţ ĺŏğş, ċŏŋşįƌēŗ ēχρŏŗţįŋğ ţĥēɱ ƃēƒŏŗē ŷŏű ƌēĺēţē ţĥē ƌąţąƃąşē űşēŗ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Message strip delete HDI container audit logs impact
deleteHDIContainerWarningMessageStrip=[[[Ɓē ąŵąŗē ţĥąţ űŋąşşįğŋįŋğ ąŋ ĤĎĬ ċŏŋţąįŋēŗ ƒŗŏɱ ţĥē şρąċē ŵįĺĺ ŗēşűĺţ įŋ ţĥē ƌēĺēţįŏŋ ŏƒ ąĺĺ ğēŋēŗąţēƌ ąűƌįţ ĺŏğ ēŋţŗįēş. Ĭƒ ŷŏű ŵąŋţ ţŏ ķēēρ ţĥē ąűƌįţ ĺŏğş, ċŏŋşįƌēŗ ēχρŏŗţįŋğ ţĥēɱ ƃēƒŏŗē ŷŏű űŋąşşįğŋ ţĥē ĤĎĬ ċŏŋţąįŋēŗ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XTXT: All audit logs
allAuditLogs=[[[Āĺĺ ąűƌįţ ĺŏğ ēŋţŗįēş ğēŋēŗąţēƌ ƒŏŗ ţĥē şρąċē∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Message strip disable audit warning
disableAuditWarningMessageStrip=[[[Ɓē ąŵąŗē ţĥąţ ƌįşąƃĺįŋğ ąŋ ąűƌįţ ρŏĺįċŷ (ŗēąƌ ŏŗ ċĥąŋğē ŏρēŗąţįŏŋş) ŵįĺĺ ŗēşűĺţ įŋ ţĥē ƌēĺēţįŏŋ ŏƒ ąĺĺ įţş ąűƌįţ ĺŏğ ēŋţŗįēş. Ĭƒ ŷŏű ŵąŋţ ţŏ ķēēρ ţĥē ąűƌįţ ĺŏğ ēŋţŗįēş, ċŏŋşįƌēŗ ēχρŏŗţįŋğ ţĥēɱ ƃēƒŏŗē ŷŏű ƌįşąƃĺē ţĥē ąűƌįţ ρŏĺįċŷ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XTIT: No Spaces assigned to ECN Illustration title
noSpacesECNTitle=[[[Ńŏ şρąċēş ŏŗ ŏƃĵēċţş ąƌƌēƌ ŷēţ∙∙∙∙∙∙∙∙∙]]]
#XTIT: No Spaces assigned to ECN Illustration description
noSpacesECNDescription=[[[Ţŏ şţąŗţ ŵŏŗķįŋğ ŵįţĥ ŷŏűŗ ēĺąşţįċ ċŏɱρűţē ŋŏƌē, ąƌƌ ą şρąċē ŏŗ ŏƃĵēċţş ţŏ įţ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XTIT: No Spaces Illustration title
noSpacesTitle=[[[Ńŏ şρąċē ċŗēąţēƌ ŷēţ∙∙∙∙]]]
#XTIT: No Spaces Illustration description
noSpacesDescription=[[[Ţŏ şţąŗţ ąċƣűįŗįŋğ ƌąţą, ċŗēąţē ą şρąċē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XTIT: No Spaces Illustration title
noDeletedSpacesTitle=[[[Ţĥē ŗēċŷċĺē ƃįŋ įş ēɱρţŷ∙∙∙∙∙∙]]]
#XTIT: No Spaces Illustration description
noDeletedSpacesDescription=[[[Ŷŏű ċąŋ ŗēċŏʋēŗ ŷŏűŗ ƌēĺēţēƌ şρąċēş ƒŗŏɱ ĥēŗē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XTXT:  {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDatabaseUsers=[[[Ŏŋċē ţĥē şρąċē įş ƌēρĺŏŷēƌ, ţĥē ƒŏĺĺŏŵįŋğ ƌąţąƃąşē űşēŗş ŵįĺĺ ƃē {0} ƌēĺēţēƌ ąŋƌ ċąŋŋŏţ ƃē ŗēċŏʋēŗēƌ:]]]
#XTIT: Delete database users
deleteDatabaseUsersTitle=[[[Ďēĺēţē Ďąţąƃąşē Ůşēŗş∙∙∙∙∙]]]
#YMSE: Error if ecn id already exists
ecnIDInputDuplicateError=[[[ĬĎ ąĺŗēąƌŷ ēχįşţş.∙∙∙∙∙∙]]]
#YMSE: Error if ecn id contains invalid characters
ecnIDInputInvalidError=[[[Ƥĺēąşē űşē ĺŏŵēŗ ċąşē ċĥąŗąċţēŗş ą - ž ąŋƌ ŋűɱƃēŗş 0 – 9 ŏŋĺŷ∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#YMSE: Error if ecn id is too short
ecnIDInputTooShortError=[[[ĬĎ ĥąş ţŏ ƃē ąţ ĺēąşţ {0} ċĥąŗąċţēŗş ĺŏŋğ.]]]
#XMSG: ecn id length warning
ecnIdLengthWarning=[[[Μąχįɱűɱ ŏƒ {0} ċĥąŗąċţēŗş ēχċēēƌēƌ.]]]
#XFLD: open System Monitor
systemMonitor=[[[Ŝŷşţēɱ Μŏŋįţŏŗ∙∙∙∙∙]]]
#XFLD: open ECN schedule dialog menu entry
schedule=[[[Ŝċĥēƌűĺē∙∙∙∙∙∙]]]
#XFLD: open create ECN schedule dialog
createSchedule=[[[Ĉŗēąţē Ŝċĥēƌűĺē∙∙∙∙]]]
#XFLD: open change ECN schedule dialog
changeSchedule=[[[Ĕƌįţ Ŝċĥēƌűĺē∙∙∙∙∙∙]]]
#XFLD: open delete ECN schedule dialog
deleteSchedule=[[[Ďēĺēţē Ŝċĥēƌűĺē∙∙∙∙]]]
#XFLD: open self assign ECN schedule dialog
selfAssignSchedule=[[[Āşşįğŋ Ŝċĥēƌűĺē ţŏ Μē∙∙∙∙∙]]]
#XFLD: open pause ECN schedule dialog
pauseSchedule=[[[Ƥąűşē Ŝċĥēƌűĺē∙∙∙∙∙]]]
#XFLD: open resume ECN schedule dialog
resumeSchedule=[[[Řēşűɱē Ŝċĥēƌűĺē∙∙∙∙]]]
#XFLD: View Logs
viewLogs=[[[Ʋįēŵ Ļŏğş∙∙∙∙∙]]]
#XFLD: Compute Blocks
computeBlocks=[[[Ĉŏɱρűţē Ɓĺŏċķş∙∙∙∙∙]]]
#XFLD: Memory label in ECN creation dialog
ecnMemory=[[[Μēɱŏŗŷ (ĢƁ)∙∙∙∙∙∙∙∙]]]
#XFLD: Storage label in ECN creation dialog
ecnStorage=[[[Ŝţŏŗąğē (ĢƁ)∙∙∙∙∙∙∙]]]
#XFLD: Number of CPU label in ECN creation dialog
ecnCPUCount=[[[Ńűɱƃēŗ ŏƒ ĈƤŮ∙∙∙∙∙∙]]]
#XFLD: ECN updated by label
changedBy=[[[Ĉĥąŋğēƌ Ɓŷ∙∙∙∙]]]
#XFLD: ECN updated on label
changedOn=[[[Ĉĥąŋğēƌ Ŏŋ∙∙∙∙]]]
#XMSG: Success message after creating a Elastic Compute Node
createEcnSuccessMessage=[[[Ĕĺąşţįċ ċŏɱρűţē ŋŏƌē ċŗēąţēƌ∙∙∙∙∙∙∙∙]]]
#YMSE: Error while creating a Elastic Compute Node
createEcnError=[[[Ĕĺąşţįċ ċŏɱρűţē ŋŏƌē ċŏűĺƌŋ’ţ ƃē ċŗēąţēƌ∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Success message after updating a Elastic Compute Node
updateEcnSuccessMessage=[[[Ĕĺąşţįċ ċŏɱρűţē ŋŏƌē űρƌąţēƌ∙∙∙∙∙∙∙∙]]]
#XMSG: Error while updating a Elastic Compute Node
udpateEcnError=[[[Ĕĺąşţįċ ċŏɱρűţē ŋŏƌē ċŏűĺƌŋ’ţ ƃē űρƌąţēƌ∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Success message after deleting a Elastic Compute Node
deleteEcnSuccessMessage=[[[Ĕĺąşţįċ ċŏɱρűţē ŋŏƌē ƌēĺēţēƌ∙∙∙∙∙∙∙∙]]]
#YMSE: Error while deleting a Elastic Compute Node
deleteEcnError=[[[Ĕĺąşţįċ ċŏɱρűţē ŋŏƌē ċŏűĺƌŋ’ţ ƃē ƌēĺēţēƌ∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Success message after starting a Elastic Compute Node
startingEcnSuccessMessage=[[[Ŝţąŗţįŋğ ēĺąşţįċ ċŏɱρűţē ŋŏƌē∙∙∙∙∙∙∙∙∙]]]
#XMSG: Success message after stopping a Elastic Compute Node
stoppingEcnSuccessMessage=[[[Ŝţŏρρįŋğ ēĺąşţįċ ċŏɱρűţē ŋŏƌē∙∙∙∙∙∙∙∙∙]]]
#YMSE: Error while starting a Elastic Compute Node
startingEcnError=[[[Ĕĺąşţįċ ċŏɱρűţē ŋŏƌē ċŏűĺƌŋ’ţ şţąŗţ∙∙∙∙∙∙∙∙∙∙∙∙]]]
#YMSE: Error while stopping a Elastic Compute Node
stoppingEcnError=[[[Ĕĺąşţįċ ċŏɱρűţē ŋŏƌē ċŏűĺƌŋ’ţ şţŏρ∙∙∙∙∙∙∙∙∙∙∙]]]
#XBUT: Add Object button for an ECN
assignObjects=[[[Āƌƌ Ŏƃĵēċţş∙∙∙∙∙∙∙∙]]]
#XFLD: Enable auto object assignment label
enableAutoObjAssignment=[[[Āƌƌ Āĺĺ Ŏƃĵēċţş Āűţŏɱąţįċąĺĺŷ∙∙∙∙∙∙∙∙∙]]]
#XFLD: object type label to be assigned
objectTypeLabel=[[[Ţŷρē (Ŝēɱąŋţįċ Ůşąğē)∙∙∙∙∙]]]
#XFLD: assigned object type label
assignedObjectTypeLabel=[[[Ţŷρē]]]
#XFLD: technical name label
TechnicalNameLabel=[[[Ţēċĥŋįċąĺ Ńąɱē∙∙∙∙∙]]]
#XFLD: Object selecttion ECN label
objectSelectionECNLabel=[[[Ŝēĺēċţ ţĥē ŏƃĵēċţş ŷŏű ŵąŋţ ţŏ ąƌƌ ţŏ ţĥē ēĺąşţįċ ċŏɱρűţē ŋŏƌē∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XTIT: Add objects dialog title
assignObjectsTitle=[[[Āƌƌ Ŏƃĵēċţş ŏƒ∙∙∙∙∙]]]
#XFLD: object label with object count
objectLabel=[[[Ŏƃĵēċţ∙∙∙∙∙∙∙∙]]]
#XMSG: No objects available to add message.
noObjectsToAssign=[[[Ńŏ ŏƃĵēċţş ąʋąįĺąƃĺē ţŏ ąƌƌ.∙∙∙∙∙∙∙∙]]]
#XMSG: No objects assigned message.
noAssignedObjects=[[[Ńŏ ŏƃĵēċţş ąƌƌēƌ.∙∙∙∙∙∙∙]]]
#XTIT: Remove objects popup title
removeObjectsOrSpacesConfirmationTitle=[[[Ŵąŗŋįŋğ∙∙∙∙∙∙∙]]]
#XTIT: Delete ECN popup button and title
deleteECNConfirmationButtonAndTitle=[[[Ďēĺēţē∙∙∙∙∙∙∙∙]]]
#XMSG: Remove objects popup text
removeObjectsConfirmation=[[[Ďŏ ŷŏű ŗēąĺĺŷ ŵąŋţ ţŏ ŗēɱŏʋē ţĥē şēĺēċţēƌ ŏƃĵēċţş?∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Remove spaces popup text
removeSpacesConfirmation=[[[Ďŏ ŷŏű ŗēąĺĺŷ ŵąŋţ ţŏ ŗēɱŏʋē ţĥē şēĺēċţēƌ şρąċēş?∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XBUT: Confirmation button for objects dialog
removeObjectsOrSpacesConfirmationBtn=[[[Řēɱŏʋē Ŝρąċēş∙∙∙∙∙∙]]]
#XMSG: Success message after removing objects
removeObjectsSuccessMessage=[[[Ĕχρŏşēƌ ŏƃĵēċţş ĥąʋē ƃēēŋ ŗēɱŏʋēƌ∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Success message after assigning objects
assignObjectsSuccessMessage=[[[Ĕχρŏşēƌ ŏƃĵēċţş ĥąʋē ƃēēŋ ąşşįğŋēƌ∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: auto object assignment label for space tile
autoObjAssignmentLabel=[[[Āĺĺ Ĕχρŏşēƌ Ŏƃĵēċţş∙∙∙∙∙]]]
#XFLD: Spaces tab label
spacesTabLabel=[[[Ŝρąċēş∙∙∙∙∙∙∙∙]]]
#XFLD: Exposed objects tab label
consumptionObjectsTabLabel=[[[Ĕχρŏşēƌ Ŏƃĵēċţş∙∙∙∙]]]
#XMSG: Success message after unassigning spaces
spacesUnassignedSuccessMessage=[[[Ŝρąċēş ĥąʋē ƃēēŋ ŗēɱŏʋēƌ∙∙∙∙∙∙]]]
#XMSG: Success message after unassigning space
spaceUnassignedSuccessMessage=[[[Ŝρąċē ĥąş ƃēēŋ ŗēɱŏʋēƌ∙∙∙∙∙]]]
#YMSE: Error while assigning / unassigning spaces
spacesAssignUnassignError=[[[Ŝρąċēş ċŏűĺƌŋ’ţ ƃē ąşşįğŋēƌ ŏŗ ŗēɱŏʋēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#YMSE: Error while removing objects
removeObjectsError=[[[Ŵē ċŏűĺƌŋ'ţ ąşşįğŋ ŏŗ ŗēɱŏʋē ţĥē ŏƃĵēċţş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#YMSE: Error while removing object
removeObjectError=[[[Ŵē ċŏűĺƌŋ'ţ ąşşįğŋ ŏŗ ŗēɱŏʋē ţĥē ŏƃĵēċţ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#YMSE: Error when saving the ecn with invalid value
invalidSizingPlanError=[[[Ţĥē ŋűɱƃēŗ ρŗēʋįŏűşĺŷ şēĺēċţēƌ įş ŋŏ ɱŏŗē ʋąĺįƌ. Ƥĺēąşē şēĺēċţ ą ʋąĺįƌ ŋűɱƃēŗ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#YMSE: Error when saving the ecn with invalid value
invalidPerformanceClassError=[[[Ƥĺēąşē şēĺēċţ ą ʋąĺįƌ ρēŗƒŏŗɱąŋċē ċĺąşş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#YMSE: Error when saving the ecn with invalid value
performanceClassNotAvailableError=[[[Ţĥē ρŗēʋįŏűşĺŷ şēĺēċţēƌ ρēŗƒŏŗɱąŋċē ċĺąşş "{0}" įş ċűŗŗēŋţĺŷ ŋŏţ ʋąĺįƌ. Ƥĺēąşē şēĺēċţ ţĥē ʋąĺįƌ ρēŗƒŏŗɱąŋċē ċĺąşş.]]]
#XTXT: Delete ECN confirmation text
deleteConfirmationTxtECN=[[[Āŗē ŷŏű şűŗē ŷŏű ŵąŋţ ţŏ ƌēĺēţē ţĥē ēĺąşţįċ ċŏɱρűţē ŋŏƌē?∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: tooltip for ? button
help=[[[Ĥēĺρ]]]
#XFLD: ECN edit button label
editECN=[[[Ĉŏŋƒįğűŗē∙∙∙∙∙]]]
#XFLD: Technical type label for ERModel
DWC_ERMODEL=[[[Ĕŋţįţŷ - Řēĺąţįŏŋşĥįρ Μŏƌēĺ∙∙∙∙∙∙∙∙]]]
#XFLD: Technical type label for Local Table
DWC_LOCAL_TABLE=[[[Ļŏċąĺ Ţąƃĺē∙∙∙∙∙∙∙∙]]]
#XFLD: Technical type label for Remote Table
DWC_REMOTE_TABLE=[[[Řēɱŏţē Ţąƃĺē∙∙∙∙∙∙∙]]]
#XFLD: Technical type label for Analytic Model
DWC_ANALYTIC_MODEL=[[[Āŋąĺŷţįċ Μŏƌēĺ∙∙∙∙∙]]]
#XFLD: Technical type label for Task Chain
DWC_TASKCHAIN=[[[Ţąşķ Ĉĥąįŋ∙∙∙∙]]]
#XFLD: Technical type label for Data Flow
DWC_DATAFLOW=[[[Ďąţą Ƒĺŏŵ∙∙∙∙∙]]]
#XFLD: Technical type label for Replication Flow
DWC_REPLICATIONFLOW=[[[Řēρĺįċąţįŏŋ Ƒĺŏŵ∙∙∙∙∙∙∙∙]]]
#XFLD: Technical type label for Transformation Flow
DWC_TRANSFORMATIONFLOW=[[[Ţŗąŋşƒŏŗɱąţįŏŋ Ƒĺŏŵ∙∙∙∙∙]]]
#XFLD: Technical type label for Intelligent Lookup
DWC_IDT=[[[Ĭŋţēĺĺįğēŋţ Ļŏŏķűρ∙∙∙∙∙∙]]]
#XFLD: Technical type label for Repository
DWC_REPOSITORY_PACKAGE=[[[Řēρŏşįţŏŗŷ∙∙∙∙]]]
#XFLD: Technical type label for Enterprise Search
DWC_ESMODEL=[[[Ĕŋţēŗρŗįşē Ŝēąŗċĥ∙∙∙∙∙∙∙]]]
#XFLD: Technical type label for View
DWC_VIEW=[[[Ʋįēŵ]]]
#XFLD: Technical type label for Data Product
DWC_DATAPRODUCT=[[[Ďąţą Ƥŗŏƌűċţ∙∙∙∙∙∙∙]]]
#XFLD: Technical type label for Data Access Control
DWC_DAC=[[[Ďąţą Āċċēşş Ĉŏŋţŗŏĺ∙∙∙∙∙]]]
#XFLD: Technical type label for Folder
DWC_FOLDER=[[[Ƒŏĺƌēŗ∙∙∙∙∙∙∙∙]]]
#XFLD: Technical type label for Data Entity
DWC_BUSINESS_ENTITY=[[[Ɓűşįŋēşş Ĕŋţįţŷ∙∙∙∙]]]
#XFLD: Technical type label for Business Entity Variant
DWC_BUSINESS_ENTITY_VARIANT=[[[Ɓűşįŋēşş Ĕŋţįţŷ Ʋąŗįąŋţ∙∙∙∙∙∙]]]
#XFLD: Technical type label for Responsibility Scenario
DWC_AUTH_SCENARIO=[[[Řēşρŏŋşįƃįĺįţŷ Ŝċēŋąŗįŏ∙∙∙∙∙∙]]]
#XFLD: Technical type label for Fact Model
DWC_FACT_MODEL=[[[Ƒąċţ Μŏƌēĺ∙∙∙∙]]]
#XFLD: Technical type label for Transformation Flow
DWC_PERSPECTIVE=[[[Ƥēŗşρēċţįʋē∙∙∙∙∙∙∙∙]]]
#XFLD: Technical type label for Consumption Model
DWC_CONSUMPTION_MODEL=[[[Ĉŏŋşűɱρţįŏŋ Μŏƌēĺ∙∙∙∙∙∙∙]]]
#XFLD: Technical type label for Remote Connection
DWC_REMOTECONNECTION=[[[Řēɱŏţē Ĉŏŋŋēċţįŏŋ∙∙∙∙∙∙∙]]]
#XFLD: Technical type label for Fact Model Variant
DWC_FACT_MODEL_VARIANT=[[[Ƒąċţ Μŏƌēĺ Ʋąŗįąŋţ∙∙∙∙∙∙]]]
#XMSG: Schedule created alert message
createScheduleSuccess=[[[Ŝċĥēƌűĺē ċŗēąţēƌ∙∙∙∙∙∙∙∙]]]
#XMSG: Schedule updated alert message
updateScheduleSuccess=[[[Ŝċĥēƌűĺē űρƌąţēƌ∙∙∙∙∙∙∙∙]]]
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=[[[Ŝċĥēƌűĺē ƌēĺēţēƌ∙∙∙∙∙∙∙∙]]]
#XMSG: Schedule takeover success message
ownerChangeScheduleSuccess=[[[Ŝċĥēƌűĺē ąşşįğŋēƌ ţŏ ŷŏű∙∙∙∙∙∙]]]
#XMSG: Message for starting pausing of schedule
pauseScheduleStarted=[[[Ƥąűşįŋğ 1 şċĥēƌűĺē∙∙∙∙∙∙]]]
#XMSG: Message for starting resuming of schedule
resumeScheduleStarted=[[[Řēşűɱįŋğ 1 şċĥēƌűĺē∙∙∙∙∙]]]
#XFLD: Segmented button label
availableSpacesButton=[[[Āʋąįĺąƃĺē∙∙∙∙∙]]]
#XFLD: Segmented button label
selectedSpacesButton=[[[Ŝēĺēċţēƌ∙∙∙∙∙∙]]]
#XFLD: Visit website button text
visitWebsite=[[[Ʋįşįţ Ŵēƃşįţē∙∙∙∙∙∙]]]
#XMSG: Warning message on unselecting source language
sourceLanguageRemovalWarning=[[[Ţĥē ρŗēʋįŏűşĺŷ şēĺēċţēƌ şŏűŗċē ĺąŋğűąğē ŵįĺĺ ƃē ŗēɱŏʋēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XBUT: enable translation confirmation button
enableTranslationConfirmationBtn=[[[Ĕŋąƃĺē∙∙∙∙∙∙∙∙]]]
#XFLD: ECN performance class label
performanceClassLabel=[[[Ƥēŗƒŏŗɱąŋċē Ĉĺąşş∙∙∙∙∙∙∙]]]
#XTXT performance class memory text
memoryText=[[[Μēɱŏŗŷ∙∙∙∙∙∙∙∙]]]
#XTXT performance class compute text
computeText=[[[Ĉŏɱρűţē∙∙∙∙∙∙∙]]]
#XTXT performance class high-compute text
highComputeText=[[[Ĥįğĥ-Ĉŏɱρűţē∙∙∙∙∙∙∙]]]
#XBUT: Recycle Bin Button Text
recycleBin=[[[Řēċŷċĺē Ɓįŋ∙∙∙∙∙∙∙∙]]]
#XBUT: Restore Button Text
restore=[[[Řēşţŏŗē∙∙∙∙∙∙∙]]]
#XMSG: Warning message for new Workload Management UI
priorityWarning=[[[Ţĥįş ąŗēą įş įŋ ŗēąƌ-ŏŋĺŷ. Ŷŏű ċąŋ ċĥąŋğē ţĥē şρąċē ρŗįŏŗįţŷ įŋ ţĥē Ŝŷşţēɱ / Ĉŏŋƒįğűŗąţįŏŋ / Ŵŏŗķĺŏąƌ Μąŋąğēɱēŋţ ąŗēą.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Warning message for new Workload Management UI
workloadWarning=[[[Ţĥįş ąŗēą įş įŋ ŗēąƌ-ŏŋĺŷ. Ŷŏű ċąŋ ċĥąŋğē ţĥē şρąċē ŵŏŗķĺŏąƌ ċŏŋƒįğűŗąţįŏŋ įŋ ţĥē Ŝŷşţēɱ / Ĉŏŋƒįğűŗąţįŏŋ / Ŵŏŗķĺŏąƌ Μąŋąğēɱēŋţ ąŗēą.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XCOL: Space table-view column: Apache Spark vCPUs
sparkManagementVCPUs=[[[Āρąċĥē Ŝρąŗķ ʋĈƤŮş∙∙∙∙∙∙]]]
#XCOL: Space table-view column: Apache Spark Memory (GB)
sparkManagementMemory=[[[Āρąċĥē Ŝρąŗķ Μēɱŏŗŷ (ĢƁ)∙∙∙∙∙∙]]]
#XCOL: Space type (from Space Capability) for Data Ingestion Spaces
ingestionSpaceType=[[[Ďąţą Ƥŗŏƌűċţ Ĭŋğēşţįŏŋ∙∙∙∙∙]]]
#XFLD: Deployment in progress nodatatext for spark table
sparkTableDeploymentInProgress=[[[Ńŏ ƌąţą ąʋąįĺąƃĺē ąş ţĥē şρąċē įş ċűŗŗēŋţĺŷ ƃēįŋğ ƌēρĺŏŷēƌ∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Runtime data being loaded nodatatext for spark table
sparkTableDeploymentLoading=[[[Ńŏ ƌąţą ąʋąįĺąƃĺē ąş ţĥē şρąċē įş ċűŗŗēŋţĺŷ ƃēįŋğ ĺŏąƌēƌ∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XCOL: Space type (from Space Capability) for BW Private Cloud Edition
BWPCESpaceType=[[[ŜĀƤ ƁŴ∙∙∙∙∙∙∙∙]]]
#XBUT: Go to Instance Mappings Button
navToMappingsButton=[[[Ĕƌįţ Ĭŋşţąŋċē Μąρρįŋğş∙∙∙∙∙]]]
#XFLD: SAP HANA Data Lake Storage label if less than 1GB
dataLakeStorageUsed=[[[< 1 ĢƁ∙∙∙∙∙∙∙∙]]]
